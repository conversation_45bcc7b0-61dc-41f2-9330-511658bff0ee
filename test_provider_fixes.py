#!/usr/bin/env python3
"""
Test Provider Failure Fixes and GitHub Integration
Verifies that LLM provider failures are handled correctly and GitHub tool is integrated
"""

import time
from enhanced_research_orchestrator import EnhancedResearchOrchestrator
from unified_model_interface import UnifiedModelInterface

def test_provider_retry_logic():
    """Test the enhanced retry logic for provider failures"""
    print("🔄 TESTING PROVIDER RETRY LOGIC")
    print("=" * 50)
    
    # Load config
    import yaml
    with open('config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Initialize model interface
    model_interface = UnifiedModelInterface(config.get('unified_models', {}))
    
    # Test retry logic with a simple prompt
    print("🤖 Testing model generation with retry logic...")
    
    result = model_interface.generate(
        "Test prompt for retry logic",
        provider="gemini",
        max_retries=3,
        max_tokens=100,
        temperature=0.7
    )
    
    print(f"📊 Result status: {result.get('status')}")
    print(f"🤖 Provider used: {result.get('provider', 'unknown')}")
    
    if result['status'] == 'success':
        print(f"✅ Response length: {len(result.get('content', ''))}")
        print(f"📝 Response preview: {result.get('content', '')[:100]}...")
    else:
        print(f"❌ Error: {result.get('error', 'Unknown error')}")
        attempted = result.get('attempted_providers', [])
        if attempted:
            print(f"🔄 Attempted providers: {attempted}")
    
    return result['status'] == 'success'

def test_github_tool_integration():
    """Test GitHub tool integration"""
    print("\n🐙 TESTING GITHUB TOOL INTEGRATION")
    print("=" * 50)
    
    orchestrator = EnhancedResearchOrchestrator(silent=True)
    
    # Check if GitHub tool is loaded
    github_available = 'github_research' in orchestrator.tools
    print(f"📊 GitHub tool loaded: {github_available}")
    
    if github_available:
        github_tool = orchestrator.tools['github_research']
        
        # Test GitHub tool availability
        is_available = github_tool.is_available()
        print(f"📊 GitHub API available: {is_available}")
        
        if is_available:
            # Test GitHub search
            print("🔍 Testing GitHub repository search...")
            
            result = github_tool.execute(
                query="machine learning",
                search_type="repositories",
                sort="stars",
                max_results=5,
                language="python"
            )
            
            print(f"📊 Search status: {result.get('status')}")
            
            if result.get('status') == 'success':
                repos = result.get('repositories', [])
                print(f"✅ Found {len(repos)} repositories")
                
                for i, repo in enumerate(repos[:3], 1):
                    name = repo.get('name', 'Unknown')
                    stars = repo.get('stars', 0)
                    description = repo.get('description', '')[:50]
                    print(f"  {i}. {name} ({stars} stars) - {description}...")
                
                return True
            else:
                print(f"❌ GitHub search failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print("⚠️ GitHub API not available (check token)")
            return False
    else:
        print("❌ GitHub tool not loaded")
        return False

def test_research_api_fallback():
    """Test the enhanced research API fallback chain"""
    print("\n🔍 TESTING RESEARCH API FALLBACK CHAIN")
    print("=" * 50)
    
    orchestrator = EnhancedResearchOrchestrator(silent=True)
    
    # Set up research context
    orchestrator.research_context = {
        "api_intensity": "intensive",
        "tools_config": {
            "use_semantic_scholar": True,
            "use_knowledge_base": True,
            "use_github": True,
            "use_web_search": True
        }
    }
    
    # Test API fallback
    print("🔍 Testing API fallback chain...")
    
    result = orchestrator._try_research_apis_with_fallback(
        "machine learning computer vision", 
        "literature_review"
    )
    
    sources = result.get('sources', [])
    content = result.get('content', '')
    
    print(f"📊 Sources used: {sources}")
    print(f"📊 Content length: {len(content)}")
    print(f"📊 Detailed results: {len(result.get('detailed_results', []))}")
    
    # Check if GitHub was used
    github_used = 'github' in sources
    print(f"🐙 GitHub used: {github_used}")
    
    return len(sources) > 0

def test_agent_failure_resilience():
    """Test that the system handles agent failures gracefully"""
    print("\n🛡️ TESTING AGENT FAILURE RESILIENCE")
    print("=" * 50)
    
    orchestrator = EnhancedResearchOrchestrator(silent=True)
    
    # Test with a small number of agents
    print("🤖 Testing research with 3 agents...")
    
    start_time = time.time()
    
    result = orchestrator.orchestrate_research(
        research_query="Test machine learning research",
        research_type="literature_review",
        model_provider="gemini",
        context_aware=True,
        context_mode="standard",
        num_agents=3,
        max_tokens=1024,
        temperature=0.7,
        api_intensity="standard",
        tools_config={
            "use_semantic_scholar": True,
            "use_knowledge_base": True,
            "use_github": True,
            "use_web_search": True
        }
    )
    
    execution_time = time.time() - start_time
    
    print(f"📊 Research status: {result.get('status')}")
    print(f"⏱️ Execution time: {execution_time:.1f}s")
    print(f"🤖 Model provider: {result.get('model_provider', 'unknown')}")
    print(f"👥 Number of agents: {result.get('num_agents', 'unknown')}")
    
    if result.get('status') == 'success':
        phases = result.get('phases', {})
        for phase_name, phase_results in phases.items():
            if isinstance(phase_results, list):
                successful = sum(1 for r in phase_results if r.get('status') == 'success')
                total = len(phase_results)
                print(f"  📋 {phase_name}: {successful}/{total} agents successful")
        
        synthesis = result.get('final_synthesis', '')
        print(f"🧠 Synthesis generated: {len(synthesis) > 0}")
        
        return True
    else:
        print(f"❌ Research failed: {result.get('error', 'Unknown error')}")
        return False

def main():
    """Run all tests"""
    print("🧪 PROVIDER FAILURE FIXES & GITHUB INTEGRATION TESTS")
    print("=" * 70)
    
    tests = [
        ("Provider Retry Logic", test_provider_retry_logic),
        ("GitHub Tool Integration", test_github_tool_integration),
        ("Research API Fallback", test_research_api_fallback),
        ("Agent Failure Resilience", test_agent_failure_resilience)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name}...")
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} CRASHED: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 70)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 70)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Provider failure handling: FIXED")
        print("✅ GitHub integration: WORKING")
        print("✅ Retry logic: IMPLEMENTED")
        print("✅ Agent resilience: IMPROVED")
    else:
        print(f"\n⚠️ {failed} tests failed. Check the issues above.")

if __name__ == '__main__':
    main()

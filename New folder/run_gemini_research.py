#!/usr/bin/env python3
"""
Direct Gemini API Research for Multimodal Dataset Distillation
Using Gemini 2.5 Pro directly for comprehensive research
"""

import google.generativeai as genai
import time
import yaml
import json

def main():
    print("🎓 GEMINI RESEARCH - Multimodal Dataset Distillation")
    print("=" * 80)
    
    # Load config
    with open('config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Configure Gemini
    gemini_config = config['research_apis']['gemini']
    genai.configure(api_key=gemini_config['api_key'])
    model = genai.GenerativeModel(gemini_config['model'])
    
    print(f"🤖 Model: {gemini_config['model']}")
    print(f"🔑 API Key: {gemini_config['api_key'][:20]}...")
    print()
    
    # Research prompt
    research_prompt = """You are a well-known ambitious doctor in AI specialized in dataset distillation with very strong background in Math. Your task is as follows:

Research Directive: Advancing Multimodal Dataset Distillation for tri-modal or more modality datasets

Objective: Develop novel and feasible multimodal dataset distillation (MDD) techniques specifically tailored for datasets with image, text, and audio modalities. As an example, you will work with "MMIS (MMIS- Multimodal Dataset for Interior Scene Visual Generation and Recognition)". However, this technique should be applied to all multimodal datasets.

Phase 1: Comprehensive Analysis of Common Limitations in Multimodal Dataset Distillation

Conduct an exhaustive analysis of the inherent challenges and limitations in current dataset distillation (DD) methodologies, with particular focus on their applicability and shortcomings in multimodal contexts.

Specifically, investigate:

1. Computational Complexity and Scalability: Examine the bottlenecks associated with prevalent bi-level optimization frameworks. How do long-range gradient unrolling and repeated model training steps contribute to prohibitive computational costs and memory overhead, especially for high-resolution images or large-scale multimodal datasets?

2. Limited Cross-Architecture Generalization: Analyze why synthetic datasets often exhibit poor generalization capabilities across different, unseen model architectures. What are the underlying causes of this architecture overfitting, and how can it be mitigated?

3. Modality Collapse and Diversity Issues in Multimodal Data: Critically investigate the phenomenon of "modality collapse" in MDD, where synthetic data may fail to capture the full diversity and richness of each modality or the intricate cross-modal relationships present in the original dataset.

4. Training Instability: Identify sources of instability in distillation optimization, particularly observed in medical imaging dataset distillation, and explore how these impact robustness.

5. Bias and Fairness Concerns: Research how the distillation process on imbalanced datasets can exacerbate existing biases, leading to similarly skewed synthetic datasets and potentially unfair model decisions.

6. Challenges with Discrete and Structured Data: Explore the specific difficulties of distilling non-image data, such as high-dimensional, sparse, or discrete categorical data (e.g., text, graphs, tabular data).

Please provide a comprehensive analysis with specific technical details, mathematical formulations where appropriate, and concrete examples from recent literature."""

    try:
        print("🚀 Starting Gemini Research Analysis...")
        start_time = time.time()
        
        # Generate response with proper timeout and delay
        time.sleep(5.0)  # 5 second delay
        
        response = model.generate_content(
            research_prompt,
            generation_config=genai.types.GenerationConfig(
                max_output_tokens=8192,
                temperature=0.7,
                top_p=0.8,
                top_k=40
            ),
            request_options={"timeout": 300}  # 300 second timeout
        )
        
        execution_time = time.time() - start_time
        
        print("=" * 80)
        print("🎯 GEMINI RESEARCH RESULTS")
        print("=" * 80)
        print(f"✅ Status: SUCCESS")
        print(f"⏱️  Execution Time: {execution_time:.1f}s")
        print(f"📝 Response Length: {len(response.text):,} characters")
        print()
        
        print("📄 COMPREHENSIVE ANALYSIS:")
        print("-" * 80)
        print(response.text)
        print("-" * 80)
        
        # Save results
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        output_file = f"gemini_research_results_{timestamp}.txt"
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("GEMINI RESEARCH - Multimodal Dataset Distillation Analysis\n")
            f.write("=" * 80 + "\n")
            f.write(f"Timestamp: {timestamp}\n")
            f.write(f"Model: {gemini_config['model']}\n")
            f.write(f"Execution Time: {execution_time:.1f}s\n")
            f.write(f"Response Length: {len(response.text):,} characters\n")
            f.write("\n" + "=" * 80 + "\n")
            f.write("RESEARCH ANALYSIS:\n")
            f.write("=" * 80 + "\n")
            f.write(response.text)
        
        print(f"💾 Results saved to: {output_file}")
        
    except Exception as e:
        print(f"❌ Gemini Research Failed: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Research completed successfully!")
    else:
        print("\n💥 Research failed!")

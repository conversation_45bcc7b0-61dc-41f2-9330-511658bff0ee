import json
import re
from typing import Dict, Any, List
from .base_tool import BaseTool

class HypothesisGenerationTool(BaseTool):
    """Advanced hypothesis generation tool for scientific and research inquiries"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    @property
    def name(self) -> str:
        return "hypothesis_generation"
    
    @property
    def description(self) -> str:
        return "Generate testable hypotheses, theories, and explanations based on research findings and observations"
    
    @property
    def parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "research_findings": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Research findings, observations, or data to base hypotheses on"
                },
                "research_domain": {
                    "type": "string",
                    "description": "Research domain or field (e.g., 'biology', 'computer science', 'psychology')",
                    "default": ""
                },
                "hypothesis_type": {
                    "type": "string",
                    "enum": ["causal", "correlational", "descriptive", "predictive", "explanatory", "comparative"],
                    "description": "Type of hypothesis to generate",
                    "default": "explanatory"
                },
                "evidence_level": {
                    "type": "string",
                    "enum": ["preliminary", "moderate", "strong"],
                    "description": "Level of evidence available for hypothesis generation",
                    "default": "moderate"
                },
                "testability_focus": {
                    "type": "boolean",
                    "description": "Whether to focus on generating testable hypotheses",
                    "default": true
                },
                "novelty_threshold": {
                    "type": "string",
                    "enum": ["incremental", "moderate", "breakthrough"],
                    "description": "Desired novelty level of hypotheses",
                    "default": "moderate"
                }
            },
            "required": ["research_findings"]
        }
    
    def execute(self, research_findings: List[str], research_domain: str = "",
                hypothesis_type: str = "explanatory", evidence_level: str = "moderate",
                testability_focus: bool = True, novelty_threshold: str = "moderate") -> Dict[str, Any]:
        """Generate hypotheses based on research findings"""
        try:
            if not research_findings:
                return {
                    "error": "Research findings are required for hypothesis generation",
                    "status": "error"
                }
            
            # Analyze research findings
            analysis = self._analyze_research_findings(research_findings, research_domain)
            
            # Generate hypotheses based on type
            hypotheses = self._generate_hypotheses_by_type(
                analysis, hypothesis_type, evidence_level, novelty_threshold
            )
            
            # Evaluate testability if requested
            if testability_focus:
                hypotheses = self._evaluate_testability(hypotheses, research_domain)
            
            # Generate experimental designs
            experimental_designs = self._generate_experimental_designs(hypotheses, research_domain)
            
            # Assess feasibility
            feasibility_assessment = self._assess_hypothesis_feasibility(hypotheses, research_domain)
            
            return {
                "status": "success",
                "research_domain": research_domain,
                "hypothesis_type": hypothesis_type,
                "evidence_level": evidence_level,
                "analysis": analysis,
                "generated_hypotheses": hypotheses,
                "experimental_designs": experimental_designs,
                "feasibility_assessment": feasibility_assessment,
                "summary": f"Generated {len(hypotheses)} {hypothesis_type} hypotheses based on {len(research_findings)} findings"
            }
            
        except Exception as e:
            return {
                "error": f"Hypothesis generation failed: {str(e)}",
                "status": "error"
            }
    
    def _analyze_research_findings(self, findings: List[str], domain: str) -> Dict[str, Any]:
        """Analyze research findings to extract key patterns and relationships"""
        
        # Extract key concepts and variables
        concepts = self._extract_concepts(findings)
        
        # Identify relationships and patterns
        relationships = self._identify_relationships(findings, concepts)
        
        # Find gaps and unexplained phenomena
        gaps = self._identify_knowledge_gaps(findings, concepts)
        
        # Extract mechanisms and processes
        mechanisms = self._extract_mechanisms(findings, domain)
        
        return {
            "key_concepts": concepts,
            "relationships": relationships,
            "knowledge_gaps": gaps,
            "mechanisms": mechanisms,
            "domain_context": domain,
            "finding_count": len(findings)
        }
    
    def _extract_concepts(self, findings: List[str]) -> List[Dict[str, Any]]:
        """Extract key concepts from research findings"""
        concepts = {}
        
        # Simple concept extraction based on frequency and context
        for finding in findings:
            words = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', finding)  # Capitalized terms
            words.extend(re.findall(r'\b(?:effect|factor|variable|mechanism|process|system|method|approach)\b', finding.lower()))
            
            for word in words:
                if len(word) > 3:
                    concepts[word.lower()] = concepts.get(word.lower(), 0) + 1
        
        # Convert to structured format
        concept_list = []
        for concept, frequency in sorted(concepts.items(), key=lambda x: x[1], reverse=True)[:15]:
            concept_list.append({
                "concept": concept,
                "frequency": frequency,
                "importance": "high" if frequency > len(findings) * 0.3 else "medium" if frequency > 1 else "low"
            })
        
        return concept_list
    
    def _identify_relationships(self, findings: List[str], concepts: List[Dict]) -> List[Dict[str, Any]]:
        """Identify relationships between concepts"""
        relationships = []
        concept_names = [c["concept"] for c in concepts[:10]]  # Top 10 concepts
        
        # Look for relationship indicators
        relationship_patterns = [
            (r'(\w+)\s+(?:causes?|leads? to|results? in|produces?)\s+(\w+)', 'causal'),
            (r'(\w+)\s+(?:correlates? with|associated with|related to)\s+(\w+)', 'correlational'),
            (r'(\w+)\s+(?:increases?|decreases?|affects?)\s+(\w+)', 'influence'),
            (r'(\w+)\s+(?:depends? on|requires?|needs?)\s+(\w+)', 'dependency')
        ]
        
        for finding in findings:
            for pattern, rel_type in relationship_patterns:
                matches = re.finditer(pattern, finding.lower())
                for match in matches:
                    var1, var2 = match.groups()
                    if var1 in concept_names or var2 in concept_names:
                        relationships.append({
                            "variable_1": var1,
                            "variable_2": var2,
                            "relationship_type": rel_type,
                            "evidence": finding[:100] + "...",
                            "strength": "moderate"  # Could be enhanced with NLP
                        })
        
        return relationships[:10]  # Top 10 relationships
    
    def _identify_knowledge_gaps(self, findings: List[str], concepts: List[Dict]) -> List[Dict[str, Any]]:
        """Identify knowledge gaps and unexplained phenomena"""
        gaps = []
        
        # Look for gap indicators
        gap_indicators = [
            "unknown", "unclear", "not understood", "remains to be determined",
            "further research needed", "mechanism unclear", "unexplained",
            "contradictory", "inconsistent", "paradox", "anomaly"
        ]
        
        for finding in findings:
            for indicator in gap_indicators:
                if indicator in finding.lower():
                    gaps.append({
                        "gap_type": "knowledge_gap",
                        "description": finding[:200] + "...",
                        "indicator": indicator,
                        "priority": "high" if indicator in ["unknown", "unclear", "unexplained"] else "medium"
                    })
        
        # Add conceptual gaps (concepts mentioned but not well explained)
        high_freq_concepts = [c["concept"] for c in concepts if c["importance"] == "high"]
        for concept in high_freq_concepts:
            explanation_count = sum(1 for finding in findings 
                                  if any(word in finding.lower() for word in ["explain", "mechanism", "how", "why"]) 
                                  and concept in finding.lower())
            if explanation_count == 0:
                gaps.append({
                    "gap_type": "mechanistic_gap",
                    "description": f"Mechanism or explanation for {concept} not well understood",
                    "concept": concept,
                    "priority": "medium"
                })
        
        return gaps[:8]  # Top 8 gaps
    
    def _extract_mechanisms(self, findings: List[str], domain: str) -> List[Dict[str, Any]]:
        """Extract potential mechanisms and processes"""
        mechanisms = []
        
        # Look for mechanism indicators
        mechanism_patterns = [
            r'(?:mechanism|process|pathway|method|approach|system)\s+(?:of|for|that)\s+([^.]+)',
            r'([^.]+)\s+(?:mechanism|process|pathway)',
            r'(?:through|via|by)\s+([^.]+)'
        ]
        
        for finding in findings:
            for pattern in mechanism_patterns:
                matches = re.finditer(pattern, finding.lower())
                for match in matches:
                    mechanism_text = match.group(1).strip()
                    if len(mechanism_text) > 5 and len(mechanism_text) < 100:
                        mechanisms.append({
                            "mechanism": mechanism_text,
                            "context": finding[:150] + "...",
                            "type": "process" if "process" in mechanism_text else "mechanism",
                            "domain": domain
                        })
        
        return mechanisms[:6]  # Top 6 mechanisms
    
    def _generate_hypotheses_by_type(self, analysis: Dict, hypothesis_type: str, 
                                   evidence_level: str, novelty_threshold: str) -> List[Dict[str, Any]]:
        """Generate hypotheses based on specified type"""
        
        hypotheses = []
        concepts = analysis["key_concepts"]
        relationships = analysis["relationships"]
        gaps = analysis["knowledge_gaps"]
        mechanisms = analysis["mechanisms"]
        
        if hypothesis_type == "causal":
            hypotheses.extend(self._generate_causal_hypotheses(concepts, relationships, evidence_level))
        elif hypothesis_type == "correlational":
            hypotheses.extend(self._generate_correlational_hypotheses(concepts, relationships))
        elif hypothesis_type == "descriptive":
            hypotheses.extend(self._generate_descriptive_hypotheses(concepts, gaps))
        elif hypothesis_type == "predictive":
            hypotheses.extend(self._generate_predictive_hypotheses(concepts, relationships))
        elif hypothesis_type == "explanatory":
            hypotheses.extend(self._generate_explanatory_hypotheses(concepts, mechanisms, gaps))
        elif hypothesis_type == "comparative":
            hypotheses.extend(self._generate_comparative_hypotheses(concepts, relationships))
        
        # Filter by novelty threshold
        hypotheses = self._filter_by_novelty(hypotheses, novelty_threshold)
        
        return hypotheses[:10]  # Top 10 hypotheses
    
    def _generate_causal_hypotheses(self, concepts: List[Dict], relationships: List[Dict], 
                                  evidence_level: str) -> List[Dict[str, Any]]:
        """Generate causal hypotheses"""
        hypotheses = []
        
        # Generate from existing relationships
        for rel in relationships:
            if rel["relationship_type"] in ["causal", "influence"]:
                hypotheses.append({
                    "hypothesis": f"{rel['variable_1'].title()} directly causes changes in {rel['variable_2']}",
                    "type": "causal",
                    "variables": [rel["variable_1"], rel["variable_2"]],
                    "direction": "unidirectional",
                    "confidence": evidence_level,
                    "testable": True,
                    "novelty": "moderate"
                })
        
        # Generate novel causal relationships
        high_concepts = [c["concept"] for c in concepts if c["importance"] == "high"]
        for i, concept1 in enumerate(high_concepts[:3]):
            for concept2 in high_concepts[i+1:4]:
                hypotheses.append({
                    "hypothesis": f"Increased {concept1} causally influences {concept2} through an indirect pathway",
                    "type": "causal",
                    "variables": [concept1, concept2],
                    "direction": "unidirectional",
                    "confidence": "preliminary",
                    "testable": True,
                    "novelty": "high"
                })
        
        return hypotheses
    
    def _generate_explanatory_hypotheses(self, concepts: List[Dict], mechanisms: List[Dict], 
                                       gaps: List[Dict]) -> List[Dict[str, Any]]:
        """Generate explanatory hypotheses"""
        hypotheses = []
        
        # Generate explanations for knowledge gaps
        for gap in gaps[:3]:
            if gap["gap_type"] == "mechanistic_gap":
                concept = gap.get("concept", "phenomenon")
                hypotheses.append({
                    "hypothesis": f"The {concept} operates through a multi-step regulatory mechanism involving feedback loops",
                    "type": "explanatory",
                    "target_phenomenon": concept,
                    "proposed_mechanism": "multi-step regulation with feedback",
                    "testable": True,
                    "novelty": "moderate"
                })
        
        # Generate mechanism-based explanations
        for mechanism in mechanisms[:2]:
            hypotheses.append({
                "hypothesis": f"The observed {mechanism['mechanism']} can be explained by underlying molecular interactions",
                "type": "explanatory",
                "target_phenomenon": mechanism["mechanism"],
                "proposed_mechanism": "molecular interaction model",
                "testable": True,
                "novelty": "moderate"
            })
        
        return hypotheses
    
    def _generate_correlational_hypotheses(self, concepts: List[Dict], relationships: List[Dict]) -> List[Dict[str, Any]]:
        """Generate correlational hypotheses"""
        hypotheses = []
        
        high_concepts = [c["concept"] for c in concepts if c["importance"] == "high"]
        for i, concept1 in enumerate(high_concepts[:3]):
            for concept2 in high_concepts[i+1:4]:
                hypotheses.append({
                    "hypothesis": f"{concept1.title()} and {concept2} show positive correlation under specific conditions",
                    "type": "correlational",
                    "variables": [concept1, concept2],
                    "correlation_type": "positive",
                    "conditions": "specific environmental conditions",
                    "testable": True,
                    "novelty": "moderate"
                })
        
        return hypotheses
    
    def _generate_predictive_hypotheses(self, concepts: List[Dict], relationships: List[Dict]) -> List[Dict[str, Any]]:
        """Generate predictive hypotheses"""
        hypotheses = []
        
        for rel in relationships[:3]:
            hypotheses.append({
                "hypothesis": f"Changes in {rel['variable_1']} can predict future changes in {rel['variable_2']} with 70% accuracy",
                "type": "predictive",
                "predictor": rel["variable_1"],
                "outcome": rel["variable_2"],
                "predicted_accuracy": "70%",
                "time_frame": "short-term",
                "testable": True,
                "novelty": "moderate"
            })
        
        return hypotheses
    
    def _generate_descriptive_hypotheses(self, concepts: List[Dict], gaps: List[Dict]) -> List[Dict[str, Any]]:
        """Generate descriptive hypotheses"""
        hypotheses = []
        
        for concept in concepts[:3]:
            hypotheses.append({
                "hypothesis": f"{concept['concept'].title()} exhibits distinct patterns under different experimental conditions",
                "type": "descriptive",
                "target": concept["concept"],
                "pattern_type": "condition-dependent variation",
                "testable": True,
                "novelty": "low"
            })
        
        return hypotheses
    
    def _generate_comparative_hypotheses(self, concepts: List[Dict], relationships: List[Dict]) -> List[Dict[str, Any]]:
        """Generate comparative hypotheses"""
        hypotheses = []
        
        high_concepts = [c["concept"] for c in concepts if c["importance"] == "high"]
        for i in range(0, len(high_concepts)-1, 2):
            if i+1 < len(high_concepts):
                hypotheses.append({
                    "hypothesis": f"{high_concepts[i].title()} shows greater effect size than {high_concepts[i+1]} in controlled conditions",
                    "type": "comparative",
                    "comparison_targets": [high_concepts[i], high_concepts[i+1]],
                    "comparison_metric": "effect size",
                    "conditions": "controlled experimental conditions",
                    "testable": True,
                    "novelty": "moderate"
                })
        
        return hypotheses
    
    def _filter_by_novelty(self, hypotheses: List[Dict], novelty_threshold: str) -> List[Dict[str, Any]]:
        """Filter hypotheses by novelty threshold"""
        novelty_order = {"breakthrough": 3, "moderate": 2, "incremental": 1, "low": 0}
        threshold_value = novelty_order.get(novelty_threshold, 2)
        
        filtered = []
        for hyp in hypotheses:
            hyp_novelty = novelty_order.get(hyp.get("novelty", "moderate"), 2)
            if hyp_novelty >= threshold_value:
                filtered.append(hyp)
        
        return filtered
    
    def _evaluate_testability(self, hypotheses: List[Dict], domain: str) -> List[Dict[str, Any]]:
        """Evaluate and enhance testability of hypotheses"""
        for hyp in hypotheses:
            # Add testability assessment
            testability_score = self._calculate_testability_score(hyp, domain)
            hyp["testability_score"] = testability_score
            hyp["testability_assessment"] = self._assess_testability(testability_score)
            
            # Add suggested improvements for testability
            if testability_score < 0.7:
                hyp["testability_improvements"] = self._suggest_testability_improvements(hyp, domain)
        
        # Sort by testability score
        hypotheses.sort(key=lambda x: x.get("testability_score", 0), reverse=True)
        
        return hypotheses
    
    def _calculate_testability_score(self, hypothesis: Dict, domain: str) -> float:
        """Calculate testability score for a hypothesis"""
        score = 0.5  # Base score
        
        # Check for measurable variables
        if "variables" in hypothesis and len(hypothesis["variables"]) >= 2:
            score += 0.2
        
        # Check for specific predictions
        if any(word in hypothesis["hypothesis"].lower() for word in ["increase", "decrease", "correlation", "effect"]):
            score += 0.2
        
        # Check for operational definitions
        if "mechanism" in hypothesis or "conditions" in hypothesis:
            score += 0.1
        
        # Domain-specific adjustments
        if domain in ["biology", "chemistry", "physics"]:
            score += 0.1  # Generally more testable
        elif domain in ["psychology", "sociology"]:
            score += 0.05  # Moderately testable
        
        return min(score, 1.0)
    
    def _assess_testability(self, score: float) -> str:
        """Assess testability level based on score"""
        if score >= 0.8:
            return "highly testable"
        elif score >= 0.6:
            return "moderately testable"
        elif score >= 0.4:
            return "somewhat testable"
        else:
            return "difficult to test"
    
    def _suggest_testability_improvements(self, hypothesis: Dict, domain: str) -> List[str]:
        """Suggest improvements to make hypothesis more testable"""
        suggestions = []
        
        if "variables" not in hypothesis or len(hypothesis.get("variables", [])) < 2:
            suggestions.append("Define specific, measurable variables")
        
        if not any(word in hypothesis["hypothesis"].lower() for word in ["increase", "decrease", "correlation"]):
            suggestions.append("Specify the direction and magnitude of expected effects")
        
        if "conditions" not in hypothesis:
            suggestions.append("Define specific experimental conditions and controls")
        
        suggestions.append("Develop operational definitions for key concepts")
        suggestions.append("Specify measurable outcomes and success criteria")
        
        return suggestions[:3]  # Top 3 suggestions
    
    def _generate_experimental_designs(self, hypotheses: List[Dict], domain: str) -> List[Dict[str, Any]]:
        """Generate experimental designs for testing hypotheses"""
        designs = []
        
        for hyp in hypotheses[:5]:  # Top 5 hypotheses
            design = {
                "hypothesis_id": hypotheses.index(hyp),
                "design_type": self._determine_design_type(hyp),
                "variables": {
                    "independent": hyp.get("variables", ["factor_1"])[0] if hyp.get("variables") else "factor_1",
                    "dependent": hyp.get("variables", ["outcome"])[1] if len(hyp.get("variables", [])) > 1 else "outcome"
                },
                "methodology": self._suggest_methodology(hyp, domain),
                "sample_size": self._estimate_sample_size(hyp, domain),
                "duration": self._estimate_duration(hyp, domain),
                "feasibility": self._assess_experimental_feasibility(hyp, domain)
            }
            designs.append(design)
        
        return designs
    
    def _determine_design_type(self, hypothesis: Dict) -> str:
        """Determine appropriate experimental design type"""
        hyp_type = hypothesis.get("type", "explanatory")
        
        design_mapping = {
            "causal": "randomized controlled trial",
            "correlational": "observational study",
            "descriptive": "descriptive study",
            "predictive": "longitudinal study",
            "explanatory": "mechanistic study",
            "comparative": "comparative study"
        }
        
        return design_mapping.get(hyp_type, "experimental study")
    
    def _suggest_methodology(self, hypothesis: Dict, domain: str) -> List[str]:
        """Suggest methodology for testing hypothesis"""
        methodologies = []
        
        # Domain-specific methodologies
        if domain in ["biology", "medicine"]:
            methodologies.extend(["controlled laboratory experiment", "animal model study", "cell culture assay"])
        elif domain in ["psychology", "cognitive science"]:
            methodologies.extend(["behavioral experiment", "neuroimaging study", "survey research"])
        elif domain in ["computer science", "engineering"]:
            methodologies.extend(["simulation study", "prototype testing", "performance benchmarking"])
        else:
            methodologies.extend(["controlled experiment", "observational study", "computational modeling"])
        
        # Hypothesis-type specific additions
        if hypothesis.get("type") == "causal":
            methodologies.append("intervention study")
        elif hypothesis.get("type") == "correlational":
            methodologies.append("cross-sectional analysis")
        
        return methodologies[:3]
    
    def _estimate_sample_size(self, hypothesis: Dict, domain: str) -> str:
        """Estimate required sample size"""
        base_sizes = {
            "biology": "n=20-50 per group",
            "psychology": "n=30-100 per group", 
            "medicine": "n=50-200 per group",
            "computer science": "n=100-1000 data points",
            "engineering": "n=10-30 per condition"
        }
        
        return base_sizes.get(domain, "n=30-100 per group")
    
    def _estimate_duration(self, hypothesis: Dict, domain: str) -> str:
        """Estimate study duration"""
        if hypothesis.get("type") == "longitudinal":
            return "6-12 months"
        elif domain in ["biology", "medicine"]:
            return "3-6 months"
        else:
            return "1-3 months"
    
    def _assess_experimental_feasibility(self, hypothesis: Dict, domain: str) -> Dict[str, Any]:
        """Assess feasibility of experimental testing"""
        return {
            "cost": "moderate",
            "time": "reasonable",
            "technical_difficulty": "moderate",
            "ethical_considerations": "standard approval required",
            "resource_requirements": "standard laboratory resources"
        }
    
    def _assess_hypothesis_feasibility(self, hypotheses: List[Dict], domain: str) -> Dict[str, Any]:
        """Assess overall feasibility of hypothesis testing"""
        total_hypotheses = len(hypotheses)
        testable_count = sum(1 for h in hypotheses if h.get("testable", False))
        
        return {
            "total_hypotheses": total_hypotheses,
            "testable_hypotheses": testable_count,
            "testability_rate": f"{(testable_count/total_hypotheses)*100:.1f}%" if total_hypotheses > 0 else "0%",
            "recommended_priority": "high" if testable_count >= 3 else "medium" if testable_count >= 1 else "low",
            "resource_estimate": "moderate to high",
            "timeline_estimate": "3-12 months for comprehensive testing"
        }

/* Research Heavy Custom Styles */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* Query Input Styling */
#queryInput {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    transition: border-color 0.3s ease;
}

#queryInput:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Agent Status Styling */
.agent-status {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin: 4px 0;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.agent-status.queued {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
}

.agent-status.processing {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    animation: pulse 2s infinite;
}

.agent-status.completed {
    background-color: #d1e7dd;
    border-left: 4px solid #198754;
}

.agent-status.error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Progress Styling */
.progress {
    height: 8px;
    border-radius: 4px;
}

.phase-progress {
    margin: 10px 0;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #0d6efd;
}

.phase-progress h6 {
    margin: 0 0 8px 0;
    color: #495057;
}

.phase-progress .progress {
    height: 6px;
}

/* Results Styling */
.result-section {
    margin: 20px 0;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    border-left: 4px solid #0d6efd;
}

.result-section h5 {
    color: #495057;
    margin-bottom: 15px;
}

.agent-result {
    margin: 15px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.agent-result .agent-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.agent-result .agent-id {
    font-weight: bold;
    color: #0d6efd;
}

.agent-result .agent-status-badge {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
}

.agent-result .agent-content {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.6;
    white-space: pre-wrap;
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    max-height: 300px;
    overflow-y: auto;
}

/* Synthesis Styling */
.synthesis-section {
    margin: 30px 0;
    padding: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
}

.synthesis-section h4 {
    margin-bottom: 20px;
    font-weight: bold;
}

.synthesis-content {
    font-size: 15px;
    line-height: 1.7;
    white-space: pre-wrap;
}

/* Knowledge Base Styling */
.kb-entry {
    padding: 12px;
    margin: 8px 0;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #28a745;
}

.kb-entry-title {
    font-weight: bold;
    color: #495057;
    margin-bottom: 5px;
}

.kb-entry-content {
    font-size: 14px;
    color: #6c757d;
    max-height: 100px;
    overflow: hidden;
}

.kb-entry-meta {
    font-size: 12px;
    color: #adb5bd;
    margin-top: 8px;
}

/* Real-time Updates */
.real-time-update {
    padding: 8px 12px;
    margin: 4px 0;
    background-color: #e3f2fd;
    border-radius: 4px;
    border-left: 3px solid #2196f3;
    font-size: 13px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Button Styling */
.btn-primary {
    background: linear-gradient(45deg, #0d6efd, #0056b3);
    border: none;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3);
}

.btn-outline-primary:hover {
    transform: translateY(-1px);
}

/* Modal Styling */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 12px 12px 0 0;
}

/* Form Styling */
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Range Slider Styling */
.form-range::-webkit-slider-thumb {
    background-color: #0d6efd;
}

.form-range::-moz-range-thumb {
    background-color: #0d6efd;
    border: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }
    
    .card {
        margin-bottom: 15px;
    }
    
    .agent-result .agent-content {
        font-size: 12px;
        max-height: 200px;
    }
    
    .btn-group .btn {
        font-size: 12px;
        padding: 4px 8px;
    }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.online {
    background-color: #28a745;
}

.status-indicator.offline {
    background-color: #dc3545;
}

.status-indicator.processing {
    background-color: #ffc107;
    animation: pulse 1.5s infinite;
}

/* Export Buttons */
.export-section {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    text-align: center;
}

/* Syntax Highlighting Override */
pre[class*="language-"] {
    background: #2d3748;
    border-radius: 6px;
    font-size: 13px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

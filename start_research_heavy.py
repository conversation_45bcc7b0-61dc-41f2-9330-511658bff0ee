#!/usr/bin/env python3
"""
Research Heavy Enhanced Startup Script
Comprehensive multi-agent research orchestration with web interface
"""

import os
import sys
import time
import argparse
import subprocess
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    required_packages = [
        'flask', 'flask_socketio', 'requests', 'google.generativeai', 
        'yaml', 'numpy', 'socketio', 'werkzeug'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("📦 Install with: pip install -r requirements_web.txt")
        return False
    
    print("✅ All dependencies satisfied")
    return True

def check_config():
    """Check configuration file"""
    print("\n🔧 Checking configuration...")
    
    config_file = Path("config.yaml")
    if not config_file.exists():
        print("❌ config.yaml not found")
        return False
    
    try:
        import yaml
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        # Check for unified models configuration
        if 'unified_models' not in config:
            print("⚠️  unified_models configuration missing")
            return False
        
        # Check for at least one API key
        providers = config.get('unified_models', {}).get('providers', {})
        has_api_key = False
        
        for provider, provider_config in providers.items():
            api_key = provider_config.get('api_key', '')
            if api_key and api_key != 'YOUR_API_KEY' and len(api_key) > 10:
                print(f"  ✅ {provider} API key configured")
                has_api_key = True
            else:
                print(f"  ⚠️  {provider} API key not configured")
        
        if not has_api_key:
            print("⚠️  No API keys configured. Please update config.yaml or use the web interface.")
        
        print("✅ Configuration file valid")
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def check_tools():
    """Check if research tools are available"""
    print("\n🛠️  Checking research tools...")
    
    try:
        from tools import discover_tools
        import yaml
        
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        tools = discover_tools(config, silent=True)
        print(f"✅ Loaded {len(tools)} research tools")
        
        # Check for key tools
        key_tools = ['knowledge_base', 'gemini_research', 'semantic_scholar', 'dblp_search']
        for tool in key_tools:
            if tool in tools:
                print(f"  ✅ {tool}")
            else:
                print(f"  ⚠️  {tool} not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Tools check failed: {e}")
        return False

def start_web_interface(host='0.0.0.0', port=5000, debug=False):
    """Start the web interface"""
    print(f"\n🚀 Starting Research Heavy Web Interface...")
    print(f"📡 Server: http://{host}:{port}")
    print(f"🔧 Debug mode: {'ON' if debug else 'OFF'}")
    print("\n" + "="*60)
    print("🎓 RESEARCH HEAVY - Multi-Agent Research Orchestration")
    print("="*60)
    print("Features:")
    print("  • Multi-model LLM support (Gemini, OpenAI, Anthropic, etc.)")
    print("  • Dynamic agent scaling (3-20 agents)")
    print("  • Knowledge base with semantic search")
    print("  • Real-time progress tracking")
    print("  • Context-aware agent coordination")
    print("  • Automatic checkpointing")
    print("  • Export results (JSON, TXT)")
    print("="*60)
    
    try:
        from web_frontend import app, socketio
        socketio.run(app, debug=debug, host=host, port=port)
    except KeyboardInterrupt:
        print("\n\n👋 Research Heavy stopped by user")
    except Exception as e:
        print(f"\n❌ Failed to start web interface: {e}")
        return False
    
    return True

def start_cli_mode():
    """Start CLI mode for direct research"""
    print("\n🖥️  Starting CLI mode...")
    
    try:
        from enhanced_research_orchestrator import EnhancedResearchOrchestrator
        
        orchestrator = EnhancedResearchOrchestrator()
        
        print("✅ Enhanced Research Orchestrator initialized")
        print(f"🤖 Available agents: {orchestrator.num_agents}")
        print(f"🔧 Available models: {list(orchestrator.model_interface.providers.keys())}")
        
        while True:
            print("\n" + "="*50)
            query = input("🔍 Enter research query (or 'quit' to exit): ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                break
            
            if not query:
                continue
            
            print(f"\n🚀 Starting research: {query[:50]}...")
            
            try:
                result = orchestrator.orchestrate_research(query, 'comprehensive')
                
                if result.get('status') == 'success':
                    print(f"\n✅ Research completed in {result.get('execution_time', 0):.1f}s")
                    print(f"📊 Phases: {len(result.get('phases', {}))}")
                    
                    # Show synthesis
                    synthesis = result.get('final_synthesis', '')
                    if synthesis:
                        print(f"\n📄 Final Synthesis:")
                        print("-" * 50)
                        print(synthesis[:1000] + "..." if len(synthesis) > 1000 else synthesis)
                else:
                    print(f"❌ Research failed: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"❌ Research error: {e}")
        
        print("\n👋 CLI mode ended")
        return True
        
    except Exception as e:
        print(f"❌ Failed to start CLI mode: {e}")
        return False

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Research Heavy - Multi-Agent Research Orchestration')
    parser.add_argument('--mode', choices=['web', 'cli'], default='web', 
                       help='Run mode: web interface or CLI')
    parser.add_argument('--host', default='0.0.0.0', 
                       help='Host for web interface (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5000, 
                       help='Port for web interface (default: 5000)')
    parser.add_argument('--debug', action='store_true', 
                       help='Enable debug mode')
    parser.add_argument('--skip-checks', action='store_true', 
                       help='Skip dependency and configuration checks')
    
    args = parser.parse_args()
    
    print("🎓 Research Heavy - Enhanced Multi-Agent Research Orchestration")
    print("=" * 70)
    
    # Run checks unless skipped
    if not args.skip_checks:
        if not check_dependencies():
            print("\n💡 Install dependencies with: pip install -r requirements_web.txt")
            return 1
        
        if not check_config():
            print("\n💡 Please check your config.yaml file")
            return 1
        
        if not check_tools():
            print("\n💡 Some tools may not be available")
    
    # Start in requested mode
    if args.mode == 'web':
        success = start_web_interface(args.host, args.port, args.debug)
    else:
        success = start_cli_mode()
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())

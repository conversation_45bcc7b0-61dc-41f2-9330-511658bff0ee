#!/usr/bin/env python3
"""
Test Critical Fixes for Research Heavy System
Tests sequential execution, timeout increases, and API key updates
"""

import time
import yaml
import requests
from enhanced_research_orchestrator import EnhancedResearchOrchestrator

def test_sequential_execution():
    """Test that agents execute sequentially with rate limiting"""
    print("🔄 TESTING SEQUENTIAL EXECUTION WITH RATE LIMITING")
    print("=" * 60)
    
    orchestrator = EnhancedResearchOrchestrator(silent=True)
    
    # Test with a small number of agents to verify sequential execution
    print("🤖 Testing sequential execution with 3 agents...")
    
    start_time = time.time()
    
    # Create simple tasks
    tasks = [
        "Research machine learning fundamentals",
        "Analyze deep learning applications", 
        "Investigate neural network architectures"
    ]
    
    # Execute with sequential method
    results = orchestrator._execute_parallel_research_with_fallback(
        tasks, "test_phase", "gemini", True, "standard"
    )
    
    execution_time = time.time() - start_time
    
    print(f"📊 Execution Results:")
    print(f"   Total time: {execution_time:.1f}s")
    print(f"   Expected minimum time: {(len(tasks)-1) * 5}s (rate limiting)")
    print(f"   Agents processed: {len(results)}")
    
    # Verify sequential execution took appropriate time (should be at least 10s for 3 agents with 5s delays)
    expected_min_time = (len(tasks) - 1) * 5  # 5s delay between agents
    sequential_execution_verified = execution_time >= expected_min_time
    
    print(f"✅ Sequential execution verified: {sequential_execution_verified}")
    
    # Check results
    successful_agents = sum(1 for r in results if r.get('status') == 'success')
    print(f"📊 Successful agents: {successful_agents}/{len(results)}")
    
    return sequential_execution_verified and len(results) == len(tasks)

def test_timeout_configurations():
    """Test that timeout configurations are set to 600 seconds"""
    print("\n⏱️ TESTING TIMEOUT CONFIGURATIONS")
    print("=" * 60)
    
    # Test config.yaml timeouts
    print("📄 Checking config.yaml timeouts...")
    with open('config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    timeout_checks = []
    
    # Check unified models provider timeouts
    if 'unified_models' in config and 'providers' in config['unified_models']:
        for provider, provider_config in config['unified_models']['providers'].items():
            timeout = provider_config.get('timeout', 0)
            timeout_checks.append((f"Provider {provider}", timeout, timeout >= 600))
            print(f"   {provider}: {timeout}s {'✅' if timeout >= 600 else '❌'}")
    
    # Check orchestrator timeout
    if 'orchestrator' in config:
        orch_timeout = config['orchestrator'].get('task_timeout', 0)
        timeout_checks.append(("Orchestrator", orch_timeout, orch_timeout >= 600))
        print(f"   Orchestrator: {orch_timeout}s {'✅' if orch_timeout >= 600 else '❌'}")
    
    # Check GitHub timeout
    if 'github' in config:
        github_timeout = config['github'].get('timeout', 0)
        timeout_checks.append(("GitHub", github_timeout, github_timeout >= 600))
        print(f"   GitHub: {github_timeout}s {'✅' if github_timeout >= 600 else '❌'}")
    
    # Test unified model interface default timeout by checking the source code
    print("\n🔧 Checking UnifiedModelInterface default timeout...")

    # Read the source code to check default timeout value
    try:
        with open('unified_model_interface.py', 'r') as f:
            source_code = f.read()

        # Look for the default timeout value in BaseLLMProvider
        if "timeout', 600)" in source_code:
            default_timeout = 600
            timeout_checks.append(("Default Provider", default_timeout, True))
            print(f"   Default provider timeout: {default_timeout}s ✅")
        else:
            timeout_checks.append(("Default Provider", 300, False))
            print(f"   Default provider timeout: 300s ❌ (not updated)")
    except Exception as e:
        print(f"   ⚠️ Could not check default timeout: {e}")
        timeout_checks.append(("Default Provider", 0, False))
    
    all_timeouts_correct = all(check[2] for check in timeout_checks)
    print(f"\n✅ All timeouts >= 600s: {all_timeouts_correct}")
    
    return all_timeouts_correct

def test_api_key_updates():
    """Test that API key updates work through web interface"""
    print("\n🔑 TESTING API KEY UPDATES")
    print("=" * 60)
    
    # Test the config update endpoint
    print("📡 Testing /api/config endpoint...")
    
    test_config = {
        "providers": {
            "gemini": {
                "api_key": "test_gemini_key_12345",
                "model": "gemini-2.5-pro",
                "rate_limit_delay": 5.0,
                "timeout": 600
            },
            "openai": {
                "api_key": "test_openai_key_12345", 
                "model": "gpt-4o-mini",
                "rate_limit_delay": 1.0,
                "timeout": 600
            }
        },
        "github": {
            "token": "test_github_token_12345",
            "base_url": "https://api.github.com",
            "rate_limit_delay": 1.0,
            "timeout": 600
        },
        "default_provider": "gemini",
        "fallback_order": ["gemini", "openai", "openrouter", "anthropic", "moonshot", "deepseek"]
    }
    
    try:
        response = requests.post(
            'http://localhost:5001/api/config',
            headers={'Content-Type': 'application/json'},
            json=test_config,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Config update successful: {data.get('message', 'No message')}")
            
            # Verify config was saved
            with open('config.yaml', 'r') as f:
                updated_config = yaml.safe_load(f)
            
            # Check if test keys were saved
            gemini_key = updated_config.get('unified_models', {}).get('providers', {}).get('gemini', {}).get('api_key', '')
            github_token = updated_config.get('github', {}).get('token', '')
            
            keys_updated = 'test_gemini_key_12345' in gemini_key and 'test_github_token_12345' in github_token
            print(f"   ✅ API keys saved to config: {keys_updated}")
            
            return True
        else:
            print(f"   ❌ Config update failed: {response.status_code} - {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("   ⚠️ Web server not running - cannot test API key updates")
        print("   💡 Start server with: python start_research_heavy.py --mode web --port 5001")
        return False
    except Exception as e:
        print(f"   ❌ Config update error: {e}")
        return False

def test_orchestrator_reinitialization():
    """Test that orchestrator reinitializes correctly with new config"""
    print("\n🔄 TESTING ORCHESTRATOR REINITIALIZATION")
    print("=" * 60)
    
    print("🔧 Creating orchestrator with current config...")
    orchestrator1 = EnhancedResearchOrchestrator(silent=True)
    
    # Get initial provider count
    initial_providers = len(orchestrator1.model_interface.providers)
    print(f"   Initial providers: {initial_providers}")
    
    print("🔧 Creating new orchestrator (simulating reinitialization)...")
    orchestrator2 = EnhancedResearchOrchestrator(silent=True)
    
    # Get new provider count
    new_providers = len(orchestrator2.model_interface.providers)
    print(f"   New providers: {new_providers}")
    
    # Check if providers are properly initialized
    providers_match = initial_providers == new_providers
    print(f"   ✅ Provider count consistent: {providers_match}")
    
    # Check if tools are loaded
    tools_loaded = len(orchestrator2.tools) > 0
    print(f"   ✅ Tools loaded: {tools_loaded} ({len(orchestrator2.tools)} tools)")
    
    return providers_match and tools_loaded

def main():
    """Run all critical fix tests"""
    print("🧪 CRITICAL FIXES TEST SUITE")
    print("=" * 70)
    print("Testing: Sequential Execution, Timeout Increases, API Key Updates")
    print("=" * 70)
    
    tests = [
        ("Sequential Execution with Rate Limiting", test_sequential_execution),
        ("Timeout Configurations (600s)", test_timeout_configurations),
        ("API Key Updates via Web Interface", test_api_key_updates),
        ("Orchestrator Reinitialization", test_orchestrator_reinitialization)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name}...")
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} CRASHED: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 70)
    print("🎯 CRITICAL FIXES TEST RESULTS")
    print("=" * 70)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL CRITICAL FIXES VERIFIED!")
        print("✅ Sequential execution with rate limiting: WORKING")
        print("✅ 600-second timeouts: CONFIGURED")
        print("✅ API key updates: FUNCTIONAL")
        print("✅ Orchestrator reinitialization: WORKING")
        print("\n🚀 System ready for production with improved reliability!")
    else:
        print(f"\n⚠️ {failed} tests failed. Review the issues above.")

if __name__ == '__main__':
    main()

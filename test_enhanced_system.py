#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced Research Heavy System
Tests all components and fixes implemented
"""

import os
import sys
import time
import yaml
import json
from pathlib import Path

def test_tool_discovery():
    """Test that all 15 tools are discovered and loaded"""
    print("🔍 Testing Tool Discovery...")
    
    try:
        from tools import discover_tools
        
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        tools = discover_tools(config, silent=True)
        
        expected_tools = [
            'calculate', 'creative_ideation', 'dblp_search', 'gemini_research',
            'github_research', 'hypothesis_generation', 'knowledge_base',
            'prompt_decomposer', 'read_file', 'red_team_adversarial',
            'research_synthesis', 'search_web', 'semantic_scholar',
            'mark_task_complete', 'write_file'
        ]
        
        print(f"  ✅ Loaded {len(tools)} tools")
        
        missing_tools = []
        for tool in expected_tools:
            if tool in tools:
                print(f"  ✅ {tool}")
            else:
                print(f"  ❌ {tool} - MISSING")
                missing_tools.append(tool)
        
        if missing_tools:
            print(f"  ⚠️  Missing tools: {missing_tools}")
            return False
        
        print("  ✅ All expected tools loaded successfully")
        return True
        
    except Exception as e:
        print(f"  ❌ Tool discovery failed: {e}")
        return False

def test_unified_model_interface():
    """Test the unified model interface"""
    print("\n🤖 Testing Unified Model Interface...")
    
    try:
        from unified_model_interface import UnifiedModelInterface
        
        # Create test config
        test_config = {
            'default_provider': 'gemini',
            'fallback_order': ['gemini', 'openrouter'],
            'providers': {
                'gemini': {
                    'api_key': 'test_key',
                    'model': 'gemini-2.5-pro',
                    'rate_limit_delay': 5.0,
                    'timeout': 300
                }
            }
        }
        
        interface = UnifiedModelInterface(test_config)
        
        print(f"  ✅ Interface initialized with {len(interface.providers)} providers")
        
        # Test provider status
        status = interface.get_provider_status()
        print(f"  ✅ Provider status retrieved: {list(status.keys())}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Unified model interface test failed: {e}")
        return False

def test_enhanced_research_orchestrator():
    """Test the enhanced research orchestrator"""
    print("\n🔬 Testing Enhanced Research Orchestrator...")
    
    try:
        from enhanced_research_orchestrator import EnhancedResearchOrchestrator
        
        orchestrator = EnhancedResearchOrchestrator(silent=True)
        
        print(f"  ✅ Orchestrator initialized")
        print(f"  ✅ Number of agents: {orchestrator.num_agents}")
        print(f"  ✅ Available models: {list(orchestrator.model_interface.providers.keys())}")
        print(f"  ✅ Available tools: {len(orchestrator.tools)}")
        
        # Test context enhancement
        test_task = "Test research task"
        enhanced_standard = orchestrator._enhance_task_with_context(
            test_task, "literature_review", True, "standard"
        )
        enhanced_enhanced = orchestrator._enhance_task_with_context(
            test_task, "gap_analysis", True, "enhanced"
        )
        
        print(f"  ✅ Standard context enhancement: {len(enhanced_standard)} chars")
        print(f"  ✅ Enhanced context enhancement: {len(enhanced_enhanced)} chars")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Enhanced orchestrator test failed: {e}")
        return False

def test_prompt_decomposer_research_types():
    """Test prompt decomposer research type functionality"""
    print("\n📝 Testing Prompt Decomposer Research Types...")
    
    try:
        from tools.prompt_decomposer_tool import PromptDecomposerTool

        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)

        decomposer = PromptDecomposerTool(config)
        
        test_prompt = "Analyze machine learning techniques for natural language processing"
        
        # Test each research type
        research_types = ['literature_review', 'gap_analysis', 'idea_generation', 'implementation', 'comprehensive']
        
        for research_type in research_types:
            result = decomposer.decompose_by_research_type(test_prompt, research_type, 6)
            
            if result and 'tasks' in result or 'phases' in result:
                print(f"  ✅ {research_type}: Generated tasks successfully")
            else:
                print(f"  ❌ {research_type}: Failed to generate tasks")
                return False
        
        print("  ✅ All research types working correctly")
        return True
        
    except Exception as e:
        print(f"  ❌ Prompt decomposer test failed: {e}")
        return False

def test_semantic_scholar_rate_limiting():
    """Test Semantic Scholar rate limiting"""
    print("\n⏱️  Testing Semantic Scholar Rate Limiting...")
    
    try:
        from tools.semantic_scholar_tool import SemanticScholarTool

        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)

        tool = SemanticScholarTool(config)
        
        print(f"  ✅ Rate limit interval: {tool.min_request_interval}s")
        
        # Test that rate limiting variables are accessible
        from tools.semantic_scholar_tool import _semantic_scholar_lock, _semantic_scholar_last_request
        
        print(f"  ✅ Global rate limiting lock: {_semantic_scholar_lock}")
        print(f"  ✅ Last request time: {_semantic_scholar_last_request}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Semantic Scholar rate limiting test failed: {e}")
        return False

def test_knowledge_base_enhancements():
    """Test knowledge base semantic search and upload"""
    print("\n📚 Testing Knowledge Base Enhancements...")
    
    try:
        from tools.knowledge_base_tool import KnowledgeBaseTool

        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)

        kb_tool = KnowledgeBaseTool(config)
        
        # Test semantic search capability
        result = kb_tool.execute(
            action="semantic_search",
            query="test search query"
        )
        
        print(f"  ✅ Semantic search executed: {result.get('status', 'unknown')}")
        
        # Test that embeddings methods exist
        if hasattr(kb_tool, '_simple_text_embedding'):
            print("  ✅ Text embedding method available")
        else:
            print("  ❌ Text embedding method missing")
            return False
        
        if hasattr(kb_tool, '_cosine_similarity'):
            print("  ✅ Cosine similarity method available")
        else:
            print("  ❌ Cosine similarity method missing")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Knowledge base test failed: {e}")
        return False

def test_web_frontend_files():
    """Test that web frontend files exist and are properly structured"""
    print("\n🌐 Testing Web Frontend Files...")
    
    required_files = [
        'web_frontend.py',
        'templates/index.html',
        'static/css/style.css',
        'static/js/app.js'
    ]
    
    all_exist = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} - MISSING")
            all_exist = False
    
    # Test that HTML contains context mode options
    try:
        with open('templates/index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        if 'contextMode' in html_content:
            print("  ✅ Context mode options in HTML")
        else:
            print("  ❌ Context mode options missing from HTML")
            all_exist = False
        
        if 'Enhanced Context-Aware' in html_content:
            print("  ✅ Enhanced context mode option present")
        else:
            print("  ❌ Enhanced context mode option missing")
            all_exist = False
            
    except Exception as e:
        print(f"  ❌ HTML content test failed: {e}")
        all_exist = False
    
    return all_exist

def test_config_unified_models():
    """Test that config.yaml has unified models configuration"""
    print("\n⚙️  Testing Configuration...")
    
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        if 'unified_models' in config:
            print("  ✅ unified_models section present")
            
            unified_config = config['unified_models']
            
            if 'providers' in unified_config:
                print(f"  ✅ Providers configured: {list(unified_config['providers'].keys())}")
            else:
                print("  ❌ No providers in unified_models")
                return False
            
            if 'default_provider' in unified_config:
                print(f"  ✅ Default provider: {unified_config['default_provider']}")
            else:
                print("  ❌ No default provider set")
                return False
            
            if 'fallback_order' in unified_config:
                print(f"  ✅ Fallback order: {unified_config['fallback_order']}")
            else:
                print("  ❌ No fallback order set")
                return False
        else:
            print("  ❌ unified_models section missing from config")
            return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Config test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🎓 RESEARCH HEAVY - Enhanced System Test Suite")
    print("=" * 60)
    
    tests = [
        ("Tool Discovery", test_tool_discovery),
        ("Unified Model Interface", test_unified_model_interface),
        ("Enhanced Research Orchestrator", test_enhanced_research_orchestrator),
        ("Prompt Decomposer Research Types", test_prompt_decomposer_research_types),
        ("Semantic Scholar Rate Limiting", test_semantic_scholar_rate_limiting),
        ("Knowledge Base Enhancements", test_knowledge_base_enhancements),
        ("Web Frontend Files", test_web_frontend_files),
        ("Configuration", test_config_unified_models)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"  ❌ {test_name} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print("🎯 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! System is ready for use.")
        return 0
    else:
        print(f"\n⚠️  {failed} tests failed. Please review the issues above.")
        return 1

if __name__ == '__main__':
    sys.exit(main())

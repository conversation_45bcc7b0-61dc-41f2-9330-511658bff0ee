#!/usr/bin/env python3
"""
Complete Web Functionality Test for Research Heavy
Tests all critical web interface features
"""

import requests
import json
import time
import os

BASE_URL = "http://localhost:5001"

def test_complete_research_workflow():
    """Test the complete research workflow through the web API"""
    print("🔬 Testing Complete Research Workflow...")
    
    # Step 1: Configure API keys
    print("  Step 1: Configuring API keys...")
    config_data = {
        "providers": {
            "gemini": {
                "api_key": "AIzaSyCbiclmPhwSaIYNWugD09VSguiN5uBZhpI",
                "model": "gemini-2.5-pro",
                "rate_limit_delay": 5.0,
                "timeout": 300
            }
        },
        "default_provider": "gemini",
        "fallback_order": ["gemini"]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/config",
            headers={'Content-Type': 'application/json'},
            json=config_data
        )
        
        if response.status_code == 200:
            print("    ✅ API configuration successful")
        else:
            print(f"    ❌ API configuration failed: {response.text}")
            return False
    except Exception as e:
        print(f"    ❌ API configuration error: {e}")
        return False
    
    # Step 2: Start research with different context modes
    context_modes = [
        ("off", "Independent Agents"),
        ("standard", "Standard Context-Aware"),
        ("enhanced", "Enhanced Context-Aware")
    ]
    
    for context_mode, mode_name in context_modes:
        print(f"  Step 2: Testing {mode_name} mode...")
        
        research_data = {
            "query": f"Machine learning research with {mode_name}",
            "research_type": "literature_review",
            "model_provider": "gemini",
            "context_aware": context_mode != 'off',
            "context_mode": context_mode if context_mode != 'off' else 'standard',
            "num_agents": 3
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/research",
                headers={'Content-Type': 'application/json'},
                json=research_data
            )
            
            if response.status_code == 200:
                data = response.json()
                session_id = data.get('session_id')
                print(f"    ✅ {mode_name} research started: {session_id}")
                
                # Wait a moment and check status
                time.sleep(2)
                status_response = requests.get(f"{BASE_URL}/api/research/status/{session_id}")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    print(f"    ✅ Status check successful: {status_data.get('status', 'unknown')}")
                else:
                    print(f"    ⚠️ Status check failed: {status_response.text}")
            else:
                print(f"    ❌ {mode_name} research failed: {response.text}")
                return False
                
        except Exception as e:
            print(f"    ❌ {mode_name} research error: {e}")
            return False
    
    return True

def test_knowledge_base_functionality():
    """Test knowledge base upload and search functionality"""
    print("\n📚 Testing Knowledge Base Functionality...")
    
    # Test search functionality
    print("  Testing semantic search...")
    try:
        search_data = {
            "query": "machine learning algorithms",
            "search_type": "semantic_search"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/knowledge-base/search",
            headers={'Content-Type': 'application/json'},
            json=search_data
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"    ✅ Semantic search working: {data.get('status', 'unknown')}")
            print(f"    📊 Results found: {data.get('total_results', 0)}")
        else:
            print(f"    ❌ Semantic search failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"    ❌ Semantic search error: {e}")
        return False
    
    # Test list functionality
    print("  Testing knowledge base listing...")
    try:
        response = requests.get(f"{BASE_URL}/api/knowledge-base/list")
        
        if response.status_code == 200:
            data = response.json()
            print(f"    ✅ Knowledge base listing working")
            print(f"    📊 Entries: {len(data.get('entries', []))}")
        else:
            print(f"    ❌ Knowledge base listing failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"    ❌ Knowledge base listing error: {e}")
        return False
    
    return True

def test_research_types():
    """Test all research types"""
    print("\n🔬 Testing All Research Types...")
    
    research_types = [
        "literature_review",
        "gap_analysis", 
        "idea_generation",
        "implementation",
        "comprehensive"
    ]
    
    for research_type in research_types:
        print(f"  Testing {research_type}...")
        
        research_data = {
            "query": f"Test query for {research_type}",
            "research_type": research_type,
            "model_provider": "gemini",
            "context_aware": True,
            "context_mode": "standard",
            "num_agents": 3
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/api/research",
                headers={'Content-Type': 'application/json'},
                json=research_data
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"    ✅ {research_type} started successfully")
            else:
                print(f"    ❌ {research_type} failed: {response.text}")
                return False
                
        except Exception as e:
            print(f"    ❌ {research_type} error: {e}")
            return False
    
    return True

def test_model_providers():
    """Test model provider availability"""
    print("\n🤖 Testing Model Providers...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/models")
        
        if response.status_code == 200:
            data = response.json()
            providers = data.get('available_providers', [])
            models = data.get('models', {})
            
            print(f"    ✅ Available providers: {providers}")
            
            for provider in providers:
                if provider in models:
                    model_info = models[provider]
                    print(f"    📊 {provider}: {model_info.get('model', 'unknown')} - {'✅' if model_info.get('available') else '❌'}")
            
            return len(providers) > 0
        else:
            print(f"    ❌ Models endpoint failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"    ❌ Models endpoint error: {e}")
        return False

def test_web_interface_elements():
    """Test that web interface has all required elements"""
    print("\n🌐 Testing Web Interface Elements...")
    
    try:
        response = requests.get(BASE_URL)
        
        if response.status_code == 200:
            html = response.text
            
            required_elements = [
                ('Research Heavy', 'Title'),
                ('contextMode', 'Context Mode Options'),
                ('Start Research', 'Start Button'),
                ('Configure APIs', 'API Config Button'),
                ('Knowledge Base', 'KB Button'),
                ('numAgents', 'Agent Slider'),
                ('researchType', 'Research Type Dropdown'),
                ('modelProvider', 'Model Provider Dropdown')
            ]
            
            all_present = True
            for element, description in required_elements:
                if element in html:
                    print(f"    ✅ {description} found")
                else:
                    print(f"    ❌ {description} missing")
                    all_present = False
            
            return all_present
        else:
            print(f"    ❌ Web interface failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"    ❌ Web interface error: {e}")
        return False

def main():
    """Run complete web functionality tests"""
    print("🧪 COMPLETE WEB FUNCTIONALITY TESTS")
    print("=" * 60)
    print("🌐 Testing Research Heavy at http://localhost:5001")
    print("=" * 60)
    
    tests = [
        ("Web Interface Elements", test_web_interface_elements),
        ("Model Providers", test_model_providers),
        ("Knowledge Base Functionality", test_knowledge_base_functionality),
        ("Research Types", test_research_types),
        ("Complete Research Workflow", test_complete_research_workflow)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name}...")
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} CRASHED: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 COMPLETE WEB FUNCTIONALITY TEST RESULTS")
    print("=" * 60)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL WEB FUNCTIONALITY TESTS PASSED!")
        print("🌐 Research Heavy web interface is fully functional!")
        print("🚀 Ready for production use at http://localhost:5001")
    else:
        print(f"\n⚠️ {failed} tests failed. Check the issues above.")

if __name__ == '__main__':
    main()

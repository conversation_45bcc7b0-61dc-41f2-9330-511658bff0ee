import time
import threading
import sys
import os
from research_orchestrator import ResearchOrchestrator

class ResearchCLI:
    """CLI interface for PhD research orchestration"""
    
    def __init__(self):
        self.orchestrator = ResearchOrchestrator()
        self.start_time = None
        self.running = False
        
        # Extract model name for display
        model_full = self.orchestrator.config['openrouter']['model']
        if '/' in model_full:
            model_name = model_full.split('/')[-1]
        else:
            model_name = model_full
        
        # Clean up model name for display
        model_parts = model_name.split('-')
        clean_name = '-'.join(model_parts[:3]) if len(model_parts) >= 3 else model_name
        self.model_display = clean_name.upper() + " RESEARCH"
        
        # Research workflow types
        self.research_types = {
            "1": ("comprehensive", "Comprehensive Research Analysis"),
            "2": ("literature_review", "Literature Review & Analysis"),
            "3": ("gap_analysis", "Research Gap Analysis"),
            "4": ("idea_generation", "Novel Idea Generation"),
            "5": ("implementation", "Implementation Research")
        }
    
    def display_banner(self):
        """Display research CLI banner"""
        print("=" * 80)
        print("🎓 RESEARCH HEAVY - PhD Research Orchestration System")
        print("=" * 80)
        print(f"🤖 Model: {self.model_display}")
        print(f"🔬 Research Agents: {self.orchestrator.num_agents}")
        print(f"⏱️  Timeout: {self.orchestrator.task_timeout}s per agent")
        print("🧠 Enhanced with: Gemini API, DBLP, GitHub, Knowledge Base")
        print("=" * 80)
        print()
    
    def display_research_menu(self):
        """Display research workflow options"""
        print("📋 RESEARCH WORKFLOW OPTIONS:")
        print("-" * 40)
        for key, (workflow_type, description) in self.research_types.items():
            print(f"{key}. {description}")
        print("-" * 40)
        print("Type 'help' for detailed descriptions")
        print("Type 'status' to check API configurations")
        print("Type 'quit' to exit")
        print()
    
    def display_help(self):
        """Display detailed help information"""
        print("\n📖 RESEARCH WORKFLOW DESCRIPTIONS:")
        print("=" * 60)
        
        descriptions = {
            "comprehensive": """
🔍 COMPREHENSIVE RESEARCH ANALYSIS
- Complete 4-phase research workflow
- Literature review → Gap analysis → Idea generation → Implementation
- Uses all available research tools and APIs
- Generates comprehensive PhD-level analysis
- Stores findings in knowledge base
- Best for: Starting new research projects
""",
            "literature_review": """
📚 LITERATURE REVIEW & ANALYSIS  
- Systematic literature search and analysis
- DBLP academic database integration
- Citation analysis and trend identification
- Key researcher and institution mapping
- Best for: Understanding current state of research
""",
            "gap_analysis": """
🔍 RESEARCH GAP ANALYSIS
- Identifies methodological gaps
- Theoretical limitation analysis
- Empirical validation opportunities
- Cross-disciplinary research potential
- Best for: Finding research opportunities
""",
            "idea_generation": """
💡 NOVEL IDEA GENERATION
- Creative research idea synthesis
- Interdisciplinary approach generation
- Novel methodology design
- Breakthrough direction identification
- Best for: Developing research proposals
""",
            "implementation": """
⚙️ IMPLEMENTATION RESEARCH
- Technical approach analysis
- GitHub code repository search
- Tool and framework evaluation
- System architecture design
- Best for: Planning research implementation
"""
        }
        
        for workflow_type, description in descriptions.items():
            print(description)
        
        print("=" * 60)
    
    def check_api_status(self):
        """Check and display API configuration status"""
        print("\n🔧 API CONFIGURATION STATUS:")
        print("=" * 50)
        
        config = self.orchestrator.config
        
        # Check Gemini API
        if 'research_apis' in config and 'gemini' in config['research_apis']:
            gemini_key = config['research_apis']['gemini']['api_key']
            status = "✅ Configured" if gemini_key and gemini_key != 'YOUR_API_KEY' else "❌ Not configured"
            print(f"Gemini API: {status}")
            if status == "✅ Configured":
                print(f"  Model: {config['research_apis']['gemini']['model']}")
        else:
            print("Gemini API: ❌ Not configured")
        
        # Check DBLP (no API key required)
        print("DBLP API: ✅ Available (no key required)")
        print("  Database: 81,684+ computer science papers")
        
        # Check GitHub API
        if 'research_apis' in config and 'github' in config['research_apis']:
            github_token = config['research_apis']['github']['token']
            status = "✅ Configured" if github_token and github_token.startswith('ghp_') else "❌ Not configured"
            print(f"GitHub API: {status}")
        else:
            print("GitHub API: ❌ Not configured")
        
        # Check Semantic Scholar
        if 'research_apis' in config and 'semantic_scholar' in config['research_apis']:
            ss_status = config['research_apis']['semantic_scholar'].get('status', 'unknown')
            if ss_status == 'disabled_due_to_rate_limits':
                print("Semantic Scholar: ⚠️ Disabled (rate limits)")
                print("  Fallback: DBLP will be used instead")
            else:
                print("Semantic Scholar: ✅ Available")
        else:
            print("Semantic Scholar: ❌ Not configured")
        
        # Check Knowledge Base
        if 'research_apis' in config and 'knowledge_base' in config['research_apis']:
            kb_path = config['research_apis']['knowledge_base']['path']
            status = "✅ Configured" if os.path.exists(kb_path) else "⚠️ Path not found"
            print(f"Knowledge Base: {status}")
            print(f"  Path: {kb_path}")
        else:
            print("Knowledge Base: ❌ Not configured")
        
        print("=" * 50)
    
    def progress_monitor(self):
        """Monitor and display research progress"""
        while self.running:
            try:
                # Clear screen and show progress
                if not self.orchestrator.silent:
                    self.update_display()
                time.sleep(2)  # Update every 2 seconds
            except:
                break
    
    def update_display(self):
        """Update progress display"""
        if not self.running:
            return
        
        # Clear screen (works on most terminals)
        os.system('cls' if os.name == 'nt' else 'clear')
        
        # Show header
        print("🎓 RESEARCH HEAVY - Active Research Session")
        print("=" * 60)
        
        # Show elapsed time
        if self.start_time:
            elapsed = time.time() - self.start_time
            print(f"⏱️  Elapsed Time: {elapsed:.1f}s")
        
        # Show agent progress
        progress = self.orchestrator.agent_progress
        if progress:
            print(f"\n🤖 Research Agents Status:")
            for agent_id, status in progress.items():
                status_icon = "✅" if "COMPLETED" in status else "🔄" if "PROCESSING" in status else "⏳"
                print(f"   Agent {agent_id + 1}: {status_icon} {status}")
        
        print("\n" + "=" * 60)
        print("Press Ctrl+C to interrupt")
    
    def run_research_task(self, query: str, research_type: str):
        """Run research orchestration with live progress"""
        self.start_time = time.time()
        self.running = True
        
        # Start progress monitoring
        progress_thread = threading.Thread(target=self.progress_monitor, daemon=True)
        progress_thread.start()
        
        try:
            # Run the research orchestrator
            result = self.orchestrator.orchestrate_research(query, research_type)
            
            # Stop progress monitoring
            self.running = False
            
            # Final display update
            self.update_display()
            
            # Show results
            self.display_results(result, research_type)
            
            return result
            
        except KeyboardInterrupt:
            self.running = False
            print("\n\n⚠️ Research interrupted by user")
            return None
        except Exception as e:
            self.running = False
            print(f"\n\n❌ Research failed: {str(e)}")
            return None
    
    def display_results(self, result: dict, research_type: str):
        """Display research results"""
        print("\n" + "=" * 80)
        print("🎯 RESEARCH RESULTS")
        print("=" * 80)
        
        if result["status"] == "success":
            print(f"✅ Research Type: {research_type.replace('_', ' ').title()}")
            print(f"⏱️  Execution Time: {result['execution_time']:.1f}s")
            
            if research_type == "comprehensive":
                print(f"📊 Knowledge Base Entries: {result['knowledge_base_entries']}")
                print(f"🔬 Research Phases Completed: {len(result['phases'])}")
                
                print("\n📋 FINAL SYNTHESIS:")
                print("-" * 40)
                print(result['final_synthesis'])
                
            else:
                # Display specific results based on research type
                if 'synthesis' in result:
                    print("\n📋 SYNTHESIS:")
                    print("-" * 40)
                    print(result['synthesis'])
                
                if 'results' in result:
                    print(f"\n📊 Agent Results: {len(result['results'])} completed")
        
        else:
            print(f"❌ Research failed: {result.get('error', 'Unknown error')}")
        
        print("\n" + "=" * 80)
    
    def interactive_mode(self):
        """Run interactive research CLI"""
        self.display_banner()
        
        print("Welcome to Research Heavy! 🎓")
        print("Advanced PhD research orchestration with parallel AI agents.\n")
        
        while True:
            try:
                self.display_research_menu()
                user_input = input("Select research workflow (1-5) or command: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye! Happy researching!")
                    break
                
                elif user_input.lower() == 'help':
                    self.display_help()
                    continue
                
                elif user_input.lower() == 'status':
                    self.check_api_status()
                    continue
                
                elif user_input in self.research_types:
                    # Get research query
                    query = input("\n🔍 Enter your research query: ").strip()
                    
                    if not query:
                        print("❌ Please enter a research query.")
                        continue
                    
                    # Get research type
                    research_type, description = self.research_types[user_input]
                    
                    print(f"\n🚀 Starting {description}...")
                    print(f"📝 Query: {query}")
                    print(f"🤖 Deploying {self.orchestrator.num_agents} research agents...\n")
                    
                    # Run research
                    result = self.run_research_task(query, research_type)
                    
                    if result:
                        # Ask if user wants to save results
                        save = input("\n💾 Save results to knowledge base? (y/n): ").strip().lower()
                        if save in ['y', 'yes']:
                            print("✅ Results saved to knowledge base")
                
                else:
                    print("❌ Invalid selection. Please choose 1-5 or a valid command.")
                
            except KeyboardInterrupt:
                print("\n\n👋 Exiting Research Heavy...")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                print("Please try again or type 'quit' to exit.")

def main():
    """Main entry point for research CLI"""
    cli = ResearchCLI()
    cli.interactive_mode()

if __name__ == "__main__":
    main()

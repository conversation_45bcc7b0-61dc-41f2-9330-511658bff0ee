import json
import yaml
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
from agent import OpenRouterAgent

class ResearchOrchestrator:
    """Specialized orchestrator for PhD research workflows with enhanced parallel processing"""
    
    def __init__(self, config_path="config.yaml", silent=False):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Use research-specific settings if available
        if 'research_orchestrator' in self.config:
            research_config = self.config['research_orchestrator']
            self.num_agents = research_config['parallel_agents']
            self.task_timeout = research_config['task_timeout']
            self.aggregation_strategy = research_config['aggregation_strategy']
            self.research_phases = research_config.get('research_phases', [])
            self.agent_types = research_config.get('agent_types', {})
        else:
            # Fallback to regular orchestrator settings
            self.num_agents = self.config['orchestrator']['parallel_agents']
            self.task_timeout = self.config['orchestrator']['task_timeout']
            self.aggregation_strategy = self.config['orchestrator']['aggregation_strategy']
            self.research_phases = []
            self.agent_types = {}
        
        self.silent = silent
        
        # Track agent progress and results
        self.agent_progress = {}
        self.agent_results = {}
        self.progress_lock = threading.Lock()
        
        # Research-specific tracking
        self.research_context = {}
        self.knowledge_base_entries = []
    
    def orchestrate_research(self, research_query: str, research_type: str = "comprehensive") -> Dict[str, Any]:
        """
        Main research orchestration method with specialized research workflows
        """
        
        # Initialize research context
        self.research_context = {
            "query": research_query,
            "type": research_type,
            "start_time": time.time(),
            "phases_completed": [],
            "knowledge_gathered": []
        }
        
        # Reset progress tracking
        self.agent_progress = {}
        self.agent_results = {}
        
        if research_type == "comprehensive":
            return self._comprehensive_research_workflow(research_query)
        elif research_type == "literature_review":
            return self._literature_review_workflow(research_query)
        elif research_type == "gap_analysis":
            return self._gap_analysis_workflow(research_query)
        elif research_type == "idea_generation":
            return self._idea_generation_workflow(research_query)
        elif research_type == "implementation":
            return self._implementation_research_workflow(research_query)
        else:
            # Default to comprehensive research
            return self._comprehensive_research_workflow(research_query)
    
    def _comprehensive_research_workflow(self, query: str) -> Dict[str, Any]:
        """Comprehensive research workflow covering all phases"""
        
        # Phase 1: Literature Review and Data Gathering
        literature_tasks = self._generate_literature_tasks(query)
        literature_results = self._execute_parallel_research(literature_tasks, "literature_review")
        
        # Store literature findings in knowledge base
        self._store_research_findings(literature_results, "literature")
        
        # Phase 2: Gap Analysis
        gap_analysis_tasks = self._generate_gap_analysis_tasks(query, literature_results)
        gap_results = self._execute_parallel_research(gap_analysis_tasks, "gap_analysis")
        
        # Phase 3: Idea Generation
        idea_tasks = self._generate_idea_generation_tasks(query, literature_results, gap_results)
        idea_results = self._execute_parallel_research(idea_tasks, "idea_generation")
        
        # Phase 4: Implementation Planning
        implementation_tasks = self._generate_implementation_tasks(query, idea_results)
        implementation_results = self._execute_parallel_research(implementation_tasks, "implementation")
        
        # Final Synthesis
        final_synthesis = self._synthesize_comprehensive_research(
            literature_results, gap_results, idea_results, implementation_results, query
        )
        
        return {
            "status": "success",
            "research_type": "comprehensive",
            "query": query,
            "phases": {
                "literature_review": literature_results,
                "gap_analysis": gap_results,
                "idea_generation": idea_results,
                "implementation_planning": implementation_results
            },
            "final_synthesis": final_synthesis,
            "knowledge_base_entries": len(self.knowledge_base_entries),
            "execution_time": time.time() - self.research_context["start_time"]
        }
    
    def _literature_review_workflow(self, query: str) -> Dict[str, Any]:
        """Specialized literature review workflow"""
        
        # Generate literature-focused tasks
        tasks = [
            f"Search DBLP for recent papers on: {query}",
            f"Use Gemini to analyze key concepts and themes in: {query}",
            f"Search GitHub for implementation examples related to: {query}",
            f"Perform comprehensive web search for latest developments in: {query}",
            f"Synthesize findings and identify key research directions in: {query}",
            f"Analyze citation patterns and influential works in: {query}"
        ]
        
        # Execute with literature review agents
        results = self._execute_parallel_research(tasks, "literature_review")
        
        # Synthesize literature findings
        synthesis = self._synthesize_literature_review(results, query)
        
        return {
            "status": "success",
            "research_type": "literature_review",
            "query": query,
            "results": results,
            "synthesis": synthesis,
            "execution_time": time.time() - self.research_context["start_time"]
        }
    
    def _gap_analysis_workflow(self, query: str) -> Dict[str, Any]:
        """Specialized gap analysis workflow"""
        
        # First gather existing research
        literature_tasks = [
            f"Comprehensive literature search on: {query}",
            f"Identify current methodologies in: {query}",
            f"Analyze existing solutions for: {query}"
        ]
        
        literature_results = self._execute_parallel_research(literature_tasks, "literature_review")
        
        # Then perform gap analysis
        gap_tasks = [
            f"Identify methodological gaps in research on: {query}",
            f"Analyze theoretical limitations in current approaches to: {query}",
            f"Find empirical validation gaps in: {query} research",
            f"Identify cross-disciplinary opportunities for: {query}",
            f"Assess technological gaps in implementing: {query}",
            f"Evaluate scalability and practical limitations in: {query}"
        ]
        
        gap_results = self._execute_parallel_research(gap_tasks, "gap_analysis")
        
        # Synthesize gap analysis
        synthesis = self._synthesize_gap_analysis(literature_results, gap_results, query)
        
        return {
            "status": "success",
            "research_type": "gap_analysis",
            "query": query,
            "literature_foundation": literature_results,
            "gap_analysis": gap_results,
            "synthesis": synthesis,
            "execution_time": time.time() - self.research_context["start_time"]
        }
    
    def _idea_generation_workflow(self, query: str) -> Dict[str, Any]:
        """Specialized idea generation workflow"""
        
        # Generate creative research tasks
        tasks = [
            f"Generate novel research ideas combining {query} with AI/ML approaches",
            f"Identify interdisciplinary opportunities for {query} research",
            f"Propose innovative methodologies for studying {query}",
            f"Design novel experimental approaches for {query}",
            f"Generate creative applications of {query} in different domains",
            f"Synthesize breakthrough research directions for {query}"
        ]
        
        results = self._execute_parallel_research(tasks, "idea_generation")
        
        # Use research synthesis tool for advanced idea generation
        synthesis_results = self._advanced_idea_synthesis(results, query)
        
        return {
            "status": "success",
            "research_type": "idea_generation",
            "query": query,
            "generated_ideas": results,
            "advanced_synthesis": synthesis_results,
            "execution_time": time.time() - self.research_context["start_time"]
        }
    
    def _implementation_research_workflow(self, query: str) -> Dict[str, Any]:
        """Specialized implementation research workflow"""
        
        tasks = [
            f"Search GitHub for existing implementations related to: {query}",
            f"Analyze technical approaches and architectures for: {query}",
            f"Identify implementation challenges and solutions for: {query}",
            f"Research scalability considerations for: {query}",
            f"Find code examples and best practices for: {query}",
            f"Evaluate tools and frameworks suitable for: {query}"
        ]
        
        results = self._execute_parallel_research(tasks, "implementation")
        
        # Generate implementation roadmap
        roadmap = self._generate_implementation_roadmap(results, query)
        
        return {
            "status": "success",
            "research_type": "implementation",
            "query": query,
            "implementation_research": results,
            "implementation_roadmap": roadmap,
            "execution_time": time.time() - self.research_context["start_time"]
        }
    
    def _generate_literature_tasks(self, query: str) -> List[str]:
        """Generate literature review tasks"""
        return [
            f"Search DBLP academic database for papers on: {query}",
            f"Use Gemini for comprehensive analysis of: {query}",
            f"Search for recent developments and trends in: {query}",
            f"Identify key researchers and institutions working on: {query}",
            f"Analyze citation networks and influential papers in: {query}",
            f"Find review papers and surveys on: {query}"
        ]
    
    def _generate_gap_analysis_tasks(self, query: str, literature_results: List[Dict]) -> List[str]:
        """Generate gap analysis tasks based on literature findings"""
        return [
            f"Identify methodological gaps in {query} research based on literature review",
            f"Analyze theoretical limitations in current {query} approaches",
            f"Find empirical validation gaps in {query} studies",
            f"Identify cross-disciplinary research opportunities for {query}",
            f"Assess technological and implementation gaps in {query}",
            f"Evaluate scalability and practical limitations in {query} research"
        ]
    
    def _generate_idea_generation_tasks(self, query: str, literature_results: List[Dict], gap_results: List[Dict]) -> List[str]:
        """Generate idea generation tasks based on literature and gaps"""
        return [
            f"Generate novel research ideas for {query} addressing identified gaps",
            f"Propose interdisciplinary approaches combining {query} with other fields",
            f"Design innovative methodologies for {query} research",
            f"Create novel applications of {query} in unexplored domains",
            f"Synthesize breakthrough research directions for {query}",
            f"Generate creative solutions to identified limitations in {query}"
        ]
    
    def _generate_implementation_tasks(self, query: str, idea_results: List[Dict]) -> List[str]:
        """Generate implementation planning tasks"""
        return [
            f"Research technical implementation approaches for {query}",
            f"Identify tools, frameworks, and technologies for {query}",
            f"Analyze implementation challenges and solutions for {query}",
            f"Design system architecture for {query} implementation",
            f"Plan experimental validation strategy for {query}",
            f"Create development roadmap and milestones for {query}"
        ]
    
    def _execute_parallel_research(self, tasks: List[str], phase: str) -> List[Dict[str, Any]]:
        """Execute research tasks in parallel with specialized agents"""
        
        # Initialize progress tracking
        for i in range(len(tasks)):
            self.agent_progress[i] = "QUEUED"
        
        agent_results = []
        
        with ThreadPoolExecutor(max_workers=min(len(tasks), self.num_agents)) as executor:
            # Submit all tasks
            future_to_task = {
                executor.submit(self._run_research_agent, i, tasks[i], phase): i 
                for i in range(len(tasks))
            }
            
            # Collect results
            for future in as_completed(future_to_task, timeout=self.task_timeout):
                try:
                    result = future.result()
                    agent_results.append(result)
                except Exception as e:
                    task_id = future_to_task[future]
                    agent_results.append({
                        "agent_id": task_id,
                        "status": "error",
                        "response": f"Task {task_id + 1} failed: {str(e)}",
                        "execution_time": 0
                    })
        
        return sorted(agent_results, key=lambda x: x["agent_id"])
    
    def _run_research_agent(self, agent_id: int, task: str, phase: str) -> Dict[str, Any]:
        """Run a single research agent with specialized configuration"""
        try:
            self.update_agent_progress(agent_id, f"PROCESSING {phase.upper()}...")
            
            # Create specialized agent
            agent = OpenRouterAgent(silent=True)
            
            # Add research context to the task
            enhanced_task = self._enhance_task_with_context(task, phase)
            
            start_time = time.time()
            response = agent.run(enhanced_task)
            execution_time = time.time() - start_time
            
            self.update_agent_progress(agent_id, "COMPLETED")
            
            return {
                "agent_id": agent_id,
                "status": "success",
                "response": response,
                "task": task,
                "phase": phase,
                "execution_time": execution_time
            }
            
        except Exception as e:
            return {
                "agent_id": agent_id,
                "status": "error",
                "response": f"Error: {str(e)}",
                "task": task,
                "phase": phase,
                "execution_time": 0
            }
    
    def _enhance_task_with_context(self, task: str, phase: str) -> str:
        """Enhance task with research context and specialized instructions"""
        
        phase_instructions = {
            "literature_review": """
You are a research literature analyst. Focus on:
- Finding high-quality academic sources
- Identifying key concepts and methodologies
- Analyzing trends and developments
- Noting influential researchers and institutions
- Summarizing key findings and insights
""",
            "gap_analysis": """
You are a research gap analyst. Focus on:
- Identifying what's missing in current research
- Finding methodological limitations
- Spotting theoretical gaps
- Identifying practical implementation challenges
- Suggesting areas for future research
""",
            "idea_generation": """
You are a creative research ideator. Focus on:
- Generating novel and innovative ideas
- Combining concepts from different domains
- Proposing new methodologies
- Identifying unexplored applications
- Creating breakthrough research directions
""",
            "implementation": """
You are a technical implementation researcher. Focus on:
- Finding practical implementation approaches
- Identifying tools and technologies
- Analyzing technical challenges
- Proposing system architectures
- Planning development strategies
"""
        }
        
        instruction = phase_instructions.get(phase, "You are a research assistant.")
        
        return f"{instruction}\n\nTask: {task}\n\nProvide detailed, research-focused analysis."
    
    def update_agent_progress(self, agent_id: int, status: str):
        """Thread-safe progress tracking"""
        with self.progress_lock:
            self.agent_progress[agent_id] = status
    
    def _store_research_findings(self, results: List[Dict], category: str):
        """Store research findings in knowledge base"""
        for result in results:
            if result["status"] == "success":
                self.knowledge_base_entries.append({
                    "category": category,
                    "content": result["response"],
                    "task": result.get("task", ""),
                    "timestamp": time.time()
                })
    
    def _synthesize_comprehensive_research(self, literature_results: List[Dict], 
                                         gap_results: List[Dict], idea_results: List[Dict], 
                                         implementation_results: List[Dict], query: str) -> str:
        """Synthesize all research phases into comprehensive analysis"""
        
        # Create synthesis agent
        synthesis_agent = OpenRouterAgent(silent=True)
        
        # Prepare comprehensive synthesis prompt
        synthesis_prompt = f"""
You are a senior research synthesizer tasked with creating a comprehensive PhD-level research analysis.

Research Query: {query}

You have access to results from 4 research phases:

LITERATURE REVIEW FINDINGS:
{self._format_results_for_synthesis(literature_results)}

GAP ANALYSIS FINDINGS:
{self._format_results_for_synthesis(gap_results)}

IDEA GENERATION FINDINGS:
{self._format_results_for_synthesis(idea_results)}

IMPLEMENTATION RESEARCH FINDINGS:
{self._format_results_for_synthesis(implementation_results)}

Please provide a comprehensive synthesis that includes:
1. Executive Summary
2. Current State of Research
3. Identified Gaps and Opportunities
4. Novel Research Directions
5. Implementation Roadmap
6. Recommendations for PhD Research

Focus on creating a coherent, actionable research framework.
"""
        
        try:
            return synthesis_agent.run(synthesis_prompt)
        except Exception as e:
            return f"Synthesis failed: {str(e)}. Please review individual phase results."
    
    def _format_results_for_synthesis(self, results: List[Dict]) -> str:
        """Format results for synthesis prompt"""
        formatted = ""
        for i, result in enumerate(results, 1):
            if result["status"] == "success":
                formatted += f"Agent {i}: {result['response'][:500]}...\n\n"
        return formatted
    
    # Additional synthesis methods would be implemented similarly...
    def _synthesize_literature_review(self, results: List[Dict], query: str) -> str:
        """Synthesize literature review results"""
        return "Literature review synthesis completed."
    
    def _synthesize_gap_analysis(self, literature_results: List[Dict], gap_results: List[Dict], query: str) -> str:
        """Synthesize gap analysis results"""
        return "Gap analysis synthesis completed."
    
    def _advanced_idea_synthesis(self, results: List[Dict], query: str) -> Dict[str, Any]:
        """Advanced idea synthesis using research synthesis tool"""
        return {"advanced_ideas": "Generated using research synthesis tool"}
    
    def _generate_implementation_roadmap(self, results: List[Dict], query: str) -> Dict[str, Any]:
        """Generate implementation roadmap"""
        return {"roadmap": "Implementation roadmap generated"}

import google.generativeai as genai
import json
import yaml
from typing import Dict, Any
from .base_tool import BaseTool

class GeminiResearchTool(BaseTool):
    """Direct Gemini API integration for high-context research tasks"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        # Configure Gemini API
        if 'research_apis' in config and 'gemini' in config['research_apis']:
            genai.configure(api_key=config['research_apis']['gemini']['api_key'])
            self.model_name = config['research_apis']['gemini']['model']
            self.model = genai.GenerativeModel(self.model_name)
        else:
            self.model = None
    
    @property
    def name(self) -> str:
        return "gemini_research"
    
    @property
    def description(self) -> str:
        return "Use Google Gemini 2.5 Pro for high-context research analysis, literature synthesis, and complex reasoning tasks"
    
    @property
    def parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Research query or complex analysis task for <PERSON>"
                },
                "context": {
                    "type": "string",
                    "description": "Additional context or background information (optional)"
                },
                "task_type": {
                    "type": "string",
                    "enum": ["analysis", "synthesis", "generation", "reasoning", "comparison"],
                    "description": "Type of research task to perform"
                },
                "max_tokens": {
                    "type": "integer",
                    "description": "Maximum tokens for response (optional, default: 8192)",
                    "default": 8192
                }
            },
            "required": ["query", "task_type"]
        }
    
    def execute(self, query: str, task_type: str, context: str = "", max_tokens: int = 8192) -> Dict[str, Any]:
        """Execute Gemini research query"""
        try:
            if not self.model:
                return {
                    "error": "Gemini API not configured. Please check research_apis.gemini settings in config.yaml",
                    "status": "error"
                }
            
            # Construct research prompt based on task type
            prompt = self._construct_research_prompt(query, task_type, context)
            
            # Generate response with Gemini
            response = self.model.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=max_tokens,
                    temperature=0.7,
                    top_p=0.8,
                    top_k=40
                )
            )
            
            return {
                "status": "success",
                "response": response.text,
                "task_type": task_type,
                "model": self.model_name,
                "tokens_used": len(response.text.split()),  # Approximate
                "query": query
            }
            
        except Exception as e:
            return {
                "error": f"Gemini API error: {str(e)}",
                "status": "error",
                "query": query,
                "task_type": task_type
            }
    
    def _construct_research_prompt(self, query: str, task_type: str, context: str) -> str:
        """Construct specialized research prompt based on task type"""
        
        base_context = f"Context: {context}\n\n" if context else ""
        
        task_prompts = {
            "analysis": f"""You are a research analyst with expertise in academic research and critical thinking.
{base_context}Research Query: {query}

Please provide a comprehensive analysis that includes:
1. Key concepts and definitions
2. Current state of knowledge
3. Critical evaluation of existing approaches
4. Identification of strengths and limitations
5. Implications and significance

Provide detailed, evidence-based analysis with clear reasoning.""",

            "synthesis": f"""You are a research synthesizer specializing in combining multiple sources and perspectives.
{base_context}Research Query: {query}

Please provide a comprehensive synthesis that:
1. Integrates multiple perspectives and sources
2. Identifies common themes and patterns
3. Highlights contradictions or gaps
4. Develops unified understanding
5. Suggests novel connections or insights

Focus on creating coherent, integrated knowledge from diverse sources.""",

            "generation": f"""You are a creative research ideator specializing in novel concept generation.
{base_context}Research Query: {query}

Please generate innovative ideas that:
1. Build upon existing knowledge
2. Identify unexplored directions
3. Propose novel approaches or solutions
4. Consider interdisciplinary connections
5. Evaluate feasibility and potential impact

Focus on originality while maintaining scientific rigor.""",

            "reasoning": f"""You are a logical reasoning specialist with expertise in research methodology.
{base_context}Research Query: {query}

Please provide systematic reasoning that:
1. Breaks down complex problems logically
2. Identifies assumptions and premises
3. Evaluates evidence and arguments
4. Draws valid conclusions
5. Considers alternative explanations

Use rigorous logical analysis and clear argumentation.""",

            "comparison": f"""You are a comparative research analyst specializing in systematic comparisons.
{base_context}Research Query: {query}

Please provide a comprehensive comparison that:
1. Identifies key dimensions for comparison
2. Systematically evaluates similarities and differences
3. Analyzes strengths and weaknesses
4. Considers contextual factors
5. Draws meaningful conclusions

Provide balanced, objective comparative analysis."""
        }
        
        return task_prompts.get(task_type, task_prompts["analysis"])

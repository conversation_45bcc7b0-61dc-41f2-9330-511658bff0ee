import requests
import json
import time
from typing import Dict, Any, List
from .base_tool import BaseTool

class SemanticScholarTool(BaseTool):
    """Semantic Scholar API integration with rate limiting and fallback handling"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        if 'research_apis' in config and 'semantic_scholar' in config['research_apis']:
            self.api_key = config['research_apis']['semantic_scholar']['api_key']
            self.base_url = config['research_apis']['semantic_scholar']['base_url']
            self.status = config['research_apis']['semantic_scholar'].get('status', 'enabled')
        else:
            self.api_key = None
            self.base_url = 'https://api.semanticscholar.org/graph/v1'
            self.status = 'disabled'
        
        self.headers = {
            'x-api-key': self.api_key if self.api_key else '',
            'User-Agent': 'Research-Agent/1.0'
        }
        
        # Rate limiting - Semantic Scholar: 1 request per second cumulative across all endpoints
        self.last_request_time = 0
        self.min_request_interval = 1.1  # 1.1 seconds between requests for safety margin
    
    @property
    def name(self) -> str:
        return "semantic_scholar"
    
    @property
    def description(self) -> str:
        return "Search Semantic Scholar for academic papers with citation data, abstracts, and metadata (with rate limiting)"
    
    @property
    def parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query for academic papers"
                },
                "search_type": {
                    "type": "string",
                    "enum": ["paper", "author", "venue"],
                    "description": "Type of search to perform",
                    "default": "paper"
                },
                "fields": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Fields to retrieve (title, abstract, authors, year, citationCount, etc.)",
                    "default": ["title", "abstract", "authors", "year", "citationCount", "url"]
                },
                "max_results": {
                    "type": "integer",
                    "description": "Maximum number of results to return (1-100)",
                    "default": 20,
                    "minimum": 1,
                    "maximum": 100
                },
                "year_filter": {
                    "type": "string",
                    "description": "Year filter (e.g., '2020-2024')",
                    "default": ""
                },
                "venue_filter": {
                    "type": "string",
                    "description": "Venue filter (e.g., 'ICML', 'NeurIPS')",
                    "default": ""
                },
                "paper_id": {
                    "type": "string",
                    "description": "Specific paper ID for detailed lookup",
                    "default": ""
                }
            },
            "required": ["query"]
        }
    
    def execute(self, query: str, search_type: str = "paper", 
                fields: List[str] = None, max_results: int = 20,
                year_filter: str = "", venue_filter: str = "", 
                paper_id: str = "") -> Dict[str, Any]:
        """Execute Semantic Scholar search with rate limiting"""
        
        # Check if API is disabled
        if self.status == 'disabled_due_to_rate_limits':
            return {
                "error": "Semantic Scholar API is currently disabled due to rate limits. Using DBLP as fallback.",
                "status": "disabled",
                "fallback_suggestion": "Use dblp_search tool instead",
                "query": query
            }
        
        try:
            # Rate limiting
            self._rate_limit()
            
            if not fields:
                fields = ["title", "abstract", "authors", "year", "citationCount", "url"]
            
            # Handle specific paper lookup
            if paper_id:
                return self._get_paper_details(paper_id, fields)
            
            # Handle different search types
            if search_type == "paper":
                return self._search_papers(query, fields, max_results, year_filter, venue_filter)
            elif search_type == "author":
                return self._search_authors(query, max_results)
            elif search_type == "venue":
                return self._search_venues(query, max_results)
            else:
                return self._search_papers(query, fields, max_results, year_filter, venue_filter)
                
        except Exception as e:
            return {
                "error": f"Semantic Scholar API error: {str(e)}",
                "status": "error",
                "query": query,
                "fallback_suggestion": "Consider using DBLP search as alternative"
            }
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def _search_papers(self, query: str, fields: List[str], max_results: int, 
                      year_filter: str, venue_filter: str) -> Dict[str, Any]:
        """Search for papers"""
        try:
            # Construct query with filters
            search_query = query
            if year_filter:
                search_query += f" year:{year_filter}"
            if venue_filter:
                search_query += f" venue:{venue_filter}"
            
            params = {
                'query': search_query,
                'limit': min(max_results, 100),
                'fields': ','.join(fields)
            }
            
            response = requests.get(f"{self.base_url}/paper/search", 
                                  headers=self.headers, params=params, timeout=30)
            
            # Handle rate limiting
            if response.status_code == 429:
                return {
                    "error": "Rate limit exceeded. API temporarily disabled.",
                    "status": "rate_limited",
                    "fallback_suggestion": "Use DBLP search instead",
                    "query": query
                }
            
            response.raise_for_status()
            data = response.json()
            
            papers = []
            for paper in data.get('data', []):
                paper_info = {
                    'paperId': paper.get('paperId', ''),
                    'title': paper.get('title', ''),
                    'abstract': paper.get('abstract', ''),
                    'year': paper.get('year', ''),
                    'citationCount': paper.get('citationCount', 0),
                    'url': paper.get('url', ''),
                    'authors': [author.get('name', '') for author in paper.get('authors', [])],
                    'venue': paper.get('venue', ''),
                    'doi': paper.get('externalIds', {}).get('DOI', ''),
                    'arxivId': paper.get('externalIds', {}).get('ArXiv', '')
                }
                papers.append(paper_info)
            
            return {
                "status": "success",
                "search_type": "papers",
                "query": query,
                "total_results": data.get('total', len(papers)),
                "returned_results": len(papers),
                "papers": papers,
                "summary": f"Found {len(papers)} papers for '{query}' via Semantic Scholar"
            }
            
        except Exception as e:
            return {
                "error": f"Paper search failed: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _search_authors(self, query: str, max_results: int) -> Dict[str, Any]:
        """Search for authors"""
        try:
            params = {
                'query': query,
                'limit': min(max_results, 100)
            }
            
            response = requests.get(f"{self.base_url}/author/search", 
                                  headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 429:
                return {
                    "error": "Rate limit exceeded. API temporarily disabled.",
                    "status": "rate_limited",
                    "query": query
                }
            
            response.raise_for_status()
            data = response.json()
            
            authors = []
            for author in data.get('data', []):
                author_info = {
                    'authorId': author.get('authorId', ''),
                    'name': author.get('name', ''),
                    'affiliations': author.get('affiliations', []),
                    'paperCount': author.get('paperCount', 0),
                    'citationCount': author.get('citationCount', 0),
                    'hIndex': author.get('hIndex', 0),
                    'url': author.get('url', '')
                }
                authors.append(author_info)
            
            return {
                "status": "success",
                "search_type": "authors",
                "query": query,
                "returned_results": len(authors),
                "authors": authors,
                "summary": f"Found {len(authors)} authors for '{query}' via Semantic Scholar"
            }
            
        except Exception as e:
            return {
                "error": f"Author search failed: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _search_venues(self, query: str, max_results: int) -> Dict[str, Any]:
        """Search for venues (conferences/journals)"""
        try:
            params = {
                'query': query,
                'limit': min(max_results, 100)
            }
            
            response = requests.get(f"{self.base_url}/venue/search", 
                                  headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 429:
                return {
                    "error": "Rate limit exceeded. API temporarily disabled.",
                    "status": "rate_limited",
                    "query": query
                }
            
            response.raise_for_status()
            data = response.json()
            
            venues = []
            for venue in data.get('data', []):
                venue_info = {
                    'venueId': venue.get('venueId', ''),
                    'name': venue.get('name', ''),
                    'type': venue.get('type', ''),
                    'url': venue.get('url', ''),
                    'paperCount': venue.get('paperCount', 0)
                }
                venues.append(venue_info)
            
            return {
                "status": "success",
                "search_type": "venues",
                "query": query,
                "returned_results": len(venues),
                "venues": venues,
                "summary": f"Found {len(venues)} venues for '{query}' via Semantic Scholar"
            }
            
        except Exception as e:
            return {
                "error": f"Venue search failed: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _get_paper_details(self, paper_id: str, fields: List[str]) -> Dict[str, Any]:
        """Get detailed information about a specific paper"""
        try:
            params = {
                'fields': ','.join(fields + ['references', 'citations'])
            }
            
            response = requests.get(f"{self.base_url}/paper/{paper_id}", 
                                  headers=self.headers, params=params, timeout=30)
            
            if response.status_code == 429:
                return {
                    "error": "Rate limit exceeded. API temporarily disabled.",
                    "status": "rate_limited",
                    "paper_id": paper_id
                }
            
            response.raise_for_status()
            paper = response.json()
            
            paper_details = {
                'paperId': paper.get('paperId', ''),
                'title': paper.get('title', ''),
                'abstract': paper.get('abstract', ''),
                'year': paper.get('year', ''),
                'citationCount': paper.get('citationCount', 0),
                'url': paper.get('url', ''),
                'authors': [author.get('name', '') for author in paper.get('authors', [])],
                'venue': paper.get('venue', ''),
                'doi': paper.get('externalIds', {}).get('DOI', ''),
                'arxivId': paper.get('externalIds', {}).get('ArXiv', ''),
                'references': [ref.get('title', '') for ref in paper.get('references', [])[:10]],  # First 10 refs
                'citations': [cit.get('title', '') for cit in paper.get('citations', [])[:10]]  # First 10 citations
            }
            
            return {
                "status": "success",
                "search_type": "paper_details",
                "paper_id": paper_id,
                "paper": paper_details,
                "summary": f"Retrieved details for paper '{paper_details['title']}'"
            }
            
        except Exception as e:
            return {
                "error": f"Paper details retrieval failed: {str(e)}",
                "status": "error",
                "paper_id": paper_id
            }

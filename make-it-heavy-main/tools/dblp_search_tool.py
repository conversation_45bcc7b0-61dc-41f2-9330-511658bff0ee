import requests
import json
import time
from typing import Dict, Any, List
from .base_tool import BaseTool

class DBLPSearchTool(BaseTool):
    """DBLP academic database search and analysis tool"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        if 'research_apis' in config and 'dblp' in config['research_apis']:
            self.base_url = config['research_apis']['dblp']['base_url']
            self.author_url = config['research_apis']['dblp']['author_url']
            self.venue_url = config['research_apis']['dblp']['venue_url']
        else:
            self.base_url = 'https://dblp.org/search/publ/api'
            self.author_url = 'https://dblp.org/search/author/api'
            self.venue_url = 'https://dblp.org/search/venue/api'
    
    @property
    def name(self) -> str:
        return "dblp_search"
    
    @property
    def description(self) -> str:
        return "Search DBLP academic database for papers, authors, and venues. Access to 81,684+ computer science publications."
    
    @property
    def parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query for papers, authors, or venues"
                },
                "search_type": {
                    "type": "string",
                    "enum": ["publications", "authors", "venues"],
                    "description": "Type of search to perform",
                    "default": "publications"
                },
                "max_results": {
                    "type": "integer",
                    "description": "Maximum number of results to return (1-100)",
                    "default": 20,
                    "minimum": 1,
                    "maximum": 100
                },
                "year_filter": {
                    "type": "string",
                    "description": "Year filter (e.g., '2020-2024' or '2023')",
                    "default": ""
                },
                "venue_filter": {
                    "type": "string",
                    "description": "Venue filter (e.g., 'ICML', 'NeurIPS')",
                    "default": ""
                }
            },
            "required": ["query"]
        }
    
    def execute(self, query: str, search_type: str = "publications", max_results: int = 20, 
                year_filter: str = "", venue_filter: str = "") -> Dict[str, Any]:
        """Execute DBLP search"""
        try:
            # Select appropriate URL based on search type
            if search_type == "authors":
                url = self.author_url
            elif search_type == "venues":
                url = self.venue_url
            else:
                url = self.base_url
            
            # Construct search parameters
            params = {
                'q': query,
                'h': min(max_results, 100),  # DBLP max is 100
                'format': 'json'
            }
            
            # Add filters if provided
            if year_filter:
                params['q'] += f' year:{year_filter}'
            if venue_filter and search_type == "publications":
                params['q'] += f' venue:{venue_filter}'
            
            # Make API request
            response = requests.get(url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Parse results based on search type
            if search_type == "publications":
                return self._parse_publications(data, query)
            elif search_type == "authors":
                return self._parse_authors(data, query)
            elif search_type == "venues":
                return self._parse_venues(data, query)
            
        except requests.exceptions.RequestException as e:
            return {
                "error": f"DBLP API request failed: {str(e)}",
                "status": "error",
                "query": query
            }
        except Exception as e:
            return {
                "error": f"DBLP search error: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _parse_publications(self, data: Dict, query: str) -> Dict[str, Any]:
        """Parse publication search results"""
        try:
            result = data.get('result', {})
            hits = result.get('hits', {})
            total = int(hits.get('@total', 0))
            
            publications = []
            hit_list = hits.get('hit', [])
            if not isinstance(hit_list, list):
                hit_list = [hit_list]
            
            for hit in hit_list:
                info = hit.get('info', {})
                pub = {
                    'title': info.get('title', 'N/A'),
                    'authors': self._extract_authors(info.get('authors', {})),
                    'venue': info.get('venue', 'N/A'),
                    'year': info.get('year', 'N/A'),
                    'type': info.get('type', 'N/A'),
                    'doi': info.get('doi', ''),
                    'url': info.get('url', ''),
                    'key': info.get('key', '')
                }
                publications.append(pub)
            
            return {
                "status": "success",
                "search_type": "publications",
                "query": query,
                "total_results": total,
                "returned_results": len(publications),
                "publications": publications,
                "summary": f"Found {total} publications for '{query}'. Showing top {len(publications)} results."
            }
            
        except Exception as e:
            return {
                "error": f"Failed to parse publication results: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _parse_authors(self, data: Dict, query: str) -> Dict[str, Any]:
        """Parse author search results"""
        try:
            result = data.get('result', {})
            hits = result.get('hits', {})
            total = int(hits.get('@total', 0))
            
            authors = []
            hit_list = hits.get('hit', [])
            if not isinstance(hit_list, list):
                hit_list = [hit_list]
            
            for hit in hit_list:
                info = hit.get('info', {})
                author = {
                    'name': info.get('author', 'N/A'),
                    'aliases': info.get('aliases', []),
                    'affiliation': info.get('notes', {}).get('note', []),
                    'url': info.get('url', ''),
                    'key': info.get('key', '')
                }
                authors.append(author)
            
            return {
                "status": "success",
                "search_type": "authors",
                "query": query,
                "total_results": total,
                "returned_results": len(authors),
                "authors": authors,
                "summary": f"Found {total} authors for '{query}'. Showing top {len(authors)} results."
            }
            
        except Exception as e:
            return {
                "error": f"Failed to parse author results: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _parse_venues(self, data: Dict, query: str) -> Dict[str, Any]:
        """Parse venue search results"""
        try:
            result = data.get('result', {})
            hits = result.get('hits', {})
            total = int(hits.get('@total', 0))
            
            venues = []
            hit_list = hits.get('hit', [])
            if not isinstance(hit_list, list):
                hit_list = [hit_list]
            
            for hit in hit_list:
                info = hit.get('info', {})
                venue = {
                    'name': info.get('venue', 'N/A'),
                    'acronym': info.get('acronym', ''),
                    'type': info.get('type', 'N/A'),
                    'url': info.get('url', ''),
                    'key': info.get('key', '')
                }
                venues.append(venue)
            
            return {
                "status": "success",
                "search_type": "venues",
                "query": query,
                "total_results": total,
                "returned_results": len(venues),
                "venues": venues,
                "summary": f"Found {total} venues for '{query}'. Showing top {len(venues)} results."
            }
            
        except Exception as e:
            return {
                "error": f"Failed to parse venue results: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _extract_authors(self, authors_data) -> List[str]:
        """Extract author names from DBLP authors data"""
        if not authors_data:
            return []
        
        author_list = authors_data.get('author', [])
        if not isinstance(author_list, list):
            author_list = [author_list]
        
        # Extract text content from author entries
        authors = []
        for author in author_list:
            if isinstance(author, dict):
                name = author.get('#text', author.get('text', str(author)))
            else:
                name = str(author)
            authors.append(name)
        
        return authors

import json
import re
from typing import Dict, Any, List
from .base_tool import BaseTool

class RedTeamAdversarialTool(BaseTool):
    """Advanced adversarial analysis tool for finding flaws, biases, and weaknesses in reasoning"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    @property
    def name(self) -> str:
        return "red_team_adversarial"
    
    @property
    def description(self) -> str:
        return "Critically analyze arguments, findings, and conclusions to identify flaws, biases, weaknesses, and alternative explanations"
    
    @property
    def parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "target_content": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Content to analyze (arguments, conclusions, research findings, etc.)"
                },
                "analysis_type": {
                    "type": "string",
                    "enum": ["logical_flaws", "bias_detection", "evidence_critique", "alternative_explanations", "comprehensive"],
                    "description": "Type of adversarial analysis to perform",
                    "default": "comprehensive"
                },
                "domain": {
                    "type": "string",
                    "description": "Domain or field of the content being analyzed",
                    "default": ""
                },
                "rigor_level": {
                    "type": "string",
                    "enum": ["standard", "high", "extreme"],
                    "description": "Level of critical rigor to apply",
                    "default": "high"
                },
                "focus_areas": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Specific areas to focus criticism on",
                    "default": []
                }
            },
            "required": ["target_content"]
        }
    
    def execute(self, target_content: List[str], analysis_type: str = "comprehensive",
                domain: str = "", rigor_level: str = "high", focus_areas: List[str] = None) -> Dict[str, Any]:
        """Execute adversarial analysis on target content"""
        try:
            if not target_content:
                return {
                    "error": "Target content is required for adversarial analysis",
                    "status": "error"
                }
            
            focus_areas = focus_areas or []
            
            # Perform comprehensive analysis
            analysis_results = {}
            
            if analysis_type in ["logical_flaws", "comprehensive"]:
                analysis_results["logical_flaws"] = self._analyze_logical_flaws(target_content, rigor_level)
            
            if analysis_type in ["bias_detection", "comprehensive"]:
                analysis_results["bias_detection"] = self._detect_biases(target_content, domain, rigor_level)
            
            if analysis_type in ["evidence_critique", "comprehensive"]:
                analysis_results["evidence_critique"] = self._critique_evidence(target_content, domain, rigor_level)
            
            if analysis_type in ["alternative_explanations", "comprehensive"]:
                analysis_results["alternative_explanations"] = self._generate_alternative_explanations(target_content, domain)
            
            # Generate overall assessment
            overall_assessment = self._generate_overall_assessment(analysis_results, rigor_level)
            
            # Generate recommendations for improvement
            recommendations = self._generate_improvement_recommendations(analysis_results, domain)
            
            return {
                "status": "success",
                "analysis_type": analysis_type,
                "domain": domain,
                "rigor_level": rigor_level,
                "content_analyzed": len(target_content),
                "analysis_results": analysis_results,
                "overall_assessment": overall_assessment,
                "improvement_recommendations": recommendations,
                "summary": f"Identified {self._count_total_issues(analysis_results)} potential issues across {len(analysis_results)} analysis categories"
            }
            
        except Exception as e:
            return {
                "error": f"Adversarial analysis failed: {str(e)}",
                "status": "error"
            }
    
    def _analyze_logical_flaws(self, content: List[str], rigor_level: str) -> Dict[str, Any]:
        """Analyze logical flaws and reasoning errors"""
        
        logical_fallacies = []
        reasoning_gaps = []
        invalid_inferences = []
        
        # Common logical fallacy patterns
        fallacy_patterns = {
            "ad_hominem": [r"(?:he|she|they|author|researcher)\s+(?:is|are)\s+(?:wrong|biased|incompetent)", 
                          r"(?:can't|cannot)\s+trust\s+(?:him|her|them)"],
            "straw_man": [r"(?:claims?|argues?|says?)\s+that\s+all\s+", r"oversimplif"],
            "false_dichotomy": [r"(?:either|only)\s+.*\s+or\s+", r"no\s+other\s+(?:option|choice|alternative)"],
            "hasty_generalization": [r"all\s+.*\s+are\s+", r"always\s+", r"never\s+", r"every\s+.*\s+is\s+"],
            "appeal_to_authority": [r"expert\s+says\s+", r"according\s+to\s+.*\s+therefore"],
            "correlation_causation": [r"because\s+.*\s+correlates?\s+", r"since\s+.*\s+is\s+associated"],
            "circular_reasoning": [r"because\s+it\s+is\s+", r"by\s+definition"],
            "appeal_to_emotion": [r"(?:terrible|horrible|wonderful|amazing)\s+", r"think\s+of\s+the\s+"]
        }
        
        for content_item in content:
            for fallacy_type, patterns in fallacy_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, content_item.lower()):
                        logical_fallacies.append({
                            "type": fallacy_type,
                            "description": f"Potential {fallacy_type.replace('_', ' ')} detected",
                            "evidence": content_item[:150] + "...",
                            "severity": self._assess_fallacy_severity(fallacy_type, rigor_level),
                            "confidence": "medium"
                        })
        
        # Analyze reasoning gaps
        reasoning_gaps = self._identify_reasoning_gaps(content, rigor_level)
        
        # Identify invalid inferences
        invalid_inferences = self._identify_invalid_inferences(content, rigor_level)
        
        return {
            "logical_fallacies": logical_fallacies[:10],  # Top 10
            "reasoning_gaps": reasoning_gaps[:8],
            "invalid_inferences": invalid_inferences[:6],
            "total_logical_issues": len(logical_fallacies) + len(reasoning_gaps) + len(invalid_inferences)
        }
    
    def _detect_biases(self, content: List[str], domain: str, rigor_level: str) -> Dict[str, Any]:
        """Detect various types of biases in content"""
        
        cognitive_biases = []
        selection_biases = []
        confirmation_biases = []
        cultural_biases = []
        
        # Cognitive bias patterns
        bias_patterns = {
            "confirmation_bias": [r"supports?\s+(?:our|my|the)\s+(?:hypothesis|theory|view)",
                                 r"confirms?\s+(?:what|that)\s+we\s+(?:expected|predicted)"],
            "availability_heuristic": [r"recent\s+(?:study|example|case)\s+shows?",
                                      r"well-known\s+(?:example|case)"],
            "anchoring_bias": [r"(?:first|initial)\s+(?:estimate|value|finding)",
                              r"starting\s+(?:point|assumption)"],
            "survivorship_bias": [r"successful\s+(?:cases|examples)\s+show",
                                 r"(?:winners|survivors)\s+demonstrate"],
            "selection_bias": [r"(?:selected|chosen)\s+(?:participants|subjects|cases)",
                              r"convenience\s+sample"],
            "publication_bias": [r"published\s+(?:studies|research)\s+show",
                               r"peer-reviewed\s+literature\s+indicates"]
        }
        
        for content_item in content:
            for bias_type, patterns in bias_patterns.items():
                for pattern in patterns:
                    if re.search(pattern, content_item.lower()):
                        bias_category = self._categorize_bias(bias_type)
                        bias_entry = {
                            "type": bias_type,
                            "category": bias_category,
                            "description": f"Potential {bias_type.replace('_', ' ')} detected",
                            "evidence": content_item[:150] + "...",
                            "severity": self._assess_bias_severity(bias_type, rigor_level),
                            "mitigation": self._suggest_bias_mitigation(bias_type)
                        }
                        
                        if bias_category == "cognitive":
                            cognitive_biases.append(bias_entry)
                        elif bias_category == "selection":
                            selection_biases.append(bias_entry)
                        elif bias_category == "confirmation":
                            confirmation_biases.append(bias_entry)
        
        # Detect cultural and domain-specific biases
        cultural_biases = self._detect_cultural_biases(content, domain)
        
        return {
            "cognitive_biases": cognitive_biases[:6],
            "selection_biases": selection_biases[:4],
            "confirmation_biases": confirmation_biases[:4],
            "cultural_biases": cultural_biases[:4],
            "total_biases": len(cognitive_biases) + len(selection_biases) + len(confirmation_biases) + len(cultural_biases)
        }
    
    def _critique_evidence(self, content: List[str], domain: str, rigor_level: str) -> Dict[str, Any]:
        """Critically analyze the quality and validity of evidence"""
        
        evidence_quality_issues = []
        methodological_concerns = []
        data_limitations = []
        generalizability_issues = []
        
        # Evidence quality indicators
        quality_issues = {
            "sample_size": [r"small\s+(?:sample|study|group)", r"n\s*=\s*[1-9]\b", r"few\s+(?:participants|subjects)"],
            "methodology": [r"(?:preliminary|pilot)\s+study", r"case\s+study", r"anecdotal\s+evidence"],
            "replication": [r"single\s+study", r"not\s+(?:replicated|reproduced)", r"one-time\s+finding"],
            "controls": [r"no\s+control\s+group", r"uncontrolled\s+study", r"lack\s+of\s+controls"],
            "measurement": [r"self-reported", r"subjective\s+measure", r"proxy\s+measure"],
            "statistical": [r"p-hacking", r"multiple\s+comparisons", r"cherry-picking"]
        }
        
        for content_item in content:
            for issue_type, patterns in quality_issues.items():
                for pattern in patterns:
                    if re.search(pattern, content_item.lower()):
                        evidence_quality_issues.append({
                            "type": issue_type,
                            "description": f"Potential {issue_type.replace('_', ' ')} issue",
                            "evidence": content_item[:150] + "...",
                            "severity": self._assess_evidence_severity(issue_type, rigor_level),
                            "impact": self._assess_evidence_impact(issue_type)
                        })
        
        # Analyze methodological concerns
        methodological_concerns = self._identify_methodological_concerns(content, domain)
        
        # Identify data limitations
        data_limitations = self._identify_data_limitations(content)
        
        # Assess generalizability
        generalizability_issues = self._assess_generalizability_issues(content, domain)
        
        return {
            "evidence_quality_issues": evidence_quality_issues[:8],
            "methodological_concerns": methodological_concerns[:6],
            "data_limitations": data_limitations[:6],
            "generalizability_issues": generalizability_issues[:4],
            "total_evidence_issues": len(evidence_quality_issues) + len(methodological_concerns) + len(data_limitations)
        }
    
    def _generate_alternative_explanations(self, content: List[str], domain: str) -> Dict[str, Any]:
        """Generate alternative explanations and interpretations"""
        
        alternative_hypotheses = []
        competing_theories = []
        confounding_factors = []
        reverse_causality = []
        
        # Extract main claims and conclusions
        main_claims = self._extract_main_claims(content)
        
        # Generate alternatives for each claim
        for claim in main_claims[:5]:  # Top 5 claims
            # Alternative hypotheses
            alternatives = self._generate_claim_alternatives(claim, domain)
            alternative_hypotheses.extend(alternatives)
            
            # Identify potential confounding factors
            confounders = self._identify_confounding_factors(claim, domain)
            confounding_factors.extend(confounders)
            
            # Consider reverse causality
            reverse_causal = self._consider_reverse_causality(claim)
            if reverse_causal:
                reverse_causality.append(reverse_causal)
        
        # Generate competing theories
        competing_theories = self._generate_competing_theories(main_claims, domain)
        
        return {
            "alternative_hypotheses": alternative_hypotheses[:8],
            "competing_theories": competing_theories[:4],
            "confounding_factors": confounding_factors[:6],
            "reverse_causality": reverse_causality[:3],
            "total_alternatives": len(alternative_hypotheses) + len(competing_theories) + len(confounding_factors)
        }
    
    def _identify_reasoning_gaps(self, content: List[str], rigor_level: str) -> List[Dict[str, Any]]:
        """Identify gaps in reasoning and logic"""
        gaps = []
        
        gap_indicators = [
            ("missing_premise", [r"therefore\s+", r"thus\s+", r"hence\s+"], "Conclusion without sufficient premises"),
            ("unsupported_assumption", [r"assume\s+", r"given\s+that", r"it\s+is\s+clear"], "Unsupported assumption"),
            ("logical_leap", [r"obviously\s+", r"clearly\s+", r"it\s+follows"], "Logical leap without justification"),
            ("missing_evidence", [r"proves?\s+", r"demonstrates?\s+", r"shows?\s+"], "Claim without evidence")
        ]
        
        for content_item in content:
            for gap_type, patterns, description in gap_indicators:
                for pattern in patterns:
                    if re.search(pattern, content_item.lower()):
                        gaps.append({
                            "type": gap_type,
                            "description": description,
                            "evidence": content_item[:150] + "...",
                            "severity": "high" if rigor_level == "extreme" else "medium"
                        })
        
        return gaps
    
    def _identify_invalid_inferences(self, content: List[str], rigor_level: str) -> List[Dict[str, Any]]:
        """Identify invalid inferences and conclusions"""
        invalid_inferences = []
        
        inference_patterns = [
            ("overgeneralization", [r"all\s+.*\s+are", r"every\s+.*\s+has"], "Overgeneralization from limited data"),
            ("false_precision", [r"\d+\.\d{3,}", r"exactly\s+\d+"], "False precision in estimates"),
            ("causal_inference", [r"causes?\s+", r"leads?\s+to", r"results?\s+in"], "Causal inference from correlation"),
            ("temporal_confusion", [r"before\s+.*\s+after", r"then\s+.*\s+because"], "Temporal sequence confusion")
        ]
        
        for content_item in content:
            for inference_type, patterns, description in inference_patterns:
                for pattern in patterns:
                    if re.search(pattern, content_item.lower()):
                        invalid_inferences.append({
                            "type": inference_type,
                            "description": description,
                            "evidence": content_item[:150] + "...",
                            "severity": self._assess_inference_severity(inference_type, rigor_level)
                        })
        
        return invalid_inferences
    
    def _extract_main_claims(self, content: List[str]) -> List[str]:
        """Extract main claims and conclusions from content"""
        claims = []
        
        claim_indicators = [
            r"conclude\s+that\s+([^.]+)",
            r"find\s+that\s+([^.]+)",
            r"results?\s+show\s+([^.]+)",
            r"evidence\s+suggests?\s+([^.]+)",
            r"demonstrates?\s+that\s+([^.]+)"
        ]
        
        for content_item in content:
            for pattern in claim_indicators:
                matches = re.finditer(pattern, content_item.lower())
                for match in matches:
                    claim = match.group(1).strip()
                    if len(claim) > 10 and len(claim) < 200:
                        claims.append(claim)
        
        return claims[:10]  # Top 10 claims
    
    def _generate_claim_alternatives(self, claim: str, domain: str) -> List[Dict[str, Any]]:
        """Generate alternative explanations for a specific claim"""
        alternatives = []
        
        # Generic alternative patterns
        alternative_templates = [
            f"The observed effect could be due to measurement error rather than {claim}",
            f"Third variable explanation: an unmeasured factor could explain both the predictor and outcome",
            f"Reverse causation: the supposed effect might actually be causing the supposed cause",
            f"Sampling bias: the results might not generalize beyond the specific sample studied",
            f"Temporal confounding: changes over time might explain the observed pattern"
        ]
        
        for i, template in enumerate(alternative_templates[:3]):
            alternatives.append({
                "alternative_id": i + 1,
                "explanation": template,
                "plausibility": "moderate",
                "testability": "high" if i < 2 else "medium",
                "type": "methodological" if i < 3 else "theoretical"
            })
        
        return alternatives
    
    # Helper methods for assessment and categorization
    def _assess_fallacy_severity(self, fallacy_type: str, rigor_level: str) -> str:
        """Assess severity of logical fallacy"""
        high_severity = ["straw_man", "false_dichotomy", "circular_reasoning"]
        if fallacy_type in high_severity:
            return "high"
        return "high" if rigor_level == "extreme" else "medium"
    
    def _categorize_bias(self, bias_type: str) -> str:
        """Categorize bias type"""
        categories = {
            "confirmation_bias": "confirmation",
            "availability_heuristic": "cognitive",
            "anchoring_bias": "cognitive",
            "survivorship_bias": "selection",
            "selection_bias": "selection",
            "publication_bias": "selection"
        }
        return categories.get(bias_type, "cognitive")
    
    def _assess_bias_severity(self, bias_type: str, rigor_level: str) -> str:
        """Assess severity of bias"""
        critical_biases = ["confirmation_bias", "selection_bias", "survivorship_bias"]
        if bias_type in critical_biases:
            return "high"
        return "medium"
    
    def _suggest_bias_mitigation(self, bias_type: str) -> str:
        """Suggest mitigation strategies for bias"""
        mitigations = {
            "confirmation_bias": "Actively seek disconfirming evidence",
            "selection_bias": "Use random sampling methods",
            "availability_heuristic": "Consider base rates and systematic data",
            "anchoring_bias": "Generate multiple independent estimates",
            "survivorship_bias": "Include failed cases in analysis",
            "publication_bias": "Search for unpublished studies and null results"
        }
        return mitigations.get(bias_type, "Apply systematic bias checking procedures")
    
    def _assess_evidence_severity(self, issue_type: str, rigor_level: str) -> str:
        """Assess severity of evidence quality issue"""
        critical_issues = ["sample_size", "controls", "replication"]
        if issue_type in critical_issues:
            return "high"
        return "medium"
    
    def _assess_evidence_impact(self, issue_type: str) -> str:
        """Assess impact of evidence quality issue"""
        high_impact = ["sample_size", "methodology", "controls"]
        return "high" if issue_type in high_impact else "medium"
    
    def _assess_inference_severity(self, inference_type: str, rigor_level: str) -> str:
        """Assess severity of invalid inference"""
        critical_inferences = ["causal_inference", "overgeneralization"]
        if inference_type in critical_inferences:
            return "high"
        return "medium"
    
    # Placeholder implementations for complex analysis methods
    def _detect_cultural_biases(self, content: List[str], domain: str) -> List[Dict[str, Any]]:
        """Detect cultural and domain-specific biases"""
        return [{"type": "cultural_assumption", "description": "Potential cultural bias detected", "severity": "medium"}]
    
    def _identify_methodological_concerns(self, content: List[str], domain: str) -> List[Dict[str, Any]]:
        """Identify methodological concerns"""
        return [{"type": "methodology", "description": "Methodological concern identified", "severity": "medium"}]
    
    def _identify_data_limitations(self, content: List[str]) -> List[Dict[str, Any]]:
        """Identify data limitations"""
        return [{"type": "data_quality", "description": "Data limitation identified", "severity": "medium"}]
    
    def _assess_generalizability_issues(self, content: List[str], domain: str) -> List[Dict[str, Any]]:
        """Assess generalizability issues"""
        return [{"type": "generalizability", "description": "Generalizability concern", "severity": "medium"}]
    
    def _identify_confounding_factors(self, claim: str, domain: str) -> List[Dict[str, Any]]:
        """Identify potential confounding factors"""
        return [{"factor": "unmeasured_variable", "description": "Potential confounding factor", "likelihood": "moderate"}]
    
    def _consider_reverse_causality(self, claim: str) -> Dict[str, Any]:
        """Consider reverse causality explanations"""
        return {"explanation": "Reverse causality possible", "plausibility": "moderate"}
    
    def _generate_competing_theories(self, claims: List[str], domain: str) -> List[Dict[str, Any]]:
        """Generate competing theoretical explanations"""
        return [{"theory": "Alternative theoretical framework", "support": "moderate", "testability": "high"}]
    
    def _count_total_issues(self, analysis_results: Dict) -> int:
        """Count total issues identified across all analyses"""
        total = 0
        for category, results in analysis_results.items():
            if isinstance(results, dict):
                for key, value in results.items():
                    if isinstance(value, list):
                        total += len(value)
                    elif key.startswith("total_"):
                        total += value if isinstance(value, int) else 0
        return total
    
    def _generate_overall_assessment(self, analysis_results: Dict, rigor_level: str) -> Dict[str, Any]:
        """Generate overall assessment of content quality"""
        total_issues = self._count_total_issues(analysis_results)
        
        if total_issues == 0:
            quality = "high"
            reliability = "high"
        elif total_issues <= 5:
            quality = "moderate"
            reliability = "moderate"
        else:
            quality = "low"
            reliability = "low"
        
        return {
            "overall_quality": quality,
            "reliability_assessment": reliability,
            "total_issues_identified": total_issues,
            "confidence_level": "high" if rigor_level == "extreme" else "moderate",
            "recommendation": "proceed with caution" if total_issues > 3 else "acceptable with minor revisions"
        }
    
    def _generate_improvement_recommendations(self, analysis_results: Dict, domain: str) -> List[str]:
        """Generate recommendations for improving content quality"""
        recommendations = []
        
        # Generic recommendations based on issues found
        if "logical_flaws" in analysis_results:
            recommendations.append("Review logical structure and eliminate fallacies")
        
        if "bias_detection" in analysis_results:
            recommendations.append("Implement bias mitigation strategies")
        
        if "evidence_critique" in analysis_results:
            recommendations.append("Strengthen evidence base and methodology")
        
        if "alternative_explanations" in analysis_results:
            recommendations.append("Address alternative explanations and competing theories")
        
        # Add domain-specific recommendations
        if domain in ["science", "research"]:
            recommendations.append("Conduct replication studies to verify findings")
        
        recommendations.append("Seek independent peer review and validation")
        
        return recommendations[:5]  # Top 5 recommendations

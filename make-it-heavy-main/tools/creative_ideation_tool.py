import json
import random
import re
from typing import Dict, Any, List
from .base_tool import BaseTool

class CreativeIdeationTool(BaseTool):
    """Advanced creative ideation tool for brainstorming, innovation, and novel solution generation"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        # Creative thinking techniques
        self.ideation_techniques = [
            "brainstorming", "scamper", "six_thinking_hats", "morphological_analysis",
            "biomimicry", "analogical_thinking", "reverse_thinking", "random_stimulation"
        ]
    
    @property
    def name(self) -> str:
        return "creative_ideation"
    
    @property
    def description(self) -> str:
        return "Generate creative ideas, innovative solutions, and novel approaches using advanced ideation techniques"
    
    @property
    def parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "challenge_description": {
                    "type": "string",
                    "description": "Description of the challenge, problem, or area for ideation"
                },
                "ideation_type": {
                    "type": "string",
                    "enum": ["problem_solving", "product_innovation", "business_models", "research_directions", "creative_applications", "process_improvement"],
                    "description": "Type of ideation to perform",
                    "default": "problem_solving"
                },
                "creativity_level": {
                    "type": "string",
                    "enum": ["incremental", "radical", "breakthrough"],
                    "description": "Desired level of creativity and innovation",
                    "default": "radical"
                },
                "domain_context": {
                    "type": "string",
                    "description": "Domain or industry context for ideation",
                    "default": ""
                },
                "constraints": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Constraints or limitations to consider",
                    "default": []
                },
                "inspiration_sources": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Sources of inspiration (nature, other industries, technologies, etc.)",
                    "default": []
                },
                "target_audience": {
                    "type": "string",
                    "description": "Target audience or beneficiaries",
                    "default": ""
                },
                "ideation_techniques": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Specific ideation techniques to use",
                    "default": []
                }
            },
            "required": ["challenge_description"]
        }
    
    def execute(self, challenge_description: str, ideation_type: str = "problem_solving",
                creativity_level: str = "radical", domain_context: str = "",
                constraints: List[str] = None, inspiration_sources: List[str] = None,
                target_audience: str = "", ideation_techniques: List[str] = None) -> Dict[str, Any]:
        """Execute creative ideation process"""
        try:
            if not challenge_description:
                return {
                    "error": "Challenge description is required for ideation",
                    "status": "error"
                }
            
            constraints = constraints or []
            inspiration_sources = inspiration_sources or []
            ideation_techniques = ideation_techniques or ["brainstorming", "scamper", "analogical_thinking"]
            
            # Analyze the challenge
            challenge_analysis = self._analyze_challenge(challenge_description, domain_context, constraints)
            
            # Generate ideas using multiple techniques
            generated_ideas = []
            for technique in ideation_techniques:
                ideas = self._apply_ideation_technique(
                    technique, challenge_description, challenge_analysis, 
                    creativity_level, inspiration_sources
                )
                generated_ideas.extend(ideas)
            
            # Apply creativity enhancement
            enhanced_ideas = self._enhance_creativity(generated_ideas, creativity_level, inspiration_sources)
            
            # Evaluate and rank ideas
            evaluated_ideas = self._evaluate_ideas(enhanced_ideas, challenge_analysis, constraints, domain_context)
            
            # Generate implementation concepts
            implementation_concepts = self._generate_implementation_concepts(evaluated_ideas[:5], domain_context)
            
            # Create innovation roadmap
            innovation_roadmap = self._create_innovation_roadmap(evaluated_ideas[:3], domain_context)
            
            return {
                "status": "success",
                "challenge_description": challenge_description,
                "ideation_type": ideation_type,
                "creativity_level": creativity_level,
                "domain_context": domain_context,
                "challenge_analysis": challenge_analysis,
                "generated_ideas": evaluated_ideas,
                "implementation_concepts": implementation_concepts,
                "innovation_roadmap": innovation_roadmap,
                "techniques_used": ideation_techniques,
                "summary": f"Generated {len(evaluated_ideas)} creative ideas using {len(ideation_techniques)} techniques"
            }
            
        except Exception as e:
            return {
                "error": f"Creative ideation failed: {str(e)}",
                "status": "error"
            }
    
    def _analyze_challenge(self, challenge: str, domain: str, constraints: List[str]) -> Dict[str, Any]:
        """Analyze the challenge to understand key aspects"""
        
        # Extract key elements
        key_elements = self._extract_key_elements(challenge)
        
        # Identify stakeholders
        stakeholders = self._identify_stakeholders(challenge, domain)
        
        # Analyze problem dimensions
        problem_dimensions = self._analyze_problem_dimensions(challenge)
        
        # Identify success criteria
        success_criteria = self._identify_success_criteria(challenge, domain)
        
        return {
            "key_elements": key_elements,
            "stakeholders": stakeholders,
            "problem_dimensions": problem_dimensions,
            "success_criteria": success_criteria,
            "constraints": constraints,
            "complexity_level": self._assess_complexity(challenge, constraints)
        }
    
    def _extract_key_elements(self, challenge: str) -> List[str]:
        """Extract key elements from challenge description"""
        # Simple keyword extraction
        keywords = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', challenge)
        keywords.extend(re.findall(r'\b(?:improve|enhance|solve|create|develop|design|optimize|reduce|increase)\b', challenge.lower()))
        
        # Remove duplicates and return top elements
        unique_keywords = list(set(keywords))
        return unique_keywords[:10]
    
    def _identify_stakeholders(self, challenge: str, domain: str) -> List[str]:
        """Identify potential stakeholders"""
        stakeholder_patterns = [
            r'\b(?:users?|customers?|clients?|patients?|students?|employees?|workers?)\b',
            r'\b(?:managers?|executives?|leaders?|administrators?)\b',
            r'\b(?:researchers?|scientists?|engineers?|developers?)\b',
            r'\b(?:society|community|public|environment)\b'
        ]
        
        stakeholders = []
        for pattern in stakeholder_patterns:
            matches = re.findall(pattern, challenge.lower())
            stakeholders.extend(matches)
        
        # Add domain-specific stakeholders
        domain_stakeholders = {
            "healthcare": ["patients", "doctors", "nurses", "hospitals"],
            "education": ["students", "teachers", "parents", "schools"],
            "business": ["customers", "employees", "shareholders", "partners"],
            "technology": ["users", "developers", "companies", "society"]
        }
        
        if domain.lower() in domain_stakeholders:
            stakeholders.extend(domain_stakeholders[domain.lower()])
        
        return list(set(stakeholders))[:8]
    
    def _analyze_problem_dimensions(self, challenge: str) -> Dict[str, str]:
        """Analyze different dimensions of the problem"""
        dimensions = {
            "technical": "medium",
            "social": "medium", 
            "economic": "medium",
            "environmental": "low",
            "ethical": "low",
            "temporal": "medium"
        }
        
        # Simple pattern matching to assess dimensions
        if any(word in challenge.lower() for word in ["technology", "system", "algorithm", "software", "hardware"]):
            dimensions["technical"] = "high"
        
        if any(word in challenge.lower() for word in ["people", "social", "community", "culture", "behavior"]):
            dimensions["social"] = "high"
        
        if any(word in challenge.lower() for word in ["cost", "money", "budget", "economic", "financial"]):
            dimensions["economic"] = "high"
        
        if any(word in challenge.lower() for word in ["environment", "sustainable", "green", "climate"]):
            dimensions["environmental"] = "high"
        
        return dimensions
    
    def _identify_success_criteria(self, challenge: str, domain: str) -> List[str]:
        """Identify potential success criteria"""
        criteria = []
        
        # Extract explicit criteria from challenge
        criteria_patterns = [
            r'(?:improve|increase|enhance|boost)\s+([^.]+)',
            r'(?:reduce|decrease|minimize|eliminate)\s+([^.]+)',
            r'(?:achieve|reach|attain)\s+([^.]+)'
        ]
        
        for pattern in criteria_patterns:
            matches = re.findall(pattern, challenge.lower())
            criteria.extend([match.strip() for match in matches])
        
        # Add generic success criteria
        generic_criteria = [
            "user satisfaction",
            "efficiency improvement",
            "cost reduction",
            "quality enhancement",
            "scalability",
            "sustainability"
        ]
        
        criteria.extend(generic_criteria[:3])
        return criteria[:6]
    
    def _assess_complexity(self, challenge: str, constraints: List[str]) -> str:
        """Assess complexity level of the challenge"""
        complexity_indicators = len(constraints) + len(challenge.split())
        
        if complexity_indicators > 100:
            return "high"
        elif complexity_indicators > 50:
            return "medium"
        else:
            return "low"
    
    def _apply_ideation_technique(self, technique: str, challenge: str, analysis: Dict,
                                creativity_level: str, inspiration_sources: List[str]) -> List[Dict[str, Any]]:
        """Apply specific ideation technique"""
        
        if technique == "brainstorming":
            return self._brainstorming(challenge, analysis, creativity_level)
        elif technique == "scamper":
            return self._scamper_technique(challenge, analysis)
        elif technique == "six_thinking_hats":
            return self._six_thinking_hats(challenge, analysis)
        elif technique == "morphological_analysis":
            return self._morphological_analysis(challenge, analysis)
        elif technique == "biomimicry":
            return self._biomimicry(challenge, inspiration_sources)
        elif technique == "analogical_thinking":
            return self._analogical_thinking(challenge, inspiration_sources)
        elif technique == "reverse_thinking":
            return self._reverse_thinking(challenge, analysis)
        elif technique == "random_stimulation":
            return self._random_stimulation(challenge, analysis)
        else:
            return self._brainstorming(challenge, analysis, creativity_level)
    
    def _brainstorming(self, challenge: str, analysis: Dict, creativity_level: str) -> List[Dict[str, Any]]:
        """Generate ideas using brainstorming technique"""
        ideas = []
        
        # Generate ideas based on key elements
        key_elements = analysis.get("key_elements", [])
        
        for element in key_elements[:5]:
            # Direct solutions
            ideas.append({
                "idea": f"Develop an innovative {element.lower()} solution using advanced technology",
                "technique": "brainstorming",
                "category": "direct_solution",
                "creativity_score": 0.6,
                "feasibility": "high"
            })
            
            # Combination ideas
            if len(key_elements) > 1:
                other_element = random.choice([e for e in key_elements if e != element])
                ideas.append({
                    "idea": f"Combine {element.lower()} with {other_element.lower()} for synergistic benefits",
                    "technique": "brainstorming",
                    "category": "combination",
                    "creativity_score": 0.7,
                    "feasibility": "medium"
                })
        
        # Add wild ideas for high creativity
        if creativity_level in ["radical", "breakthrough"]:
            wild_ideas = [
                "Completely reimagine the fundamental approach using quantum principles",
                "Create a self-evolving system that adapts and improves autonomously",
                "Develop a crowd-sourced, AI-powered collaborative solution",
                "Design a gamified experience that makes the challenge enjoyable"
            ]
            
            for wild_idea in wild_ideas[:2]:
                ideas.append({
                    "idea": wild_idea,
                    "technique": "brainstorming",
                    "category": "wild_idea",
                    "creativity_score": 0.9,
                    "feasibility": "low"
                })
        
        return ideas
    
    def _scamper_technique(self, challenge: str, analysis: Dict) -> List[Dict[str, Any]]:
        """Apply SCAMPER technique (Substitute, Combine, Adapt, Modify, Put to other uses, Eliminate, Reverse)"""
        ideas = []
        
        scamper_prompts = {
            "substitute": "What can be substituted or replaced?",
            "combine": "What can be combined or merged?",
            "adapt": "What can be adapted from elsewhere?",
            "modify": "What can be modified or emphasized?",
            "put_to_other_uses": "How can this be used differently?",
            "eliminate": "What can be removed or simplified?",
            "reverse": "What can be reversed or rearranged?"
        }
        
        for action, prompt in scamper_prompts.items():
            ideas.append({
                "idea": f"Apply {action} principle: {prompt} in the context of {challenge[:50]}...",
                "technique": "scamper",
                "category": action,
                "creativity_score": 0.7,
                "feasibility": "medium"
            })
        
        return ideas
    
    def _six_thinking_hats(self, challenge: str, analysis: Dict) -> List[Dict[str, Any]]:
        """Apply Six Thinking Hats technique"""
        ideas = []
        
        thinking_hats = {
            "white": "Focus on facts and information",
            "red": "Consider emotions and feelings",
            "black": "Think about cautions and critical assessment",
            "yellow": "Explore benefits and optimism",
            "green": "Generate creative alternatives",
            "blue": "Manage the thinking process"
        }
        
        for hat, description in thinking_hats.items():
            ideas.append({
                "idea": f"{hat.title()} hat perspective: {description} - leads to innovative approach",
                "technique": "six_thinking_hats",
                "category": f"{hat}_hat",
                "creativity_score": 0.6,
                "feasibility": "medium"
            })
        
        return ideas
    
    def _morphological_analysis(self, challenge: str, analysis: Dict) -> List[Dict[str, Any]]:
        """Apply morphological analysis technique"""
        ideas = []
        
        # Define dimensions and options
        dimensions = {
            "approach": ["technological", "social", "economic", "hybrid"],
            "scale": ["individual", "group", "organizational", "societal"],
            "timeline": ["immediate", "short-term", "long-term", "continuous"],
            "resources": ["minimal", "moderate", "substantial", "unlimited"]
        }
        
        # Generate combinations
        for i in range(3):  # Generate 3 combinations
            combination = {}
            for dim, options in dimensions.items():
                combination[dim] = random.choice(options)
            
            idea_description = f"Develop a {combination['approach']} solution at {combination['scale']} scale, implemented over {combination['timeline']} with {combination['resources']} resources"
            
            ideas.append({
                "idea": idea_description,
                "technique": "morphological_analysis",
                "category": "systematic_combination",
                "creativity_score": 0.8,
                "feasibility": "medium",
                "dimensions": combination
            })
        
        return ideas
    
    def _biomimicry(self, challenge: str, inspiration_sources: List[str]) -> List[Dict[str, Any]]:
        """Apply biomimicry technique"""
        ideas = []
        
        natural_inspirations = [
            ("ant colonies", "swarm intelligence and collective problem-solving"),
            ("gecko feet", "advanced adhesion without chemicals"),
            ("shark skin", "drag reduction and efficiency"),
            ("bird flocking", "distributed coordination and navigation"),
            ("tree networks", "resource sharing and communication"),
            ("immune system", "adaptive defense and learning")
        ]
        
        # Add user-provided inspiration sources
        if inspiration_sources:
            for source in inspiration_sources[:3]:
                natural_inspirations.append((source, "natural optimization principles"))
        
        for organism, principle in natural_inspirations[:4]:
            ideas.append({
                "idea": f"Mimic {organism} {principle} to solve the challenge through bio-inspired design",
                "technique": "biomimicry",
                "category": "nature_inspired",
                "creativity_score": 0.8,
                "feasibility": "medium",
                "inspiration": organism
            })
        
        return ideas
    
    def _analogical_thinking(self, challenge: str, inspiration_sources: List[str]) -> List[Dict[str, Any]]:
        """Apply analogical thinking technique"""
        ideas = []
        
        analogy_domains = [
            ("cooking", "recipe optimization and ingredient combination"),
            ("music", "harmony, rhythm, and composition principles"),
            ("sports", "team coordination and performance optimization"),
            ("architecture", "structural design and space utilization"),
            ("transportation", "flow optimization and route planning"),
            ("games", "rule systems and strategic thinking")
        ]
        
        # Add user-provided inspiration sources
        if inspiration_sources:
            for source in inspiration_sources[:2]:
                analogy_domains.append((source, "domain-specific optimization"))
        
        for domain, principle in analogy_domains[:4]:
            ideas.append({
                "idea": f"Apply {domain} principles of {principle} to create an analogous solution",
                "technique": "analogical_thinking",
                "category": "cross_domain_analogy",
                "creativity_score": 0.7,
                "feasibility": "medium",
                "analogy_domain": domain
            })
        
        return ideas
    
    def _reverse_thinking(self, challenge: str, analysis: Dict) -> List[Dict[str, Any]]:
        """Apply reverse thinking technique"""
        ideas = []
        
        # Reverse the problem
        ideas.append({
            "idea": "Instead of solving the problem, eliminate the conditions that create it",
            "technique": "reverse_thinking",
            "category": "problem_elimination",
            "creativity_score": 0.8,
            "feasibility": "medium"
        })
        
        # Reverse the approach
        ideas.append({
            "idea": "Start with the desired outcome and work backwards to identify necessary steps",
            "technique": "reverse_thinking",
            "category": "backward_design",
            "creativity_score": 0.7,
            "feasibility": "high"
        })
        
        # Reverse assumptions
        ideas.append({
            "idea": "Challenge fundamental assumptions and do the opposite of conventional wisdom",
            "technique": "reverse_thinking",
            "category": "assumption_reversal",
            "creativity_score": 0.9,
            "feasibility": "low"
        })
        
        return ideas
    
    def _random_stimulation(self, challenge: str, analysis: Dict) -> List[Dict[str, Any]]:
        """Apply random stimulation technique"""
        ideas = []
        
        random_words = [
            "butterfly", "quantum", "mirror", "spiral", "crystal", "wave",
            "network", "dance", "puzzle", "bridge", "garden", "symphony"
        ]
        
        selected_words = random.sample(random_words, 3)
        
        for word in selected_words:
            ideas.append({
                "idea": f"Use '{word}' as inspiration: What if the solution had the properties of a {word}?",
                "technique": "random_stimulation",
                "category": "random_association",
                "creativity_score": 0.6,
                "feasibility": "variable",
                "stimulus_word": word
            })
        
        return ideas
    
    def _enhance_creativity(self, ideas: List[Dict], creativity_level: str, inspiration_sources: List[str]) -> List[Dict[str, Any]]:
        """Enhance creativity of generated ideas"""
        enhanced_ideas = []
        
        for idea in ideas:
            enhanced_idea = idea.copy()
            
            # Boost creativity based on level
            if creativity_level == "breakthrough":
                enhanced_idea["creativity_score"] = min(enhanced_idea.get("creativity_score", 0.5) + 0.2, 1.0)
                enhanced_idea["idea"] = f"Revolutionary approach: {enhanced_idea['idea']}"
            elif creativity_level == "radical":
                enhanced_idea["creativity_score"] = min(enhanced_idea.get("creativity_score", 0.5) + 0.1, 1.0)
                enhanced_idea["idea"] = f"Innovative solution: {enhanced_idea['idea']}"
            
            # Add cross-pollination from inspiration sources
            if inspiration_sources and random.random() < 0.3:  # 30% chance
                source = random.choice(inspiration_sources)
                enhanced_idea["idea"] += f" (inspired by {source})"
                enhanced_idea["creativity_score"] = min(enhanced_idea.get("creativity_score", 0.5) + 0.1, 1.0)
            
            enhanced_ideas.append(enhanced_idea)
        
        return enhanced_ideas
    
    def _evaluate_ideas(self, ideas: List[Dict], analysis: Dict, constraints: List[str], domain: str) -> List[Dict[str, Any]]:
        """Evaluate and rank ideas"""
        
        for idea in ideas:
            # Calculate overall score
            creativity = idea.get("creativity_score", 0.5)
            feasibility = self._assess_feasibility(idea, constraints, domain)
            impact = self._assess_impact(idea, analysis)
            novelty = self._assess_novelty(idea)
            
            # Weighted scoring
            overall_score = (creativity * 0.3 + feasibility * 0.3 + impact * 0.25 + novelty * 0.15)
            
            idea.update({
                "feasibility_score": feasibility,
                "impact_score": impact,
                "novelty_score": novelty,
                "overall_score": overall_score,
                "ranking_factors": {
                    "creativity": creativity,
                    "feasibility": feasibility,
                    "impact": impact,
                    "novelty": novelty
                }
            })
        
        # Sort by overall score
        ideas.sort(key=lambda x: x["overall_score"], reverse=True)
        
        return ideas
    
    def _assess_feasibility(self, idea: Dict, constraints: List[str], domain: str) -> float:
        """Assess feasibility of an idea"""
        base_feasibility = {"high": 0.8, "medium": 0.6, "low": 0.3, "variable": 0.5}.get(
            idea.get("feasibility", "medium"), 0.6
        )
        
        # Adjust for constraints
        constraint_penalty = len(constraints) * 0.05
        
        # Domain-specific adjustments
        domain_bonus = 0.1 if domain in ["technology", "software"] else 0.0
        
        return max(0.1, min(1.0, base_feasibility - constraint_penalty + domain_bonus))
    
    def _assess_impact(self, idea: Dict, analysis: Dict) -> float:
        """Assess potential impact of an idea"""
        # Base impact based on stakeholders affected
        stakeholder_count = len(analysis.get("stakeholders", []))
        base_impact = min(0.9, 0.3 + stakeholder_count * 0.1)
        
        # Boost for certain categories
        category_boost = {
            "wild_idea": 0.2,
            "breakthrough": 0.3,
            "systematic_combination": 0.1
        }.get(idea.get("category", ""), 0.0)
        
        return min(1.0, base_impact + category_boost)
    
    def _assess_novelty(self, idea: Dict) -> float:
        """Assess novelty of an idea"""
        technique_novelty = {
            "brainstorming": 0.5,
            "scamper": 0.6,
            "biomimicry": 0.8,
            "morphological_analysis": 0.7,
            "reverse_thinking": 0.8,
            "random_stimulation": 0.6
        }.get(idea.get("technique", ""), 0.5)
        
        category_novelty = {
            "wild_idea": 0.9,
            "nature_inspired": 0.8,
            "cross_domain_analogy": 0.7,
            "assumption_reversal": 0.9
        }.get(idea.get("category", ""), 0.5)
        
        return (technique_novelty + category_novelty) / 2
    
    def _generate_implementation_concepts(self, top_ideas: List[Dict], domain: str) -> List[Dict[str, Any]]:
        """Generate implementation concepts for top ideas"""
        implementations = []
        
        for i, idea in enumerate(top_ideas):
            implementation = {
                "idea_id": i,
                "idea_summary": idea["idea"][:100] + "...",
                "implementation_approach": self._suggest_implementation_approach(idea, domain),
                "required_resources": self._estimate_resources(idea, domain),
                "timeline": self._estimate_timeline(idea, domain),
                "key_milestones": self._define_milestones(idea, domain),
                "risk_factors": self._identify_risks(idea, domain),
                "success_metrics": self._define_success_metrics(idea, domain)
            }
            implementations.append(implementation)
        
        return implementations
    
    def _create_innovation_roadmap(self, top_ideas: List[Dict], domain: str) -> Dict[str, Any]:
        """Create innovation roadmap for top ideas"""
        return {
            "phase_1": {
                "duration": "3-6 months",
                "focus": "Concept development and validation",
                "activities": ["Prototype development", "Market research", "Feasibility studies"],
                "deliverables": ["Proof of concept", "Business case", "Technical specifications"]
            },
            "phase_2": {
                "duration": "6-12 months", 
                "focus": "Development and testing",
                "activities": ["Full development", "User testing", "Iteration"],
                "deliverables": ["Beta version", "User feedback", "Refined solution"]
            },
            "phase_3": {
                "duration": "3-6 months",
                "focus": "Launch and scaling",
                "activities": ["Market launch", "Scaling operations", "Performance monitoring"],
                "deliverables": ["Market-ready solution", "Scaling plan", "Performance metrics"]
            },
            "total_timeline": "12-24 months",
            "key_decision_points": ["Go/no-go after Phase 1", "Pivot decisions in Phase 2", "Scaling decisions in Phase 3"]
        }
    
    # Helper methods for implementation planning
    def _suggest_implementation_approach(self, idea: Dict, domain: str) -> str:
        """Suggest implementation approach"""
        approaches = {
            "technology": "Agile development with iterative prototyping",
            "business": "Lean startup methodology with MVP development",
            "research": "Systematic research with pilot studies",
            "social": "Community-based participatory approach"
        }
        return approaches.get(domain.lower(), "Phased implementation with stakeholder engagement")
    
    def _estimate_resources(self, idea: Dict, domain: str) -> Dict[str, str]:
        """Estimate required resources"""
        return {
            "human_resources": "3-5 team members",
            "financial_resources": "Moderate investment required",
            "technical_resources": "Standard development tools",
            "time_resources": "6-12 months"
        }
    
    def _estimate_timeline(self, idea: Dict, domain: str) -> str:
        """Estimate implementation timeline"""
        feasibility = idea.get("feasibility_score", 0.5)
        if feasibility > 0.7:
            return "6-9 months"
        elif feasibility > 0.4:
            return "9-15 months"
        else:
            return "15-24 months"
    
    def _define_milestones(self, idea: Dict, domain: str) -> List[str]:
        """Define key milestones"""
        return [
            "Concept validation completed",
            "Prototype developed",
            "User testing completed",
            "Final solution delivered"
        ]
    
    def _identify_risks(self, idea: Dict, domain: str) -> List[str]:
        """Identify implementation risks"""
        return [
            "Technical feasibility challenges",
            "Resource availability constraints",
            "Market acceptance uncertainty",
            "Competitive response"
        ]
    
    def _define_success_metrics(self, idea: Dict, domain: str) -> List[str]:
        """Define success metrics"""
        return [
            "User adoption rate",
            "Performance improvement metrics",
            "Cost-benefit ratio",
            "Stakeholder satisfaction"
        ]

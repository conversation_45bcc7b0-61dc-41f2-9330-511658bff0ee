#!/usr/bin/env python3
"""
Fix Embedding Dimensions
Reprocesses embeddings to ensure dimension consistency
"""

import os
from tools.knowledge_base_tool import KnowledgeBaseTool

def fix_embedding_dimensions():
    """Fix embedding dimension mismatch by reprocessing"""
    print("🔧 FIXING EMBEDDING DIMENSION MISMATCH")
    print("=" * 60)
    
    try:
        # Initialize knowledge base tool
        config = {}
        kb_tool = KnowledgeBaseTool(config)
        
        print(f"📊 Current Configuration:")
        print(f"   🧠 Embedding model: {type(kb_tool.embedding_model).__name__ if hasattr(kb_tool.embedding_model, '__class__') else str(kb_tool.embedding_model)}")
        print(f"   📐 Expected dimension: {kb_tool.embedding_dim}")
        
        # Check current status
        status_result = kb_tool.execute(action="status")
        if status_result.get('status') == 'success':
            current_chunks = status_result.get('total_chunks', 0)
            print(f"   🧩 Current chunks: {current_chunks:,}")
        
        # Remove old files to force reprocessing
        kb_path = kb_tool.kb_path
        files_to_remove = [
            'enhanced_embeddings.pkl',
            'faiss_index.bin'
        ]
        
        print(f"\n🗑️ Removing old embedding files...")
        for filename in files_to_remove:
            file_path = os.path.join(kb_path, filename)
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"   ✅ Removed {filename}")
            else:
                print(f"   ⚠️ {filename} not found")
        
        # Reprocess files with correct dimensions
        print(f"\n🔄 Reprocessing files with correct dimensions...")
        process_result = kb_tool.execute(action="process_files")
        
        if process_result.get('status') == 'success':
            new_chunks = process_result.get('processed_chunks', 0)
            print(f"   ✅ Reprocessing complete!")
            print(f"   📊 New chunks: {new_chunks:,}")
            
            # Test the fixed system
            print(f"\n🧪 Testing fixed system...")
            test_result = kb_tool.execute(
                action="semantic_search",
                query="machine learning",
                max_results=3
            )
            
            if test_result.get('status') == 'success':
                results = test_result.get('results', [])
                method = test_result.get('search_method', 'unknown')
                print(f"   ✅ Search working: {len(results)} results using {method}")
                return True
            else:
                print(f"   ❌ Search test failed: {test_result.get('error', 'unknown')}")
                return False
        else:
            print(f"   ❌ Reprocessing failed: {process_result.get('error', 'unknown')}")
            return False
            
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        return False

def main():
    """Main fix function"""
    print("🎯 EMBEDDING DIMENSION FIX")
    print("=" * 50)
    
    success = fix_embedding_dimensions()
    
    if success:
        print(f"\n🎉 EMBEDDING DIMENSIONS FIXED!")
        print(f"   ✅ All embeddings now use consistent dimensions")
        print(f"   ✅ FAISS index rebuilt with correct dimensions")
        print(f"   ✅ Semantic search working properly")
        print(f"   ✅ Ready for integration testing")
    else:
        print(f"\n❌ Fix failed. Please check the errors above.")
    
    return success

if __name__ == '__main__':
    main()

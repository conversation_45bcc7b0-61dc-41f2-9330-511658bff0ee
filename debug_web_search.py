#!/usr/bin/env python3
"""
Debug Web Search Tool
Identifies the exact source of the 'list' object has no attribute 'get' error
"""

import traceback
from tools.search_tool import SearchTool

def debug_web_search():
    """Debug the web search tool to find the exact error location"""
    print("🔍 DEBUGGING WEB SEARCH TOOL")
    print("=" * 50)
    
    # Create search tool with proper config
    config = {
        'search': {
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
    }
    
    search_tool = SearchTool(config)
    
    print(f"Config type: {type(search_tool.config)}")
    print(f"Config content: {search_tool.config}")
    
    try:
        print("\n🌐 Testing web search...")
        results = search_tool.execute("python programming", max_results=2)
        
        print(f"Results type: {type(results)}")
        print(f"Results length: {len(results) if hasattr(results, '__len__') else 'No length'}")
        
        if results:
            print(f"First result type: {type(results[0])}")
            if isinstance(results[0], dict):
                print(f"First result keys: {list(results[0].keys())}")
            else:
                print(f"First result content: {results[0]}")
        
        print("✅ Web search completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Web search failed: {e}")
        print("\n📋 Full traceback:")
        traceback.print_exc()
        
        # Check if it's the specific error we're looking for
        error_str = str(e)
        if "'list' object has no attribute 'get'" in error_str:
            print("\n🎯 Found the 'list' object error!")
            print("This error occurs when trying to call .get() on a list instead of a dict")
        
        return False

def debug_ddgs_directly():
    """Test DDGS library directly to see what it returns"""
    print("\n🦆 DEBUGGING DDGS LIBRARY DIRECTLY")
    print("=" * 50)
    
    try:
        from ddgs import DDGS
        
        ddgs = DDGS()
        raw_results = ddgs.text("python programming", max_results=2)
        
        print(f"Raw results type: {type(raw_results)}")
        
        # Convert to list
        results_list = list(raw_results)
        print(f"Results list type: {type(results_list)}")
        print(f"Results list length: {len(results_list)}")
        
        if results_list:
            first_result = results_list[0]
            print(f"First result type: {type(first_result)}")
            
            if isinstance(first_result, dict):
                print(f"First result keys: {list(first_result.keys())}")
                print(f"Sample result: {first_result}")
            else:
                print(f"First result (not dict): {first_result}")
        
        print("✅ DDGS direct test completed")
        return True
        
    except Exception as e:
        print(f"❌ DDGS direct test failed: {e}")
        traceback.print_exc()
        return False

def test_with_minimal_search():
    """Test with a minimal search implementation"""
    print("\n🧪 TESTING MINIMAL SEARCH IMPLEMENTATION")
    print("=" * 50)
    
    try:
        from ddgs import DDGS
        
        ddgs = DDGS()
        results = list(ddgs.text("test query", max_results=1))
        
        print(f"Got {len(results)} results")
        
        for i, result in enumerate(results):
            print(f"Result {i+1}:")
            print(f"  Type: {type(result)}")
            
            if isinstance(result, dict):
                print(f"  Keys: {list(result.keys())}")
                
                # Test each key access safely
                for key in ['href', 'url', 'title', 'body', 'snippet']:
                    try:
                        value = result.get(key, 'NOT_FOUND')
                        print(f"  {key}: {str(value)[:50]}...")
                    except Exception as e:
                        print(f"  {key}: ERROR - {e}")
            else:
                print(f"  Content: {str(result)[:100]}...")
        
        print("✅ Minimal search test completed")
        return True
        
    except Exception as e:
        print(f"❌ Minimal search test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all debug tests"""
    print("🐛 WEB SEARCH DEBUG SUITE")
    print("=" * 60)
    
    tests = [
        ("DDGS Library Direct Test", debug_ddgs_directly),
        ("Minimal Search Implementation", test_with_minimal_search),
        ("Full Web Search Tool Test", debug_web_search)
    ]
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            success = test_func()
            print(f"{'✅' if success else '❌'} {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            print(f"❌ {test_name}: CRASHED - {e}")
            traceback.print_exc()
    
    print(f"\n🎯 DEBUG COMPLETE")
    print("If the error persists, it's likely in the DDGS library response format")
    print("or in how we're processing the results.")

if __name__ == '__main__':
    main()

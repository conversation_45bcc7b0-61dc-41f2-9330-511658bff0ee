// Research Heavy Frontend Application
let socket;
let currentSessionId = null;
let researchInProgress = false;

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeSocketIO();
    loadAvailableModels();
    loadKnowledgeBase();
    setupEventListeners();
});

// Socket.IO initialization
function initializeSocketIO() {
    socket = io();
    
    socket.on('connect', function() {
        console.log('Connected to Research Heavy server');
        updateConnectionStatus(true);
    });
    
    socket.on('disconnect', function() {
        console.log('Disconnected from server');
        updateConnectionStatus(false);
    });
    
    socket.on('research_started', function(data) {
        console.log('Research started:', data);
        showProgressPanel();
        updateProgress(0, 'Research started...');
    });
    
    socket.on('agent_progress_update', function(data) {
        updateAgentStatus(data.progress);
    });
    
    socket.on('research_completed', function(data) {
        console.log('Research completed:', data);
        researchInProgress = false;
        displayResults(data.results);
        hideProgressPanel();
        enableResearchForm();
    });
    
    socket.on('research_error', function(data) {
        console.error('Research error:', data);
        researchInProgress = false;
        showError('Research failed: ' + data.error);
        hideProgressPanel();
        enableResearchForm();
    });
}

// Event listeners setup
function setupEventListeners() {
    // Research form submission
    document.getElementById('researchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        startResearch();
    });
    
    // Preset buttons
    document.querySelectorAll('[onclick^="setPreset"]').forEach(btn => {
        btn.addEventListener('click', function() {
            const preset = this.getAttribute('onclick').match(/'(\w+)'/)[1];
            setPreset(preset);
        });
    });
}

// Load available models
async function loadAvailableModels() {
    try {
        const response = await fetch('/api/models');
        const data = await response.json();
        
        if (data.status === 'success') {
            updateModelProviderOptions(data.available_providers);
            updateModelStatus(data.models);
        }
    } catch (error) {
        console.error('Failed to load models:', error);
    }
}

// Update model provider dropdown
function updateModelProviderOptions(availableProviders) {
    const select = document.getElementById('modelProvider');
    const currentValue = select.value;
    
    // Clear existing options except "Auto"
    while (select.children.length > 1) {
        select.removeChild(select.lastChild);
    }
    
    // Add available providers
    availableProviders.forEach(provider => {
        const option = document.createElement('option');
        option.value = provider;
        option.textContent = provider.charAt(0).toUpperCase() + provider.slice(1);
        
        // Add status indicator
        option.textContent += ' ✓';
        
        select.appendChild(option);
    });
    
    // Restore previous selection if still available
    if (availableProviders.includes(currentValue)) {
        select.value = currentValue;
    }
}

// Update model status display
function updateModelStatus(models) {
    // This could be used to show model status in a dedicated panel
    console.log('Model status:', models);
}

// Start research
async function startResearch() {
    console.log('🚀 Starting research...');

    if (researchInProgress) {
        console.log('⚠️ Research already in progress');
        return;
    }

    const query = document.getElementById('queryInput').value.trim();
    if (!query) {
        showError('Please enter a research query');
        return;
    }

    console.log('📝 Query:', query);
    
    const contextMode = document.querySelector('input[name="contextMode"]:checked');
    if (!contextMode) {
        showError('Please select a context mode');
        return;
    }

    const researchData = {
        query: query,
        research_type: document.getElementById('researchType').value,
        model_provider: document.getElementById('modelProvider').value || null,
        context_aware: contextMode.value !== 'off',
        context_mode: contextMode.value === 'off' ? 'standard' : contextMode.value,
        num_agents: parseInt(document.getElementById('numAgents').value)
    };
    
    console.log('📊 Research data:', researchData);

    try {
        researchInProgress = true;
        disableResearchForm();
        showProgressPanel();

        console.log('📡 Sending request to /api/research...');

        const response = await fetch('/api/research', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(researchData)
        });

        console.log('📡 Response status:', response.status);
        
        const data = await response.json();
        console.log('📊 Response data:', data);

        if (data.status === 'success') {
            currentSessionId = data.session_id;
            console.log('✅ Research session started:', currentSessionId);
            updateProgress(10, 'Research session started...');
            startProgressPolling();
        } else {
            console.error('❌ Research failed:', data.message);
            throw new Error(data.message);
        }
        
    } catch (error) {
        researchInProgress = false;
        enableResearchForm();
        hideProgressPanel();
        showError('Failed to start research: ' + error.message);
    }
}

// Progress polling
function startProgressPolling() {
    if (!currentSessionId) return;
    
    const pollInterval = setInterval(async () => {
        if (!researchInProgress) {
            clearInterval(pollInterval);
            return;
        }
        
        try {
            const response = await fetch(`/api/research/status/${currentSessionId}`);
            const data = await response.json();
            
            if (data.status === 'completed') {
                clearInterval(pollInterval);
                researchInProgress = false;
                displayResults(data.results);
                hideProgressPanel();
                enableResearchForm();
            } else if (data.status === 'error') {
                clearInterval(pollInterval);
                researchInProgress = false;
                showError('Research failed: ' + data.error);
                hideProgressPanel();
                enableResearchForm();
            } else {
                // Update progress
                const progressPercent = Math.min(90, (data.execution_time / 300) * 100); // Estimate based on time
                updateProgress(progressPercent, `Running for ${Math.round(data.execution_time)}s...`);
            }
            
        } catch (error) {
            console.error('Failed to poll status:', error);
        }
    }, 2000); // Poll every 2 seconds
}

// Update agent status
function updateAgentStatus(agentProgress) {
    const statusContainer = document.getElementById('agentStatus');
    
    if (!agentProgress || Object.keys(agentProgress).length === 0) {
        statusContainer.innerHTML = '<p class="text-muted">No active agents</p>';
        return;
    }
    
    let statusHTML = '';
    Object.entries(agentProgress).forEach(([agentId, status]) => {
        const statusClass = getStatusClass(status);
        const statusIcon = getStatusIcon(status);
        
        statusHTML += `
            <div class="agent-status ${statusClass}">
                <span class="status-indicator ${statusClass}"></span>
                ${statusIcon} Agent ${parseInt(agentId) + 1}: ${status}
            </div>
        `;
    });
    
    statusContainer.innerHTML = statusHTML;
}

// Get status class for styling
function getStatusClass(status) {
    if (status.includes('COMPLETED')) return 'completed';
    if (status.includes('PROCESSING')) return 'processing';
    if (status.includes('ERROR') || status.includes('error')) return 'error';
    return 'queued';
}

// Get status icon
function getStatusIcon(status) {
    if (status.includes('COMPLETED')) return '<i class="fas fa-check-circle text-success"></i>';
    if (status.includes('PROCESSING')) return '<i class="fas fa-spinner fa-spin text-warning"></i>';
    if (status.includes('ERROR') || status.includes('error')) return '<i class="fas fa-exclamation-circle text-danger"></i>';
    return '<i class="fas fa-clock text-muted"></i>';
}

// Display research results
function displayResults(results) {
    const resultsContent = document.getElementById('resultsContent');
    const exportButtons = document.getElementById('exportButtons');
    
    if (!results || results.status !== 'success') {
        resultsContent.innerHTML = `
            <div class="alert alert-danger">
                <h5>Research Failed</h5>
                <p>${results?.error || 'Unknown error occurred'}</p>
            </div>
        `;
        return;
    }
    
    let html = `
        <div class="research-summary mb-4">
            <h4><i class="fas fa-chart-line"></i> Research Summary</h4>
            <div class="row">
                <div class="col-md-3">
                    <strong>Query:</strong><br>
                    <span class="text-muted">${results.query}</span>
                </div>
                <div class="col-md-2">
                    <strong>Type:</strong><br>
                    <span class="badge bg-primary">${results.research_type}</span>
                </div>
                <div class="col-md-2">
                    <strong>Agents:</strong><br>
                    <span class="badge bg-info">${results.num_agents}</span>
                </div>
                <div class="col-md-2">
                    <strong>Time:</strong><br>
                    <span class="badge bg-success">${Math.round(results.execution_time)}s</span>
                </div>
                <div class="col-md-3">
                    <strong>Model:</strong><br>
                    <span class="badge bg-secondary">${results.model_provider || 'Auto'}</span>
                </div>
            </div>
        </div>
    `;
    
    // Display phase results
    if (results.phases) {
        Object.entries(results.phases).forEach(([phaseName, phaseResults]) => {
            html += `
                <div class="result-section">
                    <h5><i class="fas fa-layer-group"></i> ${phaseName.replace(/_/g, ' ').toUpperCase()}</h5>
            `;
            
            if (Array.isArray(phaseResults)) {
                phaseResults.forEach((agentResult, index) => {
                    const statusBadge = agentResult.status === 'success' ? 
                        '<span class="badge bg-success">Success</span>' : 
                        '<span class="badge bg-danger">Error</span>';
                    
                    html += `
                        <div class="agent-result">
                            <div class="agent-header">
                                <span class="agent-id">Agent ${index + 1}</span>
                                ${statusBadge}
                                <small class="text-muted">
                                    ${agentResult.execution_time ? Math.round(agentResult.execution_time) + 's' : ''}
                                    ${agentResult.model_used ? '• ' + agentResult.model_used : ''}
                                </small>
                            </div>
                            <div class="agent-content">${agentResult.response || 'No response'}</div>
                        </div>
                    `;
                });
            }
            
            html += '</div>';
        });
    }
    
    // Display final synthesis
    if (results.final_synthesis) {
        html += `
            <div class="synthesis-section">
                <h4><i class="fas fa-brain"></i> Final Synthesis</h4>
                <div class="synthesis-content">${results.final_synthesis}</div>
            </div>
        `;
    }
    
    resultsContent.innerHTML = html;
    exportButtons.style.display = 'block';
    
    // Highlight code blocks
    Prism.highlightAll();
}

// Show/hide progress panel
function showProgressPanel() {
    document.getElementById('progressPanel').style.display = 'block';
}

function hideProgressPanel() {
    document.getElementById('progressPanel').style.display = 'none';
}

// Update progress
function updateProgress(percent, message) {
    const progressBar = document.getElementById('overallProgress');
    const updatesContainer = document.getElementById('realTimeUpdates');
    
    progressBar.style.width = percent + '%';
    progressBar.setAttribute('aria-valuenow', percent);
    
    if (message) {
        const timestamp = new Date().toLocaleTimeString();
        const updateHTML = `
            <div class="real-time-update">
                <small class="text-muted">${timestamp}</small> ${message}
            </div>
        `;
        updatesContainer.insertAdjacentHTML('afterbegin', updateHTML);
        
        // Keep only last 10 updates
        while (updatesContainer.children.length > 10) {
            updatesContainer.removeChild(updatesContainer.lastChild);
        }
    }
}

// Form state management
function disableResearchForm() {
    document.getElementById('startResearchBtn').disabled = true;
    document.getElementById('startResearchBtn').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Research in Progress...';
}

function enableResearchForm() {
    document.getElementById('startResearchBtn').disabled = false;
    document.getElementById('startResearchBtn').innerHTML = '<i class="fas fa-play"></i> Start Research';
}

// Preset configurations
function setPreset(preset) {
    const numAgentsSlider = document.getElementById('numAgents');
    const agentCountDisplay = document.getElementById('agentCount');
    const researchType = document.getElementById('researchType');
    
    switch (preset) {
        case 'quick':
            numAgentsSlider.value = 3;
            agentCountDisplay.textContent = '3';
            researchType.value = 'literature_review';
            break;
        case 'standard':
            numAgentsSlider.value = 6;
            agentCountDisplay.textContent = '6';
            researchType.value = 'comprehensive';
            break;
        case 'deep':
            numAgentsSlider.value = 12;
            agentCountDisplay.textContent = '12';
            researchType.value = 'comprehensive';
            break;
    }
    
    // Visual feedback
    document.querySelectorAll('.btn-group .btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
}

// Export results
async function exportResults(format) {
    if (!currentSessionId) {
        showError('No research session to export');
        return;
    }
    
    try {
        const response = await fetch(`/api/research/export/${currentSessionId}/${format}`);
        
        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `research_results_${new Date().toISOString().slice(0,10)}.${format}`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } else {
            throw new Error('Export failed');
        }
    } catch (error) {
        showError('Failed to export results: ' + error.message);
    }
}

// Error handling
function showError(message) {
    const alertHTML = `
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.getElementById('resultsContent').insertAdjacentHTML('afterbegin', alertHTML);
}

// Connection status
function updateConnectionStatus(connected) {
    // Could add a connection indicator to the UI
    console.log('Connection status:', connected ? 'Connected' : 'Disconnected');
}

// Configuration Modal Functions
function showConfigModal() {
    const modal = new bootstrap.Modal(document.getElementById('configModal'));
    modal.show();
}

async function saveConfig() {
    console.log('💾 Saving configuration...');

    const configData = {
        providers: {
            gemini: {
                api_key: document.getElementById('geminiApiKey').value,
                model: document.getElementById('geminiModel').value,
                rate_limit_delay: 5.0,
                timeout: 300
            },
            openai: {
                api_key: document.getElementById('openaiApiKey').value,
                model: document.getElementById('openaiModel').value,
                rate_limit_delay: 1.0,
                timeout: 300
            },
            openrouter: {
                api_key: document.getElementById('openrouterApiKey').value,
                model: document.getElementById('openrouterModel').value,
                rate_limit_delay: 1.0,
                timeout: 300
            },
            anthropic: {
                api_key: document.getElementById('anthropicApiKey').value,
                model: document.getElementById('anthropicModel').value,
                rate_limit_delay: 1.0,
                timeout: 300
            },
            moonshot: {
                api_key: document.getElementById('moonshotApiKey').value,
                model: document.getElementById('moonshotModel').value,
                rate_limit_delay: 1.0,
                timeout: 300
            }
        },
        default_provider: document.getElementById('defaultProvider').value,
        fallback_order: ['gemini', 'openrouter', 'openai', 'anthropic', 'moonshot']
    };

    console.log('📊 Config data:', configData);

    try {
        console.log('📡 Sending config to /api/config...');

        const response = await fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configData)
        });

        console.log('📡 Config response status:', response.status);
        const data = await response.json();
        console.log('📊 Config response data:', data);

        if (data.status === 'success') {
            bootstrap.Modal.getInstance(document.getElementById('configModal')).hide();
            showSuccess('Configuration saved successfully');
            loadAvailableModels(); // Reload models
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        showError('Failed to save configuration: ' + error.message);
    }
}

// Knowledge Base Modal Functions
function showKnowledgeBaseModal() {
    const modal = new bootstrap.Modal(document.getElementById('knowledgeBaseModal'));
    modal.show();
    loadKnowledgeBase();
}

async function loadKnowledgeBase() {
    try {
        const response = await fetch('/api/knowledge-base/list');
        const data = await response.json();

        const container = document.getElementById('knowledgeBaseEntries');

        if (data.status === 'success' && data.entries && data.entries.length > 0) {
            let html = '';
            data.entries.forEach(entry => {
                html += `
                    <div class="kb-entry">
                        <div class="kb-entry-title">${entry.title || 'Untitled'}</div>
                        <div class="kb-entry-content">${(entry.content || '').substring(0, 150)}...</div>
                        <div class="kb-entry-meta">
                            Type: ${entry.data_type || 'unknown'} |
                            Created: ${entry.created || 'unknown'}
                            ${entry.tags ? ' | Tags: ' + entry.tags.join(', ') : ''}
                        </div>
                    </div>
                `;
            });
            container.innerHTML = html;
        } else {
            container.innerHTML = '<p class="text-muted">No entries found in knowledge base</p>';
        }
    } catch (error) {
        document.getElementById('knowledgeBaseEntries').innerHTML =
            '<p class="text-danger">Failed to load knowledge base</p>';
    }
}

async function uploadDocuments() {
    const fileInput = document.getElementById('documentUpload');
    const files = fileInput.files;

    if (files.length === 0) {
        showError('Please select files to upload');
        return;
    }

    for (let file of files) {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch('/api/knowledge-base/upload', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.status === 'success') {
                showSuccess(`Uploaded: ${file.name}`);
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            showError(`Failed to upload ${file.name}: ${error.message}`);
        }
    }

    // Clear file input and reload knowledge base
    fileInput.value = '';
    loadKnowledgeBase();
}

async function searchKnowledgeBase() {
    const query = document.getElementById('kbSearchQuery').value.trim();

    if (!query) {
        showError('Please enter a search query');
        return;
    }

    try {
        const response = await fetch('/api/knowledge-base/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query: query,
                search_type: 'semantic_search'
            })
        });

        const data = await response.json();
        const container = document.getElementById('knowledgeBaseEntries');

        if (data.status === 'success' && data.results && data.results.length > 0) {
            let html = '<h6>Search Results:</h6>';
            data.results.forEach(result => {
                html += `
                    <div class="kb-entry">
                        <div class="kb-entry-title">
                            ${result.title || 'Untitled'}
                            <span class="badge bg-info ms-2">Score: ${result.similarity_score}</span>
                        </div>
                        <div class="kb-entry-content">${result.content}</div>
                        <div class="kb-entry-meta">
                            Type: ${result.data_type} | Created: ${result.created}
                        </div>
                    </div>
                `;
            });
            container.innerHTML = html;
        } else {
            container.innerHTML = '<p class="text-muted">No results found</p>';
        }
    } catch (error) {
        showError('Search failed: ' + error.message);
    }
}

// Success message helper
function showSuccess(message) {
    const alertHTML = `
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.getElementById('resultsContent').insertAdjacentHTML('afterbegin', alertHTML);
}

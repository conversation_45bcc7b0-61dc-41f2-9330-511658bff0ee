#!/usr/bin/env python3
"""
Enhanced Research Orchestrator with Multi-Model Support and Fallback Mechanisms
Integrates knowledge base, semantic search, and multiple LLM providers
"""

import json
import yaml
import time
import threading
import os
import pickle
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Any, Optional
from unified_model_interface import UnifiedModelInterface

class EnhancedResearchOrchestrator:
    """Enhanced research orchestrator with multi-model support and intelligent fallback"""
    
    def __init__(self, config_path="config.yaml", silent=False):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Initialize unified model interface
        self.model_interface = UnifiedModelInterface(self.config.get('unified_models', {}))
        
        # Research orchestrator settings
        if 'research_orchestrator' in self.config:
            research_config = self.config['research_orchestrator']
            self.num_agents = research_config['parallel_agents']
            self.task_timeout = research_config['task_timeout']
            self.aggregation_strategy = research_config['aggregation_strategy']
            self.research_phases = research_config.get('research_phases', [])
            self.agent_types = research_config.get('agent_types', {})
        else:
            self.num_agents = self.config['orchestrator']['parallel_agents']
            self.task_timeout = self.config['orchestrator']['task_timeout']
            self.aggregation_strategy = self.config['orchestrator']['aggregation_strategy']
            self.research_phases = []
            self.agent_types = {}
        
        self.silent = silent
        
        # Track agent progress and results
        self.agent_progress = {}
        self.agent_results = {}
        self.progress_lock = threading.Lock()
        
        # Research-specific tracking
        self.research_context = {}
        self.knowledge_base_entries = []
        
        # Checkpoint management
        self.checkpoint_dir = "research_checkpoints"
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        self.auto_checkpoint = True
        self.checkpoint_interval = 300  # 5 minutes
        
        # Initialize tools
        self._initialize_tools()
    
    def _initialize_tools(self):
        """Initialize research tools with fallback mechanisms"""
        try:
            from tools import discover_tools
            self.tools = discover_tools(self.config, silent=True)
            print(f"✅ Loaded {len(self.tools)} research tools")
        except Exception as e:
            print(f"❌ Failed to load tools: {e}")
            self.tools = {}
    
    def orchestrate_research(self, research_query: str, research_type: str = "comprehensive",
                           model_provider: Optional[str] = None, context_aware: bool = True,
                           context_mode: str = "standard", num_agents: Optional[int] = None,
                           max_tokens: int = 4096, temperature: float = 0.7,
                           api_intensity: str = "standard", tools_config: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Enhanced research orchestration with multi-model support and intelligent fallback
        """
        
        # Override agent count if specified
        if num_agents:
            self.num_agents = min(max(num_agents, 1), 20)  # Limit between 1-20
        
        # Initialize research context with advanced options
        self.research_context = {
            "query": research_query,
            "type": research_type,
            "start_time": time.time(),
            "phases_completed": [],
            "knowledge_gathered": [],
            "active": True,
            "model_provider": model_provider,
            "context_aware": context_aware,
            "context_mode": context_mode,
            "num_agents": self.num_agents,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "api_intensity": api_intensity,
            "tools_config": tools_config or {}
        }
        
        # Reset progress tracking
        self.agent_progress = {}
        self.agent_results = {}
        
        # Check knowledge base first to avoid redundant queries
        kb_results = self._check_knowledge_base(research_query)
        if kb_results and len(kb_results) > 0:
            print(f"📚 Found {len(kb_results)} relevant entries in knowledge base")
            self.research_context["kb_results"] = kb_results
        
        # Execute research workflow
        if research_type == "comprehensive":
            return self._comprehensive_research_workflow(research_query, model_provider, context_aware, context_mode)
        elif research_type == "literature_review":
            return self._literature_review_workflow(research_query, model_provider, context_aware, context_mode)
        elif research_type == "gap_analysis":
            return self._gap_analysis_workflow(research_query, model_provider, context_aware)
        elif research_type == "idea_generation":
            return self._idea_generation_workflow(research_query, model_provider, context_aware)
        elif research_type == "implementation":
            return self._implementation_research_workflow(research_query, model_provider, context_aware)
        else:
            return self._comprehensive_research_workflow(research_query, model_provider, context_aware)
    
    def _check_knowledge_base(self, query: str) -> List[Dict[str, Any]]:
        """Check knowledge base for relevant information before external API calls"""
        try:
            if 'knowledge_base' in self.tools:
                # First try semantic search
                semantic_result = self.tools['knowledge_base'].execute(
                    action="semantic_search",
                    query=query
                )
                
                if semantic_result.get("status") == "success" and semantic_result.get("results"):
                    return semantic_result["results"]
                
                # Fallback to regular search
                search_result = self.tools['knowledge_base'].execute(
                    action="search",
                    query=query
                )
                
                if search_result.get("status") == "success" and search_result.get("results"):
                    return search_result["results"]
            
        except Exception as e:
            print(f"⚠️ Knowledge base check failed: {e}")
        
        return []
    
    def _comprehensive_research_workflow(self, query: str, model_provider: Optional[str] = None,
                                       context_aware: bool = True, context_mode: str = "standard") -> Dict[str, Any]:
        """Comprehensive research workflow with enhanced capabilities"""
        
        try:
            # Start auto-checkpoint monitoring
            checkpoint_thread = threading.Thread(
                target=self.auto_checkpoint_monitor, 
                args=({"query": query, "type": "comprehensive"},), 
                daemon=True
            )
            checkpoint_thread.start()
            
            # Create initial checkpoint
            self.create_checkpoint({"query": query, "type": "comprehensive", "phase": "start"})
            
            # Phase 1: Literature Review and Data Gathering with API fallback
            print("🔍 Phase 1: Literature Review and Data Gathering")
            literature_tasks = self._generate_literature_tasks(query)
            literature_results = self._execute_parallel_research_with_fallback(
                literature_tasks, "literature_review", model_provider, context_aware, context_mode
            )
            
            # Store in knowledge base
            self._store_research_findings(literature_results, "literature")
            
            # Checkpoint after Phase 1
            self.create_checkpoint({
                "query": query,
                "type": "comprehensive",
                "phase": "literature_complete",
                "literature_results": literature_results
            })
            
            # Phase 2: Gap Analysis
            print("🔍 Phase 2: Gap Analysis")
            gap_analysis_tasks = self._generate_gap_analysis_tasks(query, literature_results)
            gap_results = self._execute_parallel_research_with_fallback(
                gap_analysis_tasks, "gap_analysis", model_provider, context_aware, context_mode
            )
            
            # Phase 3: Idea Generation
            print("💡 Phase 3: Idea Generation")
            idea_tasks = self._generate_idea_generation_tasks(query, literature_results, gap_results)
            idea_results = self._execute_parallel_research_with_fallback(
                idea_tasks, "idea_generation", model_provider, context_aware, context_mode
            )
            
            # Phase 4: Implementation Planning
            print("⚙️ Phase 4: Implementation Planning")
            implementation_tasks = self._generate_implementation_tasks(query, idea_results)
            implementation_results = self._execute_parallel_research_with_fallback(
                implementation_tasks, "implementation", model_provider, context_aware, context_mode
            )
            
            # Final Synthesis with best available model
            print("🔬 Final Synthesis")
            final_synthesis = self._synthesize_comprehensive_research(
                literature_results, gap_results, idea_results, implementation_results, query, model_provider
            )
            
            # Mark research as complete
            self.research_context["active"] = False
            
            return {
                "status": "success",
                "research_type": "comprehensive",
                "query": query,
                "model_provider": model_provider,
                "context_aware": context_aware,
                "num_agents": self.num_agents,
                "phases": {
                    "literature_review": literature_results,
                    "gap_analysis": gap_results,
                    "idea_generation": idea_results,
                    "implementation_planning": implementation_results
                },
                "final_synthesis": final_synthesis,
                "knowledge_base_entries": len(self.knowledge_base_entries),
                "execution_time": time.time() - self.research_context["start_time"],
                "kb_results_used": len(self.research_context.get("kb_results", []))
            }
            
        except Exception as e:
            self.research_context["active"] = False
            return {
                "status": "error",
                "error": f"Research workflow failed: {str(e)}",
                "query": query,
                "execution_time": time.time() - self.research_context["start_time"]
            }
    
    def _execute_parallel_research_with_fallback(self, tasks: List[str], phase: str,
                                               model_provider: Optional[str] = None,
                                               context_aware: bool = True, context_mode: str = "standard") -> List[Dict[str, Any]]:
        """Execute research tasks with API fallback mechanisms"""
        
        # Initialize progress tracking
        for i in range(len(tasks)):
            self.agent_progress[i] = "QUEUED"
        
        agent_results = []

        print(f"🔄 Executing {len(tasks)} agents sequentially with 5s rate limiting...")

        # Execute tasks sequentially with rate limiting to prevent API failures
        for i, task in enumerate(tasks):
            try:
                print(f"🤖 Starting Agent {i+1}/{len(tasks)}: {task[:60]}...")

                # Execute the research agent
                result = self._run_enhanced_research_agent(
                    i, task, phase, model_provider, context_aware,
                    self.research_context.get("context_mode", "standard")
                )

                agent_results.append(result)

                # Log result status
                status = result.get('status', 'unknown')
                model_used = result.get('model_used', 'unknown')
                exec_time = result.get('execution_time', 0)
                print(f"✅ Agent {i+1} completed: {status} ({model_used}, {exec_time:.1f}s)")

                # Rate limiting: Wait 5 seconds between agents (except for the last one)
                if i < len(tasks) - 1:
                    print(f"⏳ Rate limiting: waiting 5 seconds before next agent...")
                    time.sleep(5)

            except Exception as e:
                print(f"❌ Agent {i+1} failed: {str(e)}")
                agent_results.append({
                    "agent_id": i,
                    "status": "error",
                    "response": f"Agent {i + 1} failed: {str(e)}",
                    "execution_time": 0,
                    "model_used": "failed"
                })

                # Still wait between agents even on failure to maintain rate limiting
                if i < len(tasks) - 1:
                    print(f"⏳ Rate limiting: waiting 5 seconds before next agent...")
                    time.sleep(5)

        print(f"🎯 Sequential execution completed: {len(agent_results)} agents processed")
        return sorted(agent_results, key=lambda x: x["agent_id"])
    
    def _run_enhanced_research_agent(self, agent_id: int, task: str, phase: str,
                                   model_provider: Optional[str] = None,
                                   context_aware: bool = True, context_mode: str = "standard") -> Dict[str, Any]:
        """Run a single research agent with enhanced capabilities and fallback"""
        try:
            self.update_agent_progress(agent_id, f"PROCESSING {phase.upper()}...")
            
            # Enhance task with context if enabled
            enhanced_task = self._enhance_task_with_context(task, phase, context_aware, context_mode)
            
            # Try external APIs with fallback: semantic_scholar → DBLP → web_search
            api_result = self._try_research_apis_with_fallback(enhanced_task, phase)
            
            # Generate response using specified or best available model
            start_time = time.time()
            
            # Use unified model interface with advanced options
            max_tokens = self.research_context.get("max_tokens", 4096)
            temperature = self.research_context.get("temperature", 0.7)

            # Use enhanced model interface with retry logic
            model_result = self.model_interface.generate(
                enhanced_task,
                provider=model_provider,
                max_retries=3,  # Add retry logic for failed agents
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            execution_time = time.time() - start_time
            
            if model_result['status'] == 'success':
                # Combine API results with model response
                combined_response = self._combine_api_and_model_results(api_result, model_result['content'])
                
                self.update_agent_progress(agent_id, "COMPLETED")
                
                return {
                    "agent_id": agent_id,
                    "status": "success",
                    "response": combined_response,
                    "task": task,
                    "phase": phase,
                    "execution_time": execution_time,
                    "model_used": model_result.get('provider', 'unknown'),
                    "api_sources": api_result.get('sources', [])
                }
            else:
                return {
                    "agent_id": agent_id,
                    "status": "error",
                    "response": f"Model generation failed: {model_result.get('error', 'Unknown error')}",
                    "task": task,
                    "phase": phase,
                    "execution_time": execution_time,
                    "model_used": "failed"
                }
            
        except Exception as e:
            return {
                "agent_id": agent_id,
                "status": "error",
                "response": f"Error: {str(e)}",
                "task": task,
                "phase": phase,
                "execution_time": 0,
                "model_used": "error"
            }

    def _enhance_task_with_context(self, task: str, phase: str, context_aware: bool,
                                 context_mode: str = "standard") -> str:
        """Enhance task with research context if enabled"""
        if not context_aware:
            return task

        # Add research context
        context_info = []

        # Add knowledge base results if available
        kb_results = self.research_context.get("kb_results", [])
        if kb_results:
            context_info.append(f"Relevant knowledge base entries: {len(kb_results)} found")
            # Add top 3 KB results as context
            for i, result in enumerate(kb_results[:3]):
                context_info.append(f"KB{i+1}: {result.get('title', 'Untitled')} - {result.get('content', '')[:200]}...")

        # Add detailed research query context
        query = self.research_context['query']
        context_info.append(f"RESEARCH FOCUS: {query}")
        context_info.append(f"Research phase: {phase}")
        context_info.append(f"Research intensity: {self.research_context.get('api_intensity', 'standard')}")

        # Add specific guidance based on query content
        query_lower = query.lower()
        if any(term in query_lower for term in ['machine learning', 'ml', 'deep learning', 'neural']):
            context_info.append("FOCUS AREAS: Include recent advances in neural networks, transformers, foundation models, and practical applications")
        if any(term in query_lower for term in ['dataset', 'data', 'distillation']):
            context_info.append("FOCUS AREAS: Include data efficiency, synthetic data generation, and dataset optimization techniques")
        if any(term in query_lower for term in ['multimodal', 'vision', 'nlp', 'audio']):
            context_info.append("FOCUS AREAS: Include cross-modal learning, vision-language models, and multimodal architectures")

        # Add specific search guidance
        context_info.append("SEARCH STRATEGY: Use specific technical terms, author names, and recent publication venues")
        context_info.append("QUALITY FOCUS: Prioritize high-impact venues (NeurIPS, ICML, ICLR, CVPR, ACL, etc.) and recent papers (2020-2024)")

        # Enhanced Context-Aware Mode: Add previous phase results for later phases
        if context_mode == "enhanced" and phase != "literature_review":
            previous_results = self._get_previous_phase_results(phase)
            if previous_results:
                context_info.append("PREVIOUS PHASE FINDINGS:")
                for phase_name, findings in previous_results.items():
                    context_info.append(f"{phase_name.upper()}: {findings[:300]}...")

        if context_info:
            enhanced_task = f"CONTEXT:\n{chr(10).join(context_info)}\n\nTASK:\n{task}"
            return enhanced_task

        return task

    def _get_previous_phase_results(self, current_phase: str) -> Dict[str, str]:
        """Get summarized results from previous phases"""
        phase_order = ["literature_review", "gap_analysis", "idea_generation", "implementation"]

        try:
            current_index = phase_order.index(current_phase)
        except ValueError:
            return {}

        previous_results = {}

        # Get results from completed phases
        for i in range(current_index):
            phase_name = phase_order[i]
            if phase_name in self.research_context.get("completed_phases", {}):
                phase_results = self.research_context["completed_phases"][phase_name]
                # Summarize the results
                summary = self._summarize_phase_results(phase_results)
                previous_results[phase_name] = summary

        return previous_results

    def _summarize_phase_results(self, phase_results: List[Dict]) -> str:
        """Summarize phase results for context sharing"""
        if not phase_results:
            return "No results available"

        successful_results = [r for r in phase_results if r.get("status") == "success"]
        if not successful_results:
            return "No successful results from this phase"

        # Combine and truncate responses
        combined_text = " ".join([r.get("response", "")[:200] for r in successful_results[:3]])
        return combined_text[:500] + "..." if len(combined_text) > 500 else combined_text

    def _enhance_academic_query(self, query: str) -> str:
        """Enhance query for better academic search results"""

        # Add academic terms and synonyms
        query_lower = query.lower()
        enhanced_terms = []

        # Machine learning enhancements
        if any(term in query_lower for term in ['machine learning', 'ml']):
            enhanced_terms.extend(['neural networks', 'deep learning', 'artificial intelligence'])

        if 'deep learning' in query_lower:
            enhanced_terms.extend(['transformers', 'attention mechanisms', 'foundation models'])

        if any(term in query_lower for term in ['advanced', 'techniques']):
            enhanced_terms.extend(['state-of-the-art', 'novel methods', 'recent advances'])

        if 'dataset distillation' in query_lower:
            enhanced_terms.extend(['data efficiency', 'synthetic data', 'knowledge distillation'])

        if 'multimodal' in query_lower:
            enhanced_terms.extend(['vision-language', 'cross-modal', 'multi-modal learning'])

        # Combine original query with enhanced terms
        if enhanced_terms:
            enhanced_query = f"{query} {' '.join(enhanced_terms[:3])}"
        else:
            enhanced_query = query

        # Add recent year filter for academic relevance
        enhanced_query += " 2020..2024"

        return enhanced_query

    def _synthesize_literature_review(self, results: List[Dict], query: str, model_provider: Optional[str] = None) -> str:
        """Synthesize literature review findings"""

        # Prepare synthesis prompt
        synthesis_prompt = f"""
        Synthesize the following literature review findings for: {query}

        LITERATURE REVIEW FINDINGS:
        {self._format_results_for_synthesis(results)}

        Please provide a comprehensive literature review synthesis that:
        1. Summarizes key findings and trends
        2. Identifies major research themes
        3. Highlights important methodologies
        4. Notes significant researchers and institutions
        5. Provides actionable insights for future research
        """

        # Use unified model interface for synthesis
        result = self.model_interface.generate(
            synthesis_prompt,
            provider=model_provider,
            max_tokens=4096,
            temperature=0.7
        )

        if result['status'] == 'success':
            return result['content']
        else:
            return f"Literature review synthesis failed: {result.get('error', 'Unknown error')}. Please review individual agent results."

    def _try_research_apis_with_fallback(self, query: str, phase: str) -> Dict[str, Any]:
        """Try research APIs with fallback based on configuration"""
        api_results = {"sources": [], "content": "", "detailed_results": []}
        tools_config = self.research_context.get("tools_config", {})
        api_intensity = self.research_context.get("api_intensity", "standard")

        # Determine max results based on intensity
        max_results = {"light": 3, "standard": 5, "intensive": 10}.get(api_intensity, 5)

        print(f"🔍 Research API fallback - Intensity: {api_intensity}, Max results: {max_results}")

        # Try Knowledge Base first if enabled
        if tools_config.get("use_knowledge_base", True) and 'knowledge_base' in self.tools:
            try:
                print("📚 Searching knowledge base...")
                result = self.tools['knowledge_base'].execute(
                    action="semantic_search",
                    query=query,
                    max_results=max_results
                )
                if result.get("status") == "success" and result.get("results"):
                    api_results["sources"].append("knowledge_base")
                    api_results["content"] += f"Knowledge Base: {len(result['results'])} entries found. "
                    api_results["detailed_results"].extend(result["results"][:3])  # Add top 3
                    print(f"✅ Knowledge base: {len(result['results'])} results")
            except Exception as e:
                print(f"⚠️ Knowledge base search failed: {e}")

        # Try Semantic Scholar if enabled
        if tools_config.get("use_semantic_scholar", True) and 'semantic_scholar' in self.tools:
            try:
                print(f"🎓 Searching Semantic Scholar for: {query[:50]}...")

                # Enhance query for better academic search
                enhanced_query = self._enhance_academic_query(query)
                print(f"   Enhanced query: {enhanced_query[:60]}...")

                result = self.tools['semantic_scholar'].execute(query=enhanced_query, max_results=max_results)
                if result.get("status") == "success" and result.get("papers"):
                    api_results["sources"].append("semantic_scholar")
                    papers = result["papers"]
                    api_results["content"] += f"Semantic Scholar: {len(papers)} papers found. "

                    # Add detailed paper information
                    for paper in papers[:3]:
                        title = paper.get('title', 'Unknown')
                        authors = paper.get('authors', [])
                        year = paper.get('year', 'Unknown')
                        venue = paper.get('venue', 'Unknown')
                        api_results["content"] += f"Paper: '{title}' by {authors[:2]} ({year}, {venue}). "

                    api_results["detailed_results"].extend(papers[:3])
                    print(f"✅ Semantic Scholar: {len(papers)} papers found")
                    if api_intensity == "light":
                        return api_results  # Return early for light mode
            except Exception as e:
                print(f"⚠️ Semantic Scholar failed: {e}")

        # Fallback to DBLP
        if 'dblp_search' in self.tools:
            try:
                print("💻 Searching DBLP...")
                result = self.tools['dblp_search'].execute(query=query, max_results=max_results)
                if result.get("status") == "success" and result.get("publications"):
                    api_results["sources"].append("dblp")
                    api_results["content"] += f"DBLP: {len(result['publications'])} publications found. "
                    api_results["detailed_results"].extend(result["publications"][:2])  # Add top 2
                    print(f"✅ DBLP: {len(result['publications'])} publications")
            except Exception as e:
                print(f"⚠️ DBLP search failed: {e}")

        # Try GitHub research for implementation details if enabled
        if tools_config.get("use_github", True) and 'github_research' in self.tools:
            try:
                print("🐙 Searching GitHub repositories...")
                result = self.tools['github_research'].execute(
                    query=query,
                    search_type="repositories",
                    sort="stars",
                    max_results=min(max_results, 10),  # Limit GitHub results
                    language="python"  # Focus on Python implementations
                )
                if result.get("status") == "success" and result.get("repositories"):
                    api_results["sources"].append("github")
                    repos = result["repositories"]
                    api_results["content"] += f"GitHub: {len(repos)} repositories found. "

                    # Add repository information
                    for repo in repos[:2]:
                        name = repo.get('name', 'Unknown')
                        stars = repo.get('stars', 0)
                        description = repo.get('description', '')[:100]
                        api_results["content"] += f"Repo: '{name}' ({stars} stars) - {description}. "

                    api_results["detailed_results"].extend(repos[:2])
                    print(f"✅ GitHub: {len(repos)} repositories found")
            except Exception as e:
                print(f"⚠️ GitHub search failed: {e}")

        # Fallback to web search if enabled
        if tools_config.get("use_web_search", True) and 'search_web' in self.tools:
            try:
                print("🌐 Searching web...")
                result = self.tools['search_web'].execute(query=query)

                # Web search tool returns a list directly, not a dict with status
                if isinstance(result, list) and len(result) > 0:
                    # Filter out error results
                    valid_results = [r for r in result if isinstance(r, dict) and 'error' not in r]

                    if valid_results:
                        api_results["sources"].append("web_search")
                        api_results["content"] += f"Web Search: {len(valid_results)} results found. "
                        api_results["detailed_results"].extend(valid_results[:2])  # Add top 2
                        print(f"✅ Web search: {len(valid_results)} results")
                    else:
                        print(f"⚠️ Web search: No valid results found")
                else:
                    print(f"⚠️ Web search: Invalid result format")
            except Exception as e:
                print(f"⚠️ Web search failed: {e}")

        return api_results

    def _combine_api_and_model_results(self, api_result: Dict[str, Any], model_response: str) -> str:
        """Combine API research results with model-generated response"""
        if api_result.get("content"):
            combined = f"RESEARCH DATA:\n{api_result['content']}\n\nANALYSIS:\n{model_response}"
            if api_result.get("sources"):
                combined += f"\n\nSOURCES: {', '.join(api_result['sources'])}"
            return combined
        return model_response

    def update_agent_progress(self, agent_id: int, status: str):
        """Update agent progress with thread safety"""
        with self.progress_lock:
            self.agent_progress[agent_id] = status

    def _store_research_findings(self, results: List[Dict], data_type: str):
        """Store research findings in knowledge base"""
        if 'knowledge_base' not in self.tools:
            return

        for result in results:
            if result.get("status") == "success":
                try:
                    self.tools['knowledge_base'].execute(
                        action="store",
                        data_type=data_type,
                        key=f"{data_type}_{int(time.time())}_{result['agent_id']}",
                        content=result.get("response", ""),
                        metadata={
                            "phase": result.get("phase", ""),
                            "agent_id": result.get("agent_id", 0),
                            "execution_time": result.get("execution_time", 0),
                            "model_used": result.get("model_used", "unknown"),
                            "research_query": self.research_context.get("query", "")
                        },
                        tags=[data_type, "research", result.get("phase", "")]
                    )
                    self.knowledge_base_entries.append(result)
                except Exception as e:
                    print(f"⚠️ Failed to store finding: {e}")

    # Placeholder methods for different research workflows
    def _literature_review_workflow(self, query: str, model_provider: Optional[str] = None,
                                  context_aware: bool = True, context_mode: str = "standard") -> Dict[str, Any]:
        """Literature review workflow"""
        tasks = self._generate_literature_tasks(query)
        results = self._execute_parallel_research_with_fallback(tasks, "literature_review", model_provider, context_aware, context_mode)

        # Generate synthesis for literature review
        synthesis = self._synthesize_literature_review(results, query, model_provider)

        return {
            "status": "success",
            "research_type": "literature_review",
            "query": query,
            "model_provider": model_provider,
            "context_aware": context_aware,
            "context_mode": context_mode,
            "num_agents": len(tasks),
            "phases": {
                "literature_review": results
            },
            "final_synthesis": synthesis,
            "execution_time": time.time() - self.research_context["start_time"]
        }

    def _gap_analysis_workflow(self, query: str, model_provider: Optional[str] = None,
                             context_aware: bool = True) -> Dict[str, Any]:
        """Gap analysis workflow"""
        tasks = self._generate_gap_analysis_tasks(query, [])
        results = self._execute_parallel_research_with_fallback(tasks, "gap_analysis", model_provider, context_aware)

        return {
            "status": "success",
            "research_type": "gap_analysis",
            "query": query,
            "results": results,
            "execution_time": time.time() - self.research_context["start_time"]
        }

    def _idea_generation_workflow(self, query: str, model_provider: Optional[str] = None,
                                context_aware: bool = True) -> Dict[str, Any]:
        """Idea generation workflow"""
        tasks = self._generate_idea_generation_tasks(query, [], [])
        results = self._execute_parallel_research_with_fallback(tasks, "idea_generation", model_provider, context_aware)

        return {
            "status": "success",
            "research_type": "idea_generation",
            "query": query,
            "results": results,
            "execution_time": time.time() - self.research_context["start_time"]
        }

    def _implementation_research_workflow(self, query: str, model_provider: Optional[str] = None,
                                        context_aware: bool = True) -> Dict[str, Any]:
        """Implementation research workflow"""
        tasks = self._generate_implementation_tasks(query, [])
        results = self._execute_parallel_research_with_fallback(tasks, "implementation", model_provider, context_aware)

        return {
            "status": "success",
            "research_type": "implementation",
            "query": query,
            "results": results,
            "execution_time": time.time() - self.research_context["start_time"]
        }

    # Task generation methods
    def _generate_literature_tasks(self, query: str) -> List[str]:
        """Generate literature review tasks based on number of agents"""

        # Base tasks that are always relevant
        base_tasks = [
            f"Conduct comprehensive literature search on: {query}",
            f"Analyze recent research trends and developments in: {query}",
            f"Identify key researchers, institutions, and research groups working on: {query}",
            f"Review methodological approaches and techniques used in: {query}",
            f"Examine theoretical frameworks and foundations for: {query}",
            f"Survey empirical findings and experimental results in: {query}",
            f"Investigate state-of-the-art methods and breakthrough papers in: {query}",
            f"Analyze performance benchmarks and evaluation metrics for: {query}",
            f"Review survey papers and comprehensive reviews on: {query}",
            f"Examine applications and real-world implementations of: {query}",
            f"Investigate challenges and limitations in current research on: {query}",
            f"Analyze future research directions and open problems in: {query}",
            f"Review interdisciplinary connections and cross-domain applications of: {query}",
            f"Examine datasets, tools, and resources commonly used in: {query}",
            f"Investigate ethical considerations and societal impacts of: {query}",
            f"Analyze computational complexity and scalability issues in: {query}",
            f"Review reproducibility and replication studies in: {query}",
            f"Examine industry adoption and commercial applications of: {query}",
            f"Investigate emerging trends and novel approaches in: {query}",
            f"Analyze comparative studies and method comparisons in: {query}"
        ]

        # Return the number of tasks matching the number of agents
        num_tasks = min(self.num_agents, len(base_tasks))
        return base_tasks[:num_tasks]

    def _generate_gap_analysis_tasks(self, query: str, literature_results: List[Dict]) -> List[str]:
        """Generate gap analysis tasks based on number of agents"""

        base_tasks = [
            f"Identify methodological gaps in current research on: {query}",
            f"Analyze theoretical limitations and missing frameworks in: {query}",
            f"Find empirical validation opportunities and unexplored areas in: {query}",
            f"Examine cross-disciplinary research potential and interdisciplinary gaps in: {query}",
            f"Assess scalability challenges and computational limitations in: {query}",
            f"Evaluate practical implementation gaps and deployment barriers for: {query}",
            f"Identify data availability and quality gaps in: {query}",
            f"Analyze evaluation metrics and benchmarking gaps in: {query}",
            f"Examine reproducibility and replication gaps in: {query}",
            f"Identify standardization and protocol gaps in: {query}",
            f"Analyze computational efficiency and optimization gaps in: {query}",
            f"Examine ethical and fairness considerations gaps in: {query}",
            f"Identify real-world deployment and adoption gaps in: {query}",
            f"Analyze robustness and generalization gaps in: {query}",
            f"Examine interpretability and explainability gaps in: {query}"
        ]

        num_tasks = min(self.num_agents, len(base_tasks))
        return base_tasks[:num_tasks]

    def _generate_idea_generation_tasks(self, query: str, literature_results: List[Dict],
                                      gap_results: List[Dict]) -> List[str]:
        """Generate idea generation tasks"""
        return [
            f"Generate novel research directions for: {query}",
            f"Propose innovative methodological approaches to: {query}",
            f"Design creative solutions for challenges in: {query}",
            f"Develop interdisciplinary research ideas for: {query}",
            f"Create breakthrough concepts related to: {query}",
            f"Formulate testable hypotheses for: {query}"
        ]

    def _generate_implementation_tasks(self, query: str, idea_results: List[Dict]) -> List[str]:
        """Generate implementation tasks"""
        return [
            f"Design implementation strategy for: {query}",
            f"Analyze technical requirements for: {query}",
            f"Evaluate resource needs for: {query}",
            f"Create development roadmap for: {query}",
            f"Assess feasibility and risks of: {query}",
            f"Plan validation and testing approach for: {query}"
        ]

    def _synthesize_comprehensive_research(self, literature_results: List[Dict], gap_results: List[Dict],
                                         idea_results: List[Dict], implementation_results: List[Dict],
                                         query: str, model_provider: Optional[str] = None) -> str:
        """Synthesize comprehensive research findings"""

        # Prepare synthesis prompt
        synthesis_prompt = f"""
        Synthesize the following comprehensive research analysis for: {query}

        LITERATURE REVIEW FINDINGS:
        {self._format_results_for_synthesis(literature_results)}

        GAP ANALYSIS FINDINGS:
        {self._format_results_for_synthesis(gap_results)}

        IDEA GENERATION FINDINGS:
        {self._format_results_for_synthesis(idea_results)}

        IMPLEMENTATION FINDINGS:
        {self._format_results_for_synthesis(implementation_results)}

        Please provide a comprehensive synthesis that integrates all findings and provides actionable insights.
        """

        # Use unified model interface for synthesis
        result = self.model_interface.generate(
            synthesis_prompt,
            provider=model_provider,
            max_tokens=8192,
            temperature=0.7
        )

        if result['status'] == 'success':
            return result['content']
        else:
            return f"Synthesis failed: {result.get('error', 'Unknown error')}. Please review individual phase results."

    def _format_results_for_synthesis(self, results: List[Dict]) -> str:
        """Format results for synthesis"""
        formatted = []
        for i, result in enumerate(results):
            if result.get("status") == "success":
                formatted.append(f"Agent {i+1}: {result.get('response', '')[:500]}...")
        return "\n".join(formatted) if formatted else "No successful results"

    # Checkpoint methods
    def create_checkpoint(self, session_data: Dict[str, Any], checkpoint_name: str = None) -> str:
        """Create a checkpoint of the current research session"""
        try:
            if not checkpoint_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                checkpoint_name = f"research_checkpoint_{timestamp}"

            checkpoint_path = os.path.join(self.checkpoint_dir, f"{checkpoint_name}.pkl")

            checkpoint_data = {
                "timestamp": datetime.now().isoformat(),
                "session_data": session_data,
                "research_context": self.research_context,
                "knowledge_base_entries": self.knowledge_base_entries,
                "agent_progress": self.agent_progress,
                "agent_results": self.agent_results,
                "config": self.config
            }

            with open(checkpoint_path, 'wb') as f:
                pickle.dump(checkpoint_data, f)

            if not self.silent:
                print(f"✅ Checkpoint created: {checkpoint_path}")
            return checkpoint_path

        except Exception as e:
            print(f"❌ Failed to create checkpoint: {str(e)}")
            return None

    def load_checkpoint(self, checkpoint_path: str) -> Dict[str, Any]:
        """Load a research session from checkpoint"""
        try:
            with open(checkpoint_path, 'rb') as f:
                checkpoint_data = pickle.load(f)

            # Restore session state
            self.research_context = checkpoint_data.get("research_context", {})
            self.knowledge_base_entries = checkpoint_data.get("knowledge_base_entries", [])
            self.agent_progress = checkpoint_data.get("agent_progress", {})
            self.agent_results = checkpoint_data.get("agent_results", {})

            print(f"✅ Checkpoint loaded: {checkpoint_path}")
            return checkpoint_data.get("session_data", {})

        except Exception as e:
            print(f"❌ Failed to load checkpoint: {str(e)}")
            return {}

    def auto_checkpoint_monitor(self, session_data: Dict[str, Any]):
        """Monitor and create automatic checkpoints"""
        if not self.auto_checkpoint:
            return

        last_checkpoint = time.time()

        while self.research_context.get("active", False):
            time.sleep(60)  # Check every minute

            if time.time() - last_checkpoint >= self.checkpoint_interval:
                self.create_checkpoint(session_data, f"auto_checkpoint_{int(time.time())}")
                last_checkpoint = time.time()

    def list_checkpoints(self) -> List[str]:
        """List available checkpoints"""
        try:
            checkpoints = []
            for file in os.listdir(self.checkpoint_dir):
                if file.endswith('.pkl'):
                    checkpoints.append(os.path.join(self.checkpoint_dir, file))
            return sorted(checkpoints, key=os.path.getmtime, reverse=True)
        except Exception as e:
            print(f"❌ Failed to list checkpoints: {str(e)}")
            return []

    def get_available_models(self) -> Dict[str, Any]:
        """Get available model providers and their status"""
        return self.model_interface.get_provider_status()

    def set_model_provider(self, provider: str):
        """Set the default model provider"""
        self.model_interface.set_current_provider(provider)

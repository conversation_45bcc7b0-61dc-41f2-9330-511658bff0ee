#!/usr/bin/env python3
"""
Enhanced Research Orchestrator with Multi-Model Support and Fallback Mechanisms
Integrates knowledge base, semantic search, and multiple LLM providers
"""

import json
import yaml
import time
import threading
import os
import pickle
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from typing import List, Dict, Any, Optional
from unified_model_interface import UnifiedModelInterface

class EnhancedResearchOrchestrator:
    """Enhanced research orchestrator with multi-model support and intelligent fallback"""
    
    def __init__(self, config_path="config.yaml", silent=False):
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        # Initialize unified model interface
        self.model_interface = UnifiedModelInterface(self.config.get('unified_models', {}))
        
        # Research orchestrator settings
        if 'research_orchestrator' in self.config:
            research_config = self.config['research_orchestrator']
            self.num_agents = research_config['parallel_agents']
            self.task_timeout = research_config['task_timeout']
            self.aggregation_strategy = research_config['aggregation_strategy']
            self.research_phases = research_config.get('research_phases', [])
            self.agent_types = research_config.get('agent_types', {})
        else:
            self.num_agents = self.config['orchestrator']['parallel_agents']
            self.task_timeout = self.config['orchestrator']['task_timeout']
            self.aggregation_strategy = self.config['orchestrator']['aggregation_strategy']
            self.research_phases = []
            self.agent_types = {}
        
        self.silent = silent
        
        # Track agent progress and results
        self.agent_progress = {}
        self.agent_results = {}
        self.progress_lock = threading.Lock()
        
        # Research-specific tracking
        self.research_context = {}
        self.knowledge_base_entries = []
        
        # Checkpoint management
        self.checkpoint_dir = "research_checkpoints"
        os.makedirs(self.checkpoint_dir, exist_ok=True)
        self.auto_checkpoint = True
        self.checkpoint_interval = 300  # 5 minutes
        
        # Initialize tools
        self._initialize_tools()
    
    def _initialize_tools(self):
        """Initialize research tools with fallback mechanisms"""
        try:
            from tools import discover_tools
            self.tools = discover_tools(self.config, silent=True)
            print(f"✅ Loaded {len(self.tools)} research tools")
        except Exception as e:
            print(f"❌ Failed to load tools: {e}")
            self.tools = {}
    
    def orchestrate_research(self, research_query: str, research_type: str = "comprehensive", 
                           model_provider: Optional[str] = None, context_aware: bool = True,
                           num_agents: Optional[int] = None) -> Dict[str, Any]:
        """
        Enhanced research orchestration with multi-model support and intelligent fallback
        """
        
        # Override agent count if specified
        if num_agents:
            self.num_agents = min(max(num_agents, 1), 20)  # Limit between 1-20
        
        # Initialize research context
        self.research_context = {
            "query": research_query,
            "type": research_type,
            "start_time": time.time(),
            "phases_completed": [],
            "knowledge_gathered": [],
            "active": True,
            "model_provider": model_provider,
            "context_aware": context_aware,
            "num_agents": self.num_agents
        }
        
        # Reset progress tracking
        self.agent_progress = {}
        self.agent_results = {}
        
        # Check knowledge base first to avoid redundant queries
        kb_results = self._check_knowledge_base(research_query)
        if kb_results and len(kb_results) > 0:
            print(f"📚 Found {len(kb_results)} relevant entries in knowledge base")
            self.research_context["kb_results"] = kb_results
        
        # Execute research workflow
        if research_type == "comprehensive":
            return self._comprehensive_research_workflow(research_query, model_provider, context_aware)
        elif research_type == "literature_review":
            return self._literature_review_workflow(research_query, model_provider, context_aware)
        elif research_type == "gap_analysis":
            return self._gap_analysis_workflow(research_query, model_provider, context_aware)
        elif research_type == "idea_generation":
            return self._idea_generation_workflow(research_query, model_provider, context_aware)
        elif research_type == "implementation":
            return self._implementation_research_workflow(research_query, model_provider, context_aware)
        else:
            return self._comprehensive_research_workflow(research_query, model_provider, context_aware)
    
    def _check_knowledge_base(self, query: str) -> List[Dict[str, Any]]:
        """Check knowledge base for relevant information before external API calls"""
        try:
            if 'knowledge_base' in self.tools:
                # First try semantic search
                semantic_result = self.tools['knowledge_base'].execute(
                    action="semantic_search",
                    query=query
                )
                
                if semantic_result.get("status") == "success" and semantic_result.get("results"):
                    return semantic_result["results"]
                
                # Fallback to regular search
                search_result = self.tools['knowledge_base'].execute(
                    action="search",
                    query=query
                )
                
                if search_result.get("status") == "success" and search_result.get("results"):
                    return search_result["results"]
            
        except Exception as e:
            print(f"⚠️ Knowledge base check failed: {e}")
        
        return []
    
    def _comprehensive_research_workflow(self, query: str, model_provider: Optional[str] = None, 
                                       context_aware: bool = True) -> Dict[str, Any]:
        """Comprehensive research workflow with enhanced capabilities"""
        
        try:
            # Start auto-checkpoint monitoring
            checkpoint_thread = threading.Thread(
                target=self.auto_checkpoint_monitor, 
                args=({"query": query, "type": "comprehensive"},), 
                daemon=True
            )
            checkpoint_thread.start()
            
            # Create initial checkpoint
            self.create_checkpoint({"query": query, "type": "comprehensive", "phase": "start"})
            
            # Phase 1: Literature Review and Data Gathering with API fallback
            print("🔍 Phase 1: Literature Review and Data Gathering")
            literature_tasks = self._generate_literature_tasks(query)
            literature_results = self._execute_parallel_research_with_fallback(
                literature_tasks, "literature_review", model_provider, context_aware
            )
            
            # Store in knowledge base
            self._store_research_findings(literature_results, "literature")
            
            # Checkpoint after Phase 1
            self.create_checkpoint({
                "query": query,
                "type": "comprehensive",
                "phase": "literature_complete",
                "literature_results": literature_results
            })
            
            # Phase 2: Gap Analysis
            print("🔍 Phase 2: Gap Analysis")
            gap_analysis_tasks = self._generate_gap_analysis_tasks(query, literature_results)
            gap_results = self._execute_parallel_research_with_fallback(
                gap_analysis_tasks, "gap_analysis", model_provider, context_aware
            )
            
            # Phase 3: Idea Generation
            print("💡 Phase 3: Idea Generation")
            idea_tasks = self._generate_idea_generation_tasks(query, literature_results, gap_results)
            idea_results = self._execute_parallel_research_with_fallback(
                idea_tasks, "idea_generation", model_provider, context_aware
            )
            
            # Phase 4: Implementation Planning
            print("⚙️ Phase 4: Implementation Planning")
            implementation_tasks = self._generate_implementation_tasks(query, idea_results)
            implementation_results = self._execute_parallel_research_with_fallback(
                implementation_tasks, "implementation", model_provider, context_aware
            )
            
            # Final Synthesis with best available model
            print("🔬 Final Synthesis")
            final_synthesis = self._synthesize_comprehensive_research(
                literature_results, gap_results, idea_results, implementation_results, query, model_provider
            )
            
            # Mark research as complete
            self.research_context["active"] = False
            
            return {
                "status": "success",
                "research_type": "comprehensive",
                "query": query,
                "model_provider": model_provider,
                "context_aware": context_aware,
                "num_agents": self.num_agents,
                "phases": {
                    "literature_review": literature_results,
                    "gap_analysis": gap_results,
                    "idea_generation": idea_results,
                    "implementation_planning": implementation_results
                },
                "final_synthesis": final_synthesis,
                "knowledge_base_entries": len(self.knowledge_base_entries),
                "execution_time": time.time() - self.research_context["start_time"],
                "kb_results_used": len(self.research_context.get("kb_results", []))
            }
            
        except Exception as e:
            self.research_context["active"] = False
            return {
                "status": "error",
                "error": f"Research workflow failed: {str(e)}",
                "query": query,
                "execution_time": time.time() - self.research_context["start_time"]
            }
    
    def _execute_parallel_research_with_fallback(self, tasks: List[str], phase: str, 
                                               model_provider: Optional[str] = None,
                                               context_aware: bool = True) -> List[Dict[str, Any]]:
        """Execute research tasks with API fallback mechanisms"""
        
        # Initialize progress tracking
        for i in range(len(tasks)):
            self.agent_progress[i] = "QUEUED"
        
        agent_results = []
        
        with ThreadPoolExecutor(max_workers=min(len(tasks), self.num_agents)) as executor:
            # Submit all tasks
            future_to_task = {
                executor.submit(
                    self._run_enhanced_research_agent, 
                    i, tasks[i], phase, model_provider, context_aware
                ): i 
                for i in range(len(tasks))
            }
            
            # Collect results
            for future in as_completed(future_to_task, timeout=self.task_timeout):
                try:
                    result = future.result()
                    agent_results.append(result)
                except Exception as e:
                    task_id = future_to_task[future]
                    agent_results.append({
                        "agent_id": task_id,
                        "status": "error",
                        "response": f"Task {task_id + 1} failed: {str(e)}",
                        "execution_time": 0,
                        "model_used": "none"
                    })
        
        return sorted(agent_results, key=lambda x: x["agent_id"])
    
    def _run_enhanced_research_agent(self, agent_id: int, task: str, phase: str, 
                                   model_provider: Optional[str] = None,
                                   context_aware: bool = True) -> Dict[str, Any]:
        """Run a single research agent with enhanced capabilities and fallback"""
        try:
            self.update_agent_progress(agent_id, f"PROCESSING {phase.upper()}...")
            
            # Enhance task with context if enabled
            enhanced_task = self._enhance_task_with_context(task, phase, context_aware)
            
            # Try external APIs with fallback: semantic_scholar → DBLP → web_search
            api_result = self._try_research_apis_with_fallback(enhanced_task, phase)
            
            # Generate response using specified or best available model
            start_time = time.time()
            
            # Use unified model interface with fallback
            model_result = self.model_interface.generate(
                enhanced_task, 
                provider=model_provider,
                max_tokens=4096,
                temperature=0.7
            )
            
            execution_time = time.time() - start_time
            
            if model_result['status'] == 'success':
                # Combine API results with model response
                combined_response = self._combine_api_and_model_results(api_result, model_result['content'])
                
                self.update_agent_progress(agent_id, "COMPLETED")
                
                return {
                    "agent_id": agent_id,
                    "status": "success",
                    "response": combined_response,
                    "task": task,
                    "phase": phase,
                    "execution_time": execution_time,
                    "model_used": model_result.get('provider', 'unknown'),
                    "api_sources": api_result.get('sources', [])
                }
            else:
                return {
                    "agent_id": agent_id,
                    "status": "error",
                    "response": f"Model generation failed: {model_result.get('error', 'Unknown error')}",
                    "task": task,
                    "phase": phase,
                    "execution_time": execution_time,
                    "model_used": "failed"
                }
            
        except Exception as e:
            return {
                "agent_id": agent_id,
                "status": "error",
                "response": f"Error: {str(e)}",
                "task": task,
                "phase": phase,
                "execution_time": 0,
                "model_used": "error"
            }

    def _enhance_task_with_context(self, task: str, phase: str, context_aware: bool) -> str:
        """Enhance task with research context if enabled"""
        if not context_aware:
            return task

        # Add research context
        context_info = []

        # Add knowledge base results if available
        kb_results = self.research_context.get("kb_results", [])
        if kb_results:
            context_info.append(f"Relevant knowledge base entries: {len(kb_results)} found")
            # Add top 3 KB results as context
            for i, result in enumerate(kb_results[:3]):
                context_info.append(f"KB{i+1}: {result.get('title', 'Untitled')} - {result.get('content', '')[:200]}...")

        # Add research query context
        context_info.append(f"Main research query: {self.research_context['query']}")
        context_info.append(f"Research phase: {phase}")

        if context_info:
            enhanced_task = f"CONTEXT:\n{chr(10).join(context_info)}\n\nTASK:\n{task}"
            return enhanced_task

        return task

    def _try_research_apis_with_fallback(self, query: str, phase: str) -> Dict[str, Any]:
        """Try research APIs with fallback: semantic_scholar → DBLP → web_search"""
        api_results = {"sources": [], "content": ""}

        # Try Semantic Scholar first
        if 'semantic_scholar' in self.tools:
            try:
                result = self.tools['semantic_scholar'].execute(query=query, max_results=5)
                if result.get("status") == "success" and result.get("papers"):
                    api_results["sources"].append("semantic_scholar")
                    api_results["content"] += f"Semantic Scholar Results: {len(result['papers'])} papers found. "
                    return api_results
            except Exception as e:
                print(f"⚠️ Semantic Scholar failed: {e}")

        # Fallback to DBLP
        if 'dblp_search' in self.tools:
            try:
                result = self.tools['dblp_search'].execute(query=query, max_results=5)
                if result.get("status") == "success" and result.get("publications"):
                    api_results["sources"].append("dblp")
                    api_results["content"] += f"DBLP Results: {len(result['publications'])} publications found. "
                    return api_results
            except Exception as e:
                print(f"⚠️ DBLP search failed: {e}")

        # Fallback to web search
        if 'search_web' in self.tools:
            try:
                result = self.tools['search_web'].execute(query=query)
                if result.get("status") == "success" and result.get("results"):
                    api_results["sources"].append("web_search")
                    api_results["content"] += f"Web Search Results: {len(result['results'])} results found. "
                    return api_results
            except Exception as e:
                print(f"⚠️ Web search failed: {e}")

        return api_results

    def _combine_api_and_model_results(self, api_result: Dict[str, Any], model_response: str) -> str:
        """Combine API research results with model-generated response"""
        if api_result.get("content"):
            combined = f"RESEARCH DATA:\n{api_result['content']}\n\nANALYSIS:\n{model_response}"
            if api_result.get("sources"):
                combined += f"\n\nSOURCES: {', '.join(api_result['sources'])}"
            return combined
        return model_response

    def update_agent_progress(self, agent_id: int, status: str):
        """Update agent progress with thread safety"""
        with self.progress_lock:
            self.agent_progress[agent_id] = status

    def _store_research_findings(self, results: List[Dict], data_type: str):
        """Store research findings in knowledge base"""
        if 'knowledge_base' not in self.tools:
            return

        for result in results:
            if result.get("status") == "success":
                try:
                    self.tools['knowledge_base'].execute(
                        action="store",
                        data_type=data_type,
                        key=f"{data_type}_{int(time.time())}_{result['agent_id']}",
                        content=result.get("response", ""),
                        metadata={
                            "phase": result.get("phase", ""),
                            "agent_id": result.get("agent_id", 0),
                            "execution_time": result.get("execution_time", 0),
                            "model_used": result.get("model_used", "unknown"),
                            "research_query": self.research_context.get("query", "")
                        },
                        tags=[data_type, "research", result.get("phase", "")]
                    )
                    self.knowledge_base_entries.append(result)
                except Exception as e:
                    print(f"⚠️ Failed to store finding: {e}")

    # Placeholder methods for different research workflows
    def _literature_review_workflow(self, query: str, model_provider: Optional[str] = None,
                                  context_aware: bool = True) -> Dict[str, Any]:
        """Literature review workflow"""
        tasks = self._generate_literature_tasks(query)
        results = self._execute_parallel_research_with_fallback(tasks, "literature_review", model_provider, context_aware)

        return {
            "status": "success",
            "research_type": "literature_review",
            "query": query,
            "results": results,
            "execution_time": time.time() - self.research_context["start_time"]
        }

    def _gap_analysis_workflow(self, query: str, model_provider: Optional[str] = None,
                             context_aware: bool = True) -> Dict[str, Any]:
        """Gap analysis workflow"""
        tasks = self._generate_gap_analysis_tasks(query, [])
        results = self._execute_parallel_research_with_fallback(tasks, "gap_analysis", model_provider, context_aware)

        return {
            "status": "success",
            "research_type": "gap_analysis",
            "query": query,
            "results": results,
            "execution_time": time.time() - self.research_context["start_time"]
        }

    def _idea_generation_workflow(self, query: str, model_provider: Optional[str] = None,
                                context_aware: bool = True) -> Dict[str, Any]:
        """Idea generation workflow"""
        tasks = self._generate_idea_generation_tasks(query, [], [])
        results = self._execute_parallel_research_with_fallback(tasks, "idea_generation", model_provider, context_aware)

        return {
            "status": "success",
            "research_type": "idea_generation",
            "query": query,
            "results": results,
            "execution_time": time.time() - self.research_context["start_time"]
        }

    def _implementation_research_workflow(self, query: str, model_provider: Optional[str] = None,
                                        context_aware: bool = True) -> Dict[str, Any]:
        """Implementation research workflow"""
        tasks = self._generate_implementation_tasks(query, [])
        results = self._execute_parallel_research_with_fallback(tasks, "implementation", model_provider, context_aware)

        return {
            "status": "success",
            "research_type": "implementation",
            "query": query,
            "results": results,
            "execution_time": time.time() - self.research_context["start_time"]
        }

    # Task generation methods
    def _generate_literature_tasks(self, query: str) -> List[str]:
        """Generate literature review tasks"""
        return [
            f"Conduct comprehensive literature search on: {query}",
            f"Analyze recent research trends related to: {query}",
            f"Identify key researchers and institutions working on: {query}",
            f"Review methodological approaches used in: {query}",
            f"Examine theoretical frameworks for: {query}",
            f"Survey empirical findings in: {query}"
        ]

    def _generate_gap_analysis_tasks(self, query: str, literature_results: List[Dict]) -> List[str]:
        """Generate gap analysis tasks"""
        return [
            f"Identify methodological gaps in current research on: {query}",
            f"Analyze theoretical limitations in: {query}",
            f"Find empirical validation opportunities for: {query}",
            f"Examine cross-disciplinary research potential in: {query}",
            f"Assess scalability challenges in: {query}",
            f"Evaluate practical implementation gaps for: {query}"
        ]

    def _generate_idea_generation_tasks(self, query: str, literature_results: List[Dict],
                                      gap_results: List[Dict]) -> List[str]:
        """Generate idea generation tasks"""
        return [
            f"Generate novel research directions for: {query}",
            f"Propose innovative methodological approaches to: {query}",
            f"Design creative solutions for challenges in: {query}",
            f"Develop interdisciplinary research ideas for: {query}",
            f"Create breakthrough concepts related to: {query}",
            f"Formulate testable hypotheses for: {query}"
        ]

    def _generate_implementation_tasks(self, query: str, idea_results: List[Dict]) -> List[str]:
        """Generate implementation tasks"""
        return [
            f"Design implementation strategy for: {query}",
            f"Analyze technical requirements for: {query}",
            f"Evaluate resource needs for: {query}",
            f"Create development roadmap for: {query}",
            f"Assess feasibility and risks of: {query}",
            f"Plan validation and testing approach for: {query}"
        ]

    def _synthesize_comprehensive_research(self, literature_results: List[Dict], gap_results: List[Dict],
                                         idea_results: List[Dict], implementation_results: List[Dict],
                                         query: str, model_provider: Optional[str] = None) -> str:
        """Synthesize comprehensive research findings"""

        # Prepare synthesis prompt
        synthesis_prompt = f"""
        Synthesize the following comprehensive research analysis for: {query}

        LITERATURE REVIEW FINDINGS:
        {self._format_results_for_synthesis(literature_results)}

        GAP ANALYSIS FINDINGS:
        {self._format_results_for_synthesis(gap_results)}

        IDEA GENERATION FINDINGS:
        {self._format_results_for_synthesis(idea_results)}

        IMPLEMENTATION FINDINGS:
        {self._format_results_for_synthesis(implementation_results)}

        Please provide a comprehensive synthesis that integrates all findings and provides actionable insights.
        """

        # Use unified model interface for synthesis
        result = self.model_interface.generate(
            synthesis_prompt,
            provider=model_provider,
            max_tokens=8192,
            temperature=0.7
        )

        if result['status'] == 'success':
            return result['content']
        else:
            return f"Synthesis failed: {result.get('error', 'Unknown error')}. Please review individual phase results."

    def _format_results_for_synthesis(self, results: List[Dict]) -> str:
        """Format results for synthesis"""
        formatted = []
        for i, result in enumerate(results):
            if result.get("status") == "success":
                formatted.append(f"Agent {i+1}: {result.get('response', '')[:500]}...")
        return "\n".join(formatted) if formatted else "No successful results"

    # Checkpoint methods
    def create_checkpoint(self, session_data: Dict[str, Any], checkpoint_name: str = None) -> str:
        """Create a checkpoint of the current research session"""
        try:
            if not checkpoint_name:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                checkpoint_name = f"research_checkpoint_{timestamp}"

            checkpoint_path = os.path.join(self.checkpoint_dir, f"{checkpoint_name}.pkl")

            checkpoint_data = {
                "timestamp": datetime.now().isoformat(),
                "session_data": session_data,
                "research_context": self.research_context,
                "knowledge_base_entries": self.knowledge_base_entries,
                "agent_progress": self.agent_progress,
                "agent_results": self.agent_results,
                "config": self.config
            }

            with open(checkpoint_path, 'wb') as f:
                pickle.dump(checkpoint_data, f)

            if not self.silent:
                print(f"✅ Checkpoint created: {checkpoint_path}")
            return checkpoint_path

        except Exception as e:
            print(f"❌ Failed to create checkpoint: {str(e)}")
            return None

    def load_checkpoint(self, checkpoint_path: str) -> Dict[str, Any]:
        """Load a research session from checkpoint"""
        try:
            with open(checkpoint_path, 'rb') as f:
                checkpoint_data = pickle.load(f)

            # Restore session state
            self.research_context = checkpoint_data.get("research_context", {})
            self.knowledge_base_entries = checkpoint_data.get("knowledge_base_entries", [])
            self.agent_progress = checkpoint_data.get("agent_progress", {})
            self.agent_results = checkpoint_data.get("agent_results", {})

            print(f"✅ Checkpoint loaded: {checkpoint_path}")
            return checkpoint_data.get("session_data", {})

        except Exception as e:
            print(f"❌ Failed to load checkpoint: {str(e)}")
            return {}

    def auto_checkpoint_monitor(self, session_data: Dict[str, Any]):
        """Monitor and create automatic checkpoints"""
        if not self.auto_checkpoint:
            return

        last_checkpoint = time.time()

        while self.research_context.get("active", False):
            time.sleep(60)  # Check every minute

            if time.time() - last_checkpoint >= self.checkpoint_interval:
                self.create_checkpoint(session_data, f"auto_checkpoint_{int(time.time())}")
                last_checkpoint = time.time()

    def list_checkpoints(self) -> List[str]:
        """List available checkpoints"""
        try:
            checkpoints = []
            for file in os.listdir(self.checkpoint_dir):
                if file.endswith('.pkl'):
                    checkpoints.append(os.path.join(self.checkpoint_dir, file))
            return sorted(checkpoints, key=os.path.getmtime, reverse=True)
        except Exception as e:
            print(f"❌ Failed to list checkpoints: {str(e)}")
            return []

    def get_available_models(self) -> Dict[str, Any]:
        """Get available model providers and their status"""
        return self.model_interface.get_provider_status()

    def set_model_provider(self, provider: str):
        """Set the default model provider"""
        self.model_interface.set_current_provider(provider)

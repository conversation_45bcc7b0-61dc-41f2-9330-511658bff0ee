# Enhanced RAG System Dependencies
# Install with: pip install -r requirements_rag_enhanced.txt

# Core RAG Dependencies
sentence-transformers>=2.2.2
faiss-cpu>=1.7.4
numpy>=1.21.0
scikit-learn>=1.0.0

# Alternative vector stores (optional)
# faiss-gpu>=1.7.4  # Use instead of faiss-cpu for GPU acceleration
# chromadb>=0.4.0   # Alternative vector database
# pinecone-client>=2.2.0  # Cloud vector database

# Enhanced embedding models
transformers>=4.21.0
torch>=1.12.0

# Text processing
nltk>=3.7
spacy>=3.4.0
tiktoken>=0.4.0  # For OpenAI tokenization

# Document processing
pypdf2>=3.0.0
python-docx>=0.8.11
markdown>=3.4.0

# Existing dependencies (ensure compatibility)
requests>=2.28.0
beautifulsoup4>=4.11.0
lxml>=4.9.0
python-dotenv>=0.19.0

# Web interface (already in requirements_web.txt)
flask>=2.2.0
flask-socketio>=5.3.0
python-engineio==4.7.1

# API clients
openai>=1.0.0
anthropic>=0.3.0

# Utilities
tqdm>=4.64.0  # Progress bars for processing
joblib>=1.1.0  # Parallel processing
psutil>=5.9.0  # System monitoring

# Development and testing
pytest>=7.0.0
pytest-asyncio>=0.21.0

# Optional: Advanced features
# milvus>=2.3.0     # Production vector database
# elasticsearch>=8.0.0  # Alternative search backend
# redis>=4.3.0      # Caching layer

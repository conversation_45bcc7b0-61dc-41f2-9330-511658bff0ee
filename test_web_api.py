#!/usr/bin/env python3
"""
Test script for Research Heavy Web API endpoints
"""

import requests
import json
import time

BASE_URL = "http://localhost:5001"

def test_models_endpoint():
    """Test the /api/models endpoint"""
    print("🔍 Testing /api/models endpoint...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/models")
        print(f"  Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Models endpoint working")
            print(f"  Available providers: {data.get('available_providers', [])}")
            return True
        else:
            print(f"  ❌ Models endpoint failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ Models endpoint error: {e}")
        return False

def test_config_endpoint():
    """Test the /api/config endpoint"""
    print("\n🔧 Testing /api/config endpoint...")
    
    test_config = {
        "providers": {
            "gemini": {
                "api_key": "test_gemini_key",
                "model": "gemini-2.5-pro",
                "rate_limit_delay": 5.0,
                "timeout": 300
            }
        },
        "default_provider": "gemini",
        "fallback_order": ["gemini"]
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/config",
            headers={'Content-Type': 'application/json'},
            json=test_config
        )
        
        print(f"  Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Config endpoint working")
            print(f"  Response: {data}")
            return True
        else:
            print(f"  ❌ Config endpoint failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ Config endpoint error: {e}")
        return False

def test_research_endpoint():
    """Test the /api/research endpoint"""
    print("\n🔬 Testing /api/research endpoint...")
    
    test_research = {
        "query": "Test research query for API testing",
        "research_type": "literature_review",
        "model_provider": "gemini",
        "context_aware": True,
        "context_mode": "standard",
        "num_agents": 3
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/research",
            headers={'Content-Type': 'application/json'},
            json=test_research
        )
        
        print(f"  Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"  ✅ Research endpoint working")
            print(f"  Session ID: {data.get('session_id', 'None')}")
            print(f"  Message: {data.get('message', 'None')}")
            return True, data.get('session_id')
        else:
            print(f"  ❌ Research endpoint failed: {response.text}")
            return False, None
            
    except Exception as e:
        print(f"  ❌ Research endpoint error: {e}")
        return False, None

def test_knowledge_base_endpoints():
    """Test knowledge base endpoints"""
    print("\n📚 Testing knowledge base endpoints...")
    
    # Test list endpoint
    try:
        response = requests.get(f"{BASE_URL}/api/knowledge-base/list")
        print(f"  List endpoint status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"  ✅ Knowledge base list endpoint working")
        else:
            print(f"  ❌ Knowledge base list failed: {response.text}")
            
    except Exception as e:
        print(f"  ❌ Knowledge base list error: {e}")
    
    # Test search endpoint
    try:
        search_data = {
            "query": "test search",
            "search_type": "semantic_search"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/knowledge-base/search",
            headers={'Content-Type': 'application/json'},
            json=search_data
        )
        
        print(f"  Search endpoint status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"  ✅ Knowledge base search endpoint working")
            return True
        else:
            print(f"  ❌ Knowledge base search failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ Knowledge base search error: {e}")
        return False

def test_web_interface():
    """Test the main web interface"""
    print("\n🌐 Testing main web interface...")
    
    try:
        response = requests.get(BASE_URL)
        print(f"  Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"  ✅ Main web interface accessible")
            
            # Check if key elements are in the HTML
            html = response.text
            if 'Research Heavy' in html:
                print(f"  ✅ Title found in HTML")
            if 'contextMode' in html:
                print(f"  ✅ Context mode options found in HTML")
            else:
                print(f"  ❌ Context mode options missing from HTML")
            if 'Start Research' in html:
                print(f"  ✅ Start Research button found in HTML")
            
            return True
        else:
            print(f"  ❌ Main interface failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ Main interface error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 RESEARCH HEAVY WEB API TESTS")
    print("=" * 50)
    
    tests = [
        ("Web Interface", test_web_interface),
        ("Models Endpoint", test_models_endpoint),
        ("Config Endpoint", test_config_endpoint),
        ("Research Endpoint", test_research_endpoint),
        ("Knowledge Base Endpoints", test_knowledge_base_endpoints)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_name == "Research Endpoint":
                result, session_id = test_func()
                if result:
                    passed += 1
                else:
                    failed += 1
            else:
                if test_func():
                    passed += 1
                else:
                    failed += 1
        except Exception as e:
            print(f"  ❌ {test_name} crashed: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print("🎯 WEB API TEST RESULTS")
    print("=" * 50)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL WEB API TESTS PASSED!")
        print("🌐 Web interface should be fully functional at http://localhost:5001")
    else:
        print(f"\n⚠️ {failed} tests failed. Check the issues above.")

if __name__ == '__main__':
    main()

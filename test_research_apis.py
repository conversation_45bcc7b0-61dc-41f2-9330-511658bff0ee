#!/usr/bin/env python3
"""
Test script for research API integrations
Verifies that all research tools are working correctly
"""

import requests
import yaml
import os
import sys

def test_dblp_api():
    """Test DBLP API connection"""
    print("🔍 Testing DBLP API...")
    try:
        test_url = "https://dblp.org/search/publ/api?q=machine+learning&h=1&format=json"
        response = requests.get(test_url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            total = data.get('result', {}).get('hits', {}).get('@total', 0)
            print(f"✅ DBLP API connection successful - {total} papers available for 'machine learning'")
            return True
        else:
            print(f"⚠ DBLP API connection issue (status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ DBLP API test failed: {e}")
        return False

def test_github_api(token):
    """Test GitHub API connection"""
    print("🔍 Testing GitHub API...")
    try:
        headers = {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        response = requests.get('https://api.github.com/user', headers=headers, timeout=10)
        if response.status_code == 200:
            user_data = response.json()
            print(f"✅ GitHub API connection successful - User: {user_data.get('login', 'Unknown')}")
            return True
        else:
            print(f"⚠ GitHub API connection issue (status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ GitHub API test failed: {e}")
        return False

def test_gemini_api(api_key):
    """Test Gemini API connection"""
    print("🔍 Testing Gemini API...")
    try:
        import google.generativeai as genai
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-pro')
        
        # Simple test query
        response = model.generate_content("Hello, this is a test. Please respond with 'API working'.")
        if response and response.text:
            print(f"✅ Gemini API connection successful - Response: {response.text[:50]}...")
            return True
        else:
            print("⚠ Gemini API responded but no text received")
            return False
    except Exception as e:
        print(f"❌ Gemini API test failed: {e}")
        return False

def test_semantic_scholar_api(api_key):
    """Test Semantic Scholar API connection"""
    print("🔍 Testing Semantic Scholar API...")
    try:
        headers = {
            'x-api-key': api_key,
            'User-Agent': 'Research-Agent/1.0'
        }
        response = requests.get(
            'https://api.semanticscholar.org/graph/v1/paper/search?query=machine+learning&limit=1',
            headers=headers, 
            timeout=10
        )
        if response.status_code == 200:
            data = response.json()
            total = data.get('total', 0)
            print(f"✅ Semantic Scholar API connection successful - {total} papers found")
            return True
        elif response.status_code == 429:
            print("⚠ Semantic Scholar API rate limited - this is expected")
            return True  # Rate limiting is expected
        else:
            print(f"⚠ Semantic Scholar API connection issue (status: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ Semantic Scholar API test failed: {e}")
        return False

def test_knowledge_base_setup(kb_path, checkpoint_path):
    """Test knowledge base directory setup"""
    print("🔍 Testing Knowledge Base setup...")
    try:
        # Create directories if they don't exist
        os.makedirs(kb_path, exist_ok=True)
        os.makedirs(checkpoint_path, exist_ok=True)
        
        # Test write access
        test_file = os.path.join(kb_path, 'test_write.txt')
        with open(test_file, 'w') as f:
            f.write('test')
        
        # Clean up test file
        os.remove(test_file)
        
        print(f"✅ Knowledge Base setup successful")
        print(f"   📁 KB Path: {kb_path}")
        print(f"   📁 Checkpoint Path: {checkpoint_path}")
        return True
    except Exception as e:
        print(f"❌ Knowledge Base setup failed: {e}")
        return False

def test_research_tools():
    """Test research tool loading"""
    print("🔍 Testing Research Tools loading...")
    try:
        # Add the tools directory to path
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tools'))
        
        from tools import discover_tools
        
        # Load configuration
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Discover tools
        tools = discover_tools(config, silent=True)
        
        research_tools = [
            'gemini_research',
            'dblp_search', 
            'github_research',
            'knowledge_base',
            'semantic_scholar',
            'research_synthesis'
        ]
        
        loaded_tools = list(tools.keys())
        print(f"✅ Loaded {len(loaded_tools)} tools total")
        
        for tool_name in research_tools:
            if tool_name in loaded_tools:
                print(f"   ✅ {tool_name}")
            else:
                print(f"   ❌ {tool_name} - not loaded")
        
        return len([t for t in research_tools if t in loaded_tools]) >= 4
        
    except Exception as e:
        print(f"❌ Research tools test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 RESEARCH HEAVY - API Integration Test")
    print("=" * 60)
    
    # Load configuration
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
    except Exception as e:
        print(f"❌ Failed to load config.yaml: {e}")
        return
    
    test_results = []
    
    # Test DBLP (no API key required)
    test_results.append(test_dblp_api())
    
    # Test GitHub API
    if 'research_apis' in config and 'github' in config['research_apis']:
        github_token = config['research_apis']['github']['token']
        if github_token and github_token.startswith('ghp_'):
            test_results.append(test_github_api(github_token))
        else:
            print("⚠ GitHub token not configured properly")
            test_results.append(False)
    else:
        print("⚠ GitHub API not configured")
        test_results.append(False)
    
    # Test Gemini API
    if 'research_apis' in config and 'gemini' in config['research_apis']:
        gemini_key = config['research_apis']['gemini']['api_key']
        if gemini_key and gemini_key != 'YOUR_API_KEY':
            test_results.append(test_gemini_api(gemini_key))
        else:
            print("⚠ Gemini API key not configured properly")
            test_results.append(False)
    else:
        print("⚠ Gemini API not configured")
        test_results.append(False)
    
    # Test Semantic Scholar API
    if 'research_apis' in config and 'semantic_scholar' in config['research_apis']:
        ss_key = config['research_apis']['semantic_scholar']['api_key']
        if ss_key and ss_key != 'YOUR_API_KEY':
            test_results.append(test_semantic_scholar_api(ss_key))
        else:
            print("⚠ Semantic Scholar API key not configured properly")
            test_results.append(False)
    else:
        print("⚠ Semantic Scholar API not configured")
        test_results.append(False)
    
    # Test Knowledge Base
    if 'research_apis' in config and 'knowledge_base' in config['research_apis']:
        kb_path = config['research_apis']['knowledge_base']['path']
        checkpoint_path = config['research_apis']['knowledge_base']['checkpoint_path']
        test_results.append(test_knowledge_base_setup(kb_path, checkpoint_path))
    else:
        print("⚠ Knowledge Base not configured")
        test_results.append(False)
    
    # Test research tools loading
    test_results.append(test_research_tools())
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"✅ Passed: {passed}/{total} tests")
    
    if passed == total:
        print("🎉 All tests passed! Research Heavy is ready to use.")
    elif passed >= total * 0.7:
        print("⚠ Most tests passed. Some APIs may need configuration.")
    else:
        print("❌ Several tests failed. Please check your configuration.")
    
    print("\n💡 To start research orchestration, run:")
    print("   python research_heavy.py")
    print("\n📖 For regular multi-agent mode, run:")
    print("   python make_it_heavy.py")

if __name__ == "__main__":
    main()

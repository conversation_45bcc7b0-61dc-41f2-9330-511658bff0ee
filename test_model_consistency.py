#!/usr/bin/env python3
"""
Test Model Consistency Across Codebase
Verifies that only o4-mini-2025-04-16 is used throughout the system
"""

import os
import re
import yaml
from unified_model_interface import OpenAIProvider

def test_openai_provider_default():
    """Test OpenAI provider default model"""
    print("🤖 TESTING OPENAI PROVIDER DEFAULT MODEL")
    print("=" * 60)
    
    # Test with empty config to check default
    config = {'api_key': 'test_key'}
    provider = OpenAIProvider(config)
    
    print(f"📊 Default model: {provider.model}")
    
    if provider.model == 'o4-mini-2025-04-16':
        print("✅ OpenAI provider default model is correct")
        return True
    else:
        print(f"❌ OpenAI provider default model is wrong: {provider.model}")
        print(f"   Expected: o4-mini-2025-04-16")
        return False

def test_config_yaml_consistency():
    """Test config.yaml model consistency"""
    print("\n📄 TESTING CONFIG.YAML CONSISTENCY")
    print("=" * 60)
    
    try:
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        openai_config = config.get('unified_models', {}).get('providers', {}).get('openai', {})
        model = openai_config.get('model', 'Not found')
        
        print(f"📊 OpenAI model in config: {model}")
        
        if model == 'o4-mini-2025-04-16':
            print("✅ Config.yaml OpenAI model is correct")
            return True
        else:
            print(f"❌ Config.yaml OpenAI model is wrong: {model}")
            return False
            
    except Exception as e:
        print(f"❌ Error reading config.yaml: {e}")
        return False

def test_web_interface_models():
    """Test web interface model consistency"""
    print("\n🌐 TESTING WEB INTERFACE MODELS")
    print("=" * 60)
    
    try:
        with open('templates/index.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Check for old model references
        old_models = ['gpt-4o-mini', 'gpt-4o', 'o3-mini', 'gpt-4-turbo', 'o1-mini', 'o1-preview']
        found_old_models = []
        
        for old_model in old_models:
            if old_model in html_content:
                found_old_models.append(old_model)
        
        if found_old_models:
            print(f"❌ Found old models in web interface: {found_old_models}")
            return False
        
        # Check for correct model
        if 'o4-mini-2025-04-16' in html_content:
            print("✅ Web interface contains o4-mini-2025-04-16")
            
            # Count occurrences
            openai_count = html_content.count('value="o4-mini-2025-04-16"')
            openrouter_count = html_content.count('value="openai/o4-mini-2025-04-16"')
            
            print(f"   OpenAI dropdown: {openai_count} occurrence(s)")
            print(f"   OpenRouter dropdown: {openrouter_count} occurrence(s)")
            
            return True
        else:
            print("❌ o4-mini-2025-04-16 not found in web interface")
            return False
            
    except Exception as e:
        print(f"❌ Error reading web interface: {e}")
        return False

def test_terminal_interface_models():
    """Test terminal interface model consistency"""
    print("\n💻 TESTING TERMINAL INTERFACE MODELS")
    print("=" * 60)
    
    try:
        with open('real_research_terminal.py', 'r', encoding='utf-8') as f:
            terminal_content = f.read()
        
        # Check for old model references
        old_models = ['gpt-4o-mini', 'gpt-4o', 'o3-mini', 'gpt-4-turbo', 'o1-mini', 'o1-preview']
        found_old_models = []
        
        for old_model in old_models:
            if old_model in terminal_content:
                found_old_models.append(old_model)
        
        if found_old_models:
            print(f"❌ Found old models in terminal interface: {found_old_models}")
            return False
        
        # Check for correct model
        if 'o4-mini-2025-04-16' in terminal_content:
            print("✅ Terminal interface contains o4-mini-2025-04-16")
            return True
        else:
            print("❌ o4-mini-2025-04-16 not found in terminal interface")
            return False
            
    except Exception as e:
        print(f"❌ Error reading terminal interface: {e}")
        return False

def test_unified_model_interface_consistency():
    """Test unified model interface consistency"""
    print("\n🔧 TESTING UNIFIED MODEL INTERFACE CONSISTENCY")
    print("=" * 60)
    
    try:
        with open('unified_model_interface.py', 'r', encoding='utf-8') as f:
            interface_content = f.read()
        
        # Check for old model references in conditions
        old_model_patterns = [
            r"'o3-mini'",
            r"'gpt-4o-mini'",
            r"'gpt-4o'",
            r"'gpt-4-turbo'",
            r"'o1-mini'",
            r"'o1-preview'"
        ]
        
        found_old_references = []
        for pattern in old_model_patterns:
            matches = re.findall(pattern, interface_content)
            if matches:
                found_old_references.extend(matches)
        
        if found_old_references:
            print(f"❌ Found old model references: {found_old_references}")
            return False
        
        # Check for correct model handling
        if "self.model == 'o4-mini-2025-04-16'" in interface_content:
            print("✅ Unified model interface handles o4-mini-2025-04-16 correctly")
            return True
        else:
            print("❌ o4-mini-2025-04-16 handling not found in unified model interface")
            return False
            
    except Exception as e:
        print(f"❌ Error reading unified model interface: {e}")
        return False

def test_optimal_parameters_configuration():
    """Test that optimal parameters are configured for o4-mini-2025-04-16"""
    print("\n⚙️ TESTING OPTIMAL PARAMETERS CONFIGURATION")
    print("=" * 60)
    
    try:
        with open('unified_model_interface.py', 'r', encoding='utf-8') as f:
            interface_content = f.read()
        
        # Check for key optimal parameters
        required_params = [
            "'temperature': 1.1",
            "'reasoning_effort': 'high'",
            "'top_p': 0.9",
            "kwargs.get('max_tokens', 1200)"
        ]
        
        found_params = []
        missing_params = []
        
        for param in required_params:
            if param in interface_content:
                found_params.append(param)
            else:
                missing_params.append(param)
        
        print(f"✅ Found parameters: {len(found_params)}/{len(required_params)}")
        for param in found_params:
            print(f"   ✓ {param}")
        
        if missing_params:
            print(f"❌ Missing parameters:")
            for param in missing_params:
                print(f"   ✗ {param}")
            return False
        
        print("✅ All optimal parameters configured correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error checking optimal parameters: {e}")
        return False

def main():
    """Run all model consistency tests"""
    print("🧪 MODEL CONSISTENCY VERIFICATION SUITE")
    print("=" * 70)
    print("Verifying: Default Model, Config Files, Web Interface, Terminal, Parameters")
    print("=" * 70)
    
    tests = [
        ("OpenAI Provider Default Model", test_openai_provider_default),
        ("Config.yaml Consistency", test_config_yaml_consistency),
        ("Web Interface Models", test_web_interface_models),
        ("Terminal Interface Models", test_terminal_interface_models),
        ("Unified Model Interface Consistency", test_unified_model_interface_consistency),
        ("Optimal Parameters Configuration", test_optimal_parameters_configuration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name}...")
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} CRASHED: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 70)
    print("🎯 MODEL CONSISTENCY TEST RESULTS")
    print("=" * 70)
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 COMPLETE MODEL CONSISTENCY ACHIEVED!")
        print("✅ OpenAI provider default: o4-mini-2025-04-16")
        print("✅ Config files: Consistent")
        print("✅ Web interface: Only o4-mini-2025-04-16")
        print("✅ Terminal interface: Only o4-mini-2025-04-16")
        print("✅ Model handling: Optimized")
        print("✅ Parameters: temperature=1.1, reasoning_effort=high")
        print("\n🚀 System fully optimized for o4-mini-2025-04-16!")
    else:
        print(f"\n⚠️ {failed} consistency issues found. Review the issues above.")

if __name__ == '__main__':
    main()

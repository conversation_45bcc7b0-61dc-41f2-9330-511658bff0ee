#!/usr/bin/env python3
"""
Test Real Research Execution
Demonstrates that the research system actually works with real API calls
"""

import time
from enhanced_research_orchestrator import EnhancedResearchOrchestrator

def test_real_research():
    """Test real research execution"""
    print("🧪 TESTING REAL RESEARCH EXECUTION")
    print("=" * 60)
    
    # Initialize orchestrator
    print("🔧 Initializing orchestrator...")
    orchestrator = EnhancedResearchOrchestrator(silent=False)
    
    # Check available providers
    available_providers = orchestrator.model_interface.get_available_providers()
    print(f"🟢 Available providers: {available_providers}")
    
    if not available_providers:
        print("❌ No providers available. Please configure API keys.")
        return
    
    # Test simple literature review
    print(f"\n🔬 Starting real literature review...")
    
    start_time = time.time()
    
    result = orchestrator.orchestrate_research(
        research_query="Machine learning for text classification",
        research_type="literature_review",
        model_provider=available_providers[0],  # Use first available provider
        context_aware=True,
        context_mode="standard",
        num_agents=2  # Use fewer agents for faster testing
    )
    
    execution_time = time.time() - start_time
    
    print(f"\n🎯 RESEARCH RESULTS")
    print("=" * 60)
    print(f"⏱️ Execution Time: {execution_time:.1f}s")
    print(f"📊 Status: {result.get('status', 'unknown')}")
    print(f"🤖 Model Provider: {result.get('model_provider', 'unknown')}")
    print(f"👥 Number of Agents: {result.get('num_agents', 'unknown')}")
    
    if result.get('status') == 'success':
        # Show phase results
        phases = result.get('phases', {})
        for phase_name, phase_results in phases.items():
            print(f"\n📋 {phase_name.upper()}:")
            print("-" * 40)
            
            if isinstance(phase_results, list):
                for i, agent_result in enumerate(phase_results, 1):
                    status = agent_result.get('status', 'unknown')
                    model_used = agent_result.get('model_used', 'unknown')
                    exec_time = agent_result.get('execution_time', 0)
                    
                    print(f"  Agent {i}: {status} ({model_used}, {exec_time:.1f}s)")
                    
                    if status == 'success':
                        response = agent_result.get('response', '')
                        # Show first 150 characters
                        preview = response[:150] + "..." if len(response) > 150 else response
                        print(f"    Response: {preview}")
                    else:
                        print(f"    Error: {agent_result.get('response', 'Unknown error')}")
        
        # Show synthesis
        synthesis = result.get('final_synthesis', '')
        if synthesis and synthesis != "No synthesis available":
            print(f"\n🧠 SYNTHESIS PREVIEW:")
            print("-" * 40)
            # Show first 300 characters of synthesis
            preview = synthesis[:300] + "..." if len(synthesis) > 300 else synthesis
            print(preview)
        else:
            print(f"\n⚠️ No synthesis generated")
        
        print(f"\n✅ REAL RESEARCH EXECUTION SUCCESSFUL!")
        print(f"   - Actual API calls made: ✅")
        print(f"   - Real model responses: ✅") 
        print(f"   - Synthesis generated: {'✅' if synthesis else '❌'}")
        
    else:
        print(f"❌ Research failed: {result.get('error', 'Unknown error')}")
    
    return result

if __name__ == '__main__':
    test_real_research()

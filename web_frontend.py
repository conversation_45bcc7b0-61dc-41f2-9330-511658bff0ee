#!/usr/bin/env python3
"""
Research Heavy Web Frontend
Advanced web interface for multi-agent research orchestration
"""

import os
import json
import time
import asyncio
import threading
from datetime import datetime
from typing import Dict, Any, List, Optional
from flask import Flask, render_template, request, jsonify, session, send_file
from flask_socketio import Socket<PERSON>, emit
from werkzeug.utils import secure_filename
import yaml

from enhanced_research_orchestrator import EnhancedResearchOrchestrator

app = Flask(__name__)
app.config['SECRET_KEY'] = 'research_heavy_secret_key_2024'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

socketio = SocketIO(app, cors_allowed_origins="*")

# Global orchestrator instance
orchestrator = None
current_research_session = None

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

@app.route('/')
def index():
    """Main research interface"""
    return render_template('index.html')

@app.route('/api/models', methods=['GET'])
def get_available_models():
    """Get available model providers and their status"""
    global orchestrator
    if not orchestrator:
        orchestrator = EnhancedResearchOrchestrator()
    
    models = orchestrator.get_available_models()
    return jsonify({
        'status': 'success',
        'models': models,
        'available_providers': orchestrator.model_interface.get_available_providers()
    })

@app.route('/api/config', methods=['POST'])
def update_config():
    """Update API configuration"""
    try:
        config_data = request.json
        print(f"🔧 Received config data: {config_data}")
        
        # Update config file
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        # Update unified models configuration
        if 'unified_models' not in config:
            config['unified_models'] = {'providers': {}, 'default_provider': 'gemini', 'fallback_order': []}

        # Update provider configurations
        for provider, provider_config in config_data.get('providers', {}).items():
            if provider_config.get('api_key') and provider_config['api_key'].strip():
                config['unified_models']['providers'][provider] = provider_config

        # Set default provider and fallback order
        config['unified_models']['default_provider'] = config_data.get('default_provider', 'gemini')
        config['unified_models']['fallback_order'] = config_data.get('fallback_order', ['gemini', 'openrouter', 'openai', 'anthropic', 'moonshot'])
        
        # Save updated config
        with open('config.yaml', 'w') as f:
            yaml.dump(config, f, default_flow_style=False)

        print("✅ Config file updated successfully")

        # Reinitialize orchestrator with new config
        global orchestrator
        orchestrator = EnhancedResearchOrchestrator()

        print("✅ Orchestrator reinitialized")

        return jsonify({'status': 'success', 'message': 'Configuration updated successfully'})
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/research', methods=['POST'])
def start_research():
    """Start a research session"""
    try:
        data = request.json
        print(f"🔬 Received research data: {data}")
        query = data.get('query', '')
        research_type = data.get('research_type', 'comprehensive')
        model_provider = data.get('model_provider')
        context_aware = data.get('context_aware', True)
        context_mode = data.get('context_mode', 'standard')
        num_agents = data.get('num_agents', 6)

        # Advanced options
        max_tokens = data.get('max_tokens', 4096)
        temperature = data.get('temperature', 0.7)
        api_intensity = data.get('api_intensity', 'standard')
        tools_config = data.get('tools_config', {})
        
        if not query:
            return jsonify({'status': 'error', 'message': 'Query is required'})
        
        # Initialize orchestrator if needed
        global orchestrator, current_research_session
        if not orchestrator:
            orchestrator = EnhancedResearchOrchestrator()
        
        # Create session ID
        session_id = f"research_{int(time.time())}"
        session['research_session_id'] = session_id
        
        # Start research in background thread
        def run_research():
            global current_research_session
            current_research_session = {
                'session_id': session_id,
                'status': 'running',
                'start_time': time.time(),
                'query': query,
                'research_type': research_type,
                'model_provider': model_provider,
                'context_aware': context_aware,
                'context_mode': context_mode,
                'num_agents': num_agents,
                'progress': {},
                'results': None
            }
            
            try:
                # Emit start event
                socketio.emit('research_started', {
                    'session_id': session_id,
                    'query': query,
                    'research_type': research_type,
                    'num_agents': num_agents
                })
                
                # Run research with real execution
                print(f"🔬 Starting real research execution...")
                print(f"   Query: {query}")
                print(f"   Type: {research_type}")
                print(f"   Provider: {model_provider}")
                print(f"   Agents: {num_agents}")

                results = orchestrator.orchestrate_research(
                    query, research_type, model_provider, context_aware, context_mode, num_agents,
                    max_tokens, temperature, api_intensity, tools_config
                )

                print(f"🎯 Research completed with status: {results.get('status', 'unknown')}")
                
                current_research_session['status'] = 'completed'
                current_research_session['results'] = results
                current_research_session['end_time'] = time.time()
                
                # Emit completion event
                socketio.emit('research_completed', {
                    'session_id': session_id,
                    'results': results,
                    'execution_time': results.get('execution_time', 0)
                })
                
            except Exception as e:
                current_research_session['status'] = 'error'
                current_research_session['error'] = str(e)
                
                socketio.emit('research_error', {
                    'session_id': session_id,
                    'error': str(e)
                })
        
        # Start research thread
        research_thread = threading.Thread(target=run_research, daemon=True)
        research_thread.start()
        
        return jsonify({
            'status': 'success',
            'session_id': session_id,
            'message': 'Research started successfully'
        })
        
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/research/status/<session_id>', methods=['GET'])
def get_research_status(session_id):
    """Get research session status"""
    global current_research_session, orchestrator
    
    if current_research_session and current_research_session['session_id'] == session_id:
        status_data = {
            'session_id': session_id,
            'status': current_research_session['status'],
            'query': current_research_session['query'],
            'research_type': current_research_session['research_type'],
            'start_time': current_research_session['start_time'],
            'execution_time': time.time() - current_research_session['start_time']
        }
        
        # Add agent progress if orchestrator is available
        if orchestrator:
            status_data['agent_progress'] = orchestrator.agent_progress
        
        # Add results if completed
        if current_research_session['status'] == 'completed':
            status_data['results'] = current_research_session['results']
        elif current_research_session['status'] == 'error':
            status_data['error'] = current_research_session.get('error', 'Unknown error')
        
        return jsonify(status_data)
    
    return jsonify({'status': 'error', 'message': 'Session not found'})

@app.route('/api/research/export/<session_id>/<format>', methods=['GET'])
def export_research(session_id, format):
    """Export research results in various formats"""
    global current_research_session
    
    if not current_research_session or current_research_session['session_id'] != session_id:
        return jsonify({'status': 'error', 'message': 'Session not found'})
    
    if current_research_session['status'] != 'completed':
        return jsonify({'status': 'error', 'message': 'Research not completed'})
    
    results = current_research_session['results']
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    try:
        if format == 'json':
            filename = f"research_results_{timestamp}.json"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            return send_file(filepath, as_attachment=True, download_name=filename)
        
        elif format == 'txt':
            filename = f"research_results_{timestamp}.txt"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"RESEARCH RESULTS\n")
                f.write(f"================\n")
                f.write(f"Query: {results.get('query', 'N/A')}\n")
                f.write(f"Type: {results.get('research_type', 'N/A')}\n")
                f.write(f"Execution Time: {results.get('execution_time', 0):.1f}s\n")
                f.write(f"Model Provider: {results.get('model_provider', 'N/A')}\n")
                f.write(f"Number of Agents: {results.get('num_agents', 'N/A')}\n\n")
                
                # Write phase results
                phases = results.get('phases', {})
                for phase_name, phase_results in phases.items():
                    f.write(f"{phase_name.upper().replace('_', ' ')}:\n")
                    f.write("-" * 50 + "\n")
                    if isinstance(phase_results, list):
                        for i, agent_result in enumerate(phase_results, 1):
                            f.write(f"\nAgent {i} ({agent_result.get('status', 'unknown')}):\n")
                            f.write(agent_result.get('response', 'No response') + "\n")
                    f.write("\n")
                
                # Write final synthesis
                f.write("FINAL SYNTHESIS:\n")
                f.write("=" * 50 + "\n")
                f.write(results.get('final_synthesis', 'No synthesis available'))
            
            return send_file(filepath, as_attachment=True, download_name=filename)
        
        else:
            return jsonify({'status': 'error', 'message': 'Unsupported format'})
    
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/knowledge-base/upload', methods=['POST'])
def upload_document():
    """Upload document to knowledge base"""
    try:
        if 'file' not in request.files:
            return jsonify({'status': 'error', 'message': 'No file provided'})
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'status': 'error', 'message': 'No file selected'})
        
        # Secure filename
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        
        # Save file
        file.save(filepath)
        
        # Initialize orchestrator if needed
        global orchestrator
        if not orchestrator:
            orchestrator = EnhancedResearchOrchestrator()
        
        # Upload to knowledge base
        if 'knowledge_base' in orchestrator.tools:
            result = orchestrator.tools['knowledge_base'].execute(
                action="upload_document",
                content=filepath,
                data_type="document"
            )
            
            # Clean up uploaded file
            os.remove(filepath)
            
            return jsonify(result)
        else:
            return jsonify({'status': 'error', 'message': 'Knowledge base tool not available'})
    
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/knowledge-base/search', methods=['POST'])
def search_knowledge_base():
    """Search knowledge base"""
    try:
        data = request.json
        query = data.get('query', '')
        search_type = data.get('search_type', 'semantic_search')
        
        if not query:
            return jsonify({'status': 'error', 'message': 'Query is required'})
        
        # Initialize orchestrator if needed
        global orchestrator
        if not orchestrator:
            orchestrator = EnhancedResearchOrchestrator()
        
        if 'knowledge_base' in orchestrator.tools:
            result = orchestrator.tools['knowledge_base'].execute(
                action=search_type,
                query=query
            )
            return jsonify(result)
        else:
            return jsonify({'status': 'error', 'message': 'Knowledge base tool not available'})
    
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/knowledge-base/list', methods=['GET'])
def list_knowledge_base():
    """List knowledge base entries"""
    try:
        global orchestrator
        if not orchestrator:
            orchestrator = EnhancedResearchOrchestrator()
        
        if 'knowledge_base' in orchestrator.tools:
            result = orchestrator.tools['knowledge_base'].execute(action="list")
            return jsonify(result)
        else:
            return jsonify({'status': 'error', 'message': 'Knowledge base tool not available'})
    
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    emit('connected', {'message': 'Connected to Research Heavy'})

@socketio.on('get_agent_progress')
def handle_get_agent_progress():
    """Send current agent progress to client"""
    global orchestrator
    if orchestrator:
        emit('agent_progress_update', {
            'progress': orchestrator.agent_progress,
            'timestamp': time.time()
        })

# Background task to send progress updates
def background_progress_updates():
    """Send periodic progress updates to connected clients"""
    while True:
        time.sleep(2)  # Update every 2 seconds
        global orchestrator, current_research_session
        
        if orchestrator and current_research_session and current_research_session['status'] == 'running':
            socketio.emit('agent_progress_update', {
                'session_id': current_research_session['session_id'],
                'progress': orchestrator.agent_progress,
                'timestamp': time.time()
            })

if __name__ == '__main__':
    # Start background progress updates
    progress_thread = threading.Thread(target=background_progress_updates, daemon=True)
    progress_thread.start()
    
    # Run the app
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)

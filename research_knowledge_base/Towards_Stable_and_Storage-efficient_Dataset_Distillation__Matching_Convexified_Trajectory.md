# Towards Stable and Storage-efficient Dataset Distillation: Matching Convexified Trajectory

Wen<PERSON>g <PERSON>hong School of software Shandong University

Haoyu Tang <sup>∗</sup> School of software Shandong University

Qinghai Zheng College of Software Fuzhou University

Mingzhu Xu School of software Shandong University

Yupeng Hu School of software Shandong University

Liqiang Nie School of Computer Science Harbin Institute of Technology (Shenzhen)

#### Abstract

The rapid evolution of deep learning and large language models has led to an exponential growth in the demand for training data, prompting the development of Dataset Distillation methods to address the challenges of managing large datasets. Among these, Matching Training Trajectories (MTT) has been a prominent approach, which replicates the training trajectory of an expert network on real data with a synthetic dataset. However, our investigation found that this method suffers from three significant limitations: 1. Instability of expert trajectory generated by Stochastic Gradient Descent (SGD); 2. Low convergence speed of the distillation process; 3. High storage consumption of the expert trajectory. To address these issues, we offer a new perspective on understanding the essence of Dataset Distillation and MTT through a simple transformation of the objective function, and introduce a novel method called Matching Convexified Trajectory (MCT), which aims to provide better guidance for the student trajectory. MCT leverages insights from the linearized dynamics of Neural Tangent Kernel methods to create a convex combination of expert trajectories, guiding the student network to converge rapidly and stably. This trajectory is not only easier to store, but also enables a continuous sampling strategy during distillation, ensuring thorough learning and fitting of the entire expert trajectory. Comprehensive experiments across three public datasets validate the superiority of MCT over traditional MTT methods.

## 1 Introduction

The advancement of deep learning has catalyzed an exponential surge in the requisite volume of training data [\[Wang et al., 2022\]](#page-10-0). With the emergence of Large Language Models (LLMs), there has been a corresponding rise in model complexity, further intensifying the demand for extensive datasets to facilitate the training of these intricate models. However, collecting and managing large datasets presents significant challenges, including storage requirements, computational load, privacy concerns,

<sup>∗</sup>Tang Haoyu is the corresponding author; email: <EMAIL>, <EMAIL>

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image contains two plots. The plot on the left is a 3D scatter plot showing two trajectories: MTT Trajectory (green dots) and Convexified Trajectory (blue dots). The axes are labeled x, y, and t. The plot on the right is a bar chart comparing the iteration counts (in K) for different settings. The x-axis shows 'ipc=1', 'ipc=10', and 'ipc=50'. The bars represent 'MTT' (orange), 'Ours' (teal), 'CIFAR-10' (gray, not visible), and 'CIFAR-100' (hatched orange and teal). For 'ipc=1', MTT takes approximately 16K iterations, and Ours takes approximately 7K iterations. For 'ipc=10', MTT takes approximately 23K iterations, and Ours takes approximately 11K iterations. For 'ipc=50', MTT takes approximately 23K iterations, and Ours takes approximately 7K iterations. The hatched bars for CIFAR-100 at 'ipc=1' and 'ipc=10' are approximately 23K and 24K respectively, while the hatched bars for CIFAR-10 at 'ipc=1' and 'ipc=10' are approximately 12K and 13K respectively. For 'ipc=50', the hatched bars for CIFAR-100 and CIFAR-10 are approximately 2K and 1.5K respectively.

(a) Visualization of the expert trajectory. (b) Illustration of Convergence Speed

Figure 1: (a): PCA projection of all waypoints model in the expert trajectory, where z-axis represents the value of  $(1 - \nu)$  validation accuracy); (b): The required iteration number to convergence for both the MCT and MTT methods during distillation. The convergence is defined by the condition where the difference between the accuracy at any iteration and the maximum accuracy is less than  $\epsilon = 2\%$ .

and the costs of data labeling. To mitigate these challenges, Dataset Distillation (DD) has emerged as a compelling strategy [\[Wang et al., 2018\]](#page-10-1). DD endeavors to distill the essence of a large, real-world dataset into a more compact, synthetic dataset that can train models with comparable efficacy.

In the landscape of DD methods, Matching Training Trajectories has emerged as a prominent approach. The MTT method aims to generate a synthetic dataset that guidea the learning trajectory of the student network to approximate the expert trajectory of this network on real data. However, upon closer examination, we identify several limitations inherent in traditional MTT approaches:

1. Instability of expert trajectory: As shown in Figure [1a,](#page-1-0) the validation accuracy of the expert network on the MTT trajectory exhibits oscillations. Matching the trajectory locally in each iteration will lead to the similar oscillation in the trajectory of the synthetic data, thereby impeding robust distillation.

2. Low Convergence Speed: The learning process for the expert trajectory is often slow. As in Figure [1b,](#page-1-0) a considerable number of distillation iterations are required to generate a synthetic dataset capable of achieving satisfactory test accuracy, resulting in time-consuming procedures.

3. High Storage Consumption: During the distillation process, the conventional MTT approach necessitates the storage of model weights along all timesteps, which is particularly burdensome in terms of storage (about 50 models should be stored). This high storage consumption is a significant limitation for applying existing DD methods to small-scale models.

Through careful observation, we have reformulated the loss function of MTT, and introduced a novel perspective to interpret the essence of DD and MTT: obtaining a synthetic dataset that offers accurate guidance regarding the magnitude and direction of the next update for any given point in the parameter space of the student model, with this guidance determined by the expert trajectory's update vector at that point. From this perspective, those three limitations can be easily addressed: to find an optimized expert trajectory that can guide the model to stably converge at each iteration, which is also easy to fit and simple to save.

How to find such a trajectory? Drawing inspiration from linearized dynamics of Neural Tangent Kernel (NTK) method [\[Arora et al., 2019,](#page-9-0) [Jacot et al., 2018\]](#page-9-1), we present a simple yet novel Matching Convexified Trajectory (MCT) method. The MCT method creates a convex combination (linear) expert trajectory based on the network's training process real data. This trajectory, which starts from a random initialization model and points directly towards the optimal model point, facilitates stable and rapid convergence of the distillation. Moreover, recovering this trajectory only needs storing two models and a set of constants. Distinct from the MTT method, the convexified trajectory also permits a "continuous sampling" strategy during the distillation, ensuring comprehensive learning and fitting of the expert trajectory.

The contributions of this paper are as follows: 1) We highlight the three limitations of traditional MTT methods, and offer a novel perspective for understanding the objective of DD through a simple reformulation of MTT's loss function. 2) We propose the MCT method, which creates an easy-tostore convexified expert trajectory with a continuous sampling strategy to enable rapid and stable distillation. 3) Comprehensive experiments on three datasets have verified the superiority of our MCT and the effectiveness of the continuous sampling strategy.

## 2 Preliminaries and Related Work

#### 2.1 Preliminaries

We first formally define the dataset distillation task. A large scale real dataset  $\mathcal{T} = \{ (x_{\mathcal{T}}^{(i)} \}$  $\overset{(i)}{\tau}, \overset{(j)}{y^{\left(i\right)}}$  $(\overset{(i)}{\tau})\}_{i=1}^{|\mathcal{T}|}$  $i=1$ is first provided, where  $x_{\mathcal{T}}^{(i)} \in \mathbb{R}^d$  and  $y_{\mathcal{T}}^{(i)} \in \mathcal{Y} = \{1, 2, ..., C\}$  are the *i*-th instance and the corresponding label. C denotes the number of classes. The core idea of this task is to learn a tiny synthetic dataset  $S = \{ (x_S^{(i)} \}$  $\overset{(i)}{\mathcal{S}}, \overset{(i)}{\mathcal{Y}}$  $\{S_{\mathcal{S}}^{(i)}\}_{i=1}^{|\mathcal{S}|}$  from the original dataset T, where  $x_{\mathcal{S}}^{(i)} \in \mathbb{R}^d$  and  $y_{\mathcal{S}_{\mathcal{S}}}^{(i)} \in \mathcal{Y}$ . Typically, *ipc* instances are crafted for each class, culminating in a total count for S of  $|\mathcal{S}| = C * ipc$ . It is always expected that  $|S| \ll |\mathcal{T}|$ , while S still preserves the majority of the pivotal information in  $\mathcal T$ . Consequently, a model trained on S should achieve performance comparable to the model trained with the original dataset  $\mathcal T$  under the real data distribution  $\mathcal P_D$ . Formally, the optimization of DD task can be formulated as:

$$
\arg\min_{\mathcal{S}} \mathcal{L}(\mathcal{S}, \mathcal{T}),\tag{1}
$$

where  $\mathcal L$  is the certain objective function, which may differ from different DD methods.

#### 2.2 Dataset Distillation Methods.

The field of DD contains four principal approaches. *a. Meta-model Matching methods* [\[Wang](#page-10-1) [et al., 2018,](#page-10-1) [Zhou et al., 2022,](#page-10-2) [Nguyen et al., 2021,](#page-10-3) [Loo et al., 2022\]](#page-10-4) involve a bi-level optimization algorithm where the inner loop updates the weights of a differentiable model using gradient descent on a synthetic dataset while caching recursive computation graphs, and the outer loop validates models trained in the inner loops on a real dataset, back-propagating the validation loss through the unrolled computation graph to the synthetic dataset. *b. Distribution Matching methods* [\[Zhao](#page-10-5) [and Bilen, 2023,](#page-10-5) [Wang et al., 2022\]](#page-10-0) align synthetic and real data by optimizing within a set of embedding spaces using maximum mean discrepancy. However, inaccurate estimation of the data distribution often results in suboptimal performance. *c. Single-step Gradient Matching methods* [\[Zhao et al., 2020,](#page-10-6) [Zhao and Bilen, 2021\]](#page-10-7) aim to align the gradient of the synthetic dataset with that of the real dataset during each training step. To enhance generalization with improved gradients, recent research efforts have focused on further optimizing the gradient matching objective by incorporating class-related information [\[Lee et al., 2022,](#page-10-8) [Jiang et al., 2023\]](#page-9-2). *d. Multi-step Trajectory Matching methods* [\[Cazenavette et al., 2022,](#page-9-3) [Guo et al., 2023\]](#page-9-4) address the accumulated trajectory errors of single-step methods by matching the multi-step training trajectories of models separately trained on synthetic and real datasets.

Our research primarily focuses on multi-step trajectory matching methods. The first method in this branch is MTT [\[Cazenavette et al., 2022\]](#page-9-3). Based on MTT, [Du et al.](#page-9-5) [\[2023\]](#page-9-5) presented to incorporate the random noise to the initialized model weights to mitigate accumulated trajectory errors, and [Cui et al.](#page-9-6) [\[2023\]](#page-9-6) proposed to decompose the objective function of MTT to improve computational efficiency and reduce GPU memory without performance degradation. Further research has explored the robustness of the synthesized dataset [\[Guo et al., 2023,](#page-9-4) [Li et al., 2022b,](#page-10-9) [Du et al., 2024\]](#page-9-7) and applied this technique to downstream tasks [\[Li et al., 2022a,](#page-10-10) [2020\]](#page-10-11).

Despite their successes, none of these approaches address the detriment of oscillations in the MTT expert trajectory on the stability and convergence speed of the distillation process. Furthermore, the necessity to retain all waypoint networks along the expert trajectory has yet to be addressed.

### 3 Motivation

#### 3.1 Review of Multi-step Trajectory Matching

In this section, we first review the multi-step trajectory matching methods. The essence of them is to minimize the discrepancy of the student training trajectory of  $S$  and the expert training trajectory of  $\mathcal T$ . Here we take MTT [\[Cazenavette et al., 2022\]](#page-9-3) as an example. Firstly, an expert trajectory  $\tau_{\rm mtt} \,=\, \{ \theta^{(t)}_{\cal T}$  $\mathcal{T}^{(t)}$   $[0 \le t \le K]$  is generated by training a randomly initialized model  $\theta_{\mathcal{T}}^{(0)}$  $\sigma$  on the real dataset  $\mathcal T$  with K timesteps. Afterward, MTT matches the student trajectory with the expert  $\tau_{\text{mtt}}$ through massive iterations. During each iteration, MTT samples a random timestep  $\theta_{\mathcal{T}}^{(t)}$  and captures  $\tau$ the target timestep  $\theta_{\mathcal{T}}^{(t+M)}$  $\mathcal{T}^{(t+M)}$  after M steps from  $\tau_{\text{mtt}}$ . Meanwhile,  $\theta_{\mathcal{T}}^{(t)}$  $\sigma$ <sup>(*l*)</sup> is also trained on synthetic dataset S for N steps to get the updated student parameters  $\theta_S^{(t+N)}$  $S^{(t+N)}$ . Formally, the objective is to minimize the normalized squared  $L_2$  error between the updated student parameters  $\theta_{\mathcal{S}}^{(t+N)}$  $\mathcal{S}^{(k+N)}$  and the future expert (target) parameters  $\theta_{\mathcal{T}}^{(t+M)}$  $\mathcal{T}^{(t+M)}$ :

<span id="page-3-0"></span>
$$
\mathcal{L}(\mathcal{S}, \mathcal{T}) = \frac{\|\theta_{\mathcal{S}}^{(t+N)} - \theta_{\mathcal{T}}^{(t+M)}\|_{2}^{2}}{\|\theta_{\mathcal{T}}^{(t)} - \theta_{\mathcal{T}}^{(t+M)}\|_{2}^{2}} \tag{2}
$$

$$
\theta_S^{(t+1)} = \theta_S^{(t)} - \alpha_S \nabla \ell(S; \theta_S^{(t)})
$$
\n(3)

$$
\theta_{\mathcal{T}}^{(t+1)} = \theta_{\mathcal{T}}^{(t)} - \alpha_{\mathcal{T}} \nabla \ell(\mathcal{T}; \theta_{\mathcal{T}}^{(t)}),\tag{4}
$$

where  $\theta_{\mathcal{T}}^{(t)} = \theta_{\mathcal{S}}^{(t)}$  $\mathcal{S}^{(t)}$ .  $\ell$  is the loss function for model training, where the cross-entropy loss is often adopted, and  $\alpha_S$  and  $\alpha_T$  are the learning rates for training on the synthetic and real datasets, respectively. To ensure generalization, MTT usually performs the above trajectory matching process on a large number of expert trajectories from different  $\theta_{\mathcal{T}}^{(0)}$  $\sigma^{\text{(0)}}$ . Although the subsequent methods have focused on optimizing model parameters [\[Du et al., 2023,](#page-9-5) [2024\]](#page-9-7) and objective functions [\[Cui et al.,](#page-9-6) [2023\]](#page-9-6), the overall process remains roughly the same as MTT.

### 3.2 Motivation: A New Perspective to Optimize the Trajectory

Through a lot of preliminary experiments and visualizations, we found that the MTT method have three serious shortcomings: 1. *Instability of the expert trajectory generated by mini-batch SGD:* The expert trajectory  $\tau_{\text{mut}}$  trained on  $\tau$  exhibits erratic oscillations instead of following a path where the loss steadily decreases, so the accuracy of the waypoint model  $\theta_{\mathcal{T}}^{(t)}$  $\tau^{(t)}$  is subject to fluctuations. This problem complicates the student network to learn better training dynamics with synthetic data. 2. *Low convergence speed of the distillation process:* When learning expert trajectories, a very large number of iterations are required to obtain a synthetic dataset that can achieve good validation accuracy, which is very time-consuming. 3. *High storage consumption of the expert trajectory:* To expedite the distillation process, the expert trajectories are pre-generated and stored in memory as trajectory buffers. These trajectories serve as sources from which the initial point  $\theta_{\tau}^{(t)}$  $\sigma^{(t)}$  and target parameters  $\theta^{(t+M)}_{\mathcal{T}}$  $\tau^{(t+M)}$  are extracted. However, the necessity to store all waypoints for each expert trajectory incurs a substantial storage footprint.

To better explain the internalization of these drawbacks, we propose a novel perspective to view the dataset distillation and explain the essence of the MTT approach: The objective of DD task can be regarded as obtaining a set of parameters (*i.e.*, the synthetic dataset S) that enables accurate prediction of how far (magnitude) and where (direction) to step next for any given network parameters  $\theta$  (*i.e.*, provides appropriate guidance  $\vec{V}_{\mathcal{S}}$  to update the current network parameters  $\theta$ ). From this perspective, each distillation iteration of the MTT method can be viewed as updating the synthetic dataset  $S$ to provide the network update guidance  $\vec{V}_{\mathcal{S}} = ||\theta_{\mathcal{S}}^{(t+N)} - \theta_{\mathcal{T}}^{(t)}||$  $\mathcal{T}^{(t)}$  ||2 of *N*-step SGD training on *S*, which aligns closer to the M-step SGD guidance  $\vec{V}_{\mathcal{T}} = \|\theta_{\mathcal{T}}^{(t+M)} - \theta_{\mathcal{T}}^{(t)}\|$  $\mathcal{T}^{(t)}$  ||2 obtained from the expert trajectory, given an arbitrary initialized point  $\theta_{\mathcal{T}}^{(t)}$  $\tau$ . A simple reformulation of Equ. [2](#page-3-0) yields the same result:

$$
\min_{\mathcal{S}} \mathcal{L}(\mathcal{S}, \mathcal{T}) = \min_{\mathcal{S}} \mathbb{E}_{\theta_{\mathcal{T}}^{(t)} \sim \tau_{\text{mut}}}} \frac{\|(\theta_{\mathcal{S}}^{(t+N)} - \theta_{\mathcal{T}}^{(t)}) - (\theta_{\mathcal{T}}^{(t+M)} - \theta_{\mathcal{T}}^{(t)})\|_2^2}{\|\theta_{\mathcal{T}}^{(t)} - \theta_{\mathcal{T}}^{(t+M)}\_2^2} = \min_{\mathcal{S}} \mathbb{E}_{\theta_{\mathcal{T}}^{(t)} \sim \tau_{\text{mut}}}} \frac{\left\|\vec{V}_{\mathcal{S}} - \vec{V}_{\mathcal{T}}\right\|_2^2}{\left\|\vec{V}_{\mathcal{T}}\right\|_2^2}
$$
(5)

Thereafter, we can regard all the waypoints of  $\tau_{\text{mut}}$  as the training "dataset" to optimize  $\vec{V}_{\mathcal{S}}$ , *i.e.*,  $\{(\theta^{(t)}_{\cal T}$  $(\overset{(t)}{\mathcal{T}}, \vec{V}_\mathcal{T}^{(t)}$  $(\mathcal{F}^{(t)}_{\mathcal{T}})|\theta^{(t)}_{\mathcal{T}} \in \tau_{\text{mtt}}, 0 \leq t \leq K\},$  where  $\vec{V}_{\mathcal{T}}^{(t)}$  $\mathcal{F}_{\tau}^{(t)}$  denotes the "label" of  $\theta_{\tau}^{(t)}$  $\mathcal{T}^{(t)}$ . From this perspective, the first two drawbacks can be easily explained: Given that the models on the expert trajectory

Image /page/4/Figure/0 description: The image displays two plots side-by-side. The left plot shows contour lines representing a loss landscape, with various trajectories plotted on it. The trajectories are indicated by different colored arrows and markers: orange circles labeled \$\theta\_T^{(t)}\$, blue triangles labeled \$\hat{\theta}^{(t)}\$, and red diamonds labeled \$\hat{\theta}\_T^{(c)}\$. The legend below explains the different trajectories: 'Original Trajectory \$\tau\_{\text{mtt}}\$' is shown with green dashed arrows, 'Convexified Trajectory \$\tau\_{\text{conv}}\$' with blue dashed arrows, '\$\vec{V}\_T\$ on \$\tau\_{\text{mtt}}\$' with yellow solid arrows, and '\$\vec{V}\_T\$ on \$\tau\_{\text{conv}}\$' with red solid arrows. The right plot is titled 'Validation Acc' and shows the progression of these trajectories over time 't'. It plots the same types of trajectories as the left plot, illustrating their behavior in terms of validation accuracy.

Figure 2: An illustration of the proposed MCT method. The left figure illustrates a schematic of the landscape in the model parameter space, while the right figure shows the validation accuracy of waypoint models extracted from expert trajectories of both the MTT method and our MCT method. In the left figure, the original trajectory  $\tau_{\text{mut}}$  exhibits constant oscillations, causing  $\vec{V}_{\mathcal{T}}^{(t)}$  $\tau^{(i)}$  to continuously change, resulting in fluctuating accuracy of the expert model in the right figure. In contrast, the trajectory  $\tau_{\text{conv}}$  of our MCT method is very stable, thereby ensuring a consistent guidance direction, which leads to a steady improvement of the expert model as shown in the right figure.

 $\tau_{\text{mut}}$  are all obtained by SGD training, and considering the variations in sample distribution across mini-batches, the expert trajectory  $\tau_{\rm mtt}$  has huge oscillations. Therefore, the training dynamics  $\vec{V}_{\mathcal{T}}^{(t)}$ The obtained by sampling two arbitrary points with an interval of M steps from  $\tau_{\text{mt}}$  cannot guarantee to always provide a favorable direction for  $\vec{V}_S^{(t)}$  $\vec{s}_{S}^{(t)}$  to learn. The final result is 1) poor  $\vec{V}_{\mathcal{T}}^{(t)}$  $\mathcal{T}^{(\iota)}$  leads to instability; 2) considerable time is expended in identifying the optimal optimization direction to achieve convergence. This raises the question: Is there a superior trajectory  $\hat{\tau}$  that consistently delivers more advantageous  $\vec{V}_{\tau}^{(t)}$  $\vec{\mathcal{T}}^{(t)}$  to optimize the synthetic dataset  $\mathcal S$  through  $\vec{V}_{\mathcal S}^{(t)}$  $\mathcal{S}^{(\nu)}$ ?

We believe that an ideal expert trajectory should: 1) For any  $\hat{\theta}_{\tau}^{(t)}$  $\overline{\tau}^{(t)}$  on  $\hat{\tau}$ , the obtained  $\overrightarrow{V}_{\tau}^{(t)}$  $\mathcal{T}^{(i)}$  should always point to the direction that guides the target loss  $\ell(\mathcal{T}; \theta_{\mathcal{T}}^{(t)})$  $\mathcal{T}^{(t)}$  to decrease; 2) This trajectory is easier to fit for S, because the size of S is much smaller than the original dataset  $\mathcal{T}$ . 3) The trajectory is easy to save and restore.

We draw inspiration from convex optimization [\[Boyd and Vandenberghe, 2004,](#page-9-8) [Bubeck, 2015\]](#page-9-9) and NTK [\[Jacot et al., 2018,](#page-9-1) [Hanin and Nica, 2019\]](#page-9-10). First, since deep learning is essentially a non-convex problem, if we can make expert trajectories exhibit more convex properties, optimization becomes much less difficult. How to find convex trajectories? NTK methods prove that for a neural network  $f_{\theta}(x)$ , its update can be approximated by its first-order Taylor expansion in the neural network tangent space [\[Lee et al., 2019\]](#page-10-12):

$$
f_{\theta}(x) \approx f_{lin,\theta}(x) = f_{\theta_0}(x) + (\theta - \theta_0)^{\mathsf{T}} \nabla_{\theta} f_{\theta_0}(x).
$$
 (6)

From this, we believe that replacing the original trajectory with a convex combination (linear) trajectory would be much more effective. The starting and ending points of this linear trajectory are the same as  $\tau_{\text{mut}}$ , and all the waypoints are distributed along this line. This trajectory meets our needs very well: 1) The visualization in Figure [1a](#page-1-0) verifies that the validation accuracy of the model on this trajectory consistently increases; 2) The direction of any  $\vec{V}_T^{(t)}$  $\tau^{(t)}$  sampled from this trajectory is always from the starting point to the ending (optimal) point, which is easy to fit for distilled data; 3) Only the parameters of its starting and ending points need to be stored, and the trajectory can be reconstructed by linear interpolation; 4) This trajectory is continuous, rather than consisting of intermittently sampled points like the original path, which greatly enriches our training set.

## 4 Our proposed MCT Method

#### 4.1 Matching Convexified Trajectory

The expert trajectory  $\tau_{\text{mtt}}$  is pre-generated with the parameter of all waypoint models stored in memory, *i.e.*,  $\tau_{\text{mtt}} = \{\theta_{\mathcal{T}}^{(0)}\}$  $\{\hat{\theta}_{\mathcal{T}}^{(0)}, \theta_{\mathcal{T}}^{(1)}, \ldots, \theta_{\mathcal{T}}^{(t)}, \ldots, \theta_{\mathcal{T}}^{(K)}\},$  where  $\theta_{\mathcal{T}}^{(t)}$  $\tau$ <sup>(*t*)</sup> is computed by multiple steps of mini-batch SGD [\[Cazenavette et al., 2022\]](#page-9-3).

However, the trajectory generated by vanilla mini-batch SGD exhibits strong non-convexity, which makes synthetic data challenging to converge to an optimal solution. To this end, we proposed MCT, which creates a convexified trajectory  $\tau_{conv}$  and is defined as:

$$
\tau_{\text{conv}} = \{\hat{\theta}^{(t)} | 0 \le t \le K\},\tag{7}
$$

$$
\hat{\theta}^{(t)} = (1 - \beta^{(t)})\theta_{\mathcal{T}}^{(0)} + \beta^{(t)}\theta_{\mathcal{T}}^{(K)},
$$
\n(8)

where  $\beta^{(t)} \in (0, 1)$  is a weight value that determines the distribution of all waypoints. The starting point  $\hat{\theta}^{(0)}$  and ending point  $\hat{\theta}^{(K)}$  are same as  $\theta_{\mathcal{T}}^{(0)}$  $_{\mathcal{T}}^{(0)}$  and  $_{\mathcal{T}}^{(K)}$  $\tau^{(K)}$  in  $\tau_{\text{conv}}$ . Particularly, the generated trajectory  $\tau_{\text{conv}}$  directly points from  $\theta_{\mathcal{T}}^{(0)}$  $\overset{(0)}{\tau}$  to  $\theta_{\mathcal{T}}^{(K)}$  $T^{(K)}$ , and  $\beta^{(t)}$  is determined by the ratio of the difference between  $\theta_{\mathcal{T}}^{(t-1)}$  $\overset{(t-1)}{\tau}$  and  $\theta_{\mathcal{T}}^{(t)}$  $\tau$ <sup>(*t*)</sup> in  $\tau$ <sub>mtt</sub> to the total length of  $\tau$ <sub>mtt</sub> as:

$$
\beta^{(0)} = 0,
$$

$$
\beta^{(t)} = \frac{\sum_{l=0}^{t-1} \text{Norm}(\theta_{\mathcal{T}}^{(l+1)} - \theta_{\mathcal{T}}^{(l)})}{\sum_{l=0}^{K-1} \text{Norm}(\theta_{\mathcal{T}}^{(l+1)} - \theta_{\mathcal{T}}^{(l)})}, t = 1, 2, ..., K,
$$
 $(9)$ 

where Norm( $\cdot$ ) is  $L_2$  norm. To mitigate discrepancies among different network layers, we calculate the  $L_2$  normalization for each layer individually, *i.e.*,  $\beta^{(t)} = [\beta_1^{(t)}, \beta_2^{(t)}, \dots, \beta_n^{(t)}]^\top$ , where each element in  $\beta^{(t)}$  represents the weight value of a network layer. Note that our trajectory is generated based on  $\tau_{\text{mtt}}$ , and the calculation of  $\beta^{(t)}$  does not require saving all the intermediate models  $\theta_{\mathcal{T}}^{(t)}$  $\mathcal{T}^{\prime}$ . It only needs to save  $\mathrm{Norm}(\theta^{(l+1)}_{\mathcal{T}} - \theta^{(l)}_{\mathcal{T}})$  $\tau^{(t)}$ ) obtained in each step of the expert trajectory  $\tau_{\text{mrt}}$ , allowing  $\beta^{(t)}$  to be calculated at the end of expert training. Given this expert trajectory  $\tau_{conv}$ , the distillation in Equ. [2](#page-3-0) can be conducted. During distillation, our MCT method always provides a convexified guidance  $\vec{V}_{\mathcal{T}}^{(t)}$  with the direction from  $\theta_{\mathcal{T}}^{(0)}$  $\overset{(0)}{\tau}$  to  $\theta_{\mathcal{T}}^{(K)}$  $\vec{\tau}^{(K)}$ , leading to the steady optimization of  $\vec{V}_{\mathcal{S}}^{(t)}$  $\mathcal{S}^{(\iota)}$ , and thus, the convergence of  $S$  will be rapid.

#### 4.2 Continuous Sampling

Due to the continuity of our convexified trajectory, we can perform continuous sampling from the trajectory during distillation. This approach is completely different from the MTT method, enabling the selection of intermediate positions such as "the 1.5th point." Specifically, the MTT method only performs discrete sampling on the expert trajectory (*i.e.*, selecting  $\theta_{\mathcal{T}}^{(t)}$  with an integer t). In contrast, for  $\tau_{conv}$  with the starting point  $\hat{\theta}^{(0)}$  and ending point  $\hat{\theta}^{(K)}$ , since all points are on a straight line, we can obtain any timestep  $\hat{\theta}^{(c)}$  with a decimal  $c \in [0, K]$  on this line by interpolation:

<span id="page-5-0"></span>
$$
\hat{\theta}^{(c)} = (1 - \hat{\beta})\hat{\theta}^{(0)} + \hat{\beta}\hat{\theta}^{(K)},
$$
  
\n
$$
\hat{\beta} = (1 - \eta)\beta^{(\lfloor c \rfloor)} + \eta\beta^{(\lceil c \rceil)},
$$
  
\n
$$
\eta = c - \lfloor c \rfloor,
$$
\n(10)

After  $\hat{\theta}^{(c)}$  and  $\hat{\theta}^{(c+M)}$  are obtained, the distillation process can be conducted. This continuous sampling strategy ensures sufficient learning and fitting of the entire expert trajectory  $\tau_{\text{conv}}$ , facilitating thorough learning of the synthetic dataset  $S$ .

<span id="page-5-1"></span>

#### 4.3 Memory-Efficient Storage

In conventional MTT, the learning of the expert trajectory requires storing the parameters of all timesteps in memory, which will incur significant storage overhead. Formally, let  $W$  denote the size

<span id="page-6-0"></span>Image /page/6/Figure/0 description: The image displays three plots. Plot (a) shows the validation accuracy on CIFAR-10 over iterations, with solid lines representing 'Ours' methods and dashed lines representing 'MTT' methods for ipc50, ipc10, and ipc1. Plot (b) shows the validation accuracy on CIFAR-100 over iterations, with similar line styles and labels as plot (a). Plot (c) is a bar chart comparing storage in bytes for CIFAR10, CIFAR100, and Tiny-ImageNet, showing 'MTT' and 'Ours' methods. The bars for CIFAR10 are approximately 0.65 and 0.05, for CIFAR100 are approximately 1.0 and 0.2, and for Tiny-ImageNet are approximately 3.9 and 0.3.

Figure 3: (a) and (b): Convergence comparisons of distillation process on CIFAR-10 and CIFAR-100, where the symbol "star" denotes the convergence point. (c): Storage comparisons on three datasets.

of the network parameter  $\theta_{\mathcal{T}}^{(t)}$  $\sigma$  and C denote the size of other irrelevant parameters. Since there are K timesteps on  $\tau_{\text{mrt}}$ , the entire required storage will be:

$$
\text{Storage}_{\text{mt}} = K \times W + C = O(KW). \tag{11}
$$

In contrast, our method only requires storing the starting point  $\hat{\theta}^{(0)}$ , the ending point  $\hat{\theta}^{(K)}$ , and point distribution  $\{\beta^{(t)} | 0 \le t \le K\}$  along the trajectory. Therefore, the entire storage cost becomes:

$$
\text{Storage}_{\text{conv}} = 2 \times W + K \times (\beta^{(t)}) + C. \tag{12}
$$

Since  $\beta^{(t)}$  is a floating-point number, the storage cost will be Storage<sub>conv</sub> =  $O(W)$ . In practice, K is usually set to 50. Once the surrogate models in distillation become complex (*e.g.* LLMs),  $K$  and  $W$ will increase simultaneously, highlighting the significant storage advantages of our MCT method.

### 5 Experiment

#### 5.1 Experiment Setup

Experiment Settings: We evaluated our method on three datasets: CIFAR-10 and CIFAR-100 [\[Krizhevsky et al., 2009\]](#page-10-13), and Tiny-ImageNet [\[Le and Yang\]](#page-10-14). We first generated the convexified trajectories with our MCT method. Similar to MTT, we applied Kornia [\[Riba et al., 2020\]](#page-10-15) Zero component analysis (ZCA) whitening on CIFAR-10, CIFAR-100, and Tiny-ImageNet datasets, and utilized Differentiable Siamese Augmentation (DSA) [\[Zhao and Bilen, 2021\]](#page-10-7) technique during training and evaluation.

Evaluation and Baselines: Our MCT method is compared with several baselines from different branches, including Dataset Condensation (DC) [\[Zhao et al., 2020\]](#page-10-6), Distribution Matching (DM) [\[Zhao and Bilen, 2023\]](#page-10-5), DSA [\[Zhao and Bilen, 2021\]](#page-10-7), Condense Aligning FEatures (CAFE) [Wang](#page-10-0) [et al.](#page-10-0) [\[2022\]](#page-10-0), dataset distillation using Parameter Pruning (PP) [\[Li et al., 2023\]](#page-10-16), and MTT. Following the conventional settings, we conducted dataset distillation using 1/10/50 images per class (ipc) for evaluations, respectively. The images with the resolution of  $32 \times 32$  and  $64 \times 64$  were synthesized on the CIFAR and Tiny-ImageNet datasets, respectively. Subsequently, five randomly initialized networks were trained in 1000 iterations with the cross-entropy loss on the distilled dataset. These trained networks were then evaluated on the real validation set, and their average accuracy (Acc) was reported as the evaluation metric. To maintain consistency with MTT and DC, we use ConvNet [\[Gidaris and Komodakis, 2018\]](#page-9-11) as the surrogate model. This model comprises 128 filters with a  $3 \times 3$ kernel size. Following the filters, instance normalization [\[Ulyanov et al., 2016\]](#page-10-17) and ReLU activation are applied. Additionally, an average pooling layer with a kernel size of  $2 \times 2$  and a stride of 2 is incorporated into the network.

Implementation Details: We adopt the same settings of MTT in most cases. Specifically, 100 expert trajectories are generated, each spanning 50 epochs of training (*i.e.*, 51 timesteps). In practice, we often insert two waypoint models in the expert trajectory of MTT to derive our convexified trajectory: the models of 6-th and 25-th epochs for CIFAR-10 and the models of 15-th and 30-th epochs for CIFAR-100 and Tiny-ImageNet. During the distillation process, 5,000 distillation iterations are

<span id="page-7-0"></span>

| Taoit 1. I chformance of various rugbrithins on Different Datascus |                |                |                |                |                |                |               |                |                |
|--------------------------------------------------------------------|----------------|----------------|----------------|----------------|----------------|----------------|---------------|----------------|----------------|
| Dataset                                                            | $CIFAR-10$     |                | CIFAR-100      |                |                | Tiny ImageNet  |               |                |                |
| ipc                                                                |                | 10             | 50             |                | 10             | 50             |               | 10             | 50             |
| Random                                                             | $15.4 \pm 0.3$ | $31.0 \pm 0.5$ | $50.6 \pm 0.3$ | $4.2 \pm 0.3$  | $14.6 \pm 0.5$ | $33.4 \pm 0.4$ | $.4 \pm 0.1$  | $5.0 \pm 0.2$  | $15.0 \pm 0.4$ |
| DC [Zhao et al., 2020]                                             | $28.3 \pm 0.5$ | $44.9 \pm 0.5$ | $53.9 \pm 0.5$ | $12.8 \pm 0.3$ | $25.2 \pm 0.3$ |                |               |                |                |
| DSA [Zhao and Bilen, 2021]                                         | $28.8 \pm 0.7$ | $52.1 \pm 0.5$ | $60.6 \pm 0.5$ | $13.9 \pm 0.3$ | $32.3 + 0.3$   | $42.8 \pm 0.4$ |               |                |                |
| CAFE [Wang et al., 2022]                                           | $30.3 \pm 1.1$ | $46.3 \pm 0.6$ | $55.5 \pm 0.6$ | $12.9 \pm 0.3$ | $27.8 \pm 0.3$ | $37.9 \pm 0.3$ |               |                |                |
| DM [Zhao and Bilen, 2023]                                          | $26.0 \pm 0.8$ | $48.9 \pm 0.6$ | $63.0 \pm 0.4$ | $11.4 \pm 0.3$ | $29.7 \pm 0.3$ | $43.6 \pm 0.4$ | $3.9 + 0.2$   | $12.9 \pm 0.4$ | $24.1 \pm 0.3$ |
| PP [Li et al., 2023]                                               | $46.4 + 0.6$   | $65.5 + 0.3$   | $71.9 \pm 0.2$ | $24.6 \pm 0.1$ | $43.1 \pm 0.3$ | $48.4 \pm 0.3$ |               |                |                |
| MTT [Cazenavette et al., 2022]                                     | $46.3 \pm 0.8$ | $65.3 \pm 0.7$ | $71.6 \pm 0.2$ | $24.3 \pm 0.3$ | $40.1 \pm 0.4$ | $47.7 \pm 0.2$ | $8.8 \pm 0.3$ | $23.2 \pm 0.2$ | $28.0 \pm 0.3$ |
| Ours                                                               | $48.5 \pm 0.2$ | $66.0 \pm 0.3$ | $72.3 \pm 0.3$ | $24.5 \pm 0.5$ | $42.5 \pm 0.5$ | $46.8 \pm 0.2$ | $9.6 \pm 0.5$ | $22.6 \pm 0.8$ | $27.6 \pm 0.4$ |
| Full dataset                                                       |                | $84.8 \pm 0.1$ |                |                | $56.2 \pm 0.3$ |                |               | $37.6 \pm 0.4$ |                |

Table 1: Performance of Various Algorithms on Different Datasets

<span id="page-7-1"></span>Image /page/7/Figure/2 description: This image contains three line graphs, each representing a different number of expert trajectories: 1, 10, and 50. All three graphs plot performance against an x-axis that ranges from 0 to 1500. The y-axis for the first graph ranges from 45 to 60, while the y-axis for the second and third graphs ranges from 45 to 65. Each graph displays two lines: one labeled 'with-CS' in purple and another labeled 'without-CS' in green. In the first graph (1 Expert Trajectory), the 'with-CS' line generally stays above the 'without-CS' line. In the second graph (10 Expert Trajectories), the two lines are very close, with the 'with-CS' line slightly above for most of the graph. In the third graph (50 Expert Trajectories), the lines are also very close, with the 'with-CS' line starting slightly below and then crossing over to be slightly above the 'without-CS' line towards the end.

Figure 4: Effects of Continuous Sampling over iterations with different expert trajectory numbers.

conducted. For each iteration,  $\hat{\theta}^{(c)}$  is generated from Equ. [10,](#page-5-0) where the decimal c is randomly sampled within [0, MaxStartEpoch]. We adopt the SGD optimizer, and a learnable learning rate is employed to distill the synthetic data. All experiments are run on four RTX3090 GPUs.

### 5.2 Experiment Result

Validation Accuracy Comparison. Table [1](#page-7-0) presents a comparison of validation accuracy between our method and various baselines across three datasets. Although performance is not the main focus of our MCT method, it is evident that our method achieves the best performance on the three metrics of the CIFAR-10 dataset as well as the ipc=1 metric of the Tiny ImageNet dataset. Notably, compared to the crucial MTT method, our MCT method demonstrates performance improvements in most metrics, indicating that our convexified trajectory and continuous sampling strategy can indeed provide enhanced guidance to the optimization of synthetic datasets.

Convergence of Distillation Process. Figure [3a](#page-6-0) and [3b](#page-6-0) illustrate the distillation processes utilizing the MCT and MTT methods for the CIFAR-10 and CIFAR-100 datasets. After every 100 distillation iterations, five networks with random initialization are trained on the current distillation dataset and their average accuracy on the validation set are recorded. The figures present the validation accuracy trends of both methods over the initial 2,500 iterations. As depicted, under all ipc settings, our MCT method achieves a substantial performance much sooner (200-1200 iterations ahead), indicating a faster convergence speed; after nearing convergence, the performance of the MCT method remains consistently stable as iterations proceed, whereas the MTT method still experiences significant performance fluctuations. These two phenomena suggest that our method effectively enhances training stability and accelerates the convergence process.

Comparison of Storage Requirement. Figure [3c](#page-6-0) compares the required storage of the expert trajectory between MTT and our MCT method. As demonstrated in Sec. [4.3,](#page-5-1) it is clear that our convex trajectories require significantly less memory (approximately 8%) compared to the expert trajectories needed by the MTT method. It is foreseeable that as model sizes and expert trajectories continue to grow, the space savings offered by our method will become even more substantial.

Visualization of Distilled Data. The visualization results of the synthetic data on CIFAR-10 with ipc=10 and CIFAR-100 with ipc=1 are presented in Figure [5a](#page-8-0) and [5b.](#page-8-0) As we can see, the synthetic set learned from our expert trajectories exhibits notable degrees of recognizability and authenticity, while it also tends to integrate various characteristic features of images within the same category.

<span id="page-8-1"></span>Table 2: Effects of Continuous Sampling with different numbers of expert trajectories on CIFAR-10.

<span id="page-8-2"></span>

| Number of expert trajectories |                                                                                                             |                                                     | 20 | 50 |
|-------------------------------|-------------------------------------------------------------------------------------------------------------|-----------------------------------------------------|----|----|
| w/o. Continuous Sampling      | $\frac{54.8 \pm 0.2}{60.6 \pm 0.2}$ $\frac{61.5 \pm 0.3}{62.3 \pm 0.3}$ $\frac{62.1 \pm 0.4}{62.1 \pm 0.4}$ |                                                     |    |    |
| w. Continuous Sampling        | $56.2 \pm 0.3$                                                                                              | $61.3\pm0.5$ $61.8\pm0.6$ $62.8\pm0.3$ $62.8\pm0.2$ |    |    |

| Table 3: Effects of different M with different ipc on CIFAR-10. |  |  |  |  |  |  |  |  |  |  |
|-----------------------------------------------------------------|--|--|--|--|--|--|--|--|--|--|
|-----------------------------------------------------------------|--|--|--|--|--|--|--|--|--|--|

| M      | 3    | 4    | 5           | 6           | 7    |
|--------|------|------|-------------|-------------|------|
| ipc=1  | 46.7 | 47.1 | <b>48.5</b> | 48.0        | 45.6 |
| ipc=10 | 62.3 | 62.6 | 65.0        | <b>66.0</b> | 65.2 |
| ipc=50 | 70.0 | 71.4 | 71.8        | <b>72.3</b> | 71.8 |

### 5.3 Ablation Studies

**Effects of Continuous Sampling.** To verify the effect of the continuous sampling, we set  $\text{p}c=10$ and randomized the starting epoch parameter within the range [0,5] on the CIFAR-10 dataset. The validation accuracy over iterations and the optimal accuracy throughout the entire distillation process are reported in Figure [4c](#page-7-1) and Table. [2,](#page-8-1) respectively. Overall, the integration of continuous sampling can improve the validation performance under all conditions. Moreover, the fewer the number of expert trajectories, the more pronounced the performance improvement brought about by the continuous sampling strategy. Those results prove that our continuous sampling can effectively expand the sampling space, ultimately leading to the enhancement of the final distillation outcomes.

**Effects of expert updating step M.** Table [3](#page-8-2) shows the effects of the updating step  $M$  of the expert trajectory  $\tau_{\text{conv}}$  on the CIFAR-10 dataset. N is set to 50 for all results. As we can see, when ipc=1, the optimal performance can be obtained at  $M=5$ , while when ipc=10 and ipc=50, the optimal performance can be obtained at  $M=6$ . Overall, our MCT method is robust to the selection of M and will not experience significant performance degradation with changes in M.

# 6 Conclusion

To address three major limitations of traditional MTT, this paper draws inspiration from NTK methods and proposes a novel perspective to understand the essence of dataset distillation and MTT. A simple yet novel Matching Convexified Trajectory method is introduced to create a simplified, convexified expert trajectory that enhances the optimization process, leading to more stable and rapid convergence and reduced memory consumption. The convexified trajectory allows for continuous sampling during distillation, enriching the learning process and ensuring thorough fitting of the expert trajectory. Our

<span id="page-8-0"></span>Image /page/8/Picture/9 description: The image displays a grid of 100 smaller images, arranged in 10 rows and 10 columns. The grid is divided into two halves, with 50 images on the left and 50 on the right. The images appear to be generated or stylized, with a soft, somewhat abstract quality. The left half of the grid features a variety of subjects including what appear to be airplanes, cars, birds, cats, deer, dogs, horses, and ships. The right half of the grid shows a different set of subjects, including an apple, a bicycle, a bottle, a camel, a chair, a couch, a crab, a cup, a deer, a dog, an elephant, a flower, a house, a lion, a motorcycle, a mushroom, a pear, a person, a sofa, a sunflower, and a tree. Many of the images have a dreamlike or painterly aesthetic.

(a) CIFAR-10,  $\text{ipc}=10$  (b) CIFAR-100,  $\text{ipc}=1$ 

Figure 5: Visualization of synthetic dataset.

experiments on CIFAR-10, CIFAR-100, and Tiny-ImageNet datasets demonstrate MCT's superiority over MTT and other baselines. MCT's ablation studies confirm the benefits of continuous sampling and the impact of the convexified trajectory on distillation performance. The results indicate that MCT is a promising solution for training complex models with reduced data needs, offering an efficient, stable, and memory-friendly approach to dataset distillation.

## 7 Limitations

Our MCT method has three primary limitations: 1. Although MCT can effectively enhance training stability and convergence speed, the improvement in validation accuracy is not very significant due to the starting and ending points being the same as those in MTT, and the enhancement is mainly attributed to the more thorough trajectory learning enabled by continuous sampling; future work could identify better endpoints to further improve performance. 2. While our trajectory provides a direction with more stable and rapidly descending, the calculation the magnitude  $\beta$  is relatively simple (derived by the proportion of MTT step size to trajectory length), and there may exist more optimal step sizes that allow for more rapid and robust trajectory learning. 3. The linear approximation of NTK is proposed based on infinitely wide networks and requires some rather strict initialization methods. However, we did not conduct our work under this condition; further theoretical deduction is required to address this issue.

## References

<span id="page-9-0"></span>Sanjeev Arora, Simon S Du, Wei Hu, Zhiyuan Li, Russ R Salakhutdinov, and Ruosong Wang. On exact computation with an infinitely wide neural net. *Advances in neural information processing systems*, 32, 2019.

<span id="page-9-8"></span>Stephen Boyd and Lieven Vandenberghe. *Convex Optimization*. Cambridge University Press, 2004.

- <span id="page-9-9"></span>Sébastien Bubeck. Convex optimization: Algorithms and complexity, 2015.
- <span id="page-9-3"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022.
- <span id="page-9-6"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023.
- <span id="page-9-5"></span>Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3749–3758, 2023.
- <span id="page-9-7"></span>Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-9-11"></span>Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4367–4375, 2018.
- <span id="page-9-4"></span>Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023.
- <span id="page-9-10"></span>Boris Hanin and Mihai Nica. Finite depth and width corrections to the neural tangent kernel. *arXiv preprint arXiv:1909.05989*, 2019.
- <span id="page-9-1"></span>Arthur Jacot, Franck Gabriel, and Clément Hongler. Neural tangent kernel: Convergence and generalization in neural networks. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-9-2"></span>Zixuan Jiang, Jiaqi Gu, Mingjie Liu, and David Z Pan. Delving into effective gradient matching for dataset condensation. In *2023 IEEE International Conference on Omni-layer Intelligent Systems (COINS)*, pages 1–6. IEEE, 2023.

<span id="page-10-13"></span>Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.

- <span id="page-10-14"></span>Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge.
- <span id="page-10-12"></span>Jaehoon Lee, Lechao Xiao, Samuel Schoenholz, Yasaman Bahri, Roman Novak, Jascha Sohl-Dickstein, and Jeffrey Pennington. Wide neural networks of any depth evolve as linear models under gradient descent. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-10-8"></span>Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364. PMLR, 2022.
- <span id="page-10-11"></span>Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Soft-label anonymous gastric x-ray image distillation. In *2020 IEEE International Conference on Image Processing (ICIP)*, pages 305–309. IEEE, 2020.
- <span id="page-10-10"></span>Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Compressed gastric image generation based on soft-label dataset distillation for medical data sharing. *Computer Methods and Programs in Biomedicine*, 227:107189, 2022a.
- <span id="page-10-9"></span>Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Dataset distillation for medical dataset sharing. *arXiv preprint arXiv:2209.14603*, 2022b.
- <span id="page-10-16"></span>Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Dataset distillation using parameter pruning. *IEICE Transactions on Fundamentals of Electronics, Communications and Computer Sciences*, 2023.
- <span id="page-10-4"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *Advances in Neural Information Processing Systems*, 35:13877– 13891, 2022.
- <span id="page-10-3"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021.
- <span id="page-10-15"></span>Edgar Riba, Dmytro Mishkin, Daniel Ponsa, Ethan Rublee, and Gary Bradski. Kornia: an open source differentiable computer vision library for pytorch. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 3674–3683, 2020.
- <span id="page-10-17"></span>Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. Instance normalization: The missing ingredient for fast stylization. *arXiv preprint arXiv:1607.08022*, 2016.
- <span id="page-10-0"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196–12205, 2022.
- <span id="page-10-1"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-10-7"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021.
- <span id="page-10-5"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023.
- <span id="page-10-6"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2020.
- <span id="page-10-2"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022.
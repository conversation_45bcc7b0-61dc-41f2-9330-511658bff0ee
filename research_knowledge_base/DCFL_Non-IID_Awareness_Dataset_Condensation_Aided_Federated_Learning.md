# DCFL: Non-IID Awareness Dataset Condensation Aided Federated Learning

Xingwang Wang *College of Computer Science and Technology Jilin University* Changchun, China

<EMAIL>

<PERSON><PERSON><PERSON> *College of Software Jilin University* Changchun, China <EMAIL> <PERSON><PERSON> Sun\*

*College of Computer Science and Technology Jilin University* Changchun, China <EMAIL>

*Abstract*—Federated learning (FL) is a decentralized learning paradigm wherein a central server iteratively trains a global model by utilizing clients who possess a certain amount of private datasets. The main challenge of FL lies in the fact that the client-side private data may not be identically and independently distributed (Non-IID), significantly impacting the accuracy of the global model. Existing methods tend to overlook analysis and utilize the characteristics of data complementary among clients due to privacy constraints. Intuitively, utilizing statistical distinctions among private data on the client side can help mitigate the Non-IID degree. Besides, the recent advancements in dataset condensation technology have inspired us to investigate its potential applicability in addressing Non-IID issues while maintaining privacy. Motivated by this, we propose DCFL which divides clients into groups by using weight similarity measurement method, like Centered Kernel Alignment (CKA), to approximate and represent data similarity. The private data from clients within the same group be complementary and then can use dataset condensation methods with Non-IID awareness to complement clients. Additionally, filtering mechanism, data enhancement techniques are incorporated to efficiently utilize condensed data, enhance model performance, and minimize communication time. Experimental results demonstrate that DCFL achieves competitive performance on popular federated learning benchmarks including MNIST, Fashion-MNIST, SVHN, and CIFAR-10 with existing FL algorithms.

*Index Terms*—federated learning, dataset condensation, centered kernel alignment, group division, client selection

## I. INTRODUCTION

With the proliferation of Internet of Things devices, Federated learning (FL) has emerged as a promising machine learning paradigm. In FL, many clients collaboratively train a model under the orchestration of a central server, while keeping the training data local [1]. Due to FL's great privacy protection, communication reduction for processing voluminous distributed data, high scalability, and other excellent advantages [2], it has received extensive attention in academia and industry. At present, FL has been widely used in keyboard word spotting [3], driver activity recognition [4], speech recognition [5], health monitoring [6], and other domains [7]–[9].

<sup>∗</sup>Corresponding author

This research was supported in part by the National Natural Science Foundation of China (NSFC) (Grants No. U19A2061, No. 61902143), in part by Jilin Scientific and Technological Development Program (No. YDZJ202101ZYTS008), in part by National Key Research and Development Program of China (No. 2023YFB4502304).

However, Non-Identically and Independent Distribution [10] (Non-IID), as the distribution of the client's local data is not representative of the distribution of the overall data [11], which causes the training process of the server model to be more unstable, leads to more communication rounds between clients and server, and degrades the final model performance drastically [12], [13]. The reduction in the test accuracy of FedAvg for Non-IID data up to 11.31% in MNIST, 51.31% in CIFAR-10 [14].

Recently, researchers have devised FL methods from diverse perspectives, such as FL optimization algorithm [15] , client selection [16], and data complement [14], to mitigate the negative effects caused by Non-IID. The optimization-oriented approaches focus on considering local model imbalances during training and designing aggregation strategies to counteract the effects of Non-IID [17]–[19]. In contrast, client selection strategies prioritize the heterogeneity of the system [20]–[22], such as computational power, communication capabilities, or both, to maximize clients' participation in the training process and minimize communication time. On the other hand, data complement utilizes a limited amount of globally shared data to rectify imbalances, thereby mitigating the negative impact of Non-IID data [23].

The first two approaches primarily manipulate local models trained on Non-IID data and do not directly address the Non-IID data itself [11]. Conversely, data complement offers a more intuitive approach to complementing data and addresses the root cause of the Non-IID issue, making it a promising direction. However, the availability of globally shared data is often unrealistic in federated learning scenarios, as client-side data and its distribution are strictly off-limits for access. More importantly, the forwarding and utilization of globally shared data increases the communication burden and privacy risk.

Inspired by the above, we propose a novel execution framework of federated learning, called DCFL, which stands for Dataset Condensation aided Federated Learning with Non-IID awareness. DCFL, which adopts a novel perspective on client data complementarity and utilizes this information to divide clients into groups and guide participant selection, aims to alleviate the negative impacts of Non-IID data on FL model training, communication, and performance by using condensed data efficiently. Measuring the complementarity of clients'

Image /page/1/Figure/0 description: This is a diagram illustrating a federated learning system with client grouping and data condensation. The server selects clients and aggregates their models and condensed datasets. Clients, upon being requested, train with DSA and perform dataset condensation, producing updated models and condensed data. Clients not requested also perform dataset condensation. The system involves transitions of the latest global model and complementary condensed data between the server and clients. A group strategy divides clients into groups (Group 1, Group 2, Group g) for coordinated learning. CKA is used for model comparison, and a 'guided' mechanism influences the process.

Fig. 1. The framework of DCFL.

data is a crucial challenge since accessing or transferring client data is prohibited due to privacy and confidentiality concerns [10]. In DCFL, we resort to using model weight similarity to approximate represent data similarity. Specifically, Centered Kernel Alignment (CKA) [24] as an effective and representative weight similarity measurement method has been applied in DCFL.

To be specific, DCFL starts by maintaining a table that records the complementary relationships among clients. Further, utilizing the table to fine-grained select participating clients and facilitate the training of the local model by selected clients with relatively less bias and full knowledge, through the utilization of auxiliary data from other complementary clients. However, in the traditional FL implementation process, we can only get weights or gradients from participating clients. Motivated by condensed data obtained by data condensation methods owns informative, representative, small quantity features and no fear of privacy violation [25], we resort to utilizing condensate data from selected clients as a backbone to be delivered between the server and clients. Besides, Data filtering, data augmentation and participant weight recalculation are also employed, further improving the final model performance, and reducing the total communication rounds and cost.

Our contributions in this work are summarized hereafter:

- CKA-based client complementarity. The CKA method is introduced to obtain the complementarity among clients, which guides client selection and condensed data transfer in DCFL. To the best of our knowledge, the proposed DCFL is the first effort to apply CKA to client grouping, client selection, and condensed data utilization.
- Condensed data-assisted Client model training with Non-IID awareness. When the client model is training, real data from the client and condensed data from the same complementary group's clients are collaboratively uti-

lized. Additionally, the filtering mechanism, the Differentiable Siamese Augmentation (DSA) data augmentation technique [26], and reorganized weight calculation for participating clients are applied, which endow DCFL with superior performance.

We employed four public datasets MNIST [27], Fashion-MNIST [28], SVHN [29], and CIFAR-10 [30], and the experiments validate the effectiveness of the DCFL framework proposed in this paper.

The rest of the paper is organized as follows. Section II introduces the overview of DCFL. Then, we present the design details and theory proofs in Section III. Section IV presents the simulation results under different scenarios. In Section V, we describe the related work about DCFL. Finally, we conclude the paper in Section VI.

## II. OVERVIEW OF DCFL

To put it simply, during the implementation of DCFL, we use CKA to group division clients and guide client selection, then use complementary condensed data as auxiliary data to complement clients. Fig. 1 illustrates the overall design of DCFL.

The execution flow of DCFL can be divided into six phases: 1. Pre-training of the server model and client grouping. Before training, the server sends requests to clients for its' condensed data and partial model parameters located in the classifier. After receiving this information, the server can use those condensed data for model pre-training and use classifier parameters for executing the calculation of CKA to get the local data complementary relationships among overall requested clients.

2. Participant selection. The server selects a fraction number of participants to participate in the current round training process.

3. Deliver information. The server sends model parameters to clients who have never been seen before, while sends model parameters and complementary condensed data which may be filtered to clients who have taken part in FL training in the past.

4. Local update. Participating clients receive the latest model from the server and train based on its private dataset and may plus the additional condensed dataset derived from complementary clients for fine-tuning if they had been selected to participate in the past. Besides, the DSA augmentation method is applied in the local training process.

5. Upload information. Clients who first take part in training send condensate data and updated local model back, while clients who have been selected, only send the latest local model to the server.

6. Model Aggregation. When clients complete the computation, the server uses FL optimization algorithm, such as FedProx, FedNova, or FedDisco, to aggregate updates from clients. In the process, according to clients' dataset quantity changes, the aggregating weight of participated clients is adjusted correspondingly.

Thanks to the structured design, it is easy to integrate FL optimization methods (e.g., FedAvg [1], FedProx [17], Fed-Nova [18], FedDisco [19]) and different dataset condensation methods (e.g., DC [31], DSA [26], DM [32]) into the proposed framework.

## III. DESIGN DETAILS

In this part, we present the design details of DCFL in three aspects, some notational symbols within this section and symbols in pseudocode are listed in Table I.

### A. CKA-based Client Complementarity

Why we can use CKA to judge the data complementarity among clients? the original idea comes from weight divergence. Reference [11] who uses

weight divergence = 
$$
\frac{\|w^{\text{FedAvg}} - w^{\text{SGD}}\|}{\|w^{\text{SGD}}\|}
$$
 (1)

to calculate the weight divergence between FedAvg and SGD <sup>1</sup>, and finds there is an association between the weight divergence and the skewness of the data. While they heuristic demonstrate the root cause of the weight divergence is due to the distance between the data distribution on each client and the population distribution and consider partial and whole into account, they don't further infer the data distribution relationships between peer-to-peer that clients with similar data distribution will have minimal weight divergence according to the variation of (1), like

weight divergence 
$$
= \frac{||w_m^t - w_n^t||}{||w^{SGD}||}
$$
 (2)

, which using the weight of client  $m$  and client  $n$  in round t instead of FedAvg and SGD to perform the calculation.

<sup>1</sup>SGD is the ideal situation where the server knows the overall dataset distribution and trains the whole dataset collected from all clients

# Algorithm 1 The Server Execution Flow of DCFL.

```
1: initialize global model w<sup>0</sup>; \mathbb{C}_E \leftarrow \emptyset
```

```
2: // the pre-training stage of server model<br>3: for r = 1 to M do
```

```
for r = 1 to M do
```

```
4: n \leftarrow \max(C_{pre} * K, 1)<br>5: \mathbb{C}_{crit} \leftarrow (random select)
```

```
\mathbb{C}_{opt} \leftarrow (random select n clients from K clients)
```

```
6: for k \in \mathbb{C}_{opt} in parallel do
```

```
7: if k not in \mathbb{C}_E then<br>8: w_k^c, \widetilde{D}_k \leftarrow Clier
8: w_k^c, \mathcal{D}_k \leftarrow ClientUpdateWoCD(k, w<sup>0</sup>)
```

```
9: \mathbb{C}_E \leftarrow \text{UpdateClient}(\mathbb{C}_E, k)
```

```
10: \widetilde{\mathcal{D}} \leftarrow \text{UpdateCondensedData}(\widetilde{\mathcal{D}}, \widetilde{\mathcal{D}}_k)<br>11: end if
```

```
end if
```

```
12<sup>i</sup> end for
```

 $22:$ 

 $29:$ 

```
13: \mathcal{M} \leftarrow \text{updateCKAMatrix}(\mathcal{M}, w_1^c, w_2^c, \ldots, w_n^c)
```

```
14: end for
15: w^1 \leftarrow \text{ServerUpdate}(w^0, \widetilde{\mathcal{D}})
```

16: // the training stage of server model

```
17: for r = 1 to T do
```

```
18: n \leftarrow \max(C_{com} * K, 1)<br>19: \mathbb{C}_{cont} \leftarrow (random select r
```

```
\mathbb{C}_{opt} \leftarrow (random select n clients from K clients)
```

```
20: for k \in \mathbb{C}_{opt} in parallel do
```

```
21: if k not in \mathbb{C}_E then
```

```
r_k^r, \widetilde{\mathcal{D}}_k \leftarrow ClientUpdateWoCD(k, w<sup>r</sup>)
```

```
23: \mathbb{C}_E \leftarrow \text{UpdateClient}(\mathbb{C}_E, k)
```

```
24: \tilde{D} \leftarrow \text{UpdateCondensedData}(\tilde{D}, \tilde{D}_k)<br>
25: \mathcal{M} \leftarrow \text{updateCKAMatrix}(\mathcal{M}, w_k^c)
```

```
25: \mathcal{M} \leftarrow \text{updateCKAMatrix}(\mathcal{M}, w_k^c)26: else
```

```
27: \mathcal{D}_k \leftarrow \text{GetComplementaryCondensedDataForK}(k, \mathcal{D},\mathbb{C}_E, \mathcal{M})
```

```
28: D\widetilde{D}_k' \leftarrow \text{filterCondensedData}(\widetilde{D}_k, \gamma)
```

```
\widetilde{v}_k^r \leftarrow \text{ClientUpdateWCD}(k, \widetilde{w}^r, \widetilde{\mathcal{D}}'_k)
```

```
30: end if
31: end for
```

```
32: p = getOptimizedWeights(\mathbb{C}_{opt})
```

```
33: Server aggregates local model w^{r+1} = \sum_{k=1}^{n} p_k w_k^r
```

```
34: end for
```

```
35: return w<sup>T</sup>
```

| Algorithm 2 The Client Execution Flow of DCFL. |
|------------------------------------------------|
|------------------------------------------------|

```
1: function CLIENTUPDATEWOCD(k, w)2: initialize client k's model with w: w_k \leftarrow w<br>3: B \leftarrow (split D_k into batches of size B_c)
             \mathcal{B} \leftarrow (split D_k into batches of size B_c)
 4: \widetilde{\mathcal{D}}_k \leftarrow \text{ClientDatasetDistillation}(k, w, D_k)<br>5: for each local epoch i from 1 to E_c do
 5: for each local epoch i from 1 to E_c do<br>6: for batch b \in B do
                    for batch b \in \mathcal{B} do
 7: w_k \leftarrow w_k - \eta_c \nabla \mathcal{L}(w_k; \mathcal{A}_w(b))8: end for
 9: end for
10: return w_k, \mathcal{D}_k11: end function
12: function CLIENTUPDATEWCD(k, w, \widetilde{\mathcal{D}})<br>13: \widetilde{\mathcal{D}}_k \leftarrow UpdateCondensedData(\widetilde{\mathcal{D}}_k, \widetilde{\mathcal{D}})
13: \mathcal{D}_k \leftarrow \text{UpdateCondensedData}(\mathcal{D}_k, \mathcal{D})<br>14: w_k \leftarrow \text{ClientUpdateWoCD}(k, \mathbf{w})14: w_k \leftarrow \text{ClientUpdateWoCD}(k, w)<br>15: //The fine-tuning stage
             //The fine-tuning stage
16: B \leftarrow (split \tilde{\mathcal{D}}_k into batches of size B_s)<br>17: for each local epoch i from 1 to E_s do
17: for each local epoch i from 1 to E_s do<br>18: for batch b \in B do
                    for batch b \in \mathcal{B} do
19: w_k \leftarrow w_k - \eta_s \nabla \mathcal{L}(w_k; \mathcal{A}_w(b))20: end for
21: end for
22: return w_k23: end function
```

TABLE I NOTATION DESCRIPTIONS.

| Notation          | Description                                                                                    |
|-------------------|------------------------------------------------------------------------------------------------|
| $K$               | The total client number in FL system                                                           |
| $D_k$             | Local training set of client $k$                                                               |
| $\widetilde{D}_k$ | Condensed data set of client $k$                                                               |
| $\widetilde{D}$   | Condensed data set which received by the server                                                |
| $\eta_c$          | The learning rate when using clients' local data                                               |
| $\eta_s$          | The learning rate when using clients' condensed data                                           |
| $B_c$             | Batch size for client's local private data                                                     |
| $B_s$             | Batch size for client's obtained condensed data                                                |
| $E_c$             | The number of local epochs by using local private data                                         |
| $E_s$             | The number of local epochs by using obtained condensed data                                    |
| $w_k^r$           | Local model of client $k$ at round $r$                                                         |
| $w_k^c$           | The classifier of client $k$ 's model                                                          |
| $w^t$             | Global model at round $t$                                                                      |
| $\mathcal{A}_w$   | Differentiable augmentation which parameterized with $w$                                       |
| $C_{com}$         | The fraction of clients who were selected to take part in the<br>FL training process           |
| $C_{pre}$         | The fraction of clients who were selected to pre-train server<br>model and provide information |
| $M$               | The total request rounds for server model pre-training                                         |
| $T$               | The total communication rounds between server and clients                                      |
| $\mathcal{M}$     | The CKA valuation matrix between clients                                                       |
| $C_{opt}$         | Clients who are selected to take part in process                                               |
| $C_E$             | Clients who have been selected to take part in process                                         |
| $p_k$             | The aggregation weight of client $k$ in current round                                          |
| $\gamma$          | Filter factor (between 0 and 1)                                                                |

Then clients with different data distributions will have large weight divergence which can further infer their complementary relationship. To further reflect weight divergence, works of [14] [23] propose applying the earth mover's distance(EMD) as follows:

$$
\sum_{i=1}^{C} \|p^{k}(y=i) - p(y=i)\|
$$
 (3)

between the global and local data distribution (p and  $p^k$ ) to simulation. The method is impractical because it assumes we already know the distribution of overall data distribution about different classes  $(p(y = i))$  and we can't obtain the specific data distribution about clients  $(p^k(y = i))$  in consideration of privacy protection. How to reflect weight divergence efficiently and reasonably, the work of [33] sheds light on this problem, which applies CKA as the alternative measure method of weight divergence to expose how the data heterogeneity affects each layer of a deep classification model and finds there exists a greater bias in the classifier than other layers. While they further propose a novel algorithm, Classifier Calibration with Virtual Representations (CCVR), and achieve excellent performance on CIFAR-10, CIFAR-100, and CINIC-10 datasets, they neglect to infer the data distribution relationships between clients and don't consider the use of CKA to guide client selection instead of traditional random client selection. Motivated by the above discoveries, we dig deeper to propose using CKA to measure weight divergence and then to approximate reflect data complementarity among clients which only uses

the partial model parameters of the classifier to calculate so that further reduce communication bandwidth and computation time cost.

To vividly understand and verify our assumption: the data similarity and complementary relationship among clients can be derived from model weight similarity measurement methods, like CKA. We perform an experimental study on clients with heterogeneous datasets. For the sake of simplicity and representativeness, we chose CIFAR-10 with 10 clients and chose a convolutional neural network with four layers. As for the Non-IID setting, we partition the data according to the Dirichlet distribution, we set the 10 clients into 5 groups, and the group list which each group contains a certain amount of clients is [2, 3, 2, 2, 1]. The detailed data distribution of clients is shown in Fig. 2(a).

We first pairwise calculate the EMD valuations of those clients to reflect their data complementarity and show in Fig. 2(b), which is the optimal situation where the data distribution of each client is known. From Fig. 2(b), we can find out that clients belonging to the same group have symmetrical and relatively high EMD valuations, while clients with different data distributions will have relatively low valuations. The lower valuation means more differences between clients' data distribution. For example, we can see client 0 is similar to client 1 and is highly different from client 5 and client 6.

Then based on clients' partial model parameters of classifier which have been updated for 10 epochs by using their local dataset to pair-wise calculate the CKA valuation, we can find the CKA relationships between clients in Fig. 2(c), which have similar judgments about clients' data complementarity relationships like Fig. 2(b). So our proposition that use CKA to measure the data complementarity relationship between clients is credible and pragmatic.

### B. Transfer of Condensed Data with Non-IID Awareness

In DCFL, we use condensed data, obtained from clients who implement dataset condensation in their private dataset, as auxiliary data to transport in two-phase: in the stage of server model pre-training (Line  $3 - 14$  of Algorithm 1). The server sends requests to clients, and then the clients can choose to send back information that contains condensed data or not according to their availability. When the server obtains a certain quantity of information after  $M$  request rounds, then it can use those condensed data to train the model instead of traditional random weight model initialization; In the stage of client-server communication, selected clients who have never taken part in the training process before will be asked to send condensate data and whole model parameters to the server to extend the server's knowledge (Line 22 – 25 of Algorithm 1). According to the complementary relationship, the server sends the latest model and condensed data from complementary clients to selected clients who have taken part in the training process before (Line 27 – 29 of Algorithm 1). Instead of transporting all collected condensed datasets, our method is more communication efficient and more fine-grained.

Image /page/4/Figure/0 description: The image displays three heatmaps comparing clients. The first heatmap, labeled (a) "Label distribution", shows the distribution of labels across clients. The second heatmap, labeled (b) "The EMD valuation between clients", illustrates the Earth Mover's Distance (EMD) between clients, with values ranging from 0.03 to 1.0. The third heatmap, labeled (c) "The CKA valuation between clients", presents the Centered Kernel Alignment (CKA) values between clients, with values ranging from 0.67 to 1.0. All heatmaps have client IDs on both the x and y axes, ranging from 0 to 9.

Fig. 2. Label distribution, EMD and CKA values between clients.

### C. Utilization of Condensate Data and Data Augmentation

DCFL is designed to operate condensate data with or without filtered (filter or not according to the quality of condensed data) for fine-tuning and applying the augmentation methods derived from DSA on the training process of the client side. We have introduced a filtration mechanism before the server delivers condensate data, which can retain more valuable data, further reduce communication bandwidth, and make the training process more stable. If the filter ratio  $\gamma$ is not set equal to 0, condensate data can be filtered based on their performance by the server (Line 28 of Algorithm 1). After participating clients receive the last server model and condensed data, they use their local dataset to update the model for  $E_c$  epochs as traditionally and then use obtained condensed data from the server with a smaller learning rate  $\eta_s$  to fine-tune for  $E_s$  epochs (Line 16 – 21 of Algorithm 2). Before adopting this organization method, we tried different methods for using condensed data like mingling synthetic data and local private data as a whole for training or freezing model parameters and using condensed data or real local data to fine-tune the classifier or more layers. After comparing the aforementioned methods, we found the currently adopted training method is the most effective and beneficial for model training. We also find Differentiable Siamese Augmentation method proposed by DSA can also be applied in DCFL to preprocess clients' local training data and received synthetic data before training (Line 7 and Line 19 of Algorithm 2) which significantly improves the final model performance.

## IV. EXPERIMENTS

### A. Experimental Setup

Datasets. In this paper, we evaluate the image classification performance of the final model which is obtained through the whole process of DCFL. So we conduct experiments on four datasets including MNIST, Fashion-MNIST, SVHN, and CIFAR-10. MNIST and Fashion-MNIST consist of 28×28 gray-scale training images of 10 classes. SVHN and CIFAR-10 contain 30k and 50k 32×32 training images from 10 categories respectively.

Experimental Settings. For data partitioning, we adopt the most representative and model-disturbing approach to divide

TABLE II TEST ACCURACY OF FL METHODS WITH DIFFERENT LEVEL OF NON-IID PARTITIONING.

| Method                                  | <b>MNIST</b> | <b>Fashion-MNIST</b> | <b>SVHN</b> | <b>CIFAR-10</b> |
|-----------------------------------------|--------------|----------------------|-------------|-----------------|
| $	ext{Accuracy} 	ext{ at } 	au = 0.5$   |              |                      |             |                 |
| FedAvg                                  | 97.39±0.18%  | 87.19±0.33%          | 86.17±0.33% | 49.15±0.94%     |
| FedAvg+DCFL                             | 98.47±0.07%  | 88.68±0.18%          | 92.19±0.20% | 63.88±0.41%     |
| FedProx                                 | 97.35±0.17%  | 87.58±0.31%          | 87.24±0.38% | 49.66±0.86%     |
| FedProx+DCFL                            | 98.38±0.06%  | 88.70±0.12%          | 91.76±0.17% | 63.33±0.39%     |
| FedNova                                 | 97.37±0.16%  | 87.21±0.34%          | 86.28±0.30% | 49.30±1.04%     |
| FedNova+DCFL                            | 98.42±0.07%  | 88.85±0.29%          | 91.72±0.14% | 62.86±0.42%     |
| FedDisco                                | 97.44±0.13%  | 87.28±0.33%          | 86.62±0.38% | 49.86±1.12%     |
| FedDisco+DCFL                           | 98.45±0.06%  | 88.84±0.10%          | 92.11±0.12% | 63.10±0.36%     |
| $	ext{Accuracy} 	ext{ at } 	au = 0.1$   |              |                      |             |                 |
| FedAvg                                  | 95.03±0.72%  | 77.77±1.67%          | 69.87±2.09% | 30.24±1.50%     |
| FedAvg+DCFL                             | 97.82±0.11%  | 84.44±0.28%          | 87.72±0.45% | 46.63±0.46%     |
| FedProx                                 | 95.19±0.50%  | 80.26±1.27%          | 80.25±1.25% | 35.23±1.07%     |
| FedProx+DCFL                            | 97.58±0.13%  | 84.40±0.24%          | 87.33±0.37% | 47.92±0.33%     |
| FedNova                                 | 95.68±0.42%  | 78.78±1.53%          | 63.25±2.93% | 27.57±1.96%     |
| FedNova+DCFL                            | 97.81±0.14%  | 84.05±0.26%          | 85.62±0.75% | 46.85±0.44%     |
| FedDisco                                | 95.25±0.62%  | 77.49±1.74%          | 73.47±1.97% | 30.69±1.60%     |
| FedDisco+DCFL                           | 97.78±0.08%  | 84.73±0.28%          | 87.61±0.37% | 47.46±0.32%     |
| $C_k = 2 	ext{ (pathological Non-IID)}$ |              |                      |             |                 |
| FedAvg                                  | 71.79±3.82%  | 54.28±3.40%          | 74.03±2.30% | 35.86±2.27%     |
| FedAvg+DCFL                             | 92.88±0.80%  | 69.14±2.39%          | 85.01±0.62% | 52.73±0.60%     |
| FedProx                                 | 75.81±3.93%  | 58.68±3.43%          | 78.28±1.74% | 38.69±1.93%     |
| FedProx+DCFL                            | 94.07±0.85%  | 72.58±2.04%          | 85.54±0.53% | 53.21±0.54%     |
| FedNova                                 | 74.16±5.56%  | 54.67±4.52%          | 72.46±2.74% | 35.67±2.04%     |
| FedNova+DCFL                            | 92.31±1.76%  | 70.66±2.15%          | 84.66±1.19% | 52.27±0.47%     |
| FedDisco                                | 76.09±6.85%  | 52.36±4.14%          | 74.05±2.55% | 34.78±1.80%     |
| FedDisco+DCFL                           | 93.04±1.36%  | 71.01±2.15%          | 85.44±1.48% | 53.12±0.69%     |

the training data for clients - data partitioning with Dirichlet distribution  $Dir_k(a)$  (aka the label distribution skew [5]). In this data split method, k is the number of clients and  $\alpha$ determines the Non-IID level. A smaller value of  $\alpha$  means a more unbalanced data distribution. We take two different scenarios of data distribution into consideration, including  $Dir_{20}(0.1)$ , and  $Dir_{20}(0.5)$ . Besides, we also adopt a quantitybased label imbalance data partition method (aka pathological Non-IID), like [1], to extend a highly extreme scenario, where each client only has data samples with two labels in our setting.

For model architecture, we adopt a convolutional neural network (CNN) for CIFAR-10 and SVHN, which is the typical deep neural network and is commonly applied in FL. Specifically, we set two 5×5 convolution layers followed by max-pooling layers and two fully connected layers with ReLU activation, same as the structure mentioned by [1], [17], [18]. For MNIST and Fashion-MNIST, we adopt a simple multilayer perceptron network.

Image /page/5/Figure/0 description: This is a figure containing three rows of plots, each row representing a different scenario of data distribution. The first row, labeled (a) Test accuracy under Dir20(0.5), shows the test accuracy of different federated learning algorithms on MNIST, FashionMNIST, SVHN, and CIFAR10 datasets. The second row, labeled (b) Test accuracy under Dir20(0.1), presents similar plots for a different data distribution scenario. The third row, labeled (c) Test accuracy under Ck=2, displays the test accuracy for another distribution scenario. Each plot shows the test accuracy (%) on the y-axis against the communication round on the x-axis, with different colored lines representing various algorithms like FedAvg, FedProx, FedNova, FedDisco, and their DCFL variants. The shaded regions around the lines indicate the fluctuation range or variance.

Fig. 3. Test accuracy under different scenarios on four datasets (the shadow around the broken line represents its fluctuation range; the x-axis of the figure represents communication round, and the y-axis represents prediction accuracy).

Hyper-parameters. Like traditional FL training, DCFL also involves tuning a set of hyperparameters. Our method needs to tune a few additional hyper-parameters compared to the traditional one, e.g., learning rate  $\eta_s$  and local update epochs  $E<sub>s</sub>$  for the fine-tuning when using condensate data, filter ratio  $\gamma$  for condensed data filtration. All experiments are run three times with different random seeds with one NVIDIA 3080 GPU and the average performance is reported in the paper.

## B. Performance Comparison

We first evaluate the performance of DCFL in improving the test accuracy, by comparing it with four baseline algorithms - FedAvg, FedProx, FedNova, and FedDisco.

According to Table II, it is shown that the accuracy of the server model trained by DCFL is generally higher than traditional FL methods in three settings. Compared with  $Dir<sub>20</sub>(0.1)$ ,  $Dir<sub>20</sub>(0.5)$ , the data distribution of clients who obey

pathological Non-IID is more extreme and heterogeneous. In this context, the performance improved by the proposed DCFL is more significant than the other two scenarios. As shown in Table II, among these three schemes, the Federated Learning optimization method - FedAvg performs the worst, and FedProx performs relatively well in total. Compared with traditional FL optimization methods, the proposed DCFL improves more than  $1.01\% \sim 21.09\%, 1.12\% \sim 18.65\%,$  $4.52\% \sim 17.85\%,$  and  $13.24\% \sim 19.28\%$  accuracy based on the four datasets, respectively.

From Fig. 3, it can be inferred that due to the full employment of obtained clients' condensed data in the pre-training stage of DCFL, which endows the global model has a relatively high prediction performance in initial compared with the traditional training process. While with the intensification of the Non-IID degree, utilizing and aggregating clients' trained

TABLE III NUMBER OF COMMUNICATION ROUNDS TO REACH A TARGET ACCURACY FOR DCFL AND OTHER FL OPTIMIZATION METHODS ON SVHN DATASET.

| Method/ToA@                   |          | α = 0.5  |          |          |
|-------------------------------|----------|----------|----------|----------|
|                               |          | ToA@0.86 | ToA@0.87 | Accuracy |
| Tradition                     | FedAvg   | 75       |          | 86.20%   |
|                               | FedProx  | 21       | 62       | 87.38%   |
|                               | FedNova  | 91       |          | 86.27%   |
|                               | FedDisco | 43       |          | 86.49%   |
| DCFL                          | FedAvg   | 3        | 5        | 92.29%   |
|                               | FedProx  | 4        | 5        | 91.92%   |
|                               | FedNova  | 3        | 5        | 91.80%   |
|                               | FedDisco | 3        | 5        | 92.36%   |
| α = 0.1                       |          |          |          |          |
|                               | ToA@0.66 | ToA@0.80 | Accuracy |          |
| Tradition                     | FedAvg   | 57       |          | 70.32%   |
|                               | FedProx  | 11       | 91       | 80.24%   |
|                               | FedNova  | 85       |          | 66.53%   |
|                               | FedDisco | 31       |          | 73.85%   |
| DCFL                          | FedAvg   | 1        | 7        | 87.73%   |
|                               | FedProx  | 1        | 9        | 87.70%   |
|                               | FedNova  | 1        | 20       | 86.31%   |
|                               | FedDisco | 1        | 7        | 87.64%   |
| Ck = 2 (pathological Non-IID) |          |          |          |          |
|                               | ToA@0.73 | ToA@0.78 | Accuracy |          |
| Tradition                     | FedAvg   | 72       |          | 76.19%   |
|                               | FedProx  | 35       | 72       | 78.76%   |
|                               | FedNova  | 92       |          | 73.54%   |
|                               | FedDisco | 71       |          | 75.35%   |
| DCFL                          | FedAvg   | 17       | 25       | 85.90%   |
|                               | FedProx  | 8        | 19       | 85.96%   |
|                               | FedNova  | 25       | 31       | 84.90%   |
|                               | FedDisco | 16       | 25       | 86.37%   |

model parameters cause model performance degradation, so that is the reason why the global model has relatively high performance at the beginning while falling sharply in the next round, like Fig. 3(b), and Fig. 3(c). In a word, DCFL makes the training process more stabilized and speeds up to reach convergence and high model performance.

## C. Communication Comparison

Then, we evaluate the performance of the proposed DCFL in communication round. From Table III, We can know that the communication round of the server model cost to achieve specific accuracy by DCFL is generally lower than traditional FL methods in three settings. Besides, it indicated that among these three schemes, the Federated Learning optimization method - FedProx performs relatively well in total. Compared with FedProx, the proposed DCFL decreased more than 92%, 90%, and 74% rounds based on the SVHN dataset.

## V. RELATED WORK

### A. Federated Learning

Federated learning, as a variation of distributed optimization, has attracted more and more attention and application nowadays in research and industry areas [10]. In order to mitigate the negative effects of Non-IID, many FL optimization algorithm has been proposed, such as Li et al. [17] proposed FedProx, which is based on FedAvg and introduces an additional L2 regularization penalty term in the local objective function to restrict the disturbance to the global server model aggregation from participated clients; Wang et al. [18] proposed FedNova, which adopts a normalized averaging method that eliminated objective inconsistency; Ye et al. [19]

proposed FedDisco, which applies a novel aggregation method based on the discrepancy between local and global category distribution. These methods are orthogonal to DCFL and can be easily combined. Besides, according to the distribution characteristics of clients' private datasets, the category of FL can be divided into horizontal FL, vertical FL, and Federated transfer learning [5]. In this paper, we mainly focus on the horizontal FL setting.

### B. Dataset Condensation

Dataset condensation<sup>2</sup> can be used to construct a smaller but informative synthetic dataset from the original large training dataset, whose condensed data is different from original training data and can acquire a comparable generalization performance with less training cost [34], [35]. Based on the objectives applied to mimic target data or to find a proxy model that learns synthetic datasets by optimizing their features and corresponding decoders, dataset condensation methods can be divided into a Meta-Learning Framework, Data Matching Framework, and Factorized Dataset Distillation [36]. Some works, like [37] use kernel to get synthetic data, while effective but time-consuming; Some works, like [38] directly matched the long-range trajectory between the target dataset and the synthetic dataset instead of single gradient matching, so that the computational overhead of training and storing expert trajectories is quite high. Due to the comprehensive consideration of computing cost, memory usage, and the specific features of FL, we use DC, DM, and DSA as our dataset condensation methods in DCFL.

#### C. Centered Kernel Alignment

To better understand and characterize the neural network representations learned from data, researchers from Google proposed the novelty and insightful method named Centered Kernel Alignment [24]. CKA provides an effective way to measure similarities between deep neural network representations, which takes the complex interaction between the training dynamics and structured data into account. Few pieces of literature apply CKA to Federated learning, but rarely of they further consider the use of the derived CKA indexes to reflect the complementary of privately owned datasets between different clients. For example, Mi Luo et al. [33] used the CKA to measure the similarity between the representations from the same layer of different clients' local models and found there exists a greater bias in the classifier than other layers, they didn't use that information to further deduce the peer-to-peer dataset complementary relationships, while to post-calibrate the classifier after federated training. In this paper, we use CKA from a novel perspective to guide client selection and condensed data utilization.

# VI. CONCLUSION

In this paper, we propose a novel implementation framework of federated learning - DCFL to achieve faster convergence,

2 It is the same as dataset distillation.

stabilize the model training process, and better model performance. By using the CKA-based client complementarity method to guide group division clients and then condensed data-assisted client model training with Non-IID awareness, we have reduced the communication rounds and cost to reach convergence. However, due to the development limitations in the field of dataset condensation, DCFL is difficult to achieve competitive results on large-scale datasets. Next, we will explore the dataset condensation method for large datasets and the feasibility of their ability to assist FL in overcoming the above issue.

# REFERENCES

- [1] B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y Arcas, "Communication-efficient learning of deep networks from decentralized data," in *Artificial intelligence and statistics*. PMLR, 2017, pp. 1273– 1282.
- [2] X. Wang, Y. Han, V. C. Leung, D. Niyato, X. Yan, and X. Chen, "Convergence of edge computing and deep learning: A comprehensive survey," *IEEE Communications Surveys & Tutorials*, vol. 22, no. 2, pp. 869–904, 2020.
- [3] X.-C. Li, J.-L. Tang, S. Song, B. Li, Y. Li, Y. Shao, L. Gan, and D.-C. Zhan, "Avoid overfitting user specific information in federated keyword spotting," *arXiv preprint arXiv:2206.08864*, 2022.
- [4] K. Doshi and Y. Yilmaz, "Federated learning-based driver activity recognition for edge devices," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 3338–3346.
- [5] Q. Yang, Y. Liu, T. Chen, and Y. Tong, "Federated machine learning: Concept and applications," *ACM Transactions on Intelligent Systems and Technology (TIST)*, vol. 10, no. 2, pp. 1–19, 2019.
- [6] J. Xu, B. S. Glicksberg, C. Su, P. Walker, J. Bian, and F. Wang, "Federated learning for healthcare informatics," *Journal of Healthcare Informatics Research*, vol. 5, pp. 1–19, 2021.
- [7] X. Huang, P. Li, H. Du, J. Kang, D. Niyato, D. I. Kim, and Y. Wu, "Federated learning-empowered ai-generated content in wireless networks," *IEEE Network*, 2024.
- [8] S. Ali, Q. Li, and A. Yousafzai, "Blockchain and federated learningbased intrusion detection approaches for edge-enabled industrial iot networks: a survey," *Ad Hoc Networks*, vol. 152, p. 103320, 2024.
- [9] S. A. Rieyan, M. R. K. News, A. M. Rahman, S. A. Khan, S. T. J. Zaarif, M. G. R. Alam, M. M. Hassan, M. Ianni, and G. Fortino, "An advanced data fabric architecture leveraging homomorphic encryption and federated learning," *Information Fusion*, vol. 102, p. 102004, 2024.
- [10] P. Kairouz, H. B. McMahan, B. Avent, A. Bellet, M. Bennis, A. N. Bhagoji, K. Bonawitz, Z. Charles, G. Cormode, R. Cummings *et al.*, "Advances and open problems in federated learning," *Foundations and Trends® in Machine Learning*, vol. 14, no. 1–2, pp. 1–210, 2021.
- [11] Q. Li, Y. Diao, Q. Chen, and B. He, "Federated learning on non-iid data silos: An experimental study," in *2022 IEEE 38th International Conference on Data Engineering (ICDE)*. IEEE, 2022, pp. 965–978.
- [12] K. Hsieh, A. Phanishayee, O. Mutlu, and P. Gibbons, "The non-iid data quagmire of decentralized machine learning," in *International Conference on Machine Learning*. PMLR, 2020, pp. 4387–4398.
- [13] M. Ye, X. Fang, B. Du, P. C. Yuen, and D. Tao, "Heterogeneous federated learning: State-of-the-art and research challenges," *ACM Computing Surveys*, vol. 56, no. 3, pp. 1–44, 2023.
- [14] Y. Zhao, M. Li, L. Lai, N. Suda, D. Civin, and V. Chandra, "Federated learning with non-iid data," *arXiv preprint arXiv:1806.00582*, 2018.
- [15] T. Li, A. K. Sahu, A. Talwalkar, and V. Smith, "Federated learning: Challenges, methods, and future directions," *IEEE signal processing magazine*, vol. 37, no. 3, pp. 50–60, 2020.
- [16] B. Soltani, V. Haghighi, A. Mahmood, Q. Z. Sheng, and L. Yao, "A survey on participant selection for federated learning in mobile networks," in *Proceedings of the 17th ACM Workshop on Mobility in the Evolving Internet Architecture*, 2022, pp. 19–24.
- [17] T. Li, A. K. Sahu, M. Zaheer, M. Sanjabi, A. Talwalkar, and V. Smith, "Federated optimization in heterogeneous networks," *Proceedings of Machine learning and systems*, vol. 2, pp. 429–450, 2020.

- [18] J. Wang, Q. Liu, H. Liang, G. Joshi, and H. V. Poor, "Tackling the objective inconsistency problem in heterogeneous federated optimization," *Advances in neural information processing systems*, vol. 33, pp. 7611–7623, 2020.
- [19] R. Ye, M. Xu, J. Wang, C. Xu, S. Chen, and Y. Wang, "Feddisco: Federated learning with discrepancy-aware collaboration," *arXiv preprint arXiv:2305.19229*, 2023.
- [20] T. Nishio and R. Yonetani, "Client selection for federated learning with heterogeneous resources in mobile edge," in *ICC 2019-2019 IEEE international conference on communications (ICC)*. IEEE, 2019, pp.  $1 - 7$ .
- [21] F. Lai, X. Zhu, H. V. Madhyastha, and M. Chowdhury, "Oort: Efficient federated learning via guided participant selection," in *15th USENIX Symposium on Operating Systems Design and Implementation (OSDI 21)*, 2021, pp. 19–35.
- [22] C. Li, X. Zeng, M. Zhang, and Z. Cao, "Pyramidfl: A fine-grained client selection framework for efficient federated learning," in *Proceedings of the 28th Annual International Conference on Mobile Computing And Networking*, 2022, pp. 158–171.
- [23] J. Ma, X. Sun, W. Xia, X. Wang, X. Chen, and H. Zhu, "Client selection based on label quantity information for federated learning," in *2021 IEEE 32nd Annual International Symposium on Personal, Indoor and Mobile Radio Communications (PIMRC)*. IEEE, 2021, pp. 1–6.
- [24] S. Kornblith, M. Norouzi, H. Lee, and G. Hinton, "Similarity of neural network representations revisited," in *International conference on machine learning*. PMLR, 2019, pp. 3519–3529.
- [25] T. Dong, B. Zhao, and L. Lyu, "Privacy for free: How does dataset condensation help privacy?" in *International Conference on Machine Learning*. PMLR, 2022, pp. 5378–5396.
- [26] Zhao, Bo and Bilen, Hakan, "Dataset condensation with differentiable siamese augmentation," in *International Conference on Machine Learning*. PMLR, 2021, pp. 12 674–12 685.
- [27] Y. LeCun, "The mnist database of handwritten digits," *http://yann. lecun. com/exdb/mnist/*, 1998.
- [28] H. Xiao, K. Rasul, and R. Vollgraf, "Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms," *arXiv preprint arXiv:1708.07747*, 2017.
- [29] Y. Netzer, T. Wang, A. Coates, A. Bissacco, B. Wu, and A. Y. Ng, "Reading digits in natural images with unsupervised feature learning," *NIPS*, 2011.
- [30] A. Krizhevsky, G. Hinton *et al.*, "Learning multiple layers of features from tiny images," *University of Toronto*, 2009.
- [31] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," *arXiv preprint arXiv:2006.05929*, 2020.
- [32] B. Zhao and H. Bilen, "Dataset condensation with distribution matching," in *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, 2023, pp. 6514–6523.
- [33] M. Luo, F. Chen, D. Hu, Y. Zhang, J. Liang, and J. Feng, "No fear of heterogeneity: Classifier calibration for federated learning with non-iid data," *Advances in Neural Information Processing Systems*, vol. 34, pp. 5972–5984, 2021.
- [34] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *arXiv preprint arXiv:1811.10959*, 2018.
- [35] X. Wang, Y. Sun, X. Chen, and H. Xu, "Ddep: Evolutionary pruning using distilled dataset," *Information Sciences*, vol. 659, p. 120048, 2024.
- [36] S. Lei and D. Tao, "A comprehensive survey to dataset distillation," *arXiv preprint arXiv:2301.05603*, 2023.
- [37] T. Nguyen, Z. Chen, and J. Lee, "Dataset meta-learning from kernel ridge-regression," *arXiv preprint arXiv:2011.00050*, 2020.
- [38] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 4750–4759.
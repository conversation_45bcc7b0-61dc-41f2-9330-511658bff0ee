Algorithm 8.3 <PERSON>pler.

- 1. Take some initial values  $U_k^{(0)}$  $k^{(0)}$ ,  $k = 1, 2, \ldots, K$ .
- 2. Repeat for  $t = 1, 2, ...,$ :
  - For  $k = 1, 2, ..., K$  generate  $U_k^{(t)}$  $\kappa^{(\iota)}$  from  $\Pr(U_k^{(t)}$  $\binom{k}{k}$  $\left| U_1^{(t)}, \ldots, U_{k-1}^{(t)}, U_{k+1}^{(t-1)}, \ldots, U_K^{(t-1)} \right|$
- 3. Continue step 2 until the joint distribution of  $(U_1^{(t)}, U_2^{(t)}, \ldots, U_K^{(t)})$ does not change.

dures. For example, one does not need to maximize with respect to all of the latent data parameters at once, but could instead maximize over one of them at a time, alternating with the M step.

# 8.6 MCMC for Sampling from the Posterior

Having defined a Bayesian model, one would like to draw samples from the resulting posterior distribution, in order to make inferences about the parameters. Except for simple models, this is often a difficult computational problem. In this section we discuss the Markov chain Monte Carlo (MCMC) approach to posterior sampling. We will see that <PERSON> sampling, an MCMC procedure, is closely related to the EM algorithm: the main difference is that it samples from the conditional distributions rather than maximizing over them.

Consider first the following abstract problem. We have random variables  $U_1, U_2, \ldots, U_K$  and we wish to draw a sample from their joint distribution. Suppose this is difficult to do, but it is easy to simulate from the conditional distributions  $Pr(U_j | U_1, U_2, \ldots, U_{j-1}, U_{j+1}, \ldots, U_K)$ ,  $j = 1, 2, \ldots, K$ . The Gibbs sampling procedure alternatively simulates from each of these distributions and when the process stabilizes, provides a sample from the desired joint distribution. The procedure is defined in Algorithm 8.3.

Under regularity conditions it can be shown that this procedure eventually stabilizes, and the resulting random variables are indeed a sample from the joint distribution of  $U_1, U_2, \ldots, U_K$ . This occurs despite the fact that the samples  $(U_1^{(t)}, U_2^{(t)}, \ldots, U_K^{(t)})$  are clearly not independent for different t. More formally, Gibbs sampling produces a Markov chain whose stationary distribution is the true joint distribution, and hence the term "Markov chain Monte Carlo." It is not surprising that the true joint distribution is stationary under this process, as the successive steps leave the marginal distributions of the  $U_k$ 's unchanged.

Note that we don't need to know the explicit form of the conditional densities, but just need to be able to sample from them. After the procedure reaches stationarity, the marginal density of any subset of the variables can be approximated by a density estimate applied to the sample values. However if the explicit form of the conditional density  $Pr(U_k, |U_\ell, \ell \neq k)$ is available, a better estimate of say the marginal density of  $U_k$  can be obtained from (Exercise 8.3):

$$
\widehat{\Pr}_{U_k}(u) = \frac{1}{(M-m+1)} \sum_{t=m}^{M} \Pr(u|U_{\ell}^{(t)}, \ell \neq k).
$$
 (8.50)

Here we have averaged over the last  $M - m + 1$  members of the sequence, to allow for an initial "burn-in" period before stationarity is reached.

Now getting back to Bayesian inference, our goal is to draw a sample from the joint posterior of the parameters given the data Z. Gibbs sampling will be helpful if it is easy to sample from the conditional distribution of each parameter given the other parameters and Z. An example—the Gaussian mixture problem—is detailed next.

There is a close connection between Gibbs sampling from a posterior and the EM algorithm in exponential family models. The key is to consider the latent data  $\mathbf{Z}^m$  from the EM procedure to be another parameter for the Gibbs sampler. To make this explicit for the Gaussian mixture problem, we take our parameters to be  $(\theta, \mathbf{Z}^m)$ . For simplicity we fix the variances  $\sigma_1^2, \sigma_2^2$  and mixing proportion  $\pi$  at their maximum likelihood values so that the only unknown parameters in  $\theta$  are the means  $\mu_1$  and  $\mu_2$ . The Gibbs sampler for the mixture problem is given in Algorithm 8.4. We see that steps  $2(a)$  and  $2(b)$  are the same as the E and M steps of the EM procedure, except that we sample rather than maximize. In step  $2(a)$ , rather than compute the maximum likelihood responsibilities  $\gamma_i = \mathbf{E}(\Delta_i | \theta, \mathbf{Z}),$ the Gibbs sampling procedure simulates the latent data  $\Delta_i$  from the distributions  $Pr(\Delta_i | \theta, \mathbf{Z})$ . In step 2(b), rather than compute the maximizers of the posterior  $Pr(\mu_1, \mu_2, \Delta | \mathbf{Z})$  we simulate from the conditional distribution  $Pr(\mu_1, \mu_2 | \Delta, \mathbf{Z}).$ 

Figure 8.8 shows 200 iterations of Gibbs sampling, with the mean parameters  $\mu_1$  (lower) and  $\mu_2$  (upper) shown in the left panel, and the proportion of class 2 observations  $\sum_i \Delta_i/N$  on the right. Horizontal broken lines have been drawn at the maximum likelihood estimate values  $\hat{\mu}_1$ ,  $\hat{\mu}_2$  and  $\sum_i \hat{\gamma}_i/N$ in each case. The values seem to stabilize quite quickly, and are distributed evenly around the maximum likelihood values.

The above mixture model was simplified, in order to make the clear connection between Gibbs sampling and the EM algorithm. More realistically, one would put a prior distribution on the variances  $\sigma_1^2, \sigma_2^2$  and mixing proportion  $\pi$ , and include separate Gibbs sampling steps in which we sample from their posterior distributions, conditional on the other parameters. One can also incorporate proper (informative) priors for the mean param-

## Algorithm 8.4 Gibbs sampling for mixtures.

- 1. Take some initial values  $\theta^{(0)} = (\mu_1^{(0)}, \mu_2^{(0)})$ .
- 2. Repeat for  $t = 1, 2, \ldots,$ .
  - (a) For  $i = 1, 2, ..., N$  generate  $\Delta_i^{(t)} \in \{0, 1\}$  with  $Pr(\Delta_i^{(t)} = 1) =$  $\hat{\gamma}_i(\theta^{(t)})$ , from equation (8.42).
  - (b) Set

$$
\hat{\mu}_1 = \frac{\sum_{i=1}^N (1 - \Delta_i^{(t)}) \cdot y_i}{\sum_{i=1}^N (1 - \Delta_i^{(t)})}
$$

$$
\hat{\mu}_2 = \frac{\sum_{i=1}^N \Delta_i^{(t)} \cdot y_i}{\sum_{i=1}^N \Delta_i^{(t)}}
$$

- and generate  $\mu_1^{(t)} \sim N(\hat{\mu}_1, \hat{\sigma}_1^2)$  and  $\mu_2^{(t)} \sim N(\hat{\mu}_2, \hat{\sigma}_2^2)$ .
- 3. Continue step 2 until the joint distribution of  $(\mathbf{\Delta}^{(t)}, \mu_1^{(t)}, \mu_2^{(t)})$  doesn't change

Image /page/2/Figure/9 description: The image contains two plots side-by-side. The left plot is titled "Mean Parameters" on the y-axis and "Gibbs Iteration" on the x-axis. It shows two lines, one in orange and one in blue, fluctuating between approximately 0 and 8 over 200 Gibbs iterations. Both lines have a dashed horizontal line indicating a mean value. The right plot is titled "Mixing Proportion" on the y-axis and "Gibbs Iteration" on the x-axis. It shows a purple line fluctuating between approximately 0.3 and 0.7 over 200 Gibbs iterations, with a dashed horizontal line indicating a mean value around 0.55.

FIGURE 8.8. Mixture example. (Left panel:) 200 values of the two mean parameters from Gibbs sampling; horizontal lines are drawn at the maximum likelihood estimates  $\hat{\mu}_1$ ,  $\hat{\mu}_2$ . (Right panel:) Proportion of values with  $\Delta_i = 1$ , for each of the 200 Gibbs sampling iterations; a horizontal line is drawn at  $\sum_i \hat{\gamma}_i/N$ .

eters. These priors must not be improper as this will lead to a degenerate posterior, with all the mixing weight on one component.

Gibbs sampling is just one of a number of recently developed procedures for sampling from posterior distributions. It uses conditional sampling of each parameter given the rest, and is useful when the structure of the problem makes this sampling easy to carry out. Other methods do not require such structure, for example the Metropolis–Hastings algorithm. These and other computational Bayesian methods have been applied to sophisticated learning algorithms such as Gaussian process models and neural networks. Details may be found in the references given in the Bibliographic Notes at the end of this chapter.

# 8.7 Bagging

Earlier we introduced the bootstrap as a way of assessing the accuracy of a parameter estimate or a prediction. Here we show how to use the bootstrap to improve the estimate or prediction itself. In Section 8.4 we investigated the relationship between the bootstrap and Bayes approaches, and found that the bootstrap mean is approximately a posterior average. Bagging further exploits this connection.

Consider first the regression problem. Suppose we fit a model to our training data  $\mathbf{Z} = \{(x_1, y_1), (x_2, y_2), \ldots, (x_N, y_N)\}\,$  obtaining the prediction  $\hat{f}(x)$  at input x. Bootstrap aggregation or *bagging* averages this prediction over a collection of bootstrap samples, thereby reducing its variance. For each bootstrap sample  $\mathbf{Z}^{*b}$ ,  $b = 1, 2, ..., B$ , we fit our model, giving prediction  $\hat{f}^{*b}(x)$ . The bagging estimate is defined by

$$
\hat{f}_{\text{bag}}(x) = \frac{1}{B} \sum_{b=1}^{B} \hat{f}^{*b}(x). \tag{8.51}
$$

Denote by  $\hat{\mathcal{P}}$  the empirical distribution putting equal probability  $1/N$  on each of the data points  $(x_i, y_i)$ . In fact the "true" bagging estimate is defined by  $E_{\hat{\mathcal{P}}} \hat{f}^*(x)$ , where  $\mathbf{Z}^* = \{(x_1^*, y_1^*), (x_2^*, y_2^*), \dots, (x_N^*, y_N^*)\}$  and each  $(x_i^*, y_i^*) \sim \hat{P}$ . Expression (8.51) is a Monte Carlo estimate of the true bagging estimate, approaching it as  $B \to \infty$ .

The bagged estimate (8.51) will differ from the original estimate  $\hat{f}(x)$ only when the latter is a nonlinear or adaptive function of the data. For example, to bag the B-spline smooth of Section 8.2.1, we average the curves in the bottom left panel of Figure 8.2 at each value of  $x$ . The  $B$ -spline smoother is linear in the data if we fix the inputs; hence if we sample using the parametric bootstrap in equation (8.6), then  $f_{\text{bag}}(x) \to f(x)$  as  $B \to \infty$ (Exercise 8.4). Hence bagging just reproduces the original smooth in the

top left panel of Figure 8.2. The same is approximately true if we were to bag using the nonparametric bootstrap.

A more interesting example is a regression tree, where  $\hat{f}(x)$  denotes the tree's prediction at input vector  $x$  (regression trees are described in Chapter 9). Each bootstrap tree will typically involve different features than the original, and might have a different number of terminal nodes. The bagged estimate is the average prediction at  $x$  from these  $B$  trees.

Now suppose our tree produces a classifier  $G(x)$  for a K-class response. Here it is useful to consider an underlying indicator-vector function  $\hat{f}(x)$ , with value a single one and  $K - 1$  zeroes, such that  $\hat{G}(x) = \arg \max_k \hat{f}(x)$ . Then the bagged estimate  $\hat{f}_{\text{bag}}(x)$  (8.51) is a K-vector  $[p_1(x), p_2(x), \ldots,$  $p_K(x)$ , with  $p_k(x)$  equal to the proportion of trees predicting class k at x. The bagged classifier selects the class with the most "votes" from the B trees,  $\widetilde{G}_{\text{bag}}(x) = \arg \max_k \widehat{f}_{\text{bag}}(x)$ .

Often we require the class-probability estimates at  $x$ , rather than the classifications themselves. It is tempting to treat the voting proportions  $p_k(x)$  as estimates of these probabilities. A simple two-class example shows that they fail in this regard. Suppose the true probability of class 1 at  $x$  is 0.75, and each of the bagged classifiers accurately predict a 1. Then  $p_1(x) =$ 1, which is incorrect. For many classifiers  $G(x)$ , however, there is already an underlying function  $\hat{f}(x)$  that estimates the class probabilities at x (for trees, the class proportions in the terminal node). An alternative bagging strategy is to average these instead, rather than the vote indicator vectors. Not only does this produce improved estimates of the class probabilities, but it also tends to produce bagged classifiers with lower variance, especially for small  $B$  (see Figure 8.10 in the next example).

## 8.7.1 Example: Trees with Simulated Data

We generated a sample of size  $N = 30$ , with two classes and  $p = 5$  features, each having a standard Gaussian distribution with pairwise correlation 0.95. The response Y was generated according to  $Pr(Y = 1|x_1 \le 0.5) = 0.2$ ,  $Pr(Y = 1 | x_1 > 0.5) = 0.8$ . The Bayes error is 0.2. A test sample of size 2000 was also generated from the same population. We fit classification trees to the training sample and to each of 200 bootstrap samples (classification trees are described in Chapter 9). No pruning was used. Figure 8.9 shows the original tree and eleven bootstrap trees. Notice how the trees are all different, with different splitting features and cutpoints. The test error for the original tree and the bagged tree is shown in Figure 8.10. In this example the trees have high variance due to the correlation in the predictors. Bagging succeeds in smoothing out this variance and hence reducing the test error.

Bagging can dramatically reduce the variance of unstable procedures like trees, leading to improved prediction. A simple argument shows why

Image /page/5/Figure/1 description: The image displays a collection of 12 decision trees, arranged in a 3x4 grid. The top-left tree is labeled 'Original Tree' and is colored orange, while the remaining 11 trees are colored teal and labeled 'b = 1' through 'b = 11'. Each tree shows a series of splits based on feature values, indicated by text above the split point (e.g., 'x.1 < 0.395'). The leaves of the trees are labeled with binary values (0 or 1). The trees illustrate the concept of bagging, where multiple decision trees are trained on different subsets of the data.

FIGURE 8.9. Bagging trees on simulated dataset. The top left panel shows the original tree. Eleven trees grown on bootstrap samples are shown. For each tree, the top split is annotated.

Image /page/6/Figure/1 description: This is a line graph showing the test error as a function of the number of bootstrap samples. The x-axis is labeled "Number of Bootstrap Samples" and ranges from 0 to 200. The y-axis is labeled "Test Error" and ranges from 0.20 to 0.50. There are two lines plotted: one in yellow representing "Consensus" and one in teal representing "Probability". Both lines start at a test error of approximately 0.46 at 0 bootstrap samples. The "Original Tree" is indicated by a dashed blue line at a test error of approximately 0.44. The "Bayes" error is indicated by a dashed black line at a test error of approximately 0.20. The lines for "Consensus" and "Probability" decrease rapidly initially and then level off around a test error of 0.36 as the number of bootstrap samples increases. The text "Bagged Trees" is placed above the leveled-off portion of the lines.

FIGURE 8.10. Error curves for the bagging example of Figure 8.9. Shown is the test error of the original tree and bagged trees as a function of the number of bootstrap samples. The orange points correspond to the consensus vote, while the green points average the probabilities.

bagging helps under squared-error loss, in short because averaging reduces variance and leaves bias unchanged.

Assume our training observations  $(x_i, y_i)$ ,  $i = 1, ..., N$  are independently drawn from a distribution  $P$ , and consider the ideal aggregate estimator  $f_{\text{ag}}(x) = \mathbb{E}_{\mathcal{P}} \hat{f}^*(x)$ . Here x is fixed and the bootstrap dataset  $\mathbf{Z}^*$ consists of observations  $x_i^*, y_i^*, i = 1, 2, ..., N$  sampled from  $P$ . Note that  $f_{\text{ag}}(x)$  is a bagging estimate, drawing bootstrap samples from the actual population  $P$  rather than the data. It is not an estimate that we can use in practice, but is convenient for analysis. We can write

$$
\begin{array}{rcl}\n\mathbf{E}_{\mathcal{P}}[Y - \hat{f}^*(x)]^2 & = & \mathbf{E}_{\mathcal{P}}[Y - f_{\text{ag}}(x) + f_{\text{ag}}(x) - \hat{f}^*(x)]^2 \\
& = & \mathbf{E}_{\mathcal{P}}[Y - f_{\text{ag}}(x)]^2 + \mathbf{E}_{\mathcal{P}}[\hat{f}^*(x) - f_{\text{ag}}(x)]^2 \\
& \geq & \mathbf{E}_{\mathcal{P}}[Y - f_{\text{ag}}(x)]^2.\n\end{array} \tag{8.52}
$$

The extra error on the right-hand side comes from the variance of  $\hat{f}^*(x)$ around its mean  $f_{\text{ag}}(x)$ . Therefore true population aggregation never increases mean squared error. This suggests that bagging—drawing samples from the training data— will often decrease mean-squared error.

The above argument does not hold for classification under 0-1 loss, because of the nonadditivity of bias and variance. In that setting, bagging a

good classifier can make it better, but bagging a bad classifier can make it worse. Here is a simple example, using a randomized rule. Suppose  $Y = 1$ for all x, and the classifier  $G(x)$  predicts  $Y = 1$  (for all x) with probability 0.4 and predicts  $Y = 0$  (for all x) with probability 0.6. Then the misclassification error of  $\hat{G}(x)$  is 0.6 but that of the bagged classifier is 1.0.

For classification we can understand the bagging effect in terms of a consensus of independent weak learners (Dietterich, 2000a). Let the Bayes optimal decision at x be  $G(x) = 1$  in a two-class example. Suppose each of the weak learners  $G_b^*$  have an error-rate  $e_b = e < 0.5$ , and let  $S_1(x) =$  $\sum_{b=1}^{B} I(G_b^*(x) = 1)$  be the consensus vote for class 1. Since the weak learners are assumed to be independent,  $S_1(x) \sim Bin(B, 1-e)$ , and  $Pr(S_1 >$  $B/2$ )  $\rightarrow$  1 as B gets large. This concept has been popularized outside of statistics as the "Wisdom of Crowds" (Surowiecki, 2004) — the collective knowledge of a diverse and independent body of people typically exceeds the knowledge of any single individual, and can be harnessed by voting. Of course, the main caveat here is "independent," and bagged trees are not. Figure 8.11 illustrates the power of a consensus vote in a simulated example, where only 30% of the voters have some knowledge.

In Chapter 15 we see how random forests improve on bagging by reducing the correlation between the sampled trees.

Note that when we bag a model, any simple structure in the model is lost. As an example, a bagged tree is no longer a tree. For interpretation of the model this is clearly a drawback. More stable procedures like nearest neighbors are typically not affected much by bagging. Unfortunately, the unstable models most helped by bagging are unstable because of the emphasis on interpretability, and this is lost in the bagging process.

Figure 8.12 shows an example where bagging doesn't help. The 100 data points shown have two features and two classes, separated by the gray linear boundary  $x_1 + x_2 = 1$ . We choose as our classifier  $G(x)$  a single axis-oriented split, choosing the split along either  $x_1$  or  $x_2$  that produces the largest decrease in training misclassification error.

The decision boundary obtained from bagging the 0-1 decision rule over  $B = 50$  bootstrap samples is shown by the blue curve in the left panel. It does a poor job of capturing the true boundary. The single split rule, derived from the training data, splits near 0 (the middle of the range of  $x_1$ ) or  $x_2$ ), and hence has little contribution away from the center. Averaging the probabilities rather than the classifications does not help here. Bagging estimates the expected class probabilities from the single split rule, that is, averaged over many replications. Note that the expected class probabilities computed by bagging cannot be realized on any single replication, in the same way that a woman cannot have 2.4 children. In this sense, bagging increases somewhat the space of models of the individual base classifier. However, it doesn't help in this and many other examples where a greater enlargement of the model class is needed. "Boosting" is a way of doing this

Image /page/8/Figure/1 description: The image is a line graph titled "Wisdom of Crowds". The x-axis is labeled "P - Probability of Informed Person Being Correct" and ranges from 0.25 to 1.00. The y-axis is labeled "Expected Correct out of 10" and ranges from 0 to 10. There are two lines on the graph: one orange line representing "Consensus" and one teal line representing "Individual". Both lines show an increasing trend as the probability of an informed person being correct increases. The "Consensus" line is consistently above the "Individual" line, indicating that the consensus of a group is expected to be more correct than an individual. Both lines have error bars indicating variability.

FIGURE 8.11. Simulated academy awards voting. 50 members vote in 10 categories, each with 4 nominations. For any category, only 15 voters have some knowledge, represented by their probability of selecting the "correct" candidate in that category (so  $P = 0.25$  means they have no knowledge). For each category, the 15 experts are chosen at random from the 50. Results show the expected correct (based on 50 simulations) for the consensus, as well as for the individuals. The error bars indicate one standard deviation. We see, for example, that if the 15 informed for a category have a 50% chance of selecting the correct candidate, the consensus doubles the expected performance of an individual.

Image /page/9/Figure/1 description: The image displays two plots side-by-side, each illustrating a decision rule applied to a set of data points. The left plot is titled "Bagged Decision Rule" and the right plot is titled "Boosted Decision Rule". Both plots show a square region with a diagonal line from the top-left to the bottom-right corner. Within this region, there are two distinct groups of data points: one group is colored green and located primarily in the upper-right portion, while the other group is colored orange and is located primarily in the lower-left portion. A blue line, representing the decision boundary, is drawn within each plot. The "Bagged Decision Rule" plot shows a smoother, more curved decision boundary, while the "Boosted Decision Rule" plot shows a more jagged, piecewise linear decision boundary that appears to more closely follow the distribution of the data points.

FIGURE 8.12. Data with two features and two classes, separated by a linear boundary. (Left panel:) Decision boundary estimated from bagging the decision rule from a single split, axis-oriented classifier. (Right panel:) Decision boundary from boosting the decision rule of the same classifier. The test error rates are 0.166, and 0.065, respectively. Boosting is described in Chapter 10.

and is described in Chapter 10. The decision boundary in the right panel is the result of the boosting procedure, and it roughly captures the diagonal boundary.

# 8.8 Model Averaging and Stacking

In Section 8.4 we viewed bootstrap values of an estimator as approximate posterior values of a corresponding parameter, from a kind of nonparametric Bayesian analysis. Viewed in this way, the bagged estimate (8.51) is an approximate posterior Bayesian mean. In contrast, the training sample estimate  $\hat{f}(x)$  corresponds to the mode of the posterior. Since the posterior mean (not mode) minimizes squared-error loss, it is not surprising that bagging can often reduce mean squared-error.

Here we discuss Bayesian model averaging more generally. We have a set of candidate models  $\mathcal{M}_m$ ,  $m = 1, \ldots, M$  for our training set **Z**. These models may be of the same type with different parameter values (e.g., subsets in linear regression), or different models for the same task (e.g., neural networks and regression trees).

Suppose  $\zeta$  is some quantity of interest, for example, a prediction  $f(x)$  at some fixed feature value x. The posterior distribution of  $\zeta$  is

$$
\Pr(\zeta|\mathbf{Z}) = \sum_{m=1}^{M} \Pr(\zeta|\mathcal{M}_m, \mathbf{Z}) \Pr(\mathcal{M}_m|\mathbf{Z}), \tag{8.53}
$$

with posterior mean

$$
E(\zeta|\mathbf{Z}) = \sum_{m=1}^{M} E(\zeta|\mathcal{M}_m, \mathbf{Z}) Pr(\mathcal{M}_m|\mathbf{Z}).
$$
\n(8.54)

This Bayesian prediction is a weighted average of the individual predictions, with weights proportional to the posterior probability of each model.

This formulation leads to a number of different model-averaging strategies. Committee methods take a simple unweighted average of the predictions from each model, essentially giving equal probability to each model. More ambitiously, the development in Section 7.7 shows the BIC criterion can be used to estimate posterior model probabilities. This is applicable in cases where the different models arise from the same parametric model, with different parameter values. The BIC gives weight to each model depending on how well it fits and how many parameters it uses. One can also carry out the Bayesian recipe in full. If each model  $\mathcal{M}_m$  has parameters  $\theta_m$ , we write

$$
\Pr(\mathcal{M}_m | \mathbf{Z}) \propto \Pr(\mathcal{M}_m) \cdot \Pr(\mathbf{Z} | \mathcal{M}_m)
$$
  
 
$$
\propto \Pr(\mathcal{M}_m) \cdot \int \Pr(\mathbf{Z} | \theta_m, \mathcal{M}_m) \Pr(\theta_m | \mathcal{M}_m) d\theta_m.
$$
 (8.55)

In principle one can specify priors  $Pr(\theta_m|M_m)$  and numerically compute the posterior probabilities from (8.55), to be used as model-averaging weights. However, we have seen no real evidence that this is worth all of the effort, relative to the much simpler BIC approximation.

How can we approach model averaging from a frequentist viewpoint? Given predictions  $\hat{f}_1(x), \hat{f}_2(x), \dots, \hat{f}_M(x)$ , under squared-error loss, we can seek the weights  $w = (w_1, w_2, \dots, w_M)$  such that

$$
\hat{w} = \underset{w}{\operatorname{argmin}} \mathbf{E}_{\mathcal{P}} \left[ Y - \sum_{m=1}^{M} w_m \hat{f}_m(x) \right]^2.
$$
\n(8.56)

Here the input value  $x$  is fixed and the  $N$  observations in the dataset  $\mathbf{Z}$  (and the target Y are distributed according to  $P$ . The solution is the population linear regression of Y on  $\hat{F}(x)^T \equiv [\hat{f}_1(x), \hat{f}_2(x), \dots, \hat{f}_M(x)]$ :

$$
\hat{w} = \mathbf{E}_{\mathcal{P}}[\hat{F}(x)\hat{F}(x)^{T}]^{-1}\mathbf{E}_{\mathcal{P}}[\hat{F}(x)Y].
$$
\n(8.57)

Now the full regression has smaller error than any single model

$$
\mathcal{E}_{\mathcal{P}}\left[Y - \sum_{m=1}^{M} \hat{w}_m \hat{f}_m(x)\right]^2 \le \mathcal{E}_{\mathcal{P}}\left[Y - \hat{f}_m(x)\right]^2 \forall m \tag{8.58}
$$

so combining models never makes things worse, at the population level.

Of course the population linear regression (8.57) is not available, and it is natural to replace it with the linear regression over the training set. But there are simple examples where this does not work well. For example, if  $f_m(x)$ ,  $m = 1, 2, ..., M$  represent the prediction from the best subset of inputs of size  $m$  among  $M$  total inputs, then linear regression would put all of the weight on the largest model, that is,  $\hat{w}_M = 1$ ,  $\hat{w}_m = 0$ ,  $m < M$ . The problem is that we have not put each of the models on the same footing by taking into account their complexity (the number of inputs  $m$  in this example).

Stacked generalization, or stacking, is a way of doing this. Let  $\hat{f}_m^{-i}(x)$ be the prediction at  $x$ , using model  $m$ , applied to the dataset with the ith training observation removed. The stacking estimate of the weights is obtained from the least squares linear regression of  $y_i$  on  $\hat{f}_m^{-i}(x_i)$ ,  $m =$  $1, 2, \ldots, M$ . In detail the stacking weights are given by

$$
\hat{w}^{\text{st}} = \underset{w}{\text{argmin}} \sum_{i=1}^{N} \left[ y_i - \sum_{m=1}^{M} w_m \hat{f}_m^{-i}(x_i) \right]^2.
$$
 (8.59)

The final prediction is  $\sum_m \hat{w}_m^{\text{st}} \hat{f}_m(x)$ . By using the cross-validated predictions  $\hat{f}^{-i}_{m}(x)$ , stacking avoids giving unfairly high weight to models with higher complexity. Better results can be obtained by restricting the weights to be nonnegative, and to sum to 1. This seems like a reasonable restriction if we interpret the weights as posterior model probabilities as in equation (8.54), and it leads to a tractable quadratic programming problem.

There is a close connection between stacking and model selection via leave-one-out cross-validation (Section 7.10). If we restrict the minimization in  $(8.59)$  to weight vectors w that have one unit weight and the rest zero, this leads to a model choice  $\hat{m}$  with smallest leave-one-out cross-validation error. Rather than choose a single model, stacking combines them with estimated optimal weights. This will often lead to better prediction, but less interpretability than the choice of only one of the M models.

The stacking idea is actually more general than described above. One can use any learning method, not just linear regression, to combine the models as in (8.59); the weights could also depend on the input location x. In this way, learning methods are "stacked" on top of one another, to improve prediction performance.

# 8.9 Stochastic Search: Bumping

The final method described in this chapter does not involve averaging or combining models, but rather is a technique for finding a better single model. Bumping uses bootstrap sampling to move randomly through model space. For problems where fitting method finds many local minima, bumping can help the method to avoid getting stuck in poor solutions.

Image /page/12/Figure/1 description: The image displays two scatter plots side-by-side, each illustrating a 4-node tree classification. The left plot is titled "Regular 4-Node Tree" and the right plot is titled "Bumped 4-Node Tree". Both plots contain numerous orange and teal dots scattered within a rectangular area. A vertical solid line and a horizontal dashed line divide each plot into four quadrants. In the "Regular 4-Node Tree" plot, the top-left and bottom-right quadrants are predominantly filled with orange dots, while the top-right and bottom-left quadrants have a mix of orange and teal dots. The "Bumped 4-Node Tree" plot shows a similar distribution of dots, with the dividing lines appearing to be in slightly different positions, suggesting a different classification strategy.

FIGURE 8.13. Data with two features and two classes (blue and orange), displaying a pure interaction. The left panel shows the partition found by three splits of a standard, greedy, tree-growing algorithm. The vertical grey line near the left edge is the first split, and the broken lines are the two subsequent splits. The algorithm has no idea where to make a good initial split, and makes a poor choice. The right panel shows the near-optimal splits found by bumping the tree-growing algorithm 20 times.

As in bagging, we draw bootstrap samples and fit a model to each. But rather than average the predictions, we choose the model estimated from a bootstrap sample that best fits the training data. In detail, we draw bootstrap samples  $\mathbf{Z}^{*1}, \ldots, \mathbf{Z}^{*B}$  and fit our model to each, giving predictions  $\hat{f}^{*b}(x), b = 1, 2, \ldots, B$  at input point x. We then choose the model that produces the smallest prediction error, averaged over the original training set. For squared error, for example, we choose the model obtained from bootstrap sample  $b$ , where

$$
\hat{b} = \arg\min_{b} \sum_{i=1}^{N} [y_i - \hat{f}^{*b}(x_i)]^2.
$$
\n(8.60)

The corresponding model predictions are  $\hat{f}^{*\hat{b}}(x)$ . By convention we also include the original training sample in the set of bootstrap samples, so that the method is free to pick the original model if it has the lowest training error.

By perturbing the data, bumping tries to move the fitting procedure around to good areas of model space. For example, if a few data points are causing the procedure to find a poor solution, any bootstrap sample that omits those data points should procedure a better solution.

For another example, consider the classification data in Figure 8.13, the notorious exclusive or (XOR) problem. There are two classes (blue and orange) and two input features, with the features exhibiting a pure inter-

action. By splitting the data at  $x_1 = 0$  and then splitting each resulting strata at  $x_2 = 0$ , (or vice versa) a tree-based classifier could achieve perfect discrimination. However, the greedy, short-sighted CART algorithm (Section 9.2) tries to find the best split on either feature, and then splits the resulting strata. Because of the balanced nature of the data, all initial splits on  $x_1$  or  $x_2$  appear to be useless, and the procedure essentially generates a random split at the top level. The actual split found for these data is shown in the left panel of Figure 8.13. By bootstrap sampling from the data, bumping breaks the balance in the classes, and with a reasonable number of bootstrap samples (here 20), it will by chance produce at least one tree with initial split near either  $x_1 = 0$  or  $x_2 = 0$ . Using just 20 bootstrap samples, bumping found the near optimal splits shown in the right panel of Figure 8.13. This shortcoming of the greedy tree-growing algorithm is exacerbated if we add a number of noise features that are independent of the class label. Then the tree-growing algorithm cannot distinguish  $x_1$  or  $x_2$  from the others, and gets seriously lost.

Since bumping compares different models on the training data, one must ensure that the models have roughly the same complexity. In the case of trees, this would mean growing trees with the same number of terminal nodes on each bootstrap sample. Bumping can also help in problems where it is difficult to optimize the fitting criterion, perhaps because of a lack of smoothness. The trick is to optimize a different, more convenient criterion over the bootstrap samples, and then choose the model producing the best results for the desired criterion on the training sample.

# Bibliographic Notes

There are many books on classical statistical inference: Cox and Hinkley (1974) and Silvey (1975) give nontechnical accounts. The bootstrap is due to Efron (1979) and is described more fully in Efron and Tibshirani (1993) and Hall (1992). A good modern book on Bayesian inference is Gelman et al. (1995). A lucid account of the application of Bayesian methods to neural networks is given in Neal (1996). The statistical application of Gibbs sampling is due to Geman and Geman (1984), and Gelfand and Smith (1990), with related work by Tanner and Wong (1987). Markov chain Monte Carlo methods, including Gibbs sampling and the Metropolis– Hastings algorithm, are discussed in Spiegelhalter et al. (1996). The EM algorithm is due to Dempster et al. (1977); as the discussants in that paper make clear, there was much related, earlier work. The view of EM as a joint maximization scheme for a penalized complete-data log-likelihood was elucidated by Neal and Hinton (1998); they credit Csiszar and Tusnády (1984) and Hathaway (1986) as having noticed this connection earlier. Bagging was proposed by Breiman (1996a). Stacking is due to Wolpert (1992); Breiman (1996b) contains an accessible discussion for statisticians. Leblanc and Tibshirani (1996) describe variations on stacking based on the bootstrap. Model averaging in the Bayesian framework has been recently advocated by Madigan and Raftery (1994). Bumping was proposed by Tibshirani and Knight (1999).

## Exercises

Ex. 8.1 Let  $r(y)$  and  $q(y)$  be probability density functions. Jensen's inequality states that for a random variable X and a convex function  $\phi(x)$ ,  $E[\phi(X)] \geq \phi[E(X)]$ . Use Jensen's inequality to show that

$$
E_q \log[r(Y)/q(Y)] \tag{8.61}
$$

is maximized as a function of  $r(y)$  when  $r(y) = q(y)$ . Hence show that  $R(\theta, \theta) \ge R(\theta', \theta)$  as stated below equation (8.46).

Ex. 8.2 Consider the maximization of the log-likelihood (8.48), over distributions  $\tilde{P}(\mathbf{Z}^m)$  such that  $\tilde{P}(\mathbf{Z}^m) \geq 0$  and  $\sum_{\mathbf{Z}^m} \tilde{P}(\mathbf{Z}^m) = 1$ . Use Lagrange multipliers to show that the solution is the conditional distribution  $\widetilde{P}(\mathbf{Z}^m) = \Pr(\mathbf{Z}^m | \mathbf{Z}, \theta'), \text{ as in (8.49)}.$ 

Ex. 8.3 Justify the estimate (8.50), using the relationship

$$
Pr(A) = \int Pr(A|B)d(Pr(B)).
$$

Ex. 8.4 Consider the bagging method of Section 8.7. Let our estimate  $\hat{f}(x)$ be the B-spline smoother  $\hat{\mu}(x)$  of Section 8.2.1. Consider the parametric bootstrap of equation (8.6), applied to this estimator. Show that if we bag  $f(x)$ , using the parametric bootstrap to generate the bootstrap samples, the bagging estimate  $\hat{f}_{\text{bag}}(x)$  converges to the original estimate  $\hat{f}(x)$  as  $B\to\infty$ .

Ex. 8.5 Suggest generalizations of each of the loss functions in Figure 10.4 to more than two classes, and design an appropriate plot to compare them.

Ex. 8.6 Consider the bone mineral density data of Figure 5.6.

- (a) Fit a cubic smooth spline to the relative change in spinal BMD, as a function of age. Use cross-validation to estimate the optimal amount of smoothing. Construct pointwise 90% confidence bands for the underlying function.
- (b) Compute the posterior mean and covariance for the true function via (8.28), and compare the posterior bands to those obtained in (a).

(c) Compute 100 bootstrap replicates of the fitted curves, as in the bottom left panel of Figure 8.2. Compare the results to those obtained in (a) and (b).

Ex. 8.7 EM as a minorization algorithm(Hunter and Lange, 2004; Wu and Lange, 2007). A function  $g(x, y)$  to said to *minorize* a function  $f(x)$  if

$$
g(x, y) \le f(x), \ \ g(x, x) = f(x) \tag{8.62}
$$

for all  $x, y$  in the domain. This is useful for maximizing  $f(x)$  since it is easy to show that  $f(x)$  is non-decreasing under the update

$$
x^{s+1} = \operatorname{argmax}_{x} g(x, x^s) \tag{8.63}
$$

There are analogous definitions for majorization, for minimizing a function  $f(x)$ . The resulting algorithms are known as  $MM$  algorithms, for "Minorize-Maximize" or "Majorize-Minimize."

Show that the EM algorithm (Section 8.5.2) is an example of an MM algorithm, using  $Q(\theta', \theta) + \log \Pr(\mathbf{Z} | \theta) - Q(\theta, \theta)$  to minorize the observed data log-likelihood  $\ell(\theta';\mathbf{Z})$ . (Note that only the first term involves the relevant parameter  $\theta'$ ).

This is page 295 Printer: Opaque this

# Additive Models, Trees, and Related Methods

In this chapter we begin our discussion of some specific methods for supervised learning. These techniques each assume a (different) structured form for the unknown regression function, and by doing so they finesse the curse of dimensionality. Of course, they pay the possible price of misspecifying the model, and so in each case there is a tradeoff that has to be made. They take off where Chapters 3–6 left off. We describe five related techniques: generalized additive models, trees, multivariate adaptive regression splines, the patient rule induction method, and hierarchical mixtures of experts.

# 9.1 Generalized Additive Models

Regression models play an important role in many data analyses, providing prediction and classification rules, and data analytic tools for understanding the importance of different inputs.

Although attractively simple, the traditional linear model often fails in these situations: in real life, effects are often not linear. In earlier chapters we described techniques that used predefined basis functions to achieve nonlinearities. This section describes more automatic flexible statistical methods that may be used to identify and characterize nonlinear regression effects. These methods are called "generalized additive models."

In the regression setting, a generalized additive model has the form

$$
E(Y|X_1, X_2, \dots, X_p) = \alpha + f_1(X_1) + f_2(X_2) + \dots + f_p(X_p).
$$
 (9.1)

9

#### 9. Additive Models, Trees, and Related Methods

As usual  $X_1, X_2, \ldots, X_p$  represent predictors and Y is the outcome; the  $f_i$ 's are unspecified smooth ("nonparametric") functions. If we were to model each function using an expansion of basis functions (as in Chapter 5), the resulting model could then be fit by simple least squares. Our approach here is different: we fit each function using a scatterplot smoother (e.g., a cubic smoothing spline or kernel smoother), and provide an algorithm for simultaneously estimating all  $p$  functions (Section 9.1.1).

For two-class classification, recall the logistic regression model for binary data discussed in Section 4.4. We relate the mean of the binary response  $\mu(X) = Pr(Y = 1|X)$  to the predictors via a linear regression model and the logit link function:

$$
\log\left(\frac{\mu(X)}{1-\mu(X)}\right) = \alpha + \beta_1 X_1 + \dots + \beta_p X_p. \tag{9.2}
$$

The additive logistic regression model replaces each linear term by a more general functional form

$$
\log\left(\frac{\mu(X)}{1-\mu(X)}\right) = \alpha + f_1(X_1) + \dots + f_p(X_p),\tag{9.3}
$$

where again each  $f_j$  is an unspecified smooth function. While the nonparametric form for the functions  $f_j$  makes the model more flexible, the additivity is retained and allows us to interpret the model in much the same way as before. The additive logistic regression model is an example of a generalized additive model. In general, the conditional mean  $\mu(X)$  of a response  $Y$  is related to an additive function of the predictors via a *link* function *q*:

$$
g[\mu(X)] = \alpha + f_1(X_1) + \dots + f_p(X_p).
$$
 (9.4)

Examples of classical link functions are the following:

- $g(\mu) = \mu$  is the identity link, used for linear and additive models for Gaussian response data.
- $g(\mu) = \text{logit}(\mu)$  as above, or  $g(\mu) = \text{probit}(\mu)$ , the *probit* link function, for modeling binomial probabilities. The probit function is the inverse Gaussian cumulative distribution function:  $\text{probit}(\mu) = \Phi^{-1}(\mu)$ .
- $g(\mu) = \log(\mu)$  for log-linear or log-additive models for Poisson count data.

All three of these arise from exponential family sampling models, which in addition include the gamma and negative-binomial distributions. These families generate the well-known class of generalized linear models, which are all extended in the same way to generalized additive models.

The functions  $f_j$  are estimated in a flexible manner, using an algorithm whose basic building block is a scatterplot smoother. The estimated function  $f_j$  can then reveal possible nonlinearities in the effect of  $X_j$ . Not all of the functions  $f_j$  need to be nonlinear. We can easily mix in linear and other parametric forms with the nonlinear terms, a necessity when some of the inputs are qualitative variables (factors). The nonlinear terms are not restricted to main effects either; we can have nonlinear components in two or more variables, or separate curves in  $X_j$  for each level of the factor  $X_k$ . Thus each of the following would qualify:

- $g(\mu) = X^T \beta + \alpha_k + f(Z)$ —a semiparametric model, where X is a vector of predictors to be modeled linearly,  $\alpha_k$  the effect for the kth level of a qualitative input  $V$ , and the effect of predictor  $Z$  is modeled nonparametrically.
- $g(\mu) = f(X) + g_k(Z)$ —again k indexes the levels of a qualitative input V, and thus creates an interaction term  $g(V, Z) = g_k(Z)$  for the effect of  $V$  and  $Z$ .
- $g(\mu) = f(X) + g(Z, W)$  where g is a nonparametric function in two features.

Additive models can replace linear models in a wide variety of settings, for example an additive decomposition of time series,

$$
Y_t = S_t + T_t + \varepsilon_t,\tag{9.5}
$$

where  $S_t$  is a seasonal component,  $T_t$  is a trend and  $\varepsilon$  is an error term.

## 9.1.1 Fitting Additive Models

In this section we describe a modular algorithm for fitting additive models and their generalizations. The building block is the scatterplot smoother for fitting nonlinear effects in a flexible way. For concreteness we use as our scatterplot smoother the cubic smoothing spline described in Chapter 5.

The additive model has the form

$$
Y = \alpha + \sum_{j=1}^{p} f_j(X_j) + \varepsilon,
$$
\n(9.6)

where the error term  $\varepsilon$  has mean zero. Given observations  $x_i, y_i$ , a criterion like the penalized sum of squares (5.9) of Section 5.4 can be specified for this problem,

$$
PRSS(\alpha, f_1, f_2, \dots, f_p) = \sum_{i=1}^{N} \left( y_i - \alpha - \sum_{j=1}^{p} f_j(x_{ij}) \right)^2 + \sum_{j=1}^{p} \lambda_j \int f_j''(t_j)^2 dt_j,
$$
\n(9.7)

where the  $\lambda_j \geq 0$  are tuning parameters. It can be shown that the minimizer of (9.7) is an additive cubic spline model; each of the functions  $f_j$  is a Algorithm 9.1 The Backfitting Algorithm for Additive Models.

- 1. Initialize:  $\hat{\alpha} = \frac{1}{N} \sum_{i=1}^{N} y_i, \hat{f}_j \equiv 0, \forall i, j$ .
- 2. Cycle:  $j = 1, 2, \ldots, p, \ldots, 1, 2, \ldots, p, \ldots$

$$
\hat{f}_j \leftarrow \mathcal{S}_j \left[ \{ y_i - \hat{\alpha} - \sum_{k \neq j} \hat{f}_k(x_{ik}) \}_1^N \right],
$$
  
$$
\hat{f}_j \leftarrow \hat{f}_j - \frac{1}{N} \sum_{i=1}^N \hat{f}_j(x_{ij}).
$$

until the functions  $\hat{f}_j$  change less than a prespecified threshold.

cubic spline in the component  $X_j$ , with knots at each of the unique values of  $x_{ij}$ ,  $i = 1, \ldots, N$ . However, without further restrictions on the model, the solution is not unique. The constant  $\alpha$  is not identifiable, since we can add or subtract any constants to each of the functions  $f_j$ , and adjust  $\alpha$  accordingly. The standard convention is to assume that  $\sum_{1}^{N} f_j(x_{ij}) =$ 0  $\forall j$ —the functions average zero over the data. It is easily seen that  $\hat{\alpha} =$  $ave(y_i)$  in this case. If in addition to this restriction, the matrix of input values (having *ij*th entry  $x_{ij}$ ) has full column rank, then  $(9.7)$  is a strictly convex criterion and the minimizer is unique. If the matrix is singular, then the *linear part* of the components  $f_j$  cannot be uniquely determined (while the nonlinear parts can!)(Buja et al., 1989).

Furthermore, a simple iterative procedure exists for finding the solution. We set  $\hat{\alpha} = \text{ave}(y_i)$ , and it never changes. We apply a cubic smoothing spline  $S_j$  to the targets  $\{y_i - \hat{\alpha} - \sum_{k \neq j} \hat{f}_k(x_{ik})\}_1^N$ , as a function of  $x_{ij}$ , to obtain a new estimate  $\hat{f}_j$ . This is done for each predictor in turn, using the current estimates of the other functions  $\hat{f}_k$  when computing  $y_i - \hat{\alpha} - \sum_{k \in \mathcal{S}} \hat{f}_k(x_{ik})$ . The process is continued until the estimates  $\hat{f}_i$  stabilize. This  $\hat{f}_{k\neq j}$   $\hat{f}_{k}(x_{ik})$ . The process is continued until the estimates  $\hat{f}_{j}$  stabilize. This procedure, given in detail in Algorithm 9.1, is known as "backfitting" and the resulting fit is analogous to a multiple regression for linear models.

In principle, the second step in (2) of Algorithm 9.1 is not needed, since the smoothing spline fit to a mean-zero response has mean zero (Exercise 9.1). In practice, machine rounding can cause slippage, and the adjustment is advised.

This same algorithm can accommodate other fitting methods in exactly the same way, by specifying appropriate smoothing operators  $S_i$ :

• other univariate regression smoothers such as local polynomial regression and kernel methods;

- linear regression operators yielding polynomial fits, piecewise constant fits, parametric spline fits, series and Fourier fits;
- more complicated operators such as surface smoothers for second or higher-order interactions or periodic smoothers for seasonal effects.

If we consider the operation of smoother  $S_j$  only at the training points, it can be represented by an  $N \times N$  operator matrix  $S_j$  (see Section 5.4.1). Then the degrees of freedom for the jth term are (approximately) computed as  $df_j = \text{trace}[\mathbf{S}_j] - 1$ , by analogy with degrees of freedom for smoothers discussed in Chapters 5 and 6.

For a large class of linear smoothers  $S_i$ , backfitting is equivalent to a Gauss–Seidel algorithm for solving a certain linear system of equations. Details are given in Exercise 9.2.

For the logistic regression model and other generalized additive models, the appropriate criterion is a penalized log-likelihood. To maximize it, the backfitting procedure is used in conjunction with a likelihood maximizer. The usual Newton–Raphson routine for maximizing log-likelihoods in generalized linear models can be recast as an IRLS (iteratively reweighted least squares) algorithm. This involves repeatedly fitting a weighted linear regression of a working response variable on the covariates; each regression yields a new value of the parameter estimates, which in turn give new working responses and weights, and the process is iterated (see Section 4.4.1). In the generalized additive model, the weighted linear regression is simply replaced by a weighted backfitting algorithm. We describe the algorithm in more detail for logistic regression below, and more generally in Chapter 6 of Hastie and Tibshirani (1990).

## 9.1.2 Example: Additive Logistic Regression

Probably the most widely used model in medical research is the logistic model for binary data. In this model the outcome  $Y$  can be coded as 0 or 1, with 1 indicating an event (like death or relapse of a disease) and 0 indicating no event. We wish to model  $Pr(Y = 1|X)$ , the probability of an event given values of the prognostic factors  $X^T = (X_1, \ldots, X_p)$ . The goal is usually to understand the roles of the prognostic factors, rather than to classify new individuals. Logistic models are also used in applications where one is interested in estimating the class probabilities, for use in risk screening. Apart from medical applications, credit risk screening is a popular application.

The generalized additive logistic model has the form

$$
\log \frac{\Pr(Y=1|X)}{\Pr(Y=0|X)} = \alpha + f_1(X_1) + \dots + f_p(X_p). \tag{9.8}
$$

The functions  $f_1, f_2, \ldots, f_p$  are estimated by a backfitting algorithm within a Newton–Raphson procedure, shown in Algorithm 9.2.

Algorithm 9.2 Local Scoring Algorithm for the Additive Logistic Regression Model.

- 1. Compute starting values:  $\hat{\alpha} = \log[\bar{y}/(1-\bar{y})]$ , where  $\bar{y} = \text{ave}(y_i)$ , the sample proportion of ones, and set  $\hat{f}_j \equiv 0 \ \forall j$ .
- 2. Define  $\hat{\eta}_i = \hat{\alpha} + \sum_j \hat{f}_j(x_{ij})$  and  $\hat{p}_i = 1/[1 + \exp(-\hat{\eta}_i)].$

Iterate:

(a) Construct the working target variable

$$
z_i = \hat{\eta}_i + \frac{(y_i - \hat{p}_i)}{\hat{p}_i(1 - \hat{p}_i)}.
$$

- (b) Construct weights  $w_i = \hat{p}_i(1-\hat{p}_i)$
- (c) Fit an additive model to the targets  $z_i$  with weights  $w_i$ , using a weighted backfitting algorithm. This gives new estimates  $\hat{\alpha}, \hat{f}_j, \forall j$
- 3. Continue step 2. until the change in the functions falls below a prespecified threshold.

The additive model fitting in step (2) of Algorithm 9.2 requires a weighted scatterplot smoother. Most smoothing procedures can accept observation weights (Exercise 5.12); see Chapter 3 of Hastie and Tibshirani (1990) for further details.

The additive logistic regression model can be generalized further to handle more than two classes, using the multilogit formulation as outlined in Section 4.4. While the formulation is a straightforward extension of (9.8), the algorithms for fitting such models are more complex. See Yee and Wild (1996) for details, and the VGAM software currently available from:

## 

## Example: Predicting Email Spam

We apply a generalized additive model to the spam data introduced in Chapter 1. The data consists of information from 4601 email messages, in a study to screen email for "spam" (i.e., junk email). The data is publicly available at ftp.ics.uci.edu, and was donated by George Forman from Hewlett-Packard laboratories, Palo Alto, California.

The response variable is binary, with values email or spam, and there are 57 predictors as described below:

• 48 quantitative predictors—the percentage of words in the email that match a given word. Examples include business, address, internet,

|             | Predicted Class |            |  |  |  |
|-------------|-----------------|------------|--|--|--|
| True Class  | email $(0)$     | spam $(1)$ |  |  |  |
| email $(0)$ | 58.3%           | $2.5\%$    |  |  |  |
| spam $(1)$  | $3.0\%$         | 36.3%      |  |  |  |

TABLE 9.1. Test data confusion matrix for the additive logistic regression model fit to the spam training data. The overall test error rate is 5.5%.

free, and george. The idea was that these could be customized for individual users.

- 6 quantitative predictors—the percentage of characters in the email that match a given character. The characters are ch;, ch(, ch[, ch!, ch\$, and ch#.
- The average length of uninterrupted sequences of capital letters: CAPAVE.
- The length of the longest uninterrupted sequence of capital letters: CAPMAX.
- The sum of the length of uninterrupted sequences of capital letters: CAPTOT.

We coded spam as 1 and email as zero. A test set of size 1536 was randomly chosen, leaving 3065 observations in the training set. A generalized additive model was fit, using a cubic smoothing spline with a nominal four degrees of freedom for each predictor. What this means is that for each predictor  $X_i$ , the smoothing-spline parameter  $\lambda_i$  was chosen so that trace[ $S_i(\lambda_i)$ ]−1 = 4, where  $\mathbf{S}_{j}(\lambda)$  is the smoothing spline operator matrix constructed using the observed values  $x_{ij}$ ,  $i = 1, ..., N$ . This is a convenient way of specifying the amount of smoothing in such a complex model.

Most of the spam predictors have a very long-tailed distribution. Before fitting the GAM model, we log-transformed each variable (actually  $\log(x+)$ 0.1)), but the plots in Figure 9.1 are shown as a function of the original variables.

The test error rates are shown in Table 9.1; the overall error rate is 5.3%. By comparison, a linear logistic regression has a test error rate of 7.6%. Table 9.2 shows the predictors that are highly significant in the additive model.

For ease of interpretation, in Table 9.2 the contribution for each variable is decomposed into a linear component and the remaining nonlinear component. The top block of predictors are positively correlated with spam, while the bottom block is negatively correlated. The linear component is a weighted least squares linear fit of the fitted curve on the predictor, while the nonlinear part is the residual. The linear component of an estimated

TABLE 9.2. Significant predictors from the additive model fit to the spam training data. The coefficients represent the linear part of  $\hat{f}_j$ , along with their standard errors and Z-score. The nonlinear P-value is for a test of nonlinearity of  $\hat{f}_j$ .

| Name             | Num. | df  | Coefficient | Std. Error | Z Score | Nonlinear<br>P-value |
|------------------|------|-----|-------------|------------|---------|----------------------|
| Positive effects |      |     |             |            |         |                      |
| our              | 5    | 3.9 | 0.566       | 0.114      | 4.970   | 0.052                |
| over             | 6    | 3.9 | 0.244       | 0.195      | 1.249   | 0.004                |
| remove           | 7    | 4.0 | 0.949       | 0.183      | 5.201   | 0.093                |
| internet         | 8    | 4.0 | 0.524       | 0.176      | 2.974   | 0.028                |
| free             | 16   | 3.9 | 0.507       | 0.127      | 4.010   | 0.065                |
| business         | 17   | 3.8 | 0.779       | 0.186      | 4.179   | 0.194                |
| hpl              | 26   | 3.8 | 0.045       | 0.250      | 0.181   | 0.002                |
| ch!              | 52   | 4.0 | 0.674       | 0.128      | 5.283   | 0.164                |
| ch\$             | 53   | 3.9 | 1.419       | 0.280      | 5.062   | 0.354                |
| CAPMAX           | 56   | 3.8 | 0.247       | 0.228      | 1.080   | 0.000                |
| CAPTOT           | 57   | 4.0 | 0.755       | 0.165      | 4.566   | 0.063                |
| Negative effects |      |     |             |            |         |                      |
| hp               | 25   | 3.9 | -1.404      | 0.224      | -6.262  | 0.140                |
| george           | 27   | 3.7 | -5.003      | 0.744      | -6.722  | 0.045                |
| 1999             | 37   | 3.8 | -0.672      | 0.191      | -3.512  | 0.011                |
| re               | 45   | 3.9 | -0.620      | 0.133      | -4.649  | 0.597                |
| edu              | 46   | 4.0 | -1.183      | 0.209      | -5.647  | 0.000                |

function is summarized by the coefficient, standard error and Z-score; the latter is the coefficient divided by its standard error, and is considered significant if it exceeds the appropriate quantile of a standard normal distribution. The column labeled nonlinear P-value is a test of nonlinearity of the estimated function. Note, however, that the effect of each predictor is fully adjusted for the entire effects of the other predictors, not just for their linear parts. The predictors shown in the table were judged significant by at least one of the tests (linear or nonlinear) at the  $p = 0.01$  level (two-sided).

Figure 9.1 shows the estimated functions for the significant predictors appearing in Table 9.2. Many of the nonlinear effects appear to account for a strong discontinuity at zero. For example, the probability of spam drops significantly as the frequency of george increases from zero, but then does not change much after that. This suggests that one might replace each of the frequency predictors by an indicator variable for a zero count, and resort to a linear logistic model. This gave a test error rate of 7.4%; including the linear effects of the frequencies as well dropped the test error to 6.6%. It appears that the nonlinearities in the additive model have an additional predictive power.

FIGURE 9.1. Spam analysis: estimated functions for significant predictors. The rug plot along the bottom of each frame indicates the observed values of the corresponding predictor. For many of the predictors the nonlinearity picks up the discontinuity at zero.

#### 9. Additive Models, Trees, and Related Methods

It is more serious to classify a genuine email message as spam, since then a good email would be filtered out and would not reach the user. We can alter the balance between the class error rates by changing the losses (see Section 2.4). If we assign a loss  $L_{01}$  for predicting a true class 0 as class 1, and  $L_{10}$  for predicting a true class 1 as class 0, then the estimated Bayes rule predicts class 1 if its probability is greater than  $L_{01}/(L_{01} + L_{10})$ . For example, if we take  $L_{01} = 10, L_{10} = 1$  then the (true) class 0 and class 1 error rates change to 0.8% and 8.7%.

More ambitiously, we can encourage the model to fit better data in the class 0 by using weights  $L_{01}$  for the class 0 observations and  $L_{10}$  for the class 1 observations. As above, we then use the estimated Bayes rule to predict. This gave error rates of  $1.2\%$  and  $8.0\%$  in (true) class 0 and class 1, respectively. We discuss below the issue of unequal losses further, in the context of tree-based models.

After fitting an additive model, one should check whether the inclusion of some interactions can significantly improve the fit. This can be done "manually," by inserting products of some or all of the significant inputs, or automatically via the MARS procedure (Section 9.4).

This example uses the additive model in an automatic fashion. As a data analysis tool, additive models are often used in a more interactive fashion, adding and dropping terms to determine their effect. By calibrating the amount of smoothing in terms of  $df_j$ , one can move seamlessly between linear models (df<sub>j</sub> = 1) and partially linear models, where some terms are modeled more flexibly. See Hastie and Tibshirani (1990) for more details.

## 9.1.3 Summary

Additive models provide a useful extension of linear models, making them more flexible while still retaining much of their interpretability. The familiar tools for modeling and inference in linear models are also available for additive models, seen for example in Table 9.2. The backfitting procedure for fitting these models is simple and modular, allowing one to choose a fitting method appropriate for each input variable. As a result they have become widely used in the statistical community.

However additive models can have limitations for large data-mining applications. The backfitting algorithm fits all predictors, which is not feasible or desirable when a large number are available. The BRUTO procedure (Hastie and Tibshirani, 1990, Chapter 9) combines backfitting with selection of inputs, but is not designed for large data-mining problems. There has also been recent work using lasso-type penalties to estimate sparse additive models, for example the COSSO procedure of Lin and Zhang (2006) and the SpAM proposal of Ravikumar et al. (2008). For large problems a forward stagewise approach such as boosting (Chapter 10) is more effective, and also allows for interactions to be included in the model.
# **8**

# **Statistical Divergences**

We study in this chapter the statistical properties of the Wasserstein distance. More specifically, we compare it to other major distances and divergences routinely used in data sciences. We quantify how one can approximate the distance between two probability distributions when having only access to samples from said distributions. To introduce these subjects, §8.1 and §8.2 review respectively divergences and integral probability metrics between probability distributions. A divergence *D* typically satisfies  $D(\alpha, \beta) > 0$  and  $D(\alpha, \beta) = 0$  if and only if  $\alpha = \beta$ , but it does not need to be symmetric or satisfy the triangular inequality. An integral probability metric for measures is a dual norm defined using a prescribed family of test functions. These quantities are sound alternatives to Wasserstein distances and are routinely used as loss functions to tackle inference problems, as will be covered in §9. We show first in §8.3 that the optimal transport distance is not Hilbertian, *i.e.* one cannot approximate it efficiently using a Hilbertian metric on a suitable feature representation of probability measures. We show in §8.4 how to approximate  $D(\alpha, \beta)$  from discrete samples  $(x_i)_i$  and  $(y_j)_j$ drawn from *α* and *β*. A good statistical understanding of that problem is crucial when using the <PERSON>ser<PERSON> distance in machine learning. Note that this section will be chiefly concerned with the statistical approximation of optimal transport between distributions supported on *continuous* sets. The very same problem when the ground space is finite has received some attention in the literature following the work of <PERSON><PERSON><PERSON> and <PERSON><PERSON> [2018], extended to entropic regularized quantities by Bigot et al. [2017a].

#### **8.1** *ϕ***-Divergences**

Before detailing in the following section "weak" norms, whose construction shares similarities with  $\mathcal{W}_1$ , let us detail a generic construction of so-called divergences between measures, which can then be used as loss functions when estimating probability distributions. Such divergences compare two input measures by comparing their mass *pointwise*, without introducing any notion of mass transportation. Divergences are functionals which, by looking at the pointwise ratio between two measures, give a sense of how close they are. They have nice analytical and computational properties and build upon *entropy functions*.

**Definition 8.1** (Entropy function). A function  $\varphi : \mathbb{R} \to \mathbb{R} \cup \{\infty\}$  is an entropy function if it is lower semicontinuous, convex, dom  $\varphi \subset [0,\infty]$ , and satisfies the following feasibility condition: dom  $\varphi \cap [0, \infty) \neq \emptyset$ . The speed of growth of  $\varphi$  at  $\infty$  is described by

$$
\varphi'_\infty = \lim_{x \to +\infty} \varphi(x)/x \in \mathbb{R} \cup \{\infty\}.
$$

If  $\varphi'_{\infty} = \infty$ , then  $\varphi$  grows faster than any linear function and  $\varphi$  is said *superlinear*. Any entropy function *ϕ* induces a *ϕ*-divergence (also known as Ciszár divergence [Ciszár, 1967, Ali and Silvey, 1966] or *f*-divergence) as follows.

**Definition 8.2** ( $\varphi$ -Divergences). Let  $\varphi$  be an entropy function. For  $\alpha, \beta \in \mathcal{M}(\mathcal{X})$ , let d*α*  $\frac{d\alpha}{d\beta}\beta + \alpha^{\perp}$  be the Lebesgue decomposition<sup>1</sup> of  $\alpha$  with respect to  $\beta$ . The divergence  $\mathcal{D}_{\varphi}$ is defined by

$$
\mathcal{D}_{\varphi}(\alpha|\beta) \stackrel{\text{def.}}{=} \int_{\mathcal{X}} \varphi\left(\frac{d\alpha}{d\beta}\right) d\beta + \varphi_{\infty}' \alpha^{\perp}(\mathcal{X}) \tag{8.1}
$$

if  $\alpha, \beta$  are nonnegative and  $\infty$  otherwise.

The additional term  $\varphi'_\infty \alpha^\perp(\mathcal{X})$  in (8.1) is important to ensure that  $\mathcal{D}_{\varphi}$  defines a continuous functional (for the weak topology of measures) even if  $\varphi$  has a linear growth at infinity, as this is, for instance, the case for the absolute value (8.8) defining the TV norm. If  $\varphi$  as a superlinear growth, *e.g.* the usual entropy (8.4), then  $\varphi'_{\infty} = +\infty$  so that  $\mathcal{D}_{\varphi}(\alpha|\beta) = +\infty$  if  $\alpha$  does not have a density with respect to  $\beta$ .

In the discrete setting, assuming

$$
\alpha = \sum_{i} \mathbf{a}_{i} \delta_{x_{i}} \quad \text{and} \quad \beta = \sum_{i} \mathbf{b}_{i} \delta_{x_{i}} \tag{8.2}
$$

are supported on the same set of *n* points  $(x_i)_{i=1}^n \subset \mathcal{X}$ , (8.1) defines a divergence on Σ*<sup>n</sup>*

$$
\mathbf{D}_{\varphi}(\mathbf{a}|\mathbf{b}) = \sum_{i \in \text{Supp}(\mathbf{b})} \varphi\left(\frac{\mathbf{a}_i}{\mathbf{b}_i}\right) \mathbf{b}_i + \varphi_{\infty}' \sum_{i \notin \text{Supp}(\mathbf{b})} \mathbf{a}_i,
$$
\n(8.3)

<sup>&</sup>lt;sup>1</sup>The Lebesgue decomposition theorem asserts that, given  $\beta$ ,  $\alpha$  admits a unique decomposition as the sum of two measures  $\alpha^s + \alpha^{\perp}$  such that  $\alpha^s$  is absolutely continuous with respect to  $\beta$  and  $\alpha^{\perp}$  and *β* are singular.

where  $\text{Supp}(\mathbf{b}) \stackrel{\text{def.}}{=} \{i \in [\![n]\!] : b_i \neq 0\}.$ 

The proof of the following proposition can be found in [Liero et al., 2018, Thm 2.7].

**Proposition 8.1.** If  $\varphi$  is an entropy function, then  $\mathcal{D}_{\varphi}$  is jointly 1-homogeneous, convex and weakly<sup>\*</sup> lower semicontinuous in  $(\alpha, \beta)$ .

Image /page/2/Figure/4 description: A line graph displays four different functions plotted against a horizontal axis ranging from 0 to 3. The vertical axis ranges from 0 to 4. The four functions are labeled in a legend: KL (blue line), TV (orange line), Hellinger (yellow line), and χ² (purple line). All four functions start at a value of approximately 1 at the horizontal axis value of 0. The Hellinger function decreases to 0 at the horizontal axis value of 1, then increases. The KL function decreases to a value slightly above 0 at the horizontal axis value of 1, then increases. The TV function decreases to 0 at the horizontal axis value of 1, then increases. The χ² function decreases to a value slightly above 0 at the horizontal axis value of 1, then increases more steeply than the other functions. At the horizontal axis value of 3, the KL function is approximately 1.2, the TV function is approximately 2, the Hellinger function is approximately 0.8, and the χ² function is approximately 4.

**Figure 8.1:** Example of entropy functionals.

**Remark 8.1** (Dual expression). A  $\varphi$ -divergence can be expressed using the Legendre transform

$$
\varphi^*(s) \stackrel{\text{\tiny def.}}{=} \sup_{t \in \mathbb{R}} st - \varphi(t)
$$

of  $\varphi$  (see also (4.54)) as

$$
\mathcal{D}_{\varphi}(\alpha|\beta) = \sup_{f:\mathcal{X}\to\mathbb{R}} \int_{\mathcal{X}} f(x) \mathrm{d}\alpha(x) - \int_{\mathcal{X}} \varphi^*(f(x)) \mathrm{d}\beta(x);
$$

see Liero et al. [2018] for more details.

We now review a few popular instances of this framework. Figure 8.1 displays the associated entropy functionals, while Figure 8.2 reviews the relationship between them.

**Example 8.1** (Kullback–Leibler divergence). The Kullback–Leibler divergence KL  $\stackrel{\text{def.}}{=}$  $\mathcal{D}_{\varphi_{\text{KL}}}$ , also known as the relative entropy, was already introduced in (4.10) and (4.6). It is the divergence associated to the Shannon–Boltzman entropy function  $\varphi_{\text{KL}}$ , given by

$$
\varphi_{\text{KL}}(s) = \begin{cases} s \log(s) - s + 1 & \text{for } s > 0, \\ 1 & \text{for } s = 0, \\ +\infty & \text{otherwise.} \end{cases} \tag{8.4}
$$

116

#### 8.1. $\varphi$ *-Divergences* 117

Image /page/3/Figure/1 description: This is a diagram illustrating relationships between different metrics: KL, W1, TV, and dH. Arrows indicate the direction of the relationship, and labels on the arrows specify the conditions or inequalities. For example, there is an arrow from KL to \"X^2\" labeled KL \le log(1 + \"X^2\"). There is also an arrow from W1 to TV labeled W1 \le dmaxTV, and a dashed arrow from TV to W1 labeled TV \le W1/dmin. Other relationships shown include TV \le \sqrt{KL}/2, dH \le \sqrt{KL}, TV \le \sqrt{\"X^2\"}/2, dH \le \sqrt{2}TV, and TV \le dH.

**Figure 8.2:** Diagram of relationship between divergences (inspired by Gibbs and Su [2002]). For  $\mathcal{X}$  a metric space with ground distance  $d$ ,  $d_{\text{max}} = \sup_{(x,x')} d(x,x')$  is the diameter of X. When X is discrete,  $d_{\min} \stackrel{\text{def.}}{=} \min_{x \neq x'} d(x, x').$ 

**Remark 8.1** (Bregman divergence). The discrete KL divergence,  $KL \stackrel{\text{def.}}{=} D_{\varphi_{KL}}$ , has the unique property of being both a  $\varphi$ -divergence and a Bregman divergence. For discrete vectors in  $\mathbb{R}^n$ , a Bregman divergence [Bregman, 1967] associated to a smooth strictly convex function  $\psi : \mathbb{R}^n \to \mathbb{R}$  is defined as

$$
\mathbf{B}_{\psi}(\mathbf{a}|\mathbf{b}) \stackrel{\text{def.}}{=} \psi(\mathbf{a}) - \psi(\mathbf{b}) - \langle \nabla \psi(\mathbf{b}), \mathbf{a} - \mathbf{b} \rangle, \tag{8.5}
$$

where  $\langle \cdot, \cdot \rangle$  is the canonical inner product on  $\mathbb{R}^n$ . Note that  $\mathbf{B}_{\psi}(\mathbf{a}|\mathbf{b})$  is a convex function of **a** and a linear function of  $\psi$ . Similarly to  $\varphi$ -divergence, a Bregman divergence satisfies  $\mathbf{B}_{\psi}(\mathbf{a}|\mathbf{b}) \geq 0$  and  $\mathbf{B}_{\psi}(\mathbf{a}|\mathbf{b}) = 0$  if and only if  $\mathbf{a} = \mathbf{b}$ . The KL divergence is the Bregman divergence for minus the entropy  $\psi = -\mathbf{H}$  defined in (4.1)), *i.e.*  $\mathbf{KL} = \mathbf{B}_{-\mathbf{H}}$ . A Bregman divergence is locally a squared Euclidean distance since

$$
\mathbf{B}_{\psi}(\mathbf{a} + \varepsilon \vert \mathbf{a} + \eta) = \langle \partial^2 \psi(\mathbf{a})(\varepsilon - \eta), \, \varepsilon - \eta \rangle + o(\Vert \varepsilon - \eta \Vert^2)
$$

and the set of separating points  $\{a : B_{\psi}(a|b) = B_{\psi}(a|b')\}$  is a hyperplane between **b** and **b**'. These properties make Bregman divergence suitable to replace Euclidean distances in first order optimization methods. The best know example is mirror gradient descent [Beck and Teboulle, 2003], which is an explicit descent step of the form (9.32). Bregman divergences are also important in convex optimization and can be used, for instance, to derive Sinkhorn iterations and study its convergence in finite dimension; see Remark 4.8.

**Remark 8.2** (Hyperbolic geometry of KL)**.** It is interesting to contrast the geometry of the Kullback–Leibler divergence to that defined by quadratic optimal transport when comparing Gaussians. As detailed, for instance, by Costa et al. [2015], the Kullback– Leibler divergence has a closed form for Gaussian densities. In the univariate case,

 $d = 1$ , if  $\alpha = \mathcal{N}(m_{\alpha}, \sigma_{\alpha}^2)$  and  $\beta = \mathcal{N}(m_{\beta}, \sigma_{\beta}^2)$ , one has

$$
KL(\alpha|\beta) = \frac{1}{2} \left( \frac{\sigma_{\alpha}^2}{\sigma_{\beta}^2} + \log \left( \frac{\sigma_{\beta}^2}{\sigma_{\alpha}^2} \right) + \frac{|m_{\alpha} - m_{\beta}|}{\sigma_{\beta}^2} - 1 \right).
$$
 (8.6)

This expression shows that the divergence between  $\alpha$  and  $\beta$  diverges to infinity as  $\sigma_{\beta}$ diminishes to 0 and *β* becomes a Dirac mass. In that sense, one can say that singular Gaussians are infinitely far from all other Gaussians in the KL geometry. That geometry is thus useful when one wants to avoid dealing with singular covariances. To simplify the analysis, one can look at the infinitesimal geometry of KL, which is obtained by performing a Taylor expansion at order 2,

$$
KL(\mathcal{N}(m+\delta_m, (\sigma+\delta_\sigma)^2)|\mathcal{N}(m, \sigma^2)) = \frac{1}{\sigma^2} \left(\frac{1}{2}\delta_m^2 + \delta_\sigma^2\right) + o(\delta_m^2, \delta_\sigma^2).
$$

This local Riemannian metric, the so-called Fisher metric, expressed over  $(m/\sqrt{2}, \sigma) \in$ **R** × **R**+*,*∗, matches exactly that of the hyperbolic Poincaré half plane. Geodesics over this space are half circles centered along the  $\sigma = 0$  line and have an exponential speed, *i.e.* they only reach the limit  $\sigma = 0$  after an infinite time. Note in particular that if  $\sigma_{\alpha} = \sigma_{\beta}$  but  $m_{\alpha} \neq m_{\alpha}$ , then the geodesic between  $(\alpha, \beta)$  over this hyperbolic half plane does not have a constant standard deviation.

The KL hyperbolic geometry over the space of Gaussian parameters  $(m, \sigma)$  should be contrasted with the Euclidean geometry associated to OT as described in Remark 2.31, since in the univariate case

$$
\mathcal{W}_2^2(\alpha, \beta) = |m_\alpha - m_\beta|^2 + |\sigma_\alpha - \sigma_\beta|^2. \tag{8.7}
$$

Figure 8.3 shows a visual comparison of these two geometries and their respective geodesics. This interesting comparison was suggested to us by Jean Feydy.

**Example 8.2** (Total variation). The total variation distance TV  $\stackrel{\text{def.}}{=} \mathcal{D}_{\varphi_{TV}}$  is the divergence associated to

$$
\varphi_{\rm TV}(s) = \begin{cases} |s-1| & \text{for } s \ge 0, \\ +\infty & \text{otherwise.} \end{cases} \tag{8.8}
$$

It actually defines a norm on the full space of measure  $\mathcal{M}(\mathcal{X})$  where

$$
TV(\alpha|\beta) = \|\alpha - \beta\|_{TV}, \quad \text{where} \quad \|\alpha\|_{TV} = |\alpha|(\mathcal{X}) = \int_{\mathcal{X}} d|\alpha|(x). \tag{8.9}
$$

If  $\alpha$  has a density  $\rho_{\alpha}$  on  $\mathcal{X} = \mathbb{R}^d$ , then the TV norm is the  $L^1$  norm on functions,  $\|\alpha\|_{TV} = \int_{\mathcal{X}} |\rho_{\alpha}(x)| dx = \|\rho_{\alpha}\|_{L^{1}}$ . If  $\alpha$  is discrete as in (8.2), then the TV norm is the  $\ell^1$  norm of vectors in  $\mathbb{R}^n$ ,  $\|\alpha\|_{TV} = \sum_i |\mathbf{a}_i| = ||\mathbf{a}||_{\ell^1}$ .

Image /page/5/Figure/1 description: The image displays two plots side-by-side, each with a top panel showing a curve on a scatter plot and a bottom panel showing a series of probability density functions. The top panels have a y-axis labeled "σ" and an x-axis labeled "m". Both top panels show a dashed semi-circular arc. The left top panel shows a curve that starts at the origin, arcs upwards and to the right, and ends on the semi-circle. The curve is marked with dots that transition in color from red to blue. A dashed line connects the origin to the blue dot on the semi-circle. The bottom panel of the left plot, labeled "KL", shows a series of bell-shaped curves that start as a sharp peak near the origin and broaden and flatten as they move to the right, with colors transitioning from red to blue. The right top panel shows a curve that starts at the origin and moves linearly upwards and to the right, ending on the semi-circle. This curve is also marked with dots transitioning from red to blue. The bottom panel of the right plot, labeled "OT", shows a series of probability density functions that start as a sharp peak near the origin and gradually shift to the right and broaden, with colors transitioning from red to blue.

**Figure 8.3:** Comparisons of interpolation between Gaussians using KL (hyperbolic) and OT (Euclidean) geometries.

**Remark 8.2** (Strong vs. weak topology)**.** The total variation norm (8.9) defines the socalled "strong" topology on the space of measure. On a compact domain  $\mathcal X$  of radius *R*, one has

$$
\mathcal{W}_1(\alpha, \beta) \le R \left\| \alpha - \beta \right\|_{\text{TV}}
$$

so that this strong notion of convergence implies the weak convergence metrized by Wasserstein distances. The converse is, however, not true, since  $\delta_x$  does not converge strongly to  $\delta_y$  if  $x \to y$  (note that  $\|\delta_x - \delta_y\|_{TV} = 2$  if  $x \neq y$ ). A chief advantage is that  $\mathcal{M}^1_+(\mathcal{X})$  (once again on a compact ground space  $\mathcal{X}$ ) is compact for the weak topology, so that from any sequence of probability measures  $(\alpha_k)_k$ , one can always extract a converging subsequence, which makes it a suitable space for several optimization problems, such as those considered in Chapter 9.

**Example 8.3** (Hellinger). The Hellinger distance  $\mathfrak{h} \stackrel{\text{def.}}{=} \mathcal{D}_{\varphi_H}^{1/2}$  is the square root of the divergence associated to

$$
\varphi_H(s) = \begin{cases} |\sqrt{s} - 1|^2 & \text{for } s \ge 0, \\ +\infty & \text{otherwise.} \end{cases}
$$

As its name suggests, h is a distance on  $\mathcal{M}_+(\mathcal{X})$ , which metrizes the strong topology as  $\|\cdot\|_{TV}$ . If  $(\alpha, \beta)$  have densities  $(\rho_\alpha, \rho_\beta)$  on  $\mathcal{X} = \mathbb{R}^d$ , then  $\mathfrak{h}(\alpha, \beta) = \|\sqrt{\rho_\alpha} - \sqrt{\rho_\beta}\|_{L^2}$ . If  $(\alpha, \beta)$  are discrete as in (8.2), then  $\mathfrak{h}(\alpha, \beta) = ||\sqrt{\mathbf{a}} -$ √ **b**<sup>|</sup>|. Considering  $\varphi_{L^p}(s)$  =  $|s^{1/p}-1|^p$  generalizes the Hellinger  $(p=2)$  and total variation  $(p=1)$  distances and  $\mathcal{D}^{1/p}_{\varphi_{LP}}$  is a distance which metrizes the strong convergence for  $0 < p < +\infty$ .

**Example 8.4** (Jensen–Shannon distance)**.** The KL divergence is not symmetric and, while being a Bregman divergence (which are locally quadratic norms), it is not the square of a distance. On the other hand, the Jensen–Shannon distance  $JS(\alpha, \beta)$ , defined as

$$
JS(\alpha, \beta)^2 \stackrel{\text{def.}}{=} \frac{1}{2} \left( KL(\alpha | \xi) + KL(\beta | \xi) \right) \text{ where } \xi = \frac{\alpha + \beta}{2},
$$

is a distance [Endres and Schindelin, 2003, Österreicher and Vajda, 2003].  $JS<sup>2</sup>$  can be shown to be a  $\varphi$ -divergence for  $\varphi(s) = t \log(t) - (t+1) \log(t+1)$ . In sharp contrast with KL,  $JS(\alpha, \beta)$  is always bounded; more precisely, it satisfies  $0 \leq JS(\alpha, \beta)^2 \leq \ln(2)$ . Similarly to the TV norm and the Hellinger distance, it metrizes the strong convergence.

**Example 8.5** ( $\chi^2$ ). The  $\chi^2$ -divergence  $\chi^2 \stackrel{\text{def.}}{=} \mathcal{D}_{\varphi_{\chi^2}}$  is the divergence associated to

$$
\varphi_{\chi^2}(s) = \begin{cases} |s-1|^2 & \text{for } s \ge 0, \\ +\infty & \text{otherwise.} \end{cases}
$$

If  $(\alpha, \beta)$  are discrete as in  $(8.2)$  and have the same support, then

$$
\chi^2(\alpha|\beta) = \sum_i \frac{(\mathbf{a}_i - \mathbf{b}_i)^2}{\mathbf{b}_i}.
$$

#### **8.2 Integral Probability Metrics**

Formulation (6.3) is a special case of a dual norm. A dual norm is a convenient way to design "weak" norms that can deal with arbitrary measures. For a symmetric convex set *B* of measurable functions, one defines

$$
\|\alpha\|_{B} \stackrel{\text{def.}}{=} \max_{f} \left\{ \int_{\mathcal{X}} f(x) \mathrm{d}\alpha(x) : f \in B \right\}.
$$
 (8.10)

These dual norms are often called "integral probability metrics'; see [Sriperumbudur et al., 2012].

**Example 8.6** (Total variation)**.** The total variation norm (Example 8.2) is a dual norm associated to the whole space of continuous functions

$$
B = \{ f \in C(\mathcal{X}) : ||f||_{\infty} \leq 1 \}.
$$

The total variation distance is the only nontrivial divergence that is also a dual norm; see [Sriperumbudur et al., 2009].

**Remark 8.3** (Metrizing the weak convergence)**.** By using smaller "balls" *B*, which typically only contain continuous (and sometimes regular) functions, one defines weaker dual norms. In order for  $\lVert \cdot \rVert_B$  to metrize the weak convergence (see Definition 2.2), it is sufficient for the space spanned by *B* to be dense in the set of continuous functions for the sup-norm  $\lVert \cdot \rVert_{\infty}$  (i.e. for the topology of uniform convergence); see [Ambrosio et al., 2006, para. 5.1].

Figure 8.4 displays a comparison of several such dual norms, which we now detail.

Image /page/7/Figure/3 description: The image displays two plots side-by-side, each with a legend indicating four curves: Energy (blue), Gauss (orange), W1 (yellow), and Flat (purple). Both plots have a y-axis ranging from 0 to 3 and an x-axis ranging from -3 to 3. The bottom of each plot contains a label indicating the parameters used. The left plot is labeled "(α, β) = (δ0, δt)", and the right plot is labeled "(α, β) = (δ0, 1/2(δ−t/2 + δt/2))". Both plots show similar shapes for the curves, with the W1 curve being a straight yellow line increasing from 0 to 3 across the x-axis. The Gauss curve is orange and plateaus at 1.5 for x > 1 and x < -1. The Flat curve is purple and stays at a constant value of 1. The Energy curve (blue) is a V-shape with its minimum at x=0, reaching approximately 2.5 at x=-3 and x=3.

**Figure 8.4:** Comparison of dual norms.

#### **8.2.1** $W_1$ and Flat Norm

If the set *B* is bounded, then  $\lVert \cdot \rVert_B$  is a norm on the whole space  $\mathcal{M}(\mathcal{X})$  of measures. This is not the case of  $W_1$ , which is only defined for  $\alpha$  such that  $\int_{\mathcal{X}} d\alpha = 0$  (otherwise  $\|\alpha\|_B = +\infty$ ). This can be alleviated by imposing a bound on the value of the potential *f*, in order to define for instance the flat norm.

**Example 8.7** ( $W_1$  norm).  $W_1$  as defined in (6.3), is a special case of dual norm (8.10), using

$$
B = \{ f : \operatorname{Lip}(f) \le 1 \}
$$

the set of 1-Lipschitz functions.

**Example 8.8** (Flat norm and Dudley metric)**.** The flat norm is defined using

$$
B = \{ f : \|\nabla f\|_{\infty} \le 1 \text{ and } \|f\|_{\infty} \le 1 \}.
$$
 (8.11)

It metrizes the weak convergence on the whole space  $\mathcal{M}(\mathcal{X})$ . Formula (6.2) is extended to compute the flat norm by adding the constraint  $|\mathbf{f}_k| \leq 1$ . The flat norm is sometimes called the "Kantorovich–Rubinstein" norm [Hanin, 1992] and has been used as a fidelity term for inverse problems in imaging [Lellmann et al., 2014]. The flat norm is similar to the Dudley metric, which uses

$$
B = \{ f : ||\nabla f||_{\infty} + ||f||_{\infty} \le 1 \}.
$$

### **8.2.2 Dual RKHS Norms and Maximum Mean Discrepancies**

It is also possible to define "Euclidean" norms (built using quadratic functionals) on measures using the machinery of kernel methods and more specifically reproducing kernel Hilbert spaces (RKHS; see [Schölkopf and Smola, 2002] for a survey of their applications in data sciences), of which we recall first some basic definitions.

**Definition 8.3.** A symmetric function *k* (resp.,  $\varphi$ ) defined on a set  $\mathcal{X} \times \mathcal{X}$  is said to be positive (resp., negative) definite if for any  $n \geq 0$ , family  $x_1, \ldots, x_n \in \mathcal{Z}$ , and vector  $r \in \mathbb{R}^n$  the following inequality holds:

$$
\sum_{i,j=1}^{n} r_i r_j k(x_i, x_j) \ge 0, \quad \left(\text{resp.} \quad \sum_{i,j=1}^{n} r_i r_j \varphi(x_i, x_j) \le 0\right). \tag{8.12}
$$

The kernel is said to be conditionally positive if positivity only holds in (8.12) for zero mean vectors *r* (i.e. such that  $\langle r, \mathbb{1}_n \rangle = 0$ ).

If *k* is conditionally positive, one defines the following norm:

$$
\|\alpha\|_{k}^{2} \stackrel{\text{def.}}{=} \int_{\mathcal{X} \times \mathcal{X}} k(x, y) d\alpha(x) d\alpha(y).
$$
 (8.13)

These norms are often referred to as "maximum mean discrepancy" (MMD) (see [Gretton et al., 2007]) and have also been called "kernel norms" in shape analysis [Glaunes et al., 2004]. This expression (8.13) can be rephrased, introducing two independent random vectors  $(X, X')$  on X distributed with law  $\alpha$ , as

$$
\|\alpha\|_k^2 = \mathbb{E}_{X,X'}(k(X,X')).
$$

One can show that  $\lVert \cdot \rVert_k^2$  is the dual norm in the sense of  $(8.10)$  associated to the unit ball *B* of the RKHS associated to *k*. We refer to [Berlinet and Thomas-Agnan, 2003, Hofmann et al., 2008, Schölkopf and Smola, 2002] for more details on RKHS functional spaces.

**Remark 8.4** (Universal kernels). According to Remark 8.3, the MMD norm  $\lVert \cdot \rVert_k$  metrizes the weak convergence if the span of the dual ball  $B$  is dense in the space of continuous functions  $\mathcal{C}(\mathcal{X})$ . This means that finite sums of the form  $\sum_{i=1}^{n} a_i k(x_i, \cdot)$  (for arbitrary choice of *n* and points  $(x_i)_i$  are dense in  $\mathcal{C}(\mathcal{X})$  for the uniform norm  $\lVert \cdot \rVert_{\infty}$ . For translation-invariant kernels over  $\mathcal{X} = \mathbb{R}^d$ ,  $k(x, y) = k_0(x - y)$ , this is equivalent to having a nonvanishing Fourier transform,  $\hat{k}_0(\omega) > 0$ .

In the special case where  $\alpha$  is a discrete measure of the form  $(2.3)$ , one thus has the simple expression

$$
\|\alpha\|_{k}^{2} = \sum_{i=1}^{n} \sum_{i'=1}^{n} \mathbf{a}_{i} \mathbf{a}_{i'} \mathbf{k}_{i,i'} = \langle \mathbf{k} \mathbf{a}, \, \mathbf{a} \rangle \quad \text{where} \quad \mathbf{k}_{i,i'} \stackrel{\text{def}}{=} k(x_i, x_{i'}).
$$

#### 8.2. Integral Probability Metrics **123**

In particular, when  $\alpha = \sum_{i=1}^{n} a_i \delta_{x_i}$  and  $\beta = \sum_{i=1}^{n} b_i \delta_{x_i}$  are supported on the same set of points,  $\|\alpha - \beta\|_k^2 = \langle \mathbf{k(a - b)}, \mathbf{a - b} \rangle$ , so that  $\|\cdot\|_k$  is a Euclidean norm (proper if **k** is positive definite, degenerate otherwise if **k** is semidefinite) on the simplex  $\Sigma_n$ . To compute the discrepancy between two discrete measures of the form (2.3), one can use

$$
\|\alpha - \beta\|_{k}^{2} = \sum_{i,i'} \mathbf{a}_{i} \mathbf{a}_{i'} k(x_{i}, x_{i'}) + \sum_{j,j'} \mathbf{b}_{j} \mathbf{b}_{j'} k(y_{j}, y_{j'}) - 2 \sum_{i,j} \mathbf{a}_{i} \mathbf{b}_{j} k(x_{i}, y_{j}). \tag{8.14}
$$

**Example 8.9** (Gaussian RKHS)**.** One of the most popular kernels is the Gaussian one  $k(x,y) = e^{-\frac{||x-y||^2}{2\sigma^2}}$  $\frac{2g}{2\sigma^2}$ , which is a positive universal kernel on  $\mathcal{X} = \mathbb{R}^d$ . An attractive feature of the Gaussian kernel is that it is separable as a product of 1-D kernels, which facilitates computations when working on regular grids (see also Remark 4.17). However, an important issue that arises when using the Gaussian kernel is that one needs to select the bandwidth parameter  $\sigma$ . This bandwidth should match the "typical" scale" between observations in the measures to be compared. If the measures have multiscale features (some regions may be very dense, others very sparsely populated), a Gaussian kernel is thus not well adapted, and one should consider a "scale-free" kernel as we detail next. An issue with such scale-free kernels is that they are global (have slow polynomial decay), which makes them typically computationally more expensive, since no compact support approximation is possible. Figure 8.5 shows a comparison between several kernels.

Image /page/9/Figure/4 description: The image displays a collection of visualizations, likely from a scientific or mathematical context. The top row features four circular or Gaussian-like plots, each with a central point and concentric rings, varying in size and intensity. The bottom row presents five more complex contour plots, rendered in shades of red and blue, suggesting positive and negative values. These plots are labeled below with text: "(α, β)", "ED(R^2, ||·||)", "(G, .005)", "(G, .02)", and "(G, .05)". The first plot in the bottom row is a colorful, intertwined curve in blue and red.

**Figure 8.5:** Top row: display of  $\psi$  such that  $\|\alpha - \beta\|_k = \|\psi \star (\alpha - \beta)\|_{L^2(\mathbb{R}^2)}$ , formally defined over Fourier as  $\hat{\psi}(\omega) = \sqrt{\hat{k}_0(\omega)}$ , where  $k(x, x') = k_0(x - x')$ . Bottom row: display of  $\psi \star (\alpha - \beta)$ . (G, $\sigma$ ) stands for Gaussian kernel of variance  $\sigma^2$ . The kernel for  $ED(\mathbb{R}^2, \|\cdot\|)$  is  $\psi(x) = 1/\sqrt{\|x\|}$ .

**Example 8.10**  $(H^{-1}(\mathbb{R}^d))$ . Another important dual norm is  $H^{-1}(\mathbb{R}^d)$ , the dual (over distributions) of the Sobolev space  $H^1(\mathbb{R}^d)$  of functions having derivatives in  $L^2(\mathbb{R}^d)$ . It is defined using the primal RKHS norm  $\|\nabla f\|_{L^2(\mathbb{R}^d)}^2$ . It is not defined for singular measures (*e.g.* Diracs) unless  $d = 1$  because functions in the Sobolev space  $H^1(\mathbb{R}^d)$  are in general not continuous. This  $H^{-1}$  norm (defined on the space of zero mean measures with densities) can also be formulated in divergence form,

$$
\|\alpha - \beta\|_{H^{-1}(\mathbb{R}^d)}^2 = \min_s \left\{ \int_{\mathbb{R}^d} \|s(x)\|_2^2 \, \mathrm{d}x \; : \; \text{div}(s) = \alpha - \beta \right\},\tag{8.15}
$$

which should be contrasted with  $(6.4)$ , where an  $L<sup>1</sup>$  norm of the vector field *s* was used in place of the *L* <sup>2</sup> norm used here. The "weighted" version of this Sobolev dual norm,

$$
\|\rho\|_{H^{-1}(\alpha)}^2 = \min_{\mathrm{div}(s) = \rho} \int_{\mathbb{R}^d} \|s(x)\|_2^2 \, \mathrm{d}\alpha(x),
$$

can be interpreted as the natural "linearization" of the Wasserstein  $\mathcal{W}_2$  norm, in the sense that the Benamou–Brenier dynamic formulation can be interpreted infinitesimally as

$$
\mathcal{W}_2(\alpha, \alpha + \varepsilon \rho) = \varepsilon \|\rho\|_{H^{-1}(\alpha)} + o(\varepsilon). \tag{8.16}
$$

The functionals  $\mathcal{W}_2(\alpha, \beta)$  and  $\|\alpha - \beta\|_{H^{-1}(\alpha)}$  can be shown to be equivalent [Peyre, 2011]. The issue is that  $\|\alpha - \beta\|_{H^{-1}(\alpha)}$  is not a norm (because of the weighting by  $\alpha$ ), and one cannot in general replace it by  $\|\alpha - \beta\|_{H^{-1}(\mathbb{R}^d)}$  unless  $(\alpha, \beta)$  have densities. In this case, if  $\alpha$  and  $\beta$  have densities on the same support bounded from below by  $a > 0$ and from above by  $b < +\infty$ , then

$$
b^{-1/2} \|\alpha - \beta\|_{H^{-1}(\mathbb{R}^d)} \le W_2(\alpha, \beta) \le a^{-1/2} \|\alpha - \beta\|_{H^{-1}(\mathbb{R}^d)}; \tag{8.17}
$$

see [Santambrogio, 2015, Theo. 5.34], and see [Peyre, 2011] for sharp constants.

**Example 8.11** (Negative Sobolev spaces)**.** One can generalize this construction by considering the Sobolev space  $H^{-r}(\mathbb{R}^d)$  of arbitrary negative index, which is the dual of the functional Sobolev space  $H^r(\mathbb{R}^d)$  of functions having r derivatives (in the sense of distributions) in  $L^2(\mathbb{R}^d)$ . In order to metrize the weak convergence, one needs functions in  $H^r(\mathbb{R}^d)$  to be continuous, which is the case when  $r > d/2$ . As the dimension *d* increases, one thus needs to consider higher regularity. For arbitrary  $\alpha$  (not necessarily integers), these spaces are defined using the Fourier transform, and for a measure *α* with Fourier transform  $\hat{\alpha}(\omega)$  (written here as a density with respect to the Lebesgue measure d*ω*)

$$
\|\alpha\|_{H^{-r}(\mathbb{R}^d)}^2 \stackrel{\text{def.}}{=} \int_{\mathbb{R}^d} \|\omega\|^{-2r} |\hat{\alpha}(\omega)|^2 d\omega.
$$

This corresponds to a dual RKHS norm with a convolutive kernel  $k(x, y) = k_0(x - y)$ with  $\hat{k}_0(\omega) = \pm ||\omega||^{-2r}$ . Taking the inverse Fourier transform, one sees that (up to

124

#### 8.3. Wasserstein Spaces Are Not Hilbertian 125

constant) one has

$$
\forall x \in \mathbb{R}^d, \quad k_0(x) = \begin{cases} \frac{1}{\|x\|^{d-2r}} & \text{if } r < d/2, \\ -\|x\|^{2r-d} & \text{if } r > d/2. \end{cases} \tag{8.18}
$$

**Example 8.12** (Energy distance). The energy distance (or Cramer distance when  $d =$ 1) [Székely and Rizzo, 2004] associated to a distance *d* is defined as

$$
\|\alpha - \beta\|_{\text{ED}(\mathcal{X}, d^p)} \stackrel{\text{def.}}{=} \|\alpha - \beta\|_{k_{\text{ED}}} \quad \text{where} \quad k_{\text{ED}}(x, y) = -d(x, y)^p \tag{8.19}
$$

for  $0 < p < 2$ . It is a valid MMD norm over measures if d is negative definite (see Definition 8.3), a typical example being the Euclidean distance  $d(x, y) = ||x - y||$ . For  $\mathcal{X} = \mathbb{R}^d$ ,  $d(x, y) = ||\cdot||$ , using (8.18), one sees that the energy distance is a Sobolev norm

$$
\|\cdot\|_{\text{ED}(\mathbb{R}^d,\|\cdot\|^p)} = \|\cdot\|_{H^{-\frac{d+p}{2}}(\mathbb{R}^d)}.
$$

A chief advantage of the energy distance over more usual kernels such as the Gaussian (Example 8.9) is that it is scale-free and does not depend on a bandwidth parameter *σ*. More precisely, one has the following scaling behavior on  $\mathcal{X} = \mathbb{R}^d$ , when denoting  $f_s(x) = sx$  the dilation by a factor  $s > 0$ ,

$$
\|f_{s\sharp}(\alpha-\beta)\|_{\mathrm{ED}(\mathbb{R}^d,\|\cdot\|^p)}=s^{\frac{p}{2}}\,\|\alpha-\beta\|_{\mathrm{ED}(\mathbb{R}^d,\|\cdot\|^p)}\,,
$$

while the Wasserstein distance exhibits a perfect linear scaling,

$$
\mathcal{W}_p(f_{s\sharp}\alpha, f_{s\sharp}\beta)) = s \mathcal{W}_p(\alpha, \beta)).
$$

Note, however, that for the energy distance, the parameter  $p$  must satisfy  $0 < p < 2$ , and that for  $p = 2$ , it degenerates to the distance between the means

$$
\|\alpha - \beta\|_{\text{ED}(\mathbb{R}^d, \|\cdot\|^2)} = \left\| \int_{\mathbb{R}^d} x(\mathrm{d}\alpha(x) - \mathrm{d}\beta(x)) \right\|,
$$

so it is not a norm anymore. This shows that it is not possible to get the same linear scaling under  $f_{s\sharp}$  with the energy distance as for the Wasserstein distance.

### **8.3 Wasserstein Spaces Are Not Hilbertian**

Some of the special cases of the Wasserstein geometry outlined earlier in §2.6 have highlighted the fact that the optimal transport distance can sometimes be computed in closed form. They also illustrate that in such cases the optimal transport distance is a *Hilbertian* metric between probability measures, in the sense that there exists a map *φ* from the space of input measures onto a Hilbert space, as defined below.

**Definition 8.4.** A distance *d* defined on a set  $\mathcal{Z} \times \mathcal{Z}$  is said to be Hilbertian if there exists a Hilbert space H and a mapping  $\phi : \mathcal{Z} \to \mathcal{H}$  such that for any pair  $z, z'$  in  $\mathcal{Z}$  we have that  $d(z, z') = ||\phi(z) - \phi(z')||_{\mathcal{H}}$ .

For instance, Remark 2.30 shows that the Wasserstein metric is a Hilbert norm between univariate distributions, simply by defining  $\phi$  to be the map that associates to a measure its generalized quantile function. Remark 2.31 shows that for univariate Gaussians, as written in (8.7) in this chapter, the Wasserstein distance between two univariate Gaussians is simply the Euclidean distance between their mean and standard deviation.

Hilbertian distances have many favorable properties when used in a data analysis context [Dattorro, 2017]. First, they can be easily cast as radial basis function kernels: for any Hilbertian distance *d*, it is indeed known that  $e^{-d^p/t}$  is a positive definite kernel for any value  $0 \leq p \leq 2$  and any positive scalar t as shown in [Berg et al., 1984, Cor. 3.3.3, Prop. 3.2.7. The Gaussian  $(p = 2)$  and Laplace  $(p = 1)$  kernels are simple applications of that result using the usual Euclidean distance. The entire field of kernel methods [Hofmann et al., 2008] builds upon the positive definiteness of a kernel function to define convex learning algorithms operating on positive definite kernel matrices. Points living in a Hilbertian space can also be efficiently embedded in lower dimensions with low distortion factors [Johnson and Lindenstrauss, 1984], [Barvinok, 2002, §V.6.2] using simple methods such as multidimensional scaling [Borg and Groenen, 2005].

Because Hilbertian distances have such properties, one might hope that the Wasserstein distance remains Hilbertian in more general settings than those outlined above, notably when the dimension of  $X$  is 2 and more. This can be disproved using the following equivalence.

**Proposition 8.1.** A distance  $d$  is Hilbertian if and only if  $d^2$  is negative definite.

*Proof.* If a distance is Hilbertian, then  $d^2$  is trivially negative definite. Indeed, given *n* points in Z, the sum  $\sum r_i r_j d^2(z_i, z_j)$  can be rewritten as  $\sum r_i r_j ||\phi(z_i) - \phi(z_j)||_{\mathcal{H}}^2$  which can be expanded, taking advantage of the fact that  $\sum r_i = 0$  to  $-2\sum r_i r_j \langle \phi(z_i), \phi(z_j) \rangle_H$ which is negative by definition of a Hilbert dot product. If, on the contrary,  $d^2$  is negative definite, then the fact that *d* is Hilbertian proceeds from a key result by Schoenberg [1938] outlined in ([Berg et al., 1984, p. 82, Prop. 3.2]).  $\Box$ 

It is therefore sufficient to show that the squared Wasserstein distance is not negative definite to show that it is not Hilbertian, as stated in the following proposition.

**Proposition 8.2.** If  $\mathcal{X} = \mathbb{R}^d$  with  $d \geq 2$  and the ground cost is set to  $d(x, y) = ||x - y||_2$ , then the *p*-Wasserstein distance is not Hilbertian for  $p = 1, 2$ .

*Proof.* It suffices to prove the result for *d* = 2 since any counterexample in that dimension suffices to obtain a counterexample in any higher dimension. We provide a nonrandom counterexample which works using measures supported on four vectors  $x^1, x^2, x^3, x^4 \in \mathbb{R}^2$  defined as follows:  $x^1 = [0, 0], x^2 = [1, 0], x^3 = [0, 1], x^4 = [1, 1].$  We now consider all points on the regular grid on the simplex of four dimensions, with

126

#### 8.3. Wasserstein Spaces Are Not Hilbertian 127

increments of 1/4. There are  $35 = \binom{4}{4} = \binom{4+4-1}{4}$  $\binom{4-1}{4}$  such points in the simplex. Each probability vector  $\mathbf{a}^i$  on that grid is such that for  $j \leq 4$ , we have that  $\mathbf{a}^i_j$  is in the set  $\{0, \frac{1}{4}$  $\frac{1}{4}, \frac{1}{2}$  $\frac{1}{2}, \frac{3}{4}$  $\frac{3}{4}$ , 1} and such that  $\sum_{j=1}^{4} a_j^i = 1$ . For a given p, the 35 × 35 pairwise Wasserstein distance matrix  $D_p$  between these histograms can be computed.  $D_p$  is not negative definite if and only if its elementwise square  $\mathbf{D}_p^2$  is such that  $\mathbf{JD}_p^2\mathbf{J}$  has positive eigenvalues, where **J** is the centering matrix  $\mathbf{J} = \mathbb{I}_n - \frac{1}{n}$  $\frac{1}{n}\mathbb{1}_{n,n}$ , which is the case as illustrated in Figure 8.6.  $\Box$ 

Image /page/13/Figure/2 description: The image is a line graph showing the relationship between the 'p parameter to define p-Wasserstein' on the x-axis and the 'Max. Eig. of Centered Distance Matrix' on the y-axis. The x-axis ranges from 1 to 4, with tick marks at 1, 1.5, 2, 2.5, 3, 3.5, and 4. The y-axis ranges from 0.2 to 1.6, with tick marks at 0.2, 0.4, 0.6, 0.8, 1, 1.2, 1.4, and 1.6. The line starts at approximately (1, 1.22), decreases to a minimum at approximately (2.8, 0.35), and then increases to approximately (4, 1.05). The line is marked with small dots at various points.

**Figure 8.6:** One can show that a distance is *not* Hilbertian by looking at the spectrum of the centered matrix  $JD_p^2J$  corresponding to the pairwise squared-distance matrix  $D_p^2$  of a set of points. The spectrum of such a matrix is necessarily non-positive if the distance is Hilbertian. Here we plot the values of the maximal eigenvalue of that matrix for points selected in the proof of Proposition 8.2. We do so for varying values of *p*, and display the maximal eigenvalues we obtain. These eigenvalues are all positive, which shows that for all these values of  $p$ , the *p*-Wasserstein distance is not Hilbertian.

#### **8.3.1 Embeddings and Distortion**

An important body of work quantifies the hardness of approximating Wasserstein distances using Hilbertian embeddings. It has been shown that embedding measures in  $\ell_2$  spaces incurs necessarily an important distortion (Naor and Schechtman [2007], Andoni et al. [2018]) as soon as  $\mathcal{X} = \mathbb{R}^d$  with  $d \geq 2$ .

It is possible to embed quasi-isometrically *p*-Wasserstein spaces for  $0 < p \le 1$  in  $\ell_1$ (see [Indyk and Thaper, 2003, Andoni et al., 2008, Do Ba et al., 2011]), but the equivalence constant between the distances grows fast with the dimension *d*. Note also that for  $p = 1$  the embedding is true only for discrete measures (*i.e.* the embedding constant depends on the minimum distance between the spikes). A closely related embedding

technique consists in using the characterization of  $\mathcal{W}_1$  as the dual of Lipschitz functions *f* (see §6.2) and approximating the Lipschitz constraint  $\|\nabla f\|_1 \leq 1$  by a weighted  $\ell_1$ ball over the wavelets coefficients; see [Shirdhonkar and Jacobs, 2008]. This weighted  $\ell_1$ ball of wavelet coefficients defines a so-called Besov space of negative index [Leeb and Coifman, 2016]. These embedding results are also similar to the bound on the Wasserstein distance obtained using dyadic partitions; see [Weed and Bach, 2017, Prop. 1] and also [Fournier and Guillin, 2015]. This also provides a quasi-isometric embedding in  $\ell_1$  (this embedding being given by rescaled wavelet coefficients) and comes with the advantage that this embedding can be computed approximately in linear time when the input measures are discretized on uniform grids. We refer to [Mallat, 2008] for more details on wavelets. Note that the idea of using multiscale embeddings to compute Wasserstein-like distances has been used extensively in computer vision; see, for instance, [Ling and Okada, 2006, Grauman and Darrell, 2005, Cuturi and Fukumizu, 2007, Lazebnik et al., 2006].

### **8.3.2 Negative/Positive Definite Variants of Optimal Transport**

We show later in §10.4 that the *sliced* approximation to Wasserstein distances, essentially a sum of 1-D directional transportation distance computed on random pushforwards of measures projected on lines, is negative definite as the sum of negative definite functions [Berg et al., 1984, §3.1.11]. This result can be used to define a positive definite kernel [Kolouri et al., 2016]. Another way to recover a positive definite kernel is to cast the optimal transport problem as a soft-min problem (over all possible transportation tables) rather than a minimum, as proposed by Kosowsky and Yuille [1994] to introduce entropic regularization. That soft-min defines a term whose negexponential (also known as a generating function) is positive definite [Cuturi, 2012].

## **8.4 Empirical Estimators for OT, MMD and** *ϕ***-divergences**

In an applied setting, given two input measures  $(\alpha, \beta) \in \mathcal{M}^1_+(\mathcal{X})^2$ , an important statistical problem is to approximate the (usually unknown) divergence  $D(\alpha, \beta)$  using only samples  $(x_i)_{i=1}^n$  from  $\alpha$  and  $(y_j)_{j=1}^m$  from  $\beta$ . These samples are assumed to be independently identically distributed from their respective distributions.

#### **8.4.1 Empirical Estimators for OT and MMD**

For both Wasserstein distances  $W_p$  (see 2.18) and MMD norms (see §8.2), a straightforward estimator of the unknown distance between distriubtions is compute it directly between the empirical measures, hoping ideally that one can control the rate of con-

#### 8.4. Empirical Estimators for OT, MMD and *ϕ*-divergences 129

vergence of the latter to the former,

$$
D(\alpha, \beta) \approx D(\hat{\alpha}_n, \hat{\beta}_m) \quad \text{where} \quad \begin{cases} \n\hat{\alpha}_n \stackrel{\text{def.}}{=} \frac{1}{n} \sum_i \delta_{x_i}, \\ \n\hat{\beta}_m \stackrel{\text{def.}}{=} \frac{1}{m} \sum_j \delta_{y_j}. \n\end{cases}
$$

Note that here both  $\hat{\alpha}_n$  and  $\hat{\beta}_m$  are random measures, so  $D(\hat{\alpha}_n, \hat{\beta}_m)$  is a random number. For simplicity, we assume that  $\mathcal X$  is compact (handling unbounded domain requires extra constraints on the moments of the input measures).

For such a dual distance that metrizes the weak convergence (see Definition 2.2), since there is the weak convergence  $\hat{\alpha}_n \to \alpha$ , one has  $D(\hat{\alpha}_n, \hat{\beta}_n) \to D(\alpha, \beta)$  as  $n \to +\infty$ . But an important question is the speed of convergence of  $D(\hat{\alpha}_n, \hat{\beta}_n)$  toward  $D(\alpha, \beta)$ , and this rate is often called the "sample complexity" of *D*.

Note that for  $D(\alpha, \beta) = ||\cdot||_{TV}$ , since the TV norm does not metrize the weak convergence,  $\|\hat{\alpha}_n - \hat{\beta}_n\|_{TV}$  is not a consistent estimator, namely it does not converge toward  $\|\alpha - \beta\|_{TV}$ . Indeed, with probability 1,  $\|\hat{\alpha}_n - \hat{\beta}_n\|_{TV} = 2$  since the support of the two discrete measures does not overlap. Similar issues arise with other *ϕ*-divergences, which cannot be estimated using divergences between empirical distributions.

**Rates for OT.** For  $\mathcal{X} = \mathbb{R}^d$  and measure supported on bounded domain, it is shown by [Dudley, 1969] that for  $d > 2$ , and  $1 \leq p < +\infty$ ,

$$
\mathbb{E}(|\mathcal{W}_p(\hat{\alpha}_n, \hat{\beta}_n) - \mathcal{W}_p(\alpha, \beta)|) = O(n^{-\frac{1}{d}}),
$$

where the expectation  $\mathbb E$  is taken with respect to the random samples  $(x_i, y_i)_i$ . This rate is tight in  $\mathbb{R}^d$  if one of the two measures has a density with respect to the Lebesgue measure. This result was proved for general metric spaces [Dudley, 1969] using the notion of covering numbers and was later refined, in particular for  $\mathcal{X} = \mathbb{R}^d$  in [Dereich et al., 2013, Fournier and Guillin, 2015]. This rate can be refined when the measures are supported on low-dimensional subdomains: Weed and Bach [2017] show that, indeed, the rate depends on the intrinsic dimensionality of the support. Weed and Bach also study the nonasymptotic behavior of that convergence, such as for measures which are discretely approximated (*e.g.* mixture of Gaussians with small variances). It is also possible to prove concentration of  $\mathcal{W}_p(\hat{\alpha}_n, \hat{\beta}_n)$  around its mean  $\mathcal{W}_p(\alpha, \beta)$ ; see [Bolley et al., 2007, Boissard, 2011, Weed and Bach, 2017].

**Rates for MMD.** For weak norms  $\lVert \cdot \rVert_k^2$  which are dual of RKHS norms (also called MMD), as defined in (8.13), and contrary to Wasserstein distances, the sample complexity does not depend on the ambient dimension

$$
\mathbb{E}(||\hat{\alpha}_n - \hat{\beta}_n||_k - \|\alpha - \beta\|_k|) = O(n^{-\frac{1}{2}});
$$

see [Sriperumbudur et al., 2012]. Figure 8.7 shows a numerical comparison of the sample complexity rates for Wasserstein and MMD distances. Note, however, that  $\|\hat{\alpha}_n - \hat{\beta}_n\|_k^2$ 

is a slightly biased estimate of  $\|\alpha - \beta\|_{k}^{2}$  $\frac{2}{k}$ . In order to define an unbiased estimator, and thus to be able to use, for instance, SGD when minimizing such losses, one should rather use the unbiased estimator

$$
MMD_k(\hat{\alpha}_n, \hat{\beta}_n)^2 \stackrel{\text{def.}}{=} \frac{1}{n(n-1)} \sum_{i,i'} k(x_i, x_{i'}) + \frac{1}{n(n-1)} \sum_{j,j'} k(y_j, y_{j'}) - 2 \frac{1}{n^2} \sum_{i,j} k(x_i, y_j),
$$

which should be compared to (8.14). It satisfies  $\mathbb{E}(\text{MMD}_k(\hat{\alpha}_n, \hat{\beta}_n)^2) = ||\alpha - \beta||_k^2$ *k* ; see [Gretton et al., 2012].

Image /page/16/Figure/4 description: The image contains two plots side-by-side. Both plots show three lines representing different values of 'd' (d=2 in red, d=3 in green, and d=5 in blue), with shaded regions indicating uncertainty. The left plot has 'Energy distance ||.||H-1' on the x-axis and values ranging from -0.5 to -1.5 on the y-axis. The right plot has 'W2' on the x-axis and values ranging from -0.2 to -1.4 on the y-axis. Both plots show a general downward trend for all lines.

**Figure 8.7:** Decay of  $\log_{10}(D(\hat{\alpha}_n, \hat{\alpha}'_n))$  as a function of  $\log_{10}(n)$  for *D* being the energy distance  $D = ||\cdot||_{H^{-1}}$  (*i.e.* the *H*<sup>−1</sup> norm) as defined in Example 8.12 (left) and the Wasserstein distance  $D = W_2$ (right). Here  $(\hat{\alpha}_n, \hat{\alpha}'_n)$  are two independent empirical distributions of  $\alpha$ , the uniform distribution on the unit cube  $[0,1]^d$ , tested for several value of  $d \in \{2,3,5\}$ . The shaded bar displays the confidence interval at  $\pm$  the standard deviation of  $log(D(\hat{\alpha}_n, \alpha))$ .

#### **8.4.2 Empirical Estimators for** *ϕ***-divergences**

It is not possible to approximate  $\mathcal{D}_{\varphi}(\alpha|\beta)$ , as defined in (8.2), from discrete samples using  $\mathcal{D}_{\varphi}(\hat{\alpha}_n|\hat{\beta}_n)$ . Indeed, this quantity is either  $+\infty$  (for instance, for the KL divergence) or is not converging to  $\mathcal{D}_{\varphi}(\alpha|\beta)$  as  $n \to +\infty$  (for instance, for the TV norm). Instead, it is required to use a density estimator to somehow smooth the discrete empirical measures and replace them by densities; see [Silverman, 1986]. In a Euclidean space  $\mathcal{X} = \mathbb{R}^d$ , introducing  $h_{\sigma} = h(\cdot/\sigma)$  with a smooth windowing function and a bandwidth  $\sigma > 0$ , a density estimator for  $\alpha$  is defined using a convolution against this kernel,

$$
\hat{\alpha}_n \star h_{\sigma} = \frac{1}{n} \sum_i h_{\sigma}(\cdot - x_i). \tag{8.20}
$$

One can then approximate the  $\varphi$  divergence using

$$
\mathcal{D}_{\varphi}^{\sigma}(\hat{\alpha}_n|\hat{\beta}_n) \stackrel{\text{def.}}{=} \frac{1}{n} \sum_{j=1}^n \varphi\left(\frac{\sum_i h_{\sigma}(y_j - x_i)}{\sum_{j'} h_{\sigma}(y_j - y_{j'})}\right)
$$

#### 8.5. Entropic Regularization: Between OT and MMD 131

where  $\sigma$  should be adapted to the number *n* of samples and to the dimension *d*. It is also possible to devise nonparametric estimators, bypassing the choice of a fixed bandwidth  $\sigma$  to select instead a number *k* of nearest neighbors. These methods typically make use of the distance between nearest neighbors [Loftsgaarden and Quesenberry, 1965], which is similar to locally adapting the bandwidth  $\sigma$  to the local sampling density. Denoting  $\Delta_k(x)$  the distance between  $x \in \mathbb{R}^d$  and its *k*th nearest neighbor among the  $(x_i)_{i=1}^n$ , a density estimator is defined as

$$
\rho_{\hat{\alpha}_n}^k(x) \stackrel{\text{def.}}{=} \frac{k/n}{|B_d|\Delta_k(x)^r},\tag{8.21}
$$

where  $|B_d|$  is the volume of the unit ball in  $\mathbb{R}^d$ . Instead of somehow "counting" the number of sample falling in an area of width  $\sigma$  in (8.20), this formula (8.21) estimates the radius required to encapsulate *k* samples. Figure 8.8 compares the estimators (8.20) and  $(8.21)$ . A typical example of application is detailed in  $(4.1)$  for the entropy functional, which is the KL divergence with respect to the Lebesgue measure. We refer to [Moon and Hero, 2014] for more details.

Image /page/17/Figure/4 description: The image displays a 2x3 grid of plots, each showing a probability distribution. The top row has plots labeled with sigma values: sigma = 2.5 \* 10^-3, sigma = 15 \* 10^-3, and sigma = 25 \* 10^-3. The bottom row has plots labeled with k values: k = 1, k = 50, and k = 100. Each plot contains a noisy curve (blue, purple, or red) and a smoother curve (black or red) that appears to be a fit or a reference distribution. The first column shows a single-peaked distribution, while the second and third columns show double-peaked distributions. The noise level and the smoothness of the fit vary across the different sigma and k values.

**Figure 8.8:** Comparison of kernel density estimation  $\hat{\alpha}_n \star h_\sigma$  (top, using a Gaussian kernel *h*) and *k*-nearest neighbors estimation  $\rho_{\hat{\alpha}_n}^k$  (bottom) for  $n = 200$  samples from a mixture of two Gaussians.

#### **8.5 Entropic Regularization: Between OT and MMD**

Following Proposition 4.7, we recall that the Sinkhorn divergence is defined as

$$
\mathfrak{P}^\varepsilon_{\bf C}({\bf a},{\bf b})\stackrel{\scriptscriptstyle\rm def.}{=} \langle {\bf P}^\star,\,{\bf C}\rangle = \langle e^{\frac{{\bf f}^\star}{\varepsilon}},\,({\bf K}\odot{\bf C})e^{\frac{{\bf g}^\star}{\varepsilon}}\rangle,
$$

where  $\mathbf{P}^*$  is the solution of (4.2) while  $(\mathbf{f}^*, \mathbf{g}^*)$  are solutions of (4.30). Assuming  $\mathbf{C}_{i,j} =$  $d(x_i, x_j)^p$  for some distance *d* on X, for two discrete probability distributions of the form (2.3), this defines a regularized Wasserstein cost

$$
\mathcal{W}_{p,\varepsilon}(\alpha,\beta)^p\stackrel{\scriptscriptstyle\rm def.}{=}\mathfrak{P}^\varepsilon_{\bf C}({\bf a},{\bf b}).
$$

This definition is generalized to any input distribution (not necessarily discrete) as

$$
\mathcal{W}_{p,\varepsilon}(\alpha,\beta)^p \stackrel{\text{\tiny def.}}{=} \int_{\mathcal{X}\times\mathcal{X}} d(x,y)^p \mathrm{d} \pi^\star(x,y),
$$

where  $\pi^*$  is the solution of (4.9).

In order to cancel the bias introduced by the regularization (in particular,  $W_{p,\varepsilon}(\alpha,\alpha) \neq 0$ , we introduce a corrected regularized divergence

$$
\tilde{\mathcal{W}}_{p,\varepsilon}(\alpha,\beta)^p\stackrel{\scriptscriptstyle\rm def.}{=} 2\,\mathcal{W}_{p,\varepsilon}(\alpha,\beta)^p-\mathcal{W}_{p,\varepsilon}(\alpha,\alpha)^p-\mathcal{W}_{p,\varepsilon}(\beta,\beta)^p.
$$

It is proved in [Feydy et al., 2019] that if  $e^{-c/\varepsilon}$  is a positive kernel, then a related corrected divergence (obtained by using  $L_C^{\varepsilon}$  in place of  $\mathfrak{P}_C^{\varepsilon}$ ) is positive. Note that it is possible to define other renormalization schemes using regularized optimal transport, as proposed, for instance, by Amari et al. [2018].

Image /page/18/Figure/7 description: The image displays two plots side-by-side, both with a legend indicating different values of epsilon: 0.01, 0.1, 1, 10, and 1000, represented by red, dark red, purple, dark blue, and blue lines, respectively. The x-axis for both plots ranges from 1 to approximately 2.5, with tick marks at 1, 1.5, and 2. The y-axis for both plots ranges from -2.5 to -0.5, with tick marks at -2.5, -2, -1.5, -1, and -0.5. The left plot is labeled "d = 2" below the x-axis, and the right plot is labeled "d = 5" below the x-axis. Both plots show a general downward trend for all lines, with the lines for smaller epsilon values generally staying above the lines for larger epsilon values.

**Figure 8.9:** Decay of  $\mathbb{E}(\log_{10}(\tilde{W}_{p,\varepsilon}(\hat{\alpha}_n,\hat{\alpha}'_n)))$ , for  $p=3/2$  for various  $\varepsilon$ , as a function of  $\log_{10}(n)$ where  $\alpha$  is the same as in Figure 8.7.

The following proposition, whose proof can be found in [Ramdas et al., 2017], shows that this regularized divergence interpolates between the Wasserstein distance and the energy distance defined in Example 8.12.

**Proposition 8.3.** One has

$$
\tilde{\mathcal{W}}_{p,\varepsilon}(\alpha,\beta) \stackrel{\varepsilon \to 0}{\longrightarrow} 2 \mathcal{W}_p(\alpha,\beta) \quad \text{and} \quad \tilde{\mathcal{W}}_{p,\varepsilon}(\alpha,\beta)^{p} \stackrel{\varepsilon \to +\infty}{\longrightarrow} \|\alpha-\beta\|_{\mathrm{ED}(\mathcal{X},d)}^2,
$$

where  $\left\| \cdot \right\|_{\mathrm{ED}(\mathcal{X},d)}$  is defined in (8.19).

Figure 8.9 shows numerically the impact of  $\varepsilon$  on the sample complexity rates. It is proved in Genevay et al. [2019], in the case of  $c(x, y) = ||x - y||^2$  on  $\mathcal{X} = \mathbb{R}^d$ , that these rates interpolate between the ones of OT and MMD.
<PERSON> (1994a) proposed a method in which rectangular neighborhoods are found adaptively by successively carving away edges of a box containing the training data. Here we describe the discriminant adaptive nearest-neighbor (DANN) rule of <PERSON><PERSON> and <PERSON><PERSON><PERSON> (1996a). Earlier, related proposals appear in <PERSON> and <PERSON><PERSON> (1981) and <PERSON><PERSON> and <PERSON> (1990).

At each query point a neighborhood of say 50 points is formed, and the class distribution among the points is used to decide how to deform the neighborhood—that is, to adapt the metric. The adapted metric is then used in a nearest-neighbor rule at the query point. Thus at each query point a potentially different metric is used.

In Figure 13.13 it is clear that the neighborhood should be stretched in the direction orthogonal to line joining the class centroids. This direction also coincides with the linear discriminant boundary, and is the direction in which the class probabilities change the least. In general this direction of maximum change will not be orthogonal to the line joining the class centroids (see Figure 4.9 on page 116.) Assuming a local discriminant model, the information contained in the local within- and between-class covariance matrices is all that is needed to determine the optimal shape of the neighborhood.

The discriminant adaptive nearest-neighbor (DANN) metric at a query point  $x_0$  is defined by

$$
D(x, x_0) = (x - x_0)^T \Sigma (x - x_0),
$$
\n(13.8)

where

$$
\Sigma = W^{-1/2}[W^{-1/2}BW^{-1/2} + \epsilon I]W^{-1/2}
$$
  
= W^{-1/2}[B^\* + \epsilon I]W^{-1/2}. (13.9)

Here **W** is the pooled within-class covariance matrix  $\sum_{k=1}^{K} \pi_k \mathbf{W}_k$  and **B** is the between class covariance matrix  $\sum_{k=1}^{K} \pi_k (\bar{x}_k - \bar{x}) (\bar{x}_k - \bar{x})^T$ , with **W** and **B** computed using only the 50 nearest neighbors around  $x_0$ . After computation of the metric, it is used in a nearest-neighbor rule at  $x_0$ .

This complicated formula is actually quite simple in its operation. It first spheres the data with respect to  $W$ , and then stretches the neighborhood in the zero-eigenvalue directions of  $\mathbf{B}^*$  (the between-matrix for the sphered data ). This makes sense, since locally the observed class means do not differ in these directions. The  $\epsilon$  parameter rounds the neighborhood, from an infinite strip to an ellipsoid, to avoid using points far away from the query point. The value of  $\epsilon = 1$  seems to work well in general. Figure 13.14 shows the resulting neighborhoods for a problem where the classes form two concentric circles. Notice how the neighborhoods stretch out orthogonally to the decision boundaries when both classes are present in the neighborhood. In the pure regions with only one class, the neighborhoods remain circular;

# 13. Prototypes and Nearest-Neighbors

Image /page/1/Figure/1 description: This is a scatter plot showing two clusters of data points, one in blue and one in orange. Several ellipses are overlaid on the plot, each representing a cluster or sub-cluster. Each ellipse has a crosshair at its center, indicating the mean, and two lines extending from the center, representing the standard deviations along the major and minor axes of the ellipse. The ellipses are oriented in different directions and have varying sizes, suggesting different variances and covariances for the data points within each cluster.

FIGURE 13.14. Neighborhoods found by the DANN procedure, at various query points (centers of the crosses). There are two classes in the data, with one class surrounding the other. 50 nearest-neighbors were used to estimate the local metrics. Shown are the resulting metrics used to form 15-nearest-neighborhoods.

in these cases the between matrix  $\mathbf{B} = 0$ , and the  $\Sigma$  in (13.8) is the identity matrix.

### 13.4.1 Example

Here we generate two-class data in ten dimensions, analogous to the twodimensional example of Figure 13.14. All ten predictors in class 1 are independent standard normal, conditioned on the radius being greater than 22.4 and less than 40, while the predictors in class 2 are independent standard normal without the restriction. There are 250 observations in each class. Hence the first class almost completely surrounds the second class in the full ten-dimensional space.

In this example there are no pure noise variables, the kind that a nearestneighbor subset selection rule might be able to weed out. At any given point in the feature space, the class discrimination occurs along only one direction. However, this direction changes as we move across the feature space and all variables are important somewhere in the space.

Figure 13.15 shows boxplots of the test error rates over ten realizations, for standard 5-nearest-neighbors, LVQ, and discriminant adaptive 5-nearest-neighbors. We used 50 prototypes per class for LVQ, to make it comparable to 5 nearest-neighbors (since  $250/5 = 50$ ). The adaptive metric significantly reduces the error rate, compared to LVQ or standard nearest-neighbors.

Image /page/2/Figure/1 description: This is a box plot showing the test error for three different methods: 5NN, LVQ, and DANN. The y-axis represents the test error, ranging from 0.0 to 0.4. The 5NN method has a test error between approximately 0.25 and 0.32, with a median around 0.29. The LVQ method has a test error between approximately 0.35 and 0.4, with a median around 0.38. The DANN method has a test error between approximately 0.15 and 0.19, with a median around 0.17.

FIGURE 13.15. Ten-dimensional simulated example: boxplots of the test error rates over ten realizations, for standard 5-nearest-neighbors, LVQ with 50 centers, and discriminant-adaptive 5-nearest-neighbors

# 13.4.2 Global Dimension Reduction for Nearest-Neighbors

The discriminant-adaptive nearest-neighbor method carries out local dimension reduction—that is, dimension reduction separately at each query point. In many problems we can also benefit from global dimension reduction, that is, apply a nearest-neighbor rule in some optimally chosen subspace of the original feature space. For example, suppose that the two classes form two nested spheres in four dimensions of feature space, and there are an additional six noise features whose distribution is independent of class. Then we would like to discover the important four-dimensional subspace, and carry out nearest-neighbor classification in that reduced subspace. Hastie and Tibshirani (1996a) discuss a variation of the discriminantadaptive nearest-neighbor method for this purpose. At each training point  $x_i$ , the between-centroids sum of squares matrix  $\mathbf{B}_i$  is computed, and then these matrices are averaged over all training points:

$$
\bar{\mathbf{B}} = \frac{1}{N} \sum_{i=1}^{N} \mathbf{B}_i.
$$
\n(13.10)

Let  $e_1, e_2, \ldots, e_p$  be the eigenvectors of the matrix **B**, ordered from largest to smallest eigenvalue  $\theta_k$ . Then these eigenvectors span the optimal subspaces for global subspace reduction. The derivation is based on the fact that the best rank-L approximation to  $\bar{\mathbf{B}}$ ,  $\bar{\mathbf{B}}_{[L]} = \sum_{\ell=1}^{L} \theta_{\ell} e_{\ell} e_{\ell}^{T}$ , solves the least squares problem

$$
\min_{\text{rank}(\mathbf{M})=L} \sum_{i=1}^{N} \text{trace}[(\mathbf{B}_i - \mathbf{M})^2]. \tag{13.11}
$$

Since each  $B_i$  contains information on (a) the local discriminant subspace, and (b) the strength of discrimination in that subspace,  $(13.11)$  can be seen

# 13. Prototypes and Nearest-Neighbors

as a way of finding the best approximating subspace of dimension L to a series of N subspaces by weighted least squares (Exercise 13.5.)

In the four-dimensional sphere example mentioned above and examined in Hastie and Tibshirani (1996a), four of the eigenvalues  $\theta_{\ell}$  turn out to be large (having eigenvectors nearly spanning the interesting subspace), and the remaining six are near zero. Operationally, we project the data into the leading four-dimensional subspace, and then carry out nearest neighbor classification. In the satellite image classification example in Section 13.3.2, the technique labeled DANN in Figure 13.8 used 5-nearest-neighbors in a globally reduced subspace. There are also connections of this technique with the *sliced inverse regression* proposal of Duan and Li (1991). These authors use similar ideas in the regression setting, but do global rather than local computations. They assume and exploit spherical symmetry of the feature distribution to estimate interesting subspaces.

# 13.5 Computational Considerations

One drawback of nearest-neighbor rules in general is the computational load, both in finding the neighbors and storing the entire training set. With  $N$  observations and p predictors, nearest-neighbor classification requires  $Np$ operations to find the neighbors per query point. There are fast algorithms for finding nearest-neighbors (Friedman et al., 1975; Friedman et al., 1977) which can reduce this load somewhat. Hastie and Simard (1998) reduce the computations for tangent distance by developing analogs of  $K$ -means clustering in the context of this invariant metric.

Reducing the storage requirements is more difficult, and various editing and condensing procedures have been proposed. The idea is to isolate a subset of the training set that suffices for nearest-neighbor predictions, and throw away the remaining training data. Intuitively, it seems important to keep the training points that are near the decision boundaries and on the correct side of those boundaries, while some points far from the boundaries could be discarded.

The multi-edit algorithm of Devijver and Kittler (1982) divides the data cyclically into training and test sets, computing a nearest neighbor rule on the training set and deleting test points that are misclassified. The idea is to keep homogeneous clusters of training observations.

The condensing procedure of Hart (1968) goes further, trying to keep only important exterior points of these clusters. Starting with a single randomly chosen observation as the training set, each additional data item is processed one at a time, adding it to the training set only if it is misclassified by a nearest-neighbor rule computed on the current training set.

These procedures are surveyed in Dasarathy (1991) and Ripley (1996). They can also be applied to other learning procedures besides nearestneighbors. While such methods are sometimes useful, we have not had much practical experience with them, nor have we found any systematic comparison of their performance in the literature.

# Bibliographic Notes

The nearest-neighbor method goes back at least to Fix and Hodges (1951). The extensive literature on the topic is reviewed by Dasarathy (1991); Chapter 6 of Ripley (1996) contains a good summary. K-means clustering is due to Lloyd (1957) and MacQueen (1967). Kohonen (1989) introduced learning vector quantization. The tangent distance method is due to Simard et al. (1993). Hastie and Tibshirani (1996a) proposed the discriminant adaptive nearest-neighbor technique.

# Exercises

Ex. 13.1 Consider a Gaussian mixture model where the covariance matrices are assumed to be scalar:  $\Sigma_r = \sigma \mathbf{I} \ \forall r = 1, \ldots, R$ , and  $\sigma$  is a fixed parameter. Discuss the analogy between the K-means clustering algorithm and the EM algorithm for fitting this mixture model in detail. Show that in the limit  $\sigma \to 0$  the two methods coincide.

Ex. 13.2 Derive formula (13.7) for the median radius of the 1-nearestneighborhood.

Ex. 13.3 Let  $E^*$  be the error rate of the Bayes rule in a K-class problem, where the true class probabilities are given by  $p_k(x)$ ,  $k = 1, \ldots, K$ . Assuming the test point and training point have identical features  $x$ , prove (13.5)

$$
\sum_{k=1}^{K} p_k(x)(1 - p_k(x)) \le 2(1 - p_{k^*}(x)) - \frac{K}{K - 1}(1 - p_{k^*}(x))^2.
$$

where  $k^* = \arg \max_k p_k(x)$ . Hence argue that the error rate of the 1nearest-neighbor rule converges in  $L_1$ , as the size of the training set increases, to a value  $E_1$ , bounded above by

$$
E^*\left(2 - E^*\frac{K}{K - 1}\right). \tag{13.12}
$$

[This statement of the theorem of Cover and Hart (1967) is taken from Chapter 6 of Ripley (1996), where a short proof is also given].

# 13. Prototypes and Nearest-Neighbors

Ex. 13.4 Consider an image to be a function  $F(x): \mathbb{R}^2 \to \mathbb{R}^1$  over the twodimensional spatial domain (paper coordinates). Then  $F(c+x_0+\mathbf{A}(x-x_0))$ represents an affine transformation of the image F, where **A** is a  $2 \times 2$ matrix.

- 1. Decompose A (via Q-R) in such a way that parameters identifying the four affine transformations (two scale, shear and rotation) are clearly identified.
- 2. Using the chain rule, show that the derivative of  $F(c+x_0+A(x-x_0))$ w.r.t. each of these parameters can be represented in terms of the two spatial derivatives of F.
- 3. Using a two-dimensional kernel smoother (Chapter 6), describe how to implement this procedure when the images are quantized to  $16 \times 16$ pixels.

Ex. 13.5 Let  $\mathbf{B}_i$ ,  $i = 1, 2, ..., N$  be square  $p \times p$  positive semi-definite matrices and let  $\mathbf{\bar{B}} = (1/N) \sum \mathbf{B}_i$ . Write the eigen-decomposition of  $\mathbf{\bar{B}}$  as  $\sum_{\ell=1}^p \theta_{\ell} e_{\ell} e_i^T$  with  $\theta_{\ell} > \theta_{\ell-1} > \cdots > \theta_1$ . Show that the best rank-L approx- $_{\ell=1}^p \theta_\ell e_\ell e_\ell^T$  with  $\theta_\ell \geq \theta_{\ell-1} \geq \cdots \geq \theta_1$ . Show that the best rank-L approximation for the  $B_i$ ,

$$
\min_{\text{rank}(\mathbf{M})=L}\sum_{i=1}^{N}\text{trace}[(\mathbf{B}_i-\mathbf{M})^2],
$$

is given by  $\bar{\mathbf{B}}_{[L]} = \sum_{\ell=1}^{L} \theta_{\ell} e_{\ell} e_{\ell}^{T}$ . (*Hint:* Write  $\sum_{i=1}^{N} \text{trace}[(\mathbf{B}_{i} - \mathbf{M})^{2}]$  as

$$
\sum_{i=1}^{N} \text{trace}[(\mathbf{B}_i - \bar{\mathbf{B}})^2] + \sum_{i=1}^{N} \text{trace}[(\mathbf{M} - \bar{\mathbf{B}})^2]).
$$

Ex. 13.6 Here we consider the problem of shape averaging. In particular,  $\mathbf{L}_i, i = 1, \dots, M$  are each  $N \times 2$  matrices of points in  $\mathbb{R}^2$ , each sampled from corresponding positions of handwritten (cursive) letters. We seek an affine invariant average **V**, also  $N \times 2$ ,  $V^T V = I$ , of the M letters  $L_i$  with the following property: V minimizes

$$
\sum_{j=1}^{M}\min_{\mathbf{A}_{j}}\left\Vert \mathbf{L}_{j}-\mathbf{V}\mathbf{A}_{j}\right\Vert ^{2}.
$$

Characterize the solution.

This solution can suffer if some of the letters are big and dominate the average. An alternative approach is to minimize instead:

$$
\sum_{j=1}^{M}\min_{\mathbf{A}_{j}}\left\Vert \mathbf{L}_{j}\mathbf{A}_{j}^{*}-\mathbf{V}\right\Vert ^{2}.
$$

Derive the solution to this problem. How do the criteria differ? Use the SVD of the  $L_j$  to simplify the comparison of the two approaches.

Ex. 13.7 Consider the application of nearest-neighbors to the "easy" and "hard" problems in the left panel of Figure 13.5.

- 1. Replicate the results in the left panel of Figure 13.5.
- 2. Estimate the misclassification errors using fivefold cross-validation, and compare the error rate curves to those in 1.
- 3. Consider an "AIC-like" penalization of the training set misclassification error. Specifically, add  $2t/N$  to the training set misclassification error, where t is the approximate number of parameters  $N/r$ , r being the number of nearest-neighbors. Compare plots of the resulting penalized misclassification error to those in 1 and 2. Which method gives a better estimate of the optimal number of nearest-neighbors: cross-validation or AIC?

Ex. 13.8 Generate data in two classes, with two features. These features are all independent Gaussian variates with standard deviation 1. Their mean vectors are  $(-1, -1)$  in class 1 and  $(1, 1)$  in class 2. To each feature vector apply a random rotation of angle  $\theta$ ,  $\theta$  chosen uniformly from 0 to  $2\pi$ . Generate 50 observations from each class to form the training set, and 500 in each class as the test set. Apply four different classifiers:

- 1. Nearest-neighbors.
- 2. Nearest-neighbors with hints: ten randomly rotated versions of each data point are added to the training set before applying nearestneighbors.
- 3. Invariant metric nearest-neighbors, using Euclidean distance invariant to rotations about the origin.
- 4. Tangent distance nearest-neighbors.

In each case choose the number of neighbors by tenfold cross-validation. Compare the results.

484 13. Prototypes and Nearest-Neighbors

This is page 485 Printer: Opaque this

# 14 Unsupervised Learning

# 14.1 Introduction

The previous chapters have been concerned with predicting the values of one or more outputs or response variables  $Y = (Y_1, \ldots, Y_m)$  for a given set of input or predictor variables  $X^T = (X_1, \ldots, X_p)$ . Denote by  $x_i^T = (x_{i1}, \ldots, x_{ip})$  the inputs for the *i*th training case, and let  $y_i$  be a response measurement. The predictions are based on the training sample  $(x_1, y_1), \ldots, (x_N, y_N)$  of previously solved cases, where the joint values of all of the variables are known. This is called supervised learning or "learning with a teacher." Under this metaphor the "student" presents an answer  $\hat{y}_i$  for each  $x_i$  in the training sample, and the supervisor or "teacher" provides either the correct answer and/or an error associated with the student's answer. This is usually characterized by some loss function  $L(y, \hat{y})$ , for example,  $L(y, \hat{y}) = (y - \hat{y})^2$ .

If one supposes that  $(X, Y)$  are random variables represented by some joint probability density  $Pr(X, Y)$ , then supervised learning can be formally characterized as a density estimation problem where one is concerned with determining properties of the conditional density  $Pr(Y|X)$ . Usually the properties of interest are the "location" parameters  $\mu$  that minimize the expected error at each  $x$ ,

$$
\mu(x) = \underset{\theta}{\text{argmin}} \, E_{Y|X} L(Y, \theta). \tag{14.1}
$$

Conditioning one has

$$
\Pr(X, Y) = \Pr(Y|X) \cdot \Pr(X),
$$

where  $Pr(X)$  is the joint marginal density of the X values alone. In supervised learning  $Pr(X)$  is typically of no direct concern. One is interested mainly in the properties of the conditional density  $Pr(Y|X)$ . Since Y is often of low dimension (usually one), and only its location  $\mu(x)$  is of interest, the problem is greatly simplified. As discussed in the previous chapters, there are many approaches for successfully addressing supervised learning in a variety of contexts.

In this chapter we address unsupervised learning or "learning without a teacher." In this case one has a set of N observations  $(x_1, x_2, \ldots, x_N)$  of a random p-vector X having joint density  $Pr(X)$ . The goal is to directly infer the properties of this probability density without the help of a supervisor or teacher providing correct answers or degree-of-error for each observation. The dimension of  $X$  is sometimes much higher than in supervised learning, and the properties of interest are often more complicated than simple location estimates. These factors are somewhat mitigated by the fact that X represents all of the variables under consideration; one is not required to infer how the properties of  $Pr(X)$  change, conditioned on the changing values of another set of variables.

In low-dimensional problems (say  $p \leq 3$ ), there are a variety of effective nonparametric methods for directly estimating the density  $Pr(X)$  itself at all X-values, and representing it graphically (Silverman, 1986, e.g.). Owing to the curse of dimensionality, these methods fail in high dimensions. One must settle for estimating rather crude global models, such as Gaussian mixtures or various simple descriptive statistics that characterize  $Pr(X)$ .

Generally, these descriptive statistics attempt to characterize  $X$ -values, or collections of such values, where  $Pr(X)$  is relatively large. Principal components, multidimensional scaling, self-organizing maps, and principal curves, for example, attempt to identify low-dimensional manifolds within the X-space that represent high data density. This provides information about the associations among the variables and whether or not they can be considered as functions of a smaller set of "latent" variables. Cluster analysis attempts to find multiple convex regions of the X-space that contain modes of  $Pr(X)$ . This can tell whether or not  $Pr(X)$  can be represented by a mixture of simpler densities representing distinct types or classes of observations. Mixture modeling has a similar goal. Association rules attempt to construct simple descriptions (conjunctive rules) that describe regions of high density in the special case of very high dimensional binary-valued data.

With supervised learning there is a clear measure of success, or lack thereof, that can be used to judge adequacy in particular situations and to compare the effectiveness of different methods over various situations. Lack of success is directly measured by expected loss over the joint distribution  $Pr(X, Y)$ . This can be estimated in a variety of ways including cross-validation. In the context of unsupervised learning, there is no such direct measure of success. It is difficult to ascertain the validity of inferences drawn from the output of most unsupervised learning algorithms. One must resort to heuristic arguments not only for motivating the algorithms, as is often the case in supervised learning as well, but also for judgments as to the quality of the results. This uncomfortable situation has led to heavy proliferation of proposed methods, since effectiveness is a matter of opinion and cannot be verified directly.

In this chapter we present those unsupervised learning techniques that are among the most commonly used in practice, and additionally, a few others that are favored by the authors.

# 14.2 Association Rules

Association rule analysis has emerged as a popular tool for mining commercial data bases. The goal is to find joint values of the variables  $X =$  $(X_1, X_2, \ldots, X_p)$  that appear most frequently in the data base. It is most often applied to binary-valued data  $X_j \in \{0,1\}$ , where it is referred to as "market basket" analysis. In this context the observations are sales transactions, such as those occurring at the checkout counter of a store. The variables represent all of the items sold in the store. For observation  $i$ , each variable  $X_j$  is assigned one of two values;  $x_{ij} = 1$  if the jth item is purchased as part of the transaction, whereas  $x_{ij} = 0$  if it was not purchased. Those variables that frequently have joint values of one represent items that are frequently purchased together. This information can be quite useful for stocking shelves, cross-marketing in sales promotions, catalog design, and consumer segmentation based on buying patterns.

More generally, the basic goal of association rule analysis is to find a collection of prototype X-values  $v_1, \ldots, v_L$  for the feature vector X, such that the probability density  $Pr(v_l)$  evaluated at each of those values is relatively large. In this general framework, the problem can be viewed as "mode finding" or "bump hunting." As formulated, this problem is impossibly difficult. A natural estimator for each  $Pr(v_l)$  is the fraction of observations for which  $X = v_l$ . For problems that involve more than a small number of variables, each of which can assume more than a small number of values, the number of observations for which  $X = v_l$  will nearly always be too small for reliable estimation. In order to have a tractable problem, both the goals of the analysis and the generality of the data to which it is applied must be greatly simplified.

The first simplification modifies the goal. Instead of seeking values  $x$ where  $Pr(x)$  is large, one seeks regions of the X-space with high probability

content relative to their size or support. Let  $S_j$  represent the set of all possible values of the j<sup>th</sup> variable (its *support*), and let  $s_i \subseteq S_i$  be a subset of these values. The modified goal can be stated as attempting to find subsets of variable values  $s_1, \ldots, s_p$  such that the probability of each of the variables simultaneously assuming a value within its respective subset,

$$
\Pr\left[\bigcap_{j=1}^{p} (X_j \in s_j)\right],\tag{14.2}
$$

is relatively large. The intersection of subsets  $\bigcap_{j=1}^p (X_j \in s_j)$  is called a *conjunctive rule.* For quantitative variables the subsets  $s_j$  are contiguous intervals; for categorical variables the subsets are delineated explicitly. Note that if the subset  $s_j$  is in fact the entire set of values  $s_j = S_j$ , as is often the case, the variable  $X_j$  is said not to appear in the rule (14.2).

## 14.2.1 Market Basket Analysis

General approaches to solving (14.2) are discussed in Section 14.2.5. These can be quite useful in many applications. However, they are not feasible for the very large  $(p \approx 10^4, N \approx 10^8)$  commercial data bases to which market basket analysis is often applied. Several further simplifications of (14.2) are required. First, only two types of subsets are considered; either  $s_j$  consists of a *single* value of  $X_j$ ,  $s_j = v_{0j}$ , or it consists of the entire set of values that  $X_j$  can assume,  $s_j = S_j$ . This simplifies the problem (14.2) to finding subsets of the integers  $\mathcal{J} \subset \{1, \ldots, p\}$ , and corresponding values  $v_{0i}, j \in \mathcal{J}$ , such that

$$
\Pr\left[\bigcap_{j\in\mathcal{J}} (X_j = v_{0j})\right]
$$
\n(14.3)

is large. Figure 14.1 illustrates this assumption.

One can apply the technique of dummy variables to turn (14.3) into a problem involving only binary-valued variables. Here we assume that the support  $S_i$  is finite for each variable  $X_i$ . Specifically, a new set of variables  $Z_1, \ldots, Z_K$  is created, one such variable for each of the values  $v_{lj}$  attainable by each of the original variables  $X_1, \ldots, X_p$ . The number of dummy variables  $K$  is

$$
K = \sum_{j=1}^{p} |\mathcal{S}_j|,
$$

where  $|S_j|$  is the number of distinct values attainable by  $X_j$ . Each dummy variable is assigned the value  $Z_k = 1$  if the variable with which it is associated takes on the corresponding value to which  $Z_k$  is assigned, and  $Z_k = 0$  otherwise. This transforms (14.3) to finding a subset of the integers  $\mathcal{K} \subset \{1, \ldots, K\}$  such that

Image /page/12/Figure/1 description: The image displays three separate grid plots, each with axes labeled X1 and X2. Each grid is composed of smaller squares, predominantly colored green. Within each grid, there is a distinct pattern of red squares. The first plot shows a vertical column of four red squares in the center-right, with one red square directly to its left. The second plot features a prominent vertical stripe of red squares running down the center of the grid. The third plot contains a single red square located in the middle of the grid.

FIGURE 14.1. Simplifications for association rules. Here there are two inputs  $X_1$  and  $X_2$ , taking four and six distinct values, respectively. The red squares indicate areas of high density. To simplify the computations, we assume that the derived subset corresponds to either a single value of an input or all values. With this assumption we could find either the middle or right pattern, but not the left one.

$$
\Pr\left[\bigcap_{k \in \mathcal{K}} (Z_k = 1)\right] = \Pr\left[\prod_{k \in \mathcal{K}} Z_k = 1\right]
$$
\n(14.4)

is large. This is the standard formulation of the market basket problem. The set K is called an "item set." The number of variables  $Z_k$  in the item set is called its "size" (note that the size is no bigger than  $p$ ). The estimated value of (14.4) is taken to be the fraction of observations in the data base for which the conjunction in (14.4) is true:

$$
\widehat{\Pr}\left[\prod_{k \in \mathcal{K}} (Z_k = 1)\right] = \frac{1}{N} \sum_{i=1}^{N} \prod_{k \in \mathcal{K}} z_{ik}.
$$
\n(14.5)

Here  $z_{ik}$  is the value of  $Z_k$  for this *i*th case. This is called the "support" or "prevalence"  $T(\mathcal{K})$  of the item set  $\mathcal{K}$ . An observation i for which  $\prod_{k \in \mathcal{K}} z_{ik} =$ 1 is said to "contain" the item set  $K$ .

In association rule mining a lower support bound  $t$  is specified, and one seeks all item sets  $\mathcal{K}_l$  that can be formed from the variables  $Z_1, \ldots, Z_K$ with support in the data base greater than this lower bound  $t$ 

$$
\{\mathcal{K}_l | T(\mathcal{K}_l) > t\}.
$$
\n(14.6)

## 14.2.2 The Apriori Algorithm

The solution to this problem (14.6) can be obtained with feasible computation for very large data bases provided the threshold  $t$  is adjusted so that (14.6) consists of only a small fraction of all  $2^{K}$  possible item sets. The "Apriori" algorithm (Agrawal et al., 1995) exploits several aspects of the

curse of dimensionality to solve (14.6) with a small number of passes over the data. Specifically, for a given support threshold  $t$ :

- The cardinality  $|\{\mathcal{K}| T(\mathcal{K}) > t\}|$  is relatively small.
- Any item set  $\mathcal L$  consisting of a subset of the items in  $\mathcal K$  must have support greater than or equal to that of  $\mathcal{K}, \mathcal{L} \subseteq \mathcal{K} \Rightarrow T(\mathcal{L}) \geq T(\mathcal{K}).$

The first pass over the data computes the support of all single-item sets. Those whose support is less than the threshold are discarded. The second pass computes the support of all item sets of size two that can be formed from pairs of the single items surviving the first pass. In other words, to generate all frequent itemsets with  $|\mathcal{K}| = m$ , we need to consider only candidates such that all of their m ancestral item sets of size  $m-1$  are frequent. Those size-two item sets with support less than the threshold are discarded. Each successive pass over the data considers only those item sets that can be formed by combining those that survived the previous pass with those retained from the first pass. Passes over the data continue until all candidate rules from the previous pass have support less than the specified threshold. The Apriori algorithm requires only one pass over the data for each value of  $|K|$ , which is crucial since we assume the data cannot be fitted into a computer's main memory. If the data are sufficiently sparse (or if the threshold  $t$  is high enough), then the process will terminate in reasonable time even for huge data sets.

There are many additional tricks that can be used as part of this strategy to increase speed and convergence (Agrawal et al., 1995). The Apriori algorithm represents one of the major advances in data mining technology.

Each high support item set  $K$  (14.6) returned by the Apriori algorithm is cast into a set of "association rules." The items  $Z_k, k \in \mathcal{K}$ , are partitioned into two disjoint subsets,  $A \cup B = \mathcal{K}$ , and written

$$
A \Rightarrow B. \tag{14.7}
$$

The first item subset  $A$  is called the "antecedent" and the second  $B$  the "consequent." Association rules are defined to have several properties based on the prevalence of the antecedent and consequent item sets in the data base. The "support" of the rule  $T(A \Rightarrow B)$  is the fraction of observations in the union of the antecedent and consequent, which is just the support of the item set  $K$  from which they were derived. It can be viewed as an estimate (14.5) of the probability of simultaneously observing both item sets  $Pr(A \text{ and } B)$  in a randomly selected market basket. The "confidence" or "predictability"  $C(A \Rightarrow B)$  of the rule is its support divided by the support of the antecedent

$$
C(A \Rightarrow B) = \frac{T(A \Rightarrow B)}{T(A)},
$$
\n(14.8)

which can be viewed as an estimate of  $Pr(B | A)$ . The notation  $Pr(A)$ , the probability of an item set A occurring in a basket, is an abbreviation for

 $Pr(\prod_{k \in A} Z_k = 1)$ . The "expected confidence" is defined as the support of the consequent  $T(B)$ , which is an estimate of the unconditional probability  $Pr(B)$ . Finally, the "lift" of the rule is defined as the confidence divided by the expected confidence

$$
L(A \Rightarrow B) = \frac{C(A \Rightarrow B)}{T(B)}.
$$

This is an estimate of the association measure  $Pr(A \text{ and } B)/Pr(A)Pr(B)$ .

As an example, suppose the item set  $\mathcal{K} = \{$  peanut butter, jelly, bread $\}$ and consider the rule {peanut butter, jelly}  $\Rightarrow$  {bread}. A support value of 0.03 for this rule means that peanut butter, jelly, and bread appeared together in 3% of the market baskets. A confidence of 0.82 for this rule implies that when peanut butter and jelly were purchased, 82% of the time bread was also purchased. If bread appeared in 43% of all market baskets then the rule {peanut butter, jelly}  $\Rightarrow$  {bread} would have a lift of 1.95.

The goal of this analysis is to produce association rules (14.7) with both high values of support and confidence (14.8). The Apriori algorithm returns all item sets with high support as defined by the support threshold  $t$  (14.6). A confidence threshold  $c$  is set, and all rules that can be formed from those item sets (14.6) with confidence greater than this value

$$
\{A \Rightarrow B \mid C(A \Rightarrow B) > c\} \tag{14.9}
$$

are reported. For each item set K of size  $|K|$  there are  $2^{|K|-1}-1$  rules of the form  $A \Rightarrow (K - A)$ ,  $A \subset K$ . Agrawal et al. (1995) present a variant of the Apriori algorithm that can rapidly determine which rules survive the confidence threshold (14.9) from all possible rules that can be formed from the solution item sets (14.6).

The output of the entire analysis is a collection of association rules (14.7) that satisfy the constraints

$$
T(A \Rightarrow B) > t
$$
 and  $C(A \Rightarrow B) > c$ .

These are generally stored in a data base that can be queried by the user. Typical requests might be to display the rules in sorted order of confidence, lift or support. More specifically, one might request such a list conditioned on particular items in the antecedent or especially the consequent. For example, a request might be the following:

Display all transactions in which ice skates are the consequent that have confidence over 80% and support of more than 2%.

This could provide information on those items (antecedent) that predicate sales of ice skates. Focusing on a particular consequent casts the problem into the framework of supervised learning.

Association rules have become a popular tool for analyzing very large commercial data bases in settings where market basket is relevant. That is

when the data can be cast in the form of a multidimensional contingency table. The output is in the form of conjunctive rules (14.4) that are easily understood and interpreted. The Apriori algorithm allows this analysis to be applied to huge data bases, much larger that are amenable to other types of analyses. Association rules are among data mining's biggest successes.

Besides the restrictive form of the data to which they can be applied, association rules have other limitations. Critical to computational feasibility is the support threshold (14.6). The number of solution item sets, their size, and the number of passes required over the data can grow exponentially with decreasing size of this lower bound. Thus, rules with high confidence or lift, but low support, will not be discovered. For example, a high confidence rule such as vodka  $\Rightarrow$  caviar will not be uncovered owing to the low sales volume of the consequent caviar.

## 14.2.3 Example: Market Basket Analysis

We illustrate the use of Apriori on a moderately sized demographics data base. This data set consists of  $N = 9409$  questionnaires filled out by shopping mall customers in the San Francisco Bay Area (Impact Resources, Inc., Columbus OH, 1987). Here we use answers to the first 14 questions, relating to demographics, for illustration. These questions are listed in Table 14.1. The data are seen to consist of a mixture of ordinal and (unordered) categorical variables, many of the latter having more than a few values. There are many missing values.

We used a freeware implementation of the Apriori algorithm due to Christian Borgelt<sup>1</sup>. After removing observations with missing values, each ordinal predictor was cut at its median and coded by two dummy variables; each categorical predictor with k categories was coded by k dummy variables. This resulted in a  $6876 \times 50$  matrix of 6876 observations on 50 dummy variables.

The algorithm found a total of 6288 association rules, involving  $\leq 5$ predictors, with support of at least 10%. Understanding this large set of rules is itself a challenging data analysis task. We will not attempt this here, but only illustrate in Figure 14.2 the relative frequency of each dummy variable in the data (top) and the association rules (bottom). Prevalent categories tend to appear more often in the rules, for example, the first category in language (English). However, others such as occupation are under-represented, with the exception of the first and fifth level.

Here are three examples of association rules found by the Apriori algorithm:

Association rule 1: Support 25%, confidence 99.7% and lift 1.03.

<sup>1</sup>See http://fuzzy.cs.uni-magdeburg.de/∼borgelt.

Image /page/16/Figure/0 description: This image contains two bar charts side-by-side, both displaying relative frequencies of attributes. The left chart is titled "Relative Frequency in Association Rules" with an x-axis ranging from 0.0 to 0.12 and the right chart is titled "Relative Frequency in Data" with an x-axis ranging from 0.0 to 0.06. Both charts have a y-axis labeled "Attribute" ranging from 0 to 50. The attributes listed on the right side of both charts are income, sex, marstat, age, educ, occup, yrs-bay, dualinc, perhous, peryoung, house, typehome, ethnic, and language. The bars in the left chart are generally longer than those in the right chart, indicating higher relative frequencies in association rules compared to the overall data for most attributes.

FIGURE 14.2. Market basket analysis: relative frequency of each dummy variable (coding an input category) in the data (top), and the association rules found by the Apriori algorithm (bottom). by the Apriori algorithm (bottom).able (coding an input category) in the data (top), and the association rules found FIGURE 14.2. Market basket analysis: relative frequency of each dummy vari-

| Feature | Demographic           | $#$ Values | Type        |
|---------|-----------------------|------------|-------------|
| 1       | Sex                   | 2          | Categorical |
| 2       | Marital status        | 5          | Categorical |
| 3       | Age                   | 7          | Ordinal     |
| 4       | Education             | 6          | Ordinal     |
| 5       | Occupation            | 9          | Categorical |
| 6       | Income                | 9          | Ordinal     |
| 7       | Years in Bay Area     | 5          | Ordinal     |
| 8       | Dual incomes          | 3          | Categorical |
| 9       | Number in household   | 9          | Ordinal     |
| 10      | Number of children    | 9          | Ordinal     |
| 11      | Householder status    | 3          | Categorical |
| 12      | Type of home          | 5          | Categorical |
| 13      | Ethnic classification | 8          | Categorical |
| 14      | Language in home      | 3          | Categorical |

TABLE 14.1. Inputs for the demographic data.

 $\sqrt{ }$ number in household  $= 1$ number of children  $= 0$ 1 ⇓ language in home  $=$  *English* 

Association rule 2: Support 13.4%, confidence 80.8%, and lift 2.13.

The following conditions imply the given income:

$$
\text{language in home} = \text{English}
$$

$$
\text{householder status} = \text{own}
$$

$$
\text{occupation} = \{\text{professional/managerial}\}
$$

$$
\Downarrow
$$

$$
\text{income} \geq \$40,000
$$

Association rule 3: Support 26.5%, confidence 82.8% and lift 2.15.

 $\sqrt{ }$  $\begin{array}{|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c$ language in home  $=$  English income  $\langle$  \$40,000 marital status = not married number of children  $= 0$ 1  $\mathbf{I}$  $\overline{1}$  $\overline{1}$ ⇓

education  $\notin \{college\ graduate, graduate\ study\}$ 

We chose the first and third rules based on their high support. The second rule is an association rule with a high-income consequent, and could be used to try to target high-income individuals.

As stated above, we created dummy variables for each category of the input predictors, for example,  $Z_1 = I$ (income  $\langle$  \$40,000) and  $Z_2 =$  $I(\text{income} \geq $40,000)$  for below and above the median income. If we were interested only in finding associations with the high-income category, we would include  $Z_2$  but not  $Z_1$ . This is often the case in actual market basket problems, where we are interested in finding associations with the presence of a relatively rare item, but not associations with its absence.

### 14.2.4 Unsupervised as Supervised Learning

Here we discuss a technique for transforming the density estimation problem into one of supervised function approximation. This forms the basis for the generalized association rules described in the next section.

Let  $q(x)$  be the unknown data probability density to be estimated, and  $q_0(x)$  be a specified probability density function used for reference. For example,  $g_0(x)$  might be the uniform density over the range of the variables. Other possibilities are discussed below. The data set  $x_1, x_2, \ldots, x_N$  is presumed to be an *i.i.d.* random sample drawn from  $g(x)$ . A sample of size  $N_0$ can be drawn from  $g_0(x)$  using Monte Carlo methods. Pooling these two data sets, and assigning mass  $w = N_0/(N + N_0)$  to those drawn from  $g(x)$ , and  $w_0 = N/(N + N_0)$  to those drawn from  $g_0(x)$ , results in a random sample drawn from the mixture density  $(g(x) + g_0(x))/2$ . If one assigns the value  $Y = 1$  to each sample point drawn from  $g(x)$  and  $Y = 0$  those drawn from  $g_0(x)$ , then

$$
\mu(x) = E(Y \mid x) = \frac{g(x)}{g(x) + g_0(x)}
$$
$$
= \frac{g(x)/g_0(x)}{1 + g(x)/g_0(x)} \tag{14.10}
$$

can be estimated by supervised learning using the combined sample

$$
(y_1, x_1), (y_2, x_2), \dots, (y_{N+N_0}, x_{N+N_0})
$$
\n(14.11)

as training data. The resulting estimate  $\hat{\mu}(x)$  can be inverted to provide an estimate for  $g(x)$ 

$$
\hat{g}(x) = g_0(x) \frac{\hat{\mu}(x)}{1 - \hat{\mu}(x)}.
$$
\n(14.12)

Generalized versions of logistic regression (Section 4.4) are especially well suited for this application since the log-odds,

$$
f(x) = \log \frac{g(x)}{g_0(x)},
$$
\n(14.13)

are estimated directly. In this case one has

Image /page/19/Figure/1 description: The image displays two scatter plots side-by-side. Both plots have 'X1' on the horizontal axis and 'X2' on the vertical axis, with numerical labels ranging from -2 to 6 on the vertical axis and -1 to 2 on the horizontal axis. The left plot shows a single cluster of orange dots, exhibiting a curved, upward trend from left to right. The right plot contains two distinct clusters of dots: orange dots concentrated in the lower-middle area and blue dots scattered more broadly across the plot. Overlaid on the right plot are several contour lines, suggesting a density estimation or classification boundary, with the contours appearing to encircle the denser regions of orange dots and extending outwards.

FIGURE 14.3. Density estimation via classification. (Left panel:) Training set of 200 data points. (Right panel:) Training set plus 200 reference data points, generated uniformly over the rectangle containing the training data. The training sample was labeled as class 1, and the reference sample class 0, and a semiparametric logistic regression model was fit to the data. Some contours for  $\hat{g}(x)$  are shown.

$$
\hat{g}(x) = g_0(x) e^{\hat{f}(x)}.
$$
\n(14.14)

An example is shown in Figure 14.3. We generated a training set of size 200 shown in the left panel. The right panel shows the reference data (blue) generated uniformly over the rectangle containing the training data. The training sample was labeled as class 1, and the reference sample class 0, and a logistic regression model, using a tensor product of natural splines (Section 5.2.1), was fit to the data. Some probability contours of  $\hat{\mu}(x)$  are shown in the right panel; these are also the contours of the density estimate  $\hat{g}(x)$ , since  $\hat{g}(x) = \hat{\mu}(x)/(1 - \hat{\mu}(x))$ , is a monotone function. The contours roughly capture the data density.

In principle any reference density can be used for  $g_0(x)$  in (14.14). In practice the accuracy of the estimate  $\hat{q}(x)$  can depend greatly on particular choices. Good choices will depend on the data density  $g(x)$  and the procedure used to estimate (14.10) or (14.13). If accuracy is the goal,  $g_0(x)$ should be chosen so that the resulting functions  $\mu(x)$  or  $f(x)$  are approximated easily by the method being used. However, accuracy is not always the primary goal. Both  $\mu(x)$  and  $f(x)$  are monotonic functions of the density ratio  $g(x)/g_0(x)$ . They can thus be viewed as "contrast" statistics that provide information concerning departures of the data density  $g(x)$  from the chosen reference density  $g_0(x)$ . Therefore, in data analytic settings, a choice for  $g_0(x)$  is dictated by types of departures that are deemed most interesting in the context of the specific problem at hand. For example, if departures from uniformity are of interest,  $g_0(x)$  might be the a uniform density over the range of the variables. If departures from joint normality

are of interest, a good choice for  $g_0(x)$  would be a Gaussian distribution with the same mean vector and covariance matrix as the data. Departures from independence could be investigated by using

$$
g_0(x) = \prod_{j=1}^p g_j(x_j),
$$
\n(14.15)

where  $g_i(x_i)$  is the marginal data density of  $X_i$ , the *j*th coordinate of X. A sample from this independent density (14.15) is easily generated from the data itself by applying a different random permutation to the data values of each of the variables.

As discussed above, unsupervised learning is concerned with revealing properties of the data density  $g(x)$ . Each technique focuses on a particular property or set of properties. Although this approach of transforming the problem to one of supervised learning  $(14.10)$ – $(14.14)$  seems to have been part of the statistics folklore for some time, it does not appear to have had much impact despite its potential to bring well-developed supervised learning methodology to bear on unsupervised learning problems. One reason may be that the problem must be enlarged with a simulated data set generated by Monte Carlo techniques. Since the size of this data set should be at least as large as the data sample  $N_0 \geq N$ , the computation and memory requirements of the estimation procedure are at least doubled. Also, substantial computation may be required to generate the Monte Carlo sample itself. Although perhaps a deterrent in the past, these increased computational requirements are becoming much less of a burden as increased resources become routinely available. We illustrate the use of supervising learning methods for unsupervised learning in the next section.

#### 14.2.5 Generalized Association Rules

The more general problem (14.2) of finding high-density regions in the data space can be addressed using the supervised learning approach described above. Although not applicable to the huge data bases for which market basket analysis is feasible, useful information can be obtained from moderately sized data sets. The problem (14.2) can be formulated as finding subsets of the integers  $\mathcal{J} \subset \{1, 2, \ldots, p\}$  and corresponding value subsets  $s_j, j \in \mathcal{J}$  for the corresponding variables  $X_j$ , such that

$$
\widehat{\Pr}\left(\bigcap_{j\in\mathcal{J}} (X_j \in s_j)\right) = \frac{1}{N} \sum_{i=1}^N I\left(\bigcap_{j\in\mathcal{J}} (x_{ij} \in s_j)\right) \tag{14.16}
$$

is large. Following the nomenclature of association rule analysis,  $\{(X_i \in$  $s_j$ } $_{j \in \mathcal{J}}$  will be called a "generalized" item set. The subsets  $s_j$  corresponding to quantitative variables are taken to be contiguous intervals within

their range of values, and subsets for categorical variables can involve more than a single value. The ambitious nature of this formulation precludes a thorough search for all generalized item sets with support (14.16) greater than a specified minimum threshold, as was possible in the more restrictive setting of market basket analysis. Heuristic search methods must be employed, and the most one can hope for is to find a useful collection of such generalized item sets.

Both market basket analysis (14.5) and the generalized formulation (14.16) implicitly reference the uniform probability distribution. One seeks item sets that are more frequent than would be expected if all joint data values  $(x_1, x_2, \ldots, x_N)$  were uniformly distributed. This favors the discovery of item sets whose marginal constituents  $(X_i \in s_i)$  are *individually* frequent, that is, the quantity

$$
\frac{1}{N} \sum_{i=1}^{N} I(x_{ij} \in s_j)
$$
\n(14.17)

is large. Conjunctions of frequent subsets (14.17) will tend to appear more often among item sets of high support (14.16) than conjunctions of marginally less frequent subsets. This is why the rule vodka  $\Rightarrow$  caviar is not likely to be discovered in spite of a high association (lift); neither item has high marginal support, so that their joint support is especially small. Reference to the uniform distribution can cause highly frequent item sets with low associations among their constituents to dominate the collection of highest support item sets.

Highly frequent subsets  $s_i$  are formed as disjunctions of the most frequent  $X_i$ -values. Using the product of the variable marginal data densities (14.15) as a reference distribution removes the preference for highly frequent values of the individual variables in the discovered item sets. This is because the density ratio  $g(x)/g_0(x)$  is uniform if there are no associations among the variables (complete independence), regardless of the frequency distribution of the individual variable values. Rules like vodka  $\Rightarrow$  caviar would have a chance to emerge. It is not clear however, how to incorporate reference distributions other than the uniform into the Apriori algorithm. As explained in Section 14.2.4, it is straightforward to generate a sample from the product density (14.15), given the original data set.

After choosing a reference distribution, and drawing a sample from it as in (14.11), one has a supervised learning problem with a binary-valued output variable  $Y \in \{0,1\}$ . The goal is to use this training data to find regions

$$
R = \bigcap_{j \in \mathcal{J}} (X_j \in s_j) \tag{14.18}
$$

for which the target function  $\mu(x) = E(Y | x)$  is relatively large. In addition, one might wish to require that the data support of these regions

14.2 Association Rules 499

$$
T(R) = \int_{x \in R} g(x) dx \qquad (14.19)
$$

not be too small.

## 14.2.6 Choice of Supervised Learning Method

The regions (14.18) are defined by conjunctive rules. Hence supervised methods that learn such rules would be most appropriate in this context. The terminal nodes of a CART decision tree are defined by rules precisely of the form (14.18). Applying CART to the pooled data (14.11) will produce a decision tree that attempts to model the target (14.10) over the entire data space by a disjoint set of regions (terminal nodes). Each region is defined by a rule of the form  $(14.18)$ . Those terminal nodes t with high average y-values

$$
\bar{y}_t = \operatorname{ave}(y_i \,|\, x_i \in t)
$$

are candidates for high-support generalized item sets (14.16). The actual (data) support is given by

$$
T(R) = \bar{y}_t \cdot \frac{N_t}{N + N_0},
$$

where  $N_t$  is the number of (pooled) observations within the region represented by the terminal node. By examining the resulting decision tree, one might discover interesting generalized item sets of relatively high-support. These can then be partitioned into antecedents and consequents in a search for generalized association rules of high confidence and/or lift.

Another natural learning method for this purpose is the patient rule induction method PRIM described in Section 9.3. PRIM also produces rules precisely of the form (14.18), but it is especially designed for finding high-support regions that maximize the average target  $(14.10)$  value within them, rather than trying to model the target function over the entire data space. It also provides more control over the support/average-target-value tradeoff.

Exercise 14.3 addresses an issue that arises with either of these methods when we generate random data from the product of the marginal distributions.

## 14.2.7 Example: Market Basket Analysis (Continued)

We illustrate the use of PRIM on the demographics data of Table 14.1.

Three of the high-support generalized item sets emerging from the PRIM analysis were the following:

Item set 1: Support= 24%.

 $\sqrt{ }$  $\mathbf{I}$ 

 $\text{martial status} = \text{married}$ householder status  $=$  own type of home  $\neq$  apartment 1  $\mathbf{I}$ 

Item set 2: Support= 24%.

 $\sqrt{ }$  $\overline{1}$  $\overline{1}$  $\overline{1}$ age  $\leq 24$ marital status  $∈ { living together not married, single}$  $\begin{array}{lll} \text{occupation} & \notin & \{ \text{professional,}\ \text{homemaker,} \ \text{retried} \} \end{array}$ householder status  $\in$  {rent, live with family}

1  $\mathbf{I}$  $\overline{1}$  $\overline{1}$ 

> 1  $\overline{1}$  $\overline{1}$  $\overline{1}$  $\overline{1}$  $\overline{1}$  $\overline{1}$  $\overline{1}$

Item set 3: Support= 15%.

| householder status $=$ rent  |                                                        |
|------------------------------|--------------------------------------------------------|
| type of home $\neq$ house    |                                                        |
| number in household $\leq$ 2 |                                                        |
| number of children $= 0$     |                                                        |
|                              | occupation $\notin \{homemaker, student, unemployed\}$ |
|                              | income $\in$ [\$20,000, \$150,000]                     |

Generalized association rules derived from these item sets with confidence (14.8) greater than 95% are the following:

Association rule 1: Support 25%, confidence 99.7% and lift 1.35.

 $\begin{bmatrix} \text{marital status} & = & \text{married} \\ \text{householder status} & = & \text{own} \end{bmatrix}$ ⇓ type of home  $\neq$  apartment

Association rule 2: Support 25%, confidence 98.7% and lift 1.97.

 $\sqrt{ }$  $\mathbf{I}$ age  $\leq 24$  $\begin{array}{lll} \text{occupation} & \notin & \{ \text{progressional,}\ \text{homemaker,} \ \text{retried} \} \end{array}$ householder status  $\in$  {rent, live with family} 1  $\mathbf{I}$ ⇓

marital status  $\in \{single, living\ together\-not\ married\}$ 

Association rule 3: Support 25%, confidence 95.9% and lift 2.61.

 $\left[ \begin{array}{ccc} \text{householder status} & = & \text{own} \\ \text{type of home} & \neq & \text{apartment} \end{array} \right]$ ⇓ marital status  $= married$ 

1  $\mathbf{I}$  $\overline{1}$  $\overline{1}$  $\overline{1}$  $\overline{1}$ 

Association rule 4: Support 15%, confidence 95.4% and lift 1.50.

 $\sqrt{ }$  $\mathbf{I}$  $\overline{1}$  $\overline{1}$  $\overline{1}$  $\overline{1}$ householder status  $=$  rent type of home  $\neq$  house<br>r in household  $\leq$  2  $\begin{tabular}{ll} number in household & $\leq$ \\ occupation & $\not \in$ \end{tabular}$  $\begin{array}{lll} \text{occupation} & \notin & \{homemaker, \ student, \ unemployed\} \end{array}$ income ∈  $[$20,000, $150,000]$ ⇓

number of children  $= 0$ 

There are no great surprises among these particular rules. For the most part they verify intuition. In other contexts where there is less prior information available, unexpected results have a greater chance to emerge. These results do illustrate the type of information generalized association rules can provide, and that the supervised learning approach, coupled with a ruled induction method such as CART or PRIM, can uncover item sets exhibiting high associations among their constituents.

How do these generalized association rules compare to those found earlier by the Apriori algorithm? Since the Apriori procedure gives thousands of rules, it is difficult to compare them. However some general points can be made. The Apriori algorithm is exhaustive—it finds all rules with support greater than a specified amount. In contrast, PRIM is a greedy algorithm and is not guaranteed to give an "optimal" set of rules. On the other hand, the Apriori algorithm can deal only with dummy variables and hence could not find some of the above rules. For example, since type of home is a categorical input, with a dummy variable for each level, Apriori could not find a rule involving the set

type of home  $\neq$  apartment.

To find this set, we would have to code a dummy variable for apartment versus the other categories of type of home. It will not generally be feasible to precode all such potentially interesting comparisons.

# 14.3 Cluster Analysis

Cluster analysis, also called data segmentation, has a variety of goals. All relate to grouping or segmenting a collection of objects into subsets or "clusters," such that those within each cluster are more closely related to one another than objects assigned to different clusters. An object can be described by a set of measurements, or by its relation to other objects. In addition, the goal is sometimes to arrange the clusters into a natural hierarchy. This involves successively grouping the clusters themselves so

Image /page/25/Figure/1 description: A scatter plot shows three clusters of data points. The x-axis is labeled X1 and the y-axis is labeled X2. The bottom cluster consists of numerous orange dots. Above the orange dots, there is a cluster of light blue dots, and above the light blue dots, there is a cluster of teal dots. The teal dots are more concentrated and appear to be in the upper left quadrant of the plot, while the light blue dots are in the middle, and the orange dots are spread across the bottom.

FIGURE 14.4. Simulated data in the plane, clustered into three classes (represented by orange, blue and green) by the K-means clustering algorithm

that at each level of the hierarchy, clusters within the same group are more similar to each other than those in different groups.

Cluster analysis is also used to form descriptive statistics to ascertain whether or not the data consists of a set distinct subgroups, each group representing objects with substantially different properties. This latter goal requires an assessment of the degree of difference between the objects assigned to the respective clusters.

Central to all of the goals of cluster analysis is the notion of the degree of similarity (or dissimilarity) between the individual objects being clustered. A clustering method attempts to group the objects based on the definition of similarity supplied to it. This can only come from subject matter considerations. The situation is somewhat similar to the specification of a loss or cost function in prediction problems (supervised learning). There the cost associated with an inaccurate prediction depends on considerations outside the data.

Figure 14.4 shows some simulated data clustered into three groups via the popular K-means algorithm. In this case two of the clusters are not well separated, so that "segmentation" more accurately describes the part of this process than "clustering." K-means clustering starts with guesses for the three cluster centers. Then it alternates the following steps until convergence:

• for each data point, the closest cluster center (in Euclidean distance) is identified;

• each cluster center is replaced by the coordinate-wise average of all data points that are closest to it.

We describe K-means clustering in more detail later, including the problem of how to choose the number of clusters (three in this example). Kmeans clustering is a top-down procedure, while other cluster approaches that we discuss are bottom-up. Fundamental to all clustering techniques is the choice of distance or dissimilarity measure between two objects. We first discuss distance measures before describing a variety of algorithms for clustering.

### 14.3.1 Proximity Matrices

Sometimes the data is represented directly in terms of the proximity (alikeness or affinity) between pairs of objects. These can be either similarities or dissimilarities (difference or lack of affinity). For example, in social science experiments, participants are asked to judge by how much certain objects differ from one another. Dissimilarities can then be computed by averaging over the collection of such judgments. This type of data can be represented by an  $N \times N$  matrix **D**, where N is the number of objects, and each element  $d_{ii'}$  records the proximity between the *i*th and *i*'th objects. This matrix is then provided as input to the clustering algorithm.

Most algorithms presume a matrix of dissimilarities with nonnegative entries and zero diagonal elements:  $d_{ii} = 0, i = 1, 2, \ldots, N$ . If the original data were collected as similarities, a suitable monotone-decreasing function can be used to convert them to dissimilarities. Also, most algorithms assume symmetric dissimilarity matrices, so if the original matrix  $D$  is not symmetric it must be replaced by  $(D + D<sup>T</sup>)/2$ . Subjectively judged dissimilarities are seldom distances in the strict sense, since the triangle inequality  $d_{ii'} \leq d_{ik} + d_{i'k}$ , for all  $k \in \{1, \ldots, N\}$  does not hold. Thus, some algorithms that assume distances cannot be used with such data.

#### 14.3.2 Dissimilarities Based on Attributes

Most often we have measurements  $x_{ij}$  for  $i = 1, 2, ..., N$ , on variables  $j = 1, 2, \ldots, p$  (also called *attributes*). Since most of the popular clustering algorithms take a dissimilarity matrix as their input, we must first construct pairwise dissimilarities between the observations. In the most common case, we define a dissimilarity  $d_j(x_{ij}, x_{i'j})$  between values of the jth attribute, and then define

$$
D(x_i, x_{i'}) = \sum_{j=1}^{p} d_j(x_{ij}, x_{i'j})
$$
\n(14.20)

as the dissimilarity between objects  $i$  and  $i'$ . By far the most common choice is squared distance

$$
d_j(x_{ij}, x_{i'j}) = (x_{ij} - x_{i'j})^2.
$$
\n(14.21)

However, other choices are possible, and can lead to potentially different results. For nonquantitative attributes (e.g., categorical data), squared distance may not be appropriate. In addition, it is sometimes desirable to weigh attributes differently rather than giving them equal weight as in  $(14.20).$ 

We first discuss alternatives in terms of the attribute type:

Quantitative variables. Measurements of this type of variable or attribute are represented by continuous real-valued numbers. It is natural to define the "error" between them as a monotone-increasing function of their absolute difference

$$
d(x_i, x_{i'}) = l(|x_i - x_{i'}|).
$$

Besides squared-error loss  $(x_i - x_{i'})^2$ , a common choice is the identity (absolute error). The former places more emphasis on larger differences than smaller ones. Alternatively, clustering can be based on the correlation

$$
\rho(x_i, x_{i'}) = \frac{\sum_j (x_{ij} - \bar{x}_i)(x_{i'j} - \bar{x}_{i'})}{\sqrt{\sum_j (x_{ij} - \bar{x}_i)^2 \sum_j (x_{i'j} - \bar{x}_{i'})^2}},
$$
\n(14.22)

with  $\bar{x}_i = \sum_j x_{ij}/p$ . Note that this is averaged over *variables*, not observations. If the *observations* are first standardized, then  $\sum_{j} (x_{ij} (x_{i'j})^2 \propto 2(1-\rho(x_i, x_{i'}))$ . Hence clustering based on correlation (similarity) is equivalent to that based on squared distance (dissimilarity).

Ordinal variables. The values of this type of variable are often represented as contiguous integers, and the realizable values are considered to be an ordered set. Examples are academic grades (A, B, C, D, F), degree of preference (can't stand, dislike, OK, like, terrific). Rank data are a special kind of ordinal data. Error measures for ordinal variables are generally defined by replacing their M original values with

$$
\frac{i - 1/2}{M}, \ i = 1, \dots, M \tag{14.23}
$$

in the prescribed order of their original values. They are then treated as quantitative variables on this scale.

Categorical variables. With unordered categorical (also called nominal) variables, the degree-of-difference between pairs of values must be delineated explicitly. If the variable assumes  $M$  distinct values, these can be arranged in a symmetric  $M \times M$  matrix with elements  $L_{rr'} =$  $L_{r'r}, L_{rr} = 0, L_{rr'} \ge 0$ . The most common choice is  $L_{rr'} = 1$  for all  $r \neq r'$ , while unequal losses can be used to emphasize some errors more than others.
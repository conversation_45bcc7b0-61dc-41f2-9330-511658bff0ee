This is a quadratic function of **w**, and so we can obtain the corresponding variational approximation to the posterior distribution by identifying the linear and quadratic terms in **w**, giving a Gaussian variational posterior of the form

$$
q(\mathbf{w}) = \mathcal{N}(\mathbf{w}|\mathbf{m}_N, \mathbf{S}_N)
$$
 (10.156)

where

$$
\mathbf{m}_N = \mathbf{S}_N \left( \mathbf{S}_0^{-1} \mathbf{m}_0 + \sum_{n=1}^N (t_n - 1/2) \phi_n \right) \quad (10.157)
$$

$$
\mathbf{S}_N^{-1} = \mathbf{S}_0^{-1} + 2 \sum_{n=1}^N \lambda(\xi_n) \phi_n \phi_n^{\mathrm{T}}.
$$
 (10.158)

As with the Laplace framework, we have again obtained a Gaussian approximation to the posterior distribution. However, the additional flexibility provided by the variational parameters  $\{\xi_n\}$  leads to improved accuracy in the approximation (<PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>, 2000).

Here we have considered a batch learning context in which all of the training data is available at once. However, Bayesian methods are intrinsically well suited to sequential learning in which the data points are processed one at a time and then discarded. The formulation of this variational approach for the sequential case is *Exercise 10.32* straightforward.

> Note that the bound given by (10.149) applies only to the two-class problem and so this approach does not directly generalize to classification problems with  $K > 2$ classes. An alternative bound for the multiclass case has been explored by Gibbs (1997).

### **10.6.2 Optimizing the variational parameters**

We now have a normalized Gaussian approximation to the posterior distribution, which we shall use shortly to evaluate the predictive distribution for new data points. First, however, we need to determine the variational parameters  $\{\xi_n\}$  by maximizing the lower bound on the marginal likelihood.

To do this, we substitute the inequality (10.152) back into the marginal likelihood to give

$$
\ln p(\mathbf{t}) = \ln \int p(\mathbf{t}|\mathbf{w})p(\mathbf{w}) \, \mathrm{d}\mathbf{w} \geqslant \ln \int h(\mathbf{w}, \boldsymbol{\xi})p(\mathbf{w}) \, \mathrm{d}\mathbf{w} = \mathcal{L}(\boldsymbol{\xi}). \tag{10.159}
$$

As with the optimization of the hyperparameter  $\alpha$  in the linear regression model of Section 3.5, there are two approaches to determining the  $\xi_n$ . In the first approach, we recognize that the function  $\mathcal{L}(\xi)$  is defined by an integration over w and so we can view **w** as a latent variable and invoke the EM algorithm. In the second approach, we integrate over **w** analytically and then perform a direct maximization over *<sup>ξ</sup>*. Let us begin by considering the EM approach.

The EM algorithm starts by choosing some initial values for the parameters  $\{\xi_n\}$ , which we denote collectively by  $\xi^{\text{old}}$ . In the E step of the EM algorithm,

*Exercise 10.32*

we then use these parameter values to find the posterior distribution over **w**, which is given by (10.156). In the M step, we then maximize the expected complete-data log likelihood which is given by

$$
Q(\xi, \xi^{\text{old}}) = \mathbb{E}\left[\ln h(\mathbf{w}, \xi)p(\mathbf{w})\right]
$$
 (10.160)

where the expectation is taken with respect to the posterior distribution  $q(\mathbf{w})$  evaluated using  $\boldsymbol{\xi}^{\text{old}}$ . Noting that  $p(\mathbf{w})$  does not depend on  $\boldsymbol{\xi}$ , and substituting for  $h(\mathbf{w}, \boldsymbol{\xi})$ we obtain

$$
Q(\boldsymbol{\xi}, \boldsymbol{\xi}^{\text{old}}) = \sum_{n=1}^{N} \left\{ \ln \sigma(\xi_n) - \xi_n/2 - \lambda(\xi_n) (\boldsymbol{\phi}_n^{\text{T}} \mathbb{E}[\mathbf{w} \mathbf{w}^{\text{T}}] \boldsymbol{\phi}_n - \xi_n^2) \right\} + \text{const}
$$
\n(10.161)

where 'const' denotes terms that are independent of *ξ*. We now set the derivative with respect to  $\xi_n$  equal to zero. A few lines of algebra, making use of the definitions of  $\sigma(\xi)$  and  $\lambda(\xi)$ , then gives

$$
0 = \lambda'(\xi_n) (\boldsymbol{\phi}_n^{\mathrm{T}} \mathbb{E}[\mathbf{w}\mathbf{w}^{\mathrm{T}}] \boldsymbol{\phi}_n - \xi_n^2).
$$
 (10.162)

We now note that  $\lambda'(\xi)$  is a monotonic function of  $\xi$  for  $\xi \geq 0$ , and that we can restrict attention to nonnegative values of  $\xi$  without loss of generality due to the symmetry of the bound around  $\xi = 0$ . Thus  $\lambda'(\xi) \neq 0$ , and hence we obtain the *Exercise 10.33* following re-estimation equations

$$
(\xi_n^{\text{new}})^2 = \boldsymbol{\phi}_n^{\text{T}} \mathbb{E}[\mathbf{w}\mathbf{w}^{\text{T}}] \boldsymbol{\phi}_n = \boldsymbol{\phi}_n^{\text{T}} \left(\mathbf{S}_N + \mathbf{m}_N \mathbf{m}_N^{\text{T}}\right) \boldsymbol{\phi}_n \tag{10.163}
$$

where we have used  $(10.156)$ .

Let us summarize the EM algorithm for finding the variational posterior distribution. We first initialize the variational parameters  $\xi^{\text{old}}$ . In the E step, we evaluate the posterior distribution over **w** given by (10.156), in which the mean and covariance are defined by (10.157) and (10.158). In the M step, we then use this variational posterior to compute a new value for  $\xi$  given by (10.163). The E and M steps are repeated until a suitable convergence criterion is satisfied, which in practice typically requires only a few iterations.

An alternative approach to obtaining re-estimation equations for *ξ* is to note that in the integral over **w** in the definition (10.159) of the lower bound  $\mathcal{L}(\xi)$ , the integrand has a Gaussian-like form and so the integral can be evaluated analytically. Having evaluated the integral, we can then differentiate with respect to  $\xi_n$ . It turns out that this gives rise to exactly the same re-estimation equations as does the EM *Exercise 10.34* approach given by (10.163).

As we have emphasized already, in the application of variational methods it is useful to be able to evaluate the lower bound  $\mathcal{L}(\xi)$  given by (10.159). The integration over **w** can be performed analytically by noting that  $p(\mathbf{w})$  is Gaussian and  $h(\mathbf{w}, \boldsymbol{\xi})$ is the exponential of a quadratic function of **w**. Thus, by completing the square and making use of the standard result for the normalization coefficient of a Gaussian *Exercise 10.35* distribution, we can obtain a closed form solution which takes the form

*Exercise 10.33*

*Exercise 10.34*

*Exercise 10.35*

Image /page/2/Figure/1 description: The image contains two plots side-by-side. Both plots show data points represented by red crosses and red circles on a 2D plane with x and y axes ranging from -4 to 4 and -6 to 6 respectively. The left plot displays contour lines labeled with values 0.01, 0.25, 0.75, and 0.99, indicating a density or probability distribution. The red crosses are clustered in the upper left quadrant, while the red circles are clustered in the lower right quadrant. The contour lines are curved and appear to separate these two clusters. The right plot shows several straight lines in different colors (purple, blue, green, cyan, yellow) that act as decision boundaries, also separating the red crosses from the red circles. The clusters of red crosses and red circles are positioned similarly to the left plot.

**Figure 10.13** Illustration of the Bayesian approach to logistic regression for a simple linearly separable data set. The plot on the left shows the predictive distribution obtained using variational inference. We see that the decision boundary lies roughly mid way between the clusters of data points, and that the contours of the predictive distribution splay out away from the data reflecting the greater uncertainty in the classification of such regions. The plot on the right shows the decision boundaries corresponding to five samples of the parameter vector **w** drawn from the posterior distribution  $p(\mathbf{w}|\mathbf{t})$ .

$$
\mathcal{L}(\xi) = \frac{1}{2} \ln \frac{|\mathbf{S}_N|}{|\mathbf{S}_0|} - \frac{1}{2} \mathbf{m}_N^{\mathrm{T}} \mathbf{S}_N^{-1} \mathbf{m}_N + \frac{1}{2} \mathbf{m}_0^{\mathrm{T}} \mathbf{S}_0^{-1} \mathbf{m}_0 + \sum_{n=1}^N \left\{ \ln \sigma(\xi_n) - \frac{1}{2} \xi_n - \lambda(\xi_n) \xi_n^2 \right\}. (10.164)
$$

This variational framework can also be applied to situations in which the data is arriving sequentially (Jaakkola and Jordan, 2000). In this case we maintain a Gaussian posterior distribution over **w**, which is initialized using the prior  $p(\mathbf{w})$ . As each data point arrives, the posterior is updated by making use of the bound (10.151) and then normalized to give an updated posterior distribution.

The predictive distribution is obtained by marginalizing over the posterior distribution, and takes the same form as for the Laplace approximation discussed in Section 4.5.2. Figure 10.13 shows the variational predictive distributions for a synthetic data set. This example provides interesting insights into the concept of 'large margin', which was discussed in Section 7.1 and which has qualitatively similar behaviour to the Bayesian solution.

### **10.6.3 Inference of hyperparameters**

So far, we have treated the hyperparameter  $\alpha$  in the prior distribution as a known constant. We now extend the Bayesian logistic regression model to allow the value of this parameter to be inferred from the data set. This can be achieved by combining the global and local variational approximations into a single framework, so as to maintain a lower bound on the marginal likelihood at each stage. Such a combined approach was adopted by Bishop and Svensen (2003) in the context of a Bayesian ´ treatment of the hierarchical mixture of experts model.

Specifically, we consider once again a simple isotropic Gaussian prior distribution of the form

$$
p(\mathbf{w}|\alpha) = \mathcal{N}(\mathbf{w}|\mathbf{0}, \alpha^{-1}\mathbf{I}).
$$
\n(10.165)

Our analysis is readily extended to more general Gaussian priors, for instance if we wish to associate a different hyperparameter with different subsets of the parameters  $w_i$ . As usual, we consider a conjugate hyperprior over  $\alpha$  given by a gamma distribution

$$
p(\alpha) = \text{Gam}(\alpha|a_0, b_0) \tag{10.166}
$$

governed by the constants  $a_0$  and  $b_0$ .

The marginal likelihood for this model now takes the form

$$
p(\mathbf{t}) = \iint p(\mathbf{w}, \alpha, \mathbf{t}) \, d\mathbf{w} \, d\alpha \tag{10.167}
$$

where the joint distribution is given by

$$
p(\mathbf{w}, \alpha, \mathbf{t}) = p(\mathbf{t}|\mathbf{w})p(\mathbf{w}|\alpha)p(\alpha).
$$
 (10.168)

We are now faced with an analytically intractable integration over **w** and  $\alpha$ , which we shall tackle by using both the local and global variational approaches in the same model

To begin with, we introduce a variational distribution  $q(\mathbf{w}, \alpha)$ , and then apply the decomposition (10.2), which in this instance takes the form

$$
\ln p(\mathbf{t}) = \mathcal{L}(q) + \mathrm{KL}(q||p) \tag{10.169}
$$

where the lower bound  $\mathcal{L}(q)$  and the Kullback-Leibler divergence  $KL(q||p)$  are defined by

$$
\mathcal{L}(q) = \iint q(\mathbf{w}, \alpha) \ln \left\{ \frac{p(\mathbf{w}, \alpha, \mathbf{t})}{q(\mathbf{w}, \alpha)} \right\} d\mathbf{w} d\alpha \qquad (10.170)
$$

$$
KL(q||p) = -\iint q(\mathbf{w}, \alpha) \ln \left\{ \frac{p(\mathbf{w}, \alpha | \mathbf{t})}{q(\mathbf{w}, \alpha)} \right\} d\mathbf{w} d\alpha.
$$
 (10.171)

At this point, the lower bound  $\mathcal{L}(q)$  is still intractable due to the form of the likelihood factor  $p(\mathbf{t}|\mathbf{w})$ . We therefore apply the local variational bound to each of the logistic sigmoid factors as before. This allows us to use the inequality (10.152) and place a lower bound on  $\mathcal{L}(q)$ , which will therefore also be a lower bound on the log marginal likelihood

$$
\ln p(\mathbf{t}) \geqslant \mathcal{L}(q) \geqslant \tilde{\mathcal{L}}(q, \xi)
$$

$$
= \iint q(\mathbf{w}, \alpha) \ln \left\{ \frac{h(\mathbf{w}, \xi) p(\mathbf{w} | \alpha) p(\alpha)}{q(\mathbf{w}, \alpha)} \right\} d\mathbf{w} d\alpha. \quad (10.172)
$$

Next we assume that the variational distribution factorizes between parameters and hyperparameters so that

$$
q(\mathbf{w}, \alpha) = q(\mathbf{w})q(\alpha). \tag{10.173}
$$

With this factorization we can appeal to the general result (10.9) to find expressions for the optimal factors. Consider first the distribution  $q(\mathbf{w})$ . Discarding terms that are independent of **w**, we have

$$
\ln q(\mathbf{w}) = \mathbb{E}_{\alpha} [\ln \{ h(\mathbf{w}, \boldsymbol{\xi}) p(\mathbf{w} | \alpha) p(\alpha) \}] + \text{const}
$$
  
=  $\ln h(\mathbf{w}, \boldsymbol{\xi}) + \mathbb{E}_{\alpha} [\ln p(\mathbf{w} | \alpha) ] + \text{const.}$ 

We now substitute for  $\ln h(\mathbf{w}, \boldsymbol{\xi})$  using (10.153), and for  $\ln p(\mathbf{w}|\alpha)$  using (10.165), giving

$$
\ln q(\mathbf{w}) = -\frac{\mathbb{E}[\alpha]}{2}\mathbf{w}^{\mathrm{T}}\mathbf{w} + \sum_{n=1}^{N} \left\{ (t_n - 1/2)\mathbf{w}^{\mathrm{T}}\boldsymbol{\phi}_n - \lambda(\xi_n)\mathbf{w}^{\mathrm{T}}\boldsymbol{\phi}_n\boldsymbol{\phi}_n^{\mathrm{T}}\mathbf{w} \right\} + \text{const.}
$$

We see that this is a quadratic function of **w** and so the solution for  $q(\mathbf{w})$  will be Gaussian. Completing the square in the usual way, we obtain

$$
q(\mathbf{w}) = \mathcal{N}(\mathbf{w}|\boldsymbol{\mu}_N, \boldsymbol{\Sigma}_N)
$$
 (10.174)

where we have defined

$$
\Sigma_N^{-1} \mu_N = \sum_{n=1}^N (t_n - 1/2) \phi_n \qquad (10.175)
$$

$$
\Sigma_N^{-1} = \mathbb{E}[\alpha] \mathbf{I} + 2 \sum_{n=1}^N \lambda(\xi_n) \phi_n \phi_n^{\mathrm{T}}.
$$
 (10.176)

Similarly, the optimal solution for the factor  $q(\alpha)$  is obtained from

$$
\ln q(\alpha) = \mathbb{E}_{\mathbf{w}} [\ln p(\mathbf{w}|\alpha)] + \ln p(\alpha) + \text{const.}
$$

Substituting for  $\ln p(\mathbf{w}|\alpha)$  using (10.165), and for  $\ln p(\alpha)$  using (10.166), we obtain

$$
\ln q(\alpha) = \frac{M}{2} \ln \alpha - \frac{\alpha}{2} \mathbb{E} \left[ \mathbf{w}^{\mathrm{T}} \mathbf{w} \right] + (a_0 - 1) \ln \alpha - b_0 \alpha + \text{const.}
$$

We recognize this as the log of a gamma distribution, and so we obtain

$$
q(\alpha) = \text{Gam}(\alpha|a_N, b_N) = \frac{1}{\Gamma(a_0)} a_0^{b_0} \alpha^{a_0 - 1} e^{-b_0 \alpha}
$$
 (10.177)

where

$$
a_N = a_0 + \frac{M}{2} \tag{10.178}
$$

$$
b_N = b_0 + \frac{1}{2} \mathbb{E}_{\mathbf{w}} \left[ \mathbf{w}^{\mathrm{T}} \mathbf{w} \right]. \tag{10.179}
$$

We also need to optimize the variational parameters  $\xi_n$ , and this is also done by maximizing the lower bound  $\mathcal{L}(q, \boldsymbol{\xi})$ . Omitting terms that are independent of  $\boldsymbol{\xi}$ , and integrating over  $\alpha$ , we have

$$
\widetilde{\mathcal{L}}(q,\xi) = \int q(\mathbf{w}) \ln h(\mathbf{w}, \xi) d\mathbf{w} + \text{const.}
$$
 (10.180)

Note that this has precisely the same form as (10.159), and so we can again appeal to our earlier result (10.163), which can be obtained by direct optimization of the marginal likelihood function, leading to re-estimation equations of the form

$$
(\xi_n^{\text{new}})^2 = \boldsymbol{\phi}_n^{\text{T}} \left( \boldsymbol{\Sigma}_N + \boldsymbol{\mu}_N \boldsymbol{\mu}_N^{\text{T}} \right) \boldsymbol{\phi}_n. \tag{10.181}
$$

We have obtained re-estimation equations for the three quantities  $q(\mathbf{w})$ ,  $q(\alpha)$ , and  $\xi$ , and so after making suitable initializations, we can cycle through these quan-*Appendix B* tities, updating each in turn. The required moments are given by

$$
\mathbb{E}\left[\alpha\right] = \frac{a_N}{b_N} \tag{10.182}
$$

$$
\mathbb{E}\left[\mathbf{w}^{\mathrm{T}}\mathbf{w}\right] = \mathbf{\Sigma}_N + \boldsymbol{\mu}_N^{\mathrm{T}}\boldsymbol{\mu}_N. \tag{10.183}
$$

## **10.7. Expectation Propagation**

We conclude this chapter by discussing an alternative form of deterministic approximate inference, known as *expectation propagation* or *EP* (Minka, 2001a; Minka, 2001b). As with the variational Bayes methods discussed so far, this too is based on the minimization of a Kullback-Leibler divergence but now of the reverse form, which gives the approximation rather different properties.

Consider for a moment the problem of minimizing  $KL(p||q)$  with respect to  $q(\mathbf{z})$ when  $p(\mathbf{z})$  is a fixed distribution and  $q(\mathbf{z})$  is a member of the exponential family and so, from (2.194), can be written in the form

$$
q(\mathbf{z}) = h(\mathbf{z})g(\boldsymbol{\eta}) \exp \{ \boldsymbol{\eta}^{\mathrm{T}} \mathbf{u}(\mathbf{z}) \}.
$$
 (10.184)

As a function of *η*, the Kullback-Leibler divergence then becomes

$$
KL(p||q) = -\ln g(\boldsymbol{\eta}) - \boldsymbol{\eta}^{T} \mathbb{E}_{p(\mathbf{z})}[\mathbf{u}(\mathbf{z})] + \text{const}
$$
 (10.185)

where the constant terms are independent of the natural parameters *η*. We can minimize  $KL(p||q)$  within this family of distributions by setting the gradient with respect to *η* to zero, giving

$$
-\nabla \ln g(\boldsymbol{\eta}) = \mathbb{E}_{p(\mathbf{z})}[\mathbf{u}(\mathbf{z})]. \tag{10.186}
$$

However, we have already seen in (2.226) that the negative gradient of  $\ln g(\eta)$  is given by the expectation of  $\mathbf{u}(\mathbf{z})$  under the distribution  $q(\mathbf{z})$ . Equating these two results, we obtain

$$
\mathbb{E}_{q(\mathbf{z})}[\mathbf{u}(\mathbf{z})] = \mathbb{E}_{p(\mathbf{z})}[\mathbf{u}(\mathbf{z})].
$$
\n(10.187)

*Appendix B*

We see that the optimum solution simply corresponds to matching the expected sufficient statistics. So, for instance, if  $q(\mathbf{z})$  is a Gaussian  $\mathcal{N}(\mathbf{z}|\boldsymbol{\mu}, \boldsymbol{\Sigma})$  then we minimize the Kullback-Leibler divergence by setting the mean  $\mu$  of  $q(z)$  equal to the mean of the distribution  $p(z)$  and the covariance  $\Sigma$  equal to the covariance of  $p(z)$ . This is sometimes called *moment matching*. An example of this was seen in Figure 10.3(a).

Now let us exploit this result to obtain a practical algorithm for approximate inference. For many probabilistic models, the joint distribution of data  $D$  and hidden variables (including parameters)  $\theta$  comprises a product of factors in the form

$$
p(\mathcal{D}, \boldsymbol{\theta}) = \prod_i f_i(\boldsymbol{\theta}).
$$
\n(10.188)

This would arise, for example, in a model for independent, identically distributed data in which there is one factor  $f_n(\theta) = p(\mathbf{x}_n|\theta)$  for each data point  $\mathbf{x}_n$ , along with a factor  $f_0(\theta) = p(\theta)$  corresponding to the prior. More generally, it would also apply to any model defined by a directed probabilistic graph in which each factor is a conditional distribution corresponding to one of the nodes, or an undirected graph in which each factor is a clique potential. We are interested in evaluating the posterior distribution  $p(\theta|\mathcal{D})$  for the purpose of making predictions, as well as the model evidence  $p(\mathcal{D})$  for the purpose of model comparison. From (10.188) the posterior is given by

$$
p(\theta|\mathcal{D}) = \frac{1}{p(\mathcal{D})} \prod_{i} f_i(\theta)
$$
 (10.189)

and the model evidence is given by

$$
p(\mathcal{D}) = \int \prod_i f_i(\boldsymbol{\theta}) \, d\boldsymbol{\theta}.
$$
 (10.190)

Here we are considering continuous variables, but the following discussion applies equally to discrete variables with integrals replaced by summations. We shall suppose that the marginalization over  $\theta$ , along with the marginalizations with respect to the posterior distribution required to make predictions, are intractable so that some form of approximation is required.

Expectation propagation is based on an approximation to the posterior distribution which is also given by a product of factors

$$
q(\boldsymbol{\theta}) = \frac{1}{Z} \prod_{i} \widetilde{f}_i(\boldsymbol{\theta})
$$
 (10.191)

in which each factor  $f_i(\theta)$  in the approximation corresponds to one of the factors  $f_i(\theta)$  in the true posterior (10.189), and the factor  $1/Z$  is the normalizing constant needed to ensure that the left-hand side of (10.191) integrates to unity. In order to obtain a practical algorithm, we need to constrain the factors  $f_i(\theta)$  in some way, and in particular we shall assume that they come from the exponential family. The product of the factors will therefore also be from the exponential family and so can

be described by a finite set of sufficient statistics. For example, if each of the  $f_i(\theta)$ is a Gaussian, then the overall approximation  $q(\theta)$  will also be Gaussian.

Ideally we would like to determine the  $f_i(\theta)$  by minimizing the Kullback-Leibler divergence between the true posterior and the approximation given by

$$
KL(p||q) = KL\left(\frac{1}{p(\mathcal{D})}\prod_{i}f_i(\theta)\middle\| \frac{1}{Z}\prod_{i}\widetilde{f}_i(\theta)\right).
$$
 (10.192)

Note that this is the reverse form of KL divergence compared with that used in variational inference. In general, this minimization will be intractable because the KL divergence involves averaging with respect to the true distribution. As a rough approximation, we could instead minimize the KL divergences between the corresponding pairs  $f_i(\theta)$  and  $f_i(\theta)$  of factors. This represents a much simpler problem to solve, and has the advantage that the algorithm is noniterative. However, because each factor is individually approximated, the product of the factors could well give a poor approximation.

Expectation propagation makes a much better approximation by optimizing each factor in turn in the context of all of the remaining factors. It starts by initializing the factors  $f_i(\theta)$ , and then cycles through the factors refining them one at a time. This is similar in spirit to the update of factors in the variational Bayes framework considered earlier. Suppose we wish to refine factor  $f_j(\theta)$ . We first remove this factor from the product to give  $\prod_{i \neq j} \widetilde{f}_i(\theta)$ . Conceptually, we will now determine a revised form of the factor  $f_j(\boldsymbol{\theta})$  by ensuring that the product

$$
q^{\text{new}}(\boldsymbol{\theta}) \propto \widetilde{f}_j(\boldsymbol{\theta}) \prod_{i \neq j} \widetilde{f}_i(\boldsymbol{\theta})
$$
\n(10.193)

is as close as possible to

$$
f_j(\boldsymbol{\theta}) \prod_{i \neq j} \widetilde{f}_i(\boldsymbol{\theta}) \tag{10.194}
$$

in which we keep fixed all of the factors  $f_i(\theta)$  for  $i \neq j$ . This ensures that the approximation is most accurate in the regions of high posterior probability as defined by the remaining factors. We shall see an example of this effect when we apply EP Section 10.7.1 to the 'clutter problem'. To achieve this, we first remove the factor  $f_j(\theta)$  from the current approximation to the posterior by defining the unnormalized distribution

$$
q^{\setminus j}(\boldsymbol{\theta}) = \frac{q(\boldsymbol{\theta})}{\widetilde{f}_j(\boldsymbol{\theta})}.
$$
 (10.195)

Note that we could instead find  $q^{j}(\theta)$  from the product of factors  $i \neq j$ , although in practice division is usually easier. This is now combined with the factor  $f_j(\theta)$  to give a distribution

$$
\frac{1}{Z_j} f_j(\boldsymbol{\theta}) q^{\backslash j}(\boldsymbol{\theta}) \tag{10.196}
$$

*Section 10.7.1*

Image /page/8/Figure/1 description: The image contains two plots. The left plot shows three bell-shaped curves in red, green, and blue, with a shaded yellow area underneath the green curve. The x-axis ranges from -2 to 4, and the y-axis ranges from 0 to 1. The right plot shows four curves in yellow, green, blue, and red, all starting at different y-values at x=-2 and increasing as x increases. The x-axis ranges from -2 to 4, and the y-axis ranges from 0 to 40. The text below the plots indicates that they illustrate something related to distributions.

**Figure 10.14** Illustration of the expectation propagation approximation using a Gaussian distribution for the example considered earlier in Figures 4.14 and 10.1. The left-hand plot shows the original distribution (yellow) along with the Laplace (red), global variational (green), and EP (blue) approximations, and the right-hand plot shows the corresponding negative logarithms of the distributions. Note that the EP distribution is broader than that variational inference, as a consequence of the different form of KL divergence.

where  $Z_j$  is the normalization constant given by

$$
Z_j = \int f_j(\boldsymbol{\theta}) q^{\setminus j}(\boldsymbol{\theta}) d\boldsymbol{\theta}.
$$
 (10.197)

We now determine a revised factor  $f_j(\theta)$  by minimizing the Kullback-Leibler divergence

$$
KL\left(\frac{f_j(\boldsymbol{\theta})q^{\setminus j}(\boldsymbol{\theta})}{Z_j}\middle\|q^{\text{new}}(\boldsymbol{\theta})\right).
$$
 (10.198)

This is easily solved because the approximating distribution  $q^{\text{new}}(\theta)$  is from the exponential family, and so we can appeal to the result (10.187), which tells us that the parameters of  $q^{new}(\theta)$  are obtained by matching its expected sufficient statistics to the corresponding moments of (10.196). We shall assume that this is a tractable operation. For example, if we choose  $q(\theta)$  to be a Gaussian distribution  $\mathcal{N}(\theta|\mu, \Sigma)$ , then  $\mu$  is set equal to the mean of the (unnormalized) distribution  $f_i(\theta)q^{\setminus j}(\theta)$ , and  $\Sigma$  is set to its covariance. More generally, it is straightforward to obtain the required expectations for any member of the exponential family, provided it can be normalized, because the expected statistics can be related to the derivatives of the normalization coefficient, as given by (2.226). The EP approximation is illustrated in Figure 10.14.

From (10.193), we see that the revised factor  $f_j(\theta)$  can be found by taking  $q^{new}(\boldsymbol{\theta})$  and dividing out the remaining factors so that

$$
\widetilde{f}_j(\boldsymbol{\theta}) = K \frac{q^{\text{new}}(\boldsymbol{\theta})}{q^{\backslash j}(\boldsymbol{\theta})}
$$
\n(10.199)

where we have used (10.195). The coefficient  $K$  is determined by multiplying both

#### **10.7. Expectation Propagation 509**

sides of (10.199) by  $q^{\setminus i}(\theta)$  and integrating to give

$$
K = \int \widetilde{f}_j(\boldsymbol{\theta}) q^{\setminus j}(\boldsymbol{\theta}) \,\mathrm{d}\boldsymbol{\theta} \tag{10.200}
$$

where we have used the fact that  $q^{\text{new}}(\theta)$  is normalized. The value of K can therefore be found by matching zeroth-order moments

$$
\int \widetilde{f}_j(\boldsymbol{\theta}) q^{\setminus j}(\boldsymbol{\theta}) d\boldsymbol{\theta} = \int f_j(\boldsymbol{\theta}) q^{\setminus j}(\boldsymbol{\theta}) d\boldsymbol{\theta}.
$$
 (10.201)

Combining this with (10.197), we then see that  $K = Z_i$  and so can be found by evaluating the integral in (10.197).

In practice, several passes are made through the set of factors, revising each factor in turn. The posterior distribution  $p(\theta|\mathcal{D})$  is then approximated using (10.191), and the model evidence  $p(\mathcal{D})$  can be approximated by using (10.190) with the factors  $f_i(\boldsymbol{\theta})$  replaced by their approximations  $f_i(\boldsymbol{\theta})$ .

## Expectation Propagation

We are given a joint distribution over observed data  $D$  and stochastic variables *θ* in the form of a product of factors

$$
p(\mathcal{D}, \boldsymbol{\theta}) = \prod_i f_i(\boldsymbol{\theta})
$$
 (10.202)

and we wish to approximate the posterior distribution  $p(\theta|\mathcal{D})$  by a distribution of the form

$$
q(\boldsymbol{\theta}) = \frac{1}{Z} \prod_{i} \widetilde{f}_i(\boldsymbol{\theta}).
$$
 (10.203)

We also wish to approximate the model evidence  $p(\mathcal{D})$ .

- 1. Initialize all of the approximating factors  $f_i(\theta)$ .
- 2. Initialize the posterior approximation by setting

$$
q(\boldsymbol{\theta}) \propto \prod_i \widetilde{f}_i(\boldsymbol{\theta}). \tag{10.204}
$$

- 3. Until convergence:
  - (a) Choose a factor  $f_j(\boldsymbol{\theta})$  to refine.
  - (b) Remove  $f_j(\boldsymbol{\theta})$  from the posterior by division

$$
q^{\setminus j}(\boldsymbol{\theta}) = \frac{q(\boldsymbol{\theta})}{\widetilde{f}_j(\boldsymbol{\theta})}.
$$
 (10.205)

(c) Evaluate the new posterior by setting the sufficient statistics (moments) of  $q^{new}(\theta)$  equal to those of  $q^{j}(\theta)f_{i}(\theta)$ , including evaluation of the normalization constant

$$
Z_j = \int q^{\setminus j}(\boldsymbol{\theta}) f_j(\boldsymbol{\theta}) \, d\boldsymbol{\theta}.
$$
 (10.206)

(d) Evaluate and store the new factor

$$
\widetilde{f}_j(\boldsymbol{\theta}) = Z_j \frac{q^{\text{new}}(\boldsymbol{\theta})}{q^{\backslash j}(\boldsymbol{\theta})}.
$$
\n(10.207)

4. Evaluate the approximation to the model evidence

$$
p(\mathcal{D}) \simeq \int \prod_i \widetilde{f}_i(\boldsymbol{\theta}) \, d\boldsymbol{\theta}.
$$
 (10.208)

A special case of EP, known as *assumed density filtering* (ADF) or *moment matching* (Maybeck, 1982; Lauritzen, 1992; Boyen and Koller, 1998; Opper and Winther, 1999), is obtained by initializing all of the approximating factors except the first to unity and then making one pass through the factors updating each of them once. Assumed density filtering can be appropriate for on-line learning in which data points are arriving in a sequence and we need to learn from each data point and then discard it before considering the next point. However, in a batch setting we have the opportunity to re-use the data points many times in order to achieve improved accuracy, and it is this idea that is exploited in expectation propagation. Furthermore, if we apply ADF to batch data, the results will have an undesirable dependence on the (arbitrary) order in which the data points are considered, which again EP can overcome.

One disadvantage of expectation propagation is that there is no guarantee that the iterations will converge. However, for approximations  $q(\theta)$  in the exponential family, if the iterations do converge, the resulting solution will be a stationary point of a particular energy function (Minka, 2001a), although each iteration of EP does not necessarily decrease the value of this energy function. This is in contrast to variational Bayes, which iteratively maximizes a lower bound on the log marginal likelihood, in which each iteration is guaranteed not to decrease the bound. It is possible to optimize the EP cost function directly, in which case it is guaranteed to converge, although the resulting algorithms can be slower and more complex to implement.

Another difference between variational Bayes and EP arises from the form of KL divergence that is minimized by the two algorithms, because the former minimizes KL $(q||p)$  whereas the latter minimizes KL $(p||q)$ . As we saw in Figure 10.3, for distributions  $p(\theta)$  which are multimodal, minimizing KL $(p||q)$  can lead to poor approximations. In particular, if EP is applied to mixtures the results are not sensible because the approximation tries to capture all of the modes of the posterior distribution. Conversely, in logistic-type models, EP often out-performs both local variational methods and the Laplace approximation (Kuss and Rasmussen, 2006).

**Figure 10.15** Illustration of the clutter problem for a data space dimensionality of  $D = 1$ . Training data points, denoted by the crosses, are drawn from a mixture of two Gaussians with components shown in red and green. The goal is to infer the mean of the green Gaussian from the observed data.

Image /page/11/Figure/2 description: The image displays a graph with two probability density functions, one in red and one in green, plotted against the variable \u03b8 (theta) on the x-axis. The x-axis ranges from -5 to 10. The red curve is a wider, flatter distribution centered around 0, while the green curve is a narrower, taller distribution centered around 3. Several black 'x' marks are scattered along the x-axis, indicating data points. Some 'x' marks are clustered between 0 and 5, with a few more scattered to the left of 0 and to the right of 5.

### **10.7.1 Example: The clutter problem**

Following Minka (2001b), we illustrate the EP algorithm using a simple example in which the goal is to infer the mean  $\theta$  of a multivariate Gaussian distribution over a variable **x** given a set of observations drawn from that distribution. To make the problem more interesting, the observations are embedded in background clutter, which itself is also Gaussian distributed, as illustrated in Figure 10.15. The distribution of observed values **x** is therefore a mixture of Gaussians, which we take to be of the form

$$
p(\mathbf{x}|\boldsymbol{\theta}) = (1 - w)\mathcal{N}(\mathbf{x}|\boldsymbol{\theta}, \mathbf{I}) + w\mathcal{N}(\mathbf{x}|\mathbf{0}, a\mathbf{I})
$$
 (10.209)

where  $w$  is the proportion of background clutter and is assumed to be known. The prior over  $\theta$  is taken to be Gaussian

$$
p(\boldsymbol{\theta}) = \mathcal{N}(\boldsymbol{\theta}|\mathbf{0}, b\mathbf{I})
$$
 (10.210)

and Minka (2001a) chooses the parameter values  $a = 10$ ,  $b = 100$  and  $w = 0.5$ . The joint distribution of N observations  $\mathcal{D} = {\mathbf{x}_1, \dots, \mathbf{x}_N}$  and  $\theta$  is given by

$$
p(\mathcal{D}, \boldsymbol{\theta}) = p(\boldsymbol{\theta}) \prod_{n=1}^{N} p(\mathbf{x}_n | \boldsymbol{\theta})
$$
 (10.211)

and so the posterior distribution comprises a mixture of  $2^N$  Gaussians. Thus the computational cost of solving this problem exactly would grow exponentially with the size of the data set, and so an exact solution is intractable for moderately large  $N$ 

To apply EP to the clutter problem, we first identify the factors  $f_0(\theta) = p(\theta)$ and  $f_n(\theta) = p(\mathbf{x}_n|\theta)$ . Next we select an approximating distribution from the exponential family, and for this example it is convenient to choose a spherical Gaussian

$$
q(\boldsymbol{\theta}) = \mathcal{N}(\boldsymbol{\theta}|\mathbf{m}, v\mathbf{I}).
$$
\n(10.212)

The factor approximations will therefore take the form of exponential-quadratic functions of the form

$$
f_n(\boldsymbol{\theta}) = s_n \mathcal{N}(\boldsymbol{\theta} | \mathbf{m}_n, v_n \mathbf{I})
$$
\n(10.213)

where  $n = 1, ..., N$ , and we set  $f_0(\theta)$  equal to the prior  $p(\theta)$ . Note that the use of  $\mathcal{N}(\theta|\cdot,\cdot)$  does not imply that the right-hand side is a well-defined Gaussian density (in fact, as we shall see, the variance parameter  $v_n$  can be negative) but is simply a convenient shorthand notation. The approximations  $f_n(\theta)$ , for  $n = 1, ..., N$ , can be initialized to unity, corresponding to  $s_n = (2\pi v_n)^{D/2}$ ,  $v_n \to \infty$  and  $m_n = 0$ , where D is the dimensionality of **x** and hence of  $\theta$ . The initial  $q(\theta)$ , defined by (10.191), is therefore equal to the prior.

We then iteratively refine the factors by taking one factor  $f_n(\theta)$  at a time and applying (10.205), (10.206), and (10.207). Note that we do not need to revise the *Exercise 10.37* term  $f_0(\theta)$  because an EP update will leave this term unchanged. Here we state the results and leave the reader to fill in the details.

First we remove the current estimate  $f_n(\theta)$  from  $q(\theta)$  by division using (10.205) *Exercise 10.38* (*b*) to give  $q^{n}(\theta)$ , which has mean and inverse variance given by

$$
\mathbf{m}^{\backslash n} = \mathbf{m} + v^{\backslash n} v_n^{-1} (\mathbf{m} - \mathbf{m}_n)
$$
(10.214)

$$
(v^{n})^{-1} = v^{-1} - v_n^{-1}.
$$
 (10.215)

Next we evaluate the normalization constant  $Z_n$  using (10.206) to give

$$
Z_n = (1 - w)\mathcal{N}(\mathbf{x}_n | \mathbf{m}^n, (v^n + 1)\mathbf{I}) + w\mathcal{N}(\mathbf{x}_n | \mathbf{0}, a\mathbf{I}).
$$
 (10.216)

Similarly, we compute the mean and variance of  $q^{new}(\theta)$  by finding the mean and *Exercise 10.39* variance of  $q^{n}(\boldsymbol{\theta})f_n(\boldsymbol{\theta})$  to give

$$
\mathbf{m} = \mathbf{m}^{n} + \rho_n \frac{v^{n}}{v^{n} + 1} (\mathbf{x}_n - \mathbf{m}^{n})
$$
 (10.217)

$$
v = v^{n} - \rho_n \frac{(v^{n})^2}{v^{n} + 1} + \rho_n (1 - \rho_n) \frac{(v^{n})^2 ||\mathbf{x}_n - \mathbf{m}^{n}||^2}{D(v^{n} + 1)^2}
$$
 (10.218)

where the quantity

$$
\rho_n = 1 - \frac{w}{Z_n} \mathcal{N}(\mathbf{x}_n | \mathbf{0}, a\mathbf{I})
$$
\n(10.219)

has a simple interpretation as the probability of the point  $x_n$  not being clutter. Then we use (10.207) to compute the refined factor  $f_n(\theta)$  whose parameters are given by

$$
v_n^{-1} = (v^{new})^{-1} - (v^{n})^{-1}
$$
 (10.220)

$$
\mathbf{m}_n = \mathbf{m}^{\backslash n} + (v_n + v^{\backslash n})(v^{\backslash n})^{-1}(\mathbf{m}^{\text{new}} - \mathbf{m}^{\backslash n}) \qquad (10.221)
$$

$$
s_n = \frac{Z_n}{(2\pi v_n)^{D/2} \mathcal{N}(\mathbf{m}_n | \mathbf{m}^{\backslash n}, (v_n + v^{\backslash n})\mathbf{I})}.
$$
 (10.222)

This refinement process is repeated until a suitable termination criterion is satisfied, for instance that the maximum change in parameter values resulting from a complete

*Exercise 10.37*

*Exercise 10.38*

Image /page/13/Figure/1 description: The image displays two plots side-by-side, each with a horizontal axis labeled "θ" ranging from -5 to 10. Both plots feature three curves: a red curve, a blue curve, and a green curve. The left plot shows a red curve that is a parabola opening upwards, with its minimum near θ=0. A tall, narrow green curve is centered at θ=0. A blue curve starts high on the left, decreases, and then flattens out horizontally around θ=5. The right plot shows a red curve that is a bell-shaped curve peaking around θ=2. A blue curve is also bell-shaped, peaking slightly to the left of the red curve, around θ=1.5. A tall, narrow green curve is centered at θ=2. The y-axis in both plots is not explicitly labeled but appears to represent some form of density or value.

**Figure 10.16** Examples of the approximation of specific factors for a one-dimensional version of the clutter problem, showing  $f_n(\theta)$  in blue,  $\tilde f_n(\theta)$  in red, and  $q^{\setminus n}(\theta)$  in green. Notice that the current form for  $q^{\setminus n}(\theta)$  controls the range of  $\theta$  over which  $f_n(\theta)$  will be a good approximation to  $f_n(\theta).$ 

pass through all factors is less than some threshold. Finally, we use (10.208) to evaluate the approximation to the model evidence, given by

$$
p(\mathcal{D}) \simeq (2\pi v^{\text{new}})^{D/2} \exp(B/2) \prod_{n=1}^{N} \left\{ s_n (2\pi v_n)^{-D/2} \right\}
$$
 (10.223)

where

$$
B = \frac{(\mathbf{m}^{\text{new}})^{\text{T}} \mathbf{m}^{\text{new}}}{v} - \sum_{n=1}^{N} \frac{\mathbf{m}_{n}^{\text{T}} \mathbf{m}_{n}}{v_{n}}.
$$
 (10.224)

Examples factor approximations for the clutter problem with a one-dimensional parameter space  $\theta$  are shown in Figure 10.16. Note that the factor approximations can have infinite or even negative values for the 'variance' parameter  $v_n$ . This simply corresponds to approximations that curve upwards instead of downwards and are not necessarily problematic provided the overall approximate posterior  $q(\theta)$  has positive variance. Figure 10.17 compares the performance of EP with variational Bayes (mean field theory) and the Laplace approximation on the clutter problem.

### **10.7.2 Expectation propagation on graphs**

So far in our general discussion of EP, we have allowed the factors  $f_i(\theta)$  in the distribution  $p(\theta)$  to be functions of all of the components of  $\theta$ , and similarly for the approximating factors  $f(\theta)$  in the approximating distribution  $q(\theta)$ . We now consider situations in which the factors depend only on subsets of the variables. Such restrictions can be conveniently expressed using the framework of probabilistic graphical models, as discussed in Chapter 8. Here we use a factor graph representation because this encompasses both directed and undirected graphs.

Image /page/14/Figure/1 description: The image contains two plots side-by-side, both with the x-axis labeled "FLOPS" and the y-axis labeled "Error". The left plot is titled "Posterior mean" and shows three curves: a blue curve labeled "laplace", a red curve labeled "vb", and a green curve labeled "ep". The y-axis is on a logarithmic scale from 10^-5 to 10^0. The right plot is titled "Evidence" and also shows three curves: a blue curve labeled "laplace", a red curve labeled "vb", and a green curve labeled "ep". The y-axis on this plot is on a logarithmic scale from 10^-204 to 10^-200. Both plots show that the error decreases as FLOPS increase, with the "ep" curve generally reaching the lowest error values.

**Figure 10.17** Comparison of expectation propagation, variational inference, and the Laplace approximation on the clutter problem. The left-hand plot shows the error in the predicted posterior mean versus the number of floating point operations, and the right-hand plot shows the corresponding results for the model evidence.

We shall focus on the case in which the approximating distribution is fully factorized, and we shall show that in this case expectation propagation reduces to loopy belief propagation (Minka, 2001a). To start with, we show this in the context of a simple example, and then we shall explore the general case.

First of all, recall from (10.17) that if we minimize the Kullback-Leibler divergence  $KL(p||q)$  with respect to a factorized distribution q, then the optimal solution for each factor is simply the corresponding marginal of  $p$ .

Now consider the factor graph shown on the left in Figure 10.18, which was *Section 8.4.4* introduced earlier in the context of the sum-product algorithm. The joint distribution is given by

$$
p(\mathbf{x}) = f_a(x_1, x_2) f_b(x_2, x_3) f_c(x_2, x_4).
$$
 (10.225)

We seek an approximation  $q(x)$  that has the same factorization, so that

$$
q(\mathbf{x}) \propto \widetilde{f}_a(x_1, x_2) \widetilde{f}_b(x_2, x_3) \widetilde{f}_c(x_2, x_4).
$$
 (10.226)

Note that normalization constants have been omitted, and these can be re-instated at the end by local normalization, as is generally done in belief propagation. Now suppose we restrict attention to approximations in which the factors themselves factorize with respect to the individual variables so that

$$
q(\mathbf{x}) \propto \widetilde{f}_{a1}(x_1) \widetilde{f}_{a2}(x_2) \widetilde{f}_{b2}(x_2) \widetilde{f}_{b3}(x_3) \widetilde{f}_{c2}(x_2) \widetilde{f}_{c4}(x_4)
$$
\n(10.227)

which corresponds to the factor graph shown on the right in Figure 10.18. Because the individual factors are factorized, the overall distribution  $q(\mathbf{x})$  is itself fully factorized.

Now we apply the EP algorithm using the fully factorized approximation. Suppose that we have initialized all of the factors and that we choose to refine factor

*Section 8.4.4*

**10.7. Expectation Propagation 515**

Image /page/15/Figure/1 description: The image displays two factor graphs, both rendered in red. The left graph has three circular nodes labeled x1, x2, and x3, arranged horizontally. A square node labeled fa connects x1 and x2. Another square node labeled fb connects x2 and x3. A third square node labeled fc connects x2 to a fourth circular node labeled x4, positioned below x2. The right graph also has three circular nodes labeled x1, x2, and x3, arranged horizontally. A square node labeled ˜fa1 connects x1 and x2. Another square node labeled ˜fa2 connects x2 to a point between x1 and x2. A square node labeled ˜fb2 connects x2 and x3. A square node labeled ˜fb3 connects x3 to a point between x2 and x3. A square node labeled ˜fc2 connects x2 to a point below x2. A square node labeled ˜fc4 connects this point to a fourth circular node labeled x4, positioned below x2.

**Figure 10.18** On the left is a simple factor graph from Figure 8.51 and reproduced here for convenience. On the right is the corresponding factorized approximation.

 $f_b(x_2, x_3) = f_{b2}(x_2) f_{b3}(x_3)$ . We first remove this factor from the approximating distribution to give

$$
q^{\setminus b}(\mathbf{x}) = \widetilde{f}_{a1}(x_1)\widetilde{f}_{a2}(x_2)\widetilde{f}_{c2}(x_2)\widetilde{f}_{c4}(x_4)
$$
(10.228)

and we then multiply this by the exact factor  $f_b(x_2, x_3)$  to give

$$
\widehat{p}(\mathbf{x}) = q^{b}(\mathbf{x}) f_b(x_2, x_3) = \widetilde{f}_{a1}(x_1) \widetilde{f}_{a2}(x_2) \widetilde{f}_{c2}(x_2) \widetilde{f}_{c4}(x_4) f_b(x_2, x_3).
$$
 (10.229)

We now find  $q^{new}(\mathbf{x})$  by minimizing the Kullback-Leibler divergence  $KL(\hat{p}||q^{new})$ . The result, as noted above, is that  $q^{new}(\mathbf{z})$  comprises the product of factors, one for each variable  $x_i$ , in which each factor is given by the corresponding marginal of  $\widehat{p}(\mathbf{x})$ . These four marginals are given by

$$
\widehat{p}(x_1) \propto \widetilde{f}_{a1}(x_1) \tag{10.230}
$$

$$
\widehat{p}(x_2) \propto \widetilde{f}_{a2}(x_2)\widetilde{f}_{c2}(x_2)\sum_{x_3} f_b(x_2,x_3) \hspace{1cm} (10.231)
$$

$$
\widehat{p}(x_3) \propto \sum_{x_2} \left\{ f_b(x_2, x_3) \widetilde{f}_{a2}(x_2) \widetilde{f}_{c2}(x_2) \right\} \quad (10.232)
$$

$$
\widehat{p}(x_4) \propto \widetilde{f}_{c4}(x_4) \tag{10.233}
$$

and  $q^{new}(\mathbf{x})$  is obtained by multiplying these marginals together. We see that the only factors in  $q(\mathbf{x})$  that change when we update  $f_b(x_2, x_3)$  are those that involve<br>the variables in formally  $x_0$  and  $x_1$ . To aktive the upfined fector  $\tilde{f}(x_1, x_2)$ . the variables in  $f_b$  namely  $x_2$  and  $x_3$ . To obtain the refined factor  $f_b(x_2, x_3) =$  $\widetilde{f}_{b2}(x_2)\widetilde{f}_{b3}(x_3)$  we simply divide  $q^{\text{new}}(\mathbf{x})$  by  $q^{\setminus b}(\mathbf{x})$ , which gives

$$
\widetilde{f}_{b2}(x_2) \propto \sum_{x_3} f_b(x_2, x_3) \tag{10.234}
$$

$$
\widetilde{f}_{b3}(x_3) \propto \sum_{x_2} \left\{ f_b(x_2, x_3) \widetilde{f}_{a2}(x_2) \widetilde{f}_{c2}(x_2) \right\}.
$$
 (10.235)

*Section 8.4.4* These are precisely the messages obtained using belief propagation in which messages from variable nodes to factor nodes have been folded into the messages from factor nodes to variable nodes. In particular,  $f_{b2}(x_2)$  corresponds to the message  $\mu_{f_b\to x_2}(x_2)$  sent by factor node  $f_b$  to variable node  $x_2$  and is given by (8.81). Similarly, if we substitute (8.78) into (8.79), we obtain (10.235) in which  $f_{a2}(x_2)$  corresponds to  $\mu_{f_a \to x_2}(x_2)$  and  $f_{c2}(x_2)$  corresponds to  $\mu_{f_c \to x_2}(x_2)$ , giving the message  $f_{b3}(x_3)$  which corresponds to  $\mu_{f_b \to x_3}(x_3)$ .

This result differs slightly from standard belief propagation in that messages are passed in both directions at the same time. We can easily modify the EP procedure to give the standard form of the sum-product algorithm by updating just one of the factors at a time, for instance if we refine only  $f_{b3}(x_3)$ , then  $f_{b2}(x_2)$  is unchanged by definition, while the refined version of  $f_{b3}(x_3)$  is again given by (10.235). If we are refining only one term at a time, then we can choose the order in which the refinements are done as we wish. In particular, for a tree-structured graph we can follow a two-pass update scheme, corresponding to the standard belief propagation schedule, which will result in exact inference of the variable and factor marginals. The initialization of the approximation factors in this case is unimportant.

Now let us consider a general factor graph corresponding to the distribution

$$
p(\boldsymbol{\theta}) = \prod_i f_i(\boldsymbol{\theta}_i)
$$
 (10.236)

where  $\theta_i$  represents the subset of variables associated with factor  $f_i$ . We approximate this using a fully factorized distribution of the form

$$
q(\boldsymbol{\theta}) \propto \prod_{i} \prod_{k} \widetilde{f}_{ik}(\theta_k)
$$
 (10.237)

where  $\theta_k$  corresponds to an individual variable node. Suppose that we wish to refine the particular term  $f_{jl}(\theta_l)$  keeping all other terms fixed. We first remove the term  $f_j(\theta_j)$  from  $q(\theta)$  to give

$$
q^{\setminus j}(\boldsymbol{\theta}) \propto \prod_{i \neq j} \prod_k \widetilde{f}_{ik}(\theta_k)
$$
 (10.238)

and then multiply by the exact factor  $f_j(\theta_j)$ . To determine the refined term  $f_{jl}(\theta_l)$ , we need only consider the functional dependence on  $\theta_l$ , and so we simply find the corresponding marginal of

$$
q^{\setminus j}(\boldsymbol{\theta})f_j(\boldsymbol{\theta}_j). \tag{10.239}
$$

Up to a multiplicative constant, this involves taking the marginal of  $f_i(\theta_i)$  multiplied by any terms from  $q^{j}(\theta)$  that are functions of any of the variables in  $\theta_j$ . Terms that correspond to other factors  $f_i(\theta_i)$  for  $i \neq j$  will cancel between numerator and denominator when we subsequently divide by  $q^{\backslash j}(\theta)$ . We therefore obtain

$$
\widetilde{f}_{jl}(\theta_l) \propto \sum_{\theta_{m \neq l} \in \Theta_j} f_j(\theta_j) \prod_k \prod_{m \neq l} \widetilde{f}_{km}(\theta_m). \tag{10.240}
$$

We recognize this as the sum-product rule in the form in which messages from variable nodes to factor nodes have been eliminated, as illustrated by the example shown in Figure 8.50. The quantity  $f_{jm}(\theta_m)$  corresponds to the message  $\mu_{f_j \to \theta_m}(\theta_m)$ , which factor node j sends to variable node m, and the product over  $k$  in (10.240) is over all factors that depend on the variables  $\theta_m$  that have variables (other than variable  $\theta_l$ ) in common with factor  $f_j(\theta_j)$ . In other words, to compute the outgoing message from a factor node, we take the product of all the incoming messages from other factor nodes, multiply by the local factor, and then marginalize.

Thus, the sum-product algorithm arises as a special case of expectation propagation if we use an approximating distribution that is fully factorized. This suggests that more flexible approximating distributions, corresponding to partially disconnected graphs, could be used to achieve higher accuracy. Another generalization is to group factors  $f_i(\theta_i)$  together into sets and to refine all the factors in a set together at each iteration. Both of these approaches can lead to improvements in accuracy (Minka, 2001b). In general, the problem of choosing the best combination of grouping and disconnection is an open research issue.

We have seen that variational message passing and expectation propagation optimize two different forms of the Kullback-Leibler divergence. Minka (2005) has shown that a broad range of message passing algorithms can be derived from a common framework involving minimization of members of the alpha family of divergences, given by (10.19). These include variational message passing, loopy belief propagation, and expectation propagation, as well as a range of other algorithms, which we do not have space to discuss here, such as *tree-reweighted message passing* (Wainwright *et al.*, 2005), *fractional belief propagation* (Wiegerinck and Heskes, 2003), and *power EP* (Minka, 2004).

# **Exercises**

- **10.1** ( $\star$ ) **www** Verify that the log marginal distribution of the observed data  $\ln p(X)$ can be decomposed into two terms in the form (10.2) where  $\mathcal{L}(q)$  is given by (10.3) and  $KL(q||p)$  is given by (10.4).
- **10.2** ( $\star$ ) Use the properties  $\mathbb{E}[z_1] = m_1$  and  $\mathbb{E}[z_2] = m_2$  to solve the simultaneous equations (10.13) and (10.15), and hence show that, provided the original distribution  $p(\mathbf{z})$  is nonsingular, the unique solution for the means of the factors in the approximation distribution is given by  $\mathbb{E}[z_1] = \mu_1$  and  $\mathbb{E}[z_2] = \mu_2$ .
- **10.3**  $(\star \star)$  **www** Consider a factorized variational distribution  $q(\mathbf{Z})$  of the form (10.5). By using the technique of Lagrange multipliers, verify that minimization of the Kullback-Leibler divergence  $KL(p||q)$  with respect to one of the factors  $q_i(\mathbf{Z}_i)$ , keeping all other factors fixed, leads to the solution (10.17).
- **10.4**  $(\star \star)$  Suppose that  $p(x)$  is some fixed distribution and that we wish to approximate it using a Gaussian distribution  $q(\mathbf{x}) = \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma})$ . By writing down the form of the KL divergence  $KL(p||q)$  for a Gaussian  $q(x)$  and then differentiating, show that

minimization of  $KL(p||q)$  with respect to  $\mu$  and  $\Sigma$  leads to the result that  $\mu$  is given by the expectation of **x** under  $p(x)$  and that  $\Sigma$  is given by the covariance.

- **10.5**  $(\star \star)$  **www** Consider a model in which the set of all hidden stochastic variables, denoted collectively by **Z**, comprises some latent variables **z** together with some model parameters  $\theta$ . Suppose we use a variational distribution that factorizes between latent variables and parameters so that  $q(\mathbf{z}, \theta) = q_{\mathbf{z}}(\mathbf{z})q_{\theta}(\theta)$ , in which the distribution  $q_{\theta}(\theta)$  is approximated by a point estimate of the form  $q_{\theta}(\theta) = \delta(\theta - \theta_0)$  where  $\theta_0$ is a vector of free parameters. Show that variational optimization of this factorized distribution is equivalent to an EM algorithm, in which the E step optimizes  $q_{\mathbf{z}}(\mathbf{z})$ , and the M step maximizes the expected complete-data log posterior distribution of *θ* with respect to  $\theta_0$ .
- **10.6**  $(\star \star)$  The alpha family of divergences is defined by (10.19). Show that the Kullback-Leibler divergence  $KL(p||q)$  corresponds to  $\alpha \rightarrow 1$ . This can be done by writing  $p^{\epsilon} = \exp(\epsilon \ln p) = 1 + \epsilon \ln p + O(\epsilon^2)$  and then taking  $\epsilon \to 0$ . Similarly show that KL(q $||p|$ ) corresponds to  $\alpha \rightarrow -1$ .
- **10.7**  $(\star \star)$  Consider the problem of inferring the mean and precision of a univariate Gaussian using a factorized variational approximation, as considered in Section 10.1.3. Show that the factor  $q_{\mu}(\mu)$  is a Gaussian of the form  $\mathcal{N}(\mu|\mu_N, \lambda_N^{-1})$  with mean and precision given by (10.26) and (10.27), respectively. Similarly show that the factor  $q_{\tau}(\tau)$  is a gamma distribution of the form  $Gam(\tau | a_N, b_N)$  with parameters given by (10.29) and (10.30).
- **10.8**  $(\star)$  Consider the variational posterior distribution for the precision of a univariate Gaussian whose parameters are given by (10.29) and (10.30). By using the standard results for the mean and variance of the gamma distribution given by (B.27) and (B.28), show that if we let  $N \to \infty$ , this variational posterior distribution has a mean given by the inverse of the maximum likelihood estimator for the variance of the data, and a variance that goes to zero.
- **10.9** ( $\star\star$ ) By making use of the standard result  $\mathbb{E}[\tau] = a_N / b_N$  for the mean of a gamma distribution, together with (10.26), (10.27), (10.29), and (10.30), derive the result (10.33) for the reciprocal of the expected precision in the factorized variational treatment of a univariate Gaussian.
- **10.10** ( $\star$ ) **www** Derive the decomposition given by (10.34) that is used to find approximate posterior distributions over models using variational inference.
- **10.11**  $(\star \star)$  **www** By using a Lagrange multiplier to enforce the normalization constraint on the distribution  $q(m)$ , show that the maximum of the lower bound (10.35) is given by (10.36).
- **10.12**  $(\star \star)$  Starting from the joint distribution (10.41), and applying the general result (10.9), show that the optimal variational distribution  $q^{\star}(\mathbf{Z})$  over the latent variables for the Bayesian mixture of Gaussians is given by (10.48) by verifying the steps given in the text.

- **10.13**  $(\star \star)$  **www** Starting from (10.54), derive the result (10.59) for the optimum variational posterior distribution over  $\mu_k$  and  $\Lambda_k$  in the Bayesian mixture of Gaussians, and hence verify the expressions for the parameters of this distribution given by  $(10.60)$ – $(10.63)$ .
- **10.14**  $(\star \star)$  Using the distribution (10.59), verify the result (10.64).
- **10.15**  $(\star)$  Using the result (B.17), show that the expected value of the mixing coefficients in the variational mixture of Gaussians is given by (10.69).
- **10.16**  $(\star \star)$  **www** Verify the results (10.71) and (10.72) for the first two terms in the lower bound for the variational Gaussian mixture model given by (10.70).
- **10.17**  $(\star \star \star)$  Verify the results (10.73)–(10.77) for the remaining terms in the lower bound for the variational Gaussian mixture model given by (10.70).
- **10.18**  $(\star \star \star)$  In this exercise, we shall derive the variational re-estimation equations for the Gaussian mixture model by direct differentiation of the lower bound. To do this we assume that the variational distribution has the factorization defined by (10.42) and (10.55) with factors given by (10.48), (10.57), and (10.59). Substitute these into (10.70) and hence obtain the lower bound as a function of the parameters of the variational distribution. Then, by maximizing the bound with respect to these parameters, derive the re-estimation equations for the factors in the variational distribution, and show that these are the same as those obtained in Section 10.2.1.
- **10.19**  $(\star \star)$  Derive the result (10.81) for the predictive distribution in the variational treatment of the Bayesian mixture of Gaussians model.
- **10.20**  $(\star \star)$  **www** This exercise explores the variational Bayes solution for the mixture of Gaussians model when the size  $N$  of the data set is large and shows that it reduces (as we would expect) to the maximum likelihood solution based on EM derived in Chapter 9. Note that results from Appendix B may be used to help answer this exercise. First show that the posterior distribution  $q^{\star}(\Lambda_k)$  of the precisions becomes sharply peaked around the maximum likelihood solution. Do the same for the posterior distribution of the means  $q^{\star}(\mu_k|\Lambda_k)$ . Next consider the posterior distribution  $q^{\star}(\pi)$ for the mixing coefficients and show that this too becomes sharply peaked around the maximum likelihood solution. Similarly, show that the responsibilities become equal to the corresponding maximum likelihood values for large  $N$ , by making use of the following asymptotic result for the digamma function for large  $x$

$$
\psi(x) = \ln x + O(1/x). \tag{10.241}
$$

Finally, by making use of  $(10.80)$ , show that for large N, the predictive distribution becomes a mixture of Gaussians.

**10.21**  $(\star)$  Show that the number of equivalent parameter settings due to interchange symmetries in a mixture model with  $K$  components is  $K!$ .

- **10.22**  $(\star \star)$  We have seen that each mode of the posterior distribution in a Gaussian mixture model is a member of a family of  $K!$  equivalent modes. Suppose that the result of running the variational inference algorithm is an approximate posterior distribution  $q$  that is localized in the neighbourhood of one of the modes. We can then approximate the full posterior distribution as a mixture of  $K!$  such q distributions, once centred on each mode and having equal mixing coefficients. Show that if we assume negligible overlap between the components of the  $q$  mixture, the resulting lower bound differs from that for a single component q distribution through the addition of an extra term  $\ln K!$ .
- **10.23**  $(\star \star)$  **www** Consider a variational Gaussian mixture model in which there is no prior distribution over mixing coefficients  $\{\pi_k\}$ . Instead, the mixing coefficients are treated as parameters, whose values are to be found by maximizing the variational lower bound on the log marginal likelihood. Show that maximizing this lower bound with respect to the mixing coefficients, using a Lagrange multiplier to enforce the constraint that the mixing coefficients sum to one, leads to the re-estimation result (10.83). Note that there is no need to consider all of the terms in the lower bound but only the dependence of the bound on the  $\{\pi_k\}$ .
- **10.24**  $(\star \star)$  **www** We have seen in Section 10.2 that the singularities arising in the maximum likelihood treatment of Gaussian mixture models do not arise in a Bayesian treatment. Discuss whether such singularities would arise if the Bayesian model were solved using maximum posterior (MAP) estimation.
- **10.25**  $(\star \star)$  The variational treatment of the Bayesian mixture of Gaussians, discussed in Section 10.2, made use of a factorized approximation (10.5) to the posterior distribution. As we saw in Figure 10.2, the factorized assumption causes the variance of the posterior distribution to be under-estimated for certain directions in parameter space. Discuss qualitatively the effect this will have on the variational approximation to the model evidence, and how this effect will vary with the number of components in the mixture. Hence explain whether the variational Gaussian mixture will tend to under-estimate or over-estimate the optimal number of components.
- **10.26**  $(\star \star \star)$  Extend the variational treatment of Bayesian linear regression to include a gamma hyperprior  $Gam(\beta|c_0, d_0)$  over  $\beta$  and solve variationally, by assuming a factorized variational distribution of the form  $q(\mathbf{w})q(\alpha)q(\beta)$ . Derive the variational update equations for the three factors in the variational distribution and also obtain an expression for the lower bound and for the predictive distribution.
- **10.27**  $(\star \star)$  By making use of the formulae given in Appendix B show that the variational lower bound for the linear basis function regression model, defined by (10.107), can be written in the form (10.107) with the various terms defined by (10.108)–(10.112).
- **10.28**  $(\star \star \star)$  Rewrite the model for the Bayesian mixture of Gaussians, introduced in Section 10.2, as a conjugate model from the exponential family, as discussed in Section 10.4. Hence use the general results (10.115) and (10.119) to derive the specific results (10.48), (10.57), and (10.59).

- **10.29** ( $\star$ ) **www** Show that the function  $f(x) = \ln(x)$  is concave for  $0 < x < \infty$ by computing its second derivative. Determine the form of the dual function  $g(\lambda)$ defined by (10.133), and verify that minimization of  $\lambda x - g(\lambda)$  with respect to  $\lambda$ according to (10.132) indeed recovers the function  $\ln(x)$ .
- **10.30** ( $\star$ ) By evaluating the second derivative, show that the log logistic function  $f(x)$  =  $-\ln(1 + e^{-x})$  is concave. Derive the variational upper bound (10.137) directly by making a second order Taylor expansion of the log logistic function around a point  $x = \xi$ .
- **10.31**  $(\star \star)$  By finding the second derivative with respect to x, show that the function  $f(x) = -\ln(e^{x/2} + e^{-x/2})$  is a concave function of x. Now consider the second derivatives with respect to the variable  $x^2$  and hence show that it is a convex function of  $x^2$ . Plot graphs of  $f(x)$  against x and against  $x^2$ . Derive the lower bound (10.144) on the logistic sigmoid function directly by making a first order Taylor series expansion of the function  $f(x)$  in the variable  $x^2$  centred on the value  $\xi^2$ .
- **10.32**  $(\star \star)$  **www** Consider the variational treatment of logistic regression with sequential learning in which data points are arriving one at a time and each must be processed and discarded before the next data point arrives. Show that a Gaussian approximation to the posterior distribution can be maintained through the use of the lower bound (10.151), in which the distribution is initialized using the prior, and as each data point is absorbed its corresponding variational parameter  $\xi_n$  is optimized.
- **10.33** ( $\star$ ) By differentiating the quantity  $Q(\xi, \xi^{\text{old}})$  defined by (10.161) with respect to the variational parameter  $\xi_n$  show that the update equation for  $\xi_n$  for the Bayesian logistic regression model is given by (10.163).
- **10.34**  $(\star \star)$  In this exercise we derive re-estimation equations for the variational parameters *ξ* in the Bayesian logistic regression model of Section 4.5 by direct maximization of the lower bound given by (10.164). To do this set the derivative of  $\mathcal{L}(\boldsymbol{\xi})$  with respect to  $\xi_n$  equal to zero, making use of the result (3.117) for the derivative of the log of a determinant, together with the expressions (10.157) and (10.158) which define the mean and covariance of the variational posterior distribution  $q(\mathbf{w})$ .
- **10.35**  $(\star \star)$  Derive the result (10.164) for the lower bound  $\mathcal{L}(\boldsymbol{\xi})$  in the variational logistic regression model. This is most easily done by substituting the expressions for the Gaussian prior  $q(\mathbf{w}) = \mathcal{N}(\mathbf{w}|\mathbf{m}_0, \mathbf{S}_0)$ , together with the lower bound  $h(\mathbf{w}, \boldsymbol{\xi})$  on the likelihood function, into the integral (10.159) which defines  $\mathcal{L}(\xi)$ . Next gather together the terms which depend on **w** in the exponential and complete the square to give a Gaussian integral, which can then be evaluated by invoking the standard result for the normalization coefficient of a multivariate Gaussian. Finally take the logarithm to obtain (10.164).
- **10.36**  $(\star \star)$  Consider the ADF approximation scheme discussed in Section 10.7, and show that inclusion of the factor  $f_i(\theta)$  leads to an update of the model evidence of the form

$$
p_j(\mathcal{D}) \simeq p_{j-1}(\mathcal{D})Z_j \tag{10.242}
$$

where  $Z_j$  is the normalization constant defined by (10.197). By applying this result recursively, and initializing with  $p_0(\mathcal{D})=1$ , derive the result

$$
p(\mathcal{D}) \simeq \prod_j Z_j. \tag{10.243}
$$

- **10.37** ( $\star$ ) **www** Consider the expectation propagation algorithm from Section 10.7, and suppose that one of the factors  $f_0(\theta)$  in the definition (10.188) has the same exponential family functional form as the approximating distribution  $q(\theta)$ . Show that if the factor  $f_0(\theta)$  is initialized to be  $f_0(\theta)$ , then an EP update to refine  $f_0(\theta)$  leaves  $f_0(\theta)$  unchanged. This situation typically arises when one of the factors is the prior  $p(\theta)$ , and so we see that the prior factor can be incorporated once exactly and does not need to be refined.
- **10.38**  $(\star \star \star)$  In this exercise and the next, we shall verify the results (10.214)–(10.224) for the expectation propagation algorithm applied to the clutter problem. Begin by using the division formula (10.205) to derive the expressions (10.214) and (10.215) by completing the square inside the exponential to identify the mean and variance. Also, show that the normalization constant  $Z_n$ , defined by (10.206), is given for the clutter problem by (10.216). This can be done by making use of the general result (2.115).
- **10.39**  $(\star \star \star)$  Show that the mean and variance of  $q^{\text{new}}(\theta)$  for EP applied to the clutter problem are given by (10.217) and (10.218). To do this, first prove the following results for the expectations of  $\theta$  and  $\theta\theta^{\text{T}}$  under  $q^{\text{new}}(\theta)$

$$
\mathbb{E}[\theta] = \mathbf{m}^{n} + v^{n} \nabla_{\mathbf{m}^{n}} \ln Z_{n}
$$
\n(10.244)\n(10.244)

$$
\mathbb{E}[\boldsymbol{\theta}^{\mathrm{T}}\boldsymbol{\theta}] = 2(v^{n})^2 \nabla_{v^{n}} \ln Z_n + 2 \mathbb{E}[\boldsymbol{\theta}]^{\mathrm{T}} \mathbf{m}^{n} - ||\mathbf{m}^{n}||^2 \quad (10.245)
$$

and then make use of the result (10.216) for  $Z_n$ . Next, prove the results (10.220)– (10.222) by using (10.207) and completing the square in the exponential. Finally, use (10.208) to derive the result (10.223).
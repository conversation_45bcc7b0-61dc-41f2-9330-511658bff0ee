# Less is More: Efficient Time Series Dataset Condensation via Two-fold Modal Matching–Extended Version

Hao Miao Aalborg University, Denmark <EMAIL>

<PERSON><PERSON><PERSON> China Normal University, China  $\boxtimes$ {cjguo,byang}@dase.ecnu.edu.cn

<PERSON><PERSON><PERSON><PERSON> Liu University of Electronic Science and Technology of China, China liuzi<PERSON>@std.uestc.edu.cn

Kai <PERSON> University of Electronic Science and Technology of China, China <EMAIL>

Yan Zhao Aalborg University, Denmark  $\boxtimes$ <EMAIL>

Christian S. Jensen Aalborg University, Denmark <EMAIL>

<span id="page-0-0"></span>Image /page/0/Figure/7 description: The image depicts a flowchart illustrating a process involving time series data. On the left, labeled 'OLTS', there are three purple boxes, with the middle one containing the symbol 'T'. To the right of these boxes, under the heading 'M time series', a grid of nine green-bordered boxes displays multiple time series graphs. An arrow labeled 'M >> N' points from this grid to a smaller grid of three green-bordered boxes, each containing a time series graph, under the heading 'N time series'. Below the 'M time series' grid, the text 'Dataset Condensation' is written, with an arrow pointing from the 'T' box to the 'N time series' grid. To the right of the 'N time series' grid, there are three peach-colored boxes labeled 'CTS', with the middle one containing the symbol 'S'. Arrows labeled 'Train' point from the 'CTS' boxes to two peach-colored boxes labeled 'TSO perators'. A dashed blue arrow connects these two 'TSO perators' boxes with the text 'Comparable?' written above it.

Figure 1: Time Series Condensation

## 1 INTRODUCTION

With the proliferation of edge computing and mobile sensing, massive volumes of time series data, comprising millions of observations, are being collected and stored into time series database systems (TSMSs) [\[18,](#page-12-0) [21,](#page-12-1) [36\]](#page-12-2), enabling various real-world applications [\[44,](#page-12-3) [46\]](#page-12-4). We are seeing impressive advances in machine learning [\[24,](#page-12-5) [51\]](#page-12-6), especially in deep learning [\[10,](#page-12-7) [44,](#page-12-3) [54\]](#page-12-8), that are successful at extracting information and creating value from large time series datasets. However, the proposed methods are also resourceintensive. Thus, storing and preprocessing datasets is costly, and model training often calls for the use of specialized equipment and infrastructure [\[8,](#page-12-9) [22,](#page-12-10) [47,](#page-12-11) [48\]](#page-12-12), limiting the application on edge devices [\[36,](#page-12-2) [45\]](#page-12-13).

An effective way to reduce costs associated with the use of large data is coreset construction [\[1,](#page-12-14) [7,](#page-12-15) [17,](#page-12-16) [22\]](#page-12-10), which often involves clustering [\[15\]](#page-12-17), Gaussian mixture models [\[25\]](#page-12-18), and streaming learning [\[22\]](#page-12-10). Coreset construction methods [\[15,](#page-12-17) [25\]](#page-12-18) often define heuristic criteria to select the most representative subsets from full datasets to form small coresets, such that models trained on the coresets are competitive with those built on the full datasets. Unfortunately, heuristic coreset construction methods guarantee neither optimal solutions nor promise the presence of representative observations in relation to downstream tasks [\[48\]](#page-12-12). Given these limitations, a recent alternative approach, dataset condensation [\[49\]](#page-12-19) aims to directly synthesize small condensed, optimized datasets, not relying on representative subset-selection.

We consider the novel problem of time series dataset condensation, where the goal is to synthesize a small but informative condensed time series dataset  $S$  derived from an original large dataset

The expanding instrumentation of processes throughout society with sensors yields a proliferation of time series data that may in turn enable important applications, e.g., related to transportation infrastructures or power grids. Machine-learning based methods are increasingly being used to extract value from such data. We provide means of reducing the resulting considerable computational and data storage costs. We achieve this by providing means of condensing large time series datasets such that models trained on the condensed data achieve performance comparable to those trained on the original, large data. Specifically, we propose a time series dataset condensation framework, TimeDC, that employs two-fold modal matching, encompassing frequency matching and training trajectory matching. Thus, TimeDC performs time series feature extraction and decomposition-driven frequency matching to preserve complex temporal dependencies in the reduced time series. Further, TimeDC employs curriculum training trajectory matching to ensure effective and generalized time series dataset condensation. To avoid memory overflow and to reduce the cost of dataset condensation, the framework includes an expert buffer storing pre-computed expert trajectories. Extensive experiments on real data offer insight into the effectiveness and efficiency of the proposed solutions.

### PVLDB Reference Format:

Hao Miao, Ziqiao Liu, Yan Zhao, Chenjuan Guo, Bin Yang, Kai Zheng, and Christian S. Jensen. Less is More: Efficient Time Series Dataset Condensation via Two-fold Modal Matching–Extended Version. PVLDB, 18(2): XXX-XXX, 2024. [doi:XX.XX/XXX.XX](https://doi.org/XX.XX/XXX.XX)

### PVLDB Artifact Availability:

The source code, data, and/or other artifacts have been made available at [https://github.com/uestc-liuzq/STdistillation.](https://github.com/uestc-liuzq/STdistillation)

Yan Zhao and Chenjuan Guo are corresponding authors.

This work is licensed under the Creative Commons BY-NC-ND 4.0 International License. Visit<https://creativecommons.org/licenses/by-nc-nd/4.0/> to view a copy of this license. For any use beyond those covered by this license, obtain permission by emailing [<EMAIL>.](mailto:<EMAIL>) Copyright is held by the owner/author(s). Publication rights licensed to the VLDB Endowment.

Proceedings of the VLDB Endowment, Vol. 18, No. 2 ISSN 2150-8097. [doi:XX.XX/XXX.XX](https://doi.org/XX.XX/XXX.XX)

T. A model, composed of stacked time series operators (TSOperators) that perform feature extraction, is trained on the condensed time series (CTS) dataset with the objective of achieving performance comparable to that of a model trained on the original large time series (OLTS) dataset for downstream tasks (see Figure [1\)](#page-0-0). The stacked TSOperators are multiple TSOperators arranged sequentially so that the output of one TSOperator is fed into the next TSOperator. Stacked TSOperators enable more complex feature extraction. Time series are usually stored in TSMSs over edge devices [\[18,](#page-12-0) [45\]](#page-12-13). In the setting of continuously produced time series, time series dataset condensation aims to alleviate the storage burden of TSMSs considering the limited processing capability of edge devices, which further contributes to the database community [\[2,](#page-12-20) [3\]](#page-12-21). Additionally, due to the small-scaled CTS, time series dataset condensation is expected to bring significant efficiency improvements in repeated training scenarios in TSMSs, e.g., streaming learning [\[27\]](#page-12-22) and model selection [\[42\]](#page-12-23). Although substantial research has been devoted to inventing effective dataset condensation methods [\[6,](#page-12-24) [48,](#page-12-12) [49\]](#page-12-19), such as gradient matching [\[48\]](#page-12-12), distribution matching [\[49\]](#page-12-19), and multistep parameters matching [\[6\]](#page-12-24), existing methods target image data and cannot be applied to time series data directly as they are not built to contend with the unique temporal dependencies present in time series [\[26,](#page-12-25) [54\]](#page-12-8). These methods thus fail to capture inherent temporal dependencies such as trends and seasonalities [\[40\]](#page-12-26). New methods are needed to enable time series condensation. However, developing such methods is non-trivial, due to the following challenges.

Challenge I: Effectiveness and Generalization Ability. It is challenging to guarantee the effectiveness and generalization ability of the condensed time series dataset [\[2,](#page-12-20) [6\]](#page-12-24). First, typical dataset condensation methods require a bi-level (even triple-level) optimization to jointly learn a minimum of two objectives: model parameters and a condensed time series dataset. Such complex non-convex optimization cannot guarantee optimal solutions, thus significantly limiting its effectiveness as the representative of the large original time series dataset. Second, the condensed time series datasets should be generalized to train different networks. Nonetheless, the condensed data may suffer from various types of overfitting [\[6\]](#page-12-24), e.g., overfitting to a certain network architecture. Also, the downstream models might overfit the condensed data during training.

Challenge II: Complex Temporal Dependencies. Existing popular dataset condensation methods mainly focus on computer vision without specific modules to capture the unique time series characteristics [\[4,](#page-12-27) [44\]](#page-12-3). Existing dataset condensation methods are illequipped for modeling the complex temporal dependencies of time series. For example, traffic conditions during afternoon rush hour may be similar on consecutive workdays. Moreover, morning rush hours may gradually start later when winter arrives as people get up later and later due to the gradual decrease in temperature and later sunrise. It is important to learn complex temporal dependencies in a time series dataset so that a condensed version of the dataset exhibits similar temporal patterns to the original dataset. In addition, time series datasets are often multivariate, encompassing correlated variables, or features (channels). Such features are often coupled to perform feature extraction simultaneously. We argue that analysis in independent channels enables more effective feature extraction. For example, traffic flow is negatively correlated with

vehicle speed, but positively correlated with road occupancy [\[38\]](#page-12-28). Coupling features with varying and complex interactions might confuse a model, leading to decreased model performance [\[38\]](#page-12-28).

Challenge III: Scalability. Existing dataset condensation methods often suffer from poor scalability, as bi-level optimization is generally time-consuming [\[55\]](#page-12-29). For example, gradient-matching methods [\[48\]](#page-12-12) set different hyper-parameters of the outer and the inner loop optimization for different learning settings, which requires extensive cross-validation, rendering condensed dataset synthesis costly. In addition, these methods may incur memory overflow: they require an entire model to reside in memory during training, which is infeasible in many resource-constrained environments [\[33\]](#page-12-30). It is highly desirable, but also non-trivial, to develop an efficient time series dataset condensation method.

This study addresses the above challenges by providing an efficient time series dataset condensation (TimeDC) framework, which features a two-fold matching mechanism: time series frequency matching and training trajectory matching. TimeDC synthesizes a small time series dataset that summarizes a large dataset such that models trained on the small dataset achieve comparable performance to models trained on the large dataset. It encompasses three major modules: a time series feature extraction module, a decomposition-driven frequency matching module, and a curriculum training trajectory matching module. Time series of stock prices are among the most common time series in real-world applications. We thus use such time series in a running example to to explain and convey the intuition underlying definitions and the proposed modules throughout the paper.

To achieve effective and generalized TS dataset condensation (Challenge I), we propose a curriculum training trajectory matching  $(CT<sup>2</sup>M)$  module (see Figure [4\)](#page-5-0), where we consider an original time series dataset as the gold standard and seek to imitate the long-term training dynamics of models trained on it. The parameter trajectories trained on the original dataset are called expert trajectories.  $CT<sup>2</sup>M$  first trains a set of expert trajectories of a model, composed of a set of stacked TSOperators, on the large dataset. These are computed offline and are stored in an expert buffer and then serve as guidance for the condensed dataset optimization. Next, we sample trajectories from the expert buffer with curriculum trajectory queries and conduct long-term trajectory matching to align the segments of trajectories between the condensed time series dataset and the large dataset, enabling comprehensive and general knowledge transfer to the condensed dataset.

To support the capture of complex temporal dependencies (Challenge II), we propose a time series feature extraction (TSFE) module. In particular, TSFE contains a channel-independent mechanism and a set of stacked TSOperators, where a TSOperator includes a set of self-attention and fully connected layers. A decomposition block separates and refines the frequency, i.e., trend and seasonality, progressively from the intermediate results extracted by each TSOperator. We design a decomposition-driven frequency matching (DDFM) module to facilitate consistent temporal pattern preservation between the condensed time series dataset and the original one. It maps the decomposed frequencies when optimizing the condensed dataset.

To address the issue of high computation costs and poor scalability (Challenge III), we propose an expert buffer to save expert

trajectories pre-computed on original time series data to avoid memory overflow and reduce the overall training time. Further, we employ a patching mechanism that splits the time series into patches, thereby accelerating time series feature extraction in the TSFE module and also enabling effective local semantics modeling for time series.

The main contributions are summarized as follows.

- To the best of our knowledge, this is the first study to learn dataset condensation over time series. We propose a framework called TimeDC that aims to condense large time series datasets into small synthetic time series datasets while retaining the expressiveness of the large datasets.
- We design a TSFE module to capture temporal dependencies of time series effectively. We further propose a DDFM module to reduce the discrepancy of frequencies between original and condensed time series.
- A novel curriculum trajectory query and matching module is proposed to penalize condensed time series data based on how far the synthetically trained model deviates from expert trajectories.
- We report on experiments using real data, offering evidence of the effectiveness and efficiency of the proposals.

The remainder of this paper is organized as follows. Section [2](#page-2-0) covers preliminary concepts and formalizes the problem of time series dataset condensation. We detail the TimeDC framework in Section [3,](#page-2-1) followed by the experimental study in Section [4.](#page-6-0) Section [5](#page-11-0) surveys related work, and Section [6](#page-11-1) concludes the paper.

<span id="page-2-0"></span>

## 2 PRELIMINARIES

We proceed to present the necessary preliminaries and then define the problem addressed.

Definition 2.1 (Time Series). A time series  $T = \langle t_1, t_2, \dots, t_n \rangle$  is a time ordered sequence of  $n$  observations, where each observation  $t_i \in \mathbb{R}^C$  is a C-dimensional vector. If  $C = 1$ , T is univariate; otherwise,  $T$  is multivariate. For example, a series of stock prices with multiple features, e.g., the opening price, closing price, highest price, and lowest price, is an example of multivariate time series.

Definition 2.2 (Time Series Dataset). A time series dataset  $T$  is a set of time series  $\mathcal{T} = \{T_1, T_2, \cdots, T_M\}$ , where *M* is the cardinality.

### 2.1 Dataset Condensation over Time Series

Definition 2.3 (Condensed Time Series Dataset). Given a time series dataset  $\mathcal{T}$ , a time series dataset  $\mathcal{S} = \left\{ \widetilde{T}_1, \widetilde{T}_2, \cdots, \widetilde{T}_N \right\}$  is condensed if N is much smaller than M, where  $\overline{T}$  is the condensed time series, *M* is the cardinality, and *S* is derived from  $\mathcal{T}$ .

Note that we can initialize each item in  $S$  with a random time series in the original dataset or a Gaussian noise. After dataset condensation, we expect to learn an optimized  $S$  to replace  $T$ .

Given a large time series dataset  $\mathcal{T} = \{T_1, T_2, \cdots, T_M\}$ , we aim to learn an optimal differentiable function  $f$  parameterized by  $\theta$  that correctly performs time series tasks, e.g., time series forecasting. We use  $\mathcal T$  as a training set to learn the optimal parameters  $\theta^{\mathcal T}$  by minimizing an empirical loss term as follows.

<span id="page-2-2"></span>
$$
\theta^{\mathcal{T}} = \underset{\theta}{\arg\min} \mathcal{L}^{\mathcal{T}}(\theta),\tag{1}
$$

where  $\mathcal{L}^{\mathcal{T}}(\theta) = \frac{1}{M} \sum_{T_i \in \mathcal{T}} \ell(f_\theta, T_i)$ , and  $\ell(\cdot)$  is a task specific loss (e.g., Mean Squared Error). We denote the generalization performance of the obtained model  $f_{\theta^{\mathcal{T}}}$  by  $\mathbb{E}_{T_i \sim P_{\mathcal{T}}} \big[ \ell(f_{\theta^{\mathcal{T}}}, T_i) \big],$  where  $P_{\mathcal{T}}$ is the data distribution of  $\mathcal T$ .

Dataset Condensation over Time Series. Given a large time series dataset  $\mathcal T$ , our goal is to synthesize a small time series dataset  $\mathcal{S} = \left\{ \widetilde{T}_1, \widetilde{T}_2, \cdots, \widetilde{T}_N \right\}$  that preserves most of the information in  $\mathcal{T}$ and  $\dot{N} \ll M$ . Once the condensed time series set S is learned, we can train  $f$  on it by replacing  $\mathcal T$  by  $\mathcal S$  in Equation [1.](#page-2-2) As the condensed set  $S$  is significantly smaller than  $T$ , we expect the optimization to be considerably faster. In addition, we expect the generalization performance of  $f_{\theta S}$  to be close to  $f_{\theta^{\mathcal{T}}}$ , i.e.,  $\mathbb{E}_{T_i \sim P_{\mathcal{T}}}[l(f_{\theta^{\mathcal{T}}}, T_i)] \simeq$  $\mathbb{E}_{T_i\sim P_{\mathcal{S}}}\left[\ell(f_{\theta^{\mathcal{S}}},T_i)\right]$ , on the real data distribution  $P_{\mathcal{T}}$ .

Thus, given a learnable function  $f$  parameterized by  $\theta$ , our problem, dataset condensation over time series, can be formulated as a bi-level optimization problem [\[48\]](#page-12-12) as follows.

$$
\underbrace{\text{outer level}}_{S} \overbrace{\mathcal{L}(f_{\theta S}, \mathcal{T})}^{outer level}, \text{ s.t. } \theta^{S} = \overbrace{\arg\min_{\theta} \mathcal{L}(f_{\theta}, S)}^{inner level}, \tag{2}
$$

where the outer loop optimizes the condensed time series set  $S$ , while the inner loop aims to learn an optimal function  $f_{\theta S}$  on  $S$ .

Offline Expert Trajectories. We denote the time sequence of parameters  $\{\theta_e^{\vec{k}}\}_{e=1}^E$  as expert trajectories, that are obtained during the training of the proposed TSFE module on the full, original time series dataset, where  $E$  is the training epoch intervals. To generate these expert trajectories, we train TSFE several times on original datasets and save their snapshot parameters at every epoch. We call these parameter sequences as expert trajectories since they represent the theoretical upper bound for the downstream applications.

<span id="page-2-1"></span>

## 3 METHODOLOGY

We proceed to detail the efficient time series dataset condensation framework, TimeDC. We first give an overview of the framework and then provide specifics on each module in the framework.

### 3.1 Framework Overview

As illustrated in Figure [2,](#page-3-0) given a model composed of stacked TSOperators, we first pre-train a set of training trajectories on the original large time series (OLTS) dataset and store these pre-trained trajectories as expert trajectories in an expert buffer. As the original dataset is used to guide the network training, we denote the parameters trained on the original dataset as expert trajectory. Then, we train the same model on the condensed time series (CTS) dataset and perform two-fold modal matching: trajectory matching and frequency matching. Specifically, we optimize the condensed dataset with respect to the distance between the synthetically training trajectories and the trajectories trained on the original dataset. Further, we align the frequencies (i.e., trend and seasonality) between the original dataset and the condensed dataset to preserve temporal correlations when synthesizing the condensed dataset. The framework encompasses three major modules: time series feature extraction (TSFE), decomposition-driven frequency matching (DDFM), and curriculum training trajectory matching  $(CT<sup>2</sup>M)$ .

• Time Series Feature Extraction. This module aims to extract effective high-dimensional features from the input time series.

<span id="page-3-0"></span>Image /page/3/Figure/0 description: This figure illustrates a deep learning framework for trajectory prediction. The framework takes OLTS (Online Learning Trajectory Sequences) and CTS (Collected Trajectory Sequences) as input. These sequences are processed through TSFE (Trajectory Sequence Feature Extraction) modules, each containing multiple TSOperator blocks. The processed features are then fed into a DDFM (Deep Dynamic Feature Mapping) module. The DDFM output is compared with Expert Trajectories, represented by a set of sequences labeled B, containing points like \( heta\_{e\_0}^i\), \( heta\_{e\_0}^{i+1}\), etc. Another output from the DDFM is used to generate a Training Trajectory, which includes predicted points like \(\" ilde{\ heta}\_{e\_0}\"), \(\" ilde\{\ heta}\_{e\_0+1}\"), etc. A CT2M (Cross-Trajectory Transformer Module) connects the Expert Trajectories and the Training Trajectory, facilitating an update mechanism. Backwards propagation is indicated from the DDFM to the TSFE modules and also from the CT2M module. The figure also shows that CTS has N >> M sequences, while OLTS has M sequences.

Figure 2: TimeDC Framework Overview

A channel-independent mechanism is adopted to decouple the time series  $T_{input} \in \mathbb{R}^{\mathcal{B} \times n \times C}$  ( $C \ge 1$ ) into C univariate time series  $\{T_{input}^c\}_{c=1}^{\hat{C}} \in \mathbb{R}^{\mathcal{B} \times n \times 1}$  for subsequent feature extraction. Next, the module splits each decoupled univariate (i.e., channelindependent) time series into  $P$  patches to learn local semantic information employing the patching mechanism. Then, the module stacks several TSOperators, composed of self-attention and fully connected layers, for channel-independent feature extraction. Finally, the channel-independent features are concatenated to enable prediction.

- Decomposition-Driven Frequency Matching. This module adopts a decomposition-driven frequency matching mechanism to reduce the discrepancy of decomposed frequencies between the original time series dataset and the condensed time series dataset, aiming to preserve temporal dependencies from the original dataset when synthesizing the condensed dataset. Specifically, it incorporates a moving-average based [\[12,](#page-12-31) [40\]](#page-12-26) series decomposition block into each TSOperator, which separates the trend and seasonal information progressively from the learned features. This decomposition block enables a TSOperator to alternately decompose and refine the intermediate results during the time series feature extraction.
- Curriculum Training Trajectory Matching. This module first trains a set of training trajectories (i.e., parameters) of stacked TSOperators on the large original time series dataset, which are stored in an expert buffer. In addition, we design a curriculum trajectory query and matching mechanism to organize training trajectories matching from similar (easy) to dissimilar (hard). In particular, we sample trajectories from the expert buffer with minimally modified parameters to those with highly modified parameters, iteratively.

We train TimeDC on the original dataset to extract frequencies and compute original parameters denoted as expert trajectories, aiming at guiding the effective condensed dataset synthesis. Thus, extracting frequencies and expert trajectories from the original dataset supports the purpose of TimeDC, which enables models trained on the small dataset to achieve comparable performance to models trained on the large dataset. Next, we provide the technical details of each module.

<span id="page-3-1"></span>Image /page/3/Figure/6 description: This is a block diagram illustrating a feature extraction model. The model takes 'Multivariate TS' as input and processes it through several stages. First, it passes through a 'CIM' (Contextual Information Module) block, followed by a 'Patching' block. These two blocks are then fed into a 'TSOperator' which is enclosed in a dashed rectangle. Inside the TSOperator, the data undergoes 'Self Attention', then a 'FC' (Fully Connected) layer, and finally a 'Concatenation' block. The output of the Concatenation block is labeled as 'Features'.

Figure 3: Feature Extraction Module

### 3.2 Time Series Feature Extraction

We first consider the time series feature extraction (TSFE) module. As shown in Figure [3,](#page-3-1) TSFE is composed of a channel-independent mechanism, a patching mechanism, and stacked TSOperators.

Channel Independent Mechanism. Most existing time series modeling methods [\[41,](#page-12-32) [44\]](#page-12-3) perform time series feature extraction by coupling all features and projecting them together into an embedding space. However, simply coupling different features may influence model performance very uncontrollably [\[38\]](#page-12-28). To avoid this problem, we employ the channel-independent mechanism  $CIM(\cdot)$ to model each time series feature independently.

We consider a time series with  $C$  features as a sequence of multi-channel variables. Specifically, given a time series  $T_{input}$  =  $\langle t_1, t_2, \cdots, t_n \rangle \in \mathbb{R}^{\mathcal{B} \times n \times C}$  ( $C \ge 1$ ), the channel independent mechanism separates  $T_{input}$  into  $C$  univariate time series along the feature (i.e., channel) dimension, where  $B$  is the batch size. For example, in a series of stock prices with multiple features, e.g., opening prices and closing prices, the channel-independent mechanism separates these features by forming univariate time series. This means that the opening and closing prices are considered as two different time series in the subsequent feature extraction. Formally, the channelindependent mechanism works as follows.

<span id="page-3-2"></span>
$$
CIM(T_{input}) = T_{input}^1, \cdots, T_{input}^c, \cdots, T_{input}^C,
$$
 (3)

where  $T_{input}^c \in \mathbb{R}^{\mathcal{B} \times n \times 1}$ . The separated univariate time series are then fed independently into the stacked TSOperators. The TSOperators share the same architecture, but their forward processes are independent.

Patching Mechanism. Time series feature extraction aims to model the correlations between observations at different time steps. We aggregate observations over several time steps of the separated univariate time series  $T_{input}^c$  into subseries-level patches [\[29\]](#page-12-33) to enable local semantics modeling, which allows the model to see the longer historical sequences to improve feature extraction. For instance, in the context of stock prices, if the original time series represents hourly prices over a week, the patching mechanism may create patches where each patch contains the stock prices for each day, enhancing the capture of local semantics capturing.

We feed the univariate time series obtained from the channel independent mechanism into the patching mechanism that then divides them into patches that can be set to be overlapping or non-overlapping. Given a patch length  $L$  and a stride (i.e., the nonoverlapping region between two consecutive patches)  $S$ , a sequence of patches  $T_p^c \in \mathbb{R}^{\mathcal{B} \times L \times \mathcal{P}}$  is generated, where  $\mathcal{P} = \lfloor \frac{n-L}{S} + 2 \rfloor$  is the number of patches. The last value  $t_n$  repeated  $S - 1$  times is padded to the end of the input time series before patching.

The patching decreases the memory usage and computational complexity of the following TSOperator, containing self-attention and fully connected (FC) layers quadratically, by a factor of  $S$  since the input time series length reduced from  $n$  to approximately  $n/S$ .

**TSOperator.** We then feed the patches  $T_b^c$  into the stacked TSOperators to learn latent features for the following frequency matching and trajectory matching. As illustrated in Figure [3,](#page-3-1) a TSOperator includes a multi-head self-attention layer followed by a fullyconnected (FC) layer. In the context of stock prices, the multi-head self-attention mechanism learns the temporal correlations between different time steps. This may include how the opening prices at the beginning of a day influence the prices in the next few days. The FC layer summarizes these temporal correlations for future prediction. The *j-th TSOperator* can be formulated as follows.

<span id="page-4-6"></span>
$$
h^{j} = TSOperator(h^{j-1}) = Norm(FC(MultiHead(h^{j-1}))), (4)
$$

where  $h^0 = T_b^c$ , and  $Norm(\cdot)$  is the normalization layer (i.e., Batch-Norm). The multi-head self-attention layer has three components: Query, Key, and Value. The multi-head self-attention is computed by concatenating the output matrix of each attention head. For *i-th* attention head, the input latent features  $h^j$  are transformed as follows.

$$
A_i^j = \text{Attention}(h^j W_i^Q, h^j W_i^K, h^j W_i^V)
$$
  
\text{Attention}(Q, K, V) = \text{softmax}\left( $\frac{Q \cdot K^T}{\sqrt{d_k}}$ \right) \cdot V (5)  
$$
h^{j+1} = \text{Norm}(h^j + FC_i(A_i^j h^j)),
$$

where  $d_k$  is a scaling factor, and  $W_i^{\overline{Q}}$  $W_i^Q, W_i^K,$  and  $W_i^V$  are the linear transformation parameter of the Query, Key, and Value, respectively.

The time series feature extraction is shown in Algorithm [1.](#page-4-0) Specifically, we first separate the batch of input time series  $T_{input}$  into  $C$ univariate time series (line [1\)](#page-4-1) and apply the patching mechanism to split the univariate time series into patches (line [2\)](#page-4-2). Then, we input the patches obtained in line [2](#page-4-2) into the stacked TSOperators for feature extraction (lines  $3-8$ ). Finally,  $C$  extracted features are concatenated to get the overall hidden features  $h$  (line [9\)](#page-4-5). The space and time complexities of the TSFE module are both  $O(\frac{n^2}{S^2})$ .

<span id="page-4-3"></span><span id="page-4-2"></span><span id="page-4-1"></span><span id="page-4-0"></span>Algorithm 1: Time Series Feature Extraction **Input:** a batch of time series:  $T_{input} \in \mathcal{B} \times n \times C$ ; the number of TSOperators:  $N_{op}$ . Output: extracted features: ℎ. 1  $T_{input}^1$ ,  $\cdots$ ,  $T_{input}^c$ ,  $\cdots$ ,  $T_{input}^C$   $\leftarrow$  Separate  $T_{input}$  into C univariate channel-independent time series with Equation [3;](#page-3-2) <sup>2</sup> Split each univariate time series into patches; 3 for  $1 \leq c \leq C$  do 4  $h_c^0 \leftarrow T_{input}^c;$ 5 for  $0 < j < N_{op}$  do 6 **Feature extraction with Equation [4;](#page-4-6)**  $\begin{array}{ccc} \n\frac{1}{7} & | & h_c^j \leftarrow \text{TSOperator}(h_c^{j-1}); \n\end{array}$  $s \mid h_c \leftarrow h_c^{N_{op}-1};$ 9 *h* ← Concatenate  $\{h_c\}_{c=1}^C$  along the channel; 10 return  $h$ .

<span id="page-4-5"></span><span id="page-4-4"></span>

#### 3.3 Decomposition-Driven Frequency Matching

Time series data usually exhibit distinct frequencies, such as trend and seasonality, separating from other modalities (e.g., images). For instance, the trend in stock prices indicates the prolonged direction of prices over time, which can be upward or downward. Seasonality refers to the regular, periodic fluctuations in stock prices, often influenced by seasonal business cycles or holidays. We argue that it would give more guidance on time series condensation if the condensed and original time series share similar frequencies. Thus, to preserve temporal patterns in the condensed time series dataset, we propose a novel decomposition-driven frequency matching (DDFM) mechanism that incorporates a decomposition block into the stacked TSOperators.

As shown in Figure [2,](#page-3-0) for each TSOperator, DDFM decomposes the intermediate hidden features, learned from the original dataset  $\mathcal T$  and the condensed dataset  $\mathcal S$ , into their frequencies (i.e., trend and seasonality) progressively. It then aligns the frequencies to reduce the discrepancy of the temporal patterns between the original dataset and the condensed dataset based on the moving average. Concretely, for the hidden features  $h^j$  of the j-th TSOperator, we adopt the moving average by means of average pooling  $AvgPool(\cdot)$ to smooth periodic fluctuations and highlight the trend and seasonality. In particular, the process of series decomposition is as follows.

$$
h_{TRE}^j = AvgPool(Padding(h^j)), h_{SEA}^j = h^j - h_{TRE}^j,
$$
 (6)

where  $h_{TRE}^j$  and  $h_{SEA}^j$  are the decomposed trend and seasonality, respectively. We employ the average pooling AvgPool( $\cdot$ ) with the padding operation  $Padding(\cdot)$  to keep the time series length unchanged. We denote the decomposition process as follows.

<span id="page-4-7"></span>
$$
h_{TRE}^j, h_{SEA}^j = SeriesDecomposition(h^j)
$$
 (7)

Given the extracted features  $h_c^j$  $\frac{j}{\mathcal{T}}$  and  $h_z^j$  $\mathcal{S}_{\mathcal{S}}$  of the original dataset  $\mathcal T$  and the condensed dataset  $\mathcal S$ , we decompose the corresponding trend and seasonality according to Equation [7,](#page-4-7) We use cosine similarity  $cos($ ,  $\cdot$ ) to measure similarities between the trends  $h_2$  $^J_{\textit{TRE}_{\mathcal{T}}},$   $h^j$  $\frac{j}{\textit{TRE}_{\mathcal{S}}}$  and seasonalities  $h_{\mathcal{S}}^j$  $_{SEA\tau}^{j},$   $h_{S}^{j}$  $S_{\text{E}A_{\mathcal{S}}}^{\prime}$ , formulated as follows.

$$
\cos(h_{TRE_{\mathcal{T}}}^{j}, h_{TRE_{\mathcal{S}}}^{j}) = \frac{h_{TRE_{\mathcal{T}}}^{j}}{||h_{TRE_{\mathcal{T}}}^{j}||_{2}} \cdot \frac{h_{TRE_{\mathcal{S}}}^{j}}{||h_{TRE_{\mathcal{S}}}^{j}||_{2}}
$$

$$
\cos(h_{SEA_{\mathcal{T}}}^{j}, h_{SEA_{\mathcal{S}}}^{j}) = \frac{h_{SEA_{\mathcal{T}}}^{j}}{||h_{SEA_{\mathcal{T}}}^{j}||_{2}} \cdot \frac{h_{SEA_{\mathcal{S}}}^{j}}{||h_{SEA_{\mathcal{S}}}^{j}||_{2}}
$$
(8)

where  $|| \cdot ||_2$  is  $l_2$ -norm.

We aim to maximize the cosine similarity to reduce the discrepancy of frequencies between the condensed data and the original data. The objective function of DDFM is formulated as follows.

<span id="page-5-2"></span>
$$
L_{Fre} = -\frac{1}{N_{op}} \sum_{j}^{N_{op}} (\cos(h_{TRE_{\mathcal{T}}}^{j}, h_{TRE_{\mathcal{S}}}^{j}) + \cos(h_{SEA_{\mathcal{T}}}^{j}, h_{SEA_{\mathcal{S}}}^{j})), \quad (9)
$$

where  $N_{op}$  is the number of TSOperators.

## 3.4 Curriculum Training Trajectory Matching

To enable effective dataset condensation, we propose a curriculum training trajectory matching mechanism. Existing popular dataset condensation methods, which are often based on gradient matching [\[48\]](#page-12-12), conduct online gradient matching step by step. These methods have drawbacks when used for short-range matching, causing short-sightedness issues, thus failing to imitate holistic learning behaviors, and reducing the quality of condensed time series data.

Motivated by an existing study [\[6\]](#page-12-24), we match the long-term training trajectories of the TTFM module with the offline guidance of expert trajectories, which are pre-trained and stored in the expert buffer. We consider the expert trajectories (i.e., the performance of stacked TSO perators trained on the original dataset  $\mathcal{T}$ ) as the theoretical upper bound for the downstream application tasks. Our goal is to obtain a condensed dataset  $S$  that will induce a similar trajectory as that induced by the real training data  $\mathcal T$  such that models trained on  $T$  and  $S$  achieve similar performance. In addition, we propose a curriculum training trajectory query to perform trajectory matching from similar to dissimilar expert trajectories, which further accelerates the model training. For example, expert trajectories derived from historical stock prices will then guide the alignment of training trajectories extracted from a condensed dataset. This alignment ensures that the condensed dataset's training trajectories capture similar dynamics (such as relationships across different time steps) to those learned from historical data.

In particular, we first train  $K$  time series models with the same architecture, i.e., stacked TSOperators, denoted as  $f<sub>T</sub>$ , on the original large time series dataset. Then, we can obtain  $K$  numbers of expert training trajectories that have a holistic knowledge of the original time series dataset in terms of  $f_{\mathcal{T}}$ 's training process. We save these model parameters  $\{ \Theta_q^k \}$  $\mathcal{F}_{T}^{k}$  =  $\{\theta_{e}^{k}\}_{e=1}^{E}$  at certain epoch intervals E in the expert buffer  ${\mathcal{B}}.$  Finally, we design a curriculum trajectory query mechanism to sample training trajectories of the expert buffer from similar to dissimilar for trajectory matching. Here, the pretraining of  $f_{\mathcal{T}}$  is offline and can be separated from the end-to-end time series condensation, reducing the online computation costs.

Training Trajectory Matching. We use the original dataset to guide the network training, and the parameters trained on this

<span id="page-5-0"></span>Image /page/5/Figure/11 description: The image displays a diagram illustrating the concept of similarity and dissimilarity between time series data. On the left, a single time series is shown, denoted by a green line with several points labeled with "theta-hat" at different time steps (t0, t0+1, t0+2, ..., t0+q). This initial time series branches out into three separate time series on the right, each enclosed in a beige box. The top box shows a time series labeled with "theta-1" at corresponding time steps (t0, t0+1, t0+2, ..., t0+a), with the label "Similar" next to it, indicating a high degree of similarity to the original. The middle box shows a similar time series labeled with "theta-2", and the bottom box shows a time series labeled with "theta-K". A vertical green arrow points downwards from "Similar" to "Dissimilar", suggesting that as the index K increases, the time series become less similar to the original. The points in these time series are also labeled with "theta" followed by the index and time step (e.g., "theta-i+1"). The overall diagram visually represents how different variations of a time series can be compared for similarity.

Figure 4: Curriculum Trajectory Query and Matching

dataset are called an expert trajectory. If a condensed time series dataset is capable of forcing network training dynamics to follow expert trajectories, the idea is that the synthetically trained network will be located close to the model trained on the original time series dataset and will achieve similar test performance for downstream applications.

When sampling a pre-computed parameter trajectory  $\theta_e$  from the expert buffer  $B$ , we aim to minimize the distance between the parameters  $\bar{\theta_e}$  trained on the condensed dataset and  $\theta_e$ :

$$
\arg\min_{\mathcal{S}} \mathbb{E}_{\theta_e \sim \mathcal{B}} \left[ L_{tmm}(\theta_e |_{e=e_0}^a, \widetilde{\theta}_e |_{e=e_0}^b) \right],\tag{10}
$$

where  $\mathbb E$  is the expectation,  $\theta_e|_{e=e_0}^a$ ,  $\widetilde{\theta}_e|_{e=e_0}^b$  are the parameters with range  $(e_0, e_0 + a)$  and  $(e_0, e_0 + b)$ , where  $e_0 < e_0 + a < E$ . More specifically, we update the condensed TS data according to the trajectory matching loss  $L_{tmm}$ .

<span id="page-5-1"></span>
$$
L_{tmm} = \frac{||\widetilde{\theta}_{e_0+b} - \theta_{e_0+a}||_2^2}{||\widetilde{\theta}_{e_0} - \theta_{e_0+a}||_2^2}
$$
(11)

Note that we initialize the parameters of  $f_{\theta S}$  with those of  $f_{\mathcal{T}}$  at  $e_0$ training step, i.e.,  $\tilde{\theta}_{e_0} = \theta_{e_0}$ , for more focused training. This way, we align the learning behavior of  $f_{\mathcal{T}}$  with *a*-steps optimization to  $f_{\theta S}$  with b-steps optimization, imitating the long-term learning behavior of time series modeling.

For the inner loop, we train  $f_{\theta^{\mathcal{S}}}$  on the condensed time series data for optimization until the optimal  $\widetilde{\theta}^*$ . Thus, the objective function of the  $CT<sup>2</sup>M$  is defined as follows.

$$
\min_{\mathcal{S}} \mathbb{E}_{\theta_e^* \sim \mathcal{B}} \left[ L_{tmm}(\theta_e |_{e=e_0}^a, \widetilde{\theta_e} |_{e=e_0}^b) \right] \quad s.t. \ \widetilde{\theta}^{*, \mathcal{S}} = \arg \min_{\widetilde{\theta}} \mathcal{L}(f_{\widetilde{\theta}}, \mathcal{S}),
$$
\n(12)

where  $\mathcal{L}(\cdot)$  is the task-specific loss (e.g., Mean Square Error (MSE)).

Utilizing training trajectory matching, we can reduce computation and memory costs during the condensation process by sampling pre-trained expert trajectories offline. Moreover, long-term trajectory matching provides a more holistic and comprehensive method to imitate the learning behaviors over the original dataset by avoiding that the condensed dataset fits to certain optimization steps short-sightedly.

Curriculum Training Trajectory Query. As shown in Figure [4,](#page-5-0) we propose a curriculum training trajectory query method to further enhance the generalization of time series condensation and accelerate the model convergence [\[37,](#page-12-34) [52\]](#page-12-35). Curriculum learning aims to train a model with easy samples first, and then gradually increases the difficulty levels. In our setting, we first query similar training trajectories  $\theta_e|_{e=e_0}^a$  and  $\widetilde{\theta_e}|_{e=e_0}^b$  , learned on the original and condensed datasets, respectively, to perform trajectory matching.

Algorithm 2: Curriculum Training Trajectory Query and Matching

<span id="page-6-4"></span><span id="page-6-3"></span>Input: A buffer  $B$  with a set of trajectories pre-trained on the original TS dataset  ${\mathcal T}$  parameterized by  $\{\Theta_q^k\}$  $_{\mathcal{T}}^{k}$  ) $_{k=1}^{K}$ ; current model parameters  $\widetilde{\theta}^{\mathcal{S}}$  on  $\mathcal{S}.$ **Output:** Trajectory matching loss  $L_{tmm}$ . 1 Distance list  $DT \leftarrow []$ ; 2 Pre-update  $\tilde{\theta}^S$  for *a*-steps with Equation [13;](#page-6-1)  $\widetilde{\theta}_{e_0+a} \leftarrow \widetilde{\theta}_{e_0} - \sum_{s=1}^a (\alpha \nabla \mathcal{L}(f_{\theta^s}, \mathcal{S}));$ 4 for  $\Theta_q^k$  $\overset{k}{\tau}\in\{\Theta_{\mathcal{I}}^{k}\}$  $_{\mathcal{T}}^k\}_{k=1}^{\breve{K}^{-1}}$ do

5 Compute the distance  $dis_k$  between  $\widetilde{\theta}|_{e_0}^a$  and  $\Theta_{\widetilde{\theta}}^k$  $_\mathcal{T}^{\kappa}$  with Equation [15;](#page-6-2)

$$
\delta \quad \ \left| \quad dis_k \leftarrow -D(\widetilde{\theta}|_{e=e_0}^a, \theta^k|_{e=e_0}^a); \right.
$$

$$
7 \quad DT \leftarrow (k, dis_k);
$$

<span id="page-6-5"></span> $\bf{8}$  Rank DT in a descending order;

<span id="page-6-6"></span> $9 \theta \leftarrow 0$ 

$$
10 while  $\beta < K$  do
$$

- 11  $k \leftarrow DT[\beta][0]$
- 12  $L_{tmm} \leftarrow$  Sample trajectory  $\Theta_q^k$  $\frac{k}{\mathcal{T}}$  and match the training trajectory according to Equation [11;](#page-5-1) 13  $\beta \leftarrow \beta + 1;$

<span id="page-6-7"></span>
$$
\frac{1}{14}
$$
 return  $L_{tnm}$ 

Then, we gradually increase the dissimilarity of the original data training trajectory.

To quantify the similarity between training trajectories of the original dataset  $\mathcal T$  and the condensed dataset  $\mathcal S$ , we design a minimally interfered retrieval sampling strategy. Specifically, given current parameters  $\tilde{\theta}_{e_0}$  of  $f_{\theta S}$  learned on the condensed dataset, trajectories { $\Theta_q^k$  ${}_{\mathcal{F}}^k$   $\big\}^K_{k=1}$  in buffer  $\mathcal{B}$ , and a standard task-specific objective function  $\min_{\theta^{\mathcal{S}}} \mathcal{L}(f_{\theta^{\mathcal{S}}}, \mathcal{S}),$  we retrieve trajectories that will be close to  $\theta$  by the update of the foreseen (i.e., future) parameters to select more similar trajectories from  $\mathcal{B}$ . We update the parameter  $\widetilde{\theta}_{e_0}$  for *a*-steps by gradient matching, as shown in Equation [13.](#page-6-1)

<span id="page-6-1"></span>
$$
\widetilde{\theta}_{e_0+a} = \widetilde{\theta}_{e_0} - \sum_{s=1}^a (\alpha \nabla \mathcal{L}(f_{\theta^S}, \mathcal{S})),\tag{13}
$$

where  $\alpha$  is the learning rate. Then, we compute the distance  $D(\cdot, \cdot)$ between the foreseen trajectory  $\widetilde{\theta}|_{e=e_0}^a$  of S and sub-trajectories  $\theta^k|_{e=e_0}^a$  of  $\{\Theta_{\mathcal{I}}^k\}$ |  $\mathcal{F}_{k+1}^K$  in  $\mathcal B$  as follows.

$$
dis_k = -D(\widetilde{\theta}|_{e=e_0}^a, \theta^k|_{e=e_0}^a)
$$
\n(14)

We use the cosine similarity to measure the distance  $D(\cdot)$ .

<span id="page-6-2"></span>
$$
D(\widetilde{\theta}|_{e=e_0}^a, \theta^k|_{e=e_0}^a) = \frac{\widetilde{\theta}|_{e=e_0}^a}{\|\widetilde{\theta}|_{e=e_0}^a\|_2} \cdot \frac{\theta^k|_{e=e_0}^a}{\|\theta^k|_{e=e_0}^a\|_2}
$$
(15)

Finally, we match current model trajectories of  $S$  with the pretrained trajectories in descending order based on the similarity  ${dis_k}_{k=1}^K$ .

Algorithm [2](#page-6-3) shows the process of the proposed curriculum training trajectory matching. Lines [1](#page-6-4)[–8](#page-6-5) concern the curriculum trajectory query, and lines [9–](#page-6-6)[13](#page-6-7) concern the trajectory matching. The space and time complexities of Algorithm [2](#page-6-3) are  $O(K \cdot n)$  and  $O((a+K) \cdot n + KlogK)$ , respectively.

## 3.5 Overall Objective Function

The final loss contains three parts: a task-specific loss  $\mathcal{L}$ , a frequency matching loss  $L_{Fre}$ , and a trajectory matching loss  $L_{tmm}$ . We combine them together and the overall loss is as follows.

<span id="page-6-8"></span>
$$
L_{all} = \mathcal{L} + L_{Fre} + L_{tnm}.\tag{16}
$$

The task-specific loss  $\mathcal L$  is specific to the particular downstream tasks to achieve a better condensed dataset tailored for the intended use. For example, we adopt the cross-entropy loss for task-specific optimization for time series classification tasks to optimize accuracy, while we adopt the mean square error (MSE) loss to minimize the discrepancy between ground truth and predicted values for time series forecasting tasks.

<span id="page-6-0"></span>

## 4 EXPERIMENTAL EVALUATION

### 4.1 Experimental Setup

4.1.1 Datasets. Various time series analytics are on edge devices where the storage is limited. We aim at getting a really small condensed dataset. The experiments are carried out on six widely-used time series datasets, covering four application domains: weather, traffic, economics, and energy.

- Weather. The Weather dataset contains 21 indicators of weather (e.g., air temperature and humidity), which are collected in Germany. The data is recorded every 10 minutes.
- Traffic. The Traffic dataset contains hourly road occupancy rates obtained from sensors located at San Francisco freeways from 2015 to 2016.
- Electricity. The Electricity dataset contains the hourly electricity consumption of 321 clients from 2012 to 2014.
- ETT. The ETT dataset includes two hourly-level datasets (ETTh1 and ETTh2) and two 15-minute-level datasets (ETTm1 and ETTm2). Each dataset includes 7 oil and load features of electricity transformers between July 2016 and July 2018.

We choose time series forecasting as a representative downstream task, as it is a popular analytics task. In Section [4.4,](#page-11-2) we also present the performance comparison on the task of time series classification. The numbers of condensed time series are set to 500 and 50 as default for forecasting and classification tasks, respectively. We employ the proposed stacked *TSOperators* as the forecasting and classification models.

4.1.2 Baselines. We compare TimeDC with the following existing methods that include coreset construction methods (i.e., Random, Herding [\[39\]](#page-12-36), and K-Center [\[14\]](#page-12-37)), and dataset condensation methods (i.e., DC [\[48\]](#page-12-12) and MTT [\[6\]](#page-12-24) ).

- Random. The Random method randomly selects certain numbers of time series as a coreset.
- Herding. The Herding method adds time series observations to coresets greedily [\[39\]](#page-12-36).
- K-Center. The K-Center method first performs K-Center clustering on the original datasets and then chooses observations from each cluster [\[14\]](#page-12-37).

<span id="page-7-0"></span>

| Baseline    | Dataset | PL    | Random |       | K-Center |       | Herding |       | DC    |       | MTT   |       | TimeDC |       | Whole Dataset |     |
|-------------|---------|-------|--------|-------|----------|-------|---------|-------|-------|-------|-------|-------|--------|-------|---------------|-----|
|             |         |       | MAE    | MSE   | MAE      | MSE   | MAE     | MSE   | MAE   | MSE   | MAE   | MSE   | MAE    | MSE   | MAE           | MSE |
| Weather     | 96      | 0.731 | 1.256  | 0.452 | 0.687    | 0.478 | 0.677   | 0.361 | 0.514 | 0.295 | 0.244 | 0.257 | 0.188  | 0.239 | 0.182         |     |
|             | 192     | 0.786 | 1.302  | 0.487 | 0.723    | 0.512 | 0.688   | 0.413 | 0.527 | 0.344 | 0.301 | 0.285 | 0.247  | 0.261 | 0.195         |     |
|             | 336     | 0.794 | 1.311  | 0.524 | 0.756    | 0.554 | 0.712   | 0.444 | 0.567 | 0.368 | 0.328 | 0.330 | 0.287  | 0.282 | 0.241         |     |
| Traffic     | 96      | 0.675 | 1.125  | 0.503 | 0.576    | 0.483 | 0.554   | 0.375 | 0.603 | 0.279 | 0.403 | 0.254 | 0.375  | 0.247 | 0.337         |     |
|             | 192     | 0.712 | 1.144  | 0.514 | 0.604    | 0.517 | 0.606   | 0.432 | 0.633 | 0.336 | 0.442 | 0.297 | 0.405  | 0.265 | 0.338         |     |
|             | 336     | 0.729 | 1.117  | 0.523 | 0.611    | 0.553 | 0.654   | 0.449 | 0.676 | 0.355 | 0.471 | 0.312 | 0.423  | 0.297 | 0.360         |     |
| Electricity | 96      | 0.421 | 0.669  | 0.448 | 0.583    | 0.501 | 0.592   | 0.376 | 0.513 | 0.296 | 0.283 | 0.274 | 0.267  | 0.252 | 0.268         |     |
|             | 192     | 0.450 | 0.743  | 0.476 | 0.601    | 0.534 | 0.628   | 0.419 | 0.532 | 0.315 | 0.337 | 0.285 | 0.294  | 0.239 | 0.255         |     |
|             | 336     | 0.491 | 0.853  | 0.506 | 0.622    | 0.569 | 0.477   | 0.436 | 0.544 | 0.339 | 0.356 | 0.304 | 0.322  | 0.271 | 0.285         |     |
| ETTh1       | 96      | 0.523 | 0.745  | 0.554 | 0.698    | 0.536 | 0.656   | 0.503 | 0.442 | 0.456 | 0.464 | 0.413 | 0.401  | 0.354 | 0.386         |     |
|             | 192     | 0.557 | 0.786  | 0.578 | 0.722    | 0.589 | 0.698   | 0.552 | 0.508 | 0.504 | 0.471 | 0.436 | 0.428  | 0.362 | 0.355         |     |
|             | 336     | 0.588 | 0.802  | 0.604 | 0.745    | 0.603 | 0.723   | 0.556 | 0.513 | 0.498 | 0.464 | 0.447 | 0.431  | 0.409 | 0.387         |     |
| ETTh2       | 96      | 0.487 | 0.655  | 0.589 | 0.711    | 0.521 | 0.589   | 0.463 | 0.524 | 0.388 | 0.342 | 0.368 | 0.271  | 0.324 | 0.255         |     |
|             | 192     | 0.509 | 0.673  | 0.605 | 0.732    | 0.553 | 0.621   | 0.488 | 0.536 | 0.416 | 0.384 | 0.389 | 0.302  | 0.332 | 0.257         |     |
|             | 336     | 0.524 | 0.689  | 0.623 | 0.744    | 0.564 | 0.640   | 0.505 | 0.540 | 0.435 | 0.455 | 0.411 | 0.334  | 0.376 | 0.296         |     |
| ETTm1       | 96      | 0.743 | 1.124  | 0.525 | 0.492    | 0.607 | 0.554   | 0.603 | 0.665 | 0.512 | 0.453 | 0.503 | 0.442  | 0.453 | 0.403         |     |
|             | 192     | 0.764 | 1.245  | 0.566 | 0.510    | 0.628 | 0.571   | 0.597 | 0.647 | 0.563 | 0.501 | 0.512 | 0.465  | 0.464 | 0.432         |     |
|             | 336     | 0.801 | 1.128  | 0.571 | 0.523    | 0.644 | 0.582   | 0.624 | 0.668 | 0.552 | 0.488 | 0.500 | 0.483  | 0.477 | 0.455         |     |
| ETTm2       | 96      | 0.664 | 0.795  | 0.486 | 0.623    | 0.524 | 0.558   | 0.472 | 0.535 | 0.376 | 0.421 | 0.354 | 0.391  | 0.347 | 0.381         |     |
|             | 192     | 0.687 | 0.804  | 0.512 | 0.643    | 0.549 | 0.583   | 0.488 | 0.567 | 0.453 | 0.479 | 0.401 | 0.421  | 0.358 | 0.403         |     |
|             | 336     | 0.702 | 0.823  | 0.558 | 0.661    | 0.598 | 0.624   | 0.493 | 0.556 | 0.473 | 0.523 | 0.453 | 0.474  | 0.406 | 0.435         |     |

Table 1: Overall Performance Comparison on Seven Datasets

- DC. The DC method employs gradient matching to perform dataset condensation [\[48\]](#page-12-12).
- MTT. The MTT method matches the multi-step training parameters of the condensed data and the original data [\[6\]](#page-12-24).

4.1.3 Evaluation Metrics. Mean Absolute Error (MAE) and Root Mean Square Error (RMSE) are adopted as the evaluation metrics, which are defined as follows.

$$
MAE = \frac{1}{M} \sum_{m=1}^{M} |\mathcal{Y}^m - \mathcal{Y}^m|, MSE = \frac{1}{M} \sum_{m=1}^{M} ||\mathcal{Y}^m - \mathcal{Y}^m||^2, \qquad (17)
$$

where  $M$  is the testing data size,  $\hat{y}^t$  is the prediction and  $y^t$  is the ground truth. The smaller the MAE and the RMSE are, the more accurate method is. We also evaluate the efficiency of the models, including the training and dynamic tensor memory cost.

4.1.4 Implementation Details. We implement our model using the Pytorch framework on an NVIDIA GTX 3090 GPU. The hyperparameters in the model are set as follows. The patch length and stride are set to 16 and 8, respectively. The initial learning rate is 0.0001. The number of TSOperator layers is 3. The number of heads in the self-attention layer is set to 16. The number of expert trajectories in the expert buffer is set to 10 by default. ETT datasets and other datasets are split into the training data, validation data, and test data by the ratio of 6 : 2 : 2 and 7 : 1 : 2, respectively. The parameters of the baseline methods are set according to their original papers and any accompanying code. All of the models follow the same experimental setup with prediction length  $PL \in$ {96, 192, 336} on all datasets.

### 4.2 Experimental Results

4.2.1 Overall Performance Comparison. We report the MAE and RMSE values of the methods in Table [1.](#page-7-0) The best performance by an existing method (Random, Herding, K-Center, DC, and MTT) is underlined, and the overall best performance is marked in bold. Whole Dataset indicates training on the original dataset and serves as an approximate upper-bound performance; is marked in italics. We use a set of stacked TSFE modules as the basic forecasting model for each baseline. The following observations are made.

- TimeDC achieves the best results on all datasets across all prediction lengths ( $PL \in \{96, 192, 336\}$ ). TimeDC performs better than the best among the baselines by up to 13.49% and 26.59% in terms of MAE and RMSE, respectively. We observe that the performance improvements obtained by TimeDC on the Weather dataset exceed those on the Traffic, Electricity, ETTh1, and ETTh2 in most cases. This is because the Weather has much more training data than the other datasets. TimeDC trained with more training data results in better results. Though the ETTm1 and ETTm2 datasets have substantial training data, TimeDC performs slightly better than baselines on these two datasets. This is because the temporal patterns in ETTm1 and ETTm2 are highly regular and can be captured easily by existing methods.
- The coreset methods (i.e., Random, K-Center, and Herding), perform worse than the condensation methods (i.e., DC, MTT, and TimeDC), where Random has the worst performance in most cases. This is because coreset methods are based on heuristic metrics, that makes it hard to select representative observations and guarantee the optimal solutions.
- A popular condensation method, MTT performs the best among the existing methods except when  $PL = 96$  on ETTh1, due to

its powerful condensation capability that involves matching multi-step parameters.

The experiment offers evidence that TimeDC is more effective than existing coreset and dataset condensation methods.

<span id="page-8-3"></span><span id="page-8-1"></span><span id="page-8-0"></span>Image /page/8/Figure/2 description: This figure displays four line graphs, labeled (a) Weather, (b) Traffic, (c) Electricity, and (d) ETTh1. Each graph plots 'Loss' on the y-axis against a numerical scale on the x-axis ranging from 100 to 800. Two lines are present in each graph: one labeled 'MAE' (Mean Absolute Error) represented by a yellow line with star markers, and another labeled 'MSE' (Mean Squared Error) represented by a blue line with triangle markers. The y-axis in all graphs is scaled from approximately 1.4 to 5.8, with a label '1e-1' indicating a multiplier of 10^-1. Specifically, for (a) Weather, MAE values decrease from 4.6 to 2.4, and MSE values decrease from 3.8 to 2.0. For (b) Traffic, MAE values decrease from 4.4 to 2.4, and MSE values decrease from 4.7 to 3.8. For (c) Electricity, MAE values decrease from 4.8 to 2.6, and MSE values decrease from 4.5 to 2.6. For (d) ETTh1, MAE values decrease from 5.6 to 4.1, and MSE values decrease from 5.7 to 4.1. The figure is titled 'Figure 5. Effects of the Size of Ground Truth TS Dataset on Performance'.

<span id="page-8-4"></span><span id="page-8-2"></span>Figure 5: Effect of the Size of Condensed TS Dataset on Four Datasets ( $PL = 96$ )

4.2.2 Effect of the Size of Condensed Time Series Dataset. To study the effect of the size of a condensed time series dataset, we conduct experiments with 100, 200, 300, 500, and 800 condensed time series. The results are shown in Figure [5.](#page-8-0) We observe that the curves first drop significantly and then increase slightly (Figures [5\(a\)](#page-8-1) and [5\(c\)\)](#page-8-2) or remain almost the same (Figures [5\(b\)](#page-8-3) and [5\(d\)\)](#page-8-4). Generally, the results demonstrate that the model performance improves with an increase in the condensed time series data, as more condensed data yields more training data. In addition, it shows that using more condensed time series data for training is more likely to lead to better performance because more useful knowledge is learned from more data. One can also observe that TimeDC with 800 condensed time series performs slightly worse than TimeDC with 500 condensed time series data on the Weather and Electricity. This may be because the patterns in these datasets are relatively simple. Additional condensed time series data might introduce recurring patterns, thereby making the model overfit to these patterns and degrading performance on other data.

4.2.3 Ablation Study. To gain insight into the effects of the different components of TimeDC, including the patching mechanism (patch), decomposition-driven frequency matching (DDFM), and curriculum training trajectory matching  $(CT<sup>2</sup>M)$ , we evaluate three variants:

- $w/o$  Patch. TimeDC without the patching mechanism.
- w/o DDFM. TimeDC without the DDFM module.
- $w/o$ <sub>C</sub>T<sup>2</sup>M. TimeDC without the CT<sup>2</sup>M module.

Figure [6](#page-8-5) shows results on Weather, Traffic, Electricity, and ETTh1. Regardless of the datasets, TimeDC outperforms its counterparts without the patching mechanism, the DDFM module, and the  $CT<sup>2</sup>M$ module. This shows that these three components are all useful for

<span id="page-8-5"></span>Image /page/8/Figure/10 description: This figure contains four bar charts labeled (a) Weather, (b) Traffic, (c) Electricity, and (d) ETTh1. Each bar chart compares the Mean Absolute Error (MAE) and Mean Squared Error (MSE) for different methods: w/o\_patch, w/o\_DDFM, w/o\_CT2M, and TimeDC. In chart (a) Weather, MAE values are approximately 0.4, 0.28, 0.63, and 0.22, while MSE values are approximately 0.37, 0.23, 0.82, and 0.2. In chart (b) Traffic, MAE values are approximately 0.37, 0.28, 0.53, and 0.24, while MSE values are approximately 0.54, 0.37, 0.72, and 0.36. In chart (c) Electricity, MAE values are approximately 0.4, 0.3, 0.7, and 0.28, while MSE values are approximately 0.3, 0.3, 1.05, and 0.28. In chart (d) ETTh1, MAE values are approximately 0.52, 0.42, 0.7, and 0.38, while MSE values are approximately 0.7, 0.42, 0.78, and 0.38. The y-axis for all charts represents 'Loss' and is scaled by 1e-1. The x-axis for all charts lists the different methods being compared.

Figure 6: Performance of TimeDC and Its Variants on Four Datasets  $(PL = 96)$ 

effective time series dataset condensation. TimeDC obtains MAE and RMSE reductions by up to 12.88% and 22.95%, respectively, compared with  $w/o$  DDFM. Further, on all datasets,  $w/o$   $CT<sup>2</sup>M$ performs worst among all variants. TimeDC performs better than  $w/o$  CT<sup>2</sup>M by at least 44.64% and 48.49% in terms of MAE and MSE, respectively, which indicates the effectiveness of the  $CT<sup>2</sup>M$  module.

4.2.4 Cross-Architecture Performance. Next, we consider the crossarchitecture performance of TimeDC. It is important to determine whether the condensed time series data generated by TimeDC can be used to train an unseen network. To assess such crossarchitecture performance comprehensively, we consider three representative state-of-the-art network architectures for time series forecasting, including Autoformer [\[41\]](#page-12-32), Informer [\[53\]](#page-12-38), and Transformer [\[35\]](#page-12-39). We first synthesize condensed time series data with TimeDC and then train these networks with the condensed time series data. The hyper parameters of these network architectures are set based on their original papers and any accompanying code. For TimeDC, we use a set of stacked TSFE modules as the forecasting network.

The prediction results are given in Table [2.](#page-9-0) Overall, TimeDC has the best performance in most cases, indicating its stable and superior performance, especially on Traffic and Electricity. It also illustrates the effectiveness of the TSFE module. We observe that Autoformer performs worse than TimeDC but better than Informer and Transformer, while the performances of Informer and Transformer are comparable, especially on ETTm1 and ETTm2. These observations are in line with their performances when trained on the original dataset [\[41\]](#page-12-32), indicating that TimeDC learns a generalized condensed time series dataset that works across different network architectures. This is because of the powerful feature extraction capabilities of the proposed stacked TSOperators, as well as the trajectory matching that imitates the long-term training dynamics of models trained on the original dataset. In addition, training a model on the condensed dataset consumes much less time than

<span id="page-9-0"></span>

| Method      | Metric | PL  | Weather | Traffic | Electricity | ETT   |       |       |       |    |     |    |     |
|-------------|--------|-----|---------|---------|-------------|-------|-------|-------|-------|----|-----|----|-----|
|             |        | 96  | 192     | 96      | 192         | 96    | 192   | 96    | 192   | 96 | 192 | 96 | 192 |
| TimeDC      | MAE    | 96  | 0.257   | 0.254   | 0.274       | 0.413 | 0.368 | 0.503 | 0.354 |    |     |    |     |
|             |        | 192 | 0.285   | 0.297   | 0.285       | 0.436 | 0.389 | 0.512 | 0.401 |    |     |    |     |
|             | MSE    | 96  | 0.188   | 0.375   | 0.267       | 0.401 | 0.271 | 0.442 | 0.391 |    |     |    |     |
|             |        | 192 | 0.247   | 0.405   | 0.294       | 0.428 | 0.302 | 0.465 | 0.421 |    |     |    |     |
| Autoformer  | MAE    | 96  | 0.312   | 0.370   | 0.343       | 0.453 | 0.473 | 0.548 | 0.342 |    |     |    |     |
|             |        | 192 | 0.381   | 0.385   | 0.355       | 0.478 | 0.491 | 0.550 | 0.334 |    |     |    |     |
|             | MSE    | 96  | 0.255   | 0.597   | 0.236       | 0.465 | 0.412 | 0.542 | 0.265 |    |     |    |     |
|             |        | 192 | 0.334   | 0.613   | 0.264       | 0.493 | 0.488 | 0.532 | 0.287 |    |     |    |     |
| Informer    | MAE    | 96  | 0.423   | 0.430   | 0.428       | 0.773 | 0.842 | 0.576 | 0.552 |    |     |    |     |
|             |        | 192 | 0.482   | 0.476   | 0.446       | 0.788 | 0.954 | 0.597 | 0.532 |    |     |    |     |
|             | MSE    | 96  | 0.354   | 0.643   | 0.253       | 0.992 | 1.032 | 0.624 | 0.402 |    |     |    |     |
|             |        | 192 | 0.478   | 0.710   | 0.271       | 0.987 | 1.055 | 0.653 | 0.432 |    |     |    |     |
| Transformer | MAE    | 96  | 0.389   | 0.412   | 0.398       | 0.632 | 0.506 | 0.563 | 0.555 |    |     |    |     |
|             |        | 192 | 0.588   | 0.431   | 0.422       | 0.612 | 0.513 | 0.588 | 0.576 |    |     |    |     |
|             | MSE    | 96  | 0.344   | 0.578   | 0.267       | 0.785 | 0.579 | 0.615 | 0.479 |    |     |    |     |
|             |        | 192 | 0.524   | 0.567   | 0.288       | 0.732 | 0.542 | 0.643 | 0.455 |    |     |    |     |

Table 2: Cross-Architecture Performance Comparison

<span id="page-9-4"></span>

#### Table 3: Dynamic Tensor Memory Cost on Four Datasets

| Dataset     | DC      | MTT      | TimeDC   |
|-------------|---------|----------|----------|
| Weather     | 10.0 GB | 8.9 GB   | 3.3 GB   |
| Traffic     | 17.8 GB | 13.7 GB  | 10.9 GB  |
| Electricity | 8.5 GB  | 932.5 MB | 516.0 MB |
| ETTh1       | 1.9 GB  | 845.5 MB | 280.9 MB |

training that on the original dataset (see Table [4\)](#page-9-1). This makes it possible to train different candidate models using the condensed data set when performing model selection in TSMSs, thus saving considerable training time.

<span id="page-9-3"></span><span id="page-9-2"></span>Image /page/9/Figure/5 description: This figure contains four bar charts comparing training time. Charts (a) and (c) are for Weather data, while charts (b) and (d) are for ETTh1 data. Charts (a) and (b) compare DC, MTT, and TimeDC methods, with three bars for each representing PL=96, PL=192, and PL=336. In chart (a), DC takes about 65s for PL=96, 70s for PL=192, and 75s for PL=336. MTT takes about 62s for PL=96, 72s for PL=192, and 77s for PL=336. TimeDC takes about 21s for PL=96 and 22s for PL=192 and PL=336. In chart (b), DC takes about 69s for PL=96, 73s for PL=192, and 77s for PL=336. MTT takes about 70s for PL=96, 75s for PL=192, and 80s for PL=336. TimeDC takes about 10s for PL=96 and 11s for PL=192 and PL=336. Charts (c) and (d) compare Autoformer, Informer, Transformer, and TimeDC methods, also with three bars for each representing PL=96, PL=192, and PL=336. In chart (c), Autoformer takes about 115s for PL=96, 145s for PL=192, and 180s for PL=336. Informer takes about 98s for PL=96, 100s for PL=192, and 102s for PL=336. Transformer takes about 95s for PL=96, 98s for PL=192, and 100s for PL=336. TimeDC takes about 22s for PL=96 and 23s for PL=192 and PL=336. In chart (d), Autoformer takes about 33s for PL=96, 40s for PL=192, and 48s for PL=336. Informer takes about 92s for PL=96, 105s for PL=192, and 118s for PL=336. Transformer takes about 67s for PL=96, 75s for PL=192, and 82s for PL=336. TimeDC takes about 8s for PL=96 and 9s for PL=192 and PL=336. The figure is titled "Figure 7: Training Time Comparison."

<span id="page-9-6"></span>Figure 7: Training Time Comparison

<span id="page-9-5"></span>4.2.5 Training Time. As resource efficiency is important in dataset condensation to enable scalability, especially on resource-constrained edge computing devices, we study training time (of an epoch) for

the condensation methods. Figures [7\(a\)](#page-9-2) and [7\(b\)](#page-9-3) report the training time on Weather and ETTh1. We see that the training time of TimeDC is much lower than those of DC and MTT, which is largely because of the expert buffer in the  $CT<sup>2</sup>M$  module that stores the precomputed trajectories. This indicates the feasibility of TimeDC for model deployment in large time series dataset reduction scenarios. We also compare the training time of TimeDC and coreset methods, which are included in the code repository, showing TimeDC achieves training times comparable to those of coreset methods. But coreset methods, especially Herding, need more time to construct coresets while having worse performance. Moreover, we compare the memory used by the dynamic (online) tensor across DC, MTT, and TimeDC in Table [3.](#page-9-4) TimeDC is able to reduce markedly the online memory and computation costs thanks to the training trajectories precomputed offline.

<span id="page-9-1"></span>Table 4: Training Time of TimeDC and Training Time on Condensed and Original Datasets (s/epoch)

| Dataset     | TimeDC | Condensed Dataset | Original Dataset |
|-------------|--------|-------------------|------------------|
| Weather     | 22.39  | 4.31              | 35.26            |
| Traffic     | 232.34 | 61.94             | 346.76           |
| Electricity | 314.56 | 41.14             | 522.85           |
| ETTh1       | 14.38  | 4.93              | 20.43            |

We also study training time across different network architectures on Weather and ETTh1—see Figures [7\(c\)](#page-9-5) and [7\(d\).](#page-9-6) It is clear that TimeDC consumes the least training time. TimeDC is faster than the other methods by at least 73.0%, due to its patching mechanism, which reduces the complexity of the self-attention mechanism through input data simplification. Thus, TimeDC has lower training time across different networks, which still offering better performance. Finally, we compare the training time of TimeDC and the training time on original time series datasets based on the stacked TSOperators, as shown in Table [4.](#page-9-1) One can see that the training time of TimeDC is notably lower than when using original datasets,

showing the efficiency and practicality of time series dataset condensation. For example, the training time of TimeDC for dataset condensation is reduced by 39.84% compared to using the original dataset on Electricity.

<span id="page-10-0"></span>Image /page/10/Figure/1 description: This image displays two scatter plots side-by-side, labeled (a) Weather and (b) ETTh1. Both plots show data points colored blue and red, with a legend indicating that blue dots represent 'Real TS' and red dots represent 'Condensed TS'. The plots visualize the distribution of these two types of data points in a 2D space, with the data points in plot (a) appearing more spread out and less clustered compared to plot (b), where the data points form more distinct clusters.

Figure 8: Dataset Distribution Comparison on Two Datasets

4.2.6 Case Study on Dataset Distribution. To observe the effectiveness of TimeDC on synthesizing condensed time series datasets that cover the original time series distribution well, we visualize the t-SNE [\[34\]](#page-12-40) graphs of the original time series dataset and the condensed time series dataset on Weather and ETTh1. Figure [8](#page-10-0) compares the dataset distributions. Blue and red dots represent the original (i.e., real) time series dataset and the condensed time series dataset, respectively. We sample 500 time series from the original time series dataset for the visualization of the original time series. We observe that the red dots are integrated with the blue dots, indicating that the original time series and condensed time series datasets exhibit similar distributions. This indicates that the condensed dataset is of high quality and that the proposed method is effective. Moreover, the condensed dataset can cover the original dataset evenly on Weather, showing that TimeDC achieves a more diverse and robust condensation result on Weather that exhibits less complicated temporal patterns.

<span id="page-10-1"></span>

| Dataset     | MAE             |             | RMSE   |             |        |
|-------------|-----------------|-------------|--------|-------------|--------|
|             | Method          | Autoformerf | TimeDC | Autoformerf | TimeDC |
| Weather     | $\mathcal{B}_0$ | 0.603       | 0.456  | 0.625       | 0.477  |
|             | $\mathcal{B}_1$ | 0.694       | 0.512  | 0.873       | 0.533  |
|             | I               | 0.823       | 0.656  | 1.014       | 0.512  |
| Traffic     | $\mathcal{B}_0$ | 0.541       | 0.465  | 0.657       | 0.489  |
|             | $\mathcal{B}_1$ | 0.635       | 0.502  | 0.829       | 0.611  |
|             | I               | 0.735       | 0.624  | 0.929       | 0.784  |
| Electricity | $\mathcal{B}_0$ | 0.557       | 0.437  | 0.601       | 0.542  |
|             | $\mathcal{B}_1$ | 0.628       | 0.504  | 0.875       | 0.599  |
|             | I               | 0.844       | 0.689  | 1.046       | 0.702  |
| ETTh1       | $\mathcal{B}_0$ | 0.655       | 0.573  | 0.812       | 0.705  |
|             | $\mathcal{B}_1$ | 0.712       | 0.604  | 0.933       | 0.742  |
|             | I               | 1.297       | 0.762  | 2.015       | 0.979  |

### 4.3 Application on Streaming Time Series

In real-world scenarios, time series data is often generated incrementally at edge devices that are distributed geographically. It may be preferable to process such streaming time series data on the edge directly to enable compliance with data access restrictions and exploit the potential for more efficient processing and lower

Table 6: Storage Comparison on Four Datasets

<span id="page-10-2"></span>

| Storage       | Weather | Traffic  | Electricity | ETTh1    |
|---------------|---------|----------|-------------|----------|
| Whole Dataset | 2.9 GB  | 20.0 GB  | 11.2 GB     | 313.1 MB |
| Condensed TS  | 38.5 MB | 827.5 MB | 308.2 MB    | 12.8 MB  |

latencies. However, time series data keeps growing while the capacities of edge devices are limited. We perform a performance, storage, and parameter comparison to determine whether our condensed time series data can be a key ingredient for streaming time series learning, thereby addressing also the catastrophic forgetting problem [\[11,](#page-12-41) [22\]](#page-12-10), due to its condensed nature.

4.3.1 Performance Comparison. A naive solution to streaming time series learning is to use only newly arrived data, not all the data collected so far, to update model parameters continuously, called fine-tuning. We compare the performance of TimeDC and finetuning to test the effectiveness of TimeDC at streaming learning. For the fine-tuning, we use Autoformer as the basic model, entitled Autoformer $_f$ . We split the original dataset into a base set  $\mathcal B$  and an incremental set  $\mathcal I$  with the ratio 7 : 3.  $\mathcal B$  and  $\mathcal I$  are further split into training data, validation data, and test data with the ratios 7 : 1 : 2. We first train and test the model on  $B$ . The result is denoted as  $B_0$ . Then, we update the learned model with  $I$  and test on  $B$  and  $I$ simultaneously, with the results denoted as  $B_1$  and  $I$ , respectively. For the model update stage of TimeDC, we condense the base set and incorporate the condensed data into  $I$  for subsequent model training to address concept drift in evolving time series datasets. The results in Table [5](#page-10-1) show that TimeDC has the best performance in terms of MAE and RMSE when compared with Autoformer $_f$ on four datasets. For example, TimeDC outperforms Autoformer $_f$ by 18.36% ∼ 21.54% and 9.82% ∼ 32.89% in terms of MAE and RMSE on Electricity, respectively. Autoformer  $_f$  provides acceptable MAE and RMSE results on the base set, but their performance deteriorates on incremental set, especially on ETTh1. This indicates that a simple fine-tuning method is insufficient, due to forgetting problems caused by the possible concept drift. TimeDC achieves relatively stable performance on all base sets and incremental sets, demonstrating its superiority.

Table 7: Parameter Comparison on Four Architectures

<span id="page-10-3"></span>

| Dataset | Autoformer | Informer | Transformer | TimeDC |
|---------|------------|----------|-------------|--------|
| Weather | 10.6 M     | 11.4 M   | 10.6 M      | 1.8 M  |
| Etth1   | 10.5 M     | 11.3 M   | 10.5 M      | 0.2 M  |

4.3.2 Storage and Parameter Comparison. As storage size is a main concern in time series streaming learning on edge devices, we compare the storage cost of the pre-processed whole dataset and the condensed time series dataset using four datasets, as shown in Table [6.](#page-10-2) Generally, the condensed time series data of TimeDC reduces the storage space substantially compared with the whole dataset. The condensed time series data is only 4.09% of the whole dataset on ETTh1. Thus, TimeDC enables much lower storage costs while achieving more promising performance on time series streaming learning.

Another characteristic of edge devices is limited computational capabilities. We thus compare the number of parameters of different network architectures including TimeDC (i.e., TSFE), Autoformer, Informer, and Transformer—see Table [7.](#page-10-3) TimeDC achieves much

fewer parameters, indicating that the TSFE module of TimeDC (mainly the patching mechanism) can significantly reduce computational costs. Tables [5,](#page-10-1) [6,](#page-10-2) and [7](#page-10-3) indicate the feasibility and scalability of TimeDC for streaming learning on edge devices.

<span id="page-11-4"></span>Image /page/11/Figure/1 description: This image contains two bar charts side-by-side, labeled (a) Accuracy and (b) Precision. Both charts display data for three categories: ECG200 (yellow bars), FordB (blue bars), and ElectricDevices (red bars), across different methods: Random, K-Center, HerdingDC, MTT, TimeDC, and Whole. The (a) Accuracy chart shows accuracy percentages on the y-axis, ranging from 0 to 100%. The (b) Precision chart shows precision values on the y-axis, ranging from 0.0 to 1.0. In the Accuracy chart, ECG200 generally shows the highest accuracy, followed by FordB, and then ElectricDevices, with performance varying across the different methods. In the Precision chart, similar trends are observed, with ECG200 generally achieving higher precision than FordB and ElectricDevices across the methods.

Figure 9: Time Series Classification Performance

<span id="page-11-2"></span>

### 4.4 Performance on Time Series Classification

It is important to show that the proposed TimeDC generalizes and can be used in other time series tasks. We conduct experiments on time series classification, considering three datasets (i.e., ECG200, ElectricDevices, and FordB) of the UCR time series archive $^\dagger$ . A set of stacked TSFE modules with an FC is used as the basic classification model. CrossEntropy Loss is used as the objective of time series classification. Accuracy and Recall are adopted as evaluation metrics. The number of condensed time series is set to 50 for each dataset. The overall performance results are provided in Figure [9.](#page-11-4) The whole (dataset) indicates training on the whole original dataset, serving as an approximate upper-bound performance. TimeDC performs better than the best among the baselines by up to 10.40% and 67.31% in terms of Accuracy and Precision, respectively. All coreset construction methods perform worse than dataset condensation methods. Herding has the best performance in most cases among the coreset methods. Overall, TimeDC achieves the best results on three time series classification datasets among the baselines, which shows that TimeDC can be extended to other time series tasks.

<span id="page-11-0"></span>

## 5 RELATED WORK

### 5.1 Time Series Modeling

Time series modeling attracts increasing interest due to the growing availability of time series data and rich downstream applications [\[13,](#page-12-42) [19,](#page-12-43) [20,](#page-12-44) [50,](#page-12-45) [51\]](#page-12-6), such as traffic prediction [\[31,](#page-12-46) [44\]](#page-12-3), electricity prediction [\[29,](#page-12-33) [53\]](#page-12-38), and anomaly detection [\[32,](#page-12-47) [46\]](#page-12-4). Traditional time series analytic models are mostly based on statistical models [\[31\]](#page-12-46). However, the statistical models cannot capture complex temporal correlations of time series data effectively due to their limited learning capacity. Recent advances in deep learning techniques have sparked a surge of interest in applying neural network architectures for time series modeling [\[4,](#page-12-27) [43,](#page-12-48) [44,](#page-12-3) [53\]](#page-12-38) outperforming traditional statistical models, including temporal convolutional network (TCN) based methods [\[9,](#page-12-49) [23\]](#page-12-50), recurrent neural network (RNN) based methods [\[30\]](#page-12-51), and transformer based methods [\[53,](#page-12-38) [54\]](#page-12-8). However, these methods are mostly supervised, and large training data is required resulting in high computational cost. At such scales, it becomes burdensome to store and preprocess the data and calls for specialized equipment and infrastructure to train machine learning models on them.

### 5.2 Coreset and Dataset Condensation

Coreset construction [\[1,](#page-12-14) [5,](#page-12-52) [7,](#page-12-15) [16,](#page-12-53) [22\]](#page-12-10) is the traditional dataset reduction approach that works by identifying the most representative training samples in an original dataset, aiming at achieving models trained on the coreset that are provably competitive with those built on the full dataset. Typically, coreset construction methods choose samples that are representative for training based on heuristic criteria [\[1,](#page-12-14) [7,](#page-12-15) [22\]](#page-12-10), e.g., minimizing distance between coreset and whole-dataset centers [\[1\]](#page-12-14), Nonetheless, these heuristic methods cannot guarantee optimal solutions and the presence of representative samples for the downstream task. A recent approach, dataset condensation (or distillation) [\[6,](#page-12-24) [48\]](#page-12-12), is proposed to address these limitations by learning a small typical dataset that distills the most important knowledge from a given large dataset, such that a model trained on it can obtain comparable testing accuracy to that trained on the original training set. Existing dataset distillation methods have demonstrated superior performance, which can be categorized into matching-based methods [\[6,](#page-12-24) [48,](#page-12-12) [49\]](#page-12-19) and kernel-based methods [\[28,](#page-12-54) [55\]](#page-12-29). Matching-based methods generate synthetic datasets by matching gradients [\[48\]](#page-12-12), multi-step parameters [\[6\]](#page-12-24), and distributions [\[49\]](#page-12-19) between two surrogate models trained on the synthetic dataset and the original dataset. Kernel-based methods [\[28,](#page-12-54) [55\]](#page-12-29) treat the synthetic dataset as the parameters to be optimized inspired by kernel functions e.g., neural tangent kernel. However, most of the above methods are designed for computer vision, and cannot be applied to time series directly due to complex temporal correlations such as seasonality and trend. In addition, these methods require substantial storage costs.

<span id="page-11-1"></span>

## 6 CONCLUSION

We present TimeDC, a new efficient time series dataset condensation framework that aims to synthesize a small but informative condensed time series dataset summarizing an original large time series dataset. To capture complex temporal dependencies, we design a time series feature extraction module with stacked TSOperators. In addition, decomposition-driven frequency matching is proposed to ensure similar temporal patterns between the condensed and original time series datasets. To enable effective and generalized dataset condensation, we propose a curriculum training trajectory matching module with an expert buffer that aims to decrease the training cost. Comprehensive experiments on original datasets offer evidence that TimeDC achieves state-of-the-art accuracy and requires fewer computational and storage resources.

## 7 ACKNOWLEDGMENTS

This work was supported in part by the Independent Research Fund Denmark under agreement 8048-00038B, the Villum Fonden under agreement 40567, the Innovation Fund Denmark project DIREC (9142-00001B), the National Natural Science Foundation of China (62372179, 62472068), Shenzhen Municipal Science and Technology R&D Funding Basic Research Program (JCYJ20210324133607021), Municipal Government of Quzhou (2023D044), and Key Laboratory of Data Intelligence and Cognitive Computing, Longhua District, Shenzhen.

<span id="page-11-3"></span><sup>†</sup>[https://www.cs.ucr.edu/~eamonn/time\\_series\\_data\\_2018/](https://www.cs.ucr.edu/~eamonn/time_series_data_2018/)

## REFERENCES

- <span id="page-12-14"></span>[1] Pankaj K Agarwal, Sariel Har-Peled, and Kasturi R Varadarajan. 2004. Approximating extent measures of points. JACM 51, 4 (2004), 606–635.
- <span id="page-12-20"></span>[2] Charu C Aggarwal and Philip S Yu. 2008. On static and dynamic methods for condensation-based privacy-preserving data mining. TODS 33, 1 (2008), 1–39.
- <span id="page-12-21"></span>[3] Fabrizio Angiulli. 2007. Fast nearest neighbor condensation for large data sets classification. TKDE 19, 11 (2007), 1450–1464.
- <span id="page-12-27"></span>[4] Angela Bonifati, Francesco Del Buono, Francesco Guerra, and Donato Tiano. 2022. Time2Feat: learning interpretable representations for multivariate time series clustering. PVLDB 16, 2 (2022), 193-201.
- <span id="page-12-52"></span>[5] David Campos, Bin Yang, Tung Kieu, Miao Zhang, Chenjuan Guo, and Christian S. Jensen. 2024. QCore: Data-Efficient, On-Device Continual Calibration for Quantized Models. PVLDB 17, 11 (2024), 2708–2721.
- <span id="page-12-24"></span>[6] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. 2022. Dataset distillation by matching training trajectories. In CVPR. 4750–4759.
- <span id="page-12-15"></span>[7] Chengliang Chai, Jiabin Liu, Nan Tang, Ju Fan, Dongjing Miao, Jiayi Wang, Yuyu Luo, and Guoliang Li. 2023. GoodCore: Data-effective and Data-efficient Machine Learning through Coreset Selection over Incomplete Data. SIGMOD 1, 2 (2023),  $1 - 27$ .
- <span id="page-12-9"></span>[8] Peng Chen, Yingying Zhang, Yunyao Cheng, Yang Shu, Yihang Wang, Qingsong Wen, Bin Yang, and Chenjuan Guo. 2024. Pathformer: Multi-scale Transformers with Adaptive Pathways for Time Series Forecasting. In ICLR.
- <span id="page-12-49"></span>[9] Yunyao Cheng, Peng Chen, Chenjuan Guo, Kai Zhao, Qingsong Wen, Bin Yang, and Christian S Jensen. 2024. Weakly guided adaptation for robust time series forecasting. PVLDB 17, 4 (2024), 766–779.
- <span id="page-12-7"></span>[10] Yunyao Cheng, Chenjuan Guo, Bin Yang, Haomin Yu, Kai Zhao, and Christian S. Jensen. 2024. A Memory Guided Transformer for Time Series Forecasting. PVLDB 18 (2024).
- <span id="page-12-41"></span>[11] Zhixuan Chu, Ruopeng Li, Stephen Rathbun, and Sheng Li. 2023. Continual causal inference with incremental observational data. In ICDE. 3430–3439.
- <span id="page-12-31"></span>[12] Robert B Cleveland, William S Cleveland, Jean E McRae, and Irma Terpenning, 1990. STL: A seasonal-trend decomposition. J. Off. Stat 6, 1 (1990), 3–73.
- <span id="page-12-42"></span>[13] Yuchen Fang, Jiandong Xie, Yan Zhao, Lu Chen, Yunjun Gao, and Kai Zheng. 2024. Temporal-Frequency Masked Autoencoders for Time Series Anomaly Detection. In ICDE. 1228–1241.
- <span id="page-12-37"></span>[14] Reza Zanjirani Farahani and Masoud Hekmatfar. 2009. Facility location: concepts, models, algorithms and case studies. Springer Science & Business Media.
- <span id="page-12-17"></span>[15] Dan Feldman and Michael Langberg. 2011. A unified framework for approximating and clustering data. In STOC. 569–578.
- <span id="page-12-53"></span>[16] Dan Feldman, Melanie Schmidt, and Christian Sohler. 2020. Turning big data into tiny data: Constant-size coresets for k-means, PCA, and projective clustering. SICOMP 49, 3 (2020), 601–657.
- <span id="page-12-16"></span>[17] Sona Hasani, Saravanan Thirumuruganathan, Abolfazl Asudeh, Nick Koudas, and Gautam Das. 2018. Efficient construction of approximate ad-hoc ML models through materialization and reuse. PVLDB 11, 11 (2018), 1468–1481.
- <span id="page-12-0"></span>[18] Søren Kejser Jensen, Torben Bach Pedersen, and Christian Thomsen. 2018. Modelardb: Modular model-based time series management with spark and cassandra. PVLDB 11, 11 (2018), 1688–1701.
- <span id="page-12-43"></span>[19] Duc Kieu, Tung Kieu, Peng Han, Bin Yang, Christian S. Jensen, and Bac Le. 2024. TEAM: Topological Evolution-aware Framework for Traffic Forecasting. PVLDB 18 (2024).
- <span id="page-12-44"></span>[20] Tung Kieu, Bin Yang, Chenjuan Guo, Christian S. Jensen, Yan Zhao, Feiteng Huang, and Kai Zheng. 2022. Robust and Explainable Autoencoders for Unsupervised Time Series Outlier Detection. In ICDE. 3038–3050.
- <span id="page-12-1"></span>[21] Zhichen Lai, Dalin Zhang, Huan Li, Christian S Jensen, Hua Lu, and Yan Zhao. 2023. LightCTS: A Lightweight Framework for Correlated Time Series Forecasting. SIGMOD 1, 2 (2023), 1–26.
- <span id="page-12-10"></span>[22] Yiming Li, Yanyan Shen, and Lei Chen. 2022. Camel: Managing Data for Efficient Stream Learning. In SIGMOD. 1271–1285.
- <span id="page-12-50"></span>[23] Chi Harold Liu, Chengzhe Piao, Xiaoxin Ma, Ye Yuan, Jian Tang, Guoren Wang, and Kin K Leung. 2021. Modeling citywide crowd flows using attentive convolutional LSTM. In ICDE. 217–228.
- <span id="page-12-5"></span>[24] Ziqiao Liu, Hao Miao, Yan Zhao, Chenxi Liu, Kai Zheng, and Huan Li. 2024. LightTR: A Lightweight Framework for Federated Trajectory Recovery. In ICDE. 4422–4434.
- <span id="page-12-18"></span>[25] Mario Lucic, Matthew Faulkner, Andreas Krause, and Dan Feldman. 2018. Training gaussian mixture models at scale via coresets. J. Mach. Learn. Res 18, 160  $(2018)$ , 1–25.
- <span id="page-12-25"></span>[26] Hao Miao, Jiaxing Shen, Jiannong Cao, Jiangnan Xia, and Senzhang Wang. 2023. MBA-STNet: Bayes-Enhanced Discriminative Multi-Task Learning for Flow Prediction. TKDE 35, 7 (2023), 7164–7177.
- <span id="page-12-22"></span>[27] Hao Miao, Yan Zhao, Chenjuan Guo, Bin Yang, Kai Zheng, Feiteng Huang, Jiandong Xie, and Christian S. Jensen. 2024. A Unified Replay-Based Continuous Learning Framework for Spatio-Temporal Prediction on Streaming Data. In ICDE. 1050–1062.

- <span id="page-12-54"></span>[28] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. 2020. Dataset Meta-Learning from Kernel Ridge-Regression. In ICLR.
- <span id="page-12-33"></span>[29] Yuqi Nie, Nam H. Nguyen, Phanwadee Sinthong, and Jayant Kalagnanam. 2023. A Time Series is Worth 64 Words: Long-term Forecasting with Transformers. In  $ICIR$
- <span id="page-12-51"></span>[30] David Salinas, Valentin Flunkert, Jan Gasthaus, and Tim Januschowski. 2020. DeepAR: Probabilistic forecasting with autoregressive recurrent networks. Int J Forecast 36, 3 (2020), 1181–1191.
- <span id="page-12-46"></span>Shashank Shekhar and Billy M Williams. 2007. Adaptive seasonal time series models for forecasting short-term traffic flow. TRR 2024, 1 (2007), 116–125.
- <span id="page-12-47"></span>[32] Emmanouil Sylligardos, Paul Boniol, John Paparrizos, Panos Trahanias, and Themis Palpanas. 2023. Choose wisely: An extensive evaluation of model selection for anomaly detection in time series. PVLDB 16, 11 (2023), 3418–3432.
- <span id="page-12-30"></span>[33] Kai Sheng Tai, Vatsal Sharan, Peter Bailis, and Gregory Valiant. 2018. Sketching linear classifiers over data streams. In SIGMOD. 757–772.
- <span id="page-12-40"></span>[34] Laurens Van der Maaten and Geoffrey Hinton. 2008. Visualizing data using t-SNE. J Mach Learn Res 9, 11 (2008).
- <span id="page-12-39"></span>[35] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N Gomez, Łukasz Kaiser, and Illia Polosukhin. 2017. Attention is all you need. NeurIPS 30 (2017).
- <span id="page-12-2"></span>[36] Chen Wang, Xiangdong Huang, Jialin Qiao, et al. 2020. Apache IoTDB: Timeseries database for internet of things. PVLDB 13, 12 (2020), 2901–2904.
- <span id="page-12-34"></span>[37] Qitong Wang, Stephen Whitmarsh, Vincent Navarro, and Themis Palpanas. 2022. iEDeaL: A Deep Learning Framework for Detecting Highly Imbalanced Interictal Epileptiform Discharges. PVLDB 16, 3 (2022), 480–490.
- <span id="page-12-28"></span>[38] Senzhang Wang, Meiyue Zhang, Hao Miao, Zhaohui Peng, and Philip S Yu. 2022. Multivariate correlation-aware spatio-temporal graph convolutional networks for multi-scale traffic prediction.  $TIST$  13, 3 (2022), 1-22.
- <span id="page-12-36"></span>[39] Max Welling. 2009. Herding dynamical weights to learn. In ICML. 1121–1128.
- <span id="page-12-26"></span>[40] Qingsong Wen, Kai He, Liang Sun, Yingying Zhang, Min Ke, and Huan Xu. 2021. RobustPeriod: Robust time-frequency mining for multiple periodicity detection. In SIGMOD. 2328–2337.
- <span id="page-12-32"></span>[41] Haixu Wu, Jiehui Xu, Jianmin Wang, and Mingsheng Long. 2021. Autoformer: Decomposition transformers with auto-correlation for long-term series forecasting. NeurIPS 34 (2021), 22419–22430.
- <span id="page-12-23"></span>[42] Xinle Wu, Xingjian Wu, Bin Yang, Lekui Zhou, Chenjuan Guo, Xiangfei Qiu, Jilin Hu, Zhenli Sheng, and Christian S. Jensen. 2024. AutoCTS++: zero-shot joint neural architecture and hyperparameter search for correlated time series forecasting. VLDBJ 33, 5 (2024), 1743–1770.
- <span id="page-12-48"></span>[43] Xinle Wu, Xingjian Wu, Dalin Zhang, Miao Zhang, Chenjuan Guo, Bin Yang, and Christian S. Jensen. 2024. Fully Automated Correlated Time Series Forecasting in Minutes. PVLDB. 18 (2024).
- <span id="page-12-3"></span>[44] Xinle Wu, Dalin Zhang, Miao Zhang, Chenjuan Guo, Bin Yang, and Christian S Jensen. 2023. AutoCTS+: Joint Neural Architecture and Hyperparameter Search for Correlated Time Series Forecasting. SIGMOD 1, 1 (2023), 1–26.
- <span id="page-12-13"></span>[45] Jinzhao Xiao, Yuxiang Huang, Changyu Hu, Shaoxu Song, Xiangdong Huang, and Jianmin Wang. 2022. Time series data encoding for efficient storage: A comparative analysis in apache IoTDB. PVLDB 15, 10 (2022), 2148–2160.
- <span id="page-12-4"></span>[46] Ronghui Xu, Hao Miao, Senzhang Wang, Philip S Yu, and Jianxin Wang. 2024. PeFAD: A Parameter-Efficient Federated Framework for Time Series Anomaly Detection. In SIGKDD. 3621–3632.
- <span id="page-12-11"></span>[47] Xinyang Yu, Yanqing Peng, Feifei Li, Sheng Wang, Xiaowei Shen, Huijun Mai, and Yue Xie. 2020. Two-level data compression using machine learning in time series database. In ICDE. 1333–1344.
- <span id="page-12-12"></span>[48] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. 2021. Dataset Condensation with Gradient Matching. In ICLR.
- <span id="page-12-19"></span>[49] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. 2023. Improved distribution matching for dataset condensation. In CVPR. 7856–7865.
- <span id="page-12-45"></span>[50] Kai Zhao, Chenjuan Guo, Yunyao Cheng, Peng Han, Miao Zhang, and Bin Yang. 2023. Multiple Time Series Forecasting with Dynamic Graph Modeling. PVLDB 17, 4 (2023), 753–765.
- <span id="page-12-6"></span>[51] Yan Zhao, Xuanhao Chen, Liwei Deng, Tung Kieu, Chenjuan Guo, Bin Yang, Kai Zheng, and Christian S. Jensen. 2022. Outlier Detection for Streaming Task Assignment in Crowdsourcing. In WWW. 1933–1943.
- <span id="page-12-35"></span>[52] Kaiping Zheng, Gang Chen, Melanie Herschel, Kee Yuan Ngiam, Beng Chin Ooi, and Jinyang Gao. 2021. PACE: learning effective task decomposition for human-in-the-loop healthcare delivery. In SIGMOD. 2156–2168.
- <span id="page-12-38"></span>[53] Haoyi Zhou, Shanghang Zhang, Jieqi Peng, Shuai Zhang, Jianxin Li, Hui Xiong, and Wancai Zhang. 2021. Informer: Beyond efficient transformer for long sequence time-series forecasting. In AAAI, Vol. 35. 11106–11115.
- <span id="page-12-8"></span>[54] Tian Zhou, Ziqing Ma, Qingsong Wen, Xue Wang, Liang Sun, and Rong Jin. 2022. Fedformer: Frequency enhanced decomposed transformer for long-term series forecasting. In ICML. 27268–27286.
- <span id="page-12-29"></span>[55] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. 2022. Dataset distillation using neural feature regression. NeurIPS 35 (2022), 9813–9827.

<span id="page-13-0"></span>

# A APPENDIX

| <b>Table 8: Summary of Notation</b> |                                             |
|-------------------------------------|---------------------------------------------|
| Symbol                              | Definition                                  |
| w                                   | Sliding window size                         |
| TRE                                 | Trend                                       |
| SEA                                 | Seasonality                                 |
| T                                   | Time series                                 |
| $t_i$                               | Observation in $T$ at the $i$ -th timestamp |
| $	au$                               | Time series dataset                         |
| M                                   | Length of $	au$                             |
| S                                   | Condensed time series dataset               |
| N                                   | Length of $S$                               |
| $	heta$                             | Parameters learned on $	au$                 |
| $	heta^{	au}$                       | Parameters learned on $S$                   |
| $T_{input}^{c}$                     | Channel-independent time series             |
| B                                   | Expert buffer                               |

## A.1 Preliminaries

A.1.1 Notation. Table [8](#page-13-0) lists notation used throughout the paper.

## A.2 Methodology

Algorithm 3: The TimeDC Framework

<span id="page-13-1"></span>Input: A buffer  $\mathcal B$  with a set of pre-trained trajectories on the original time series dataset  ${\mathcal T}$  parameterized by  $\{\Theta_q^k\}$  $_{\mathcal{T}}^{k}$  ) $_{k=1}^{K}$ ; numbers of two-fold matching steps:  $T_{0}$ ;  $f_{\theta S}$  training steps:  $T_1$ ; initialized condensed time series dataset: S.

Output: Optimized condensed time series dataset: S. 1 while  $\eta$  <  $T_0$  do

- <span id="page-13-2"></span>2 Sample a pre-trained trajectory in  $\mathcal{B}$  based on the curriculum trajectory query (see Algorithm [2\)](#page-6-3);
- <span id="page-13-3"></span>3 calculate the  $L_{tmm}$  according to Equation [9\)](#page-5-2);

```
4 for \gamma < T_1 do
```

```
\mathsf{s} \quad | \quad | \quad \text{Train}\,f_{\theta\mathcal{S}} \text{ via gradient matching and frequency }matching (see Equation 11);
```

- <span id="page-13-5"></span>6  $\theta_{\gamma+1} \leftarrow \tilde{\theta}_{\gamma} - \alpha \nabla (\mathcal{L}(f_{\theta S}, S) + L_{Fre}),$  where  $\alpha$  is the learning rate;
- <span id="page-13-6"></span> $7$  Update the condensed time series dataset S according to Equation [16;](#page-6-8)

```
sreturn\mathcal S
```

A.2.1 Algorithm. The whole process of TimeDC is shown in Algorithm [3,](#page-13-1) where lines [2–](#page-13-2)[3](#page-13-3) cover the curriculum trajectory query and matching, lines [4](#page-13-4)[–6](#page-13-5) cover the training process of  $f_{\theta S}$  and frequency matching, and line [7](#page-13-6) covers the optimization of  $S$ .

## A.3 Experiment

A.3.1 Dataset Statistics. The forecasting and classification dataset statistics are provided in Tables [9](#page-13-7) and [10,](#page-13-8) respectively.

A.3.2 Trade-off between Performance and Efficiency. To study the effect of the number of condensed time series on performance and running time, we conduct experiments with 100, 200, 300, 500, and 800 condensed time series on the Weather and Traffic datasets. The

<span id="page-13-7"></span>

| Dataset       | Feature | Time step | Granularity |
|---------------|---------|-----------|-------------|
| Weather       | 21      | 52696     | 10 minutes  |
| Traffic       | 862     | 17544     | 1 hour      |
| Electricity   | 321     | 26304     | 1 hour      |
| ETTh1 & ETTh2 | 7       | 17420     | 1 hour      |
| ETTm1 & ETTm2 | 7       | 69680     | 15 minutes  |

#### Table 10: Statistics of Classification Datasets

<span id="page-13-8"></span>

| Dataset         | Class | Length | Type                |
|-----------------|-------|--------|---------------------|
| <b>ECG200</b>   | 2     | 96     | Electrocardiography |
| ElectricDevices | 7     | 96     | Device              |
| FordB           | 2     | 500    | Sensor              |

<span id="page-13-13"></span><span id="page-13-10"></span><span id="page-13-9"></span>Image /page/13/Figure/23 description: This figure displays four plots related to the performance and running time of a model on weather and traffic datasets. Plots (a) and (c) show the performance, with 'Loss' on the y-axis and 'The Number of Condensed Time Series' on the x-axis. Both plots include two lines, MAE (Mean Absolute Error) and MSE (Mean Squared Error), plotted against the number of condensed time series (100, 200, 300, 500, 800). Plot (a) 'Performance on Weather' shows MAE values around 4.2, 3.4, 3.0, 2.5, 2.5 and MSE values around 3.8, 3.5, 2.7, 1.8, 2.0. Plot (c) 'Performance on Traffic' shows MAE values around 4.2, 3.4, 3.0, 2.5, 2.5 and MSE values around 4.8, 4.2, 3.8, 3.7, 3.8. Plots (b) and (d) show the running time, with 'Running time (s)' on the y-axis and 'The Number of Condensed Time Series' on the x-axis. Plot (b) 'Running Time on Weather' shows a single line labeled 'Weather' with values approximately 20.5, 21.5, 23.5, 25.0, 25.5. Plot (d) 'Running Time on Traffic' shows a single line labeled 'Traffic' with values approximately 1.2, 1.6, 2.0, 2.4, 2.7. The overall figure is titled 'Figure 10. Tender of between Performance and Efficiency'.

<span id="page-13-14"></span><span id="page-13-11"></span>Figure 10: Trade-off between Performance and Efficiency on Two Datasets

results, shown in Figure [10,](#page-13-9) indicate that the performance initially drops and then either stabilizes (Figure [10\(a\)\)](#page-13-10) or increase slightly (Figure [10\(c\)\)](#page-13-11). In addition, as the number of condensed time series increases, the running time decreases considerably. Generally, the results show that model performance improves with an increase in condensed time series data, but at the cost of an increased training time. When the number of condensed time series is set to 500, TimeDC achieves outstanding performance with an acceptable training time, making 500 an appropriate setting for balancing performance and efficiency.

<span id="page-13-12"></span>Table 11: Comparison between Autoformer $<sub>r</sub>$  and TimeDC on</sub> **Weather**  $(PL = 96)$ 

| Metric | MAE         |        | RMSE        |        |
|--------|-------------|--------|-------------|--------|
| Method | Autoformerr | TimeDC | Autoformerr | TimeDC |
| $ B_0$ | 0.603       | 0.456  | 0.625       | 0.477  |
| $ B_1$ | 0.835       | 0.512  | 1.055       | 0.533  |
| $ I$   | 0.638       | 0.656  | 0.687       | 0.512  |

<span id="page-14-2"></span><span id="page-14-1"></span>Image /page/14/Figure/0 description: This image displays a collection of bar charts comparing the running times of different models (DC, MTT, TimeDC, Autoformer, Informer, Transformer) across various datasets (Traffic, Electricity, ETTh2, ETTm1, ETTm2). Each chart is further categorized by different sequence lengths (PL = 96, PL = 192, PL = 336), represented by yellow, blue, and red bars, respectively. The top row of charts (a-e) shows results for DC, MTT, and TimeDC models on Traffic, Electricity, ETTh2, ETTm1, and ETTm2 datasets. The bottom row of charts (f-j) shows results for Autoformer, Informer, Transformer, and TimeDC models on the same datasets. For example, in chart (a) Traffic, the DC and MTT models with PL=96, PL=192, and PL=336 all have running times around 500 seconds, while the TimeDC model has a running time of approximately 300 seconds for PL=96 and slightly higher for PL=192 and PL=336. In chart (f) Traffic, the Autoformer, Informer, and Transformer models have running times below 100 seconds for all PL values, while the TimeDC model shows running times around 300-350 seconds.

Figure 11: Running Time Comparison

<span id="page-14-4"></span><span id="page-14-0"></span>Table 12: Training Time of TimeDC and Training Time on Original Datasets (s/epoch)

| Dataset     | Condensed Dataset | Original Dataset |
|-------------|-------------------|------------------|
| Weather     | 22.39             | 35.26            |
| Traffic     | 232.34            | 346.76           |
| Electricity | 314.56            | 522.85           |
| ETTh1       | 14.38             | 20.43            |
| ETTh2       | 5.43              | 8.95             |
| ETTm1       | 27.83             | 35.75            |
| ETTm2       | 23.15             | 30.46            |

A.3.3 Performance Comparison on Streaming Data. To assess more comprehensively the effectiveness of TimeDC, we compare it with the replay-based method Autoformer<sub>r</sub> on Weather, which adopts an explicit buffer to store a random subset of the base set  $B$ . When the incremental set  $I$  arrives, we randomly select 500 samples from the buffer and then fuse them with the new data to update the model parameters. The results are shown in Table [11,](#page-13-12) where TimeDC outperforms Autoformer $_r$ , which shows a simple replaybased method is insufficient for time series streaming learning due to the concept drift problems. The results show that TimeDC achieves relatively stable performance as well as notably better performance on the base and incremental sets in five out of six cases.

A.3.4 Training Time on Dataset Condensation and Original Datasets. We compare the training time of TimeDC and training time on original datasets based on the stacked TSOperators in terms of an epoch, as shown in Table [12.](#page-14-0) The results indicate that the training time of TimeDC is significantly lower than training on the original dataset. For example, the training time of TimeDC for dataset condensation is reduced by 39.84% compared to that of training a model on the original dataset on Electricity. Additionally, Table 1 shows that the model trained on the condensed dataset performs comparably to the model trained on the original dataset. Thus, TimeDC not only reduces the training time to achieve convergence for dataset condensation but also maintains good performance, showing the

<span id="page-14-5"></span><span id="page-14-3"></span>efficiency and practicality of time series dataset condensation, especially for resource-intensive tasks.

A.3.5 Running Time Efficiecy. As efficiency is important in dataset condensation to enable scalability, especially on resource-constrained edge computing devices, we study the training time (of an epoch) for the condensation methods and different architectures. We conduct experiments on five datasets, i.e., Traffic, Electricity, ETTh2, ETTm1, and ETTm2, as shown in Figure [11.](#page-14-1) Overall, TimeDC consumes the least training time in most cases.

We study the training time (of an epoch) for the condensation methods. Figures [11\(a\)–](#page-14-2)[11\(e\)](#page-14-3) report the training time on Traffic, Electricity, ETTh2, ETTm1, and ETTm2. We see that the training time of TimeDC is below those of DC and MTT, which is largely because of the expert buffer in the CT2M module that stores precomputed trajectories. This indicates the feasibility of TimeDC for model deployment in large time series dataset reduction scenarios.

We also study training time across different network architectures on Traffic, Electricity, ETTh2, ETTm1, and ETTm2—see Figures [11\(f\)–](#page-14-4)[11\(j\).](#page-14-5) Here, TimeDC consumes the least training time in most cases. TimeDC is faster than the other methods due to its patching mechanism, which reduces the complexity of the selfattention mechanism through input data simplification. However, TimeDC consumes more training time on Traffic and Electricity. This is because Traffic and Electricity have many more features than the other datasets—862 versus 321 features. The proposed TSOperators require more training time to learn these features simultaneously because of the channel-independent mechanism. Nonetheless, TimeDC achieves much better performance on Traffic and Electricity, indicating its ability to learn correlations across different channels.

A.3.6 Training Time of TimeDC and Its Variants. We report the training time of TimeDC and its variants in Table [13.](#page-15-0) The training time of w/o\_patch is much lower than those of the other variants, primarily due to the patch construction and storage in the patching mechanism. However, the patching mechanism enables the

<span id="page-15-1"></span>Image /page/15/Figure/0 description: This image displays six line graphs arranged in a 2x3 grid, each comparing Mean Absolute Error (MAE) and Mean Squared Error (MSE) across different datasets. The x-axis for all graphs represents a numerical range from 100 to 800, while the y-axis is labeled 'Loss'. The top row features graphs labeled (a) Random, (b) K-Center, and (c) Herding. The bottom row contains graphs labeled (d) DC, (e) MTT, and (f) TimeDC. In graph (a) Random, MAE values range from approximately 0.88 to 0.7, and MSE values range from approximately 1.3 to 1.2. For graph (b) K-Center, MAE ranges from about 0.55 to 0.42, and MSE ranges from about 0.78 to 0.65. Graph (c) Herding shows MAE from roughly 0.62 to 0.43, and MSE from approximately 0.8 to 0.65. Graph (d) DC presents MAE values from about 4.8e-1 to 3.6e-1, and MSE values from approximately 6.4e-1 to 4.8e-1. In graph (e) MTT, MAE ranges from about 3.8e-1 to 2.7e-1, and MSE ranges from approximately 3.3e-1 to 2.2e-1. Finally, graph (f) TimeDC shows MAE from roughly 4.3e-1 to 2.5e-1, and MSE from about 3.8e-1 to 2.2e-1.

Figure 12: Effect of the Size of Condensed TS Datasets across Different Methods on Weather

modeling of local semantics, which enables the model to process longer historical sequences to improve the feature extraction, thus enabling better model performance.

<span id="page-15-0"></span>Table 13: Training Time of TimeDC and Its Variants (s/epoch)

| Dataset     | <i>w/o_Patch</i> | <i>w/o_DDFM</i> | <i>w/o_CT2M</i> | TimeDC |
|-------------|------------------|-----------------|-----------------|--------|
| Weather     | 20.34            | 22.44           | 33.4            | 22.84  |
| Traffic     | 50.45            | 278.74          | 510.4           | 314.56 |
| Electricity | 60.43            | 201.45          | 331.32          | 232.34 |
| ETTh1       | 15.68            | 16.45           | 25.64           | 18.75  |

A.3.7 Effect of the Size of Condensed TS Datasets across Different Methods. We study the effect of the size of the condensed time series (TS) dataset across different methods in Figure [12.](#page-15-1) We observe that the performance curves drop significantly in most cases. This shows that the model performance improves with a larger condensed time series dataset. This increase in performance is attributed to the availability of more training data containing valuable knowledge. It is noteworthy that TimeDC shows a slight decrease in performance when using 800 condensed time series compared to 500 condensed time series on the Weather dataset. This may be because the patterns in these time series are relatively straightforward. Additional condensed time series data might introduce recurring patterns, thereby making the model overfit to these patterns and degrading performance on other data. For detailed results for other datasets, please refer to the repository at [https://github.com/uestc-liuzq/STdistillation.](https://github.com/uestc-liuzq/STdistillation)

A.3.8 Dynamic Tensor Memory Cost. We conduct experiments to compare the memory used by the dynamic (online) tensor across

DC, MTT, and TimeDC on all datasets in Table [14.](#page-15-2) TimeDC is able to significantly alleviate heavy online memory and computation costs thanks to the training trajectories precomputed offline.

<span id="page-15-2"></span>Table 14: Dynamic Tensor Memory Cost on All Datasets

| Dataset     | DC      | MTT      | TimeDC   |
|-------------|---------|----------|----------|
| Weather     | 10.0 GB | 8.9 GB   | 3.3 GB   |
| Traffic     | 17.8 GB | 13.7 GB  | 10.9 GB  |
| Electricity | 8.5 GB  | 932.5 MB | 516.0 MB |
| ETTh1       | 1.9 GB  | 845.5 MB | 280.9 MB |
| ETTh2       | 1.8 GB  | 932.5 MB | 516.1 MB |
| ETTm1       | 1.8 GB  | 932.2 MB | 515.9 MB |
| ETTm2       | 1.7 GB  | 932.5 MB | 516.1 MB |

A.3.9 The Size of Condensed Datasets on Seven Datasets. We report the condensed dataset size for each baseline and TimeDC in Table [15.](#page-16-0) We observe that the dataset sizes are similar across the different baselines because we fix the number of condensed time series at 500 for each method, resulting in minimal variation in the dataset size.

A.3.10 Case Study on Dataset Condensation. To further assess the effectiveness of TimeDC on synthesizing condensed time series datasets that cover the original time series distribution well, we show t-SNE graphs of the original time series dataset and the condensed time series dataset for Traffic, Electricity, ETTh2, ETTm1, and ETTm2. Figure [13](#page-16-1) compares the dataset distributions, where blue and red dots represent the original (i.e., real) and condensed dataset, respectively. We randomly sample 500 time series from the original time series dataset for the visualization of the original time

Table 15: Condensed Dataset Size for Seven Datasets

<span id="page-16-0"></span>

| Dataset     | Random   | K-Center | Herding  | DC       | MTT      | TimeDC   |
|-------------|----------|----------|----------|----------|----------|----------|
| Weather     | 40.3 MB  | 40.3 MB  | 40.3 MB  | 39.8 MB  | 38.6 MB  | 38.5 MB  |
| Traffic     | 1.7 GB   | 1.7 GB   | 1.7 GB   | 1.7 GB   | 1.7 GB   | 1.7 GB   |
| Electricity | 316.3 MB | 308.2 MB | 316.3 MB | 316.3 MB | 310.8 MB | 308.2 MB |
| ETTh1       | 13.4 MB  | 13.4 MB  | 13.4 MB  | 13.3 MB  | 13.4 MB  | 12.8 MB  |
| ETTh2       | 13.3 MB  | 13.4 MB  | 13.4 MB  | 13.4 MB  | 13.4 MB  | 13.4 MB  |
| ETTm1       | 13.4 MB  | 13.4 MB  | 13.4 MB  | 13.4 MB  | 13.4 MB  | 13.4 MB  |
| ETTm2       | 13.4 MB  | 13.4 MB  | 13.4 MB  | 13.4 MB  | 13.4 MB  | 13.4 MB  |

<span id="page-16-1"></span>Image /page/16/Figure/2 description: The image displays five scatter plots, each representing a different dataset: (a) Traffic, (b) Electricity, (c) ETTh2, (d) ETTm1, and (e) ETTm2. Each plot contains two sets of data points, distinguished by color and labeled in a legend at the top left of each plot. Blue dots represent 'Real TS' and red dots represent 'Condensed TS'. The plots show the distribution of these two types of time series data in a 2D space, likely after some dimensionality reduction technique like t-SNE or UMAP.

Figure 13: Dataset Distribution Comparison on Five Datasets

Table 16: Effect of Prediction Length on Weather

<span id="page-16-2"></span>

| Prediction Length | Dynamic Tensor | <b>Training Time</b> |
|-------------------|----------------|----------------------|
| 96                | 3.34 GB        | 20.82 s              |
| 192               | 3.41 GB        | 21.73 s              |
| 336               | 3.48 GB        | 22.39 s              |

series. We observe that the red dots are well integrated with the blue dots, indicating similarity in distribution between the original and condensed datasets. This indicates that the condensed dataset is of high quality and that the condensation method is effective.

A.3.11 Scalability. To assess the scalability of TimeDC, we conduct experiments to study the effect of different prediction lengths on Weather. The results are shown in Table [16.](#page-16-2) With an increase in the prediction length, TimeDC achieves similar dynamic tensor costs

and training times, demonstrating its efficiency and scalability in terms of time series prediction length.

In addition, TimeDC scales with the input dimensionality (i.e., number of features). In particular, the increase in the dynamic tensor and storage costs of TimeDC is significantly smaller than the difference in the number of input features of various datasets in most cases (see Tables 3 and 6). For example, the number of features in Traffic (i.e., 862) is approximately 123 times that of ETTh1 (i.e., 7), but the dynamic tensor and storage costs of TimeDC on Traffic are 12 and 64 times those of ETTh1, respectively.

Further, TimeDC scales with the number of condensed time series. According to Figures [10\(b\)](#page-13-13) and [10\(d\),](#page-13-14) the running time of TimeDC increases approximately linearly with the number of time series.
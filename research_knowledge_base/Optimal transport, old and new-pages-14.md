Particular Case 10.45. If  $M = \mathbb{R}^n$ , formula (10.36) becomes

$$
y = x + \nabla \psi(x) = \nabla \left(\frac{|\cdot|^2}{2} + \psi\right)(x),
$$

where  $\psi$  is  $|\cdot|^2/2$ -convex, or equivalently  $|\cdot|^2/2 + \psi$  is convex lower semicontinuous. So (10.36) can be written  $y = \nabla \Psi(x)$ , where  $\Psi$  is convex lower semicontinuous, and we are back to Theorem 9.4.

# First Appendix: A little bit of geometric measure theory

The geometric counterpart of differentiability is of course the approximation of a set  $S$  by a tangent plane, or hyperplane, or more generally by a tangent d-dimensional space, if  $d$  is the dimension of  $S$ .

If S is smooth, then there is no ambiguity on its dimension (a curve has dimension 1, a surface has dimension 2, etc.) and the tangent space always exists. But if  $S$  is not smooth, this might not be the case, at least not in the usual sense. The notion of tangent cone (sometimes called contingent cone) often remedies this problem; it is naturally associated with the notion of **countable** d-rectifiability, which acts as a replacement for the notion of "being of dimension  $d$ ". I shall recall below some of the basic results about these concepts.

**Definition 10.46 (Tangent cone).** If S is an arbitrary subset of  $\mathbb{R}^n$ , and  $x \in \overline{S}$ , then the tangent cone  $T_xS$  to S at x is defined as

$$
T_xS:=\left\{\lim_{k\to\infty}\frac{x_k-x}{t_k};\quad x_k\in S,\ x_k\to x,\ t_k>0,\ t_k\to 0\right\}.
$$

The dimension of this cone is defined as the dimension of the vector space that it generates.

**Definition 10.47 (Countable rectifiability).** Let S be a subset of  $\mathbb{R}^n$ , and let  $d \in [0,n]$  be an integer. Then S is said to be countably d-rectifiable if  $S \subset \bigcup_{k \in \mathbb{N}} f_k(D_k)$ , where each  $f_k$  is Lipschitz on a measurable subset  $D_k$  of  $\mathbb{R}^d$ . In particular, S has Hausdorff dimension at most d.

The next theorem summarizes two results which were useful in the present chapter:

## Theorem 10.48 (Sufficient conditions for countable rectifiability).

(i) Let S be a measurable set in  $\mathbb{R}^n$ , such that  $T_xS$  has dimension at most d for all  $x \in S$ . Then S is countably d-rectifiable.

(ii) Let S be a measurable set in  $\mathbb{R}^n$ , such that  $T_xS$  is included in a half-space, for each  $x \in \partial S$ . Then  $\partial S$  is countably  $(n-1)$ -rectifiable.

*Proof of Theorem 10.48.* For each  $x \in S$ , let  $\pi_x$  stand for the orthogonal projection on  $T_xS$ , and let  $\pi_x^{\perp} = \text{Id} - \pi_x$  stand for the orthogonal projection on  $(T_xS)^{\perp}$ . I claim that

$$
\forall x \in S, \quad \exists r > 0; \quad \forall y \in S, \quad |x - y| \le r \Longrightarrow |\pi_x^{\perp}(x - y)| \le |\pi_x(x - y)|. \tag{10.37}
$$

Indeed, assume that (10.37) is false. Then there is  $x \in S$ , and there is a sequence  $(y_k)_{k \in \mathbb{N}}$  such that  $|x - y_k| \leq 1/k$  and yet  $|\pi_x^{\perp}(x - y)| >$  $|\pi_x(x - y)|$ , or equivalently

$$
\left|\pi_x^{\perp}\left(\frac{x-y_k}{|x-y_k|}\right)\right| > \left|\pi_x\left(\frac{x-y_k}{|x-y_k|}\right)\right|.\tag{10.38}
$$

Up to extraction of a subsequence,  $w_k := (x - y_k)/|x - y_k|$  converges to  $\theta \in T_xS$  with  $|\theta| = 1$ . Then  $|\pi_x w_k| \to 1$  and  $|\pi_x^{\perp} w_k| \to 0$ , which contradicts (10.38). So (10.37) is true.

Next, for each  $k \in \mathbb{N}$ , let

$$
S_k := \Big\{ x \in S; \text{ property (10.37) holds true for } |x - y| \le 1/k \Big\}.
$$

It is clear that the sets  $S_k$  cover S, so it is sufficient to establish the d-rectifiability of  $S_k$  for a given k.

Let  $\delta > 0$  be small enough  $(\delta < 1/2$  will do). Let  $\Pi_d$  be the set of all orthogonal projections on d-dimensional linear spaces. Since  $\Pi_d$ is compact, we can find a finite family  $(\pi_1, \ldots, \pi_N)$  of such orthogonal projections, such that for any  $\pi \in \Pi_d$  there is  $j \in \{1, ..., N\}$  with  $\|\pi - \pi_i\| \le \delta$ , where  $\|\cdot\|$  stands for the operator norm. So the set  $S_k$ is covered by the sets

$$
S_{k\ell} := \Big\{ x \in S_k; \quad \|\pi_x - \pi_\ell\| \le \delta \Big\}.
$$

To prove (i), it suffices to prove that  $S_{k\ell}$  is locally rectifiable. We shall show that for any two  $x, x' \in S_{k\ell}$ ,

First Appendix: A little bit of geometric measure theory 271

$$
|x - x'| \le \frac{1}{k} \Longrightarrow |\pi_{\ell}^{\perp}(x - x')| \le L |\pi_{\ell}(x - x')|, \qquad L = \frac{1 + 2\delta}{1 - 2\delta}; \tag{10.39}
$$

this will imply that the intersection of  $S_{k\ell}$  with a ball of diameter  $1/k$ is contained in an L-Lipschitz graph over  $\pi_{\ell}(\mathbb{R}^n)$ , and the conclusion will follow immediately.

To prove (10.39), note that, if  $\pi, \pi'$  are any two orthogonal projections, then  $|(\pi^{\perp} - (\pi')^{\perp})(z)| = |(\text{Id} - \pi)(z) - (\text{Id} - \pi')(z)| = |(\pi - \pi')(z)|,$ therefore  $\|\pi^{\perp} - (\pi')^{\perp}\| = \|\pi - \pi'\|$ , and

$$
|\pi_{\ell}^{\perp}(x - x')| \leq |(\pi_{\ell}^{\perp} - \pi_{x}^{\perp})(x - x')| + |\pi_{x}^{\perp}(x - x')|
$$
  

$$
\leq |(\pi_{\ell} - \pi_{x})(x - x')| + |\pi_{x}(x - x')|
$$
  

$$
\leq |(\pi_{\ell} - \pi_{x})(x - x')| + |\pi_{\ell}(x - x')| + |(\pi_{\ell} - \pi_{x})(x - x')|
$$
  

$$
\leq |\pi_{\ell}(x - x')| + 2\delta|x - x'|
$$
  

$$
\leq (1 + 2\delta)|\pi_{\ell}(x - x')| + 2\delta|\pi_{\ell}^{\perp}(x - x')|.
$$

This establishes (10.39), and Theorem 10.48(i).

Now let us turn to part (ii) of the theorem. Let  $F$  be a finite set in  $S^{n-1}$  such that the balls  $(B_{1/8}(\nu))_{\nu \in F}$  cover  $S^{n-1}$ . I claim that

$$
\forall x \in \partial S, \quad \exists r > 0, \quad \exists \nu \in F, \quad \forall y \in \partial S \cap B_r(x), \quad \langle y - x, \nu \rangle \le \frac{|y - x|}{2}.\tag{10.40}
$$

Indeed, otherwise there is  $x \in \partial S$  such that for all  $k \in \mathbb{N}$  and for all  $\nu \in F$  there is  $y_k \in \partial S$  such that  $|y_k - x| \leq 1/k$  and  $\langle y_k - x, \nu \rangle$  $|y_k - x|/2$ . By assumption there is  $\xi \in S^{n-1}$  such that

$$
\forall \zeta \in T_x S, \quad \langle \xi, \zeta \rangle \le 0.
$$

Let  $\nu \in F$  be such that  $|\xi - \nu| < 1/8$  and let  $(y_k)_{k \in \mathbb{N}}$  be a sequence as above. Since  $y_k \in \partial S$  and  $y_k \neq x$ , there is  $y'_k \in S$  such that  $|y_k - y'_k|$  $|y_k - x|/8$ . Then

$$
\langle y'_k - x, \xi \rangle \ge \langle y_k - x, \nu \rangle - |y_k - x| \, |\xi - \nu| - |y - y'_k| \ge \frac{|y_k - x|}{4} \ge \frac{|x - y'_k|}{8}.
$$

So

$$
\left\langle \frac{y_k' - x}{|y_k' - x|}, \xi \right\rangle \ge \frac{1}{8}.\tag{10.41}
$$

Up to extraction of a subsequence,  $(y'_k - x)/|y'_k - x|$  converges to some  $\zeta \in T_xS$ , and then by passing to the limit in (10.41) we have  $\langle \zeta, \xi \rangle \geq$  1/8. But by definition,  $\xi$  is such that  $\langle \zeta,\xi \rangle \leq 0$  for all  $\zeta \in T_xS$ . This contradiction establishes (10.40).

As a consequence,  $\partial S$  is included in the union of all sets  $A_{1/k,\nu}$ , where  $k \in \mathbb{N}$ ,  $\nu \in F$ , and

$$
A_{r,\nu} := \left\{ x \in \partial S; \quad \forall y \in \partial S \cap B_r(x), \quad \langle y - x, \nu \rangle \le \frac{|y - x|}{2} \right\}.
$$

To conclude the proof of the theorem it is sufficient to show that each  $A_{r,\nu}$  is locally the image of a Lipschitz function defined on a subset of an  $(n-1)$ -dimensional space.

So let  $r > 0$  and  $\nu \in F$  be given, let  $x_0 \in A_{r,\nu}$ , and let  $\pi$  be the orthogonal projection of  $\mathbb{R}^n$  to  $\nu^{\perp}$ . (Explicitly,  $\pi(x) = x - \langle x, \nu \rangle \nu$ .) We shall show that on  $D := A_{r,\nu} \cap B_{r/2}(x_0)$ ,  $\pi$  is injective and its inverse (on  $\pi(D)$ ) is Lipschitz. To see this, first note that for any two  $x, x' \in D$ , one has  $x' \in B_r(x)$ , so, by definition of  $A_{r,\nu}$ ,  $\langle x'-x,\nu \rangle \leq |x'-x|/2$ . By symmetry, also  $\langle x - x', \nu \rangle \leq |x - x'|/2$ , so in fact

$$
\left| \langle x - x', \nu \rangle \right| \le \frac{|x - x'|}{2}.
$$

Then if  $z = \pi(x)$  and  $z' = \pi(x')$ ,

$$
|x - x'| \le |z - z'| + |\langle x, \nu \rangle - \langle x', \nu \rangle| \le |z - z'| + \frac{|x - x'|}{2},
$$

so  $|x-x'| \leq 2|z-z'|$ . This concludes the proof. □

$$
\Box
$$

# Second Appendix: Nonsmooth implicit function theorem

Let M be an n-dimensional smooth Riemannian manifold, and  $x_0 \in M$ . I shall say that a set  $M' \subset M$  is a k-dimensional  $C^r$  graph (resp. kdimensional Lipschitz graph) in a neighborhood of  $x_0$  if there are

(i) a smooth system of coordinates around  $x_0$ , say

$$
x = \zeta(x', y),
$$

where  $\zeta$  is a smooth diffeomorphism from an open subset of  $\mathbb{R}^k \times \mathbb{R}^{n-k}$ , into a neighborhood O of  $x_0$ ;

(ii) a  $C^r$  (resp. Lipschitz) function  $\varphi: O' \to \mathbb{R}^{n-k}$ , where  $O'$  is an open subset of  $\mathbb{R}^k$ ;

such that for all  $x \in O$ ,

$$
x \in M' \iff y = \varphi(x').
$$

This definition is illustrated by Figure 10.2.

Image /page/4/Figure/5 description: The image depicts a diagram illustrating a mapping from R^k to a manifold M. On the left, a graph shows a curve labeled 'phi' plotted against axes labeled R^n-k and R^k. A dashed arrow, labeled 'zeta', originates from the vicinity of the graph and points towards the manifold M, which is represented as a curved surface with a curve drawn on it. An arrow indicates the direction of the mapping.

Fig. 10.2. k-dimensional graph

The following statement is a consequence of the classical implicit function theorem: If  $f : M \to \mathbb{R}$  is of class  $C^r$   $(r \ge 1)$ ,  $f(x_0) = 0$  and  $\nabla f(x_0) \neq 0$ , then the set  $\{f = 0\} = f^{-1}(0)$  is an  $(n-1)$ -dimensional  $C<sup>r</sup>$  graph in a neighborhood of  $x_0$ .

In this Appendix I shall consider a nonsmooth version of this theorem. The following notion will be useful.

**Definition 10.49 (Clarke subdifferential).** Let f be a continuous real-valued function defined on an open subset U of a Riemannian manifold. For each  $x \in U$ , define  $\partial f(x)$  as the convex hull of all limits of sequences  $\nabla f(x_k)$ , where all  $x_k$  are differentiability points of f and  $x_k \rightarrow x$ . In short:

$$
\partial f(x) = \overline{\text{Conv}} \left\{ \lim_{x_k \to x} \nabla f(x_k) \right\}.
$$

Here comes the main result of this Appendix. If  $(A_i)_{1 \leq i \leq m}$  are subsets of a vector space, I shall write  $\sum A_i = \{\sum_i a_i; \ a_i \in A_i\}.$ 

Theorem 10.50 (Nonsmooth implicit function theorem). Let  $(f_i)_{1 \leq i \leq m}$  be real-valued Lipschitz functions defined in an open set U of an n-dimensional Riemannian manifold, and let  $x_0 \in U$  be such that:

$$
(a) \sum f_i(x_0) = 0;
$$

(b) 
$$
0 \notin \sum \partial f_i(x_0)
$$
.

Then  $\{\sum f_i = 0\}$  is an  $(n-1)$ -dimensional Lipschitz graph around  $x_0$ .

**Remark 10.51.** Let  $\psi$  be a convex continuous function defined around some point  $x_0 \in \mathbb{R}^n$ , and let  $p \in \mathbb{R}^n$  such that p does not belong to the Clarke differential of  $\psi$  at  $x_0$ ; then 0 does not belong to the Clarke differential of  $\tilde{\psi}: x \longmapsto \psi(x) - \psi(x_0) - p \cdot (x - x_0) + |x - x_0|^2$  at  $x_0$ , and Theorem 10.50 obviously implies the existence of  $x \neq x_0$  such that  $\psi(x) = 0$ , in particular  $\psi(x) < \psi(x_0) + p \cdot (x-x_0)$ . So p does not belong to the subdifferential of  $\psi$  at  $x_0$ . In other words, the subdifferential is included in the Clarke differential. The other inclusion is obvious, so both notions coincide. This justifies a posteriori the notation  $\partial \psi$  used in Definition 10.49. An easy localization argument shows that similarly, for any locally semiconvex function  $\psi$  defined in a neighborhood of x,  $\partial \psi(x) = \nabla^- \psi(x)$ .

Corollary 10.52 (Implicit function theorem for two subdifferentiable functions). Let  $\psi$  and  $\psi$  be two locally subdifferentiable functions defined in an open set U of an n-dimensional Riemannian manifold M, and let  $x_0 \in U$  be such that  $\psi$ ,  $\psi$  are differentiable at  $x_0$ , and

$$
\psi(x_0) = \widetilde{\psi}(x_0); \qquad \nabla \psi(x_0) \neq \nabla \widetilde{\psi}(x_0).
$$

Then there is a neighborhood V of  $x_0$  such that  $\{\psi = \widetilde{\psi}\}\cap V$  is an  $(n-1)$ -dimensional Lipschitz graph; in particular, it has Hausdorff dimension exactly  $n-1$ .

*Proof of Corollary 10.52.* Let  $f_1 = \psi$ ,  $f_2 = -\tilde{\psi}$ . Since  $f_1$  is locally subdifferentiable and  $f_2$  is locally superdifferentiable, both functions are Lipschitz in a neighborhood of  $x_0$  (Theorem 10.8(iii)). Moreover,  $\nabla f_1$  and  $\nabla f_2$  are continuous on their respective domain of definition; so  $\partial f_i(x_0) = \{\nabla f_i(x_0)\}\ (i = 1, 2)$ . Then by assumption  $\sum \partial f_i(x_0) =$  $\{\sum \nabla f_i(x_0)\}\)$  does not contain 0. The conclusion follows from Theorem 10.50. □

Proof of Theorem 10.50. The statement is purely local and invariant under  $C^1$  diffeomorphism, so we might pretend to be working in  $\mathbb{R}^n$ .

For each *i*,  $\partial f_i(x_0) \subset B(0, ||f_i||_{\text{Lip}}) \subset \mathbb{R}^n$ , so  $\partial f_i(x_0)$  is a compact convex subset of  $\mathbb{R}^n$ ; then also  $\sum \partial f_i(x_0)$  is compact and convex, and by assumption does not contain 0. By the Hahn–Banach theorem, there are  $v \in \mathbb{R}^n$  and  $\alpha > 0$  such that

$$
\langle p, v \rangle \ge \alpha
$$
 for all  $p \in \sum \partial f_i(x_0)$ . (10.42)

So there is a neighborhood V of  $x_0$  such that  $\sum \langle \nabla f_i(x), v \rangle \ge \alpha/2$ at all points  $x \in V$  where all functions  $f_i$  are differentiable. (Other- $\sum \langle \nabla f_i(x_k), v \rangle < \alpha/2$ , but then up to extraction of a subsequence we wise there would be a sequence  $(x_k)_{k\in\mathbb{N}}$ , converging to  $x_0$ , such that would have  $\nabla f_i(x_k) \to p_i \in \partial f_i(x_0)$ , so  $\langle \sum p_i, v \rangle \leq \alpha/2 < \alpha$ , which would contradict (10.42).)

Without loss of generality, we may assume that  $x_0 = 0, v =$  $(e_1, 0, \ldots, 0), V = (-\beta, \beta) \times B(0, r_0),$  where the latter ball is a subset of  $\mathbb{R}^{n-1}$  and  $r_0 \leq (\alpha \beta)/(4 \sum ||f_i||_{\text{Lip}})$ . Further, let

$$
Z' := \Big\{ y \in B(0, r_0) \subset \mathbb{R}^{n-1};
$$
  
$$
\lambda_1 \big[ \{ t \in (-\beta, \beta); \ \exists i; \ \nabla f_i(t, y) \text{ does not exist} \} \big] > 0 \Big\},
$$

and

$$
Z = (-\beta, \beta) \times Z'; \qquad D = V \setminus Z.
$$

I claim that  $\lambda_n[Z] = 0$ . To prove this it is sufficient to check that  $\lambda_{n-1}[Z'] = 0$ . But Z' is the nonincreasing limit of  $(Z'_{\ell})_{\ell \in \mathbb{N}}$ , where

$$
Z'_{\ell} = \left\{ y \in B(0, r_0);
$$
  
$$
\lambda_1 \left[ \{ t \in (-\beta, \beta); \exists i; \nabla f_i(t, y) \text{ does not exist} \} \right] \ge 1/\ell \right\}.
$$

By Fubini's theorem,

$$
\lambda_n\Big[\big\{x\in O;\ \nabla f_i(x)\text{ does not exist for some }i\big\}\Big]\geq (\lambda_{n-1}[Z'_\ell])\times(1/\ell);
$$

and the left-hand side is equal to 0 since all  $f_i$  are differentiable almost everywhere. It follows that  $\lambda_{n-1}[Z'_{\ell}] = 0$ , and by taking the limit  $\ell \to \infty$ we obtain  $\lambda_{n-1}[Z'] = 0$ .

Let  $f = \sum f_i$ , and let  $\partial_1 f = \langle \nabla f, v \rangle$  stand for its partial derivative with respect to the first coordinate. The first step of the proof has shown that  $\partial_1 f(x) \ge \alpha/2$  at each point x where all functions  $f_i$  are

differentiable. So, for each  $y \in B(0,r_0) \setminus Z'$ , the function  $t \to f(t,y)$ is Lipschitz and differentiable  $\lambda_1$ -almost everywhere on  $(-\beta, \beta)$ , and it satisfies  $f'(t, y) \ge \alpha/2$ . Thus for all  $t, t' \in (-\beta, \beta)$ ,

$$
t < t' \Longrightarrow f(t', y) - f(t, y) \ge (\alpha/2) (t' - t). \tag{10.43}
$$

This holds true for all  $((t, y), (t', y))$  in  $D \times D$ . Since  $Z = V \setminus D$ has zero Lebesgue measure,  $D$  is dense in  $V$ , so  $(10.43)$  extends to all  $((t, y), (t', y)) \in V$ .

For all  $y \in B(0, r_0)$ , inequality (10.43), combined with the estimate

$$
|f(0, y)| = |f(0, y) - f(0, 0)| \le ||f||_{\text{Lip}} |y| \le \frac{\alpha \beta}{4},
$$

guarantees that the equation  $f(t, y) = 0$  admits exactly one solution  $t = \varphi(y)$  in  $(-\beta, \beta)$ .

It only remains to check that  $\varphi$  is Lipschitz on  $B(0,r_0)$ . Let  $y, z \in$  $B(0, r_0)$ , then  $f(\varphi(y), y) = f(\varphi(z), z) = 0$ , so

$$
\big|f(\varphi(y),y) - f(\varphi(z),y)\big| = \big|f(\varphi(z),z) - f(\varphi(z),y)\big|.\tag{10.44}
$$

Since the first partial derivative of f is no less than  $\alpha/2$ , the left-hand side of (10.44) is bounded below by  $(\alpha/2)|\varphi(y)-\varphi(z)|$ , while the righthand side is bounded above by  $||f||_{\text{Lip}} |z - y|$ . The conclusion is that

$$
|\varphi(y) - \varphi(z)| \le \frac{2 \|f\|_{\text{Lip}}}{\alpha} |z - y|,
$$

so  $\varphi$  is indeed Lipschitz. □

# Third Appendix: Curvature and the Hessian of the squared distance

The practical verification of the uniform semiconcavity of a given cost function  $c(x, y)$  might be a very complicated task in general. In the particular case when  $c(x, y) = d(x, y)^2$ , this problem can be related to the sectional curvature of the Riemannian manifold. In this Appendix I shall recall some results about these links, some of them well-known, other ones more confidential. The reader who does not know about sectional curvature can skip this Appendix, or take a look at Chapter 14 first.

[]

If  $M = \mathbb{R}^n$  is the Euclidean space, then  $d(x, y) = |x - y|$  and there is the very simple formula

$$
\nabla_x^2 \left( \frac{|x - y|^2}{2} \right) = I_n,
$$

where the right-hand side is just the identity operator on  $T_x \mathbb{R}^n = \mathbb{R}^n$ .

If M is an arbitrary Riemannian manifold, there is no simple formula for the Hessian  $\nabla_x^2 d(x, y)^2/2$ , and this operator will in general not be defined in the sense that it can take eigenvalues  $-\infty$  if x and  $y$  are conjugate points. However, as we shall now see, there is still a recipe to estimate  $\nabla_x^2 d(x,y)^2/2$  from above, and thus derive estimates of semiconcavity for  $d^2/2$ .

So let x and y be any two points in M, and let  $\gamma$  be a minimizing geodesic joining y to x, parametrized by arc length; so  $\gamma(0) = y$ ,  $\gamma(d(x,y)) = x$ . Let  $H(t)$  stand for the Hessian operator of  $x \to$  $d(x,y)^2/2$  at  $x = \gamma(t)$ . (The definition of this operator is recalled and discussed in Chapter 14.) On  $[0, d(x, y))$  the operator  $H(t)$  is welldefined (since the geodesic is minimizing, it is only at  $t = d(x, y)$  that eigenvalues  $-\infty$  may appear). It starts at  $H(0) =$  Id and then its eigenvectors and eigenvalues vary smoothly as t varies in  $(0, d(x, y))$ .

The unit vector  $\dot{\gamma}(t)$  is an eigenvector of  $H(t)$ , associated with the eigenvalue  $+1$ . The problem is to bound the eigenvalues in the orthogonal subspace  $S(t) = (\dot{\gamma})^{\perp} \subset T_{\gamma(t)}M$ . So let  $(e_2, \ldots, e_n)$  be an orthonormal basis of  $S(0)$ , and let  $(e_2(t),...,e_n(t))$  be obtained by parallel transport of  $(e_2, \ldots, e_n)$  along  $\gamma$ ; for any t this remains an orthonormal basis of  $S(t)$ . To achieve our goal, it is sufficient to bound above the quantities  $h(t) = \langle H(t) \cdot e_i(t), e_i(t) \rangle_{\gamma(t)}$ , where i is arbitrary in  $\{2,\ldots,n\}$ .

Since  $H(0)$  is the identity, we have  $h(0) = 1$ . To get a differential equation on  $h(t)$ , we can use a classical computation of Riemannian geometry, about the Hessian of the *distance* (not squared!): If  $k(t) =$  $\langle \nabla_x^2 d(y, x) \cdot e_i(t), e_i(t) \rangle_{\gamma(t)}$ , then

$$
\dot{k}(t) + k(t)^2 + \sigma(t) \le 0,\t(10.45)
$$

where  $\sigma(t)$  is the sectional curvature of the plane generated by  $\dot{\gamma}(t)$  and  $e_i(t)$  inside  $T_{\gamma(t)}M$ . To relate  $k(t)$  and  $h(t)$ , we note that

$$
\nabla_x \left( \frac{d(y,x)^2}{2} \right) = d(y,x) \, \nabla_x d(y,x);
$$

278 10 Solution of the Monge problem II: Local approach

$$
\nabla_x^2 \left( \frac{d(y,x)^2}{2} \right) = d(y,x) \, \nabla_x^2 d(y,x) + \nabla_x d(x,y) \otimes \nabla_x d(x,y).
$$

By applying this to the tangent vector  $e_i(t)$  and using the fact that  $\nabla_x d(x, y)$  at  $x = \gamma(t)$  is just  $\dot{\gamma}(t)$ , we get

$$
h(t) = d(y, \gamma(t)) k(t) + \langle \dot{\gamma}(t), e_i(t) \rangle^2 = t k(t).
$$

Plugging this into (10.45) results in

$$
t\,\dot{h}(t) - h(t) + h(t)^2 \le -t^2\,\sigma(t). \tag{10.46}
$$

From (10.46) follow the two comparison results which were used in Theorem 10.41 and Corollary 10.44:

(a) Assume that the sectional curvatures of  $M$  are all non**negative.** Then (10.46) forces  $h \leq 0$ , so h remains bounded above by 1 for all times. In short:

nonnegative sectional curvature  $\implies$ x  $\int d(x,y)^2$ 2  $\setminus$  $\leq$  Id  $_{T_xM}$ . (10.47)

(If we think of the Hessian as a bilinear form, this is the same as  $\nabla_x^2(d(x,y)^2/2) \leq g$ , where g is the Riemannian metric.) Inequality (10.47) is rigorous if  $d(x, y)^2/2$  is twice differentiable at x; otherwise the conclusion should be reinterpreted as

$$
x \to \frac{d(x,y)^2}{2}
$$
 is semiconcave with a modulus  $\omega(r) = \frac{r^2}{2}$ .

(b) Assume now that the sectional curvatures at point  $x$  are bounded below by  $-C/d(x_0, x)^2$ , where  $x_0$  is an arbitrary point. In this case I shall say that M has asymptotically nonnegative curvature. Then if y varies in a compact subset, we have a lower bound like  $\sigma(t) \geq$  $-C'/d(y, x)^2 = -C'/t^2$ , where C' is some positive constant. So (10.46) implies the differential inequality

$$
t\,\dot{h}(t) \leq C' + h(t) - h(t)^2.
$$

If  $h(t)$  ever becomes greater than  $\overline{C} := (1 + \sqrt{1 + 4C'^2})/2$ , then the right-hand side becomes negative; so h can never go above  $\overline{C}$ . The conclusion is that
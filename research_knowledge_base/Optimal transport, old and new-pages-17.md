## Smoothness

The smoothness of the optimal transport map may give information about its qualitative behavior, as well as simplify computations. So it is natural to investigate the regularity of this map.

What characterizes the optimal transport map  $T$  is the existence of a c-convex  $\psi$  such that (10.20) (or (10.23)) holds true; so it is natural to search for a closed equation on  $\psi$ .

To guess the equation, let us work formally without being too demanding about regularity issues. We shall assume that  $x$  and  $y$  vary in  $\mathbb{R}^n$ , or in nice subsets of smooth *n*-dimensional Riemannian manifolds. Let  $\mu(dx) = f(x) \text{ vol}(dx)$  and  $\nu(dy) = g(y) \text{ vol}(dy)$  be two absolutely continuous probability measures, let  $c(x, y)$  be a smooth cost function, and let  $T$  be a Monge transport. The differentiation of  $(10.20)$  with respect to  $x$  (once again) leads to

$$
\nabla^2 \psi(x) + \nabla^2_{xx} c(x, T(x)) + \nabla^2_{xy} c(x, T(x)) \cdot \nabla T(x) = 0,
$$

which can be rewritten

$$
\nabla^2 \psi(x) + \nabla^2_{xx} c(x, T(x)) = -\nabla^2_{xy} c(x, T(x)) \cdot \nabla T(x). \tag{12.1}
$$

The expression on the left-hand side is the Hessian of the function  $c(x', T(x)) + \psi(x')$ , considered as a function of x' and then evaluated at x. Since this function has a minimum at  $x' = x$ , its Hessian is nonnegative, so the left-hand side of (12.1) is a nonnegative symmetric operator; in particular its determinant is nonnegative. Take absolute values of determinants on both sides of (12.1):

$$
\det\left(\nabla^2\psi(x) + \nabla^2_{xx}c(x,T(x))\right) = \left|\det \nabla^2_{xy}c(x,T(x))\right| |\det(\nabla T(x))|.
$$

Then the Jacobian determinant in the right-hand side can be replaced by  $f(x)/g(T(x))$ , and we arrive at the basic **partial differential equa**tion of optimal transport:

$$
\det(\nabla^2 \psi(x) + \nabla_{xx}^2 c(x, T(x))) = |\det \nabla_{xy}^2 c(x, T(x))| \frac{f(x)}{g(T(x))}. \tag{12.2}
$$

This becomes a closed equation on  $\psi$  in terms of f and g, if one recalls from (10.20) that

$$
T(x) = (\nabla_x c)^{-1} (x, -\nabla \psi(x)), \qquad (12.3)
$$

where the inverse is with respect to the  $y$  variable.

Remark 12.1. In a genuinely Riemannian context, equation (12.2) at first sight does not need to make sense: change the metric close to  $y = T(x)$  and not close to x, then the left-hand side is invariant but the right-hand side seems to change! This is a subtle illusion: indeed, if the metric is changed close to  $y$ , then the volume measure also, and the density  $g$  has to be modified too. All in all, the right-hand side in (12.2) is invariant.

Now what can be said of (12.2)? Unfortunately not much simplification can be expected, except in special cases. The most important of them is the quadratic cost function, or equivalently  $c(x,y) = -x \cdot y$  in  $\mathbb{R}^n$ . Then  $(12.2)$ – $(12.3)$  reduces to

$$
\det \nabla^2 \psi(x) = \frac{f(x)}{g(\nabla \psi(x))}.
$$
 (12.4)

This is an instance of the Monge–Ampère equation, well-known in the theory of partial differential equations. By extension, the system  $(12.2)$ – $(12.3)$  is also called a generalized Monge–Ampère equation.

At this point we may hope that the theory of partial differential equations will help our task quite a bit by providing regularity results for the optimal map in the Monge–Kantorovich problem, at least if we rule out cases where the map is trivially discontinuous (for instance if the support of the initial measure  $\mu$  is connected, while the support of the final measure  $\nu$  is not).

However, things are not so simple. As the next few sections will demonstrate, regularity can hold only under certain stringent assumptions on the geometry and the cost function. The identification of these conditions will introduce us to a beautiful chapter of the theory of fully nonlinear partial differential equations; but one sad conclusion will be that optimal transport is in general not smooth  $-$  even worse, smoothness requires nonlocal conditions which are probably impossible to check effectively, say on a generic Riemannian manifolds. So if we still want to use optimal transport in rather general situations, we'd better find ways to do without smoothness. It is actually a striking feature of optimal transport that this theory can be pushed very far with so little regularity available.

In this chapter I shall first study various counterexamples to identify obstructions to the regularity of the optimal transport, and then discuss some positive results. The following elementary lemma will be useful:

**Lemma 12.2.** Let  $(\mathcal{X}, \mu)$  and  $(\mathcal{Y}, \nu)$  be any two Polish probability spaces, let T be a continuous map  $\mathcal{X} \to \mathcal{Y}$ , and let  $\pi = (\mathrm{Id}, T)_{\#}\mu$ be the associated transport map. Then, for each  $x \in S$  pt  $\mu$ , the pair  $(x,T(x))$  belongs to the support of  $\pi$ .

*Proof of Lemma 12.2.* Let x and  $\varepsilon > 0$  be given. By continuity of T, there is  $\delta > 0$  such that  $T(B_{\delta}(x)) \subset B_{\varepsilon}(T(x))$ . Without loss of generality,  $\delta \leq \varepsilon$ . Then

$$
\pi[B_{\varepsilon}(x) \times B_{\varepsilon}(T(x))] = \mu[\{z \in \mathcal{X}; z \in B_{\varepsilon}(x) \text{ and } T(z) \in B_{\varepsilon}(T(x))\}]
$$
  
$$
\geq \mu[B_{\varepsilon}(x) \cap B_{\delta}(x)] = \mu[B_{\delta}(x)] > 0.
$$

Since  $\varepsilon$  is arbitrarily small, this shows that  $\pi$  attributes positive measure to any neighborhood of  $(x, T(x))$ , which proves the claim. □

### Caffarelli's counterexample

Caffarelli understood that regularity results for  $(12.2)$  in  $\mathbb{R}^n$  cannot be obtained unless one adds an assumption of convexity of the support  $of \nu$ . Without such an assumption, the optimal transport may very well be discontinuous, as the next counterexample shows.

Theorem 12.3 (An example of discontinuous optimal transport). There are smooth compactly supported probability densities f and g on  $\mathbb{R}^n$ , such that the supports of f and g are smooth and connected, f and g are (strictly) positive in the interior of their respective

supports, and yet the optimal transport between  $\mu(dx) = f(x) dx$  and  $\nu(dy) = g(y) dy$ , for the cost  $c(x, y) = |x - y|^2$ , is discontinuous.

*Proof of Theorem 12.3.* Let  $f$  be the indicator function of the unit ball B in  $\mathbb{R}^2$  (normalized to be a probability measure), and let  $g = g_{\varepsilon}$  be the (normalized) indicator function of a set  $C_{\varepsilon}$  obtained by first separating the ball into two halves  $B_1$  and  $B_2$  (say with distance 2), then building a thin bridge between those two halves, of width  $O(\varepsilon)$ . (See Figure 12.1.) Let also g be the normalized indicator function of  $B_1 \cup B_2$ : this is the limit of  $g_{\varepsilon}$  as  $\varepsilon \downarrow 0$ . It is not difficult to see that  $g_{\varepsilon}$  (identified with a probability measure) can be obtained from  $f$  by a continuous deterministic transport (after all, one can deform B continuously into  $C_{\varepsilon}$ ; just think that you are playing with clay, then it is possible to massage the ball into  $C_{\varepsilon}$ , without tearing off). However, we shall see here that for  $\varepsilon$  small enough, the optimal transport *cannot be continuous*.

Image /page/3/Figure/3 description: The image depicts a diagram illustrating the interaction of light or particles with a spherical object labeled 'S'. Two semi-circular regions, labeled 'S-' on the left and 'S+' on the right, are positioned symmetrically on either side of 'S'. Horizontal lines extend from the edges of 'S-' and 'S+' towards 'S', with double-headed arrows indicating a bidirectional flow or interaction. Dashed lines with arrows originate from the top and bottom edges of 'S' and extend outwards, suggesting a scattering or emission pattern. A horizontal bar passes through the center of 'S', extending to the left and right, and is intersected by the dashed lines. The regions 'S-' and 'S+' are shaded with parallel lines, as is the central spherical object 'S'. A vertical dashed line passes through the center of 'S', indicating an axis of symmetry.

Fig. 12.1. Principle behind Caffarelli's counterexample. The optimal transport from the ball to the "dumb-bells" has to be discontinuous, and in effect splits the upper region S into the upper left and upper right regions  $S_-\,$  and  $S_+$ . Otherwise, there should be some transport along the dashed lines, but for some lines this would contradict monotonicity.

The proof will rest on the stability of optimal transport: If T is the unique optimal transport between  $\mu$  and  $\nu$ , and  $T_{\varepsilon}$  is an optimal transport betwen  $\mu$  and  $\nu_{\varepsilon}$ , then  $T_{\varepsilon}$  converges to T in  $\mu$ -probability as  $\varepsilon \downarrow 0$  (Corollary 5.23).

In the present case, choosing  $\mu(dx) = f(x) dx$ ,  $\nu(dy) = g(y) dy$ ,  $c(x,y) = |x-y|^2$ , it is easy to figure out that the unique optimal transport T is the one that sends  $(x, y)$  to  $(x - 1, y)$  if  $x < 0$ , and to  $(x + 1, y)$  if  $x > 0$ .

Let now  $S$ ,  $S_$  and  $S_+$  be the upper regions in the ball, the left half-ball and the right half-ball, respectively, as in Figure 12.1. As a consequence of the convergence in probability, for  $\varepsilon$  small enough, a large fraction (say 0.99) of the mass in S has to go to  $S_$  (if it lies on the left) or to  $S_+$  (if it lies on the right). Since the continuous image of a connected set is itself connected, there have to be some points in  $T_{\varepsilon}(S)$ that form a path going from  $S_$  to  $S_+$ ; and so there is some  $x \in S$  such that  $T_{\varepsilon}(x)$  is close to the left-end of the tube joining the half-balls, in particular  $T_{\varepsilon}(x) - x$  has a large downward component. From the convergence in probability again, many of the neighbors  $\tilde{x}$  of x have to be transported to, say,  $S_$ , with nearly horizontal displacements  $T(\tilde{x}) \widetilde{x}$ . If such an  $\widetilde{x}$  is picked below x, we shall have  $\langle x-\widetilde{x},T(x)-T(\widetilde{x})\rangle < 0$ ; or equivalently,  $|x - T(x)|^2 + |\tilde{x} - T(\tilde{x})|^2 > |x - T(\tilde{x})|^2 + |\tilde{x} - T(x)|^2$ . If  $T$  is continuous, in view of Lemma 12.2 this contradicts the  $c$ -cyclical monotonicity of the optimal coupling. The conclusion is that when  $\varepsilon$  is small enough, the optimal map  $T_{\varepsilon}$  is discontinuous.

The maps  $f$  and  $g$  in this example are extremely smooth (in fact constant!) in the interior of their support, but they are not smooth as maps defined on  $\mathbb{R}^n$ . To produce a similar construction with functions that are smooth on  $\mathbb{R}^n$ , one just needs to regularize f and  $g_{\varepsilon}$  a tiny bit, letting the regularization parameter vanish as  $\varepsilon \to 0$ . □

### Loeper's counterexample

Loeper discovered that in a genuine Riemannian setting the smoothness of optimal transport can be prevented by some local geometric obstructions. The next counterexample will illustrate this phenomenon.

Theorem 12.4 (A further example of discontinuous optimal transport). There is a smooth compact Riemannian surface S, and there are smooth positive probability densities f and g on S, such that the optimal transport between  $\mu(dx) = f(x) \text{vol}(dx)$  and  $\nu(dy) = g(y) \text{vol}(dy)$ , with a cost function equal to the square of the geodesic distance on S, is discontinuous.

Remark 12.5. The obstruction has nothing to do with the lack of smoothness of the squared distance. Counterexamples of the same type exist for very smooth cost functions.

**Remark 12.6.** As we shall see in Theorem 12.44, the surface  $S$  in Theorem 12.4 could be replaced by any compact Riemannian manifold admitting a negative sectional curvature at some point. In that sense there is no hope for general regularity results outside the world of nonnegative sectional curvature.

Image /page/5/Figure/3 description: The image displays a diagram illustrating the principle behind a laser system. Four circular objects, labeled A+, A-, B-, and B+, are positioned symmetrically around a central point labeled O. The objects A+ and A- are aligned vertically, while B- and B+ are aligned horizontally. Dashed curved lines connect A+ to B- and B+, and A- to B- and B+, suggesting a flow or interaction between these points. The entire arrangement is enclosed within a larger, irregularly shaped boundary, which is depicted with a thick, continuous line. The circular objects are shaded with diagonal lines, indicating they represent some form of active elements or particles within the system.

Fig. 12.2. Principle behind Loeper's counterexample. This is the surface  $S$ , immersed in  $\mathbb{R}^3$ , "viewed from above". By symmetry, O has to stay in place. Because most of the initial mass is close to  $A_+$  and  $A_-$ , and most of the final mass is close to  $B_+$  and  $B_-$ , at least some mass has to move from one of the A-balls to one of the B-balls. But then, because of the modified (negative curvature) Pythagoras inequality, it is more efficient to replace the transport scheme  $(A \rightarrow B, O \rightarrow O)$ , by  $(A \rightarrow O, O \rightarrow B).$ 

*Proof of Theorem 12.4.* Let S be a compact surface in  $\mathbb{R}^3$  with the following properties: (a) S is invariant under the symmetries  $x \to -x$ ,  $y \rightarrow -y$ ; (b) S crosses the axis  $(x, y) = (0, 0)$  at exactly two points, namely  $O = (0, 0, 0)$  and  $O'$ ; (c) S coincides in an open ball  $B(O, r)$ with the "horse saddle"  $(z = x^2 - y^2)$ . (Think of S as a small piece of the horse saddle which has been completed into a closed surface.)

Let  $x_0, y_0 > 0$  to be determined later. Then let  $A_+ = (x_0, 0, x_0^2)$ ,  $A_{-} = (-x_0, 0, x_0^2)$ , and similarly  $B_{+} = (0, y_0, -y_0^2), B_{-} = (0, -y_0, -y_0^2);$ see Figure 12.2. In the sequel the symbol  $A_{\pm}$  will stand for "either  $A_{+}$ or  $A_{-}$ ", etc.

If  $x_0$  and  $y_0$  are small enough then the four points  $A_+, A_-, B_+, B_$ belong to a neighborhood of  $O$  where  $S$  has (strictly) negative curvature, and the unique geodesic joining O to  $A_{\pm}$  (resp.  $B_{\pm}$ ) satisfies the equation  $(y = 0)$  (resp.  $x = 0$ ); then the lines  $(O, A<sub>±</sub>)$  and  $(O, B<sub>±</sub>)$  are orthogonal at O. Since we are on a negatively curved surface, Pythagoras's identity in a triangle with a square angle is modified in favor of the diagonal, so

$$
d(O, A_{\pm})^2 + d(O, B_{\pm})^2 < d(A_{\pm}, B_{\pm})^2.
$$

By continuity, there is  $\varepsilon_0 > 0$  small enough that the balls  $B(A_+, \varepsilon_0)$ ,  $B(A_-, \varepsilon_0), B(B_+, \varepsilon_0)$  and  $B(B_-, \varepsilon_0)$  are all disjoint and satisfy

$$
\begin{aligned} \left[ x \in B(A_+, \varepsilon_0) \cup B(A_-, \varepsilon_0), \quad y \in B(B_+, \varepsilon_0) \cup B(B_-, \varepsilon_0) \right] \\ \implies \quad d(O, x)^2 + d(O, y)^2 < d(x, y)^2. \end{aligned} \tag{12.5}
$$

Next let f and g be smooth probability densities on  $M$ , even in x and y, such that

$$
\int_{B(A_+,\varepsilon_0)\cup B(A_-,\varepsilon_0)} f(x) dx > \frac{1}{2}; \qquad \int_{B(B_+,\varepsilon_0)\cup B(B_-,\varepsilon_0)} g(y) dy > \frac{1}{2}.
$$
\n(12.6)

Let  $\mu(dx) = f(x) dx$ ,  $\nu(dy) = g(y) dy$ , let T be the unique optimal transport between the measures  $\mu$  and  $\nu$  (for the cost function  $c(x,y)$ )  $d(x, y)^2$ ), and let  $\widetilde{T}$  be the optimal transport between  $\nu$  and  $\mu$ . (T and  $\tilde{T}$  are inverses of each other, at least in a measure-theoretical sense.) I claim that either  $T$  or  $\widetilde{T}$  is discontinuous.

Indeed, suppose to the contrary that both  $T$  and  $\tilde{T}$  are continuous. We shall first see that necessarily  $T(O) = O$ . Since the problem is symmetric with respect to  $x \to -x$  and  $y \to -y$ , and since there is uniqueness of the optimal transport,  $T$  maps  $O$  into a point that is invariant under these two transforms, that is, either  $O$  or  $O'$ . Similarly,  $T(O') \in \{O, O'\}$ . So we have two cases to dismiss:

Case 1:  $T(O) = O'$ ,  $T(O') = O$ . Then by Lemma 12.2 the two pairs  $(0,0')$  and  $(0',0)$  belong to the support of the optimal plan associated to  $T$ , which trivially contradicts the cyclical monotonicity since  $d(O, O')^2 + d(O', O)^2 > d(O, O)^2 + d(O', O')^2 = 0$ .

Case 2:  $T(O) = O', T(O') = O'.$  Then both  $(O', O)$  and  $(O', O')$ belong to the support of the optimal plan. Let  $U$  and  $U'$  be two disjoint neighborhoods of O and O' respectively. By swapping variables x and y we see that  $(O', O)$  and  $(O', O')$  belong to the support of the optimal plan  $(\mathrm{Id}, T)_{\#}\nu$ ; so for any  $\varepsilon > 0$  the map T has to send a set of positive measure in  $B_{\varepsilon}(O')$  into U, and also a set of positive measure in  $B_{\varepsilon}(O')$ into  $U'$ . This contradicts the continuity of T.

So we may assume that  $T(O) = O$ . By Lemma 12.2 again,  $(O, O)$ belongs to the support of the optimal plan  $\pi$ . Then (12.6) implies that there is some transfer of mass from either  $B(A_+,\varepsilon_0) \cup B(A_-,\varepsilon_0)$  to  $B(B_+,\varepsilon_0) \cup B(B_-,\varepsilon_0)$ ; in other words, we can find, in the support of the optimal transport, some  $(x, y)$  with  $x \in B(A_+, \varepsilon_0) \cup B(A_-, \varepsilon_0)$  and  $y \in B(B_+, \varepsilon_0) \cup B(B_-, \varepsilon_0)$ . From the previous step we know that  $(O, O)$ also lies in that support; then by c-cyclical monotonicity,

$$
d(x, y)^{2} + d(O, O)^{2} \leq d(x, O)^{2} + d(y, O)^{2};
$$

but this contradicts (12.5). The proof is complete.  $□$ 

### Smoothness and Assumption (C)

Here as in Chapter 9, I shall say that a cost function c on  $\mathcal{X} \times \mathcal{Y}$  satisfies Assumption (C) if for any c-convex function  $\psi$  and for any  $x \in \mathcal{X}$ , the c-subdifferential (or contact set) of  $\psi$  at x,  $\partial_c \psi(x)$ , is connected. Some interesting consequences of this assumption were discussed in Chapter 9, and it was shown that even the simple cost function  $c(x,y) =$  $|x - y|^p$  on ℝ<sup>n</sup> × ℝ<sup>n</sup>,  $p > 2$ , does not satisfy it. Now we shall see that this assumption is more or less necessary for the regularity of optimal transport. For simplicity I shall work in a compact setting; it would be easy to extend the proof to more general situations by imposing suitable conditions at infinity.

Theorem 12.7 (Smoothness needs Assumption  $(C)$ ). Let X  $(resp. \mathcal{Y})$  be the closure of a bounded open set in a smooth Riemannian manifold  $M$  (resp.  $N$ ), equipped with its volume measure. Let  $c: \mathcal{X} \times \mathcal{Y} \rightarrow \mathbb{R}$  be a continuous cost function. Assume that there are a c-convex function  $\psi : \mathcal{X} \to \mathbb{R}$ , and a point  $\overline{x} \in \mathcal{X}$ , such that  $\partial_c \psi(\overline{x})$ is disconnected. Then there exist  $C^{\infty}$  positive probability densities f on  $\mathcal X$  and g on  $\mathcal Y$  such that any optimal transport map from f vol to g vol is discontinuous.

**Remark 12.8.** Assumption (C) involves both the geometry of  $\mathcal{Y}$  and the local properties of  $c$ ; in some sense the obstruction described in this theorem underlies both Caffarelli's and Loeper's counterexamples.

Remark 12.9. The volume measure in Theorem 12.7 can be replaced by any finite measure giving positive mass to all open sets. (In that sense the Riemannian structure plays no real role.) Moreover, with simple modifications of the proof one can weaken the assumption " $\partial_c \psi(\overline{x})$ is disconnected" into " $\partial_c \psi(\overline{x})$  is not simply connected".

Proof of Theorem 12.7. Note first that the continuity of the cost function and the compactness of  $\mathcal{X} \times \mathcal{Y}$  implies the continuity of  $\psi$ . In the proof, the notation d will be used interchangeably for the distances on  $\mathcal X$  and  $\mathcal Y$ .

Let  $C_1$  and  $C_2$  be two disjoint connected components of  $\partial_c \psi(\overline{x})$ . Since  $\partial_c\psi(\overline{x})$  is closed,  $C_1$  and  $C_2$  lie a positive distance apart. Let  $r = d(C_1, C_2)/5$ , and let  $C = \{y \in \mathcal{Y}; d(y, \partial_c \psi(\overline{x})) \geq 2r\}$ . Further, let  $\overline{y}_1 \in C_1$ ,  $\overline{y}_2 \in C_2$ ,  $B_1 = B_r(\overline{y}_1)$ ,  $B_2 = B_r(\overline{y}_2)$ . Obviously, C is compact, and any path going from  $B_1$  to  $B_2$  has to go through  $C$ .

Then let  $K = \{z \in \mathcal{X}; \exists y \in C; y \in \partial_c \psi(z)\}.$  It is clear that K is compact: Indeed, if  $z_k \in K$  converges to  $z \in \mathcal{X}$ , and  $y_k \in \partial_c \psi(z_k)$ , then without loss of generality  $y_k$  converges to some  $y \in C$  and one can pass to the limit in the inequality  $\psi(z_k) + c(z_k, y_k) \leq \psi(t) + c(t, y_k)$ , where  $t \in \mathcal{X}$  is arbitrary. Also K is not empty since X and Y are compact.

Next I claim that for any  $y \in C$ , for any x such that  $y \in \partial_c \psi(x)$ , and for any  $i \in \{1,2\},\$ 

$$
c(\overline{x}, \overline{y}_i) + c(x, y) < c(\overline{x}, y) + c(x, \overline{y}_i). \tag{12.7}
$$

Indeed,  $y \notin \partial_c \psi(\overline{x})$ , so

$$
\psi(x) + c(x, y) = \inf_{\widetilde{x} \in \mathcal{X}} \left[ \psi(\widetilde{x}) + c(\widetilde{x}, y) \right] < \psi(\overline{x}) + c(\overline{x}, y). \tag{12.8}
$$

On the other hand,  $y_i \in \partial_c \psi(\overline{x})$ , so

$$
\psi(\overline{x}) + c(\overline{x}, \overline{y}_i) \le \psi(x) + c(x, \overline{y}_i). \tag{12.9}
$$

The combination of  $(12.8)$  and  $(12.9)$  implies  $(12.7)$ .

Next, one can reinforce (12.7) into

$$
c(\overline{x}, \overline{y}_i) + c(x, y) \le c(\overline{x}, y) + c(x, \overline{y}_i) - \varepsilon
$$
\n(12.10)

for some  $\varepsilon > 0$ . This follows easily from a contradiction argument based on the compactness of C and K, and once again the continuity of  $\partial_c \psi$ .

Then let  $\delta \in (0,r)$  be small enough that for any  $(x,y) \in K \times C$ , for any  $i \in \{1, 2\}$ , the inequalities

$$
d(x, x') \le 2\delta, \ d(\overline{x}, \overline{x}') \le 2\delta, \ d(\overline{y}_i, \overline{y}'_i) \le 2\delta, \ d(y, y') \le 2\delta
$$

imply

$$
\left| c(\overline{x}', y) - c(\overline{x}, y) \right| \le \frac{\varepsilon}{10}, \qquad \left| c(x', y) - c(x, y) \right| \le \frac{\varepsilon}{10}, \qquad (12.11)
$$
$$
\left| c(\overline{x}', \overline{y}'_i) - c(\overline{x}, \overline{y}_i) \right| \le \frac{\varepsilon}{10}, \qquad \left| c(x', \overline{y}'_i) - c(x, \overline{y}_i) \right| \le \frac{\varepsilon}{10}.
$$

Let  $K^{\delta} = \{x \in \mathcal{X}; d(x,K) \leq \delta\}$ . From the assumptions on X and Y,  $B_\delta(\overline{x})$  has positive volume, so we can fix a smooth positive probability density f on X such that the measure  $\mu = f$  vol satisfies

$$
\mu[B_{\delta}(\overline{x})] \ge \frac{3}{4}; \qquad f \ge \varepsilon_0 > 0 \quad \text{on } K^{\delta}.
$$
 (12.12)

Also we can construct a sequence of smooth positive probability densities  $(g_k)_{k\in\mathbb{N}}$  on Y such that the measures  $\nu_k = g_k$  vol satisfy

$$
\nu_k \xrightarrow[k \to \infty]{} \frac{1}{2} \left( \delta_{\overline{y}_1} + \delta_{\overline{y}_2} \right) \quad \text{weakly.} \tag{12.13}
$$

Let us assume the existence of a continuous optimal transport  $T_k$ sending  $\mu_k$  to  $\nu_k$ , for any k. We shall reach a contradiction, and this will prove the theorem.

From (12.13),  $\nu_k[B_\delta(\overline{y}_1)] \geq 1/3$  for k large enough. Then by (12.12) the transport  $T_k$  has to send some mass from  $B_\delta(\overline{x})$  to  $B_\delta(\overline{y}_1)$ , and similarly from  $B_\delta(\overline{x})$  to  $B_\delta(\overline{y}_2)$ . Since  $T_k(B_\delta(\overline{x}))$  is connected, it has to meet C. So there are  $y_k \in C$  and  $\overline{x}'_k \in B_\delta(\overline{x})$  such that  $T_k(\overline{x}'_k) = y_k$ . Let  $x_k \in K$  be such that  $y_k \in \partial_c \psi(x_k)$ . Without loss of generality we may assume that  $x_k \to x_\infty \in K$  as  $k \to \infty$ . By the second part of (12.12),  $m := \mu[B_\delta(x_\infty)] \geq \varepsilon_0$  vol  $[B_\delta(x_\infty)] > 0$ . When k is large enough,  $\nu_k[B_\delta(\overline{y}_1) \cup B_\delta(\overline{y}_2)] > 1 - m$  (by (12.13) again), so  $T_k$  has to send some mass from  $B_{\delta}(x_{\infty})$  to either  $B_{\delta}(\overline{y}_1)$  or  $B_{\delta}(\overline{y}_2)$ ; say  $B_{\delta}(\overline{y}_1)$ . In other words, there is some  $x'_k \in B_\delta(x_\infty)$  such that  $T(x'_k) \in B_\delta(\overline{y}_1)$ .

Let us recapitulate: for  $k$  large enough,
# E.4 OGB-MolPCBA

Accurate prediction of the biochemical properties of small molecules can significantly accelerate drug discovery by reducing the need for expensive lab experiments (<PERSON><PERSON><PERSON><PERSON>, 2004; <PERSON> et al., 2011). However, the experimental data available for training such models is limited compared to the extremely diverse and combinatorially large universe of candidate molecules that we would want to make predictions on (<PERSON><PERSON><PERSON><PERSON> et al., 1996; Sterling and Irwin, 2015; <PERSON><PERSON> et al., 2019; <PERSON><PERSON><PERSON><PERSON><PERSON> et al., 2020). This means that models need to generalize to out-of-distribution molecules that are structurally different from those seen in the training set.

We study this issue through the OGB-MOLPCBA dataset, which is directly adopted from the Open Graph Benchmark (<PERSON> et al., 2020b) and originally curated by MoleculeNet (<PERSON> et al., 2018).

### E.4.1 SETUP

Problem setting. We consider the domain generalization setting, where the domains are molecular scaffolds, and our goal is to learn models that generalize to structurally distinct molecules with scaffolds that are not in the training set (Figure 6). This is a multi-task classification problem: for each molecule, we predict the presence or absence of 128 kinds of biological activities, such as binding to a particular enzyme. In addition, we cluster the molecules into different scaffold groups according to their two-dimensional structure, and annotate each molecule with the scaffold group that it belongs to. Concretely, the input  $x$  is a molecular graph, the label  $y$  is a 128-dimensional binary vector where each component corresponds to a biochemical assay result, and the domain d specifies the scaffold. Not all biological activities are measured for each molecule, so  $y$  can have missing values.

Data. OGB-MolPCBA contains more than 400K small molecules with 128 kinds of prediction labels. Each small molecule is represented as a graph, where the nodes are atoms and the edges are chemical bonds. The molecules are pre-processed using RDKIT (Landrum et al., 2006). Input node features are 9-dimensional, including atomic number, chirality, whether the atom is in the ring. Input edge features are 3-dimensional, including bond type and bond stereochemistry.

We split the dataset by scaffold structure. This *scaffold split* (Wu et al., 2018) is also used in the Open Graph Benchmark (Hu et al., 2020b). By attempting to separate structurally different molecules into different subsets, it provides a realistic estimate of model performance in prospective experimental settings. We assign the largest scaffolds to the training set to make it easier for algorithms to leverage scaffold information, and the smallest scaffolds to the test set to ensure that it is maximally diverse in scaffold structure:

- 1. Training: The largest 44,930 scaffolds, with an average of 7.8 molecules per scaffold.
- 2. Validation (OOD): The next largest 31,361 scaffolds, with an average of 1.4 molecules per scaffold.
- 3. Test (OOD): The smallest 43,793 scaffolds, which are all singletons.

In Figure [22](#page-1-0) (A), we plot the statistics of the scaffolds in terms of the number of molecules belonging to each scaffold. We see that the scaffold sizes are highly skewed, with the test set containing (by design) the scaffolds with the least molecules. However, the differences in scaffold sizes do not significantly change the statistics of the molecules in each split. In Figures [22](#page-1-0) (B) and (C), we see that the label statistics remain very similar across train/validation/test splits, suggesting that the main distribution shift comes from the difference in the input molecular graph structure.

Evaluation. We evaluate models by their average Average Precision (AP) across tasks (i.e., we compute the average precision for each task separately, and then average those scores), following Hu et al. (2020b). This accounts for the extremely skewed class balance in OGB-MolPCBA (only 1.4% of data is positive). Not all labels are available for each molecule; when calculating the AP for each task, we only consider the labeled molecules for the task.

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays three plots labeled (A), (B), and (C). Plot (A) is a line graph showing the number of molecules in scaffolds on a logarithmic scale against scaffold ID. The y-axis ranges from 10^0 to 10^4, and the x-axis ranges from 0 to 120000. The plot shows a steep decline in the number of molecules for the first few scaffold IDs, followed by a gradual decrease with several plateaus. Plot (B) is a line graph illustrating the positive label ratio against task ID. The y-axis ranges from 0.00 to 0.35, and the x-axis ranges from 0 to 120. Three lines representing 'train', 'valid', and 'test' data are plotted, showing fluctuating ratios with sharp peaks around task IDs 50, 90, and 110. Plot (C) is a line graph depicting the label ratio against task ID. The y-axis ranges from 0.0 to 0.8, and the x-axis ranges from 0 to 120. Similar to plot (B), it shows 'train', 'valid', and 'test' lines with fluctuating ratios, generally increasing over time with significant drops around task IDs 45, 95, and 115.

Figure 22: Analyses of scaffold groups in the OGB-MolPCBA dataset. (A) shows the distribution of the scaffold sizes, (B) and (C) show how the ratios of positive molecules and labeled molecules for the 128 tasks vary across the train/validation /test splits.

<span id="page-1-1"></span>Table 9: Baseline results on OGB-MolPCBA. Parentheses show standard deviation across 3 replicates.

| Algorithm | Validation AP (%) | Test AP (%)       |
|-----------|-------------------|-------------------|
| ERM       | <b>27.8</b> (0.1) | <b>27.2</b> (0.3) |
| CORAL     | 18.4 (0.2)        | 17.9 (0.5)        |
| IRM       | 15.8 (0.2)        | 15.6 (0.3)        |
| Group DRO | 23.1 (0.6)        | 22.4 (0.6)        |

Potential leverage. We provide the scaffold grouping of molecules for training algorithms to leverage. Finding generalizable representations of molecules across different scaffold groups is useful for models to make accurate extrapolation on unseen scaffold groups. In fact, very recent work (Jin et al., 2020) has leveraged scaffold information of molecules to improve the extrapolation performance of molecular property predictors.

One notable characteristic of the scaffold group is that the size of each group is rather small; on the training split, each scaffold contains only 7.8 molecules on average. This also results in many scaffold groups: 44,930 groups in the training split. In Figure [22,](#page-1-0) we show that these scaffold groups are well-behaved in the sense that the train/validation/test splits contain contain similar ratios of positive labels as well as missing labels.

### E.4.2 Baseline results

Model. For all experiments, we use Graph Isomorphism Networks (GIN) (Xu et al., 2018) combined with virtual nodes (Gilmer et al., 2017), as this is currently the model with the highest performance in the Open Graph Benchmark (Hu et al., 2020b). We follow the same hyperparameters as in the Open Graph Benchmark: 5 GNN layers with a dimensionality of 300; the Adam optimizer (Kingma and Ba, 2015) with a learning rate of 0.001; and training for 100 epochs with early stopping. For each of the baseline algorithms (ERM, CORAL, IRM, and Group DRO), we separately tune the dropout rate from {0, 0.5}; in addition, for CORAL and IRM, we tune the penalty weight as in Appendix D.

ERM results and performance drops. We first compare the generalization performance of ERM on the official scaffold split against the conventional random split, in which the entire molecules are randomly split into train/validation/test sets with the same split ratio as the scaffold split (i.e.,  $80/10/10$ . Results are in Table [10.](#page-2-0) The test performance of ERM drops by 7.2 points AP when the scaffold split is used, suggesting that the scaffold split is indeed harder than the random split.

To maintain consistency with the Open Graph Benchmark, and because the number of examples (molecules) per domain (scaffold) is relatively small compared to other datasets, we opted not to split off a portion of the training set into Validation (ID) and Test (ID) sets. We therefore do not run a

<span id="page-2-0"></span>Table 10: Random split comparison for ERM models on OGB-MoLPCBA. In the official OOD setting, we train on molecules from some scaffolds and evaluate on molecules from different scaffolds, whereas in the random split setting, we randomly divide molecules into training and test sets without using scaffold information. Parentheses show standard deviation across 3 replicates.

| Setting                       | Algorithm | Test AP $(\%)$      |
|-------------------------------|-----------|---------------------|
| Official (split by scaffolds) | ERM       | 27.2(0.3)           |
| Random split (split i.i.d.)   | ERM       | <b>34.4</b> $(0.9)$ |

train-to-train comparison for OGB-MolPCBA. Moreover, as the official scaffold split assigns the largest scaffolds to the training set and the smallest scaffolds to the test set, the test scaffolds all only have one molecule per scaffold, which precludes running test-to-test and mixed-to-test comparisons.

A potential issue with the random split ID comparison is that it does not measure performance on the same test distribution as the official split, and therefore might be confounded by differences in intrinsic difficulty. However, we believe that the random split setting provides a reasonable measure of ID performance for OGB-MolPCBA, as Figure [22](#page-1-0) shows that the distribution of scaffolds assigned to the training versus test sets are similar. As the random split contains many singleton scaffolds in its test set that do not have corresponding molecules in the training set, we believe that it is likely to be an underestimate of the ID-OOD gap in OGB-MolPCBA.

Additional baseline methods. Table [9](#page-1-1) also shows that ERM performs better than CORAL, IRM, and Group DRO, all of which use scaffolds as the domains. For CORAL and IRM, we find that smaller penalties give better generalization performance, as larger penalty terms make the training insufficient. We use the 0.1 penalty for CORAL and  $\lambda = 1$  for IRM.

The primary issue with these existing methods is that they make the model significantly underfit the training data even when dropout is turned off. For instance, the training AP of CORAL and IRM is 20.0% and 15.9%, respectively, which are both lower than the 36.1% that ERM obtains even with 0.5 dropout. Also, these methods are primarily designed for the case when each group contains a decent number of examples, which is not the case for the OGB-MolPCBA dataset.

### E.4.3 Broader context

Because of the very nature of discovering new molecules, out-of-distribution prediction is prevalent in nearly all applications of machine learning to chemistry domains. Beyond drug discovery, a variety of tasks and their associated datasets have been proposed for molecules of different sizes.

For small organic molecules, the scaffold split has been widely adopted to stress-test models' capability for out-of-distribution generalization. While OGB-MolPCBA primarily focuses on predicting biophysical activity (e.g., protein binding), other datasets in MoleculeNet (Wu et al., 2018) include prediction of quantum mechanical properties (e.g., HOMO/LUMO), physical chemistry properties (e.g., water solubility), and physiological properties (e.g., toxicity prediction (Attene-Ramos et al., 2013)).

Besides small molecules, it is also of interest to apply machine learning over larger molecules such as catalysts and proteins. In the domain of catalysis, using machine learning to approximate expensive quantum chemistry simulation has gotten attention. The OC20 dataset has been recently introduced, containing 200+ million samples from quantum chemistry simulations relevant to the discovery of new catalysts for renewable energy storage and other energy applications (Becke, 2014; Chanussot et al., 2020; Zitnick et al., 2020). The OC20 dataset explicitly provides test sets with qualitatively different materials. In the domain of proteins, the recent trend is to use machine learning to predict 3D structure of proteins given their amino acid sequence information. This is known as the protein folding problem, and has sometimes been referred to as the Holy Grail of structural biology (Dill and MacCallum, 2012). CASP is a bi-annual competition to benchmark the progress of protein folding (Moult et al., 1995), and it evaluates predictions made on proteins whose 3D structures are identified very recently, presenting a natural temporal distribution shift. Recently, the AlphaFold2 deep learning model obtained breakthrough performance on the CASP challenge (Jumper et al., 2020), demonstrating exciting avenues of machine learning for structural biology.

## E.4.4 ADDITIONAL DETAILS

Data processing. The OGB-MolPCBA dataset contains 437,929 molecules annotated with 128 kinds of labels, each representing a bioassay curated in the PubChem database (Kim et al., 2016b). More details are provided in the MoleculeNet (Wu et al., 2018) and the Open Graph Benchmark (Hu et al., 2020b), from which the dataset is adopted.

### E.5 GlobalWheat-wilds

Models for automated, high-throughput plant phenotyping—measuring the physical characteristics of plants and crops, such as wheat head density and counts—are important tools for crop breeding (Thorp et al., 2018; Reynolds et al., 2020) and agricultural field management (Shi et al., 2016). These models are typically trained on data collected in a limited number of regions, even for crops grown worldwide such as wheat (Madec et al., 2019; Xiong et al., 2019; Ubbens et al., 2020; Ayalew et al., 2020). However, there can be substantial variation between regions, due to differences in crop varieties, growing conditions, and data collection protocols. Prior work on wheat head detection has shown that this variation can significantly degrade model performance on regions unseen during training (David et al., 2020).

We study this shift in an expanded version of the Global Wheat Head Dataset (David et al., 2020, 2021), a large set of wheat images collected from 12 countries around the world.

### E.5.1 SETUP

**Problem setting.** We consider the domain generalization setting, where the goal is to learn models that generalize to images taken from new countries and acquisition sessions (Figure 7). The task is wheat head detection, which is a single-class object detection task. Concretely, the input x is an overhead outdoor image of wheat plants, and the label y is a set of bounding box coordinates that enclose the wheat heads (the spike at the top of the wheat plant containing grain), excluding the hair-like awns that may extend from the head. The domain d specifies an *acquisition session*, which corresponds to a specific location, time, and sensor for which a set of images were collected. Our goal is to generalize to new acquisition sessions that are unseen during training. In particular, the dataset split captures a shift in location, with training and test sets comprising images from disjoint countries as discussed below.

Data. The dataset comprises 6,515 images containing 275,187 wheat heads. These images were collected over 47 acquisition sessions in 16 research institutes across 12 countries. We describe the metadata and statistics of each acquisition session in Table [11.](#page-4-0)

Many factors contribute to the variation in wheat appearance across acquisition sessions. In particular, across locations, there is substantial variation due to differences in wheat genotypes, growing conditions (e.g., planting density), illumination protocols, and sensors. We study the effect of this location shift by splitting the dataset by country and assigning acquisition sessions from disjoint continents to the training and test splits:

- 1. Training: Images from 18 acquisition sessions in Europe (France  $\times$ 13, Norway  $\times$ 2, Switzerland, United Kingdom, Belgium), containing 131,864 wheat heads across 2,943 images.
- 2. Validation (OOD): Images from 7 acquisition sessions in Asia (Japan  $\times$  4, China  $\times$  3) and 1 acquisition session in Africa (Sudan), containing 44,873 wheat heads across 1,424 images.

- 3. Test (OOD): Images from 11 acquisition sessions in Australia and 10 acquisition sessions in North America (USA  $\times$  6, Mexico  $\times$  3, Canada), containing 66,905 wheat heads across 1,434 images.
- 4. Validation (ID): Images from the same 18 training acquisition sessions in Europe, containing 15,733 wheat heads across 357 images.
- 5. Test (ID): Images from the same 18 training acquisition sessions in Europe, containing 16,093 wheat heads across 357 images.

<span id="page-4-0"></span>Table 11: Acquisition sessions in GLOBALWHEAT-WILDS. Growth stages are abbreviated as F: Filling, R: Ripening, PF: Post-flowering. Locations are abbreviated as VLB: Villiers le Bâcle, VSC: Villers-Saint-Christophe. UTokyo\_1 and UTokyo\_2 are from the same location with different cart sensors and UTokyo\_3 consists of images from a variety of farms in Hokkaido between 2016 and 2019. The  $\#$  images and  $\#$  heads in the "Train" domains include the images and heads used in the Val (ID) and Test (ID) splits, which are taken from the same set of training domains. The "Val" and "Test" domains refer to the Val (OOD) and Test (OOD) splits, respectively.

| Split    | Name                | Owner         | Country     | Site              | Date       | Sensor    | Stage       | $#$ Images | $#$ Heads |
|----------|---------------------|---------------|-------------|-------------------|------------|-----------|-------------|------------|-----------|
| Training | Ethz 1              | ETHZ          | Switzerland | Eschikon          | 06/06/2018 | Spidercam | $\mathbf F$ | 747        | 49603     |
| Training | Rres 1              | Rothamsted    | UK          | Rothamsted        | 13/07/2015 | Gantry    | $F-R$       | 432        | 19210     |
| Training | ULiège 1            | Uliège        | Belgium     | Gembloux          | 28/07/2020 | Cart      | $\rm R$     | 30         | 1847      |
| Training | NMBU 1              | <b>NMBU</b>   | Norway      | <b>NMBU</b>       | 24/07/2020 | Cart      | $\mathbf F$ | 82         | 7345      |
| Training | NMBU <sub>2</sub>   | <b>NMBU</b>   | Norway      | <b>NMBU</b>       | 07/08/2020 | Cart      | $\mathbb R$ | 98         | 5211      |
| Training | Arvalis 1           | Arvalis       | France      | Gréoux            | 02/06/2018 | Handheld  | PF          | 66         | 2935      |
| Training | Arvalis 2           | Arvalis       | France      | Gréoux            | 16/06/2018 | Handheld  | $\mathbf F$ | 401        | 21003     |
| Training | Arvalis 3           | Arvalis       | France      | Gréoux            | 07/2018    | Handheld  | $F-R$       | 588        | 21893     |
| Training | Arvalis 4           | Arvalis       | France      | Gréoux            | 27/05/2019 | Handheld  | $\rm F$     | 204        | 4270      |
| Training | Arvalis 5           | Arvalis       | France      | $VLB*$            | 06/06/2019 | Handheld  | $\mathbf F$ | 448        | 8180      |
| Training | Arvalis 6           | Arvalis       | France      | $VSC^*$           | 26/06/2019 | Handheld  | $F-R$       | 160        | 8698      |
| Training | Arvalis 7           | Arvalis       | France      | $VLB*$            | 06/2019    | Handheld  | $F-R$       | 24         | 1247      |
| Training | Arvalis 8           | Arvalis       | France      | $VLB*$            | 06/2019    | Handheld  | $F-R$       | 20         | 1062      |
| Training | Arvalis 9           | Arvalis       | France      | $VLB*$            | 06/2020    | Handheld  | $_{\rm R}$  | 32         | 1894      |
| Training | Arvalis 10          | Arvalis       | France      | Mons              | 10/06/2020 | Handheld  | $\rm F$     | 60         | 1563      |
| Training | Arvalis 11          | Arvalis       | France      | $VLB*$            | 18/06/2020 | Handheld  | F           | 60         | 2818      |
| Training | Arvalis 12          | Arvalis       | France      | Gréoux            | 15/06/2020 | Handheld  | $\rm F$     | 29         | 1277      |
| Training | Inrae 1             | <b>INRAe</b>  | France      | Toulouse          | 28/05/2019 | Handheld  | $F-R$       | 176        | 3634      |
| Val      | Utokyo 1            | UTokyo        | Japan       | NARO-Tsukuba      | 22/05/2018 | Cart      | $\mathbf R$ | 538        | 14185     |
| Val      | Utokyo 2            | UTokyo        | Japan       | NARO-Tsukuba      | 22/05/2018 | Cart      | $\mathbf R$ | 456        | 13010     |
| Val      | Utokyo 3            | UTokyo        | Japan       | NARO-Hokkaido     | 2016-19    | Handheld  | multiple    | 120        | 3085      |
| Val      | Ukyoto 1            | UKyoto        | Japan       | Kyoto             | 30/04/2020 | Handheld  | PF          | 60         | 2670      |
| Val      | NAU 1               | NAU           | China       | Baima             | n/a        | Handheld  | PF          | 20         | 1240      |
| Val      | NAU 2               | NAU           | China       | Baima             | 02/05/2020 | Cart      | PF          | 100        | 4918      |
| Val      | NAU 3               | NAU           | China       | Baima             | 09/05/2020 | Cart      | $\mathbf F$ | 100        | 4596      |
| Val      | ARC <sub>1</sub>    | ARC           | Sudan       | Wad Medani        | 03/2021    | Handheld  | $\rm F$     | 30         | 1169      |
| Test     | Usask 1             | USaskatchewan | Canada      | Saskatoon         | 06/06/2018 | Tractor   | $F-R$       | 200        | 5985      |
| Test     | KSU 1               | KansasStateU  | US          | Manhattan, KS     | 19/05/2016 | Tractor   | PF          | 100        | 6435      |
| Test     | KSU <sub>2</sub>    | KansasStateU  | US          | Manhattan, KS     | 12/05/2017 | Tractor   | PF          | 100        | 5302      |
| Test     | KSU 3               | KansasStateU  | US          | Manhattan, KS     | 25/05/2017 | Tractor   | $\rm F$     | 95         | 5217      |
| Test     | KSU 4               | KansasStateU  | US          | Manhattan, KS     | 25/05/2017 | Tractor   | $_{\rm R}$  | 60         | 3285      |
| Test     | Terraref 1          | TERRA-REF     | US          | Maricopa          | 02/04/2020 | Gantry    | R           | 144        | 3360      |
| Test     | Terraref 2          | TERRA-REF     | US          | Maricopa          | 20/03/2020 | Gantry    | $\rm F$     | 106        | 1274      |
| Test     | CIMMYT 1            | <b>CIMMYT</b> | Mexico      | Ciudad Obregon    | 24/03/2020 | Cart      | PF          | 69         | 2843      |
| Test     | CIMMYT <sub>2</sub> | <b>CIMMYT</b> | Mexico      | Ciudad Obregon    | 19/03/2020 | Cart      | PF          | 77         | 2771      |
| Test     | CIMMYT 3            | <b>CIMMYT</b> | Mexico      | Ciudad Obregon    | 23/03/2020 | Cart      | PF          | 60         | 1561      |
| Test     | $UQ_1$              | UQueensland   | Australia   | Gatton            | 12/08/2015 | Tractor   | PF          | 22         | 640       |
| Test     | $UQ$ 2              | UQueensland   | Australia   | Gatton            | 08/09/2015 | Tractor   | PF          | 16         | 39        |
| Test     | $UQ_3$              | UQueensland   | Australia   | Gatton            | 15/09/2015 | Tractor   | F           | 14         | 297       |
| Test     | $UQ_4$              | UQueensland   | Australia   | Gatton            | 01/10/2015 | Tractor   | $\mathbf F$ | 30         | 1039      |
| Test     | $UQ_5$              | UQueensland   | Australia   | Gatton            | 09/10/2015 | Tractor   | $F-R$       | 30         | 3680      |
| Test     | $UQ_6$              | UQueensland   | Australia   | Gatton            | 14/10/2015 | Tractor   | $F-R$       | 30         | 1147      |
| Test     | $UQ$ 7              | UQueensland   | Australia   | Gatton            | 06/10/2020 | Handheld  | $_{\rm R}$  | 17         | 1335      |
| Test     | UQ 8                | UQueensland   | Australia   | McAllister        | 09/10/2020 | Handheld  | $_{\rm R}$  | 41         | 4835      |
| Test     | UQ 9                | UQueensland   | Australia   | <b>Brookstead</b> | 16/10/2020 | Handheld  | $F-R$       | 33         | 2886      |
| Test     | $UQ$ 10             | UQueensland   | Australia   | Gatton            | 22/09/2020 | Handheld  | $F-R$       | 106        | 8629      |
| Test     | $UQ$ 11             | UQueensland   | Australia   | Gatton            | 31/08/2020 | Handheld  | PF          | 84         | 4345      |
|          |                     |               |             |                   |            |           |             |            |           |

<span id="page-5-0"></span>Table 12: Baseline results on GLOBALWHEAT-WILDS. In-distribution (ID) results correspond to the train-totrain setting. Parentheses show standard deviation across 3 replicates.

| Algorithm | Validation (ID) acc | Validation (OOD) acc | Test (ID) acc | Test (OOD) acc |
|-----------|---------------------|----------------------|---------------|----------------|
| ERM       | 77.4 (1.1)          | 68.6 (0.4)           | 77.1 (0.5)    | 51.2 (1.8)     |
| Group DRO | 76.1 (1.0)          | 66.2 (0.4)           | 76.2 (0.8)    | 47.9 (2.0)     |

Evaluation. We evaluate models by first computing the average accuracy of bounding box detection within each image; then computing the average accuracy for each acquisition session by averaging its per-image accuracies; and finally averaging the accuracies of each acquisition session. The accuracy of a bounding box detection is measured at a fixed Intersection over Union (IoU) threshold of 0.5. The accuracy of an image is computed as  $\frac{TP}{TP+FN+FP}$ , where TP is the number of true positives, which are ground-truth bounding boxes that can be matched with some predicted bounding box at IoU above the threshold;  $FN$  is the number of false negatives, which are ground-truth bounding boxes that cannot be matched as above; and FP is the number of false positives, which are predicted bounding boxes that cannot be matched with any ground-truth bounding box. We use accuracy rather than average precision, which is a common metric for object detection, because it was used in previous Global Wheat Challenges with the dataset (David et al., 2020, 2021). We use a permissive IoU threshold of 0.5 because there is some uncertainty regarding the precise outline of wheat head instances due to the stem and awns extending from the head. We measure the average accuracy across acquisition sessions because the number of images varies significantly across acquisition sessions, from 17 to 200 images in the test set, and we use average accuracy instead of worst-case accuracy because the wheat images are more difficult for some acquisition sessions.

Potential leverage. The appearance of wheat heads in the images taken from different acquisition sessions can vary significantly, due to differences in the sensors used; illumination conditions, due to differences in illumination protocols, or the time of day and time of year that the images were taken; wheat genotypes; growth stages; growing conditions; and planting strategies. For example, different locations might feature a mix of different varieties of wheat (with different genotypes) with different appearances. Likewise, wheat planting strategies and growing conditions vary between regions and can contribute to differences between sessions, e.g., higher planting density may result in more closely packed plants and more occlusion between wheat head instances.

To provide leverage for models to learn to generalize across these conditions, we include images from 5 countries and 18 acquisition sessions in the training set. These training sessions cover all growth stages and include significant variation among all of the other factors. While the test domains include unseen conditions (e.g., sensors and genotypes not seen in the training set), our hope is that the variation in the training set will be sufficient to learn models that are robust to changes in these conditions.

#### E.5.2 Baseline results

Model. For all experiments, we use the Faster-RCNN detection model (Ren et al., 2015), which has been successfully applied to the wheat head localization problem (Madec et al., 2019; David et al., 2020). To train, we fine-tune a model pre-trained with ImageNet, using a batch size of 4, a learning rate of 10<sup>-5</sup>, and weight decay of 10<sup>-3</sup> for 10 epochs with early stopping. The hyperparameters were chosen from a grid search over learning rates  $\{10^{-6}, 10^{-5}, 10^{-4}\}$  and weight decays  $\{0, 10^{-4}, 10^{-3}\}$ . We report results aggregated over 3 random seeds.

ERM results and performance drops. We ran both train-to-train and mixed-to-test comparisons. For the train-to-train comparison, which uses the data splits described in the previous subsection, the Test (ID) accuracy is substantially higher than the Test (OOD) accuracy (77.1 (0.5)

<span id="page-6-0"></span>Table 13: Mixed-to-test comparison for ERM models on GLOBALWHEAT-WILDS. In the official OOD setting, we train on data from Europe, whereas in the mixed-to-test ID setting, we train on a mix of data from Europe, Africa, and North America. In both settings, we test on data from Africa and North America. For this comparison, we report performance on 50% of the official test set (randomly selecting 50% of each test domain), with the rest of the test set mixed in to the training set in the mixed-to-test setting. Parentheses show standard deviation across 3 replicates.

| Setting                                      | Algorithm | Test accuracy (%) |
|----------------------------------------------|-----------|-------------------|
| Official (train on ID examples)              | ERM       | 49.6 (1.9)        |
| Mixed-to-test (train on $ID + OOD$ examples) | ERM       | 63.3 (1.7)        |

<span id="page-6-1"></span>Table 14: Mixed-to-test comparison for ERM models on GLOBALWHEAT-WILDS, broken down by each test domain. This is a more detailed version of Table [13.](#page-6-0) Parentheses show standard deviation across 3 replicates.

| Session    | Country   | $#$ Images | ID (mixed-to-test) acc | OOD acc       | ID-OOD gap |
|------------|-----------|------------|------------------------|---------------|------------|
| CIMMYT_1   | Mexico    | 35         | $63.1 (1.4)$           | $48.0 (2.6)$  | $15.1$     |
| CIMMYT_2   | Mexico    | $39$       | $76.1 (0.9)$           | $58.2 (3.6)$  | $17.9$     |
| CIMMYT_3   | Mexico    | $30$       | $65.6 (3.1)$           | $63.3 (2.1)$  | $2.3$      |
| KSU_1      | US        | $50$       | $73.5 (1.1)$           | $53.2 (2.3)$  | $20.3$     |
| KSU_2      | US        | $50$       | $73.6 (0.5)$           | $52.7 (2.7)$  | $20.9$     |
| KSU_3      | US        | $48$       | $73.3 (1.2)$           | $48.9 (3.0)$  | $24.4$     |
| KSU_4      | US        | $30$       | $68.3 (0.6)$           | $48.7 (3.5)$  | $19.6$     |
| Terraref_1 | US        | $72$       | $48.9 (0.5)$           | $17.9 (4.3)$  | $31.0$     |
| Terraref_2 | US        | $53$       | $34.7 (1.3)$           | $16.0 (3.4)$  | $18.7$     |
| Usask_1    | Canada    | $100$      | $78.3 (0.8)$           | $77.1 (1.4)$  | $1.2$      |
| UQ_1       | Australia | $11$       | $41.8 (1.4)$           | $29.0 (1.0)$  | $12.8$     |
| UQ_2       | Australia | $8$        | $81.6 (12.5)$          | $76.5 (14.4)$ | $5.1$      |
| UQ_3       | Australia | $7$        | $56.4 (13.8)$          | $54.3 (10.0)$ | $2.1$      |
| UQ_4       | Australia | $15$       | $68.8 (0.5)$           | $60.6 (1.3)$  | $8.2$      |
| UQ_5       | Australia | $15$       | $54.4 (2.1)$           | $38.6 (2.1)$  | $15.8$     |
| UQ_6       | Australia | $15$       | $75.8 (1.1)$           | $71.9 (0.7)$  | $3.9$      |
| UQ_7       | Australia | $9$        | $68.9 (0.6)$           | $62.8 (2.5)$  | $6.1$      |
| UQ_8       | Australia | $21$       | $58.6 (0.6)$           | $46.5 (2.1)$  | $11.1$     |
| UQ_9       | Australia | $17$       | $54.7 (1.5)$           | $43.6 (2.1)$  | $11.1$     |
| UQ_10      | Australia | $53$       | $61.7 (0.8)$           | $39.6 (2.5)$  | $22.1$     |
| UQ_11      | Australia | $42$       | $50.4 (1.5)$           | $33.5 (2.7)$  | $16.9$     |
| Total      |           | $720$      | $63.3 (1.7)$           | $49.6 (1.9)$  | $13.7$     |

vs. 51.2 (1.8); Table [12\)](#page-5-0). However, the Test (ID) and Test (OOD) sets come from entirely different regions, so this performance gap could also reflect a difference in the difficulty of the wheat head detection task in different regions (e.g., wheat heads that are more densely packed are harder to tell apart).

The mixed-to-test comparison controls for the test distribution by randomly splitting each test domain (acquisition session) into two halves, and then assigning one half to the training set. In other words, we randomly take out half of the test set and use it to replace existing examples in the training set, so that the total training set size is the same, and we retain the other half of the test set for evaluation. We also evaluated the ERM model trained on the official split on this subsampled test set. On this subsampled test set, the mixed-to-test ID accuracy is significantly higher than the OOD accuracy of the ERM model trained on the official split (63.3 (1.7) vs. 49.6 (1.9); Table [13\)](#page-6-0).

We also compared the per-domain accuracies of the models trained in the mixed-to-test and official settings (Table [14\)](#page-6-1) on the subsampled test set. The accuracy drop is not evenly distributed across each domain, though some of the domains have a relatively small number of images, so there is some variance across random replicates. The location/site of the acquisition session—which is correlated with factors like wheat genotype and the sensor used—has a large effect on performance (e.g., the KSU and Terraref sessions displayed a larger drop than the other sessions), but beyond that, it is not clear what factors are most strongly driving the accuracy drop. The Terraref sessions were particularly difficult even in the mixed-to-test setting, because of the strong contrast in its photos and the presence of hidden wheat heads under leaves. On the other hand, the KSU sessions had comparatively high accuracies in the mixed-to-test setting, but still displayed a large accuracy drop in the official OOD setting. As the KSU sessions differed primarily in their development stages and had largely similar ID and OOD accuracies, development stage does not seem to be a main driver of the accuracy drop. Finally, we note that the especially high variance across replicates for UQ\_2 and UQ\_3 is due to the proportion of empty images in those domains (88% for UQ\_2 and 57% for UQ 3). Empty images are scored as either having 0% or 100% accuracy and therefore can have a large impact on the overall domain accuracy.

Additional baseline methods. We also trained models with group DRO, treating each acquisition session as a domain, and using the same model hyperparameters as ERM. However, the group DRO models perform poorly compared to the ERM model as reported in Table [12.](#page-5-0) We leave the investigation of CORAL and IRM for future work because it is not straightforward to apply these algorithms to detection tasks.

Discussion. Our baseline models were trained without any data augmentation, in contrast to baselines reported in the original dataset (David et al., 2020). Data augmentation could reduce the performance gap and warrants further investigation in future work, although David et al. (2020) still observed performance gaps on models trained with data augmentation in the original version of the dataset. Moreover, while we evaluated models by their average performance across acquisition sessions, we noticed a large variability in performance across domains. It is possible that some domains are more challenging or suffer from larger performance drops than others, and characterizing and mitigating these variations is interesting future work.

#### E.5.3 Broader context

Wheat head localization, while being an important operational trait for wheat breeders and farmers, is not the only deep learning application in plant phenotyping that suffers from lack of generalization. Other architectural traits such as plant segmentation (Sadeghi-Tehran et al., 2017; Kuznichov et al., 2019), plant and plant organ detection (Fan et al., 2018; Madec et al., 2019), leaves and organ disease classification (Fuentes et al., 2017; Shakoor et al., 2017; Toda and Okura, 2019), and biomass and yield prediction (Aich et al., 2018; Dreccer et al., 2019) would also benefit from plant phenotyping models that generalize to new deployments. In many of these applications, field images exhibit variations in illumination and sensors, and there has been work on mitigating biases across sensors (Ayalew et al., 2020; Gogoll et al., 2020). Finally, developing models that generalize across plant species would benefit the breeding and growing of specialized crops that are presently under-represented in plant phenotyping research worldwide (Ward and Moghadam, 2020). We hope that GLOBALWHEAT-WILDS can foster the development of general solutions to plant phenotyping problems, increase collaboration between plant scientists and computer vision scientists, and encourage the development of new multi-domain plant datasets to ensure that plant phenotyping results are generalizable to all crop growing regions of the world.

## E.5.4 ADDITIONAL DETAILS

Modifications to the original dataset. The data is taken directly from the 2021 Global Wheat Challenge (David et al., 2021), which is an expanded version of the 2020 Global Wheat Challenge dataset (David et al., 2020). Compared to the challenge, the dataset splits are different: we split off part of the training set to form the Validation (ID) and Test (ID) sets, and we rearranged the Validation (OOD) and Test (OOD) sets so that they split along disjoint continents. Finally, we note that the 2021 challenge differs from the 2020 challenge in that images from North America were in the training set in the 2020 challenge, but were used for evaluation in the 2021 challenge, and are consequently assigned to the test set in GLOBALWHEAT-WILDS.

# E.6 CivilComments-wilds

Automatic review of user-generated text is an important tool for moderating the sheer volume of text written on the Internet. We focus here on the task of detecting toxic comments. Prior work has shown that toxicity classifiers can pick up on biases in the training data and spuriously associate toxicity with the mention of certain demographics (Park et al., 2018; Dixon et al., 2018). These types of spurious correlations can significantly degrade model performance on particular subpopulations (Sagawa et al., 2020a).

We study this issue through a modified variant of the CivilComments dataset (Borkan et al., 2019b).

## E.6.1 SETUP

**Problem setting.** We cast CIVILCOMMENTS-WILDS as a subpopulation shift problem, where the subpopulations correspond to different demographic identities, and our goal is to do well on all subpopulations (and not just on average across these subpopulations). Specifically, we focus on mitigating biases with respect to comments that mention particular demographic identities, and not comments written by members of those demographic identities; we discuss this distinction in the broader context section below.

The task is a binary classification task of determining if a comment is toxic. Concretely, the input x is a comment on an online article (comprising one or more sentences of text) and the label  $\eta$  is whether it is rated toxic or not. In CIVILCOMMENTS-WILDS, unlike in most of the other datasets we consider, the domain annotation  $d$  is a multi-dimensional binary vector, with the  $8$  dimensions corresponding to whether the comment mentions each of the 8 demographic identities male, female, LGBTQ, Christian, Muslim, other religions, Black, and White.

Data. CivilComments-wilds comprises 450,000 comments, each annotated for toxicity and demographic mentions by multiple crowdworkers. We model toxicity classification as a binary task. Toxicity labels were obtained in the original dataset via crowdsourcing and majority vote, with each comment being reviewed by at least 10 crowdworkers. Annotations of demographic mentions were similarly obtained through crowdsourcing and majority vote.

Each comment was originally made on some online article. We randomly partitioned these articles into disjoint training, validation, and test splits, and then formed the corresponding datasets by taking all comments on the articles in those splits. This gives the following splits:

- 1. Training: 269,038 comments.
- 2. Validation: 45,180 comments.
- 3. Test: 133,782 comments.

Evaluation. We evaluate a model by its worst-group accuracy, i.e., its lowest accuracy over groups of the test data that we define below.

As mentioned above, toxicity classifiers can spuriously latch onto mentions of particular demographic identities, resulting in a biased tendency to flag comments that innocuously mention certain demographic groups as toxic (Park et al., 2018; Dixon et al., 2018). To measure the extent of this bias, we define subpopulations based on whether they mention a particular demographic identity, compute the sensitivity (a.k.a. recall, or true positive rate) and specificity (a.k.a. true negative rate) of the classifier on each subpopulation, and then report the worst of these two metrics over all subpopulations of interest. This is equivalent to further dividing each subpopulation into two groups according to the label, and then computing the accuracy on each of these two groups.

Specifically, for each of the 8 identities we study (e.g., "male"), we form 2 groups based on the toxicity label (e.g., one group of comments that mention the male gender and are toxic, and another group that mentions the male gender and are not toxic), for a total of 16 groups. These groups overlap (a comment might mention multiple identities) and are not a complete partition (a comment might not mention any identity).

We then measure a model's performance by its worst-group accuracy, i.e., its lowest accuracy over these 16 groups. A high worst-group accuracy (relative to average accuracy) implies that the model is not spuriously associating a demographic identity with toxicity. We can view this subpopulation shift problem as testing on multiple test distributions (corresponding to different subsets of the test set, based on demographic identities and the label) and reporting the worst performance over these different test distributions.

We use 16 groups (8 identities  $\times$  2 labels) instead of just 8 groups (8 identities) to capture the desire to balance true positive and true negative rates across each of the demographic identities. Without splitting by the label, it would be possible for two different groups to have equal accuracies, but one group might be much more likely to have non-toxic comments flagged as toxic, whereas the other group might be much more likely to have toxic comments flagged as non-toxic. This would be undesirable from an application perspective, as such a model would still be biased against a particular demographic. In Appendix [E.6.4,](#page-12-0) we further discuss the motivation for our choice of evaluation metric as well as its limitations.

As variability in performance over replicates can be high due to the small sizes of some demographic groups (Table [17\)](#page-13-0), we report results averaged over 5 random seeds, instead of the 3 seeds that we use for most other datasets.

Potential leverage. Since demographic identity annotations are provided at training time, we have an i.i.d. dataset available at training time for each of the test distributions of interest (corresponding to each group). Moreover, even though demographic identity annotations are unavailable at test time, they are relatively straightforward to predict.

### E.6.2 Baseline results

Model. For all experiments, we fine-tuned DistilBERT-base-uncased models (Sanh et al., 2019), using the implementation from Wolf et al. (2019) and with the following hyperparameter settings: batch size 16; learning rate  $10^{-5}$  using the AdamW optimizer (Loshchilov and Hutter, 2019) for 5 epochs with early stopping; an L<sub>2</sub>-regularization strength of  $10^{-2}$ ; and a maximum number of tokens of 300, since 99.95% of the input examples had ≤300 tokens. The learning rate was chosen through a grid search over  $\{10^{-6}, 2 \times 10^{-6}, 10^{-5}, 2 \times 10^{-5}\}$ , and all other hyperparameters were simply set to standard/default values.

ERM results and performance drops. The ERM model does well on average, with 92.2% average accuracy (Table [15\)](#page-10-0). However, it does poorly on some subpopulations, e.g., with 57.4% accuracy on toxic comments that mention *other religions*. Overall, accuracy on toxic comments (which are a minority of the dataset) was lower than accuracy on non-toxic comments, so we also trained a reweighted model that balanced toxic and non-toxic comments by upsampling the toxic comments. This reweighted model had a slightly worse average accuracy of 89.8% and a better

<span id="page-10-0"></span>Table 15: Baseline results on CIVILCOMMENTS-WILDS. The reweighted (label) algorithm samples equally from the positive and negative class; the group DRO (label) algorithm additionally weights these classes so as to minimize the maximum of the average positive training loss and average negative training loss. Similarly, the reweighted (label  $\times$  Black) and group DRO (label  $\times$  Black) algorithms sample equally from the four groups corresponding to all combinations of class and whether there is a mention of Black identity. The CORAL and IRM algorithms extend the reweighted algorithm by adding their respective penalty terms, so they also sample equally from each group. We show standard deviation across 5 random seeds in parentheses.

| Algorithm                  | Avg val acc | Worst-group val acc | Avg test acc      | Worst-group test acc |
|----------------------------|-------------|---------------------|-------------------|----------------------|
| <b>ERM</b>                 | 92.3 (0.2)  | 50.5 (1.9)          | <b>92.2</b> (0.1) | 56.0 (3.6)           |
| Reweighted (label)         | 90.1 (0.4)  | 65.9 (1.8)          | 89.8 (0.4)        | 69.2 (0.9)           |
| Group DRO (label)          | 90.4 (0.4)  | 65.0 (3.8)          | 90.2 (0.3)        | 69.1 (1.8)           |
| Reweighted (label × Black) | 89.5 (0.6)  | 66.6 (1.5)          | 89.2 (0.6)        | 66.2 (1.2)           |
| CORAL (label × Black)      | 88.9 (0.6)  | 64.7 (1.4)          | 88.7 (0.5)        | 65.6 (1.3)           |
| IRM (label × Black)        | 89.0 (0.7)  | 65.9 (2.8)          | 88.8 (0.7)        | 66.3 (2.1)           |
| Group DRO (label × Black)  | 90.1 (0.4)  | 67.7 (1.8)          | 89.9 (0.5)        | <b>70.0</b> (2.0)    |

worst-group accuracy of 69.2% (Table [15,](#page-10-0) Reweighted (label)), but a significant gap remains between average and worst-group accuracies.

We note that the relatively small size of some of the demographic subpopulations makes it infeasible to run a test-to-test comparison, i.e., estimate how well a model could do on each subpopulation (corresponding to demographic identity) if it were trained on just that subpopulation. For example, Black comments comprise only <4% of the training data, and training just on those Black comments is insufficient to achieve high in-distribution accuracy. Without running the test-to-test comparison, it is possible that the gap between average and worst-group accuracies can be explained at least in part by differences in the intrinsic difficulty of some of the subpopulations, e.g., the labels of some subpopulations might be noisier because human annotators might disagree more frequently on comments mentioning a particular demographic identity. Future work will be required to establish estimates of in-distribution accuracies for each subpopulation that can account for these differences.

<span id="page-10-1"></span>Table 16: Accuracies on each subpopulation in CIVILCOMMENTS-WILDS, averaged over models trained by group DRO (label).

| Demographic     | Test accuracy on non-toxic comments | Test accuracy on toxic comments |
|-----------------|-------------------------------------|---------------------------------|
| Male            | 88.4 (0.7)                          | 75.1 (2.1)                      |
| Female          | 90.0 (0.6)                          | 73.7 (1.5)                      |
| LGBTQ           | 76.0 (3.6)                          | 73.7 (4.0)                      |
| Christian       | 92.6 (0.6)                          | 69.2 (2.0)                      |
| Muslim          | 80.7 (1.9)                          | 72.1 (2.6)                      |
| Other religions | 87.4 (0.9)                          | 72.0 (2.5)                      |
| Black           | 72.2 (2.3)                          | 79.6 (2.2)                      |
| White           | 73.4 (1.4)                          | 78.8 (1.7)                      |

Additional baseline methods. The CORAL, IRM, and group DRO baselines involve partitioning the training data into disjoint domains. We study the following partitions, corresponding to different rows in Table [15:](#page-10-0)

- 1. Label: 2 domains, 1 for each class.
- 2. Label  $\times$  Black: 4 domains, 1 for each combination of class and Black.

On the Label partition, we used Group DRO to train a model that seeks to balance the losses on the positive and negative examples. This performs similarly to the standard reweighted models described above (Table [15,](#page-10-0) Group DRO (label)). We found that the worst-performing demographic for non-toxic comments was the Black demographic (Table [16\)](#page-10-1), which motivated the Label  $\times$  Black partition. There, we used CORAL, IRM, and Group DRO to train models. However, these models did not perform significantly better (Table  $15$ , label  $\times$  Black). While there were slight improvements on the Black groups, accuracy degraded on some other groups like non-toxic LBGTQ comments.

We note that our implementations of CORAL and IRM are built on top of the standard reweighting algorithm, i.e., they sample equally from each group. As these two algorithms perform similarly to reweighting, it indicates that the additional penalty term is not significantly affecting performance. Indeed, our grid search for the penalty weights selected the lowest value of the penalties ( $\lambda = 10.0$ ) for CORAL and  $\lambda = 1.0$  for IRM).

Discussion. Adapting the baseline methods to handle multiple overlapping groups, which were not studied in their original settings, could be a potential approach to improving accuracy on this task. Another potential approach is using baselining to account for different groups having different intrinsic levels of difficulty (Oren et al., 2019). For example, comments mentioning different demographic groups might differ in terms of how subjective classifying them is. Others have also explored specialized data augmentation techniques for mitigating demographic biases in toxicity classifiers (Zhao et al., 2018).

Adragna et al. (2020) recently used a simplified variant of the CivilComments dataset, with artificially-constructed training and test environments, to show a proof-of-concept that IRM can improve performance on minority groups. Methods such as IRM and group DRO rely heavily on the choice of groups/domains/environments; investigating the effect of different choices would be a useful direction for future work. Other recent work has studied methods that try to automatically learn groups, for example, through unsupervised clustering (Oren et al., 2019; Sohoni et al., 2020) or identifying high-loss points (Nam et al., 2020; Liu et al., 2021a).

Toxicity classification is one application where human moderators can work together with an ML model to handle examples that the model is unsure about. However, Jones et al. (2021) found that using selective classifiers—where the model is allowed to abstain if it is unsure—can actually further worsen performance on minority subpopulations. This suggests that in addition to having low accuracy on minority subpopulations, standard models can be poorly calibrated on them.

Another important consideration for toxicity detection in practice is shifts over time, as online discourse changes quickly, and what is seen as toxic today might not have even appeared in the dataset from a few months ago. We do not study this distribution shift in this work. One limitation of the CivilComments-wilds dataset is that it is fixed to a relatively short period in time, with most comments being written in the span of a year; this makes it harder to use as a dataset for studying temporal shifts.

Finally, we note that collecting "ground truth" human annotation of toxicity is itself a subjective and challenging process; recent work has studied ways of making it less biased and more efficient (Sap et al., 2019; Han and Tsvetkov, 2020).

#### E.6.3 Broader context

The CivilComments-wilds dataset does not assume that user demographics are available; instead, it uses mentions of different demographic identities in the actual comment text. For example, we want models that do not associate comments that mention being Black with being toxic, regardless of whether a Black or non-Black person wrote the comment. This setting is particularly relevant when user demographics are unavailable, e.g., when considering anonymous online comments.

A related and important setting is subpopulation shifts with respect to user demographics (e.g., the demographics of the author of the comment, regardless of the content of the comment). Such demographic disparities have been widely documented in natural language and speech processing tasks (Hovy and Spruit, 2016), among other areas. For example, NLP models have been shown to obtain worse performance on African-American Vernacular English compared to Standard American English on part-of-speech tagging (Jørgensen et al., 2015), dependency parsing (Blodgett et al., 2016), language identification (Blodgett and O'Connor, 2017), and auto-correct systems (Hashimoto et al., 2018). Similar disparities exist in speech, with state-of-the-art commercial systems obtaining higher word error rates on particular races (Koenecke et al., 2020) and dialects (Tatman, 2017).

These disparities are present not just in academic models, but in large-scale commercial systems that are already widely deployed, e.g., in speech-to-text systems from Amazon, Apple, Google, IBM, and Microsoft (Tatman, 2017; Koenecke et al., 2020) or language identification systems from IBM, Microsoft, and Twitter (Blodgett and O'Connor, 2017). Indeed, the original CivilComments dataset was developed by Google's Conversation AI team, which is also behind a public toxicity classifier (Perspective API) that was developed in partnership with The New York Times (NYTimes, 2016).

<span id="page-12-0"></span>

### E.6.4 ADDITIONAL DETAILS

Evaluation metrics. The evaluation metric used in the original competition was a complex weighted combination of various metrics, including subgroup AUCs for each demographic identity, and a new pinned AUC metric introduced by the original authors (Borkan et al., 2019b); conceptually, these metrics also measure the degree to which model accuracy is uniform across the different identities. After discussion with the original authors, we replace the composite metric with worst-group accuracy (i.e., worst TPR/FPR over identities) for simplicity. Measuring subgroup AUCs can be misleading in this context, because it assumes that the classifier can set separate thresholds for different subgroups (Borkan et al., 2019b,a).

One downside is that measuring worst-group accuracy treats false positives and false negatives equally. In deployment systems, one might want to weight these differently, e.g., using cost-sensitive learning or by simply raising or lowering the classification threshold, especially since real data is highly imbalanced (with a lot more negatives than positives). One could also binarize the labels and identities differently: in this benchmark, we simply use majority voting from the annotators.

Perhaps more fundamentally, even if TPR and FPR were balanced across different identities, this need not imply unambiguously equitable performance, because different subpopulations might have different intrinsic levels of noise and difficulty. See Corbett-Davies and Goel (2018) for more discussion of this problem of infra-marginality.

In practice, models might also do poorly on intersections of groups (Kearns et al., 2018), e.g., on comments that mention multiple identities. Given the size of the dataset and comparative rarity of some identities and of toxic comments in general, accuracies on these intersections are difficult to estimate from this dataset. A potential avenue of future work is to develop methods for evaluating models on such subgroups, e.g., by generating data in particular groups through templates (Park et al., 2018; Ribeiro et al., 2020).

Data processing. The CivilComments-wilds dataset comprises comments from a large set of articles from the Civil Comments platform, annotated for toxicity and demographic identities (Borkan et al., 2019b). We partitioned the articles into disjoint training, validation, and test splits, and then formed the corresponding datasets by taking all comments on the articles in those splits. In total, the training set comprised 269,038 comments (60% of the data); the validation set comprised 45,180 comments (10%); and the test set comprised 133,782 (30%).

**Modifications to the original dataset.** The original dataset<sup>8</sup> also had a training and test split with disjoint articles. These splits are related to ours in the following way. Let the number of articles in the original test split be  $m$ . To form our validation split, we took  $m$  articles (sampled uniformly at random) from the original training split, and to form our test split, we took  $2m$  articles (also sampled uniformly at random) from the original training split and added it to the existing test split.

<sup>8.</sup> <www.kaggle.com/c/jigsaw-unintended-bias-in-toxicity-classification/>

| Demographic     | Number of non-toxic comments | Number of toxic comments |
|-----------------|------------------------------|--------------------------|
| Male            | 12092                        | 2203                     |
| Female          | 14179                        | 2270                     |
| LGBTQ           | 3210                         | 1216                     |
| Christian       | 12101                        | 1260                     |
| Muslim          | 5355                         | 1627                     |
| Other religions | 2980                         | 520                      |
| Black           | 3335                         | 1537                     |
| White           | 5723                         | 2246                     |

<span id="page-13-0"></span>Table 17: Group sizes in the test data for CIVILCOMMENTS-WILDS. The training and validation data follow similar proportions.

We added a fixed validation set to allow other researchers to be able to compare methods more consistently, and we tripled the size of the test set to allow for more accurate worst-group accuracy measurement.

Similarly, we combined some of the demographic identities in the original dataset to obtain larger groups (for which we could more accurately estimate accuracy). Specifically, we created an aggregate  $LGBTQ$  identity that combines the original homosexual gay or lesbian, bisexual, other sexual orientation, transgender, and other gender identities (e.g., it is 1 if any of those identities are 1), and an aggregate *other religions* identity that combines the original jewish, hindu, buddhist, atheist, and other religion identities. We also omitted the psychiatric or mental illness identity, which was evaluated in the original Kaggle competition, because of a lack of sufficient data for accurate estimation; but we note that baseline group accuracies for that identity seemed higher than for the other groups, so it is unlikely to factor into worst-group accuracy. In our new split, each identity we evaluate on (male, female, LGBTQ, Christian, Muslim, other religions, Black, and White) has at least 500 positive and 500 negative examples. In Table [17](#page-13-0) we show the sizes of each subpopulation in the test set; the training and validation sets follow similar proportions.

For convenience, we also add an *identity any* identity; this combines all of the identities in the original dataset, including *psychiatric* or mental *illness* and related identities.

**Additional baseline results.** We also trained a group DRO model using  $2^9 = 512$  domains, 1 for each combination of class and the 8 identities. This model performed similarly to the other group DRO models.

Additional data sources. All of the data, including the data with identity annotations that we use and the data with just label annotations, are also annotated for additional toxicity subtype attributes, specifically severe\_toxicity, obscene, threat, insult, identity\_attack, and sexual\_explicit. These annotations can be used to train models that are more aware of the different ways that a comment can be toxic; in particular, using the *identity* attack attribute to learn which comments are toxic because of the use of identities might help the model learn how to avoid spurious associations between toxicity and identity. These additional annotations are included in the metadata provided through the WILDS package.

The original CivilComments dataset (Borkan et al., 2019b) also contains ≈1.5M training examples that have toxicity (label) annotations but not identity (group) annotations. For simplicity, we have omitted these from the current version of CivilComments-wilds. These additional data points can be downloaded from the original data source and could be used, for example, by first inferring which group each additional point belongs to, and then running group DRO or a similar algorithm that uses group annotations at training time.
## **Appendix <PERSON>. Calculus of Variations**

We can think of a function  $y(x)$  as being an operator that, for any input value x, returns an output value y. In the same way, we can define a *functional*  $F[y]$  to be an operator that takes a function  $y(x)$  and returns an output value F. An example of a functional is the length of a curve drawn in a two-dimensional plane in which the path of the curve is defined in terms of a function. In the context of machine learning, a widely used functional is the entropy  $H[x]$  for a continuous variable x because, for any choice of probability density function  $p(x)$ , it returns a scalar value representing the entropy of x under that density. Thus the entropy of  $p(x)$  could equally well have been written as  $H[p]$ .

A common problem in conventional calculus is to find a value of  $x$  that maximizes (or minimizes) a function  $y(x)$ . Similarly, in the calculus of variations we seek a function  $y(x)$  that maximizes (or minimizes) a functional  $F[y]$ . That is, of all possible functions  $y(x)$ , we wish to find the particular function for which the functional  $F[y]$  is a maximum (or minimum). The calculus of variations can be used, for instance, to show that the shortest path between two points is a straight line or that the maximum entropy distribution is a Gaussian.

If we weren't familiar with the rules of ordinary calculus, we could evaluate a conventional derivative  $dy/dx$  by making a small change  $\epsilon$  to the variable x and then expanding in powers of  $\epsilon$ , so that

$$
y(x + \epsilon) = y(x) + \frac{dy}{dx}\epsilon + O(\epsilon^2)
$$
 (D.1)

and finally taking the limit  $\epsilon \to 0$ . Similarly, for a function of several variables  $y(x_1, \ldots, x_D)$ , the corresponding partial derivatives are defined by

$$
y(x_1 + \epsilon_1, \dots, x_D + \epsilon_D) = y(x_1, \dots, x_D) + \sum_{i=1}^D \frac{\partial y}{\partial x_i} \epsilon_i + O(\epsilon^2). \tag{D.2}
$$

The analogous definition of a functional derivative arises when we consider how much a functional  $F[y]$  changes when we make a small change  $\epsilon \eta(x)$  to the function

### **704 D. CALCULUS OF VARIATIONS**

**Figure D.1** A functional derivative can be defined by considering how the value of a functional  $F[y]$  changes when the function  $y(x)$  is changed to  $y(x) + \epsilon \eta(x)$  where  $\eta(x)$  is an arbitrary function of  $x$ .

Image /page/1/Figure/2 description: The image displays a graph with an x-axis labeled 'x' and a y-axis with an arrow pointing upwards. Two curves are plotted on the graph. The first curve, labeled 'y(x)', is red and shows an upward trend with a slight curve. The second curve, labeled 'y(x) + 
e
h(x)', is blue and also shows an upward trend, but it is more pronounced and has a different curvature than the red line. The two curves intersect at one point in the middle of the graph.

 $y(x)$ , where  $\eta(x)$  is an arbitrary function of x, as illustrated in Figure D.1. We denote the functional derivative of  $E[f]$  with respect to  $f(x)$  by  $\delta F/\delta f(x)$ , and define it by the following relation:

$$
F[y(x) + \epsilon \eta(x)] = F[y(x)] + \epsilon \int \frac{\delta F}{\delta y(x)} \eta(x) dx + O(\epsilon^2).
$$
 (D.3)

This can be seen as a natural extension of (D.2) in which  $F[y]$  now depends on a continuous set of variables, namely the values of  $y$  at all points  $x$ . Requiring that the functional be stationary with respect to small variations in the function  $y(x)$  gives

$$
\int \frac{\delta E}{\delta y(x)} \eta(x) dx = 0.
$$
 (D.4)

Because this must hold for an arbitrary choice of  $n(x)$ , it follows that the functional derivative must vanish. To see this, imagine choosing a perturbation  $\eta(x)$  that is zero everywhere except in the neighbourhood of a point  $\hat{x}$ , in which case the functional derivative must be zero at  $x = \hat{x}$ . However, because this must be true for every choice of  $\hat{x}$ , the functional derivative must vanish for all values of x.

Consider a functional that is defined by an integral over a function  $G(y, y', x)$ that depends on both  $y(x)$  and its derivative  $y'(x)$  as well as having a direct dependence on  $x$ 

$$
F[y] = \int G(y(x), y'(x), x) dx
$$
 (D.5)

where the value of  $y(x)$  is assumed to be fixed at the boundary of the region of integration (which might be at infinity). If we now consider variations in the function  $y(x)$ , we obtain

$$
F[y(x) + \epsilon \eta(x)] = F[y(x)] + \epsilon \int \left\{ \frac{\partial G}{\partial y} \eta(x) + \frac{\partial G}{\partial y'} \eta'(x) \right\} dx + O(\epsilon^2). \quad (D.6)
$$

We now have to cast this in the form  $(D.3)$ . To do so, we integrate the second term by parts and make use of the fact that  $\eta(x)$  must vanish at the boundary of the integral (because  $y(x)$  is fixed at the boundary). This gives

$$
F[y(x) + \epsilon \eta(x)] = F[y(x)] + \epsilon \int \left\{ \frac{\partial G}{\partial y} - \frac{\mathrm{d}}{\mathrm{d}x} \left( \frac{\partial G}{\partial y'} \right) \right\} \eta(x) \, \mathrm{d}x + O(\epsilon^2) \tag{D.7}
$$

from which we can read off the functional derivative by comparison with (D.3). Requiring that the functional derivative vanishes then gives

$$
\frac{\partial G}{\partial y} - \frac{\mathrm{d}}{\mathrm{d}x} \left( \frac{\partial G}{\partial y'} \right) = 0 \tag{D.8}
$$

which are known as the *Euler-Lagrange* equations. For example, if

$$
G = y(x)^{2} + (y'(x))^{2}
$$
 (D.9)

then the Euler-Lagrange equations take the form

$$
y(x) - \frac{d^2y}{dx^2} = 0.
$$
 (D.10)

This second order differential equation can be solved for  $y(x)$  by making use of the boundary conditions on  $y(x)$ .

Often, we consider functionals defined by integrals whose integrands take the form  $G(y, x)$  and that do not depend on the derivatives of  $y(x)$ . In this case, stationarity simply requires that  $\partial G/\partial y(x)=0$  for all values of x.

If we are optimizing a functional with respect to a probability distribution, then we need to maintain the normalization constraint on the probabilities. This is often *Appendix E* most conveniently done using a Lagrange multiplier, which then allows an unconstrained optimization to be performed.

> The extension of the above results to a multidimensional variable **x** is straightforward. For a more comprehensive discussion of the calculus of variations, see Sagan (1969).

*Appendix E*

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.

## **Appendix E. Lagrange Multipliers**

*Lagrange multipliers*, also sometimes called *undetermined multipliers*, are used to find the stationary points of a function of several variables subject to one or more constraints.

Consider the problem of finding the maximum of a function  $f(x_1, x_2)$  subject to a constraint relating  $x_1$  and  $x_2$ , which we write in the form

$$
g(x_1, x_2) = 0. \t\t (E.1)
$$

One approach would be to solve the constraint equation (E.1) and thus express  $x_2$  as a function of  $x_1$  in the form  $x_2 = h(x_1)$ . This can then be substituted into  $f(x_1, x_2)$ to give a function of  $x_1$  alone of the form  $f(x_1, h(x_1))$ . The maximum with respect to  $x_1$  could then be found by differentiation in the usual way, to give the stationary value  $x_1^*$ , with the corresponding value of  $x_2$  given by  $x_2^* = h(x_1^*)$ .

One problem with this approach is that it may be difficult to find an analytic solution of the constraint equation that allows  $x_2$  to be expressed as an explicit function of  $x_1$ . Also, this approach treats  $x_1$  and  $x_2$  differently and so spoils the natural symmetry between these variables.

A more elegant, and often simpler, approach is based on the introduction of a parameter  $\lambda$  called a Lagrange multiplier. We shall motivate this technique from a geometrical perspective. Consider a <sup>D</sup>-dimensional variable **x** with components  $x_1, \ldots, x_D$ . The constraint equation  $g(\mathbf{x})=0$  then represents a  $(D-1)$ -dimensional surface in **x**-space as indicated in Figure E.1.

We first note that at any point on the constraint surface the gradient  $\nabla g(\mathbf{x})$  of the constraint function will be orthogonal to the surface. To see this, consider a point **x** that lies on the constraint surface, and consider a nearby point  $\mathbf{x} + \epsilon$  that also lies on the surface. If we make a Taylor expansion around **x**, we have

$$
g(\mathbf{x} + \boldsymbol{\epsilon}) \simeq g(\mathbf{x}) + \boldsymbol{\epsilon}^{\mathrm{T}} \nabla g(\mathbf{x}).
$$
 (E.2)

Because both **x** and  $\mathbf{x} + \boldsymbol{\epsilon}$  lie on the constraint surface, we have  $g(\mathbf{x}) = g(\mathbf{x} + \boldsymbol{\epsilon})$  and here  $\boldsymbol{\epsilon}^T \nabla g(\mathbf{x}) \sim 0$ . In the limit  $\|\boldsymbol{\epsilon}\| \to 0$  we have  $\boldsymbol{\epsilon}^T \nabla g(\mathbf{x}) = 0$  and here exists hence  $\epsilon^T \nabla g(\mathbf{x}) \simeq 0$ . In the limit  $\|\epsilon\| \to 0$  we have  $\epsilon^T \nabla g(\mathbf{x}) = 0$ , and because  $\epsilon$  is

### **708 E. LAGRANGE MULTIPLIERS**

**Figure E.1** A geometrical picture of the technique of Lagrange multipliers in which we seek to maximize a function  $f(\mathbf{x})$ , subject to the constraint  $g(\mathbf{x})=0$ . If **x** is D dimensional, the constraint  $q(\mathbf{x})=0$  corresponds to a subspace of dimensionality  $D - 1$ , indicated by the red curve. The problem can be solved by optimizing the Lagrangian function  $L(\mathbf{x}, \lambda) = f(\mathbf{x}) + \lambda g(\mathbf{x}).$ 

Image /page/5/Figure/2 description: The image shows a red curve representing the constraint g(x)=0. A point labeled x\_A is on the curve. Two vectors are shown originating from x\_A: one labeled \nabla f(x) pointing away from the curve, and another labeled \nabla g(x) pointing towards the curve, perpendicular to it.

then parallel to the constraint surface  $g(\mathbf{x})=0$ , we see that the vector  $\nabla g$  is normal to the surface.

Next we seek a point  $x^*$  on the constraint surface such that  $f(x)$  is maximized. Such a point must have the property that the vector  $\nabla f(\mathbf{x})$  is also orthogonal to the constraint surface, as illustrated in Figure E.1, because otherwise we could increase the value of  $f(\mathbf{x})$  by moving a short distance along the constraint surface. Thus  $\nabla f$ and  $\nabla g$  are parallel (or anti-parallel) vectors, and so there must exist a parameter  $\lambda$ such that

$$
\nabla f + \lambda \nabla g = 0 \tag{E.3}
$$

where  $\lambda \neq 0$  is known as a *Lagrange multiplier*. Note that  $\lambda$  can have either sign.

At this point, it is convenient to introduce the *Lagrangian* function defined by

$$
L(\mathbf{x}, \lambda) \equiv f(\mathbf{x}) + \lambda g(\mathbf{x}).
$$
 (E.4)

The constrained stationarity condition (E.3) is obtained by setting  $\nabla_{\mathbf{x}}L = 0$ . Furthermore, the condition  $\partial L/\partial \lambda = 0$  leads to the constraint equation  $q(\mathbf{x})=0$ .

Thus to find the maximum of a function  $f(\mathbf{x})$  subject to the constraint  $g(\mathbf{x})=0$ , we define the Lagrangian function given by (E.4) and we then find the stationary point of  $L(\mathbf{x}, \lambda)$  with respect to both **x** and  $\lambda$ . For a D-dimensional vector **x**, this gives  $D + 1$  equations that determine both the stationary point  $\mathbf{x}^*$  and the value of  $\lambda$ . If we are only interested in  $x^*$ , then we can eliminate  $\lambda$  from the stationarity equations without needing to find its value (hence the term 'undetermined multiplier').

As a simple example, suppose we wish to find the stationary point of the function  $f(x_1, x_2) = 1 - x_1^2 - x_2^2$  subject to the constraint  $g(x_1, x_2) = x_1 + x_2 - 1 = 0$ , as illustrated in Figure E.2. The corresponding Lagrangian function is given by

$$
L(\mathbf{x}, \lambda) = 1 - x_1^2 - x_2^2 + \lambda (x_1 + x_2 - 1).
$$
 (E.5)

The conditions for this Lagrangian to be stationary with respect to  $x_1, x_2$ , and  $\lambda$  give the following coupled equations:

$$
-2x_1 + \lambda = 0 \tag{E.6}
$$

$$
-2x_2 + \lambda = 0 \tag{E.7}
$$

$$
x_1 + x_2 - 1 = 0. \t\t (E.8)
$$

### **E. LAGRANGE MULTIPLIERS 709**

**Figure E.2** A simple example of the use of Lagrange multipliers in which the aim is to maximize  $f(x_1, x_2) =$  $1-x_1^2-x_2^2$  subject to the constraint  $g(x_1, x_2)=0$ where  $g(x_1, x_2) = x_1 + x_2 - 1$ . The circles show contours of the function  $f(x_1, x_2)$ , and the diagonal line shows the constraint surface  $q(x_1, x_2)=0$ .

Image /page/6/Figure/2 description: The image displays a 2D plot with the x1 and x2 axes. Several concentric blue circles are centered at the origin, representing level curves of a function. A red line, labeled "g(x1, x2) = 0", intersects the circles. A black dot, labeled "(x1\*, x2\*)", is located at the point where the red line is tangent to the outermost blue circle. This point represents the optimal solution under the given constraint.

Solution of these equations then gives the stationary point as  $(x_1^*, x_2^*) = (\frac{1}{2}, \frac{1}{2})$ , and the corresponding value for the Lagrange multiplier is  $\lambda = 1$ .

So far, we have considered the problem of maximizing a function subject to an *equality constraint* of the form  $g(x) = 0$ . We now consider the problem of maximizing  $f(\mathbf{x})$  subject to an *inequality constraint* of the form  $g(\mathbf{x}) \ge 0$ , as illustrated in Figure E.3. in Figure E.3.

There are now two kinds of solution possible, according to whether the constrained stationary point lies in the region where  $g(x) > 0$ , in which case the constraint is *inactive*, or whether it lies on the boundary  $q(\mathbf{x})=0$ , in which case the constraint is said to be *active*. In the former case, the function  $q(\mathbf{x})$  plays no role and so the stationary condition is simply  $\nabla f(\mathbf{x})=0$ . This again corresponds to a stationary point of the Lagrange function (E.4) but this time with  $\lambda = 0$ . The latter case, where the solution lies on the boundary, is analogous to the equality constraint discussed previously and corresponds to a stationary point of the Lagrange function (E.4) with  $\lambda \neq 0$ . Now, however, the sign of the Lagrange multiplier is crucial, because the function  $f(\mathbf{x})$  will only be at a maximum if its gradient is oriented away from the region  $g(\mathbf{x}) > 0$ , as illustrated in Figure E.3. We therefore have  $\nabla f(\mathbf{x}) = -\lambda \nabla g(\mathbf{x})$  for some value of  $\lambda > 0$ .

For either of these two cases, the product  $\lambda g(\mathbf{x})=0$ . Thus the solution to the

Image /page/6/Figure/7 description: The image contains the text "Figure E.3" in red, followed by the text "Illustration of the problem of maximizing f(x) subject to the inequality constraint g(x) \ge 0."

Image /page/6/Figure/8 description: The image displays a diagram illustrating optimization concepts. A shaded region, bounded by a red curve, represents a feasible region. The text "g(x) > 0" points to the area outside the red curve, indicating that this region satisfies the inequality. The text "g(x) = 0" points to the red curve itself, signifying the boundary of the feasible region. Inside the shaded region, there are two vectors originating from the boundary and pointing inwards: "∇g(x)" and "∇f(x)". The vector "∇g(x)" is shown pointing towards the interior of the region. The vector "∇f(x)" is shown pointing from the boundary towards the interior, labeled with "xA" at its tip. A point labeled "xB" is located within the shaded region, away from the boundary.

### **710 E. LAGRANGE MULTIPLIERS**

problem of maximizing  $f(\mathbf{x})$  subject to  $g(\mathbf{x}) \ge 0$  is obtained by optimizing the Lagrange function (E.4) with respect to x and  $\lambda$  subject to the conditions Lagrange function (E.4) with respect to **x** and  $\lambda$  subject to the conditions

$$
g(\mathbf{x}) \geq 0 \tag{E.9}
$$

$$
\lambda \geqslant 0 \tag{E.10}
$$

$$
\lambda g(\mathbf{x}) = 0 \tag{E.11}
$$

These are known as the *Karush-Kuhn-Tucker* (KKT) conditions (Karush, 1939; Kuhn and Tucker, 1951).

Note that if we wish to minimize (rather than maximize) the function  $f(\mathbf{x})$  subject to an inequality constraint  $g(\mathbf{x}) \ge 0$ , then we minimize the Lagrangian function  $L(\mathbf{x}, \lambda) = f(\mathbf{x}) - \lambda g(\mathbf{x})$  with respect to x again subject to  $\lambda \ge 0$  $L(\mathbf{x}, \lambda) = f(\mathbf{x}) - \lambda g(\mathbf{x})$  with respect to **x**, again subject to  $\lambda \ge 0$ .<br>Finally it is straightforward to extend the technique of Lagran

Finally, it is straightforward to extend the technique of Lagrange multipliers to the case of multiple equality and inequality constraints. Suppose we wish to maximize  $f(\mathbf{x})$  subject to  $g_j(\mathbf{x}) = 0$  for  $j = 1, ..., J$ , and  $h_k(\mathbf{x}) \ge 0$  for  $k = 1, ..., K$ .<br>We then introduce I garange multipliers  $\{\lambda\}$  and  $\{u_k\}$  and then optimize the La-We then introduce Lagrange multipliers  $\{\lambda_i\}$  and  $\{\mu_k\}$ , and then optimize the Lagrangian function given by

$$
L(\mathbf{x}, {\lambda_j}, {\mu_k}) = f(\mathbf{x}) + \sum_{j=1}^{J} \lambda_j g_j(\mathbf{x}) + \sum_{k=1}^{K} \mu_k h_k(\mathbf{x})
$$
 (E.12)

subject to  $\mu_k \ge 0$  and  $\mu_k h_k(\mathbf{x}) = 0$  for  $k = 1, ..., K$ . Extensions to constrained functional derivatives are similarly straightforward. For a more detailed discussion *Appendix D* functional derivatives are similarly straightforward. For a more detailed discussion of the technique of Lagrange multipliers, see Nocedal and Wright (1999).

# Appendix D
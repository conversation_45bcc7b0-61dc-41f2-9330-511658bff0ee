# <span id="page-0-0"></span>TOWARDS LOSSLESS DATASET DISTILLATION VIA DIFFICULTY-ALIGNED TRAJECTORY MATCHING

<PERSON><PERSON><PERSON><sup>1,3,4</sup> <PERSON><sup>1†</sup> <PERSON><sup>2</sup> <PERSON><sup>4</sup> <PERSON><PERSON><PERSON><sup>3‡</sup> Yang <PERSON><sup>1‡</sup> <sup>1</sup>National University of Singapore  $\frac{2}{3}$ Massachusetts Institute of Technology  $3$ Shanghai Artificial Intelligence Laboratory  $4$ Xidian University

<EMAIL>, {kai.wang, youy}@comp.nus.edu.sg, <EMAIL>

<EMAIL>, (RAI.Wang), <EMAIL>, <EMAIL>

# ABSTRACT

The ultimate goal of *Dataset Distillation* is to synthesize a small synthetic dataset such that a model trained on this synthetic set will perform equally well as a model trained on the full, real dataset. Until now, no method of Dataset Distillation has reached this completely lossless goal, in part because they only remain effective when the total number of synthetic samples is *extremely small*. Since only so much information can be contained in such a small number of samples, it seems that to achieve truly lossless dataset distillation, we must develop a distillation method that remains effective as the size of the synthetic dataset grows. In this work, we present such an algorithm and elucidate *why* existing methods fail to generate larger, highquality synthetic sets. Current state-of-the-art methods rely on *trajectory-matching*, or optimizing the synthetic data to induce similar long-term training dynamics as the real data. We empirically find that the *training stage* of the trajectories we choose to match (*i.e.*, early or late) greatly affects the effectiveness of the distilled dataset. Specifically, early trajectories (where the teacher network learns *easy* patterns) work well for a low-cardinality synthetic set since there are fewer examples wherein to distribute the necessary information. Conversely, late trajectories (where the teacher network learns *hard* patterns) provide better signals for larger synthetic sets since there are now enough samples to represent the necessary complex patterns. Based on our findings, we propose to align the difficulty of the generated patterns with the size of the synthetic dataset. In doing so, we successfully scale trajectory matching-based methods to larger synthetic datasets, achieving lossless dataset distillation for the very first time. Code and distilled datasets are available at <https://github.com/NUS-HPC-AI-Lab/DATM>.

# 1 INTRODUCTION

Dataset distillation (DD) aims at distilling a large dataset into a small synthetic one, such that models trained on the distilled dataset will have similar performance as those trained on the original dataset. In recent years, several algorithms have been proposed for this important topic, such as gradient matching [\(Zhao et al.,](#page-11-0) [2020;](#page-11-0) [Kim et al.,](#page-9-0) [2022;](#page-9-0) [Zhang et al.,](#page-11-1) [2023;](#page-11-1) [Liu et al.,](#page-10-0) [2023b\)](#page-10-0), kernel inducing points [\(Nguyen et al.,](#page-10-1) [2020;](#page-10-1) [2021\)](#page-10-2), distribution matching [\(Wang et al.,](#page-10-3) [2022;](#page-10-3) [Zhao & Bilen,](#page-11-2) [2023;](#page-11-2) [Zhao et al.,](#page-11-3) [2023\)](#page-11-3), and trajectory matching [\(Cazenavette et al.,](#page-9-1) [2022;](#page-9-1) [Cui et al.,](#page-9-2) [2023;](#page-9-2) [Du et al.,](#page-9-3) [2023\)](#page-9-3). So far, dataset distillation has achieved great success in the regime of extremely small synthetic sets. For example, MTT [\(Cazenavette et al.,](#page-9-1) [2022\)](#page-9-1) achieves 71.6% test accuracy on CIFAR-10 using only 1% of the original data size. This impressive performance led to its application in a variety of downstream tasks such as continual learning [\(Masarczyk & Tautkute,](#page-10-4) [2020;](#page-10-4) [Rosasco et al.,](#page-10-5) [2021\)](#page-10-5), privacy protection [\(Zhou et al.,](#page-11-4) [2020;](#page-11-4) [Sucholutsky & Schonlau,](#page-10-6) [2021a;](#page-10-6) [Dong et al.,](#page-9-4) [2022;](#page-9-4) [Chen et al.,](#page-9-5) [2022;](#page-9-5) [Xiong et al.,](#page-11-5) [2023\)](#page-11-5), and neural architecture search [\(Such et al.,](#page-10-7) [2020;](#page-10-7) [Wang et al.,](#page-11-6) [2021\)](#page-11-6).

However, although previous DD methods have achieved great success with very few IPC (images-perclass), there still remains a significant gap between the performance of their distilled datasets and the full, real counterparts. To minimize this gap, one would intuitively think to increase the size of the

<sup>†</sup> Project lead.

<sup>‡</sup>Corresponding author.

<span id="page-1-0"></span>Image /page/1/Figure/1 description: This figure illustrates a method for synthetic data generation for model training. Panel (a) shows the overall framework where a large original dataset is used to train a model, and a distillation algorithm is used to optimize a small synthetic dataset. This synthetic dataset is then used to train a comparable model. Panel (b) depicts the 'Normal' approach where a distillation algorithm generates 'Patterns' which are added to create a synthetic dataset. Panel (c) shows 'Ours' approach, where the distillation algorithm generates both 'Easy Patterns' and 'Hard Patterns', which are then added to create a small and large synthetic dataset, respectively. Panel (d) presents a 'Performance Comparison' graph showing accuracy (%) versus IPC (In-context learning examples). The graph compares 'Full Dataset' with 'Random', 'DM', 'DSA', 'DC', 'FTD', and 'Ours' methods. The 'Ours' method achieves the highest accuracy across different IPC values, closely matching the 'Full Dataset' performance.

Figure 1: (a) Illustration of the objective of dataset distillation. (b) The optimization in dataset distillation can be viewed as the process of generating informative patterns on the synthetic dataset. (c) We align the difficulty of the synthetic patterns with the size of the distilled dataset, to enable our method to perform well in both small and large IPC regimes. (d) Comparison of the performance of multiple dataset distillation methods on CIFAR-10 with different IPC. As IPC increases, the performance of previous methods becomes worse than random selection.

synthetic dataset. Unfortunately, as IPC increases, previous distillation methods mysteriously become less effective, even performing worse than random selection [\(Cui et al.,](#page-9-6) [2022;](#page-9-6) [Zhou et al.,](#page-11-7) [2023\)](#page-11-7). In this paper, we offer an answer as to *why* previous dataset distillation methods become ineffective as IPC increases and, in doing so, become the first to circumvent this issue, allowing us to achieve lossless dataset distillation.

We start our work by observing the patterns learned by the synthetic data, taking trajectories matching (TM) based distillation methods [\(Cazenavette et al.,](#page-9-1) [2022;](#page-9-1) [Du et al.,](#page-9-3) [2023\)](#page-9-3) as an example. Generally, the process of dataset distillation can be viewed as the embedding of informative patterns into a set of synthetic samples. For TM-based distillation methods, the synthetic data learns patterns by matching the training trajectories of surrogate models optimized over the synthetic dataset and the real one. According to [\(Arpit et al.,](#page-9-7) [2017\)](#page-9-7), deep neural networks (DNNs) typically learn to recognize easy patterns early in training and hard patterns later on. As a result, we note that the properties of the data generated by TM-based methods vary widely depending on from which teacher training stage we sample our trajectories from (early or late). Specifically, matching early or late trajectories causes the synthetic data to learn easy or hard patterns respectively.

We then empirically show that the effect of learning easy and hard patterns varies with the size of the synthetic dataset (*i.e.*, IPC). In low-IPC settings, easy patterns prove the most beneficial since they explain a larger portion of the real data distribution than an equivalent number of hard samples. However, with a sufficiently large synthetic set, learning hard samples becomes optimal since their union covers both the easy and "long-tail" hard samples of the real data. In fact, learning easy patterns in the high-IPC setting performs *worse* than random selection since the synthetic images collapse towards the mean patterns of the distribution and can no longer capture the long-tail parts. Previous distillation methods default toward distilling easy patterns, leading to their ineffectiveness in high-IPC cases.

The above findings motivate us to manage to align the difficulty of the learned patterns with the size of the distilled dataset, in order to keep our method effective in both low and high IPC cases. Our experiments show that, for TM-based methods, we can control the difficulty of the generated patterns by only matching the trajectories of a specified training phase. By doing so, our method is able to work well in both low and high IPC settings. Furthermore, we propose to learn easy and hard patterns sequentially, making the optimization stable enough for learning soft labels during the distillation, bringing further significant improvement. Our method achieves state-of-the-art performance in both low and high IPC cases. Notably, we distill CIFAR-10 and CIFAR-100 to 1/5 and Tiny ImageNet to 1/10 of their original sizes without any performance loss on ConvNet, offering the first lossless method of dataset distillation.

# 2 PRELIMINARY

For a given large, real dataset  $\mathcal{D}_{\text{real}}$ , dataset distillation aims to synthesize a smaller dataset  $\mathcal{D}_{\text{syn}}$ such that models trained on  $\mathcal{D}_{syn}$  will have similar test performance as models trained on  $\mathcal{D}_{real}$ .

<span id="page-2-4"></span><span id="page-2-0"></span>Image /page/2/Figure/1 description: This image contains four plots labeled (a), (b), (c), and (d). Each plot shows the accuracy (%) on the y-axis against the iteration (10^3) on the x-axis. The plots compare three different matching strategies: 'Match All Traj.' (green squares), 'Match Early Traj.' (orange circles), and 'Match Late Traj.' (blue diamonds). Plot (a) shows results for IPC=10, with accuracies ranging from approximately 30% to 65%. Plot (b) shows results for IPC=100, with accuracies ranging from approximately 50% to 75%. Plot (c) shows results for IPC=1000, with accuracies ranging from approximately 70% to 85%. Plot (d) shows results for IPC=2000, with accuracies ranging from approximately 74% to 85%.

Figure 2: We train expert models on CIFAR-10 for 40 epochs. Then the distillation is performed under different IPC settings by matching either early trajectories  $\{\theta_t^*\ |\ 0 \le t \le 20\}$ , late trajectories  $\{\theta_t^*\ | 20 \le t \le 40\}$ , or all trajectories  $\{\theta_t^*\,|0\leq t\leq 40\}$ . As IPC increases, matching late trajectories becomes beneficial while matching early trajectories tends to be harmful.

For trajectory matching (TM) based methods, the distillation is performed by matching the training trajectories of the surrogate models optimized over  $\mathcal{D}_{\rm real}$  and  $\mathcal{D}_{\rm syn}$ . Specifically, let  $\tau^*$  denote the expert training trajectories, which is the time sequence of parameters  $\{\theta_t^*\}_0^n$  obtained during the training of a network on the real dataset  $\mathcal{D}_{real}$ . Similarly,  $\hat{\theta}_t$  denotes the parameters of the network trained on the synthetic dataset  $\mathcal{D}_{syn}$  at training step t.

In each iteration of the distillation,  $\theta_t^*$  and  $\theta_{t+M}^*$  are randomly sampled from a set of expert trajectories  $\{\tau^*\}$  as the start parameters and target parameters used for the matching, where M is a preset hyperparameter. Then TM-based distillation methods optimize the synthetic dataset  $\mathcal{D}_{syn}$  by minimizing the following loss:

<span id="page-2-2"></span>
$$
\mathcal{L} = \frac{\|\hat{\theta}_{t+N} - \theta_{t+M}^*\|_2^2}{\|\theta_t^* - \theta_{t+M}^*\|_2^2},\tag{1}
$$

where N is a preset hyper-parameter and  $\hat{\theta}_{t+N}$  is obtained in the inner optimization with cross-entropy (CE) loss  $\ell$  and the trainable learning rate  $\alpha$ :

<span id="page-2-1"></span>
$$
\hat{\theta}_{t+i+1} = \hat{\theta}_{t+i} - \alpha \nabla \ell(\hat{\theta}_{t+i}, \mathcal{D}_{\text{syn}}), \text{where } \hat{\theta}_t := \theta_t^*.
$$
 (2)

# 3 METHOD

In this section, we first analyze the influence of matching trajectories from different training stages. Then, we introduce our method and its carefully designed modules.

<span id="page-2-3"></span>

### 3.1 EXPLORATION

TM-based methods generate patterns on the synthetic data by matching training trajectories. According to [Arpit et al.](#page-9-7) [\(2017\)](#page-9-7), DNNs tend to learn easy patterns early in training, then the harder ones later on. Motivated by this, we start our work by exploring the effect of matching trajectories from different training phases. Specifically, we train expert models for 40 epochs and roughly divide their training trajectories into two parts: the early trajectories  $\{\theta_t^*\,|0 \leq t \leq 20\}$  and the latter ones  $\{\theta_t^*\}_{20} \leq t \leq 40\}$ . Then we perform the distillation by matching these two sets of trajectories under various IPC settings. Experimental results are reported in Figure [2.](#page-2-0) Our observations and relevant analyses are presented as follows.

Observation 1. As shown in Figure [2,](#page-2-0) matching early trajectories works better with small synthetic datasets, but matching late trajectories performs better as the size of the synthetic set grows larger.

Analysis 1. Since DNNs learn to recognize easy patterns early in training and hard patterns later on, we infer that matching early trajectories yields distilled data with easy patterns while matching late trajectories produces hard ones. Combined with the empirical results from Figure [2,](#page-2-0) we can conclude that distilled data with easy patterns perform well for small synthetic sets while data with hard features work better with larger sets. Perhaps unsurprisingly, this highly coincides with a common observation in the area of *dataset pruning*: preserving easy samples works better when very

<span id="page-3-0"></span>few samples are kept, while keeping hard samples works better when the pruned dataset is larger [\(Sorscher et al.,](#page-10-8) [2022\)](#page-10-8).

**Observation [2](#page-2-0).** As can be observed in Figure 2 (a), matching late trajectories leads to poor performance in the low IPC setting. When IPC is high, matching early trajectories will consistently under-mine the performance of the synthetic dataset as the distillation goes on, as can be observed in Figure [2](#page-2-0) (d). Also, as reflected in Figure [2,](#page-2-0) simply choosing to match all trajectories is not a good strategy.

Analysis 2. In low IPC settings, due to distilled data's limited capacity, it is challenging to learn data that models the outliers (hard samples) without neglecting the more plentiful easy samples; since easy samples make up most of the real data distribution, modeling these samples is more efficient performance-wise when IPC is low. Therefore, matching early trajectories (which will generate easy patterns) performs better than matching later ones (for low IPC). Conversely, in high IPC settings, distilling data that models only the easy samples is no longer necessary, and will even perform worse than a random subset of real samples. Thus, we must now consider the less-common hard samples by matching late trajectories (Figure [4\)](#page-7-0). Since previous distillation methods focus on extremely small IPC cases, they tend to be biased towards generating easy patterns, leading to their ineffective in large IPC cases.

Based on the above analyses, to keep dataset distillation effective in both low and high IPC cases, we must calibrate the difficulty of the generated patterns (*i.e.*, avoid generating patterns that are too easy or too difficult). To this end, we propose our method: Difficulty-Aligned Trajectory Matching, or DATM.

### 3.2 DIFFICULTY-ALIGNED TRAJECTORY MATCHING

Since patterns learned by matching earlier trajectories are easier than the later ones, we can control the difficulty of the generated patterns by restricting the trajectory-matching range. Specifically, let  $\tau^* = \{\theta_t^*[0 \le t \le n\}]$  denote an expert trajectory. To control the matching range flexibly, we set a lower bound  $T^-$  and an upper bound  $T^+$  on the sample range of t, such that only parameters within  $\{\theta_t^*\}$  $T^- \le t \le T^+\}$  can be sampled for the matching. Then the trajectory segment used for the matching can be formulated as:

$$
\tau^* = \{ \underbrace{\theta_0^*, \theta_1^*, \cdots}_{\text{too easy}}, \underbrace{\theta_{T-}^*, \cdots, \theta_{T+}^*, \cdots, \theta_n^*}_{\text{matching range}} \}.
$$
\n(3)

To further enrich the information contained in the synthetic dataset, an intuitive choice is using soft labels [\(Hinton et al.,](#page-9-8) [2015\)](#page-9-8). Recently, [Cui et al.](#page-9-2) [\(2023\)](#page-9-2) show that using soft labels to guide the distillation can bring non-trivial improvement for the performance. However, their soft labels are not optimized during the distillation, leading to poor consistency between synthetic data and soft labels. To enable learning labels, we find the following challenges need to be solved:

**Mislabeling.** We use logits  $L_i = f_{\theta^*}(x_i)$  to initialize soft labels, which are generated by the pretrained model  $f_{\theta^*}$  sampled from expert trajectories. However, labels initialized in this way might be incorrect (*i.e.*, target class doesn't have the highest logit score). To avoid mislabeling, we sift through  $\mathcal{D}_{\rm real}$  to find samples that can be correctly classified by model  $f_{\theta^*}$  and use them to construct the subset  $\mathcal{D}_{sub}$ . Then we randomly select samples from  $\mathcal{D}_{sub}$  to initialize  $\mathcal{D}_{syn} = \{(x_i, \hat{y}_i = softmax(L_i))\},$ such that we can avoid the distillation being misguided by the wrong label.

Instability. During the experiments, we found that optimizing soft labels will increase the instability of the distillation when the IPC is low. In low IPC settings, the distillation loss tends to be higher and less stable overall since the smaller synthetic set struggles to induce a proper training trajectory. This issue becomes fatal when labels are optimized during the distillation, as the labels are too fragile to take the wrong guidance brought by the mismatch, leading to increased instability. To alleviate this, we propose to generate only easy patterns in the early distillation phase. After enough easy patterns are embedded into the synthetic data for surrogate models to learn them well, we then gradually generate harder ones. By applying this sequential generation (SG) strategy, the surrogate model can match the expert trajectories better. Accordingly, the distillation becomes more stable.

In practice, to generate only easy patterns at the early distillation stage, we set a floating upper bound  $T$  on the sample range of  $t$ , which is set to be relatively small in the beginning and will be gradually increased as the distillation progresses until it reaches its upper bound  $T^+$ . Overall, the

<span id="page-4-1"></span><span id="page-4-0"></span>

| Dataset             |                |                | CIFAR-10       |                          |                          |                |                | CIFAR-100                |                          |                | Tiny ImageNet  |                |
|---------------------|----------------|----------------|----------------|--------------------------|--------------------------|----------------|----------------|--------------------------|--------------------------|----------------|----------------|----------------|
| <b>IPC</b>          |                | 10             | 50             | 500                      | 1000                     |                | 10             | 50                       | 100                      |                | 10             | 50             |
| Ratio               | 0.02           | 0.2            |                | 10                       | 20                       | 0.2            | $\overline{2}$ | 10                       | 20                       | 0.2            | $\overline{c}$ | 10             |
| Random              | $15.4 \pm 0.3$ | $31.0 \pm 0.5$ | $50.6 \pm 0.3$ | $73.2 \pm 0.3$           | $78.4 \pm 0.2$           | $4.2 \pm 0.3$  | $14.6 \pm 0.5$ | $33.4 \pm 0.4$           | $42.8 \pm 0.3$           | $.4 \pm 0.1$   | $5.0 + 0.2$    | $15.0 \pm 0.4$ |
| DC                  | $28.3 \pm 0.5$ | $44.9 \pm 0.5$ | $53.9 \pm 0.5$ | $72.1 + 0.4$             | $76.6 \pm 0.3$           | $12.8 \pm 0.3$ | $25.2 \pm 0.3$ |                          | $\overline{\phantom{a}}$ |                |                |                |
| DM                  | $26.0 \pm 0.8$ | $48.9 \pm 0.6$ | $63.0 \pm 0.4$ | $75.1 \pm 0.3$           | $78.8 \pm 0.1$           | $11.4 \pm 0.3$ | $29.7 \pm 0.3$ | $43.6 \pm 0.4$           | ۰                        | $3.9 \pm 0.2$  | $12.9 + 0.4$   | $24.1 \pm 0.3$ |
| <b>DSA</b>          | $28.8 \pm 0.7$ | $52.1 \pm 0.5$ | $60.6 \pm 0.5$ | $73.6 \pm 0.3$           | $78.7 \pm 0.3$           | $13.9 \pm 0.3$ | $32.3 \pm 0.3$ | $42.8 \pm 0.4$           | $\overline{\phantom{a}}$ |                |                |                |
| <b>CAFE</b>         | $30.3 \pm 1.1$ | $46.3 \pm 0.6$ | $55.5 \pm 0.6$ | ۰                        | $\overline{\phantom{a}}$ | $12.9 \pm 0.3$ | $27.8 \pm 0.3$ | $37.9 \pm 0.3$           | $\overline{\phantom{a}}$ |                |                |                |
| KIP <sup>1</sup>    | $49.9 \pm 0.2$ | $62.7 \pm 0.3$ | $68.6 \pm 0.2$ |                          | $\overline{\phantom{a}}$ | $15.7 \pm 0.2$ | $28.3 \pm 0.1$ | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ |                |                |                |
| FRePo <sup>1</sup>  | $46.8 \pm 0.7$ | $65.5 \pm 0.4$ | $71.7 \pm 0.2$ |                          | $\overline{\phantom{a}}$ | $28.7 \pm 0.1$ | $42.5 \pm 0.2$ | $44.3 \pm 0.2$           | ۰                        | $15.4 \pm 0.3$ | $25.4 \pm 0.2$ |                |
| RCIG <sup>1</sup>   | $53.9 \pm 1.0$ | $69.1 \pm 0.4$ | $73.5 \pm 0.3$ |                          | -                        | $39.3 \pm 0.4$ | $44.1 \pm 0.4$ | $46.7 \pm 0.3$           | $\overline{\phantom{a}}$ | $25.6 \pm 0.3$ | $29.4 \pm 0.2$ |                |
| MTT <sup>2</sup>    | $46.2 \pm 0.8$ | $65.4 \pm 0.7$ | $71.6 \pm 0.2$ | N                        | $\sqrt{}$                | $24.3 \pm 0.3$ | $39.7 \pm 0.4$ | $47.7 \pm 0.2$           | $49.2 \pm 0.4$           | $8.8 \pm 0.3$  | $23.2 \pm 0.2$ | $28.0 \pm 0.3$ |
| TESLA <sup>2</sup>  | $48.5 \pm 0.8$ | $66.4 \pm 0.8$ | $72.6 \pm 0.7$ | $\overline{\phantom{a}}$ | ↖                        | $24.8 \pm 0.4$ | $41.7 \pm 0.3$ | $47.9 \pm 0.3$           | $49.2 \pm 0.4$           |                |                |                |
| $FTD^{2,3}$         | $46.0 \pm 0.4$ | $65.3 \pm 0.4$ | $73.2 \pm 0.2$ | ↜                        |                          | $24.4 \pm 0.4$ | $42.5 \pm 0.2$ | $48.5 \pm 0.3$           | $49.7 \pm 0.4$           | $10.5 \pm 0.2$ | $23.4 \pm 0.3$ | $28.2 \pm 0.4$ |
| DATM (Ours)         | $46.9 \pm 0.5$ | $66.8 \pm 0.2$ | $76.1 \pm 0.3$ | $83.5 \pm 0.2$           | $85.5 \pm 0.4$           | $27.9 + 0.2$   | $47.2 \pm 0.4$ | $55.0 \pm 0.2$           | $57.5 \pm 0.2$           | $17.1 \pm 0.3$ | $31.1 \pm 0.3$ | $39.7 \pm 0.3$ |
| <b>Full Dataset</b> |                |                | $84.8 \pm 0.1$ |                          |                          |                |                | $56.2 \pm 0.3$           |                          |                | $37.6 \pm 0.4$ |                |

Table 1: Comparison with previous dataset distillation methods on CIFAR-10, CIFAR-100 and Tiny ImageNet. ConvNet is used for the distillation and evaluation. Hilighted results indicate we achieve lossless distillation. Our method consistently out-performs prior works and is the only to achieve lossless distillation. <sup>1</sup>Kernel-based methods use a much larger neural network; we underline their results when they perform best.

<sup>2</sup> Previous TM-based methods perform *worse* than random initialization in higher IPC cases, indicated by **...**  ${}^{3}$ For a fair comparison, we reproduce FTD without using EMA (exponential moving average).

process of sampling the start parameters  $\theta_t^*$  can be formulated as:

$$
\theta_t^* \sim \mathcal{U}(\{\theta_{T^-}^*, \cdots, \theta_T^*\}), \text{ where } T \to T^+.
$$
 (4)

In each iteration, after deciding the value of t, we then sample  $\theta_t^*$  and  $\theta_{t+M}^*$  from expert trajectories as the start parameters and the target parameters for the matching. Then  $\hat{\theta}_{t+N}$  can be obtained by Eq. [2.](#page-2-1) Subsequently, after calculating the matching loss using Eq. [1,](#page-2-2) we perform backpropagation to calculate the gradients and then use them to update the synthetic data  $x_i$  and  $L_i$ , where  $(x_i, \hat{y}_i = \text{softmax}(L_i)) \in \mathcal{D}_{\text{syn}}$ . See Algorithm [1](#page-15-0) for the pseudocode of our method.

# 4 EXPERIMENTS

### 4.1 SETUP

We compare our method with several representative distillation methods including DC [\(Zhao et al.,](#page-11-0) [2020\)](#page-11-0), DM [\(Zhao & Bilen,](#page-11-2) [2023\)](#page-11-2), DSA [\(Zhao & Bilen,](#page-11-8) [2021\)](#page-11-8), CAFE [\(Wang et al.,](#page-10-3) [2022\)](#page-10-3), KIP [\(Nguyen et al.,](#page-10-1) [2020\)](#page-10-1), FRePo [\(Zhou et al.,](#page-11-9) [2022\)](#page-11-9), RCIG [\(Loo et al.,](#page-10-9) [2023\)](#page-10-9), MTT [\(Cazenavette et al.,](#page-9-1) [2022\)](#page-9-1), TESLA [\(Cui et al.,](#page-9-2) [2023\)](#page-9-2), and FTD [\(Du et al.,](#page-9-3) [2023\)](#page-9-3). The evaluations are performed on several popular datasets including CIFAR-10, CIFAR-100 [\(Krizhevsky et al.,](#page-9-9) [2009\)](#page-9-9), and Tiny ImageNet (Le  $\&$  Yang, [2015\)](#page-10-10). We generate expert trajectories in the same way as FTD without modifying the involved hyperparameters. We also use the same suite of differentiable augmentations (Zhao  $\&$  Bilen,  $2021$ ) in the distillation and evaluation stage, which is generally utilized in previous works (Zhao  $\&$ [Bilen,](#page-11-8) [2021;](#page-11-8) [Wang et al.,](#page-10-3) [2022;](#page-10-3) [Cazenavette et al.,](#page-9-1) [2022;](#page-9-1) [Du et al.,](#page-9-3) [2023\)](#page-9-3).

Consistent with previous works, we use networks with instance normalization by default, while networks with batch normalization are indicated with "-BN" (*e.g.*, ConvNet-BN). Without particular specification, we perform distillation using a 3-layer ConvNet for CIFAR-10 and CIFAR-100, while we move up to a depth-4 ConvNet for Tiny ImageNet. We also use LeNet [\(LeCun et al.,](#page-10-11) [1998\)](#page-10-11), AlexNet [\(Krizhevsky et al.,](#page-9-10) [2012\)](#page-9-10), VGG11 [\(Simonyan & Zisserman,](#page-10-12) [2015\)](#page-10-12), and ResNet18 [\(He et al.,](#page-9-11) [2016\)](#page-9-11) for cross-architecture experiments. More details can be found in Section [A.8.](#page-16-0)

### 4.2 MAIN RESULTS

CIFAR-10/100 and Tiny ImageNet. As the results reported in Table [1,](#page-4-0) our method outperforms other methods with the same network architecture in all settings but CIFAR-10 with IPC=1. As can be observed, the improvements brought by previous distillation methods are quickly saturated as the distillation ratio approaches 20%. Especially in CIFAR-10, almost all previous methods have similar or even worse performance than random selection when the ratio is greater than 10%. Benefiting from our difficulty alignment strategy, our method remains effective in high IPC cases. Notably, we

<span id="page-5-3"></span><span id="page-5-2"></span>Image /page/5/Figure/1 description: The image displays four plots labeled (a), (b), and (c). Plot (a) is titled "Initialization" and shows accuracy (%) versus iteration (10^3) for "Ours" (orange line with circles) and "Random selection" (blue line with diamonds). The "Ours" line reaches approximately 44% accuracy at 2.5 x 10^3 iterations, while "Random selection" reaches approximately 42% accuracy at the same point. Plot (b) is titled "Without learning labels" and shows accuracy (%) versus iteration (10^3) for three "Labeling Expert" conditions: 20th epoch (blue line with diamonds), 60th epoch (orange line with circles), and 80th epoch (red line with squares). All three lines show fluctuating accuracy, generally reaching around 75-76% by 10 x 10^3 iterations. Plot (c) is titled "With learning labels" and shows accuracy (%) versus iteration (10^3) for two "Labeling Expert" conditions: 20th epoch (blue line with diamonds) and 60th epoch (orange line with circles). Both lines show a rapid increase in accuracy, reaching around 76% for the 20th epoch and 76.3% for the 60th epoch by 10 x 10^3 iterations. Plot (d) is titled "Distillation cost" and shows accuracy (%) versus iteration (10^3) for two "IPC" values: 50 (blue line with diamonds) and 1000 (orange line with circles). The "IPC=1000" line reaches approximately 85% accuracy at 2.5 x 10^3 iterations, with a cost of 9.6 hrs (3.2 hrs/10^3 iters). The "IPC=50" line reaches approximately 75% accuracy at 2.5 x 10^3 iterations, with a cost of 9.1 hrs (1.3 hrs/10^3 iters).

Figure 3: (a): (CIFAR-100, IPC=10) Synthetic datasets are initialized by randomly sampling data from the original dataset (random selection) or a subset of data that can be correctly classified (ours). Our strategy makes the optimization converge faster. (b): (CIFAR-10, IPC=50) Ablation on learning soft labels, where soft labels are initialized with expert models trained after different epochs. Learning labels relieves us from carefully selecting the labeling expert. (c): (CIFAR-10) The optimization with higher IPC converges in fewer iterations.

<span id="page-5-0"></span>

| Method                | ConvNet | ResNet18 | VGG   | AlexNet | Soft Label | Difficulty Alignment  | Acc   | Label Learning | Sequential Gen.      | Acc  |
|-----------------------|---------|----------|-------|---------|------------|-----------------------|-------|----------------|----------------------|------|
| Random                | 33.46   | 31.95    | 32.18 | 26.65   |            |                       | 48.50 |                |                      | 72.8 |
| <b>MTT</b>            | 45.68   | 42.56    | 41.22 | 40.29   |            |                       | 50.79 |                |                      | 75.0 |
| <b>FTD</b>            | 48.90   | 46.65    | 43.24 | 42.20   |            |                       | 52.96 |                |                      | 75.6 |
| <b>DATM</b>           | 55.03   | 51.71    | 45.38 | 45.74   |            |                       | 55.03 |                |                      | 76.1 |
| (a) CIFAR-100, IPC=50 |         |          |       |         |            | (b) CIFAR-100, IPC=50 |       |                | (c) CIFAR-10, IPC=50 |      |

Table 2: (a): Cross-Architecture evaluation. Our distilled dataset performs well across various unseen networks. (b): Ablation studies on the components of our method; all bring non-trivial improvement. (c): Ablation on learning soft labels and our sequential generation (SG) strategy.

successfully distill CIFAR-10 and CIFAR-100 to 1/5, and Tiny ImageNet to 1/10 their original size without causing any performance drop.

Cross-architecture generalization. Here we evaluate the generalizability of our distilled datasets in various IPC settings. As the results reported in Table [2](#page-5-0) (Left), our distilled dataset performs best on unseen networks when IPC is small, reflecting the good generalizability of the data and labels distilled by our method. Furthermore, we evaluate the generalizability in high IPC settings and compare the performance with two representative coreset selection methods including Glister [\(Killamsetty et al.,](#page-9-12) [2021\)](#page-9-12) and Forgetting [\(Toneva et al.,](#page-10-13) [2018\)](#page-10-13). As shown in Table [4,](#page-6-0) although coreset selection methods are applied case by case, they are not universally beneficial for all networks. Notably, although our synthetic dataset is distilled with ConvNet, it generalizes well on all networks, bringing non-trivial improvement. Furthermore, on CIFAR-100, the improvement of AlexNet is even higher than that of ConvNet. This reflects the overfitting problem of synthetic datasets to distillation networks is somewhat alleviated in higher IPC situations.

### 4.3 ABLATION

Ablation on components of our method. We perform ablation studies by adding the components of our methods one by one to measure their effect. As the results reported in Table [2](#page-5-0) (b,c), all the components of our method bring non-trivial improvement. Especially, when soft labels are utilized, the distillation becomes unstable if our proposed sequential generation strategy is not applied, leading to poor performance and sometimes program crashes.

Soft label. In our method, we use logits generated by the pre-trained model to initialize soft labels, since having an appropriate distribution before the softmax is critical for the optimization of the soft label (Section [A.2\)](#page-12-0). However, using logits will introduce additional information to the distilled dataset [\(Hinton et al.,](#page-9-8)

<span id="page-5-1"></span>

| Dataset   CIFAR-10                                                                                                                                                                                                |                                                        |  | $CIFAR-100$ |                        | Tiny ImageNet |  |  |  |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------|--|-------------|------------------------|---------------|--|--|--|
| <b>IPC</b>                                                                                                                                                                                                        | $\begin{array}{ c c c c c } \hline 1 & 10 \end{array}$ |  |             | 50   1 10 50   1 10 50 |               |  |  |  |
| $\begin{tabular}{c cccc cccc} \hline FTD & 46.0 & 65.3 & 73.2 & 24.4 & 42.5 & 48.5 & 10.5 & 23.4 & 28.2 \\ \hline FTD+ASL & 45.5 & 66.1 & 72.8 & 24.2 & 44.5 & 51.2 & 12.4 & 26.7 & 30.9 \\ \hline \end{tabular}$ |                                                        |  |             |                        |               |  |  |  |
|                                                                                                                                                                                                                   |                                                        |  |             |                        |               |  |  |  |
| DATM 46.9 66.8 76.1 27.9 47.2 53.0 17.1 29.0 33.7                                                                                                                                                                 |                                                        |  |             |                        |               |  |  |  |

Table 3: We assign soft labels (ASL) for datasets distilled by FTD. For fairness, our difficult alignment strategy is not utilized here. Results in red indicate the case when ASL is harmful.

<span id="page-6-2"></span><span id="page-6-0"></span>

| Dataset   | Ratio | Method      | ConvNet      | ConvNet-BN   | ResNet18     | ResNet18-BN  | VGG11        | AlexNet      | LeNet        | MLP          | Avg.         |
|-----------|-------|-------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|--------------|
| CIFAR-10  | 20%   | Random      | 78.38        | 80.25        | 84.58        | 87.21        | 80.81        | 80.75        | 61.85        | 50.98        | 75.60        |
|           |       | Glister     | 62.46        | 70.52        | 81.10        | 74.59        | 78.07        | 70.55        | 56.56        | 40.59        | 66.81        |
|           |       | Forgetting  | 76.27        | 80.06        | 85.67        | 87.18        | 82.04        | 81.35        | 64.59        | 52.21        | 76.17        |
|           |       | <b>DATM</b> | <b>85.50</b> | <b>85.23</b> | <b>87.22</b> | <b>88.13</b> | <b>84.65</b> | <b>85.14</b> | <b>66.70</b> | <b>52.40</b> | <b>79.37</b> |
|           |       | ↑           | $+7.12$      | $+4.98$      | $+2.64$      | $+0.92$      | $+3.84$      | $+4.39$      | $+4.85$      | $+1.42$      | $+3.77$      |
| CIFAR-100 | 20%   | Random      | 42.80        | 46.38        | 47.48        | 55.62        | 42.69        | 38.05        | 25.91        | 20.66        | 39.95        |
|           |       | Glister     | 35.45        | 37.13        | 42.49        | 46.14        | 43.06        | 28.58        | 23.33        | 17.08        | 34.16        |
|           |       | Forgetting  | 45.52        | 49.99        | 51.44        | 54.65        | 43.28        | 43.47        | 27.22        | 22.90        | 42.30        |
|           |       | <b>DATM</b> | <b>57.50</b> | <b>57.75</b> | <b>57.98</b> | <b>63.34</b> | <b>55.10</b> | <b>55.69</b> | <b>33.57</b> | <b>26.39</b> | <b>50.92</b> |
|           |       | ↑           | $+14.70$     | $+11.37$     | $+10.50$     | $+7.72$      | $+12.41$     | $+17.64$     | $+7.66$      | $+5.73$      | $+10.97$     |
| TI        | 10%   | Random      | 15.00        | 24.21        | 17.73        | 28.07        | 22.51        | 14.03        | 9.25         | 5.85         | 17.08        |
|           |       | Glister     | 17.32        | 19.77        | 18.84        | 23.12        | 19.10        | 11.68        | 8.84         | 3.86         | 15.32        |
|           |       | Forgetting  | 20.04        | 23.83        | 19.38        | 28.88        | 23.77        | 12.13        | 12.06        | 5.54         | 18.20        |
|           |       | <b>DATM</b> | <b>39.68</b> | <b>40.32</b> | <b>36.12</b> | <b>43.14</b> | <b>38.35</b> | <b>35.10</b> | <b>12.41</b> | <b>9.02</b>  | <b>31.76</b> |
|           |       | ↑           | $+24.68$     | $+16.11$     | $+18.39$     | $+15.07$     | $+15.84$     | $+21.07$     | $+3.16$      | $+3.17$      | $+14.68$     |

Table 4: We evaluate our distilled lossless datasets on unseen networks and compare them with two coreset selection methods. Results worse than random selection are indicated with red color. ↑ denotes the performance improvement brought by our method compared with random selection. TI denotes Tiny ImageNet.

[2015\)](#page-9-8). To see if this information can be directly integrated into the distilled datasets, we assign soft labels for datasets distilled by FTD [\(Du et al.,](#page-9-3) [2023\)](#page-9-3).

As shown in Table [3,](#page-5-1) directly assigning soft labels for the distilled datasets will even hurt its performance when the number of categories in the classification problem is small. For CIFAR-100 and Tiny ImageNet, although assigning soft labels slightly improves the performance of FTD, there is still a huge gap between its performance and ours. This is because the soft labels synthesized by our method are optimized constantly during the distillation, leading to better consistency between the synthetic data and their labels.

Furthermore, the information contained in logits varies with the capacity of the teacher model [\(Zong](#page-11-10)) [et al.,](#page-11-10) [2023;](#page-11-10) [Cui et al.,](#page-9-2) [2023\)](#page-9-2). In the experiments reported in Figure [3](#page-5-2) (b), we use models trained after different epochs to initialize the soft labels. In the beginning, this difference has non-trivial influences on the performance of the synthetic datasets. However, the performance gaps soon disappear as the distillation goes on if labels are optimized during the distillation. This indicates learning labels relieves us from carefully selecting models to initialize soft labels. Moreover, as can be observed in Figure  $3$  (b), when soft labels are not optimized, the distillation becomes less stable, leading to the poor performance of the distilled dataset. Because using unoptimized soft labels will enlarge the discrepancy between the training trajectories over the synthetic dataset and the original one, considering the experts are trained with one-hot labels. More analyses are attached in Section [A.2.2.](#page-12-1)

Synthetic data initialization. To avoid mislabeling, we initialize the synthetic dataset by randomly sampling data from a subset of the original dataset, which only contains samples that can be correctly classified by the model used for initializing soft labels. The process of constructing the subset can be viewed as a simple coreset selection. Here we perform an ablation study to see its effect. Specifically, synthetic datasets are either initialized by randomly sampling data from the original dataset (random selection) or a subset of data that can be correctly classified by a pre-trained ConvNet (ours).

As shown in Fig [3](#page-5-2) (a), our initialization strategy can significantly speed up the convergence of the optimization. This is because data selected by our strategy are relatively easier for DNNs to learn. Thus, models trained on these easier samples will perform better when only limited training data are provided [\(Sorscher et al.,](#page-10-8) [2022\)](#page-10-8). Although this gap is gradually bridged as the distillation goes on, our initialization strategy can be utilized as a distillation speed-up technique.

# 5 EXTENSION

<span id="page-6-1"></span>

## 5.1 VISUALIZATION

For a better understanding of easy patterns and hard patterns, we visualize the distilled images and discuss their properties. In Figure [4,](#page-7-0) we visualize the images synthesized by matching early trajectories and late trajectories under the same IPC setting, where easy patterns and hard ones are learned respectively. In Figure [5,](#page-8-0) we visualize the images distilled under different IPC settings.

<span id="page-7-2"></span><span id="page-7-0"></span>Image /page/7/Picture/1 description: The image displays three grids, each containing six smaller images. The first grid shows an airplane, the rear of a car, a bird, a person's face, a horse, and a body of water. The second grid features a silhouette of a B-2 bomber, the rear of a car, a close-up of a car's taillight, a black dog, a horse, and a blurry image of a road. The third grid contains a silhouette of a B-2 bomber, the rear of a car, a close-up of a car's taillight, a black dog, a horse, and a blurry image of a road.

Match Early Trajectories **Match Late Trajectories** Original Images Match Late Trajectories

Image /page/7/Picture/4 description: A grid of nine images, each depicting a different scene. The top row shows a blurry blue sky, a close-up of a dark object, and a colorful abstract pattern. The middle row features a black dog running, a brown and white horse standing in a field, and a blurry image of a car. The bottom row displays a close-up of a dog's face, a horse in profile, and a blurry image of a car's interior.

Figure 4: We perform the distillation on CIFAR-10 with IPC=50 by matching either early trajectories  $\{\theta_t | 0 \le t \le 10\}$  or late trajectories  $\{\theta_t | 30 \le t \le 40\}$ . All synthetic images are optimized 1000 times. Matching earlier trajectories will blur the details of the target object and change the color more drastically.

<span id="page-7-1"></span>

| <b>E\D</b> | Random | ConvNet      | ResNet18 | <b>E\D</b> | Random | ConvNet      | ResNet18 | <b>E\D</b> | Random | ConvNet      | ResNet18     |
|------------|--------|--------------|----------|------------|--------|--------------|----------|------------|--------|--------------|--------------|
| ConvNet    | 31.00  | <b>68.28</b> | 44.54    | ConvNet    | 55.55  | <b>76.08</b> | 59.08    | ConvNet    | 78.38  | <b>85.50</b> | <b>84.64</b> |
| ResNet18   | 26.70  | <b>48.66</b> | 45.28    | ResNet18   | 54.96  | <b>66.27</b> | 61.18    | ResNet18   | 84.58  | <b>87.22</b> | <b>87.70</b> |
| VGG11      | 31.63  | <b>45.93</b> | 39.68    | VGG11      | 48.72  | <b>59.43</b> | 50.72    | VGG11      | 80.81  | <b>84.65</b> | <b>84.85</b> |
| <b>MLP</b> | 26.86  | <b>33.39</b> | 29.17    | <b>MLP</b> | 36.66  | <b>33.29</b> | 34.41    | <b>MLP</b> | 50.98  | <b>52.40</b> | <b>54.01</b> |
| $IPC=10$   |        |              |          | $IPC = 50$ |        |              |          | $IPC=1000$ |        |              |              |

Table 5: We use ConvNet and ResNet18 to perform the distillation (D) on CIFAR-10 with various IPC settings. Then evaluations (E) are performed using networks with various architectures. As IPC increases, datasets distilled using ResNet18 perform relatively better.

As can be observed in Figure [4,](#page-7-0) compared with hard patterns, the learned easy patterns drive the synthetic images to move farther from their initialization and tend to blend the target object into the background. Although this process seems to make the image more informative, it blurs the texture and fine geometric details of the target object, making it harder for networks to learn to identify non-typical samples. This helps explain why generating easy patterns turned out to be harmful in high IPC cases. However, generating easy patterns performs well in the regime of low IPC, where the optimal solution is to model the most dense areas of the target category's distribution given the limited data budget. For example, as shown in Figure [5,](#page-8-0) the synthetic images collapse to almost only contain color and vague shape information when IPC is extremely low, which helps networks learn to identify easy samples from this basic property.

Furthermore, we find matching late trajectories yields distilled images that contain more fine details. For example, as can be observed in Figure [4,](#page-7-0) matching late trajectories transforms the simple background in the *dog* images into a more informative one and gives more texture details to the *dog* and *horse*. This transformation helps networks learn to identify outlier (hard) samples; hence, matching late trajectories is a better choice in high IPC cases.

### 5.2 DISTILLATION COST

In this work, we scale dataset distillation to high IPC cases. Surprisingly, we find the distillation cost does not increase linearly with IPC, since the optimization converges faster in large IPC cases. This is because we match only late trajectories in high IPC cases, where the learned hard patterns only make a few changes on the images, as we have analyzed in Section [5.1](#page-6-1) and can be observed in Figure [5.](#page-8-0) In practice, as reflected in Figure [3](#page-5-2) (c), although the distillation with IPC=1000 needs to optimize 20x more data than the case with IPC=50, the former one's cost is only 1.05 times higher.

## 5.3 DISTILLATION BACKBONE NETWORKS

So far, almost all representative distillation methods choose to use ConvNet to perform the distillation [\(Zhao & Bilen,](#page-11-8) [2021;](#page-11-8) [Cazenavette et al.,](#page-9-1) [2022;](#page-9-1) [Loo et al.,](#page-10-9) [2023\)](#page-10-9). Using other networks as the distillation backbone will result in non-trivial performance degradation [\(Liu et al.,](#page-10-0) [2023b\)](#page-10-0). What makes ConvNet so effective for distillation remains an open question.

Here we offer an answer from our perspective: part of the specialness of ConvNet comes from its low capacity. In general, networks with more capacity can learn more complex patterns. Accordingly, when used as distilling networks, their generated patterns are relatively harder for DNNs to learn.

<span id="page-8-1"></span><span id="page-8-0"></span>Image /page/8/Figure/1 description: This image displays a comparison of image reconstruction quality across different datasets and parameters. The left side shows results for CIFAR-100, with an original image of a lion, followed by reconstructions with IPC=1, IPC=10, and IPC=100. The right side shows results for Tiny ImageNet, with an original image of a goldfish, followed by reconstructions with IPC=1, IPC=10, and IPC=50. The reconstructions generally improve in quality as the IPC value increases, with the highest IPC values yielding the most recognizable images.

Figure 5: Visualization of the synthetic datasets distilled with different IPC settings. As IPC increases, synthetic images move less far from their initialization.

As we have analyzed in section [3.1,](#page-2-3) in small IPC cases (where previous distillation methods focus their attention), most improvement comes from the easy patterns generated on the synthetic data. Thus networks with more capacity such as ResNet will perform worse than ConvNet when IPC is low because their generated patterns are harder for DNNs to learn. However, in high IPC cases, where hard patterns play an important role, using stronger networks should perform relatively better. To verify this, we use ResNet18 and ConvNet to perform distillation on CIFAR-10 with different IPC settings. As shown in Table [5,](#page-7-1) when IPC is low, ConvNet performs much better than ResNet18 as the distillation network. However, when IPC reaches 1000, ResNet18 has a comparable or even better performance compared with ConvNet.

# 6 RELATED WORK

Dataset distillation introduced by [Wang et al.](#page-11-11) [\(2018\)](#page-11-11) is naturally a bi-level optimization problem, which aims at distilling a large dataset into a small one without causing performance drops. The following works can be divided into two types according to their mechanism:

Kernel-based distillation methods use kernel ridge-regression with NTK [\(Lee et al.,](#page-10-14) [2019\)](#page-10-14) to obtain a closed-form solution for the inner optimization [\(Nguyen et al.,](#page-10-1) [2020\)](#page-10-1). By doing so, dataset distillation can be formulated as a single-level optimization problem. The following works have significantly reduced the training cost [\(Zhou et al.,](#page-11-9) [2022\)](#page-11-9) and improved the performance [\(Loo et al.,](#page-10-15) [2022;](#page-10-15) [2023\)](#page-10-9). However, since the heavy resource consumption of inversing matrix operation, it is hard to scale kernel-based methods to larger IPC.

Matching-based methods minimize defined metrics of surrogate models learned from the synthetic dataset and the original one. According to the definition of the metric, they can be divided into four categories: based on matching gradients [\(Zhao et al.,](#page-11-0) [2020;](#page-11-0) [Kim et al.,](#page-9-0) [2022;](#page-9-0) [Zhang et al.,](#page-11-1) [2023\)](#page-11-1), features [\(Wang et al.,](#page-10-3) [2022\)](#page-10-3), distribution [\(Zhao & Bilen,](#page-11-2) [2023;](#page-11-2) [Zhao et al.,](#page-11-3) [2023\)](#page-11-3), and training trajectories [\(Cazenavette et al.,](#page-9-1) [2022;](#page-9-1) [Cui et al.,](#page-9-2) [2023;](#page-9-2) [Du et al.,](#page-9-3) [2023\)](#page-9-3). So far, trajectory matchingbased methods have shown impressive performance on every benchmark with low IPC [\(Cui et al.,](#page-9-6) [2022;](#page-9-6) [Yu et al.,](#page-11-12) [2023\)](#page-11-12). In this work, we further explore and show its great power in higher IPC cases.

# 7 CONCLUSION AND DISCUSSION

In this work, we find the difficulty of patterns generated by dataset distillation algorithms should be aligned with the size of the synthetic dataset, which is the key to keeping them effective in both lowand high-IPC cases. Building upon this insight, our method excels not only in low IPC cases but also maintains its efficacy in high IPC scenarios, achieving lossless dataset distillation for the first time.

However, our distilled data are only *lossless* for the distillation backbone network: when evaluating them with other networks, the performance drops still exist. We think this is because models with different capacities need varying amounts of training data. How to overcome this issue is still a challenging problem. Moreover, it is hard to scale TM-based methods to large datasets due to its high distillation cost. How to improve its efficiency would be the goal of our future work.

Acknowledgements. This work is supported by the National Research Foundation, Singapore under its AI Singapore Programme (AISG Award No: AISG2-PhD-2021-08- 008). This work is also supported in part by the National Key R&D Program of China (NO.2022ZD0160100 and NO.2022ZD0160101). Part of Hui Li's work is supported by the National Natural Science Foundation of China (61932015), Shaanxi Innovation Team project (2018TD-007), Higher Education Discipline Innovation 111 project (B16037). Yang You's research group is being sponsored by NUS startup grant (Presidential Young Professorship), Singapore MOE Tier-1 grant, ByteDance grant, ARCTIC grant, SMI grant (WBS number: A-8001104-00-00), Alibaba grant, and Google grant for TPU usage. We thank Bo Zhao for valuable discussions and feedback.

#### **REFERENCES**

- <span id="page-9-7"></span>Devansh Arpit, Stanisław Jastrz˛ebski, Nicolas Ballas, David Krueger, Emmanuel Bengio, Maxinder S Kanwal, Tegan Maharaj, Asja Fischer, Aaron Courville, Yoshua Bengio, et al. A closer look at memorization in deep networks. In *ICML*, 2017. [2,](#page-1-0) [3](#page-2-4)
- <span id="page-9-13"></span>Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. In *NeurIPS Workshop*, 2020. [16](#page-15-1)
- <span id="page-9-1"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022. [1,](#page-0-0) [2,](#page-1-0) [5,](#page-4-1) [8,](#page-7-2) [9,](#page-8-1) [16,](#page-15-1) [17](#page-16-1)
- <span id="page-9-5"></span>Dingfan Chen, Raouf Kerkouche, and Mario Fritz. Private set generation with discriminative information. *NeurIPS*, 2022. [1](#page-0-0)
- <span id="page-9-14"></span>Xuxi Chen, Yu Yang, Zhangyang Wang, and Baharan Mirzasoleiman. Data distillation can be like vodka: Distilling more times for better quality. In *ICLR*, 2024. [16](#page-15-1)
- <span id="page-9-6"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. In *NeurIPS*, 2022. [2,](#page-1-0) [9,](#page-8-1) [17](#page-16-1)
- <span id="page-9-2"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *ICML*, 2023. [1,](#page-0-0) [4,](#page-3-0) [5,](#page-4-1) [7,](#page-6-2) [9,](#page-8-1) [18](#page-17-0)
- <span id="page-9-4"></span>Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *ICML*, 2022. [1](#page-0-0)
- <span id="page-9-3"></span>Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *CVPR*, 2023. [1,](#page-0-0) [2,](#page-1-0) [5,](#page-4-1) [7,](#page-6-2) [9,](#page-8-1) [14,](#page-13-0) [17](#page-16-1)
- <span id="page-9-11"></span>Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, 2016. [5](#page-4-1)
- <span id="page-9-8"></span>Geoffrey Hinton, Oriol Vinyals, and Jeffrey Dean. Distilling the knowledge in a neural network. In *NeurIPS Workshop*, 2015. [4,](#page-3-0) [6](#page-5-3)
- <span id="page-9-15"></span>Wei Jin, Lingxiao Zhao, Shichang Zhang, Yozen Liu, Jiliang Tang, and Neil Shah. Graph condensation for graph neural networks. In *ICLR*, 2022. [16](#page-15-1)
- <span id="page-9-12"></span>Krishnateja Killamsetty, Durga Sivasubramanian, Ganesh Ramakrishnan, and Rishabh Iyer. Glister: Generalization based data subset selection for efficient and robust learning. In *AAAI*, 2021. [6](#page-5-3)
- <span id="page-9-0"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *ICML*, 2022. [1,](#page-0-0) [9](#page-8-1)
- <span id="page-9-9"></span>Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [5](#page-4-1)
- <span id="page-9-10"></span>Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. In *NeurIPS*, 2012. [5](#page-4-1)

<span id="page-10-10"></span>Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. 2015. [5](#page-4-1)

- <span id="page-10-11"></span>Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 1998. [5](#page-4-1)
- <span id="page-10-14"></span>Jaehoon Lee, Lechao Xiao, Samuel Schoenholz, Yasaman Bahri, Roman Novak, Jascha Sohl-Dickstein, and Jeffrey Pennington. Wide neural networks of any depth evolve as linear models under gradient descent. In *NeurIPS*, 2019. [9](#page-8-1)
- <span id="page-10-17"></span>Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *NeurIPS*, 2022. [16](#page-15-1)
- <span id="page-10-19"></span>Songhua Liu, Jingwen Ye, Runpeng Yu, and Xinchao Wang. Slimmable dataset condensation. In *CVPR*, 2023a. [16](#page-15-1)
- <span id="page-10-0"></span>Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *ICCV*, 2023b. [1,](#page-0-0) [8,](#page-7-2) [16](#page-15-1)
- <span id="page-10-15"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, 2022. [9](#page-8-1)
- <span id="page-10-9"></span>Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. In *ICML*, 2023. [5,](#page-4-1) [8,](#page-7-2) [9](#page-8-1)
- <span id="page-10-4"></span>Wojciech Masarczyk and Ivona Tautkute. Reducing catastrophic forgetting with learning on synthetic data. In *CVPR Workshop*, 2020. [1](#page-0-0)
- <span id="page-10-1"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. In *ICLR*, 2020. [1,](#page-0-0) [5,](#page-4-1) [9](#page-8-1)
- <span id="page-10-2"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021. [1](#page-0-0)
- <span id="page-10-5"></span>Andrea Rosasco, Antonio Carta, Andrea Cossu, Vincenzo Lomonaco, and Davide Bacciu. Distilled replay: Overcoming forgetting through synthetic samples. In *International Workshop on Continual Semi-Supervised Learning*, 2021. [1](#page-0-0)
- <span id="page-10-12"></span>Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. In *ICLR*, 2015. [5](#page-4-1)
- <span id="page-10-8"></span>Ben Sorscher, Robert Geirhos, Shashank Shekhar, Surya Ganguli, and Ari Morcos. Beyond neural scaling laws: beating power law scaling via data pruning. In *NeurIPS*, 2022. [4,](#page-3-0) [7,](#page-6-2) [15](#page-14-0)
- <span id="page-10-7"></span>Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *ICML*, 2020. [1](#page-0-0)
- <span id="page-10-6"></span>Ilia Sucholutsky and Matthias Schonlau. Secdd: Efficient and secure method for remotely training neural networks (student abstract). In *AAAI*, 2021a. [1](#page-0-0)
- <span id="page-10-16"></span>Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *International Joint Conference on Neural Networks*, 2021b. [16](#page-15-1)
- <span id="page-10-13"></span>Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. In *ICLR*, 2018. [6](#page-5-3)
- <span id="page-10-3"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022. [1,](#page-0-0) [5,](#page-4-1) [9](#page-8-1)
- <span id="page-10-18"></span>Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *arXiv*, 2023. [16](#page-15-1)

- <span id="page-11-6"></span>Ruochen Wang, Minhao Cheng, Xiangning Chen, Xiaocheng Tang, and Cho-Jui Hsieh. Rethinking architecture selection in differentiable nas. In *ICLR*, 2021. [1](#page-0-0)
- <span id="page-11-11"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [9,](#page-8-1) [16](#page-15-1)
- <span id="page-11-16"></span>Xindi Wu, Byron Zhang, Zhiwei Deng, and Olga Russakovsky. Vision-language dataset distillation. *arXiv preprint arXiv:2308.07545*, 2023. [16](#page-15-1)
- <span id="page-11-5"></span>Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. Feddm: Iterative distribution matching for communication-efficient federated learning. In *CVPR*, 2023. [1](#page-0-0)
- <span id="page-11-13"></span>Beining Yang, Kai Wang, Qingyun Sun, Cheng Ji, Xingcheng Fu, Hao Tang, Yang You, and Jianxin Li. Does graph distillation see like vision dataset counterpart? In *NeurIPS*, 2023. [16](#page-15-1)
- <span id="page-11-12"></span>Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023. [9](#page-8-1)
- <span id="page-11-1"></span>Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. In *CVPR*, 2023. [1,](#page-0-0) [9,](#page-8-1) [16](#page-15-1)
- <span id="page-11-14"></span>Tianle Zhang, Yuchen Zhang, Kun Wang, Kai Wang, Beining Yang, Kaipeng Zhang, Wenqi Shao, Ping Liu, Joey Tianyi Zhou, and Yang You. Two trades is not baffled: Condensing graph via crafting rational gradient matching, 2024a. [16](#page-15-1)
- <span id="page-11-15"></span>Yuchen Zhang, Tianle Zhang, Kai Wang, Ziyao Guo, Yuxuan Liang, Xavier Bresson, Wei Jin, and Yang You. Navigating complexity: Toward lossless graph condensation via expanding window matching. *arXiv preprint arXiv:2402.05011*, 2024b. [16](#page-15-1)
- <span id="page-11-8"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021. [5,](#page-4-1) [8](#page-7-2)
- <span id="page-11-2"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023. [1,](#page-0-0) [5,](#page-4-1) [9](#page-8-1)
- <span id="page-11-0"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2020. [1,](#page-0-0) [5,](#page-4-1) [9](#page-8-1)
- <span id="page-11-3"></span>Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *CVPR*, 2023. [1,](#page-0-0) [9](#page-8-1)
- <span id="page-11-7"></span>Daquan Zhou, Kai Wang, Jianyang Gu, Xiangyu Peng, Dongze Lian, Yifan Zhang, Yang You, and Jiashi Feng. Dataset quantization. *arXiv preprint arXiv:2308.10524*, 2023. [2](#page-1-0)
- <span id="page-11-4"></span>Yanlin Zhou, George Pu, Xiyao Ma, Xiaolin Li, and Dapeng Wu. Distilled one-shot federated learning. *arXiv preprint arXiv:2009.07999*, 2020. [1](#page-0-0)
- <span id="page-11-9"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *NeurIPS*, 2022. [5,](#page-4-1) [9](#page-8-1)
- <span id="page-11-10"></span>Martin Zong, Zengyu Qiu, Xinzhu Ma, Kunlin Yang, Chunya Liu, Jun Hou, Shuai Yi, and Wanli Ouyang. Better teacher better student: Dynamic prior knowledge for knowledge distillation. In *ICLR*, 2023. [7](#page-6-2)

## A APPENDIX

## A.1 SOFT LABEL DISTRIBUTION

To observe the changes in soft labels' distribution during the distillation, we record the standard deviation (std) of soft labels (after softmax) for each synthetic image, and report the average value of their std in Figure [6.](#page-13-1) As can be observed, for all datasets, their labels' std tends to increase as the distillation goes on. However, this increase does not arise due to the diversity between the values of soft labels becoming larger, but the values of non-target categories' labels are suppressed. Since the value of the target category is much higher than others, to facilitate observation, we only report the values of non-target categories in Figure [7.](#page-13-2) As can be observed, after the distillation, the values of non-target categories' labels are suppressed more drastically when IPC is smaller. This is because, in low IPC cases, basic patterns of target category are embedded into the synthetic data since only early expert trajectories are used for the matching. Accordingly, the model becomes more confident that the generated sample belongs to the target category.

<span id="page-12-0"></span>

## A.2 SOFT LABEL INITIALIZATION

We have tried to initialize soft labels with the original one-hot labels and directly optimize their values during the distillation, but the distillation soon crashed. We also have tried to add a softmax layer on it. However, the distillation is still not stable. After initializing labels with class probabilities calculated using softmax and logits output by a pre-trained model, finally, soft labels can be optimized stably during the distillation.

For labels with a given distribution, their values before softmax can be different. In this case, due to the utilization of softmax, when performing backpropagation, the gradients of pre-softmax logits will also be different even if their values after softmax are the same. Through experiments, we find using an appropriate distribution before softmax to initialize soft labels is crucial to maintaining the stability of the distillation. We have also tried to modify the distribution of logits without changing their values after softmax. However, we see this operation will greatly increase the instability of distillation. Moreover, we have tried to scale the values of logits during the initialization, which also leads to the degradation of performance.

## A.2.1 STABABILITY

In the manuscript, we propose to generate easy and hard patterns sequentially to make the distillation stable enough for learning soft labels. The insight here is enabling the surrogate model to learn more easy patterns through the finite training steps and limited samples in inner optimization, such that the model can match the expert trajectories better. We find that simply increasing the update times in inner optimization can also stabilize the distillation. Because the surrogate model can learn easy patterns better through a longer learning process. However, increasing the update times in inner optimization will increase the memory requirement and the training cost. Thus we use the method introduced in the manuscript by default.

<span id="page-12-1"></span>

## A.2.2 SOFT LABEL OPTIMIZATION

We can choose to only replace original one-hot labels with soft labels but don't optimize them during the distillation. However, this will make the surrogate model harder to match the expert training trajectories, because the expert trajectories are trained with one-hot labels. As reflected in Figure [8](#page-14-1) (Left), when soft labels are not optimized, the matching loss is higher than using one-hot labels. Although using unoptimized soft labels still performs better than one-hot labels because of the additional information contained in the soft labels, its performance is undermined by the under-matching.

The under-matching issue can be alleviated by optimizing soft labels during the distillation. As can be observed in Figure [8](#page-14-1) (Left), when soft labels are optimized during the distillation, the matching loss becomes lower than using one-hot labels. Accordingly, the performance is improved, as can be observed in Figure [8](#page-14-1) (Right).

<span id="page-13-1"></span><span id="page-13-0"></span>Image /page/13/Figure/1 description: The image displays three line graphs side-by-side, each plotting the average standard deviation against the iteration number (in thousands). The first graph, titled "CIFAR-10", shows an orange line that starts at approximately 0.2905 at iteration 0, rises sharply to about 0.293 at iteration 1, and then gradually increases to a peak of around 0.295 by iteration 4, before leveling off and slightly decreasing to about 0.2945 by iteration 10. The second graph, titled "CIFAR-100", features a blue line that begins at roughly 0.066 at iteration 0, steadily climbs to approximately 0.079 by iteration 10. The third graph, titled "Tiny ImageNet", presents an orange line that starts at about 0.032 at iteration 0, shows a steep increase to around 0.043 at iteration 1, and continues to rise more gradually to approximately 0.058 by iteration 10.

Figure 6: Visualization of the changing of soft labels in various datasets where IPC=50. The average standard deviation of soft labels tends to increase as the distillation goes on.

<span id="page-13-2"></span>Image /page/13/Figure/3 description: This image contains three bar charts side-by-side. The first bar chart, titled "0 Iterations", displays blue bars representing label values for different categories. The categories range from 0 to 8, and the label values on the y-axis range from 0.000 to 0.006. The tallest bar is at category 2, reaching approximately 0.0055. The second bar chart, titled "10K Iterations, IPC=50", displays orange bars. The categories also range from 0 to 8, and the y-axis label values are the same. The bars are much shorter than in the first chart, with the tallest at category 1 reaching approximately 0.001. The third bar chart, titled "10K Iterations, IPC=1000", also displays orange bars. The tallest bar is at category 1, reaching approximately 0.0023, followed by a bar at category 3 reaching approximately 0.0012. The remaining bars are very short.

Figure 7: Visualization of the distribution of non-target soft labels of a synthetic image initialized with the same soft labels and image but distilled with different IPC settings. The values of non-target categories are suppressed more drastically when IPC is smaller.

### A.3 ABLATION ON SYNTHETIC STEPS

We find increasing the synthetic steps  $N$  (Algorithm [1\)](#page-15-0) will bring more performance gain when soft labels are utilized, as reflected in Figure [9](#page-14-2) (Left). This is because the optimization of the surrogate model in the inner optimization affects how well it can match the expert trajectories. When soft labels are not utilized, the information contained in the synthetic dataset is relatively limited, thus the surrogate barely benefits from a longer learning process. Moreover, we find increasing the synthetic steps  $N$  can also bring improvement in high IPC cases. As shown in Figure [9](#page-14-2) (middle), setting  $N=80$  performs best in the case with IPC=1000, where the batch size is set to 1000. In this case, in every iteration, the parameters of the surrogate model are optimized over 80K images (10K unduplicated images) contained in the synthetic dataset, while the target parameters are obtained by the optimization over 100k images (50k unduplicated images) contained in the original datasets. In this case, although the *length* of the training trajectory over the synthetic dataset  $\mathcal{D}_{syn}$  and the original dataset  $\mathcal{D}_{real}$  are similar, matching trajectories still can improve the training performance of the synthetic datasets. Based on this observation, we conjecture that the key to keeping TM-based methods effective is to ensure the number of unduplicated images contained in  $\mathcal{D}_{syn}$  is smaller than that of  $\mathcal{D}_{\text{real}}$ , rather than use the *short* trajectory trained on  $\mathcal{D}_{\text{syn}}$  to match the *longer* one optimized over  $\mathcal{D}_{\text{real}}$ .

### A.4 PREVIOUS TM-BASED METHODS IN LARGE IPC SETTINGS

We have tried to use previous TM-based methods to perform the distillation in larger IPC settings. The distillation logs of FTD [\(Du et al.,](#page-9-3) [2023\)](#page-9-3) are reported in Figure [9.](#page-14-2) As can be observed, FTD will undermine the training performance of the datasets in larger IPC cases. We have tried to tune its hyper-parameters including the learning rate, batch size, synthetic steps, and the upper bound of the sample range, but the effort can only slow down the rate of performance degradation it arose. Without aligning the difficulty of the generated patterns with the size of the synthetic datasets, previous TM-based methods can not keep being effective in high IPC settings.

<span id="page-14-1"></span><span id="page-14-0"></span>Image /page/14/Figure/1 description: The image contains two plots side-by-side, both titled "CIFAR-100, IPC=10". The left plot is a line graph showing "Matching Loss" on the y-axis and "Iteration (k)" on the x-axis, ranging from 0 to 10. There are three lines: a green line with diamond markers labeled "w/ fixed one-hot labels", a blue line with square markers labeled "w/ learning soft labels", and an orange line with circle markers labeled "w/o learning soft labels". The matching loss decreases for all lines as the iteration increases, with the blue line showing the lowest loss. The right plot is a line graph showing "Acc (%)" on the y-axis and "Iteration (k)" on the x-axis, also ranging from 0 to 10. The same three lines are present. The accuracy increases for all lines as the iteration increases, with the blue line showing the highest accuracy.

Figure 8: Left: Logs of the matching loss (smoothed with EMA), where the labels of the synthetic dataset are either one-hot labels, unoptimized soft labels, or soft labels that are optimized during the distillation. Right: Logs of performance of the distilled datasets. Learning soft labels during the distillation enables surrogate models to match expert trajectories better. Accordingly, its synthetic datasets have a better performance.

<span id="page-14-2"></span>Image /page/14/Figure/3 description: The image displays three plots. The first plot is a bar chart comparing accuracy with and without soft labels for different values of N (20, 40, 80). Without soft labels, accuracies are around 43-44%. With soft labels, accuracies increase to around 46-49%. The second plot is a line graph showing accuracy versus synthetic steps (N) for IPC=500 and IPC=1000. Accuracy increases with synthetic steps for both IPC values, with IPC=1000 generally yielding higher accuracy. The third plot is a line graph showing accuracy versus iteration (10^3) for IPC=500 and IPC=1000. Both lines show fluctuations, with IPC=1000 generally performing slightly better or similarly to IPC=500.

Figure 9: Left: (CIFAR-100, IPC=10) Ablation on synthetic step N, distillation with soft labels benefits more from a larger N. Middle: (CIFAR-10) Distillation in larger IPC settings can still benefit from a higher larger  $N$ . Right: Distillation log of FTD on CIFAR-10 with larger IPC. The performance of the synthetic datasets keeps being degraded as the distillation goes on.

<span id="page-14-3"></span>

## A.5 MORE DETAILED ANALYSIS ON MATCHING LOSS

Here we provide more results and additional analysis about the matching loss over expert trajectories. As can be observed in Figure [10,](#page-16-2) initially, the matching loss over the former part of the trajectories is always lower than the one over the later part. This indicates earlier trajectories are relatively easier to match compared with the later ones. In other words, the patterns that surrogate models need to learn to match the early trajectories are relatively easier.

Moreover, it is interesting to observe that matching later trajectories can also reduce the matching loss over early trajectories in high IPC cases. We hypothesize that this is because, in the late training phases, DNNs just *prefer* to learn hard patterns to help identify the outliers, while a few easy patterns are also learned in late training phases. From this perspective, TM-based methods might not be the most efficient way to distill datasets with large IPC, since matching later trajectories still will generate a few easy patterns.

We can also find that when IPC is small, matching late trajectories will raise the matching loss over the early trajectories. This indicates generating hard patterns is harmful for the model to learn basic (easy) patterns to obtain the basic capacity to perform the classification when data is limited. This coincide with the observation in the *dataset pruning* area: preserving hard samples perform worse when only limited samples are kept [\(Sorscher et al.,](#page-10-8) [2022\)](#page-10-8).

## A.6 GUIDANCE FOR ALIGNING DIFFICULTY

Our difficulty alignment aims at letting the models trained on the synthetic dataset learn as many hard patterns as possible, without compromising their capacity to classify easy patterns. For TM-based methods, this can be quantified by the matching loss over a distillation-uninvolved expert trajectory, as we have analyzed in section [A.5.](#page-14-3) Specifically, we want to add patterns that can help to reduce the matching loss over the latter part of the expert trajectory without increasing the matching

<span id="page-15-1"></span>

### Algorithm 1: Pipeline of our method

<span id="page-15-0"></span>**Input:**  $\{\tau^*\}$ : set of expert parameter trajectories. N: update times of the surrogate network in each inner optimization. M: update times between the start and target expert parameters.  $T^-, T, T^+$ : lower, current upper, final upper bound of the sample range of t.  $\mathcal{D}_{real}$ : original dataset. I: interval for expanding the sampling range. Sample a model  $f_{\theta^*}$  from  $\{\tau^*\}.$ Construct  $\mathcal{D}_{sub} = \{(x_i, softmax(L_i)) | (x_i, y_i) \in \mathcal{D}_{real} \text{ and } argmax(L_i) == y_i\}$ , where  $L_i = f_{\theta^*}(x_i).$ Randomly sample data from  $\mathcal{D}_{sub}$  to initialize synthetic dataset  $\mathcal{D}_{syn}$ . for iteration  $\leftarrow 0$  *to* max iteration do Randomly sample an expert training trajectory  $\tau^* \in {\{\tau^*\}}$  with  $\tau^* = {\{\theta^*_i\}}_0^n$ Select random start timestamp t, where  $T^- \leq t \leq T$ Sample  $\theta_t^*, \theta_{t+M}^*$  from  $\tau^*$ , initialize  $\hat{\theta}_t = \theta_t^*$ for  $i \leftarrow 0$  to N-1 do  $b_{t+i} \sim \mathcal{D}_{syn}$  > sample a mini-batch of distilled dataset  $\hat{\theta}_{t+i+1} = \hat{\theta}_{t+i} - \alpha \nabla \ell (\hat{\theta}_{t+i}$  $\rho$  update surrogate model with CE loss Compute matching loss between  $\hat{\theta}_{t+N}$  and  $\theta_{t+N}^*$  with Eq. [1](#page-2-2) Update  $(x_i, \text{softmax}(L_i)) \in \{b\}_{t}^{t+N-1}$  and  $\alpha$  with respect to the matching loss if (iteration %  $I == 0$ ) and  $(T < T<sup>+</sup>)$  then  $\lfloor T = T + 1 \rfloor$ **Output:** distilled dataset  $\mathcal{D}_{syn}$  and learning rate  $\alpha$ 

loss over the former part. In practice, we realize it by only matching a certain part of the expert trajectories during the distillation.

Here we introduce how to tune the values of  $T^-$ , T, and  $T^+$  (Algorithm [1\)](#page-15-0). For an initialized synthetic dataset  $\mathcal{D}_{syn}$ , we first set  $T^- = 0$  and  $T^+ = T^- + 10$  to perform the distillation for 50 iterations, where the matching loss over a distillation-uninvolved expert trajectory is recorded. Then we simultaneously increase the values of  $T^-$  and  $T^+$  until the distillation will not increase the matching loss over the latter part of the expert trajectory. Subsequently, we increase the value of  $T^{+}$  until the distillation will increase the matching loss over the former part of the expert trajectory. After deciding the values of  $T^-$  and  $T^+$ , we let them respectively be the lower- and upper-bound of the sample range and perform the distillation. During the distillation, we record the value of  $t$ if the matching loss is larger than 1, which denotes the surrogate model can not match the expert trajectory. Then  $T$  is set as the minimum recorded value, to avoid matching too hard trajectories in the beginning.

### A.7 MORE RELATED WORK

Two early works [\(Bohdal et al.,](#page-9-13) [2020;](#page-9-13) [Sucholutsky & Schonlau,](#page-10-16) [2021b\)](#page-10-16) in the dataset distillation area also focus on optimizing the labels of the datasets. Specifically, based on the dataset distillation algorithm proposed by [Wang et al.](#page-11-11) [\(2018\)](#page-11-11), [Sucholutsky & Schonlau](#page-10-16) [\(2021b\)](#page-10-16) propose to optimize labels during the distillation, while [Bohdal et al.](#page-9-13) [\(2020\)](#page-9-13) choose to only distill soft labels without optimizing the training data. Different from them, we use the pre-trained model to initialize soft labels, which contain more information. Furthermore, our method is based on matching training trajectories [\(Cazenavette et al.,](#page-9-1) [2022\)](#page-9-1) rather than the method proposed by [Wang et al.](#page-11-11) [\(2018\)](#page-11-11).

Recently, several methods are proposed to improve the performance, efficiency and suitability of dataset distillation. For example, [Liu et al.](#page-10-17) [\(2022\)](#page-10-17) proposed to use hallucinations to enlarge the synthetic datasets in the deployment stage. Subsequently, [Wang et al.](#page-10-18) [\(2023\)](#page-10-18) achieved this goal by distilling the target dataset into a generative model. Moreover, [\(Zhang et al.,](#page-11-1) [2023;](#page-11-1) [Liu et al.,](#page-10-0) [2023b\)](#page-10-0) were proposed to accelerate the distillation and [Liu et al.](#page-10-19) [\(2023a\)](#page-10-19) proposed a method that allows adjusting the size of the distilled dataset during the deployment stage. Recently, [Chen et al.](#page-9-14) [\(2024\)](#page-9-14) proposed to improve the quality of the synthetic dataset with a carefully designed distillation schedule. Moreover, dataset distillation has also been successfully applied in condensing gragh data [\(Jin et al.,](#page-9-15) [2022;](#page-9-15) [Yang et al.,](#page-11-13) [2023;](#page-11-13) [Zhang et al.,](#page-11-14) [2024a](#page-11-14)[;b\)](#page-11-15) and multi-modality data [\(Wu et al.,](#page-11-16) [2023\)](#page-11-16).

<span id="page-16-2"></span><span id="page-16-1"></span>Image /page/16/Figure/1 description: The image displays a grid of four plots, each showing loss and accuracy over iterations for different IPC (Iterations Per Class) values: 10, 100, 1000, and 2000. The top row contains four plots illustrating loss trends, with each plot showing multiple lines colored from light pink to dark purple, representing a time parameter 't' from 0 to 40. A dashed yellow line at y=1.0 serves as a reference. The bottom row contains four plots illustrating accuracy trends, with each plot showing two distinct lines labeled 'Strategy 1' and 'Strategy 2'. The x-axis for all plots is labeled 'Iteration (k)' ranging from 0 to 5. The y-axis for the loss plots ranges from 0.2 to 1.4, and for the accuracy plots, it ranges from approximately 0.5 to 0.84. The overall figure is titled 'Figure 10. More detailed results for experiments in Figure 2. We train the network on CIFAR-10 for 5 epochs.'

Figure 10: More detailed results of experiments reported in Figure [2.](#page-2-0) We train the expert models on CIFAR-10 for 40 epochs. Then we distill datasets with two strategies: (1) matching the early part of expert training trajectories, where  $\theta_t^* \in \{\theta_0^*, \theta_{20}^*\}$ . (2) matching the latter part of expert training trajectories, where  $\theta_t^* \in \{\theta_{20}^*, \theta_{40}^*\}$ . The first row shows the matching loss over a distillation-uninvolved expert trajectory, where the distillation is performed with strategy 1, and the second row shows the loss of strategy 2. In the first two rows, lines with darker color indicates the matching loss over more later part of the trajectories. t denotes the timestamp of the start parameters used for matching (Algorithm [1\)](#page-15-0). The last row shows the performance of the datasets distilled by different strategies with various IPC settings.

<span id="page-16-0"></span>

### A.8 SETTINGS

Distillation. Consistent with previous works [\(Cazenavette et al.,](#page-9-1) [2022;](#page-9-1) [Du et al.,](#page-9-3) [2023\)](#page-9-3), we perform the distillation for 10000 iterations to make sure the optimization is fully converged. We use ZCA whitening in all the involved experiments by default.

Evaluation. We keep our evaluation process consistent with previous works [\(Cazenavette et al.,](#page-9-1) [2022;](#page-9-1) [Du et al.,](#page-9-3) [2023\)](#page-9-3). Specifically, we train a randomly initialized network on the distilled dataset and then evaluate its performance on the entire validation set of the original dataset. Following previous works [\(Cazenavette et al.,](#page-9-1) [2022;](#page-9-1) [Du et al.,](#page-9-3) [2023\)](#page-9-3), the evaluation networks are trained for 1000 epochs to make sure the optimization is fully converged. All the results are the average over five trials. For fairness, experimental results of previous distillation methods in low IPC settings are obtained from [\(Du et al.,](#page-9-3) [2023\)](#page-9-3), while their results in high IPC cases come from [\(Cui et al.,](#page-9-6) [2022\)](#page-9-6).

Since the exponential moving average (EMA) used in FTD [\(Du et al.,](#page-9-3) [2023\)](#page-9-3) is a plug-and-play technique that hasn't been utilized by previous matching-based methods, for a fair comparison, we reproduce FTD with the official released code without using EMA. Accordingly, we do not use EMA in our method.

Network. We use various networks to evaluate the generalizability of our distilled datasets. Specifically, to scale ResNet, LeNet, and AlexNet to Tiny-ImageNet, we increase the stride of their first convolution layer from 1 to 2. For VGG, we increase the stride of its last max pooling layer from 1 to 2. The MLP utilized in our evaluation has one hidden layer with 128 units.

<span id="page-17-0"></span>Hyper-parameters. We report the hyper-parameters of our method in Table [6.](#page-17-1) Additionally, for all the experiments with optimizing soft labels, we set its momentum to 0.9. We find learning labels with a low momentum will somewhat increase the instability of the distillation. We conjecture this is because the optimized soft labels are easy to overfit the expert trajectories considering we only match one trajectory in each iteration.

Compute resources. Our experiments are run on 4 NVIDIA A100 GPUs, each with 80 GB of memory. The heavy reliance on GPU memory can be alleviated by TESLA [\(Cui et al.,](#page-9-2) [2023\)](#page-9-2) or simply reducing the synthetic steps  $N$ , which will not cause too much performance degradation. For example, reducing the synthetic steps  $N$  from 80 to 40 saves about half the GPU memory, while it only makes the performance drop by around 0.8% for CIFAR-10 with IPC=1000, 0.7% for CIFAR-100 with IPC=100, and 0.4% for TinyImageNet with IPC=50.

<span id="page-17-1"></span>

| Dataset     | <b>IPC</b> | N  | М              | $T^-$    | T              | $T^+$          | Interval                 | Synthetic<br><b>Batch Size</b> | Learning Rate<br>(Label) | Learning Rate<br>(Pixels) |
|-------------|------------|----|----------------|----------|----------------|----------------|--------------------------|--------------------------------|--------------------------|---------------------------|
|             |            | 80 | $\overline{c}$ | $\Omega$ | $\overline{4}$ | $\overline{4}$ |                          | 10                             | 5                        | 100                       |
|             | 10         | 80 | $\overline{c}$ | $\Omega$ | 10             | 20             | 100                      | 100                            | 2                        | 100                       |
| $CIFAR-10$  | 50         | 80 | 2              | $\Omega$ | 20             | 40             | 100                      | 500                            | $\mathfrak{2}$           | 1000                      |
|             | 500        | 80 | $\mathfrak{D}$ | 40       | 60             | 60             | $\overline{\phantom{a}}$ | 1000                           | 10                       | 50                        |
|             | 1000       | 80 | $\overline{c}$ | 40       | 60             | 60             | $\overline{\phantom{a}}$ | 1000                           | 10                       | 50                        |
|             | 1          | 40 | 3              | $\Omega$ | 10             | 20             | 100                      | 100                            | 10                       | 1000                      |
| $CIFAR-100$ | 10         | 80 | 2              | $\Omega$ | 30             | 50             | 100                      | 1000                           | 10                       | 1000                      |
|             | 50         | 80 | $\overline{c}$ | 20       | 70             | 70             | $\overline{\phantom{a}}$ | 1000                           | 10                       | 1000                      |
|             | 100        | 80 | $\overline{c}$ | 30       | 70             | 70             |                          | 1000                           | 10                       | 50                        |
| TI          | 1          | 60 | $\overline{c}$ | $\Omega$ | 15             | 20             | 400                      | 200                            | 10                       | 10000                     |
|             | 10         | 60 | $\mathfrak{D}$ | 10       | 50             | 50             | -                        | 250                            | 10                       | 100                       |
|             | 50         | 80 | 2              | 40       | 70             | 70             |                          | 250                            | 10                       | 100                       |

Table 6: Hyper-parameters for different datasets.

Image /page/18/Picture/1 description: The image is a grid of 80 small images, arranged in 8 rows and 10 columns. Each small image appears to be a generated or processed image, with a variety of colors and abstract or semi-abstract content. Some images show textures, patterns, or hints of objects like animals, landscapes, or buildings, but they are generally blurry or stylized. The overall impression is a collage of diverse visual elements, possibly representing outputs from a machine learning model or a collection of artistic digital creations.

Figure 11: (Tiny ImageNet, IPC=1) Visualization of distilled images (1/2).

Image /page/19/Picture/1 description: The image is a grid of 80 small images, arranged in 8 rows and 10 columns. Each small image appears to be a generated image, possibly from a deep learning model, with a somewhat abstract and painterly style. The images vary in color and subject matter, with some appearing to depict landscapes, portraits, food items, and abstract patterns. The overall impression is a collage of diverse, digitally created visuals.

Figure 12: (Tiny ImageNet, IPC=1) Visualization of distilled images (2/2).

Image /page/20/Picture/1 description: This is a grid of 64 images, each depicting a different subject. The images are arranged in 8 rows and 8 columns. The subjects include animals such as a goldfish, salamander, frog, snake, koala, jellyfish, lion, bear, orangutan, elephant, and various birds and insects. There are also images of people in different activities, such as a person in a black robe, a woman playing basketball, and a baby. Other images include a lighthouse, a beer bottle, a birdhouse, a barn, a blue barrel, and various food items like meat and pastries. Some images appear to be close-ups of textures or objects, such as a caterpillar, a butterfly, and a spider. The overall impression is a diverse collection of photographs.

Figure 13: (Tiny ImageNet, IPC=50) Visualization of distilled images (1/2).

Figure 14: (Tiny ImageNet, IPC=50) Visualization of distilled images (2/2).
independent, so that

$$
p(\mathbf{x}_{\mathrm{I}}, \mathbf{x}_{\mathrm{B}} | \mathcal{C}_k) = p(\mathbf{x}_{\mathrm{I}} | \mathcal{C}_k) p(\mathbf{x}_{\mathrm{B}} | \mathcal{C}_k).
$$
 (1.84)

*Section 8.2*

*Section 8.2* This is an example of *conditional independence* property, because the independence holds when the distribution is conditioned on the class  $\mathcal{C}_k$ . The posterior probability, given both the X-ray and blood data, is then given by

$$
p(C_k|\mathbf{x}_{\mathrm{I}}, \mathbf{x}_{\mathrm{B}}) \propto p(\mathbf{x}_{\mathrm{I}}, \mathbf{x}_{\mathrm{B}} | C_k) p(C_k)
$$
  
 
$$
\propto p(\mathbf{x}_{\mathrm{I}} | C_k) p(\mathbf{x}_{\mathrm{B}} | C_k) p(C_k)
$$
  
 
$$
\propto \frac{p(C_k|\mathbf{x}_{\mathrm{I}}) p(C_k|\mathbf{x}_{\mathrm{B}})}{p(C_k)}
$$
(1.85)

Thus we need the class prior probabilities  $p(\mathcal{C}_k)$ , which we can easily estimate from the fractions of data points in each class, and then we need to normalize the resulting posterior probabilities so they sum to one. The particular condi-*Section 8.2.2* tional independence assumption (1.84) is an example of the *naive Bayes model*. Note that the joint marginal distribution  $p(\mathbf{x}_{I}, \mathbf{x}_{B})$  will typically not factorize under this model. We shall see in later chapters how to construct models for combining data that do not require the conditional independence assumption (1.84).

### **1.5.5 Loss functions for regression**

So far, we have discussed decision theory in the context of classification problems. We now turn to the case of regression problems, such as the curve fitting *Section 1.1* example discussed earlier. The decision stage consists of choosing a specific estimate  $y(x)$  of the value of t for each input x. Suppose that in doing so, we incur a loss  $L(t, y(\mathbf{x}))$ . The average, or expected, loss is then given by

$$
\mathbb{E}[L] = \iint L(t, y(\mathbf{x})) p(\mathbf{x}, t) \, \mathrm{d}\mathbf{x} \, \mathrm{d}t. \tag{1.86}
$$

A common choice of loss function in regression problems is the squared loss given by  $L(t, y(\mathbf{x})) = \{y(\mathbf{x}) - t\}^2$ . In this case, the expected loss can be written

$$
\mathbb{E}[L] = \iint \{y(\mathbf{x}) - t\}^2 p(\mathbf{x}, t) \, \mathrm{d}\mathbf{x} \, \mathrm{d}t. \tag{1.87}
$$

Our goal is to choose  $y(x)$  so as to minimize  $\mathbb{E}[L]$ . If we assume a completely *Appendix D* flexible function  $y(x)$ , we can do this formally using the calculus of variations to give

$$
\frac{\delta \mathbb{E}[L]}{\delta y(\mathbf{x})} = 2 \int \{y(\mathbf{x}) - t\} p(\mathbf{x}, t) dt = 0.
$$
\n(1.88)

Solving for  $y(x)$ , and using the sum and product rules of probability, we obtain

$$
y(\mathbf{x}) = \frac{\int tp(\mathbf{x}, t) dt}{p(\mathbf{x})} = \int tp(t|\mathbf{x}) dt = \mathbb{E}_t[t|\mathbf{x}]
$$
 (1.89)

*Appendix D*

**Figure 1.28** The regression function  $y(x)$ , which minimizes the expected squared loss, is given by the mean of the conditional distribution  $p(t|x)$ .

Image /page/1/Figure/2 description: The image displays a graph with the x-axis labeled 'x' and the y-axis labeled 't'. A red curve, labeled 'y(x)', represents a function that increases as x increases. A dashed green line indicates a specific value 'y(x0)' on the t-axis, corresponding to a point on the red curve at x = x0. A vertical blue line is drawn at x = x0. Overlaid on this vertical line is a blue curve representing a probability distribution, labeled 'p(t|x0)', which is centered around the value y(x0) and shows a bell-like shape, indicating a probability density function of t given x0.

which is the conditional average of <sup>t</sup> conditioned on **x** and is known as the *regression function*. This result is illustrated in Figure 1.28. It can readily be extended to multiple target variables represented by the vector **t**, in which case the optimal solution *Exercise 1.25* is the conditional average  $\mathbf{v}(\mathbf{x}) = \mathbb{E}_{t}[\mathbf{t}|\mathbf{x}]$ .

We can also derive this result in a slightly different way, which will also shed light on the nature of the regression problem. Armed with the knowledge that the optimal solution is the conditional expectation, we can expand the square term as follows

$$
{y(\mathbf{x}) - t}2 = {y(\mathbf{x}) - \mathbb{E}[t|\mathbf{x}] + \mathbb{E}[t|\mathbf{x}] - t}2
$$
  
= {y(\mathbf{x}) - \mathbb{E}[t|\mathbf{x}]}<sup>2</sup> + 2{y(\mathbf{x}) - \mathbb{E}[t|\mathbf{x}]}{\mathbb{E}[t|\mathbf{x}] - t} + {\mathbb{E}[t|\mathbf{x}] - t}<sup>2</sup>

where, to keep the notation uncluttered, we use  $\mathbb{E}[t|\mathbf{x}]$  to denote  $\mathbb{E}_t[t|\mathbf{x}]$ . Substituting into the loss function and performing the integral over  $t$ , we see that the cross-term vanishes and we obtain an expression for the loss function in the form

$$
\mathbb{E}[L] = \int \{y(\mathbf{x}) - \mathbb{E}[t|\mathbf{x}]\}^2 p(\mathbf{x}) \, \mathrm{d}\mathbf{x} + \int \{\mathbb{E}[t|\mathbf{x}] - t\}^2 p(\mathbf{x}) \, \mathrm{d}\mathbf{x}.
$$
 (1.90)

The function  $y(x)$  we seek to determine enters only in the first term, which will be minimized when  $y(x)$  is equal to  $E[t|x]$ , in which case this term will vanish. This is simply the result that we derived previously and that shows that the optimal least squares predictor is given by the conditional mean. The second term is the variance of the distribution of <sup>t</sup>, averaged over **x**. It represents the intrinsic variability of the target data and can be regarded as noise. Because it is independent of  $y(\mathbf{x})$ , it represents the irreducible minimum value of the loss function.

As with the classification problem, we can either determine the appropriate probabilities and then use these to make optimal decisions, or we can build models that make decisions directly. Indeed, we can identify three distinct approaches to solving regression problems given, in order of decreasing complexity, by:

(a) First solve the inference problem of determining the joint density  $p(\mathbf{x}, t)$ . Then normalize to find the conditional density  $p(t|\mathbf{x})$ , and finally marginalize to find the conditional mean given by (1.89).

- **(b)** First solve the inference problem of determining the conditional density  $p(t|\mathbf{x})$ , and then subsequently marginalize to find the conditional mean given by (1.89).
- **(c)** Find a regression function  $y(x)$  directly from the training data.

The relative merits of these three approaches follow the same lines as for classification problems above.

The squared loss is not the only possible choice of loss function for regression. Indeed, there are situations in which squared loss can lead to very poor results and where we need to develop more sophisticated approaches. An important example concerns situations in which the conditional distribution  $p(t|\mathbf{x})$  is multimodal, as *Section 5.6* often arises in the solution of inverse problems. Here we consider briefly one simple generalization of the squared loss, called the *Minkowski* loss, whose expectation is given by

$$
\mathbb{E}[L_q] = \iint |y(\mathbf{x}) - t|^q p(\mathbf{x}, t) \, \mathrm{d}\mathbf{x} \, \mathrm{d}t \tag{1.91}
$$

which reduces to the expected squared loss for  $q = 2$ . The function  $|y - t|^q$  is plotted against  $y - t$  for various values of q in Figure 1.29. The minimum of  $\mathbb{E}[L_q]$ is given by the conditional mean for  $q = 2$ , the conditional median for  $q = 1$ , and *Exercise 1.27* the conditional mode for  $q \to 0$ .

# **1.6. Information Theory**

In this chapter, we have discussed a variety of concepts from probability theory and decision theory that will form the foundations for much of the subsequent discussion in this book. We close this chapter by introducing some additional concepts from the field of information theory, which will also prove useful in our development of pattern recognition and machine learning techniques. Again, we shall focus only on the key concepts, and we refer the reader elsewhere for more detailed discussions (Viterbi and Omura, 1979; Cover and Thomas, 1991; MacKay, 2003) .

We begin by considering a discrete random variable  $x$  and we ask how much information is received when we observe a specific value for this variable. The amount of information can be viewed as the 'degree of surprise' on learning the value of  $x$ . If we are told that a highly improbable event has just occurred, we will have received more information than if we were told that some very likely event has just occurred, and if we knew that the event was certain to happen we would receive no information. Our measure of information content will therefore depend on the probability distribution  $p(x)$ , and we therefore look for a quantity  $h(x)$  that is a monotonic function of the probability  $p(x)$  and that expresses the information content. The form of  $h(\cdot)$  can be found by noting that if we have two events x and  $y$  that are unrelated, then the information gain from observing both of them should be the sum of the information gained from each of them separately, so that  $h(x, y) = h(x) + h(y)$ . Two unrelated events will be statistically independent and so  $p(x, y) = p(x)p(y)$ . From these two relationships, it is easily shown that  $h(x)$ *Exercise 1.28* must be given by the logarithm of  $p(x)$  and so we have

*Section 5.6*

Image /page/3/Figure/1 description: This image displays four plots, each illustrating the relationship between |y-t|^q and y-t for different values of q. The top-left plot shows the case where q = 0.3, the top-right plot shows q = 1, the bottom-left plot shows q = 2, and the bottom-right plot shows q = 10. All plots have the y-axis labeled as |y-t|^q and the x-axis labeled as y-t, with values ranging from -2 to 2 on both axes. The plots demonstrate how the function's shape changes with varying q values, transitioning from a sharp V-shape for q=1 to a more rounded shape for q=0.3 and a parabolic shape for q=2, and finally a very steep, almost flat shape near zero for q=10.

**Figure 1.29** Plots of the quantity  $L_q = |y - t|^q$  for various values of q.

$$
h(x) = -\log_2 p(x) \tag{1.92}
$$

where the negative sign ensures that information is positive or zero. Note that low probability events  $x$  correspond to high information content. The choice of basis for the logarithm is arbitrary, and for the moment we shall adopt the convention prevalent in information theory of using logarithms to the base of 2. In this case, as we shall see shortly, the units of  $h(x)$  are bits ('binary digits').

Now suppose that a sender wishes to transmit the value of a random variable to a receiver. The average amount of information that they transmit in the process is obtained by taking the expectation of (1.92) with respect to the distribution  $p(x)$  and is given by

$$
H[x] = -\sum_{x} p(x) \log_2 p(x).
$$
 (1.93)

This important quantity is called the *entropy* of the random variable  $x$ . Note that  $\lim_{n\to 0} p \ln p = 0$  and so we shall take  $p(x) \ln p(x) = 0$  whenever we encounter a value for x such that  $p(x)=0$ .

So far we have given a rather heuristic motivation for the definition of informa-

tion (1.92) and the corresponding entropy (1.93). We now show that these definitions indeed possess useful properties. Consider a random variable  $x$  having 8 possible states, each of which is equally likely. In order to communicate the value of  $x$  to a receiver, we would need to transmit a message of length 3 bits. Notice that the entropy of this variable is given by

$$
H[x] = -8 \times \frac{1}{8} \log_2 \frac{1}{8} = 3
$$
 bits.

Now consider an example (Cover and Thomas, 1991) of a variable having 8 possible states  $\{a, b, c, d, e, f, g, h\}$  for which the respective probabilities are given by  $(\frac{1}{2}, \frac{1}{4}, \frac{1}{8}, \frac{1}{16}, \frac{1}{64}, \frac{1}{64}, \frac{1}{64}, \frac{1}{64})$ . The entropy in this case is given by

$$
H[x] = -\frac{1}{2}\log_2\frac{1}{2}-\frac{1}{4}\log_2\frac{1}{4}-\frac{1}{8}\log_2\frac{1}{8}-\frac{1}{16}\log_2\frac{1}{16}-\frac{4}{64}\log_2\frac{1}{64}=2\text{ bits}.
$$

We see that the nonuniform distribution has a smaller entropy than the uniform one, and we shall gain some insight into this shortly when we discuss the interpretation of entropy in terms of disorder. For the moment, let us consider how we would transmit the identity of the variable's state to a receiver. We could do this, as before, using a 3-bit number. However, we can take advantage of the nonuniform distribution by using shorter codes for the more probable events, at the expense of longer codes for the less probable events, in the hope of getting a shorter average code length. This can be done by representing the states  $\{a, b, c, d, e, f, g, h\}$  using, for instance, the following set of code strings: 0, 10, 110, 1110, 111100, 111101, 111110, 111111. The average length of the code that has to be transmitted is then

average code length = 
$$
\frac{1}{2} \times 1 + \frac{1}{4} \times 2 + \frac{1}{8} \times 3 + \frac{1}{16} \times 4 + 4 \times \frac{1}{64} \times 6 = 2 \text{ bits}
$$

which again is the same as the entropy of the random variable. Note that shorter code strings cannot be used because it must be possible to disambiguate a concatenation of such strings into its component parts. For instance, 11001110 decodes uniquely into the state sequence  $c, a, d$ .

This relation between entropy and shortest coding length is a general one. The *noiseless coding theorem* (Shannon, 1948) states that the entropy is a lower bound on the number of bits needed to transmit the state of a random variable.

From now on, we shall switch to the use of natural logarithms in defining entropy, as this will provide a more convenient link with ideas elsewhere in this book. In this case, the entropy is measured in units of 'nats' instead of bits, which differ simply by a factor of ln 2.

We have introduced the concept of entropy in terms of the average amount of information needed to specify the state of a random variable. In fact, the concept of entropy has much earlier origins in physics where it was introduced in the context of equilibrium thermodynamics and later given a deeper interpretation as a measure of disorder through developments in statistical mechanics. We can understand this alternative view of entropy by considering a set of  $N$  identical objects that are to be divided amongst a set of bins, such that there are  $n_i$  objects in the  $i^{\text{th}}$  bin. Consider

# **1.6. Information Theory 51**

the number of different ways of allocating the objects to the bins. There are  $N$ ways to choose the first object,  $(N - 1)$  ways to choose the second object, and so on, leading to a total of  $N!$  ways to allocate all N objects to the bins, where  $N!$ (pronounced 'factorial N') denotes the product  $N \times (N-1) \times \cdots \times 2 \times 1$ . However, we don't wish to distinguish between rearrangements of objects within each bin. In the  $i^{\text{th}}$  bin there are  $n_i!$  ways of reordering the objects, and so the total number of ways of allocating the  $N$  objects to the bins is given by

$$
W = \frac{N!}{\prod_i n_i!} \tag{1.94}
$$

which is called the *multiplicity*. The entropy is then defined as the logarithm of the multiplicity scaled by an appropriate constant

$$
H = \frac{1}{N} \ln W = \frac{1}{N} \ln N! - \frac{1}{N} \sum_{i} \ln n_i!.
$$
 (1.95)

We now consider the limit  $N \to \infty$ , in which the fractions  $n_i/N$  are held fixed, and apply Stirling's approximation

$$
\ln N! \simeq N \ln N - N \tag{1.96}
$$

which gives

$$
H = -\lim_{N \to \infty} \sum_{i} \left(\frac{n_i}{N}\right) \ln\left(\frac{n_i}{N}\right) = -\sum_{i} p_i \ln p_i \tag{1.97}
$$

where we have used  $\sum_i n_i = N$ . Here  $p_i = \lim_{N \to \infty} (n_i/N)$  is the probability of an object being assigned to the  $i^{\text{th}}$  bin. In physics terminology, the specific arrangements of objects in the bins is called a *microstate*, and the overall distribution of occupation numbers, expressed through the ratios  $n_i/N$ , is called a *macrostate*. The multiplicity W is also known as the *weight* of the macrostate.

We can interpret the bins as the states  $x_i$  of a discrete random variable X, where  $p(X = x_i) = p_i$ . The entropy of the random variable X is then

$$
H[p] = -\sum_{i} p(x_i) \ln p(x_i).
$$
 (1.98)

Distributions  $p(x_i)$  that are sharply peaked around a few values will have a relatively low entropy, whereas those that are spread more evenly across many values will have higher entropy, as illustrated in Figure 1.30. Because  $0 \leq p_i \leq 1$ , the entropy is nonnegative, and it will equal its minimum value of 0 when one of the  $p_i =$ 1 and all other  $p_{j\neq i} = 0$ . The maximum entropy configuration can be found by *Appendix E* maximizing H using a Lagrange multiplier to enforce the normalization constraint on the probabilities. Thus we maximize

$$
\widetilde{H} = -\sum_{i} p(x_i) \ln p(x_i) + \lambda \left( \sum_{i} p(x_i) - 1 \right)
$$
\n(1.99)

*Appendix E*

Image /page/6/Figure/1 description: Two histograms are displayed side-by-side. Both histograms have a y-axis labeled "probabilities" ranging from 0 to 0.5, with tick marks at 0, 0.25, and 0.5. The left histogram shows a tall, narrow distribution peaking around a probability of 0.3, with the text "H = 1.77" above it. The right histogram shows a wider, flatter distribution peaking around a probability of 0.05, with the text "H = 3.09" above it. Both histograms have a black outline and blue bars.

**Figure 1.30** Histograms of two probability distributions over 30 bins illustrating the higher value of the entropy H for the broader distribution. The largest entropy would arise from a uniform distribution that would give  $H =$  $-\ln(1/30) = 3.40.$ 

from which we find that all of the  $p(x_i)$  are equal and are given by  $p(x_i)=1/M$ where M is the total number of states  $x_i$ . The corresponding value of the entropy is then  $H = \ln M$ . This result can also be derived from Jensen's inequality (to be *Exercise 1.29* discussed shortly). To verify that the stationary point is indeed a maximum, we can evaluate the second derivative of the entropy, which gives

$$
\frac{\partial \widetilde{H}}{\partial p(x_i)\partial p(x_j)} = -I_{ij}\frac{1}{p_i}
$$
\n(1.100)

where  $I_{ij}$  are the elements of the identity matrix.

We can extend the definition of entropy to include distributions  $p(x)$  over continuous variables x as follows. First divide x into bins of width  $\Delta$ . Then, assuming  $p(x)$  is continuous, the *mean value theorem* (Weisstein, 1999) tells us that, for each such bin, there must exist a value  $x_i$  such that

$$
\int_{i\Delta}^{(i+1)\Delta} p(x) dx = p(x_i)\Delta.
$$
 (1.101)

We can now quantize the continuous variable x by assigning any value x to the value  $x_i$  whenever x falls in the i<sup>th</sup> bin. The probability of observing the value  $x_i$  is then  $p(x_i)\Delta$ . This gives a discrete distribution for which the entropy takes the form

$$
H_{\Delta} = -\sum_{i} p(x_i) \Delta \ln (p(x_i) \Delta) = -\sum_{i} p(x_i) \Delta \ln p(x_i) - \ln \Delta \qquad (1.102)
$$

where we have used  $\sum_i p(x_i) \Delta = 1$ , which follows from (1.101). We now omit the second term  $-\ln \Delta$  on the right-hand side of (1.102) and then consider the limit

 $\Delta \rightarrow 0$ . The first term on the right-hand side of (1.102) will approach the integral of  $p(x) \ln p(x)$  in this limit so that

$$
\lim_{\Delta \to 0} \left\{ \sum_{i} p(x_i) \Delta \ln p(x_i) \right\} = - \int p(x) \ln p(x) dx \quad (1.103)
$$

where the quantity on the right-hand side is called the *differential entropy*. We see that the discrete and continuous forms of the entropy differ by a quantity  $\ln \Delta$ , which diverges in the limit  $\Delta \rightarrow 0$ . This reflects the fact that to specify a continuous variable very precisely requires a large number of bits. For a density defined over multiple continuous variables, denoted collectively by the vector **x**, the differential entropy is given by

$$
H[\mathbf{x}] = -\int p(\mathbf{x}) \ln p(\mathbf{x}) \, \mathrm{d}\mathbf{x}.\tag{1.104}
$$

In the case of discrete distributions, we saw that the maximum entropy configuration corresponded to an equal distribution of probabilities across the possible states of the variable. Let us now consider the maximum entropy configuration for a continuous variable. In order for this maximum to be well defined, it will be necessary to constrain the first and second moments of  $p(x)$  as well as preserving the normalization constraint. We therefore maximize the differential entropy with the

Image /page/7/Picture/6 description: A black and white portrait of a man with a full beard and glasses.

## Ludwig Boltzmann 1844 –1906

Ludwig Eduard Boltzmann was an Austrian physicist who created the field of statistical mechanics. Prior to Boltzmann, the concept of entropy was already known from classical thermodynamics where it

quantifies the fact that when we take energy from a system, not all of that energy is typically available to do useful work. Boltzmann showed that the thermodynamic entropy  $S$ , a macroscopic quantity, could be related to the statistical properties at the microscopic level. This is expressed through the famous equation  $S = k \ln W$  in which W represents the number of possible microstates in a macrostate, and  $k \approx 1.38 \times 10^{-23}$  (in units of Joules per Kelvin) is known as Boltzmann's constant. Boltzmann's ideas were disputed by many scientists of they day. One difficulty they saw arose from the second law of thermodynamics, which states that the entropy of a closed system tends to increase with time. By contrast, at the microscopic level the classical Newtonian equations of physics are reversible, and so they found it difficult to see how the latter could explain the former. They didn't fully appreciate Boltzmann's arguments, which were statistical in nature and which concluded not that entropy could never decrease over time but simply that with overwhelming probability it would generally increase. Boltzmann even had a longrunning dispute with the editor of the leading German physics journal who refused to let him refer to atoms and molecules as anything other than convenient theoretical constructs. The continued attacks on his work lead to bouts of depression, and eventually he committed suicide. Shortly after Boltzmann's death, new experiments by Perrin on colloidal suspensions verified his theories and confirmed the value of the Boltzmann constant. The equation  $S = k \ln W$  is carved on Boltzmann's tombstone.

three constraints

$$
\int_{-\infty}^{\infty} p(x) dx = 1 \qquad (1.105)
$$

$$
\int_{-\infty}^{\infty} x p(x) \, \mathrm{d}x = \mu \tag{1.106}
$$

$$
\int_{-\infty}^{\infty} (x - \mu)^2 p(x) dx = \sigma^2.
$$
 (1.107)

*Appendix E* The constrained maximization can be performed using Lagrange multipliers so that we maximize the following functional with respect to  $p(x)$ 

$$
- \int_{-\infty}^{\infty} p(x) \ln p(x) dx + \lambda_1 \left( \int_{-\infty}^{\infty} p(x) dx - 1 \right) + \lambda_2 \left( \int_{-\infty}^{\infty} x p(x) dx - \mu \right) + \lambda_3 \left( \int_{-\infty}^{\infty} (x - \mu)^2 p(x) dx - \sigma^2 \right).
$$

*Appendix D* Using the calculus of variations, we set the derivative of this functional to zero giving

$$
p(x) = \exp \{-1 + \lambda_1 + \lambda_2 x + \lambda_3 (x - \mu)^2\}.
$$
 (1.108)

The Lagrange multipliers can be found by back substitution of this result into the *Exercise 1.34* three constraint equations, leading finally to the result

$$
p(x) = \frac{1}{(2\pi\sigma^2)^{1/2}} \exp\left\{-\frac{(x-\mu)^2}{2\sigma^2}\right\}
$$
 (1.109)

and so the distribution that maximizes the differential entropy is the Gaussian. Note that we did not constrain the distribution to be nonnegative when we maximized the entropy. However, because the resulting distribution is indeed nonnegative, we see with hindsight that such a constraint is not necessary.

*Exercise 1.35*

*Exercise 1.35* If we evaluate the differential entropy of the Gaussian, we obtain

$$
H[x] = \frac{1}{2} \{ 1 + \ln(2\pi\sigma^2) \}.
$$
 (1.110)

Thus we see again that the entropy increases as the distribution becomes broader, i.e., as  $\sigma^2$  increases. This result also shows that the differential entropy, unlike the discrete entropy, can be negative, because  $H(x) < 0$  in (1.110) for  $\sigma^2 < 1/(2\pi e)$ .

Suppose we have a joint distribution  $p(x, y)$  from which we draw pairs of values of **x** and **y**. If a value of **x** is already known, then the additional information needed to specify the corresponding value of **y** is given by  $-\ln p(\mathbf{y}|\mathbf{x})$ . Thus the average additional information needed to specify **y** can be written as

$$
H[\mathbf{y}|\mathbf{x}] = -\iint p(\mathbf{y}, \mathbf{x}) \ln p(\mathbf{y}|\mathbf{x}) \, d\mathbf{y} \, d\mathbf{x}
$$
 (1.111)

*Exercise* 1.37

which is called the *conditional entropy* of **y** given **x**. It is easily seen, using the *Exercise 1.37* product rule, that the conditional entropy satisfies the relation

$$
H[\mathbf{x}, \mathbf{y}] = H[\mathbf{y}|\mathbf{x}] + H[\mathbf{x}] \tag{1.112}
$$

where  $H[x, y]$  is the differential entropy of  $p(x, y)$  and  $H[x]$  is the differential entropy of the marginal distribution  $p(x)$ . Thus the information needed to describe **x** and **y** is given by the sum of the information needed to describe **x** alone plus the additional information required to specify **y** given **x**.

### **1.6.1 Relative entropy and mutual information**

So far in this section, we have introduced a number of concepts from information theory, including the key notion of entropy. We now start to relate these ideas to pattern recognition. Consider some unknown distribution  $p(x)$ , and suppose that we have modelled this using an approximating distribution  $q(\mathbf{x})$ . If we use  $q(\mathbf{x})$  to construct a coding scheme for the purpose of transmitting values of **x** to a receiver, then the average *additional* amount of information (in nats) required to specify the value of **x** (assuming we choose an efficient coding scheme) as a result of using  $q(\mathbf{x})$ instead of the true distribution  $p(x)$  is given by

$$
KL(p||q) = -\int p(\mathbf{x}) \ln q(\mathbf{x}) d\mathbf{x} - \left(-\int p(\mathbf{x}) \ln p(\mathbf{x}) d\mathbf{x}\right)
$$
  
= 
$$
-\int p(\mathbf{x}) \ln \left\{\frac{q(\mathbf{x})}{p(\mathbf{x})}\right\} d\mathbf{x}.
$$
 (1.113)

This is known as the *relative entropy* or *Kullback-Leibler divergence*, or *KL divergence* (Kullback and Leibler, 1951), between the distributions  $p(x)$  and  $q(x)$ . Note that it is not a symmetrical quantity, that is to say  $KL(p||q) \neq KL(q||p)$ .

We now show that the Kullback-Leibler divergence satisfies  $KL(p||q) \ge 0$  with equality if, and only if,  $p(x) = q(x)$ . To do this we first introduce the concept of *convex* functions. A function  $f(x)$  is said to be convex if it has the property that every chord lies on or above the function, as shown in Figure 1.31. Any value of  $x$ in the interval from  $x = a$  to  $x = b$  can be written in the form  $\lambda a + (1 - \lambda)b$  where  $0 \le \lambda \le 1$ . The corresponding point on the chord is given by  $\lambda f(a) + (1 - \lambda)f(b)$ ,

Image /page/9/Picture/10 description: A black and white close-up portrait of a man with dark hair, wearing a suit and tie. He is looking directly at the camera with a serious expression. The background is blurred and appears to be a cityscape or a wall with vertical lines.

## Claude Shannon 1916 –2001

After graduating from Michigan and MIT, Shannon joined the AT&T Bell Telephone laboratories in 1941. His paper 'A Mathematical Theory of Communication' published in the Bell System Technical Journal in

ory. This paper introduced the word 'bit', and his concept that information could be sent as a stream of 1s and 0s paved the way for the communications revolution. It is said that von Neumann recommended to Shannon that he use the term entropy, not only because of its similarity to the quantity used in physics, but also because "nobody knows what entropy really is, so in any discussion you will always have an advantage".

1948 laid the foundations for modern information the-

**Figure 1.31** A convex function  $f(x)$  is one for which every chord (shown in blue) lies on or above the function (shown in red).

Image /page/10/Figure/2 description: The image displays a graph illustrating the concept of a chord in calculus. The x-axis is labeled with points 'a', 'xλ', and 'b'. The y-axis is unlabeled. A red curve, labeled 'f(x)', represents a function. A blue line segment, identified as a 'chord', connects two points on the curve, specifically at x=a and x=b. A vertical green line extends from the x-axis at x=xλ up to the chord, indicating a specific point on the chord.

and the corresponding value of the function is  $f(\lambda a + (1 - \lambda)b)$ . Convexity then implies

$$
f(\lambda a + (1 - \lambda)b) \leq \lambda f(a) + (1 - \lambda)f(b).
$$
 (1.114)

This is equivalent to the requirement that the second derivative of the function be *Exercise 1.36* everywhere positive. Examples of convex functions are x ln x (for  $x > 0$ ) and  $x^2$ . A function is called *strictly convex* if the equality is satisfied only for  $\lambda = 0$  and  $\lambda = 1$ . If a function has the opposite property, namely that every chord lies on or below the function, it is called *concave*, with a corresponding definition for *strictly concave*. If a function  $f(x)$  is convex, then  $-f(x)$  will be concave.

*Exercise 1.38* Using the technique of proof by induction, we can show from (1.114) that a convex function  $f(x)$  satisfies

$$
f\left(\sum_{i=1}^{M} \lambda_i x_i\right) \leqslant \sum_{i=1}^{M} \lambda_i f(x_i) \tag{1.115}
$$

where  $\lambda_i \geq 0$  and  $\sum_i \lambda_i = 1$ , for any set of points  $\{x_i\}$ . The result (1.115) is known as *Jensen's inequality*. If we interpret the  $\lambda_i$  as the probability distribution over a discrete variable x taking the values  $\{x_i\}$ , then (1.115) can be written

$$
f\left(\mathbb{E}[x]\right) \le \mathbb{E}[f(x)]\tag{1.116}
$$

where  $\mathbb{E}[\cdot]$  denotes the expectation. For continuous variables, Jensen's inequality takes the form

$$
f\left(\int \mathbf{x}p(\mathbf{x}) \, \mathrm{d}\mathbf{x}\right) \leqslant \int f(\mathbf{x})p(\mathbf{x}) \, \mathrm{d}\mathbf{x}.\tag{1.117}
$$

We can apply Jensen's inequality in the form (1.117) to the Kullback-Leibler divergence (1.113) to give

$$
KL(p||q) = -\int p(\mathbf{x}) \ln \left\{ \frac{q(\mathbf{x})}{p(\mathbf{x})} \right\} d\mathbf{x} \ge -\ln \int q(\mathbf{x}) d\mathbf{x} = 0 \quad (1.118)
$$

where we have used the fact that  $-\ln x$  is a convex function, together with the normalization condition  $\int q(\mathbf{x}) d\mathbf{x} = 1$ . In fact,  $-\ln x$  is a strictly convex function, so the equality will hold if, and only if,  $q(\mathbf{x}) = p(\mathbf{x})$  for all **x**. Thus we can interpret the Kullback-Leibler divergence as a measure of the dissimilarity of the two distributions  $p(x)$  and  $q(x)$ .

We see that there is an intimate relationship between data compression and density estimation (i.e., the problem of modelling an unknown probability distribution) because the most efficient compression is achieved when we know the true distribution. If we use a distribution that is different from the true one, then we must necessarily have a less efficient coding, and on average the additional information that must be transmitted is (at least) equal to the Kullback-Leibler divergence between the two distributions.

Suppose that data is being generated from an unknown distribution  $p(x)$  that we wish to model. We can try to approximate this distribution using some parametric distribution  $q(x|\theta)$ , governed by a set of adjustable parameters  $\theta$ , for example a multivariate Gaussian. One way to determine  $\theta$  is to minimize the Kullback-Leibler divergence between  $p(x)$  and  $q(x|\theta)$  with respect to  $\theta$ . We cannot do this directly because we don't know  $p(x)$ . Suppose, however, that we have observed a finite set of training points  $x_n$ , for  $n = 1, ..., N$ , drawn from  $p(x)$ . Then the expectation with respect to  $p(x)$  can be approximated by a finite sum over these points, using (1.35), so that

$$
KL(p||q) \simeq \sum_{n=1}^{N} \left\{-\ln q(\mathbf{x}_n|\boldsymbol{\theta}) + \ln p(\mathbf{x}_n)\right\}.
$$
 (1.119)

The second term on the right-hand side of  $(1.119)$  is independent of  $\theta$ , and the first term is the negative log likelihood function for  $\theta$  under the distribution  $q(\mathbf{x}|\theta)$  evaluated using the training set. Thus we see that minimizing this Kullback-Leibler divergence is equivalent to maximizing the likelihood function.

Now consider the joint distribution between two sets of variables **x** and **y** given by  $p(x, y)$ . If the sets of variables are independent, then their joint distribution will factorize into the product of their marginals  $p(x, y) = p(x)p(y)$ . If the variables are not independent, we can gain some idea of whether they are 'close' to being independent by considering the Kullback-Leibler divergence between the joint distribution and the product of the marginals, given by

$$
I[\mathbf{x}, \mathbf{y}] \equiv KL(p(\mathbf{x}, \mathbf{y}) \| p(\mathbf{x}) p(\mathbf{y})) \\ = - \iint p(\mathbf{x}, \mathbf{y}) \ln \left( \frac{p(\mathbf{x}) p(\mathbf{y})}{p(\mathbf{x}, \mathbf{y})} \right) \, d\mathbf{x} \, d\mathbf{y} \tag{1.120}
$$

which is called the *mutual information* between the variables **x** and **y**. From the properties of the Kullback-Leibler divergence, we see that  $I(\mathbf{x}, \mathbf{y}) \ge 0$  with equal-<br>ity if and only if x and y are independent. Using the sum and product rules of ity if, and only if, **x** and **y** are independent. Using the sum and product rules of probability, we see that the mutual information is related to the conditional entropy

$$
I[\mathbf{x}, \mathbf{y}] = H[\mathbf{x}] - H[\mathbf{x}|\mathbf{y}] = H[\mathbf{y}] - H[\mathbf{y}|\mathbf{x}].
$$
 (1.121)

*Exercise 1.41* through

Thus we can view the mutual information as the reduction in the uncertainty about **x** by virtue of being told the value of **y** (or vice versa). From a Bayesian perspective, we can view  $p(x)$  as the prior distribution for **x** and  $p(x|y)$  as the posterior distribution after we have observed new data **y**. The mutual information therefore represents the reduction in uncertainty about **x** as a consequence of the new observation **y**.

# **Exercises**

**1.1**  $(\star)$  **www** Consider the sum-of-squares error function given by (1.2) in which the function  $y(x, w)$  is given by the polynomial (1.1). Show that the coefficients  $\mathbf{w} = \{w_i\}$  that minimize this error function are given by the solution to the following set of linear equations

$$
\sum_{j=0}^{M} A_{ij} w_j = T_i
$$
 (1.122)

where

$$
A_{ij} = \sum_{n=1}^{N} (x_n)^{i+j}, \qquad T_i = \sum_{n=1}^{N} (x_n)^{i} t_n.
$$
 (1.123)

Here a suffix i or j denotes the index of a component, whereas  $(x)^i$  denotes x raised to the power of  $i$ .

- **1.2** (**\***) Write down the set of coupled linear equations, analogous to (1.122), satisfied by the coefficients  $w_i$  which minimize the regularized sum-of-squares error function given by  $(1.4)$ .
- **1.3**  $(\star \star)$  Suppose that we have three coloured boxes r (red), b (blue), and g (green). Box  $r$  contains 3 apples, 4 oranges, and 3 limes, box  $b$  contains 1 apple, 1 orange, and 0 limes, and box  $q$  contains 3 apples, 3 oranges, and 4 limes. If a box is chosen at random with probabilities  $p(r)=0.2$ ,  $p(b)=0.2$ ,  $p(q)=0.6$ , and a piece of fruit is removed from the box (with equal probability of selecting any of the items in the box), then what is the probability of selecting an apple? If we observe that the selected fruit is in fact an orange, what is the probability that it came from the green box?
- **1.4**  $(\star \star)$  **www** Consider a probability density  $p_x(x)$  defined over a continuous variable  $\overline{x}$ , and suppose that we make a nonlinear change of variable using  $x = q(y)$ , so that the density transforms according to  $(1.27)$ . By differentiating  $(1.27)$ , show that the location  $\hat{y}$  of the maximum of the density in y is not in general related to the location  $\hat{x}$  of the maximum of the density over x by the simple functional relation  $\hat{x} = g(\hat{y})$  as a consequence of the Jacobian factor. This shows that the maximum of a probability density (in contrast to a simple function) is dependent on the choice of variable. Verify that, in the case of a linear transformation, the location of the maximum transforms in the same way as the variable itself.
- **1.5** ( $\star$ ) Using the definition (1.38) show that var $[f(x)]$  satisfies (1.39).

- **1.6** ( $\star$ ) Show that if two variables x and y are independent, then their covariance is zero.
- **1.7**  $(\star \star)$  **www** In this exercise, we prove the normalization condition (1.48) for the univariate Gaussian. To do this consider, the integral

$$
I = \int_{-\infty}^{\infty} \exp\left(-\frac{1}{2\sigma^2}x^2\right) dx \tag{1.124}
$$

which we can evaluate by first writing its square in the form

$$
I^2 = \int_{-\infty}^{\infty} \int_{-\infty}^{\infty} \exp\left(-\frac{1}{2\sigma^2}x^2 - \frac{1}{2\sigma^2}y^2\right) dx dy.
$$
 (1.125)

Now make the transformation from Cartesian coordinates  $(x, y)$  to polar coordinates  $(r, \theta)$  and then substitute  $u = r^2$ . Show that, by performing the integrals over  $\theta$  and  $u$ , and then taking the square root of both sides, we obtain

$$
I = \left(2\pi\sigma^2\right)^{1/2}.\tag{1.126}
$$

Finally, use this result to show that the Gaussian distribution  $\mathcal{N}(x|\mu, \sigma^2)$  is normalized.

**1.8**  $(\star \star)$  **www** By using a change of variables, verify that the univariate Gaussian distribution given by (1.46) satisfies (1.49). Next, by differentiating both sides of the normalization condition

$$
\int_{-\infty}^{\infty} \mathcal{N}(x|\mu, \sigma^2) \, \mathrm{d}x = 1 \tag{1.127}
$$

with respect to  $\sigma^2$ , verify that the Gaussian satisfies (1.50). Finally, show that (1.51) holds.

- **1.9 () www** Show that the mode (i.e. the maximum) of the Gaussian distribution  $(1.46)$  is given by  $\mu$ . Similarly, show that the mode of the multivariate Gaussian  $(1.52)$  is given by  $\mu$ .
- **1.10** ( $\star$ ) **www** Suppose that the two variables x and z are statistically independent. Show that the mean and variance of their sum satisfies

$$
\mathbb{E}[x+z] = \mathbb{E}[x] + \mathbb{E}[z] \tag{1.128}
$$

$$
\text{var}[x+z] = \text{var}[x] + \text{var}[z].\tag{1.129}
$$

**1.11** ( $\star$ ) By setting the derivatives of the log likelihood function (1.54) with respect to  $\mu$ and  $\sigma^2$  equal to zero, verify the results (1.55) and (1.56).

**1.12**  $(\star \star)$  **www** Using the results (1.49) and (1.50), show that

$$
\mathbb{E}[x_n x_m] = \mu^2 + I_{nm} \sigma^2 \tag{1.130}
$$

where  $x_n$  and  $x_m$  denote data points sampled from a Gaussian distribution with mean  $\mu$  and variance  $\sigma^2$ , and  $I_{nm}$  satisfies  $I_{nm} = 1$  if  $n = m$  and  $I_{nm} = 0$  otherwise. Hence prove the results (1.57) and (1.58).

- **1.13**  $(\star)$  Suppose that the variance of a Gaussian is estimated using the result (1.56) but with the maximum likelihood estimate  $\mu_{ML}$  replaced with the true value  $\mu$  of the mean. Show that this estimator has the property that its expectation is given by the true variance  $\sigma^2$ .
- **1.14**  $(\star \star)$  Show that an arbitrary square matrix with elements  $w_{ij}$  can be written in the form  $w_{ij} = w_{ij}^{\rm S} + w_{ij}^{\rm A}$  where  $w_{ij}^{\rm S}$  and  $w_{ij}^{\rm A}$  are symmetric and anti-symmetric matrices, respectively, satisfying  $w_{ij}^{\rm S} = w_{ji}^{\rm S}$  and  $w_{ij}^{\rm A} = -w_{ji}^{\rm A}$  for all i and j. Now consider the second order term in a higher order polynomial in  $D$  dimensions, given by

$$
\sum_{i=1}^{D} \sum_{j=1}^{D} w_{ij} x_i x_j.
$$
 (1.131)

Show that

$$
\sum_{i=1}^{D} \sum_{j=1}^{D} w_{ij} x_i x_j = \sum_{i=1}^{D} \sum_{j=1}^{D} w_{ij}^{S} x_i x_j
$$
 (1.132)

so that the contribution from the anti-symmetric matrix vanishes. We therefore see that, without loss of generality, the matrix of coefficients  $w_{ij}$  can be chosen to be symmetric, and so not all of the  $D<sup>2</sup>$  elements of this matrix can be chosen independently. Show that the number of independent parameters in the matrix  $w_{ij}^{\rm S}$  is given by  $D(D+1)/2$ .

**1.15**  $(\star \star \star)$  **www** In this exercise and the next, we explore how the number of independent parameters in a polynomial grows with the order  $M$  of the polynomial and with the dimensionality D of the input space. We start by writing down the  $M<sup>th</sup>$  order term for a polynomial in  $D$  dimensions in the form

$$
\sum_{i_1=1}^{D} \sum_{i_2=1}^{D} \cdots \sum_{i_M=1}^{D} w_{i_1 i_2 \cdots i_M} x_{i_1} x_{i_2} \cdots x_{i_M}.
$$
 (1.133)

The coefficients  $w_{i_1 i_2 \cdots i_M}$  comprise  $D^M$  elements, but the number of independent parameters is significantly fewer due to the many interchange symmetries of the factor  $x_{i_1} x_{i_2} \cdots x_{i_M}$ . Begin by showing that the redundancy in the coefficients can be removed by rewriting this  $\dot{M}$ <sup>th</sup> order term in the form

$$
\sum_{i_1=1}^{D} \sum_{i_2=1}^{i_1} \cdots \sum_{i_M=1}^{i_{M-1}} \widetilde{w}_{i_1 i_2 \cdots i_M} x_{i_1} x_{i_2} \cdots x_{i_M}.
$$
 (1.134)

Note that the precise relationship between the  $\tilde{w}$  coefficients and w coefficients need not be made explicit. Use this result to show that the number of *independent* parameters  $n(D, M)$ , which appear at order M, satisfies the following recursion relation

$$
n(D, M) = \sum_{i=1}^{D} n(i, M - 1).
$$
 (1.135)

Next use proof by induction to show that the following result holds

$$
\sum_{i=1}^{D} \frac{(i+M-2)!}{(i-1)!(M-1)!} = \frac{(D+M-1)!}{(D-1)!M!}
$$
\n(1.136)

which can be done by first proving the result for  $D = 1$  and arbitrary M by making use of the result  $0! = 1$ , then assuming it is correct for dimension D and verifying that it is correct for dimension  $D + 1$ . Finally, use the two previous results, together with proof by induction, to show

$$
n(D, M) = \frac{(D + M - 1)!}{(D - 1)! M!}.
$$
\n(1.137)

To do this, first show that the result is true for  $M = 2$ , and any value of  $D \ge 1$ , by comparison with the result of Exercise  $1.14$ . Then make use of  $(1.135)$ , together with (1.136), to show that, if the result holds at order  $M - 1$ , then it will also hold at order M

**1.16**  $(\star \star \star)$  In Exercise 1.15, we proved the result (1.135) for the number of independent parameters in the  $M<sup>th</sup>$  order term of a D-dimensional polynomial. We now find an expression for the total number  $N(D, M)$  of independent parameters in all of the terms up to and including the M6th order. First show that  $N(D, M)$  satisfies

$$
N(D, M) = \sum_{m=0}^{M} n(D, m)
$$
\n(1.138)

where  $n(D,m)$  is the number of independent parameters in the term of order m. Now make use of the result (1.137), together with proof by induction, to show that

$$
N(d, M) = \frac{(D+M)!}{D! M!}.
$$
\n(1.139)

This can be done by first proving that the result holds for  $M = 0$  and arbitrary  $D \geq 1$ , then assuming that it holds at order M, and hence showing that it holds at order  $M + 1$ . Finally, make use of Stirling's approximation in the form

$$
n! \simeq n^n e^{-n} \tag{1.140}
$$

for large n to show that, for  $D \gg M$ , the quantity  $N(D,M)$  grows like  $D^M$ , and for  $M \gg D$  it grows like  $M^D$ . Consider a cubic  $(M = 3)$  polynomial in D dimensions, and evaluate numerically the total number of independent parameters for (i)  $D = 10$  and (ii)  $D = 100$ , which correspond to typical small-scale and medium-scale machine learning applications.

**1.17**  $(\star \star)$  **www** The gamma function is defined by

$$
\Gamma(x) \equiv \int_0^\infty u^{x-1} e^{-u} \, \mathrm{d}u. \tag{1.141}
$$

Using integration by parts, prove the relation  $\Gamma(x + 1) = x\Gamma(x)$ . Show also that  $\Gamma(1) = 1$  and hence that  $\Gamma(x + 1) = x!$  when x is an integer.

**1.18**  $(\star \star)$  **www** We can use the result (1.126) to derive an expression for the surface area  $S_D$ , and the volume  $V_D$ , of a sphere of unit radius in D dimensions. To do this, consider the following result, which is obtained by transforming from Cartesian to polar coordinates

$$
\prod_{i=1}^{D} \int_{-\infty}^{\infty} e^{-x_i^2} dx_i = S_D \int_0^{\infty} e^{-r^2} r^{D-1} dr.
$$
 (1.142)

Using the definition  $(1.141)$  of the Gamma function, together with  $(1.126)$ , evaluate both sides of this equation, and hence show that

$$
S_D = \frac{2\pi^{D/2}}{\Gamma(D/2)}.
$$
\n(1.143)

Next, by integrating with respect to radius from  $0$  to 1, show that the volume of the unit sphere in  $D$  dimensions is given by

$$
V_D = \frac{S_D}{D}.\tag{1.144}
$$

Finally, use the results  $\Gamma(1) = 1$  and  $\Gamma(3/2) = \sqrt{\pi}/2$  to show that (1.143) and (1.144) reduce to the usual expressions for  $D = 2$  and  $D = 3$ .

**1.19**  $(\star \star)$  Consider a sphere of radius a in D-dimensions together with the concentric hypercube of side  $2a$ , so that the sphere touches the hypercube at the centres of each of its sides. By using the results of Exercise 1.18, show that the ratio of the volume of the sphere to the volume of the cube is given by

$$
\frac{\text{volume of sphere}}{\text{volume of cube}} = \frac{\pi^{D/2}}{D2^{D-1}\Gamma(D/2)}.\tag{1.145}
$$

Now make use of Stirling's formula in the form

$$
\Gamma(x+1) \simeq (2\pi)^{1/2} e^{-x} x^{x+1/2} \tag{1.146}
$$

which is valid for  $x \gg 1$ , to show that, as  $D \to \infty$ , the ratio (1.145) goes to zero. Show also that the ratio of the distance from the centre of the hypercube to one of the corners, divided by the perpendicular distance to one of the sides, is  $\sqrt{D}$ , which therefore goes to  $\infty$  as  $D \to \infty$ . From these results we see that, in a space of high dimensionality, most of the volume of a cube is concentrated in the large number of corners, which themselves become very long 'spikes'!

**1.20**  $(\star \star)$  **www** In this exercise, we explore the behaviour of the Gaussian distribution in high-dimensional spaces. Consider a Gaussian distribution in  $D$  dimensions given by

$$
p(\mathbf{x}) = \frac{1}{(2\pi\sigma^2)^{D/2}} \exp\left(-\frac{\|\mathbf{x}\|^2}{2\sigma^2}\right). \tag{1.147}
$$

We wish to find the density with respect to radius in polar coordinates in which the direction variables have been integrated out. To do this, show that the integral of the probability density over a thin shell of radius r and thickness  $\epsilon$ , where  $\epsilon \ll 1$ , is given by  $p(r) \in \mathbf{where}$ 

$$
p(r) = \frac{S_D r^{D-1}}{(2\pi\sigma^2)^{D/2}} \exp\left(-\frac{r^2}{2\sigma^2}\right)
$$
 (1.148)

where  $S_D$  is the surface area of a unit sphere in D dimensions. Show that the function p(r) has a single stationary point located, for large D, at  $\hat{r} \simeq \sqrt{D}\sigma$ . By considering  $p(\hat{x} \mid \epsilon)$  where  $\epsilon \ll \hat{x}$  show that for large D, at  $\hat{r} \simeq \sqrt{D}\sigma$ . By considering  $p(\hat{r} + \epsilon)$  where  $\epsilon \ll \hat{r}$ , show that for large D,

$$
p(\hat{r} + \epsilon) = p(\hat{r}) \exp\left(-\frac{3\epsilon^2}{2\sigma^2}\right)
$$
 (1.149)

which shows that  $\hat{r}$  is a maximum of the radial probability density and also that  $p(r)$ decays exponentially away from its maximum at  $\hat{r}$  with length scale  $\sigma$ . We have already seen that  $\sigma \ll \hat{r}$  for large D, and so we see that most of the probability mass is concentrated in a thin shell at large radius. Finally, show that the probability density  $p(x)$  is larger at the origin than at the radius  $\hat{r}$  by a factor of  $\exp(D/2)$ . We therefore see that most of the probability mass in a high-dimensional Gaussian distribution is located at a different radius from the region of high probability density. This property of distributions in spaces of high dimensionality will have important consequences when we consider Bayesian inference of model parameters in later chapters.

**1.21**  $(\star \star)$  Consider two nonnegative numbers a and b, and show that, if  $a \leq b$ , then  $a \leq (ab)^{1/2}$ . Use this result to show that, if the decision regions of a two-class classification problem are chosen to minimize the probability of misclassification, this probability will satisfy

$$
p(\text{mistake}) \leqslant \int \left\{ p(\mathbf{x}, C_1) p(\mathbf{x}, C_2) \right\}^{1/2} \, \mathrm{d}\mathbf{x}.
$$
 (1.150)

- **1.22** ( $\star$ ) **www** Given a loss matrix with elements  $L_{ki}$ , the expected risk is minimized if, for each **x**, we choose the class that minimizes (1.81). Verify that, when the loss matrix is given by  $L_{kj} = 1 - I_{kj}$ , where  $I_{kj}$  are the elements of the identity matrix, this reduces to the criterion of choosing the class having the largest posterior probability. What is the interpretation of this form of loss matrix?
- **1.23**  $(\star)$  Derive the criterion for minimizing the expected loss when there is a general loss matrix and general prior probabilities for the classes.

- **1.24**  $(\star \star)$  **www** Consider a classification problem in which the loss incurred when an input vector from class  $\mathcal{C}_k$  is classified as belonging to class  $\mathcal{C}_j$  is given by the loss matrix  $L_{ki}$ , and for which the loss incurred in selecting the reject option is  $\lambda$ . Find the decision criterion that will give the minimum expected loss. Verify that this reduces to the reject criterion discussed in Section 1.5.3 when the loss matrix is given by  $L_{kj} = 1 - I_{kj}$ . What is the relationship between  $\lambda$  and the rejection threshold  $\theta$ ?
- **1.25**  $(\star)$  **www** Consider the generalization of the squared loss function (1.87) for a single target variable t to the case of multiple target variables described by the vector **t** given by

$$
\mathbb{E}[L(\mathbf{t}, \mathbf{y}(\mathbf{x}))] = \iint \|\mathbf{y}(\mathbf{x}) - \mathbf{t}\|^2 p(\mathbf{x}, \mathbf{t}) \, \mathrm{d}\mathbf{x} \, \mathrm{d}\mathbf{t}.\tag{1.151}
$$

Using the calculus of variations, show that the function  $y(x)$  for which this expected loss is minimized is given by  $y(x) = \mathbb{E}_t[t|x]$ . Show that this result reduces to (1.89) for the case of a single target variable  $t$ .

- **1.26**  $(\star)$  By expansion of the square in (1.151), derive a result analogous to (1.90) and hence show that the function  $y(x)$  that minimizes the expected squared loss for the case of a vector **t** of target variables is again given by the conditional expectation of **t**.
- **1.27**  $(\star \star)$  **www** Consider the expected loss for regression problems under the  $L_q$  loss function given by (1.91). Write down the condition that  $y(x)$  must satisfy in order to minimize  $\mathbb{E}[L_q]$ . Show that, for  $q = 1$ , this solution represents the conditional median, i.e., the function  $y(x)$  such that the probability mass for  $t < y(x)$  is the same as for  $t \geq y(\mathbf{x})$ . Also show that the minimum expected  $L_q$  loss for  $q \to 0$  is given by the conditional mode i.e. by the function  $y(\mathbf{x})$  equal to the value of t that given by the conditional mode, i.e., by the function  $y(\mathbf{x})$  equal to the value of t that maximizes  $p(t|\mathbf{x})$  for each **x**.
- **1.28** ( $\star$ ) In Section 1.6, we introduced the idea of entropy  $h(x)$  as the information gained on observing the value of a random variable x having distribution  $p(x)$ . We saw that, for independent variables x and y for which  $p(x, y) = p(x)p(y)$ , the entropy functions are additive, so that  $h(x, y) = h(x) + h(y)$ . In this exercise, we derive the relation between h and p in the form of a function  $h(p)$ . First show that  $h(p^2)$  =  $2h(p)$ , and hence by induction that  $h(p^n) = nh(p)$  where n is a positive integer. Hence show that  $h(p^{n/m})=(n/m)h(p)$  where m is also a positive integer. This implies that  $h(p^x) = xh(p)$  where x is a positive rational number, and hence by continuity when it is a positive real number. Finally, show that this implies  $h(p)$ must take the form  $h(p) \propto \ln p$ .
- **1.29**  $(\star)$  **www** Consider an M-state discrete random variable x, and use Jensen's inequality in the form (1.115) to show that the entropy of its distribution  $p(x)$  satisfies  $H[x] \leqslant \ln M$ .
- **1.30**  $(\star \star)$  Evaluate the Kullback-Leibler divergence (1.113) between two Gaussians  $p(x) = \mathcal{N}(x|\mu, \sigma^2)$  and  $q(x) = \mathcal{N}(x|m, s^2)$ .

Image /page/19/Figure/1 description: Table 1.3 is titled "The joint distribution p(x, y) for two binary variables x and y used in Exercise 1.39." The table shows a column for the variable y with a value of 0.

0 1  $x = \begin{bmatrix} 0 & 1/3 & 1/3 \\ 0 & 0 & 1/3 \end{bmatrix}$  $1 \mid 0 \quad 1/3$ 

**1.31**  $(\star \star)$  **www** Consider two variables **x** and **y** having joint distribution  $p(\mathbf{x}, \mathbf{y})$ . Show that the differential entropy of this pair of variables satisfies

$$
H[\mathbf{x}, \mathbf{y}] \le H[\mathbf{x}] + H[\mathbf{y}] \tag{1.152}
$$

with equality if, and only if, **x** and **y** are statistically independent.

- **1.32** ( $\star$ ) Consider a vector **x** of continuous variables with distribution  $p(\mathbf{x})$  and corresponding entropy  $H[x]$ . Suppose that we make a nonsingular linear transformation of **x** to obtain a new variable  $y = Ax$ . Show that the corresponding entropy is given by  $H[y] = H[x] + \ln |A|$  where  $|A|$  denotes the determinant of A.
- **1.33**  $(\star \star)$  Suppose that the conditional entropy H[y|x] between two discrete random variables x and y is zero. Show that, for all values of x such that  $p(x) > 0$ , the variable y must be a function of x, in other words for each x there is only one value of y such that  $p(y|x) \neq 0$ .
- **1.34**  $(\star \star)$  **www** Use the calculus of variations to show that the stationary point of the functional  $(1.108)$  is given by  $(1.108)$ . Then use the constraints  $(1.105)$ ,  $(1.106)$ , and (1.107) to eliminate the Lagrange multipliers and hence show that the maximum entropy solution is given by the Gaussian (1.109).
- **1.35**  $(\star)$  **www** Use the results (1.106) and (1.107) to show that the entropy of the univariate Gaussian (1.109) is given by (1.110).
- **1.36 ()** A strictly convex function is defined as one for which every chord lies above the function. Show that this is equivalent to the condition that the second derivative of the function be positive.
- **1.37** ( $\star$ ) Using the definition (1.111) together with the product rule of probability, prove the result (1.112).
- **1.38**  $(\star \star)$  **www** Using proof by induction, show that the inequality (1.114) for convex functions implies the result (1.115).
- **1.39**  $(\star \star \star)$  Consider two binary variables x and y having the joint distribution given in Table 1.3.

Evaluate the following quantities

| (a) $H[x]$ | (c) $H[y x]$ | (e) $H[x, y]$   |
|------------|--------------|-----------------|
| (b) $H[y]$ | (d) $H[x y]$ | (f) $I[x, y]$ . |

Draw a diagram to show the relationship between these various quantities.

- **1.40** ( $\star$ ) By applying Jensen's inequality (1.115) with  $f(x) = \ln x$ , show that the arithmetic mean of a set of real numbers is never less than their geometrical mean.
- **1.41** (**\***) **www** Using the sum and product rules of probability, show that the mutual information  $I(\mathbf{x}, \mathbf{y})$  satisfies the relation (1.121).
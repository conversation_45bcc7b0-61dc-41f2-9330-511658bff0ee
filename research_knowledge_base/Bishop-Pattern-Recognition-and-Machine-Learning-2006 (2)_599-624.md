eigenvector decomposition of the sample covariance matrix, the EM approach is iterative and so might appear to be less attractive. However, each cycle of the EM algorithm can be computationally much more efficient than conventional PCA in spaces of high dimensionality. To see this, we note that the eigendecomposition of the covariance matrix requires  $O(D^3)$  computation. Often we are interested only in the first  $M$  eigenvectors and their corresponding eigenvalues, in which case we can use algorithms that are  $O(MD^2)$ . However, the evaluation of the covariance matrix itself takes  $O(ND^2)$  computations, where N is the number of data points. Algorithms such as the snapshot method (<PERSON>, 1987), which assume that the eigenvectors are linear combinations of the data vectors, avoid direct evaluation of the covariance matrix but are  $O(N^3)$  and hence unsuited to large data sets. The EM algorithm described here also does not construct the covariance matrix explicitly. Instead, the most computationally demanding steps are those involving sums over the data set that are  $O(NDM)$ . For large D, and  $M \ll D$ , this can be a significant saving compared to  $O(ND^2)$  and can offset the iterative nature of the EM algorithm.

Note that this EM algorithm can be implemented in an on-line form in which each D-dimensional data point is read in and processed and then discarded before the next data point is considered. To see this, note that the quantities evaluated in the E step (an M-dimensional vector and an  $M \times M$  matrix) can be computed for each data point separately, and in the M step we need to accumulate sums over data points, which we can do incrementally. This approach can be advantageous if both Nand *D* are large.

Because we now have a fully probabilistic model for PCA, we can deal with missing data, provided that it is missing at random, by marginalizing over the distribution of the unobserved variables. Again these missing values can be treated using the EM algorithm. We give an example of the use of this approach for data visualization in Figure 12.11.

Another elegant feature of the EM approach is that we can take the limit  $\sigma^2 \to 0$ , corresponding to standard PCA, and still obtain a valid EM-like algorithm (Roweis, 1998). From (12.55), we see that the only quantity we need to compute in the Estep is  $\mathbb{E}[z_n]$ . Furthermore, the M step is simplified because  $\mathbf{M} = \mathbf{W}^T \mathbf{W}$ . To emphasize the simplicity of the algorithm, let us define  $\widetilde{\mathbf{X}}$  to be a matrix of size  $N \times D$  whose  $n<sup>th</sup>$  row is given by the vector  $x_n - \overline{x}$  and similarly define  $\Omega$  to be a matrix of size  $D \times M$  whose  $n<sup>th</sup>$  row is given by the vector  $\mathbb{E}[z_n]$ . The E step (12.54) of the EM algorithm for PCA then becomes

$$
\mathbf{\Omega} = (\mathbf{W}_{\text{old}}^{\text{T}} \mathbf{W}_{\text{old}})^{-1} \mathbf{W}_{\text{old}}^{\text{T}} \widetilde{\mathbf{X}}
$$
 (12.58)

and the M step (12.56) takes the form

$$
\mathbf{W}_{\text{new}} = \widetilde{\mathbf{X}}^{\text{T}} \Omega^{\text{T}} (\Omega \Omega^{\text{T}})^{-1}.
$$
 (12.59)

Again these can be implemented in an on-line form. These equations have a simple interpretation as follows. From our earlier discussion, we see that the E step involves an orthogonal projection of the data points onto the current estimate for the principal subspace. Correspondingly, the M step represents a re-estimation of the principal

Exercise 12.17

Image /page/1/Figure/1 description: Two scatter plots are shown side-by-side. Both plots display data points represented by red crosses, blue plus signs, and green circles. The points are arranged in a general horizontal distribution, with a cluster of red crosses at the bottom left and a more spread-out group of blue plus signs and green circles in the upper middle. The overall pattern suggests a separation of data points into distinct groups.

Figure 12.11 Probabilistic PCA visualization of a portion of the oil flow data set for the first 100 data points. The left-hand plot shows the posterior mean projections of the data points on the principal subspace. The right-hand plot is obtained by first randomly omitting 30% of the variable values and then using EM to handle the missing values. Note that each data point then has at least one missing measurement but that the plot is very similar to the one obtained without missing values.

subspace to minimize the squared reconstruction error in which the projections are fixed.

We can give a simple physical analogy for this EM algorithm, which is easily visualized for  $D = 2$  and  $M = 1$ . Consider a collection of data points in two dimensions, and let the one-dimensional principal subspace be represented by a solid rod. Now attach each data point to the rod via a spring obeying Hooke's law (stored energy is proportional to the square of the spring's length). In the E step, we keep the rod fixed and allow the attachment points to slide up and down the rod so as to minimize the energy. This causes each attachment point (independently) to position itself at the orthogonal projection of the corresponding data point onto the rod. In the M step, we keep the attachment points fixed and then release the rod and allow it to move to the minimum energy position. The E and M steps are then repeated until a suitable convergence criterion is satisfied, as is illustrated in Figure 12.12.

# 12.2.3 Bayesian PCA

So far in our discussion of PCA, we have assumed that the value  $M$  for the dimensionality of the principal subspace is given. In practice, we must choose a suitable value according to the application. For visualization, we generally choose  $M = 2$ , whereas for other applications the appropriate choice for M may be less clear. One approach is to plot the eigenvalue spectrum for the data set, analogous to the example in Figure 12.4 for the off-line digits data set, and look to see if the eigenvalues naturally form two groups comprising a set of small values separated by a significant gap from a set of relatively large values, indicating a natural choice for  $M$ . In practice, such a gap is often not seen.

Image /page/2/Figure/1 description: This figure displays six scatter plots labeled (a) through (f), illustrating the EM algorithm for PCA. Each plot shows green data points, blue points representing cluster centers, and a red line indicating the principal component. In plot (a), the initial state shows data points and a dashed line representing the initial principal component. Plots (b) through (f) show the progression of the algorithm, with the blue points moving and the red line adjusting to better fit the data, demonstrating the iterative refinement of the principal component and cluster centers.

Figure 12.12 Synthetic data illustrating the EM algorithm for PCA defined by (12.58) and (12.59). (a) A data set X with the data points shown in green, together with the true principal components (shown as eigenvectors scaled by the square roots of the eigenvalues). (b) Initial configuration of the principal subspace defined by W. shown in red, together with the projections of the latent points Z into the data space, given by  $\mathbf{ZW}^{\mathrm{T}}$ , shown in cyan. (c) After one M step, the latent space has been updated with Z held fixed. (d) After the successive E step, the values of Z have been updated, giving orthogonal projections, with W held fixed. (e) After the second M step. (f) After the second E step.

Section 1.3

Because the probabilistic PCA model has a well-defined likelihood function, we could employ cross-validation to determine the value of dimensionality by selecting the largest log likelihood on a validation data set. Such an approach, however, can become computationally costly, particularly if we consider a probabilistic mixture of PCA models (Tipping and Bishop, 1999a) in which we seek to determine the appropriate dimensionality separately for each component in the mixture.

Given that we have a probabilistic formulation of PCA, it seems natural to seek a Bayesian approach to model selection. To do this, we need to marginalize out the model parameters  $\mu$ , W, and  $\sigma^2$  with respect to appropriate prior distributions. This can be done by using a variational framework to approximate the analytically intractable marginalizations (Bishop, 1999b). The marginal likelihood values, given by the variational lower bound, can then be compared for a range of different values of  $M$  and the value giving the largest marginal likelihood selected.

Here we consider a simpler approach introduced by based on the evidence ap-

**Figure 12.13** Probabilistic graphical model for Bayesian PCA in which the distribution over the parameter matrix W is governed by a vector  $\alpha$  of hyperparameters.

Image /page/3/Figure/2 description: This is a graphical representation of a probabilistic model. The model includes variables z\_n, x\_n, W, sigma squared, mu, and alpha. The variables z\_n and x\_n are enclosed within a plate labeled N, indicating that these variables are repeated N times. There are directed arrows showing the dependencies between these variables. Specifically, sigma squared and mu are inputs to x\_n, and z\_n is also an input to x\_n. W is an output that depends on alpha. The variable x\_n is represented as a filled circle, suggesting it is an observed variable, while z\_n and W are represented as unfilled circles, indicating they are latent variables. Sigma squared, mu, and alpha are represented as filled circles with dots, indicating they are parameters or constants.

*proximation,* which is appropriate when the number of data points is relatively large and the corresponding posterior distribution is tightly peaked (Bishop, 1999a). It involves a specific choice of prior over  $W$  that allows surplus dimensions in the principal subspace to be pruned out of the model. This corresponds to an example of *automatic relevance determination,* or *ARD,* discussed in Section 7.2.2. Specifically, we define an independent Gaussian prior over each column of W, which represent the vectors defining the principal subspace. Each such Gaussian has an independent variance governed by a precision hyperparameter  $\alpha_i$  so that

$$
p(\mathbf{W}|\alpha) = \prod_{i=1}^{M} \left(\frac{\alpha_i}{2\pi}\right)^{D/2} \exp\left\{-\frac{1}{2}\alpha_i \mathbf{w}_i^{\mathsf{T}} \mathbf{w}_i\right\}
$$
(12.60)

where  $w_i$  is the i<sup>th</sup> column of W. The resulting model can be represented using the directed graph shown in Figure 12.13.

The values for  $\alpha_i$  will be found iteratively by maximizing the marginal likelihood function in which W has been integrated out. As a result of this optimization, some of the  $\alpha_i$  may be driven to infinity, with the corresponding parameters vector  $w_i$  being driven to zero (the posterior distribution becomes a delta function at the origin) giving a sparse solution. The effective dimensionality of the principal subspace is then determined by the number of finite  $\alpha_i$  values, and the corresponding vectors  $w_i$  can be thought of as 'relevant' for modelling the data distribution. In this way, the Bayesian approach is automatically making the trade-off between improving the fit to the data, by using a larger number of vectors  $w_i$  with their corresponding eigenvalues  $\lambda_i$  each tuned to the data, and reducing the complexity of the model by suppressing some of the  $w_i$  vectors. The origins of this sparsity were discussed earlier in the context of relevance vector machines.

The values of  $\alpha_i$  are re-estimated during training by maximizing the log marginal likelihood given by

$$
p(\mathbf{X}|\alpha, \mu, \sigma^2) = \int p(\mathbf{X}|\mathbf{W}, \mu, \sigma^2) p(\mathbf{W}|\alpha) d\mathbf{W}
$$
 (12.61)

where the log of  $p(X|W, \mu, \sigma^2)$  is given by (12.43). Note that for simplicity we also treat  $\mu$  and  $\sigma^2$  as parameters to be estimated, rather than defining priors over these parameters.

*Section 7.2*

Because this integration is intractable, we make use of the Laplace approximation. If we assume that the posterior distribution is sharply peaked, as will occur for sufficiently large data sets, then the re-estimation equations obtained by maximizing the marginal likelihood with respect to  $\alpha_i$  take the simple form

> $\alpha_i^{\text{new}} = \frac{D}{\mathbf{w}_i^{\text{T}} \mathbf{w}_i}$ (12.62)

which follows from (3.98), noting that the dimensionality of  $w_i$  is D. These reestimations are interleaved with the EM algorithm updates for determining  $W$  and  $\sigma^2$ . The E-step equations are again given by (12.54) and (12.55). Similarly, the Mstep equation for  $\sigma^2$  is again given by (12.57). The only change is to the M-step equation for  $W$ , which is modified to give

$$
\mathbf{W}_{\text{new}} = \left[\sum_{n=1}^{N} (\mathbf{x}_n - \overline{\mathbf{x}}) \mathbb{E}[\mathbf{z}_n]^{\text{T}}\right] \left[\sum_{n=1}^{N} \mathbb{E}[\mathbf{z}_n \mathbf{z}_n^{\text{T}}] + \sigma^2 \mathbf{A}\right]^{-1}
$$
(12.63)

where  $\mathbf{A} = \text{diag}(\alpha_i)$ . The value of  $\mu$  is given by the sample mean, as before.

If we choose  $M = D - 1$  then, if all  $\alpha_i$  values are finite, the model represents a full-covariance Gaussian, while if all the  $\alpha_i$  go to infinity the model is equivalent to an isotropic Gaussian, and so the model can encompass all pennissible values for the effective dimensionality of the principal subspace. It is also possible to consider smaller values of  $M$ , which will save on computational cost but which will limit the maximum dimensionality of the subspace. A comparison of the results of this algorithm with standard probabilistic PCA is shown in Figure 12.14.

Bayesian PCA provides an opportunity to illustrate the Gibbs sampling algorithm discussed in Section 11.3. Figure 12.15 shows an example of the samples from the hyperparameters  $\ln \alpha_i$  for a data set in  $D = 4$  dimensions in which the dimensionality of the latent space is  $M = 3$  but in which the data set is generated from a probabilistic PCA model having one direction of high variance, with the remaining directions comprising low variance noise. This result shows clearly the presence of three distinct modes in the posterior distribution. At each step of the iteration, one of the hyperparameters has a small value and the remaining two have large values, so that two of the three latent variables are suppressed. During the course of the Gibbs sampling, the solution makes sharp transitions between the three modes.

The model described here involves a prior only over the matrix  $W$ . A fully Bayesian treatment of PCA, including priors over  $\mu$ ,  $\sigma^2$ , and  $\alpha$ , and solved using variational methods, is described in Bishop (1999b). For a discussion of various Bayesian approaches to detennining the appropriate dimensionality for a PCA model, see Minka (2001c).

## 12.2.4 Factor analysis

Factor analysis is a linear-Gaussian latent variable model that is closely related to probabilistic PCA. Its definition differs from that of probabilistic PCA only in that the conditional distribution of the observed variable  $x$  given the latent variable  $z$  is

*Section 4.4*

*Section 3.5.3*

Image /page/5/Figure/1 description: Figure 12.14 displays two Hinton diagrams of a matrix W. Each element of the matrix is represented by a square. The size of the square corresponds to the magnitude of the element, and the color of the square indicates the sign: black squares represent negative values, and white squares represent positive values. The diagram on the left shows a 7x7 grid of squares, while the diagram on the right shows a 7x2 grid of squares. The squares vary in size and color, illustrating the distribution of positive and negative values within the matrix.

a square (white for positive and black for negative values) whose area is proportional to the magnitude of that element. The synthetic data sel comprises 300 data points in  $D = 10$  dimensions sampled from a Gaussian distribution having standard deviation 1.0 in 3 directions and standard deviation 0.5 in the remaining 7 directions for a data set in  $D = 10$  dimensions having  $M = 3$  directions with larger variance than the remaining 7 directions. The left-hand plot shows the result from maximum likelihood probabilistic PCA, and the left-hand plot shows the corresponding result from Bayesian PCA. We see how the Bayesian model is able to discover the appropriate dimensionality by suppressing the 6 surplus degrees of freedom.

taken to have a diagonal rather than an isotropic covariance so that

$$
p(\mathbf{x}|\mathbf{z}) = \mathcal{N}(\mathbf{x}|\mathbf{W}\mathbf{z} + \boldsymbol{\mu}, \boldsymbol{\Psi})
$$
 (12.64)

where  $\Psi$  is a  $D \times D$  diagonal matrix. Note that the factor analysis model, in common with probabilistic PCA, assumes that the observed variables  $x_1, \ldots, x_p$  are independent. given the latent variable z. In essence. the factor analysis model is explaining the observed covariance structure of the data by representing the independent variance associated with each coordinate in the matrix  $\Psi$  and capturing the covariance between variables in the matrix  $W$ . In the factor analysis literature, the columns of W, which capture the correlations between observed variables, are called *factor loadings*, and the diagonal elements of  $\Psi$ , which represent the independent noise variances for each of the variables, are called *uniquenesses*.

The origins of factor analysis are as old as those of PCA. and discussions of factor analysis can be found in the books by Everitt (1984). Bartholomew (1987), and Basilevsky (1994). Links between factor analysis and PCA were investigated by Lawley (1953) and Anderson (1963) who showed that at stationary points of the likelihood function, for a factor analysis model with  $\Psi = \sigma^2 I$ , the columns of W are scaled eigenvectors of the sample covariance matrix, and  $\sigma^2$  is the average of the discarded eigenvalues. Later. Tipping and Bishop (1999b) showed that the maximum of the log likelihood function occurs when the eigenvectors comprising W are chosen to be the principal eigenvectors.

Making use of (2.115). we see that the marginal distribution for the observed

Figure 12.15 Gibbs sampling for Bayesian PCA showing plots of  $\ln \alpha_i$ versus iteration number for three  $\alpha$  values. showing transitions between the three modes of the posterior distribution.

Image /page/6/Figure/2 description: The image displays three line graphs stacked vertically. Each graph plots a fluctuating signal over time, with the y-axis ranging from 0 to 10. The top graph is in red, the middle graph is in green, and the bottom graph is in dark blue. All three graphs show periods where the signal is relatively high, around 7-8, and periods where it drops to a lower level, around 2-3. The transitions between these high and low states appear abrupt. The red graph shows two distinct transitions from high to low and then back to high. The green graph shows a transition from low to high, then a drop back to low, and finally a rise back to high. The blue graph exhibits a more complex pattern with multiple transitions between high and low states, creating a series of pulses.

variable is given by  $p(x) = \mathcal{N}(x|\mu, C)$  where now

$$
C = WWT + \Psi.
$$
 (12.65)

Exercise 12.19

As with probabilistic PCA, this model is invariant to rotations in the latent space.

Historically, factor analysis has been the subject of controversy when attempts have been made to place an interpretation on the individual factors (the coordinates in z-space), which has proven problematic due to the nonidentifiability of factor analysis associated with rotations in this space. From our perspective, however, we shall view factor analysis as a form of latent variable density model, in which the form of the latent space is of interest but not the particular choice of coordinates used to describe it. If we wish to remove the degeneracy associated with latent space rotations, we must consider non-Gaussian latent variable distributions, giving rise to independent component analysis (ICA) models.

We can determine the parameters  $\mu$ , W, and  $\Psi$  in the factor analysis model by maximum likelihood. The solution for  $\mu$  is again given by the sample mean. However, unlike probabilistic PCA, there is no longer a closed-form maximum likelihood solution for W, which must therefore be found iteratively. Because factor analysis is a latent variable model, this can be done using an EM algorithm (Rubin and Thayer, 1982) that is analogous to the one used for probabilistic PCA. Specifically, the E-step equations are given by

$$
\mathbb{E}[\mathbf{z}_n] = \mathbf{G}\mathbf{W}^{\mathrm{T}}\mathbf{\Psi}^{-1}(\mathbf{x}_n - \overline{\mathbf{x}})
$$
(12.66)

$$
\mathbb{E}[\mathbf{z}_n \mathbf{z}_n^{\mathrm{T}}] = \mathbf{G} + \mathbb{E}[\mathbf{z}_n] \mathbb{E}[\mathbf{z}_n]^{\mathrm{T}} \tag{12.67}
$$

where we have defined

$$
\mathbf{G} = (\mathbf{I} + \mathbf{W}^{\mathrm{T}} \boldsymbol{\Psi}^{-1} \mathbf{W})^{-1}.
$$
 (12.68)

Note that this is expressed in a form that involves inversion of matrices of size  $M \times M$ rather than  $D \times D$  (except for the  $D \times D$  diagonal matrix  $\Psi$  whose inverse is trivial

Section 12.4

Exercise 12.21

*Exercise 12.22*

to compute in  $O(D)$  steps), which is convenient because often  $M \ll D$ . Similarly, the M-step equations take the form

$$
\mathbf{W}^{\text{new}} = \left[ \sum_{n=1}^{N} (\mathbf{x}_n - \overline{\mathbf{x}}) \mathbb{E}[\mathbf{z}_n]^{\text{T}} \right] \left[ \sum_{n=1}^{N} \mathbb{E}[\mathbf{z}_n \mathbf{z}_n^{\text{T}}] \right]^{-1}
$$
(12.69)

$$
\mathbf{\Psi}^{\text{new}} = \text{diag}\left\{\mathbf{S} - \mathbf{W}_{\text{new}} \frac{1}{N} \sum_{n=1}^{N} \mathbb{E}[\mathbf{z}_n](\mathbf{x}_n - \overline{\mathbf{x}})^{\text{T}}\right\}
$$
(12.70)

where the 'diag' operator sets all of the nondiagonal elements of a matrix to zero. A Bayesian treatment of the factor analysis model can be obtained by a straightforward application of the techniques discussed in this book.

Another difference between probabilistic PCA and factor analysis concerns their *Exercise* 12.25 different behaviour under transformations of the data set. For PCA and probabilistic PCA, if we rotate the coordinate system in data space, then we obtain exactly the same fit to the data but with the W matrix transformed by the corresponding rotation matrix. However, for factor analysis, the analogous property is that if we make a component-wise re-scaling of the data vectors, then this is absorbed into a corresponding re-scaling of the elements of  $\Psi$ .

# 12.3. Kernel pea

In Chapter 6, we saw how the technique of kernel substitution allows us to take an algorithm expressed in terms of scalar products of the form  $x<sup>T</sup>x'$  and generalize that algorithm by replacing the scalar products with a nonlinear kernel. Here we apply this technique of kernel substitution to principal component analysis, thereby obtaining a nonlinear generalization called *kernel* peA (Scholkopf *et al., 1998).*

Consider a data set  $\{x_n\}$  of observations, where  $n = 1, ..., N$ , in a space of dimensionality D. In order to keep the notation uncluttered, we shall assume that we have already subtracted the sample mean from each of the vectors  $x_n$ , so that  $\sum_{n}$  **x**<sub>n</sub> = 0. The first step is to express conventional PCA in such a form that the data vectors  $\{x_n\}$  appear only in the form of the scalar products  $x_n^Tx_m$ . Recall that the principal components are defined by the eigenvectors  $\mathbf{u}_i$  of the covariance matrix

$$
\mathbf{S}\mathbf{u}_i = \lambda_i \mathbf{u}_i \tag{12.71}
$$

where  $i = 1, \ldots, D$ . Here the  $D \times D$  sample covariance matrix **S** is defined by

$$
\mathbf{S} = \frac{1}{N} \sum_{n=1}^{N} \mathbf{x}_n \mathbf{x}_n^{\mathrm{T}},
$$
\n(12.72)

and the eigenvectors are normalized such that  $\mathbf{u}_i^{\mathrm{T}} \mathbf{u}_i = 1$ .

Now consider a nonlinear transformation  $\phi(\mathbf{x})$  into an M-dimensional feature space, so that each data point  $x_n$  is thereby projected onto a point  $\phi(x_n)$ . We can

Image /page/8/Figure/1 description: The image displays two scatter plots side-by-side. The left plot shows data points (red dots) scattered in a 2D space with axes labeled x1 and x2. Several curved green lines are overlaid on the data, suggesting a non-linear relationship or boundaries. The right plot shows a similar set of red dots, but in a different 2D space with axes labeled phi1 and phi2. In this plot, the data points appear more linearly correlated along a diagonal line. A thick blue arrow labeled 'v1' originates from the center and points along this line, indicating a principal direction or component. Several thin green lines are perpendicular to this blue arrow, intersecting it at various points, possibly representing projections or variances.

Figure 12.16 Schematic illustration of kernel PCA. A data set in the original data space (left-hand plot) is projected by a nonlinear transformation  $\phi(x)$  into a feature space (right-hand plot). By performing PCA in the feature space, we obtain the principal components, of which the first is shown in blue and is denoted by the vector  $v_1$ . The green lines in feature space indicate the linear projections onto the first principal component, which correspond to nonlinear projections in the original data space. Note that in general it is not possible to represent the nonlinear principal component by a vector in  $x$  space.

now perform standard PCA in the feature space, which implicitly defines a nonlinear principal component model in the original data space, as illustrated in Figure 12.16.

For the moment, let us assume that the projected data set also has zero mean, so that  $\sum_{n} \phi(\mathbf{x}_n) = 0$ . We shall return to this point shortly. The  $M \times M$  sample covariance matrix in feature space is given by

$$
\mathbf{C} = \frac{1}{N} \sum_{n=1}^{N} \phi(\mathbf{x}_n) \phi(\mathbf{x}_n)^{\mathrm{T}}
$$
(12.73)

and its eigenvector expansion is defined by

$$
Cv_i = \lambda_i v_i \tag{12.74}
$$

 $i = 1, \ldots, M$ . Our goal is to solve this eigenvalue problem without having to work explicitly in the feature space. From the definition of  $C$ , the eigenvector equations tells us that  $v_i$  satisfies

$$
\frac{1}{N} \sum_{n=1}^{N} \phi(\mathbf{x}_n) \left\{ \phi(\mathbf{x}_n)^{\mathrm{T}} \mathbf{v}_i \right\} = \lambda_i \mathbf{v}_i
$$
\n(12.75)

and so we see that (provided  $\lambda_i > 0$ ) the vector  $\mathbf{v}_i$  is given by a linear combination of the  $\phi(\mathbf{x}_n)$  and so can be written in the form

$$
\mathbf{v}_i = \sum_{n=1}^{N} a_{in} \phi(\mathbf{x}_n). \tag{12.76}
$$

Substituting this expansion back into the eigenvector equation, we obtain

$$
\frac{1}{N} \sum_{n=1}^{N} \phi(\mathbf{x}_n) \phi(\mathbf{x}_n)^{\mathrm{T}} \sum_{m=1}^{N} a_{im} \phi(\mathbf{x}_m) = \lambda_i \sum_{n=1}^{N} a_{in} \phi(\mathbf{x}_n).
$$
 (12.77)

The key step is now to express this in terms of the kernel function  $k(\mathbf{x}_n, \mathbf{x}_m) =$  $\phi(\mathbf{x}_n)^T\phi(\mathbf{x}_m)$ , which we do by multiplying both sides by  $\phi(\mathbf{x}_l)^T$  to give

$$
\frac{1}{N}\sum_{n=1}^{N}k(\mathbf{x}_l,\mathbf{x}_n)\sum_{m=1}^{m}a_{im}k(\mathbf{x}_n,\mathbf{x}_m)=\lambda_i\sum_{n=1}^{N}a_{in}k(\mathbf{x}_l,\mathbf{x}_n).
$$
 (12.78)

This can be written in matrix notation as

$$
\mathbf{K}^2 \mathbf{a}_i = \lambda_i N \mathbf{K} \mathbf{a}_i \tag{12.79}
$$

where  $a_i$  is an N-dimensional column vector with elements  $a_{ni}$  for  $n = 1, \ldots, N$ . We can find solutions for  $a_i$  by solving the following eigenvalue problem

$$
\mathbf{K}\mathbf{a}_i = \lambda_i N \mathbf{a}_i \tag{12.80}
$$

in which we have removed a factor of  $\bf{K}$  from both sides of (12.79). Note that the solutions of  $(12.79)$  and  $(12.80)$  differ only by eigenvectors of K having zero

*Exercise 12.26*

The normalization condition for the coefficients  $a_i$  is obtained by requiring that the eigenvectors in feature space be normalized. Using (12.76) and (12.80), we have

eigenvalues that do not affect the principal components projection.

$$
1 = \mathbf{v}_i^{\mathrm{T}} \mathbf{v}_i = \sum_{n=1}^{N} \sum_{m=1}^{N} a_{in} a_{im} \phi(\mathbf{x}_n)^{\mathrm{T}} \phi(\mathbf{x}_m) = \mathbf{a}_i^{\mathrm{T}} \mathbf{K} \mathbf{a}_i = \lambda_i N \mathbf{a}_i^{\mathrm{T}} \mathbf{a}_i.
$$
 (12.81)

Having solved the eigenvector problem, the resulting principal component projections can then also be cast in terms of the kernel function so that, using (12.76), the projection of a point  $x$  onto eigenvector  $i$  is given by

$$
y_i(\mathbf{x}) = \phi(\mathbf{x})^{\mathrm{T}} \mathbf{v}_i = \sum_{n=1}^{N} a_{in} \phi(\mathbf{x})^{\mathrm{T}} \phi(\mathbf{x}_n) = \sum_{n=1}^{N} a_{in} k(\mathbf{x}, \mathbf{x}_n)
$$
(12.82)

and so again is expressed in terms of the kernel function.

In the original D-dimensional x space there are *D* orthogonal eigenvectors and hence we can find at most *D* linear principal components. The dimensionality M of the feature space, however, can be much larger than *D* (even infinite), and thus we can find a number of nonlinear principal components that can exceed D. Note, however, that the number of nonzero eigenvalues cannot exceed the number *N* of data points, because (even if  $M > N$ ) the covariance matrix in feature space has rank at most equal to *N.* This is reflected in the fact that kernel PCA involves the eigenvector expansion of the  $N \times N$  matrix **K**.

So far we have assumed that the projected data set given by  $\phi(\mathbf{x}_n)$  has zero mean, which in general will not be the case. We cannot simply compute and then subtract off the mean, since we wish to avoid working directly in feature space, and so again, we formulate the algorithm purely in terms of the kernel function. The projected data points after centralizing, denoted  $\phi(\mathbf{x}_n)$ , are given by

$$
\widetilde{\phi}(\mathbf{x}_n) = \phi(\mathbf{x}_n) - \frac{1}{N} \sum_{l=1}^{N} \phi(\mathbf{x}_l)
$$
\n(12.83)

and the corresponding elements of the Gram matrix are given by

$$
\widetilde{K}_{nm} = \widetilde{\phi}(\mathbf{x}_n)^{\mathrm{T}} \widetilde{\phi}(\mathbf{x}_m)
$$

$$
= \phi(\mathbf{x}_n)^{\mathrm{T}} \phi(\mathbf{x}_m) - \frac{1}{N} \sum_{l=1}^{N} \phi(\mathbf{x}_n)^{\mathrm{T}} \phi(\mathbf{x}_l)
$$

$$
- \frac{1}{N} \sum_{l=1}^{N} \phi(\mathbf{x}_l)^{\mathrm{T}} \phi(\mathbf{x}_m) + \frac{1}{N^2} \sum_{j=1}^{N} \sum_{l=1}^{N} \phi(\mathbf{x}_j)^{\mathrm{T}} \phi(\mathbf{x}_l)
$$

$$
= k(\mathbf{x}_n, \mathbf{x}_m) - \frac{1}{N} \sum_{l=1}^{N} k(\mathbf{x}_l, \mathbf{x}_m)
$$

$$
- \frac{1}{N} \sum_{l=1}^{N} k(\mathbf{x}_n, \mathbf{x}_l) + \frac{1}{N^2} \sum_{j=1}^{N} \sum_{l=1}^{N} k(\mathbf{x}_j, \mathbf{x}_l).
$$
**(12.84)**

This can be expressed in matrix notation as

 $\overline{a}$ 

$$
\ddot{\mathbf{K}} = \mathbf{K} - \mathbf{1}_N \mathbf{K} - \mathbf{K} \mathbf{1}_N + \mathbf{1}_N \mathbf{K} \mathbf{1}_N \tag{12.85}
$$

where  $\mathbf{1}_N$  denotes the  $N \times N$  matrix in which every element takes the value  $1/N$ . Thus we can evaluate  $\widetilde{K}$  using only the kernel function and then use  $\widetilde{K}$  to determine the eigenvalues and eigenvectors. Note that the standard PCA algorithm is recovered as a special case if we use a linear kernel  $k(x, x') = x^T x'$ . Figure 12.17 shows an example of kernel PCA applied to a synthetic data set (Schölkopf et al., 1998). Here a 'Gaussian' kernel of the form

$$
k(\mathbf{x}, \mathbf{x}') = \exp(-\|\mathbf{x} - \mathbf{x}'\|^2 / 0.1) \tag{12.86}
$$

is applied to a synthetic data set. The lines correspond to contours along which the projection onto the corresponding principal component, defined by

$$
\phi(\mathbf{x})^{\mathrm{T}} \mathbf{v}_i = \sum_{n=1}^{N} a_{in} k(\mathbf{x}, \mathbf{x}_n)
$$
 (12.87)

is constant.

*Exercise 12.27*

Image /page/11/Picture/1 description: The image displays a grid of eight plots, arranged in two rows of four. Each plot features a dark teal background with blue contour lines, indicating a topographical or density map. Scattered across each plot are numerous small red circles, representing data points. Above each plot, a title indicates an "Eigenvalue" followed by a numerical value. The eigenvalues range from 21.72 down to 2.53, suggesting a Principal Component Analysis or similar dimensionality reduction technique. The distribution and clustering of the red points vary across the plots, with some showing dense clusters and others more dispersed points, all overlaid on the contour maps.

Figure 12.17 Example of kernel PCA, with a Gaussian kernel applied to a synthetic data set in two dimensions, showing the first eight eigenfunctions along with their eigenvalues. The contours are lines along which the projection onto the corresponding principal component is constant. Note how the first two eigenvectors separate the three clusters, the next three eigenvectors split each of the cluster into halves, and the following three eigenvectors again split the clusters into halves along directions orthogonal to the previous splits.

One obvious disadvantage of kernel PCA is that it involves finding the eigenvectors of the  $N \times N$  matrix **K** rather than the  $D \times D$  matrix **S** of conventional linear PCA, and so in practice for large data sets approximations are often used.

Finally, we note that in standard linear PCA, we often retain some reduced number  $L < D$  of eigenvectors and then approximate a data vector  $x_n$  by its projection  $\hat{\mathbf{x}}_n$  onto the L-dimensional principal subspace, defined by

$$
\widehat{\mathbf{x}}_n = \sum_{i=1}^L \left( \mathbf{x}_n^{\mathrm{T}} \mathbf{u}_i \right) \mathbf{u}_i.
$$
 (12.88)

In kernel PCA, this will in general not be possible. To see this, note that the mapping  $\phi(\mathbf{x})$  maps the *D*-dimensional x space into a *D*-dimensional *manifold* in the M-dimensional feature space  $\phi$ . The vector x is known as the *pre-image* of the corresponding point  $\phi(\mathbf{x})$ . However, the projection of points in feature space onto the linear PCA subspace in that space will typically not lie on the nonlinear  $D$ dimensional manifold and so will not have a corresponding pre-image in data space. Techniques have therefore been proposed for finding approximate pre-images (Bakir et al., 2004).

# 12.4. Nonlinear Latent Variable Models

**In** this chapter, we have focussed on the simplest class of models having continuous latent variables, namely those based on linear-Gaussian distributions. As well as having great practical importance, these models are relatively easy to analyse and to fit to data and can also be used as components in more complex models. Here we consider briefly some generalizations of this framework to models that are either nonlinear or non-Gaussian, or both.

**In** fact, the issues of nonlinearity and non-Gaussianity are related because a general probability density can be obtained from a simple fixed reference density, such as a Gaussian, by making a nonlinear change of variables. This idea forms the basis of several practical latent variable models as we shall see shortly.

# 12.4.1 Independent component analysis

We begin by considering models in which the observed variables are related linearly to the latent variables, but for which the latent distribution is non-Gaussian. An important class of such models, known as *independent component analysis,* or  $\textit{ICA}$ , arises when we consider a distribution over the latent variables that factorizes, so that

$$
p(\mathbf{z}) = \prod_{j=1}^{M} p(z_j). \tag{12.89}
$$

To understand the role of such models, consider a situation in which two people are talking at the same time, and we record their voices using two microphones. If we ignore effects such as time delay and echoes, then the signals received by the microphones at any point in time will be given by linear combinations of the amplitudes of the two voices. The coefficients of this linear combination will be constant, and if we can infer their values from sample data, then we can invert the mixing process (assuming it is nonsingular) and thereby obtain two clean signals each of which contains the voice of just one person. This is an example of a problem called *blind source separation* in which 'blind' refers to the fact that we are given only the mixed data, and neither the original sources nor the mixing coefficients are observed (Cardoso, 1998).

This type of problem is sometimes addressed using the following approach (MacKay, 2003) in which we ignore the temporal nature of the signals and treat the successive samples as i.i.d. We consider a generative model in which there are two latent variables corresponding to the unobserved speech signal amplitudes, and there are two observed variables given by the signal values at the microphones. The latent variables have a joint distribution that factorizes as above, and the observed variables are given by a linear combination of the latent variables. There is no need to include a noise distribution because the number of latent variables equals the number of observed variables, and therefore the marginal distribution of the observed variables will not in general be singular, so the observed variables are simply deterministic linear combinations of the latent variables. Given a data set of observations, the

*Exercise 12.28*

likelihood function for this model is a function of the coefficients in the linear combination. The log likelihood can be maximized using gradient-based optimization giving rise to a particular version of independent component analysis.

The success of this approach requires that the latent variables have non-Gaussian distributions. To see this, recall that in probabilistic PCA (and in factor analysis) the latent-space distribution is given by a zero-mean isotropic Gaussian. The model therefore cannot distinguish between two different choices for the latent variables where these differ simply by a rotation in latent space. This can be verified directly by noting that the marginal density (12.35), and hence the likelihood function, is unchanged if we make the transformation  $W \rightarrow WR$  where R is an orthogonal matrix satisfying  $\mathbf{R}\mathbf{R}^{\mathrm{T}} = \mathbf{I}$ , because the matrix C given by (12.36) is itself invariant. Extending the model to allow more general Gaussian latent distributions does not change this conclusion because, as we have seen, such a model is equivalent to the zero-mean isotropic Gaussian latent variable model.

Another way to see why a Gaussian latent variable distribution in a linear model is insufficient to find independent components is to note that the principal components represent a rotation of the coordinate system in data space such as to diagonalize the covariance matrix, so that the data distribution in the new coordinates is then uncorrelated. Although zero correlation is a necessary condition for independence it is not, however, sufficient. In practice, a common choice for the latent-variable distribution is given by

$$
p(z_j) = \frac{1}{\pi \cosh(z_j)} = \frac{1}{\pi(e^{z_j} + e^{-z_j})}
$$
(12.90)

which has heavy tails compared to a Gaussian, reflecting the observation that many real-world distributions also exhibit this property.

The original ICA model (Bell and Sejnowski, 1995) was based on the optimization of an objective function defined by information maximization. One advantage of a probabilistic latent variable formulation is that it helps to motivate and formulate generalizations of basic ICA. For instance, *independent factor analysis* (Attias, 1999a) considers a model in which the number of latent and observed variables can differ, the observed variables are noisy, and the individual latent variables have flexible distributions modelled by mixtures of Gaussians. The log likelihood for this model is maximized using EM, and the reconstruction of the latent variables is approximated using a variational approach. Many other types of model have been considered, and there is now a huge literature on ICA and its applications (Jutten and Herault, 1991; Comon *et at.,* 1991; Amari *et at.,* 1996; Pearlmutter and Parra, 1997; Hyvarinen and Oja, 1997; Hinton *et at.,* 2001; Miskin and MacKay, 2001; Hojen-Sorensen *et at.,* 2002; Choudrey and Roberts, 2003; Chan *et at.,* 2003; Stone, 2004).

## 12.4.2 Autoassociative neural networks

In Chapter 5 we considered neural networks in the context of supervised learning, where the role of the network is to predict the output variables given values

*Exercise 12.29*

Figure 12.18 An autoassociative multilayer perceptron having two layers of weights. Such a network is trained to map input vectors onto themselves by minimization *ot* a sum-ot-squares error. Even with nonlinear units in the hidden layer, such a network is equivalent to linear principal component analysis. Links representing bias parameters have been omitted for clarity.

Image /page/14/Figure/2 description: This is a diagram of a neural network. On the left side, labeled "inputs", there are two nodes, labeled x\_D and x\_1. On the right side, labeled "outputs", there are also two nodes, labeled x\_D and x\_1. In the middle, there are two hidden layer nodes, labeled z\_M and z\_1. There are solid lines connecting the input nodes to the hidden layer nodes, and solid lines connecting the hidden layer nodes to the output nodes. There are also dotted lines connecting the input nodes to the output nodes. An arrow is shown above the diagram, pointing to the right.

for the input variables. However, neural networks have also been applied to unsupervised learning where they have been used for dimensionality reduction. This is achieved by using a network having the same number of outputs as inputs, and optimizing the weights so as to minimize some measure of the reconstruction error between inputs and outputs with respect to a set of training data.

Consider first a multilayer perceptron of the form shown in Figure 12.18, having D inputs, D output units and M hidden units, with  $M < D$ . The targets used to train the network are simply the input vectors themselves, so that the network is attempting to map each input vector onto itself. Such a network is said to form an *autoassociative* mapping. Since the number of hidden units is smaller than the number of inputs, a perfect reconstruction of all input vectors is not in general possible. We therefore determine the network parameters w by minimizing an error function which captures the degree of mismatch between the input vectors and their reconstructions. In particular, we shall choose a sum-of-squares error of the form

$$
E(\mathbf{w}) = \frac{1}{2} \sum_{n=1}^{N} \|\mathbf{y}(\mathbf{x}_n, \mathbf{w}) - \mathbf{x}_n\|^2.
$$
 (12.91)

If the hidden units have linear activations functions, then it can be shown that the error function has a unique global minimum, and that at this minimum the network performs a projection onto the  $M$ -dimensional subspace which is spanned by the first M principal components of the data (Bourlard and Kamp, 1988; Baldi and Hornik, 1989). Thus, the vectors of weights which lead into the hidden units in Figure 12.18 form a basis set which spans the principal subspace. Note, however, that these vectors need not be orthogonal or normalized. This result is unsurprising, since both principal component analysis and the neural network are using linear dimensionality reduction and are minimizing the same sum-of-squares error function.

It might be thought that the limitations of a linear dimensionality reduction could be overcome by using nonlinear (sigmoidal) activation functions for the hidden units in the network in Figure 12.18. However, even with nonlinear hidden units, the minimum error solution is again given by the projection onto the principal component subspace (Bourlard and Kamp, 1988). There is therefore no advantage in using twolayer neural networks to perform dimensionality reduction. Standard techniques for principal component analysis (based on singular value decomposition) are guaranteed to give the correct solution in finite time, and they also generate an ordered set of eigenvalues with corresponding orthonormal eigenvectors.

Figure 12.19 Addition of extra hidden layers of nonlinear units gives an autoassociative network which can perform a nonlinear dimensionality reduction.

Image /page/15/Figure/2 description: This is a diagram of a neural network. The network has an input layer with two nodes labeled x\_D and x\_1. The output layer also has two nodes labeled x\_D and x\_1. Between the input and output layers, there are two hidden layers. The first hidden layer has two nodes, and the second hidden layer has one node. All connections between layers are shown as solid lines, except for the connections between the input nodes and the first hidden layer, which are shown as dotted lines. Arrows labeled F1 and F2 are shown above the network, indicating forces. The text 'inputs' is placed to the left of the input layer, and the text 'outputs' is placed to the right of the output layer. The text 'non-linear' is placed below the hidden layers, indicating a non-linear activation function.

The situation is different, however, if additional hidden layers are permitted in the network. Consider the four-layer autoassociativc network shown in Figure 12.19. Again the output units are linear, and the  $M$  units in the second hidden layer can also be linear. however, the first and third hidden layers have sigmoidal nonlinear activation functions. The network is again trained by minimization of the error function (12.91). We can view this network as two successive functional mappings  $\mathbf{F}_1$  and  $\mathbf{F}_2$ , as indicated in Figure 12.19. The first mapping  $\mathbf{F}_1$  projects the original Ddimensional data onto an  $M$ -dimensional subspace  $S$  defined by the activations of the units in the second hidden layer. Because of the presence of the first hidden layer of nonlinear units. this mapping is very general. and in particular is not restricted to being linear. Similarly. the second half of the network defines an arbitrary functional mapping from the  $M$ -dimensional space back into the original  $D$ -dimensional input space. This has a simple geometrical interpretation, as indicated for the case  $D = 3$ and  $M = 2$  in Figure 12.20.

Such a network effectively performs a nonlinear principal component analysis.

Image /page/15/Figure/5 description: The image depicts a transformation process in a 3D space. On the left, a coordinate system with axes labeled x1, x2, and x3 is shown. A single point is marked in this space. An arrow labeled F1 curves from this point towards a shaded square region labeled S in a second coordinate system with axes labeled z1 and z2. From the square region S, another arrow labeled F2 curves towards a shaded, wavy surface in a third coordinate system, also with axes labeled x1, x2, and x3. The overall illustration shows how a point and a region are mapped through two successive transformations.

Figure 12.20 Geometrical interpretation of the mappings performed by the network in Figure 12.19 for the case of  $D=3$  inputs and  $M=2$  units in the middle hidden layer. The function  $\mathbf{F}_2$  maps from an M-dimensional space *S* into a *D*-dimensional space and therefore defines the way in which the space *S* is embedded within the original x-space. Since the mapping  $\mathbf{F}_2$  can be nonlinear, the embedding of *S* can be nonplanar, as indicated in the figure. The mapping  $F_1$  then defines a projection of points in the original D-dimensional space into the  $M$ -dimensional subspace  $S$ .

**It** has the advantage of not being limited to linear transformations, although it contains standard principal component analysis as a special case. However, training the network now involves a nonlinear optimization problem, since the error function (12.91) is no longer a quadratic function of the network parameters. Computationally intensive nonlinear optimization techniques must be used, and there is the risk of finding a suboptimal local minimum of the error function. Also, the dimensionality of the subspace must be specified before training the network.

# 12.4.3 Modelling nonlinear Manifolds

As we have already noted, many natural sources of data correspond to lowdimensional, possibly noisy, nonlinear manifolds embedded within the higher dimensional observed data space. Capturing this property explicitly can lead to improved density modelling compared with more general methods. Here we consider briefly a range of techniques that attempt to do this.

One way to model the nonlinear structure is through a combination of linear models, so that we make a piece-wise linear approximation to the manifold. This can be obtained, for instance, by using a clustering technique such as *K* -means based on Euclidean distance to partition the data set into local groups with standard PCA applied to each group. A better approach is to use the reconstruction error for cluster assignment (Kambhatla and Leen, 1997; Hinton *et al.,* 1997) as then a common cost function is being optimized in each stage. However, these approaches still suffer from limitations due to the absence of an overall density model. By using probabilistic PCA it is straightforward to define a fully probabilistic model simply by considering a mixture distribution in which the components are probabilistic PCA models (Tipping and Bishop, 1999a). Such a model has both discrete latent variables, corresponding to the discrete mixture, as well as continuous latent variables, and the likelihood function can be maximized using the EM algorithm. A fully Bayesian treatment, based on variational inference (Bishop and Winn, 2000), allows the number of components in the mixture, as well as the effective dimensionalities of the individual models, to be inferred from the data. There are many variants of this model in which parameters such as the  $W$  matrix or the noise variances are tied across components in the mixture, or in which the isotropic noise distributions are replaced by diagonal ones, giving rise to a mixture of factor analysers (Ghahramani and Hinton, 1996a; Ghahramani and Beal, 2000). The mixture of probabilistic PCA models can also be extended hierarchically to produce an interactive data visualization algorithm (Bishop and Tipping, 1998).

An alternative to considering a mixture of linear models is to consider a single nonlinear model. Recall that conventional PCA finds a linear subspace that passes close to the data in a least-squares sense. This concept can be extended to onedimensional nonlinear surfaces in the form of *principal curves* (Hastie and Stuetzle, 1989). We can describe a curve in a  $D$ -dimensional data space using a vector-valued function  $f(\lambda)$ , which is a vector each of whose elements is a function of the scalar  $\lambda$ . There are many possible ways to parameterize the curve, of which a natural choice is the arc length along the curve. For any given point  $\hat{x}$  in data space, we can find the point on the curve that is closest in Euclidean distance. We denote this point by  $\lambda = g_f(\mathbf{x})$  because it depends on the particular curve  $f(\lambda)$ . For a continuous data density  $p(x)$ , a principal curve is defined as one for which every point on the curve is the mean of all those points in data space that project to it, so that

$$
\mathbb{E}\left[\mathbf{x}|g_{\mathbf{f}}(\mathbf{x})=\lambda\right]=\mathbf{f}(\lambda). \tag{12.92}
$$

For a given continuous density, there can be many principal curves. In practice, we are interested in finite data sets, and we also wish to restrict attention to smooth curves. Hastie and Stuetzle (1989) propose a two-stage iterative procedure for finding such principal curves, somewhat reminiscent of the EM algorithm for PCA. The curve is initialized using the first principal component, and then the algorithm alternates between a data projection step and curve re-estimation step. In the projection step, each data point is assigned to a value of  $\lambda$  corresponding to the closest point on the curve. Then in the re-estimation step, each point on the curve is given by a weighted average of those points that project to nearby points on the curve, with points closest on the curve given the greatest weight. In the case where the subspace is constrained to be linear, the procedure converges to the first principal component and is equivalent to the power method for finding the largest eigenvector of the covariance matrix. Principal curves can be generalized to multidimensional manifolds called *principal surfaces* although these have found limited use due to the difficulty of data smoothing in higher dimensions even for two-dimensional manifolds.

PCA is often used to project a data set onto a lower-dimensional space, for example two dimensional, for the purposes of visualization. Another linear technique with a similar aim is *multidimensional scaling,* or *MDS* (Cox and Cox, 2000). It finds a low-dimensional projection of the data such as to preserve, as closely as possible, the pairwise distances between data points, and involves finding the eigenvectors of the distance matrix. In the case where the distances are Euclidean, it gives equivalent results to PCA. The MDS concept can be extended to a wide variety of data types specified in terms of a similarity matrix, giving *nonmetric* MDS.

Two other nonprobabilistic methods for dimensionality reduction and data visualization are worthy of mention. *Locally linear embedding,* or *LLE* (Roweis and Saul, 2000) first computes the set of coefficients that best reconstructs each data point from its neighbours. These coefficients are arranged to be invariant to rotations, translations, and scalings of that data point and its neighbours, and hence they characterize the local geometrical properties of the neighbourhood. LLE then maps the high-dimensional data points down to a lower dimensional space while preserving these neighbourhood coefficients. If the local neighbourhood for a particular data point can be considered linear, then the transformation can be achieved using a combination of translation, rotation, and scaling, such as to preserve the angles formed between the data points and their neighbours. Because the weights are invariant to these transformations, we expect the same weight values to reconstruct the data points in the low-dimensional space as in the high-dimensional data space. In spite of the nonlinearity, the optimization for LLE does not exhibit local minima.

In *isometric feature mapping,* or *isomap* (Tenenbaum *et ai.,* 2000), the goal is to project the data to a lower-dimensional space using MDS, but where the dissimilarities are defined in terms of the *geodesic distances* measured along the manifold. For instance, if two points lie on a circle, then the geodesic is the arc-length distance measured around the circumference of the circle not the straight line distance measured along the chord connecting them. The algorithm first defines the neighbourhood for each data point, either by finding the *K* nearest neighbours or by finding all points within a sphere of radius  $\epsilon$ . A graph is then constructed by linking all neighbouring points and labelling them with their Euclidean distance. The geodesic distance between any pair of points is then approximated by the sum of the arc lengths along the shortest path connecting them (which itself is found using standard algorithms). Finally, metric MDS is applied to the geodesic distance matrix to find the low-dimensional projection.

Our focus in this chapter has been on models for which the observed variables are continuous. We can also consider models having continuous latent variables together with discrete observed variables, giving rise to *latent trait* models (Bartholomew, 1987). In this case, the marginalization over the continuous latent variables, even for a linear relationship between latent and observed variables, cannot be performed analytically, and so more sophisticated techniques are required. Tipping (1999) uses variational inference in a model with a two-dimensional latent space, allowing a binary data set to be visualized analogously to the use of PCA to visualize continuous data. Note that this model is the dual of the Bayesian logistic regression problem discussed in Section 4.5. In the case of logistic regression we have N observations of the feature vector  $\phi_n$  which are parameterized by a single parameter vector w, whereas in the latent space visualization model there is a single latent space variable x (analogous to  $\phi$ ) and N copies of the latent variable  $w_n$ . A generalization of probabilistic latent variable models to general exponential family distributions is described in Collins *et al. (2002).*

We have already noted that an arbitrary distribution can be formed by taking a Gaussian random variable and transforming it through a suitable nonlinearity. This is exploited in a general latent variable model called a *density network* (MacKay, 1995; MacKay and Gibbs, 1999) in which the nonlinear function is governed by a multilayered neural network. If the network has enough hidden units, it can approximate a given nonlinear function to any desired accuracy. The downside of having such a flexible model is that the marginalization over the latent variables, required in order to obtain the likelihood function, is no longer analytically tractable. Instead, the likelihood is approximated using Monte Carlo techniques by drawing samples from the Gaussian prior. The marginalization over the latent variables then becomes a simple sum with one term for each sample. However, because a large number of sample points may be required in order to give an accurate representation of the marginal, this procedure can be computationally costly.

If we consider more restricted forms for the nonlinear function, and make an appropriate choice of the latent variable distribution, then we can construct a latent variable model that is both nonlinear and efficient to train. The *generative topographic mapping,* or *GTM* (Bishop *et aI.,* 1996; Bishop *et aI.,* 1997a; Bishop *et aI.,* 1998b) uses a latent distribution that is defined by a finite regular grid of delta functions over the (typically two-dimensional) latent space. Marginalization over the latent space then simply involves summing over the contributions from each of the grid locations.

*Chapter 5*

*Chapter JJ*

Image /page/19/Figure/1 description: Two scatter plots are shown side-by-side. Both plots display data points marked with circles, crosses, and plus signs in green, red, and blue, respectively. The left plot shows three distinct clusters of points. The top cluster is predominantly green circles with some blue plus signs and red crosses interspersed. Below this, there is a dense cluster of blue plus signs. At the bottom, there is a cluster of red crosses. The right plot also shows three clusters, but they are more spread out and less defined. The top cluster consists of green circles and some blue plus signs and red crosses. The middle section has a scattering of blue plus signs and green circles. The bottom section contains a cluster of red crosses, similar to the left plot, but with more points.

Figure 12.21 Plot of the oil flow data set visualized using PCA on the left and GTM on the right. For the GTM model, each data point is plotted at the mean of its posterior distribution in latent space. The nonlinearity of the GTM model allows the separation between the groups of data points to be seen more clearly.

### Chapter 3

Section 1.4

The nonlinear mapping is given by a linear regression model that allows for general nonlinearity while being a linear function of the adaptive parameters. Note that the usual limitation of linear regression models arising from the curse of dimensionality does not arise in the context of the GTM since the manifold generally has two dimensions irrespective of the dimensionality of the data space. A consequence of these two choices is that the likelihood function can be expressed analytically in closed form and can be optimized efficiently using the EM algorithm. The resulting GTM model fits a two-dimensional nonlinear manifold to the data set, and by evaluating the posterior distribution over latent space for the data points, they can be projected back to the latent space for visualization purposes. Figure 12.21 shows a comparison of the oil data set visualized with linear PCA and with the nonlinear GTM.

The GTM can be seen as a probabilistic version of an earlier model called the self organizing map, or SOM (Kohonen, 1982; Kohonen, 1995), which also represents a two-dimensional nonlinear manifold as a regular array of discrete points. The SOM is somewhat reminiscent of the K-means algorithm in that data points are assigned to nearby prototype vectors that are then subsequently updated. Initially, the prototypes are distributed at random, and during the training process they 'self organize' so as to approximate a smooth manifold. Unlike  $K$ -means, however, the SOM is not optimizing any well-defined cost function (Erwin et al., 1992) making it difficult to set the parameters of the model and to assess convergence. There is also no guarantee that the 'self-organization' will take place as this is dependent on the choice of appropriate parameter values for any particular data set.

By contrast, GTM optimizes the log likelihood function, and the resulting model defines a probability density in data space. In fact, it corresponds to a constrained mixture of Gaussians in which the components share a common variance, and the means are constrained to lie on a smooth two-dimensional manifold. This proba-

*Section 6.4*

bilistic foundation also makes it very straightforward to define generalizations of GTM (Bishop *et al.,* 1998a) such as a Bayesian treatment, dealing with missing val-*Section* 6.4 ues, a principled extension to discrete variables, the use of Gaussian processes to define the manifold, or a hierarchical GTM model (Tino and Nabney, 2002).

> Because the manifold in GTM is defined as a continuous surface, not just at the prototype vectors as in the SOM, it is possible to compute the *magnification factors* corresponding to the local expansions and compressions of the manifold needed to fit the data set (Bishop *et al.,* 1997b) as well as the *directional curvatures* of the manifold (Tino *et al.,* 2001). These can be visualized along with the projected data and provide additional insight into the model.

# Exercises

- $(*\star)$  WWW In this exercise, we use proof by induction to show that the linear projection onto an M -dimensional subspace that maximizes the variance of the projected data is defined by the  $M$  eigenvectors of the data covariance matrix  $S$ , given by  $(12.3)$ , corresponding to the M largest eigenvalues. In Section 12.1, this result was proven for the case of  $M = 1$ . Now suppose the result holds for some general value of M and show that it consequently holds for dimensionality  $M + 1$ . To do this, first set the derivative of the variance of the projected data with respect to a vector  $\mathbf{u}_{M+1}$  defining the new direction in data space equal to zero. This should be done subject to the constraints that  $\mathbf{u}_{M+1}$  be orthogonal to the existing vectors  $\mathbf{u}_1, \ldots, \mathbf{u}_M$ , and also that it be normalized to unit length. Use Lagrange multipli-*Appendix E* ers to enforce these constraints. Then make use of the orthonormality properties of the vectors  $\mathbf{u}_1, \dots, \mathbf{u}_M$  to show that the new vector  $\mathbf{u}_{M+1}$  is an eigenvector of S. Finally, show that the variance is maximized if the eigenvector is chosen to be the one corresponding to eigenvector  $\lambda_{M+1}$  where the eigenvalues have been ordered in decreasing value.
  - **12.2**  $(\star \star)$  Show that the minimum value of the PCA distortion measure J given by (12.15) with respect to the  $\mathbf{u}_i$ , subject to the orthonormality constraints (12.7), is obtained when the  $\mathbf{u}_i$  are eigenvectors of the data covariance matrix S. To do this, introduce a matrix  $H$  of Lagrange multipliers, one for each constraint, so that the modified distortion measure, in matrix notation reads

$$
\widetilde{J} = \text{Tr}\left\{\widehat{\mathbf{U}}^{\text{T}}\mathbf{S}\widehat{\mathbf{U}}\right\} + \text{Tr}\left\{\mathbf{H}(\mathbf{I} - \widehat{\mathbf{U}}^{\text{T}}\widehat{\mathbf{U}})\right\} \tag{12.93}
$$

where  $\widehat{U}$  is a matrix of dimension  $D \times (D - M)$  whose columns are given by  $\mathbf{u}_i$ . Now minimize  $\widetilde{J}$  with respect to  $\widehat{U}$  and show that the solution satisfies  $S\widehat{U} = \widehat{U}H$ . Clearly, one possible solution is that the columns of  $\hat{U}$  are eigenvectors of S, in which case  $H$  is a diagonal matrix containing the corresponding eigenvalues. To obtain the general solution, show that  $H$  can be assumed to be a symmetric matrix, and by using its eigenvector expansion show that the general solution to  $S\hat{U} = \hat{U}H$ gives the same value for  $\tilde{J}$  as the specific solution in which the columns of  $\hat{U}$  are

the eigenvectors of S. Because these solutions are all equivalent, it is convenient to choose the eigenvector solution.

- **12.3** ( $\star$ ) Verify that the eigenvectors defined by (12.30) are normalized to unit length, assuming that the eigenvectors  $v_i$  have unit length.
- **12.4** ( $\star$ ) **WWW** Suppose we replace the zero-mean, unit-covariance latent space distribution (12.31) in the probabilistic PCA model by a general Gaussian distribution of the form  $\mathcal{N}(z|\mathbf{m}, \Sigma)$ . By redefining the parameters of the model, show that this leads to an identical model for the marginal distribution  $p(x)$  over the observed variables for any valid choice of m and  $\Sigma$ .
- **12.5**  $(\star \star)$  Let x be a D-dimensional random variable having a Gaussian distribution given by  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma})$ , and consider the M-dimensional random variable given by  $y = Ax + b$  where A is an  $M \times D$  matrix. Show that y also has a Gaussian distribution, and find expressions for its mean and covariance. Discuss the form of this Gaussian distribution for  $M < D$ , for  $M = D$ , and for  $M > D$ .
- **12.6**  $\left(\star\right)$  **WWW** Draw a directed probabilistic graph for the probabilistic PCA model described in Section 12.2 in which the components of the observed variable x are shown explicitly as separate nodes. Hence verify that the probabilistic PCA model has the same independence structure as the naive Bayes model discussed in Section 8.2.2.
- **12.7**  $(\star \star)$  By making use of the results (2.270) and (2.271) for the mean and covariance of a general distribution, derive the result (12.35) for the marginal distribution  $p(x)$ in the probabilistic PCA model.
- **12.8**  $(\star \star)$  WWW By making use of the result (2.116), show that the posterior distribution  $p(\mathbf{z}|\mathbf{x})$  for the probabilistic PCA model is given by (12.42).
- **12.9** ( $\star$ ) Verify that maximizing the log likelihood (12.43) for the probabilistic PCA model with respect to the parameter  $\mu$  gives the result  $\mu_{ML} = \bar{x}$  where  $\bar{x}$  is the mean of the data vectors.
- **12.10**  $(\star \star)$  By evaluating the second derivatives of the log likelihood function (12.43) for the probabilistic PCA model with respect to the parameter  $\mu$ , show that the stationary point  $\mu_{ML} = \bar{x}$  represents the unique maximum.
- **12.11**  $(\star \star)$  **WWW** Show that in the limit  $\sigma^2 \to 0$ , the posterior mean for the probabilistic PCA model becomes an orthogonal projection onto the principal subspace, as in conventional PCA.
- **12.12** ( $\star\star$ ) For  $\sigma^2 > 0$  show that the posterior mean in the probabilistic PCA model is shifted towards the origin relative to the orthogonal projection.
- **12.13**  $(\star \star)$  Show that the optimal reconstruction of a data point under probabilistic PCA, according to the least squares projection cost of conventional PCA, is given by

$$
\widetilde{\mathbf{x}} = \mathbf{W}_{\mathrm{ML}} (\mathbf{W}_{\mathrm{ML}}^{\mathrm{T}} \mathbf{W}_{\mathrm{ML}})^{-1} \mathbf{M} \mathbb{E}[\mathbf{z} | \mathbf{x}]. \tag{12.94}
$$

- **12.14**  $(\star)$  The number of independent parameters in the covariance matrix for the probabilistic PCA model with an  $M$ -dimensional latent space and a  $D$ -dimensional data space is given by (12.51). Verify that in the case of  $M = D - 1$ , the number of independent parameters is the same as in a general covariance Gaussian, whereas for  $M = 0$  it is the same as for a Gaussian with an isotropic covariance.  $M = 0$  it is the same as for a Gaussian with an isotropic covariance.
- **12.15**  $(\star \star)$  **WWW** Derive the M-step equations (12.56) and (12.57) for the probabilistic PCA model by maximization of the expected complete-data log likelihood function given by (12.53).
- **12.16**  $(\star \star \star)$  In Figure 12.11, we showed an application of probabilistic PCA to a data set in which some of the data values were missing at random. Derive the EM algorithm for maximizing the likelihood function for the probabilistic PCA model in this situation. Note that the  $\{z_n\}$ , as well as the missing data values that are components of the vectors  $\{x_n\}$ , are now latent variables. Show that in the special case in which all of the data values are observed, this reduces to the EM algorithm for probabilistic PCA derived in Section 12.2.2.
- **12.17** ( $\star\star$ ) **WWW** Let W be a  $D \times M$  matrix whose columns define a linear subspace of dimensionality M embedded within a data space of dimensionality D, and let  $\mu$ be a D-dimensional vector. Given a data set  $\{x_n\}$  where  $n = 1, \ldots, N$ , we can approximate the data points using a linear mapping from a set of  $M$ -dimensional vectors  $\{z_n\}$ , so that  $x_n$  is approximated by  $Wz_n + \mu$ . The associated sum-ofsquares reconstruction cost is given by

$$
J = \sum_{n=1}^{N} ||\mathbf{x}_n - \boldsymbol{\mu} - \mathbf{W} \mathbf{z}_n||^2.
$$
 (12.95)

First show that minimizing J with respect to  $\mu$  leads to an analogous expression with  $\mathbf{x}_n$  and  $\mathbf{z}_n$  replaced by zero-mean variables  $\mathbf{x}_n - \overline{\mathbf{x}}$  and  $\mathbf{z}_n - \overline{\mathbf{z}}$ , respectively, where  $\overline{\mathbf{x}}$ and  $\overline{z}$  denote sample means. Then show that minimizing  $J$  with respect to  $z_n$ , where W is kept fixed, gives rise to the PCA E step  $(12.58)$ , and that minimizing J with respect to **W**, where  $\{z_n\}$  is kept fixed, gives rise to the PCA M step (12.59).

- **12.18** ( $\star$ ) Derive an expression for the number of independent parameters in the factor analysis model described in Section 12.2.4.
- **12.19**  $(\star \star)$  **WWW** Show that the factor analysis model described in Section 12.2.4 is invariant under rotations of the latent space coordinates.
- **12.20**  $(\star \star)$  By considering second derivatives, show that the only stationary point of the log likelihood function for the factor analysis model discussed in Section 12.2.4 with respect to the parameter  $\mu$  is given by the sample mean defined by (12.1). Furthermore, show that this stationary point is a maximum.
- **12.21**  $(\star \star)$  Derive the formulae (12.66) and (12.67) for the E step of the EM algorithm for factor analysis. Note that from the result of Exercise 12.20, the parameter  $\mu$  can be replaced by the sample mean  $\bar{x}$ .

- **12.22**  $(\star \star)$  Write down an expression for the expected complete-data log likelihood function for the factor analysis model, and hence derive the corresponding M step equations (12.69) and (12.70).
- **12.23** ( $\star$ ) **WWW** Draw a directed probabilistic graphical model representing a discrete mixture of probabilistic PCA models in which each PCA model has its own values of W,  $\mu$ , and  $\sigma^2$ . Now draw a modified graph in which these parameter values are shared between the components of the mixture.
- **12.24**  $(\star \star \star)$  We saw in Section 2.3.7 that Student's t-distribution can be viewed as an infinite mixture of Gaussians in which we marginalize with respect to a continuous latent variable. By exploiting this representation, formulate an EM algorithm for maximizing the log likelihood function for a multivariate Student's t-distribution given an observed set of data points, and derive the forms of the E and M step equations.
- **12.25**  $(\star \star)$  **IVIAN** Consider a linear-Gaussian latent-variable model having a latent space distribution  $p(z) = \mathcal{N}(x|0, I)$  and a conditional distribution for the observed variable  $p(x|z) = \mathcal{N}(x|Wz + \mu, \Phi)$  where  $\Phi$  is an arbitrary symmetric, positivedefinite noise covariance matrix. Now suppose that we make a nonsingular linear transformation of the data variables  $x \rightarrow Ax$ , where A is a  $D \times D$  matrix. If  $\mu_{\text{ML}}$ , W<sub>ML</sub> and  $\Phi_{\text{ML}}$  represent the maximum likelihood solution corresponding to the original untransformed data, show that  $A\mu_{ML}$ ,  $AW_{ML}$ , and  $A\Phi_{ML}A^{T}$  will represent the corresponding maximum likelihood solution for the transformed data set. Finally, show that the form of the model is preserved in two cases: (i)  $\bf{A}$  is a diagonal matrix and  $\Phi$  is a diagonal matrix. This corresponds to the case of factor analysis. The transformed  $\Phi$  remains diagonal, and hence factor analysis is *covariant* under component-wise re-scaling of the data variables; (ii) A is orthogonal and  $\Phi$  is proportional to the unit matrix so that  $\Phi = \sigma^2 I$ . This corresponds to probabilistic PCA. The transformed  $\Phi$  matrix remains proportional to the unit matrix, and hence probabilistic PCA is covariant under a rotation of the axes of data space, as is the case for conventional PCA.
- **12.26**  $(\star \star)$  Show that any vector  $a_i$  that satisfies (12.80) will also satisfy (12.79). Also, show that for any solution of (12.80) having eigenvalue  $\lambda$ , we can add any multiple of an eigenvector of  $\bf{K}$  having zero eigenvalue, and obtain a solution to (12.79) that also has eigenvalue  $\lambda$ . Finally, show that such modifications do not affect the principal-component projection given by (12.82).
- **12.27**  $(\star \star)$  Show that the conventional linear PCA algorithm is recovered as a special case of kernel PCA if we choose the linear kernel function given by  $k(\mathbf{x}, \mathbf{x}') = \mathbf{x}^T \mathbf{x}'$ .
- **12.28**  $(\star \star)$  WWW Use the transformation property (1.27) of a probability density under a change of variable to show that any density  $p(y)$  can be obtained from a fixed density  $q(x)$  that is everywhere nonzero by making a nonlinear change of variable  $y = f(x)$  in which  $f(x)$  is a monotonic function so that  $0 \le f'(x) < \infty$ . Write down the differential equation satisfied by  $f(x)$  and draw a diagram illustrating the transformation of the density.

**12.29** ( $\star \star$ ) WWW Suppose that two variables  $z_1$  and  $z_2$  are independent so that  $p(z_1, z_2) =$  $p(z_1)p(z_2)$ . Show that the covariance matrix between these variables is diagonal. This shows that independence is a sufficient condition for two variables to be uncorrelated. Now consider two variables  $y_1$  and  $y_2$  in which  $-1 \leq y_1 \leq 1$  and  $y_2 = y_2^2$ . Write down the conditional distribution  $p(y_2|y_1)$  and observe that this is dependent on  $y_1$ , showing that the two variables are not independent. Now show that the covariance matrix between these two variables is again diagonal. To do this, use the relation  $p(y_1, y_2) = p(y_1)p(y_2|y_1)$  to show that the off-diagonal terms are zero. This counter-example shows that zero correlation is not a sufficient condition for independence.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
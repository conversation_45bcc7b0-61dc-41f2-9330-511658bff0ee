# <span id="page-0-1"></span>Physical Adversarial Attack Meets Computer Vision: A Decade Survey

<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>

**Abstract**—Despite the impressive achievements of Deep Neural Networks (DNNs) in computer vision, their vulnerability to adversarial attacks remains a critical concern. Extensive research has demonstrated that incorporating sophisticated perturbations into input images can lead to a catastrophic degradation in DNNs' performance. This perplexing phenomenon not only exists in the digital space but also in the physical world. Consequently, it becomes imperative to evaluate the security of DNNs-based systems to ensure their safe deployment in real-world scenarios, particularly in security-sensitive applications. To facilitate a profound understanding of this topic, this paper presents a comprehensive overview of physical adversarial attacks. Firstly, we distill four general steps for launching physical adversarial attacks. Building upon this foundation, we uncover the pervasive role of artifacts carrying adversarial perturbations in the physical world. These artifacts influence each step. To denote them, we introduce a new term: adversarial medium. Then, we take the first step to systematically evaluate the performance of physical adversarial attacks, taking the adversarial medium as a first attempt. Our proposed evaluation metric, *hiPAA*, comprises six perspectives: *Effectiveness*, *Stealthiness*, *Robustness*, *Practicability*, *Aesthetics*, and *Economics*. We also provide comparative results across task categories, together with insightful observations and suggestions for future research directions.

✦

**Index Terms**—Adversarial Attack, Physical World, Adversarial Medium, Computer Vision, Survey.

# **1 INTRODUCTION**

**DEEP Neural Networks (DNNs) have achieved impression [\[1\]](#page-15-0)** to natural language processing [\[2\]](#page-15-1) to speech process-EEP Neural Networks (DNNs) have achieved impres-sive results in a variety of fields: from computer viing [\[3\]](#page-15-2), and it is increasingly empowering many aspects of modern society. Nonetheless, Szegedy *et al.* [\[4\]](#page-15-3) discovered in 2014 that adversarial samples can cause DNNs-based models to produce incorrect predictions, leading to a significant degradation in performance. This is a groundbreaking work that exposes the vulnerability of DNNs, casting a shadow over their reliability and security. Since then, researchers have conducted extensive explorations into adversarial samples, revealing their pervasive existence across a wide range of DNNs-based computer vision tasks [\[5\]](#page-15-4), [\[6\]](#page-15-5), [\[7\]](#page-15-6), [\[8\]](#page-15-7), [\[9\]](#page-15-8),  $[10]$ ,  $[11]$ ,  $[12]$ . They designed adversarial clothing  $[13]$  to evade person detectors, adversarial eyeglasses [\[14\]](#page-15-13) to deceive face recognizers, etc. Increasingly, these methods use

- *Hao Tang is with the National Key Laboratory for Multimedia Information Processing, School of Computer Science, Peking University, Beijing 100871, P.R.China.*
- *Hanxun Yu is with the Colleage of Software and Technology, Zhejiang University, Hangzhou, P.R.China.*
- *Zhubo Li is with the School of Cyber Science and Engineering, Wuhan University, Wuhan, P.R.China.*
- *Zhixiang Wang and Shin'ichi Satoh are with the Digital Content and Media Sciences Research Division, National Institute of Informatics, Japan, and also with the Department of Information and Communication Engineering, Graduate School of Information Science and Technology, The University of Tokyo, Japan.*
- *Luc Van Gool is with the Computer Vision Lab of ETH Zurich, 8092 Z¨urich, Switzerland, and also with KU Leuven, 3000 Leuven, Belgium, and INSAIT, Sofia.*
- *Zheng Wang is the corresponding author. E-mail: <EMAIL>*

<span id="page-0-0"></span>TABLE 1: Comparative analysis of current surveys on physical adversarial attack in computer vision.

| Survey | Physical Attack | Adversarial Medium | Evaluation | Number of Methods | Number of Tasks | Year |
|--------|-----------------|--------------------|------------|-------------------|-----------------|------|
| [15]   | ✓               | ✕                  | ✕          | 5                 | 3               | 2018 |
| [16]   | ✓               | ✕                  | ✕          | 47                | 11              | 2022 |
| [17]   | ✓               | ✕                  | ✕          | 69                | 7               | 2022 |
| [18]   | ✓               | ✕                  | ✕          | 22                | 4               | 2023 |
| Ours   | ✓               | ✓                  | ✓          | 86                | 15              | 2024 |

a class of techniques called adversarial attacks.

Generally, adversarial attacks occur by adding perturbations to input data (e.g., image, video) and fooling the DNNs-based models in the inference stage. Regarding the various domains, adversarial attacks can be categorized into two distinct classes: **(i) Digital Adversarial Attack**, which occurs in the digital space through the addition of subtle perturbations (e.g., style perturbations [\[19\]](#page-15-18) and contextaware perturbations [\[20\]](#page-15-19)). **(ii) Physical Adversarial Attack**, which occurs in the real world using tangible artifacts that contain adversarial perturbations (e.g., adversarial patches  $[21]$ ,  $[22]$  and adversarial stickers  $[23]$ ,  $[24]$ ). Compared to the former, the latter pose an augmented threat to social security, raising significant apprehensions, particularly in safety-critical domains like autonomous driving, video surveillance, and facial biometric systems. To facilitate a profound understanding and provide in-depth insights into this topic, we present a comprehensive review of articles on physical adversarial attacks in computer vision tasks.

Though some existing surveys have also summarized the physical adversarial attack methods [\[15\]](#page-15-14), [\[16\]](#page-15-15), [\[17\]](#page-15-16), [\[18\]](#page-15-17), they primarily focus on listing and categorization, ignoring the evaluation and comparison (see TABLE [1\)](#page-0-0). A unified evaluation criterion is still absent. This motivates us to

<sup>•</sup> *Hui Wei, Xuemei Jia, and Zheng Wang are with the School of Computer Science, National Engineering Research Center for Multimedia Software, Wuhan University, Wuhan, P.R.China.*

<span id="page-1-2"></span><span id="page-1-0"></span>Image /page/1/Figure/1 description: This diagram illustrates the process of adversarial attacks on deep neural networks (DNNs). The first stage shows a benign image being combined with a perturbation to create a threat image, which is then fed into a DNN. The perturbation is updated based on the DNN's output. The second stage depicts the perturbation being applied to a physical scene, potentially through an adversarial medium, which then affects a sensor. The sensor captures a threat image, which is then used to attack the DNN, resulting in a misclassification or failure, indicated by a red 'X' within the DNN's output layer.

Fig. 1: The flow of designing a physical adversarial attack, including four main steps: 1) *Adversarial perturbation generation in the digital space*, 2) *Adversarial medium manufacturing in the physical space*, 3) *Threat image capturing*, and 4) *Attacking*.

take the first step to evaluate the performance of physical adversarial attacks systematically.

To this end, we outline the four general steps (as shown in Fig. [1\)](#page-1-0) required to build a physical adversarial attack:

- 1) Step 1: *Adversarial perturbation generation*. Generating perturbations in the digital domain based on given DNNs-based models, constrained by different attack forms and attack objectives.
- 2) Step 2: *Adversarial medium manufacturing*. Designing appropriate physical medium for carrying the perturbations in alignment with attack forms, and subsequently manufacturing them using suitable materials.
- 3) Step 3: *Threat image capturing*. Applying the adversarial medium in real-world scenarios to be captured by an imaging sensor, thereby generating threat images.
- 4) Step 4: *Attacking*. The captured threat images are fed to the DNNs-based model to initiate attacks.

Note that we introduce the concept of an "adversarial medium" to denote the tangible artifact responsible for carrying the adversarial perturbation in the real world. According to the four steps mentioned above, we discern the significant role of adversarial mediums in building a physical adversarial attack. They determine the form of perturbations (Step 1), impact manufacturing processes (Step 2), and hold relevance for real-world applications (Step 3). Therefore, we embrace an approach centered on adversarial mediums to examine the existing methods, systematically quantifying and evaluating them in the following six perspectives: *Effectiveness*, *Stealthiness*, *Robustness*, *Practicability*, *Aesthetics*, and *Economics*. Meanwhile, we introduce a comprehensive metric, the hexagonal indicator of Physical Adversarial Attack (*hiPAA*), and provide comparative results across task categories, along with insightful observations and suggestions for future research directions.

Our contributions can be summarized as follows:

- 1) Through a comprehensive review of existing methodologies (see TABLEs [3,](#page-5-0) [4,](#page-6-0) [5\)](#page-6-1), we abstract and summarize a general workflow for launching a physical adversarial attack, comprising four distinct steps (see Fig. [1\)](#page-1-0).
- 2) Leveraging this general workflow, we discover that tangible artifacts carrying adversarial perturbations exert substantial influence over attacks, prompting the introduction of the new concept of adversarial medium to represent them.
- 3) As opposed to existing reviews [\[15\]](#page-15-14), [\[16\]](#page-15-15), [\[17\]](#page-15-16), [\[18\]](#page-15-17), we take the first step to systematically evaluate the performance of physical adversarial attack, taking adversarial medium as a first attempt. Our proposed metric, *hiPAA*, comprises six perspectives: *Effectiveness*, *Stealthiness*, *Robustness*, *Practicability*, *Aesthetics*, and *Economics*.

4) We conduct comprehensive comparisons of existing attack methods, and discuss limitations, challenges, and potential directions from the standpoint of real-world applications to facilitate future research.

The remaining article is organized as follows. We first provide a brief introduction to the preliminaries in Sec. [2,](#page-1-1) which cover essential topics and concepts for the proper understanding of this work. Sec. [3](#page-3-0) discusses the concept of adversarial mediums. Sec. [4](#page-3-1) introduces the evaluation metric for conducting a comprehensive comparison. Then we present the recent advancements in physical adversarial attacks according to mainstream visual tasks and systematically evaluate them for each task in Sec. [5.](#page-7-0) Moreover, we provide discussions and future research opportunities in Sec. [6.](#page-13-0) Finally, we conclude this review in Sec. [7.](#page-15-24) An overview of the survey scope is shown in Fig. [2.](#page-2-0) We also provide a regularly updated project page on [https://github.](https://github.com/weihui1308/PAA) [com/weihui1308/PAA.](https://github.com/weihui1308/PAA)

# <span id="page-1-1"></span>**2 PRELIMINARIES**

In this section, we provide a concise introduction to problem formulation, key topics, and concepts, aiming to enhance comprehension of our work.

#### **2.1 Computer Vision**

Computer Vision (CV) aims to enable machines to perceive, observe, and understand the physical world like human eyes. An important milestone was reached when Krizhevsky *et al*. [\[25\]](#page-16-0) proposed AlexNet, which secured victory in the ILSVRC, thus promoting the application of DNNs to address a wide variety of tasks. Up to the present, DNNs-based models have achieved impressive and competitive performances in CV tasks, including classification [\[26\]](#page-16-1), [\[27\]](#page-16-2), segmentation [\[28\]](#page-16-3), [\[29\]](#page-16-4), detection [\[30\]](#page-16-5), [\[31\]](#page-16-6), and re-identification [\[32\]](#page-16-7), [\[33\]](#page-16-8), [\[34\]](#page-16-9). This survey concentrates on three prominent tasks: classification, detection, and reidentification, which are extensively utilized and encompass numerous attack scenarios. Given a DNNs-based model  $f:X\rightarrow Y$  with pre-trained weights  $\theta$ , for the sake of conciseness, we formulate the DNNs-based CV models as follows:

$$
\hat{y} = f_{\theta}(x), \qquad x \in X, y \in Y,\tag{1}
$$

where for any input data  $x \in X$ , the well-trained model  $f_{\theta}(\cdot)$  is able to predict a  $\hat{y}$  that closely approximates the corresponding ground truth  $y \in Y$ .

<span id="page-2-1"></span><span id="page-2-0"></span>Image /page/2/Figure/1 description: This is a flowchart illustrating the topic of "Physical Adversarial Attack on Computer Vision." The flowchart is organized into three main categories: "Adversarial Medium (Section 3)", "Evaluation Metrics (Section 4)", and "Victim Tasks (Section 5)". Under "Adversarial Medium", there are subcategories including "Sticker", "Patch", "Clothing", "Image", "Makeup", "Camera", "TC material", "Light", and "3D-printed artifact". The "Evaluation Metrics" category includes "Effectiveness", "Robustness", "Stealthiness", "Practicability", "Aesthetic", and "Economics". The "Victim Tasks" category is further divided into "Classification (Section 5.1)", "Detection (Section 5.2)", "Re-Identification (Section 5.3)", and "Others (Section 5.4)". Each of these subcategories has its own set of more specific tasks listed. For example, "Classification" includes "General Classification (Section 5.1.1)" and "Traffic Sign Classification (Section 5.1.2)". "Detection" includes "Vehicle Detection (Section 5.2.1)", "Person Detection (Section 5.2.2)", and "Traffic Sign Detection (Section 5.2.3)". "Re-Identification" includes "Face Recognition (Section 5.3.1)" and "Person Re-Identification (Section 5.3.2)". The "Others" category includes "Optical Flow Estimation", "Crowd Counting", "Object Tracking", "Image Captioning", "Steering Angle Prediction", "Segmentation", "Depth Estimation", and "X-ray Detection".

Fig. 2: A general overview of the scope in our survey.

## **2.2 Adversarial Attacks**

## *2.2.1 Problem Formulation*

Adversarial attacks involve introducing perturbations to input data, causing the model  $f_{\theta}(\cdot)$  to produce incorrect predictions y'. Note that the attacker's modification is limited to the input data. Exactly, the sample  $x$ , following the addition of perturbations  $\delta$ , is denoted as the adversarial sample  $x'$ . Mathematically, the joint representation is expressed as

$$
\begin{cases} x' = x + \delta \\ y' = f_{\theta}(x') \end{cases} \quad s.t. \quad y' \neq \hat{y}, \tag{2}
$$

where perturbations  $\delta$  are typically constrained by factors such as intensity, size, and the adversarial medium.

## *2.2.2 Distinguishing Adversarial Attacks from Backdoor Attacks and Poisoning Attacks.*

Apart from adversarial attacks, there are two other widely used attack types: backdoor (a.k.a. trojan) attacks [\[35\]](#page-16-10), [\[36\]](#page-16-11), [\[37\]](#page-16-12), [\[38\]](#page-16-13) and poisoning attacks [\[39\]](#page-16-14), [\[40\]](#page-16-15), [\[41\]](#page-16-16). While these three attack categories aim to mislead the model into incorrect predictions, their implementation methods differ fundamentally. We elucidate these distinctions by introducing the DNNs-based model lifecycle, depicted in Fig. [3,](#page-3-2) which comprises six phases: data collection and preparation, model selection, model training, model testing, model deployment, and model updating.

**Poisoning Attack.** DNN-based models require substantial data to achieve high performance. Developers often use publicly available datasets or scrape data from online sources, creating opportunities for poisoning attacks. These attacks involve injecting malicious or misleading samples into the training set, which can diminish learning efficiency and, in some cases, hinder convergence, ultimately disrupting the learning process [\[42\]](#page-16-17). Poisoning attacks typically occur during the data collection and preparation phase.

**Backdoor Attack.** Backdoor attacks involve injecting a hidden pattern or trigger into a DNNs-based model, causing incorrect behavior or erroneous predictions when the model encounters input data containing the trigger. Attackers achieve this by contaminating training data, altering model weights, or modifying the model architecture [\[43\]](#page-16-18). These attacks can occur at multiple stages in the model lifecycle, using methods such as code poisoning [\[44\]](#page-16-19), data poisoning [\[35\]](#page-16-10), and controlling the training process [\[45\]](#page-16-20).

Backdoor attacks should remain inconspicuous to users and should not disrupt the normal functioning of DNNs.

**Adversarial Attacks.** Compared to the two attack types mentioned above, adversarial attacks have the weakest underlying assumption: they solely modify input data, without any other alterations. This characteristic facilitates attackers in conducting practical applications more easily. Adversarial attacks occur during both the model testing and model deployment phases.

## **2.3 The Taxonomy of Adversarial Attacks**

Adversarial attacks can generally be categorized into three types based on adversarial knowledge:

**White-Box Attacks.** In a white-box attack, adversaries have full access to data and model details, including network architecture, parameters, and weights. They exploit this information to assess vulnerabilities and adapt their strategies, making this attack method relatively straightforward to execute.

**Black-Box Attacks.** The attackers lack access to the target model's structure and parameters and can only interact with the model to obtain predictions for specific samples.

**Gray-Box Attacks.** In a gray-box setting, the attack lies between black-box and white-box attacks, where the attacker has partial knowledge of the model, such as the model's output probability distribution or model structure.

Considering the adversarial goals, adversarial attacks can be divided into two categories:

**Targeted Attacks.** Targeted attacks aim to mislead DNNbased models to specified labels, e.g., misidentifying a cat image as that of a dog. The specified labels make targeted attacks more challenging to achieve with high success rates. **Untargeted Attacks.** The untargeted attacks mislead DNNsbased models to any wrong label. Designing an effective untargeted attack only requires making the model's predictions incorrect, without necessarily concerning what the incorrect predictions are.

## **2.4 Introduction to Physical Adversarial Attacks**

Physical adversarial attacks refer to where attackers deploy tangible perturbations in physical space to mislead DNNs-based models into providing inaccurate predictions. As shown in Fig. [1,](#page-1-0) we summarize the general process of physical adversarial attack as four steps: 1) *Adversarial*

<span id="page-3-3"></span><span id="page-3-2"></span>Image /page/3/Figure/1 description: The image depicts a timeline of machine learning model development, highlighting various attack vectors. The timeline progresses through stages: Data Collection and Preparation, Model Selection, Model Training, Model Testing, Model Deployment, and Model Updating. Above each stage, different types of attacks are indicated by colored rectangles and accompanied by a red arrow pointing downwards towards the stage, along with a devil emoji symbolizing a threat. Specifically, 'Poisoning Attacks' and 'Backdoor Attacks' are shown during Data Collection and Preparation. 'Backdoor Attacks' are also indicated during Model Selection and Model Training. 'Adversarial Attacks' are shown during Model Testing and Model Deployment. Finally, 'Backdoor Attacks' are indicated during Model Updating.

Fig. 3: An overview of the lifecycle in which the three types of attacks occur. Adversarial attacks occur only during the model deployment phase, without modifying the model and training data. Compared to backdoor attacks and poisoning attacks, adversarial attacks have weaker assumptions, focusing on the vulnerability of the model itself.

# *perturbation generation*, 2) *Adversarial medium manufacturing*, 3) *Threat image capturing*, and 4) *Attacking*.

Unlike digital adversarial attacks, which typically involve imperceptible perturbations to the human eye, as demonstrated in the One Pixel Attack [\[46\]](#page-16-21), physical adversarial attacks demand more intense perturbations. This level of intensity is crucial for the perturbation to be detectable by sensors in real-world scenarios. Meanwhile, physical adversarial attacks face additional challenges due to various physical constraints (e.g., spatial deformation) and environmental dynamics (e.g., lighting).

It is worth noting that backdoor attacks can also be executed in the physical world [\[47\]](#page-16-22), [\[48\]](#page-16-23). For instance, Qi *et al.* [\[38\]](#page-16-13) proposed a physically realizable backdoor attack algorithm against image classification tasks. However, it's important to clarify that this category is not within the scope of our discussion, as our primary focus is on physical adversarial attacks.

# <span id="page-3-0"></span>**3 ADVERSARIAL MEDIUMS**

We observe a commonality across all physical adversarial attack methods: the necessity of a physical entity to carry the specially designed perturbations. Therefore, we introduce the novel concept of adversarial medium for the first time to represent these entities. The *medium* is commonly used in physics. When a substance exists inside another substance, the latter is the medium of the former [\[49\]](#page-16-24). In most cases, properties such as form, density, and shape of the former would influence the properties of the latter. Analogously, for launching attacks in the physical space, the adversarial perturbation must have a carrier, i.e., the adversarial perturbation exists inside the carrier. Meanwhile, the carrier has an effect on the perturbation. Thus we define the carrier as the adversarial medium.

An adversarial medium is indispensable for physical adversarial attacks. In TABLEs [3,](#page-5-0) [4,](#page-6-0) [5,](#page-6-1) we have compiled a comprehensive list of all the physical adversarial attack methods examined in this paper, organized by medium and chronological order. Additionally, TABLE [2](#page-4-0) enumerates nine adversarial mediums, which, to our knowledge, encompass all current types. We provide specific definitions for each adversarial medium in this review. Here, we specifically clarify the similarities and differences between stickers and patches, as they can be easily confused. Specifically, they share commonalities, such as 1) the general use of materials like paper or plastic, 2) application to the host object, and 3) limited area coverage. The distinctions lie in 1) the method of application, with stickers emphasizing attachment to the host object, while patches can involve sticking, hanging, or

embedding, and 2) the relative size of the covered area, as stickers can cover the entire host object, whereas patches only occupy a small portion.

# <span id="page-3-1"></span>**4 EVALUATION METRIC**

Despite the widespread attention given to physical adversarial attacks, their performance has been subject to inconsistent and case-by-case evaluations. This phenomenon primarily stems from two factors:

- 1) *Challenges in Replication*: The creation of adversarial mediums, these physical entities, often incur high production costs and face various inherent factors during the manufacturing process. Factors such as the variability in printer models used for pattern printing and the difficulties in standardizing aspects like clothing style and color can influence the process. Furthermore, maintaining consistent real-world experimental conditions proves to be a daunting task. These challenges collectively hinder the replication of experiments conducted by others, making comparisons arduous.
- 2) *Varied Objectives*: Physical adversarial attack methods pursue diverse dimensions of enhancement. Some methods prioritize increasing attack effectiveness [\[13\]](#page-15-12), [\[21\]](#page-15-20), [\[85\]](#page-17-0), while others place emphasis on improving attack stealthiness [\[22\]](#page-15-21), [\[54\]](#page-16-25), [\[65\]](#page-16-26). A separate category strives to enhance robustness [\[90\]](#page-17-1), [\[93\]](#page-17-2), [\[102\]](#page-17-3). Consequently, a single metric struggles to impartially assess multiple methods.

These problems raise our question: *how does the actual performance of the physical adversarial attacks?* In this section, we take the first step in conducting a comprehensive assessment of physical adversarial attack methods, encompassing all the work in this field.

Since the diverse objectives across various works, we summarize six perspectives from existing literature, namely: *Effectiveness*, *Stealthiness*, *Robustness*, *Practicability*, *Aesthetics*, and *Economics*. Hingun *et al.* [\[130\]](#page-18-0) introduced a large-scale realistic adversarial patch benchmark to assess the effectiveness of attacks. Parallel to this work, Wang *et al.* [\[131\]](#page-18-1) evaluated the system-level effect of attack methods in autonomous driving. Li *et al.* [\[132\]](#page-18-2) proposed an evaluation of visual naturalness. These evaluation were conducted in settings with a *single perspective* and a *single task*. Towards a unified evaluation, we introduce the hexagonal indicator of Physical Adversarial Attack (*hiPAA*) to systematically quantify and compare attack methods across the aforementioned

#### IEEE TRANSACTIONS ON PATTERN ANALYSIS AND MACHINE INTELLIGENCE 5

<span id="page-4-0"></span>TABLE 2: Chronological overview of the typical physical adversarial attach methods using different adversarial mediums. We provide detailed definitions for each adversarial medium in this survey. Colors indicate the category of adversarial mediums used. ∗ denotes milestone papers.

| Chronological overview                                                                                                                                                                                                        | Adversarial medium              | Definition in this survey                                                                                                                                                                                                                                                                                                             |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| CAPatch<br>(Zhang et al.)<br><b>SLAttack</b><br>گ<br>(Li et al.)<br><b>HOTCOLD Block</b>                                                                                                                                      | 1 Sticker                       | Sticker is a piece of paper or plastic, with adversarial pertur-<br>bation patterns on one side, and needs to be affixed to a host<br>object. There are no size restrictions, and it can cover the entire<br>surface if necessary.                                                                                                    |
| (Wei et al.)<br>CMPatch<br>②<br>*<br>(Wei et al.)<br>AT3D<br>(Yang et al.)<br>T-SEA<br>$\circled{2}$<br>(Huang et al.)<br>AdvCaT<br>③                                                                                         | Patch                           | Patch is a material with limited area that carries perturbations,<br>which can be in the form of paper or cardboard. Its area is<br>smaller than the object to which it is applied.                                                                                                                                                   |
| (Hu et al.)<br>2023<br>TC-EGA<br>③<br>*<br>(Hu et al.)<br><b>SPAE</b><br>(Huang et al.)<br><b>AdvSticker</b><br>⊕<br>(Wei et al.)                                                                                             | 3 Clothing                      | Clothing as an adversarial medium refers to the attacker im-<br>printing or crafting perturbed textures onto garments, consti-<br>tuting a wearable design. Examples include t-shirts, capes, or<br>pants.                                                                                                                            |
| Adv-Shadow<br>(Zhong et al.)<br>2022<br>AdvLB<br>گ<br>(Duan et al.)<br><b>InvisPerturb</b><br>(Sayles et al.) AdvMakeup<br>$^\circledR$                                                                                       | $\circledcirc$<br>Image         | Image as an adversarial medium entails the attacker incorpo-<br>rating adversarial perturbations across the entire image. In<br>contrast, stickers and patches are applied exclusively to the<br>target object or a limited area.                                                                                                     |
| (Yin et al.) $_{\odot}$<br><b>AdvBulbs</b><br>*<br>(Zhu et al.)<br><b>AdvISP</b><br>$^{\circ}$<br>(Phan et al.) $_{\textcircled{\tiny{A}}}$<br><b>MetaAttack</b><br>(Feng et al.)<br><b>DAS</b><br>⊕<br>(Wang et al.)<br>2021 | $\circledS$<br>Light            | Light as the adversarial medium refers to the attacker using<br>light to construct adversarial perturbations in the physical space<br>and then <b>projecting</b> them onto the target object.                                                                                                                                         |
| <b>UPC</b><br>③<br>(Huang et al.)<br>AdvACO<br>(Liu et al.)<br>$\circled{1}$<br>Advhat<br>(Komkov et al.<br>AdvT-shirt<br>3<br>*<br>(Xu et al.)<br>AdvCloak<br>③                                                              | $^{\circledR}$<br>Camera        | Camera as an adversarial medium refers to attackers exploiting<br>the inherent imaging characteristics or parameter settings of<br>the camera to construct adversarial perturbations. This approach<br>involves introducing adversarial perturbations during the cap-<br>ture of physical scenes, thereby affecting downstream tasks. |
| (Wu et al.)<br>2020<br>AdvYOLO<br>$\circled{2}$<br>*<br>(Thys et al.)<br>D <sub>2</sub> P<br>⊕<br>(Jan et al.) $_{\odot}$<br>ACS<br>(Li et al.)<br>2019<br>$^\circledR$                                                       | ℗<br><b>TC</b> materials        | TC materials as an adversarial medium, involve attackers lever-<br>aging specialized physical materials to influence the imaging of<br>thermal infrared cameras. This primarily affects the distribution<br>of thermal radiation, enabling the construction of adversarial<br>textures.                                               |
| *<br>(Athalye et al.) $_{\textcircled{\scriptsize{4}}}$<br><b>DisAttack</b><br>(Song et al.)<br>RP <sub>2</sub><br>CAMOU (Eykholt et al.)<br>(Zhang et al.)<br>2018<br>Phy-FGSM<br>⊕<br>(Kurakin et al.)                      | $\circledR$<br><b>Makeup</b>    | Makeup as an adversarial medium refers to attackers using<br>cosmetic techniques to alter facial appearance by applying pre-<br>generated adversarial perturbations to the face.                                                                                                                                                      |
| AdvPatch<br>*<br>(Brown et al.)<br>2017<br>AdvEyeglass<br>$\circledcirc$<br>*<br>(Sharif et al.)                                                                                                                              | ◎<br><b>3D-printed artifact</b> | 3D-printed artifact as an adversarial medium refers to attack-<br>ers employing 3D printing technology to fabricate adversarial<br>objects with a 3D spatial structure. Such adversarial objects are<br>typically designed to execute real-world attacks from multiple<br>angles.                                                     |

six perspectives. We define the *hiPAA* as the weighted sum of six components:

$$
hiPAA = \lambda_1 \cdot Eff. + \lambda_2 \cdot Rub. + \lambda_3 \cdot Ste. + \lambda_4 \cdot Aes. + \lambda_5 \cdot Pra. + \lambda_6 \cdot Eco.,
$$
 (3)

where the weights of  $\lambda_1$  to  $\lambda_6$  are assigned based on the importance of each component. Specifically, we categorized the six components into three tiers based on their importance, assigning different weights to each tier. *Effectiveness* is allocated the highest tier weight of 0.3, followed by *Robustness* and *Stealthiness* with a weight of 0.2 each. The remaining three components are assigned the lowest tier weight of 0.1 each. Overall, the weights for the six dimensions are {0.3, 0.2, 0.2, 0.1, 0.1, 0.1}. Their sum equals 1.

## **4.1 Six Perspectives of** *hiPAA*

**Effectiveness.** Attack effectiveness is employed to evaluate the influence a method can exert on the victim model. To evaluate the effectiveness of physical adversarial attacks, our primary concern lies in quantifying the degree of performance degradation induced by these attack methods. Note that while the attack success rate (ASR) serves as a prevalent

<span id="page-5-1"></span><span id="page-5-0"></span>TABLE 3: Physical adversarial attack methods that use **stickers** and **patches** as adversarial mediums. We list them by the adversarial medium and time order.

| Adversarial |              | Description   | Method                 | Victim Task              | Venue                         | Year             |      |
|-------------|--------------|---------------|------------------------|--------------------------|-------------------------------|------------------|------|
| Medium      | Manufacture  | Instrument    | Attack Type            |                          |                               |                  |      |
| Sticker     | print, paste | eyeglasses    | impersonation, dodging | AdvEyeglass [14]         | <b>Face Recognition</b>       | <b>ACM CCS</b>   | 2016 |
|             | cover        | car body      | hiding                 | CAMOU <sup>[23]</sup>    | Vehicle Detection             | <b>ICLR</b>      | 2018 |
|             | display      | screen        | hiding                 | InvisibleCloak [50]      | Person Detection              | <b>UEMCON</b>    | 2018 |
|             | paste        | camera lens   | misclassification      | <b>ACS</b> [51]          | General Classification        | <b>PMLR</b>      | 2019 |
|             | print, paste | eyeglasses    | impersonation, dodging | AdvEyeglass+ [52]        | Face Recognition              | <b>TOPS</b>      | 2019 |
|             | print, affix | hat           | impersonation          | Advhat [24]              | Face Recognition              | <b>ICPR</b>      | 2020 |
|             | print, paste | eyeglasses    | misrecognition         | CLBAAttack [53]          | Face Recognition              | <b>BIOSIG</b>    | 2021 |
|             | affix        | car body      | hiding                 | DAS [54]                 | Vehicle Detection             | <b>CVPR</b>      | 2021 |
|             | affix        | road marking  | misdirection           | AdvMarkings [55]         | Lane Detection                | <b>USENIX</b>    | 2021 |
|             | full cover   | car body      | hiding                 | <b>FCA</b> [56]          | Vehicle Detection             | AAAI             | 2022 |
|             | full cover   | car body      | hiding                 | <b>DTA</b> [57]          | Vehicle Detection             | <b>CVPR</b>      | 2022 |
|             | imprint      | face mask     | dodging                | AdvMask [58]             | Face Recognition              | <b>ECML PKDD</b> | 2022 |
|             | print, paste | face          | impersonation          | AdvSticker [59]          | Face Recognition              | <b>TPAMI</b>     | 2022 |
|             | cover        | car body      | hiding                 | $CAC$ [60]               | Vehicle Detection             | <b>IJCAI</b>     | 2022 |
|             | print, paste | face          | impersonation          | DOPatch [61]             | Face Recognition              | arXiv            | 2023 |
|             | print, paste | car body      | false estimation       | $3D^2$ Fool [62]         | Depth Estimation              | <b>CVPR</b>      | 2024 |
| Patch       | print, put   | image patch   | misclassification      | AdvPatch <sup>[21]</sup> | <b>General Classification</b> | <b>NIPS</b>      | 2017 |
|             | print, paste | traffic sign  | misclassification      | $RP_2$ [63]              | Sign Classification           | <b>CVPR</b>      | 2018 |
|             | print, paste | traffic sign  | misdetection           | NestedAE [64]            | <b>Sign Detection</b>         | <b>CCS</b>       | 2019 |
|             | print, paste | traffic sign  | misclassification      | $PS-GAN [65]$            | Sign Classification           | AAAI             | 2019 |
|             | display      | screen        | lose track             | PAT [66]                 | <b>Object Tracking</b>        | <b>ICCV</b>      | 2019 |
|             | print        | image patch   | hiding                 | AdvYOLO [13]             | Person Detection              | <b>CVPRW</b>     | 2019 |
|             | print, paste | image patch   | mismatching            | AdvPattern [67]          | Person Re-ID                  | <b>ICCV</b>      | 2019 |
|             | imprint      | image patch   | false estimation       | FlowAttack [68]          | Flow Estimation               | <b>ICCV</b>      | 2019 |
|             | print, paste | image patch   | misclassification      | AdvACO [69]              | General Classification        | <b>ECCV</b>      | 2020 |
|             | paste        | camera lens   | misdetection           | TransPatch [70]          | Sign Detection                | <b>CVPR</b>      | 2021 |
|             | print, paste | image patch   | lose track             | <b>MTD</b> [71]          | Object Tracking               | AAAI             | 2021 |
|             | print, paste | face          | impersonation          | <b>TAP</b> [72]          | <b>Face Recognition</b>       | <b>CVPR</b>      | 2021 |
|             | print, paste | image patch   | misclassification      | $AdvACO+ [73]$           | General Classification        | TIP              | 2021 |
|             | display      | screen        | misdetection           | <b>AITP</b> [74]         | Sign Detection                | <b>ACM AISec</b> | 2022 |
|             | print, paste | image patch   | misclassification      | CPAttack [75]            | General Classification        | <b>NIPS</b>      | 2022 |
|             | print, paste | image patch   | misclassification      | TnTAttack [76]           | General Classification        | <b>TIFS</b>      | 2022 |
|             | print, paste | image patch   | false estimation       | OAP [77]                 | Depth Estimation              | <b>ECCV</b>      | 2022 |
|             | print        | image patch   | false segmentation     | <b>RWAEs</b> [78]        | Segmentation                  | <b>WACV</b>      | 2022 |
|             | print, paste | image patch   | false estimation       | PAP [79]                 | Crowd Counting                | <b>ACM CCS</b>   | 2022 |
|             | print, put   | image patch   | misclassification      | DAPatch <sup>[80]</sup>  | General Classification        | <b>ECCV</b>      | 2022 |
|             | print, paste | face          | impersonation          | <b>SOPP</b> [81]         | Face Recognition              | <b>TPAMI</b>     | 2022 |
|             | print, paste | car           | hiding                 | AerialAttack [82]        | Vehicle Detection             | <b>WACV</b>      | 2022 |
|             | paste        | aerogel patch | hiding                 | AdvInfrared [83]         | Person Detection              | <b>CVPR</b>      | 2023 |
|             | display      | screen        | hiding                 | $T-SEA [84]$             | Person Detection              | <b>CVPR</b>      | 2023 |
|             | paste        | aerogel patch | hiding                 | CMPatch [85]             | Person Detection              | <b>ICCV</b>      | 2023 |
|             | print        | image patch   | misstatement           | CAPatch <sup>[86]</sup>  | <b>Image Captioning</b>       | <b>USENIX</b>    | 2023 |
|             | paste        | aerogel patch | hiding                 | IAPatch [87]             | Person Detection              | <b>IJCV</b>      | 2023 |
|             | print, paste | image patch   | $mis\{det., cla.\}$    | TPatch <sup>[88]</sup>   | Sign Det. & Cla.              | <b>USENIX</b>    | 2023 |

metric, its applicability across all tasks is not universal. For instance, defining attack success in segmentation tasks can pose inherent challenges. Furthermore, to address variations across different tasks, we compute the percentage of performance degradation:

$$
Eff. = 1 - Acc'/Acc,
$$
 (4)

where  $Acc$  and  $Acc'$  represent the model's accuracy without and with attacks, respectively.

**Robustness.** Attack robustness is utilized to evaluate the method's stability in dynamic environments. To evaluate the robustness of physical adversarial attack, we consider three evaluation scenarios: 1) Cross-model: whether the attack method remains effective when the model changes, 2) Crossscenario: whether the attack method can consistently perform in various real-world scenarios, and 3) Transformationresistant: whether the attack method can withstand various real-world transformations, including rotation, camera-toobject distances, view angles, *etc*. As shown in the "Robustness" section of the Fig. [4,](#page-7-1) we present six candidate variables for assessing robustness. TABLE [9](#page-11-0) serves as an example demonstration of robustness evaluation.

**Stealthiness.** Stealthiness is used to measure the invisibility of adversarial textures or attack methods. In real-world attacks, stealthiness is crucial, as conspicuous attacks are easily detectable and can be thwarted by humans. Since stealthiness is targeted at human observers, we conduct a human study in which participants rate the images using a 5-point Absolute Category Rating (ACR) scale [\[132\]](#page-18-2).

**Aesthetics.** Aesthetics evaluates the social acceptability of the adversarial textures or attack methods. This metric is crucial, especially for wearable adversarial mediums [\[89\]](#page-17-19), [\[92\]](#page-17-20), [\[93\]](#page-17-2). Unusual patterns or appearances may lead users to reject them, whereas solutions that prioritize aesthetics are more likely to be accepted. Similar to stealthiness, we assess aesthetics through human ratings.

Li *et al.* [\[132\]](#page-18-2) introduced the Dual Prior Alignment (DPA) network to assess the visual naturalness of physical adversarial attacks. In this review, we adopt two perspectives, namely stealthiness and aesthetics, to provide a more

Image

| Adversarial | Description |                           |                   | Method             | Victim Task            | Venue     | Year |
|-------------|-------------|---------------------------|-------------------|--------------------|------------------------|-----------|------|
|             | Manufacture | Instrument                | Attack Type       |                    |                        |           |      |
| Clothing    | print       | T-shirt                   | hiding            | AdvT-shirt [89]    | Person Detection       | ECCV      | 2020 |
|             | print       | pants, sweaters, mask     | misdetection      | UPC [90]           | Person Detection       | CVPR      | 2020 |
|             | print       | sweatshirt                | hiding            | AdvCloak [91]      | Person Detection       | ECCV      | 2020 |
|             | print       | sweatshirt                | hiding            | NAP [92]           | Person Detection       | ICCV      | 2021 |
|             | print       | T-shirt                   | hiding            | LAP [22]           | Person Detection       | ACM MM    | 2021 |
|             | print       | dresses, T-shirts, skirts | hiding            | TC-EGA [93]        | Person Detection       | CVPR      | 2022 |
|             | crop        | aerogel material          | hiding            | InvisClothing [94] | Person Detection       | CVPR      | 2022 |
|             | rendering   | full coverage             | hiding            | DAC [95]           | Person Detection       | NN        | 2023 |
|             | print       | pants, sweatshirt         | hiding            | AdvCaT [96]        | Person Detection       | CVPR      | 2023 |
|             |             |                           |                   |                    |                        |           |      |
|             | print       | picture                   | misclassification | Phy-FGSM [97]      | General Classification | ICLR      | 2017 |
|             | print       | picture                   | misdetection      | ShapeShifter [98]  | Sign Detection         | ECML PKDD | 2018 |

print picture misdetection RP<sub>2</sub>+ [\[99\]](#page-17-27) Sign Detection USENIX 2018<br>print picture misclassification D2P [100] General Classification AAAI 2019 print picture misclassification D2P [\[100\]](#page-17-28) General Classification AAAI 2019<br>print picture misclassification ABBA [101] General Classification NIPS 2020

print picture false prediction PhysGAN [\[102\]](#page-17-3) Steering Angle Prediction CVPR 2020<br>print picture misclassification AdvCam [103] General Classification CVPR 2020 print picture misclassification AdvCam [\[103\]](#page-17-30) General Classification CVPR 2020<br>print picture hiding LPAttack [104] Sign Detection AAAI 2020 print picture hiding LPAttack [\[104\]](#page-17-31) Sign Detection AAAI

print picture misclassification MetaAttack [\[105\]](#page-17-32) General Classification ICCV 2021<br>print picture misclassification ViewFool [106] General Classification NIPS 2022

ABBA [\[101\]](#page-17-29) General Classification<br>PhysGAN [102] Steering Angle Prediction

General Classification

<span id="page-6-2"></span><span id="page-6-0"></span>TABLE 4: Physical adversarial attack methods that use **clothing**, **Images**, and **lights** as adversarial mediums. We list them

|       | print   | picture       | misclassification | Meta-GAN [107]   | General Classification | TIFS     | 2023 |
|-------|---------|---------------|-------------------|------------------|------------------------|----------|------|
|       | project | projector     | misclassification | PTAttack [108]   | General Classification | AAAI     | 2018 |
|       | project | projector     | misclassification | Poster [109]     | General Classification | IEEE S&P | 2019 |
|       | project | projector     | impersonation     | ALPA [110]       | Face Recognition       | CVPR     | 2020 |
|       | project | projector     | hiding            | SLAP [111]       | Sign Detection         | USENIX   | 2021 |
|       | project | projector     | misclassification | OPAD [112]       | General Classification | ICCV     | 2021 |
|       | project | projector     | misclassification | AdvLB [113]      | General Classification | CVPR     | 2021 |
| Light | project | shadow        | misclassification | Adv-Shadow [114] | Sign Classification    | CVPR     | 2022 |
|       | project | projector     | misclassification | SPAA [115]       | General Classification | IEEE VR  | 2022 |
|       | project | projector     | hiding            | AdvLight [116]   | Vehicle Detection      | ICASSP   | 2023 |
|       | project | laser pointer | hiding            | AdvLS [117]      | Sign Detection         | PMLR     | 2023 |
|       | project | projector     | impersonation     | SLAttack [118]   | Face Recognition       | CVPR     | 2023 |
|       | reflect | mirror        | misclassification | RFLA [119]       | Sign Classification    | ICCV     | 2023 |

<span id="page-6-1"></span>TABLE 5: Physical adversarial attack methods that use niche adversarial mediums. We list them by the adversarial medium and time order.

| Adversarial Medium   | Description                                               |                                                                                      |                                                                                              | Method                                                                        | Victim Task                                                                                                                    | Venue                                                                     | Year                                 |
|----------------------|-----------------------------------------------------------|--------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------|--------------------------------------|
|                      | Manufacture                                               | Instrument                                                                           | Attack Type                                                                                  |                                                                               |                                                                                                                                |                                                                           |                                      |
| Camera               | capture<br>capture                                        | rolling shutter effect<br>camera ISP                                                 | misclassification<br>misclassification                                                       | InvisPerturb [120]<br>AdvISP [121]                                            | <b>General Classification</b><br><b>General Classification</b>                                                                 | <b>CVPR</b><br><b>CVPR</b>                                                | 2021<br>2021                         |
| TC material          | heating<br>paste                                          | cardboard<br>warm/cool paste                                                         | hiding<br>hiding                                                                             | AdvBulbs [122]<br>HOTCOLD Block [123]                                         | Person Detection<br>Person Detection                                                                                           | AAAI<br>AAAI                                                              | 2021<br>2023                         |
| Makeup               | cosmetics                                                 | face                                                                                 | impersonation                                                                                | AdvMakeup [124]                                                               | Face Recognition                                                                                                               | <b>IJCAI</b>                                                              | 2021                                 |
| 3D-print<br>artifact | 3D-print<br>rendering<br>3D-print<br>3D-print<br>3D-print | tangible turtle<br>renderer<br>tangible mesh<br>adversarial metal<br>tangible object | misclassification<br>misclassification<br>impersonation<br>hiding<br>$mis{cla., det., cap.}$ | <b>EOT</b> [125]<br>3DAttack [126]<br>AT3D [127]<br>X-Adv [128]<br>TT3D [129] | <b>General Classification</b><br><b>General Classification</b><br>Face Recognition<br>X-ray Item Detection<br>Cla., Det., Cap. | <b>PMLR</b><br><b>CVPR</b><br><b>CVPR</b><br><b>USENIX</b><br><b>CVPR</b> | 2018<br>2019<br>2023<br>2023<br>2024 |

nuanced evaluation of visual naturalness. Stealthiness and aesthetics are closely tied to naturalness. The distinction between stealthiness and aesthetics lies in the former measuring the invisibility of adversarial textures or attack methods, while the latter evaluates the alignment with human aesthetics under the premise of visible adversarial textures.

**Practicability.** Practicability evaluates the practicality and feasibility of using the attack method in real-world scenarios. It considers factors such as the ease of implementation, availability of resources, and compatibility with existing systems. The higher the practicability, the more feasible and convenient it is to employ the adversarial medium in practical applications. Similar to stealthiness, we assess practicability through human ratings.

**Economics.** Economics evaluates the resource requirements and expenses associated with implementing and deploying the attack methods. Existing methods [\[122\]](#page-18-6), [\[123\]](#page-18-7) often provide descriptions of the costs associated with manufacturing adversarial mediums. We assess affordability based on these costs. For research that does not explicitly detail these expenses, we make estimations based on experience.

#### **4.2 Evaluation Details**

For all evaluation perspectives, TABLE [6](#page-7-2) lists the ranges of values, along with explanations of the score meanings for each dimension. To ensure fairness, we normalized the scores to fall between 0 and 1. In Fig. [4,](#page-7-1) we present the questions and labels utilized in the human evaluation. We <span id="page-7-3"></span><span id="page-7-2"></span>IEEE TRANSACTIONS ON PATTERN ANALYSIS AND MACHINE INTELLIGENCE 8

| Perspective of hiPAA | Range of values           | Normalization                               | Description                                                                                                                                                                                                                                   |
|----------------------|---------------------------|---------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Effectiveness        | [0, 1]                    | [0, 1]                                      | A higher value indicates a greater decline in model accuracy,<br>reflecting superior attack performance. Conversely, a lower value<br>suggests a lesser decrease in model accuracy, indicating poorer<br>attack performance.                  |
| Robustness           | $\{0, 1, 2, 3, 4, 5, 6\}$ | $\{0, 0.17, 0.33, 0.50, 0.67, 0.83, 1.00\}$ | A higher value indicates that the method takes into account a<br>greater number of factors, resulting in better robustness. Con-<br>versely, a lower value suggests that the method considers fewer<br>factors, leading to poorer robustness. |
| Stealthiness         | $\{1, 2, 3, 4, 5\}$       | $\{0.2, 0.4, 0.6, 0.8, 1.0\}$               | A higher value indicates better concealment, while a lower value<br>suggests poorer concealment.                                                                                                                                              |
| Aesthetics           | $\{1, 2, 3, 4, 5\}$       | $\{0.2, 0.4, 0.6, 0.8, 1.0\}$               | A higher value indicates greater aesthetic appeal, while a lower<br>value suggests less aesthetic appeal.                                                                                                                                     |
| Practicability       | $\{1, 2, 3, 4, 5\}$       | $\{0.2, 0.4, 0.6, 0.8, 1.0\}$               | A higher value indicates better practicality, while a lower value<br>suggests poorer practicality.                                                                                                                                            |
| Economics            | [0, 1]                    | [0, 1]                                      | A higher value indicates lower cost, while a lower value suggests                                                                                                                                                                             |

| TABLE 6: Description of the scores for each dimension of the hiPAA indicator. |  |  |
|-------------------------------------------------------------------------------|--|--|
|                                                                               |  |  |

<span id="page-7-1"></span>

| Robustness                                 | Stealthiness           | Aesthetics                                              | Practicability           |
|--------------------------------------------|------------------------|---------------------------------------------------------|--------------------------|
| Tick the variables that are<br>considered. | Rate the stealthiness. | Rate the aesthetics of the<br>adversarial perturbation. | Rate the practicability. |
| 1. Cross-model                             | 1. Very poor           | 1. Very poor                                            | 1. Very poor             |
| 2. Lighting                                | 2. Poor                | 2. Poor                                                 | 2. Poor                  |
| 3. Background                              | 3. Acceptable          | 3. Acceptable                                           | 3. Acceptable            |
| 4. Distance                                | 4. Good                | 4. Good                                                 | 4. Good                  |
| 5. Angle                                   | 5. Very good           | 5. Very good                                            | 5. Very good             |
| 6. Rotation                                |                        |                                                         |                          |

higher cost.

Fig. 4: Questions and labels of the four evaluation dimensions design.

recruited 30 participants, all of whom were over 18 years old, and provided consent to potentially encounter offensive content during the task. Before scoring, we provided them with prerequisite knowledge about physical adversarial attacks and evaluation details. Each participant is required to rate the stealthiness, aesthetics, and practicability of each method according to the provided questions and labels.

## **4.3 Discussion**

While the *hiPPA* offers a valuable means of aggregating multiple performance aspects, it has limitations concerning scenario-specific weighting and cross-task comparison. Firstly, the *hiPPA* was designed with a specific set of weights to balance various aspects. However, the importance of these aspects varies across different scenarios. For instance, effectiveness may be prioritized in laboratory settings, while stealthiness and economics might be critical in real-world applications. This variability means that the fixed weights may not always reflect the optimal balance for all use cases. Future work should explore adaptive weighting schemes that dynamically adjust based on scenario-specific requirements or user-defined priorities.

Secondly, the *hiPPA* score's effectiveness component is limited by the diversity of CV tasks, which use different datasets, evaluation protocols, and metrics. Our current approach involves extracting accuracy scores from each paper, leading to inconsistencies due to varying metrics and experimental setups. We mitigate this by normalizing the decrease in accuracy before and after the attack. However, this score should still be interpreted cautiously. Future research should focus on developing task-specific evaluation protocols to

facilitate more consistent and comparable assessments of physical adversarial attacks on CV tasks.

# <span id="page-7-0"></span>**5 VICTIM TASKS**

In this section, we methodically review physical adversarial attacks, categorizing them based on their respective task types. As shown in Fig. [2,](#page-2-0) to our knowledge, there are presently 15 sub-tasks associated with physical adversarial attacks. We organize these tasks into 4 main groups: classification, detection, re-identification, and others. The chronological overview in TABLE [2](#page-4-0) marks the milestone works in this field.

## **5.1 Attacks on Classification Tasks**

Considering the fundamental nature and significant downstream impact, substantial research is concentrated on attacking classification tasks, primarily focusing on general classification and traffic sign classification. Please refer to Fig. [5](#page-8-0) and Fig. [6](#page-9-0) for visual representations.

#### *5.1.1 General Classification*

Brown *et al.* [\[21\]](#page-15-20) proposed the adversarial patch, a milestone in physical adversarial attack methods. The attacker places a printed circular image patch next to the "banana", effectively deceiving the classifier into recognizing the banana as a "toaster" with high confidence (see Fig. [5\)](#page-8-0). Due to its simplicity in production, ease of deployment, and high attack potency, the adversarial patch immediately garnered widespread attention. Liu *et al.* [\[69\]](#page-16-44), [\[73\]](#page-17-4) developed class-agnostic universal adversarial patches with

<span id="page-8-2"></span><span id="page-8-0"></span>Image /page/8/Figure/1 description: This image illustrates the concept of an adversarial patch in machine learning. On the left, an "Adversarial Patch" is depicted as a colorful, abstract circular pattern. An arrow indicates that this patch is applied to a "Classifier Input." The top row shows a "Classifier Input" image of a banana, and its corresponding "Classifier Output" bar chart, which shows a high confidence score for "banana." The bottom row shows the same banana with the adversarial patch placed next to it as a "Classifier Input." The "Classifier Output" for this modified input shows a very high confidence score for "toaster," with negligible scores for other categories like "banana," "piggy\_bank," and "Spaghetti\_."

Fig. 5: Display of the physical adversarial attack in general classification tasks. Initially, the classifier accurately labels the image as "banana". However, when an adversarial patch is placed adjacent to the banana, the classifier misclassifies the image as "toaster", despite the continued presence of the banana. Adapted from AdvPatch [\[21\]](#page-15-20).

robust generalization capabilities for attacking classifiers in Automatic Check-out scenarios. Casper *et al.* [\[75\]](#page-17-6) designed the "copy/paste" attacks, employing adversarial patches to investigate the reliability and interpretability of models. To unleash the patch-based attack potential, Chen *et al.* [\[80\]](#page-17-11) designed a Deformable Adversarial Patch (DAPatch) to explore the impact of patch shapes. They simultaneously optimized shape and texture to enhance the attack performance. While these methods exhibit respectable attack performance, they are prone to visual abnormalities that render them conspicuously noticeable. To generate a naturallooking patch, Doan *et al.* [\[76\]](#page-17-7) proposed searching for naturalistic patches with adversarial effects within the latent space z of Generative Adversarial Networks (GANs) [\[133\]](#page-18-14).

In contrast to patch-based methods that only add perturbations in a limited region, another category of methods adds perturbations across the entire image. Phy-FGSM [\[97\]](#page-17-25) adds carefully designed perturbations to images from the ImageNet dataset [\[134\]](#page-18-15), prints them out, and then captures them using a cell phone camera. The results demonstrate that the captured images can still successfully attack Inception v3 [\[135\]](#page-18-16). With the generation of digital images and their application in the physical realm, a gap arises between these domains. D2P [\[100\]](#page-17-28) was the first to address this domain gap, utilizing conditional GANs [\[136\]](#page-18-17) to model the digitalto-physical transformation. Its objective is to mitigate the impact of this gap on attack performance. MetaAttack [\[105\]](#page-17-32) and Meta-GAN [\[107\]](#page-17-34) generate robust adversarial examples to maintain attacks in physical dynamics. These perturbations are subtle and constrained within a certain range  $\varepsilon$ , i.e.,  $\|\delta\| < \varepsilon$ , but they are visible to the human eye, unlike digital adversarial attacks  $[46]$  that are imperceptible. To make these perturbations appear natural, ABBA [\[101\]](#page-17-29) disguises the perturbations as motion blur, and AdvCam [\[103\]](#page-17-30) conceals them within natural styles. In addition to adding perturbations for attacks, ViewFool [\[106\]](#page-17-33) claims that changes in the viewpoint of the target object can also affect the classifier's predictions. It utilizes adversarial viewpoints to launch attacks and assess the classifier's robustness.

Inspired by One Pixel Attack [\[46\]](#page-16-21), Nichols *et al.* [\[108\]](#page-17-35) pioneered light-based adversarial attacks on classifiers. This

<span id="page-8-1"></span>TABLE 7: Comparison of the *hiPPA* metric among attacks for the general classification task. We highlight the minimum and maximum values in **blue** and red, respectively.

| Methods                   | Hexagonal Score |      |      |      |      |      | hiPAA                                |
|---------------------------|-----------------|------|------|------|------|------|--------------------------------------|
|                           | Eff.            | Rob. | Ste. | Aes. | Pra. | Eco. |                                      |
| AdvPatch [21] NIPS17      | 1.00            | 0.67 | 0.24 | 0.21 | 0.61 | 0.99 | 0.66                                 |
| Phy-FGSM [97] ICLR17      | 0.50            | 0.67 | 0.43 | 0.40 | 0.68 | 0.99 | 0.58                                 |
| PTAttack [108] AAAI18     | 0.78            | 0.50 | 0.64 | 0.66 | 0.83 | 0.95 | 0.71                                 |
| EOT [125] PMLR18          | 0.99            | 0.83 | 0.62 | 0.63 | 0.60 | 0.92 | 0.80                                 |
| 3DAttack [126]CVPR19      | 0.94            | 0.50 | 0.87 | 0.86 | 0.63 | 0.92 | 0.80                                 |
| Poster [109] S&P19        | 0.83            | 0.33 | 0.26 | 0.46 | 0.65 | 0.92 | 0.57                                 |
| ACS [51] PMLR19           | 0.49            | 0.33 | 0.88 | 0.87 | 0.85 | 0.99 | 0.66                                 |
| D2P [100] AAAI19          | 0.93            | 0.83 | 0.41 | 0.43 | 0.67 | 0.99 | 0.74                                 |
| AdvACO [69] ECCV20        | 0.44            | 0.83 | 0.81 | 0.86 | 0.69 | 0.99 | 0.71                                 |
| ABBA [101] NIPS20         | 0.85            | 0.50 | 0.69 | 0.64 | 0.61 | 0.99 | 0.72                                 |
| AdvCam [103] CVPR20       | 0.40            | 0.50 | 0.88 | 0.81 | 0.60 | 0.99 | 0.64                                 |
| MetaAttack [105] ICCV21   | 0.95            | 0.50 | 0.44 | 0.43 | 0.66 | 0.99 | 0.68                                 |
| AdvACO+ [73] TIP21        | 0.72            | 0.83 | 0.89 | 0.81 | 0.69 | 0.99 | <span style="color:red">0.81</span>  |
| InvisPerturb [120] CVPR21 | 0.94            | 0.67 | 0.81 | 0.43 | 0.45 | 0.00 | 0.67                                 |
| AdvISP [121] CVPR21       | 0.90            | 0.17 | 0.41 | 0.42 | 0.63 | 0.00 | <span style="color:blue">0.49</span> |
| OPAD [112] ICCV21         | 0.43            | 0.50 | 0.28 | 0.48 | 0.88 | 0.85 | 0.51                                 |
| AdvLB [113] CVPR21        | 0.88            | 0.67 | 0.85 | 0.41 | 0.81 | 0.92 | 0.78                                 |
| CPAttack [75] NIPS22      | 1.00            | 0.17 | 0.26 | 0.26 | 0.63 | 0.99 | 0.57                                 |
| TnTAttack [76] TIFS22     | 0.95            | 0.67 | 0.43 | 0.83 | 0.67 | 0.99 | 0.75                                 |
| DAPatch [80] ECCV22       | 0.44            | 0.67 | 0.26 | 0.27 | 0.64 | 0.99 | 0.51                                 |
| ViewFool [106] NIPS22     | 0.92            | 0.50 | 1.00 | 0.89 | 0.43 | 0.99 | <span style="color:red">0.81</span>  |
| SPAA [115] VR22           | 1.00            | 0.33 | 0.23 | 0.41 | 0.89 | 0.92 | 0.63                                 |
| Meta-GAN [107] TIFS23     | 0.95            | 0.67 | 0.44 | 0.43 | 0.61 | 0.99 | 0.71                                 |
| TT3D [129] CVPR24         | 0.83            | 0.83 | 0.75 | 0.57 | 0.70 | 0.86 | 0.78                                 |

method enables attackers to initiate an attack by directing a specially designed light onto the target object. However, this method has only been validated on low-resolution image datasets CIFAR-10. Man *et al.* [\[109\]](#page-17-36) manipulated the imaging of printed figures using the "flare effect" and the "blooming effect" to achieve attacks. OPAD [\[112\]](#page-17-39) modeled the light emitted by a physical projector to cast adversarial patterns onto target objects, resulting in robust attacks. Duan *et al.* [\[113\]](#page-17-40) proposed AdvLB, a method that enables the manipulation of the physical parameters of laser beams to execute adversarial attacks, which can be accomplished using handheld laser pointers in real-world scenarios. To enhance the stealthiness of light-based attacks, Huang *et al.* [\[115\]](#page-17-42) devised an optimization algorithm to balance adversarial loss and stealthiness loss.

As a key component of physical adversarial attack workflow, cameras are responsible for the transformation from the physical scene to a digital image. In contrast to altering objects in the physical scene, Li *et al.* [\[51\]](#page-16-28) innovatively proposed applying translucent stickers directly onto the camera lens to achieve the attack goal. These specially crafted stickers introduce perturbations into the captured images. Subsequently, Sayles *et al.* [\[120\]](#page-18-4) also focused on the camera, leveraging the Rolling Shutter Effect inherent in cameras, combined with adversarially illuminating a scene, to introduce an attack signal into the captured images. Phan *et al.* [\[121\]](#page-18-5) designed adversarial attacks that are effective only under specific camera ISP parameter settings. These methods provide an alternative approach to implementing physical adversarial attacks by modifying certain camera attributes or settings.

Apart from utilizing 2D images, methods involving 3D objects have also been explored. Athalye *et al.* [\[125\]](#page-18-9) fabricated the first 3D adversarial object—a turtle misclassified as a rifle from multiple viewpoints. Their proposed Expectation Over Transformation (EOT) technique enhances the robustness of adversarial examples, later widely applied in

<span id="page-9-4"></span><span id="page-9-0"></span>Image /page/9/Picture/1 description: A red octagonal stop sign with white lettering that spells out "STOP". There are several pieces of black and white tape obscuring parts of the letters. Specifically, there is a white square over the "S", a black rectangle over the "T", a white rectangle over the "O", and a black rectangle over the "P".

Image /page/9/Picture/2 description: A close-up shot of a circular speed limit sign with a red border and a white background. The number "20" is prominently displayed in black in the center of the sign. A small, colorful sticker is affixed to the number "20".

(a) A Stop sign with black/white blocks is classified as Speed Limit 45.

(b) A Speed Limit 20 sign with an adversarial patch is classified as Slippery Road.

(c) A Speed Limit 25 sign with shadows is classified as Speed Limit 35.

PEED

Fig. 6: Display of the physical adversarial attack in traffic sign classification tasks. Adapted from  $RP_2$  [\[63\]](#page-16-39) (a), PS-GAN [\[65\]](#page-16-26) (b), and Adv-Shadow [\[114\]](#page-17-41) (c).

physical adversarial attack domains. Zeng *et al.* [\[126\]](#page-18-10) perturbed 3D physical properties under a renderer to deceive 3D object classification and visual question-answering models. Subsequently, aiming to enhance cross-task transferability of 3D adversarial examples, Huang *et al.* [\[129\]](#page-18-13) introduced TT3D, enabling attacks across classification, detection, and captioning tasks.

The evaluation results of attacks on general classification tasks are summarized in TABLE [7.](#page-8-1) These results indicate that the performance of these attack methods has not continuously improved over the years. The lack of a comprehensive evaluation framework has contributed to this issue. For example, while CPAttack [\[75\]](#page-17-6) performs well in terms of effectiveness and economics, it neglects robustness.

## *5.1.2 Traffic Sign Classification*

The classification of traffic signs plays a pivotal role in aiding autonomous driving systems to comprehend scenes and make informed decisions [\[137\]](#page-18-18). Consequently, safety assessments in this domain have garnered considerable attention (see Fig. [6\)](#page-9-0). Eykholt *et al.* [\[63\]](#page-16-39) were the first to investigate the robustness of traffic sign classifiers and introduced Robust Physical Perturbations  $(RP_2)$ , a method that misguides these classifiers by affixing black and white blocks onto signs. Liu *et al.* [\[65\]](#page-16-26) proposed a perceptual-sensitive generative adversarial network (PS-GAN), which generates adversarial patches that visually resemble the scrawls typically found on signs in the real world, enhancing their stealthiness. Subsequently, building on potential real-world interferences, Zhong *et al.* [\[114\]](#page-17-41) subsequently introduced Adv-Shadow to evaluate the influence of shadows on traffic sign classifiers. Similarly, Wang *et al.* [\[119\]](#page-18-3) proposed using reflections from mirrors to launch attacks. Experimental results show that optimized shadows and reflections can effectively deceive target models. Fig. [7](#page-9-1) presents a comparison of  $RP_2$ , PS-GAN, and Adv-Shadow in terms of the six perspectives of the *hiPPA* metric. Results show that such attacks demonstrate excellence in effectiveness and economics, but they lack aesthetics and practicality. These observations provide potential directions for further improvement.

## **5.2 Attacks on Detection Tasks**

Physical adversarial attacks on detectors primarily focus on evading detection. As shown in Fig. [8,](#page-10-0) we display examples of attacking the vehicle, person, and sign detection tasks.

<span id="page-9-1"></span>Image /page/9/Figure/13 description: The image displays three radar charts, labeled (a) RP2 [63], (b) PS-GAN [65], and (c) Adv-Shadow [114]. Each chart has six axes representing different metrics: Ste., Rob., Eff., Eco., Pra., and Aes. Concentric circles indicate values from 0.00 to 1.00, with markings at 0.30, 0.60, 0.90, and 1.00. The charts show the performance of different methods across these metrics, with shaded polygons indicating the area covered by each method's scores.

Fig. 7: Comparison of six perspectives of hiPAA across three physical adversarial attack methods on traffic sign classification task.

#### *5.2.1 Vehicle Detection*

Physical adversarial attacks on vehicle detection focus on applying distinctive patterns to a vehicle's exterior to evade detection models.

Zhang *et al.* [\[23\]](#page-15-22) were the first to effectively camouflage a car in a 3D simulation environment. They employed the photorealistic Unreal Engine  $4^1$  $4^1$  to simulate the complex transformations induced by the physical environment comprehensively. This engine offers a comprehensive set of configuration parameters, including camouflage resolution, patterns, 3D vehicle models, camera settings, and environmental variables. Similar to this work, Wu *et al.* [\[138\]](#page-18-19) employed the open-source simulator CARLA [\[139\]](#page-18-20). They introduced an Enlarge-and-Repeat process and a discrete search method to craft physically adversarial textures. In addition, Duan *et al*. [\[60\]](#page-16-36) utilized the Unity<sup>[2](#page-9-3)</sup> and introduced a method called Coated Adversarial Camouflage (CAC).

The neural renderer is commonly used in 2D-to-3D transformation. One of the applications is to wrap the texture image to the 3D model, which then is rendered to the 2D image  $[140]$ ,  $[141]$ ,  $[142]$ . Thus utilizing the neural renderer to paint the adversarial stickers onto the vehicle surface is being pervasively used. Full-coverage Camouflage Attack (FCA) [\[56\]](#page-16-32) tries rendering the nonplanar texture over the full vehicle surface to overcome the partial occluded and long-distance issues. It bridges the gap between digital attacks and physical attacks via a differentiable neural renderer. Then, FCA introduces a transformation function to transfer the rendered camouflaged vehicle into a photo-realistic scenario. It outperforms other advanced attacks and achieves higher attack performance on both digital and physical attacks.

However, existing neural renderers cannot fully represent various real-world transformations due to a lack of control of scene parameters compared to legacy photo-realistic renderers. Motivated by the challenge, Suryanto *et al.* [\[57\]](#page-16-33) presented the Differentiable Transformation Attack (DTA), a framework for generating effective physical adversarial camouflage on 3D objects. It combines the advantages of a photorealistic rendering engine with the differentiability of a novel rendering technique, outperforming previous works in both effectiveness and transferability.

The evaluation results of attack methods on vehicle detection tasks are summarized in Fig. [9.](#page-10-1) From these results, the following insights can be drawn. First, all attack methods do not perform ideally in terms of stealthiness. This is

```
2. https://unity.com/
```

<span id="page-9-2"></span><sup>1.</sup> <https://www.unrealengine.com/>

<span id="page-10-2"></span><span id="page-10-0"></span>Image /page/10/Figure/1 description: The image displays three distinct object detection examples. The first, labeled (a) Vehicle Detection, shows a car with colorful graphics and the word "bird" highlighted on its side. The second, labeled (b) Person Detection, features two individuals standing in front of lockers, with one person outlined in magenta. The third, labeled (c) Sign Detection, presents two stop signs, one with a "stop sign: 98%" label above it, indicating successful detection.

Fig. 8: Display of the physical adversarial attack in vehicle, person, and traffic sign detection tasks. The detectors fail to detect the perturbed target. Adapted from UPC [\[90\]](#page-17-1) (a), AdvYOLO  $[13]$  (b), and ShapeShifter  $[98]$  (c).

due to the fact that vehicles are large entities with a single color, and any external alterations are likely to be noticeable. Second, no method excels in all six aspects; each has its own strengths. For instance, AdvLight [\[116\]](#page-17-43) demonstrates strong robustness but fares poorly in terms of economics.

## *5.2.2 Person Detection*

The objective of physical adversarial attacks on person detection is to conceal a person from detection models in the real world. Refer to TABLE [8](#page-11-1) for the *hiPPA* evaluation, and TABLE [9](#page-11-0) presents the robustness evaluation.

Yang *et al.* [\[50\]](#page-16-27) were the first to propose attacking person detectors in the real world. The adversarial patches they generated caused the accuracy of the Tiny YOLO detector [\[143\]](#page-18-24) to drop from 1.00 to 0.28. Thys *et al.* [\[13\]](#page-15-12) designed a small (40cm  $\times$  40cm) adversarial patch that, when held by an attacker, can deceive the YOLOv2 [\[144\]](#page-18-25). Given an input image, mainstream detectors predict the bounding boxes  $Vpos$ , the object probability  $Vobj$ , and the class score  $Vcls$ . During the training phase, they minimize  $Vobj$  and  $V_{cls}$  to cause the detector to ignore persons. Meanwhile, the INRIAPerson dataset [\[145\]](#page-18-26) provides person instances that facilitate the generation of effective adversarial patches. Subsequently, T-SEA [\[84\]](#page-17-15) achieves high attack transferability through a series of strategies that involve self-ensembling the input data, the victim model, and the adversarial patch.

Not being satisfied with attacking detectors with printed cardboard, Xu *et al.* [\[89\]](#page-17-19) crafted T-shirts with the generated adversarial patches. We termed their approach AdvT-shirt. Due to the non-rigid deformation of a T-shirt caused by pose changes of a moving person, AdvT-shirt employs a TPS-based transformer to ensure the attack's efficacy in the physical world. Parallel to this work, Wu *et al.* [\[91\]](#page-17-21) made a wearable invisibility cloak that, when placed over an object either digitally or physically, makes that object invisible to detectors. To fairly evaluate the effectiveness of different physical attacks, Huang *et al.* [\[90\]](#page-17-1) presented the first standardized dataset, AttackScenes, which simulates the real 3D world under controllable and reproducible settings to ensure that all experiments are conducted under fair comparisons for future research in this domain. In addition, they proposed the Universal Physical Camouflage (UPC) attack, which crafts adversarial patterns by jointly fooling the region proposal network, as well as misleading the classifier and the regressor to output errors.

Legitimate Adversarial Patches (LAP) [\[22\]](#page-15-21) and DAC [\[95\]](#page-17-23) aim to evade both human perception and detection models in real-world settings. LAP generates cartoon-style adver-

<span id="page-10-1"></span>Image /page/10/Figure/9 description: This image displays six bar charts, arranged in a 2x3 grid, each with the title 'Score' on the y-axis and various categories on the x-axis. The top row contains charts for 'Effectiveness', 'Robustness', and 'Stealthiness'. The 'Effectiveness' chart shows scores of 0.32, 0.56, 0.76, 0.64, 0.94, 0.74, and 0.83. The 'Robustness' chart shows scores of 0.83, 0.67, 0.83, 1, 1, 0.67, and 1. The 'Stealthiness' chart shows scores of 0.2, 0.4, 0.4, 0.4, 0.2, 0.2, and 0.2. The bottom row contains charts for 'Aesthetics', 'Practicability', and 'Economics'. The 'Aesthetics' chart shows scores of 0.2, 0.8, 0.6, 0.8, 0.2, 0.2, and 0.2, with corresponding x-axis labels CAMOU, DAS, FCA, DTA, CAC, AerialAttack, and AdvLight. The 'Practicability' chart shows scores of 0.4, 0.6, 0.6, 0.6, 0.4, 0.6, and 0.4, with the same x-axis labels. The 'Economics' chart shows scores of 0.99, 0.92, 0.92, 0.92, 0.99, and 0.99, with the same x-axis labels.

Fig. 9: Comparison of physical adversarial attack methods on vehicle detection tasks. These methods encompass CAMOU [\[23\]](#page-15-22), DAS [\[54\]](#page-16-25), FCA [\[56\]](#page-16-32), DTA [\[57\]](#page-16-33), CAC [\[60\]](#page-16-36), AerialAttack [\[82\]](#page-17-13), and AdvLight [\[116\]](#page-17-43).

sarial patches, while DAC integrates background styles into adversarial textures to balance effectiveness and stealthiness. Generative adversarial networks (GANs) have the ability to efficiently generate desired samples [\[146\]](#page-18-27). Consid-ering this, NAP [\[92\]](#page-17-20) crafts adversarial patches by leveraging the learned image manifold of BigGAN [\[147\]](#page-18-28) and Style-GAN [\[148\]](#page-18-29) pretrained on real-world images. Moreover, in this work, the MPII Human Pose dataset [\[149\]](#page-18-30) is introduced to provide the diversity of training data.

To address the segment-missing problem and support multi-angle attacks, Hu *et al.* [\[93\]](#page-17-2) introduced a novel generative method, the Toroidal-Cropping-based Expandable Generative Attack (TC-EGA). This approach generates adversarial textures characterized by repetitive structures. Unlike the prior studies, in the first stage, TC-EGA trains a fully convolutional network (FCN) [\[150\]](#page-18-31), [\[151\]](#page-18-32) as the generator to produce textures by sampling random latent variables as input. In the second stage, TC-EGA searches for the best local pattern of the latent variable with a cropping technique: Toroidal Cropping [\[152\]](#page-18-33). They have produced a variety of clothing items, such as T-shirts, skirts, and dresses, that have been printed with generated adversarial textures. These clothing items are effective for carrying out attacks when the wearer turns around or changes their posture. In the subsequent year, Hu *et al.* [\[96\]](#page-17-24) improved the attack's stealthiness by refining the clothing texture based on the TC-EGA method. Specifically, they introduced camouflage textures with a high naturalness score, resulting in the production of natural-looking adversarial clothing.

The security of DNNs-based models has received substantial attention in the context of visible light, but its exploration in thermal infrared imaging remains incomplete. Additionally, thermal infrared detection systems hold significant relevance in various security-related domains, such as autonomous driving [\[153\]](#page-18-34) and night surveillance [\[154\]](#page-18-35). Thus, Zhu *et al.* [\[122\]](#page-18-6) presented a groundbreaking method, named AdvBulbs, which is the first to enable physical attacks on thermal infrared person detectors. Their design is classified as a patch-based attack, with experiments conducted on the Teledyne FLIR ADAS Thermal dataset [\[155\]](#page-18-36). The following year, Zhu *et al.* [\[94\]](#page-17-22) designed infrared invisible clothing based on a new material aerogel. Compared with the AdvBulbs, infrared invisible clothing hid from infrared detectors from multiple angles. Since then, this field has gar<span id="page-11-2"></span>TABLE 8: Comparison of the *hiPPA* metric among attack methods for both the person detection task and traffic sign detection task. We highlight the minimum and maximum values using blue and red, respectively.

<span id="page-11-1"></span>

| Methods                      | Hexagonal Score |      |      |      |      |      | hiPAA |
|------------------------------|-----------------|------|------|------|------|------|-------|
|                              | Eff.            | Rob. | Ste. | Aes. | Pra. | Eco. |       |
| InvisibleCloak [50] UEMCON18 | 0.72            | 0.67 | 0.25 | 0.25 | 0.61 | 0.29 | 0.52  |
| AdvYOLO [13] CVPRW19         | 0.75            | 0.50 | 0.25 | 0.24 | 0.62 | 0.99 | 0.56  |
| AdvT-shirt [89] ECCV20       | 0.43            | 0.67 | 0.67 | 0.68 | 0.81 | 0.95 | 0.64  |
| UPC [90] CVPR20              | 0.93            | 0.67 | 0.26 | 0.24 | 0.81 | 0.91 | 0.66  |
| AdvCloak [91] ECCV20         | 0.50            | 0.83 | 0.61 | 0.60 | 0.81 | 0.95 | 0.67  |
| NAP[92] ICCV21               | 0.66            | 0.50 | 0.85 | 0.88 | 0.81 | 0.95 | 0.73  |
| LAP [22] ACM MM21            | 0.52            | 0.67 | 0.88 | 0.89 | 0.81 | 0.95 | 0.73  |
| AdvBulbs [122] AAAI21        | 0.65            | 0.83 | 0.23 | 0.43 | 0.68 | 0.99 | 0.62  |
| TC-EGA [93] CVPR22           | 0.65            | 1.00 | 0.41 | 0.28 | 0.81 | 0.95 | 0.68  |
| InvisClothing [94] CVPR22    | 0.88            | 1.00 | 0.27 | 0.27 | 0.27 | 0.00 | 0.57  |
| AdvInfrared [83] CVPR23      | 0.85            | 0.50 | 1.00 | 0.25 | 0.65 | 0.95 | 0.74  |
| T-SEA [84] CVPR23            | 0.99            | 0.67 | 0.26 | 0.24 | 0.69 | 0.29 | 0.60  |
| DAC [95] NN23                | 0.75            | 0.67 | 0.63 | 0.37 | 0.00 | 0.93 | 0.61  |
| AdvCaT [96] CVPR23           | 0.85            | 1.00 | 0.86 | 0.87 | 0.81 | 0.91 | 0.89  |
| HOTCOLD Block [123] AAAI23   | 0.57            | 0.67 | 1.00 | 0.27 | 0.65 | 0.99 | 0.70  |
| CMPatch [85] ICCV23          | 0.62            | 0.67 | 1.00 | 0.26 | 0.81 | 0.95 | 0.72  |
| IAPatch [87] IJCV23          | 0.88            | 0.50 | 1.00 | 0.26 | 0.81 | 0.95 | 0.77  |
| ShapeShifter [98] EP18       | 0.93            | 1.00 | 0.26 | 0.24 | 0.63 | 0.99 | 0.72  |
| RP2+ [99] USENIX18           | 0.85            | 0.67 | 0.81 | 0.41 | 0.67 | 0.99 | 0.76  |
| NestedAE [64] CCS19          | 0.92            | 0.67 | 0.26 | 0.25 | 0.66 | 0.99 | 0.65  |
| LPAttack [104] AAAI20        | 0.80            | 1.00 | 0.27 | 0.40 | 0.47 | 0.99 | 0.68  |
| TransPatch [70] CVPR21       | 0.42            | 0.33 | 0.88 | 0.84 | 0.85 | 0.99 | 0.64  |
| AdvMarkings [55] USENIX21    | 1.00            | 0.83 | 1.00 | 0.64 | 0.89 | 0.99 | 0.92  |
| SLAP [111]USENIX21           | 0.99            | 1.00 | 0.25 | 0.26 | 0.65 | 0.92 | 0.73  |
| AITP [74] AISec22            | 0.90            | 0.83 | 0.27 | 0.46 | 0.61 | 0.99 | 0.70  |
| AdvLS [117] PMLR23           | 0.66            | 0.50 | 0.86 | 0.42 | 0.88 | 0.92 | 0.69  |
| TPatch [88] USENIX23         | 1.00            | 0.83 | 0.77 | 0.83 | 0.51 | 0.57 | 0.81  |

nered significant attention. Wei *et al.* [\[83\]](#page-17-14) also used aerogel material, with the difference that they designed irregularly shaped patches for the attack. Parallel to this work, Wei *et al.* [\[123\]](#page-18-7) ingeniously employed anti-fever stickers and heating pads to create adversarial patches. Instead of optimizing the texture and structural characteristics of the patches, their emphasis lies in studying the impact of patch size, shape, and placement on the attacks. Wei *et al.* [\[85\]](#page-17-0) introduced the concept of cross-modal physical adversarial attacks, which remain effective under both thermal infrared and visible light imaging modalities.

## *5.2.3 Traffic Sign Detection*

Traffic sign detection involves identifying road signs (e.g., stop signs and lane lines) in driving scenarios, essential for autonomous driving. Due to its critical role in safety and decision-making, adversarial attacks on traffic sign detection have evolved to improve the resilience of detection algorithms. The comparative results of these methods are shown at the bottom of TABLE [8.](#page-11-1)

The mainstream detectors prune the region proposals by using heuristics like non-maximum suppression (NMS) [\[156\]](#page-18-37), [\[157\]](#page-18-38). The pruning operations are usually nondifferentiable. However, generating adversarial perturbations pervasively requires calculating a backward gradient end to end. The non-differentiable operations make it hard to optimize the objective loss. To tackle this problem, Chen *et al.* [\[98\]](#page-17-26) carefully studied the Faster R-CNN object detector [\[156\]](#page-18-37) and successfully performed optimization-based attacks using gradient descent and backpropagation. Concretely, they ran the forward pass of the region proposal network and fixed the pruned region proposals as fixed constants to the second stage classification in each iteration. The  $RP_2$  algorithm of [\[63\]](#page-16-39) only focuses on attacking the

TABLE 9: Evaluation of the robustness dimension of the *hiPPA* metric on attacking person detection task.

<span id="page-11-0"></span>

| Cross-model | Cross-scenario |      | Transformation |      |      | Method              | Rob. |
|-------------|----------------|------|----------------|------|------|---------------------|------|
|             | Lig.           | Bac. | Dis.           | Ang. | Rot. |                     |      |
| ✗           | ✓              | ✓    | ✓              | ✗    | ✓    | InvisibleCloak [50] | 0.67 |
| ✗           | ✓              | ✓    | ✗              | ✗    | ✓    | AdvYOLO [13]        | 0.50 |
| ✗           | ✓              | ✓    | ✓              | ✗    | ✓    | AdvT-shirt [89]     | 0.67 |
| ✓           | ✓              | ✓    | ✓              | ✗    | ✗    | UPC [90]            | 0.67 |
| ✓           | ✓              | ✓    | ✓              | ✗    | ✓    | AdvCloak[91]        | 0.83 |
| ✓           | ✗              | ✓    | ✓              | ✗    | ✓    | NAP [92]            | 0.50 |
| ✓           | ✓              | ✓    | ✓              | ✗    | ✓    | LAP [22]            | 0.67 |
| ✓           | ✓              | ✓    | ✓              | ✗    | ✓    | AdvBulbs [122]      | 0.83 |
| ✓           | ✓              | ✓    | ✓              | ✓    | ✓    | TC-EGA [93]         | 1.00 |
| ✓           | ✓              | ✓    | ✓              | ✓    | ✓    | InvisClothing [94]  | 1.00 |
| ✓           | ✓              | ✓    | ✗              | ✗    | ✓    | AdvInfrared [83]    | 0.50 |
| ✓           | ✓              | ✓    | ✓              | ✗    | ✓    | T-SEA [84]          | 0.67 |
| ✓           | ✓              | ✓    | ✓              | ✓    | ✓    | DAC [95]            | 0.67 |
| ✓           | ✓              | ✓    | ✓              | ✓    | ✓    | AdvCaT [96]         | 1.00 |
| ✓           | ✓              | ✓    | ✗              | ✗    | ✓    | HOTCOLD Block [123] | 0.67 |
| ✓           | ✓              | ✓    | ✗              | ✗    | ✓    | CMPatch[85]         | 0.67 |

• *Lig.*, *Bac.*, *Dis.*, *Ang.*, and *Rot.* represent lighting, background, camera-to-object distances, view angles, and rotation respectively.

traffic sign classification task. Following this line, Song *et*  $al.$  [\[99\]](#page-17-27) extended the  $RP_2$  to provide proof-of-concept attacks for object detection networks. They experimented with the YOLOv2 [\[144\]](#page-18-25) and achieved 85.6% ASR in an indoor environment and 72.5% ASR in an outdoor environment.

Lane detection is important for autonomous driving because it supports steering decisions. Jing *et al.* [\[55\]](#page-16-31) pioneered the investigation into the security of lane detection modules in real vehicles, focusing on the Tesla Model S. We term their method "Adversarial Markings" as it utilizes small markings on the road surface to mislead the vehicle's visual system. Extensive experiments show that Tesla Autopilot is vulnerable to Adversarial Markings in the physical world and follows the fake lane into oncoming traffic.

Zolfi *et al.* [\[70\]](#page-16-45) designed a universal perturbation, called TransPatch, to fool the detector for all instances of a specific object class while maintaining the detection of other objects. TransPatch is a type of colored translucent sticker, which performs attacks by attaching this special sticker to the camera lens, resulting in disturbing the camera's imaging. In addition, Giulio *et al.* [\[111\]](#page-17-38) introduced SLAP, a light-based technique enabling physical attacks in self-driving scenarios. Using a projector, SLAP projects a specific pattern onto a Stop Sign, causing YOLOv3 [\[30\]](#page-16-5) and Mask-RCNN [\[28\]](#page-16-3) to misdetect the targeted object in moving vehicle environments. However, such methods may raise suspicion among human drivers. To address this, Zhu *et al.* [\[88\]](#page-17-18) proposed TPatch, innovatively designing a triggered physical adversarial patch. TPatch exhibits malicious behavior only when triggered by acoustic signals; otherwise, it behaves benignly. This approach offers a novel perspective on enhancing the stealthiness of adversarial patches.

#### **5.3 Attacks on Re-Identification Tasks**

In this section, we review physical adversarial attacks on reidentification (Re-ID) tasks (see Fig. 10). TABLE [10](#page-12-0) presents the comparative results of these methods based on the *hiPAA* metric.

#### *5.3.1 Face Recognition*

Face Recognition Systems (FRS) are widely used in surveillance and access control [\[158\]](#page-18-39), [\[159\]](#page-18-40). It is valuable to ex-

<span id="page-12-2"></span>Image /page/12/Figure/1 description: The image depicts a two-stage process of impersonation. The first stage shows a man wearing glasses with a colorful, abstract pattern. An arrow labeled 'impersonation' points from this image to a close-up of a woman's face. The second stage shows a person wearing a t-shirt with a similar colorful, abstract pattern on the chest. A magnified view of the pattern on the t-shirt is shown to the left. An arrow labeled 'impersonation' points from this image to a full-body shot of a woman walking away from the camera.

(a) Impersonation attack in face recognition task.

<span id="page-12-1"></span>(b) Impersonation attack in person re-identification task.

Fig. 10: Display of the physical adversarial attack in re-identification (Re-ID) tasks. Adapted from AdvEyeglass [\[14\]](#page-15-13) (a) and AdvPattern [\[67\]](#page-16-42) (b).

TABLE 10: Comparison of the *hiPPA* metric among attack methods for both the face recognition task and person Re-ID task. We highlight the minimum and maximum values using blue and red, respectively.

<span id="page-12-0"></span>

| Methods                  | Hexagonal Score |      |      |      |      |      | hiPAA |
|--------------------------|-----------------|------|------|------|------|------|-------|
|                          | Eff.            | Rob. | Ste. | Aes. | Pra. | Eco. |       |
| AdvEyeglass [14] CCS16   | 1.00            | 0.33 | 0.65 | 0.67 | 0.89 | 0.99 | 0.75  |
| AdvEyeglass+ [52] TOPS19 | 1.00            | 0.67 | 0.65 | 0.67 | 1.00 | 0.99 | 0.83  |
| Advhat [24] ICRP20       | 1.00            | 0.83 | 0.25 | 0.47 | 0.69 | 0.99 | 0.73  |
| ALPA [110] CVPR20        | 1.00            | 0.50 | 0.26 | 0.25 | 0.66 | 0.92 | 0.64  |
| CLBAAttack [53] BIOSIG21 | 0.95            | 0.33 | 0.29 | 0.25 | 0.63 | 0.99 | 0.60  |
| AdvMask [58] EP21        | 0.96            | 1.00 | 0.23 | 0.20 | 0.86 | 0.98 | 0.74  |
| AdvMakeup [124] IJCAI21  | 0.40            | 0.33 | 0.88 | 0.68 | 0.65 | 0.98 | 0.59  |
| <b>TAP</b> [72] CVPR21   | 1.00            | 0.17 | 0.43 | 0.63 | 0.61 | 0.99 | 0.64  |
| AdvSticker [59] TPAMI22  | 0.98            | 0.67 | 0.69 | 0.88 | 0.65 | 0.99 | 0.82  |
| SOPP[81] TPAMI22         | 0.96            | 0.83 | 0.42 | 0.63 | 0.65 | 0.99 | 0.76  |
| SLAttack [118] CVPR23    | 0.65            | 0.67 | 0.43 | 0.46 | 0.65 | 0.29 | 0.56  |
| AT3D [127] CVPR23        | 0.48            | 0.67 | 0.27 | 0.47 | 0.65 | 0.99 | 0.54  |
| DOPatch [61] arXiv23     | 0.88            | 0.83 | 0.42 | 0.63 | 0.65 | 0.99 | 0.74  |
| AdvPattern [67] ICCV19   | 0.69            | 0.50 | 0.26 | 0.25 | 0.69 | 0.99 | 0.55  |

plore the potential risks of FRS. Sharif *et al.* [\[14\]](#page-15-13) developed a groundbreaking method to attack the face recognition algorithm by printing a pair of eyeglass frames. The person who wears the adversarial eyeglasses is able to evade being recognized or impersonate another individual. The non-printability score (NPS) is defined to ensure the perturbations are printable, and it is widely used as a loss function during adversarial perturbation optimization. They demonstrate how an attacker that is unaware of the system's internals is able to achieve inconspicuous impersonation under a commercial FRS [\[160\]](#page-18-41).

Pautov *et al.* [\[161\]](#page-18-42) explored physical attacks on Arc-Face [\[162\]](#page-18-43) using adversarial patches. They designed a cosine similarity loss to minimize the similarity between the patched photo and the ground truth. The generated gray patch is easily printable. They tested the patch in three forms: eyeglasses, and stickers on the nose and forehead. Numerical experiments demonstrated effective real-world attacks on ArcFace. Light-based attacks have shown feasibility in classification tasks [\[112\]](#page-17-39). For FRS, Nguyen *et al.* [\[110\]](#page-17-37) designed a real-time adversarial light projection attack using an off-the-shelf camera-projector setup, targeting state-ofthe-art FRS such as FaceNet [\[163\]](#page-18-44) and SphereFace [\[164\]](#page-18-45).

Advhat [\[24\]](#page-15-23) implements an easily reproducible physical adversarial attack on the state-of-the-art public Face ID system [\[162\]](#page-18-43), [\[165\]](#page-18-46). In the digital space, Advhat uses Spatial Transformer Layer (STL) [\[166\]](#page-18-47) to project the obtained sticker on the image of the face. In the physical space, Advhat launches attacks by wearing a hat with a special sticker on the forehead area, which significantly reduces the similarity to the ground truth class.

Yin *et al.* [\[124\]](#page-18-8) proposed AdvMakeup, a unified adversarial face generation method that addresses a common and practical scenario: applying makeup to eye regions to deceive FRS while maintaining a visually inconspicuous appearance, resembling natural makeup. Concretely, AdvMakeup first introduces a makeup generation module, which can add natural eye shadow over the orbital region. Then, a task-driven fine-grained meta-learning adversarial attack strategy guarantees the attacking effectiveness of the generated makeup. Experimental results show that the AdvMakeup' attack effectiveness is substantially higher than Advhat [\[24\]](#page-15-23) and AdvEyeglass [\[14\]](#page-15-13).

#### *5.3.2 Person Re-Identification*

Person Re-ID is the task of identifying and tracking an individual of interest across multiple non-overlapping cameras [\[167\]](#page-18-48). This task plays an important role in surveillance and security applications. Wang *et al.* [\[67\]](#page-16-42) were the first and only ones to propose a physical attack on the Re-ID model, known as AdvPattern. They accomplished evasion and impersonation attacks by formulating distinct optimization objectives. As shown in Fig. [10\(b\),](#page-12-1) AdvPattern employs adversarial patches featuring specially crafted patterns as the adversarial medium, which are affixed to a person's chest. The method degrades the rank-1 accuracy of person Re-ID models from 87.9% to 27.1% and under impersonation attack. This easily implementable approach exposes the vulnerability of the DNNs-based Re-ID system.

#### **5.4 Attacks on Other Tasks**

In addition to the three aforementioned mainstream tasks, physical adversarial attacks extend to eight niche tasks, encompassing optical flow estimation [\[68\]](#page-16-43), steering angle prediction [\[102\]](#page-17-3), crowd counting [\[79\]](#page-17-10), segmentation [\[78\]](#page-17-9), object tracking [\[66\]](#page-16-41), [\[71\]](#page-16-46), monocular depth estimation [\[77\]](#page-17-8), image captioning  $[86]$ , and X-ray detection  $[128]$ . Table [11](#page-13-1) presents the comparative results based on the *hiPAA* metric. **Optical Flow Estimation** (OFE) aims to measure the pixel 2D motion of an image sequence [\[168\]](#page-18-49). As shown in Fig. [11\(a\),](#page-13-2) Ranjan *et al.* [\[68\]](#page-16-43) proposed FlowAttack to perturb the OFE models. FlowAttack utilizes the gradients from pretrained optical flow networks to update adversarial patches. Experimental results show that FlowAttack can cause large errors for encoder-decoder networks but not strongly affect spatial pyramid networks. This phenomenon demonstrates the correlation between network structure and vulnerability. **Crowd Counting** aims to estimate the number of individuals within images or videos, with significant applications

<span id="page-13-6"></span><span id="page-13-2"></span>Image /page/13/Picture/1 description: The image displays four distinct visual panels. The top-left panel shows a woman with long, curly hair standing in a room with shelves containing various items, next to a colorful, abstract representation of motion or data. The bottom-left panel shows the same scene but with the colorful representation replaced by a blank white space. The middle-left panel shows a young person wearing a mask and holding a piece of paper with a QR code pattern, with the text "Ground Truth: 4" below. The middle-right panel shows the same person, but now with a heat map overlay on the paper and the text "Predicted Count: 77". The right side of the image features two stacked panels. The top panel shows the rear view of a dark SUV driving on a road, with two smaller images of the same scene to its left and right. The middle panel shows a depth map of the SUV, with warmer colors indicating closer objects. The bottom panel displays a 3D point cloud representation of the scene, colored by depth, with a wireframe box superimposed on it. The far right panel shows two people holding a large, patterned sheet of paper in an outdoor setting, with a colorful semantic segmentation map below it, depicting roads, buildings, and vegetation.

<span id="page-13-3"></span>

<span id="page-13-5"></span><span id="page-13-4"></span>

(a) Optical flow estimation (b) Crowd counting (c) Monocular depth estimation (d) Semantic segmentation

Fig. 11: Display of the physical adversarial attack in other tasks. Adapted from FlowAttack [\[68\]](#page-16-43) (a), PAP [\[79\]](#page-17-10) (b), OAP [\[77\]](#page-17-8) (c), and RWAEs [\[78\]](#page-17-9) (d).

in public safety and traffic management [\[169\]](#page-18-50). Liu *et al.* [\[79\]](#page-17-10) proposed a Perceptual Adversarial Patch (PAP) for attacking crowd-counting systems in the real world. PAP generates an adversarial patch by maximizing the model loss, leading the target victim model to overestimate the count by up to 100 on 80% of the samples (see Fig. [11\(b\)\)](#page-13-3).

**Monocular Depth Estimation** (MDE) aims to estimate the distance between the camera and a target object, which is crucial for autonomous driving [\[170\]](#page-18-51). Recently, Cheng *et al.* [\[77\]](#page-17-8) introduced an attack named OAP against MDE models (see Fig. $11(c)$ ). OAP employs a rectangular patch region optimization method to find the optimal patch-pasting region, resulting in over 6 meters mean depth estimation error and 93% ASR in downstream tasks. Despite its effectiveness, OAP relies on 2D image patches and cannot achieve multi-viewpoint attacks. To address this limitation, Zheng *et al.*  $[62]$  proposed  $3D^2$  Fool, which integrates UV mapping into adversarial texture optimization, creating robust 3D camouflage textures capable of making the car vanish.

**Semantic Segmentation** aims to classify each pixel into predefined categories without distinguishing between individual object instances [\[28\]](#page-16-3). Nesti *et al.* [\[78\]](#page-17-9) crafted adversarial patches to perturb the semantic segmentation models. As shown in Fig.  $11(d)$ , they created a large adversarial patch, measuring  $1m \times 2m$ , which disrupts the predictions of segmentation models in the physical world. The adversarial patch is optimized using pixel-wise cross-entropy loss on the pre-trained ICNet [\[171\]](#page-19-0). Meanwhile, they built abundant and diverse scenes by the CARLA Simulator [\[139\]](#page-18-20) for scenespecific attacks. Experimental results show that their attack method can reduce the model's accuracy in the digital space, but in the real world, the attack is greatly downgraded.

**Steering Angle Prediction** assists autonomous driving systems in making informed decisions [\[172\]](#page-19-1), [\[173\]](#page-19-2), [\[174\]](#page-19-3). To evaluate the safety and robustness of this task, Kong *et al.* [\[102\]](#page-17-3) introduced PhysGAN, a method that generates physically resilient adversarial examples to deceive autonomous steering systems. As shown in Fig. [12\(a\),](#page-14-0) by utilizing the discriminator within the GAN framework to assess the visual disparities between adversarial roadside signs and their original counterparts, PhysGAN can generate realistic adversarial examples. Meanwhile, it can maintain attack effectiveness continuously across all frames throughout the entire trajectory.

**Object Tracking** aims to detect moving objects and track them from frame to frame [\[175\]](#page-19-4). Wiyatno *et al.* [\[66\]](#page-16-41) proposed the first physical adversarial attack on this task. Specifically, they perform optimization to create a distinctive pattern,

TABLE 11: Comparison of the hiPPA metric among attack methods for eight niche tasks.

<span id="page-13-1"></span>

| Methods                | Hexagonal Score |      |      |      |      |      | hiPAA |
|------------------------|-----------------|------|------|------|------|------|-------|
|                        | Eff.            | Rob. | Ste. | Aes. | Pra. | Eco. |       |
| PAT [66] ICCV19        | 0.60            | 0.67 | 0.27 | 0.27 | 0.87 | 0.29 | 0.51  |
| MTD [71] AAAI21        | 0.74            | 0.67 | 0.27 | 0.40 | 0.69 | 0.99 | 0.62  |
| OAP[77] ECCV22         | 0.94            | 1.00 | 0.87 | 0.61 | 0.65 | 0.98 | 0.88  |
| $3D2$ Fool [62] CVPR24 | 0.95            | 1.00 | 0.59 | 0.73 | 0.71 | 0.79 | 0.83  |
| FlowAttack [68] ICCV19 | 1.00            | 0.67 | 0.25 | 0.25 | 0.64 | 0.99 | 0.67  |
| PhysGAN [102] CVPR20   | 1.00            | 0.67 | 0.65 | 0.89 | 0.65 | 0.95 | 0.81  |
| RWAEs[78] WACV22       | 1.00            | 0.33 | 0.20 | 0.46 | 0.25 | 0.95 | 0.57  |
| PAP [79] CCS22         | 1.00            | 0.33 | 0.64 | 0.47 | 0.61 | 0.99 | 0.70  |
| CAPatch [86] USENIX23  | 1.00            | 1.00 | 0.20 | 0.20 | 0.63 | 0.99 | 0.72  |
| X-Adv [128] USENIX23   | 0.74            | 0.33 | 0.93 | 0.86 | 0.69 | 0.93 | 0.72  |

which is then presented on a large monitor as a background. When a person moves in front of the monitor, the tracker tends to prioritize locking onto the background and disregards the person. Subsequently, Ding *et al.* [\[71\]](#page-16-46) proposed a patch-based attack method to launch universal physical attacks on single object tracking. As shown in Fig. [12\(b\),](#page-14-1) in the presence of the patch, the tracker neglects the originally tracked object. These explorations raise security concerns for real-world visual tracking.

**Image Captioning** focuses on generating a description of an image, which requires recognizing the important objects, their attributes, and their relationships in an image [\[176\]](#page-19-5). Inspired by patch-based attacks, Zhang *et al.* [\[86\]](#page-17-16) designed CAPatch, a method capable of inducing errors in final captions within real-world scenarios (see Fig.  $12(c)$ ). CAPatch deceives image captioning systems, causing them to produce a specified caption or conceal certain keywords. In contrast to existing attack methods, this study represents the initial endeavor to employ an adversarial patch against multi-modal artificial intelligence systems.

**X-ray Detection** is widely used in security screening to identify prohibited items in safety-critical scenarios [\[177\]](#page-19-6). Liu *et al.* [\[128\]](#page-18-12) pioneered the exploration of physical adversarial attacks in X-ray imaging. They introduced X-Adv, a technique for creating physically plausible adversarial metal objects. When positioned near the targeted prohibited item, these objects enable the item to evade detection. X-Adv exposes vulnerabilities in X-ray detection systems, emphasizing the necessity for enhanced robustness.

## <span id="page-13-0"></span>**6 DISCUSSION**

During the development of this paper, we have observed the diversity and broad scope of physical adversarial attacks. Despite the growth in published works in recent years, there

<span id="page-14-5"></span><span id="page-14-0"></span>Image /page/14/Picture/1 description: The image displays three distinct panels. The leftmost panel shows a road with a billboard on the right side, under a clear blue sky. The middle panel contains two smaller images, one above the other, both showing a person walking on a path with trees and buildings in the background. Both images have a green bounding box around the person and a frame number indicated. The bottom image shows the person further down the path. The rightmost panel features a close-up of a horse grazing in a field, with a small inset image in the top right corner. Below the horse image, there is text that reads "A horse is eating grass in a field." followed by an arrow and then "A bird is flying over a body of water."

(a) Steering Angle Prediction (b) Object Tracking (c) Image Captioning

<span id="page-14-1"></span>Fig. 12: Display of the physical adversarial attack across three CV tasks: steering angle prediction, object tracking, and image captioning. Adapted from PhysGAN [\[102\]](#page-17-3) (a), MTD [\[71\]](#page-16-46) (b), and CAPatch [\[86\]](#page-17-16) (c).

are still gaps to be explored. Here, we discuss the current challenges and opportunities in this field.

## **6.1 Current Challenges**

## <span id="page-14-3"></span>*6.1.1 Existing Domain Gaps*

The workflow of physical adversarial attacks (see Fig. [1\)](#page-1-0) reveals a process where attackers first design in the digital space, deploy in the physical space, and ultimately execute attacks in the digital domain. This workflow involves the transformation between the digital and physical domains. Some researchers have recognized this; for instance, Jan *et al.* [\[100\]](#page-17-28) designed a D2P network to model the transformation of images from the digital domain to the physical domain. However, the current research has paid limited attention to addressing domain gaps, resulting in the unstable attack performance of many methods. The present practices in physical adversarial attacks face significant challenges in reliability and reproducibility.

## <span id="page-14-4"></span>*6.1.2 Uncontrollable Evaluation Settings*

Most existing works evaluate their physical adversarial attack methods in the real world using the adversarial mediums they manufacture. The real-world environment is dynamic, and the process of crafting adversarial mediums involves subjective factors, e.g., the material of the clothing, the quality of the printing, *etc*, all of which are uncontrollable. Future work with reliable and controllable evaluation setups is anticipated.

## **6.2 Future Work**

## *6.2.1 New Adversarial Medium*

In this survey, we define the adversarial medium as the object that carries the adversarial perturbations in the physical world. The adversarial medium plays a significant role in performing a physical attack. From the above discussion, we see that different tasks have different requirements for the adversarial medium, and the suitable adversarial medium can improve the performance of the attack in solving the trilemma, i.e., effectiveness, robustness, and stealthiness. The attacks using patches [\[21\]](#page-15-20), light [\[110\]](#page-17-37), camera ISP [\[121\]](#page-18-5), makeup [\[124\]](#page-18-8), 3D-printed object [\[125\]](#page-18-9), clothing [\[89\]](#page-17-19), *etc*, emerged in turn. Recently, the Laser Beam [\[113\]](#page-17-40) and small lighting bulbs have been used to deceive the DNNs-based models, which inspire novel attack methods and expose the potential risks of DNNs-based applications.

## *6.2.2 Cross-Domain Physical Adversarial Attacks*

To address the challenges mentioned in Sec. [6.1.1,](#page-14-3) attackers need to consider two domain gaps.

<span id="page-14-2"></span>*Digital-to-physical domain gap* arises in Step 2 (see Fig. [1\)](#page-1-0), specifically denoting the procedure in which attackers manufacture physical perturbations based on digital perturbations. A typical example is the printing loss proposed by Sharif *et al.* [\[14\]](#page-15-13), which specifically refers to the inability to accurately and reliably reproduce colors due to the smaller color space of printing devices compared to the RGB color space. They introduced the non-printability score (NPS) to address this issue. Additionally, Jan *et al.* [\[100\]](#page-17-28) employed an image-to-image translation network to model the digital-tophysical transformation. Moreover, the adversarial medium, materials, and certain physical constraints all influence this domain gap. A more detailed consideration will facilitate effective cross-domain attacks.

*Physical-to-digital domain gap* arises in Step 3 (see Fig. [1\)](#page-1-0). Adversarial perturbations carried by the adversarial medium are captured by cameras in the real world, converted into digital images, and then used to attack DNNsbased models. Throughout this process, there exists a domain gap between the transformations from physical perturbations to digital images. Phan *et al.* [\[121\]](#page-18-5) have studied physical adversarial attacks under specific ISP conditions, but they did not explore the performance of attacks across different ISPs. Differentiable ISP simulation or camera simulation is an ideal solution. Combining these simulators will enable the generated adversarial perturbations to maintain attack stability across various hardware imaging devices.

## *6.2.3 Reproducible Evaluation*

To address the challenges mentioned in Sec. [6.1.2,](#page-14-4) researchers should be required to disclose more experimental details. On one hand, this includes production details such as materials, size, and manufacturing processes of the adversarial medium. On the other hand, it involves the environmental conditions of physical experiments, such as lighting, background, and shooting distance. While these details may seem trivial and easily overlooked, they are indispensable for a comprehensive evaluation. Otani *et al.* [\[178\]](#page-19-7) have proposed a template for researchers to reference, thereby promoting fairness and reproducibility in evaluation. In the field of physical adversarial attacks, a template for disclosing experimental details is anticipated in the future.

#### *6.2.4 Physical World Simulation*

The key characteristic of physical adversarial attacks is their real-world feasibility. Precisely simulating the physical environment enhances attacks' robustness in dynamic settings. Simulation engines like Unreal Engine and Unity offer various conditions for attacks, such as lighting, backgrounds, camera distances, and view angles. Most existing methods use these simulators to assess attack efficacy [\[23\]](#page-15-22), [\[60\]](#page-16-36). However, due to non-differentiability, they cannot be used in end-to-end optimization for adversarial perturbations. Beyond basic operations like rotation, noise addition, affine transformations, and occlusions [\[13\]](#page-15-12), [\[22\]](#page-15-21), [\[93\]](#page-17-2), integrating advanced physical scene simulation methods into the attack pipeline is crucial for considering dynamic settings during adversarial perturbation design.

## <span id="page-15-25"></span>*6.2.5 Hexagonal Physical Adversarial Attacks*

The proposed evaluation metric, *hiPAA*, evaluates physical adversarial attacks from six perspectives. However, during the evaluation, we observed that most existing methods lack a comprehensive consideration [\[68\]](#page-16-43), [\[94\]](#page-17-22). They tend to focus on individual perspectives while neglecting others. Some approaches involve tradeoffs between individual dimensions, such as effectiveness and stealthiness [\[22\]](#page-15-21), [\[92\]](#page-17-20). In real-world applications, physical adversarial attacks often need to excel from all six perspectives. Future work with holistic considerations is expected, advancing and evaluating their methods across various perspectives.

## *6.2.6 Physical Adversarial Attacks on New Tasks*

As described in this survey, the current mainstream physical adversarial attack methods are oriented to tasks such as person detection [\[22\]](#page-15-21), [\[90\]](#page-17-1), traffic sign detection [\[99\]](#page-17-27), [\[111\]](#page-17-38), face recognition [\[14\]](#page-15-13), [\[161\]](#page-18-42), *etc*. Although many fields have been covered, some tasks have not yet been explored. For example, Cheng *et al.* [\[77\]](#page-17-8) recently proposed an adversarial patch attack against monocular depth estimation (MDE), which is a critical vision task in real-world driving scenarios. It is the first time to propose an attack on MDE. Besides, we consider that domains with the following two characteristics can be explored for physical adversarial attacks: **1)** using the DNNs techniques, and **2)** applying in the physical world. Such as trajectory prediction [\[179\]](#page-19-8), pose estimation [\[180\]](#page-19-9), action recognition [\[181\]](#page-19-10), *etc*.

## <span id="page-15-24"></span>**7 CONCLUSION**

Physical adversarial attacks have cast a shadow over the reliability of deep neural networks, raising security concerns. Consequently, extensive research has proposed various methods for real-world attacks across multiple tasks. We have provided an overview of the field of physical adversarial attacks on computer vision tasks, covering classification, detection, re-identification, and some niche tasks, with a focus on the adversarial mediums and a comprehensive evaluation. We first propose a general workflow for launching a physical adversarial attack, underlining the important role of the adversarial medium. Additionally, we have devised a new metric termed *hiPPA*, systematically quantifying and assessing attack methods from six distinct perspectives. Correspondingly, we present comparative results for existing methods, offering valuable insights for future improvements. Many challenges remain ahead, and we hope that this paper can motivate further discussion in this field and provides important guidance for future research, ultimately advancing the safety and reliability of machine vision systems.

# **8 ACKNOWLEDGEMENTS**

This work was supported by Hubei Key R&D Project (2022BAA033), National Natural Science Foundation of China (62171325), the Fundamental Research Funds for the Central Universities, Peking University, JSPS KAKENHI (JP23K24876), and JST ASPIRE (JPMJAP2303).

# **REFERENCES**

- <span id="page-15-0"></span>[1] A. Voulodimos, N. Doulamis, A. Doulamis, and E. Protopapadakis, "Deep learning for computer vision: A brief review," *Computational intelligence and neuroscience*, vol. 2018, 2018. [1](#page-0-1)
- <span id="page-15-1"></span>[2] D. W. Otter, J. R. Medina, and J. K. Kalita, "A survey of the usages of deep learning for natural language processing," *TNNLS*, vol. 32, no. 2, pp. 604–624, 2020. [1](#page-0-1)
- <span id="page-15-2"></span>[3] A. B. Nassif, I. Shahin, I. Attili, M. Azzeh, and K. Shaalan, "Speech recognition using deep neural networks: A systematic review," *IEEE access*, vol. 7, pp. 19 143–19 165, 2019. [1](#page-0-1)
- <span id="page-15-3"></span>[4] C. Szegedy, W. Zaremba, I. Sutskever, J. Bruna, D. Erhan, I. J. Goodfellow, and R. Fergus, "Intriguing properties of neural networks," in *ICLR*, Y. Bengio and Y. LeCun, Eds., 2014. [1](#page-0-1)
- <span id="page-15-4"></span>[5] N. Carlini and D. Wagner, "Towards evaluating the robustness of neural networks," in *2017 ieee symposium on security and privacy (sp)*. Ieee, 2017, pp. 39–57. [1](#page-0-1)
- <span id="page-15-5"></span>[6] C. Xiao, B. Li, J.-Y. Zhu, W. He, M. Liu, and D. Song, "Generating adversarial examples with adversarial networks," *arXiv preprint arXiv:1801.02610*, 2018. [1](#page-0-1)
- <span id="page-15-6"></span>[7] N. Inkawhich, W. Wen, H. H. Li, and Y. Chen, "Feature space perturbations yield more transferable adversarial examples," in *CVPR*, 2019, pp. 7066–7074. [1](#page-0-1)
- <span id="page-15-7"></span>[8] X. Yuan, P. He, Q. Zhu, and X. Li, "Adversarial examples: Attacks and defenses for deep learning," *TNNLS*, vol. 30, no. 9, pp. 2805– 2824, 2019. [1](#page-0-1)
- <span id="page-15-8"></span>[9] Z. Zhao, Z. Liu, and M. Larson, "Towards large yet imperceptible adversarial image perturbations with perceptual color distance," in *CVPR*, 2020, pp. 1039–1048. [1](#page-0-1)
- <span id="page-15-9"></span>Y. Diao, T. Shao, Y.-L. Yang, K. Zhou, and H. Wang, "Basar: Blackbox attack on skeletal action recognition," in *CVPR*, 2021, pp. 7597–7607. [1](#page-0-1)
- <span id="page-15-10"></span>[11] Z. Cai, S. Rane, A. E. Brito, C. Song, S. V. Krishnamurthy, A. K. Roy-Chowdhury, and M. S. Asif, "Zero-query transfer attacks on context-aware object detectors," in *CVPR*, 2022, pp. 15 024-15 034. [1](#page-0-1)
- <span id="page-15-11"></span>[12] Z. Wei, J. Chen, M. Goldblum, Z. Wu, T. Goldstein, and Y.- G. Jiang, "Towards transferable adversarial attacks on vision transformers," in *AAAI*, vol. 36, no. 3, 2022, pp. 2668–2676. [1](#page-0-1)
- <span id="page-15-12"></span>[13] S. Thys, W. Van Ranst, and T. Goedemé, "Fooling automated surveillance cameras: adversarial patches to attack person detection," in *CVPR workshops*, 2019, pp. 0–0. [1,](#page-0-1) [4,](#page-3-3) [6,](#page-5-1) [11,](#page-10-2) [12,](#page-11-2) [15](#page-14-5)
- <span id="page-15-13"></span>[14] M. Sharif, S. Bhagavatula, L. Bauer, and M. K. Reiter, "Accessorize to a crime: Real and stealthy attacks on state-of-the-art face recognition," in *Proceedings of the 2016 acm sigsac conference on computer and communications security*, 2016, pp. 1528–1540. [1,](#page-0-1) [6,](#page-5-1) [13,](#page-12-2) [15,](#page-14-5) [16](#page-15-25)
- <span id="page-15-14"></span>[15] L. Sun, M. Tan, and Z. Zhou, "A survey of practical adversarial example attacks," *Cybersecurity*, vol. 1, pp. 1–9, 2018. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-15-15"></span>[16] X. Wei, B. Pu, J. Lu, and B. Wu, "Physically adversarial attacks and defenses in computer vision: A survey," *arXiv preprint arXiv:2211.01671*, 2022. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-15-16"></span>[17] D. Wang, W. Yao, T. Jiang, G. Tang, and X. Chen, "A survey on physical adversarial attack in computer vision," *arXiv preprint arXiv:2209.14262*, 2022. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-15-17"></span>[18] K. Nguyen, T. Fernando, C. Fookes, and S. Sridharan, "Physical adversarial attacks for surveillance: A survey," *arXiv preprint arXiv:2305.01074*, 2023. [1,](#page-0-1) [2](#page-1-2)
- <span id="page-15-18"></span>[19] Q. Xu, G. Tao, S. Cheng, and X. Zhang, "Towards feature space adversarial attack by style perturbation," in *AAAI*, vol. 35, no. 12, 2021, pp. 10 523–10 531. [1](#page-0-1)
- <span id="page-15-19"></span>[20] Z. Cai, X. Xie, S. Li, M. Yin, C. Song, S. V. Krishnamurthy, A. K. Roy-Chowdhury, and M. S. Asif, "Context-aware transfer attacks for object detection," in *AAAI*, vol. 36, no. 1, 2022, pp. 149–157. [1](#page-0-1)
- <span id="page-15-20"></span>[21] T. B. Brown, D. Mané, A. Roy, M. Abadi, and J. Gilmer, "Adversarial patch," in *NIPS workshop*, 2017. [1,](#page-0-1) [4,](#page-3-3) [6,](#page-5-1) [8,](#page-7-3) [9,](#page-8-2) [15](#page-14-5)
- <span id="page-15-21"></span>[22] J. Tan, N. Ji, H. Xie, and X. Xiang, "Legitimate adversarial patches: Evading human eyes and detection models in the physical world," in *ACM MM*, 2021, pp. 5307–5315. [1,](#page-0-1) [4,](#page-3-3) [7,](#page-6-2) [11,](#page-10-2) [12,](#page-11-2) [15,](#page-14-5) [16](#page-15-25)
- <span id="page-15-22"></span>[23] Y. Zhang, H. Foroosh, P. David, and B. Gong, "Camou: Learning physical vehicle camouflages to adversarially attack detectors in the wild," in *ICLR*, 2018. [1,](#page-0-1) [6,](#page-5-1) [10,](#page-9-4) [11,](#page-10-2) [15](#page-14-5)
- <span id="page-15-23"></span>[24] S. Komkov and A. Petiushko, "Advhat: Real-world adversarial attack on arcface face id system," in *ICPR*. IEEE, 2021, pp. 819– 826. [1,](#page-0-1) [6,](#page-5-1) [13](#page-12-2)

- <span id="page-16-0"></span>[25] O. Russakovsky, J. Deng, H. Su, J. Krause, S. Satheesh, S. Ma, Z. Huang, A. Karpathy, A. Khosla, M. Bernstein *et al.*, "Imagenet large scale visual recognition challenge," *IJCV*, vol. 115, no. 3, pp. 211–252, 2015. [2](#page-1-2)
- <span id="page-16-1"></span>[26] Z. Liu, Y. Lin, Y. Cao, H. Hu, Y. Wei, Z. Zhang, S. Lin, and B. Guo, "Swin transformer: Hierarchical vision transformer using shifted windows," in *ICCV*, 2021, pp. 10 012–10 022. [2](#page-1-2)
- <span id="page-16-2"></span>[27] J. Yu, Z. Wang, V. Vasudevan, L. Yeung, M. Seyedhosseini, and Y. Wu, "Coca: Contrastive captioners are image-text foundation models," *Transactions on Machine Learning Research*, 2022. [2](#page-1-2)
- <span id="page-16-3"></span>[28] K. He, G. Gkioxari, P. Dollár, and R. Girshick, "Mask r-cnn," in *ICCV*, 2017, pp. 2961–2969. [2,](#page-1-2) [12,](#page-11-2) [14](#page-13-6)
- <span id="page-16-4"></span>[29] V. Badrinarayanan, A. Kendall, and R. Cipolla, "Segnet: A deep convolutional encoder-decoder architecture for image segmentation," *TPAMI*, vol. 39, no. 12, pp. 2481–2495, 2017. [2](#page-1-2)
- <span id="page-16-5"></span>[30] J. Redmon and A. Farhadi, "Yolov3: An incremental improvement," *arXiv preprint arXiv:1804.02767*, 2018. [2,](#page-1-2) [12](#page-11-2)
- <span id="page-16-6"></span>[31] H. Zhang, F. Li, S. Liu, L. Zhang, H. Su, J. Zhu, L. M. Ni, and H.-Y. Shum, "Dino: Detr with improved denoising anchor boxes for end-to-end object detection," *arXiv preprint arXiv:2203.03605*, 2022. [2](#page-1-2)
- <span id="page-16-7"></span>[32] M. Ye, Z. Wang, X. Lan, and P. C. Yuen, "Visible thermal person re-identification via dual-constrained top-ranking." in *IJCAI*, vol. 1, 2018, p. 2. [2](#page-1-2)
- <span id="page-16-8"></span>Z. Wang, M. Ye, F. Yang, X. Bai, and S. S. 0001, "Cascaded srgan for scale-adaptive low resolution person re-identification." in *IJCAI*, vol. 1, no. 2, 2018, p. 4. [2](#page-1-2)
- <span id="page-16-9"></span>[34] Q. Meng, S. Zhao, Z. Huang, and F. Zhou, "Magface: A universal representation for face recognition and quality assessment," in *CVPR*, 2021, pp. 14 225–14 234. [2](#page-1-2)
- <span id="page-16-10"></span>T. Gu, B. Dolan-Gavitt, and S. Garg, "Badnets: Identifying vulnerabilities in the machine learning model supply chain," *arXiv preprint arXiv:1708.06733*, 2017. [3](#page-2-1)
- <span id="page-16-11"></span>[36] Y. Liu, S. Ma, Y. Aafer, W.-C. Lee, J. Zhai, W. Wang, and X. Zhang, "Trojaning attack on neural networks," 2017. [3](#page-2-1)
- <span id="page-16-12"></span>[37] E. Wenger, J. Passananti, A. N. Bhagoji, Y. Yao, H. Zheng, and B. Y. Zhao, "Backdoor attacks against deep learning systems in the physical world," in *CVPR*, 2021, pp. 6206–6215. [3](#page-2-1)
- <span id="page-16-13"></span>[38] X. Qi, T. Xie, R. Pan, J. Zhu, Y. Yang, and K. Bu, "Towards practical deployment-stage backdoor attack on deep neural networks," in *CVPR*, 2022, pp. 13 347–13 357. [3,](#page-2-1) [4](#page-3-3)
- <span id="page-16-14"></span>[39] M. Barreno, B. Nelson, R. Sears, A. D. Joseph, and J. D. Tygar, "Can machine learning be secure?" in *Proceedings of the 2006 ACM Symposium on Information, computer and communications security*, 2006, pp. 16–25. [3](#page-2-1)
- <span id="page-16-15"></span>[40] A. Shafahi, W. R. Huang, M. Najibi, O. Suciu, C. Studer, T. Dumitras, and T. Goldstein, "Poison frogs! targeted clean-label poisoning attacks on neural networks," *NIPS*, vol. 31, 2018. [3](#page-2-1)
- <span id="page-16-16"></span>X. Zhang, X. Zhu, and L. Lessard, "Online data poisoning attacks," in *Learning for Dynamics and Control*. PMLR, 2020, pp. 201–210. [3](#page-2-1)
- <span id="page-16-17"></span>[42] A. Oprea, A. Singhal, and A. Vassilev, "Poisoning attacks against machine learning: Can machine learning be trustworthy?" *Computer*, vol. 55, no. 11, pp. 94–99, 2022. [3](#page-2-1)
- <span id="page-16-18"></span>[43] Y. Gao, B. G. Doan, Z. Zhang, S. Ma, J. Zhang, A. Fu, S. Nepal, and H. Kim, "Backdoor attacks and countermeasures on deep learning: A comprehensive review," *arXiv preprint arXiv:2007.10760*, 2020. [3](#page-2-1)
- <span id="page-16-19"></span>[44] E. Bagdasaryan and V. Shmatikov, "Blind backdoors in deep learning models," in *USENIX Security*, 2021, pp. 1505–1521. [3](#page-2-1)
- <span id="page-16-20"></span>[45] K. Doan, Y. Lao, W. Zhao, and P. Li, "Lira: Learnable, imperceptible and robust backdoor attacks," in *ICCV*, 2021, pp. 11 966– 11 976. [3](#page-2-1)
- <span id="page-16-21"></span>[46] J. Su, D. V. Vargas, and K. Sakurai, "One pixel attack for fooling deep neural networks," *IEEE Transactions on Evolutionary Computation*, vol. 23, no. 5, pp. 828–841, 2019. [4,](#page-3-3) [9](#page-8-2)
- <span id="page-16-22"></span>[47] H. Ma, Y. Li, Y. Gao, Z. Zhang, A. Abuadbba, A. Fu, S. F. Al-Sarawi, N. Surya, and D. Abbott, "Macab: Model-agnostic cleanannotation backdoor to object detection with natural trigger in real-world," 2022. [4](#page-3-3)
- <span id="page-16-23"></span>[48] H. Wei, H. Yu, K. Zhang, Z. Wang, J. Zhu, and Z. Wang, "Moiré backdoor attack (mba): A novel trigger for pedestrian detectors in the physical world," in *ACM MM*, 2023, pp. 8828–8838. [4](#page-3-3)
- <span id="page-16-24"></span>[49] M. P. Van Albada and A. Lagendijk, "Observation of weak localization of light in a random medium," *Physical review letters*, vol. 55, no. 24, p. 2692, 1985. [4](#page-3-3)

- <span id="page-16-27"></span>[50] D. Y. Yang, J. Xiong, X. Li, X. Yan, J. Raiti, Y. Wang, H. Wu, and Z. Zhong, "Building towards" invisible cloak": Robust physical adversarial attack on yolo object detector," in *UEMCON*. IEEE, 2018, pp. 368–374. [6,](#page-5-1) [11,](#page-10-2) [12](#page-11-2)
- <span id="page-16-28"></span>[51] J. Li, F. Schmidt, and Z. Kolter, "Adversarial camera stickers: A physical camera-based attack on deep learning systems," in *ICML*. PMLR, 2019, pp. 3896–3904. [6,](#page-5-1) [9](#page-8-2)
- <span id="page-16-29"></span>[52] M. Sharif, S. Bhagavatula, L. Bauer, and M. K. Reiter, "A general framework for adversarial examples with objectives," *ACM TOPS*, vol. 22, no. 3, pp. 1–30, 2019. [6,](#page-5-1) [13](#page-12-2)
- <span id="page-16-30"></span>[53] I. Singh, S. Momiyama, K. Kakizaki, and T. Araki, "On brightness agnostic adversarial examples against face recognition systems," in *BIOSIG*. IEEE, 2021, pp. 1–5. [6,](#page-5-1) [13](#page-12-2)
- <span id="page-16-25"></span>[54] J. Wang, A. Liu, Z. Yin, S. Liu, S. Tang, and X. Liu, "Dual attention suppression attack: Generate adversarial camouflage in physical world," in *CVPR*, 2021, pp. 8565–8574. [4,](#page-3-3) [6,](#page-5-1) [11](#page-10-2)
- <span id="page-16-31"></span>[55] P. Jing, Q. Tang, Y. Du, L. Xue, X. Luo, T. Wang, S. Nie, and S. Wu, "Too good to be safe: Tricking lane detection in autonomous driving with crafted perturbations," in *USENIX Security*, 2021, pp. 3237–3254. [6,](#page-5-1) [12](#page-11-2)
- <span id="page-16-32"></span>[56] D. Wang, T. Jiang, J. Sun, W. Zhou, Z. Gong, X. Zhang, W. Yao, and X. Chen, "Fca: Learning a 3d full-coverage vehicle camouflage for multi-view physical adversarial attack," in *AAAI*, vol. 36, no. 2, 2022, pp. 2414–2422. [6,](#page-5-1) [10,](#page-9-4) [11](#page-10-2)
- <span id="page-16-33"></span>[57] N. Suryanto, Y. Kim, H. Kang, H. T. Larasati, Y. Yun, T.-T.-H. Le, H. Yang, S.-Y. Oh, and H. Kim, "Dta: Physical camouflage attacks using differentiable transformation network," in *CVPR*, 2022, pp. 15 305–15 314. [6,](#page-5-1) [10,](#page-9-4) [11](#page-10-2)
- <span id="page-16-34"></span>[58] A. Zolfi, S. Avidan, Y. Elovici, and A. Shabtai, "Adversarial mask: Real-world adversarial attack against face recognition models," *arXiv preprint arXiv:2111.10759*, 2021. [6,](#page-5-1) [13](#page-12-2)
- <span id="page-16-35"></span>[59] X. Wei, Y. Guo, and J. Yu, "Adversarial sticker: A stealthy attack method in the physical world," *TPAMI*, 2022. [6,](#page-5-1) [13](#page-12-2)
- <span id="page-16-36"></span>[60] Y. Duan, J. Chen, X. Zhou, J. Zou, Z. He, J. Zhang, W. Zhang, and Z. Pan, "Learning coated adversarial camouflages for object detectors," in *IJCAI*, 2022, pp. 891–897. [6,](#page-5-1) [10,](#page-9-4) [11,](#page-10-2) [15](#page-14-5)
- <span id="page-16-37"></span>[61] X. Wei, S. Ruan, Y. Dong, and H. Su, "Distributional modeling for location-aware adversarial patches," *arXiv preprint arXiv:2306.16131*, 2023. [6,](#page-5-1) [13](#page-12-2)
- <span id="page-16-38"></span>[62] J. Zheng, C. Lin, J. Sun, Z. Zhao, Q. Li, and C. Shen, "Physical 3d adversarial attacks against monocular depth estimation in autonomous driving," in *CVPR*, 2024, pp. 24 452–24 461. [6,](#page-5-1) [14](#page-13-6)
- <span id="page-16-39"></span>[63] K. Eykholt, I. Evtimov, E. Fernandes, B. Li, A. Rahmati, C. Xiao, A. Prakash, T. Kohno, and D. Song, "Robust physical-world attacks on deep learning visual classification," in *CVPR*, 2018, pp. 1625–1634. [6,](#page-5-1) [10,](#page-9-4) [12](#page-11-2)
- <span id="page-16-40"></span>[64] Y. Zhao, H. Zhu, R. Liang, Q. Shen, S. Zhang, and K. Chen, "Seeing isn't believing: Towards more robust adversarial attack against real world object detectors," in *Proceedings of the 2019 ACM SIGSAC Conference on Computer and Communications Security*, 2019, pp. 1989–2004. [6,](#page-5-1) [12](#page-11-2)
- <span id="page-16-26"></span>[65] A. Liu, X. Liu, J. Fan, Y. Ma, A. Zhang, H. Xie, and D. Tao, "Perceptual-sensitive gan for generating adversarial patches," in *AAAI*, vol. 33, no. 01, 2019, pp. 1028–1035. [4,](#page-3-3) [6,](#page-5-1) [10](#page-9-4)
- <span id="page-16-41"></span>[66] R. R. Wiyatno and A. Xu, "Physical adversarial textures that fool visual object tracking," in *ICCV*, 2019, pp. 4822–4831. [6,](#page-5-1) [13,](#page-12-2) [14](#page-13-6)
- <span id="page-16-42"></span>[67] Z. Wang, S. Zheng, M. Song, Q. Wang, A. Rahimpour, and H. Qi, "advpattern: physical-world attacks on deep person reidentification via adversarially transformable patterns," in *ICCV*, 2019, pp. 8341–8350. [6,](#page-5-1) [13](#page-12-2)
- <span id="page-16-43"></span>[68] A. Ranjan, J. Janai, A. Geiger, and M. J. Black, "Attacking optical flow," in *ICCV*, 2019, pp. 2404–2413. [6,](#page-5-1) [13,](#page-12-2) [14,](#page-13-6) [16](#page-15-25)
- <span id="page-16-44"></span>[69] A. Liu, J. Wang, X. Liu, B. Cao, C. Zhang, and H. Yu, "Bias-based universal adversarial patch attack for automatic check-out," in *ECCV*. Springer, 2020, pp. 395–410. [6,](#page-5-1) [8,](#page-7-3) [9](#page-8-2)
- <span id="page-16-45"></span>[70] A. Zolfi, M. Kravchik, Y. Elovici, and A. Shabtai, "The translucent patch: A physical and universal attack on object detectors," in *CVPR*, 2021, pp. 15 232–15 241. [6,](#page-5-1) [12](#page-11-2)
- <span id="page-16-46"></span>[71] L. Ding, Y. Wang, K. Yuan, M. Jiang, P. Wang, H. Huang, and Z. J. Wang, "Towards universal physical attacks on single object tracking," in *AAAI*, vol. 35, no. 2, 2021, pp. 1236–1245. [6,](#page-5-1) [13,](#page-12-2) [14,](#page-13-6) [15](#page-14-5)
- <span id="page-16-47"></span>[72] Z. Xiao, X. Gao, C. Fu, Y. Dong, W. Gao, X. Zhang, J. Zhou, and J. Zhu, "Improving transferability of adversarial patches on face recognition with generative models," in *CVPR*, 2021, pp. 11 845– 11 854. [6,](#page-5-1) [13](#page-12-2)

- <span id="page-17-4"></span>[73] J. Wang, A. Liu, X. Bai, and X. Liu, "Universal adversarial patch attack for automatic checkout using perceptual and attentional bias," *TIP*, vol. 31, pp. 598–611, 2021. [6,](#page-5-1) [8,](#page-7-3) [9](#page-8-2)
- <span id="page-17-5"></span>[74] P. A. Sava, J.-P. Schulze, P. Sperl, and K. Böttinger, "Assessing the impact of transformations on physical adversarial attacks, in *Proceedings of the 15th ACM Workshop on Artificial Intelligence and Security*, 2022, pp. 79–90. [6,](#page-5-1) [12](#page-11-2)
- <span id="page-17-6"></span>S. Casper, M. Nadeau, D. Hadfield-Menell, and G. Kreiman, "Robust feature-level adversaries are interpretability tools," *NIPS*, vol. 35, pp. 33 093–33 106, 2022. [6,](#page-5-1) [9,](#page-8-2) [10](#page-9-4)
- <span id="page-17-7"></span>[76] B. G. Doan, M. Xue, S. Ma, E. Abbasnejad, and D. C. Ranasinghe, "Tnt attacks! universal naturalistic adversarial patches against deep neural network systems," *TIFS*, vol. 17, pp. 3816–3830, 2022. [6,](#page-5-1) [9](#page-8-2)
- <span id="page-17-8"></span>[77] Z. Cheng, J. Liang, H. Choi, G. Tao, Z. Cao, D. Liu, and X. Zhang, "Physical attack on monocular depth estimation with optimal adversarial patches," in *ECCV*. Springer, 2022, pp. 514–532. [6,](#page-5-1) [13,](#page-12-2) [14,](#page-13-6) [16](#page-15-25)
- <span id="page-17-9"></span>[78] F. Nesti, G. Rossolini, S. Nair, A. Biondi, and G. Buttazzo, "Evaluating the robustness of semantic segmentation for autonomous driving against real-world adversarial patch attacks," in *WACV*, 2022, pp. 2280–2289. [6,](#page-5-1) [13,](#page-12-2) [14](#page-13-6)
- <span id="page-17-10"></span>[79] S. Liu, J. Wang, A. Liu, Y. Li, Y. Gao, X. Liu, and D. Tao, "Harnessing perceptual adversarial patches for crowd counting," in *Proceedings of the 2022 ACM SIGSAC Conference on CCS*. New York, NY, USA: Association for Computing Machinery, 2022, p. 2055–2069. [6,](#page-5-1) [13,](#page-12-2) [14](#page-13-6)
- <span id="page-17-11"></span>[80] Z. Chen, B. Li, S. Wu, J. Xu, S. Ding, and W. Zhang, "Shape matters: deformable patch attack," in *ECCV*. Springer, 2022, pp. 529–548. [6,](#page-5-1) [9](#page-8-2)
- <span id="page-17-12"></span>[81] X. Wei, Y. Guo, J. Yu, and B. Zhang, "Simultaneously optimizing perturbations and positions for black-box adversarial patch attacks," *TPAMI*, 2022. [6,](#page-5-1) [13](#page-12-2)
- <span id="page-17-13"></span>[82] A. Du, B. Chen, T.-J. Chin, Y. W. Law, M. Sasdelli, R. Rajasegaran, and D. Campbell, "Physical adversarial attacks on an aerial imagery object detector," in *WACV*, 2022, pp. 1796–1806. [6,](#page-5-1) [11](#page-10-2)
- <span id="page-17-14"></span>[83] X. Wei, J. Yu, and Y. Huang, "Physically adversarial infrared patches with learnable shapes and locations," in *CVPR*, June 2023, pp. 12 334–12 342. [6,](#page-5-1) [12](#page-11-2)
- <span id="page-17-15"></span>[84] H. Huang, Z. Chen, H. Chen, Y. Wang, and K. Zhang, "Tsea: Transfer-based self-ensemble attack on object detection," in *CVPR*, 2023. [6,](#page-5-1) [11,](#page-10-2) [12](#page-11-2)
- <span id="page-17-0"></span>[85] X. Wei, Y. Huang, Y. Sun, and J. Yu, "Unified adversarial patch for visible-infrared cross-modal attacks in the physical world," 2023. [4,](#page-3-3) [6,](#page-5-1) [12](#page-11-2)
- <span id="page-17-16"></span>[86] S. Zhang, Y. Cheng, W. Zhu, X. Ji, and W. Xu, "{CAPatch}: Physical adversarial patch against image captioning systems, in *USENIX Security*, 2023, pp. 679–696. [6,](#page-5-1) [13,](#page-12-2) [14,](#page-13-6) [15](#page-14-5)
- <span id="page-17-17"></span>[87] X. Wei, J. Yu, and Y. Huang, "Infrared adversarial patches with learnable shapes and locations in the physical world," *IJCV*, pp. 1–17, 2023. [6,](#page-5-1) [12](#page-11-2)
- <span id="page-17-18"></span>W. Zhu, X. Ji, Y. Cheng, S. Zhang, and W. Xu, "Tpatch: A triggered physical adversarial patch," *arXiv preprint arXiv:2401.00148*, 2023. [6,](#page-5-1) [12](#page-11-2)
- <span id="page-17-19"></span>[89] K. Xu, G. Zhang, S. Liu, Q. Fan, M. Sun, H. Chen, P.-Y. Chen, Y. Wang, and X. Lin, "Adversarial t-shirt! evading person detectors in a physical world," in *ECCV*. Springer, 2020, pp. 665–681. [6,](#page-5-1) [7,](#page-6-2) [11,](#page-10-2) [12,](#page-11-2) [15](#page-14-5)
- <span id="page-17-1"></span>L. Huang, C. Gao, Y. Zhou, C. Xie, A. L. Yuille, C. Zou, and N. Liu, "Universal physical camouflage attacks on object detectors," in *CVPR*, 2020, pp. 720–729. [4,](#page-3-3) [7,](#page-6-2) [11,](#page-10-2) [12,](#page-11-2) [16](#page-15-25)
- <span id="page-17-21"></span>[91] Z. Wu, S.-N. Lim, L. S. Davis, and T. Goldstein, "Making an invisibility cloak: Real world adversarial attacks on object detectors," in *ECCV*. Springer, 2020, pp. 1–17. [7,](#page-6-2) [11,](#page-10-2) [12](#page-11-2)
- <span id="page-17-20"></span>[92] Y.-C.-T. Hu, B.-H. Kung, D. S. Tan, J.-C. Chen, K.-L. Hua, and W.-H. Cheng, "Naturalistic physical adversarial patch for object detectors," in *ICCV*, 2021, pp. 7848–7857. [6,](#page-5-1) [7,](#page-6-2) [11,](#page-10-2) [12,](#page-11-2) [16](#page-15-25)
- <span id="page-17-2"></span>Z. Hu, S. Huang, X. Zhu, F. Sun, B. Zhang, and X. Hu, "Adversarial texture for fooling person detectors in the physical world," in *CVPR*, 2022, pp. 13 307–13 316. [4,](#page-3-3) [6,](#page-5-1) [7,](#page-6-2) [11,](#page-10-2) [12,](#page-11-2) [15](#page-14-5)
- <span id="page-17-22"></span>[94] X. Zhu, Z. Hu, S. Huang, J. Li, and X. Hu, "Infrared invisible clothing: Hiding from infrared detectors at multiple angles in real world," in *CVPR*, 2022, pp. 13 317–13 326. [7,](#page-6-2) [11,](#page-10-2) [12,](#page-11-2) [16](#page-15-25)
- <span id="page-17-23"></span>[95] J. Sun, W. Yao, T. Jiang, D. Wang, and X. Chen, "Differential evolution based dual adversarial camouflage: Fooling human eyes and object detectors," *Neural Networks*, vol. 163, pp. 256–271, 2023. [7,](#page-6-2) [11,](#page-10-2) [12](#page-11-2)

- <span id="page-17-24"></span>[96] Z. Hu, W. Chu, X. Zhu, H. Zhang, B. Zhang, and X. Hu, "Physically realizable natural-looking clothing textures evade person detectors via 3d modeling," in *CVPR*, June 2023, pp. 16 975– 16 984. [7,](#page-6-2) [11,](#page-10-2) [12](#page-11-2)
- <span id="page-17-25"></span>[97] A. Kurakin, I. J. Goodfellow, and S. Bengio, "Adversarial examples in the physical world," 2017. [Online]. Available: <https://openreview.net/forum?id=HJGU3Rodl> [7,](#page-6-2) [9](#page-8-2)
- <span id="page-17-26"></span>[98] S.-T. Chen, C. Cornelius, J. Martin, and D. H. P. Chau, "Shapeshifter: Robust physical adversarial attack on faster r-cnn object detector," in *Joint European Conference on Machine Learning and Knowledge Discovery in Databases*. Springer, 2018, pp. 52–68. [7,](#page-6-2) [11,](#page-10-2) [12](#page-11-2)
- <span id="page-17-27"></span>[99] D. Song, K. Eykholt, I. Evtimov, E. Fernandes, B. Li, A. Rahmati, F. Tramèr, A. Prakash, and T. Kohno, "Physical adversarial examples for object detectors," in *12th USENIX Workshop on Offensive Technologies (WOOT 18)*, 2018. [7,](#page-6-2) [12,](#page-11-2) [16](#page-15-25)
- <span id="page-17-28"></span>[100] S. T. Jan, J. Messou, Y.-C. Lin, J.-B. Huang, and G. Wang, "Connecting the digital and physical world: Improving the robustness of adversarial attacks," in *AAAI*, vol. 33, no. 01, 2019, pp. 962–969. [7,](#page-6-2) [9,](#page-8-2) [15](#page-14-5)
- <span id="page-17-29"></span>[101] Q. Guo, F. Juefei-Xu, X. Xie, L. Ma, J. Wang, B. Yu, W. Feng, and Y. Liu, "Watch out! motion is blurring the vision of your deep neural networks," *NIPS*, vol. 33, pp. 975–985, 2020. [7,](#page-6-2) [9](#page-8-2)
- <span id="page-17-3"></span>[102] Z. Kong, J. Guo, A. Li, and C. Liu, "Physgan: Generating physical-world-resilient adversarial examples for autonomous driving," in *CVPR*, 2020, pp. 14 254–14 263. [4,](#page-3-3) [7,](#page-6-2) [13,](#page-12-2) [14,](#page-13-6) [15](#page-14-5)
- <span id="page-17-30"></span>[103] R. Duan, X. Ma, Y. Wang, J. Bailey, A. K. Qin, and Y. Yang, "Adversarial camouflage: Hiding physical-world attacks with natural styles," in *CVPR*, 2020, pp. 1000–1008. [7,](#page-6-2) [9](#page-8-2)
- <span id="page-17-31"></span>[104] K. Yang, T. Tsai, H. Yu, T.-Y. Ho, and Y. Jin, "Beyond digital domain: Fooling deep learning based recognition system in physical world," in *AAAI*, vol. 34, no. 01, 2020, pp. 1088–1095. [7,](#page-6-2) [12](#page-11-2)
- <span id="page-17-32"></span>[105] W. Feng, B. Wu, T. Zhang, Y. Zhang, and Y. Zhang, "Meta-attack: Class-agnostic and model-agnostic physical adversarial attack," in *ICCV*, 2021, pp. 7787–7796. [7,](#page-6-2) [9](#page-8-2)
- <span id="page-17-33"></span>[106] Y. Dong, S. Ruan, H. Su, C. Kang, X. Wei, and J. Zhu, "Viewfool: Evaluating the robustness of visual recognition to adversarial viewpoints," *NIPS*, vol. 35, pp. 36 789–36 803, 2022. [7,](#page-6-2) [9](#page-8-2)
- <span id="page-17-34"></span>[107] W. Feng, N. Xu, T. Zhang, B. Wu, and Y. Zhang, "Robust and generalized physical adversarial attacks via meta-gan," *TIFS*, 2023. [7,](#page-6-2) [9](#page-8-2)
- <span id="page-17-35"></span>[108] N. Nichols and R. Jasper, "Projecting trouble: Light based adversarial attacks on deep learning classifiers," 2018. [7,](#page-6-2) [9](#page-8-2)
- <span id="page-17-36"></span>[109] Y. Man, M. Li, and R. Gerdes, "Poster: Perceived adversarial examples," in *IEEE Symposium on Security and Privacy*, no. 2019, 2019. [7,](#page-6-2) [9](#page-8-2)
- <span id="page-17-37"></span>[110] D.-L. Nguyen, S. S. Arora, Y. Wu, and H. Yang, "Adversarial light projection attacks on face recognition systems: A feasibility study," in *CVPR workshops*, 2020, pp. 814–815. [7,](#page-6-2) [13,](#page-12-2) [15](#page-14-5)
- <span id="page-17-38"></span>[111] G. Lovisotto, H. Turner, I. Sluganovic, M. Strohmeier, and I. Martinovic, "SLAP: Improving physical adversarial examples with Short-Lived adversarial perturbations," in *USENIX Security*, 2021, pp. 1865–1882. [7,](#page-6-2) [12,](#page-11-2) [16](#page-15-25)
- <span id="page-17-39"></span>[112] A. Gnanasambandam, A. M. Sherman, and S. H. Chan, "Optical adversarial attack," in *ICCV*, 2021, pp. 92–101. [7,](#page-6-2) [9,](#page-8-2) [13](#page-12-2)
- <span id="page-17-40"></span>[113] R. Duan, X. Mao, A. K. Qin, Y. Chen, S. Ye, Y. He, and Y. Yang, "Adversarial laser beam: Effective physical-world attack to dnns in a blink," in *CVPR*, 2021, pp. 16 062–16 071. [7,](#page-6-2) [9,](#page-8-2) [15](#page-14-5)
- <span id="page-17-41"></span>[114] Y. Zhong, X. Liu, D. Zhai, J. Jiang, and X. Ji, "Shadows can be dangerous: Stealthy and effective physical-world adversarial attack by natural phenomenon," in *CVPR*, 2022, pp. 15 345– 15 354. [7,](#page-6-2) [10](#page-9-4)
- <span id="page-17-42"></span>[115] B. Huang and H. Ling, "Spaa: Stealthy projector-based adversarial attacks on deep image classifiers," in *2022 IEEE Conference on Virtual Reality and 3D User Interfaces (VR)*. IEEE, 2022, pp. 534–542. [7,](#page-6-2) [9](#page-8-2)
- <span id="page-17-43"></span>[116] H. Wen, S. Chang, and L. Zhou, "Light projection-based physicalworld vanishing attack against car detection," in *ICASSP*, 2023, pp. 1–5. [7,](#page-6-2) [11](#page-10-2)
- <span id="page-17-44"></span>[117] C. Hu, Y. Wang, K. Tiliwalidi, and W. Li, "Adversarial laser spot: Robust and covert physical-world attack to dnns," in *Asian Conference on Machine Learning*. PMLR, 2023, pp. 483–498. [7,](#page-6-2) [12](#page-11-2)
- <span id="page-17-45"></span>[118] Y. Li, Y. Li, X. Dai, S. Guo, and B. Xiao, "Physical-world optical adversarial attacks on 3d face recognition," in *CVPR*, June 2023, pp. 24 699–24 708. [7,](#page-6-2) [13](#page-12-2)

- <span id="page-18-3"></span>[119] D. Wang, W. Yao, T. Jiang, C. Li, and X. Chen, "Rfla: A stealthy reflected light adversarial attack in the physical world," in *ICCV*, 2023, pp. 4455–4465. [7,](#page-6-2) [10](#page-9-4)
- <span id="page-18-4"></span>[120] A. Sayles, A. Hooda, M. Gupta, R. Chatterjee, and E. Fernandes, "Invisible perturbations: Physical adversarial examples exploiting the rolling shutter effect," in *CVPR*, 2021, pp. 14 666–14 675. [7,](#page-6-2) [9](#page-8-2)
- <span id="page-18-5"></span>[121] B. Phan, F. Mannan, and F. Heide, "Adversarial imaging pipelines," in *CVPR*, 2021, pp. 16 051–16 061. [7,](#page-6-2) [9,](#page-8-2) [15](#page-14-5)
- <span id="page-18-6"></span>[122] X. Zhu, X. Li, J. Li, Z. Wang, and X. Hu, "Fooling thermal infrared pedestrian detectors in real world using small bulbs," in *AAAI*, vol. 35, no. 4, 2021, pp. 3616–3624. [7,](#page-6-2) [11,](#page-10-2) [12](#page-11-2)
- <span id="page-18-7"></span>[123] H. Wei, Z. Wang, X. Jia, Y. Zheng, H. Tang, S. Satoh, and Z. Wang, "Hotcold block: Fooling thermal infrared detectors with a novel wearable design," in *AAAI*, vol. 37, no. 12, 2023, pp. 15 233– 15 241. [7,](#page-6-2) [12](#page-11-2)
- <span id="page-18-8"></span>[124] B. Yin, W. Wang, T. Yao, J. Guo, Z. Kong, S. Ding, J. Li, and C. Liu, "Adv-makeup: A new imperceptible and transferable attack on face recognition," 2021. [7,](#page-6-2) [13,](#page-12-2) [15](#page-14-5)
- <span id="page-18-9"></span>[125] A. Athalye, L. Engstrom, A. Ilyas, and K. Kwok, "Synthesizing robust adversarial examples," in *ICML*. PMLR, 2018, pp. 284– 293. [7,](#page-6-2) [9,](#page-8-2) [15](#page-14-5)
- <span id="page-18-10"></span>[126] X. Zeng, C. Liu, Y.-S. Wang, W. Qiu, L. Xie, Y.-W. Tai, C.-K. Tang, and A. L. Yuille, "Adversarial attacks beyond the image space, in *CVPR*, 2019, pp. 4302–4311. [7,](#page-6-2) [9,](#page-8-2) [10](#page-9-4)
- <span id="page-18-11"></span>[127] X. Yang, C. Liu, L. Xu, Y. Wang, Y. Dong, N. Chen, H. Su, and J. Zhu, "Towards effective adversarial textured 3d meshes on physical face recognition," in *CVPR*, June 2023, pp. 4119–4128. [7,](#page-6-2) [13](#page-12-2)
- <span id="page-18-12"></span>[128] A. Liu, J. Guo, J. Wang, S. Liang, R. Tao, W. Zhou, C. Liu, X. Liu, and D. Tao, "{X-Adv}: Physical adversarial object attacks against x-ray prohibited item detection," in *USENIX Security*, 2023, pp. 3781–3798. [7,](#page-6-2) [13,](#page-12-2) [14](#page-13-6)
- <span id="page-18-13"></span>[129] Y. Huang, Y. Dong, S. Ruan, X. Yang, H. Su, and X. Wei, "Towards transferable targeted 3d adversarial attack in the physical world," in *CVPR*, 2024, pp. 24 512–24 522. [7,](#page-6-2) [9,](#page-8-2) [10](#page-9-4)
- <span id="page-18-0"></span>[130] N. Hingun, C. Sitawarin, J. Li, and D. Wagner, "Reap: A largescale realistic adversarial patch benchmark," *ICCV*, 2023. [4](#page-3-3)
- <span id="page-18-1"></span>[131] N. Wang, Y. Luo, T. Sato, K. Xu, and Q. A. Chen, "Does physical adversarial example really matter to autonomous driving? towards system-level effect of adversarial object evasion attack," *ICCV*, 2023. [4](#page-3-3)
- <span id="page-18-2"></span>[132] S. Li, S. Zhang, G. Chen, D. Wang, P. Feng, J. Wang, A. Liu, X. Yi, and X. Liu, "Towards benchmarking and assessing visual naturalness of physical world adversarial attacks," in *CVPR*, 2023, pp. 12 324–12 333. [4,](#page-3-3) [6](#page-5-1)
- <span id="page-18-14"></span>[133] I. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. Warde-Farley, S. Ozair, A. Courville, and Y. Bengio, "Generative adversarial networks," *Communications of the ACM*, vol. 63, no. 11, pp. 139–144, 2020. [9](#page-8-2)
- <span id="page-18-15"></span>[134] J. Deng, W. Dong, R. Socher, L.-J. Li, K. Li, and L. Fei-Fei, "Imagenet: A large-scale hierarchical image database," in *CVPR*. Ieee, 2009, pp. 248–255. [9](#page-8-2)
- <span id="page-18-16"></span>[135] C. Szegedy, V. Vanhoucke, S. Ioffe, J. Shlens, and Z. Wojna, "Rethinking the inception architecture for computer vision," in *CVPR*, 2016, pp. 2818–2826. [9](#page-8-2)
- <span id="page-18-17"></span>[136] J.-Y. Zhu, T. Park, P. Isola, and A. A. Efros, "Unpaired image-toimage translation using cycle-consistent adversarial networks," in *ICCV*, 2017, pp. 2223–2232. [9](#page-8-2)
- <span id="page-18-18"></span>[137] J. Wang, Z. Yin, P. Hu, A. Liu, R. Tao, H. Qin, X. Liu, and D. Tao, "Defensive patches for robust recognition in the physical world," in *CVPR*, 2022, pp. 2456–2465. [10](#page-9-4)
- <span id="page-18-19"></span>[138] T. Wu, X. Ning, W. Li, R. Huang, H. Yang, and Y. Wang, "Physical adversarial attack on vehicle detector in the carla simulator," *arXiv preprint arXiv:2007.16118*, 2020. [10](#page-9-4)
- <span id="page-18-20"></span>[139] A. Dosovitskiy, G. Ros, F. Codevilla, A. Lopez, and V. Koltun, "Carla: An open urban driving simulator," in *Conference on robot learning*. PMLR, 2017, pp. 1–16. [10,](#page-9-4) [14](#page-13-6)
- <span id="page-18-21"></span>[140] H. Kato, Y. Ushiku, and T. Harada, "Neural 3d mesh renderer," in *CVPR*, 2018, pp. 3907–3916. [10](#page-9-4)
- <span id="page-18-22"></span>[141] J. Thies, M. Zollhöfer, and M. Nießner, "Deferred neural rendering: Image synthesis using neural textures," *TOG*, vol. 38, no. 4, pp. 1–12, 2019. [10](#page-9-4)
- <span id="page-18-23"></span>[142] K. Rematas and V. Ferrari, "Neural voxel renderer: Learning an accurate and controllable rendering tool," in *CVPR*, 2020, pp. 5417–5427. [10](#page-9-4)

- <span id="page-18-24"></span>[143] J. Redmon, S. Divvala, R. Girshick, and A. Farhadi, "You only look once: Unified, real-time object detection," in *CVPR*, 2016, pp. 779–788. [11](#page-10-2)
- <span id="page-18-25"></span>[144] J. Redmon and A. Farhadi, "Yolo9000: better, faster, stronger," in *CVPR*, 2017, pp. 7263–7271. [11,](#page-10-2) [12](#page-11-2)
- <span id="page-18-26"></span>[145] N. Dalal and B. Triggs, "Histograms of oriented gradients for human detection," in *CVPR*, vol. 1. Ieee, 2005, pp. 886-893. [11](#page-10-2)
- <span id="page-18-27"></span>[146] Z. Wang, Q. She, and T. E. Ward, "Generative adversarial networks in computer vision: A survey and taxonomy," *ACM Computing Surveys*, vol. 54, no. 2, pp. 1–38, 2021. [11](#page-10-2)
- <span id="page-18-28"></span>[147] A. Brock, J. Donahue, and K. Simonyan, "Large scale gan training for high fidelity natural image synthesis," *arXiv preprint arXiv:1809.11096*, 2018. [11](#page-10-2)
- <span id="page-18-29"></span>[148] T. Karras, M. Aittala, S. Laine, E. Härkönen, J. Hellsten, J. Lehtinen, and T. Aila, "Alias-free generative adversarial networks," *NIPS*, vol. 34, pp. 852–863, 2021. [11](#page-10-2)
- <span id="page-18-30"></span>[149] M. Andriluka, L. Pishchulin, P. Gehler, and B. Schiele, "2d human pose estimation: New benchmark and state of the art analysis," in *CVPR*, 2014, pp. 3686–3693. [11](#page-10-2)
- <span id="page-18-31"></span>[150] J. Long, E. Shelhamer, and T. Darrell, "Fully convolutional networks for semantic segmentation," in *CVPR*, 2015, pp. 3431–3440. [11](#page-10-2)
- <span id="page-18-32"></span>[151] J. T. Springenberg, A. Dosovitskiy, T. Brox, and M. Riedmiller, "Striving for simplicity: The all convolutional net," *arXiv preprint arXiv:1412.6806*, 2014. [11](#page-10-2)
- <span id="page-18-33"></span>[152] A. Hatcher, *Algebraic topology*. Cambridge University Press, 2002. [11](#page-10-2)
- <span id="page-18-34"></span>[153] M. Martin, A. Roitberg, M. Haurilet, M. Horne, S. Reiß, M. Voit, and R. Stiefelhagen, "Drive&act: A multi-modal dataset for finegrained driver behavior recognition in autonomous vehicles," in *ICCV*, 2019, pp. 2801–2810. [11](#page-10-2)
- <span id="page-18-35"></span>[154] M. Ye, C. Chen, J. Shen, and L. Shao, "Dynamic tri-level relation mining with attentive graph for visible infrared reidentification," *TIFS*, vol. 17, pp. 386–398, 2021. [11](#page-10-2)
- <span id="page-18-36"></span>[155] FLIR, "Teledyne flir free adas thermal datasets v2," [EB/OL], 2022, [https://adas-dataset-v2.flirconservator.com/.](https://adas-dataset-v2.flirconservator.com/) [11](#page-10-2)
- <span id="page-18-37"></span>[156] S. Ren, K. He, R. Girshick, and J. Sun, "Faster r-cnn: Towards real-time object detection with region proposal networks," *NIPS*, vol. 28, 2015. [12](#page-11-2)
- <span id="page-18-38"></span>[157] G. Jocher, "Yolov5 detector," [https://github.com/ultralytics/](https://github.com/ultralytics/yolov5) [yolov5,](https://github.com/ultralytics/yolov5) 2020, accessed: 2023-08-15. [12](#page-11-2)<br>MobileSec.
- <span id="page-18-39"></span>[158] MobileSec, "Mobilesec android authentication framework," [https://github.com/mobilesec/](https://github.com/mobilesec/authentication-framework-module-face) [authentication-framework-module-face,](https://github.com/mobilesec/authentication-framework-module-face) 2022. [12](#page-11-2)
- <span id="page-18-40"></span>[159] N. Technology, "Sentiveillance sdk," [http://www.](http://www.neurotechnology.com/sentiveillance.html) [neurotechnology.com/sentiveillance.html,](http://www.neurotechnology.com/sentiveillance.html) 2022. [12](#page-11-2)
- <span id="page-18-41"></span>[160] M. Inc, "Face++," [http://www.faceplusplus.com/,](http://www.faceplusplus.com/) 2022. [13](#page-12-2)
- <span id="page-18-42"></span>[161] M. Pautov, G. Melnikov, E. Kaziakhmedov, K. Kireev, and A. Petiushko, "On adversarial patches: Real-world attack on arcface-100 face recognition system," in *2019 International Multi-Conference on Engineering, Computer and Information Sciences (SIBIRCON)*, 2019, pp. 0391–0396. [13,](#page-12-2) [16](#page-15-25)
- <span id="page-18-43"></span>[162] J. Deng, J. Guo, N. Xue, and S. Zafeiriou, "Arcface: Additive angular margin loss for deep face recognition," in *CVPR*, June 2019. [13](#page-12-2)
- <span id="page-18-44"></span>[163] F. Schroff, D. Kalenichenko, and J. Philbin, "Facenet: A unified embedding for face recognition and clustering," in *CVPR*, June 2015. [13](#page-12-2)
- <span id="page-18-45"></span>[164] W. Liu, Y. Wen, Z. Yu, M. Li, B. Raj, and L. Song, "Sphereface: Deep hypersphere embedding for face recognition," in *CVPR*, July 2017. [13](#page-12-2)
- <span id="page-18-46"></span>[165] P. Grother and M. Ngan, "Face recognition vendor test ( frvt ) performance of face identification algorithms," *NIST Interagency/Internal Report (NISTIR) - 8009*, 2014. [13](#page-12-2)
- <span id="page-18-47"></span>[166] M. Jaderberg, K. Simonyan, A. Zisserman *et al.*, "Spatial transformer networks," vol. 28, 2015. [13](#page-12-2)
- <span id="page-18-48"></span>[167] Z. Wang, Z. Wang, Y. Zheng, Y.-Y. Chuang, and S. Satoh, "Learning to reduce dual-level discrepancy for infrared-visible person re-identification," in *CVPR*, 2019, pp. 618–626. [13](#page-12-2)
- <span id="page-18-49"></span>[168] E. Ilg, N. Mayer, T. Saikia, M. Keuper, A. Dosovitskiy, and T. Brox, "Flownet 2.0: Evolution of optical flow estimation with deep networks," in *CVPR*, 2017, pp. 2462–2470. [13](#page-12-2)
- <span id="page-18-50"></span>[169] X. Jiang, L. Zhang, M. Xu, T. Zhang, P. Lv, B. Zhou, X. Yang, and Y. Pang, "Attention scaling for crowd counting," in *CVPR*, 2020, pp. 4706–4715. [14](#page-13-6)
- <span id="page-18-51"></span>[170] Y. Wang, W.-L. Chao, D. Garg, B. Hariharan, M. Campbell, and K. Q. Weinberger, "Pseudo-lidar from visual depth estimation:

Bridging the gap in 3d object detection for autonomous driving," in *CVPR*, June 2019. [14](#page-13-6)

- <span id="page-19-0"></span>[171] H. Zhao, X. Qi, X. Shen, J. Shi, and J. Jia, "Icnet for real-time semantic segmentation on high-resolution images," in *ECCV*, 2018, pp. 405–420. [14](#page-13-6)
- <span id="page-19-1"></span>[172] C. Chen, A. Seff, A. Kornhauser, and J. Xiao, "Deepdriving: Learning affordance for direct perception in autonomous driving," in *ICCV*, 2015, pp. 2722–2730. [14](#page-13-6)
- <span id="page-19-2"></span>[173] A. Prakash, K. Chitta, and A. Geiger, "Multi-modal fusion transformer for end-to-end autonomous driving," in *CVPR*, 2021, pp. 7077–7087. [14](#page-13-6)
- <span id="page-19-3"></span>[174] K. Chitta, A. Prakash, and A. Geiger, "Neat: Neural attention fields for end-to-end autonomous driving," in *ICCV*, 2021, pp. 15 793–15 803. [14](#page-13-6)
- <span id="page-19-4"></span>[175] A. Yilmaz, O. Javed, and M. Shah, "Object tracking: A survey," *Acm computing surveys*, vol. 38, no. 4, pp. 13–es, 2006. [14](#page-13-6)
- <span id="page-19-5"></span>[176] M. Z. Hossain, F. Sohel, M. F. Shiratuddin, and H. Laga, "A comprehensive survey of deep learning for image captioning," *ACM Computing Surveys*, vol. 51, no. 6, pp. 1–36, 2019. [14](#page-13-6)
- <span id="page-19-6"></span>[177] R. Tao, Y. Wei, X. Jiang, H. Li, H. Qin, J. Wang, Y. Ma, L. Zhang, and X. Liu, "Towards real-world x-ray security inspection: A high-quality benchmark and lateral inhibition module for prohibited items detection," in *ICCV*, 2021, pp. 10 923–10 932. [14](#page-13-6)
- <span id="page-19-7"></span>[178] M. Otani, R. Togashi, Y. Sawai, R. Ishigami, Y. Nakashima, E. Rahtu, J. Heikkilä, and S. Satoh, "Toward verifiable and reproducible human evaluation for text-to-image generation," in *CVPR*, 2023, pp. 14 277–14 286. [15](#page-14-5)
- <span id="page-19-8"></span>[179] J. Gu, C. Sun, and H. Zhao, "Densetnt: End-to-end trajectory prediction from dense goal sets," in *ICCV*, 2021, pp. 15 303– 15 312. [16](#page-15-25)
- <span id="page-19-9"></span>[180] W. Liu and T. Mei, "Recent advances of monocular 2d and 3d human pose estimation: A deep learning perspective," *ACM Computing Surveys*, 2022. [16](#page-15-25)
- <span id="page-19-10"></span>[181] Z. Wang, Q. She, and A. Smolic, "Action-net: Multipath excitation for action recognition," in *CVPR*, 2021, pp. 13 214–13 223. [16](#page-15-25)
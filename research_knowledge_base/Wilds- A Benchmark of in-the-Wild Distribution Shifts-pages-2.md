## <span id="page-0-1"></span>5. Performance drops from distribution shifts

For a dataset to be appropriate for WILDS, the distribution shift reflected in its official train/test split should cause significant performance drops in standard models. How to measure the performance drop due to a distribution shift is a crucial but subtle question. In this section, we discuss our approach and the results on each of the WILDS datasets. To construct WILDS, we selected datasets with large performance drops; in Section 8, we discuss other datasets with real-world shifts that did not show large performance drops and were therefore not included in the benchmark.

Our general approach is to measure the difference between the out-of-distribution (OOD) and in-distribution (ID) performance of standard models trained via empirical risk minimization (ERM). Concretely, we first measure the OOD performance using the official train/test splits described in Section 4. We then construct an appropriate in-distribution (ID) setting to measure ID performance, typically by modifying the official train/test splits. However, practical constraints often prevent us from constructing an ID setting in exactly the way we want, which makes the choice of appropriate ID setting for each dataset a case-by-case issue.

<span id="page-0-0"></span>

### 5.1 In-distribution performance should be measured on $P<sup>test</sup>$ , not $P<sup>train</sup>$

Choosing an appropriate in-distribution (ID) setting is the crux of measuring how much a distribution shift affects performance. But what distribution should "in-distribution" be taken with respect to? Consider a distribution shift from a training distribution  $P<sup>train</sup>$  to a test distribution  $P<sup>test</sup>$ . It is common to measure ID performance by taking a model trained on  $P<sup>train</sup>$  and evaluating it on additional held-out data from  $P<sup>train.1</sup>$ . This is useful for checking if the model can generalize well on both the training and the shifted test distributions. However, it fails to isolate the effect of the distribution shift since it does not control for the data distribution on which the model is evaluated: the ID setting evaluates on data from  $P<sup>train</sup>$ , whereas the OOD setting evaluates on data from  $P<sup>test</sup>$ . As a result, the performance gap might also be due to other factors such as differences in the difficulty of fitting a model to  $P<sup>train</sup>$  versus  $P<sup>test</sup>$ .

For illustration, consider the task of wheat head detection on GlobalWheat-wilds. The shift from P<sup>train</sup> to P<sup>test</sup>, which contain images of wheat fields in Europe and North America respectively, involves changes in factors such as wheat genotype, illumination, and growing conditions. These changes mean that the task can be more challenging in some regions than others: for example, wheat is grown in higher densities in certain regions than others, and it is harder to detect wheat heads reliably when they are more densely packed together. If, for example, the task is harder in the regions in  $P<sup>test</sup>$ , then we might see especially low performance on  $P<sup>test</sup>$  compared to  $P<sup>train</sup>$ . However, this performance gap would overestimate the actual gap caused by the distribution shift, in the sense that performance on  $P^{\text{test}}$  would still be lower even if we could train a model purely on data from  $P^{\text{test}}$ .

To isolate the gap caused by the distribution shift, it is therefore important to keep the evaluation data distribution fixed between the ID and OOD settings by evaluating on  $P<sup>test</sup>$  in the ID setting. For example, we could measure ID performance by training on  $P<sup>test</sup>$  and evaluating on  $P<sup>test</sup>$  and then compare this with the standard OOD setting of training on  $P<sup>train</sup>$  and evaluating on  $P<sup>test</sup>$ . However, there is a practical drawback: we generally have much more data from  $P<sup>train</sup>$  rather than  $P<sup>test</sup>$ , and training and evaluating on  $P<sup>test</sup>$  would require us to have a substantial number of labeled examples from each test domain. In contrast, the standard ID setting of training and evaluating on  $P<sup>train</sup>$  is typically much more feasible, and it is also more convenient as we can reuse the same model trained on  $P<sup>train</sup>$  for both ID and OOD evaluations.

In WILDS, we take the approach of measuring ID performance on  $P<sup>test</sup>$  whenever practically feasible, and we lean on standard ID evaluations on  $P<sup>train</sup>$  otherwise. In either case, we generally provide held-out data from  $P<sup>train</sup>$  in order to track model performance on  $P<sup>train</sup>$ .

<sup>1.</sup> For example, in domain generalization, we might train a model on the training domains and then report its ID performance on held-out examples from the same domains; and in subpopulation shift, we might report average performance on  $P<sup>train</sup>$  as the ID performance.

### 5.2 Types of in-distribution settings

To measure the performance drop on each WILDS dataset, we picked the most appropriate ID setting(s) that were feasible. We now describe five specific ways of constructing ID settings and their pros and cons. The first two ID settings (test-to-test and mixed-to-test) control for the evaluation distribution and thus isolate the performance drops due to distribution shifts, as discussed in Section [5.1.](#page-0-0) However, these procedures require substantial training data from test domains, so in cases where such data is not practically available, we consider the other ID settings (train-to-train, average, and random split). Appendix E describes dataset-specific rationales for the selected ID settings and additional details for each dataset.

Below, we denote the training and OOD test sets of the official WILDS splits as  $D<sup>train</sup>$  and  $D<sup>test</sup>$ , sampled from distributions  $P<sup>train</sup>$  and  $P<sup>test</sup>$ , respectively.

Test-to-test (train on  $P<sup>test</sup>$ , test on  $P<sup>test</sup>$ ). To control for the evaluation distribution, we can hold the test set  $D^{\text{test}}$  fixed and train on a separate but identically-distributed training set  $D^{\text{test}}_{\text{heldout}}$ drawn from  $P<sup>test</sup>$ . The ID performance reported in this setting is directly comparable to OOD performance, which is also evaluated on  $D<sup>test</sup>$ . The main drawback is that for a fair comparison to the OOD setting, where we train a model on  $D<sup>train</sup>$ , we would require  $D<sup>test</sup>$  to match the size of  $D<sup>train</sup>$ . This is not feasible in our datasets, as  $D<sup>train</sup>$  typically comprises the bulk of the available data. We therefore do not use the test-to-test comparison for any of the WILDS datasets and instead consider the more practical alternative below, which still controls for the evaluation data distribution.

Mixed-to-test (train on a mixture of  $P<sup>train</sup>$  and  $P<sup>test</sup>$ , test on  $P<sup>test</sup>$ ). In the mixed-to-test setting, we train a model on a mixture of data from  $P<sup>train</sup>$  and  $P<sup>test</sup>$  and then evaluate it only on  $P<sup>test</sup>$ . This is a more practical version of the test-to-test setting, as it retains the advantage of controlling for the evaluation distribution, while mitigating the need for large amounts of labeled data from  $P<sup>test</sup>$ to use for training.<sup>2</sup> We use the mixed-to-test comparison for the WILDS datasets wherever feasible, except when we expect the train-to-train comparison to give similar results as described in the below discussion on train-to-train setting (e.g., for IWILDCAM2020-WILDS and PY150-WILDS).

One downside is that compared to the test-to-test setting, the mixed-to-test setting might underestimate ID performance, since it trains a model that simultaneously fits both  $P<sup>train</sup>$  and  $P<sup>test</sup>$ , instead of just focusing on  $P<sup>test</sup>$ . However, this is useful as a sanity check that we can learn a model that can simultaneously fit both  $P<sup>train</sup>$  and  $P<sup>test</sup>$ ; if such a model were not possible to learn, then it suggests that the distribution shift in the dataset is intractable for the model family.

Train-to-train (train on  $P<sup>train</sup>$ , evaluate on  $P<sup>train</sup>$ ). In the train-to-train setting, we train a model on  $D<sup>train</sup>$  and evaluate on a separate but identically-distributed test set  $D<sup>train</sup>$ <sub>heldout</sub> drawn from P train. As discussed in Section [5.1,](#page-0-0) this is practical—it does not require large amounts of data from  $P<sup>test</sup>$ , and we can reuse the model for OOD evaluation—but has the drawback of not controlling for the evaluation distribution.

This drawback is less of an issue when we expect  $D<sup>train</sup>$  and  $D<sup>test</sup>$  to be of equal difficulty in the sense of Section [5.1.](#page-0-0) This may be the case when the dataset has a relatively large number of training and test domains that are drawn from the same distribution, and they are thus roughly interchangeable. For instance, in IWILDCAM2020-WILDS and PY150-WILDS, there are many available domains (camera traps and GitHub repositories, respectively) randomly split across  $D<sup>train</sup>$  and  $D<sup>test</sup>$ , so we use the train-to-train comparison for them. For most of the other datasets, we also include train-to-train comparisons to track model performance on  $P<sup>train</sup>$  (i.e., the official splits typically also include a held-out  $D_{\text{heldout}}^{\text{train}}$ ; we report results on these in Appendix E), but we complement them whenever feasible with other ID settings that better isolate the effect of the distribution shift.

<sup>2.</sup> In practice, we typically split up  $D<sup>test</sup>$  and use some of it for training by replacing examples in  $D<sup>train</sup>$  (so that the size of the training set is similar to the OOD setting). This still requires D<sup>test</sup> to be large enough to support using a sufficient number of examples for training while also having enough examples left over for accurate evaluation.

Average (report average instead of worst-case performance). In subpopulation shift datasets, we measure the OOD performance of a model by reporting the performance on the worst-case subpopulation, and we can measure ID performance by simply reporting the average performance. This average comparison corresponds to a special case of the train-to-train setting,<sup>3</sup> so they share the same pros and cons. In particular, the average comparison is much more practical than running a test-to-test comparison on each subpopulation, as it can be especially difficult to obtain sufficient training examples from minority subpopulations. In Table [1,](#page-3-0) we use this average comparison for the CivilComments-wilds and Amazon-wilds datasets, which both consider a large number of subpopulations that are individually quite small.

Random split (train and evaluate on an i.i.d. split). Another standard approach to measuring ID performance is to shuffle all of the data in  $D<sup>train</sup> \cup D<sup>test</sup>$  into i.i.d. training, validation, and test splits, while keeping the size of the training set constant. We use this in OGB-MOLPCBA to be consistent with prior work from the Open Graph Benchmark (Hu et al., 2020b). As with the train-to-train comparison, the random split comparison is simple to implement and does not require large amounts of data from  $D<sup>test</sup>$ , but it does not control for the evaluation distribution.

### 5.3 Model selection

We used standard model architectures for each dataset: ResNet and DenseNet for images (He et al., 2016; Huang et al., 2017), DistilBERT for text (Sanh et al., 2019), a Graph Isomorphism Network (GIN) for graphs (Xu et al., 2018), and Faster-RCNN (Ren et al., 2015) for detection. As our goal is high OOD performance, we use a separate OOD validation set for early stopping and hyperparameter selection.4 Relative to the training set, this OOD validation set reflects a distribution shift similar to, but distinct from, the test set. For example, in IWILDCAM2020-WILDS, the training, validation, and test sets each comprise photos from distinct sets of camera traps. We detail experimental protocol in Appendix D and models and hyperparameters for each dataset in Appendix E.

For the ID comparisons, we use the same hyperparameters optimized on the OOD validation set, so our ID results are slightly lower than if we had optimized hyperparameters for ID performance (Appendix D). In other words, the ID-OOD gaps in Table [1](#page-3-0) are slightly underestimated.

### 5.4 Results

Table [1](#page-3-0) shows that for each dataset, OOD performance is consistently and substantially lower than the corresponding ID performance. Moreover, on the datasets that allow for mixed-to-test ID comparisons, we show that models trained on a mix of the ID and OOD distributions can simultaneously achieve high ID and OOD performance, indicating that lower OOD performance is not due to the OOD test sets being intrinsically more difficult than the ID test sets. Overall, these results demonstrate that the real-world distribution shifts reflected in the WILDS datasets meaningfully degrade standard model performance. Additional results for datasets that admit multiple ID comparisons are described for each dataset in Appendix E.

# 6. Baseline algorithms for distribution shifts

Many algorithms have been proposed for training models that are more robust to particular distribution shifts than standard models trained by empirical risk minimization (ERM), which trains models to minimize the average training loss. Unlike ERM, these algorithms tend to utilize domain annotations during training, with the goal of learning a model that can generalize across domains.

<sup>3.</sup> In subpopulation shifts, the training distribution reflects the empirical make-up over the pre-defined subpopulations, whereas the test distribution of interest corresponds to the worst-case subpopulation.

<sup>4.</sup> This means that while the ERM models do not make use of any additional metadata (e.g., domain annotations) during training, this metadata is still implicitly (but very mildly) used for model selection.

<span id="page-3-0"></span>Table 1: The in-distribution (ID) vs. out-of-distribution (OOD) performance of models trained with empirical risk minimization. The OOD test sets are drawn from the shifted test distributions described in Section 4, while the ID comparisons vary per dataset and are described in Section [5.1.](#page-0-0) For each dataset, higher numbers are better. In all tables in this paper, we report in parentheses the standard deviation across 3+ replicates, which measures the variability between replicates; note that this is higher than the standard error of the mean, which measures the variability in the estimate of the mean across replicates. All datasets show performance drops due to distribution shift, with substantially better ID performance than OOD performance.

| Dataset             | Metric              | In-dist setting | In-dist     | Out-of-dist | Gap  |
|---------------------|---------------------|-----------------|-------------|-------------|------|
| iWildCAM2020-WILDS  | Macro F1            | Train-to-train  | 47.0 (1.4)  | 31.0 (1.3)  | 16.0 |
| CAMELYON17-WILDS    | Average acc         | Train-to-train  | 93.2 (5.2)  | 70.3 (6.4)  | 22.9 |
| RxRx1-WILDS         | Average acc         | Mixed-to-test   | 39.8 (0.2)  | 29.9 (0.4)  | 9.9  |
| OGB-MOLPCBA         | Average AP          | Random split    | 34.4 (0.9)  | 27.2 (0.3)  | 7.2  |
| GLOBALWHEAT-WILDS   | Average domain acc  | Mixed-to-test   | 63.3 (1.7)  | 49.6 (1.9)  | 13.7 |
| CIVILCOMMENTS-WILDS | Worst-group acc     | Average         | 92.2 (0.1)  | 56.0 (3.6)  | 36.2 |
| FMoW-WILDS          | Worst-region acc    | Mixed-to-test   | 48.6 (0.9)  | 32.3 (1.3)  | 16.3 |
| POVERTYMAP-WILDS    | Worst-U/R Pearson R | Mixed-to-test   | 0.60 (0.06) | 0.45 (0.06) | 0.15 |
| AMAZON-WILDS        | 10th percentile acc | Average         | 71.9 (0.1)  | 53.8 (0.8)  | 18.1 |
| PY150-WILDS         | Method/class acc    | Train-to-train  | 75.4 (0.4)  | 67.9 (0.1)  | 7.5  |

Table 2: The out-of-distribution test performance of models trained with different baseline algorithms: CORAL, originally designed for unsupervised domain adaptation; IRM, for domain generalization; and Group DRO, for subpopulation shifts. Evaluation metrics for each dataset are the same as in Table [1;](#page-3-0) higher is better. Overall, these algorithms did not improve over empirical risk minimization (ERM), and sometimes made performance significantly worse, except on CivilComments-wilds where they perform better but still do not close the in-distribution gap in Table [1.](#page-3-0) For GlobalWheat-wilds, we omit CORAL and IRM as those methods do not port straightforwardly to detection settings; its ERM number also differs from Table [1](#page-3-0) as its ID comparison required a slight change to the OOD test set. Parentheses show standard deviation across 3+ replicates.

| Dataset             | Setting       | ERM               | CORAL             | IRM         | Group DRO         |
|---------------------|---------------|-------------------|-------------------|-------------|-------------------|
| iWildCAM2020-WILDS  | Domain gen.   | 31.0 (1.3)        | <b>32.8 (0.1)</b> | 15.1 (4.9)  | 23.9 (2.1)        |
| CAMELYON17-WILDS    | Domain gen.   | <b>70.3 (6.4)</b> | 59.5 (7.7)        | 64.2 (8.1)  | 68.4 (7.3)        |
| RxRx1-WILDS         | Domain gen.   | <b>29.9 (0.4)</b> | 28.4 (0.3)        | 8.2 (1.1)   | 23.0 (0.3)        |
| OGB-MoLPCBA         | Domain gen.   | <b>27.2 (0.3)</b> | 17.9 (0.5)        | 15.6 (0.3)  | 22.4 (0.6)        |
| GLOBALWHEAT-WILDS   | Domain gen.   | <b>51.2 (1.8)</b> | —                 | —           | 47.9 (2.0)        |
| CIVILCOMMENTS-WILDS | Subpop. shift | 56.0 (3.6)        | 65.6 (1.3)        | 66.3 (2.1)  | <b>70.0 (2.0)</b> |
| FMoW-WILDS          | Hybrid        | <b>32.3 (1.3)</b> | 31.7 (1.2)        | 30.0 (1.4)  | 30.8 (0.8)        |
| PovertyMAP-WILDS    | Hybrid        | 0.45 (0.06)       | 0.44 (0.06)       | 0.43 (0.07) | 0.39 (0.06)       |
| AMAZON-WILDS        | Hybrid        | <b>53.8 (0.8)</b> | 52.9 (0.8)        | 52.4 (0.8)  | 53.3 (0.0)        |
| Py150-WILDS         | Hybrid        | <b>67.9 (0.1)</b> | 65.9 (0.1)        | 64.3 (0.2)  | 65.9 (0.1)        |

In this section, we evaluate several representative algorithms from prior work and show that the out-of-distribution performance drops shown in Section [5](#page-0-1) still remain.

### 6.1 Domain generalization baselines

Methods for domain generalization typically involve adding a penalty to the ERM objective that encourages some form of invariance across domains. We include two such methods as representatives:

- **CORAL** (Sun and Saenko, 2016), which penalizes differences in the means and covariances of the feature distributions (i.e., the distribution of last layer activations in a neural network) for each domain. Conceptually, CORAL is similar to other methods that encourage feature representations to have the same distribution across domains (Tzeng et al., 2014; Long et al., 2015; Ganin et al., 2016; Li et al., 2018c,b).
- **IRM** (Arjovsky et al., 2019), which penalizes feature distributions that have different optimal linear classifiers for each domain. This builds on earlier work on invariant predictors (Peters et al., 2016).

Other techniques for domain generalization include conditional variance regularization (Heinze-Deml and Meinshausen, 2017); self-supervision (Carlucci et al., 2019); and meta-learning-based approaches (Li et al., 2018a; Balaji et al., 2018; Dou et al., 2019).

### 6.2 Subpopulation shift baselines

In subpopulation shift settings, our aim is to train models that perform well on all relevant subpopulations. We test the following approach:

• Group DRO (Hu et al., 2018; Sagawa et al., 2020a), which uses distributionally robust optimization to explicitly minimize the loss on the worst-case domain during training. Group DRO builds on the maximin approach developed in Meinshausen and Bühlmann (2015).

Other methods for subpopulation shifts include reweighting methods based on class/domain frequencies (Shimodaira, 2000; Cui et al., 2019); label-distribution-aware margin losses (Cao et al., 2019); adaptive Lipschitz regularization (Cao et al., 2020); slice-based learning (Chen et al., 2019b; Ré et al., 2019); style transfer across domains (Goel et al., 2020); or other DRO algorithms that do not make use of explicit domain information and rely on, for example, unsupervised clustering (Oren et al., 2019; Sohoni et al., 2020) or upweighting high-loss points (Nam et al., 2020; Liu et al., 2021a).

Subpopulation shifts are also connected to the well-studied notions of tail performance and risk-averse optimization (Chapter 6 in Shapiro et al. (2014)). For example, optimizing for the worst case over all subpopulations of a certain size, regardless of domain, can guarantee a certain level of performance over the smaller set of subpopulations defined by domains (Duchi et al., 2020; Duchi and Namkoong, 2021).

#### 6.3 Setup

We trained CORAL, IRM, and Group DRO models on each dataset. While Group DRO was originally developed for subpopulation shifts, for completeness, we also experiment with using it for domain generalization. In that setting, Group DRO models aim to achieve similar performance across domains: e.g., in CAMELYON17-WILDS, where the domains are hospitals, Group DRO optimizes for the training hospital with the highest loss. Similarly, we also test CORAL and IRM on subpopulation shifts, where they encourage models to learn invariant representations across subpopulations. As in Section [5,](#page-0-1) we used the same OOD validation set for early stopping and to tune the penalty weights for the CORAL and IRM algorithms. More experimental details are in Appendix D, and dataset-specific hyperparameters and domain choices are discussed in Appendix E.
# **11** *Mixture models and the EM algorithm*

## **11.1 Latent variable models**

In Chapter 10 we showed how graphical models can be used to define high-dimensional joint probability distributions. The basic idea is to model dependence between two variables by adding an edge between them in the graph. (Technically the graph represents conditional independence, but you get the point.)

An alternative approach is to assume that the observed variables are correlated because they arise from a hidden common "cause". Model with hidden variables are also known as **latent variable models** or **LVM**s. As we will see in this chapter, such models are harder to fit than models with no latent variables. However, they can have significant advantages, for two main reasons. First, LVMs often have fewer parameters than models that directly represent correlation in the visible space. This is illustrated in Figure 11.1. If all nodes (including H) are binary and all CPDs are tabular, the model on the left has 17 free parameters, whereas the model on the right has 59 free parameters.

Second, the hidden variables in an LVM can serve as a **bottleneck**, which computes a compressed representation of the data. This forms the basis of unsupervised learning, as we will see. Figure 11.2 illustrates some generic LVM structures that can be used for this purpose. In general there are L latent variables,  $z_{i1},...,z_{IL}$ , and D visible variables,  $x_{i1},...,x_{iD}$ , where usually  $D \gg L$ . If we have  $L > 1$ , there are many latent factors contributing to each observation, so we have a many-to-many mapping. If  $L = 1$ , we we only have a single latent variable; in this case,  $z_i$  is usually discrete, and we have a one-to-many mapping. We can also have a many-to-one mapping, representing different competing factors or causes for each observed variable; such models form the basis of probabilistic matrix factorization, discussed in Section 27.6.2. Finally, we can have a one-to-one mapping, which can be represented as  $\mathbf{z}_i \to \mathbf{x}_i$ . By allowing  $\mathbf{z}_i$  and/or  $\mathbf{x}_i$  to be vector-valued, this representation can subsume all the others. Depending on the form of the likelihood  $p(\mathbf{x}_i|\mathbf{z}_i)$  and the prior  $p(\mathbf{z}_i)$ , we can generate a variety of different models, as summarized in Table 11.1.

## **11.2 Mixture models**

The simplest form of LVM is when  $z_i \in \{1,\ldots,K\}$ , representing a discrete latent state. We will use a discrete prior for this,  $p(z_i) = \text{Cat}(\boldsymbol{\pi})$ . For the likelihood, we use  $p(\mathbf{x}_i|z_i = k) = p_k(\mathbf{x}_i)$ ,

Image /page/1/Figure/1 description: The image displays two diagrams illustrating neural network architectures. The diagram on the left shows a network with four input nodes, one hidden node labeled 'H', and three output nodes. Arrows indicate that all four input nodes feed into the hidden node, and the hidden node feeds into the three output nodes. Below this diagram, it states "17 parameters". The diagram on the right shows a network with four input nodes, one hidden node, and three output nodes. In this network, all four input nodes are connected to the single hidden node, and the hidden node is connected to all three output nodes. Additionally, there are cross-connections from the input nodes to the output nodes. Below this diagram, it states "59 parameters".

Figure 11.1 A DGM with and without hidden variables. The leaves represent medical symptoms. The roots represent primary causes, such as smoking, diet and exercise. The hidden variable can represent mediating factors, such as heart disease, which might not be directly visible.

Image /page/1/Figure/3 description: The image displays four different causal diagrams labeled (a), (b), (c), and (d). Diagram (a) shows L variables, zi1 through ziL, each with an arrow pointing to D variables, xi1 through xiD, with two arrows crossing between zi1 and xiD, and ziL and xi1. Diagram (b) shows a single variable zi with arrows pointing to D variables, xi1 through xiD. Diagram (c) shows L variables, zi1 through ziL, with arrows pointing to a single variable xi. Diagram (d) shows a single variable zi with an arrow pointing to a single variable xi.

**Figure 11.2** A latent variable model represented as a DGM. (a) Many-to-many. (b) One-to-many. (c) Many-to-one. (d) One-to-one.

where  $p_k$  is the k'th **base distribution** for the observations; this can be of any type. The overall model is known as a **mixture model**, since we are mixing together the K base distributions as follows:

$$
p(\mathbf{x}_i|\boldsymbol{\theta}) = \sum_{k=1}^K \pi_k p_k(\mathbf{x}_i|\boldsymbol{\theta})
$$
\n(II.1)

This is a **convex combination** of the  $p_k$ 's, since we are taking a weighted sum, where the **mixing weights**  $\pi_k$  satisfy  $0 \le \pi_k \le 1$  and  $\sum_{k=1}^K \pi_k = 1$ . We give some examples below.

| $p(\mathbf{x}_i \mathbf{z}_i)$ | $p(\mathbf{z}_i)$ | Name                               | Section |
|--------------------------------|-------------------|------------------------------------|---------|
| MVN                            | Discrete          | Mixture of Gaussians               | 11.2.1  |
| Prod. Discrete                 | Discrete          | Mixture of multinomials            | 11.2.2  |
| Prod. Gaussian                 | Prod. Gaussian    | Factor analysis/ probabilistic PCA | 12.1.5  |
| Prod. Gaussian                 | Prod. Laplace     | Probabilistic ICA/ sparse coding   | 12.6    |
| Prod. Discrete                 | Prod. Gaussian    | Multinomial PCA                    | 27.2.3  |
| Prod. Discrete                 | Dirichlet         | Latent Dirichlet allocation        | 27.3    |
| Prod. Noisy-OR                 | Prod. Bernoulli   | BN20/ QMR                          | 10.2.3  |
| Prod. Bernoulli                | Prod. Bernoulli   | Sigmoid belief net                 | 27.7    |

Table 11.1 Summary of some popular directed latent variable models. Here "Prod" means product, so "Prod. Discrete" in the likelihood means a factored distribution of the form  $\prod_j \text{Cat}(x_{ij}|\mathbf{z}_i)$ , and "Prod."<br>Gaussian" means a factored distribution of the form  $\Pi$ ,  $\mathcal{N}(x_{ij}|\mathbf{z})$ , "PCA" stands for "principal co Gaussian" means a factored distribution of the form  $\prod_j \mathcal{N}(x_{ij}|\mathbf{z}_i)$ . "PCA" stands for "principal components<br>analysis", "ICA" stands for "indepedendent components analysis" analysis". "ICA" stands for "indepedendent components analysis".

Image /page/2/Figure/3 description: The image displays two subfigures, labeled (a) and (b). Subfigure (a) is a 2D contour plot showing three overlapping ellipses in red, green, and blue. The x-axis ranges from 0.1 to 0.9, and the y-axis ranges from 0.2 to 0.8. Subfigure (b) is a 3D surface plot with a brown color, depicting a landscape with multiple peaks and valleys.

**Figure 11.3** A mixture of 3 Gaussians in 2d. (a) We show the contours of constant probability for each component in the mixture. (b) A surface plot of the overall density. Based on Figure 2.23 of (Bishop 2006a). Figure generated by mixGaussPlotDemo.

## **11.2.1 Mixtures of Gaussians**

The most widely used mixture model is the **mixture of Gaussians** (MOG), also called a **Gaussian mixture model** or **GMM**. In this model, each base distribution in the mixture is a multivariate Gaussian with mean  $\mu_k$  and covariance matrix  $\Sigma_k$ . Thus the model has the form

$$
p(\mathbf{x}_i|\boldsymbol{\theta}) = \sum_{k=1}^K \pi_k \mathcal{N}(\mathbf{x}_i|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)
$$
\n(11.2)

Figure 11.3 shows a mixture of 3 Gaussians in 2D. Each mixture component is represented by a different set of eliptical contours. Given a sufficiently large number of mixture components, a GMM can be used to approximate any density defined on  $\mathbb{R}^D$ .

## **11.2.2 Mixture of multinoullis**

We can use mixture models to define density models on many kinds of data. For example, suppose our data consist of  $D$ -dimensional bit vectors. In this case, an appropriate classconditional density is a product of Bernoullis:

$$
p(\mathbf{x}_i | z_i = k, \boldsymbol{\theta}) = \prod_{j=1}^{D} \text{Ber}(x_{ij} | \mu_{jk}) = \prod_{j=1}^{D} \mu_{jk}^{x_{ij}} (1 - \mu_{jk})^{1 - x_{ij}}
$$
(11.3)

where  $\mu_{ik}$  is the probability that bit j turns on in cluster k.

The latent variables do not have to any meaning, we might simply introduce latent variables in order to make the model more powerful. For example, one can show (Exercise 11.8) that the mean and covariance of the mixture distribution are given by

$$
\mathbb{E}\left[\mathbf{x}\right] = \sum_{k} \pi_{k} \boldsymbol{\mu}_{k} \tag{11.4}
$$

cov 
$$
[\mathbf{x}] = \sum_{k} \pi_k [\mathbf{\Sigma}_k + \boldsymbol{\mu}_k \boldsymbol{\mu}_k^T] - \mathbb{E} [\mathbf{x}] \mathbb{E} [\mathbf{x}]^T
$$
 (11.5)

where  $\Sigma_k = \text{diag}(\mu_{ik}(1 - \mu_{ik}))$ . So although the component distributions are factorized, the joint distribution is not. Thus the mixture distribution can capture correlations between variables, unlike a single product-of-Bernoullis model.

### **11.2.3 Using mixture models for clustering**

There are two main applications of mixture models. The first is to use them as a **black-box** density model,  $p(\mathbf{x}_i)$ . This can be useful for a variety of tasks, such as data compression, outlier detection, and creating generative classifiers, where we model each class-conditional density  $p(\mathbf{x}|y = c)$  by a mixture distribution (see Section 14.7.3).

The second, and more common, application of mixture models is to use them for clustering. We discuss this topic in detail in Chapter 25, but the basic idea is simple. We first fit the mixture model, and then compute  $p(z_i = k|\mathbf{x}_i, \boldsymbol{\theta})$ , which represents the posterior probability that point i belongs to cluster k. This is known as the **responsibility** of cluster k for point i, and can be computed using Bayes rule as follows:

$$
r_{ik} \triangleq p(z_i = k | \mathbf{x}_i, \boldsymbol{\theta}) = \frac{p(z_i = k | \boldsymbol{\theta}) p(\mathbf{x}_i | z_i = k, \boldsymbol{\theta})}{\sum_{k'=1}^{K} p(z_i = k' | \boldsymbol{\theta}) p(\mathbf{x}_i | z_i = k', \boldsymbol{\theta})}
$$
(11.6)

This procedure is called **soft clustering**, and is identical to the computations performed when using a generative classifier. The difference between the two models only arises at training time: in the mixture case, we never observe  $z_i$ , whereas with a generative classifier, we do observe  $y_i$ (which plays the role of  $z_i$ ).

We can represent the amount of uncertainty in the cluster assignment by using  $1-\max_k r_{ik}$ . Assuming this is small, it may be reasonable to compute a **hard clustering** using the MAP estimate, given by

$$
z_i^* = \arg\max_k r_{ik} = \arg\max_k \log p(\mathbf{x}_i | z_i = k, \boldsymbol{\theta}) + \log p(\mathbf{z}_i = k | \boldsymbol{\theta})
$$
(11.7)

Image /page/4/Figure/1 description: The image displays two plots side-by-side. Plot (a), titled 'yeast microarray data', shows a scatter plot with multiple colored lines representing gene expression levels over time. The x-axis is labeled 'time' and ranges from 0 to 20.5, with tick marks at 0, 9.5, 11.5, 13.5, 15.5, 18.5, and 20.5. The y-axis is labeled 'genes' and ranges from -5 to 5, with tick marks at -5, -4, -3, -2, -1, 0, 1, 2, 4, and 5. Plot (b), titled 'K-Means centroids', presents a 3x4 grid of smaller plots, each showing a distinct blue line graph representing a centroid pattern. These patterns vary, with some showing a general decrease, some an increase, and others more complex fluctuations over time.

**Figure 11.4** (a) Some yeast gene expression data plotted as a time series. (c) Visualizing the 16 cluster centers produced by K-means. Figure generated by kmeansYeastDemo.

Image /page/4/Figure/3 description: The image displays a grid of ten blurry grayscale images of handwritten digits, arranged in two rows of five. Each digit has a small number above it, likely representing a probability or confidence score. The top row shows the digits 1, 9, 4, 0, and 3, with scores 0.12, 0.14, 0.12, 0.06, and 0.13 respectively. The bottom row shows the digits 2, 5, 1, 0, and 6, with scores 0.07, 0.05, 0.15, 0.07, and 0.09 respectively.

**Figure 11.5** We fit a mixture of 10 Bernoullis to the binarized MNIST digit data. We show the MLE for the corresponding cluster means,  $\mu_k$ . The numbers on top of each image represent the mixing weights  $\hat{\pi}_k$ . No labels were used when training the model. Figure generated by mixBerMnistEM.

Hard clustering using a GMM is illustrated in Figure 1.8, where we cluster some data representing the height and weight of people. The colors represent the hard assignments. Note that the identity of the labels (colors) used is immaterial; we are free to rename all the clusters, without affecting the partitioning of the data; this is called **label switching**.

Another example is shown in Figure 11.4. Here the data vectors  $x_i \in \mathbb{R}^7$  represent the expression levels of different genes at 7 different time points. We clustered them using a GMM. We see that there are several kinds of genes, such as those whose expression level goes up monotonically over time (in response to a given stimulus), those whose expression level goes down monotonically, and those with more complex response patterns. We have clustered the series into  $K = 16$  groups. (See Section 11.5 for details on how to choose K.) For example, we can represent each cluster by a **prototype** or **centroid**. This is shown in Figure 11.4(b).

As an example of clustering binary data, consider a binarized version of the MNIST handwritten digit dataset (see Figure 1.5(a)), where we ignore the class labels. We can fit a mixture of Bernoullis to this, using  $K = 10$ , and then visualize the resulting centroids,  $\hat{\mu}_k$ , as shown in Figure 11.5. We see that the method correctly discovered some of the digit classes, but overall the results aren't great: it has created multiple clusters for some digits, and no clusters for others. There are several possible reasons for these "errors":

- The model is very simple and does not capture the relevant visual characteristics of a digit. For example, each pixel is treated independently, and there is no notion of shape or a stroke.
- Although we think there should be 10 clusters, some of the digits actually exhibit a fair degree of visual variety. For example, there are two ways of writing 7's (with and without the cross bar). Figure 1.5(a) illustrates some of the range in writing styles. Thus we need  $K \gg 10$ clusters to adequately model this data. However, if we set  $K$  to be large, there is nothing in the model or algorithm preventing the extra clusters from being used to create multiple versions of the same digit, and indeed this is what happens. We can use model selection to prevent too many clusters from being chosen but what looks visually appealing and what makes a good density estimator may be quite different.
- The likelihood function is not convex, so we may be stuck in a local optimum, as we explain in Section 11.3.2.

This example is typical of mixture modeling, and goes to show one must be very cautious trying to "interpret" any clusters that are discovered by the method. (Adding a little bit of supervision, or using informative priors, can help a lot.)

## **11.2.4 Mixtures of experts**

Section 14.7.3 described how to use mixture models in the context of generative classifiers. We can also use them to create discriminative models for classification and regression. For example, consider the data in Figure 11.6(a). It seems like a good model would be three different linear regression functions, each applying to a different part of the input space. We can model this by allowing the mixing weights and the mixture densities to be input-dependent:

$$
p(y_i|\mathbf{x}_i, z_i = k, \boldsymbol{\theta}) = \mathcal{N}(y_i|\mathbf{w}_k^T \mathbf{x}_i, \sigma_k^2)
$$
(11.8)

$$
p(z_i|\mathbf{x}_i,\boldsymbol{\theta}) = \text{Cat}(z_i|\mathcal{S}(\mathbf{V}^T\mathbf{x}_i))
$$
\n(11.9)

See Figure 11.7(a) for the DGM.

This model is called a **mixture of experts** or MoE (Jordan and Jacobs 1994). The idea is that each submodel is considered to be an "expert" in a certain region of input space. The function  $p(z_i = k|\mathbf{x}_i, \boldsymbol{\theta})$  is called a **gating function**, and decides which expert to use, depending on the input values. For example, Figure 11.6(b) shows how the three experts have "carved up" the 1d input space, Figure 11.6(a) shows the predictions of each expert individually (in this case, the experts are just linear regression models), and Figure 11.6(c) shows the overall prediction of the model, obtained using

$$
p(y_i|\mathbf{x}_i,\boldsymbol{\theta}) = \sum_k p(z_i = k|\mathbf{x}_i,\boldsymbol{\theta}) p(y_i|\mathbf{x}_i, z_i = k, \boldsymbol{\theta})
$$
\n(11.10)

We discuss how to fit this model in Section 11.4.3.

Image /page/6/Figure/1 description: This image contains three plots labeled (a), (b), and (c). Plot (a) is titled "expert predictions, fixed mixing weights=0" and shows data points scattered around three lines: a horizontal blue line at y=1, a dotted red line with a slight upward slope, and a dashed black line with a slight upward slope. Plot (b) is titled "gating functions, fixed mixing weights=0" and displays three curves: a dotted red curve that starts at 1, decreases to 0 around x=-0.25, and stays at 0; a solid blue curve that starts at 0, increases to 1 around x=-0.25, stays at 1 until x=0.25, and then decreases to 0; and a dashed black curve that starts at 0, increases to 1 around x=0.25, and stays at 1. Plot (c) is titled "predicted mean and var, fixed mixing weights=0" and shows data points with error bars scattered around a red curve that resembles a step function, being low for x < -0.5, high for x > 0.5, and transitioning between -1 and 1 in the range of -0.5 to 0.5. The x-axis for all plots ranges from -1.5 to 1, and the y-axis for plots (a) and (c) ranges from -1.5 to 1.5, while the y-axis for plot (b) ranges from 0 to 1.

Figure 11.6 (a) Some data fit with three separate regression lines. (b) Gating functions for three different "experts". (c) The conditionally weighted average of the three expert predictions. Figure generated by mixexpDemo.

Image /page/6/Figure/3 description: The image displays two probabilistic graphical models, labeled (a) and (b). Model (a) shows a directed acyclic graph with three nodes: a shaded node labeled 'xi', a white node labeled 'zi', and a shaded node labeled 'yi'. There are directed edges from 'xi' to 'zi' and from 'zi' to 'yi'. Additionally, there is a curved edge from 'xi' to 'yi'. Model (b) shows a similar structure but with an additional intermediate node. It has a shaded node labeled 'xi', two white nodes labeled 'zi^1' and 'zi^2' in sequence, and a shaded node labeled 'yi'. Directed edges connect 'xi' to 'zi^1', 'zi^1' to 'zi^2', and 'zi^2' to 'yi'. Curved edges connect 'xi' to 'zi^1' and 'xi' to 'yi'.

Figure 11.7 (a) A mixture of experts. (b) A hierarchical mixture of experts.

Image /page/7/Figure/1 description: This image contains three scatter plots labeled (a), (b), and (c). Plot (a), titled "forwards problem", shows a set of blue circles scattered across a graph with x-axis values from 0 to 1 and y-axis values from -0.2 to 1.2. The data points generally follow an upward curve. Plot (b), titled "expert predictions", displays a more complex pattern with several sets of data points. There are blue diamonds forming a curve, green diamonds forming a line, red diamonds forming a line, and purple diamonds forming a curve. Three colored lines (blue, red, and green) are also plotted. The x-axis ranges from -0.2 to 1.2, and the y-axis ranges from -0.2 to 1.2. Plot (c), titled "prediction", shows blue circles, red asterisks labeled "mean", and black squares labeled "mode". The blue circles form a curved pattern similar to plot (a). The red asterisks and black squares are plotted along two distinct linear paths. The x-axis ranges from -0.2 to 1.2, and the y-axis ranges from 0 to 1.

**Figure 11.8** (a) Some data from a simple forwards model. (b) Some data from the inverse model, fit with a mixture of 3 linear regressions. Training points are color coded by their responsibilities. (c) The predictive mean (red cross) and mode (black square). Based on Figures 5.20 and 5.21 of (Bishop 2006b). Figure generated by mixexpDemoOneToMany.

It should be clear that we can "plug in" any model for the expert. For example, we can use neural networks (Chapter 16) to represent both the gating functions and the experts. The result is known as a **mixture density network**. Such models are slower to train, but can be more flexible than mixtures of experts. See (Bishop 1994) for details.

It is also possible to make each expert be itself a mixture of experts. This gives rise to a model known as the **hierarchical mixture of experts**. See Figure 11.7(b) for the DGM, and Section 16.2.6 for further details.

### **11.2.4.1 Application to inverse problems**

Mixtures of experts are useful in solving **inverse problems**. These are problems where we have to invert a many-to-one mapping. A typical example is in robotics, where the location of the end effector (hand) **y** is uniquely determined by the joint angles of the motors, **x**. However, for any given location **y**, there are many settings of the joints **x** that can produce it. Thus the inverse mapping  $\mathbf{x} = f^{-1}(\mathbf{y})$  is not unique. Another example is **kinematic tracking** of people from video (Bo et al. 2008), where the mapping from image appearance to pose is not unique, due to self occlusion, etc.

Image /page/8/Figure/1 description: The image displays two probabilistic graphical models, labeled (a) and (b). Model (a) shows a hexagonal structure where a node labeled \(\\theta\_{z}\\) connects to nodes \(z\_{1}\\) and \(z\_{N}\\) via directed edges. Nodes \(z\_{1}\\) and \(z\_{N}\\) are connected to observed nodes \(x\_{1}\\) and \(x\_{N}\\) respectively. There are also nodes \(x\_{1}\\) and \(x\_{N}\\) connected to a node labeled \(\\theta\_{x}\\) via directed edges. The ellipsis \(...\) indicates a continuation of this pattern for \(N\) elements. Model (b) uses plate notation to represent a similar structure more compactly. A node \(\\theta\_{z}\\) connects to a node \(z\_{i}\\) which in turn connects to an observed node \(x\_{i}\\) via directed edges. The node \(x\_{i}\\) is also connected to a node \(\\theta\_{x}\\) via a directed edge. The entire \(z\_{i}\\) and \(x\_{i}\\) structure is enclosed within a plate labeled \(N\), indicating that this structure is repeated \(N\) times independently.

Figure 11.9 A LVM represented as a DGM. Left: Model is unrolled for N examples. Right: same model using plate notation.

A simpler example, for illustration purposes, is shown in Figure 11.8(a). We see that this defines a function,  $y = f(x)$ , since for every value x along the horizontal axis, there is a unique response y. This is sometimes called the **forwards model**. Now consider the problem of computing  $x = f^{-1}(y)$ . The corresponding inverse model is shown in Figure 11.8(b); this is obtained by simply interchanging the  $x$  and  $y$  axes. Now we see that for some values along the horizontal axis, there are multiple possible outputs, so the inverse is not uniquely defined. For example, if  $y = 0.8$ , then x could be 0.2 or 0.8. Consequently, the predictive distribution,  $p(x|y, \theta)$  is multimodal.

We can fit a mixture of linear experts to this data. Figure 11.8(b) shows the prediction of each expert, and Figure 11.8(c) shows (a plugin approximation to) the posterior predictive mode and mean. Note that the posterior mean does not yield good predictions. In fact, any model which is trained to minimize mean squared error — even if the model is a flexible nonlinear model, such as neural network — will work poorly on inverse problems such as this. However, the posterior mode, where the mode is input dependent, provides a reasonable approximation.

## **11.3 Parameter estimation for mixture models**

We have seen how to compute the posterior over the hidden variables given the observed variables, assuming the parameters are known. In this section, we discuss how to learn the parameters.

In Section 10.4.2, we showed that when we have complete data and a factored prior, the posterior over the parameters also factorizes, making computation very simple. Unfortunately this is no longer true if we have hidden variables and/or missing data. The reason is apparent from looking at Figure 11.9. If the  $z_i$  were observed, then by d-separation, we see that  $\theta_z \perp \theta_x | \mathcal{D}$ , and hence the posterior will factorize. But since, in an LVM, the  $z_i$  are hidden, the parameters are no longer independent, and the posterior does not factorize, making it much harder to

Image /page/9/Figure/1 description: The image displays two plots side-by-side. Plot (a) is a histogram with the x-axis labeled from -25 to 25 in increments of 5, and the y-axis labeled from 0 to 35 in increments of 5. The histogram consists of several blue bars, with heights approximately reaching 7, 19, 33, 31, 11, 29, 27, and 12 at x-values around -22, -17, -12, -7, -2, 3, 8, 13, and 18 respectively. Plot (b) is a contour plot with the x-axis labeled as 'μ1' from -15.5 to 19.5 in increments of 5, and the y-axis labeled as 'μ2' from -15.5 to 19.5 in increments of 5. The contour plot shows several closed contours, forming four distinct circular or elliptical shapes, with some areas exhibiting a grid-like pattern of contours.

**Figure 11.10** Left:  $N = 200$  data points sampled from a mixture of 2 Gaussians in 1d, with  $\pi_k = 0.5$ ,  $\sigma_k = 5$ ,  $\mu_1 = -10$  and  $\mu_2 = 10$ . Right: Likelihood surface  $p(\mathcal{D}|\mu_1, \mu_2)$ , with all other parameters set to their true values. We see the two symmetric modes, reflecting the unidentifiability of the parameters. Figure generated by mixGaussLikSurfaceDemo.

compute. This also complicates the computation of MAP and ML estimates, as we discus below.

### **11.3.1 Unidentifiability**

The main problem with computing  $p(\theta|\mathcal{D})$  for an LVM is that the posterior may have multiple modes. To see why, consider a GMM. If the  $z_i$  were all observed, we would have a unimodal posterior for the parameters:

$$
p(\theta|\mathcal{D}) = \text{Dir}(\boldsymbol{\pi}|\mathcal{D}) \prod_{k=1}^{K} \text{NIW}(\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k|\mathcal{D})
$$
\n(II.11)

Consequently we can easily find the globally optimal MAP estimate (and hence globally optimal MLE).

But now suppose the  $z_i$ 's are hidden. In this case, for each of the possible ways of "filling in" the  $z_i$ 's, we get a different unimodal likelihood. Thus when we marginalize out over the  $z_i$ 's, we get a multi-modal posterior for  $p(\theta|\mathcal{D})$ .<sup>1</sup> These modes correspond to different labelings of the clusters. This is illustrated in Figure 11.10(b), where we plot the likelihood function,  $p(\mathcal{D}|\mu_1, \mu_2)$ , for a 2D GMM with  $K = 2$  for the data is shown in Figure 11.10(a). We see two peaks, one corresponding to the case where  $\mu_1 = -10$ ,  $\mu_2 = 10$ , and the other to the case where  $\mu_1 = 10$ ,  $\mu_2 = -10$ . We say the parameters are not **identifiable**, since there is not a unique MLE. Therefore there cannot be a unique MAP estimate (assuming the prior does not rule out certain labelings), and hence the posterior must be multimodal. The question of how many modes there

<sup>1.</sup> Do not confuse multimodality of the parameter posterior,  $p(\theta|\mathcal{D})$ , with the multimodality defined by the model,  $p(x|\theta)$ . In the latter case, if we have K clusters, we would expect to only get K peaks, although it is theoretically possible to get more than K, at least if  $D > 1$  (Carreira-Perpinan and Williams 2003).

are in the parameter posterior is hard to answer. There are  $K!$  possible labelings, but some of the peaks might get merged. Nevertheless, there can be an exponential number, since finding the optimal MLE for a GMM is NP-hard (Aloise et al. 2009; Drineas et al. 2004).

Unidentifiability can cause a problem for Bayesian inference. For example, suppose we draw some samples from the posterior,  $\theta^{(s)} \sim p(\theta|\mathcal{D})$ , and then average them, to try to approximate the posterior mean,  $\bar{\theta} = \frac{1}{S} \sum_{s=1}^{S} \theta^{(s)}$ . (This kind of Monte Carlo approach is explained in more detail in Chapter 24.) If the samples come from different modes, the average will be meaningless. Note, however, that it is reasonable to average the posterior predictive distributions,  $p(\mathbf{x}) \approx \frac{1}{S} \sum_{s=1}^{S} p(\mathbf{x}|\boldsymbol{\theta}^{(s)})$ , since the likelihood function is invariant to which mode the parameters came from.

A variety of solutions have been proposed to the unidentifiability problem. These solutions depend on the details of the model and the inference algorithm that is used. For example, see (Stephens 2000) for an approach to handling unidentifiability in mixture models using MCMC.

The approach we will adopt in this chapter is much simpler: we just compute a single local mode, i.e., we perform approximate MAP estimation. (We say "approximate" since finding the globally optimal MLE, and hence MAP estimate, is NP-hard, at least for mixture models (Aloise et al. 2009).) This is by far the most common approach, because of its simplicity. It is also a reasonable approximation, at least if the sample size is sufficiently large. To see why, consider Figure 11.9(a). We see that there are N latent variables, each of which gets to "see" one data point each. However, there are only two latent parameters, each of which gets to see  $N$  data points. So the posterior uncertainty about the parameters is typically much less than the posterior uncertainty about the latent variables. This justifies the common strategy of computing  $p(z_i|\mathbf{x}_i,\theta)$ , but not bothering to compute  $p(\theta|\mathcal{D})$ . In Section 5.6, we will study hierarchical Bayesian models, which essentially put structure on top of the parameters. In such models, it is important to model  $p(\theta|\mathcal{D})$ , so that the parameters can send information between themselves. If we used a point estimate, this would not be possible.

### **11.3.2 Computing a MAP estimate is non-convex**

In the previous sections, we have argued, rather heuristically, that the likelihood function has multiple modes, and hence that finding an MAP or ML estimate will be hard. In this section, we show this result by more algebraic means, which sheds some additional insight into the problem. Our presentation is based in part on (Rennie 2004).

Consider the log-likelihood for an LVM:

$$
\log p(\mathcal{D}|\boldsymbol{\theta}) = \sum_{i} \log \left[ \sum_{\mathbf{z}_i} p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta}) \right]
$$
(11.12)

Unfortunately, this objective is hard to maximize. since we cannot push the log inside the sum. This precludes certain algebraic simplications, but does not prove the problem is hard.

Now suppose the joint probability distribution  $p(\mathbf{z}_i, \mathbf{x}_i|\boldsymbol{\theta})$  is in the exponential family, which means it can be written as follows:

$$
p(\mathbf{x}, \mathbf{z}|\boldsymbol{\theta}) = \frac{1}{Z(\boldsymbol{\theta})} \exp[\boldsymbol{\theta}^T \boldsymbol{\phi}(\mathbf{x}, \mathbf{z})]
$$
(11.13)

where  $\phi(\mathbf{x}, \mathbf{z})$  are the sufficient statistics, and  $Z(\theta)$  is the normalization constant (see Section 9.2 for more details). It can be shown (Exercise 9.2) that the MVN is in the exponential family, as are nearly all of the distributions we have encountered so far, including Dirichlet, multinomial, Gamma, Wishart, etc. (The Student distribution is a notable exception.) Furthermore, mixtures of exponential families are also in the exponential family, providing the mixing indicator variables are observed (Exercise 11.11).

With this assumption, the **complete data log likelihood** can be written as follows:

$$
\ell_c(\boldsymbol{\theta}) = \sum_i \log p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta}) = \boldsymbol{\theta}^T (\sum_i \boldsymbol{\phi}(\mathbf{x}_i, \mathbf{z}_i)) - NZ(\boldsymbol{\theta})
$$
\n(11.14)

The first term is clearly linear in  $\theta$ . One can show that  $Z(\theta)$  is a convex function (Boyd and Vandenberghe 2004), so the overall objective is concave (due to the minus sign), and hence has a unique maximum.

Now consider what happens when we have missing data. The **observed data log likelihood** is given by

$$
\ell(\boldsymbol{\theta}) = \sum_{i} \log \sum_{\mathbf{z}_i} p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta}) = \sum_{i} \log \left[ \sum_{\mathbf{z}_i} e^{\boldsymbol{\theta}^T \boldsymbol{\phi}(\mathbf{z}_i, \mathbf{x}_i)} \right] - N \log Z(\boldsymbol{\theta}) \tag{11.15}
$$

One can show that the log-sum-exp function is convex (Boyd and Vandenberghe 2004), and we know that  $Z(\theta)$  is convex. However, the difference of two convex functions is not, in general, convex. So the objective is neither convex nor concave, and has local optima.

The disadvantage of non-convex functions is that it is usually hard to find their global optimum. Most optimization algorithms will only find a local optimum; which one they find depends on where they start. There are some algorithms, such as simulated annealing (Section 24.6.1) or genetic algorithms, that claim to always find the global optimum, but this is only under unrealistic assumptions (e.g., if they are allowed to be cooled "infinitely slowly", or allowed to run "infinitely long"). In practice, we will run a local optimizer, perhaps using **multiple random restarts** to increase out chance of finding a "good" local optimum. Of course, careful initialization can help a lot, too. We give examples of how to do this on a case-by-case basis.

Note that a convex method for fitting mixtures of Gaussians has been proposed. The idea is to assign one cluster per data point, and select from amongst them, using a convex  $\ell_1$ -type penalty, rather than trying to optimize the locations of the cluster centers. See (Lashkari and Golland 2007) for details. This is essentially an unsupervised version of the approach used in sparse kernel logistic regression, which we will discuss in Section 14.3.2. Note, however, that the  $\ell_1$  penalty, although convex, is not necessarily a good way to promote sparsity, as discussed in Chapter 13. In fact, as we will see in that Chapter, some of the best sparsity-promoting methods use non-convex penalties, and use EM to optimie them! The moral of the story is: do not be afraid of non-convexity.

## **11.4 The EM algorithm**

For many models in machine learning and statistics, computing the ML or MAP parameter estimate is easy provided we observe all the values of all the relevant random variables, i.e., if

| Model                                 | Section        |
|---------------------------------------|----------------|
| Mix. Gaussians                        | 11.4.2         |
| Mix. experts                          | 11.4.3         |
| Factor analysis                       | 12.1.5         |
| Student T                             | 11.4.5         |
| Probit regression                     | 11.4.6         |
| DGM with hidden variables             | 11.4.4         |
| MVN with missing data                 | 11.6.1         |
| <b>HMMs</b>                           | 17.5.2         |
| Shrinkage estimates of Gaussian means | Exercise 11.13 |

**Table 11.2** Some models discussed in this book for which EM can be easily applied to find the ML/ MAP parameter estimate.

we have complete data. However, if we have missing data and/or latent variables, then computing the ML/MAP estimate becomes hard.

One approach is to use a generic gradient-based optimizer to find a local minimum of the **negative log likelihood** or **NLL**, given by

$$
\text{NLL}(\boldsymbol{\theta}) = -\triangleq \frac{1}{N} \log p(\mathcal{D}|\boldsymbol{\theta}) \tag{11.16}
$$

However, we often have to enforce constraints, such as the fact that covariance matrices must be positive definite, mixing weights must sum to one, etc., which can be tricky (see Exercise 11.5). In such cases, it is often much simpler (but not always faster) to use an algorithm called **expectation maximization**, or **EM** for short (Dempster et al. 1977; Meng and van Dyk 1997; McLachlan and Krishnan 1997). This is a simple iterative algorithm, often with closed-form updates at each step. Furthermore, the algorithm automatically enforce the required constraints.

EM exploits the fact that if the data were fully observed, then the ML/ MAP estimate would be easy to compute. In particular, EM is an iterative algorithm which alternates between inferring the missing values given the parameters (E step), and then optimizing the parameters given the "filled in" data (M step). We give the details below, followed by several examples. We end with a more theoretical discussion, where we put the algorithm in a larger context. See Table 11.2 for a summary of the applications of EM in this book.

### **11.4.1 Basic idea**

Let  $x_i$  be the visible or observed variables in case i, and let  $z_i$  be the hidden or missing variables. The goal is to maximize the log likelihood of the observed data:

$$
\ell(\boldsymbol{\theta}) = \sum_{i=1}^{N} \log p(\mathbf{x}_i | \boldsymbol{\theta}) = \sum_{i=1}^{N} \log \left[ \sum_{\mathbf{z}_i} p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta}) \right]
$$
(11.17)

Unfortunately this is hard to optimize, since the log cannot be pushed inside the sum.

EM gets around this problem as follows. Define the **complete data log likelihood** to be

$$
\ell_c(\boldsymbol{\theta}) \triangleq \sum_{i=1}^N \log p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta})
$$
\n(11.18)

This cannot be computed, since  $z_i$  is unknown. So let us define the **expected complete data log likelihood** as follows:

$$
Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{t-1}) = \mathbb{E}\left[\ell_c(\boldsymbol{\theta}) \middle| \mathcal{D}, \boldsymbol{\theta}^{t-1}\right]
$$
\n(11.19)

where t is the current iteration number.  $Q$  is called the **auxiliary function**. The expectation is taken wrt the old parameters,  $\theta^{t-1}$ , and the observed data D. The goal of the **E** step is to compute  $Q(\theta, \theta^{t-1})$ , or rather, the terms inside of it which the MLE depends on; these are known as the **expected sufficient statistics** or ESS. In the **M step**, we optimize the Q function wrt *θ*:

$$
\boldsymbol{\theta}^t = \arg \max_{\boldsymbol{\theta}} Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{t-1})
$$
\n(11.20)

To perform MAP estimation, we modify the M step as follows:

$$
\boldsymbol{\theta}^{t} = \underset{\boldsymbol{\theta}}{\operatorname{argmax}} Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{t-1}) + \log p(\boldsymbol{\theta})
$$
\n(11.21)

The E step remains unchanged.

In Section 11.4.7 we show that the EM algorithm monotonically increases the log likelihood of the observed data (plus the log prior, if doing MAP estimation), or it stays the same. So if the objective ever goes down, there must be a bug in our math or our code. (This is a surprisingly useful debugging tool!)

Below we explain how to perform the E and M steps for several simple models, that should make things clearer.

## **11.4.2 EM for GMMs**

In this section, we discuss how to fit a mixture of Gaussians using EM. Fitting other kinds of mixture models requires a straightforward modification — see Exercise 11.3. We assume the number of mixture components, K, is known (see Section 11.5 for discussion of this point).

### **11.4.2.1 Auxiliary function**

The expected complete data log likelihood is given by

$$
Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{(t-1)}) \triangleq \mathbb{E}\left[\sum_i \log p(\mathbf{x}_i, z_i | \boldsymbol{\theta})\right]
$$
(11.22)

$$
= \sum_{i} \mathbb{E}\left[\log\left[\prod_{k=1}^{K}(\pi_k p(\mathbf{x}_i|\boldsymbol{\theta}_k))^{\mathbb{I}(z_i=k)}\right]\right]
$$
(11.23)

$$
= \sum_{i} \sum_{k} \mathbb{E}\left[\mathbb{I}(z_i = k)\right] \log[\pi_k p(\mathbf{x}_i | \boldsymbol{\theta}_k)] \tag{11.24}
$$

$$
= \sum_{i} \sum_{k} p(z_i = k | \mathbf{x}_i, \boldsymbol{\theta}^{t-1}) \log[\pi_k p(\mathbf{x}_i | \boldsymbol{\theta}_k)] \qquad (11.25)
$$

$$
= \sum_{i} \sum_{k} r_{ik} \log \pi_k + \sum_{i} \sum_{k} r_{ik} \log p(\mathbf{x}_i | \boldsymbol{\theta}_k)
$$
(11.26)

where  $r_{ik} \triangleq p(z_i = k | \mathbf{x}_i, \boldsymbol{\theta}^{(t-1)})$  is the **responsibility** that cluster k takes for data point *i*. This is computed in the E step, described below.

### **11.4.2.2 E step**

The E step has the following simple form, which is the same for any mixture model:

$$
r_{ik} = \frac{\pi_k p(\mathbf{x}_i | \boldsymbol{\theta}_k^{(t-1)})}{\sum_{k'} \pi_{k'} p(\mathbf{x}_i | \boldsymbol{\theta}_{k'}^{(t-1)})}
$$
(11.27)

### **11.4.2.3 M step**

In the M step, we optimize Q wrt  $\pi$  and the  $\theta_k$ . For  $\pi$ , we obviously have

$$
\pi_k = \frac{1}{N} \sum_i r_{ik} = \frac{r_k}{N}
$$
\n(11.28)

where  $r_k \triangleq \sum_i r_{ik}$  is the weighted number of points assigned to cluster k.

To derive the M step for the  $\mu_k$  and  $\Sigma_k$  terms, we look at the parts of Q that depend on  $\mu_k$ and  $\Sigma_k$ . We see that the result is

$$
\ell(\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) = \sum_k \sum_i r_{ik} \log p(\mathbf{x}_i | \boldsymbol{\theta}_k)
$$
\n(11.29)

$$
= -\frac{1}{2}\sum_{i} r_{ik} \left[ \log |\mathbf{\Sigma}_k| + (\mathbf{x}_i - \boldsymbol{\mu}_k)^T \mathbf{\Sigma}_k^{-1} (\mathbf{x}_i - \boldsymbol{\mu}_k) \right]
$$
(11.30)

This is just a weighted version of the standard problem of computing the MLEs of an MVN (see Section 4.1.3). One can show (Exercise 11.2) that the new parameter estimates are given by

$$
\mu_k = \frac{\sum_i r_{ik} \mathbf{x}_i}{r_k} \tag{11.31}
$$

$$
\Sigma_k = \frac{\sum_i r_{ik} (\mathbf{x}_i - \boldsymbol{\mu}_k)(\mathbf{x}_i - \boldsymbol{\mu}_k)^T}{r_k} = \frac{\sum_i r_{ik} \mathbf{x}_i \mathbf{x}_i^T}{r_k} - \boldsymbol{\mu}_k \boldsymbol{\mu}_k^T
$$
(11.32)

These equations make intuitive sense: the mean of cluster  $k$  is just the weighted average of all points assigned to cluster  $k$ , and the covariance is proportional to the weighted empirical scatter matrix.

After computing the new estimates, we set  $\boldsymbol{\theta}^t = (\pi_k, \mu_k, \Sigma_k)$  for  $k = 1 : K$ , and go to the next E step.

### **11.4.2.4 Example**

An example of the algorithm in action is shown in Figure 11.11. We start with  $\mu_1 = (-1, 1)$ ,  $\Sigma_1 = I$ ,  $\mu_2 = (1, -1)$ ,  $\Sigma_2 = I$ . We color code points such that blue points come from cluster 1 and red points from cluster 2. More precisely, we set the color to

$$
color(i) = r_{i1}blue + r_{i2}red
$$
\n(11.33)

so ambiguous points appear purple. After 20 iterations, the algorithm has converged on a good clustering. (The data was standardized, by removing the mean and dividing by the standard deviation, before processing. This often helps convergence.)

### **11.4.2.5 K-means algorithm**

There is a popular variant of the EM algorithm for GMMs known as the **K-means algorithm**, which we now discuss. Consider a GMM in which we make the following assumptions:  $\Sigma_k =$  $\sigma^2 \mathbf{I}_D$  is fixed, and  $\pi_k = 1/K$  is fixed, so only the cluster centers,  $\mu_k \in \mathbb{R}^D$ , have to be estimated. Now consider the following delta-function approximation to the posterior computed during the E step:

$$
p(z_i = k | \mathbf{x}_i, \boldsymbol{\theta}) \approx \mathbb{I}(k = z_i^*)
$$
\n(11.34)

where  $z_i^* = \argmax_k p(z_i = k | \mathbf{x}_i, \theta)$ . This is sometimes called **hard EM**, since we are making a hard assignment of points to clusters. Since we assumed an equal spherical covariance matrix for each cluster, the most probable cluster for  $x_i$  can be computed by finding the nearest prototype:

$$
z_i^* = \arg\min_k ||\mathbf{x}_i - \boldsymbol{\mu}_k||_2^2 \tag{11.35}
$$

Hence in each E step, we must find the Euclidean distance between  $N$  data points and  $K$  cluster centers, which takes  $O(NKD)$  time. However, this can be sped up using various techniques, such as applying the triangle inequality to avoid some redundant computations (Elkan 2003). Given the hard cluster assignments, the M step updates each cluster center by computing the mean of all points assigned to it:

$$
\mu_k = \frac{1}{N_k} \sum_{i:z_i=k} \mathbf{x}_i
$$
\n(11.36)

See Algorithm 5 for the pseudo-code.

Image /page/16/Figure/1 description: This image displays six scatter plots illustrating the Expectation-Maximization (EM) algorithm applied to Gaussian Mixture Models (GMM) on the Old Faithful dataset. The plots are arranged in a 2x3 grid. Plot (a) shows the initial state with green data points and two circles representing initial cluster centers. Plot (b) shows the data points colored red and blue, with the circles indicating the initial cluster centers. Plots (c), (d), (e), and (f) show the progression of the EM algorithm at iterations 1, 3, 5, and 16, respectively. In these plots, the data points are colored red and blue, and ellipses representing the estimated covariance of each cluster are overlaid. The x and y axes in all plots range from -2 to 2. The caption below the plots reads "Illustration of the EM for a GMM applied to the Old Faithful data. (a) Initial (ran".

**Figure 11.11** Illustration of the EM for a GMM applied to the Old Faithful data. (a) Initial (random) values of the parameters. (b) Posterior responsibility of each point computed in the first E step. The degree of redness indicates the degree to which the point belongs to the red cluster, and similarly for blue; this purple points have a roughly uniform posterior over clusters. (c) We show the updated parameters after the first M step. (d) After 3 iterations. (e) After 5 iterations. (f) After 16 iterations. Based on (Bishop 2006a) Figure 9.8. Figure generated by mixGaussDemoFaithful.
# RETHINKING BACKDOOR ATTACKS ON DATASET DISTILLATION: A KERNEL METHOD PERSPECTIVE

Pi<PERSON>-Yu <PERSON> IBM Research

Ming-Yu <PERSON>

National Taiwan University

Sheng-Yen <PERSON>u The Chinese University of Hong Kong

Sy-Yen Kuo

National Taiwan University

Chia-Mu Yu

<PERSON> Yang Ming Chiao Tung University

The following are the results of the experiment:

- **Test 1:** 50%
- **Test 2:** 75%
- **Test 3:** 100%

<PERSON><PERSON><PERSON><PERSON><PERSON> The Chinese University of Hong Kong

## ABSTRACT

Dataset distillation offers a potential means to enhance data efficiency in deep learning. Recent studies have shown its ability to counteract backdoor risks present in original training samples. In this study, we delve into the theoretical aspects of backdoor attacks and dataset distillation based on kernel methods. We introduce two new theory-driven trigger pattern generation methods specialized for dataset distillation. Following a comprehensive set of analyses and experiments, we show that our optimization-based trigger design framework informs effective backdoor attacks on dataset distillation. Notably, datasets poisoned by our designed trigger prove resilient against conventional backdoor attack detection and mitigation methods. Our empirical results validate that the triggers developed using our approaches are proficient at executing resilient backdoor attacks. [1](#page-0-0)

## 1 INTRODUCTION

In recent years, deep neural networks have achieved significant success in many fields, such as natural language modeling, computer vision, medical diagnosis, etc. These successes are usually built on large-scale datasets consisting of millions or even billions of samples. Under this scale of datasets, training a model becomes troublesome because of the need for sufficiently large memory to store the datasets or the need for special infrastructure to train a model. To deal with this problem, dataset distillation [\(Wang et al., 2018\)](#page-11-0) or dataset condensation [\(Zhao et al., 2021\)](#page-11-1) is designed to compress the information of large datasets into a small synthetic dataset. These small datasets generated by dataset distillation, called distilled datasets, still retain a certain degree of utility. Under the same model (neural network) structure, the performance of the model trained on the distilled dataset is only slightly lower than that of the model trained on the original large-scale dataset.

However, with the development of dataset distillation techniques, the related security and privacy issues started to emerge [\(Liu et al., 2023a;](#page-10-0)[c;](#page-10-1) [Dong et al., 2022\)](#page-9-0). In this paper, we focus on backdoor attacks on dataset distillation. In particular, as each distilled sample does not have a clear connection to the original samples, a straightforward stealthy backdoor attack is to poison a benign dataset first and then derive the corresponding distilled poisoned dataset. One can expect that the triggers can hardly be detected visually in the distilled poisoned dataset. However, these triggers, if not designed properly, can be diluted during dataset distillation, making backdoor attacks ineffective.

[Liu et al.](#page-10-1) [\(2023c\)](#page-10-1) empirically demonstrate the feasibility of generating a poisoned dataset surviving dataset distillation. In particular, [Liu et al.](#page-10-1) [\(2023c\)](#page-10-1) propose DOORPING as a distillation-resilient backdoor. However, DOORPING suffers from two major weaknesses. First, the resiliency and optimality of a backdoor against dataset distillation remain unclear, mainly due to the lack of a theoret-

<span id="page-0-0"></span> ${}^{1}$ Code is available at <https://github.com/Mick048/KIP-based-backdoor-attack.git>.

ical foundation for the distillation resiliency. Second, DOORPING relies on a bi-level optimization and, as a consequence, consumes a significant amount of time to generate backdoor triggers.

To bridge this gap, this paper makes a step toward dataset distillation-resilient backdoors with a theoretical foundation. Our contributions can be summarized as follows:

- To the best of our knowledge, we establish the first theoretical framework to characterize backdoor effects on dataset distillation, which explains why certain backdoors survive dataset distillation.
- We propose two theory-induced backdoors, simple-trigger and relax-trigger. In particular, relaxtrigger and DOORPING share the same clean test accuracy (CTA) and attack success rate (ASR). However, relax-trigger relies only on ordinary (single-level) optimization procedures and can be computationally efficient.
- We experimentally show both simple-trigger and relax-trigger signify the advanced threat vector to either completely break or weaken eight existing defenses. In particular, relax-trigger can evade all eight existing backdoor detection and cleansing methods considered in this paper.

## 2 BACKGROUND AND RELATED WORKS

Dataset Distillation. Dataset distillation is a technique for compressing the information of a target dataset into a small synthetic dataset. The explicit definition can be described as follows. Consider the input space  $\mathcal{X} \subset \mathbb{R}^d$ , the label space  $\mathcal{Y} \subset \mathbb{R}^C$ , and the distribution  $(x, y) \sim \mathcal{D}$ , where  $x \in \mathcal{X}$ and  $y \in \mathcal{Y}$ . Suppose we are given a dataset denoted by  $\mathcal{T} = \{(x_t, y_t)\}_{t=1}^N \sim \mathcal{D}^N$  where  $x_t \in \mathcal{X}$ ,  $y_t$  ∈  $\mathcal{Y}$ , and  $N$  is the number of samples, and a synthetic dataset denoted as  $\mathcal{S} = \{(x_s, y_s)\}_{s=1}^{N_{\mathcal{S}}}$  where  $x_s \in \mathcal{X}$ ,  $y_s \in \mathcal{Y}$ ,  $N_{\mathcal{S}}$  is the number of samples in  $\mathcal{S}$ , and  $N_{\mathcal{S}} \ll N$ . generated by a dataset distillation method can be formulated as

<span id="page-1-0"></span>
$$
S^* = \underset{S}{\arg\min} \mathcal{L}(S, \mathcal{T}),\tag{1}
$$

where  $\mathcal L$  is some function to measure the information loss between  $\mathcal S$  and  $\mathcal T$ . There are several types of  $\mathcal L$ . One of the most straightforward ways to define  $\mathcal L$  is to measure the model's performance. In this sense, the dataset distillation can be reformulated as

$$
S^* = \underset{S}{\arg\min} \frac{1}{N} \ell(f_{\mathcal{S}}, \mathcal{T}) \text{ subject to } f_{\mathcal{S}} = \underset{f \in \mathcal{H}}{\arg\min} \frac{1}{N_{\mathcal{S}}} \ell(f, \mathcal{S}) + \lambda \|f\|_{\mathcal{H}}^2 \tag{2}
$$

where the model (a classifier) is denoted as  $f : \mathcal{X} \to \mathcal{Y}, \mathcal{H}$  is some collection of models (hypothesis class),  $\ell$  is the loss function measuring the loss of model evaluated on the dataset,  $\lambda \geq 0$  is the weight for the regularization term, and  $\|\cdot\|_{\mathcal{H}}$  is some norm defined on  $\mathcal{H}$ . Eq. [\(2\)](#page-1-0) forms a bi-level optimization problem. This type of dataset distillation is categorized as *performance-matching dataset distillation* in [\(Yu et al., 2023\)](#page-11-2). For example, all of the methods from [\(Wang et al., 2018;](#page-11-0) [Nguyen](#page-10-2) [et al., 2021;](#page-10-2) [Loo et al., 2022;](#page-10-3) [Zhou et al., 2022;](#page-11-3) [Loo et al., 2023\)](#page-10-4) are performance-matching dataset distillation, while the methods from [\(Zhao & Bilen, 2023;](#page-11-4) [Lee et al., 2022a;](#page-9-1) [Wang et al., 2022;](#page-11-5) [Zhao](#page-11-1) [et al., 2021;](#page-11-1) [Lee et al., 2022b;](#page-9-2) [Liu et al., 2022;](#page-10-5) [2023b;](#page-10-6) [Wang et al., 2023\)](#page-11-6) belong to either parameterpreserving or distribution-preserving. In this paper, we focus only on performance-matching dataset distillation, with a particular example on kernel inducing points (KIP) from [Nguyen et al.](#page-10-2) [\(2021\)](#page-10-2).

Reproducing Kernel Hilbert Space and KIP. In general, the inner optimization problem in Eq. [\(2\)](#page-1-0) does not have a closed-form solution, which not only increases the computational cost, but also increases the difficulty of analyzing this problem. To alleviate this problem, we assume our model lies in the reproducing kernel Hilbert space (RKHS) [\(Aronszajn, 1950;](#page-9-3) [Berlinet & Thomas-](#page-9-4)[Agnan, 2011;](#page-9-4) [Ghojogh et al., 2021\)](#page-9-5).

<span id="page-1-1"></span>**Definition 1** (Kernel).  $k : \mathcal{X} \times \mathcal{X} \to \mathbb{R}$  is a kerenl if the following two points hold. (a)  $\forall x, x' \in \mathcal{X}$ ,  $the \: k$  *is symmetric; i.e.,*  $k(x, x') = k(x', x)$ . (b)  $\forall n \in \mathbb{N}, \forall \{x_1, x_2, \ldots, x_n\}$  where each  $x_i$ are sampled from X, the kernel matrix  $K$  defined as  $K_{ij} := k(x_i, x_j)$  is postive semi-definite.

**Definition 2** (Reproducing Kernel Hilbert Space). *Given an kernel*  $k : \mathcal{X} \times \mathcal{X} \to \mathbb{R}$ *, the collection of real-valued model*  $\mathcal{H}_k = \{f : \mathcal{X} \to \mathbb{R}\}$  *is a reproducing kernel Hilbert space corresponding to the kernel k, if (a)*  $H_k$  *is a Hilbert space corresponding to the inner product*  $\langle \cdot, \cdot \rangle_{H_k}$ *, (b)*  $\forall x \in X$ *,*  $k(\cdot, x) \in \mathcal{H}_k$ , (c)  $\forall x \in \mathcal{X}$  and  $f \in \mathcal{H}_k$ ,  $f(x) = \langle f, k(\cdot, x) \rangle_{\mathcal{H}_k}$  (Reproducing property).

There are several advantages to considering RKHS for solving optimization problems. One of the most beneficial properties is that there is Representer Theorem [\(Kimeldorf & Wahba, 1971;](#page-9-6) [Ghojogh](#page-9-5) [et al., 2021\)](#page-9-5) induced by the reproducing property. In particular, consider the optimization problem:

$$
f^* = \underset{f \in \mathcal{H}_k}{\arg \min} \frac{1}{N} \sum_{i=1}^N \ell(f(x_i), y_i) + \lambda \|f\|_{\mathcal{H}_k}^2,
$$
\n(3)

where  $f: \mathcal{X} \to \mathbb{R}, y_i \in \mathbb{R}, \lambda \geq 0$  is the weight for the regularization term. The solution of the optimization problem  $f^*$  can be expressed as the linear combination of  $\{k(\cdot, x_i)\}_i^N$ . Furthermore, if we set  $\ell(f, (x, y)) = ||f(x) - y||_2^2$ , there is a closed-form expression for  $f^*$ :

<span id="page-2-0"></span>
$$
f^*(x) = k(x, \mathbf{X})[k(\mathbf{X}, \mathbf{X}) + N\lambda \mathbf{I}]^{-1} \mathbf{Y},
$$
\n(4)

where  $k(x, X) = [k(x, x_1), k(x, x_2), \dots, k(x, x_N)], k(X, X)$  is an  $N \times N$  matrix with  $[k(\bm{X}, \bm{X})]_{ij} = k(x_i, x_j)$ , and  $\bm{Y} = [y_1, y_2, \dots, y_N]^T$ . Now, we return to Eq. [\(2\)](#page-1-0). By rewriting the model  $f: \mathcal{X} \to \mathcal{Y} \subset \mathbb{R}^c$  as  $[f^1, f^2, \dots, f^c]^T$ , where each  $f^i: \mathcal{X} \to \mathbb{R}$  is a real-valued function and  $f^i$  is bounded in the RKHS  $\mathcal{H}_k$ , the inner optimization problem for  $f_{\mathcal{S}}$  in Eq. [\(2\)](#page-1-0) can be considered as  $c$  independent optimization problems and each problem has a closed-form solution as shown in Eq. [\(4\)](#page-2-0). Thus, the solution of the inner optimization problem can be expressed as

<span id="page-2-1"></span>
$$
f_{\mathcal{S}}(x)^{T} = k(x, \mathbf{X}_{\mathcal{S}})[k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}}) + N_{\mathcal{S}}\lambda \mathbf{I}]^{-1} \mathbf{Y}_{\mathcal{S}},
$$
\n(5)

where  $k(x, X_S) = [k(x, x_{s_1}), k(x, x_{s_2}), \dots, k(x, x_{s_{N_S}})]$ ,  $k(X_S, X_S)$  is a  $N_S \times N_S$  matrix with  $[k(X_S, X_S)]_{ij} = k(x_{s_i}, x_{s_j})$ , and  $Y_S$  is a  $N_S \times c$  matrix with  $Y_S = [y_{s_1}, y_{s_2}, \dots, y_{s_{N_S}}]^T$ .

Then, the dataset distillation problem can be expressed as

<span id="page-2-2"></span>
$$
S^* = \underset{S}{\arg\min} \frac{1}{N} \sum_{t=1}^N \|f_{S}(x_t) - y_t\|_2^2, \tag{6}
$$

where  $f_{\mathcal{S}}(x)^T = k(x, \mathbf{X}_{\mathcal{S}})[k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}}) + N_{\mathcal{S}}\lambda \mathbf{I}]^{-1}Y_{\mathcal{S}}$  as shown in Eq. [\(5\)](#page-2-1). We reduce a two-level optimization problem to a one-level optimization problem using RKHS. Essentially, KIP [\(Nguyen](#page-10-2) [et al., 2021\)](#page-10-2) can be formulated as Eq. [\(6\)](#page-2-2).

An important problem for Eq. [\(6\)](#page-2-2) is how to construct or select a kernel  $k(\cdot, \cdot)$ . Nevertheless, we do not discuss this problem in this paper. We directly consider the neural tangent kernel (NTK) [\(Jacot](#page-9-7) [et al., 2018;](#page-9-7) [He et al., 2020;](#page-9-8) [Lee et al., 2019\)](#page-9-9) induced by a three-layer neural network as the kernel  $k(\cdot, \cdot)$  to do the experiment in Section [4.](#page-6-0)

Backdoor Attack. Backdoor attack introduces some malicious behavior into the model without degrading the model's performance on the original task by poisoning the dataset [\(Gu et al., 2019;](#page-9-10) [Chen et al., 2017;](#page-9-11) [Liu et al., 2018b;](#page-10-7) [Turner et al., 2019;](#page-10-8) [Nguyen & Tran, 2020;](#page-10-9) [Barni et al., 2019;](#page-9-12) [Li et al., 2021c;](#page-9-13) [Nguyen & Tran, 2021;](#page-10-10) [Liu et al., 2020;](#page-10-11) [Tang et al., 2021;](#page-10-12) [Qi et al., 2022;](#page-10-13) [Souri](#page-10-14) [et al., 2022\)](#page-10-14). To be more specific, consider the following scenario. Suppose there are two types of distributions,  $(x_a, y_a) \sim \mathcal{D}_A$  and  $(x_b, y_b) \sim \mathcal{D}_B$ .  $\mathcal{D}_A$  corresponds to the original normal behavior, while  $\mathcal{D}_B$  corresponds to the malicious behavior. The goal of the backdoor attack is to construct a poisoned dataset such that the model trained on it learns well for both the original normal distribution  $\mathcal{D}_A$  and the malicious distribution  $\mathcal{D}_B$ . In other words, an attacker wants to construct a dataset D such that the model trained on  $\tilde{D}$ , denoted  $f_{\tilde{D}}$ , has sufficiently low risk  $\mathbb{E}_{(x_a,y_a)\sim\mathcal{D}_A}\ell(f_{\tilde{D}},(x_a,y_a))$ and  $\mathbb{E}_{(x_b, y_b)\sim \mathcal{D}_B} \ell(f_{\tilde{D}}, (x_b, y_b))$  at the same time.

One approach to constructing such a dataset  $\tilde{D}$  is to directly mix the *benign dataset*  $D_A \sim \mathcal{D}_A^{N_A}$ and the *trigger dataset*  $D_B \sim \mathcal{D}_B^{N_B}$ . An attacker usually wants to make the attack stealthy, and so it sets  $N_B \ll N_A$ . We define  $\mathcal{D}_B$  according to the original normal behavior  $\mathcal{D}_A$ , the trigger  $T \in \mathbb{R}^d$ , and the trigger label  $y_T \in \mathcal{Y}$ :

<span id="page-2-3"></span>
$$
(x_b, y_b) := ((1 - m) \odot x_a + m \odot T, y_T), \tag{7}
$$

where  $x_a \sim \mathcal{D}_A$ ,  $m \in \mathbb{R}^d$  is the real-valued mask, and ⊙ is the Hadamard product.

## 3 PROPOSED METHODS AND THEORETICAL ANALYSIS

In this paper, we aim to use dataset distillation (KIP as a representative) to perform the backdoor attack. In the simplest form of KIP-based backdoor attacks (as shown in Algorithm [1](#page-16-0) of the Appendix), we first construct the *poisoned dataset*  $\tilde{D} = D_A \cup D_B$  from  $\mathcal{D}_A^{N_A}$  and  $\mathcal{D}_B^{N_B}$ . Then, we perform KIP on  $\tilde{D}$  and compress the information in  $\tilde{D}$  into the *distilled poisoned dataset*  $S^* = \{(x_s, y_s)\}_{s=1}^{N_S}$ , where  $N_S \ll N_A + N_B$ . Namely, we solve the following optimization problem

<span id="page-3-1"></span>
$$
S^* = \underset{S}{\text{arg min}} \frac{1}{N_A + N_B} \sum_{(x,y) \in \tilde{D}} \|f_S(x) - y\|_2^2, \tag{8}
$$

where  $f_{\mathcal{S}}(x)^T = k(x,\bm{X}_{\mathcal{S}})[k(\bm{X}_{\mathcal{S}},\bm{X}_{\mathcal{S}})+N_{\mathcal{S}}\lambda\bm{I}]^{-1}\bm{Y}_{\mathcal{S}}$ . Essentially, the above KIP-based backdoor attack is the same as Naive attack in [\(Liu et al., 2023c\)](#page-10-1) except that the other distillation, instead of KIP, is used in Naive attack. The experimental results in [\(Liu et al., 2023c\)](#page-10-1) show that ASR grows but CTA drops significantly when the trigger size increases. [Liu et al.](#page-10-1) [\(2023c\)](#page-10-1) claims a trade-off between CTA and the trigger size. Nonetheless, we find that our KIP-based backdoor attack does not have such a trade-off. This motivates us to develop a theoretical framework for backdoor attacks on dataset distillation.

Below, we introduce the theoretical framework in Section [3.1,](#page-3-0) followed by two theory-induced backdoor attacks, simple-trigger and relax-trigger in Section [3.2](#page-5-0) and Section [3.3,](#page-5-1) respectively.

<span id="page-3-0"></span>

### 3.1 THEORETICAL FRAMEWORK

We first introduce the structure of our analysis, which divides the risk of KIP-based backdoor attacks into three parts: projection loss, conflict loss, and generalization gap. Then, we provide an upper bound for each part of the risk.

Structure of Analysis. Recall that the goal of a KIP-based backdoor attack is to construct the synthetic dataset  $\mathcal{S}^*$  such that the risk  $\mathbb{E}_{(x,y)\sim\mathcal{D}}^{\times}(\mathcal{E}_{S^*}, (x, y))$  is sufficiently low, where  $\mathcal D$  is the normal distribution  $\mathcal{D}_A$  or the malicious distribution  $\mathcal{D}_B$ . The classical framework for analyzing this problem is to divide the risk into two parts, the empirical risk and generalization gap. Namely,

$$
\mathbb{E}_{(x,y)\sim\mathcal{D}} \ell(f_{\mathcal{S}^*}, (x,y)) = \underbrace{\mathbb{E}_{(x,y)\sim\mathcal{D}} \ell(f_{\mathcal{S}^*}, (x,y))}_{\text{Empirical risk}} + \underbrace{\left[\mathbb{E}_{(x,y)\sim\mathcal{D}} \ell(f_{\mathcal{S}^*}, (x,y)) - \mathbb{E}_{(x,y)\sim\mathcal{D}} \ell(f_{\mathcal{S}^*}, (x,y))\right]}_{\text{Generalization gap}} \tag{9}
$$

where  $D = \{(x_i, y_i)\}_{i=1}^N$  is the dataset sampled from the distribution  $\mathcal{D}^N$  and N is the number of samples of D. Here, we consider that D is  $D_A \sim \mathcal{D}^{N_A}$  or  $D_B \sim \mathcal{D}^{N_B}$ . In our framework, we continue to divide the empirical risk into two parts as

$$
\mathbb{E}_{(x,y)\sim D} \ell(f_{\mathcal{S}^*}, (x,y)) \leq \frac{N_A + N_B}{N} \left[\min_{\mathcal{S}} \mathbb{E}_{(x,y)\sim \tilde{D}} \ell(f_{\mathcal{S}}, (x, f_{\tilde{D}}(x)))\right]_{\text{Projection Loss}} + \left[\mathbb{E}_{(x,y)\sim \tilde{D}} \ell(f_{\tilde{D}}, (x,y))\right]_{\text{conflict Loss}} \tag{10}
$$

where  $\tilde{D} = D_A \cup D_B$ ,  $f_{\tilde{D}}$  is the model trained on  $\tilde{D}$  with the weight of the regularization term  $\lambda \geq 0$ and  $f_{\mathcal{S}}$  is the model trained on S with the weight of the regularization term  $\lambda_{\mathcal{S}} \geq 0$ . Intuitively, given a dataset  $\tilde{D}$  constructed from  $\mathcal{D}_A^{N_A}$  and  $\mathcal{D}_B^{N_B}$ ,  $f_{\tilde{D}}$  is regarded as the best model derived from the information of  $\tilde{D}$ . The conflict loss reflects the internal information conflict between the information about  $\mathcal{D}_A$  in D and the information about  $\mathcal{D}_B$  in D. For example, we consider a dog/cat picture classification problem. In the dataset  $D_A$ , we label the dog pictures with 0 and label the cat pictures with 1. However, in the dataset  $D_B$ , we label the dog pictures with 1 and label the cat pictures with 0. It is clear that the model trained on  $\ddot{D}$  must perform terribly on the dataset either  $D_A$  or  $D_B$ . In this case, the information between  $D_A$  and  $D_B$  have strong conflict and the conflict loss would be large. On the other hand, projection loss reflects the loss of information caused by projecting  $f_{\tilde{D}}$ into  $\{f_{\mathcal{S}} | \mathcal{S} = \{(x_i, y_i) \in \mathcal{X} \times \mathcal{Y}\}_{i=1}^{N_{\mathcal{S}}} \}$ . We can also consider the projection loss as the increase in

information induced by compressing the information of  $\ddot{D}$  into the synthetic dataset S. Take writing an abstract for example. If we want to write a 100 words abstract to describe a 10000 words article, the abstract may suffer some lack of semantics to some degree. Such a phenomena also happens for dataset distillation. When the information of a large dataset is complex enough, the information loss for dataset distillation will be significant; When the information of a large dataset is very simple, it is possible that there is only very limited information loss. We introduce the projection loss defined above to measure this phenomenon. More details can be found below.

**Conflict Loss.** In a KIP-based backdoor attack, the dataset  $\tilde{D}$  is defined as  $\tilde{D} = D_A \cup D_B$ , where  $D_A \sim \mathcal{D}_A^{N_A}$  and  $D_B \sim \mathcal{D}_B^{N_B}$ . By Eq. [\(5\)](#page-2-1) we know that the model trained on  $\tilde{D}$  with the weight of the regularization term  $\lambda \geq 0$  has a closed-form solution if we constrain the model in the RKHS  $\mathcal{H}_k^c$ and suppose that  $\ell(f, (x, y)) := ||f(x) - y||_2^2$ :

<span id="page-4-3"></span><span id="page-4-1"></span>
$$
f_{\tilde{D}}(x)^T = k(x, X_{AB})[k(X_{AB}, X_{AB}) + (N_A + N_B)\lambda I]^{-1}Y_{AB},
$$
\n(11)

where  $(N_A + N_B) \times d$  matrix  $X_{AB}$  is the matrix corresponding to the features of D,  $(N_A + N_B) \times c$ matrix  $Y_{AB}$  is the matrix corresponding to the labels of  $\tilde{D}$ ,  $k(x, X_{AB})$  is a  $1 \times (N_A + N_B)$  matrix,  $k(\mathbf{X}_{AB}, \mathbf{X}_{AB})$  is a  $(N_A + N_B) \times (N_A + N_B)$  matrix with  $[k(\mathbf{X}_{AB}, \mathbf{X}_{AB})]_{ij} = k(x_i, x_j)$ , and  $Y_{AB}$  is a  $(N_A + N_B) \times c$  matrix with  $Y_{AB} = [y_1, y_2, \dots, y_{(N_A+N_B)}]^T$ . Hence, we can express the conflict loss  $\mathcal{L}_{conflict}$  as

$$
\mathcal{L}_{conflict} = \frac{1}{N_A + N_B} ||\mathbf{Y}_{AB} - k(\mathbf{X}_{AB}, \mathbf{X}_{AB})[k(\mathbf{X}_{AB}, \mathbf{X}_{AB}) + (N_A + N_B)\lambda \mathbf{I}]^{-1} \mathbf{Y}_{AB}||_2^2.
$$
 (12)

We can obtain the upper bound of  $\mathcal{L}_{conflict}$  as Theorem [1.](#page-4-0)

<span id="page-4-0"></span>Theorem 1 (Upper bound of conflict loss). *The conflict loss* L*conflict can be bounded as*

$$
\mathcal{L}_{conflict} \leq \frac{1}{N_A + N_B} Tr(\boldsymbol{I} - k(\boldsymbol{X}_{AB}, \boldsymbol{X}_{AB})[k(\boldsymbol{X}_{AB}, \boldsymbol{X}_{AB}) + (N_A + N_B)\lambda \boldsymbol{I}]^{-1})^2 ||\boldsymbol{Y}_{AB}||_2^2 \qquad (13)
$$

*where Tr is the trace operator,*  $k(X_{AB}, X_{AB})$  *is a*  $(N_A + N_B) \times (N_A + N_B)$  *matrix, and*  $Y_{AB}$  *is*  $a (N_A + N_B) \times c$  *matrix.* 

The proof of Theorem [1](#page-4-0) can be found in Appendix [A.3.](#page-13-0) From Theorem [1,](#page-4-0) we know that the conflict loss can be characterized by  $\text{Tr}(\bm{I}-k(\bm{X}_{AB},\bm{X}_{AB})[k(\bm{X}_{AB},\bm{X}_{AB})+(N_A+N_B)\lambda \bm{I}]^{-1})$ . However, in the latter sections, we do not utilize  $\text{Tr}(\bm{I}-k(\bm{X_{AB}},\bm{X_{AB}})[k(\bm{X_{AB}},\bm{X_{AB}})+ (N_A+N_B)\lambda \bm{I}]^{-1})$ to construct trigger pattern generalization algorithm; instead, we use Eq. [\(12\)](#page-4-1) directly. It is because Eq. [\(12\)](#page-4-1) can be computed more precisely although  $Tr(I - k(X_{AB}, X_{AB})[k(X_{AB}, X_{AB}) + (N_A +$  $(N_B)\lambda I]^{-1}$ ) and Eq. [\(12\)](#page-4-1) have similar computational cost.

Projection Loss. To derive the upper bound of the projection loss, we first derive Lemma [1.](#page-4-2)

<span id="page-4-2"></span>**Lemma 1** (Projection lemma). *Given a synthetic dataset*  $S = \{(x_s, y_s)\}_{s=1}^{N_S}$ , and a dataset  $\tilde{D}$  =  $\{(x_i, y_i)\}_{i=1}^{N_A+N_B}$  where  $(N_A+N_B)$  is the number of the samples of  $\tilde{D}$ *. Suppose the kernel matrix*  $k(\boldsymbol{X}_{\mathcal{S}}, \boldsymbol{X}_{\mathcal{S}})$  is invertible, then we have

$$
k(\cdot, x_i) = \underbrace{k(\cdot, \mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}}, x_i)}_{\in \mathcal{H}_{\mathcal{S}}} + \underbrace{[k(\cdot, x_i) - k(\cdot, \mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}}, x_i)]}_{\in \mathcal{H}_{\mathcal{S}}^{\perp}}, \quad \forall (x_i, y_i) \in \tilde{D} \quad (14)
$$

 $where$   $\mathcal{H_S}:= span(\{k(\cdot,x_s)\in\mathcal{H}_k | (x_s,y_s)\in\mathcal{S}\})$  and  $\mathcal{H}_\mathcal{S}^\perp$  is the collection of functions orthogonal *to*  $H_S$  *corresponding to the inner product*  $\langle \cdot, \cdot \rangle_{H_k}$ *. Thus,*  $k(\cdot, X_S)k(X_S, X_S)^{-1}k(X_S, x_i)$  *is the solution of the optimization problem:*

$$
\underset{f \in \mathcal{H}_S}{\arg \min} \sum_{(x_s, y_s) \in \mathcal{S}} \|f(x_s) - k(x_s, x_i)\|_2^2. \tag{15}
$$

The proof of Lemma [1](#page-4-2) can be found in Appendix [A.2.](#page-12-0) Now, we turn to the scenario of the KIP-based backdoor attack. Given a mixed dataset  $\tilde{D} = D_A \cup D_B$  where  $D_A \sim \mathcal{D}_A^{N_A}$  and  $D_B \sim \mathcal{D}_B^{N_B}$ . We also constrained models in the RKHS  $\mathcal{H}_k^c$  and suppose  $\ell(f,(x,y)) := ||\tilde{f(x)} - y||_2^2$ . With the help of Lemma [1,](#page-4-2) we can obtain the following theorem:

<span id="page-5-2"></span>Theorem 2 (Upper bound of projection loss). *Suppose the kernel matrix of the synthetic dataset*  $k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})$  is invertible,  $f_{\mathcal{S}}$  is the model trained on the synthetic dataset S with the regularization *term*  $\lambda_S$ *, where the projection loss*  $\mathcal{L}_{project} = \min_S \mathbb{E}_{(x,y)\sim \tilde{D}} \ell(f_S,(x,f_{\tilde{D}}(x)))$  *can be bounded as* 

<span id="page-5-5"></span>
$$
\mathcal{L}_{project} \leq \sum_{(x_i, y_i) \in \tilde{D}} \min_{\mathbf{X}_{\mathcal{S}}} \sum_{j=1}^c \frac{|\alpha_{i,j}|^2}{N_A + N_B} ||k(\mathbf{X}_{AB}, x_i) - k(\mathbf{X}_{AB}, \mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}}, x_i)||_2^2.
$$
 (16)

 $\omega_{i,j}:=[[k(\bm{X}_{AB},\bm{X}_{AB})+(N_A+N_B)\lambda \bm{I}]^{-1}\bm{Y}_{AB}]_{i,j}$ , which is the weight of  $k(\cdot,x_i)$  corre $s$ ponding to  $f_{\tilde{D}'}^{j}$  ,  $\bm{X}_{AB}$  is the  $(N_A+N_B)\times d$  matrix corresponding to the features of  $\tilde{D}$ ,  $\bm{X}_{\mathcal{S}}$  is the  $N_S \times d$  *matrix corresponding to the features of* S,  $Y_{AB}$  *is the*  $(N_A + N_B) \times c$  *matrix corresponding to the labels of D,*  $Y_s$  *is the*  $N_s \times c$  *matrix corresponding to the labels of* S.

The proof of Theorem [2](#page-5-2) can be found in Appendix [A.4.](#page-14-0) In Theorem [2,](#page-5-2) we first characterize the natural information loss when compressing the information of D into an arbitrary dataset  $S$ , and then bound the information loss for the synthetic dataset  $S^*$  generated by dataset compression by taking the minimum. This formulation gives some insight into the construction of our trigger generation algorithm, which is discussed in the later section.

Generalization Gap. Finally, for the generalization gap, we follow the existing theoretical results (Theorem 3.3 in [\(Mohri et al., 2012\)](#page-10-15)), but modify them a bit. Let  $\mathcal{G} = \{g : (x, y) \mapsto ||f(x) ||y||_2^2 |f \in \mathcal{H}_k^c$ . Assume that the distribution D is distributed in a bounded region, and that  $\mathcal{G} \subset$  $C^1$  and the norm of the gradient of  $g \in \mathcal{G}$  have a common non-trivial upper bound. Namely,  $||(x,y)-(x',y')||_2 \leq \Gamma_{\mathcal{D}}$  for any sample which is picked from  $\mathcal{D}$  and  $||\nabla g||_2 \leq L_{\mathcal{D}}$ . Then we can obtain Theorem [3.](#page-5-3)

<span id="page-5-3"></span>Theorem 3 (Upper bound of generalization gap). *Given a* N*-sample dataset* D*, sampled from the distribution*  $\mathcal{D}$ *, the following generalization gap holds for all*  $g \in \mathcal{G}$  *with probability at least*  $1 - \delta$ *:* 

<span id="page-5-4"></span>
$$
\mathbb{E}_{(x,y)\sim\mathcal{D}}[g((x,y))] - \sum_{(x_i,y_i)\in\mathcal{D}} \frac{g((x_i,y_i))}{N} \leq 2\hat{\mathfrak{R}}_D(\mathcal{G}) + 3L_{\mathcal{D}}\Gamma_{\mathcal{D}}\sqrt{\frac{\log\frac{2}{\delta}}{2N}},\tag{17}
$$

*where* X is the matrix of the features of D and  $\Re_D(\mathcal{G})$  is the empirical Rademacher's complexity.

The proof of Theorem [3](#page-5-3) can be found in Appendix [A.5.](#page-15-0) We know from Theorem [3](#page-5-3) that the upper bound of the generalization gap is characterized by two factors,  $\Re_D(\mathcal{G})$  and  $\Gamma_\mathcal{D}$ . The lower  $\Re_D(\mathcal{G})$ and  $\Gamma_{\mathcal{D}}$  imply the lower generalization gap. We usually assume  $k(x, x) \leq r^2$  and  $\sqrt{\langle f, f \rangle_{\mathcal{H}_k}} \leq \Lambda$ (as in Theorem 6.12 of [\(Mohri et al., 2012\)](#page-10-15)). Under this setting, we can ignore  $\mathfrak{R}_D(\mathcal{G})$  for the upper bound of the generalization gap and only focus on  $\Gamma_{\mathcal{D}}$ . ASR relates to the risk for  $\mathcal{D}_B$  and hence corresponds to the generalization gap evaluated on  $\mathcal{D}_B$ . This theoretical consequence can be used to explain the phenomenon that ASR of the backdoor attack increases as we enlarge the trigger size.

<span id="page-5-0"></span>

### 3.2 THEORY-INDUCED BACKDOOR: SIMPLE-TRIGGER

Consider  $D$  in Theorem [3](#page-5-3) as  $D_B$  and the corresponding dataset D as  $D_B$ . Conventionally, a cell of the mask m in Eq. [\(7\)](#page-2-3) is 1 it corresponds to a trigger, and is 0 otherwise. Recall that the definition of  $\mathcal{D}_B$  in Eq. [\(7\)](#page-2-3), it is clear that the  $\Gamma_{\mathcal{D}_B}$  will monotonely decrease from  $\Gamma_{\mathcal{D}_A}$  to 0 as we enlarge the trigger size. If we enlarge the trigger size, the  $\Gamma_{\mathcal{D}_B}$  drops to zero, which implies that the corresponding generalization gap will be considerably small. Thus, the success of the large trigger pattern can be attributed to its relatively small generalization gap.

So, given an image of size  $m \times n$  ( $m \leq n$ ), simple-trigger generates a trigger of size  $m \times n$ . The default pattern for the trigger generated by simple-trigger is whole-white. In fact, since the generalization gap is irrelevant to the trigger pattern, we do not impost any pattern restrictions.

<span id="page-5-1"></span>

### 3.3 THEORY-INDUCED BACKDOOR: RELAX-TRIGGER

In simple-trigger, we optimize the trigger through only the generalization gap. However, we know that ASR can be determined by conflict loss, projection loss, and generalization gap because of Theorems [1](#page-4-0)∼[3](#page-5-3) (i.e., all are related to  $\mathcal{D}_B$ ). On the other hand, CTA is related to conflict loss and projection loss, because the generalization gap is irrelevant to CTA. That is, Eq. [\(17\)](#page-5-4) evaluated on  $\mathcal{D}_A$  is a constant as we modify the trigger. As a result, the lower conflict loss, projection loss, and generalization gap imply a backdoor attack with greater ASR and CTA. Therefore, relax-trigger aims to construct a trigger whose corresponding  $\mathcal{D}_B$  make Eq. [\(12\)](#page-4-1), Eq. [\(16\)](#page-5-5), and  $\Gamma_{\mathcal{D}_B}$  sufficiently low. The computation procedures of relax-trigger can be found in Algorithm [2](#page-16-1) of Appendix [A.7.](#page-16-2)

Suppose  $D_A$ ,  $N_A$  and  $N_B$  are fixed. To reduce the bound in Eq. [\(12\)](#page-4-1), one considers  $D_B$  as a function depending on the trigger T and then uses the optimizer to find the optimal trigger  $T^*$ . In this sense, we solve the following optimization problem

$$
\underset{T}{\arg\min} \|\mathbf{Y}_{AB} - k(\mathbf{X}_{AB}, \mathbf{X}_{AB})[k(\mathbf{X}_{AB}, \mathbf{X}_{AB}) + (N_A + N_B)\lambda \mathbf{I}]^{-1}\mathbf{Y}_{AB}\|_2^2. \tag{18}
$$

On the other hand, a low  $\Gamma_{\mathcal{D}_B}$  can be realized by enlarging the trigger as mentioned in Section [3.2.](#page-5-0)

Finally, to make Eq. [\(16\)](#page-5-5) sufficiently low, we consider  $\mathcal{D}_B$  as a function of the trigger T, and then directly optimize

<span id="page-6-1"></span>
$$
\underset{T}{\arg\min} \left\{ \sum_{(x_i, y_i) \in \tilde{D}} \underset{\mathbf{X}_{\mathcal{S}}}{\min} \sum_{j=1}^{c} |\alpha_{i,j}|^2 \| k(\mathbf{X}_{AB}, x_i) - k(\mathbf{X}_{AB}, \mathbf{X}_{\mathcal{S}}) k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1} k(\mathbf{X}_{\mathcal{S}}, x_i) \|_2^2 \right\}.
$$
 (19)

However, Eq. [\(19\)](#page-6-1) is a bi-level optimization problem that is difficult to solve. Instead, we set the synthetic dataset S in Eq. [\(19\)](#page-6-1) to  $S_A$ , which is the distilled dataset from  $D_A$ . Then, the two-level optimization problem can be converted into a one-level optimization problem below.

<span id="page-6-2"></span>
$$
\arg\min_{T} \left\{ \sum_{(x_i, y_i) \in \tilde{D}} \sum_{j=1}^c |\alpha_{i,j}|^2 \|k(\mathbf{X}_{AB}, x_i) - k(\mathbf{X}_{AB}, \mathbf{X}_{\mathcal{S}_A})k(\mathbf{X}_{\mathcal{S}_A}, \mathbf{X}_{\mathcal{S}_A})^{-1} k(\mathbf{X}_{\mathcal{S}_A}, x_i) \|_{2}^2 \right\}.
$$
(20)

Eq. [\(20\)](#page-6-2) can be easily solved by directly applying optimizers like Adam [\(P. Kingma & Ba, 2015\)](#page-10-16). Eq. [\(20\)](#page-6-2) aims to find a trigger T such that  $\ddot{D}$  generated from  $D_A$  and  $D_B$  will be compressed into the neighborhood of  $S_A \subset (\mathcal{X} \times \mathcal{Y})^{N_S}$ , which guarantees that CTA of the model trained on the distilled  $\tilde{D}$  is similar to CTA of the model trained on the distilled  $D_A$ . Overall, relax-trigger solves the following optimization,

$$
\underset{T}{\arg\min} \{ \sum_{(x_i, y_i) \in \tilde{D}} \sum_{j=1}^{c} |\alpha_{i,j}|^2 \| k(\mathbf{X}_{AB}, x_i) - k(\mathbf{X}_{AB}, \mathbf{X}_{S_A}) k(\mathbf{X}_{S_A}, \mathbf{X}_{S_A})^{-1} k(\mathbf{X}_{S_A}, x_i) \|_2^2
$$

<span id="page-6-3"></span>
$$
+\rho\|\mathbf{Y}_{AB} - k(\mathbf{X}_{AB}, \mathbf{X}_{AB})[k(\mathbf{X}_{AB}, \mathbf{X}_{AB}) + (N_A + N_B)\lambda \mathbf{I}]^{-1}\mathbf{Y}_{AB}\|_2^2\},\tag{21}
$$

where  $\rho > 0$  is the penalty parameter, m is the previously chosen mask, the malicious dataset is defined as  $D_B = \{(x_b, y_b) = ((1 - m) \odot x_a + m \odot T, y_T) | (x_a, y_a) \in D_A\}$ . We particularly note that Eq. [\(19\)](#page-6-1) is converted into Eq. [\(20\)](#page-6-2) because we use  $S_A$  to replace the minimization over S.

relax-trigger is different from DOORPING in [\(Liu et al., 2023c\)](#page-10-1). DOORPING generates the trigger during the process of sample compression. In other words, DOORPING is induced by solving a bilevel optimization problem. However, relax-trigger is induced by a one-level optimization problem (Eq. [\(21\)](#page-6-3)). The design rationale of relax-trigger is different from DOORPING. DOORPING aims to find the globally best trigger but consumes a significant amount of computation time. On the other hand, through our theoretical framework, relax-trigger aims to find the trigger that reliably compresses the corresponding  $\ddot{D}$  into the neighborhood of our  $\mathcal{S}_A$  with the benefit of time efficiency.

## <span id="page-6-0"></span>4 EVALUATION

### 4.1 EXPERIMENTAL SETTING

Dataset. Two datasets are chosen for measuring the backdoor performance.

- CIFAR-10 is a 10-class dataset with 6000  $32 \times 32$  color images per class. CIFAR-10 is split into 50000 training images and 10000 testing images.
- GTSRB contains 43 classes of traffic signs with 39270 images, which are split into 26640 training images and 12630 testing images. We resize all images to  $32 \times 32$  color images.

Dataset Distillation and Backdoor Attack. We use KIP [\(Nguyen et al., 2021\)](#page-10-2) to implement backdoor attacks with the neural tangent kernel (NTK) induced by a 3-layer neural network, which has the same structure in the Colab notebook of [\(Nguyen et al., 2021\)](#page-10-2). We also set the optimizer to Adam [\(P. Kingma & Ba, 2015\)](#page-10-16), the learning rate to 0.01, and the batch size to  $10 \times$  number of class for each dataset. We run KIP with 1000 training steps to generate a distilled dataset. We perform 3 independent runs for each KIP-based backdoor attack to examine the performance.

Evaluation Metrics. We consider two metrics, clean test accuracy (CTA) and attack success rate (ASR). Consider  $S$  as a distilled dataset from the KIP-based backdoor attack. CTA is defined as the test accuracy of the model trained on  $S$  and evaluated on the normal (clean) test dataset, while ASR is defined as the test accuracy of the model trained on  $S$  and evaluated on the trigger test dataset.

Defense for Backdoor Attack. In this paper we consider eight existing defenses, SCAn [\(Tang](#page-10-12) [et al., 2021\)](#page-10-12), AC [\(Chen et al., 2018\)](#page-9-14), SS [\(Tran et al., 2018\)](#page-10-17), Strip (modified as a poison cleaner) [\(Gao et al., 2019\)](#page-9-15), ABL [\(Li et al., 2021a\)](#page-9-16), NAD [\(Li et al., 2021b\)](#page-9-17), STRIP (backdoor input filter) [\(Gao et al., 2019\)](#page-9-15), FP [\(Liu et al., 2018a\)](#page-10-18), to investigate the ability to defend against KIP-based backdoor attack. The implementation of the above defenses is from the backdoor-toolbox<sup>[2](#page-7-0)</sup>.

## 4.2 EXPERIMENTAL RESULTS

Performance of **simple-trigger**. We performed a series of experiments to demonstrate the effectiveness of simple-trigger. In our setting,  $N_{\mathcal{S}}$  is set to 10  $\times$  number of classes and 50  $\times$ number of classes for each dataset. We also configurated the trigger as  $2 \times 2$ ,  $4 \times 4$ ,  $8 \times 8$ ,  $16 \times 16$ ,  $32 \times 32$  white square patterns. The corresponding results are shown in Table [1.](#page-7-1) The experiment results suggest that CTA and ASR of simple-trigger increase as we enlarge the trigger size, which is consistent with our theoretical analysis (Theorem [3\)](#page-5-3). One can see that for the  $32 \times 32$  white square trigger, ASR can achieve 100% without sacrificing CTA.

<span id="page-7-1"></span>

| Data. $(Size)$ Trig. | None         | $2 \times 2$ |              | $4 \times 4$ |              | $8 \times 8$                       |              | $16 \times 16$ |              | $32 \times 32$ |              |
|----------------------|--------------|--------------|--------------|--------------|--------------|------------------------------------|--------------|----------------|--------------|----------------|--------------|
|                      | CTA(%)       | CTA(%)       | $ASR(\%)$    | CTA(%)       | $ASR(\%)$    | CTA(%)                             | $ASR(\%)$    | CTA(%)         | $ASR(\%)$    | CTA(%)         | $ASR(\%)$    |
| CIFAR-10 (100)       | 42.55(0.13)  | 41.78 (0.22) | 65.73(0.80)  | 41.53(0.31)  | 90.59 (0.22) | 41.46(0.32)                        | 98.29(0.18)  | 41.55(0.43)    | 99.94 (0.05) | 41.70(0.25)    | 100.00(0.00) |
| CIFAR-10 (500)       | 44.52 (0.23) | 43.89(0.13)  | 82.36(0.39)  | 43.85(0.23)  |              | $\mid$ 92.89 (0.16)   43.60 (0.23) | 98.19(0.16)  | 43.70 (0.40)   | 99.88 (0.07) | 43.66(0.40)    | 100.00(0.00) |
| GTSRB (430)          | 69.27(0.19)  | 67.06 (0.74) | 74.14 (0.50) | 67.01(0.69)  | 81.46(0.37)  | 66.98(0.64)                        | 89.63 (0.58) | 67.10(0.63)    | 98.43 (0.13) | 67.56(0.60)    | 100.00(0.00) |
| GTSRB (2150)         | 72.07 (0.20) | 70.87 (0.27) | 76.79 (1.08) | 70.90(0.25)  | 81.93 (0.62) | 70.92(0.31)                        | 90.48 (0.74) | 70.98 (0.22)   | 98.89 (0.17) | 71.27(0.24)    | 100.00(0.00) |

Table 1: Performance of simple-trigger on CIFAR-10 and GTSRB (mean and standard deviation).

**Performance of relax-trigger.** Here, we relax the setting of the mask m; i.e., each component of  $m$  is defined to be 0.3, instead of 1. This can be regarded as an increase in the trigger's transparency (the level of invisibility) for mixing an image and the trigger. Recall the definition of  $\mathcal{D}_B$  in (Eq. [7\)](#page-2-3). From theory point of view, under such a mask m,  $\Gamma_{\mathcal{D}_B}$  will drop to  $0.3 * \Gamma_{\mathcal{D}_A} > 0$ , as we enlarge the trigger. Hence, we cannot reduce the generalization gap considerably as in the experiments of simple-trigger. It turns out that to derive better CTA and ASR, we resort to consider relax-trigger.

The result is presented in Table [2.](#page-7-2) We compare the performance (CTA and ASR) between simpletrigger  $(32 \times 32)$  white square), DOORPING and relax-trigger. For CIFAR-10, relax-trigger increases the ASR about 24% from simple-trigger without losing CTA. For GTSRB, relax-trigger not only increases the ASR about 30%, but also slightly increases the CTA. On the other hand, relax-trigger possesses higher CTA and ASR compared to DOORPING. These results confirm the effectiveness of relax-trigger. The trigger patterns of relax-trigger are visualized in Figure [1.](#page-8-0)

<span id="page-7-2"></span>

| Dataset  | Size\Trig. | simple-trigger (baseline) |              | relax-trigger |               | DOORPING     |               |
|----------|------------|---------------------------|--------------|---------------|---------------|--------------|---------------|
|          |            | CTA (%)                   | ASR (%)      | CTA (%)       | ASR (%)       | CTA (%)      | ASR (%)       |
| CIFAR-10 | 100        | 41.40 (0.06)              | 75.92 (1.19) | 41.66 (0.74)  | 100.00 (0.00) | 36.35 (0.42) | 80.00 (40.00) |
| CIFAR-10 | 500        | 42.98 (0.13)              | 75.79 (0.58) | 43.64 (0.40)  | 100.00 (0.00) |              |               |
| GTSRB    | 430        | 67.02 (0.07)              | 62.74 (0.23) | 68.73 (0.67)  | 95.26 (0.54)  | 68.03 (0.92) | 90.00 (30.00) |
| GTSRB    | 2150       | 70.28 (0.07)              | 62.65 (1.12) | 71.54 (0.33)  | 95.08 (0.33)  |              |               |

Table 2: Performance of relax-trigger on CIFAR-10 and GTSRB (mean and standard deviation).

Off-the-shelf Backdoor Defenses. We examine whether simple-trigger and relax-trigger can survive backdoor detection and cleansing. Here, we utilize backdoor-toolbox and retrain the distilled dataset on ResNet (default setting in backdoor-toolbox) to compute CTA and ASR. In our experimental results, the term "None" denotes no defense.

<span id="page-7-0"></span><sup>&</sup>lt;sup>2</sup>Available at <https://github.com/vtu81/backdoor-toolbox>.

<span id="page-8-0"></span>Image /page/8/Picture/1 description: This is a highly pixelated and abstract image with a vibrant color palette. The dominant colors are bright blues, greens, and purples, with accents of red and yellow. The overall impression is chaotic and energetic, with no discernible objects or patterns.

Image /page/8/Picture/2 description: A colorful, abstract, pixelated image with a variety of colors including red, yellow, green, blue, and black. The colors are arranged in a chaotic, mosaic-like pattern, with no discernible shapes or objects.

Image /page/8/Picture/3 description: A close-up, pixelated image shows a colorful, abstract representation of a face or figure against a black background. The figure is composed of various colors including red, blue, white, green, and yellow, with a prominent white rectangular shape in the center. Below the image, text reads "(c) GTSRB (size 430)".

Image /page/8/Picture/4 description: A colorful, pixelated image shows a cluster of bright lights against a dark background. The lights are a mix of white, blue, pink, and green, with some red and yellow hues visible at the edges. The overall impression is of a distant celestial object or a vibrant abstract pattern.

(a) CIFAR10 (size 100)  $\qquad$  (b) CIFAR10 (size 500)  $\qquad$  (c) GTSRB (size 430)  $\qquad$  (d) GTSRB (size 2150)

|  | Figure 1: Triggers generated by relax-trigger for GTSRB and CIFAR. |  |
|--|--------------------------------------------------------------------|--|
|  |                                                                    |  |

<span id="page-8-1"></span>

| Trig. Def. |              | None          |              | SCAn          |              | AC            |              | SS            | Strip        |               |
|------------|--------------|---------------|--------------|---------------|--------------|---------------|--------------|---------------|--------------|---------------|
|            | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       |
| 2 x 2      | 23.18 (1.24) | 13.98 (8.36)  | 24.39 (2.23) | 18.17 (2.74)  | 23.84 (1.12) | 10.57 (3.52)  | 22.60 (1.62) | 11.64 (1.42)  | 25.08 (0.48) | 12.79 (5.25)  |
| 4 x 4      | 23.18 (1.40) | 25.26 (9.67)  | 24.67 (0.97) | 15.73 (3.69)  | 24.37 (1.29) | 14.00 (5.84)  | 21.98 (3.30) | 10.81 (2.44)  | 24.28 (0.39) | 17.68 (5.47)  |
| 8 x 8      | 25.69 (1.06) | 13.35 (5.38)  | 26.40 (0.11) | 14.08 (3.72)  | 23.24 (1.96) | 9.19 (5.53)   | 21.49 (1.77) | 7.10 (4.61)   | 25.13 (0.74) | 12.09 (5.52)  |
| 16 x 16    | 25.90 (5.76) | 81.29 (2.96)  | 26.39 (2.96) | 49.66 (9.66)  | 25.85 (1.57) | 55.26 (10.94) | 24.03 (1.66) | 40.03 (27.29) | 26.22 (0.75) | 41.36 (40.18) |
| 32 x 32    | 28.95 (1.56) | 100.00 (0.00) | 28.28 (1.45) | 66.67 (47.14) | 25.35 (2.05) | 66.67 (47.14) | 22.21 (1.02) | 66.67 (47.14) | 25.68 (2.04) | 0.00 (0.00)   |
| Trig. Def. |              |               |              | ABL           |              | NAD           |              | STRIP         |              | FP            |
|            |              |               | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       |
| 2 x 2      |              |               | 13.31 (2.43) | 1.38 (1.14)   | 31.74 (1.90) | 5.45 (0.78)   | 20.91 (1.07) | 12.63 (7.61)  | 13.05 (1.33) | 21.85 (30.90) |
| 4 x 4      |              |               | 13.12 (2.04) | 13.46 (13.00) | 30.87 (3.23) | 7.86 (4.36)   | 20.95 (1.15) | 19.08 (4.01)  | 13.11 (1.43) | 73.25 (12.25) |
| 8 x 8      |              |               | 14.10 (0.47) | 24.92 (34.63) | 33.05 (1.04) | 10.82 (5.21)  | 23.07 (1.01) | 11.84 (4.84)  | 15.27 (1.77) | 2.81 (0.66)   |
| 16 x 16    |              |               | 14.56 (2.67) | 35.47 (36.63) | 32.77 (1.66) | 22.25 (4.21)  | 23.35 (0.30) | 66.53 (10.35) | 15.54 (0.21) | 22.94 (32.37) |

Table 3: Defenses for simple-trigger on CIFAR-10 with distilled dataset size = 100.

For simple-trigger, we find that both CTA and ASR of None increase as we enlarge the trigger size. Moreover, both CTA and ASR of None increase as we enlarge the size of the distilled dataset. The above implies that simple-trigger is more suitable for large-size distilled datasets. Since the CTA and ASR increase as we enlarge the trigger, we focus on  $32 \times 32$  trigger images in the following discussion. In the case of CIFAR-10, for size 100 (see Table [3\)](#page-8-1), we can find that ASR of NAD is still 1. That is, NAD fails to remove the backdoor. For the other defenses, the CTA drops over 7%, though they can reduce the ASR. Hence, we conclude that these defenses are not effective. For size 500 (see Table [5](#page-17-0) in Appendix [A.8\)](#page-16-3), the ASR of SCAn is still 1, implying that SCAn fails to remove the backdoor. The other defenses, SS, Strip, ABL, STRIP, and FP considerably compromise the CTA. Overall, the above results also suggest that the defenses may be more successful when we increase the size of the distilled dataset. On the other hand, for GTSRB (see Tabel [6](#page-17-1) and Table [7](#page-17-2) in Appendix [A.8\)](#page-16-3), we also reach a similar conclusion.

For relax-trigger (see Table [8](#page-17-3) in Appendix [A.8\)](#page-16-3), all defenses considered in this paper cannot effectively remove the backdoor. In particular, in the case of CIFAR-10, for size 100, SCAn, AC, Strip, and ABL do not reduce the ASR. They even increase ASR to some degree. On the other hand, SS, STRIP, and FP also compromise the CTA too much. Lastly, though NAD reaches a better defense result; however, the corresponding ASR still remains about 50% of None's ASR. Essentially, this suggests that NAD cannot completely defend against relax-trigger. For the other defenses, the ASR still remains over 30% of None's ASR. These defenses are ineffective against relax-trigger.

In the case of GTSRB, for size 430, we can also find that SCAn, NAD, and STRIP cannot successfully remove the backdoor. The ASR still remains over 70% of None's ASR. Besides, we can find that AC, SS, Stip, ABL, and FP still compromise the CTA too much. Finally, for size 2150, AC, Strip, NAD, and STRIP still remain ASR over 50% of None's ASR. Furthermore, SCAn, ABL, and FP even increase the ASR. In addition, SS decreases the CTA by about 45% of None's CTA. To sum up, relax-trigger shows strong backdoor resiliency against all the tested defenses.

# 5 CONCLUSION

In this paper, we present a novel theoretical framework based on the kernel inducing points (KIP) method to study the interplay between backdoor attacks and dataset distillation. The backdoor effect is characterized by three key components: conflict loss, projection loss, and generalization gap, along with two theory-induced attacks, simple-trigger and relax-trigger. Our simple-trigger proves that enlarged trigger size leads to improved ASR without sacrificing CTA. Our relax-trigger presents a new and resilient backdoor attack scheme that either completely breaks or significantly weakens eight existing backdoor defense methods. Our study provides novel theoretical insights, unveils new risks of dataset distillation-based backdoor attacks, and calls for better defenses.

## **REFERENCES**

- <span id="page-9-3"></span>Nachman Aronszajn. Theory of reproducing kernels. *Transactions of the American mathematical society*, 68(3):337–404, 1950.
- <span id="page-9-12"></span>Mauro Barni, Kassem Kallas, and Benedetta Tondi. A new backdoor attack in cnns by training set corruption without label poisoning. In *2019 IEEE International Conference on Image Processing (ICIP)*, pp. 101–105. IEEE, 2019.
- <span id="page-9-4"></span>Alain Berlinet and Christine Thomas-Agnan. *Reproducing kernel Hilbert spaces in probability and statistics*. Springer Science & Business Media, 2011.
- <span id="page-9-14"></span>Bryant Chen, Wilka Carvalho, Nathalie Baracaldo, Heiko Ludwig, Benjamin Edwards, Taesung Lee, Ian Molloy, and Biplav Srivastava. Detecting backdoor attacks on deep neural networks by activation clustering. In *AAAI Artificial Intelligence Safety Workshop (SafeAI)*, 2018.
- <span id="page-9-11"></span>Xinyun Chen, Chang Liu, Bo Li, Kimberly Lu, and Dawn Song. Targeted backdoor attacks on deep learning systems using data poisoning. *arXiv preprint arXiv:1712.05526*, 2017.
- <span id="page-9-0"></span>Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning (ICML)*, 2022.
- <span id="page-9-15"></span>Yansong Gao, Change Xu, Derui Wang, Shiping Chen, Damith C Ranasinghe, and Surya Nepal. Strip: A defence against trojan attacks on deep neural networks. In *Proceedings of the 35th Annual Computer Security Applications Conference*, pp. 113–125, 2019.
- <span id="page-9-5"></span>Benyamin Ghojogh, Ali Ghodsi, Fakhri Karray, and Mark Crowley. Reproducing kernel hilbert space, mercer's theorem, eigenfunctions, nyström method, and use of kernels in machine learning: Tutorial and survey. *arXiv preprint arXiv:2106.08443*, 2021.
- <span id="page-9-10"></span>Tianyu Gu, Kang Liu, Brendan Dolan-Gavitt, and Siddharth Garg. Badnets: Evaluating backdooring attacks on deep neural networks. *IEEE Access*, 7:47230–47244, 2019.
- <span id="page-9-8"></span>Bobby He, Balaji Lakshminarayanan, and Yee Whye Teh. Bayesian deep ensembles via the neural tangent kernel. *Advances in neural information processing systems*, 33:1010–1022, 2020.
- <span id="page-9-7"></span>Arthur Jacot, Franck Gabriel, and Clément Hongler. Neural tangent kernel: Convergence and generalization in neural networks. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-9-6"></span>George Kimeldorf and Grace Wahba. Some results on tchebycheffian spline functions. *Journal of mathematical analysis and applications*, 33(1):82–95, 1971.
- <span id="page-9-1"></span>Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*, 2022a.
- <span id="page-9-9"></span>Jaehoon Lee, Lechao Xiao, Samuel Schoenholz, Yasaman Bahri, Roman Novak, Jascha Sohl-Dickstein, and Jeffrey Pennington. Wide neural networks of any depth evolve as linear models under gradient descent. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-9-2"></span>Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning (ICML)*, 2022b.
- <span id="page-9-16"></span>Yige Li, Xixiang Lyu, Nodens Koren, Lingjuan Lyu, Bo Li, and Xingjun Ma. Anti-backdoor learning: Training clean models on poisoned data. *Advances in Neural Information Processing Systems*, 34:14900–14912, 2021a.
- <span id="page-9-17"></span>Yige Li, Xixiang Lyu, Nodens Koren, Lingjuan Lyu, Bo Li, and Xingjun Ma. Neural attention distillation: Erasing backdoor triggers from deep neural networks. In *International Conference on Learning Representations (ICLR)*, 2021b.
- <span id="page-9-13"></span>Yuezun Li, Yiming Li, Baoyuan Wu, Longkang Li, Ran He, and Siwei Lyu. Invisible backdoor attack with sample-specific triggers. In *Proceedings of the IEEE/CVF international conference on computer vision*, pp. 16463–16472, 2021c.

- <span id="page-10-18"></span>Kang Liu, Brendan Dolan-Gavitt, and Siddharth Garg. Fine-pruning: Defending against backdooring attacks on deep neural networks. In *International Symposium on Research in Attacks, Intrusions and Defenses (RAID)*, 2018a.
- <span id="page-10-5"></span>Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *Advances in Neural Information Processing Systems (NeurIPS)*, 2022.
- <span id="page-10-0"></span>Tengjun Liu, Ying Chen, and Wanxuan Gu. Copyright-certified distillation dataset: Distilling one million coins into one bitcoin with your private key. In *Proceedings of the AAAI Conference on Artificial Intelligence*, 2023a.
- <span id="page-10-6"></span>Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. In *International Conference on Computer Vision (ICCV)*, 2023b.
- <span id="page-10-7"></span>Yingqi Liu, Shiqing Ma, Yousra Aafer, Wen-Chuan Lee, Juan Zhai, Weihang Wang, and Xiangyu Zhang. Trojaning attack on neural networks. In *25th Annual Network And Distributed System Security Symposium (NDSS 2018)*. Internet Soc, 2018b.
- <span id="page-10-1"></span>Yugeng Liu, Zheng Li, Michael Backes, Yun Shen, and Yang Zhang. Backdoor attacks against dataset distillation. *Network and Distributed System Security (NDSS) Symposium*, 2023c.
- <span id="page-10-11"></span>Yunfei Liu, Xingjun Ma, James Bailey, and Feng Lu. Reflection backdoor: A natural backdoor attack on deep neural networks. In *Computer Vision–ECCV 2020: 16th European Conference, Glasgow, UK, August 23–28, 2020, Proceedings, Part X 16*, pp. 182–199. Springer, 2020.
- <span id="page-10-3"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *Annual Conference on Neural Information Processing Systems (NeurIPS)*, 2022.
- <span id="page-10-4"></span>Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. *International Conference on Machine Learning (ICML)*, 2023.
- <span id="page-10-15"></span>Mehryar Mohri, Afshin Rostamizadeh, and Ameet Talwalkar. *Foundations of Machine Learning*. The MIT Press, 2012. ISBN 026201825X.
- <span id="page-10-2"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. *International Conference on Learning Representations (ICLR)*, 2021.
- <span id="page-10-9"></span>Tuan Anh Nguyen and Anh Tran. Input-aware dynamic backdoor attack. *Advances in Neural Information Processing Systems*, 33:3454–3464, 2020.
- <span id="page-10-10"></span>Tuan Anh Nguyen and Anh Tuan Tran. Wanet - imperceptible warping-based backdoor attack. In *International Conference on Learning Representations (ICLR)*, 2021.
- <span id="page-10-16"></span>Diederik P. Kingma and Jimmy Ba. Adam: A method for stochastic optimization. *International Conference on Learning Representations (ICLR)*, 2015.
- <span id="page-10-13"></span>Xiangyu Qi, Tinghao Xie, Yiming Li, Saeed Mahloujifar, and Prateek Mittal. Revisiting the assumption of latent separability for backdoor defenses. In *The eleventh international conference on learning representations*, 2022.
- <span id="page-10-14"></span>Hossein Souri, Liam Fowl, Rama Chellappa, Micah Goldblum, and Tom Goldstein. Sleeper agent: Scalable hidden trigger backdoors for neural networks trained from scratch. *Advances in Neural Information Processing Systems*, 35:19165–19178, 2022.
- <span id="page-10-12"></span>Di Tang, XiaoFeng Wang, Haixu Tang, and Kehuan Zhang. Demon in the variant: Statistical analysis of dnns for robust backdoor contamination detection. In *USENIX Security Symposium*, 2021.
- <span id="page-10-17"></span>Brandon Tran, Jerry Li, and Aleksander Madry. Spectral signatures in backdoor attacks. *Advances in Neural Information Processing Systems (NeurIPS)*, 31, 2018.
- <span id="page-10-8"></span>Alexander Turner, Dimitris Tsipras, and Aleksander Madry. Label-consistent backdoor attacks. *arXiv preprint arXiv:1912.02771*, 2019.

- <span id="page-11-5"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2022.
- <span id="page-11-6"></span>Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023.
- <span id="page-11-0"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-11-2"></span>Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023.
- <span id="page-11-4"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision (WACV)*, pp. 6514–6523, 2023.
- <span id="page-11-1"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *International Conference on Learning Representations (ICLR)*, 2021.
- <span id="page-11-3"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems (NeurIPS)*, 2022.

## A APPENDIX

### A.1 NOTATION TABLE

<span id="page-12-1"></span>The notations used in this paper are presented in Table [4.](#page-12-1)

| Notations                                                 | Descriptions                                                                          |
|-----------------------------------------------------------|---------------------------------------------------------------------------------------|
| $\overline{\mathcal{X}}$                                  | feature space $\subset \mathbb{R}^d$                                                  |
| $\mathcal{Y}$                                             | label space $\subset \mathbb{R}^c$                                                    |
| $\boldsymbol{x}$                                          | feature                                                                               |
| $\boldsymbol{y}$                                          | label                                                                                 |
| D                                                         | probability distribution which is distributed in $\mathcal{X} \times \mathcal{Y}$     |
| $\mathcal{D}_A$                                           | probability distribution which is distributed in $\mathcal{X} \times \mathcal{Y}$ for |
|                                                           | benign behaviors                                                                      |
| $\mathcal{D}_B$                                           | probability distribution which is distributed in $\mathcal{X} \times \mathcal{Y}$ for |
|                                                           | malicious behavior (trigger)                                                          |
| $\cal N$                                                  | number of samples for some dataset                                                    |
| $N_A$                                                     | number of samples for benign dataset                                                  |
| $N_B$                                                     | number of samples for trigger dataset                                                 |
| $N_{\mathcal{S}}$                                         | number of samples for distilled dataset                                               |
| $\boldsymbol{D}$                                          | dataset picked from the distribution $D$ with $N$ samples                             |
| $D_A$                                                     | dataset picked from the distribution $\mathcal{D}_A$ with $N_A$ samples               |
| $D_B$                                                     | dataset picked from the distribution $\mathcal{D}_B$ with $N_B$ samples               |
| $\mathcal{S}% _{M_{1},M_{2}}^{\alpha,\beta}(\varepsilon)$ | any dataset with $N_{\mathcal{S}}$ samples                                            |
| $\mathcal{S}^*$                                           | distilled dataset with $N_{\mathcal{S}}$ samples                                      |
| $\frac{\mathcal{S}^*_A}{T}$                               | distilled dataset from $D_A$ with $N_S$ samples                                       |
|                                                           | trigger pattern $\in \mathbb{R}^d$                                                    |
| $y_T$                                                     | trigger label $\in \mathcal{Y}$                                                       |
| $\tilde{D}$                                               | poisoned dataset which is the union from $D_A$ and $D_B$                              |
| $\boldsymbol{X}$                                          | the $N \times d$ matrix induced from the feature set in D.                            |
| $\boldsymbol{Y}$                                          | the $N \times c$ matrix induced from the label set in D.                              |
| $X_A$                                                     | the $N_A \times d$ matrix induced from the feature set in $D_A$ .                     |
| $\boldsymbol{Y}_A$                                        | the $N_A \times c$ matrix induced from the label set in $D_A$ .                       |
| $\boldsymbol{X}_B$                                        | the $N_B \times d$ matrix induced from the feature set in $D_B$ .                     |
| $\boldsymbol{Y_B}$                                        | the $N_B \times c$ matrix induced from the label set in $D_B$ .                       |
| $X_{\mathcal{S}}$                                         | the $N_S \times d$ matrix induced from the feature set in S.                          |
| $Y_{\mathcal{S}}$                                         | the $N_S \times c$ matrix induced from the label set in S.                            |
| $\boldsymbol{X}_{AB}$                                     | the $(N_A + N_B) \times d$ matrix induced from the feature set in<br>$\tilde{D}$ .    |
| $\boldsymbol{Y_{AB}}$                                     | the $(N_A + N_B) \times c$ matrix induced from the label set in $\hat{D}$ .           |
| $k(\cdot,\cdot)$                                          | the kernel                                                                            |
| $\mathcal{H}_k$                                           | the reproducing kernel hilbert space induced by kernel $k$ .                          |
| $\lambda$                                                 | weight of regularization term.                                                        |
| $\lambda_{\mathcal{S}}$                                   | weight of regularization term for $S$ .                                               |
| $\rho$                                                    | penalty parameter.                                                                    |
| $f_{\tilde{D}}$                                           | the model trained on $\ddot{D}$ with the weight of the regulariza-                    |
|                                                           | tion term $\lambda \geq 0$ .                                                          |
| $f_{\mathcal{S}}$                                         | the model trained on $S$ with the weight of the regularization                        |
|                                                           | term $\lambda_{\mathcal{S}} \geq 0$ .                                                 |

Table 4: Notation Table

<span id="page-12-0"></span>

### A.2 LEMMA 1 AND ITS PROOF

<span id="page-12-2"></span>**Lemma 1** (Projection lemma). *Given a synthetic dataset*  $S = \{(x_s, y_s)\}_{s=1}^{N_S}$ , and a dataset  $\tilde{D}$  =  $\{(x_i, y_i)\}_{i=1}^{N_A+N_B}$  where  $(N_A+N_B)$  is the number of the samples of  $\tilde{D}$ *. Suppose the kernel matrix*   $k(\boldsymbol{X}_{\mathcal{S}}, \boldsymbol{X}_{\mathcal{S}})$  *is invertible, then we have* 

$$
k(\cdot, x_i) = \underbrace{k(\cdot, \mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}}, x_i)}_{\in \mathcal{H}_{\mathcal{S}}}
$$
(22)

$$
+\underbrace{[k(\cdot,x_i)-k(\cdot,\mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}},\mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}},x_i)]}_{\in\mathcal{H}_{\mathcal{S}}^{\perp}},\quad\forall(x_i,y_i)\in\tilde{D}
$$
(23)

 $where \ H_{\mathcal{S}} := span(\{k(\cdot,x_s) \in \mathcal{H}_k | (x_s,y_s) \in \mathcal{S}\})$  and  $\mathcal{H}_{\mathcal{S}}^{\perp}$  is the collection of functions which is *orthogonal to*  $\mathcal{H_S}$  *corresponding to the inner product*  $\langle\cdot,\cdot\rangle_{\mathcal{H}_k}$ *. The right hand side of [\(22\)](#page-13-1) lies in*  $\mathcal{H_S}$ while [\(23\)](#page-13-2) lies in  $H_S^{\perp}$ . Thus,  $k(\cdot, X_S)k(X_S, X_S)^{-1}k(X_S, x_i)$  is the solution of the optimization *problem:*

<span id="page-13-4"></span><span id="page-13-3"></span><span id="page-13-2"></span><span id="page-13-1"></span>
$$
\underset{f \in \mathcal{H}_S}{\arg \min} \sum_{(x_s, y_s) \in \mathcal{S}} \| f(x_s) - k(x_s, x_i) \|_2^2. \tag{24}
$$

*Proof.*  $k(\cdot, X_{\mathcal{S}})k(X_{\mathcal{S}}, X_{\mathcal{S}})^{-1}k(X_{\mathcal{S}}, x_i)$  lies in  $\mathcal{H}_{\mathcal{S}}$  is clearly. We just need to show that  $k(\cdot, x_i)$  –  $k(\cdot, \mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}}, x_i)$  lies in  $\mathcal{H}_{\mathcal{S}}^{\perp}$ . Notice that

$$
\langle k(\cdot, x_s), k(\cdot, x_i) - k(\cdot, \mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}}, x_i)) \rangle_{\mathcal{H}_k}
$$
(25)

$$
=k(xs,xi) - k(xs, XS)k(XS, XS)-1k(XS, xi)), \quad \forall (xs, ys) \in S.
$$
 (26)

If we collect all  $\langle k(\cdot, x_s), k(\cdot, x_i) - k(\cdot, \mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}}, x_i)) \rangle_{\mathcal{H}_k}$  for all  $(x_s, y_s) \in \mathcal{S}$ , we can obtain

$$
k(\boldsymbol{X}_{\mathcal{S}}, x_i) - k(\boldsymbol{X}_{\mathcal{S}}, \boldsymbol{X}_{\mathcal{S}})k(\boldsymbol{X}_{\mathcal{S}}, \boldsymbol{X}_{\mathcal{S}})^{-1}k(\boldsymbol{X}_{\mathcal{S}}, x_i) = k(\boldsymbol{X}_{\mathcal{S}}, x_i) - k(\boldsymbol{X}_{\mathcal{S}}, x_i) = 0.
$$
 (27)

This implies that  $\langle k(\cdot,x_s), k(\cdot,x_i) - k(\cdot,\bm{X}_{\mathcal{S}})k(\bm{X}_{\mathcal{S}},\bm{X}_{\mathcal{S}})^{-1}k(\bm{X}_{\mathcal{S}},x_i))\rangle_{\mathcal{H}_k} = 0$  for  $x_s \in$ S.  $k(\cdot, x_i) - k(\cdot, X_{\mathcal{S}})k(X_{\mathcal{S}}, X_{\mathcal{S}})^{-1}k(X_{\mathcal{S}}, x_i)$  lies in  $\mathcal{H}_{\mathcal{S}}^{\perp}$ . Eq. [\(27\)](#page-13-3) also suggest that  $k(x_s,\boldsymbol{X}_{\mathcal{S}})k(\boldsymbol{X}_{\mathcal{S}},\boldsymbol{X}_{\mathcal{S}})^{-1}k(\boldsymbol{X}_{\mathcal{S}},x_i)$  is equal to  $k(x_s,x_i)$  for all  $(x_s,y_s) \in \mathcal{S}$ . So,  $k(\cdot, \mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}}, x_i)$  is the solution of Eq. [\(24\)](#page-13-4).

<span id="page-13-0"></span>

### A.3 THEOREM 1 AND ITS PROOF

Theorem 1 (Upper bound of conflict loss). *The conflict loss* L*conflict can be bounded as*

$$
\mathcal{L}_{conflict} \leq \frac{1}{N_A + N_B} Tr(\boldsymbol{I} - k(\boldsymbol{X}_{AB}, \boldsymbol{X}_{AB})[k(\boldsymbol{X}_{AB}, \boldsymbol{X}_{AB}) + (N_A + N_B)\lambda \boldsymbol{I}]^{-1})^2 \|\boldsymbol{Y}_{AB}\|_2^2
$$
\n(28)

*where Tr is the trace operator,*  $k(\mathbf{X}_{AB}, \mathbf{X}_{AB})$  *is a*  $(N_A + N_B) \times (N_A + N_B)$  *matrix, and*  $\mathbf{Y}_{AB}$  *is*  $a (N_A + N_B) \times c$  *matrix.* 

*Proof.* From Definition [1,](#page-1-1) we know that the kernel matrix  $k(X_{AB}, X_{AB})$  is positive semidefinite. Hence, there exist some unitary matrix U such that  $k(\mathbf{X}_{AB}, \mathbf{X}_{AB}) = \mathbf{U} \Sigma \mathbf{U}^T$  where  $\Sigma$  is some diagonal matrix with non-negative components. Then, from Eq. [\(12\)](#page-4-1), we can express the upper bound of the conflict loss  $\mathcal{L}_{conflict}$  as

$$
\mathcal{L}_{\text{conflict}} = \frac{1}{N_A + N_B} \|\mathbf{I} \mathbf{Y}_{AB} - \mathbf{U} \Sigma \mathbf{U}^T [\mathbf{U} \Sigma \mathbf{U}^T + (N_A + N_B)\lambda \mathbf{I}]^{-1} \mathbf{Y}_{AB} \|_2^2 \tag{29}
$$

$$
= \frac{1}{N_A + N_B} \|\mathbf{U}\mathbf{I}\mathbf{U}^T\mathbf{Y}_{AB} - \mathbf{U}\Sigma\mathbf{U}^T[\mathbf{U}(\Sigma + (N_A + N_B)\lambda\mathbf{I})\mathbf{U}^T]^{-1}\mathbf{Y}_{AB}\|_2^2 \qquad (30)
$$

$$
= \frac{1}{N_A + N_B} \left| U(I - \Sigma[\Sigma + (N_A + N_B)\lambda I)]^{-1} \right| U^T Y_{AB} \right|_2^2 \tag{31}
$$

$$
= \frac{1}{N_A + N_B} \left\| (\mathbf{I} - \Sigma[\Sigma + (N_A + N_B)\lambda \mathbf{I})]^{-1} (\mathbf{U}^T \mathbf{Y}_{AB})_2^2 \right\| \tag{32}
$$

$$
\leq \frac{1}{N_A + N_B} \|\text{Tr}(\boldsymbol{I} - \Sigma[\Sigma + (N_A + N_B)\lambda \boldsymbol{I}]^{-1}) \boldsymbol{U}^T \boldsymbol{Y}_{AB}\|_2^2
$$
\n(33)

<span id="page-13-5"></span>
$$
= \frac{1}{N_A + N_B} \text{Tr}(\boldsymbol{I} - \Sigma[\Sigma + (N_A + N_B)\lambda \boldsymbol{I}]^{-1})^2 \|\mathbf{Y}_{AB}\|_2^2.
$$
 (34)

Moreover, we have

$$
\text{Tr}(\boldsymbol{I} - k(\boldsymbol{X}_{AB}, \boldsymbol{X}_{AB})[k(\boldsymbol{X}_{AB}, \boldsymbol{X}_{AB}) + (N_A + N_B)\lambda \boldsymbol{I}]^{-1})
$$
\n
$$
= \text{Tr}(\boldsymbol{U}(\boldsymbol{I} - \Sigma[\Sigma + (N_A + N_B)\lambda \boldsymbol{I}]^{-1})\boldsymbol{U}^T)
$$
\n(35)

<span id="page-14-1"></span>
$$
= \operatorname{Tr}((\boldsymbol{I} - \Sigma[\Sigma + (N_A + N_B)\lambda \boldsymbol{I}]^{-1})\boldsymbol{U}^T \boldsymbol{U}) \tag{36}
$$

$$
= \operatorname{Tr}((\boldsymbol{I} - \Sigma[\Sigma + (N_A + N_B)\lambda \boldsymbol{I}]^{-1})). \tag{37}
$$

Combining Eq. [\(34\)](#page-13-5) and Eq. [\(37\)](#page-14-1) completes the proof.  $\blacksquare$ 

<span id="page-14-0"></span>

### A.4 THEOREM 2 AND ITS PROOF

Theorem 2 (Upper bound of projection loss). *Suppose the kernel matrix of the synthetic dataset*  $k(\textbf{X}_{\mathcal{S}}, \textbf{X}_{\mathcal{S}})$  is invertible,  $f_{\mathcal{S}}$  is the model trained on the synthetic dataset S with the regularization *term*  $\lambda_S$ *, where the projection loss*  $\mathcal{L}_{project} = \min_S \mathbb{E}_{(x,y)\sim \tilde{D}} \ell(f_S,(x,f_{\tilde{D}}(x)))$  *can be bounded as* 

$$
\mathcal{L}_{project} \leq \sum_{(x_i, y_i) \in \tilde{D}} \min_{\mathbf{X}_{\mathcal{S}}} \sum_{j=1}^c \frac{|\alpha_{i,j}|^2}{N_A + N_B} ||k(\mathbf{X}_{AB}, x_i) - k(\mathbf{X}_{AB}, \mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}}, x_i)||_2^2.
$$
\n(38)

where  $\alpha_{i,j}:=[[k(\bm{X}_{AB},\bm{X}_{AB})+(N_A+N_B)\lambda \bm{I}]^{-1}\bm{Y}_{AB}]_{i,j}$  , which is the weight of  $k(\cdot,x_i)$  corre $s$ ponding to  $f_{\tilde{D}'}^{j}$  ,  $\bm{X}_{AB}$  is the  $(N_A+N_B)\times d$  matrix corresponding to the features of  $\tilde{D}$ ,  $\bm{X}_{\mathcal{S}}$  is the  $N_S \times d$  *matrix corresponding to the features of* S,  $Y_{AB}$  *is the*  $(N_A + N_B) \times c$  *matrix corresponding to the labels of*  $\tilde{D}$ *,*  $\mathbf{Y}_{\mathcal{S}}$  *is the*  $N_{\mathcal{S}} \times c$  *matrix corresponding to the labels of*  $\mathcal{S}$ *.* 

*Proof.* From [\(11\)](#page-4-3), we know that

$$
f_{\tilde{D}}^{j}(x) = [k(x, \mathbf{X}_{AB})[k(\mathbf{X}_{AB}, \mathbf{X}_{AB}) + (N_A + N_B)\lambda \mathbf{I}]^{-1} \mathbf{Y}_{AB}]_{j}
$$
  
= 
$$
\sum_{(x_i, y_i) \in \tilde{D}} \alpha_{i,j} k(x, x_i).
$$
 (39)

Then, we can bound the projection loss as

$$
\mathcal{L}_{\text{project}} = \min_{\mathcal{S}} \mathbb{E}_{(x,y)\sim \tilde{D}} \ell(f_{\mathcal{S}}, (x, f_{\tilde{D}}(x)))
$$

$$
= \min_{\mathcal{S}} \frac{1}{N_A + N_B} \sum_{(x,y)\in \tilde{D}} \ell(f_{\mathcal{S}}, (x, f_{\tilde{D}}(x))) \quad (40)
$$

$$
\leq \sum_{(x_i,y_i)\in \tilde{D}} \min_{\mathcal{S}} \left\{ \frac{1}{N_A + N_B} \sum_{(x,y)\in \tilde{D}} \sum_{j=1}^c \ell(f_{\mathcal{S}}^j,(x,\alpha_{i,j}k(x,x_i))) \right\} \tag{41}
$$

$$
= 
\sum_{s} \min_{(x_i,y_i)\in\tilde{D}} \left\{\frac{1}{N_A+N_B} \sum_{(x,y)\in\tilde{D}} \sum_{j=1}^c ||[k(x,\boldsymbol{X}_{\mathcal{S}})[k(\boldsymbol{X}_{\mathcal{S}},\boldsymbol{X}_{\mathcal{S}})+N_{\mathcal{S}}\lambda_{\mathcal{S}}\boldsymbol{I}]^{-1} \boldsymbol{Y}_{\mathcal{S}}]_j - \alpha_{i,j} k(x,x_i) ||_2^2 \right\}.
$$
(42)

For each  $(x_i, y_i) \in \tilde{D}$ , we have

$$
\min_{\mathcal{S}} \left\{ \frac{1}{N_A + N_B} \sum_{(x,y)\in \tilde{D}} \sum_{j=1}^c ||[k(x, \mathbf{X}_{\mathcal{S}})[k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}}) + N_{\mathcal{S}}\lambda_{\mathcal{S}}\mathbf{I}]^{-1} \mathbf{Y}_{\mathcal{S}}]_j - \alpha_{i,j} k(x, x_i) ||_2^2 \right\}
$$
\n
$$
\leq \min_{\mathbf{X}_{\mathcal{S}}} \left\{ \frac{1}{N_A + N_B} \sum_{(x,y)\in \tilde{D}} \sum_{j=1}^c \min_{\mathbf{Y}_{\mathcal{S}}} ||[k(x, \mathbf{X}_{\mathcal{S}})[k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}}) + N_{\mathcal{S}}\lambda_{\mathcal{S}}\mathbf{I}]^{-1} \mathbf{Y}_{\mathcal{S}}]_j - \alpha_{i,j} k(x, x_i) ||_2^2 \right\}
$$
\n(43)

<span id="page-14-2"></span>
$$
= \min_{\mathbf{X}_{\mathcal{S}}} \left\{ \frac{1}{N_A + N_B} \sum_{(x,y) \in \tilde{D}} \sum_{j=1}^c \min_{f_{i,j} \in \mathcal{H}_{\mathcal{S}}} \|f_{i,j}(x) - \alpha_{i,j} k(x, x_i)\|_2^2 \right\}.
$$
 (44)

Then, with the help of Lemma [1,](#page-12-2) we bound Eq. [\(44\)](#page-14-2) as follows

$$
\min_{\mathbf{X}_{\mathcal{S}}} \left\{ \frac{1}{N_A + N_B} \sum_{(x,y) \in \tilde{D}} \sum_{j=1}^{c} \min_{f_{i,j} \in \mathcal{H}_{\mathcal{S}}} \|f_{i,j}(x) - \alpha_{i,j} k(x, x_i)\|_2^2 \right\}
$$

$$
\leq \min_{\mathbf{X}_{\mathcal{S}}} \left\{ \frac{1}{N_A + N_B} \sum_{k=1}^{c} \sum_{j=1}^{c} \|\alpha_{i,j}[k(x, x_i) - k(x, \mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}}, x_i)\|_2^2 \right\} \tag{45}
$$

$$
=\frac{1}{X_{\mathcal{S}}}\left[N_A+N_B\sum_{(x,y)\in\tilde{D}}\sum_{j=1}^{\infty}\prod_{i=1}^{n_{\alpha_i,j}\left[\alpha_i(x,y)\right]}n_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\alpha_i,x,y}\mu_{\
$$

$$
\leq \min_{\mathbf{X}_{\mathcal{S}}} \left\{ \sum_{j=1}^{c} \frac{|\alpha_{i,j}|^2}{N_A + N_B} \|k(\mathbf{X}_{AB}, x_i) - k(\mathbf{X}_{AB}, \mathbf{X}_{\mathcal{S}})k(\mathbf{X}_{\mathcal{S}}, \mathbf{X}_{\mathcal{S}})^{-1}k(\mathbf{X}_{\mathcal{S}}, x_i)\|_2^2 \right\}
$$
(46)

We take the summation over  $(x_i, y_i) \in \tilde{D}$  for Eq. [\(46\)](#page-15-1) and then derive the upper bound.

<span id="page-15-0"></span>

### A.5 THEOREM 3 AND ITS PROOF

Theorem 3 (Upper bound of generalization gap). *Given a* N*-sample dataset* D*, sampled from the distribution* D, then the following generalization gap holds for all  $g \in \mathcal{G}$  with probability at least  $1 - \delta$ :

<span id="page-15-1"></span>
$$
\mathbb{E}_{(x,y)\sim\mathcal{D}}g((x,y)) - \sum_{(x_i,y_i)\in D} \frac{g((x_i,y_i))}{N} \leq 2\hat{\mathfrak{R}}_D(\mathcal{G}) + 3L_{\mathcal{D}}\Gamma_{\mathcal{D}}\sqrt{\frac{\log\frac{2}{\delta}}{2N}},\tag{47}
$$

*where*  $X$  *is the matrix corresponding to the features of*  $D$  *and*  $\mathfrak{\hat{R}}_D(\mathcal{G})$  *is the empirical Rademacher's complexity.*

*Proof.* Here we only sketch the proof, which mainly follows the proof of Theorem 3.3 in [\(Mohri](#page-10-15) [et al., 2012\)](#page-10-15), but is slightly modified under our assumption. First, we denote the maximum of the generalization gap for the dataset  $D$  as

$$
\Phi(D) = \sup_{g \in \mathcal{G}} (\mathbb{E}_{(x,y)\in \mathcal{D}} g((x,y)) - \frac{1}{N} \sum_{(x_i,y_i)\in D} g((x_i,y_i))).
$$
\n(48)

Consider another dataset  $D'$  sampled from the distribution  $D$ .  $D$  and  $D'$  differ by only one sample, which is denoted as  $(x_N, y_N)$  and  $(x'_N, y'_N)$ . Then, according to our assumption, we have

$$
\Phi(D) - \Phi(D') \le \sup_{g \in \mathcal{G}} \left( \frac{1}{N} g((x_N, y_N)) - \frac{1}{N} g((x'_N, y'_N)) \right) \tag{49}
$$

$$
\leq \frac{L_D \|(x_N, y_N) - x'_N, y'_N)\|_2}{N} \tag{50}
$$

$$
\leq \frac{L_{\mathcal{D}} \Gamma_{\mathcal{D}}}{N}.\tag{51}
$$

Then, we can apply McDiarmid's inequality on  $\Phi(D)$ . We can derive

<span id="page-15-2"></span>
$$
\Phi(D) \leq \mathbb{E}_D \Phi(D) + L_{\mathcal{D}} \Gamma_{\mathcal{D}} \sqrt{\frac{\log \frac{2}{\delta}}{2N}},\tag{52}
$$

which holds with probability at least  $1 - \frac{\delta}{2}$ . In the proof of Theorem 3.3 in [\(Mohri et al., 2012\)](#page-10-15), we can also prove that  $\mathbb{E}_D \check{\Phi}(D) \leq 2\Re(\mathcal{G})$ , where  $\mathfrak{R}(\mathcal{G})$  is Rademacher's complexity. Under our assumption, we notice that the empirical Rademacher complexity  $\hat{\mathfrak{R}}_D(\mathcal{G})$  also satisfies

$$
\hat{\mathfrak{R}}_D(\mathcal{G}) - \hat{\mathfrak{R}}_{D'}(\mathcal{G}) \le \frac{L_D \Gamma_D}{N}.
$$
\n(53)

So, we can apply McDiarmid's inequality again and obtain

<span id="page-15-3"></span>
$$
\Re(\mathcal{G}) \le \hat{\Re}_D(\mathcal{G}) + L_{\mathcal{D}} \Gamma_{\mathcal{D}} \sqrt{\frac{\log \frac{2}{\delta}}{2N}},\tag{54}
$$

which holds with probability at least  $1-\frac{\delta}{2}$ . Combine [\(52\)](#page-15-2), [\(54\)](#page-15-3) and the fact that  $\mathbb{E}_D\Phi(D) \leq 2\Re(\mathcal{G})$ , we have

$$
\mathbb{E}_{(x,y)\sim\mathcal{D}}g((x,y)) - \sum_{(x_i,y_i)\in D} \frac{g((x_i,y_i))}{N} \leq 2\hat{\Re}_D(\mathcal{G}) + 3L_{\mathcal{D}}\Gamma_{\mathcal{D}}\sqrt{\frac{\log\frac{2}{\delta}}{2N}}.\tag{55}
$$

which holds with probability at least  $1 - \delta$ .

### A.6 PSEUDOCODE FOR THE SIMPLEST FORM OF KIP-BASED BACKDOOR ATTACK

<span id="page-16-0"></span>Algorithm 1 The Simplest Form of KIP-based Backdoor Attack

**Require:** benign dataset  $D_A$ , initial trigger  $T_0$ , trigger label  $y_T$ , mask m, size of distilled dataset  $N_{\mathcal{S}}$ , training step STEP > 0, batch size BATCH > 0, mix ratio  $\rho_m > 0$ , learning rate  $\eta > 0$ . Ensure: synthetic dataset  $S^*$  $N \leftarrow 1$  $S \leftarrow$  Randomly sample  $N_S$  data from  $D_A$  as initial distilled dataset.  $D_B \leftarrow \{(x_b, y_b) := ((1-m) \odot x + m \odot T, y_T) | (x_a, y_a) \in D_A\}$ while  $N \leq$  STEP do  $(X_A^{\text{batch}}, Y_A^{\text{batch}}) \leftarrow$  Randomly sample BATCH data from  $D_A$ .  $(X_B^{\text{batch}}, Y_B^{\text{batch}}) \leftarrow$  Randomly sample BATCH data from  $D_B$ .  $\tilde{D}^{\text{batch}} \leftarrow (\boldsymbol{X}^{\text{batch}}_A, \boldsymbol{Y}^{\text{batch}}_A) \cup (\boldsymbol{X}^{\text{batch}}_B, \boldsymbol{Y}^{\text{batch}}_B)$  $S \leftarrow S - \eta \nabla_S \mathcal{L}(S)$   $\triangleright \mathcal{L}$  is defined in Eq. [\(8\)](#page-3-1).  $N \leftarrow N + 1$ end while  $\mathcal{S}^* \leftarrow \mathcal{S}$ 

<span id="page-16-2"></span>

### A.7 PSEUDOCODE FOR RELAX-TRIGGER

<span id="page-16-1"></span>

### Algorithm 2 relax-trigger

**Require:** benign dataset  $D_A$ , initial trigger  $T_0$ , trigger label  $y_T$ , mask m, training step STEP > 0, batch size BATCH > 0, mix ratio  $\rho_m > 0$ , penalty parameter  $\rho > 0$ , learning rate  $\eta > 0$ . **Ensure:** optimized  $T^*$  $T \leftarrow T_0$  $N \leftarrow 1$ 

 $S_A^* \leftarrow$  Apply KIP to  $D_A$   $\triangleright$  We use S <sup>\*</sup><sup>\*</sup> to denote  $S^*$  from  $D_A$ while  $N \leq$  STEP do  $(X_A^{\text{batch}}, Y_A^{\text{batch}}) \leftarrow$  Randomly pick BATCH samples from  $D_A$  $(X^{\text{batch}}, Y^{\text{batch}}) \leftarrow$  Randomly pick BATCH  $\times \rho_m$ samples from  $D_A$  $(X_B^{\text{batch}}, Y_B^{\text{batch}}) \leftarrow \{(x_b, y_b) := ((1-m) \odot x + m \odot T, y_T) | (x, y) \in (X^{\text{batch}}, Y^{\text{batch}})\}$  $T\leftarrow T-\eta\nabla_T\mathcal{L}(\mathcal{S}^*_A, (\boldsymbol{X}^{\text{batch}}_A, \boldsymbol{Y}^{\text{batch}}_A), (\boldsymbol{X}^{\text{batch}}_B, \boldsymbol{Y}^{\text{batch}}_B)$  $\triangleright$   $\mathcal{L}$  is defined in Eq. [\(21\)](#page-6-3).  $N \leftarrow N + 1$ end while  $T^* \leftarrow T$ 

<span id="page-16-3"></span>

### A.8 EXTRA EXPERIMENTS.

In Tables [5](#page-17-0)∼ [8,](#page-17-3) we provide extra experimental results.

<span id="page-17-0"></span>

| Trig.   | Def. | None         |               | SCAn          |               | AC           |               | SS            |               | Strip         |               |
|---------|------|--------------|---------------|---------------|---------------|--------------|---------------|---------------|---------------|---------------|---------------|
|         |      | CTA (%)      | ASR (%)       | CTA (%)       | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)       | ASR (%)       | CTA (%)       | ASR (%)       |
| 2 x 2   |      | 29.47 (0.44) | 25.67 (4.35)  | 28.70 (2.23)  | 35.29 (4.40)  | 29.93 (1.66) | 26.17 (9.14)  | 28.13 (1.45)  | 19.24 (4.39)  | 27.52 (3.30)  | 28.73 (13.83) |
| 4 x 4   |      | 29.72 (1.62) | 31.08 (8.07)  | 32.43 (1.87)  | 26.70 (2.30)  | 30.57 (0.87) | 30.20 (3.17)  | 30.48 (0.91)  | 11.07 (1.53)  | 25.95 (4.07)  | 25.37 (11.61) |
| 8 x 8   |      | 32.00 (1.03) | 51.65 (12.41) | 30.78 (2.08)  | 37.45 (9.23)  | 29.57 (0.74) | 35.99 (2.63)  | 28.46 (2.56)  | 12.77 (3.79)  | 28.39 (3.18)  | 15.94 (2.67)  |
| 16 x 16 |      | 34.61 (1.01) | 85.65 (17.12) | 33.88 (1.65)  | 44.24 (3.85)  | 31.96 (0.53) | 59.56 (21.29) | 30.70 (0.09)  | 44.77 (25.11) | 27.96 (7.42)  | 43.00 (25.69) |
| 32 x 32 |      | 33.78 (0.53) | 100.00 (0.00) | 34.54 (0.93)  | 100.00 (0.00) | 32.25 (2.29) | 33.33 (47.14) | 29.04 (0.91)  | 33.33 (47.14) | 26.93 (8.95)  | 0.00 (0.00)   |
| Trig.   | Def. |              |               | ABL           |               | NAD          |               | STRIP         |               | FP            |               |
|         |      | CTA (%)      | ASR (%)       | CTA (%)       | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)       | ASR (%)       |               |               |
| 2 x 2   |      |              | 25.31 (4.40)  | 13.67 (6.65)  | 36.27 (0.54)  | 7.32 (1.77)  | 25.05 (2.73)  | 27.25 (8.88)  | 15.28 (2.96)  | 35.74 (41.58) |               |
| 4 x 4   |      |              | 24.25 (2.70)  | 5.81 (5.26)   | 36.29 (2.07)  | 6.57 (0.81)  | 26.85 (1.39)  | 27.92 (7.10)  | 14.67 (3.67)  | 69.41 (31.97) |               |
| 8 x 8   |      |              | 22.97 (5.54)  | 19.06 (8.02)  | 36.96 (1.73)  | 14.53 (5.15) | 28.84 (0.89)  | 40.99 (8.03)  | 19.41 (2.06)  | 75.62 (17.01) |               |
| 16 x 16 |      |              | 28.43 (2.31)  | 64.73 (34.70) | 37.01 (1.16)  | 29.42 (1.39) | 31.09 (0.80)  | 22.80 (22.02) | 21.25 (2.94)  | 19.08 (21.95) |               |

Table 5: Defenses for simple-trigger on CIFAR-10 with size 500.

<span id="page-17-1"></span>

| Trig. $ Δ$ . | None         |               | SCAn          |               | AC           |               | SS            |               | Strip         |               |
|--------------|--------------|---------------|---------------|---------------|--------------|---------------|---------------|---------------|---------------|---------------|
|              | CTA (%)      | ASR (%)       | CTA (%)       | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)       | ASR (%)       | CTA (%)       | ASR (%)       |
| 2 × 2        | 37.78 (2.09) | 10.71 (2.33)  | 40.17 (8.89)  | 5.18 (2.40)   | 27.61 (1.09) | 9.43 (4.63)   | 14.55 (1.71)  | 6.47 (3.30)   | 28.52 (2.29)  | 5.22 (4.19)   |
| 4 × 4        | 39.07 (2.27) | 18.67 (8.87)  | 33.47 (6.75)  | 3.59 (1.11)   | 23.22 (7.42) | 9.71 (5.61)   | 15.22 (2.03)  | 2.28 (2.36)   | 26.56 (4.15)  | 7.67 (2.29)   |
| 8 × 8        | 38.89 (1.68) | 47.53 (9.30)  | 21.48 (12.74) | 2.85 (0.37)   | 28.60 (1.36) | 8.25 (4.15)   | 12.86 (1.78)  | 8.60 (6.45)   | 34.96 (5.02)  | 8.69 (1.85)   |
| 16 × 16      | 37.92 (2.84) | 84.24 (3.60)  | 32.99 (4.62)  | 11.04 (8.85)  | 26.91 (2.77) | 56.51 (39.03) | 14.01 (1.84)  | 4.76 (4.32)   | 29.22 (1.59)  | 37.06 (41.29) |
| 32 × 32      | 41.97 (0.97) | 66.67 (47.14) | 33.47 (9.14)  | 33.33 (47.14) | 27.20 (3.83) | 33.33 (47.14) | 13.99 (0.79)  | 33.33 (47.14) | 35.07 (1.95)  | 33.33 (47.14) |
| Trig. $ Δ$ . |              |               | ABL           |               | NAD          |               | STRIP         |               | FP            |               |
|              |              |               | CTA (%)       | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)       | ASR (%)       | CTA (%)       | ASR (%)       |
| 2 × 2        |              |               | 32.31 (4.67)  | 5.85 (1.90)   | 94.05 (0.51) | 0.40 (0.04)   | 22.36 (14.79) | 5.48 (4.00)   | 14.57 (10.65) | 8.13 (5.75)   |
| 4 × 4        |              |               | 36.25 (4.05)  | 6.68 (3.75)   | 93.75 (0.41) | 0.29 (0.09)   | 35.23 (2.17)  | 16.99 (8.05)  | 23.73 (4.09)  | 11.45 (11.59) |
| 8 × 8        |              |               | 25.49 (15.17) | 11.37 (3.68)  | 93.72 (0.11) | 0.45 (0.19)   | 34.82 (1.63)  | 42.69 (7.99)  | 24.67 (1.23)  | 33.71 (29.71) |
| 16 × 16      |              |               | 37.15 (1.59)  | 63.80 (31.74) | 93.78 (0.07) | 0.09 (0.08)   | 34.04 (2.48)  | 50.69 (10.77) | 25.35 (2.59)  | 40.16 (30.45) |
| 32 × 32      |              |               | 34.56 (5.89)  | 66.67 (47.14) |              |               | 37.87 (0.80)  |               | 66.67 (47.14) |               |

Table 6: Defenses for simple-trigger on GTSRB with distilled dataset size = 430.

<span id="page-17-2"></span>

| $Trig. \overline{Def.}$ |              | None                       |               | SCAn          |               | AC            |              | SS            | Strip         |               |
|-------------------------|--------------|----------------------------|---------------|---------------|---------------|---------------|--------------|---------------|---------------|---------------|
|                         | CTA(%)       | ASR $(%)$                  | CTA(%)        | $ASR(\%)$     | CTA(%)        | ASR(%)        | CTA(%)       | $ASR(\%)$     | CTA(%)        | $ASR(\%)$     |
| $2 \times 2$            | 72.23 (2.76) | 1.03(0.42)                 | 72.82 (12.40) | 2.37(1.69)    | 63.73(7.87)   | 2.06(0.56)    | 44.50 (2.26) | 3.08(0.81)    | 76.63 (3.00)  | 1.00(0.47)    |
| $4 \times 4$            | 73.29 (1.22) | 1.08(0.37)                 | 81.19 (2.30)  | 1.18(0.09)    | 71.79(1.78)   | 2.37(0.40)    | 45.16(5.15)  | 3.53(1.12)    | 73.28 (11.33) | 0.94(0.46)    |
| $8 \times 8$            | 73.29(0.26)  | 8.08 (4.20)                | 79.28 (2.69)  | 3.84 (1.72)   | 62.79(9.81)   | 6.30(3.27)    | 40.46 (4.65) | 17.40 (7.28)  | 74.84 (1.37)  | 2.78(1.41)    |
| $16 \times 16$          | 73.12 (0.69) | 70.10 (13.96)              | 81.39 (5.97)  | 61.28 (19.18) | 68.37 (2.70)  | 46.99 (24.09) | 39.58 (0.15) | 22.70 (11.70) | 73.52 (6.41)  | 28.07 (18.36) |
| $32 \times 32$          | 74.13 (1.39) | $\overline{100.00}$ (0.00) | 76.85 (5.79)  | 100.00 (0.00) | 45.93 (21.02) | 33.33 (47.14) | 44.87(6.65)  | 33.33 (47.14) | 83.42 (0.65)  | 0.00(0.00)    |
|                         |              |                            |               |               |               |               |              |               |               |               |
| $Trig. \Delta f.$       |              |                            |               | ABL           |               | <b>NAD</b>    |              | <b>STRIP</b>  |               | FP            |
|                         |              |                            | CTA(%)        | $ASR(\%)$     | CTA(%)        | $ASR(\%)$     | CTA(%)       | $ASR(\%)$     | CTA(%)        | $ASR(\%)$     |
| $2 \times 2$            |              |                            | 77.04(4.01)   | 1.86 (2.06)   | 94.68(0.60)   | 0.32(0.05)    | 65.05(2.53)  | 0.95(0.39)    | 51.95(2.11)   | 0.29(0.17)    |
| $4 \times 4$            |              |                            | 79.11(1.23)   | 1.04(0.93)    | 94.73(0.26)   | 0.27(0.04)    | 65.94(1.11)  | 1.02(0.34)    | 52.61 (0.49)  | 0.04(0.06)    |
| $8 \times 8$            |              |                            | 75.89(1.80)   | 4.14(2.19)    | 94.76(0.33)   | 0.24(0.07)    | 65.90(0.23)  | 7.65(4.03)    | 51.83         | 0.23          |
| $16 \times 16$          |              |                            | 79.29(3.46)   | 73.46 (3.25)  | 94.67(0.29)   | 0.07(0.04)    | 65.73(0.65)  | 59.97 (5.28)  | 53.38 (3.29)  | 28.53 (39.26) |

Table 7: Defenses for simple-trigger on GTSRB with distilled dataset size 2150.

<span id="page-17-3"></span>

| Data. (Size)\Def. |              | None         |              | SCAn          | AC           | SS            | Strip        |               |              |               |
|-------------------|--------------|--------------|--------------|---------------|--------------|---------------|--------------|---------------|--------------|---------------|
|                   | CTA (%)      | ASR (%)      | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       |
| CIFAR-10 (100)    | 26.28 (1.56) | 42.10 (4.16) | 27.23 (1.37) | 55.63 (4.78)  | 26.18 (1.70) | 44.56 (13.65) | 21.13 (0.67) | 5.98 (0.96)   | 25.05 (0.79) | 50.27 (15.74) |
| CIFAR-10 (500)    | 33.98 (0.63) | 90.87 (4.01) | 35.40 (0.77) | 82.63 (3.21)  | 33.23 (2.63) | 58.22 (38.26) | 32.52 (0.47) | 29.00 (10.45) | 34.33 (0.23) | 83.42 (6.34)  |
| GTSRB (430)       | 37.49 (1.98) | 69.14 (2.84) | 36.86 (1.28) | 53.68 (4.41)  | 23.91 (2.58) | 28.27 (11.06) | 13.39 (1.19) | 25.09 (12.68) | 30.88 (2.18) | 50.50 (13.23) |
| GTSRB (2150)      | 75.40 (0.39) | 65.28 (2.15) | 82.47 (1.81) | 70.51 (3.14)  | 65.84 (8.99) | 61.81 (1.22)  | 39.95 (3.48) | 26.01 (11.47) | 72.43 (5.26) | 63.77 (1.18)  |
| Data. (Size)\Def. |              |              | ABL          | NAD           | STRIP        | FP            |              |               |              |               |
|                   | CTA (%)      | ASR (%)      | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       | CTA (%)      | ASR (%)       |
| CIFAR-10 (100)    |              |              | 14.03 (0.92) | 73.30 (17.71) | 31.60 (2.10) | 21.67 (18.72) | 23.83 (1.30) | 35.96 (5.88)  | 15.87 (1.08) | 33.50 (38.47) |
| CIFAR-10 (500)    |              |              | 29.10 (1.51) | 13.41 (5.42)  | 37.75 (1.19) | 44.20 (10.85) | 30.61 (0.49) | 40.13 (18.51) | 20.83 (1.43) | 32.28 (44.66) |
| GTSRB(430)        |              |              | 32.26 (2.68) | 45.02 (6.54)  | 93.32 (0.34) | 67.18 (2.36)  | 33.88 (1.82) | 61.89 (2.63)  | 22.79 (2.98) | 53.54 (13.43) |
| GTSRB (2150)      |              |              | 80.90 (1.39) | 65.85 (0.63)  | 94.34 (0.13) | 33.68 (1.43)  | 67.91 (0.31) | 45.40 (2.13)  | 55.81 (1.39) | 68.73 (0.57)  |

Table 8: Defenses for relax-trigger on CIFAR-10 and GTSRB.

## B ABLATION STUDIES

### B.1 KIP-BASED BACKDOOR ATTACK ON IMAGENET

We perform our KIP-based backdoor attack on ImageNet. In our experiment, we randomly choose ten sub-classes to perform our experiment. We also resize each image in the ImageNet into 128x128. The experimental results show that our KIP-based backdoor attack is effective (see Table [9\)](#page-18-0).

### B.2 IMPACT OF IPC ON KIP-BASED BACKDOOR ATTACK

We examine the efficacy of KIP-based backdoor attack influenced by IPC (Image Per Class). We examine the efficacy of simple-trigger and relax-trigger under different sizes of synthetic dataset (IPC 10  $\sim$  IPC 50). The experimental results show that both CTA and ASR are gradually rising as

<span id="page-18-0"></span>

| Trigger-type   | Dataset  | Model | IPC (Image Per Class) | CTA (%) | ASR (%) |
|----------------|----------|-------|-----------------------|---------|---------|
| simple-trigger | ImageNet | NTK   | 10                    | 15.00   | 100.00  |
| simple-trigger | ImageNet | NTK   | 50                    | 16.60   | 100.00  |
| relax-trigger  | ImageNet | NTK   | 10                    | 16.40   | 100.00  |
| relax-trigger  | ImageNet | NTK   | 50                    | 17.00   | 100.00  |

Table 9: Efficacy of KIP-based backdoor attack on ImageNet.

the IPC increases (see Table [10\)](#page-18-1). The corresponding experiments for DOORPING is presented in Table [11.](#page-18-2)

<span id="page-18-1"></span>

| Dataset  | Trigger-type   | IPC (Image Per Class) | CTA (%)     | ASR (%)     |
|----------|----------------|-----------------------|-------------|-------------|
| CIFAR-10 | simple-trigger | 10                    | 41.70(0.25) | 100(0.00)   |
| CIFAR-10 | simple-trigger | 20                    | 42.58(0.23) | 100(0.00)   |
| CIFAR-10 | simple-trigger | 30                    | 43.29(0.35) | 100(0.00)   |
| CIFAR-10 | simple-trigger | 40                    | 43.55(0.42) | 100(0.00)   |
| CIFAR-10 | simple-trigger | 50                    | 43.66(0.40) | 100(0.00)   |
|          |                |                       |             |             |
| CIFAR-10 | relax-trigger  | 10                    | 41.66(0.01) | 100(0.00)   |
| CIFAR-10 | relax-trigger  | 20                    | 42.46(0.01) | 100(0.00)   |
| CIFAR-10 | relax-trigger  | 30                    | 42.99(0.08) | 100(0.00)   |
| CIFAR-10 | relax-trigger  | 40                    | 43.10(0.09) | 100(0.00)   |
| CIFAR-10 | relax-trigger  | 50                    | 43.64(0.40) | 100(0.00)   |
|          |                |                       |             |             |
| GTSRB    | simple-trigger | 10                    | 67.56(0.60) | 100(0.00)   |
| GTSRB    | simple-trigger | 20                    | 69.44(0.35) | 100(0.00)   |
| GTSRB    | simple-trigger | 30                    | 70.24(0.38) | 100(0.00)   |
| GTSRB    | simple-trigger | 40                    | 70.84(0.32) | 100(0.00)   |
| GTSRB    | simple-trigger | 50                    | 71.27(0.24) | 100(0.00)   |
|          |                |                       |             |             |
| GTSRB    | relax-trigger  | 10                    | 68.73(0.67) | 95.26(0.54) |
| GTSRB    | relax-trigger  | 20                    | 70.38(0.03) | 94.85(0.13) |
| GTSRB    | relax-trigger  | 30                    | 71.26(0.02) | 95.73(0.32) |
| GTSRB    | relax-trigger  | 40                    | 71.81(0.01) | 95.84(0.18) |
| GTSRB    | relax-trigger  | 50                    | 71.54(0.33) | 95.08(0.33) |

Table 10: Efficacy of KIP-based backdoor attack influenced by the size of the synthetic dataset.

<span id="page-18-2"></span>

| Dataset  | Trigger-type | IPC (Image Per Class) | CTA (%)      | ASR (%)      |
|----------|--------------|-----------------------|--------------|--------------|
| CIFAR-10 | DOORPING     | 10                    | 36.35(0.42)  | 80.00(40.00) |
| CIFAR-10 | DOORPING     | 20                    | 37.65(0.42)  | 70.00(45.83) |
| CIFAR-10 | DOORPING     | 30                    | 38.48(0.36)  | 90.00(30.00) |
| CIFAR-10 | DOORPING     | 40                    | 37.78(0.61)  | 70.00(45.83) |
| GTSRB    | DOORPING     | 10                    | 68.03(0.92)  | 90.00(30.00) |
| GTSRB    | DOORPING     | 20                    | 81.45 (0.46) | 80.00(40.00) |
| GTSRB    | DOORPING     | 30                    | 81.62(0.71)  | 100.00(0.00) |

Table 11: Efficacy of DOORPING influenced by the size of the synthetic dataset.

B.3 CROSS MODEL ABILITY OF KIP-BASED BACKDOOR ATTACK

The experiment for cross model ability is presented in Table [12.](#page-19-0) We train the distilled dataset poinsoned by simple-trigger and relax-trigger on 3-layers MLP and 3-layers ConvNet. The experimental results show that both CTA and ASR go up as we increase the IPC (Image Per Class), which suggests that the cross model issue may be relieved as the IPC is large enough.

<span id="page-19-0"></span>

| Dataset  | Trigger-type   | IPC (Image Per Class) | Cross_model    | CTA (%)      | ASR (%)       |
|----------|----------------|-----------------------|----------------|--------------|---------------|
| CIFAR-10 | simple-trigger | 10                    | MLP            | 11.58 (2.10) | 40.00 (48.98) |
| CIFAR-10 | simple-trigger | 10                    | CNN            | 47.37 (7.44) | 40.00 (48.98) |
| CIFAR-10 | simple-trigger | 10                    | NTK (baseline) | 41.70 (0.25) | 100.00 (0.00) |
| CIFAR-10 | simple-trigger | 50                    | MLP            | 48.08 (4.72) | 40.00 (48.98) |
| CIFAR-10 | simple-trigger | 50                    | CNN            | 95.96 (1.10) | 100.00 (0.00) |
| CIFAR-10 | simple-trigger | 50                    | NTK (baseline) | 43.66 (0.40) | 100.00 (0.00) |
| CIFAR-10 | relax-trigger  | 10                    | MLP            | 10.52 (7.44) | 19.40 (38.80) |
| CIFAR-10 | relax-trigger  | 10                    | CNN            | 64.21 (6.98) | 81.80 (11.44) |
| CIFAR-10 | relax-trigger  | 10                    | NTK (baseline) | 41.66 (0.74) | 100.00 (0.00) |
| CIFAR-10 | relax-trigger  | 50                    | MLP            | 44.24 (4.49) | 78.28 (24.57) |
| CIFAR-10 | relax-trigger  | 50                    | CNN            | 93.13 (2.24) | 82.80 (6.53)  |
| CIFAR-10 | relax-trigger  | 50                    | NTK (baseline) | 43.64 (0.40) | 100.00 (0.00) |

Table 12: Experiment of cross model ability of KIP-based backdoor attack.

### B.4 TRANSFERABILITY OF KIP-BASED BACKDOOR ATTACK

Our KIP-based backdoor attack can evade other data distillation techniques. In particular, we perform experiments to examine the transferability of our theory-induced triggers. We first use our simple-trigger and relax-trigger to poison the dataset. Then, we distill dataest with a different distillation method, FRePo [\(Zhou et al., 2022\)](#page-11-3) and DM [\(Zhao & Bilen, 2023\)](#page-11-4). The experimental results shows that our triggers can successfully transfer to the FrePo and DM (see Table [13](#page-19-1) and Table [14\)](#page-19-2).

<span id="page-19-1"></span>

| Trigger-type | Dataset        | IPC (Image Per Class) | Distillation | Model   | CTA (%) | ASR (%) |
|--------------|----------------|-----------------------|--------------|---------|---------|---------|
| CIFAR-10     | simple-trigger | 10                    | FRePO        | ConvNet | 60.32   | 83.10   |
| CIFAR-10     | relax-trigger  | 50                    | FRePO        | ConvNet | 68.34   | 81.61   |

Table 13: Experiment of transferability for FRePO.

<span id="page-19-2"></span>

| Trigger-type | Dataset        | IPC (Image Per Class) | Distillation | Model | CTA (%) | ASR $(%)$ |
|--------------|----------------|-----------------------|--------------|-------|---------|-----------|
| Cifar10      | simple-trigger | 10                    | DM           | MLP   | 36.41   | 77.03     |
| Cifar10      | simple-trigger | 50                    | DM           | MLP   | 36.88   | 76.79     |
| Cifar10      | relax-trigger  | 10                    | DM           | MLP   | 36.31   | 76.04     |
| Cifar10      | relax-trigger  | 50                    | DM           | MLP   | 36.81   | 76.21     |

Table 14: Experiment of transferability for DM.

### B.5 KIP-BASED BACKDOOR ATTACK ON NAS AND CL

We train our distilled dataset poinsoned by simple-trigger and relax-trigger in different scenarios, neural architecture search (NAS) and continual learning (CL). The experimental results are shown in Table [15](#page-19-3) and Table [16.](#page-20-0)

<span id="page-19-3"></span>

| Trigger-type                                           | Dataset            | IPC (Image Per Class)   Scenario   CTA $(\%)$ |            |                                        | $ASR(\%)$ |
|--------------------------------------------------------|--------------------|-----------------------------------------------|------------|----------------------------------------|-----------|
| $\frac{1}{2}$ simple-trigger CIFAR-10 $\frac{1}{2}$ 50 |                    |                                               | <b>NAS</b> | $\mid$ 37.49(3.44) $\mid$ 100.00(0.00) |           |
| relax-trigger                                          | CIFAR-10 $\mid$ 50 |                                               | <b>NAS</b> | $36.43(3.62)$   86.23(3.22)            |           |

Table 15: Experiment for NAS. The experiment result shows that our triggers remain effective for NAS.

Note that the details about our implementation of NAS and CL are described below.

<span id="page-20-0"></span>

| Trigger-type   | Dataset  | IPC (Image Per Class) | Scenario | CTA (%)     | ASR (%)      |
|----------------|----------|-----------------------|----------|-------------|--------------|
| simple-trigger | CIFAR-10 | 50                    | CL.      | 13.93(1.93) | 100.00(0.00) |
| simple-trigger | CIFAR-10 | 50                    | baseline | 13.60(1.66) | 100.00(0.00) |
| relax-trigger  | CIFAR-10 | 50                    | CL       | 20.13(2.94) | 60.94(21.68) |
| relax-trigger  | CIFAR-10 | 50                    | baseline | 14.00(3.54) | 43.11(7.83)  |

Table 16: Experiment for CL. The experiment result shows that both CTA and ASR are slightly higher than baseline.

- NAS : The process defines a search space (random search) that includes a range of possible model parameters such as the number of convolutional layers, the number of dense layers, and the size of the convolutional layers. The program randomly selects parameters from this space to generate multiple candidate model architectures. A CNN model is then built, comprising convolutional layers (Conv2D), batch normalization (BatchNormalization), activation functions (such as ReLU), pooling layers (MaxPooling2D), flattening layers (Flatten), fully connected layers (Dense), and optionally Dropout layers. Each model is compiled and trained using the Adam optimizer and categorical cross-entropy loss function, but in this case, the same dataset is used for evaluation (although typically, an independent test set should be used). The accuracy and loss functions of different models are compared, and ultimately the best model is selected and saved
- CL : The dataset is divided into different category-specific subsets (as in CIFAR-10, which is divided into 10 categories), each containing images and their corresponding labels. This allows the model to gradually train on each subset. A CNN model is built, including multiple convolutional layers (Conv2D), batch normalization layers (BatchNormalization), ReLU activation functions, max pooling layers (MaxPooling2D), and fully connected layers (Dense). The final layer uses a softmax activation function, a typical configuration for label classification tasks. The model is compiled using an RMSprop optimizer and categorical cross-entropy loss function. Further training optimization can be applied, such as using Elastic Weight Consolidation (EWC) to minimize the impact on the originally trained model when learning new subsets.

### B.6 PERFORMANCE OF THE TRIGGERS WITHOUT DISTILLATION

We perform the experiments on CIFAR-10 and GTSRB. We first utilize the simple-trigger and relax-trigger to poison the dataset. Then, we use 3-layers ConvNet to train a model and evaluate corresponding CTA and ASR. The experimental results demonstrate that our triggers simple-trigger and relax-trigger both remain effective (see Table [17\)](#page-20-1).

<span id="page-20-1"></span>

| Dataset      | Trigger-type   | Transparency (m) | CTA (%)      | ASR (%)       |
|--------------|----------------|------------------|--------------|---------------|
| CIFAR-10     | simple-trigger | 1                | 70.02 (0.40) | 100.00 (0.00) |
| CIFAR-10     | relax-trigger  | 0.3              | 70.02 (0.65) | 99.80 (0.04)  |
| CIFAR-10     | simple-trigger | 0.3              | 67.84 (0.36) | 95.50 (1.23)  |
|              |                |                  |              |               |
| <b>GTSRB</b> | simple-trigger | 1                | 72.47 (3.36) | 100.00 (0.00) |
| <b>GTSRB</b> | relax-trigger  | 0.3              | 75.50 (2.09) | 99.82 (0.09)  |
| <b>GTSRB</b> | simple-trigger | 0.3              | 70.21 (3.03) | 99.36 (0.20)  |

Table 17: Experiment of the performance of the triggers without distillation.
This is page 43 Printer: Opaque this

# 3 Linear Methods for Regression

# 3.1 Introduction

A linear regression model assumes that the regression function  $E(Y|X)$  is linear in the inputs  $X_1, \ldots, X_p$ . Linear models were largely developed in the precomputer age of statistics, but even in today's computer era there are still good reasons to study and use them. They are simple and often provide an adequate and interpretable description of how the inputs affect the output. For prediction purposes they can sometimes outperform fancier nonlinear models, especially in situations with small numbers of training cases, low signal-to-noise ratio or sparse data. Finally, linear methods can be applied to transformations of the inputs and this considerably expands their scope. These generalizations are sometimes called basis-function methods, and are discussed in Chapter 5.

In this chapter we describe linear methods for regression, while in the next chapter we discuss linear methods for classification. On some topics we go into considerable detail, as it is our firm belief that an understanding of linear methods is essential for understanding nonlinear ones. In fact, many nonlinear techniques are direct generalizations of the linear methods discussed here.

# 3.2 Linear Regression Models and Least Squares

As introduced in Chapter 2, we have an input vector  $X^T = (X_1, X_2, \ldots, X_n)$ , and want to predict a real-valued output  $Y$ . The linear regression model has the form

$$
f(X) = \beta_0 + \sum_{j=1}^{p} X_j \beta_j.
$$
 (3.1)

The linear model either assumes that the regression function  $E(Y|X)$  is linear, or that the linear model is a reasonable approximation. Here the  $\beta_j$ 's are unknown parameters or coefficients, and the variables  $X_j$  can come from different sources:

- quantitative inputs;
- transformations of quantitative inputs, such as log, square-root or square;
- basis expansions, such as  $X_2 = X_1^2$ ,  $X_3 = X_1^3$ , leading to a polynomial representation;
- numeric or "dummy" coding of the levels of qualitative inputs. For example, if G is a five-level factor input, we might create  $X_i$ ,  $j =$  $1, \ldots, 5$ , such that  $X_j = I(G = j)$ . Together this group of  $X_j$  repre- $\sum_{j=1}^{5} X_j \beta_j$ , one of the  $X_j$ s is one, and the others are zero. sents the effect of  $G$  by a set of level-dependent constants, since in
- interactions between variables, for example,  $X_3 = X_1 \cdot X_2$ .

No matter the source of the  $X_j$ , the model is linear in the parameters.

Typically we have a set of training data  $(x_1, y_1) \dots (x_N, y_N)$  from which to estimate the parameters  $\beta$ . Each  $x_i = (x_{i1}, x_{i2}, \dots, x_{ip})^T$  is a vector of feature measurements for the ith case. The most popular estimation method is least squares, in which we pick the coefficients  $\beta = (\beta_0, \beta_1, \dots, \beta_p)^T$ to minimize the residual sum of squares

$$
RSS(\beta) = \sum_{i=1}^{N} (y_i - f(x_i))^2
$$

$$
= \sum_{i=1}^{N} (y_i - \beta_0 - \sum_{j=1}^{p} x_{ij} \beta_j)^2. \quad (3.2)
$$

From a statistical point of view, this criterion is reasonable if the training observations  $(x_i, y_i)$  represent independent random draws from their population. Even if the  $x_i$ 's were not drawn randomly, the criterion is still valid if the  $y_i$ 's are conditionally independent given the inputs  $x_i$ . Figure 3.1 illustrates the geometry of least-squares fitting in the  $\mathbb{R}^{p+1}$ -dimensional

Image /page/2/Figure/1 description: A 3D scatter plot shows red dots representing data points scattered around a light blue plane. The plane is tilted and appears to be a regression plane fitted to the data. Vertical black lines connect some of the red dots to the plane, indicating the residuals or errors. The axes are labeled X1, X2, and Y, suggesting a multivariate regression model where Y is the dependent variable and X1 and X2 are independent variables. The overall visualization illustrates the concept of linear regression in three dimensions.

**FIGURE 3.1.** Linear least squares fitting with  $X \in \mathbb{R}^2$ . We seek the linear function of  $X$  that minimizes the sum of squared residuals from  $Y$ .

space occupied by the pairs  $(X, Y)$ . Note that  $(3.2)$  makes no assumptions about the validity of model (3.1); it simply finds the best linear fit to the data. Least squares fitting is intuitively satisfying no matter how the data arise; the criterion measures the average lack of fit.

How do we minimize (3.2)? Denote by **X** the  $N \times (p+1)$  matrix with each row an input vector (with a 1 in the first position), and similarly let y be the N-vector of outputs in the training set. Then we can write the residual sum-of-squares as

$$
RSS(\beta) = (\mathbf{y} - \mathbf{X}\beta)^{T}(\mathbf{y} - \mathbf{X}\beta).
$$
\n(3.3)

This is a quadratic function in the  $p + 1$  parameters. Differentiating with respect to  $\beta$  we obtain

$$
\frac{\partial \text{RSS}}{\partial \beta} = -2\mathbf{X}^T(\mathbf{y} - \mathbf{X}\beta)
$$
  
$$
\frac{\partial^2 \text{RSS}}{\partial \beta \partial \beta^T} = 2\mathbf{X}^T \mathbf{X}.
$$
(3.4)

Assuming (for the moment) that **X** has full column rank, and hence  $X^T X$ is positive definite, we set the first derivative to zero

$$
\mathbf{X}^T(\mathbf{y} - \mathbf{X}\beta) = 0 \tag{3.5}
$$

to obtain the unique solution

$$
\hat{\beta} = (\mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T \mathbf{y}.
$$
\n(3.6)

Image /page/3/Figure/1 description: This is a diagram illustrating vector projection. A horizontal axis is labeled x1. A vector labeled y is shown in red, originating from the origin and pointing upwards and to the left. A dashed red line extends vertically downwards from the tip of vector y to the horizontal axis. A vector labeled x2 is shown in cyan, originating from the origin and pointing upwards and to the right. The vectors x1 and x2 define a yellow-shaded region. A dashed red line extends from the tip of vector y to the line segment formed by the origin and x2, and a right angle symbol indicates that this dashed line is perpendicular to the line segment. The point where the dashed red line meets the line segment is labeled ŷ. This diagram visually represents the projection of vector y onto the subspace spanned by vector x2.

FIGURE 3.2. The N-dimensional geometry of least squares regression with two predictors. The outcome vector  $\mathbf y$  is orthogonally projected onto the hyperplane spanned by the input vectors  $\mathbf{x}_1$  and  $\mathbf{x}_2$ . The projection  $\hat{\mathbf{y}}$  represents the vector of the least squares predictions

The predicted values at an input vector  $x_0$  are given by  $\hat{f}(x_0) = (1 : x_0)^T \hat{\beta}$ ; the fitted values at the training inputs are

$$
\hat{\mathbf{y}} = \mathbf{X}\hat{\beta} = \mathbf{X}(\mathbf{X}^T\mathbf{X})^{-1}\mathbf{X}^T\mathbf{y},\tag{3.7}
$$

where  $\hat{y}_i = \hat{f}(x_i)$ . The matrix  $\mathbf{H} = \mathbf{X} (\mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T$  appearing in equation (3.7) is sometimes called the "hat" matrix because it puts the hat on y.

Figure 3.2 shows a different geometrical representation of the least squares estimate, this time in  $\mathbb{R}^N$ . We denote the column vectors of **X** by  $x_0, x_1, \ldots, x_p$ , with  $\mathbf{x}_0 \equiv 1$ . For much of what follows, this first column is treated like any other. These vectors span a subspace of  $\mathbb{R}^N$ , also referred to as the column space of **X**. We minimize  $RSS(\hat{\beta}) = ||\mathbf{y} - \mathbf{X}\hat{\beta}||^2$  by choosing  $\hat{\beta}$  so that the residual vector  $y - \hat{y}$  is orthogonal to this subspace. This orthogonality is expressed in (3.5), and the resulting estimate  $\hat{y}$  is hence the *orthogonal projection* of y onto this subspace. The hat matrix  $H$  computes the orthogonal projection, and hence it is also known as a projection matrix.

It might happen that the columns of  $X$  are not linearly independent, so that X is not of full rank. This would occur, for example, if two of the inputs were perfectly correlated, (e.g.,  $\mathbf{x}_2 = 3\mathbf{x}_1$ ). Then  $\mathbf{X}^T\mathbf{X}$  is singular and the least squares coefficients  $\hat{\beta}$  are not uniquely defined. However, the fitted values  $\hat{\mathbf{y}} = \mathbf{X}\hat{\beta}$  are still the projection of y onto the column space of  $X$ ; there is just more than one way to express that projection in terms of the column vectors of X. The non-full-rank case occurs most often when one or more qualitative inputs are coded in a redundant fashion. There is usually a natural way to resolve the non-unique representation, by recoding and/or dropping redundant columns in X. Most regression software packages detect these redundancies and automatically implement

46

some strategy for removing them. Rank deficiencies can also occur in signal and image analysis, where the number of inputs  $p$  can exceed the number of training cases N. In this case, the features are typically reduced by filtering or else the fitting is controlled by regularization (Section 5.2.3 and Chapter 18).

Up to now we have made minimal assumptions about the true distribution of the data. In order to pin down the sampling properties of  $\beta$ , we now assume that the observations  $y_i$  are uncorrelated and have constant variance  $\sigma^2$ , and that the  $x_i$  are fixed (non random). The variance–covariance matrix of the least squares parameter estimates is easily derived from (3.6) and is given by

$$
Var(\hat{\beta}) = (\mathbf{X}^T \mathbf{X})^{-1} \sigma^2.
$$
\n(3.8)

Typically one estimates the variance  $\sigma^2$  by

$$
\hat{\sigma}^2 = \frac{1}{N - p - 1} \sum_{i=1}^{N} (y_i - \hat{y}_i)^2.
$$

The  $N - p - 1$  rather than N in the denominator makes  $\hat{\sigma}^2$  an unbiased estimate of  $\sigma^2$ :  $E(\hat{\sigma}^2) = \sigma^2$ .

To draw inferences about the parameters and the model, additional assumptions are needed. We now assume that  $(3.1)$  is the correct model for the mean; that is, the conditional expectation of Y is linear in  $X_1, \ldots, X_p$ . We also assume that the deviations of  $Y$  around its expectation are additive and Gaussian. Hence

$$
Y = \mathbf{E}(Y|X_1, ..., X_p) + \varepsilon
$$
  
=  $\beta_0 + \sum_{j=1}^p X_j \beta_j + \varepsilon,$  (3.9)

where the error  $\varepsilon$  is a Gaussian random variable with expectation zero and variance  $\sigma^2$ , written  $\varepsilon \sim N(0, \sigma^2)$ .

Under (3.9), it is easy to show that

$$
\hat{\beta} \sim N(\beta, (\mathbf{X}^T \mathbf{X})^{-1} \sigma^2). \tag{3.10}
$$

This is a multivariate normal distribution with mean vector and variance– covariance matrix as shown. Also

$$
(N - p - 1)\hat{\sigma}^2 \sim \sigma^2 \chi^2_{N - p - 1},\tag{3.11}
$$

a chi-squared distribution with  $N - p - 1$  degrees of freedom. In addition  $\hat{\beta}$ and  $\hat{\sigma}^2$  are statistically independent. We use these distributional properties to form tests of hypothesis and confidence intervals for the parameters  $\beta_i$ .

Image /page/5/Figure/1 description: This is a line graph showing tail probabilities on the y-axis, ranging from 0.01 to 0.06, and Z values on the x-axis, ranging from 2.0 to 3.0. There are three lines plotted: 't30' (yellow), 't100' (blue), and 'normal' (green). All three lines show a decreasing trend as Z increases. Dashed horizontal lines are present at 0.01 and 0.05. Dashed vertical lines are also shown at approximately Z=1.9, Z=2.0, Z=2.1, and Z=2.6, corresponding to points where the lines intersect the horizontal dashed lines.

**FIGURE 3.3.** The tail probabilities  $Pr(|Z| > z)$  for three distributions,  $t_{30}$ ,  $t_{100}$ and standard normal. Shown are the appropriate quantiles for testing significance at the  $p = 0.05$  and  $0.01$  levels. The difference between t and the standard normal becomes negligible for N bigger than about 100.

To test the hypothesis that a particular coefficient  $\beta_j = 0$ , we form the standardized coefficient or Z-score

$$
z_j = \frac{\hat{\beta}_j}{\hat{\sigma}\sqrt{v_j}},\tag{3.12}
$$

where  $v_j$  is the jth diagonal element of  $(\mathbf{X}^T \mathbf{X})^{-1}$ . Under the null hypothesis that  $\beta_j = 0$ ,  $z_j$  is distributed as  $t_{N-p-1}$  (a t distribution with  $N-p-1$ degrees of freedom), and hence a large (absolute) value of  $z_j$  will lead to rejection of this null hypothesis. If  $\hat{\sigma}$  is replaced by a known value  $\sigma$ , then  $z_i$  would have a standard normal distribution. The difference between the tail quantiles of a t-distribution and a standard normal become negligible as the sample size increases, and so we typically use the normal quantiles (see Figure 3.3).

Often we need to test for the significance of groups of coefficients simultaneously. For example, to test if a categorical variable with k levels can be excluded from a model, we need to test whether the coefficients of the dummy variables used to represent the levels can all be set to zero. Here we use the  $F$  statistic,

$$
F = \frac{(\text{RSS}_0 - \text{RSS}_1)/(p_1 - p_0)}{\text{RSS}_1/(N - p_1 - 1)},
$$
\n(3.13)

where  $RSS<sub>1</sub>$  is the residual sum-of-squares for the least squares fit of the bigger model with  $p_1+1$  parameters, and RSS<sub>0</sub> the same for the nested smaller model with  $p_0 + 1$  parameters, having  $p_1 - p_0$  parameters constrained to be zero. The F statistic measures the change in residual sum-of-squares per additional parameter in the bigger model, and it is normalized by an estimate of  $\sigma^2$ . Under the Gaussian assumptions, and the null hypothesis that the smaller model is correct, the F statistic will have a  $F_{p_1-p_0,N-p_1-1}$  distribution. It can be shown (Exercise 3.1) that the  $z_i$  in (3.12) are equivalent to the F statistic for dropping the single coefficient  $\beta_j$  from the model. For large N, the quantiles of  $F_{p_1-p_0,N-p_1-1}$  approach those of  $\chi^2_{p_1-p_0}/(p_1-p_0)$ .

Similarly, we can isolate  $\beta_j$  in (3.10) to obtain a  $1-2\alpha$  confidence interval for  $\beta_i$ :

$$
(\hat{\beta}_j - z^{(1-\alpha)} v_j^{\frac{1}{2}} \hat{\sigma}, \ \hat{\beta}_j + z^{(1-\alpha)} v_j^{\frac{1}{2}} \hat{\sigma}). \tag{3.14}
$$

Here  $z^{(1-\alpha)}$  is the  $1-\alpha$  percentile of the normal distribution:

$$
z^{(1-0.025)} = 1.96,
$$
  
$$
z^{(1-0.05)} = 1.645, \text{ etc.}
$$

Hence the standard practice of reporting  $\hat{\beta} \pm 2 \cdot \text{se}(\hat{\beta})$  amounts to an approximate 95% confidence interval. Even if the Gaussian error assumption does not hold, this interval will be approximately correct, with its coverage approaching  $1 - 2\alpha$  as the sample size  $N \to \infty$ .

In a similar fashion we can obtain an approximate confidence set for the entire parameter vector  $\beta$ , namely

$$
C_{\beta} = {\beta | (\hat{\beta} - \beta)^T \mathbf{X}^T \mathbf{X} (\hat{\beta} - \beta)} \leq \hat{\sigma}^2 \chi_{p+1}^2^{(1-\alpha)}, \qquad (3.15)
$$

where  $\chi^2_{\ell}$  $\binom{(1-\alpha)}{1-\alpha}$  is the 1 –  $\alpha$  percentile of the chi-squared distribution on  $\ell$ degrees of freedom: for example,  $\chi_5^{2(1-0.05)} = 11.1, \chi_5^{2(1-0.1)} = 9.2$ . This confidence set for  $\beta$  generates a corresponding confidence set for the true function  $f(x) = x^T \beta$ , namely  $\{x^T \beta | \beta \in C_{\beta}\}\$  (Exercise 3.2; see also Figure 5.4 in Section 5.2.2 for examples of confidence bands for functions).

# 3.2.1 Example: Prostate Cancer

The data for this example come from a study by Stamey et al. (1989). They examined the correlation between the level of prostate-specific antigen and a number of clinical measures in men who were about to receive a radical prostatectomy. The variables are log cancer volume (lcavol), log prostate weight (lweight), age, log of the amount of benign prostatic hyperplasia (lbph), seminal vesicle invasion (svi), log of capsular penetration (lcp), Gleason score (gleason), and percent of Gleason scores 4 or 5 (pgg45). The correlation matrix of the predictors given in Table 3.1 shows many strong correlations. Figure 1.1 (page 3) of Chapter 1 is a scatterplot matrix showing every pairwise plot between the variables. We see that svi is a binary variable, and gleason is an ordered categorical variable. We see, for

|         | lcavol | lweight | age   | lbph   | svi   | lcp   | gleason |
|---------|--------|---------|-------|--------|-------|-------|---------|
| lweight | 0.300  |         |       |        |       |       |         |
| age     | 0.286  | 0.317   |       |        |       |       |         |
| lbph    | 0.063  | 0.437   | 0.287 |        |       |       |         |
| svi     | 0.593  | 0.181   | 0.129 | -0.139 |       |       |         |
| lcp     | 0.692  | 0.157   | 0.173 | -0.089 | 0.671 |       |         |
| gleason | 0.426  | 0.024   | 0.366 | 0.033  | 0.307 | 0.476 |         |
| pgg45   | 0.483  | 0.074   | 0.276 | -0.030 | 0.481 | 0.663 | 0.757   |

TABLE 3.1. Correlations of predictors in the prostate cancer data.

TABLE 3.2. Linear model fit to the prostate cancer data. The Z score is the coefficient divided by its standard error (3.12). Roughly a Z score larger than two in absolute value is significantly nonzero at the  $p = 0.05$  level.

| Term      | Coefficient | Std. Error | Z Score |
|-----------|-------------|------------|---------|
| Intercept | 2.46        | 0.09       | 27.60   |
| lcavol    | 0.68        | 0.13       | 5.37    |
| lweight   | 0.26        | 0.10       | 2.75    |
| age       | -0.14       | 0.10       | -1.40   |
| lbph      | 0.21        | 0.10       | 2.06    |
| svi       | 0.31        | 0.12       | 2.47    |
| lcp       | -0.29       | 0.15       | -1.87   |
| gleason   | -0.02       | 0.15       | -0.15   |
| pgg45     | 0.27        | 0.15       | 1.74    |

example, that both lcavol and lcp show a strong relationship with the response lpsa, and with each other. We need to fit the effects jointly to untangle the relationships between the predictors and the response.

We fit a linear model to the log of prostate-specific antigen, lpsa, after first standardizing the predictors to have unit variance. We randomly split the dataset into a training set of size 67 and a test set of size 30. We applied least squares estimation to the training set, producing the estimates, standard errors and Z-scores shown in Table 3.2. The Z-scores are defined in (3.12), and measure the effect of dropping that variable from the model. A Z-score greater than 2 in absolute value is approximately significant at the 5% level. (For our example, we have nine parameters, and the 0.025 tail quantiles of the  $t_{67-9}$  distribution are  $\pm 2.002$ !) The predictor lcavol shows the strongest effect, with lweight and svi also strong. Notice that lcp is not significant, once lcavol is in the model (when used in a model without lcavol, lcp is strongly significant). We can also test for the exclusion of a number of terms at once, using the  $F$ -statistic  $(3.13)$ . For example, we consider dropping all the non-significant terms in Table 3.2, namely age, lcp, gleason, and pgg45. We get

$$
F = \frac{(32.81 - 29.43)/(9 - 5)}{29.43/(67 - 9)} = 1.67,
$$
\n(3.16)

which has a p-value of 0.17 ( $Pr(F_{4,58} > 1.67) = 0.17$ ), and hence is not significant.

The mean prediction error on the test data is 0.521. In contrast, prediction using the mean training value of lpsa has a test error of 1.057, which is called the "base error rate." Hence the linear model reduces the base error rate by about 50%. We will return to this example later to compare various selection and shrinkage methods.

## 3.2.2 The Gauss–Markov Theorem

One of the most famous results in statistics asserts that the least squares estimates of the parameters  $\beta$  have the smallest variance among all linear unbiased estimates. We will make this precise here, and also make clear that the restriction to unbiased estimates is not necessarily a wise one. This observation will lead us to consider biased estimates such as ridge regression later in the chapter. We focus on estimation of any linear combination of the parameters  $\theta = a^T \beta$ ; for example, predictions  $f(x_0) = x_0^T \beta$  are of this form. The least squares estimate of  $a^T \beta$  is

$$
\hat{\theta} = a^T \hat{\beta} = a^T (\mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T \mathbf{y}.
$$
\n(3.17)

Considering **X** to be fixed, this is a linear function  $\mathbf{c}_0^T \mathbf{y}$  of the response vector **y**. If we assume that the linear model is correct,  $a^T\hat{\beta}$  is unbiased since

$$
E(a^T \hat{\beta}) = E(a^T (\mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T \mathbf{y})
$$
  
=  $a^T (\mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T \mathbf{X} \beta$   
=  $a^T \beta$ . (3.18)

The Gauss–Markov theorem states that if we have any other linear estimator  $\tilde{\theta} = \mathbf{c}^T \mathbf{y}$  that is unbiased for  $a^T \beta$ , that is,  $E(\mathbf{c}^T \mathbf{y}) = a^T \beta$ , then

$$
Var(a^T \hat{\beta}) \le Var(c^T \mathbf{y}).\tag{3.19}
$$

The proof (Exercise 3.3) uses the triangle inequality. For simplicity we have stated the result in terms of estimation of a single parameter  $a^T\beta$ , but with a few more definitions one can state it in terms of the entire parameter vector  $\beta$  (Exercise 3.3).

Consider the mean squared error of an estimator  $\tilde{\theta}$  in estimating  $\theta$ :

$$
MSE(\tilde{\theta}) = E(\tilde{\theta} - \theta)^2
$$
  
= Var(\tilde{\theta}) + [E(\tilde{\theta}) - \theta]^2. (3.20)

The first term is the variance, while the second term is the squared bias. The Gauss-Markov theorem implies that the least squares estimator has the smallest mean squared error of all linear estimators with no bias. However, there may well exist a biased estimator with smaller mean squared error. Such an estimator would trade a little bias for a larger reduction in variance. Biased estimates are commonly used. Any method that shrinks or sets to zero some of the least squares coefficients may result in a biased estimate. We discuss many examples, including variable subset selection and ridge regression, later in this chapter. From a more pragmatic point of view, most models are distortions of the truth, and hence are biased; picking the right model amounts to creating the right balance between bias and variance. We go into these issues in more detail in Chapter 7.

Mean squared error is intimately related to prediction accuracy, as discussed in Chapter 2. Consider the prediction of the new response at input  $x_0$ ,

$$
Y_0 = f(x_0) + \varepsilon_0. \tag{3.21}
$$

Then the expected prediction error of an estimate  $\tilde{f}(x_0) = x_0^T \tilde{\beta}$  is

$$
E(Y_0 - \tilde{f}(x_0))^2 = \sigma^2 + E(x_0^T \tilde{\beta} - f(x_0))^2
$$
  
=  $\sigma^2 + MSE(\tilde{f}(x_0)).$  (3.22)

Therefore, expected prediction error and mean squared error differ only by the constant  $\sigma^2$ , representing the variance of the new observation  $y_0$ .

## 3.2.3 Multiple Regression from Simple Univariate Regression

The linear model (3.1) with  $p > 1$  inputs is called the *multiple linear* regression model. The least squares estimates (3.6) for this model are best understood in terms of the estimates for the *univariate*  $(p = 1)$  linear model, as we indicate in this section.

Suppose first that we have a univariate model with no intercept, that is,

$$
Y = X\beta + \varepsilon. \tag{3.23}
$$

The least squares estimate and residuals are

$$
\hat{\beta} = \frac{\sum_{1}^{N} x_i y_i}{\sum_{1}^{N} x_i^2},
$$
\n
$$
r_i = y_i - x_i \hat{\beta}.
$$
\n(3.24)

In convenient vector notation, we let  $\mathbf{y} = (y_1, \dots, y_N)^T$ ,  $\mathbf{x} = (x_1, \dots, x_N)^T$ and define

$$
<\langle \mathbf{x}, \mathbf{y} \rangle = \sum_{i=1}^{N} x_i y_i,
$$
  
$$
= \mathbf{x}^T \mathbf{y}, \quad (3.25)
$$

the *inner product* between **x** and  $y^1$ . Then we can write

$$
\hat{\beta} = \frac{\langle \mathbf{x}, \mathbf{y} \rangle}{\langle \mathbf{x}, \mathbf{x} \rangle},
$$
  
$$
\mathbf{r} = \mathbf{y} - \mathbf{x}\hat{\beta}.
$$
 (3.26)

As we will see, this simple univariate regression provides the building block for multiple linear regression. Suppose next that the inputs  $x_1, x_2, \ldots, x_p$ (the columns of the data matrix **X**) are orthogonal; that is  $\langle \mathbf{x}_i, \mathbf{x}_k \rangle = 0$ for all  $j \neq k$ . Then it is easy to check that the multiple least squares estimates  $\hat{\beta}_j$  are equal to  $\langle \mathbf{x}_j, \mathbf{y} \rangle / \langle \mathbf{x}_j, \mathbf{x}_j \rangle$ —the univariate estimates. In other words, when the inputs are orthogonal, they have no effect on each other's parameter estimates in the model.

Orthogonal inputs occur most often with balanced, designed experiments (where orthogonality is enforced), but almost never with observational data. Hence we will have to orthogonalize them in order to carry this idea further. Suppose next that we have an intercept and a single input x. Then the least squares coefficient of x has the form

$$
\hat{\beta}_1 = \frac{\langle \mathbf{x} - \bar{x} \mathbf{1}, \mathbf{y} \rangle}{\langle \mathbf{x} - \bar{x} \mathbf{1}, \mathbf{x} - \bar{x} \mathbf{1} \rangle},\tag{3.27}
$$

where  $\bar{x} = \sum_i x_i/N$ , and  $\mathbf{1} = \mathbf{x}_0$ , the vector of N ones. We can view the estimate (3.27) as the result of two applications of the simple regression (3.26). The steps are:

- 1. regress **x** on 1 to produce the residual  $z = x \bar{x}$ 1;
- 2. regress **y** on the residual **z** to give the coefficient  $\hat{\beta}_1$ .

In this procedure, "regress b on a" means a simple univariate regression of b on a with no intercept, producing coefficient  $\hat{\gamma} = \langle \mathbf{a}, \mathbf{b} \rangle / \langle \mathbf{a}, \mathbf{a} \rangle$  and residual vector  $\mathbf{b} - \hat{\gamma} \mathbf{a}$ . We say that **b** is adjusted for **a**, or is "orthogonalized" with respect to a.

Step 1 orthogonalizes x with respect to  $x_0 = 1$ . Step 2 is just a simple univariate regression, using the orthogonal predictors 1 and z. Figure 3.4 shows this process for two general inputs  $x_1$  and  $x_2$ . The orthogonalization does not change the subspace spanned by  $x_1$  and  $x_2$ , it simply produces an orthogonal basis for representing it.

This recipe generalizes to the case of p inputs, as shown in Algorithm 3.1. Note that the inputs  $z_0, \ldots, z_{j-1}$  in step 2 are orthogonal, hence the simple regression coefficients computed there are in fact also the multiple regression coefficients.

<sup>1</sup>The inner-product notation is suggestive of generalizations of linear regression to different metric spaces, as well as to probability spaces.

Image /page/11/Figure/1 description: This is a diagram illustrating vector projection. A red vector labeled 'y' is shown originating from the origin. A dashed red line drops perpendicularly from the tip of 'y' to a horizontal line, indicating the projection of 'y' onto the horizontal axis. The projected vector is labeled 'ŷ'. Two vectors, 'x1' and 'x2', are shown along the horizontal axis. A green vector labeled 'z' originates from the tip of 'x2' and points towards the tip of 'y'. The region enclosed by the origin, the tip of 'x2', and the tip of 'y' is shaded yellow. Right angle symbols are used to denote perpendicularity between the dashed red line and the horizontal axis, and between the vector 'z' and the horizontal axis.

FIGURE 3.4. Least squares regression by orthogonalization of the inputs. The vector  $x_2$  is regressed on the vector  $x_1$ , leaving the residual vector **z**. The regression of y on z gives the multiple regression coefficient of  $x_2$ . Adding together the projections of **y** on each of  $x_1$  and **z** gives the least squares fit  $\hat{y}$ .

Algorithm 3.1 Regression by Successive Orthogonalization.

- 1. Initialize  $z_0 = x_0 = 1$ .
- 2. For  $j = 1, 2, ..., p$ 
  - Regress  $\mathbf{x}_j$  on  $\mathbf{z}_0, \mathbf{z}_1, \ldots, , \mathbf{z}_{j-1}$  to produce coefficients  $\hat{\gamma}_{\ell j}$  =  $\langle \mathbf{z}_{\ell}, \mathbf{x}_{j} \rangle / \langle \mathbf{z}_{\ell}, \mathbf{z}_{\ell} \rangle, \ell = 0, \ldots, j - 1$  and residual vector  $\mathbf{z}_{j} =$  $\mathbf{x}_j - \sum_{k=0}^{j-1} \hat{\gamma}_{kj} \mathbf{z}_k.$
- 3. Regress y on the residual  $\mathbf{z}_p$  to give the estimate  $\hat{\beta}_p$ .

The result of this algorithm is

$$
\hat{\beta}_p = \frac{\langle \mathbf{z}_p, \mathbf{y} \rangle}{\langle \mathbf{z}_p, \mathbf{z}_p \rangle}.
$$
\n(3.28)

Re-arranging the residual in step 2, we can see that each of the  $x_j$  is a linear combination of the  $z_k$ ,  $k \leq j$ . Since the  $z_j$  are all orthogonal, they form a basis for the column space of X, and hence the least squares projection onto this subspace is  $\hat{y}$ . Since  $z_p$  alone involves  $x_p$  (with coefficient 1), we see that the coefficient (3.28) is indeed the multiple regression coefficient of **y** on  $x_p$ . This key result exposes the effect of correlated inputs in multiple regression. Note also that by rearranging the  $x_j$ , any one of them could be in the last position, and a similar results holds. Hence stated more generally, we have shown that the  $j$ th multiple regression coefficient is the univariate regression coefficient of y on  $\mathbf{x}_{j\cdot012...(j-1)(j+1)...,p}$ , the residual after regressing  $\mathbf{x}_j$  on  $\mathbf{x}_0, \mathbf{x}_1, \ldots, \mathbf{x}_{j-1}, \mathbf{x}_{j+1}, \ldots, \mathbf{x}_p$ :

The multiple regression coefficient  $\hat{\beta}_j$  represents the additional contribution of  $x_j$  on y, after  $x_j$  has been adjusted for  $x_0, x_1, \ldots, x_{j-1}$ ,  $\mathbf{x}_{j+1}, \ldots, \mathbf{x}_p$ .

If  $\mathbf{x}_p$  is highly correlated with some of the other  $\mathbf{x}_k$ 's, the residual vector  $\mathbf{z}_p$  will be close to zero, and from (3.28) the coefficient  $\hat{\beta}_p$  will be very unstable. This will be true for all the variables in the correlated set. In such situations, we might have all the Z-scores (as in Table 3.2) be small any one of the set can be deleted—yet we cannot delete them all. From (3.28) we also obtain an alternate formula for the variance estimates (3.8),

$$
Var(\hat{\beta}_p) = \frac{\sigma^2}{\langle \mathbf{z}_p, \mathbf{z}_p \rangle} = \frac{\sigma^2}{\|\mathbf{z}_p\|^2}.
$$
 (3.29)

In other words, the precision with which we can estimate  $\hat{\beta}_p$  depends on the length of the residual vector  $z_p$ ; this represents how much of  $x_p$  is unexplained by the other  $\mathbf{x}_k$ 's.

Algorithm 3.1 is known as the Gram–Schmidt procedure for multiple regression, and is also a useful numerical strategy for computing the estimates. We can obtain from it not just  $\hat{\beta}_p$ , but also the entire multiple least squares fit, as shown in Exercise 3.4.

We can represent step 2 of Algorithm 3.1 in matrix form:

$$
\mathbf{X} = \mathbf{Z}\mathbf{\Gamma},\tag{3.30}
$$

where **Z** has as columns the  $z_j$  (in order), and  $\Gamma$  is the upper triangular matrix with entries  $\hat{\gamma}_{kj}$ . Introducing the diagonal matrix **D** with jth diagonal entry  $D_{jj} = ||\mathbf{z}_j||$ , we get

$$
\mathbf{X} = \mathbf{Z}\mathbf{D}^{-1}\mathbf{D}\mathbf{\Gamma}
$$
  
=  $\mathbf{Q}\mathbf{R},$  (3.31)

the so-called QR decomposition of **X**. Here **Q** is an  $N \times (p+1)$  orthogonal matrix,  $\mathbf{Q}^T \mathbf{Q} = \mathbf{I}$ , and **R** is a  $(p+1) \times (p+1)$  upper triangular matrix.

The QR decomposition represents a convenient orthogonal basis for the column space of X. It is easy to see, for example, that the least squares solution is given by

$$
\hat{\beta} = \mathbf{R}^{-1} \mathbf{Q}^T \mathbf{y},\tag{3.32}
$$

$$
\hat{\mathbf{y}} = \mathbf{Q}\mathbf{Q}^T\mathbf{y}.\tag{3.33}
$$

Equation  $(3.32)$  is easy to solve because **R** is upper triangular (Exercise 3.4).

#### 3.2.4 Multiple Outputs

Suppose we have multiple outputs  $Y_1, Y_2, \ldots, Y_K$  that we wish to predict from our inputs  $X_0, X_1, X_2, \ldots, X_p$ . We assume a linear model for each output

$$
Y_k = \beta_{0k} + \sum_{j=1}^p X_j \beta_{jk} + \varepsilon_k \tag{3.34}
$$

$$
= f_k(X) + \varepsilon_k. \tag{3.35}
$$

With N training cases we can write the model in matrix notation

$$
Y = XB + E. \tag{3.36}
$$

Here Y is the  $N \times K$  response matrix, with ik entry  $y_{ik}$ , X is the  $N \times (p+1)$ input matrix, **B** is the  $(p + 1) \times K$  matrix of parameters and **E** is the  $N \times K$  matrix of errors. A straightforward generalization of the univariate loss function (3.2) is

$$
RSS(\mathbf{B}) = \sum_{k=1}^{K} \sum_{i=1}^{N} (y_{ik} - f_k(x_i))^2
$$
\n(3.37)

$$
= \operatorname{tr}[(\mathbf{Y} - \mathbf{X}\mathbf{B})^T (\mathbf{Y} - \mathbf{X}\mathbf{B})]. \tag{3.38}
$$

The least squares estimates have exactly the same form as before

$$
\hat{\mathbf{B}} = (\mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T \mathbf{Y}.
$$
\n(3.39)

Hence the coefficients for the kth outcome are just the least squares estimates in the regression of  $y_k$  on  $x_0, x_1, \ldots, x_p$ . Multiple outputs do not affect one another's least squares estimates.

If the errors  $\varepsilon = (\varepsilon_1, \ldots, \varepsilon_K)$  in (3.34) are correlated, then it might seem appropriate to modify (3.37) in favor of a multivariate version. Specifically, suppose  $Cov(\varepsilon) = \Sigma$ , then the multivariate weighted criterion

$$
RSS(\mathbf{B}; \Sigma) = \sum_{i=1}^{N} (y_i - f(x_i))^T \Sigma^{-1} (y_i - f(x_i))
$$
(3.40)

arises naturally from multivariate Gaussian theory. Here  $f(x)$  is the vector function  $(f_1(x),...,f_K(x))^T$ , and  $y_i$  the vector of K responses for observation  $i$ . However, it can be shown that again the solution is given by (3.39); K separate regressions that ignore the correlations (Exercise 3.11). If the  $\Sigma_i$  vary among observations, then this is no longer the case, and the solution for B no longer decouples.

In Section 3.7 we pursue the multiple outcome problem, and consider situations where it does pay to combine the regressions.

# 3.3 Subset Selection

There are two reasons why we are often not satisfied with the least squares estimates (3.6).

- The first is *prediction accuracy*: the least squares estimates often have low bias but large variance. Prediction accuracy can sometimes be improved by shrinking or setting some coefficients to zero. By doing so we sacrifice a little bit of bias to reduce the variance of the predicted values, and hence may improve the overall prediction accuracy.
- The second reason is *interpretation*. With a large number of predictors, we often would like to determine a smaller subset that exhibit the strongest effects. In order to get the "big picture," we are willing to sacrifice some of the small details.

In this section we describe a number of approaches to variable subset selection with linear regression. In later sections we discuss shrinkage and hybrid approaches for controlling variance, as well as other dimension-reduction strategies. These all fall under the general heading model selection. Model selection is not restricted to linear models; Chapter 7 covers this topic in some detail.

With subset selection we retain only a subset of the variables, and eliminate the rest from the model. Least squares regression is used to estimate the coefficients of the inputs that are retained. There are a number of different strategies for choosing the subset.

#### 3.3.1 Best-Subset Selection

Best subset regression finds for each  $k \in \{0, 1, 2, \ldots, p\}$  the subset of size k that gives smallest residual sum of squares (3.2). An efficient algorithm the leaps and bounds procedure (Furnival and Wilson, 1974)—makes this feasible for  $p$  as large as 30 or 40. Figure 3.5 shows all the subset models for the prostate cancer example. The lower boundary represents the models that are eligible for selection by the best-subsets approach. Note that the best subset of size 2, for example, need not include the variable that was in the best subset of size 1 (for this example all the subsets are nested). The best-subset curve (red lower boundary in Figure 3.5) is necessarily decreasing, so cannot be used to select the subset size  $k$ . The question of how to choose k involves the tradeoff between bias and variance, along with the more subjective desire for parsimony. There are a number of criteria that one may use; typically we choose the smallest model that minimizes an estimate of the expected prediction error.

Many of the other approaches that we discuss in this chapter are similar, in that they use the training data to produce a sequence of models varying in complexity and indexed by a single parameter. In the next section we use

Image /page/15/Figure/1 description: This is a scatter plot showing the relationship between subset size k and residual sum-of-squares. The x-axis is labeled "Subset Size k" and ranges from 0 to 8. The y-axis is labeled "Residual Sum-of-Squares" and ranges from 0 to 100. There are multiple gray dots scattered across the plot, with the density of dots generally decreasing as the subset size increases. A red line connects red dots, indicating a trend. At subset size 0, the residual sum-of-squares is approximately 98. It drops to about 44 at subset size 1, then to about 37 at subset size 2, and continues to decrease gradually to around 30 by subset size 8. The red dots represent the mean or median residual sum-of-squares for each subset size, while the gray dots represent individual data points.

FIGURE 3.5. All possible subset models for the prostate cancer example. At each subset size is shown the residual sum-of-squares for each model of that size.

cross-validation to estimate prediction error and select  $k$ ; the AIC criterion is a popular alternative. We defer more detailed discussion of these and other approaches to Chapter 7.

## 3.3.2 Forward- and Backward-Stepwise Selection

Rather than search through all possible subsets (which becomes infeasible for  $p$  much larger than 40), we can seek a good path through them. Forwardstepwise selection starts with the intercept, and then sequentially adds into the model the predictor that most improves the fit. With many candidate predictors, this might seem like a lot of computation; however, clever updating algorithms can exploit the QR decomposition for the current fit to rapidly establish the next candidate (Exercise 3.9). Like best-subset regression, forward stepwise produces a sequence of models indexed by  $k$ , the subset size, which must be determined.

Forward-stepwise selection is a greedy algorithm, producing a nested sequence of models. In this sense it might seem sub-optimal compared to best-subset selection. However, there are several reasons why it might be preferred:

58

- *Computational*; for large p we cannot compute the best subset sequence, but we can always compute the forward stepwise sequence (even when  $p \gg N$ ).
- *Statistical*; a price is paid in variance for selecting the best subset of each size; forward stepwise is a more constrained search, and will have lower variance, but perhaps more bias.

Image /page/16/Figure/3 description: This is a line graph showing the expected value of the squared difference between the estimated coefficient vector and the true coefficient vector, E||\hat{\beta}(k) - \beta||^2, on the y-axis, plotted against the subset size k on the x-axis. The graph displays four different methods: Best Subset (black dots), Forward Stepwise (orange dots), Backward Stepwise (blue dots), and Forward Stagewise (green dots). The y-axis ranges from 0.65 to 0.95, and the x-axis ranges from 0 to 30. All methods show a decreasing trend as the subset size increases, with the Best Subset, Forward Stepwise, and Backward Stepwise methods converging to a similar low value around 0.65 for larger subset sizes. The Forward Stagewise method shows a slower decrease and remains at a higher value compared to the other methods.

FIGURE 3.6. Comparison of four subset-selection techniques on a simulated linear regression problem  $Y = X^T \beta + \varepsilon$ . There are  $N = 300$  observations on  $p = 31$ standard Gaussian variables, with pairwise correlations all equal to 0.85. For 10 of the variables, the coefficients are drawn at random from a  $N(0, 0.4)$  distribution; the rest are zero. The noise  $\varepsilon \sim N(0, 6.25)$ , resulting in a signal-to-noise ratio of 0.64. Results are averaged over 50 simulations. Shown is the mean-squared error of the estimated coefficient  $\hat{\beta}(k)$  at each step from the true  $\beta$ .

Backward-stepwise selection starts with the full model, and sequentially deletes the predictor that has the least impact on the fit. The candidate for dropping is the variable with the smallest Z-score (Exercise 3.10). Backward selection can only be used when  $N > p$ , while forward stepwise can always be used.

Figure 3.6 shows the results of a small simulation study to compare best-subset regression with the simpler alternatives forward and backward selection. Their performance is very similar, as is often the case. Included in the figure is forward stagewise regression (next section), which takes longer to reach minimum error.

On the prostate cancer example, best-subset, forward and backward selection all gave exactly the same sequence of terms.

Some software packages implement hybrid stepwise-selection strategies that consider both forward and backward moves at each step, and select the "best" of the two. For example in the R package the step function uses the AIC criterion for weighing the choices, which takes proper account of the number of parameters fit; at each step an add or drop will be performed that minimizes the AIC score.

Other more traditional packages base the selection on F-statistics, adding "significant" terms, and dropping "non-significant" terms. These are out of fashion, since they do not take proper account of the multiple testing issues. It is also tempting after a model search to print out a summary of the chosen model, such as in Table 3.2; however, the standard errors are not valid, since they do not account for the search process. The bootstrap (Section 8.2) can be useful in such settings.

Finally, we note that often variables come in groups (such as the dummy variables that code a multi-level categorical predictor). Smart stepwise procedures (such as step in R) will add or drop whole groups at a time, taking proper account of their degrees-of-freedom.

### 3.3.3 Forward-Stagewise Regression

Forward-stagewise regression (FS) is even more constrained than forwardstepwise regression. It starts like forward-stepwise regression, with an intercept equal to  $\bar{y}$ , and centered predictors with coefficients initially all 0. At each step the algorithm identifies the variable most correlated with the current residual. It then computes the simple linear regression coefficient of the residual on this chosen variable, and then adds it to the current coefficient for that variable. This is continued till none of the variables have correlation with the residuals—i.e. the least-squares fit when  $N > p$ .

Unlike forward-stepwise regression, none of the other variables are adjusted when a term is added to the model. As a consequence, forward stagewise can take many more than  $p$  steps to reach the least squares fit, and historically has been dismissed as being inefficient. It turns out that this "slow fitting" can pay dividends in high-dimensional problems. We see in Section 3.8.1 that both forward stagewise and a variant which is slowed down even further are quite competitive, especially in very highdimensional problems.

Forward-stagewise regression is included in Figure 3.6. In this example it takes over 1000 steps to get all the correlations below  $10^{-4}$ . For subset size k, we plotted the error for the last step for which there where  $k$  nonzero coefficients. Although it catches up with the best fit, it takes longer to do so.
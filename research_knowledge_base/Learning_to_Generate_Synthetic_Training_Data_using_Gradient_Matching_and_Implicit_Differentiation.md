# Learning to Generate Synthetic Training Data using Gradient Matching and Implicit Differentiation

1st <PERSON> *Lomonosov MSU* Moscow, Russia <EMAIL>

*Abstract*—Using huge training datasets can be costly and inconvenient. This article explores various data distillation techniques that can reduce the amount of data required to successfully train deep networks. Inspired by recent ideas, we suggest new data distillation techniques based on generative teaching networks, gradient matching, and the Implicit Function Theorem. Experiments with the MNIST image classification problem show that the new methods are computationally more efficient than previous ones and allow to increase the performance of models trained on distilled data.

*Index Terms*—data distillation, gradient matching, implicit differentiation, generative teaching network.

## I. INTRODUCTION

In machine learning, the purpose of data distillation [\[1\]](#page-7-0) is to compress the original dataset while maintaining the performance of the models trained on it. Generalizability is also needed: the ability of the dataset to train models of architectures that were not involved in the distillation process. Since training with less data is usually faster, distillation can be useful in practice. For example, it can be used to speed up a neural architecture search (NAS) task. Acceleration is achieved through the faster training of candidates.

In many recent works [\[1\]](#page-7-0), [\[3\]](#page-7-1), [\[5\]](#page-7-2), [\[6\]](#page-7-3), [\[7\]](#page-7-4), distillation is formulated as an optimization problem with the objects of a new dataset as parameters for optimization. Therefore, to distill the dataset for an image classification task, pixels of images have to be optimized. First, all new objects are initialized with random noise, then these objects are used to train the student (randomly selected network). Then the student misclassification loss is calculated on real data. Finally, a gradient descent step is used to update the synthetic objects. Gradients can be calculated by backpropagating the error through the entire student's learning process. The step of this procedure can be very time-consuming and memoryintensive, so there is a need for an alternative. In [\[2\]](#page-7-5), the authors use the implicit function theorem to solve the memory consumption problem. In [\[3\]](#page-7-1), the data distillation problem has been reformulated to use gradient matching loss and speed up the optimization of synthetic objects and reduce memory usage.

There is an alternative to optimizing the pixels of synthetic data. In [\[4\]](#page-7-6), the authors suggest to optimize parameters of the generator model (generative teaching network or GTN) to 2<sup>nd</sup> Alexander D'yakonov *Lomonosov MSU* Moscow, Russia <EMAIL>

produce synthetic data from noise and labels. This creates a dataset that provides better performance for models trained with it. The disadvantage is that the authors used backpropagation through the learning process for optimization. Inspired by recent ideas in the field of data distillation, we propose replacing it with gradient matching or with implicit differentiation to make the procedure less computationally expensive. We have found that this allows not only to reduce memory costs but also to create more efficient and generalizable datasets. In addition, we investigate the use of augmentation in the distillation procedure and in models' learning on distilled data.

The rest of the paper is divided into 8 sections. We first overview the related work in section [II](#page-0-0) and give a general formulation of the data distillation problem in [III.](#page-1-0) We then analyse the first data distillation algorithm [\[1\]](#page-7-0) and discuss its problems in section [IV.](#page-1-1) A brief description of the algorithms for implicit differentiation [\[2\]](#page-7-5) and gradient matching [\[3\]](#page-7-1) can be found in sections [V](#page-2-0) and [VI.](#page-2-1) [VII](#page-3-0) presents the generative teaching network architecture that we use in our work. The [VIII](#page-4-0) section contains the results of experiments with the MNIST image classification benchmark. In [VIII-A](#page-4-1) we compare the results of all the described distillation methods, limiting the distillation time to a constant. In [VIII-B](#page-4-2) and [VIII-C](#page-5-0) we show results of new distillation techniques when training a generator with gradient matching and implicit differentiation, respectively. In [VIII-D](#page-6-0) we study the use of augmentation by distillation, and in [VIII-E](#page-6-1) we check the generalization of the data obtained with the new methods. Finally, we present our findings in section [IX.](#page-6-2) All code can be found in our GitHub  $page<sup>1</sup>$  $page<sup>1</sup>$  $page<sup>1</sup>$ .

## II. RELATED WORK

<span id="page-0-0"></span>The general idea behind data distillation is to optimize hyperparameters (pixels of synthetic images are hyperparameters in an image classification problem) using gradients (also called hypergradients). The use of backpropagation [\[8\]](#page-7-7) to optimize hyperparameters has been suggested in [\[9\]](#page-7-8) and [\[10\]](#page-7-9). Backpropagation through L-BFGS [\[11\]](#page-7-10) and SGD with momentum [\[12\]](#page-7-11) has been introduced in [\[7\]](#page-7-4). Because of the great spatial complexity of this backpropagation, a more efficient

<span id="page-0-1"></span><sup>1</sup><https://github.com/dm-medvedev/EfficientDistillation>

one has been proposed in [\[5\]](#page-7-2). There were also the results of data optimization experiments.

The successful distillation of the MNIST dataset [\[16\]](#page-7-12) was shown in [\[1\]](#page-7-0). Leaving only 10 examples (one for each class), and thus reducing the dataset volume by 600 times, the LeNet model [\[6\]](#page-7-3) trained on compressed dataset showed an accuracy close to that of training on the original dataset. The authors also mentioned the distilled data generalization problem and suggested using a fixed distribution to initialize the network.

In [\[6\]](#page-7-3), the authors show a way to distill both objects and their labels. Their experiments show that such distillation increases accuracy for multiple image classification benchmarks and allows distilled datasets to consist of fewer samples than number of classes. Despite this, recent works [\[3\]](#page-7-1), [\[4\]](#page-7-6) do not use label distillation, because joint optimization complicates the problem since labels depend on objects, and vice versa.

It is important to note that most of the works in data distillation were inspired by network distillation [\[16\]](#page-7-12), that is the transfer of knowledge from an ensemble of well-trained models into a single compact one.

## III. GENERAL FORMULATION

<span id="page-1-0"></span>Let  $\lambda$  be teacher parameters. These can be either GTN network's parameters, or synthetic objects' parameters (e.g. pixels of synthetic images). To update  $\lambda$ , we must first train the student network  $\theta$  on synthetic data, minimizing the task specific loss  $\mathcal{L}_{\mathcal{S}}$  (e.g. cross-entropy), and then get the loss on real data  $\mathcal{L}_{\mathcal{T}}$ . To take care of generalizability, student's initialization goes from preset distribution  $p(\theta_0)$ . Afterall, the optimization problem for  $\lambda$  can be formulated as follows:

$$
\lambda^* := \underset{\lambda}{\operatorname{argmin}} \mathbb{E}_{\theta_0 \sim p(\theta_0)} \mathcal{L}_{\mathcal{T}}^*, \text{ where } (1)
$$
  
$$
\mathcal{L}_{\mathcal{T}}^* := \mathcal{L}_{\mathcal{T}}(\theta^*(\lambda)), \qquad \theta^*(\lambda) := \underset{\theta}{\operatorname{argmin}} \mathcal{L}_{\mathcal{S}}(\lambda, \theta).
$$

<span id="page-1-3"></span>To resolve the [\(1\)](#page-1-2) problem we can calculate gradient of  $\mathcal{L}_{\mathcal{T}}$ with respect to  $\lambda$  to do the gradient descent step:

$$
\frac{\partial \mathcal{L}_{\mathcal{T}}^{*}}{\partial \lambda} = \frac{\partial \mathcal{L}_{\mathcal{T}}}{\partial \lambda} + \frac{\partial \mathcal{L}_{\mathcal{T}}}{\partial \theta} \cdot \frac{\partial \theta^{*}}{\partial \lambda} = \frac{\partial \mathcal{L}_{\mathcal{T}}}{\partial \theta} \frac{\partial \theta^{*}}{\partial \lambda}.
$$
 (2)

In this work we use cross-entropy loss as  $\mathcal{L}_{\mathcal{T}}$  and there is an explicit dependence only on  $\theta$  and parameters of real data, so  $\frac{\partial \mathcal{L}_{\mathcal{T}}}{\partial \lambda} = 0$ . Thus, the main part is the calculation of  $\frac{\partial \theta^*}{\partial \lambda}$ . Where the dependence of  $\theta^*$  on  $\lambda$  comes from student's training procedure. In our work, we use two methods of calculating [\(2\)](#page-1-3): backpropagation through the student's learning process [\[1\]](#page-7-0) and implicit differentiation [\[2\]](#page-7-5). Such a gradient can also be called hypergradient, since it is a gradient with respect to  $\lambda$ , which is a set of hyperparameters in the original student learning problem.

<span id="page-1-1"></span>

## IV. BACKPROPAGATION THROUGH THE STUDENT'S LEARNING PROCESS

This data distillation algorithm was suggested in [\[1\]](#page-7-0) and it is based on the assumption that the student's learning procedure is differentiable. This means that we can backpropogate 1: Input: teacher's parameters  $\lambda$ , student's initialization distribution  $p(\theta_0)$ , number of distillation epochs K, number of student's learning steps N, real data  $\mathcal{T}$ , learning rate η.

2: **for** 
$$
k = 1, ..., K
$$
 **do**:

3:  $\mathcal{B}^{\mathcal{T}} \sim \mathcal{T}$ ,  $\theta_0 \sim p(\theta_0)$  ⊳ sample batch and weights 4: **Memory**  $\leftarrow \theta_0$   $\triangleright$  store initial weights 5: **for**  $n = 0, ...N - 1$  **do**:

- 6:  $g_n = \eta \frac{\partial \mathcal{L}_{\mathcal{S}}(\lambda, \theta_n)}{\partial \theta_n}$
- $θ<sub>n+1</sub> = θ<sub>n</sub> θ<sub>n</sub>$ <br>7:  $θ<sub>n+1</sub> = θ<sub>n</sub> g<sub>n</sub>$
- 8: **Memory**  $\leftarrow$   $g_n$ ,  $\theta_{n+1}$   $\triangleright$  store graph and weights.
- 9:  $\mathcal{L}_{\mathcal{T}} = \text{ClassificationLoss}(\mathcal{B}^{\mathcal{T}}, \theta_N(\lambda))$
- 10:  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}} \leftarrow \text{hypergrad}_{\text{unroll}}(\text{Memory}, \mathcal{L}_{\mathcal{T}}) \Rightarrow \text{Fig. 2}$  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}} \leftarrow \text{hypergrad}_{\text{unroll}}(\text{Memory}, \mathcal{L}_{\mathcal{T}}) \Rightarrow \text{Fig. 2}$  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}} \leftarrow \text{hypergrad}_{\text{unroll}}(\text{Memory}, \mathcal{L}_{\mathcal{T}}) \Rightarrow \text{Fig. 2}$ <br>11: **Update**( $\lambda, \nabla_{\lambda} \mathcal{L}_{\mathcal{T}}$ )  $\Rightarrow$  update with any optimizer 11: Update( $\lambda$ ,  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}}$ )  $\Rightarrow$  update with any optimizer

12: Output: 
$$
\lambda
$$

<span id="page-1-5"></span>Fig. 1. Backpropagation through the learning process.

- 1: **Input:** loss on real data  $\mathcal{L}_{\mathcal{T}}$ , computational graph and weights Memory.
- 2:  $\theta_N \leftarrow$ **Memory**,  $v = \frac{\partial \mathcal{L_T}}{\partial \theta_N}$ ,  $\nabla_{\lambda} \mathcal{L_T} = 0$ 3: for  $n = N - 1, ...0$  do:
- 4:  $g_n, \theta_n \leftarrow$  **Memory**
- 5:  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}}$  -= grad (func =  $g_n$ , wrt =  $\lambda$ , vec =  $v$ )
- 6:  $v \rightarrow \textbf{grad}\left(\textbf{func} = g_n, \textbf{wrt} = \theta_n, \textbf{vec} = v\right)$

7: Output:  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}}$ 

<span id="page-1-4"></span>Fig. 2. **hypergrad**<sub>unroll</sub> (Memory,  $\mathcal{L}_{\mathcal{T}}$ ).

<span id="page-1-2"></span>gradient through it. We will denote it as **unroll**. Let  $\theta_i$  be the student's parameters obtained at the i-th step of the training procedure,  $\mathcal{B}^{\mathcal{T}}$  be a batch of original data, and  $\eta$  be the learning rate, then:

$$
\theta_0 \sim p(\theta_0);
$$
  
\n
$$
\theta_{n+1} = \theta_n - \eta \nabla_{\theta} \mathcal{L}_{\mathcal{S}}(\lambda, \theta_n); \quad k = 0, ..., N - 1; \quad (3)
$$
  
\n
$$
\mathcal{L}_{\mathcal{T}} = \text{ClassificationLoss}(\mathcal{B}^{\mathcal{T}}, \theta_N(\lambda)) \to \min_{\lambda}.
$$

The learning rate  $\eta$  can be optimized in the same way as  $\lambda$ , but in [\[3\]](#page-7-1) and [\[7\]](#page-7-4) it was found that this leads to overfitting of the synthetic dataset to the architecture of student used in distillaion process. To write out the desired derivative  $\frac{\partial \theta^*(\lambda)}{\partial \lambda}$ , we can unroll the learning procedure (see full derivation in [\[2\]](#page-7-5)):

$$
\frac{\partial \theta^{*}(\lambda)}{\partial \lambda} = \sum_{1 \le j \le N} \left[ \prod_{1 \le k < j} \left( I - \eta \frac{\partial^2 \mathcal{L}_{\mathcal{S}}(\lambda, \theta_{N-k})}{\partial \theta^2} \right) \right] \cdot \frac{\partial^2 \mathcal{L}_{\mathcal{S}}(\lambda, \theta_{N-j})}{\partial \theta \partial \lambda} \cdot (-1). \tag{4}
$$

The resulting algorithm (see Fig. [1](#page-1-5) and [2\)](#page-1-4) can be implemented using the Higher library [\[17\]](#page-7-13). Note that Higher allows to backporopogate through many optimizers besides simple gradient descent. In our paper we use SGD with momentum [\[12\]](#page-7-11). Note that **grad** in Fig. [1](#page-1-5) and [2](#page-1-4) denotes Vector Jacobian product.

This distillation method is both time and space consuming. To perform a single step of updating  $\lambda$  it is necessary to perform  $N$  (see Fig. [1\)](#page-1-5) student optimization steps, while all intermediate results (copies of the student weights) must be stored in memory. Considering that usually a student's training can take many optimization steps, the efficiency problem become the main one. There is also a problem with the generalization of resulting syntetic dataset, which can be solved by sampling student's initialization and architecture. In our work we only randomly sample initializations.

Note that the procedure of student's training on the resulting synthetic dataset can be carried out in different ways. New data, parameterized with  $\lambda$ , can be used as single large batch or it can be split into several smaller ones. This split can be useful to reduce memory consumption per training step. Instead of randomly sample distilled objects, the authors of the original work propose to attach each of them to a specific batch. These batches can have a certain order in an epoch. In our paper, we use the same schemes, and in addition, we choose  $K$  (see Fig. [1\)](#page-1-5) to stay within the particular time limit. Let *ic* (input count) be the number of batches of the synthetic dataset, note that it must be divisor of  $N$ . In our experiments we try  $ic = 1$  and  $ic = 10$ .

## V. IMPLICIT DIFFERENTIATION

<span id="page-2-0"></span>This method suggested in [\[2\]](#page-7-5) is based on implicit function theorem:

*Theorem 1 (Cauchy, Implicit Function Theorem):*

Let  $\frac{\partial \mathcal{L_S}}{\partial \theta}(\lambda, \theta) : \Lambda \times \Theta \to \Theta$ , be a continuously differentiable function. Fix a point  $(\lambda', \theta')$  with  $\frac{\partial \mathcal{L}_{\mathcal{S}}}{\partial \theta}(\lambda', \theta') = 0$ . If the Jacobian matrix  $\frac{\partial^2 \mathcal{L}_S}{\partial \theta^2}$  is invertible, then there exists an open set  $\lambda : U \subseteq \Lambda$  containing  $\lambda'$  such that there exists a unique continuously differentiable function  $\theta^* : U \to \Theta$ , such that:

$$
\theta^*(\boldsymbol{\lambda}')=\theta^{'} \quad \text{and} \quad \forall \boldsymbol{\lambda} \in U, \quad \frac{\partial \mathcal{L_S}}{\partial \theta}(\boldsymbol{\lambda},\theta^*(\boldsymbol{\lambda}))=0.
$$

Moreover, the partial derivatives of  $\theta^*$  in U are given by the matrix product:

$$
\frac{\partial \theta^*}{\partial \lambda}(\lambda) = -\left[\frac{\partial^2 \mathcal{L}_S}{\partial \theta^2}(\lambda, \theta^*(\lambda))\right]^{-1} \frac{\partial^2 \mathcal{L}_S}{\partial \theta \partial \lambda}(\lambda, \theta^*(\lambda)).
$$
 (5)

So, if there was an efficient way to invert the matrix, we would simply use [\(5\)](#page-2-2), after the student  $\theta$  has reached a local minimum, assuming  $\frac{\partial \mathcal{L}_{\mathcal{S}}}{\partial \theta}(\lambda, \theta^*(\lambda)) \approx 0$ . But the inversion operation is time costly, so the authors used the approximation by the Neumann series:

$$
\[
\left[\frac{\partial^2 \mathcal{L}_\mathcal{S}}{\partial \theta^2}(\lambda, \theta^*(\lambda))\right]^{-1}
\]
$$

$$
= \lim_{i \to \infty} \sum_{j=0}^i \left[I - \frac{\partial^2 \mathcal{L}_\mathcal{S}}{\partial \theta^2}(\lambda, \theta^*(\lambda))\right]^j.
$$
(6)

1: **Input:** teacher's parameters  $\lambda$ , student's initialization distribution  $p(\theta_0)$ , number of distillation epochs K, number of student's learning steps  $\zeta_{\theta}$ , real data T, learning rate η.

2: for  $k = 1, ..., K$  do 3:  $\mathcal{B}^{\mathcal{T}} \sim \mathcal{T}, \quad \theta \sim p(\theta_0)$ 4: for  $n = 1, ..., \zeta_{\theta}$  do

5: 
$$
\theta = \eta \frac{\partial \mathcal{L}_{\mathcal{S}}(\lambda, \theta)}{\partial \theta}
$$

6:  $\mathcal{L}_{\mathcal{T}} = \text{ClassificationLoss}(\mathcal{B}^{\mathcal{T}}, \theta)$ 

7:  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}} = \text{hypergrad}_{\text{IFT}}(\mathcal{L}_{\mathcal{T}}, \mathcal{L}_{\mathcal{S}}, \lambda, \theta) \quad \text{be} \text{Fig. 4}$  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}} = \text{hypergrad}_{\text{IFT}}(\mathcal{L}_{\mathcal{T}}, \mathcal{L}_{\mathcal{S}}, \lambda, \theta) \quad \text{be} \text{Fig. 4}$  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}} = \text{hypergrad}_{\text{IFT}}(\mathcal{L}_{\mathcal{T}}, \mathcal{L}_{\mathcal{S}}, \lambda, \theta) \quad \text{be} \text{Fig. 4}$ 8: Update( $\lambda$ ,  $\nabla_{\lambda} \mathcal{L}_{\mathcal{T}}$ )  $\Rightarrow$  update with any optimizer return  $\lambda$ 

<span id="page-2-5"></span>Image /page/2/Figure/17 description: Fig. 3. Distillation with implicit differentiation.

1: **Input:** loss on real data  $\mathcal{L}_{\mathcal{T}}$ , loss on synthetic data  $\mathcal{L}_{\mathcal{S}}$ , teacher's parameters  $\lambda$ , student's parameters  $\theta$ .

2: 
$$
p = v = \frac{\partial \mathcal{L_T}}{\partial \theta}
$$
  
3: **for**  $j = 1, ..., N$  **do**  $\triangleright N$  — number of elements in (6)  
4:  $v = \alpha \cdot \text{grad}(\text{func} = \frac{\partial \mathcal{L_S}}{\partial \theta}, \text{wrt} = \theta, \text{vec} = v)$   
5:  $p += v$   
6: **return**  $p = -\alpha \cdot \text{grad}(\text{func} = \frac{\partial \mathcal{L_S}}{\partial \theta}, \text{wrt} = \lambda, \text{vec} = p)$   
Fig. 4. hypergrad<sub>IFT</sub>( $\mathcal{L_T}, \mathcal{L_S}, \lambda, \theta$ ).

<span id="page-2-3"></span>To approximate the desired derivative, we just need to take the first few elements of the [\(6\)](#page-2-4) series. To ensure the convergence of the series, the maximum absolute eigenvalue of the matrix must be less than one. Therefore, the authors used the additional hyperparameter  $\alpha$ :

$$
\[
\left[
\frac{\partial^2 \mathcal{L}_S}{\partial \theta^2}(\lambda, \theta^*(\lambda))
\right]^{-1}
\approx
\qquad (7)
\]
$$

$$
\approx \alpha \sum_{j=0}^N 
\[
\left[I - \alpha \frac{\partial^2 \mathcal{L}_S}{\partial \theta^2}(\lambda, \theta^*(\lambda))
\right]^j
\cdot
\]
$$

The resulting algorithm (see Fig. [3](#page-2-5) and [4\)](#page-2-3) has no problems with memory consumption since there is no need to store copies of the student  $\theta$ . And, despite the many approximations in calculations, the experimental results show that method has a competitive performance (see Table [IV\)](#page-6-3).

<span id="page-2-2"></span>Another interesting detail of this method is that there is no dependence on which optimizer is used to train the student, and on the order (curriculum) of batches of synthetic data. So, in our paper we only use single large batch of synthetic data. The original work [\[2\]](#page-7-5) lacks a detailed description of the experimental results, so it can be found in our paper (see section [VIII-C\)](#page-5-0). We used the open-source  $code^2$  $code^2$  as the basis for the implementing the method.

## VI. GRADIENT MATCHING

<span id="page-2-4"></span><span id="page-2-1"></span>The gradient matching method (GM) was proposed in [\[3\]](#page-7-1), and it solves a different problem than the general one (see

<span id="page-2-6"></span><sup>2</sup><https://github.com/AvivNavon/AuxiLearn>

section [III\)](#page-1-0). The main difference is that we want not only to train the student  $\theta$  to achieve a good performance on real data but also to get such a solution as if it was trained on real data. To formulate this let  $D(\theta_1, \theta_2)$  be the function of how close one student's parameters are to another. Let  $\theta^{\mathcal{S}}$  and  $\theta^{\mathcal{T}}$  be parameters obtained by training on distilled and real data, respectively.  $\zeta_{\mathcal{S}}$  and  $\zeta_{\mathcal{T}}$  are the number of steps to train the student on synthetic and real data. The optimization of student is done with  $opt_\theta$  (it can be any known optimization algorithm), then:

$$
\lambda^* = \operatorname*{argmin}_{\lambda} \mathbb{E}_{\theta_0 \sim p_{\theta_0}} \Big[ \sum_{n=1}^N D(\theta_n^S, \theta_n^{\mathcal{T}}) \Big], \quad \text{where:} \tag{8}
$$

$$
\theta_t^{\mathcal{S}} = \text{opt}_{\theta}(\mathcal{L}_{\mathcal{S}}(\lambda, \theta_{t-1}^{\mathcal{S}}), \zeta_{\mathcal{S}}), \qquad \theta_t^{\mathcal{T}} = \text{opt}_{\theta}(\mathcal{L}_{\mathcal{T}}(\theta_{t-1}^{\mathcal{T}}), \zeta_{\mathcal{T}}).
$$

Let  $D(\theta_{t-1}^S, \theta_{t-1}^T) \approx 0$ , such assumption is true if we are close to the problem solution. Note that  $\theta_n = \theta_{n-1} - \nabla_{\theta} \mathcal{L}_{\mathcal{S}}$ , then:

$$
\lambda^* = \operatorname{argmin}_{\lambda} \mathbb{E}_{\theta_0 \sim P_{\theta_0}} \left[ \sum_{n=1}^{N-1} D(\nabla_{\theta} \mathcal{L}_{\mathcal{S}}(\lambda, \theta_n), \nabla_{\theta} \mathcal{L}_{\mathcal{T}}(\theta_n)) \right] (9)
$$

The distance function  $D$  is just the sum (in our paper for GTN experiments we used the mean) of the cosine distance functions for each student layer  $\theta^l$ . Let A and B be gradient tensors with respect to layer parameters. Let  $i$  be the index of the output axis (e.g. for a convolutional layer this is the index of the output channel). Let  $A_i$  and  $B_i$  be flat gradient vectors corresponding to each output element indexed by  $i$  then:

$$
D(\nabla_{\theta} \mathcal{L}_{\mathcal{S}}, \nabla_{\theta} \mathcal{L}_{\mathcal{T}}) = \sum_{l=1}^{L} d(\nabla_{\theta^l} \mathcal{L}_{\mathcal{S}}, \nabla_{\theta^l} \mathcal{L}_{\mathcal{T}}), \text { where } (10)
$$
$$
d(A, B) = \sum_{i=1}^{\dim(A)} \left( 1 - \frac{A_i \\cdot B_i}{\\left| \\! \\! A_i \\! \\! \right| \\! \\! \\! \\! B_i} \right).
$$

The most interesting detail here is that the authors suggest to update  $\lambda$  after each step of student optimization, so now we don't need to wait until it reaches a local minimum, as it was before. The authors also propose not to store student copies and to minimize  $D(\nabla_{\theta} \mathcal{L}_{\mathcal{S}}(\lambda, \theta_{t-1}), \nabla_{\theta} \mathcal{L}_{\mathcal{T}}(\theta_{t-1}))$  for each step separately. So there is no backpropagation through  $opt_{\theta}$ . Both of these proposals make the gradient matching method very computational effective.

The peculiarity of this loss function is that the gradient of one synthetic object depends on other objects from the same batch, because of a normalization operation in the  $d$ equation [\(10\)](#page-3-1). It makes the optimization problem harder and can cause negative effects (see Table [II\)](#page-5-1). So authors decided to distill objects separately for each class.

Note that gradient matching is independent of the student training optimization algorithm. There is only one assumption that the direction should be based on the gradient. Another 1: **Input**: teacher's parameters  $\lambda$  and synthetic objects  $S(\lambda)$ , student's initialization distribution  $p(\theta_0)$ , number of distillation epochs K, number of student's learning steps  $\zeta_{\theta}$ , real data  $\mathcal T$ , learning rate  $\eta_\theta$ , number of inner loop steps N.

2: **for** 
$$
k = 0, ..., K - 1
$$
 **do**  
3:  $\theta_0 \sim p_{\theta_0}$   
4: **for**  $n = 0, ..., N - 1$  **do**  
5:  $\mathcal{B}^{\mathcal{T}} \sim \mathcal{T}, \quad \mathcal{B}^{\mathcal{S}} \sim \mathcal{S}(\lambda)$   
6:  $\mathcal{L}_{\mathcal{T}} = \text{ClassificationLoss}(\mathcal{B}^{\mathcal{T}}, \theta_n)$   
7:  $\mathcal{L}_{\mathcal{S}} = \text{ClassificationLoss}(\mathcal{B}^{\mathcal{S}}, \theta_n)$   
8:  $\mathcal{L}(\lambda) = D(\nabla_{\theta} \mathcal{L}_{\mathcal{S}}(\lambda, \theta_n), \nabla_{\theta} \mathcal{L}_{\mathcal{T}}(\theta_n))$   
9: **Update** $(\lambda, \nabla_{\lambda} \mathcal{L}(\lambda))$   
10:  $\theta_{n+1} \leftarrow opt_{\theta}(\mathcal{L}_{\mathcal{S}}(\lambda, \theta_n), \zeta_{\theta}, \eta_{\theta})$   
11: **Output:**  $\lambda$ 

Fig. 5. Gradient matching.

detail is that the curriculum (the order of the synthetic batches in the student's learning procedure) can be learned with this distillation method. We used open-source  $code^3$  $code^3$  as the implementation of this method.

## VII. GENERATIVE TEACHING NETWORK

<span id="page-3-0"></span>The idea first appeared in [\[4\]](#page-7-6), where authors suggested to use the generator as the teacher  $\lambda$ . The input of the generator is a concatenation of noise and one hot encoded label (for conditional generation). In the original paper, the authors use backpropagation through the student's learning process to train the generator, which is inconvenient for practical use due to high memory consumption, so in our paper, we show that the same or even better results can be achieved more effectively by using gradient matching or implicit differentiation.

<span id="page-3-1"></span>Experimental results in [\[4\]](#page-7-6) show that using a generator can help improve student performance. The best results were achieved with the learned curriculum. This was done by treating the generator input as teacher parameters and fixing their order. Thus, the generator produces only a finite number of synthetic objects and gives them for training the student as batches in a fixed order. This makes sense since the use of a generator can be seen as a more general case of usual distillation (when the parameters of objects are optimized).

If the generator input is synthetic images and the generation operation is the product of the images and generator parameters, which are the identity matrix, then there will be the usual data distillation. In our paper, we check if we can improve distillation performance using larger generators.

Note that the size in our experiments is controlled by the *k* hyperparameter (see Fig. [6\)](#page-4-3). The generator consists of two linear layers and two convolutional layers. The output size of the first layer is k. And  $|k/2| \times$  (width)  $\times$  height of picture is the output size of the second layer.  $\lfloor k/4 \rfloor$  is the number of output channels of the first convolution.

<span id="page-3-2"></span><sup>3</sup><https://github.com/VICO-UoE/DatasetCondensation>

Image /page/4/Figure/0 description: This is a diagram illustrating a neural network architecture. The process begins with two inputs, 'label' and 'noise', which are combined and fed into a 'Linear (d, k)' layer. This is followed by a 'LeakyRelu' activation function. The output then goes into another 'Linear' layer with dimensions '(k, k/2 \* width \* hight)', followed by another 'LeakyRelu'. Subsequently, there are two 'Conv2d' layers. The first 'Conv2d' layer has dimensions '(k/2, k/4)' and is followed by a 'LeakyRelu'. The second 'Conv2d' layer has dimensions '(k/4, 1)' and is followed by a 'Tanh' activation function, representing the final output of the network.

<span id="page-4-3"></span>Fig. 6. Generator's architecture.  $k$  — hyperparameter to control network's size.  $d = 64$  — generator's input.

Hereinafter, unless otherwise indicated, we use the following notation: **DD** (data distillation) — distillation, when the parameters of the teacher  $\lambda$  are pixels of synthetic images, and GTN — for distillation using a generator. Note that the generator has two modes: GTN-rnd — generator with random noise as input, (GTN-lrn) — generator with learned input.

## VIII. EXPERIMENTS

<span id="page-4-1"></span><span id="page-4-0"></span>

### A. Distillation with time limit

The neural architecture search (NAS) is one of the most promising areas for distillation and it is important to note that the time spent on distillation should be added to the time spent on the NAS, this idea was also mentioned in review<sup>[4](#page-4-4)</sup> of [\[4\]](#page-7-6). So, in this section, we check the performance of all known distillation methods. We think that it is fair to distill the data by all methods for the same limited time. We have chosen a time limit of  $\approx 15$  minutes, and it is based on common sense and NAS time spent in similar experiments [\[3\]](#page-7-1). Note that this limit may not be accurate, as distillation takes an integer number of steps, and each step may take slightly different times.

To check the performance we use the following scheme. First we train teacher  $\lambda$  with three restarts. The number of steps is determined by the time limit indicated above. Then, to get the final results we train five randomly initialized students  $\theta$  for each of the three teachers. Each student's training takes 1000 optimization steps.

In our work we use the MNIST [\[14\]](#page-7-14) benchmark and make the same preparations as in [\[4\]](#page-7-6). We extract part of the training data for validation (10 thousands of images) and use it to get the best teacher hyperparameters. We use  $|\mathcal{B}^{\mathcal{T}}| = 256$ batch size of training data. For most of our experiments we use ConvNet [\[19\]](#page-7-15) as a student. As student's optimizer we use SGD with momentum with the same parameters as suggested in [\[3\]](#page-7-1). We use the same teacher optimizers as in the original papers [\[1\]](#page-7-0), [\[3\]](#page-7-1), [\[4\]](#page-7-6). The volume of synthetic data can be controlled by *ipc* (images per class) parameter. For each table in this paper, the largest numbers in the column are shown in bold.

<span id="page-4-5"></span>TABLE I MEAN AND STANDARD DEVIATION OF TEST ACCURACY FOR DIFFERENT DISTILLATION ALGORITHMS.

| Method + Teacher                                | Accuracy       | Params  | GPU (MiB)      |
|-------------------------------------------------|----------------|---------|----------------|
| GM + DD ( $K = 60$ ,<br>$\zeta_{\theta} = 50$ ) | $94.9 \pm 0.1$ | 78.4 K  | $\approx 2390$ |
| unroll + DD ( $ic = 1$ )                        | $88.4 \pm 0.3$ | 78.4 K  | $\approx 4432$ |
| unroll + DD ( $ic = 10$ )                       | $79.2 \pm 0.7$ | 784 K   | $\approx 4426$ |
| unroll + GTN-lrn<br>( $ic = 1$ )                | $92.0 \pm 0.3$ | 1.646 M | $\approx 4480$ |
| unroll + GTN-lrn<br>( $ic = 10$ )               | $91.6 \pm 0.5$ | 1.704 M | $\approx 4480$ |
| unroll + GTN-rnd                                | $91.7 \pm 0.3$ | 1.640 M | $\approx 4480$ |

Table [I](#page-4-5) shows mean and standard deviation of test accuracy, reached by students trained on distilled data. Note that there is only one difference from previous works, we use time limit for each distillation procedure, so there is degradation in performance. For this experiment, we use  $K = 1000, N = 10$ as default hyperparameters values.

To check the memory consumption we use a special tool<sup>[5](#page-4-6)</sup>, which can measure the GPU memory usage. Note that using of the unroll distillation procedure consumes the most memory. The second column shows the number of teacher parameters, and although GTN  $(k = 64)$  is twice as large as DD, there is not much difference in memory usage.

<span id="page-4-2"></span>

### B. Training generator with gradient matching

In this section we explore the use of gradient matching to train teacher generator. We first check the hyperparameters for this distillation method. N controls frequency of student's reinitialization,  $\zeta_{\theta}$  controls the speed at which teacher parameters are updated. Fig. [7](#page-5-2) (a-d) shows the non-trivial relationship between performance and hyperparameter choice. We assume that such a dependence can be caused by the time limit and the fact that increasing the values of these hyperparameters may cause longer convergence. Note that in previous works [\[1\]](#page-7-0), [\[3\]](#page-7-1), [\[4\]](#page-7-6) where no time limit was used, increasing *ipc* always resulted in better performance.

Fig[.7.](#page-5-2)e shows that fixation the generator input is really important for gradient matching distillation because teacher  $\lambda$ training diverges when using random input. Another important detail mentioned above is that the gradient must be calculated per class. Table [II](#page-5-1) shows the results for per class case and not. It seems that per class distillation gives significantly better results.

Fig. [7.](#page-5-2)f shows the accuracy achieved with data distilled with generators of different sizes (marked with different  $k$ ), and without a generator (DD). This plot depicts the dependency between the number of synthetic images per class (*ipc*) and student's performance on test. It seems that the correct size selection for the generator allows to get better performance. More detailed results can be found in Tables [II](#page-5-1) and [III.](#page-5-3) For experiment in Table [II,](#page-5-1) we use  $ipc = 10$ ,  $ic = 1$ ,  $N =$ 10,  $K = 110$ ,  $\zeta_{\theta} = 10$  and  $k = 64$  for GTN as default

<span id="page-4-4"></span><sup>4</sup>[https://openreview.net/forum?id=HJg](https://openreview.net/forum?id=HJg_ECEKDr) ECEKDr

<span id="page-4-6"></span><sup>5</sup>[https://pytorch.org/docs/stable/cuda.html#torch.cuda.max](https://pytorch.org/docs/stable/cuda.html#torch.cuda.max_memory_reserved)\_memory\_ [reserved](https://pytorch.org/docs/stable/cuda.html#torch.cuda.max_memory_reserved)

Image /page/5/Figure/0 description: The image displays six plots labeled a) through f), illustrating the dependence of student performance on various hyperparameters. Plots a), b), c), and d) show accuracy on the y-axis against different parameters on the x-axis. Plot a) shows accuracy versus 'images per class: ipc' for 'learned input', with accuracy peaking at 50 images per class (around 0.963) and decreasing at 100 images per class (around 0.962). Plot b) shows accuracy versus 'student's learning steps: \$\zeta^{\theta}\$' for 'learned input', with accuracy peaking at 10 steps (around 0.951) and decreasing to around 0.92 at 50 steps. Plot c) shows accuracy versus 'teacher's learning steps: N' for 'learned input', with accuracy peaking at 10 steps (around 0.951) and decreasing to around 0.935 at 50 steps. Plot d) shows accuracy versus 'input count: ic' for 'learned input', with accuracy peaking at 5 input counts (around 0.956) and remaining similar at 10 input counts (around 0.956). Plot e) shows accuracy versus 'images per class: ipc' for 'random input', with accuracy decreasing from around 0.30 at 10 images per class to around 0.20 at 100 images per class. Plot f) shows accuracy versus 'images per class: ipc' for 'dd', 'k=16', 'k=32', 'k=64', and 'k=128'. For 'dd', accuracy peaks around 0.965 at 50 images per class. For 'k=16', accuracy peaks around 0.945 at 50 images per class. For 'k=32', accuracy peaks around 0.965 at 50 images per class. For 'k=64', accuracy peaks around 0.968 at 50 images per class. For 'k=128', accuracy peaks around 0.968 at 50 images per class. The overall title for the figure is 'Fig. 7. Dependence of student's performance and hyperparameters of distil'.

<span id="page-5-2"></span>Fig. 7. Dependence of student's performance and hyperparameters of distillation procedure. Next parameters used as default:  $ipc = 10$ ,  $ic = 1$ ,  $N =$  $10, \zeta_{\theta} = 10, k = 64.$ 

<span id="page-5-1"></span>TABLE II MEAN AND STANDARD DEVIATION OF TEST ACCURACY FOR DIFFERENT DISTILLATION ALGORITHMS.

| Method + Teacher | Accuracy       | Params    | GPU (MiB)      |
|------------------|----------------|-----------|----------------|
| $GM + DD$        | $95.6 \pm 0.1$ | 78.4 K    | $\approx 2390$ |
| $GM + DD$        | $86.9 \pm 1.5$ | 78.4 K    | $\approx 2370$ |
| (not per class)  |                |           |                |
| $GM + GTN-1rn$   | $95.2 \pm 0.1$ | 1.646 M   | $\approx 2454$ |
| $GM + GTN-1m$    | $93.4 + 0.3$   | $1.646$ M | $\approx 2434$ |
| (not per class)  |                |           |                |

hyperparameters values. For experiment in Table [III,](#page-5-3) we use  $k = 64, ipc = 50, K = 35, N = 10, \zeta_{\theta} = 10.$ 

<span id="page-5-3"></span>TABLE III MEAN AND STANDARD DEVIATION OF TEST ACCURACY FOR DIFFERENT DISTILLATION ALGORITHMS.

| Method + Teacher      | Accuracy       | Params  | GPU (MiB)      |
|-----------------------|----------------|---------|----------------|
| $GM + GTN-1m$         | $94.2 \pm 0.4$ | 172.2 K | $\approx 4192$ |
| $(k = 16, ipc = 100)$ |                |         |                |
| $GM + GTN-1m$         | $95.9 + 0.2$   | 449.7 K | $\approx$ 3610 |
| $(k = 32, K = 50)$    |                |         |                |
| $GM + GTN-1m$         | $96.4 + 0.1$   | 1.672 M | $\approx 3640$ |
| $(K = 50)$            |                |         |                |
| $GM + GTN-1m$         | $96.8 \pm 0.1$ | 6.533 M | $\approx 3770$ |
| $(k = 128, K = 50)$   |                |         |                |
| $GM + GTN-rnd$        | $29.0 + 6.1$   | 1.640 M | $\approx 2454$ |
| $(ipc = 10, K = 110)$ |                |         |                |

Tables [II](#page-5-1) and [III](#page-5-3) show the GPU memory usage. It seems that *ipc* has a greater impact on memory usage than *k*, which is

Image /page/5/Figure/8 description: This image contains four plots labeled a, b, c, and d. Plots a, b, and c are titled "learned input" and plot d is titled "random input". All plots show accuracy on the y-axis. Plot a shows accuracy versus images per class (ipc) for DD and GTN, with DD accuracy decreasing from approximately 0.912 at 10 images to 0.903 at 45 images, and GTN accuracy decreasing from approximately 0.925 at 10 images to 0.919 at 45 images. Plot b shows accuracy versus student's learning steps for DD and GTN. DD accuracy increases from approximately 0.76 at 1 step to 0.925 at 50 steps. GTN accuracy increases from approximately 0.76 at 1 step to 0.915 at 10 steps, then decreases to 0.75 at 50 steps. Plot c shows accuracy versus Neumann series elements: N for DD and GTN. DD accuracy decreases from approximately 0.915 at 5 elements to 0.900 at 30 elements. GTN accuracy remains relatively constant from approximately 0.925 at 5 elements to 0.922 at 30 elements. Plot d shows accuracy versus images per class (ipc) for DD. DD accuracy decreases from approximately 0.910 at 10 images to 0.893 at 30 images, and remains at 0.893 at 45 images.

<span id="page-5-4"></span>Fig. 8. relation of distillation method's hyperparameters and test performance. We use as default:  $ipc = 10, N = 10, \zeta_{\theta} = 10, k = 64$ .

another benefit of using GTN. Note that memory usage can be reduced by changing the *ic* value to optimize more synthetic images using smaller batches. Note that such change can slow down convergence.

<span id="page-5-0"></span>

### C. Distillation with implicit differentiation

This method was proposed in [\[2\]](#page-7-5), and we will abbreviate it as IFT (implicit function theorem). As mentioned above (see section [V\)](#page-2-0), there is no detailed description of the results in the original paper, so they can be found in this section. Fig. [8](#page-5-4) (a-c) shows the relationship between the hyperparameters of the distillation method and the student's performance on the test. We assume that these results can be explained by the fact that increasing the values of these hyperparameters decreases frequency of  $\lambda$  update, which negatively affects performance. The only exception is  $\zeta_{\theta}$ .

Fig. [8.](#page-5-4)d shows results for distillation using generator with random input (GTN-rnd). Such a generator can produce as much data as we need, but it can't converge when trained with gradient matching. It seems that such distillation becomes possible using implicit differentiation.

Table [IV](#page-6-3) shows the best results for each method. For this experiment, we use  $K = 1080, \zeta_{\theta} = 50, ipc = 10, N = 10$ as default hyperparameters values. The performance seems to be the same or even better compared to backpropagation through the training procedure unroll (see. Table [I\)](#page-4-5). Note the difference in memory usage in both tables. Note that the implicit differentiation distillation is inferior to the gradient matching distillation. We think this may be connected with the difference in frequency of  $\lambda$  update. To do one update using IFT, we first have to train the student, which is not needed in case of GM. It is also important to note that this method is very sensitive to  $\alpha$  and  $\zeta_{\theta}$ , and in some **DD** cases it starts to diverge after several iterations. Meanwhile the use of GTN makes the procedure more stable and allows for a more generalizable dataset (see Table [VI\)](#page-6-4).

Image /page/6/Figure/0 description: The image displays six grids of synthetic images, labeled a) through f). Each grid contains 100 smaller images arranged in a 10x10 matrix. Above each grid, the numbers 0 through 9 are displayed, indicating that each grid likely represents generated images for each digit. The synthetic images themselves appear to be variations of handwritten digits, with some grids showing clearer representations than others. The overall presentation suggests a comparison or demonstration of generated digits for a classification task.

<span id="page-6-6"></span>Fig. 9. Synthetic images for MNIST classification task obtained with different distillation methods: a) GM+DD, b) IFT+DD, c) GM+GTN-lrn, d) IFT+GTNlrn, e) GM+GTN-rnd, f) IFT+GTN-rnd. We use the same hyperparameters as mentioned in table [V.](#page-6-5) Hyperparameters for GM+GTN-rnd are described in caption of table [IV.](#page-6-3)

<span id="page-6-3"></span>TABLE IV MEAN AND STANDARD DEVIATION OF TEST ACCURACY FOR DIFFERENT DISTILLATION ALGORITHMS.

| Method + Teacher                      | Accuracy       | Params  | GPU (MiB)      |
|---------------------------------------|----------------|---------|----------------|
| IFT + DD $(K = 500)$                  | $93.5 pm 0.5$ | 78.4 K  | $approx 2726$ |
| IFT + GTN-lrn $(\zeta_{\theta} = 10)$ | $92.4 pm 0.2$ | 1.646 M | $approx 2726$ |
| IFT + GTN-rnd $(\zeta_{\theta} = 10)$ | $90.9 pm 0.3$ | 1.640 M | $approx 2726$ |

Fig. [9](#page-6-6) shows part of the final synthetic dataset for GM (see a, c, e) and IFT (see b, d, f). The greatest difference is obtained when data distilled without a generator (see a, b). Synthetic data obtained using implicit differentiation looks less realistic and therefore can be used for federative learning [\[20\]](#page-7-16). Also note that the images distilled using generator have more contrast.

<span id="page-6-0"></span>

### D. Distillation with augmentation

In previous works, augmentation has been used in different ways. In [\[4\]](#page-7-6) it takes place during distillation (let's call it train augmentation) by applying transformations to real images  $\mathcal{B}^{\mathcal{T}}$ . In [\[1\]](#page-7-0), [\[3\]](#page-7-1) it is used when teaching student on synthetic data (let's call it test augmentation). In our study, we decided to compare augmentation techniques. Table [V](#page-6-5) shows the test performance for various distillation and augmentation techniques. It seems that for the MNIST classification problem only test augmentation gives improvement (see tables [II,](#page-5-1) [III,](#page-5-3) [IV\)](#page-6-3). To augment images we use random crop and rotation. For this experiment, we use  $K = 1080, ipc = 10, \zeta_{\theta} = 10, N = 10$ as default hyperparameters values.

<span id="page-6-5"></span>TABLE V MEAN AND STANDARD DEVIATION OF TEST ACCURACY FOR DIFFERENT DISTILLATION ALGORITHMS AND DIFFERENT AUGMENTATIONS.

| Method $+$                     | Test Aug.                 | Train Aug.     | $Test +$                  |
|--------------------------------|---------------------------|----------------|---------------------------|
| Teacher                        |                           |                | Train Aug.                |
| GM+DD $(ic = 1,$               | $96.1 \pm 0.4$            | $94.8 \pm 0.1$ | $93.9 \pm 0.5$            |
| $K = 110$                      |                           |                |                           |
| GM+GTN-lrn                     | $97.4 \pm 0.1$            | $96.2 \pm 0.2$ | $95.5 \pm 0.4$            |
| $(k = 128, ipc = 50,$          |                           |                |                           |
| $K=50$                         |                           |                |                           |
| IFT+DD $(\zeta_{\theta} = 50,$ | $\overline{92.3} \pm 0.9$ | $91.4 \pm 0.5$ | $89.2 \pm 1.5$            |
| $K = 500$                      |                           |                |                           |
| IFT+GTN-lrn                    | $93.0 \pm 0.2$            | $91.4 \pm 0.3$ | $91.4 \pm 0.4$            |
| IFT+GTN-rnd                    | $92.2 \pm 0.3$            | $89.7 \pm 0.3$ | $\overline{90.9} \pm 0.6$ |

<span id="page-6-4"></span>

| TABLE VI                                                   |
|------------------------------------------------------------|
| MEAN AND STANDARD DEVIATION OF TEST ACCURACY FOR DIFFERENT |
| DISTILLATION ALGORITHMS AND STUDENT'S ARCHITECTURES.       |

| $Method +$<br>Teacher | LeNet          | AlexNet         | VGG11          | MI P      |
|-----------------------|----------------|-----------------|----------------|-----------|
| $GM+DD$               | $94.1 \pm 0.6$ | $95.0 \pm 0.2$  | $95.8 \pm 0.3$ | 88.6      |
|                       |                |                 |                | $\pm 0.4$ |
| $GM+$                 | $95.5 \pm 0.3$ | $96.7 + 0.2$    | $97.4 \pm 0.1$ | 86.8      |
| GTN-lrn               |                |                 |                | $\pm 0.3$ |
| IFT+DD                | $74.0 \pm 7.8$ | $68.6 \pm 8.9$  | $86.5 \pm 1.6$ | 50.9      |
|                       |                |                 |                | $\pm 8.3$ |
| $IFT+$                | $91.5 \pm 1.0$ | $82.5 \pm 14.9$ | $93.0 \pm 0.4$ | 79.9      |
| GTN-lrn               |                |                 |                | $\pm 0.6$ |
| $IFT+$                | $88.3 \pm 2.3$ | $85.3 \pm 3.9$  | $92.1 + 0.4$   | 74.4      |
| GTN-rnd               |                |                 |                | $\pm 1.1$ |

<span id="page-6-1"></span>

### E. Generalizability

The generalization problem of distilled data was first mentioned in [\[1\]](#page-7-0) and then studied in [\[4\]](#page-7-6) and [\[7\]](#page-7-4). The problem is that such data can't guarantee convergence for students which didn't participate in the distillation procedure. And this problem is of great importance, since the main practical use of synthetic data is NAS. For this experiment, we use  $K = 1080, ipc = 10, \zeta_{\theta} = 10, N = 10$  as default hyperparameters values.

Table [VI](#page-6-4) shows the results of students with different architectures trained on data distilled with different methods. For distillation we used ConvNet student's architecture, all results were obtained with test augmentation. It seems that the best generalizability can be obtained using GTN and GM use. For a comparison with ConvNet see the first column of Table [V.](#page-6-5)

## IX. CONCLUSION

<span id="page-6-2"></span>This work explores all the latest ideas in dataset distillation field suggested in [\[1\]](#page-7-0), [\[2\]](#page-7-5), [\[3\]](#page-7-1), [\[4\]](#page-7-6). We honestly compared the performance of all known methods, limiting their running time. We also proposed new methods based on the joint use of generators and memory efficient methods. Experiments with the MNIST benchmark show that selecting the correct size for the generator allows to achieve better performance for gradient matching distillation, and improves the generalizability of implicit differentiation distillation. This paper also presents the results of augmentation impact on distillation. We also provide a detailed description of the experimental results for implicit differentiation distillation, as we didn't find them in

the original work [\[2\]](#page-7-5). As future work, we want to experiment with much more diverse datasets and architectures. We also want to improve the distilled data generalizing ability using stochastic depth networks [\[18\]](#page-7-17). We are also interested in experiments with bringing the distribution of synthetic objects closer to the original one.

## ACKNOWLEDGMENT

This research was performed at the Center for Big Data Storage and Analysis of Lomonosov Moscow State University and was supported by the National Technology Initiative Foundation (13/1251/2018 of December 11, 2018).

## REFERENCES

- <span id="page-7-0"></span>[1] Wang T., Zhu J., Torralba A., Efros A. A.: Dataset Distillation. CoRR; abs/1811.10959 (2018)
- <span id="page-7-5"></span>[2] Lorraine J., Vicol P., Duvenaud D. Optimizing Millions of Hyperparameters by Implicit Differentiation. CoRR; abs/1911.02590 (2019)
- <span id="page-7-1"></span>[3] Zhao B., Mopuri K. R., Bilen H. Dataset Condensation with Gradient Matching. CoRR; abs/2006.05929 (2020)
- <span id="page-7-6"></span>[4] Such F. P., Rawal A., Lehman J., Stanley K. O., Clune J. Generative Teaching Networks: Accelerating Neural Architecture Search by Learning to Generate Synthetic Training Data. CoRR; abs/1912.07768 (2019)
- <span id="page-7-2"></span>[5] Maclaurin D., Duvenaud D. and Adams R.: Gradient-Based Hyperparameter Optimization Through Reversible Learning. CoRR; abs/1502.03492 (2015)
- <span id="page-7-3"></span>[6] Sucholutsky I., Schonlau M.: Soft-Label Dataset Distillation and Text Dataset Distillation. CoRR; abs/1910.02551 (2019)
- <span id="page-7-4"></span>[7] Medvedev D., D'yakonov A. New Properties of the Data Distillation Method When Working with Tabular Data. In: van der Aalst W.M.P. et al. (eds) Analysis of Images, Social Networks and Texts. AIST 2020. Lecture Notes in Computer Science, vol 12602. Springer, Cham. CoRR; abs/2010.09839 (2021)
- <span id="page-7-7"></span>[8] LeCun Y., Boser B., Denker J. S., Henderson D., Howard R. E., Hubbard W., and Jackel L. D.: Backpropagation Applied to Handwritten Zip Code RecognitionNeural Computation. Neural Computation 1(4), 541– 551 (1989)
- <span id="page-7-8"></span>[9] Bengio Y.: Gradient-Based Optimization of Hyperparameters. Neural Computation 12(8), 1889–1900 (2000)
- <span id="page-7-9"></span>[10] Baydin A., Pearlmutter B.: Automatic Differentiation of Algorithms for Machine Learning. In: Proceedings of the AutoML Workshop at the International Conference on Machine Learning (ICML). Beijing, China, June 21–26 (2014)
- <span id="page-7-10"></span>[11] Liu D. C., Nocedal J.: On the Limited Memory BFGS Method for Large Scale Optimization. Mathematical Programming 45, 503–528 (1989)
- <span id="page-7-11"></span>[12] Polyak B.: Some Methods of Speeding Up the Convergence of Iteration Methods. USSR Computational Mathematics and Mathematical Physics, vol. 4, pp. 1–17 (1964)
- [13] Domke J.: Generic Methods for Optimization-Based Modeling. In: Proceedings of the Fifteenth International Conference on Artificial Intelligence and Statistics, pp. 318–326. PMLR (2012)
- <span id="page-7-14"></span>[14] MNIST Handwritten Digit Database, [http://yann.lecun.com/exdb/mnist/.](http://yann.lecun.com/exdb/mnist/) Last accessed 17 April 2021.
- [15] Lecun Y., Bottou L., Bengio Y., Haffner P.: Gradient-Based Learning Applied to Document Recognition. In: Proceedings of the IEEE, vol. 86, pp. 2278–2324 (1998)
- <span id="page-7-12"></span>[16] Hinton G., Vinyals O., Dean J.: Distilling the Knowledge in a Neural Network. In: NIPS Deep Learning and Representation Learning Workshop. (2015)
- <span id="page-7-13"></span>[17] Grefenstette E., Amos B., Yarats D., Htut P. M., Molchanov A., Meier F., Kiela D., Cho K., Chintala S. Generalized Inner Loop Meta-Learning. CoRR; abs/1910.01727 (2019)
- <span id="page-7-17"></span>[18] Huang G., Sun Y., Liu Z., Sedra D. and Weinberger K.: Deep Networks With Stochastic Depth. CoRR; abs/1603.09382 (2016)
- <span id="page-7-15"></span>[19] Gidaris S., Komodakis N. Dynamic Few-Shot Visual Learning without Forgetting. CoRR; abs/1804.09458 (2018)
- <span id="page-7-16"></span>[20] Zhou Y., Pu G., Ma X., Li X., Wu D. Distilled One-Shot Federated Learning. CoRR; abs/2009.07999 (2020)
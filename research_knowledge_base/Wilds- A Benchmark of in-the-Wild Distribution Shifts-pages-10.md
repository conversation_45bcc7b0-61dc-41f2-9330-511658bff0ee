## E.9 Amazon-wilds

In many consumer-facing ML applications, models are trained on data collected on one set of users and then deployed across a wide range of potentially new users. These models can perform well on average but poorly on some individuals (<PERSON><PERSON>, 2017; <PERSON><PERSON> et al., 2018; <PERSON> et al., 2019b; <PERSON><PERSON> et al., 2020). These large performance disparities across users are practical concerns in consumer-facing applications, and they can also indicate that models are exploiting biases or spurious correlations in the data (<PERSON><PERSON><PERSON> et al., 2019; <PERSON><PERSON> et al., 2019). We study this issue of inter-individual performance disparities on a variant of the AMAZON-WILDS Reviews dataset (<PERSON> et al., 2019).

### E.9.1 SETUP

Problem setting. We consider a hybrid domain generalization and subpopulation problem where the domains correspond to different reviewers. The task is multi-class sentiment classification, where the input x is the text of a review, the label y is a corresponding star rating from 1 to 5, and the domain  $d$  is the identifier of the reviewer who wrote the review. Our goal is to perform consistently well across a wide range of reviewers, i.e., to achieve high tail performance on different subpopulations of reviewers in addition to high average performance. In addition, we consider disjoint set of reviewers between training and test time.

Data. The dataset comprises 539,502 customer reviews on Amazon taken from the Amazon Reviews dataset (<PERSON> et al., 2019). Each input example has a maximum token length of 512. For each example, the following additional metadata is also available at both training and evaluation time: reviewer ID, product ID, product category, review time, and summary.

To reliably measure model performance on each reviewer, we include at least 75 reviews per reviewer in each split. Concretely, we consider the following splits, where reviewers are randomly assigned to either in-distribution or out-of-distribution sets:

- 1. Training: 245,502 reviews from 1,252 reviewers.
- 2. Validation (OOD): 100,050 reviews from another set of 1,334 reviewers, distinct from training and test (OOD).
- 3. Test (OOD): 100,050 reviews from another set of 1,334 reviewers, distinct from training and validation (OOD).
- 4. Validation (ID): 46,950 reviews from 626 of the 1,252 reviewers in the training set.
- 5. Test (ID): 46,950 reviews from 626 of the 1,252 reviewers in the training set.

The reviewers in the train and in-distribution splits; the validation (OOD) split; and the test (OOD) split are all disjoint, which allows us to test generalization to unseen reviewers. See Appendix [E.9.4](#page-3-0) for more details.

Evaluation. To assess whether models perform consistently well across reviewers, we evaluate models by their accuracy on the reviewer at the 10th percentile. This follows the federated learning literature, where it is standard to measure model performance on devices and users at various percentiles in an effort to encourage good performance across many devices (Caldas et al., 2018; Li et al., 2019b).

Potential leverage. We include more than a thousand reviewers in the training set, capturing variation across a wide range of reviewers. In addition, we provide reviewer ID annotations for all reviews in the dataset. These annotations could be used to directly mitigate performance disparities across reviewers seen during training time.

<span id="page-1-1"></span>Table 23: Baseline results on Amazon-wilds. We report the accuracy of models trained using ERM, CORAL, IRM, and group DRO, as well as a reweighting baseline that reweights for class balance. To measure tail performance across reviewers, we report the accuracy for the reviewer in the 10th percentile. While the performance drop on Amazon-wilds is primarily from subpopulation shift, there is also a performance drop from evaluating on unseen reviewers, as evident in the gaps in accuracies between the in-distribution and the out-of-distribution sets.

<span id="page-1-0"></span>

| Algorithm          | Validation (ID) |            | Validation (OOD) |            | Test (ID)       |            | Test (OOD)      |            |
|--------------------|-----------------|------------|------------------|------------|-----------------|------------|-----------------|------------|
|                    | 10th percentile | Average    | 10th percentile  | Average    | 10th percentile | Average    | 10th percentile | Average    |
| ERM                | 58.7 (0.0)      | 75.7 (0.2) | 55.2 (0.7)       | 72.7 (0.1) | 57.3 (0.0)      | 74.7 (0.1) | 53.8 (0.8)      | 71.9 (0.1) |
| CORAL              | 56.2 (1.7)      | 74.4 (0.3) | 54.7 (0.0)       | 72.0 (0.3) | 55.1 (0.4)      | 73.4 (0.2) | 52.9 (0.8)      | 71.1 (0.3) |
| <b>IRM</b>         | 56.4 (0.8)      | 74.3 (0.1) | 54.2 (0.8)       | 71.5 (0.3) | 54.7 (0.0)      | 72.9 (0.2) | 52.4 (0.8)      | 70.5 (0.3) |
| Group DRO          | 57.8 (0.8)      | 73.7 (0.6) | 54.7 (0.0)       | 70.7 (0.6) | 55.8 (1.0)      | 72.5 (0.3) | 53.3 (0.0)      | 70.0 (0.5) |
| Reweighted (label) | 55.1 (0.8)      | 71.9 (0.4) | 52.1 (0.2)       | 69.1 (0.5) | 54.4 (0.4)      | 70.7 (0.4) | 52.0 (0.0)      | 68.6 (0.6) |

Image /page/1/Figure/2 description: This is a histogram showing the distribution of per-reviewer test accuracies for two methods: Random baseline and ERM. The x-axis represents the per-reviewer test accuracy, ranging from 0.0 to 1.0. The y-axis represents the density. The Random baseline (shown in gray) has a distribution peaking around 0.7, with a long tail towards lower accuracies. The ERM method (shown in blue) has a distribution that is shifted towards higher accuracies, with a peak around 0.75 and a broader spread compared to the random baseline. Both distributions show a general trend of increasing accuracy from left to right, with a significant number of reviewers achieving accuracies above 0.6.

Distribution of per-reviewer test accuracies

Figure 27: Distribution of per-reviewer accuracy on the test set for the ERM model (blue). The corresponding random baseline would have per-reviewer accuracy distribution in grey.

#### E.9.2 Baseline results

Model. For all experiments, we finetuned DistilBERT-base-uncased models (Sanh et al., 2019), using the implementation from Wolf et al. (2019), and with the following hyperparameter settings: batch size 8; learning rate  $1 \times 10^{-5}$  with the AdamW optimizer (Loshchilov and Hutter, 2019); L<sub>2</sub>-regularization strength 0.01; 3 epochs with early stopping; and a maximum number of tokens of 512. We selected the above hyperparameters based on a grid search over learning rates  $\{1 \times 10^{-6}, 2 \times 10^{-6}, 1 \times 10^{-5}, 2 \times 10^{-5}\},$ and all other hyperparameters were simply set to standard/default values.

ERM results and performance drops. A DistilBERT-base-uncased model trained with the standard ERM objective performs well on average, but performance varies widely across reviewers (Figure [27,](#page-1-0) Table [23\)](#page-1-1). Despite the high average accuracy of 71.9%, per-reviewer accuracies vary widely between  $100.0\%$  and  $12.0\%$ , with accuracy at the 10th percentile of 53.8%. The above variation is larger than expected from randomness: a random binomial baseline with equal average accuracy would have a 10th percentile accuracy of 65.4%. We observe low tail performance on both previously seen and unseen reviewers, with low 10th percentile accuracy on in-distribution and out-of-distribution sets (Table [23\)](#page-1-1). In addition, we observe drops on both average and 10th percentile accuracies upon evaluating on unseen reviewers, as evident in the performance gaps between the in-distribution and the out-of-distribution sets.

As with CivilComments-wilds, the relatively small number of reviews per reviewer makes it difficult to run a test-to-test comparison (e.g., training a model on just the reviewers in the bottom 10th percentile). Without running the test-to-test comparison, it is possible that the gap between average and 10th percentile accuracies can be explained at least in part by differences in the intrinsic

Table 24: Dataset details for AMAZON-WILDS.

<span id="page-2-0"></span>

| Split            | # Reviews | # Reviewers | # Reviews per reviewer (mean / minimum) |
|------------------|-----------|-------------|-----------------------------------------|
| Training         | 245,502   | 1,252       | 196 / 75                                |
| Validation (OOD) | 100,050   | 1,334       | 75 / 75                                 |
| Test (OOD)       | 46,950    | 662         | 75 / 75                                 |
| Validation (ID)  | 100,050   | 1,334       | 75 / 75                                 |
| Test (ID)        | 46,950    | 662         | 75 / 75                                 |

difficulty of reviews from different reviewers, e.g., some reviewers might not write text reviews that are informative of their star rating. Future work will be required to establish in-distribution accuracies that account for these differences.

Additional baseline methods. We now consider models trained by existing robust training algorithms and show that these models also perform poorly on tail reviewers, failing to mitigate the performance drop (Table [23\)](#page-1-1). We observe that reweighting to achieve uniform class balance fails to improve the 10th percentile accuracy, showing that variation across users cannot be solved simply by accounting for label imbalance. In addition, CORAL, IRM, and Group DRO fail to improve both average and 10th percentile accuracies on both ID and OOD sets. Our grid search selected  $\lambda = 1.0$ for the CORAL penalty and  $\lambda = 1.0$  for the IRM penalty.

Discussion. The distribution shift and the evaluation criteria for AMAZON-WILDS focus on the tail performance, unlike the other datasets in WILDS. Because of this, AMAZON-WILDS might have distinct empirical trends or be conducive to different algorithms compared to other datasets. Potential approaches include extensions to algorithms for worst-group performance, for example to handle a large number of groups, as well as adaptive approaches that yield user-specific predictions.

## E.9.3 Broader context

Performance disparities across individuals have been observed in a wide range of tasks and applications, including in natural language processing (Geva et al., 2019), automatic speech recognition (Koenecke et al., 2020; Tatman, 2017), federated learning (Li et al., 2019b; Caldas et al., 2018), and medical imaging (Badgeley et al., 2019). These performance gaps are practical limitations in applications that call for good performance across a wide range of users, including many user-facing applications such as speech recognition (Koenecke et al., 2020; Tatman, 2017) and personalized recommender systems (Patro et al., 2020), tools used for analysis of individuals such as sentiment classification in computational social science (West et al., 2014) and user analytics (Lau et al., 2014), and applications in federated learning. These performance disparities have also been studied in the context of algorithmic fairness, including in the federated learning literature, in which uniform performance across individuals is cast as a goal toward fairness (Li et al., 2019b; Dwork et al., 2012). Lastly, these performance disparities can also highlight models' failures to learn the actual task in a generalizable manner; instead, some models have been shown learn the biases specific to individuals. Prior work has shown that individuals—technicians for medical imaging in this case—can not only be identified from data, but also are predictive of the diagnosis, highlighting the risk of learning to classify technicians rather than the medical condition (Badgeley et al., 2019). More directly, across a few natural language processing tasks where examples are annotated by crowdworkers, models have been observed to perform well on annotators that are commonly seen at training time, but fail to generalize to unseen annotators, suggesting that models are merely learning annotator-specific patterns and not the task (Geva et al., 2019).

<span id="page-3-0"></span>

## E.9.4 Additional details

Data processing. We consider a modified version of the Amazon reviews dataset (Ni et al., 2019). We consider disjoint reviewers between the training, OOD validation, and OOD test sets, and we also provide separate ID validation and test sets that include reviewers seen during training for additional reporting. These reviewers are selected uniformly at random from the reviewer pool, with the constraint that they have at least 150 reviews in the pre-processed dataset. Statistics for each split are described in Table [24.](#page-2-0) Notably, each reviewer has at least 75 reviews in the training set and exactly 75 reviews in the validation and test sets.

To process the data, we first eliminate reviews that are longer than 512 tokens, reviews without any text, and any duplicate reviews with identical star rating, reviewer ID, product ID, and time. We then obtain the 30-core subset of the reviews, which contains the maximal set of reviewers and products such that each reviewer and product has at least 30 reviews; this is a standard preprocessing procedure used in the original dataset (Ni et al., 2019). To construct the dataset for reviewer shifts in particular, we further eliminate the following reviews: (i) reviews that contain HTML, (ii) reviews with identical text within a user in order to ensure sufficiently high effective sample size per reviewer, and (iii) reviews with identical text across users to eliminate generic reviews. Once we have the filtered set of reviews, we consider reviewers with at least 150 reviews and sample uniformly at random until the training set contains approximately 250,000 reviews and each evaluation set contains at least 100,000 reviews. As we construct the training set, we reserve a random sample of 75 reviews for each user for evaluation and put all other reviews in the training set. For the evaluation set, we put a random sample of 75 reviews for each user.

Modifications to the original dataset. The original dataset does not prescribe a specific task or split. We consider a standard task of sentiment classification, but instead of using a standard i.i.d. split, we instead consider disjoint users between training and evaluation time as described above. In addition, we preprocess the data as detailed above.

### E.10 Py150-WILDS

Code completion models—autocomplete tools used by programmers to suggest subsequent source code tokens, such as the names of API calls—are commonly used to reduce the effort of software development (Robbes and Lanza, 2008; Bruch et al., 2009; Nguyen and Nguyen, 2015; Proksch et al., 2015; Franks et al., 2015). These models are typically trained on data collected from existing codebases but then deployed more generally across other codebases, which may have different distributions of API usages (Nita and Notkin, 2010; Proksch et al., 2016; Allamanis and Brockschmidt, 2017). This shift across codebases can cause substantial performance drops in code completion models. Moreover, prior studies of real-world usage of code completion models have noted that these models can generalize poorly on some important subpopulations of tokens such as method names (Hellendoorn et al., 2019).

We study this problem using a variant of the Py150 Dataset, originally developed by Raychev et al. (2016) and adapted to a code completion task by Lu et al. (2021).

#### E.10.1 SETUP

Problem setting. We consider a hybrid domain generalization and subpopulation shift problem, where the domains are codebases (GitHub repositories), and our goal is to learn code completion models that generalize to source code written in new codebases. Concretely, the input  $x$  is a sequence of source code tokens taken from a single file, the label y is the next token (e.g., "environ", "communicate" in Figure 12), and the domain d is an integer that identifies the repository that the source code belongs to. We aim to solve both a domain generalization problem across codebases and improve subpopulation performance on class and methods tokens.

Table 25: Token statistics for Py150-WILDS.

<span id="page-4-0"></span>

| Split            | #Files | #Total tokens | #Class  | #Method | #Punctuator | #Keyword  | #Literal  |
|------------------|--------|---------------|---------|---------|-------------|-----------|-----------|
| Training         | 79,866 | 14,129,619    | 894,753 | 789,456 | 4,512,143   | 1,246,624 | 1,649,653 |
| Validation (ID)  | 5,000  | 882,745       | 55,645  | 48,866  | 282,568     | 77,230    | 105,456   |
| Test (ID)        | 20,000 | 3,539,524     | 222,822 | 194,293 | 1,130,607   | 313,008   | 420,232   |
| Validation (OOD) | 5,160  | 986,638       | 65,237  | 56,756  | 310,914     | 84,677    | 111,282   |
| Test (OOD)       | 39,974 | 7,340,433     | 444,713 | 412,700 | 2,388,151   | 640,939   | 869,083   |

Data. The dataset comprises 150,000 Python source code files from 8,421 different repositories on GitHub ([github.com](https://github.com/)). Each source code file is associated with the repository ID so that code from the same repository can be linked.

We split the dataset by randomly partitioning the data by repositories:

- 1. Training: 79,866 code files from 5,477 repositories.
- 2. Validation (OOD): 5,160 code files from different 261 repositories.
- 3. Test (OOD): 39,974 code files from different 2,471 repositories.
- 4. Validation (ID): 5,000 code files from the same repositories as the training set (but different files).
- 5. Test (ID): 20,000 code files from the same repositories as the training set (but different files).

The repositories are randomly distributed across the training, validation (OOD), and test (OOD) sets. As we use models pre-trained on the CodeSearchNet dataset (Husain et al., 2019), which partially overlaps with the Py150 dataset, we ensured that all GitHub repositories used in CodeSearchNet only appear in the training set in Py150-wilds and not in the validation/test sets.

Table [25](#page-4-0) shows the token statistics of the source code files, as well as the token type breakdown (e.g., class, method, punctuator, keyword, literal). The tokens are defined by the built-in Python tokenizer and the CodeGPT tokenizer, following Lu et al. (2021). Training and evaluation are conducted at the token-level (more details are provided below).

Evaluation. We evaluate models by their accuracy on predicting class and method tokens in the test set code files. This subpopulation metric is inspired by Hellendoorn et al. (2019), which finds that in real-world settings, developers primarily use code completion tools for completing class names and method names; in contrast, measuring average token accuracy would prioritize common tokens such as punctuators, which are often not a problem in real-world settings.

Potential leverage. We provide the GitHub repository that each source code files was derived from, which training algorithms can leverage. As programming tools like code completion are expected to be used across codebases in real applications (Nita and Notkin, 2010; Allamanis and Brockschmidt, 2017), it is important for models to learn generalizable representations of code and extrapolate well on unseen codebases. We hope that approaches using the provided repository annotations can learn to factor out common features and codebase-specific features, resulting in more robust models.

Additionally, besides the (integer) IDs of repositories, we also provide the repository names and file names in natural language as extra metadata. While we only use the repository IDs in our baseline experiments described below, the extra natural language annotations can potentially be leveraged as well to adapt models to target repositories/files.

##### E.10.2 Baseline results

Model. For all experiments, we use the CodeGPT model (Lu et al., 2021) pre-trained on Code-SearchNet (Husain et al., 2019) as our model and finetune it on P $Y150$ -WILDS, using all the tokens

<span id="page-5-0"></span>Table 26: Baseline results on Py150-WILDS. We report both the model's accuracy on predicting class and method tokens and accuracy on all tokens trained using ERM, CORAL, IRM and group DRO. Standard deviations over 3 trials are in parentheses.

|           | Validation (ID) |              | Validation (OOD) |              | Test (ID)         |                   | Test (OOD)        |                   |
|-----------|-----------------|--------------|------------------|--------------|-------------------|-------------------|-------------------|-------------------|
|           | Algorithm       | Method/class | All              | Method/class | All               | Method/class      | All               | Method/class      |
| ERM       | 75.5 (0.5)      | 74.6 (0.4)   | 68.0 (0.1)       | 69.4 (0.1)   | <b>75.4 (0.4)</b> | <b>74.5 (0.4)</b> | <b>67.9 (0.1)</b> | <b>69.6 (0.1)</b> |
| CORAL     | 70.7 (0.0)      | 70.9 (0.1)   | 65.7 (0.2)       | 67.2 (0.1)   | 70.6 (0.0)        | 70.8 (0.1)        | 65.9 (0.1)        | 67.9 (0.0)        |
| IRM       | 67.3 (1.1)      | 68.4 (0.7)   | 63.9 (0.3)       | 65.6 (0.1)   | 67.3 (1.1)        | 68.3 (0.7)        | 64.3 (0.2)        | 66.4 (0.1)        |
| Group DRO | 70.8 (0.0)      | 71.2 (0.1)   | 65.4 (0.0)       | 67.3 (0.0)   | 70.8 (0.0)        | 71.0 (0.0)        | 65.9 (0.1)        | 67.9 (0.0)        |

in the training set. We tokenize input source code by the CodeGPT tokenizer and take blocks of length 256 tokens. We then train the CodeGPT model with a batch size of 6 (with  $6 \times 256 = 1,536$ ) tokens), a learning rate of  $8 \times 10^{-5}$ , no  $L_2$  regularization, and the AdamW optimizer (Loshchilov and Hutter, 2019) for 3 epochs with early stopping. Using the hyperparameters from Lu et al. (2021) as a starting point, we selected the above hyperparameters by a grid search over learning rates  ${8 \times 10^{-4}, 8 \times 10^{-5}, 8 \times 10^{-6}}$  and  $L_2$  regularization strength  ${0, 0.01, 0.1}$ . All other hyperparameters were simply set to standard/default values.

ERM results and performance drops. Table [26](#page-5-0) shows that model performance on class and method tokens dropped substantially from 75.4% on the train-to-train in-distribution repositories in the Test (ID) set to 67.9% on the out-of-distribution repositories in the Test (OOD) set. This gap shrinks if we evaluate the model on all tokens (instead of class and method tokens): accuracy drops from 74.5% on Test (ID) to 69.6% on Test (OOD). This is because the evaluation across all tokens includes many tokens that are used universally across repositories, such as punctuators and keywords.

We only ran a train-to-train comparison because there are a relatively large number of domains (repositories) split i.i.d. between the training and test sets, which suggests that the training and test sets should be "equally difficult". We therefore do not expect test-to-test and mixed-to-test comparisons to yield significantly different results.

Additional baseline methods. We trained CORAL, IRM, and Group DRO baselines, treating each repository as a domain. For CORAL and IRM, we find that the smaller penalties give slightly better generalization performance ( $\lambda = 1$  for CORAL and  $\lambda = 1$  for IRM). Compared to the ERM baseline, while CORAL and IRM reduced the performance gap between ID and OOD, neither of them improved upon ERM on the final OOD performance.

### E.10.3 Broader context

Machine learning can aid programming and software engineering in various ways: automatic code completion (Raychev et al., 2014; Svyatkovskiy et al., 2019), program synthesis (Bunel et al., 2018; Kulal et al., 2019), program repair (Vasic et al., 2019; Yasunaga and Liang, 2020), code search (Husain et al., 2019), and code summarization (Allamanis et al., 2015). However, these systems face several forms of distribution shifts when deployed in practice. One major challenge is the shifts across codebases (which our Py150-wilds dataset focuses on), where systems need to adapt to factors such as project content, coding conventions, or library or API usage in each codebase (Nita and Notkin, 2010; Allamanis and Brockschmidt, 2017). A second source of shifts is programming languages, which includes adaptation across different domain-specific languages (DSLs), e.g., in robotic environments (Shin et al., 2019); and across different versions of languages, e.g., Python 2 and 3 (Malloy and Power, 2017). Another challenge is the shift from synthetic training sets to real usage: for instance, Hellendoorn et al. (2019) show that existing code completion systems, which are typically trained as language models on source code, perform poorly on the real completion instances that are most commonly used by developers in IDEs, such as API calls (class and method calls).

E.10.4 Additional details

Data split. We generate the splits in the following steps. First, to avoid test set contamination, we took all of the repositories in CodeSearchNet (which, as a reminder, is used to pretrain our baseline model) and assigned them to the training set. Second, we randomly split all of the remaining repositories into three groups: Validation (OOD), Test (OOD), and Others. Finally, to generate the ID splits, we randomly split the files in the Others repositories into three sets: Training, Validation (ID), and Test (ID).

**Modifications to the original dataset.** The original Py150 dataset (Raychev et al., 2016) splits the total 150k files into 100k training files and 50k test files, regardless of the repository that each file was from. In Py150-WILDS, we re-split the dataset based on repositories to construct the aforementioned train, validation (ID), validation (OOD), test (ID), and test (OOD) sets.

Additionally, in the Py150 code completion task introduced in Lu et al. (2021), models are evaluated by the accuracy of predicting every token in source code. However, according to developer studies, this evaluation may include various tokens that are rarely used in real code completion, such as punctuators, strings, numerals, etc. (Robbes and Lanza, 2008; Proksch et al., 2016; Hellendoorn et al., 2019). To define a task closer to real applications, in Py150-wilds we focus on class name and method name prediction (which are used most commonly by developers).

## Appendix F. Datasets with distribution shifts that do not cause performance drops

### F.1 SQF: Criminal possession of weapons across race and locations

In this section, we provide more details on the stop-and-frisk dataset discussed in Section 8.1. The original data was provided by the New York City Police Department, and has been widely used in previous ML and data analysis work (Goel et al., 2016; Zafar et al., 2017; Pierson et al., 2018; Kallus and Zhou, 2018; Srivastava et al., 2020). For our analysis, we use the version of the dataset that was processed by Goel et al. (2016). Our problem setting and dataset structure closely follow theirs.

#### F.1.1 SETUP

Problem setting. We study a subpopulation shift in a weapons prediction task, where each data point corresponds to a pedestrian who was stopped by the police on suspicion of criminal possession of a weapon. The input  $x$  is a vector that represents 29 observable features from the UF-250 stop-and-frisk form filled out by the officer after each stop: e.g., whether the stop was initiated based on a radio run or at an officer's discretion, whether the officer was uniformed, and any reasons the officer gave for the stop (encoded as a categorical variable). Importantly, these features can all be observed by the officer prior to making the stop.<sup>9</sup> The binary label y is whether the pedestrian in fact possessed a weapon (i.e., whether the stop fulfilled its stated purpose). We consider, separately, two types of domains d: 1) race groups and 2) locations (boroughs in New York City). We consider location and race as our domains because previous work has shown that they can produce substantial disparities in policing practices and in algorithmic performance (Goel et al., 2016).

Data. Each row of the dataset represents one stop of one pedestrian. Following Goel et al. (2016), we filter for the 621,696 stops where the reason for the stop is suspicion of criminal possession of a weapon. We then filter for rows with complete data for observable features; with stopped pedestrians who are Black, white, or Hispanic; and who are stopped during the years 2009-2012 (the time range

<sup>9.</sup> When we consider subpopulation shifts over race groups, the input  $x$  additionally includes 75 one-hot indicators corresponding to the precinct that the stop was made in. We do not include those features when we consider shifts over locations, as they prevent the model from generalizing to new locations.

used in Goel et al. (2016)). These filters yield a total of 506,283 stops, 3.5% of which are positive examples (in which the officer finds that the pedestrian is illegally possessing a weapon).

The training versus validation split is a random 80%-20% partition of all stops in 2009 and 2010. We test on stops from 2011-2012; this follows the experimental setup in Goel et al. (2016). Overall, our data splits are as follows:

- 1. Training: 241,964 stops from 2009 and 2010.
- 2. Validation: 60,492 stops from 2009 and 2010, disjoint from the training set.
- 3. Test: 289,863 stops from 2011 and 2012.

In the experiments below, we do not use the entire training set, as we observed in our initial experiments that the model performed less well on certain subgroups (Black pedestrians and pedestrians from the Bronx). To determine whether this inferior performance might be ameliorated by training specifically on those groups, we controlled for training set size by downsampling the training set to the size of the disadvantaged population of interest for a given split. Specifically, we consider the following (overlapping) training subsets, each of which is subsampled from the overall training set described above:

- 1. Black pedestrians only: 155,929 stops of Black pedestrians from 2009 and 2010.
- 2. All pedestrians, subsampled to  $#$  Black pedestrians: 155,929 stops of all pedestrians from 2009 and 2010.
- 3. Bronx pedestrians only: 69,129 stops of pedestrians in the Bronx from 2009 and 2010.
- 4. All pedestrians, subsampled to  $#$  Bronx pedestrians: 69,129 stops of all pedestrians from 2009 and 2010.

These amount to running a test-to-test comparison for the subpopulations of Black pedestrians and Bronx pedestrians.

Evaluation. Our metric for classifier performance is the precision for each race group and each borough at a global recall of  $60\%$ —i.e., when using a threshold which recovers  $60\%$  of all weapons in the test data, similar to the recall evaluated in Goel et al. (2016). The results are similar when using different recall thresholds. Examining the precision for each race/borough captures the fact, discussed in Goel et al. (2016), that very low-precision stops may violate the Fourth Amendment, which requires *reasonable suspicion* for conducting a police stop; thus, the metric encapsulates the intuition that the police are attempting to avoid Fourth Amendment violations for any race group or borough while still recovering a substantial fraction of the illegal weapons.

##### F.1.2 Baseline results

Model. For all experiments, we use a logistic regression model trained with the Adam optimizer (Kingma and Ba, 2015) and early stopping. We trained one model on each of the 4 training sets, separately picking hyperparameters through a grid search across 7 learning rates logarithmicallyspaced in  $[5 \times 10^{-8}, 5 \times 10^{-2}]$  and batch sizes in  $\{4, 8, 16, 32, 64\}$ . Table [29](#page-8-0) provides the hyperparameters used for each training set. All models were trained with a reweighted cross-entropy objective that upsampled the positive examples to achieve class balance.

ERM results and performance drops. Performance differed substantially across race and location groups: precision was lowest on Black pedestrians (Table [27,](#page-8-1) top row) and pedestrians in the Bronx (Table [28,](#page-8-2) top row). To assess whether in-distribution training would improve performance on these groups, we trained the model only on Black pedestrians (Table [27,](#page-8-1) bottom row) and pedestrians in the Bronx (Table [28,](#page-8-2) bottom row). However, this did not substantially improve performance on

| Training dataset                                     | Precision at 60% recall |          |       |
|------------------------------------------------------|-------------------------|----------|-------|
|                                                      | Black                   | Hispanic | White |
| Black pedestrians only                               | 0.131                   | 0.174    | 0.360 |
| All pedestrians, subsampled to $#$ Black pedestrians | 0.135                   | 0.183    | 0.362 |

<span id="page-8-1"></span>Table 27: Comparison of precision scores for each race group at 60% global weapon recall. Train set size is 69,129 for both rows.

<span id="page-8-2"></span>Table 28: Comparison of precision scores for each borough at a threshold which achieves 60% global weapon recall. Train set size is 155,929 for both rows.

| Training dataset                                     | Precision at 60% recall |          |           |        |               |
|------------------------------------------------------|-------------------------|----------|-----------|--------|---------------|
|                                                      | Bronx                   | Brooklyn | Manhattan | Queens | Staten Island |
| Bronx pedestrians only                               | 0.074                   | 0.158    | 0.207     | 0.157  | 0.105         |
| All pedestrians, subsampled to $#$ Bronx pedestrians | 0.075                   | 0.162    | 0.224     | 0.168  | 0.107         |

<span id="page-8-0"></span>

| Training data                                        | Batch size | Learning rate     | Number of epochs |
|------------------------------------------------------|------------|-------------------|------------------|
| Black pedestrians only                               | 4          | $5 	imes 10^{-4}$ | 1                |
| All pedestrians, subsampled to $#$ Black pedestrians | 4          | $5 	imes 10^{-4}$ | 4                |
| Bronx pedestrians only                               | 4          | $5 	imes 10^{-4}$ | 2                |
| All pedestrians, subsampled to $#$ Bronx pedestrians | 4          | $5 	imes 10^{-3}$ | 4                |

Table 29: Model parameters used in this analysis.

Black pedestrians or pedestrians from the Bronx; the difference in precision was less than 0.005 for both groups relative to the original model trained on all races and locations. This is consistent with the fact that groups with the lowest performance are not necessarily small minorities of the dataset: for example, more than 90% of the stops are of Black or Hispanic pedestrians, but performance on these groups is worse than that for white pedestrians. The lack of improvement from in-distribution training suggests that approaches like group DRO would be unlikely to further improve performance, and we thus did not assess these approaches.

Discussion. We observed large disparities in performance across race and location groups. However, the fact that test-to-test in-distribution training did not ameliorate these disparities suggests that they do not occur because some groups comprise small minorities of the original dataset, and thus suffer worse performance. Instead, our results suggest that classification performance on some race and location groups are intrinsically noisier; it is possible, for example, that collection of additional features would be necessary to improve performance on these groups (Chen et al., 2018).

###### F.1.3 ADDITIONAL DETAILS

Modifications to the original dataset. The features we use are very similar to those used in Goel et al. (2016). The two primary differences are that 1) we remove features which convey information about a stopped pedestrian's race, since those might be illegal to use in real-world policing contexts and 2) we do not include a "local hit rate" feature which captures the fraction of historical stops in the vicinity of a stop which resulted in discovery of a weapon; we omit this latter feature because it was unnecessary to match performance in Goel et al. (2016). test-to-test
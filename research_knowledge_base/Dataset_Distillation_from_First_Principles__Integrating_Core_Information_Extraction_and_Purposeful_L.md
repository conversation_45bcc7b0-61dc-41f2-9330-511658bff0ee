# Dataset Distillation from First Principles: Integrating Core Information Extraction and Purposeful Learning

**<PERSON><PERSON><PERSON><PERSON>**

*Czech Technical University in Prague*

**Yuanfang Peng** <EMAIL> *Hong Kong University of Science and Technology*

**<PERSON><PERSON>ang Gu Gundhi** Gundhi Gundhi Gundhi Gundhi Gundhi Gundhi Gundhi Gundhi Gundhi Gundhi Gundhi Gundhi Gundhi G *Ohio State University*

*Duke University*

<NAME_EMAIL> *Imperial College London and Trinity College Dublin*

Fadwa Idlahcen **<EMAIL>** *Czech Technical University in Prague*

*Duke University*

Editor: My editor

<PERSON><PERSON><PERSON><PERSON> <EMAIL>

<EMAIL>

<EMAIL>

<NAME_EMAIL>

<EMAIL>

<EMAIL>

**<PERSON>ran Chen** <EMAIL>

## Abstract

Dataset distillation (DD) is an increasingly important technique that focuses on constructing a synthetic dataset capable of capturing the core information in training data to achieve comparable performance in models trained on the latter. While DD has a wide range of applications, the theory supporting it is less well evolved. New methods of DD are compared on a common set of benchmarks, rather than oriented towards any particular learning task. In this work, we present a formal model of DD, arguing that a precise characterization of the underlying optimization problem must specify the inference task associated with the application of interest. Without this task-specific focus, the DD problem is under-specified, and the selection of a DD algorithm for a particular task is merely heuristic. Our formalization reveals novel applications of DD across different modeling environments. We analyze existing DD methods through this broader lens, highlighting their strengths and limitations in terms of accuracy and faithfulness to optimal DD operation. Finally, we present numerical results for two case studies important in contemporary settings. Firstly, we address a critical challenge in medical data analysis: merging the knowledge from different datasets composed of intersecting—but not identical—sets of features, in order to construct a larger dataset in what is usually a small sample setting. Secondly, we consider out-of-distribution error across boundary conditions for physics-informed neural networks (PINNs), showing the potential for DD to provide more physically faithful data. By establishing this general formulation of DD, we aim to establish a new research paradigm by which DD can be understood and from which new DD techniques can arise.

Keywords: Dataset distillation (DD), information core extraction, resilient learning, task-specific optimization

©2024 V. Kungurtsev, Y. Peng, S. Vahidian, J. Gu, S. Vahidian, A. Quinn, F. Idlahcen and Y. Chen.

License: CC-BY 4.0, see <https://creativecommons.org/licenses/by/4.0/>. Attribution requirements are provided at <http://jmlr.org/papers/v24/24-0000.html>.

<span id="page-1-1"></span>

## 1 Introduction

Dataset distillation (DD) refers to a range of techniques for generating a synthetic dataset whose sample size  $N_S$  is relatively small, that functionally represents a much larger ( $N_T \gg N_S$ ) training set. The functional requirement is that a machine learning model trained on the synthetic data perform comparably in a specific task to one trained on the original data. DD has attracted increasing interest in recent years [Wang et al.](#page-29-1) [\(2018\)](#page-29-1); [Zhao et al.](#page-29-2) [\(2020\)](#page-29-2), with the growing research activity reflected in several survey articles [Lei and Tao](#page-27-0) [\(2023\)](#page-27-0); [Yu et al.](#page-29-3) [\(2023\)](#page-29-3); [Geng et al.](#page-26-0) [\(2023\)](#page-26-0).

Various methods have been developed for DD. Based on how the synthetic data is optimized, these methods can be broadly categorized into two approaches:

- 1. designing new criteria to measure the similarity between synthetic and original datasets, *e.g.*, [Zhao et al.](#page-29-2) [\(2020\)](#page-29-2); [Zhao and Bilen](#page-29-4) [\(2023\)](#page-29-4); [Kim et al.](#page-27-1) [\(2022\)](#page-27-1); [Cazenavette et al.](#page-25-0) [\(2022\)](#page-25-0); [Liu et al.](#page-27-2) [\(2023\)](#page-27-2); and
- 2. calculating meta validation loss on original datasets with models trained on synthetic ones, which can be regarded as the meta-learning style, *e.g.*, [Wang et al.](#page-29-1) [\(2018\)](#page-29-1); [Nguyen et al.](#page-28-0) [\(2020,](#page-28-0) [2021\)](#page-28-1); [Loo et al.](#page-27-3) [\(2022\)](#page-27-3); [Zhou et al.](#page-29-5) [\(2022\)](#page-29-5).

However, the current body of research lacks a cohesive framework. Formal proofs connecting the proposed criteria to a general notion of DD performance are missing, and there is a lack of mathematical and statistical insights in the existing literature. We argue that this gap exists because the task of DD is not yet well defined. Our goal is to address this issue by presenting a generalized framework that can provide a clearer conceptualization of the DD task.

Among the two primary approaches to DD, above, the first focuses on optimizing training efficiency, making it more practical and directly linked to performance outcomes. The optimization criteria within this approach can, in turn, be categorized into two types:

- (i) criteria which ensure distributional similarity between the synthetic dataset and the original training set, *e.g.*, [Zhao and Bilen](#page-29-4) [\(2023\)](#page-29-4); [Zhao et al.](#page-29-6) [\(2023\)](#page-29-6); [Sajedi et al.](#page-28-2) [\(2023\)](#page-28-2); and
- (ii) criteria which target the training dynamics, specifically the gradients along the training trajectories, *e.g.*, [Zhao et al.](#page-29-2) [\(2020\)](#page-29-2); [Cazenavette et al.](#page-25-0) [\(2022\)](#page-25-0); [Liu et al.](#page-27-2) [\(2023\)](#page-27-2).

However, there has been little analysis to determine why certain criteria are more effective for DD than others, or how to establish robust benchmarks for evaluating whether a given criterion genuinely advances DD performance. Of course, there is clear intuition behind these methods, for distributional similarity suggests an independent sub-sample and training dynamics motivate some functional criteria through its gradient definition. Thus the methods' clear sensibility presents an opportunity for a proper evaluation as to the intuition and mechanism behind their merit.

One recent innovative but superficial formal model is provided in [Sachdeva and McAuley](#page-28-3) [\(2023\)](#page-28-3). Here, the evaluation criterion for DD is the test error, that is, the error on the population of a model trained on the synthetic dataset. Of course, this is never available, so DD methods can be considered various approximations to this ideal procedure. This perspective aligns with the bilevel optimization problem which has been adopted as a suitable framework for DD in other studies (e.g. [Vahidian et al.](#page-29-7) [\(2024\)](#page-29-7) :

<span id="page-1-0"></span>
$$
\min_{\hat{D}} f(\theta^*, D), \, \theta^* \in \arg\min f(\theta, \hat{D}) \tag{1}
$$

where  $f(\theta, D)$  is the loss of the model with parameters  $\theta$  on the population data D and D is optimal synthetic dataset.

To evaluate the faithfulness of this criterion as an appropriate definition of the functional features and properties of DD, we consider a thought experiment, below. Specifically, we argue that it is possible to construct a solution that—despite satisfying criterion [\(1\)](#page-1-0)—is vacuous, and cannot possibly serve as a valid criterion for what can be commonly understood to be the intention of DD.

Recall that in standard DD works, the model is a Deep Neural Network (DNN), and, given that  $N<sub>S</sub>$  is small, is overparameterized relative to the distilled dataset, which implies that there exist multiple minimizers with zero loss for the inner optimization problem in [\(1\)](#page-1-0) [Cooper](#page-25-1) [\(2021\)](#page-25-1). Consequently, the optimal dataset is one where

$$
\arg\min_{\theta} f(\theta, \hat{D}) \subset \arg\min_{\theta} f(\theta, D)
$$

This inclusion suggests a vacuous solution, because it implies the following unreasonable anomalies:

- 1. There is no guarantee that  $\hat{D} \in \text{supp}(D)$ . Under some models, it may be very far from the support of the original population distribution  $D = (X, Y)$  and still satisfy [\(1\)](#page-1-0);
- 2. It is evident that increasing the size of  $\hat{D}$  makes the problem more challenging, as the minimal surface becomes lower-dimensional with a larger sample size [Cooper](#page-25-1) [\(2021\)](#page-25-1). Intuitively, however, a larger synthetic dataset allows for better representation of the distribution.
- 3. The sufficient statistics of D are enough to yield a solution. For instance, if  $\hat{D} = \{x, y\}$  with  $x = \mathbb{E}[X], y = \mathbb{E}[f(\theta^*, X)]$  with  $\theta^* \in \arg\min_{\theta} f(\theta, D)$  -this would be the optimal solution.

These anomalies suggest that the formulation of DD in [\(1\)](#page-1-0) does not capture the full complexity of the uncertainty involved and the subtlety associated with DD practice and how it is done by ML practitioners.

In this paper, we provide a formal definition of DD, which overcomes weaknesses in current approaches to the problem. A key insight is that criteria for DD should be tailored to the specific application of interest, rather than being predetermined as a one-size-fits-all approach.

In summary, the main contributions of this paper are:

- A formalization of the DD problem as on explicit optimization problem that is general and flexible as far as settings, criteria, models, data distribution and application.
- A comprehensive list of existing applications that can be subsumed in this framework, as well as new possible applications of this powerful technique.
- An understanding of the theoretical properties of the optimization problem and how existing techniques can be understood as approximating different components of the general procedure.
- Two case studies of novel applications in DD for settings of contemporary interest: medical data augmentation and physics informed machine learning generalization across boundary conditions.

The rest of the paper is structured as follows: Section [2](#page-3-0) presents our formalism for the general DD optimization problem. In Section [3,](#page-6-0) we provide several examples, illustrating how this formalism provides intuitive flexibility to consider a range of applications of DD which have not yet been addressed in the literature. Section [4](#page-9-0) discusses variational aspects of the DD optimization problem, in light of the use of probability distributions as state variables. Section [5](#page-12-0) analyzes existing methods of DD in light of our general framework. Section [6](#page-15-0) gives an analytical derivation of a DD solution, under our optimization framework, for categorical data, suggesting the possibility of its further development. In Section [7,](#page-16-0) we present a novel and important application of DD: the synthesis of partially censored data in order to build a complete dataset appropriate for probabilistic graphical modelling in health applications. Another novel example is given in Section [8,](#page-22-0) involving physics-informed neural networks (PINNs). Finally, we provide concluding remarks in Section [9.](#page-24-0)

<span id="page-3-0"></span>

# 2 The Formal Dataset Distillation (DD) Optimization Problem

We now define the notation for all required quantities and operators:

- $D = (D_X, D_Y) = (X, Y)$  is a random variable which represents sampling from the population from which the training set is drawn, associated with a probability space,  $(\Xi, \mathcal{F}, \mathbb{P}_D)$ . For instance, in computer vision,  $X$  can be pixel data and  $Y$  the label on an image. We can consider that  $\mathbb{P}_D$  has a density  $\mu$ , with respect to an appropriate reference measure,  $\lambda$  (typically Lebesgue or counting),  $\mathbb{P}_D(A) = \int_A \mu(x, y) d\lambda(x, y)$  for  $A \subset \mathbb{R}^{d_x+d_y}$ . The domain of  $(X, Y)$  can also be a manifold, M. Also, in the generative case, the probability model may only involve the features  $D = X$ . For simplicity of notation, in the sequel we consider that  $\Xi \subset \mathbb{R}^n$  and that every independent sample is in this finite dimensional Euclidean space.
- A training set, defined as an i.i.d. sample of the population  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^T \subset D$ .
- We denote the synthetic dataset by  $\hat{D} = \{(\hat{x}_i, \hat{y}_i)\}_{i=1,\dots,N_S}$ . This finite-dimensional quantity is the primary optimization decision variable in DD.
- In the general case, consider  $\mathcal{D}_{\theta(\omega)}(\cdot||\cdot)$  to be the kernel of a probability distance or f-divergence, and thus takes two measures as arguments. In the sense of kernel we mean a distance  $d$ on metric spaces, that is with  $\mathcal{I}(D, D) = \mu^{I}$ , a probability measure for the inference I,  $\mathcal{D}_{\theta(\omega)}(\hat{I}||\mathcal{I}(D,D)) = \mathbb{E}_{\omega}l(y_1(\nu),y_2(\nu)) = (y_1(\nu)-y_2(\nu))^2 \in \mathbb{R}^+ \times \Xi$ . That is, for a random element in D, we define the metric distance from the ideal to the computed distribution of the inference task. When the latter is itself stochastic, we take the expectation.
- $M_{\theta} \in \mathcal{B}$  is a model in some function space parameterized by  $\theta$ , for instance a (deterministic) linear function,  $y = \beta_0 + \beta_1^T x$ . Thus, formally,  $M_\theta$  is a map from the parameters  $\theta$  into a function space, B, e.g.  $\{(\bar{\beta}_0, \beta_1) \in \mathbb{R}^2\} \to \beta_0 + \beta_1^T x$ . Alternatively,  $M_{\theta(\omega)} \in \mathcal{B}(\omega)$  is either the optimization problem with noise or error, or a Bayesian model. For instance, a neural network  $\sigma(x, \theta(\omega))$  with weights following some parameterized distribution or process,  $\theta(\omega) \sim \mathcal{W}_{\omega}(w)$  with hyperparameters w. Thus, formally,  $M_{\theta(\omega)}$  is a stochastic process defined on a function space. We shall sometimes use, e.g.,  $M[\theta]$  instead as for notational convenience.
- $O(\cdot)$  or  $O(\cdot,\omega)$  denotes an optimizer with guaranteed convergence to a minimum, or an ergodic sampler for Bayesian models. For instance, consider the application of stochastic gra-

dient descent (SGD) on  $\overline{D} \subset D$  yielding  $O(\overline{D}) \to \hat{\theta}$ , where now  $\hat{\theta}$  are the neural network weights to use in  $M_{\theta}$ , the neural network model. In the case of a sampler rather than a point optimizer, a canonical example is the application of Hamilton Monte Carlo, yielding a Gibbs distribution on the same problem, i.e.  $\theta \sim \omega = \frac{e^{-f(\bar{D})}}{Z}$  $\frac{f(D)}{Z}$ , a posterior or pseudo-posterior. In this case, we incorporate the additional stochastic parameterization,  $M_{\theta(\omega)}$ , and so inference on input variables itself corresponds to sampling, that is, first from  $\theta(\omega)$  and then application of this instance on the input to yield  $M_{\theta(\omega)}[\bar{x}] \to \bar{y}(\omega)$ 

- $\mathcal{R}_{D_X}$  denotes a risk measure, which can be the risk-neutral expectation operator,  $\mathbb{E}[\cdot]$ , or a specific risk measure such as the conditional value at risk (CVaR). The distribution with respect to which the expectation oeprator in the risk quantity is taken is the marginal  $\mu_x$ , i.e. the distribution of the input variable,  $X$ .
- $\mathcal{I}(M_{\theta}, D)$  is an inference task of the model on the population data D. The output yields a particular distribution, possibly degenerate (i.e. delta-type). An example is a collection of evaluations of a trained model on a set of input data. Another example is a condition or marginal inquiry with respect to some of the input variables of the posterior likelihood, i.e. the distribution of Y.  $\mathcal{I}(\cdot, \cdot)$  could also be itself the input to an optimization or sampling problem, such as in neural architecture search (NAS). Formally,  $\mathcal{I}: B \times \Xi \to \bar{\mu}^I_D$ , where  $\bar{\mu}^I_D$ denotes a measure on  $\Xi$  With an abuse of notation, we also write  $\mathcal{I}(D, D)$  as some conditional marginalization of the distribution of D, but generically we write  $\mathcal{I}(q, D)$  where  $q : \Xi \to \Xi$ is some operator on D.

With these notions, we define dataset distillation (DD) as the following optimization problem:

<span id="page-4-0"></span>
$$
\min_{\hat{D}\in\mathbb{R}^{S\times n}} \mathcal{R}_{D_X} \left\{ \mathcal{D}_{\theta(\omega)} \left( \mathcal{I} \left( M[O(\hat{D}, \omega)], D \right) || \mathcal{I}(g, D) \right) \right\} \tag{2}
$$

With this compact notation, we see that the DD optimization problem is defined as one of seeking a dataset,  $\ddot{D}$ , which—if used to train a model via  $O$ —ensures that a *specified* inference task,  $\mathcal{I}(\cdot, \cdot)$ , exhibits minimal discrepancy from the true population statistics, averaged over noise in the statistical model. While the decision variable of DD is  $\ddot{D}$ , a deterministic quantity, the composition involves a nested sequence of probabilistic operations. Moreover, the regularity of the operations, that is their appropriate functional space and continuity properties, are not immediately clear. For instance,  $O(\cdot)$  may not even be a continuous function of its argument,  $\hat{D}$ , for example if it involves minimization of a non-convex loss function. The criteria and algorthmic choices for  $O$  can significantly affect the outcome of inference  $I$ . This clearly suggests that the inference accuracy—or some surrogate thereof—should be of prime consideration in the choice of design of the operator O. Ultimately, though, because little is even qualitatively certain regarding the map from the dataset  $\hat{D}$  to the quantity given as the full cost function, that is,  $\mathcal{R}_{D_X} \{ \mathcal{D}_{\theta(\omega)} (\cdots) \}$  above, in [\(2\)](#page-4-0), there are significant challenges in demonstrating the existence of a minimizing sequence and a limit of that sequence.

Let us now write the optimization problem lifted to include equality constraints. Formally:

<span id="page-4-1"></span>min   
 \underset{\hat{D}, \hat{\theta}(\omega), \hat{I}}{\text{min}} \mathcal{R}\_{D\_X} \left\{ \mathcal{D}\_{\omega} \left( \hat{I}(\omega) || \mathcal{I}(g, D) \right) \right\}

subject to

$$
\hat{\theta}(\omega) = O(\hat{D}, \omega)
$$
$$
\hat{I}(\omega) = \mathcal{I}(M_{\hat{\theta}(\omega)}, D)
$$
(3)

Thus, we interpret a dataset as properly distilled if it is useful for some inference task  $I$ , that is the model trained on it will yield a marginal distribution  $\hat{I}$  that closely approximates a desired functional on the population distribution  $\mathcal{I}(g, D)$ . As this could be applied in a variety of domains for a variety of purposes, we aim to analyze a general inference task. This permits practitioners to tailor optimizers to the specific inference task and application of interest. We present the equality constraints explicitly in [\(3\)](#page-4-1), rather than implicitly in the objective function [\(2\)](#page-4-0), in order to reveal the derivative structure which might be exploited in the optimization.

This decomposition presents two additional state variables,  $\hat{\theta}(\omega)$  and  $\hat{I}$ . The first one presents a flexible set of possibilities:

- 1.  $\theta \in \mathbb{R}^m$ , for a point estimate vector of an optimal set of parameters
- 2.  $\theta(\omega) \in \mathcal{M}(\mathbb{R}^m)$ , indicating that it is a probability distribution over the m-dimensional realvalued parameter vector.
- 3.  $M_{\theta(\omega)} \in \mathcal{M}(\mathcal{B}(\mathbb{R}^d))$ , i.e., the most general case of a nonparametric model, where  $\mathcal B$  is an appropriately chosen function space. In this case we have a stochastic process on the data vector space  $\mathbb{R}^d$ .

The quantity  $\hat{I}$  is the output of the application of the model to the population data as far as computing some inference task. An inference task can simply be the test error, i.e. comparing the push-forward of the model on the input data,  $M_{\theta(\omega)}(X)$ , to the conditional distribution  $Y|X$ , weighed by the population prior of  $X$ . Otherwise, it can be some marginal or conditional enquiry, a likelihood or scoring function calculation, etc.

Let us consider the parametric (possibly Bayesian) case first. Generically, both  $\theta(\omega)$  and I are probability density functions, which are generally considered in the functional class of distributions, and so we must apply the appropriate analytic machinery to consider solution existence and regularity.

Note that the training set,  $\mathcal{T}$ , does not explicitly appear in either formulation of DD above (i.e. in [\(2\)](#page-4-0) or [\(3\)](#page-4-1)). Rather, it remains implicit and defines a data generating procedure of  $\mathcal{T} \sim D$  in order to evaluate the quantity  $\mathcal{I}(q, D)$  in the objective. In other words, T is a subset to be used for empirical risk minimization, or samples taken with or without replacement to mimic an online learning stochastic approximation setting.

Formally, we consider [\(3\)](#page-4-1) to be the **Online Stochastic Approximation** problem, i.e., we have unlimited access to i.i.d. samples of  $D$ . Then an **Empirical Risk DD** variant, with limited training data,  $\mathcal{T}$ , becomes a sample average approximation (SAA) to operations within the problem; i.e. with  $N_T$  training samples and  $N_W$  samples of  $\omega$ :

$$
I^* = \mathcal{I}(g, D) \approx \mathcal{I}(g, \mathcal{T}),
$$
  
\n
$$
\mathcal{R}_{D_X}[\mathcal{D}(\hat{I}||I^*)] = \mathbb{E}_{D_X}[\mathcal{D}(\hat{I}(\omega)||I^*)] \approx \frac{1}{N_T N_W} \sum_{j=1}^{N_W} \sum_{i=1}^{N_T} d(\hat{I}(\omega_j), g(x_i, y_i))
$$
\n(4)

While it may then be tempting to redefine [\(3\)](#page-4-1) in terms of the sample averages, we avoid this in light of the fact that sub-sampling may be useful as intermediate procedures to target the real risk more successfully (see, *e.g.* [Patil and Du](#page-28-4) [\(2024\)](#page-29-7) and, in application to DD directly, [Vahidian et al.](#page-29-7) (2024)).

We present several examples of the general DD formulation [\(3\)](#page-4-1) in the next Section. We include some of the existing DD use cases, as well as some novel potential applications that this flexible formulation facilitates. Then, in Section [4,](#page-9-0) we analyze the optimality conditions of the problem. The

fact that we decompose the DD problem explicitly with respect to the constraints [\(3\)](#page-4-1)—thereby introducing the measure-valued intermediate states explicitly in the problem—reveals a more transparent understanding of the solution, as well as an understanding of various potential approximations.

<span id="page-6-0"></span>

## 3 Inference Tasks as DD applications

In order to provide some grounding for the DD formalism presented in Section [2,](#page-3-0) we now consider several DD applications, and construct concrete examples of all of the operators and quantities given in [\(2\)](#page-4-0).

Example 1 *Test Error This is the simplest setting, being the one invoked in most of the published numerical experiments on DD. Furthermore, it is equivalent to the axiomatic DD optimization problem defined in [Sachdeva and McAuley](#page-28-3) [\(2023\)](#page-28-3).*

*For simplicity, we assume an optimization procedure with global convergence guarantees, and so*

$$
O(\hat{D}) = \arg\min_{\theta}, \, l(m(\theta, \hat{D}_x), \hat{D}_y)
$$

*where* l *is the loss function and* m *is the model associated to* O*. This same function is the target of inference:*

$$
(\mathcal{I}: X \to Y) [D_x], \hat{I} = m(\theta, D_x)
$$

*If we take*  $\mathcal{R} = \mathbb{E}$  *and*  $\mathcal{D} = l(\cdot, \cdot)$  *in* [\(2\)](#page-4-0)*, we finally get the problem:* 

<span id="page-6-1"></span>
$$
\min_{\hat{D},\hat{I},\theta} \mathbb{E}_X \left[ l(\hat{I}, D_Y) \right] \nsubject to \quad \theta = \arg \min l(m(\theta, \hat{D}_X), \hat{D}_Y) \n\hat{I} = m(\theta, D_X)
$$
\n(5)

As noted in Section [1,](#page-1-1) we can construct a vacuous solution as follows: If we know some  $\theta^* =$  $\argmin l(m(\theta,D_X),D_Y)$ , and  $\theta\in\mathbb{R}^d$ , then with  $\sharp(\hat{D})=N_S\ll d$ , there is a  $d-N_S$  dimensional *manifold that satisfies*  $\theta^* = \arg \min l(m(\theta, \hat{D}_X), \hat{D}_Y)$ . Any such  $\hat{D}$  will solve [\(5\)](#page-6-1). This suggests *that the problem is insufficiently determined, and a more precise specification of the DD criteria will enable a more precise understanding of potential algorithmic solutions.*

Example 2 *Conditional Marginal Uncertainty Quantification This is similar to the previous example, but points to the flexibility of this DD construction. It can be motivated by considerations in medical diagnosis. We want to estimate the probability of a positive label under the condition that one input variable is a particular value. Furthermore, we would like to consider the entire uncertainty profile of the label across different inputs. We use an HMC sampler with a stationary*  $distribution \ \pi^\theta(V)$  of Gibbs form on the potential  $V.$  In this case, formally,

min
  

$$
$\hat{D}, \hat{I}, \theta$
$$

$$
\mathbb{E}_X 
[ 
 $\mathbb{E}$ 
 $\omega$ 
 $\mathcal{KL}(\hat{I}(\omega), \mathcal{I}(\{y | D_X : [X]_i = a\}, D_Y) | D_X : [X]_i = a)$ 
]
$$
 $(6)$ 

subject to

$$
$\theta \sim \pi(m(\theta, \hat{D}_{X | [X]_i = a}), \hat{D}_Y, \omega)$
$$

$$
$\hat{I}(\omega) = \pi^{\theta}(X | [X]_i = a, \omega)$
$$

*where here we abuse notation and simply mean the*  $\mathcal{KL}$  *kernel, that is,*  $\mathcal{KL}(a, b) = a \log(b/a)$ 

Example 3 *Neural Architecture Search In this case we are interested in a dataset that is useful insofar as optimizing an inner problem in a bilevel optimization problem. The inner problem considers the optimal weights given an architecture. In this case the inference depends on the solution of an optimization problem (the inner problem). Consider a prior* π <sup>A</sup> *over the architectures before their optimization, we want the dataset that fits the best performing weights across possible architectures* A *defining* m*, i.e., it is itself a measure on a function space* B*. This can be constructed as follows:*

min   
 \(\hat{D},\hat{I},\theta\) \(\mathbb{E}\_X\left[\mathbb{E}\_{\mathcal{A}}\left[l(\hat{I}(\mathcal{A}), D\_Y)\right]\right]\)

*subject to*

$$
\hat{\theta}(\mathcal{A}) = \arg \min_l l(m(\theta, \hat{D}_X, \mathcal{A}), \hat{D}_Y)
$$

$$
\hat{I}(\mathcal{A}) \sim \pi^{\mathcal{A}} \circ \left[ \hat{\theta}(\mathcal{A}), D_X \right]
$$
(7)

*where the last quantity is the distribution, with respect to the prior on* A*, of the predicted label given* θ *and an element of the distribution of* X*.*

Example 4 *Continuous Learning Consider that we want to be able to update a model to future heterogeneous data, while simultaneously continuing to be able to fit historical data, i.e., avoid catastrophic forgetting. We can formulate the procedure by indicating that the optimizer will be parameterized to add an additional dataset with a shifted distribution.*

$$
\min_{\hat{D},\hat{I},\theta(\omega)} \mathcal{R}_{D_X} \left\{ \max \left( \mathcal{D}_{\omega} \left( \hat{I}(\omega) || \mathcal{I}(\bar{D}(\omega), \cdot) \right), \mathcal{D}_{\omega} \left( \hat{I}(\omega) || \mathcal{I}(D, \cdot) \right) \right) \right\}
$$
subject to

$$
\hat{\theta}(\omega) = O \left( \{ \hat{D} \cup \bar{D}(\omega) \} \right)
$$
$$
\hat{I}(\omega) = \mathcal{I}(M_{\hat{\theta}(\omega)}, D) \tag{8}
$$

*The generic inference operation*  $\mathcal I$  *is flexible, but can be (test) loss. The subscript of*  $\mathcal D$  *over*  $\omega$ *indicates the distribution over past data considered, and thus the expectation performs a weighing of the historical loss.*

With this framework, one can obtain the appropriate intuition as to how to design DD algorithms for a given task. While all of these examples are intractable as they are stated, each component can be approximated using well known stochastic optimization techniques associated with Stochastic Approximation and Sample Average Approximation. In this way the training samples are used  $\mathcal T$ algorithmically to approximate  $D$ . Thus algorithms can be developed to increasingly improve the various aspects of the training over time, escalating the research program of DD.

We present three final examples in greater detail. The first two we highlight as the target of this work and the focus of the Numerical Results. The final example includes a comprehensive analytic derivation of the details for pedagogical purposes.

Example 5 *Medical Data Bootstrapping It often occurs in applications involving public health and modeling medical diagnosis and treatment that a number of heterogeneous data sets are available. At the same time each particular dataset is one with a relatively low number of individual samples, due to the cost of scaling the participant count in clinical trials, that is relatively high dimensional, as medical advances has yielded a number of informative biomarkers one can measure. As far as modeling from the ML perspective, the issue of overparameterization becomes paramount, and so we propose an approach that* leverages *the large union of many different data sources to construct* *an accurate neural model, then constructs a dataset that is informative for the express purpose of constructing a probabilistic graphical model (PGM),*

<span id="page-8-0"></span>min
 $\hat{D}, \hat{I}, \theta(\omega)$ 
subject to

$$
\mathcal{R}_{D_X} \bigcup_i \left\{\mathcal{D}_{\omega} \left( \hat{I} | \mathcal{I}(D, D(\tilde{y}_i)) \right) \right\}
$$

$$
\hat{\theta}(\omega) = O_{PGM(i)} (\hat{D}, \omega)
$$

$$
\hat{I} = \mathcal{I}(M_{\hat{\theta}(\omega)}, D(i_i))
$$
(9)

*where*  $\{i_i\}$  *is a set of possible inputs and*  $\{\tilde{y}_i\}$  *the outputs of the tasks of inference of interest, e.g., expected time to disease progression, conditional treatment effect, etc. with the associated marginal distribution*  $D(\iota_i)$ ,  $O_{PGM}$  performs structure and parameter learning from the data-set D. We let *this depend on the particular inference task, as different structures of PGMs are more efficient to perform different tasks. We consider generically a risk over the union of such inference measures, and in practice this can be some weighted average across the choices of inference tasks. Note that this formulation does not explicitly incorporate the neural modeling, this will be described as an instrumental technique.*

**Example 6** *Physics Informed Machine Learning Consider that we have some PDE* $\mathcal{F}(u(x), \gamma(x)) =$ 0 *on* Ω *with boundary conditions* B(u(x)) = 0 *for* x ∈ ∂Ω*. A neural network is used to parameter*ize  $u$  to obtain a loss function, over a dataset of  $N$  lattices  $\{x_k\} \in \mathbb{R}^{d \times M}$  and their corresponding *values*  $f_k$ 

$$
\mathcal{L}_{F}(\theta_{u}, \theta_{\gamma}; \{x\}, \hat{f}) = \frac{1}{MN} \sum_{m=1}^{M} \sum_{l=1}^{N} (\mathcal{F}(\bar{u}_{l}(\theta, x_{m}), \bar{\gamma}_{l}(\theta, x_{m})) - \hat{f}_{ml})^{2}
$$

*Suppose that we are particularly interested in generalization across boundary conditions. As such, consider that there is a population prior on the boundary*  $\pi(u_{\partial\Omega})$ *. Based on this prior we would like to minimize the chance that a set of parameters* ˆθ *trained on* Dˆ *minimizes the Conditional Value* at Risk, across the distribution, of the PDE interior reconstruction loss. Consider that  $\hat{D}_g$  also *includes a set of samples of functions*  $g(x)$  *with*  $x \in ∂Ω$ *. We can assume that* g *is sampled from*  $\pi(u_{\partial\Omega})$ ,

$$
\min_{\hat{D}, \theta} \quad \mathbf{CVaR}_{\pi(u_{\partial\Omega})} [\mathcal{L}_F(\theta_u, \theta_\gamma; D)]
$$

$$
subject to \quad \theta = \arg \min_{\theta} \mathcal{L}_F(\theta_u, \theta_\gamma; \hat{D})
$$

$$
s.t. \quad u(x_{\partial\Omega}, \theta, g) = g(x_{\partial\Omega}) \sim \hat{D}_g \tag{10}
$$

*where we can collapse the inference task*  $\hat{I}$  *as just the application of the neural network on the distributional data (which we would use the training data to estimate).*

Finally, we present a more transparent analytic derivation of the learning optimization problem defined by DD towards a classical approach to modeling time series data.

Example 7 *Mixture of AR Time Series Models Inspired by [Karn](#page-26-1) ´ y [\(2016\)](#page-26-1); Sm ` [´ıdl and Quinn](#page-28-5) [\(2005\)](#page-28-5). As a reasonable concrete setting in which DD can apply, consider that we wish to learn a mixture of* *Autoregressive Time Series models for a multi-dimensional stochastic process, defined over mixture weights*  $\{m_q\}$  *and transition matrices*  $\{M_q\}$ ,

$$
X_{k+1} \sim \sum_{q=1}^{m} \mathbf{m}_q \mathbf{M}_q^T X_{k+1}
$$

*where for simplicity we consider a finite mixture. In the case that*  $X_k$  *is categorical, that is it is X*-valued, where *X* is isomorphic to  $\mathbb{Z}_{/n}^+$  for some *n*, this permits sufficient expressivity for any *potential transition.*

*Consider that at a particular system is observed over a finite period of time. Then, the observations cease, either due to their being unobservable for a subsequent period of time, or the research staff being unavailable to collect the data, or even some technical malfunction. Knowing that the process is stationary, the learner constructs a synthetic dataset that is representative of the historical information. We consider that the desired inference task is forecasting the subsequent time period.*

min
 $\hat{D}, \hat{I}, \theta(\omega)$ 

$$
\mathcal{R}_{D_t} \left\{ \mathcal{D} \left( \hat{I} || D_{t+1} \right) \right\}
$$
subject to
$$
\mathbf{m} = O_{EAR} \left( \hat{D} \right), M \in \Delta^I
$$

$$
\hat{I} = \mathcal{I} \left( \sum \mathbf{m}_q \mathbf{M}_q, D_t \right)
$$
(11)

*which we solve with a standard Empirical Risk least squares Minimization as,*

$$
\min_{\hat{D}, \hat{I}, \theta(\omega)} \quad \left\| \hat{I} - \mathcal{T}_{t+1} \right\|^2
$$

$$
\text{subject to} \quad \mathbf{m} = O_{EAR}(\hat{D}), \ M \in \Delta^I
$$

$$
\hat{I} = \mathcal{I}(\sum \mathbf{m}_q \mathbf{M}_q, \mathcal{T}_t) \tag{12}
$$

We can obtain a consistent estimator  $O_{EAR}$  with counting operations on the transitions. This assigns a model  $\sum^I$ i  $m_i(\hat{D})\Theta_i$  of mixture weights. From this model we obtain the forecast on the training data  $\hat{I} = \sum^I m_i(\hat{D}) \Theta_i^T X \tau_t$  and compute the reconstruction loss to the real future in the *training*  $X_{\mathcal{T}_{t+1}}$ . This is one function evaluation. However, unlike the other examples, there is no *clear backwards pass or gradient, indeed* Dˆ *is categorical, then there is no well defined gradient.*

*In the subsequent Section [6](#page-15-0) we approach the problem of DD for categorical variates using integer programming.*

<span id="page-9-0"></span>

## 4 Properties of the Optimization Problem

As noted in Section [2,](#page-3-0) despite the technical challenges involved, understanding the mechanics of DD requires studying a lifted variant of the problem wherein the intermediate operations are clearly visible and take on auxiliary variables. This decomposition allows a more fine tuned algorithm design, that is by focusing on particular components of the composition. Many specific ML techniques can be defined as approximations to some of these compositions. However, there is a significant difficulty added, namely that this becomes infinite dimensional optimization problem on the set of probability measures.

# DD FIRST PRINCIPLES

In this section we derive and study the optimality conditions for the DD optimization problem defined above. Treatment of statistical fitting with distributions from an optimization context is classical in Bayesian analysis Csiszár [\(1975\)](#page-26-2). But with  $D$  a finite dimensional real vector, in general variational considerations are well amenable to establishing optimality results for standard Bayesian inference techniques for parametric models, as the problem can be formulated simply as a function of the parameters in the model as decision variables [Bissiri et al.](#page-25-2) [\(2016\)](#page-25-2). When (even auxiliary) variables are introduced that are probability densities themselves, the machinery has to properly account for them.

The study of optimization problems with distributions as explicit variables has been a growing topic of importance. The framework of probabilistic design Kárnỳ and Kroupa [\(2012\)](#page-27-4) explicitly seeks distributions to minimize distance to some target, under some conditions. Recently, generalized variational inference [Knoblauch et al.](#page-27-5) [\(2022\)](#page-27-5) codified a general approach to probabilistic modeling that encapsulates standard sampling, classical Variational Inference techniques, and others.

Deeper formalisms include recent work considering the circumstance in which the measure space is endowed with the Wasserstein metric, thus presenting a topological vector space for which optimality conditions can be valid. See, *e.g.* [Molchanov and Zuyev](#page-28-6) [\(2004\)](#page-28-6) and a more recent presentation incorporating constraints [Bonnet](#page-25-3) [\(2019\)](#page-25-3). More general techniques for incorporating a variety of functionals on distributions can be performed with Orlicz spaces [Edgar and Sucheston](#page-26-3) [\(1992\)](#page-26-3).

Optimality conditions are generally conditions involving the directional derivative of the objective along feasible directions. As we are not considering any inequality constraints, feasible directions are just linearized subspaces of the equality constraints, that is tangent cones are not a necessarily technical focus point of our analysis. Extensions, however, are available, the appropriate variational analysis is described in [Molchanov and Zuyev](#page-28-7) [\(2000\)](#page-28-7).

Let us recall the formal optimization problem for reference, simplifying the presentation by considering point optimization rather than Bayesian models, i.e.  $\theta(\omega) = \theta$ , we obtain:

subject to

$$
M_{\theta(\omega)} = O(\hat{D})
$$
$$
\hat{I} = \mathcal{I}(M_{\theta(\omega)}, D)
$$

where d is simply a metric on the topological vector space corresponding to  $\hat{I}$ .

We proceed with the analytical derivation of the directional derivatives. To this end the tangent cone corresponding to feasible directions  $(\delta \hat{D}, \delta \hat{I}, \delta \theta)$  are the linearization of the constraints, that is,

<span id="page-10-0"></span>
$$
DM_{\theta^*}[\delta\theta] = DO(\hat{D})[\delta\hat{D}], \qquad \delta\hat{I} = D^{(1)}\mathcal{I}(M_{\theta^*}, D) \, DM_{\theta^*}[\delta\theta] \tag{13}
$$

where the variations can be considered as Gateaux derivatives. The first expression indicates the linear sensitivity of the optimization solution  $\theta^*$  in the direction of  $\hat{D}$  (see [Bonnans and Shapiro](#page-25-4) [\(2013\)](#page-25-4) for a comprehensive study, or [Levy](#page-27-6) [\(2001\)](#page-27-6) for a concise informative presentation). That is, as the data parameterizing the optimization solver  $D$  is perturbed by a small variation, this induces a change in the solution  $\delta\theta$  that perturbs the model  $M_\theta$ . We can observe that with most simple functional models, the parametric dependence on  $\theta$  is Lipschitz or their gradient is Lipschitz. Moreover, by adding a small strongly convex regularization to the objective, a unique (local) solution as well as local stability and directional differentiability subject to perturbations is expected to hold.

The second expression involves the variation in the inference distribution with respect to the model. The overall stability will again depend on the specific inference computation. However, standard conditional, marginal, or total expectation operators are stable to first order parametric perturbations Dupačová [\(1990\)](#page-26-4).

Continuing to variations of the objective, we obtain the stationarity condition:

<span id="page-11-0"></span>
$$
\partial \mathcal{R}_{D_X} \left\{ d \left( \hat{I}^*, \mathcal{I}(D, \cdot) \right) \right\} D^{(1)} d \left( \hat{I}, \mathcal{I}(D, D) \right) \delta \hat{I} \ge 0 \tag{14}
$$

for all  $(\delta \hat{D}, \delta \hat{I}, \delta \theta)$  satisfying [\(13\)](#page-10-0). Note that  $\mathcal R$  is convex, so the subgradient is the conventional one. From the Orlicz perspective (see [Javeed et al.](#page-26-5) [\(2023\)](#page-26-5) for a contemporary application inspiring this analysis) we can consider that  $\hat{I} \in L^1(\mathbb{R}^{d_x+d_y})$  and the dual operator  $D^{(1)}d(\hat{I}, \mathcal{I}(D, D)) \in$  $L^s(\mathbb{R}^{d_x+d_y})$  for  $s \in [1,\infty)$  if the (topological vector space) pairing is endowed with the weak topology and  $s = +\infty$  with a weak\* topology. Note that application to  $\hat{I}$  requires  $M_{\theta^*} \in (L^1(\mathbb{R}^{d_x + d_y})^*$ and we shall consider this in the sequel. Finally  $\partial \mathcal{R}_{D_X}\left\{d\left(\hat{I}^*, \mathcal{I}(D, \cdot)\right)\right\} \langle \cdot, \cdot \rangle$  is a bilinear form. We can see by similar reasoning as by [Javeed et al.](#page-26-5) [\(2023\)](#page-26-5) and Ruszczyński and Shapiro [\(2006\)](#page-28-8) that we can ensure, for well behaved metrics  $d$  and inference tasks  $\mathcal{I}$ , that any local minimizer satisfies the Variational Inequality and that there exists at least one tuple  $(\delta \hat{D}, \delta \hat{I}, \delta \theta)$  satisfying [\(14\)](#page-11-0).

Now, consider the application of [\(Molchanov and Zuyev, 2004,](#page-28-6) Theorem 2.1) to define the first order necessary conditions as satisfied by  $\hat{D}^*, \mu_*^{\hat{I}}$  and  $\theta^*$  together with the two Lagrange multipliers  $y^M \in C^1(\Theta, (L^1(\mathbb{R}^{d_x+d_y}))^{**})$  and  $y^I \in (L^1(\mathbb{R}^{d_x+d_y}))^*$ . The variational in Lagrange multiplier form is the condition that for *all* valid tuples  $(\delta \hat{D}, \delta \hat{I}, \delta \theta)$  (i.e., satisfying [\(13\)](#page-10-0)), that are measureable and preserving nonnegativity of the operator, satisfy,

$$
\partial \mathcal{R}_{D_X} \left\{ d \left( \hat{I}^*, \mathcal{I}(D, \cdot) \right) \right\} D^{(1)} d \left( \hat{I}, \mathcal{I}(D, D) \right) \delta \hat{I} - \left\langle y^I, \delta \hat{I} \right\rangle \ge 0
$$

$$
\left\langle y^M, DM_{\theta^*}[\delta \theta] \right\rangle + \left\langle y^I, \mathcal{I}(DM_{\theta^*}[\delta \theta], D) \right\rangle \ge 0
$$

$$
\left\langle y^M, DO(\hat{D})[\delta \hat{D}] \right\rangle \ge 0
$$
(15)

where we applied linearity of  $R$  with respect to its base measure.

We proceed along the lines of reasoning in [Molchanov and Zuyev](#page-28-6) [\(2004\)](#page-28-6). Using the Radon-Nykodyn Theorem, we can consider the first order optimality conditions for the optimization problem in strong form, as in as equation systems rather than statements on directional derivatives. In this case we denote the measure associated with  $\hat{I}$  as  $\mu^{\hat{I}}$  with densities  $p^{\hat{I}} = \frac{d\mu^{\hat{I}}}{d\lambda}$ .

$$
\partial \mathcal{R}_{D_X} \left\{ d \left( p^{\hat{I}^*}, \mathcal{I}(D, D) \right) \right\} D^{(1)} d \left( p^{\hat{I}}, \mathcal{I}(D, D) \right) p^{\delta \hat{I}} - \left\langle y^I, p^{\delta \hat{I}} \right\rangle = 0
$$

$$
\left\langle y^M, DM_{\theta^*}[\delta \theta] \right\rangle + \left\langle y^I, \mathcal{I}(DM_{\theta^*}[\delta \theta], D) \right\rangle = 0
$$

$$
\left\langle y^M, DO(\hat{D})[\delta \hat{D}] \right\rangle = 0
$$
 $(16)$ 

Additional complications arise with Bayesian methods for computing  $\theta(\omega)$ , which are beyond the scope of the current work.

With these optimality conditions in place, we can now consider different algorithms for DD as targeting the satisfaction of these equations with varying levels of precision or approximation.

<span id="page-12-0"></span>

## 5 Classical Methods

### 5.1 DD by Trajectory Matching

We highlight Trajectory Matching because it appeared to be the most appropriate for analyzing within our framework, and furthermore it is generally considered the state of the art as far as numerical performance, *e.g.*, [Cazenavette et al.](#page-25-0) [\(2022\)](#page-25-0); [Du et al.](#page-26-6) [\(2023\)](#page-26-6); [Liu et al.](#page-27-7) [\(2024\)](#page-27-7).

Consider the problem of finding a trajectory matching dataset  $\tilde{D}$  defined in terms of SDE dynamics. The neural network model is given generically by the loss with respect to the parameter and data as  $f(\theta, D)$ .

One possibility is to study the optimization problem of matching the gradients of the flows:

$$
\min_{\tilde{D}} \mathbb{E} \int_0^T \left\| \nabla f(\theta_t, D) - \nabla f(\hat{\theta}_t, \tilde{D}) \right\|^2 dt
$$

$$
\text{s.t.} \quad d\theta_t = -\nabla f(\theta, D)dt + dW_t,
$$

$$
d\hat{\theta}_t = -\nabla f(\theta, \tilde{D})dt
$$

$$
(17)
$$

Note that this is an optimization not control problem, i.e.,  $\tilde{D}$  is time independent. Also  $\hat{\theta}$  has deterministic dynamics since gradient descent can be performed with the full dataset on the smaller synthetic data.

Writing the optimality conditions, we see:

$$
\mathbb{E}\int_0^T \nabla_{\theta D}^2 f(\hat{\theta}_t, \tilde{D}) \left( \nabla f(\theta_t, D) - \nabla f(\hat{\theta}_t, \tilde{D}) \right) dt - \int_0^T \nabla_{\theta D}^2 f(\hat{\theta}_t, \tilde{D}) \lambda_t dt = 0
$$

from which follows, using Fubini's theorem,

$$
\int_0^T \nabla_{\theta D}^2 f(\hat{\theta}_t, \tilde{D}) \mathbb{E}_D \left( \nabla f(\theta_t, D) - \nabla f(\hat{\theta}_t, \tilde{D}) - \lambda_t \right) dt = 0
$$
  
$$
\int_0^T \nabla_{\theta D}^2 f(\hat{\theta}_t, \tilde{D}) \left( \mathbb{E}[\nabla f(\theta_t, D)] - \nabla f(\hat{\theta}_t, \tilde{D}) - \lambda_t \right) dt = 0
$$

Alternatively we can consider not matching the gradients but the actual trajectory eschewing random operators entirely by working with the Fokker Planck equation defining the distributional dynamics of  $\theta_t$ , defined by  $\rho(\theta, t)$ . This can done with a cosine similarity functional of the form:

<span id="page-12-1"></span>min

$$
\int_0^T \int_{\theta} \sigma_{\cos} \left( \frac{\partial \rho(\theta, t)}{\partial t}, (\theta - \hat{\theta}_t) \odot \frac{d\hat{\theta}_t}{dt} \right) d\theta dt
$$
s.t.

$$
\frac{\partial \rho(\theta, t)}{\partial t} = -\nabla \cdot (\nabla f(\theta, D)) + \Delta \rho(\theta, t)
$$

$$
\frac{d\hat{\theta}_t}{dt} = -\nabla f(\theta, \tilde{D}) \quad (18)
$$

With optimality conditions,

$$
\int_0^T \int_{\theta} \nabla_{\theta}^2 f(\hat{\theta}_t, \tilde{D}) \odot (\theta - \hat{\theta}_t) \odot \sigma'_{cos,(2)} \left( \frac{\partial \rho(\theta, t)}{\partial t}, -(\theta - \hat{\theta}_t) \odot \nabla f(\hat{\theta}_t, \tilde{D}) \right) d\theta dt = 0
$$

We can specify  $f$  to be more specifically, in the standard mean field training regime notation [Mei](#page-27-8) [et al.](#page-27-8) [\(2018\)](#page-27-8),

$$
f(\theta, \rho_t) = V(\theta) + \int U(\theta, \theta') \rho_t(d\theta'), V(\theta) = -\mathbb{E}[y\sigma(x; \theta)], U(\theta, \theta') = \mathbb{E}[\sigma(x; \theta)\sigma(x; \theta')]
$$

where  $\sigma$  is the neural network model. The results of [Mei et al.](#page-27-8) [\(2018\)](#page-27-8) can be combined with the level of accuracy achieved in reconstructing the trajectory to obtain similar trajectory bounds with respect optimal distributional dynamics.

Point of View from Stability Another possible perspective can be derived from [Ho et al.](#page-26-7) [\(2020\)](#page-26-7).

In this work they consider instead of the standard empirical risk minimization and generalization decomposition, with  $G$  the iteration

$$
G_k^t(\theta^0) - \theta^* = G_k^t(\theta^0) - \hat{\theta}_{ERM} + \hat{\theta}_{ERM} - \theta^*
$$

one analyzes a version of the algorithm with full data information, and perturbations from that,

$$
G_N^t(\theta^0) - \theta^* = G^t(\theta^0) - \theta^* + G_N^t(\theta^0) - G^t(\theta^0)
$$

and proceed to study algorithms to classify them as *fast* or *slow* and *stable* or *unstable* based on the optimization convergence rate for the full data dynamics  $G^t - \theta^*$  and the stable tracking of the empirical dynamics to the full data dynamics  $G^t - G_N^t$ . It appears that DD is directly aiming to approximate the second term,

Relation To DD Optimization Problem Ultimately it is clear that we can consider this to be attempting to satisfy the condition:

<span id="page-13-0"></span>
$$
O(\hat{D}) = O(\bar{D}) \approx O(D) \tag{19}
$$

where  $\bar{D}$  is a large training set. We observe that if the optimization process O is such that,

$$
O(D) \subseteq \arg\min_{\theta} \mathcal{R} \{ \mathcal{DI}(M_{\theta}[O(D)], D_X) || \mathcal{I}(D, D) \}
$$

and moreover, that in the overparametrized distributional regime [Simsek et al.](#page-28-9) [\(2021\)](#page-28-9),

$$
\min_{\theta} \mathcal{R}\left\{ \mathcal{DI}(M_{\theta}[O(D)], D_X) || \mathcal{I}(D, D) \right\} = 0
$$

then the choice of targeting [\(19\)](#page-13-0) is reasonable for the original problem.

The fact that we are performing this on a training set means that we can understand algorithmic trajectory matching, *e.g.*, as [Cazenavette et al.](#page-25-0) [\(2022\)](#page-25-0), as Sample Average Approximation (SAA) applied to [\(19\)](#page-13-0). That is, the algorithms are discretizations of [\(18\)](#page-12-1).

Notice that one can alternatively solve the stochastic optimal control problem directly. The accuracy of the solution to the distributional PDE control formulation [\(18\)](#page-12-1) depends on the error of discretizing the optimality conditions of this system. The weak form suggests continuously differentiable finite elements can be used, and we can get an error rate of the order of  $O(h^2)$ , with  $h$  the grid size.

### 5.2 Other Methods

DD by Distribution Matching Distribution matching for DD, introduced in [Zhao and Bilen](#page-29-4) [\(2023\)](#page-29-4), amounts to using some metric of distance to directly optimize  $\hat{D}$  as proximity to  $\hat{D}$ , and in practice some finite training set  $\mathcal{T} \subset \mathcal{D}$ . This is performed by applying an encoder to generate latent code for the data samples and comparing the expected difference,

<span id="page-13-1"></span>
$$
\mathbb{E}\left\|\psi(\hat{z}) - \psi(z)\right\|^2, \, \hat{z} \in \hat{D}, \, z \sim D \tag{20}
$$

Let us consider this strategy in light of the optimization problem [\(2\)](#page-4-0). Notice that if  $\hat{D} = D$ , then the inference model would be one that is optimized for D, i.e.,  $\hat{I} = \mathcal{I}(O(D), D)$ , and so blundly it would appear that this is a generically effective technique. Furthermore, the use of a latent encoder suggests that a model from which the encoded value is potentially interpretable or explainable in some way.

However, this does not take into account the inference quality of interest. Given the small dataset size, we can expect the minimization of distance solution [\(20\)](#page-13-1) to have many minima. Knowledge as to the intention of the DD can properly refine the solution to be appropriate for that task. As far as a versatile strategy wherein we can see the error in the estimate as:

$$
\mathcal{I}(O(D), D) - \mathcal{I}(O(\hat{D}), D) \approx \mathcal{I}(O(D), D) + D^1 \mathcal{I}(O(D), D)[O'(D)](\hat{D} - D)
$$

that is, with an error associated to the data the optimizer works with by the optimmizers' and inference operator's sensitivity.

Factorization Methods Factorization methods [Liu et al.](#page-27-9) [\(2022\)](#page-27-9) are a recent popular competitive method, without much theoretical intuition provided. They consider the synthetic dataset to be simply a factorization wherein the original data is represented in a lower dimensional basis. This makes sense from the Neural Tangent Kernel (NTK) point of view [Jacot et al.](#page-26-8) [\(2018\)](#page-26-8). That is, with  $\hat{D} = b\hat{c}$  with  $||b\hat{c} - D||$  minimized in some sense, then we can expect an optimizer will effectively project the gradient flow in the space of b. If the basis is chosen suitably to target  $\mathcal{I}$ , can result in the desired training performance.

Recent Work and Methodological Insights Finally, here we shall review recent variations of existing methods and state of the art work as of writing, and attempt to provide brief insight into these methods through our framework.

As stated previously, mainstream dataset distillation methods can be roughly categorized into matching-based ones and bi-level optimization ones, *e.g.*, [Zhao et al.](#page-29-2) [\(2020\)](#page-29-2); [Zhao and Bilen](#page-29-4) [\(2023\)](#page-29-4); [Cazenavette et al.](#page-25-0) [\(2022\)](#page-25-0); [Nguyen et al.](#page-28-0) [\(2020\)](#page-28-0); [Loo et al.](#page-27-3) [\(2022\)](#page-27-3). For matching-based methods, recent explorations mainly focus on designing better criterion, such that the synthetic data can better reflect the characteristics of original data. [Zhang et al.](#page-29-8) [\(2024\)](#page-29-8), [Shang et al.](#page-28-10) [\(2023\)](#page-28-10) and [Deng](#page-26-9) [et al.](#page-26-9) [\(2024\)](#page-26-9) adopt Maximum Mean Discrepancy, mutual information and sample relationship to better fit the original distribution, respectively. These can be understood to establish proxy variables for the inference task of interest  $\mathcal I$  into the otherwise completely agnostic distributional matching techniques, thus ameliorating the lack of steering problem described.

[Liu et al.](#page-27-7) [\(2024\)](#page-27-7) dynamically select trajectories with proper length to optimize the synthetic data. There are also works aiming at selecting informative samples for matching, *e.g.*, [Liu et al.](#page-27-2) [\(2023\)](#page-27-2); [Xu et al.](#page-29-9) [\(2024\)](#page-29-9). These can be readily understood to improve the accuracy of the approximation  $O(D) \approx O(D)$ .

Regarding bi-level optimization methods, meta-learning is incorporated into the surrogate image update process, using validation performance as a direct optimization target to enhance the quality of distilled datasets. This approach has been explored extensively in recent works, where nested optimization loops are used to iteratively refine synthetic datasets while fine-tuning model parameters to achieve optimal performance on validation tasks, *e.g.*, [Zhou et al.](#page-29-5) [\(2022\)](#page-29-5); [Loo et al.](#page-27-10) [\(2023\)](#page-27-10). This can be understood to be ameliorating the vacuous solution problem identified with the bilevel approach at the beginning of our paper. By iteratively incorporating validation datasets, generalization is directly targeted, ensuring that at least for the purposes of representing data to optimize a model's population loss the technique is effective.

In addition to the above works, there are also methods designed from generative perspectives, *e.g.*, [Cazenavette et al.](#page-25-5) [\(2023\)](#page-25-5); [Gu et al.](#page-26-10) [\(2024a\)](#page-26-10); [Su et al.](#page-28-11) [\(2024\)](#page-28-11). Through introducing generative prior, the generated images demonstrates better cross-architecture generalization and interpretability. Efforts also include dataset parametrization, enabling more information in the restricted dataset size, *e.g.*, [Kim et al.](#page-27-1) [\(2022\)](#page-27-1); [Wei et al.](#page-29-10) [\(2023\)](#page-29-10); [Liu et al.](#page-27-9) [\(2022\)](#page-27-9). A generative prior corresponds to  $\omega$  including some previously learned components to bias the model  $M_{(\omega)}$ . Thus background knowledge can be seen to easily fit into the framework.

While dataset distillation achieves certain progresses in constructing small surrogate datasets, it is further applied to many other research topics, such as continual learning and federated learning, *e.g.*, [Gu et al.](#page-26-11) [\(2024b\)](#page-26-11); [Jia et al.](#page-26-12) [\(2023\)](#page-26-12); [Yang et al.](#page-29-11) [\(2023\)](#page-29-11); [Xiong et al.](#page-29-12) [\(2023\)](#page-29-12); [Wang et al.](#page-29-13) [\(2024\)](#page-29-13). The trustworthy issue is also broadly focused to make dataset distillation more practical in realworld scenarios. [Vahidian et al.](#page-29-7) [\(2024\)](#page-29-7) involve a group distributionally robust optimization method to enhance the robustness of distilled data. [Zhu et al.](#page-30-0) [\(2023\)](#page-30-0) mitigates the information loss during the distillation process, and better calibrate the data and finally [Lu et al.](#page-27-11) [\(2024\)](#page-27-11) investigates the influence of dataset bias on DD. These can be all understood as 1) acknowledging the fact that  $\mathcal T$  is just a sample of  $D$  and adopting measures of robustness to mitigate some of the practical consequences of sampling error and 2) adopting (potentially composite) criteria  $\mathcal I$  to target particular secondary objectives, and 3) developing tools to improve the approximation  $O(D) \approx O(D)$ .

<span id="page-15-0"></span>

# 6 Mixture Extended AutoRegressive Model and an Invitation of DD for Categorical Data

Recall the Autoregressive Model given as the example at the end of Section [3](#page-6-0)

$$
\min_{\hat{D}, \hat{I}, \theta(\omega)} \quad \left\| \hat{I} - \mathcal{T}_{t+1} \right\|^2
$$

$$
\text{subject to} \quad \mathbf{m} = O_{EAR}\left(\hat{D}\right), \mathbf{m} \in \Delta^m
$$

$$
\hat{I} = \mathcal{I}(\sum_{Q} \mathbf{m}_q \mathbf{M}_q, \mathcal{T}_t)
$$

We now describe how to compute  $O_{EAR}$  with counting operations on the transitions to obtain a model  $\sum^m$  $\sum_{q}^{n} \mathbf{m}_q(\hat{D}) \mathbf{M}_q$  of mixture weights.

We consider that there are 3 states  $\mathbb{Z}/3\mathbb{Z} := \{0, 1, 2\}$  for all *n* features and *N* data observations.

$$
\hat{D} = \{z_{i,l}\} \subset (\mathbb{Z}/3\mathbb{Z})^{nN}
$$

Now consider that there is an unknown Markov transition matrix M that describes the evolution of D and that this matrix is of the form:

$$
\mathbf{M} = \mathbf{m} \cdot \left[ \mathbf{M}^{(1)}, ..., \mathbf{M}^{(m)} \right]
$$

i.e., it is a linear combination defined by a mixture  $\mathbf{m} \in \Delta^m$  coefficient combination of the set of matrices  $\{M_i\}$ . Let  $\bar{n} := 3^n$ . Each matrix is valued  $\bar{n} \times \bar{n}$  and is row stochastic.

We initialize m to be a uniform Dirichlet prior, i.e., with

$$
\vec{\alpha} = {\alpha}^m, \, \alpha = 1/m
$$

We obtain a prior:

$$
\pi(\mathbf{m}) = \frac{1}{\Gamma(\alpha)^m} \prod_{q=1}^m [\mathbf{m}]_q^{\alpha_q - 1}
$$

We can make this a prior on the transition matrix itself for a given row  $i$ :

$$
\pi(\mathbf{M}_{i}) = \frac{1}{\Gamma(\alpha)^{m}} \prod_{j=1}^{\bar{n}} \left\{ \sum_{q=1}^{m} \left( [\mathbf{m}]_{q}^{\alpha_{q}-1} \mathbf{M}_{ij}^{(q)} \right) \right\}
$$

This is conjugate to the prior for the categorical distribution. Thus upon obtaining  $\hat{D}$  we can obtain a posterior estimate of the distribution. Consider some natural isomorphism  $\phi$  (*e.g.*, lexicographic) from  $\{1, ..., \bar{n}\}$  to the permutations of Perm  $\{(\mathbb{Z}/3\mathbb{Z})^n\}$ . Indeed, the update can be defined as:

$$
p(\mathbf{M}_{i:}|\hat{D}) \propto p(\hat{D}|\mathbf{M}_{i:})\pi(\mathbf{M}_{i:}) = \frac{1}{\Gamma(\alpha)^m} \prod_{j=1}^{\bar{n}} \mathbf{M}_{i,j}^{c_{i,j}(\hat{D})} \left( \sum_{q} \delta_{\mathbf{M}_{i,j}^{(q)}} (\alpha_q - 1) \right), \text { where,}
$$
  
$$
c_{i,j}(\hat{D}) = \frac{\sum_{l}^{N} \sum_{i,j=1}^{\bar{n},\bar{n}} \mathbf{1}((z_{l,t}, z_{l,t+1}) = (\phi(i), \phi(j)))}{\sum_{l}^{N} \sum_{i,j=1}^{\bar{n},\bar{n}} \mathbf{1}(z_{l,t} = \phi(i))}
$$

To compute the posterior mixture weights we have,

$$
p(\mathbf{m}|\hat{D}) = \int p(\mathbf{m}|\mathbf{M})p(\mathbf{M}|\hat{D})d\mathbf{M} = \int p(\mathbf{M}|\mathbf{m})\pi(\mathbf{m})p(\mathbf{M}|\hat{D})d\mathbf{M}
$$
  
 
$$
\propto \frac{1}{\Gamma(\alpha)^m} \prod_{q=1}^m [\mathbf{m}]_q^{\alpha_q - (\sum_{ij} \mathbb{M}_{ij}^{(q)} - c_{ij})/\bar{n}^2 - 1}
$$

Now we take the model and obtain the forecast on the training data  $\hat{I} = \sum_{i=1}^{I} m_i(\hat{D}) \Theta_i^T X_{\mathcal{T}_t}$  and compute the reconstruction loss to the real future in the training  $X_{\mathcal{T}_{t+1}}$ . It can be see that the closer the empirical frequencies of the training and distilled data are, the smaller the loss.

Considering a more robust criteria, we can consider formulating an objective that recovers a particular marginal inquiry regarding  $M_{ij}$  accuracy robust with error in the training data. This creates a more complex, bilevel, integer optimization problem.

<span id="page-16-0"></span>

## 7 Case Study Description: DD to construct PGMs for Medical Models

Medical data, broadly speaking, has continued to be analyzed primarily by methods of classic statistics due to the fact that the structure of the data is usually the opposite of the setting amenable to deep learning approaches. Specifically, the number of training samples is generally rather small while the information content of any one sample can be substantial, introducing significant underdeterminacy as far as any attempt to develop an accurate model. It is especially difficult and expensive to establish an experimental and control cohort for clinical trials. Observational studies are more feasible with greater sample size, but often still require inconvenient or even invasive measurement, making recruitment difficult and data collection expensive. By contrast, the rapid growth in the precision of NMR and other technology together with growing biochemical understanding of the human body has created the potential of surveying very detailed *omics* profiles for individuals. In this section, we consider a procedure whereby one can augment a dataset one is working with by systematically constructing additional data from existing data banks and previous research. The problem with

simply combining external data directly is often the heterogeneous nature of the choice of features chosen. That is, one dataset can have  $X$  and  $Y$ , another  $Y$  and  $Z$ , and yet another  $Z$  and  $X$ . Imputing missing values can result in significant imprecision and error. However, with DD we can construct a complete dataset that has the most relevant feature information accross disparate datasets.

Consider that to begin with there are a number of training datasets available  $\{\mathcal{T}_n\}$  where each  $\mathcal{T}_n$  may have missing data for one or more components. Using this entire collection, the goal is to construct a new dataset  $\hat{D}$ , for which every sample is complete in information. There are a set I of inference tasks and for each i, the map  $\iota(i)$  defines the marginal distribution  $D(\iota(i))$  associated with that task. For instance, given a person has  $a$  background characteristic, and the  $b$  covariate is at a value of 4.3, what is the probability of a particular biomarker for a disease of interest being present.

We present  $O_{PGM(i)}(\cdot)$  as the operator to take a dataset and make a probabilistic graphical model for which the task  $i$  is tractable and interpretable. If we collapse the union to just  $i$ , one evaluation of [\(9\)](#page-8-0) would correspond to the PGM built to form a (possibly Bayesian) model  $M_{\theta(\omega)}$ . This model would perform the  $i$ -associated inference task on some sample from the distribution and compare it to the ground truth.

We describe our procedure specialized for this problem. At each iteration  $k$  we start with the current form of the synthetic dataset  $D$  and sample uniformly at random some inference task i. Each inference task will involve conditioning on some variables, and query on another variable, with the rest as nuisance quantities. A training set will be subsampled from  $\{D_n\}$ , with a weighting of one for all  $D_n$  such that all of the conditioned and query variables are present in  $D_n$ , and then a fraction weight corresponding to the proportion available for the others. Denote the subsampled training set at  $\bar{\mathcal{T}}_k$ . A (noisy) evaluation can be performed by learning the model  $PGM(i)$  and evaluating the inference query on the model to the query on the subsample.

Note that in this case there are a number of methods to train a PGM, many of which without gradient trajectories, and also that without a neural network other approaches, such as based on NTK, are not appropriate. At the same time, a gradient of the original problem is not analytically available. We propose a zero order approximation to the gradient, using a classic two point function approximation rule.

**Zero Order Optimization** We formally detail the first procedure. Algorithm [1](#page-18-0) describes an evaluation of the objective of interest with respect to the input decision variable, the synthetic dataset  $\ddot{D}$ .

In this algorithm, which requires an inference task i and subsample  $\overline{T}$ , the first step is to train a Probabilistic Graphical Model on the synthetic dataset. This returns a model defined as  $M_{\theta(\omega)}$ . In the numerical experiments, we use Dynamic Bayesian Networks, DBNs.

Subsequently, we evaluate the model  $M_{\theta(\omega)}$  on the inference task as performed on the training subsample  $\bar{\mathcal{T}}$ . Finally, a probability distance is computed between the computed inference task  $\bar{I}$  and the inference task ground truth from some validation set  $I(D_{(i)})$ . Some statistical operation across the training subsample is performed, whether an expectation, or a risk measure like Conditional Value at Risk (CVaR).

<span id="page-18-0"></span>

#### Algorithm 1 DD Loss Evaluation

**Input:** Current synthetic dataset  $\hat{D}$ . Inference task  $i \in I$ . Subsample  $\overline{T}$ , hold out validation set  $V$ 

- 1: Compute  $M_{\theta(\omega)} = O_{PGM(i)}(\hat{D})$
- 2: Compute  $\hat{I} = \mathcal{I}(M_{\theta(\omega)}, \bar{\mathcal{T}}_{i_i})$  using EM to impute missing data, where  $\bar{\mathcal{T}}_{i_i}$  refers to the training data relevant to the calculation of the inference task.
- 3: Return the value of,

$$
\mathcal{R}_{\mathcal{T}_{\iota(i)}}\left[\mathcal{D}_{\theta(\omega)}\left(\hat{I}||I(\mathcal{V}_{(i)})\right)\right]
$$

where  $I(V_{(i)})$  defines the inference procedure on the validation set.

In order to compute [\(22\)](#page-18-1) we can consider two classes procedures at either end of the variance accuracy tradeoff spectrum in zero order or derivative free optimization (see, *e.g.* [Berahas et al.](#page-25-6) [\(2022\)](#page-25-6)).

The first, based on sparse noisy function evaluations, is common in machine learning. The *Two Point Function Approximation* Estimate is given by:

<span id="page-18-3"></span>
$$
g_k = \frac{1}{M} \sum_{l=1}^{M} \frac{L(\hat{D} + \sigma v_l, i) - L(\hat{D}_k, i)}{\sigma} v_l
$$
 (21)

with  $v_l \sim \mathcal{N}(0, I)$ , where  $L(\hat{D})$  is the evaluation given by Algorithm [1](#page-18-0) with inputs  $\hat{D}$  and i.

First Order Algorithm The actual optimization procedure is defined in Algorithm [2](#page-18-2)

<span id="page-18-2"></span>

| <b>Algorithm 2 Optimization Loop</b>                                                                                              |  |
|-----------------------------------------------------------------------------------------------------------------------------------|--|
| <b>Input:</b> Training set $\mathcal T$ and set of inference tasks $I$                                                            |  |
| 1: for $k = 0, 1, 2, \dots$ do                                                                                                    |  |
| 2:     Sample $i \in I$                                                                                                           |  |
| 3:     Subsample, with importance weighing on datasets without missing features, $\overline{\mathcal{T}}_k \subset \mathcal{T}$ . |  |

4: Compute an estimate of the gradient of  $L$  with respect to the synthetic dataset:

<span id="page-18-1"></span>
$$
g_k \approx \nabla L(\hat{D}_k) \tag{22}
$$

5: Set stepsize s (diminishing stepsize rule, or stepwise decay). For instance:

$$
s = 0.1/\sqrt{1+k}
$$

or a stepwise decay

6: Set  $\hat{D}_k - s g_k \to \hat{D}_{k+1}$ 7: end for

Note that alternatively, when the dimensionality isn't prohibitive, a more accurate and less noisy estimate can be performed, such as the one in commonly used in Derivative Free Optimization (DFO) with schemes to obtain accuracy with high probability [Larson and Billups](#page-27-12) [\(2016\)](#page-27-12). We found the performance of the zero order approximator was superior in this case, however.

To illustrate the proof of concept we will perform the evaluation of the inference task of a test likelihood. That is  $\mathcal I$  is the total likelihood and  $\mathcal R$  is taken to be

<span id="page-19-0"></span>Image /page/19/Figure/1 description: This figure illustrates a two-part process for data generation and analysis. Part (a) shows the generation of training data. It begins with a randomly generated Deep Belief Network (DBN) with nodes labeled x0, x1, x2, and x3, depicting relationships between them. Samples are drawn from this DBN to create clean data, which is then split into multiple sets. These sets are then subjected to a 'Hide' and 'add noise' process, resulting in the final training data, denoted as {DT}. Part (b) details the synthetic data generation and retrieval process. The training data {DT} is used to initialize synthetic data, denoted as {DjS}. This synthetic data is then sampled and augmented with Gaussian noise to create DjS + delta. Both the original sampled synthetic data and the noise-added data are fed into a 'dmmhc' process, which results in a 'Fitted DBN'. The output of these fitted DBNs is then used to calculate a Log-Likelihood, producing scores S0 and S1. The process also involves a 'retrieval' step where other training data sets {DT | i != j} are used.

Figure 1: (a) We generate datasets by sampling from a randomly initailized DBN. To mimic heterogeneity of medical data, we split the sampled clean data to  $K$  partitions. For each partition, we hide the values of different variables and add Gaussian noise to each entry. (b) To distill the synthetic data more effectively, we initialize  $D<sup>S</sup>$  from each source of training data. During each epoch, we sample a part  $D_j^S$  from  $D^S$ , add Gaussian noise  $\delta$  to it, learn two different DBNs from them respectively. Then retrieve training data  $\{D_i^T \mid i \neq j\}$ . Finally we compute the log-likelihood of  $D_j^S$  in two different DBNs as the evaluation score, to calculate the gradient with respect to  $D_j^S$ .

#### 7.1 Experimental Setup

Dataset: As illustrated in Figure [1,](#page-19-0) we use a randomly generated Gaussian Dynamic Bayesian Network (GDBN) to generate training and testing datasets. Source nodes (indegree equal to 0) are Gaussian nodes with a mean value of 0 and a standard deviation of 0.5 . The mean values of intermediate nodes and target nodes are linear combinations of parent nodes, while the standard deviation is also set to 0.5. For computational efficiency, the GDBN consists of 20 variables and 2 time-slices. We sample 1000 observations from the GDBN as the testing dataset  $D^{Test}$ . For training dataset, we firstly sample 1000 observations, then split them to 5 partitions. For each partition,  $10\%$ of the variables are randomly hidden, and Gaussian noise is added independently, to mimic the heterogeneity of medical datasets from different data sources. We also investigate the performance of our algorithm when scaling up time slices. Specifically, we test our algorithm in 20 variables and [1](#page-19-1)0 time-slices setting<sup>1</sup>, keeping other experimental settings the same.

**Evaluation and Baseline:** After distilling the synthetic dataset  $\hat{D}$ , we use  $O_{PGM}(\hat{D})$  to fit a GDBN  $M_{\theta(\omega)}$ . The Log-Likelihood of  $D^{Test}$  on  $M_{\theta(\omega)}$  is computed as the evaluation metric. For comparison, we randomly sample the same quantity of fully-observed clean observations  $D^{sub}$  from training dataset before hiding variables and add noise to it. It is a challenging baseline because  $D^{sub}$  is fully-observed with no noise, while our algorithm starts with partially observed dataset with Gaussian noise. We use IPC to denote quantity of observations in synthetic dataset and subset of

<span id="page-19-1"></span><sup>1.</sup> The experiment of scaling to more variables and time slices is prohibitively time-consuming because of bi-level optimization nature of DD and structure learning algorithm  $O_{PGM}(\hat{D})$ , then we only scale time-slices from 2 to 10 in this paper.

<span id="page-20-0"></span>Image /page/20/Figure/1 description: The image is a line graph showing the log-likelihood of test data on the y-axis and IPC on the x-axis. There are two lines plotted: one for synthetic data (blue) and one for original data (green). A dashed red line represents the full-set. The synthetic data line starts at approximately -44000 at IPC=10, rises to -40000 at IPC=15, then to -38500 at IPC=50, and finally to -37000 at IPC=100. The original data line starts at -58000 at IPC=10, rises sharply to -42000 at IPC=15, then to -38000 at IPC=50, and finally to -37000 at IPC=100. The full-set line is a horizontal dashed red line at -35000.

Figure 2: Performance of our algorithm in 4 different IPC settings. Across 4 settings, our algorithm is much better than fully-observed clean data  $D^{sub}$ , especially in low-data regime. When IPC=100, our algorithm is almost as effective as the complete and clean training dataset.

<span id="page-20-1"></span>Image /page/20/Figure/3 description: The image displays four plots, labeled (a), (b), (c), and (d), each illustrating the log-likelihood of test data against the number of epochs. Plot (a), titled "IPC=10", shows the log-likelihood increasing from approximately -58000 to -42500 over 160 epochs, with a dashed red line indicating the original data's log-likelihood at -57500. Plot (b), titled "IPC=20", shows the log-likelihood increasing from approximately -48000 to -40000 over 1000 epochs, with the original data's log-likelihood at -42500. Plot (c), titled "IPC=50", shows the log-likelihood increasing from approximately -40500 to -38000 over 4000 epochs, with the original data's log-likelihood at -38500. Plot (d), titled "IPC=100", shows the log-likelihood increasing from approximately -38000 to -36500 over 5000 epochs, with the original data's log-likelihood at -36500. All plots show a trend of increasing log-likelihood for synthetic data as epochs progress, generally surpassing the original data's log-likelihood in plots (b), (c), and (d).

Figure 3: The Log-Likelihood (LL) curves in testing set  $D^{Test}$  of 4 different IPC settings. We evaluate the synthetic dataset  $\hat{D}$  after each distillation process and report LL, with comparison to  $D<sup>sub</sup>$ 

training dataset in baseline. For both our algorithm and baseline, we investigate 4 IPC settings: 10, 20, 50, 100 . We also test the performance of the complete fully-observaed clean training set as the upper bound.

<span id="page-21-0"></span>Image /page/21/Figure/1 description: The image is a line graph plotting the log-likelihood of test data against IPC. The x-axis is labeled 'IPC' and ranges from 0 to 100. The y-axis is labeled 'Log-Likelihood of test data' and ranges from -240000 to -140000. There are two lines plotted: one in teal representing 'synthetic data' and one in dark green representing 'original data'. Both lines show an increasing trend as IPC increases. A dashed red line labeled 'full-set' is also present, indicating a reference log-likelihood value. The 'synthetic data' line starts at approximately -178000 for IPC=10, rises to about -158000 for IPC=20, then to -156000 for IPC=50, and finally to -155000 for IPC=100. The 'original data' line starts at approximately -235000 for IPC=10, rises to -180000 for IPC=20, then to -160000 for IPC=50, and finally to -157000 for IPC=100. The 'full-set' line is a horizontal dashed line at approximately -154000.

Figure 4: Performance of our algorithm when scaling up time-slices from 2 to 10. Across 4 IPC settings, our algorithm is much better than baseline.

**Implementation Details** For approximating the gradient of objective with respect to  $\hat{D}$  with lower variance, we set M as 10 in [\(21\)](#page-18-3). Optimization iterations is set to 160, 1000, 4000, 5500 for IPC 10, 20, 50, 100 respectively. The learning rate is kept as 4e-5 along the distillation process without scheduling and special optimizer. We initialize  $D$  with 5 sources of training dataset, with missing values sampled from Gaussian, for we observed that it converge faster and performs better than purely initializing D from Gaussian. The  $O_{PGM}(D)$  operator and LL computation are adopted from dbnR in [Quesada](#page-28-12) [\(2024\)](#page-28-12).

##### 7.2 Numerical Results

**Main Results:** We compare our algorithm with baseline in 4 different IPC settings. As illustrated in Figure [2,](#page-20-0) across 4 settings, our algorithm illustrates significant superiority over even the fullyobserved clean data  $D^{sub}$ , especially in low-data regime. When IPC=100, our algorithm is almost as effective as the complete and clean training dataset. However, when IPC gets larger, the marginal rate of performance increase of both our algorithm and  $D^{sub}$  declines. This is like to be because of the growing redundancy in  $\hat{D}$  and  $D^{sub}$ . The training curves of different IPC settings during the distillation process are shown in Figure [3.](#page-20-1) Although faced with Gaussian noise, missing values in training data and the black-box optimization nature of the algorithm, the training curves show steady gradient and performance growth and outperform the strong baseline across 4 different settings.

**Scaling Up Time-slices** Medical data typically has many time-slices. To test the scalability of our algorithm, we scale time-slices from 2 to 10, keeping other settings the same. As shown in Figure [4,](#page-21-0) the synthetic data is again even better than the fully-observed clean data across all settings. From IPC=20 to larger quantities of synthetic data, its performance is very close to original full training dataset, indicating the consistent scalability of our algorithm. However, we observed that time consumption of DD grows linear with time-slices, which is intuitive because the outer loop evaluation has to compute values of missing variables and calculate LL slice by slice. How to scale to even larger problem dimension, sample size, and time-slices is beyond the scope of this work and can be tackled in the future.

<span id="page-22-1"></span>Image /page/22/Figure/1 description: This is a flowchart illustrating a process involving a population prior, a PDE solver, and a domain. The population prior, represented by a bell curve with 'train' and 'test' regions indicated by dashed lines and arrows, generates samples denoted by alpha (× P). These samples are fed into a PDE solver, which also receives input from a 'PDE' block. The PDE solver produces a 'Solution'. Separately, the 'domain', depicted as a circle with x and y axes, is evenly sampled N times to produce points xi. The 'Solution' is then used to generate yi, which are then modified by adding noise to produce y-tilde i. Finally, the data is presented as pairs of (xi, y-tilde i).

Figure 5: Training and test dataset generation of PINN example. To demonstrate the out-ofdistribution generalization ability of our algorithm, boundary conditions  $(\alpha)$  of training and test dataset are sampled from two opposite tails of normal distribution. For training dataset, noise is added to ground truth  $y_i$  to mimic measurement error, while for test dataset, none noise is added to  $y_i$ 

<span id="page-22-0"></span>

## 8 Case Study Description: DD to Construct Boundary-Generalizable PINNs

Recall the PDE loss function given in Section [3](#page-6-0)

$$
\mathcal{L}_F(\theta_u, \theta_\gamma; \{x\}, \hat{f}) = \frac{1}{MN} \sum_{m=1}^M \sum_{l=1}^N (\mathcal{F}(\bar{u}_l(\theta, x_m), \bar{\gamma}_l(\theta, x_m)) - \hat{f}_{ml})^2
$$

and the bilevel optimization problem defining the DD procedure:

$$
\min_{\hat{D}, \theta} \quad \mathbf{CVaR}_{\pi(u_{\partial\Omega})} [\mathcal{L}_F(\theta_u, \theta_\gamma; D)]
$$

$$
\text{subject to} \quad \theta = \arg\min_{\mathcal{L}_F(\theta_u, \theta_\gamma; \hat{D})} \mathcal{L}_F(\theta_u, \theta_\gamma; \hat{D})
$$

$$
\text{s.t. } u(x_{\partial\Omega}, \theta, g) = g(x_{\partial\Omega}) \sim \hat{D}_g
$$

.

We further design corresponding experiments on PINN to demonstrate the effectiveness of our proposed dataset distillation scheme.

### 8.1 Experimental Setup

Dataset We deal with Laplace equation on a disk with Dirichlet boundary conditions:

$$
r\frac{dy}{dr} + r^2 \frac{d^2y}{dr^2} + \frac{d^2y}{d\theta^2} = 0, \quad r \in [0, 1], \quad \theta \in [0, 2\pi]
$$
$$
y(1, \theta) = \cos(\theta) + \alpha
$$
$$
y(r, \theta + 2\pi) = y(r, \theta)
$$

As demonstrated in Figure [5,](#page-22-1) we sample boundary conditions  $\alpha$  from two opposite tails of population prior  $\mathcal{N}(0, 1)$ , to verify the out of distribution (OOD) generalization ability of our algorithm. Specifically,  $\alpha_{train}$  is restricted to be less than  $Q(a)$  and  $\alpha_{test}$  is restricted to be greater than

<span id="page-23-1"></span>

|      |               | <b>IPC</b> |        |        |        |
|------|---------------|------------|--------|--------|--------|
|      | <b>Budget</b> | 10         | 20     | 40     | 80     |
|      | 5             | 0.1310     | 0.1056 | 0.1288 | 0.1419 |
|      | 10            | 0.1752     | 0.0995 | 0.0595 | 0.0726 |
| Ours | 20            | 0.1258     | 0.1248 | 0.0346 | 0.0186 |
|      | 40            | 0.8427     | 0.0973 | 0.1794 | 0.0320 |
|      | 100           | 2.1470     | 0.0602 | 0.0417 | 0.0307 |
|      | 200           | 0.8130     | 0.1609 | 0.0801 | 0.0506 |
|      | Subset        | 0.2003     | 0.1167 | 0.1008 | 0.0552 |

Table 1: Comparison of our algorithm with baseline in PINN example. Across 4 IPC settings, ours has smaller prediction error than baseline.

 $Q(1-a)$ , where  $Q(a)$  is quintile function of  $\mathcal{N}(0, 1)$ . Then a numerical PDE solver is used to solve the PDE equation with  $\alpha$  and return a solution<sup>[2](#page-23-0)</sup> $y = f(x)$ . Finally we evenly sample from a lattice of points  $x_i$  in the domain and obtain the ground truth  $y_i$  by  $y = f(x)$ . For the training dataset, Gaussian noise is added to  $y_i$  to mimic the measurement error, while for the test dataset, no noise is added.  $P$  in Figure [5](#page-22-1) is set to 100.  $N$  is set to 2620, where 2540 points inside the domain and 80 points on the boundary. Noise is sampled from  $N(0, 0.05)$ . *a* is set to 40%.

Implementation Details The implementation is based on DeepXDE in [Lu et al.](#page-27-13) [\(2021\)](#page-27-13). When learning a PINN from a synthetic dataset, we use a 4-layer FCN as the network with tanh activations. The loss function consists of prediction loss  $l(y_i, \hat{y_i})$  and the PDE-constrain loss  $|\Delta y_i|$  $\pi^2 \sin(\pi x_i)$ . The learning rate and distilling epoch number are set to 0.001 and 1000, respectively, with an Adam optimizer adopted. When optimizing the synthetic dataset, we adopt DFO algorithm NgIohTuned in Nevergrad [Rapin and Teytaud](#page-28-13) [\(2018\)](#page-28-13) to search for the best values of synthetic dataset. For the only significant parameter  $budget$  in NgIohTuned, we experiment with 5, 10, 20, 40,100 and 200 to find the best value. For evaluation, we learn a PINN from the synthetic dataset as described earlier and report the L2 error on test dataset. We compare our synthetic dataset with random sampled subsample from the training dataset without noise of the same quantity. We investigate 4 IPC settings: 10, 20, 40 and 80. We initialize the synthetic dataset from Gaussian noise when IPC=10 and from randomly sampled data from training dataset when IPC=20,40,80 for efficacy-efficiency trade-off.

### 8.2 Numerical Results

We investigate our algorithm and compare with baseline in 4 IPC settings. For each IPC setting, we try 6 budgets to find the best budget of the DFO algorithm. As shown in Table [1,](#page-23-1) our algorithm outperforms baseline in all the 4 different IPC settings. Although faced with noise, our algorithm shows smaller prediction error and stronger OOD generalization ability. When IPC is equal to 80, the generalization error is only one quarter of the strong baseline.

<span id="page-23-0"></span><sup>2.</sup> For the Laplace equation on a disk with Dirichlet boundary conditions, the analytic solution is known:  $y = r \cos \theta +$ α.

<span id="page-24-1"></span>Image /page/24/Figure/1 description: This image displays a 2x3 grid of plots. The top row, labeled (a), (b), and (c), shows a circular region with color gradients representing data. Plot (a) is titled 'Ground Truth' and shows a color gradient from purple on the left to yellow on the right, with a color bar ranging from -0.50 to 1.25. Plot (b) is titled 'PDE Reconstruction by Training on Synthetic Data' and exhibits a similar color gradient, with a color bar from -0.50 to 1.25. Plot (c) is titled 'Reconstruction Error by Training on Synthetic Data' and shows a color gradient with variations, indicating error, and a color bar from -0.01 to 0.06. The bottom row, labeled (d), (e), and (f), also shows circular regions with color gradients. Plot (d) is titled 'Ground Truth' and displays a color gradient from purple on the left to yellow on the right, with a color bar from -0.50 to 1.25. Plot (e) is titled 'PDE Reconstruction by Training on Subset Baseline' and shows a similar color gradient to (d), with a color bar from -0.75 to 1.00. Plot (f) is titled 'Reconstruction Error by Training on Subset Baseline' and displays a color gradient with variations, indicating error, and a color bar from -0.15 to 0.10. All plots have x and y axes labeled from -1.00 to 1.00.

Figure 6: PDE reconstruction visualization.  $(x, y)$  is the coordinate in the 2D domain. Value of the function or error is represented by color. (a) and (d) are ground truth of PDE solution. (b) and (e) are PDE reconstructed solution given by PINN trained on our synthetic data and baseline respectively. (c) and (f) show the reconstruction error of our method and baseline

PDE Reconstruction Visualization To visualize PDE reconstructed solution of our method and baseline, we sample a boundary condition  $\alpha$  from test-side tail in Figure [5](#page-22-1) and reconstruct the PDE with  $\alpha$  boundary condition by training a PINN on our synthetic dataset and baseline dataset respectively. As shown in Figure [6,](#page-24-1) both our method and baseline can roughly reconstruct the outline of ground truth solution, while our method shows much smaller reconstruction error compared with baseline, which verifies the effectiveness of our method.

**OOD Generalization Stability** To test the OOD generalization stability, the distance of two tails as shown in Figure [5](#page-22-1) is varied. In other words, the parameter a of quintile function  $Q(a)$  is set to  $50\%, 40\%, 30\%, 20\%$  and PDE reconstruction error of test dataset is computed as OOD generalization error under these 4 settings. As seen in Figure [7,](#page-25-7) our algorithm shows strong OOD generalization stability when  $\alpha$  varies from 50% to 20%. The OOD generalization error could be restricted to smaller than or around 0.05.

<span id="page-24-0"></span>

## 9 Conclusion

In this paper we provided a general formalization of DD, and presented a broad theoretical understanding of the optimization problem so defined. With this formalization we can define a number of interesting existing and new applications of DD, as well as provide an interpretive framework by

<span id="page-25-7"></span>Image /page/25/Figure/1 description: A bar chart titled "OOD Generalization Stability" displays the OOD Generalization Error on the y-axis and the parameter 'a' on the x-axis. The x-axis is labeled with percentages: 50%, 40%, 30%, and 20%. The corresponding OOD Generalization Errors are approximately 0.027 for 50%, 0.018 for 40%, 0.052 for 30%, and 0.032 for 20%.

Figure 7: Experimental result of OOD generalization stability. The horizontal axis  $a$  is parameter of quintile function  $Q(a)$ , which is varied from 50% to 20% to control the distance between training population prior and testing population prior.

which to design algorithms. We analyzed existing procedures for DD and provided a set of illustrative examples of novel applications. We hope that this work serves to provide useful scaffolding to assist future research activity in insight into DD and new DD methods.

## References

- <span id="page-25-6"></span>Albert S Berahas, Liyuan Cao, Krzysztof Choromanski, and Katya Scheinberg. A theoretical and empirical comparison of gradient approximations in derivative-free optimization. *Foundations of Computational Mathematics*, 22(2):507–560, 2022.
- <span id="page-25-2"></span>Pier Giovanni Bissiri, Chris C Holmes, and Stephen G Walker. A general framework for updating belief distributions. *Journal of the Royal Statistical Society: Series B (Statistical Methodology)*, 78(5):1103–1130, 2016.
- <span id="page-25-4"></span>J Frédéric Bonnans and Alexander Shapiro. *Perturbation analysis of optimization problems*. Springer Science & Business Media, 2013.
- <span id="page-25-3"></span>Benoît Bonnet. A pontryagin maximum principle in wasserstein spaces for constrained optimal control problems. *ESAIM: Control, Optimisation and Calculus of Variations*, 25:52, 2019.
- <span id="page-25-0"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022.
- <span id="page-25-5"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3739–3748, 2023.
- <span id="page-25-1"></span>Yaim Cooper. Global minima of overparameterized neural networks. *SIAM Journal on Mathematics of Data Science*, 3(2):676–691, 2021.

- <span id="page-26-2"></span>Imre Csiszár. I-divergence geometry of probability distributions and minimization problems. *The annals of probability*, pages 146–158, 1975.
- <span id="page-26-9"></span>Wenxiao Deng, Wenbin Li, Tianyu Ding, Lei Wang, Hongguang Zhang, Kuihua Huang, Jing Huo, and Yang Gao. Exploiting inter-sample and inter-feature relations in dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 17057–17066, 2024.
- <span id="page-26-6"></span>Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 3749–3758, 2023.
- <span id="page-26-4"></span>Jitka Dupačová. Stability and sensitivity-analysis for stochastic programming. Annals of operations *research*, 27:115–142, 1990.
- <span id="page-26-3"></span>Gerald A Edgar and Louis Sucheston. *Stopping times and directed processes*. Number 47. Cambridge University Press, 1992.
- <span id="page-26-0"></span>Jiahui Geng, Zongxiong Chen, Yuandou Wang, Herbert Woisetschlaeger, Sonja Schimmler, Ruben Mayer, Zhiming Zhao, and Chunming Rong. A survey on dataset distillation: Approaches, applications and future directions. *arXiv preprint arXiv:2305.01975*, 2023.
- <span id="page-26-10"></span>Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 15793–15803, 2024a.
- <span id="page-26-11"></span>Jianyang Gu, Kai Wang, Wei Jiang, and Yang You. Summarizing stream data for memory-restricted online continual learning. In *Proceedings of the AAAI Conference on Artificial Intelligence (AAAI)*, 2024b.
- <span id="page-26-7"></span>Nhat Ho, Koulik Khamaru, Raaz Dwivedi, Martin J Wainwright, Michael I Jordan, and Bin Yu. Instability, computational efficiency and statistical accuracy. *arXiv preprint arXiv:2005.11411*, 2020.
- <span id="page-26-8"></span>Arthur Jacot, Franck Gabriel, and Clément Hongler. Neural tangent kernel: Convergence and generalization in neural networks. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-26-5"></span>Aurya S Javeed, Drew P Kouri, and Thomas M Surowiec. A risk management perspective on statistical estimation and generalized variational inference. *arXiv preprint arXiv:2310.17376*, 2023.
- <span id="page-26-12"></span>Yuqi Jia, Saeed Vahidian, Jingwei Sun, Jianyi Zhang, Vyacheslav Kungurtsev, Neil Zhenqiang Gong, and Yiran Chen. Unlocking the potential of federated learning: The symphony of dataset distillation via deep generative latents. 2023. URL [https://arxiv.org/abs/2312.](https://arxiv.org/abs/2312.01537) [01537](https://arxiv.org/abs/2312.01537).
- <span id="page-26-1"></span>Miroslav Kárnỳ. Recursive estimation of high-order markov chains: Approximation by finite mixtures. *Information Sciences*, 326:188–201, 2016.

- <span id="page-27-4"></span>Miroslav Kárnỳ and Tomáš Kroupa. Axiomatisation of fully probabilistic design. *Information Sciences*, 186(1):105–113, 2012.
- <span id="page-27-1"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022.
- <span id="page-27-5"></span>Jeremias Knoblauch, Jack Jewson, and Theodoros Damoulas. An optimization-centric view on bayes' rule: Reviewing and generalizing variational inference. *The Journal of Machine Learning Research*, 23(1):5789–5897, 2022.
- <span id="page-27-12"></span>Jeffrey Larson and Stephen C Billups. Stochastic derivative-free optimization using a trust region framework. *Computational Optimization and applications*, 64:619–645, 2016.
- <span id="page-27-0"></span>Shiye Lei and Dacheng Tao. A comprehensive survey to dataset distillation. *arXiv preprint arXiv:2301.05603*, 2023.
- <span id="page-27-6"></span>Adam B Levy. Solution sensitivity from general principles. *SIAM journal on control and optimization*, 40(1):1–38, 2001.
- <span id="page-27-7"></span>Dai Liu, Jindong Gu, Hu Cao, Carsten Trinitis, and Martin Schulz. Dataset distillation by automatic training trajectories. In *Proceedings of the European Conference on Computer Vision (ECCV)*, 2024.
- <span id="page-27-9"></span>Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *Advances in neural information processing systems*, 35:1100–1113, 2022.
- <span id="page-27-2"></span>Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 17314–17324, 2023.
- <span id="page-27-3"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *Advances in Neural Information Processing Systems*, 35:13877– 13891, 2022.
- <span id="page-27-10"></span>Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. In *International Conference on Machine Learning*, pages 22649–22674. PMLR, 2023.
- <span id="page-27-13"></span>Lu Lu, Xuhui Meng, Zhiping Mao, and George Em Karniadakis. DeepXDE: A deep learning library for solving differential equations. *SIAM Review*, 63(1):208–228, 2021. doi: 10.1137/ 19M1274067.
- <span id="page-27-11"></span>Yao Lu, Jianyang Gu, Xuguang Chen, Saeed Vahidian, and Qi Xuan. Exploring the impact of dataset bias on dataset distillation. pages 7656–7663, June 2024.
- <span id="page-27-8"></span>Song Mei, Andrea Montanari, and Phan-Minh Nguyen. A mean field view of the landscape of twolayer neural networks. *Proceedings of the National Academy of Sciences*, 115(33):E7665–E7671, 2018.

- <span id="page-28-7"></span>Ilya Molchanov and Sergei Zuyev. Tangent sets in the space of measures: with applications to variational analysis. *Journal of mathematical analysis and applications*, 249(2):539–552, 2000.
- <span id="page-28-6"></span>Ilya Molchanov and Sergei Zuyev. Optimisation in space of measures and optimal design. *ESAIM: Probability and Statistics*, 8:12–24, 2004.
- <span id="page-28-0"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. *arXiv preprint arXiv:2011.00050*, 2020.
- <span id="page-28-1"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186– 5198, 2021.
- <span id="page-28-4"></span>Pratik Patil and Jin-Hong Du. Generalized equivalences between subsampling and ridge regularization. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-28-12"></span>David Quesada. *dbnR: Dynamic Bayesian Network Learning and Inference*, 2024. URL [https:](https://CRAN.R-project.org/package=dbnR) [//CRAN.R-project.org/package=dbnR](https://CRAN.R-project.org/package=dbnR). R package version 0.7.9.
- <span id="page-28-13"></span>J. Rapin and O. Teytaud. Nevergrad - A gradient-free optimization platform. [https://GitHub.](https://GitHub.com/FacebookResearch/Nevergrad) [com/FacebookResearch/Nevergrad](https://GitHub.com/FacebookResearch/Nevergrad), 2018.
- <span id="page-28-8"></span>Andrzej Ruszczyński and Alexander Shapiro. Optimization of risk measures. *Probabilistic and randomized methods for design under uncertainty*, pages 119–157, 2006.
- <span id="page-28-3"></span>Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *arXiv preprint arXiv:2301.04272*, 2023.
- <span id="page-28-2"></span>Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z. Liu, Yuri A. Lawryshyn, and Konstantinos N. Plataniotis. DataDAM: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)*, pages 17097– 17107, 2023.
- <span id="page-28-10"></span>Yuzhang Shang, Zhihang Yuan, and Yan Yan. MIM4DD: Mutual information maximization for dataset distillation. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023.
- <span id="page-28-9"></span>Berfin Simsek, François Ged, Arthur Jacot, Francesco Spadaro, Clément Hongler, Wulfram Gerstner, and Johanni Brea. Geometry of the loss landscape in overparameterized neural networks: Symmetries and invariances. In *International Conference on Machine Learning*, pages 9722– 9732. PMLR, 2021.
- <span id="page-28-5"></span>Václav Smídl and Anthony Quinn. Mixture-based extension of the ar model and its recursive bayesian identification. *IEEE Transactions on Signal Processing*, 53(9):3530–3542, 2005.
- <span id="page-28-11"></span>Duo Su, Junjie Hou, Weizhi Gao, Yingjie Tian, and Bowen Tang. D4M: Dataset distillation via disentangled diffusion model. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 5809–5818, 2024.

- <span id="page-29-7"></span><span id="page-29-0"></span>Saeed Vahidian, Mingyu Wang, Jianyang Gu, Vyacheslav Kungurtsev, Wei Jiang, and Yiran Chen. Group distributionally robust dataset distillation with risk minimization. *arXiv preprint arXiv:2402.04676*, 2024.
- <span id="page-29-1"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-29-13"></span>Yuan Wang, Huazhu Fu, Renuga Kanagavelu, Qingsong Wei, Yong Liu, and Rick Siow Mong Goh. An aggregation-free federated learning for tackling data heterogeneity. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 26233– 26242, 2024.
- <span id="page-29-10"></span>Xing Wei, Anjia Cao, Funing Yang, and Zhiheng Ma. Sparse parameterization for epitomic dataset distillation. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023.
- <span id="page-29-12"></span>Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. FedDM: Iterative distribution matching for communication-efficient federated learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 16323– 16332, 2023.
- <span id="page-29-9"></span>Yue Xu, Yong-Lu Li, Kaitong Cui, Ziyu Wang, Cewu Lu, Yu-Wing Tai, and Chi-Keung Tang. Distill gold from massive ores: Efficient dataset distillation via critical samples selection. In *Proceedings of the European Conference on Computer Vision (ECCV)*, 2024.
- <span id="page-29-11"></span>Enneng Yang, Li Shen, Zhenyi Wang, Tongliang Liu, and Guibing Guo. An efficient dataset condensation plugin and its application to continual learning. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023.
- <span id="page-29-3"></span>Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023.
- <span id="page-29-8"></span>Hansong Zhang, Shikun Li, Pengju Wang, and Shiming Zeng, Dan Ge. M3D: Dataset condensation by minimizing maximum mean discrepancy. In *Proceedings of the AAAI Conference on Artificial Intelligence (AAAI)*, 2024.
- <span id="page-29-4"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023.
- <span id="page-29-2"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.
- <span id="page-29-6"></span>Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7856–7865, 2023.
- <span id="page-29-5"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022.

<span id="page-30-0"></span>Dongyao Zhu, Bowen Lei, Jie Zhang, Yanbo Fang, Ruqi Zhang, Yiqun Xie, and Dongkuan Xu. Rethinking data distillation: Do not overlook calibration. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)*, pages 4935–4945, 2023.
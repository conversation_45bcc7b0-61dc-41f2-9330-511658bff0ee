# GANs Trained by a Two Time-Scale Update Rule Converge to a Local Nash Equilibrium

Martin <PERSON>l <PERSON>thiner <PERSON>

**<PERSON>**

Sepp Hochreiter

LIT AI Lab & Institute of Bioinformatics, <PERSON> University Linz A-4040 Linz, Austria {mhe,ramsauer,unterthiner,nessler,hochreit}@bioinf.jku.at

## Abstract

Generative Adversarial Networks (GANs) excel at creating realistic images with complex models for which maximum likelihood is infeasible. However, the convergence of GAN training has still not been proved. We propose a two time-scale update rule (TTUR) for training GANs with stochastic gradient descent on arbitrary GAN loss functions. TTUR has an individual learning rate for both the discriminator and the generator. Using the theory of stochastic approximation, we prove that the TTUR converges under mild assumptions to a stationary local Nash equilibrium. The convergence carries over to the popular Adam optimization, for which we prove that it follows the dynamics of a heavy ball with friction and thus prefers flat minima in the objective landscape. For the evaluation of the performance of GANs at image generation, we introduce the 'Fréchet Inception Distance" (FID) which captures the similarity of generated images to real ones better than the Inception Score. In experiments, TTUR improves learning for DCGANs and Improved Wasserstein GANs (WGAN-GP) outperforming conventional GAN training on CelebA, CIFAR-10, SVHN, LSUN Bedrooms, and the One Billion Word Benchmark.

## Introduction

Generative adversarial networks (GANs) [\[18\]](#page-35-0) have achieved outstanding results in generating realistic images  $[51, 36, 27, 3, 6]$  $[51, 36, 27, 3, 6]$  $[51, 36, 27, 3, 6]$  $[51, 36, 27, 3, 6]$  $[51, 36, 27, 3, 6]$  $[51, 36, 27, 3, 6]$  $[51, 36, 27, 3, 6]$  $[51, 36, 27, 3, 6]$  $[51, 36, 27, 3, 6]$  and producing text  $[23]$ . GANs can learn complex generative models for which maximum likelihood or a variational approximations are infeasible. Instead of the likelihood, a discriminator network serves as objective for the generative model, that is, the generator. GAN learning is a game between the generator, which constructs synthetic data from random variables, and the discriminator, which separates synthetic data from real world data. The generator's goal is to construct data in such a way that the discriminator cannot tell them apart from real world data. Thus, the discriminator tries to minimize the synthetic-real discrimination error while the generator tries to maximize this error. Since training GANs is a game and its solution is a Nash equilibrium, gradient descent may fail to converge [\[53,](#page-36-2) [18,](#page-35-0) [20\]](#page-35-3). Only *local* Nash equilibria are found, because gradient descent is a local optimization method. If there exists a local neighborhood around a point in parameter space where neither the generator nor the discriminator can unilaterally decrease their respective losses, then we call this point a local Nash equilibrium.

31st Conference on Neural Information Processing Systems (NIPS 2017), Long Beach, CA, USA.

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image contains two plots. The left plot shows the FID score on the y-axis against the mini-batch size (in thousands) on the x-axis. There are three lines: 'orig 1e-5' (light blue), 'orig 1e-4' (dark blue), and 'TTUR 1e-5 1e-4' (red). The light blue line shows a sharp peak around 100k mini-batches, reaching an FID of over 400, before decreasing and fluctuating. The dark blue and red lines show a decreasing trend, with the red line generally staying below the dark blue line. The right plot shows the norm of the difference between the current and optimal state, ||x(n) - x\*||, on the y-axis against the iteration number on the x-axis, which is on a logarithmic scale. There are four lines representing different values of alpha and beta: a dotted light blue line for alpha = beta = [0.05, 0.05, 0.05, 0.05], a dashed dark blue line for alpha = beta = [0.5, 0.5, 0.5, 0.5], a solid dark blue line for alpha = beta = [1, 1, 1, 1], and a solid light blue line for alpha = beta = [5, 5, 5, 5]. The plot shows that as the iteration number increases, the norm generally decreases, with some fluctuations. The solid light blue line (alpha = beta = [5, 5, 5, 5]) shows a relatively stable and low norm compared to the other lines, especially at higher iterations. There are also arrows indicating a difference between two points on the solid dark blue line at around 10^4 iterations.

Figure 1: Left: Original vs. TTUR GAN training on CelebA. Right: Figure from Zhang 2007 [\[61\]](#page-37-0) and repeatedly return to a neighborhood of the optimal solution (see also Appendix Section  $A2.3$ ). However, when the upper bounds  $\alpha$ However, when the upper bounds on the errors are large, the iterates typically diverge. network flow problem. When the upper bounds on the errors  $(\alpha, \beta)$  are small, the iterates oscillate which shows the distance of the parameter from the optimum for a one time-scale update of a 4 node

To characterize the convergence properties of training general GANs is still an open challenge  $[19, 20]$ .  $\alpha$  cannot to convergence to  $\alpha$  obtain a convergence to the optimal convergence to the optimal convergence to the optimal convergence to the optimal convergence to the optimal convergence of  $\alpha$ For special GAN variants, convergence can be proved under certain assumptions  $[39, 22, 56]$  $[39, 22, 56]$ . A prerequisit for many convergence proofs is local stability [35] which was shown for GANs by to a  $\frac{1}{2}$  into a minimized substitute  $\frac{1}{2}$  is not a substitute. However, Nagarajan and Kolter require for  $\frac{1}{2}$  regarded and Rotter  $\frac{1}{2}$  for a num-max GATV setting. Trowever, ivagarded and Rotter require for their proof either rather strong and unrealistic assumptions or a restriction to a linear discriminator. tneir proor eitner ratner strong and<br>Recent convergence proofs for GA Recent convergence proofs for GANs hold for expectations over training samples or for the number<br>of examples going to infinity  $[37, 45, 40, 41]$  thus do not consider mini batch logging which loads to of examples going to infinity  $[37, 45, 40, 4]$ , thus do not consider mini-batch learning which leads to a stochastic gradient  $[57, 25, 42, 38]$  $[57, 25, 42, 38]$ . To characterize the convergence properties of training general GANs is still an open challenge  $[19, 20]$  $[19, 20]$  $[19, 20]$ . Nagarajan and Kolter [\[46\]](#page-36-4) for a min-max GAN setting. However, Nagarajan and Kolter require for<br>their great sither rether strong and unrealistic essumptions are restriction to a linear discriminator. of examples going to infinity  $[37, 45, 40, 4]$  $[37, 45, 40, 4]$  $[37, 45, 40, 4]$  $[37, 45, 40, 4]$  $[37, 45, 40, 4]$  $[37, 45, 40, 4]$ , thus do not consider mini-batch learning which leads to<br>a stochastic gradient  $[57, 25, 42, 38]$ 

 $\mathbf{F}$  and  $\mathbf{F}$  that when  $\mathbf{F}$  that when the upper-Recently actor-critic learning has been analyzed using stochastic approximation. Prasad et al. [\[50\]](#page-36-10) showed that a two time-scale update rule ensures that training reaches a stationary local Nash equilibrium if the critic learns faster than the actor. Convergence was proved via an ordinary equilibrium if the critic learns faster than the actor. Convergence was proved via an ordinary equilibrium if the cruc fearms fa equilibrium if the critic learns faster than the actor. Convergence was proved via an ordinary differential equation (ODE), whose stable limit points coincide with stationary local Nash equilibria. from Fig. 5 that the same approach. We prove that GANs converge to a local Nash equilibrium when trained We follow the same approach. We prove that GANs converge to a local Nash equilibrium when trained by a two time-scale update rule (TTUR), i.e., when discriminator and generator have separate learning by a two time-searc update rule (f f OK), i.e., when discriminator and generator have separate rearing<br>rates. This also leads to better results in experiments. The main premise is that the discriminator converges to a local minimum when the generator is fixed. If the generator changes slowly enough,<br>than the discriminator still converges since the generator perturbations are small. Besides enough, then the discriminator still converges, since the generator perturbations are small. Besides ensuring convergence, the performance may also improve since the discriminator must first learn new patterns before they are transferred to the generator. In contrast, a generator which is overly fast, drives the discriminator steadily into new regions without capturing its gathered information. In recent GAN implementations, the discriminator often learned faster than the generator. A new objective slowed down the generator to prevent it from overtraining on the current discriminator [\[53\]](#page-36-2). The Wasserstein GAN algorithm uses more update steps for the discriminator than for the generator [\[3\]](#page-34-0). We compare TTUR and standard GAN training. Fig. [1](#page-1-0) shows at the left panel a stochastic gradient example on CelebA for original GAN training (orig), which often leads to oscillations, and the TTUR. On the right panel an example of a 4 node network flow problem of Zhang et al. [\[61\]](#page-37-0) is shown. The distance between the actual parameter and its optimum for an one time-scale update rule is shown across iterates. When the upper bounds on the errors are small, the iterates return to a neighborhood of the optimal solution, while for large errors the iterates may diverge (see also Appendix Section [A2.3\)](#page-26-0).

Our novel contributions in this paper are:

- The two time-scale update rule for GANs,
- We proof that GANs trained with TTUR converge to a stationary local Nash equilibrium,
- The description of Adam as heavy ball with friction and the resulting second order differential equation,
- The convergence of GANs trained with TTUR and Adam to a stationary local Nash equilibrium,
- We introduce the "Fréchet Inception Distance" (FID) to evaluate GANs, which is more consistent than the Inception Score.

## Two Time-Scale Update Rule for GANs

We consider a discriminator  $D(.; w)$  with parameter vector w and a generator  $G(.; \theta)$  with parameter vector  $\theta$ . Learning is based on a stochastic gradient  $\tilde{g}(\theta, w)$  of the discriminator's loss function  $\mathcal{L}_D$ and a stochastic gradient  $\hat{h}(\theta, w)$  of the generator's loss function  $\mathcal{L}_G$ . The loss functions  $\mathcal{L}_D$  and  $\mathcal{L}_G$  can be the original as introduced in Goodfellow et al. [\[18\]](#page-35-0), its improved versions [\[20\]](#page-35-3), or recently proposed losses for GANs like the Wasserstein GAN [\[3\]](#page-34-0). Our setting is not restricted to min-max GANs, but is valid for all other, more general GANs for which the discriminator's loss function  $\mathcal{L}_D$ is not necessarily related to the generator's loss function  $\mathcal{L}_G$ . The gradients  $\tilde{g}(\theta, w)$  and  $\tilde{h}(\theta, w)$ are stochastic, since they use mini-batches of m real world samples  $x^{(i)}$ ,  $1 \leqslant i \leqslant m$  and m synthetic samples  $z^{(i)}$ ,  $1 \leq i \leq m$  which are randomly chosen. If the true gradients are  $g(\theta, w) = \nabla_w \mathcal{L}_D$  and  $h(\theta, w) = \nabla_{\theta} \mathcal{L}_G$ , then we can define  $\tilde{g}(\theta, w) = g(\theta, w) + M^{(w)}$  and  $\tilde{h}(\theta, w) = h(\theta, w) + M^{(\theta)}$ with random variables  $M^{(w)}$  and  $M^{(\theta)}$ . Thus, the gradients  $\tilde{g}(\theta, w)$  and  $\tilde{h}(\theta, w)$  are stochastic approximations to the true gradients. Consequently, we analyze convergence of GANs by two time-scale stochastic approximations algorithms. For a two time-scale update rule (TTUR), we use the learning rates  $b(n)$  and  $a(n)$  for the discriminator and the generator update, respectively:

<span id="page-2-0"></span>
$$
\boldsymbol{w}_{n+1} = \boldsymbol{w}_n + b(n) \left( \boldsymbol{g}(\boldsymbol{\theta}_n, \boldsymbol{w}_n) + \boldsymbol{M}_n^{(w)} \right), \ \boldsymbol{\theta}_{n+1} = \boldsymbol{\theta}_n + a(n) \left( \boldsymbol{h}(\boldsymbol{\theta}_n, \boldsymbol{w}_n) + \boldsymbol{M}_n^{(\theta)} \right). \tag{1}
$$

For more details on the following convergence proof and its assumptions see Appendix Section [A2.1.](#page-15-0) To prove convergence of GANs learned by TTUR, we make the following assumptions (The actual assumption is ended by  $\blacktriangleleft$ , the following text are just comments and explanations):

(A1) The gradients h and q are Lipschitz.  $\triangleleft$  Consequently, networks with Lipschitz smooth activation functions like ELUs ( $\alpha = 1$ ) [\[13\]](#page-35-8) fulfill the assumption but not ReLU networks.

$$
\textbf{(A2)}\ \sum_{n} a(n) = \infty, \sum_{n} a^{2}(n) < \infty, \sum_{n} b(n) = \infty, \sum_{n} b^{2}(n) < \infty, a(n) = o(b(n)) \blacktriangleleft
$$

- (A3) The stochastic gradient errors  $\{M_n^{(\theta)}\}$  and  $\{M_n^{(w)}\}$  are martingale difference sequences w.r.t. the increasing  $\sigma$ -field  $\mathcal{F}_n = \sigma(\theta_l, w_l, M_l^{(\theta)}, M_l^{(w)}, l \le n), n \ge 0$  with  $\mathbb{E}\left[\|\mathbf{M}_n^{(\theta)}\|^2 \mid \mathcal{F}_n^{(\theta)}\right] \leqslant B_1$  and  $\mathbb{E}\left[\|\mathbf{M}_n^{(w)}\|^2 \mid \mathcal{F}_n^{(w)}\right] \leqslant B_2$ , where  $B_1$  and  $B_2$  are positive deterministic constants. $\blacktriangleleft$  The original Assumption (A3) from Borkar 1997 follows from Lemma 2 in  $[7]$  (see also  $[52]$ ). The assumption is fulfilled in the Robbins-Monro setting, where mini-batches are randomly sampled and the gradients are bounded.
- (A4) For each  $\theta$ , the ODE  $\dot{w}(t) = g(\theta, w(t))$  has a local asymptotically stable attractor  $\lambda(\theta)$  within a domain of attraction  $G_{\theta}$  such that  $\lambda$  is Lipschitz. The ODE  $\dot{\theta}(t)$  =  $h(\theta(t), \lambda(\theta(t)))$  has a local asymptotically stable attractor  $\theta^*$  within a domain of attraction. $\blacktriangleleft$  The discriminator must converge to a minimum for fixed generator parameters and the generator, in turn, must converge to a minimum for this fixed discriminator minimum. Borkar 1997 required unique global asymptotically stable equilibria [\[9\]](#page-34-4). The assumption of global attractors was relaxed to local attractors via Assumption (A6) and Theorem 2.7 in Karmakar & Bhatnagar [\[28\]](#page-35-9). See for more details Assumption (A6) in the Appendix Section [A2.1.3.](#page-19-0) Here, the GAN objectives may serve as Lyapunov functions. These assumptions of locally stable ODEs can be ensured by an additional weight decay term in the loss function which increases the eigenvalues of the Hessian. Therefore, problems with a region-wise constant discriminator that has zero second order derivatives are avoided. For further discussion see Appendix Section  $A2$  (C3).
- (A5)  $\sup_n \|\theta_n\| < \infty$  and  $\sup_n \|w_n\| < \infty$ . Typically ensured by the objective or a weight decay term.

The next theorem has been proved in the seminal paper of Borkar 1997 [\[9\]](#page-34-4).

Theorem 1 (Borkar). *If the assumptions are satisfied, then the updates Eq.* [\(1\)](#page-2-0) *converge to*  $(\boldsymbol{\theta}^*, \boldsymbol{\lambda}(\boldsymbol{\theta}^*))$  a.s.

The solution  $(\theta^*, \lambda(\theta^*))$  is a stationary local Nash equilibrium [\[50\]](#page-36-10), since  $\theta^*$  as well as  $\lambda(\theta^*)$  are local asymptotically stable attractors with  $g(\theta^*, \lambda(\theta^*)) = 0$  and  $h(\theta^*, \lambda(\theta^*)) = 0$ . An alternative approach to the proof of convergence using the Poisson equation for ensuring a solution to the fast update rule can be found in the Appendix Section  $A2.1.2$ . This approach assumes a linear update function in the fast update rule which, however, can be a linear approximation to a nonlinear gradient [\[30,](#page-35-10) [32\]](#page-35-11). For the rate of convergence see Appendix Section [A2.2,](#page-22-0) where Section [A2.2.1](#page-22-1) focuses on linear and Section [A2.2.2](#page-24-0) on non-linear updates. For equal time-scales it can only be proven that the updates revisit an environment of the solution infinitely often, which, however, can be very large [\[61,](#page-37-0) [14\]](#page-35-12). For more details on the analysis of equal time-scales see Appendix Section [A2.3.](#page-26-0) The main idea of the proof of Borkar [\[9\]](#page-34-4) is to use  $(T, \delta)$  perturbed ODEs according to Hirsch 1989 [\[24\]](#page-35-13) (see also Appendix Section C of Bhatnagar, Prasad, & Prashanth 2013 [\[8\]](#page-34-5)). The proof relies on the fact that there eventually is a time point when the perturbation of the slow update rule is small enough (given by  $\delta$ ) to allow the fast update rule to converge. For experiments with TTUR, we aim at finding learning rates such that the slow update is small enough to allow the fast to converge. Typically, the slow update is the generator and the fast update the discriminator. We have to adjust the two learning rates such that the generator does not affect discriminator learning in a undesired way and perturb it too much. However, even a larger learning rate for the generator than for the discriminator may ensure that the discriminator has low perturbations. Learning rates cannot be translated directly into perturbation since the perturbation of the discriminator by the generator is different from the perturbation of the generator by the discriminator.

## <span id="page-3-0"></span>Adam Follows an HBF ODE and Ensures TTUR Convergence

In our experiments, we aim at using Adam stochastic approximation to avoid mode collapsing. GANs suffer from "mode collapsing" where large masses of probability are mapped onto a few modes that cover only small regions. While these regions represent meaningful samples, the variety of the

real world data is lost and only few prototype samples are generated. Different methods have been proposed to avoid mode collapsing  $[11, 43]$  $[11, 43]$  $[11, 43]$ . We obviate mode collapsing by using Adam stochastic approximation [\[29\]](#page-35-14). Adam can be described as Heavy Ball with Friction (HBF) (see below), since it averages over past gradients. This averaging corresponds to a velocity that makes the generator resistant to getting pushed into small regions. Adam as an HBF method typically overshoots small local minima that correspond to mode collapse and can find flat minima which generalize well [\[26\]](#page-35-15). Fig. [2](#page-3-0) depicts the dynamics of HBF, where the ball settles at a flat minimum. Next, we analyze Figure 2: Heavy Ball with Friction, where the whether GANs trained with TTUR converge when using ball with mass overshoots the local minimum Adam. For more details see Appendix Section [A3.](#page-29-0)

Image /page/3/Figure/4 description: The image displays a graph illustrating the concept of optimization, likely in the context of machine learning or calculus. The y-axis is labeled f( heta), representing a function, and the x-axis is labeled with values heta\_0, heta^+, heta\_n, and heta^\*. A curve representing the function f( heta) is shown, starting from a high value at heta\_0 and decreasing, with some local minima and maxima, eventually reaching a global minimum at heta^\*. Several circles, some solid and some hollow, are depicted along the curve, with arrows indicating movement. At heta\_0, a hollow circle is shown at the peak, with an arrow pointing downwards, suggesting the start of an optimization process. As the process progresses, solid circles mark points along the curve, and hollow circles with arrows indicate the movement towards lower values of f( heta). Specifically, there's a hollow circle with arrows indicating movement towards a local minimum between heta\_0 and heta^+, then a solid circle at heta^+, followed by another hollow circle with arrows indicating movement towards a local minimum between heta^+ and heta\_n. A solid circle is shown at heta\_n, and then another hollow circle with arrows indicating movement towards the global minimum at heta^\*.

 $\theta^+$  and settles at the flat minimum  $\theta^*$ .

We recapitulate the Adam update rule at step n, with learning rate a, exponential averaging factors  $\beta_1$ for the first and  $\beta_2$  for the second moment of the gradient  $\nabla f(\theta_{n-1})$ :

$$
g_n \leftarrow \nabla f(\theta_{n-1}) \\ m_n \leftarrow (\beta_1/(1-\beta_1^n)) m_{n-1} + ((1-\beta_1)/(1-\beta_1^n)) g_n \\ v_n \leftarrow (\beta_2/(1-\beta_2^n)) v_{n-1} + ((1-\beta_2)/(1-\beta_2^n)) g_n \odot g_n \\ \theta_n \leftarrow \theta_{n-1} - a m_n/(\sqrt{v_n} + \epsilon), \quad (2)
$$

where following operations are meant componentwise: the product  $\odot$ , the square root  $\sqrt{\cdot}$ , and the division / in the last line. Instead of learning rate a, we introduce the damping coefficient  $a(n)$  with  $a(n) = an^{-\tau}$  for  $\tau \in (0, 1]$ . Adam has parameters  $\beta_1$  for averaging the gradient and  $\beta_2$  parametrized by a positive  $\alpha$  for averaging the squared gradient. These parameters can be considered as defining a memory for Adam. To characterize  $\beta_1$  and  $\beta_2$  in the following, we define the exponential memory  $r(n) = r$  and the polynomial memory  $r(n) = r/\sum_{l=1}^{n} a(l)$  for some positive constant r. The next theorem describes Adam by a differential equation, which in turn allows to apply the idea of  $(T, \delta)$ perturbed ODEs to TTUR. Consequently, learning GANs with TTUR and Adam converges.

**Theorem 2.** *If Adam is used with*  $\beta_1 = 1 - a(n+1)r(n)$ ,  $\beta_2 = 1 - \alpha a(n+1)r(n)$  *and with*  $\nabla f$ *as the full gradient of the lower bounded, continuously differentiable objective* f*, then for stationary second moments of the gradient, Adam follows the differential equation for Heavy Ball with Friction* *(HBF):*

<span id="page-4-1"></span><span id="page-4-0"></span>
$$
\ddot{\theta}_t + a(t) \dot{\theta}_t + \nabla f(\theta_t) = \mathbf{0}.
$$
 (3)

*Adam converges for gradients* ∇f *that are* L*-Lipschitz.*

*Proof.* Gadat et al. derived a discrete and stochastic version of Polyak's Heavy Ball method [\[49\]](#page-36-13), the Heavy Ball with Friction (HBF) [\[17\]](#page-35-16):

$$
\theta_{n+1} = \theta_n - a(n+1) \mathbf{m}_n, \qquad (4)
$$
  
$$
\mathbf{m}_{n+1} = (1 - a(n+1) r(n)) \mathbf{m}_n + a(n+1) r(n) (\nabla f(\theta_n) + \mathbf{M}_{n+1}).
$$

These update rules are the first moment update rules of Adam [\[29\]](#page-35-14). The HBF can be formulated as the differential equation Eq.  $(3)$  [\[17\]](#page-35-16). Gadat et al. showed that the update rules Eq.  $(4)$  converge for loss functions f with at most quadratic grow and stated that convergence can be proofed for  $\nabla f$  that are  $L$ -Lipschitz [\[17\]](#page-35-16). Convergence has been proved for continuously differentiable  $f$  that is quasiconvex (Theorem 3 in Goudou & Munier [\[21\]](#page-35-17)). Convergence has been proved for  $\nabla f$  that is L-Lipschitz and bounded from below (Theorem 3.1 in Attouch et al. [\[5\]](#page-34-7)). Adam normalizes the average  $m_n$  by the second moments  $v_n$  of of the gradient  $g_n$ :  $v_n = \mathrm{E}[g_n \odot g_n]$ .  $m_n$  is componentwise divided by the square root of the components of  $v_n$ . We assume that the second moments of  $g_n$  are stationary, i.e.,  $v = \mathbb{E}[g_n \odot g_n]$ . In this case the normalization can be considered as additional noise since the normalization factor randomly deviates from its mean. In the HBF interpretation the normalization by  $\sqrt{v}$  corresponds to introducing gravitation. We obtain

$$
\mathbf{v}_n = \frac{1-\beta_2}{1-\beta_2^n} \sum_{l=1}^n \beta_2^{n-l} \mathbf{g}_l \odot \mathbf{g}_l \; , \; \Delta \mathbf{v}_n = \mathbf{v}_n - \mathbf{v} = \frac{1-\beta_2}{1-\beta_2^n} \sum_{l=1}^n \beta_2^{n-l} \left( \mathbf{g}_l \odot \mathbf{g}_l - \mathbf{v} \right) \; . \quad (5)
$$

For a stationary second moment v and  $\beta_2 = 1 - \alpha a(n+1)r(n)$ , we have  $\Delta v_n \propto a(n+1)r(n)$ . We use a componentwise linear approximation to Adam's second moment normalization  $1/\sqrt{v + \Delta v_n} \approx$  $1/\sqrt{v} - (1/(2v \odot \sqrt{v})) \odot \Delta v_n + O(\Delta^2 v_n)$ , where all operations are meant componentwise. If we set  $M_{n+1}^{(v)} = -(m_n \odot \Delta v_n)/(2v \odot \sqrt{v}a(n+1)r(n))$ , then  $m_n/\sqrt{v_n} \approx m_n/\sqrt{v} + a(n+1)r(n)$  $(1) r(n) M_{n+1}^{(v)}$  and  $\mathrm{E}\left[M_{n+1}^{(v)}\right] = 0$ , since  $\mathrm{E}[g_l \odot g_l - v] = 0$ . For a stationary second moment v, the random variable  $\{M_n^{(v)}\}$  is a martingale difference sequence with a bounded second moment. Therefore  $\{M_{n+1}^{(v)}\}$  can be subsumed into  $\{M_{n+1}\}\$ in update rules Eq. [\(4\)](#page-4-1). The factor  $1/\sqrt{v}$  can be componentwise incorporated into the gradient  $g$  which corresponds to rescaling the parameters without changing the minimum. П

According to Attouch et al. [\[5\]](#page-34-7) the energy, that is, a Lyapunov function, is  $E(t) = 1/2|\dot{\theta}(t)|^2 + f(\theta(t))$ and  $\dot{E}(t) = -a |\dot{\theta}(t)|^2 < 0$ . Since Adam can be expressed as differential equation and has a Lyapunov function, the idea of  $(T, \delta)$  perturbed ODEs [\[9,](#page-34-4) [24,](#page-35-13) [10\]](#page-34-8) carries over to Adam. Therefore the convergence of Adam with TTUR can be proved via two time-scale stochastic approximation analysis like in Borkar [\[9\]](#page-34-4) for stationary second moments of the gradient.

In the Appendix we further discuss the convergence of two time-scale stochastic approximation algorithms with additive noise, linear update functions depending on Markov chains, nonlinear update functions, and updates depending on controlled Markov processes. Futhermore, the Appendix presents work on the rate of convergence for both linear and nonlinear update rules using similar techniques as the local stability analysis of Nagarajan and Kolter [\[46\]](#page-36-4). Finally, we elaborate more on equal time-scale updates, which are investigated for saddle point problems and actor-critic learning.

## Experiments

Performance Measure. Before presenting the experiments, we introduce a quality measure for models learned by GANs. The objective of generative learning is that the model produces data which matches the observed data. Therefore, each distance between the probability of observing real world data  $p_w(.)$  and the probability of generating model data  $p(.)$  can serve as performance measure for generative models. However, defining appropriate performance measures for generative models

Image /page/5/Figure/0 description: The image displays six plots, each illustrating the relationship between a "disturbance level" on the x-axis and "FID" on the y-axis. Each plot is accompanied by a series of images at the top, representing different levels of disturbance. The first plot shows FID values of approximately 0, 50, 75, and 225 at disturbance levels 0, 1, 2, and 3, respectively, with images showing increasing noise. The second plot shows FID values of approximately 0, 25, 125, and 250 at disturbance levels 0, 1, 2, and 3, with images showing increasing blur. The third plot shows FID values of approximately 0, 125, 225, and 375 at disturbance levels 0, 1, 2, and 3, with images showing increasing black pixel corruption. The fourth plot shows FID values of approximately 0, 10, 125, and 425 at disturbance levels 0, 1, 2, and 3, with images showing increasing geometric distortion. The fifth plot shows FID values of approximately 0, 375, 375, and 375 at disturbance levels 0, 1, 2, and 3, with images showing increasing speckle noise. The sixth plot shows FID values of approximately 0, 50, 125, and 250 at disturbance levels 0, 1, 2, and 3, with images showing a mix of faces and objects like clocks and cars.

<span id="page-5-0"></span>Figure 3: FID is evaluated for upper left: Gaussian noise, upper middle: Gaussian blur, upper right: implanted black rectangles, lower left: swirled images, lower middle: salt and pepper noise, and lower right: CelebA dataset contaminated by ImageNet images. The disturbance level rises from zero and increases to the highest level. The FID captures the disturbance level very well by monotonically increasing.

is difficult [\[55\]](#page-36-14). The best known measure is the likelihood, which can be estimated by annealed importance sampling [\[59\]](#page-37-3). However, the likelihood heavily depends on the noise assumptions for the real data and can be dominated by single samples  $[55]$ . Other approaches like density estimates have drawbacks, too [\[55\]](#page-36-14). A well-performing approach to measure the performance of GANs is the "Inception Score" which correlates with human judgment [\[53\]](#page-36-2). Generated samples are fed into an inception model that was trained on ImageNet. Images with meaningful objects are supposed to have low label (output) entropy, that is, they belong to few object classes. On the other hand, the entropy across images should be high, that is, the variance over the images should be large. Drawback of the Inception Score is that the statistics of real world samples are not used and compared to the statistics of synthetic samples. Next, we improve the Inception Score. The equality  $p(.) = p_w(.)$ holds except for a non-measurable set if and only if  $\int p(.) f(x) dx = \int p_w(.) f(x) dx$  for a basis  $\tilde{f}(.)$ spanning the function space in which  $p(.)$  and  $p_w(.)$  live. These equalities of expectations are used to describe distributions by moments or cumulants, where  $f(x)$  are polynomials of the data x. We generalize these polynomials by replacing x by the coding layer of an inception model in order to obtain vision-relevant features. For practical reasons we only consider the first two polynomials, that is, the first two moments: mean and covariance. The Gaussian is the maximum entropy distribution for given mean and covariance, therefore we assume the coding units to follow a multidimensional Gaussian. The difference of two Gaussians (synthetic and real-world images) is measured by the Fréchet distance [\[16\]](#page-35-18) also known as Wasserstein-2 distance [\[58\]](#page-37-4). We call the Fréchet distance  $d(.,.)$ between the Gaussian with mean  $(m, C)$  obtained from  $p(.)$  and the Gaussian with mean  $(m_w, C_w)$ obtained from  $p_w(.)$  the "Fréchet Inception Distance" (FID), which is given by [\[15\]](#page-35-19):

$$
d^{2}((\boldsymbol{m},\boldsymbol{C}),(\boldsymbol{m}_{w},\boldsymbol{C}_{w}))=\|\boldsymbol{m}-\boldsymbol{m}_{w}\|_{2}^{2}+\mathrm{Tr}(\boldsymbol{C}+\boldsymbol{C}_{w}-2(\boldsymbol{C}\boldsymbol{C}_{w})^{1/2})\,. \tag{6}
$$

Next we show that the FID is consistent with increasing disturbances and human judgment. Fig. [3](#page-5-0) evaluates the FID for Gaussian noise, Gaussian blur, implanted black rectangles, swirled images, salt and pepper noise, and CelebA dataset contaminated by ImageNet images. The FID captures the disturbance level very well. In the experiments we used the FID to evaluate the performance of GANs. For more details and a comparison between FID and Inception Score see Appendix Section [A1,](#page-10-0) where we show that FID is *more consistent* with the noise level than the Inception Score.

Model Selection and Evaluation. We compare the two time-scale update rule (TTUR) for GANs with the original GAN training to see whether TTUR improves the convergence speed and performance of GANs. We have selected Adam stochastic optimization to reduce the risk of mode collapsing. The advantage of Adam has been confirmed by MNIST experiments, where Adam indeed

considerably reduced the cases for which we observed mode collapsing. Although TTUR ensures that the discriminator converges during learning, practicable learning rates must be found for each experiment. We face a trade-off since the learning rates should be small enough (e.g. for the generator) to ensure convergence but at the same time should be large enough to allow fast learning. For each of the experiments, the learning rates have been optimized to be large while still ensuring stable training which is indicated by a decreasing FID or Jensen-Shannon-divergence (JSD). We further fixed the time point for stopping training to the update step when the FID or Jensen-Shannon-divergence of the best models was no longer decreasing. For some models, we observed that the FID diverges or starts to increase at a certain time point. An example of this behaviour is shown in Fig. [5.](#page-7-0) The performance of generative models is evaluated via the Fréchet Inception Distance (FID) introduced above. For the One Billion Word experiment, the normalized JSD served as performance measure. For computing the FID, we propagated all images from the training dataset through the pretrained Inception-v3 model following the computation of the Inception Score [\[53\]](#page-36-2), however, we use the last pooling layer as coding layer. For this coding layer, we calculated the mean  $m_w$  and the covariance matrix  $C_w$ . Thus, we approximate the first and second central moment of the function given by the Inception coding layer under the real world distribution. To approximate these moments for the model distribution, we generate 50,000 images, propagate them through the Inception-v3 model, and then compute the mean  $m$  and the covariance matrix  $C$ . For computational efficiency, we evaluate the FID every 1,000 DCGAN mini-batch updates, every 5,000 WGAN-GP outer iterations for the image experiments, and every 100 outer iterations for the WGAN-GP language model. For the one time-scale updates a WGAN-GP outer iteration for the image model consists of five discriminator mini-batches and ten discriminator mini-batches for the language model, where we follow the original implementation. For TTUR however, the discriminator is updated only once per iteration. We repeat the training for each single time-scale (orig) and TTUR learning rate eight times for the image datasets and ten times for the language benchmark. Additionally to the mean FID training progress we show the minimum and maximum FID over all runs at each evaluation time-step. For more details, implementations and further results see Appendix Section [A4](#page-31-0) and [A6.](#page-33-0)

**Simple Toy Data.** We first want to demonstrate the difference between a single time-scale update rule and TTUR on a simple toy min/max problem where a saddle point should be found. The objective  $f(x, y) = (1 + x^2)(100 - y^2)$  in Fig. [4](#page-6-0) (left) has a saddle point at  $(x, y) = (0, 0)$  and fulfills assumption A4. The norm  $\|(x, y)\|$  measures the distance of the parameter vector  $(x, y)$  to the saddle point. We update  $(x, y)$  by gradient descent in x and gradient ascent in y using additive Gaussian noise in order to simulate a stochastic update. The updates should converge to the saddle point  $(x, y) = (0, 0)$  with objective value  $f(0, 0) = 100$  and the norm 0. In Fig. [4](#page-6-0) (right), the first two rows show one time-scale update rules. The large learning rate in the first row diverges and has large fluctuations. The smaller learning rate in the second row converges but slower than the TTUR in the third row which has slow  $x$ -updates. TTUR with slow  $y$ -updates in the fourth row also converges but slower.

<span id="page-6-0"></span>Image /page/6/Figure/2 description: The image displays a 3D surface plot alongside a grid of smaller plots. The 3D plot shows a curved surface with axes labeled from -3 to 3 on one axis and -30 to 30 on another, with a third axis ranging from 1000 to -8000. Two red lines are drawn across the surface. To the right of the 3D plot, there are three columns of smaller plots, each column representing a different metric: 'objective', 'norm', and 'x vs y'. Each column contains four plots stacked vertically. The 'objective' plots show fluctuating values over approximately 4000 iterations, with some plots stabilizing quickly while others remain variable. The 'norm' plots also show values over time, with some decreasing rapidly to near zero and others showing more gradual decreases or fluctuations. The 'x vs y' plots display scatter points, with some showing a clear pattern of convergence or distribution, while others are more spread out or show distinct clusters.

Figure 4: Left: Plot of the objective with a saddle point at  $(0,0)$ . Right: Training progress with equal learning rates of  $0.01$  (first row) and  $0.001$  (second row)) for x and y, TTUR with a learning rate of 0.0001 for x vs. 0.01 for y (third row) and a larger learning rate of 0.01 for x vs. 0.0001 for y (fourth row). The columns show the function values (left), norms (middle), and  $(x, y)$  (right). TTUR (third row) clearly converges faster than with equal time-scale updates and directly moves to the saddle point as shown by the norm and in the  $(x, y)$ -plot.

**DCGAN on Image Data.** We test TTUR for the deep convolutional GAN (DCGAN) [\[51\]](#page-36-0) at the CelebA, CIFAR-10, SVHN and LSUN Bedrooms dataset. Fig. [5](#page-7-0) shows the FID during learning

Image /page/7/Figure/0 description: This image contains four line graphs, each plotting FID (Fréchet Inception Distance) on the y-axis against mini-batch size (in thousands) on the x-axis. Each graph displays multiple lines representing different configurations, indicated by labels such as 'orig 1e-5', 'orig 1e-4', 'orig 5e-4', 'orig 2e-4', 'orig 5e-5', and 'TTUR 1e-5 5e-4' or 'TTUR 1e-4 5e-4'. Shaded regions around the lines represent confidence intervals. The top-left graph shows FID values ranging from 0 to 400, with the x-axis extending to 250. The top-right graph shows FID values from 40 to 120, with the x-axis extending to 120. The bottom-left graph shows FID values from 0 to 400, with the x-axis extending to 175. The bottom-right graph shows FID values from 0 to 400, with the x-axis extending to 400.

<span id="page-7-0"></span>Figure 5: Mean FID (solid line) surrounded by a shaded area bounded by the maximum and the minimum over 8 runs for DCGAN on CelebA, CIFAR-10, SVHN, and LSUN Bedrooms. TTUR learning rates are given for the discriminator b and generator a as: "TTUR b  $a$ ". Top Left: CelebA. Top Right: CIFAR-10, starting at mini-batch update 10k for better visualisation. Bottom Left: SVHN. Bottom Right: LSUN Bedrooms. Training with TTUR (red) is more stable, has much lower variance, and leads to a better FID.

with the original learning method (orig) and with TTUR. The original training method is faster at the beginning, but TTUR eventually achieves better performance. DCGAN trained TTUR reaches constantly a lower FID than the original method and for CelebA and LSUN Bedrooms all one time-scale runs diverge. For DCGAN the learning rate of the generator is larger then that of the discriminator, which, however, does not contradict the TTUR theory (see the Appendix Section [A5\)](#page-33-1). In Table [1](#page-8-0) we report the best FID with TTUR and one time-scale training for optimized number of updates and learning rates. TTUR constantly outperforms standard training and is more stable.

WGAN-GP on Image Data. We used the WGAN-GP image model [\[23\]](#page-35-2) to test TTUR with the CIFAR-10 and LSUN Bedrooms datasets. In contrast to the original code where the discriminator is trained five times for each generator update, TTUR updates the discriminator only once, therefore we align the training progress with wall-clock time. The learning rate for the original training was optimized to be large but leads to stable learning. TTUR can use a higher learning rate for the discriminator since TTUR stabilizes learning. Fig. [6](#page-7-1) shows the FID during learning with the original learning method and with TTUR. Table [1](#page-8-0) shows the best FID with TTUR and one time-scale training for optimized number of iterations and learning rates. Again TTUR reaches lower FIDs than one time-scale training.

Image /page/7/Figure/4 description: Two line graphs show the FID score over time in minutes for different training configurations. The left graph spans 1000 minutes and shows four lines: 'orig 1e-4' (teal), 'orig 5e-4' (orange), 'orig 7e-4' (green), and 'TTUR 3e-4 1e-4' (red). The 'orig 1e-4' line starts at approximately 150 FID and decreases sharply to around 40 FID by 200 minutes, then fluctuates slightly. The 'orig 5e-4' and 'TTUR 3e-4 1e-4' lines start around 70-80 FID and decrease to around 30-40 FID by 200 minutes, with the red line generally showing the lowest FID. The 'orig 7e-4' line starts around 70 FID and decreases to around 50 FID by 200 minutes, then gradually increases to around 60 FID by 1000 minutes, with a shaded green area indicating variability. The right graph spans 2000 minutes and shows similar trends but with higher initial FID values. The 'orig 1e-4' line starts at approximately 350 FID and decreases to around 100 FID by 500 minutes, then fluctuates. The 'orig 5e-4' and 'TTUR 3e-4 1e-4' lines start around 150-170 FID and decrease to around 50-60 FID by 500 minutes. The 'orig 7e-4' line starts around 150 FID and decreases to around 100 FID by 500 minutes, then gradually increases to around 120 FID by 2000 minutes, with a shaded green area indicating variability. Both graphs have 'FID' on the y-axis and 'minutes' on the x-axis.

<span id="page-7-1"></span>Figure 6: Mean FID (solid line) surrounded by a shaded area bounded by the maximum and the minimum over 8 runs for WGAN-GP on CelebA, CIFAR-10, SVHN, and LSUN Bedrooms. TTUR learning rates are given for the discriminator b and generator a as: "TTUR b  $a$ ". Left: CIFAR-10, starting at minute 20. Right: LSUN Bedrooms. Training with TTUR (red) has much lower variance and leads to a better FID.

Image /page/8/Figure/0 description: Two plots are shown side-by-side. Both plots have "minutes" on the x-axis and "JSD" on the y-axis. The left plot shows two lines, a blue line labeled "orig 1e-4" and a red line labeled "TTUR 3e-4 1e-4". The blue line starts at approximately 0.52 and decreases to approximately 0.43 at 1200 minutes. The red line starts at approximately 0.49 and decreases to approximately 0.38 at 1200 minutes. The left plot's y-axis ranges from 0.35 to 0.55. The right plot also shows two lines, a blue line labeled "orig 1e-4" and a red line labeled "TTUR 3e-4 1e-4". The blue line starts at approximately 0.87 and decreases to approximately 0.78 at 1200 minutes. The red line starts at approximately 0.85 and decreases to approximately 0.77 at 1200 minutes. The right plot's y-axis ranges from 0.75 to 0.85. Both plots show shaded areas around the lines, indicating variability.

<span id="page-8-1"></span>Figure 7: Performance of WGAN-GP models trained with the original (orig) and our TTUR method on the One Billion Word benchmark. The performance is measured by the normalized Jensen-Shannon-divergence based on 4-gram (left) and 6-gram (right) statistics averaged (solid line) and surrounded by a shaded area bounded by the maximum and the minimum over 10 runs, aligned to wall-clock time and starting at minute 150. TTUR learning (red) clearly outperforms the original one time-scale learning.

WGAN-GP on Language Data. Finally the One Billion Word Benchmark [\[12\]](#page-34-9) serves to evaluate TTUR on WGAN-GP. The character-level generative language model is a 1D convolutional neural network (CNN) which maps a latent vector to a sequence of one-hot character vectors of dimension 32 given by the maximum of a softmax output. The discriminator is also a 1D CNN applied to sequences of one-hot vectors of 32 characters. Since the FID criterium only works for images, we measured the performance by the Jensen-Shannon-divergence (JSD) between the model and the real world distribution as has been done previously  $[23]$ . In contrast to the original code where the critic is trained ten times for each generator update, TTUR updates the discriminator only once, therefore we align the training progress with wall-clock time. The learning rate for the original training was optimized to be large but leads to stable learning. TTUR can use a higher learning rate for the discriminator since TTUR stabilizes learning. We report for the 4 and 6-gram word evaluation the normalized mean JSD for ten runs for original training and TTUR training in Fig. [7.](#page-8-1) In Table [1](#page-8-0) we report the best JSD at an optimal time-step where TTUR outperforms the standard training for both measures. The improvement of TTUR on the 6-gram statistics over original training shows that TTUR enables to learn to generate more subtle pseudo-words which better resembles real words.

| <b>DCGAN</b> Image                |             |            |            |            |        |         |            |            |  |
|-----------------------------------|-------------|------------|------------|------------|--------|---------|------------|------------|--|
| dataset                           | method      | b, a       | updates    | <b>FID</b> | method | b = a   | updates    | <b>FID</b> |  |
| CelebA                            | <b>TTUR</b> | 1e-5, 5e-4 | 225k       | 12.5       | orig   | 5e-4    | 70k        | 21.4       |  |
| CIFAR-10                          | <b>TTUR</b> | 1e-4, 5e-4 | 75k        | 36.9       | orig   | 1e-4    | 100k       | 37.7       |  |
| <b>SVHN</b>                       | <b>TTUR</b> | 1e-5, 1e-4 | 165k       | 12.5       | orig   | 5e-5    | 185k       | 21.4       |  |
| <b>LSUN</b>                       | <b>TTUR</b> | 1e-5, 1e-4 | 340k       | 57.5       | orig   | 5e-5    | 70k        | 70.4       |  |
| WGAN-GP Image                     |             |            |            |            |        |         |            |            |  |
| dataset                           | method      | b, a       | time(m)    | <b>FID</b> | method | b = a   | time(m)    | <b>FID</b> |  |
| CIFAR-10                          | <b>TTUR</b> | 3e-4, 1e-4 | 700        | 24.8       | orig   | 1e-4    | 800        | 29.3       |  |
| <b>LSUN</b>                       | <b>TTUR</b> | 3e-4, 1e-4 | 1900       | 9.5        | orig   | 1e-4    | 2010       | 20.5       |  |
| <b>WGAN-GP Language</b><br>n-gram |             |            |            |            |        |         |            |            |  |
| method                            | b, a        | time(m)    | <b>JSD</b> | method     | b = a  | time(m) | <b>JSD</b> |            |  |
| 4-gram                            | <b>TTUR</b> | 3e-4, 1e-4 | 1150       | 0.35       | orig   | 1e-4    | 1040       | 0.38       |  |
| 6-gram                            | <b>TTUR</b> | 3e-4, 1e-4 | 1120       | 0.74       | orig   | 1e-4    | 1070       | 0.77       |  |

<span id="page-8-0"></span>Table 1: The performance of DCGAN and WGAN-GP trained with the original one time-scale update rule and with TTUR on CelebA, CIFAR-10, SVHN, LSUN Bedrooms and the One Billion Word Benchmark. During training we compare the performance with respect to the FID and JSD for optimized number of updates. TTUR exhibits consistently a better FID and a better JSD.

## Conclusion

For learning GANs, we have introduced the two time-scale update rule (TTUR), which we have proved to converge to a stationary local Nash equilibrium. Then we described Adam stochastic optimization as a heavy ball with friction (HBF) dynamics, which shows that Adam converges and that Adam tends to find flat minima while avoiding small local minima. A second order differential equation describes the learning dynamics of Adam as an HBF system. Via this differential equation, the convergence of GANs trained with TTUR to a stationary local Nash equilibrium can be extended to Adam. Finally, to evaluate GANs, we introduced the 'Fréchet Inception Distance" (FID) which captures the similarity of generated images to real ones better than the Inception Score. In experiments we have compared GANs trained with TTUR to conventional GAN training with a one time-scale update rule on CelebA, CIFAR-10, SVHN, LSUN Bedrooms, and the One Billion Word Benchmark. TTUR outperforms conventional GAN training consistently in all experiments.

## Acknowledgment

This work was supported by NVIDIA Corporation, Bayer AG with Research Agreement 09/2017, Zalando SE with Research Agreement 01/2016, Audi.JKU Deep Learning Center, Audi Electronic Venture GmbH, IWT research grant IWT150865 (Exaptation), H2020 project grant 671555 (ExCAPE) and FWF grant P 28660-N31.

## References

The references are provided after Section [A6.](#page-34-10)

## Appendix

## Contents

| A1 Fréchet Inception Distance (FID)                                            | 11 |
|--------------------------------------------------------------------------------|----|
| A2 Two Time-Scale Stochastic Approximation Algorithms                          | 16 |
| A2.1 Convergence of Two Time-Scale Stochastic Approximation Algorithms         | 16 |
| A2.1.1 Additive Noise                                                          | 16 |
| A2.1.2 Linear Update, Additive Noise, and Markov Chain                         | 18 |
| A2.1.3 Additive Noise and Controlled Markov Processes                          | 20 |
| A2.2 Rate of Convergence of Two Time-Scale Stochastic Approximation Algorithms | 23 |
| A2.2.1 Linear Update Rules                                                     | 23 |
| A2.2.2 Nonlinear Update Rules                                                  | 25 |
| A2.3 Equal Time-Scale Stochastic Approximation Algorithms                      | 27 |
| A2.3.1 Equal Time-Scale for Saddle Point Iterates                              | 27 |
| A2.3.2 Equal Time Step for Actor-Critic Method                                 | 28 |
| A3 ADAM Optimization as Stochastic Heavy Ball with Friction                    | 30 |
| A4 Experiments: Additional Information                                         | 32 |
| A4.1 WGAN-GP on Image Data.                                                    | 32 |
| A4.2 WGAN-GP on the One Billion Word Benchmark.                                | 33 |
| A4.3 BEGAN                                                                     | 33 |
| A5 Discriminator vs. Generator Learning Rate                                   | 34 |
| A6 Used Software, Datasets, Pretrained Models, and Implementations             | 34 |
| List of Figures                                                                | 38 |
| List of Tables                                                                 | 38 |

## <span id="page-10-0"></span>A1 Fréchet Inception Distance (FID)

We improve the Inception score for comparing the results of GANs [\[53\]](#page-36-2). The Inception score has the disadvantage that it does not use the statistics of real world samples and compare it to the statistics of synthetic samples. Let  $p(.)$  be the distribution of model samples and  $p_w(.)$  the distribution of the samples from real world. The equality  $p(.) = p_w(.)$  holds except for a non-measurable set if and only if  $\int p(.) f(x)dx = \int p_w(.) \dot{f}(x)dx$  for a basis  $f(.)$  spanning the function space in which  $p(.)$  and  $p_w(.)$  live. These equalities of expectations are used to describe distributions by moments or cumulants, where  $f(x)$  are polynomials of the data x. We replacing x by the coding layer of an Inception model in order to obtain vision-relevant features and consider polynomials of the coding unit functions. For practical reasons we only consider the first two polynomials, that is, the first two moments: mean and covariance. The Gaussian is the maximum entropy distribution for given mean and covariance, therefore we assume the coding units to follow a multidimensional Gaussian. The difference of two Gaussians is measured by the Fréchet distance [\[16\]](#page-35-18) also known as Wasserstein-2 distance [\[58\]](#page-37-4). The Fréchet distance  $d(.,.)$  between the Gaussian with mean and covariance  $(m, C)$ obtained from  $p(.)$  and the Gaussian  $(m_w, C_w)$  obtained from  $p_w(.)$  is called the "Fréchet Inception Distance" (FID), which is given by  $[15]$ :

$$
d^{2}((\boldsymbol{m},\boldsymbol{C}),(\boldsymbol{m}_{w},\boldsymbol{C}_{w}))=\|\boldsymbol{m}-\boldsymbol{m}_{w}\|_{2}^{2}+\text{Tr}(\boldsymbol{C}+\boldsymbol{C}_{w}-2(\boldsymbol{C}\boldsymbol{C}_{w})^{1/2})\,. \tag{7}
$$

Next we show that the FID is consistent with increasing disturbances and human judgment on the CelebA dataset. We computed the  $(m_w, C_w)$  on all CelebA images, while for computing  $(m, C)$ we used 50,000 randomly selected samples. We considered following disturbances of the image  $X$ :

- 1. Gaussian noise: We constructed a matrix N with Gaussian noise scaled to  $[0, 255]$ . The noisy image is computed as  $(1 - \alpha)X + \alpha N$  for  $\alpha \in \{0, 0.25, 0.5, 0.75\}$ . The larger  $\alpha$  is, the larger is the noise added to the image, the larger is the disturbance of the image.
- 2. Gaussian blur: The image is convolved with a Gaussian kernel with standard deviation  $\alpha \in \{0, 1, 2, 4\}$ . The larger  $\alpha$  is, the larger is the disturbance of the image, that is, the more the image is smoothed.
- 3. Black rectangles: To an image five black rectangles are are added at randomly chosen locations. The rectangles cover parts of the image. The size of the rectangles is  $\alpha$ imagesize with  $\alpha \in \{0, 0.25, 0.5, 0.75\}$ . The larger  $\alpha$  is, the larger is the disturbance of the image, that is, the more of the image is covered by black rectangles.
- 4. Swirl: Parts of the image are transformed as a spiral, that is, as a swirl (whirlpool effect). Consider the coordinate  $(x, y)$  in the noisy (swirled) image for which we want to find the color. Towards this end we need the reverse mapping for the swirl transformation which gives the location which is mapped to  $(x, y)$ . We first compute polar coordinates relative to a center  $(x_0, y_0)$  given by the angle  $\theta = \arctan((y - y_0)/(x - x_0))$  and the radius  $r = \sqrt{(x - x_0)^2 + (y - y_0)^2}$ . We transform them according to  $\theta' = \theta + \alpha e^{-5r/(\ln 2\rho)}$ . Here  $\alpha$  is a parameter for the amount of swirl and  $\rho$  indicates the swirl extent in pixels. The original coordinates, where the color for  $(x, y)$  can be found, are  $x_{org} = x_0 + r \cos(\theta')$ and  $y_{\text{org}} = y_0 + r \sin(\theta')$ . We set  $(x_0, y_0)$  to the center of the image and  $\rho = 25$ . The disturbance level is given by the amount of swirl  $\alpha \in \{0, 1, 2, 4\}$ . The larger  $\alpha$  is, the larger is the disturbance of the image via the amount of swirl.
- 5. Salt and pepper noise: Some pixels of the image are set to black or white, where black is chosen with 50% probability (same for white). Pixels are randomly chosen for being flipped to white or black, where the ratio of pixel flipped to white or black is given by the noise level  $\alpha \in \{0, 0.1, 0.2, 0.3\}$ . The larger  $\alpha$  is, the larger is the noise added to the image via flipping pixels to white or black, the larger is the disturbance level.
- 6. ImageNet contamination: From each of the 1,000 ImageNet classes, 5 images are randomly chosen, which gives 5,000 ImageNet images. The images are ensured to be RGB and to have a minimal size of 256x256. A percentage of  $\alpha \in \{0, 0.25, 0.5, 0.75\}$  of the CelebA images has been replaced by ImageNet images.  $\alpha = 0$  means all images are from CelebA,  $\alpha = 0.25$  means that 75% of the images are from CelebA and 25% from ImageNet etc. The larger  $\alpha$  is, the larger is the disturbance of the CelebA dataset by contaminating it by ImageNet images. The larger the disturbance level is, the more the dataset deviates from the reference real world dataset.

We compare the Inception Score  $[53]$  with the FID. The Inception Score with m samples and K classes is

$$
\exp\big(\frac{1}{m}\sum_{i=1}^{m}\sum_{k=1}^{K}p(y_k \mid \boldsymbol{X}_i)\log\frac{p(y_k \mid \boldsymbol{X}_i)}{p(y_k)}\big) . \tag{8}
$$

The FID is a distance, while the Inception Score is a score. To compare FID and Inception Score, we transform the Inception Score to a distance, which we call "Inception Distance" (IND). This transformation to a distance is possible since the Inception Score has a maximal value. For zero probability  $p(y_k | X_i) = 0$ , we set the value  $p(y_k | X_i) \log \frac{p(y_k | X_i)}{p(y_k)} = 0$ . We can bound the log-term by

$$
\log \frac{p(y_k \mid \boldsymbol{X}_i)}{p(y_k)} \leqslant \log \frac{1}{1/m} = \log m. \tag{9}
$$

Using this bound, we obtain an upper bound on the Inception Score:

$$
\exp\big(\frac{1}{m}\sum_{i=1}^{m}\sum_{k=1}^{K}p(y_k \mid \boldsymbol{X}_i)\log\frac{p(y_k \mid \boldsymbol{X}_i)}{p(y_k)}\big) \tag{10}
$$

$$
\leqslant \exp\left(\log m \frac{1}{m} \sum_{i=1}^{m} \sum_{k=1}^{K} p(y_k \mid \boldsymbol{X}_i)\right) \tag{11}
$$

$$
= \exp\left(\log m \frac{1}{m} \sum_{i=1}^{m} 1\right) = m. \tag{12}
$$

The upper bound is tight and achieved if  $m \leq K$  and every sample is from a different class and the sample is classified correctly with probability 1. The IND is computed "IND =  $m$  - Inception Score", therefore the IND is zero for a perfect subset of the ImageNet with  $m < K$  samples, where each sample stems from a different class. Therefore both distances should increase with increasing disturbance level. In Figure [A8](#page-12-0) we present the evaluation for each kind of disturbance. The larger the disturbance level is, the larger the FID and IND should be. In Figure [A9,](#page-13-0) [A10,](#page-13-1) [A11,](#page-14-0) and [A11](#page-14-0) we show examples of images generated with DCGAN trained on CelebA with FIDs 500, 300, 133, 100, 45, 13, and FID 3 achieved with WGAN-GP on CelebA.

Image /page/12/Figure/0 description: The image displays a grid of six plots, each showing two graphs. The left graph in each row plots FID (Fréchet Inception Distance) against disturbance level, with disturbance levels ranging from 0 to 3. The right graph in each row plots IND (Inception Score) against disturbance level, also from 0 to 3. Each plot includes a line graph connecting data points marked with stars, and a series of small images above the graphs illustrating the effect of increasing disturbance levels. The first row shows Gaussian noise, with FID increasing from 0 to approximately 250 and IND fluctuating between 2 and 1.75. The second row shows blurring, with FID increasing from 0 to approximately 250 and IND peaking at 0.5 at disturbance level 2. The third row shows masked regions, with FID increasing from 0 to approximately 250 and IND decreasing from 0.75 to 0.5. The fourth row shows rotational distortion, with FID increasing from 0 to approximately 150 and IND peaking at 0.4 at disturbance level 2. The fifth row shows speckle noise, with FID increasing sharply from 0 to approximately 350 at disturbance level 1 and then plateauing, while IND increases from 1.75 to 2.25. The sixth row shows a mix of distortions including color shifts, pixelation, and object changes, with FID increasing from 0 to approximately 150 and IND decreasing from 70 to 5.

<span id="page-12-0"></span>Figure A8: Left: FID and right: Inception Score are evaluated for first row: Gaussian noise, second row: Gaussian blur, third row: implanted black rectangles, fourth row: swirled images, fifth row. salt and pepper noise, and sixth row: the CelebA dataset contaminated by ImageNet images. Left is the smallest disturbance level of zero, which increases to the highest level at right. The FID captures the disturbance level very well by monotonically increasing whereas the Inception Score fluctuates, stays flat or even, in the worst case, decreases.  $\frac{13}{13}$ 

<span id="page-13-0"></span>Image /page/13/Picture/0 description: The image displays a grid of generated faces on the right side, arranged in 8 rows and 8 columns. The faces are varied in appearance, with different hair colors, skin tones, and facial features. Some faces are clearer than others, suggesting a generative process. To the left of the faces, there is a large gray area filled with a subtle, repeating pattern of small squares or pixels, which appears to be a placeholder or a less developed part of the generated output.

Figure A9: Samples generated from DCGAN trained on CelebA with different FIDs. Left: FID 500 and Right: FID 300.

<span id="page-13-1"></span>Image /page/13/Picture/2 description: A grid of 60 faces, arranged in 6 rows and 10 columns. Each face is a small, square image, and the overall impression is of a collection of diverse individuals, though the resolution is low, giving the faces a somewhat abstract or painterly quality. The lighting and color tones vary across the grid, with some faces appearing brighter and others darker. The faces are mostly looking forward, with a variety of expressions and hairstyles visible.

Figure A10: Samples generated from DCGAN trained on CelebA with different FIDs. Left: FID 133 and Right: FID 100.

Image /page/14/Picture/0 description: The image is a grid of 48 portraits of people, arranged in 6 rows and 8 columns. The portraits appear to be generated by an AI, as they have a slightly abstract or distorted quality. The people in the portraits are diverse in terms of age, gender, and ethnicity. Some portraits are clearer than others, with some showing more detail in facial features and hair than others. The background of each portrait is a solid color or a simple gradient.

Figure A11: Samples generated from DCGAN trained on CelebA with different FIDs. Left: FID 45 and Right: FID 13.

<span id="page-14-1"></span><span id="page-14-0"></span>Image /page/14/Picture/2 description: A grid of 42 portraits of people, arranged in 7 rows and 6 columns. The portraits are of varying quality, with some appearing more realistic than others. The overall impression is a collection of generated faces, likely from a machine learning model.

Figure A12: Samples generated from WGAN-GP trained on CelebA with a FID of 3.

## <span id="page-15-1"></span>A2 Two Time-Scale Stochastic Approximation Algorithms

Stochastic approximation algorithms are iterative procedures to find a root or a stationary point (minimum, maximum, saddle point) of a function when only noisy observations of its values or its derivatives are provided. Two time-scale stochastic approximation algorithms are two coupled iterations with different step sizes. For proving convergence of these interwoven iterates it is assumed that one step size is considerably smaller than the other. The slower iterate (the one with smaller step size) is assumed to be slow enough to allow the fast iterate converge while being perturbed by the the slower. The perturbations of the slow should be small enough to ensure convergence of the faster.

The iterates map at time step  $n \geq 0$  the fast variable  $w_n \in \mathbb{R}^k$  and the slow variable  $\theta_n \in \mathbb{R}^m$  to their new values:

<span id="page-15-4"></span><span id="page-15-3"></span>
$$
\theta_{n+1} = \theta_n + a(n) \left( \boldsymbol{h}(\theta_n, \boldsymbol{w}_n, \boldsymbol{Z}_n^{(\theta)}) + \boldsymbol{M}_n^{(\theta)} \right), \qquad (13)
$$

$$
\boldsymbol{w}_{n+1} = \boldsymbol{w}_n + b(n) \left( \boldsymbol{g}(\boldsymbol{\theta}_n, \boldsymbol{w}_n, \boldsymbol{Z}_n^{(w)}) + \boldsymbol{M}_n^{(w)} \right) . \tag{14}
$$

The iterates use

- $h(.) \in \mathbb{R}^m$ : mapping for the slow iterate Eq. [\(13\)](#page-15-3),
- $g(.) \in \mathbb{R}^k$ : mapping for the fast iterate Eq. [\(14\)](#page-15-4),
- $a(n)$ : step size for the slow iterate Eq. [\(13\)](#page-15-3),
- $b(n)$ : step size for the fast iterate Eq. [\(14\)](#page-15-4),
- $M_n^{(\theta)}$ : additive random Markov process for the slow iterate Eq. [\(13\)](#page-15-3),
- $M_n^{(w)}$ : additive random Markov process for the fast iterate Eq. [\(14\)](#page-15-4),
- $\mathbf{Z}_n^{(\theta)}$ : random Markov process for the slow iterate Eq. [\(13\)](#page-15-3),
- $\mathbf{Z}_n^{(w)}$ : random Markov process for the fast iterate Eq. [\(14\)](#page-15-4).

<span id="page-15-0"></span>

#### A2.1 Convergence of Two Time-Scale Stochastic Approximation Algorithms

<span id="page-15-2"></span>

##### A2.1.1 Additive Noise

The first result is from Borkar 1997 [\[9\]](#page-34-4) which was generalized in Konda and Borkar 1999 [\[31\]](#page-35-20). Borkar considered the iterates:

$$
\theta_{n+1} = \theta_n + a(n) \left( h(\theta_n, w_n) + M_n^{(\theta)} \right), \qquad (15)
$$

$$
\boldsymbol{w}_{n+1} = \boldsymbol{w}_n + b(n) \left( \boldsymbol{g}(\boldsymbol{\theta}_n, \boldsymbol{w}_n) + \boldsymbol{M}_n^{(w)} \right) . \tag{16}
$$

Assumptions. We make the following assumptions:

- (A1) Assumptions on the update functions: The functions  $h : \mathbb{R}^{k+m} \mapsto \mathbb{R}^m$  and  $g : \mathbb{R}^{k+m} \mapsto \mathbb{R}^k$ are Lipschitz.
- (A2) Assumptions on the learning rates:

<span id="page-15-6"></span><span id="page-15-5"></span>
$$
\sum_{n} a(n) = \infty , \quad \sum_{n} a^{2}(n) < \infty , \tag{17}
$$

$$
\sum_{n} b(n) = \infty , \quad \sum_{n} b^{2}(n) < \infty , \tag{18}
$$

$$
a(n) = o(b(n)), \qquad (19)
$$

(A3) Assumptions on the noise: For the increasing  $\sigma$ -field

$$
\mathcal{F}_n = \sigma(\boldsymbol{\theta}_l, \boldsymbol{w}_l, \boldsymbol{M}_l^{(\theta)}, \boldsymbol{M}_l^{(w)}, l \leqslant n), n \geqslant 0,
$$

the sequences of random variables  $(M_n^{(\theta)}, \mathcal{F}_n)$  and  $(M_n^{(w)}, \mathcal{F}_n)$  satisfy

$$
\sum_{n} a(n) M_n^{(\theta)} < \infty \text{ a.s.} \tag{20}
$$

$$
\sum_{n} b(n) \, \mathbf{M}_n^{(w)} < \infty \text{ a.s.} \,. \tag{21}
$$

(A4) Assumption on the existence of a solution of the fast iterate: For each  $\theta \in \mathbb{R}^m$ , the ODE

$$
\dot{\boldsymbol{w}}(t) = \boldsymbol{g}(\boldsymbol{\theta}, \boldsymbol{w}(t)) \tag{22}
$$

has a unique global asymptotically stable equilibrium  $\lambda(\theta)$  such that  $\lambda : \mathbb{R}^m \mapsto \mathbb{R}^k$  is Lipschitz.

(A5) Assumption on the existence of a solution of the slow iterate: The ODE

$$
\dot{\boldsymbol{\theta}}(t) = \boldsymbol{h}\big(\boldsymbol{\theta}(t), \boldsymbol{\lambda}(\boldsymbol{\theta}(t))\big) \tag{23}
$$

has a unique global asymptotically stable equilibrium  $\theta^*$ .

(A6) Assumption of bounded iterates:

$$
\sup_n \|\boldsymbol{\theta}_n\| < \infty \,, \tag{24}
$$

$$
\sup_n \|w_n\| < \infty \, . \tag{25}
$$

##### Convergence Theorem The next theorem is from Borkar 1997 [9].

Theorem 3 (Borkar). *If the assumptions are satisfied, then the iterates Eq.* [\(15\)](#page-15-5) *and Eq.* [\(16\)](#page-15-6) *converge to* (θ ∗ ,λ(θ ∗ )) *a.s.*

## Comments

(C1) According to Lemma 2 in [\[7\]](#page-34-3) Assumption (A3) is fulfilled if  $\{M_n^{(\theta)}\}$  is a martingale difference sequence w.r.t  $\mathcal{F}_n$  with

$$
\mathrm{E}\left[\|\mathbf{M}_n^{(\theta)}\|^2\mid\mathcal{F}_n^{(\theta)}\right]\,\leqslant\, B_1
$$

and  $\{M_n^{(w)}\}$  is a martingale difference sequence w.r.t  $\mathcal{F}_n$  with

$$
{\rm E}\left[\|{\mathbf M}_n^{(w)}\|^2\mid \mathcal{F}_n^{(w)}\right]\;\leqslant\; B_2\;,
$$

where  $B_1$  and  $B_2$  are positive deterministic constants.

- (C2) Assumption (A3) holds for mini-batch learning which is the most frequent case of stochastic gradient. The batch gradient is  $G_n := \nabla_{\theta}(\frac{1}{N} \sum_{i=1}^N f(x_i, \theta)), 1 \leq i \leq N$  and the minibatch gradient for batch size s is  $h_n := \nabla_{\theta}(\frac{1}{s} \sum_{i=1}^{s} f(x_{u_i}, \theta)), 1 \leq u_i \leq N$ , where the indexes  $u_i$  are randomly and uniformly chosen. For the noise  $M_n^{(\theta)} := h_n - G_n$  we have  $\mathbb{E}[M_n^{(\theta)}] = \mathbb{E}[h_n] - G_n = G_n - G_n = 0$ . Since the indexes are chosen without knowing past events, we have a martingale difference sequence. For bounded gradients we have bounded  $||M_n^{(\theta)}||^2$ .
- (C3) We address assumption (A4) with weight decay in two ways: (I) Weight decay avoids problems with a discriminator that is region-wise constant and, therefore, does not have a locally stable generator. If the generator is perfect, then the discriminator is 0.5 everywhere. For generator with mode collapse, (i) the discriminator is 1 in regions without generator examples, (ii) 0 in regions with generator examples only, (iii) is equal to the local ratio

of real world examples for regions with generator and real world examples. Since the discriminator is locally constant, the generator has gradient zero and cannot improve. Also the discriminator cannot improve, since it has minimal error given the current generator. However, without weight decay the Nash Equilibrium is not stable since the second order derivatives are zero, too. (II) Weight decay avoids that the generator is driven to infinity with unbounded weights. For example a linear discriminator can supply a gradient for the generator outside each bounded region.

- (C4) The main result used in the proof of the theorem relies on work on perturbations of ODEs according to Hirsch 1989 [\[24\]](#page-35-13).
- (C5) Konda and Borkar 1999 [\[31\]](#page-35-20) generalized the convergence proof to distributed asynchronous update rules.
- (C6) Tadić relaxed the assumptions for showing convergence  $[54]$  $[54]$ . In particular the noise assumptions (Assumptions A2 in  $[54]$ ) do not have to be martingale difference sequences and are more general than in  $[9]$ . In another result the assumption of bounded iterates is not necessary if other assumptions are ensured  $[54]$ . Finally, Tadić considers the case of non-additive noise  $[54]$ . Tadić does not provide proofs for his results. We were not able to find such proofs even in other publications of Tadić.

<span id="page-17-0"></span>

##### A2.1.2 Linear Update, Additive Noise, and Markov Chain

In contrast to the previous subsection, we assume that an additional Markov chain influences the iterates [\[30,](#page-35-10) [32\]](#page-35-11). The Markov chain allows applications in reinforcement learning, in particular in actor-critic setting where the Markov chain is used to model the environment. The slow iterate is the actor update while the fast iterate is the critic update. For reinforcement learning both the actor and the critic observe the environment which is driven by the actor actions. The environment observations are assumed to be a Markov chain. The Markov chain can include eligibility traces which are modeled as explicit states in order to keep the Markov assumption.

The Markov chain is the sequence of observations of the environment which progresses via transition probabilities. The transitions are not affected by the critic but by the actor.

Konda et al. considered the iterates [\[30,](#page-35-10) [32\]](#page-35-11):

$$
\boldsymbol{\theta}_{n+1} = \boldsymbol{\theta}_n + a(n) \, \boldsymbol{H}_n \,, \tag{26}
$$

$$
\mathbf{w}_{n+1} = \mathbf{w}_n + b(n) \left( \mathbf{g}(\mathbf{Z}_n^{(w)}; \theta_n) + \mathbf{G}(\mathbf{Z}_n^{(w)}; \theta_n) \mathbf{w}_n + \mathbf{M}_n^{(w)} \mathbf{w}_n \right). \tag{27}
$$

 $H_n$  is a random process that drives the changes of  $\theta_n$ . We assume that  $H_n$  is a slow enough process. We have a linear update rule for the fast iterate using the vector function  $g(.) \in \mathbb{R}^k$  and the matrix function  $\boldsymbol{G}(.)\in\mathbb{R}^{\bar{k}\times k}$ .

Assumptions. We make the following assumptions:

(A1) Assumptions on the Markov process, that is, the transition kernel: The stochastic process  $\mathbf{Z}_n^{(w)}$  takes values in a Polish (complete, separable, metric) space  $\mathbb Z$  with the Borel  $\sigma$ -field

<span id="page-17-2"></span><span id="page-17-1"></span>
$$
\mathcal{F}_n = \sigma(\boldsymbol{\theta}_l, \boldsymbol{w}_l, \boldsymbol{Z}_l^{(w)}, \boldsymbol{H}_l, l \leqslant n), n \geqslant 0.
$$

For every measurable set  $A \subset \mathbb{Z}$  and the parametrized transition kernel  $P(.;\theta_n)$  we have:

$$
P(\mathbf{Z}_{n+1}^{(w)} \in A \mid \mathcal{F}_n) = P(\mathbf{Z}_{n+1}^{(w)} \in A \mid \mathbf{Z}_n^{(w)}; \theta_n) = P(\mathbf{Z}_n^{(w)}, A; \theta_n).
$$
 (28)  
We define for every measurable function  $f$ 

$$
\mathrm{P}_{\boldsymbol{\theta}}f(\boldsymbol{z})\;:=\;\int \mathrm{P}(\boldsymbol{z},{\rm d}\bar{\boldsymbol{z}};\boldsymbol{\theta}_n)\;f(\bar{\boldsymbol{z}})\;.
$$

(A2) Assumptions on the learning rates:

$$
\sum_{n} b(n) = \infty , \quad \sum_{n} b^{2}(n) < \infty , \tag{29}
$$

$$
\sum_{n} \left(\frac{a(n)}{b(n)}\right)^d < \infty \,,\tag{30}
$$

for some  $d > 0$ .

(A3) Assumptions on the noise: The sequence  $M_n^{(w)}$  is a  $k \times k$ -matrix valued  $\mathcal{F}_n$ -martingale difference with bounded moments:

$$
\mathrm{E}\left[\mathbf{M}_{n}^{(w)}\mid\mathcal{F}_{n}\right]=0\,,\tag{31}
$$

$$
\sup_{n} \mathcal{E}\left[\left\|\mathbf{M}_{n}^{(w)}\right\|^{d}\right] < \infty \,,\,\forall d > 0 \,.
$$
\n(32)

We assume slowly changing  $\theta$ , therefore the random process  $H_n$  satisfies

$$
\sup_{n} \mathbf{E}\left[\|\mathbf{H}_{n}\|^{d}\right] < \infty \,,\,\forall d > 0 \,.
$$
\n(33)

(A4) Assumption on the existence of a solution of the fast iterate: We assume the existence of a solution to the Poisson equation for the fast iterate. For each  $\theta \in \mathbb{R}^m$ , there exist functions  $\bar{g}(\boldsymbol{\theta}) \in \mathbb{R}^k, \bar{G}(\boldsymbol{\theta}) \in \mathbb{R}^{k \times k}, \hat{g}(\boldsymbol{z}; \boldsymbol{\theta}):~ \mathbb{Z} \rightarrow \mathbb{R}^k, \text{and}~ \hat{G}(\boldsymbol{z}; \boldsymbol{\theta}):~ \mathbb{Z} \rightarrow \mathbb{R}^{k \times k}$  that satisfy the Poisson equations:

$$
\hat{g}(z;\theta) = g(z;\theta) - \bar{g}(\theta) + (\mathcal{P}_{\theta}\hat{g}(.;\theta))(z) , \qquad (34)
$$

$$
\hat{G}(z;\theta) = G(z;\theta) - \bar{G}(\theta) + (P_{\theta}\hat{G}(.;\theta))(z) . \qquad (35)
$$

- (A5) Assumptions on the update functions and solutions to the Poisson equation:
  - (a) Boundedness of solutions: For some constant C and for all  $\theta$ :

$$
\max\{\|\bar{g}(\boldsymbol{\theta})\|\}\ \leqslant\ C\ ,\tag{36}
$$

$$
\max\{\|\bar{G}(\boldsymbol{\theta})\|\}\ \leqslant\ C\ .
$$

(b) Boundedness in expectation: All moments are bounded. For any  $d > 0$ , there exists  $C_d > 0$  such that

$$
\sup_{n} \mathbf{E}\left[\left\|\hat{\boldsymbol{g}}(\boldsymbol{Z}_{n}^{(w)};\boldsymbol{\theta})\right\|^{d}\right] \leqslant C_{d},\tag{38}
$$

$$
\sup_{n} \mathcal{E}\left[\left\|\boldsymbol{g}(\boldsymbol{Z}_{n}^{(w)};\boldsymbol{\theta})\right\|^{d}\right] \leqslant C_{d},\tag{39}
$$

$$
\sup_{n} \mathcal{E}\left[\left\|\hat{G}(Z_n^{(w)};\theta)\right\|^d\right] \leqslant C_d,
$$
\n(40)

$$
\sup_{n} \mathcal{E}\left[\left\|G(\mathbf{Z}_{n}^{(w)};\boldsymbol{\theta})\right\|^{d}\right] \leqslant C_{d}.
$$
\n(41)

(c) Lipschitz continuity of solutions: For some constant  $C > 0$  and for all  $\theta, \bar{\theta} \in \mathbb{R}^m$ :

$$
\left\|\bar{g}(\theta) - \bar{g}(\bar{\theta})\right\| \leqslant C \left\|\theta - \bar{\theta}\right\|,\tag{42}
$$

$$
\left\|\bar{G}(\theta) - \bar{G}(\bar{\theta})\right\| \leqslant C \left\|\theta - \bar{\theta}\right\|.
$$
 (43)

(d) Lipschitz continuity in expectation: There exists a positive measurable function  $C(.)$ on  $\mathbb Z$  such that

$$
\sup_{n} \mathcal{E}\left[C(\mathbf{Z}_{n}^{(w)})^{d}\right] < \infty \,, \,\forall d > 0 \,. \tag{44}
$$

Function  $C(.)$  gives the Lipschitz constant for every  $z$ :

$$
\left\| (P_{\theta}\hat{g}(.;\theta))(z) \ - \ (P_{\bar{\theta}}\hat{g}(.;\bar{\theta}))(z) \right\| \ \leqslant \ C(z) \left\| \theta - \bar{\theta} \right\|, \tag{45}
$$

$$
\left\| (P_{\theta}\hat{G}(.;\theta))(z) - (P_{\bar{\theta}}\hat{G}(.;\bar{\theta}))(z) \right\| \leqslant C(z) \left\| \theta - \bar{\theta} \right\|.
$$
 (46)

(e) Uniform positive definiteness: There exists some  $\alpha > 0$  such that for all  $\mathbf{w} \in \mathbb{R}^k$  and  $\boldsymbol{\theta} \in \mathbb{R}^m$ :

$$
\boldsymbol{w}^T \,\bar{G}(\boldsymbol{\theta}) \, \boldsymbol{w} \, \geqslant \, \alpha \, \|\boldsymbol{w}\|^2 \,. \tag{47}
$$

Convergence Theorem. We report Theorem 3.2 (see also Theorem 7 in [\[32\]](#page-35-11)) and Theorem 3.13 from [\[30\]](#page-35-10):

Theorem 4 (Konda & Tsitsiklis). *If the assumptions are satisfied, then for the iterates Eq.* [\(26\)](#page-17-1) *and Eq.* [\(27\)](#page-17-2) *holds:*

$$
\lim_{n\to\infty}\left\|\bar{G}(\boldsymbol{\theta}_n)\,\boldsymbol{w}_n\,-\,\bar{g}(\boldsymbol{\theta}_n)\right\|\,=\,0\quad a.s.\,,\tag{48}
$$

$$
\lim_{n\to\infty}\left\|\boldsymbol{w}_n\,-\,\bar{G}^{-1}(\boldsymbol{\theta}_n)\,\bar{g}(\boldsymbol{\theta}_n)\right\|\,=\,0\,.
$$
\n(49)

##### Comments.

- (C1) The proofs only use the boundedness of the moments of  $H_n$  [\[30,](#page-35-10) [32\]](#page-35-11), therefore  $H_n$  may depend on  $w_n$ . In his PhD thesis [\[30\]](#page-35-10), Vijaymohan Konda used this framework for the actor-critic learning, where  $H_n$  drives the updates of the actor parameters  $\theta_n$ . However, the actor updates are based on the current parameters  $w_n$  of the critic.
- (C2) The random process  $Z_n^{(w)}$  can affect  $H_n$  as long as boundedness is ensured.
- (C3) Nonlinear update rule.  $g(Z_n^{(w)}; \theta_n) + G(Z_n^{(w)}; \theta_n) w_n$  can be viewed as a linear approxi-mation of a nonlinear update rule. The nonlinear case has been considered in [\[30\]](#page-35-10) where additional approximation errors due to linearization were addressed. These errors are treated in the given framework [\[30\]](#page-35-10).

<span id="page-19-0"></span>

##### A2.1.3 Additive Noise and Controlled Markov Processes

The most general iterates use nonlinear update functions  $g$  and  $h$ , have additive noise, and have controlled Markov processes [\[28\]](#page-35-9).

$$
\theta_{n+1} = \theta_n + a(n) \left( \boldsymbol{h}(\theta_n, \boldsymbol{w}_n, \boldsymbol{Z}_n^{(\theta)}) + \boldsymbol{M}_n^{(\theta)} \right), \qquad (50)
$$

$$
\mathbf{w}_{n+1} = \mathbf{w}_n + b(n) \left( \mathbf{g}(\theta_n, \mathbf{w}_n, \mathbf{Z}_n^{(w)}) + \mathbf{M}_n^{(w)} \right). \tag{51}
$$

**Required Definitions.** *Marchaud Map*: A set-valued map  $h : \mathbb{R}^l \to \{\text{subsets of } \mathbb{R}^k\}$  is called a *Marchaud map* if it satisfies the following properties:

- (i) For each  $\theta \in \mathbb{R}^l$ ,  $h(\theta)$  is convex and compact.
- (ii) *(point-wise boundedness)* For each  $\boldsymbol{\theta} \in \mathbb{R}^l$ , sup  $\sup_{\boldsymbol{w}\in\boldsymbol{h}(\boldsymbol{\theta})} \|\boldsymbol{w}\| < K\left(1 + \|\boldsymbol{\theta}\|\right)$  for some  $K > 0$ .
- (iii) h is an *upper-semicontinuous* map.

We say that h is upper-semicontinuous, if given sequences  $\{\theta_n\}_{n\geq 1}$  (in  $\mathbb{R}^l$ ) and  $\{y_n\}_{n\geq 1}$  $\mathfrak{m}(\text{in } \mathbb{R}^k)$  with  $\theta_n \to \theta$ ,  $y_n \to y$  and  $y_n \in h(\theta_n)$ ,  $n \geq 1$ ,  $y \in h(\theta)$ . In other words, the graph of  $h$ ,  $\{(x, y) : y \in h(x), x \in \mathbb{R}^l\}$ , is closed in  $\mathbb{R}^l \times \mathbb{R}^k$ .

If the set-valued map  $H : \mathbb{R}^m \to \{\text{subsets of } \mathbb{R}^m\}$  is Marchaud, then the differential inclusion (DI) given by

<span id="page-19-1"></span>
$$
\dot{\boldsymbol{\theta}}(t) \in H(\boldsymbol{\theta}(t)) \tag{52}
$$

is guaranteed to have at least one solution that is absolutely continuous. If  $\Theta$  is an absolutely continuous map satisfying Eq. [\(52\)](#page-19-1) then we say that  $\Theta \in \Sigma$ .

*Invariant Set*:  $M \subseteq \mathbb{R}^m$  is *invariant* if for every  $\theta \in M$  there exists a trajectory,  $\Theta$ , entirely in M with  $\Theta(0) = \theta$ . In other words,  $\Theta \in \Sigma$  with  $\Theta(t) \in M$ , for all  $t \geq 0$ .

*Internally Chain Transitive Set:*  $M \subset \mathbb{R}^m$  is said to be internally chain transitive if M is compact and for every  $\theta, y \in M$ ,  $\epsilon > 0$  and  $T > 0$  we have the following: There exist  $\Phi^1, \ldots, \Phi^n$  that are n solutions to the differential inclusion  $\dot{\theta}(t) \in h(\theta(t))$ , a sequence  $\theta_1(=\theta), \ldots, \theta_{n+1}(=\mathbf{y}) \subset M$  and *n* real numbers  $t_1, t_2, \ldots, t_n$  greater than T such that:  $\Phi_{t_i}^i(\theta_i) \in N^{\epsilon}(\theta_{i+1})$  where  $N^{\epsilon}(\theta)$  is the open *e*-neighborhood of  $\theta$  and  $\Phi^i_{[0,t_i]}(\theta_i) \subset M$  for  $1 \leq i \leq n$ . The sequence  $(\theta_1(=\theta), \dots, \theta_{n+1}(=\boldsymbol{y}))$ is called an  $(\epsilon, T)$  chain in M from  $\theta$  to y.

Assumptions. We make the following assumptions [\[28\]](#page-35-9):

(A1) Assumptions on the controlled Markov processes: The controlled Markov process  $\{Z_n^{(w)}\}$ takes values in a compact metric space  $S^{(w)}$ . The controlled Markov process  $\{Z_n^{(\theta)}\}$ takes values in a compact metric space  $S^{(\theta)}$ . Both processes are controlled by the iterate sequences  $\{\theta_n\}$  and  $\{w_n\}$ . Furthermore  $\{Z_n^{(w)}\}$  is additionally controlled by a random process  $\{A_n^{(w)}\}$  taking values in a compact metric space  $U^{(w)}$  and  $\{Z_n^{(\theta)}\}$  is additionally controlled by a random process  $\{A_n^{(\theta)}\}$  taking values in a compact metric space  $U^{(\theta)}$ . The  $\{Z_n^{(\theta)}\}$  dynamics is

$$
P(\mathbf{Z}_{n+1}^{(\theta)} \in B^{(\theta)} | \mathbf{Z}_l^{(\theta)}, \mathbf{A}_l^{(\theta)}, \boldsymbol{\theta}_l, \mathbf{w}_l, l \leqslant n) = \int_{B^{(\theta)}} p^{(\theta)}(\mathrm{d}z | \mathbf{Z}_n^{(\theta)}, \mathbf{A}_n^{(\theta)}, \boldsymbol{\theta}_n, \mathbf{w}_n), n \geqslant 0,
$$
\n(53)

for  $B^{(\theta)}$  Borel in  $S^{(\theta)}$ . The  $\{Z_n^{(w)}\}$  dynamics is

$$
P(\mathbf{Z}_{n+1}^{(w)} \in B^{(w)} | \mathbf{Z}_{l}^{(w)}, \mathbf{A}_{l}^{(w)}, \theta_{l}, \mathbf{w}_{l}, l \leqslant n) = \int_{B^{(w)}} p^{(w)}(\mathrm{d}z | \mathbf{Z}_{n}^{(w)}, \mathbf{A}_{n}^{(w)}, \theta_{n}, \mathbf{w}_{n}), n \geqslant 0,
$$
\n(54)

for  $B^{(w)}$  Borel in  $S^{(w)}$ .

(A2) Assumptions on the update functions:  $h: \mathbb{R}^{m+k} \times S^{(\theta)} \to \mathbb{R}^m$  is jointly continuous as well as Lipschitz in its first two arguments uniformly w.r.t. the third. The latter condition means that

$$
\forall \boldsymbol{z}^{(\theta)} \in S^{(\theta)}: \|h(\boldsymbol{\theta}, \boldsymbol{w}, \boldsymbol{z}^{(\theta)}) - h(\boldsymbol{\theta}', \boldsymbol{w}', \boldsymbol{z}^{(\theta)})\| \leqslant L^{(\theta)} \left(\|\boldsymbol{\theta} - \boldsymbol{\theta}'\| + \|\boldsymbol{w} - \boldsymbol{w}'\|\right). \tag{55}
$$

Note that the Lipschitz constant  $L^{(\theta)}$  does not depend on  $\mathbf{z}^{(\theta)}$ .

 $g: \mathbb{R}^{k+m} \times S^{(w)} \to \mathbb{R}^k$  is jointly continuous as well as Lipschitz in its first two arguments uniformly w.r.t. the third. The latter condition means that

$$
\forall z^{(w)} \in S^{(w)}: \|g(\theta, w, z^{(w)}) - g(\theta', w', z^{(w)})\| \leq L^{(w)} (\|\theta - \theta'\| + \|w - w'\|).
$$
\n(56)

Note that the Lipschitz constant  $L^{(w)}$  does not depend on  $\boldsymbol{z}^{(w)}$ .

(A3) Assumptions on the additive noise:  $\{M_n^{(\theta)}\}$  and  $\{M_n^{(w)}\}$  are martingale difference sequence with second moments bounded by  $K(1 + ||\boldsymbol{\theta}_n||^2 + ||\boldsymbol{w}_n||^2)$ . More precisely,  $\{M_n^{(\theta)}\}$  is a martingale difference sequence w.r.t. increasing  $\sigma$ -fields

$$
\mathcal{F}_n = \sigma(\boldsymbol{\theta}_l, \boldsymbol{w}_l, \boldsymbol{M}_l^{(\theta)}, \boldsymbol{M}_l^{(w)}, \boldsymbol{Z}_l^{(\theta)}, \boldsymbol{Z}_l^{(w)}, l \leqslant n), \ n \geqslant 0 \,, \tag{57}
$$

satisfying

$$
\mathrm{E}\left[\|\mathbf{M}_{n+1}^{(\theta)}\|^2\mid\mathcal{F}_n\right] \leqslant K\left(1\ +\|\boldsymbol{\theta}_n\|^2\ +\|\boldsymbol{w}_n\|^2\right),\tag{58}
$$

for  $n \geq 0$  and a given constant  $K > 0$ .

 $\{M_n^{(w)}\}$  is a martingale difference sequence w.r.t. increasing  $\sigma$ -fields

$$
\mathcal{F}_n = \sigma(\boldsymbol{\theta}_l, \boldsymbol{w}_l, \boldsymbol{M}_l^{(\theta)}, \boldsymbol{M}_l^{(w)}, \boldsymbol{Z}_l^{(\theta)}, \boldsymbol{Z}_l^{(w)}, l \leqslant n), \ n \geqslant 0 \,, \tag{59}
$$

satisfying

$$
\mathbf{E}\left[\|\boldsymbol{M}_{n+1}^{(w)}\|^2 \mid \mathcal{F}_n\right] \leqslant K\left(1 + \|\boldsymbol{\theta}_n\|^2 + \|\boldsymbol{w}_n\|^2\right),\tag{60}
$$

for  $n \geq 0$  and a given constant  $K > 0$ .

(A4) Assumptions on the learning rates:

$$
\sum_{n} a(n) = \infty , \quad \sum_{n} a^{2}(n) < \infty , \tag{61}
$$

$$
\sum_{n} b(n) = \infty , \quad \sum_{n} b^{2}(n) < \infty , \tag{62}
$$

$$
a(n) = o(b(n)), \qquad (63)
$$

Furthermore,  $a(n)$ ,  $b(n)$ ,  $n \ge 0$  are non-increasing.

(A5) Assumptions on the controlled Markov processes, that is, the transition kernels: The stateaction map

$$
S^{(\theta)} \times U^{(\theta)} \times \mathbb{R}^{m+k} \ni (\boldsymbol{z}^{(\theta)}, \boldsymbol{a}^{(\theta)}, \boldsymbol{\theta}, \boldsymbol{w}) \rightarrow \mathrm{p}^{(\theta)}(\mathrm{d}\boldsymbol{y} \mid \boldsymbol{z}^{(\theta)}, \boldsymbol{a}^{(\theta)}, \boldsymbol{\theta}, \boldsymbol{w}) \qquad (64)
$$

and the state-action map

$$
S^{(w)} \times U^{(w)} \times \mathbb{R}^{m+k} \ni (\boldsymbol{z}^{(w)}, \boldsymbol{a}^{(w)}, \boldsymbol{\theta}, \boldsymbol{w}) \rightarrow \mathrm{p}^{(w)}(\mathrm{d}\boldsymbol{y} \mid \boldsymbol{z}^{(w)}, \boldsymbol{a}^{(w)}, \boldsymbol{\theta}, \boldsymbol{w}) \qquad (65)
$$

are continuous.

(A6) Assumptions on the existence of a solution:

We consider *occupation measures* which give for the controlled Markov process the probability or density to observe a particular state-action pair from  $S \times U$  for given  $\theta$  and a given control policy  $\pi$ . We denote by  $D^{(w)}(\theta, w)$  the set of all ergodic occupation measures for the prescribed  $\theta$  and w on state-action space  $S^{(w)} \times U^{(\theta)}$  for the controlled Markov process  $\bm{Z}^{(w)}$  with policy  $\pi^{(w)}$ . Analogously we denote, by  $D^{(\theta)}(\bm{\theta},\bm{w})$  the set of all ergodic occupation measures for the prescribed  $\theta$  and w on state-action space  $S^{(\theta)} \times U^{(\theta)}$  for the controlled Markov process  $Z^{(\theta)}$  with policy  $\pi^{(\theta)}$ . Define

$$
\tilde{g}(\theta, w, \nu) = \int g(\theta, w, z) \, \nu(\mathrm{d}z, U^{(w)}) \tag{66}
$$

for  $\nu$  a measure on  $S^{(w)} \times U^{(w)}$  and the Marchaud map

$$
\hat{g}(\theta, w) = \{\tilde{g}(\theta, w, \nu) : \ \nu \in D^{(w)}(\theta, w)\} . \tag{67}
$$

We assume that the set  $D^{(w)}(\theta, w)$  is singleton, that is,  $\hat{g}(\theta, w)$  contains a single function and we use the same notation for the set and its single element. If the set is not a singleton, the assumption of a solution can be expressed by the differential inclusion  $\dot{w}(t) \in \hat{q}(\theta, w(t))$ [\[28\]](#page-35-9).

 $\forall \theta \in \mathbb{R}^m$ , the ODE

$$
\dot{\boldsymbol{w}}(t) = \hat{\boldsymbol{g}}(\boldsymbol{\theta}, \boldsymbol{w}(t)) \tag{68}
$$

has an asymptotically stable equilibrium  $\lambda(\theta)$  with domain of attraction  $G_{\theta}$  where  $\lambda$ :  $\mathbb{R}^m \to \mathbb{R}^k$  is a Lipschitz map with constant K. Moreover, the function  $V : G \to [0, \infty)$ is continuously differentiable where  $V(\theta,.)$  is the Lyapunov function for  $\lambda(\theta)$  and  $G =$  $\{(\theta, w) : w \in G_\theta, \theta \in \mathbb{R}^m\}$ . This extra condition is needed so that the set  $\{(\theta, \lambda(\theta)) :$  $\hat{\theta} \in \mathbb{R}^m$  becomes an asymptotically stable set of the coupled ODE

$$
\dot{\boldsymbol{w}}(t) = \hat{\boldsymbol{g}}(\boldsymbol{\theta}(t), \boldsymbol{w}(t)) \tag{69}
$$

$$
\dot{\boldsymbol{\theta}}(t) = 0. \tag{70}
$$

(A7) Assumption of bounded iterates:

$$
\sup_n \|\boldsymbol{\theta}_n\| < \infty \text{ a.s. }, \tag{71}
$$

$$
\sup_n \|w_n\| < \infty \text{ a.s.}
$$
 (72)

### Convergence Theorem. The following theorem is from Karmakar & Bhatnagar $[28]$ :

**Theorem 5** (Karmakar & Bhatnagar). *Under above assumptions if for all*  $\theta \in \mathbb{R}^m$ , with probability *1,*  $\{w_n\}$  *belongs to a compact subset*  $Q_\theta$  *(depending on the sample point) of*  $G_\theta$  "eventually", then

$$
(\theta_n, \boldsymbol{w}_n) \rightarrow \bigcup_{\boldsymbol{\theta}^* \in A_0} (\boldsymbol{\theta}^*, \boldsymbol{\lambda}(\boldsymbol{\theta}^*)) \ \ a.s. \quad \text{as } n \rightarrow \infty \ , \tag{73}
$$

*where*  $A_0 = \bigcap_{t \geq 0} {\overline{\{\theta(s) : s \geq t\}}}$  *which is almost everywhere an internally chain transitive set of the differential inclusion*

$$
\dot{\boldsymbol{\theta}}(t) \in \hat{\boldsymbol{h}}(\boldsymbol{\theta}(t)), \tag{74}
$$

*where*  $\hat{\boldsymbol{h}}(\boldsymbol{\theta}) = {\{\tilde{\boldsymbol{h}}(\boldsymbol{\theta}, \boldsymbol{\lambda}(\boldsymbol{\theta}), \boldsymbol{\nu}) : \boldsymbol{\nu} \in D^{(w)}(\boldsymbol{\theta}, \boldsymbol{\lambda}(\boldsymbol{\theta}))\}}.$ 

##### Comments.

- (C1) This framework allows to show convergence for gradient descent methods beyond stochastic gradient like for the ADAM procedure where current learning parameters are memorized and updated. The random processes  $Z^{(w)}$  and  $Z^{(\theta)}$  may track the current learning status for the fast and slow iterate, respectively.
- (C2) Stochastic regularization like dropout is covered via the random processes  $A^{(w)}$  and  $A^{(\theta)}$ .

<span id="page-22-0"></span>

### A2.2 Rate of Convergence of Two Time-Scale Stochastic Approximation Algorithms

<span id="page-22-1"></span>

#### A2.2.1 Linear Update Rules

First we consider linear iterates according to the PhD thesis of Konda [\[30\]](#page-35-10) and Konda & Tsitsiklis [\[33\]](#page-35-21).

$$
\theta_{n+1} = \theta_n + a(n) \left( a_1 - A_{11} \theta_n - A_{12} w_n + M_n^{(\theta)} \right), \qquad (75)
$$

$$
\mathbf{w}_{n+1} = \mathbf{w}_n + b(n) \left( \mathbf{a}_2 - \mathbf{A}_{21} \, \mathbf{\theta}_n - \mathbf{A}_{22} \, \mathbf{w}_n + \mathbf{M}_n^{(w)} \right) \,. \tag{76}
$$

Assumptions. We make the following assumptions:

(A1) The random variables  $(M_n^{(\theta)}, M_n^{(w)}), n = 0, 1, \ldots$ , are independent of  $w_0, \theta_0$  and of each other. The have zero mean:  $E[M_n^{(\theta)}] = 0$  and  $E[M_n^{(w)}] = 0$ . The covariance is

$$
\mathbf{E}\left[\mathbf{M}_n^{(\theta)}\left(\mathbf{M}_n^{(\theta)}\right)^T\right] = \mathbf{\Gamma}_{11},\tag{77}
$$

$$
\mathrm{E}\left[M_n^{(\theta)}\left(M_n^{(w)}\right)^T\right] = \Gamma_{12} = \Gamma_{21}^T\,,\tag{78}
$$

$$
\mathrm{E}\left[\mathbf{M}_n^{(w)}\left(\mathbf{M}_n^{(w)}\right)^T\right] = \mathbf{\Gamma}_{22} \,. \tag{79}
$$

(A2) The learning rates are deterministic, positive, nondecreasing and satisfy with  $\epsilon \leq 0$ :

$$
\sum_{n} a(n) = \infty , \quad \lim_{n \to \infty} a(n) = 0 , \qquad (80)
$$

$$
\sum_{n} b(n) = \infty , \quad \lim_{n \to \infty} b(n) = 0 , \qquad (81)
$$

$$
\frac{a(n)}{b(n)} \to \epsilon \,. \tag{82}
$$

We often consider the case  $\epsilon = 0$ .

(A3) Convergence of the iterates: We define

$$
\Delta := A_{11} - A_{12} A_{22}^{-1} A_{21} . \tag{83}
$$

A matrix is *Hurwitz* if the real part of each eigenvalue is strictly negative. We assume that the matrices  $-A_{22}$  and  $-\Delta$  are Hurwitz.

##### (A4) Convergence rate remains simple:

(a) There exists a constant  $\bar{a} \leq 0$  such that

$$
\lim_{n} (a(n+1)^{-1} - a(n)^{-1}) = \bar{a}.
$$
 (84)

(b) If  $\epsilon = 0$ , then

$$
\lim_{n}(b(n+1)^{-1} - b(n)^{-1}) = 0.
$$
 (85)

(c) The matrix

$$
-\left(\Delta - \frac{\bar{a}}{2} I\right) \tag{86}
$$

is Hurwitz.

Rate of Convergence Theorem. The next theorem is taken from Konda [\[30\]](#page-35-10) and Konda & Tsitsiklis [\[33\]](#page-35-21).

Let  $\theta^* \in \mathbb{R}^m$  and  $w^* \in \mathbb{R}^k$  be the unique solution to the system of linear equations

$$
A_{11} \theta_n + A_{12} w_n = a_1 , \qquad (87)
$$

$$
A_{21} \theta_n + A_{22} w_n = a_2 \,. \tag{88}
$$

For each  $n$ , let

$$
\hat{\theta}_n = \theta_n - \theta^*, \qquad (89)
$$

$$
\hat{\boldsymbol{w}}_n = \boldsymbol{w}_n - \boldsymbol{A}_{22}^{-1} (\boldsymbol{a}_2 - \boldsymbol{A}_{21} \boldsymbol{\theta}_n) , \qquad (90)
$$

$$
\Sigma_{11}^n = \theta_n^{-1} \mathbf{E} \left[ \hat{\theta}_n \hat{\theta}_n^T \right], \qquad (91)
$$

$$
\Sigma_{12}^n = \left(\Sigma_{21}^n\right)^T = \boldsymbol{\theta}_n^{-1} \operatorname{E}\left[\hat{\boldsymbol{\theta}}_n \hat{\boldsymbol{w}}_n^T\right],\tag{92}
$$

$$
\Sigma_{22}^n = \boldsymbol{w}_n^{-1} \mathbf{E} \left[ \hat{\boldsymbol{w}}_n \hat{\boldsymbol{w}}_n^T \right], \tag{93}
$$

$$
\Sigma^n = \begin{pmatrix} \Sigma_{11}^n & \Sigma_{12}^n \\ \Sigma_{21}^n & \Sigma_{22}^n \end{pmatrix} . \tag{94}
$$

**Theorem 6** (Konda & Tsitsiklis). *Under above assumptions and when the constant*  $\epsilon$  *is sufficiently small, the limit matrices*

$$
\Sigma_{11}^{(\epsilon)} = \lim_{n} \Sigma_{11}^{n}, \quad \Sigma_{12}^{(\epsilon)} = \lim_{n} \Sigma_{12}^{n}, \quad \Sigma_{22}^{(\epsilon)} = \lim_{n} \Sigma_{22}^{n}.
$$
 (95)

*exist. Furthermore, the matrix*

$$
\Sigma^{(0)} = \begin{pmatrix} \Sigma_{11}^{(0)} & \Sigma_{12}^{(0)} \\ \Sigma_{21}^{(0)} & \Sigma_{22}^{(0)} \end{pmatrix}
$$
 (96)

*is the unique solution to the following system of equations*

$$
\Delta \Sigma_{11}^{(0)} + \Sigma_{11}^{(0)} \Delta^T - \bar{a} \Sigma_{11}^{(0)} + A_{12} \Sigma_{21}^{(0)} + \Sigma_{12}^{(0)} A_{12}^T = \Gamma_{11},
$$
\n(97)

$$
A_{12} \Sigma_{22}^{(0)} + \Sigma_{12}^{(0)} A_{22}^T = \Gamma_{12} , \qquad (98)
$$

$$
A_{22} \Sigma_{22}^{(0)} + \Sigma_{22}^{(0)} A_{22}^T = \Gamma_{22} . \tag{99}
$$

*Finally,*

$$
\lim_{\epsilon \downarrow 0} \Sigma_{11}^{(\epsilon)} = \Sigma_{11}^{(0)}, \quad \lim_{\epsilon \downarrow 0} \Sigma_{12}^{(\epsilon)} = \Sigma_{12}^{(0)}, \quad \lim_{\epsilon \downarrow 0} \Sigma_{22}^{(\epsilon)} = \Sigma_{22}^{(0)}.
$$
 (100)

The next theorems shows that the asymptotic covariance matrix of  $a(n)^{-1/2}\theta_n$  is the same as that of  $a(n)^{-1/2}\bar{\theta}_n$ , where  $\bar{\theta}_n$  evolves according to the single time-scale stochastic iteration:

$$
\bar{\theta}_{n+1} = \bar{\theta}_n + a(n) \left( a_1 - A_{11} \bar{\theta}_n - A_{12} \bar{w}_n + M_n^{(\theta)} \right), \qquad (101)
$$

$$
\mathbf{0} = \mathbf{a}_2 - \mathbf{A}_{21} \bar{\mathbf{\theta}}_n - \mathbf{A}_{22} \bar{\mathbf{w}}_n + \mathbf{M}_n^{(w)}.
$$
 (102)

The next theorem combines Theorem 2.8 of Konda & Tsitsiklis and Theorem 4.1 of Konda & Tsitsiklis:

Theorem 7 (Konda & Tsitsiklis 2nd). *Under above assumptions*

$$
\Sigma_{11}^{(0)} = \lim_{n} a(n)^{-1} \mathbf{E} \left[ \bar{\boldsymbol{\theta}}_{n} \bar{\boldsymbol{\theta}}_{n}^{T} \right]. \tag{103}
$$

*If the assumptions hold with*  $\epsilon = 0$ , then  $a(n)^{-1/2}\hat{\theta}_n$  converges in distribution to  $\mathcal{N}(\mathbf{0}, \mathbf{\Sigma}_{11}^{(0)})$ .

### Comments.

(C1) In his PhD thesis [\[30\]](#page-35-10) Konda extended the analysis to the nonlinear case. Konda makes a linearization of the nonlinear function  $h$  and  $g$  with

$$
A_{11} = -\frac{\partial h}{\partial \theta}, \quad A_{12} = -\frac{\partial h}{\partial w}, \quad A_{21} = -\frac{\partial g}{\partial \theta}, \quad A_{22} = -\frac{\partial g}{\partial w}.
$$
 (104)

There are additional errors due to linearization which have to be considered. However, only a sketch of a proof is provided but not a complete proof.

- (C2) Theorem 4.1 of Konda & Tsitsiklis is important to generalize to the nonlinear case.
- (C3) The convergence rate is governed by  $A_{22}$  for the fast and  $\Delta$  for the slow iterate.  $\Delta$  in turn is affected by the interaction effects captured by  $A_{21}$  and  $A_{12}$  together with the inverse of  $A_{22}$ .

<span id="page-24-0"></span>

#### A2.2.2 Nonlinear Update Rules

The rate of convergence for nonlinear update rules according to Mokkadem & Pelletier is considered [\[44\]](#page-36-16).

The iterates are

$$
\theta_{n+1} = \theta_n + a(n) \left( \boldsymbol{h}(\theta_n, \boldsymbol{w}_n) + \boldsymbol{Z}_n^{(\theta)} + \boldsymbol{M}_n^{(\theta)} \right), \qquad (105)
$$

$$
\mathbf{w}_{n+1} = \mathbf{w}_n + b(n) \left( \mathbf{g}(\theta_n, \mathbf{w}_n) + \mathbf{Z}_n^{(w)} + \mathbf{M}_n^{(w)} \right).
$$
 (106)

with the increasing  $\sigma$ -fields

$$
\mathcal{F}_n = \sigma(\boldsymbol{\theta}_l, \boldsymbol{w}_l, \boldsymbol{M}_l^{(\theta)}, \boldsymbol{M}_l^{(w)}, \boldsymbol{Z}_l^{(\theta)}, \boldsymbol{Z}_l^{(w)}, l \leqslant n), n \geqslant 0. \tag{107}
$$

The terms  $Z_n^{(\theta)}$  and  $Z_n^{(w)}$  can be used to address the error through linearization, that is, the difference of the nonlinear functions to their linear approximation.

#### Assumptions. We make the following assumptions:

(A1) Convergence is ensured:

$$
\lim_{n \to \infty} \theta_n = \theta^* \text{ a.s.}, \qquad (108)
$$

$$
\lim_{n \to \infty} \mathbf{w}_n = \mathbf{w}^* \text{ a.s.}. \tag{109}
$$

##### (A2) Linear approximation and Hurwitz:

There exists a neighborhood  $\mathcal{U}$  of  $(\theta^*, \omega^*)$  such that, for all  $(\theta, \omega) \in \mathcal{U}$ 

$$
\begin{pmatrix}\n h(\theta, w) \\
 g(\theta, w)\n\end{pmatrix} =\n\begin{pmatrix}\n A_{11} & A_{12} \\
 A_{21} & A_{22}\n\end{pmatrix}\n\begin{pmatrix}\n \theta & -\theta^* \\
 w & -w^*\n\end{pmatrix} +\nO\n\begin{pmatrix}\n \theta & -\theta^* \\
 w & -w^*\n\end{pmatrix}^2.
$$
\n(110)

We define

$$
\Delta := A_{11} - A_{12} A_{22}^{-1} A_{21} . \qquad (111)
$$

A matrix is *Hurwitz* if the real part of each eigenvalue is strictly negative. We assume that the matrices  $A_{22}$  and  $\Delta$  are Hurwitz.

(A3) Assumptions on the learning rates:

$$
a(n) = a_0 n^{-\alpha} \tag{112}
$$

$$
b(n) = b_0 \, n^{-\beta} \,, \tag{113}
$$

where  $a_0 > 0$  and  $b_0 > 0$  and  $1/2 < \beta < \alpha \leq 1$ . If  $\alpha = 1$ , then  $a_0 > 1/(2e_{\min})$  with  $e_{\min}$ as the absolute value of the largest eigenvalue of  $\Delta$  (the eigenvalue closest to 0).

- (A4) Assumptions on the noise and error:
  - (a) martingale difference sequences:

$$
\mathrm{E}\left[\mathbf{M}_{n+1}^{(\theta)} \mid \mathcal{F}_n\right] = 0 \text{ a.s.},\qquad(114)
$$

$$
\mathrm{E}\left[\boldsymbol{M}_{n+1}^{(w)}\mid\mathcal{F}_n\right] = 0 \text{ a.s.} \qquad (115)
$$

(b) existing second moments:

$$
\lim_{n\to\infty} \mathrm{E}\left[\begin{pmatrix} M_{n+1}^{(\theta)} \\ M_{n+1}^{(w)} \end{pmatrix} \begin{pmatrix} (M_{n+1}^{(\theta)})^T & (M_{n+1}^{(w)})^T \end{pmatrix} | \mathcal{F}_n \right] = \Gamma = \begin{pmatrix} \Gamma_{11} & \Gamma_{12} \\ \Gamma_{21} & \Gamma_{22} \end{pmatrix} \text{ a.s.}
$$
\n(116)

(c) bounded moments:

There exist  $l > 2/\beta$  such that

$$
\sup_{n} \mathbb{E}\left[\|M_{n+1}^{(\theta)}\|^l \mid \mathcal{F}_n\right] < \infty \text{ a.s. }, \tag{117}
$$

$$
\sup_{n} \mathbb{E}\left[\|\mathbf{M}_{n+1}^{(w)}\|^{l} \mid \mathcal{F}_n\right] < \infty \quad \text{a.s.} \tag{118}
$$

(d) bounded error:

$$
\mathbf{Z}_n^{(\theta)} = r_n^{(\theta)} + \mathrm{O}\!\left(\|\theta - \theta^*\|^2 + \|\mathbf{w} - \mathbf{w}^*\|^2\right),\tag{119}
$$

$$
\mathbf{Z}_n^{(w)} = r_n^{(w)} + \mathcal{O}(\|\boldsymbol{\theta} - \boldsymbol{\theta}^*\|^2 + \|\boldsymbol{w} - \boldsymbol{w}^*\|^2), \qquad (120)
$$

with

$$
||r_n^{(\theta)}|| + ||r_n^{(w)}|| = o(\sqrt{a(n)}) \text{ a.s.}
$$
 (121)

Rate of Convergence Theorem. We report a theorem and a proposition from Mokkadem & Pel-letier [\[44\]](#page-36-16). However, first we have to define the covariance matrices  $\Sigma_{\theta}$  and  $\Sigma_{w}$  which govern the rate of convergence.

First we define

$$
\Gamma_{\theta} := \lim_{n \to \infty} \mathrm{E}\left[ \left( M_{n+1}^{(\theta)} - A_{12} A_{22}^{-1} M_{n+1}^{(w)} \right) \left( M_{n+1}^{(\theta)} - A_{12} A_{22}^{-1} M_{n+1}^{(w)} \right)^T | \mathcal{F}_n \right] = \tag{122}
$$

$$
\Gamma_{11} + A_{12} A_{22}^{-1} \Gamma_{22} (A_{22}^{-1})^T A_{12}^T - \Gamma_{12} (A_{22}^{-1})^T A_{12}^T - A_{12} A_{22}^{-1} \Gamma_{21}.
$$
  
We now define the asymptotic covariance matrices  $\Sigma_{\theta}$  and  $\Sigma_{w}$ :

$$
\Sigma_{\theta} = \int_0^{\infty} \exp\left(\left(\Delta + \frac{\mathbb{I}_{a=1}}{2 a_0} I\right) t\right) \Gamma_{\theta} \exp\left(\left(\Delta^T + \frac{\mathbb{I}_{a=1}}{2 a_0} I\right) t\right) dt, \quad (123)
$$

$$
\Sigma_w = \int_0^\infty \exp\left(A_{22} t\right) \Gamma_{22} \exp\left(A_{22} t\right) dt. \tag{124}
$$

 $\Sigma_{\theta}$  and  $\Sigma_{w}$  are solutions of the Lyapunov equations:

$$
\left(\Delta + \frac{\mathbb{I}_{a=1}}{2 a_0} I\right) \Sigma_{\theta} + \Sigma_{\theta} \left(\Delta^T + \frac{\mathbb{I}_{a=1}}{2 a_0} I\right) = -\Gamma_{\theta}, \qquad (125)
$$

$$
\mathbf{A}_{22} \; \mathbf{\Sigma}_w \; + \; \mathbf{\Sigma}_w \; \mathbf{A}_{22}^T \; = \; - \; \mathbf{\Gamma}_{22} \; . \tag{126}
$$

Theorem 8 (Mokkadem & Pelletier: Joint weak convergence). *Under above assumptions:*

$$
\begin{pmatrix}\n\sqrt{a(n)^{-1}} (\theta - \theta^*) \\
\sqrt{b(n)^{-1}} (\boldsymbol{w} - \boldsymbol{w}^*)\n\end{pmatrix} \xrightarrow{\mathcal{D}} \mathcal{N} \begin{pmatrix}\n\mathbf{0} , \begin{pmatrix}\n\Sigma_\theta & \mathbf{0} \\
\mathbf{0} & \Sigma_w\n\end{pmatrix}\n\end{pmatrix}.
$$
\n(127)

Theorem 9 (Mokkadem & Pelletier: Strong convergence). *Under above assumptions:*

$$
\|\boldsymbol{\theta} - \boldsymbol{\theta}^*\| = O\left(\sqrt{a(n) \log\left(\sum_{l=1}^n a(l)\right)}\right) \ a.s. ,
$$
 (128)

$$
\|\boldsymbol{w} - \boldsymbol{w}^*\| = \mathcal{O}\left(\sqrt{b(n)\,\log\left(\sum_{l=1}^n b(l)\right)}\right) \; \text{a.s.} \tag{129}
$$

### Comments.

(C1) Besides the learning steps  $a(n)$  and  $b(n)$ , the convergence rate is governed by  $A_{22}$  for the fast and  $\Delta$  for the slow iterate.  $\Delta$  in turn is affected by interaction effects which are captured by  $A_{21}$  and  $A_{12}$  together with the inverse of  $A_{22}$ .

<span id="page-26-0"></span>

#### A2.3 Equal Time-Scale Stochastic Approximation Algorithms

In this subsection we consider the case when the learning rates have equal time-scale.

<span id="page-26-1"></span>

#### A2.3.1 Equal Time-Scale for Saddle Point Iterates

If equal time-scales assumed then the iterates revisit infinite often an environment of the solution [\[61\]](#page-37-0). In Zhang 2007, the functions of the iterates are the derivatives of a Lagrangian with respect to the dual and primal variables  $[61]$ . The iterates are

$$
\theta_{n+1} = \theta_n + a(n) \left( \boldsymbol{h}(\theta_n, \boldsymbol{w}_n) + \boldsymbol{Z}_n^{(\theta)} + \boldsymbol{M}_n^{(\theta)} \right), \qquad (130)
$$

$$
\mathbf{w}_{n+1} = \mathbf{w}_n + a(n) \left( \mathbf{g}(\theta_n, \mathbf{w}_n) + \mathbf{Z}_n^{(w)} + \mathbf{M}_n^{(w)} \right).
$$
 (131)

with the increasing  $\sigma$ -fields

$$
\mathcal{F}_n = \sigma(\boldsymbol{\theta}_l, \boldsymbol{w}_l, \boldsymbol{M}_l^{(\theta)}, \boldsymbol{M}_l^{(w)}, \boldsymbol{Z}_l^{(\theta)}, \boldsymbol{Z}_l^{(w)}, l \leqslant n), n \geqslant 0.
$$
\n(132)

The terms  $Z_n^{(\theta)}$  and  $Z_n^{(w)}$  subsum biased estimation errors.

#### Assumptions. We make the following assumptions:

(A1) Assumptions on update function:  $h$  and  $g$  are continuous, differentiable, and bounded. The Jacobians

$$
\frac{\partial g}{\partial w} \quad \text{and} \quad \frac{\partial h}{\partial \theta} \tag{133}
$$

are Hurwitz. A matrix is *Hurwitz* if the real part of each eigenvalue is strictly negative. This assumptions corresponds to the assumption in [\[61\]](#page-37-0) that the Lagrangian is concave in  $w$  and convex in  $\theta$ .

(A2) Assumptions on noise:

 ${M_n^{(\theta)}}$  and  ${M_n^{(w)}}$  are a martingale difference sequences w.r.t. the increasing  $\sigma$ -fields  $\mathcal{F}_n$ . Furthermore they are mutually independent.

Bounded second moment:

$$
\mathrm{E}\left[\|M_{n+1}^{(\theta)}\|^2 \mid \mathcal{F}_n\right] < \infty \text{ a.s. }, \tag{134}
$$

$$
\mathbf{E}\left[\|\mathbf{M}_{n+1}^{(w)}\|^2 \mid \mathcal{F}_n\right] < \infty \quad \text{a.s.} \tag{135}
$$

(A3) Assumptions on the learning rate:

$$
a(n) > 0
$$
,  $a(n) \to 0$ ,  $\sum_{n} a(n) = \infty$ ,  $\sum_{n} a^{2}(n) < \infty$ . (136)

(A4) Assumption on the biased error:

Boundedness:

$$
\lim_{n} \sup \|Z_{n}^{(\theta)}\| \leq \alpha^{(\theta)} \text{ a.s.}
$$
\n(137)

$$
\lim_{n} \sup \|Z_{n}^{(w)}\| \leqslant \alpha^{(w)} \text{ a.s.}
$$
\n(138)

**Theorem.** Define the "contraction region"  $A_n$  as follows:

$$
A_{\eta} = \{(\boldsymbol{\theta}, \boldsymbol{w}) : \alpha^{(\boldsymbol{\theta})} \geqslant \eta \|\boldsymbol{h}(\boldsymbol{\theta}, \boldsymbol{w})\| \quad \text{or} \quad \alpha^{(w)} \geqslant \eta \|\boldsymbol{g}(\boldsymbol{\theta}, \boldsymbol{w})\|, \ 0 \leqslant \eta < 1\} \,.
$$

**Theorem 10** (Zhang). *Under above assumptions the iterates return to*  $A_{\eta}$  *infinitely often with probability one (a.s.).*

### Comments.

- (C1) The proof of the theorem in [\[61\]](#page-37-0) does not use the saddle point condition and not the fact that the functions of the iterates are derivatives of the same function.
- (C2) For the unbiased case, Zhang showed in Theorem 3.1 of [\[61\]](#page-37-0) that the iterates converge. However, he used the saddle point condition of the Lagrangian. He considered iterates with functions that are the derivatives of a Lagrangian with respect to the dual and primal variables [\[61\]](#page-37-0).

<span id="page-27-0"></span>

#### A2.3.2 Equal Time Step for Actor-Critic Method

If equal time-scales assumed then the iterates revisit infinite often an environment of the solution of DiCastro & Meir  $[14]$ . The iterates of DiCastro & Meir are derived for actor-critic learning.

To present the actor-critic update iterates, we have to define some functions and terms.  $\mu(u \mid x, \theta)$  is the policy function parametrized by  $\theta \in \mathbb{R}^m$  with observations  $x \in \mathcal{X}$  and actions  $u \in \mathcal{U}$ . A Markov chain given by  $P(y \mid x, u)$  gives the next observation y using the observation x and the action u. In each state x the agent receives a reward  $r(x)$ .

The average reward per stage is for the recurrent state  $x^*$ :

$$
\tilde{\eta}(\boldsymbol{\theta}) = \lim_{T \to \infty} \mathbf{E} \left[ \frac{1}{T} \sum_{n=0}^{T-1} r(\boldsymbol{x}_n) \mid \boldsymbol{x}_0 = \boldsymbol{x}^*, \boldsymbol{\theta} \right]. \tag{140}
$$

The estimate of  $\tilde{\eta}$  is denoted by  $\eta$ .

The differential value function is

$$
\tilde{h}(\boldsymbol{x},\boldsymbol{\theta}) = \mathrm{E}\left[\sum_{n=0}^{T-1} (r(\boldsymbol{x}_n) - \tilde{\eta}(\boldsymbol{\theta})) \mid \boldsymbol{x}_0 = \boldsymbol{x}, \boldsymbol{\theta}\right]. \tag{141}
$$

The temporal difference is

$$
\tilde{d}(\boldsymbol{x},\boldsymbol{y},\boldsymbol{\theta})\ =\ r(\boldsymbol{x})\ -\ \tilde{\eta}(\boldsymbol{\theta})\ +\ \tilde{h}(\boldsymbol{y},\boldsymbol{\theta})\ -\ \tilde{h}(\boldsymbol{x},\boldsymbol{\theta})\ .\tag{142}
$$

The estimate of  $\tilde{d}$  is denoted by d.

The likelihood ratio derivative  $\Psi \in \mathbb{R}^m$  is

$$
\Psi(x, u, \theta) = \frac{\nabla_{\theta} \mu(u \mid x, \theta)}{\mu(u \mid x, \theta)}.
$$
\n(143)

The value function  $\tilde{h}$  is approximated by

$$
h(\boldsymbol{x},\boldsymbol{w}) = \boldsymbol{\phi}(\boldsymbol{x})^T \boldsymbol{w} \,, \tag{144}
$$

where  $\phi(\boldsymbol{x}) \in \mathbb{R}^k$ . We define  $\boldsymbol{\Phi} \in \mathbb{R}^{|\mathcal{X}| \times k}$ 

$$
\Phi = \begin{pmatrix}
\phi_1(x_1) & \phi_2(x_1) & \dots & \phi_k(x_1) \\
\phi_1(x_2) & \phi_2(x_2) & \dots & \phi_k(x_2) \\
\vdots & \vdots & & \vdots \\
\phi_1(x_{|\mathcal{X}|}) & \phi_2(x_{|\mathcal{X}|}) & \dots & \phi_k(x_{|\mathcal{X}|})
\end{pmatrix}
$$
(145)

and

$$
h(\mathbf{w}) = \mathbf{\Phi} \mathbf{w} \,. \tag{146}
$$

For TD( $\lambda$ ) we have an eligibility trace:

$$
e_n = \lambda e_{n-1} + \phi(\boldsymbol{x}_n). \tag{147}
$$

We define the approximation error with optimal parameter  $w^*(\theta)$ :

$$
\epsilon_{\text{app}}(\boldsymbol{\theta}) = \inf_{\boldsymbol{w}\in\mathbb{R}^k} \|\tilde{h}(\boldsymbol{\theta}) - \boldsymbol{\Phi}\,\boldsymbol{w}\|_{\pi(\boldsymbol{\theta})} = \|\tilde{h}(\boldsymbol{\theta}) - \boldsymbol{\Phi}\,\boldsymbol{w}^*(\boldsymbol{\theta})\|_{\pi(\boldsymbol{\theta})}, \qquad (148)
$$

where  $\pi(\theta)$  is an projection operator into the span of  $\Phi w$ . We bound this error by

$$
\epsilon_{\rm app} = \sup_{\boldsymbol{\theta} \in \mathbb{R}^k} \epsilon_{\rm app}(\boldsymbol{\theta}) \,.
$$
 (149)

We denoted by  $\tilde{\eta}$ ,  $\tilde{d}$ , and  $\tilde{h}$  the exact functions and used for their approximation  $\eta$ ,  $d$ , and  $h$ , respectively. We have learning rate adjustments  $\Gamma_{\eta}$  and  $\Gamma_{w}$  for the critic.

The update rules are: Critic:

$$
\eta_{n+1} = \eta_n + a(n) \Gamma_\eta \left( r(\boldsymbol{x}_n) - \eta_n \right), \qquad (150)
$$

$$
h(\boldsymbol{x},\boldsymbol{w}_n) = \boldsymbol{\phi}(\boldsymbol{x})^T \boldsymbol{w}_n , \qquad (151)
$$

$$
d(\boldsymbol{x}_n, \boldsymbol{x}_{n+1}, \boldsymbol{w}_n) = r(\boldsymbol{x}_n) - \eta_n + h(\boldsymbol{x}_{n+1}, \boldsymbol{w}_n) - h(\boldsymbol{x}_n, \boldsymbol{w}_n), \qquad (152)
$$

$$
e_n = \lambda e_{n-1} + \phi(\boldsymbol{x}_n), \qquad (153)
$$

$$
\boldsymbol{w}_{n+1} = \boldsymbol{w}_n + a(n) \Gamma_w d(\boldsymbol{x}_n, \boldsymbol{x}_{n+1}, \boldsymbol{w}_n) e_n . \qquad (154)
$$

Actor:

$$
\boldsymbol{\theta}_{n+1} = \boldsymbol{\theta}_n + a(n) \, \boldsymbol{\Psi}(\boldsymbol{x}_n, \boldsymbol{u}_n, \boldsymbol{\theta}_n) \, d(\boldsymbol{x}_n, \boldsymbol{x}_{n+1}, \boldsymbol{w}_n) \, . \tag{155}
$$

Assumptions. We make the following assumptions:

(A1) Assumption on rewards:

The rewards  $\{r(x)\}_{{\boldsymbol{x}} \in \mathcal{X}}$  are uniformly bounded by a finite constant  $B_r$ .

(A2) Assumption on the Markov chain:

Each Markov chain for each  $\theta$  is aperiodic, recurrent, and irreducible.

(A3) Assumptions on the policy function:

The conditional probability function  $\mu(u \mid x, \theta)$  is twice differentiable. Moreover, there exist positive constants,  $B_{\mu_1}$  and  $B_{\mu_2}$ , such that for all  $x \in \mathcal{X}$ ,  $u \in \mathcal{U}$ ,  $\theta \in \mathbb{R}^m$  and  $1 \leq l_1, l_2 \leq m$  we have

$$
\left\|\frac{\partial \mu(u \mid x, \theta)}{\partial \theta_l}\right\| \leq B_{\mu_1}, \quad \left\|\frac{\partial^2 \mu(u \mid x, \theta)}{\partial \theta_{l_1} \partial \theta_{l_2}}\right\| \leq B_{\mu_2}.
$$
 (156)

(A4) Assumption on the likelihood ratio derivative:

For all  $x \in \mathcal{X}$ ,  $u \in \mathcal{U}$ , and  $\theta \in \mathbb{R}^m$ , there exists a positive constant  $B_{\Psi}$ , such that

$$
\|\Psi(x,u,\theta)\|_2\,\leqslant\,B_{\Psi}\,<\,\infty\,,\qquad \qquad (157)
$$

where  $\Vert . \Vert_2$  is the Euclidean  $L_2$  norm.

(A5) Assumptions on the approximation space given by  $\Phi$ :

The columns of the matrix  $\Phi$  are independent, that is, the form a basis of dimension k. The norms of the columns vectors of the matrix  $\Phi$  are bounded above by 1, that is,  $\|\phi_l\|_2 \leq 1$ for  $1 \leq l \leq k$ .

(A6) Assumptions on the learning rate:

$$
\sum_{n} a(n) = \infty , \quad \sum_{n} a^{2}(n) < \infty . \tag{158}
$$

**Theorem.** The algorithm converged if  $\nabla_{\theta} \tilde{\eta}(\theta) = 0$ , since the actor reached a stationary point where the updates are zero. We assume that  $\|\nabla_{\theta}\tilde{\eta}(\theta)\|$  hints at how close we are to the convergence point.

The next theorem from DiCastro & Meir  $[14]$  implies that the trajectory visits a neighborhood of a local maximum infinitely often. Although it may leave the local vicinity of the maximum, it is guaranteed to return to it infinitely often.

Theorem 11 (DiCastro & Meir). *Define*

$$
B_{\nabla \tilde{\eta}} = \frac{B_{\Delta t d1}}{\Gamma_w} + \frac{B_{\Delta t d2}}{\Gamma_\eta} + B_{\Delta t d3} \epsilon_{\text{app}} , \qquad (159)
$$

*where* B∆td1*,* B∆td2*, and* B∆td<sup>3</sup> *are finite constants depending on the Markov decision process and the agent parameters.*

*Under above assumptions*

$$
\lim_{t \to \infty} \inf \| \nabla_{\theta} \tilde{\eta}(\boldsymbol{\theta}_t) \| \leq B_{\nabla \tilde{\eta}}.
$$
\n(160)

*The trajectory visits a neighborhood of a local maximum infinitely often.*

##### Comments.

- (C1) The larger the critic learning rates  $\Gamma_w$  and  $\Gamma_n$  are, the smaller is the region around the local maximum.
- (C2) The results are in agreement with those of Zhang 2007 [\[61\]](#page-37-0).
- (C3) Even if the results are derived for a special actor-critic setting, they carry over to a more general setting of the iterates.

<span id="page-29-0"></span>

## A3 ADAM Optimization as Stochastic Heavy Ball with Friction

The Nesterov Accelerated Gradient Descent (NAGD) [\[47\]](#page-36-17) has raised considerable interest due to its numerical simplicity and its low complexity. Previous to NAGD and its derived methods there was Polyak's Heavy Ball method [\[49\]](#page-36-13). The idea of the Heavy Ball is a ball that evolves over the graph of a function  $f$  with damping (due to friction) and acceleration. Therefore, this second-order dynamical system can be described by the ODE for the Heavy Ball with Friction (HBF) [\[17\]](#page-35-16):

$$
\ddot{\theta}_t + a(t)\dot{\theta}_t + \nabla f(\theta_t) = \mathbf{0}, \qquad (161)
$$

where  $a(n)$  is the damping coefficient with  $a(n) = \frac{a}{n^{\beta}}$  for  $\beta \in (0, 1]$ . This ODE is equivalent to the integro-differential equation

$$
\dot{\boldsymbol{\theta}}_t = -\frac{1}{k(t)} \int_0^t h(s) \nabla f(\boldsymbol{\theta}_s) \mathrm{d}s \,, \tag{162}
$$

where k and h are two memory functions related to  $a(t)$ . For polynomially memoried HBF we have  $k(t) = t^{\alpha+1}$  and  $h(t) = (\alpha+1)t^{\alpha}$  for some positive  $\alpha$ , and for exponentially memoried HBF we have  $k(t) = \lambda \exp(\lambda t)$  and  $h(t) = \exp(\lambda t)$ . For the sum of the learning rates, we obtain

$$
\sum_{l=1}^{n} a(l) = a \begin{cases} \ln(n) + \gamma + \frac{1}{2n} + \mathcal{O}\left(\frac{1}{n^2}\right) & \text{for } \beta = 1\\ \frac{n^{1-\beta}}{1-\beta} & \text{for } \beta < 1 \end{cases}
$$
 (163)

where  $\gamma = 0.5772156649$  is the Euler-Mascheroni constant.

Gadat et al. derived a discrete and stochastic version of the HBF [\[17\]](#page-35-16):

$$
\theta_{n+1} = \theta_n - a(n+1) m_n
$$
\n
$$
m_{n+1} = m_n + a(n+1) r(n) \left( \nabla f(\theta_n) - m_n \right) + a(n+1) r(n) M_{n+1},
$$
\n(164)

where

<span id="page-30-1"></span>
$$
r(n) = \begin{cases} r & \text{for exponentially memoried HBF} \\ \frac{r}{\sum_{l=1}^{n} a(l)} & \text{for polynomially memoried HBF} \end{cases}
$$
 (165)

This recursion can be rewritten as

$$
\theta_{n+1} = \theta_n - a(n+1) \, \boldsymbol{m}_n \tag{166}
$$

$$
\mathbf{m}_{n+1} = (1 - a(n+1) r(n)) \mathbf{m}_n + a(n+1) r(n) (\nabla f(\theta_n) + \mathbf{M}_{n+1}). \tag{167}
$$

The recursion Eq. [\(166\)](#page-30-0) is the first moment update of ADAM [\[29\]](#page-35-14).

For the term  $r(n)a(n)$  we obtain for the polynomial memory the approximations

<span id="page-30-0"></span>
$$
r(n) a(n) \approx r \begin{cases} \frac{1}{n \log n} & \text{for } \beta = 1 \\ \frac{1 - \beta}{n} & \text{for } \beta < 1 \end{cases},
$$
 (168)

Gadat et al. showed that the recursion Eq. [\(164\)](#page-30-1) converges for functions with at most quadratic grow [\[17\]](#page-35-16). The authors mention that convergence can be proofed for functions  $f$  that are  $L$ -smooth, that is, the gradient is L-Lipschitz.

Kingma et al. [\[29\]](#page-35-14) state in Theorem 4.1 convergence of ADAM while assuming that  $\beta_1$ , the first moment running average coefficient, decays exponentially. Furthermore they assume that  $\frac{\beta_1^2}{\sqrt{\beta_2}} < 1$ and the learning rate  $\alpha_t$  decays with  $\alpha_t = \frac{\alpha}{\sqrt{t}}$ .

ADAM divides  $m_n$  of the recursion Eq. [\(166\)](#page-30-0) by the bias-corrected second raw moment estimate. Since the bias-corrected second raw moment estimate changes slowly, we consider it as an error.

$$
\frac{1}{\sqrt{v + \Delta v}} \approx \frac{1}{\sqrt{v}} - \frac{1}{2 v \sqrt{v}} \Delta v + \mathcal{O}(\Delta v^2). \tag{169}
$$

ADAM assumes the second moment  $E[g^2]$  to be stationary with its approximation  $v_n$ :

$$
v_n = \frac{1 - \beta_2}{1 - \beta_2^n} \sum_{l=1}^n \beta_2^{n-l} g_l^2.
$$
 (170)

$$
\Delta_n v_n = v_n - v_{n-1} = \frac{1 - \beta_2}{1 - \beta_2^n} \sum_{l=1}^n \beta_2^{n-l} g_l^2 - \frac{1 - \beta_2}{1 - \beta_2^{n-1}} \sum_{l=1}^{n-1} \beta_2^{n-l-1} g_l^2 \qquad (171)
$$
$$
= \frac{1 - \beta_2}{1 - \beta_2^n} g_n^2 + \frac{\beta_2 (1 - \beta_2)}{1 - \beta_2^n} \sum_{l=1}^{n-1} \beta_2^{n-l-1} g_l^2 - \frac{1 - \beta_2}{1 - \beta_2^{n-1}} \sum_{l=1}^{n-1} \beta_2^{n-l-1} g_l^2
$$
$$
= \frac{1 - \beta_2}{1 - \beta_2^n} \left( g_n^2 + \left( \beta_2 - \frac{1 - \beta_2^n}{1 - \beta_2^{n-1}} \right) \sum_{l=1}^{n-1} \beta_2^{n-l-1} g_l^2 \right)
$$
$$
= \frac{1 - \beta_2}{1 - \beta_2^n} \left( g_n^2 - \frac{1 - \beta_2}{1 - \beta_2^{n-1}} \sum_{l=1}^{n-1} \beta_2^{n-l-1} g_l^2 \right).
$$

Therefore

$$
E[\Delta_n v_n] = E[v_n - v_{n-1}] = \frac{1 - \beta_2}{1 - \beta_2^n} \left( E[g^2] - \frac{1 - \beta_2}{1 - \beta_2^{n-1}} \sum_{l=1}^{n-1} \beta_2^{n-l-1} E[g^2] \right)
$$
(172)  
$$
= \frac{1 - \beta_2}{1 - \beta_2^n} (E[g^2] - E[g^2]) = 0.
$$

We are interested in the difference of actual stochastic  $v_n$  to the true stationary  $v$ :

$$
\Delta v_n = v_n - v = \frac{1 - \beta_2}{1 - \beta_2^n} \sum_{l=1}^n \beta_2^{n-l} (g_l^2 - v) . \qquad (173)
$$

For a stationary second moment of  $m_n$  and  $\beta_2 = 1 - \alpha a(n + 1)r(n)$ , we have  $\Delta v_n \propto a(n + 1)r(n)$ 1)r(n). We use a linear approximation to ADAM's second moment normalization  $1/\sqrt{v + \Delta v_n} \approx$  $1/\sqrt{v} - 1/(2v\sqrt{v})\Delta v_n + O(\Delta^2 v_n)$ . If we set  $M_{n+1}^{(v)} = -(m_n \Delta v_n)/(2v\sqrt{v}a(n+1)r(n))$ , then  $m_n/\sqrt{v_n} \approx m_n/\sqrt{v} + a(n+1)r(n)M_{n+1}^{(v)}$  and  $E\left[M_{n+1}^{(v)}\right] = 0$ , since  $E[g_l^2 - v] = 0$ . For a stationary second moment of  $m_n$ ,  $\{M_n^{(v)}\}$  is a martingale difference sequence with a bounded second moment. Therefore  $\{M_{n+1}^{(v)}\}$  can be subsumed into  $\{M_{n+1}\}$  in update rules Eq. [\(166\)](#page-30-0). The factor  $1/\sqrt{v}$  can be incorporated into  $a(n+1)$  and  $r(n)$ .

<span id="page-31-0"></span>

#### A4 Experiments: Additional Information

<span id="page-31-1"></span>A4.1 WGAN-GP on Image Data.

<span id="page-31-2"></span>Table A2: The performance of WGAN-GP trained with the original procedure and with TTUR on CIFAR-10 and LSUN Bedrooms. We compare the performance with respect to the FID at the optimal number of iterations during training and wall-clock time in minutes.

| dataset  | method | b, a       | iter | time(m) | FID  | method | b = a | iter | time(m) | FID  |
|----------|--------|------------|------|---------|------|--------|-------|------|---------|------|
| CIFAR-10 | TTUR   | 3e-4, 1e-4 | 168k | 700     | 24.8 | orig   | 1e-4  | 53k  | 800     | 29.3 |
| LSUN     | TTUR   | 3e-4, 1e-4 | 80k  | 1900    | 9.5  | orig   | 1e-4  | 23k  | 2010    | 20.5 |

<span id="page-32-0"></span>

#### A4.2 WGAN-GP on the One Billion Word Benchmark.

<span id="page-32-3"></span>Table A3: Samples generated by WGAN-GP trained on fhe One Billion Word benchmark with TTUR (left) the original method (right).

| Dry Hall Sitning tven the concer | No say that tent Franstal at Bra |
|----------------------------------|----------------------------------|
| There are court phinchs hasffort | Caulh Paphionars tven got corfle |
| He scores a supponied foutver il | Resumaly, braaky facting he at   |
| Bartfol reportings ane the depor | On toipe also houd, aid of sole  |
| Seu hid, it 's watter 's remold  | When Barrysels commono toprel to |
| Later fasted the store the inste | The Moster suprr tent Elay diccu |
| Indiwezal deducated belenseous K | The new vebators are demases to  |
| Starfers on Rbama 's all is lead | Many 's lore wockerssaow 2 2 ) A |
| Inverdick oper, caldawho's non   | Andly, has le wordd Uold steali  |
| She said, five by theically rec  | But be the firmoters is no 200 s |
| RichI, Learly said remain.       | Jermueciored a noval wan 't mar  |
| Reforded live for they were like | Onles that his boud-park, the g  |
| The plane was git finally fuels  | ISLUN, The crather wilh a them   |
| The skip lifely will neek by the | Fow 22o2 surgeedeto, theirestra  |
| SEW McHardy Berfect was luadingu | Make Sebages of intarmamates, a  |
| But I pol rated Franclezt is the | Gullla " has cautaria Thoug ly t |

<span id="page-32-4"></span>Table A4: The performance of WGAN-GP trained with the original procedure and with TTUR on the One Billion Word Benchmark. We compare the performance with respect to the JSD at the optimal number of iterations and wall-clock time in minutes during training. WGAN-GP trained with TTUR exhibits consistently a better FID.

| n-gram | method | b, a       | iter | $time(m)$ | JSD         | method | $b = a$ | iter | $time(m)$ | JSD  |
|--------|--------|------------|------|-----------|-------------|--------|---------|------|-----------|------|
| 4-gram | TTUR   | 3e-4, 1e-4 | 98k  | 1150      | <b>0.35</b> | orig   | 1e-4    | 33k  | 1040      | 0.38 |
| 6-gram | TTUR   | 3e-4, 1e-4 | 100k | 1120      | <b>0.74</b> | orig   | 1e-4    | 32k  | 1070      | 0.77 |

<span id="page-32-1"></span>

#### A4.3 BEGAN

The Boundary Equilibrium GAN (BEGAN) [\[6\]](#page-34-1) maintains an equilibrium between the discriminator and generator loss (cf. Section 3.3 in [\[6\]](#page-34-1))

<span id="page-32-2"></span>
$$
E[\mathcal{L}(G(z))] = \gamma E[\mathcal{L}(x)] \tag{174}
$$

which, in turn, also leads to a fixed relation between the two gradients, therefore, a two time-scale update is not ensured by solely adjusting the learning rates. Indeed, for stable learning rates, we see no differences in the learning progress between orig and TTUR as depicted in Figure [A13.](#page-32-2)

Image /page/32/Figure/9 description: The image displays two line graphs side-by-side, both plotting FID (Fréchet Inception Distance) against mini-batches in thousands. The left graph shows three lines: 'orig 5e-5' (blue), 'orig 1e-4' (orange), and 'TTUR 6e-5 4e-5' (red). The x-axis ranges from 0 to 250, and the y-axis ranges from approximately 35 to 65. The right graph shows two lines: 'orig 2e-5' (blue) and 'TTUR 3e-5 1e-5' (red). The x-axis ranges from 0 to 140, and the y-axis ranges from approximately 100 to 450. Both graphs include shaded areas around the lines, indicating variability.

Figure A13: Mean, maximum and minimum FID over eight runs for BEGAN training on CelebA and LSUN Bedrooms. TTUR learning rates are given as pairs  $(b, a)$  of discriminator learning rate  $b$  and generator learning rate  $a$ : "TTUR  $b$   $a$ ". Left: CelebA, starting at mini-batch 10k for better visualisation. Right: LSUN Bedrooms. Orig and TTUR behave similar. For BEGAN we cannot ensure TTUR by adjusting learning rates.

<span id="page-33-1"></span>

## A5 Discriminator vs. Generator Learning Rate

The convergence proof for learning GANs with TTUR assumes that the generator learning rate will eventually become small enough to ensure convergence of the discriminator learning. At some time point, the perturbations of the discriminator updates by updates of the generator parameters are sufficient small to assure that the discriminator converges. Crucial for discriminator convergence is the magnitude of the perturbations which the generator induces into the discriminator updates. These perturbations are not only determined by the generator learning rate but also by its loss function, current value of the loss function, optimization method, size of the error signals that reach the generator (vanishing or exploding gradient), complexity of generator's learning task, architecture of the generator, regularization, and others. Consequently, the size of generator learning rate does not solely determine how large the perturbations of the discriminator updates are but serve to modulate them. Thus, the generator learning rate may be much larger than the discriminator learning rate without inducing large perturbation into the discriminator learning.

Even the learning dynamics of the generator is different from the learning dynamics of the discrimi-nator, though they both have the same learning rate. Figure [A14](#page-33-2) shows the loss of the generator and the discriminator for an experiment with DCGAN on CelebA, where the learning rate was 0.0005 for both the discriminator and the generator. However, the discriminator loss is decreasing while the generator loss is increasing. This example shows that the learning rate neither determines the perturbations nor the progress in learning for two coupled update rules. The choice of the learning rate for the generator should be independent from choice for the discriminator. Also the search ranges of discriminator and generator learning rates should be independent from each other, but adjusted to the corresponding architecture, task, etc.

Image /page/33/Figure/3 description: A line graph shows the discriminator loss and generator loss over mini-batches. The x-axis is labeled "mini-batch x 1k" and ranges from 0 to 20. The y-axis is labeled "loss" and ranges from 0 to 6. The discriminator loss, shown in blue, starts at approximately 1.5 and decreases to around 0.2 by the end of the graph. The generator loss, shown in orange, starts at approximately 3.2, increases to around 6.2 by the end of the graph, with some fluctuations in between.

<span id="page-33-2"></span>Figure A14: The respective losses of the discriminator and the generator show the different learning dynamics of the two networks.

<span id="page-33-0"></span>

## A6 Used Software, Datasets, Pretrained Models, and Implementations

We used the following datasets to evaluate GANs: The Large-scale CelebFaces Attributes (CelebA) dataset, aligned and cropped [\[41\]](#page-36-18), the training dataset of the bedrooms category of the large scale image database (LSUN)  $[60]$ , the CIFAR-10 training dataset  $[34]$ , the Street View House Numbers training dataset (SVHN) [\[48\]](#page-36-19), and the One Billion Word Benchmark [\[12\]](#page-34-9).

All experiments rely on the respective reference implementations for the corresponding GAN model. The software framework for our experiments was Tensorflow 1.3 [\[1,](#page-34-11) [2\]](#page-34-12) and Python 3.6. We used following software, datasets and pretrained models:

• BEGAN in Tensorflow, <https://github.com/carpedm20/BEGAN-tensorflow>, Fixed random seeds removed. Accessed: 2017-05-30

- DCGAN in Tensorflow, <https://github.com/carpedm20/DCGAN-tensorflow>, Fixed random seeds removed. Accessed: 2017-04-03
- Improved Training of Wasserstein GANs, image model, [https://github.com/igul222/](https://github.com/igul222/improved_wgan_training/blob/master/gan_64x64.py) [improved\\_wgan\\_training/blob/master/gan\\_64x64.py](https://github.com/igul222/improved_wgan_training/blob/master/gan_64x64.py), Accessed: 2017-06-12
- Improved Training of Wasserstein GANs, language model, [https://github.com/](https://github.com/igul222/improved_wgan_training/blob/master/gan_language.py)<br>igu1222/improved\_wgan\_training/blob/master/gan\_language.pv. Accessed: [igul222/improved\\_wgan\\_training/blob/master/gan\\_language.py](https://github.com/igul222/improved_wgan_training/blob/master/gan_language.py), 2017-06-12
- Inception-v3 pretrained, [http://download.tensorflow.org/models/image/](http://download.tensorflow.org/models/image/imagenet/inception-2015-12-05.tgz) [imagenet/inception-2015-12-05.tgz](http://download.tensorflow.org/models/image/imagenet/inception-2015-12-05.tgz), Accessed: 2017-05-02

Implementations are available at

• <https://github.com/bioinf-jku/TTUR>

<span id="page-34-10"></span>

#### References

- <span id="page-34-11"></span>[1] M. Abadi, A. Agarwal, P. Barham, E. Brevdo, Z. Chen, C. Citro, G. S. Corrado, A. Davis, J. Dean, M. Devin, S. Ghemawat, I. J. Goodfellow, A. Harp, G. Irving, M. Isard, Y. Jia, R. Józefowicz, L. Kaiser, M. Kudlur, J. Levenberg, D. Mané, R. Monga, S. Moore, D. G. Murray, C. Olah, M. Schuster, J. Shlens, B. Steiner, I. Sutskever, K. Talwar, P. A. Tucker, V. Vanhoucke, V. Vasudevan, F. B. Viégas, O. Vinyals, P. Warden, M. Wattenberg, M. Wicke, Y. Yu, and X. Zheng. Tensorflow: Large-scale machine learning on heterogeneous distributed systems. *arXiv e-prints*, arXiv:1603.04467, 2016.
- <span id="page-34-12"></span>[2] M. Abadi, P. Barham, J. Chen, Z. Chen, A. Davis, J. Dean, M. Devin, S. Ghemawat, G. Irving, M. Isard, M. Kudlur, J. Levenberg, R. Monga, S. Moore, D. G. Murray, B. Steiner, P. Tucker, V. Vasudevan, P. Warden, M. Wicke, Y. Yu, and X. Zheng. Tensorflow: A system for largescale machine learning. In *12th USENIX Symposium on Operating Systems Design and Implementation (OSDI 16)*, pages 265–283, 2016.
- <span id="page-34-0"></span>[3] M. Arjovsky, S. Chintala, and L. Bottou. Wasserstein GAN. *arXiv e-prints*, arXiv:1701.07875, 2017.
- <span id="page-34-2"></span>[4] S. Arora, R. Ge, Y. Liang, T. Ma, and Y. Zhang. Generalization and equilibrium in generative adversarial nets (GANs). In D. Precup and Y. W. Teh, editors, *Proceedings of the 34th International Conference on Machine Learning*, Proceedings of Machine Learning Research, vol. 70, pages 224–232, 2017.
- <span id="page-34-7"></span>[5] H. Attouch, X. Goudou, and P. Redont. The heavy ball with friction method, I. the continuous dynamical system: Global exploration of the local minima of a real-valued function by asymptotic analysis of a dissipative dynamical system. *Communications in Contemporary Mathematics*, 2(1):1–34, 2000.
- <span id="page-34-1"></span>[6] D. Berthelot, T. Schumm, and L. Metz. BEGAN: Boundary equilibrium generative adversarial networks. *arXiv e-prints*, arXiv:1703.10717, 2017.
- <span id="page-34-3"></span>[7] D. P. Bertsekas and J. N. Tsitsiklis. Gradient convergence in gradient methods with errors. *SIAM Journal on Optimization*, 10(3):627–642, 2000.
- <span id="page-34-5"></span>[8] S. Bhatnagar, H. L. Prasad, and L. A. Prashanth. *Stochastic Recursive Algorithms for Optimization*. Lecture Notes in Control and Information Sciences. Springer-Verlag London, 2013.
- <span id="page-34-4"></span>[9] V. S. Borkar. Stochastic approximation with two time scales. *Systems & Control Letters*, 29(5):291–294, 1997.
- <span id="page-34-8"></span>[10] V. S. Borkar and S. P. Meyn. The O.D.E. method for convergence of stochastic approximation and reinforcement learning. *SIAM Journal on Control and Optimization*, 38(2):447–469, 2000.
- <span id="page-34-6"></span>[11] T. Che, Y. Li, A. P. Jacob, Y. Bengio, and W. Li. Mode regularized generative adversarial networks. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2017. arXiv:1612.02136.
- <span id="page-34-9"></span>[12] C. Chelba, T. Mikolov, M. Schuster, Q. Ge, T. Brants, P. Koehn, and T. Robinson. One billion word benchmark for measuring progress in statistical language modeling. *arXiv e-prints*, arXiv:1312.3005, 2013.

- <span id="page-35-8"></span>[13] D.-A. Clevert, T. Unterthiner, and S. Hochreiter. Fast and accurate deep network learning by exponential linear units (ELUs). In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2016. arXiv:1511.07289.
- <span id="page-35-12"></span>[14] D. DiCastro and R. Meir. A convergent online single time scale actor critic algorithm. *J. Mach. Learn. Res.*, 11:367–410, 2010.
- <span id="page-35-19"></span>[15] D. C. Dowson and B. V. Landau. The Fréchet distance between multivariate normal distributions. *Journal of Multivariate Analysis*, 12:450–455, 1982.
- <span id="page-35-18"></span>[16] M. Fréchet. Sur la distance de deux lois de probabilité. *C. R. Acad. Sci. Paris*, 244:689–692, 1957.
- <span id="page-35-16"></span>[17] S. Gadat, F. Panloup, and S. Saadane. Stochastic heavy ball. *arXiv e-prints*, arXiv:1609.04228, 2016.
- <span id="page-35-0"></span>[18] I. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. Warde-Farley, S. Ozair, A. Courville, and Y. Bengio. Generative adversarial nets. In Z. Ghahramani, M. Welling, C. Cortes, N. D. Lawrence, and K. Q. Weinberger, editors, *Advances in Neural Information Processing Systems 27*, pages 2672–2680, 2014.
- <span id="page-35-4"></span>[19] I. J. Goodfellow. On distinguishability criteria for estimating generative models. In *Workshop at the International Conference on Learning Representations (ICLR)*, 2015. arXiv:1412.6515.
- <span id="page-35-3"></span>[20] I. J. Goodfellow. NIPS 2016 tutorial: Generative adversarial networks. *arXiv e-prints*, arXiv:1701.00160, 2017.
- <span id="page-35-17"></span>[21] X. Goudou and J. Munier. The gradient and heavy ball with friction dynamical systems: the quasiconvex case. *Mathematical Programming*, 116(1):173–191, 2009.
- <span id="page-35-5"></span>[22] P. Grnarova, K. Y. Levy, A. Lucchi, T. Hofmann, and A. Krause. An online learning approach to generative adversarial networks. *arXiv e-prints*, arXiv:1706.03269, 2017.
- <span id="page-35-2"></span>[23] I. Gulrajani, F. Ahmed, M. Arjovsky, V. Dumoulin, and A. Courville. Improved training of Wasserstein GANs. *arXiv e-prints*, arXiv:1704.00028, 2017. Advances in Neural Information Processing Systems 31 (NIPS 2017).
- <span id="page-35-13"></span>[24] M. W. Hirsch. Convergent activation dynamics in continuous time networks. *Neural Networks*, 2(5):331–349, 1989.
- <span id="page-35-7"></span>[25] R. D. Hjelm, A. P. Jacob, T. Che, K. Cho, and Y. Bengio. Boundary-seeking generative adversarial networks. *arXiv e-prints*, arXiv:1702.08431, 2017.
- <span id="page-35-15"></span>[26] S. Hochreiter and J. Schmidhuber. Flat minima. *Neural Computation*, 9(1):1–42, 1997.
- <span id="page-35-1"></span>[27] P. Isola, J.-Y. Zhu, T. Zhou, and A. A. Efros. Image-to-image translation with conditional adversarial networks. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, 2017. arXiv:1611.07004.
- <span id="page-35-9"></span>[28] P. Karmakar and S. Bhatnagar. Two time-scale stochastic approximation with controlled Markov noise and off-policy temporal-difference learning. *Mathematics of Operations Research*, 2017.
- <span id="page-35-14"></span>[29] D. P. Kingma and J. L. Ba. Adam: A method for stochastic optimization. In *Proceedings of the International Conference on Learning Representations (ICLR))*, 2015. arXiv:1412.6980.
- <span id="page-35-10"></span>[30] V. R. Konda. *Actor-Critic Algorithms*. PhD thesis, Department of Electrical Engineering and Computer Science, Massachusetts Institute of Technology, 2002.
- <span id="page-35-20"></span>[31] V. R. Konda and V. S. Borkar. Actor-critic-type learning algorithms for Markov decision processes. *SIAM J. Control Optim.*, 38(1):94–123, 1999.
- <span id="page-35-11"></span>[32] V. R. Konda and J. N. Tsitsiklis. Linear stochastic approximation driven by slowly varying Markov chains. *Systems & Control Letters*, 50(2):95–102, 2003.
- <span id="page-35-21"></span>[33] V. R. Konda and J. N. Tsitsiklis. Convergence rate of linear two-time-scale stochastic approximation. *The Annals of Applied Probability*, 14(2):796–819, 2004.
- <span id="page-35-22"></span>[34] A. Krizhevsky, I. Sutskever, and G. E. Hinton. ImageNet classification with deep convolutional neural networks. In *Proceedings of the 25th International Conference on Neural Information Processing Systems*, pages 1097–1105, 2012.
- <span id="page-35-6"></span>[35] H. J. Kushner and G. G. Yin. *Stochastic Approximation Algorithms and Recursive Algorithms and Applications*. Springer-Verlag New York, second edition, 2003.

- <span id="page-36-1"></span>[36] C. Ledig, L. Theis, F. Huszar, J. Caballero, A. P. Aitken, A. Tejani, J. Totz, Z. Wang, and W. Shi. Photo-realistic single image super-resolution using a generative adversarial network. *arXiv e-prints*, arXiv:1609.04802, 2016.
- <span id="page-36-5"></span>[37] C.-L. Li, W.-C. Chang, Y. Cheng, Y. Yang, and B. Póczos. MMD GAN: Towards deeper understanding of moment matching network. In *Advances in Neural Information Processing Systems 31 (NIPS 2017)*, 2017. arXiv:1705.08584.
- <span id="page-36-9"></span>[38] J. Li, A. Madry, J. Peebles, and L. Schmidt. Towards understanding the dynamics of generative adversarial networks. *arXiv e-prints*, arXiv:1706.09884, 2017.
- <span id="page-36-3"></span>[39] J. H. Lim and J. C. Ye. Geometric GAN. *arXiv e-prints*, arXiv:1705.02894, 2017.
- <span id="page-36-7"></span>[40] S. Liu, O. Bousquet, and K. Chaudhuri. Approximation and convergence properties of generative adversarial learning. In *Advances in Neural Information Processing Systems 31 (NIPS 2017)*, 2017. arXiv:1705.08991.
- <span id="page-36-18"></span>[41] Z. Liu, P. Luo, X. Wang, and X. Tang. Deep learning face attributes in the wild. In *Proceedings of International Conference on Computer Vision (ICCV)*, 2015.
- <span id="page-36-8"></span>[42] L. M. Mescheder, S. Nowozin, and A. Geiger. The numerics of GANs. In *Advances in Neural Information Processing Systems 31 (NIPS 2017)*, 2017. arXiv:1705.10461.
- <span id="page-36-12"></span>[43] L. Metz, B. Poole, D. Pfau, and J. Sohl-Dickstein. Unrolled generative adversarial networks. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2017. arXiv:1611.02163.
- <span id="page-36-16"></span>[44] A. Mokkadem and M. Pelletier. Convergence rate and averaging of nonlinear two-time-scale stochastic approximation algorithms. *The Annals of Applied Probability*, 16(3):1671–1702, 2006.
- <span id="page-36-6"></span>[45] Y. Mroueh and T. Sercu. Fisher GAN. In *Advances in Neural Information Processing Systems 31 (NIPS 2017)*, 2017. arXiv:1705.09675.
- <span id="page-36-4"></span>[46] V. Nagarajan and J. Z. Kolter. Gradient descent GAN optimization is locally stable. *arXiv e-prints*, arXiv:1706.04156, 2017. Advances in Neural Information Processing Systems 31 (NIPS 2017).
- <span id="page-36-17"></span>[47] Y. Nesterov. A method of solving a convex programming problem with convergence rate o(1/k<sup>2</sup> ). *Soviet Mathematics Doklady*, 27:372–376, 1983.
- <span id="page-36-19"></span>[48] Y. Netzer, T. Wang, A. Coates, A. Bissacco, B. Wu, and A. Y. Ng. Reading digits in natural images with unsupervised feature learning. In *NIPS Workshop on Deep Learning and Unsupervised Feature Learning 2011*, 2011.
- <span id="page-36-13"></span>[49] B. T. Polyak. Some methods of speeding up the convergence of iteration methods. *USSR Computational Mathematics and Mathematical Physics*, 4(5):1–17, 1964.
- <span id="page-36-10"></span>[50] H. L. Prasad, L. A. Prashanth, and S. Bhatnagar. Two-timescale algorithms for learning Nash equilibria in general-sum stochastic games. In *Proceedings of the 2015 International Conference on Autonomous Agents and Multiagent Systems (AAMAS '15)*, pages 1371–1379, 2015.
- <span id="page-36-0"></span>[51] A. Radford, L. Metz, and S. Chintala. Unsupervised representation learning with deep convolutional generative adversarial networks. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2016. arXiv:1511.06434.
- <span id="page-36-11"></span>[52] A. Ramaswamy and S. Bhatnagar. Stochastic recursive inclusion in two timescales with an application to the lagrangian dual problem. *Stochastics*, 88(8):1173–1187, 2016.
- <span id="page-36-2"></span>[53] T. Salimans, I. J. Goodfellow, W. Zaremba, V. Cheung, A. Radford, and X. Chen. Improved techniques for training GANs. In D. D. Lee, M. Sugiyama, U. V. Luxburg, I. Guyon, and R. Garnett, editors, *Advances in Neural Information Processing Systems 29*, pages 2234–2242, 2016.
- <span id="page-36-15"></span>[54] V. B. Tadic. Almost sure convergence of two time-scale stochastic approximation algorithms. ´ In *Proceedings of the 2004 American Control Conference*, volume 4, pages 3802–3807, 2004.
- <span id="page-36-14"></span>[55] L. Theis, A. van den Oord, and M. Bethge. A note on the evaluation of generative models. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2016. arXiv:1511.01844.

- <span id="page-37-1"></span>[56] I. Tolstikhin, S. Gelly, O. Bousquet, C.-J. Simon-Gabriel, and B. Schölkopf. AdaGAN: Boosting generative models. *arXiv e-prints*, arXiv:1701.02386, 2017. Advances in Neural Information Processing Systems 31 (NIPS 2017).
- <span id="page-37-2"></span>[57] R. Wang, A. Cully, H. J. Chang, and Y. Demiris. MAGAN: margin adaptation for generative adversarial networks. *arXiv e-prints*, arXiv:1704.03817, 2017.
- <span id="page-37-4"></span>[58] L. N. Wasserstein. Markov processes over denumerable products of spaces describing large systems of automata. *Probl. Inform. Transmission*, 5:47–52, 1969.
- <span id="page-37-3"></span>[59] Y. Wu, Y. Burda, R. Salakhutdinov, and R. B. Grosse. On the quantitative analysis of decoderbased generative models. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2017. arXiv:1611.04273.
- <span id="page-37-6"></span>[60] F. Yu, Y. Zhang, S. Song, A. Seff, and J. Xiao. LSUN: construction of a large-scale image dataset using deep learning with humans in the loop. *arXiv e-prints*, arXiv:1506.03365, 2015.
- <span id="page-37-0"></span>[61] J. Zhang, D. Zheng, and M. Chiang. The impact of stochastic noisy feedback on distributed network utility maximization. In *IEEE INFOCOM 2007 - 26th IEEE International Conference on Computer Communications*, pages 222–230, 2007.

<span id="page-37-5"></span>

## List of Figures

|                | $\mathcal{L}$<br>Oscillation in GAN training                                                                   |
|----------------|----------------------------------------------------------------------------------------------------------------|
| $\mathcal{L}$  | Heavy Ball with Friction<br>4                                                                                  |
| $\mathcal{F}$  | FID evaluated for different disturbances<br>6                                                                  |
| $\overline{4}$ | TTUR and single time-scale update with toy data. $\dots \dots \dots \dots \dots \dots \dots$<br>$\tau$         |
| 5              | FID for DCGAN on CelebA, CIFAR-10, SVHN, and LSUN Bedrooms.<br>8                                               |
| 6              | FID for WGAN-GP trained on CIFAR-10 and LSUN Bedrooms.<br>8                                                    |
| 7              | Performance of WGAN-GP on One Billion Word<br>9                                                                |
| A8             | FID and Inception Score Comparison<br>13                                                                       |
| A9             | CelebA Samples with FID 500 and 300<br>14                                                                      |
|                | A10 CelebA Samples with FID 133 and $100 \ldots \ldots \ldots \ldots \ldots \ldots \ldots \ldots \ldots$<br>14 |
|                | A11 CelebA Samples with FID 45 and 13<br>15                                                                    |
|                | A12 CelebA Samples with FID 3 $\dots \dots \dots \dots \dots \dots \dots \dots \dots \dots \dots \dots$<br>15  |
|                | A13 FID for BEGAN trained on CelebA and LSUN Bedrooms.<br>33                                                   |
|                | A14 Learning dynamics of two networks.<br>34                                                                   |

## List of Tables

| 1 Results DCGAN and WGAN-GP                                        | 9  |
|--------------------------------------------------------------------|----|
| A2 Results WGAN-GP on Image Data                                   | 32 |
| A3 Samples of the One Billion Word benchmark generated by WGAN-GP. | 33 |
| A4 Results WGAN-GP on One Billion Word                             | 33 |
# DREAM+: Efficient Dataset Distillation by Bidirectional Representative <PERSON><PERSON><sup>\*</sup>, <PERSON><PERSON><PERSON><sup>\*</sup>, <PERSON><sup>†</sup>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON><sup>‡</sup>

**Abstract**—Dataset distillation plays a crucial role in creating compact datasets with similar training performance compared with original large-scale ones. This is essential for addressing the challenges of data storage and training costs. Prevalent methods facilitate knowledge transfer by matching the gradients, embedding distributions, or training trajectories of synthetic images with those of the sampled original images. Although there are various matching objectives, currently the strategy for selecting original images is limited to naive random sampling. We argue that random sampling overlooks the evenness of the selected sample distribution, which may result in noisy or biased matching targets. Besides, the sample diversity is also not constrained by random sampling. Additionally, current methods predominantly focus on single-dimensional matching, where information is not fully utilized. To address these challenges, we propose a novel matching strategy called Dataset **D**istillation by Bidirectional **RE**present**A**tive **M**atching (DREAM+), which selects representative original images for bidirectional matching. DREAM+ is applicable to a variety of mainstream dataset distillation frameworks and significantly reduces the number of distillation iterations by more than 15 times without affecting performance. Given sufficient training time, DREAM+ can further improve the performance and achieve state-of-the-art results. We have released the code at [github.com/NUS-HPC-AI-Lab/DREAM+.](https://github.com/lyq312318224/DREAM/)

✦

**Index Terms**—Dataset distillation, Bidirectional optimization, Training efficiency.

# 1 INTRODUCTION

THE development of deep learning has ushered in a remarkable<br>era of achievements in computer vision, as evidenced by<br>mumorous influential works [1], [2], [2], [4], [5], [6], [7], [9] HE development of deep learning has ushered in a remarkable numerous influential works [\[1\]](#page-11-0), [\[2\]](#page-11-1), [\[3\]](#page-11-2), [\[4\]](#page-12-0), [\[5\]](#page-12-1), [\[6\]](#page-12-2), [\[7\]](#page-12-3), [\[8\]](#page-12-4). However, these achievements are often established upon massive datasets, especially for recent large-scale models [\[9\]](#page-12-5), [\[10\]](#page-12-6), [\[11\]](#page-12-7), [\[12\]](#page-12-8). In addition to the extraordinary effort for data collection and processing, the dependency on massive data, in turn, leads to severe problems for common deep learning practices [\[13\]](#page-12-9), [\[14\]](#page-12-10), [\[15\]](#page-12-11). On the one hand, training on such large datasets requires enormous calculation resources, which can be infeasible for resource-restricted researchers. On the other hand, the storage and maintenance demands for massive data are also hard to afford. [\[16\]](#page-12-12), [\[17\]](#page-12-13). In response, various methodologies have emerged to tackle the cumbersome data burden by compressing the scale of the training data [\[13\]](#page-12-9), [\[18\]](#page-12-14), [\[19\]](#page-12-15), [\[20\]](#page-12-16).

A group of methods attempt to address the problem through selecting representative samples from the original dataset, denoted as coreset methods [\[15\]](#page-12-11), [\[21\]](#page-12-17). However, along with the selection, a large number of samples are directly deserted, where certain information for encapsulating the full essence of the dataset is lost. As a result, the performance is often not satisfactory under high compression ratios [\[22\]](#page-12-18), [\[23\]](#page-12-19), [\[24\]](#page-12-20). On the other hand, dataset distillation has emerged as a leading strategy, aiming to distill the information of the whole dataset into surrogate sets of manageable sizes [\[25\]](#page-12-21), [\[26\]](#page-12-22), [\[27\]](#page-12-23), [\[28\]](#page-12-24), [\[29\]](#page-12-25). This paradigm begins with a small number of learnable image tensors and iteratively refines them through alignment with various facets of the original data, includ-

- *Y. Liu, K. Wang, Y. You are with the National University of Singapore, Singapore. (Corresponding author: Yang You. e-mail: <EMAIL>)*
- *J. Gu and W. Jiang are with Zhejiang University, China.*
- *Z. Zhu is with Tsinghua University, China.*
- *K. Zhang is with Shanghai AI Laboratory, China.*

*\*Equal contribution, †Project lead*

ing training gradients [\[16\]](#page-12-12), [\[27\]](#page-12-23), embedding distributions [\[26\]](#page-12-22), [\[30\]](#page-12-26), or training trajectories [\[25\]](#page-12-21), [\[29\]](#page-12-25). This noble pursuit has become pivotal in addressing the data problem and has attracted significant scholarly attention [\[31\]](#page-12-27), [\[32\]](#page-12-28), [\[33\]](#page-12-29), [\[34\]](#page-12-30).

Despite the notable performance gains and compression ratios achieved by dataset distillation [\[27\]](#page-12-23), a persistent challenge remains, which is the prolonged duration of the distillation process. For example, for distilling information into 50 images per class (IPC) on CIFAR-10 dataset, the expert trajectory training time and distillation time for MTT [\[25\]](#page-12-21) take approximately 16 hours or more. IDC [\[27\]](#page-12-23) requires more than 20 hours of distillation time to achieve considerable performance. On CIFAR-100 dataset, IDC requires more than 40 hours to finish 50 IPC distillation. We claim that the training efficiency of dataset distillation methods is largely influenced by two key elements: the sampling strategy for selecting original matching images and the optimization objectives.

Dataset distillation enriches the information in synthetic images by aligning training characteristics [\[16\]](#page-12-12), [\[30\]](#page-12-26). Normally, random sampling is adopted for forming a mini-batch of original images to reduce the required memory in the training stage [\[16\]](#page-12-12), [\[30\]](#page-12-26). However, random sampling often overlooks the evenness of sample distribution, for which those with larger training gradients may dominate the optimization process for gradient matching [\[26\]](#page-12-22). Besides, random sampling also fails to constrain the diversity within small batches, leading to unfaithful representation of the original data. Another aspect overlooked by previous dataset distillation methods is the optimization objectives. With various alignment paradigms proposed [\[16\]](#page-12-12), [\[27\]](#page-12-23), [\[30\]](#page-12-26), [\[35\]](#page-12-31), there are not yet works attempting to fuse these optimization targets together. We argue that the single-dimensional optimization objective cannot thoroughly reflect the characteristics of the original data, and hence restricts the training efficiency.

Accordingly, we introduce a novel method named as Efficient Dataset Distillation by Bidirectional REpresentAtive Matching

<span id="page-1-0"></span>Image /page/1/Figure/1 description: This is a histogram showing the distribution of gradient norms (L2) for different sample types. The x-axis represents the gradient norm (L2), ranging from 0 to 40. The y-axis represents the sample number. Three categories of samples are shown: 'All samples' (grey bars), 'Random samples' (blue bars), and 'DREAM+ samples' (yellow bars). The histogram indicates that 'All samples' have a peak around a gradient norm of 2, with a mean of 8.8 and a standard deviation of 7.9. 'Random samples' are distributed with a mean of 11.4 and a standard deviation of 9.5, with a dashed blue line indicating the mean. 'DREAM+ samples' are concentrated at lower gradient norms, with a dashed yellow line indicating their mean.

(a) The gradient norm distribution of the ship class in CIFAR-10.

Image /page/1/Figure/3 description: The image displays two scatter plots, each illustrating a different sampling method. The left plot, labeled "DREAM+", shows several red stars representing synthetic images connected by solid red arrows to yellow circles representing matched original images. The right plot, labeled "Random Sampling", shows similar red stars connected by dashed blue lines to blue circles representing matched original images. Both plots are set against a background of numerous small gray dots, suggesting a data distribution. A legend at the bottom indicates that yellow and blue circles represent "Matched original images" and red stars represent "Synthetic images".

(b) The migration of synthetic samples during training.

Fig. 1: Samples on the decision boundaries usually provide larger gradients, which biases the gradient matching optimization. Random sampling (left) overlooks the evenness of of the selected sample distribution, resulting in unstable optimization process of the synthesized samples. By only matching with proper gradients from representative original samples, our proposed DREAM+ (right) greatly improves the training efficiency of dataset distillation tasks. Best viewed in color.

(DREAM+) for more efficient dataset distillation. First, for sample selection, a clustering process is performed periodically within each class to generate sub-clusters that reflect the sample distribution. The samples closest to the center of each sub-cluster are selected to form the target mini-batch for matching. Selection of such center samples serves the dual purpose of representing nearby samples and achieving uniform coverage of the entire class distribution. As illustrated in Fig. [1a,](#page-1-0) the clustering-based selection results in a set of samples with less gradient variance compared with random sampling. Secondly, we integrate both forward features and backward gradients to provide bidirectional optimization directions. This bidirectional matching paradigm significantly improves the training stability, leading to a smoother and more robust distillation process. For the synthetic image initialization, we adopt a clustering-based strategy, akin to [\[28\]](#page-12-24), where center samples from each sub-cluster are employed.

DREAM+ can be easily integrated into current dataset distillation frameworks. Comparative evaluations against common random sample selection and single-dimensional matching techniques highlight DREAM<sup>+</sup>'s ability to enhance the training efficiency. We conduct extensive experiments to demonstrate that DREAM+ achieves comparable performance to baseline methods in less than one-fifteenth the number of iterations required. Moreover, with the same training iteration set as other state-of-the-art methods, DREAM+ achieves even better performance. For example, DREAM+ surpasses IDC by 2.3% on CIFAR-100 with 10 IPC.

This work expands upon our earlier conference paper [\[36\]](#page-12-32) and introduces several new contributions:

- DREAM+, an enhanced version of DREAM, effectively addresses the training efficiency issue associated with single-dimensional matching during dataset distillation. The improved matching technique better captures the characteristics of the original data.
- The experiments across diverse datasets and dataset distillation techniques demonstrates that DREAM+ further accelerates training by over 15 times without compromising the distillation performance.
- Beyond the core methodology, we provide supplementary results, analyses, and visualizations that delve into the intricacies of bidirectional matching, offering a more comprehensive understanding of this innovative component.

# 2 RELATED WORKS

## 2.1 Coreset Selection

Coreset selection selects a subset of data based on specific metrics [\[37\]](#page-12-33), [\[38\]](#page-12-34). Lapedriza et al. measure sample importance based on the benefits gained from model training on each sample [\[21\]](#page-12-17). Toneva et al. observe that samples exhibit varying forgetting characteristics, with easily forgettable samples containing more information [\[15\]](#page-12-11). Coreset-based methods are also widely used in continual learning [\[39\]](#page-12-35), [\[40\]](#page-12-36), [\[41\]](#page-12-37) and active learning tasks [\[42\]](#page-12-38). Shleifer et al. expedite neural network architecture search by selecting a group of "easier" samples [\[43\]](#page-12-39). While coreset-based methods are practical, they face limitations in extracting rich information from a small subset of original samples, restricting their ability to further enhance compression ratios.

## 2.2 Dataset distillation

Dataset distillation is implemented by synthesizing image samples guided by various optimization objectives. Wang et al. introduce the concept of dataset distillation from the perspective of optimization and update synthetic images using a meta-learning approach [\[13\]](#page-12-9). Subsequent works employ a variety of optimization targets to constrain image synthesis, including matching training gradients [\[16\]](#page-12-12), [\[35\]](#page-12-31), [\[44\]](#page-12-40), embedding distributions [\[26\]](#page-12-22), [\[30\]](#page-12-26), and training trajectories [\[25\]](#page-12-21) of original images. IDC injects additional information into synthetic samples under fixed storage constraints [\[27\]](#page-12-23). IDM optimizes distribution matching by expanding feature dimensions and model parameter space [\[45\]](#page-12-41). Nguyen et al. develop a distributed meta-learning framework and incorporate kernel approximation methods [\[46\]](#page-12-42). RFAD accelerates the metric computation through random feature approximation [\[47\]](#page-12-43). HaBa leverages data hallucination networks to construct base images and enhance the representation capability of distilled datasets [\[48\]](#page-12-44). FRePo introduces an efficient meta-gradient computation method and a "model pool" to mitigate the overfitting towards specific architectures [\[49\]](#page-12-45). Some methods use generative models to complete dataset distillation, such as GLaD [\[50\]](#page-12-46) and ITGAN [\[51\]](#page-12-47), which compress datasets into latent variables in feature space and then use decoders for data synthesis. DiM [\[52\]](#page-12-48) transfers knowledge by distilling datasets into generative models.

Dataset distillation methods significantly enhance compression ratios by incorporating more information into synthetic images. However, recent state-of-the-art methods often require a large

<span id="page-2-0"></span>Image /page/2/Figure/1 description: The image displays three plots. The first plot, titled "Accuracy-Iteration", shows the accuracy of different methods (DREAM+, DREAM, Random, Central, Boundary) over 1000 iterations. The y-axis ranges from 40 to 68, and the x-axis represents iterations from 0 to 1000. The second plot, titled "MMD-Iteration", illustrates the MMD (Maximum Mean Discrepancy) values for "Ours" and "Random" methods over 100 iterations. The y-axis ranges from 1 to 4, and the x-axis represents iterations from 0 to 90. The third plot is a "Clustering T-SNE" visualization, showing data points clustered into different groups, each represented by a color and a star marker indicating the cluster center. The x-axis is labeled "comp-1" and ranges from -40 to 40, while the y-axis is labeled "comp-2" and ranges from -40 to 30.

(a) The accuracy curve with different strategies for selecting original images.

Image /page/2/Figure/3 description: The image contains text that reads "(b) The MMD curve between the sampled mini-batch and the corresponding class data."

(c) Example clustering and sub-cluster center results of DREAM+.

Fig. 2: The original images obtained by random sampling have uneven distributions, which may result in noisy or biased matching targets. Besides, the coverage of random sampling on the whole sample space is low and has large fluctuations during training. Comparatively, the centers selected by DREAM+ (stars) are representative for corresponding sub-clusters, and are evenly distributed over the whole class feature space. Experiments for (a) and (b) are conducted under 10 images-per-class setting on CIFAR-10. Best viewed in color.

number of iterations to achieve desired validation accuracy, indicating low training efficiency. In this work, we focus on designing a novel matching strategy to improve the efficiency of dataset distillation training.

## 2.3 Clustering

Clustering is an unsupervised technique used to group data samples into distinct clusters [\[53\]](#page-12-49). Several clustering methods exist, each with its unique characteristics and applications. Kmeans [\[54\]](#page-12-50), [\[55\]](#page-12-51) is a well-known method that requires specifying the number of target clusters. It optimizes the data partition to create clusters with similar sizes [\[56\]](#page-12-52). Density-Based Spatial Clustering of Applications with Noise (DBSCAN) relies on density and does not necessitate a prior knowledge of the number of clusters. It gradually forms clusters by including data points within a specified tolerance range [\[57\]](#page-12-53). DBSCAN is versatile and can handle datasets of various shapes. However, it has some limitations, including unstable cluster sizes, exclusion of outliers from clusters, and potential merging of closely located clusters. Hierarchical clustering methods encompass two main approaches: Agglomerative and Divisive. The former progressively merges multiple clusters until a predefined condition is met, resulting in a hierarchical structure. Conversely, the latter divides a cluster into smaller segments, iteratively refining the hierarchy [\[58\]](#page-12-54).

### 2.4 Differences from Related Works

Several recent works have been proposed to improve the efficiency of dataset distillation. It's essential to understand the distinctions between these approaches and our proposed method. Random Feature Approximation for Dataset Distillation (RFAD) reduces the computational complexity associated with Kernel Inducing Points (KIP) by employing random feature approximation [\[47\]](#page-12-43). RFAD primarily targets computational complexity reduction within the context of KIP. In contrast, our proposed method, DREAM+, concentrates on improving the training efficiency by introducing bidirectional matching strategies with selected representative original images. There are no contradictory between these two approaches. Instead, they address different aspects of efficiency issues for dataset distillation.

Jiang et al. analyze the limitations of the gradient matching method and introduce the concept of matching multi-level gradients [\[44\]](#page-12-40). Additionally, there are other methods such as those by Lorraine et al. [\[59\]](#page-12-55) and Vicol et al. [\[60\]](#page-12-56), which examine shortcomings in existing techniques from the perspective of twolevel optimization and enhance efficiency accordingly. In contrast, DREAM+ addresses training efficiency from the perspective of sampling and matching objectives within optimization-based methods. It offers seamless integration with various dataset distillation approaches, resulting in a substantial reduction in required training iterations. These distinctions emphasize the unique contributions of DREAM+ in the area of dataset distillation efficiency.

# 3 METHOD

Aiming at tackling the issue of low training efficiency in dataset distillation tasks, we propose a novel distillation approach denoted as Dataset Distillation by Bidirectional REpresentAtive Matching (DREAM+). DREAM+ is designed to enhance the stability and robustness of the training process by focusing on bidirectional matching with representative original images. In this section, we outline the foundational training framework for dataset distillation, share our analysis on the training efficiency problem, and provide a comprehensive overview of the DREAM+ methodology.

### 3.1 Preliminaries

Given a large-scale dataset  $\mathcal{T} = \{(\mathbf{x}_t^i, y_t^i)\}_{i=1}^{|\mathcal{T}|}$ , the target of dataset distillation is to create a compact surrogate dataset  $S =$  $\{(x_s^i, y_s^i)\}_{i=1}^{|S|}$  with minimal information loss, where  $|S| \ll |\mathcal{T}|$ . Information loss is typically quantified by the performance drop observed when training a model on the original images  $T$  compared with the surrogate set  $S$ .

Commonly adopted optimization-based methods follow a synthetic pipeline. Initially, the surrogate set  $S$  is initialized with randomly selected original images from  $T$ . These synthetic images are then updated, guided by matching objectives  $\phi(\cdot)$ , to mimic

<span id="page-3-0"></span>Image /page/3/Figure/1 description: This diagram illustrates a process involving original and synthetic images. The process begins with a full dataset that undergoes clustering and sampling to produce original images. These original images, along with synthetic images, are fed into a model. The model outputs t-distribution, gs, and gt. The synthetic images also produce an s-distribution. Gradient matching is performed on gt and gs. The t-distribution and s-distribution are then combined with gradient matching and distribution matching to calculate a loss. This loss is used to update the synthetic image through backpropagation. The diagram also defines 't: Original image' and 's: Synthetic image', and indicates 'Data pass' with dashed arrows and 'Backpropagation' with dashed red arrows.

Fig. 3: The training pipeline of the proposed DREAM+ strategy.

the distribution of the original images. This process is formulated as follows:

$$
S^* = \arg\min_{S} \mathbf{D} \left( \phi(S), \phi(\mathcal{T}) \right),\tag{1}
$$

where  **stands for the matching metric. Typically, we opt for** either training gradients or embedding features as the matching target, denoted as  $\phi(\cdot)$ . When we contemplate a random model  $\mathcal{M}_{\theta}$  with training parameters  $\theta$ , the objective for S is to produce gradients that closely mirror those of  $T$  throughout the training process of  $\mathcal{M}_{\theta}$  or embedding feature distributions identical to that of random  $\mathcal{M}_{\theta}$ . This objective can be expressed as:

$$
S^* = \arg\min_{S} \mathbf{D} \left( \nabla_{\theta} \mathcal{L}(\mathcal{M}_{\theta}(\mathcal{A}(S))), \nabla_{\theta} \mathcal{L}(\mathcal{M}_{\theta}(\mathcal{A}(T)))) \right),
$$
\nor

$$
S^* = \arg\min_{S} \mathbf{D} \left( \xi(\mathcal{M}_{\theta}(\mathcal{A}(S))), \xi(\mathcal{M}_{\theta}(\mathcal{A}(T)))) \right), \quad (2b)
$$

where  $\mathcal{L}(\cdot, \cdot)$  represents the training loss,  $\xi$  represents averaging the features in the channel dimension, and  $A$  denotes the differentiable augmentation techniques [\[61\]](#page-13-0), [\[62\]](#page-13-1), [\[63\]](#page-13-2), [\[64\]](#page-13-3).

In practice, the matching objectives are calculated on the synthetic images and a mini-batch of original images  $\{(\mathbf{x}_t^i, y_t^i)\}_{i=1}^N$ sampled from  $T$  with the same class labels. The objective matching and training of  $\mathcal{M}_{\theta}$  occur in an alternating manner. This process, involving the matching of gradients or embedding features at different training stages, constitutes the inner optimization loop of dataset distillation. The inner loop is iterated with different random  $\mathcal{M}_{\theta}$  models to introduce diversity in matching gradients and features, denoted as the outer optimization loop.

Recent literature introduces a series of matching objectives that achieve significant test accuracy when trained on compact synthetic datasets [\[25\]](#page-12-21), [\[26\]](#page-12-22), [\[27\]](#page-12-23). However, it is essential to note that the dataset distillation process itself remains time-consuming, indicating low training efficiency. In this work, we delve into the relationships between the training efficiency and the selection of original images utilized for matching, as well as the interplay between training efficiency and the chosen matching objectives. Drawing insights from the analysis, we introduce an innovative bidirectional matching strategy.

### 3.2 Observations on Training Efficiency

During the process of dataset distillation, knowledge is condensed by matching a subset of original images within a defined parameter space. For memory restriction, there have to be sample selection for original images to form a mini-batch. The selection of these original images can significantly affect training efficiency, where recent literature usually adopts random sampling [\[16\]](#page-12-12), [\[27\]](#page-12-23). Besides, the matching objectives can also influence the training efficiency. Although there have been various objectives proposed, most of previous works rely on a single aspects among them [\[27\]](#page-12-23), [\[30\]](#page-12-26). Without losing the generality, here we use gradient matching as an example, and illustrate how these factors affect the efficient training of dataset distillation.

<span id="page-3-1"></span>First, we examine the matching effect across samples from different distribution regions. Among all samples in a class, those closer to the center of the whole distribution tend to show higher prediction accuracy, indicating smaller backward gradients. In contrast, those located on the decision boundary show the opposite. In the case of gradient matching, center samples provide poor supervision contributions, while the gradients of boundary samples have a significant impact on the optimization direction. We show in Figure [2a](#page-2-0) the training accuracy curves for synthetic images matched only with center or boundary samples. It is obvious that the small gradients provided by the center samples quickly lose the guidance for the training process. Conversely, while boundary samples are crucial for delineating decision boundaries, relying solely on them for matching introduces chaotic matching targets, ultimately reducing the quality of the distillation process.

Second, we illustrate that random sampling does not guarantee a uniform distribution of samples inside mini-batches throughout the training process. We quantify this by recording the Maximum Mean Discrepancy (MMD) between the selected mini-batch and the overall class distribution during training, as shown in Figure [2b.](#page-2-0) It can be observed that MMD remains at a consistently high level and has large fluctuations throughout the training process.

For gradient matching, when mini-batches cannot effectively and consistently cover the distribution of original samples, the gradient differences between individual samples become unbalanced. Due to the existence of boundary samples with large training gradients, the matching target of the mini-batch may be biased towards those samples, leading to unstable supervision. In addition, unevenly distributed small batches also mean that sample diversity is relatively limited. This imbalance is characterized by information redundancy in dense regions and scarcity of information in sparse regions, making mini-batches insufficient to represent the full width of the original data.

Furthermore, we claim that relying solely on a single opti-

IEEE TRANSACTIONS ON PATTERN ANALYSIS AND MACHINE INTELLIGENCE 5

<span id="page-4-0"></span>TABLE 1: Top-1 accuracy of test models trained on distilled synthetic images on multiple datasets. The distillation training is conducted with ConvNet-3.  $^{\dagger}$  denotes the reported error range is reproduced by us. Best results are marked as **red**.

| Dataset        | MNIST |      |      | FashionMNIST |      |      | SVHN |      |      | CIFAR-10 |      |      | CIFAR-100 |      |      |
|----------------|-------|------|------|--------------|------|------|------|------|------|----------|------|------|-----------|------|------|
|                | 1     | 10   | 50   | 1            | 10   | 50   | 1    | 10   | 50   | 1        | 10   | 50   | 1         | 10   | 50   |
| Random         | 64.9  | 95.1 | 97.9 | 51.4         | 73.8 | 82.5 | 14.6 | 35.1 | 70.9 | 14.4     | 26.0 | 43.4 | 4.2       | 14.6 | 30.0 |
| Herding        | 89.2  | 93.7 | 94.9 | 67.0         | 71.1 | 71.9 | 20.9 | 50.5 | 72.6 | 21.5     | 31.6 | 40.4 | 8.4       | 17.3 | 33.7 |
| K-Center       | 89.3  | 84.4 | 97.4 | 66.9         | 54.7 | 68.3 | 21.0 | 14.0 | 20.1 | 21.5     | 14.7 | 27.0 | -         | -    | -    |
| Forgetting     | 35.5  | 68.1 | 88.2 | 42.0         | 53.9 | 55.0 | 12.1 | 16.8 | 27.2 | 13.5     | 23.3 | 23.3 | 4.5       | 15.1 | 30.5 |
| DD [13]        | -     | 79.5 | -    | -            | -    | -    | -    | -    | -    | -        | 36.8 | -    | -         | -    | -    |
| LD [65]        | 60.9  | 87.3 | 93.3 | -            | -    | -    | -    | -    | -    | 25.7     | 38.3 | 42.5 | 11.5      | -    | -    |
| DC [16]        | 91.7  | 97.4 | 98.8 | 70.5         | 82.3 | 83.6 | 31.2 | 76.1 | 82.3 | 28.3     | 44.9 | 53.9 | 12.8      | 25.2 | -    |
| DSA [35]       | 88.7  | 97.8 | 99.2 | 70.6         | 84.6 | 88.7 | 27.5 | 79.2 | 84.4 | 28.8     | 52.1 | 60.6 | 13.9      | 32.3 | 42.8 |
| DM [30]        | 89.7  | 97.5 | 98.6 | -            | -    | -    | -    | -    | -    | 26.0     | 48.9 | 63.0 | 11.4      | 29.7 | 43.6 |
| CAFE [26]      | 93.1  | 97.2 | 98.6 | 77.1         | 83.0 | 84.8 | 42.6 | 75.9 | 81.3 | 30.3     | 46.3 | 55.5 | 12.9      | 27.8 | 37.9 |
| MTT [25]       | -     | -    | -    | -            | -    | -    | -    | -    | -    | 46.3     | 65.3 | 71.6 | 24.3      | 40.1 | 47.7 |
| IDC [27]       | 94.2  | 98.4 | 99.1 | 81.0         | 86.0 | 86.2 | 68.5 | 87.5 | 90.1 | 50.6     | 67.5 | 74.5 | -         | 45.1 | -    |
| IDM [45]       | -     | -    | -    | -            | -    | -    | -    | -    | -    | 45.6     | 58.6 | 67.5 | 20.1      | 45.1 | 50.0 |
| KIP [46], [66] | 90.1  | 97.5 | 98.3 | 73.5         | 86.8 | 88.0 | 57.3 | 75.0 | 80.5 | 49.9     | 62.7 | 68.6 | 15.7      | 28.3 | -    |
| RFAD [47]      | 94.4  | 98.5 | 98.8 | 78.6         | 87.0 | 88.8 | 52.2 | 74.9 | 80.9 | 53.6     | 66.3 | 71.1 | 26.3      | 33.0 | -    |
| HaBa [48]      | 92.4  | 97.4 | 98.1 | -            | -    | -    | 69.8 | 83.2 | 88.3 | 48.3     | 69.9 | 74.0 | 33.4      | 40.2 | 47.0 |
| FRePo [49]     | 93.0  | 98.6 | 99.2 | 75.6         | 86.2 | 89.6 | -    | -    | -    | 46.8     | 65.5 | 71.7 | 28.7      | 42.5 | 44.3 |
| DREAM          | 95.7  | 98.6 | 99.2 | 81.3         | 86.4 | 86.8 | 69.8 | 87.9 | 90.5 | 51.1     | 69.4 | 74.8 | 29.5      | 46.8 | 52.6 |
| DREAM+         | 96.1  | 98.6 | 99.2 | 82.6         | 87.2 | 87.6 | 71.8 | 88.9 | 91.5 | 52.5     | 69.9 | 75.3 | 29.7      | 47.4 | 52.6 |

mization objective accesses limited information, which can also be improved for efficiency. Specifically, by only matching the training gradients, the consistency on feature distribution is overlooked. The feature-level matching typically produces more even distribution coverage over the original dataset [\[26\]](#page-12-22). With a more balanced synthetic data distribution, the gradient supervision can be better applied for optimization, and thereby further improves the training efficiency. In addition to the sample selection, the lack of feature alignment during the matching process also affects the efficiency of knowledge transfer.

These factors jointly lead to unstable optimization during the distillation process, ultimately reducing the training efficiency. We therefore advocate the development of a novel strategy to construct mini-batches with uniform and diverse distributions while optimizing the matching objective to achieve more efficient dataset distillation.

### 3.3 Bidirectional Representative Matching

For a stable and efficient optimization, we select representative original images for bidirectional matching. The selection process follows two basic principles. First, the selected images must be evenly distributed to prevent bias in the matching target. Second, while maintaining diversity, the selected samples should accurately reflect the overall sample distribution within the class.

To this end, we employ a clustering approach to select representative original images. Out of the considerations of uniform sub-cluster size and distribution, we use K-Means [\[54\]](#page-12-50), [\[55\]](#page-12-51), [\[67\]](#page-13-6) for sub-cluster partitioning. As shown in Figure [2c,](#page-2-0) the clustering is performed within each class, generating  $N$  sub-clusters that faithfully represent the sample density. Here,  $N$  represents a predefined hyper-parameter of the mini-batch size of real images. The sub-cluster centers are strategically positioned to evenly distribute the entire class sample space, and simultaneously hold sufficient diversity, perfectly meeting the above principles.

The entire training process is shown in Figure [3.](#page-3-0) First, we randomly initialize a model and train it for one epoch. The initial training helps extract improved features for subsequent phases. The selected mini-batch of images as well as synthetic images with the same class labels are then passed through the model. This step produces embedding features and prediction scores. Next, we compute the classification loss and its corresponding gradient. In  $DREAM+$ , we adopt a distance metric  $D$  that combines embedding distance and gradient distance. The enhancement increases the efficiency of knowledge transfer throughout the process. The combined loss (as derived in Eq. [2\)](#page-3-1) is back-propagated to update the synthetic image.

The sub-clusters are expected to have consistent information with the matching optimization. Therefore, we use the distillation model to extract the features for clustering as well as matching. At the same time, the model is updated in the inner loop to provide more diverse gradient supervision for matching at each stage. To account for the additional time cost, the clustering process is performed every  $I_{int}$  iterations.

Furthermore, similar to [\[28\]](#page-12-24), we apply a clustering at the beginning of the training process. Here, we cluster the data in each class into subclusters, each subcluster corresponding to a predefined number of images per class. The center sample of each sub-cluster is selected as the initialization point of the synthesized image. This balanced, cluster-based initialization method better captures data distribution and accelerates convergence from the beginning of the training process.

# 4 EXPERIMENTS

### 4.1 Datasets and Implementation Details

We validate the effectiveness of our method on several popular datasets, including CIFAR-10 [\[68\]](#page-13-7), CIFAR-100 [\[68\]](#page-13-7), SVHN [\[69\]](#page-13-8), MNIST [\[70\]](#page-13-9), FashionMNIST [\[71\]](#page-13-10), and TinyImageNet [\[72\]](#page-13-11). Our evaluation involves training a model on the distilled synthetic images and testing it on the original testing images. We report Top-1 accuracy to demonstrate performance.

Unless otherwise specified, we employ 3-layer convolutional networks (ConvNet-3) [\[73\]](#page-13-12) with 128 filters and instance normalization [\[74\]](#page-13-13). The matching mini-batch size for original images

<span id="page-5-1"></span>Image /page/5/Figure/1 description: The image displays three line graphs, each titled "Accuracy-Iteration." The y-axis for all graphs represents "Accuracy" ranging from 50 to 70, and the x-axis represents "Iteration" ranging from 0 to 2000. The first graph shows two lines: a teal line labeled "IDC" and a magenta line labeled "DREAM+". A dashed blue line indicates "15x faster" for DREAM+ compared to IDC. The second graph also shows two lines: a teal line labeled "IDC(feat)" and a magenta line labeled "DREAM+". A dashed blue line indicates "40x faster" for DREAM+ compared to IDC(feat). The third graph shows a teal line labeled "DREAM" and a magenta line labeled "DREAM+". All graphs illustrate the accuracy of different models over iterations, with DREAM+ consistently showing higher accuracy than the other models in each respective comparison.

(a) The accuracy curve of adding DREAM+ to IDC(gradient).

(b) The accuracy curve of adding DREAM+ to IDC(feature).

(c) The accuracy curves of DREAM+ and DREAM.

Fig. 4: Applying the DREAM+ strategy brings stable performance and efficiency improvements.

<span id="page-5-0"></span>TABLE 2: Top-1 accuracy of test models trained on distilled synthetic images on TinyImageNet. The distillation training is conducted with ConvNet-4.

| IPC | Ratio % | DM [30]          | MTT [25]         | DREAM+                    | Whole            |
|-----|---------|------------------|------------------|---------------------------|------------------|
| 1   | 0.017   | $3.9_{\pm 0.2}$  | $8.8_{\pm 0.3}$  | $\textbf{10.5}_{\pm 0.4}$ |                  |
| 10  | 0.17    | $12.9_{\pm 0.4}$ | $23.2_{\pm 0.2}$ | $\textbf{24.0}_{\pm 0.4}$ |                  |
| 50  | 0.83    | $24.1_{\pm 0.3}$ | $28.0_{\pm 0.3}$ | $\textbf{29.5}_{\pm 0.3}$ | $37.6_{\pm 0.4}$ |

is set to 128. In the case of TinyImageNet, where the image resolution is  $64\times64$ , we utilize ConvNet-4. Our default baseline method is IDC [\[27\]](#page-12-23). The matching objective combines gradient matching and distribution matching. The matching metric D in Eq. [2](#page-3-1) is empirically defined as the mean squared error for CIFAR-10, CIFAR-100, TinyImageNet, and SVHN. For MNIST and FashionMNIST, we set  $D$  as the mean absolute error [\[27\]](#page-12-23). We perform a total of 1,200 matching iterations, with each iteration comprising 100 inner loops. We employ SGD as the optimizer, with a learning rate set to 0.005.

For clustering, we employ the distillation model for feature extraction. The clustering interval  $I_{int}$  is set as 10 iterations, whose sensitiveness is analyzed in Sec. [4.4.](#page-7-0) We also analyze the influence of different sampling strategy in Sec. [4.4.](#page-7-1) For evaluation, we train a network for 1,000 epochs on the distilled images with a learning rate of 0.01. We conduct 5 runs for each experiment and report the mean and standard deviation of the results.

### 4.2 Comparison with State-of-the-art Methods

We perform a comprehensive comparison of DREAM+ with stateof-the-art (SOTA) coreset-based and optimization-based methods across multiple datasets, each with varying images-per-class (IPC) settings, as summarized in Tab. [1.](#page-4-0) Additionally, for the TinyImageNet dataset, we specifically compare DREAM+ with DM [\[30\]](#page-12-26) and MTT [\[25\]](#page-12-21) as presented in Tab. [2.](#page-5-0) DREAM+ consistently demonstrates state-of-the-art (SOTA) results across most cases. The reduced performance gap between the small-scale distilled dataset and its original dataset means less information loss during the dataset distillation process. It illustrates the effectiveness of bidirectional matching of representative samples in our method. It is worth mentioning that RFAD [\[47\]](#page-12-43) employs a ConvNet with 1024 convolutional channels, while the results we report are based on a 128-channel ConvNet. DREAM+ outperforms RFAD in

<span id="page-5-2"></span>TABLE 3: Ablation study on the components of the proposed DREAM. RM indicates Representative Matching, Init stands for clustering-based initialization, and BM indicates Bidirectional Matching. "Iter" stands for the required iterations to achieve the baseline performance.

|            | Comp<br>RM Init BM | $Top-1$                                     | Iter |                 | RM Init BM | Comp | $Top-1$                 |
|------------|--------------------|---------------------------------------------|------|-----------------|------------|------|-------------------------|
|            |                    | $-67.5_{\pm 0.5}1000$                       |      |                 |            |      | 44.9 $_{\pm0.5}$        |
|            |                    | $-68.9_{\pm 0.5}$ 350                       |      | $\overline{DC}$ |            |      | $-45.9_{\pm 0.3}$       |
| <b>IDC</b> |                    | - $68.1_{\pm 0.3}$ 750                      |      |                 |            |      | $\sqrt{46.9}_{\pm 0.4}$ |
|            |                    | $\sqrt{68.7}_{\pm 0.3}$ 480                 |      |                 |            |      | $-52.1_{\pm 0.5}$       |
|            |                    | $-69.4_{\pm 0.4}$ 150                       |      | <b>DSA</b>      |            |      | $-53.1_{\pm 0.4}$       |
|            |                    | $\sqrt{}$ $\sqrt{}$ 69.9 <sub>±0.5</sub> 60 |      |                 |            |      | $\sqrt{53.5}$ ± 0.2     |

extracting better synthetic images except for IPC=1 on CIFAR-10. Meanwhile, HaBa [\[48\]](#page-12-44) incorporates a data hallucination process that generates additional samples from the base image. HaBa achieves superior performance with 1 IPC on CIFAR-100. However, in other cases, DREAM+ consistently gains superior performance compared to HaBa. These results together highlight the competitive performance of DREAM+ under different IPC settings across multiple datasets.

### 4.3 Efficiency comparison

We evaluate the efficiency of our proposed method on both gradient-based and feature-based IDC, as shown in Figure [4.](#page-5-1) Notably, our approach significantly reduces the number of iterations required for dataset distillation. Among them, for gradient matching, DREAM+ reduces the number of iterations by more than 15 times; for distribution matching, DREAM+ reduces the number of iterations by more than 40 times. As the training iterations increases, DREAM+ further boost the performance of the model. Additionally, the improved version of DREAM+ also demonstrates better efficiency compared with previous DREAM. This empirical evidence highlights the effectiveness of our bidirectional representative matching in improving the stability and efficiency of dataset distillation.

### 4.4 Ablation Study and Analysis

We conduct comprehensive experiments to evaluate the effectiveness of our proposed DREAM+ strategy. By default, the experiments are conducted at 10 IPC settings on CIFAR-10.

<span id="page-6-0"></span>TABLE 4: Ablation study on cross architecture distilled dataset performance of the proposed DREAM strategy. The dataset is first distilled on a model D and then validated on another model T.  $^{\dagger}$ denotes the accuracy is reproduced by us.

|                   | $\rm{D}\backslash\rm{T}$ | $Conv-3$           | $Res-10$           | Dense-121          |
|-------------------|--------------------------|--------------------|--------------------|--------------------|
| MTT [25]          | $Conv-3$                 | $64.3_{\pm 0.7}$   | $34.5_{\pm 0.6}$ † | $41.5_{\pm 0.5}$ † |
|                   | Res-10                   | $44.2_{\pm 0.3}$ † | $20.4_{\pm 0.9}$ † | $24.2_{\pm 1.3}$ † |
| <b>IDC</b> [27]   | $Conv-3$                 | $67.5_{\pm 0.5}$   | $63.5_{\pm 0.1}$   | $61.6_{\pm 0.6}$   |
|                   | Res-10                   | $53.6_{\pm 0.6}$ † | $50.6_{\pm 0.9}$ † | $51.7_{\pm 0.6}$ † |
| <b>DREAM</b> [36] | $Conv-3$                 | $69.4_{\pm 0.5}$   | $66.3_{\pm 0.8}$   | $65.9_{\pm 0.5}$   |
|                   | Res-10                   | $53.7_{\pm 0.6}$ † | $51.0_{\pm 0.9}$ † | $52.8_{\pm 0.6}$ † |
| DREAM+            | $Conv-3$                 | $69.9_{\pm 0.5}$   | $66.5_{\pm 0.8}$   | $66.0_{\pm 0.5}$   |
|                   | Res-10                   | $53.8_{\pm 0.6}$   | $51.2_{\pm 0.9}$   | $53.0_{\pm 0.6}$   |

<span id="page-6-1"></span>TABLE 5: Ablation study on different sampling strategy to form a mini-batch from sub-clusters.

| DREAM                          |   | Sub-cluster number $N$ |               |               |               |
|--------------------------------|---|------------------------|---------------|---------------|---------------|
|                                |   | 32                     | 64            | 128           | 256           |
| Samples per<br>sub-cluster $n$ | 1 | 67.2 $\pm0.3$          | 68.5 $\pm0.1$ | 69.4 $\pm0.4$ | 68.9 $\pm0.2$ |
|                                | 2 | 67.7 $\pm0.3$          | 68.6 $\pm0.3$ | 69.2 $\pm0.7$ | -             |
|                                | 4 | 67.7 $\pm0.4$          | 68.7 $\pm0.4$ | -             | -             |
|                                | 8 | 67.5 $\pm0.3$          | -             | -             | -             |
| DREAM+                         |   | Sub-cluster number $N$ |               |               |               |
|                                |   | 32                     | 64            | 128           | 256           |
| Samples per<br>sub-cluster $n$ | 1 | 67.4 $\pm0.3$          | 69.4 $\pm0.1$ | 69.9 $\pm0.4$ | 69.6 $\pm0.2$ |
|                                | 2 | 68.7 $\pm0.3$          | 69.8 $\pm0.3$ | 69.8 $\pm0.7$ | -             |
|                                | 4 | 68.8 $\pm0.4$          | 69.6 $\pm0.4$ | -             | -             |
|                                | 8 | 69.0 $\pm0.3$          | -             | -             | -             |

Component Combination Evaluation. We first perform an analysis on the components of the proposed DREAM+ strategy in Table [3.](#page-5-2) Representative matching and bidirectional matching greatly reduce the number of iterations required to reach baseline performance. Furthermore, clustering-based initialization shows a huge performance advantage before training starts, although its final impact is still relatively limited. However, when combined with bidirectional representational matching, it provides stable enhancement and accelerates the training convergence. By integrating all these components, the full DREAM+ approach proved highly effective, reducing the number of iterations required to achieve baseline performance by more than 15 times. These findings highlight the importance of representative matching and bidirectional matching components in improving the training efficiency and dataset distillation performance.

To further emphasize the effectiveness of bidirectional representative matching, we show the results in Figure [2.](#page-2-0) The figure visually demonstrates how our strategy affects the training efficiency and synthetic dataset performance. As shown in Figure [2a,](#page-2-0) we obtain significant improvements in both performance and efficiency by simply using samples sampled from sub-clusters as matching targets (previous DREAM strategy). The bidirectional representative matching further enhances the acceleration, achieving baseline performance in less than one-fifteenth of the original required number of training iterations. Furthermore, by increasing the number of training iterations, the mutually constrained matching objectives of gradient and feature matching enhance the

<span id="page-6-2"></span>Image /page/6/Figure/7 description: This image contains three histograms stacked vertically, each depicting the distribution of gradient norms (L2) for random samples and DREAM+ samples across different sub-cluster numbers. The top histogram, for N=32 sub-clusters, shows DREAM+ samples with a mean of 7.5 and a standard deviation of 6.1, while random samples have a mean of 11.2 and a standard deviation of 9.2. The middle histogram, for N=128 sub-clusters, indicates DREAM+ samples with a mean of 8.9 and a standard deviation of 7.9, and random samples with a mean of 11.4 and a standard deviation of 9.3. The bottom histogram, for N=256 sub-clusters, shows DREAM+ samples with a mean of 10.0 and a standard deviation of 8.9, and random samples with a mean of 11.3 and a standard deviation of 9.2. All histograms have 'Sample Number' on the y-axis and 'Gradient Norm (L2)' on the x-axis, ranging from 0 to 40. The legend in each plot distinguishes between blue bars for 'Random samples' and orange bars for 'DREAM+ samples'.

Fig. 5: The gradient distribution comparison between random sampling and our proposed DREAM strategy under different subcluster sample number N. Best viewed in color.

representation capabilities of synthetic data, leading to an overall improvement in the performance.

For the sample distribution, as shown in Figure [2b,](#page-2-0) we calculate the MMD to the real data distribution of images selected by our method and random sampling. The results consistently show that the former has lower MMD scores and less fluctuations. The reduction in fluctuations indicates that the sub-cluster centers effectively and stably cover the feature distribution, and thereby reduce the sample-level noise during training. With sufficient sample diversity, uniform distribution, and appropriate bidirectional supervision, DREAM+ ensures that the optimization process of dataset distillation training is smoother and more robust. To further illustrate the generality of DREAM+, we apply representative bidirectional matching and clustering-based initialization to several other baseline methods. The results, shown in Tab. [3,](#page-5-2) demonstrate similar improvements. It confirms that DREAM+ is suitable for a variety of dataset distillation frameworks and can significantly improve the training efficiency.

DREAM+ on Distribution Matching. In addition to gradient matching, we also explore the adaptability of our method to feature distribution matching. Random sampling not only introduces biased matching targets in gradient matching, it also has a similar impact on distribution matching. Specifically, random sampling tends to select samples around the center of feature distribution. It would reduce the feature diversity and training efficiency.

We conduct the experiments based on IDC [\[27\]](#page-12-23) under the setting of 10 images of each class on the CIFAR-10. The original IDC method performs significantly worse than gradient matching, which is consistent with the conclusion drawn by the previous work [\[27\]](#page-12-23). DREAM+ substantially improves in the performance and requires only about one-fortieth of iteration number to reach the baseline performance, as shown in Figure [4b.](#page-5-1)

Cross Architecture Generalization Analysis. A recurring challenge in dataset distillation methods is their inability to generalize effectively across different architectures. This limitation

<span id="page-7-1"></span>Image /page/7/Figure/1 description: This image contains four plots labeled (a), (b), (c), and (d). Plot (a) shows "Logarithmic Feature Migration" on the y-axis and "Iteration" on the x-axis, with three lines representing "DREAM+" (red), "DREAM" (yellow), and "Random" (green). Plot (b) shows "Gradient Difference" on the y-axis and "Iteration" on the x-axis, with three lines representing "IDC" (green), "DREAM" (yellow), and "DREAM+" (red). Plot (c) shows "Feature Difference" on the y-axis and "Iteration" on the x-axis, with three lines representing "IDC" (green), "DREAM" (yellow), and "DREAM+" (red). Plot (d) shows "MMD" on the y-axis and "Iteration" on the x-axis, with two lines representing "Gradient Matching" (teal) and "Bidirectional Matching" (purple).

Fig. 6: (a): The feature migration during the training process. (b): Curve of MMD variation between synthetic and original images. (c): The gradient difference curve during the training process. (d): The feature difference curve during the training process.

is due to the fact that synthetic images tend to be over-fitted to the specific architecture used for matching [\[16\]](#page-12-12), [\[27\]](#page-12-23). To evaluate the cross-architecture performance of our proposed DREAM+ strategy, we conducted the experiments in Table [4.](#page-6-0) The compact datasets are distilled with ConvNet-3 and ResNet-10 [\[1\]](#page-11-0), and then evaluated on ConvNet-3, ResNet-10, and DenseNet-121 [\[75\]](#page-13-14).

It is worth noting that DREAM+ not only outperforms other methods on specific distillation models, but also achieves significant performance improvements on other unseen network architectures. This strong cross-architecture generalization highlights that DREAM+ builds representations of datasets with clearer decision boundaries than random sampling. Synthetic data that has closer overall distribution to the original data helps the model learn more general features and knowledge.

Sampling Strategy Analysis. We delve into the impact of different sampling strategies on the training results, as performed in Table [5](#page-6-1) and Figure [5.](#page-6-2) Our representative matching approach entails clustering for each class and subsequently sampling original images from sub-clusters to form mini-batches. By varying the sub-cluster number and selected sample number per sub-cluster, we generate original image mini-batches that differ in scale and diversity. The ablation study provides better interpretability for the effectiveness of our approach.

In general, the performance of the dataset significantly benefits from representative matching compared to the baseline (67.5). However, specific nuances become apparent upon closer examination. For instance, with a small sub-cluster number (e.g.,  $N = 32$ ), the sub-cluster centers tend to be concentrated in regions with smaller gradients, as depicted in the first row of Figure [5.](#page-6-2) As the random model  $\mathcal{M}_{\theta}$  undergoes training, these samples gradually lose their ability to provide effective gradient-based supervision, ultimately resulting in sub-optimal performance. Conversely, a larger sub-cluster number (e.g.,  $N = 256$ ) leads to a distribution that closely resembles random sampling, and causes a minor performance drop. Due to memory constraints, further increasing  $N$ is unfeasible, but it is reasonable to assume that extreme conditions would yield results similar to those of random sampling.

On the other hand, variations in the sample number per subcluster  $(n)$  appear to exert only a marginal influence on results. The configuration involving one center sample per sub-cluster and a total of 128 sub-clusters provides optimal gradient-based supervision, as evidenced by the second row of Figure [5.](#page-6-2) Consequently, this configuration is selected for mini-batch composition. In addition, under different sampling strategies, the DREAM+

<span id="page-7-0"></span>Image /page/7/Figure/8 description: The image contains two plots, labeled (a) and (b). Plot (a) is a line graph showing Top-1 and Top-5 accuracy percentages against clustering intervals. The x-axis represents the clustering interval with values 1, 2, 4, 10, 20, 25, and 50. The left y-axis shows Top-1 Accuracy (%) ranging from 60 to 72, with a green line representing Top-1 accuracy. The right y-axis shows Top-5 Accuracy (%) ranging from 93 to 100, with a blue line representing Top-5 accuracy. Plot (b) is a line graph showing accuracy against iteration. The x-axis represents the iteration with values 0, 400, 800, 1200, 1600, and 2000. The y-axis represents accuracy ranging from 65.0 to 70.5. Multiple colored lines represent different intervals: Interval=1 (teal), Interval=2 (green), Interval=4 (light green), Interval=10 (yellow), Interval=20 (orange), Interval=25 (red), and Interval=50 (purple).

Fig. 7: Ablation study on different clustering interval. As the clustering interval increases, the efficiency of distillation gradually decreases.

strategy has improved to a certain extent compared with DREAM, which also verifies the effectiveness of the new method.

Training Stability Analysis. In order to describe the impact of DREAM+ on the training process more intuitively, we visualize the feature migration during the distillation process of DREAM+, DREAM and random sampling. We save the distilled images at intervals of 20 iterations and use a well-trained network to extract the features. The Euclidean distance between consecutive versions of the image is calculated and summarized in Figure [6a.](#page-7-1)

For both DREAM+ and DREAM, the synthetic images go through a large feature migration in the early stages of training, which fully demonstrates that representative matching accelerates the optimization process of synthetic images. When the number of iterations is slightly increased, the migration of methods based on representative matching already turns small and stable. It illustrates that representative sampling efficiently optimizes the images to a relatively optimal position, and makes subsequent fine-tuning. For synthetic images that match randomly sampled original images, the feature migration remains relatively high. This phenomenon is partly attributed to the uneven mini-batch of noisy matching targets, where the optimization is biased by the largegradient samples inside a mini-batch, hindering a stable optimization process. Besides, compared with DREAM, DREAM+ shows further improvement in providing a stable overall feature migration, which indicates that distribution matching also effectively constrains the optimization process.

# <span id="page-8-0"></span>IEEE TRANSACTIONS ON PATTERN ANALYSIS AND MACHINE INTELLIGENCE 9

TABLE 6: Time cost of adding DREAM strategy (s).

| Datasets  | Methods            | Clustering | Update<br>Images | Inner<br>Loop |
|-----------|--------------------|------------|------------------|---------------|
| CIFAR-10  | IDC [27]<br>DREAM+ | -<br>0.1   | 0.2<br>0.2       | 0.2<br>0.3    |
| CIFAR-100 | IDC [27]<br>DREAM+ | -<br>0.1   | 2.0<br>2.0       | 2.0<br>2.1    |

Clustering Interval Sensitivity Analysis. We further analyze the sensitivity of the clustering interval  $I_{int}$ . Our findings are shown in Figure [7a.](#page-7-0) We observe that in representative bidirectional matching, different clustering intervals have little impact on top-1 accuracy and top-5 accuracy. We also visualize the iterationaccuracy curve under different clustering interval settings in [7b.](#page-7-0) It can be found that when the clustering interval gradually increases, the efficiency of distillation gradually decreases, but overall there is no significant impact on performance. Based on these observations, we choose a clustering interval of 10. This choice achieves a balance between performance on synthetic datasets and the additional computational time introduced by clustering.

Clustering Analysis. To provide a comprehensive perspective on the computational impact of the clustering process, we present the extra time costs incurred in Table [6.](#page-8-0) For CIFAR-10, each inner loop involves both the matching process and image updating, which collectively consume approximately 0.2 seconds. Every ten inner loops, we introduce a clustering process, which requires an additional 1 second. By average, this translates to a clustering time of 0.1 seconds per inner loop. Consequently, the total average duration of an inner loop becomes 0.3 seconds, compared to the original 0.2 seconds. Compared with DREAM, since the features used in the newly introduced distribution alignment of DREAM+ come from the features that have been calculated in the forward pass in gradient matching, the newly introduced time overhead is very small and can be ignored. Remarkably, considering that we achieve the same level of performance with only one-twentieth to one-tenth of the iterations, this implementation of DREAM+ allows us to save over 85% of the time. For CIFAR-100, which involves a more extensive set of classes, the extra clustering time accounts for a mere twentieth of the original image updating time, which is negligible. In essence, DREAM+ contributes significantly to enhanced the training efficiency, and substantially reducing the required training time for dataset distillation.

### 4.5 Visualizations

Gradient Difference Curve. Given that dataset distillation training depends on the gradient matching to some extent, and the smaller the gradient difference indicates the more effective the matching, we show the gradient difference curve of the dataset distillation process in Figure [6b.](#page-7-1) The gradient difference is calculated based on the training loss, as defined in the Eq. [2.](#page-3-1) We compared the DREAM+ curve with IDC and DREAM. Across the entire training trajectory, DREAM+ exhibits smaller gradient differences compared to baseline methods. This observation serves a dual purpose. First, it confirms the efficacy of DREAM+ in improving training efficiency, successfully reducing the gradient difference within a limited number of iterations. Second, the large fluctuations seen in the baseline method confirm the existence of noise gradients generated by random sampling.

<span id="page-8-1"></span>Image /page/8/Figure/7 description: The image displays two scatter plots side-by-side, each containing numerous small gray dots representing data points. Both plots are overlaid with red star markers. The left plot is labeled "Ours" and the right plot is labeled "Random". The "Ours" plot shows 10 red stars distributed across the scatter plot, with a few clustered together and others more spread out. The "Random" plot also shows 10 red stars, but their distribution appears more random and less clustered than in the "Ours" plot.

Fig. 8: The sample distribution comparison on the final distilled images (marked as red stars) between our proposed DREAM+ (left) and random sampling (right).

<span id="page-8-2"></span>Image /page/8/Figure/9 description: The image displays two line graphs side-by-side, labeled (a) and (b). Both graphs are titled "Accuracy-Iteration" and plot "Accuracy" on the y-axis against "Iteration" on the x-axis. Graph (a) shows two lines: a magenta line labeled "DC+DREAM+" and a teal line labeled "DC". The magenta line starts at approximately 0.35 accuracy at 0 iterations, rises sharply to about 0.45 by 100 iterations, and then fluctuates between 0.45 and 0.50, ending around 0.48. The teal line starts at approximately 0.27 accuracy at 0 iterations, rises to about 0.42 by 100 iterations, and then fluctuates between 0.42 and 0.45, ending around 0.45. Graph (b) also shows two lines: a magenta line labeled "DSA+DREAM+" and a teal line labeled "DSA". The magenta line starts at approximately 0.27 accuracy at 0 iterations, rises sharply to about 0.50 by 100 iterations, and then fluctuates between 0.50 and 0.55, ending around 0.54. The teal line starts at approximately 0.27 accuracy at 0 iterations, rises to about 0.45 by 100 iterations, and then fluctuates between 0.45 and 0.50, ending around 0.51. Both graphs have dashed orange lines indicating a specific iteration point and corresponding accuracy level.

Fig. 9: Applying the DREAM+ strategy brings stable performance and efficiency improvements for (a) DC and (b) DSA.

Feature Difference Curve. Feature distribution matching is another critical aspect in the training process of dataset distillation. The smaller the feature distribution difference means that the synthetic data more accurately approximates the feature distribution of the original data. This in turn helps the model acquire more general features and knowledge. In Figure [6c](#page-7-1) and Figure [6d](#page-7-1) we provide a visualization of the feature difference during the training and the MMD between the feature distributions of the synthetic and original data throughout the distillation process. Compared with pure gradient matching, the introduction of bidirectional matching leads to better feature alignment, especially in the early stages of training. The bidirectional optimization strategy effectively enhances the stability and efficiency of dataset distillation and alleviates potential problems related to feature transformation.

Sample Distribution Visualization. To more intuitively illustrate the efficacy of our DREAM+ strategy in mimicing the original sample distribution, we employed t-SNE [\[76\]](#page-13-15) visualization for both random sampling and DREAM+. Referring to Figure [8,](#page-8-1) the t-SNE plot clearly illustrates the difference between the two methods. The results from DREAM+ present a final distribution that evenly spans the entire category range. In contrast, random sampling can lead to significant bias in optimization results. Furthermore, random sampling results show that most samples are drawn to the edge of the distribution. This observation highlights the bias introduced by boundary samples with larger gradients during the matching process. By continuously providing appropriate gradient supervision and distribution supervision, DREAM+ achieves more diverse and resilient distillation results.

Appliance on More Methods. DREAM+ is suitable for a variety of mainstream dataset distillation methods, including DC [\[16\]](#page-12-12), DSA [\[35\]](#page-12-31), etc. We provide the training accuracy curve in Figure [9.](#page-8-2) Examining these curves carefully, we see that DREAM+ requires

<span id="page-9-0"></span>Image /page/9/Picture/1 description: The image displays three rows of small, pixelated images, each containing a car. The cars are rendered in a variety of colors and angles, with some appearing more detailed than others. The overall impression is a grid of generated car images, likely from a machine learning model.

Fig. 10: Comparison of distilled datasets on CIFAR-10 (plane, car, dog, cat classes) for DC (top row), DC with DREAM strategy (middle row), and DC with DREAM+ strategy (bottom row). On the basis of DREAM introducing more obvious categorical characteristics and diversity, DREAM+ further adds diverse features to the synthesized images. Best viewed in color.

only a fraction of the iterations to achieve the same performance compared to the original method. Specifically, in the case of DC and DSA, one-tenth of the number of iterations is sufficient to reach the original performance benchmark. As training iterations increase, DREAM+ continues to boost the performance. All the above experiments are performed on CIFAR-10 with 10 IPC.

Synthetic Image Visualization. In order to more intuitively understand the impact of DREAM+ on distilled images, we visually compare the distillation results of DREAM+, DREAM and the baseline in Figure [10.](#page-9-0) First, images optimized with DREAM+ and DREAM exhibit more distinct and obvious categorical characteristics, making them visually clear and easily identifiable. Second, DREAM+ and DREAM also introduce greater diversity to distilled images, resulting in broader representation of the synthetic dataset. In addition, based on DREAM, DREAM+ introduces more diverse feature representations. Clearer categorical characteristics, feature complexity, and higher image diversity work together to improve performance of synthetic datasets.

# 4.6 Application on Continual Learning

Dataset distillation is promising to apply in the continual learning [\[27\]](#page-12-23), [\[39\]](#page-12-35), [\[40\]](#page-12-36), [\[41\]](#page-12-37). In Figure [11,](#page-9-1) we evaluate the effectiveness of our proposed DREAM+ strategy in the continual learning

<span id="page-9-1"></span>Image /page/9/Figure/7 description: This line graph displays the test accuracy of four different methods (DREAM+, DREAM, IDC, and Herding) against the number of classes. The x-axis represents the number of classes, ranging from 20 to 100, with increments of 20. The y-axis represents the test accuracy, ranging from 30 to 75. The DREAM+ line, colored magenta, starts at approximately 68% accuracy at 20 classes and decreases to about 54% at 100 classes. The DREAM line, colored orange, starts at approximately 66% accuracy at 20 classes and decreases to about 52% at 100 classes. The IDC line, colored teal, starts at approximately 63% accuracy at 20 classes and decreases to about 46% at 100 classes. The Herding line, colored purple, starts at approximately 46% accuracy at 20 classes and decreases to about 32% at 100 classes. All lines show a downward trend as the number of classes increases.

Fig. 11: The continual learning accuracy curve.

scenario. Following the experimental settings of [\[16\]](#page-12-12), [\[27\]](#page-12-23), we conduct a 5-step class incremental experiment on CIFAR-100, in which 20 new classes were introduced at each step. We perform distillation synthesis on ConvNet-3 and verified it on ResNet-10. Throughout the training process, DREAM+ always maintains its performance advantage over other methods. Furthermore, the performance gap widens as the number of learning categories gradually increases. These results highlight the concept that improving the quality of distillation helps build clearer decision boundaries within the model, thereby better preserving discriminative information.

# 5 CONCLUSION

In this paper, we introduce a novel dataset distillation method named Dataset Distillation by Bidirectional Representative Matching (DREAM+). Our goal is to solve the training efficiency problem in dataset distillation. By sampling a representative set of original images for bidirectional matching, DREAM+ further mitigates the instability of optimization, resulting in a more stable and robust training process. DREAM+ can be widely applied to existing dataset distillation frameworks, and significantly reduces the number of training iterations by more than 15 times without performance drop. This enhanced optimization stability contributes to superior final performance and improved generalization capabilities. Furthermore, the improved efficiency of bidirectional matching opens the door to exploring more complex matching metrics in the future.

# 6 LIMITATIONS AND FUTURE WORKS

Although our proposed DREAM+ strategy greatly improves the training efficiency of optimization-based dataset distillation methods, it is worth noting that the computational requirements are still large, especially when dealing with larger image sizes and more classes. Even with the efficiency enhancements introduced by DREAM+, these techniques may still encounter difficulties when processing very large datasets such as ImageNet [\[72\]](#page-13-11). Furthermore, scaling up matching-based methods to accommodate more images per class may pose challenges. Future research could focus on developing more computationally efficient distance measures or integrated core set methods to expand the number of images per class in image dataset distillation. These advancements could further enhance the scalability and practicality of dataset distillation for extensive and diverse image datasets.

<span id="page-10-0"></span>

| $-25$<br>256<br>â<br>춞<br>÷<br>Œ<br>-n. | $\rightarrow$<br>£.<br>÷<br><b>ACT</b><br>$\sim$<br>- 13 | O)<br>$\overline{\mathcal{C}}$<br><b>ARK</b><br>老<br>$\sim$ 0<br>-12 |
|-----------------------------------------|----------------------------------------------------------|----------------------------------------------------------------------|
| À<br>☞<br>ø                             | ø                                                        |                                                                      |
| 学                                       | 4                                                        |                                                                      |
| $\vec{r}$                               | лÍ                                                       |                                                                      |
|                                         |                                                          |                                                                      |
| <b>LA</b><br><b>Section</b>             |                                                          |                                                                      |
| xX.<br>Lf<br>WA                         | ġ,                                                       |                                                                      |
| j.<br>20H<br>برتوب                      | Ä.<br>$\bullet$                                          | 4                                                                    |
| $4 -$<br>-39                            | $\blacktriangleleft$<br>H                                |                                                                      |
|                                         |                                                          |                                                                      |
| (a) iteration=0                         | (b) iteration=200                                        | (c) iteration=400                                                    |
|                                         |                                                          |                                                                      |
|                                         |                                                          | ਵ                                                                    |
| <b>AGE</b><br>m.<br>Ξ.                  | 63<br>震<br>СV.<br>- 4                                    | روي<br>SE.<br>$\overline{\mathcal{L}}$<br>œ                          |
| <b>TI</b>                               | Ť                                                        |                                                                      |
|                                         |                                                          |                                                                      |
|                                         |                                                          |                                                                      |
|                                         | ы                                                        |                                                                      |
|                                         | an d                                                     |                                                                      |
|                                         |                                                          |                                                                      |
|                                         |                                                          |                                                                      |
| ತ<br>еŅ                                 | ä.<br>52<br>Đ                                            | á.                                                                   |
|                                         |                                                          |                                                                      |

Fig. 12: Visualization of synthetic images at different training stages on CIFAR-10.

<span id="page-10-1"></span>

|           | fa.             |                |
|-----------|-----------------|----------------|
|           | š               |                |
|           |                 |                |
|           | ×               |                |
|           |                 |                |
|           |                 |                |
| 60        | u.              |                |
|           | دنكاء<br>σ      |                |
|           |                 | W.             |
| $(a)$ DC  | (b) DC+DREAM    | (c) DC+DREAM+  |
|           |                 |                |
|           |                 |                |
|           | z.              |                |
|           | ž<br><b>VEN</b> |                |
|           |                 |                |
|           |                 |                |
|           |                 |                |
|           | ×               |                |
|           | Ġ,              |                |
|           | æ               | ш              |
| $(d)$ DSA | (e) DSA+DREAM   | (f) DSA+DREAM+ |

Fig. 13: Applying DREAM enhances sample diversity, while DREAM+ further improves image quality through feature alignment.

<span id="page-11-3"></span>Image /page/11/Figure/1 description: The image displays a grid of six subfigures, each representing a different dataset. Subfigure (a) labeled "MNIST" shows rows of handwritten digits from 0 to 9. Subfigure (b) labeled "FashionMNIST" displays various clothing items and accessories. Subfigure (c) labeled "SVHN" presents images of house numbers. Subfigure (d) labeled "CIFAR-10" contains a grid of diverse objects and animals. Subfigure (e) labeled "CIFAR-100" shows a similar grid of objects and animals, likely with more categories. Subfigure (f) labeled "TinyImageNet" displays a collection of images, also featuring animals and objects, potentially at a higher resolution or variety than CIFAR datasets.

Fig. 14: Example visualizations of the distilled images on MNIST, FashionMNIST, SVHN, CIFAR-10, CIFAR-100 and TinyImageNet.

# APPENDIX MORE VISUALIZATION RESULTS

Visualization of Synthetic Image Variations. We performed a visual exploration of synthetic images evolving throughout the dataset distillation process, as shown in Figure [12.](#page-10-0) This visual representation provides insight into the transformations the synthetic data undergoes during various training iterations. By observing changes in appearance, diversity, and alignment of these images, we can efficiently track convergence and evaluate the effectiveness of our proposed DREAM+ strategy. These visualizations not only provide a tangible sense of how the synthesis evolves, but also validate the stability and consistency of the bidirectional matching. Furthermore, they are strong evidence of DREAM+'s enhanced ability to generate high-quality synthetic data that faithfully captures the characteristics of the original dataset.

Visualization of distillation dataset. In order to more intuitively describe the impact on distilled images, we compared the dataset distillation results with and without using the DREAM+ strategy and DREAM, as shown in Figure [13.](#page-10-1) DREAM+ enhances the distillation dataset from two different perspectives. First, thanks to the newly introduced feature distribution matching, images optimized by DREAM+ show more obvious classification characteristics. Second, DREAM+ introduces more diversity to distilled images. This diversification helps provide a richer representation in the dataset, which in turn improves the performance

of distilled datasets.

We provide additional visualizations in Fig. [14.](#page-11-3) Covering MNIST, FashionMNIST, SVHN, CIFAR-10, CIFAR-100, and TinyImageNet, these visualizations reiterate the advantages of DREAM+ in various dataset distillation scenarios.

# ACKNOWLEDGMENTS

This research is supported by the National Research Foundation, Singapore under its AI Singapore Programme (AISG Award No: AISG2-PhD-2021-08- 008). Yang You's research group is being sponsored by NUS startup grant (Presidential Young Professorship), Singapore MOE Tier-1 grant, ByteDance grant, ARCTIC grant, SMI grant and Alibaba grant. The research is also supported by the National Natural Science Foundation of China (No. 62173302).

### REFERENCES

- <span id="page-11-0"></span>[1] K. He, X. Zhang, S. Ren, and J. Sun, "Deep residual learning for image recognition," in *CVPR*, 2016, pp. 770–778.
- <span id="page-11-1"></span>[2] A. Dosovitskiy, L. Beyer, A. Kolesnikov, D. Weissenborn, X. Zhai, T. Unterthiner, M. Dehghani, M. Minderer, G. Heigold, S. Gelly *et al.*, "An image is worth 16x16 words: Transformers for image recognition at scale," *arXiv preprint arXiv:2010.11929*, 2020.
- <span id="page-11-2"></span>[3] J. Redmon, S. Divvala, R. Girshick, and A. Farhadi, "You only look once: Unified, real-time object detection," in *CVPR*, 2016, pp. 779–788.

- <span id="page-12-0"></span>[4] Z. Liu, Y. Lin, Y. Cao, H. Hu, Y. Wei, Z. Zhang, S. Lin, and B. Guo, "Swin transformer: Hierarchical vision transformer using shifted windows," in *ICCV*, 2021, pp. 10 012–10 022.
- <span id="page-12-1"></span>[5] Y.-H. Tsai, W.-C. Hung, S. Schulter, K. Sohn, M.-H. Yang, and M. Chandraker, "Learning to adapt structured output space for semantic segmentation," in *CVPR*, 2018, pp. 7472–7481.
- <span id="page-12-2"></span>[6] I. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. Warde-Farley, S. Ozair, A. Courville, and Y. Bengio, "Generative adversarial networks," *Communications of the ACM*, vol. 63, no. 11, pp. 139–144, 2020.
- <span id="page-12-3"></span>M. Danelljan, G. Bhat, F. Shahbaz Khan, and M. Felsberg, "Eco: Efficient convolution operators for tracking," in *CVPR*, 2017, pp. 6638–6646.
- <span id="page-12-4"></span>[8] Z. Zheng, M. Ma, K. Wang, Z. Qin, X. Yue, and Y. You, "Preventing zero-shot transfer degradation in continual learning of vision-language models," *arXiv preprint arXiv:2303.06628*, 2023.
- <span id="page-12-5"></span>[9] W. Chiang, Z. Li, Z. Lin, Y. Sheng, Z. Wu, H. Zhang, L. Zheng, S. Zhuang, Y. Zhuang, J. Gonzalez *et al.*, "Vicuna: An open-source chatbot impressing gpt-4 with 90%\* chatgpt quality, mar. 2023."
- <span id="page-12-6"></span>[10] H. Touvron, T. Lavril, G. Izacard, X. Martinet, M.-A. Lachaux, T. Lacroix, B. Rozière, N. Goyal, E. Hambro, F. Azhar et al., "Llama: Open and efficient foundation language models," *arXiv preprint arXiv:2302.13971*, 2023.
- <span id="page-12-7"></span>[11] D. Zhu, J. Chen, X. Shen, X. Li, and M. Elhoseiny, "Minigpt-4: Enhancing vision-language understanding with advanced large language models," *arXiv preprint arXiv:2304.10592*, 2023.
- <span id="page-12-8"></span>[12] W. Hu, Y. Xu, Y. Li, W. Li, Z. Chen, and Z. Tu, "Bliva: A simple multimodal llm for better handling of text-rich visual questions," *arXiv preprint arXiv:2308.09936*, 2023.
- <span id="page-12-9"></span>[13] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-12-10"></span>[14] M. Paul, S. Ganguli, and G. K. Dziugaite, "Deep learning on a data diet: Finding important examples early in training," *NeurIPS*, vol. 34, pp. 20 596–20 607, 2021.
- <span id="page-12-11"></span>[15] M. Toneva, A. Sordoni, R. T. d. Combes, A. Trischler, Y. Bengio, and G. J. Gordon, "An empirical study of example forgetting during deep neural network learning," *arXiv preprint arXiv:1812.05159*, 2018.
- <span id="page-12-12"></span>[16] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," in *ICLR*, 2020.
- <span id="page-12-13"></span>[17] K. He, X. Chen, S. Xie, Y. Li, P. Dollár, and R. Girshick, "Masked autoencoders are scalable vision learners," in *CVPR*, 2022, pp. 16 000– 16 009.
- <span id="page-12-14"></span>[18] B. Sorscher, R. Geirhos, S. Shekhar, S. Ganguli, and A. Morcos, "Beyond neural scaling laws: beating power law scaling via data pruning," *NeurIPS*, vol. 35, pp. 19 523–19 536, 2022.
- <span id="page-12-15"></span>[19] Z. Qin, K. Wang, Z. Zheng, J. Gu, X. Peng, D. Zhou, and Y. You, "Infobatch: Lossless training speed up by unbiased dynamic data pruning," *arXiv preprint arXiv:2303.04947*, 2023.
- <span id="page-12-16"></span>[20] Z. Daquan, K. Wang, J. Gu, D. Lian, X. Peng, Y. Zhang, Y. You, and J. Feng, "Lossless dataset compression via dataset quantization," 2022.
- <span id="page-12-17"></span>[21] A. Lapedriza, H. Pirsiavash, Z. Bylinskii, and A. Torralba, "Are all training examples equally valuable?" *arXiv preprint arXiv:1311.6510*, 2013.
- <span id="page-12-18"></span>[22] R. Iyer, N. Khargoankar, J. Bilmes, and H. Asanani, "Submodular combinatorial information measures with applications in machine learning." in *Algorithmic Learning Theory*. PMLR, 2021, pp. 722–754.
- <span id="page-12-19"></span>[23] K. Killamsetty, D. Sivasubramanian, G. Ramakrishnan, and R. Iyer, "Glister: Generalization based data subset selection for efficient and robust learning," in *Proceedings of the AAAI Conference on Artificial Intelligence*, vol. 35, no. 9, 2021, pp. 8110–8118.
- <span id="page-12-20"></span>[24] K. Killamsetty, S. Durga, G. Ramakrishnan, A. De, and R. Iyer, "Gradmatch: Gradient matching based data subset selection for efficient deep model training," in *ICML*. PMLR, 2021, pp. 5464–5474.
- <span id="page-12-21"></span>[25] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *CVPR*, 2022, pp. 4750–4759.
- <span id="page-12-22"></span>[26] K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You, "Cafe: Learning to condense dataset by aligning features," in *CVPR*, 2022, pp. 12 196–12 205.
- <span id="page-12-23"></span>[27] J.-H. Kim, J. Kim, S. J. Oh, S. Yun, H. Song, J. Jeong, J.-W. Ha, and H. O. Song, "Dataset condensation via efficient synthetic-data parameterization," *arXiv preprint arXiv:2205.14959*, 2022.
- <span id="page-12-24"></span>[28] J. Cui, R. Wang, S. Si, and C.-J. Hsieh, "Dc-bench: Dataset condensation benchmark," in *NeurIPS*, 2022.
- <span id="page-12-25"></span>[29] J. Du, Y. Jiang, V. Y. F. Tan, J. T. Zhou, and H. Li, "Minimizing the accumulated trajectory error to improve dataset distillation," in *CVPR*, 2023, pp. 3749–3758.
- <span id="page-12-26"></span>[30] B. Zhao and H. Bilen, "Dataset condensation with distribution matching," *arXiv preprint arXiv:2110.04181*, 2021.

- <span id="page-12-27"></span>[31] J. Geng, Z. Chen, Y. Wang, H. Woisetschlaeger, S. Schimmler, R. Mayer, Z. Zhao, and C. Rong, "A survey on dataset distillation: Approaches, applications and future directions," *arXiv preprint arXiv:2305.01975*, 2023.
- <span id="page-12-28"></span>[32] N. Sachdeva and J. McAuley, "Data distillation: A survey," *arXiv preprint arXiv:2301.04272*, 2023.
- <span id="page-12-29"></span>[33] S. Lei and D. Tao, "A comprehensive survey to dataset distillation," arXiv *preprint arXiv:2301.05603*, 2023.
- <span id="page-12-30"></span>[34] R. Yu, S. Liu, and X. Wang, "Dataset distillation: A comprehensive review," *arXiv preprint arXiv:2301.07014*, 2023.
- <span id="page-12-31"></span>[35] B. Zhao and H. Bilen, "Dataset condensation with differentiable siamese augmentation," in *ICML*. PMLR, 2021, pp. 12 674–12 685.
- <span id="page-12-32"></span>[36] Y. Liu, J. Gu, K. Wang, Z. Zhu, W. Jiang, and Y. You, "Dream: Efficient dataset distillation by representative matching," *arXiv preprint arXiv:2302.14416*, 2023.
- <span id="page-12-33"></span>[37] C. Guo, B. Zhao, and Y. Bai, "Deepcore: A comprehensive library for coreset selection in deep learning," *arXiv preprint arXiv:2204.08499*, 2022.
- <span id="page-12-34"></span>[38] C. Coleman, C. Yeh, S. Mussmann, B. Mirzasoleiman, P. Bailis, P. Liang, J. Leskovec, and M. Zaharia, "Selection via proxy: Efficient data selection for deep learning," *arXiv preprint arXiv:1906.11829*, 2019.
- <span id="page-12-35"></span>[39] S.-A. Rebuffi, A. Kolesnikov, G. Sperl, and C. H. Lampert, "icarl: Incremental classifier and representation learning," in *CVPR*, 2017, pp. 2001–2010.
- <span id="page-12-36"></span>[40] R. Aljundi, M. Lin, B. Goujaud, and Y. Bengio, "Gradient based sample selection for online continual learning," in *NeurIPS*, 2019, pp. 11 817– 11 826.
- <span id="page-12-37"></span>[41] F. Wiewel and B. Yang, "Condensed composite memory continual learning," in *IJCNN*. IEEE, 2021, pp. 1–8.
- <span id="page-12-38"></span>[42] O. Sener and S. Savarese, "Active learning for convolutional neural networks: A core-set approach," *arXiv preprint arXiv:1708.00489*, 2017.
- <span id="page-12-39"></span>[43] S. Shleifer and E. Prokop, "Using small proxy datasets to accelerate hyperparameter search," *arXiv preprint arXiv:1906.04887*, 2019.
- <span id="page-12-40"></span>[44] Z. Jiang, J. Gu, M. Liu, and D. Z. Pan, "Delving into effective gradient matching for dataset condensation," *arXiv preprint arXiv:2208.00311*, 2022.
- <span id="page-12-41"></span>[45] G. Zhao, G. Li, Y. Qin, and Y. Yu, "Improved distribution matching for dataset condensation," in *CVPR*, 2023, pp. 7856–7865.
- <span id="page-12-42"></span>[46] T. Nguyen, R. Novak, L. Xiao, and J. Lee, "Dataset distillation with infinitely wide convolutional networks," *NeurIPS*, vol. 34, pp. 5186– 5198, 2021.
- <span id="page-12-43"></span>[47] N. Loo, R. Hasani, A. Amini, and D. Rus, "Efficient dataset distillation using random feature approximation," in *NeurIPS*, 2022.
- <span id="page-12-44"></span>[48] S. Liu, K. Wang, X. Yang, J. Ye, and X. Wang, "Dataset distillation via factorization," in *NeurIPS*, 2022.
- <span id="page-12-45"></span>[49] Y. Zhou, E. Nezhadarya, and J. Ba, "Dataset distillation using neural feature regression," in *NeurIPS*, 2022.
- <span id="page-12-46"></span>[50] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Generalizing dataset distillation via deep generative prior," in *CVPR*, 2023, pp. 3739–3748.
- <span id="page-12-47"></span>[51] B. Zhao and H. Bilen, "Synthesizing informative training samples with gan," *arXiv preprint arXiv:2204.07513*, 2022.
- <span id="page-12-48"></span>[52] K. Wang, J. Gu, D. Zhou, Z. Zhu, W. Jiang, and Y. You, "Dim: Distilling dataset into generative model," *arXiv preprint arXiv:2303.04707*, 2023.
- <span id="page-12-49"></span>[53] H. Rehioui, A. Idrissi, M. Abourezq, and F. Zegrari, "Denclue-im: A new approach for big data clustering," *Procedia Computer Science*, vol. 83, pp. 560–567, 2016.
- <span id="page-12-50"></span>[54] E. W. Forgy, "Cluster analysis of multivariate data: efficiency versus interpretability of classifications," *Biometrics*, vol. 21, pp. 768–769, 1965.
- <span id="page-12-51"></span>[55] D. Arthur and S. Vassilvitskii, "k-means++: The advantages of careful seeding," Stanford, Tech. Rep., 2006.
- <span id="page-12-52"></span>[56] G. Hamerly and C. Elkan, "Alternatives to the k-means algorithm that find better clusterings," in *CIKM*, 2002, pp. 600–607.
- <span id="page-12-53"></span>[57] M. Ester, H.-P. Kriegel, J. Sander, X. Xu *et al.*, "A density-based algorithm for discovering clusters in large spatial databases with noise." in *KDD*, 1996, pp. 226–231.
- <span id="page-12-54"></span>[58] C. Ding and X. He, "Cluster merging and splitting in hierarchical clustering algorithms," in *ICDM.* IEEE, 2002, pp. 139–146.
- <span id="page-12-55"></span>[59] J. Lorraine, P. Vicol, and D. Duvenaud, "Optimizing millions of hyperparameters by implicit differentiation," in *International Conference on Artificial Intelligence and Statistics*. PMLR, 2020, pp. 1540–1552.
- <span id="page-12-56"></span>[60] P. Vicol, J. P. Lorraine, F. Pedregosa, D. Duvenaud, and R. B. Grosse, "On implicit bias in overparameterized bilevel optimization," in *ICML*. PMLR, 2022, pp. 22 234–22 259.

- <span id="page-13-0"></span>[61] T. Karras, M. Aittala, J. Hellsten, S. Laine, J. Lehtinen, and T. Aila, "Training generative adversarial networks with limited data," *NeurIPS* , vol. 33, pp. 12 104–12 114, 2020.
- <span id="page-13-1"></span>[62] S. Zhao, Z. Liu, J. Lin, J.-Y. Zhu, and S. Han, "Differentiable augmentation for data-efficient gan training," *NeurIPS*, vol. 33, pp. 7559–7570, 2020.
- <span id="page-13-2"></span>[63] N.-T. Tran, V.-H. Tran, N.-B. Nguyen, T.-K. Nguyen, and N.-M. Cheung, "Towards good practices for data augmentation in gan training," *arXiv preprint arXiv:2006.05338*, vol. 2, p. 3, 2020.
- <span id="page-13-3"></span>[64] Z. Zhao, Z. Zhang, T. Chen, S. Singh, and H. Zhang, "Image augmentations for gan training," *arXiv preprint arXiv:2006.02595*, 2020.
- <span id="page-13-4"></span>[65] O. Bohdal, Y. Yang, and T. Hospedales, "Flexible dataset distillation: Learn labels instead of images," *arXiv preprint arXiv:2006.08572*, 2020.
- <span id="page-13-5"></span>[66] T. Nguyen, Z. Chen, and J. Lee, "Dataset meta-learning from kernel ridge-regression," in *ICLR*, 2020.
- <span id="page-13-6"></span>[67] S. Omer, "fast-pytorch-kmeans," 9 2020. [Online]. Available: [https://github.com/DeMoriarty/fast](https://github.com/DeMoriarty/fast_pytorch_kmeans)\_pytorch\_kmeans
- <span id="page-13-7"></span>[68] A. Krizhevsky, G. Hinton *et al.*, "Learning multiple layers of features from tiny images," 2009.
- <span id="page-13-8"></span>[69] Y. Netzer, T. Wang, A. Coates, A. Bissacco, B. Wu, and A. Y. Ng, "Reading digits in natural images with unsupervised feature learning," 2011.
- <span id="page-13-9"></span>[70] Y. LeCun, L. Bottou, Y. Bengio, and P. Haffner, "Gradient-based learning applied to document recognition," *Proceedings of the IEEE*, vol. 86, no. 11, pp. 2278–2324, 1998.
- <span id="page-13-10"></span>[71] H. Xiao, K. Rasul, and R. Vollgraf, "Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms," *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-13-11"></span>[72] J. Deng, W. Dong, R. Socher, L.-J. Li, K. Li, and L. Fei-Fei, "Imagenet: A large-scale hierarchical image database," in *CVPR*. Ieee, 2009, pp. 248–255.
- <span id="page-13-12"></span>[73] S. Gidaris and N. Komodakis, "Dynamic few-shot visual learning without forgetting," in *CVPR*, 2018, pp. 4367–4375.
- <span id="page-13-13"></span>[74] D. Ulyanov, A. Vedaldi, and V. Lempitsky, "Instance normalization: The missing ingredient for fast stylization," *arXiv preprint arXiv:1607.08022* , 2016.
- <span id="page-13-14"></span>[75] G. Huang, Z. Liu, L. Van Der Maaten, and K. Q. Weinberger, "Densely connected convolutional networks," in *CVPR*, 2017, pp. 4700–4708.
- <span id="page-13-15"></span>[76] L. Van der Maaten and G. Hinton, "Visualizing data using t-sne." *Journal of machine learning research*, vol. 9, no. 11, 2008.
# INFLUENCE-G<PERSON>DED DIFFUSION FOR DATASET DISTIL-LATION

Ming<PERSON> Chen<sup>1,2</sup>, <PERSON><PERSON><PERSON><sup>3</sup>, <PERSON><sup>1,2</sup>, <PERSON><sup>4</sup>, <PERSON><PERSON><sup>5</sup>, <PERSON><sup>1,2</sup>

<sup>1</sup>The Hong Kong University of Science and Technology (Guangzhou) <sup>2</sup>The Hong Kong University of Science and Technology <sup>3</sup>CFAR, A\*STAR, Singapore <sup>4</sup>Dongguan University of Technology <sup>5</sup>Southwest Jiaotong University

<EMAIL>,<EMAIL>

# ABSTRACT

Dataset distillation aims to streamline the training process by creating a compact yet effective dataset for a much larger original dataset. However, existing methods often struggle with distilling large, high-resolution datasets due to prohibitive resource costs and limited performance, primarily stemming from sample-wise optimizations in the pixel space. Motivated by the remarkable capabilities of diffusion generative models in learning target dataset distributions and controllably sampling high-quality data tailored to user needs, we propose framing dataset distillation as a controlled diffusion generation task aimed at generating data specifically tailored for effective training purposes. By establishing a correlation between the overarching objective of dataset distillation and the trajectory influence function, we introduce the Influence-Guided Diffusion (IGD) sampling framework to generate training-effective data without the need to retrain diffusion models. An efficient guided function is designed by leveraging the trajectory influence function as an indicator to steer diffusions to produce data with influence promotion and diversity enhancement. Extensive experiments show that the training performance of distilled datasets generated by diffusions can be significantly improved by integrating with our IGD method and achieving state-of-the-art performance in distilling ImageNet datasets. Particularly, an exceptional result is achieved on the ImageNet-1K, reaching 60.3% at IPC=50. Our code is available at [https:](https://github.com/mchen725/DD_IGD) [//github.com/mchen725/DD\\_IGD](https://github.com/mchen725/DD_IGD).

# 1 INTRODUCTION

Dataset distillation has gained significant attention due to its ability to balance the conflict demands of maintaining training effectiveness while overwhelming resource overhead. This method involves crafting a compact yet effective surrogate dataset for a large-scale original dataset. The surrogate is optimized to retain essential information from the cumbersome original, enabling models trained on it to achieve performance comparable to those trained on the complete one.

Early dataset distillation methods have made significant strides in distillation efficacy through various insightful paradigms [\(Zhao et al., 2021;](#page-12-0) [Kim et al., 2022;](#page-11-0) [Nguyen et al., 2021;](#page-11-1) [Cazenavette et al.,](#page-10-0) [2022;](#page-10-0) [Du et al., 2023;](#page-10-1) [Cui et al., 2023\)](#page-10-2). However, their success is mainly limited to distilling small datasets like CIFAR [\(Krizhevsky & Hinton, 2009\)](#page-11-2) or downscaled ImageNet [\(Russakovsky et al.,](#page-11-3) [2015\)](#page-11-3) with low resolution. Extending these methods to higher-resolution datasets (e.g.,  $\geq 128 \times 128$ ) is hindered by treating data as a entity and refining it at the pixel level. This escalates time and computational costs with data dimensionality and preset compression ratios, typically indicated by Images Per Class (IPC). Moreover, prioritizing pixel-level optimization overlooks distributional shifts from the original dataset. Yet, at higher resolutions, synthetic data retains ineffective high-frequency patterns, leading to performance degradation [\(Cazenavette et al., 2023\)](#page-10-3).

Recognizing the robust capability to capture intricate data distributions, a recent approach [\(Gu](#page-10-4) [et al., 2024\)](#page-10-4) integrates diffusion models to tackle the high-resolution challenges faced by previous pixel-oriented methods, achieving cutting-edge performance. This technique entails fine-tuning a latent diffusion model through a minimax criterion, yielding distilled datasets that harmonize representativeness and diversity for better alignment with the authentic data distribution. However, research on core-set selection techniques [\(Killamsetty et al., 2021a;](#page-11-4)[b;](#page-11-5) [Iyer et al., 2021\)](#page-11-6) indicates that even data sampled directly from the authentic distribution can contribute unevenly to model training. Concerns remain about the effectiveness of the proposed objective in generating distilled datasets that are optimally tailored for highly effective training.

In this work, we introduce a new paradigm of using diffusion models in the task of dataset distillation, termed the Influence-Guided Diffusion (IGD) sampling method. This method is conceptually tailored to directly guide diffusion models in generating data under a generalized training-effective condition, without requiring to retrain diffusion models. We highlight the challenges inherent in achieving this target, particularly due to the abstract nature of the tailored condition, in contrast to existing controlled diffusion generation tasks that involve explicit content specifications [\(Rombach et al., 2022;](#page-11-7) [Ho & Salimans,](#page-10-5) [2022\)](#page-10-5). To address this challenge, we first establish a correlation between the overarching objective of dataset distillation and the trajectory influence function [\(Pruthi et al., 2020\)](#page-11-8), which approximates the impact of training on given data in terms of test loss

<span id="page-1-0"></span>Image /page/1/Figure/3 description: A bar chart compares the validation accuracy and normalized influence of four different methods: DiT, DiT-IGD, Minimax, and Minimax-IGD. The y-axis on the left represents validation accuracy in percentage, ranging from 76% to 88%. The y-axis on the right represents normalized influence, ranging from 0.36 to 0.50. For DiT, accuracy is approximately 78.5% and influence is approximately 80%. For DiT-IGD, accuracy is approximately 84.5% and influence is approximately 83.5%. For Minimax, accuracy is approximately 81.5% and influence is approximately 81%. For Minimax-IGD, accuracy is approximately 86% and influence is approximately 85%. Below the bars, a legend indicates that blue bars represent accuracy and red bars represent influence. Further down, a dashed box with circles and dots indicates different experimental settings: fine-tuning (represented by an empty circle) and guided sampling (represented by a filled circle). The DiT and Minimax methods are associated with fine-tuning, while DiT-IGD and Minimax-IGD are associated with guided sampling.

Figure 1: Enhanced cross-architecture performance with average influence by integrating IGD in distilling ImageNette with IPC=100.

variation. Building on this connection, we develop diversity-constraint and influence-based guidance as indicators to steer the diffusion models towards generating data with influence promotion and diversity enhancement, as illustrated in Figure [5.](#page-13-0) As evidenced by Figure [1,](#page-1-0) integrating IGD significantly enhances the performance of the vanilla Diffusion Transformer (DiT), outperforming results obtained through the fine-tuning method Minimax. Moreover, IGD complements Minimax to achieve even better results, with a simultaneous increase in influence.

In summary, our contributions are as follows:

- We propose a new scheme for dataset distillation by framing the task as a guided-diffusion generation problem.
- We establish a novel diffusion sampling framework that pioneers the integration of the influence function as a guidance for the controlled diffusion generation, with the aim of achieving generalized training-enhancing objectives.
- Experimental results illustrate that our method significantly improves the performance of diffusion models across different architectures on two ImageNet subsets. Furthermore, a state-of-the-art result is achieved on the ImageNet-1K, reaching  $60.3\%$  at IPC=50.

# 2 PRELIMINARIES

# 2.1 BACKGROUND ON DATASET DISTILLATION

We refer to the target dataset as  $\mathcal{T} = \{(\bm{x}_i, \bm{y}_i)\}_{i=1}^{|\mathcal{T}|}$ . Each sample  $\bm{x}_i$  is drawn i.i.d. from a natural distribution  $q(x)$ , where  $x_i \in \mathbb{R}^d$  and  $y_i \in \mathcal{Y} = \{1, 2, ..., C\}$  refers to the ground-truth label. Dataset Distillation (DD) aims to condense this large labelled dataset  $\mathcal T$  into a smaller synthetic dataset  $\mathcal{S} = \{(\bm{u}_i, \bm{y}_i)\}_{i=1}^{|\mathcal{S}|}$ , with  $\bm{u}_i \in \mathbb{R}^d$  and  $\bm{y}_i \in \mathcal{Y}$ , such that  $|\mathcal{S}| \ll |\mathcal{T}|$ . The reduced dataset  $\mathcal{S}$  is optimized to retain essential information from  $\mathcal T$  to ensure that any model initialized with parameters  $\theta_0$  can be optimized to minimize the validation loss on the target dataset  $\mathcal{T}$ :

<span id="page-1-1"></span>
$$
\min_{\mathcal{S}} \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \left[ \ell\left(\boldsymbol{x}_i, \boldsymbol{y}_i; \boldsymbol{\theta}_*^{\mathcal{S}}\right) - \ell\left(\boldsymbol{x}_i, \boldsymbol{y}_i; \boldsymbol{\theta}_0\right) \right] \quad s.t. \ \boldsymbol{\theta}_*^{\mathcal{S}} = Alg(\mathcal{S}, \boldsymbol{\theta}_0). \tag{1}
$$

Here,  $Alg(S, \theta_0) = \arg \min_{\theta} \mathbb{E}_{(\mathbf{u}_i, \mathbf{y}_i) \in S} [\ell(\mathbf{u}_i, \mathbf{y}_i; \theta)]$  represents the training algorithm that optimizes the initialized parameters  $\hat{\theta}_0$  over the synthetic data S, and  $\ell(x, y; \theta)$  denotes the prediction

loss of a model with parameters  $\theta$  on a data pair  $(x, y)$ . To prevent unexpected distributional shift, we propose to frame DD as *learning a conditional distribution of the authentic distribution*, e.g.,  $p(x|Condition)$ , to sample near-real data under the generalized training-effective conditions.

### 2.2 GUIDED DIFFUSION GENERATION

Given samples from the data distribution  $q(x)$ , diffusion models are capable of learning a parameterized distribution  $p_{\phi}(x)$  that approximates  $q(x)$  and is easy to sample from it [\(Song et al., 2020b\)](#page-12-1). On a high level, this is implemented through a forward noising process and a reverse denoising process. Concretely, the forward process gradually adds Gaussian noise  $\epsilon \sim \mathcal{N}(0, I)$  of different magnitudes to clean data point  $z_0$ :  $z_t = \sqrt{\alpha_t} z_0 + \sqrt{1 - \alpha_t} \epsilon$ , where  $\alpha_t$  controls the noise scale at step t. A diffusion model is a denoising function that learns by minimizing the dissimilarity, e.g., mean squared error, between the predicted noise  $\epsilon_{\phi}(z_t, t, c)$  and  $\epsilon$ , where c is a conditional input such as labels. The reverse process generates denoised samples by sampling from  $p_{\phi}(z_{t-1}|z_t, z_0)$ , which is generally parameterized as a Gaussian distribution and varies across studies in its approximation [\(Ho et al.,](#page-11-9) [2020\)](#page-11-9). For instance, Denoising Diffusion Implicit Model (DDIM) [\(Song et al., 2020a\)](#page-12-2) first predicts the clean data point  $\hat{z}_{0|t}$  based on  $z_t$  as:

<span id="page-2-0"></span>
$$
\hat{z}_{0|t} = \frac{1}{\sqrt{\alpha_t}} (z_t - \sqrt{1 - \alpha_t} \epsilon_\phi(z_t, t, c)). \tag{2}
$$

 $z_{t-1}$  is then sampled from  $\mathcal{N}\left(\sqrt{\alpha_{t-1}}\hat{z}_{0|t} + \sqrt{1-\alpha_{t-1}-\sigma_t^2}\epsilon_\phi(z_t,t,c), \sigma_t^2 I\right)$ , where  $\sigma_t$  is the predefined noise factor. For notation simplicity, we abstract this process as:  $z_{t-1} = s(z_t, t, \epsilon_\phi)$ . In this work, we adopt the widely utilized *latent diffusion* [\(Peebles & Xie, 2023\)](#page-11-10) as the backbone. Here, an encoder is employed to transform images to latent codes  $z = E(x)$  and a decoder  $D(\cdot)$ reconstructs latent codes back to the image space to obtain the distilled dataset  $\mathcal{S} = \{ (D(z_i), y_i) \}_{i=1}^{|\mathcal{S}|}$ .

Diffusion models typically employ conditioning to tailor outputs to specific user inputs, such as labels or text prompts. However, our purpose diverges from explicit content specifications, focusing instead on more abstract requirements. We aim to guide the diffusion model to identify conditional distributions within the learned distribution and selectively sample data to optimize training effectiveness. To this end, we employ a more adaptable method of controlling model outputs through *guided-diffusion generation* [\(Bansal et al., 2023;](#page-10-6) [Yu et al., 2023;](#page-12-3) [Gopalakrishnan Nair et al., 2023\)](#page-10-7). These methods are largely inspired by the energy-based model (EBM) used for formulating score-based diffusions [\(Song](#page-12-1) [et al., 2020b;](#page-12-1) [2021\)](#page-12-4). Intuitively, any metric function  $f_C(.)$  that subtly measures the compatibility of the noisy sample  $z_t$  to the condition C is valid for providing steering guidance. By this means, the sampling step can generally be implemented as:

$$
\mathbf{z}_{t-1} = s(\mathbf{z}_t, t, \boldsymbol{\epsilon}_{\phi}) - \rho_t \nabla_{\mathbf{z}_t} f_C(\mathbf{z}_t),
$$
\n(3)

where  $\rho_t$  is defined to align with the denoising scale of the current  $\epsilon_\phi(z_t, t)$ . By introducing a meticulously designed guided function that effectively measures the impact of data on training efficacy (e.g., as depicted by validation loss), this implementation seamlessly aligns with our objective of framing dataset distillation as sampling data from a desirable conditional distribution.

### 3 METHOD

### 3.1 ESTIMATING DATA INFLUENCE AS DIFFUSION CONDITIONAL GUIDANCE

We identify influence function [\(Koh & Liang, 2017\)](#page-11-11) as insightful parallel research that can quantify the impact of specific training data on model validation loss. This is highly relevant to the design of metric functions used for steering guidance in diffusion models under our training-effective condition. Leveraging the Fundamental Theorem of Calculus, [Pruthi et al.](#page-11-8) [\(2020\)](#page-11-8) introduced trajectory influence to estimate the cumulative influence of a training data pair  $(x, y)$  on validation data pair  $(x', y')$ . This method integrates the stepwise changes in the loss of the validation data throughout the training process. In our case, employing Stochastic Gradient Descent (SGD) as the training algorithm  $Alg$ , the model update can be expressed as  $\theta_{t+1} - \theta_t = -\eta_t \nabla_{\theta} \ell(x, y; \theta_t)$ , where  $\eta_t$  represents the learning rate at timestep t. Utilizing the first-order Taylor expansion, the loss change of  $(x', y')$  at

each timestep can be approximated by:

$$
\ell(\mathbf{x}', \mathbf{y}'; \theta_{t+1}) - \ell(\mathbf{x}', \mathbf{y}'; \theta_t) \approx \nabla_{\theta} \ell(\mathbf{x}', \mathbf{y}'; \theta_t) \cdot (\theta_{t+1} - \theta_t) \n= -\eta_t \nabla_{\theta} \ell(\mathbf{x}', \mathbf{y}'; \theta_t) \cdot \nabla_{\theta} \ell(\mathbf{x}, \mathbf{y}; \theta_t).
$$
\n(4)

The overall influence of  $(x, y)$  on  $(x', y')$  throughout the training trajectory is quantified by aggregating these stepwise changes across epochs:

$$
\mathcal{I}(\boldsymbol{x}, \boldsymbol{x}') = \sum_{e=0}^{E} \bar{\eta}_e \nabla_{\boldsymbol{\theta}} \ell(\boldsymbol{x}', \boldsymbol{y}'; \boldsymbol{\theta}_e) \cdot \nabla_{\boldsymbol{\theta}} \ell(\boldsymbol{x}, \boldsymbol{y}; \boldsymbol{\theta}_e) \propto \ell(\boldsymbol{x}', \boldsymbol{y}'; \boldsymbol{\theta}_0) - \ell(\boldsymbol{x}', \boldsymbol{y}'; \boldsymbol{\theta}_E), \qquad (5)
$$

where  $\bar{\eta}_e$  denotes the learning rate of the e-th epoch, for a total of E epochs. By substituting the validation data  $(x', y')$  as the real data in the original dataset, this formulation is an effective approximation to the general objective of dataset distillation defined in Equation [\(1\)](#page-1-1). Based on this insight, we define the objective of the guidance for a latent code  $z$  given a certain class  $c$  as:

<span id="page-3-0"></span>
$$
\max_{\boldsymbol{z}} \frac{1}{|\mathcal{T}_c|} \sum_{i=1}^{|\mathcal{T}_c|} \mathcal{I}(D(\boldsymbol{z}), \boldsymbol{x}_i) = \max_{\boldsymbol{z}} \sum_{e=0}^{E} \bar{\eta}_e \bar{\nabla}_{\boldsymbol{\theta}} \ell_c \left( \boldsymbol{\mathcal{X}}_c; \boldsymbol{\theta}_e^{\mathcal{S}} \right) \cdot \nabla_{\boldsymbol{\theta}} \ell_c \left( D(\boldsymbol{z}); \boldsymbol{\theta}_e^{\mathcal{S}} \right), \tag{6}
$$

where  $\bar{\nabla}_{\theta} \ell_c(\mathcal{X}_c; \theta_e^{\mathcal{S}}) = \frac{1}{|\mathcal{T}_c|} \sum_{i=1}^{|\mathcal{T}_c|} \nabla_{\theta} \ell(\mathbf{x}_i, c; \theta_e^{\mathcal{S}})$  based on the Fubini's Theorem and  $\mathcal{T}_c$  is the subset of the given class  $c, \theta_e^S$  represents a checkpoint obtained on the decoded data. Intuitively, this objective can be optimized if models trained on synthetic data obtain trajectories equivalent to those trained on  $\mathcal{T}_c$ , thereby maximizing the validation loss drop. This essentially shares a similar purpose with the Gradient-Matching (GM) scheme [\(Zhao et al., 2021;](#page-12-0) [Zhao & Bilen, 2021a\)](#page-12-5). However, we identify three primary issues with directly adapting this formulation as the metric function for guided diffusion in dataset distillation: (1) prohibitive cost: the necessity of model retraining at each diffusion sampling step is computationally burdensome; (2) accumulated error: akin to the limitations of the GM method, the gap between trajectories inevitably accumulates during training on synthetic data, leading to ineffective matching and consequently degraded performance [\(Cazenavette et al.,](#page-10-0) [2022\)](#page-10-0); (3) information redundancy: the relatively poor diversity of diffusion-generated data limits its effectiveness for dataset distillation [\(Du et al., 2023\)](#page-10-1), and matching with the averaged real gradients, as shown in Equation [\(6\)](#page-3-0), may further exacerbate this issue.

In the following section, we tackle these challenges by developing diversity-constrained guided functions and detailing our Influence-Guided Diffusion (IGD) sampling framework.

<span id="page-3-2"></span>

### 3.2 EFFICIENT INFLUENCE-GUIDED DIFFUSION SAMPLING WITH DIVERSITY CONSTRAINT

Denote  $\theta_e^{\mathcal{T}_c} = \theta_{e-1}^{\mathcal{T}_c} - \bar{\eta}_{e-1} \bar{\nabla}_{\theta} \ell_c(\mathcal{X}_c; \theta_{e-1}^{\mathcal{T}_c})$  as checkpoints trained on the real subset  $\mathcal{T}_c$  with SGD and the same learning rate schedule as on the synthetic data. Replacing the checkpoints  $\theta_e^{\mathcal{S}}$  with  $\theta_e^{\mathcal{T}_c}$  in Equation [\(6\)](#page-3-0) is an optimizably equivalent target. This equivalence holds because these two targets converge to the same optimal solution when  $z$  can provide the same training dynamics as  $\mathcal{T}_{c}$ , i.e.,  $\bar{\nabla}_{\theta} \ell_{c}(\mathcal{X}_{c}; \theta_{e}^{\mathcal{T}_{c}}) = \nabla_{\theta} \ell_{c}(D(z); \theta_{e}^{\mathcal{T}_{c}})$   $\forall e \in [0, E]$ . Building on this insight, in practical implementation, we extend this usage to the checkpoints  $\theta_e^{\mathcal{T}}$  obtained through standard mini-batch updates over the entire dataset  $\mathcal T$ . This adjustment mitigates the mismatch caused by the discrepancy between synthetic and real trajectories [\(Kim et al., 2022\)](#page-11-0), while also eliminating the time cost associated with retraining models on  $S$  at each sampling step. Additionally, we use cosine similarity instead of the dot product to stabilize the magnitude of the guidance signal provided by the influence function. These modifications yield the influence guided loss function as:

<span id="page-3-1"></span>
$$
\mathcal{G}_{I}(z) = \frac{1}{|E|} \sum_{e=1}^{E} \bar{\eta}_{e} \left( 1 - \frac{\bar{\nabla}_{\theta} \ell_{c} \left( \mathcal{X}_{c} ; \theta_{e}^{\mathcal{T}} \right) \cdot \nabla_{\theta} \ell_{c} \left( D(z) ; \theta_{e}^{\mathcal{T}} \right)}{\left\| \bar{\nabla}_{\theta} \ell_{c} \left( \mathcal{X}_{c} ; \theta_{e}^{\mathcal{T}} \right) \right\| \left\| \nabla_{\theta} \ell_{c} \left( D(z) ; \theta_{e}^{\mathcal{T}} \right) \right\|} \right). \tag{7}
$$

Directly computing the influence over an intermediate noisy  $z_t$  is undesirable, as the checkpoints  $\theta_e^{\mathcal{T}}$ are trained on clean data and do not adapt to provide meaningful guidance as a metric function when the input is noisy [\(Ho & Salimans, 2022\)](#page-10-5). To mitigate this issue, we utilize the *predicted* clean data  $\hat{z}_{0}|t}$  of the current  $z_t$ , based on Equation [\(2\)](#page-2-0) as defined by DDIM, as an approximation of the real  $z_0$ . Subsequently, we compute the influence guidance  $G_I(\hat{z}_{0|t})$  on the predicted clean data and derive the guided gradient  $\nabla_{z_t} G_I((z_t - \sqrt{1 - \alpha_t} \epsilon_{\phi}(z_t, t))/\sqrt{\alpha_t})$  through backpropagation.

#### Algorithm 1: Influence-Guided Diffusion Sampling

- <span id="page-4-1"></span>1 **Parameters:** Class c, influence factor  $\rho_t$ , deviation factor  $\gamma_t$ , scales  $\{\alpha_t\}_{t=1}^T$ , guided range A,B **2 Required:** Pre-trained diffusion model  $\epsilon_{\phi}$ , list of retained checkpoints R, list of averaged
- gradients  $G_c$ , generated data memory  $\mathcal{M}_c$ , decoder model D
- 3 Initialize: Sample initial random noise  $z_T \sim \mathcal{N}(0, I);$ 4 for  $t = T$  to  $I$  do
- 5 Obtain the denoised signal  $\epsilon_{\phi}(z_t, t, c)$  from the diffusion model;
- 6 **if** t in  $[A, B]$  then
- 7 Calculate the influence metric  $G_I(\hat{z}_{0|t})$  as Equation [\(7\)](#page-3-1) with R and  $G_c$ ;
- 8 Calculate the deviation metric  $\mathcal{G}_D(z_t)$  as Equation [\(8\)](#page-4-0) with  $\mathcal{M}_c$ ;
- 9 | Implement guided sampling  $z_{t-1} = s(z_t, t, \epsilon_{\phi}) \rho_t \nabla_{z_t} \mathcal{G}_I(\hat{z}_{0|t}) \gamma_t \nabla_{z_t} \mathcal{G}_D(z_t);$
- <sup>10</sup> else
- 11 | Implement vanilla sampling  $z_{t-1} = s(z_t, t, \epsilon_{\phi})$ ;
- 12 **return** Decoded synthetic image  $D(z_0)$ ;

To ensure diversity and avoid excessive redundancy in the surrogate dataset's training signals, we propose adding a constraint to the generation objective. This constraint ensures that the similarity between generated data within a certain class does not exceed a specified threshold:  $\text{sim}(z_i, z_j) \leq$ δ,  $\forall z_i, z_j \in \mathcal{Z}_c$ , where  $z_i \neq z_j$ . In practice, we incorporate this constraint using a Lagrangian multiplier and propose a deviation guidance function to optimize it in each guided sampling step:

<span id="page-4-0"></span>
$$
\mathcal{G}_D(z) = \frac{z \cdot \tilde{z}^*}{\|z\| \|\tilde{z}^*\|} \quad \text{subject to} \quad \tilde{z}^* = \underset{\tilde{z} \in \mathcal{M}^c}{\arg \max} \frac{z \cdot \tilde{z}}{\|z\| \|\tilde{z}\|},\tag{8}
$$

where  $\mathcal{M}^c$  represents the set of all previously generated data for a certain class  $c$ .

Ultimately, we utilize the influence guidance of  $\mathcal{G}_I(\hat{\mathcal{z}}_{0|t})$  alongside the deviation guidance of  $\mathcal{G}_D(\mathcal{z}_t)$ , reformulating the guided sampling step as:

<span id="page-4-2"></span>
$$
\mathbf{z}_{t-1} = s(\mathbf{z}_t, t, \boldsymbol{\epsilon}_{\phi}) - \rho_t \nabla_{\mathbf{z}_t} \mathcal{G}_I(\hat{\mathbf{z}}_{0|t}) - \gamma_t \nabla_{\mathbf{z}_t} \mathcal{G}_D(\mathbf{z}_t), \text{ where } \rho_t = k \cdot \sqrt{1 - \alpha_t} \frac{\|\boldsymbol{\epsilon}_{\phi}(\mathbf{z}_t, t, c)\|}{\|\nabla_{\mathbf{z}_t} \mathcal{G}_I(\hat{\mathbf{z}}_{0|t})\|} \tag{9}
$$

is the scale factor designed to adaptively adjust the magnitude of the influence guidance alongside the dynamics of the denoised signal  $\epsilon_{\phi}$ , and  $\gamma_t$  is empirically preset for the deviation guidance. Furthermore, we introduce two practical techniques that are essential for enhancing both the efficiency and efficacy of the proposed IGD framework.

Choosing representative checkpoints via gradient similarity. For efficiency, trajectory influence initially suggests saving checkpoints at regular intervals to compute step-wise influence. However, given the non-linear nature of training dynamics, evenly spaced checkpoints may scatter attention to critical stages. To efficiently calculate the influence guidance, we propose a simple yet effective filtering algorithm. We store  $\theta_0^{\mathcal{T}}$  as the first checkpoint in a list R and compute its averaged gradient  $\mathbb{E}_c[\bar{\nabla}_{\theta} \tilde{\ell}_c(\tilde{\bm{X}}_c; \theta_0^{\mathcal{T}})]$  as the initial reference. For each subsequent checkpoint, we compute the averaged gradient and calculate its cosine similarity with the reference. If the similarity is below a given threshold, we store the current checkpoint and update its averaged gradient as the new reference. This process traverses all epochs, and only the retained checkpoints in  $R$  are used by influence guidance.

Mitigating overfitting and reducing runtime by early-stage Guidance. Guided diffusion tasks face a trade-off between generation quality and the impact of guidance [\(Lugmayr et al., 2022;](#page-11-12) [Bansal et al., 2023\)](#page-10-6). In our problem, we observe that samples generated with a large preset k in  $\rho_t$ achieve significant influence loss reduction but also exhibit noticeable abnormalities and degraded performance. Detailed evaluations are provided in Section [4.4.](#page-7-0) Empirical observations in diffusion generation demonstrate that most semantic content is generated during the early-to-mid stages of sampling [\(Yu et al., 2023\)](#page-12-3). We adopt guided sampling only in these partial steps, allowing vanilla sampling to refine details in the remaining steps. For example, in DDIM with 50 sampling steps, guided sampling is applied only when  $t$  is in [30, 45]. This approach allows data generated with strong guidance to maintain comparable influence without noticeable abnormalities or performance degradation. Consequently, this also reduces the runtime associated with guidance calculation.

| Dataset | Model       | IPC             | Random                                             | DM                                                 | $IDC-1$                                            | DiT                                                | DiT-IGD                                            | Minimax                                            | Minimax-IGD                                        | Full           |
|---------|-------------|-----------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------|
|         | ConvNet-6   | 10<br>50<br>100 | $46.0 \pm 0.5$<br>$71.8 \pm 1.2$<br>79.9±0.8       | $49.8 \pm 1.1$<br>$70.3 \pm 0.8$<br>$78.5 \pm 0.8$ | $48.2 \pm 1.2$<br>$72.4 \pm 0.7$<br>$80.6 \pm 1.1$ | $56.2 \pm 1.3$<br>$74.1 \pm 0.6$<br>$78.2 \pm 0.3$ | $61.9 \pm 1.9$<br>$80.9 \pm 0.9$<br>$84.5 \pm 0.7$ | $58.2 \pm 0.9$<br>$76.9 \pm 0.9$<br>$81.1 \pm 0.3$ | $58.8 \pm 1.0$<br>$82.3 \pm 0.8$<br>$86.3{\pm}0.8$ | $94.3 \pm 0.5$ |
| Nette   | ResNetAP-10 | 10<br>50<br>100 | $54.2 \pm 1.2$<br>$77.3 \pm 1.0$<br>$81.1 \pm 0.6$ | $60.2 \pm 0.7$<br>$76.7 \pm 1.0$<br>$80.9 \pm 0.7$ | $60.4 \pm 0.6$<br>$77.4 \pm 0.7$<br>$81.5 \pm 1.2$ | $62.8 \pm 0.8$<br>$76.9 \pm 0.5$<br>$80.1 \pm 1.1$ | $66.5 \pm 1.1$<br>$81.0 \pm 1.2$<br>$85.2 \pm 0.5$ | $63.2 \pm 1.0$<br>$78.2 \pm 0.7$<br>$81.3 \pm 0.9$ | $63.5 \pm 1.1$<br>$82.3 \pm 1.1$<br>$86.1 \pm 0.9$ | $94.6 \pm 0.5$ |
|         | ResNet-18   | 10<br>50<br>100 | $55.8 \pm 1.0$<br>$75.8 \pm 1.1$<br>$82.0 \pm 0.4$ | $60.9 \pm 0.7$<br>$75.0 \pm 1.0$<br>$81.5 \pm 0.4$ | $61.0 \pm 0.8$<br>$77.8 \pm 0.7$<br>$81.7 \pm 0.8$ | $62.5 \pm 0.9$<br>$75.2 \pm 0.9$<br>$77.8 \pm 0.6$ | $67.7 \pm 0.3$<br>$81.0 \pm 0.7$<br>$84.4 \pm 0.8$ | $64.9 \pm 0.6$<br>$78.1 \pm 0.6$<br>$81.3 \pm 0.7$ | $66.2 \pm 1.2$<br>$82.0 \pm 0.3$<br>$86.0 \pm 0.6$ | $95.3 \pm 0.6$ |
|         | ConvNet-6   | 10<br>50<br>100 | $25.2 \pm 1.1$<br>$41.9 \pm 1.4$<br>$52.3 \pm 1.5$ | $27.6 \pm 1.2$<br>$43.8 \pm 1.1$<br>$50.1 \pm 0.9$ | $34.1 \pm 0.8$<br>$42.6 \pm 0.9$<br>$51.0 \pm 1.1$ | $32.3 \pm 0.8$<br>$48.5 \pm 1.3$<br>$54.2 \pm 1.5$ | $35.0 \pm 0.8$<br>$54.2 \pm 0.7$<br>$61.1 \pm 1.0$ | $33.5 \pm 1.4$<br>$50.7 \pm 1.8$<br>$57.1 \pm 1.9$ | $36.2 \pm 1.6$<br>$55.7 + 0.8$<br>$63.0 \pm 1.8$   | $85.9 \pm 0.4$ |
| Woof    | ResNetAP-10 | 10<br>50<br>100 | $31.6 \pm 0.8$<br>$50.1 \pm 1.6$<br>$59.2 \pm 0.9$ | $29.8 \pm 1.0$<br>$47.8 \pm 1.2$<br>$59.8 \pm 1.3$ | $38.5 \pm 0.7$<br>$49.3 \pm 0.9$<br>$56.4 \pm 0.5$ | $39.0 \pm 0.9$<br>$55.8 \pm 1.1$<br>$62.5 \pm 0.9$ | $41.0 \pm 0.8$<br>$62.7 \pm 1.2$<br>$69.7 \pm 0.9$ | $39.6 \pm 1.2$<br>$59.8 \pm 0.8$<br>$66.8 \pm 1.2$ | $43.3 \pm 0.3$<br>$65.0 \pm 0.8$<br>$71.5 \pm 0.8$ | $87.2 \pm 0.6$ |
|         | ResNet-18   | 10<br>50<br>100 | $30.9 \pm 1.3$<br>$54.0 \pm 0.8$<br>$63.6 \pm 0.5$ | $30.2 \pm 0.6$<br>$53.9 \pm 0.7$<br>$64.9 \pm 0.7$ | $36.7 \pm 0.8$<br>$54.5 \pm 1.0$<br>$57.7 \pm 0.8$ | $40.6 \pm 0.6$<br>$57.4 \pm 0.7$<br>$62.3 \pm 0.5$ | $44.8 \pm 0.8$<br>$62.0 \pm 1.1$<br>$70.6 \pm 1.8$ | $42.2 \pm 1.2$<br>$60.5 \pm 0.5$<br>$67.4 \pm 0.7$ | $47.2 \pm 1.6$<br>$65.4 \pm 1.8$<br>$72.1 \pm 0.9$ | $89.0 \pm 0.6$ |

<span id="page-5-0"></span>Table 1: **ImageNette & ImageWoof:** Performance comparison with state-of-the-art pixel-level distillation methods, pretrained DiT and Minimax-tuned DiT models with vanilla generation. DiT-IGD and Minimax-IGD denote utilizing our proposed IGD sampling framework for generation.

Algorithm [1](#page-4-1) outlines the detailed process of our influence-guided diffusion sampling framework for generating each synthetic image. Before constructing the surrogate dataset, we first obtain checkpoints  $\{\tilde{\bm{\theta}}_e^{\mathcal{T}}\}_{e=1}^E$  trained on  $\mathcal{T}$  and apply the proposed filtering algorithm to retain representative checkpoints in the list  $R$ . Before initiating generation for a specific class  $c$ , we calculate the averaged gradient  $\bar{\nabla}_{\theta} \ell_c(\mathcal{X}_c; \theta_e^{\mathcal{T}})$  across each retained checkpoint and store them in a list  $G_c$ . Subsequently, we execute the algorithm, storing the generated images in memory  $\mathcal{M}_c$  until the desired number of images reaches the preset target IPC (images per class).

# 4 EXPERIMENTS

### 4.1 EXPERIMENTAL SETUP

Datasets. As our primary interest lies in large-scale, high-resolution distillation tasks, we assess the performance of our method on the complete ImageNet-1K dataset (224×224) [\(Russakovsky et al.,](#page-11-3) [2015\)](#page-11-3). To provide comparable evaluations across varying task difficulties, we conduct comprehensive experiments on two representative subsets, ImageNette and ImageWoof [\(Howard, 2019\)](#page-11-13). ImageNette, consisting of 10 classes with less similarity and therefore easier to distinguish between, contrasts with ImageWoof, a challenging subset containing 10 classes of dog breeds.

Baselines and evaluation metric. We compare our method with several state-of-the-art dataset distillation methods including DM [\(Zhao & Bilen, 2021b\)](#page-12-6), IDC-1 [\(Kim et al., 2022\)](#page-11-0), SRe<sup>2</sup>L [\(Yin et al.,](#page-12-7) [2024\)](#page-12-7), G-VBSM [\(Shao et al., 2023\)](#page-12-8), and RDED [\(Sun et al., 2024\)](#page-12-9). Additionally, we regard pretrained DiT [\(Peebles & Xie, 2023\)](#page-11-10) as a notable baseline because it achieves performance comparable to state-of-the-art methods even without tailored optimizations for dataset distillation. Furthermore, we include Minimax [\(Gu et al., 2024\)](#page-10-4), a recent work refined DiT specifically for dataset distillation through a fine-tuning scheme, as a perpendicular baseline. Test architectures include ConvNet-6, ResNet-10 [\(He et al., 2016\)](#page-10-8) with Average Pooling, ResNet-18, ResNet-101, MoblieNet-V2 [\(Sandler](#page-12-10) [et al., 2018\)](#page-12-10), EfficientNet-B0 [\(Tan, 2019\)](#page-12-11) and Swin Transformer [\(Liu et al., 2021\)](#page-11-14). The top-1 test accuracies of models trained on distilled datasets with different IPC (Image Per Class) are reported.

Implementation detail. For a fair comparison, we follow the official implementation of Minimax, utilizing a latent DiT model from Pytorch's official repository and an open-source VAE model from Stable Diffusion. DDIM [\(Song et al., 2020a\)](#page-12-2) with 50 denoised steps is used as the vanilla sampling method for generation. For each test dataset, we train a 6-layer ConvNet (ConvNet-6) for 50 epochs with the learning rate  $1 \times 10^{-2}$  to collect the surrogate checkpoints used in Equation [\(7\)](#page-3-1). The similarity threshold for choosing representative checkpoints is set as 0.7. The detailed setup of hyperparameters k and  $\gamma_t$  for each datasets is discussed in Appendix [A.10.](#page-17-0) All the experimental results of our method can be obtained on a single RTX 4090 GPU.

<span id="page-6-0"></span>Table 2: ImageNet-1K: Performance comparison over ResNet-18 with state-of-the-art dataset distillation methods, pretrained DiT and Minimax-tuned DiT models with vanilla DDIM generation.

| Dataset     | IPC | SRe2L        | G-VBSM       | RDED         | DiT          | DiT-IGD      | Minimax      | Minimax-IGD  |
|-------------|-----|--------------|--------------|--------------|--------------|--------------|--------------|--------------|
| ImageNet-1K | 10  | 21.3 $±$ 0.6 | 31.4 $±$ 0.5 | 42.0 $±$ 0.1 | 39.6 $±$ 0.4 | 45.5 $±$ 0.5 | 44.3 $±$ 0.5 | 46.2 $±$ 0.6 |
|             | 50  | 46.8 $±$ 0.2 | 51.8 $±$ 0.4 | 56.5 $±$ 0.1 | 52.9 $±$ 0.6 | 59.8 $±$ 0.3 | 58.6 $±$ 0.3 | 60.3 $±$ 0.4 |

<span id="page-6-1"></span>Table 3: **ImageNet-1K:** Cross-architecture generalization performance comparison.

|                                       | ResNet101                                          |                                              | MobileNet-V2                                       |                                                |                                                    | EfficientNet-B0                                    | Swin Transformer                                   |                                                    |
|---------------------------------------|----------------------------------------------------|----------------------------------------------|----------------------------------------------------|------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|
|                                       | IPC10                                              | IPC50                                        | IPC10                                              | IPC50                                          | IPC10                                              | <b>IPC50</b>                                       | IPC10                                              | IPC50                                              |
| <b>RDED</b><br>DiT-IGD<br>Minimax-IGD | $48.3 \pm 1.0$<br>$52.6 \pm 1.2$<br>$53.4 \pm 0.9$ | $612+04$<br>$66.2 \pm 0.2$<br>$66.8 \pm 0.2$ | $40.4 \pm 0.1$<br>$39.2 \pm 0.2$<br>$39.7 \pm 0.4$ | $53.3+0.2$<br>$57.8 \pm 0.2$<br>$58.5 \pm 0.3$ | $31.0 \pm 0.1$<br>$47.7 \pm 0.1$<br>$48.5 \pm 0.1$ | $58.5 \pm 0.4$<br>$62.0 \pm 0.1$<br>$62.7 \pm 0.2$ | $42.3 \pm 0.6$<br>$44.1 \pm 0.6$<br>$44.8 \pm 0.8$ | $53.2 \pm 0.8$<br>$58.6 \pm 0.5$<br>$58.2 \pm 0.5$ |

### 4.2 COMPARISON WITH STATE-OF-THE-ART METHODS

Evaluation on Woof & Nette. As a training-free sampling framework, our IGD method can be incorporated into the pretrained DiT and Minimax-tuned DiT during the generation process. We designate these two methods as **DIT-IGD** and **Minimax-IGD**, respectively. As depicted in Table [1,](#page-5-0) our IGD-based methods demonstrate a significant improvement over the original backbone methods, and achieve state-of-the-art performance across both Woof and Nette datasets in all IPC settings. These enhancements are consistently observed across all evaluations conducted on the three tested architectures, highlighting a robust cross-architecture generalization ability. Particularly for IPC  $\geq$  50, DiT-IGD notably enhances the performance of DiT by 5.8% on Nette and by 6.6% on Woof, on average. Comparing with Minimax, Minimax-IGD averagely provides a 4.7% boost on Nette and a 5.1% boost on Woof. Moreover, we observed that DiT-IGD outperforms Minimax in most evaluations. Especially for the easier dataset Nette, despite the class distinctions facilitating knowledge condensation, Minimax only shows a marginal average improvement of 2.5% over DiT at IPC=100. In contrast, DiT-IGD achieves an average boost of 6.1%. Compared to diffusion-based methods, the pixel-level optimization methods DM and IDC-1 achieve moderate performance gains over random original images at IPC=10. However, as the IPC increases, the performance gain drastically diminishes or even becomes negative.

Evaluation on ImageNet-1K. Recent approaches proposed for efficiently distilling ImageNet-1K data rely on using well-trained models to provide synthetic images with soft labels to acquire richer information. Following the evaluation protocol of the RDED, we employ a ResNet-18 model, trained on the original dataset, to generate soft labels for synthetic images. The performances shown in Table [2](#page-6-0) are evaluated over the same ResNet-18 architecture. The results demonstrate consistent improvements in integrating our IGD method over the DiT and Minimax methods. In particular, DiT-IGD demonstrates significant improvement to raw DiT, with enhancements of 5.9% at IPC=10 and 6.9% at IPC=50. This also positions our Minimax-IGD method at the forefront of this practical distillation task, surpassing the state-of-the-art image-based method RDED by 4.0%. In the crossarchitecture comparison detailed in Table [3,](#page-6-1) synthetic datasets generated using our IGD methods generally outperform those created by RDED across four different unseen networks. Notably, our DiT-IGD and Minimax-IGD methods surpass RDED by an average margin of 4.6% and 5.0% at IPC=50, respectively. These remarkable performance improvements underline the promising potential of diffusion-based methods in the future of dataset distillation research.

<span id="page-6-2"></span>

## 4.3 CROSS-ARCHITECTURE ROBUSTNESS OF INFLUENCE GUIDANCE

In our IGD framework, influence guidance necessitates a surrogate model to be trained on the original dataset, collecting representative checkpoints for calculating guided loss. Here, we test the impact of influence guidance obtained over networks of different architectures, including ConvNet-6, ResNetAP-10, and ResNet18. We then train these networks on generated surrogate datasets from scratch and evaluate their cross-architecture performance. Table [4](#page-7-1) demonstrates that datasets generated based on ConvNet-6 generally exhibit superior performance. In most cross-architecture evaluations involving ResNetAP-10 and ResNet-18, they even outperform datasets generated with

| Dataset | Surrogate                            | ConvNet-6                                          |                                                    |                                                    |                                                    | ResNetAP-10                                        |                                                    |  | ResNet-18                                          |                                                    |                                                    |
|---------|--------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|--|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|
|         |                                      | IPC10                                              | IPC50                                              | <b>IPC100</b>                                      | IPC10                                              | IPC50                                              | <b>IPC100</b>                                      |  | IPC10                                              | IPC50                                              | <b>IPC100</b>                                      |
| Nette   | ConvNet-6<br>ResNetAP-10<br>ResNet18 | $61.9 \pm 1.9$<br>$58.9 \pm 0.4$<br>$62.2 \pm 0.3$ | $80.9 \pm 0.9$<br>$79.5 \pm 0.8$<br>$78.5 \pm 0.9$ | $84.5 \pm 0.7$<br>$83.7 \pm 0.4$<br>$80.1 \pm 0.3$ | $66.5 \pm 1.1$<br>$66.2 \pm 0.8$<br>$63.3 \pm 1.6$ | $81.0 \pm 1.2$<br>$82.3 \pm 0.2$<br>$79.5 \pm 0.8$ | $85.2 \pm 0.5$<br>$84.4 \pm 0.8$<br>$82.1 \pm 0.5$ |  | $67.7 \pm 0.3$<br>$66.7 \pm 0.6$<br>$63.1 \pm 0.3$ | $81.0 \pm 0.7$<br>$82.3 \pm 0.9$<br>$80.3 \pm 0.7$ | $84.4 \pm 0.8$<br>$85.4 \pm 0.4$<br>$83.3 \pm 1.4$ |
| Woof    | ConvNet-6<br>ResNetAP-10<br>ResNet18 | $35.0 \pm 0.8$<br>$33.8 \pm 1.0$<br>$34.3 \pm 0.8$ | $54.2 \pm 0.7$<br>$53.5 \pm 0.3$<br>$54.3 \pm 0.8$ | $61.1 \pm 1.0$<br>$60.0 \pm 0.4$<br>$61.0 \pm 1.8$ | $41.0 \pm 0.8$<br>$39.6 \pm 0.4$<br>$39.5 \pm 1.1$ | $62.7 \pm 1.2$<br>$61.5 \pm 0.8$<br>$61.0 + 1.4$   | $69.7 \pm 0.9$<br>$68.8 \pm 0.5$<br>$68.7 \pm 0.7$ |  | $44.8 \pm 0.8$<br>$43.6 \pm 0.5$<br>$43.8 \pm 1.4$ | $62.0 \pm 1.1$<br>$65.5 \pm 0.7$<br>$62.9 \pm 1.0$ | $70.6 \pm 1.8$<br>$69.3 \pm 0.4$<br>$69.5 \pm 0.4$ |

<span id="page-7-1"></span>Table 4: Cross-architecture performance of DiT-IGD using different surrogate architectures to calculate influence guidance.

<span id="page-7-2"></span>Table 5: The ablation study of proposed influence guidance  $\mathcal{G}_I$  and deviation guidance  $\mathcal{G}_D$  tested with ResNet-18 on ImageNette .

|       |                 |                | DiT-IGD        |                | Minimax-IGD    |
|-------|-----------------|----------------|----------------|----------------|----------------|
| $G_I$ | $\mathcal{G}_D$ | IPC50          | <b>IPC100</b>  | IPC50          | IPC100         |
| х     | х               | $75.2 \pm 0.9$ | $77.8 \pm 0.6$ | $78.1 \pm 0.6$ | $81.3 \pm 0.7$ |
| ✓     | х               | $76.5 \pm 0.6$ | $79.1 \pm 0.4$ | $81.5 \pm 0.4$ | $85.1 \pm 0.4$ |
| х     | √               | $78.2 + 0.4$   | $80.7 \pm 0.7$ | $78.5 \pm 0.2$ | $82.8 \pm 0.3$ |
| ✓     | ✓               | $81.0 \pm 0.7$ | $84.4 \pm 0.8$ | $82.0 \pm 0.3$ | $86.0 \pm 0.6$ |

Table 6: Comparison of checkpoint selection strategies for Minimax-IGD: the gradientsimilarity-based method versus regular interval selection, on ImageNette with ResNet-18.

| Threshold            | # Checkpoints | Regular                                            | Ours                                               |
|----------------------|---------------|----------------------------------------------------|----------------------------------------------------|
| 0.65<br>0.70<br>0.75 | 3<br>6        | $79.5 \pm 0.6$<br>$79.8 \pm 1.1$<br>$80.5 \pm 0.4$ | $80.4 \pm 0.7$<br>$82.0 \pm 0.3$<br>$81.4 \pm 0.5$ |
| 0.80                 | 10            | $81.1 \pm 0.5$                                     | $80.8 \pm 0.3$                                     |

the test architecture. Additionally, due to fewer model parameters compared to the other two, the computational time required for influence loss calculations is reduced. Based on these observations, we choose to utilize ConvNet-6 as the surrogate in our formal implementation. However, we also note that the performance gap between datasets generated with different architectures is not significant. Particularly, datasets generated with ResNetAP-10 notably outperform ConvNet-6 in several tests against ResNet-18. These results further validate the robustness and generalization ability of our proposed IGD sampling framework.

<span id="page-7-0"></span>

### 4.4 ABLATION STUDY AND ANALYSIS

Guidance component analysis. Table [5](#page-7-2) presents the performance achieved by independently applying influence guidance and deviation guidance to raw DiT and Minimax. The independent utilization of the two proposed guidance mechanisms still enhances the performance of both backbone methods. Specifically, in the case of raw DiT, the incorporation of deviation guidance yields results akin to those obtained with raw Minimax, primarily due to its ability to augment the diversity of generated data. Conversely, for Minimax, sole reliance on influence guidance markedly elevates its performance, achieving parity with the comprehensive framework. Despite Minimax's inherent focus on refining sample diversity through fine-tuning, additional gains can be attained through the integration of deviation guidance. Moreover, it is important to note that although influence guidance yields moderate improvements for raw DiT, the integration of deviation guidance results in significant enhancements. These observations substantiate our discourse regarding the critical role of data diversity in optimizing influence effectiveness. Conclusively, the synergy between influence guidance and deviation guidance complements each other, facilitating our guided sampling framework harmoniously in aligning with the training-enhancing objective.

Early-stage guidance analysis. We assess the practicability of our early-stage guidance strategy by comparing it with the entire guided sampling approach on ImageWoof, with variations in the influence guidance scaling factor  $k$ . Figure [2b](#page-8-0) demonstrates that applying the influence guidance throughout the entire generation stage with a large preset  $k$  can significantly reduce influence loss. However, as illustrated by Figure [2c,](#page-8-0) when  $k \ge 10$ , despite a reduction in loss, validation accuracy notably drops, likely due to overfitting to the surrogate used for influence calculation. Moreover, this also leads to abnormal image generation shown in Figure [2a.](#page-8-0) In contrast, the early-stage guidance strategy allows strong guidance signals to steer the generation process effectively while mitigating the overfitting problem. Consequently, this strategy achieves superior performance in less generation time, thereby enhancing both the efficacy and efficiency of the process.

<span id="page-8-0"></span>Image /page/8/Figure/1 description: The image displays a comparison between 'Entire Guiding' and 'Early-Stage Guiding' methods, illustrated with generated images of dogs and two accompanying plots. Part (a) shows generated images for both methods with k=10 and k=50. Part (b) is a line plot titled 'Normalized Influence' versus 'k in pt', showing that 'Early-Stage Guiding' (red line with stars) generally has higher normalized influence than 'Entire Guiding' (blue dashed line with circles) across different values of k (0.1, 1, 10, 50). Part (c) is a line plot titled 'Validation Accuracy (%)' versus 'k in pt', where both methods show similar trends, with 'Early-Stage Guiding' slightly outperforming 'Entire Guiding' at k=1 and k=10, but both dropping significantly at k=50.

Figure 2: (a) Examples generated using entire and early-stage guidance with varying influence magnitude k on ImageWoof; (b) Averaged normalized loss  $\mathcal{G}_I$  of datasets generated with different values of k and IPC=100; (c) Corresponding validation accuracies for varying  $k$ .

<span id="page-8-1"></span>Image /page/8/Figure/3 description: This figure displays four t-SNE plots, each representing a different method: (a) DiT, (b) DiT-IGD, (c) Minimax, and (d) Minimax-IGD. Each plot shows clusters of data points, with labels indicating different dog breeds such as Beagle, Border terrier, Bobtail, Dingo, English foxhound, Golden retriever, Rhodesian ridgeback, Samoyed, and Shih-Tzu. Above each plot, the Wasserstein distance to T is provided, with values 6.04 for (a), 4.61 for (b), 5.78 for (c), and 4.92 for (d). Additionally, the accuracy (Acc) is shown for each plot: 62.3% for (a), 70.6% for (b), 67.4% for (c), and 72.1% for (d). The plots visualize the distribution of data points in a 2D space, with different colors representing different breeds.

Figure 3: Visualization study for sample distributions of synthetic datasets (IPC=100) generated by four methodologies versus the original ImageWoof dataset. Smaller Wasserstein distances to the original dataset  $T$  signify closer alignment with the authentic distribution.

Checkpoints selection strategy analysis. We assess the efficacy of the gradient-similarity-based checkpoint selection strategy proposed for computing the influence-guided loss (Equation [\(7\)](#page-3-1)). A predetermined threshold is utilized to determine checkpoint selection based on the similarity of its averaged real gradient to the current reference, with an empirically identified suitable range set as [0.6, 0.8]. Thresholds beyond this range result in excessive checkpoint selection, leading to diminished efficiency, while overly small thresholds yield minimal selection. The baseline comparison involves the original trajectory influence's strategy, which saves checkpoints at fixed regular intervals. In Table [6,](#page-7-2) we contrast our strategy's results with various thresholds against the original regular strategy. To ensure fairness, an equal number of regularly collected checkpoints is used for guidance calculation at each threshold scenario. Comparative analysis reveals superior performance of our strategy over the regular approach. Notably, at a threshold of 0.7, our strategy with 4 checkpoints outperforms the results of 10 regularly selected checkpoints, demonstrating enhanced efficiency and efficacy. For a case study, checkpoint selection indexes of  $\{0, 4, 11, 40\}$  are observed at a threshold of 0.7, compared

<span id="page-9-0"></span>Image /page/9/Figure/1 description: This image displays a grid of images, organized into four rows and six columns. Each row is labeled on the left side with a different term: 'DiT', 'DiT-IGD', 'Minimax', and 'Minimax-IGD'. The columns appear to be grouped into three categories, separated by dashed lines. The first two columns in each row feature images of Samoyed dogs in various poses and settings. The next three columns showcase images of churches, ranging from exterior shots of white steeples to interior views of ornate architecture. The final column in each row presents images of vintage gas pumps, predominantly red and green, with some showing signs of wear and tear.

Figure 4: Comparison of image generation results from raw DiT, DiT-IGD, Minimax, and Minimax-IGD. Images in each column share the same random seed. Integrating IGD directly into the generation process produces high-quality data with varying semantic content and enhanced diversity compared to vanilla generation. Many instances exhibit robust consistency under the guidance of IGD.

to the regular indexes  $\{0, 16, 32, 48\}$ . This adaptive selection indicates better alignment with typical training dynamics, as more checkpoints are selected from the early stages of training.

# 4.5 VISUALIZATION STUDY ON GENERATED DATA

**Data distribution comparison.** To clearly investigate the effect of our guided sampling method on diffusion generation, Figure [3](#page-8-1) shows t-SNE distribution comparisons among the full ImageWoof training dataset and data produced by two baseline methods, DiT and Minimax, as well as our two IGD-based approaches, each set at IPC=100. Additionally, we use the Wasserstein distance to quantitatively evaluate how well the distributions of the generated datasets align with the entire training dataset. Relative to the Minimax method, our IGD approach guides the diffusion process to achieve a closer match to the original training set's distribution, offering more comprehensive coverage and lower Wasserstein distances. Notably, Minimax-IGD surpasses DiT-IGD in performance, despite a higher Wasserstein distance from the original dataset. This finding lends partial support to our hypothesis that pinpointing a pivotal conditional distribution within the authentic distribution can be more beneficial than mere distribution alignment.

**Synthetic image comparison.** Figure [4](#page-9-0) compares images generated by vanilla sampling of raw DiT and Minimax with those from guided sampling methods DiT-IGD and Minimax-IGD, using the same random seeds for each column. While baseline DiT generates high-quality images, they often share similar content, such as poses and structures. Minimax attempts to address the diversity issue in the generated data through fine-tuning DiT, but in many cases, the primary content or layout of the objects does not significantly change. In contrast, our method introduces additional signals in each guided generation step, achieving significant content variation and enhanced diversity without reducing quality. Furthermore, the guided signal from IGD is robust, producing similar content in both Minimax fine-tuned DiT and raw DiT in many cases.

# 5 CONCLUSION

In this work, we introduce a novel approach to dataset distillation by framing it as a guided diffusion generation problem. We correlate the general objective of dataset distillation with the trajectory influence function, designing an efficient influence-guided function for the diffusion sampling process. Additionally, we implement a deviation guidance function to ensure diversity and prevent training signal redundancy. These innovations enable us to create an efficient influence-guided diffusion sampling framework. Comprehensive experimental results illustrate that our method significantly improves the performance of diffusion models and demonstrate remarkable crossarchitecture generalization ability.

# ACKNOWLEDGEMENTS

Jiawei Du was supported by the A\*STAR Career Development Fund (Grant No. C233312004). Yi Wang was supported in part by the Guangdong Basic and Applied Basic Research Foundation (Grant No. 2023B1515120058). Wei Wang was supported by the Guangdong Provincial Key Laboratory of Integrated Communication, Sensing, and Computation for Ubiquitous Internet of Things (Grant No. 2023B1212010007), the Guangzhou Municipal Science and Technology Project (Grant Nos. 2023A03J0003, 2023A03J0013, and 2024A03J0621), and the Institute of Education Innovation and Practice Project (Grant Nos. G01RF000012 and G01RF000017).

# REFERENCES

- <span id="page-10-6"></span>Arpit Bansal, Hong-Min Chu, Avi Schwarzschild, Soumyadip Sengupta, Micah Goldblum, Jonas Geiping, and Tom Goldstein. Universal guidance for diffusion models. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 843–852, 2023.
- <span id="page-10-13"></span>Lukas Bossard, Matthieu Guillaumin, and Luc Van Gool. Food-101–mining discriminative components with random forests. In *Computer vision–ECCV 2014: 13th European conference, zurich, Switzerland, September 6-12, 2014, proceedings, part VI 13*, pp. 446–461. Springer, 2014.
- <span id="page-10-0"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, pp. 10708–10717. IEEE, 2022.
- <span id="page-10-3"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *CVPR*, pp. 3739–3748. IEEE, 2023.
- <span id="page-10-9"></span>Mingyang Chen, Bo Huang, Junda Lu, Bing Li, Yi Wang, Minhao Cheng, and Wei Wang. Dataset distillation via adversarial prediction matching, 2023. URL [https://arxiv.org/abs/](https://arxiv.org/abs/2312.08912) [2312.08912](https://arxiv.org/abs/2312.08912).
- <span id="page-10-10"></span>Hyungjin Chung, Jeongsol Kim, Michael T Mccann, Marc L Klasky, and Jong Chul Ye. Diffusion posterior sampling for general noisy inverse problems. *arXiv preprint arXiv:2209.14687*, 2022.
- <span id="page-10-2"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *ICML*, volume 202 of *Proceedings of Machine Learning Research*, pp. 6565–6590. PMLR, 2023.
- <span id="page-10-12"></span>Prafulla Dhariwal and Alexander Quinn Nichol. Diffusion models beat gans on image synthesis. In *NeurIPS*, pp. 8780–8794, 2021.
- <span id="page-10-1"></span>Jiawei Du, Yidi Jiang, Vincent Y. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *CVPR*, pp. 3749–3758. IEEE, 2023.
- <span id="page-10-7"></span>Nithin Gopalakrishnan Nair, Anoop Cherian, Suhas Lohit, Ye Wang, Toshiaki Koike-Akino, Vishal M Patel, and Tim K Marks. Steered diffusion: A generalized framework for plug-and-play conditional image synthesis. *arXiv e-prints*, pp. arXiv–2310, 2023.
- <span id="page-10-11"></span>Alexandros Graikos, Nikolay Malkin, Nebojsa Jojic, and Dimitris Samaras. Diffusion models as plug-and-play priors. *Advances in Neural Information Processing Systems*, 35:14715–14728, 2022.
- <span id="page-10-4"></span>Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2024.
- <span id="page-10-14"></span>Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *ICLR*. OpenReview.net, 2024.
- <span id="page-10-8"></span>Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, pp. 770–778. IEEE Computer Society, 2016.
- <span id="page-10-5"></span>Jonathan Ho and Tim Salimans. Classifier-free diffusion guidance. *arXiv preprint arXiv:2207.12598*, 2022.

- <span id="page-11-9"></span>Jonathan Ho, Ajay Jain, and Pieter Abbeel. Denoising diffusion probabilistic models. *Advances in neural information processing systems*, 33:6840–6851, 2020.
- <span id="page-11-13"></span>Jeremy Howard. Imagenette: A smaller subset of 10 easily classified classes from imagenet, March 2019. URL <https://github.com/fastai/imagenette>.
- <span id="page-11-6"></span>Rishabh Iyer, Ninad Khargoankar, Jeff Bilmes, and Himanshu Asanani. Submodular combinatorial information measures with applications in machine learning. In *Algorithmic Learning Theory*, pp. 722–754. PMLR, 2021.
- <span id="page-11-16"></span>Bahjat Kawar, Michael Elad, Stefano Ermon, and Jiaming Song. Denoising diffusion restoration models. *Advances in Neural Information Processing Systems*, 35:23593–23606, 2022.
- <span id="page-11-4"></span>Krishnateja Killamsetty, Sivasubramanian Durga, Ganesh Ramakrishnan, Abir De, and Rishabh Iyer. Grad-match: Gradient matching based data subset selection for efficient deep model training. In *International Conference on Machine Learning*, pp. 5464–5474. PMLR, 2021a.
- <span id="page-11-5"></span>Krishnateja Killamsetty, Durga Sivasubramanian, Ganesh Ramakrishnan, and Rishabh Iyer. Glister: Generalization based data subset selection for efficient and robust learning. In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 35, pp. 8110–8118, 2021b.
- <span id="page-11-0"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *ICML*, volume 162 of *Proceedings of Machine Learning Research*, pp. 11102–11118. PMLR, 2022.
- <span id="page-11-11"></span>Pang Wei Koh and Percy Liang. Understanding black-box predictions via influence functions. In *International conference on machine learning*, pp. 1885–1894. PMLR, 2017.
- <span id="page-11-2"></span>Alex Krizhevsky and Geoffrey Hinton. Learning multiple layers of features from tiny images. [https:](https://www.cs.toronto.edu/~kriz/cifar.html) [//www.cs.toronto.edu/˜kriz/cifar.html](https://www.cs.toronto.edu/~kriz/cifar.html), 2009. Accessed: March 1, 2023.
- <span id="page-11-14"></span>Ze Liu, Yutong Lin, Yue Cao, Han Hu, Yixuan Wei, Zheng Zhang, Stephen Lin, and Baining Guo. Swin transformer: Hierarchical vision transformer using shifted windows. In *Proceedings of the IEEE/CVF international conference on computer vision*, pp. 10012–10022, 2021.
- <span id="page-11-15"></span>Noel Loo, Ramin M. Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. In *ICML*, volume 202 of *Proceedings of Machine Learning Research*, pp. 22649–22674. PMLR, 2023.
- <span id="page-11-17"></span>Cheng Lu, Yuhao Zhou, Fan Bao, Jianfei Chen, Chongxuan Li, and Jun Zhu. Dpm-solver: A fast ode solver for diffusion probabilistic model sampling in around 10 steps, 2022. URL [https:](https://arxiv.org/abs/2206.00927) [//arxiv.org/abs/2206.00927](https://arxiv.org/abs/2206.00927).
- <span id="page-11-12"></span>Andreas Lugmayr, Martin Danelljan, Andres Romero, Fisher Yu, Radu Timofte, and Luc Van Gool. Repaint: Inpainting using denoising diffusion probabilistic models. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 11461–11471, 2022.
- <span id="page-11-1"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. In *ICLR*. OpenReview.net, 2021.
- <span id="page-11-10"></span>William Peebles and Saining Xie. Scalable diffusion models with transformers. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pp. 4195–4205, 2023.
- <span id="page-11-8"></span>Garima Pruthi, Frederick Liu, Satyen Kale, and Mukund Sundararajan. Estimating training data influence by tracing gradient descent. *Advances in Neural Information Processing Systems*, 33: 19920–19930, 2020.
- <span id="page-11-7"></span>Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Bjorn Ommer. High- ¨ resolution image synthesis with latent diffusion models. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 10684–10695, 2022.
- <span id="page-11-3"></span>Olga Russakovsky, Jia Deng, Hao Su, Jonathan Krause, Sanjeev Satheesh, Sean Ma, Zhiheng Huang, Andrej Karpathy, Aditya Khosla, Michael S. Bernstein, Alexander C. Berg, and Li Fei-Fei. Imagenet large scale visual recognition challenge. *Int. J. Comput. Vis.*, 115(3):211–252, 2015.

- <span id="page-12-10"></span>Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 4510–4520, 2018.
- <span id="page-12-8"></span>Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. *arXiv preprint arXiv:2311.17950*, 2023.
- <span id="page-12-2"></span>Jiaming Song, Chenlin Meng, and Stefano Ermon. Denoising diffusion implicit models. *arXiv preprint arXiv:2010.02502*, 2020a.
- <span id="page-12-1"></span>Yang Song, Jascha Sohl-Dickstein, Diederik P Kingma, Abhishek Kumar, Stefano Ermon, and Ben Poole. Score-based generative modeling through stochastic differential equations. *arXiv preprint arXiv:2011.13456*, 2020b.
- <span id="page-12-4"></span>Yang Song, Conor Durkan, Iain Murray, and Stefano Ermon. Maximum likelihood training of score-based diffusion models. *Advances in neural information processing systems*, 34:1415–1428, 2021.
- <span id="page-12-16"></span>Duo Su, Junjie Hou, Weizhi Gao, Yingjie Tian, and Bowen Tang. Dˆ 4: Dataset distillation via disentangled diffusion model. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 5809–5818, 2024.
- <span id="page-12-9"></span>Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2024.
- <span id="page-12-11"></span>Mingxing Tan. Efficientnet: Rethinking model scaling for convolutional neural networks. *arXiv preprint arXiv:1905.11946*, 2019.
- <span id="page-12-13"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. CAFE: learning to condense dataset by aligning features. In *CVPR*, pp. 12186–12195. IEEE, 2022a.
- <span id="page-12-14"></span>Yinhuai Wang, Jiwen Yu, and Jian Zhang. Zero-shot image restoration using denoising diffusion null-space model. *arXiv preprint arXiv:2212.00490*, 2022b.
- <span id="page-12-7"></span>Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-12-3"></span>Jiwen Yu, Yinhuai Wang, Chen Zhao, Bernard Ghanem, and Jian Zhang. Freedom: Trainingfree energy-guided conditional diffusion model. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pp. 23174–23184, 2023.
- <span id="page-12-15"></span>Jianhao Yuan, Jie Zhang, Shuyang Sun, Philip Torr, and Bo Zhao. Real-fake: Effective training data synthesis through distribution matching. *arXiv preprint arXiv:2310.10402*, 2023.
- <span id="page-12-5"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, volume 139 of *Proceedings of Machine Learning Research*, pp. 12674–12685. PMLR, 2021a.
- <span id="page-12-6"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *CoRR*, abs/2110.04181, 2021b.
- <span id="page-12-0"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*. OpenReview.net, 2021.
- <span id="page-12-12"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *NeurIPS*, 2022.

# A APPENDIX

<span id="page-13-0"></span>Image /page/13/Figure/2 description: This figure illustrates two diffusion generation processes: Influence-Guided Diffusion Generation and Vanilla Diffusion Generation. The Influence-Guided Diffusion Generation process takes an input zt, passes it through a Denoised Diffusion model to get s(zt, t, εφ), and then adds influence guidance (-∇zt G\_I(ẑ0|t)) and diversity guidance (-∇zt G\_D(zt)) before proceeding to zt-1. The Vanilla Diffusion Generation process is simpler, taking zt, passing it through Denoised Diffusion to get s(zt, t, εφ), and then proceeding to zt-1. Both processes have a conditional step where if t=0, they output z0. The figure also shows a probability distribution p(z) with a 'High-influence region' and a 'Low-influence region', with arrows connecting the generation processes to these regions. The Influence-Guided Diffusion Generation is connected to the High-influence region, and the Vanilla Diffusion Generation is connected to the Low-influence region. Above the Influence-Guided Diffusion Generation box, there are three images of churches, suggesting the influence guidance is related to church imagery. Below the Vanilla Diffusion Generation box, there are three different images of churches, suggesting the low-influence region is also related to church imagery.

Figure 5: An intuitive comparison between our influence-guided diffusion generation and the vanilla diffusion generation frameworks.

## A.1 RELATED WORK

Dataset Distillation. Current dataset distillation methods can be categorized into meta-learning, data-matching, and model-inversion approaches. Meta-learning methods [\(Nguyen et al., 2021;](#page-11-1) [Zhou](#page-12-12) [et al., 2022;](#page-12-12) [Loo et al., 2023\)](#page-11-15) tackle dataset distillation as a nested optimization problem, aiming to minimize generalization errors on original data caused by models trained on distilled data. Datamatching methods involve synthesizing data to replicate specific behaviours from the original dataset, such as latent distributions [\(Zhao & Bilen, 2021b;](#page-12-6) [Wang et al., 2022a\)](#page-12-13), gradients [\(Zhao et al., 2021;](#page-12-0) [Zhao & Bilen, 2021a;](#page-12-5) [Kim et al., 2022\)](#page-11-0), training trajectories [\(Cazenavette et al., 2022;](#page-10-0) [Du et al.,](#page-10-1) [2023;](#page-10-1) [Cui et al., 2023\)](#page-10-2) and surrogate predictions [\(Chen et al., 2023\)](#page-10-9). Model-inversion methods [\(Yin](#page-12-7) [et al., 2024;](#page-12-7) [Shao et al., 2023\)](#page-12-8) are established on data-free knowledge distillation (DFKD) techniques with specific batch normalization statistic alignment. Additionally, recent research [\(Gu et al., 2024\)](#page-10-4) has integrated diffusion models into dataset distillation alongside a fine-tuning scheme, perpendicular to our training-free, sampling-oriented approach.

**Guided-Diffusion Sampling.** Works in this category employ a pre-trained diffusion model as a foundation but modify the sampling method to guide generation with feedback from the guidance function [\(Kawar et al., 2022;](#page-11-16) [Chung et al., 2022;](#page-10-10) [Graikos et al., 2022\)](#page-10-11). Early work employed classifiers as guidance, adjusting gradients during sampling [\(Dhariwal & Nichol, 2021\)](#page-10-12). However, classifiers for noisy images are domain-specific and often unavailable. [\(Wang et al., 2022b\)](#page-12-14) introduced linear operator-based external guidance, generating images in the null space of these operators, though extending to non-linear functions is challenging. Several recent works [\(Gopalakrishnan Nair et al.,](#page-10-7) [2023;](#page-10-7) [Yu et al., 2023;](#page-12-3) [Bansal et al., 2023\)](#page-10-6) explored general guidance functions, modifying the sampling process with gradients of the guidance function on expected denoised images. However, these methods rely on existing metric functions that can concretely measure specific requirements. In contrast, our contribution lies in guiding the model to generate data that meets abstract, trainingenchaining criteria.

## A.2 LIMITATIONS AND FUTURE WORK

The main limitation of our method is the additional time incurred by guidance calculations during the diffusion sampling process. Despite efforts to improve efficiency, our sampling framework takes 5 to 6 $\times$  longer than the vanilla method. For example, raw DDIM generates a 256  $\times$  256 image in  $\sim$ 1.5 seconds, our method takes  $\sim$ 8.2 seconds on a RTX 4090 GPU. This is particularly challenging

for distilling extensive datasets in resource-constrained scenarios. Consequently, improving the generation efficiency of guided diffusion sampling method will be a key focus of our future research.

## A.3 GRADIENT-SIMILARITY-BASED CHECKPOINT SELECTION ALGORITHM

In Algorithm [2,](#page-14-0) we present a detailed implementation of the gradient-similarity-based checkpoint selection algorithm introduced in Section [3.2.](#page-3-2) This algorithm is designed to select representative checkpoints for calculating the influence guidance  $\mathcal{G}_I$ . The core intuition behind this algorithm is that if the gradients at a checkpoint closely resemble those at the previous one, the previous checkpoint can effectively represent the current one.

Complexity analysis. The computational overhead primarily stems from calculating the averaged gradient  $g_t$  w.r.t the model parameters  $\theta$  at each of the E checkpoints collected during training. When using the same cross-entropy loss as in model training, due to its **additive nature**, the computational complexity of calculating  $g_t$  at a given checkpoint  $\theta_t$  is equivalent to the complexity of one epoch of gradient descent, approximately  $O(|\theta| \cdot B \cdot \frac{N}{B} \cdot d)$ , where B is the batch size, N is the number of data instances, and  $d$  is the data dimension. Essentially, without any optimization, the complexity of this algorithm is similar to training a model parameterized by  $\theta$  for E epochs. In practice, instead of loading the entire dataset into the dataloader to compute the average gradient  $\bar{\nabla}_{\theta} \ell_c$  for each class, we first load all images from a class folder into CPU memory and slice them into GPU memory for gradient computation and accumulation. Empirically, this approach further reduces the runtime of the filtering algorithm. Additionally, the cross-architecture evaluation discussed in Section [4.3](#page-6-2) and Table [4](#page-7-1) demonstrates that using models with simpler architectures (e.g., ConvNet) as surrogates can provide more effective influence guidance, further reducing the time overhead for selecting representative checkpoints.

Algorithm 2: Filtering Algorithm for Influence Guidance

<span id="page-14-0"></span>**Input:** Original dataset  $\mathcal{T}$ , Initial checkpoint  $\boldsymbol{\theta}_0^{\mathcal{T}}$ , Threshold  $\delta$ **Output:** Retained checkpoints list  $\mathcal{R}$ 1 Initialize:  $\mathcal{R} \leftarrow \theta_0^{\mathcal{T}};$ 2 Compute  $\mathbb{E}_c[\bar{\nabla}_{\bm{\theta}} \ell_c(\mathcal{X}_c; \bm{\theta}_0^{\mathcal{T}})]$  as reference gradient  $\bm{g}_{\text{ref}}$ ; 3 for  $t = 1$  to  $E$  do 4 Compute averaged gradient  $\mathbf{g}_t = \mathbb{E}_c[\bar{\nabla}_{\theta} \ell_c(\mathcal{X}_c; \theta_t^{\mathcal{T}})];$  $\mathsf{S}$  Calculate cosine similarity  $s = \frac{g_t \cdot \hat{g}_{\text{ref}}}{\|g_t\| \|g_{\text{ref}}\|};$ 6 if  $s < \delta$  then  $\tau \quad | \quad \mid \quad \mathcal{R} \leftarrow \mathcal{R} \cup \{\boldsymbol{\theta}_t^{\mathcal{T}}\};$  $\left\vert \right.$  Update reference gradient  $g_{\text{ref}} = g_t$ ; 9 return  $\mathcal{R}$ ;

## A.4 ADDITIONAL PERFORMANCE EVALUATION ON FOOD-101 DATASET

We evaluate the performance of our IGD methods on Food-101 [\(Bossard et al., 2014\)](#page-10-13) dataset to provide further test on distilling other large, high-resolution datasets. Food-101 is a challenging dataset that includes 101 food categories, totaling 101,000 images, with each category containing 250 manually reviewed test images and 750 training images. All images are scaled to a maximum side length of 256 pixels. Results detailed in Table [7](#page-15-0) show that our IGD methods achieve superior performances over all IPC scenarios. Furthermore, applying our method to baseline methods, including DiT and Minimax, results in noticeable performance enhancements, with average improvements of 3.8% and 3.5%, respectively. In contrast, the Minimax method yields only a marginal average improvement of 0.8% to DiT. These findings align with evaluations conducted on ImageNet, indicating robust scalability across large, high-resolution datasets.

## A.5 ADDITIONAL PERFORMANCE EVALUATION ON CIFAR DATASETS

The results presented in Tables [1](#page-5-0) and [2](#page-6-0) demonstrate the outstanding performance of our method in distilling targeted high-resolution datasets. In this section, we further investigate the generalizability of our method to smaller datasets. Specifically, we compare the performance of our framework with

<span id="page-15-0"></span>Table 7: Food-101: performance comparison with state-of-the-art pixel-level distillation methods, pretrained DiT and Minimax-tuned DiT models with vanilla generation. The results are obtained on ResNetAP-10 at different IPCs.

| IPC | Random                    | DM                        | DiT                       | <span style="text-decoration: underline;">DiT-IGD</span> | Minimax                   | <span style="text-decoration: underline;">Minimax-IGD</span> | Full                      |
|-----|---------------------------|---------------------------|---------------------------|----------------------------------------------------------|---------------------------|--------------------------------------------------------------|---------------------------|
| 10  | $16.2 	ext{ 	extpm } 0.5$ | $18.5 	ext{ 	extpm } 0.8$ | $23.9 	ext{ 	extpm } 1.0$ | $27.2 	ext{ 	extpm } 0.9$                                | $24.8 	ext{ 	extpm } 0.9$ | <b><math>28.3 	ext{ 	extpm } 0.9</math></b>                  | $78.6 	ext{ 	extpm } 0.4$ |
| 50  | $36.9 	ext{ 	extpm } 0.3$ | $37.8 	ext{ 	extpm } 0.4$ | $40.8 	ext{ 	extpm } 0.7$ | <b><math>45.2 	ext{ 	extpm } 0.7</math></b>              | $41.6 	ext{ 	extpm } 1.0$ | $44.8 	ext{ 	extpm } 0.7$                                    |                           |
| 100 | $46.8 	ext{ 	extpm } 0.3$ | $44.8 	ext{ 	extpm } 0.3$ | $45.9 	ext{ 	extpm } 0.5$ | $49.7 	ext{ 	extpm } 0.3$                                | $46.5 	ext{ 	extpm } 0.5$ | <b><math>50.3 	ext{ 	extpm } 0.6</math></b>                  |                           |

<span id="page-15-1"></span>Table 8: CIFAR-10 & CIFAR-100: Perfomrance comparison with two low-resolution-orientied methods DM and DATM and two high-resolution-oriented methods  $SRe<sup>2</sup>L$  and RDED.

|           | CIFAR-10       |                |                | CIFAR-100      |                |                |
|-----------|----------------|----------------|----------------|----------------|----------------|----------------|
|           | IPC            | 50             | 500            | 1000           | 10             | 50             |
| Ratio (%) | 1.0            | 10.0           | 20.0           | 2.0            | 10.0           | 20.0           |
| DM        | $63.1 \pm 0.4$ | $74.3 \pm 2$   | $79.2 \pm 0.2$ | $29.7 \pm 0.3$ | $43.6 \pm 0.4$ | $47.1 \pm 0.4$ |
| DATM      | $76.1 \pm 0.3$ | $83.5 \pm 0.2$ | $85.5 \pm 0.4$ | $47.2 \pm 0.4$ | $55.0 \pm 0.2$ | $57.5 \pm 0.2$ |
| SRe2L     | $43.2 \pm 0.3$ | $55.3 \pm 0.4$ | $57.1 \pm 0.4$ | $24.5 \pm 0.4$ | $45.2 \pm 0.3$ | $46.6 \pm 0.5$ |
| RDED      | $68.4 \pm 0.2$ | $78.1 \pm 0.4$ | $79.8 \pm 0.4$ | $46.4 \pm 0.3$ | $51.5 \pm 0.3$ | $52.6 \pm 0.4$ |
| DiT-IGD   | $66.8 \pm 0.5$ | $82.6 \pm 0.6$ | $84.6 \pm 0.5$ | $45.8 \pm 0.5$ | $53.9 \pm 0.6$ | $55.9 \pm 0.4$ |

two state-of-the-art high-resolution-oriented methods,  $SRe^{2}L$  [Yin et al.](#page-12-7) [\(2024\)](#page-12-7) and RDED [Sun et al.](#page-12-9) [\(2024\)](#page-12-9), as well as two low-resolution-oriented methods, DM [Zhao & Bilen](#page-12-6) [\(2021b\)](#page-12-6) and DATM [Guo](#page-10-14) [et al.](#page-10-14) [\(2024\)](#page-10-14), on CIFAR-10 and CIFAR-100. Notably, DATM, a recent strong baseline, has been shown to achieve lossless distillation on small-scale datasets such as CIFAR. Table [8](#page-15-1) presents the comparison results on ConvNet under varying IPC values. Our experimental findings indicate that our method consistently outperforms both  $SRe<sup>2</sup>L$  and RDED across most scenarios. Remarkably, our approach achieves nearly lossless performance, comparable to that of DATM, even at a 20% compression ratio. These results, combined with the exceptional performance observed on larger datasets like ImageNet, suggest that our method is a versatile and unified solution that excels in both low-resolution and high-resolution settings.

## A.6 COMPARISON WITH OTHER DIFFUSION-BASED METHODS ON IMAGENET-1K

We compare our method with two other synthetic dataset generation methods, namely TDSDM [\(Yuan](#page-12-15) [et al., 2023\)](#page-12-15) and  $D^4M$  [\(Su et al., 2024\)](#page-12-16), which leverage pre-trained diffusion models. Although TDSDM was not initially designed for dataset distillation tasks, its goal is to enhance the training efficacy of synthetic data generated by diffusion models. Along with our baseline method, Minimax, these three approaches utilize distribution-matching-like objectives to fine-tune diffusion models and improve the performance of synthetic data in training. The results presented in Table [9](#page-15-2) are evaluated on ImageNet-1K under the evaluation protocol of RDED. Our findings demonstrate that our guided-diffusion method consistently outperforms the others, reinforcing the importance of introducing informative guidance when applying diffusion models in dataset distillation.

<span id="page-15-2"></span>Table 9: Performance comparison over ResNet-18 with state-of-the-art diffusion-finetuning methods

| Dataset     | IPC | TDSDM    | D4M      | Minimax  | DiT-IGD  | Minimax-IGD |
|-------------|-----|----------|----------|----------|----------|-------------|
| ImageNet-1K | 10  | 44.5±0.4 | 27.9±0.7 | 44.3±0.5 | 45.5±0.5 | 46.2±0.6    |
| ImageNet-1K | 50  | 59.4±0.3 | 55.2±0.3 | 58.6±0.3 | 59.8±0.3 | 60.3±0.4    |

### A.7 EVALUATING THE ROBUSTNESS OF INFLUENCE-GUIDED DIFFUSION WITH DPM SOLVER

In the practical implementation of our IGD methods, we propose using DDIM with 50 denoising steps. To assess the generalizability of our proposed Influence Guidance and Deviation Guidance across different diffusion solvers, we additionally explore the use of the DPM solver [Lu et al.](#page-11-17) [\(2022\)](#page-11-17) as a replacement for the DDIM solver in outputting  $s(z_t, t, \epsilon_{\phi})$  in Equation [\(9\)](#page-4-2). The DPM solver is a fast, high-order solver for diffusion ODEs with a convergence order guarantee. We employ the second-order DPM solver with 20 denoising steps by default. Accordingly, we adjust the guided range for diffusion guidance to [12, 18]. This adjustment results in a significant 50% reduction in average sampling time, from 8.2 seconds to 4.3 seconds on an RTX 4090. Table [10](#page-16-0) compares the average performance of DDIM with 50 steps and the DPM solver with 20 steps for distilling ImageNette and ImageWoof with IPC=50. The results indicate that using the DPM solver with fewer denoising steps does not lead to a significant degradation in performance, and in some scenarios, it even yields slight improvements. This further validates the robustness of our influence-guided sampling method.

<span id="page-16-0"></span>Table 10: Performance of ResNet-18 using the DDIM-50 and DPM-20 solvers for diffusion generation.

|                                                                                       | Solver                                                                         |  | DiT DiT-IGD Minimax Minimax-IGD  |
|---------------------------------------------------------------------------------------|--------------------------------------------------------------------------------|--|----------------------------------|
| ImageNette                                                                            | DDIM-50 75.2±0.9 81.0±0.7 78.1±0.6<br>DPM-20 73.5±0.8 <b>82.0±0.5</b> 76.6±0.4 |  | $82.0 \pm 0.3$<br>$80.6 \pm 0.5$ |
| ImageWoof DDIM-50   57.4±0.7 62.0±1.1 60.5±0.5<br>DPM-20   57.8±0.6 64.0±1.0 60.3±0.6 |                                                                                |  | $65.4 \pm 1.8$<br>$64.5 \pm 1.3$ |

### A.8 DISTRIBUTION DIVERSITY AND COVERAGE ANALYSIS

In Figure [3,](#page-8-1) we provided t-SNE visualizations comparing the distributions of data generated by two baseline methods (DiT and Minimax) and our IGD-based methods with IPC=100. The figure shows that integrating IGD enhances diversity and alignment with the original dataset, supported by lower Wasserstein distances to the original dataset. In this section, we further compare the FID scores and coverage of surrogate datasets (IPC=100) generated for ImageWoof by different methods. Here, coverage metric was assessed based on whether each original data point had a nearest neighbor in the surrogate dataset within a given threshold (e.g., 300 in the Inception V3 latent space). For fairness, we excluded data selected by the Random method from the original dataset during coverage calculation. From the results shown in Table [11](#page-16-1) also demonstrate a clear diversity improment achieved by our IGD method to vanilla DiT. However we also observed that although the randomly selected dataset has the lowest FID and highest coverage, its performance was the worst. Similarly, while Minimax-IGD has worse FID and coverage than DiT-IGD, it performed better. These findings suggest that our diversity-constraint influence-guided objective is a more effective measure for DD than relying solely on distribution alignment.

<span id="page-16-1"></span>Table 11: FID and distribution coverage comparison among different methods.

| Metrics      | DiT  | DiT-IGD | Minimax | Minimax-IGD | Random      |
|--------------|------|---------|---------|-------------|-------------|
| FID          | 81.1 | 75.9    | 80.1    | 76.4        | <b>54.1</b> |
| Coverage (%) | 65.4 | 68.1    | 66.5    | 67.2        | <b>72.1</b> |
| Accuracy (%) | 62.3 | 70.6    | 67.4    | <b>72.1</b> | 63.6        |

### A.9 PARAMETER ANALYSIS

In our IGD sampling framework, two critical hyper-parameters are  $k$ , which controls the magnitude of influence guidance, and  $\gamma_t$ , which controls the magnitude of deviation guidance. In Figure [6,](#page-17-1) we examine the impact of these scaling factors on DiT-IGD and Minimax-IGD using the ImageNette dataset as an instance. For DiT-IGD, variations in both k and  $\gamma_t$  significantly influence performance.

Increasing the values of these parameters enhances performance, highlighting the importance of influence and dataset diversity for model training. However, setting  $k$  too high results in a notable performance drop. As discussed in Section [3.2,](#page-3-2) this is likely due to excessive overfitting to the surrogate data with distorted content. In contrast, for Minimax-IGD, increasing  $\gamma_t$  contributes marginally to performance improvement. This is because Minimax-IGD inherently focuses on increasing diversity as a core aspect of its fine-tuning-based scheme. However, increasing influence guidance by enlarging  $k$  significantly improves its results. Despite this improvement, a similar performance drop is observed when k becomes excessively large. These findings underscore the necessity of carefully tuning k and  $\gamma_t$  to optimize the effectiveness of our IGD sampling framework, ensuring balanced influence and diversity without overfitting.

<span id="page-17-1"></span>Image /page/17/Figure/2 description: This figure contains four line graphs, labeled (a), (b), (c), and (d). Graphs (a) and (c) are titled "DiT-IGD" and "Minimax-IGD" respectively, and plot "Validation Accuracy (%)" on the y-axis against "k of Influence Guidance" on the x-axis. Graphs (b) and (d) are also titled "DiT-IGD" and "Minimax-IGD" respectively, and plot "Validation Accuracy (%)" on the y-axis against "γ of Deviation Guidance" on the x-axis. All graphs show three lines representing "IPC10" (green), "IPC50" (blue), and "IPC100" (red). In graph (a), "IPC10" peaks at approximately 68% at k=5, while "IPC50" peaks at approximately 82% at k=7, and "IPC100" peaks at approximately 85% at k=5. In graph (b), "IPC10" peaks at approximately 68% at γ=50, "IPC50" peaks at approximately 82% at γ=100, and "IPC100" peaks at approximately 85% at γ=100. In graph (c), "IPC10" peaks at approximately 68% at k=5, "IPC50" peaks at approximately 83% at k=7, and "IPC100" peaks at approximately 86% at k=7. In graph (d), "IPC10" peaks at approximately 68% at γ=10, "IPC50" peaks at approximately 83% at γ=15, and "IPC100" peaks at approximately 86% at γ=15.

Figure 6: Hyper-parameter analysis on (a)  $\&$  (c) the scaling factor k of influence guidance, and (b)  $\&$ (d) the scaling factor  $\gamma_t$  of deviation guidance for DiT-IGD and Minimax-IGD.

<span id="page-17-0"></span>

## A.10 HYPERPARAMETER SETUP AND GUIDELINES

In Table [12,](#page-18-0) we provide a detailed hyperparameter configuration for k and  $\gamma_t$  in Equation [\(7\)](#page-3-1) to replicate the results obtained across ImageNette, ImageWoof, and ImageNet-1K datasets. Despite incorporating an adaptive scaling factor based on the ratio between the denoised signal magnitude from diffusion and the guided signal from the influence guidance  $G_I$ , manual specification of the scale factor k remains essential to forestall unexpected overfitting resulting from the influence guidance. Drawing from insights gleaned from our ablation study, as illustrated in Figure [6,](#page-17-1) we recommend setting the value range of k within  $[1, 50]$  for scaling our method in distillation tasks involving other ImageNet subsets. Similarly, we suggest a grid-search range for the scaling factor  $\gamma_t$  of the deviation guidance as [10, 200]. Particularly for scenarios with small IPC, we advocate for starting from a relatively smaller value of  $k$  to hold the representatives of generated data.

### A.11 MORE VISUALIZATION COMPARISON OF SYNTHETIC DATA.

Here, we provide an additional visual comparison between images generated by two backbone models with vanilla DDIM sampling: the raw DiT and the Minimax-tuned DiT, and with our IGD-sampling framework: DiT-IGD and Minimax IGD. All synthetic data were generated for the ImageWoof and ImageNette datasets.

|       | Parameter  | DIT-IGD |       |        | Minimax-IGD |       |        |
|-------|------------|---------|-------|--------|-------------|-------|--------|
|       |            | IPC10   | IPC50 | IPC100 | IPC10       | IPC50 | IPC100 |
| Nette | $k$        | 5       | 5     | 5      | 15          | 15    | 15     |
|       | $\gamma_t$ | 50      | 120   | 120    | 10          | 10    | 10     |
| woof  | $k$        | 5       | 5     | 5      | 10          | 10    | 10     |
|       | $\gamma_t$ | 50      | 120   | 120    | 50          | 100   | 100    |
| 1K    | $k$        | 5       | 5     | 5      | 10          | 10    | 10     |
|       | $\gamma_t$ | 120     | 120   | 120    | 100         | 100   | 100    |

<span id="page-18-0"></span>Table 12: Detailed setup of hyperparameters k and  $\gamma_t$  in Equation [\(7\)](#page-3-1) for reproducing the results reported in Table [1](#page-5-0) & [2.](#page-6-0)

Image /page/18/Picture/3 description: This image is a grid of dog photos, organized by breed and by two different methods, DiT and DiT-IGD. The breeds listed are Rhodesian Ridgeback, Border Terrier, Golden Retriever, Shih Tzu, English Foxhound, Australian Terrier, Beagle, Dingo, Samoyed, and Old English Sheepdog. Each breed has four photos under the DiT column and four photos under the DiT-IGD column, for a total of eight photos per breed. The photos show dogs in various poses and settings.

Figure 7: Visualizaiton comparison between raw DiT and DiT-IGD on ImageWoof.

Image /page/19/Picture/1 description: This image displays a comparison of dog breed image generation results between two methods: Minimax and Minimax-IGD. The image is organized into a grid with dog breed names listed on the left side. Each row corresponds to a specific dog breed, and each column within the Minimax and Minimax-IGD sections shows different generated images for that breed. The breeds shown are Rhodesian Ridgeback, Border Terrier, Golden Retriever, Shih Tzu, English Foxhound, Australian Terrier, Beagle, Dingo, Samoyed, and Old English Sheepdog. The Minimax section has five columns of images, and the Minimax-IGD section also has five columns of images, separated by a dashed vertical line. The overall figure is titled 'Figure 8: Visualization comparison between Minimax and Minimax-IGD on ImageWoof'.

Figure 8: Visualizaiton comparison between Minimax and Minimax-IGD on ImageWoof.

Image /page/20/Picture/1 description: The image is a grid of images comparing two different methods, DiT and DiT-IGD. Each row represents a different category of object, and each column within a method shows a different generated image for that category. The categories are Parachute, Gas Pump, Tench, Cassette Player, French Horn, English Springer, Chain Saw, Church, Golf Ball, and Garbage Truck. The DiT method is on the left, and the DiT-IGD method is on the right, separated by a dashed line. The images show variations in the generated objects within each category and between the two methods.

Figure 9: Visualizaiton comparison between raw DiT and DiT-IGD on ImageNette.

Image /page/21/Picture/1 description: This is a grid of images comparing Minimax and Minimax-IGD. The grid is organized into rows representing different categories: Parachute, Gas Pump, Tench, Cassette Player, French Horn, English Springer, Chain Saw, Church, Golf Ball, and Garbage Truck. Each category has multiple images, with the left side of the grid showing Minimax results and the right side showing Minimax-IGD results. The images within each category show variations of the object or scene, likely representing different generated or captured examples.

Figure 10: Visualizaiton comparison between Minimax and Minimax-IGD on ImageNette.
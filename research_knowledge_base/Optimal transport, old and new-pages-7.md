I shall now discuss a time-dependent version of optimal transport leading to a continuous displacement of measures. There are two main motivations for that extension:

- a time-dependent model gives a more complete description of the transport;
- the richer mathematical structure will be useful later on.

As in the previous chapter I shall assume that the initial and final probability measures are defined on the same Polish space  $(\mathcal{X}, d)$ . The main additional structure assumption is that the cost is associated with an action, which is a way to measure the cost of displacement along a continuous curve, defined on a given time-interval, say [0, 1]. So the cost function between an initial point  $x$  and a final point  $y$  is obtained by minimizing the action among paths that go from  $x$  to  $y$ :

$$
c(x, y) = \inf \Big\{ \mathcal{A}(\gamma); \quad \gamma_0 = x, \ \gamma_1 = y; \quad \gamma \in \mathcal{C} \Big\}.
$$
 (7.1)

Here  $\mathcal C$  is a certain class of continuous curves, to be specified in each particular case of interest, on which the action functional  $A$  is defined.

Of course, Assumption (7.1) is meaningless unless one requires some specific structure on the action functional (otherwise, just choose  $\mathcal{A}(\gamma) = c(\gamma_0, \gamma_1)...$ . A good notion of action should provide a recipe for choosing optimal paths, and in particular a recipe to interpolate between points in  $\mathcal{X}$ . It will turn out that under soft assumptions, this interpolation recipe between points can be "lifted" to an interpolation recipe between probability measures. This will provide a time-dependent notion of optimal transport, that will be called displacement interpolation (by opposition to the standard linear interpolation between probability measures).

This is a key chapter in this course, and I have worked hard to attain a high level of generality, at the price of somewhat lengthy arguments. So the reader should not hesitate to skip proofs at first reading, concentrating on statements and explanations. The main result in this chapter is Theorem 7.21.

## Deterministic interpolation via action-minimizing curves

To better understand what an action functional should be, let us start with some examples and informal discussions. Consider a model where the unknown is the position of a given physical system in some position space, say a Riemannnian manifold  $M$ . (See the Appendix for reminders about Riemannian geometry if needed.) We learn from classical physics that in the absence of a potential, the action is the integral over time of the (instantaneous) kinetic energy:

$$
\mathcal{A}(\gamma) = \int_0^1 \frac{|\dot{\gamma}_t|^2}{2} dt,
$$

where  $\dot{\gamma}_t$  stands for the velocity (or time-derivative) of the curve  $\gamma$  at time t. More generally, an action is classically given by the time-integral of a Lagrangian along the path:

$$
\mathcal{A}(\gamma) = \int_0^1 L(\gamma_t, \dot{\gamma}_t, t) dt.
$$
 (7.2)

Here L is defined on  $TM \times [0,1]$ , where the smooth manifold M is the position space and the tangent bundle TM is the phase space, which is the space of all possible positions and velocities. It is natural to work in TM because one often deals with second-order differential equations on M (such as Newton's equations), which transform themselves into first-order equations on  $TM$ . Typically L would take the form

$$
L(x, v, t) = \frac{|v|^2}{2} - V(x)
$$
\n(7.3)

where  $V$  is a potential; but much more complicated forms are admissible. When  $V$  is continuously differentiable, it is a simple particular case of the formula of first variation (recalled in the Appendix) that minimizers of (7.3), with given endpoints, satisfy Newton's equation

$$
\frac{d^2x}{dt^2} = -\nabla V(x). \tag{7.4}
$$

To make sure that  $\mathcal{A}(\gamma)$  is well-defined, it is natural to assume that the path  $\gamma$  is continuously differentiable, or piecewise continuously differentiable, or at least almost everywhere differentiable as a function of  $t$ . A classical and general setting is that of **absolutely continuous** curves: By definition, if  $(X, d)$  is a metric space, a continuous curve  $\gamma : [0,1] \to \mathcal{X}$  is said to be absolutely continuous if there exists a function  $\ell \in L^1([0,1]; dt)$  such that for all intermediate times  $t_0 < t_1$  in  $[0, 1],$ 

$$
d(\gamma_{t_0}, \gamma_{t_1}) \le \int_{t_0}^{t_1} \ell(t) dt.
$$
 (7.5)

More generally, it is said to be absolutely continuous of order  $p$  if formula (7.5) holds with some  $\ell \in L^p([0,1]; dt)$ .

If  $\gamma$  is absolutely continuous, then the function  $t \mapsto d(\gamma_{t_0}, \gamma_t)$  is differentiable almost everywhere, and its derivative is integrable. But the converse is false: for instance, if  $\gamma$  is the "Devil's staircase", encountered in measure theory textbooks (a nonconstant function whose distributional derivative is concentrated on the Cantor set in  $[0, 1]$ ), then  $\gamma$  is differentiable almost everywhere, and  $\dot{\gamma}(t) = 0$  for almost every t, even though  $\gamma$  is not constant! This motivates the "integral" definition of absolute continuity based on formula (7.5).

If  $\mathcal{X}$  is  $\mathbb{R}^n$ , or a smooth differentiable manifold, then absolutely continuous paths are differentiable for Lebesgue-almost all  $t \in [0, 1]$ ; in physical words, the velocity is well-defined for almost all times.

Before going further, here are some simple and important examples. For all of them, the class of admissible curves is the space of absolutely continuous curves.

**Example 7.1.** In  $\mathcal{X} = \mathbb{R}^n$ , choose  $L(x, v, t) = |v|$  (Euclidean norm of the velocity). Then the action is just the length functional, while the cost  $c(x,y) = |x - y|$  is the Euclidean distance. Minimizing curves are straight lines, with arbitrary parametrization:  $\gamma_t = \gamma_0 + s(t)(\gamma_1 - \gamma_0)$ , where  $s : [0,1] \rightarrow [0,1]$  is nondecreasing and absolutely continuous.

**Example 7.2.** In  $\mathcal{X} = \mathbb{R}^n$  again, choose  $L(x, v, t) = c(v)$ , where c is strictly convex. By Jensen's inequality,

$$
c(\gamma_1 - \gamma_0) = c \left( \int_0^1 \dot{\gamma}_t dt \right) \le \int_0^1 c(\dot{\gamma}_t) dt,
$$

and this is an equality if and only if  $\dot{\gamma_t}$  is constant. Therefore actionminimizers are straight lines with *constant velocity:*  $\gamma_t = \gamma_0 + t(\gamma_1 - \gamma_0)$ . Then, of course,

$$
c(x, y) = c(y - x).
$$

Remark 7.3. This example shows that very different Lagrangians can have the same minimizing curves.

**Example 7.4.** Let  $\mathcal{X} = M$  be a smooth Riemannian manifold, TM its tangent bundle, and  $L(x, v, t) = |v|^p$ ,  $p \ge 1$ . Then the cost function is  $d(x, y)^p$ , where d is the geodesic distance on M. There are two quite different cases:

- If  $p > 1$ , minimizing curves are defined by the equation  $\ddot{\gamma}_t = 0$ (zero acceleration), to be understood as  $\left(\frac{d}{dt}\right)$  $\dot{\gamma}_t = 0$ , where  $\left(\frac{d}{dt}\right)$ stands for the covariant derivative along the path  $\gamma$  (once again, see the reminders in the Appendix if necessary). Such curves have constant speed  $((d/dt)|\dot{\gamma}_t| = 0)$ , and are called **minimizing, constant**speed geodesics, or simply geodesics.
- If  $p = 1$ , minimizing curves are geodesic curves parametrized in an arbitrary way.

**Example 7.5.** Again let  $\mathcal{X} = M$  be a smooth Riemannian manifold, and now consider a general Lagrangian  $L(x, v, t)$ , assumed to be *strictly*  $convex$  in the velocity variable  $v$ . The characterization and study of extremal curves for such Lagrangians, under various regularity assumptions, is one of the most classical topics in the calculus of variations. Here are some of the basic — which does not mean trivial — results in the field. Throughout the sequel, the Lagrangian  $L$  is a  $C<sup>1</sup>$  function defined on  $TM \times [0,1]$ .

• By the first variation formula (a proof of which is sketched in the Appendix), minimizing curves satisfy the Euler–Lagrange equation

$$
\frac{d}{dt}\Big[(\nabla_v L)(\gamma_t, \dot{\gamma}_t, t)\Big] = (\nabla_x L)(\gamma_t, \dot{\gamma}_t, t),\tag{7.6}
$$

which generalizes (7.4). At least this equation should be satisfied for minimizing curves that are sufficiently smooth, say piecewise  $C^1$ .

If there exists  $K, C > 0$  such that

$$
L(x, v, t) \ge K|v| - C,
$$

then the action of a curve  $\gamma$  is bounded below by  $K \mathcal{L}(\gamma) - C$ , where  $\mathcal{L}$ is the length; this implies that all action-minimizing curves starting from a given compact  $K_0$  and ending in a given compact  $K_1$  stay within a bounded region.

- If minimizing curves depend smoothly on their position and velocity at some time, then there is also a bound on the velocities along minimizers that join  $K_0$  to  $K_1$ . Indeed, there is a bound on  $\int_0^1 L(x,v,t) dt$ ; so there is a bound on  $L(x,v,t)$  for some t; so there is a bound on the velocity at some time, and then this bound is propagated in time.
- Assume that  $L$  is strictly convex and superlinear in the velocity variable, in the following sense:

$$
\forall (x, t) \quad \begin{cases} v \longmapsto L(x, v, t) & \text{is convex,} \\ \frac{L(x, v, t)}{|v|} \xrightarrow[|v| \to \infty]{} + \infty. \end{cases} \tag{7.7}
$$

Then  $v \mapsto \nabla_v L$  is invertible, and (7.6) can be rewritten as a differential equation on the new unknown  $\nabla_v L(\gamma, \dot{\gamma}, t)$ .

- If in addition L is  $C^2$  and the strict inequality  $\nabla_v^2 L > 0$  holds (more rigorously,  $\nabla_v^2 L(x, \cdot, t) \ge K(x) g_x$  for all x, where g is the metric and  $K(x) > 0$ , then the new equation (where x and  $p = \nabla_v L(x, v, t)$  are the unknowns) has locally Lipschitz coefficients, and the Cauchy– Lipschitz theorem can be applied to guarantee the unique local existence of Lipschitz continuous solutions to (7.6). Under the same assumptions on  $L$ , at least if  $L$  does not depend on  $t$ , one can show directly that minimizers are of class at least  $C<sup>1</sup>$ , and therefore satisfy (7.6). Conversely, solutions of (7.6) are locally (in time) minimizers of the action.
- Finally, the convexity of  $L$  makes it possible to define its Legendre transform (again, with respect to the velocity variable):

$$
H(x, p, t) := \sup_{v \in T_x M} \Big( p \cdot v - L(x, v, t) \Big),
$$

which is called the **Hamiltonian**; then one can recast  $(7.6)$  in terms of a Hamiltonian system, and access to the rich mathematical world

of Hamiltonian dynamics. As soon as  $L$  is strictly convex superlinear, the Legendre transform  $(x, v) \rightarrow (x, \nabla_v L(x, v, t))$  is a homeomorphism, so assumptions about  $(x, v)$  can be re-expressed in terms of the new variables  $(x, p = \nabla_v L(x, v, t)).$ 

• If L does not depend on t, then  $H(x, \nabla_v L(x, v))$  is constant along minimizing curves  $(x, v) = (\gamma_t, \dot{\gamma}_t)$ ; if L does depend on t, then  $(d/dt)H(x, \nabla_v L(x,v)) = (\partial_t H)(x, \nabla_v L(x,v)).$ 

Some of the above-mentioned assumptions will come back often in the sequel, so I shall summarize the most interesting ones in the following definition:

### Definition 7.6 (Classical conditions on a Lagrangian function).

Let M be a smooth, complete Riemannian manifold, and  $L(x, v, t)$  a Lagrangian on  $TM \times [0, 1]$ . In this course, it is said that L satisfies the classical conditions if

(a) L is  $C^1$  in all variables;

(b)  $L$  is a strictly convex superlinear function of v, in the sense of (7.7);

(c) There are constants  $K, C > 0$  such that for all  $(x, v, t) \in TM \times$  $[0, 1], L(x, v, t) \geq K|v| - C;$ 

(d) There is a well-defined locally Lipschitz flow associated to the Euler–Lagrange equation, or more rigorously to the minimization problem; that is, there is a locally Lipschitz map  $(x_0, v_0, t_0; t) \rightarrow \phi_t(x_0, v_0, t_0)$ on  $TM \times [0,1] \times [0,1]$ , with values in TM, such that each actionminimizing curve  $\gamma : [0,1] \to M$  belongs to  $C^1([0,1];M)$  and satisfies  $(\gamma(t), \dot{\gamma}(t)) = \phi_t(\gamma(t_0), \dot{\gamma}(t_0), t_0).$ 

**Remark 7.7.** Assumption (d) above is automatically satisfied if  $L$  is of class  $C^2$ ,  $\nabla_v^2 L > 0$  everywhere and L does not depend on t.

This looks general enough, however there are interesting cases where  $\mathcal X$  does not have enough differentiable structure for the velocity vector to be well-defined (tangent spaces might not exist, for lack of smoothness). In such a case, it is still possible to define the speed along the curve:

$$
|\dot{\gamma}_t| := \limsup_{\varepsilon \to 0} \frac{d(\gamma_t, \gamma_{t+\varepsilon})}{|\varepsilon|}.
$$
 (7.8)

This generalizes the natural notion of speed, which is the norm of the velocity vector. Thus it makes perfect sense to write a Lagrangian of the

form  $L(x, |v|, t)$  in a general metric space X; here L might be essentially any measurable function on  $\mathcal{X} \times \mathbb{R}_+ \times [0,1]$ . (To ensure that  $\int_0^1 L dt$ makes sense in  $\mathbb{R} \cup \{+\infty\}$ , it is natural to assume that L is bounded below.)

**Example 7.8.** Let  $(\mathcal{X}, d)$  be a metric space. Define the **length** of an absolutely continuous curve by the formula

$$
\mathcal{L}(\gamma) = \int_0^1 |\dot{\gamma}_t| dt. \tag{7.9}
$$

Then minimizing curves are called geodesics. They may have variable speed, but, just as on a Riemannian manifold, one can always *reparametrize* them (that is, replace  $\gamma$  by  $\tilde{\gamma}$  where  $\tilde{\gamma}_t = \gamma_{s(t)}$ , with s continuous increasing) in such a way that they have constant speed. In that case  $d(\gamma_s, \gamma_t) = |t - s| \mathcal{L}(\gamma)$  for all  $s, t \in [0, 1].$ 

Example 7.9. Again let  $(\mathcal{X}, d)$  be a metric space, but now consider the action

$$
\mathcal{A}(\gamma) = \int_0^1 c(|\dot{\gamma}_t|) dt,
$$

where c is strictly convex and strictly increasing (say  $c(|v|) = |v|^p$ ,  $p > 1$ ). Then,

$$
c\big(d(\gamma_0,\gamma_1)\big) \leq c(\mathcal{L}(\gamma)) = c\left(\int_0^1 |\dot{\gamma}_t| dt\right) \leq \int_0^1 c(|\dot{\gamma}_t|) dt,
$$

with equality in both inequalities if and only if  $\gamma$  is a constant-speed, minimizing geodesic. Thus  $c(x, y) = c(d(x, y))$  and minimizing curves are also geodesics, but with constant speed. Note that the distance can be recovered from the cost function, just by inverting c. As an illustration, if  $p > 1$ , and  $c(|v|) = |v|^p$ , then

$$
d(x, y) = \inf \left\{ \int_0^1 |\dot{\gamma}_t|^p dt; \quad \gamma_0 = x, \quad \gamma_1 = y \right\}^{\frac{1}{p}}.
$$

In a given metric space, geodesics might not always exist, and it can even be the case that nonconstant continuous curves do not exist (think of a discrete space). So to continue the discussion we shall have to impose appropriate assumptions on our metric space and our cost function.

Here comes an important observation. When one wants to compute "in real life" the length of a curve, one does not use formula (7.9), but rather subdivides the curve into very small pieces, and approximates the length of each small piece by the distance between its endpoints. The finer the subdivision, the greater the measured approximate length (this is a consequence of the triangle inequality). So by taking finer and finer subdivisions we get an increasing family of measurements, whose upper bound may be taken as the measured length. This is actually an alternative definition of the length, which agrees with (7.9) for absolutely continuous curves, but does not require any further regularity assumption than plain continuity:

$$
\mathcal{L}(\gamma) = \sup_{N \in \mathbb{N}} \sup_{0 = t_0 < t_1 < \ldots < t_N = 1} \left[ d(\gamma_{t_0}, \gamma_{t_1}) + \cdots + d(\gamma_{t_{N-1}}, \gamma_{t_N}) \right]. \tag{7.10}
$$

Then one can define a **length space** as a metric space  $(\mathcal{X}, d)$  in which, for any two  $x, y \in \mathcal{X}$ ,

$$
d(x,y) = \inf_{\gamma \in C([0,1];\mathcal{X})} \Big\{ \mathcal{L}(\gamma); \quad \gamma_0 = x, \ \gamma_1 = y \Big\}.
$$
 (7.11)

If in addition  $X$  is complete and locally compact, then the infimum is a minimum, in which case the space is said to be a strictly intrinsic length space, or geodesic space. (By abuse of language, one often says just "length space" for "strictly intrinsic length space".) Such spaces play an important role in modern nonsmooth geometry.

Formulas (7.10) and (7.11) show an intimate link between the length and the distance: The length determines the distance by minimization, but conversely the distance determines the length by subdivision and approximation. The idea behind it is that the length of an "infinitesimal curve" is determined solely by the endpoints. A similar relation holds true for an action which is defined by a general Lagrangian of the form (7.2): indeed, if  $\gamma$  is differentiable at t, then

$$
\int_{t}^{t+\varepsilon} L(\gamma_{\tau}, \dot{\gamma}_{\tau}, \tau) d\tau \simeq \varepsilon L(\gamma_{t}, \dot{\gamma}_{t}, t),
$$

and the vector  $\dot{\gamma_t}$  is uniquely determined, up to an error  $o(\varepsilon)$ , by  $\gamma_t$  and  $\gamma_{t+\varepsilon}$ . Such would not be the case if the function L would also depend on, say, the acceleration of the curve.

This reconstruction property plays an important role and it is natural to enforce it in an abstract generalization. To do so, it will be useful to consider an action as a family of functionals parametrized by the initial and the final times: So  $\mathcal{A}^{s,t}$  is a functional on the set of paths  $[s,t] \rightarrow \mathcal{X}$ . Then we let

$$
c^{s,t}(x,y) = \inf \left\{ \mathcal{A}^{s,t}(\gamma); \quad \gamma_s = x, \ \gamma_t = y; \quad \gamma^{s,t} \in C([s,t];\mathcal{X}) \right\}.
$$
\n(7.12)

In words,  $c^{s,t}(x,y)$  is the minimal work needed to go from point x at initial time  $s$ , to point  $y$  at final time  $t$ .

**Example 7.10.** Consider the Lagrangian  $L(x, |v|, t) = |v|^p$ . Then

$$
c^{s,t}(x,y) = \frac{d(x,y)^p}{(t-s)^{p-1}}.
$$

Note a characteristic property of these "power law" Lagrangians: The cost function depends on s, t only through multiplication by a constant. In particular, minimizing curves will be independent of  $s$  and  $t$ , up to reparametrization.

## Abstract Lagrangian action

After all these preparations, the following definition should appear somewhat natural.

**Definition 7.11 (Lagrangian action).** Let  $(X, d)$  be a Polish space, and let  $t_i, t_f \in \mathbb{R}$ . A Lagrangian action  $(A)^{t_i,t_f}$  on X is a family of lower semicontinuous functionals  $\mathcal{A}^{s,t}$  on  $C([s,t];\mathcal{X})$   $(t_i \leq s < t \leq t_f)$ , and cost functions  $c^{s,t}$  on  $\mathcal{X} \times \mathcal{X}$ , such that:

(i)  $t_i \le t_1 < t_2 < t_3 \le t_f \implies \mathcal{A}^{t_1,t_2} + \mathcal{A}^{t_2,t_3} = \mathcal{A}^{t_1,t_3};$ (ii) ∀x,y ∈ X  $c^{s,t}(x,y) = \inf \left\{ \mathcal{A}^{s,t}(\gamma); \quad \gamma \in C([s,t];\mathcal{X}); \ \ \gamma_s = x, \ \gamma_t = y \right\};$ 

(iii) For any curve  $(\gamma_t)_{t_i \leq t \leq t_f}$ ,

$$
\mathcal{A}^{t_i, t_f}(\gamma) = \sup_{N \in \mathbb{N}} \sup_{t_i = t_0 \le t_1 \le \dots \le t_N = t_f} \left[ c^{t_0, t_1}(\gamma_{t_0}, \gamma_{t_1}) + c^{t_1, t_2}(\gamma_{t_1}, \gamma_{t_2}) + \dots + c^{t_{N-1}, t_N}(\gamma_{t_{N-1}}, \gamma_{t_N}) \right]
$$

.

The functional  $A = A^{t_i,t_f}$  will just be called the action, and the cost function  $c = c^{t_i, t_f}$  the cost associated with the action. A curve  $\gamma:[t_i,t_f]\rightarrow \mathcal{X}$  is said to be action-minimizing if it minimizes  $\mathcal{A}% _{ij}$  among all curves having the same endpoints.

Examples 7.12. (i) To recover (7.2) as a particular case of Definition 7.11, just set

$$
\mathcal{A}^{s,t}(\gamma) = \int_s^t L(\gamma_\tau, \dot{\gamma}_\tau, \tau) d\tau.
$$
 (7.13)

(ii) A length space is a space in which  $\mathcal{A}^{s,t}(\gamma) = \mathcal{L}(\gamma)$  (here  $\mathcal L$  is the length) defines a Lagrangian action.

If  $[t'_i, t'_f] \subset [t_i, t_f]$  then it is clear that  $(\mathcal{A})^{t_i, t_f}$  induces an action  $(\mathcal{A})^{t'_i,t'_f}$  on the time-interval  $[t'_i,t'_f]$ , just by restriction.

In the rest of this section I shall take  $(t_i, t_f) = (0, 1)$ , just for simplicity; of course one can always reduce to this case by reparametrization.

It will now be useful to introduce further assumptions about existence and compactness of minimizing curves.

**Definition 7.13 (Coercive action).** Let  $(A)^{0,1}$  be a Lagrangian action on a Polish space  $\mathcal{X}$ , with associated cost functions  $(c^{s,t})_{0 \leq s < t \leq 1}$ . For any two times s, t  $(0 \leq s < t \leq 1)$ , and any two compact sets  $K_s, K_t \subset \mathcal{X}, \text{ let } \Gamma_{K_s}^{s,t}$  $K_s, K_t, K_t$  be the set of minimizing paths starting in  $K_s$ at time s, and ending in  $K_t$  at time t. The action will be called coercive if:

(i) It is bounded below, in the sense that

$$
\inf_{s -\infty;
$$

(ii) If  $s < t$  are any two intermediate times, and  $K_s$ ,  $K_t$  are any two nonempty compact sets such that  $c^{s,t}(x,y) < +\infty$  for all  $x \in K_s$ ,  $y \in K_t$ , then the set  $\Gamma_{K_s}^{s,t}$  $K_s \rightarrow K_t$  is compact and nonempty.

In particular, minimizing curves between any two fixed points  $x, y$ with  $c(x,y) < +\infty$  should always exist and form a compact set.

**Remark 7.14.** If each  $A^{s,t}$  has compact sub-level sets (more explicitly, if  $\{\gamma; \mathcal{A}^{s,t}(\gamma) \leq A\}$  is compact in  $C([s,t];\mathcal{X})$  for any  $A \in \mathbb{R}$ ), then the lower semicontinuity of  $\mathcal{A}^{s,t}$ , together with a standard compactness argument (just as in Theorem 4.1) imply the existence of at least one action-minimizing curve among the set of curves that have prescribed final and initial points. In that case the requirement of nonemptiness in (ii) is fulfilled.

**Examples 7.15.** (i) If  $\mathcal{X}$  is a smooth complete Riemannian manifold and  $L(x, v, t)$  is a Lagrangian satisfying the classical conditions of Definition 7.6, then the action defined by (7.13) is coercive.

(ii) If  $\mathcal X$  is a geodesic length space, then the action defined by  $\mathcal{A}^{s,t}(\gamma) = \mathcal{L}(\gamma)^2/(t-s)$  is coercive; in fact minimizers are constantspeed minimizing geodesic curves. On the other hand the action defined by  $\mathcal{A}^{s,t}(\gamma) = \mathcal{L}(\gamma)$  is not coercive, since the possibility of reparametrization prevents the compactness of the set of minimizing curves.

Proposition 7.16 (Properties of Lagrangian actions). Let  $(X, d)$ be a Polish space and  $(\mathcal{A})^{0,1}$  a coercive Lagrangian action on X. Then:

(i) For all intermediate times  $s < t$ ,  $c^{s,t}$  is lower semicontinuous on  $\mathcal{X} \times \mathcal{X}$ , with values in  $\mathbb{R} \cup \{+\infty\}.$ 

(ii) If a curve  $\gamma$  on [s, t]  $\subset$  [0, 1] is a minimizer of  $\mathcal{A}^{s,t}$ , then its restriction to  $[s', t'] \subset [s, t]$  is also a minimizer for  $\mathcal{A}^{s', t'}$ .

(iii) For all times  $t_1 < t_2 < t_3$  in [0, 1], and  $x_1, x_3$  in X,

$$
c^{t_1,t_3}(x_1,x_3) = \inf_{x_2 \in \mathcal{X}} \left( c^{t_1,t_2}(x_1,x_2) + c^{t_2,t_3}(x_2,x_3) \right); \tag{7.14}
$$

and if the infimum is achieved at some point  $x_2$ , then there is a minimizing curve which goes from  $x_1$  at time  $t_1$  to  $x_3$  at time  $t_3$ , and passes through  $x_2$  at time  $t_2$ .

(iv) A curve  $\gamma$  is a minimizer of A if and only if, for all intermediate times  $t_1 < t_2 < t_3$  in [0, 1],

$$
c^{t_1,t_3}(\gamma_{t_1}, \gamma_{t_3}) = c^{t_1,t_2}(\gamma_{t_1}, \gamma_{t_2}) + c^{t_2,t_3}(\gamma_{t_2}, \gamma_{t_3}).
$$
\n(7.15)

(v) If the cost functions  $c^{s,t}$  are continuous, then the set  $\Gamma$  of all action-minimizing curves is closed in the topology of uniform convergence;

(vi) For all times  $s < t$ , there exists a Borel map  $S_{s\to t} : \mathcal{X} \times \mathcal{X} \to$  $C([s,t];\mathcal{X})$ , such that for all  $x,y \in \mathcal{X}$ ,  $S(x,y)$  belongs to  $\Gamma^{s,t}_{x \to y}$ . In words, there is a measurable recipe to join any two endpoints  $x$  and  $y$ by a minimizing curve  $\gamma : [s,t] \to \mathcal{X}$ .

Remark 7.17. The statement in (iv) is a powerful formulation of the minimizing property. It is often quite convenient from the technical point of view, even in a smooth setting, because it does not involve any time-derivative.

**Remark 7.18.** The continuity assumption in  $(v)$  is satisfied in most cases of interest. For instance, if  $\mathcal{A}^{s,t}(\gamma) = \mathcal{L}(\gamma)^2/(t-s)$ , then  $c^{s,t}(x, y) =$  $d(x, y)^2/(t - s)$ , which is obviously continuous. Continuity also holds true in the other model example where  $\mathcal X$  is a Riemannian manifold and the cost is obtained from a Lagrangian function  $L(x, v, t)$  on  $TM \times [0, 1]$ satisfying the classical assumptions; a proof is sketched in the Appendix.

Proof of Proposition 7.16. Let us prove (i). By definition of the coercivity,  $c(x, y)$  is never  $-\infty$ . Let  $(x_k)_{k \in \mathbb{N}}$  and  $(y_k)_{k \in \mathbb{N}}$  be sequences converging to x and y respectively. Then the family  $(x_k) \cup \{x\}$  forms a compact set  $K_s$ , and the family  $(y_k) \cup \{y\}$  also forms a compact set  $K_t$ . By assumption, for each k we can find a minimizing curve  $\gamma_k : [s,t] \to \mathcal{X}$  joining  $x_k$  to  $y_k$ , so  $\gamma_k$  belongs to  $\Gamma_{K_s}^{s,t}$  $K_s \rightarrow K_t$  which is compact. From  $(\gamma_k)_{k\in\mathbb{N}}$  we can extract a subsequence which converges uniformly to some minimizing curve  $\gamma$ . The uniform convergence implies that  $x_k = \gamma_k(s) \rightarrow \gamma(s)$ ,  $y_k = \gamma_k(t) \rightarrow \gamma(t)$ , so  $\gamma$  joins x to y. The lower semicontinuity of  $\mathcal{A}^{s,t}$  implies that  $\mathcal{A}^{s,t}(\gamma) \leq \liminf \mathcal{A}^{s,t}(\gamma_k)$ ; thus

$$
c^{s,t}(x,y) \leq \mathcal{A}^{s,t}(\gamma) \leq \liminf \mathcal{A}^{s,t}(\gamma_k) = \liminf c^{s,t}(x_k, y_k).
$$

This establishes the lower semicontinuity of the cost  $c^{s,t}$ .

Property (ii) is obvious: if the restriction of  $\gamma$  to  $[s', t']$  is not optimal, introduce  $\widetilde{\gamma}$  on [s', t'] such that  $\mathcal{A}^{s',t'}(\widetilde{\gamma}) < \mathcal{A}^{s',t'}(\gamma)$ . Then the path obtained by concatenating  $\gamma$  on  $[s, s'], \widetilde{\gamma}$  on  $[s', t']$  and  $\gamma$  again on  $[t', t],$ has a strictly lower action  $\mathcal{A}^{s,t}$  than  $\gamma$ , which is impossible. (Obviously, this is the same line of reasoning as in the proof of the "restriction property" of Theorem 4.6.)

Now, to prove (iii), introduce minimizing curves  $\gamma_{1\rightarrow 2}$  joining  $x_1$  at time  $t_1$ , to  $x_2$  at time  $t_2$ , and  $\gamma_{2\rightarrow 3}$  joining  $x_2$  at time  $t_2$ , to  $x_3$  at time  $t_3$ . Then define  $\gamma$  on  $[t_1,t_3]$  by concatenation of  $\gamma_{1\rightarrow 2}$  and  $\gamma_{2\rightarrow 3}$ . From the axioms of Definition 7.11,

$$
c^{t_1,t_3}(x_1,x_3) \leq \mathcal{A}^{t_1,t_3}(\gamma) = \mathcal{A}^{t_1,t_2}(\gamma_{1\to 2}) + \mathcal{A}^{t_2,t_3}(\gamma_{2\to 3})
$$
  
=  $c^{t_1,t_2}(x_1,x_2) + c^{t_2,t_3}(x_2,x_3).$ 

The inequality in (iii) follows by taking the infimum over  $x_2$ . Moreover, if there is equality, that is,

$$
c^{t_1,t_2}(x_1,x_2) + c^{t_2,t_3}(x_2,x_3) = c^{t_1,t_3}(x_1,x_3),
$$

then equality holds everywhere in the above chain of inequalities, so the curve  $\gamma$  achieves the optimal cost  $c^{t_1,t_3}(x_1,x_3)$ , while passing through  $x_2$  at time  $t_2$ .

It is a consequence of (iii) that any minimizer should satisfy (7.15), since the restrictions of  $\gamma$  to  $[t_1,t_2]$  and to  $[t_2,t_3]$  should both be minimizing. Conversely, let  $\gamma$  be a curve satisfying (7.15) for all  $(t_1, t_2, t_3)$ with  $t_1 < t_2 < t_3$ . By induction, this implies that for each subdivision  $0 = t_0 < t_1 \leq \ldots < t_N = 1$ ,

$$
c^{0,1}(\gamma_0, \gamma_1) = \sum_j c^{t_j, t_{j+1}}(\gamma_{t_j}, \gamma_{t_{j+1}}).
$$

By point (iii) in Definition 7.11, it follows that  $c^{0,1}(\gamma_0, \gamma_1) = \mathcal{A}^{0,1}(\gamma)$ , which proves (iv).

If  $0 \leq t_1 < t_2 < t_3 \leq 1$ , now let  $\Gamma(t_1,t_2,t_3)$  stand for the set of all curves satisfying (7.15). If all functions  $c^{s,t}$  are continuous, then  $\Gamma(t_1,t_2,t_3)$  is closed for the topology of uniform convergence. Then  $\Gamma$  is the intersection of all  $\Gamma(t_1,t_2,t_3)$ , so it is closed also; this proves statement (v). (Now there is a similarity with the proof of Theorem 5.20.)

For given times  $s < t$ , let  $\Gamma^{s,t}$  be the set of all action-minimizing curves defined on  $[s,t]$ , and let  $E_{s,t}$  be the "endpoints" mapping, defined on  $\Gamma^{s,t}$  by  $\gamma \longmapsto (\gamma_s, \gamma_t)$ . By assumption, any two points are joined by at least one minimizing curve, so  $E_{s,t}$  is onto  $\mathcal{X} \times \mathcal{X}$ . It is clear that  $E_{s,t}$  is a continuous map between Polish spaces, and by assumption  $E_{s,t}^{-1}(x,y)$  is compact for all  $x,y$ . It follows by general theorems of measurable selection (see the bibliographical notes in case of need) that  $E_{s,t}$  admits a measurable right-inverse  $S_{s\to t}$ , i.e.  $E_{s,t} \circ S_{s\to t} = \text{Id}$ . This proves statement (vi). □

## Interpolation of random variables

Action-minimizing curves provide a fairly general framework to interpolate between points, which can be seen as deterministic random variables. What happens when we want to interpolate between genuinely random variables, in a way that is most economic? Since a deterministic point can be identified with a Dirac mass, this new problem contains both the classical action-minimizing problem and the Monge– Kantorovich problem.

Here is a natural recipe. Let c be the cost associated with the Lagrangian action, and let  $\mu_0$ ,  $\mu_1$  be two given laws. Introduce an optimal coupling  $(X_0, X_1)$  of  $(\mu_0, \mu_1)$ , and a random action-minimizing path  $(X_t)_{0 \leq t \leq 1}$  joining  $X_0$  to  $X_1$ . (We shall see later that such a thing always exists.) Then the random variable  $X_t$  is an interpolation of  $X_0$ and  $X_1$ ; or equivalently the law  $\mu_t$  is an interpolation of  $\mu_0$  and  $\mu_1$ . This procedure is called displacement interpolation, by opposition to the linear interpolation  $\mu_t = (1 - t) \mu_0 + t \mu_1$ . Note that there is a priori no uniqueness of the displacement interpolation.

Some of the concepts which we just introduced deserve careful attention. In the sequel,  $e_t$  will stand for the evaluation at time t:  $e_t(\gamma) = \gamma(t).$ 

Definition 7.19 (Dynamical coupling). Let  $(X, d)$  be a Polish space. A dynamical transference plan  $\Pi$  is a probability measure on the space  $C([0, 1]; \mathcal{X})$ . A dynamical coupling of two probability measures  $\mu_0, \mu_1 \in P(\mathcal{X})$  is a random curve  $\gamma : [0, 1] \to \mathcal{X}$  such that law  $(\gamma_0) = \mu_0$ ,  $law(\gamma_1) = \mu_1.$ 

Definition 7.20 (Dynamical optimal coupling). Let  $(\mathcal{X}, d)$  be a Polish space,  $(A)^{0,1}$  a Lagrangian action on X, c the associated cost, and  $\Gamma$  the set of action-minimizing curves. A dynamical optimal transference plan is a probability measure  $\Pi$  on  $\Gamma$  such that

$$
\pi_{0,1} := (e_0, e_1)_{\#} \Pi
$$

is an optimal transference plan between  $\mu_0$  and  $\mu_1$ . Equivalently,  $\Pi$  is the law of a random action-minimizing curve whose endpoints constitute an optimal coupling of  $\mu_0$  and  $\mu_1$ . Such a random curve is called a dynamical optimal coupling of  $(\mu_0, \mu_1)$ . By abuse of language,  $\Pi$  itself is often called a dynamical optimal coupling.

The next theorem is the main result of this chapter. It shows that the law at time  $t$  of a dynamical optimal coupling can be seen as a minimizing path in the space of probability measures. In the important case when the cost is a power of a geodesic distance, the corollary stated right after the theorem shows that displacement interpolation can be thought of as a geodesic path in the space of probability measures. ("A geodesic in the space of laws is the law of a geodesic.") The theorem also shows that such interpolations can be constructed under quite weak assumptions.

**Theorem 7.21 (Displacement interpolation).** Let  $(\mathcal{X}, d)$  be a Polish space, and  $(A)^{0,1}$  a coercive Lagrangian action on X, with continuous cost functions  $c^{s,t}$ . Whenever  $0 \leq s \leq t \leq 1$ , denote by  $C^{s,t}(\mu,\nu)$  the optimal transport cost between the probability measures  $\mu$  and  $\nu$  for the cost  $c^{s,t}$ ; write  $c = c^{0,1}$  and  $C = C^{0,1}$ . Let  $\mu_0$  and  $\mu_1$ be any two probability measures on  $X$ , such that the optimal transport cost  $C(\mu_0,\mu_1)$  is finite. Then, given a continuous path  $(\mu_t)_{0 \leq t \leq 1}$ , the following properties are equivalent:

(i) For each  $t \in [0,1]$ ,  $\mu_t$  is the law of  $\gamma_t$ , where  $(\gamma_t)_{0 \leq t \leq 1}$  is a dynamical optimal coupling of  $(\mu_0, \mu_1)$ ;

(ii) For any three intermediate times  $t_1 < t_2 < t_3$  in [0, 1],

$$
C^{t_1,t_2}(\mu_{t_1}, \mu_{t_2}) + C^{t_2,t_3}(\mu_{t_2}, \mu_{t_3}) = C^{t_1,t_3}(\mu_{t_1}, \mu_{t_3});
$$

(iii) The path  $(\mu_t)_{0 \leq t \leq 1}$  is a minimizing curve for the coercive action functional defined on  $P(X)$  by

$$
\mathbb{A}^{s,t}(\mu) = \sup_{N \in \mathbb{N}} \sup_{s = t_0 < t_1 < \ldots < t_N = t} \sum_{i=0}^{N-1} C^{t_i, t_{i+1}}(\mu_{t_i}, \mu_{t_{i+1}}) \tag{7.16}
$$

$$
= \inf_{\gamma} \mathbb{E} \mathcal{A}^{s,t}(\gamma), \tag{7.17}
$$

where the last infimum is over all random curves  $\gamma : [s,t] \to \mathcal{X}$  such that law  $(\gamma_{\tau}) = \mu_{\tau}$   $(s \leq \tau \leq t)$ .

In that case  $(\mu_t)_{0 \leq t \leq 1}$  is said to be a displacement interpolation between  $\mu_0$  and  $\mu_1$ . There always exists at least one such curve.

Finally, if  $\mathcal{K}_0$  and  $\mathcal{K}_1$  are two compact subsets of  $P(\mathcal{X})$ , such that  $C^{0,1}(\mu_0,\mu_1)<+\infty$  for all  $\mu_0\in\mathcal{K}_0$ ,  $\mu_1\in\mathcal{K}_1$ , then the set of dynamical optimal transference plans  $\Pi$  with  $(e_0)_\# \Pi \in \mathcal{K}_0$  and  $(e_1)_\# \Pi \in \mathcal{K}_1$  is compact.

Theorem 7.21 admits two important corollaries:

Corollary 7.22 (Displacement interpolation as geodesics). Let  $(\mathcal{X}, d)$  be a complete separable, locally compact length space. Let  $p > 1$ 

and let  $P_p(\mathcal{X})$  be the space of probability measures on X with finite moment of order p, metrized by the Wasserstein distance  $W_p$ . Then, given any two  $\mu_0, \mu_1 \in P_p(\mathcal{X})$ , and a continuous curve  $(\mu_t)_{0 \leq t \leq 1}$ , valued in  $P(X)$ , the following properties are equivalent:

(i)  $\mu_t$  is the law of  $\gamma_t$ , where  $\gamma$  is a random (minimizing, constantspeed) geodesic such that  $(\gamma_0, \gamma_1)$  is an optimal coupling;

(ii)  $(\mu_t)_{0 \leq t \leq 1}$  is a geodesic curve in the space  $P_p(\mathcal{X})$ .

Moreover, if  $\mu_0$  and  $\mu_1$  are given, there exists at least one such curve. More generally, if  $\mathcal{K}_0 \subset P_p(\mathcal{X})$  and  $\mathcal{K}_1 \subset P_p(\mathcal{X})$  are compact subsets of  $P(\mathcal{X})$ , then the set of geodesic curves  $(\mu_t)_{0 \leq t \leq 1}$  such that  $\mu_0 \in \mathcal{K}_0$  and  $\mu_1 \in \mathcal{K}_1$  is compact and nonempty; and also the set of dynamical optimal transference plans  $\Pi$  with  $(e_0)_\# \Pi \in \mathcal{K}_0$ ,  $(e_1)_\# \Pi \in \mathcal{K}_1$  is compact and nonempty.

## Corollary 7.23 (Uniqueness of displacement interpolation). With the same assumptions as in Theorem 7.21, if:

(a) there is a unique optimal transport plan  $\pi$  between  $\mu_0$  and  $\mu_1$ ;

(b)  $\pi(dx_0 dx_1)$ -almost surely,  $x_0$  and  $x_1$  are joined by a unique minimizing curve;

then there is a unique displacement interpolation  $(\mu_t)_{0 \leq t \leq 1}$  joining  $\mu_0$ to  $\mu_1$ .

**Remark 7.24.** In Corollary 7.22,  $\mathcal{A}^{s,t}(\gamma) = \int_s^t |\dot{\gamma}_{\tau}|^p d\tau$ . Then actionminimizing curves in  $\mathcal X$  are the same, whatever the value of  $p > 1$ . Yet geodesics in  $P_p(\mathcal{X})$  are not the same for different values of p, because a coupling of  $(\mu_0, \mu_1)$  which is optimal for a certain value of p, might well not be for another value.

**Remark 7.25.** Theorem 7.21 applies to Lagrangian functions  $L(x, v, t)$ on a Riemannian manifold  $TM$ , as soon as L is  $C<sup>2</sup>$  and satisfies the classical conditions of Definition 7.6. Then  $\mu_t$  is the law at time t of a random solution of the Euler–Lagrange equation (7.6).

Remark 7.26. In Theorem 7.21, the minimizing property of the path  $(\mu_t)$  is expressed in a weak formulation, which makes sense with a lot of generality. But this theorem leaves open certain natural questions:

• Is there a differential equation for geodesic curves, or more generally optimal paths  $(\mu_t)_{0 \le t \le 1}$ ? Of course, the answer is related to the possibility of defining a tangent space in the space of measures.

- Is there a more explicit formula for the action on the space of probability measures, say for a simple enough action on  $\mathcal{X}$ ? Can it be written as  $\int_0^1 \mathbb{L}(\mu_t, \mu_t, t) dt$ ? (Of course, in Corollary 7.22 this is the case with  $\mathbb{L} = |\dot{\mu}|^p$ , but this expression is not very "explicit".)
- Are geodesic paths nonbranching? (Does the velocity at initial time uniquely determine the final measure  $\mu_1$ ?)
- Can one identify simple conditions for the existence of a *unique* geodesic path between two given probability measures?

All these questions will be answered affirmatively in the sequel of this course, under suitable regularity assumptions on the space, the action or the probability measures.

Remark 7.27. The assumption of local compactness in Corollary 7.22 is not superficial: it is used to guarantee the coercivity of the action. For spaces that are not locally compact, there might be an analogous theory, but it is certainly more tricky. First of all, selection theorems are not immediately available if one does not assume compactness of the set of geodesics joining two given endpoints. More importantly, the convergence scheme used below to construct a random geodesic curve from a time-dependent law might fail to work. Here we are encountering a general principle in probability theory: Analytic characterizations of stochastic processes (like those based on semigroups, generators, etc.) are essentially available only in locally compact spaces. In spite of all that, there are some representation theorems for Wasserstein geodesics that do not need local compactness; see the bibliographical notes for details.

The proof of Theorem 7.21 is not so difficult, but a bit cumbersome because of measurability issues. For training purposes, the reader might rewrite it in the simpler case where any pair of points is joined by a *unique* geodesic (as in the case of  $\mathbb{R}^n$ ). To help understanding, I shall first sketch the main idea.

Main idea in the proof of Theorem 7.21. The delicate part consists in showing that if  $(\mu_t)$  is a given action-minimizing curve, then there exists a random minimizer  $\gamma$  such that  $\mu_t = \text{law}(\gamma_t)$ . This  $\gamma$  will be constructed by dyadic approximation, as follows. First let  $(\gamma_0^{(0)})$  $\overset{(0)}{0},\overset{(0)}{\gamma_1^0}$  $\binom{(0)}{1}$ be an optimal coupling of  $(\mu_0, \mu_1)$ . (Here the notation  $\gamma_0^{(0)}$  $\frac{1}{0}$  could be replaced by just  $x_0$ , it does not mean that there is some curve  $\gamma^{(0)}$ 

behind.) Then let  $(\gamma_0^{(1)})$  $\overset{(1)}{0},\overset{(1)}{\gamma_{1/2}^{(1)}}$  $\binom{1}{1/2}$  be an optimal coupling of  $(\mu_0, \mu_{1/2})$ , and  $((\gamma')_1^{(1)}$  $_{1/2}^{(1)}, \gamma_1^{(1)}$  $\binom{1}{1}$  be an optimal coupling of  $(\mu_{1/2}, \mu_1)$ . By gluing these couplings together, I can actually assume that  $(\gamma')_{1/2}^{(1)} = \gamma_{1/2}^{(1)}$  $\frac{1}{1/2}$ , so that I have a triple  $(\gamma_0^{(1)}$  $\gamma^{(1)}_{0},\gamma^{(1)}_{1/2}$  $\frac{\left(1\right)}{1/2},\gamma_{1}^{\left(1\right)}$  $1^{(1)}$ ) in which the first two components on the one hand, and the last two components on the other hand, constitute optimal couplings.

Now the key observation is that if  $(\gamma_{t_1}, \gamma_{t_2})$  and  $(\gamma_{t_2}, \gamma_{t_3})$  are optimal couplings of  $(\mu_{t_1}, \mu_{t_2})$  and  $(\mu_{t_2}, \mu_{t_3})$  respectively, and the  $\mu_{t_k}$  satisfy the equality appearing in (ii), then also  $(\gamma_{t_1}, \gamma_{t_3})$  should be optimal. Indeed, by taking expectation in the inequality

$$
c^{t_1,t_3}(\gamma_{t_1},\gamma_{t_3}) \leq c^{t_1,t_2}(\gamma_{t_1},\gamma_{t_2}) + c^{t_2,t_3}(\gamma_{t_2},\gamma_{t_3})
$$

and using the optimality assumption, one obtains

$$
\mathbb{E} c^{t_1,t_3}(\gamma_{t_1},\gamma_{t_3}) \leq C^{t_1,t_2}(\mu_{t_1},\mu_{t_2}) + C^{t_2,t_3}(\mu_{t_2},\mu_{t_3}).
$$

Now the fact that  $(\mu_t)$  is action-minimizing imposes

$$
C^{t_1,t_2}(\mu_{t_1},\mu_{t_2})+C^{t_2,t_3}(\mu_{t_2},\mu_{t_3})=C^{t_1,t_3}(\mu_{t_1},\mu_{t_3});
$$

so actually

$$
\mathbb{E} c^{t_1,t_3}(\gamma_{t_1},\gamma_{t_3}) \leq C^{t_1,t_3}(\mu_{t_1},\mu_{t_3}),
$$

which means that indeed  $(\gamma_{t_1}, \gamma_{t_3})$  is an optimal coupling of  $(\mu_{t_1}, \mu_{t_3})$ for the cost  $c^{t_1,t_3}$ .

So  $(\gamma_0^{(1)}$  $\overset{(1)}{0},\overset{(1)}{\gamma_1^{(1)}}$  $1^{(1)}$ ) is an optimal coupling of  $(\mu_0, \mu_1)$ . Now we can proceed in the same manner and define, for each  $k$ , a random discrete path  $(\gamma_i^{(k)}$  $j_{2^{-k}}^{(k)}$ ) such that  $(\gamma_s^{(k)}, \gamma_t^{(k)})$  $t_t^{(k)}$ ) is an optimal coupling for all times s, t of the form  $j/2^k$ . These are only discrete paths, but it is possible to extend them into paths  $(\gamma_t^{(k)})$  $\lambda_t^{(k)}$ ) $0 \le t \le 1$  that are minimizers of the action. Of course, if t is not of the form  $j/2^k$ , there is no reason why law  $(\gamma_t^{(k)})$  $t^{(\kappa)}$  would coincide with  $\mu_t$ . But hopefully we can pass to the limit as  $k \to \infty$ , for each dyadic time, and conclude by a density argument. □

Complete proof of Theorem 7.21. First, if  $\mathcal{A}^{s,t}(\gamma)$  is bounded below by a constant  $-C$ , independently of s, t and  $\gamma$ , then the same is true of the cost functions  $c^{s,t}$  and of the total costs  $C^{s,t}$ . So all the quantities appearing in the proof will be well-defined, the value  $+\infty$  being possibly attained. Moreover, the action  $A^{s,t}$  defined by the formula in (iii) will also be bounded below by the same constant  $-C$ , so Property (i) of Definition 7.13 will be satisfied.

Now let  $\mu_0$  and  $\mu_1$  be given. According to Theorem 4.1, there exists at least one optimal transference plan  $\pi$  between  $\mu_0$  and  $\mu_1$ , for the cost  $c = c^{0,1}$ . Let  $S_{0\rightarrow 1}$  be the mapping appearing in Proposition 7.16(vi), and let

$$
\Pi := (S_{0\rightarrow 1})_{\#}\pi.
$$

Then  $\Pi$  defines the law of a random geodesic  $\gamma$ , and the identity  $E_{0,1} \circ$  $S_{0\rightarrow1}$  = Id implies that the endpoints of  $\gamma$  are distributed according to  $\pi$ . This proves the existence of a path satisfying (i). Now the main part of the proof consists in checking the equivalence of properties (i) and (ii). This will be performed in four steps.

**Step 1.** Let  $(\mu_t)_{0 \le t \le 1}$  be any continuous curve in the space of probability measures, and let  $t_1, t_2, t_3$  be three intermediate times. Let  $\pi_{t_1 \to t_2}$ be an optimal transference plan between  $\mu_{t_1}$  and  $\mu_{t_2}$  for the transport cost  $c^{t_1,t_2}$ , and similarly let  $\pi_{t_2 \to t_3}$  be an optimal transference plan between  $\mu_{t_2}$  and  $\mu_{t_3}$  for the transport cost  $c^{t_2,t_3}$ . By the Gluing Lemma of Chapter 1 one can construct random variables  $(\gamma_{t_1}, \gamma_{t_2}, \gamma_{t_3})$  such that law  $(\gamma_{t_1}, \gamma_{t_2}) = \pi_{t_1 \to t_2}$  and law  $(\gamma_{t_2}, \gamma_{t_3}) = \pi_{t_2 \to t_3}$  (in particular,  $\text{law}(\gamma_{t_i}) = \mu_{t_i} \text{ for } i = 1, 2, 3$ . Then, by (7.14),

$$
C^{t_1,t_3}(\mu_{t_1}, \mu_{t_3}) \leq \mathbb{E} \, c^{t_1,t_3}(\gamma_{t_1}, \gamma_{t_3}) \leq \mathbb{E} \, c^{t_1,t_2}(\gamma_{t_1}, \gamma_{t_2}) + \mathbb{E} \, c^{t_2,t_3}(\gamma_{t_2}, \gamma_{t_3})
$$
  
= 
$$
C^{t_1,t_2}(\mu_{t_1}, \mu_{t_2}) + C^{t_2,t_3}(\mu_{t_2}, \mu_{t_3}).
$$

This inequality holds for any path, optimal or not.

**Step 2.** Assume that  $(\mu_t)$  satisfies (i), so there is a dynamical optimal transference plan  $\Pi$  such that  $\mu_t = (e_t)_\# \Pi$ . Let  $\gamma$  be a random minimizing curve with law  $\Pi$ , and consider the obvious coupling  $(\gamma_{t_1}, \gamma_{t_2})$  (resp.  $(\gamma_{t_2}, \gamma_{t_3})$ ) of  $(\mu_{t_1}, \mu_{t_2})$  (resp.  $(\mu_{t_2}, \mu_{t_3})$ ). Then from the definition of the optimal cost and the minimizing property of  $\gamma$ ,

$$
C^{t_1,t_2}(\mu_{t_1}, \mu_{t_2}) + C^{t_2,t_3}(\mu_{t_2}, \mu_{t_3}) \leq \mathbb{E} c^{t_1,t_2}(\gamma_{t_1}, \gamma_{t_2}) + \mathbb{E} c^{t_2,t_3}(\gamma_{t_2}, \gamma_{t_3})
$$
  
=  $\mathbb{E} \mathcal{A}^{t_1,t_2}(\gamma) + \mathbb{E} \mathcal{A}^{t_2,t_3}(\gamma) = \mathbb{E} \mathcal{A}^{t_1,t_3}(\gamma) = \mathbb{E} c^{t_1,t_3}(\gamma_{t_1}, \gamma_{t_3}).$  (7.18)

Now choose  $t_1 = 0, t_2 = t, t_3 = 1$ . Since by assumption  $(\gamma_0, \gamma_1)$  is an optimal coupling of  $(\mu_0, \mu_1)$ , the above computation implies

$$
C^{0,t}(\mu_0,\mu_t) + C^{t,1}(\mu_t,\mu_1) \le C^{0,1}(\mu_0,\mu_1),
$$

and since the reverse inequality holds as a consequence of Step 1, actually

$$
C^{0,t}(\mu_0, \mu_t) + C^{t,1}(\mu_t, \mu_1) = C^{0,1}(\mu_0, \mu_1).
$$

Moreover, equality has to hold in (7.18) (for that particular choice of intermediate times); since  $C^{0,1}(\mu_0, \mu_1) < +\infty$  this implies  $C^{0,t}(\mu_0, \mu_t) =$  $\mathbb{E} c^{0,t}(\gamma_0,\gamma_t)$ , which means that  $(\gamma_0,\gamma_t)$  should actually be an optimal coupling of  $(\mu_0, \mu_t)$ . Similarly,  $(\gamma_t, \gamma_1)$  should be an optimal coupling of  $(\mu_t, \mu_1).$ 

Next choose  $t_1 = 0, t_2 = s, t_3 = t$ , and apply the previous deduction to discover that  $(\gamma_s, \gamma_t)$  is an optimal coupling of  $(\mu_s, \mu_t)$ . After inserting this information in (7.18) with  $s = t_2$  and  $t = t_3$ , we recover

$$
C^{t_1,t_2}(\mu_{t_1},\mu_{t_2})+C^{t_2,t_3}(\mu_{t_2},\mu_{t_3})\leq C^{t_1,t_3}(\mu_{t_1},\mu_{t_3}).
$$

This together with Step 1 proves that  $(\mu_t)$  satisfies Property (ii). So far we have proven (i)  $\Rightarrow$  (ii).

**Step 3.** Assume that  $(\mu_t)$  satisfies Property (ii); then we can perform again the same computation as in Step 1, but now all the inequalities have to be equalities. This implies that the random variables  $(\gamma_{t_1}, \gamma_{t_2}, \gamma_{t_3})$  satisfy:

- (a)  $(\gamma_{t_1}, \gamma_{t_3})$  is an optimal coupling of  $(\mu_{t_1}, \mu_{t_3})$  for the cost  $c^{t_1,t_3}$ ; (b)  $c^{t_1,t_3}(\gamma_{t_1}, \gamma_{t_3}) = c^{t_1,t_2}(\gamma_{t_1}, \gamma_{t_2}) + c^{t_2,t_3}(\gamma_{t_2}, \gamma_{t_3})$  almost surely;
- (c)  $c^{s,t}(\gamma_s, \gamma_t) < +\infty$  almost surely.

Armed with that information, we proceed as follows. We start from an optimal coupling  $(\gamma_0, \gamma_1)$  of  $(\mu_0, \mu_1)$ , with joint law  $\pi_{0 \to 1}$ . Then as in Step 1 we construct a triple  $(\gamma_0^{(1)})$  $\gamma_0^{(1)}, \gamma_{1}^{(1)}, \gamma_{1}^{(1)}$  $\binom{1}{1}$  with law  $(\gamma_0^{(1)})$  $\binom{1}{0}$  =  $\mu_0$ ,  $\text{law } (\gamma_{\frac{1}{2}}^{(1)}) = \mu_{\frac{1}{2}}, \text{ law } (\gamma_1^{(1)})$ 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2  $\binom{1}{1}$  =  $\mu_1$ , such that  $(\gamma_0^{(1)})$  $\gamma_0^{(1)}, \gamma_{\frac{1}{2}}^{(1)}$  is an optimal coupling of  $(\mu_0, \mu_{\frac{1}{2}})$  for the cost  $c^{0, \frac{1}{2}}$  and  $(\gamma_{\frac{1}{2}}^{(1)}, \gamma_1^{(1)})$  $j_1^{(1)}$ ) is an optimal coupling of  $(\mu_{\frac{1}{2}}, \mu_1)$  for the cost  $c^{\frac{1}{2},1}$ . From (a) and (b) above we know that  $(\gamma_0^{(1)}$  $\overset{(1)}{0},\overset{(1)}{\gamma_1^{(1)}}$  $\binom{1}{1}$  is an optimal coupling of  $(\mu_0, \mu_1)$  (but law  $(\gamma_0^{(1)})$  $\overset{(1)}{0}, \overset{(1)}{\gamma_1}$  $\binom{1}{1}$ might be different from law  $(\gamma_0, \gamma_1)$ ), and moreover  $c^{0,1}(\gamma_0^{(1)})$  $\overset{(1)}{0},\overset{(1)}{\gamma_1^{\left(1\right)}}$  $j_1^{(1)}$ ) =  $c^{0,\frac{1}{2}}(\gamma_0^{(1)}$  $(c^{(1)}, \gamma^{(1)}_{\frac{1}{2}}) + c^{\frac{1}{2},1}(\gamma^{(1)}_{\frac{1}{2}}, \gamma^{(1)}_{1})$  $1^{(1)}$ ) almost surely.

Next it is possible to iterate the construction, introducing more and more midpoints. By a reasoning similar to the one above and an induction argument, one can construct, for each integer  $k \geq 1$ , random variables  $(\gamma_0^{(k)})$  $\frac{(k)}{0}, \gamma \frac{(k)}{\frac{1}{2^k}}$  $,\gamma_{\frac{2}{2^k}}^{(k)}$  $,\gamma_{\frac{3}{2^k}}^{(k)}$  $,\ldots \gamma _{1}^{(k)}$  $\binom{(\kappa)}{1}$  in such a way that

(a) for any two  $i, j \leq 2^k$ ,  $(\gamma_{\frac{i}{2^k}}^{(k)})$  $,\gamma_{\frac{j}{2^k}}^{(k)}$ ) constitutes an optimal coupling of  $(\mu_{\frac{i}{2^k}}, \mu_{\frac{j}{2^k}}),$ 

(b) for any three indices  $i_1, i_2, i_3 \leq 2^k$ , one has

$$
c^{\frac{i_1}{2^k},\frac{i_3}{2^k}}(\gamma^{(k)}_{\frac{i_1}{2^k}},\gamma^{(k)}_{\frac{i_3}{2^k}})=c^{\frac{i_1}{2^k},\frac{i_2}{2^k}}(\gamma^{(k)}_{\frac{i_1}{2^k}},\gamma^{(k)}_{\frac{i_2}{2^k}})+c^{\frac{i_2}{2^k},\frac{i_3}{2^k}}(\gamma^{(k)}_{\frac{i_2}{2^k}},\gamma^{(k)}_{\frac{i_3}{2^k}}).
$$

At this stage it is convenient to extend the random variables  $\gamma^{(k)}$ , which are only defined for times  $j/2^k$ , into (random) continuous curves  $(\gamma_t^{(k)}$  $(t^{(k)})_{0 \leq t \leq 1}$ . For that we use Proposition 7.16(vi) again, and for any  $t \in (i/2^k, (i + 1)/2^k)$  we define

$$
\gamma_t:=e_t\Big(S_{\frac{i}{2^k},\frac{i+1}{2^k}}(\gamma_{\frac{i}{2^k}},\gamma_{\frac{i+1}{2^k}})\Big).
$$

(Recall that  $e_t$  is just the evaluation at time t.) Then the law  $\Pi^{(k)}$  of  $(\gamma_t)_{0 \leq t \leq 1}$  is a probability measure on the set of continuous curves in X.

I claim that  $\Pi^{(k)}$  is actually concentrated on minimizing curves. (Skip at first reading and go directly to Step 4.) To prove this, it is sufficient to check the criterion in Proposition 7.16(iv), involving three intermediate times  $t_1, t_2, t_3$ . By construction, the criterion holds true if all these times belong to the same time-interval  $[i/2^k, (i+1)/2^k]$ , and also if they are all of the form  $j/2^k$ ; the problem consists in "crossing" subintervals". Let us show that

$$
(i-1) 2^{-k} < s < i2^{-k} 
Arr
$$

$$
\begin{cases}
 c^{\frac{i-1}{2^k}, \frac{j+1}{2^k}}(\gamma_{\frac{i-1}{2^k}}, \gamma_{\frac{j+1}{2^k}}) = c^{\frac{i-1}{2^k}, s}(\gamma_{\frac{i-1}{2^k}}, \gamma_s) + c^{s, t}(\gamma_s, \gamma_t) + c^{t, \frac{j+1}{2^k}}(\gamma_t, \gamma_{\frac{j+1}{2^k}})
\\ c^{s, t}(\gamma_s, \gamma_t) = c^{s, \frac{i}{2^k}}(\gamma_s, \gamma_{\frac{i}{2^k}}) + c^{\frac{i}{2^k}, \frac{j}{2^k}}(\gamma_{\frac{i}{2^k}}, \gamma_{\frac{j}{2^k}}) + c^{\frac{j}{2^k}, t}(\gamma_{\frac{j}{2^k}}, \gamma_t)
\end{cases}
$$
 $(7.19)$ 

To prove this, we start with

$$
c^{\frac{i-1}{2^k}, \frac{j+1}{2^k}}(\gamma_{\frac{i-1}{2^k}}, \gamma_{\frac{j+1}{2^k}}) \leq c^{\frac{i-1}{2^k}, s}(\gamma_{\frac{i-1}{2^k}}, \gamma_s) + c^{s, t}(\gamma_s, \gamma_t) + c^{t, \frac{j+1}{2^k}}(\gamma_t, \gamma_{\frac{j+1}{2^k}})
$$
  
$$
\leq c^{\frac{i-1}{2^k}, s}(\gamma_{\frac{i-1}{2^k}}, \gamma_s) + c^{s, \frac{i}{2^k}}(\gamma_s, \gamma_{\frac{i}{2^k}}) + c^{\frac{i}{2^k}, \frac{i+1}{2^k}}(\gamma_{\frac{i}{2^k}}, \gamma_{\frac{i+1}{2^k}})
$$
  
$$
+ \dots + c^{\frac{j}{2^k}, t}(\gamma_{\frac{j}{2^k}}, \gamma_t) + c^{t, \frac{j+1}{2^k}}(\gamma_t, \gamma_{\frac{j+1}{2^k}}).
$$
(7.20)

Since we have used minimizing curves to interpolate on each dyadic subinterval,

$$
c^{\frac{i-1}{2^k},s}(\gamma_{\frac{i-1}{2^k}},\gamma_s)+c^{s,\frac{i}{2^k}}(\gamma_s,\gamma_{\frac{i}{2^k}})=c^{\frac{i-1}{2^k},\frac{i}{2^k}}(\gamma_{\frac{i-1}{2^k}},\gamma_{\frac{i}{2^k}}),
$$

etc. So the right-hand side of (7.20) coincides with

$$
c^{\frac{i-1}{2^k},\frac{i}{2^k}}(\gamma_{\frac{i-1}{2^k}},\gamma_{\frac{i}{2^k}})+\ldots+c^{\frac{j}{2^k},\frac{j+1}{2^k}}(\gamma_{\frac{j}{2^k}},\gamma_{\frac{j+1}{2^k}}),
$$

and by construction of  $\Pi^{(k)}$  this is just  $c^{\frac{i-1}{2^k}, \frac{j+1}{2^k}}(\gamma_{\frac{i-1}{2^k}}, \gamma_{\frac{j+1}{2^k}})$ . So there has to be equality everywhere in  $(7.20)$ , which leads to  $(7.19)$ . (Here I use the fact that  $c^{s,t}(\gamma_s, \gamma_t) < +\infty$ .) After that it is an easy game to conclude the proof of the minimizing property for arbitrary times  $t_1, t_2, t_3.$ 

**Step 4.** To recapitulate: Starting from a curve  $(\mu_t)_{0 \leq t \leq 1}$ , we have constructed a family of probability measures  $\Pi^{(k)}$  which are all concentrated on the set  $\Gamma$  of minimizing curves, and satisfy  $(e_t)_\# \Pi^{(k)} = \mu_t$ for all  $t = j/2^k$ . It remains to pass to the limit as  $k \to \infty$ . For that we shall check the tightness of the sequence  $(\Pi^{(k)})_{k \in \mathbb{N}}$ . Let  $\varepsilon > 0$  be arbitrary. Since  $\mu_0$ ,  $\mu_1$  are tight, there are compact sets  $K_0$ ,  $K_1$  such that  $\mu_0[\mathcal{X} \setminus K_0] \leq \varepsilon$ ,  $\mu_1[\mathcal{X} \setminus K_1] \leq \varepsilon$ . From the coercivity of the action, the set  $\Gamma^{0,1}_{K_0}$  $K_0 \rightarrow K_1$  of action-minimizing curves joining  $K_0$  to  $K_1$  is compact, and  $\Pi\left[\Gamma \setminus \Gamma_{K_0}^{0,1}\right]$  $K_0 \rightarrow K_1$ is (with obvious notation)

$$
\mathbb{P}\left[\left(\gamma_0,\gamma_1\right)\notin K_0\times K_1\right] \leq \mathbb{P}\left[\gamma_0\notin K_0\right] + \mathbb{P}\left[\gamma_1\notin K_1\right] \n= \mu_0[\mathcal{X}\setminus K_0] + \mu_1[\mathcal{X}\setminus K_1] \leq 2\varepsilon.
$$

This proves the tightness of the family  $(\Pi^{(k)})$ . So one can extract a subsequence thereof, still denoted  $\Pi^{(k)}$ , that converges weakly to some probability measure  $\Pi$ .

By Proposition 7.16(v),  $\Gamma$  is closed; so  $\Pi$  is still supported in  $\Gamma$ . Moreover, for all dyadic time  $t = i/2^{\ell}$  in [0, 1], we have, if k is larger than  $\ell$ ,  $(e_t)_\# \Pi^{(k)} = \mu_t$ , and by passing to the limit we find that  $(e_t)_\# \Pi = \mu_t$  also.

By assumption,  $\mu_t$  depends continuously on  $t$ . So, to conclude that  $(e_t)_\# \Pi = \mu_t$  for all times  $t \in [0, 1]$  it now suffices to check the continuity of  $(e_t)_\#$ II as a function of t. In other words, if  $\varphi$  is an arbitrary bounded continuous function on  $\mathcal{X}$ , one has to show that

$$
\psi(t) = \mathbb{E}\,\varphi(\gamma_t)
$$

is a continuous function of t if  $\gamma$  is a random geodesic with law  $\Pi$ . But this is a simple consequence of the continuity of  $t \mapsto \gamma_t$  (for all  $\gamma$ ), and Lebesgue's dominated convergence theorem. This concludes Step 4, and the proof of (ii)  $\Rightarrow$  (i).

Next, let us check that the two expressions for  $\mathcal{A}^{s,t}$  in (iii) do coincide. This is about the same computation as in Step 1 above. Let  $s < t$ be given, let  $(\mu_{\tau})_{s\leq \tau \leq t}$  be a continuous path, and let  $(t_i)$  be a subdivision of [s, t]. Further, let  $\gamma$  be such that law  $(\gamma_{\tau}) = \mu_{\tau}$ , and let  $(X_s, X_t)$ be an optimal coupling of  $(\mu_s, \mu_t)$ , for the cost function  $c^{s,t}$ . Further, let  $(\gamma_{\tau})_{s\leq \tau\leq t}$  be a random continuous path, such that law  $(\gamma_{\tau}) = \mu_{\tau}$  for all  $\tau \in [s, t]$ . Then

$$
\sum_{i} C^{t_i, t_{i+1}}(\mu_{t_i}, \mu_{t_{i+1}}) \leq C^{s, t}(\mu_s, \mu_t) = \mathbb{E} c^{s, t}(X_s, X_t)
$$
  
$$
\leq \mathbb{E} c^{s, t}(\gamma_s, \gamma_t) \leq \mathbb{E} \mathcal{A}^{s, t}(\gamma),
$$

where the next-to-last inequality follows from the fact that  $(\gamma_s, \gamma_t)$  is a coupling of  $(\mu_s, \mu_t)$ , and the last inequality is a consequence of the definition of  $c^{s,t}$ . This shows that

$$
\sum_{i} C^{t_i, t_{i+1}}(\mu_{t_i}, \mu_{t_{i+1}}) \leq \mathbb{E} \mathcal{A}^{s, t}(\gamma).
$$

On the other hand, there is equality in the whole chain of inequalities if  $t_0 = s$ ,  $t_1 = t$ ,  $X_s = \gamma_s$ ,  $X_t = \gamma_t$ , and  $\gamma_\tau$  is a (random) actionminimizing curve. So the two expressions in (iii) do coincide.

Now let us address the equivalence between (ii) and (iii). First, it is clear that  $A^{s,t}$  is lower semicontinuous, since it is defined as a supremum of lower semicontinuous functionals. The inequality  $\mathbb{A}^{t_1,t_3} \geq$  $\mathbb{A}^{t_1,t_2} + \mathbb{A}^{t_2,t_3}$  holds true for all intermediate times  $t_1 < t_2 < t_3$  (this is a simple consequence of the definitions), and the converse inequality is a consequence of the general inequality

$$
s < t_2 < t \Longrightarrow C^{s,t_2}(\mu_s, \mu_{t_2}) \leq C^{s,t_2}(\mu_s, \mu_{t_2}) + C^{t_2,t}(\mu_{t_2}, \mu_t),
$$

which we proved in Step 1 above. So Property (i) in Definition 7.11 is satisfied. To check Property (ii) of that definition, take any two probability measures  $\mu_s$ ,  $\mu_t$  and introduce a displacement interpolation

 $(\mu_{\tau})_{s\leq \tau\leq t}$  for the Lagrangian action restricted to [s, t]. Then Property (ii) of Theorem 7.21 implies  $\mathbb{A}^{s,t}(\mu) = C^{s,t}(\mu_s, \mu_t)$ . Finally, Property (iii) in Definition 7.11 is also satisfied by construction. In the end,  $(A)$ does define a Lagrangian action, with induced cost functionals  $C^{s,t}$ .

To conclude the proof of Theorem 7.21 it only remains to check the coercivity of the action; then the equivalence of (i), (ii) and (iii) will follow from Proposition 7.16(iv). Let  $s < t$  be two given times in  $[0, 1]$ , and let  $\mathcal{K}_s$ ,  $\mathcal{K}_t$  be compact sets of probability measures such that  $C^{s,t}(\mu_s, \mu_t) < +\infty$  for all  $\mu_s \in \mathcal{K}_s$ ,  $\mu_t \in \mathcal{K}_t$ . Action-minimizing curves for  $\mathbb{A}^{s,t}$  can be written as law  $(\gamma_{\tau})_{s\leq \tau\leq t}$ , where  $\gamma$  is a random actionminimizing curve  $[s, t] \to \mathcal{X}$  such that  $\text{law}(\gamma_s) \in \mathcal{K}_s$ ,  $\text{law}(\gamma_t) \in \mathcal{K}_t$ . One can use an argument similar to the one in Step 4 above to prove that the laws  $\Pi$  of such minimizing curves form a tight, closed set; so we have a compact set of dynamical transference plans  $\Pi^{s,t}$ , that are probability measures on  $C([s,t];\mathcal{X})$ . The problem is to show that the paths  $(e_{\tau})_{\#}\Pi^{s,t}$  constitute a compact set in  $C([s,t];P(\mathcal{X}))$ . Since the continuous image of a compact set is compact, it suffices to check that the map

$$
\Pi^{s,t} \longmapsto ((e_{\tau})_{\#} \Pi^{s,t})_{s \le \tau \le t}
$$

is continuous from  $P(C([s,t]; \mathcal{X}))$  to  $C([s,t]; P(\mathcal{X}))$ . To do so, it will be convenient to metrize  $P(\mathcal{X})$  with the Wasserstein distance  $W_1$ , replacing if necessary d by a bounded, topologically equivalent distance. (Recall Corollary 6.13.) Then the uniform distance on  $C([s,t];\mathcal{X})$  is also bounded and there is an associated Wasserstein distance  $W_1$  on  $P(C([s,t];\mathcal{X}))$ . Let  $\Pi$  and  $\Pi$  be two dynamical optimal transference plans, and let  $((\gamma_{\tau}),(\tilde{\gamma}_{\tau}))$  be an optimal coupling of  $\Pi$  and  $\Pi$ ; let also  $\mu_{\tau}$ ,  $\tilde{\mu}_{\tau}$  be the associated displacement interpolations; then the required continuity follows from the chain of inequalities

$$
\sup_{t\in[0,1]}W_1(\mu_t,\widetilde{\mu}_t)\leq \sup_{t\in[0,1]}\mathbb{E}\,d(\gamma_t,\widetilde{\gamma}_t)\leq \mathbb{E}\sup_{t\in[0,1]}d(\gamma_t,\widetilde{\gamma}_t)=\mathcal{W}_1(\Pi,\widetilde{\Pi}).
$$

This proves that displacement interpolations with endpoints lying in given compact sets themselves form a compact set, and concludes the proof of the coercivity of the action  $(A)$ . □

**Remark 7.28.** In the proof of the implication (ii)  $\Rightarrow$  (i), instead of defining  $\Pi^{(k)}$  on the space of continuous curves, one could instead work with  $\Pi^{(k)}$  defined on discrete times, construct by compactness a consistent system of marginals on  $\mathcal{X}^{2^{\ell}+1}$ , for all  $\ell$ , and then invoke

Kolmogorov's existence theorem to get a  $\Pi$  which is defined on a set of curves. Things however are not that simple, since Kolmogorov's theorem constructs a random measurable curve which is not a priori continuous. Here one has the same conceptual catch as in the construction of Brownian motion as a probability measure on continuous paths.

Proof of Corollary 7.22. Introduce the family of actions

$$
\mathcal{A}^{s,t}(\gamma) = \int_s^t |\dot{\gamma}_\tau|^p \, d\tau.
$$

Then

$$
c^{s,t}(x,y) = \frac{d(x,y)^p}{(t-s)^{p-1}},
$$

and all our assumptions hold true for this action and cost. (The assumption of local compactness is used to prove that the action is coercive, see the Appendix.) The important point now is that

$$
C^{s,t}(\mu,\nu) = \frac{W_p(\mu,\nu)^p}{(t-s)^{p-1}}.
$$

So, according to the remarks in Example 7.9, Property (ii) in Theorem 7.21 means that  $(\mu_t)$  is in turn a minimizer of the action associated with the Lagrangian  $|\dot{\mu}|^p$ , i.e. a geodesic in  $P_p(\mathcal{X})$ . Note that if  $\mu_t$  is the law of a random optimal geodesic  $\gamma_t$  at time t, then

$$
W_p(\mu_t, \mu_s)^p \leq \mathbb{E} d(\gamma_s, \gamma_t)^p = \mathbb{E} (t - s)^p d(\gamma_0, \gamma_1)^p = (t - s)^p W_p(\mu_0, \mu_1)^p,
$$

so the path  $\mu_t$  is indeed continuous (and actually 1-Lipschitz) for the distance  $W_p$ .

Proof of Corollary 7.23. By Theorem 7.21, any displacement interpolation has to take the form  $(e_t)_\# \Pi$ , where  $\Pi$  is a probability measure on action-minimizing curves such that  $\pi := (e_0, e_1)_\# \Pi$  is an optimal transference plan. By assumption, there is exactly one such  $\pi$ . Let Z be the set of pairs  $(x_0, x_1)$  such that there is more than one minimizing curve joining  $x_0$  to  $x_1$ ; by assumption  $\pi[Z] = 0$ . For  $(x_0, x_1) \notin Z$ , there is a unique geodesic  $\gamma = S(x_0, x_1)$  joining  $x_0$  to  $x_1$ . So  $\Pi$  has to coincide with  $S_{\#}\pi$ . □

To conclude this section with a simple application, I shall use Corollary 7.22 to derive the Lipschitz continuity of moments, alluded to in Remark 6.10.

Proposition 7.29 ( $W_p$ -Lipschitz continuity of p-moments). Let  $(X, d)$  be a locally compact Polish length space, let  $p \geq 1$  and  $\mu, \nu \in$  $P_p(\mathcal{X})$ . Then for any  $\varphi \in \text{Lip}(\mathcal{X}; \mathbb{R}_+),$ 

$$
\left| \left( \int \varphi(x)^p \, \mu(dx) \right)^{\frac{1}{p}} - \left( \int \varphi(y)^p \, \nu(dy) \right)^{\frac{1}{p}} \right| \leq ||\varphi||_{\text{Lip}} \, W_p(\mu, \nu).
$$

*Proof of Proposition 7.29.* Without loss of generality,  $\|\varphi\|_{\text{Lip}} = 1$ . The case  $p = 1$  is obvious from (6.3) and holds true for any Polish space, so I shall assume  $p > 1$ . Let  $(\mu_t)_{0 \le t \le 1}$  be a displacement interpolation between  $\mu_0 = \mu$  and  $\mu_1 = \nu$ , for the cost function  $c(x, y) = d(x, y)^p$ . By Corollary 7.22, there is a probability measure  $\Pi$  on  $\Gamma$ , the set of geodesics in X, such that  $\mu_t = (e_t)_\# \Pi$ .

Then let  $\Psi(t) = \int_{\mathcal{X}} \varphi(x)^p \mu_t(dx) = \int_{\Gamma} \varphi(\gamma_t)^p \Pi(d\gamma)$ . By Fatou's lemma and Hölder's inequality,

$$
\frac{d^+ \Psi}{dt} \leq \int_{\Gamma} \frac{d^+ \varphi(\gamma_t)^p}{dt} \, \Pi(d\gamma)
$$

$$
\leq p \int \varphi(\gamma_t)^{p-1} \, |\dot{\gamma}_t| \, \Pi(d\gamma)
$$

$$
\leq p \left( \int \varphi(\gamma_t)^p \, \Pi(d\gamma) \right)^{1-\frac{1}{p}} \left( \int |\dot{\gamma}_t|^p \, \Pi(d\gamma) \right)^{\frac{1}{p}}
$$

$$
= p \Psi(t)^{1-\frac{1}{p}} \left( \int d(\gamma_0, \gamma_1)^p \, \Pi(d\gamma) \right)^{\frac{1}{p}}
$$

$$
= p \Psi(t)^{1-\frac{1}{p}} \, W_p(\mu, \nu).
$$

So  $(d^+/dt)[\Psi(t)^{1/p}] \leq W_p(\mu,\nu)$ , thus  $|\Psi(1)^{1/p} - \Psi(0)^{1/p}| \leq W_p(\mu,\nu)$ , which is the desired result.

## Displacement interpolation between intermediate times and restriction

Again let  $\mu_0$  and  $\mu_1$  be any two probability measures,  $(\mu_t)_{0 \leq t \leq 1}$  a displacement interpolation associated with a dynamical optimal transference plan  $\Pi$ , and  $(\gamma_t)_{0 \leq t \leq 1}$  a random action-minimizing curve with law  $(\gamma) = \Pi$ . In particular  $(\gamma_0, \gamma_1)$  is an optimal coupling of  $(\mu_0, \mu_1)$ . With the help of the previous arguments, it is almost obvious that  $(\gamma_{t_0}, \gamma_{t_1})$  is also an optimal coupling of  $(\mu_{t_0}, \mu_{t_1})$ . What may look at first sight more surprising is that if  $(t_0, t_1) \neq (0, 1)$ , this is the *only* optimal coupling, at least if action-minimizing curves "cannot branch". Furthermore, there is a time-dependent version of Theorem 4.6.

Theorem 7.30 (Interpolation from intermediate times and restriction). Let  $X$  be a Polish space equipped with a coercive action (A) on  $C([0,1];\mathcal{X})$ . Let  $\Pi \in P(C([0,1];\mathcal{X}))$  be a dynamical optimal transport plan associated with a finite total cost. For any  $t_0, t_1$  in [0, 1] with  $0 \le t_0 < t_1 \le 1$ , define the time-restriction of  $\Pi$  to  $[t_0, t_1]$  as  $\Pi^{t_0,t_1} := (r_{t_0,t_1}) \# \Pi$ , where  $r_{t_0,t_1}(\gamma)$  is the restriction of  $\gamma$  to the interval  $[t_0,t_1]$ . Then:

(i)  $\Pi^{t_0,t_1}$  is a dynamical optimal coupling for the action  $(A)^{t_0,t_1}$ .

(ii) If  $\widetilde{\Pi}$  is a measure on  $C([t_0,t_1];\mathcal{X})$ , such that  $\widetilde{\Pi} \leq \Pi^{t_0,t_1}$  and  $\Pi[C([t_0,t_1];\mathcal{X})] > 0, let$ 

$$
\Pi' := \frac{\widetilde{\Pi}}{\widetilde{\Pi}\big[C([t_0, t_1]; \mathcal{X})\big]}, \qquad \mu'_t = (e_t)_{\#} \Pi'.
$$

Then  $\Pi'$  is a dynamical optimal coupling between  $\mu'_{t_0}$  and  $\mu'_{t_1}$ ; and  $(\mu_t')_{t_0 \leq t \leq t_1}$  is a displacement interpolation.

(iii) Further, assume that action-minimizing curves are uniquely and measurably determined by their restriction to a nontrivial timeinterval, and  $(t_0, t_1) \neq (0, 1)$ . Then,  $\Pi'$  in (ii) is the unique dynamical optimal coupling between  $\mu'_{t_0}$  and  $\mu'_{t_1}$ . In particular,  $(\pi')^{t_0,t_1} :=$  $(e_{t_0}, e_{t_1})_{\#}\Pi'$  is the unique optimal transference plan between  $\mu'_{t_0}$  and  $\mu'_{t_1}$ ; and  $\mu'_t := (e_t)_\# \Pi'$  ( $t_0 \le t \le t_1$ ) is the unique displacement interpolation between  $\mu'_{t_0}$  and  $\mu'_{t_1}$ .

(iv) Under the same assumptions as in (iii), for any  $t \in (0,1)$ ,  $(\Pi \otimes \Pi)(d\gamma d\widetilde{\gamma})$ -almost surely,

$$
[\gamma_t = \widetilde{\gamma}_t] \Longrightarrow [\gamma = \widetilde{\gamma}].
$$

In other words, the curves seen by  $\Pi$  cannot cross at intermediate times. If the costs  $c^{s,t}$  are continuous, this conclusion extends to all curves  $\gamma, \widetilde{\gamma} \in \text{Spt } \Pi$ .

 $(v)$  Under the same assumptions as in (iii), there is a measurable map  $F_t: \mathcal{X} \to \Gamma(\mathcal{X})$  such that,  $\Pi(d\gamma)$ -almost surely,  $F_t(\gamma_t) = \gamma$ .

Remark 7.31. In Chapter 8 we shall work in the setting of smooth Riemannian manifolds and derive a quantitative variant of the "nocrossing" property expressed in (iv).

Corollary 7.32 (Nonbranching is inherited by the Wasserstein space). Let  $(X, d)$  be a complete separable, locally compact length space and let  $p \in (1,\infty)$ . Assume that X is nonbranching, in the sense that a geodesic  $\gamma : [0,1] \to \mathcal{X}$  is uniquely determined by its restriction to a nontrivial time-interval. Then also the Wasserstein space  $P_p(\mathcal{X})$  is nonbranching. Conversely, if  $P_p(\mathcal{X})$  is nonbranching, then  $\mathcal X$  is nonbranching.

*Proof of Theorem 7.30.* Let  $\gamma$  be a random geodesic with law  $\Pi$ . Let  $\gamma^{0,t_0}$ ,  $\gamma^{t_0,t_1}$  and  $\gamma^{t_1,1}$  stand for the restrictions of  $\gamma$  to the time intervals  $[0,t_0]$ ,  $[t_0,t_1]$  and  $[t_1,1]$ , respectively. Then

$$
C^{0,t_0}(\mu_0, \mu_{t_0}) + C^{t_0,t_1}(\mu_{t_0}, \mu_{t_1}) + C^{t_1,1}(\mu_{t_1}, \mu_1)
$$

$$
\leq \mathbb{E} c^{0,t_0}(\gamma^{0,t_0}) + \mathbb{E} c^{t_0,t_1}(\gamma^{t_0,t_1}) + \mathbb{E} c^{t_1,1}(\gamma^{t_1,1})
$$

$$
= \mathbb{E} c^{0,1}(\gamma) = C^{0,1}(\mu_0, \mu_1)
$$

$$
\leq C^{0,t_0}(\mu_0, \mu_{t_0}) + C^{t_0,t_1}(\mu_{t_0}, \mu_{t_1}) + C^{t_1,1}(\mu_{t_1}, \mu_1).
$$

So there has to be equality in all the inequalities, and it follows that

$$
\mathbb{E} c^{t_0,t_1}(\gamma^{t_0},\gamma^{t_1})=C^{t_0,t_1}(\mu_{t_0},\mu_{t_1}).
$$

So  $\gamma^{t_0,t_1}$  is optimal, and  $\Pi^{t_0,t_1}$  is a dynamical optimal transference plan. Statement (i) is proven.

As a corollary of (i),  $\pi^{t_0,t_1} = (e_{t_0}, e_{t_1}) \# \Pi^{t_0,t_1}$  is an optimal transference plan between  $\mu_{t_0}$  and  $\mu_{t_1}$ . Let  $\tilde{\pi} := (e_{t_0}, e_{t_1})_{\#}\Pi$ . The inequality  $\widetilde{H} \leq H^{t_0,t_1}$  is preserved by push-forward, so  $\widetilde{\pi} \leq \pi^{t_0,t_1}$ . Also  $[\widetilde{\pi}[\mathcal{X} \times \mathcal{X}] = \Pi[C([t_0, t_1]; \mathcal{X})] > 0.$  By Theorem 4.6,  $\pi' := \widetilde{\pi}/\widetilde{\pi}[\mathcal{X} \times \mathcal{X}]$ is an optimal transference plan between its marginals. But  $\pi'$  coincides with  $(e_{t_0}, e_{t_1})_{\#}\Pi'$ , and since  $\Pi'$  is concentrated (just as  $\Pi$ ) on actionminimizing curves, Theorem 7.21 guarantees that  $\Pi'$  is a dynamical optimal transference plan between its marginals. This proves (ii). (The continuity of  $\mu'_t$  in t is shown by the same argument as in Step 4 of the proof of Theorem 7.21.)

To prove (iii), assume, without loss of generality, that  $t_0 > 0$ ; then an action-minimizing curve  $\gamma$  is uniquely and measurably determined

by its restriction  $\gamma^{0,t_0}$  to [0,t<sub>0</sub>]. In other words, there is a measurable function  $F^{0,t_0}: \Gamma^{0,t_0} \to \Gamma$ , defined on the set of all  $\gamma^{0,t_0}$ , such that any action-minimizing curve  $\gamma : [0,1] \rightarrow \mathcal{X}$  can be written as  $F^{0,t_0}(\gamma^{0,t_0})$ . Similarly, there is also a measurable function  $F^{t_0,t_1}$  such that  $F^{t_0,t_1}(\gamma^{t_0,t_1}) = \gamma$ .

By construction,  $\tilde{\Pi}$  is concentrated on the curves  $\gamma^{t_0,t_1}$ , which are the restrictions to  $[t_0, t_1]$  of the action-minimizing curves  $\gamma$ . Then let  $\overline{\Pi} := (F^{t_0,t_1}) \# \overline{\Pi}$ ; this is a probability measure on  $C([0,1];\mathcal{X})$ . Of course  $\overline{\Pi} \leq (F^{t_0,t_1})_{\#}\Pi^{t_0,t_1} = \Pi$ ; so by (ii),  $\overline{\Pi}/\overline{\Pi}[C([0,1];\mathcal{X})]$  is optimal. (In words,  $\overline{II}$  is obtained from  $\overline{II}$  by extending to the time-interval  $[0, 1]$  those curves which appear in the sub-plan  $\Pi'$ .) Then it is easily seen that  $\Pi' = (r_{t_0,t_1})_{\#}\Pi$ , and  $\Pi[C([0,1];\mathcal{X})] = \Pi'[C([t_0,t_1];\mathcal{X})]$ . So it suffices to prove Theorem 7.30(iii) in the case when  $\tilde{\Pi} = \Pi^{t_0,t_1}$ , and this will be assumed in the sequel.

Now let  $\gamma$  be a random geodesic with law  $\Pi$ , and  $\Pi^{0,t_0} = \text{law}(\gamma^{0,t_0})$ ,  $\Pi^{t_0,t_1} = \text{law}(\gamma^{t_0,t_1}), \Pi^{t_1,1} = \text{law}(\gamma^{t_1,1}).$  By (i),  $\Pi^{t_0,t_1}$  is a dynamical optimal transference plan between  $\mu_{t_0}$  and  $\mu_{t_1}$ ; let  $\overline{\Pi}^{t_0,t_1}$  be another such plan. The goal is to show that  $\overline{H}^{t_0,t_1} = \overline{H}^{t_0,t_1}$ .

Disintegrate  $\Pi^{0,t_0}$  and  $\widetilde{\Pi}^{t_0,t_1}$  along their common marginal  $\mu_{t_0}$  and glue them together. This gives a probability measure on  $C([0, t_0); \mathcal{X}) \times$  $\mathcal{X} \times C((t_0,t_1];\mathcal{X})$ , supported on triples  $(\gamma,g,\widetilde{\gamma})$  such that  $\gamma(t) \to g$  as  $t \to t_0^-, \tilde{\gamma}(t) \to g$  as  $t \to t_0^+$ . Such triples can be identified with continuous functions on  $[0, t_1]$ , so what we have is in fact a probability measure on  $C([0, t_1]; \mathcal{X})$ . Repeat the operation by gluing this with  $\Pi^{t_1,1}$ , so as to get a probability measure  $\overline{\Pi}$  on  $C([0, 1]; \mathcal{X})$ .

Then let  $\overline{\gamma}$  be a random variable with law  $\overline{\Pi}$ : By construction,  $\text{law}(\overline{\gamma}^{0,t_0}) = \text{law}(\gamma^{0,t_0}), \text{ and law}(\overline{\gamma}^{t_1,1}) = \text{law}(\gamma^{t_1,1}), \text{ so}$ 

$$
\mathbb{E} c^{0,1}(\overline{\gamma}) \leq \mathbb{E} c^{0,t_0}(\overline{\gamma}^{0,t_0}) + \mathbb{E} c^{t_0,t_1}(\overline{\gamma}^{t_0,t_1}) + \mathbb{E} c^{t_1,1}(\overline{\gamma}^{t_1,1})
$$
  
$$
= \mathbb{E} c^{0,t_0}(\gamma^{0,t_0}) + \mathbb{E} c^{t_0,t_1}(\overline{\gamma}^{t_0,t_1}) + \mathbb{E} c^{t_1,1}(\gamma^{t_1,1})
$$
  
$$
= C^{0,t_0}(\mu_0, \mu_{t_0}) + C^{t_0,t_1}(\mu_{t_0}, \mu_{t_1}) + C^{t_1,1}(\mu_{t_1}, \mu_1)
$$
  
$$
= C^{0,1}(\mu_0, \mu_1).
$$

Thus  $\overline{\Pi}$  is a dynamical optimal transference plan between  $\mu_0$  and  $\mu_1$ . It follows from Theorem 7.21 that there is a random action-minimizing curve  $\hat{\gamma}$  with law  $(\hat{\gamma}) = \overline{\Pi}$ . In particular,

$$
\operatorname{law}(\widehat{\gamma}^{0,t_0}) = \operatorname{law}(\gamma^{0,t_0}); \qquad \operatorname{law}(\widehat{\gamma}^{t_0,t_1}) = \operatorname{law}(\widehat{\gamma}^{t_0,t_1}).
$$

By assumption there is a measurable function  $F$   $(F = r_{t_0,t_1} \circ F^{0,t_0})$ such that  $g^{t_0,t_1} = F(g^{0,t_0})$ , for any action-minimizing curve g. So

law 
$$
(\tilde{\gamma}^{t_0,t_1}) = \text{law } (\hat{\gamma}^{t_0,t_1}) = \text{law } (F(\hat{\gamma}^{0,t_0})) = \text{law } (F(\gamma^{0,t_0})) = \text{law } (\gamma^{t_0,t_1}).
$$

This proves the uniqueness of the dynamical optimal transference plan joining  $\mu'_{t_0}$  to  $\mu'_{t_1}$ . The remaining part of (iii) is obvious since any optimal plan or displacement interpolation has to come from a dynamical optimal transference plan, according to Theorem 7.21.

Now let us turn to the proof of (iv). Since the plan  $\pi = (e_0, e_1)_\# \Pi$ is  $c^{0,1}$ -cyclically monotone (Theorem 5.10(ii)), we have,  $\Pi \otimes \Pi(d\gamma d\tilde{\gamma})$ almost surely,

$$
c^{0,1}(\gamma_0, \gamma_1) + c^{0,1}(\tilde{\gamma}_0, \tilde{\gamma}_1) \le c^{0,1}(\gamma_0, \tilde{\gamma}_1) + c^{0,1}(\tilde{\gamma}_0, \gamma_1), \tag{7.21}
$$

and all these quantities are finite (almost surely).

If  $\gamma$  and  $\tilde{\gamma}$  are two such paths, assume that  $\gamma_t = \tilde{\gamma}_t = X$  for some  $t \in (0, 1)$ . Then

$$
c^{0,1}(\gamma_0, \tilde{\gamma}_1) \le c^{0,t}(\gamma_0, X) + c^{t,1}(X, \tilde{\gamma}_1), \tag{7.22}
$$

and similarly

$$
c^{0,1}(\tilde{\gamma}_0, \gamma_1) \le c^{0,t}(\tilde{\gamma}_0, X) + c^{t,1}(X, \gamma_1).
$$
 (7.23)

By adding up  $(7.22)$  and  $(7.23)$ , we get

$$
c^{0,1}(\gamma_0, \widetilde{\gamma}_1) + c^{0,1}(\widetilde{\gamma}_0, \gamma_1)
$$

$$
\leq [c^{0,t}(\gamma_0, X) + c^{t,1}(X, \gamma_1)] + [c^{0,t}(\widetilde{\gamma}_0, X) + c^{t,1}(X, \widetilde{\gamma}_1)]
$$

$$
= c^{0,1}(\gamma_0, \gamma_1) + c^{0,1}(\widetilde{\gamma}_0, \widetilde{\gamma}_1).
$$

Since the reverse inequality holds true by (7.21), equality has to hold in all intermediate inequalities, for instance in (7.22). Then it is easy to see that the path  $\overline{\gamma}$  defined by  $\overline{\gamma}(s) = \gamma(s)$  for  $0 \leq s \leq t$ , and  $\overline{\gamma}(s) = \tilde{\gamma}(s)$  for  $s \geq t$ , is a minimizing curve. Since it coincides with  $\gamma$ on a nontrivial time-interval, it has to coincide with  $\gamma$  everywhere, and similarly it has to coincide with  $\widetilde{\gamma}$  everywhere. So  $\gamma = \widetilde{\gamma}$ .

If the costs  $c^{s,t}$  are continuous, the previous conclusion holds true not only  $\Pi \otimes \Pi$ -almost surely, but actually for any two minimizing curves  $\gamma$ ,  $\tilde{\gamma}$  lying in the support of  $\Pi$ . Indeed, inequality (7.21) defines a closed set C in  $\Gamma \times \Gamma$ , where  $\Gamma$  stands for the set of minimizing curves; so  $\operatorname{Spt} \Pi \times \operatorname{Spt} \Pi = \operatorname{Spt} (\Pi \otimes \Pi) \subset \mathcal{C}$ .

It remains to prove (v). Let  $\Gamma^{0,1}$  be a c-cyclically monotone subset of  $\mathcal{X} \times \mathcal{X}$  on which  $\pi$  is concentrated, and let  $\Gamma$  be the set of minimizing curves  $\gamma : [0,1] \to \mathcal{X}$  such that  $(\gamma_0, \gamma_1) \in \Gamma^{0,1}$ . Let  $(K_{\ell})_{\ell \in \mathbb{N}}$  be a nondecreasing sequence of compact sets contained in  $\Gamma$ , such that  $\Pi[\cup K_\ell] = \Pi[\Gamma] = 1$ . For each  $\ell$ , we define  $F_\ell$  on  $e_t(K_\ell)$  by  $F_\ell(\gamma_t) = \gamma$ . This map is continuous: Indeed, if  $x_k \in e_t(K_\ell)$  converges to x, then for each k we have  $x_k = (\gamma_k)_t$  for some  $\gamma_k \in K_{\ell}$ , and up to extraction  $\gamma_k$ converges to  $\gamma \in K_{\ell}$ , and in particular  $(\gamma_k)_t$  converges to  $\gamma_t$ ; but then  $F_{\ell}(\gamma_t) = \gamma$ . Then we can define F on  $\cup K_{\ell}$  as a map which coincides with  $F_{\ell}$  on each  $K_{\ell}$ . (Obviously, this is the same line of reasoning as in Theorem 5.30.) □

*Proof of Corollary 7.32.* Assume that  $X$  is nonbranching. Then there exists some function F, defined on the set of all curves  $\gamma^{0,t_0}$ , where  $\gamma^{0,t_0}$  is the restriction to  $[0,t_0]$  of the geodesic  $\gamma : [0,1] \to \mathcal{X}$ , such that  $\gamma = F(\gamma^{0,t_0}).$ 

I claim that F is automatically continuous. Indeed, let  $(\gamma_n)_{n\in\mathbb{N}}$  be such that  $\gamma_n^{0,t_0}$  converges uniformly on  $[0,t_0]$  to some  $g: [0,t_0] \to \mathcal{X}$ . Since the functions  $\gamma_n^{0,t_0}$  are uniformly bounded, the speeds of all the geodesics  $\gamma_n$  are uniformly bounded too, and the images  $\gamma_n([0,1])$  are all included in a compact subset of  $X$ . It follows from Ascoli's theorem that the sequence  $(\gamma_n)$  converges uniformly, up to extraction of a subsequence. But then its limit  $\gamma$  has to be a geodesic, and its restriction to  $[0, t_0]$  coincides with g. There is at most one such geodesic, so  $\gamma$  is uniquely determined, and the whole sequence  $\gamma_n$  converges. This implies the continuity of F.

Then Theorem 7.30(iii) applies: If  $(\mu_t)_{0 \leq t \leq 1}$  is a geodesic in  $P_p(\mathcal{X})$ , there is only one geodesic between  $\mu_{t_0}$  and  $\mu_1$ . So  $(\mu_t)_{0 \leq t \leq 1}$  is uniquely determined by its restriction to  $[0, t_0]$ . The same reasoning could be done for any nontrivial time-interval instead of  $[0,t_0]$ ; so  $P_p(\mathcal{X})$  is indeed nonbranching.

The converse implication is obvious, since any geodesic  $\gamma$  in X induces a geodesic in  $P_p(\mathcal{X})$ , namely  $(\delta_{\gamma(t)})_{0 \le t \le 1}$ .  $\Box$ 

## Interpolation of prices

When the path  $\mu_t$  varies in time, what becomes of the pair of "prices"  $(\psi,\phi)$  in the Kantorovich duality? The short answer is that these functions will also evolve continuously in time, according to Hamilton– Jacobi equations.

Definition 7.33 (Hamilton–Jacobi–Hopf–Lax–Oleinik evolution **semigroup).** Let X be a metric space and  $(A)^{0,1}$  a coercive Lagrangian action on  $\mathcal{X}$ , with cost functions  $(c^{s,t})_{0 \leq s < t \leq 1}$ . For any two functions  $\psi : \mathcal{X} \to \mathbb{R} \cup \{+\infty\}, \ \phi : \mathcal{X} \to \mathbb{R} \cup \{-\infty\}, \$ and any two times  $0 \leq s \leq t \leq 1$ , define

\left\{
$$
H_{+}^{s,t}\psi(y) = \inf_{x \in \mathcal{X}} \left( \psi(x) + c^{s,t}(x, y) \right);
$$

$$
H_{-}^{t,s}\phi(x) = \sup_{y \in \mathcal{X}} \left( \phi(y) - c^{s,t}(x, y) \right).
$$
\right\}

The family of operators  $(H_+^{s,t})_{t>s}$  (resp.  $(H_-^{s,t})_{s) is called the for$ ward (resp. backward) Hamilton–Jacobi (or Hopf–Lax, or Lax–Oleinik) semigroup.

Roughly speaking,  $H^{s,t}_+$  gives the values of  $\psi$  at time t, from its values at time s; while  $H_{-}^{s,t}$  does the reverse. So the semigroups  $H_{-}$  and  $H_{+}$ are in some sense inverses of each other. Yet it is not true in general that  $H_-^{t,s} H_+^{s,t} = \mathrm{Id}$ . Proposition 7.34 below summarizes some of the main properties of these semigroups; the denomination of "semigroup" itself is justified by Property (ii).

Proposition 7.34 (Elementary properties of Hamilton–Jacobi semigroups). With the notation of Definition 7.33,

- (i)  $H^{s,t}_+$  and  $H^{s,t}_-$  are order-preserving:  $\psi \leq \overline{\psi} \Longrightarrow H^{s,t}_\pm \psi \leq H^{s,t}_\pm \overline{\psi}$ .
- (ii) Whenever  $t_1 < t_2 < t_3$  are three intermediate times in [0, 1],

$$
\left\{
$$

$$
H_+^{t_2,t_3} H_+^{t_1,t_2} = H_+^{t_1,t_3}
$$

$$
H_-^{t_2,t_1} H_-^{t_3,t_2} = H_-^{t_3,t_1}
$$

(iii) Whenever  $s < t$  are two times in [0, 1],

$$
H_{-}^{t,s}H_{+}^{s,t} \leq \text{Id}\,;\qquad H_{+}^{s,t}H_{-}^{t,s} \geq \text{Id}\,.
$$

Proof of Proposition 7.34. Properties (i) and (ii) are immediate consequences of the definitions and Proposition 7.16(iii). To check Property (iii), e.g. the first half of it, write

$$
H_{-}^{t,s}(H_{+}^{s,t}\psi)(x) = \sup_{y} \inf_{x'} \left( \psi(x') + c^{s,t}(x',y) - c^{s,t}(x,y) \right).
$$

The choice  $x' = x$  shows that the infimum above is bounded above by  $\psi(x)$ , independently of y; so  $H_-^{t,s}(H_+^{s,t}\psi)(x) \leq \psi(x)$ , as desired.  $\Box$ 

The Hamilton–Jacobi semigroup is well-known and useful in geometry and dynamical systems theory. On a smooth Riemannian manifold, when the action is given by a Lagrangian  $L(x, v, t)$ , strictly convex and superlinear in the velocity variable, then  $S_+(t, \cdot) := H_+^{0,t} \psi_0$  solves the differential equation

$$
\frac{\partial S_+}{\partial t}(x,t) + H\big(x,\nabla S_+(x,t),t\big) = 0,\t(7.24)
$$

where  $H = L^*$  is obtained from L by Legendre transform in the v variable, and is called the Hamiltonian of the system. This equation provides a bridge between a Lagrangian description of action-minimizing curves, and an Eulerian description: From  $S_+(x,t)$  one can reconstruct a velocity field  $v(x,t) = \nabla_p H(x, \nabla S_+(x,t), t)$ , in such a way that integral curves of the equation  $\dot{x} = v(x, t)$  are minimizing curves. Well, rigorously speaking, that would be the case if  $S_{+}$  were differentiable! But things are not so simple because  $S_+$  is not in general differentiable everywhere, so the equation has to be interpreted in a suitable sense (called viscosity sense). It is important to note that if one uses the backward semigroup and defines  $S_-(x,t) := H_-^{t,1} \psi_t$ , then  $S_-$  formally satisfies the same equation as  $S_{+}$ , but the equation has to be interpreted with a different convention (backward viscosity). This will be illustrated by the next example.

Example 7.35. On a Riemannian manifold  $M$ , consider the simple Lagrangian cost  $L(x, v, t) = |v|^2/2$ ; then the associated Hamiltonian is just  $H(x,p,t) = |p|^2/2$ . If S is a  $C^1$  solution of  $\partial S/\partial t + |\nabla S|^2/2 = 0$ , then the gradient of  $S$  can be interpreted as the velocity field of a family of minimizing geodesics. But if  $S_0$  is a given Lipschitz function and  $S_{+}(t,x)$  is defined by the forward Hamilton–Jacobi semigroup starting from initial datum  $S_0$ , one only has (for all  $t, x$ )

$$
\frac{\partial S_+}{\partial t} + \frac{|\nabla^{\scriptscriptstyle -} S_+|^2}{2} = 0,
$$

where

$$
|\nabla^- f|(x) := \limsup_{y \to x} \frac{[f(y) - f(x)]}{d(x, y)}, \qquad z_- = \max(-z, 0).
$$

Conversely, if one uses the backward Hamilton–Jacobi semigroup to define a function  $S_-(x,t)$ , then

$$
\frac{\partial S_{-}}{\partial t} + \frac{|\nabla^{+} S_{-}|^{2}}{2} = 0, \qquad |\nabla^{+} f|(x) := \limsup_{y \to x} \frac{[f(y) - f(x)]_{+}}{d(x, y)},
$$

where now  $z_+ = \max(z, 0)$ . When the Lagrangian is more complicated, things may become much more intricate. The standard convention is to use the forward Hamilton–Jacobi semigroup by default.

We shall now see that the Hamilton–Jacobi semigroup provides a simple answer to the problem of interpolation in dual variables. In the next statement,  $\mathcal X$  is again a Polish space,  $(\mathcal A)^{0,1}$  a coercive Lagrangian action on  $\mathcal{X}$ , with associated cost functions  $c^{s,t}$ ; and  $C^{s,t}$  stands for the optimal total cost in the transport problem with cost  $c^{s,t}$ .

Theorem 7.36 (Interpolation of prices). With the same assumptions and notation as in Definition 7.33, let  $\mu_0$ ,  $\mu_1$  be two probability measures on X, such that  $C^{0,1}(\mu_0, \mu_1) < +\infty$ , and let  $(\psi_0, \phi_1)$  be a pair of  $c^{0,1}$ -conjugate functions such that any optimal plan  $\pi_{0,1}$  between  $\mu_0$ and  $\mu_1$  has its support included in  $\partial_{c^{0,1}}\psi_0$ . (Recall Theorem 5.10; under adequate integrability conditions, the pair  $(\psi_0, \phi_1)$  is just a solution of the dual Kantorovich problem.) Further, let  $(\mu_t)_{0 \leq t \leq 1}$  be a displacement interpolation between  $\mu_0$  and  $\mu_1$ . Whenever  $s < t$  are two intermediate times in [0, 1], define

$$
\psi_s := H_+^{0,s} \psi_0, \qquad \phi_t := H_-^{1,t} \phi_1.
$$

Then  $(\psi_s, \phi_t)$  is optimal in the dual Kantorovich problem associated to  $(\mu_s, \mu_t)$  and  $c^{s,t}$ . In particular,

$$
C^{s,t}(\mu_s, \mu_t) = \int \phi_t \, d\mu_t - \int \psi_s \, d\mu_s,
$$

and

$$
\phi_t(y) - \psi_s(x) \le c^{s,t}(x, y),
$$

with equality  $\pi_{s,t}(dx\,dy)$ -almost surely, where  $\pi_{s,t}$  is any optimal transference plan between  $\mu_s$  and  $\mu_t$ .

Proof of Theorem 7.36. From the definitions,

$$
\begin{aligned} \phi_t(y) - \psi_s(x) - c^{s,t}(x, y) \\ &= \sup_{y',x'} \left[ \phi_1(y') - c^{t,1}(y', y) - \psi_0(x') - c^{0,s}(x', x) - c^{s,t}(x, y) \right] \end{aligned}
$$

.

Since  $c^{0,s}(x',x) + c^{s,t}(x,y) + c^{t,1}(y,y') \ge c^{0,1}(x',y')$ , it follows that

$$
\phi_t(y) - \psi_s(x) - c^{s,t}(x, y) \le \sup_{y', x'} \Big[ \phi_1(y') - \psi_0(x') - c^{0,1}(x', y') \Big] \le 0.
$$

So  $\phi_t(y) - \psi_s(x) \leq c^{s,t}(x, y)$ . This inequality does not depend on the fact that  $(\psi_0, \phi_1)$  is a tight pair of prices, in the sense of (5.5), but only on the inequality  $\phi_1 - \psi_0 \leq c^{0,1}$ .

Next, introduce a random action-minimizing curve  $\gamma$  such that  $\mu_t = \text{law}(\gamma_t)$ . Since  $(\psi_0, \phi_1)$  is an optimal pair, we know from Theorem 5.10(ii) that, almost surely,

$$
\phi_1(\gamma_1) - \psi_0(\gamma_0) = c^{0,1}(\gamma_0, \gamma_1).
$$

From the identity  $c^{0,1}(\gamma_0, \gamma_1) = c^{0,s}(\gamma_0, \gamma_s) + c^{s,t}(\gamma_s, \gamma_t) + c^{t,1}(\gamma_t, \gamma_1)$  and the definition of  $\psi_s$  and  $\phi_t$ ,

$$
c^{s,t}(\gamma_s, \gamma_t) = [\phi_1(\gamma_1) - c^{t,1}(\gamma_t, \gamma_1)] - [\psi_0(\gamma_0) + c^{0,s}(\gamma_0, \gamma_s)] \le \phi_t(\gamma_t) - \psi_s(\gamma_s).
$$

This shows that actually  $c^{s,t}(\gamma_s, \gamma_t) = \phi_t(\gamma_t) - \psi_s(\gamma_s)$  almost surely, so  $(\psi_s, \phi_t)$  has to be optimal in the dual Kantorovich problem between  $\mu_s = \text{law}(\gamma_s) \text{ and } \mu_t = \text{law}(\gamma_t).$ 

**Remark 7.37.** In the limit case  $s \to t$ , the above results become

$$
\begin{cases} \phi_t \leq \psi_t \\ \phi_t = \psi_t \quad \text{$\mu_t$-almost surely} \end{cases}
$$

... but it is not true in general that  $\phi_t = \psi_t$  everywhere in  $\mathcal{X}$ .

**Remark 7.38.** However, the identity  $\psi_1 = \phi_1$  holds true everywhere as a consequence of the definitions.

Exercise 7.39. After reading the rest of Part I, the reader can come back to this exercise and check his or her understanding by proving that, for a quadratic Lagrangian:

(i) The displacement interpolation between two balls in Euclidean space is always a ball, whose radius increases linearly in time (here I am identifying a set with the uniform probability measure on this set).

(ii) More generally, the displacement interpolation between two ellipsoids is always an ellipsoid.

(iii) But the displacement interpolation between two sets is in general not a set.

(iv) The displacement interpolation between two spherical caps on the sphere is in general not a spherical cap.

(v) The displacement interpolation between two antipodal spherical caps on the sphere is unique, while the displacement interpolation between two antipodal points can be realized in an infinite number of ways.

## Appendix: Paths in metric structures

This Appendix is a kind of crash basic course in Riemannian geometry, and nonsmooth generalizations thereof. Much more detail can be obtained from the references cited in the bibliographical notes.

A (finite-dimensional, smooth) Riemannian manifold is a manifold M equipped with a **Riemannian metric** g: this means that g defines a scalar product on each tangent space  $T_xM$ , varying smoothly with x. So if v and w at tangent vectors at x, the notation  $v \cdot w$  really means  $g_x(v, w)$ , where  $g_x$  is the metric at x. The degree of smoothness of g depends on the context, but it is customary to consider  $C^3$  manifolds with a  $C^2$  metric. For the purpose of this course, the reader might assume  $C^{\infty}$  smoothness.

Let  $\gamma : [0, 1] \to M$  be a smooth path,<sup>1</sup> denoted by  $(\gamma_t)_{0 \leq t \leq 1}$ . For each  $t \in (0, 1)$ , the time-derivative at time t is — by the very definition of tangent space — the tangent vector  $v = \dot{\gamma}_t$  in  $T_{\gamma_t}M$ . The scalar product g gives a way to measure the norm of that vector:  $|v|_{T_xM} = \sqrt{v \cdot v}$ . Then one can define the **length** of  $\gamma$  by the formula

$$
\mathcal{L}(\gamma) = \int_0^1 |\dot{\gamma}_t| dt, \qquad (7.25)
$$

and the **distance**, or geodesic distance, between two points x and  $y$  by the formula

$$
d(x,y) = \inf \Big\{ \mathcal{L}(\gamma); \quad \gamma_0 = x, \ \gamma_1 = y \Big\}.
$$
 (7.26)

 $1$  For me the words "path" and "curve" are synonymous.

After that it is easy to extend the length formula to absolutely continuous curves. Note that any one of the three objects (metric, length, distance) determines the other two; indeed, the metric can be recovered from the distance via the formula

$$
|\dot{\gamma}_0| = \lim_{t \downarrow 0} \frac{d(\gamma_0, \gamma_t)}{t},\tag{7.27}
$$

and the usual polarization identity

$$
g(v, w) = \frac{1}{4} [g(v + w, v + w) - g(v - w, v - w)].
$$

Let TM stand for the collection of all  $T_xM$ ,  $x \in M$ , equipped with a manifold structure which is locally product. A point in TM is a pair  $(x, v)$  with  $v \in T_xM$ . The map  $\pi : (x, v) \longmapsto x$  is the projection of TM onto M; it is obviously smooth and surjective. A function  $M \to TM$  is called a vector field: It is given by a tangent vector at each point. So a vector field really is a map  $f: x \to (x, v)$ , but by abuse of notation one often writes  $f(x) = v$ . If  $\gamma : [0, 1] \to M$  is an injective path, one defines a vector field along  $\gamma$  as a path  $\xi : [0, 1] \to TM$  such that  $\pi \circ \xi = \gamma$ .

If  $p = p(x)$  is a linear form varying smoothly on  $T_xM$ , then it can be identified, thanks to the metric g, to a vector field  $\xi$ , via the formula

$$
p(x) \cdot v = \xi(x) \cdot v,
$$

where  $v \in T_xM$ , and the dot in the left-hand side just means " $p(x)$ " applied to v", while the dot in the right-hand side stands for the scalar product defined by  $g$ . As a particular case, if  $p$  is the differential of a function f, the corresponding vector field  $\xi$  is the *gradient* of f, denoted by  $\nabla f$  or  $\nabla_x f$ .

If  $f = f(x, v)$  is a function on TM, then one can differentiate it with respect to x or with respect to v. Since  $T_{(x,v)}T_xM \simeq T_xM$ , both  $d_xf$ and  $d_v f$  can be seen as linear forms on  $T_x M$ ; this allows us to define  $\nabla_x f$  and  $\nabla_v f$ , the "gradient with respect to the position variable" and the "gradient with respect to the velocity variable".

Differentiating functions does not pose any particular conceptual problem, but differentiating vector fields is quite a different story. If  $\xi$ is a vector field on M, then  $\xi(x)$  and  $\xi(y)$  live in different vector spaces, so it does not a priori make sense to compare them, unless one *identifies* in some sense  $T_xM$  and  $T_yM$ . (Of course, one could say that  $\xi$  is a map  $M \to TM$  and define its differential as a map  $TM \to T(TM)$  but this

is of little use, because  $T(TM)$  is "too large"; it is much better if we can come up with a reasonable notion of derivation which produces a map  $TM \to TM$ .)

There is in general no canonical way to identify  $T_xM$  and  $T_yM$  if  $x \neq y$ ; but there is a canonical way to identify  $T_{\gamma_0}M$  and  $T_{\gamma_t}M$  as t varies continuously. This operation is called parallel transport, or Levi-Civita transport. A vector field which is transported in a parallel way along the curve  $\gamma$  will "look constant" to an observer who lives in M and travels along  $\gamma$ . If M is a surface embedded in  $\mathbb{R}^3$ , parallel transport can be described as follows: Start from the tangent plane at  $\gamma(0)$ , and then press your plane onto M along  $\gamma$ , in such a way that there is no friction (no slip, no rotation) between the plane and the surface.

With this notion it becomes possible to compute the derivative of a vector field *along a path*: If  $\gamma$  is a path and  $\xi$  is a vector field along γ, then the derivative of  $\xi$  is another vector field along γ, say  $t \to \xi(t)$ , defined by

$$
\dot{\xi}(t_0) = \left. \frac{d}{dt} \right|_{t=t_0} \theta_{t \to t_0}(\xi(\gamma_t)),
$$

where  $\theta_{t\to t_0}$  is the parallel transport from  $T_{\gamma_t}M$  to  $T_{\gamma_{t_0}}M$  along  $\gamma$ . This makes sense because  $\theta_{t\rightarrow t_0} \xi(\gamma_t)$  is an element of the fixed vector space  $T_{\gamma_{t_0}}M$ . The path  $t \to \xi(t)$  is a vector field along  $\gamma$ , called the **covariant** derivative of  $\xi$  along  $\gamma$ , and denoted by  $\nabla_{\dot{\gamma}} \xi$ , or, if there is no possible confusion about the choice of  $\gamma$ ,  $D\xi/Dt$  (or simply  $d\xi/dt$ ). If  $M = \mathbb{R}^n$ , then  $\nabla_{\dot{\gamma}} \xi$  coincides with  $(\dot{\gamma} \cdot \nabla) \xi$ .

It turns out that the value of  $\dot{\xi}(t_0)$  only depends on  $\gamma_{t_0}$ , on the values of  $\xi$  in a neighborhood of  $\gamma_{t_0}$ , and on the velocity  $\dot{\gamma}_{t_0}$  (not on the whole path  $\gamma_t$ ). Thus if  $\xi$  is a vector field defined in the neighborhood of a point x, and v is a tangent vector at x, it makes sense to define  $\nabla_v \xi$ by the formula

$$
\nabla_v \xi(x) = \frac{D\xi}{Dt}(0), \qquad \gamma_0 = x, \quad \dot{\gamma}_0 = v.
$$

The quantity  $\nabla_{v}\xi(x)$  is "the covariant derivative of the vector field  $\xi$ at x in direction v." Of course, if  $\xi$  and v are two vector fields, one can define a vector field  $\nabla_v \xi$  by the formula  $(\nabla_v \xi)(x) = (\nabla_{v(x)} \xi)(x)$ . The linear operator  $v \mapsto \nabla_v \xi(x)$  is the **covariant gradient** of  $\xi$  at x, denoted by  $\nabla \xi(x)$  or  $\nabla_x \xi$ ; it is a linear operation  $T_x M \to T_x M$ .

It is worth noticing explicitly that the notion of covariant derivation coincides with the convective derivation used in fluid mechanics (for instance in Euler's equation for an incompressible fluid). I shall sometimes adopt the notation classically used in fluid mechanics:  $(\nabla \xi)v = v \cdot \nabla \xi$ . (On the contrary, the notation  $(\nabla \xi) \cdot v$  should rather be reserved for  $(\nabla \xi)^* v$ , where  $(\nabla \xi)^*$  is the adjoint of  $\nabla \xi$ ; then  $\langle v \cdot \nabla \xi, w \rangle = \langle v, \nabla \xi \cdot w \rangle$ and we are back to the classical conventions of  $\mathbb{R}^n$ .)

The procedure of parallel transport allows one to define the covariant derivation; conversely, the equations of parallel transport along  $\gamma$ can be written as  $D\xi/Dt = 0$ , where  $D/Dt$  is the covariant derivative along  $\gamma$ . So it is equivalent to define the notion of covariant derivation, or to define the rules of parallel transport.

There are (at least) three points of view about the covariant derivation. The first one is the *extrinsic* point of view: Let us think of  $M$  as an embedded surface in  $\mathbb{R}^N$ ; that is, M is a subset of  $\mathbb{R}^N$ , it is equipped with the topology induced by  $\mathbb{R}^N$ , and the quadratic form  $g_x$  is just the usual Euclidean scalar product on  $\mathbb{R}^N$ , restricted to  $T_xM$ . Then the covariant derivative is defined by

$$
\dot{\xi}(t) = \Pi_{T_{\gamma_t}M}\left(\frac{d(\xi(\gamma_t))}{dt}\right),\,
$$

where  $\Pi_{T_xM}$  stands for the orthogonal projection (in  $\mathbb{R}^N$ ) onto  $T_xM$ . In short, the covariant derivative is the projection of the usual derivative onto the tangent space.

While this definition is very simple, it does not reveal the fact that the covariant derivation and parallel transport are intrinsic notions, which are invariant under isometry and do not depend on the embedding of M into  $\mathbb{R}^N$ , but just on g. An intrinsic way to define covariant derivation is as follows: It is uniquely characterized by the two natural rules

$$
\frac{d}{dt}\langle \xi, \zeta \rangle = \langle \dot{\xi}, \zeta \rangle + \langle \xi, \dot{\zeta} \rangle; \qquad \frac{D}{Dt}(f\xi) = \dot{f}\,\xi + f\,\dot{\xi},\tag{7.28}
$$

where the dependence of all the expressions on  $t$  is implicit; and by the not so natural rule

$$
\nabla_{\zeta}\xi-\nabla_{\xi}\zeta=[\xi,\zeta].
$$

Here  $[\xi, \zeta]$  is the Lie bracket of  $\xi$  and  $\zeta$ , which is defined as the unique vector field such that for any function  $F$ ,

$$
(dF) \cdot [\xi, \zeta] = d(dF \cdot \xi) \cdot \zeta - d(dF \cdot \zeta) \cdot \xi.
$$

Further, note that in the second formula of  $(7.28)$  the symbol  $\dot{f}$  stands for the usual derivative of  $t \to f(\gamma_t)$ ; while the symbols  $\dot{\xi}$  and  $\dot{\zeta}$  stand for the covariant derivatives of the vector fields  $\xi$  and  $\zeta$  along  $\gamma$ .

A third approach to covariant derivation is based on coordinates. Let  $x \in M$ , then there is a neighborhood O of x which is diffeomorphic to some open subset  $U \subset \mathbb{R}^n$ . Let  $\Phi$  be a diffeomorphism  $U \to O$ , and let  $(e_1, \ldots, e_n)$  be the usual basis of  $\mathbb{R}^n$ . A point m in O is said to have coordinates  $(y^1, \ldots, y^n)$  if  $m = \Phi(y^1, \ldots, y^n)$ ; and a vector  $v \in T_mM$ is said to have *components*  $v^1, \ldots, v^k$  if  $d_{(y^1, \ldots, y^n)} \Phi \cdot (v_1, \ldots, v_k) = v$ . Then the *coefficients* of the metric g are the functions  $g_{ij}$  defined by  $g(v, v) = \sum g_{ij}v^iv^j$ .

The coordinate point of view reduces everything to "explicit" computations and formulas in  $\mathbb{R}^n$ ; for instance the derivation of a function f along the *i*th direction is defined as  $\partial_i f := (\partial/\partial y^i)(f \circ \Phi)$ . This is conceptually simple, but rapidly leads to cumbersome expressions. A central role in these formulas is played by the Christoffel symbols, which are defined by

$$
\Gamma_{ij}^m := \frac{1}{2} \sum_{k=1}^n \left( \partial_i g_{jk} + \partial_j g_{ki} - \partial_k g_{ij} \right) g^{km},
$$

where  $(g^{ij})$  is by convention the inverse of  $(g_{ij})$ . Then the covariant derivation along  $\gamma$  is given by the formula

$$
\left(\frac{D\xi}{Dt}\right)^k = \frac{d\xi^k}{dt} + \sum_{ij} \Gamma^k_{ij} \dot{\gamma}^i \xi^j.
$$

Be it in the extrinsic or the intrinsic or the coordinate point of view, the notion of covariant derivative is one of the cornerstones on which differential Riemannian geometry has been constructed.

Another important concept is that of **Riemannian volume**, which I shall denote by vol. It can be defined intrinsically as the n-dimensional Hausdorff measure associated with the geodesic distance (where  $n$  is the dimension of the manifold). In coordinates,  $vol(dx) = \sqrt{det(g)} dx$ . The Riemannian volume plays the same role as the Lebesgue measure in  $\mathbb{R}^n$ .

After these reminders about Riemannian calculus, we can go back to the study of action minimization. Let  $L(x, v, t)$  be a smooth Lagrangian on  $TM \times [0,1]$ . To find an equation satisfied by the curves which minimize the action, we can compute the differential of the action. So let  $\gamma$  be a curve, and h a small variation of that curve. (This amounts to considering a family  $\gamma_{s,t}$  in such a way that  $\gamma_{0,t} = \gamma_t$  and  $(d/ds)|_{s=0}\gamma_{s,t}=h(t).$  Then the infinitesimal variation of the action A at  $\gamma$ , along the variation h, is

$$
d\mathcal{A}(\gamma) \cdot h = \int_0^1 \left( \nabla_x L(\gamma_t, \dot{\gamma}_t, t) \cdot h(t) + \nabla_v L(\gamma_t, \dot{\gamma}_t, t) \cdot \dot{h}(t) \right) dt.
$$

Thanks to (7.28) we can perform an integration by parts with respect to the time variable, and get

$$
d\mathcal{A}(\gamma) \cdot h = \int_0^1 \left( \nabla_x L - \frac{d}{dt} (\nabla_v L) \right) (\gamma_t, \dot{\gamma}_t, t) \cdot h(t) dt + (\nabla_v L)(\gamma_1, \dot{\gamma}_1, 1) \cdot h(1) - (\nabla_v L)(\gamma_0, \dot{\gamma}_0, 0) \cdot h(0). \quad (7.29)
$$

This is the first variation formula.

When the endpoints  $x, y$  of  $\gamma$  are fixed, the tangent curve h vanishes at  $t = 0$  and  $t = 1$ . Since h is otherwise arbitrary, it is easy to deduce the equation for minimizers:

$$
\frac{d}{dt}\nabla_v L = \nabla_x L.
$$
\n(7.30)

More explicitly, if a differentiable curve  $(\gamma_t)_{0 \leq t \leq 1}$  is minimizing, then

$$
\frac{d}{dt} \Big( \nabla_v L(\gamma_t, \dot{\gamma}_t, t) \Big) = \nabla_x L(\gamma_t, \dot{\gamma}_t, t), \qquad 0 < t < 1.
$$

This is the Euler–Lagrange equation associated with the Lagrangian  $L$ ; to memorize it, it is convenient to write it as

$$
\frac{d}{dt}\frac{\partial L}{\partial \dot{x}} = \frac{\partial L}{\partial x},\tag{7.31}
$$

so that the two time-derivatives in the left-hand side formally "cancel out". Note carefully that the left-hand side of the Euler–Lagrange equation involves the time-derivative of a curve which is valued in TM; so  $(d/dt)$  in (7.31) is in fact a covariant derivative along the minimizing curve  $\gamma$ , the same operation as we denoted before by  $\nabla_{\dot{\gamma}}$ , or  $D/Dt$ .

The most basic example is when  $L(x, v, t) = |v|^2/2$ . Then  $\nabla_v L = v$ and the equation reduces to  $dv/dt = 0$ , or  $\nabla_{\dot{\gamma}} \dot{\gamma} = 0$ , which is the usual equation of vanishing acceleration. Curves with zero acceleration are called geodesics; their equation, in coordinates, is

$$
\ddot{\gamma}^k + \sum_{ij} \Gamma_{ij}^k \dot{\gamma}^i \dot{\gamma}^j = 0.
$$

(Note:  $\ddot{\gamma}^k$  is the derivative of  $t \to \dot{\gamma}^k(t)$ , not the kth component of  $\ddot{\gamma}$ .) The speed of such a curve  $\gamma$  is constant, and to stress this fact one can say that these are constant-speed geodesics, by opposition with general geodesics that can be reparametrized in an arbitrary way. Often I shall just say "geodesics" for constant-speed geodesics. It is equivalent to say that a geodesic  $\gamma$  has constant speed, or that its length between any two times  $s < t$  is proportional to  $t - s$ .

An important concept related to geodesics is that of the **exponen**tial map. If  $x \in M$  and  $v \in T_xM$  are given, then  $\exp_x v$  is defined as  $\gamma(1)$ , where  $\gamma : [0, 1] \to M$  is the unique constant-speed geodesic starting from  $\gamma(0) = x$  with velocity  $\dot{\gamma}(0) = v$ . The exponential map is a convenient notation to handle "all" geodesics of a Riemannian manifold at the same time.

We have seen that minimizing curves have zero acceleration, and the converse is also true *locally*, that is if  $\gamma_1$  is very close to  $\gamma_0$ . A curve which minimizes the action between its endpoints is called a **minimizing** geodesic, or minimal geodesic, or simply a geodesic. The Hopf–Rinow theorem guarantees that if the manifold  $M$  (seen as a metric space) is complete, then any two points in  $M$  are joined by at least one minimal geodesic. There might be several minimal geodesics joining two points x and y (to see this, consider two antipodal points on the sphere), but geodesics are:

- *nonbranching*: Two geodesics that are defined on a time interval  $[0, t]$ and coincide on  $[0, t']$  for some  $t' > 0$  have to coincide on the whole of  $[0, t]$ . Actually, a stronger statement holds true: The velocity of the geodesic at time  $t = 0$  uniquely determines the final position at time  $t = 1$  (this is a consequence of the uniqueness statement in the Cauchy–Lipschitz theorem).
- locally unique: For any given x, there is  $r_x > 0$  such that any y in the ball  $B_{r_x}(x)$  can be connected to x by a single geodesic  $\gamma = \gamma^{x \to y}$ , and then the map  $y \mapsto \dot{\gamma}(0)$  is a diffeomorphism (this corresponds to parametrize the endpoint by the initial velocity).
- almost everywhere unique: For any  $x$ , the set of points  $y$  that can be connected to  $x$  by several (minimizing!) geodesics is of zero measure. A way to see this is to note that the square distance function

 $d^2(x, \cdot)$  is locally semiconcave, and therefore differentiable almost everywhere. (See Chapter 10 for background about semiconcavity.)

The set  $\Gamma_{x,y}$  of (minimizing, constant speed) geodesics joining x and y might not be single-valued, but in any case it is compact in  $C([0,1], M)$ , even if M is not compact. To see this, note that (i) the image of any element of  $\Gamma_{x,y}$  lies entirely in the ball  $B(x, d(x, y))$ , so  $\Gamma_{x,y}$  is uniformly bounded, (ii) elements in  $\Gamma_{x,y}$  are  $d(x,y)$ -Lipschitz, so they constitute an equi-Lipschitz family; (iii)  $\Gamma_{x,y}$  is closed because it is defined by the equations  $\gamma(0) = x, \gamma(1) = y, \mathcal{L}(\gamma) \leq d(\gamma_0, \gamma_1)$ (the length functional  $\mathcal L$  is not continuous with respect to uniform convergence, but it is lower semicontinuous, so an upper bound on the length defines a closed set); (iv)  $M$  is locally compact, so Ascoli's compactness theorem applies to functions with values in M.

A similar argument shows that for any two given compact sets  $K_s$ and  $K_t$ , the set of geodesics  $\gamma$  such that  $\gamma_s \in K_s$  and  $\gamma_t \in K_t$  is compact in  $C([s,t];M)$ . So the Lagrangian action defined by  $\mathcal{A}^{s,t}(\gamma)$  =  $\mathcal{L}(\gamma)^2/(t-s)$  is coercive in the sense of Definition 7.13.

Most of these statements can be generalized to the action coming from a Lagrangian function  $L(x, v, t)$  on  $TM \times [0, 1]$ , if L is  $C^2$  and satisfies the classical conditions of Definition 7.6. In particular the associated cost functions will be continuous. Here is a sketch of the proof: Let x and y be two given points, and let  $x_k \to x$  and  $y_k \to y$  be converging sequences. For any  $\varepsilon > 0$ , small enough,

$$
c^{s,t}(x_k, y_k) \le c^{s,s+\varepsilon}(x_k, x) + c^{s+\varepsilon, t-\varepsilon}(x, y) + c^{t-\varepsilon, t}(y, y_k). \tag{7.32}
$$

It is easy to show that there is a uniform bound  $K$  on the speeds of all minimizing curves which achieve the costs appearing above. Then the Lagrangian is uniformly bounded on these curves, so  $c^{s,s+\epsilon}(x_k,x)$  $O(\varepsilon)$ ,  $c^{t-\varepsilon,t}(y,y_k) = O(\varepsilon)$ . Also it does not affect much the Lagrangian (evaluated on candidate minimizers) to reparametrize  $[s + \varepsilon, t - \varepsilon]$ into [s, t] by a linear change of variables, so  $c^{s+\epsilon,t-\epsilon}(x, y)$  converges to  $c^{s,t}(x,y)$  as  $s \to t$ . This proves the upper semicontinuity, and therefore the continuity, of  $c^{s,t}$ .

In fact there is a finer statement:  $c^{s,t}$  is superdifferentiable. This notion will be explained and developed later in Chapter 10.

Besides the Euclidean space, Riemannian manifolds constitute in some sense the most regular metric structure used by mathemati-

cians. A Riemannian structure comes with many nice features (calculus, length, distance, geodesic equations); it also has a well-defined dimension n (the dimension of the manifold) and carries a natural volume.

Finsler structures constitute a generalization of the Riemannian structure: one has a differentiable manifold, with a norm on each tangent space  $T<sub>r</sub>M$ , but that norm does not necessarily come from a scalar product. One can then define lengths of curves, the induced distance as for a Riemannian manifold, and prove the existence of geodesics, but the geodesic equations are more complicated.

Another generalization is the notion of length space (or intrinsic length space), in which one does not necessarily have tangent spaces, yet one assumes the existence of a length  $\mathcal L$  and a distance d which are compatible, in the sense that

$$
\begin{cases}
\mathcal{L}(\gamma) = \int_0^1 |\dot{\gamma}_t| dt, & |\dot{\gamma}_t| := \limsup_{\varepsilon \to 0} \frac{d(\gamma_t, \gamma_{t+\varepsilon})}{|\varepsilon|},
\end{cases}
$$

$$
\begin{cases}
d(x, y) = \inf \left\{ \mathcal{L}(\gamma); \quad \gamma_0 = x, \ \gamma_1 = y \right\}.
\end{cases}
$$

In practice the following criterion is sometimes useful: A complete metric space  $(\mathcal{X}, d)$  is a length space if and only if for any two points in X, and any  $\varepsilon > 0$  one can find an  $\varepsilon$ -midpoint of  $(x, y)$ , i.e. a point  $m_{\varepsilon}$  such that

$$
\left|\frac{d(x,y)}{2}-d(x,m_{\varepsilon})\right|\leq \varepsilon, \qquad \left|\frac{d(x,y)}{2}-d(y,m_{\varepsilon})\right|\leq \varepsilon.
$$

Minimizing paths are fundamental objects in geometry. A length space in which any two points can be joined by a minimizing path, or geodesic, is called a geodesic space, or strictly intrinsic length space, or just (by abuse of language) length space. There is a criterion in terms of midpoints: A complete metric space  $(\mathcal{X}, d)$  is a geodesic space if and only if for any two points in  $X$  there is a midpoint, which is of course some  $m \in X$  such that

$$
d(x,m) = d(m,y) = \frac{d(x,y)}{2}.
$$

There is another useful criterion: If the metric space  $(\mathcal{X}, d)$  is a complete, locally compact length space, then it is geodesic. This is a generalization of the Hopf–Rinow theorem in Riemannian geometry. One can also reparametrize geodesic curves  $\gamma$  in such a way that their speed  $|\dot{\gamma}|$  is constant, or equivalently that for all intermediate times s and t, their length between times  $s$  and  $t$  coincides with the distance between their positions at times  $s$  and  $t$ .

The same proof that I sketched for Riemannian manifolds applies in geodesic spaces, to show that the set  $\Gamma_{x,y}$  of (minimizing, constant speed) geodesics joining  $x$  to  $y$  is compact; more generally, the set  $\Gamma_{K_0 \to K_1}$  of geodesics  $\gamma$  with  $\gamma_0 \in K_0$  and  $\gamma_1 \in K_1$  is compact, as soon as  $K_0$  and  $K_1$  are compact. So there are important common points between the structure of a length space and the structure of a Riemannian manifold. From the practical point of view, some main differences are that (i) there is no available equation for geodesic curves, (ii) geodesics may "branch", (iii) there is no guarantee that geodesics between  $x$  and y are unique for y very close to x, (iv) there is neither a unique notion of dimension, nor a canonical reference measure, (v) there is no guarantee that geodesics will be almost everywhere unique. Still there is a theory of differential analysis on nonsmooth geodesic spaces (first variation formula, norms of Jacobi fields, etc.) mainly in the case where there are lower bounds on the sectional curvature (in the sense of Alexandrov, as will be described in Chapter 26).

## Bibliographical notes

There are plenty of classical textbooks on Riemannian geometry, with variable degree of pedagogy, among which the reader may consult [223], [306], [394]. For an introduction to the classical calculus of variations in dimension 1, see for instance [347, Chapters 2–3], [177], or [235]. For an introduction to the Hamiltonian formalism in classical mechanics, one may use the very pedagogical treatise by Arnold [44], or the more complex one by Thirring [780]. For an introduction to analysis in metric spaces, see Ambrosio and Tilli [37]. A wonderful introduction to the theory of length spaces can be found in Burago, Burago and Ivanov [174]. In the latter reference, a Riemannian manifold is defined as a length space which is locally isometric to  $\mathbb{R}^n$  equipped with a quadratic form  $g_x$  depending smoothly on the point x. This definition is not standard, but it is equivalent to the classical definition, and in some sense more satisfactory if one wishes to emphasize the metric point of view. Advanced elements of differential analysis on nonsmooth metric spaces can be found also in the literature on Alexandrov spaces, see the bibliographical notes of Chapter 26.

I may have been overcautious in the formulation of the classical conditions in Definition 7.6, but in the time-dependent case there are crazy counterexamples showing that nice  $C<sup>1</sup>$  Lagrangian functions do not necessarily have  $C^1$  minimizing curves (equivalently, these minimizing curves won't solve the Euler–Lagrange equation); see for instance the constructions by Ball and Mizel [63, 64]. (A backwards search in Math-SciNet will give access to many papers concerned with multidimensional analogs of this problem, in relation with the so-called Lavrentiev phenomenon.) I owe these remarks to Mather, who also constructed such counterexamples and noticed that many authors have been fooled by these issues. Mather [601, Section 2 and Appendices], Clarke and Vinter [235, 236, 823] discuss in great detail sufficient conditions under which everything works fine. In particular, if  $L$  is  $C<sup>1</sup>$ , strictly convex superlinear in  $v$  and time-independent, then minimizing curves are automatically  $C^1$  and satisfy the Euler–Lagrange equation. (The difficult point is to show that minimizers are Lipschitz; after that it is easier to see that they are at least as smooth as the Lagrangian.)

I introduced the abstract concept of "coercive Lagrangian action" for the purpose of this course, but this concept looks so natural to me that I would not be surprised if it had been previously discussed in the literature, maybe in disguised form.

Probability measures on action-minimizing curves might look a bit scary when encountered for the first time, but they were actually rediscovered several times by various researchers, so they are arguably natural objects: See in particular the works by Bernot, Caselles and Morel [109, 110] on irrigation problems; by Bangert [67] and Hohloch [476] on problems inspired by geometry and dynamical systems; by Ambrosio on transport equations with little or no regularity [21, 30]. In fact, in the context of partial differential equations, this approach already appears in the much earlier works of Brenier [155, 157, 158, 159] on the incompressible Euler equation and related systems. One technical difference is that Brenier considers probability measures on the huge (nonmetrizable) space of measurable paths, while the other above-mentioned authors only consider much smaller spaces consisting of continuous, or Lipschitz-continuous functions. There are important subtleties with probability measures on nonmetrizable spaces, and I advise the reader to stay away from them.

Also in relation to the irrigation problem, various models of traffic plans and "dynamic cost function" are studied in [108, 113]; while paths in the space of probability measures are considered in [152].

The Hamilton–Jacobi equation with a quadratic cost function (i.e.  $L(x, v, t) = |v|^2$ ) will be considered in more detail in Chapter 22; see in particular Proposition 22.16. For further information about Hamilton– Jacobi equations, there is an excellent book by Cannarsa and Sinestrari [199]; one may also consult [68, 327, 558] and the references therein. Of course Hamilton–Jacobi equations are closely related to the concept of c-convexity: for instance, it is equivalent to say that  $\psi$  is c-convex, or that it is a solution at time 0 of the backward Hamilton– Jacobi semigroup starting at time 1 (with some arbitrary initial datum).

At the end of the proof of Proposition 7.16 I used once again the basic measurable selection theorem which was already used in the proof of Corollary 5.22, see the bibliographical notes on p. 104.

Interpolation arguments involving changes of variables have a long history. The concept and denomination of displacement interpolation was introduced by McCann [614] in the particular case of the quadratic cost in Euclidean space. Soon after, it was understood by Brenier that this procedure could formally be recast as an action minimization problem in the space of measures, which would reduce to the classical geodesic problem when the probability measures are Dirac masses. In Brenier's approach, the action is defined, at least formally, by the formula

$$
\mathbb{A}(\mu) = \inf_{v(t,x)} \left\{ \int_0^1 \int |v(t,x)|^2 \, d\mu_t(x) \, dt; \quad \frac{\partial \mu}{\partial t} + \nabla \cdot (v\mu) = 0 \right\},\tag{7.33}
$$

and then one has the Benamou–Brenier formula

$$
W_2(\mu_0, \mu_1)^2 = \inf \mathbb{A}(\mu), \tag{7.34}
$$

where the infimum is taken among all paths  $(\mu_t)_{0 \leq t \leq 1}$  satisfying certain regularity conditions. Brenier himself gave two sketches of the proof for this formula [88, 164], and another formal argument was suggested by Otto and myself [671, Section 3]. Rigorous proofs were later provided by several authors under various assumptions [814, Theorem 8.1] [451] [30, Chapter 8] (the latter reference contains the most precise results). The adaptation to Riemannian manifolds has been considered in [278, 431, 491]. We shall come back to these formulas later on, after a more precise qualitative picture of optimal transport has emerged. One of

the motivations of Benamou and Brenier was to devise new numerical methods [88, 89, 90, 91]. Wolansky [838] considered a more general situation in the presence of sources and sinks.

There was a rather amazing precursor to the idea of displacement interpolation, in the form of Nelson's theory of "stochastic mechanics". Nelson tried to build up a formalism in which quantum effects would be explained by stochastic fluctuations. For this purpose he considered an action minimization problem which was also studied by Guerra and Morato:

$$
\inf \ \mathbb{E} \, \int_0^1 |\dot{X}_t|^2 \, dt,
$$

where the infimum is over all random paths  $(X_t)_{0 \le t \le 1}$  such that law  $(X_0) = \mu_0$ , law  $(X_1) = \mu_1$ , and in addition  $(X_t)$  solves the stochastic differential equation

$$
\frac{dX_t}{dt} = \sigma \frac{dB_t}{dt} + \xi(t, X_t),
$$

where  $\sigma > 0$  is some coefficient,  $B_t$  is a standard Brownian motion, and  $\xi$  is a drift, which is an unknown in the problem. (So the minimization is over all possible couplings  $(X_0, X_1)$  but also over all drifts!) This formulation is very similar to the Benamou–Brenier formula just alluded to, only there is the additional Brownian noise in it, thus it is more complex in some sense. Moreover, the expected value of the action is always infinite, so one has to renormalize it to make sense of Nelson's problem. Nelson made the incredible discovery that after a change of variables, minimizers of the action produced solutions of the free Schrödinger equation in  $\mathbb{R}^n$ . He developed this approach for some time, and finally gave up because it was introducing unpleasant nonlocal features. I shall give references at the end of the bibliographical notes for Chapter 23.

It was Otto [669] who first explicitly reformulated the Benamou– Brenier formula (7.34) as the equation for a geodesic distance on a Riemannian setting, from a formal point of view. Then Ambrosio, Gigli and Savaré pointed out that if one is not interested in the equations of motion, but just in the geodesic property, it is simpler to use the metric notion of geodesic in a length space [30]. Those issues were also developed by other authors working with slightly different formalisms [203, 214].

All the above-mentioned works were mainly concerned with displacement interpolation in  $\mathbb{R}^n$ . Agueh [4] also considered the case of cost  $c(x, y) = |x - y|^p$   $(p > 1)$  in Euclidean space. Then displacement interpolation on Riemannian manifolds was studied, from a heuristic point of view, by Otto and myself [671]. Some useful technical tools were introduced in the field by Cordero-Erausquin, McCann and Schmuckenschläger [246] for Riemannian manifolds; Cordero-Erausquin adapted them to the case of rather general strictly convex cost functions in  $\mathbb{R}^n$  [243].

The displacement interpolation for more general cost functions, arising from a smooth Lagrangian, was constructed by Bernard and Buffoni [105], who first introduced in this context Property (ii) in Theorem 7.21. At the same time, they made the explicit link with the Mather minimization problem, which will appear in subsequent chapters. This connection was also studied independently by De Pascale, Gelli and Granieri [278].

In all these works, displacement interpolation took place in a smooth structure, resulting in particular in the uniqueness (almost everywhere) of minimizing curves used in the interpolation, at least if the Lagrangian is nice enough. Displacement interpolation in length spaces, as presented in this chapter, via the notion of dynamical transference plan, was developed more recently by Lott and myself [577]. Theorem 7.21 in this course is new; it was essentially obtained by rewriting the proof in [577] with enough generality added to include the setting of Bernard and Buffoni.

The most natural examples of Lagrangian functions are those taking the form  $L(t, x, v) = |v|^2/2 - U(t, x)$ , where  $U(t, x)$  is a potential energy. In relation with incompressible fluid mechanics, Ambrosio and Figalli [24, 25] studied the case when U is the pressure field (a priori nonsmooth). Another case of great interest is when  $U(t, x)$  is the scalar curvature of a manifold evolving in time according to the Ricci flow; then, up to a correct time rescaling, the associated minimal action is known in geometry as Perelman's  $\mathcal{L}\text{-distance}$ . First Topping [782], and then Lott [576] discussed the Lagrangian action induced by the  $\mathcal{L}\text{-distance}$  (and some of its variants) at the level of the space of probability measures. They used this formalism to recover some key results in the theory of Ricci flow.

Displacement interpolation in the case  $p = 1$  is quite subtle because of the possibility of reparametrization; it was carefully discussed in the Euclidean space by Ambrosio [20]. Recently, Bernard and Buffoni [104] shed some new light on that issue by making explicit the link with the Mather–Mañé problem. Very roughly, the distance cost function is a typical representative of cost functions that arise from Lagrangians, if one also allows minimization over the choice of the time-interval  $[0,T] \subset \mathbb{R}$  (rather than fixing, say,  $T = 1$ ). This extra freedom accounts for the degeneracy of the problem.

Lagrangian cost functions of the form  $V(\gamma)|\dot{\gamma}|$ , where V is a "Lyapunov functional", have been used by Hairer and Mattingly [458] in relation to convergence to equilibrium, as a way to force the system to visit a compact set. Such cost functions also appear formally in the modeling of irrigation [108], but in fact this is a nonlinear problem since  $V(\gamma)$  is determined by the total mass of particles passing at a given point.

The observation in Remark 7.27 came from a discussion with S. Evans, who pointed out to me that it was difficult, if not impossible, to get characterizations of random processes expressed in terms of the measures when working in state spaces that are not locally compact (such as the space of real trees). In spite of that remark, recently Lisini [565] was able to obtain representation theorems for general absolutely continuous paths  $(\mu_t)_{0 \leq t \leq 1}$  in the Wasserstein space  $P_p(\mathcal{X})$  $(p > 1)$ , as soon as  $\int ||\dot{\mu}_t||_F^p$  $_{P_p}^p$  dt  $<\infty$ , where X is just a Polish space and  $\|\mu_t\|_{P_p}$  is the metric speed in  $P_p(\mathcal{X})$ . He showed that such a curve may be written as  $(e_t)_\# \Pi$ , where  $\Pi$  is the law of a random absolutely continuous curve  $\gamma$ ; as a consequence, he could generalize Corollary 7.22 by removing the assumption of local compactness. Lisini also established a metric replacement for the relation of conservation of mass: For almost all  $t$ ,

$$
\mathbb{E} \, |\dot{\gamma}_t|^p \leq \|\dot{\mu}\|_{P_p}^p.
$$

He further applied his results to various problems about transport in infinite-dimensional Banach spaces.

Proposition 7.29 is a generalization of a result appearing in [444]. Gigli communicated to me an alternative proof, which is more elementary and needs neither local compactness, nor length space property.
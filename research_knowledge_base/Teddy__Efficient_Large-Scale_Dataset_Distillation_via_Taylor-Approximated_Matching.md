# Teddy: Efficient Large-Scale Dataset Distillation via Taylor-Approximated Matching

Ruonan Yu<sup>o</sup>[,](https://orcid.org/0000-0001-8415-3597) <PERSON><PERSON> Liu<sup>o</sup>, <PERSON><PERSON><sup>o</sup>, and <PERSON><PERSON><PERSON><sup>o\*</sup>

National University of Singapore

{ruonan,songhua.liu}@u.nus.edu , {jingweny,xinchao}@nus.edu.sg

Abstract. Dataset distillation or condensation refers to compressing a large-scale dataset into a much smaller one, enabling models trained on this synthetic dataset to generalize effectively on real data. Tackling this challenge, as defined, relies on a bi-level optimization algorithm: a novel model is trained in each iteration within a nested loop, with gradients propagated through an unrolled computation graph. However, this approach incurs high memory and time complexity, posing difficulties in scaling up to large datasets such as ImageNet. Addressing these concerns, this paper introduces <PERSON>, a Taylor-approximat e d dataset distillation framework designed to handle large-scale dataset and enhance efficiency. On the one hand, backed up by theoretical analysis, we propose a memory-efficient approximation derived from Taylor expansion, which transforms the original form dependent on multi-step gradients to a first-order one. On the other hand, rather than repeatedly training a novel model in each iteration, we unveil that employing a precached pool of weak models, which can be generated from a single base model, enhances both time efficiency and performance concurrently, particularly when dealing with large-scale datasets. Extensive experiments demonstrate that the proposed Teddy attains state-of-the-art efficiency and performance on the Tiny-ImageNet and original-sized ImageNet-1K dataset, notably surpassing prior methods by up to 12.8%, while reducing 46.6% runtime. Our code will be available at [https://github.com/Lexie-](https://github.com/Lexie-YU/Teddy)[YU/Teddy.](https://github.com/Lexie-YU/Teddy)

Keywords: Dataset distillation · Taylor approximation · Efficient training

## 1 Introduction

Deep learning has made remarkable strides in various fields, largely owing to the utilization of extensive training data [\[7,](#page-14-0)[9,](#page-14-1)[16,](#page-14-2)[28,](#page-15-0)[35\]](#page-16-0). However, there are associated expenses; achieving optimal performance necessitates substantial computational resources and a significant amount of GPU time, particularly for the latest stateof-the-art models [\[2,](#page-14-3)[9,](#page-14-1)[24,](#page-15-1)[26\]](#page-15-2). Concerning these issues, a promising and emerging field, dataset distillation (DD), also known as dataset condensation (DC), has

<sup>⋆</sup> Corresponding author.

<span id="page-1-0"></span>Image /page/1/Figure/0 description: This figure compares a traditional DD approach with an "Ours" approach. The traditional DD method involves original data and synthetic data being fed into randomly initialized networks that are independently trained, followed by a match loss calculation. This process is optimized using second-order optimization. The "Ours" approach uses a single model that jointly generates original data and synthetic data, also followed by a match loss calculation, and is optimized using first-order optimization. The figure also includes two plots. The left plot shows "Peak GPU Memory (GiB)" on the y-axis and "# of Pixels" on the x-axis. It compares "DD-# of Inner Loops 10", "DD-# of Inner Loops 30", "DD-# of Inner Loops 50", and "Ours". The right plot shows "Runtime for Each Step (s)" on the y-axis and "# of Pixels" on the x-axis, with the same legend as the left plot. Both plots show that the "Ours" approach has significantly lower peak GPU memory and runtime compared to the traditional DD methods, especially as the number of pixels increases. Insets in both plots provide a closer view of the lower range of the data.

Fig. 1: Illustration of meta-learning-based methods, our method (left), and the comparison of memory and time efficiency (right). Our proposed method exhibits surprising memory and time efficiency.

recently garnered significant attention. DD concentrates on condensing the extensive original dataset into a much smaller version while retaining the entirety of the dataset knowledge so that models trained on the synthetic dataset can generalize well on real data.

Following the definition of DD, Wang et al. [\[33\]](#page-15-3) in seminal propose a principled meta-learning-based algorithm: in each iteration, a novel model is trained on the synthetic dataset in a nested loop, and the trained model is meta-tested on original data, and the gradient is backpropagated through an unrolled computation graph to update current synthetic data. Although effective, such bi-level optimization results in high memory and time complexity in practice. Specifically, on the one hand, each update of the current synthetic dataset necessitates an iterative meta-train process from a new model, making the time required for each step notably prolonged. On the other hand, caching the whole computation graph for back-propagation demands significant GPU memory. As illustrated in Fig. [1,](#page-1-0) for instance, under the setting of a 10-class dataset with 50 images per class, each update requires over 50 GiB GPU memory, and the entire data generation takes hundreds of hours, let alone when condensing the large-scale ImageNet dataset, which has thousands of times optimization pixel space of the small dataset like CIFAR10.

To address the challenges above, subsequent research endeavors have focused on devising surrogate objectives to mitigate the bi-level nature of DD. While experiments have demonstrated their enhanced efficiency and effectiveness, these approaches still necessitate extensive forward and backward passes in neural networks for computation. For instance, methods such as gradient matching [\[39,](#page-16-1) [41\]](#page-16-2) entail sampling gradients of current synthetic datasets and original data across a multitude of networks. Similarly, distribution matching [\[31,](#page-15-4)[40\]](#page-16-3) involves computing feature statistics within numerous networks. Additionally, trainingtrajectory-matching techniques [\[3,](#page-14-4) [5\]](#page-14-5) necessitate the preparation of hundreds of training trajectories containing thousands of model checkpoints altogether. The substantial computational demands of current algorithms pose significant challenges when scaling up to handle large-scale datasets.

In light of the analysis presented above, we rethink and deeply explore the primary DD optimization objectives in this paper. Concentrating on the main bi-level optimization problem, we propose an efficient approximation solution derived from Taylor expansion to the original one, transforming the original paradigm dependent on multi-step gradients or the second-order optimization to a first-order one. Moreover, we further simplify this Taylor approximation solution by pre-caching a pool of weak models, which enhances time efficiency and has also been verified as a more advantageous alternative to training models from scratch or using fully converged ones. Specifically, two practical cases are taken into consideration here to obtain weak teachers: prior and post-generation. Both efficiently generate the model pool from a single base model.

To demonstrate the rationality of our proposed method, we provide a comprehensive theoretical analysis. This analysis also reveals the relationships between the mainstream optimization objectives adopted by existing methods and builds a unified theoretical framework of DD. Extensive experiments also validate the superiority of the proposed Teddy. It outperforms previous state-of-the-art methods by a large margin, especially for the original-sized ImageNet-1K dataset. Our contributions can be summarized below:

- We propose an efficient large-scale dataset distillation method termed Teddy, a Taylor-approximated version of the solution to the DD. Our proposed method decouples the bi-level optimization and avoids model training within inner loops for each iteration, which can better handle large-scale datasets.
- We provide a comprehensive theoretical analysis for our proposed method, and we are the first to give a theoretical analysis linking the existing mainstream dataset distillation objectives used in drastically different dataset distillation methods. Our study establishes a unified theoretical framework for dataset distillation.
- We pre-cache weak models to decrease the time complexity introduced by training student models in nested loops for each iteration. Specifically, we consider two practical cases, the prior and post model pool generation, both efficiently generating the model pool from a single base model.
- We conduct comprehensive experiments on our proposed method, which show state-of-the-art performance. Notably, on the full-sized ImageNet-1K dataset, our proposed method outperforms previous methods by a substantial margin, reaching up to 12.8%.

## 2 Related Works

Dataset distillation (DD), first proposed by Wang *et al.* [\[33\]](#page-15-3), aims to condense the large-scale dataset into a small synthetic dataset and, when performed as the training data, preserve the performance of the trained models. DD addresses the tremendous computational cost issues and improves the efficiency of downstream tasks. In recent years of rapid development, increasing research has emerged in the field of DD. Based on the variations in the optimization objectives, it can be broadly categorized into three classes: performance matching [\[19,](#page-15-5) [22,](#page-15-6) [33,](#page-15-3) [43\]](#page-16-4), parameter matching [\[3,](#page-14-4) [5,](#page-14-5) [10,](#page-14-6) [20,](#page-15-7) [34,](#page-15-8) [39,](#page-16-1) [41\]](#page-16-2), and distribution matching [\[31,](#page-15-4) [40,](#page-16-3) [42\]](#page-16-5). Moreover, some research is dedicated to improving the efficiency of data

storage space utilization and expanding the learnable information space, such as label distillation [\[1,](#page-14-7)[29\]](#page-15-9), dataset parameterization [\[8,](#page-14-8)[15,](#page-14-9)[17,](#page-15-10)[18,](#page-15-11)[39\]](#page-16-1), and model augmentation [\[38\]](#page-16-6), further enhancing the distilled performance.

However, previous works in DD still have problems scaling up, including generating larger synthetic datasets, adapting to more extensive training networks, and handling more complex and massive original datasets [\[4,](#page-14-10) [37\]](#page-16-7). One significant reason for this issue is the bilevel optimization process, where each synthetic dataset update requires backpropagation through the unrolled graph, making it high-memory demanding. For this issue, one intuitive idea is to decouple the bilevel optimization process and unbind the network and the synthetic dataset [\[37\]](#page-16-7), such as the practice of DM [\[40\]](#page-16-3) and IDM [\[42\]](#page-16-5), treating the network as the feature extractor without updating it or updating using the original dataset, and matching the feature distribution of the original and the synthetic datasets. However, there continues to be a substantial performance disparity with the performance and parameter matching methods, and recalculating the mean of the original dataset features for every iteration is an additional overhead.

A recent work, proposed by Yin et al. [\[36\]](#page-16-8), utilizes the statistic information running mean and running variance of the original training datasets stored in the batch normalization layers of the well-trained model. They only adopt one single model, which may lack a comprehensive view of the entire dataset and lead to the mode collapse problem. Moreover, statistics information stored in batch normalization layers of well-trained models may overemphasize long-tail samples, which is challenging for the synthetic dataset to fit and be optimized.

## 3 Methodology

This section provides a comprehensive introduction to the proposed method, Teddy. We will begin by elucidating the underlying motivation, followed by an exhaustive presentation of our algorithm and theoretical analysis.

### 3.1 Preliminary

Given the original dataset, denoted as  $\mathcal{T} = (X_t, Y_t)$ , where  $X_t \in \mathbb{R}^{N_t \times d}$  and  $Y_t \in$  $\mathbb{R}^{N_t \times c}$ , the primary goal of DD is to generate a synthetic dataset  $\mathcal{S} = (X_s, Y_s)$ , where  $X_s \in \mathbb{R}^{N_s \times d}$  and  $Y_s \in \mathbb{R}^{N_s \times c}$ , such that the model training on it can obtain a comparable performance with the original dataset. Here,  $N_t$  and  $N_s$  are the number of samples in  $\mathcal T$  and  $\mathcal S$ , respectively, and  $N_s \ll N_t$ . d is the number of features for each sample.  $c$  is the number of classes for classification tasks. The objective can be formulated as:

<span id="page-3-0"></span>
$$
S = \underset{S}{\arg\min} \mathbb{E}_{\theta^{(0)} \sim \Theta}[l(\mathcal{T}; \theta_{S}^{(T)})],
$$
  
\n
$$
\theta_{S}^{(t)} = \theta_{S}^{(t-1)} - \alpha \nabla_{\theta_{S}^{(t-1)}} l(S; \theta_{S}^{(t-1)}),
$$
\n(1)

where  $\Theta$  is the distribution of model initialization, T is the number of epochs the model trained on the synthetic dataset,  $1 \leq t \leq T$ ,  $\alpha$  is the learning rate, and  $l(\cdot; \theta)$  is the cross-entropy loss for classification tasks.

<span id="page-4-1"></span>Image /page/4/Figure/1 description: The image displays a three-part figure illustrating a method for generating data using teacher models. Part (a), labeled 'Prior Generation', shows an initial model with parameters \(\theta\_J^{(0)}\) undergoing limited training and then being combined with random noise to produce a sequence of models \(\theta\_J^{(0+m)}, \dots, \theta\_J^{(T\_e)}\), each processed with 'Statistic Matching + Label Matching'. Part (b), labeled 'Post Generation', starts with a trained model \(\theta\_J\) which is subjected to 'Random Pruning' and then combined with random noise to generate a sequence of models \(\theta\_J^{1'}, \theta\_J^{2'}, \dots, \theta\_J^{n'}\), also processed with 'Statistic Matching + Label Matching'. Part (c), titled 'Distance v.s. Accuracy', presents two plots. The left plot shows the 'Distance' between student and teachers, with the x-axis representing '# of Training Epochs of Teachers' ranging from 1-41 to 41-81. The y-axis ranges from 0.0055 to 0.0080. Three lines represent 'IPC 10' (red), 'IPC 50' (green), and 'IPC 100' (blue), with solid lines indicating distance and dashed lines indicating a related metric. The right plot shows the 'Accuracy' of data generated by teachers, with the x-axis also representing '# of Training Epochs of Teachers' and the y-axis ranging from 25 to 55. The same three lines (IPC 10, IPC 50, IPC 100) are plotted, showing accuracy trends.

Fig. 2: (a)(b) Illustration of our proposed method. Firstly, we generate the model pool by prior or post-generation. Then, update the initialized distilled data via statistic and label matching. Lastly, input the generated distilled data with augmentation into the model pool again to obtain the soft label. (c) The left figure shows the distance between student and teacher models at different stages. The dotted line represents the distance between a single teacher and a student. The solid line represents the average of the distances within the range. The right figure shows the performance of distilled data generated from teacher models at different stages.

As Eq. [1](#page-3-0) shows, the objective involves a bi-level optimization process. Each step of updating the synthetic dataset needs to backpropagate through an unrolled computation graph, making it inefficient for both memory and time. Moreover, it should train a novel model in a nested loop for every iteration, resulting in notable time consumption, particularly when handling complex datasets.

### 3.2 Taylor-Approximated Matching

In this section, we start with the fundamental solution to DD, the meta-learningbased method, progressively delving into our proposed method, Teddy, with theoretical analysis. The DD formulation is shown in Eq. [1.](#page-3-0) Practically, to enhance the generalization ability of the distilled data on the real data, the inner loop is typically configured with multiple steps,  $e.g., T > 10$ . Considering the synthetic dataset is of small size, the model is highly susceptible to overfitting on it as training for T epochs. Therefore, it has  $l(S; \theta_S^{(T)})$  $\mathcal{S}^{(1)}$  <  $\epsilon$ , where  $\epsilon$  is close to zero. Employing the Karush-Kuhn-Tucker (KKT) conditions, the optimization objective in Eq. [1](#page-3-0) can be transformed into the following:

<span id="page-4-0"></span>
$$
S = \underset{S}{\arg \min} [l(\mathcal{T}; \theta_S^{(T)}) + u(l(\mathcal{S}; \theta_S^{(T)}) - \epsilon)],
$$
  

$$
\theta_S^{(t)} = \theta^{(0)} - \alpha \sum_{i=0}^{t-1} g_S^{(i)} = \theta_S^{(t-1)} - \alpha g_S^{(t-1)}
$$
 $(2)$ 

where  $\theta^{(0)} \sim \Theta$ ,  $g_{\mathcal{S}}^{(t)} = \nabla_{\theta_{\mathcal{S}}^{(t)}} l(\mathcal{S}; \theta_{\mathcal{S}}^{(t)})$  $S^{(t)}(s)$ , and u is the Lagrange multiplier.

Proposition 1 The meta-learning-based optimization objective can be Taylorapproximated as the sum of the gradient matching of the distilled data and the original data for all steps along the training trajectory of the student model.

Proof. Here, we recursively apply the first-order Taylor expansion to unfold the unrolled computational graph. The first term of the optimization objective can be transformed into follows:

<span id="page-5-0"></span>
$$
l(\mathcal{T}; \theta_{\mathcal{S}}^{(T)}) = l(\mathcal{T}; \theta_{\mathcal{S}}^{(T-1)}) - \alpha g_{\mathcal{S}}^{(T-1)} \approx l(\mathcal{T}; \theta_{\mathcal{S}}^{(T-1)}) - \alpha g_{\mathcal{T}}^{(T-1)} \cdot g_{\mathcal{S}}^{(T-1)}
$$

$$
\approx l(\mathcal{T}; \theta_{\mathcal{S}}^{(T-i)}) - \alpha \sum_{t=T-i}^{T-1} g_{\mathcal{T}}^{(t)} \cdot g_{\mathcal{S}}^{(t)} \approx l(\mathcal{T}; \theta^{(0)}) - \alpha \sum_{t=0}^{T-1} g_{\mathcal{T}}^{(t)} \cdot g_{\mathcal{S}}^{(t)} (3)
$$

where  $\theta^{(0)} \sim \Theta$ , and  $g_{\mathcal{T}}^{(t)} = \nabla_{\theta_{\mathcal{S}}^{(t)}} l(\mathcal{T}; \theta_{\mathcal{S}}^{(t)})$  $S^{(t)}(S)$ . The first term  $l(\mathcal{T}; \theta^{(0)})$  is the loss of the original dataset on the initial model  $\theta^{(0)}$ , and irrelevant to the optimization problem  $w.r.t$  S. Concluded from Eq. [3,](#page-5-0) the meta-learning-based optimization objective can be transformed into the sum of the gradient matching for all steps pathing the student model training trajectory.

However, updating the distilled data should compute and accumulate the gradient matching loss at each step within the inner loop. It involves a secondorder optimization problem, which may cause high computational complexity. To tackle this issue, we transfer the optimization objective into a first order one.

Proposition 2 Gradient matching is equivalent to first-order and second-order statistic information matching in feature space if the dataset classes are balanced.

Proof. For simplicity, we assume the case in FRePo [\[43\]](#page-16-4), where only the last linear layer of the network  $(\theta)$  is updated for synthetic data, denoted as W, while the preceding layers  $(f_{\theta})$  serve as the feature extractor. In this case, the gradient can form as follows:

<span id="page-5-2"></span><span id="page-5-1"></span>
$$
g = \frac{\partial l(\cdot;\theta)}{\partial W} = \frac{1}{|X|} f_{\theta}(X)^{T} (f_{\theta}(X)W - Y)
$$
  
$$
= \frac{1}{|X|} f_{\theta}(X)^{T} f_{\theta}(X)W - \frac{1}{|X|} f_{\theta}(X)^{T} Y
$$
  
$$
\stackrel{\theta^{(0)} \simeq \Theta}{=} \frac{1}{|X|} \sigma^{2} (f_{\theta}(X))W - \frac{1}{|X|} \mu(f_{\theta}(X)).
$$
 (4)

The first term of Eq. [4](#page-5-1) is the weighted correlation of the extracted features of the dataset, and the second term is the class-wise mean of the extracted features. Under the condition of  $\theta^{(0)} \sim \Theta$ , the constraining covariance is essentially equivalent with variance. Also, when classes in the dataset are balanced, the class-wise mean can be replaced by global mean. For detailed proofs, please refer to the supplementary materials.

Therefore, the optimization objective in Eq. [2](#page-4-0) can be transferred to:

$$
l(\mathcal{T}; \theta_{\mathcal{S}}^{(T)}) \approx \mathbb{E}_{{\theta^{(0)}} \sim \Theta} \left[ \sum_{t=0}^{T-1} \left( \sum_{l} ||\mu_{l}(f_{{\theta_{\mathcal{S}}^{(t)}}}(X_{s})) - \mu_{l}(f_{{\theta_{\mathcal{S}}^{(t)}}}(X_{t})) ||_{2} \right) + \sum_{l} ||\sigma_{l}^{2}(f_{{\theta_{\mathcal{S}}^{(t)}}}(X_{s})) - \sigma_{l}^{2}(f_{{\theta_{\mathcal{S}}^{(t)}}}(X_{t})) ||_{2} + u \cdot l(\mathcal{S}; \theta_{\mathcal{S}}^{(T)}) \right] \quad (5)
$$

where  $\mu_l$  and  $\sigma_l^2$  refer to the mean and variance of the  $l^{th}$  layer features. Here, we solve the core issue of bi-level optimization by stripping the inner loop training process from the backpropagation computational graph. That is, we match the statistical information of the original and synthetic dataset in the feature space. Specifically, we train a single student trajectory for every iteration from scratch. For each checkpoint along the trajectory, we perform the statistic information (the mean and variance in each layer) matching of the original and synthetic datasets, which largely improves the efficiency.

### 3.3 Model Pool Generation

Considering that we have already improved the efficiency by Eq. [5,](#page-5-2) it is still time demanding since we need to re-train the new model for each synthetic data updating. It will become significantly pronounced when handling the large-scale datasets.

To solve this problem and improve the efficiency, we should deal with the long student training trajectory and retraining student model for each iteration. We notice that for each synthetic data updating iteration, any segments of the student model training trajectory that begin with t and end with  $t + m$  have:

<span id="page-6-0"></span>
$$
l(\mathcal{T}; \theta_{\mathcal{S}}^{(t+m)}) = l(\mathcal{T}; \theta_{\mathcal{S}}^{(t)} - \alpha \sum_{i=t}^{t+m-1} g_{\mathcal{S}}^{(i)}) \approx l(\mathcal{T}; \theta_{\mathcal{S}}^{(t)}) - \alpha \left( \sum_{i=t}^{t+m-1} g_{\mathcal{S}}^{(i)} \right) \cdot g_{\mathcal{T}}^{(t)}, \quad (6)
$$

of which the  $(\sum_{i=t}^{t+m-1} g_{\mathcal{S}}^{(i)}$  $g^{(i)}_\mathcal{S})\cdot g^{(t)}_\mathcal{T}$  $\sigma$  shows that multi-step student model updating  $\sum_{i=t}^{t+m-1} g_{\mathcal{S}}^{(i)}$  $s^{(i)}_{\mathcal{S}}$  is comparable with single-step teacher updating  $g_{\mathcal{T}}^{(t)}$  $\sigma$  for appropriate interval m. Thus, each student trajectory segmentation can be replaced by a single comparable teacher model. The "comparable" here means the teachers have the same-level performance as students (trained on the synthetic dataset), which does not reach convergence on the real data and is **weak** in performance. Also, the distance between the students and teachers is close.

Insight from Eq. [6,](#page-6-0) to further improve the time efficiency, we construct the weak teacher model pool from two practical cases, the prior and post-generation. These strategies are both efficiently generating model pool from a single base model, as shown in Fig. [2a](#page-4-1) and Fig. [2b.](#page-4-1) Here, as we utilize weak teachers instead of fully-convergence ones, we can just use a single model as the base to generate diverse weak teachers. This strategy does not compromise performance. More specifically, if the base model is:

- at the early stage or randomly initialized, we adopt the prior-generation strategy. We pre-cache the weak teachers from the early stage of the training trajectory for every  $m$  steps, substituting the whole student training trajectory.
- at the late stage or well-trained, we propose an alternative pruning-based post-generation model pool method. Here, we employ the DepGraph [\[11\]](#page-14-11) with random strategy, complemented by fine-tuning with very limited  $(e.g.,$ 0-2) steps.

Additionally, when dealing with extensive models and datasets, training weak teachers, even with a limited number of epochs, still remains a significant overhead. In this case, post-generation allows for the rapid generation of many weak models with low computational costs. Also, pruned models exhibit enhanced inference speeds, further boosting the efficiency of our method.

To verify the rationality of this strategy, we conduct validation experiments. We measure the distance between the student and teacher models at different stages and the accuracy of the distilled data generated from teacher models at different stages. Here, we use KL divergence to measure model distance, capturing the information loss when approximating one probability distribution with another. The results shown in Fig. [2c](#page-4-1) reveal a strong inverse correlation between model distance and distilled data performance. Closer distances indicate better approximation and superior performance.

Here, as we replace the student models with weak teachers, we can utilize the running mean and running variance for the original dataset from the batch normalization layers in the weak teachers instead of recalculating multiple times for each iteration. It will reduce a lot of computational costs, especially for largescale datasets. Additionally, the classifier prior  $l(S; \theta_S^{(T)})$  $S^{(1)}$ ) can also be replaced by ensemble classifier prior, as weak teachers may mislead generation direction. It will also benefit better alignment with the target classes. In summary, the final optimization objective is as follows:

<span id="page-7-0"></span>
$$
\sum_{t=T_b}^{T_e} \left( \sum_l ||\mu_l(f_{\theta_{\mathcal{T}}^{(t)}}(X_s)) - RM_{\theta_{\mathcal{T}}^{(t)}}^l(X_t))||_2 \right. \\
\left. + \sum_l ||\sigma_l^2(f_{\theta_{\mathcal{T}}^{(t)}}(X_s)) - RV_{\theta_{\mathcal{T}}^{(t)}}^l(X_t))||_2 + u \cdot l(\mathcal{S}; \theta_{\mathcal{T}}^{(t)})),\right.
$$
\n(7)

where  $RM$  and  $RV$  refer to the running mean and variance stored in the batch normalization layers.  $T_b$  and  $T_e$  (both at the early stage) are the starting and end points of the teacher training trajectory for statistic information matching. We only consider performing the matching for every m steps from  $T_b$  to  $T_e$ . For more theoretical details, please refer to the supplementary materials.

### 3.4 Algorithm Summary

In summary, we propose an efficient large-scale dataset distillation method, Teddy, solving the core bi-level optimization problem and further improving the efficiency via Taylor approximation. The framework of Teddy is shown in Algorithm [1.](#page-8-0) We first generate model pool M from single base model  $\theta_{base}$ . According to the state of the  $\theta_{base}$ , we choose from two efficient model pool generation strategies, prior and post-generation. Then, we follow our proposed optimization objective, Eq. [7,](#page-7-0) to update the synthetic dataset, which only requires the first-order optimization process. Here, considering the weak teacher models may mislead the data generation direction, we generate soft labels via weak teacher ensemble, which provides more richer label information expression.

Algorithm 1: Teddy Framework

<span id="page-8-0"></span>**Input:** Original dataset  $\mathcal{T}$ , single base model  $\theta_{base}$ **Output:** Synthetic dataset  $S$ Initialize  $S$ if  $\theta_{base}$  is from random or at early stage then  $\mathcal{L}$  Prior-generate model pool  $\mathcal{M}$ else if  $\theta_{base}$  is well-trained or at late stage then Post-generation model pool M while *not converge* do Randomly select  $n$  models from  $\mathcal M$ Compute  $\mathcal{L}(\mathcal{S}, \mathcal{T})$  as Eq. [7](#page-7-0) Back-propagate and update  $S$ Ensembly generate soft label via  $\mathcal{M}, Y_s = \frac{1}{|\mathcal{M}|} \sum_{\theta \in \mathcal{M}} h(\mathcal{A}(X_s); \theta)$  $\rho \sim h(\cdot; \theta)$  represents the model with parameter  $\theta$ , A is the function of data augmentation. return S

## 4 Experiments

In this section, we validate the efficacy of our proposed method, Teddy, via extensive experiments. Note that Teddy is designed for large-scale dataset distillation; we focus on two more challenging datasets, Tiny-ImageNet and full-size ImageNet. We also compare Teddy on the small datasets like CIFAR10 with fair comparison, showing its comparative performance (it is included in supplementary). We evaluate the cross-architecture generalization performance of our generated synthetic dataset and perform comprehensive ablation studies to demonstrate the effectiveness of the strategies adopted in our method.

### 4.1 Experiment Setting

Datasets and Networks To validate the efficacy and the efficiency of Teddy for large-scale datasets, we adopt two challenging datasets,  $64 \times 64$  Tiny-ImageNet and  $224 \times 224$  ImageNet-1K. While some previous methods have demonstrated effectiveness on downsized versions of ImageNet-1K or subsets, Teddy achieves impressive performance on the original, full-size ImageNet-1K. As for Tiny-ImageNet, following the instruction of MoCo (CIFAR) [\[12\]](#page-14-12) and the prior work  $S\text{Re}^2L$  [\[36\]](#page-16-8), we adopt modified ResNet18 as the teacher model by replacing the first Conv layer with the  $3\times3$  kernel, stride 1 Conv layer, and removing the first maxpool. For ImageNet-1K, we use ResNet18 [\[13\]](#page-14-13), implemented by the official torchvision. Moreover, to show the generalization capabilities of our method, we evaluate the cross-architecture classification performance of ImageNet-1K with IPC 10 on several model architectures: ResNet50 [\[13\]](#page-14-13), ResNet101 [13], DenseNet121 [\[14\]](#page-14-14), MobileNetV2 [\[27\]](#page-15-12), ShuffleNetV2 [\[21\]](#page-15-13), and EfficientNetB0 [\[30\]](#page-15-14).

<span id="page-9-0"></span>

| Method            |                | Tiny-ImageNet                               | $ImageNet-1K$                                                                                               |                  |                  |  |
|-------------------|----------------|---------------------------------------------|-------------------------------------------------------------------------------------------------------------|------------------|------------------|--|
|                   | 50             | 100                                         | 10                                                                                                          | 50               | 100              |  |
| Random (Conv)     | $15.1 \pm 0.3$ | $24.3 \pm 0.3$                              | $4.1 \pm 0.1^*$                                                                                             | $16.2 \pm 0.8^*$ | $19.5 \pm 0.5^*$ |  |
| Random (ResNet18) | $18.2 \pm 0.2$ | $25.0 \pm 0.2$                              | $6.8 \pm 0.1$                                                                                               | $32.0 \pm 0.2$   | $45.7 \pm 0.1$   |  |
| DC [41]           | $11.2 \pm 0.3$ |                                             | ٠                                                                                                           |                  |                  |  |
| <b>DSA</b> [39]   | $25.3 \pm 0.2$ |                                             |                                                                                                             |                  |                  |  |
| DM [40]           | $24.1 \pm 0.3$ | $29.4 \pm 0.2$                              |                                                                                                             |                  |                  |  |
| <b>IDM</b> [42]   | $27.7 \pm 0.3$ |                                             |                                                                                                             |                  |                  |  |
| MTT [3]           | $28.2 \pm 0.5$ | $33.7 \pm 0.6$                              |                                                                                                             |                  |                  |  |
| <b>FTD</b> [10]   | $31.5 \pm 0.3$ | $34.5 \pm 0.4$                              |                                                                                                             |                  |                  |  |
| TESLA [5]         | $33.4 \pm 0.5$ | $34.7 \pm 0.2$                              | $17.8 \pm 1.3^*$                                                                                            | $27.9 \pm 1.2^*$ | $29.2 \pm 1.0^*$ |  |
| $SRe^2L$ [36]     | $41.1 \pm 0.4$ | $49.7 \pm 0.3$                              | $21.3 \pm 0.6$                                                                                              | $46.8 \pm 0.2$   | $52.8 \pm 0.3$   |  |
| Ours $(post)$     |                | $44.5 \pm 0.2 (+3.4)$ $51.4 \pm 0.2 (+1.7)$ | $32.7 \pm 0.2 + 11.4$ $52.5 \pm 0.1 + 5.7$ $56.2 \pm 0.2 + 3.4$                                             |                  |                  |  |
| Ours (prior)      |                |                                             | $45.2 \pm 0.1 (+ 4.1) 52.0 \pm 0.2 (+ 2.3) 34.1 \pm 0.1 (+ 12.8) 52.5 \pm 0.1 (+ 5.7) 56.5 \pm 0.1 (+ 3.7)$ |                  |                  |  |

Table 1: Comparison with baseline methods. \* indicates the evaluation results on downsampled ImageNet-1K dataset. Here,  $SRe^{2}L$  and our proposed methods adopt the ResNet18 as the training and evaluation model, other methods adopt ConvNet.

<span id="page-9-1"></span>

| Method        | ResNet50                                                                                                                                                                     | ResNet101    | DenseNet121  | MobileNetV2  | ShuffleNetV2 | EfficientNetB0 |
|---------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------|--------------|--------------|--------------|----------------|
| $SRe^2L$ [36] | $28.4 \pm 0.1$                                                                                                                                                               | $30.9 + 0.1$ | $21.5 + 0.5$ | $10.2 + 0.2$ | $29.1 + 0.1$ | $16.1 + 0.1$   |
|               | <b>Ours (post)</b> $37.9 \pm 0.1 + 9.5$ $40.0 \pm 0.1 + 9.1$ $33.0 \pm 0.1 + 11.5$ $20.5 \pm 0.1 + 10.3$ $40.0 \pm 0.3 + 10.9$ $27.3 \pm 0.2 + 11.2$                         |              |              |              |              |                |
|               | Ours (prior) $39.0 \pm 0.1$ (+ $10.6$ ) $40.3 \pm 0.1$ (+ $9.4$ ) $34.3 \pm 0.1$ (+ $12.8$ ) $23.4 \pm 0.3$ (+ $13.2$ ) $38.5 \pm 0.1$ (+ $9.4$ ) $29.2 \pm 0.1$ (+ $13.1$ ) |              |              |              |              |                |

Table 2: Evaluation results of cross-architecture generalization under the ImageNet-1K with IPC 10 setting.  $S\text{Re}^2L$  and our methods use ResNet18 as the training model.

Implementation Details For prior model pool generation, we cache 9 checkpoints in the early stage of single teacher training trajectory to build the model pool for generating distilled data of ImageNet-1K, and 8 for Tiny-ImageNet. During the data generation phase, a subset of 3 models will be randomly selected to generate the  $i^{th}$  image for all classes, alleviating the mode collapse problem. As for the pruning-based model pool, we perform pruning on the offthe-shelf pre-trained ResNet18 provided by PyTorch, with the Top-1 accuracy of 69.76%, and finetune for very limited epochs, e.g., 0-2 epochs. The GFLOPs of the target pruned model is 1.2G, and the number of parameters is 7.72M. Here, we save 10 models for generating distilled data of ImageNet-1K, and 9 for Tiny-ImageNet. We randomly select 4 models from the model pool to generate the synthetic dataset for ImageNet-1K and 5 models for Tiny-ImageNet. Moreover, after generation, the augmented synthetic data will input the model pool again to get soft labels. For more details, please refer to the supplementary materials.

Evaluation and Baselines Following prior works [\[33,](#page-15-3) [40,](#page-16-3) [41\]](#page-16-2), we evaluate the distilled datasets by training several randomly initialized models from scratch and report the mean and standard deviation of their accuracy on the corresponding real test set. We compare our proposed methods with series methods, including DC [\[41\]](#page-16-2), DSA [\[40\]](#page-16-3), DM [\[40\]](#page-16-3), IDM [\[42\]](#page-16-5), MTT [\[3\]](#page-14-4), FTD [\[10\]](#page-14-6), TESLA [\[5\]](#page-14-5) and  $S\text{Re}^2L$  [\[36\]](#page-16-8). Here, we present the results derived from their original paper or DD benchmarks [\[4\]](#page-14-10) if available for selected baseline methods. However, as most existing methods are challenging to apply to the full-sized ImageNet-1K, we also report results on downsized ImageNet-1K. Additionally, the methods adopted to compare in the baseline all employ ConvNet, and SRe2L [\[36\]](#page-16-8) and our method utilize ResNet18 as the training and evaluation model.

### 4.2 Results on Baselines

As demonstrated in Table [1,](#page-9-0) our proposed method, Teddy, significantly surpasses all the baselines for both the Tiny-ImageNet and ImageNet-1K datasets with all IPC settings. Furthermore, comparing the results across the two datasets reveals that our method exhibits a more pronounced improvement on complex datasets. In particular, in the case of ImageNet-1K with IPC 10, our evaluation results outperform the previous state-of-the-art methods by a large margin up to 12.8%. Moreover, our method achieves the same performance with the setting of IPC 50 (52.5%) as the previous SOTA method in the case of IPC 100 (52.8%). Additionally, for DD, enhancing the performance of the larger synthetic dataset is particularly challenging. This is because the existing DD methods focus mainly on key dataset information, ignoring long-tail information. With increased IPCs, there is less room to enhance as compressing these details is hard. However, as shown in Table [1,](#page-9-0) our method considerably improves the performance for large IPCs (5.7% and 3.7% for IPC 50 and 100). For the post-generation, although we applied pruning to the teacher model, resulting in a reduction of statistic information space in its batch normalization layers, the results in Table [1](#page-9-0) indicate that the impact is not substantial. We can still achieve comparable performance.

### 4.3 Results on Cross-Architecture Generalization

The generalization capacity is vital for practically applying dataset distillation. Here, we evaluate the generalization performance of the distilled dataset of the ImageNet-1K IPC 10 setting. In selecting model architecture, we adopt the ResNet50, ResNet101, DenseNet121, MobileNetV2, ShuffleNetV2, and Efficient-NetB0. All results are reported in Table [2.](#page-9-1)

The results show that our proposed method, Teddy, surpasses all previous state-of-the-art methods by a significant margin. When the evaluation models have larger and deeper architectures, there is a significant and proportional performance improvement, comparing 34.1%, 39.0%, and 40.3% for ResNet18, 50, and 101, with enhancements of 12.8%, 10.6% and 9.4%. For structurally different networks, our method demonstrates strong generalization capabilities. For instance, on DenseNet121, MobileNetV2, ShuffleNetV2, and EfficientNetB0, we achieved 34.3%, 23.4%, 40.0%, and 29.2%, respectively, surpassing the SOTA by 12.8%, 13.2%, 10.9%, and 13.1%. As shown in Table [2,](#page-9-1) the evaluation results of the post-generation method indicate a strong generalization capability, even with the architecture heterogeneity introduced by network pruning, surpassing the previous SOTA methods.

### 4.4 Ablation Study

To substantiate the efficacy of our method, we perform ablation studies explicitly focusing on the effect of the model pool and the weak teachers. The outcomes

<span id="page-11-0"></span>Image /page/11/Figure/1 description: The image displays five plots labeled (a) through (e). Plot (a) is a line graph showing accuracy versus the number of teacher models, with two lines representing 'Post' and 'Prior' accuracies. The 'Post' accuracy starts at 30 and increases to 32.5 at 2 teacher models, then to 32.75 at 4 teacher models, and finally drops to 33.5 at 5 teacher models. The 'Prior' accuracy starts at 31 and increases to 34 at 3 teacher models, then drops to 33.75 at 4 teacher models and 5 teacher models. Plot (b) is similar to (a) but extends to 10 teacher models. The 'Post' accuracy starts at 49.5 and increases to 51.5 at 2 teacher models, then to 52 at 4 teacher models, 52.25 at 6 teacher models, 52.5 at 8 teacher models, and 52.75 at 10 teacher models. The 'Prior' accuracy starts at 50 and increases to 51.5 at 2 teacher models, then to 52 at 4 teacher models, 52.25 at 6 teacher models, 52.5 at 8 teacher models, and 52.75 at 10 teacher models. Plot (c) shows accuracy versus the size of the model pool, with lines for 'IPC 10' and 'IPC 50'. The 'IPC 10' accuracy starts at 24 and increases to 30.5 at 9 models. The 'IPC 50' accuracy starts at 46.5 and increases to 50.5 at 9 models. Plot (d) is a scatter plot showing accuracy versus runtime of generation in hours, with three sets of data points labeled 'SRe2L', 'ours-size 5', and 'ours-size 9'. The 'SRe2L' data points are around (4.5, 32) and (6.5, 21). The 'ours-size 5' data points are around (4, 31), (4.2, 33), and (4.5, 32.5). The 'ours-size 9' data points are around (4, 30.5), (4.2, 34), and (4.5, 33.5). Plot (e) shows accuracy versus continual learning steps, with lines for 'Random', 'SRe2L', and 'Ours'. The 'Random' accuracy starts at 35.5 and decreases to 25.25 at 5 steps. The 'SRe2L' accuracy starts at 32.5 and increases to 40.5 at 5 steps. The 'Ours' accuracy starts at 40 and increases to 45.25 at 5 steps.

Fig. 3: (a) Ablation study on different number of models ensemble to generate synthetic data. (b) Ablation study on different number of models ensemble to generate soft label. (c) Ablation study on size of the model pool under the setting of ImageNet-1K IPC 10 and IPC 50 with the prior-generation strategy. (d) The time and memory requirement of our method compared with the previous SOTA. Here the size of the points represents the peak GPU memory, and the three points, from left to right, report the evaluation results of 1, 2, and 3 teacher models ensemble utilized in generating the synthetic data. (e) Continual learning on Tiny-ImageNet IPC 50 with 5-step incremental protocol.

underscore the effectiveness of the employed strategies, leading to a substantial enhancement in the performance of our proposed method.

Effect of Model Pool We utilize the model pool of weak teachers as a substitute for students, reducing computational expenses. To analyze effectiveness, we conduct separate experiments for data generation and relabel phases.

For the data generation part, we conduct experiments on the setting of ImageNet-1K with IPC 10, and the setting of the model pool is the same as the main table. For data generation, 1, 2, 3, 4, or 5 models will be randomly ensemble selected. The soft labels are generated by the whole model pool. The results in Fig. [3a](#page-11-0) reveal that the performance is better with multiple models than with a single model. The accuracy drops in Fig. [3b](#page-11-0) can be attributed to the trade-off between trying fit numerous models comprehensively or concentrating on fitting fewer ones. Also, our method exhibits robustness in terms of the number of models selected for generation.

As for the relabel part, we conduct experiments on the setting of ImageNet-1K with IPC 50, and the setting of the model pool is the same as the main table. For data generation, 3 models are randomly selected from the model pool. Those generated data will be ensemble relabeled by 1, 2, 4, 6, 8, or whole models of the model pool. The evaluation results are shown in Fig. [3b.](#page-11-0) We observe a significant enhancement in performance with ensemble relabeling, but also robust for the number of models selected for relabeling.

Effect of Weak Teacher We conduct ablation studies on the stage of weak teachers adopted in our method under the setting of ImageNet-1K with IPC 10, IPC 50, and IPC 100. The results are shown in Fig. [2c.](#page-4-1) Here, we generate the model pool with weak teachers in different stages. We cache 9 models for each trajectory segment with step 5. For data generation, we randomly select 3 out of the model pool and use the whole models in the model pool to relabel the synthetic data. From the results, it is evident that the performance of teacher models significantly impacts the distilled data. For smaller IPCs, adopting the weak teacher models of the early stage is necessary.

Size of the Model Pool We also perform an ablation study on the size of the model pool, and the results are shown in Fig. [3c.](#page-11-0) The experiments are under the setting of ImageNet-1K IPC 10 and IPC 50. For one experiment setting, we cache the teacher models from the same trajectory segments; the beginning and ending points are the same as the main table but with different step lengths. Here, we randomly select 3 models from the model pool to generate data, and the whole model pool is adopted to generate the soft label. From the results, we observe that the size of the model pool does not significantly impact the performance of the generated synthetic data when the size is larger than 5. In this setting, a smaller size of the model pool may lead to an issue with mode collapse and a subsequent decline in performance.

### 4.5 Efficiency Evaluation

In this section, we evaluate the efficiency of our proposed method. The experiments are under the setting of ImageNet-1K IPC 10, utilizing 8 RTX 4090 GPUs. Here, the cached teachers are at stages 1 to 41. We report the total run time of model pre-training and data synthesizing, and the peak GPU memory of the data synthesizing process. The evaluation results are shown in Fig. [3d.](#page-11-0) From the results, due to the fact that we only need to train teacher models for a limited number of epochs (which is the major time overhead), the total time required for our method is significantly less than that of SRe<sup>2</sup>L. Noticing that when utilizing a single teacher model, the required GPU memory is the same as SRe<sup>2</sup>L, and the performance of the distilled data is notably superior to SRe<sup>2</sup>L.

### 4.6 Continual Learning

Continual learning (CL) [\[6,](#page-14-15) [25,](#page-15-15) [32\]](#page-15-16) focuses on learning tasks sequentially while prior task data is unavailable. In order to prevent catastrophic forgetting, one typical strategy is using a small buffer to retain critical information from past tasks. In this case, the capacity of dataset distillation to retain the information of the original datasets within a confined storage space benefits practical applications in continual learning.

Here, we follow the previous work [\[40\]](#page-16-3) employing the GDumb [\[23\]](#page-15-17) as the base method. This method stores and combines past and new training data to train a model from scratch. We evaluate our proposed method on Tiny-ImageNet with IPC 50 following the 5-step class-incremental protocol. The results are shown in Fig. [3e.](#page-11-0) Our method significantly outperforms the baseline methods and demonstrates an accuracy trend that differs from random selection, gradually increasing with the addition of categories. This trend is attributed to the soft label strategy, which allows for incorporating more cross-class knowledge.

<span id="page-13-0"></span>Image /page/13/Figure/1 description: This image displays a comparison of image generation results for three categories: Giant Panda, Bulbul, and Airship. Each category has two rows of generated images, labeled "SRe2L" and "Ours". The "Giant Panda" section shows several blurry images of pandas in a green, outdoor environment. The "Bulbul" section features blurry images of birds, some in flight and some perched, against light-colored backgrounds. The "Airship" section presents blurry images of airships in various aerial settings, including cloudy skies and landscapes.

**Fig. 4:** Visualization of synthetic data generated by  $S\text{Re}^{2}L$  and our method. The first row is generated by  $SRe^{2}L$ , and the second row is generated by ours with the priorgeneration strategy. Here, we choose three classes from ImageNet-1K: Giant Panda, Bulbul, and Airship.

### 4.7 Visualization

In this section, we present the visualization of ImageNet-1K synthetic datasets with IPC 50 setting generated by our method and  $\text{SRe}^2L$  [\[36\]](#page-16-8). Fig. [4](#page-13-0) shows that our method generates high quality, more distinctive features and a more comprehensive range of variations data, showing more perspective of the original dataset. In comparison, images generated from  $S\text{Re}^2L$ , while realistic, face the problem of mode collapse, especially for large IPCs.

## 5 Conclusion

In this paper, we introduce Teddy, an efficient large-scale dataset distillation method that can handle the full-size ImageNet-1K. Back up with theoretical analysis, we propose a memory-efficient approximation derived from Taylor expansion to decouple the bi-level optimization of the original DD into a first-order one. To further improve the time efficiency, instead of training a novel model for each synthetic data update, we propose two efficient model pool generation strategies, the prior and post-generation, corresponding to two practical cases. Both strategies can generate the model pool from one single base model. Lastly, we adopt ensemble soft relabeling to improve the generalizability of the distilled data. Additionally, to the best of our knowledge, we are the first to build a unified theoretical framework of the DD, linking the existing mainstream DD methods. Various experiments and ablation studies show the effectiveness of our proposed method, outperforming the prior state-of-the-art DD methods by a significant margin.

## Acknowledgements

This project is supported by the Ministry of Education, Singapore, under its Academic Research Fund Tier 2 (Award Number: MOE-T2EP20122-0006), and the National Research Foundation, Singapore, under its AI Singapore Programme (AISG Award No: AISG2-RP-2021-023).

## References

- <span id="page-14-7"></span>1. Bohdal, O., Yang, Y., Hospedales, T.: Flexible dataset distillation: Learn labels instead of images. arXiv preprint arXiv:2006.08572 (2020)
- <span id="page-14-3"></span>2. Bubeck, S., Chandrasekaran, V., Eldan, R., Gehrke, J., Horvitz, E., Kamar, E., Lee, P., Lee, Y.T., Li, Y., Lundberg, S., et al.: Sparks of artificial general intelligence: Early experiments with gpt-4. arXiv preprint arXiv:2303.12712 (2023)
- <span id="page-14-4"></span>3. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Dataset distillation by matching training trajectories. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 4750–4759 (2022)
- <span id="page-14-10"></span>4. Cui, J., Wang, R., Si, S., Hsieh, C.J.: Dc-bench: Dataset condensation benchmark. Advances in Neural Information Processing Systems 35, 810–822 (2022)
- <span id="page-14-5"></span>5. Cui, J., Wang, R., Si, S., Hsieh, C.J.: Scaling up dataset distillation to imagenet-1k with constant memory. In: International Conference on Machine Learning. pp. 6565–6590. PMLR (2023)
- <span id="page-14-15"></span>6. De Lange, M., Aljundi, R., Masana, M., Parisot, S., Jia, X., Leonardis, A., Slabaugh, G., Tuytelaars, T.: A continual learning survey: Defying forgetting in classification tasks. IEEE transactions on pattern analysis and machine intelligence 44(7), 3366–3385 (2021)
- <span id="page-14-0"></span>7. Deng, J., Dong, W., Socher, R., Li, L.J., Li, K., Fei-Fei, L.: Imagenet: A largescale hierarchical image database. In: 2009 IEEE conference on computer vision and pattern recognition. pp. 248–255. Ieee (2009)
- <span id="page-14-8"></span>8. Deng, Z., Russakovsky, O.: Remember the past: Distilling datasets into addressable memories for neural networks. Advances in Neural Information Processing Systems 35, 34391–34404 (2022)
- <span id="page-14-1"></span>9. Dosovitskiy, A., Beyer, L., Kolesnikov, A., Weissenborn, D., Zhai, X., Unterthiner, T., Dehghani, M., Minderer, M., Heigold, G., Gelly, S., et al.: An image is worth  $16x16$  words: Transformers for image recognition at scale. arXiv preprint arXiv:2010.11929 (2020)
- <span id="page-14-6"></span>10. Du, J., Jiang, Y., Tan, V.Y., Zhou, J.T., Li, H.: Minimizing the accumulated trajectory error to improve dataset distillation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3749–3758 (2023)
- <span id="page-14-11"></span>11. Fang, G., Ma, X., Song, M., Mi, M.B., Wang, X.: Depgraph: Towards any structural pruning. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 16091–16101 (2023)
- <span id="page-14-12"></span>12. He, K., Fan, H., Wu, Y., Xie, S., Girshick, R.: Momentum contrast for unsupervised visual representation learning. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 9729–9738 (2020)
- <span id="page-14-13"></span>13. He, K., Zhang, X., Ren, S., Sun, J.: Deep residual learning for image recognition. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 770–778 (2016)
- <span id="page-14-14"></span>14. Huang, G., Liu, Z., Van Der Maaten, L., Weinberger, K.Q.: Densely connected convolutional networks. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 4700–4708 (2017)
- <span id="page-14-9"></span>15. Kim, J.H., Kim, J., Oh, S.J., Yun, S., Song, H., Jeong, J., Ha, J.W., Song, H.O.: Dataset condensation via efficient synthetic-data parameterization. In: International Conference on Machine Learning. pp. 11102–11118. PMLR (2022)
- <span id="page-14-2"></span>16. Krizhevsky, A., Sutskever, I., Hinton, G.E.: Imagenet classification with deep convolutional neural networks. Advances in neural information processing systems 25 (2012)

- 16 Yu et al.
- <span id="page-15-10"></span>17. Lee, H.B., Lee, D.B., Hwang, S.J.: Dataset condensation with latent space knowledge factorization and sharing. arXiv preprint arXiv:2208.10494 (2022)
- <span id="page-15-11"></span>18. Liu, S., Wang, K., Yang, X., Ye, J., Wang, X.: Dataset distillation via factorization. Advances in Neural Information Processing Systems 35, 1100–1113 (2022)
- <span id="page-15-5"></span>19. Liu, S., Wang, X.: Mgdd: A meta generator for fast dataset distillation. Advances in Neural Information Processing Systems 36 (2024)
- <span id="page-15-7"></span>20. Liu, S., Ye, J., Yu, R., Wang, X.: Slimmable dataset condensation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 3759–3768 (2023)
- <span id="page-15-13"></span>21. Ma, N., Zhang, X., Zheng, H.T., Sun, J.: Shufflenet v2: Practical guidelines for efficient cnn architecture design. In: Proceedings of the European conference on computer vision (ECCV). pp. 116–131 (2018)
- <span id="page-15-6"></span>22. Nguyen, T., Chen, Z., Lee, J.: Dataset meta-learning from kernel ridge-regression. arXiv preprint arXiv:2011.00050 (2020)
- <span id="page-15-17"></span>23. Prabhu, A., Torr, P.H., Dokania, P.K.: Gdumb: A simple approach that questions our progress in continual learning. In: Computer Vision–ECCV 2020: 16th European Conference, Glasgow, UK, August 23–28, 2020, Proceedings, Part II 16. pp. 524–540. Springer (2020)
- <span id="page-15-1"></span>24. Ramesh, A., Dhariwal, P., Nichol, A., Chu, C., Chen, M.: Hierarchical textconditional image generation with clip latents. arXiv preprint arXiv:2204.06125 1(2), 3 (2022)
- <span id="page-15-15"></span>25. Rebuffi, S.A., Kolesnikov, A., Sperl, G., Lampert, C.H.: icarl: Incremental classifier and representation learning. In: Proceedings of the IEEE conference on Computer Vision and Pattern Recognition. pp. 2001–2010 (2017)
- <span id="page-15-2"></span>26. Rombach, R., Blattmann, A., Lorenz, D., Esser, P., Ommer, B.: High-resolution image synthesis with latent diffusion models. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition. pp. 10684–10695 (2022)
- <span id="page-15-12"></span>27. Sandler, M., Howard, A., Zhu, M., Zhmoginov, A., Chen, L.C.: Mobilenetv2: Inverted residuals and linear bottlenecks. In: Proceedings of the IEEE conference on computer vision and pattern recognition. pp. 4510–4520 (2018)
- <span id="page-15-0"></span>28. Simonyan, K., Zisserman, A.: Very deep convolutional networks for large-scale image recognition. arXiv preprint arXiv:1409.1556 (2014)
- <span id="page-15-9"></span>29. Sucholutsky, I., Schonlau, M.: Soft-label dataset distillation and text dataset distillation. In: 2021 International Joint Conference on Neural Networks (IJCNN). pp. 1–8. IEEE (2021)
- <span id="page-15-14"></span>30. Tan, M., Le, Q.: Efficientnet: Rethinking model scaling for convolutional neural networks. In: International conference on machine learning. pp. 6105–6114. PMLR (2019)
- <span id="page-15-4"></span>31. Wang, K., Zhao, B., Peng, X., Zhu, Z., Yang, S., Wang, S., Huang, G., Bilen, H., Wang, X., You, Y.: Cafe: Learning to condense dataset by aligning features. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 12196–12205 (2022)
- <span id="page-15-16"></span>32. Wang, L., Zhang, X., Su, H., Zhu, J.: A comprehensive survey of continual learning: Theory, method and application. IEEE Transactions on Pattern Analysis and Machine Intelligence (2024)
- <span id="page-15-3"></span>33. Wang, T., Zhu, J.Y., Torralba, A., Efros, A.A.: Dataset distillation. arXiv preprint arXiv:1811.10959 (2018)
- <span id="page-15-8"></span>34. Ye, J., Yu, R., Liu, S., Wang, X.: Distilled datamodel with reverse gradient matching. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 11954–11963 (2024)

- <span id="page-16-0"></span>35. Ye, J., Yu, R., Liu, S., Wang, X.: Mutual-modality adversarial attack with semantic perturbation. In: Proceedings of the AAAI Conference on Artificial Intelligence. vol. 38, pp. 6657–6665 (2024)
- <span id="page-16-8"></span>36. Yin, Z., Xing, E., Shen, Z.: Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. arXiv preprint arXiv:2306.13092 (2023)
- <span id="page-16-7"></span>37. Yu, R., Liu, S., Wang, X.: Dataset distillation: A comprehensive review. arXiv preprint arXiv:2301.07014 (2023)
- <span id="page-16-6"></span>38. Zhang, L., Zhang, J., Lei, B., Mukherjee, S., Pan, X., Zhao, B., Ding, C., Li, Y., Xu, D.: Accelerating dataset distillation via model augmentation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 11950–11959 (2023)
- <span id="page-16-1"></span>39. Zhao, B., Bilen, H.: Dataset condensation with differentiable siamese augmentation. In: International Conference on Machine Learning. pp. 12674–12685. PMLR (2021)
- <span id="page-16-3"></span>40. Zhao, B., Bilen, H.: Dataset condensation with distribution matching. In: Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision. pp. 6514–6523 (2023)
- <span id="page-16-2"></span>41. Zhao, B., Mopuri, K.R., Bilen, H.: Dataset condensation with gradient matching. arXiv preprint arXiv:2006.05929 (2020)
- <span id="page-16-5"></span>42. Zhao, G., Li, G., Qin, Y., Yu, Y.: Improved distribution matching for dataset condensation. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 7856–7865 (2023)
- <span id="page-16-4"></span>43. Zhou, Y., Nezhadarya, E., Ba, J.: Dataset distillation using neural feature regression. Advances in Neural Information Processing Systems 35, 9813–9827 (2022)

## A Comprehensive Theoretical Analysis

Given the original dataset, denoted as  $\mathcal{T} = (X_t, Y_t)$ , where  $X_t \in \mathbb{R}^{N_t \times d}$  and  $Y_t \in \mathbb{R}^{N_t \times c}$ , DD aims to learn a much smaller synthetic dataset  $\mathcal{S} = (X_s, Y_s)$ , where  $X_s \in \mathbb{R}^{N_s \times d}$  and  $Y_s \in \mathbb{R}^{N_s \times c}$ , such that models trained on S and T are of comparable performance. Here,  $N_t$  and  $N_s$  are the number of data in  $\mathcal T$ and S, respectively, and  $N_s \ll N_t$ . d is number of features for each data, and c represents the number of classes for classification task.

For better generalization ability of  $S$ , it always adopts multiple inner loops to train a novel student model. Considering the small size of  $S$ , models trained on it can easily overfit. Thus, we have  $l(S; \theta_S^{(T)})$  $S^{(I)}(s) < \epsilon$ , where  $l(\cdot; \theta)$  is the loss function, T is the number of inner loops, and  $\epsilon$  is close to zero. With the Karush-Kuhn-Tucker (KKT) conditions, the problem can be formulated as:

$$
S = \underset{S}{\arg\min} \mathbb{E}_{\theta^{(0)} \sim \Theta}[l(\mathcal{T}; \theta_{\mathcal{S}}^{(T)}) + u(l(\mathcal{S}; \theta_{\mathcal{S}}^{(T)}) - \epsilon)]
$$
  
= 
$$
\underset{S}{\arg\min} \mathbb{E}_{\theta^{(0)} \sim \Theta}[l(\mathcal{T}; \theta_{\mathcal{S}}^{(T)}) + ul(\mathcal{S}; \theta_{\mathcal{S}}^{(T)})],
$$
 (8)

where u is the Lagrange multiplier,  $\Theta$  is the distribution of networks, and

$$
\theta_{\mathcal{S}}^{(t)} = \theta^{(0)} - \alpha \sum_{i=0}^{t-1} g_{\mathcal{S}}^{(i)} = \theta_{\mathcal{S}}^{(t-1)} - \alpha g_{\mathcal{S}}^{(t-1)}, \quad \text{(9)}
$$
$$
g_{\mathcal{S}}^{(t)} = \nabla_{\theta_{\mathcal{S}}^{(t)}} l(\mathcal{S}; \theta_{\mathcal{S}}^{(t)}).
$$

<span id="page-17-1"></span>Following the Proposition 1, we recursively apply the first-order Taylor expansion to the first term of the optimization objective, and it can be transformed into follows:

$$
l(\mathcal{T}; \theta_{\mathcal{S}}^{(T)}) = l(\mathcal{T}; \theta_{\mathcal{S}}^{(T-1)} - \alpha g_{\mathcal{S}}^{(T-1)})
$$

$$
= l(\mathcal{T}; \theta_{\mathcal{S}}^{(T-1)}) - \alpha g_{\mathcal{T}}^{(T-1)} \cdot g_{\mathcal{S}}^{(T-1)}
$$

$$
= l(\mathcal{T}; \theta_{\mathcal{S}}^{(T-i)}) - \alpha \sum_{t=T-i}^{T-1} g_{\mathcal{T}}^{(t)} \cdot g_{\mathcal{S}}^{(t)}
$$

$$
= l(\mathcal{T}; \theta^{(0)}) - \alpha \sum_{t=0}^{T-1} g_{\mathcal{T}}^{(t)} \cdot g_{\mathcal{S}}^{(t)} \quad (10)
$$

where  $g_{\mathcal{T}}^{(t)} = \nabla_{\theta_{\mathcal{S}}^{(t)}} l(\mathcal{T}; \theta_{\mathcal{S}}^{(t)})$  $S^{(t)}(S)$ . The Taylor-approximated version of the optimization objective is as follows:

<span id="page-17-0"></span>
$$
S = \underset{S}{\arg\min} \left[ -\alpha \sum_{t=0}^{T-1} g_{\mathcal{T}}^{(t)} \cdot g_{\mathcal{S}}^{(t)} + ul(\mathcal{S}; \theta_{\mathcal{S}}^{(T)}) \right]
$$
  
= 
$$
\underset{S}{\arg\min} \left[ -\sum_{t=0}^{T-1} \frac{g_{\mathcal{T}}^{(t)} \cdot g_{\mathcal{S}}^{(t)}}{||g_{\mathcal{T}}^{(t)}|| ||g_{\mathcal{S}}^{(t)}||} + u^{'} l(\mathcal{S}; \theta_{\mathcal{S}}^{(T)}) \right],
$$
(11)

where  $\theta^{(0)} \sim \Theta$ , and  $u' = \frac{u}{\sqrt{(\theta - \mu)}}$  $\frac{u}{\alpha||g_{\mathcal{T}}^{(t)}|| ||g_{\mathcal{S}}^{(t)}||}$ . The first term of the Eq. [11](#page-17-0) is the sum of the cosine distance of the gradients for the synthetic and original data, respectively, on the all checkpoints of the student trajectory. In other words, the meta-learning-based optimization objective can be Taylor-approximated as the sum of the multi-step gradient matching. Here, we denote  $e^{(t)} = \frac{g^{(t)}}{\ln a^{(t)}}$  $\frac{g^{(t)}}{||g^{(t)}||}$ , and for each gradient matching at step  $t$ , we have:

<span id="page-18-0"></span>
$$
-e_{\mathcal{T}}^{(t)} \cdot e_{\mathcal{S}}^{(t)} = \frac{1}{2} ||e_{\mathcal{T}}^{(t)} - e_{\mathcal{S}}^{(t)}||^2 - 1.
$$
 (12)

So for each step of gradient matching, we only need to optimize the  $l^2$  distance between the gradients of the original data and the synthetic data, such that:

$$
S = \underset{S}{\arg\min} [\frac{1}{2} \sum_{t=0}^{T-1} ||e_{\mathcal{T}}^{(t)} - e_{\mathcal{S}}^{(t)}||^2 + u^{'} l(\mathcal{S}; \theta_{\mathcal{S}}^{(T)})]. \tag{13}
$$

However, the Eq. [13](#page-18-0) shows that each update step of  $\mathcal S$  should compute the gradient of  $S$  and  $T$  on all checkpoints of the student trajectory, which is a large amount of computational costs. Here, we reduce the costs by adopting another approximation strategy, turning the second-order optimization into the firstorder one. Following the Proposition 2, for simplicity in explanation, we only consider the last linear layer of the network is updated during the generation process, and the parameter is denoted as W. The preceding layers are regraded as the feature extractor, denoted as  $f_{\theta}$ . For each gradient, we have:

<span id="page-18-1"></span>
$$
g = \frac{\partial l(\cdot; \theta)}{\partial W} = \frac{1}{|X|} f_{\theta}(X)^{T} (f_{\theta}(X)W - Y)
$$

$$
= \frac{1}{|X|} f_{\theta}(X)^{T} f_{\theta}(X)W - \frac{1}{|X|} f_{\theta}(X)^{T} Y.
$$
 $(14)$ 

The first term of Eq. [14](#page-18-1) is a weighted covariance matrix of the feature space, and the second term is the class-wise mean of the feature space. The gradient matching for  $l^2$  distance can be transformed as:

<span id="page-18-2"></span>
$$
\begin{split}\n&||(\frac{1}{N_t}f_{\theta}(X_t)^T f_{\theta}(X_t) - \frac{1}{N_s}f_{\theta}(X_s)^T f_{\theta}(X_s))W \\
&- (\frac{1}{N_t}f_{\theta}(X_t)^T Y - \frac{1}{N_s}f_{\theta}(X_s)^T Y)||^2 \frac{1}{||W||^2} \\
&\leq ||\frac{1}{N_t}f_{\theta}(X_t)^T f_{\theta}(X_t) - \frac{1}{N_s}f_{\theta}(X_s)^T f_{\theta}(X_s)||^2 \\
&+ ||\frac{1}{N_t}f_{\theta}(X_t)^T Y - \frac{1}{N_s}f_{\theta}(X_s)^T Y||^2 \frac{1}{||W||^2}.\n\end{split} \tag{15}
$$

From Eq. [15,](#page-18-2) it shows that the upper bound of the gradient matching is the first-order and the second-order statistic information matching in feature space. Here, we need to consider the prior condition such that  $\theta^{(0)} \sim \Theta$ . It means

that for every  $\theta^{(0)}$  samples from the distribution  $\Theta$ , Eq. [15](#page-18-2) reaches the minimum value. In this case, the first term of Eq. [15,](#page-18-2) the covariance for the original data and synthetic data, is equivalent for all  $\theta^{(0)}$  samples, and the class-wise mean. Also, the equality in Eq. [15](#page-18-2) holds. As for  $\theta^{(t)}$ , as long as the samples are sufficient, the condition still holds. To be more specific, here we assume  $f_{\theta}(X_t) \in \mathbb{R}^{N_t \times f_d}$ , and  $f_{\theta}(X_s) \in \mathbb{R}^{N_s \times f_d}$ ,  $f_d$  is the dimension of the feature.  $f_{\theta}(X_t) = [f_{\theta}(x_1^t)^T, \ldots, f_{\theta}(x_{N_t}^t)^T]^T$ , and  $f_{\theta}(x_i^t) \in \mathbb{R}^{f_d}$ . Also,  $f_{\theta}(X_s) =$  $[f_{\theta}(x_1^{s})^T, \ldots, f_{\theta}(x_{N_s}^{s})^T]^T$ , and  $f_{\theta}(x_i^{s}) \in \mathbb{R}^{f_d}$ .  $f_{\theta}(X_t) = [F_1^t, \ldots, F_{f_d}^t]$ ,  $F_i^t \in \mathbb{R}^{N_t}$ , and  $f_{\theta}(X_s) = [F_1^s, \ldots, F_{f_d}^s], F_i^s \in \mathbb{R}^{N_s}$ . For the first term of Eq. [15,](#page-18-2) we have:

<span id="page-19-0"></span>
$$
\frac{1}{N_t} f_{\theta}(X_t)^T f_{\theta}(X_t) - \frac{1}{N_s} f_{\theta}(X_s)^T f_{\theta}(X_s)
$$
$$
= \frac{1}{N_t} [(F_1^t)^T, \dots, (F_{f_d}^t)^T]^T [F_1^t, \dots, F_{f_d}^t]
$$
$$
- \frac{1}{N_s} [(F_1^s)^T, \dots, (F_{f_d}^s)^T]^T [F_1^s, \dots, F_{f_d}^s]
$$
$$
= \begin{bmatrix} Var(F_1^t) & \cdots & Cov(F_1^t, F_{f_d}^t) \ \vdots & \ddots & \vdots \ Cov(F_{f_d}^t, F_1^t) & \cdots & Var(F_{f_d}^t) \end{bmatrix} \qquad (16)
$$
$$
- \begin{bmatrix} Var(F_1^s) & \cdots & Cov(F_1^s, F_{f_d}^s) \ \vdots & \ddots & \vdots \ Cov(F_{f_d}^s, F_1^s) & \cdots & Var(F_{f_d}^s) \end{bmatrix}.
$$

Eq. [16](#page-19-0) shows that if the covariance matching holds, then variance matching also holds. To improve the calculation efficiency, here we adopt variance matching instead of covariance matching. As for the second term, it is the class-wise mean matching in feature space. Specifically, we have:

<span id="page-19-1"></span>
$$
\frac{1}{N_t} f_{\theta}(X_t)^T Y - \frac{1}{N_s} f_{\theta}(X_s)^T Y \\ = \frac{1}{N_t} [f_{\theta}(x_1^t), \dots, f_{\theta}(x_{N_t}^t)] \mathcal{M}_c^t \quad (17) \\ - \frac{1}{N_s} [f_{\theta}(x_1^s), \dots, f_{\theta}(x_{N_s}^s)] \mathcal{M}_c^s.
$$

Here,  $\mathcal{M}_c^t \in \mathbb{R}^{N_t \times c}$  and  $\mathcal{M}_c^s \in \mathbb{R}^{N_s \times c}$ , are the matrices to indicate whether the samples belong to the classes. For instance,  $M_{ij} = 1$  means the *i*<sup>th</sup> sample is belong to the  $j^{th}$  class, otherwise, it is not. The Eq. [17](#page-19-1) can be:

Ξ

$$
\frac{1}{N_t} \left[ \sum_{i=1}^{N_t} f_{\theta}(x_i^t) M_{i1}^t, \dots, \sum_{i=1}^{N_t} f_{\theta}(x_i^t) M_{ic}^t \right]
$$

$$
- \frac{1}{N_s} \left[ \sum_{i=1}^{N_s} f_{\theta}(x_i^s) M_{i1}^s, \dots, \sum_{i=1}^{N_s} f_{\theta}(x_i^s) M_{ic}^s \right]
$$

$$
= \frac{1}{N_t} \left[ \sum_{x_i^t \in Cl_1} f_{\theta}(x_i^t), \dots, \sum_{x_i^t \in Cl_c} f_{\theta}(x_i^t) \right]
$$

$$
- \frac{1}{N_s} \left[ \sum_{x_i^s \in Cl_1} f_{\theta}(x_i^s), \dots, \sum_{x_i^s \in Cl_c} f_{\theta}(x_i^s) \right], (18)
$$

where  $Cl_i$  is the set of samples belong to  $i^{th}$  class. When the classes of the original dataset is balanced, or the number of samples in every class of the original dataset is same, the second term of Eq. [15](#page-18-2) can be replaced by the global mean. Therefore, the Eq. [13](#page-18-0) can be transformed as follows:

<span id="page-20-0"></span>
$$
\begin{aligned}
&\sum_{t=0}^{T-1} \left( \sum_{l} ||\mu_l(f_{\theta_{\mathcal{S}}^{(t)}}(X_s)) - \mu_l(f_{\theta_{\mathcal{S}}^{(t)}}(X_t))||_2 \right) \\
&+ \sum_{l} ||\sigma_l^2(f_{\theta_{\mathcal{S}}^{(t)}}(X_s)) - \sigma_l^2(f_{\theta_{\mathcal{S}}^{(t)}}(X_t))||_2 \\
&+ u \cdot l(\mathcal{S}; \theta_{\mathcal{S}}^{(T)}) \quad &(19)
\end{aligned}
$$

where  $\theta^{(0)} \sim \Theta$ ,  $\mu_l$  and  $\sigma_l^2$  refer to the mean and variance of the  $l^{th}$  layer features. Although Eq. [19](#page-20-0) has significantly improved efficiency, computing the mean and variance of checkpoints at each inner loop remains a substantial computational overhead, especially when there are numerous inner loops and a relatively large original dataset. Here, we propose substituting performance-comparable weak teachers for student models and reducing the number of rounds required for inner loop. As for the student trajectory starting at t and ending at  $t + m$ , we have:

<span id="page-20-1"></span>
$$
l(\mathcal{T}; \theta_{\mathcal{S}}^{(t+m)}) = l(\mathcal{T}; \theta_{\mathcal{S}}^{(t)}) - \alpha \sum_{i=t}^{t+m-1} g_{\mathcal{S}}^{(i)}
$$

$$
= l(\mathcal{T}; \theta_{\mathcal{S}}^{(t)}) - \alpha \left( \sum_{i=t}^{t+m-1} g_{\mathcal{S}}^{(i)} \right) \cdot g_{\mathcal{T}}^{(t)}.
$$
 $(20)$ 

From Eq. [20,](#page-20-1) it shows that the multi-step gradient of the model on the synthetic dataset compared to the single-step gradient on the original dataset. Therefore, we cache the weak teacher for every m steps from  $T_b$  to  $T_e$ . Here, we also utilize the running mean and running variance stored in the batch normalization layers of weak teachers to replace the mean and variance of the original dataset in

<span id="page-20-2"></span>21

feature space, reducing computational costs. The optimization objective is as follows:

$$
\sum_{t=T_b}^{T_e} \left( \sum_l \left\Vert \mu_l(f_{\theta_{\mathcal{T}}}^{(t)}(X_s)) - RM_{\theta_{\mathcal{T}}}^{(t)}(X_t) \right\Vert_2 \right) + \sum_l \left\Vert \sigma_l^2(f_{\theta_{\mathcal{T}}}^{(t)}(X_s)) - RV_{\theta_{\mathcal{T}}}^{(t)}(X_t) \right\Vert_2 + u \cdot l(\mathcal{S}; \theta_{\mathcal{T}}^{(t)}) \right)
$$

$$
(21)
$$

where  $T_b$  and  $T_e$  is the starting checkpoint and the end point of the teacher training trajectory.

## B Error Analysis for Taylor Approximation

In Eq. [10,](#page-17-1) we recursively apply the first-order Taylor expansion to the original optimization objective to decouple the bi-level optimization process. This section will further analyze the error caused by Taylor approximation part, and theoretical reasonability.

Proposition 3 Taylor approximation minimizes the upper bound of the original loss function.

Proof. Considering each Taylor approximation iteration independently is as follows:

$$
l(\mathcal{T}; \theta_{\mathcal{S}}^{(t)}) = ||f(X_t; \theta_{\mathcal{S}}^{(t)}) - Y_t||
$$
$$
\leq ||f(X_t; \theta_{\mathcal{S}}^{(t)}) - f(X_t; \theta_{\mathcal{T}}^{(t)})|| + ||f(X_t; \theta_{\mathcal{T}}^{(t)}) - Y_t||
$$
$$
\leq \beta ||\theta_{\mathcal{S}}^{(t)} - \theta_{\mathcal{T}}^{(t)}|| + ||f(X_t; \theta_{\mathcal{T}}^{(t)}) - Y_t||
$$
$$
= \beta ||g_{\mathcal{S}}^{(t-1)} - g_{\mathcal{T}}^{(t-1)}|| + C
$$
$$
= \beta g_{\mathcal{S}}^{(t-1)} \cdot g_{\mathcal{T}}^{(t-1)} + C,
$$
 (22)

where  $\beta$  is the Lipschitz constant and C is irrelevant to the optimization.

We also conduct validation experiments under the setting of CIFAR10 IPC 5 with the original DD optimization objective and our Taylor-approximation version. The results are shown in Fig. [5.](#page-22-0) We evaluate the difference of average losses, average accuracy per iteration, and peak accuracy during training. The results demonstrate that the bound is tight in practice, and indicate that errors introduced by our approximation are negligible and the overall training dynamics are comparable.

<span id="page-22-0"></span>Image /page/22/Figure/1 description: The image contains two plots. The left plot shows the loss over training epochs for different numbers of inner loops (10, 20, 30, 40, 60, 80). The x-axis is labeled "# of Training Epochs" and ranges from 0 to 100. The y-axis is labeled "Loss" and ranges from 2.0 to 2.4. The right plot shows the difference in metrics (Average Loss Diff., Average Acc. Diff., Peak Acc. Diff.) against the number of inner loops. The x-axis is labeled "# of Inner Loops" and ranges from 20 to 80. The y-axis is labeled "Difference" and ranges from 0.00 to 0.05. The legend for the left plot indicates different colored lines for each number of inner loops, with dashed lines also present. The legend for the right plot shows the three difference metrics in green, red, and blue respectively.

Fig. 5: Left: training loss of the original DD (dashed line) and our approximated objectives (solid line); Right: the difference of average loss, average accuracy and peak accuracy during training.

<span id="page-22-1"></span>

| Imbalanced dataset Acc.(%) / F1(%) |             | SRe2L         | Ours          | Ours with Class-wise Statistics |
|------------------------------------|-------------|---------------|---------------|---------------------------------|
| 60%~100%                           | 24.2 / 24.2 | 32.7 (+ 8.5)  | 32.5 (+ 8.3)  | 36.0 (+ 11.8) / 36.0 (+ 11.8)   |
| 40%~100%                           | 23.0 / 23.0 | 33.6 (+ 10.2) | 33.3 (+ 10.3) | 35.6 (+ 12.2) / 35.3 (+ 12.3)   |

Table 3: Results on imbalanced dataset with each class has 60%-100% and 40%-100% samples.

<span id="page-22-2"></span>

| Acc. $(\%)$                                  |                 | 10                                                                                                                      | 50             |  | $ mAP (%) 10$ Imgs 20 Imgs | 40 Imgs |
|----------------------------------------------|-----------------|-------------------------------------------------------------------------------------------------------------------------|----------------|--|----------------------------|---------|
| $\mathbf{S}\mathbf{R}\mathbf{e}^2\mathbf{L}$ |                 | 17.8 48.4 baseline 3.6 5.6<br>Ours 21.0 (+ 15.6) 37.2 (+ 19.4) 58.5 (+ 10.1) ours 6.4 (+ 2.8) 10.4 (+ 4.8) 15.3 (+ 5.4) |                |  |                            | 9.9     |
|                                              | $-11$ $-1$ $-1$ |                                                                                                                         | $\blacksquare$ |  |                            |         |

Table 4: Results on larger model (left) and other task (right).

## C More Experimental Results

### C.1 Experimental Results on Imbalanced Dataset

In Eq. [18,](#page-20-2) we assume that the dataset is balanced. This section will further analyze this condition and show the robustness of our proposed method. We conduct the experiments under the setting of Tiny-ImageNet IPC 20. For each class of Tiny-ImageNet, we randomly select 60%∼100% and 40%∼100% of the original daraset to build the imbalanced dataset. The results are shown in the Table [3.](#page-22-1) Even if imbalanced models are used, our method still demonstrates some resistance to this issue compared with the baseline as shown in Table [3.](#page-22-1) This can be attributed to more informative statistics from diversified teachers for model-to-data synthesis. Moreover, the performance could be further improved by considering the class-wise statistics.

### C.2 Experimental Results on Larger Network

To further demonstrate the effectiveness of our proposed method for larger networks, we conduct the experiments on ResNet50. Here, ResNet50 is utilized as the model pool base architecture, and the downstream network. The results are

shown in the Table [4](#page-22-2) (left). Our method achieves superior performance for larger models.

### C.3 Experimental Results on Other Task

Current DD field mainly focuses on classification, with detection and other tasks research still blank. Here, we simply apply our method on the detection task to show the generalizability of our proposed method across different tasks. We conduct the experiments on Pascal VOC, and the architecture of the detector is Faster RCNN. Table [4](#page-22-2) (right) shows the preliminary results of our method and baseline adapted to object detection on Pascal VOC, demonstrating the potential of our method in wider applications.

### C.4 Experimental Results on Small Dataset

Our proposed method, Teddy, is designed mainly for large-scale datasets as we do several Taylor-approximation strategies to improve the efficiency of the original solution to the DD definition, which may cause information loss. However, it still shows quite competitive performance compared with baselines SRe<sup>2</sup>L and DM on small datasets. Here, we adopt CIFAR-10 and CIFAR-100 for experiments. Specifically, we drop the soft label adopted in both  $S\text{Re}^2L$  and our method for fair comparison. Also, we only use the DSA strategy for data augmentation. The evaluation results are shown in Table [5.](#page-24-0) It shows that our weak-teacher strategy is a more favorable surrogate of the original ones than fully-converged models  $(SRe<sup>2</sup>L)$  and random initialization without training  $(DM)$ .

## D More Implementation Details

### D.1 Model Pool Generation

In our experiments, we have two strategies to generate the model pool: priorgeneration and post-generation. The base network architecture is ResNet18. For prior-generation, we follow the official torchvision code to train ResNet18 for ImageNet-1K and modified ResNet18 for Tiny-ImageNet. We adopt different stage teacher models for different IPC settings. The size of the model pool is 9 for ImageNet-1K and 8 for Tiny-ImageNet. For more details, please refer to Table [6.](#page-24-1)

As for post-generation, we utilize the pre-trained ResNet18 provided by the official torchvision for ImageNet-1K and well-trained modified ResNet18 for Tiny-ImageNet. We use DepGraph to perform structural pruning with the random standard, and finetune the pruned models for very limited epochs, e.g., 0-2 epochs, with learning rate from 0.1 to 0.001. It is worth noting that during finetuning, we simulate the accuracy curve of teacher models in trajectory-based model pool. The GFLOPs of the target pruned model is 1.2G, and the number of parameters is 7.72M. The size of model pool is 10 for ImageNet-1K and 9 for Tiny-ImageNet. For more details, please refer to Table [6.](#page-24-1)

<span id="page-24-0"></span>

| Method                                                                                                                                   | $CIFAR-10$ |    |    | $CIFAR-100$                                                                               |    |    |
|------------------------------------------------------------------------------------------------------------------------------------------|------------|----|----|-------------------------------------------------------------------------------------------|----|----|
|                                                                                                                                          |            | 10 | 50 |                                                                                           | 10 | 50 |
| DM (noise)                                                                                                                               |            |    |    | $25.6 \pm 0.2$ $49.8 \pm 0.3$ $59.5 \pm 0.1$ $11.4 \pm 0.2$ $29.6 \pm 0.3$ $36.9 \pm 0.1$ |    |    |
| $DM$ (real)                                                                                                                              |            |    |    | $26.0 \pm 0.8$ $48.9 \pm 0.6$ $63.0 \pm 0.4$ $11.4 \pm 0.3$ $29.7 \pm 0.3$ $43.6 \pm 0.4$ |    |    |
| <b>SRe<sup>2</sup>L</b> w/o soft label (noise) $24.9 \pm 0.2$ $35.8 \pm 0.4$ $37.0 \pm 0.2$ $10.8 \pm 0.2$ $18.4 \pm 0.1$ $25.6 \pm 0.1$ |            |    |    |                                                                                           |    |    |
| $SRe^2L$ w/o soft label (real)                                                                                                           |            |    |    | $27.1 \pm 0.4$ 44.7 $\pm$ 0.2 55.7 $\pm$ 0.2 10.7 $\pm$ 0.3 24.6 $\pm$ 0.1 39.2 $\pm$ 0.1 |    |    |
| Ours $w/o$ soft label (noise)                                                                                                            |            |    |    | $29.2 \pm 0.3$ $51.0 \pm 0.1$ $63.6 \pm 0.1$ $12.5 \pm 0.2$ $30.6 \pm 0.1$ $44.4 \pm 0.2$ |    |    |
| Ours $w/o$ soft label (real)                                                                                                             |            |    |    | $30.1 \pm 0.4$ 53.0 $\pm$ 0.3 66.1 $\pm$ 0.1 13.5 $\pm$ 0.2 33.4 $\pm$ 0.1 49.4 $\pm$ 0.4 |    |    |

Table 5: Results on small datasets CIFAR-10 and CIFAR-100. For a fair comparison, we drop the soft label adopted in both  $SRe^{2}L$  and our proposed method and only use DSA as the data augmentation strategy.

### D.2 Data Generation

For ImageNet-1K, we randomly select 3 models from the prior-generated model pool or 4 models from the post-generated model pool to generate the  $i^{th}$  image for all classes. As for Tiny-ImageNet, we randomly select 3 models from priorgenerated model pool or 5 models from the post-generated model pool. The batch size is 100, and the number of synthetic data generation iterations is 6000. For more details, please refer to Table [7.](#page-25-0)

<span id="page-24-1"></span>

|               | Hypeparameter                | Prior-Generation                                          | Post-Generation |
|---------------|------------------------------|-----------------------------------------------------------|-----------------|
| Tiny-ImageNet | Optimizer                    | SGD                                                       | SGD             |
|               | Base Learning Rate           | 0.2                                                       | 0.1-0.001       |
|               | Learning Rate Scheduler      | Cosine                                                    | Step            |
|               | Weight Decay                 | 1e-4                                                      | 1e-4            |
|               | Learning Rate Step Size      | -                                                         | 0-2             |
|               | Momentum                     | -                                                         | 0.9             |
|               | Batch Size                   | 256                                                       | 32              |
|               | Model Pool Size              | 8                                                         | 9               |
|               | Training / Finetuning Epochs | 46, 50                                                    | 0-2             |
|               | Stage of Teachers            | 11-46, with step 5; 16-50, with step 5                    | -               |
| ImageNet-1K   | Optimizer                    | SGD                                                       | SGD             |
|               | Base Learning Rate           | 0.1                                                       | 0.1-0.001       |
|               | Learning Rate Scheduler      | Step                                                      | Step            |
|               | Weight Decay                 | 1e-4                                                      | 1e-4            |
|               | Learning Rate Step Size      | 30                                                        | 0-2             |
|               | Momentum                     | 0.9                                                       | 0.9             |
|               | Batch Size                   | 32                                                        | 32              |
|               | Model Pool Size              | 9                                                         | 10              |
|               | Training / Finetuning Epochs | 41, 61, 71                                                | 0-2             |
|               | Stage of Teachers            | 1-41, with step 5; 21-61, with step 5; 31-71, with step 5 | -               |

Table 6: The hyper-parameters for model pool generation part.

### D.3 Validation

For the validation part, the generated data is first augmented using the CutMix strategy, and then the soft labels of the augmented data are generated by the

<span id="page-25-0"></span>

| Hyperparameter | Prior-Generation                                                                                | Post-Generation |          |
|----------------|-------------------------------------------------------------------------------------------------|-----------------|----------|
| Tiny-ImageNet  | Optimizer                                                                                       | Adam            | Adam     |
|                | Base Learning Rate                                                                              | 0.1             | 0.1      |
|                | Learning Rate Scheduler                                                                         | Cosine          | Cosine   |
|                | Weight of BN Loss                                                                               | 1.0             | 1.0      |
|                | Weight Decay                                                                                    | 1e-4            | 1e-4     |
|                | Momentum                                                                                        | 0.5, 0.9        | 0.5, 0.9 |
|                | Batch Size                                                                                      | 100             | 100      |
|                | Model Pool Size                                                                                 | 8               | 9        |
|                | Generation Iterations                                                                           | 4000            | 4000     |
|                | Number of Teachers Ensemble 3 for data generation at one time 5 for data generation at one time |                 |          |
| ImageNet-1K    | Optimizer                                                                                       | Adam            | Adam     |
|                | Base Learning Rate                                                                              | 0.25            | 0.25     |
|                | Learning Rate Scheduler                                                                         | Cosine          | Cosine   |
|                | Weight of BN Loss                                                                               | 0.01            | 0.01     |
|                | Weight Decay                                                                                    | 1e-4            | 1e-4     |
|                | Momentum                                                                                        | 0.5, 0.9        | 0.5, 0.9 |
|                | Batch Size                                                                                      | 100             | 100      |
|                | Model Pool Size                                                                                 | 9               | 10       |
|                | Generation Iterations                                                                           | 6000            | 6000     |
|                | Number of Teachers Ensemble 3 for data generation at one time 4 for data generation at one time |                 |          |

Table 7: The hyper-parameters for data generation part.

<span id="page-25-1"></span>Image /page/25/Figure/3 description: The image displays a grid of 9x10 images, with each column representing a different category. The categories are labeled as Gold Fish, Barrel, Golden Retriever, Gondola, Lesser Panda, King Penguin, Check, Monarch Butterfly, and Lampshade. Each category column contains three rows of smaller images, showcasing various visual representations related to the category title. For example, the Gold Fish column shows close-ups of goldfish, while the Monarch Butterfly column displays images of monarch butterflies.

Fig. 6: Visualization results of our proposed method for Tiny-ImageNet IPC 50.

model pool. The number of validation epochs for Tiny-ImageNet is 100, and for ImageNet-1K is 300. The training batch size is 256 for Tiny-ImageNet and 1024 for ImageNet-1K. The optimizer we adopted is AdamW, and the optimizer momentum is 0.9 and 0.999, respectively. The base learning rate is 0.001, the weight decay is 0.01, and the learning rate scheduler is Cosine.

## E More Visualization Results

Here, we provide additional visualizations of the synthetic dataset generated by our method, including different settings on Tiny-ImageNet (Fig. [6\)](#page-25-1) and ImageNet-1K (Fig. [7\)](#page-26-0). From a visualization perspective, our method showcases a broader range of perspectives and diversity. Here, for instance, in the whistle category, it can be observed that the focal points of the generated model pool by different methods are distinct. Post-generation focuses on the object itself, while prior-generation also pays attention to the surrounding environment,  $e.g.,$ the animal blowing the whistle.

<span id="page-26-0"></span>![A lighthouse on a hill overlooking the sea](https://image.pollinations.ai/prompt/A%20lighthouse%20on%20a%20hill%20overlooking%20the%20sea)

rion Prior-C<br>Beacon

Image /page/26/Picture/2 description: A collage of three images, each depicting a Venetian canal scene with gondolas. The top image shows gondolas on a bright blue canal with buildings and trees reflected in the water. The middle image features a narrow canal flanked by tall, dark buildings, with a gondola in the center. The bottom image displays a gondola on a canal with colorful buildings lining the sides.

Image /page/26/Picture/3 description: A collage of six abstract paintings, each depicting a Venetian canal scene with gondolas. The paintings vary in color palette and style, with some featuring vibrant blues and greens, while others lean towards earthy browns and grays. The brushstrokes are loose and impressionistic, giving the images a dreamlike quality.

Gondola

Image /page/26/Picture/4 description: This is a collage of four images, each depicting a desk with various items on it. The top row shows two images, and the bottom row shows two images. The bottom left image is labeled "Post-Generation" and the bottom right image is labeled "Prior-Generation". The images appear to be computer-generated renderings of office desks, with some variations in the arrangement of objects and the overall style.

Image /page/26/Picture/5 description: The image displays a series of labels and categories, likely representing data or classifications. At the top, there are labels such as "Prior-Generation" and "Post-Generation" repeated multiple times. Below these, there are distinct items labeled "Beacon", "Gondola", "Desk", and "Alp". Each of these items appears to be associated with the "Prior-Generation" and "Post-Generation" categories, suggesting a comparison or breakdown of these items across different time periods or states.

Generation Prior-Genera
Alp

Image /page/26/Picture/7 description: The image displays a collage of four surrealist paintings, each featuring melting clocks in a dreamlike setting. The clocks appear distorted and fluid, draped over various surfaces like branches or walls. The overall aesthetic is reminiscent of Salvador Dalí's work, with a muted color palette dominated by earthy tones and soft lighting.

Image /page/26/Picture/8 description: A surrealist painting depicts a distorted clock face with melting hands, set against a dark, abstract background. The clock appears to be held within a shadowy, human-like form, suggesting a theme of time distortion or the subjective experience of time.

Ice Cream Trifle Trifle Whistle Whistle Well Clock Post-Generation Prior-Generation Post-Generation Prior-Generation Post-Generation Prior-Generation Post-Generation Prior-Generation

Image /page/26/Picture/10 description: The image displays a grid of generated images, categorized by subject matter: Otter, Sunglasses, Teapot, and Grand Piano. Each category features two columns: "Post-Generation" and "Prior-Generation." The Otter category shows four images of otters in water, with the post-generation images appearing more detailed. The Sunglasses category presents abstract images of sunglasses, with varying colors and styles. The Teapot category displays generated images of teapots, some with intricate designs. The Grand Piano category shows abstract representations of grand pianos, with some images featuring distorted or fragmented forms. The overall presentation suggests a comparison between different stages or methods of image generation.

 $\blacksquare$ 

Fig. 7: Visualization results of our proposed method for ImageNet-1K IPC 50.

Image /page/26/Picture/12 description: The image contains the number 27 in a large, bold font.
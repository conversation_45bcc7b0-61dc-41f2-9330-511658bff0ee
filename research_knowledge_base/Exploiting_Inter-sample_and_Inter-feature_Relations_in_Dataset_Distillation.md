# <span id="page-0-1"></span>Exploiting Inter-sample and Inter-feature Relations in Dataset Distillation

Wen<PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><sup>1</sup><sup>\*</sup>, <PERSON><PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><sup>3</sup>, <PERSON><PERSON><PERSON><sup>4</sup>, <PERSON><PERSON><PERSON><sup>5</sup>, <PERSON><sup>1</sup>, <PERSON><sup>1</sup>

<sup>1</sup>State Key Laboratory for Novel Software Technology, Nanjing University, China

<sup>2</sup>Microsoft Corporation, USA  $3$ University of Wollongong, Australia

<sup>4</sup>Systems Engineering Institute, AMS, China <sup>5</sup>School of Systems Engineering, University of Defense Technology, China

## Abstract

*Dataset distillation has emerged as a promising approach in deep learning, enabling efficient training with small synthetic datasets derived from larger real ones. Particularly, distribution matching-based distillation methods attract attention thanks to its effectiveness and low computational cost. However, these methods face two primary limitations: the dispersed feature distribution within the same class in synthetic datasets, reducing class discrimination, and an exclusive focus on mean feature consistency, lacking precision and comprehensiveness. To address these challenges, we introduce two novel constraints: a class centralization constraint and a covariance matching constraint. The class centralization constraint aims to enhance class discrimination by more closely clustering samples within classes. The covariance matching constraint seeks to achieve more accurate feature distribution matching between real and synthetic datasets through local feature covariance matrices, particularly beneficial when sample sizes are much smaller than the number of features. Experiments demonstrate notable improvements with these constraints, yielding performance boosts of up to 6.6% on CIFAR10, 2.9% on SVHN, 2.5% on CIFAR100, and 2.5% on TinyImageNet, compared to the state-of-the-art relevant methods. In addition, our method maintains robust performance in cross-architecture settings, with a maximum performance drop of 1.7% on four architectures. Code is available at* <https://github.com/VincenDen/IID>*.*

## 1. Introduction

Deep learning has evolved rapidly in recent years, achieving remarkable results across various fields [\[10,](#page-8-0) [15,](#page-8-1) [20,](#page-8-2) [23,](#page-8-3) [31,](#page-9-0) [35,](#page-9-1) [36\]](#page-9-2). Dataset distillation [\[33\]](#page-9-3), a process of distilling knowledge from a large real dataset to a much smaller synthetic dataset, has emerged as a key technique for efficient deep learning training. It has also been widely adopted in

<span id="page-0-0"></span>Image /page/0/Figure/10 description: The image displays four scatter plots, each representing data points colored differently. Plots (a) and (c) are labeled "DM IPC=10" and "DM IPC=50" respectively, showing a dense distribution of points with many distinct clusters. Plots (b) and (d) are labeled "Ours IPC=10" and "Ours IPC=50" respectively, and they show sparser distributions with fewer, more separated clusters. The plots are arranged in a 2x2 grid, with the "DM" plots on the left and the "Ours" plots on the right. The x and y axes are labeled "t-SNE Component 1" and "t-SNE" respectively, with numerical scales indicated on the axes of plots (a), (c), and (d).

Figure 1. T-SNE visualisation of features from synthetic dataset obtained by DM [\[39\]](#page-9-4) and our method, using a pre-trained Resnet18 on CIFAR10. Different colors represent different classes. IPC denotes the number of images per class.

areas like neural architecture search [\[6,](#page-8-4) [11,](#page-8-5) [37,](#page-9-5) [39,](#page-9-4) [40\]](#page-9-6), continual learning  $[11, 24, 26]$  $[11, 24, 26]$  $[11, 24, 26]$  $[11, 24, 26]$  $[11, 24, 26]$ , privacy protection  $[3, 9, 42]$  $[3, 9, 42]$  $[3, 9, 42]$  $[3, 9, 42]$  $[3, 9, 42]$ .

Dataset distillation, also known as dataset condensation, initially relied on coreset selection methods [\[12,](#page-8-10) [27,](#page-8-11) [34\]](#page-9-8), which involve selecting representative samples from a dataset. However, these methods have limitations in performance and scalability, especially for large datasets. Pioneering work by Wang *et al*. [\[33\]](#page-9-3) introduces a metalearning based approach for dataset distillation, which uses backpropagation to optimize images directly. Presently, dataset distillation methods are primarily categorized into three types: gradient matching [\[37,](#page-9-5) [40\]](#page-9-6), trajectory matching [\[5\]](#page-8-12), and distribution matching [\[39,](#page-9-4) [41\]](#page-9-9). The first two types, despite their effectiveness, are computationally expensive due to their reliance on second-order gradient optimization. In contrast, distribution matching methods [\[39\]](#page-9-4) like those introduced by Zhao *et al*. [\[41\]](#page-9-9), CAFE [\[32\]](#page-9-10), and DataDAM [\[25\]](#page-8-13) address this challenge by matching feature distributions in the embedding space, thereby reducing computational costs. More details about distribution matching method are provided in Section [3.1.](#page-2-0)

<sup>\*</sup>Corresponding author

<span id="page-1-0"></span>In this paper, we focus on distribution matching (DM) methods due to their impressive performance and computational efficiency. The idea of the distribution matching method is to make the synthetic dataset have the same feature distribution as the real dataset with the same class of samples, so how to carve and match this distribution is the key to improve the performance. We identify two key limitations of the methods and design two constraints to improve them. First, the feature distribution of samples within the same class in synthetic datasets could still be excessively scattered, leading to poor class discrimination in the embedding space. This issue becomes more pronounced with a smaller number of images per class (IPC), as shown in Figs. [1a](#page-0-0) and [1c.](#page-0-0) To counter this, we propose a *class centralization constraint*, designed to promote clustering of class-specific samples, demonstrated in Figs. [1b](#page-0-0) and [1d.](#page-0-0) Second, existing methods inadequately match feature distributions, focusing only on the mean features between real and synthetic datasets. We argue that a comprehensive feature distribution description should include not only means but also covariance matrices, the latter characterises inter-feature relationships. However, in synthetic datasets the number of samples is typically smaller than feature dimensions, accurately estimating covariance matrices is challenging. Our solution is a *covariance matching constraint* that can achieve more precise feature distribution matching between real and synthetic datasets through local feature covariance matrices, even with small sample sizes.

Importantly, both constraints can be seamlessly integrated with existing methods. We have applied them to DM [\[39\]](#page-9-4) and IDM [39], two representative distribution matching-based methods, and carried out experiments on SVHN, CIFAR10, CIFAR100, and Tiny-Imagenet datasets. Our experiments demonstrate a performance gain of up to 6.6% on CIFAR10. In cross-architecture experiments, synthetic dataset obtained by the proposed method is tested on network architectures different from the distillation phase, our method exhibits a minimal performance reduction, with a maximum of only 1.7% across four architectures.

Our main contributions of this work are as follows:

- We propose a simple yet effective *class centralization constraint*, significantly enhancing class discrimination in synthetic dataset, and improving the performance of dataset distillation.
- We develop a novel *covariance matching constraint*, which facilitates more accurate feature distribution matching between real and synthetic datasets even with limited sample sizes, thus effectively augmenting the general mean distribution matching approach.
- We evaluate the proposed two constraints across multiple benchmark datasets, achieving substantial performance improvements over recent baseline methods and surpassing current state-of-the-art techniques.

## 2. Related Works

Coreset selection. Coreset selection, an early approach to dataset distillation, involves selecting representative samples from a dataset and is applied in various contexts [\[1,](#page-8-14) [4,](#page-8-15) [22\]](#page-8-16). Simple methods include random selection, while more sophisticated techniques like Herding [\[34\]](#page-9-8) focuses on class centers, and K-Center [\[12,](#page-8-10) [27\]](#page-8-11) selects multiple centroids. The forgetting [\[30\]](#page-9-11) identifies samples that are easily forgotten during training to select representative ones. However, coreset selection struggles with scalability on large datasets and often exhibits suboptimal performance.

Dataset distillation. Dataset distillation or condensation aims to condense a large dataset into much smaller yet informative one. It finds applications in neural architecture search [\[6,](#page-8-4) [11,](#page-8-5) [37,](#page-9-5) [39,](#page-9-4) [40\]](#page-9-6), continual learning [\[11,](#page-8-5) [24,](#page-8-6) [26\]](#page-8-7), and privacy protection [\[3,](#page-8-8) [9,](#page-8-9) [42\]](#page-9-7), *etc*. The concept was introduced by Wang *et al*. [\[33\]](#page-9-3) using a meta-learning approach. Nguyen *et al*. [\[21\]](#page-8-17) further optimize this method using NTKbased ridge regression for better cross-architecture generalization. Zhao *et al*. [\[37\]](#page-9-5) further improve the distillation performance using an insertable differentiable siamese augmentation. After that, Bohdal *et al*. [\[13\]](#page-8-18) demonstrate that labels could also be distilled. Gradient matching methods proposed by Zhao *et al*. [\[40\]](#page-9-6) and trajectory matching methods by Cazenavette *et al*. [\[5\]](#page-8-12) aim to match gradients and model parameters of synthetic and real datasets, respectively. Recent advances [\[8,](#page-8-19) [18,](#page-8-20) [38\]](#page-9-12) focus on preserving features and corresponding decoders, then restore the features to the original size of the image. This will enhance performance while also introducing additional preprocessing steps for synthetic datasets.

To reduce the high computational cost of the previous methods, Zhao *et al*. [\[39\]](#page-9-4) propose distribution matching (DM), optimizing for maximum mean discrepancy (MMD) between synthetic and real datasets. CAFE [\[32\]](#page-9-10) aligns features by matching them across layers in convolutional neural networks, and IDM [\[41\]](#page-9-9) improves DM with partitioning augmentation and class-aware distribution regularization. DataDAM [\[25\]](#page-8-13) proposes an attention matching framework to dataset distillation by focusing on feature attention.

Although demonstrating promising performance, existing distribution matching methods methods face two primary limitations: the dispersed feature distribution within the same class in synthetic datasets, reducing class discrimination, and an exclusive focus on mean feature consistency, lacking precision and comprehensiveness. This work addresses above limitations by proposing two plug-and-play constraints, which significantly enhance dataset distillation performance by focusing on inter-sample and inter-feature relations, respectively.

<span id="page-2-4"></span>

## 3. Method

In this section, we outline the fundamentals of dataset distillation and basic framework of distribution matching-based methods. We then delve into specifics of our proposed *class centralization constraint* and *covariance matching constraint*. Lastly, we present the entire objective function.

<span id="page-2-0"></span>

### 3.1. Preliminaries

Given a large image dataset  $\mathcal{T} = \{(x_1, y_1), (x_2, y_2), \dots,$  $(x_{|\mathcal{T}|}, y_{|\mathcal{T}|})$ , containing  $|\mathcal{T}|$  images and labels, dataset distillation aims to condense  $T$  to a significantly smaller synthetic dataset  $S = \{(s_1, y_1^s), (s_2, y_2^s), \dots, (s_{|S|}, y_{|S|}^s)\}\$ comprising  $|S|$  images and class labels. The goal is to ensure that a model  $\psi_{\theta}$ s, trained from scratch with S, performs comparably to a model  $\psi_{\theta}$  trained with T. This is achieved by optimizing the following objective function:

$$
\mathcal{S}^* = \underset{\mathcal{S}}{\arg\min} \mathbb{E}_{\boldsymbol{x}\sim P_{\mathcal{T}}} \left\| \ell \left( \psi_{\boldsymbol{\theta}}(\boldsymbol{x}), y \right) - \ell \left( \psi_{\boldsymbol{\theta}}(\boldsymbol{x}), y \right) \right\|, (1)
$$

where  $\ell$  represents the cross-entropy loss since all existing dataset distillation methods focus on classification tasks.

DM [\[39\]](#page-9-4) is a representative distribution matching-based dataset distillation method, aiming to minimize the maximum mean discrepancy (MMD) between the synthetic dataset and real dataset:

<span id="page-2-1"></span>
$$
\mathbb{E}_{\boldsymbol{\theta} \sim P_{\boldsymbol{\theta}}} \left\| \frac{1}{|\mathcal{T}|} \sum_{i=1}^{|\mathcal{T}|} \psi_{\boldsymbol{\theta}}\left(\boldsymbol{x}_i\right) - \frac{1}{|\mathcal{S}|} \sum_{j=1}^{|\mathcal{S}|} \psi_{\boldsymbol{\theta}}\left(\boldsymbol{s}_j\right) \right\|^2, \qquad (2)
$$

where  $P_{\theta}$  denotes the distribution of randomly initialized network parameters.

#### 3.2. Class centralization constraint (inter-sample)

Synthetic datasets obtained by methods like DM [\[39\]](#page-9-4) often exhibit insufficient class discrimination. This issue, particularly evident when the number of samples per class (IPC) is small, is illustrated by the scattered feature distribution and unclear class boundaries in synthetic datasets (Figs. [1a](#page-0-0) and [1c\)](#page-0-0). To address this, we propose a class centralization constraint, which aims to cluster features  $\phi(s)$  extracted from the synthetic dataset within the same class, rather than allowing them to disperse. We formulate the loss to enforce this constraint as:

<span id="page-2-2"></span>
$$
\mathcal{L}_{CC} = \sum_{c}^{C} \left( \sum_{j=1}^{K} \max(0, \exp(\alpha \left\| \psi(s_j^c) - \psi(s^c) \right\|^2) - \beta) \right), \tag{3}
$$

where

$$
\psi(s^c) = \frac{1}{K} \sum_{j=1}^{K} \psi(s_j^c),\tag{4}
$$

 $C$  is the number of categories,  $K$  is the number of samples per class,  $\alpha$  is a scaling factor, and  $\beta$  is the centralization threshold. A smaller  $\beta$  encourages tighter clustering of samples within each class.

<span id="page-2-3"></span>Image /page/2/Figure/15 description: This diagram illustrates a process involving real and synthetic datasets. A Convolutional Neural Network (CNN) processes a batch of the real dataset to produce a real local feature map, which is then used to calculate the real local covariance matrix. Simultaneously, the CNN, with its network parameters not updated (indicated by a snowflake icon), processes the synthetic dataset to generate a synthetic local feature map and its corresponding covariance matrix. A 'Match' step is shown, with bidirectional arrows indicating a comparison or alignment between the real and synthetic covariance matrices, and backward propagation is indicated by red arrows. The diagram also defines B as a batch of the real dataset and K as the IPC of the synthetic dataset, with feature maps having dimensions (d, h x w).

Figure 2. Illustration of the proposed covariance matching constraint. This constraint involves calculating local covariance matrices for corresponding classes in both real and synthetic datasets, followed by matching these matrices.

Since this is a plug-and-play constraint, we keep the original constraint Eq. [\(2\)](#page-2-1) from the baseline, but it's worth noting that the previous methods used ConvNet  $\psi$  in Eq. [\(2\)](#page-2-1) with randomly initialized parameters (DM [\[39\]](#page-9-4)) or pretrained parameters (IDM [\[41\]](#page-9-9)), our approach employs Resnet18 for network  $\psi$  in Eq. [\(3\)](#page-2-2), which is distinct from network  $\psi$  in Eq. [\(2\)](#page-2-1). Despite ResNet's more complex architecture, which typically poses challenges for small datasets, our experiments reveal that Resnet18 more effectively differentiates between class features. Additionally, using different neural networks enhances the crossarchitecture generalization of dataset distillation. As illustrated in Figs. [1b](#page-0-0) and [1d,](#page-0-0) our class centralization constraint results in a more concentrated feature distribution within each class in the synthetic dataset, achieving clear differentiation between classes. The effectiveness of this constraint is further validated in our experimental results.

##### 3.3. Covariance matching constraint (inter-feature)

The essence of distribution matching-based dataset distillation lies in aligning the feature distributions of real and synthetic datasets. While existing methods primarily focus on matching feature means, an effective representation of feature distribution shall also consider the covariance matrix, which captures inter-feature relationships. However, in distilled synthetic datasets, the number of samples in each class is often much smaller than the feature dimensions. This is often known as "small sample problem" and it could lead to less precise covariance matrix estimations.

To address this, we propose the covariance matching constraint for more accurate feature distribution matching, even when the sample size is smaller than the dimensions of features extracted by network  $\psi$ . As shown in Fig. [2,](#page-2-3) for the feature of a single sample, rather than flattening, we reshape it into a tensor of shape  $(d, hw)$ , resulting in

<span id="page-3-2"></span>d hw-dimensional local feature descriptors, expressed as  $X_i \in \mathbb{R}^{d \times hw}$  for real dataset,  $S_i \in \mathbb{R}^{d \times hw}$  for synthetic dataset. In this way, we greatly reduce the feature dimensions and avoid high-dimensional vector space computation, which facilitates a more accurate evaluation of the covariance matrices. We then calculate the local feature covariance matrices  $\Sigma_{\tau} \in \mathbb{R}^{d \times d}$  and  $\Sigma_s \in \mathbb{R}^{d \times d}$  for the real and synthetic datasets, respectively, and compute a matching loss between these two matrices:

$$
\mathcal{L}_{CM} = \sum_{c=1}^{C} \left\| \Sigma_s^c - \Sigma_\tau^c \right\|^2, \tag{5}
$$

where

$$
\Sigma_s^c = \frac{1}{K} \sum_{i=1}^K (S_i^c - \bar{S}^c) (S_i^c - \bar{S}^c)^\top,
$$
 (6)

$$
\Sigma_{\tau}^{c} = \frac{1}{B} \sum_{i=1}^{B} (X_{i}^{c} - \bar{X^{c}})(X_{i}^{c} - \bar{X^{c}})^{\top}.
$$
 (7)

Here,  $K$  is the number of samples per class in the synthetic dataset  $S$ ,  $B$  is the batch size per class of the real dataset  $\mathcal{T}, \bar{S}^c \in \mathbb{R}^{d \times hw}$  and  $\bar{X}^c \in \mathbb{R}^{d \times hw}$  are the feature means of class c for the synthetic and real datasets, respectively.

#### 3.4. The objective function

Our proposed constraints are designed as plug-and-play, making them adaptable to various distribution matchingbased methods. For instance, when employing DM [\[39\]](#page-9-4) as the baseline method, the overall objective function can be expressed as:

<span id="page-3-0"></span>
$$
\mathcal{L} = \mathcal{L}_{DM} + \lambda_{CC} \mathcal{L}_{CC} + \lambda_{CM} \mathcal{L}_{CM} . \tag{8}
$$

Similarly, when using IDM [\[41\]](#page-9-9) as the baseline, the objective function will be defined as:

<span id="page-3-1"></span>
$$
\mathcal{L} = \mathcal{L}_{IDM} + \lambda_{CC} \mathcal{L}_{CC} + \lambda_{CM} \mathcal{L}_{CM} . \tag{9}
$$

In above formulas,  $\lambda_{CC}$  and  $\lambda_{CM}$  are the weighting parameters, and  $\mathcal{L}_{DM}$  and  $\mathcal{L}_{IDM}$  represent the loss functions in DM [\[39\]](#page-9-4) and IDM [\[41\]](#page-9-9), respectively.

## 4. Experiments

### 4.1. Experimental setup

Datasets. We evaluate our method on several benchmark datasets for dataset distillation: SVHN [\[28\]](#page-9-13), CIFAR10 and CIFAR100 [\[16\]](#page-8-21), as well as the larger TinyImageNet [\[17\]](#page-8-22). SVHN contains over 600,000 digit images of house numbers around the world. CIFAR10 and CIFAR100 feature 10 and 100 classes respectively, each with 600 images per class, and an image resolution of  $32 \times 32$ . For larger datasets, we performed on TinyImageNet [\[17\]](#page-8-22), which contains 200 classes with 600 images in each class, which have an image resolution of 64×64.

Network architectures. For  $\mathcal{L}_{DM}$ ,  $\mathcal{L}_{IDM}$  in Eq. [\(8\)](#page-3-0) and Eq. [\(9\)](#page-3-1), we utilize the ConvNet with the same architecture as in DM [\[39\]](#page-9-4) and IDM [\[41\]](#page-9-9) to extract features. This architecture comprises 3 blocks, each with a  $3 \times 3$ convolutional layer, instance normalization, ReLU activation, and a  $3 \times 3$  average pooling with a stride of 2. For our class centralization constraint  $\mathcal{L}_{CC}$ , we employ a pretrained ResNet18 [\[14\]](#page-8-23), trained for 30 epochs on each corresponding dataset. The architecture of ResNet18 includes 4 layers with 2 blocks per layer, and convolutional layer, instance normalization, and ReLU activation. ConvNet, AlexNet, VGG11, and ResNet18 are used in the crossarchitecture generalization experiments, and we follow the architectural setup of DM [\[39\]](#page-9-4).

Evaluation. We adhere to the evaluation protocol of previous works [\[18,](#page-8-20) [33,](#page-9-3) [37,](#page-9-5) [39,](#page-9-4) [40\]](#page-9-6). On various datasets, we synthesize distilled datasets with IPC =  $1, 10, 50$ , then train a randomly initialized model from scratch using these datasets. The training configurations follow the prior works [\[18,](#page-8-20) [33,](#page-9-3) [37,](#page-9-5) [39,](#page-9-4) [40\]](#page-9-6). We compute the Top-1 accuracy on the test set of the original real dataset, repeating each experiment five times to calculate the mean value.

Implementation details. Our method is implemented based on DM [\[41\]](#page-9-9) and IDM [\[41\]](#page-9-9), adhering to their baseline hyperparameter settings. We follow the DSA [\[37\]](#page-9-5) augmentation method used in previous works. Synthetic datasets are learned using SGD with a learning rate of 1, and a batch size of 5000 for IDM experiments with IPC=1 on CI-FAR10/100, and 256 in other scenarios. We set  $\lambda_{CC}$  as 0.05, and  $\lambda_{CM}$  as 0.01 for IPC = 1, and 10, while for IPC = 50,  $\lambda_{CC}$  is set as 0.003 and  $\lambda_{CM}$  as 0.01. All experiments are conducted using the PyTorch framework on two RTX3090 GPUs, each with 24 GB of memory.

#### 4.2. Comparison with state-of-the-art methods

Competitive methods. We compare our method with both classic coreset selection methods, including Random, Herding [\[34\]](#page-9-8), K-Center [\[27\]](#page-8-11), and Forgetting [\[30\]](#page-9-11), and state-of-the-art dataset distillation techniques including DD [\[33\]](#page-9-3), LD [\[2\]](#page-8-24), DC [\[40\]](#page-9-6), DSA [\[37\]](#page-9-5), KIP [\[21\]](#page-8-17), CAFE [\[32\]](#page-9-10), DCC [\[19\]](#page-8-25), DM [\[39\]](#page-9-4), IDM [\[41\]](#page-9-9), DataDAM [\[25\]](#page-8-13). For DM [\[39\]](#page-9-4) and IDM [\[41\]](#page-9-9), we report results reproduced using their official codes, while results for other methods are quoted from their respective original papers.

Performance comparison. Tabs. [1](#page-4-0) and [2](#page-4-1) show the results of our method in comparison with previous methods across various datasets. The experimental results demonstrate significant improvements over the baselines. Specifically, our method surpasses DM by 2.9% and 2.7% on SVHN at IPC=10 and 50, respectively; 6.6% and 2.9% on CIFAR10 at IPC=10 and 50; and 2.5% and 2.0% on TinyImageNet at IPC=10 and 50. When compared with IDM, our method shows an improvement of 1.9%, 2.6%

<span id="page-4-2"></span><span id="page-4-0"></span>Table 1. Comparative analysis of dataset distillation methods. Ratio (%): the proportion of condensed images relative to the number of entire training set. Whole Dataset: the accuracy of training on the entire original dataset. The best results in each column are highlighted for clarity.

| Method               | Venue                    |                          | <b>SVHN</b>    |                          | CIFAR10                  |                |                | CIFAR100                 |                |                |
|----------------------|--------------------------|--------------------------|----------------|--------------------------|--------------------------|----------------|----------------|--------------------------|----------------|----------------|
| <b>IPC</b>           |                          | 1                        | 10             | 50                       | 1                        | 10             | 50             | 1                        | 10             | 50             |
| Ratio $(\%)$         |                          | 0.0014                   | 0.14           | 0.7                      | 0.02                     | 0.2            | 1              | 0.2                      | 2              | 10             |
| Random               | Classic                  | $14.6 \pm 1.6$           | $35.1 \pm 4.1$ | $70.9 \pm 0.9$           | $14.4 \pm 2.0$           | $26.0 \pm 1.2$ | $43.4 \pm 1.0$ | $4.2 \pm 0.3$            | $14.6 \pm 0.5$ | $30.0 \pm 0.4$ |
| Herding $[34]$       | Classic                  | $20.9 \pm 1.3$           | $50.5 \pm 3.3$ | $72.6 \pm 0.8$           | $21.5 \pm 1.2$           | $31.6 \pm 0.7$ | $40.4 \pm 0.6$ | $8.4 \pm 0.3$            | $17.3 \pm 0.3$ | $33.7 \pm 0.5$ |
| K-Center $[27]$      | Classic                  | $21.0 \pm 1.5$           | $14.0 \pm 1.3$ | $20.1 \pm 1.4$           | $21.5 \pm 1.3$           | $14.7 \pm 0.9$ | $27.0 \pm 1.4$ | $8.4 \pm 0.3$            | $17.3 \pm 0.2$ | $30.5 \pm 0.3$ |
| Forgetting [30]      | Classic                  | $12.1 \pm 5.6$           | $16.8 \pm 1.2$ | $27.2 \pm 1.5$           | $13.5 \pm 1.2$           | $23.3 \pm 1.0$ | $23.3 \pm 1.1$ | $4.5 \pm 0.3$            | $15.1 \pm 0.2$ | $30.5 \pm 0.4$ |
| DD [33]              | Arxiv'18                 |                          |                |                          | $\overline{\phantom{a}}$ | $36.8 \pm 1.2$ |                |                          |                | -              |
| LD[2]                | NeurIPS'20               |                          |                | $\overline{\phantom{a}}$ | $25.7 \pm 0.7$           | $38.3 \pm 0.4$ | $42.5 \pm 0.4$ | $11.5 \pm 0.4$           |                | -              |
| DC[40]               | ICLR'21                  | $31.2 \pm 1.4$           | $76.1 \pm 0.6$ | $82.3 \pm 0.3$           | $28.3 \pm 0.5$           | $44.9 \pm 0.5$ | $53.9 \pm 0.5$ | $12.8 \pm 0.3$           | $25.2 \pm 0.3$ | -              |
| <b>DSA</b> [37]      | $ICML$ 21                | $27.5 \pm 1.4$           | $79.2 \pm 0.5$ | $84.4 \pm 0.4$           | $28.8 \pm 0.7$           | $52.1 \pm 0.5$ | $60.6 \pm 0.5$ | $13.9 \pm 0.3$           | $32.3 \pm 0.3$ | $42.8 \pm 0.4$ |
| $KIP$ [21]           | NeurIPS'21               | $62.4 \pm 0.3$           | $81.1 \pm 0.5$ | $84.3 \pm 0.1$           | $29.8 \pm 0.2$           | $46.1 \pm 0.3$ | $53.2 \pm 0.4$ | $12.0 \pm 0.2$           | $29.0 \pm 0.2$ | -              |
| <b>CAFE</b> [32]     | CVPR'22                  | $42.9 \pm 3.3$           | $77.9 \pm 0.6$ | $82.3 \pm 0.3$           | $30.3 \pm 1.1$           | $46.3 \pm 0.6$ | $55.5 \pm 0.6$ | $12.9 \pm 0.3$           | $27.8 \pm 0.3$ | $37.9 \pm 0.3$ |
| <b>DCC</b> [19]      | ICML'22                  | $34.3 \pm 1.6$           | $76.2 \pm 0.8$ | $83.3 \pm 0.2$           | $34.0 \pm 0.7$           | $54.4 \pm 0.5$ | $64.2 \pm 0.4$ | $14.6 \pm 0.3$           | $33.5 \pm 0.3$ | $39.3 \pm 0.4$ |
| DataDAM [25]         | ICCV23                   |                          |                | $\overline{\phantom{a}}$ | $32.0 \pm 1.2$           | $54.2 \pm 0.8$ | $67.0 \pm 0.4$ | $14.5 \pm 0.5$           | $34.8 \pm 0.5$ | $49.4 \pm 0.3$ |
| DM [39]              | WACV'23                  | $21.6 \pm 0.8$           | $72.8 \pm 0.3$ | $82.6 \pm 0.5$           | $26.4 \pm 0.8$           | $48.5 \pm 0.6$ | $62.2 \pm 0.5$ | $11.4 \pm 0.3$           | $29.7 \pm 0.3$ | $43.0 \pm 0.4$ |
| DM+Ours              | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $75.7 \pm 0.3$ | $85.3 \pm 0.2$           | $\overline{\phantom{a}}$ | $55.1 \pm 0.1$ | $65.1 \pm 0.2$ | $\overline{\phantom{a}}$ | $32.2 \pm 0.5$ | $43.6 \pm 0.3$ |
| IDM $[41]$           | CVPR'23                  | $65.3 \pm 0.3$           | $81.0 \pm 0.1$ | $84.1 \pm 0.1$           | $45.2 \pm 0.5$           | $57.3 \pm 0.3$ | $67.2 \pm 0.1$ | $23.1 \pm 0.2$           | $44.7 \pm 0.1$ | 49.9 $\pm$ 0.2 |
| <b>IDM+Ours</b>      | $\overline{\phantom{a}}$ | $66.3 \pm 0.1$           | 82.1 $\pm$ 0.3 | $85.1 \pm 0.5$           | 47.1 $\pm$ 0.1           | $59.9 \pm 0.2$ | $69.0 \pm 0.3$ | $24.6 \pm 0.1$           | 45.7 $\pm 0.4$ | $51.3 \pm 0.4$ |
| <b>Whole Dataset</b> |                          |                          | $95.4 \pm 0.2$ |                          |                          | $84.8 \pm 0.1$ |                |                          | $56.2 \pm 0.3$ |                |

and 1.8% on CIFAR10 at IPC=1, 10, and 50, respectively, and 1.5%, 1.0%, and 1.4% on CIFAR100 at IPC=10, 50. Our IDM-based method also outperforms all other state-ofthe-art methods, achieving accuracies of 59.9% and 69.0% on CIFAR10, and 45.7% and 51.3% on CIFAR100, both at IPC=10 and 50. On the larger TinyImageNet dataset, our method also achieves state-of-the-art performance with an accuracy of 23.3% at IPC=10, surpassing the second best method by 1.3%. For the simpler SVHN dataset with IPC=10, using DM as the baseline proved most effective, suggesting that simpler approaches may be more beneficial for less complex datasets.

Notably, our method, when used with DM as the baseline, does not provide IPC=1 results due to its requirement for a sample size greater than 1. However, IDM, with its ability to preserve more samples at IPC=1 through partitioning and expansion augmentation, is compatible with our approach.

#### 4.3. Cross-architecture generalization

Cross-architecture generalization is a crucial metric for evaluating dataset distillation, particularly because it is hard to predict the neural network architectures that will be used in a real application. Significant performance drop upon changing model architectures is undesirable. We evaluate the synthetic dataset generated by our method on CIFAR10 across four different architectures, comparing it with previ-

<span id="page-4-1"></span>Table 2. Comparative analysis of dataset distillation methods on higher resolution datasets. The best results in each column are highlighted for clarity.

| Method               | Venue   | TinyImageNet   |                |                |
|----------------------|---------|----------------|----------------|----------------|
|                      |         | 1              | 10             | 50             |
| IPC                  |         | 1              | 10             | 50             |
| Ratio (%)            |         | 0.2            | 2              | 10             |
| Random               | Classic | 1.4 $\pm$ 0.1  | 5.0 $\pm$ 0.2  | 15.0 $\pm$ 0.4 |
| Herding [34]         | Classic | 2.8 $\pm$ 0.2  | 6.3 $\pm$ 0.2  | 16.7 $\pm$ 0.3 |
| K-Center [27]        | Classic | 1.6 $\pm$ 0.2  | 5.1 $\pm$ 0.1  | 15.0 $\pm$ 0.3 |
| Forgetting [30]      | Classic | 1.6 $\pm$ 0.2  | 5.1 $\pm$ 0.3  | 15.0 $\pm$ 0.1 |
| DataDAM [25]         | ICCV'23 | 8.3 $\pm$ 0.4  | 18.7 $\pm$ 0.3 | 28.7 $\pm$ 0.3 |
| DM [39]              | WACV'23 | 3.9 $\pm$ 0.2  | 12.9 $\pm$ 0.4 | 24.1 $\pm$ 0.3 |
| DM+Ours              | -       | -              | 15.4 $\pm$ 0.2 | 26.1 $\pm$ 0.1 |
| IDM [41]             | CVPR'23 | 9.8 $\pm$ 0.2  | 21.9 $\pm$ 0.2 | 26.2 $\pm$ 0.3 |
| <b>IDM+Ours</b>      | -       | 10.0 $\pm$ 0.1 | 23.3 $\pm$ 0.1 | 27.5 $\pm$ 0.3 |
| <b>Whole Dataset</b> |         |                | 37.6 $\pm$ 0.6 |                |

ous methods. Tabs. [3](#page-5-0) and [4](#page-5-1) present the cross-architecture performance for IPC=10 and 50. Our experiments included widely-used models such as ConvNet, AlexNet [\[7\]](#page-8-26), VGG11 [\[29\]](#page-9-14), and ResNet18 [\[14\]](#page-8-23), with architectural details following previous work. Each experiment is repeated five times to determine the mean value.

For IPC=50 as shown in Tab. [4,](#page-5-1) our method's per-

<span id="page-5-2"></span><span id="page-5-0"></span>Table 3. Cross-architecture testing on CIFAR10 with IPC=10.

|                 | ConvNet                          | AlexNet                           | VGG11                            | ResNet18                         |
|-----------------|----------------------------------|-----------------------------------|----------------------------------|----------------------------------|
| DM [39]         | $48.9 pm 0.6$                   | $38.8 pm 0.5$                    | $42.1 pm 0.4$                   | $41.2 pm 1.1$                   |
| <b>DSA</b> [37] | $52.1 pm 0.7$                   | $35.9 pm 2.6$                    | $43.2 pm 0.5$                   | $42.8 pm 1.0$                   |
| KIP [21]        | $47.6 pm 0.9$                   | $24.4 pm 3.9$                    | $42.1 pm 0.4$                   | $36.8 pm 1.0$                   |
| IDM [41]        | $53.0 pm 0.3$                   | $44.6 pm 0.8$                    | $47.8 pm 1.1$                   | $44.6 pm 0.4$                   |
| <b>IDM+Ours</b> | <b><math>59.9 pm 0.2</math></b> | <b><math>56.8 pm 0.02</math></b> | <b><math>58.0 pm 0.5</math></b> | <b><math>56.9 pm 0.5</math></b> |

<span id="page-5-1"></span>Table 4. Cross-architecture testing on CIFAR10 with IPC=50.

| ConvNet         | AlexNet                          | VGG11                            | ResNet18                         |                                  |
|-----------------|----------------------------------|----------------------------------|----------------------------------|----------------------------------|
| DC [40]         | $53.9 \pm 0.5$                   | $28.8 \pm 0.7$                   | $38.8 \pm 1.1$                   | $20.9 \pm 1.0$                   |
| CAFE [32]       | $62.3 \pm 0.4$                   | $43.2 \pm 0.4$                   | $48.8 \pm 0.5$                   | $43.3 \pm 0.7$                   |
| DSA [37]        | $60.6 \pm 0.5$                   | $53.7 \pm 0.6$                   | $51.4 \pm 1.0$                   | $47.8 \pm 0.9$                   |
| DM [39]         | $63.0 \pm 0.4$                   | $60.1 \pm 0.5$                   | $57.4 \pm 0.8$                   | $52.9 \pm 0.4$                   |
| KIP [21]        | $56.9 \pm 0.4$                   | $53.2 \pm 1.6$                   | $53.2 \pm 0.5$                   | $47.6 \pm 0.8$                   |
| DataDAM [25]    | $67.0 \pm 0.4$                   | $63.9 \pm 0.9$                   | $64.8 \pm 0.5$                   | $60.2 \pm 0.7$                   |
| <b>IDM+Ours</b> | <b><math>69.0 \pm 0.2</math></b> | <b><math>67.3 \pm 0.2</math></b> | <b><math>67.3 \pm 0.3</math></b> | <b><math>68.3 \pm 0.2</math></b> |

formance on ResNet18 shows an 8% improvement over DataDAM [\[25\]](#page-8-13). More notably, across ConvNet, AlexNet, VGG11, and ResNet18, we record accuracies of 69.0%, 67.3%, 67.3%, and 68.3%, respectively, maintaining a performance drop of less than 1.7% across these architectures. This is particularly significant considering that previous methods often experienced substantial performance drops on ResNet18. For IPC=10 as shown in Tab. [3,](#page-5-0) our method performs significantly better than others, with a performance loss not exceeding 3.1% on AlexNet, VGG11, and ResNet18, while KIP [\[21\]](#page-8-17) loss 10.8% from ConvNet to Resnet18. The better cross-architecture generalization performance achieved by our method allows us to utilize more diverse model architectures in the dataset distillation phase perhaps facilitating the synthesis of datasets that are more generalizable across different model architectures.

Note that some existing methods only appear in one of the two tables because they only reported the result for either IPC =  $10$  or IPC =  $50$ .

#### 4.4. Ablation study

Analysis of cluster constraint threshold. The  $\beta$  in Eq. [\(3\)](#page-2-2) serves as the centralization threshold, where a larger  $\beta$  indicates sample features are farther from the class feature center, while a smaller  $\beta$  brings them closer. We conducted experiments with varying  $\beta$  values, ranging from 0.0 to 2.0. To isolate the effects of  $\beta$ , we keep  $\alpha$  in Eq. [\(3\)](#page-2-2) constant and do not include our proposed covariance matching constraint. The results on CIFAR10 with IPC=10 are visual-ized in Fig. [3,](#page-6-0) and the performance corresponding to each  $\beta$ value is detailed in Tab. [5.](#page-6-1)

Our visualizations demonstrate that feature distribution per class becomes more dispersed with larger  $\beta$  values and more concentrated with smaller  $\beta$ . Performance evaluation experiments reveal that optimal performance is achieved when  $\beta$  is below 0.7. This suggests that smaller  $\beta$  values, which lead to higher class discrimination, are advantageous for dataset distillation, enhancing performance without significantly compromising generalization.

Effectiveness of each component. To validate the effectiveness of the proposed two components, we perform ablation experiments. We test the performance impact of implementing the class centralization constraint and covariance matching constraint both separately and combined. Tab. [6](#page-6-2) reveals that each constraint individually enhances DM's performance by 3.7% and 3.1% on CIFAR10, and 1.4% and 1.5% on CIFAR100, respectively. Notably, the greatest performance improvement, 6.6% on CIFAR10 and 2.5% on CIFAR100, is achieved when both components are used concurrently.

Evaluation of weighting parameter  $\lambda$ . We perform a sensitivity analysis focusing on the weighting parameters in Eq. [\(8\)](#page-3-0). The results, shown in Fig. [4,](#page-6-3) indicate that larger values of  $\lambda_{CC}$  and  $\lambda_{CM}$  correspond to a more pronounced impact on the optimization objective. Varying  $\lambda_{CC}$  within a range of 0.01 to 0.09 resulted in a performance difference of 1.47%, suggesting moderate sensitivity. Similarly, altering  $\lambda_{CM}$  between 0.005 and 0.015 lead to a 1.1% performance variation, indicating comparable sensitivity levels.

Number of iterations required for convergence. Contrary to previous methods which typically require around 20,000 training iterations, our method achieves convergence with significantly fewer iterations. As demonstrated in Fig. [5,](#page-6-4) less than 2,000 iterations are needed for IPC=10 to attain peak performance. For IPC=50, optimal performance is essentially reached at about 3,000 iterations. In addition to this, we find that accuracy grows quickly in the early stages of training, and if the training time requirement is high while the performance requirement is relatively low, we can consider using early stopping training methods.

Different compression ratios. Previous dataset distillation studies have primarily focused on improving performance at very small compression ratios, such as 0.2% (IPC=10 on CIFAR10) and  $1\%$  (IPC=50 on CIFAR10). However, an equally important consideration is determining the necessary compression ratio to retain performance comparable to the full dataset. To explore this, we conduct experiments at higher IPC values, including IPC=100, 200, 400, 600, 800, and 1000, and compared our method's performance with previous methods, as shown in Fig. [6](#page-6-5) and Tab. [7,](#page-7-0) with most results sourced from DC-BENCH [\[6\]](#page-8-4).

Our method significantly outperforms others like DC [\[40\]](#page-9-6), DSA [\[37\]](#page-9-5), and DM [\[39\]](#page-9-4) at these varied IPC levels. Notably, at IPC=1000 (a 20% compression ratio), our

<span id="page-6-1"></span><span id="page-6-0"></span>Image /page/6/Figure/0 description: The image displays a table and a series of scatter plots illustrating the impact of a parameter \"β\". The table shows the accuracy (Acc (%)) for different values of \"β\". The values for \"β\" range from 0.0 to 2.0, with corresponding accuracies: 52.2±0.2 for β=0.0, 52.2±0.1 for β=0.1, 52.2±0.1 for β=0.7, 51.5±0.3 for β=0.9, 51.1±0.2 for β=1.0, 49.9±0.2 for β=1.1, 49.2±0.2 for β=1.2, 49.1±0.1 for β=1.3, 48.9±0.3 for β=1.5, and 48.9±0.1 for β=2.0. Below the table, ten scatter plots labeled (a) through (j) correspond to different \"β\" values: (a) β = 0.0, (b) β = 0.1, (c) β = 0.5, (d) β = 0.7, (e) β = 0.9, (f) β = 1.1, (g) β = 1.2, (h) β = 1.3, (i) β = 1.5, and (j) β = 2.0. The scatter plots show points of various colors, with the density and clustering of points changing as \"β\" increases. Initially, for lower \"β\" values, the points are sparsely distributed. As \"β\" increases, the points begin to form clusters, becoming more dense and organized in the plots for β = 1.3, 1.5, and 2.0.

Table 5. Comparison of performance for different values of  $\beta$  on CIFAR10 with IPC=10.

Figure 3. Visualization of different  $\beta$  on CIFAR10 with IPC=10.

<span id="page-6-2"></span>Table 6. Ablation study on the proposed two components. Results are averaged over 5 runs.

| Method                 | CIFAR10                      | CIFAR100                     |
|------------------------|------------------------------|------------------------------|
| Baseline               | 48.5 $±$ 0.2                 | 29.7 $±$ 0.1                 |
| + Class centralization | 52.2 $±$ 0.3                 | 31.1 $±$ 0.2                 |
| + Covariance matching  | 51.6 $±$ 0.1                 | 31.2 $±$ 0.1                 |
| + Both                 | <b>55.1<math>±</math>0.1</b> | <b>32.2<math>±</math>0.3</b> |

<span id="page-6-3"></span>Image /page/6/Figure/5 description: The image contains two line graphs side-by-side, labeled (a) and (b). Both graphs plot Accuracy (%) on the y-axis against a parameter on the x-axis. Graph (a), titled "Ablation of \(\lambda\_{CC}\)", shows a green line. The x-axis ranges from 0.004 to 0.016, with tick marks every 0.002. The y-axis ranges from 53.0 to 56.0, with tick marks every 0.5. The green line starts at approximately 53.7% at \(\lambda\_{CC}\) = 0.004, rises to about 54.7% at 0.006, peaks at 55.1% at 0.010, drops to 53.7% at 0.012, and ends at 53.6% at 0.015. Graph (b), titled "Ablation of \(\lambda\_{CM}\)", shows a yellow line. The x-axis and y-axis scales are the same as in graph (a). The yellow line starts at approximately 54.1% at \(\lambda\_{CM}\) = 0.004, rises to 54.3% at 0.006, then to 54.5% at 0.008, peaks at 55.1% at 0.010, drops to 53.9% at 0.012, and rises again to 54.4% at 0.015.

Figure 4. Ablation study of the weighting parameter on CIFAR10.

method achieves 93.3% of the full dataset's performance. However, it's observed that as the compression ratio increases, the performance gap between all methods, including classical and even random methods, narrows. This suggests that current dataset distillation approaches are more effective at smaller compression ratios. In future research, it would be valuable to explore dataset distillation methods suitable for larger compression ratios, aiming for an optimal balance between performance and data reduction.

<span id="page-6-4"></span>Image /page/6/Figure/8 description: The image displays two line graphs side-by-side, both plotting accuracy against iterations. Graph (a), labeled "IPC=10", shows accuracy on the y-axis ranging from 30.0% to 55.0% and iterations on the x-axis from 0 to 3000. The green line starts at approximately 30.0% and rapidly increases to about 52.5% by 500 iterations, then plateaus and fluctuates slightly between 52.5% and 53.5% until 3000 iterations. Graph (b), labeled "IPC=50", shows accuracy on the y-axis ranging from 47.0% to 67.0% and iterations on the x-axis from 0 to 6000. The cyan line starts at approximately 49.5% and shows a steeper initial rise, reaching about 64.5% by 2000 iterations. It then continues to increase more slowly, reaching a peak of around 66.5% between 4000 and 5000 iterations, and stays relatively flat thereafter.

<span id="page-6-5"></span>Figure 5. Accuracy progression over iteration on CIFAR10.

Image /page/6/Figure/10 description: This is a line graph showing the accuracy of different methods as the number of images per class increases. The x-axis represents the number of images per class, ranging from 0 to 1000. The y-axis represents the accuracy in percentage, ranging from 20% to 80%. The graph displays seven lines, each representing a different method: 'Whole dataset' (cyan dashed line), 'Random' (tan line), 'K\_Center' (dark blue line), 'DC' (yellow line), 'DSA' (green line), 'DM' (purple line), and 'Ours' (red line with square markers). The 'Whole dataset' line is consistently at the top, indicating the highest accuracy. The 'Ours' method shows a significant increase in accuracy with the number of images per class, starting from around 25% at 0 images and reaching approximately 80% at 1000 images. Other methods also show an increasing trend in accuracy, but generally perform lower than 'Ours' and 'Whole dataset'.

Figure 6. Performance comparison at different compression ratios on CIFAR10.

#### 4.5. Applications

Continual learning. The goal of continual learning is to develop models capable of adapting to new tasks while minimizing the forgetting of previously learned tasks. Dataset distillation, with its ability to maintain performance with a minimal number of samples, has potential applications

<span id="page-7-3"></span><span id="page-7-0"></span>Table 7. Accuracy (%) with different compression ratios on CI-FAR10.

| IPC  | Random | K-Center | DC    | DSA   | DM    | <b>Ours</b> |
|------|--------|----------|-------|-------|-------|-------------|
| 1    | 15.40  | 25.16    | 29.34 | 27.76 | 26.45 | 47.11       |
| 10   | 31.00  | 41.49    | 50.99 | 52.96 | 47.64 | 59.92       |
| 50   | 50.55  | 56.00    | 56.81 | 60.28 | 61.99 | 69.01       |
| 100  | 57.89  | 62.18    | 65.70 | 66.18 | 65.12 | 70.46       |
| 200  | 64.70  | 67.25    | 68.41 | 69.49 | 69.15 | 74.15       |
| 400  | 70.28  | 71.88    | 70.86 | 72.22 | 72.61 | 76.35       |
| 600  | 74.00  | 75.98    | 72.84 | 74.99 | 76.07 | 78.38       |
| 800  | 75.52  | 76.94    | 74.80 | 76.76 | 77.41 | 79.21       |
| 1000 | 78.38  | 79.47    | 76.62 | 78.68 | 78.83 | 80.31       |

<span id="page-7-1"></span>Image /page/7/Figure/2 description: This image contains two line graphs side-by-side, labeled (a) Step=5 and (b) Step=10. Both graphs plot Accuracy (%) on the y-axis against Step on the x-axis. The x-axis for graph (a) ranges from 20 to 100 in increments of 20, while the x-axis for graph (b) ranges from 10 to 100 in increments of 10. The y-axis for both graphs ranges from 25/30 to 60/70 respectively, with tick marks every 5 units. Five lines are plotted in each graph, representing different methods: Random (dotted orange), Herding (dashed blue), DSA (dashed red), DM (dashed green), and Ours (dashed purple). Each line shows a general downward trend as the Step increases. Graph (a) shows that at Step=20, the accuracy for 'Ours' is approximately 60%, 'DM' is approximately 57%, 'DSA' is approximately 55%, 'Herding' is approximately 49%, and 'Random' is approximately 45%. By Step=100, the accuracies are approximately 44% for 'Ours', 40% for 'DM', 35% for 'DSA', 30% for 'Herding', and 27% for 'Random'. Graph (b) shows that at Step=10, the accuracy for 'Ours' is approximately 68%, 'DM' is approximately 64%, 'DSA' is approximately 62%, 'Herding' is approximately 54%, and 'Random' is approximately 45%. By Step=100, the accuracies are approximately 42% for 'Ours', 35% for 'DM', 30% for 'DSA', 26% for 'Herding', and 24% for 'Random'. Shaded regions around each line indicate variability.

Figure 7. Continual learning on CIFAR100.

in continual learning. Following the setup from the prior work [\[39\]](#page-9-4), we limit the buffer size to 20 images per category. Experiments are conducted in both 5-step (20 classes per task) and 10-step (10 classes per task) scenarios. For each step, three networks are randomly initialized, trained on the synthetic dataset from the buffer, and evaluated on a test set of seen classes to determine the mean performance. Five class orders are randomly generated for these experiments, and the mean and variance are calculated. The results, depicted in Fig. [7,](#page-7-1) show that our method notably outperforms DM [\[39\]](#page-9-4), DSA [\[37\]](#page-9-5), and Herding [\[34\]](#page-9-8), with the performance gap widening as the number of steps increases.

### 4.6. Visualization

The synthetic datasets, initialized randomly from real datasets, are visualized in Fig. [8.](#page-7-2) These include synthesized datasets from SVHN, CIFAR10/100, and Tiny-ImageNet at IPC=10. For datasets with smaller resolutions like SVHN and CIFAR10/100, the majority of high-frequency information is preserved, making them more recognizable to the human eye. In contrast, for higher resolution datasets such as Tiny-ImageNet, the synthetic dataset's visualization appears much different from the real dataset, becoming less distinguishable visually. Differential visualization results appeared between low-resolution data sets and highresolution data sets, suggesting that in the future we may need to design different dataset distillation methods for datasets of different scales.

<span id="page-7-2"></span>Image /page/7/Picture/7 description: The image displays four grids of images, each labeled with a dataset name. Grid (a) is labeled "SVHN" and contains images of handwritten digits from 0 to 9. Grid (b) is labeled "CIFAR10" and shows a variety of objects and scenes. Grid (c) is labeled "CIFAR100 (Partial)" and displays a diverse collection of images, likely from the CIFAR100 dataset. Grid (d) is labeled "TinyImageNet (Partial)" and presents a wide range of images, suggesting it is a subset of the Tiny ImageNet dataset. All grids are composed of smaller square images arranged in a matrix format.

Figure 8. Visualization of synthetic images.

## 5. Conclusion

Previous distribution matching-based methods for dataset distillation face two primary challenges: insufficient class discrimination and incomplete distribution matching. To address these, we introduced the class centralization constraint and the covariance matching constraint, focusing on improving both inter-sample and inter-feature relations. The class centralization constraint improves class discrimination by clustering samples closer to their class centers, while the covariance matching constraint aligns interfeature relationships between real and synthetic datasets. Our method, tested across various resolutions, has demonstrated significant superiority over previous methods and excelled in cross-architecture scenarios. Additionally, we explored larger compression ratios to determine the necessary compression ratio for maintaining performance comparable to the full dataset.

Acknowledgements. This work is supported in part by the National Natural Science Foundation of China (62106100, 62192783, 62276128, 62106282), Science and Technology Innovation 2030 New Generation Artificial Intelligence Major Project (2021ZD0113303), Jiangsu Natural Science Foundation (BK20221441), Young Elite Scientists Sponsorship Program by CAST (2023QNRC001), the Collaborative Innovation Center of Novel Software Technology and Industrialization, Beijing Nova Program (20220484139).

# References

- <span id="page-8-14"></span>[1] Rahaf Aljundi, Min Lin, Baptiste Goujaud, and Yoshua Bengio. Gradient based sample selection for online continual learning. *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 32, 2019. [2](#page-1-0)
- <span id="page-8-24"></span>[2] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS Workshop)*, 2020. [4,](#page-3-2) [5](#page-4-2)
- <span id="page-8-8"></span>[3] Nicholas Carlini, Vitaly Feldman, and Milad Nasr. No free lunch in" privacy for free: How does dataset condensation help privacy". *Proceedings of the International Conference on Machine Learning (ICML)*, 2022. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-8-15"></span>[4] Francisco M Castro, Manuel J Marín-Jiménez, Nicolás Guil, Cordelia Schmid, and Karteek Alahari. End-to-end incremental learning. In *Proceedings of the European conference on computer vision (ECCV)*, pages 233–248, 2018. [2](#page-1-0)
- <span id="page-8-12"></span>[5] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 4750–4759, 2022. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-8-4"></span>[6] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dcbench: Dataset condensation benchmark. *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 35:810–822, 2022. [1,](#page-0-1) [2,](#page-1-0) [6](#page-5-2)
- <span id="page-8-26"></span>[7] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR)*, pages 248–255, 2009. [5](#page-4-2)
- <span id="page-8-19"></span>[8] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 35:34391–34404, 2022. [2](#page-1-0)
- <span id="page-8-9"></span>[9] Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *Proceedings of the International Conference on Machine Learning (ICML)*, pages 5378–5396, 2022. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-8-0"></span>[10] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *Proceedings of the International Conference on Learning Representations (ICLR)*, 2020. [1](#page-0-1)
- <span id="page-8-5"></span>[11] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 3749–3758, 2023. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-8-10"></span>[12] Reza Zanjirani Farahani and Masoud Hekmatfar. *Facility location: concepts, models, algorithms and case studies*. Springer Science & Business Media, 2009. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-8-18"></span>[13] Jianping Gou, Baosheng Yu, Stephen J Maybank, and Dacheng Tao. Knowledge distillation: A survey. *International Journal of Computer Vision*, 129:1789–1819, 2021. [2](#page-1-0)

- <span id="page-8-23"></span>[14] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR)*, pages 770–778, 2016. [4,](#page-3-2) [5](#page-4-2)
- <span id="page-8-1"></span>[15] Gao Huang, Zhuang Liu, Laurens Van Der Maaten, and Kilian Q Weinberger. Densely connected convolutional networks. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR)*, pages 4700–4708, 2017. [1](#page-0-1)
- <span id="page-8-21"></span>[16] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [4](#page-3-2)
- <span id="page-8-22"></span>[17] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [4](#page-3-2)
- <span id="page-8-20"></span>[18] Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*, 2022. [2,](#page-1-0) [4](#page-3-2)
- <span id="page-8-25"></span>[19] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *Proceedings of the International Conference on Machine Learning (ICML)*, pages 12352–12364, 2022. [4,](#page-3-2) [5](#page-4-2)
- <span id="page-8-2"></span>[20] Ze Liu, Yutong Lin, Yue Cao, Han Hu, Yixuan Wei, Zheng Zhang, Stephen Lin, and Baining Guo. Swin transformer: Hierarchical vision transformer using shifted windows. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR)*, pages 10012–10022, 2021. [1](#page-0-1)
- <span id="page-8-17"></span>[21] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 34:5186–5198, 2021. [2,](#page-1-0) [4,](#page-3-2) [5,](#page-4-2) [6](#page-5-2)
- <span id="page-8-16"></span>[22] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H Lampert. icarl: Incremental classifier and representation learning. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR)*, pages 2001–2010, 2017. [2](#page-1-0)
- <span id="page-8-3"></span>[23] Joseph Redmon, Santosh Divvala, Ross Girshick, and Ali Farhadi. You only look once: Unified, real-time object detection. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR)*, pages 779–788, 2016. [1](#page-0-1)
- <span id="page-8-6"></span>[24] Andrea Rosasco, Antonio Carta, Andrea Cossu, Vincenzo Lomonaco, and Davide Bacciu. Distilled replay: Overcoming forgetting through synthetic samples. In *International Workshop on Continual Semi-Supervised Learning*, pages 104–117, 2021. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-8-13"></span>[25] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR)*, pages 17097–17107, 2023. [1,](#page-0-1) [2,](#page-1-0) [4,](#page-3-2) [5,](#page-4-2) [6](#page-5-2)
- <span id="page-8-7"></span>[26] Mattia Sangermano, Antonio Carta, Andrea Cossu, and Davide Bacciu. Sample condensation in online continual learning. In *International Joint Conference on Neural Networks (IJCNN)*, pages 01–08, 2022. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-8-11"></span>[27] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *Proceedings*

*of the International Conference on Learning Representations (ICLR)*, 2018. [1,](#page-0-1) [2,](#page-1-0) [4,](#page-3-2) [5](#page-4-2)

- <span id="page-9-13"></span>[28] Pierre Sermanet, Soumith Chintala, and Yann LeCun. Convolutional neural networks applied to house numbers digit classification. In *Proceedings of the 21st international conference on pattern recognition (ICPR2012)*, pages 3288– 3291, 2012. [4](#page-3-2)
- <span id="page-9-14"></span>[29] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR)*, 2014. [5](#page-4-2)
- <span id="page-9-11"></span>[30] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *Proceedings of the International Conference on Learning Representations (ICLR)*, 2018. [2,](#page-1-0) [4,](#page-3-2) [5](#page-4-2)
- <span id="page-9-0"></span>[31] Yi-Hsuan Tsai, Wei-Chih Hung, Samuel Schulter, Kihyuk Sohn, Ming-Hsuan Yang, and Manmohan Chandraker. Learning to adapt structured output space for semantic segmentation. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (CVPR)*, pages 7472–7481, 2018. [1](#page-0-1)
- <span id="page-9-10"></span>[32] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 12196–12205, 2022. [1,](#page-0-1) [2,](#page-1-0) [4,](#page-3-2) [5,](#page-4-2) [6](#page-5-2)
- <span id="page-9-3"></span>[33] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2,](#page-1-0) [4,](#page-3-2) [5](#page-4-2)
- <span id="page-9-8"></span>[34] Max Welling. Herding dynamical weights to learn. In *Proceedings of the International Conference on Machine Learning (ICML)*, pages 1121–1128, 2009. [1,](#page-0-1) [2,](#page-1-0) [4,](#page-3-2) [5,](#page-4-2) [8](#page-7-3)
- <span id="page-9-1"></span>[35] Yang Yang, Da-Wei Zhou, De-Chuan Zhan, Hui Xiong, and Yuan Jiang. Adaptive deep models for incremental learning: Considering capacity scalability and sustainability. In *Proceedings of the 25th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining*, pages 74–82, Anchorage, AK, 2019. [1](#page-0-1)
- <span id="page-9-2"></span>[36] Yang Yang, Zhen-Qiang Sun, Hengshu Zhu, Yanjie Fu, Yuanchun Zhou, Hui Xiong, and Jian Yang. Learning adaptive embedding considering incremental class. *IEEE Trans. Knowl. Data Eng.*, 35(3):2736–2749, 2023. [1](#page-0-1)
- <span id="page-9-5"></span>[37] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *Proceedings of the International Conference on Machine Learning (ICML)*, pages 12674–12685, 2021. [1,](#page-0-1) [2,](#page-1-0) [4,](#page-3-2) [5,](#page-4-2) [6,](#page-5-2) [8](#page-7-3)
- <span id="page-9-12"></span>[38] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS Workshop)*, 2022.  $\overline{2}$  $\overline{2}$  $\overline{2}$
- <span id="page-9-4"></span>[39] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision (CVPR)*, pages 6514–6523, 2023. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-4) [4,](#page-3-2) [5,](#page-4-2) [6,](#page-5-2) [8](#page-7-3)

- <span id="page-9-6"></span>[40] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. 2021. [1,](#page-0-1) [2,](#page-1-0) [4,](#page-3-2) [5,](#page-4-2) [6](#page-5-2)
- <span id="page-9-9"></span>[41] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 7856–7865, 2023. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-4) [4,](#page-3-2) [5,](#page-4-2) [6](#page-5-2)
- <span id="page-9-7"></span>[42] Tianhang Zheng and Baochun Li. Differentially private dataset condensation. 2022. [1,](#page-0-1) [2](#page-1-0)
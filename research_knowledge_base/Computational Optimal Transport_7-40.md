# **1**

## **Introduction**

The shortest path principle guides most decisions in life and sciences: When a commodity, a person or a single bit of information is available at a given point and needs to be sent at a target point, one should favor using the least possible effort. This is typically reached by moving an item along a straight line when in the plane or along geodesic curves in more involved metric spaces. The theory of optimal transport generalizes that intuition in the case where, instead of moving only one item at a time, one is concerned with the problem of moving simultaneously several items (or a continuous distribution thereof) from one configuration onto another. As schoolteachers might attest, planning the transportation of a group of individuals, with the constraint that they reach a given target configuration upon arrival, is substantially more involved than carrying it out for a single individual. Indeed, thinking in terms of groups or distributions requires a more advanced mathematical formalism which was first hinted at in the seminal work of <PERSON><PERSON> [1781]. Yet, no matter how complicated that formalism might look at first sight, that problem has deep and concrete connections with our daily life. Transportation, be it of people, commodities or information, very rarely involves moving only one item. All major economic problems, in logistics, production planning or network routing, involve moving distributions, and that thread appears in all of the seminal references on optimal transport. Indeed <PERSON><PERSON><PERSON><PERSON> [1930], <PERSON> [1941] and <PERSON><PERSON><PERSON><PERSON> [1942] were all guided by practical concerns. It was only a few years later, mostly after the 1980s, that mathematicians discovered, thanks to the works of <PERSON><PERSON><PERSON> [1991] and others, that this theory provided a fertile ground for research, with deep connections to convexity, partial differential equations and statistics. At the turn of the millenium, researchers in computer, imaging and more generally data sciences understood that op-

timal transport theory provided very powerful tools to study distributions in a different and more abstract context, that of comparing distributions readily available to them under the form of bags-of-features or descriptors.

Several reference books have been written on optimal transport, including the two recent monographs by Villani (2003, 2009), those by Rachev and Rüschendorf (1998a, 1998b) and more recently that by Santambrogio [2015]. As exemplified by these books, the more formal and abstract concepts in that theory deserve in and by themselves several hundred pages. Now that optimal transport has gradually established itself as an applied tool (for instance, in economics, as put forward recently by Galichon [2016]), we have tried to balance that rich literature with a computational viewpoint, centered on applications to data science, notably imaging sciences and machine learning. We follow in that sense the motivation of the recent review by Kolouri et al. [2017] but try to cover more ground. Ultimately, our goal is to present an overview of the main theoretical insights that support the practical effectiveness of OT and spend more time explaining how to turn these insights into fast computational schemes. The main body of Chapters 2, 3, 4, 9, and 10 is devoted solely to the study of the geometry induced by optimal transport in the space of probability vectors or discrete histograms. Targeting more advanced readers, we also give in the same chapters, in light gray boxes, a more general mathematical exposition of optimal transport tailored for discrete measures. Discrete measures are defined by their probability weights, but also by the location at which these weights are defined. These locations are usually taken in a continuous metric space, giving a second important degree of freedom to model random phenomena. Lastly, the third and most technical layer of exposition is indicated in dark gray boxes and deals with arbitrary measures that need not be discrete, and which can have in particular a density w.r.t. a base measure. This is traditionally the default setting for most classic textbooks on OT theory, but one that plays a less important role in general for practical applications. Chapters 5 to 8 deal with the interplay between continuous and discrete measures and are thus targeting a more mathematically inclined audience.

The field of computational optimal transport is at the time of this writing still an extremely active one. There are therefore a wide variety of topics that we have not touched upon in this survey. Let us cite in no particular order the subjects of distributionally robust optimization [Shafieezadeh Abadeh et al., 2015, Esfahani and Kuhn, 2018, Lee and Raginsky, 2018, GAO et al., 2018], in which parameter estimation is carried out by minimizing the worst posssible empirical risk of any data measure taken within a certain Wasserstein distance of the input data; convergence of the Langevin Monte Carlo sampling algorithm in the Wasserstein geometry [Dalalyan and Karagulyan, 2017, Dalalyan, 2017, Bernton, 2018]; other numerical methods to solve OT with a squared Euclidian cost in low-dimensional settings using the Monge-Ampère equation [Froese and Oberman, 2011, Benamou et al., 2014, Sulman et al., 2011] which are only briefly mentioned in Remark 2.25.

### **Notation**

- $[n]$ : set of integers  $\{1, \ldots, n\}$ .
- $\mathbb{1}_{n,m}$ : matrix of  $\mathbb{R}^{n \times m}$  with all entries identically set to 1.  $\mathbb{1}_n$ : vector of ones.
- $\mathbb{I}_n$ : identity matrix of size  $n \times n$ .
- For  $u \in \mathbb{R}^n$ , diag(*u*) is the  $n \times n$  matrix with diagonal *u* and zero otherwise.
- $\Sigma_n$ : probability simplex with *n* bins, namely the set of probability vectors in  $\mathbb{R}^n_+$ .
- (**a**, **b**): histograms in the simplices  $\Sigma_n \times \Sigma_m$ .
- $(\alpha, \beta)$ : measures, defined on spaces  $(\mathcal{X}, \mathcal{Y})$ .
- $\bullet$   $\frac{d\alpha}{d\beta}$  $\frac{d\alpha}{d\beta}$ : relative density of a measure *α* with respect to *β*.
- $\rho_{\alpha} = \frac{d\alpha}{dx}$  $\frac{d\alpha}{dx}$ : density of a measure  $\alpha$  with respect to Lebesgue measure.
- $(\alpha = \sum_i \mathbf{a}_i \delta_{x_i}, \beta = \sum_j \mathbf{b}_j \delta_{y_j})$ : discrete measures supported on  $x_1, \dots, x_n \in \mathcal{X}$  and  $y_1, \ldots, y_m \in \mathcal{Y}$ .
- $c(x, y)$ : ground cost, with associated pairwise cost matrix  $\mathbf{C}_{i,j} = (c(x_i, y_j))_{i,j}$ evaluated on the support of  $\alpha$ ,  $\beta$ .
- *π*: coupling measure between  $\alpha$  and  $\beta$ , namely such that for any  $A \subset \mathcal{X}, \pi(A \times$  $\mathcal{Y}$  = *α*(*A*), and for any subset *B*  $\subset \mathcal{Y}, \pi(\mathcal{X} \times B) = \beta(B)$ . For discrete measures  $\pi = \sum_{i,j} \mathbf{P}_{i,j} \delta_{(x_i,y_j)}.$
- $\mathcal{U}(\alpha, \beta)$ : set of coupling measures, for discrete measures **U**( $\mathbf{a}, \mathbf{b}$ ).
- R(*c*): set of admissible dual potentials; for discrete measures **R**(**C**).
- $T: \mathcal{X} \to \mathcal{Y}$ : Monge map, typically such that  $T_{\sharp}\alpha = \beta$ .
- $(\alpha_t)_{t=0}^1$ : dynamic measures, with  $\alpha_{t=0} = \alpha_0$  and  $\alpha_{t=1} = \alpha_1$ .
- *v*: speed for Benamou–Brenier formulations;  $J = \alpha v$ : momentum.
- $\bullet$   $(f, g)$ : dual potentials, for discrete measures  $(f, g)$  are dual variables.
- $(\mathbf{u}, \mathbf{v}) \stackrel{\text{def.}}{=} (e^{\mathbf{f}/\varepsilon}, e^{\mathbf{g}/\varepsilon})$ : Sinkhorn scalings.
- $\mathbf{K} \stackrel{\text{def.}}{=} e^{-\mathbf{C}/\varepsilon}$ : Gibbs kernel for Sinkhorn.

- *s*: flow for  $W_1$ -like problem (optimization under divergence constraints).
- L<sub>C</sub>( $\mathbf{a}, \mathbf{b}$ ) and  $\mathcal{L}_c(\alpha, \beta)$ : value of the optimization problem associated to the OT with cost **C** (histograms) and *c* (arbitrary measures).
- $W_p(a, b)$  and  $W_p(\alpha, \beta)$ : *p*-Wasserstein distance associated to ground distance matrix **D** (histograms) and distance *d* (arbitrary measures).
- $\lambda \in \Sigma_S$ : weight vector used to compute the barycenters of *S* measures.
- $\langle \cdot, \cdot \rangle$ : for the usual Euclidean dot-product between vectors; for two matrices of the same size *A* and *B*,  $\langle A, B \rangle \stackrel{\text{def.}}{=} \text{tr}(A^{\top}B)$  is the Frobenius dot-product.
- $f \oplus g(x, y) \stackrel{\text{def.}}{=} f(x) + g(y)$ , for two functions  $f : \mathcal{X} \to \mathbb{R}, g : \mathcal{Y} \to \mathbb{R}$ , defines  $f \oplus q : \mathcal{X} \times \mathcal{Y} \rightarrow \mathbb{R}$ .
- $\mathbf{f} \oplus \mathbf{g} \stackrel{\text{def.}}{=} \mathbf{f} \mathbb{1}_m^{\top} + \mathbb{1}_n \mathbf{g}^{\top} \in \mathbb{R}^{n \times m}$  for two vectors  $\mathbf{f} \in \mathbb{R}^n$ ,  $\mathbf{g} \in \mathbb{R}^m$ .
- $\alpha \otimes \beta$  is the product measure on  $\mathcal{X} \times \mathcal{Y}$ , *i.e.*  $\int_{\mathcal{X} \times \mathcal{Y}} g(x, y) d(\alpha \otimes \beta)(x, y) \stackrel{\text{def.}}{=}$  $\int_{\mathcal{X}\times\mathcal{Y}} g(x,y) \mathrm{d}\alpha(x) \mathrm{d}\beta(y)$ .
- $\bullet$  **a** ⊗ **b**  $\stackrel{\text{def.}}{=}$  **ab**<sup>T</sup> ∈  $\mathbb{R}^{n \times m}$ .
- $\mathbf{u} \odot \mathbf{v} = (\mathbf{u}_i \mathbf{v}_i) \in \mathbb{R}^n$  for  $(\mathbf{u}, \mathbf{v}) \in (\mathbb{R}^n)^2$ .

## **2**

### **Theoretical Foundations**

This chapter describes the basics of optimal transport, introducing first the related notions of optimal matchings and couplings between probability vectors (**a***,* **b**), generalizing gradually this computation to transport between discrete measures  $(\alpha, \beta)$ , to cover lastly the general setting of arbitrary measures. At first reading, these last nuances may be omitted and the reader can only focus on computations between probability vectors, namely histograms, which is the only requisite to implement algorithms detailed in Chapters 3 and 4. More experienced readers will reach a better understanding of the problem by considering the formulation that applies to arbitrary measures, and will be able to apply it for more advanced problems (*e.g.* in order to move positions of clouds of points, or in a statistical setting where points are sampled from continuous densities).

#### **2.1 Histograms and Measures**

We will use interchangeably the terms histogram and probability vector for any element  $\mathbf{a} \in \Sigma_n$  that belongs to the probability simplex

$$
\Sigma_n \stackrel{\text{\tiny def.}}{=} \left\{ \mathbf{a} \in \mathbb{R}^n_+ \; : \; \sum_{i=1}^n \mathbf{a}_i = 1 \right\}.
$$

A large part of this review focuses exclusively on the study of the geometry induced by optimal transport on the simplex.

**Remark 2.1** (Discrete measures)**.** A discrete measure with weights **a** and locations  $x_1, \ldots, x_n \in \mathcal{X}$  reads

$$
\alpha = \sum_{i=1}^{n} \mathbf{a}_i \delta_{x_i},\tag{2.1}
$$

where  $\delta_x$  is the Dirac at position *x*, intuitively a unit of mass which is infinitely concentrated at location *x*. Such a measure describes a probability measure if, additionally,  $\mathbf{a} \in \Sigma_n$  and more generally a positive measure if all the elements of vector **a** are nonnegative. To avoid degeneracy issues where locations with no mass are accounted for, we will assume when considering discrete measures that all the elements of **a** are positive.

**Remark 2.2** (General measures)**.** A convenient feature of OT is that it can deal with measures that are either or both discrete and continuous within the same framework. To do so, one relies on the set of Radon measures  $\mathcal{M}(\mathcal{X})$  on the space  $\mathcal{X}$ . The formal definition of that set requires that  $\mathcal{X}$  is equipped with a distance, usually denoted *d*, because one can access a measure only by "testing" (integrating) it against continuous functions, denoted  $f \in C(X)$ .

Integration of  $f \in \mathcal{C}(\mathcal{X})$  against a discrete measure  $\alpha$  computes a sum

$$
\int_{\mathcal{X}} f(x) \mathrm{d}\alpha(x) = \sum_{i=1}^{n} \mathbf{a}_i f(x_i).
$$

More general measures, for instance on  $\mathcal{X} = \mathbb{R}^d$  (where  $d \in \mathbb{N}^*$  is the dimension), can have a density  $d\alpha(x) = \rho_\alpha(x)dx$  w.r.t. the Lebesgue measure, often denoted  $\rho_{\alpha} = \frac{d\alpha}{dx}$  $\frac{d\alpha}{dx}$ , which means that

$$
\forall h \in \mathcal{C}(\mathbb{R}^d), \quad \int_{\mathbb{R}^d} h(x) \mathrm{d}\alpha(x) = \int_{\mathbb{R}^d} h(x) \rho_\alpha(x) \mathrm{d}x.
$$

An arbitrary measure  $\alpha \in \mathcal{M}(\mathcal{X})$  (which need not have a density nor be a sum of Diracs) is defined by the fact that it can be integrated against any continuous function  $f \in C(\mathcal{X})$  and obtain  $\int_{\mathcal{X}} f(x) d\alpha(x) \in \mathbb{R}$ . If  $\mathcal{X}$  is not compact, one should also impose that *f* has compact support or at least has 0 limit at infinity. Measures are thus in some sense "less regular" than functions but more regular than distributions (which are dual to smooth functions). For instance, the derivative of a Dirac is not a measure. We denote  $\mathcal{M}_+(\mathcal{X})$  the set of all positive measures on X. The set of probability measures is denoted  $\mathcal{M}^1_+(\mathcal{X})$ , which means that any  $\alpha \in \mathcal{M}^1_+(\mathcal{X})$ is positive, and that  $\alpha(\mathcal{X}) = \int_{\mathcal{X}} d\alpha = 1$ . Figure 2.1 offers a visualization of the different classes of measures, beyond histograms, considered in this work.

Image /page/6/Figure/1 description: This image illustrates the transition from discrete data to density representations in different dimensions. On the left, labeled 'Discrete d = 1', there are two horizontal lines with vertical bars representing discrete data points. The top line has red bars of varying heights, and the bottom line has blue bars of varying heights. An arrow points to the right, indicating a transformation. Next, labeled 'Discrete d = 2', is a scatter plot showing red dots clustered in the upper region and blue dots, some larger than others, clustered in the lower region. Another arrow points to the right. Following this, labeled 'Density d = 1', is a purple curve on a horizontal axis, representing a probability density function with two peaks. Finally, labeled 'Density d = 2', is a set of concentric, elliptical contour lines in purple, indicating a two-dimensional probability density function.

**Figure 2.1:** Schematic display of discrete distributions  $\alpha = \sum_{i=1}^{n} \mathbf{a}_i \delta_{x_i}$  (red corresponds to empirical uniform distribution  $\mathbf{a}_i = 1/n$ , and blue to arbitrary distributions) and densities  $d\alpha(x) = \rho_\alpha(x)dx$  (in purple), in both one and two dimensions. Discrete distributions in one-dimension are displayed as stem plots (with length equal to **a***i*) and in two dimensions using point clouds (in which case their radius might be equal to  $a_i$  or, for a more visually accurate representation, their area).

#### **2.2 Assignment and Monge Problem**

Given a cost matrix  $(\mathbf{C}_{i,j})_{i\in[\![n]\!],j\in[\![m]\!]}$ , assuming  $n=m$ , the optimal assignment problem seeks for a bijection  $\sigma$  in the set Perm $(n)$  of permutations of *n* elements solving

$$
\min_{\sigma \in \text{Perm}(n)} \frac{1}{n} \sum_{i=1}^{n} \mathbf{C}_{i, \sigma(i)}.
$$
\n(2.2)

One could naively evaluate the cost function above using all permutations in the set Perm $(n)$ . However, that set has size *n*!, which is gigantic even for small *n*. Consider, for instance, that such a set has more than  $10^{100}$  elements [Dantzig, 1983] when *n* is as small as 70. That problem can therefore be solved only if there exist efficient algorithms to optimize that cost function over the set of permutations, which is the subject of §3.7.

**Remark 2.3** (Uniqueness)**.** Note that the optimal assignment problem may have several optimal solutions. Suppose, for instance, that  $n = m = 2$  and that the matrix **C** is the pairwise distance matrix between the four corners of a 2-D square of side length 1, as represented in the left plot of Figure 2.2. In that case only two assignments exist, and they are both optimal.

**Remark 2.4** (Monge problem between discrete measures)**.** For discrete measures

$$
\alpha = \sum_{i=1}^{n} \mathbf{a}_i \delta_{x_i} \quad \text{and} \quad \beta = \sum_{j=1}^{m} \mathbf{b}_j \delta_{y_j}, \tag{2.3}
$$

the Monge problem [1781] seeks a map that associates to each point  $x_i$  a single point  $y_j$  and which must push the mass of  $\alpha$  toward the mass of  $\beta$ , namely, such a

Image /page/7/Figure/1 description: The image displays two diagrams illustrating relationships between variables represented by colored circles and arrows. The left diagram shows a diamond shape with blue circles labeled x1 and x2, and red circles labeled y1 and y2. Solid arrows point from x1 to y1 and from x2 to y2. Dashed arrows point from x1 to y2 and from x2 to y1. The right diagram consists of three separate clusters of circles and arrows. The top cluster shows a blue circle labeled x1 pointing to a larger red circle labeled y2. A smaller blue circle labeled x2 also points to y2. The middle cluster shows a blue circle labeled x3 pointing to a red circle labeled y3. The bottom cluster shows a large red circle labeled y1 with multiple blue circles labeled x4, x5, x6, and x7 pointing towards it.

**Figure 2.2:** Left: blue dots from measure  $\alpha$  and red dots from measure  $\beta$  are pairwise equidistant. Hence, either matching  $\sigma = (1, 2)$  (full line) or  $\sigma = (2, 1)$  (dotted line) is optimal. Right: a Monge map can associate the blue measure  $\alpha$  to the red measure  $\beta$ . The weights  $\alpha_i$  are displayed proportionally to the area of the disk marked at each location. The mapping here is such that  $T(x_1) = T(x_2) = y_2$ ,  $T(x_3) = y_3$ , whereas for  $4 \leq i \leq 7$  we have  $T(x_i) = y_1$ .

map  $T: \{x_1, \ldots, x_n\} \to \{y_1, \ldots, y_m\}$  must verify that

$$
\forall j \in [\![m]\!], \quad \mathbf{b}_j = \sum_{i: T(x_i) = y_j} \mathbf{a}_i,\tag{2.4}
$$

which we write in compact form as  $T_{\sharp}\alpha = \beta$ . Because all the elements of **b** are positive, that map is necessarily surjective. This map should minimize some transportation cost, which is parameterized by a function  $c(x, y)$  defined for points  $(x, y) \in \mathcal{X} \times \mathcal{Y}$ 

$$
\min_{T} \left\{ \sum_{i} c(x_i, T(x_i)) \; : \; T_{\sharp} \alpha = \beta \right\}.
$$
\n(2.5)

Such a map between discrete points can be of course encoded, assuming all *x*'s and *y*'s are distinct, using indices  $\sigma : \llbracket n \rrbracket \to \llbracket m \rrbracket$  so that  $j = \sigma(i)$ , and the mass conservation is written as

$$
\sum_{i\in \sigma^{-1}(j)} \mathbf{a}_i = \mathbf{b}_j,
$$

where the inverse  $\sigma^{-1}(j)$  is to be understood as the preimage set of *j*. In the special case when  $n = m$  and all weights are uniform, that is,  $\mathbf{a}_i = \mathbf{b}_j = 1/n$ , then the mass conservation constraint implies that *T* is a bijection, such that  $T(x_i) = y_{\sigma(i)}$ , and the Monge problem is equivalent to the optimal matching problem (2.2), where the cost matrix is

$$
\mathbf{C}_{i,j} \stackrel{\text{def.}}{=} c(x_i, y_j).
$$

When  $n \neq m$ , note that, optimality aside, Monge maps may not even exist between a discrete measure to another. This happens when their weight vectors are not

### 2.2. Assignment and Monge Problem

compatible, which is always the case when the target measure has more points than the source measure,  $n \leq m$ . For instance, the right plot in Figure 2.2 shows an (optimal) Monge map between  $\alpha$  and  $\beta$ , but there is no Monge map from  $\beta$  to *α*.

**Remark 2.5** (Push-forward operator). For a continuous map  $T: \mathcal{X} \to \mathcal{Y}$ , we define its corresponding push-forward operator  $T_{\sharp}: \mathcal{M}(\mathcal{X}) \to \mathcal{M}(\mathcal{Y})$ . For discrete measures (2.1), the push-forward operation consists simply in moving the positions of all the points in the support of the measure

$$
T_\sharp \alpha \stackrel{\scriptscriptstyle\rm def.}{=} \sum_i {\bf a}_i \delta_{T(x_i)}.
$$

For more general measures, for instance, for those with a density, the notion of push-forward plays a fundamental role to describe the spatial modification (or transport) of a probability measure. The formal definition reads as follows.

**Definition 2.1** (Push-forward). For  $T : \mathcal{X} \to \mathcal{Y}$ , the push-forward measure  $\beta =$  $T_{\sharp}\alpha \in \mathcal{M}(\mathcal{Y})$  of some  $\alpha \in \mathcal{M}(\mathcal{X})$  satisfies

$$
\forall h \in \mathcal{C}(\mathcal{Y}), \quad \int_{\mathcal{Y}} h(y) \mathrm{d}\beta(y) = \int_{\mathcal{X}} h(T(x)) \mathrm{d}\alpha(x). \tag{2.6}
$$

Equivalently, for any measurable set  $B \subset \mathcal{Y}$ , one has

$$
\beta(B) = \alpha(\{x \in \mathcal{X} : T(x) \in B\}) = \alpha(T^{-1}(B)).
$$
\n(2.7)

Note that  $T_{\sharp}$  preserves positivity and total mass, so that if  $\alpha \in M^1_+(\mathcal{X})$  then  $T_{\sharp}\alpha \in M^1_+(\mathcal{Y}).$ 

Intuitively, a measurable map  $T : \mathcal{X} \to \mathcal{Y}$  can be interpreted as a function moving a single point from a measurable space to another.  $T_{\sharp}$  is an extension of *T* that can move an entire probability measure on  $X$  toward a new probability measure on  $\mathcal{Y}$ . The operator  $T_{\sharp}$  *pushes forward* each elementary mass of a measure  $\alpha$  on  $\mathcal X$  by applying the map  $T$  to obtain then an elementary mass in  $\mathcal Y$ . Note that a push-forward operator  $T_{\sharp}: \mathcal{M}^1_+(\mathcal{X}) \to \mathcal{M}^1_+(\mathcal{Y})$  is *linear* in the sense that for two measures  $\alpha_1, \alpha_2$  on  $\mathcal{X}, T_\sharp(\alpha_1 + \alpha_2) = T_\sharp \alpha_1 + T_\sharp \alpha_2$ .

**Remark 2.6** (Push-forward for multivariate densities)**.** Explicitly doing the change of variables in formula (2.6) for measures with densities  $(\rho_{\alpha}, \rho_{\beta})$  on  $\mathbb{R}^d$  (assuming *T* is smooth and bijective) shows that a push-forward acts on densities linearly as a change of variables in the integration formula. Indeed, one has

$$
\rho_{\alpha}(x) = |\det(T'(x))|\rho_{\beta}(T(x)), \qquad (2.8)
$$

where  $T'(x) \in \mathbb{R}^{d \times d}$  is the Jacobian matrix of *T* (the matrix formed by taking the gradient of each coordinate of *T*). This implies

$$
|\det(T'(x))| = \frac{\rho_{\alpha}(x)}{\rho_{\beta}(T(x))}.
$$

**Remark 2.7** (Monge problem between arbitrary measures). The Monge problem (2.5) can be extended to the case where two arbitrary probability measures  $(\alpha, \beta)$ , supported on two spaces  $(\mathcal{X}, \mathcal{Y})$  can be linked through a map  $T : \mathcal{X} \to \mathcal{Y}$ that minimizes

$$
\min_{T} \left\{ \int_{\mathcal{X}} c(x, T(x)) \mathrm{d}\alpha(x) : T_{\sharp}\alpha = \beta \right\}.
$$
 (2.9)

The constraint  $T_{\sharp} \alpha = \beta$  means that *T* pushes forward the mass of  $\alpha$  to  $\beta$ , using the push-forward operator defined in Remark 2.5

**Remark 2.8** (Push-forward vs. pull-back). The push-forward  $T_{\sharp}$  of measures should not be confused with the pull-back of functions  $T^{\sharp}: \mathcal{C}(\mathcal{Y}) \to \mathcal{C}(\mathcal{X})$  which corresponds to "warping" between functions, defined as the linear map which to  $q \in \mathcal{C}(\mathcal{Y})$ associates  $T^{\sharp}g = g \circ T$ . Push-forward and pull-back are actually adjoint to one another, in the sense that

$$
\forall (\alpha, g) \in \mathcal{M}(\mathcal{X}) \times \mathcal{C}(\mathcal{Y}), \quad \int_{\mathcal{Y}} g d(T_{\sharp}\alpha) = \int_{\mathcal{X}} (T^{\sharp}g) d\alpha.
$$

Note that even if  $(\alpha, \beta)$  have densities  $(\rho_{\alpha}, \rho_{\beta})$  with respect to a fixed measure (*e.g.*) Lebesgue on  $\mathbb{R}^d$ ),  $T_{\sharp}\alpha$  does not have  $T^{\sharp}\rho_{\beta}$  as density, because of the presence of the Jacobian in (2.8). This explains why OT should be used with caution to perform image registration, because it does not operate as an image warping method. Figure 2.3 illustrates the distinction between these push-forward and pull-back operators.

**Remark 2.9** (Measures and random variables)**.** Radon measures can also be viewed as representing the distributions of random variables. A random variable  $X$  on  $\mathcal X$ is actually a map  $X : \Omega \to \mathcal{X}$  from some abstract (often unspecified) probability space  $(\Omega, \mathbb{P})$ , and its distribution  $\alpha$  is the Radon measure  $\alpha \in \mathcal{M}^1_+(\mathcal{X})$  such that  $P(X \in A) = \alpha(A) = \int_A d\alpha(x)$ . Equivalently, it is the push-forward of  $P$  by *X*,  $\alpha = X_{\sharp} \mathbb{P}$ . Applying another push-forward  $\beta = T_{\sharp} \alpha$  for  $T : \mathcal{X} \to \mathcal{Y}$ , following (2.6),

Image /page/10/Figure/1 description: The image displays two diagrams illustrating concepts in mathematics. The left diagram, labeled "Push-forward of measures," shows a transformation T mapping red dots to blue dots. Below this, the measure \alpha, represented as a sum of Dirac deltas \sum\_i \delta\_{x\_i}, is shown to be transformed by T to T\_\#\alpha, which is defined as \sum\_i \delta\_{T(x\_i)}. The right diagram, labeled "Pull-back of functions," illustrates the concept of a pull-back. It shows a function g, represented by nested blue contours, and a function T that maps red contours to blue contours. The pull-back of g by T is denoted as T^\# g, and it is defined as g \circ T.

**Figure 2.3:** Comparison of the push-forward operator  $T_f$ , which can take as an input any measure, and the pull-back operator  $T^{\sharp}$ , which operates on functions, notaly densities.

is equivalent to defining another random variable  $Y = T(X) : \omega \in \Omega \to T(X(\omega)) \in$ *Y*, so that  $\beta$  is the distribution of *Y*. Drawing a random sample *y* from *Y* is thus simply achieved by computing  $y = T(x)$ , where *x* is drawn from *X*.

#### **2.3 Kantorovich Relaxation**

The assignment problem, and its generalization found in the Monge problem laid out in Remark 2.4, is not always relevant to studying discrete measures, such as those found in practical problems. Indeed, because the assignment problem is formulated as a permutation problem, it can only be used to compare *uniform* histograms of the *same* size. A direct generalization to discrete measures with nonuniform weights can be carried out using Monge's formalism of push-forward maps, but that formulation may also be degenerate in the absence of feasible solutions satisfying the mass conservation constraint  $(2.4)$  (see the end of Remark 2.4). Additionally, the assignment problem  $(2.5)$  is combinatorial, and the feasible set for the Monge problem (2.9), despite being continuously parameterized as the set consisting in all push-forward measures that satisfy the mass conservation constraint, is *nonconvex*. Both are therefore difficult to solve when approached in their original formulation.

The key idea of Kantorovich [1942] is to relax the deterministic nature of transportation, namely the fact that a source point *x<sup>i</sup>* can only be assigned to another point or location  $y_{\sigma_i}$  or  $T(x_i)$  only. Kantorovich proposes instead that the mass at any point *x<sup>i</sup>* be potentially dispatched across several locations. Kantorovich moves away from the idea that mass transportation should be *deterministic* to consider instead a *probabilistic* transport, which allows what is commonly known now as *mass splitting* from a source toward several targets. This flexibility is encoded using, in place of a permutation  $\sigma$  or a map *T*, a coupling matrix  $P \in \mathbb{R}_+^{n \times m}$ , where  $P_{i,j}$  describes the amount of mass flowing

from bin *i* toward bin *j*, or from the mass found at  $x_i$  toward  $y_j$  in the formalism of discrete measures (2.3). Admissible couplings admit a far simpler characterization than Monge maps,

$$
\mathbf{U}(\mathbf{a},\mathbf{b}) \stackrel{\text{def.}}{=} \left\{ \mathbf{P} \in \mathbb{R}_+^{n \times m} \; : \; \mathbf{P} \mathbb{1}_m = \mathbf{a} \quad \text{and} \quad \mathbf{P}^{\mathrm{T}} \mathbb{1}_n = \mathbf{b} \right\},\tag{2.10}
$$

where we used the following matrix-vector notation:

$$
\mathbf{P} \mathbb{1}_m = \left(\sum_j \mathbf{P}_{i,j}\right)_i \in \mathbb{R}^n \text{ and } \mathbf{P}^{\mathrm{T}} \mathbb{1}_n = \left(\sum_i \mathbf{P}_{i,j}\right)_j \in \mathbb{R}^m.
$$

The set of matrices  $U(a, b)$  is bounded and defined by  $n + m$  equality constraints, and therefore is a convex polytope (the convex hull of a finite set of matrices) [Brualdi, 2006, §8.1].

Additionally, whereas the Monge formulation (as illustrated in the right plot of Figure 2.2) was intrisically asymmetric, Kantorovich's relaxed formulation is always symmetric, in the sense that a coupling **P** is in  $U(a, b)$  if and only if  $P<sup>T</sup>$  is in  $U(b, a)$ . Kantorovich's optimal transport problem now reads

$$
L_{\mathbf{C}}(\mathbf{a}, \mathbf{b}) \stackrel{\text{def.}}{=} \min_{\mathbf{P} \in \mathbf{U}(\mathbf{a}, \mathbf{b})} \langle \mathbf{C}, \mathbf{P} \rangle \stackrel{\text{def.}}{=} \sum_{i,j} \mathbf{C}_{i,j} \mathbf{P}_{i,j}.
$$
 (2.11)

This is a linear program (see Chapter 3), and as is usually the case with such programs, its optimal solutions are not necessarily unique.

**Remark 2.10** (Mines and factories)**.** The Kantorovich problem finds a very natural illustration in the following resource allocation problem (see also Hitchcock [1941]). Suppose that an operator runs *n* warehouses and *m* factories. Each warehouse contains a valuable raw material that is needed by the factories to run properly. More precisely, each warehouse is indexed with an integer  $i$  and contains  $a_i$  units of the raw material. These raw materials must all be moved to the factories, with a prescribed quantity  $\mathbf{b}_j$ needed at factory *j* to function properly. To transfer resources from a warehouse *i* to a factory *j*, the operator can use a transportation company that will charge  $C_{i,j}$  to move a single unit of the resource from location *i* to location *j*. We assume that the transportation company has the monopoly to transport goods and applies the same linear pricing scheme to all actors of the economy: the cost of shipping *a* units of the resource from *i* to *j* is equal to  $a \times \mathbf{C}_{i,j}$ .

Faced with the problem described above, the operator chooses to solve the linear program described in Equation  $(2.11)$  to obtain a transportation plan  $\mathbf{P}^{\star}$  that quantifies for each pair  $i, j$  the amount of goods  $P_{i,j}$  that must transported from warehouse i to factory *j*. The operator pays on aggregate a total of  $\langle \mathbf{P}^*, \mathbf{C} \rangle$  to the transportation company to execute that plan.

### 2.3. Kantorovich Relaxation

**Permutation matrices as couplings.** For a permutation  $\sigma \in \text{Perm}(n)$ , we write  $P_{\sigma}$ for the corresponding permutation matrix,

$$
\forall (i,j) \in [\![n]\!]^2, \quad (\mathbf{P}_{\sigma})_{i,j} = \begin{cases} 1/n & \text{if } j = \sigma_i, \\ 0 & \text{otherwise.} \end{cases}
$$
 (2.12)

One can check that in that case

$$
\langle \mathbf{C}, \, \mathbf{P}_{\sigma} \rangle = \frac{1}{n} \sum_{i=1}^{n} \mathbf{C}_{i, \sigma_i},
$$

which shows that the assignment problem  $(2.2)$  can be recast as a Kantorovich problem (2.11) where the couplings **P** are restricted to be exactly permutation matrices:

$$
\min_{\sigma \in \text{Perm}(n)} \frac{1}{n} \sum_{i=1}^{n} \mathbf{C}_{i,\sigma(i)} = \min_{\sigma \in \text{Perm}(n)} \langle \mathbf{C}, \, \mathbf{P}_{\sigma} \rangle.
$$

Next, one can easily check that the set of permutation matrices is strictly included in the Birkhoff polytope  $\mathbf{U}(\mathbb{1}_n/n, \mathbb{1}_n/n)$ . Indeed, for any permutation  $\sigma$  we have  $\mathbf{P}_{\sigma} \mathbb{1} =$  $\mathbb{1}_n$  and  $\mathbf{P}_{\sigma}^T \mathbb{1} = \mathbb{1}_n$ , whereas  $\mathbb{1}_n \mathbb{1}_n^T/n^2$  is a valid coupling but not a permutation matrix. Therefore, the minimum of  $\langle \mathbf{C}, \mathbf{P} \rangle$  is necessarily smaller when considering all transportation than when considering only permutation matrices:

$$
\mathcal{L}_{\mathbf{C}}(\mathbb{1}_n/n, \mathbb{1}_n/n) \leq \min_{\sigma \in \text{Perm}(n)} \langle \mathbf{C}, \, \mathbf{P}_{\sigma} \rangle.
$$

The following proposition shows that these problems result in fact in the same optimum, namely that one can always find a permutation matrix that minimizes Kantorovich's problem (2.11) between two uniform measures  $\mathbf{a} = \mathbf{b} = \mathbb{1}_n/n$ . The Kantorovich relaxation is therefore *tight* when considered on assignment problems. Figure 2.4 shows on the left a 2-D example of optimal matching corresponding to this special case.

**Proposition 2.1** (Kantorovich for matching). If  $m = n$  and  $\mathbf{a} = \mathbf{b} = \mathbb{1}_n/n$ , then there exists an optimal solution for Problem (2.11)  $P_{\sigma^*}$ , which is a permutation matrix associated to an optimal permutation  $\sigma^* \in \text{Perm}(n)$  for Problem (2.2).

*Proof.* Birkhoff's theorem [1946] states that the set of extremal points of  $\mathbf{U}(\mathbb{1}_n/n, \mathbb{1}_n/n)$ is equal to the set of permutation matrices. A fundamental theorem of linear programming [Bertsimas and Tsitsiklis, 1997, Theorem 2.7] states that the minimum of a linear objective in a nonempty polyhedron, if finite, is reached at an extremal point of the polyhedron. $\Box$ 

Image /page/13/Figure/1 description: The image displays two starburst-like network graphs side-by-side on a white background. Each graph consists of a central cluster of blue nodes, with numerous red and blue nodes extending outwards, connected by black lines. The red nodes are generally smaller than the blue nodes. The Greek letter alpha (α) is written in red above the upper right portion of the left graph and the upper left portion of the right graph. The Greek letter beta (β) is written in blue to the left of the central cluster in both graphs. The overall impression is of two similar, complex, interconnected structures.

**Figure 2.4:** Comparison of optimal matching and generic couplings. A black segment between  $x_i$ and  $y_j$  indicates a nonzero element in the displayed optimal coupling  $\mathbf{P}_{i,j}$  solving (2.11). Left: optimal matching, corresponding to the setting of Proposition 2.1 (empirical measures with the same number  $n = m$  of points). Right: these two weighted point clouds cannot be matched; instead a Kantorovich coupling can be used to associate two arbitrary discrete measures.

**Remark 2.11** (Kantorovich problem between discrete measures)**.** For discrete measures  $\alpha$ ,  $\beta$  of the form (2.3), we store in the matrix **C** all pairwise costs between points in the supports of  $\alpha, \beta$ , namely  $\mathbf{C}_{i,j} \stackrel{\text{def.}}{=} c(x_i, y_j)$ , to define

$$
\mathcal{L}_c(\alpha, \beta) \stackrel{\text{def.}}{=} \mathcal{L}_{\mathbf{C}}(\mathbf{a}, \mathbf{b}). \tag{2.13}
$$

Therefore, the Kantorovich formulation of optimal transport between discrete measures is the same as the problem between their associated probability weight vectors **a**, **b** except that the cost matrix **C** depends on the support of  $\alpha$  and  $\beta$ . The notation  $\mathcal{L}_c(\alpha, \beta)$ , however, is useful in some situations, because it makes explicit the dependency with respect to *both* probability weights and supporting points, the latter being exclusively considered through the cost function *c*.

**Remark 2.12** (Using optimal assignments and couplings)**.** The optimal transport plan itself (either as a coupling **P** or a Monge map *T* when it exists) has found many applications in data sciences, and in particular image processing. It has, for instance, been used for contrast equalization [Delon, 2004] and texture synthesis Gutierrez et al. [2017]. A significant part of applications of OT to imaging sciences is for image matching [Zhu et al., 2007, Wang et al., 2013, Museyko et al., 2009, Li et al., 2013], image fusion [Courty et al., 2016], medical imaging [Wang et al., 2011] and shape registration [Makihara and Yagi, 2010, Lai and Zhao, 2017,

Image /page/14/Figure/1 description: The image displays three distinct scenarios labeled 'Discrete', 'Semidiscrete', and 'Continuous'. Each scenario illustrates the relationship between two sets of data, labeled 'alpha' and 'beta'. In the 'Discrete' scenario, red dots represent alpha and blue dots represent beta, arranged in clusters. Below these clusters, a grid shows black dots placed at specific intersections, with red dots aligned to the left and blue dots aligned to the top, indicating a discrete mapping. The 'Semidiscrete' scenario shows alpha as a red contour plot and beta as a cluster of blue dots. Below, a grid contains vertical black bars of varying lengths, aligned with blue dots at the top and a red contour plot on the left. The 'Continuous' scenario depicts alpha as a red contour plot and beta as a blue contour plot. Below, a square contains a black curve, with a red contour plot on the left and a blue contour plot on the top, representing a continuous mapping.

**Figure 2.5:** Schematic viewed of input measures  $(\alpha, \beta)$  and couplings  $\mathcal{U}(\alpha, \beta)$  encountered in the three main scenarios for Kantorovich OT. Chapter 5 is dedicated to the semidiscrete setup.

Su et al., 2015], and image watermarking [Mathon et al., 2014]. In astrophysics, OT has been used for reconstructing the early universe [Frisch et al., 2002]. OT has also been used for music transcription [Flamary et al., 2016], and finds numerous applications in economics to interpret matching data [Galichon, 2016]. Lastly, let us note that the computation of transportation maps computed using OT techniques (or inspired from them) is also useful to perform sampling [Reich, 2013, Oliver, 2014] and Bayesian inference [Kim et al., 2013, El Moselhy and Marzouk, 2012].

**Remark 2.13** (Kantorovich problem between arbitrary measures)**.** Definition (2.13) of  $\mathcal{L}_c$  is extended to arbitrary measures by considering couplings  $\pi \in \mathcal{M}^1_+(\mathcal{X} \times \mathcal{Y})$ which are joint distributions over the product space. The discrete case is a special situation where one imposes this product measure to be of the form  $\pi = \sum_{i,j} \mathbf{P}_{i,j} \delta_{(x_i,y_j)}$ . In the general case, the mass conservation constraint (2.10) should be rewritten as a marginal constraint on joint probability distributions

$$
\mathcal{U}(\alpha,\beta) \stackrel{\text{def.}}{=} \left\{ \pi \in \mathcal{M}_+^1(\mathcal{X} \times \mathcal{Y}) \; : \; P_{\mathcal{X} \sharp} \pi = \alpha \quad \text{and} \quad P_{\mathcal{Y} \sharp} \pi = \beta \right\}. \tag{2.14}
$$

Here  $P_{\mathcal{X}^{\sharp}}$  and  $P_{\mathcal{Y}^{\sharp}}$  are the push-forwards (see Definition 2.1) of the projections  $P_X(x,y) = x$  and  $P_Y(x,y) = y$ . Figure 2.5 shows how these coupling constraints translate for different classes of problems (discrete measures and densities). Using (2.7), these marginal constraints are equivalent to imposing that  $\pi(A \times Y) = \alpha(A)$  and  $\pi(X \times B) = \beta(B)$  for sets  $A \subset \mathcal{X}$  and  $B \subset \mathcal{Y}$ . The Kantorovich problem (2.11) is then generalized as

$$
\mathcal{L}_c(\alpha, \beta) \stackrel{\text{def.}}{=} \min_{\pi \in \mathcal{U}(\alpha, \beta)} \int_{\mathcal{X} \times \mathcal{Y}} c(x, y) \mathrm{d}\pi(x, y). \tag{2.15}
$$

This is an infinite-dimensional linear program over a space of measures. If  $(\mathcal{X}, \mathcal{Y})$ are compact spaces and  $c$  is continuous, then it is easy to show that it always has solutions. Indeed  $\mathcal{U}(\alpha, \beta)$  is compact for the weak topology of measures (see Remark 2.2),  $\pi \mapsto \int c d\pi$  is a continuous function for this topology and the constraint set is nonempty (for instance,  $\alpha \otimes \beta \in \mathcal{U}(\alpha, \beta)$ ). Figure 2.6 shows examples of discrete and continuous optimal coupling solving (2.15). Figure 2.7 shows other examples of optimal 1-D couplings, involving discrete and continuous marginals.

Image /page/15/Figure/4 description: The image displays two related visualizations. On the left, there are two plots arranged in a 2x2 grid, with the top-right cell empty. The top-left cell contains a red curve labeled 'α', which appears to be a probability density function with two peaks. The bottom-left cell is empty. The top-right cell contains a blue curve labeled 'β', also resembling a probability density function with two peaks. The bottom-right cell contains a grayscale plot labeled 'π', showing a complex, possibly multi-modal distribution. On the right side of the image, there is a grid representing a matrix, with rows labeled 'α' and columns labeled 'π'. Above the grid, there are several blue circles of varying sizes, labeled 'β'. To the left of the grid, there are several red circles of varying sizes, labeled 'α'. Within the grid, there are black circles of varying sizes, positioned at different intersections of rows and columns, suggesting a relationship or interaction between the 'α' and 'β' variables and the 'π' distribution.

**Figure 2.6:** Left: "continuous" coupling *π* solving (2.14) between two 1-D measures with density. The coupling is localized along the graph of the Monge map  $(x, T(x))$  (displayed in black). Right: "discrete" coupling *T* solving (2.11) between two discrete measures of the form (2.3). The positive entries  $T_{i,j}$  are displayed as black disks at position  $(i, j)$  with radius proportional to  $T_{i,j}$ .

Image /page/15/Figure/6 description: The image displays four diagrams arranged horizontally. Each diagram consists of two parts: a top schematic and a bottom grid. The top schematics show two parallel lines labeled 'alpha' and 'beta'. The first two diagrams depict arrows originating from the 'alpha' line and pointing to the 'beta' line. The third and fourth diagrams show a red circle on the 'alpha' line with arrows pointing to blue circles on the 'beta' line or to segments on the 'beta' line. The bottom grids are square with dotted lines dividing them into smaller squares. Each grid has labels 'alpha' and 'beta' on its axes. The first grid shows a blue segment on the 'beta' axis and a purple line segment forming a diagonal with the label 'pi'. The second grid shows two blue segments on the 'beta' axis and two purple diagonal line segments, each labeled 'pi'. The third grid shows blue circles on the 'beta' axis and purple circles on the grid, with a red circle at the origin of the 'alpha' axis, and a label 'pi'. The fourth grid shows blue segments on the 'beta' axis, a red circle on the 'alpha' axis, and a vertical purple line segment labeled 'pi'.

**Figure 2.7:** Four simple examples of optimal couplings between 1-D distributions, represented as maps above (arrows) and couplings below. Inspired by Lévy and Schwindt [2018].

**Remark 2.14** (Probabilistic interpretation)**.** Kantorovich's problem can be reinterpreted through the prism of random variables, following Remark 2.9. Indeed, Problem (2.15) is equivalent to

$$
\mathcal{L}_c(\alpha, \beta) = \min_{(X, Y)} \left\{ \mathbb{E}_{(X, Y)}(c(X, Y)) : X \sim \alpha, Y \sim \beta \right\},\tag{2.16}
$$

where  $(X, Y)$  is a couple of random variables over  $\mathcal{X} \times \mathcal{Y}$  and  $X \sim \alpha$  (resp.,  $Y \sim \beta$ ) means that the law of *X* (resp., *Y*), represented as a measure, must be  $\alpha$  (resp., *β*). The law of the couple  $(X, Y)$  is then  $\pi \in \mathcal{U}(\alpha, \beta)$  over the product space  $\mathcal{X} \times \mathcal{Y}$ .

#### **2.4 Metric Properties of Optimal Transport**

An important feature of OT is that it defines a distance between histograms and probability measures as soon as the cost matrix satisfies certain suitable properties. Indeed, OT can be understood as a canonical way to lift a ground distance between points to a distance between histogram or measures.

We first consider the case where, using a term first introduced by Rubner et al. [2000], the "ground metric" matrix **C** is fixed, representing substitution costs between bins, and shared across several histograms we would like to compare. The following proposition states that OT provides a valid distance between histograms supported on these bins.

**Proposition 2.2.** We suppose  $n = m$  and that for some  $p \ge 1$ ,  $\mathbf{C} = \mathbf{D}^p = (\mathbf{D}_{i,j}^p)_{i,j} \in$  $\mathbb{R}^{n \times n}$ , where  $\mathbf{D} \in \mathbb{R}^{n \times n}_+$  is a distance on  $[n]$ , *i.e.* 

- (i)  $\mathbf{D} \in \mathbb{R}_+^{n \times n}$  is symmetric;
- (ii)  $\mathbf{D}_{i,j} = 0$  if and only if  $i = j$ ;
- $(iii) \ \forall (i, j, k) \in [n]^3, \mathbf{D}_{i,k} \leq \mathbf{D}_{i,j} + \mathbf{D}_{j,k}.$

Then

$$
W_p(\mathbf{a}, \mathbf{b}) \stackrel{\text{def.}}{=} L_{\mathbf{D}^p}(\mathbf{a}, \mathbf{b})^{1/p} \tag{2.17}
$$

(note that  $W_p$  depends on **D**) defines the *p*-Wasserstein distance on  $\Sigma_n$ , *i.e.*  $W_p$  is symmetric, positive,  $W_p(a, b) = 0$  if and only if  $a = b$ , and it satisfies the triangle inequality

$$
\forall \mathbf{a}, \mathbf{b}, \mathbf{c} \in \Sigma_n, \quad \mathrm{W}_p(\mathbf{a}, \mathbf{c}) \le \mathrm{W}_p(\mathbf{a}, \mathbf{b}) + \mathrm{W}_p(\mathbf{b}, \mathbf{c}).
$$

*Proof.* Symmetry and definiteness of the distance are easy to prove: since  $\mathbf{C} = \mathbf{D}^p$ has a null diagonal,  $W_p(a, a) = 0$ , with corresponding optimal transport matrix  $P^* =$ diag(a); by the positivity of all off-diagonal elements of  $\mathbf{D}^p$ ,  $W_p(\mathbf{a}, \mathbf{b}) > 0$  whenever  $\mathbf{a} \neq \mathbf{b}$  (because in this case, an admissible coupling necessarily has a nonzero element outside the diagonal); by symmetry of  $\mathbf{D}^p$ ,  $W_p(\mathbf{a}, \mathbf{b})$  is itself a symmetric function.

 $\Box$ 

To prove the triangle inequality of Wasserstein distances for arbitrary measures, Villani [2003, Theorem 7.3] uses the gluing lemma, which stresses the existence of couplings with a prescribed structure. In the discrete setting, the explicit constuction of this glued coupling is simple. Let  $\mathbf{a}, \mathbf{b}, \mathbf{c} \in \Sigma_n$ . Let **P** and **Q** be two optimal solutions of the transport problems between **a** and **b**, and **b** and **c**, respectively. To avoid issues that may arise from null coordinates in **b**, we define a vector  $\tilde{\mathbf{b}}$  such that  $\tilde{\mathbf{b}}_j \stackrel{\text{def.}}{=} \mathbf{b}_j$  if  $\mathbf{b}_j > 0$ , and  $\tilde{\mathbf{b}}_j \stackrel{\text{def.}}{=} 1$  otherwise, to write

$$
\mathbf{S} \stackrel{\text{\tiny def.}}{=} \mathbf{P} \operatorname{diag}(1/\tilde{\mathbf{b}}) \mathbf{Q} \in \mathbb{R}_+^{n \times n},
$$

and notice that  $S \in U(a, c)$  because

$$
\mathbf{S} \mathbb{1}_n = \mathbf{P} \operatorname{diag}(1/\tilde{\mathbf{b}}) \mathbf{Q} \mathbb{1}_n = \mathbf{P}(\mathbf{b}/\tilde{\mathbf{b}}) = \mathbf{P} \mathbb{1}_{\operatorname{Supp}(\mathbf{b})} = \mathbf{a},
$$

where we denoted  $\mathbb{1}_{\text{Supp}(\mathbf{b})}$  the vector of size *n* with ones located at those indices *j* where  $\mathbf{b}_j > 0$  and zero otherwise, and we use the fact that  $\mathbf{P} \mathbb{1}_{\text{Supp}(\mathbf{b})} = \mathbf{P} \mathbb{1} = \mathbf{a}$  because necessarily  $P_{i,j} = 0$  for those *j* where  $\mathbf{b}_j = 0$ . Similarly one verifies that  $\mathbf{S}^{\mathrm{T}} \mathbb{1}_n = \mathbf{c}$ . The triangle inequality follows then from

$$
W_p(\mathbf{a}, \mathbf{c}) = \left(\min_{\mathbf{P} \in U(\mathbf{a}, \mathbf{c})} \langle \mathbf{P}, \mathbf{D}^p \rangle \right)^{1/p} \leq \langle \mathbf{S}, \mathbf{D}^p \rangle^{1/p}
$$
  
=  $\left(\sum_{ik} \mathbf{D}_{ik}^p \sum_j \frac{\mathbf{P}_{ij} \mathbf{Q}_{jk}}{\tilde{\mathbf{b}}_j} \right)^{1/p} \leq \left(\sum_{ijk} (\mathbf{D}_{ij} + \mathbf{D}_{jk})^p \frac{\mathbf{P}_{ij} \mathbf{Q}_{jk}}{\tilde{\mathbf{b}}_j} \right)^{1/p}$   
 $\leq \left(\sum_{ijk} \mathbf{D}_{ij}^p \frac{\mathbf{P}_{ij} \mathbf{Q}_{jk}}{\tilde{\mathbf{b}}_j} \right)^{1/p} + \left(\sum_{ijk} \mathbf{D}_{jk}^p \frac{\mathbf{P}_{ij} \mathbf{Q}_{jk}}{\tilde{\mathbf{b}}_j} \right)^{1/p}.$ 

The first inequality is due to the suboptimality of **S**, the second is the triangle inequality for elements in **D**, and the third comes from Minkowski's inequality. One thus has

$$
W_p(\mathbf{a}, \mathbf{c}) \leq \left(\
\sum_{ij} \mathbf{D}_{ij}^p \mathbf{P}_{ij} \
\sum_k \frac{\mathbf{Q}_{jk}}{\tilde{\mathbf{b}}_j}\
\right)^{1/p} + \
\left(\n\sum_{jk} \mathbf{D}_{jk}^p \mathbf{Q}_{jk} \
\sum_i \frac{\mathbf{P}_{ij}}{\tilde{\mathbf{b}}_j}\
\right)^{1/p}
$$
  
=  $\left(\n\sum_{ij} \mathbf{D}_{ij}^p \mathbf{P}_{ij}\
\right)^{1/p} + \n\left(\n\sum_{jk} \mathbf{D}_{jk}^p \mathbf{Q}_{jk}\
\right)^{1/p}$   
=  $W_p(\mathbf{a}, \mathbf{b}) + W_p(\mathbf{b}, c),$ 

which concludes the proof.

**Remark 2.15** (The cases  $0 < p \le 1$ ). Note that if  $0 < p \le 1$ , then  $\mathbf{D}^p$  is itself distance. This implies that while for  $p \geq 1$ ,  $W_p(a, b)$  is a distance, in the case  $p \leq 1$ , it is actually  $W_p(\mathbf{a}, \mathbf{b})^p$  which defines a distance on the simplex.

20

**Remark 2.16** (Applications of Wasserstein distances)**.** The fact that the OT distance automatically "lifts" a ground metric between bins to a metric between histograms on such bins makes it a method of choice for applications in computer vision and machine learning to compare histograms. In these fields, a classical approach is to "pool" local features (for instance, image descriptors) and compute a histogram of the empirical distribution of features (a so-called bag of features) to perform retrieval, clustering or classification; see, for instance, [Oliva and Torralba, 2001]. Along a similar line of ideas, OT distances can be used over some lifted feature spaces to perform signal and image analysis [Thorpe et al., 2017]. Applications to retrieval and clustering were initiated by the landmark paper [Rubner et al., 2000], with renewed applications following faster algorithms for threshold matrices **C** that fit for some applications, for example, in computer vision [Pele and Werman, 2008, 2009]. More recent applications stress the use of the earth mover's distance for bags-of-words, either to carry out dimensionality reduction [Rolet et al., 2016] and classify texts [Kusner et al., 2015, Huang et al., 2016], or to define an alternative loss to train multiclass classifiers that output bags-of-words [Frogner et al., 2015]. Kolouri et al. [2017] provides a recent overview of such applications to signal processing and machine learning.

**Remark 2.17** (Wasserstein distance between measures)**.** Proposition 2.2 can be generalized to deal with arbitrary measures that need not be discrete.

**Proposition 2.3.** We assume  $\mathcal{X} = \mathcal{Y}$  and that for some  $p \geq 1$ ,  $c(x, y) = d(x, y)^p$ , where  $d$  is a distance on  $\mathcal{X}, i.e.$ 

- (i)  $d(x, y) = d(y, x) > 0;$
- (ii)  $d(x, y) = 0$  if and only if  $x = y$ ;
- (iii)  $\forall (x, y, z) \in \mathcal{X}^3, d(x, z) \leq d(x, y) + d(y, z).$

Then the *p*-Wasserstein distance on  $\mathcal{X}$ ,

$$
\mathcal{W}_p(\alpha, \beta) \stackrel{\text{def.}}{=} \mathcal{L}_{d^p}(\alpha, \beta)^{1/p} \tag{2.18}
$$

(note that  $W_p$  depends on *d*), is indeed a distance, namely  $W_p$  is symmetric, nonnegative,  $W_p(\alpha, \beta) = 0$  if and only if  $\alpha = \beta$ , and it satisfies the triangle inequality

 $\forall (\alpha, \beta, \gamma) \in \mathcal{M}^1_+(\mathcal{X})^3$ ,  $\mathcal{W}_p(\alpha, \gamma) \leq \mathcal{W}_p(\alpha, \beta) + \mathcal{W}_p(\beta, \gamma)$ .

*Proof.* The proof follows the same approach as that for Proposition 2.2 and relies on the existence of a coupling between  $(\alpha, \gamma)$  obtained by "gluing" optimal couplings between  $(\alpha, \beta)$  and  $(\beta, \gamma)$ . П

**Remark 2.18** (Geometric intuition and weak convergence)**.** The Wasserstein distance  $\mathcal{W}_p$  has many important properties, the most important being that it is a weak distance, *i.e.* it allows one to compare singular distributions (for instance, discrete ones) whose supports do not overlap and to quantify the spatial shift between the supports of two distributions. In particular, "classical" distances (or divergences) are not even defined between discrete distributions (the *L* <sup>2</sup> norm can only be applied to continuous measures with a density with respect to a base measure, and the discrete  $\ell^2$  norm requires that positions  $(x_i, y_j)$  take values in a predetermined discrete set to work properly). In sharp contrast, one has that for any  $p > 0$ ,  $\mathcal{W}_p^p(\delta_x, \delta_y) = d(x, y)$ . Indeed, it suffices to notice that  $\mathcal{U}(\delta_x, \delta_y) = \{\delta_{x,y}\}\$  and therefore the Kantorovich problem having only one feasible solution,  $\mathcal{W}_p^p(\delta_x, \delta_y)$ is necessarily  $(d(x, y)^p)^{1/p} = d(x, y)$ . This shows that  $\mathcal{W}_p(\delta_x, \delta_y) \to 0$  if  $x \to y$ . This property corresponds to the fact that  $\mathcal{W}_p$  is a way to quantify the weak convergence, as we now define.

**Definition 2.2** (Weak convergence). On a compact domain  $\mathcal{X}$ ,  $(\alpha_k)_k$  converges weakly to  $\alpha$  in  $\mathcal{M}^1_+(\mathcal{X})$  (denoted  $\alpha_k \to \alpha$ ) if and only if for any continuous function  $g \in C(\mathcal{X})$ ,  $\int_{\mathcal{X}} g \, d\alpha_k \to \int_{\mathcal{X}} g \, d\alpha$ . One needs to add additional decay conditions on *g* on noncompact domains. This notion of weak convergence corresponds to the convergence in the law of random vectors.

This convergence can be shown to be equivalent to  $W_p(\alpha_k, \alpha) \to 0$  [Villani, 2009, Theorem 6.8] (together with a convergence of the moments up to order *p* for unbounded metric spaces).

**Remark 2.19** (Translations)**.** A nice feature of the Wasserstein distance over a Euclidean space  $\mathcal{X} = \mathbb{R}^d$  for the ground cost  $c(x, y) = ||x - y||^2$  is that one can factor out translations; indeed, denoting  $T_\tau : x \mapsto x - \tau$  the translation operator, one has

$$
\mathcal{W}_2(T_{\tau\sharp}\alpha,T_{\tau'\sharp}\beta)^2=\mathcal{W}_2(\alpha,\beta)^2-2\langle\tau-\tau',\,\mathbf{m}_\alpha-\mathbf{m}_\beta\rangle+\|\tau-\tau'\|^2\,,
$$

where  $\mathbf{m}_{\alpha} \stackrel{\text{def.}}{=} \int_{\mathcal{X}} x \, d\alpha(x) \in \mathbb{R}^d$  is the mean of  $\alpha$ . In particular, this implies the nice decomposition of the distance as

$$
\mathcal{W}_2(\alpha,\beta)^2 = \mathcal{W}_2(\tilde{\alpha},\tilde{\beta})^2 + ||\mathbf{m}_{\alpha} - \mathbf{m}_{\beta}||^2,
$$

where  $(\tilde{\alpha}, \tilde{\beta})$  are the "centered" zero mean measures  $\tilde{\alpha} = T_{\mathbf{m}_{\alpha} \#} \alpha$ .

**Remark 2.20** (The case  $p = +\infty$ ). Informally, the limit of  $\mathcal{W}_p^p$  as  $p \to +\infty$  is

$$
\mathcal{W}_{\infty}(\alpha,\beta) \stackrel{\text{def.}}{=} \min_{\pi \in \mathcal{U}(\alpha,\beta)} \sup_{(x,y) \in \text{Supp}(\pi)} d(x,y), \tag{2.19}
$$

where the sup should be understood as the essential supremum according to the measure  $\pi$  on  $\mathcal{X}^2$ . In contrast to the cases  $p < +\infty$ , this is a nonconvex optimization problem, which is difficult to solve numerically and to study theoretically. The  $\mathcal{W}_{\infty}$ distance is related to the Hausdorff distance between the supports of  $(\alpha, \beta)$ ; see § 10.6.1. We refer to [Champion et al., 2008] for details.

#### **2.5 Dual Problem**

The Kantorovich problem (2.11) is a constrained convex minimization problem, and as such, it can be naturally paired with a so-called dual problem, which is a constrained concave maximization problem. The following fundamental proposition explains the relationship between the primal and dual problems.

**Proposition 2.4.** The Kantorovich problem  $(2.11)$  admits the dual

$$
L_{\mathbf{C}}(\mathbf{a}, \mathbf{b}) = \max_{(\mathbf{f}, \mathbf{g}) \in \mathbf{R}(\mathbf{C})} \langle \mathbf{f}, \mathbf{a} \rangle + \langle \mathbf{g}, \mathbf{b} \rangle, \tag{2.20}
$$

where the set of admissible dual variables is

$$
\mathbf{R}(\mathbf{C}) \stackrel{\text{def.}}{=} \{ (\mathbf{f}, \mathbf{g}) \in \mathbb{R}^n \times \mathbb{R}^m \; : \; \forall (i, j) \in [\![n]\!] \times [\![m]\!], \mathbf{f} \oplus \mathbf{g} \leq \mathbf{C} \} \, . \tag{2.21}
$$

Such dual variables are often referred to as "Kantorovich potentials."

*Proof.* This result is a direct consequence of the more general result on the strong duality for linear programs [Bertsimas and Tsitsiklis, 1997, p. 148, Theo. 4.4]. The easier part of the proof, namely, establishing that the right-hand side of Equation (2.20) is a lower bound of  $L_C(a, b)$ , is discussed in Remark 3.2 in the next section. For the sake of completeness, let us derive our result using Lagrangian duality. The Lagangian associated to (2.11) reads

$$
\min_{\mathbf{P} \geq 0} \max_{(\mathbf{f}, \mathbf{g}) \in \mathbb{R}^n \times \mathbb{R}^m} \langle \mathbf{C}, \mathbf{P} \rangle + \langle \mathbf{a} - \mathbf{P} \mathbb{1}_m, \mathbf{f} \rangle + \langle \mathbf{b} - \mathbf{P}^{\mathrm{T}} \mathbb{1}_n, \mathbf{g} \rangle. \tag{2.22}
$$

We exchange the min and the max above, which is always possible when considering linear programs (in finite dimension), to obtain

$$
\max_{(\mathbf{f},\mathbf{g})\in\mathbb{R}^n\times\mathbb{R}^m}\ \langle\mathbf{a},\,\mathbf{f}\rangle + \langle\mathbf{b},\,\mathbf{g}\rangle + \min_{\mathbf{P}\geq 0}\ \langle\mathbf{C}-\mathbf{f}\mathbb{1}_m^{\mathrm{T}}-\mathbb{1}_n\mathbf{g}^{\mathrm{T}},\,\mathbf{P}\rangle.
$$

We conclude by remarking that

$$
\min_{\mathbf{P}\geq 0} \langle \mathbf{Q}, \, \mathbf{P} \rangle = \begin{cases} 0 & \text{if } \mathbf{Q} \geq 0, \\ -\infty & \text{otherwise} \end{cases}
$$

 $\Box$ 

so that the constraint reads  $\mathbf{C} - \mathbf{f} \mathbb{1}_m^T - \mathbb{1}_n \mathbf{g}^T = \mathbf{C} - \mathbf{f} \oplus \mathbf{g} \ge 0.$ 

The primal-dual optimality relation for the Lagrangian (2.22) allows us to locate the support of the optimal transport plan (see also §3.3)

$$
\{(i,j)\in[\![n]\!]\times[\![m]\!]:\mathbf{P}_{i,j}>0\}\subset\left\{(i,j)\in[\![n]\!]\times[\![m]\!]\!]:\ \mathbf{f}_i+\mathbf{g}_j=\mathbf{C}_{i,j}\right\}.\tag{2.23}
$$

**Remark 2.21.** Following the interpretation given to the Kantorovich problem in Remark 2.10, we follow with an intuitive presentation of the dual. Recall that in that setup, an operator wishes to move at the least possible cost an overall amount of resources from warehouses to factories. The operator can do so by solving (2.11), follow the instructions set out in  $\mathbf{P}^*$ , and pay  $\langle \mathbf{P}^*, \mathbf{C} \rangle$  to the transportation company.

*Outsourcing logistics.* Suppose that the operator does not have the computational means to solve the linear program (2.11). He decides instead to outsource that task to a vendor. The vendor chooses a pricing scheme with the following structure: the vendor splits the logistic task into that of collecting and then delivering the goods and will apply a collection price  $f_i$  to collect a unit of resource at each warehouse  $i$  (no matter where that unit is sent to) and a price  $g_j$  to deliver a unit of resource to factory  $j$ (no matter from which warehouse that unit comes from). On aggregate, since there are exactly  $a_i$  units at warehouse *i* and  $b_j$  needed at factory *j*, the vendor asks as a consequence of that pricing scheme a price of  $\langle \mathbf{f}, \mathbf{a} \rangle + \langle \mathbf{g}, \mathbf{b} \rangle$  to solve the operator's logistic problem.

*Setting prices.* Note that the pricing system used by the vendor allows quite naturally for arbitrarily negative prices. Indeed, if the vendor applies a price vector **f** for warehouses and a price vector **g** for factories, then the total bill will not be changed by simultaneously decreasing all entries in **f** by an arbitrary number and increasing all entries of **g** by that same number, since the total amount of resources in all warehouses is equal to those that have to be delivered to the factories. In other words, the vendor can give the illusion of giving an extremely good deal to the operator by paying him to collect some of his goods, but compensate that loss by simply charging him more for delivering them. Knowing this, the vendor, wishing to charge as much as she can for that service, sets vectors **f** and **g** to be as high as possible.

*Checking prices.* In the absence of another competing vendor, the operator must therefore think of a quick way to check that the vendor's prices are reasonable. A possible way to do so would be for the operator to compute the price  $L_C(a, b)$  of the most efficient plan by solving problem (2.11) and check if the vendor's offer is at the very least no larger than that amount. However, recall that the operator cannot afford such a lengthy computation in the first place. Luckily, there is a far more efficient way for the operator to check whether the vendor has a competitive offer. Recall that **f***<sup>i</sup>* is the price charged by the vendor for picking a unit at  $i$  and  $\mathbf{g}_j$  to deliver one at  $j$ . Therefore, the vendor's pricing scheme implies that transferring one unit of the resource

### 2.5. Dual Problem

from *i* to *j* costs exactly  $\mathbf{f}_i + \mathbf{g}_j$ . Yet, the operator also knows that the cost of shipping one unit from *i* to *j* as priced by the transporting company is  $C_{i,j}$ . Therefore, if for any pair *i, j* the aggregate price  $f_i + g_j$  is strictly larger that  $C_{i,j}$ , the vendor is charging more than the fair price charged by the transportation company for that task, and the operator should refuse the vendor's offer.

Image /page/22/Figure/2 description: The image displays two scatter plots side-by-side. The left plot shows blue circles labeled 'α' connected by thick gray lines to red squares labeled 'P\*'. There are three blue circles on the left and five red squares on the right, with lines connecting them in a pattern. The label 'β' is placed between the two sets of points. The right plot shows a collection of points enclosed by a gray contour. Within this contour, there are several colored circles and squares. Specifically, there are two orange circles, one yellow circle, and three squares filled with dark red or black. Outside the contour, there are two blue circles with yellow outlines. The label 'f\*' is placed near the top left of the enclosed area, and 'g\*' is placed near the bottom right. To the right of the plot, there is a vertical color bar ranging from -20 at the bottom to 40 at the top, with yellow at the top, transitioning through orange and red to black at the bottom, indicating a color scale.

**Figure 2.8:** Consider in the left plot the optimal transport problem between two discrete measures *α* and  $\beta$ , represented respectively by blue dots and red squares. The area of these markers is proportional to the weight at each location. That plot also displays the optimal transport  $P^*$  using a quadratic Euclidean cost. The corresponding dual (Kantorovich) potentials  $f^*$  and  $g^*$  that correspond to that configuration are also displayed on the right plot. Since there is a "price"  $f_i^*$  for each point in  $\alpha$  (and conversely for **g** and  $\beta$ ), the color at that point represents the obtained value using the color map on the right. These potentials can be interpreted as relative prices in the sense that they indicate the individual cost, under the best possible transport scheme, to move a mass away at each location in  $\alpha$ , or on the contrary to send a mass toward any point in  $\beta$ . The optimal transport cost is therefore equal to the sum of the squared lengths of all the arcs on the left weighted by their thickness or, alternatively, using the dual formulation, to the sum of the values (encoded with colors) multiplied by the area of each marker on the right plot.

*Optimal prices as a dual problem.* It is therefore in the interest of the operator to check that for all pairs *i, j* the prices offered by the vendor verify  $\mathbf{f}_i + \mathbf{g}_j \leq \mathbf{C}_{i,j}$ . Suppose that the operator does check that the vendor has provided price vectors that do comply with these  $n \times m$  inequalities. Can he conclude that the vendor's proposal is attractive? Doing a quick back of the hand calculation, the operator does indeed conclude that it is in his interest to accept that offer. Indeed, since any of his transportation plans **P** would have a cost  $\langle \mathbf{P}, \mathbf{C} \rangle = \sum_{i,j} \mathbf{P}_{i,j} \mathbf{C}_{i,j}$ , the operator can conclude by applying these  $n \times m$  inequalities that for any transport plan **P** (including the optimal one  $\mathbf{P}^*$ ), the marginal constraints imply

$$
\sum_{i,j} \mathbf{P}_{i,j} \mathbf{C}_{i,j} \ge \sum_{i,j} \mathbf{P}_{i,j} \left( \mathbf{f}_i + \mathbf{g}_j \right) = \left( \sum_i \mathbf{f}_i \sum_j \mathbf{P}_{i,j} \right) + \left( \sum_j \mathbf{g}_j \sum_i \mathbf{P}_{i,j} \right)
$$
$$
= \langle \mathbf{f}, \mathbf{a} \rangle + \langle \mathbf{g}, \mathbf{b} \rangle,
$$

and therefore observe that *any* attempt at doing the job by himself would necessarily be more expensive than the vendor's price.

Knowing this, the vendor must therefore find a set of prices **f***,* **g** that maximize  $\langle f, \mathbf{a} \rangle + \langle \mathbf{g}, \mathbf{b} \rangle$  but that must satisfy at the very least for all *i, j* the basic inequality that  $\mathbf{f}_i + \mathbf{g}_j \leq \mathbf{C}_{i,j}$  for his offer to be accepted, which results in Problem (2.20). One can show, as we do later in §3.1, that the best price obtained by the vendor is in fact exactly equal to the best possible cost the operator would obtain by computing  $L_C(a, b)$ .

Figure 2.8 illustrates the primal and dual solutions resulting from the same transport problem. On the left, blue dots represent warehouses and red dots stand for factories; the areas of these dots stand for the probability weights **a***,* **b**, links between them represent an optimal transport, and their width is proportional to transfered amounts. Optimal prices obtained by the vendor as a result of optimizing Problem (2.20) are shown on the right. Prices have been chosen so that their mean is equal to 0. The highest relative prices come from collecting goods at an isolated warehouse on the lower left of the figure, and delivering goods at the factory located in the upper right area.

**Remark 2.22** (Dual problem between arbitrary measures)**.** To extend this primaldual construction to arbitrary measures, it is important to realize that measures are naturally paired in duality with continuous functions (a measure can be accessed only through integration against continuous functions). The duality is formalized in the following proposition, which boils down to Proposition 2.4 when dealing with discrete measures.

#### **Proposition 2.5.** One has

$$
\mathcal{L}_c(\alpha, \beta) = \sup_{(f,g) \in \mathcal{R}(c)} \int_{\mathcal{X}} f(x) \mathrm{d}\alpha(x) + \int_{\mathcal{Y}} g(y) \mathrm{d}\beta(y), \tag{2.24}
$$

where the set of admissible dual potentials is

$$
\mathcal{R}(c) \stackrel{\text{def.}}{=} \left\{ (f,g) \in \mathcal{C}(\mathcal{X}) \times \mathcal{C}(\mathcal{Y}) \; : \; \forall (x,y), f(x) + g(y) \le c(x,y) \right\}. \tag{2.25}
$$

Here,  $(f, g)$  is a pair of continuous functions and are also called, as in the discrete case, "Kantorovich potentials."

The discrete case (2.20) corresponds to the dual vectors being samples of the continuous potentials, *i.e.*  $(\mathbf{f}_i, \mathbf{g}_j) = (f(x_i), g(y_j))$ . The primal-dual optimality conditions allow us to track the support of the optimal plan, and (2.23) is generalized as

Supp $(\pi) \subset \{(x, y) \in \mathcal{X} \times \mathcal{Y} : f(x) + g(y) = c(x, y)\}\.$  (2.26)

Note that in contrast to the primal problem (2.15), showing the existence of solutions to  $(2.24)$  is nontrivial, because the constraint set  $\mathcal{R}(c)$  is not compact and the function to minimize noncoercive. Using the machinery of *c*-transform detailed

### 2.5. Dual Problem

in § 5.1, in the case  $c(x, y) = d(x, y)^p$  with  $p \ge 1$ , one can, however, show that optimal  $(f, g)$  are necessarily Lipschitz regular, which enables us to replace the constraint by a compact one.

**Remark 2.23** (Unconstrained dual). In the case  $\int_{\mathcal{X}} d\alpha = \int_{\mathcal{Y}} d\beta = 1$ , the constrained dual problem (2.24) can be replaced by an unconstrained one,

$$
\mathcal{L}_c(\alpha, \beta) = \sup_{(f,g)\in\mathcal{C}(\mathcal{X})\times\mathcal{C}(\mathcal{Y})} \int_{\mathcal{X}} f \, d\alpha + \int_{\mathcal{Y}} g \, d\beta + \min_{\mathcal{X}\otimes\mathcal{Y}} \left(c - f \oplus g\right),\tag{2.27}
$$

where we denoted  $(f \oplus g)(x, y) = f(x) + g(y)$ . Here the minimum should be considered as the essential supremum associated to the measure  $\alpha \otimes \beta$ , i.e., it does not change if f or q is modified on sets of zero measure for  $\alpha$  and  $\beta$ . This alternative dual formulation was pointed out to us by Francis Bach. It is obtained from the primal problem (2.15) by adding the redundant constraint  $\int d\pi = 1$ .

**Remark 2.24** (Monge–Kantorovich equivalence—Brenier theorem)**.** The following theorem is often attributed to Brenier [1991] and ensures that in  $\mathbb{R}^d$  for  $p = 2$ , if at least one of the two input measures has a density, and for measures with second order moments, then the Kantorovich and Monge problems are equivalent. The interested reader should also consult variants of the same result published more or less at the same time by Cuesta and Matran [1989], Rüschendorf and Rachev [1990], including notably the original result in [Brenier, 1987] and a precursor by Knott and Smith [1984].

**Theorem 2.1** (Brenier). In the case  $\mathcal{X} = \mathcal{Y} = \mathbb{R}^d$  and  $c(x, y) = ||x - y||^2$ , if at least one of the two input measures (denoted  $\alpha$ ) has a density  $\rho_{\alpha}$  with respect to the Lebesgue measure, then the optimal  $\pi$  in the Kantorovich formulation (2.15) is unique and is supported on the graph  $(x, T(x))$  of a "Monge map"  $T : \mathbb{R}^d \to \mathbb{R}^d$ . This means that  $\pi = (\text{Id}, T)_{\text{H}}\alpha$ , *i.e.* 

$$
\forall h \in \mathcal{C}(\mathcal{X} \times \mathcal{Y}), \quad \int_{\mathcal{X} \times \mathcal{Y}} h(x, y) d\pi(x, y) = \int_{\mathcal{X}} h(x, T(x)) d\alpha(x). \tag{2.28}
$$

Furthermore, this map *T* is uniquely defined as the gradient of a convex function  $\varphi$ ,  $T(x) = \nabla \varphi(x)$ , where  $\varphi$  is the unique (up to an additive constant) convex function such that  $(\nabla \varphi)_{\sharp} \alpha = \beta$ . This convex function is related to the dual potential *f* solving (2.24) as  $\varphi(x) = \frac{\|x\|^2}{2} - f(x)$ .

*Proof.* We sketch the main ingredients of the proof; more details can be found, for instance, in [Santambrogio, 2015]. We remark that  $\int c d\pi = C_{\alpha,\beta} - 2 \int \langle x, y \rangle d\pi(x,y)$ ,

where the constant is  $C_{\alpha,\beta} = \int ||x||^2 d\alpha(x) + \int ||y||^2 d\beta(y)$ . Instead of solving (2.15), one can thus consider the problem

$$
\max_{\pi \in \mathcal{U}(\alpha,\beta)} \int_{\mathcal{X} \times \mathcal{Y}} \langle x, y \rangle d\pi(x,y),
$$

whose dual reads

$$
\min_{(\varphi,\psi)} \left\{ \int_{\mathcal{X}} \varphi \, d\alpha + \int_{\mathcal{Y}} \psi \, d\beta \; : \; \forall (x,y), \quad \varphi(x) + \psi(y) \ge \langle x, y \rangle \right\}. \tag{2.29}
$$

The relation between these variables and those of  $(2.25)$  is  $(\varphi, \psi) = (\frac{\|\cdot\|^2}{2} - f, \frac{\|\cdot\|^2}{2} - f)$ *g*). One can replace the constraint by

$$
\forall y, \quad \psi(y) \ge \varphi^*(y) \stackrel{\text{def.}}{=} \sup_x \langle x, y \rangle - \varphi(x). \tag{2.30}
$$

Here  $\varphi^*$  is the Legendre transform of  $\varphi$  and is a convex function as a supremum of linear forms (see also  $(4.54)$ ). Since the objective appearing in  $(2.31)$  is linear and the integrating measures positive, one can minimize explicitly with respect to  $\psi$  and set  $\psi = \varphi^*$  in order to consider the unconstrained problem

$$
\min_{\varphi} \int_{\mathcal{X}} \varphi \, \mathrm{d}\alpha + \int_{\mathcal{Y}} \varphi^* \, \mathrm{d}\beta; \tag{2.31}
$$

see also §3.2 and §5.1, where that idea is applied respectively in the discrete setting and for generic costs  $c(x, y)$ . By iterating this argument twice, one can replace  $\varphi$  by  $\varphi^{**}$ , which is a convex function, and thus impose in (2.31) that  $\varphi$  is convex. Condition (2.26) shows that an optimal  $\pi$  is supported on  $\{(x, y) : \varphi(x) + \varphi^*(y) = \langle x, y \rangle\}$ , which shows that such a *y* is optimal for the minimization (2.30) of the Legendre transform, whose optimality condition reads  $y \in \partial \varphi(x)$ . Since  $\varphi$  is convex, it is differentiable almost everywhere, and since  $\alpha$  has a density, it is also differentiable  $\alpha$ -almost everywhere. This shows that for each *x*, the associated *y* is uniquely defined *α*-almost everywhere as  $y = \nabla \varphi(x)$ , and it shows that necessarily  $\pi = (\text{Id}, \nabla \varphi)_{\text{H}}\alpha$ .  $\Box$ 

This result shows that in the setting of  $\mathcal{W}_2$  with no-singular densities, the Monge problem (2.9) and its Kantorovich relaxation (2.15) are equal (the relaxation is tight). This is the continuous counterpart of Proposition 2.1 for the assignment case (2.1), which states that the minimum of the optimal transport problem is achieved at a permutation matrix (a discrete map) when the marginals are equal and uniform. Brenier's theorem, stating that an optimal transport map must be the gradient of a convex function, provides a useful generalization of the notion of increasing functions in dimension more than one. This is the main reason why

### 2.5. Dual Problem

optimal transport can be used to define quantile functions in arbitrary dimensions, which is in turn useful for applications to quantile regression problems [Carlier et al., 2016].

Note also that this theorem can be extended in many directions. The condition that  $\alpha$  has a density can be weakened to the condition that it does not give mass to "small sets" having Hausdorff dimension smaller than *d*−1 (*e.g.* hypersurfaces). One can also consider costs of the form  $c(x, y) = h(x - y)$ , where *h* is a strictly convex function.

**Remark 2.25** (Monge–Ampère equation)**.** For measures with densities, using (2.8), one obtains that  $\varphi$  is the unique (up to the addition of a constant) convex function which solves the following Monge–Ampère-type equation:

$$
\det(\partial^2 \varphi(x))\rho_\beta(\nabla \varphi(x)) = \rho_\alpha(x) \tag{2.32}
$$

where  $\partial^2 \varphi(x) \in \mathbb{R}^{d \times d}$  is the Hessian of  $\varphi$ . The Monge–Ampère operator det( $\partial^2 \varphi(x)$ ) can be understood as a nonlinear degenerate Laplacian. In the limit of small displacements,  $\varphi = \text{Id} + \varepsilon \psi$ , one indeed recovers the Laplacian  $\Delta$  as a linearization since for smooth maps

$$
\det(\partial^2 \varphi(x)) = 1 + \varepsilon \Delta \psi(x) + o(\varepsilon).
$$

The convexity constraint forces  $\det(\partial^2 \varphi(x)) \geq 0$  and is necessary for this equation to have a solution. There is a large body of literature on the theoretical analysis of the Monge–Ampère equation, and in particular the regularity of its solution—see, for instance, [Gutiérrez, 2016]; we refer the interested read to the review paper by Caffarelli [2003]. A major difficulty is that in full generality, solutions need not be smooth, and one has to resort to the machinery of Alexandrov solutions when the input measures are arbitrary (*e.g.* Dirac masses). Many solvers have been proposed in the simpler case of the Monge–Ampère equation  $\det(\partial^2 \varphi(x)) = f(x)$  for a fixed right-hand-side *f*; see, for instance, [Benamou et al., 2016b] and the references therein. In particular, capturing anisotropic convex functions requires special care, and usual finite differences can be inaccurate. For optimal transport, where *f* actually depends on  $\nabla\varphi$ , the discretization of Equation (2.32), and the boundary condition result in technical challenges outlined in [Benamou et al., 2014] and the references therein. Note also that related solvers based on fixed-point iterations have been applied to image registration [Haker et al., 2004].

#### **2.6 Special Cases**

In general, computing OT distances is numerically involved. Before detailing in §§3,4, and 7 different numerical solvers, we first review special favorable cases where the resolution of the OT problem is relatively easy.

**Remark 2.26** (Binary cost matrix and 1-norm)**.** One can easily check that when the cost matrix **C** is 0 on the diagonal and 1 elsewhere, namely, when  $\mathbf{C} = \mathbb{1}_{n \times n} - \mathbb{1}_n$ , the 1-Wasserstein distance between **a** and **b** is equal to the 1-norm of their difference,  $L_{\mathbf{C}}(\mathbf{a}, \mathbf{b}) = ||\mathbf{a} - \mathbf{b}||_1.$ 

**Remark 2.27** (Kronecker cost function and total variation)**.** In addition to Remark 2.26 above, one can also easily check that this result extends to arbitrary measures in the case where  $c(x, y)$  is 0 if  $x = y$  and 1 when  $x \neq y$ . The OT distance between two discrete measures  $\alpha$  and  $\beta$  is equal to their total variation distance (see also Example 8.2).

**Remark 2.28** (1-D case—Empirical measures). Here  $\mathcal{X} = \mathbb{R}$ . Assuming  $\alpha =$ 1  $\frac{1}{n}\sum_{i=1}^{n}\delta_{x_i}$  and  $\beta = \frac{1}{n}$  $\frac{1}{n} \sum_{j=1}^{n} \delta_{y_j}$ , and assuming (without loss of generality) that the points are ordered, *i.e.*  $x_1 \leq x_2 \leq \cdots \leq x_n$  and  $y_1 \leq y_2 \leq \cdots \leq y_n$ , then one has the simple formula

$$
\mathcal{W}_p(\alpha,\beta)^p = \frac{1}{n} \sum_{i=1}^n |x_i - y_i|^p, \qquad (2.33)
$$

*i.e.* locally (if one assumes distinct points),  $W_p(\alpha, \beta)$  is the  $\ell^p$  norm between two vectors of ordered values of  $\alpha$  and  $\beta$ . That statement is valid only locally, in the sense that the order (and those vector representations) might change whenever some of the values change. That formula is a simple consequence of the more general setting detailed in Remark 2.30. Figure 2.9, top row, illustrates the 1-D transportation map between empirical measures with the same number of points. The bottom row shows how this monotone map generalizes to arbitrary discrete measures.

It is also possible to leverage this 1-D computation to also compute efficiently OT on the circle as shown by Delon et al. [2010]. Note that if the cost is a concave function of the distance, notably when *p <* 1, the behavior of the optimal transport plan is very different, yet efficient solvers also exist [Delon et al., 2012].

Image /page/28/Figure/1 description: The image displays two horizontal timelines with colored dots and arrows, illustrating a process or system. The top timeline shows a sequence of blue and red dots, with black arrows indicating movement or transitions between them. Some arrows form arcs, suggesting jumps or longer-range interactions. The label 'sigma' is placed above the first blue dot on this timeline. The bottom timeline mirrors the top one in terms of the arrangement of dots and arrows, but with a key difference: some dots, both blue and red, are significantly larger than others. Specifically, the first blue dot and a red dot in the middle of the sequence are depicted as larger. Above the timelines, the Greek letters 'alpha' and 'beta' are shown next to a red dot and a blue dot, respectively, likely serving as labels for the dot types.

**Figure 2.9:** 1-D optimal couplings: each arrow  $x_i \rightarrow y_j$  indicates a nonzero  $\mathbf{P}_{i,j}$  in the optimal coupling. Top: empirical measures with same number of points (optimal matching). Bottom: generic case. This corresponds to monotone rearrangements, if  $x_i \leq x_{i'}$  are such that  $\mathbf{P}_{i,j} \neq 0$ ,  $\mathbf{P}_{i',j'} \neq 0$ , then necessarily  $y_j \leq y_{j'}$ .

**Remark 2.29** (Histogram equalization)**.** One-dimensional optimal transport can be used to perform histogram equalization, with applications to the normalization of the palette of grayscale images, see Figure 2.10. In this case, one denotes  $(\bar{x}_i)_i$  and  $(\bar{y}_i)_i$  the gray color levels (0 for black, 1 for white, and all values in between) of all pixels of the two input images enumerated in a predefined order (*i.e.* columnwise). Assuming the number of pixels in each image is the same and equal to  $n \times m$ , sorting these color levels defines  $x_i = \bar{x}_{\sigma_1(i)}$  and  $y_j = \bar{y}_{\sigma_2(j)}$  as in Remark 2.28, where  $\sigma_1, \sigma_2 : \{1, \ldots, nm\} \to \{1, \ldots, nm\}$  are permutations, so that  $\sigma \stackrel{\text{def.}}{=} \sigma_2 \circ \sigma_1^{-1}$  is the optimal assignment between the two discrete distributions. For image processing applications,  $(\bar{y}_{\sigma(i)})_i$  defines the color values of an equalized version of  $\bar{x}$ , whose empirical distribution matches exactly the one of  $\bar{y}$ . The equalized version of that image can be recovered by folding back that *nm*-dimensional vector as an image of size  $n \times m$ . Also,  $t \in [0, 1] \mapsto (1 - t)\bar{x}_i + t\bar{y}_{\sigma(i)}$  defines an interpolation between the original image and the equalized one, whose empirical distribution of pixels is the displacement interpolation (as defined in (7.7)) between those of the inputs.

**Remark 2.30** (1-D case—Generic case). For a measure  $\alpha$  on  $\mathbb{R}$ , we introduce the cumulative distribution function from  $\mathbb{R}$  to  $\rightarrow$  [0, 1] defined as

$$
\forall x \in \mathbb{R}, \quad \mathcal{C}_{\alpha}(x) \stackrel{\text{def.}}{=} \int_{-\infty}^{x} d\alpha,
$$
 (2.34)

and its pseudoinverse  $C_{\alpha}^{-1} : [0,1] \to \mathbb{R} \cup \{-\infty\}$ 

$$
\forall r \in [0,1], \quad \mathcal{C}_{\alpha}^{-1}(r) = \min_{x} \{x \in \mathbb{R} \cup \{-\infty\} : \mathcal{C}_{\alpha}(x) \ge r\}.
$$
 (2.35)

That function is also called the generalized quantile function of  $\alpha$ . For any  $p \geq 1$ ,

Image /page/29/Figure/1 description: The image displays five grayscale photographs of a hibiscus flower arranged horizontally in the top row. Below each flower image is a corresponding histogram in the bottom row, representing the pixel intensity distribution. The histograms are labeled with time values: t = 0, t = 0.25, t = 0.5, t = .75, and t = 1. The histogram for t = 0 shows a distribution skewed towards darker tones, with a peak in the lower intensity range. As the time value increases, the histograms shift towards higher intensity values, with the histogram for t = 1 showing a uniform distribution across the higher intensity range, indicating a brightening or transformation of the image over time.

**Figure 2.10:** Histogram equalization for image processing, where *t* parameterizes the displacement interpolation between the histograms.

one has

$$
\mathcal{W}_p(\alpha,\beta)^p = \left\| \mathcal{C}_\alpha^{-1} - \mathcal{C}_\beta^{-1} \right\|_{L^p([0,1])}^p = \int_0^1 |\mathcal{C}_\alpha^{-1}(r) - \mathcal{C}_\beta^{-1}(r)|^p dr. \tag{2.36}
$$

This means that through the map  $\alpha \mapsto C_{\alpha}^{-1}$ , the Wasserstein distance is isometric to a linear space equipped with the  $L^p$  norm or, equivalently, that the Wasserstein distance for measures on the real line is a Hilbertian metric. This makes the geometry of 1-D optimal transport very simple but also very different from its geometry in higher dimensions, which is not Hilbertian as discussed in Proposition 8.1 and more generally in §8.3. For  $p = 1$ , one even has the simpler formula

$$
\mathcal{W}_1(\alpha, \beta) = \|\mathcal{C}_\alpha - \mathcal{C}_\beta\|_{L^1(\mathbb{R})} = \int_{\mathbb{R}} |\mathcal{C}_\alpha(x) - \mathcal{C}_\beta(x)| \, \mathrm{d}x \tag{2.37}
$$

$$
= \int_{\mathbb{R}} \left| \int_{-\infty}^{x} d(\alpha - \beta) \right| dx, \tag{2.38}
$$

which shows that  $W_1$  is a norm (see §6.2 for the generalization to arbitrary dimensions). An optimal Monge map *T* such that  $T_i \alpha = \beta$  is then defined by

$$
T = \mathcal{C}_{\beta}^{-1} \circ \mathcal{C}_{\alpha}.\tag{2.39}
$$

Figure 2.11 illustrates the computation of 1-D OT through cumulative functions. It also displays displacement interpolations, computed as detailed in (7.7); see also Remark 9.6. For a detailed survey of the properties of optimal transport in one dimension, we refer the reader to [Santambrogio, 2015, Chapter 2].

**Remark 2.31** (Distance between Gaussians). If  $\alpha = \mathcal{N}(\mathbf{m}_{\alpha}, \Sigma_{\alpha})$  and

### 2.6. Special Cases

Image /page/30/Figure/1 description: The image displays a series of plots related to optimal transport. The top row shows three plots. The first plot, labeled 'α', is a red histogram with a bimodal distribution. The second plot, labeled 'β', is a blue histogram with a trimodal distribution. The third plot, labeled '(tT + (1 − t)Id)]α', shows a sequence of distributions transitioning from red to blue, suggesting a transformation over time or parameter 't'. The bottom row contains four plots, each with x and y axes ranging from 0 to 1. The first plot, labeled '(Cα, Cβ)', shows two curves, one red labeled 'Cμ' and one blue labeled 'Cν', representing cumulative distribution functions. The second plot, labeled '(C−1α , C−1β)', shows two inverse cumulative distribution functions, one red labeled 'C−1μ' and one blue labeled 'C−1ν'. The third plot, labeled '(T, T−1)', shows two curves, one yellow labeled 'T' and one teal labeled 'T−1'. The fourth plot, labeled '(1 − t)C−1α + tC−1β', displays a family of curves in shades of purple and blue, indicating a parameterized set of inverse cumulative distribution functions.

**Figure 2.11:** Computation of OT and displacement interpolation between two 1-D measures, using cumulant function as detailed in (2.39).

 $\mathcal{N}(\mathbf{m}_{\beta}, \mathbf{\Sigma}_{\beta})$  are two Gaussians in  $\mathbb{R}^d$ , then one can show that the following map  $T: x \mapsto \mathbf{m}_{\beta} + A(x - \mathbf{m}_{\alpha}),$  (2.40)

where

$$
A = \Sigma_\alpha^{-\frac{1}{2}} \left( \Sigma_\alpha^{\frac{1}{2}} \Sigma_\beta \Sigma_\alpha^{\frac{1}{2}} \right)^{\frac{1}{2}} \Sigma_\alpha^{-\frac{1}{2}} = A^{\mathrm{T}},
$$

is such that  $T_{\sharp}\rho_{\alpha} = \rho_{\beta}$ . Indeed, one simply has to notice that the change of variables formula (2.8) is satisfied since

$$
\rho_{\beta}(T(x)) = \det(2\pi \Sigma_{\beta})^{-\frac{1}{2}} \exp(-\langle T(x) - \mathbf{m}_{\beta}, \Sigma_{\beta}^{-1}(T(x) - \mathbf{m}_{\beta})\rangle)
$$
  
$$
= \det(2\pi \Sigma_{\beta})^{-\frac{1}{2}} \exp(-\langle x - \mathbf{m}_{\alpha}, A^{\mathsf{T}}\Sigma_{\beta}^{-1}A(x - \mathbf{m}_{\alpha})\rangle)
$$
  
$$
= \det(2\pi \Sigma_{\beta})^{-\frac{1}{2}} \exp(-\langle x - \mathbf{m}_{\alpha}, \Sigma_{\alpha}^{-1}(x - \mathbf{m}_{\alpha})\rangle),
$$

and since *T* is a linear map we have that

$$
|\det T'(x)| = \det A = \left(\frac{\det \mathbf{\Sigma}_{\beta}}{\det \mathbf{\Sigma}_{\alpha}}\right)^{\frac{1}{2}}
$$

and we therefore recover  $\rho_{\alpha} = |\det T'|\rho_{\beta}$  meaning  $T_{\sharp}\alpha = \beta$ . Notice now that *T* is the gradient of the convex function  $\psi: x \mapsto \frac{1}{2}\langle x - \mathbf{m}_{\alpha}, A(x - \mathbf{m}_{\alpha})\rangle + \langle \mathbf{m}_{\beta}, x\rangle$ to conclude, using Brenier's theorem [1991] (see Remark 2.24), that *T* is optimal. Both that map *T* and the corresponding potential  $\psi$  are illustrated in Figures 2.12 and 2.13

With additional calculations involving first and second order moments of  $\rho_{\alpha}$ , we obtain that the transport cost of that map is

$$
\mathcal{W}_2^2(\alpha, \beta) = ||\mathbf{m}_\alpha - \mathbf{m}_\beta||^2 + \mathcal{B}(\mathbf{\Sigma}_\alpha, \mathbf{\Sigma}_\beta)^2,
$$
\n(2.41)

where  $\beta$  is the so-called Bures metric [1969] between positive definite matrices (see also Forrester and Kieburg [2016]),

$$
\mathcal{B}(\Sigma_{\alpha}, \Sigma_{\beta})^2 \stackrel{\text{def.}}{=} \text{tr}\left(\Sigma_{\alpha} + \Sigma_{\beta} - 2(\Sigma_{\alpha}^{1/2} \Sigma_{\beta} \Sigma_{\alpha}^{1/2})^{1/2}\right),\tag{2.42}
$$

where  $\Sigma^{1/2}$  is the matrix square root. One can show that  $\beta$  is a distance on covariance matrices and that  $\mathcal{B}^2$  is convex with respect to both its arguments. In the case where  $\Sigma_{\alpha} = \text{diag}(r_i)_i$  and  $\Sigma_{\beta} = \text{diag}(s_i)_i$  are diagonals, the Bures metric is the Hellinger distance √ √

$$
\mathcal{B}(\mathbf{\Sigma}_{\alpha},\mathbf{\Sigma}_{\beta})=\left\Vert \sqrt{r}-\sqrt{s}\right\Vert _{2}.
$$

For 1-D Gaussians,  $\mathcal{W}_2$  is thus the Euclidean distance on the 2-D plane plotting the mean and the standard deviation of a Gaussian  $(\mathbf{m}, \sqrt{\Sigma})$ , as illustrated in Figure 2.14. For a detailed treatment of the Wasserstein geometry of Gaussian distributions, we refer to Takatsu [2011], and for additional considerations on the Bures metric the reader can consult the very recent references [Malagò et al., 2018, Bhatia et al., 2018]. One can also consult [Muzellec and Cuturi, 2018] for a a recent application of this metric to compute probabilistic embeddings for words, [Shafieezadeh Abadeh et al., 2018] to see how it is used to compute a robust extension to Kalman filtering, or [Mallasto and Feragen, 2017] in which it is applied to covariance functions in reproducing kernel Hilbert spaces.

**Remark 2.32** (Distance between elliptically contoured distributions)**.** Gelbrich provides a more general result than that provided in Remark 2.31: the Bures metric between Gaussians extends more generally to *elliptically contoured distributions* [1990]. In a nutshell, one can first show that for two measures with given mean and covariance matrices, the distance between the two Gaussians with these respective parameters is a lower bound of the Wasserstein distance between the two measures [Gelbrich, 1990, Theorem 2.1]. Additionally, the closed form (2.41) extends to families of elliptically contoured densities: If two densities  $\rho_{\alpha}$  and  $\rho_{\beta}$ belong to such a family, namely when  $\rho_{\alpha}$  and  $\rho_{\beta}$  can be written for any point *x* using a mean and positive definite parameter,

### 2.6. Special Cases

Image /page/32/Figure/1 description: The image displays two overlapping probability density functions, labeled \(\rho\_{\alpha}\) and \(\rho\_{\beta}\). The x-axis ranges from -4 to 6, and the y-axis ranges from -3 to 4. \(\rho\_{\alpha}\) is centered around x = -2 and y = 0, with contour lines indicating a decrease in probability away from the center. \(\rho\_{\beta}\) is centered around x = 3 and y = 1.5, also with contour lines showing decreasing probability. Several black arrows originate from the vicinity of \(\rho\_{\alpha}\) and point towards \(\rho\_{\beta}\), suggesting a transformation or flow between the two distributions.

**Figure 2.12:** Two Gaussians  $\rho_{\alpha}$  and  $\rho_{\beta}$ , represented using the contour plots of their densities, with respective mean and variance matrices  $\mathbf{m}_{\alpha} = (-2,0), \mathbf{\Sigma}_{\alpha} = \frac{1}{2} \left(1 - \frac{1}{2}; -\frac{1}{2}1\right)$  and  $\mathbf{m}_{\beta} = (3,1), \mathbf{\Sigma}_{\beta} =$  $(2, \frac{1}{2}, \frac{1}{2}, 1)$ . The arrows originate at random points *x* taken on the plane and end at the corresponding mappings of those points  $T(x) = \mathbf{m}_{\beta} + A(x - \mathbf{m}_{\alpha}).$ 

$$
\rho_{\alpha}(x) = \frac{1}{\sqrt{\det(\mathbf{A})}} h(\langle x - \mathbf{m}_{\alpha}, \mathbf{A}^{-1}(x - \mathbf{m}_{\alpha}) \rangle)
$$
$$
\rho_{\beta}(x) = \frac{1}{\sqrt{\det(\mathbf{B})}} h(\langle x - \mathbf{m}_{\beta}, \mathbf{B}^{-1}(x - \mathbf{m}_{\beta}) \rangle),
$$

for the same nonnegative valued function *h* such that the integral

$$
\int_{\mathbb{R}^d} h(\langle x, x \rangle) dx = 1,
$$

then their optimal transport map is also the linear map (2.40) and their Wasserstein distance is also given by the expression  $(2.41)$ , with a slightly different scaling of the Bures metric that depends only the generator function *h*. For instance, that scaling is 1 for Gaussians  $(h(t) = e^{-t/2})$  and  $1/(d+2)$  for uniform distributions on ellipsoids  $(h$  the indicator function for  $[0, 1]$ ). This result follows from the fact that the covariance matrix of an elliptic distribution is a constant times its positive definite parameter [Gómez et al., 2003, Theo. 4(ii)] and that the Wasserstein distance between elliptic distributions is a function of the Bures distance between their covariance matrices [Gelbrich, 1990, Cor. 2.5].

Image /page/33/Figure/1 description: A 3D plot shows a surface representing the function psi(x) = 1/2(x - m\_alpha)^T A(x - m\_alpha) + m\_beta^T x. Below the surface, two probability density functions are plotted on a 2D plane. The first, labeled rho\_alpha, is a narrow, peaked Gaussian distribution. The second, labeled rho\_beta, is a wider, flatter distribution. The surface itself is a curved plane, colored in shades of blue and purple, with its shape influenced by the underlying probability distributions.

**Figure 2.13:** Same Gaussians  $\rho_{\alpha}$  and  $\rho_{\beta}$  as defined in Figure 2.12, represented this time as surfaces. The surface above is the Brenier potential  $\psi$  defined up to an additive constant (here +50) such that  $T = \nabla \psi$ . For visual purposes, both Gaussian densities have been multiplied by a factor of 100.

Image /page/33/Figure/3 description: The image displays two plots side-by-side. The left plot shows a series of bell-shaped curves, each colored with a gradient from red to purple, with the peaks of the curves shifting to the right and their heights decreasing as the color transitions from red to purple. The right plot is a scatter plot with an upward sloping line connecting several colored dots. The x-axis is labeled 'm', and the y-axis is labeled with the Greek letter sigma ('σ'). The dots on the scatter plot also follow a color gradient from red to blue, mirroring the color progression in the left plot.

**Figure 2.14:** Computation of displacement interpolation between two 1-D Gaussians. Denoting  $\mathcal{G}_{m,\sigma}(x)$  $\overset{\text{def.}}{=}$  $=\frac{1}{\sqrt{2\pi s}}e^{-\frac{(x-m)^2}{2s^2}}$  the Gaussian density, it thus shows the interpolation  $\mathcal{G}_{(1-t)m_0+tm_1,(1-t)\sigma_0+t\sigma_1}.$
# 6

# W<sup>1</sup> Optimal Transport

This chapter focuses on optimal transport problems in which the ground cost is equal to a distance. Historically, this corresponds to the original problem posed by <PERSON><PERSON> in 1781; this setting was also that chosen in early applications of optimal transport in computer vision [<PERSON><PERSON><PERSON> et al., 2000] under the name of "earth mover's distances".

Unlike the case where the ground cost is a *squared* Hilbertian distance (studied in particular in Chapter 7), transport problems where the cost is a metric are more difficult to analyze theoretically. In contrast to Remark 2.24 that states the uniqueness of a transport map or coupling between two absolutely continuous measures when using a squared metric, the optimal Kantorovich coupling is in general not unique when the cost is the ground distance itself. Hence, in this regime it is often impossible to recover a uniquely defined Monge map, making this class of problems ill-suited for interpolation of measures. We refer to works by <PERSON><PERSON><PERSON> and <PERSON> [2001], <PERSON><PERSON> et al. [2002], <PERSON><PERSON><PERSON> [1979], <PERSON> and <PERSON> [1999] for proofs of existence of optimal  $W_1$  transportation plans and detailed analyses of their geometric structure.

Although more difficult to analyze in theory, optimal transport with a linear ground distance is usually more robust to outliers and noise than a quadratic cost. Furthermore, a cost that is a metric results in an elegant dual reformulation involving local flow, divergence constraints, or Lipschitzness of the dual potential, suggesting cheaper numerical algorithms that align with *minimum-cost flow* methods over networks in graph theory. This setting is also popular because the associated OT distances define a norm that can compare arbitrary distributions, even if they are not positive; this property is shared by a larger class of so-called *dual norms* (see §8.2 and Remark 10.6 for more details).

## 6.1 W<sup>1</sup> on Metric Spaces

Here we assume that *d* is a distance on  $\mathcal{X} = \mathcal{Y}$ , and we solve the OT problem with the ground cost  $c(x, y) = d(x, y)$ . The following proposition highlights key properties of the *c*-transform (5.1) in this setup. In the following, we denote the Lipschitz constant of a function  $f \in \mathcal{C}(\mathcal{X})$  as

$$
\operatorname{Lip}(f) \stackrel{\text{\tiny def.}}{=} \sup \left\{ \frac{|f(x) - f(y)|}{d(x, y)} \ : \ (x, y) \in \mathcal{X}^2, x \neq y \right\}.
$$

We define Lipschitz functions to be those functions *f* satisfying  $Lip(f) < +\infty$ ; they form a convex subset of  $\mathcal{C}(\mathcal{X})$ .

**Proposition 6.1.** Suppose  $\mathcal{X} = \mathcal{Y}$  and  $c(x, y) = d(x, y)$ . Then, there exists *g* such that  $f = g^c$  if and only  $\text{Lip}(f) \leq 1$ . Furthermore, if  $\text{Lip}(f) \leq 1$ , then  $f^c = -f$ .

*Proof.* First, suppose  $f = g^c$ . Then, for  $x, y \in \mathcal{X}$ ,

$$
|f(x) - f(y)| = | \inf_{z \in X} d(x, z) - g(z) - \inf_{z \in X} d(y, z) - g(z) |
$$

$$
\leq \sup_{z \in X} |d(x, z) - d(y, z)| \leq d(x, y).
$$

The first equality follows from the definition of  $g^c$ , the next inequality from the identity  $|\inf f - \inf g| \leq \sup |f - g|$ , and the last from the triangle inequality. This shows that  $\text{Lip}(f) \leq 1.$ 

Now, suppose  $\text{Lip}(f) \leq 1$ , and define  $g \stackrel{\text{def.}}{=} -f$ . By the Lipschitz property, for all  $x, y \in \mathcal{X}, f(y) - d(x, y) \leq f(x) \leq f(y) + d(x, y)$ . Applying these inequalities,

$$
g^{c}(y) = \inf_{x \in \mathcal{X}} [d(x, y) + f(x)] \ge \inf_{x \in \mathcal{X}} [d(x, y) + f(y) - d(x, y)] = f(y),
$$
  
$$
g^{c}(y) = \inf_{x \in \mathcal{X}} [d(x, y) + f(x)] \le \inf_{x \in \mathcal{X}} [d(x, y) + f(y) + d(x, y)] = f(y).
$$

Hence,  $f = g^c$  with  $g = -f$ . Using the same inequalities shows

$$
f^{c}(y) = \inf_{x \in \mathcal{X}} [d(x, y) - f(x)] \ge \inf_{x \in \mathcal{X}} [d(x, y) - f(y) - d(x, y)] = -f(y),
$$
  
$$
f^{c}(y) = \inf_{x \in \mathcal{X}} [d(x, y) - f(x)] \le \inf_{x \in \mathcal{X}} [d(x, y) - f(y) + d(x, y)] = -f(y).
$$

This shows  $f^c = -f$ .

Starting from the single potential formulation (5.4), one can iterate the construction and replace the couple  $(g, g^c)$  by  $(g^c, (g^c)^c)$ . The last proposition shows that one can thus use  $(g^c, -g^c)$ , which in turn is equivalent to any pair  $(f, -f)$  such that  $\text{Lip}(f) \leq 1$ . This leads to the following alternative expression for the  $\mathcal{W}_1$  distance:

$$
\mathcal{W}_1(\alpha, \beta) = \max_{f} \left\{ \int_{\mathcal{X}} f(x) (\mathrm{d}\alpha(x) - \mathrm{d}\beta(x)) \; : \; \mathrm{Lip}(f) \le 1 \right\}.
$$
 (6.1)

 $\Box$ 

This expression shows that  $W_1$  is actually a norm, *i.e.*  $W_1(\alpha, \beta) = ||\alpha - \beta||_{W_1}$ , and that it is still valid for any measures (not necessary positive) as long as  $\int_{\mathcal{X}} \alpha = \int_{\mathcal{X}} \beta$ . This norm is often called the Kantorovich and Rubinstein norm [1958].

For discrete measures of the form (2.1), writing  $\alpha - \beta = \sum_{k} \mathbf{m}_k \delta_{z_k}$  with  $z_k \in \mathcal{X}$  and  $\sum_{k} \mathbf{m}_k = 0$ , the optimization (6.1) can be rewritten as

$$
\mathcal{W}_1(\alpha, \beta) = \max_{(\mathbf{f}_k)_k} \left\{ \sum_k \mathbf{f}_k \mathbf{m}_k \ : \ \forall \ (k, \ell), |\mathbf{f}_k - \mathbf{f}_\ell| \leq d(z_k, z_\ell), \right\} \tag{6.2}
$$

which is a finite-dimensional convex program with quadratic-cone constraints. It can be solved using interior point methods or, as we detail next for a similar problem, using proximal methods.

When using  $d(x, y) = |x - y|$  with  $\mathcal{X} = \mathbb{R}$ , we can reduce the number of constraints by ordering the  $z_k$ 's via  $z_1 \leq z_2 \leq \ldots$  In this case, we only have to solve

$$
\mathcal{W}_1(\alpha,\beta)=\max_{(\mathbf{f}_k)_k}\left\{\sum_k \mathbf{f}_k \mathbf{m}_k\;:\; \forall\, k, |\mathbf{f}_{k+1}-\mathbf{f}_k|\leq z_{k+1}-z_k\right\},\,
$$

which is a linear program. Note that furthermore, in this 1-D case, a closed form expression for  $W_1$  using cumulative functions is given in (2.37).

**Remark 6.1** ( $W_p$  with  $0 < p \le 1$ ). If  $0 < p \le 1$ , then  $\tilde{d}(x, y) \stackrel{\text{def.}}{=} d(x, y)^p$  satisfies the triangular inequality, and hence  $\tilde{d}$  is itself a distance. One can thus apply the results and algorithms detailed above for  $W_1$  to compute  $W_p$  by simply using  $\tilde{d}$  in place of  $d$ . This is equivalent to stating that  $\mathcal{W}_p$  is the dual of *p*-Hölder functions  $\{f : \text{Lip}_p(f) \leq 1\},\$ where

$$
\mathrm{Lip}_p(f) \stackrel{\text{\tiny def.}}{=} \sup \left\{ \frac{|f(x) - f(y)|}{d(x, y)^p} \ : \ (x, y) \in \mathcal{X}^2, x \neq y \right\}.
$$

## 6.2 W<sup>1</sup> on Euclidean Spaces

In the special case of Euclidean spaces  $\mathcal{X} = \mathcal{Y} = \mathbb{R}^d$ , using  $c(x, y) = ||x - y||$ , the global Lipschitz constraint appearing in (6.1) can be made local as a uniform bound on the gradient of *f*,

$$
\mathcal{W}_1(\alpha, \beta) = \max_{f} \left\{ \int_{\mathbb{R}^d} f(x) (\mathrm{d}\alpha(x) - \mathrm{d}\beta(x)) \; : \; \|\nabla f\|_{\infty} \le 1 \right\}.
$$
 (6.3)

Here the constraint  $\|\nabla f\|_{\infty} \leq 1$  signifies that the norm of the gradient of *f* at any point *x* is upper bounded by 1,  $\|\nabla f(x)\|_2 \leq 1$  for any *x*.

Considering the dual problem to (6.3), one obtains an optimization problem under fixed divergence constraint

$$
\mathcal{W}_1(\alpha, \beta) = \min_s \left\{ \int_{\mathbb{R}^d} ||s(x)||_2 \, \mathrm{d}x \; : \; \mathrm{div}(s) = \alpha - \beta \right\},\tag{6.4}
$$

which is often called the Beckmann formulation [Beckmann, 1952]. Here the vectorial function  $s(x) \in \mathbb{R}^2$  can be interpreted as a flow field, describing locally the movement of mass. Outside the support of the two input measures,  $div(s) = 0$ , which is the conservation of mass constraint. Once properly discretized using finite elements, Problems (6.3) and (6.4) become nonsmooth convex optimization problems. It is possible to use an off-the-shelf interior points quadratic-cone optimization solver, but as advocated in §7.3, large-scale problems require the use of simpler but more adapted first order methods. One can thus use, for instance, Douglas–Rachford (DR) iterations (7.14) or the related alternating direction method of multipliers method. Note that on a uniform grid, projecting on the divergence constraint is conveniently handled using the fast Fourier transform. We refer to Solomon et al. [2014a] for a detailed account for these approaches and application to OT on triangulated meshes. See also Li et al. [2018a], Ryu et al. [2017b,a] for similar approaches using primal-dual splitting schemes. Approximation schemes that relax the Lipschitz constraint on the dual potentials *f* have also been proposed, using, for instance, a constraint on wavelet coefficients leading to an explicit formula [Shirdhonkar and Jacobs, 2008], or by considering only functions *f* parameterized as multilayer neural networks with "rectified linear"  $max(0, \cdot)$  activation function and clipped weights [Arjovsky et al., 2017].

## 6.3 W<sub>1</sub> on a Graph

The previous formulations (6.3) and (6.4) of  $W_1$  can be generalized to the setting where X is a geodesic space, *i.e.*  $c(x, y) = d(x, y)$  where d is a geodesic distance. We refer to Feldman and McCann [2002] for a theoretical analysis in the case where  $\mathcal X$  is a Riemannian manifold. When  $\mathcal{X} = [\![1,n]\!]$  is a discrete set, equipped with undirected edges  $(i, j) \in \mathcal{E} \subset \mathcal{X}^2$  labeled with a weight (length)  $\mathbf{w}_{i,j}$ , we recover the important case where  $\mathcal X$  is a graph equipped with the geodesic distance (or shortest path metric):

$$
\mathbf{D}_{i,j} \stackrel{\text{def.}}{=} \min_{K \ge 0, (i_k)_k : i \to j} \left\{ \sum_{k=1}^{K-1} \mathbf{w}_{i_k, i_{k+1}} \; : \; \forall \, k \in [\![ 1, K-1 ]\!], (i_k, i_{k+1}) \in \mathcal{E} \right\},
$$

where  $i \rightarrow j$  indicates that  $i_1 = i$  and  $i_K = j$ , namely that the path starts at *i* and ends at *j*.

We consider two vectors  $(\mathbf{a}, \mathbf{b}) \in (\mathbb{R}^n)^2$  defining (signed) discrete measures on the graph X such that  $\sum_i \mathbf{a}_i = \sum_i \mathbf{b}_i$  (these weights do not need to be positive). The goal is now to compute  $W_1(a, b)$ , as introduced in (2.17) for  $p = 1$ , when the ground metric is the graph geodesic distance. This computation should be carried out without going as far as having to compute a "full" coupling **P** of size  $n \times n$ , to rely instead on local operators thanks to the underlying connectivity of the graph. These operators are discrete formulations for the gradient and divergence differential operators.

A discrete dual Kantorovich potential  $f \in \mathbb{R}^n$  is a vector indexed by all vertices of the graph. The gradient operator  $\nabla : \mathbb{R}^n \to \mathbb{R}^\mathcal{E}$  is defined as

$$
\forall (i,j) \in \mathcal{E}, \quad (\nabla \mathbf{f})_{i,j} \stackrel{\text{def.}}{=} \mathbf{f}_i - \mathbf{f}_j.
$$

A flow  $\mathbf{s} = (\mathbf{s}_{i,j})_{i,j}$  is defined on edges, and the divergence operator div:  $\mathbb{R}^{\mathcal{E}} \to \mathbb{R}^n$ , which is the adjoint of the gradient  $\nabla$ , maps flows to vectors defined on vertices and is defined as

$$
\forall i \in [\![1,n]\!], \quad \text{div}(\mathbf{s})_i \stackrel{\text{def.}}{=} \sum_{j:(i,j) \in \mathcal{E}} (\mathbf{s}_{i,j} - \mathbf{s}_{j,i}) \in \mathbb{R}^n.
$$

Problem (6.3) becomes, in the graph setting,

$$
W_1(\mathbf{a}, \mathbf{b}) = \max_{\mathbf{f} \in \mathbb{R}^n} \left\{ \sum_{i=1}^n \mathbf{f}_i(\mathbf{a}_i - \mathbf{b}_i) \ : \ \forall (i, j) \in \mathcal{E}, \ |(\nabla \mathbf{f})_{i,j}| \leq \mathbf{w}_{i,j} \right\}.
$$
 (6.5)

The associated dual problem, which is analogous to Formula (6.4), is then

$$
W_1(\mathbf{a}, \mathbf{b}) = \min_{\mathbf{s} \in \mathbb{R}_+^{\mathcal{E}}} \left\{ \sum_{(i,j) \in \mathcal{E}} \mathbf{w}_{i,j} \mathbf{s}_{i,j} \ : \ \text{div}(\mathbf{s}) = \mathbf{a} - \mathbf{b} \right\}.
$$
 (6.6)

This is a linear program and more precisely an instance of min-cost flow problems. Highly efficient dedicated simplex solvers have been devised to solve it; see, for instance, [Ling and Okada, 2007]. Figure 6.1 shows an example of primal and dual solutions. Formulation (6.6) is the so-called Beckmann formulation [Beckmann, 1952] and has been used and extended to define and study traffic congestion models; see, for instance, [Carlier et al., 2008].

Image /page/5/Figure/1 description: The image displays two related diagrams, labeled 'f' and '(a, b) and s'. The diagram labeled 'f' shows a network of points connected by thin green and black lines. The points are colored red, purple, and blue, with a gradient from red to purple to blue as they move across the network. The diagram labeled '(a, b) and s' shows a similar network of points and lines, but here the lines are predominantly thin black lines, with some thicker black lines forming distinct paths. The points are colored red and blue, with the red points concentrated on the left and the blue points on the right. The thicker black lines appear to highlight specific routes or connections within the network.

**Figure 6.1:** Example of computation of  $W_1(a, b)$  on a planar graph with uniform weights  $w_{i,j} = 1$ . Left: potential **f** solution of  $(6.5)$  (increasing value from red to blue). The green color of the edges is proportional to  $|(\nabla \mathbf{f})_{i,j}|$ . Right: flow **s** solution of (6.6), where bold black edges display nonzero  $\mathbf{s}_{i,j}$ , which saturate to  $\mathbf{w}_{i,j} = 1$ . These saturating flow edge on the right match the light green edge on the left where  $|(\nabla \mathbf{f})_{i,j}| = 1$ .
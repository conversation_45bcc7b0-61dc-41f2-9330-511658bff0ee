# **Data Distillation: A Survey**

**Noveen Sachdeva** *<EMAIL> Computer Science & Engineering University of California, San Diego*

**<PERSON>** *<EMAIL> Computer Science & Engineering University of California, San Diego*

**Reviewed on OpenReview:** *[https: // openreview. net/ forum? id= lmXMXP74TO](https://openreview.net/forum?id=lmXMXP74TO)*

## **Abstract**

The popularity of deep learning has led to the curation of a vast number of massive and multifarious datasets. Despite having close-to-human performance on individual tasks, training parameter-hungry models on large datasets poses multi-faceted problems such as (a) high model-training time; (b) slow research iteration; and (c) poor eco-sustainability. As an alternative, *data distillation* approaches aim to synthesize terse data summaries, which can serve as effective drop-in replacements of the original dataset for scenarios like model training, inference, architecture search, *etc.* In this survey, we present a formal framework for data distillation, along with providing a detailed taxonomy of existing approaches. Additionally, we cover data distillation approaches for different data modalities, namely images, graphs, and user-item interactions (recommender systems), while also identifying current challenges and future research directions.

# **1 Introduction**

<span id="page-0-0"></span>**(Loose) Definition 1. (Data distillation)** *Approaches that aim to synthesize tiny and high-fidelity data summaries which distill the most important knowledge from a given target dataset. Such distilled summaries are optimized to serve as effective drop-in replacements of the original dataset for efficient and accurate data-usage applications like model training, inference, architecture search, etc.*

The recent "scale-is-everything" viewpoint [\(Ghorbani et al.,](#page-15-0) [2021;](#page-15-0) [Hoffmann et al.,](#page-15-1) [2022;](#page-15-1) [Kaplan et al.,](#page-15-2) [2020\)](#page-15-2), argues that training bigger models (*i.e.*, consisting of a higher number of parameters) on bigger datasets, and using larger computational resources is the sole key for advancing the frontier of artificial intelligence. Such studies observe and hypothesize the generalizability of neural networks as a power-law *w.r.t.* the aforementioned factors, albeit with small exponents. On the other hand, a reasonable argument is that a principled and well-reasoned solution will be more amenable to various scaling-laws, thereby leading to faster progress. Data distillation (Definition [1\)](#page-0-0) is clearly a task rooted in the latter school of thought by introducing the *fidelity of data* as in important covariate in such neural scaling-laws. [Sorscher et al.](#page-18-0) [\(2022\)](#page-18-0) demonstrate this viewpoint analytically by using simple heuristics to prune away data with low measures of signal for model training. Clearly, the scale viewpoint still holds, in that if we keep increasing the amount of data (albeit now compressed and of higher quality), we will observe an improvement in both upstream and downstream generalization, but at a faster rate.

**Motivation.** A terse, high-quality data summary has use cases from a variety of standpoints. First and foremost, it leads to a faster model-training procedure. In turn, faster model training equates to (1) computecost saving and expedited research iterations, *i.e.*, the investigative procedure of manually experimenting different ideas; and (2) improved eco-sustainability, *i.e.*, lowering the amount of compute time directly leads to a lower carbon footprint from running power-hungry accelerated hardware [\(Gupta et al.,](#page-15-3) [2022\)](#page-15-3). Additionally,

*<EMAIL>*

*<EMAIL>*

Image /page/1/Figure/1 description: The image illustrates a data distillation process. On the left, a collection of 50K images is shown. A red curved arrow labeled 'Data Distillation' points from these images to a smaller set of approximately 50K distilled images, arranged in two rows of five. An arrow labeled 'Train' goes from the distilled images to a neural network diagram. Below this, another arrow labeled 'Train' goes from the original 50K images to a similar neural network diagram. A dotted red arrow labeled 'Similar Performance' curves from the output of the first neural network to the input of the second, indicating comparable results. The second neural network is labeled 'Learning algorithm'.

[\[HQ Image Link\]](https://www.noveens.com/images/dd_survey/overview.pdf) Figure 1: The premise of data distillation demonstrated using an image dataset.

a small data summary democratizes the entire pipeline, as more people can train state-of-the-art algorithms on reasonably accessible hardware using the data summary. Finally, a high-quality data summary indirectly also accelerates orthogonal procedures like neural architecture search [\(Liu et al.,](#page-16-0) [2019\)](#page-16-0), approximate nearest neighbour search [\(Arya et al.,](#page-13-0) [1998\)](#page-13-0), knowledge distillation [\(Hinton et al.,](#page-15-4) [2015\)](#page-15-4), *etc.*, where the procedure needs to iterate over the entire dataset multiple times.

**Comparison with data pruning.** Another reasonable avenue for summarizing large datasets is *pruning* away *low-quality* data which presumably does not carry large amount of *signal* to be captured during modeltraining. The primary challenge for such data pruning approaches (*a.k.a.* coreset construction) lies in tagging the *hardness* of each data-point which can be used for subsequent pruning (typically in a greedy fashion). Prominent data-pruning approaches propose heuristics for the same, relying on concepts such as shapley values [\(Ghorbani & Zou,](#page-15-5) [2019\)](#page-15-5), confidence scores [\(Coleman et al.,](#page-14-0) [2020\)](#page-14-0), error-contribution [\(Toneva et al.,](#page-18-1) [2019\)](#page-18-1), feature-space geometry [\(Abbas et al.,](#page-13-1) [2023;](#page-13-1) [Sorscher et al.,](#page-18-0) [2022;](#page-18-0) [Welling,](#page-19-0) [2009\)](#page-19-0), *etc.* Another line of work builds on the advances in submodular optimization (see [Bilmes](#page-13-2) [\(2022\)](#page-13-2) for a review) to approximately solve the NP-Hard combinatorial optimization of selecting the subset that maximizes a set-level *goodness* function, when such goodness functions are provably submodular [\(Killamsetty et al.,](#page-16-1) [2021;](#page-16-1) [Mirzasoleiman](#page-17-0) [et al.,](#page-17-0) [2020;](#page-17-0) [S et al.,](#page-17-1) [2021\)](#page-17-1). Notably, such data pruning methodologies inherently share the same goal as data distillation but are severely restricted due to only retaining data already in the target dataset, thereby leading to finite expressivity and hence, generally, lower sample-fidelity (see [Ayed & Hayou](#page-13-3) [\(2023\)](#page-13-3) for a deeper theoretical outlook on the fundamental limitations of data pruning). Further, recent empirical studies of data pruning methodologies [\(Guo et al.,](#page-15-6) [2022\)](#page-15-6) demonstrate that the efficacy of such data pruning heuristics rarely and irregularly transfers to practical scenarios, with random downsampling being a hard baseline.

**Comparison with knowledge distillation & transfer learning.** Despite inherently distilling some notion of *knowledge*, we would like to highlight both *knowledge distillation* and *transfer learning* are orthogonal procedures to data distillation, and can potentially work in conjunction to perform both tasks more efficiently. More specifically, knowledge distillation [\(Hinton et al.,](#page-15-4) [2015\)](#page-15-4) entails distilling the knowledge from a trained teacher network into a smaller student network. On the other hand, transfer learning [\(Pratt,](#page-17-2) [1992\)](#page-17-2) focuses on transferring knowledge across similar tasks, *e.g.*, from image classification to image segmentation. Orthogonally, data distillation aims to distill the knowledge from a given dataset into a terse data summary. Such data summaries can be used *in conjunction* with knowledge distillation or transfer learning procedures for both (1) faster learning of the teacher models; and (2) faster knowledge transfer to the student models. The same holds true for model compression techniques [\(LeCun et al.,](#page-16-2) [1989\)](#page-16-2), where similar to knowledge distillation, the goal is to reduce model storage size rather than reducing the training time or increasing the sample-fidelity.

*In this survey*, we intend to provide a succinct overview of various data distillation frameworks across different data modalities. We start by presenting a formal data distillation framework in Section [2,](#page-2-0) and present technicalities of various existing techniques. We classify all data distillation techniques into four categories (see Figure [2](#page-3-0) for a taxonomy) and provide a detailed empirical comparison of image distillation techniques in Table [1.](#page-8-0) Subsequently, in Section [3,](#page-9-0) we discuss existing data distillation frameworks for synthesizing data of different modalities, as well as outlining the associated challenges. In Section [4,](#page-11-0) we discuss alternative applications of synthesizing a high-fidelity data summary rather than simply accelerating model training along with pointers to existing work. Finally, in Section [5,](#page-12-0) we conclude by presenting common pitfalls in existing data distillation techniques, along with proposing interesting directions for future work.

<span id="page-2-0"></span>

# **2 The Data Distillation Framework**

Before going into the specifics of data distillation, we start by outlining useful notation. Let  $\mathcal{D} \triangleq \{(x_i, y_i)\}_{i=1}^{|\mathcal{D}|}$ *i*=1 be a given dataset which needs to be distilled, where  $x_i \in \mathcal{X}$  are the set of input features, and  $y_i \in \mathcal{Y}$  is the desired label for  $x_i$ . For classification tasks, let C be the set of unique classes in  $\mathcal{Y}$ , and  $\mathcal{D}^c \triangleq \{(x_i, y_i) \mid y_i =$  $c \}^{|\mathcal{D}|}_{i=1}$  be the subset of D with class c. We also define the matrices  $\mathbf{X} \triangleq [x_i]_{i=1}^{|\mathcal{D}|}$  and  $\mathbf{Y} \triangleq [y_i]_{i=1}^{|\mathcal{D}|}$  for convenience. Given a data budget  $n \in \mathbb{Z}^+$ , data distillation techniques aim to synthesize a high-fidelity data summary  $\mathcal{D}_{syn} \triangleq \{(\tilde{x}_i, \tilde{y}_i)\}_{i=1}^n$  such that  $n \ll |\mathcal{D}|$ . We define  $\mathcal{D}_{syn}^c$ ,  $\mathbf{X}_{syn}$ , and  $\mathbf{Y}_{syn}$  similarly as defined for  $\mathcal{D}$ . Let  $\Phi_{\theta}: \mathcal{X} \mapsto \mathcal{Y}$  represent a learning algorithm parameterized by  $\theta$ . We also assume access to a twice-differentiable cost function  $l : \mathcal{Y} \times \mathcal{Y} \mapsto \mathbb{R}$ , and define  $\mathcal{L}_{\mathcal{D}}(\theta) \triangleq \mathbb{E}_{(x,y)\sim\mathcal{D}}[l(\Phi_{\theta}(x),y)]$  for convenience. Notation is also summarized in Appendix [A.](#page-20-0) Notably, since  $\mathcal D$  and  $\mathcal D_{syn}$  share the same data domain  $(\mathcal X)$ , under reasonable systems' assumptions, training  $\Phi$  using gradient descent (GD) on  $\mathcal{D}_{syn}$  will have a  $\frac{|\mathcal{D}|}{n} \times$  training-time speedup compared to training  $\Phi$  on  $\mathcal{D}$ .

For the sake of uniformity, we refer to the data synthesized by data distillation techniques as a *data summary* henceforth. Inspired by the definition of coresets [\(Bachem et al.,](#page-13-4) [2017\)](#page-13-4), we formally define an *ϵ*−approximate data summary, and the data distillation task as follows:

 $\bf{Definition 2.}$   $($  $\epsilon$  $\bf{-approximate\ data\ summary}$  $)$   $Given\ a\ learning\ algorithm\ \Phi,$   $let\ \theta^{\mathcal{D}},$   $\theta^{\mathcal{D}_{syn}}$   $represent$ *the optimal set of parameters for*  $\Phi$  *estimated on*  $\mathcal{D}$  *and*  $\mathcal{D}_{syn}$ *, and*  $\epsilon \in \mathbb{R}^+$ *; we define an*  $\epsilon$ -*approximate data summary as one which satisfies:*

$$
\sup \{ |l(\Phi_{\theta^{\mathcal{D}}}(x), y) - l(\Phi_{\theta^{\mathcal{D}_{\text{syn}}}}(x), y) | \}_{y \sim \mathcal{Y}} \le \epsilon \tag{1}
$$

<span id="page-2-1"></span>**Definition 3. (Data distillation)** *Given a learning algorithm*  $\Phi$ *, let*  $\theta^{\mathcal{D}}$ *,*  $\theta^{\mathcal{D}_{syn}}$  *represent the optimal set of parameters for* Φ *estimated on* D *and* Dsyn*; we define data distillation as optimizing the following:*

<span id="page-2-2"></span>
$$
\underset{\mathcal{D}_{syn},n}{\arg\min} \left( \sup \left\{ \left| l\left(\Phi_{\theta^{\mathcal{D}}}(x),y\right) - l\left(\Phi_{\theta^{\mathcal{D}_{syn}}}(x),y\right) \right| \right.\right)_{\substack{x\sim\mathcal{X}\\y\sim\mathcal{Y}}} \right) \tag{2}
$$

From Definition [3,](#page-2-1) we highlight three cornerstones of evaluating data distillation methods: (1) Performance: downstream evaluation of models trained on the synthesized data summary *vs.* the full dataset (*e.g.*, accuracy, FID, nDCG, *etc.*); (2) Efficiency: how quickly can models reach full-data performance (or even exceed it), *i.e.*, the scaling of *n vs.* downstream task-performance; and (3) Transferability: how well can data summaries generalize to a diverse pool of learning algorithms, in terms of downstream evaluation.

**No free lunch.** The universal "No Free Lunch" theorem [\(Wolpert & Macready,](#page-19-1) [1997\)](#page-19-1) applies to data distillation as well. For example, looking at the transferability of a data summary, it is strongly dependent on the set of encoded inductive biases, *i.e.*, through the choice of the learning algorithm  $\Phi$  used while distilling, as well as the objective function  $l(\cdot, \cdot)$ . Such biases are unavoidable for any data distillation technique, in a sense that learning algorithms closely following the set of encoded inductive biases, will be able to generalize better on the data summary than others.

Keeping these preliminaries in mind, we now present a formal framework for data distillation, encapsulating existing data distillation approaches. Notably, the majority of existing techniques intrinsically solve a bilevel optimization problem, which are tractable surrogates of Equation [\(2\)](#page-2-2). The inner-loop typically optimizes a representative learning algorithm on the data summary, and using the optimized learning algorithm, the outer-loop optimizes a tractable proxy of Equation [\(2\)](#page-2-2).

<span id="page-3-0"></span>Image /page/3/Figure/1 description: This is a flowchart illustrating different types of data distillation. At the top, 'Data Distillation' is the main topic. Below this, there are three categories: 'Image Distillation', 'RecSys Distillation', and 'Graph Distillation'. Branching down from these categories are four main types of matching techniques: 'Meta-Model Matching', 'Gradient Matching', 'Trajectory Matching', and 'Distribution Matching'. Each matching type lists specific methods associated with it. 'Meta-Model Matching' includes DD, KIP, RFAD, FREPO, LINBA, and DISTILL-CF. 'Gradient Matching' includes DC, DSA, DCC, IDC, GCOND, and DosCOND. 'Trajectory Matching' includes MTT, HABA, and TESLA. 'Distribution Matching' includes DM, CAFE, IT-GAN, KFS, and GCDM. Each of these sections is also labeled with a corresponding section number, from Section 2.1 to Section 2.4.

[\[HQ Image Link\]](https://www.noveens.com/images/dd_survey/taxonomy.pdf) Figure 2: A taxonomy of existing data distillation approaches.

Some common assumptions that existing data distillation techniques follow are: (1) static-length data summary, *i.e.*, *n* is fixed and is treated as a tunable hyper-parameter; and (2) we have on-demand access to the target dataset D which is also assumed to be iid. Notably, the outer-loop optimization of  $\mathcal{D}_{syn}$  happens simply through GD on the analogously defined  $\mathbf{X}_{syn} \in \mathbb{R}^{n \times \dim(\mathcal{X})}$ , which is instantiated as free parameters. Note that the labels,  $\mathbf{Y}_{syn} \in \mathbb{R}^{n \times \dim(\mathcal{Y})}$ , can be similarly optimized through GD as well [\(Bohdal et al.,](#page-13-5) [2020\)](#page-13-5). For the sake of notational clarity, we will interchangeably use optimization of  $\mathcal{D}_{syn}$  *or* ( $\mathbf{X}_{syn}$ ,  $\mathbf{Y}_{syn}$ ) henceforth.

<span id="page-3-1"></span>

## **2.1 Data Distillation by Meta-model Matching**

Meta-model matching-based data distillation approaches fundamentally optimize for the transferability of models trained on the data summary when generalized to the original dataset:

<span id="page-3-2"></span>
$$
\underset{\mathcal{D}_{syn}}{\arg\min} \quad \mathcal{L}_{\mathcal{D}}\left(\theta^{\mathcal{D}_{syn}}\right) \quad \text{s.t.} \quad \theta^{\mathcal{D}_{syn}} \triangleq \underset{\theta}{\arg\min} \quad \mathcal{L}_{\mathcal{D}_{syn}}(\theta), \tag{3}
$$

where intuitively, the inner-loop trains a representative learning algorithm on the data summary *until convergence*, and the outer-loop subsequently optimizes the data summary for the transferability of the optimized learning algorithm to the original dataset. Besides common assumptions mentioned earlier, the key simplifying assumption for this family of methods is that a perfect classifier exists and can be estimated on D, *i.e.*,  $\exists \theta^{\mathcal{D}}$  s.t.  $l(\Phi_{\theta^{\mathcal{D}}}(x), y) = 0$ ,  $\forall x \sim \mathcal{X}, y \sim \mathcal{Y}$ . Plugging the second assumption along with the iid assumption of  $\mathcal D$  in Equation [\(2\)](#page-2-2) directly translates to Equation [\(3\)](#page-3-2). Despite the assumption, Equation (3) is highly expensive both in terms of computation time and memory, due to which, methods from this family typically resort to making further assumptions.

[Wang et al.](#page-19-2) [\(2018\)](#page-19-2) (DD) originally proposed the task of data distillation, and used the meta-model matching framework for optimization. DD makes the optimization in Equation [\(3\)](#page-3-2) tractable by performing (1) local optimization *à la* stochastic gradient descent (SGD) in the inner-loop, and (2) outer-loop optimization using Truncated Back-Propagation Through Time (TBPTT), *i.e.*, unrolling only a limited number of inner-loop optimization steps. Formally, the modified optimization objective for DD is as follows:

<span id="page-3-3"></span>
$$
\underset{\mathcal{D}_{syn}}{\arg\min} \quad \underset{\theta_0 \sim \mathbf{P}_{\theta}}{\mathbb{E}} \left[ \mathcal{L}_{\mathcal{D}} \left( \theta_T \right) \right] \quad \text{s.t.} \quad \theta_{t+1} \leftarrow \theta_t - \eta \cdot \nabla_{\theta} \mathcal{L}_{\mathcal{D}_{syn}} \left( \theta_t \right), \tag{4}
$$

where **P***<sup>θ</sup>* is a parameter initialization distribution of choice, *T* accounts for the truncation in TBPTT, and *η* is a tunable learning rate. We also elucidate DD's control-flow in Algorithm [1](#page-4-1) for reference.

Notably, TBPTT has been associated with drawbacks such as (1) computationally expensive inner-loop unrolling; (2) bias involved with truncated unrolling [\(Wu et al.,](#page-19-3) [2018\)](#page-19-3); and (3) poorly conditioned loss landscapes, particularly with long unrolls [\(Metz et al.,](#page-17-3) [2019\)](#page-17-3). Consequently, the TBPTT framework was empirically shown to be ineffective for data distillation [\(Zhao et al.,](#page-19-4) [2021\)](#page-19-4). However, recent work [\(Deng](#page-14-1) [& Russakovsky,](#page-14-1) [2022\)](#page-14-1) claims that using momentum-based optimizers and longer inner-loop unrolling can greatly improve performance. We delay a deeper discussion of this work to Section [2.5](#page-7-0) for clarity.

| <b>Algorithm 1:</b> Control-flow of data distillation using naïve meta-matching (Equation $(4)$ ) |
|---------------------------------------------------------------------------------------------------|
|---------------------------------------------------------------------------------------------------|

<span id="page-4-1"></span>**Input:** Target dataset  $\mathcal{D}$ , outer-loop iterations *K*, parameter initialization distribution  $\mathbf{P}_{\theta}$ , inner-loop iterations *T*, inner-loop learning rate *η*, outer-loop learning rate  $\eta_{syn}$  $\mathbf{1}$  **Initialize:**  $(\mathbf{X}_{\mathsf{syn}}^0, \mathbf{Y}_{\mathsf{syn}}^0) \sim \mathcal{D}$ **2** for  $k = 1, ..., K$  do  $\qquad \qquad$  // Outer-loop: optimize  $\mathcal{D}_{syn}$ **3** | Initialize  $θ_0$  ∼ **P**<sub>θ</sub> **4**  $\int$  **for**  $t = 1, ..., T$  **do** // **Inner-loop:** optimize  $\Phi$  on  $\mathcal{D}_{syn}^{k-1}$  $\mathbf{5}$   $\phi_t \leftarrow \theta_{t-1} - \eta \cdot \nabla_{\theta} \mathcal{L}_{\mathcal{D}^{k-1}_{\text{syn}}}(\theta_{t-1})$  $\mathbf{B} \quad \Big| \quad \mathbf{X}^k_{\mathsf{syn}} \leftarrow \mathbf{X}^{k-1}_{\mathsf{syn}}$ syn − *η*syn · ∇**<sup>X</sup>**synLD(*θ<sup>T</sup>* ) // Update **X**syn by computing unrolled meta-gradient  $\mathbf{Y}^k_{\mathsf{syn}} \leftarrow \mathbf{Y}^{k-1}_{\mathsf{syn}}$ syn − *η*syn · ∇**<sup>Y</sup>**synLD(*θ<sup>T</sup>* ) // Update **Y**syn by computing unrolled meta-gradient  $\mathbf{Output:} \ \mathcal{D}^K_{\mathsf{syn}} \equiv (\mathbf{X}^K_{\mathsf{syn}}, \mathbf{Y}^K_{\mathsf{syn}})$ 

Analogously, a separate line of work focuses on using Neural Tangent Kernel (NTK) [\(Jacot et al.,](#page-15-7) [2018\)](#page-15-7) based algorithms to solve the inner-loop in closed form. As a brief side note, the infinite-width correspondence states that performing Kernelized Ridge Regression (KRR) using the NTK of a given neural network, is equivalent to training the same  $\infty$ -width neural network with L2 reconstruction loss for  $\infty$  SGD-steps. These " $\infty$ -width" neural networks have been shown to perform reasonably compared to their finite-width counterparts, while also being solved in closed-form (see [Lee et al.](#page-16-3) [\(2020\)](#page-16-3) for a detailed analysis on finite *vs.* infinite neural networks for image classification). KIP uses the NTK of a fully-connected neural network [\(Nguyen et al.,](#page-17-4) [2021a\)](#page-17-4), or a convolutional network [\(Nguyen et al.,](#page-17-5) [2021b\)](#page-17-5) in the inner-loop of Equation [\(3\)](#page-3-2) for efficient data distillation. More formally, given the NTK  $\mathcal{K} : \mathcal{X} \times \mathcal{X} \mapsto \mathbb{R}$  of a neural network architecture, KIP optimizes the following objective:

<span id="page-4-2"></span>
$$
\underset{\mathbf{X}_{syn}, \mathbf{Y}_{syn}}{\arg \min} \quad \left\| \mathbf{Y} - \mathbf{K}_{\mathbf{X}\mathbf{X}_{syn}} \cdot (\mathbf{K}_{\mathbf{X}_{syn}} \mathbf{X}_{syn} + \lambda I)^{-1} \cdot \mathbf{Y}_{syn} \right\|^2, \tag{5}
$$

where  $\mathbf{K}_{AB} \in \mathbb{R}^{|A|\times|B|}$  represents the gramian matrix of two sets *A* and *B*, and whose  $(i, j)$ <sup>th</sup> element is defined by  $\mathcal{K}(A_i, B_j)$ . Although KIP doesn't impose any additional simplifications to the meta-model matching framework, it has an  $\mathcal{O}(|\mathcal{D}| \cdot n \cdot \dim(\mathcal{X}))$  time and memory complexity, limiting its scalability. Subsequently, RFAD [\(Loo et al.,](#page-16-4) [2022\)](#page-16-4) proposes using (1) the light-weight Empirical Neural Network Gaussian Process (NNGP) kernel [\(Neal,](#page-17-6) [2012\)](#page-17-6) instead of the NTK; and (2) a classification loss (*e.g.*, NLL) instead of the L2-reconstruction loss for the outer-loop to get  $\mathcal{O}(n)$  time complexity while also having better performance. On a similar note, FRePO [\(Zhou et al.,](#page-19-5) [2022b\)](#page-19-5) decouples the feature extractor and a linear classifier in  $\Phi$ , and alternatively optimizes (1) the data summary along with the classifier, and (2) the feature extractor. To be precise, let  $f_{\theta}: \mathcal{X} \mapsto \mathcal{X}'$  be the feature extractor,  $g_{\psi}: \mathcal{X}' \mapsto \mathcal{Y}$  be the linear classifier, s.t.  $\Phi(x) \equiv g_{\psi}(f_{\theta}(x))$   $\forall x \in \mathcal{X}$ ; the optimization objective for FRePO can be written as:

s.t.

$$
\underset{\mathbf{X}_{syn}, \mathbf{Y}_{syn}}{\arg \min} \quad \underset{\theta_0 \sim \mathbf{P}_{\theta}}{\mathbb{E}} \left[ \sum_{t=0}^{T} \left\| \mathbf{Y} - \mathbf{K}_{\mathbf{XX}_{syn}}^{\theta_t} \cdot (\mathbf{K}_{\mathbf{X}_{syn}}^{\theta_t} \mathbf{X}_{syn} + \lambda I)^{-1} \cdot \mathbf{Y}_{syn} \right\|^2 \right]
$$
 $\theta_{t+1} \leftarrow \theta_t - \eta \cdot \underset{(x,y) 
sim \mathcal{D}_{syn}}{\mathbb{E}} \left[ \nabla_{\theta} l(g_{\psi}(f_{\theta}(x)), y) \right] ; \quad \mathbf{K}_{\mathbf{X}_{syn}}^{\theta} \mathbf{X}_{syn} \triangleq f_{\theta_t}(\mathbf{X}_{syn}) f_{\theta_t}(\mathbf{X}_{syn})^T,$ 

$$
(6)
$$

where *T* represents the number of inner-loop update steps for the feature extractor  $f_\theta$ . Notably, (1) a wide architecture for  $f_\theta$  is crucial for distillation quality in FRePO; and (2) despite the bilevel optimization, FRePO is shown to be more scalable compared to KIP (Equation [\(5\)](#page-4-2)), while also being more generalizable.

<span id="page-4-0"></span>

## **2.2 Data Distillation by Gradient Matching**

Gradient matching based data distillation, at a high level, performs one-step distance matching on (1) the network trained on the target dataset (D) *vs.* (2) the same network trained on the data summary ( $\mathcal{D}_{syn}$ ). In contrast to the meta-model matching framework, such an approach circumvents the unrolling of the inner-loop, thereby making the overall optimization much more efficient. First proposed by [Zhao et al.](#page-19-4) [\(2021\)](#page-19-4) (DC), data summaries optimized by gradient-matching significantly outperformed data pruning methodologies, as well as TBPTT-based data distillation proposed by [Wang et al.](#page-19-2) [\(2018\)](#page-19-2). Formally, given a learning algorithm Φ, DC solves the following optimization objective:

<span id="page-5-1"></span>
$$
\underset{\mathcal{D}_{syn}}{\arg\min} \quad \underset{c \sim \mathcal{C}}{\mathbb{E}} \left[ \sum_{t=0}^{T} \mathbf{D} \left( \nabla_{\theta} \mathcal{L}_{\mathcal{D}^c}(\theta_t), \nabla_{\theta} \mathcal{L}_{\mathcal{D}^c_{syn}}(\theta_t) \right) \right] \quad \text{s.t.} \quad \theta_{t+1} \leftarrow \theta_t - \eta \cdot \nabla_{\theta} \mathcal{L}_{\mathcal{D}_{syn}}(\theta_t), \tag{7}
$$

where *T* accounts for model similarity *T*-steps in the future, and  $\mathbf{D} : \mathbb{R}^{|\theta|} \times \mathbb{R}^{|\theta|} \mapsto \mathbb{R}$  is a distance metric of choice (typically cosine distance). In addition to assumptions imposed by the meta-model matching framework (Section [2.1\)](#page-3-1), gradient-matching assumes (1) inner-loop optimization of only *T* steps; (2) local smoothness: two sets of model parameters close to each other (given a distance metric) imply model similarity; and (3) first-order approximation of  $\theta_t^{\mathcal{D}}$ : instead of exactly computing the training trajectory of optimizing  $\theta_0$  on  $\mathcal D$ (say  $\theta_t^D$ ); perform first-order approximation on the optimization trajectory of  $\theta_0$  on the much smaller  $\mathcal{D}_{syn}$  (say *θ*<sup>*D<sub>syn</sub>*</sup>, *i.e.*, approximate *θ*<sup>*D*</sup> as a single gradient-descent update on  $θ_{t-1}^{D_{syn}}$  using *D* rather than  $θ_{t-1}^{D}$  (Figure [3\)](#page-6-1).

Subsequently, numerous other approaches have been built atop this framework with subtle variations. DSA [\(Zhao & Bilen,](#page-19-6) [2021\)](#page-19-6) improves over DC by performing the same image-augmentations (*e.g.*, crop, rotate, jitter, *etc.*) on both  $D$  and  $D_{syn}$  while optimizing Equation [\(7\)](#page-5-1). Since these augmentations are universal and are applicable across data distillation frameworks, augmentations performed by DSA have become a common part of all methods proposed henceforth, but we omit them for notational clarity. DCC [\(Lee et al.,](#page-16-5) [2022b\)](#page-16-5) further modifies the gradient-matching objective to incorporate class contrastive signals inside each gradient-matching step and is shown to improve stability as well as performance. With  $\theta_t$  evolving similarly as in Equation  $(7)$ , the modified optimization objective for DCC can be written as:

$$
\underset{\mathcal{D}_{\text{syn}}}{\arg\min} \quad \underset{\theta_0 \sim \mathbf{P}_{\theta}}{\mathbb{E}} \left[ \sum_{t=0}^T \mathbf{D} \left( \underset{c \in \mathcal{C}}{\mathbb{E}} \left[ \nabla_{\theta} \mathcal{L}_{\mathcal{D}^c}(\theta_t) \right], \underset{c \in \mathcal{C}}{\mathbb{E}} \left[ \nabla_{\theta} \mathcal{L}_{\mathcal{D}_{\text{syn}}^c}(\theta_t) \right] \right) \right]
$$
(8)

Most recently, [Kim et al.](#page-16-6) [\(2022\)](#page-16-6) (IDC) extend the gradient matching framework by: (1) multi-formation: to synthesize a higher amount of data within the same memory budget, store the data summary (*e.g.*, images) in a lower resolution to remove spatial redundancies, and upsample (using *e.g.*, bilinear, FSRCNN [\(Dong](#page-14-2) [et al.,](#page-14-2) [2016\)](#page-14-2)) to the original scale while usage; and (2) matching gradients of the network's training trajectory over the full dataset  $D$  rather than the data summary  $\mathcal{D}_{syn}$ . To be specific, given a  $k \times$  upscaling function  $f: \mathbb{R}^{d \times d} \mapsto \mathbb{R}^{kd \times kd}$ , the modified optimization objective for IDC can be formalized as:

$$
\underset{\mathcal{D}_{syn}}{\arg\min} \quad \underset{c \sim \mathcal{C}}{\mathbb{E}} \left[ \sum_{t=0}^{T} \mathbf{D} \left( \nabla_{\theta} \mathcal{L}_{\mathcal{D}^c}(\theta_t), \nabla_{\theta} \mathcal{L}_{f(\mathcal{D}_{syn}^c)}(\theta_t) \right) \right] \quad \text{s.t.} \quad \theta_{t+1} \leftarrow \theta_t - \eta \cdot \nabla_{\theta} \mathcal{L}_{\mathcal{D}}(\theta_t) \tag{9}
$$

[Kim et al.](#page-16-6) [\(2022\)](#page-16-6) further hypothesize that training models on  $\mathcal{D}_{syn}$  instead of  $\mathcal D$  in the inner-loop has two major drawbacks: (1) strong coupling of the inner- and outer-loop resulting in a chicken-egg problem [\(McLachlan &](#page-17-7) [Krishnan,](#page-17-7) [2007\)](#page-17-7); and (2) vanishing network gradients due to the small size of  $\mathcal{D}_{syn}$ , leading to an improper outer-loop optimization for gradient-matching based techniques.

<span id="page-5-0"></span>

## **2.3 Data Distillation by Trajectory Matching**

[Cazenavette et al.](#page-14-3) [\(2022\)](#page-14-3) proposed MTT which aims to match the training trajectories of models trained on D *vs.*  $\mathcal{D}_{syn}$ . More specifically, let  $\{\theta_t^{\mathcal{D}}\}_{t=0}^T$  represent the training trajectory of training  $\Phi_{\theta}$  on D; trajectory matching algorithms aim to solve the following optimization:

<span id="page-5-2"></span>
$$
\underset{\mathcal{D}_{syn}}{\arg\min} \mathbb{E}\left[\sum_{t=0}^{T-M} \frac{\mathbf{D}\left(\theta_{t+M}^{\mathcal{D}}, \theta_{t+N}^{\mathcal{D}_{syn}}\right)}{\mathbf{D}\left(\theta_{t+M}^{\mathcal{D}}, \theta_{t}^{\mathcal{D}}\right)}\right]
$$
\ns.t.  $\theta_{t+i+1}^{\mathcal{D}_{syn}} \leftarrow \theta_{t+i}^{\mathcal{D}_{syn}} - \eta \cdot \nabla_{\theta} \mathcal{L}_{\mathcal{D}_{syn}}(\theta_{t+i}^{\mathcal{D}_{syn}}) \quad ; \quad \theta_{t+1}^{\mathcal{D}_{syn}} \leftarrow \theta_{t}^{\mathcal{D}} - \eta \cdot \nabla_{\theta} \mathcal{L}_{\mathcal{D}_{syn}}(\theta_{t}^{\mathcal{D}}),$ \n
$$
(10)
$$

where  $\mathbf{D}: \mathbb{R}^{|\theta|} \times \mathbb{R}^{|\theta|} \mapsto \mathbb{R}$  is a distance metric of choice (typically L2 distance). Such an optimization can intuitively be seen as optimizing for similar quality models trained with *N* SGD steps on  $\mathcal{D}_{syn}$ , compared to

<span id="page-6-1"></span>Image /page/6/Figure/1 description: The image depicts a diagram illustrating a process involving updates to datasets D and D\_syn, represented by nodes labeled with theta parameters. The process starts with an initial parameter theta\_0 sampled from P\_theta. There are two main update paths: one for D, shown with blue arrows, and one for D\_syn, shown with orange arrows. Several matching strategies are indicated by dashed arrows: Meta-Model Matching (teal), Gradient Matching (purple), and Trajectory Matching (tan). The diagram also highlights two minimization objectives. The first objective, within a green box, is to update D\_syn by minimizing L\_D(theta\_syn\_t) which is defined as the expectation over D of a loss function l applied to the output of a model with parameters theta\_syn\_t. The second objective, within a peach box, is to minimize a ratio of D values involving parameters from D and D\_syn at different time steps, specifically D(theta\_{t+M}, theta\_{t+N}) / D(theta\_{t+M}, theta\_t), with the condition that M is much greater than N. The diagram shows a progression of parameters over time, with updates leading to new parameter states.

[\[HQ Image Link\]](https://www.noveens.com/images/dd_survey/optimization.pdf) Figure 3: The underlying optimization in various data distillation frameworks.

 $M \gg N$  steps on  $\mathcal{D}$ , thereby invoking long-horizon trajectory matching. Notably, calculating the gradient of Equation [\(10\)](#page-5-2) *w.r.t.*  $\mathcal{D}_{syn}$  encompasses gradient unrolling through *N*-timesteps, thereby limiting the scalability of MTT. On the other hand, since the trajectory of training  $\Phi_{\theta}$  on  $\mathcal{D}$ , *i.e.*,  $\{\theta_t^{\mathcal{D}}\}_{t=0}^T$  is independent of the optimization of  $\mathcal{D}_{syn}$ , it can be pre-computed for various  $\theta_0 \sim \mathbf{P}_{\theta}$  initializations and directly substituted. Similar to gradient matching methods (Section [2.2\)](#page-4-0), the trajectory matching framework also optimizes the first-order distance between parameters, thereby inheriting the local smoothness assumption. As a scalable alternative, [Cui et al.](#page-14-4) [\(2022b\)](#page-14-4) proposed TESLA, which re-parameterizes the parameter-matching loss of MTT in Equation  $(10)$  (specifically when **D** is set as the L2 distance), using linear algebraic manipulations to make the bilevel optimization's memory complexity independent of *N*. Furthermore, TESLA uses learnable soft-labels  $(Y_{syn})$  during the optimization for an increased compression efficiency.

<span id="page-6-0"></span>

## **2.4 Data Distillation by Distribution Matching**

Even though the aforementioned gradient-matching or trajectory-matching based data distillation techniques have been empirically shown to synthesize high-quality data summaries, the underlying bilevel optimization, however, is oftentimes a computationally expensive procedure. To this end, distribution-matching techniques solve a correlated proxy task via a single-level optimization, leading to a vastly improved scalability. More specifically, instead of matching the quality of models on  $\mathcal D$  *vs.*  $\mathcal D_{syn}$ , distribution-matching techniques directly match the distribution of  $\mathcal D$  *vs.*  $\mathcal D_{syn}$ . The key assumption for this family of methods is that two datasets that are similar according to a particular distribution divergence metric, lead to similarly trained models.

First proposed by [Zhao & Bilen](#page-19-7) [\(2023\)](#page-19-7), DM uses (1) numerous parametric encoders to cast high-dimensional data into respective low-dimensional latent spaces; and (2) an approximation of the Maximum Mean Discrepancy to compute the distribution mismatch between  $\mathcal D$  and  $\mathcal D_{syn}$  in each of the latent spaces. More precisely, given a set of *k* encoders  $\mathcal{E} \triangleq \{\psi_i : \mathcal{X} \mapsto \mathcal{X}_i\}_{i=1}^k$ , the optimization objective can be written as:

<span id="page-6-2"></span>
$$
\underset{\mathcal{D}_{syn}}{\arg\min} \quad \underset{c \sim \mathcal{C}}{\mathbb{E}} \left[ \left\| \underset{x \sim \mathcal{D}^c}{\mathbb{E}} \left[ \psi(x) \right] - \underset{x \sim \mathcal{D}_{syn}}{\mathbb{E}} \left[ \psi(x) \right] \right\|^2 \right] \tag{11}
$$

DM uses a set of randomly initialized neural networks (with the same architecture) to instantiate  $\mathcal{E}$ . They observe similar performance when instantiated with more meaningful, task-optimized neural networks, despite it being much less efficient. CAFE [\(Wang et al.,](#page-19-8) [2022\)](#page-19-8) further refines the distribution-matching idea by: (1) solving a bilevel optimization problem for jointly optimizing a *single* encoder (Φ) and the data summary, rather than using a pre-determined *set* of encoders  $(\mathcal{E})$ ; and (2) assuming a neural network encoder  $(\Phi)$ , match the latent representations obtained at all intermediate layers of the encoder instead of only the last layer. Formally, given a  $(L+1)$ -layer neural network  $\Phi_{\theta}: \mathcal{X} \mapsto \mathcal{Y}$  where  $\Phi_{\theta}^l$  represents  $\Phi$ 's output at the  $l^{\text{th}}$ 

layer, the optimization problem for CAFE can be specified as:

$$
\underset{\mathcal{D}_{syn}}{\arg \min} \quad \mathbb{E}_{c \sim \mathcal{C}} \left[ \sum_{l=1}^{L} \left\| \mathbb{E}_{x \sim \mathcal{D}_{c}} \left[ \Phi_{\theta_t}^{l}(x) \right] - \mathbb{E}_{x \sim \mathcal{D}_{syn}^{c}} \left[ \Phi_{\theta_t}^{l}(x) \right] \right\|^2 - \beta \cdot \mathbb{E}_{(x,y) \sim \mathcal{D}^{c}} \left[ \log \hat{p}(y|x, \theta_t) \right] \right]
$$
s.t.

 $\theta_{t+1} \leftarrow \theta_t - \eta \cdot \nabla_{\theta} \mathcal{L}_{\mathcal{D}_{syn}}(\theta_t) \; ; \; \hat{p}(y|x, \theta) \triangleq \text{softmax}_{y} \left( \left\langle \Phi_{\theta}^{L}(x), \mathbb{E}_{x' \sim \mathcal{D}_{syn}^{y}} \left[ \Phi_{\theta}^{L}(x') \right] \right\rangle \right),$ 

$$
(12)
$$

where  $\hat{p}(\cdot|\cdot,\theta)$  intuitively represents the nearest centroid classifier on  $\mathcal{D}_{syn}$  using the latent representations obtained by last layer of Φ*θ*. Analogously, IT-GAN [\(Zhao & Bilen,](#page-19-9) [2022\)](#page-19-9) also uses the distribution-matching framework in Equation [\(11\)](#page-6-2) to generate data that is informative for model training, in contrast to the traditional GAN [\(Goodfellow et al.,](#page-15-8) [2014\)](#page-15-8) which focuses on generating realistic data.

<span id="page-7-0"></span>

## **2.5 Data Distillation by Factorization**

All of the aforementioned data distillation frameworks intrinsically maintain the synthesized data summary as a large set of free parameters, which are in turn optimized. Arguably, such a setup prohibits knowledge sharing between synthesized data points (parameters), which might introduce data redundancy. On the other hand, factorization-based data distillation techniques parameterize the data summary using two separate components: (1) bases: a set of mutually independent base vectors; and (2) hallucinators: a mapping from the bases' vector space to the joint data- and label-space. In turn, both the bases and hallucinators are optimized for the task of data distillation.

Formally, let  $\mathcal{B} \triangleq \{b_i \in \mathbb{B}\}_{i=1}^{|\mathcal{B}|}$  be the set of bases, and  $\mathcal{H} \triangleq \{h_i : \mathbb{B} \mapsto \mathcal{X} \times \mathcal{Y}\}_{i=1}^{|\mathcal{H}|}$  be the set of hallucinators, then the data summary is parameterized as  $\mathcal{D}_{syn} \triangleq \{h(b)\}_{b \sim \mathcal{B}, h \sim \mathcal{H}}$ . Even though such a two-pronged approach seems similar to generative modeling of data, note that unlike classic generative models, (1) the input space consists *only of* a fixed and optimized set of latent codes and isn't meant to take any other inputs; and (2) given a specific B and H, we can generate at most  $|\mathcal{B}| \cdot |\mathcal{H}|$  sized data summaries. Notably, such a hallucinator-bases data parameterization can be optimized using any of the aforementioned data optimization frameworks (Sections [2.1](#page-3-1) to [2.4\)](#page-6-0)

This framework was concurrently proposed by [Deng & Russakovsky](#page-14-1) [\(2022\)](#page-14-1) (we take the liberty to term their unnamed model as "*Lin*-ear *Ba*-ses") and [Liu et al.](#page-16-7) [\(2022c\)](#page-16-7) (HaBa). LinBa modifies the general hallucinator-bases framework by assuming  $(1)$  the bases' vector space  $(\mathbb{B})$  to be the same as the task input space  $(\mathcal{X})$ ; and (2) the hallucinator to be linear and additionally conditioned on a given predictand. More specifically, the data parameterization can be formalized as follows:

<span id="page-7-1"></span>
$$
\mathcal{D}_{syn} \triangleq \left\{ (y \mathbf{H}^T \mathbf{B}, y) \right\}_{\substack{y \sim C \ \mathbf{H} \sim \mathcal{H}}} \mathcal{E} \left\{ \mathbf{H}^T \mathbf{B}, \mathbf{H} \right\}_{i \sim \mathcal{H}} \tag{13}
$$

$$
\text{s.t.} \quad \mathbf{B} \in \mathbb{R}^{|\mathbf{B}| \times \dim(\mathcal{X})} \triangleq [b_i \in \mathcal{X}]_{i=1}^{|\mathbf{B}|} \quad ; \quad \mathcal{H} \triangleq \left\{ \mathbf{H}_i \in \mathbb{R}^{|\mathbf{B}| \times |\mathcal{C}|} \right\}_{i=1}^{|\mathcal{H}|},
$$

where for the sake of notational simplicity, we assume  $y \in \mathbb{R}^{|\mathcal{C}|}$  represents the one-hot vector of the label for which we want to generate data, and the maximum amount of data that can be synthesized  $n \leq |\mathcal{C}| \cdot |\mathcal{H}|$ . Since the data generation (Equation  $(13)$ ) is end-to-end differentiable, both **B** and  $H$  are jointly optimized using the TBPTT framework discussed in Section [2.1,](#page-3-1) albeit with some crucial modifications for vastly improved performance: (1) using momentum-based optimizers instead of vanilla SGD in the inner-loop; and (2) longer unrolling ( $\geq 100$  steps) of the inner-loop during TBPTT. [Liu et al.](#page-16-7) [\(2022c\)](#page-16-7) (HaBa) relax the linear and predictand-conditional hallucinator assumption of LinBa, equating to the following data parameterization:

<span id="page-7-2"></span>
$$
\mathcal{D}_{\text{syn}} \triangleq \left\{ (h(b), y) \right\}_{\substack{b, y \sim \mathcal{B} \\ h \sim \mathcal{H}}} \quad \text{s.t.} \quad \mathcal{B} \triangleq \left\{ (b_i \in \mathcal{X}, y_i \in \mathcal{Y}) \right\}_{i=1}^{|\mathcal{B}|} \quad ; \quad \mathcal{H} \triangleq \left\{ h_{\theta_i} : \mathcal{X} \mapsto \mathcal{X} \right\}_{i=1}^{|\mathcal{H}|},\tag{14}
$$

where  $\beta$  and  $\mathcal H$  are optimized using the trajectory matching framework (Section [2.3\)](#page-5-0) with an additional contrastive constraint to promote diversity in  $\mathcal{D}_{syn}$  (cf. [Liu et al.](#page-16-7) [\(2022c\)](#page-16-7), Equation (6)). Following this setup, HaBa can generate at most  $|\mathcal{B}| \cdot |\mathcal{H}|$  sized data summaries. Furthermore, one striking difference between HaBa (Equation [\(14\)](#page-7-2)) and LinBa (Equation [\(13\)](#page-7-1)) is that to generate each data point, LinBa uses a linear combination of *all* the bases, whereas HaBa generates a data point using a *single* base vector.

<span id="page-8-0"></span>Table 1: Comparison of data distillation methods. Each method (1) synthesizes the data summary on the train-set; (2) unless mentioned, trains a 128-width ConvNet [\(Gidaris & Komodakis,](#page-15-9) [2018\)](#page-15-9) on the data summary; and (3) evaluates it on the test-set. Confidence intervals are obtained by training at least 5 networks on the data summary. LinBa (No Fact.) represents LinBa with the no factorization. Methods evaluated using KRR are marked as ( $\infty$ -Conv) or ( $\infty$ -FC). The equivalent storage-in-bytes is used for factorization-based techniques instead of IPC. The best method in their category is **emboldened**, the best-overall non-factorized method evaluated on ConvNet is **colored orange**, and the best-overall factorized method is **colored blue**.

| Dataset              |                               | <b>MNIST</b>               |                    |                   | $CIFAR-10$                 |                          |                                 | <b>CIFAR-100</b>  |                            |                   | Tiny ImageNet                     |                                                                 |                   |
|----------------------|-------------------------------|----------------------------|--------------------|-------------------|----------------------------|--------------------------|---------------------------------|-------------------|----------------------------|-------------------|-----------------------------------|-----------------------------------------------------------------|-------------------|
| Imgs/Class (IPC)     |                               | $\mathbf{1}$               | 10                 | 50                | $\mathbf{1}$               | 10                       | 50                              | $\mathbf{1}$      | 10                         | 50                | $\mathbf{1}$                      | 10                                                              | 50                |
| Baselines            | Random                        | 64.9<br>$\pm 3.5$          | 95.1<br>±0.9       | 97.9<br>$\pm$ 0.2 | 14.4<br>±2.0               | 26.0<br>$\pm\,1.2$       | 43.4<br>$\pm\textbf{1.0}$       | 4.2<br>$\pm 0.3$  | 14.6<br>$\pm 0.5$          | 30.0<br>$\pm 0.4$ | 1.5<br>$\pm$ 0.1                  | 6.0<br>±0.8                                                     | 16.8<br>±1.8      |
|                      | Herding <sup>L</sup>          | $\underset{\pm 1.6}{89.2}$ | 93.7<br>$\pm 0.3$  | 94.9<br>$\pm 0.2$ | $\underset{\pm 1.2}{21.5}$ | 31.6<br>$±$ 0.7          | $\substack{40.4 \\ \pm 0.6}$    | 8.4<br>$\pm 0.3$  | 17.3<br>$\pm 0.5$          | 33.7<br>$\pm 0.5$ |                                   |                                                                 |                   |
|                      | Forgetting <sup>2</sup>       | 35.5<br>±5.6               | 68.1<br>$\pm 3.3$  | 88.2<br>$\pm 1.2$ | 13.5<br>$\pm 1.2$          | 23.3<br>$\pm 1.0$        | 23.3<br>$\pm 1.1$               | 4.5<br>$\pm 0.2$  | 15.1<br>$\pm 0.3$          | 30.5<br>$\pm 0.3$ |                                   |                                                                 |                   |
| Meta-model Matching  | DD <sup>3</sup>               |                            | 79.5<br>$\pm\,8.1$ | $\overline{a}$    |                            | 36.8<br>$\pm 1.2$        |                                 |                   |                            |                   |                                   |                                                                 |                   |
|                      | LinBa (No Fact.) $16$         | 95.2<br>±0.3               | 98.8<br>±0.1       | 99.2<br>±0.1      | 49.1<br>$\pm 0.6$          | 62.4<br>$\pm 0.4$        | 70.5<br>$\pm 0.4$               | 21.3<br>$\pm 0.6$ | 34.7<br>$\pm 0.5$          |                   |                                   |                                                                 |                   |
|                      | $KIP$ (ConvNet) <sup>4</sup>  | 90.1<br>$\pm 0.1$          | 97.5<br>$\pm 0.0$  | 98.3<br>$\pm 0.1$ | 49.9<br>$\pm 0.2$          | 62.7<br>$\pm 0.3$        | 68.6<br>$\pm 0.2$               | 15.7<br>$\pm 0.2$ | 28.3<br>$\pm 0.1$          |                   |                                   |                                                                 |                   |
|                      | $RFAD$ (ConvNet) <sup>5</sup> | 94.4<br>$\pm\,1.5$         | 98.5<br>$\pm 0.1$  | 98.8<br>$\pm 0.1$ | 53.6<br>±1.2               | 66.3<br>$\pm$ 0.5        | 71.1<br>$\pm 0.4$               | 26.3<br>$\pm 1.1$ | 33.0<br>$\pm 0.3$          |                   |                                   |                                                                 |                   |
|                      | $FRepO (ConvNet)^6$           | 93.0<br>$\pm 0.4$          | 98.6<br>$\pm 0.1$  | 99.2<br>±0.1      | 46.8<br>$\pm 0.7$          | 65.5<br>$\pm 0.6$        | 71.7<br>$\pm 0.2$               | 28.7<br>$\pm 0.1$ | 42.5<br>±0.2               | 44.3<br>$\pm 0.2$ | 15.4<br>±0.3                      | $\bf 25.4$<br>±0.2                                              |                   |
|                      | KIP $(\infty$ -FC $)^7$       | 85.5<br>$\pm 0.1$          | 97.2<br>$\pm 0.2$  | 98.4<br>$\pm 0.1$ | 40.5<br>$\pm 0.4$          | 53.1<br>$\pm 0.5$        | 58.6<br>$\pm 0.4$               | $\overline{a}$    | $\overline{a}$             |                   |                                   |                                                                 |                   |
|                      | KIP $(\infty$ -Conv $)^4$     | 97.3<br>$\pm$ 0.1          | 99.1<br>$\pm$ 0.1  | 99.5<br>±0.1      | 64.7<br>$\pm$ 0.2          | 75.6<br>$\pm$ 0.2        | 80.6<br>$\pm$ 0.1               | 34.9<br>$\pm 0.1$ | 49.5<br>±0.3               |                   |                                   |                                                                 |                   |
|                      | RFAD $(\infty$ -Conv $)^5$    | 97.2<br>$\pm 0.2$          | 99.1<br>$\pm 0.0$  | 99.1<br>$\pm 0.0$ | 61.4<br>$\pm 0.8$          | 73.7<br>$\pm 0.2$        | 76.6<br>$\pm 0.3$               | 44.1<br>$\pm$ 0.1 | 46.8<br>$\pm 0.2$          |                   |                                   |                                                                 |                   |
|                      | FRePO $(\infty$ -Conv $)^6$   | 92.6<br>$\pm 0.4$          | 98.6<br>$\pm 0.1$  | 99.2<br>$\pm 0.1$ | 47.9<br>$\pm 0.6$          | 68.0<br>$\pm 0.2$        | 74.4<br>$\pm 0.1$               | 32.3<br>$\pm 0.1$ | 44.9<br>$\pm 0.2$          | 43.0<br>$\pm 0.3$ | 19.1<br>$\pm 0.3$                 | $\bf 26.5$<br>$\pm 0.1$                                         |                   |
| Matching<br>Gradient | $\mathrm{DC}^{\bf 8}$         | 91.7<br>±0.5               | 97.4<br>$\pm 0.2$  | 98.2<br>$\pm 0.2$ | 28.3<br>$\pm 0.5$          | 44.9<br>$\pm 0.5$        | 53.9<br>$\pm 0.5$               | 12.8<br>$\pm 0.3$ | 25.2<br>$\pm 0.3$          | 30.5<br>$\pm 0.3$ | 4.6<br>$\pm 0.6$                  | 11.2<br>$\pm 1.6$                                               | 10.9<br>±0.7      |
|                      | DSA <sup>9</sup>              | 88.7<br>$\pm 0.6$          | 97.8<br>±0.1       | 99.2<br>±0.1      | 28.8<br>±0.7               | 52.1<br>$\pm 0.5$        | 60.6<br>$\pm 0.5$               | 13.9<br>$\pm 0.3$ | $_{\pm0.3}^{32.3}$         | 42.8<br>±0.4      | 6.6<br>$\pm 0.2$                  | 14.4<br>±2.0                                                    | 22.6<br>±2.6      |
|                      | DCC <sup>10</sup>             | -                          | $\overline{a}$     | $\overline{a}$    | 34.0<br>±0.7               | 54.5<br>±0.5             | 64.2<br>$\pm$ 0.4               | 14.6<br>$\pm 0.3$ | 33.5<br>$\pm 0.3$          | 39.3<br>$\pm 0.4$ | $\overline{a}$                    | $\overline{a}$                                                  |                   |
| Distr.<br>Matching   | DM <sup>11</sup>              | 89.7<br>$\pm 0.6$          | 97.5<br>$\pm$ 0.1  | 98.6<br>$\pm 0.1$ | 26.0<br>$\pm 0.8$          | 48.9<br>$\pm 0.6$        | 63.0<br>$\pm$ 0.4               | 11.4<br>$\pm 0.3$ | 29.7<br>$\pm 0.3$          | 43.6<br>$\pm$ 0.4 | 3.9<br>$\pm$ 0.2                  | 12.9<br>$\pm$ 0.4                                               | 24.1<br>$\pm 0.3$ |
|                      | $\mathrm{CAFE}^{12}$          | 90.8<br>±0.5               | 97.5<br>$\pm$ 0.1  | 98.9<br>$\pm 0.2$ | $\bf{31.6}$<br>±0.8        | 50.9<br>$\pm$ 0.5        | 62.3<br>$\pm 0.4$               | 14.0<br>$\pm 0.3$ | $_{\rm 31.5}$<br>$\pm 0.2$ | 42.9<br>$\pm 0.2$ | $\overline{a}$                    |                                                                 |                   |
| Traj.<br>Matching    | MTT <sup>13</sup>             |                            |                    |                   | 46.3<br>$\pm 0.8$          | 65.3<br>$\pm 0.7$        | 71.6<br>$\pm 0.2$               | 24.3<br>$\pm 0.3$ | 40.1<br>$\pm 0.4$          | 47.7<br>$\pm 0.2$ | 8.8<br>$\pm$ 0.3                  | 23.2<br>$\pm 0.2$                                               | 28.0<br>±0.3      |
|                      | $\mathrm{TESLA^{14}}$         | $\overline{a}$             |                    | $\overline{a}$    | $\bf 48.5$<br>±0.8         | 66.4<br>±0.8             | 72.6<br>±0.7                    | 24.8<br>±0.4      | 41.7<br>$\pm 0.3$          | 47.9<br>±0.3      | $\overline{\phantom{a}}$          |                                                                 |                   |
| Factorization        | $\mathrm{IDC}^{15}$           |                            |                    |                   | 50.0<br>$\pm 0.4$          | 67.5<br>$\pm 0.5$        | 74.5<br>$\pm 0.1$               | Ĭ.                | 44.8<br>$\pm 0.2$          |                   |                                   |                                                                 |                   |
|                      | LinBa <sup>16</sup>           | 98.7<br>±0.7               | 99.3<br>±0.5       | 99.4<br>±0.4      | 66.4<br>±0.4               | 71.2<br>$\pm 0.4$        | 73.6<br>$\pm 0.5$               | 34.0<br>$\pm 0.4$ | $^{42.9}_{\pm 0.7}$        |                   | 16.0<br>±0.7                      |                                                                 |                   |
|                      | HaBa <sup>17</sup>            |                            |                    |                   | 48.3<br>$_{\pm 0.8}$       | 69.9<br>$\pm 0.4$        | $_{\pm0.2}^{74.0}$              | 33.4<br>$\pm 0.4$ | 40.2<br>$\pm 0.2$          | 47.0<br>$\pm 0.2$ |                                   |                                                                 |                   |
|                      | KFS <sup>18</sup>             | $\overline{a}$             |                    | $\overline{a}$    | 59.8<br>$\pm 0.5$          | $\mathbf{72.0}_{\pm0.3}$ | $\mathop{75.0}\limits_{\pm0.2}$ | 40.0<br>$\pm 0.5$ | 50.6<br>$\pm 0.2$          | $\overline{a}$    | $\mathbf{22.7}_{\pm\mathbf{0.2}}$ | $\mathbf{\begin{smallmatrix} 27.8 \ \pm 0.2 \end{smallmatrix}}$ |                   |
| <b>Full Dataset</b>  |                               |                            | 99.6<br>$\pm 0.1$  |                   |                            | 84.8<br>$\pm 0.1$        |                                 |                   | 56.2<br>$\pm 0.3$          |                   |                                   | 37.6<br>$\pm$ 0.4                                               |                   |

<sup>1</sup> [\(Welling,](#page-19-0) [2009\)](#page-19-0), <sup>2</sup> [\(Toneva et al.,](#page-18-1) [2019\)](#page-18-1), <sup>3</sup> [\(Wang et al.,](#page-19-2) [2018\)](#page-19-2), <sup>4</sup> [\(Nguyen et al.,](#page-17-5) [2021b\)](#page-17-5), <sup>5</sup> [\(Loo et al.,](#page-16-4) [2022\)](#page-16-4)

 $6$  [\(Zhou et al.,](#page-19-5) [2022b\)](#page-16-5),  $7$  [\(Nguyen et al.,](#page-17-4) [2021a\)](#page-17-4),  $8$  [\(Zhao et al.,](#page-19-4) [2021\)](#page-19-6),  $9$  [\(Zhao & Bilen,](#page-19-6) 2021),  $10$  [\(Lee et al.,](#page-16-5) 2022b)

<sup>11</sup> [\(Zhao & Bilen,](#page-19-7) [2023\)](#page-19-7), <sup>12</sup> [\(Wang et al.,](#page-19-8) [2022\)](#page-19-8), <sup>13</sup> [\(Cazenavette et al.,](#page-14-3) [2022\)](#page-14-3), <sup>14</sup> [\(Cui et al.,](#page-14-4) [2022b\)](#page-14-4)

<sup>15</sup> [\(Kim et al.,](#page-16-6) [2022\)](#page-16-6), <sup>16</sup> [\(Deng & Russakovsky,](#page-14-1) [2022\)](#page-14-1), <sup>17</sup> [\(Liu et al.,](#page-16-7) [2022c\)](#page-16-7), <sup>18</sup> [\(Lee et al.,](#page-16-8) [2022a\)](#page-16-8)

[Lee et al.](#page-16-8) [\(2022a\)](#page-16-8) (KFS) further build atop this framework by maintaining a different bases' vector space B from the data domain  $\mathcal{X}$ , such that dim(B)  $\langle \dim(\mathcal{X}) \rangle$ . This parameterization allows KFS to store an even larger number of images, with a comparable storage budget to other methods. Formally, the data parameterization for KFS can be specified as:

$$
\mathcal{D}_{syn} \triangleq \bigcup_{c \in C} \{ (h(b), c) \}_{b \sim \mathcal{B}_c}^{h \sim \mathcal{H}} \qquad (15)
$$
s.t.

$$
\mathcal{B} \triangleq \bigcup_{c \in C} \mathcal{B}_c \quad ; \quad \mathcal{B}_c \triangleq \{ b_i^c \in \mathbb{B} \}_{i=1}^B \quad ; \quad \mathcal{H} \triangleq \{ h_{\theta_i} : \mathbb{B} \mapsto \mathcal{X} \}_{i=1}^{|\mathcal{H}|},
$$

where KFS stores *B* bases per class, equivalent to a total of  $n = |\mathcal{C}| \cdot B \cdot |\mathcal{H}|$  sized data summaries. Following this data parameterization,  $\beta$  and  $\mathcal H$  are optimized using the distribution matching framework for data distillation (Equation  $(11)$ ) to ensure fast, single-level optimization.

**Data Distillation** *vs.* **Data Compression.** We highlight that it is non-trivial to ensure a fair comparison between data distillation techniques that (1) are "non-factorized", *i.e.*, maintain each synthesized data point as a set of free-parameters (Sections [2.1](#page-3-1) to [2.4\)](#page-6-0); and (2) use factorized approaches discussed in this section to efficiently organize the data summary. If we use the size of the data summary (*n*) as the efficiency metric, factorized approaches are adversely affected as they need a much smaller storage budget to synthesize the same-sized data summaries. On the other hand, if we use "end-to-end bytes of storage" as the efficiency metric, non-factorized approaches are adversely affected as they perform no kind of data compression, but focus solely on better understanding the model-to-data relationship through the lens of optimization. For a better intuition, one can apply posthoc lossless compression (*e.g.*, Huffman coding) on data synthesized by non-factorized data distillation approaches to fit more images in the same storage budget [\(Schirrmeister et al.,](#page-18-2) [2022\)](#page-18-2). Such techniques unintentionally deviate from the original intent of data distillation, and progress more toward better data compression techniques. As a potential solution, we encourage the community to consider reporting results for both scenarios: a fixed data summary size *n*, as well as fixed bytes-of-storage. Nonetheless, for the ease of empirical comparison amongst the discussed data distillation techniques, we provide a collated set of results over four image-classification datasets in Table [1.](#page-8-0)

<span id="page-9-0"></span>

# **3 Data Modalities**

Having learned about different kinds of optimization frameworks for data distillation, we now discuss an orthogonal (and important) aspect of data distillation — *what kinds of data can data distillation techniques summarize?* From continuous-valued images to heterogeneous and semi-structured graphs, the underlying data for each unique application of machine learning has its own modality, structure, and set of assumptions. While the earliest data distillation techniques were designed to summarize images, recent steps have been taken to expand the horizon of data distillation into numerous other scenarios. In what follows, we categorize existing data distillation methods by their intended data modality, while also discussing their unique challenges.

**Images.** A large-portion of existing data distillation techniques are designed for image classification data [\(Cazenavette et al.,](#page-14-3) [2022;](#page-14-3) [Deng & Russakovsky,](#page-14-1) [2022;](#page-14-1) [Kim et al.,](#page-16-6) [2022;](#page-16-6) [Lee et al.,](#page-16-8) [2022a;](#page-16-8)[b;](#page-16-5) [Liu et al.,](#page-16-7) [2022c;](#page-16-7) [Loo et al.,](#page-16-4) [2022;](#page-16-4) [Nguyen et al.,](#page-17-4) [2021a;](#page-17-4)[b;](#page-17-5) [Wang et al.,](#page-19-8) [2022;](#page-19-8) [2018;](#page-19-2) [Zhao & Bilen,](#page-19-6) [2021;](#page-19-6) [2022;](#page-19-9) [2023;](#page-19-7) [Zhao et al.,](#page-19-4) [2021;](#page-19-4) [Zhou et al.,](#page-19-5) [2022b\)](#page-19-5) simply because images have a real-valued, continuous data-domain ( $\mathcal{X} \equiv \mathbb{R}^{d \times d}$ ). This allows SGD-based optimization directly on the data, which is treated as a set of free parameters. Intuitively, incrementally changing each pixel value can be treated as slight perturbations in the color space, and hence given a suitable data distillation loss, can be naïvely optimized using SGD.

**Text.** Textual data is available in large amounts from sources like websites, news articles, academic manuscripts, *etc.*, and is also readily accessible with datasets like the common crawl<sup>[1](#page-9-1)</sup> which sizes up to almost 541TB. Furthermore, with the advent of large language models (LLM) [\(Brown et al.,](#page-13-6) [2020;](#page-13-6) [Devlin et al.,](#page-14-5) [2019;](#page-14-5) [Thoppilan et al.,](#page-18-3) [2022\)](#page-18-3), training such models from scratch on large datasets has become an increasingly

<span id="page-9-1"></span><sup>1</sup><https://commoncrawl.org/the-data/>

Image /page/10/Figure/1 description: The image depicts a process called "Data Distillation." It shows four types of input data on the top row: a grid of images, a document with placeholder text and icons representing multiple documents, a network graph labeled (A, X, Y), and a matrix representing users and items (Movies/Ads/Songs) with binary values. These inputs are fed into a central gray rectangle labeled "Data Distillation." The bottom row shows the corresponding distilled outputs: a smaller grid of images, a document with condensed placeholder text, a smaller network graph, and a matrix labeled "Fake Users" and "Items (Movies/Ads/Songs)" containing decimal values ranging from 0.1 to 1.0.

[\[HQ Image Link\]](https://www.noveens.com/images/dd_survey/data_modalities.pdf) Figure 4: Overview of distilling data for a few commonly observed data modalities.

expensive procedure. Despite recent efforts in democratizing LLM training [\(Geiping & Goldstein,](#page-14-6) [2022;](#page-14-6) [Scao](#page-18-4) [et al.,](#page-18-4) [2022;](#page-18-4) [Wolf et al.,](#page-19-10) [2020\)](#page-19-10), effectively distilling large-scale textual data as a solution is yet to be explored. The key bottlenecks for distilling textual data are: (1) the inherently discrete nature of data, where a token should belong in a limited vocabulary of words; (2) the presence of a rich underlying structure, *i.e.*, sentences of words (text) obey fixed patterns according to a grammar; and (3) richness of context, *i.e.*, a given piece of text could have wildly different semantic interpretations under different contexts.

[Sucholutsky & Schonlau](#page-18-5) [\(2021\)](#page-18-5) take a latent-embedding approach to textual data distillation. On a high level, to circumvent the discreteness of the optimization, the authors perform distillation in a continuous embedding space. More specifically, assuming access to a latent space specified by a *fixed* text-encoder, the authors learn continuous *representations* of each word in the distilled text and optimize it using the TBPTT data-distillation framework proposed by [Wang et al.](#page-19-2) [\(2018\)](#page-19-2) (Equation [\(4\)](#page-3-3)). Finally, the distilled text representations are decoded by following a simple nearest-neighbor protocol.

**Graphs.** A wide variety of data and applications can inherently be modeled as graphs, *e.g.*, user-item interactions [\(Mittal et al.,](#page-17-8) [2021;](#page-17-8) [Sachdeva & McAuley,](#page-17-9) [2020;](#page-17-9) [Wu et al.,](#page-19-11) [2020\)](#page-19-11), social networks [\(Fan et al.,](#page-14-7) [2019\)](#page-14-7), autonomous driving [\(Casas et al.,](#page-13-7) [2020;](#page-13-7) [Sachdeva et al.,](#page-18-6) [2022b\)](#page-18-6), *etc.* Taking the example of social networks, underlying user-user graphs typically size up to the billion-scale [\(Chen et al.,](#page-14-8) [2021\)](#page-14-8), calling for principled scaling solutions. Graph distillation trivially solves a majority of the scale challenges, but synthesizing tiny, high-fidelity graphs has the following hurdles: (1) nodes in a graph can be highly abstract, *e.g.*, users, products, *etc.* and could be discrete, heterogeneous, or even numerical IDs; (2) graphs follow a variety of intrinsic patterns (*e.g.*, spatial [\(Kipf & Welling,](#page-16-9) [2017\)](#page-16-9)) which need to be retained in the distilled graphs; and (3) quadratic size of the adjacency matrix could be computationally prohibitive for data optimization.

[Jin et al.](#page-15-10) [\(2022b\)](#page-15-10) propose GCOND which distills graphs in the inductive node-classification setting, specified by its node-feature matrix  $\mathbf{X}$ , adjacency matrix  $\mathbf{A}$ , and node-target matrix  $\mathbf{Y}$ . GCOND distills the given graph by learning a synthetic node-feature matrix  $\mathbf{X}_{syn}$ , and using  $\mathbf{X}_{syn}$  to generate  $\mathbf{A}_{syn} \triangleq f_{\theta}(\mathbf{X}_{syn})$  which can be realized, *e.g.*, through a parametric similarity function  $\sin\theta(\cdot, \cdot)$  between the features of two nodes, *i.e.*,  $\mathbf{A}^{i,j}_{syn} \triangleq \sigma(\text{sim}_{\theta}(\mathbf{X}^i_{syn}, \mathbf{X}^j_{syn}))$ , where  $\sigma(\cdot)$  is the sigmoid function. Finally, both  $\mathbf{X}_{syn}$  and  $\theta$  are optimized using the gradient-matching framework proposed by [Zhao et al.](#page-19-4) [\(2021\)](#page-19-4) (Equation [\(7\)](#page-5-1)). Another work [\(Liu](#page-16-10) [et al.,](#page-16-10) [2022a\)](#page-16-10) (GCDM) shares the same framework as GCOND but instead uses the distribution matching framework proposed by [Zhao & Bilen](#page-19-7) [\(2023\)](#page-19-7) (Equation [\(11\)](#page-6-2)) to optimize **X**syn and *θ*. Extending to a graph-classification setting, [Jin et al.](#page-15-11) [\(2022a\)](#page-15-11) further propose DosCond with two major changes compared to GCOND: (1) instead of parameterizing the adjacency matrix using a similarity function on  $\mathbf{X}_{syn}$ , they maintain a free-parameter matrix  $\Omega$  with the same size as the adjacency matrix, and sample each  $\mathbf{A}^{i,j}_{syn}$  entry through an independent Bernoulli draw on  $\Omega^{i,j}$  as the prior using the reparameterization trick [\(Maddison](#page-17-10) [et al.,](#page-17-10) [2017\)](#page-17-10). Such a procedure ensures differentiability as well as discrete matrix synthesis; and (2) **X**syn and  $\Omega$  are still optimized using the gradient-matching framework (Equation [\(7\)](#page-5-1)), albeit with only a single-step, *i.e.*,  $T = 1$  for improved scalability and without empirically observing a loss in performance.

**Recommender Systems.** The amount of online user-feedback data available for training recommender systems is rapidly increasing [\(Wu et al.,](#page-19-12) [2022\)](#page-19-12). Furthermore, typical user-facing recommender systems need to be periodically re-trained [\(Naumov et al.,](#page-17-11) [2019\)](#page-17-11), which adds to requirements for smarter data summarization solutions (see [Sachdeva et al.](#page-18-7) [\(2022c\)](#page-18-7) for background on sampling recommender systems data). However, distilling recommender systems data has the following challenges: (1) the data is available in the form of abstract and discrete (userID, itemID, relevance) tuples, which departs from the typical (features, label) setup; (2) the distribution of both user- and item-popularity follows a strong power-law which leads to data scarcity and unstable optimization; and (3) the data inherits a variety of inherent structures, *e.g.*, sequential patterns [\(Kang & McAuley,](#page-15-12) [2018;](#page-15-12) [Sachdeva et al.,](#page-17-12) [2019\)](#page-17-12), user-item graph patterns [\(Wu et al.,](#page-19-13) [2019\)](#page-19-13), item-item co-occurrence patterns [\(Steck,](#page-18-8) [2019\)](#page-18-8), missing-not-at-randomness [\(Sachdeva et al.,](#page-18-9) [2020;](#page-18-9) [Schnabel et al.,](#page-18-10) [2016\)](#page-18-10), *etc.*

[Sachdeva et al.](#page-18-11) [\(2022a\)](#page-18-11) propose Distill-CF which distills implicit-feedback recommender systems data, *i.e.*, when the observed user-item relevance is binary (*e.g.*, click or no-click). Such data can be visualized as a binary user-item matrix **R** where each row represents a single user, and each column represents an item. On a high-level, Distill-CF synthesizes fake users along with their item-consumption histories, visualized as a synthetic user-item matrix **R**syn. Notably, to preserve semantic meaning, the item-space in **R**syn is the same as in **R**. To alleviate the data discreteness problem, DISTILL-CF maintains a sampling-prior matrix  $\Omega$ which has the same size as  $\mathbf{R}_{syn}$ , and can in-turn be used to generate  $\mathbf{R}_{syn}$  using multi-step Gumbel sampling with replacement [\(Jang et al.,](#page-15-13) [2017\)](#page-15-13) for each user's prior in  $\Omega$  (equivalent to each row). Such a formulation automatically also circumvents the dynamic user- and item-popularity artifact in recommender systems data, which can analogously be controlled by the row- and column-wise entropy of  $\Omega$ . Finally,  $\Omega$  is optimized using the meta-model matching framework proposed by [Nguyen et al.](#page-17-4) [\(2021a\)](#page-17-4). Notably, [Sachdeva et al.](#page-18-11) [\(2022a\)](#page-18-11) also propose infinite-width autoencoders which suit the task of item recommendation while also leading to closed-form computation of the inner-loop in the meta-model matching framework (Equation [\(5\)](#page-4-2)).

<span id="page-11-0"></span>

# **4 Applications**

While the data distillation task was originally designed to accelerate model training, there are numerous other applications of a high-fidelity data summary. Below we briefly discuss a few such promising applications, along with providing pointers to existing works.

**Differential Privacy.** Data distillation was recently shown to be a promising solution for differential privacy as defined by [Dwork](#page-14-9) [\(2008\)](#page-14-9). [Dong et al.](#page-14-10) [\(2022\)](#page-14-10) show that data distillation techniques can perform better than existing state-of-the-art differentially-private data generators [\(Cao et al.,](#page-13-8) [2021;](#page-13-8) [Harder et al.,](#page-15-14) [2021\)](#page-15-14) on both performance and privacy grounds. Notably, the privacy benefits of data distillation techniques are virtually *free*, as none of these methods were optimized for generating differentially-private data. [Chen et al.](#page-14-11) [\(2022\)](#page-14-11) further modify the gradient matching framework (Equation [\(7\)](#page-5-1)) by clipping and adding white noise to the gradients obtained on the original dataset while optimization. Such a routine was shown to have better sample utility, while also satisfying strict differential privacy guarantees. From a completely application perspective, data distillation has been used to effectively distill sensitive medical data as well [\(Li et al.,](#page-16-11) [2020a;](#page-16-11) [2022\)](#page-16-12).

**Neural Architecture Search (NAS).** Automatic searching of neural-network architectures can alleviate a majority of manual effort, as well as lead to more accurate models (see [Elsken et al.](#page-14-12) [\(2019\)](#page-14-12) for a detailed review). Analogous to using model extrapolation, *i.e.*, extrapolating the performance of an under-trained model architecture on the full dataset; data extrapolation, on the other hand, aims to train models on a small, high-fidelity data sample till convergence. [Zhao et al.](#page-19-4) [\(2021\)](#page-19-4) show promise of their technique (DC) on a small custom NAS test-bed consisting of only 720 variations of the ConvNet architecture [\(Gidaris & Komodakis,](#page-15-9) [2018\)](#page-15-9) by employing the data extrapolation framework. However, [Cui et al.](#page-14-13) [\(2022a\)](#page-14-13) show that data distillation *does not* perform well when evaluating diverse architectures on the bigger test-bed, NAS-Bench-201 [\(Dong &](#page-14-14) [Yang,](#page-14-14) [2020\)](#page-14-14), calling for better rank-preserving data distillation techniques.

**Continual Learning.** Never-ending learning (see [Parisi et al.](#page-17-13) [\(2019\)](#page-17-13) for a detailed review) has been frequently associated with catastrophic forgetting [\(French,](#page-14-15) [1999\)](#page-14-15), *i.e.*, patterns extracted from old data/tasks are easily forgotten when patterns from new data/tasks are learned. Data distillation has been shown as an effective solution to alleviate catastrophic forgetting, by simply using the distilled data summary in a replay buffer that is continually updated and used in subsequent data/task training [\(Rosasco et al.,](#page-17-14) [2021;](#page-17-14) [Sangermano et al.,](#page-18-12) [2022;](#page-18-12) [Wiewel & Yang,](#page-19-14) [2021\)](#page-19-14). [Deng & Russakovsky](#page-14-1) [\(2022\)](#page-14-1) show further evidence of a simple *compress-then-recall* strategy outperforming existing state-of-the-art continual learning approaches. Notably, *only* the data summary is stored for each task, and a new model is trained (from scratch) using all previous data summaries, for each new incoming task.

**Federated Learning.** Federated or collaborative learning (see [Li et al.](#page-16-13) [\(2020b\)](#page-16-13) for a detailed survey) involves training a learning algorithm in a decentralized fashion. A standard approach to federated learning is to synchronize local parameter updates to a central server, instead of synchronizing the raw data itself [\(Konečn`y et al.,](#page-16-14) [2016\)](#page-16-14). Data distillation, on the other hand, alleviates the need to synchronize large parametric models across clients and servers, by synchronizing tiny synthesized data summaries to the central server instead. Subsequently, the entire training happens only on the central server. Such data distillation-based federated learning methods [\(Goetz & Tewari,](#page-15-15) [2020;](#page-15-15) [Hu et al.,](#page-15-16) [2022;](#page-15-16) [Liu et al.,](#page-16-15) [2022b;](#page-16-15) [Song et al.,](#page-18-13) [2022;](#page-18-13) [Xiong](#page-19-15) [et al.,](#page-19-15) [2022;](#page-19-15) [Zhou et al.,](#page-19-16) [2020\)](#page-19-16) are shown to perform better than model-synchronization based federated learning approaches, while also requiring multiple orders lesser client-server communication.

<span id="page-12-0"></span>

# **5 Challenges & Future Directions**

Despite achieving remarkable progress in data-efficient learning, there are numerous framework-based, theoretical, and application-based directions yet to be explored in data distillation. In what follows, we highlight and discuss such directions for the community to further explore, based either on early evidence or our intuition.

**New data modalities.** Extending the discussion in Section [3,](#page-9-0) existing data distillation techniques have largely been restricted to cater to image datasets, primarily due to the amenable data-optimization in the continuous pixel-domain of images. Despite recent efforts in increasing the horizon of data distillation to other data modalities such as graphs [\(Jin et al.,](#page-15-11) [2022a](#page-15-11)[;b\)](#page-15-10) and recommender systems [\(Sachdeva et al.,](#page-18-11) [2022a\)](#page-18-11); each data modality poses its unique challenges and calls for future work, *e.g.*, handling long sequences of time-series data in audio-classification [\(Hershey et al.,](#page-15-17) [2017\)](#page-15-17), video classification [\(Karpathy et al.,](#page-16-16) [2014\)](#page-16-16), self-driving [\(Sun et al.,](#page-18-14) [2020\)](#page-18-14); millions of categorical features in tabular data [\(Wang et al.,](#page-19-17) [2021\)](#page-19-17); sparse and noisy financial data [\(Xu & Cohen,](#page-19-18) [2018\)](#page-19-18); *etc.*

**New predictive tasks.** Another limitation of existing data distillation techniques is that their underlying optimization is primarily designed for classification scenarios. However, a large number of predictive tasks fail to naïvely fit into the existing supervised data distillation framework, *e.g.*, image-generation [\(Ramesh](#page-17-15) [et al.,](#page-17-15) [2022;](#page-17-15) [Rombach et al.,](#page-17-16) [2022\)](#page-17-16), language modeling [\(Brown et al.,](#page-13-6) [2020;](#page-13-6) [Devlin et al.,](#page-14-5) [2019;](#page-14-5) [Touvron et al.,](#page-18-15) [2023\)](#page-18-15), representation learning [\(Chen et al.,](#page-14-16) [2020;](#page-14-16) [Grill et al.,](#page-15-18) [2020\)](#page-15-18), *etc.* Further, the aforementioned tasks have gained immense popularity and have seen widespread practical use in the recent years, calling for future work in developing data distillation techniques for more predictive tasks.

**Better scaling.** Existing data distillation techniques validate their prowess *only* in the super low-data regime (typically 1 − 50 data points per class) due to (i) computational difficulties in synthesizing large data summaries with existing techniques; and (ii) collapse to the random-sampling baseline when synthesizing large data summaries, as noted by [Cui et al.](#page-14-13) [\(2022a\)](#page-14-13). This calls for future work from both directions — developing efficient data distillation techniques that are scalable to web-scale datasets, and deeper investigations of the cause and potential fixes of the observed scaling artifacts of existing techniques.

**Improved optimization.** A unifying thread across data distillation techniques is an underlying bilevel optimization, which is provably NP-hard even in the linear inner-optimization case [\(Vicente et al.,](#page-18-16) [1994\)](#page-18-16). Notably, bilevel optimization has been successfully applied in a variety of other applications like meta-learning [\(Finn et al.,](#page-14-17) [2017;](#page-14-17) [Li et al.,](#page-16-17) [2017\)](#page-16-17), hyper-parameter optimization [\(Lorraine et al.,](#page-16-18) [2020;](#page-16-18) [Maclaurin et al.,](#page-16-19)

[2015\)](#page-16-19), neural architecture search [\(Liu et al.,](#page-16-0) [2019\)](#page-16-0), coreset construction [\(Borsos et al.,](#page-13-9) [2020;](#page-13-9) [Zhou et al.,](#page-19-19) [2022a\)](#page-19-19), *etc.* Despite its success, many theoretical underpinnings are yet to be explored, *e.g.*, the effect of commonly-used singleton solution assumption [\(Franceschi et al.,](#page-14-18) [2018\)](#page-14-18), the effect of over-parameterization on bilevel optimization [\(Vicol et al.,](#page-18-17) [2022\)](#page-18-17), connections to statistical influence functions [\(Bae et al.,](#page-13-10) [2022\)](#page-13-10), the bias-variance tradeoff [\(Vicol et al.,](#page-18-18) [2021\)](#page-18-18), *etc.* Clearly, an overall better understanding of bilevel optimization will directly enable the development of better data distillation techniques.

**Improved data-quality evaluation.** As briefly discussed in Section [2,](#page-2-0) data synthesized using data distillation is evaluated from performance, efficiency, and transferability standpoints. However, numerous high-stakes use-cases call for being able to train robust models from a variety of angles such as fairness [\(Mehrabi et al.,](#page-17-17) [2021\)](#page-17-17), adversarial robustness [\(Madry et al.,](#page-17-18) [2018\)](#page-17-18), *etc.* Hence, synthesizing data summaries able to support such robust model training is practical and an important direction for future work. Notably, while popular metrics exist for evaluating the robustness of learning algorithms from the aforementioned standpoints, developing such notions at the dataset-level is non-trivial, and with little existing literature [\(Ben-Eliezer & Yogev,](#page-13-11) [2020;](#page-13-11) [Celis et al.,](#page-14-19) [2018\)](#page-14-19).

## **Acknowledgments**

We sincerely thank Zhiwei Deng, Bo Zhao, and George Cazenavette for their feedback on early drafts of this survey.

## **References**

- <span id="page-13-1"></span>Amro Abbas, Kushal Tirumala, Dániel Simig, Surya Ganguli, and Ari S Morcos. Semdedup: Data-efficient learning at web-scale through semantic deduplication. *arXiv preprint arXiv:2303.09540*, 2023.
- <span id="page-13-0"></span>Sunil Arya, David M Mount, Nathan S Netanyahu, Ruth Silverman, and Angela Y Wu. An optimal algorithm for approximate nearest neighbor searching fixed dimensions. *Journal of the ACM (JACM)*, 45(6):891–923, 1998.
- <span id="page-13-3"></span>Fadhel Ayed and Soufiane Hayou. Data pruning and neural scaling laws: fundamental limitations of score-based algorithms. *arXiv preprint arXiv:2302.06960*, 2023.
- <span id="page-13-4"></span>Olivier Bachem, Mario Lucic, and Andreas Krause. Practical coreset constructions for machine learning. *arXiv preprint arXiv:1703.06476*, 2017.
- <span id="page-13-10"></span>Juhan Bae, Nathan Ng, Alston Lo, Marzyeh Ghassemi, and Roger Grosse. If influence functions are the answer, then what is the question? *arXiv preprint arXiv:2209.05364*, 2022.
- <span id="page-13-11"></span>Omri Ben-Eliezer and Eylon Yogev. The adversarial robustness of sampling. In *Proceedings of the 39th ACM SIGMOD-SIGACT-SIGAI Symposium on Principles of Database Systems*, pp. 49–62, 2020.
- <span id="page-13-2"></span>Jeff Bilmes. Submodularity in machine learning and artificial intelligence. *arXiv preprint arXiv:2202.00132*, 2022.
- <span id="page-13-5"></span>Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020.
- <span id="page-13-9"></span>Zalán Borsos, Mojmir Mutny, and Andreas Krause. Coresets via bilevel optimization for continual learning and streaming. *Advances in Neural Information Processing Systems*, 33:14879–14890, 2020.
- <span id="page-13-6"></span>Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. *Advances in neural information processing systems*, 33:1877–1901, 2020.
- <span id="page-13-8"></span>Tianshi Cao, Alex Bie, Arash Vahdat, Sanja Fidler, and Karsten Kreis. Don't generate me: Training differentially private generative models with sinkhorn divergence. *Advances in Neural Information Processing Systems*, 2021.
- <span id="page-13-7"></span>Sergio Casas, Cole Gulino, Renjie Liao, and Raquel Urtasun. Spagnn: Spatially-aware graph neural networks for relational behavior forecasting from sensor data. In *2020 IEEE International Conference on Robotics and Automation (ICRA)*, pp. 9491–9497. IEEE, 2020.

- <span id="page-14-3"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 4750–4759, 2022.
- <span id="page-14-19"></span>Elisa Celis, Vijay Keswani, Damian Straszak, Amit Deshpande, Tarun Kathuria, and Nisheeth Vishnoi. Fair and diverse dpp-based data summarization. In *International Conference on Machine Learning*, pp. 716–725. PMLR, 2018.
- <span id="page-14-11"></span>Dingfan Chen, Raouf Kerkouche, and Mario Fritz. Private set generation with discriminative information. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022.
- <span id="page-14-8"></span>Qi Chen, Bing Zhao, Haidong Wang, Mingqin Li, Chuanjie Liu, Zengzhong Li, Mao Yang, and Jingdong Wang. Spann: Highly-efficient billion-scale approximate nearest neighborhood search. *Advances in Neural Information Processing Systems*, 34:5199–5212, 2021.
- <span id="page-14-16"></span>Ting Chen, Simon Kornblith, Mohammad Norouzi, and Geoffrey Hinton. A simple framework for contrastive learning of visual representations. In *International conference on machine learning*, pp. 1597–1607. PMLR, 2020.
- <span id="page-14-0"></span>Cody Coleman, Christopher Yeh, Stephen Mussmann, Baharan Mirzasoleiman, Peter Bailis, Percy Liang, Jure Leskovec, and Matei Zaharia. Selection via proxy: Efficient data selection for deep learning. In *International Conference on Learning Representations*, 2020.
- <span id="page-14-13"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. DC-BENCH: Dataset condensation benchmark. In *Thirty-sixth Conference on Neural Information Processing Systems Datasets and Benchmarks Track*, 2022a.
- <span id="page-14-4"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. *arXiv preprint arXiv:2211.10586*, 2022b.
- <span id="page-14-1"></span>Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In *Advances in Neural Information Processing Systems*, 2022.
- <span id="page-14-5"></span>Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. BERT: Pre-training of deep bidirectional transformers for language understanding. In *Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, Volume 1 (Long and Short Papers)*, Minneapolis, Minnesota, June 2019. Association for Computational Linguistics.
- <span id="page-14-2"></span>Chao Dong, Chen Change Loy, and Xiaoou Tang. Accelerating the super-resolution convolutional neural network. In *European conference on computer vision*, pp. 391–407. Springer, 2016.
- <span id="page-14-10"></span>Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *Proceedings of the 39th International Conference on Machine Learning*. PMLR, 2022.
- <span id="page-14-14"></span>Xuanyi Dong and Yi Yang. Nas-bench-201: Extending the scope of reproducible neural architecture search. In *International Conference on Learning Representations*, 2020.
- <span id="page-14-9"></span>Cynthia Dwork. Differential privacy: A survey of results. In *International conference on theory and applications of models of computation*, pp. 1–19. Springer, 2008.
- <span id="page-14-12"></span>Thomas Elsken, Jan Hendrik Metzen, and Frank Hutter. Neural architecture search: A survey. *The Journal of Machine Learning Research*, 20(1):1997–2017, 2019.
- <span id="page-14-7"></span>Wenqi Fan, Yao Ma, Qing Li, Yuan He, Eric Zhao, Jiliang Tang, and Dawei Yin. Graph neural networks for social recommendation. In *The world wide web conference*, pp. 417–426, 2019.
- <span id="page-14-17"></span>Chelsea Finn, Pieter Abbeel, and Sergey Levine. Model-agnostic meta-learning for fast adaptation of deep networks. In *International conference on machine learning*, pp. 1126–1135. PMLR, 2017.
- <span id="page-14-18"></span>Luca Franceschi, Paolo Frasconi, Saverio Salzo, Riccardo Grazzi, and Massimiliano Pontil. Bilevel programming for hyperparameter optimization and meta-learning. In *International Conference on Machine Learning*, pp. 1568–1577. PMLR, 2018.

<span id="page-14-15"></span>Robert M French. Catastrophic forgetting in connectionist networks. *Trends in cognitive sciences*, 3(4):128–135, 1999.

<span id="page-14-6"></span>Jonas Geiping and Tom Goldstein. Cramming: Training a language model on a single gpu in one day, 2022.

- <span id="page-15-5"></span>Amirata Ghorbani and James Zou. Data shapley: Equitable valuation of data for machine learning. In *International Conference on Machine Learning*, pp. 2242–2251. PMLR, 2019.
- <span id="page-15-0"></span>Behrooz Ghorbani, Orhan Firat, Markus Freitag, Ankur Bapna, Maxim Krikun, Xavier Garcia, Ciprian Chelba, and Colin Cherry. Scaling laws for neural machine translation. *arXiv preprint arXiv:2109.07740*, 2021.
- <span id="page-15-9"></span>Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 4367–4375, 2018.
- <span id="page-15-15"></span>Jack Goetz and Ambuj Tewari. Federated learning via synthetic data. *arXiv preprint arXiv:2008.04489*, 2020.
- <span id="page-15-8"></span>Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. In *Advances in neural information processing systems*, pp. 2672–2680, 2014.
- <span id="page-15-18"></span>Jean-Bastien Grill, Florian Strub, Florent Altché, Corentin Tallec, Pierre Richemond, Elena Buchatskaya, Carl Doersch, Bernardo Avila Pires, Zhaohan Guo, Mohammad Gheshlaghi Azar, et al. Bootstrap your own latent-a new approach to self-supervised learning. *Advances in neural information processing systems*, 33:21271–21284, 2020.
- <span id="page-15-6"></span>Chengcheng Guo, Bo Zhao, and Yanbing Bai. Deepcore: A comprehensive library for coreset selection in deep learning. In *Database and Expert Systems Applications: 33rd International Conference, DEXA 2022, Vienna, Austria, August 22–24, 2022, Proceedings, Part I*, pp. 181–195. Springer, 2022.
- <span id="page-15-3"></span>Udit Gupta, Young Geun Kim, Sylvia Lee, Jordan Tse, Hsien-Hsin S Lee, Gu-Yeon Wei, David Brooks, and Carole-Jean Wu. Chasing carbon: The elusive environmental footprint of computing. *IEEE Micro*, 42(4):37–47, 2022.
- <span id="page-15-14"></span>Frederik Harder, Kamil Adamczewski, and Mijung Park. Dp-merf: Differentially private mean embeddings with randomfeatures for practical privacy-preserving data generation. In *International conference on artificial intelligence and statistics*, pp. 1819–1827. PMLR, 2021.
- <span id="page-15-17"></span>Shawn Hershey, Sourish Chaudhuri, Daniel PW Ellis, Jort F Gemmeke, Aren Jansen, R Channing Moore, Manoj Plakal, Devin Platt, Rif A Saurous, Bryan Seybold, et al. Cnn architectures for large-scale audio classification. In *2017 ieee international conference on acoustics, speech and signal processing (icassp)*, pp. 131–135. IEEE, 2017.
- <span id="page-15-4"></span>Geoffrey Hinton, Oriol Vinyals, Jeff Dean, et al. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2(7), 2015.
- <span id="page-15-1"></span>Jordan Hoffmann, Sebastian Borgeaud, Arthur Mensch, Elena Buchatskaya, Trevor Cai, Eliza Rutherford, Diego de Las Casas, Lisa Anne Hendricks, Johannes Welbl, Aidan Clark, et al. Training compute-optimal large language models. *arXiv preprint arXiv:2203.15556*, 2022.
- <span id="page-15-16"></span>Shengyuan Hu, Jack Goetz, Kshitiz Malik, Hongyuan Zhan, Zhe Liu, and Yue Liu. Fedsynth: Gradient compression via synthetic data in federated learning. *arXiv preprint arXiv:2204.01273*, 2022.
- <span id="page-15-7"></span>Arthur Jacot, Franck Gabriel, and Clément Hongler. Neural tangent kernel: Convergence and generalization in neural networks. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-15-13"></span>Eric Jang, Shixiang Gu, and Ben Poole. Categorical reparameterization with gumbel-softmax. In *5th International Conference on Learning Representations, ICLR 2017, Toulon, France, April 24-26, 2017, Conference Track Proceedings*. OpenReview.net, 2017.
- <span id="page-15-11"></span>Wei Jin, Xianfeng Tang, Haoming Jiang, Zheng Li, Danqing Zhang, Jiliang Tang, and Bing Yin. Condensing graphs via one-step gradient matching. In *Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, pp. 720–730, 2022a.
- <span id="page-15-10"></span>Wei Jin, Lingxiao Zhao, Shichang Zhang, Yozen Liu, Jiliang Tang, and Neil Shah. Graph condensation for graph neural networks. In *International Conference on Learning Representations*, 2022b.
- <span id="page-15-12"></span>Wang-Cheng Kang and Julian McAuley. Self-attentive sequential recommendation. In *2018 IEEE international conference on data mining (ICDM)*, pp. 197–206. IEEE, 2018.
- <span id="page-15-2"></span>Jared Kaplan, Sam McCandlish, Tom Henighan, Tom B Brown, Benjamin Chess, Rewon Child, Scott Gray, Alec Radford, Jeffrey Wu, and Dario Amodei. Scaling laws for neural language models. *arXiv preprint arXiv:2001.08361*, 2020.

- <span id="page-16-16"></span>Andrej Karpathy, George Toderici, Sanketh Shetty, Thomas Leung, Rahul Sukthankar, and Li Fei-Fei. Large-scale video classification with convolutional neural networks. In *Proceedings of the IEEE conference on Computer Vision and Pattern Recognition*, pp. 1725–1732, 2014.
- <span id="page-16-1"></span>Krishnateja Killamsetty, Durga S, Ganesh Ramakrishnan, Abir De, and Rishabh Iyer. Grad-match: Gradient matching based data subset selection for efficient deep model training. In Marina Meila and Tong Zhang (eds.), *Proceedings of the 38th International Conference on Machine Learning*, volume 139 of *Proceedings of Machine Learning Research*, pp. 5464–5474. PMLR, 18–24 Jul 2021.
- <span id="page-16-6"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *Proceedings of the 39th International Conference on Machine Learning*, 2022.
- <span id="page-16-9"></span>Thomas N. Kipf and Max Welling. Semi-Supervised Classification with Graph Convolutional Networks. In *Proceedings of the 5th International Conference on Learning Representations*, ICLR '17, 2017.
- <span id="page-16-14"></span>Jakub Konečn`y, H Brendan McMahan, Daniel Ramage, and Peter Richtárik. Federated optimization: Distributed machine learning for on-device intelligence. *arXiv preprint arXiv:1610.02527*, 2016.
- <span id="page-16-2"></span>Yann LeCun, John Denker, and Sara Solla. Optimal brain damage. *Advances in neural information processing systems*, 2, 1989.
- <span id="page-16-8"></span>Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*, 2022a.
- <span id="page-16-3"></span>Jaehoon Lee, Samuel Schoenholz, Jeffrey Pennington, Ben Adlam, Lechao Xiao, Roman Novak, and Jascha Sohl-Dickstein. Finite versus infinite neural networks: an empirical study. *Advances in Neural Information Processing Systems*, 33:15156–15172, 2020.
- <span id="page-16-5"></span>Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *Proceedings of the 39th International Conference on Machine Learning*, pp. 12352–12364, 2022b.
- <span id="page-16-11"></span>Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Soft-label anonymous gastric x-ray image distillation. In *2020 IEEE International Conference on Image Processing (ICIP)*, pp. 305–309. IEEE, 2020a.
- <span id="page-16-12"></span>Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Compressed gastric image generation based on soft-label dataset distillation for medical data sharing. *Computer Methods and Programs in Biomedicine*, pp. 107189, 2022.
- <span id="page-16-13"></span>Tian Li, Anit Kumar Sahu, Ameet Talwalkar, and Virginia Smith. Federated learning: Challenges, methods, and future directions. *IEEE Signal Processing Magazine*, 37(3):50–60, 2020b.
- <span id="page-16-17"></span>Zhenguo Li, Fengwei Zhou, Fei Chen, and Hang Li. Meta-sgd: Learning to learn quickly for few-shot learning. *arXiv preprint arXiv:1707.09835*, 2017.
- <span id="page-16-0"></span>Hanxiao Liu, Karen Simonyan, and Yiming Yang. DARTS: Differentiable architecture search. In *International Conference on Learning Representations*, 2019.
- <span id="page-16-10"></span>Mengyang Liu, Shanchuan Li, Xinshi Chen, and Le Song. Graph condensation via receptive field distribution matching. *arXiv preprint arXiv:2206.13697*, 2022a.
- <span id="page-16-15"></span>Ping Liu, Xin Yu, and Joey Tianyi Zhou. Meta knowledge condensation for federated learning. *arXiv preprint arXiv:2209.14851*, 2022b.
- <span id="page-16-7"></span>Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *NeurIPS*, 2022c.
- <span id="page-16-4"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *Advances in Neural Information Processing Systems*, 2022.
- <span id="page-16-18"></span>Jonathan Lorraine, Paul Vicol, and David Duvenaud. Optimizing millions of hyperparameters by implicit differentiation. In *International Conference on Artificial Intelligence and Statistics*, pp. 1540–1552. PMLR, 2020.
- <span id="page-16-19"></span>Dougal Maclaurin, David Duvenaud, and Ryan Adams. Gradient-based hyperparameter optimization through reversible learning. In *International conference on machine learning*, pp. 2113–2122. PMLR, 2015.

- <span id="page-17-10"></span>Chris J. Maddison, Andriy Mnih, and Yee Whye Teh. The concrete distribution: A continuous relaxation of discrete random variables. In *International Conference on Learning Representations*, 2017.
- <span id="page-17-18"></span>Aleksander Madry, Aleksandar Makelov, Ludwig Schmidt, Dimitris Tsipras, and Adrian Vladu. Towards deep learning models resistant to adversarial attacks. In *International Conference on Learning Representations*, 2018.
- <span id="page-17-7"></span>Geoffrey J McLachlan and Thriyambakam Krishnan. *The EM algorithm and extensions*. John Wiley & Sons, 2007.
- <span id="page-17-17"></span>Ninareh Mehrabi, Fred Morstatter, Nripsuta Saxena, Kristina Lerman, and Aram Galstyan. A survey on bias and fairness in machine learning. *ACM Computing Surveys (CSUR)*, 54(6):1–35, 2021.
- <span id="page-17-3"></span>Luke Metz, Niru Maheswaranathan, Jeremy Nixon, Daniel Freeman, and Jascha Sohl-Dickstein. Understanding and correcting pathologies in the training of learned optimizers. In *International Conference on Machine Learning*, pp. 4556–4565. PMLR, 2019.
- <span id="page-17-0"></span>Baharan Mirzasoleiman, Jeff Bilmes, and Jure Leskovec. Coresets for data-efficient training of machine learning models. In *International Conference on Machine Learning*, pp. 6950–6960. PMLR, 2020.
- <span id="page-17-8"></span>Anshul Mittal, Noveen Sachdeva, Sheshansh Agrawal, Sumeet Agarwal, Purushottam Kar, and Manik Varma. Eclare: Extreme classification with label graph correlations. In *Proceedings of the Web Conference 2021*, WWW '21, 2021.
- <span id="page-17-11"></span>Maxim Naumov, Dheevatsa Mudigere, Hao-Jun Michael Shi, Jianyu Huang, Narayanan Sundaraman, Jongsoo Park, Xiaodong Wang, Udit Gupta, Carole-Jean Wu, Alisson G. Azzolini, Dmytro Dzhulgakov, Andrey Mallevich, Ilia Cherniavskii, Yinghai Lu, Raghuraman Krishnamoorthi, Ansha Yu, Volodymyr Kondratenko, Stephanie Pereira, Xianjie Chen, Wenlin Chen, Vijay Rao, Bill Jia, Liang Xiong, and Misha Smelyanskiy. Deep learning recommendation model for personalization and recommendation systems. *CoRR*, abs/1906.00091, 2019.
- <span id="page-17-6"></span>Radford M Neal. *Bayesian learning for neural networks*, volume 118. Springer Science & Business Media, 2012.
- <span id="page-17-4"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *International Conference on Learning Representations*, 2021a.
- <span id="page-17-5"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34, 2021b.
- <span id="page-17-13"></span>German I Parisi, Ronald Kemker, Jose L Part, Christopher Kanan, and Stefan Wermter. Continual lifelong learning with neural networks: A review. *Neural Networks*, 113:54–71, 2019.
- <span id="page-17-2"></span>Lorien Y Pratt. Discriminability-based transfer between neural networks. *Advances in neural information processing systems*, 5, 1992.
- <span id="page-17-15"></span>Aditya Ramesh, Prafulla Dhariwal, Alex Nichol, Casey Chu, and Mark Chen. Hierarchical text-conditional image generation with clip latents. *arXiv preprint arXiv:2204.06125*, 2022.
- <span id="page-17-16"></span>Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Björn Ommer. High-resolution image synthesis with latent diffusion models. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 10684–10695, 2022.
- <span id="page-17-14"></span>Andrea Rosasco, Antonio Carta, Andrea Cossu, Vincenzo Lomonaco, and Davide Bacciu. Distilled replay: Overcoming forgetting through synthetic samples. *arXiv preprint arXiv:2103.15851*, 2021.
- <span id="page-17-1"></span>Durga S, Rishabh Iyer, Ganesh Ramakrishnan, and Abir De. Training data subset selection for regression with controlled generalization error. In Marina Meila and Tong Zhang (eds.), *Proceedings of the 38th International Conference on Machine Learning*, volume 139 of *Proceedings of Machine Learning Research*, pp. 9202–9212. PMLR, 18–24 Jul 2021.
- <span id="page-17-9"></span>Noveen Sachdeva and Julian McAuley. *How Useful Are Reviews for Recommendation? A Critical Review and Potential Improvements*, pp. 1845–1848. SIGIR '20. Association for Computing Machinery, New York, NY, USA, 2020. ISBN 9781450380164. doi: 10.1145/3397271.3401281.
- <span id="page-17-12"></span>Noveen Sachdeva, Giuseppe Manco, Ettore Ritacco, and Vikram Pudi. Sequential variational autoencoders for collaborative filtering. In *Proceedings of the twelfth ACM international conference on web search and data mining*, pp. 600–608, 2019.

- <span id="page-18-9"></span>Noveen Sachdeva, Yi Su, and Thorsten Joachims. Off-policy bandits with deficient support. In *Proceedings of the 26th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining*, KDD '20, pp. 965–975, New York, NY, USA, 2020. Association for Computing Machinery. doi: 10.1145/3394486.3403139.
- <span id="page-18-11"></span>Noveen Sachdeva, Mehak Preet Dhaliwal, Carole-Jean Wu, and Julian McAuley. Infinite recommendation networks: A data-centric approach. In *Advances in Neural Information Processing Systems*, 2022a.
- <span id="page-18-6"></span>Noveen Sachdeva, Ziran Wang, Kyungtae Han, Rohit Gupta, and Julian McAuley. Gapformer: Fast autoregressive transformers meet rnns for personalized adaptive cruise control. In *2022 IEEE 25th International Conference on Intelligent Transportation Systems (ITSC)*, pp. 2528–2535, 2022b. doi: 10.1109/ITSC55140.2022.9922275.
- <span id="page-18-7"></span>Noveen Sachdeva, Carole-Jean Wu, and Julian McAuley. On sampling collaborative filtering datasets. In *Proceedings of the Fifteenth ACM International Conference on Web Search and Data Mining*, WSDM '22, 2022c.
- <span id="page-18-12"></span>Mattia Sangermano, Antonio Carta, Andrea Cossu, and Davide Bacciu. Sample condensation in online continual learning. In *2022 International Joint Conference on Neural Networks (IJCNN)*, pp. 01–08. IEEE, 2022.
- <span id="page-18-4"></span>Teven Le Scao, Angela Fan, Christopher Akiki, Ellie Pavlick, Suzana Ilić, Daniel Hesslow, Roman Castagné, Alexandra Sasha Luccioni, François Yvon, Matthias Gallé, et al. Bloom: A 176b-parameter open-access multilingual language model. *arXiv preprint arXiv:2211.05100*, 2022.
- <span id="page-18-2"></span>Robin Tibor Schirrmeister, Rosanne Liu, Sara Hooker, and Tonio Ball. When less is more: Simplifying inputs aids neural network understanding. *arXiv preprint arXiv:2201.05610*, 2022.
- <span id="page-18-10"></span>Tobias Schnabel, Adith Swaminathan, Ashudeep Singh, Navin Chandak, and Thorsten Joachims. Recommendations as treatments: Debiasing learning and evaluation. In *Proceedings of The 33rd International Conference on Machine Learning*, volume 48 of *Proceedings of Machine Learning Research*, pp. 1670–1679. PMLR, 2016.
- <span id="page-18-13"></span>Rui Song, Dai Liu, Dave Zhenyu Chen, Andreas Festag, Carsten Trinitis, Martin Schulz, and Alois Knoll. Federated learning via decentralized dataset distillation in resource-constrained edge environments. *arXiv preprint arXiv:2208.11311*, 2022.
- <span id="page-18-0"></span>Ben Sorscher, Robert Geirhos, Shashank Shekhar, Surya Ganguli, and Ari S. Morcos. Beyond neural scaling laws: beating power law scaling via data pruning. In Alice H. Oh, Alekh Agarwal, Danielle Belgrave, and Kyunghyun Cho (eds.), *Advances in Neural Information Processing Systems*, 2022.
- <span id="page-18-8"></span>Harald Steck. Embarrassingly shallow autoencoders for sparse data. In *The World Wide Web Conference*, 2019.
- <span id="page-18-5"></span>Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *2021 International Joint Conference on Neural Networks (IJCNN)*, pp. 1–8. IEEE, 2021.
- <span id="page-18-14"></span>Pei Sun, Henrik Kretzschmar, Xerxes Dotiwalla, Aurelien Chouard, Vijaysai Patnaik, Paul Tsui, James Guo, Yin Zhou, Yuning Chai, Benjamin Caine, et al. Scalability in perception for autonomous driving: Waymo open dataset. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 2446–2454, 2020.
- <span id="page-18-3"></span>Romal Thoppilan, Daniel De Freitas, Jamie Hall, Noam Shazeer, Apoorv Kulshreshtha, Heng-Tze Cheng, Alicia Jin, Taylor Bos, Leslie Baker, Yu Du, et al. Lamda: Language models for dialog applications. *arXiv preprint arXiv:2201.08239*, 2022.
- <span id="page-18-1"></span>Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J. Gordon. An empirical study of example forgetting during deep neural network learning. In *International Conference on Learning Representations*, 2019.
- <span id="page-18-15"></span>Hugo Touvron, Thibaut Lavril, Gautier Izacard, Xavier Martinet, Marie-Anne Lachaux, Timothée Lacroix, Baptiste Rozière, Naman Goyal, Eric Hambro, Faisal Azhar, et al. Llama: Open and efficient foundation language models. *arXiv preprint arXiv:2302.13971*, 2023.
- <span id="page-18-16"></span>Luis Vicente, Gilles Savard, and Joaquim Júdice. Descent approaches for quadratic bilevel programming. *Journal of Optimization theory and applications*, 81(2):379–399, 1994.
- <span id="page-18-18"></span>Paul Vicol, Luke Metz, and Jascha Sohl-Dickstein. Unbiased gradient estimation in unrolled computation graphs with persistent evolution strategies. In *International Conference on Machine Learning*, pp. 10553–10563. PMLR, 2021.
- <span id="page-18-17"></span>Paul Vicol, Jonathan P Lorraine, Fabian Pedregosa, David Duvenaud, and Roger B Grosse. On implicit bias in overparameterized bilevel optimization. In *International Conference on Machine Learning*. PMLR, 2022.

- <span id="page-19-8"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 12196–12205, 2022.
- <span id="page-19-17"></span>Ruoxi Wang, Rakesh Shivanna, Derek Cheng, Sagar Jain, Dong Lin, Lichan Hong, and Ed Chi. Dcn v2: Improved deep & cross network and practical lessons for web-scale learning to rank systems. In *Proceedings of the web conference 2021*, pp. 1785–1797, 2021.
- <span id="page-19-2"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-19-0"></span>Max Welling. Herding dynamical weights to learn. In *Proceedings of the 26th Annual International Conference on Machine Learning*, ICML '09, 2009.
- <span id="page-19-14"></span>Felix Wiewel and Bin Yang. Condensed composite memory continual learning. In *2021 International Joint Conference on Neural Networks (IJCNN)*, pp. 1–8. IEEE, 2021.
- <span id="page-19-10"></span>T. Wolf, L. Debut, V. Sanh, J. Chaumond, C. Delangue, A. Moi, P. Cistac, T. Rault, R. Louf, M. Funtowicz, J. Davison, S. Shleifer, P. von Platen, C. Ma, Y. Jernite, J. Plu, C. Xu, T. Le Scao, S. Gugger, M. Drame, Q. Lhoest, and A. Rush. Transformers: State-of-the-art natural language processing. In *Proceedings of the 2020 Conference on Empirical Methods in Natural Language Processing: System Demonstrations*, pp. 38–45, Online, October 2020. Association for Computational Linguistics. doi: 10.18653/v1/2020.emnlp-demos.6.
- <span id="page-19-1"></span>D.H. Wolpert and W.G. Macready. No free lunch theorems for optimization. *IEEE Transactions on Evolutionary Computation*, 1(1):67–82, 1997. doi: 10.1109/4235.585893.
- <span id="page-19-12"></span>Carole-Jean Wu, Ramya Raghavendra, Udit Gupta, Bilge Acun, Newsha Ardalani, Kiwan Maeng, Gloria Chang, Fiona Aga, Jinshi Huang, Charles Bai, et al. Sustainable ai: Environmental implications, challenges and opportunities. *Proceedings of Machine Learning and Systems*, 4:795–813, 2022.
- <span id="page-19-11"></span>Shiwen Wu, Fei Sun, Wentao Zhang, Xu Xie, and Bin Cui. Graph neural networks in recommender systems: a survey. *ACM Computing Surveys (CSUR)*, 2020.
- <span id="page-19-13"></span>Shu Wu, Yuyuan Tang, Yanqiao Zhu, Liang Wang, Xing Xie, and Tieniu Tan. Session-based recommendation with graph neural networks. In *Proceedings of the AAAI conference on artificial intelligence*, 2019.
- <span id="page-19-3"></span>Yuhuai Wu, Mengye Ren, Renjie Liao, and Roger Grosse. Understanding short-horizon bias in stochastic metaoptimization. In *International Conference on Learning Representations*, 2018.
- <span id="page-19-15"></span>Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. Feddm: Iterative distribution matching for communication-efficient federated learning. *arXiv preprint arXiv:2207.09653*, 2022.
- <span id="page-19-18"></span>Yumo Xu and Shay B Cohen. Stock movement prediction from tweets and historical prices. In *Proceedings of the 56th Annual Meeting of the Association for Computational Linguistics (Volume 1: Long Papers)*, pp. 1970–1979, 2018.
- <span id="page-19-6"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pp. 12674–12685. PMLR, 2021.
- <span id="page-19-9"></span>Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. *arXiv preprint arXiv:2204.07513*, 2022.
- <span id="page-19-7"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision (WACV)*, 2023.
- <span id="page-19-4"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021.
- <span id="page-19-19"></span>Xiao Zhou, Renjie Pi, Weizhong Zhang, Yong Lin, Zonghao Chen, and Tong Zhang. Probabilistic bilevel coreset selection. In *International Conference on Machine Learning*, pp. 27287–27302. PMLR, 2022a.
- <span id="page-19-16"></span>Yanlin Zhou, George Pu, Xiyao Ma, Xiaolin Li, and Dapeng Wu. Distilled one-shot federated learning. *arXiv preprint arXiv:2009.07999*, 2020.
- <span id="page-19-5"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *Advances in Neural Information Processing Systems*, 2022b.

<span id="page-20-0"></span>

## **A Notation**

### **Dataset related**

| $\mathcal{D} \triangleq \{(x_i \in \mathcal{X}, y_i \in \mathcal{Y}) _{i=1}^{ \mathcal{D} }\}$ | The target dataset to be distilled               |
|------------------------------------------------------------------------------------------------|--------------------------------------------------|
| $\mathcal{X}$                                                                                  | Data domain                                      |
| $\mathcal{Y}$                                                                                  | Predictand domain                                |
| $\mathcal{C}$                                                                                  | Set of unique classes in $\mathcal Y$            |
| $\mathcal{D}^c \triangleq \{(x_i, y_i)   y_i = c\}_{i=1}^{ \mathcal{D} }$                      | Portion of $\mathcal{D}$ with class $c$          |
| $\mathbf{X} \triangleq [x_i]_{i=1}^{ \mathcal{D} }$                                            | Matrix of all features in $\mathcal{D}$          |
| $\mathbf{Y} \triangleq [y_i]_{i=1}^{ \mathcal{D} }$                                            | Matrix of all predictands in $\mathcal{D}$       |
| $n$                                                                                            | Size of data summary                             |
| $\mathcal{D}_{syn} \triangleq \{(\tilde{x}_i, \tilde{y}_i)\}_{i=1}^{n}$                        | Data summary                                     |
| $\mathcal{D}_{syn}^c \triangleq \{(\tilde{x}_i, \tilde{y}_i)   \tilde{y}_i = c\}_{i=1}^{n}$    | Portion of $\mathcal{D}_{syn}$ with class $c$    |
| $\mathbf{X}_{syn} \triangleq [\tilde{x}_i]_{i=1}^{n}$                                          | Matrix of all features in $\mathcal{D}_{syn}$    |
| $\mathbf{Y}_{syn} \triangleq [\tilde{y}_i]_{i=1}^{n}$                                          | Matrix of all predictands in $\mathcal{D}_{syn}$ |

### **Learning related**

| Learning algorithm parameterized by $\theta$   |
|------------------------------------------------|
| Twice-differentiable cost function             |
| Expected loss of $\Phi$ on $\mathcal D$        |
| Expected loss of $\Phi$ on $\mathcal{D}_{syn}$ |
|                                                |

 $\mathbb{E}_{x}[f(x)] \triangleq \sum_{x}$ 

### **General**

| $\dim(\mathcal{A})$            | Size of basis of $A$                                  |
|--------------------------------|-------------------------------------------------------|
| $ \mathcal{A} $                | Number of elements in $A$                             |
| sup                            | Supremum                                              |
| $\arg \min_{\theta} f(\theta)$ | Optimum value of $\theta$ which minimizes $f(\theta)$ |
| $\sum_{x} p(x) \cdot f(x)$     | Expected value of $f(x)$ when domain of x is discrete |
# Displacement convexity I

Convexity plays a prominent role in analysis in general. It is most generally used in a vector space V: A function  $F: V \to \mathbb{R} \cup \{+\infty\}$  is said to be convex if for all  $x, y \in V$ ,

 $\forall t \in [0,1]$  $((1-t)x + ty) \le (1-t) F(x) + t F(y).$  (16.1)

But convexity is also a metric notion: In short, convexity in a metric space means convexity along geodesics. Consequently, geodesic spaces are a natural setting in which to define convexity:

Definition 16.1 (Convexity in a geodesic space). Let  $(\mathcal{X}, d)$  be a complete geodesic space. Then a function  $F : \mathcal{X} \to \mathbb{R} \cup \{+\infty\}$  is said to be geodesically convex, or just convex, if for any constant-speed geodesic path  $(\gamma_t)_{0 \leq t \leq 1}$  valued in X,

$$
\forall t \in [0, 1] \qquad F(\gamma_t) \le (1 - t) F(\gamma_0) + t F(\gamma_1). \tag{16.2}
$$

It is said to be weakly convex if for any  $x_0$ ,  $x_1$  in X there exists at least one constant-speed geodesic path  $(\gamma_t)_{0 \leq t \leq 1}$  with  $\gamma_0 = x_0$ ,  $\gamma_1 = x_1$ , such that inequality  $(16.2)$  holds true.

It is a natural problem to identify functionals that are convex on the Wasserstein space. In his 1994 PhD thesis, McCann established and used the convexity of certain functionals on  $P_2(\mathbb{R}^n)$  to prove the uniqueness of their minimizers. Since then, his results have been generalized; yet almost all examples which have been treated so far belong to the general class

$$
\mathcal{F}(\mu) = \int_{\mathcal{X}^k} I(x_1, \dots, x_k) d\mu(x_1) \dots d\mu(x_k) + \int_{\mathcal{X}} U\left(\frac{d\mu}{d\nu}\right) d\nu,
$$

# 450 16 Displacement convexity I

where  $I(x_1,...,x_k)$  is a certain "k-particle interaction potential", U is a nice function  $\mathbb{R}_+ \to \mathbb{R}$ , and  $\nu$  is a reference measure.

In this and the next chapter I shall consider the convexity problem on a general Riemannian manifold  $M$ , in the case  $I = 0$ , so the functionals under study will be the functionals  $U_{\nu}$  defined by

$$
U_{\nu}(\mu) = \int_M U(\rho) d\nu, \qquad \mu = \rho \nu.
$$
 (16.3)

As a start, I shall give some reminders about the notion of convexity and its refinements; then I shall make these notions more explicit in the case of the Wasserstein space  $P_2(M)$ . In the last section of this chapter I shall use Otto's calculus to guess sufficient conditions under which  $U_{\nu}$ satisfies some interesting convexity properties (Guesses 16.6 and 16.7).

Let the reader not be offended if I strongly insist that convexity in the metric space  $P_2(M)$  has nothing to do with the convex structure of the space of probability measures. The former concept will be called "convexity along optimal transport" or displacement convexity.

### Reminders on convexity: differential and integral conditions

The material in this section has nothing to do with optimal transport, and is, for the most part, rather standard.

It is well-known that a function  $F : \mathbb{R}^n \to \mathbb{R}$  is convex, in the sense of (16.1), if and only if it satisfies

$$
\nabla^2 F \ge 0 \tag{16.4}
$$

(nonnegative Hessian) on  $\mathbb{R}^n$ . The latter inequality should generally be understood in distribution sense, but let me just forget about this subtlety which is not essential here.

The inequality (16.4) is a differential condition, in contrast with the "integral" condition (16.1). There is a more general principle relating a lower bound on the Hessian (differential condition) to a convexity-type inequality (integral condition). It can be stated in terms of the onedimensional Green function (of the Laplace operator with Dirichlet boundary conditions). That Green function is the nonnegative kernel  $G(s,t)$  such that for all functions  $\varphi \in C([0,1];\mathbb{R}) \cap C^2((0,1);\mathbb{R}),$ 

Reminders on convexity: differential and integral conditions 451

$$
\varphi(t) = (1-t)\,\varphi(0) + t\,\varphi(1) - \int_0^1 \ddot{\varphi}(s)\,G(s,t)\,ds. \tag{16.5}
$$

It is easy to give an explicit expression for  $G$  (see Figure 16.1):

$$
G(s,t) = \begin{cases} s(1-t) & \text{if } s \le t \\ t(1-s) & \text{if } s \ge t. \end{cases} \tag{16.6}
$$

Then formula (16.5) actually extends to arbitrary continuous functions  $\varphi$  on [0, 1], provided that  $\ddot{\varphi}$  (taken in a distribution sense) is bounded below by a real number.

Image /page/2/Figure/5 description: The image shows a graph with the x-axis labeled 's' and the y-axis unlabeled. The graph starts at the origin (0,0), rises linearly to a peak at the point (t, y\_max), and then descends linearly to the point (1,0). A dashed vertical line is drawn from the peak at (t, y\_max) down to the x-axis at 't'.

Fig. 16.1. The Green function  $G(s, t)$  as a function of s.

The next statement provides the equivalence between several differential and integral convexity conditions in a rather general setting.

Proposition 16.2 (Convexity and lower Hessian bounds). Let  $(M,g)$  be a Riemannian manifold, and let  $\Lambda = \Lambda(x,v)$  be a continuous quadratic form on TM; that is, for any x,  $\Lambda(x, \cdot)$  is a quadratic form in  $v$ , and it depends continuously on  $x$ . Assume that for any constantspeed geodesic  $\gamma : [0, 1] \to M$ ,

$$
\lambda[\gamma] := \inf_{0 \le t \le 1} \frac{\Lambda(\gamma_t, \dot{\gamma}_t)}{|\dot{\gamma}_t|^2} > -\infty. \tag{16.7}
$$

Then, for any function  $F \in C^2(M)$ , the following statements are equivalent:

$$
(i) \nabla^2 F \ge A;
$$

(ii) For any constant-speed, minimizing geodesic  $\gamma : [0,1] \to M$ ,

$$
F(\gamma_t) \le (1-t) F(\gamma_0) + t F(\gamma_1) - \int_0^1 \Lambda(\gamma_s, \dot{\gamma}_s) G(s, t) ds;
$$

(iii) For any constant-speed, minimizing geodesic  $\gamma : [0,1] \to M$ ,

$$
F(\gamma_1) \ge F(\gamma_0) + \langle \nabla F(\gamma_0), \dot{\gamma}_0 \rangle + \int_0^1 \Lambda(\gamma_t, \dot{\gamma}_t) (1 - t) dt;
$$

(iv) For any constant-speed, minimizing geodesic  $\gamma : [0,1] \to M$ ,

$$
\langle \nabla F(\gamma_1), \dot{\gamma}_1 \rangle - \langle \nabla F(\gamma_0), \dot{\gamma}_0 \rangle \ge \int_0^1 A(\gamma_t, \dot{\gamma}_t) dt.
$$

When these properties are satisfied,  $F$  is said to be  $\Lambda$ -convex. The equivalence is still preserved if conditions  $(ii)$ ,  $(iii)$  and  $(iv)$  are respectively replaced by the following a priori weaker conditions:

(ii') For any constant-speed, minimizing geodesic  $\gamma : [0,1] \to M$ ,

$$
F(\gamma_t) \le (1-t) F(\gamma_0) + t F(\gamma_1) - \lambda[\gamma] \frac{t(1-t)}{2} d(\gamma_0, \gamma_1)^2;
$$

(iii') For any constant-speed, minimizing geodesic  $\gamma : [0, 1] \to M$ ,

$$
F(\gamma_1) \ge F(\gamma_0) + \langle \nabla F(\gamma_0), \dot{\gamma}_0 \rangle + \lambda[\gamma] \frac{d(\gamma_0, \gamma_1)^2}{2};
$$

(iv') For any constant-speed, minimizing geodesic  $\gamma : [0,1] \to M$ ,

$$
\langle \nabla F(\gamma_1), \dot{\gamma}_1 \rangle - \langle \nabla F(\gamma_0), \dot{\gamma}_0 \rangle \ge \lambda[\gamma] d(\gamma_0, \gamma_1)^2.
$$

**Remark 16.3.** In the particular case when  $\Lambda$  is equal to  $\lambda g$  for some constant  $\lambda \in \mathbb{R}$ , Property (ii) reduces to Property (ii) with  $\lambda[\gamma] = \lambda$ . Indeed, since  $\gamma$  has constant speed,

$$
F(\gamma_t) \le (1-t) F(\gamma_0) + t F(\gamma_1) - \lambda \int_0^1 g(\gamma_s, \dot{\gamma}_s) G(s, t) ds
$$

$$
= (1-t) F(\gamma_0) + t F(\gamma_1) - \lambda d(\gamma_0, \gamma_1)^2 \int_0^1 G(s, t) ds.
$$

By plugging the function  $\varphi(t) = t^2$  into (16.5) one sees that  $\int_0^1 G(s, t) ds =$  $t(1-t)/2$ . So (ii) indeed reduces to

$$
F(\gamma_t) \le (1-t) F(\gamma_0) + t F(\gamma_1) - \frac{\lambda t (1-t)}{2} d(\gamma_0, \gamma_1)^2.
$$
 (16.8)

**Definition 16.4 (** $\Lambda$ **-convexity).** Let M be a Riemannian manifold, and let  $\Lambda = \Lambda(x, v)$  be a continuous quadratic form on M, satisfying (16.7). A function  $F : M \to \mathbb{R} \cup \{+\infty\}$  is said to be  $\Lambda$ -convex if Property (ii) in Proposition 16.2 is satisfied. In the case when  $\Lambda = \lambda q$ ,  $\lambda \in \mathbb{R}$ , F will be said to be  $\lambda$ -convex; this means that inequality (16.8) is satisfied. In particular, 0-convexity is just plain convexity.

Proof of Proposition 16.2. The arguments in this proof will come again several times in the sequel, in various contexts.

Assume that (i) holds true. Consider  $x_0$  and  $x_1$  in M, and introduce a constant-speed minimizing geodesic  $\gamma$  joining  $\gamma_0 = x_0$  to  $\gamma_1 = x_1$ . Then

$$
\frac{d^2}{dt^2}F(\gamma_t) = \left\langle \nabla^2 F(\gamma_t) \cdot \dot{\gamma}_t, \, \dot{\gamma}_t \right\rangle \ge A(\gamma_t, \dot{\gamma}_t).
$$

Then Property (ii) follows from identity (16.5) with  $\varphi(t) := F(\gamma_t)$ .

As for Property (iii), it can be established either by dividing the inequality in (ii) by  $t > 0$ , and then letting  $t \to 0$ , or directly from (i) by using the Taylor formula at order 2 with  $\varphi(t) = F(\gamma_t)$  again. Indeed,  $\dot{\varphi}(0) = \langle \nabla F(\gamma_0), \dot{\gamma}_0 \rangle$ , while  $\ddot{\varphi}(t) \geq \Lambda(\gamma_t, \dot{\gamma}_t)$ .

To go from (iii) to (iv), replace the geodesic  $\gamma_t$  by the geodesic  $\gamma_{1-t}$ , to get

$$
F(\gamma_0) \ge F(\gamma_1) - \langle \nabla F(\gamma_1), \dot{\gamma}_1 \rangle + \int_0^1 \Lambda(\gamma_{1-t}, \dot{\gamma}_{1-t}) (1-t) dt.
$$

After changing variables in the last integral, this is

$$
F(\gamma_0) \ge F(\gamma_1) - \langle \nabla F(\gamma_1), \dot{\gamma}_1 \rangle + \int_0^1 \Lambda(\gamma_t, \dot{\gamma}_t) \, t \, dt,
$$

and by adding up (iii), one gets Property (iv).

So far we have seen that (i)  $\Rightarrow$  (ii)  $\Rightarrow$  (iii)  $\Rightarrow$  (iv). To complete the proof of equivalence it is sufficient to check that (iv') implies (i).

So assume (iv'). From the identity

$$
\langle \nabla F(\gamma_1), \dot{\gamma}_1 \rangle - \langle \nabla F(\gamma_0), \dot{\gamma}_0 \rangle = \int_0^1 \nabla^2 F(\gamma_t)(\dot{\gamma}_t) dt
$$

and (iv'), one deduces that for all geodesic paths  $\gamma$ ,

$$
\lambda[\gamma] d(\gamma_0, \gamma_1)^2 \le \int_0^1 \nabla^2 F(\gamma_t)(\dot{\gamma}_t) dt.
$$
 (16.9)

# 454 16 Displacement convexity I

Choose  $(x_0, v_0)$  in TM, with  $v_0 \neq 0$ , and  $\gamma(t) = \exp_{x_0}(\varepsilon tv_0)$ , where  $\varepsilon > 0$ ; of course  $\gamma$  depends implicitly on  $\varepsilon$ . Note that  $d(\gamma_0, \gamma_1) = \varepsilon |v_0|$ . Write

$$
\lambda[\gamma] \frac{d(\gamma_0, \gamma_1)^2}{\varepsilon^2} \le \int_0^1 \nabla^2 F(\gamma_t) \left(\frac{\dot{\gamma}_t}{\varepsilon}\right) dt. \tag{16.10}
$$

As  $\varepsilon \to 0$ ,  $(\gamma_t, \dot{\gamma_t}) \simeq (x_0, \varepsilon v_0)$  in TM, so

$$
\lambda[\gamma] = \inf_{0 \le t \le 1} \frac{\Lambda(\gamma_t, \dot{\gamma}_t)}{|\dot{\gamma}_t|^2} = \inf_{0 \le t \le 1} \frac{\Lambda(\gamma_t, \dot{\gamma}_t/\varepsilon)}{|\dot{\gamma}_t/\varepsilon|^2} \xrightarrow[\varepsilon \to 0]{} \frac{\Lambda(x_0, v_0)}{|v_0|^2}.
$$

Thus the left-hand side of (16.10) converges to  $A(x_0, v_0)$ . On the other hand, since  $\nabla^2 F$  is continuous, the right-hand side obviously converges to  $\nabla^2 F(x_0)(v_0)$ . Property (i) follows. to  $\nabla^2 F(x_0)(v_0)$ . Property (i) follows.

### Displacement convexity

I shall now discuss convexity in the context of optimal transport, replacing the manifold M of the previous section by the geodesic space  $P_2(M)$ . For the moment I shall only consider measures that are absolutely continuous with respect to the volume on  $M$ , and denote by  $P_2^{\rm ac}(M)$  the space of such measures. It makes sense to study convexity in  $P_2^{\text{ac}}(M)$  because this is a *geodesically convex* subset of  $P_2(M)$ : By Theorem 8.7, a displacement interpolation between any two absolutely continuous measures is itself absolutely continuous. (Singular measures will be considered later, together with singular metric spaces, in Part III.)

So let  $\mu_0$  and  $\mu_1$  be two probability measures on M, absolutely continuous with respect to the volume element, and let  $(\mu_t)_{0 \leq t \leq 1}$  be the displacement interpolation between  $\mu_0$  and  $\mu_1$ . Recall from Chapter 13 that this displacement interpolation is uniquely defined, and characterized by the formulas  $\mu_t = (T_t)_{\#}\mu_0$ , where

$$
T_t(x) = \exp_x(t \nabla \psi(x)),\tag{16.11}
$$

and  $\psi$  is  $d^2/2$ -convex. (Forget about the symbol if you don't like it.) Moreover,  $T_t$  is injective for  $t < 1$ ; so for any  $t < 1$  it makes sense to define the velocity field  $v(t,x)$  on  $T_t(M)$  by

$$
v(t, T_t(x)) = \frac{d}{dt}T_t(x),
$$

and one also has

$$
v(t,T_t(x)) = \widetilde{\nabla}\psi_t(T_t(x)),
$$

where  $\psi_t$  is a solution at time t of the quadratic Hamilton–Jacobi equation with initial datum  $\psi_0 = \psi$ .

The next definition adapts the notions of convexity,  $\lambda$ -convexity and  $\Lambda$ -convexity to the setting of optimal transport. Below  $\lambda$  is a real number that might nonnegative or nonpositive, while  $\Lambda = \Lambda(\mu, v)$  defines for each probability measure  $\mu$  a quadratic form on vector fields  $v : M \to TM$ .

Definition 16.5 (Displacement convexity). With the above notation, a functional  $F: P_2^{\text{ac}}(M) \to \mathbb{R} \cup \{+\infty\}$  is said to be:

• displacement convex if, whenever  $(\mu_t)_{0 \leq t \leq 1}$  is a (constant-speed, minimizing) geodesic in  $P_2^{\text{ac}}(M)$ ,

$$
\forall t \in [0,1] \qquad F(\mu_t) \le (1-t) \, F(\mu_0) + t \, F(\mu_1);
$$

•  $\lambda$ -displacement convex, if, whenever  $(\mu_t)_{0 \leq t \leq 1}$  is a (constantspeed, miminizing) geodesic in  $P_2^{\text{ac}}(M)$ ,

$$
\forall t \in [0,1] \qquad F(\mu_t) \le (1-t) F(\mu_0) + t F(\mu_1) - \frac{\lambda t (1-t)}{2} W_2(\mu_0, \mu_1)^2;
$$

• A-displacement convex, if, whenever  $(\mu_t)_{0 \leq t \leq 1}$  is a (constantspeed, minimizing) geodesic in  $P_2^{\text{ac}}(M)$ , and  $(\psi_t)_{0 \leq t \leq 1}$  is an associated solution of the Hamilton–Jacobi equation,

$$
\forall t \in [0,1] \qquad F(\mu_t) \le (1-t) \, F(\mu_0) + t \, F(\mu_1) - \int_0^1 \Lambda(\mu_s, \widetilde{\nabla} \psi_s) \, G(s,t) \, ds,
$$

where  $G(s,t)$  is the one-dimensional Green function of (16.6). (It is assumed that  $\Lambda(\mu_s, \nabla \psi_s) G(s,t)$  is bounded below by an integrable function of  $s \in [0,1]$ .)

Of course these definitions are more and more general: Λ-displacement convexity reduces to  $\lambda$ -displacement convexity when  $\Lambda(\mu, v) = \lambda ||v||^2_{L^2(\mu)}$ ; and this in turn reduces to plain displacement convexity when  $\lambda = 0$ .

# Displacement convexity from curvature-dimension bounds

The question is whether the previously defined concepts apply to functionals of the form  $U_{\nu}$ , as in (16.3). Of course Proposition 16.2 does not apply, because neither  $P_2(M)$  nor  $P_2^{\text{ac}}(M)$  are smooth manifolds. However, if one believes in Otto's formalism, then one can hope that displacement convexity,  $\lambda$ -displacement convexity,  $\Lambda$ -displacement convexity of  $U_{\nu}$  would be respectively equivalent to

$$
\operatorname{Hess}_{\mu} U_{\nu} \ge 0, \qquad \operatorname{Hess}_{\mu} U_{\nu} \ge \lambda, \qquad \operatorname{Hess}_{\mu} U_{\nu}(\dot{\mu}) \ge \Lambda(\mu, \dot{\mu}), \tag{16.12}
$$

where  $\text{Hess}_{\mu} U_{\nu}$  stands for the formal Hessian of  $U_{\nu}$  at  $\mu$  (which was computed in Chapter 15),  $\lambda$  is shorthand for  $\lambda \|\cdot\|_{L^2(\mu)}^2$ , and  $\mu$  is identified with  $\nabla \psi$  via the usual continuity equation

$$
\dot{\mu} + \nabla \cdot (\nabla \psi \,\mu) = 0.
$$

Let us try to identify simple sufficient conditions on the manifold M, the reference measure  $\nu$  and the energy function U, for (16.12) to hold. This quest is, for the moment, just formal; it will be checked later, without any reference to Otto's formalism, that our guess is correct.

To identify conditions for displacement convexity I shall use again the formalism of Chapter 14. Equip the Riemannian manifold M with a reference measure  $\nu = e^{-V}$  vol, where V is a smooth function on M, and assume that the resulting space satisfies the curvature-dimension bound  $CD(K, N)$ , as in Theorem 14.8, for some  $N \in [1, \infty]$  and  $K \in \mathbb{R}$ . Everywhere in the sequel,  $\rho$  will stand for the density of  $\mu$  with respect to  $\nu$ .

Consider a continuous function  $U : \mathbb{R}_+ \to \mathbb{R}$ . I shall assume that U is *convex* and  $U(0) = 0$ . The latter condition is quite natural from a physical point of view (no matter  $\Rightarrow$  no energy). The convexity assumption might seem more artificial, and to justify it I will argue that (i) the convexity of U is necessary for  $U_{\nu}$  to be lower semicontinuous with respect to the weak topology induced by the metric  $W_2$ ; (ii) if one imposes the nonnegativity of the pressure  $p(r) = r U'(r) - U(r)$ , which is natural from the physical point of view, then conditions for displacement convexity will be in the end quite more stringent than just convexity of  $U$ ; (iii) the convexity of  $U$  automatically implies the nonnegativity of the pressure, since  $p(r) = r U'(r) - U(r) = r U'(r) - U(r) + U(0) \ge 0$ . For simplicity I shall also impose that  $U$  is twice continuously differentiable

everywhere in  $(0, +\infty)$ . Finally, I shall assume that  $\psi$  in (16.11) is  $C^2$ , and I shall avoid the discussion about the domain of definition of  $U_{\nu}$ by just considering compactly supported probability measures. Then, from (15.13) and (14.51),

$$
\operatorname{Hess}_{\mu} U_{\nu}(\mu) = \int_{M} \Gamma_{2}(\psi) p(\rho) d\nu + \int_{M} (L\psi)^{2} p_{2}(\rho) d\nu \qquad (16.13)
$$
  
\n
$$
\geq \int_{M} \operatorname{Ric}_{N,\nu}(\nabla \psi) p(\rho) d\nu + \int_{M} (L\psi)^{2} \left[ p_{2} + \frac{p}{N} \right] (\rho) d\nu
$$
  
\n
$$
\geq K \int |\nabla \psi|^{2} p(\rho) d\nu + \int (L\psi)^{2} \left[ p_{2} + \frac{p}{N} \right] (\rho) d\nu.
$$

$$
\geq K \int_M |\nabla \psi|^2 p(\rho) d\nu + \int_M (L\psi)^2 \left[ p_2 + \frac{P}{N} \right] (\rho) d\nu. \tag{16.15}
$$

To get a bound on this expression, it is natural to assume that

$$
p_2 + \frac{p}{N} \ge 0.
$$
\n(16.16)

The set of all functions  $U$  for which  $(16.16)$  is satisfied will be called the displacement convexity class of dimension  $N$  and denoted by  $DC_N$ . A typical representative of  $DC_N$ , for which (16.16) holds as an equality, is  $U = U_N$  defined by

$$
U_N(\rho) = \begin{cases} -N(\rho^{1-\frac{1}{N}} - \rho) & (1 < N < \infty) \\ \rho \log \rho & (N = \infty). \end{cases}
$$
 (16.17)

These functions will come back again and again in the sequel, and the associated functionals will be denoted by  $H_{N,\nu}$ .

If inequality (16.16) holds true, then

$$
\operatorname{Hess}_{\mu} U_{\nu} \geq K \Lambda_U,
$$

where

$$
\Lambda_U(\mu, \dot{\mu}) = \int_M |\nabla \psi|^2 p(\rho) d\nu.
$$
\n(16.18)

So the conclusion is as follows:

Guess 16.6. Let M be a Riemannian manifold satisfying a curvaturedimension bound  $CD(K, N)$  for some  $K \in \mathbb{R}$ ,  $N \in (1, \infty]$ , and let U satisfy (16.16); then  $U_{\nu}$  is  $K\Lambda_{U}$ -displacement convex.

# 458 16 Displacement convexity I

Actually, there should be an equivalence between the two statements in Guess 16.6. To see this, assume that  $U_{\nu}$  is  $K\Lambda_{U}$ -displacement convex; pick up an arbitrary point  $x_0 \in M$ , and a tangent vector  $v_0 \in T_{x_0}M$ ; consider the particular function  $U = U_N$ , a probability measure  $\mu$  which is very much concentrated close to  $x_0$ , and a function  $\psi$  such that  $\nabla \psi(x_0) = v_0$  and  $\Gamma_2(\psi) + (L\psi)^2/N = \text{Ric}_{N,\nu}(v_0)$  (as in the proof of Theorem 14.8). Then, on the one hand,

$$
K\Lambda_U(\mu,\dot{\mu}) = K \int |\nabla \psi|^2 \,\rho^{1-\frac{1}{N}} \,d\nu \simeq K|v_0|^2 \int \rho^{1-\frac{1}{N}} \,d\nu; \qquad (16.19)
$$

on the other hand, by the choice of  $U$ ,

$$
\operatorname{Hess}_{\mu} U_{\nu}(\dot{\mu}) = \int \left[ \varGamma_2(\psi) + \frac{(L\psi)^2}{N} \right] \rho^{1-\frac{1}{N}} d\nu,
$$

but then since  $\mu$  is concentrated around  $x_0$ , this is well approximated by

$$
\[F_2(\psi) + \frac{(L\psi)^2}{N}\] (x_0) \int \rho^{1-\frac{1}{N}} d\nu = \text{Ric}_{N,\nu}(v_0) \int \rho^{1-\frac{1}{N}} d\nu.
$$

Comparing the latter expression with (16.19) shows that  $\text{Ric}_{N,\nu}(v_0) \geq$  $K|v_0|^2$ . Since  $x_0$  and  $v_0$  were arbitrary, this implies  $Ric_{N,\nu} \geq K$ . Note that this reasoning only used the functional  $H_{N,\nu} = (U_N)_{\nu}$ , and probability measures  $\mu$  that are very concentrated around a given point.

This heuristic discussion is summarized in the following:

**Guess 16.7.** If, for each  $x_0 \in M$ ,  $H_{N,\nu}$  is  $KA_U$ -displacement convex when applied to probability measures that are supported in a small neighborhood of  $x_0$ , then M satisfies the CD(K, N) curvature-dimension bound.

**Example 16.8.** Condition CD(0,  $\infty$ ) with  $\nu =$  vol just means Ric  $\geq$  0, and the statement  $U \in \mathcal{DC}_{\infty}$  just means that the iterated pressure  $p_2$ is nonnegative. The typical example is when  $U(\rho) = \rho \log \rho$ , and then the corresponding functional is

$$
H(\mu) = \int \rho \log \rho \, d\text{vol}, \qquad \mu = \rho \,\text{vol}.
$$

Then the above considerations suggest that the following statements are equivalent:

(i) Ric  $\geq 0$ ;

(ii) If the nonlinearity  $U$  is such that the nonnegative iterated pressure  $p_2$  is nonnegative, then the functional  $U_{\text{vol}}$  is displacement convex;

(iii)  $H$  is displacement convex;

(iii') For any  $x_0 \in M$ , the functional H is displacement convex when applied to probability measures that are supported in a small neighborhood of  $x_0$ .

Example 16.9. The above considerations also suggest that the inequality Ric  $\geq Kg$  is equivalent to the K-displacement convexity of the H functional, whatever the value of  $K \in \mathbb{R}$ .

These guesses will be proven and generalized in the next chapter.

### A fluid mechanics feeling for Ricci curvature

Ricci curvature is familiar to physicists because it plays a crucial role in Einstein's theory of general relativity. But what we have been discovering in this chapter is that Ricci curvature can also be given a physical interpretation in terms of classical fluid mechanics. To provide the reader with a better feeling of this new point of view, let us imagine how two physicists, the first one used to relativity and light propagation, the second one used to fluid mechanics, would answer the following question: Describe in an informal way an experiment that can determine whether we live in a nonnegatively Ricci-curved space.

The light source test: Take a small light source, and try to determine its volume by looking at it from a distant position. If you systematically overestimate the volume of the light source, then you live in a nonnegatively curved space (recall Figure 14.4).

The lazy gas experiment: Take a perfect gas in which particles do not interact, and ask him to move from a certain prescribed density field at time  $t = 0$ , to another prescribed density field at time  $t = 1$ . Since the gas is lazy, he will find a way to do so that needs a minimal amount of work (least action path). Measure the entropy of the gas at each time, and check that it always lies above the line joining the final and initial entropies. If such is the case, then we know that we live in a nonnegatively curved space (see Figure 16.2).

Image /page/11/Figure/1 description: The image displays a diagram illustrating a transformation process over time, labeled with "t = 0", "t = 1/2", and "t = 1". Blue lines connect points from the shape at t=0 to the shape at t=1/2, and then to the shape at t=1, suggesting a continuous deformation. The shapes themselves are irregular blobs, with the middle shape at t=1/2 appearing more elongated and spread out than the shapes at t=0 and t=1. Below this diagram, a graph plots "S = -∫ ρ log ρ" against time, indicated by "t = 0" on the left and "t = 1" on the right. The plotted curve starts at a certain value at t=0, rises to a peak, and then descends towards t=1. A dashed line is also present in the graph, running diagonally downwards from t=0 to t=1, serving as a reference.

Fig. 16.2. The lazy gas experiment: To go from state 0 to state 1, the lazy gas uses a path of least action. In a nonnegatively curved world, the trajectories of the particles first diverge, then converge, so that at intermediate times the gas can afford to have a lower density (higher entropy).

## Bibliographical notes

Convexity has been extensively studied in the Euclidean space [705] and in Banach spaces [172, 324]. I am not aware of textbooks where the study of convexity in more general geodesic spaces is developed, although this notion is now quite frequently used (in the context of optimal transport, see, e.g., [30, p. 50]).

The concept and terminology of displacement convexity were introduced by McCann in the mid-nineties [614]. He identified (16.16) as the basic criterion for convexity in  $P_2(\mathbb{R}^n)$ , and also discussed other formulations of this condition, which will be studied in the next chapter. Inequality (16.16) was later rediscovered by several authors, in various contexts.

The application of Otto calculus to the study of displacement convexity goes back to [669] and [671]. In the latter reference it was conjectured that nonnegative Ricci curvature would imply displacement convexity of  $H$ .

Ricci curvature appears explicitly in Einstein's equations, and will be encountered in any mildly advanced book on general relativity. Fluid mechanics analogies for curvature appear explicitly in the work by Cordero-Erausquin, McCann and Schmuckenschläger [246].

Lott [576] recently pointed out some interesting properties of displacement convexity for functionals explicitly depending on  $t \in [0,1]$ ; for instance the convexity of  $t \int \rho_t \log \rho_t d\nu + N t \log t$  along displacement interpolation is a characterization of  $CD(0, N)$ . The Otto formalism is also useful here.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
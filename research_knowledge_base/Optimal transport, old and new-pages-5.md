# Cyclical monotonicity and Kantorovich duality

To go on, we should become acquainted with two basic concepts in the theory of optimal transport. The first one is a geometric property called cyclical monotonicity; the second one is the Kantorovich dual problem, which is another face of the original Monge–<PERSON><PERSON><PERSON> problem. The main result in this chapter is Theorem 5.10.

#### Definitions and heuristics

I shall start by explaining the concepts of cyclical monotonicity and Kantorovich duality in an informal way, sticking to the bakery analogy of Chapter 3. Assume you have been hired by a large consortium of bakeries and cafés, to be in charge of the distribution of bread from production units (bakeries) to consumption units (cafés). The locations of the bakeries and caf´es, their respective production and consumption rates, are all determined in advance. You have written a transference plan, which says, for each bakery (located at)  $x_i$  and each café  $y_i$ , how much bread should go each morning from  $x_i$  to  $y_j$ .

As there are complaints that the transport cost associated with your plan is actually too high, you try to reduce it. For that purpose you choose a bakery  $x_1$  that sends part of its production to a distant café  $y_1$ , and decide that one basket of bread will be rerouted to another café  $y_2$ , that is closer to  $x_1$ ; thus you will gain  $c(x_1,y_2) - c(x_1,y_1)$ . Of course, now this results in an excess of bread in  $y_2$ , so one basket of bread arriving to  $y_2$  (say, from bakery  $x_2$ ) should in turn be rerouted to yet another café, say  $y_3$ . The process goes on and on until finally you redirect a basket from some bakery  $x_N$  to  $y_1$ , at which point you can stop since you have a new admissible transference plan (see Figure 5.1).

Image /page/1/Figure/2 description: A diagram shows four black squares, each with an arrow pointing to a white circle. Three of the arrows are solid lines, and one is a dashed line. Two of the squares are positioned on the left side of the image, and two are positioned in the lower-middle section. The circles are scattered to the right of the squares. A dashed line forms a pentagon shape, connecting the last circle on the right to the second-to-last circle, then to the second square from the left, then to the bottom-most square, and finally back to the first circle on the right.

Fig. 5.1. An attempt to improve the cost by a cycle; solid arrows indicate the mass transport in the original plan, dashed arrows the paths along which a bit of mass is rerouted.

The new plan is (strictly) better than the older one if and only if

$$
c(x_1, y_2) + c(x_2, y_3) + \ldots + c(x_N, y_1) < c(x_1, y_1) + c(x_2, y_2) + \ldots + c(x_N, y_N).
$$

Thus, if you can find such cycles  $(x_1,y_1),\ldots,(x_N,y_N)$  in your transference plan, certainly the latter is not optimal. Conversely, if you do not find them, then your plan cannot be improved (at least by the procedure described above) and it is likely to be optimal. This motivates the following definitions.

Definition 5.1 (Cyclical monotonicity). Let  $\mathcal{X}, \mathcal{Y}$  be arbitrary sets, and  $c: \mathcal{X} \times \mathcal{Y} \to (-\infty, +\infty]$  be a function. A subset  $\Gamma \subset \mathcal{X} \times \mathcal{Y}$ is said to be c-cyclically monotone if, for any  $N \in \mathbb{N}$ , and any family  $(x_1,y_1),\ldots,(x_N,y_N)$  of points in Γ, holds the inequality

$$
\sum_{i=1}^{N} c(x_i, y_i) \le \sum_{i=1}^{N} c(x_i, y_{i+1})
$$
\n(5.1)

(with the convention  $y_{N+1} = y_1$ ). A transference plan is said to be c-cyclically monotone if it is concentrated on a c-cyclically monotone set.

Informally, a c-cyclically monotone plan is a plan that *cannot be im*proved: it is impossible to perturb it (in the sense considered before, by rerouting mass along some cycle) and get something more economical. One can think of it as a kind of local minimizer. It is intuitively obvious that an optimal plan should be c-cyclically monotone; the converse property is much less obvious (maybe it is possible to get something better by radically changing the plan), but we shall soon see that it holds true under mild conditions.

The next key concept is the dual Kantorovich problem. While the central notion in the original Monge–Kantorovich problem is cost, in the dual problem it is price. Imagine that a company offers to take care of all your transportation problem, buying bread at the bakeries and selling them to the cafés; what happens in between is not your problem (and maybe they have tricks to do the transport at a lower price than you). Let  $\psi(x)$  be the price at which a basket of bread is bought at bakery x, and  $\phi(y)$  the price at which it is sold at café y. On the whole, the price which the consortium bakery  $+$  café pays for the transport is  $\phi(y) - \psi(x)$ , instead of the original cost  $c(x, y)$ . This of course is for each unit of bread: if there is a mass  $\mu(dx)$  at x, then the total price of the bread shipment from there will be  $\psi(x) \mu(dx)$ .

So as to be competitive, the company needs to set up prices in such a way that

$$
\forall (x, y), \qquad \phi(y) - \psi(x) \le c(x, y). \tag{5.2}
$$

When you were handling the transportation yourself, your problem was to minimize the cost. Now that the company takes up the transportation charge, their problem is to maximize the profits. This naturally leads to the dual Kantorovich problem:

$$
\sup \left\{ \int_{\mathcal{Y}} \phi(y) d\nu(y) - \int_{\mathcal{X}} \psi(x) d\mu(x); \quad \phi(y) - \psi(x) \le c(x, y) \right\}. \tag{5.3}
$$

From a mathematical point of view, it will be imposed that the functions  $\psi$  and  $\phi$  appearing in (5.3) be integrable:  $\psi \in L^1(\mathcal{X}, \mu);$  $\phi \in L^1(\mathcal{Y}, \nu).$ 

With the intervention of the company, the shipment of each unit of bread does not cost more than it used to when you were handling it yourself; so it is obvious that the supremum in (5.3) is no more than the optimal transport cost:

66 5 Cyclical monotonicity and Kantorovich duality

$$
\sup_{\phi-\psi\leq c} \left\{ \int_{\mathcal{Y}} \phi(y) d\nu(y) - \int_{\mathcal{X}} \psi(x) d\mu(x) \right\} \leq \inf_{\pi \in \Pi(\mu,\nu)} \left\{ \int_{\mathcal{X}\times\mathcal{Y}} c(x,y) d\pi(x,y) \right\}. (5.4)
$$

Clearly, if we can find a pair  $(\psi, \phi)$  and a transference plan  $\pi$  for which there is equality, then  $(\psi, \phi)$  is optimal in the left-hand side and  $\pi$  is also optimal in the right-hand side.

A pair of price functions  $(\psi, \phi)$  will informally be said to be *com*petitive if it satisfies  $(5.2)$ . For a given y, it is of course in the interest of the company to set the highest possible competitive price  $\phi(y)$ , i.e. the highest lower bound for (i.e. the infimum of)  $\psi(x) + c(x, y)$ , among all bakeries x. Similarly, for a given x, the price  $\psi(x)$  should be the supremum of all  $\phi(y) - c(x, y)$ . Thus it makes sense to describe a pair of prices  $(\psi, \phi)$  as tight if

$$
\phi(y) = \inf_{x} \left( \psi(x) + c(x, y) \right), \qquad \psi(x) = \sup_{y} \left( \phi(y) - c(x, y) \right). \tag{5.5}
$$

In words, prices are tight if it is impossible for the company to raise the selling price, or lower the buying price, without losing its competitivity.

Consider an arbitrary pair of competitive prices  $(\psi, \phi)$ . We can always improve  $\phi$  by replacing it by  $\phi_1(y) = \inf_x (\psi(x) + c(x, y))$ . Then we can also improve  $\psi$  by replacing it by  $\psi_1(x) = \sup_y (\phi_1(y) - c(x, y));$ then replacing  $\phi_1$  by  $\phi_2(y) = \inf_x (\psi_1(x) + c(x, y)),$  and so on. It turns out that this process is stationary: as an easy exercise, the reader can check that  $\phi_2 = \phi_1$ ,  $\psi_2 = \psi_1$ , which means that after just one iteration one obtains a pair of tight prices. Thus, when we consider the dual Kantorovich problem (5.3), it makes sense to restrict our attention to tight pairs, in the sense of equation (5.5). From that equation we can reconstruct  $\phi$  in terms of  $\psi$ , so we can just take  $\psi$  as the only unknown in our problem.

That unknown cannot be just any function: if you take a general function  $\psi$ , and compute  $\phi$  by the first formula in (5.5), there is no chance that the second formula will be satisfied. In fact this second formula will hold true if and only if  $\psi$  is *c*-convex, in the sense of the next definition (illustrated by Figure 5.2).

**Definition 5.2** (c-convexity). Let  $\mathcal{X}, \mathcal{Y}$  be sets, and  $c : \mathcal{X} \times \mathcal{Y} \rightarrow$  $(-\infty, +\infty]$ . A function  $\psi : \mathcal{X} \to \mathbb{R} \cup \{+\infty\}$  is said to be c-convex if it is not identically  $+\infty$ , and there exists  $\zeta : \mathcal{Y} \to \mathbb{R} \cup {\pm \infty}$  such that

Definitions and heuristics 67

$$
\forall x \in \mathcal{X} \qquad \psi(x) = \sup_{y \in \mathcal{Y}} \Big( \zeta(y) - c(x, y) \Big). \tag{5.6}
$$

Then its c-transform is the function  $\psi^c$  defined by

$$
\forall y \in \mathcal{Y} \qquad \psi^c(y) = \inf_{x \in \mathcal{X}} \left( \psi(x) + c(x, y) \right), \tag{5.7}
$$

and its c-subdifferential is the c-cyclically monotone set defined by

$$
\partial_c \psi := \Big\{ (x, y) \in \mathcal{X} \times \mathcal{Y}; \quad \psi^c(y) - \psi(x) = c(x, y) \Big\}.
$$

The functions  $\psi$  and  $\psi^c$  are said to be c-conjugate.

Moreover, the c-subdifferential of  $\psi$  at point x is

$$
\partial_c \psi(x) = \left\{ y \in \mathcal{Y}; \quad (x, y) \in \partial_c \psi \right\},\
$$

or equivalently

$$
\forall z \in \mathcal{X}, \qquad \psi(x) + c(x, y) \le \psi(z) + c(z, y). \tag{5.8}
$$

Image /page/4/Figure/11 description: The image displays a graph with a horizontal line and a curve. The horizontal line is labeled with points y0, y1, and y2. Below the horizontal line, there are three shaded regions that resemble hills or arches. Vertical dotted lines connect points on the horizontal line to the peaks of these shaded regions. These points on the horizontal line are labeled x0, x1, and x2, corresponding to y0, y1, and y2 respectively. The curve passes through y0, dips down, rises to a peak near y1, dips again, and then rises to a peak near y2. The curve appears to be a cubic function or a similar polynomial.

Fig. 5.2. A c-convex function is a function whose graph you can entirely caress from below with a tool whose shape is the negative of the cost function (this shape might vary with the point y). In the picture  $y_i \in \partial_c \psi(x_i)$ .

**Particular Case 5.3.** If  $c(x,y) = -x \cdot y$  on  $\mathbb{R}^n \times \mathbb{R}^n$ , then the ctransform coincides with the usual Legendre transform, and c-convexity is just plain convexity on  $\mathbb{R}^n$ . (Actually, this is a slight oversimplification: c-convexity is equivalent to plain convexity plus lower semicontinuity! A convex function is automatically continuous on the largest open set  $\Omega$  where it is finite, but lower semicontinuity might fail at the boundary of  $\Omega$ .) One can think of the cost function  $c(x,y) = -x \cdot y$ as basically the same as  $c(x, y) = |x - y|^2/2$ , since the "interaction" between the positions  $x$  and  $y$  is the same for both costs.

**Particular Case 5.4.** If  $c = d$  is a *distance* on some metric space  $X$ , then a c-convex function is just a 1-Lipschitz function, and it is its own c-transform. Indeed, if  $\psi$  is c-convex it is obviously 1-Lipschitz; conversely, if  $\psi$  is 1-Lipschitz, then  $\psi(x) \leq \psi(y) + d(x, y)$ , so  $\psi(x) = \inf_y [\psi(y) + d(x, y)] = \psi^c(x)$ . As an even more particular case, if  $c(x,y) = 1_{x\neq y}$ , then  $\psi$  is c-convex if and only if sup  $\psi$  – inf  $\psi \leq 1$ , and then again  $\psi^c = \psi$ . (More generally, if c satisfies the triangle inequality  $c(x, z) \leq c(x, y) + c(y, z)$ , then  $\psi$  is c-convex if and only if  $\psi(y) - \psi(x) \leq c(x, y)$  for all  $x, y$ ; and then  $\psi = \psi^c$ .)

Remark 5.5. There is no measure theory in Definition 5.2, so no assumption of measurability is made, and the supremum in (5.6) is a true supremum, not just an essential supremum; the same for the infimum in  $(5.7)$ . If c is continuous, then a c-convex function is automatically lower semicontinuous, and its subdifferential is closed; but if c is not continuous the measurability of  $\psi$  and  $\partial_c \psi$  is not a priori guaranteed.

**Remark 5.6.** I excluded the case when  $\psi \equiv +\infty$  so as to avoid trivial situations; what I called a c-convex function might more properly (!) be called a proper c-convex function. This automatically implies that  $\zeta$  in (5.6) does not take the value  $+\infty$  at all if c is real-valued. If  $c$  does achieve infinite values, then the correct convention in  $(5.6)$  is  $(+\infty) - (+\infty) = -\infty.$ 

If  $\psi$  is a function on X, then its c-transform is a function on Y. Conversely, given a function on  $\mathcal{Y}$ , one may define its c-transform as a function on  $\mathcal{X}$ . It will be convenient in the sequel to define the latter concept by an infimum rather than a supremum. This convention has the drawback of breaking the symmetry between the roles of  $\mathcal X$  and  $\mathcal Y$ , but has other advantages that will be apparent later on.

**Definition 5.7 (c-concavity).** With the same notation as in Definition 5.2, a function  $\phi : \mathcal{Y} \to \mathbb{R} \cup \{-\infty\}$  is said to be c-concave if it is not identically  $-\infty$ , and there exists  $\psi : \mathcal{X} \to \mathbb{R} \cup \{\pm \infty\}$  such that  $\phi = \psi^c$ . Then its c-transform is the function  $\phi^c$  defined by

$$
\forall x \in \mathcal{X} \qquad \phi^c(x) = \sup_{y \in \mathcal{Y}} \Big( \phi(y) - c(x, y) \Big);
$$

and its c-superdifferential is the c-cyclically monotone set defined by

$$
\partial^c \phi := \Big\{ (x, y) \subset \mathcal{X} \times \mathcal{Y}; \quad \phi(y) - \phi^c(x) = c(x, y) \Big\}.
$$

In spite of its short and elementary proof, the next crucial result is one of the main justifications of the concept of c-convexity.

Proposition 5.8 (Alternative characterization of  $c$ -convexity). For any function  $\psi : \mathcal{X} \to \mathbb{R} \cup \{+\infty\}$ , let its c-convexification be defined by  $\psi^{cc} = (\psi^c)^c$ . More explicitly,

$$
\psi^{cc}(x) = \sup_{y \in \mathcal{Y}} \inf_{\widetilde{x} \in \mathcal{X}} \left( \psi(\widetilde{x}) + c(\widetilde{x}, y) - c(x, y) \right).
$$

Then  $\psi$  is c-convex if and only if  $\psi^{cc} = \psi$ .

Proof of Proposition 5.8. As a general fact, for any function  $\phi : \mathcal{Y} \to$  $\mathbb{R} \cup \{-\infty\}$  (not necessarily c-convex), one has the identity  $\phi^{ccc} = \phi^c$ . Indeed,

$$
\phi^{ccc}(x) = \sup_{y} \inf_{\widetilde{x}} \sup_{\widetilde{y}} \left[ \phi(\widetilde{y}) - c(\widetilde{x}, \widetilde{y}) + c(\widetilde{x}, y) - c(x, y) \right];
$$

then the choice  $\tilde{x} = x$  shows that  $\phi^{ccc}(x) \leq \phi^{c}(x)$ ; while the choice  $\widetilde{y} = y$  shows that  $\phi^{ccc}(x) \geq \phi^{c}(x)$ .

If  $\psi$  is c-convex, then there is  $\zeta$  such that  $\psi = \zeta^c$ , so  $\psi^{cc} = \zeta^{ccc} =$  $\zeta^c = \psi.$ 

The converse is obvious: If  $\psi^{cc} = \psi$ , then  $\psi$  is c-convex, as the c-transform of  $\psi^c$ . ⊔□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□

Remark 5.9. Proposition 5.8 is a generalized version of the Legendre duality in convex analysis (to recover the usual Legendre duality, take  $c(x, y) = -x \cdot y$  in  $\mathbb{R}^n \times \mathbb{R}^n$ .

#### Kantorovich duality

We are now ready to state and prove the main result in this chapter.

**Theorem 5.10 (Kantorovich duality).** Let  $(\mathcal{X}, \mu)$  and  $(\mathcal{Y}, \nu)$  be two Polish probability spaces and let  $c : \mathcal{X} \times \mathcal{Y} \to \mathbb{R} \cup \{+\infty\}$  be a lower semicontinuous cost function, such that

$$
\forall (x, y) \in \mathcal{X} \times \mathcal{Y}, \qquad c(x, y) \ge a(x) + b(y)
$$

for some real-valued upper semicontinuous functions  $a \in L^1(\mu)$  and  $b \in L^1(\nu)$ . Then

(i) There is duality:

$$
\min_{\pi \in \Pi(\mu,\nu)} \int_{\mathcal{X} \times \mathcal{Y}} c(x,y) d\pi(x,y)
$$

$$
= \sup_{(\psi,\phi) \in C_b(\mathcal{X}) \times C_b(\mathcal{Y}); \ \phi-\psi \leq c} \left( \int_{\mathcal{Y}} \phi(y) d\nu(y) - \int_{\mathcal{X}} \psi(x) d\mu(x) \right)
$$

$$
= \sup_{(\psi,\phi) \in L^1(\mu) \times L^1(\nu); \ \phi-\psi \leq c} \left( \int_{\mathcal{Y}} \phi(y) d\nu(y) - \int_{\mathcal{X}} \psi(x) d\mu(x) \right)
$$

$$
= \sup_{\psi \in L^1(\mu)} \left( \int_{\mathcal{Y}} \psi^c(y) d\nu(y) - \int_{\mathcal{X}} \psi(x) d\mu(x) \right)
$$

$$
= \sup_{\phi \in L^1(\nu)} \left( \int_{\mathcal{Y}} \phi(y) d\nu(y) - \int_{\mathcal{X}} \phi^c(x) d\mu(x) \right),
$$

and in the above suprema one might as well impose that  $\psi$  be c-convex and  $\phi$  c-concave.

(ii) If c is real-valued and the optimal cost  $C(\mu, \nu) = \inf_{\pi \in \Pi(\mu, \nu)} \int c d\pi$ is finite, then there is a measurable c-cyclically monotone set  $\Gamma \subset \mathcal{X} \times \mathcal{Y}$ (closed if a, b, c are continuous) such that for any  $\pi \in \Pi(\mu, \nu)$  the following five statements are equivalent:

- (a)  $\pi$  is optimal;
- (b)  $\pi$  is c-cyclically monotone;
- (c) There is a c-convex  $\psi$  such that,  $\pi$ -almost surely,
  - $\psi^c(y) \psi(x) = c(x, y);$
- (d) There exist  $\psi : \mathcal{X} \to \mathbb{R} \cup \{+\infty\}$  and  $\phi : \mathcal{Y} \to \mathbb{R} \cup \{-\infty\},$ such that  $\phi(y) - \psi(x) \leq c(x, y)$  for all  $(x, y)$ , with equality  $\pi$ -almost surely;
- (e)  $\pi$  is concentrated on  $\Gamma$ .

(iii) If c is real-valued,  $C(\mu, \nu) < +\infty$ , and one has the pointwise upper bound

$$
c(x,y) \le c_{\mathcal{X}}(x) + c_{\mathcal{Y}}(y), \qquad (c_{\mathcal{X}}, c_{\mathcal{Y}}) \in L^1(\mu) \times L^1(\nu), \tag{5.9}
$$

then both the primal and dual Kantorovich problems have solutions, so

$$
\min_{\pi \in \Pi(\mu,\nu)} \int_{\mathcal{X} \times \mathcal{Y}} c(x,y) d\pi(x,y)
$$
=

$$
\max_{(\psi,\phi) \in L^1(\mu) \times L^1(\nu); \ \phi - \psi \leq c} \left( \int_{\mathcal{Y}} \phi(y) d\nu(y) - \int_{\mathcal{X}} \psi(x) d\mu(x) \right)
$$
=

$$
\max_{\psi \in L^1(\mu)} \left( \int_{\mathcal{Y}} \psi^c(y) d\nu(y) - \int_{\mathcal{X}} \psi(x) d\mu(x) \right),
$$

and in the latter expressions one might as well impose that  $\psi$  be cconvex and  $\phi = \psi^c$ . If in addition a, b and c are continuous, then there is a closed c-cyclically monotone set  $\Gamma \subset \mathcal{X} \times \mathcal{Y}$ , such that for any  $\pi \in \Pi(\mu, \nu)$  and for any c-convex  $\psi \in L^1(\mu)$ ,

 $\int \pi$  is optimal in the Kantorovich problem if and only if  $\pi[\Gamma] = 1$ ;  $\psi$  is optimal in the dual Kantorovich problem if and only if  $\Gamma \subset \partial_c \psi$ .

**Remark 5.11.** When the cost c is continuous, then the support of  $\pi$ is c-cyclically monotone; but for a discontinuous cost function it might a priori be that  $\pi$  is concentrated on a (nonclosed) c-cyclically monotone set, while the support of  $\pi$  is not c-cyclically monotone. So, in the sequel, the words "concentrated on" are not exchangeable with "supported in". There is another subtlety for discontinuous cost functions: It is not clear that the functions  $\phi$  and  $\psi^c$  appearing in statements (ii) and (iii) are Borel measurable; it will only be proven that they coincide with measurable functions outside of a  $\nu$ -negligible set.

Remark 5.12. Note the difference between statements (b) and (e): The set  $\Gamma$  appearing in (ii)(e) is the same for all optimal  $\pi$ 's, it only depends on  $\mu$  and  $\nu$ . This set is in general not unique. If c is continuous and  $\Gamma$  is imposed to be closed, then one can define a smallest Γ, which is the closure of the union of all the supports of the optimal  $\pi$ 's. There is also a largest  $\Gamma$ , which is the intersection of all the c-subdifferentials  $\partial_c \psi$ , where  $\psi$  is such that there exists an optimal  $\pi$ supported in  $\partial_{\alpha}\psi$ . (Since the cost function is assumed to be continuous, the c-subdifferentials are closed, and so is their intersection.)

Remark 5.13. Here is a useful practical consequence of Theorem 5.10: Given a transference plan  $\pi$ , if you can cook up a pair of competitive prices  $(\psi, \phi)$  such that  $\phi(y) - \psi(x) = c(x, y)$  throughout the support of  $\pi$ , then you know that  $\pi$  is optimal. This theorem also shows that or even

optimal transference plans satisfy very special conditions: if you fix an optimal pair  $(\psi, \phi)$ , then mass arriving at y can come from x only if  $c(x, y) = \phi(y) - \psi(x) = \psi^{c}(y) - \psi(x)$ , which means that

$$
x \in \underset{x' \in \mathcal{X}}{\operatorname{Arg\,min}} \Big( \psi(x') + c(x', y) \Big).
$$

In terms of my bakery analogy this can be restated as follows: A café accepts bread from a bakery only if the combined cost of buying the bread there and transporting it here is lowest among all possible bakeries. Similarly, given a pair of competitive prices  $(\psi, \phi)$ , if you can cook up a transference plan  $\pi$  such that  $\phi(y) - \psi(x) = c(x, y)$  throughout the support of  $\pi$ , then you know that  $(\psi, \phi)$  is a solution to the dual Kantorovich problem.

**Remark 5.14.** The assumption  $c \leq c_{\mathcal{X}} + c_{\mathcal{Y}}$  in (iii) can be weakened into

$$
\int_{\mathcal{X}\times\mathcal{Y}} c(x,y) d\mu(x) d\nu(y) < +\infty,
$$

$$
\begin{cases}
\mu\[\left\{\{x; \int_{\mathcal{Y}} c(x,y) d\nu(y) < +\infty\}\right\}\] > 0; \\
\nu\[\left\{\{y; \int_{\mathcal{X}} c(x,y) d\mu(x) < +\infty\}\right\}\] > 0.
\end{cases}
$$
(5.10)

**Remark 5.15.** If the variables x and y are swapped, then  $(\mu, \nu)$  should be replaced by  $(\nu, \mu)$  and  $(\psi, \phi)$  by  $(-\phi, -\psi)$ .

Particular Case 5.16. Particular Case 5.4 leads to the following variant of Theorem 5.10. When  $c(x,y) = d(x,y)$  is a distance on a Polish space  $\mathcal X$ , and  $\mu, \nu$  belong to  $P_1(\mathcal X)$ , then

$$
\inf \mathbb{E} d(X, Y) = \sup \mathbb{E} [\psi(X) - \psi(Y)] = \sup \left\{ \int_{\mathcal{X}} \psi \, d\mu - \int_{\mathcal{Y}} \psi \, d\nu \right\}.
$$
\n(5.11)

where the infimum on the left is over all couplings  $(X, Y)$  of  $(\mu, \nu)$ , and the supremum on the right is over all 1-Lipschitz functions  $\psi$ . This is the Kantorovich–Rubinstein formula; it holds true as soon as the supremum in the left-hand side is finite, and it is very useful.

**Particular Case 5.17.** Now consider  $c(x, y) = -x \cdot y$  in  $\mathbb{R}^n \times \mathbb{R}^n$ . This cost is not nonnegative, but we have the lower bound  $c(x,y) \geq$ 

 $-(|x|^2+|y|^2)/2$ . So if  $x \to |x|^2 \in L^1(\mu)$  and  $y \to |y|^2 \in L^1(\nu)$ , then one can invoke the Particular Case 5.3 to deduce from Theorem 5.10 that

$$
\sup \mathbb{E}(X \cdot Y) = \inf \mathbb{E}[\varphi(X) + \varphi^*(Y)] = \inf \left\{ \int_{\mathcal{X}} \varphi \, d\mu + \int_{\mathcal{Y}} \varphi^* \, d\nu \right\},\tag{5.12}
$$

where the supremum on the left is over all couplings  $(X, Y)$  of  $(\mu, \nu)$ , the infimum on the right is over all (lower semicontinuous) convex functions on  $\mathbb{R}^n$ , and  $\varphi^*$  stands for the usual Legendre transform of  $\varphi$ . In formula (5.12), the signs have been changed with respect to the statement of Theorem 5.10, so the problem is to maximize the correlation of the random variables  $X$  and  $Y$ .

Before proving Theorem 5.10, I shall first informally explain the construction. At first reading, one might be content with these informal explanations and skip the rigorous proof.

Idea of proof of Theorem 5.10. Take an optimal  $\pi$  (which exists from Theorem 4.1), and let  $(\psi, \phi)$  be two competitive prices. Of course, as in (5.4),

$$
\int c(x,y) d\pi(x,y) \ge \int \phi d\nu - \int \psi d\mu = \int [\phi(y) - \psi(x)] d\pi(x,y).
$$

So if both quantities are equal, then  $\int [c - \phi + \psi] d\pi = 0$ , and since the integrand is nonnegative, necessarily

$$
\phi(y) - \psi(x) = c(x, y) \qquad \pi(dx \, dy) - \text{almost surely.}
$$

Intuitively speaking, whenever there is some transfer of goods from  $x$ to y, the prices should be adjusted exactly to the transport cost.

Now let  $(x_i)_{0 \leq i \leq m}$  and  $(y_i)_{0 \leq i \leq m}$  be such that  $(x_i, y_i)$  belongs to the support of  $\pi$ , so there is indeed some transfer from  $x_i$  to  $y_i$ . Then we hope that

$$
\[

\phi
(
y
0
)
-
\psi
(
x
0
)
=
c
(
x
0,
y
0
)

\phi
(
y
1
)
-
\psi
(
x
1
)
=
c
(
x
1,
y
1
)

\cdots

\phi
(
y
m
)
-
\psi
(
x
m
)
=
c
(
x
m,
y
m
)
.
$$

On the other hand, if  $x$  is an arbitrary point,

74 5 Cyclical monotonicity and Kantorovich duality

$$
{

ϕ(y0)-Χ(x1)≤c(x1,y0)

ϕ(y1)-Χ(x2)≤c(x2,y1)

…

ϕ(ym)-Χ(x)≤c(x,ym)
$$

By subtracting these inequalities from the previous equalities and adding up everything, one obtains

$$
\psi(x) \geq \psi(x_0) + [c(x_0, y_0) - c(x_1, y_0)] + \ldots + [c(x_m, y_m) - c(x, y_m)].
$$

Of course, one can add an arbitrary constant to  $\psi$ , provided that one subtracts the same constant from  $\phi$ ; so it is possible to decide that  $\psi(x_0) = 0$ , where  $(x_0, y_0)$  is arbitrarily chosen in the support of  $\pi$ . Then

$$
\psi(x) \geq [c(x_0, y_0) - c(x_1, y_0)] + \ldots + [c(x_m, y_m) - c(x, y_m)], \quad (5.13)
$$

and this should be true for all choices of  $(x_i, y_i)$   $(1 \leq i \leq m)$  in the support of  $\pi$ , and for all  $m \geq 1$ . So it becomes natural to *define*  $\psi$ as the supremum of all the functions (of the variable  $x$ ) appearing in the right-hand side of (5.13). It will turn out that this  $\psi$  satisfies the equation

$$
\psi^{c}(y) - \psi(x) = c(x, y) \qquad \pi(dx \, dy)
$$
-almost surely.

Then, if  $\psi$  and  $\psi^c$  are integrable, one can write

$$
\int c d\pi = \int \psi^c d\pi - \int \psi d\pi = \int \psi^c d\nu - \int \psi d\mu.
$$

This shows at the same time that  $\pi$  is optimal in the Kantorovich problem, and that the pair  $(\psi, \psi^c)$  is optimal in the dual Kantorovich problem. □

Rigorous proof of Theorem 5.10, Part  $(i)$ . First I claim that it is sufficient to treat the case when c is nonnegative. Indeed, let

$$
\widetilde{c}(x,y) := c(x,y) - a(x) - b(y) \ge 0, \qquad A := \int a \, d\mu + \int b \, d\nu \in \mathbb{R}.
$$

Whenever  $\psi : \mathcal{X} \to \mathbb{R} \cup \{+\infty\}$  and  $\phi : \mathcal{Y} \to \mathbb{R} \cup \{-\infty\}$  are two functions, define

$$
\psi(x) := \psi(x) + a(x), \qquad \phi(y) := \phi(y) - b(y).
$$

Then the following properties are readily checked:

c real-valued  $\implies$   $\tilde{c}$  real-valued c lower semicontinuous  $\implies$   $\tilde{c}$  lower semicontinuous  $\widetilde{\psi} \in L^1(\mu) \Longleftrightarrow \psi \in L^1(\mu); \qquad \widetilde{\phi} \in L^1(\nu) \Longleftrightarrow \phi \in L^1(\nu);$  $\forall \pi \in \Pi(\mu, \nu),$ Z  $\widetilde{c} d\pi =$ Z  $c d\pi - \Lambda;$  $\forall (\psi, \phi) \in L^1(\mu) \times L^1(\nu),$ Z  $\phi$  dv – Z  $\psi d\mu =$ Z  $\phi$  dv –  $\psi d\nu - \Lambda;$  $\psi$  is c-convex  $\Longleftrightarrow \widetilde{\psi}$  is  $\widetilde{c}$ -convex;  $\phi$  is c-concave  $\Longleftrightarrow \widetilde{\phi}$  is  $\widetilde{c}$ -concave;  $(\phi, \psi)$  are c-conjugate  $\Longleftrightarrow (\tilde{\phi}, \tilde{\psi})$  are  $\tilde{c}$ -conjugate;  $\Gamma$  is c-cyclically monotone  $\Longleftrightarrow \Gamma$  is  $\tilde{c}$ -cyclically monotone.

Thanks to these formulas, it is equivalent to establish Theorem 5.10 for the cost c or for the nonnegative cost  $\tilde{c}$ . So in the sequel, I shall assume, without further comment, that  $c$  is nonnegative.

The rest of the proof is divided into five steps.

Step 1: If  $\mu = (1/n) \sum_{i=1}^n \delta_{x_i}, \nu = (1/n) \sum_{j=1}^n \delta_{y_j}$ , where the costs  $c(x_i, y_j)$  are finite, then there is at least one cyclically monotone transference plan.

Indeed, in that particular case, a transference plan between  $\mu$  and  $\nu$  can be identified with a bistochastic  $n \times n$  array of real numbers  $a_{ij} \in [0,1]$ : each  $a_{ij}$  tells what proportion of the  $1/n$  mass carried by point  $x_i$  will go to destination  $y_j$ . So the Monge–Kantorovich problem becomes

$$
\inf_{(a_{ij})} \sum_{ij} a_{ij} c(x_i, y_i)
$$

where the infimum is over all arrays  $(a_{ij})$  satisfying

$$
\sum_{i} a_{ij} = 1, \qquad \sum_{j} a_{ij} = 1. \tag{5.14}
$$

Here we are minimizing a linear function on the compact set  $[0,1]^{n \times n}$ , so obviously there exists a minimizer; the corresponding transference plan  $\pi$  can be written as

76 5 Cyclical monotonicity and Kantorovich duality

$$
\pi = \frac{1}{n} \sum_{ij} a_{ij} \, \delta_{(x_i, y_j)},
$$

and its support S is the set of all couples  $(x_i, y_j)$  such that  $a_{ij} > 0$ .

Assume that S is not cyclically monotone: Then there exist  $N \in \mathbb{N}$ and  $(x_{i_1}, y_{j_1}), \ldots, (x_{i_N}, y_{j_N})$  in S such that

$$
c(x_{i_1}, y_{j_2}) + c(x_{i_2}, y_{j_3}) + \ldots + c(x_{i_N}, y_{j_1}) < c(x_{i_1}, y_{j_1}) + \ldots + c(x_{i_N}, y_{j_N}). \tag{5.15}
$$

Let  $a := \min(a_{i_1,j_1}, \ldots, a_{i_N,j_N}) > 0$ . Define a new transference plan  $\widetilde{\pi}$ by the formula

$$
\widetilde{\pi} = \pi + \frac{a}{n} \sum_{\ell=1}^N \left( \delta_{(x_{i_\ell}, y_{j_{\ell+1}})} - \delta_{(x_{i_\ell}, y_{j_\ell})} \right).
$$

It is easy to check that this has the correct marginals, and by (5.15) the cost associated with  $\tilde{\pi}$  is strictly less than the cost associated with  $\pi$ . This is a contradiction, so S is indeed c-cyclically monotone!

**Step 2:** If  $c$  is continuous, then there is a cyclically monotone transference plan.

To prove this, consider sequences of independent random variables  $x_i \in \mathcal{X}, y_j \in \mathcal{Y},$  with respective law  $\mu$ ,  $\nu$ . According to the law of large numbers for empirical measures (sometimes called fundamental theorem of statistics, or Varadarajan's theorem), one has, with probability 1,

$$
\mu_n := \frac{1}{n} \sum_{i=1}^n \delta_{x_i} \longrightarrow \mu, \qquad \nu_n := \frac{1}{n} \sum_{j=1}^n \delta_{y_j} \longrightarrow \nu \tag{5.16}
$$

as  $n \to \infty$ , in the sense of weak convergence of measures. In particular, by Prokhorov's theorem,  $(\mu_n)$  and  $(\nu_n)$  are tight sequences.

For each *n*, let  $\pi_n$  be a cyclically monotone transference plan between  $\mu_n$  and  $\nu_n$ . By Lemma 4.4,  $\{\pi_n\}_{n\in\mathbb{N}}$  is tight. By Prokhorov's theorem, there is a subsequence, still denoted  $(\pi_n)$ , which converges weakly to some probability measure  $\pi$ , i.e.

$$
\int h(x,y) d\pi_n(x,y) \longrightarrow \int h(x,y) d\pi(x,y)
$$

for all bounded continuous functions h on  $\mathcal{X} \times \mathcal{Y}$ . By applying the previous identity with  $h(x,y) = f(x)$  and  $h(x,y) = g(y)$ , we see that  $\pi$  has marginals  $\mu$  and  $\nu$ , so this is an admissible transference plan between  $\mu$  and  $\nu$ .

For each n, the cyclical monotonicity of  $\pi_n$  implies that for all N and  $\pi_n^{\otimes N}$ -almost all  $(x_1, y_1), \ldots, (x_N, y_N)$ , the inequality  $(5.1)$  is satisfied; in other words,  $\pi_n^{\otimes N}$  is concentrated on the set  $\mathcal{C}(N)$  of all  $((x_1,y_1),\ldots,(x_N,y_N)) \in (\mathcal{X} \times \mathcal{Y})^N$  satisfying  $(5.1)$ . Since c is continuous,  $\mathcal{C}(N)$  is a *closed* set, so the weak limit  $\pi^{\otimes N}$  of  $\pi^{\otimes N}$  is also concentrated on  $\mathcal{C}(N)$ . Let  $\Gamma = \text{Spt } \pi$  (Spt stands for "support"), then

$$
\Gamma^N = (\mathrm{Spt}\,\pi)^N = \mathrm{Spt}(\pi^{\otimes N}) \subset \mathcal{C}(N),
$$

and since this holds true for all  $N$ ,  $\Gamma$  is c-cyclically monotone.

Step 3: If c is continuous real-valued and  $\pi$  is c-cyclically monotone, then there is a c-convex  $\psi$  such that  $\partial_c \psi$  contains the support of  $\pi$ .

Indeed, let  $\Gamma$  again denote the support of  $\pi$  (this is a closed set). Pick any  $(x_0, y_0) \in \Gamma$ , and define

$$
\psi(x) := \sup_{m \in \mathbb{N}} \sup \Big\{ \big[c(x_0, y_0) - c(x_1, y_0) \big] + \big[c(x_1, y_1) - c(x_2, y_1) \big] + \cdots + \big[c(x_m, y_m) - c(x, y_m) \big]; \quad (x_1, y_1), \ldots, (x_m, y_m) \in \Gamma \Big\}.
$$
(5.17)

By applying the definition with  $m = 1$  and  $(x_1, y_1) = (x_0, y_0)$ , one immediately sees that  $\psi(x_0) \geq 0$ . On the other hand,  $\psi(x_0)$  is the supremum of all the quantities  $[c(x_0,y_0)-c(x_1,y_0)]+\ldots+[c(x_m,y_m)-\ldots]$  $c(x_0,y_m)$  which by cyclical monotonicity are all nonpositive. So actually  $\psi(x_0) = 0$ . (In fact this is the only place in this step where c-cyclical monotonicity will be used!)

By renaming  $y_m$  as  $y$ , obviously

$$
\psi(x) = \sup_{y \in \mathcal{Y}} \sup_{m \in \mathbb{N}} \sup_{(x_1, y_1), \dots, (x_{m-1}, y_{m-1}), x_m} \left\{ \left[ c(x_0, y_0) - c(x_1, y_0) \right] + \left[ c(x_1, y_1) - c(x_2, y_1) \right] + \dots + \left[ c(x_m, y) - c(x, y) \right]; (x_1, y_1), \dots, (x_m, y) \in \Gamma \right\}. (5.18)
$$

So  $\psi(x) = \sup_y [\zeta(y) - c(x, y)]$ , if  $\zeta$  is defined by

$$
\zeta(y) = \sup \Big\{ \big[c(x_0, y_0) - c(x_1, y_0)\big] + \big[c(x_1, y_1) - c(x_2, y_1)\big] + \cdots + c(x_m, y);
$$
  
$$
m \in \mathbb{N}, (x_1, y_1), \dots, (x_m, y) \in \Gamma \Big\} \quad (5.19)
$$

(with the convention that  $\zeta = -\infty$  out of  $proj_{\mathcal{V}}(\Gamma)$ ). Thus  $\psi$  is a cconvex function.

Now let  $(\overline{x}, \overline{y}) \in \Gamma$ . By choosing  $x_m = \overline{x}, y_m = \overline{y}$  in the definition of  $\psi$ ,

$$
\psi(x) \ge \sup_{m} \left\{ \left( \sup_{(x_1, y_1), ..., (x_{m-1}, y_{m-1})} \left[ c(x_0, y_0) - c(x_1, y_0) \right] + \\ \cdots + \left[ c(x_{m-1}, y_{m-1}) - c(\overline{x}, y_{m-1}) \right] \right) + \left[ c(\overline{x}, \overline{y}) - c(x, \overline{y}) \right] \right\}.
$$

In the definition of  $\psi$ , it does not matter whether one takes the supremum over  $m-1$  or over m variables, since one also takes the supremum over m. So the previous inequality can be recast as

$$
\psi(x) \ge \psi(\overline{x}) + c(\overline{x}, \overline{y}) - c(x, \overline{y}).
$$

In particular,  $\psi(x) + c(x, \overline{y}) \geq \psi(\overline{x}) + c(\overline{x}, \overline{y})$ . Taking the infimum over  $x \in \mathcal{X}$  in the left-hand side, we deduce that

$$
\psi^c(\overline{y}) \ge \psi(\overline{x}) + c(\overline{x}, \overline{y}).
$$

Since the reverse inequality is always satisfied, actually

$$
\psi^c(\overline{y}) = \psi(\overline{x}) + c(\overline{x}, \overline{y}),
$$

and this means precisely that  $(\overline{x}, \overline{y}) \in \partial_c \psi$ . So  $\Gamma$  does lie in the csubdifferential of  $\psi$ .

#### Step 4: If c is continuous and bounded, then there is duality.

Let  $||c|| := \sup c(x, y)$ . By Steps 2 and 3, there exists a transference plan  $\pi$  whose support is included in  $\partial_c \psi$  for some c-convex  $\psi$ , and which was constructed "explicitly" in Step 3. Let  $\phi = \psi^c$ .

From (5.17),  $\psi = \sup \psi_m$ , where each  $\psi_m$  is a supremum of continuous functions, and therefore lower semicontinuous. In particular,  $\psi$  is measurable.<sup>1</sup> The same is true of  $\phi$ .

Next we check that  $\psi$ ,  $\phi$  are bounded. Let  $(x_0, y_0) \in \partial_c \psi$  be such that  $\psi(x_0) < +\infty$ ; then necessarily  $\phi(y_0) > -\infty$ . So, for any  $x \in \mathcal{X}$ ,

$$
\psi(x) = \sup_{y} [\phi(y) - c(x, y)] \ge \phi(y_0) - c(x, y_0) \ge \phi(y_0) - ||c||;
$$

<sup>1</sup> A lower semicontinuous function on a Polish space is always measurable, even if it is obtained as a supremum of uncountably many continuous functions; in fact it can always be written as a supremum of countably many continuous functions!

$$
\phi(y) = \inf_x \, [\psi(x) + c(x, y)] \leq \psi(x_0) + c(x_0, y) \leq \psi(x_0) + ||c||.
$$

Re-injecting these bounds into the identities  $\psi = \phi^c$ ,  $\phi = \psi^c$ , we get

$$
\psi(x) \le \sup_{y} \phi(y) \le \psi(x_0) + ||c||;
$$
  
$$
\phi(y) \ge \inf_{x} \psi(x) \ge \phi(y_0) - ||c||.
$$

So both  $\psi$  and  $\phi$  are bounded from above and below.

Thus we can integrate  $\phi$ ,  $\psi$  against  $\mu$ ,  $\nu$  respectively, and, by the marginal condition,

$$
\int \phi(y) d\nu(y) - \int \psi(x) d\mu(x) = \int [\phi(y) - \psi(x)] d\pi(x, y).
$$

Since  $\phi(y) - \psi(x) = c(x, y)$  on the support of  $\pi$ , the latter quantity equals  $\int c(x,y) d\pi(x,y)$ . It follows that (5.4) is actually an equality, which proves the duality.

Step 5: If c is lower semicontinuous, then there is duality.

Since  $c$  is nonnegative lower semicontinuous, we can write

$$
c(x, y) = \lim_{k \to \infty} c_k(x, y),
$$

where  $(c_k)_{k \in \mathbb{N}}$  is a nondecreasing sequence of bounded, uniformly continuous functions. To see this, just choose

$$
c_k(x,y) = \inf_{(x',y')} \Big\{ \min \Big( c(x',y'),k \Big) + k \Big[ d(x,x') + d(y,y') \Big] \Big\};
$$

note that  $c_k$  is k-Lipschitz, nondecreasing in  $k$ , and further satisfies  $0 \le c_k(x,y) \le \min(c(x,y),k)^2$ 

By Step 4, for each k we can find  $\pi_k$ ,  $\phi_k$ ,  $\psi_k$  such that  $\psi_k$  is bounded and c-convex,  $\phi_k = (\psi_k)^c$ , and

$$
\int c_k(x,y) d\pi_k(x,y) = \int \phi_k(y) d\nu(y) - \int \psi_k(x) d\mu(x).
$$

Since  $c_k$  is no greater than c, the constraint  $\phi_k(y) - \psi_k(x) \leq c_k(x,y)$ implies  $\phi_k(y) - \psi_k(x) \leq c(x, y)$ ; so all  $(\phi_k, \psi_k)$  are admissible in the

<sup>&</sup>lt;sup>2</sup> It is instructive to understand exactly where the lower semicontinuity assumption is used to show  $c = \lim c_k$ .

## 5 Cyclical monotonicity and Kantorovich duality

dual problem with cost c. Moreover, for each k the functions  $\phi_k$  and  $\psi_k$  are uniformly continuous because c itself is uniformly continuous.

By Lemma 4.4,  $\Pi(\mu,\nu)$  is weakly sequentially compact. Thus, up to extraction of a subsequence, we can assume that  $\pi_k$  converges to some  $\widetilde{\pi} \in \Pi(\mu, \nu)$ . For all indices  $\ell \leq k$ , we have  $c_{\ell} \leq c_k$ , so

$$
\int c_{\ell} d\tilde{\pi} = \lim_{k \to \infty} \int c_{\ell} d\pi_k
$$

$$
\leq \limsup_{k \to \infty} \int c_k d\pi_k
$$

$$
= \limsup_{k \to \infty} \left( \int \phi_k(y) d\nu(y) - \int \psi_k(x) d\mu(x) \right).
$$

On the other hand, by monotone convergence,

$$
\int c\,d\widetilde{\pi}=\lim_{\ell\to\infty}\int c_{\ell}\,d\widetilde{\pi}.
$$

So

$$
\inf_{\Pi(\mu,\nu)} \int c \, d\pi \le \int c \, d\tilde{\pi} \le \limsup_{k \to \infty} \left( \int \phi_k(y) \, d\nu(y) - \int \psi_k(x) \, d\mu(x) \right)
$$
$$
\le \inf_{\Pi(\mu,\nu)} \int c \, d\pi.
$$

Moreover,

$$
\int \phi_k(y) \, d\nu(y) - \int \psi_k(x) \, d\mu(x) \xrightarrow[k \to \infty]{} \inf_{\Pi(\mu,\nu)} \int c \, d\pi. \tag{5.20}
$$

Since each pair  $(\psi_k, \phi_k)$  lies in  $C_b(\mathcal{X}) \times C_b(\mathcal{Y})$ , the duality also holds with bounded continuous (and even Lipschitz) test functions, as claimed in Theorem 5.10(i).  $□$ 

Proof of Theorem 5.10, Part (ii). From now on, I shall assume that the optimal transport cost  $C(\mu, \nu)$  is finite, and that c is real-valued. As in the proof of Part  $(i)$  I shall assume that c is nonnegative, since the general case can always be reduced to that particular case. Part (ii) will be established in the following way: (a)  $\Rightarrow$  (b)  $\Rightarrow$  (c)  $\Rightarrow$  (d)  $\Rightarrow$  $(a) \Rightarrow (e) \Rightarrow (b)$ . There seems to be some redundancy in this chain of implications, but this is because the implication (a)  $\Rightarrow$  (c) will be used to construct the set  $\Gamma$  appearing in (e).

(a)  $\Rightarrow$  (b): Let  $\pi$  be an optimal plan, and let  $(\phi_k, \psi_k)_{k \in \mathbb{N}}$  be as in Step 5 of the proof of Part (i). Since the optimal transport cost is finite by assumption, the cost function c belongs to  $L^1(\pi)$ . From (5.20) and the marginal property of  $\pi$ ,

$$
\int \left[c(x,y) - \phi_k(y) + \psi_k(x)\right] d\pi(x,y) \xrightarrow[k \to \infty]{} 0,
$$

so  $c(x,y) - \phi_k(y) + \psi_k(x)$  converges to 0 in  $L^1(\pi)$  as  $k \to \infty$ . Up to choosing a subsequence, we can assume that the convergence is almost sure; then  $\phi_k(y_i) - \psi_k(x_i)$  converges to  $c(x_i, y_i)$ ,  $\pi(dx_i dy_i)$ -almost surely, as  $k \to \infty$ . By passing to the limit in the inequality

$$
\sum_{i=1}^{N} c(x_i, y_{i+1}) \ge \sum_{i=1}^{N} [\phi_k(y_{i+1}) - \psi_k(x_i)] = \sum_{i=1}^{N} [\phi_k(y_i) - \psi_k(x_i)]
$$

(where by convention  $y_{N+1} = y_1$ ) we see that,  $\pi^{\otimes N}$ -almost surely,

$$
\sum_{i=1}^{N} c(x_i, y_{i+1}) \ge \sum_{i=1}^{N} c(x_i, y_i).
$$
 (5.21)

At this point we know that  $\pi^{\otimes N}$  is concentrated on some set  $\Gamma_N \subset \mathbb{R}^N$  $(\mathcal{X} \times \mathcal{Y})^N$ , such that  $\Gamma_N$  consists of N-tuples  $((x_1, y_1), \ldots, (x_N, y_N))$ satisfying (5.21). Let  $\text{proj}_k((x_i, y_i)_{\substack{1 \leq i \leq N}}) := (x_k, y_k)$  be the projection on the k<sup>th</sup> factor of  $(X \times Y)^N$ . It is not difficult to check that  $\Gamma := \bigcap_{1 \leq k \leq N} \text{proj}_k(\Gamma_N)$  is a c-cyclically monotone set which has full  $\pi$ -measure; so  $\pi$  is indeed *c*-cyclically monotone.

(b)  $\Rightarrow$  (c): Let  $\pi$  be a cyclically monotone transference plan. The function  $\psi$  can be constructed just as in Step 3 of the proof of Part (i), only with some differences. First,  $\Gamma$  is not necessarily closed; it is just a Borel set such that  $\pi[\Gamma] = 1$ . (If  $\Gamma$  is not Borel, make it Borel by modifying it on a negligible set.) With this in mind, define, as in Step 3 of Part (i),

$$
\psi(x) := \sup_{m \in \mathbb{N}} \sup \Big\{ \Big[ c(x_0, y_0) - c(x_1, y_0) \Big] + \Big[ c(x_1, y_1) - c(x_2, y_1) \Big] + \cdots + \Big[ c(x_m, y_m) - c(x, y_m) \Big]; \quad (x_1, y_1), \ldots, (x_m, y_m) \in \Gamma \Big\}. \tag{5.22}
$$

From its definition, for any  $x \in \mathcal{X}$ ,

82 5 Cyclical monotonicity and Kantorovich duality

$$
\psi(x) \ge c(x_0, y_0) - c(x, y_0) > -\infty.
$$

(Here the assumption of c being real-valued is useful.) Then there is no difficulty in proving, as in Step 3, that  $\psi(x_0) = 0$ , that  $\psi$  is c-convex, and that  $\pi$  is concentrated on  $\partial_c \psi$ .

The rest of this step will be devoted to the *measurability* of  $\psi$ ,  $\psi^c$ and  $\partial_c \psi$ . These are surprisingly subtle issues, which do not arise if c is continuous; so the reader who only cares for a continuous cost function might go directly to the next step.

First, the measurability of  $\psi$  is not clear at all from formula (5.22): This is typically an uncountable supremum of upper semicontinuous functions, and there is no a priori reason for this to be Borel measurable.

Since  $c$  is nonnegative lower semicontinuous, there is a nondecreasing sequence  $(c_{\ell})_{\ell \in \mathbb{N}}$  of continuous nonnegative functions, such that  $c_{\ell}(x,y)$ converges to  $c(x,y)$  as  $\ell \to \infty$ , for all  $(x,y)$ . By Egorov's theorem, for each  $k \in \mathbb{N}$  there is a Borel set  $E_k$  with  $\pi[E_k] \leq 1/k$ , such that the convergence of  $c_{\ell}$  to c is uniform on  $\Gamma \setminus E_k$ . Since  $\pi$  (just as any probability measure on a Polish space) is regular, we can find a compact set  $\Gamma_k \subset \Gamma \backslash E_k$ , such that  $\pi[\Gamma_k] \geq 1-2/k$ . There is no loss of generality in assuming that the sets  $\Gamma_k$  are increasing in k.

On each  $\Gamma_k$ , the sequence  $(c_{\ell})$  converges uniformly and monotonically to c; in particular c is continuous on  $\Gamma_k$ . Furthermore, since  $\pi$  is obviously concentrated on the union of all  $\Gamma_k$ , there is no loss of generality in assuming that  $\Gamma = \cup \Gamma_k$ . We may also assume that  $(x_0, y_0) \in \Gamma_1$ .

Now, let x be given in  $\mathcal{X}$ , and for each  $k, \ell, m$ , let

$$
F_{m,k,\ell}(x_0,y_0,\ldots,x_m,y_m) := [c(x_0,y_0) - c_{\ell}(x_1,y_0)] + [c(x_1,y_1) - c_{\ell}(x_2,y_1)] + \cdots + [c(x_m,y_m) - c_{\ell}(x,y_m)],
$$

for  $(x_0, y_0, \ldots, x_m, y_m) \in \Gamma_k^m$ . It is clear that  $F_{m,k,\ell}$  is a continuous function (because  $c_{\ell}$  is continuous on  $\mathcal{X} \times \mathcal{X}$ , and c is continuous on  $\Gamma_k$ ). It is defined on the compact set  $\Gamma_k^m$ , and it is nonincreasing as a function of  $\ell$ , with

$$
\lim_{\ell \to \infty} F_{m,k,\ell} = F_{m,k},
$$

where

$$
F_{m,k}(x_0, y_0, \dots, x_m, y_m) := [c(x_0, y_0) - c(x_1, y_0)] + [c(x_1, y_1) - c(x_2, y_1)] + \dots + [c(x_m, y_m) - c(x, y_m)].
$$

Now I claim that

$$
\lim_{\ell \to \infty} \sup_{\Gamma_k^m} F_{m,k,\ell} = \sup_{\Gamma_k^m} F_{m,k}.
$$
\n(5.23)

Indeed, by compactness, for each  $\ell \in \mathbb{N}$  there is  $X_\ell \in \varGamma_k^m$  such that

$$
\sup_{\Gamma_k^m} F_{m,k,\ell} = F_{m,k,\ell}(X_{\ell});
$$

and up to extraction of a subsequence, one may assume that  $X_\ell$  converges to some X. Then by monotonicity, for any  $\ell' \leq \ell$ ,

$$
\sup_{\Gamma_k^m} F_{m,k,\ell} = F_{m,k,\ell}(X_{\ell}) \le F_{m,k,\ell'}(X_{\ell});
$$

and if one lets  $\ell \to \infty$ , with  $\ell'$  fixed, one obtains

$$
\limsup_{\ell \to \infty} \sup_{\Gamma_k^m} F_{m,k,\ell} \leq F_{m,k,\ell'}(X).
$$

Now let  $\ell' \to \infty$ , to get

$$
\limsup_{\ell \to \infty} \sup_{\Gamma_k^m} F_{m,k,\ell} \leq F_{m,k}(X) \leq \sup_{\Gamma_k^m} F_{m,k}.
$$

The converse inequality

$$
\sup_{\Gamma_k^m} F_{m,k} \leq \liminf_{\ell \to \infty} \sup_{\Gamma_k^m} F_{m,k,\ell}
$$

is obvious because  $F_{m,k} \leq F_{m,k,\ell}$ ; so (5.23) is proven. To summarize: If we let

$$
\psi_{m,k,\ell}(x) := \sup \Big\{ \big[c(x_0, y_0) - c_{\ell}(x_1, y_0) \big] + \big[c(x_1, y_1) - c_{\ell}(x_2, y_1) \big] + \cdots + \big[c(x_m, y_m) - c_{\ell}(x, y_m) \big]; \quad (x_1, y_1), \ldots, (x_m, y_m) \in \Gamma_k \Big\},\
$$

then we have

$$
\lim_{\ell \to \infty} \psi_{m,k,\ell}(x) = \sup \Big\{ \Big[ c(x_0, y_0) - c(x_1, y_0) \Big] + \Big[ c(x_1, y_1) - c(x_2, y_1) \Big] + \cdots + \Big[ c(x_m, y_m) - c(x, y_m) \Big]; \quad (x_1, y_1), \ldots, (x_m, y_m) \in \Gamma_k \Big\}.
$$

It follows easily that, for each  $x$ ,

84 5 Cyclical monotonicity and Kantorovich duality

$$
\psi(x) = \sup_{m \in \mathbb{N}} \sup_{k \in \mathbb{N}} \lim_{\ell \to \infty} \psi_{m,k,\ell}(x).
$$

Since  $\psi_{m,k,\ell}(x)$  is lower semicontinuous in x (as a supremum of continuous functions of x),  $\psi$  itself is measurable.

The measurability of  $\phi := \psi^c$  is subtle also, and at the present level of generality it is not clear that this function is really Borel measurable. However, it can be modified on a  $\nu$ -negligible set so as to become measurable. Indeed,  $\phi(y) - \psi(x) = c(x, y)$ ,  $\pi(dx\,dy)$ -almost surely, so if one disintegrates  $\pi(dx\,dy)$  as  $\pi(dx|y)\nu(dy)$ , then  $\phi(y)$ will coincide,  $\nu(dy)$ -almost surely, with the Borel function  $\phi(y) :=$ R  $\int_{\mathcal{X}}[\psi(x)+c(x,y)]\,\pi(dx|y).$ 

Then let Z be a Borel set of zero  $\nu$ -measure such that  $\widetilde{\phi} = \phi$  outside of Z. The subdifferential  $\partial_c \psi$  coincides, out of the  $\pi$ -negligible set  $\mathcal{X} \times \mathcal{Z}$ , with the measurable set  $\{(x,y) \in \mathcal{X} \times \mathcal{Y}; \phi(y) - \psi(x) = c(x,y)\}.$  The conclusion is that  $\partial_c \psi$  can be modified on a  $\pi$ -negligible set so as to be Borel measurable.

(c)  $\Rightarrow$  (d): Just let  $\phi = \psi^c$ .

(d)  $\Rightarrow$  (a): Let  $(\psi, \phi)$  be a pair of admissible functions, and let  $\pi$  be a transference plan such that  $\phi - \psi = c$ ,  $\pi$ -almost surely. The goal is to show that  $\pi$  is optimal. The main difficulty lies in the fact that  $\psi$  and  $\phi$  need not be *separately* integrable. This problem will be circumvented by a careful truncation procedure. For  $n \in \mathbb{N}$ ,  $w \in \mathbb{R} \cup \{\pm \infty\}$ , define

$$
T_n(w) = \begin{cases} w & \text{if } |w| \le n \\ n & \text{if } w > n \\ -n & \text{if } w < -n, \end{cases}
$$

and

$$
\xi(x, y) := \phi(y) - \psi(x);
$$
  $\xi_n(x, y) := T_n(\phi(y)) - T_n(\psi(x)).$ 

In particular,  $\xi_0 = 0$ . It is easily checked that  $\xi_n$  converges monotonically to  $\xi$ ; more precisely,

•  $\xi_n(x,y)$  remains equal to 0 if  $\xi(x,y) = 0$ ;

- $\xi_n(x,y)$  increases to  $\xi(x,y)$  if the latter quantity is positive;
- $\xi_n(x, y)$  decreases to  $\xi(x, y)$  if the latter quantity is negative.

As a consequence,  $\xi_n \leq (\xi_n)_+ \leq \xi_+ \leq c$ . So  $(T_n \phi, T_n \psi)$  is an admissible pair in the dual Kantorovich problem, and

Kantorovich duality 85

$$
\int \xi_n d\pi = \int (T_n \phi) d\nu - \int (T_n \psi) d\mu \le \sup_{\phi' - \psi' \le c} \left( \int \phi' d\mu - \int \psi' d\nu \right).
$$
\n(5.24)

On the other hand, by monotone convergence and since  $\xi$  coincides with c outside of a  $\pi$ -negligible set,

$$
\int_{\xi \ge 0} \xi_n d\pi \xrightarrow[n \to \infty]{} \int_{\xi \ge 0} \xi d\pi = \int c d\pi;
$$
  
$$
\int_{\xi < 0} \xi_n d\pi \xrightarrow[n \to \infty]{} \int_{\xi < 0} \xi d\pi = 0.
$$

This and (5.24) imply that

$$
\int c d\pi \leq \sup_{\phi' - \psi' \leq c} \left( \int \phi' d\mu - \int \psi' d\nu \right);
$$

so  $\pi$  is optimal.

Before completing the chain of equivalences, we should first construct the set  $\Gamma$ . By Theorem 4.1, there is at least one optimal transference plan, say  $\tilde{\pi}$ . From the implication (a)  $\Rightarrow$  (c), there is some  $\psi$ such that  $\widetilde{\pi}$  is concentrated on  $\partial_c \psi$ ; just choose  $\Gamma := \partial_c \psi$ .

(a)  $\Rightarrow$  (e): Let  $\tilde{\pi}$  be the optimal plan used to construct  $\Gamma$ , and let  $\psi = \psi$  be the associated c-convex function; let  $\phi = \psi^c$ . Then let  $\pi$ be another optimal plan. Since  $\pi$  and  $\widetilde{\pi}$  have the same cost and same marginals,

$$
\int c d\pi = \int c d\tilde{\pi} = \lim_{n \to \infty} \int (T_n \phi - T_n \psi) d\tilde{\pi}
$$
$$
= \lim_{n \to \infty} \int (T_n \phi - T_n \psi) d\pi,
$$

where  $T_n$  is the truncation operator that was already used in the proof of  $(d) \Rightarrow (a)$ . So

$$
\int \left[c(x,y) - T_n \phi(y) + T_n \psi(x)\right] d\pi(x,y) \xrightarrow[n \to \infty]{} 0.
$$
 (5.25)

As before, define  $\xi(x,y) := \phi(y) - \psi(x)$ ; then by monotone convergence,

$$
\int_{\xi \ge 0} \left[ c - T_n \phi + T_n \psi \right] d\pi \xrightarrow[n \to \infty]{} \int_{\xi \ge 0} (c - \xi) d\pi;
$$

86 5 Cyclical monotonicity and Kantorovich duality

$$
\int_{\xi < 0} \left[ c - T_n \phi + T_n \psi \right] d\pi \xrightarrow[n \to \infty]{} \int_{\xi < 0} (c - \xi) d\pi.
$$

Since  $\xi \leq c$ , the integrands here are nonnegative and both integrals make sense in  $[0, +\infty]$ . So by adding the two limits and using (5.25) we get

$$
\int (c - \xi) d\pi = \lim_{n \to \infty} \int [c - T_n \phi + T_n \psi] = 0.
$$

Since  $\xi \leq c$ , this proves that c coincides  $\pi$ -almost surely with  $\xi$ , which was the desired conclusion.

(e)  $\Rightarrow$  (b): This is obvious since  $\Gamma$  is cyclically monotone by assumption. tion. ⊓⊔

Proof of Theorem 5.10, Part (iii). As in the proof of Part (i) we may assume that  $c \geq 0$ . Let  $\pi$  be optimal, and let  $\psi$  be a c-convex function such that  $\pi$  is concentrated on  $\partial_c \psi$ . Let  $\phi := \psi^c$ . The goal is to show that under the assumption  $c \leq c_{\mathcal{X}} + c_{\mathcal{Y}}$ ,  $(\psi, \phi)$  solves the dual Kantorovich problem.

The point is to prove that  $\psi$  and  $\phi$  are integrable. For this we repeat the estimates of Step 4 in the proof of Part (i), with some variants: After securing  $(x_0, y_0)$  such that  $\phi(y_0), \psi(x_0), c_{\mathcal{X}}(x_0)$  and  $c_{\mathcal{Y}}(y_0)$  are finite, we write

$$
\psi(x) + c_{\mathcal{X}}(x) = \sup_{y} [\phi(y) - c(x, y) + c_{\mathcal{X}}(x)]
$$

$$
\ge \sup_{y} [\phi(y) - c_{\mathcal{Y}}(y)]
$$

$$
\ge \phi(y_0) - c_{\mathcal{Y}}(y_0);
$$

$$
\phi(y) - c_{\mathcal{Y}}(y) = \inf_{x} [\psi(x) + c(x, y) - c_{\mathcal{Y}}(y)] \le \inf_{x} [\psi(x) + c_{\mathcal{X}}(x)]
$$
  
$$
\le \psi(x_0) + c_{\mathcal{X}}(x_0).
$$

So  $\psi$  is bounded below by the  $\mu$ -integrable function  $\phi(y_0)-c_{\mathcal{Y}}(y_0)-c_{\mathcal{X}}$ , and  $\phi$  is bounded above by the *v*-integrable function  $\psi(x_0)+c_{\mathcal{X}}(x_0)+c_{\mathcal{Y}};$ thus both  $-\int \psi \, d\mu$  and  $\int \phi \, d\nu$  make sense in  $\mathbb{R} \cup \{-\infty\}$ . Since their sum is  $\int (\phi - \psi) d\pi = \int c d\pi > -\infty$ , both integrals are finite. So

$$
\int c d\pi = \int \phi d\nu - \int \psi d\mu,
$$

and it results from Part (i) of the theorem that both  $\pi$  and  $(\psi, \phi)$  are optimal, respectively in the original and the dual Kantorovich problems.

To prove the last part of (iii), assume that  $c$  is continuous; then the subdifferential of any c-convex function is a closed c-cyclically monotone set.

Let  $\pi$  be an arbitrary optimal transference plan, and  $(\psi, \phi)$  an optimal pair of prices. We know that  $(\psi, \psi^c)$  is optimal in the dual Kantorovich problem, so

$$
\int c(x,y) d\pi(x,y) = \int \psi^c d\nu - \int \psi d\mu.
$$

Thanks to the marginal condition, this be rewritten as

$$
\int \left[\psi^{c}(y) - \psi(x) - c(x, y)\right] d\pi(x, y) = 0.
$$

Since the integrand is nonnegative, it follows that  $\pi$  is concentrated on the set of pairs  $(x, y)$  such that  $\psi^c(y) - \psi(x) - c(x, y) = 0$ , which is precisely the subdifferential of  $\psi$ . Thus any optimal transference plan is concentrated on the subdifferential of any optimal  $\psi$ . So if  $\Gamma$  is defined as the intersection of all subdifferentials of optimal functions  $\psi$ , then  $\Gamma$  also contains the supports of all optimal plans.

Conversely, if  $\tilde{\pi} \in \Pi(\mu, \nu)$  is a transference plan concentrated on  $\Gamma$ , then  $\int c d\tilde{\pi} = \int [\psi^c - \psi] d\tilde{\pi} = \int \psi^c d\nu - \int \psi d\mu$ , so  $\tilde{\pi}$  is optimal. Similarly, if  $\widetilde{\psi}$  is a c-convex function such that  $\partial_c \widetilde{\psi}$  contains  $\Gamma$ , then by the previous estimates  $\psi$  and  $\psi^c$  are integrable against  $\mu$  and  $\nu$ respectively, and  $\int c d\pi = \int [\tilde{\psi}^c - \tilde{\psi}] d\pi = \int \tilde{\psi}^c d\nu - \int \tilde{\psi} d\mu$ , so  $(\tilde{\psi}, \tilde{\psi}^c)$ is optimal. This concludes the proof. ⊓⊔

## Restriction property

The dual side of the Kantorovich problem also behaves well with respect to restriction, as shown by the following results.

**Lemma 5.18 (Restriction of c-convexity).** Let  $\mathcal{X}, \mathcal{Y}$  be two sets and  $c: \mathcal{X} \times \mathcal{Y} \to \mathbb{R} \cup \{+\infty\}$ . Let  $\mathcal{X}' \subset \mathcal{X}$ ,  $\mathcal{Y}' \subset \mathcal{Y}$  and let  $c'$  be the restriction of c to  $\mathcal{X}' \times \mathcal{Y}'$ . Let  $\psi : \mathcal{X} \to \mathbb{R} \cup \{+\infty\}$  be a c-convex function. Then there is a c'-convex function  $\psi': \mathcal{X}' \to \mathbb{R} \cup \{+\infty\}$  such that  $\psi' \leq \psi$  on  $\mathcal{X}'$ ,  $\psi'$  coincides with  $\psi$  on  $\text{proj}_{\mathcal{X}}((\partial_c \psi) \cap (\mathcal{X}' \times \mathcal{Y}'))$ and  $\partial_c \psi \cap (\mathcal{X}' \times \mathcal{Y}') \subset \partial_{c'} \psi'.$ 

Theorem 5.19 (Restriction for the Kantorovich duality theorem). Let  $(\mathcal{X}, \mu)$  and  $(\mathcal{Y}, \nu)$  be two Polish probability spaces, let  $a \in L^1(\mu)$  and  $b \in L^1(\nu)$  be real-valued upper semicontinuous functions, and let  $c: \mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  be a lower semicontinuous cost function such that  $c(x,y) \geq a(x) + b(y)$  for all x,y. Assume that the optimal transport cost  $C(\mu, \nu)$  between  $\mu$  and  $\nu$  is finite. Let  $\pi$  be an optimal transference plan, and let  $\psi$  be a c-convex function such that  $\pi$  is concentrated on  $\partial_c \psi$ . Let  $\tilde{\pi}$  be a measure on  $\mathcal{X} \times \mathcal{Y}$  satisfying  $\tilde{\pi} \leq \pi$ , and  $Z = \widetilde{\pi}[X \times Y] > 0$ ; let  $\pi' := \widetilde{\pi}/Z$ , and let  $\mu'$ ,  $\nu'$  be the marginals of  $\pi'$ . Let  $\mathcal{X}' \subset \mathcal{X}$  be a closed set containing  $\text{Spt}\,\mu'$  and  $\mathcal{Y}' \subset \mathcal{Y}$  a closed set containing Spt  $\nu'$ . Let  $c'$  be the restriction of c to  $\mathcal{X}' \times \mathcal{Y}'$ . Then there is a c'-convex function  $\psi': \mathcal{X}' \to \mathbb{R} \cup \{+\infty\}$  such that

(a)  $\psi'$  coincides with  $\psi$  on  $\text{proj}_{\mathcal{X}}((\partial_c \psi) \cap (\mathcal{X}' \times \mathcal{Y}'))$ , which has full  $\mu'$ -measure;

(b)  $\pi'$  is concentrated on  $\partial_{c'}\psi'$ ;

(c)  $\psi'$  solves the dual Kantorovich problem between  $(\mathcal{X}', \mu')$  and  $(\mathcal{Y}', \nu')$  with cost c'.

In spite of its superficially complicated appearance, Theorem 5.19 is very simple. If the reader feels that it is obvious, or alternatively that it is obscure, he or she may just skip the proofs and retain the loose statement that "it is always possible to restrict the cost function".

*Proof of Lemma 5.18*. Let  $\phi = \psi^c$ . For  $y \in \mathcal{Y}'$ , define

$$
\phi'(y) = \begin{cases} \phi(y) & \text{if } \exists x' \in \mathcal{X'}; \\ -\infty & \text{otherwise.} \end{cases} (x', y) \in \partial_c \psi;
$$

For  $x \in \mathcal{X}'$  let then

$$
\psi'(x) = \sup_{y \in \mathcal{Y}'} \left[ \phi'(y) - c(x, y) \right] = \sup_{y \in \mathcal{Y}'} \left[ \phi'(y) - c'(x, y) \right].
$$

By construction,  $\psi'$  is c'-convex. Since  $\phi' \leq \phi$  and  $\mathcal{Y}' \subset \mathcal{Y}$  it is obvious that

$$
\forall x \in \mathcal{X}', \qquad \psi'(x) \le \sup_{y \in \mathcal{Y}} \left[ \phi(y) - c(x, y) \right] = \psi(x).
$$

Let  $x \in \text{proj}_{\mathcal{X}}((\partial_c \psi) \cap (\mathcal{X}' \cap \mathcal{Y}'))$ ; this means that there is  $y \in \mathcal{Y}'$  such that  $(x, y) \in \partial_c \psi$ . Then  $\phi'(y) = \phi(y)$ , so

$$
\psi'(x) \ge \phi'(y) - c(x, y) = \phi(y) - c(x, y) = \psi(x).
$$

Thus  $\psi'$  does coincide with  $\psi$  on  $\text{proj}_{\mathcal{X}}((\partial_c \psi) \cap (\mathcal{X}' \times \mathcal{Y}'))$ .

Finally, let  $(x, y) \in \partial_c \psi \cap (\mathcal{X}' \times \mathcal{Y}')$ , then  $\phi'(y) = \phi(y)$ ,  $\psi'(x) = \psi(x)$ ; so for all  $z \in \mathcal{X}'$ ,

$$
\psi'(x) + c'(x, y) = \psi(x) + c(x, y) = \phi(y) = \phi'(y) \le \psi'(z) + c'(z, y),
$$

which means that  $(x, y) \in \partial_{c'} \psi'$ .

*Proof of Theorem 5.19.* Let  $\psi'$  be defined by Lemma 5.18. To prove (a), it suffices to note that  $\pi'$  is concentrated on  $(\partial_c \psi) \cap (\mathcal{X}' \times \mathcal{Y}')$ , so  $\mu'$  is concentrated on  $proj_{\mathcal{X}}((\partial_c \psi) \cap (\mathcal{X}' \times \mathcal{Y}'))$ . Then  $\pi$  is concentrated on  $\partial_c \psi$ , so  $\tilde{\pi}$  is concentrated on  $\partial_c \psi \cap (\mathcal{X}' \times \mathcal{Y}')$ , which by Lemma 5.18 is included in  $\partial_{c'}\psi'$ ; this proves (b). Finally, (c) follows from Theorem 5.10(ii). □

The rest of this chapter is devoted to various applications of Theorem 5.10.

# Application: Stability

An important consequence of Theorem 5.10 is the stability of optimal transference plans. For simplicity I shall prove it under the assumption that c is bounded below.

Theorem 5.20 (Stability of optimal transport). Let X and Y be Polish spaces, and let  $c \in C(X \times Y)$  be a real-valued continuous cost function, inf  $c > -\infty$ . Let  $(c_k)_{k \in \mathbb{N}}$  be a sequence of continuous cost functions converging uniformly to c on  $\mathcal{X} \times \mathcal{Y}$ . Let  $(\mu_k)_{k \in \mathbb{N}}$  and  $(\nu_k)_{k \in \mathbb{N}}$ be sequences of probability measures on  $\mathcal X$  and  $\mathcal Y$  respectively. Assume that  $\mu_k$  converges to  $\mu$  (resp.  $\nu_k$  converges to  $\nu$ ) weakly. For each k, let  $\pi_k$  be an optimal transference plan between  $\mu_k$  and  $\nu_k$ . If

$$
\forall k \in \mathbb{N}, \qquad \int c_k \, d\pi_k < +\infty,
$$

then, up to extraction of a subsequence,  $\pi_k$  converges weakly to some c-cyclically monotone transference plan  $\pi \in \Pi(\mu, \nu)$ .

If moreover

$$
\liminf_{k \in \mathbb{N}} \int c_k d\pi_k < +\infty,
$$

then the optimal total transport cost  $C(\mu,\nu)$  between  $\mu$  and  $\nu$  is finite, and  $\pi$  is an optimal transference plan.

Corollary 5.21 (Compactness of the set of optimal plans). Let X and Y be Polish spaces, and let  $c(x, y)$  be a real-valued continuous cost function, inf  $c > -\infty$ . Let K and L be two compact subsets of  $P(X)$  and  $P(Y)$  respectively. Then the set of optimal transference plans  $\pi$  whose marginals respectively belong to K and L is itself compact in  $P(X \times Y)$ .

*Proof of Theorem 5.20.* Since  $\mu_k$  and  $\nu_k$  are convergent sequences, by Prokhorov's theorem they constitute tight sets, and then by Lemma 4.4 the measures  $\pi_k$  all lie in a tight set of  $\mathcal{X} \times \mathcal{Y}$ ; so we can extract a further subsequence, still denoted  $(\pi_k)$  for simplicity, which converges weakly to some  $\pi \in \Pi(\mu, \nu)$ .

To prove that  $\pi$  is c-monotone, the argument is essentially the same as in Step 2 of the proof of Theorem 5.10(i). Indeed, by Theorem 5.10, each  $\pi_k$  is concentrated on a  $c_k$ -cyclically monotone set; so  $\pi_k^{\otimes N}$  is concentrated on the set  $\mathcal{C}_k(N)$  of  $((x_1,y_1),\ldots,(x_N,y_N))$  such that

$$
\sum_{1 \leq i \leq N} c_k(x_i, y_i) \leq \sum_{1 \leq i \leq N} c_k(x_i, y_{i+1}),
$$

where as usual  $y_{N+1} = y_1$ . So if  $\varepsilon > 0$  and N are given, for k large enough  $\pi_k^{\otimes N}$  is concentrated on the set  $\mathcal{C}_{\varepsilon}(N)$  defined by

$$
\sum_{1 \le i \le N} c(x_i, y_i) \le \sum_{1 \le i \le N} c(x_i, y_{i+1}) + \varepsilon.
$$

Since this is a closed set, the same is true for  $\pi^{\otimes N}$ , and then by letting  $\varepsilon \to 0$  we see that  $\pi^{\otimes N}$  is concentrated on the set  $\mathcal{C}(N)$  defined by

$$
\sum_{1 \le i \le N} c(x_i, y_i) \le \sum_{1 \le i \le N} c(x_i, y_{i+1}).
$$

So the support of  $\pi$  is c-cyclically monotone, as desired.

Now assume that  $\liminf_{k\to\infty} \int c_k d\pi_k < +\infty$ . Then by the same argument as in the proof of Theorem 4.1,

$$
\int c d\pi \le \liminf_{k \to \infty} \int c_k d\pi_k < +\infty.
$$

In particular,  $C(\mu, \nu) < +\infty$ ; so Theorem 5.10(ii) guarantees the optimality of  $\pi$ .  $□$ 

An immediate consequence of Theorem 5.20 is the possibility to select optimal transport plans in a measurable way.

Corollary 5.22 (Measurable selection of optimal plans). Let  $\mathcal{X},$  $\mathcal Y$  be Polish spaces and let  $c: \mathcal X \times \mathcal Y \to \mathbb R$  be a continuous cost function, inf  $c > -\infty$ . Let  $\Omega$  be a measurable space and let  $\omega \mapsto (\mu_{\omega}, \nu_{\omega})$  be a measurable function  $\Omega \to P(\mathcal{X}) \times P(\mathcal{Y})$ . Then there is a measurable choice  $\omega \mapsto \pi_{\omega}$  such that for each  $\omega$ ,  $\pi_{\omega}$  is an optimal transference plan between  $\mu_{\omega}$  and  $\nu_{\omega}$ .

*Proof of Corollary 5.22.* Let  $\mathcal O$  be the set of all optimal transference plans, equipped with the weak topology on  $P(\mathcal{X} \times \mathcal{Y})$ , and let  $\Phi : \mathcal{O} \rightarrow$  $P(X) \times P(Y)$  be the map which to  $\pi$  associates its marginals  $(\mu, \nu)$ . Obviously  $\Phi$  is continuous. By Theorem 5.20,  $\mathcal O$  is closed; in particular it is a Polish space. By Theorem 4.1,  $\Phi$  is onto. By Corollary 5.21 all pre-images  $\Phi^{-1}(\mu,\nu)$  are compact. So the conclusion follows from general theorems of measurable selection (see the bibliographical notes for more information). □

Theorem 5.20 also admits the following corollary about the stability of transport maps.

Corollary 5.23 (Stability of the transport map). Let X be a locally compact Polish space, and let  $(\mathcal{Y}, d)$  be another Polish space. Let  $c: \mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  be a lower semicontinuous function with  $\inf c > -\infty$ , and for each  $k \in \mathbb{N}$  let  $c_k : \mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  be lower semicontinuous, such that  $c_k$  converges uniformly to c. Let  $\mu \in P(\mathcal{X})$  and let  $(\nu_k)_{k \in \mathbb{N}}$  be a sequence of probability measures on  $\mathcal{Y}$ , converging weakly to  $\nu \in P(\mathcal{Y})$ . Assume the existence of measurable maps  $T_k : \mathcal{X} \to \mathcal{Y}$  such that each  $\pi_k := (\mathrm{Id}, T_k)_{\#} \mu$  is an optimal transference plan between  $\mu$  and  $\nu_k$ for the cost  $c_k$ , having finite total transport cost. Further, assume the existence of a measurable map  $T : \mathcal{X} \to \mathcal{Y}$  such that  $\pi := (\mathrm{Id}, T)_{\#}\mu$  is the unique optimal transference plan between  $\mu$  and  $\nu$ , for the cost c, and has finite total transport cost. Then  $T_k$  converges to T in probability:

$$
\forall \varepsilon > 0 \quad \mu \Big[ \Big\{ x \in X; \ d\big(T_k(x), T(x)\big) \ge \varepsilon \Big\} \Big] \xrightarrow[k \to \infty]{} 0. \tag{5.26}
$$

Remark 5.24. An important assumption in the above statement is the uniqueness of the optimal transport map T.

**Remark 5.25.** If the measure  $\mu$  is replaced by a sequence  $(\mu_k)_{k\in\mathbb{N}}$ converging weakly to  $\mu$ , then the maps  $T_k$  and T may be far away from each other, even  $\mu_k$ -almost surely: take for instance  $\mathcal{X} = \mathcal{Y} = \mathbb{R}$ ,  $\mu_k = \delta_{1/k}, \mu = \delta_0, \nu_k = \nu = \delta_0, T_k(x) = 0, T(x) = 1_{x \neq 0}.$ 

Proof of Corollary 5.23. By Theorem 5.20 and uniqueness of the optimal coupling between  $\mu$  and  $\nu$ , we know that  $\pi_k = (\mathrm{Id}, T_k)_{\#} \mu_k$  converges weakly to  $\pi = (\mathrm{Id}, T)_{\#}\mu$ .

Let  $\varepsilon > 0$  and  $\delta > 0$  be given. By Lusin's theorem (in the abstract version recalled in the end of the bibliographical notes) there is a closed set  $K \subset \mathcal{X}$  such that  $\mu[\mathcal{X} \setminus K] \leq \delta$  and the restriction of T to K is continuous. So the set

$$
A_{\varepsilon} = \Big\{ (x, y) \in K \times \mathcal{Y}; \ d(T(x), y) \ge \varepsilon \Big\}.
$$

is closed in  $K \times \mathcal{Y}$ , and therefore closed in  $\mathcal{X} \times \mathcal{Y}$ . Also  $\pi[A_{\varepsilon}] = 0$  since  $\pi$  is concentrated on the graph of T. So, by weak convergence of  $\pi_k$  and closedness of  $A_{\varepsilon}$ ,

$$
0 = \pi[A_{\varepsilon}] \ge \limsup_{k \to \infty} \pi_k[A_{\varepsilon}]
$$
$$
= \limsup_{k \to \infty} \pi_k[\{(x, y) \in K \times \mathcal{Y}; d(T(x), y) \ge \varepsilon\}]
$$
$$
= \limsup_{k \to \infty} \mu[\{x \in K; d(T(x), T_k(x)) \ge \varepsilon\}]
$$
$$
\ge \limsup_{k \to \infty} \mu[\{x \in \mathcal{X}; d(T(x), T_k(x)) \ge \varepsilon\}] - \delta.
$$

Letting  $\delta \to 0$  we conclude that  $\limsup \mu[d(T(x),T_k(x)) \geq \varepsilon] = 0$ ,<br>which was the soal which was the goal.

# Application: Dual formulation of transport inequalities

Let c be a given cost function, and let

$$
C(\mu, \nu) = \inf_{\pi \in \Pi(\mu, \nu)} \int c \, d\pi \tag{5.27}
$$

stand for the value of the optimal transport cost of transport between  $\mu$  and  $\nu$ .

If  $\nu$  is a given reference measure, inequalities of the form

$$
\forall \mu \in P(\mathcal{X}), \qquad C(\mu, \nu) \le F(\mu)
$$

arise in several branches of mathematics; some of them will be studied in Chapter 22. It is useful to know that if  $F$  is a convex function of  $\mu$ , then there is a nice dual reformulation of these inequalities in terms of the Legendre transform of  $F$ . This is the content of the following theorem.

Theorem 5.26 (Dual transport inequalities). Let  $\mathcal{X}, \mathcal{Y}$  be two Polish spaces, and  $\nu$  a given probability measure on  $\mathcal{Y}$ . Let  $F: P(\mathcal{X}) \to$  $\mathbb{R} \cup \{+\infty\}$  be a convex lower semicontinuous function on  $P(\mathcal{X})$ , and  $\Lambda$ its Legendre transform on  $C_b(\mathcal{X})$ ; more explicitly, it is assumed that

\(\forall \mu \in P(\mathcal{X}), F(\mu) = \sup\_{\varphi \in C\_b(\mathcal{X})} \left( \int\_{\mathcal{X}} \varphi \, d\mu - \Lambda(\varphi) \right)\)

\(\forall \varphi \in C\_b(\mathcal{X}), \Lambda(\varphi) = \sup\_{\mu \in P(\mathcal{X})} \left( \int\_{\mathcal{X}} \varphi \, d\mu - F(\mu) \right)\) \(\text{5.28}\)

Further, let  $c : \mathcal{X} \times \mathcal{Y} \to \mathbb{R} \cup \{+\infty\}$  be a lower semicontinuous cost function, inf  $c > -\infty$ . Then, the following two statements are equivalent:

(i) 
$$
\forall \mu \in P(\mathcal{X}),
$$
  $C(\mu, \nu) \le F(\mu);$   
\n(ii)  $\forall \phi \in C_b(\mathcal{Y}),$   $\Lambda \left( \int_{\mathcal{Y}} \phi \, d\nu - \phi^c \right) \le 0$ , where  $\phi^c(x) := \sup_y [\phi(y) - c(x, y)].$ 

Moreover, if  $\Phi : \mathbb{R} \to \mathbb{R}$  is a nondecreasing convex function with  $\Phi(0) = 0$ , then the following two statements are equivalent:

$$
(i') \forall \mu \in P(\mathcal{X}), \qquad \Phi(C(\mu, \nu)) \le F(\mu);
$$
  

$$
(ii') \forall \phi \in C_b(\mathcal{Y}), \forall t \ge 0, \qquad \Lambda \left( t \int_{\mathcal{Y}} \phi \, d\nu - t \, \phi^c - \Phi^*(t) \right) \le 0,
$$
  
where  $\Phi^*$  stands for the Legendre transform of  $\Phi$ .

Remark 5.27. The writing in (ii) or (ii') is not very rigorous since Λ is a priori defined on the set of bounded continuous functions, and  $\phi^c$  might not belong to that set. (It is clear that  $\phi^c$  is bounded from above, but this is all that can be said.) However, from (5.28)  $\Lambda(\varphi)$  is a nondecreasing function of  $\varphi$ , so there is in practice no problem to extend  $\Lambda$  to a more general class of measurable functions. In any case, the correct way to interpret the left-hand side in (ii) is

$$
\Lambda\left(\int_{\mathcal{Y}} \phi \, d\nu - \phi^c\right) = \sup_{\psi \ge \phi^c} \Lambda\left(\int_{\mathcal{Y}} \phi \, d\nu - \psi\right),
$$

where  $\psi$  in the supremum is assumed to be bounded continuous.

**Remark 5.28.** One may simplify (ii) by taking the supremum over  $t$ ; since  $\Lambda$  is nonincreasing, the result is

$$
\Lambda\left(\Phi\left(\int_{\mathcal{Y}} \phi \, d\nu - \phi^c\right)\right) \le 0. \tag{5.29}
$$

(This shows in particular that the equivalence (i)  $\Leftrightarrow$  (ii) is a particular case of the equivalence (i')  $\Leftrightarrow$  (ii').) However, in certain situations it is better to use the inequality (ii') rather than  $(5.29)$ ; see for instance Proposition 22.5.

Example 5.29. The most famous example of inequality of the type of (i) occurs when  $\mathcal{X} = \mathcal{Y}$  and  $F(\mu)$  is the Kullback information of  $\mu$  with respect to  $\nu$ , that is  $F(\mu) = H_{\nu}(\mu) = \int \rho \log \rho \, d\nu$ , where  $\rho =$  $d\mu/d\nu$ ; and by convention  $F(\mu) = +\infty$  if  $\mu$  is not absolutely continuous with respect to  $\nu$ . Then one has the explicit formula

$$
\Lambda(\varphi) = \log \left( \int e^{\varphi} \, d\nu \right).
$$

So the two functional inequalities

$$
\forall \mu \in P(\mathcal{X}), \quad C(\mu, \nu) \le H_{\nu}(\mu)
$$

and

$$
\forall \phi \in C_b(\mathcal{X}), \quad e^{\int \phi \, d\nu} \le \int e^{\phi^c} \, d\nu
$$

are equivalent.

Proof of Theorem 5.26. First assume that (i) is satisfied. Then for all  $\psi \geq \phi^c$ ,

$$
\Lambda \left( \int_{\mathcal{Y}} \phi \, d\nu - \psi \right) = \sup_{\mu \in P(\mathcal{X})} \left\{ \int_{\mathcal{X}} \left( \int_{\mathcal{Y}} \phi \, d\nu - \psi \right) \, d\mu - F(\mu) \right\}
$$
$$
= \sup_{\mu \in P(\mathcal{X})} \left\{ \int_{\mathcal{Y}} \phi \, d\nu - \int_{\mathcal{X}} \psi \, d\mu - F(\mu) \right\}
$$
$$
\leq \sup_{\mu \in P(\mathcal{X})} \left[ C(\mu, \nu) - F(\mu) \right] \leq 0,
$$

where the easiest part of Theorem  $5.10$  (that is, inequality  $(5.4)$ ) was used to go from the next-to-last line to the last one. Then (ii) follows upon taking the supremum over  $\psi$ .

Conversely, assume that (ii) is satisfied. Then, for any pair  $(\psi, \phi) \in$  $C_b(\mathcal{X}) \times C_b(\mathcal{Y})$  one has, by (5.28),

$$
\int_{\mathcal{Y}} \phi \, d\nu - \int_{\mathcal{X}} \psi \, d\mu = \int_{\mathcal{X}} \left( \int_{\mathcal{Y}} \phi \, d\nu - \psi \right) \, d\mu \le \Lambda \left( \int_{\mathcal{Y}} \phi \, d\nu - \psi \right) + F(\mu).
$$

Taking the supremum over all  $\psi \geq \phi^c$  yields

$$
\int_{\mathcal{Y}} \phi \, d\nu - \int_{\mathcal{X}} \phi^c \, d\mu \le \Lambda \left( \int_{\mathcal{Y}} \phi \, d\nu - \phi^c \right) + F(\mu).
$$

By assumption, the first term in the right-hand side is always nonpositive; so in fact

$$
\int_{\mathcal{Y}} \phi \, d\nu - \int_{\mathcal{X}} \phi^c \, d\mu \le F(\mu).
$$

Then (i) follows upon taking the supremum over  $\phi \in C_b(\mathcal{Y})$  and applying Theorem 5.10 (i).

Now let us consider the equivalence between (i') and (ii'). By assumption,  $\Phi(r) \leq 0$  for  $r \leq 0$ , so  $\Phi^*(t) = \sup_r [rt - \Phi(r)] = +\infty$  if  $t < 0$ . Then the Legendre inversion formula says that

$$
\forall r \in \mathbb{R}, \quad \Phi(r) = \sup_{t \in \mathbb{R}_+} \left[ r \, t - \Phi^*(t) \right].
$$

(The important thing is that the supremum is over  $\mathbb{R}_+$  and not  $\mathbb{R}$ .)

If (i') is satisfied, then for all  $\phi \in C_b(\mathcal{X})$ , for all  $\psi \geq \phi^c$  and for all  $t \in \mathbb{R}_+,$ 

$$
\Lambda \left( t \int_{\mathcal{Y}} \phi \, d\nu - t \psi - \Phi^*(t) \right)
$$

$$
= \sup_{\mu \in P(\mathcal{X})} \left[ \int_{\mathcal{X}} \left( t \int_{\mathcal{Y}} \phi \, d\nu - t \psi - \Phi^*(t) \right) d\mu - F(\mu) \right]
$$

$$
= \sup_{\mu \in P(\mathcal{X})} \left[ t \left( \int_{\mathcal{Y}} \phi \, d\nu - \int_{\mathcal{X}} \psi \, d\mu \right) - \Phi^*(t) - F(\mu) \right]
$$

$$
\leq \sup_{\mu \in P(\mathcal{X})} \left[ t C(\mu, \nu) - \Phi^*(t) - F(\mu) \right]
$$

$$
\leq \sup_{\mu \in P(\mathcal{X})} \left[ \Phi(C(\mu, \nu)) - F(\mu) \right] \leq 0,
$$

where the inequality  $tr \leq \Phi(r) + \Phi^*(t)$  was used.

96 5 Cyclical monotonicity and Kantorovich duality

Conversely, if (ii') is satisfied, then for all  $(\phi, \psi) \in C_b(\mathcal{X}) \times C_b(\mathcal{Y})$ and  $t \geq 0$ ,

$$
t\int_{\mathcal{Y}} \phi \, d\nu - t \int_{\mathcal{X}} \psi \, d\mu - \Phi^*(t) = \int_{\mathcal{X}} \left( \int_{\mathcal{Y}} t\phi \, d\nu - t \, \psi - \Phi^*(t) \right) \, d\mu
$$
  
$$
\leq \Lambda \left( t \int_{\mathcal{Y}} \phi \, d\nu - t \, \psi - \Phi^*(t) \right) + F(\mu);
$$

then by taking the supremum over  $\psi \geq \phi^c$  one obtains

$$
t C(\mu, \nu) - \Phi^*(t) \le \Lambda \left( t \int_{y} \phi \, d\nu - t \phi^c - \Phi^*(t) \right) + F(\mu)
$$

$$
\le F(\mu);
$$

and (i') follows by taking the supremum over  $t \geq 0$ . □

[]

## Application: Solvability of the Monge problem

As a last application of Theorem 5.10, I shall now present the criterion which is used in the large majority of proofs of existence of a deterministic optimal coupling (or Monge transport).

Theorem 5.30 (Criterion for solvability of the Monge prob**lem).** Let  $(X, \mu)$  and  $(Y, \nu)$  be two Polish probability spaces, and let  $a \in L^1(\mu)$ ,  $b \in L^1(\nu)$  be two real-valued upper semicontinuous functions. Let  $c: \mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  be a lower semicontinuous cost function such that  $c(x,y) \ge a(x) + b(y)$  for all x, y. Let  $C(\mu,\nu)$  be the optimal total transport cost between  $\mu$  and  $\nu$ . If

(i)  $C(\mu,\nu) < +\infty;$ 

(ii) for any c-convex function  $\psi : \mathcal{X} \to \mathbb{R} \cup \{+\infty\}$ , the set of  $x \in \mathcal{X}$ such that  $\partial_c \psi(x)$  contains more than one element is  $\mu$ -negligible;

then there is a unique (in law) optimal coupling  $(X, Y)$  of  $(\mu, \nu)$ , and it is deterministic. It is characterized (among all possible couplings) by the existence of a c-convex function  $\psi$  such that, almost surely, Y belongs to  $\partial_c \psi(X)$ . In particular, the Monge problem with initial measure  $\mu$ and final measure  $\nu$  admits a unique solution.

Proof of Theorem 5.30. The argument is almost obvious. By Theorem 5.10(ii), there is a c-convex function  $\psi$ , and a measurable set  $\Gamma \subset \partial_c \psi$  such that any optimal plan  $\pi$  is concentrated on  $\Gamma$ . By assumption there is a Borel set Z such that  $\mu[Z] = 0$  and  $\partial_c \psi(x)$  contains at most one element if  $x \notin Z$ . So for any  $x \in \text{proj}_{\mathcal{X}}(\Gamma) \setminus Z$ , there is exactly one  $y \in \mathcal{Y}$  such that  $(x, y) \in \Gamma$ , and we can then define  $T(x) = y$ .

Let now  $\pi$  be any optimal coupling. Because it has to be concentrated on  $\Gamma$ , and  $Z \times Y$  is  $\pi$ -negligible,  $\pi$  is also concentrated on  $\Gamma \setminus (Z \times Y)$ , which is precisely the set of all pairs  $(x, T(x))$ , i.e. the graph of T. It follows that  $\pi$  is the Monge transport associated with the map T.

The argument above is in fact a bit sloppy, since I did not check the measurability of  $T$ . I shall show below how to slightly modify the construction of  $T$  to make sure that it is measurable. The reader who does not want to bother about measurability issues can skip the rest of the proof.

Let  $(K_{\ell})_{\ell \in \mathbb{N}}$  be a nondecreasing sequence of compact sets, all of them included in  $\Gamma \setminus (Z \times \mathcal{Y})$ , such that  $\pi[\cup K_{\ell}] = 1$ . (The family  $(K_{\ell})$  exists because  $\pi$ , just as any finite Borel measure on a Polish space, is regular.) If  $\ell$  is given, then for any x lying in the compact set  $J_{\ell} := \text{proj}_{\mathcal{X}}(K_{\ell})$ we can define  $T_{\ell}(x)$  as the unique y such that  $(x, y) \in K_{\ell}$ . Then we can define T on  $\cup J_{\ell}$  by stipulating that for each  $\ell$ , T restricts to  $T_{\ell}$ on  $J_{\ell}$ . The map  $T_{\ell}$  is continuous on  $J_{\ell}$ : Indeed, if  $x_m \in T_{\ell}$  and  $x_m \to x$ , then the sequence  $(x_m, T_\ell(x_m))_{m \in \mathbb{N}}$  is valued in the compact set  $K_\ell$ , so up to extraction it converges to some  $(x, y) \in K_{\ell}$ , and necessarily  $y = T_{\ell}(x)$ . So T is a Borel map. Even if it is not defined on the whole of  $\Gamma \setminus (Z \times Y)$ , it is still defined on a set of full  $\mu$ -measure, so the proof can be concluded just as before. can be concluded just as before. ⊓⊔

## Bibliographical notes

There are many ways to state the Kantorovich duality, and even more ways to prove it. There are also several economic interpretations, that belong to folklore. The one which I formulated in this chapter is a variant of one that I learnt from Caffarelli. Related economic interpretations underlie some algorithms, such as the fascinating "auction algorithm" developed by Bertsekas (see [115, Chapter 7], or the various surveys written by Bertsekas on the subject). But also many more classical algorithms are based on the Kantorovich duality [743].

A common convention consists in taking the pair  $(-\psi, \phi)$  as the unknown.<sup>3</sup> This has the advantage of making some formulas more symmetric: The c-transform becomes  $\varphi^c(y) = \inf_x [c(x, y) - \varphi(x)]$ , and then  $\psi^c(x) = \inf_y [c(x, y) - \psi(y)],$  so this is the same formula going back and forth between functions of  $x$  and functions of  $y$ , upon exchange of  $x$ and y. Since  $\mathcal X$  and  $\mathcal Y$  may have nothing in common, in general this symmetry is essentially cosmetic. The conventions used in this chapter lead to a somewhat natural "economic" interpretation, and will also lend themselves better to a time-dependent treatment. Moreover, they also agree with the conventions used in weak KAM theory, and more generally in the theory of dynamical systems [105, 106, 347]. It might be good to make the link more explicit. In weak KAM theory,  $\mathcal{X} = \mathcal{Y}$  is a Riemannian manifold M; a Lagrangian cost function is given on the tangent bundle TM; and  $c = c(x, y)$  is a continuous cost function defined from the dynamics, as the minimum action that one should take to go from x to y (as later in Chapter 7). Since in general  $c(x,x) \neq 0$ , it is not absurd to consider the optimal transport cost  $C(\mu,\mu)$  between a measure  $\mu$  and itself. If M is compact, it is easy to show that there exists a  $\overline{\mu}$  that minimizes  $C(\mu,\mu)$ . To the optimal transport problem between  $\overline{\mu}$  and  $\overline{\mu}$ , Theorem 5.10 associates two distinguished closed c-cyclically monotone sets: a minimal one and a maximal one, respectively  $\varGamma_{\text{min}}$  and  $\varGamma_{\text{max}} \subset M \times M$ . These sets can be identified with subsets of TM via the embedding (initial position, final position)  $\longmapsto$  (initial position, initial velocity). Under that identification,  $\Gamma_{\text{min}}$  and  $\Gamma_{\text{max}}$  are called respectively the Mather and Aubry sets; they carry valuable information about the underlying dynamics. For mnemonic purposes, to recall which is which, the reader might use the resemblance of the name "Mather" to the word "measure". (The Mather set is the one cooked up from the supports of the probability measures.)

In the particular case when  $c(x, y) = |x - y|^2/2$  in Euclidean space, it is customary to expand  $c(x, y)$  as  $|x|^2/2 - x \cdot y + |y|^2/2$ , and change unknowns by including  $|x|^2/2$  and  $|y|^2/2$  into  $\psi$  and  $\phi$  respectively, then change signs and reduce to the cost function  $x \cdot y$ , which is the one

<sup>&</sup>lt;sup>3</sup> The latter pair was denoted  $(\varphi, \psi)$  in [814, Chapter 1], which will probably upset the reader.

appearing naturally in the Legendre duality of convex functions. This is explained carefully in [814, Chapter 2], where reminders and references about the theory of convex functions in  $\mathbb{R}^n$  are also provided.

The Kantorovich duality theorem was proven by Kantorovich himself on a compact space in his famous note [501] (even before he realized the connection with Monge's transportation problem). As Kantorovich noted later in [502], the duality for the cost  $c(x,y) = |x - y|$ in  $\mathbb{R}^n$  implies that transport pathlines are orthogonal to the surfaces  $\{\psi = \text{constant}\}\,$ , where  $\psi$  is the Kantorovich potential, i.e. the solution of the dual problem; in this way he recovered Monge's celebrated original observation.

In 1958, Kantorovich and Rubinstein [506] made the duality more explicit in the special case when  $c(x,y) = d(x,y)$ . Much later the statement was generalized by Dudley [316, Lecture 20] [318, Section 11.8], with an alternative argument (partly based on ideas by Neveu) which does not need completeness (this sometimes is useful to handle complete nonseparable spaces); the proof in the first reference contains a gap which was filled by de Acosta [273, Appendix B].<sup>4</sup> R¨uschendorf [390, 715], Fernique [356], Szulga [769], Kellerer [512], Feyel [357], and probably others, contributed to the problem.

Modern treatments most often use variants of the Hahn–Banach theorem, see for instance [550, 696, 814]. The proof presented in [814, Theorem 1 first proves the duality when  $\mathcal{X}, \mathcal{Y}$  are compact, then treats the general case by an approximation argument; this is somewhat tricky but has the advantage of avoiding the general version of the axiom of choice, since it uses the Hahn–Banach theorem only in the separable space  $C(K)$ , where K is compact.

Mikami [629] recovered the duality theorem in  $\mathbb{R}^n$  using stochastic control, and together with Thieullen [631] extended it to certain classes of stochastic processes.

Ramachandran and Rüschendorf [698, 699] investigated the Kantorovich duality out of the setting of Polish spaces, and found out a necessary and sufficient condition for its validity (the spaces should be "perfect").

In the case when the cost function is a distance, the optimal transport problem coincides with the Kantorovich transshipment problem, for which more duality theorems are available, and a vast literature

<sup>4</sup> De Acosta used an idea suggested by Dudley in Saint-Flour, 25 years before my own course!

has been written; see [696, Chapter 6] for results and references. This topic is closely related to the subject of "Kantorovich norms": see [464], [695, Chapters 5 and 6], [450, Chapter 4], [149] and the many references therein.

Around the mid-eighties, it was understood that the study of the dual problem, and in particular the existence of a maximizer, could lead to precious qualitative information about the solution of the Monge–Kantorovich problem. This point of view was emphasized by many authors such as Knott and Smith [524], Cuesta-Albertos, Matrán and Tuero-Díaz  $[254, 255, 259]$ , Brenier  $[154, 156]$ , Rachev and Rüschendorf [696, 722], Abdellaoui and Heinich [1, 2], Gangbo [395], Gangbo and McCann [398, 399], McCann [616] and probably others. Then Ambrosio and Pratelli proved the existence of a maximizing pair under the conditions (5.10); see [32, Theorem 3.2]. Under adequate assumptions, one can also prove the existence of a maximizer for the dual problem by direct arguments which do not use the original problem (see for instance [814, Chapter 2]).

The classical theory of convexity and its links with the property of cyclical monotonicity are exposed in the well-known treatise by Rockafellar [705]. The more general notions of c-convexity and ccyclical monotonicity were studied by several researchers, in particular Rüschendorf  $[722]$ . Some authors prefer to use c-concavity; I personally advocate working with c-convexity, because signs get better in many situations. However, the conventions used in the present set of notes have the disadvantage that the cost function  $c(\cdot, y)$  is not c-convex. A possibility to remedy this would be to call  $(-c)$ -convexity the notion which I defined. This convention (suggested to me by Trudinger) is appealing, but would have forced me to write  $(-c)$ -convex hundreds of times throughout this book.

The notation  $\partial_c \psi(x)$  and the terminology of c-subdifferential is derived from the usual notation  $\partial \psi(x)$  in convex analysis. Let me stress that in my notation  $\partial_c \psi(x)$  is a set of points, not a set of tangent vectors or differential forms. Some authors prefer to call  $\partial_c \psi(x)$  the **contact** set of  $\psi$  at x (any y in the contact set is a contact point) and to use the notation  $\partial_c \psi(x)$  for a set of tangent vectors (which under suitable assumptions can be identified with the contact set, and which I shall denote by  $-\nabla_x c(x, \partial_c \psi_c(x))$ , or  $\nabla_c^-\psi(x)$ , in Chapters 10 and 12).

In [712] the authors argue that c-convex functions should be constructible in practice when the cost function  $c$  is convex (in the usual sense), in the sense that such c-convex profiles can be "engineered" with a convex tool.

For the proof of Theorem 5.10, I borrowed from McCann [613] the idea of recovering c-cyclical monotonicity from approximation by combinations of Dirac masses; from Rüschendorf [719] the method used to reconstruct  $\psi$  from  $\Gamma$ ; from Schachermayer and Teichmann [738] the clever truncation procedure used in the proof of Part (ii). Apart from that the general scheme of proof is more or less the one which was used by Ambrosio and Pratelli [32], and Ambrosio, Gigli and Savaré [30]. On the whole, the proof avoids the use of the axiom of choice, does not need any painful approximation procedure, and leads to the best known results. In my opinion these advantages do compensate for its being somewhat tricky.

About the proof of the Kantorovich duality, it is interesting to notice that "duality for somebody implies duality for everybody" (a rule which is true in other branches of analysis): In the present case, constructing one particular cyclically monotone transference plan allows one to prove the duality, which leads to the conclusion that all transference plans should be cyclically monotone. By the way, the latter statement could also be proven directly with the help of a bit of measure theoretical abstract nonsense, see e.g. [399, Theorem 2.3] or [1, 2].

The use of the law of large numbers for empirical measures might be natural for a probabilistic audience, but one should not forget that this is a subtle result: For any bounded continuous test function, the usual law of large numbers yields convergence out of a negligible set, but then one has to find a negligible set that works for all bounded continuous test functions. In a compact space X this is easy, because  $C_b(\mathcal{X})$  is separable, but if  $\mathcal X$  is not compact one should be careful. Dudley [318, Theorem 11.4.1] proves the law of large numbers for empirical measures on general separable metric spaces, giving credit to Varadarajan for this theorem. In the dynamical systems community, these results are known as part of the so-called Krylov–Bogoljubov theory, in relation with ergodic theorems; see e.g. Oxtoby [674] for a compact space.

The equivalence between the properties of optimality (of a transference plan) and cyclical monotonicity, for quite general cost functions and probability measures, was a widely open problem until recently; it was explicitly listed as Open Problem 2.25 in [814] for a quadratic cost function in  $\mathbb{R}^n$ . The current state of the art is as follows:

- the equivalence is *false* for a general lower semicontinuous cost function with possibly infinite values, as shown by a clever counterexample of Ambrosio and Pratelli [32];
- the equivalence is true for a *continuous* cost function with possibly infinite values, as shown by Pratelli [688];
- the equivalence is true for a *real-valued* lower semicontinuous cost function, as shown by Schachermayer and Teichmann [738]; this is the result that I chose to present in this course. Actually it is sufficient for the cost to be lower semicontinuous and real-valued almost everywhere (with respect to the product measure  $\mu \otimes \nu$ ).
- more generally, the equivalence is true as soon as  $c$  is measurable (not even lower semicontinuous!) and  $\{c = \infty\}$  is the union of a closed set and a  $(\mu \otimes \nu)$ -negligible Borel set; this result is due to Beiglböck, Goldstern, Maresch and Schachermayer [79].

Schachermayer and Teichmann gave a nice interpretation of the Ambrosio–Pratelli counterexample and suggested that the correct notion in the whole business is not cyclical monotonicity, but a variant which they named "strong cyclical monotonicity condition" [738].

As I am completing these notes, it seems that the final resolution of this equivalence issue might soon be available, but at the price of a journey into the very guts of measure theory. The following construction was explained to me by Bianchini. Let  $c$  be an arbitrary lower semicontinuous cost function with possibly infinite values, and let  $\pi$  be a c-cyclically monotone plan. Let  $\Gamma$  be a c-cyclically monotone set with  $\pi[\Gamma] = 1$ . Define an equivalence relation R on  $\Gamma$  as follows:  $(x, y) \sim (x', y')$  if there is a finite number of pairs  $(x_k, y_k)$ ,  $0 \le k \le N$ , such that:  $(x, y) = (x_0, y_0)$ ; either  $c(x_0, y_1) < +\infty$  or  $c(x_1, y_0) < +\infty$ ;  $(x_1,y_1) \in \Gamma$ ; either  $c(x_1,y_2) < +\infty$  or  $c(x_2,y_1) < +\infty$ ; etc. until  $(x_N, y_N) = (x', y')$ . The relation R divides  $\Gamma$  into equivalence classes  $(\Gamma_{\alpha})_{\alpha \in \Gamma / \mathcal{R}}$ . Let p be the map which to a point x associates its equivalence class  $\bar{x}$ . The set  $\Gamma/R$  in general has no topological or measurable structure, but we can equip it with the largest  $\sigma$ -algebra making p measurable. On  $\Gamma \times (\Gamma / \mathcal{R})$  introduce the product  $\sigma$ -algebra. If now the *graph* of p is measurable for this  $\sigma$ -algebra, then  $\pi$  should be optimal in the Monge–Kantorovich problem.

Related to the above discussion is the "transitive c-monotonicity" considered by Beiglböck, Goldstern, Maresch and Schachermayer [79], who also study in depth the links of this notion with c-monotonicity, optimality, strong c-monotonicity in the sense of [738], and a new interesting concept of "robust optimality". The results in [79] unify those of [688] and [738]. A striking theorem is that robust optimality is always equivalent to strong c-monotonicity.

An alternative approach to optimality criteria, based on extensions of the classical saddle-point theory, was developed by Léonard [550].

In most applications, the cost function is continuous, and often rather simple. However, it is sometimes useful to consider cost functions that achieve the value  $+\infty$ , as in the "secondary variational problem" considered by Ambrosio and Pratelli [32] and also by Bernard and Buffoni [104]. Such is also the case for the optimal transport in Wiener space considered by Feyel and Ustünel  $[358, 359, 360, 361]$ , for which the cost function  $c(x, y)$  is the square norm of  $x - y$  in the Cameron– Martin space (so it is  $+\infty$  if  $x - y$  does not belong to that space). In this setting, optimizers in the dual problem can be constructed via finite-dimensional approximations, but it is not known whether there is a more direct construction by c-monotonicity.

If one uses the cost function  $|x-y|^p$  in  $\mathbb{R}^n$  and lets  $p \to \infty$ , then the c-cyclical monotonicity condition becomes sup  $|x_i - y_i| \leq \sup |x_i - y_{i+1}|$ . Much remains of the analysis of the Kantorovich duality, but there are also noticeable changes [222].

When condition (5.9) (or its weakened version (5.10)) is relaxed, it is not clear in general that the dual Kantorovich problem admits a maximizing pair. Yet this is true for instance in the case of optimal transport in Wiener space; this is an indication that condition (5.10) might not be the "correct" one, although at present no better general condition is known.

Lemma 5.18 and Theorem 5.19 were inspired by a recent work of Fathi and Figalli [348], in which a restriction procedure is used to solve the Monge problem for certain cost functions arising from Lagrangian dynamics in unbounded space; see Theorem 10.28 for more information.

The core argument in the proof of Theorem 5.20 has probably been discovered several times; I learnt it in [30, Proposition 7.13]. At the present level of generality this statement is due to Schachermayer and Teichmann [738].

I learnt Corollary 5.23 from Ambrosio and Pratelli [32], for measures on Euclidean space. The general statement presented here, and its simple proof, were suggested to me by Schulte, together with the counterexample in Remark 5.25. In some situations where more structure is available and optimal transport is smooth, one can refine the convergence in probability into true pointwise convergence [822]. Even when that information is not available, under some circumstances one can obtain the uniform convergence of  $c(x,T_k(x))$ : Theorem 28.9(v) is an illustration (this principle also appears in the tedious proof of Theorem 23.14).

Measurable selection theorems in the style of Corollary 5.22 go back at least to Berbee [98]. Recently, Fontbona, Guérin and Méléard [374] studied the measurability of the map which to  $(\mu, \nu)$  associates the union of the supports of all optimal transports between  $\mu$  and  $\nu$  (measurability in the sense of set-valued functions). This question was motivated by applications in particle systems and coupling of stochastic differential equations.

Theorem 5.26 appears in a more or less explicit form in various works, especially for the particular case described in Example 5.29; see in particular [128, Section 3]. About Legendre duality for convex functions in R, one may consult [44, Chapter 14]. The classical reference textbook about Legendre duality for plainly convex functions in  $\mathbb{R}^n$ is [705]. An excellent introduction to the Legendre duality in Banach spaces can be found in [172, Section I.3].

Finally, I shall say a few words about some basic measure-theoretical tools used in this chapter.

The regularity of Borel measures on Polish spaces is proven in [318, p. 225].

Measurable selection theorems provide conditions under which one may select elements satisfying certain conditions, in a measurable way. The theorem which I used to prove Corollary 5.22 is one of the most classical results of this kind: A surjective Borel map f between Polish spaces, such that the fibers  $f^{-1}(y)$  are all compact, admits a Borel right-inverse. See Dellacherie [288] for a modern proof. There are more advanced selection theorems due to Aumann, Castaing, Kuratowski, Michael, Novikov, Ryll-Nardzewski, Sainte-Beuve, von Neumann, and others, whose precise statements are remarkably opaque for nonexperts; a simplified and readable account can be found in [18, Chapter 18].

Lusin's theorem [352, Theorem 2.3.5], which was used in the proof of Corollary 5.23, states the following: If  $\mathcal X$  is a locally compact metric space,  $\mathcal Y$  is a separable metric space,  $\mu$  is a Borel measure on  $\mathcal X$ , f is a measurable map  $\mathcal{X} \to \mathcal{Y}$ , and  $A \subset \mathcal{X}$  is a measurable set with finite measure, then for each  $\delta > 0$  there is a closed set  $K \subset A$  such that  $\mu[A \setminus K] < \delta$  and the restriction of f to K is continuous.
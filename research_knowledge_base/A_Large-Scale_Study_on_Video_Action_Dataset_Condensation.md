# <span id="page-0-1"></span>A Large-Scale Study on Video Action Dataset Condensation

<PERSON><sup>1</sup> <PERSON><PERSON><sup>2</sup> <PERSON><sup>2</sup> <PERSON><PERSON><sup>1, 3,  $\boxtimes$ </sup> <sup>1</sup>State Key Laboratory for Novel Software Technology, Nanjing University  $2^2$ Ant Group  $3$  Shanghai AI Lab

## Abstract

*Recently, dataset condensation has made significant progress in the image domain. Unlike images, videos possess an additional temporal dimension, which harbors considerable redundant information, making condensation even more crucial. However, video dataset condensation still remains an underexplored area. We aim to bridge this gap by providing a large-scale study with systematic design and fair comparison. Specifically, our work delves into three key aspects to provide valuable empirical insights: (1) temporal processing of video data, (2) the evaluation protocol for video dataset condensation, and (3) adaptation of condensation algorithms to the space-time domain. From this study, we derive several intriguing observations: (i) labeling methods greatly influence condensation performance, (ii) simple sliding-window sampling is effective for temporal processing, and (iii) dataset distillation methods perform better in challenging scenarios, while sample selection methods excel in easier ones. Furthermore, we propose a unified evaluation protocol for the fair comparison of different condensation algorithms and achieve state-of-theart results on four widely-used action recognition datasets: HMDB51, UCF101, SSv2 and K400. Our code is available at* <https://github.com/MCG-NJU/Video-DC>*.*

## 1. Introduction

Dataset condensation, crucial for data-efficient learning, focuses on reducing the size of an original dataset while maintaining the performance of models trained on the condensed version [\[44\]](#page-11-0). This task addresses challenges associated with training neural networks on large datasets, including high computational costs and storage requirements. Dataset condensation has numerous applications, such as neural architecture search [\[34\]](#page-11-1), privacy protection [\[24\]](#page-10-0), and more.

Previous research on dataset condensation [\[3,](#page-10-1) [7,](#page-10-2) [8,](#page-10-3) [13,](#page-10-4) [18,](#page-10-5) [21,](#page-10-6) [23,](#page-10-7) [27,](#page-11-2) [28,](#page-11-3) [31,](#page-11-4) [35,](#page-11-5) [40,](#page-11-6) [44,](#page-11-0) [51](#page-11-7)[–53,](#page-11-8) [55\]](#page-11-9) has primarily focused on image datasets. Some studies have explored ex-

<span id="page-0-0"></span>Image /page/0/Figure/8 description: This is a flowchart illustrating a two-stage process: (1) Condensation and (2) Evaluation. In the Condensation stage, a 'Synthetic Video' (represented as a sequence of frames with a sliding window) is processed by an 'Interpolator'. This output, along with a 'Real Video' (also a sequence of frames), is fed into a 'Condensation Network'. The network's output is then used in a 'Sample Selection' process, which, based on a condition 'G > 0', leads to 'Dataset Distillation'. The 'Condensation Network' also receives a backward loss signal. In the Evaluation stage, a 'Synthetic Video' is processed by an 'Interpolator' and 'Labeling' module, both feeding into an 'Evaluation Network'. The output of the 'Evaluation Network' is then 'Test on Val Set'. The diagram also includes a legend indicating that a dashed red box represents a 'Sliding-window', a dashed red arrow represents 'Loss Backward', 'G' signifies 'Require Grads', and a network diagram represents the 'Condensation Network'.

Figure 1. Pipeline of our study. Our study includes three core elements: temporal processing, condensation algorithms and evaluation settings. Temporal processing is the special design for video data, including sampling and interpolation. Real videos comes across a condensation algorithm (categorized into sample selection and dataset distillation, with G indicating the chosen approach) to form the synthetic dataset (left). Then, the synthetic videos combined with the labeling methods train an evaluation network to test on val set as the evaluation metric (right). Both of the phases rely on a neural network.

tending condensation algorithms to other modalities, such as graph  $[47, 50]$  $[47, 50]$  $[47, 50]$  and text data  $[25, 46]$  $[25, 46]$  $[25, 46]$ . As a natural extension of images, video serves as a crucial medium for conveying visual information. The additional data in videos increases storage and computational requirements, while also introducing redundant scene and texture information. These factors highlight the increased need for, and the significant potential of video dataset condensation.

Wang *et al*. [\[45\]](#page-11-13) first explore the possibility of applying dataset condensation to video data, parameterizing segmented matching with synthetic frames, real frames, segments and interpolation algorithms. They proposed a twostage framework that disentangle static and dynamic information. However, they did not investigate the evaluation settings and primarily conducted experiments on the smallscale HMDB51 and MiniUCF. The performance of video dataset condensation remains limited.

In response to the growing significance of video dataset

 $\boxtimes$ : Corresponding author (<EMAIL>).

<span id="page-1-0"></span>condensation, we conduct a large-scale empirical study, which involves various temporal processing techniques, diverse condensation algorithms, complex evaluation settings and four action recognition datasets. Based on these, we provide valuable insights and achieve sota results.

Figure [1](#page-0-0) clearly illustrates the pipeline of our study, which is divided into two main phases: condensation and evaluation. The real dataset is first condensed into a synthetic one by sample selection or dataset distillation methods. Then, the synthetic dataset trains evaluation networks, whose performance on the real validation set serves as the metric to evaluate the condensation algorithms.

Three core elements of the study are also depicted in Fig. [1:](#page-0-0) temporal processing, condensation methods and evaluation settings. For temporal processing, we employ a sampling and interpolation pipeline both on condensation and evaluation phases. For condensation algorithms, we adapt RDED  $[35]$ , EDC  $[31]$ , DATM  $[13]$  to video data, which have demonstrated their effectiveness on image datasets. Representing sample selection methods, RDED directly select samples from real data without optimization. While, the latter two methods, belonging to dataset distillation, synthesize data by optimizing proxy objectives: matching distributions or training trajectories. For evaluation, we utilize action recognition as the downstream task. In addition, we explore various evaluation settings, including labeling methods, data augmentation and loss functions. Based on this, we propose a unified evaluation protocol to enable fair comparison of condensation algorithms.

According to the experiments, we draw intriguing observations: (1) Labeling methods greatly influence final results. Similar conclusions are also drawn in image datasets [\[29\]](#page-11-14). (2) Simple sliding-window sampling is effective for temporal processing. Compared with previous segment sampling [\[45\]](#page-11-13), our method better guarantee temporal coherence of synthetic videos. (3) Dataset distillation methods outperform sample selection methods on challenging scenarios, while sample selection methods excel in easier ones. This shows great potential of dataset distillation methods to tackle training efficiency problems, which fits the original intention of dataset condensation.

In summary, our key contributions are as follows: (1) the establishment of an evaluation protocol for video dataset condensation, (2) the development of temporal processing methods, especially the proposed sliding-window sampling, (3) comprehensive experimental results and in-depth analyses of condensation algorithms, and (4) state-of-theart performance on widely-used video action datasets: HMDB51 [\[19\]](#page-10-9), UCF101 [\[33\]](#page-11-15), SSv2 [\[11\]](#page-10-10) and K400 [\[2\]](#page-10-11).

## 2. Related Work

Image Dataset Condensation. Dataset condensation algorithms can be broadly categorized into sample selection

and dataset distillation. Sample selection is further divided into optimization-based and score-based approaches. Optimization-based selection aims to identify a small coreset that effectively captures the diverse characteristics of the full dataset, with methods such as Herding [\[5\]](#page-10-12), Kcenter [\[30\]](#page-11-16), Craig [\[26\]](#page-11-17), and GradMatch [\[16\]](#page-10-13). By contrast, score-based selection assigns heuristic measures to instances, like difficulty or the impact on network training. Traditional approaches include Forgetting [\[36\]](#page-11-18), Glister [\[17\]](#page-10-14), and C-Score [\[14\]](#page-10-15). DeepCore [\[12\]](#page-10-16) has shown that random selection remains a strong baseline among the above methods. More recently, RDED [\[35\]](#page-11-5) has achieved remarkable results on various image datasets. It selects samples based on realism and diversity scores, and then concatenates them to form the condensed dataset.

Dataset distillation methods can be categorized into performance matching, distribution matching, and trajectory matching. Due to the inherent bi-level optimization, performance matching methods face scalability challenges [\[23,](#page-10-7) [27,](#page-11-2) [28,](#page-11-3) [44,](#page-11-0) [55\]](#page-11-9), making them less practical for large-scale scenarios. Thus, our focus is primarily on the latter two kinds of methods. Trajectory matching optimizes condensed data by aligning the parameter updates of a network trained on real data and synthetic data. Methods such as DC [\[53\]](#page-11-8), DSA [\[51\]](#page-11-7), MTT [\[3\]](#page-10-1), TESLA [\[7\]](#page-10-2), and DATM [\[13\]](#page-10-4) have progressively refined this approach, achieving lossless results on small datasets. Recent concurrent works continue to advance this framework [\[41,](#page-11-19) [54\]](#page-11-20). Distribution matching, on the other hand, aligns real and synthetic data across different feature spaces [\[40,](#page-11-6) [52\]](#page-11-21). SRe2L [\[48\]](#page-11-22) introduced statistical matching to reduce computational and storage costs, and subsequent works such as G-VBSM [\[32\]](#page-11-23) and EDC [\[31\]](#page-11-4) further enhanced this approach, leading to improved performance especially on large datasets.

Condensation on Other Modalities. Many studies have extended dataset condensation algorithms to other modalities. Graph data, which captures relationships between nodes, has been explored extensively [\[15,](#page-10-17) [47,](#page-11-10) [50\]](#page-11-11). Similarly, text and time series data also draw great interest [\[9,](#page-10-18) [22,](#page-10-19) [25,](#page-10-8) [46\]](#page-11-12). More recently, as a crucial medium for conveying visual information, video is first explored by Wang *et al*. [\[45\]](#page-11-13). Building upon this foundation, our work provides insightful results to foster further exploration.

Action Recognition. The development of video backbones for action recognition has progressed significantly to better capture spatial and temporal features. C3D [\[38\]](#page-11-24) introduced 3D convolutions to process both aspects simultaneously but was computationally heavy. I3D [\[2\]](#page-10-11) improved efficiency by inflating 2D convolutional filters pre-trained on image data into 3D, benefiting from transfer learning. R(2+1)D [\[39\]](#page-11-25) refined this by separating 3D convolutions into 2D spatial and 1D temporal components, enhancing flexibility and reducing parameters. The SlowFast [\[10\]](#page-10-20) network further ad-

<span id="page-2-2"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: The image displays three different sampling methods for synthetic videos: Naïve Sampling, Segment Sampling, and Sliding-window Sampling. Each method starts with a synthetic video represented by a series of blue blocks. In Naïve Sampling, all blocks are selected. In Segment Sampling, the video is divided into segments, and blocks from these segments are selected. Sliding-window Sampling involves a window that moves across the video, selecting blocks within the window. Following the sampling, each method involves an 'Interpolation' step, represented by a purple rectangle. Finally, each method results in an 'Input Clip', depicted as a series of green blocks.

Figure 2. Temporal processing encompasses sampling and interpolation. (a) Naive sampling directly treats the video as the sampled clip. (b) Segment sampling views videos as independent clips. (c) Sliding-window sampling sequentially samples clips along time. Different interpolation methods can then be applied to generate the final input clips.

vanced the field with a dual-pathway design that processes video at different frame rates, capturing both detailed spatial information and fast motion changes. Some transformerbased backbone [\[1,](#page-10-21) [37,](#page-11-26) [42\]](#page-11-27) have also demonstrated great effectiveness. In this work, we use these video backbones for video dataset condensation.

## 3. Method

This work investigates recent dataset condensation algorithms for condensing video datasets. For these methods were initially designed for image datasets, we extend them to the space-time domain, examining the implementation details and comparing their performance under consistent conditions to evaluate their efficacy in dataset condensation. Our study primarily focuses on one sample selection method: RDED [\[35\]](#page-11-5), and two dataset distillation methods: DATM [\[13\]](#page-10-4) and EDC [\[31\]](#page-11-4).

As in Fig. [1,](#page-0-0) the process of the dataset condensation can be divided into two main phases. First, the original dataset is condensed into a smaller one using either distillation or selection methods. Next, the condensed dataset is applied to a downstream task to assess the effectiveness of the condensation algorithms. Adapting condensation algorithms from images to videos, we propose sliding-window sampling and interpolation for temporal processing. In this section, we will discuss about the temporal processing and then introduce two phases of dataset condensation separately.

<span id="page-2-1"></span>

### 3.1. Temporal Processing

From images to videos, the processing of temporal dimension is the most crucial topic. As shown in Fig. [2,](#page-2-0) temporal processing incorporates sampling and interpolation, getting input clips from synthetic videos.

For sampling, the most naive method is viewing the whole video as the sampled clip (Fig. [2a](#page-2-0)). It can be re-

garded as the baseline of the sampling method. Wang *et al*. [\[45\]](#page-11-13) proposed segment sampling (Fig. [2b](#page-2-0)), which splits the synthetic videos as independent segments. This method disrupts the temporal consistency of videos. To overcome this problem, we propose the sliding-window sampling. It sequentially samples video clips along the temporal dimension as shown in Fig. [2c](#page-2-0). Thus, the co-optimization of overlapping clips ensures the consistency and coherence of the synthetic videos.

Additionally, the interpolation methods encompass none, duplication, linear interpolation and even learnable interpolation. It aligns the length of the sampled clips with the input clips and better condenses the temporal information of synthetic videos.

The two processes help condense the temporal information of videos, which is essential for video dataset condensation. Let N and  $T_m$  denote the number of videos and the average number of frames in the real dataset, respectively, and  $N_c$  and  $T_c$  represent those values for the condensed dataset. Thus,  $\frac{N_c}{N}$  represents the instance compression ratio,  $\frac{T_c}{T_m}$  represents the temporal compression ratio, and the total condensation ratio is given by  $\frac{N_c \times T_c}{N \times T_m}$ . We will further analyze the trade-off between  $N_c$  and  $T_c$  in Sec. [4.2.](#page-5-0)

#### 3.2. Condensation Algorithms

Given a large, real\_dataset  $\mathcal{D}^{\mathcal{T}} := \{ \mathbf{x}_i^{\mathcal{T}}, \mathbf{y}_i^{\mathcal{T}} \}_{i=1}^{|\mathcal{D}^{\mathcal{T}}|}$  consisting of images  $X^{\mathcal{T}}$  and labels  $Y^{\mathcal{T}}$ , dataset condensation aims to synthesize a smaller dataset  $\mathcal{D}^S = \{ \mathbf{x}_i^S, \mathbf{y}_i^S \}_{i=1}^{|\mathcal{D}^S|}$ , which includes images  $\mathcal{X}^{\mathcal{S}}$  and labels  $\mathcal{Y}^{\mathcal{S}}$ , such that a model trained on  $\mathcal{D}^{\mathcal{S}}$  exhibits comparable generalization performance to one trained on  $\mathcal{D}^{\mathcal{T}}$ . The objective of dataset condensation can be formally expressed as:

$$
\mathcal{D}^{\mathcal{S}} = \underset{\mathcal{D}^{\mathcal{S}}}{\arg\min} \mathbb{E}_{(\mathbf{x}^{\mathcal{T}}, \mathbf{y}^{\mathcal{T}}) \sim \mathcal{D}^{\mathcal{T}}} [\ell(\phi_{\theta^*}(\mathbf{x}^{\mathcal{T}}), \mathbf{y}^{\mathcal{T}})]
$$
  
s.t.  $\theta^* = \underset{\theta}{\arg\min} \mathbb{E}_{(\mathbf{x}^{\mathcal{S}}, \mathbf{y}^{\mathcal{S}}) \sim \mathcal{D}^{\mathcal{S}}} [\ell(\phi_{\theta}(\mathbf{x}^{\mathcal{S}}), \mathbf{y}^{\mathcal{S}})],$  (1)

where  $\ell$  denotes the loss function for the specific task,  $\phi_{\theta}$ represents the model, and  $\theta^*$  denotes the optimal parameters after training on  $\mathcal{D}^{\mathcal{S}}$ . This objective is challenging to optimize directly. Consequently, previous works introduce proxy tasks to optimize condensed datasets. Specifically, dataset distillation methods use the proxy loss  $\mathcal L$  to describe differences between real and synthetic data, while sample selection methods typically design heuristic objectives for optimization. We then present the proxy tasks of various condensation algorithms.

DATM [\[13\]](#page-10-4) (Fig. [3a](#page-3-0)) is a representative of matching training trajectories. The proxy task is to match the training trajectories of condensation models trained on  $\mathcal{D}^{\mathcal{T}}$  and  $\mathcal{D}^{\mathcal{S}}$ . Specifically, let  $\{\theta_t^R\}_{0}^n$  denote the training trajectory obtained by  $\mathcal{D}^{\mathcal{T}}$ , where  $\theta_t^R$  is the parameters of the conden-

<span id="page-3-2"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: The figure illustrates a method for condensing datasets, featuring three main components: (a) Trajectory Matching, (b) Distribution Matching, and (c) Score Selection. The overall process begins with a 'Condensed Dataset' and a 'Real Dataset' feeding into a 'Condensation Module'. This module's output can be either a 'Proxy Loss' or a 'Selection Index', which then updates the process. Component (a) shows a trajectory matching process involving model parameters (θ) at different time steps (t-1, t, t+1) for both real (R) and synthetic (S) data, calculating a 'Loss' through matching. Component (b) details a multi-stage distribution matching process (Stage I, Stage II, Stage III), where matching occurs at each stage, culminating in a 'CE Loss + Matching Loss'. Component (c) describes score selection, where 'N' scores are selected by an 'Observer' to produce 'IPC' scores, multiplied by 'C'. A legend indicates that 'θ0' represents 'Model Param', pink rectangles represent 'Video Clip', and green rectangles represent 'Model Layer'.

Figure 3. Conceptual visualization of three dataset condensation frameworks applied to video. Trajectory Matching (a) and Distribution Matching (b) in the blue boxes belong to dataset distillation methods, and Score Selection (c) in the green box belongs to sample selection methods. Dataset distillation defines a proxy task to help condense the dataset and uses the output proxy loss (left) to update the condensed dataset, while sample selection directly draws samples from the real dataset to form the condensed one. Trajectory Matching (a) uses a normed L2 loss to describe distance between parameters, Distribution Matching (b) matches the distribution between model layers, and Score Selection (c) scores each sample from the real dataset and concatenates them to form the condensed dataset.

sation network at training step t. Similarly,  $\theta_t^S$  denotes the parameters trained on  $\mathcal{D}^{\mathcal{S}}$  at time step t.

In each iteration of the distillation, sampling time step t from 0 to n, the proxy loss is:

<span id="page-3-1"></span>
$$
\mathcal{L} = \frac{\|\theta_{t+N}^{S} - \theta_{t+M}^{R}\|_{2}^{2}}{\|\theta_{t}^{R} - \theta_{t+M}^{R}\|_{2}^{2}},
$$
\n(2)

where M and N is the hyper-parameter of optimization step on  $\mathcal{D}^{\mathcal{T}}$  and  $\mathcal{D}^{\mathcal{S}}$  for the condensation network.

Specifically, to better align the difficulty of distillation as the process progresses, DATM gradually increases the time steps of the initial parameters, ensuring smoother and more effective optimization.

EDC [\[31\]](#page-11-4) (Fig. [3b](#page-3-0)) follows SRe2L [\[48\]](#page-11-22) to generalize distribution matching to statistical matching. The proxy task is to matching the statistics of original and condensed datasets.

$$
\mathcal{L} = ||p(\mu|\mathcal{X}^{\mathcal{S}}) - p(\mu|\mathcal{X}^{\mathcal{T}})||_2
$$
  
+ 
$$
||p(\sigma^2|\mathcal{X}^{\mathcal{S}}) - p(\sigma^2|\mathcal{X}^{\mathcal{T}})||_2, \text{ s.t. } \mathcal{L} \sim \mathbb{S}_{\text{match}},
$$
 (3)

where  $\mathbb{S}_{match}$  denotes the extensive collection of statistical matching operators, which operate across a variety of network architectures and layers as described by G-VBSM [\[32\]](#page-11-23) and EDC [\[31\]](#page-11-4). Here,  $\mu$  and  $\sigma^2$  are defined as the mean and variance, respectively.

RDED [\[35\]](#page-11-5) (Fig. [3c](#page-3-0)) emphasizes the realism and diversity of the condensed dataset. It selects the most realistic instances from a set of samples with a greedy algorithm and concatenates them to form the condensed dataset. These two designs ensure the realism and diversity respectively.

$$
\{\mathbf{x}_{k}^{i}\}_{k=1}^{N} = \underset{\mathbf{x}_{k}}{\arg \max} [\sum_{k=1}^{N} \mathcal{F}(\mathbf{x}_{k})], \quad \mathbf{x}_{k} \in \mathcal{X}_{i}^{\mathcal{T}},
$$

$$
\mathcal{X}^{\mathcal{S}} = \bigcup_{i=1}^{C} \mathcal{X}_{i}^{\mathcal{S}}, \quad \mathcal{X}_{i}^{\mathcal{S}} = \{\mathbf{x}_{j}^{i} = \text{concat}(\{\mathbf{x}_{k}^{i}\}_{k=1}^{N})\}_{j=1}^{\text{IPC}} \hfill (4)
$$

where  $F$  is the score function measuring the realism of instances, C denotes the number of classes and concat( $\cdot$ ) concatenates N samples together.

Previous studies [\[12\]](#page-10-16) show that many carefully designed sample selection methods do not have obvious advantages over random selection. However, RDED achieves remarkable results with this simple design, and the training-free method also guarantees high efficiency.

Implementation Specifics. We draw an analogy between clips in videos and cropped images. For dataset distillation methods, we update the batch of sampled clips in each iteration. We extend the spatial statistics in EDC to the spacetime domain. By contrast, for concatenation in RDED, we only concatenate video tubes along the spatial dimension.

<span id="page-4-2"></span><span id="page-4-0"></span>

| Dataset | #videos | $n_{mean}$ | $T_{mean}$ | $T_{median}$ |
|---------|---------|------------|------------|--------------|
| HMDB51  | 3,570   | 70         | 97         | 79           |
| UCF101  | 9,537   | 94         | 187        | 168          |
| SSv2    | 168,913 | 970        | 45         | 44           |
| K400    | 240,436 | 601        | 286        | 300          |

Table 1. Statistics of action recognition video datasets.  $n_{mean}$ represents the average number of videos per class.  $T_{mean}$  and  $T_{median}$  are the mean and median of frames per video.

<span id="page-4-1"></span>Image /page/4/Figure/2 description: This is a line graph titled "Dataset Condensation on UCF101". The x-axis is labeled "Instance Per Class (IPC)" and ranges from 1 to 20. The y-axis is labeled "Accuracy(%)" and ranges from 0 to 35. There are four lines plotted: EDC (blue circles, solid line), DATM (green squares, solid line), RDED (orange diamonds, solid line), and a dashed line representing "Hard label" which appears to be associated with EDC and RDED. At 1 IPC, EDC has ~12% accuracy, DATM has ~16% accuracy, RDED has ~9% accuracy, and the hard label line has ~7% accuracy. At 5 IPC, EDC has ~22% accuracy, DATM has ~27% accuracy, RDED has ~29% accuracy, and the hard label line has ~18% accuracy. At 10 IPC, EDC has ~23% accuracy, DATM has ~32% accuracy, RDED has ~34% accuracy, and the hard label line has ~19% accuracy. At 20 IPC, EDC has ~28% accuracy, DATM has ~36% accuracy, and RDED has ~38% accuracy. The hard label line at 20 IPC has ~28% accuracy.

Figure 4. Performance curve of various condensation algorithms with different labeling methods on UCF101, illustrating the comparison among condensation algorithms and highlighting the significant impact of labeling methods.

More details can be found in Appendix [A.](#page-8-0)

#### 3.3. Evaluation Protocol

This work primarily focuses on video dataset condensation for action recognition. The evaluation process involves training a new neural network specifically for action recognition, making it more complex than other tasks. Consequently, different training settings significantly impact the final metric. Thus, we conduct an experimental analysis of the factors that have the most significant impact on the results, including labeling methods, data augmentation, and loss functions. According to the results, we propose a unified evaluation protocol for fair comparison of different condensation algorithms.

Labeling methods include hard labeling, soft labeling and multi soft labeling (Multi-SL). Hard labeling [\[44\]](#page-11-0) assigns each video a one-hot encoded label that remains unchanged during the dataset condensation process. This method is the most straightforward and intuitive, serving as the fundamental method. Soft labeling [\[7,](#page-10-2) [13\]](#page-10-4) utilizes logits as labels to enhance performance. The logits can be be obtained from a pre-trained teacher model or defined as learnable vectors optimized alongside the condensed videos. Finally, Multi-SL [\[31,](#page-11-4) [32,](#page-11-23) [48\]](#page-11-22) adopts the philosophy of knowledge distillation. For the same video, multiple inputs are generated through sampling and data augmentation, each as-

signed different logits as labels. By doing so, it captures diverse aspects of the video under various augmented views, enhancing the model's generalization ability.

Additionally, data augmentation and loss functions significantly impact the results. We conduct ablation studies on them with different condensation and labeling methods to broadly demonstrate that: (1) comparisons between condensation algorithms are consistent across different evaluation settings, (2) evaluation settings substantially affect the final metrics, and (3) employing different evaluation settings when comparing condensation algorithms may lead to incorrect conclusions. Therefore, we propose a unified evaluation protocol based on our experimental results. Details of the evaluation protocol can be found in Appendix [B.](#page-8-1)

## 4. Experiment

Our work builds upon numerous existing methods for image dataset condensation, so we retain their original names for simplicity. In this section, unless otherwise specified, all methods refer to their video-version implementations.

Datasets. We conduct experiments on HMDB51 [\[19\]](#page-10-9), UCF101 [\[33\]](#page-11-15), SSv2 [\[11\]](#page-10-10) and K400 [\[2\]](#page-10-11). Table [1](#page-4-0) shows the statistics of the action recognition datasets. HMDB51 consists of 51 action categories, with 70 videos per category. UCF101 contains 101 categories, with an average of 94 videos per category. SSv2 consists of 174 categories, with an average of up to 970 videos per category. K400 consists of 400 categories, with an average of 600 videos per category. With a suitable scale on video duration and number, UCF101 is the main dataset for ablation. Given the current progress in video dataset distillation, SSv2 and K400 are sufficient for validating the scalability of dataset condensation algorithms. Additionally, SSv2 emphasizes temporal dynamics and motion patterns, making it particularly useful for evaluating the condensation of motion information.

Experiment Details. For training, we mainly use a fourlayer MiniC3D following the implementation in [\[45\]](#page-11-13). Other model architectures are explored in Tab. [7.](#page-6-0) For evaluation, we report the main results using MiniC3D, and crossarchitecture generalization results with R(2+1)D, I3D and SlowOnly. The latter three models use the ResNet-18 backbone. For HMDB51 and UCF101, we crop and resize the frame to  $112 \times 112$ . For SSv2 and K400, the frames are cropped and resized to  $56 \times 56$ , considering memory and computation consumption. The default data augmentation we use is resized crop and horizontal flip.

### 4.1. Effect of Evaluation Settings

In this section, we analyze key aspects of the evaluation process, including labeling methods, data augmentation, and loss functions. Our experiments highlight the importance of maintaining a consistent evaluation setting, leading to the proposal of a unified evaluation protocol.

<span id="page-5-5"></span><span id="page-5-1"></span>

| CutMix | Hard Label |     | Soft Label |      | Multi-SL |      |
|--------|------------|-----|------------|------|----------|------|
|        | w/         | w/o | w/         | w/o  | w/       | w/o  |
| EDC†   | 4.5        | 4.6 | 3.0        | 6.0  | 5.7      | 7.0  |
| DATM   | 6.7        | 6.8 | 9.7        | 12.1 | 14.9     | 15.4 |
| RDED   | 10.2       | 9.7 | 11.0       | 14.3 | 16.1     | 17.7 |

Table 2. Ablation results of CutMix augmentation on UCF101. Not using CutMix consistently improve the accuracy with only one exception (RDED hard label). EDC<sup>†</sup> represents a simple version of EDC without category-wise matching.

<span id="page-5-2"></span>

| Loss            | Soft Label                                          |                                                     | Multi-SL                                            |                                                     |
|-----------------|-----------------------------------------------------|-----------------------------------------------------|-----------------------------------------------------|-----------------------------------------------------|
|                 | KL                                                  | MSE-GT                                              | KL                                                  | MSE-GT                                              |
| $EDC^{\dagger}$ | <span style="text-decoration:underline">4.8</span>  | <span style="text-decoration:underline">6.0</span>  | 5.5                                                 | <span style="text-decoration:underline">7.9</span>  |
| <b>DATM</b>     | 11.3                                                | <span style="text-decoration:underline">13.7</span> | 14.7                                                | <span style="text-decoration:underline">15.4</span> |
| <b>RDED</b>     | <span style="text-decoration:underline">15.0</span> | 14.3                                                | <span style="text-decoration:underline">18.2</span> | 17.7                                                |

Table 3. Ablation results of loss function on UCF101. MSE-GT loss generally outperforms KL loss, except RDED. For not affecting the comparison between method, we choose MSE-GT loss for simplification.  $EDC^{\dagger}$  is a simple version of EDC without categorywise matching.

Labeling Methods. Figure [4](#page-4-1) shows the performance curve of various condensation algorithms with different labeling methods. It can be vividly seen that labeling methods influence performance a lot, even greater than condensation algorithms (EDC with Multi-SL outperforms DATM with hard labeling). We provide more results with different labeling methods on Tab. [2](#page-5-1) and Tab. [3,](#page-5-2) further confirming the impact of labeling methods. In terms of labeling methods, Multi-SL performs the best, followed by soft labeling, and then hard labeling. It is unacceptable that an evaluation setting outweighs the algorithm itself. Thus, we emphasize the importance of a unified evaluation protocol. We consistently use Multi-SL in our ablations, and soft labeling in Tab. [8](#page-7-0) for fair comparison with the previous work [\[45\]](#page-11-13).

Augmentation and Loss Function. Table [2](#page-5-1) presents an ablation study on the use of CutMix [\[49\]](#page-11-28), a strong data augmentation applied in previous works [\[31,](#page-11-4) [32,](#page-11-23) [35\]](#page-11-5). Table [3](#page-5-2) ablates two loss functions widely used in previous dataset condensation works [\[13,](#page-10-4) [31,](#page-11-4) [32,](#page-11-23) [35\]](#page-11-5): KL-Divergence and MSE-GT. All of the experiments are done in the setting of IPC=1 on UCF101. We show the results of three condensation algorithms with different labeling methods and make the following observations:

(1) Using MSE-GT loss without CutMix augmentation generally leads to better performance. Through all the comparison groups, labeling methods contribute up to an 8.6% improvement, followed by 3.3% from augmentation and 2.4% from loss functions, in that order.

(2) Whatever the evaluation setting, RDED performs better than DATM, and  $EDC^{\dagger}$  performs worst. That is to say

<span id="page-5-3"></span>

| Sampling       | Interpolation | RDED        | EDC         | DATM       |
|----------------|---------------|-------------|-------------|------------|
| Naive          | X             | 11.2        | 10.0        | 7.2        |
| Segment        | X             | 10.6        | 10.9        | 7.3        |
| Sliding-window | X             | <b>11.5</b> | <b>12.0</b> | <b>8.5</b> |
| Sliding-window | ✓             | 9.0         | 11.8        | 5.5        |

Table 4. Ablation results of temporal processing on K400. Slidingwindow sampling outperforms segment sampling and brings larger improvements on synthetic data. Linear interpolation does not work for all methods.

<span id="page-5-4"></span>

| Interpolation | None | Duplication | Linear |
|---------------|------|-------------|--------|
| EDC           | 11.2 | 8.9         | 9.3    |
| DATM          | 15.3 | 10.5        | 9.0    |
| RDED          | 17.5 | 12.3        | 15.2   |

Table 5. Ablation results of interpolation algorithms on SSv2. It shows that training-free interpolators are ineffective.

the comparison between methods is consistent under various unified evaluation settings.

(3) Although RDED generally performs best under the same setting, DATM without CutMix provides 12.1% accuracy surpassing the performance of RDED with CutMix (11.0%) under soft labels. There remains such examples. This shows that evaluation settings can affect the conclusion of comparison.

To conclude, evaluation settings for dataset condensation makes a great influence to the final metric, sometimes even larger than the condensation algorithm itself. Thus, it is urgent to establish an evaluation protocol for video dataset condensation. Following our experiments, we adopted a evaluation protocol with Multi-SL labeling, without Cut-Mix, and using MES-GT loss. Further details of the evaluation protocol can be found in Appendix [B.](#page-8-1)

<span id="page-5-0"></span>

### 4.2. Effect of Temporal Processing

Previous experiments employ naive temporal processing, where synthetic videos are directly used as model inputs. In this subsection, we investigate advanced temporal processing methods, including sampling and interpolation (Sec. [3.1\)](#page-2-1), and present ablation results on instance compression and temporal compression.

Table [4](#page-5-3) first ablates different temporal processing methods on K400, and we also provide results on SSv2 in Appendix [C.](#page-8-2) The first row is the baseline setting that we used in previous subsection. The second and the third rows apply different sampling methods as introduced in Sec. [3.1.](#page-2-1) The fourth row adds linear interpolation based on the third row. From the results, we conclude that: (1) Our proposed sliding-window brings consistent improvements and outperforms segment sampling. This aligns with our analysis in

<span id="page-6-2"></span><span id="page-6-1"></span>

| [IPC, $T_c$ ] | [8, 4] | [4, 8] | [2, 16] | [1, 32]     | Condensation Network |         |     |          | Evaluation Network |
|---------------|--------|--------|---------|-------------|----------------------|---------|-----|----------|--------------------|
| HMDB51        | 15.2   | 18.3   | 17.5    | 17.8        | MiniC3D              | R(2+1)D | I3D | SlowOnly | MiniC3D            |
| <b>UCF101</b> | 22.9   | 26.1   | 24.3    | 23.3        | ✓                    |         |     |          | 10.1               |
| SSv2          | 23.5   | 25.3   | 22.6    | 19.5        | ✓                    | ✓       |     |          | 8.7                |
| K400          | 14.3   | 16.2   | 15.4    | 14.1        | ✓                    |         | ✓   |          | 11.7               |
| ✓             |        |        | ✓       | 12.1        |                      |         |     |          |                    |
| ✓             | ✓      |        | ✓       | <b>12.4</b> |                      |         |     |          |                    |

Table 6. Trade-off between IPC and  $T_c$  based on RDED. Generally, IPC outweighs  $T_c$ , and [4,8] performs best on all the datasets. When  $T_c$  is set to four, the drop in performance is due to the use of additional interpolation. Note that IPC  $\propto N_c$ .

Sec. [3.1.](#page-2-1) (2) Linear interpolation brings performance drop. It appears that training-free interpolation is ineffective for video dataset condensation.

Table [5](#page-5-4) further verifies the second conclusion above. It provides results of three algorithms with different interpolation algorithms. We observe that not using interpolation yields the best performance among these training-free interpolators. For trainable interpolators, we encourage further exploration of their performance. Following our experiments, we only employ interpolation when the number of video frames  $T_c$  is lower than the model's input length 8 in subsequent studies.

Then, Tab. [6](#page-6-1) shows the trade-off between IPC and  $T_c$ based on RDED. The training iteration of the evaluation model and the total frame number remain consistent across different results. Additional interpolation is applied to [8,4], matching the frame number to the model's input length. Generally, larger IPC brings better results except [8,4]. Considering the interpolation brings sharp performance drop by previous analysis, we conclude that the number of videos is more important than the number of frames.

#### 4.3. Ablation of Condensation Models

In the dataset condensation pipeline, neural networks play two key roles: aiding the condensation process and being trained on the condensed dataset to evaluate the effectiveness of the condensation algorithm. Previous experiments use MiniC3D for both roles. However, in dataset distillation, using multiple condensation models often enhances performance. This subsection investigates the impact of different condensation models on dataset distillation performance on UCF101.

We try MiniC3D,  $R(2+1)D$ , I3D and SlowOnly as condensation models on Tab. [7.](#page-6-0) The results show that I3D and SlowOnly boost the performance when combined with MiniC3D, but  $R(2+1)D$  has a negative influence. This may be due to  $R(2+1)D$  explicitly separates spatial and temporal modeling, creating a significant structural difference from MiniC3D. As a result, it is challenging to jointly optimize condensed data using both architectures simultaneously. Furthermore, the final row demonstrates that the improvements achieved through the integration of I3D and

<span id="page-6-0"></span>

Table 7. Ablation results of condensation networks on UCF101. Various combinations of condensation networks for EDC are tried.

SlowOnly can be accumulated.

### 4.4. Main Results and Analysis

One of our main motivations is to compare previous image dataset condensation algorithms on video datasets. Figure [4](#page-4-1) intuitively shows their performance on UCF101 with increasing IPCs. From the figure, we can observe that RDED with Multi-SL generally outperforms other two methods.

Table [8](#page-7-0) further shows the results on four widely-used datasets. The full dataset refers to the accuracy of the network trained on the full dataset, serving as the upper-bound results. Following the previous work [\[45\]](#page-11-13), we report the top1 accuracy for HMDB51 and UCF101, and top5 accuracy for SSv2 and K400. We adopt soft labeling in evaluation for fair comparison with previous results [\[45\]](#page-11-13).

For sample selection methods, our results show that Random outperforms Herding in most cases. This is violated with the intuition. However, the similar conclusion can be found in DeepCore [\[12\]](#page-10-16). It conducts an empirical study on all kinds of traditional sample selection methods, and finds that Random remains a strong baseline. However, more recent work RDED outperforms previous sample selection methods just as results in image domains [\[35\]](#page-11-5). For dataset distillation methods, we can find that our implementation of EDC and DATM both surpass previous methods [\[45\]](#page-11-13). DATM achieves the best performance among dataset distillation methods. However, the computation and storage consumption of DATM is much higher than EDC. In our experiments, DATM consumes up to 40G GPU memories, while EDC only requires less than 10G memories. Trajectory matching methods require further refinement and development to better scale to larger datasets.

By comprehensively comparing the results across all methods, we observe that RDED achieves superior performance over DATM on HMDB51 and UCF101, while DATM performs best on the SSv2 and K400. We argue that SSv2 and K400 are more challenging than the former two datasets, primarily due to two key factors: (1) their significantly larger data scale, which demands more effective information preservation by condensation algorithms, and (2) their more diverse data distribution, characterized by a greater number of action categories and mo-

<span id="page-7-2"></span><span id="page-7-0"></span>

| Dataset                 | HMDB51                |             |             | UCF101      |             |             | SSv2        |             |             | K400        |             |             |             |
|-------------------------|-----------------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|
|                         | IPC                   | 1           | 5           | 10          | 1           | 5           | 10          | 1           | 5           | 10          | 1           | 5           | 10          |
|                         | Ratio $‰$             | 1.2         | 5.9         | 11.8        | 0.4         | 0.9         | 4.6         | 0.18        | 0.92        | 1.8         | 0.05        | 0.23        | 0.47        |
| Full Dataset            |                       | 26.1        |             |             | 52.6        |             |             | 47.5        |             |             | 44.6        |             |             |
| Sample<br>Selection     | Random                | 8.3         | 15.6        | 17.0        | 10.6        | 20.1        | 25.0        | 11.8        | 18.0        | 21.6        | 7.1         | 11.1        | 14.1        |
|                         | Herding               | 10.6        | 12.2        | 15.9        | 10.2        | 16.8        | 22.2        | 11.7        | 17.4        | 20.0        | 6.1         | 11.3        | 13.8        |
|                         | <b>RDED</b>           | <b>12.0</b> | <b>15.7</b> | <b>18.0</b> | <b>14.3</b> | <b>23.8</b> | <b>28.2</b> | <b>12.7</b> | <b>19.8</b> | <b>22.5</b> | <b>11.9</b> | <b>16.5</b> | <b>17.1</b> |
| Dataset<br>Distillation | $DM$ <i>†</i> [45]    | 6.0         | 8.2         | -           | -           | -           | -           | 4.0         | 3.8         | -           | 6.3         | 7.0         | -           |
|                         | $MTT$ <i>†</i> [45]   | 6.5         | 8.9         | -           | -           | -           | -           | 5.5         | 8.3         | -           | 6.3         | 11.5        | -           |
|                         | $FRePo$ <i>†</i> [45] | 8.6         | 10.3        | -           | -           | -           | -           | -           | -           | -           | -           | -           | -           |
|                         | <b>EDC</b>            | 6.9         | 8.9         | 12.5        | 8.6         | 9.4         | 12.0        | 8.4         | 10.5        | 12.3        | 10.9        | 11.2        | 12.6        |
|                         | <b>DATM</b>           | 9.0         | 12.9        | 14.2        | 14.2        | 20.8        | 23.2        | 12.9        | 20.6        | 24.2        | 12.1        | 16.7        | 18.2        |

Table 8. Results of previous SOTA and our methods on HMDB51, UCF101, SSv2 and K400. Top-1 accuracy for HMDB51 and UCF101, and Top-5 accuracy for SSv2 and K400 are reported. The upper and lower sections of the table shows results for sample selection and dataset distillation, respectively. Underlined values indicate the best results within its category (selection or distillation), while **bold** values represent the overall best result. IPC: Instance(s) Per Class, Ratio: total condensation ratio defined in Sec. [3.1.](#page-2-1) † indicates the methods come from previous work [\[45\]](#page-11-13).

<span id="page-7-1"></span>

|             | <b>Evaluation Network</b> |             |             |             |
|-------------|---------------------------|-------------|-------------|-------------|
|             | MiniC3D                   | $R(2+1)D$   | I3D         | SlowOnly    |
| Random      | 14.7                      | 8.8         | 9.1         | 13.7        |
| Herding     | 13.0                      | 5.7         | 8.7         | 12.3        |
| <b>RDED</b> | <b>17.7</b>               | <b>11.7</b> | <b>14.1</b> | <b>21.1</b> |
| EDC         | 12.0                      | 3.7         | 8.7         | 7.4         |
| <b>DATM</b> | 15.4                      | 7.0         | 9.3         | 15.0        |

Table 9. Cross-architecture generalization on UCF101 with IPC=1. MiniC3D is used as the condensation network and all the four networks are used for evaluation.

tion paradigms. These challenges highlight the significant potential of dataset distillation methods in demanding scenarios. This aligns with the ultimate goal of achieving efficient training in the era of big data, where reducing computational and storage costs while maintaining performance is critical. Last but not least, our methods achieve state-ofthe-art results on all the four datasets.

Cross-Architecture Generalization. A key attribute of a condensed dataset is its capacity to generalize effectively to various unseen architectures [\[6\]](#page-10-22). Previous experiments performed dataset condensation using MiniC3D and evaluated it on the same architecture. In this subsection, we evaluate the cross-architecture generalization by considering additional networks architectures for evaluation. As presented in Tab. [9,](#page-7-1) RDED outperforms other methods on whatever evaluation network. The rankings of different methods are consistent through different evaluation networks, and all of them shows good generalization ability.

## 5. Limitations

Our results have shown that training-free interpolator fails to enhance the condensation of temporal information. However, we do not propose effective trainable interpolators to solve the problem. Besides, DATM requires relatively high resource consumption, which limits its scalability to next level. This is crucial for fully unlocking its potential in addressing challenges in the era of big data. Nevertheless, certain algorithms are not the focus of this work. Finally, there are some interesting works utilizing generative models or their ideas to assist dataset condensation [\[4,](#page-10-23) [43\]](#page-11-29). Due to the limitation of time and energy, we do not include them in our study. We anticipate future research to address these challenges, contributing to advancements in the field and enabling more efficient and effective solutions.

### 6. Conclusion

In response to the increasing demands of video dataset condensation, we extend three representative dataset condensation algorithms to space-time, each representing a distinct category of condensation algorithm. Besides, we ablate on the evaluation settings of dataset condensation and find that these settings significantly influence the final metrics, especially the labeling methods. Therefore, we propose a unified evaluation protocol to ensure consistent and reliable assessments. Based on this evaluation protocol, we further compare temporal processing methods in videos. Our experiments reveal that our proposed sliding-window sampling outperforms previous methods. Besides, we find that the number of videos is more critical than number of frames. Finally, we conducted a comprehensive comparison of various dataset condensation algorithms. Dataset distillation methods perform better in challenging scenarios, while

<span id="page-8-4"></span>sample selection methods excel in easier ones. This highlights the great potential of distillation methods on larger datasets. We hope our study will foster future research on video dataset condensation.

## Appendix

<span id="page-8-0"></span>

# A. Implementation Details of Video Condensation Algorithms

All of the video backbones employed in this study are built upon mmaction codebase. We implement MiniC3D described in  $[45]$  and make use of existing  $R(2+1)D$ , I3D and SlowOnly with ResNet-18. Then, we introduce the implementation details of specific algorithms.

### A.1. Details of DATM

We pre-compute 20 expert trajectories for each dataset. Then, we conduct a grid research for some critical hyperparameters: the mapping scope of expert trajectories, learning rate of images, syn-step (N in Eq. [\(2\)](#page-3-1), M is fixed), and batch-syn on UCF101 with IPC=1. Considering the large search scope, we divide them into two groups: three values about the mapping scope as group1 and the latter three values as group2. For the hyper-parameters of other datasets, we adjust their values according to previous experience. After the hyperparameter tuning, their values are fixed across different ablations.

## A.2. Details of EDC

We adopt the paradigm introduced in the previous subsection to perform hyperparameter tuning. The hyperparameters of EDC include batch size, learning rate, iteration and weight for different losses. Real images are used as the initialization of synthetic images both in EDC and DATM.

#### A.3. Details of RDED

Considering the concatenation operation is not employed in other methods, we simply set the factor to 1 for fair comparison with other methods. In the selection phase of RDED, we randomly sample 10 clips from each video.

<span id="page-8-1"></span>

### B. Evaluation Protocol

For fair comparison among different types of methods, we propose a unified evaluation protocol. We introduce the evaluation details in this section for quick reference.

**Optimizer:** we use Adam optimizer with base learning rate 0.001, betas [0.9, 0.999] and weight decay 0.01.

Scheduler: we train the evaluation for 300 epochs with a cosine learning rate scheduler.

Batch size: following the implementation in Sel-Match [\[20\]](#page-10-24), we adjust batch size according to dataset and IPC to make sure the iteration number is fixed:

<span id="page-8-3"></span>

| Sampling       | Interpolation | RDED        | EDC         |
|----------------|---------------|-------------|-------------|
| None           | x             | 17.5        | 11.2        |
| Segment        | x             | 18.8        | 11.3        |
| Sliding-window | x             | <b>21.1</b> | <b>13.1</b> |
| Sliding-window | ✓             | 13.4        | 12.6        |

Table 10. Ablation results of temporal processing on SSv2. The setting is same as Tab. [4.](#page-5-3) It also shows that sliding-window sampling outperforms segment sampling. Linear interpolation does not work for all methods.

$$
BS = base \times IPC,
$$
 (5)

where BS is the training batch size, and base is the batch size when IPC is equal to one. The base for HMDB51 and UCF101 is set to 10, base for SSv2 is 20, and that for K400 is 40.

Augmentation: we apply resized crop and horizontal flip when training evaluation models.

Loss Function: we use MSE-GT as the loss function.

Labeling Method: we use soft labeling in Tab. [8](#page-7-0) for comparison with previous results, and use Multi-SL in other experiments.

<span id="page-8-2"></span>

### C. More Results

Table [10](#page-8-3) supplements ablation results of temporal processing on SSv2. It shares similar observations as previous results: sliding-window sampling outperforms previous segment sampling, and training-free interpolation brings sharp drops in performance.

Table [11](#page-10-25) supplements results on UCF101 with Multi-SL. The results are higher than those in Tab. [8](#page-7-0) with soft labeling. The comparison between methods is consistent with the previous observation: RDED and DATM achieve best results within their respective category, and RDED achieves sota on UCF101.

### D. Visualization

We show the visualization of RDED and EDC in Fig. [5](#page-9-0) and Fig. [6,](#page-9-1) representing selection and distillation method respectively. Compared with RDED (real data), EDC captures some crucial patterns through distillation. While there is some redundant information in the temporal dimension.

### Acknowledge

Yang Chen would like to thank Ruopeng Gao, Shuai Wang and Shitong Shao for the kind discussion.

<span id="page-9-0"></span>Image /page/9/Picture/0 description: The image displays five rows of video frames. The top row shows a woman applying makeup to her eyes. The second row shows a woman applying lipstick. The third row shows a person walking in a wooded area. The fourth row shows a baby crawling. The bottom row shows a gymnast performing on a balance beam.

Figure 5. Visualization of RDED for UCF101 IPC=1

<span id="page-9-1"></span>Figure 6. Visualization of EDC for UCF101 IPC=1

<span id="page-10-25"></span>

| <b>IPC</b>          |             |      | 10   |      |
|---------------------|-------------|------|------|------|
| <b>Full Dataset</b> |             | 52.6 |      |      |
| Sample              | Random      | 14.7 | 26.9 | 31.4 |
| Selection           | Herding     | 13.0 | 22.8 | 27.7 |
|                     | <b>RDED</b> | 17.7 | 29.4 | 33.5 |
| Dataset             | EDC.        | 12.0 | 22.2 | 24.3 |
| Distillation        | <b>DATM</b> | 15.4 | 27.2 | 31.7 |

Table 11. Results of our methods with Multi-SL on UCF101.

## References

- <span id="page-10-21"></span>[1] Gedas Bertasius, Heng Wang, and Lorenzo Torresani. Is space-time attention all you need for video understanding? In *ICML*, page 4, 2021. [3](#page-2-2)
- <span id="page-10-11"></span>[2] João Carreira and Andrew Zisserman. Quo vadis, action recognition? a new model and the kinetics dataset. In *CVPR*, pages 4724–4733, 2017. [2,](#page-1-0) [5](#page-4-2)
- <span id="page-10-1"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-10-23"></span>[4] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 3739–3748, 2023. [8](#page-7-2)
- <span id="page-10-12"></span>[5] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. In *Proceedings of the Twenty-Sixth Conference on Uncertainty in Artificial Intelligence*, pages 109–116, 2010. [2](#page-1-0)
- <span id="page-10-22"></span>[6] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. DC-BENCH: Dataset condensation benchmark. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022. [8](#page-7-2)
- <span id="page-10-2"></span>[7] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *ICML*, pages 6565–6590, 2023. [1,](#page-0-1) [2,](#page-1-0) [5](#page-4-2)
- <span id="page-10-3"></span>[8] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In *NeurIPS*, 2022. [1](#page-0-1)
- <span id="page-10-18"></span>[9] Jianrong Ding, Zhanyu Liu, Guanjie Zheng, Haiming Jin, and Linghe Kong. CondTSF: One-line plugin of dataset condensation for time series forecasting. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2024. [2](#page-1-0)
- <span id="page-10-20"></span>[10] Christoph Feichtenhofer, Haoqi Fan, Jitendra Malik, and Kaiming He. Slowfast networks for video recognition. In *ICCV*, pages 6202–6211, 2019. [2](#page-1-0)
- <span id="page-10-10"></span>[11] Raghav Goyal, Samira Ebrahimi Kahou, Vincent Michalski, Joanna Materzyńska, Susanne Westphal, Heuna Kim, Valentin Haenel, Ingo Fruend, Peter Yianilos, Moritz Mueller-Freitag, Florian Hoppe, Christian Thurau, Ingo Bax, and Roland Memisevic. The "something something" video database for learning and evaluating visual common sense, 2017. [2,](#page-1-0) [5](#page-4-2)

- <span id="page-10-16"></span>[12] Chengcheng Guo, Bo Zhao, and Yanbing Bai. Deepcore: A comprehensive library for coreset selection in deep learning, 2022. [2,](#page-1-0) [4,](#page-3-2) [7](#page-6-2)
- <span id="page-10-4"></span>[13] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *The Twelfth International Conference on Learning Representations*, 2024. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-2) [5,](#page-4-2) [6](#page-5-5)
- <span id="page-10-15"></span>[14] Ziheng Jiang, Chiyuan Zhang, Kunal Talwar, and Michael C Mozer. Characterizing structural regularities of labeled data in overparameterized models. In *International Conference on Machine Learning*, 2021. [2](#page-1-0)
- <span id="page-10-17"></span>[15] Wei Jin, Lingxiao Zhao, Shichang Zhang, Yozen Liu, Jiliang Tang, and Neil Shah. Graph condensation for graph neural networks. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2022. [2](#page-1-0)
- <span id="page-10-13"></span>[16] Krishnateja Killamsetty, Sivasubramanian Durga, Ganesh Ramakrishnan, Abir De, and Rishabh Iyer. Grad-match: Gradient matching based data subset selection for efficient deep model training. In *International Conference on Machine Learning*, 2021. [2](#page-1-0)
- <span id="page-10-14"></span>[17] Krishnateja Killamsetty, Durga Sivasubramanian, Ganesh Ramakrishnan, and Rishabh Iyer. Glister: Generalization based data subset selection for efficient and robust learning. In *Association for the Advancement of Artificial Intelligence*, 2021. [2](#page-1-0)
- <span id="page-10-5"></span>[18] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *ICML*, 2022. [1](#page-0-1)
- <span id="page-10-9"></span>[19] H. Kuehne, H. Jhuang, E. Garrote, T. Poggio, and T. Serre. Hmdb: A large video database for human motion recognition. In *ICCV*, pages 2556–2563, 2011. [2,](#page-1-0) [5](#page-4-2)
- <span id="page-10-24"></span>[20] Yongmin Lee and Hye Won Chung. Selmatch: Effectively scaling up dataset distillation via selection-based initialization and partial updates by trajectory matching. In *Forty-first International Conference on Machine Learning*, 2024. [9](#page-8-4)
- <span id="page-10-6"></span>[21] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *NeurIPS*, 2022. [1](#page-0-1)
- <span id="page-10-19"></span>[22] Zhanyu Liu, Ke Hao, Guanjie Zheng, and Yanwei Yu. Dataset condensation for time series classification via dual domain matching. In *Proceedings of the ACM SIGKDD Conference on Knowledge Discovery and Data Mining (KDD)*, 2024. [2](#page-1-0)
- <span id="page-10-7"></span>[23] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, 2022. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-10-0"></span>[24] Noel Loo, Ramin Hasani, Mathias Lechner, Alexander Amini, and Daniela Rus. Dataset distillation fixes dataset reconstruction attacks. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2024. [1](#page-0-1)
- <span id="page-10-8"></span>[25] Huimin Lu, Masaru Isonuma, Junichiro Mori, and Ichiro Sakata. UniDetox: Universal detoxification of large language models via dataset distillation. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2025. [1,](#page-0-1) [2](#page-1-0)

- <span id="page-11-17"></span>[26] Baharan Mirzasoleiman, Jeff Bilmes, and Jure Leskovec. Coresets for data-efficient training of machine learning models. In *International Conference on Machine Learning*, 2020.  $\mathcal{D}$
- <span id="page-11-2"></span>[27] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-11-3"></span>[28] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-11-14"></span>[29] Tian Qin, Zhiwei Deng, and David Alvarez-Melis. A label is worth a thousand images in dataset distillation. In *The Thirty-eighth Annual Conference on Neural Information Processing Systems*, 2024. [2](#page-1-0)
- <span id="page-11-16"></span>[30] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *International Conference on Learning Representations*, 2018. [2](#page-1-0)
- <span id="page-11-4"></span>[31] Shitong Shao, Zikai Zhou, Huanran Chen, and Zhiqiang Shen. Elucidating the design space of dataset condensation. *arXiv preprint arXiv:2404.13733*, 2024. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-2) [4,](#page-3-2) [5,](#page-4-2) [6](#page-5-5)
- <span id="page-11-23"></span>[32] Xindong Zhang Shitong Shao, Zeyuan Yin and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. *arXiv preprint arXiv:2311.17950*, 2023. [2,](#page-1-0) [4,](#page-3-2) [5,](#page-4-2) [6](#page-5-5)
- <span id="page-11-15"></span>[33] Khurram Soomro, Amir Roshan Zamir, and Mubarak Shah. UCF101: A dataset of 101 human actions classes from videos in the wild. *CoRR*, abs/1212.0402, 2012. [2,](#page-1-0) [5](#page-4-2)
- <span id="page-11-1"></span>[34] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *Proceedings of the International Conference on Machine Learning (ICML)*, pages 9206–9216, 2020. [1](#page-0-1)
- <span id="page-11-5"></span>[35] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2024. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-2) [4,](#page-3-2) [6,](#page-5-5) [7](#page-6-2)
- <span id="page-11-18"></span>[36] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018. [2](#page-1-0)
- <span id="page-11-26"></span>[37] Zhan Tong, Yibing Song, Jue Wang, and Limin Wang. Videomae: Masked autoencoders are data-efficient learners for self-supervised video pre-training. In *NeurIPS*, pages 10078–10093. Curran Associates, Inc., 2022. [3](#page-2-2)
- <span id="page-11-24"></span>[38] Du Tran, Lubomir Bourdev, Rob Fergus, Lorenzo Torresani, and Manohar Paluri. Learning spatiotemporal features with 3d convolutional networks. In *ICCV*, pages 4489–4497, 2015. [2](#page-1-0)
- <span id="page-11-25"></span>[39] Du Tran, Heng Wang, Lorenzo Torresani, Jamie Ray, Yann LeCun, and Manohar Paluri. A closer look at spatiotemporal convolutions for action recognition. In *CVPR*, pages 6450– 6459, 2018. [2](#page-1-0)
- <span id="page-11-6"></span>[40] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and

Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022. [1,](#page-0-1) [2](#page-1-0)

- <span id="page-11-19"></span>[41] Kai Wang, Zekai Li, Zhi-Qi Cheng, Samir Khaki, Ahmad Sajedi, Ramakrishna Vedantam, Konstantinos N Plataniotis, Alexander Hauptmann, and Yang You. Emphasizing discriminative features for dataset distillation in complex scenarios. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2025. [2](#page-1-0)
- <span id="page-11-27"></span>[42] Mengmeng Wang, Jiazheng Xing, and Yong Liu. Actionclip: A new paradigm for video action recognition. *CoRR*, abs/2109.08472, 2021. [3](#page-2-2)
- <span id="page-11-29"></span>[43] Shaobo Wang, Yicun Yang, Zhiyuan Liu, Chenghao Sun, Xuming Hu, Conghui He, and Linfeng Zhang. Dataset distillation with neural characteristic function: A minmax perspective. *arXiv preprint arXiv:2502.20653*, 2025. [8](#page-7-2)
- <span id="page-11-0"></span>[44] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2,](#page-1-0) [5](#page-4-2)
- <span id="page-11-13"></span>[45] Ziyu Wang, Yue Xu, Cewu Lu, and Yong-Lu Li. Dancing with still images: Video distillation via static-dynamic disentanglement, 2024. [1,](#page-0-1) [2,](#page-1-0) [3,](#page-2-2) [5,](#page-4-2) [6,](#page-5-5) [7,](#page-6-2) [8,](#page-7-2) [9](#page-8-4)
- <span id="page-11-12"></span>[46] Xindi Wu, Byron Zhang, Zhiwei Deng, and Olga Russakovsky. Vision-language dataset distillation. *Transactions on Machine Learning Research*, 2024. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-11-10"></span>[47] Beining Yang, Kai Wang, Qingyun Sun, Cheng Ji, Xingcheng Fu, Hao Tang, Yang You, and Jianxin Li. Does graph distillation see like vision dataset counterpart? In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-11-22"></span>[48] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *Thirty-seventh Conference on Neural Information Processing Systems*, 2023. [2,](#page-1-0) [4,](#page-3-2) [5](#page-4-2)
- <span id="page-11-28"></span>[49] Sangdoo Yun, Dongyoon Han, Seong Joon Oh, Sanghyuk Chun, Junsuk Choe, and Youngjoon Yoo. Cutmix: Regularization strategy to train strong classifiers with localizable features. In *Proceedings of the IEEE/CVF international conference on computer vision*, pages 6023–6032, 2019. [6](#page-5-5)
- <span id="page-11-11"></span>[50] Tianle Zhang, Yuchen Zhang, Kun Wang, Kai Wang, Beining Yang, Kaipeng Zhang, Wenqi Shao, Ping Liu, Joey Tianyi Zhou, and Yang You. Two trades is not baffled: Condensing graph via crafting rational gradient matching. *arXiv preprint arXiv:2402.04924*, 2024. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-11-7"></span>[51] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-11-21"></span>[52] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023. [2](#page-1-0)
- <span id="page-11-8"></span>[53] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [1,](#page-0-1) [2](#page-1-0)
- <span id="page-11-20"></span>[54] Wenliang Zhong, Haoyu Tang, Qinghai Zheng, Mingzhu Xu, Yupeng Hu, and Liqiang Nie. Towards stable and storageefficient dataset distillation: Matching convexified trajectory. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2025. [2](#page-1-0)
- <span id="page-11-9"></span>[55] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *arXiv preprint arXiv:2206.00719*, 2022. [1,](#page-0-1) [2](#page-1-0)
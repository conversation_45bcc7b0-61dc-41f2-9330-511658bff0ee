# Dataset Distillation Using Parameter Pruning

<PERSON><PERSON>, *Graduate Student Member, IEEE*, <PERSON>, *Member, IEEE*, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>, *Senior Member, IEEE*

*Abstract*—In this study, we propose a novel dataset distillation method based on parameter pruning. The proposed method can synthesize more robust distilled datasets and improve distillation performance by pruning difficult-to-match parameters during the distillation process. Experimental results on two benchmark datasets show the superiority of the proposed method.

*Index Terms*—Dataset distillation, optimization, parameter pruning.

## I. INTRODUCTION

**LARGE** datasets containing millions of samples have become the standard for obtaining advanced models in many artificial intelligence areas, including natural lan-ARGE datasets containing millions of samples have become the standard for obtaining advanced models guage processing, speech recognition, and computer vision [\[1\]](#page-3-0). Meanwhile, large datasets also raise some issues. For example, data storage and preprocessing are becoming increasingly difficult. Furthermore, expensive servers are required to train models on these datasets, which is not friendly for lowresource environments [\[2\]](#page-3-1). An effective way to solve these problems is data selection, which identifies representative training samples of large datasets [\[3\]](#page-3-2). However, because some of the original data cannot be discarded, there is an upper limit on the compression rate of the data selection method.

Recently, dataset distillation as an alternative method to the data selection has attracted widespread attention [\[4\]](#page-3-3). Dataset distillation is the task of synthesizing a small dataset that preserves most information of the original large dataset. The algorithm of dataset distillation takes a sizable real dataset as the input and synthesizes a small distilled dataset. Unlike the data selection method that uses actual data from the original dataset, dataset distillation generates synthetic data with a different distribution from the original one [\[5\]](#page-4-0). Therefore, the dataset distillation method can distill the whole dataset into several images, or even only one image [\[6\]](#page-4-1). Dataset distillation has many application scenarios, such as privacy protection [\[7\]](#page-4-2), [\[8\]](#page-4-3), continual learning [\[9\]](#page-4-4), and neural architecture search [\[10\]](#page-4-5), etc.

Since the dataset distillation task was first introduced in 2018 by Wang et al. [\[4\]](#page-3-3), it has gained increasing attention in the research community [\[11\]](#page-4-6). The original dataset distillation algorithm is based on meta-learning and optimizes distilled images by gradient-based hyperparameter optimization. Subsequently, many studies have significantly improved distillation performance by label distillation [\[12\]](#page-4-7), gradient matching [\[10\]](#page-4-5), differentiable augmentation [\[13\]](#page-4-8), and distribution/feature matching [\[14\]](#page-4-9), [\[15\]](#page-4-10). The recently proposed dataset distillation method by matching network parameters has been the new state-of-the-art (SOTA) on several datasets [\[16\]](#page-4-11). However, we found that a few parameters are difficult to match during the distillation process, which degrades distillation performance.

The presence of difficult-to-match parameters during dataset distillation is due to data heterogeneity. This heterogeneity arises from differences and variations in the training datasets used for the teacher and student networks. While the teacher network is trained on a large, original dataset, the student network is trained on a compressed distilled dataset. Data heterogeneity introduces discrepancies in data distribution and representation between the teacher and student datasets. As a result, certain patterns and critical knowledge may be underrepresented or even absent in the distilled dataset. Consequently, the absence of crucial information in the distilled dataset can lead to some parameters in the student network being unable to sufficiently match their corresponding counterparts in the teacher network, giving rise to the emergence of difficult-to-match parameters.

In this study, we propose a new dataset distillation method using parameter pruning. As one of the model pruning approaches, parameter pruning is frequently used for model compression and accelerated model training. Here, we introduce parameter pruning into dataset distillation to remove the effect of difficult-to-match parameters. The proposed method can synthesize more robust distilled datasets by pruning difficultto-match parameters during the distillation process, improving the distillation and cross-architecture generalization performance. Experimental results on two benchmark datasets show the superiority of the proposed method to other SOTA dataset distillation methods.

Our main contributions can be summarized as follows:

- We propose a new dataset distillation method based on parameter pruning, which can synthesize more robust distilled datasets and improve the distillation performance.
- The proposed method outperforms other SOTA dataset distillation methods on two benchmark datasets and has better cross-architecture generalization performance.

## II. METHODOLOGY

An overview of the proposed method is depicted in Fig. [1.](#page-1-0) Our method consists of three stages: teacher-student architecture training, teacher-student parameter matching, and optimized distilled dataset generation.

### A. Teacher-Student Architecture Training

First, we pretrain N teacher networks on  $\mathcal{D}_{original}$  and save their snapshot parameters at each epoch. We define teacher

This research was supported in part by the Hokkaido University-Hitachi Collaborative Education and Research Support Program and AMED Grant Number JP22zf0127004h0002. All experiments were conducted on the Data Science Computing System of Education and Research Center for Mathematical and Data Science, Hokkaido University.

Image /page/1/Figure/1 description: The image depicts a process involving neural networks and datasets. A legend indicates that orange arrows represent training on a distilled dataset (Student) and gradient backpropagation, while green arrows represent training on an original dataset (Teacher). Dashed orange arrows signify updating the distilled dataset. The diagram shows an initial network with parameters \(\\theta\_i\) being trained on the original dataset. This is followed by a series of iterations \((\\tilde{\\theta}\_{i,1}, \\tilde{\\theta}\_{i,2}, ..., \\tilde{\\theta}\_{i,J})\) where the student network is trained on a distilled dataset \(D\_{distill}\). At iteration \(J \ll K\), parameter pruning is applied, resulting in pruned networks \(\\tilde{\\theta}'\_{i,J}\) and \(\\theta''\_{i,J}\). A similarity metric is calculated, and a loss \(\mathcal{L}\) is computed, likely comparing the outputs or parameters of these pruned networks.

<span id="page-1-0"></span>Fig. 1. Overview of the proposed method. Our method uses a teacher-student architecture, and the objective is to make the student network parameters  $\tilde{\theta}'_{i,j}$ match the teacher network parameters  $\theta'_{i+K}$ .

 $\mathcal{D}_{\textrm{original}}$ 

parameters as time sequences of parameters  $\{\theta_i\}_0^I$ . Meanwhile, student parameters are defined as  $\tilde{\theta}_i$ , which are trained on the distilled dataset  $\mathcal{D}_{distill}$  at each training step *i*. At each distillation step, we first sample parameters from one of the teacher parameters at a random step  $i$  and use them to initialize student parameters as  $\tilde{\theta}_i = \theta_i$ . We set an upper bound  $I^+$  on the random step  $i$  to ignore the less informative later parts of the teacher parameters. The number of updates for student and teacher parameters are set as  $J$  and  $K$ , respectively, where  $J \ll K$ . For each student update j, we sample a minibatch  $b_{i,j}$  from a distilled dataset as follows:

$$
b_{i,j} \sim \mathcal{D}_{\text{distill}}.\tag{1}
$$

Then we perform j updates on the student parameters  $\theta$  using the cross-entropy loss  $\ell$  as follows:

$$
\tilde{\theta}_{i,j+1} = \tilde{\theta}_{i,j} - \alpha \nabla \ell(\mathcal{A}(b_{i,j}); \tilde{\theta}_{i,j}),
$$
\n(2)

where  $\alpha$  represents the trainable learning rate. A represents a differentiable data augmentation module proposed in [\[13\]](#page-4-8), which can improve the distillation performance.

### B. Teacher-Student Parameter Matching

Next, we obtain the student parameters  $\hat{\theta}_{i,J}$  trained on the distilled dataset  $\mathcal{D}_{distill}$  from J updates after initializing the student network. Meanwhile, we can obtain the teacher parameters  $\theta_{i+K}$  trained on the original dataset  $\mathcal{D}_{original}$  from  $K$  updates, which are the known parameters that have been pretrained. Next, we transform the student parameters  $\hat{\theta}_{i,J}$ and teacher parameters  $\theta_{i+K}$  into one-dimensional vectors as follows:

$$
\tilde{\theta}_{i,J} = [\tilde{\theta}_{i,J}^1, \tilde{\theta}_{i,J}^2, \cdots, \tilde{\theta}_{i,J}^p],\tag{3}
$$

$$
\theta_{i+K} = [\theta_{i+K}^1, \theta_{i+K}^2, \cdots, \theta_{i+K}^p],
$$
 (4)

where  $p$  represents the total number of parameters. If the numerical similarity of a parameter pair  $\frac{\tilde{\theta}_{i,J}^x}{\theta_{i+K}^x}$  or  $\frac{\theta_{i+K}^x}{\tilde{\theta}_{i,J}^x} < \epsilon$ , where  $\epsilon$  is a threshold, the parameter is recognized as difficultto-match parameter. The index  $x$  of the difficult-to-match parameter is remembered and then automatically pruned in

<span id="page-1-1"></span>

## Algorithm 1 Dataset Distillation Using Parameter Pruning

- **Require:**  $\{\theta_i\}_0^T$ : teacher parameters trained on  $\mathcal{D}_{original}$ ;  $\alpha_0$ : initial value for  $\alpha$ ; A: differentiable augmentation function;  $\epsilon$ : threshold for pruning; T: number of distillation steps; J: number of updates for the student network;  $K$ : number of updates for the teacher network;  $I^+$ : maximum start epoch.
- Ensure: optimized distilled dataset  $\mathcal{D}^*_{\text{distill}}$  and learning rate  $\alpha^*$ .
- 1: Initialize distilled dataset:  $\mathcal{D}_{\text{distill}} \sim \mathcal{D}_{\text{original}}$
- 2: Initialize trainable learning rate:  $\alpha = \alpha_0$
- 3: for each distillation step  $t = 0$  to  $T 1$  do
- 4: Choose random start epoch  $i < I^+$
- 5: Initialize student network with teacher parameter:  $\theta_i =$  $\theta_i$
- 6: **for** each student update  $j = 0$  to  $J 1$  do
- 7: Sample a minibatch of distilled dataset:  $b_{i,j} \sim \mathcal{D}_{distill}$

\n- 8: Update student network with cross-entropy loss:
\n- $$
  \tilde{\theta}_{i,j+1} = \tilde{\theta}_{i,j} - \alpha \nabla \ell(A(b_{i,j}); \tilde{\theta}_{i,j})
  $$
\n

$$
\theta_{i,j+1} = \theta_{i,j} - \alpha \nabla \ell(\mathcal{A}(\math>
$$

- 10: end for
- 11: if parameter similarity in  $\hat{\theta}_{i,J}$  and  $\theta_{i+K}$  is less than  $\epsilon$ then
- 12: Prune difficult-to-match parameters: Eqs. (3)–(7)
- 13: end if
- 14: Compute loss between the pruned parameters:
- 15:  $\mathcal{L} = ||\tilde{\theta}'_{i,J} \theta'_{i+K}||_2^2 / ||\theta'_i \theta'_{i+K}||_2^2$

16: Update 
$$
\mathcal{D}_{\text{distill}}
$$
 and  $\alpha$  with respect to  $\mathcal{L}$ 

Image /page/1/Figure/32 description: The image contains the text "17: end for" in a bold, black font against a white background.

 $\tilde{\theta}_{i,J}$ ,  $\theta_{i+K}$ , and  $\theta_i$ . The remaining effective parameters are defined as follows:

$$
\tilde{\theta}'_{i,J} = [\tilde{\theta}^1_{i,J}, \tilde{\theta}^2_{i,J}, \cdots, \tilde{\theta}^u_{i,J}],
$$
\n(5)

$$
\theta'_{i+K} = [\theta^1_{i+K}, \theta^2_{i+K}, \cdots, \theta^u_{i+K}],
$$
 (6)

$$
\theta_i' = [\theta_i^1, \theta_i^2, \cdots, \theta_i^u],\tag{7}
$$

<span id="page-2-0"></span>IPC Random Forgetting [\[17\]](#page-4-12) Herding [\[18\]](#page-4-13) DSA [\[13\]](#page-4-8) DM [\[14\]](#page-4-9) CAFE [\[15\]](#page-4-10) MTT [\[16\]](#page-4-11) Ours Full Dataset CIFAR-10  $1 \quad 14.4 \pm 2.0$   $13.5 \pm 1.2$   $21.5 \pm 1.2$   $28.8 \pm 0.7$   $26.0 \pm 0.8$   $31.6 \pm 0.8$   $46.3 \pm 0.8$   $46.4 \pm 0.6$  $10\begin{array}{|l} \n\text{10}}\n\end{array}$  26.0±1.2 23.3±1.0 31.6±0.7 52.1±0.5 48.9±0.6 50.9±0.5 65.3±0.7 65.5±0.3 84.8±0.1  $50 \mid 43.4 \pm 1.0$   $23.3 \pm 1.1$   $40.4 \pm 0.6$   $60.6 \pm 0.5$   $63.0 \pm 0.4$   $62.3 \pm 0.4$   $71.6 \pm 0.2$   $71.9 \pm 0.2$ CIFAR-100  $1 \begin{array}{|l|l|l|l|l|l|l|l|l|l|l|l|l|l|l|l|l|l|l$  $10 \begin{array}{|l} 14.6 \pm 0.5 \end{array}$   $15.1 \pm 0.3$   $17.3 \pm 0.3$   $32.3 \pm 0.3$   $29.7 \pm 0.3$   $31.5 \pm 0.2$   $40.1 \pm 0.4$   $43.1 \pm 0.3$   $56.2 \pm 0.3$  $50 \mid 30.0 \pm 0.4$   $30.5 \pm 0.3$   $33.7 \pm 0.5$   $42.8 \pm 0.4$   $43.6 \pm 0.4$   $42.9 \pm 0.2$   $47.7 \pm 0.2$   $48.4 \pm 0.3$ 

TABLE I TEST ACCURACY OF DIFFERENT METHODS ON CIFAR-10 AND CIFAR-100.

where  $u$  represents the number of remaining effective parameters. When pruning is applied, the less important or redundant parameters are eliminated, leading to a more concise representation of the student network. This process helps the student network align more closely with the teacher network, as it reduces the impact of data heterogeneity-induced discrepancies and improves the likelihood of parameter matching. By discarding irrelevant information, pruning allows the student network to focus on essential patterns and knowledge, thus mitigating the negative effects of information absence in the distilled dataset. Consequently, the alignment of parameter values between the teacher and student networks becomes more feasible, and the challenge of difficult-to-match parameters is alleviated. The final loss  $\mathcal L$  calculates the normalized squared  $L_2$  error between the remaining effective student parameters  $\tilde{\theta}'_{i,J}$  and teacher parameters  $\theta'_{i+K}$  as follows:

$$
\mathcal{L} = \frac{||\tilde{\theta}'_{i,J} - \theta'_{i+K}||_2^2}{||\theta'_i - \theta'_{i+K}||_2^2},
$$
\n(8)

where we normalize the  $L_2$  error by the distance  $\theta_i' - \theta_{i+K}'$ related to the teacher so that we can still obtain proper supervision from the late training period of the teacher network even if it has converged. In addition, the normalization process eliminates cross-layer and neuronal differences in magnitude.

### C. Optimized Distilled Dataset Generation

Finally, we minimize the loss  $\mathcal L$  using momentum stochastic gradient descent and backpropagate the gradients through all J updates to the student network for updating the pixels of the distilled dataset  $\mathcal{D}_{\text{distill}}$  and trainable learning rate  $\alpha$ . Note that the process of determining the optimized learning rate  $\alpha^*$  can function as an automatic adjustment for the number of student and teacher updates (i.e., hyperparameters  $J$  and  $K$ ). The distillation process of the proposed method is summarized in Algorithm [1.](#page-1-1) After obtaining the optimized distilled dataset  $\mathcal{D}^*_{\text{distill}}$ , we can train different neural networks on it for efficiency and use for downstream tasks, such as continual learning and neural architecture search.

## III. EXPERIMENTS

### A. Experimental Settings

We used two benchmark datasets (i.e., CIFAR-10 and CIFAR-100) in the experiments for comparison with other methods. The resolution of the images in CIFAR-10 and CIFAR-100 is  $32 \times 32$ . For comparative methods, we used three data selection methods: random selection (Random), example forgetting (Forgetting) [\[17\]](#page-4-12), and herding method (Herding) [\[18\]](#page-4-13). The random selection method offers simplicity while lacking informative example prioritization. Example forgetting method aims to reduce redundancy, potentially capturing diverse patterns, yet risks information loss from underrepresented examples. The herding method focuses on uncertain examples to enhance robustness. However, the method is computationally demanding.

Furthermore, we used five SOTA dataset distillation methods: differentiable siamese augmentation (DSA) [\[13\]](#page-4-8), distribution matching (DM) [\[14\]](#page-4-9), aligning features (CAFE) [\[15\]](#page-4-10), matching training trajectories (MTT) [\[16\]](#page-4-11) and kernel inducing point (KIP) [\[19\]](#page-4-14). Among the SOTA dataset distillation methods, DSA employs Siamese networks for augmentation and end-to-end training. However, optimal hyperparameter tuning is essential for DSA. DM aligns datasets' distributions to enhance parameter alignment and its effectiveness is influenced by the distribution-matching strategy employed. CAFE focuses on feature-level alignment via feature matching, with efficacy dependent on feature complexity and network architecture. MTT aligns training evolution for dynamic knowledge transfer that necessitates meticulous tuning and consideration of difficult-to-match parameters. KIP employs kernel methods to facilitate robust knowledge transfer. However, its effectiveness is influenced by the computational complexity it introduces and the critical decision of choosing an appropriate kernel function.

The network used in this study is a sample 128-width ConvNet [\[20\]](#page-4-15), which is frequently used in current dataset distillation methods. We conducted two experiments to verify the effectiveness of the proposed method: benchmark comparison, and cross-architecture generalization. We found that pruning too many parameters would crash model training. Hence, the parameter pruning threshold  $\epsilon$  was set to 0.1, which performed well in all experiments. All experimental results are the average accuracy and standard deviation of five networks trained from scratch on the distilled dataset.

### B. Benchmark Comparison

In this subsection, we verify the effectiveness of the proposed method by comparing it with other SOTA dataset distillation methods on two benchmark datasets: CIFAR-10 and CIFAR-100. We employed zero-phase component analysis (ZCA) whitening with default parameters and used a 3 depth ConvNet the same as MTT [\[16\]](#page-4-11). We pretrained 200 teacher networks (50 epochs per teacher) for the distillation process. The number of distillation steps was set to 5,000. The number of images per class (IPC) was set to 1, 10, and 50, respectively. For KIP [\[19\]](#page-4-14), we used their original 1024-width

Image /page/3/Figure/1 description: The image displays a grid of images, categorized by class. At the top, there is a row of sample images, each representing a different class: Plane, Car, Bird, Cat, Deer, Dog, Frog, Horse, Ship, and Truck. Below this, a larger grid is presented, with rows labeled by class (Plane, Car, Bird, Cat, Deer, Dog, Frog, Horse, Ship, Truck) and columns showing multiple images within each class. The grid is divided into two sections: the top section shows one image per class, and the bottom section shows ten images per class. The images themselves appear to be generated or representative examples of each category, with varying degrees of clarity and detail.

10 images per class

<span id="page-3-5"></span>Fig. 2. Visualization results of the distilled CIFAR-10 dataset.

<span id="page-3-4"></span>TABLE II TEST ACCURACY OF DIFFERENT WIDTH KIP [\[19\]](#page-4-14) AND OUR METHOD ON CIFAR-10 AND CIFAR-100.

|           | IPC | KIP-1024 | KIP-128 | Ours-128    |
|-----------|-----|----------|---------|-------------|
| CIFAR-10  | 1   | 49.9     | 38.3    | <b>46.4</b> |
|           | 10  | 62.7     | 57.6    | <b>65.5</b> |
|           | 50  | 68.6     | 65.8    | <b>71.9</b> |
| CIFAR-100 | 1   | 15.7     | 18.2    | <b>24.6</b> |
|           | 10  | 28.3     | 32.8    | <b>43.1</b> |
|           | 50  | -        | -       | <b>48.4</b> |

ConvNet (KIP-1024) and 128-width ConvNet (KIP-128) for a fair comparison. Furthermore, we used their custom ZCA implementation for distillation and evaluation.

Table [I](#page-2-0) shows that the proposed method outperformed the dataset selection methods and SOTA dataset distillation methods in all settings. Especially for CIFAR-100 with IPC = 10, our method increased accuracy by 3.0% compared to the second-best method MTT. As listed in Table [II,](#page-3-4) the proposed method drastically outperformed KIP using the same 128 width ConvNet. Even for KIP that uses 1024-width ConvNet, our method has higher accuracy except for CIFAR-10 with 1 image per class. For the results of CIFAR-100 with IPC = 50, KIP did not conduct experiments due to the large computational resources and time required; thus, we only report our results in this paper.

Figure [2](#page-3-5) shows the visualization results of the distilled CIFAR-10 dataset. As depicted in Fig. [2,](#page-3-5) when we set the number of distilled images to 1, the resulting images were not only more abstract but also more information-dense than the original images because all information about a class has to be compressed into only one image during the distillation process. Meanwhile, when the number of distilled images was set to 10, the resulting images were more realistic and contained

<span id="page-3-6"></span>TABLE III CROSS-ARCHITECTURE GENERALIZATION RESULTS ON CIFAR-10 DATASET WITH  $IPC = 10$ .

| Architecture | ConvNet                      | AlexNet                      | VGG11                        | ResNet18                     |
|--------------|------------------------------|------------------------------|------------------------------|------------------------------|
| Ours         | <b><math>65.4±0.4</math></b> | <b><math>35.8±1.3</math></b> | <b><math>52.9±0.9</math></b> | <b><math>51.8±1.1</math></b> |
| MTT [16]     | $64.3±0.7$                   | $34.2±2.6$                   | $50.3±0.8$                   | $46.4±0.6$                   |
| KIP [19]     | $47.6±0.9$                   | $24.4±3.9$                   | $42.1±0.4$                   | $36.8±1.0$                   |

various forms because discriminative features in a class can be compressed into multiple images during the distillation process. For example, we can see various types of dogs and different colored cars.

### C. Cross-Architecture Generalization

In this subsection, we verify the effectiveness of our method in cross-architecture generalization. A cross-architecture means using distilled images generated by one architecture and testing on other architectures. The distilled images were generated by ConvNet on CIFAR-10 and the number of distilled images was set to 10. We used the same pretrained teacher networks used in subsection 3.2 for rapid distillation and experimentation. For KIP, we used 128-width ConvNet and their custom ZCA implementation for distillation and evaluation. We also tested the accuracy of ConvNet and three cornerstone networks for the evaluation of cross-architecture generalization: AlexNet [\[21\]](#page-4-16), VGG11 [\[22\]](#page-4-17), and ResNet18 [\[23\]](#page-4-18).

Table [III](#page-3-6) shows that our method outperformed the SOTA methods MTT and KIP for all architectures. Especially for ResNet, our method increased accuracy by 5.2% compared with MTT. The results indicate that our method generated more robust distilled images than the other methods. By pruning difficult-to-match parameters in teacher and student networks, the proposed method can avoid the influence of these parameters on the distilled dataset, improving crossarchitecture generalization performance.

## IV. CONCLUSION

This study proposed a novel dataset distillation method based on parameter pruning. The proposed method can synthesize more robust distilled datasets by pruning difficult-tomatch parameters during the distillation process. The experimental results show that the proposed method outperforms other SOTA dataset distillation methods on two benchmark datasets and has better cross-architecture generalization performance.

## REFERENCES

- <span id="page-3-0"></span>[1] Weibo Liu, Zidong Wang, Xiaohui Liu, Nianyin Zeng, Yurong Liu, and Fuad E Alsaadi, "A survey of deep neural network architectures and their applications," *Neurocomputing*, vol. 234, pp. 11–26, 2017.
- <span id="page-3-1"></span>[2] Mingkang Xiong, Zhenghong Zhang, Tao Zhang, and Huilin Xiong, "Ld-net: A lightweight network for real-time self-supervised monocular depth estimation," *IEEE Signal Process. Lett.*, vol. 29, pp. 882–886, 2022.
- <span id="page-3-2"></span>[3] Olivier Bachem, Mario Lucic, and Andreas Krause, "Practical coreset constructions for machine learning," *arXiv:1703.06476*, 2017.
- <span id="page-3-3"></span>[4] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros, "Dataset distillation," *arXiv:1811.10959*, 2018.

- <span id="page-4-0"></span>[5] Tian Dong, Bo Zhao, and Lingjuan Liu, "Privacy for free: How does dataset condensation help privacy?," in Proc. Int. Conf. Mach. Learn., 2022, pp. 5378–5396.
- <span id="page-4-1"></span>[6] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama, "Compressed gastric image generation based on soft-label dataset distillation for medical data sharing," *Comput. Methods Programs Biomed.*, 2022.
- <span id="page-4-2"></span>[7] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama, "Soft-label anonymous gastric x-ray image distillation," in *Proc. IEEE Int. Conf. Image Process.*, 2020, pp. 305–309.
- <span id="page-4-3"></span>[8] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama, "Dataset distillation for medical dataset sharing," in *Proc. AAAI Conf. Artif. Intell., Workshop*, 2023, pp. 1–6.
- <span id="page-4-4"></span>[9] Felix Wiewel and Bin Yang, "Condensed composite memory continual learning," in *Proc. Int. Jt. Conf. Neural Netw.*, 2021, pp. 1–8.
- <span id="page-4-5"></span>[10] Bo Zhao and Hakan Bilen, "Dataset condensation with gradient matching," in *Proc. Int. Conf. Learn. Represent.*, 2021.
- <span id="page-4-6"></span>[11] Ruonan Yu, Songhua Liu, and Xinchao Wang, "A comprehensive survey to dataset distillation," *arXiv:2301.07014*, 2023.
- <span id="page-4-7"></span>[12] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales, "Flexible dataset distillation: Learn labels instead of images," in *Proc. Adv. Neural Inf. Process. Syst., Workshop*, 2020.
- <span id="page-4-8"></span>[13] Bo Zhao and Hakan Bilen, "Dataset condensation with differentiable siamese augmentation," in *Proc. Int. Conf. Mach. Learn.*, 2021, pp. 12674–12685.
- <span id="page-4-9"></span>[14] Bo Zhao and Hakan Bilen, "Dataset condensation with distribution matching," in *Proc. IEEE/CVF Wint. Conf. Appl. Comput. Vision*, 2023.
- <span id="page-4-10"></span>[15] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You, "CAFE: Learning to condense dataset by aligning features," in *Proc. IEEE/CVF Conf. Comput. Vision Pattern Recognit.*, 2022, pp. 12196–12205.
- <span id="page-4-11"></span>[16] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu, "Dataset distillation by matching training trajectories," in *Proc. IEEE/CVF Conf. Comput. Vision Pattern Recognit.*, 2022, pp. 4750–4759.
- <span id="page-4-12"></span>[17] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon, "An empirical study of example forgetting during deep neural network learning," in *Proc. Int. Conf. Learn. Represent.*, 2019.
- <span id="page-4-13"></span>[18] Yutian Chen, Max Welling, and Alex Smola, "Super-samples from kernel herding," in *Proc. Conf. Uncertainty Artif. Intell.*, 2010.
- <span id="page-4-14"></span>[19] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee, "Dataset distillation with infinitely wide convolutional networks," in *Proc. Adv. Neural Inf. Process. Syst.*, 2021, pp. 5186–5198.
- <span id="page-4-15"></span>[20] Spyros Gidaris and Nikos Komodakis, "Dynamic few-shot visual learning without forgetting," in *Proc. IEEE/CVF Conf. Comput. Vision Pattern Recognit.*, 2018, pp. 4367–4375.
- <span id="page-4-16"></span>[21] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton, "Imagenet classification with deep convolutional neural networks," in *Proc. Adv. Neural Inf. Process. Syst.*, 2012, pp. 1097–1105.
- <span id="page-4-17"></span>[22] Karen Simonyan and Andrew Zisserman, "Very deep convolutional networks for large-scale image recognition," *Proc. Int. Conf. Learn. Represent.*, 2015.
- <span id="page-4-18"></span>[23] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun, "Deep residual learning for image recognition," in *Proc. IEEE/CVF Conf. Comput. Vision Pattern Recognit.*, 2016, pp. 770–778.
# **4**

# **Entropic Regularization of Optimal Transport**

This chapter introduces a family of numerical schemes to approximate solutions to Ka<PERSON><PERSON>ich formulation of optimal transport and its many generalizations. It operates by adding an entropic regularization penalty to the original problem. This regularization has several important advantages, which make it, when taken altogether, a very useful tool: the minimization of the regularized problem can be solved using a simple alternate minimization scheme; that scheme translates into iterations that are simple matrix-vector products, making them particularly suited to execution of GPU; for some applications, these matrix-vector products do not require storing an  $n \times m$  cost matrix, but instead only require access to a kernel evaluation; in the case where a large group of measures share the same support, all of these matrix-vector products can be cast as matrix-matrix products with significant speedups; the resulting approximate distance is smooth with respect to input histogram weights and positions of the Diracs and can be differentiated using automatic differentiation.

# **4.1 Entropic Regularization**

The discrete entropy of a coupling matrix is defined as

$$
\mathbf{H}(\mathbf{P}) \stackrel{\text{def.}}{=} -\sum_{i,j} \mathbf{P}_{i,j} (\log(\mathbf{P}_{i,j}) - 1), \tag{4.1}
$$

with an analogous definition for vectors, with the convention that  $\mathbf{H}(\mathbf{a}) = -\infty$  if one of the entries  $\mathbf{a}_j$  is 0 or negative. The function **H** is 1-strongly concave, because its Hessian is  $\partial^2 \mathbf{H}(P) = -\text{diag}(1/\mathbf{P}_{i,j})$  and  $\mathbf{P}_{i,j} \leq 1$ . The idea of the entropic regularization of optimal transport is to use −**H** as a regularizing function to obtain approximate

solutions to the original transport problem (2.11):

$$
L_{\mathbf{C}}^{\varepsilon}(\mathbf{a},\mathbf{b}) \stackrel{\text{def.}}{=} \min_{\mathbf{P} \in \mathbf{U}(\mathbf{a},\mathbf{b})} \langle \mathbf{P}, \mathbf{C} \rangle - \varepsilon \mathbf{H}(\mathbf{P}). \tag{4.2}
$$

Since the objective is an  $\varepsilon$ -strongly convex function, Problem  $(4.2)$  has a unique optimal solution. The idea to regularize the optimal transport problem by an entropic term can be traced back to modeling ideas in transportation theory [Wilson, 1969]: Actual traffic patterns in a network do not agree with those predicted by the solution of the optimal transport problem. Indeed, the former are more diffuse than the latter, which tend to rely on a few routes as a result of the sparsity of optimal couplings for (2.11). To mitigate this sparsity, researchers in transportation proposed a model, called the "gravity" model [Erlander, 1980], that is able to form a more "blurred" prediction of traffic given marginals and transportation costs.

Figure 4.1 illustrates the effect of the entropy to regularize a linear program over the simplex  $\Sigma_3$  (which can thus be visualized as a triangle in two dimensions). Note how the entropy pushes the original LP solution away from the boundary of the triangle. The optimal  $P_{\varepsilon}$  progressively moves toward an "entropic center" of the triangle. This is further detailed in the proposition below. The convergence of the solution of that regularized problem toward an optimal solution of the original linear program has been studied by Cominetti and San Martín [1994], with precise asymptotics.

Image /page/1/Figure/5 description: The image displays a series of four triangular contour plots arranged horizontally. A black arrow labeled 'c' points towards the top-left corner of the first triangle. The triangles are colored with a gradient from blue on the left to red in the center and yellow on the right, with black contour lines indicating levels. A black dot is present in the bottom-left corner of the first triangle, and another black dot labeled 'Pε' is located within the second triangle. The x-axis is labeled with the Greek letter epsilon (ε) and has an arrow pointing to the right, indicating a progression across the plots.

**Figure 4.1:** Impact of  $\varepsilon$  on the optimization of a linear function on the simplex, solving  $P_{\varepsilon}$  =  $\operatorname{argmin}_{\mathbf{P}\in\Sigma_3} \langle \mathbf{C}, \, \mathbf{P} \rangle - \varepsilon \mathbf{H}(\mathbf{P})$  for a varying  $\varepsilon$ .

**Proposition 4.1** (Convergence with  $\varepsilon$ ). The unique solution  $P_{\varepsilon}$  of (4.2) converges to the optimal solution with maximal entropy within the set of all optimal solutions of the Kantorovich problem, namely

$$
\mathbf{P}_{\varepsilon} \stackrel{\varepsilon \to 0}{\longrightarrow} \underset{\mathbf{P}}{\text{argmin}} \ \{-\mathbf{H}(\mathbf{P}) \ : \ \mathbf{P} \in \mathbf{U}(\mathbf{a}, \mathbf{b}), \langle \mathbf{P}, \mathbf{C} \rangle = \mathbf{L}_{\mathbf{C}}(\mathbf{a}, \mathbf{b}), \}
$$
(4.3)

so that in particular

$$
\mathrm{L}^\varepsilon_{\mathbf{C}}(\mathbf{a},\mathbf{b})\stackrel{\varepsilon\to 0}{\longrightarrow} \mathrm{L}_{\mathbf{C}}(\mathbf{a},\mathbf{b}).
$$

One also has

$$
\mathbf{P}_{\varepsilon} \stackrel{\varepsilon \to \infty}{\longrightarrow} \mathbf{a} \otimes \mathbf{b} = \mathbf{a} \mathbf{b}^{\mathrm{T}} = (\mathbf{a}_{i} \mathbf{b}_{j})_{i,j}.
$$
 (4.4)

# 4.1. Entropic Regularization 59

*Proof.* We consider a sequence  $(\varepsilon_\ell)_\ell$  such that  $\varepsilon_\ell \to 0$  and  $\varepsilon_\ell > 0$ . We denote  $P_\ell$  the solution of (4.2) for  $\varepsilon = \varepsilon_{\ell}$ . Since  $U(a, b)$  is bounded, we can extract a sequence (that we do not relabel for the sake of simplicity) such that  $P_\ell \to P^*$ . Since  $U(a, b)$  is closed,  $P^* \in U(a, b)$ . We consider any **P** such that  $\langle C, P \rangle = L_C(a, b)$ . By optimality of **P** and  $P_\ell$  for their respective optimization problems (for  $\varepsilon = 0$  and  $\varepsilon = \varepsilon_\ell$ ), one has

$$
0 \le \langle \mathbf{C}, \mathbf{P}_{\ell} \rangle - \langle \mathbf{C}, \mathbf{P} \rangle \le \varepsilon_{\ell} (\mathbf{H}(\mathbf{P}_{\ell}) - \mathbf{H}(\mathbf{P})). \tag{4.5}
$$

Since **H** is continuous, taking the limit  $\ell \to +\infty$  in this expression shows that  $\langle C, P^* \rangle =$  $\langle \mathbf{C}, \mathbf{P} \rangle$  so that  $\mathbf{P}^*$  is a feasible point of (4.3). Furthermore, dividing by  $\varepsilon_\ell$  in (4.5) and taking the limit shows that  $\mathbf{H}(\mathbf{P}) \leq \mathbf{H}(\mathbf{P}^*)$ , which shows that  $\mathbf{P}^*$  is a solution of (4.3). Since the solution  $\mathbf{P}_0^*$  to this program is unique by strict convexity of  $-\mathbf{H}$ , one has  $\mathbf{P}^{\star} = \mathbf{P}_0^{\star}$ , and the whole sequence is converging. In the limit  $\varepsilon \to +\infty$ , a similar proof shows that one should rather consider the problem

$$
\min_{\mathbf{P}\in\mathbf{U}(\mathbf{a},\mathbf{b})}~- \mathbf{H}(\mathbf{P}),
$$

the solution of which is  $\mathbf{a} \otimes \mathbf{b}$ .

Formula (4.3) states that for a small regularization  $\varepsilon$ , the solution converges to the maximum entropy optimal transport coupling. In sharp contrast, (4.4) shows that for a large regularization, the solution converges to the coupling with maximal entropy between two prescribed marginals **a***,* **b**, namely the joint probability between two independent random variables distributed following **a***,* **b**. A refined analysis of this convergence is performed in Cominetti and San Martín [1994], including a first order expansion in  $\varepsilon$  (resp.,  $1/\varepsilon$ ) near  $\varepsilon = 0$  (resp.,  $\varepsilon = +\infty$ ). Figures 4.2 and 4.3 show visually the effect of these two convergences. A key insight is that, as  $\varepsilon$  increases, the optimal coupling becomes less and less sparse (in the sense of having entries larger than a prescribed threshold), which in turn has the effect of both accelerating computational algorithms (as we study in §4.2) and leading to faster statistical convergence (as shown in §8.5).

Defining the Kullback–Leibler divergence between couplings as

$$
\mathbf{KL}(\mathbf{P}|\mathbf{K}) \stackrel{\text{def.}}{=} \sum_{i,j} \mathbf{P}_{i,j} \log \left( \frac{\mathbf{P}_{i,j}}{\mathbf{K}_{i,j}} \right) - \mathbf{P}_{i,j} + \mathbf{K}_{i,j}, \tag{4.6}
$$

the unique solution  $P_{\varepsilon}$  of (4.2) is a projection onto  $U(a, b)$  of the Gibbs kernel associated to the cost matrix **C** as

$$
\mathbf{K}_{i,j} \stackrel{\text{def.}}{=} e^{-\frac{\mathbf{C}_{i,j}}{\varepsilon}}.
$$

Indeed one has that using the definition above

$$
\mathbf{P}_{\varepsilon} = \text{Proj}_{\mathbf{U}(\mathbf{a}, \mathbf{b})}^{\mathbf{KL}}(\mathbf{K}) \stackrel{\text{def.}}{=} \underset{\mathbf{P} \in \mathbf{U}(\mathbf{a}, \mathbf{b})}{\text{argmin}} \mathbf{KL}(\mathbf{P}|\mathbf{K}). \tag{4.7}
$$

 $\Box$ 

Image /page/3/Figure/1 description: The image displays four plots arranged in a 2x2 grid. The top row shows 2D heatmaps, with the first plot in magenta, the second in grayscale, and the third and fourth in green. The bottom row presents 3D surface plots corresponding to the heatmaps above, each with overlaid 1D curves in red and blue. The plots are labeled with values of epsilon: \u03b5 = 10, \u03b5 = 1, \u03b5 = 10^-1, and \u03b5 = 10^-2, from left to right.

**Figure 4.2:** Impact of *ε* on the couplings between two 1-D densities, illustrating Proposition 4.1. Top row: between two 1-D densities. Bottom row: between two 2-D discrete empirical densities with the same number  $n = m$  of points (only entries of the optimal  $(\mathbf{P}_{i,j})_{i,j}$  above a small threshold are displayed as segments between  $x_i$  and  $y_j$ ).

Image /page/3/Figure/3 description: The image displays four network graphs arranged horizontally, with a black arrow labeled "Epsilon" pointing from left to right below them. Each graph consists of blue and red dots connected by black lines, representing nodes and edges. The graphs show an increasing density of connections from left to right. The leftmost graph has the fewest connections, while the rightmost graph is densely interconnected. The labels "alpha" in red and "beta" in blue are visible near the top left of the first graph.

**Figure 4.3:** Impact of  $\varepsilon$  on coupling between two 2-D discrete empirical densities with the same number  $n = m$  of points (only entries of the optimal  $(\mathbf{P}_{i,j})_{i,j}$  above a small threshold are displayed as segments between  $x_i$  and  $y_j$ ).

**Remark 4.1** (Entropic regularization between discrete measures)**.** For discrete measures of the form (2.1), the definition of regularized transport extends naturally to

$$
\mathcal{L}_{c}^{\varepsilon}(\alpha,\beta) \stackrel{\text{def.}}{=} \mathcal{L}_{\mathbf{C}}(\mathbf{a},\mathbf{b}),\tag{4.8}
$$

with cost  $\mathbf{C}_{i,j} = c(x_i, y_j)$ , to emphasize the dependency with respect to the positions  $(x_i, y_j)$  supporting the input measures.

**Remark 4.2** (General formulation)**.** One can consider arbitrary measures by replacing the discrete entropy by the relative entropy with respect to the product measure  $d\alpha \otimes d\beta(x, y) \stackrel{\text{def.}}{=} d\alpha(x) d\beta(y)$ , and propose a regularized counterpart to (2.15) using

$$
\mathcal{L}_{c}^{\varepsilon}(\alpha,\beta) \stackrel{\text{def.}}{=} \min_{\pi \in \mathcal{U}(\alpha,\beta)} \int_{\mathcal{X} \times \mathcal{Y}} c(x,y) d\pi(x,y) + \varepsilon \operatorname{KL}(\pi|\alpha \otimes \beta), \tag{4.9}
$$

where the relative entropy is a generalization of the discrete Kullback–Leibler divergence (4.6)

KL(π|ξ) =  $≖$   $\int_{\mathcal{X}\times\mathcal{Y}} \log\left(\frac{dπ}{dξ}(x,y)\right) dπ(x,y)$  +  $\int_{\mathcal{X}\times\mathcal{Y}} (dξ(x,y) - dπ(x,y)),$ 

(4.10)

and by convention  $KL(\pi|\xi) = +\infty$  if  $\pi$  does not have a density  $\frac{d\pi}{d\xi}$  with respect to *ξ*. It is important to realize that the reference measure  $\alpha \otimes \beta$  chosen in (4.9) to define the entropic regularizing term  $KL(\cdot|\alpha \otimes \beta)$  plays no specific role; only its support matters, as noted by the following proposition.

**Proposition 4.2.** For any  $\pi \in \mathcal{U}(\alpha,\beta)$ , and for any  $(\alpha',\beta')$  having the same 0 measure sets as  $(\alpha, \beta)$  (so that they have both densities with respect to one another) one has

$$
KL(\pi|\alpha \otimes \beta) = KL(\pi|\alpha' \otimes \beta') - KL(\alpha \otimes \beta|\alpha' \otimes \beta').
$$

This proposition shows that choosing  $KL(\cdot|\alpha' \otimes \beta')$  in place of  $KL(\cdot|\alpha \otimes \beta)$ in (4.9) results in the same solution.

Formula (4.9) can be refactored as a projection problem

$$
\min_{\pi \in \mathcal{U}(\alpha,\beta)} \mathrm{KL}(\pi|\mathcal{K}),\tag{4.11}
$$

where K is the Gibbs distributions  $d\mathcal{K}(x, y) \stackrel{\text{def.}}{=} e^{-\frac{c(x, y)}{\varepsilon}} d\alpha(x) d\beta(y)$ . This problem is often referred to as the "static Schrödinger problem" [Léonard, 2014, Rüschendorf and Thomsen, 1998], since it was initially considered by Schrödinger in statistical physics [Schrödinger, 1931]. As  $\varepsilon \to 0$ , the unique solution to (4.11) converges to the maximum entropy solution to (2.15); see [Léonard, 2012, Carlier et al., 2017]. Section 7.6 details an alternate "dynamic" formulation of the Schrödinger problem over the space of paths connecting the points of two measures.

**Remark 4.3** (Mutual entropy)**.** Similarly to (2.16), one can rephrase (4.9) using

random variables

$$
\mathcal{L}_{c}^{\varepsilon}(\alpha,\beta) = \min_{(X,Y)} \left\{ \mathbb{E}_{(X,Y)}(c(X,Y)) + \varepsilon I(X,Y) : X \sim \alpha, Y \sim \beta \right\},\,
$$

where, denoting  $\pi$  the distribution of  $(X, Y)$ ,  $I(X, Y) \stackrel{\text{def.}}{=} KL(\pi | \alpha \otimes \beta)$  is the socalled mutual information between the two random variables. One has  $I(X, Y) \geq 0$ and  $I(X, Y) = 0$  if and only if the two random variables are independent.

**Remark 4.4** (Independence and couplings). A coupling  $\pi \in \mathcal{U}(\alpha, \beta)$  describes the distribution of a couple of random variables  $(X, Y)$  defined on  $(X, Y)$ , where X  $(\text{resp.}, Y)$  has law  $\alpha$  (resp.,  $\beta$ ). Proposition 4.1 carries over for generic (nonnecessary discrete) measures, so that the solution  $\pi_{\varepsilon}$  of (4.9) converges to the tensor product coupling  $\alpha \otimes \beta$  as  $\varepsilon \to +\infty$ . This coupling  $\alpha \otimes \beta$  corresponds to the random variables  $(X, Y)$  being independent. In contrast, as  $\varepsilon \to 0$ ,  $\pi_{\varepsilon}$  convergence to a solution  $\pi_0$  of the OT problem (2.15). On  $\mathcal{X} = \mathcal{Y} = \mathbb{R}^d$ , if  $\alpha$  and  $\beta$  have densities with respect to the Lebesgue measure, as detailed in Remark 2.24, then  $\pi_0$  is unique and supported on the graph of a bijective Monge map  $T : \mathbb{R}^d \to \mathbb{R}^d$ . In this case,  $(X, Y)$  are in some sense fully dependent, since  $Y = T(X)$  and  $X = T^{-1}(Y)$ . In the simple 1-D case  $d = 1$ , a convenient way to visualize the dependency structure between X and *Y* is to use the copula  $\xi_{\pi}$  associated to the joint distribution  $\pi$ . The cumulative function defined in (2.34) is extended to couplings as

$$
\forall (x, y) \in \mathbb{R}^2, \quad \mathcal{C}_{\pi}(x, y) \stackrel{\text{def.}}{=} \int_{-\infty}^x \int_{-\infty}^y d\pi.
$$

The copula is then defined as

$$
\forall (s,t) \in [0,1]^2, \quad \xi_\pi(s,t) \stackrel{\scriptscriptstyle\rm def.}{=} \mathcal{C}_\pi(\mathcal{C}_\alpha^{-1}(s), \mathcal{C}_\beta^{-1}(t)),
$$

where the pseudoinverse of a cumulative function is defined in (2.35). For independent variables,  $\varepsilon = +\infty$ , *i.e.*  $\pi = \alpha \otimes \beta$ , one has  $\xi_{\pi+\infty}(s,t) = st$ . In contrast, for fully dependent variables,  $\varepsilon = +\infty$ , one has  $\xi_{\pi_0}(s,t) = \min(s,t)$ . Figure 4.4 shows how entropic regularization generates copula  $\xi_{\pi_{\varepsilon}}$  interpolating between these two extreme cases.

# **4.2 Sinkhorn's Algorithm and Its Convergence**

The following proposition shows that the solution of  $(4.2)$  has a specific form, which can be parameterized using  $n + m$  variables. That parameterization is therefore essentially dual, in the sense that a coupling **P** in  $U(a, b)$  has *nm* variables but  $n + m$  constraints.

Image /page/6/Figure/1 description: The image displays a figure with two rows and five columns of plots. The top row shows two plots, labeled 'α' and 'β' respectively. The 'α' plot is a red curve with two peaks, while the 'β' plot is a blue bell-shaped curve. The second row contains five plots, each with contour lines. The first two plots in the second row are colored in shades of purple and show a double-peaked distribution. The remaining three plots are colored in shades of green and show a distribution that appears to be concentrated along a diagonal line. Below each plot in the second row, there is a label indicating a value of epsilon (ε): ε = 10, ε = 1, ε = 0.5 · 10−1, ε = 10−1, and ε = 10−3, from left to right. The bottom row also contains five plots, each with contour lines and a gradient fill. The first two plots are filled with purple gradients, and the last three are filled with green gradients. These plots appear to represent the same distributions as the ones above them, but with a different visualization style.

**Figure 4.4:** Top: evolution with  $\varepsilon$  of the solution  $\pi_{\varepsilon}$  of (4.9). Bottom: evolution of the copula function *ξ<sup>π</sup><sup>ε</sup>* .

**Proposition 4.3.** The solution to  $(4.2)$  is unique and has the form

$$
\forall (i,j) \in [\![n]\!] \times [\![m]\!], \quad \mathbf{P}_{i,j} = \mathbf{u}_i \mathbf{K}_{i,j} \mathbf{v}_j \tag{4.12}
$$

for two (unknown) scaling variable  $(\mathbf{u}, \mathbf{v}) \in \mathbb{R}_+^n \times \mathbb{R}_+^m$ .

*Proof.* Introducing two dual variables  $f \in \mathbb{R}^n$ ,  $g \in \mathbb{R}^m$  for each marginal constraint, the Lagrangian of (4.2) reads

$$
\mathcal{E}(\mathbf{P}, \mathbf{f}, \mathbf{g}) = \langle \mathbf{P}, \mathbf{C} \rangle - \varepsilon \mathbf{H}(\mathbf{P}) - \langle \mathbf{f}, \mathbf{P} \mathbb{1}_m - \mathbf{a} \rangle - \langle \mathbf{g}, \mathbf{P}^{\mathrm{T}} \mathbb{1}_n - \mathbf{b} \rangle.
$$

First order conditions then yield

$$
\frac{\partial \mathcal{E}(\mathbf{P}, \mathbf{f}, \mathbf{g})}{\partial \mathbf{P}_{i,j}} = \mathbf{C}_{i,j} + \varepsilon \log(\mathbf{P}_{i,j}) - \mathbf{f}_i - \mathbf{g}_j = 0,
$$

which result, for an optimal **P** coupling to the regularized problem, in the expression  ${\bf P}_{i,j} = e^{{\bf f}_i/\varepsilon}e^{-{\bf C}_{i,j}/\varepsilon}e^{{\bf g}_j/\varepsilon}$ , which can be rewritten in the form provided above using nonnegative vectors **u** and **v**.  $\Box$ 

**Regularized OT as matrix scaling.** The factorization of the optimal solution exhibited in Equation (4.12) can be conveniently rewritten in matrix form as  $P =$ 

diag( $\mathbf{u}$ )**K** diag( $\mathbf{v}$ ). The variables ( $\mathbf{u}, \mathbf{v}$ ) must therefore satisfy the following nonlinear equations which correspond to the mass conservation constraints inherent to  $U(a, b)$ :

$$
diag(\mathbf{u})\mathbf{K} \operatorname{diag}(\mathbf{v})\mathbb{1}_m = \mathbf{a}, \quad \text{and} \quad \operatorname{diag}(\mathbf{v})\mathbf{K}^\top \operatorname{diag}(\mathbf{u})\mathbb{1}_n = \mathbf{b}.
$$
 (4.13)

These two equations can be further simplified, since  $diag(\mathbf{v})\mathbb{1}_m$  is simply **v**, and the multiplication of diag( $\mathbf{u}$ ) times  $\mathbf{K}\mathbf{v}$  is

$$
\mathbf{u} \odot (\mathbf{K} \mathbf{v}) = \mathbf{a} \quad \text{and} \quad \mathbf{v} \odot (\mathbf{K}^{\mathrm{T}} \mathbf{u}) = \mathbf{b}, \tag{4.14}
$$

where  $\odot$  corresponds to entrywise multiplication of vectors. That problem is known in the numerical analysis community as the matrix scaling problem (see [Nemirovski and Rothblum, 1999] and references therein). An intuitive way to handle these equations is to solve them iteratively, by modifying first **u** so that it satisfies the left-hand side of Equation (4.14) and then **v** to satisfy its right-hand side. These two updates define Sinkhorn's algorithm,

$$
\mathbf{u}^{(\ell+1)} \stackrel{\text{def}}{=} \frac{\mathbf{a}}{\mathbf{K} \mathbf{v}^{(\ell)}} \quad \text{and} \quad \mathbf{v}^{(\ell+1)} \stackrel{\text{def}}{=} \frac{\mathbf{b}}{\mathbf{K}^{\mathrm{T}} \mathbf{u}^{(\ell+1)}},\tag{4.15}
$$

initialized with an arbitrary positive vector  $\mathbf{v}^{(0)} = \mathbb{1}_m$ . The division operator used above between two vectors is to be understood entrywise. Note that a different initialization will likely lead to a different solution for **u***,* **v**, since **u***,* **v** are only defined up to a multiplicative constant (if **u**, **v** satisfy (4.13) then so do  $\lambda$ **u**, **v**/ $\lambda$  for any  $\lambda > 0$ ). It turns out, however, that these iterations converge (see Remark 4.8 for a justification using iterative projections, and see Remark 4.14 for a strict contraction result) and all result in the same optimal coupling diag( $\mathbf{u}$ )**K** diag( $\mathbf{v}$ ). Figure 4.5, top row, shows the evolution of the coupling  $diag(\mathbf{u}^{(\ell)})\mathbf{K}$  diag( $\mathbf{v}^{(\ell)}$ ) computed by Sinkhorn iterations. It evolves from the Gibbs kernel **K** toward the optimal coupling solving  $(4.2)$  by progressively shifting the mass away from the diagonal.

**Remark 4.5** (Historical perspective)**.** The iterations (4.15) first appeared in [Yule, 1912, Kruithof, 1937]. They were later known as the iterative proportional fitting procedure (IPFP) Deming and Stephan [1940] and RAS [Bacharach, 1965] methods [Idel, 2016]. The proof of their convergence is attributed to Sinkhorn [1964], hence the name of the algorithm. This algorithm was later extended in infinite dimensions by Ruschendorf [1995]. This regularization was used in the field of economics to obtain approximate solutions to optimal transport problems, under the name of gravity models [Wilson, 1969, Erlander, 1980, Erlander and Stewart, 1990]. It was rebranded as "softassign" by Kosowsky and Yuille [1994] in the assignment case, namely when  $\mathbf{a} = \mathbf{b} = \mathbb{1}_n/n$ , and used to solve matching problems in economics more recently by Galichon and Salanié [2009]. This regularization has received renewed attention in data sciences (including machine learning, vision, graphics and imaging) following [Cuturi, 2013], who

# 4.2. Sinkhorn's Algorithm and Its Convergence 65

showed that Sinkhorn's algorithm provides an efficient and scalable approximation to optimal transport, thanks to seamless parallelization when solving several OT problems simultaneously (notably on GPUs; see Remark 4.16), and that this regularized quantity also defines, unlike the linear programming formulation, a differentiable loss function (see §4.5). There exist countless extensions and generalizations of the Sinkhorn algorithm (see for instance  $\S 4.6$ ). For instance, when  $\mathbf{a} = \mathbf{b}$ , one can use averaged projection iterations to maintain symmetry [Knight et al., 2014].

Image /page/8/Figure/2 description: The image displays two plots. The top plot shows a series of six square matrices, each with a diagonal line and a gradient that changes across the series. The label "π(l)ε" is present in the first matrix. An arrow labeled "l" points to the right below these matrices, indicating the x-axis. The bottom plot is a line graph with the x-axis labeled from 0 to 5000 in increments of 1000. The y-axis is labeled from -2 to 0 in increments of 0.5. Three distinct colored lines (blue, purple, and red) are plotted, representing different values of epsilon: ε = 10 (blue), ε = 0.1 (purple), and ε = 10⁻³ (red). The lines show a rapid decrease from 0 on the y-axis as the x-value increases, with the blue line decreasing the fastest and the red line the slowest.

**Figure 4.5:** Top: evolution of the coupling  $\pi_{\varepsilon}^{(\ell)} = \text{diag}(\mathbf{u}^{(\ell)})\mathbf{K} \text{diag}(\mathbf{v}^{(\ell)})$  computed at iteration  $\ell$ of Sinkhorn's iterations, for 1-D densities on  $\mathcal{X} = [0,1], c(x,y) = |x-y|^2$ , and  $\varepsilon = 0.1$ . Bottom: impact of *ε* the convergence rate of Sinkhorn, as measured in term of marginal constraint violation  $\log(\|\pi_\varepsilon^{(\ell)}\mathbb{1}_m - \mathbf{b}\|_1).$ 

**Remark 4.6** (Overall complexity)**.** By doing a careful convergence analysis (assuming  $n = m$  for the sake of simplicity), Altschuler et al. [2017] showed that by setting  $\varepsilon = \frac{4 \log(n)}{\tau}$  $\frac{g(n)}{\tau}$ ,  $O(||\mathbf{C}||^3_{\infty} \log(n) \tau^{-3})$  Sinkhorn iterations (with an additional rounding step to compute a valid coupling  $\hat{\mathbf{P}} \in \mathbf{U}(\mathbf{a}, \mathbf{b})$  are enough to ensure that  $\langle \hat{\mathbf{P}}, \mathbf{C} \rangle \leq L_{\mathbf{C}}(\mathbf{a}, \mathbf{b}) + \tau$ . This implies that Sinkhorn computes a  $\tau$ -approximate solution of the unregularized OT problem in  $O(n^2 \log(n) \tau^{-3})$  operations. The rounding scheme consists in, given two vectors  $\mathbf{u} \in \mathbb{R}^n$ ,  $\mathbf{v} \in \mathbb{R}^m$  to carry out the following

updates ([Altschuler et al., 2017, Alg. 2]):

$$
\mathbf{u}' \stackrel{\text{def.}}{=} \mathbf{u} \odot \min \left( \frac{\mathbf{a}}{\mathbf{u} \odot (\mathbf{K} \mathbf{v})}, \mathbb{1}_n \right), \mathbf{v}' \stackrel{\text{def.}}{=} \mathbf{v} \odot \min \left( \frac{\mathbf{b}}{\mathbf{v} \odot (\mathbf{K}^{\mathrm{T}} \mathbf{u}')}, \mathbb{1}_n \right),
$$
  
$$
\Delta_{\mathbf{a}} \stackrel{\text{def.}}{=} \mathbf{a} - \mathbf{u}' \odot (\mathbf{K} \mathbf{v}'), \Delta_{\mathbf{b}} \stackrel{\text{def.}}{=} \mathbf{b} - \mathbf{v}' \odot (\mathbf{K}^{\mathrm{T}} \mathbf{u}),
$$
  
$$
\hat{\mathbf{P}} \stackrel{\text{def.}}{=} \text{diag}(\mathbf{u}') \mathbf{K} \text{diag}(\mathbf{v}') + \Delta_{\mathbf{a}} (\Delta_{\mathbf{b}})^{\mathrm{T}} / ||\Delta_{\mathbf{a}}||_1.
$$

This yields a matrix  $\hat{\mathbf{P}} \in \mathbf{U}(\mathbf{a}, \mathbf{b})$  such that the 1-norm between  $\hat{\mathbf{P}}$  and  $diag(\mathbf{u})\mathbf{K}$  diag( $\mathbf{v}$ ) is controlled by the marginal violations of diag( $\mathbf{u}$ )**K** diag( $\mathbf{v}$ ), namely

$$
\left\|\hat{\mathbf{P}} - \text{diag}(\mathbf{u})\mathbf{K} \text{ diag}(\mathbf{v})\right\|_{1} \leq \|\mathbf{a} - \mathbf{u} \odot (\mathbf{K} \mathbf{v})\|_{1} + \left\|\mathbf{b} - \mathbf{v} \odot (\mathbf{K}^{T} \mathbf{u})\right\|_{1}.
$$

This field remains active, as shown by the recent improvement on the result above by Dvurechensky et al. [2018].

**Remark 4.7** (Numerical stability of Sinkhorn iterations)**.** As we discuss in Remarks 4.14 and 4.15, the convergence of Sinkhorn's algorithm deteriorates as  $\varepsilon \to 0$ . In numerical practice, however, that slowdown is rarely observed in practice for a simpler reason: Sinkhorn's algorithm will often fail to terminate as soon as some of the elements of the kernel **K** become too negligible to be stored in memory as positive numbers, and become instead null. This can then result in a matrix product  $\mathbf{K} \mathbf{v}$  or  $\mathbf{K}^T \mathbf{u}$  with ever smaller entries that become null and result in a division by 0 in the Sinkhorn update of Equation (4.15). Such issues can be partly resolved by carrying out computations on the multipliers **u** and **v** in the log domain. That approach is carefully presented in Remark 4.23 and is related to a direct resolution of the dual of Problem (4.2).

**Remark 4.8** (Relation with iterative projections)**.** Denoting

$$
\mathcal{C}_{\mathbf{a}}^1 \stackrel{\text{def.}}{=} \{\mathbf{P} \,:\, \mathbf{P} \mathbb{1}_m = \mathbf{a}\} \quad \text{and} \quad \mathcal{C}_{\mathbf{b}}^2 \stackrel{\text{def.}}{=} \left\{\mathbf{P} \,:\, \mathbf{P}^T \mathbb{1}_m = \mathbf{b}\right\}
$$

the rows and columns constraints, one has  $U(a, b) = C_a^1 \cap C_b^2$ . One can use Bregman iterative projections [Bregman, 1967],

$$
\mathbf{P}^{(\ell+1)} \stackrel{\text{def.}}{=} \text{Proj}_{\mathcal{C}_{\mathbf{a}}^1}^{\mathbf{KL}}(\mathbf{P}^{(\ell)}) \quad \text{and} \quad \mathbf{P}^{(\ell+2)} \stackrel{\text{def.}}{=} \text{Proj}_{\mathcal{C}_{\mathbf{b}}^2}^{\mathbf{KL}}(\mathbf{P}^{(\ell+1)}). \tag{4.16}
$$

Since the sets  $C_{\mathbf{a}}^1$  and  $C_{\mathbf{b}}^2$  are affine, these iterations are known to converge to the solution of (4.7); see [Bregman, 1967]. These iterates are equivalent to Sinkhorn iterations (4.15) since defining

$$
\mathbf{P}^{(2\ell)} \stackrel{\text{\tiny def.}}{=} \text{diag}(\mathbf{u}^{(\ell)}) \mathbf{K} \, \text{diag}(\mathbf{v}^{(\ell)}),
$$

one has

$$
\mathbf{P}^{(2\ell+1)} \stackrel{\text{def.}}{=} \text{diag}(\mathbf{u}^{(\ell+1)}) \mathbf{K} \text{ diag}(\mathbf{v}^{(\ell)})
$$
  
and 
$$
\mathbf{P}^{(2\ell+2)} \stackrel{\text{def.}}{=} \text{diag}(\mathbf{u}^{(\ell+1)}) \mathbf{K} \text{ diag}(\mathbf{v}^{(\ell+1)}).
$$

66

In practice, however, one should prefer using (4.15), which only requires manipulating scaling vectors and multiplication against a Gibbs kernel, which can often be accelerated (see Remarks 4.17 and 4.19 below).

**Remark 4.9** (Proximal point algorithm)**.** In order to approximate a solution of the unregularized  $(\varepsilon = 0)$  problem (2.11), it is possible to use iteratively the Sinkhorn algorithm, using the so-called proximal point algorithm for the KL metric. We denote  $F(\mathbf{P}) \stackrel{\text{def.}}{=} \langle \mathbf{P}, \pi \rangle + \iota_{\mathbf{U}(\mathbf{a}, \mathbf{b})}(\mathbf{P})$  the unregularized objective function. The proximal point iterations for the **KL** divergence computes a minimizer of *F*, and hence a solution of the unregularized OT problem (2.11), by computing iteratively

$$
\mathbf{P}^{(\ell+1)} \stackrel{\text{def.}}{=} \text{Prox}_{\frac{1}{\varepsilon}F}^{\mathbf{KL}}(\mathbf{P}^{(\ell)}) \stackrel{\text{def.}}{=} \operatorname*{argmin}_{\mathbf{P} \in \mathbb{R}^{n \times m}_{+}} \mathbf{KL}(\mathbf{P}|\mathbf{P}^{(\ell)}) + \frac{1}{\varepsilon}F(\mathbf{P}) \tag{4.17}
$$

starting from an arbitrary  $P^{(0)}$  (see also  $(4.52)$ ). The proximal point algorithm is the most basic proximal splitting method. Initially introduced for the Euclidean metric (see, for instance, (Rockafellar 1976)), it extends to any Bregman divergence [Censor and Zenios, 1992], so in particular it can be applied here for the **KL** divergence (see Remark 8.1). The proximal operator is usually not available in closed form, so some form of subiterations are required. The optimization appearing in (4.17) is very similar to the entropy regularized problem (4.2), with the relative entropy  $KL(\cdot|P^{(\ell)})$  used in place of the negative entropy −**H**. Proposition 4.3 and Sinkhorn iterations (4.15) carry over to this more general setting when defining the Gibbs kernel as  $\mathbf{K} = e^{-\frac{\mathbf{C}}{\varepsilon}} \odot \mathbf{P}^{(\ell)}$  $(e^{-\frac{\mathbf{C}_{i,j}}{\varepsilon}} \mathbf{P}_{i,j}^{(\ell)})_{i,j}$ . Iterations (4.17) can thus be implemented by running the Sinkhorn algorithm at each iteration. Assuming for simplicity  $\mathbf{P}^{(0)} = \mathbb{1}_n \mathbb{1}_m^{\top}$ , these iterations thus have the form

$$
P^{(\ell+1)} = \text{diag}(\mathbf{u}^{(\ell)})(e^{-\frac{\mathbf{C}}{\varepsilon}} \odot \mathbf{P}^{(\ell)}) \text{ diag}(\mathbf{v}^{(\ell)})
$$
  
= diag( $\mathbf{u}^{(\ell)} \odot \cdots \odot \mathbf{u}^{(0)})e^{-\frac{(\ell+1)\mathbf{C}}{\varepsilon}} \odot \mathbf{P}^{(\ell)}$ ) diag( $\mathbf{v}^{(\ell)} \odot \cdots \odot \mathbf{v}^{(0)}$ ).

The proximal point iterates apply therefore iteratively Sinkhorn's algorithm with a kernel  $e^{-\frac{\mathbf{C}}{\varepsilon/\ell}}$ , i.e., with a decaying regularization parameter  $\varepsilon/\ell$ . This method is thus tightly connected to a series of works which combine Sinkhorn with some decaying schedule on the regularization; see, for instance, [Kosowsky and Yuille, 1994]. They are efficient in small spacial dimension, when combined with a multigrid strategy to approximate the coupling on an adaptive sparse grid [Schmitzer, 2016b].

**Remark 4.10** (Other regularizations)**.** It is possible to replace the entropic term −**H**(**P**) in (4.2) by any strictly convex penalty  $R(\mathbf{P})$ , as detailed, for instance, in [Dessein et al., 2018]. A typical example is the squared  $\ell^2$  norm

$$
R(\mathbf{P}) = \sum_{i,j} \mathbf{P}_{i,j}^2 + \iota_{\mathbb{R}_+}(\mathbf{P}_{i,j});
$$
\n(4.18)

see [Essid and Solomon, 2017]. Another example is the family of Tsallis entropies [Muzellec et al., 2017]. Note, however, that if the penalty function is defined even when entries of **P** are nonpositive, which is, for instance, the case for a quadratic regularization (4.18), then one must add back a nonnegativity constraint  $P \geq 0$ , in addition to the marginal constraints  $P\mathbb{1}_m = \mathbf{a}$  and  $P^{\top} \mathbb{1}_n = \mathbf{b}$ . Indeed, one can afford to ignore the nonnegativity constraint using entropy because that penalty incorporates a logarithmic term which forces the entries of **P** to stay in the positive orthant. This implies that the set of constraints is no longer affine and iterative Bregman projections do not converge anymore to the solution. A workaround is to use instead Dykstra's algorithm (1983, 1985) (see also Bauschke and Lewis 2000), as detailed in [Benamou et al., 2015]. This algorithm uses projections according to the Bregman divergence associated to *R*. We refer to Remark 8.1 for more details regarding Bregman divergences. An issue is that in general these projections cannot be computed explicitly. For the squared norm (4.18), this corresponds to computing the Euclidean projection on  $(\mathcal{C}_{a}^{1}, \mathcal{C}_{b}^{2})$  (with the extra positivity constraints), which can be solved efficiently using projection algorithms on simplices [Condat, 2015]. The main advantage of the quadratic regularization over entropy is that it produces sparse approximation of the optimal coupling, yet this comes at the expense of a slower algorithm that cannot be parallelized as efficiently as Sinkhorn to compute several optimal transports simultaneously (as discussed in §4.16). Figure 4.6 contrasts the approximation achieved by entropic and quadratic regularizers.

Image /page/11/Figure/2 description: The image displays a grid of ten contour plots, arranged in two rows of five plots each. Each plot is labeled with a value of epsilon (ε). The top row shows plots for ε = 10, ε = 1, ε = 0.5 · 10−1, ε = 10−1, and ε = 10−3. The bottom row shows plots for ε = 5 · 103, ε = 103, ε = 102, ε = 10, and ε = 1. The plots illustrate how the contour patterns change with varying values of ε, transitioning from a bimodal distribution in the top left to a more elongated, winding shape in the bottom right.

**Figure 4.6:** Comparison of entropic regularization  $R = -H$  (top row) and quadratic regularization  $R = ||\cdot||^2 + \iota_{\mathbb{R}_+}$  (bottom row). The  $(\alpha, \beta)$  marginals are the same as for Figure 4.4.

## 4.2. Sinkhorn's Algorithm and Its Convergence 69

**Remark 4.11** (Barycentric projection)**.** Consider again the setting of Remark 4.1 in which we use entropic regularization to approximate OT between discrete measures. The Kantorovich formulation in (2.11) and its entropic regularization (4.2) both yield a coupling  $P \in U(a, b)$ . In order to define a transportation map  $T : \mathcal{X} \to \mathcal{Y}$ , in the case where  $\mathcal{Y} = \mathbb{R}^d$ , one can define the so-called barycentric projection map

$$
T: x_i \in \mathcal{X} \longmapsto \frac{1}{\mathbf{a}_i} \sum_j \mathbf{P}_{i,j} y_j \in \mathcal{Y},\tag{4.19}
$$

where the input measures are discrete of the form (2.3). Note that this map is only defined for points  $(x_i)_i$  in the support of  $\alpha$ . In the case where T is a permutation matrix (as detailed in Proposition 2.1), then *T* is equal to a Monge map, and as  $\varepsilon \to 0$ , the barycentric projection progressively converges to that map if it is unique. For arbitrary (not necessarily discrete) measures, solving (2.15) or its regularized version (4.9) defines a coupling  $\pi \in \mathcal{U}(\alpha, \beta)$ . Note that this coupling  $\pi$  always has a density  $\frac{d\pi(x,y)}{d\alpha(x)d\beta(y)}$  with respect to  $\alpha \otimes \beta$ . A map can thus be retrieved by the formula

$$
T: x \in \mathcal{X} \longmapsto \int_{\mathcal{Y}} y \frac{d\pi(x, y)}{d\alpha(x) d\beta(y)} d\beta(y).
$$
 (4.20)

In the case where, for  $\varepsilon = 0$ ,  $\pi$  is supported on the graph of the Monge map (see Remark 2.24), then using  $\varepsilon > 0$  produces a smooth approximation of this map. Such a barycentric projection is useful to apply the OT Monge map to solve problems in imaging; see Figure 9.6 for an application to color modification. It has also been used to compute approximations of principal geodesics in the space of probability measures endowed with the Wasserstein metric; see [Seguy and Cuturi, 2015].

**Remark 4.12** (Hilbert metric)**.** As initially explained by [Franklin and Lorenz, 1989], the global convergence analysis of Sinkhorn is greatly simplified using the Hilbert projective metric on  $\mathbb{R}^n_{+,*}$  (positive vectors), defined as

$$
\forall \left(\mathbf{u},\mathbf{u}'\right) \in (\mathbb{R}^n_{+,*})^2, \quad d_{\mathcal{H}}(\mathbf{u},\mathbf{u}') \stackrel{\text{\tiny def.}}{=} \log \max_{i,j} \frac{\mathbf{u}_i \mathbf{u}'_j}{\mathbf{u}_j \mathbf{u}'_i}.
$$

It can be shown to be a distance on the projective cone  $\mathbb{R}^n_{+, *}/\sim$ , where  $\mathbf{u} \sim \mathbf{u}'$ means that  $\exists r > 0, \mathbf{u} = r\mathbf{u}'$  (the vectors are equal up to rescaling, hence the name "projective"). This means that  $d_{\mathcal{H}}$  satisfies the triangular inequality and  $d_{\mathcal{H}}(\mathbf{u}, \mathbf{u}') = 0$  if and only if  $\mathbf{u} \sim \mathbf{u}'$ . This is a projective version of Hilbert's original distance on bounded open convex sets [Hilbert, 1895]. The projective cone  $\mathbb{R}^n_{+, *}/\sim$ is a complete metric space for this distance. By a logarithmic change of variables, the Hilbert metric on the rays of the positive cone is isometric to the variation seminorm (it is a norm between vectors that are defined up to an additive constant)

$$
d_{\mathcal{H}}(\mathbf{u}, \mathbf{u}') = ||\log(\mathbf{u}) - \log(\mathbf{u}')||_{var}
$$
\n(4.21)

where  $\| \mathbf{f} \|_{var} \stackrel{\text{def.}}{=} (\max_i \mathbf{f}_i) - (\min_i \mathbf{f}_i).$ 

This variation seminorm is closely related to the  $\ell^{\infty}$  norm since one always has  $\|\mathbf{f}\|_{\text{var}} \leq 2 \|\mathbf{f}\|_{\infty}$ . If one imposes that  $\mathbf{f}_i = 0$  for some fixed *i*, then a converse inequality also holds since  $\|\mathbf{f}\|_{\infty} \leq \|\mathbf{f}\|_{var}$ . These bounds are especially useful to analyze Sinkhorn convergence (see Remark 4.14 below), because dual variables  $f = \log(u)$ solving (4.14) are defined up to an additive constant, so that one can impose that  $f_i = 0$  for some *i*. The Hilbert metric was introduced independently by [Birkhoff, 1957] and [Samelson et al., 1957]. They proved the following fundamental theorem, which shows that a positive matrix is a strict contraction on the cone of positive vectors.

**Theorem 4.1.** Let  $\mathbf{K} \in \mathbb{R}_{+,*}^{n \times m}$ ; then for  $(\mathbf{v}, \mathbf{v}') \in (\mathbb{R}_{+,*}^{m})^2$ 

$$
d_{\mathcal{H}}(\mathbf{Kv},\mathbf{Kv}') \leq \lambda(\mathbf{K})d_{\mathcal{H}}(\mathbf{v},\mathbf{v}'), \text{ where } \left\{\begin{array}{l} \lambda(\mathbf{K}) \stackrel{\text{def.}}{=} \frac{\sqrt{\eta(\mathbf{K})}-1}{\sqrt{\eta(\mathbf{K})}+1} < 1, \ \eta(\mathbf{K}) \stackrel{\text{def.}}{=} \max_{i,j,k,\ell} \frac{\mathbf{K}_{i,k}\mathbf{K}_{j,\ell}}{\mathbf{K}_{j,k}\mathbf{K}_{i,\ell}}. \end{array}\right.
$$

Figure 4.7 illustrates this theorem.

Image /page/13/Figure/8 description: The image displays a sequence of three diagrams illustrating a transformation in a 2D space. The first diagram shows a coordinate system with two rays originating from the origin, labeled \"u\" (blue) and \"u'\" (red). The blue ray is defined as \"u = {ru ; r > 0}\", and the distance between \"u\" and \"u'\" is denoted as \"dH(u, u')\". A shaded region representing \"R^2\_+\" is also shown in the first quadrant. The second diagram shows the same shaded region transformed by a mapping labeled \"K\" into a new shape, \"K R^2\_+\", which is a cone-like region in the first quadrant. The third diagram shows this transformed region \"K R^2\_+\" further transformed by \"K\" into \"K^2 R^2\_+\", resulting in a narrower cone-like region in the first quadrant. Dotted lines with arrows indicate the direction of the transformation.

**Figure 4.7:** Left: the Hilbert metric  $d_{\mathcal{H}}$  is a distance over rays in cones (here positive vectors). Right: visualization of the contraction induced by the iteration of a positive matrix **K**.

**Remark 4.13** (Perron–Frobenius)**.** A typical application of Theorem 4.1 is to provide a quantitative proof of the Perron–Frobenius theorem, which, as explained in Remark 4.15, is linked to a local linearization of Sinkhorn's iterates. A matrix  $\mathbf{K} \in \mathbb{R}_+^{n \times n}$  with  $\mathbf{K}^\top \mathbb{1}_n = \mathbb{1}_n$  maps  $\Sigma_n$  into  $\Sigma_n$ . If furthermore  $\mathbf{K} > 0$ , then according to Theorem 4.1, it is strictly contractant for the metric  $d_{\mathcal{H}}$ , hence there exists a unique invariant probability distribution  $p^* \in \Sigma_n$  with  $\mathbf{K} p^* = p^*$ . Furthermore,

for any  $p_0 \in \Sigma_n$ ,  $d_{\mathcal{H}}(\mathbf{K}^{\ell} p_0, p^*) \leq \lambda(\mathbf{K})^{\ell} d_{\mathcal{H}}(p_0, p^*)$ , *i.e.* one has linear convergence of the iterates of the matrix toward  $p^*$ . This is illustrated in Figure 4.8.

Image /page/14/Figure/2 description: The image displays four triangular diagrams arranged horizontally, each illustrating a sequence of nested triangles. The first diagram, labeled "Σ3", shows a large blue triangle with a thick border, containing a series of lighter, nested triangles that converge towards a point labeled "p\*". A black dot is positioned near "p\*". The second diagram, labeled "KΣ3", is similar but the nested triangles are rotated, and the thick border is a darker shade of blue. The third diagram, labeled "K2Σ3", shows further rotation and a slightly different shade of blue for the thick border. The fourth diagram, labeled "{KlΣ3}l", presents the most complex nesting, with very thin triangles converging to a point, and the thick border is a deep purple, with a gradient of purple lines connecting the vertices of the nested triangles.

**Figure 4.8:** Evolution of  $\mathbf{K}^{\ell} \Sigma_3 \to \{p^{\star}\}\$  the invariant probability distribution of  $\mathbf{K} \in \mathbb{R}^{3 \times 3}_{+,*}$  with  $\mathbf{K}^{\top} \mathbb{1}_3 = \mathbb{1}_3.$ 

**Remark 4.14** (Global convergence)**.** The following theorem, proved by [Franklin and Lorenz, 1989], makes use of Theorem 4.1 to show the linear convergence of Sinkhorn's iterations.

**Theorem 4.2.** One has  $(\mathbf{u}^{(\ell)}, \mathbf{v}^{(\ell)}) \rightarrow (\mathbf{u}^*, \mathbf{v}^*)$  and

$$
d_{\mathcal{H}}(\mathbf{u}^{(\ell)}, \mathbf{u}^{\star}) = O(\lambda(\mathbf{K})^{2\ell}), \quad d_{\mathcal{H}}(\mathbf{v}^{(\ell)}, \mathbf{v}^{\star}) = O(\lambda(\mathbf{K})^{2\ell}). \tag{4.22}
$$

One also has

$$
d_{\mathcal{H}}(\mathbf{u}^{(\ell)}, \mathbf{u}^{\star}) \leq \frac{d_{\mathcal{H}}(\mathbf{P}^{(\ell)} \mathbb{1}_m, \mathbf{a})}{1 - \lambda(\mathbf{K})^2},
$$
  
$$
d_{\mathcal{H}}(\mathbf{v}^{(\ell)}, \mathbf{v}^{\star}) \leq \frac{d_{\mathcal{H}}(\mathbf{P}^{(\ell), \top} \mathbb{1}_n, \mathbf{b})}{1 - \lambda(\mathbf{K})^2},
$$
(4.23)

where we denoted  $\mathbf{P}^{(\ell)} \stackrel{\text{def.}}{=} \text{diag}(\mathbf{u}^{(\ell)})\mathbf{K} \text{diag}(\mathbf{v}^{(\ell)})$ . Last, one has

$$
\|\log(\mathbf{P}^{(\ell)}) - \log(\mathbf{P}^{\star})\|_{\infty} \le d_{\mathcal{H}}(\mathbf{u}^{(\ell)}, \mathbf{u}^{\star}) + d_{\mathcal{H}}(\mathbf{v}^{(\ell)}, \mathbf{v}^{\star}),\tag{4.24}
$$

where  $\mathbf{P}^*$  is the unique solution of (4.2).

*Proof.* One notices that for any  $(\mathbf{v}, \mathbf{v}') \in (\mathbb{R}^m_{+,*})^2$ , one has

$$
d_{\mathcal{H}}(\mathbf{v}, \mathbf{v}') = d_{\mathcal{H}}(\mathbf{v}/\mathbf{v}', \mathbb{1}_m) = d_{\mathcal{H}}(\mathbb{1}_m/\mathbf{v}, \mathbb{1}_m/\mathbf{v}').
$$

This shows that

$$
d_{\mathcal{H}}(\mathbf{u}^{(\ell+1)}, \mathbf{u}^{\star}) = d_{\mathcal{H}}\left(\frac{\mathbf{a}}{\mathbf{K}\mathbf{v}^{(\ell)}}, \frac{\mathbf{a}}{\mathbf{K}\mathbf{v}^{\star}}\right)
$$
  
=  $d_{\mathcal{H}}(\mathbf{K}\mathbf{v}^{(\ell)}, \mathbf{K}\mathbf{v}^{\star}) \leq \lambda(\mathbf{K}) d_{\mathcal{H}}(\mathbf{v}^{(\ell)}, \mathbf{v}^{\star}),$ 

where we used Theorem 4.1. This shows  $(4.22)$ . One also has, using the triangular inequality,

$$
d_{\mathcal{H}}(\mathbf{u}^{(\ell)}, \mathbf{u}^{\star}) \le d_{\mathcal{H}}(\mathbf{u}^{(\ell+1)}, \mathbf{u}^{(\ell)}) + d_{\mathcal{H}}(\mathbf{u}^{(\ell+1)}, \mathbf{u}^{\star})
$$
  

$$
\le d_{\mathcal{H}}\left(\frac{\mathbf{a}}{\mathbf{K}\mathbf{v}^{(\ell)}}, \mathbf{u}^{(\ell)}\right) + \lambda(\mathbf{K})^2 d_{\mathcal{H}}(\mathbf{u}^{(\ell)}, \mathbf{u}^{\star})
$$
  

$$
= d_{\mathcal{H}}(\mathbf{a}, \mathbf{u}^{(\ell)} \odot (\mathbf{K}\mathbf{v}^{(\ell)})) + \lambda(\mathbf{K})^2 d_{\mathcal{H}}(\mathbf{u}^{(\ell)}, \mathbf{u}^{\star}),
$$

which gives the first part of (4.23) since  $\mathbf{u}^{(\ell)} \odot (\mathbf{K} \mathbf{v}^{(\ell)}) = \mathbf{P}^{(\ell)} \mathbb{1}_m$  (the second one being similar). The proof of (4.24) follows from [Franklin and Lorenz, 1989, Lem. 3].  $\Box$ 

The bound (4.23) shows that some error measures on the marginal constraints violation, for instance,  $\|\mathbf{P}^{(\ell)}\mathbb{1}_m - \mathbf{a}\|_1$  and  $\|\mathbf{P}^{(\ell)}\mathbb{1}_n - \mathbf{b}\|_1$ , are useful stopping criteria to monitor the convergence. Note that thanks to (4.21), these Hilbert metric rates on the scaling variable  $(\mathbf{u}^{(\ell)}, \mathbf{v}^{(\ell)})$  give a linear rate on the dual variables  $(\mathbf{f}^{(\ell)}, \mathbf{g}^{(\ell)}) \stackrel{\text{def.}}{=} (\varepsilon \log(\mathbf{u}^{(\ell)}), \varepsilon \log(\mathbf{v}^{(\ell)}))$  for the variation norm  $\lVert \cdot \rVert_{\text{var}}$ .

Figure 4.5, bottom row, highlights this linear rate on the constraint violation and shows how this rate degrades as  $\varepsilon \to 0$ . These results are proved in [Franklin] and Lorenz, 1989] and are tightly connected to nonlinear Perron–Frobenius theory [Lemmens and Nussbaum, 2012]. Perron–Frobenius theory corresponds to the linearization of the iterations; see (4.25). This convergence analysis is extended by [Linial et al., 1998], who show that each iteration of Sinkhorn increases the permanence of the scaled coupling matrix.

**Remark 4.15** (Local convergence)**.** The global linear rate (4.24) is often quite pessimistic, typically in  $\mathcal{X} = \mathcal{Y} = \mathbb{R}^d$  for cases where there exists a Monge map when  $\varepsilon = 0$  (see Remark 2.7). The global rate is in contrast rather sharp for more difficult situations where the cost matrix **C** is close to being random, and in these cases, the rate scales exponentially bad with  $\varepsilon$ ,  $1 - \lambda(\mathbf{K}) \sim e^{-1/\varepsilon}$ . To obtain a finer asymptotic analysis of the convergence (*e.g.* if one is interested in a highprecision solution and performs a large number of iterations), one usually rather studies the local convergence rate. One can write a Sinkhorn update as iterations

## 4.3. Speeding Up Sinkhorn's Iterations 73

of a fixed-point map  $f^{(\ell+1)} = \Phi(f^{(\ell)})$ , where

$$
\Phi \stackrel{\text{\tiny def.}}{=} \Phi_2 \odot \Phi_1 \quad \text{where} \quad \left\{ \begin{array}{l} \Phi_1(\mathbf{f}) = \varepsilon \log \mathbf{K}^{\mathrm{T}}(e^{\mathbf{f}/\varepsilon}) - \log(\mathbf{b}), \\ \Phi_2(\mathbf{g}) = \varepsilon \log \mathbf{K}(e^{\mathbf{g}/\varepsilon}) - \log(\mathbf{a}). \end{array} \right.
$$

For optimal  $(f, g)$  solving  $(4.30)$ , denoting  $P = \text{diag}(e^{f/\varepsilon})K \text{diag}(e^{g/\varepsilon})$  the optimal coupling solving (4.2), one has the following Jacobian:

$$
\partial \Phi(\mathbf{f}) = \text{diag}(\mathbf{a})^{-1} \odot \mathbf{P} \odot \text{diag}(\mathbf{b})^{-1} \odot \mathbf{P}^{\mathrm{T}}.
$$
 (4.25)

This Jacobian is a positive matrix with  $\partial \Phi(f) 1_n = 1_n$ , and thus by the Perron– Frobenius theorem, it has a single dominant eigenvector  $\mathbb{1}_m$  with associated eigenvalue 1. Since **f** is defined up to a constant, it is actually the second eigenvalue  $1 - \kappa < 1$  which governs the local linear rate, and this shows that for  $\ell$  large enough,

$$
\|\mathbf{f}^{(\ell)} - \mathbf{f}\| = O((1 - \kappa)^{\ell}).
$$

Numerically, in "simple cases" (such as when there exists a smooth Monge map when  $\varepsilon = 0$ ), this rate scales like  $\kappa \sim \varepsilon$ . We refer to [Knight, 2008] for more details in the bistochastic (assignment) case.

# **4.3 Speeding Up Sinkhorn's Iterations**

The main computational bottleneck of Sinkhorn's iterations is the vector-matrix multiplication against kernels  $\mathbf{K}$  and  $\mathbf{K}^{\top}$ , with complexity  $O(nm)$  if implemented naively. We now detail several important cases where the complexity can be improved significantly.

**Remark 4.16** (Parallel and GPU friendly computation)**.** The simplicity of Sinkhorn's algorithm yields an extremely efficient approach to compute simultaneously several regularized Wasserstein distances between pairs of histograms. Let *N* be an integer,  $\mathbf{a}_1, \ldots, \mathbf{a}_N$  be histograms in  $\Sigma_n$ , and  $\mathbf{b}_1, \ldots, \mathbf{b}_N$  be histograms in  $\Sigma_m$ . We seek to compute all *N* approximate distances  $L_C^{\varepsilon}(\mathbf{a}_1, \mathbf{b}_1), \ldots, L_C^{\varepsilon}(\mathbf{a}_N, \mathbf{b}_N)$ . In that case, writing  $\mathbf{A} = [\mathbf{a}_1, \dots, \mathbf{a}_N]$  and  $\mathbf{B} = [\mathbf{b}_1, \dots, \mathbf{b}_N]$  for the  $n \times N$  and  $m \times N$  matrices storing all histograms, one can notice that all Sinkhorn iterations for all these *N* pairs can be carried out in parallel, by setting, for instance,

$$
\mathbf{U}^{(\ell+1)} \stackrel{\text{def.}}{=} \frac{\mathbf{A}}{\mathbf{K} \mathbf{V}^{(\ell)}} \quad \text{and} \quad \mathbf{V}^{(\ell+1)} \stackrel{\text{def.}}{=} \frac{\mathbf{B}}{\mathbf{K}^{\mathrm{T}} \mathbf{U}^{(\ell+1)}},\tag{4.26}
$$

initialized with  $\mathbf{V}^{(0)} = \mathbb{1}_{m \times N}$ . Here  $\frac{1}{n}$  corresponds to the entrywise division of matrices. One can further check that upon convergence of **V** and **U**, the (row) vector of regularized distances simplifies to

 ${\mathbb 1}_n^{\rm T}({\mathbf U}\odot \log {\mathbf U}\odot (({{\mathbf K}}\odot {\mathbf C}){\mathbf V}) + {\mathbf U}\odot (({{\mathbf K}}\odot {\mathbf C})({\mathbf V}\odot \log {\mathbf V}))) \in {\mathbb R}^N.$ 

Note that the basic Sinkhorn iterations described in Equation (4.15) are intrinsically GPU friendly, since they only consist in matrix-vector products, and this was exploited, for instance, to solve matching problems in Slomp et al. [2011]). However, the matrixmatrix operations presented in Equation (4.26) present even better opportunities for parallelism, which explains the success of Sinkhorn's algorithm to compute OT distances between histograms at large scale.

**Remark 4.17** (Speed-up for separable kernels)**.** We consider in this section an important particular case for which the complexity of each Sinkhorn iteration can be significantly reduced. That particular case happens when each index *i* and *j* considered in the costmatrix can be described as a *d*-uple taken in the cartesian product of *d* finite sets  $[n_1], \ldots, [n_d],$ 

$$
i = (i_k)_{k=1}^d, j = (j_k)_{k=1}^d \in [\![n_1]\!] \times \cdots \times [\![n_d]\!].
$$

In that setting, if the cost  $\mathbf{C}_{ij}$  between indices *i* and *j* is additive along these sub-indices, namely if there exists *d* matrices  $\mathbf{C}^1, \ldots, \mathbf{C}^d$ , each of respective size  $n_1 \times n, \ldots, n_d \times n_d$ , such that

$$
\mathbf{C}_{ij} = \sum_{k=1}^d \mathbf{C}^k_{i_k,j_k},
$$

then one obtains as a direct consequence that the kernel appearing in the Sinkhorn iterations has a separable multiplicative structure,

$$
\mathbf{K}_{i,j} = \prod_{k=1}^{d} \mathbf{K}_{i_k,j_k}^k.
$$
\n(4.27)

Such a separable multiplicative structure allows for a very fast (exact) evaluation of **Ku**. Indeed, instead of instantiating **K** as a matrix of size  $n \times n$ , which would have a prohibitive size since  $n = \prod_k n_k$  is usually exponential in the dimension *d*, one can instead recover **Ku** by simply applying  $\mathbf{K}^k$  along each "slice" of **u**. If  $n = m$ , the complexity reduces to  $O(n^{1+1/d})$  in place of  $O(n^2)$ .

An important example of this speed-up arises when  $\mathcal{X} = \mathcal{Y} = [0,1]^d$ ; the ground cost is the *q*-th power of the *q*-norm,

$$
c(x,y) = \|x - y\|_q^q = \sum_{i=1}^d |x_i - y_i|^q, \ q > 0;
$$

and the space is discretized using a regular grid in which only points  $x_i$  =  $(i_1/n_1, \ldots, i_d/n_d)$  for  $i = (i_1, \ldots, i_d) \in [n_1] \times \cdots \times [n_d]$  are considered. In that case a multiplication by **K** can be carried out more efficiently by applying each 1-D  $n_k \times n_k$ convolution matrix

$$
\mathbf{K}^{k} = \left[\exp\left(-\left|\frac{r-s}{n_k}\right|^{q}/\varepsilon\right)\right]_{1 \leq r,s \leq n_k}
$$

# 4.3. Speeding Up Sinkhorn's Iterations 75

to **u** reshaped as a tensor whose first dimension has been permuted to match the *k*-th set of indices. For instance, if  $d = 2$  (planar case) and  $q = 2$  (2-Wasserstein, resulting in Gaussian convolutions), histograms **a** and as a consequence Sinkhorn multipliers **u** can be instantiated as  $n_1 \times n_2$  matrices. We write **U** to underline the fact that the multiplier **u** is reshaped as a  $n_1 \times n_2$  matrix, rather than a vector of length  $n_1 n_2$ . Then, computing **Ku**, which would naively require  $(n_1n_2)^2$  operations with a naive implementation, can be obtained by applying two 1-D convolutions separately, as

$$
(\mathbf{K}^2(\mathbf{K}^1\mathbf{U})^T)^T = \mathbf{K}^1\mathbf{U}\mathbf{K}^2,
$$

to recover a  $n_1 \times n_2$  matrix in  $(n_1^2)n_2 + n_1(n_2^2)$  operations instead of  $n_1^2n_2^2$  operations. Note that this example agrees with the exponent  $(1 + 1/d)$  given above. With larger *d*, one needs to apply these very same 1-D convolutions to each slice of **u** (reshaped as a tensor of suitable size) an operation which is extremely efficient on GPUs.

This important observations underlies many of the practical successes found when applying optimal transport to shape data in 2-D and 3-D, as highlighted in [Solomon et al., 2015, Bonneel et al., 2016], in which distributions supported on grids of sizes as large as  $200^3 = 8 \times 10^6$  are handled.

**Remark 4.18** (Approximated convolutions)**.** The main computational bottleneck of Sinkhorn's iterations (4.15) lies in the multiplication of a vector by **K** or by its adjoint. Besides using separability (4.27), it is also possible to exploit other special structures in the kernel. The simplest case is for translation invariant kernels  $\mathbf{K}_{i,j} = k_{i-j}$ , which is typically the case when discretizing the measure on a fixed uniform grid in Euclidean space  $\mathcal{X} = \mathbb{R}^d$ . Then  $\mathbf{Kv} = k \star \mathbf{v}$  is a convolution, and there are several algorithms to approximate the convolution in nearly linear time. The most usual one is by Fourier transform  $\mathcal F$ , assuming for simplicity periodic boundary conditions, because  $\mathcal{F}(k \star \mathbf{v}) = \mathcal{F}(k) \odot \mathcal{F}(\mathbf{v})$ . This leads, however, to unstable computations and is often unacceptable for small *ε*. Another popular way to speed up computation is by approximating the convolution using a succession of autoregressive filters, using, for instance, the Deriche filtering method Deriche [1993]. We refer to [Getreuer, 2013] for a comparison of various fast filtering methods.

**Remark 4.19** (Geodesic in heat approximation)**.** For nonplanar domains, the kernel **K** is not a convolution, but in the case where the cost is  $\mathbf{C}_{i,j} = d_{\mathcal{M}}(x_i, y_j)^p$  where  $d_{\mathcal{M}}$ is a geodesic distance on a surface  $\mathcal M$  (or a more general manifold), it is also possible to perform fast approximations of the application of  $\mathbf{K} = e^{-\frac{d_{\mathcal{M}}}{\varepsilon}}$  to a vector. Indeed, Varadhan's formulas [1967] assert that this kernel is close to the Laplacian kernel (for  $p = 1$ ) and the heat kernel (for  $p = 2$ ). The first formula of Varadhan states

$$
-\frac{\sqrt{t}}{2}\log(\mathcal{P}_t(x,y)) = d_{\mathcal{M}}(x,y) + o(t) \quad \text{where} \quad \mathcal{P}_t \stackrel{\text{def.}}{=} (\text{Id} - t\Delta_{\mathcal{M}})^{-1},\tag{4.28}
$$

where  $\Delta_M$  is the Laplace–Beltrami operator associated to the manifold M (which is negative semidefinite), so that  $P_t$  is an integral kernel and  $g = \int_{\mathcal{M}} P_t(x, y) f(y) dy$  is the solution of  $g - t\Delta_M g = f$ . The second formula of Varadhan states

$$
\sqrt{-4t\log(\mathcal{H}_t(x,y))} = d_{\mathcal{M}}(x,y) + o(t),\tag{4.29}
$$

where  $\mathcal{H}_t$  is the integral kernel defined so that  $g_t = \int_{\mathcal{M}} \mathcal{H}_t(x, y) f(y) dy$  is the solution at time *t* of the heat equation

$$
\frac{\partial g_t(x)}{\partial t} = (\Delta_{\mathcal{M}} g_t)(x).
$$

The convergence in these formulas (4.28) and (4.29) is uniform on compact manifolds. Numerically, the domain  $\mathcal M$  is discretized (for instance, using finite elements) and  $\Delta_M$  is approximated by a discrete Laplacian matrix *L*. A typical example is when using piecewise linear finite elements, so that *L* is the celebrated cotangent Laplacian (see [Botsch et al., 2010] for a detailed account for this construction). These formulas can be used to approximate efficiently the multiplication by the Gibbs kernel  $\mathbf{K}_{i,j} =$  $e^{-\frac{d(x_i, y_j)^p}{\varepsilon}}$  $\frac{1}{\epsilon}$ . Equation (4.28) suggests, for the case  $p = 1$ , to use  $\epsilon =$  $\sqrt{t}$  $\frac{\sqrt{t}}{2}$  and to replace the multiplication by **K** by the multiplication by  $(\text{Id} - tL)^{-1}$ , which necessitates the resolution of a positive symmetric linear system. Equation (4.29), coupled with *R* steps of implicit Euler for the stable resolution of the heat flow, suggests for  $p = 2$  to trade the multiplication by **K** by the multiplication by  $(\text{Id} - \frac{t}{k})$  $\frac{t}{R}L$ )<sup>-*R*</sup> for  $4t = \varepsilon$ , which in turn necessitates *R* resolutions of linear systems. Fortunately, since these linear systems are supposed to be solved at each Sinkhorn iteration, one can solve them efficiently by precomputing a sparse Cholesky factorization. By performing a reordering of the rows and columns of the matrix [George and Liu, 1989], one obtains a nearly linear sparsity for 2-D manifolds and thus each Sinkhorn iteration has linear complexity (the performance degrades with the dimension of the manifold). The use of Varadhan's formula to approximate geodesic distances was initially proposed in [Crane et al., 2013] and its use in conjunction with Sinkhorn iterations in [Solomon et al., 2015].

**Remark 4.20** (Extrapolation acceleration)**.** Since the Sinkhorn algorithm is a fixed-point algorithm (as shown in Remark 4.15), one can use standard linear or even nonlinear extrapolation schemes to enhance the conditioning of the fixed-point mapping near the solution, and improve the linear convergence rate. This is similar to the successive overrelaxation method (see, for instance, [Hadjidimos, 2000]), so that the local linear rate of convergence is improved from  $O((1 - \kappa)^{\ell})$  to  $O((1 - \sqrt{\kappa})^{\ell})$  for some  $\kappa > 0$  (see Remark 4.15). We refer to [Peyré et al., 2017] for more details.

# **4.4 Stability and Log-Domain Computations**

As briefly mentioned in Remark 4.7, the Sinkhorn algorithm suffers from numerical overflow when the regularization parameter  $\varepsilon$  is small compared to the entries of the cost matrix **C**. This concern can be alleviated to some extent by carrying out computations in the log domain. The relevance of this approach is made more clear by considering the dual problem associated to  $(4.2)$ , in which these log-domain computations arise naturally.

**Proposition 4.4.** One has

$$
L_{\mathbf{C}}^{\varepsilon}(\mathbf{a},\mathbf{b}) = \max_{\mathbf{f}\in\mathbb{R}^{n},\mathbf{g}\in\mathbb{R}^{m}}\langle\mathbf{f},\,\mathbf{a}\rangle + \langle\mathbf{g},\,\mathbf{b}\rangle - \varepsilon\langle e^{\mathbf{f}/\varepsilon},\,\mathbf{K}e^{\mathbf{g}/\varepsilon}\rangle. \tag{4.30}
$$

The optimal  $(f, g)$  are linked to scalings  $(\mathbf{u}, \mathbf{v})$  appearing in (4.12) through

$$
(\mathbf{u}, \mathbf{v}) = (e^{\mathbf{f}/\varepsilon}, e^{\mathbf{g}/\varepsilon}).
$$
\n(4.31)

*Proof.* We start from the end of the proof of Proposition 4.3, which links the optimal primal solution **P** and dual multipliers **f** and **g** for the marginal constraints as

$$
\mathbf{P}_{i,j} = e^{\mathbf{f}_i/\varepsilon} e^{-\mathbf{C}_{i,j}/\varepsilon} e^{\mathbf{g}_j/\varepsilon}.
$$

Substituting in the Lagrangian  $\mathcal{E}(\mathbf{P}, \mathbf{f}, \mathbf{g})$  of Equation (4.2) the optimal **P** as a function of **f** and **g**, we obtain that the Lagrange dual function equals

$$
\mathbf{f}, \mathbf{g} \mapsto \langle e^{\mathbf{f}/\varepsilon}, (\mathbf{K} \odot \mathbf{C}) e^{\mathbf{g}/\varepsilon} \rangle - \varepsilon \mathbf{H} (\mathrm{diag}(e^{\mathbf{f}/\varepsilon}) \mathbf{K} \mathrm{diag}(e^{\mathbf{g}/\varepsilon})). \tag{4.32}
$$

The neg-entropy of **P** scaled by  $\varepsilon$ , namely  $\varepsilon \langle \mathbf{P}, \log \mathbf{P} - \mathbb{1}_n \times m \rangle$ , can be stated explicitly as a function of  $f$ ,  $g$ ,  $C$ ,

$$
\langle \operatorname{diag}(e^{\mathbf{f}/\varepsilon}) \mathbf{K} \operatorname{diag}(e^{\mathbf{g}/\varepsilon}), \mathbf{f} \mathbb{1}_m^{\mathrm{T}} + \mathbb{1}_n \mathbf{g}^{\mathrm{T}} - \mathbf{C} - \varepsilon \mathbb{1}_{n \times m} \rangle
$$
  
= -\langle e^{\mathbf{f}/\varepsilon}, (\mathbf{K} \odot \mathbf{C}) e^{\mathbf{g}/\varepsilon} \rangle + \langle \mathbf{f}, \mathbf{a} \rangle + \langle \mathbf{g}, \mathbf{b} \rangle - \varepsilon \langle e^{\mathbf{f}/\varepsilon}, \mathbf{K} e^{\mathbf{g}/\varepsilon} \rangle;

therefore, the first term in (4.32) cancels out with the first term in the entropy above. The remaining terms are those appearing in (4.30).  $\Box$ 

**Remark 4.21** (Sinkhorn as a block coordinate ascent on the dual problem)**.** A simple approach to solving the unconstrained maximization problem (4.30) is to use an exact *block coordinate ascent* strategy, namely to update alternatively **f** and **g** to cancel the respective gradients in these variables of the objective of (4.30). Indeed, one can notice after a few elementary computations that, writing  $Q(\mathbf{f}, \mathbf{g})$  for the objective of (4.30),

$$
\nabla|_{\mathbf{f}}Q(\mathbf{f},\mathbf{g}) = \mathbf{a} - e^{\mathbf{f}/\varepsilon} \odot (\mathbf{K}e^{\mathbf{g}/\varepsilon}), \qquad (4.33)
$$

$$
\nabla|_{\mathbf{g}} Q(\mathbf{f}, \mathbf{g}) = \mathbf{b} - e^{\mathbf{g}/\varepsilon} \odot \left( \mathbf{K}^{\mathrm{T}} e^{\mathbf{f}/\varepsilon} \right). \tag{4.34}
$$

Block coordinate ascent can therefore be implemented in a closed form by applying successively the following updates, starting from any arbitrary  $\mathbf{g}^{(0)}$ , for  $l \geq 0$ :

$$
\mathbf{f}^{(\ell+1)} = \varepsilon \log \mathbf{a} - \varepsilon \log \left( \mathbf{K} e^{\mathbf{g}^{(\ell)} / \varepsilon} \right),\tag{4.35}
$$

$$
\mathbf{g}^{(\ell+1)} = \varepsilon \log \mathbf{b} - \varepsilon \log \left( \mathbf{K}^{\mathrm{T}} e^{\mathbf{f}^{(\ell+1)}/\varepsilon} \right). \tag{4.36}
$$

Such iterations are mathematically equivalent to the Sinkhorn iterations (4.15) when considering the primal-dual relations highlighted in (4.31). Indeed, we recover that at any iteration

$$
(\mathbf{f}^{(\ell)}, \mathbf{g}^{(\ell)}) = \varepsilon(\log(\mathbf{u}^{(\ell)}), \log(\mathbf{v}^{(\ell)})).
$$

**Remark 4.22** (Soft-min rewriting)**.** Iterations (4.35) and (4.36) can be given an alternative interpretation, using the following notation. Given a vector **z** of real numbers we write min*<sup>ε</sup>* **z** for the *soft-minimum* of its coordinates, namely

$$
\min_{\varepsilon} \mathbf{z} = -\varepsilon \log \sum_{i} e^{-\mathbf{z}_i/\varepsilon}.
$$
\n(4.37)

Note that  $\min_{\varepsilon} (\mathbf{z})$  converges to min **z** for any vector **z** as  $\varepsilon \to 0$ . Indeed,  $\min_{\varepsilon} \text{ can be}$ interpreted as a differentiable approximation of the min function, as shown in Figure 4.9.

Image /page/21/Figure/9 description: The image displays five contour plots arranged horizontally, each labeled with a different value of epsilon (ε). The first plot is labeled "ε = 1", the second "ε = 0.5", the third "ε = 10⁻¹", the fourth "ε = 10⁻²", and the fifth "ε = 10⁻³". All plots show a similar pattern of curved contour lines originating from the top-right corner and extending towards the bottom-left. The color scheme transitions from dark blue in the bottom-left to lighter blues, greens, and finally yellow in the top-right. As the value of epsilon decreases from 1 to 10⁻³, the contour lines become more concentrated and form sharper, more square-like shapes in the top-right corner, indicating a steeper gradient.

**Figure 4.9:** Display of the function  $\min_{\varepsilon} (\mathbf{z})$  in 2-D,  $\mathbf{z} \in \mathbb{R}^2$ , for varying  $\varepsilon$ .

Using this notation, Equations (4.35) and (4.36) can be rewritten

$$
(\mathbf{f}^{(\ell+1)})_i = \min_{\varepsilon} (\mathbf{C}_{ij} - \mathbf{g}_j^{(\ell)})_j + \varepsilon \log \mathbf{a}_i, \tag{4.38}
$$

$$
(\mathbf{g}^{(\ell+1)})_j = \min_{\varepsilon} (\mathbf{C}_{ij} - \mathbf{f}_i^{(\ell)})_i + \varepsilon \log \mathbf{b}_j.
$$
 (4.39)

Here the term  $\min_{\varepsilon} (\mathbf{C}_{ij} - \mathbf{g}_{i}^{(\ell)})$  $j^{(t)}$ <sub>j</sub> denotes the soft-minimum of all values of the *j*th column of matrix  $(C - 1<sub>n</sub>(g<sup>(l)</sup>)<sup>T</sup>)$ . To simplify notations, we introduce an operator that takes a matrix as input and outputs now a column vector of the soft-minimum values of its columns or rows. Namely, for any matrix  $A \in \mathbb{R}^{n \times m}$ , we define

$$
\begin{aligned} \mathrm{Min}^{\mathrm{row}}_{\varepsilon} \left( \mathbf{A} \right) & \stackrel{\mathrm{def.}}{=} \left( \mathrm{min}_{\varepsilon} \left( \mathbf{A}_{i,j} \right)_{j} \right)_{i} \in \mathbb{R}^{n}, \\ \mathrm{Min}^{\mathrm{col}}_{\varepsilon} \left( \mathbf{A} \right) & \stackrel{\mathrm{def.}}{=} \left( \mathrm{min}_{\varepsilon} \left( \mathbf{A}_{i,j} \right)_{i} \right)_{j} \in \mathbb{R}^{m}. \end{aligned}
$$

# 4.4. Stability and Log-Domain Computations 79

Note that these operations are equivalent to the entropic *c*-transform introduced in §5.3 (see in particular (5.11)). Using this notation, Sinkhorn's iterates read

$$
\mathbf{f}^{(\ell+1)} = \text{Min}_{\varepsilon}^{\text{row}} \left( \mathbf{C} - \mathbb{1}_n \mathbf{g}^{(\ell)}^{\text{T}} \right) + \varepsilon \log \mathbf{a},\tag{4.40}
$$

$$
\mathbf{g}^{(\ell+1)} = \text{Min}_{\varepsilon}^{\text{col}} \left( \mathbf{C} - \mathbf{f}^{(\ell)} \mathbb{1}_m^{\text{T}} \right) + \varepsilon \log \mathbf{b}.\tag{4.41}
$$

Note that as  $\varepsilon \to 0$ , min<sub>e</sub> converges to min, but the iterations do not converge anymore in the limit  $\varepsilon = 0$ , because alternate minimization does not converge for constrained problems, which is the case for the unregularized dual (2.20).

**Remark 4.23** (Log-domain Sinkhorn)**.** While mathematically equivalent to the Sinkhorn updates (4.15), iterations (4.38) and (4.39) suggest using the *log-sum-exp* stabilization trick to avoid underflow for small values of  $\varepsilon$ . Writing  $z = \min z$ , that trick suggests evaluating min*<sup>ε</sup>* **z** as

$$
\min_{\varepsilon} \mathbf{z} = \underline{z} - \varepsilon \log \sum_{i} e^{-(\mathbf{z}_i - \underline{z})/\varepsilon}.\tag{4.42}
$$

Instead of substracting  $\underline{z}$  to stabilize the log-domain iterations as in  $(4.42)$ , one can actually substract the previously computed scalings. This leads to the stabilized iteration

$$
\mathbf{f}^{(\ell+1)} = \text{Min}_{\varepsilon}^{\text{row}} \left( \mathbf{S}(\mathbf{f}^{(\ell)}, \mathbf{g}^{(\ell)}) \right) + \mathbf{f}^{(\ell)} + \varepsilon \log(\mathbf{a}),\tag{4.43}
$$

$$
\mathbf{g}^{(\ell+1)} = \text{Min}_{\varepsilon}^{\text{col}}\left(\mathbf{S}(\mathbf{f}^{(\ell+1)}, \mathbf{g}^{(\ell)})\right) + \mathbf{g}^{(\ell)} + \varepsilon \log(\mathbf{b}),\tag{4.44}
$$

where we defined

$$
\mathbf{S}(\mathbf{f},\mathbf{g})=\left(\mathbf{C}_{i,j}-\mathbf{f}_i-\mathbf{g}_j\right)_{i,j}.
$$

In contrast to the original iterations (4.15), these log-domain iterations (4.43) and (4.44) are stable for arbitrary  $\varepsilon > 0$ , because the quantity  $S(f, g)$  stays bounded during the iterations. The downside is that it requires *nm* computations of exp at each step. Computing a  $\text{Min}_{\varepsilon}^{\text{row}}$  or  $\text{Min}_{\varepsilon}^{\text{col}}$  is typically substantially slower than matrix multiplications and requires computing line by line soft-minima of matrices **S**. There is therefore no efficient way to parallelize the application of Sinkhorn maps for several marginals simultaneously. In Euclidean domains of small dimension, it is possible to develop efficient multiscale solvers with a decaying  $\varepsilon$  strategy to significantly speed up the computation using sparse grids [Schmitzer, 2016b].

**Remark 4.24** (Dual for generic measures)**.** For generic and not necessarily discrete input measures  $(\alpha, \beta)$ , the dual problem (4.30) reads

$$
\sup_{(f,g)\in\mathcal{C}(\mathcal{X})\times\mathcal{C}(\mathcal{Y})}\int_{\mathcal{X}}f\mathrm{d}\alpha+\int_{\mathcal{Y}}g\mathrm{d}\beta-\varepsilon\int_{\mathcal{X}\times\mathcal{Y}}e^{\frac{-c(x,y)+f(x)+g(y)}{\varepsilon}}\mathrm{d}\alpha(x)\mathrm{d}\beta(y).
$$
 (4.45)

This corresponds to a smoothing of the constraint  $\mathcal{R}(c)$  appearing in the original problem (2.24), which is retrieved in the limit  $\varepsilon \to 0$ . Proving existence (*i.e.* the sup is actually a max) of these Kantorovich potentials  $(f, g)$  in the case of entropic transport is less easy than for classical OT, because one cannot use the *c*-transform and potentials are not automatically Lipschitz. Proof of existence can be done using the convergence of Sinkhorn iterations; see [Chizat et al., 2018b] for more details.

**Remark 4.25** (Unconstrained entropic dual)**.** As in Remark 2.23, in the case  $\int_{\mathcal{X}} d\alpha = \int_{\mathcal{Y}} d\beta = 1$ , one can consider an alternative dual formulation

$$
\sup_{(f,g)\in\mathcal{C}(\mathcal{X})\times\mathcal{C}(\mathcal{Y})}\int_{\mathcal{X}}f\mathrm{d}\alpha+\int_{\mathcal{Y}}g\mathrm{d}\beta+\min_{\varepsilon}(c-f\oplus g),\tag{4.46}
$$

which achieves the same optimal value as  $(4.45)$ . Similarly to  $(4.37)$ , the softminimum (here on  $\mathcal{X} \times \mathcal{Y}$ ) is defined as

$$
\forall S \in \mathcal{C}(\mathcal{X} \times \mathcal{Y}), \quad \min_{\varepsilon} S \stackrel{\text{def.}}{=} -\varepsilon \int_{\mathcal{X} \times \mathcal{Y}} e^{\frac{-S(x,y)}{\varepsilon}} d\alpha(x) d\beta(y)
$$

(note that it depends on  $(\alpha, \beta)$ ). As  $\varepsilon \to 0$ , min<sub> $\varepsilon$ </sub>  $\to$  min, as used in the unregularized and unconstrained formulation (2.27). Note that while both (4.45) and (4.46) are unconstrained problems, a chief advantage of (4.46) is that it is better conditioned, in the sense that the Hessian of the functional is uniformly bounded by *ε*. Another way to obtain such a conditioning improvement is to consider semidual problems; see §5.3 and in particular Remark 5.1. A disadvantage of this alternative dual formulation is that the presence of a log prevents the use of stochastic optimization methods as detailed in §5.4; see in particular Remark 5.3.

# **4.5 Regularized Approximations of the Optimal Transport Cost**

The entropic dual (4.30) is a smooth unconstrained concave maximization problem, which approximates the original Kantorovich dual (2.20), as detailed in the following proposition.

**Proposition 4.5.** Any pair of optimal solutions  $(f^{\star}, g^{\star})$  to (4.30) are such that  $(f^{\star}, g^{\star}) \in$ **R**(**C**), the set of feasible Kantorovich potentials defined in (2.21). As a consequence, we have that for any  $\varepsilon$ ,

$$
\langle \mathbf{f}^{\star},\,\mathbf{a}\rangle + \langle \mathbf{g}^{\star},\,\mathbf{b}\rangle \leq L_{\mathbf{C}}(\mathbf{a},\mathbf{b}).
$$

*Proof.* Primal-dual optimality conditions in (4.4) with the constraint that **P** is a probability and therefore  $\mathbf{P}_{i,j} \leq 1$  for all *i, j* yields that  $\exp(-(\mathbf{f}_i^{\star} + \mathbf{g}_j^{\star} - \mathbf{C}_{i,j})/\varepsilon) \leq 1$  and therefore that  $\mathbf{f}_i^{\star} + \mathbf{g}_j^{\star} \leq \mathbf{C}_{i,j}$ .  $\Box$ 

A chief advantage of the regularized transportation cost  $L_C^{\varepsilon}$  defined in (4.2) is that it is smooth and convex, which makes it a perfect fit for integrating as a loss function in variational problems (see Chapter 9).

**Proposition 4.6.** L<sub> $\epsilon$ </sub><sup> $\epsilon$ </sup>(**a**, **b**) is a jointly convex function of **a** and **b** for  $\epsilon \geq 0$ . When  $\varepsilon > 0$ , its gradient is equal to

$$
\nabla L^\varepsilon_{\mathbf C}(\mathbf a,\mathbf b)=\begin{bmatrix} \mathbf f^\star \\ \mathbf g^\star \end{bmatrix},
$$

where  $f^*$  and  $g^*$  are the optimal solutions of Equation (4.30) chosen so that their coordinates sum to 0.

In [Cuturi, 2013], lower and upper bounds to approximate the Wasserstein distance between two histograms were proposed. These bounds consist in evaluating the primal and dual objectives at the solutions provided by the Sinkhorn algorithm.

**Definition 4.1** (Sinkhorn divergences). Let  $f^*$  and  $g^*$  be optimal solutions to (4.30) and  $\mathbf{P}^*$  be the solution to (4.2). The Wasserstein distance is approximated using the following primal and dual Sinkhorn divergences:

$$
\begin{aligned} &\mathfrak{P}^\varepsilon_{\mathbf{C}}(\mathbf{a},\mathbf{b})\stackrel{\text{\tiny def.}}{=} \langle \mathbf{C},\,\mathbf{P}^\star\rangle = \langle e^{\frac{\mathbf{f}^\star}{\varepsilon}},\,(\mathbf{K}\odot\mathbf{C})e^{\frac{\mathbf{g}^\star}{\varepsilon}}\rangle,\\ &\mathfrak{D}^\varepsilon_{\mathbf{C}}(\mathbf{a},\mathbf{b})\stackrel{\text{\tiny def.}}{=} \langle \mathbf{f}^\star,\,\mathbf{a}\rangle + \langle \mathbf{g}^\star,\,\mathbf{b}\rangle, \end{aligned}
$$

where  $\odot$  stands for the elementwise product of matrices,

**Proposition 4.7.** The following relationship holds:

$$
\mathfrak{D}^\varepsilon_{\mathbf{C}}(\mathbf{a},\mathbf{b})\leq \mathrm{L}^\varepsilon_{\mathbf{C}}(\mathbf{a},\mathbf{b})\leq \mathfrak{P}^\varepsilon_{\mathbf{C}}(\mathbf{a},\mathbf{b}).
$$

Furthermore

$$
\mathfrak{P}_{\mathbf{C}}^{\varepsilon}(\mathbf{a},\mathbf{b}) - \mathfrak{D}_{\mathbf{C}}^{\varepsilon}(\mathbf{a},\mathbf{b}) = \varepsilon (\mathbf{H}(\mathbf{P}^{\star}) + 1). \tag{4.47}
$$

*Proof.* Equation (4.47) is obtained by writing that the primal and dual problems have the same values at the optima (see  $(4.30)$ ), and hence

$$
\mathrm{L}^\varepsilon_\mathbf{C}(\mathbf{a},\mathbf{b}) = \mathfrak{P}^\varepsilon_\mathbf{C}(\mathbf{a},\mathbf{b}) - \varepsilon \mathbf{H}(\mathbf{P}^\star) = \mathfrak{D}^\varepsilon_\mathbf{C}(\mathbf{a},\mathbf{b}) - \varepsilon \langle e^{\mathbf{f}^\star/\varepsilon},\,\mathbf{K} e^{\mathbf{g}^\star/\varepsilon}\rangle
$$

The final result can be obtained by remarking that  $\langle e^{f^*}/\varepsilon, \mathbf{K} e^{g^*}/\varepsilon \rangle = 1$ , since the latter amounts to computing the sum of all entries of  $P^*$ .  $\Box$ 

The relationships given above suggest a practical way to bound the actual OT distance, but they are, in fact, valid only upon convergence of the Sinkhorn algorithm and therefore never truly useful in practice. Indeed, in practice Sinkhorn iterations are always terminated after a certain accuracy threshold is reached. When a predetermined number of *L* iterations is set and used to evaluate  $\mathfrak{D}_{\mathbf{C}}^{\varepsilon}$  using iterates  $\mathbf{f}^{(L)}$  and  $\mathbf{g}^{(L)}$  instead of optimal solutions  $f^*$  and  $g^*$ , one recovers, however, a lower bound: Using notation appearing in Equations  $(4.43)$  and  $(4.44)$ , we thus introduce the following finite step approximation of L *ε* **C**:

$$
\mathfrak{D}_{\mathbf{C}}^{(L)}(\mathbf{a}, \mathbf{b}) \stackrel{\text{def.}}{=} \langle \mathbf{f}^{(L)}, \mathbf{a} \rangle + \langle \mathbf{g}^{(L)}, \mathbf{b} \rangle. \tag{4.48}
$$

This "algorithmic" Sinkhorn functional lower bounds the regularized cost function as soon as  $L \geq 1$ .

**Proposition 4.8** (Finite Sinkhorn divergences)**.** The following relationship holds:

$$
\mathfrak{D}^{(L)}_{\bf C}({\bf a},{\bf b})\leq \mathrm{L}^\varepsilon_{\bf C}({\bf a},{\bf b}).
$$

*Proof.* Similarly to the proof of Proposition 4.5, we exploit the fact that after even just one single Sinkhorn iteration, we have, following  $(4.35)$  and  $(4.36)$ , that  $\mathbf{f}^{(L)}$  and  $\mathbf{g}^{(L)}$ are such that the matrix with elements  $\exp(-(f_i^{(L)} + g_j^{(L)} - C_{i,j})/\varepsilon)$  has column sum **b** and its elements are therefore each upper bounded by 1, which results in the dual feasibility of  $(f_i^{(L)}$  $\mathbf{g}^{(L)}$ ,  $\mathbf{g}^{(L)}$ ).  $\Box$ 

**Remark 4.26** (Primal infeasibility of the Sinkhorn iterates)**.** Note that the primal iterates provided in (4.8) are not primal feasible, since, by definition, these iterates are designed to satisfy upon convergence marginal constraints. Therefore, it is not valid to consider  $\langle C, \mathbf{P}^{(2L+1)} \rangle$  as an approximation of  $L_{\mathbf{C}}(\mathbf{a}, \mathbf{b})$  since  $\mathbf{P}^{(2L+1)}$  is not feasible. Using the rounding scheme of Altschuler et al. [2017] laid out in Remark 4.6 one can, however, yield an upper bound on  $L_{\mathbf{C}}^{\varepsilon}(\mathbf{a}, \mathbf{b})$  that can, in addition, be conveniently computed using matrix operations in parallel for several pairs of histograms, in the same fashion as Sinkhorn's algorithm [Lacombe et al., 2018].

**Remark 4.27** (Nonconvexity of finite dual Sinkhorn divergence)**.** Unlike the regularized expression  $L_{\mathbf{C}}^{\varepsilon}$  in (4.30), the finite Sinkhorn divergence  $\mathfrak{D}_{\mathbf{C}}^{(L)}(\mathbf{a},\mathbf{b})$  is *not*, in general, a convex function of its arguments (this can be easily checked numerically).  $\mathfrak{D}_{\mathbf{C}}^{(L)}(\mathbf{a}, \mathbf{b})$ is, however, a differentiable function which can be differentiated using automatic differentiation techniques (see Remark 9.1.3) with respect to any of its arguments, notably **C***,* **a**, or **b**.

# **4.6 Generalized Sinkhorn**

The regularized OT problem (4.2) is a special case of a structured convex optimization problem of the form

$$
\min_{\mathbf{P}} \sum_{i,j} \mathbf{C}_{i,j} \mathbf{P}_{i,j} - \varepsilon \mathbf{H}(\mathbf{P}) + F(\mathbf{P} \mathbb{1}_m) + G(\mathbf{P}^{\mathrm{T}} \mathbb{1}_n). \tag{4.49}
$$

Indeed, defining  $F = \iota_{\{\mathbf{a}\}}$  and  $G = \iota_{\{\mathbf{b}\}}$ , where the indicator function of a closed convex set  $\mathcal C$  is

$$
\iota_{\mathcal{C}}(x) = \begin{cases} 0 & \text{if } x \in \mathcal{C}, \\ +\infty & \text{otherwise}, \end{cases}
$$
 (4.50)

# 4.6. Generalized Sinkhorn 83

one retrieves the hard marginal constraints defining  $\mathcal{U}(\mathbf{a}, \mathbf{b})$ . The proof of Proposition 4.3 carries to this more general problem (4.49), so that the unique solution of (4.49) also has the form (4.12).

As shown in [Peyré, 2015, Frogner et al., 2015, Chizat et al., 2018b, Karlsson and Ringh, 2016], Sinkhorn iterations (4.15) can hence be extended to this problem, and they read

$$
\mathbf{u} \leftarrow \frac{\text{Prox}_{F}^{\mathbf{KL}}(\mathbf{K}\mathbf{v})}{\mathbf{K}\mathbf{v}} \quad \text{and} \quad \mathbf{v} \leftarrow \frac{\text{Prox}_{G}^{\mathbf{KL}}(\mathbf{K}^{\mathrm{T}}\mathbf{u})}{\mathbf{K}^{\mathrm{T}}\mathbf{u}}, \tag{4.51}
$$

where the proximal operator for the **KL** divergence is

$$
\forall \mathbf{u} \in \mathbb{R}_+^N, \quad \text{Prox}_F^{\mathbf{KL}}(\mathbf{u}) = \underset{\mathbf{u}' \in \mathbb{R}_+^N}{\text{argmin }} \mathbf{KL}(\mathbf{u}'|\mathbf{u}) + F(\mathbf{u}'). \tag{4.52}
$$

For some functions *F*, *G* it is possible to prove the linear rate of convergence for iterations (4.51), and these schemes can be generalized to arbitrary measures; see [Chizat et al., 2018b] for more details.

Iterations (4.51) are thus interesting in the cases where  $\text{Prox}_{F}^{KL}$  and  $\text{Prox}_{G}^{KL}$  can be computed in closed form or very efficiently. This is in particular the case for separable functions of the form  $F(\mathbf{u}) = \sum_i F_i(\mathbf{u}_i)$  since in this case

$$
\mathrm{Prox}_{F}^{\mathbf{KL}}(\mathbf{u}) = \left(\mathrm{Prox}_{F_i}^{\mathbf{KL}}(\mathbf{u}_i)\right)_i.
$$

Computing each  $Prox_{F_i}^{KL}$  is usually simple since it is a scalar optimization problem. Note that, similarly to the initial Sinkhorn algorithm, it is also possible to stabilize the computation using log-domain computations [Chizat et al., 2018b].

This algorithm can be used to approximate the solution to various generalizations of OT, and in particular unbalanced OT problems of the form (10.7) (see §10.2 and in particular iterations (10.9)) and gradient flow problems of the form (9.26) (see §9.3).

**Remark 4.28** (Duality and Legendre transform)**.** The dual problem to (4.49) reads

$$
\max_{\mathbf{f}, \mathbf{g}} \ -F^*(\mathbf{f}) - G^*(\mathbf{g}) - \varepsilon \sum_{i,j} e^{\frac{\mathbf{f}_i + \mathbf{g}_j - \mathbf{C}_{i,j}}{\varepsilon}} \tag{4.53}
$$

so that  $(\mathbf{u}, \mathbf{v}) = (e^{\mathbf{f}/\varepsilon}, e^{\mathbf{g}/\varepsilon})$  are the associated scalings appearing in (4.12). Here, *F* <sup>∗</sup> and *G*<sup>∗</sup> are the Fenchel–Legendre conjugate, which are convex functions defined as

$$
\forall \mathbf{f} \in \mathbb{R}^n, \quad F^*(\mathbf{f}) \stackrel{\text{def.}}{=} \max_{\mathbf{a} \in \mathbb{R}^n} \langle \mathbf{f}, \mathbf{a} \rangle - F(\mathbf{a}). \tag{4.54}
$$

The generalized Sinkhorn iterates (4.51) are a special case of Dykstra's algorithm [Dykstra, 1983, 1985] (extended to Bregman divergence [Bauschke and Lewis, 2000, Censor and Reich, 1998]; see also Remark 8.1) and is an alternate maximization scheme on the dual problem (4.53).

The formulation (4.49) can be further generalized to more than two functions and more than a single coupling; we refer to [Chizat et al., 2018b] for more details. This includes as a particular case the Sinkhorn algorithm (10.2) for the multimarginal problem, as detailed in §10.1. It is also possible to rewrite the regularized barycenter problem (9.15) this way, and the iterations (9.18) are in fact a special case of this generalized Sinkhorn.
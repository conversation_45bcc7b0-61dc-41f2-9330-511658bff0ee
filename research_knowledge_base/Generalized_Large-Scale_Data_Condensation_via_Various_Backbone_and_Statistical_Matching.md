# <span id="page-0-2"></span>Generalized Large-Scale Data Condensation via Various Backbone and Statistical Matching

<PERSON><PERSON><sup>1,3</sup> <PERSON><PERSON><PERSON><sup>1</sup> <PERSON><PERSON><sup>1</sup> <PERSON><PERSON><PERSON><sup>2,3</sup> <PERSON><PERSON><PERSON><PERSON><sup>1,∗</sup> <sup>1</sup><PERSON> bin <PERSON> University of AI <sup>2</sup>Hong Kong Polytechnic University <sup>3</sup>OPPO Research

<EMAIL>, {zeyuan.yin,muxin.zhou,zhiqiang.shen}@mbzuai.ac.ae <EMAIL>, ∗:Corresponding author

Image /page/0/Figure/3 description: This image displays a comparison of image generation results for three different subjects: Great white shark, Timber wolf, and Baboon. The results are categorized by four methods: SRe2L, DD, DD+GBM, and DD+GBM+GSM. Each category shows multiple generated images. On the right side of the image, there is a diagram illustrating two neural network architectures. The top diagram shows a ResNet followed by BatchNorm, Channel Var, and Channel Mean, labeled as SRe2L with an accuracy of 21.3% on ImageNet-1k Top-1. The bottom diagram shows a more complex architecture labeled GV-BSM (Ours), which includes ResNet and MobileNet, with intermediate layers like BatchNorm, Convolution, Channel Var, Channel Mean, Patch Var, and Patch Mean. This architecture achieves an accuracy of 31.4% on ImageNet-1k Top-1, with specific parameters (IPC=10, evaluation model=ResNet18).

<span id="page-0-1"></span>Figure 1. Left: Our proposed G-VBSM consists of three novel and effective modules, named DD, GSM and GBM. The richness and quality of information in the synthetic data have been significantly enhanced compared with the *baseline* SRe2L through the sequential merging of DD, GBM, and GSM. Right: G-VBSM prioritizes "generalized matching" to ensure consistency between distilled and complete datasets across various backbones, layers, and statistics, and achieves the highest accuracy 31.4% on ImageNet-1k under IPC 10.

### Abstract

*The lightweight "local-match-global" matching introduced by SRe2L successfully creates a distilled dataset with comprehensive information on the full 224*×*224 ImageNet-1k. However, this one-sided approach is limited to a particular backbone, layer, and statistics, which limits the improvement of the generalization of a distilled dataset. We suggest that sufficient and various "local-match-global" matching are more precise and effective than a single one and have the ability to create a distilled dataset with richer information and better generalization ability. We call this perspective "generalized matching" and propose Generalized Various Backbone and Statistical Matching (G-VBSM) in this work, which aims to create a synthetic dataset with densities, ensuring consistency with the complete dataset across various backbones, layers, and statistics. As experimentally demonstrated, G-VBSM is the first algorithm to obtain strong performance across both* *small-scale and large-scale datasets. Specifically, G-VBSM achieves performances of 38.7% on CIFAR-100, 47.6% on Tiny-ImageNet, and 31.4% on the full 224×224 ImageNet-1k, respectively*[1](#page-0-0) *. These results surpass all SOTA methods by margins of 3.9%, 6.5%, and 10.1%, respectively.*

## 1. Introduction

With the development of deep learning, the number of model parameters and the quantity of training data have become increasingly large [\[4,](#page-8-0) [37\]](#page-9-0). Researchers have tried to minimize the training overhead while preventing a decline in the generalization ability. Data condensation (DC), also known as Dataset distillation, first introduced by Wang *et al.* [\[32\]](#page-9-1), aims to alleviate the training burden by synthesizing a small yet informative distilled dataset derived from the complete training dataset, while ensuring that the behav-

<span id="page-0-0"></span><sup>&</sup>lt;sup>1</sup>Settings: CIFAR-100 with 128-width ConvNet under 10 images per class (IPC), Tiny-ImageNet with ResNet18 under 50 IPC, and ImageNet-1k with ResNet18 under 10 IPC.

<span id="page-1-0"></span>ior of the distilled dataset on the target task remains consistent with that of the complete dataset. The extremely compressed distilled dataset contains sufficiently valuable information and have the potential for fast model training, and have been become a popular choice for different downstream application, like federated learning [\[7,](#page-8-1) [22\]](#page-8-2), continual learning [\[14,](#page-8-3) [22,](#page-8-2) [40\]](#page-9-2), neural architecture search [\[24,](#page-8-4) [41,](#page-9-3) [42\]](#page-9-4) and 3D point clouds [\[30\]](#page-9-5).

A persistent problem that researchers have been working to solve [\[2,](#page-8-5) [9,](#page-8-6) [12,](#page-8-7) [42\]](#page-9-4) in DC is the substantial data synthesis overhead [\[19,](#page-8-8) [36\]](#page-9-6), which hinders its application in real-world large-scale datasets (*e.g.*, ImageNet-1k) training. Typical performance matching [\[15,](#page-8-9) [32,](#page-9-1) [44\]](#page-9-7) and trajectory matching [\[1,](#page-8-10) [3\]](#page-8-11) unroll recursive computation graphs, requiring substantial GPU memory and resulting in prohibitive training costs. Zhao *et al.* [\[42\]](#page-9-4) proposed gradient matching to address this, synthesizing distilled datasets by matching gradients from synthetic and real data in a single step. However, gradient computation and matching remain time-consuming [\[9\]](#page-8-6), leading to the proposal of distribution matching [\[41\]](#page-9-3). This method and its variants [\[31\]](#page-9-8) employ a network-based feature extractor to embed both synthetic and real data into a high-dimensional Hilbert space, then perform distribution matching. The training load for this direct, single-step process stems only from one gradient update of the synthetic data and the feature extractor [\[36\]](#page-9-6). Unfortunately, all of the above mentioned improved methods still have extremely large training overheads on the full 224×224 ImageNet-1k.

Recently, SRe2L [\[34\]](#page-9-9) accomplished data condensation for the first time on the full  $224\times224$  ImageNet-1k [\[18\]](#page-8-12), achieving Top-1 validation accuracy 21.3% with ResNet18 under IPC 10. This method outperformed the latest stateof-the-art TESLA [\[2\]](#page-8-5), which conducted on a low-resolution version of ImageNet-1k, by being  $16\times$  faster and improved performance by a margin of 13.6%. SRe2L is inspired by DeepInversion [\[33\]](#page-9-10) and aims to match statistics in Batch-Norm generated from synthetic and real data. We reevaluate the success of SRe2L through the lightweight "localmatch-global" essentially. The "local-match-global" refers to utilizing more comprehensive information (*e.g.*, statistics in BatchNorm), generated from the model using the complete dataset (global), to guide the parameter update of the distilled dataset (local).

However, such lightweight and effective matching in SRe2L is singular, depending on the particular layer (*i.e.*, BatchNorm), model (*i.e.*, ResNet18), and statistics (*i.e.*, channel mean/variance). Intuitively, sufficient "localmatch-global" matching can result in more accurate and rational supervision than a single one, further enhancing the generalization of the distilled dataset. We call this perspective "generalized matching" and suggest that the distilled dataset is likely to perform consistent with the complete

dataset on the evaluation model, provided that there is sufficient variety in backbones, layers, and statistics used for matching.

Inspired by this, we propose Generalized Various Backbone and Statistical Matching (G-VBSM) to fulfill "generalized matching". G-VBSM comprises three integral and effective parts named data densification (DD), generalized statistical matching (GSM), and generalized backbone matching (GBM). DD is utilized to ensure that the images within each class are linearly independent, thereby enhancing the (intra-class) diversity of the distilled dataset. This ultimately guarantees that "generalized matching" preserves the rich and diverse information within the synthetic data. GBM and GSM are designed to implement "generalized matching". Among them, GSM traverses the complete dataset without computing and updating the gradient, to record the statistics of Convolution at both the patch and channel levels. These statistics are subsequently utilized for matching during the data synthesis phase, in conjunction with the channel-level statistics in BatchNorm. Furthermore, GBM aims to ensure consistency between distilled and complete datasets across various backbones, enhancing matching sufficiency and leading to strong generalization in the evaluation phase. In particular, G-VBSM also ensures the efficiency of dataset condensation through a series of strategies, as mentioned in Sec. [3.](#page-2-0)

Extensive experiments on CIFAR-10, CIFAR-100, Tiny-ImageNet, and the full 224×224 ImageNet-1k, demonstrating that G-VBSM is the first algorithm that performs well on both small-scale and large-scale datasets. Specifically, we not only verify through ablation studies that GSM, GBM and DD are consistently reliable, but also achieve the highest 38.7%, 47.6% and 31.4% on CIFAR-100 (128 width ConvNet), Tiny-ImageNet (ResNet18), and the full 224×224 ImageNet-1k (ResNet18) under images per class (IPC) 10, 50 and 10, respectively, which outperforms all previous state-of-the-art (SOTA) methods by 3.9%, 6.5% and 10.1%, respectively.

## 2. Background

Dataset condensation (DC) represents a data synthesis procedure that aims to compress a complete, large dataset  $\mathcal{T} = \{ (X_i, y_i) \}_{i=1}^{|\mathcal{T}|}$  into a smaller, distilled dataset  $\mathcal{S} =$  $\{(\tilde{X}_i, \tilde{y}_i)\}_{i=1}^{|S|}$ , subject to  $|S| \ll |\mathcal{T}|$ , while ensuring that an arbitrary evaluation model  $f_{eval}(\cdot)$  trained on S yields similar performance to one trained on  $\mathcal T$ . Classical data distillation algorithms invariably require the candidate model  $f_{\text{cand}}(\cdot)$  to execute one or more steps on S to update its parameter  $\theta_{\text{cand}}$ , subsequently achieving matching in terms of performance [\[15,](#page-8-9) [32\]](#page-9-1), gradient [\[42\]](#page-9-4), trajectory [\[1,](#page-8-10) [2\]](#page-8-5), or dis-tribution [\[31,](#page-9-8) [41\]](#page-9-3). The process  $\theta_{\text{cand}} - \alpha \nabla_{\theta_{\text{cand}}} \ell(f_{\text{cand}}(X), \tilde{y})$ , where  $\ell(\cdot, \cdot)$  and  $(X, \tilde{y})$  represent the loss function and

<span id="page-2-2"></span><span id="page-2-1"></span>Image /page/2/Figure/0 description: This diagram illustrates a deep learning model architecture for knowledge distillation. The model consists of a backbone with three stages (Stage I, Stage II, Stage III) that processes a 'Complete Dataset'. A 'Distilled Dataset' is also used. The process involves 'Data Densification' and 'Generalized Statistical Matching'. The 'Generalized Backbone Matching' section shows a 'Distilled Dataset' undergoing 'Random Sampling' to select from candidate models like ResNet, MobileNet, ShuffleNet, and EfficientNet. An optional step involves calculating a norm-based metric for each candidate. Finally, an 'Ensemble' step is performed to obtain a 'Soft Label'. The 'Generalized Statistical Matching' section details how statistics (mean and variance) are extracted from 'BatchNorm' and 'Convolution' layers for both the 'Complete Dataset' and 'Distilled Dataset' at different levels (channel, other layers, patch level, and channel level) to calculate a loss based on KL divergence.

Figure 2. The overview of G-VBSM on the full 224×224 ImageNet-1k, which ensures the consistency between the distilled and the complete datasets across various backbones, layers and statistics via "generalized matching".

a batch sampled from  $S$ , respectively, is notably timeconsuming. Consequently, even the relatively swiftest distribution matching  $[20, 31]$  $[20, 31]$  $[20, 31]$  is slower than the recent proposed SRe2L [\[34\]](#page-9-9). In fact, SRe2L is the only workable way to achieve DC on the full  $224 \times 224$  ImageNet-1k, as it requires updating the parameters of the synthetic data only once per iteration.

SRe2L [\[34\]](#page-9-9) encompasses three incremental subprocesses: Squeeze, Recover, and Relabel. Squeeze is designed to train  $f_{\text{cand}}(\cdot)$  containing BatchNorm in a standard manner, aiming to record the global channel mean  $BN_l^{CM}$ and channel variance  $BN_l^{CV}$  (*l* refers to the index of the *l*th layer) via exponential moving average (EMA), extracted from  $T$ , for subsequent matching in Recover. In Recover after that, given the channel mean  $\mu_l(X)$  and channel variance  $\sigma_l^2(\tilde{X})$  in *l*-th BatchNorm obtained from *S*, the statistical matching loss function can be formulated as

$$
\mathcal{L}_{\text{BN}}(\tilde{X}) = \sum_{l} \left| \left| \mu_l(\tilde{X}) - \mathbf{BN}_l^{\text{CM}} \right| \right|_2 + \left| \left| \sigma_l^2(\tilde{X}) - \mathbf{BN}_l^{\text{CV}} \right| \right|_2.
$$
 (1)

Based on this, we can give the entire optimization objective in Recover as

$$
\underset{\tilde{X}}{\arg\min} \mathcal{L}_{\text{BN}}(\tilde{X}) + \ell(f_{\text{cand}}(\tilde{X}), y), \tag{2}
$$

where  $y$  denotes the ground truth label. Moreover, SRe2L assigns soft labels  $\tilde{y}$  to the synthetic data, utilizing the logitbased distillation [\[6,](#page-8-14) [8\]](#page-8-15) to improve the generalization ability of the distilled dataset. This can be denoted as

$$
\tilde{y} = \text{softmax}(f_{\text{cand}}(\tilde{X})/\tau), \tag{3}
$$

where  $\tau$  denotes the temperature to regulate the smoothness of the soft labels, thereby enhancing the distilled dataset's potential for generalization to unseen evaluation models. The generated soft label can be stored on disk using FKD [\[23\]](#page-8-16) so as not to defeat the purpose of DC. A crucial point of SRe2L is that BatchNorm calculates the statistics of the entire dataset using EMA, thereby providing a comprehensive and representative matching for the distilled dataset. Encouraged by this, our research focuses on applying sufficient "local-match-global" matching to achieve "generalized matching".

### <span id="page-2-0"></span>3. Method

The comprehensive and detailed framework of our proposed Generalized Various Backbone and Statistical Matching (G-VBSM) is illustrated in Fig. [2.](#page-2-1) In essence, G-VBSM employs the lightweight regularization strategy data densification (DD) to ensure both the diversity and density of the distilled dataset, ensuring that the potential of "generalized matching" can be fully exploited. Moreover, generalized backbone matching (GBM) and generalized statistical matching (GSM) are utilized to achieve "generalized matching" by performing "local-match-global" matching across various backbones, layers, and statistics. In particular, the efficiency and effectiveness of DD, GBM, GSM, SRe2L, and TESLA are illustrated in Fig. [5.](#page-5-0)

#### <span id="page-2-3"></span>3.1. Data Densification

As illustrated in Fig. [3,](#page-3-0) the synthetic data generated by SRe2L exhibit excessive similarity within the same class, leading to a lack of diversity. Consequently, even if "generalized matching" preserves sufficient valuable information within a single image, the aggregate information content across all images within the same class does not increase effectively, which ultimately prevents "generalized match-

<span id="page-3-3"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: The figure displays a line graph illustrating the intra-class cosine similarity across different class indices. The x-axis represents the class index, ranging from 0 to 1000. The y-axis represents the intra-class cosine similarity, with values ranging from 0.0 to 0.8. Four lines are plotted: 'SRe2L+DD' (light green, noisy), 'SRe2L' (light orange, noisy), 'SRe2L+DD (Smoothed)' (dashed orange), and 'SRe2L (Smoothed)' (dashed dark green). The smoothed lines show a general trend of the intra-class cosine similarity. The 'SRe2L (Smoothed)' line fluctuates between approximately 0.25 and 0.4, while the 'SRe2L+DD (Smoothed)' line fluctuates between approximately 0.1 and 0.25.

Figure 3. Visualization of the mean cosine similarity between pairwise samples within the same class on ImageNet-1k under IPC 10.

ing" from being sufficiently advantageous. Data densification (DD) is proposed to address this by ensuring the data  $\overline{X}$  has full rank in the batch dimension, thereby guaranteeing that samples in each class are linearly independent, and ultimately ensuring that the data is diverse and abundant to fully exploit the potential of "generalized matching".

To execute this pipeline,  $\tilde{X}$  first needs to be downsampled to reduce the computational cost of eigenvalue decomposition:

$$
\hat{X} = \text{AvgPool2d}(\tilde{X}), \ s.t. \ \hat{X} \in \mathbb{R}^{B \times C \times 32 \times 32}, \tag{4}
$$

where  $B$  and  $C$  represent the batch size and the number of channels, respectively. Afterward, we reshape  $X$  from  $B \times C \times 32 \times 32$  to  $B \times (1024C)$  and perform matrix multiplication in each class  $y$  to obtain the set of the Gram matrix  $\{\hat{X}_y \hat{X}_y^T\}_{y \in \mathcal{Y}}$ , where  $\hat{\mathcal{Y}}$  refers to a set of all classes existing in this batch. And  $\hat{X}_y$  is a subbatch with class y. Note that the alternative form  $\{\hat{X}_y^T \hat{X}_y\}_{y \in \mathcal{Y}}$  is not desirable, as it is applicable only for dimensionality reduction in feature dimensions, which is why we do not consider singular value decomposition (SVD). To render  $\hat{X}_y \hat{X}_y^T$  as full-rank as possible, we introduce the data densification loss in Eq. [5.](#page-3-1)

$$
\mathcal{L}_{\text{DD}}(\tilde{X}) = \sum_{y \in \mathcal{Y}} D_{\text{KL}}(\text{stop\_grad}(p(\Sigma_y/\tau))||p(\Sigma_y)), \quad (5)
$$

where  $\Sigma_y$ ,  $\tau$ , stop-grad(·) and  $p(\cdot)$  refer to the eigenvalues of  $\hat{X}_y \hat{X}_y^T$ , the temperature, the stop gradient operator and the softmax function, respectively. And  $D_{KL}(\cdot||\cdot)$  denotes Kullback-Leibler divergence. As demonstrated in Sec. [E,](#page-12-0) the diversity of the data is significantly enhanced by the employment of Eq. [5.](#page-3-1) In our experiment,  $\tau$  is set as 4 in default and we do not assign a deliberate weight (set to 1 by default) to  $\mathcal{L}_{DD}$  because  $\mathcal{L}_{DD} \equiv 0$  at the early 10% of the iterations. In other words, DD is quite stable and the optimization objective  $\argmin_{\{\Sigma_y\}_{y\in\mathcal{Y}}}\mathcal{L}_{\text{DD}}$  is equivalent to  $\arg\min_{\{\Sigma_y\}_{y\in\mathcal{Y}}}\sum_y \sigma^2(\Sigma_y).$ 

Technical Detail. A problem that warrants attention is that in the SRe2L's implementation [\[34\]](#page-9-9), having merely a single sample in each class of a batch indicates insufficient

<span id="page-3-2"></span>Image /page/3/Figure/9 description: This image illustrates a comparison between an 'Original Loop' and a 'Reorder Loop' in the context of image processing or machine learning. Both loops are organized by 'IPC' (likely referring to inter-process communication or a similar concept) and 'CLASS'. The 'Original Loop' shows batches of images arranged sequentially, with the 'Current Batch' containing actual images and subsequent batches ('Next Batch' to 'Last Batch') filled with placeholder gray squares, suggesting unprocessed or corrupted data. The 'Reorder Loop' presents a different arrangement where the 'Current Batch' also contains actual images, but the subsequent batches are interspersed with actual images from different stages (labeled '1st', 'N-2th', 'N-1th', 'Nth'), implying a reordering or restructuring of the data flow. An arrow indicates the transition from the original to the reordered loop.

Figure 4. The illustration of the original loop and the reorder loop.

to execute DD under the order of the original loop, as depicted in Fig. [4](#page-3-2) (Left). A simple solution is to translate the original loop to the reorder loop, as shown in Fig. [4](#page-3-2) (Right). However, our experiment on ResNet50 (*i.e.*, the evaluation model) substantiates that this solution suffers a 2.6% accuracy degradation (details can be found in Sec. [4.1\)](#page-5-1) on ImageNet-1k under IPC 10. The reason is that the number of classes in each iteration within the reorder loop is insufficient, preventing a batch have the ability to match the global statistics in BatchNorm (*i.e.*,  $\mathbf{BN}_l^{\text{CM}}$  and  $\mathbf{BN}_l^{\text{CV}}$ ). Motivated by score distillation sampling (SDS) [\[17\]](#page-8-17), we update the statistics during data synthesis via EMA to solve this issue, so that the statistics of all past batches can assist the statistics of the current batch match  $\mathbf{BN}_{l}^{\mathsf{CM}}$  and  $\mathbf{BN}_{l}^{\mathsf{CV}}$ :

$$
\mu_l^{\text{total}} = \alpha \mu_l^{\text{total}} + (1 - \alpha) \mu_l(\tilde{X}), \quad \sigma_l^{2, \text{total}} = \alpha \sigma_l^{2, \text{total}} + (1 - \alpha) \sigma_l^2(\tilde{X}),
$$

$$
\mathcal{L}_{\text{BN}}'(\tilde{X}) = \sum_l \|\mu_l(\tilde{X}) - \text{BN}_l^{\text{CM}} - \text{stop\_grad}(\mu_l(\tilde{X}) - \mu_l^{\text{total}})\|_2 + \|\sigma_l^2(\tilde{X}) - \text{BN}_l^{\text{CV}} - \text{stop\_grad}(\sigma_l^2(\tilde{X}) - \sigma_l^{2, \text{total}})\|_2.
$$
 $(6)$ 

The derivation of  $\mathcal{L}'_{BN}(\tilde{X})$  can be found in Appendix [B.](#page-11-0) We call this lightweight strategy as "match in the form of score distillation sampling" and have demonstrated its effectiveness and feasibility in our ablation studies.

#### <span id="page-3-1"></span>3.2. Generalized Backbone Matching

Performing data synthesis only on a single pre-trained model is not able to enjoy ensemble gains from various backbones. Meanwhile, classical DC algorithms such as MTT [\[1\]](#page-8-10) and FTD [\[3\]](#page-8-11) obtain performance improvements from multiple randomly initialized backbones. Therefore, introducing generalized backbone matching (GBM) to apply various backbones for data synthesis is a desirable choice for "generalized matching". It ensures a number of "local-match-global" matching nearly  $N<sub>b</sub> \times$  higher compared to just depending on a single backbone, where  $N<sub>b</sub>$ denotes the number of backbones. Regrettably, unrolling various backbone computational graphs in parallel for data synthesis is extremely expensive at the computational cost level. A solution is to randomly sample a backbone from the candidates in per iteration. This simple yet effective strategy not only ensures computational efficiency but also allows the data synthesis process to benefit from the diverse and multifaceted matching that the various backbones pro<span id="page-4-1"></span>vide. We denote this pipeline as

$$
f_{\text{cand}}(\cdot) \sim \mathcal{U}(\mathbb{S}), \mathbb{S} = \{ \text{ResNet18}, \cdots, \text{ShuffleNetV2-0.5} \}. \tag{7}
$$

To maintain backbone consistency in both the data synthesis and soft label generation phases, we introduce a backbone ensemble during soft label generation:

$$
\tilde{z} = \begin{cases} \sum_{f_{\text{cand}} \in \mathbb{S}} \frac{\sum_{g \in \mathbb{S}} ||g(\tilde{X})||_{\text{F}}}{|\mathbb{S}| ||f_{\text{cand}}(\tilde{X})||_{\text{F}}} \frac{f_{\text{cand}}(\tilde{X})}{|\mathbb{S}|}, & \text{w/ LN,} \\ \sum_{f_{\text{cand}} \in \mathbb{S}} \frac{f_{\text{cand}}(\tilde{X})}{|\mathbb{S}|}, & \text{w/o LN,} \end{cases} \quad (8)
$$

where  $|| \cdot ||_F$  and LN refer to the Frobenius norm and Logit Normalization, respectively. Thus, we can obtain the soft label by  $\tilde{y}$  = softmax $(\tilde{z}/\tau)$ . Particularly, the use of LN is optional, as demonstrated by our ablation studies; it is beneficial for ResNet18 but not for ResNet50 or ResNet101. Moreover, it's important to highlight that we apply a parallel mechanism for soft label generation since it provides a significantly lower computational cost, less than 1/30 of that required for data synthesis, thus making the computational overhead negligible.

#### 3.3. Generalized Statistical Matching

Only ensuring backbone diversity is insufficient to fully exploit the potential of "generalized matching". In this subsection, we aim to introduce additional statistics for matching during the data synthesis phase. Prior methods [\[33,](#page-9-10) [34\]](#page-9-9) only utilize BatchNorm since the presence of global information statistics within the pre-trained model, with no apparent solution ready for other layers (*e.g.*, Convolution). Retraining the model is the simplest way to address this problem, followed by updating the other layers' parameters through EMA. This approach is obviously impractical, as it defeats the purpose of using gradient descent. By contrast, we propose to allow the pre-trained model  $f_{eval}(\cdot)$  to loop through the training dataset  $T$  once without calculating the gradient to obtain the global statistics of Convolution, thus serving "local-match-global":

$$
\text{Conv}_{l}^{\text{CM}} = \frac{1}{|\mathcal{T}|} \sum_{i}^{|\mathcal{T}|} \text{CM}_{i}^{l}, \quad \text{Conv}_{l}^{\text{CV}} = \frac{1}{|\mathcal{T}|} \sum_{i}^{|\mathcal{T}|} \text{CV}_{i}^{l}, \\ \text{Conv}_{l}^{\text{PM}} = \frac{1}{|\mathcal{T}|} \sum_{i}^{|\mathcal{T}|} \text{PM}_{i}^{l}, \quad \text{Conv}_{l}^{\text{PV}} = \frac{1}{|\mathcal{T}|} \sum_{i}^{|\mathcal{T}|} \text{PV}_{i}^{l}. \tag{9}
$$

Here,  $\mathbf{CM}_i^l \in \mathbb{R}^{C_l}$ ,  $\mathbf{CV}_i^l \in \mathbb{R}^{C_l}$ ,  $\mathbf{PM}_i^l \in \mathbb{R}^{\lceil \frac{H}{N_l^p} \rceil \times \lceil \frac{W}{N_l^p} \rceil}$  and  $\mathbf{PV}_{i}^{l} \in \mathbb{R}^{\lceil \frac{H}{N_{l}^{p}} \rceil \times \lceil \frac{W}{N_{l}^{p}} \rceil}$  refer to the channel mean, the channel variance, the patch mean and the patch variance, respectively, for the *l*-th Convolution when the input to  $f_{cand}(\cdot)$ is the *i*-th batch, where  $C_l$  and  $\lceil \frac{H}{N_l^p} \rceil \times \lceil \frac{W}{N_l^p} \rceil$  denote the number of channels and patches of the  $l$ -th Convolution, respectively. We define  $N_l^p$  as 4, 4, 4 and 16 by default on CIFAR-10, CIFAR-100, Tiny-ImageNet and ImageNet-1k, respectively. After obtaining the global channel mean **Conv**<sup>CM</sup>, the global channel variance **Conv**<sup>CV</sup>, the global patch mean  $\text{Conv}_{l}^{\text{PM}}$  and the global patch variance  $\text{Conv}_{l}^{\text{PV}},$ we can store them in a disk thus avoiding secondary calculations. In the data synthesis phase, we introduce  $\mathcal{L}'_{Conv}(\tilde{X})$ in Eq. [10](#page-4-0) to accomplish joint matching with  $\mathcal{L}'_{BN}(\tilde{X})$ .

<span id="page-4-0"></span>
$$
\mathcal{L}'_{\text{Conv}}(\tilde{X}) = \sum_{l} ||\mu_l^c(\tilde{X}) - \text{Conv}_l^{\text{CM}} - \text{stop\_grad}(\mu_l^c(\tilde{X}) - \mu_l^{c,\text{total}})||_2   
 + ||\sigma_l^{c,2}(\tilde{X}) - \text{Conv}_l^{\text{CV}} - \text{stop\_grad}(\sigma_l^{c,2}(\tilde{X}) - \sigma_l^{c,2,\text{total}})||_2,   
 + ||\mu_l^p(\tilde{X}) - \text{Conv}_l^{\text{PM}} - \text{stop\_grad}(\mu_l^p(\tilde{X}) - \mu_l^{p,\text{total}})||_2   
 + ||\sigma_l^{p,2}(\tilde{X}) - \text{Conv}_l^{\text{PV}} - \text{stop\_grad}(\sigma_l^{p,2}(\tilde{X}) - \sigma_l^{p,2,\text{total}})||_2,
$$
(10)

where  $\mu_l^{c, \text{total}}$ ,  $\sigma_l^{c, 2, \text{total}}$ ,  $\mu_l^{p, \text{total}}$ , and  $\sigma_l^{p, 2, \text{total}}$  are each updated from the channel mean  $\mu_l^c(\tilde{X})$ , channel variance  $\sigma_l^{c,2}(\tilde{X})$ , patch mean  $\mu_l^p(\tilde{X})$ , and patch variance  $\sigma_l^{p,2}(\tilde{X})$ respectively, all obtained via EMA from the current batch. In experiments, we discovered that Eq. [10](#page-4-0) causes a sightly larger computational burden, so we randomly drop the matching of statistics with a probability of  $\beta_{dr}$  to ensure the efficiency of GSM.

Loss Function in the Evaluation Phase. Unlike DD, GBM and GSM are designed to create a distilled dataset that is enriched with information. Here, we introduce an enhancement to the loss function tailored specifically for the evaluation phase. Essentially, the evaluation phase is a knowledge distillation framework for transferring knowledge from a pre-trained model to the evaluation model. SRe2L utilizes  $D_{KL}(\tilde{y}||softmax(f_{eval}(\tilde{X})/\tau))$  as the loss function and experimentally illustrates that it improves performance by roughly 10%. As established in SRe2L, an increase in temperature  $\tau$  correlates with enhanced performance of the evaluation model. Inspired by this and  $\tau^2 D_{\text{KL}}(\text{softmax}(p)/\tau || \text{softmax}(q)/\tau)$  is equivalent to  $\frac{1}{2C}$ || $p - q$ || $\frac{2}{2}$  when  $\tau \to +\infty$  [\[10\]](#page-8-18), we introduce a novel loss function MSE+ $\gamma \times$ GT to avoid numerical error caused by the large  $\tau$  and improve the generalization of the distilled dataset without any additional overhead (ignore the weights  $\tau^2$  and  $\frac{1}{2C}$ ):

$$
\mathcal{L}_{\text{eval}}(\tilde{X}, \tilde{y}, y) = ||f_{\text{eval}}(\tilde{X}) - \tilde{z}||_2^2 - \gamma \mathbf{y} \log(\text{softmax}(f_{\text{eval}}(\tilde{X}))),
$$
\n(11)

where y represents the one-hot encoding (*w.r.t.* the ground truth label  $y$ ). As illustrated in Fig. [5,](#page-5-0) simply replacing the loss function with MSE+0.1×GT ( $\gamma$  is set as 0.1) in SRe2L improves the performance of ResNet18 (*i.e.*, the evaluation model) by a margin of 0.9% on ImageNet-1k under IPC 10.

## 4. Experiment

We perform comparison experiments on the large-scale dataset including the full  $224 \times 224$  ImageNet-1k [\[18\]](#page-8-12) and the small-scale datasets including Tiny-ImageNet [\[26\]](#page-8-19),

<span id="page-5-5"></span><span id="page-5-0"></span>Image /page/5/Figure/0 description: A scatter plot titled "Efficiency vs. Effectiveness of Different Methods (Under IPC 10, evaluation model is ResNet18)" displays the relationship between GPU Latency (ms) on the x-axis and Top-1 Validation Accuracy (%) on the y-axis. Several methods are plotted with different colored stars and labels indicating their accuracy and latency. The TESLA method is plotted at (4.32ms, 31.4%). SRe2L is at (3.10ms, 30.6%). SRe2L + (MSE + GT) is at (1.12ms, 27.8%). DD is at (1.12ms, 25.1%). DD + GBM is at (0.84ms, 22.4%). DD + GBM + GSM (βdr=1.0) + (MSE + GT) is at (0.83ms, 22.2%). DD + GBM + GSM (βdr=0.4) + (MSE + GT) is at (0.83ms, 21.3%). The last plotted point is at (12.89ms, 7.7%). A legend on the right side of the plot associates colors and star shapes with the different methods.

Figure 5. Comparison of the effectiveness and efficiency of G-VBSM components. Among them, "DD+GBM+GSM  $(\beta_{dr}=0.0)+(MSE+GT)$ " represents the comprehensive G-VBSM.

CIFAR-10/100 [\[11\]](#page-8-20). To highlight that our proposed G-VBSM is designed for large-scale datasets, all ablation experiments are performed on ImageNet-1k.

Hyperparameter Settings. We prioritize selecting various convolution-based backbone, ensuring the maximal difference in architecture, while also adhering to the criterion of minimizing the number of parameters, an approach empirically demonstrated to be superior [\[34,](#page-9-9) [38\]](#page-9-11). On ImageNet-1k, we skip the model pre-training phase by directly using Torchvision's open source pretraining weights [\[16\]](#page-8-21) of {ResNet18 [\[5\]](#page-8-22), MobileNetV2 [\[21\]](#page-8-23), EfficientNet-B0 [\[25\]](#page-8-24), ShuffleNetV2-0.5 [\[39\]](#page-9-12)}. For the remaining dataset, we all train {128-width ConvNet [\[39\]](#page-9-12), WRN-16-2 [\[37\]](#page-9-0), ResNet18 [\[5\]](#page-8-22), ShuffleNetV2-0.5 [\[39\]](#page-9-12), MobileNetV2-0.5 [\[21\]](#page-8-23)} from scratch with few epoch in the model pre-training phases. Gray cells in all tables represent the highest performance. Meanwhile, (R18), (R50), etc. in all tables, represent the evaluation models. More details about the remaining method hyperparameter settings and additional ablation studies can be found in Appendix [A](#page-10-0) and Appendix  $C & D$  $C & D$ , respectively.

<span id="page-5-1"></span>

### 4.1. Ablation Studies

The Trade-off between Efficiency and Effectiveness. The performance improvements and increased GPU latency for components of G-VBSM in comparison to the *baseline* SRe2L, are illustrated in Fig. [5.](#page-5-0) Clearly, DD, GBM, and GSM are highly effective in enhancing the generalization of distilled datasets. Compared with GSM, both DD and GBM are slightly lightweight and efficient. With DD and GBM alone – *i.e.*, DD+GBM+GSM  $(\beta_{dr}=1.0)$ +(MSE+GT) – the accuracy of this approach surpasses that of SRe2L by a significant margin of 6.5%. Additionally, the comprehensive G-VBSM, DD+GBM+GSM  $(\beta_{dr}=0.0)$ +(MSE+GT), further enhances the performance of DD+GBM+GSM  $(\beta_{dr} = 1.0) + (MSE + GT)$  by a notable margin of 3.6%. Note that MSE+ $\gamma \times$ GT ( $\gamma$  is set as 0.1) is also extremely critical, for SRe2L and DD+GBM boosting 0.9% and 2.7%, respectively.

<span id="page-5-2"></span>

| $\alpha$ Evaluation Model | ResNet18 | ResNet <sub>50</sub>                      |
|---------------------------|----------|-------------------------------------------|
|                           |          | $(MSE+0.1\times GT)$ $(MSE+0.1\times GT)$ |
| $\Omega$                  | $30.4\%$ | 31.9%                                     |
| 0.4                       | 31.4%    | 34.5%                                     |
| በ ጸ                       | 31.9%    | 36.4%                                     |

Table 1. ImageNet-1k Top-1 Acc. about different  $\alpha$  under IPC 10.

Matching in the Form of SDS. Benefiting from this matching form, G-VBSM can achieve the best performance with small batch sizes (*e.g.*, 40 in ImageNet-1k and 50 in CIFAR-100) in different settings, as demonstrated in our comparative experiments. Table [1](#page-5-2) demonstrates the impact of the factor  $\alpha$  for performing the EMA update on the final performance achieved by G-VBSM, where  $\alpha$ =0 indicates that  $\mathcal{L}_{\text{BN}}'$  degenerates to  $\mathcal{L}_{\text{BN}}$ . We can conclude that the SDS matching form is feasible and the generalization of the distilled dataset improves with increasing  $\alpha$ .

<span id="page-5-3"></span>

| Method        | <b>Evaluation Model</b> |          |             |           |
|---------------|-------------------------|----------|-------------|-----------|
|               | DeiT-Tiny               | ResNet18 | MobileNetV2 | Swin-Tiny |
| SRe2L         | 15.41%                  | 46.79%   | 36.59%      | 39.23%    |
| <b>G-VBSM</b> | 29.43%                  | 51.82%   | 48.66%      | 57.40%    |

Table 2. ImageNet-1k Top-1 Acc. on cross-architecture generalization under IPC 50.

Cross-Architecture Generalization. The evaluation of cross-architecture generalization is crucial in assessing the quality of distilled datasets. Unlike traditional methods [\[1,](#page-8-10) [15,](#page-8-9) [20\]](#page-8-13) which focus on CIFAR-100, our approach evaluates the effectiveness of the distilled dataset on ImageNet-1k, employing a suite of models with real-world applica-bility, including ResNet18 [\[5\]](#page-8-22), MobileNetV2 [\[21\]](#page-8-23), DeiT-Tiny [\[28\]](#page-8-25), and Swin-Tiny [\[13\]](#page-8-26). The experimental results are reported in Table [2.](#page-5-3) From Tables [7](#page-10-1) and [2,](#page-5-3) it is evident that the G-VBSM-synthetic dataset can effectively generalize across ResNet {18, 50, 101}, MobileNetV2, DeiT-Tiny, and Swin-Tiny architectures. Notably, DeiT-Tiny and Swin-Tiny, two architectures not encountered during the data synthesis phase, demonstrates significant proficiency with the accuracy 29.43% and 57.40%, outperforming the latest SOTA SRe2L by a margin of 14.01% and 18.17%, respectively.

<span id="page-5-4"></span>

| Candidates (Backbone) |             |                 |                  | Evaluation Model |          |
|-----------------------|-------------|-----------------|------------------|------------------|----------|
| ResNet18              | MobileNetV2 | EfficientNet-B0 | ShuffleNetV2-0.5 | ResNet18         | ResNet50 |
| ✓                     |             |                 |                  | $25.7%$          | $30.2%$  |
| ✓                     | ✓           |                 |                  | 27.2%            | 31.2%    |
| ✓                     | ✓           | ✓               |                  | 27.9%            | 32.0%    |
| ✓                     | ✓           |                 | ✓                | 31.4%            | 34.5%    |

Table 3. ImageNet-1k Top-1 Acc. about the number of candidate backbone in the soft label generation pipeline under IPC 10.

Ensemble in Soft Label Generation. In knowledge distillation, soft labels obtained through multiple teacher ensembles can effectively enhance the generalization of the student [\[35\]](#page-9-13). This observation is similarly corroborated in

<span id="page-6-5"></span><span id="page-6-1"></span>Image /page/6/Figure/0 description: The image contains three bar charts. The first bar chart, titled "Fixed Batch Size: 40 & Loss Function: MSE+0.1×GT", shows accuracy percentages on the y-axis ranging from 25 to 32, and beta values (βdr) on the x-axis: 0.8, 0.4, and 0.0. The accuracies for these beta values are approximately 28.5%, 30.5%, and 31.5% respectively. The second bar chart, titled "Fixed βdr=1.0 & Loss Function: MSE+0.1×GT", displays accuracy percentages on the y-axis from 31.0 to 31.5, and batch sizes on the x-axis: 40, 60, and 80. The accuracies for these batch sizes are approximately 31.4%, 31.3%, and 31.5% respectively. The third bar chart, titled "Fixed Batch Size: 40 & βdr=0.0", plots accuracy percentages on the y-axis from 0 to 30, and loss functions on the x-axis: KL, MSE+0.01×GT, MSE+0.1×GT, and MSE+1×GT. The corresponding accuracies are approximately 24.5%, 25.5%, 26.5%, and 6% respectively.

Figure 6. ImageNet-1k Top-1 Acc. of different loss function,  $\beta_{dr}$  and batch sizes under IPC 10.

<span id="page-6-3"></span>

| Dataset       | TPC. | MTT [1] (CW128)          | DataDAM [20] (CW128)     | <b>TESLA</b> [2] (R18)   | SRe2L(R18)     | <b>SRe2L (R50)</b> | <b>SRe2L (R101)</b> | $G-VBSM(R18)$  | $G-VBSM(R50)$  | $G-VBSM(R101)$ |
|---------------|------|--------------------------|--------------------------|--------------------------|----------------|--------------------|---------------------|----------------|----------------|----------------|
|               |      | $28.0 \pm 0.3$           | $28.7 \pm 0.3$           | $\overline{\phantom{0}}$ | $41.1 \pm 0.4$ | $42.2 \pm 0.5$     | $42.5 \pm 0.2$      | $47.6 \pm 0.3$ | $48.7 \pm 0.2$ | $48.8 \pm 0.4$ |
| Tiny-ImageNet | 100  |                          | <b>.</b>                 | $\overline{\phantom{0}}$ | $49.7 \pm 0.3$ | $51.2 \pm 0.4$     | $51.5 \pm 0.3$      | $51.0 \pm 0.4$ | $52.1 \pm 0.3$ | $52.3 \pm 0.1$ |
| ImageNet-1k   | 10   | $64.0 \pm 1.3^{\dagger}$ | $6.3 \pm 0.0$            | $7.7 \pm 0.1$            | $21.3 \pm 0.6$ | $28.4 \pm 0.1$     | $30.9 \pm 0.1$      | $31.4 \pm 0.5$ | $35.4 \pm 0.8$ | $38.2 \pm 0.4$ |
|               | 50   |                          | $15.5 \pm 0.2$           | $\overline{\phantom{0}}$ | $46.8 \pm 0.2$ | $55.6 \pm 0.3$     | $60.8 \pm 0.5$      | $51.8 \pm 0.4$ | $58.7 \pm 0.3$ | $61.0 \pm 0.4$ |
|               | 100  | <b>.</b>                 | $\overline{\phantom{a}}$ | $\overline{\phantom{0}}$ | $52.8 \pm 0.3$ | $61.0 \pm 0.4$     | $62.8 \pm 0.2$      | $55.7 \pm 0.4$ | $62.2 \pm 0.3$ | $63.7 \pm 0.2$ |

Table 4. Comparison with baseline models in Tiny-ImageNet and ImageNet-1k. <sup>†</sup> indicates the ImageNette dataset, which contains only 10 classes. DataDAM [\[20\]](#page-8-13) and TESLA [\[2\]](#page-8-5) use the downsampled 64×64 ImageNet-1k. We cite the experimental results from SRe2L [\[34\]](#page-9-9).

<span id="page-6-4"></span>

| Dataset                                                                                           | $_{\rm IPC}$ | Random | <b>Coreset Selection</b>                  |  |                                                                                                                                                                                          | Training Set Synthesis (CW128) |                | Herding K-Center Forgetting DC [42] DM [41] CAFE [31] KIP [15] MTT [1] DataDAM[20] G-VBSM   SRe2L                                                                                                                                                                                    |                                                                                                  | Training Set Synthesis (R18) Whole Dataset<br><b>G-VBSM</b> | (CW128)        |
|---------------------------------------------------------------------------------------------------|--------------|--------|-------------------------------------------|--|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------|----------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------|-------------------------------------------------------------|----------------|
| CIFAR-10                                                                                          |              |        |                                           |  |                                                                                                                                                                                          |                                |                | $10\ \  26.0\pm1.2\ \ 31.6\pm0.7\ \ 14.7\pm0.9\ \ 23.3\pm1.0\ \  44.9\pm0.5\ \ 48.9\pm0.6\ \ 50.9\pm0.5\ \ 46.1\pm0.7\ \ 65.3\pm0.7\ \ -54.2\pm0.8$<br>$50 43.4\pm 1.0 40.4\pm 0.6 27.0\pm 1.4 23.3\pm 1.1 53.9\pm 0.5 63.0\pm 0.4 62.3\pm 0.4 53.2\pm 0.7 71.6\pm 0.2 63.0\pm 0.4 $ | $46.5 \pm 0.7$   27.2 $\pm$ 0.5  <br>$54.3 \pm 0.3$ 47.5 $\pm 0.6$                               | $53.5 + 0.6$<br>$59.2 \pm 0.4$                              | $84.8 \pm 0.1$ |
| CIFAR-100 10   14.6±0.5 17.3±0.3 17.3±0.3 15.1±0.3   25.2±0.3 29.7±0.3 31.5±0.2 40.1±0.4 33.1±0.4 |              |        | $50 30.0 \pm 0.433.7 \pm 0.530.5 \pm 0.3$ |  | $1   4.2 \pm 0.3$ 8.3 $\pm$ 0.3 8.4 $\pm$ 0.3 4.5 $\pm$ 0.2 12.8 $\pm$ 0.3 11.4 $\pm$ 0.3 14.0 $\pm$ 0.3 12.0 $\pm$ 0.2 24.3 $\pm$ 0.3<br>$30.6 \pm 0.6$ 43.6 $\pm$ 0.4 47.7 $\pm$ 0.2 - |                                | $42.9 \pm 0.3$ | $14.5 \pm 0.5$<br>$34.8 + 0.5$<br>$49.4 + 0.3$                                                                                                                                                                                                                                       | $16.4 \pm 0.7$   $2.0 \pm 0.2$  <br>$38.7 \pm 0.2$ 31.6 $\pm$ 0.5<br>$45.7 + 0.4$ $ 49.5 + 0.3 $ | $25.9 \pm 0.5$<br>$59.5 \pm 0.4$<br>$65.0 \pm 0.5$          | $56.2 \pm 0.3$ |

Table 5. Comparison with baseline models on CIFAR-10/100. All methods, except for SRe2L and G-VBSM, use a 128-width ConvNet (CW128) for data synthesis and evaluation. G-VBSM utilizes {CW128, WRN-16-2, ResNet18 (R18), ShuffleNetV2-0.5, MobileNetV2- 0.5} for data synthesis and {CW128, R18} for evaluation. We cite the experimental results, except for SRe2L's, from DataDAM [\[20\]](#page-8-13).

dataset distillation, as evidenced in Table [3.](#page-5-4) The greater the number of models, the stronger the generalization of the distilled dataset. It is particularly interesting to point out that ShuffleNetV2-0.5 improves further by more than 2% even though the other three models have already achieved modest ensemble gains. We attribute this enhancement to the channel shuffle mechanism within ShuffleNetV2-0.5, which imposes a beneficial regularization constraint for G-VBSM.

<span id="page-6-0"></span>

| Logit<br>Normalization | Evaluation Model         |                  |                          |                  |
|------------------------|--------------------------|------------------|--------------------------|------------------|
|                        | ResNet18<br>(MSE+0.1×GT) | ResNet18<br>(KL) | ResNet50<br>(MSE+0.1×GT) | ResNet50<br>(KL) |
| Yes                    | $31.4%$                  | $25.4%$          | $34.5%$                  | $28.6%$          |
| No                     | $31.0%$                  | $25.1%$          | $35.4%$                  | $31.9%$          |

Table 6. ImageNet-1k Top-1 Acc. about the use of logit normalization under IPC 10.

Logit Normalization in Soft Label Generation. The aim of this strategy is to maintain the consistency of the logits (can be viewed as vectors) magnitude across all models within a high-dimensional space, thereby ensuring equal contribution of these models to the ultimate soft label for ensemble. This approach is not universally effective, as shown in Table [6.](#page-6-0) When the evaluation model parameter count is low (*e.g.*, ResNet18), it can enhance G-VBSM performance. Conversely, with a model like ResNet50, it may hinder performance, rendering G-VBSM uncompetitive. As

a result, in this work, ResNet {50, 101} and ViT-based models do not employ logit normalization, while the remaining models do.

Loss Function in the Evaluation Phase. As illustrated in Fig. [6,](#page-6-1) replacing  $D_{KL}(\cdot||\cdot)$  in SRe2L with MSE+ $\gamma \times GT$ demonstrates to be extremely effective. For example, with  $\beta_{\rm dr}$ =0.0 and batch size=40, adjusting  $\gamma$  to 0.01 and 0.1 enhances model performance during the evaluation phase by 1.4% and 6.0%, respectively. As a result, we employ a  $\gamma$  of 0.1 in all experiments conducted on ImageNet-1k. Furthermore, we observe that the distilled dataset's generalization is not significantly affected  $(\leq 2\%)$  by variations in either batch size or  $\beta_{dr}$ . This ensured that G-VBSM attain SOTA results with a minimal batch size 40 and  $\beta_{dr}$ =0.4 (*i.e.*, the default settings in ImageNet-1k).

<span id="page-6-2"></span>Image /page/6/Picture/12 description: The image displays a grid of images, divided into two main sections. The left section is labeled "Tiny-ImageNet" and contains four images arranged in a 2x2 grid. The right section is labeled "CIFAR-10 CIFAR-100" and contains twelve images arranged in a 3x4 grid. The images themselves appear to be generated or processed, with some exhibiting abstract patterns and distortions, while others show recognizable, albeit blurry, objects or scenes.

Figure 7. Synthetic data visualization on small-scale datasets.

<span id="page-7-1"></span>Synthetic Data Visualization. The visualization results for the large-scale (the full  $224 \times 224$  ImageNet-1k) and small-scale (CIFAR-10/100 and Tiny-ImageNet) datasets are displayed in Figs. [1](#page-0-1) and [7,](#page-6-2) respectively. On ImageNet-1k, the images synthesized by G-VBSM are more informative and abstract compared with those from SRe2L. Furthermore, on small-scale datasets, the distilled images obtained by G-VBSM markedly differ from the images presented in MTT and DataDAM's papers [\[1,](#page-8-10) [20\]](#page-8-13), illustrating that G-VBSM is an out-of-the ordinary algorithm. More synthetic data can be found in Appendix [I.](#page-14-0)

<span id="page-7-0"></span>Image /page/7/Figure/1 description: The image displays two scatter plots side-by-side, both titled "(Logit Embedding)". The left plot is labeled "GV-BSM" and the right plot is labeled "SRe2L". Both plots show data points colored and shaped according to a legend that lists ten categories: tench, english springer, cassette player, chain saw, church, horn, dustcart, gas pump, golf ball, and chute. The left plot, "GV-BSM", shows these ten categories clustered into several distinct groups, with many points within each cluster. The right plot, "SRe2L", shows the same ten categories, but the points are much more tightly clustered into ten separate, well-defined groups. Below the left plot, the text "after classifier and before softmax" is written in orange. Below the right plot, the same text is written in blue.

Figure 8. t-SNE visualization on ImageNet-1k.

Logit Embedding Distributions Visualization. We feed distilled datasets into a pre-trained model (*i.e.*, ResNet18) to obtain logit embeddings for t-SNE [\[29\]](#page-9-14) visualization. Note that unlike previous methods that used feature embeddings before the classifier layer, we utilize logit embeddings after the classifier layer for presentation. Fig. [8](#page-7-0) shows the distributions of G-VBSM and SRe2L logit embeddings. The logit embedding obtained by G-VBSM has less intra-class similarity than SRe2L, ensuring softer labels, preventing model overconfidence during the evaluation phase, and ultimately enhancing model generalization.

### 4.2. Large-Scale Dataset Comparison

Full  $224 \times 224$  ImageNet-1k. As illustrated in the ImageNet-1k part of Table [4,](#page-6-3) G-VBSM consistently outperforms SRe2L across all IPC {10, 50, 100}, with the evaluation model being ResNet {18, 50, 101}, indicating that G-VBSM is highly competitive in the large-scale dataset ImageNet-1k. In particular, under extreme compression scenarios, such as IPC 10, G-VBSM achieves significant performance increases – 10.1%, 7.0%, and 7.3% for ResNet18, ResNet50, and ResNet101, respectively – compared with SRe2L. In comparison to the latest SOTA classical method, DataDAM, just experimenting on the full  $64\times64$  ImageNet-1k, we exceed it by a margin of 23.7% under IPC 10. These results are particularly impressive when considered in the context of the large-scale dataset ImageNet-1k.

### 4.3. Small-Scale Dataset Comparison

Tiny-ImageNet. Similar to SRe2L, we evaluate the generalization of the distilled dataset on ResNet {18, 50, 101}. As presented in the Tiny-ImageNet part of Table [4,](#page-6-3) G-VBSM consistently surpasses the latest SOTA method SRe2L on different settings. Our method outperforms SRe2L, MTT and DataDAM by 6.5%, 19.6% and 18.9%, respectively, under IPC 50 applying 128-width ConvNet or ResNet18 as the evaluation model.

CIFAR-100. One of the disadvantages of SRe2L is its inability to compete with SOTA performance on this smallscale dataset benchmark (*i.e.*, evaluated with 128-width ConvNet and ResNet18), such as the performance on CIFAR-100 shown in Table [5,](#page-6-4) as a result, it fails to demonstrate that "local-match-global" (*e.g.*, statistical matching of BatchNorm) can rival or outperform traditional DC algorithms. In contrast, as shown in the CIFAR-100 part of Table [5,](#page-6-4) G-VBSM achieves accuracies 16.4% and 38.7%, the highest under IPC 1 and 10 on CIFAR-100 through "generalized matching", while maintaining the same number of epochs (*i.e.*, 1000) and the model (*i.e.*, 128-width ConvNet) for the evaluation phase as the traditional methods, outperforming the latest SOTA DataDAM by 1.9% and 3.9%, respectively. To the best of our knowledge, our proposed G-VBSM is the first algorithm that is strongly competitive on CIAR-100 as well as the full  $224\times224$  ImageNet-1k.

CIFAR-10. As illustrated in the CIFAR-10 part of Table [5,](#page-6-4) G-VBSM outperforms vanilla DC [\[32\]](#page-9-1), KIP [\[15\]](#page-8-9), and all coreset selection algorithms on the benchmark with 128-width ConvNet as the evaluation model. Meanwhile, on the benchmark with ResNet 18 as the evaluation model, G-VBSM outperforms the *baseline* SRe2L by 26.3% and 11.7% under IPC 10 and 50, respectively.

### 5. Conclusion

In this paper, we introduce a novel perspective termed "generalized matching" for dataset condensation. This posits that an abundance of lightweight "local-match-global" matching are more effective than a single "local-matchglobal" matching, and even surpass the precise and costly matching used by traditional methods. Consequently, we present G-VBSM, designed to ensure data densification while performing matching based on sufficient and various backbones, layers, and statistics. Experiments conducted on CIFAR-10/100, Tiny-ImageNet, and the full  $224 \times 224$ ImageNet-1k demonstrated that our method is the first algorithm to show strong performance across both small-scale and large-scale datasets. For future research, we plan to extend this approach's applicability to large-scale detection and segmentation datasets.

### References

- <span id="page-8-10"></span>[1] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Computer Vision and Pattern Recognition*, New Orleans, LA, USA, 2022. IEEE. [2,](#page-1-0) [4,](#page-3-3) [6,](#page-5-5) [7,](#page-6-5) [8,](#page-7-1) [1,](#page-0-2) [3](#page-2-2)
- <span id="page-8-5"></span>[2] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590, Honolulu, Hawaii, USA, 2023. PMLR. [2,](#page-1-0) [7](#page-6-5)
- <span id="page-8-11"></span>[3] Jiawei Du, Yidi Jiang, Vincent Y. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Computer Vision and Pattern Recognition*, pages 3749–3758, Vancouver, BC, Canada, 2023. IEEE. [2,](#page-1-0) [4](#page-3-3)
- <span id="page-8-0"></span>[4] Jianping Gou, Baosheng Yu, Stephen J Maybank, and Dacheng Tao. Knowledge distillation: A survey. *International Journal of Computer Vision*, 129(6):1789–1819, 2021. [1](#page-0-2)
- <span id="page-8-22"></span>[5] Kaiming He, Xiangyu Zhang, and Shaoqing Ren. Deep residual learning for image recognition. In *Computer Vision and Pattern Recognition*, pages 770–778, Las Vegas, NV, USA, 2016. IEEE. [6](#page-5-5)
- <span id="page-8-14"></span>[6] Geoffrey Hinton, Oriol Vinyals, and Jeff Dean. Distilling the knowledge in a neural network, 2015. [3,](#page-2-2) [1](#page-0-2)
- <span id="page-8-1"></span>[7] Shengyuan Hu, Jack Goetz, Kshitiz Malik, Hongyuan Zhan, Zhe Liu, and Yue Liu. Fedsynth: Gradient compression via synthetic data in federated learning. *arXiv preprint arXiv:2204.01273*, 2022. [2](#page-1-0)
- <span id="page-8-15"></span>[8] Tao Huang, Shan You, Fei Wang, Chen Qian, and Chang Xu. Knowledge distillation from a stronger teacher. In *Neural Information Processing Systems*, 2022. [3,](#page-2-2) [1](#page-0-2)
- <span id="page-8-6"></span>[9] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *International Conference on Machine Learning*, pages 11102–11118, Baltimore, Maryland, USA, 2022. PMLR. [2](#page-1-0)
- <span id="page-8-18"></span>[10] Taehyeon Kim, Jaehoon Oh, NakYil Kim, Sangwook Cho, and Se-Young Yun. Comparing kullback-leibler divergence and mean squared error loss in knowledge distillation. In *International Joint Conference on Artificial Intelligence*, Virtual Event, 2021. Morgan Kaufmann. [5](#page-4-1)
- <span id="page-8-20"></span>[11] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [6](#page-5-5)
- <span id="page-8-7"></span>[12] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. DREAM: efficient dataset distillation by representative matching. *arXiv preprint arXiv:2302.14416*, 2023. [2](#page-1-0)
- <span id="page-8-26"></span>[13] Ze Liu, Yutong Lin, Yue Cao, Han Hu, Yixuan Wei, Zheng Zhang, Stephen Lin, and Baining Guo. Swin transformer: Hierarchical vision transformer using shifted windows. In *International Conference on Computer Vision*, pages 10012– 10022, 2021. [6](#page-5-5)
- <span id="page-8-3"></span>[14] Wojciech Masarczyk and Ivona Tautkute. Reducing catastrophic forgetting with learning on synthetic data. In *Com-*

*puter Vision and Pattern Recognition Workshops*, pages 252– 253, Virtual Event, 2020. IEEE. [2](#page-1-0)

- <span id="page-8-9"></span>[15] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [2,](#page-1-0) [6,](#page-5-5) [7,](#page-6-5) [8](#page-7-1)
- <span id="page-8-21"></span>[16] Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, et al. Pytorch: An imperative style, high-performance deep learning library. In *Neural Information Processing Systems*, Vancouver, BC, Canada, 2019. [6,](#page-5-5) [2](#page-1-0)
- <span id="page-8-17"></span>[17] Ben Poole, Ajay Jain, Jonathan T. Barron, and Ben Mildenhall. Dreamfusion: Text-to-3d using 2d diffusion. In *International Conference on Learning Representations*, 2023. [4,](#page-3-3) [2](#page-1-0)
- <span id="page-8-12"></span>[18] Olga Russakovsky, Jia Deng, Hao Su, Jonathan Krause, Sanjeev Satheesh, Sean Ma, Zhiheng Huang, Andrej Karpathy, Aditya Khosla, Michael Bernstein, et al. Imagenet large scale visual recognition challenge. *International Journal of Computer Vision*, 115(3):211–252, 2015. [2,](#page-1-0) [5](#page-4-1)
- <span id="page-8-8"></span>[19] Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *arXiv preprint arXiv:2301.04272*, 2023. [2](#page-1-0)
- <span id="page-8-13"></span>[20] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *International Conference on Computer Vision*, pages 17097–17107, Paris, France, 2023. IEEE. [3,](#page-2-2) [6,](#page-5-5) [7,](#page-6-5) [8,](#page-7-1) [1](#page-0-2)
- <span id="page-8-23"></span>[21] Mark Sandler, Andrew G. Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *Computer Vision and Pattern Recognition*, pages 4510–4520, Salt Lake City, UT, USA, 2018. IEEE. [6](#page-5-5)
- <span id="page-8-2"></span>[22] Mattia Sangermano, Antonio Carta, Andrea Cossu, and Davide Bacciu. Sample condensation in online continual learning. In *International Joint Conference on Neural Networks*, pages 1–8, Padua, Italy, 2022. IEEE. [2](#page-1-0)
- <span id="page-8-16"></span>[23] Zhiqiang Shen and Eric Xing. A fast knowledge distillation framework for visual recognition. In *European Conference on Computer Vision*, pages 673–690. Springer, 2022. [3](#page-2-2)
- <span id="page-8-4"></span>[24] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth O. Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *International Conference on Machine Learning*, pages 9206–9216, Virtual Event, 2020. PMLR. [2](#page-1-0)
- <span id="page-8-24"></span>[25] Mingxing Tan and Quoc Le. Efficientnetv2: Smaller models and faster training. In *International Conference on Machine Learning*, pages 10096–10106, Virtual Event, 2021. PMLR. [6](#page-5-5)
- <span id="page-8-19"></span>[26] Amirhossein Tavanaei. Embedded encoder-decoder in convolutional networks towards explainable AI. 2020. [5](#page-4-1)
- <span id="page-8-27"></span>[27] Yonglong Tian, Dilip Krishnan, and Phillip Isola. Contrastive representation distillation. In *International Conference on Learning Representations*, 2019. [1](#page-0-2)
- <span id="page-8-25"></span>[28] Hugo Touvron, Matthieu Cord, Matthijs Douze, Francisco Massa, Alexandre Sablayrolles, and Hervé Jégou. Training

data-efficient image transformers & distillation through attention. In *International Conference on Machine Learning*, pages 10347–10357, Virtual Event, 2021. PMLR. [6](#page-5-5)

- <span id="page-9-14"></span>[29] Laurens Van der Maaten and Geoffrey Hinton. Visualizing data using t-SNE. *Journal of Machine Learning Research*, 9 (11), 2008. [8](#page-7-1)
- <span id="page-9-5"></span>[30] Chunwei Wang, Chao Ma, Ming Zhu, and Xiaokang Yang. Pointaugmenting: Cross-modal augmentation for 3d object detection. In *Computer Vision and Pattern Recognition*, pages 11794–11803, Virtual Event, 2021. [2](#page-1-0)
- <span id="page-9-8"></span>[31] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Computer Vision and Pattern Recognition*, pages 12196–12205, New Orleans, LA, USA, 2022. IEEE. [2,](#page-1-0) [3,](#page-2-2) [7](#page-6-5)
- <span id="page-9-1"></span>[32] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-2) [2,](#page-1-0) [8,](#page-7-1) [3](#page-2-2)
- <span id="page-9-10"></span>[33] Hongxu Yin, Pavlo Molchanov, Jose M Alvarez, Zhizhong Li, Arun Mallya, Derek Hoiem, Niraj K Jha, and Jan Kautz. Dreaming to distill: Data-free knowledge transfer via deepinversion. In *Computer Vision and Pattern Recognition*, pages 8715–8724, Virtual Event, 2020. IEEE. [2,](#page-1-0) [5](#page-4-1)
- <span id="page-9-9"></span>[34] Zeyuan Yin, Eric P. Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from A new perspective. In *Neural Information Processing Systems*. NIPS, 2023. [2,](#page-1-0) [3,](#page-2-2) [4,](#page-3-3) [5,](#page-4-1) [6,](#page-5-5) [7,](#page-6-5) [1](#page-0-2)
- <span id="page-9-13"></span>[35] Shan You, Chang Xu, Chao Xu, and Dacheng Tao. Learning from multiple teacher networks. In *International Conference on Knowledge Discovery and Data Mining*, pages 1285–1294, Halifax, NS, Canada, 2017. ACM. [6](#page-5-5)
- <span id="page-9-6"></span>[36] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023. [2](#page-1-0)
- <span id="page-9-0"></span>[37] Sergey Zagoruyko and Nikos Komodakis. Wide residual networks. In *British Machine Vision Conference*, York, UK, 2016. BMVA Press. [1,](#page-0-2) [6](#page-5-5)
- <span id="page-9-11"></span>[38] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. In *Computer Vision and Pattern Recognition*, Vancouver, BC, Canada, 2023. IEEE. [6,](#page-5-5) [5](#page-4-1)
- <span id="page-9-12"></span>[39] Xiangyu Zhang, Xinyu Zhou, Mengxiao Lin, and Jian Sun. Shufflenet: An extremely efficient convolutional neural network for mobile devices. In *Computer Vision and Pattern Recognition*, pages 6848–6856, 2018. [6](#page-5-5)
- <span id="page-9-2"></span>[40] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685, Virtual Event, 2021. PMLR. [2](#page-1-0)
- <span id="page-9-3"></span>[41] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Winter Conference on Applications of Computer Vision*, pages 6514–6523, Waikoloa, Hawaii, 2023. IEEE. [2,](#page-1-0) [7,](#page-6-5) [5](#page-4-1)
- <span id="page-9-4"></span>[42] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, Virtual Event, 2021. OpenReview.net. [2,](#page-1-0) [7](#page-6-5)

- <span id="page-9-15"></span>[43] Borui Zhao, Quan Cui, Renjie Song, Yiyu Qiu, and Jiajun Liang. Decoupled knowledge distillation. In *Computer Vision and Pattern Recognition*, pages 11953–11962, 2022. [1](#page-0-2)
- <span id="page-9-7"></span>[44] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *Neural Information Processing Systems*, New Orleans, LA, USA, 2022. NIPS. [2](#page-1-0)

# <span id="page-10-0"></span>A. Hyperparameter Settings

<span id="page-10-1"></span>

| Phase                         | Optimizer | Learning<br>Rate | Optimizer<br>Momentum        | Loss<br>Function                                                                                                         | Batch<br>Size | Epoch/<br>Iteration | Augmentation                 | Others                                                                              |
|-------------------------------|-----------|------------------|------------------------------|--------------------------------------------------------------------------------------------------------------------------|---------------|---------------------|------------------------------|-------------------------------------------------------------------------------------|
| Pre-trained model<br>training | SGD       | 0.1              | 0.9                          | cross-entropy                                                                                                            | 256           | Epoch<br>100        | RandomResizedCrop            | -                                                                                   |
| Data synthesis                | Adam      | 0.1              | $\beta_1, \beta_2=0.5,0.9$   | $\ell(f_{\text{cand}}(\tilde{X}), y) + \mathcal{L}'_{\text{BN}} + \mathcal{L}'_{\text{DD}} + \mathcal{L}'_{\text{Conv}}$ | 40            | Iteration<br>4000   | RandomResizedCrop            | $\beta_{dr}$ =0.4, Backbone={ResNet18,MobileNetV2,EfficientNet-B0,ShuffleNetV2-0.5} |
| Soft label<br>generation      | -         | -                | -                            | -                                                                                                                        | 1024          | Epoch<br>300        | RandomResizedCrop,<br>CutMix | Backbone={ResNet18,MobileNetV2,EfficientNet-B0,ShuffleNetV2-0.5}                    |
| Evaluation                    | AdamW     | 0.001            | $\beta_1, \beta_2=0.9,0.999$ | $MSE+0.1\times GT$                                                                                                       | 1024          | Epoch<br>300        | RandomResizedCrop,<br>CutMix | Evaluation Model={ResNet18, ResNet50, ResNet101, MobileNetV2,Swin-Tiny,DeiT-Tiny}   |

Table 7. G-VBSM hyperparameter settings on ImageNet-1k.

Table 8. G-VBSM hyperparameter settings on Tiny-ImageNet.

<span id="page-10-2"></span>

| Phase                         | Optimizer | Learning<br>Rate                                                          | Optimizer<br>Momentum         | Loss<br>Function                                                                                                        | Batch<br>Size | Epoch/<br>Iteration | Augmentation                       | Others                                                                                              |
|-------------------------------|-----------|---------------------------------------------------------------------------|-------------------------------|-------------------------------------------------------------------------------------------------------------------------|---------------|---------------------|------------------------------------|-----------------------------------------------------------------------------------------------------|
| Pre-trained model<br>training | SGD       | 0.1                                                                       | 0.9                           | cross-entropy                                                                                                           | 128           | Epoch<br>50         | RandomCrop<br>RandomHorizontalFlip | -                                                                                                   |
| Data synthesis                | Adam      | 0.05                                                                      | $\beta_1, \beta_2 = 0.5, 0.9$ | $\ell(f_{\text{cand}}(\tilde{X}), y) + \mathcal{L}'_{\text{BN}} + \mathcal{L}_{\text{DD}} + \mathcal{L}'_{\text{conv}}$ | 50            | Iteration<br>4000   | RandomResizedCrop                  | $\beta_{dr}$ =0.4, Backbone=<br>{ResNet18,128-width ConvNet,MobileNetV2 ,WRN-16-2,ShuffleNetV2-0.5} |
| Soft label<br>generation      | -         | -                                                                         | -                             | -                                                                                                                       | 128           | Epoch<br>100        | RandomResizedCrop,<br>CutMix       | Backbone=<br>{ResNet18,128-width ConvNet,MobileNetV2 ,WRN-16-2,ShuffleNetV2-0.5}                    |
| Evaluation                    | SGD       | 0.2, 0.1 and 0.1 on<br>ResNet18, ResNet50 and ResNet101<br>, respectively | 0.9                           | $MSE+0.1\times GT$                                                                                                      | 128           | Epoch<br>100        | RandomResizedCrop,<br>CutMix       | Evaluation Model=<br>{ResNet18,ResNet50,ResNet101}                                                  |

Table 9. G-VBSM hyperparameter settings on CIFAR-10/100.

<span id="page-10-3"></span>

| Phase                         | Optimizer                                                    | Learning<br>Rate                                             | Optimizer<br>Momentum                                                                   | Loss<br>Function                                                                  | Batch<br>Size                                         | Epoch/<br>Iteration                                              | Augmentation                       | Others                                                                                                 |
|-------------------------------|--------------------------------------------------------------|--------------------------------------------------------------|-----------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------|-------------------------------------------------------|------------------------------------------------------------------|------------------------------------|--------------------------------------------------------------------------------------------------------|
| Pre-trained model<br>training | SGD                                                          | 0.05                                                         | 0.9                                                                                     | cross-entropy                                                                     | 64                                                    | Epoch<br>50 and 5 on<br>CIFAR-100 and CIFAR-10<br>, respectively | RandomCrop<br>RandomHorizontalFlip | -                                                                                                      |
| Data synthesis                | Adam                                                         | 0.05                                                         | $\beta_1, \beta_2 = 0.5, 0.9$                                                           | $\ell(f_{\text{cand}}(X),y) + L'_{\text{BN}} + L'_{\text{DD}} + L'_{\text{Conv}}$ | 50                                                    | Iteration<br>4000                                                | RandomResizedCrop                  | $\beta_{dr}$ =0.4. Backbone=<br>{ResNet18,128-width ConvNet,MobileNetV2,<br>WRN-16-2,ShuffleNetV2-0.5} |
| Soft label<br>generation      | -                                                            | -                                                            | -                                                                                       |                                                                                   | $64$ or $ \mathcal{S} $<br>$( \mathcal{S}  \leq 100)$ | Epoch<br>1000                                                    | RandomResizedCrop,<br>CutMix       | Backbone=<br>{ResNet18,128-width ConvNet,MobileNetV2,<br>WRN-16-2,ShuffleNetV2-0.5}                    |
| Evaluation                    | SGD and AdamW on<br>CIFAR-100 and CIFAR-10<br>, respectively | 0.1 and 0.001 on<br>CIFAR-100 and CIFAR-10<br>, respectively | 0.9 and $\beta_1$ , $\beta_2$ =0.9,0.999 on<br>CIFAR-100 and CIFAR-10<br>, respectively | $MSE+0.15\times GT$                                                               | 64 or $ S $<br>$( \mathcal{S}  \leq 100)$             | Epoch<br>1000                                                    | RandomResizedCrop,<br>CutMix       | Evaluation Model=<br>{128-width ConvNet, ResNet18}                                                     |

Here, we present the hyperparameter settings of G-VBSM in the pre-trained model training (*i.e.*, Squeeze in SRe2L), the data synthesis (*i.e.*, Recover in SRe2L), the soft label generation (*i.e.*, Relabel in SRe2L), and the evaluation phases in Tables [7](#page-10-1) (ImageNet-1k), [8](#page-10-2) (Tiny-ImageNet), and [9](#page-10-3) (CIFAR-10/CIFAR-100). The hyperparameter settings for the ImageNet-1k and Tiny-ImageNet datasets predominantly adhere to SRe2L [\[34\]](#page-9-9). Furthermore, the settings for CIFAR-10/CIFAR-100 draw upon the classical knowledge distillation framework [\[6,](#page-8-14) [8,](#page-8-15) [27,](#page-8-27) [43\]](#page-9-15). Notably, we employ the same evaluation model (*i.e.* 128 width ConvNet) and identical number of epochs (*i.e.* 1000) during the evaluation phase on CIFAR-10/CIFAR-100 as those used in the prior dataset distillation approaches [\[1,](#page-8-10) [20\]](#page-8-13), ensuring experimental fairness.

The Consistency of Backbone used in Data Synthesis and Soft Label Generation. In all experiments conducted on different datasets, we maintain the same architectures and identical parameters of the pre-trained model for data synthesis and soft label generation. Similar to SRe2L, our exploratory studies revealed that preserving the consistency of the backbone results in the best generalization ability for the distilled dataset.

**The Hyperparameter**  $\beta_{\text{dr}}$ **.** Given G-VBSM's computational efficiency on ImageNet-1k under IPC 10, which serves as the benchmark for the majority of our ablation studies, we set  $\beta_{dr}$  to 0.0 for this specific benchmark. For the remaining experiments, including Tiny-ImageNet, CIFAR-10 and CIFAR-100,  $\beta_{dr}$  is set to 0.4.

The Weights of the Loss Function. To underscore the generalization and applicability of our proposed G-VBSM, we intentionally avoid setting the weights of any loss functions, except for MSE+ $\gamma \times GT$ , in a bespoke manner. To be specific, the weights for both  $\mathcal{L}'_{BN}$  and  $\mathcal{L}'_{Conv}$  are established at 0.01 for ImageNet-1k, consistent with the weight of  $\mathcal{L}_{BN}$ , which SRe2L is set as 0.01 for ImageNet-1k. Since we transposed the loop (*i.e.*, translate the original loop to the reorder loop), the weights for  $\mathcal{L}'_{\rm{BN}}$  and  $\mathcal{L}'_{\rm{Conv}}$  are set at 0.01 for Tiny-ImageNet, different from SRe2L, which assigns a weight of 1.0 to  $\mathcal{L}_{\rm{BN}}$  for the same dataset. Due to SRe2L was not evaluated on CIFAR-10 and CIFAR-100, we empirically adjusted the weights for  $\mathcal{L}'_{BN}$  and  $\mathcal{L}'_{\text{Conv}}$  to 0.01 in our experiments. Additionally, the weight of  $\mathcal{L}_{\text{DD}}$  is consistently applied at 1.0 across all datasets. Through empirical validation in our experiments, we establish that the performance of the distilled dataset – when the weight of  $\mathcal{L}_{DD}$ is configured as  $\{0.1, 1.0, 10.0\}$  – remains precisely identical.

# <span id="page-11-0"></span>B. The Derivation of "match in the form of score distillation sampling"

Our proposed novel loss function, denoted as  $\mathcal{L}'_{BN}(\tilde{X})$ , draws inspiration from score distillation sampling (SDS) [\[17\]](#page-8-17). It is employed to mitigate the performance degradation that arises from directly substituting the original loop with the reorder loop. Here, we give the detailed derivation of  $\mathcal{L}'_{BN}(\tilde{X})$  to facilitate understanding. First, we give the gradient of the original loss function  $\mathcal{L}_{BN}(X)$  with respect to X:

$$
\frac{\partial \mathcal{L}_{BN}(\tilde{X})}{\partial \tilde{X}} = \sum_{l} \frac{\partial \mu_{l}(\tilde{X})}{\partial \tilde{X}} \frac{\mu_{l}(\tilde{X}) - \mathbf{BN}_{l}^{CM}}{||\mu_{l}(\tilde{X}) - \mathbf{BN}_{l}^{CM}||_{2}} + \frac{\partial \sigma_{l}^{2}(\tilde{X})}{\partial \tilde{X}} \frac{\sigma_{l}^{2}(\tilde{X}) - \mathbf{BN}_{l}^{CV}}{||\sigma_{l}^{2}(\tilde{X}) - \mathbf{BN}_{l}^{CV}||_{2}}.
$$
\n(12)

<span id="page-11-2"></span>In Eq. [12,](#page-11-2)  $\frac{\mu_l(\tilde{X}) - BN_l^{\text{CM}}}{\|\mu_l(\tilde{X}) - BN_l^{\text{CM}}\|_2}$  and  $\frac{\sigma_l^2(\tilde{X}) - BN_l^{\text{CV}}}{\|\sigma_l^2(\tilde{X}) - BN_l^{\text{CV}}\|_2}$  are unit vectors that dominate the direction of the gradient descent in the data synthesis process. As analyzed in Sec[.3.1,](#page-2-3) the precise global statistics generated by all past batches are feasible to assist in matching between the limited statistics generated by the current batch and  $BN_l^{CM}$  as well as  $BN_l^{CV}$ . We utilize EMA to update the statistics  $\mu_l^{\text{total}}$  and  $\sigma_l^{\text{2,total}}$  generated by all past batches:

$$
\mu_l^{\text{total}} = \alpha \mu_l^{\text{total}} + (1 - \alpha) \mu_l(\tilde{X}), \sigma_l^{\text{2,total}} = \alpha \sigma_l^{\text{2,total}} + (1 - \alpha) \sigma_l^2(\tilde{X}). \tag{13}
$$

We can achieve the SDS-like loss by simply replacing  $\frac{\mu_l(\tilde{X}) - BN_l^{CM}}{||\mu_l(\tilde{X}) - BN_l^{CM}||_2}$  with  $\frac{\mu_l^{total}(\tilde{X}) - BN_l^{CM}}{||\mu_l^{total}(\tilde{X}) - BN_l^{CM}||_2}$  and  $\frac{\sigma_l^2(\tilde{X}) - BN_l^{CV}}{||\sigma_l^2(\tilde{X}) - BN_l^{CV}||_2}$  with  $\frac{\sigma_l^2, \text{total}(\tilde{X}) - \text{BN}_l^{\text{CV}}}{\|\sigma_l^2, \text{total}(\tilde{X}) - \text{BN}_l^{\text{CV}}\|_2}$ . In this way, the direction of gradient descent for data synthesis is no longer determined by the imprecise statistics of the single current batch, which ultimately improves the quality of the synthetic data and its ability to generalize to unseen evaluation models. In practice, we can implement the replacement easily with Pytorch's  $[16]$  stop grad( $\cdot$ ) operator:

$$
\mathcal{L}'_{\text{BN}}(\tilde{X}) = \sum_{l} ||\mu_l(\tilde{X}) - \mathbf{BN}_l^{\text{CM}} - \text{stop\_grad}(\mu_l(\tilde{X}) - \mu_l^{\text{total}})||_2 + ||\sigma_l^2(\tilde{X}) - \mathbf{BN}_l^{\text{CV}} - \text{stop\_grad}(\sigma_l^2(\tilde{X}) - \sigma_l^{2,\text{total}})||_2.
$$
 (14)

We can find the gradient of  $\mathcal{L}'_{BN}(\tilde{X})$  with respect to  $\tilde{X}$  by derivation as

$$
\frac{\partial \mathcal{L}'_{BN}(\tilde{X})}{\partial \tilde{X}} = \sum_{l} \frac{\partial \mu_{l}(\tilde{X})}{\partial \tilde{X}} \frac{\mu_{l}^{\text{total}}(\tilde{X}) - \mathbf{BN}_{l}^{\text{CM}}}{||\mu_{l}^{\text{total}}(\tilde{X}) - \mathbf{BN}_{l}^{\text{CM}}||_{2}} + \frac{\partial \sigma_{l}^{2}(\tilde{X})}{\partial \tilde{X}} \frac{\sigma_{l}^{2,\text{total}}(\tilde{X}) - \mathbf{BN}_{l}^{\text{CV}}}{||\sigma_{l}^{2,\text{total}}(\tilde{X}) - \mathbf{BN}_{l}^{\text{CV}}||_{2}}.
$$
\n(15)

Clearly,  $\mathcal{L}'_{BN}(\tilde{X})$  effectively achieves our primary purpose. Additionally, our ablation studies in Sec. [4.1](#page-5-1) empirically demonstrate that the "match in the form of score distillation sampling" strategy is remarkable.

<span id="page-11-3"></span><span id="page-11-1"></span>

### C. Additional Ablation Experiments on ImageNet-1k

| $\mathcal{L}'_{\text{BN}}$ | $\mathcal{L}_{\text{Conv}}'$ | ResNet18 | ResNet50 | ResNet101 |
|----------------------------|------------------------------|----------|----------|-----------|
| ✓                          |                              | 27.8%    | 33.4%    | 35.5%     |
|                            | ✓                            | 24.0%    | 26.1%    | 30.4%     |
| ✓                          | ✓                            | 31.4%    | 35.4%    | 38.2%     |

Table 10. Ablation study about  $\mathcal{L}'_{\text{BN}}$  and  $\mathcal{L}'_{\text{Conv}}$  in the synthetic data phase on ImageNet-1k. Meanwhile, ResNet {18, 50, 101} represent evaluation models.

This section presents ablation experiments for  $\mathcal{L}'_{BN}$  and  $\mathcal{L}'_{Conv}$  to underscore their equal importance. As illustrated in Table [10,](#page-11-3) omitting either  $\mathcal{L}'_{\rm{BN}}$  or  $\mathcal{L}'_{\rm{Conv}}$  from the entire loss function during data synthesis phase leads to a performance decline. Hence, conducting the "local-match-global" matching via both  $\mathcal{L}'_{BN}$  and  $\mathcal{L}'_{Conv}$  is essential.

<span id="page-12-1"></span>

## D. Exploratory Studies on CIFAR-10/100

The Choice of Candidate Backbones in GBM. Under IPC 10 on CIFAR-100, we evaluated candidate backbones {ResNet18, MobileNetV2, WRN-16-2, ShuffleNetV2-0.5}, omitting the 128-width ConvNet, during the data synthesis and soft label generation phases. In addition, we kept other hyperparameters consistent as shown in Table [9](#page-10-3) and obtained the 128-width ConvNet evaluation performance as 32.8%. However, incorporating the 128-width ConvNet into the candidate backbones increased the accuracy from 32.8% to 38.7%. It's important to mention that the 128-width ConvNet solely utilizes GroupNorm, not BatchNorm. This enhancement to 38.7% was accomplished by relying solely on statistics within Convolution, substantiating that statistics in BatchNorm may not be the only option in the data synthesis phase.

| Evaluation Model \ Epoch | 5     | 10    | 20    | 40    |
|--------------------------|-------|-------|-------|-------|
| 128-width ConvNet        | 46.5% | 45.8% | 42.5% | 42.1% |

<span id="page-12-2"></span>Table 11. Ablation study about the number of epochs in pre-trained model training phase. We maintain the consistency of other hyperparameters as presented in Table [9.](#page-10-3)

The Number of Epochs in the Pre-Trained Model Training Phase. As illustrated in Table [11,](#page-12-2) fewer pre-training epochs on CIFAR-10 enhance the generalization of the distilled dataset. This finding could provide an explanation for the remarkable performance achieved by traditional algorithms [\[1,](#page-8-10) [32\]](#page-9-1) on CIFAR-10, even when they employ models with random initializations. As a result, this ablation study informed our decision to pre-train models on CIFAR-10 for only 5 epochs. More important, as our experiments transition from ImageNet-1k to Tiny-ImageNet to CIFAR-100, and finally to CIFAR-10, the dataset complexity reduces, and the ideal number of pre-training epochs successively decreases from 100 to 50, to 50, and finally to 5. The most intuitive and empirical extrapolation is due to the complexity of the dataset, and we believe that this conclusion may be of some inspiration to other researchers.

<span id="page-12-0"></span>

## E. Additional Explanation of Data Densification

<span id="page-12-3"></span>Here we provide theoretical proofs within Eq. [16](#page-12-3) to show that entropy  $H(\Sigma_y/\tau)$  ( $\tau > 1$ ) is greater than  $H(\Sigma_y)$ , thus increasing  $H(\Sigma_y)$  through Eq. [5,](#page-3-1) which ultimately improves the entropy of the eigenvalues and ensures the diversity of data.

$$
H(z/\tau) - H(z) = (\tau - 1) \frac{\partial H}{\partial \tau}(\tau'), \text{ s.t. } 1 \leq \tau' \leq \tau, \text{ define } z = \Sigma_y \text{ for convenience}
$$
\n
$$
= -\frac{\tau - 1}{\tau'} \sum_{i} [(\log(\frac{e^{z_i/\tau'}}{\sum_{j} e^{z_j/\tau'}}) + 1)(\frac{e^{z_i/\tau'}}{\sum_{j} e^{z_j/\tau'}})
$$
\n
$$
(\frac{-z_i/\tau'}{\sum_{j} (e^{z_j/\tau'}) + \sum_{j} (e^{z_j/\tau'} z_j/\tau')}{\sum_{j} e^{z_j/\tau'}})] = -\frac{\tau - 1}{\tau'} [\sum_{i} \log(\frac{e^{z_i/\tau'}}{\sum_{j} e^{z_j/\tau'}})
$$
\n
$$
(\frac{e^{z_i/\tau'}}{\sum_{j} e^{z_j/\tau'}})(\frac{-z_i/\tau'}{\sum_{j} (e^{z_j/\tau'}) + \sum_{j} (e^{z_j/\tau'} z_j/\tau')}{\sum_{j} e^{z_j/\tau'}}) - \frac{\sum_{j} z_j/\tau' e^{z_j/\tau'}}{\sum_{j} e^{z_j/\tau'}} + \frac{\sum_{j} z_j/\tau' e^{z_j/\tau'}}{\sum_{j} e^{z_j/\tau'}}] = -\frac{\tau - 1}{\tau'} \sum_{j} (\frac{e^{z_i/\tau'}}{\sum_{j} e^{z_j/\tau'}}) \log(\frac{e^{z_i/\tau'}}{\sum_{j} e^{z_j/\tau'}})
$$
\n
$$
[-z_i/\tau' + \frac{\sum_{j} e^{z_j/\tau'} z_j/\tau'}{\sum_{j} e^{z_j/\tau'}}] = \frac{\tau - 1}{\tau'} [\sum_{i} (z_i/\tau')^2 e^{z_i/\tau'} - \sum_{i} (z_i/\tau' e^{z_i/\tau'})]
$$
\n
$$
\frac{\sum_{i} (z_i/\tau' e^{z_i/\tau'})}{\sum_{i} (e^{z_i/\tau'})} + \log(\sum_{i} e^{z_i/\tau'}) (\sum_{i} z_i/\tau' e^{z_i/\tau'} - \sum_{i} z_i/\tau' e^{z_i/\tau'})] > 0.
$$

# <span id="page-13-0"></span>F. Statistics Visualization

Image /page/13/Figure/1 description: The image displays five line graphs, each illustrating statistics for different neural network architectures: ResNet18, ShuffleNetV2, MobileNetV2, WRN-16-2, and a 128-width ConvNet. All graphs are titled with the architecture name and "(Sigmoid Normalized)". The y-axis for all graphs is labeled "Sigmoid Normalized Value", ranging from 0.0 to 1.0, except for the 128-width ConvNet graph which ranges from 0.500 to 0.675. The x-axis is labeled "Index" for all graphs. Each graph plots multiple lines representing different statistics: "BN channel mean's mean", "BN channel mean's var", "BN channel var's mean", "Conv channel mean's mean", "Conv channel mean's var", and "Conv channel var's mean". The ResNet18 graph shows data points from index 0 to 20. The ShuffleNetV2 graph shows data points from index 0 to 50. The MobileNetV2 graph shows data points from index 0 to 50. The WRN-16-2 graph shows data points from index 0 to 12.5. The 128-width ConvNet graph shows data points from index 1.0 to 3.0. The overall caption below the graphs reads "Statistics visualization across various backbones {ResNet18, ShuffleNetV2, MobileNetV2, WRN-16-2, 128-width Conv".

Figure 9. Statistics visualization across various backbones {ResNet18, ShuffleNetV2, MobileNetV2, WRN-16-2, 128-width ConvNet} on CIFAR-100.

It is important to emphasize that Convolution and BatchNorm offer different supervisory information in statistics. As a result, G-VBSM is more effective than SRe2L when optimized statistics in Convolution and BatchNorm together. For clarity, we visualized the statistics in the pre-trained models {ResNet18, 128-width ConvNet, MobileNetV2, WRN-16-2, ShuffleNetV2-  $0.5$  on CIFAR-100 in Fig. [9.](#page-13-0) In each subplot of Fig. [9,](#page-13-0) the horizontal axis denotes the layer index (with orthogonal indexes for Convolution and BatchNorm), while the vertical axis shows the post-sigmoid normalized result. Due to the extensive dimensions of channel mean and channel variance, we calculate only their mean and variance for visualization. Furthermore, since BatchNorm is not included in 128-width ConvNet, only Convolution statistics are visualized. From Fig. [9,](#page-13-0) we can

<span id="page-14-2"></span>conclude that the values of the statistics in Convolution and BatchNorm are different in any model, which indicates that G-VBSM is significant and can enhance the generalization of the distilled dataset as demonstrated in Fig. [5.](#page-5-0)

Image /page/14/Figure/1 description: This is a line graph showing the Top-1 Val Accuracy (%) on the y-axis against the Step on the x-axis. There are two lines plotted: SRe2L, represented by a red line with square markers, and G-VBSM, represented by a blue dashed line with circular markers. The SRe2L line starts at approximately 8.67% at Step 1, dips to 7.67% at Step 2, and then steadily increases to 21.67% at Step 10. The G-VBSM line starts at approximately 9.67% at Step 1, increases to 15.67% at Step 3, and continues to increase to 30.67% at Step 10. The G-VBSM line consistently shows higher accuracy than the SRe2L line throughout the steps.

Figure 10. The experimental result of continual learning application on ImageNet-1k under IPC 10.

# G. Continual Learning Application

Many data condensation algorithms [\[34,](#page-9-9) [38,](#page-9-11) [41\]](#page-9-3) have evaluated the generalization ability of distilled datasets in continual learning. We follow the class-incremental learning approach<sup>[2](#page-14-1)</sup> adopted in DM [\[41\]](#page-9-3) for performing this task. Similar to the ablation studies of the main paper, our experiments are conducted on the full  $224 \times 224$  ImageNet-1k, underscoring that G-VBSM is intended for use with large-scale datasets. We conduct class incremental learning with ResNet18 on the 10-step class-incremental learning strategy under 10 IPC. The experimental results are illustrated in Fig. [10.](#page-14-2) We can discover that G-VBSM significantly outperforms SRe2L, thus confirming the usefulness and effectiveness of G-VBSM.

# <span id="page-14-3"></span>H. Date Free Pruning Application

| Data Free Pruning                | IPC 10 | IPC 10   | <b>IPC 50</b> | <b>IPC 50</b> |
|----------------------------------|--------|----------|---------------|---------------|
| (ImageNet-1k, VGG-A, 50% Pruned) | SRe2L  | SRe2L+DD | SRe2L         | SRe2L+DD      |
| Top-1 Val Accuracy               | 12.5%  | 12.9%    | 31.7%         | 32.8%         |

Table 12. The experimental result of data free pruning application on IamgeNet-1k.

*Data Free Pruning of Slimming* aims to reduce the model size and decrease the run-time memory footprint simultaneously for convolutional nerual network. We argue that the distilled dataset facilitates efficient data-free pruning. To substantiate this claim, we conduct experiments on ImageNet-1k with IPC 10. As illustrated in Table [12,](#page-14-3) data densification enhances downstream knowledge transfer as above by increasing synthesized data diversity and significantly boosting SRe2L.

<span id="page-14-0"></span>

## I. Synthetic Data Visualization

We provide more visualization results on synthetic data randomly selected from G-VBSM in Figs. [11](#page-15-0) (ImageNet-1k), [12](#page-16-0) (Tiny-ImageNet), [13](#page-17-0) (CIFAR-100) and [14](#page-18-0) (CIFAR-10).

<span id="page-14-1"></span> $2$ This involves gradually increasing the number of classes and combining previously stored data with newly acquired data to train a model from scratch.

<span id="page-15-0"></span>Image /page/15/Picture/0 description: This is a grid of 25 images, each depicting a surreal and abstract scene with a variety of objects and creatures. The images appear to be AI-generated, with a dreamlike quality and distorted perspectives. Some images feature animals like birds and dogs, while others include human-like figures, furniture, and natural landscapes. The overall aesthetic is colorful and fragmented, with a mix of realistic and fantastical elements.

Figure 11. Synthetic data visualization on ImageNet-1k randomly selected from G-VBSM.

<span id="page-16-0"></span>Image /page/16/Picture/0 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each smaller image is a colorful, abstract representation of a scene or object, with some images appearing distorted or pixelated. The overall impression is a mosaic of diverse visual elements, possibly representing data points or generated images from a machine learning model.

Figure 12. Synthetic data visualization on Tiny-ImageNet randomly selected from G-VBSM.

<span id="page-17-0"></span>Image /page/17/Picture/0 description: The image displays a grid of 20x20 small images, totaling 400 images. Each small image appears to be a synthetic data visualization, likely generated by a model, as suggested by the caption below the main image. The individual images are diverse, showing various abstract patterns and recognizable objects or parts of objects, such as animal faces, eyes, and natural elements like plants and water. The overall impression is a colorful and complex mosaic of generated visual content.

Figure 13. Synthetic data visualization on CIFAR-100 randomly selected from G-VBSM.

<span id="page-18-0"></span>Image /page/18/Picture/0 description: The image displays a grid of many small, colorful squares, each containing a distorted or abstract image. The overall impression is a mosaic of fragmented visuals, with colors ranging from blues and greens to browns and whites. Some squares appear to depict blurry shapes that could be interpreted as objects or scenes, while others are more abstract patterns of color and light. The arrangement suggests a collection of data samples or generated images, possibly from a machine learning experiment.

Figure 14. Synthetic data visualization on CIFAR-10 randomly selected from G-VBSM.
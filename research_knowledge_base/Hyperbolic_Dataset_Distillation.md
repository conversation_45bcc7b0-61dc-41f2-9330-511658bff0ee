# Hyperbolic Dataset Distillation

Wenyuan <PERSON><sup>∗</sup> <PERSON><PERSON><PERSON>e <PERSON>kkaido <PERSON>

## Abstract

To address the computational and storage challenges posed by large-scale datasets in deep learning, dataset distillation has been proposed to synthesize a compact dataset that replaces the original while maintaining comparable model performance. Unlike optimization-based approaches that require costly bi-level optimization, distribution matching (DM) methods improve efficiency by aligning the distributions of synthetic and original data, thereby eliminating nested optimization. DM achieves high computational efficiency and has emerged as a promising solution. However, existing DM methods, constrained to Euclidean space, treat data as independent and identically distributed points, overlooking complex geometric and hierarchical relationships. To overcome this limitation, we propose a novel hyperbolic dataset distillation method, termed HDD. Hyperbolic space, characterized by negative curvature and exponential volume growth with distance, naturally models hierarchical and tree-like structures. HDD embeds features extracted by a shallow network into the Lorentz hyperbolic space, where the discrepancy between synthetic and original data is measured by the hyperbolic (geodesic) distance between their centroids. By optimizing this distance, the hierarchical structure is explicitly integrated into the distillation process, guiding synthetic samples to gravitate towards the root-centric regions of the original data distribution while preserving their underlying geometric characteristics. Furthermore, we find that pruning in hyperbolic space requires only 20% of the distilled core set to retain model performance, while significantly improving training stability. Notably, HDD is seamlessly compatible with most existing DM methods, and extensive experiments on different datasets validate its effectiveness. To the best of our knowledge, this is the first work to incorporate the hyperbolic space into the dataset distillation process. The code will be released upon acceptance.

# 1 Introduction

Recently, deep neural networks (DNNs) have demonstrated outstanding performance across a wide range of tasks. However, the continuous performance improvement has led to increasingly large datasets, which in turn have escalated storage costs and computational demands, emerging as key bottlenecks in the further advancement of deep learning. To address this issue, dataset distillation (DD) has been proposed [\[57\]](#page-12-0). By condensing the information of the original dataset, DD synthesizes a significantly smaller artificial dataset while striving to achieve comparable model performance. Beyond this, DD has also been widely applied in various domains, such as neural architecture search [\[68,](#page-12-1) [14,](#page-9-0) [43,](#page-11-0) [53\]](#page-12-2), continual learning [\[20,](#page-10-0) [61\]](#page-12-3), and privacy protection [\[13,](#page-9-1) [6,](#page-9-2) [32,](#page-10-1) [33\]](#page-10-2).

To avoid the bi-level optimization problem of the DD methods, matching-based dataset distillation methods have been proposed. Currently, they can be broadly classified into three categories: gradient matching [\[69\]](#page-12-4), trajectory matching [\[4,](#page-9-3) [15\]](#page-9-4), and distribution matching [\[68,](#page-12-1) [64,](#page-12-5) [70\]](#page-12-6). The first two approaches can be collectively referred to as optimization-driven dataset distillation methods. Although

<sup>∗</sup>Correspondence to: Guang Li <<EMAIL>>

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image illustrates an example of a dataset tree, showing how data can be embedded in hyperbolic space. On the left, a tree structure is depicted with nodes containing images of cats. The tree has leaf nodes at the top and a root node at the bottom. Arrows indicate relationships: red arrows labeled 'Part', blue arrows labeled 'Less visible angles', and dotted arrows labeled 'Other hierarchical branches'. To the right, a 3D representation of Lorentz Hyperbolic Space shows a curved surface with points connected in a tree-like structure, originating from a red point labeled 'origin'. An arrow labeled 'Embedding' points from this hyperbolic space representation to a collection of cat images labeled 'The 'cat' class in the CIFAR-10 dataset'.

Figure 1: An example of hierarchical representation in hyperbolic space using the 'Cat' class from the CIFAR-10 Dataset. Hyperbolic space naturally encodes hierarchical structures. In this context, samples located near the root node often represent the category prototype more effectively, while those situated at higher hierarchical levels (closer to the leaf nodes) tend to contain noisier or specific information, such as object parts or less visible angles.

these methods have achieved promising performance, their reliance on expensive optimization or nested gradients often incurs high computational costs, which hinders their scalability and broader application. In contrast, Zhao et al. proposed a distribution matching approach, which mitigates the need for expensive optimization by aligning the feature distributions encoded by neural networks from both the original and synthetic datasets, thereby reducing computational overhead [\[68\]](#page-12-1). Despite its advantages, distribution matching methods generally underperform optimization-driven approaches in terms of final model accuracy.

Distribution matching is typically divided into instance-level (point-wise) matching [\[56,](#page-12-7) [48\]](#page-11-1) and moment matching [\[68,](#page-12-1) [64\]](#page-12-5). The central challenge lies in defining an effective metric to quantify the distributional discrepancy between the original and synthetic datasets. Point-wise matching is performed in Euclidean space by comparing feature representations using Mean Squared Error (MSE) on a per-sample basis. However, MSE primarily focuses on local alignment (e.g., pixel-wise similarity within samples) and tends to overlook the global semantic structure embedded in high-dimensional manifolds. In contrast, moment matching employs Maximum Mean Discrepancy (MMD) as a metric, which enables effective measurement of overall distribution differences in a Reproducing Kernel Hilbert Space (RKHS). Although both MSE and MMD attempt to reduce the distribution gap between original and synthetic datasets, they overlook a critical aspect: the hierarchical (or tree-like) structure inherent in dataset samples [\[60,](#page-12-8) [47\]](#page-11-2), as illustrated in Figure [1.](#page-1-0) Under the hierarchy, the significance of samples varies—lower-level samples (closer to the root) tend to better represent the category prototype, whereas higher-level samples (closer to the leaves) often carry more irrelevant or noisy information [\[24,](#page-10-3) [21\]](#page-10-4). Treating all samples as independent and identically distributed (i.i.d.) when using MSE or MMD may thus degrade distillation performance.

To address the above-mentioned limitation, we introduce hyperbolic space as the distribution space for samples and propose a novel hyperbolic dataset distillation (HDD) method. Unlike Euclidean and Hilbert spaces, hyperbolic space is characterized by negative curvature, whose geometric constraints offer a continuous approximation of hierarchical tree-like structures, effectively capturing complex hierarchical relationships [\[12,](#page-9-5) [16\]](#page-9-6). In hyperbolic space, the centroid of a data distribution is the point that minimizes the total of squared hyperbolic distances to all sample points. Due to the unique geometric properties of hyperbolic space, higher-level samples exert less influence on the centroid, naturally biasing it toward lower-level samples that are more representative of category prototypes. Nevertheless, the centroid still integrates the influence of all samples, which allows it to encode the overall geometric structure of the dataset. Based on this observation, we propose to match the distribution centroids of the original and synthetic datasets in hyperbolic space. This strategy aims to minimize distributional discrepancies, particularly concerning lower-level (prototype-like) samples, while also preserving the global geometric structure of the dataset [\[27\]](#page-10-5). The motivation of this study is that samples within a dataset contribute unequally to the overall representation depending on their hierarchical level, and the distillation process should be designed to reflect this imbalance. Notably, HDD is fully compatible with most existing dataset distillation methods. To the best of our knowledge, this is the first work to introduce hyperbolic space into the dataset distillation framework.

To summarize, our contributions are as follows:

- We propose hyperbolic dataset distillation (HDD), a novel method that incorporates hyperbolic geometry into dataset distillation to enable hierarchical sample weighting, effectively capturing semantic structures at multiple levels. Additionally, HDD aligns the global geometric distributions of the original and distilled datasets by matching their centroids in hyperbolic space.
- We analyze the contributions of samples at different hierarchical levels to the overall training loss, providing insights into their respective roles during distillation.
- Extensive experiments on diverse benchmarks, including Fashion-MNIST, SVHN, CIFAR-10, CIFAR-100, and TinyImageNet, demonstrate the effectiveness of our method. Additionally, our model also performs well in cross-architecture experiments.
- Furthermore, we apply hierarchical pruning to the original dataset by utilizing only the pruned subset for distribution alignment. Empirical results show that merely 20% of the original data suffices to preserve performance, underscoring the efficacy of hierarchical structuring within hyperbolic space.

# 2 Related Works

Dataset Distillation. Existing DD methods can be broadly categorized into three categories: gradient matching, trajectory matching, and distribution matching [\[36,](#page-11-3) [31,](#page-10-6) [63,](#page-12-9) [42\]](#page-11-4). Gradient matching [\[69,](#page-12-4) [67\]](#page-12-10) seeks to preserve critical information by minimizing the discrepancy between the gradients induced by synthetic and original samples during model training. Trajectory matching [\[4,](#page-9-3) [15,](#page-9-4) [23,](#page-10-7) [9,](#page-9-7) [34,](#page-10-8) [35\]](#page-10-9) achieves fine-grained knowledge transfer by aligning the training trajectories of network parameters. Distribution matching [\[68,](#page-12-1) [64,](#page-12-5) [70\]](#page-12-6) improves the representational capacity of synthetic samples by aligning their statistical distributions with those of original data in feature or activation spaces. Recently, generative-based dataset distillation [\[19,](#page-10-10) [51,](#page-11-5) [52,](#page-11-6) [38,](#page-11-7) [39,](#page-11-8) [40\]](#page-11-9) and decoupling optimizationbased methods [\[62,](#page-12-11) [54,](#page-12-12) [50\]](#page-11-10) have been proposed, accelerating advancements in the field of dataset distillation. In this work, we introduce hyperbolic space into dataset distillation by leveraging its inherent negative curvature to impose the tree-like hierarchy of the original dataset onto synthetic data, thereby offering a novel perspective to address the fundamental challenges in dataset distillation.

Hyperbolic Machine Learning. Hyperbolic space naturally encodes hierarchical data, which has attracted considerable interest in machine learning. It was first widely adopted in graph neural networks [\[5,](#page-9-8) [66,](#page-12-13) [18,](#page-10-11) [2\]](#page-9-9) to more effectively capture hierarchical and complex graph structures. In computer vision and multimodal tasks, hyperbolic geometry has also been applied to metric learning [\[17,](#page-9-10) [46,](#page-11-11) [41\]](#page-11-12), generation [\[8,](#page-9-11) [3\]](#page-9-12), recognition [\[26\]](#page-10-12), and segmentation [\[1\]](#page-9-13). As fully hyperbolic architectures have matured, hyperbolic-based vision methods have become increasingly sophisticated. In this work, we introduce hyperbolic space into dataset distillation for the first time, leveraging its hierarchical properties to assign differentiated weights to samples.

# 3 Method

## 3.1 Preliminaries

**Problem Definition.** Consider a large-scale original dataset  $\mathcal{R} = \{(r_i^{\text{real}}, t_i^{\text{real}})\}_{i=1}^{|\mathcal{R}|}$ , where  $r_i^{\text{real}}$ represents the  $i$ -th sample instance from the original dataset,  $t_i^{\text{real}}$  represents the corresponding label of the sample  $r_i^{\text{real}}$  in the original dataset, and  $|\mathcal{R}|$  is the total number of samples in the original dataset. The goal of dataset distillation is to construct a significantly smaller synthetic dataset  $S = \left\{ (s_j^{\text{syn}}, t_j^{\text{syn}}) \right\}_{j=1}^{|S|}$ , where  $s_j^{\text{syn}}$  represents the j-th synthetic sample instance,  $t_j^{\text{syn}}$  represents the corresponding label of the synthetic sample  $s_j^{syn}$ , and  $|S|$  is the total number of samples in the synthetic dataset, with  $|S| \ll |\mathcal{R}|$ . Such that a model trained on S (denoted  $\theta_{syn}$ ) exhibits performance comparable to one trained on  $R$  (denoted  $\theta_{\text{real}}$ ) when evaluated on previously unseen samples. Formally, let  $P_T$  denote the true data distribution and  $\ell$  a loss function (e.g., cross-entropy),

then the optimal synthetic dataset is obtained by minimizing the discrepancy in performance between  $\theta_{syn}$  and  $\theta_{real}$  as follows:

<span id="page-3-0"></span>
$$
S^* = \arg\min_{E_{(x,t)} \sim P_T} \left\| \ell\left(\theta_{\text{syn}}(x), t\right) - \ell\left(\theta_{\text{real}}(x), t\right) \right\|,\tag{1}
$$

where  $(x, t)$  represents a sample pair drawn from the true data distribution  $P_T$ , with x denoting the data instance and  $t$  its corresponding label.

To tackle Eq. [\(1\)](#page-3-0), previous optimization-based methods have primarily focused on two key strategies. One approach refines  $S$  through meta-learning, while the other aligns gradients or parameters between S and  $\mathcal R$ . Nevertheless, both strategies necessitate a bi-level optimization structure, which is computationally demanding due to the need for nested gradient computations. In contrast, DM [\[68\]](#page-12-1) introduces distribution matching as a more efficient alternative by aligning the feature distributions between  $S$  and  $R$ . Within this framework, the optimization of the condensed dataset is typically categorized into instance-level matching and moment matching. Instance level matching overlooks the global semantic structure of the data, making it a suboptimal choice. In contrast, moment matching is formulated as follows:

$$
S^* = \arg\min_{\mathbb{E}_{\phi_Q \sim \mathcal{P}_{\phi_Q}}} \left\| \frac{1}{|\mathcal{R}|} \sum_{i=1}^{|\mathcal{R}|} \phi_Q(r_i^{\text{real}}) - \frac{1}{|\mathcal{S}|} \sum_{j=1}^{|\mathcal{S}|} \phi_Q(s_j^{syn}) \right\|^2, \tag{2}
$$

where  $\phi_Q \sim \mathcal{P}_{\phi_Q}$  represents a feature extractor randomly sampled from the distribution  $\mathcal{P}_{\phi_Q}$ (typically instantiated by a randomly initialized DNN without the final linear classification layer).

**Hyperbolic Geometry.** In hyperbolic geometry, the  $n$ -dimensional hyperbolic space is formally defined as a Riemannian manifold  $(M^n, g_K)$  endowed with a constant negative curvature  $K < 0$ , where  $M<sup>n</sup>$  denotes the underlying manifold and  $g<sub>K</sub>$  is the Riemannian metric that characterizes its geometric structure. To facilitate efficient and numerically stable computations, we adopt the Lorentz model  $\mathbb{L}_K^n = (\mathcal{L}, g_L)$ , which embeds the hyperbolic space into an  $(n+1)$ -dimensional Minkowski space. Here,  $\mathcal L$  represents the set of points satisfying the constraint  $\langle x, x \rangle_{\mathcal L} = 1/K$ , and the metric tensor is given by  $g_K = diag([-1, 1_n])$ , the Lorentzian manifold can be defined as follows:

$$
\mathcal{L} := \left\{ \mathbf{x} \in \mathbb{R}^{n+1} \; \middle| \; \langle \mathbf{x}, \mathbf{x} \rangle_{\mathcal{L}} = \frac{1}{K}, \; x_t > 0 \right\}.
$$
 (3)

Each point  $\mathbf{x} \in \mathbb{L}^n$  can be expressed as a vector  $\mathbf{x} = [x_t \ x_s]^T$ , where  $x_t > 0$  is referred to as the time component and  $x_s \in \mathbb{R}^n$  as the spatial component. The Lorentzian inner product is defined as:

<span id="page-3-1"></span>
$$
\langle \mathbf{x}, \mathbf{y} \rangle_{\mathcal{L}} := -x_t y_t + x_s^\top y_s. \tag{4}
$$

Although several isometrically equivalent models exist in hyperbolic geometry, such as the Poincaré ball, the Klein model, and the upper half-space model, our work primarily utilizes the Lorentz model due to its analytical tractability and improved numerical behavior. The relevant details are explained in detail in Appendix A.

### 3.2 Hyperbolic Dataset Distillation for Distribution Matching

Given the original dataset  $\mathcal{R} = \{(r_i^{\text{real}}, t_i^{\text{real}})\}_{i=1}^{|\mathcal{R}|}$  and the synthetic dataset for update  $\mathcal{S} =$  $\{(s_j^{\text{syn}}, t_j^{\text{syn}})\}_{j=1}^{|\mathcal{S}|}$  (where  $|\mathcal{S}| \ll |\mathcal{R}|$ ), we first encode the data through a frozen pre-trained encoder  $\phi$ , generating corresponding feature vectors  $v_i^{\text{real}}$  and  $v_j^{\text{syn}}$  as follows:

$$
v_i^{\text{real}} = \phi(r_i^{\text{real}}), v_j^{\text{syn}} = \phi(s_j^{\text{syn}}).
$$
\n(5)

This process projects both original samples  $r_i^{\text{real}}$  and synthetic samples  $s_j^{\text{syn}}$  into Euclidean feature space parameterized by  $\phi$ . Subsequently, we map each sample from both the original and synthetic datasets to the hyperbolic space via the exponential map, yielding the hyperbolic embeddings  $z_i^{\text{real}}$ and  $z_j^{\text{syn}}$ , respectively, as follows:

$$
z_i^{\text{real}} = \exp_{p_0}(v_i^{\text{real}}) = \cosh\left(\|v_i^{\text{real}}\|\right) p_0 + \sinh\left(\|v_i^{\text{real}}\|\right) \frac{v_i^{\text{real}}}{\|v_i^{\text{real}}\|},\tag{6}
$$

$$
z_j^{\rm syn} = \exp_{p_0}(v_j^{\rm syn}) = \cosh(||v_j^{\rm syn}||) \, p_0 \, + \, \sinh(||v_j^{\rm syn}||) \, \frac{v_j^{\rm syn}}{||v_j^{\rm syn}|}. \tag{7}
$$

Here,  $||v|| = \sqrt{\langle v, v \rangle}$  denotes the norm induced by the Minkowski inner product, and  $p_0$  represents the base point in the hyperbolic space, which is defined as:

$$
p_0 = \left(\sqrt{-\frac{1}{K}}, 0, 0, \dots, 0\right),\tag{8}
$$

where  $K < 0$  denotes the curvature of the hyperbolic space.

To facilitate subsequent analysis, we collect all hyperbolic embeddings of the original and synthetic datasets into two sets:

$$
Z^{\text{real}} = \{ z_i^{\text{real}}, t_i^{\text{real}} \}_{i=1}^{|\mathcal{R}|}, \quad Z^{\text{syn}} = \{ z_j^{\text{syn}}, t_j^{\text{syn}} \}_{j=1}^{|\mathcal{S}|}.
$$
 (9)

Here,  $Z^{\text{real}}$  and  $Z^{\text{syn}}$  denote the sample points in the hyperbolic space corresponding to the real and synthetic samples, respectively. Unlike distribution matching methods in Euclidean space, in hyperbolic space, the distributional center of each embedded dataset is characterized by its Riemannian (Karcher) mean. We define their Riemannian means in the Lorentz model as:

$$
\bar{z}^{\text{real}} = \underset{z \in \mathbb{H}_K^n}{\arg \min} \sum_{i=1}^{|\mathcal{R}|} d_L^2(z, z_i^{\text{real}}), \quad \bar{z}^{\text{syn}} = \underset{z \in \mathbb{H}_K^n}{\arg \min} \sum_{j=1}^{|\mathcal{S}|} d_L^2(z, z_j^{\text{syn}}), \tag{10}
$$

where z denotes a point in the Lorentzian hyperbolic space  $\mathbb{L}_K^n$  over which the Riemannian mean is optimized, and the Lorentzian hyperbolic distance  $d<sub>L</sub>$  on the upper-sheet hyperboloid model is

$$
d_L(m, n) = \frac{1}{\sqrt{-K}} \operatorname{acosh}(-K \langle m, n \rangle_{\mathcal{L}}), \quad m, n \in \mathbb{L}_K^n,
$$
\n(11)

and  $\langle \cdot, \cdot \rangle_c$  denotes the Minkowski inner product, as shown in Eq. [\(4\)](#page-3-1).

To mitigate the extra computational overhead introduced by iterative procedures, we employ the centroid approximation approach proposed by Law et al. [\[27\]](#page-10-5), which can be expressed as follows:

$$
\mathbf{c} = \sqrt{-K} \cdot \frac{\bar{\mathbf{z}}}{\sqrt{|\langle \bar{\mathbf{z}}, \bar{\mathbf{z}} \rangle_{\mathcal{L}}| + \epsilon}}, \quad \text{where} \quad \bar{\mathbf{z}} = \frac{1}{n} \sum_{i=1}^{n} \mathbf{z}_i.
$$
 (12)

Here,  $z_i \in \mathbb{R}^{d+1}$  denotes the input vectors in Lorentzian hyperbolic space,  $\bar{z}$  is their Euclidean average. The curvature constant  $K < 0$  reflects the negative curvature of the hyperbolic space. A small  $\epsilon > 0$  is added for numerical stability.

Finally, we define the distribution matching loss as the Lorentzian hyperbolic distance between the two means as follows:

$$
\mathcal{L}_{\text{Lhd}} = \lambda \, d_L \left( \bar{z}^{\text{real}}, \, \bar{z}^{\text{syn}} \right) = \frac{\lambda}{\sqrt{-K}} \operatorname{acosh} \left( -K \left( \bar{z}^{\text{real}}, \, \bar{z}^{\text{syn}} \right)_{\mathcal{L}} \right),\tag{13}
$$

where  $\lambda$  is the gradient scaling factor. In hyperbolic space, the centroid distribution is close to the origin, resulting in a very small distance between the centroids of the original dataset and the synthetic dataset. Additional parameters are required for amplification, as detailed in Appendix B.

Based on this loss, our objective in distribution matching is reformulated as minimizing the Lorentzian hyperbolic distance between the Riemannian means of the original and synthetic datasets:

$$
S^* = \arg\min_{\mathbb{E}_{\phi_Q \sim \mathcal{P}_{\phi_Q}}} [\lambda \, d_L(\bar{z}^{\text{real}}, \bar{z}^{\text{syn}})]. \tag{14}
$$

As illustrated in Fig. [2,](#page-5-0) the framework of HDD is presented. It is compatible with a broad range of existing distribution matching frameworks.

### 3.3 Loss Contribution of Samples at Different Levels

In hyperbolic space, samples embedded at lower levels tend to better represent category prototypes. When calculating the centroid, hyperbolic space can effectively assign different weights to relatively lower-level and higher-level samples, meaning their contributions to the centroid vary in influence. To gain explicit insight into how each sample influences the alignment between the original dataset  $\mathcal R$  and

<span id="page-5-0"></span>Image /page/5/Figure/0 description: The image depicts a diagram illustrating a method for hyperbolic dataset distillation. The process begins with a 'Real Dataset' and a 'Synthetic Dataset', both shown as grids of images. An 'Encoder' with parameters denoted by phi takes these datasets as input. The encoder outputs vectors, v\_i^real and v\_j^syn, which are then processed. For the real dataset, these vectors are mapped to a 'Euclidean space' where their centroid is marked with a red star. Subsequently, they are transformed into 'Lorentz Hyperbolic Space' using an exponential map, visualized in both 'Top-down perspective' and 'Eye-level perspective'. The synthetic dataset undergoes a similar process, with its vectors mapped to Euclidean space and then to Lorentz Hyperbolic Space. The diagram highlights the calculation of 'Geodesic Distance' in the hyperbolic space between points representing the real and synthetic data. A legend explains the symbols used: a star/plus indicates centroids/means, a snowflake indicates encoder parameters that don't update, a red dot signifies the origin in hyperbolic space, 'B' represents a batch of the real dataset, and 'I' represents an IPC of the synthetic dataset. Red arrows indicate backpropagation.

Figure 2: The framework of hyperbolic dataset distillation. The proposed method leverages exponential mapping to embed the dataset into hyperbolic space, enabling a hierarchical representation where samples at different levels are assigned varying weights to reflect their significance within the global geometry. Centroids of both the original and synthetic datasets are then computed in the hyperbolic space, and the geodesic distance between them is used to quantify the distributional discrepancy. This hyperbolic distance serves as a loss term to iteratively update the synthetic dataset, encouraging it to better align with the class-specific prototypes of the original data.

the synthetic dataset  $S$  in hyperbolic space, we adopt a tangent space approximation centered at the origin  $o \in \mathbb{L}_K^n$ . Since the centroids of the sets ( $\bar{z}^{\text{real}}$  and  $\bar{z}^{\text{syn}}$ ) are near the origin, this approximation is reasonably effective. For  $Z^{\text{real}}$  and  $Z^{\text{syn}}$ , respectively define the hyperbolic radius (distance to the origin) of each sample as:

$$
r_i = d_L(o, r_i^{\text{real}}), \qquad s_j = d_L(o, s_j^{\text{syn}}), \tag{15}
$$

and let the corresponding normalized tangent vectors at the origin be

$$
u_i = \frac{r_i^{\text{real}} - \cosh r_i \, o}{\sinh r_i}, \qquad v_j = \frac{s_j^{\text{syn}} - \cosh s_j \, o}{\sinh s_j}.\tag{16}
$$

These vectors satisfy  $u_i, v_j \in T_o \mathbb{L}_K^n$  (tangent space at the origin), i.e., they lie in the tangent space at the origin and satisfy  $\langle u_i, o \rangle_L = \langle v_j, o \rangle_L = 0$ .

To capture the radial influence of each sample, we introduce the scalar weight function (the derivation process is in Appendix C):

$$
w(\mathbf{r}) = \frac{\sqrt{|K|} d}{\sinh(\sqrt{|K|} d)},\tag{17}
$$

which is strictly decreasing in  $d$ .  $d$  represents the distance from the corresponding point to the reference point, which is defined as the origin in this context. This reflects that samples closer to the origin (i.e., with smaller hyperbolic norm) contribute more strongly to the Fréchet mean in the tangent space.

Under the tangent-space approximation of the Fréchet mean condition (i.e., the first-order optimality condition for the squared distance sum), the logarithmic maps of the centroids can be approximated as:

$$
Log_o(\bar{z}^{\text{real}}) \approx \sum_{i=1}^{|\mathcal{R}|} w(r_i) u_i, \qquad Log_o(\bar{z}^{\text{syn}}) \approx \sum_{j=1}^{|\mathcal{S}|} w(s_j) v_j.
$$
 (18)

This yields the approximate loss function as the Euclidean distance between the two log-mapped centroids in the tangent space:

$$
\mathcal{L}_{\text{approx}} = d_L(\bar{z}^{\text{real}}, \bar{z}^{\text{syn}}) \approx \left\| \sum_{i=1}^{|\mathcal{R}|} w(r_i) u_i - \sum_{j=1}^{|\mathcal{S}|} w(s_j) v_j \right\|_{T_o \mathbb{L}_K^n}.
$$
 (19)

 $\mathbf{u}$ 

This formulation explicitly reveals the per-sample contribution to the overall loss: each sample affects the direction of the weighted log-mean, with its impact modulated by the scalar weight  $w(r)$ . Specifically, central samples (closer to the origin) receive higher weights, while peripheral samples (with larger hyperbolic radius) contribute less. This reflects a natural attenuation of influence in hyperbolic geometry and enhances stability by reducing the effect of outliers. Furthermore, we also explain this phenomenon from the perspective of gradients. For details, please refer to Appendix D.

# 4 Experiments

## 4.1 Experimental Setup

Dataset. We evaluated HDD on several standard benchmark datasets, including Fashion-MNIST [\[59\]](#page-12-14), SVHN [\[44\]](#page-11-13), CIFAR-10 [\[25\]](#page-10-13), CIFAR-100 [\[25\]](#page-10-13), and the larger-scale TinyImageNet [\[28\]](#page-10-14). Additionally, for hybrid architecture experiments, we utilized the ImageWoof subset of ImageNet [\[10\]](#page-9-14), which features higher resolution images. Please refer to Appendix E for detailed information about the used datasets.

Network Architectures. For our primary experiments, we adopt the same convolutional network (ConvNet [\[29\]](#page-10-15)) architecture as used in DC [\[69\]](#page-12-4), DM [\[68\]](#page-12-1), and IDM [\[70\]](#page-12-6) to extract feature representations. This ConvNet consists of three sequential modules, each comprising a convolutional layer, instance normalization, a ReLU activation, and a stride-2 average pooling layer. To evaluate cross-architecture generalization, we follow the protocol in DM and conduct experiments using ConvNet, AlexNet, VGG11, and ResNet18 (The results can be found in Appendix F). For hybrid architecture experiments, we adopt the architectural configuration proposed in Dance [\[64\]](#page-12-5).

Implementation Details. Our hyperparameter settings follow the design of the DM [\[68\]](#page-12-1), IDM [\[70\]](#page-12-6), and Dance [\[64\]](#page-12-5) architectures. We adopt the differentiable siamese augmentation [\[67\]](#page-12-10) enhancement method used in prior works. The synthetic dataset is learned using SGD. For DM with HDD, we train for 20,000 iterations, while for IDM with HDD and Dance with HDD, we train for 10,000 iterations. For all experiments, we set the batch size to 256. Additionally, for different experiments, we use distinct hyperbolic curvature K, gradient scaling factor  $\lambda$ , and synthetic image learning rate r, as detailed in Appendix G. All experiments are conducted on one RTX A6000 Ada GPU.

## 4.2 Main Results

In the main results section, we established a comprehensive set of baseline methods to evaluate model performance. For core set selection approaches, we employed Random Selection [\[7\]](#page-9-15), Herding [\[58\]](#page-12-15), K-Center [\[49\]](#page-11-14), and Forgetting [\[55\]](#page-12-16). Within the category of optimization-based methods, we incorporated DC [\[69\]](#page-12-4), DSA [\[67\]](#page-12-10), and DCC [\[30\]](#page-10-16). For distribution-matching methods, our baselines included CAFE [\[56\]](#page-12-7), CAFE+DSA [\[56\]](#page-12-7), DataDAM[\[48\]](#page-11-1), as well as DM [\[68\]](#page-12-1) and IDM [\[70\]](#page-12-6). Additionally, we have also considered the decoupling optimization method G-VBSM [\[50\]](#page-11-10). Detailed descriptions of these baseline methods are provided in Appendix H. For DM, IDM, and HDD, each experiment is conducted three times, and the mean and standard deviation are reported.

Table [1](#page-7-0) presents a comparative evaluation of our method against prior approaches on Fashion-MNIST [\[59\]](#page-12-14), SVHN [\[44\]](#page-11-13), CIFAR-10 [\[25\]](#page-10-13), and CIFAR-100 [\[25\]](#page-10-13). The results for TinyImageNet [\[28\]](#page-10-14) are provided in Appendix I. IDM augmented with HDD, which exploits the hierarchical inductive bias of hyperbolic space, consistently outperforms the baseline IDM across all benchmarks. Under the IPC = 1 setting, IDM with HDD achieves classification accuracies of 78.5% on FashionMNIST (+1.1%), 67.8% on SVHN (+2.5%), 47.0% on CIFAR-10 (+1.8%), and 25.3% on CIFAR-100  $(+3.2\%)$ , demonstrating its superiority in low-data regimes. With IPC = 10, the proposed method attains 61.3% accuracy on CIFAR-10, a 4.0% improvement over IDM. Under IPC = 50, it yields gains of 2.4%, 2.5%, and 2.4% on SVHN, CIFAR-10, and CIFAR-100, respectively. Furthermore, DM with HDD also exhibits notable enhancements relative to DM: on SVHN, accuracy increases by

| Method               | FashionMNIST     |                  |                  | SVHN             |                  |                  | CIFAR10          |                  |                  | CIFAR100         |                  |                  |
|----------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|
|                      | 1                | 10               | 50               | 1                | 10               | 50               | 1                | 10               | 50               | 1                | 10               | 50               |
| IPC<br>Ratio (%)     | 0.017            | 0.17             | 0.83             | 0.014            | 0.14             | 0.7              | 0.02             | 0.2              | 1                | 0.2              | 2                | 10               |
| Random               | $51.4 
espm 3.8$ | $73.8 
espm 0.7$ | $82.5 
espm 0.7$ | $14.6 
espm 1.6$ | $35.1 
espm 4.1$ | $70.9 
espm 0.9$ | $14.4 
espm 2.0$ | $26.0 
espm 1.2$ | $43.4 
espm 1.0$ | $4.2 
espm 0.3$  | $14.6 
espm 0.5$ | $30.0 
espm 0.4$ |
| Herding              | $67.0 
espm 1.9$ | $71.1 
espm 0.7$ | $71.9 
espm 0.8$ | $20.9 
espm 1.3$ | $50.5 
espm 3.3$ | $72.6 
espm 0.8$ | $21.5 
espm 1.2$ | $31.6 
espm 0.7$ | $40.4 
espm 0.6$ | $8.4 
espm 0.3$  | $17.3 
espm 0.3$ | $33.7 
espm 0.5$ |
| K-Center             | $66.9 
espm 1.8$ | $54.7 
espm 1.5$ | $68.3 
espm 0.8$ | $21.0 
espm 1.5$ | $14.0 
espm 1.3$ | $20.1 
espm 1.4$ | $21.5 
espm 1.3$ | $14.7 
espm 0.7$ | $27.0 
espm 1.4$ | $8.3 
espm 0.3$  | $7.1 
espm 0.2$  | $30.5 
espm 0.3$ |
| Forgetting           |                  |                  |                  | $12.1 
espm 5.6$ | $16.8 
espm 1.2$ | $27.2 
espm 1.5$ | $13.5 
espm 1.5$ | $23.3 
espm 1.0$ | $23.3 
espm 1.1$ | $4.5 
espm 0.3$  | $15.1 
espm 0.2$ | $30.5 
espm 0.4$ |
| DC [69]              | $70.5 
espm 0.6$ | $82.3 
espm 0.4$ | $83.6 
espm 0.4$ | $31.2 
espm 1.4$ | $76.1 
espm 0.6$ | $82.3 
espm 0.3$ | $28.3 
espm 0.5$ | $44.9 
espm 0.5$ | $53.9 
espm 0.5$ | $12.8 
espm 0.3$ | $25.2 
espm 0.3$ |                  |
| <b>DSA</b> [67]      | $70.6 
espm 0.6$ | $84.6 
espm 0.3$ | $88.7 
espm 0.3$ | $27.5 
espm 1.4$ | $79.2 
espm 0.5$ | $84.4 
espm 0.4$ | $28.8 
espm 0.7$ | $52.1 
espm 0.5$ | $60.6 
espm 0.5$ | $13.9 
espm 0.3$ | $32.3 
espm 0.3$ | $42.8 
espm 0.4$ |
| <b>CAFE</b> [56]     | $77.1 
espm 0.9$ | $83.0 
espm 0.4$ | $84.8 
espm 0.4$ | $42.6 
espm 3.3$ | $75.9 
espm 0.6$ | $81.3 
espm 0.3$ | $30.3 
espm 1.1$ | $46.3 
espm 0.6$ | $55.5 
espm 0.6$ | $12.9 
espm 0.3$ | $27.8 
espm 0.3$ | $37.9 
espm 0.3$ |
| CAFE+DSA [56]        | $73.7 
espm 0.7$ | $83.0 
espm 0.3$ | $88.2 
espm 0.3$ | $42.9 
espm 3.0$ | $77.9 
espm 0.6$ | $82.3 
espm 0.4$ | $31.6 
espm 0.8$ | $50.9 
espm 0.5$ | $62.3 
espm 0.4$ | $14.0 
espm 0.3$ | $31.5 
espm 0.2$ | $42.9 
espm 0.2$ |
| <b>DCC</b> [30]      |                  |                  |                  | $34.3 
espm 1.6$ | $76.2 
espm 0.8$ | $83.3 
espm 0.2$ | $34.0 
espm 0.7$ | $54.4 
espm 0.5$ | $64.2 
espm 0.4$ | $14.6 
espm 0.3$ | $33.5 
espm 0.3$ | $39.4 
espm 0.4$ |
| G-VBSM [50]          |                  |                  |                  |                  |                  |                  |                  | $46.5 
espm 0.7$ | $54.3 
espm 0.3$ | $16.4 
espm 0.7$ | $38.7 
espm 0.2$ | $45.7 
espm 0.4$ |
| DataDAM[48]          |                  |                  |                  |                  |                  |                  | $32.0 
espm 1.2$ | $54.2 
espm 0.8$ | $67.0 
espm 0.4$ | $14.5 
espm 0.5$ | $34.8 
espm 0.5$ | $49.4 
espm 0.3$ |
| DM [68]              | $70.7 
espm 0.6$ | $83.4 
espm 0.1$ | $88.1 
espm 0.6$ | $21.9 
espm 0.4$ | $72.8 
espm 0.3$ | $82.6 
espm 0.3$ | $26.4 
espm 0.3$ | $48.5 
espm 0.6$ | $62.2 
espm 0.5$ | $11.4 
espm 0.3$ | $29.7 
espm 0.3$ | $43.0 
espm 0.4$ |
| DM with HDD          | $72.1 
espm 0.2$ | $84.0 
espm 0.1$ | $88.8 
espm 0.4$ | $25.0 
espm 0.2$ | $75.1 
espm 0.2$ | $83.0 
espm 0.3$ | $28.7 
espm 0.2$ | $50.3 
espm 0.3$ | $63.2 
espm 0.4$ | $13.3 
espm 0.2$ | $30.1 
espm 0.1$ | $43.8 
espm 0.2$ |
| <b>IDM</b> [70]      | $77.4 
espm 0.3$ | $82.4 
espm 0.2$ | $84.5 
espm 0.1$ | $65.3 
espm 0.3$ | $81.0 
espm 0.1$ | $85.2 
espm 0.3$ | $45.2 
espm 0.5$ | $57.3 
espm 0.3$ | $67.2 
espm 0.1$ | $22.1 
espm 0.2$ | $44.7 
espm 0.3$ | $46.5 
espm 0.4$ |
| <b>IDM</b> with HDD  | $78.5 
espm 0.2$ | $83.8 
espm 0.2$ | $86.4 
espm 0.3$ | $67.8 
espm 0.2$ | $84.0 
espm 0.2$ | $87.6 
espm 0.1$ | $47.0 
espm 0.1$ | $61.3 
espm 0.1$ | $69.7 
espm 0.2$ | $25.3 
espm 0.2$ | $45.4 
espm 0.1$ | $48.9 
espm 0.3$ |
| <b>Whole Dataset</b> |                  | $93.5 
espm 0.1$ |                  |                  | $95.4 
espm 0.1$ |                  |                  | $84.8 
espm 0.1$ |                  |                  | $56.2 
espm 0.3$ |                  |

<span id="page-7-0"></span>Table 1: Comparison of different methods on the FashionMNIST, SVHN, CIFAR10, and CIFAR100 datasets with  $IPC = 1$ , 10, and 50.

3.1% (IPC = 1) and 2.3% (IPC = 10), and on CIFAR-10 (IPC = 1) by 2.3%. We present some of the distilled images in Appendix J.

## 4.3 Hierarchical Pruning

To validate the efficacy of hyperbolic-spaceaware hierarchical pruning, we conducted the pruning experiments on CIFAR-10 (IPC =  $10$ ) by comparing DM with HDD against IDM with HDD across varying hyperbolic curvatures K. Specifically, given the original CIFAR-10 dataset  $\mathcal{D} = \{ (r_i, t_i, K_i) \}_{i=1}^N$ , where  $K_i$ denotes the curvature associated with sample i, we sort all samples in descending order of

<span id="page-7-1"></span>

|  | Table 2: The distillation accuracy of CIFAR10 |  |  |
|--|-----------------------------------------------|--|--|
|  | $(IPC = 10)$ for different pruning ratios.    |  |  |

| <b>Pruning Ratio</b> | DМ             | DM with HDD    | <b>IDM</b> with HDD |
|----------------------|----------------|----------------|---------------------|
| 95%                  | $48.2 + 0.6$   | $49.6 + 0.5$   | $59.1 + 0.4$        |
| 80%                  | $48.7 + 0.2$   | $50.2 + 0.2$   | $60.3 + 0.3$        |
| 50%                  | $48.8 + 0.2$   | $50.3 + 0.1$   | $60.9 + 0.2$        |
| $0\%$                | $48.5 \pm 0.6$ | $50.3 \pm 0.3$ | $61.3 \pm 0.1$      |

 $K_i$  and remove the top  $\alpha$ % of samples exhibiting the highest curvature, with pruning ratios  $\alpha \in \{95\%, 80\%, 50\%\}.$  Formally, the retained subset is defined as

$$
\mathcal{D}' = \{(r_i, t_i, K_i) \in \mathcal{D} \mid \text{rank}(K_i) > \lceil \alpha N \rceil \},\tag{20}
$$

where  $rank(K_i)$  denotes the position of  $K_i$  in the descending-sorted list.

<span id="page-7-2"></span>Image /page/7/Figure/10 description: The image displays two main sections, labeled (a) DM with HDD and (b) IDM with HDD. Each section contains three line graphs plotting accuracy (%) against iteration. In section (a), the first graph shows 'Prune by 95%' (light blue line) and 'No pruning' (orange line) with iterations from 5000 to 20000. The second graph in (a) also shows 'Prune by 80%' (light blue line) and 'No pruning' (orange line) over the same iteration range. The third graph in (a) shows 'Prune by 50%' (light blue line) and 'No pruning' (orange line) from 5000 to 20000 iterations. In section (b), the first graph plots 'Prune by 95%' (purple line) and 'No pruning' (green line) with iterations from 2000 to 10000. The second graph in (b) shows 'Prune by 80%' (purple line) and 'No pruning' (green line) over the same iteration range. The third graph in (b) displays 'Prune by 50%' (purple line) and 'No pruning' (green line) from 2000 to 10000 iterations. The y-axis for all graphs is labeled 'accuracy%' and ranges from approximately 49.0 to 50.0 for section (a), and 58 to 61 for section (b).

Figure 3: Distillation accuracy variations of CIFAR-10 (IPC = 10) during the distillation process with different pruning rates.

Table [2](#page-7-1) presents the matching accuracy after hierarchical pruning: both DM and DM with HDD require only 20% of the original training set to maintain performance, while IDM with HDD likewise preserves the vast majority of its performance with just 20% of data. This observation demonstrates that lower-level samples possess greater representativeness in hyperbolic space. However, we also observed that excessively small sample sizes still lead to performance degradation, indicating that higher-level samples also influence the centroid. Furthermore, Figs. [3-](#page-7-2)(a) and (b) depict the accuracy trajectories throughout the distillation process under various pruning ratios for HDD-DM and HDD-IDM, respectively. Notably, after pruning, the accuracy curves exhibit significantly reduced fluctuations during later training stages, demonstrating markedly enhanced training stability.

### 4.4 Hybrid Architecture Experiment

To evaluate HDD's scalability, we ran additional experiments with the Hybrid Dance [\[64\]](#page-12-5) architecture that alternates between cross-entropy and distribution matching optimization. We compared our

proposed Dance with HDD method with leading distribution matching methods (IID [\[11\]](#page-9-16), DSDM [\[37\]](#page-11-15), and M3D [\[65\]](#page-12-17)) as well as state-of-the-art approaches from other domains (DATM [\[22\]](#page-10-17), RDED [\[54\]](#page-12-12), D<sup>4</sup>M [\[51\]](#page-11-5)), and the comprehensive experimental results are summarized in Table [3.](#page-8-0) On CIFAR-10 with IPC = 50, Dance with HDD improves over the original Dance by 0.8%. On CIFAR-100 with IPC  $= 1$ , it outperforms both Dance and M3D by 1.5%. Remarkably, at IPC = 10 on CIFAR-100, Dance with HDD is within 6% of training on the whole dataset. When scaling up to higher resolutions, our method still leads: on ImageWoof, it gains  $0.5\%$  at IPC = 1 and  $0.4\%$  at IPC = 10 compared to Dance.

## 4.5 Discussion

The original CIFAR-10 data and the distilled synthetic sets with HDD were both projected onto the Poincaré ball for visualization; their centroids almost perfectly align. The essence of HDD lies in replacing the densely treestructured distribution of the original dataset with a sparse tree-structured representation. As shown in Figs. [4-](#page-8-1) (a) and (c), although the number of samples in the synthetic dataset is significantly smaller, it still approx-

<span id="page-8-0"></span>Table 3: Comparison of different methods on the CIFAR10, CIFAR100, and ImageWoof datasets.

| Method               |                | CIFAR10        |              |                          | CIFAR100       |                |                          | <b>ImageWoof</b>         |
|----------------------|----------------|----------------|--------------|--------------------------|----------------|----------------|--------------------------|--------------------------|
| IPC.                 |                | 10             | 50           |                          | 10             | 50             |                          | 10                       |
| Ratio (%)            | 0.02           | 0.2            |              | 0.2                      | $\overline{c}$ | 10             | 0.11                     | 1.10                     |
| <b>DATM [22]</b>     | $46.9 \pm 0.5$ | $66.8 \pm 0.2$ | $76.1 + 0.3$ | $27.9 + 0.2$             | $47.2 \pm 0.4$ | $55.0 + 0.2$   | ٠                        | $\overline{\phantom{a}}$ |
| <b>RDED [54]</b>     | $23.5 + 0.3$   | $50.2 + 0.3$   | $68.4 + 0.1$ | $196 + 03$               | $48.1 + 0.3$   | $57.0 + 0.1$   | $18.5 + 0.9$             | $40.6 + 2.0$             |
| $D4M$ [51]           |                | $562+04$       | $72.8 + 0.5$ | $\overline{\phantom{a}}$ | $45.0 + 0.1$   | $48.8 + 0.3$   | ٠                        | $\overline{\phantom{a}}$ |
| $IID$ $(IDM)$ $[11]$ | $47.1 + 0.1$   | $59.9 + 0.2$   | $69.0 + 0.3$ | $24.6 + 0.1$             | $45.7 + 0.4$   | $51.3 + 0.4$   | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ |
| <b>DSDM1371</b>      | $45.0 + 0.4$   | $66.5 + 0.3$   | $75.8 + 0.3$ | $19.5 + 0.2$             | $46.2 + 0.3$   | $54.0 + 0.2$   | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ |
| M3D [65]             | $45.3 \pm 0.3$ | $63.5 + 0.2$   | $69.9 + 0.5$ | $26.2 + 0.3$             | $42.4 + 0.2$   | $50.9 + 0.7$   | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ |
| Dance [64]           | $47.2 + 0.3$   | $70.2 + 0.2$   | $76.3 + 0.1$ | $26.2 + 0.2$             | $49.7 + 0.1$   | $52.8 + 0.1$   | $27.1 + 0.2$             | $46.2 + 0.2$             |
| Dance with HDD       | $46.8 \pm 0.3$ | $70.8 + 0.2$   | $77.1 + 0.2$ | $27.7 + 0.3$             | $50.2 \pm 0.2$ | $53.9 \pm 0.1$ | $27.6 + 0.2$             | $46.6 + 0.1$             |
| <b>Whole Dataset</b> |                | $84.8 \pm 0.1$ |              |                          | $56.2 \pm 0.3$ |                |                          | $67.0 \pm 1.3$           |

imately captures the distributional trajectory of the original dataset. The synthetic dataset tends to be denser in regions where the original data is dense and sparser in regions where the original data is sparse. However, we also observe a tendency of the synthetic samples to concentrate closer to the root node (i.e., toward the center), as illustrated in Fig. [4-](#page-8-1)(b). Despite the presence of pronounced edge accumulation in the original dataset (i.e., a large number of samples located near the boundary), the synthetic samples are noticeably "attracted" toward the direction of the root node. As shown in Fig. [4-](#page-8-1)(d), although the synthetic dataset contains fewer samples overall, it exhibits a higher concentration of points near the root node compared to the original dataset.

<span id="page-8-1"></span>Image /page/8/Figure/6 description: The image displays four scatter plots arranged in a 2x2 grid, each depicting data points within a Poincaré Ball. The top row is labeled "DM with HDD" and shows plots (a) and (b) for IPC10 and IPC50 respectively. The bottom row is labeled "IDM with HDD" and shows plots (c) and (d) for IPC10 and IPC50 respectively. Each plot contains numerous cyan and pink circular data points, with some points enlarged and highlighted with a dashed red box and an arrow indicating a zoomed-in view. A purple star symbol is present in each of the four plots, serving as a reference point. The axes are labeled as "z0" for the x-axis and "z1" for the y-axis, with values ranging from -1.00 to 1.00.

: Sample points of the original dataset.  $\Box$ : Sample points of the synthetic dataset. The centroids of the original dataset

Figure 4: After distillation with DM with HDD and IDM with HDD, the distributions of the original and synthetic datasets in the Poincaré hyperbolic space are visualized.

# 5 Conclusion and Future Works

In this study, we introduce hyperbolic space into dataset distillation for the first time and propose a novel hyperbolic dataset distillation method, termed HDD. Leveraging the negative curvature of hyperbolic geometry, HDD effectively captures the hierarchical structure inherent in real-world datasets. By aligning the centroids of the original and synthetic datasets in hyperbolic space, we ensure that the synthetic data preserves the underlying geometric properties of the original data. Crucially, due to the varying influence of samples from different hierarchical levels on the centroid, the loss function naturally emphasizes contributions from lower-level (prototype) samples. This inductive bias enhances the preservation of class prototype distributions, thereby improving the quality of distillation. Currently, distribution metrics from information theory (e.g., KL divergence) and optimal transport theory (e.g., Wasserstein distance) have been extensively utilized in dataset distillation to enhance model performance. However, the application of these methods in hyperbolic dataset distillation remains unexplored, which presents a promising direction for future research to extend these methodologies into non-Euclidean-based dataset distillation.

## **References**

- <span id="page-9-13"></span>[1] Mina Ghadimi Atigh, Julian Schoep, Erman Acar, Nanne Van Noord, and Pascal Mettes. Hyperbolic image segmentation. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4453–4462, 2022.
- <span id="page-9-9"></span>[2] Gregor Bachmann, Gary Bécigneul, and Octavian Ganea. Constant curvature graph convolutional networks. In *International Conference on Machine Learning*, pages 486–496, 2020.
- <span id="page-9-12"></span>[3] Ahmad Bdeir, Kristian Schwethelm, and Niels Landwehr. Fully hyperbolic convolutional neural networks for computer vision. *arXiv preprint arXiv:2303.15919*, 2023.
- <span id="page-9-3"></span>[4] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022.
- <span id="page-9-8"></span>[5] Ines Chami, Zhitao Ying, Christopher Ré, and Jure Leskovec. Hyperbolic graph convolutional neural networks. *Advances in Neural Information Processing Systems*, 32, 2019.
- <span id="page-9-2"></span>[6] Dingfan Chen, Raouf Kerkouche, and Mario Fritz. Private set generation with discriminative information. *Advances in Neural Information Processing Systems*, 35:14678–14690, 2022.
- <span id="page-9-15"></span>[7] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. *arXiv preprint arXiv:1203.3472*, 2012.
- <span id="page-9-11"></span>[8] Seunghyuk Cho, Juyong Lee, and Dongwoo Kim. Hyperbolic vae via latent gaussian distributions. *Advances in Neural Information Processing Systems*, 36:569–588, 2023.
- <span id="page-9-7"></span>[9] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590, 2023.
- <span id="page-9-14"></span>[10] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *IEEE Conference on Computer Vision and Pattern Recognition*, pages 248–255, 2009.
- <span id="page-9-16"></span>[11] Wenxiao Deng, Wenbin Li, Tianyu Ding, Lei Wang, Hongguang Zhang, Kuihua Huang, Jing Huo, and Yang Gao. Exploiting inter-sample and inter-feature relations in dataset distillation. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 17057–17066, 2024.
- <span id="page-9-5"></span>[12] Karan Desai, Maximilian Nickel, Tanmay Rajpurohit, Justin Johnson, and Shanmukha Ramakrishna Vedantam. Hyperbolic image-text representations. In *International Conference on Machine Learning*, pages 7694–7731, 2023.
- <span id="page-9-1"></span>[13] Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning*, pages 5378–5396, 2022.
- <span id="page-9-0"></span>[14] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3749–3758, 2023.
- <span id="page-9-4"></span>[15] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3749–3758, 2023.
- <span id="page-9-6"></span>[16] Songwei Ge, Shlok Mishra, Simon Kornblith, Chun-Liang Li, and David Jacobs. Hyperbolic contrastive learning for visual representations beyond objects. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 6840–6849, 2023.
- <span id="page-9-10"></span>[17] Mina Ghadimi Atigh, Martin Keller-Ressel, and Pascal Mettes. Hyperbolic busemann learning with ideal prototypes. *Advances in Neural Information Processing Systems*, 34:103–115, 2021.

- <span id="page-10-11"></span>[18] Albert Gu, Frederic Sala, Beliz Gunel, and Christopher Ré. Learning mixed-curvature representations in product spaces. In *International Conference on Learning Representations*, 2018.
- <span id="page-10-10"></span>[19] Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 15793–15803, 2024.
- <span id="page-10-0"></span>[20] Jianyang Gu, Kai Wang, Wei Jiang, and Yang You. Summarizing stream data for memoryconstrained online continual learning. In *AAAI Conference on Artificial Intelligence*, pages 12217–12225, 2024.
- <span id="page-10-4"></span>[21] Yunhui Guo, Youren Zhang, Yubei Chen, and Stella X Yu. Unsupervised feature learning with emergent data-driven prototypicality. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 23199–23208, 2024.
- <span id="page-10-17"></span>[22] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023.
- <span id="page-10-7"></span>[23] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *International Conference on Learning Representations*, 2024.
- <span id="page-10-3"></span>[24] Valentin Khrulkov, Leyla Mirvakhabova, Evgeniya Ustinova, Ivan Oseledets, and Victor Lempitsky. Hyperbolic image embeddings. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 6418–6428, 2020.
- <span id="page-10-13"></span>[25] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-10-12"></span>[26] Hyeongjun Kwon, Jinhyun Jang, Jin Kim, Kwonyoung Kim, and Kwanghoon Sohn. Improving visual recognition with hyperbolical visual hierarchy mapping. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 17364–17374, 2024.
- <span id="page-10-5"></span>[27] Marc Law, Renjie Liao, Jake Snell, and Richard Zemel. Lorentzian distance learning for hyperbolic representations. In *International Conference on Machine Learning*, pages 3672– 3681, 2019.
- <span id="page-10-14"></span>[28] Yann Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-10-15"></span>[29] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *IEEE*, 86(11):2278–2324, 1998.
- <span id="page-10-16"></span>[30] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364, 2022.
- <span id="page-10-6"></span>[31] Shiye Lei and Dacheng Tao. A comprehensive survey to dataset distillation. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 46(1):17–32, 2023.
- <span id="page-10-1"></span>[32] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Soft-label anonymous gastric x-ray image distillation. In *IEEE International Conference on Image Processing*, pages 305–309, 2020.
- <span id="page-10-2"></span>[33] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Compressed gastric image generation based on soft-label dataset distillation for medical data sharing. *Computer Methods and Programs in Biomedicine*, 227:107189, 2022.
- <span id="page-10-8"></span>[34] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Dataset distillation using parameter pruning. *IEICE Transactions on Fundamentals of Electronics, Communications and Computer Sciences*, 2023.
- <span id="page-10-9"></span>[35] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Importance-aware adaptive dataset distillation. *Neural Networks*, 2024.

- <span id="page-11-3"></span>[36] Guang Li, Bo Zhao, and Tongzhou Wang. Awesome dataset distillation. [https://github.](https://github.com/Guang000/Awesome-Dataset-Distillation) [com/Guang000/Awesome-Dataset-Distillation](https://github.com/Guang000/Awesome-Dataset-Distillation), 2022.
- <span id="page-11-15"></span>[37] Hongcheng Li, Yucan Zhou, Xiaoyan Gu, Bo Li, and Weiping Wang. Diversified semantic distribution matching for dataset distillation. In *ACM International Conference on Multimedia*, pages 7542–7550, 2024.
- <span id="page-11-7"></span>[38] Longzhen Li, Guang Li, Ren Togo, Keisuke Maeda, Takahiro Ogawa, and Miki Haseyama. Generative dataset distillation: Balancing global structure and local details. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops*, pages 7664–7671, 2024.
- <span id="page-11-8"></span>[39] Longzhen Li, Guang Li, Ren Togo, Keisuke Maeda, Takahiro Ogawa, and Miki Haseyama. Generative dataset distillation based on self-knowledge distillation. In *IEEE International Conference on Acoustics, Speech, and Signal Processing*, pages 1–5, 2024.
- <span id="page-11-9"></span>[40] Mingzhuo Li, Guang Li, Jiafeng Mao, Takahiro Ogawa, and Miki Haseyama. Diversity-driven generative dataset distillation based on diffusion model with self-adaptive memory. In *IEEE International Conference on Image Processing*, 2024.
- <span id="page-11-12"></span>[41] Fangfei Lin, Bing Bai, Kun Bai, Yazhou Ren, Peng Zhao, and Zenglin Xu. Contrastive multi-view hyperbolic hierarchical clustering. *arXiv preprint arXiv:2205.02618*, 2022.
- <span id="page-11-4"></span>[42] Ping Liu and Jiawei Du. The evolution of dataset distillation: Toward scalable and generalizable solutions. *arXiv preprint arXiv:2502.05673*, 2025.
- <span id="page-11-0"></span>[43] Dmitry Medvedev and Alexander D'yakonov. Learning to generate synthetic training data using gradient matching and implicit differentiation. In *International Conference on Analysis of Images, Social Networks and Texts*, pages 138–150, 2021.
- <span id="page-11-13"></span>[44] Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Baolin Wu, Andrew Y Ng, et al. Reading digits in natural images with unsupervised feature learning. In *Neural Information Processing Systems Workshops*, 2011.
- <span id="page-11-16"></span>[45] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021.
- <span id="page-11-11"></span>[46] Avik Pal, Max van Spengler, Guido Maria D'Amely di Melendugno, Alessandro Flaborea, Fabio Galasso, and Pascal Mettes. Compositional entailment learning for hyperbolic vision-language models. *arXiv preprint arXiv:2410.06912*, 2024.
- <span id="page-11-2"></span>[47] Sameera Ramasinghe, Violetta Shevchenko, Gil Avraham, and Ajanthan Thalaiyasingam. Accept the modality gap: An exploration in the hyperbolic space. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 27263–27272, 2024.
- <span id="page-11-1"></span>[48] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *IEEE/CVF International Conference on Computer Vision*, pages 17097–17107, 2023.
- <span id="page-11-14"></span>[49] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. *arXiv preprint arXiv:1708.00489*, 2017.
- <span id="page-11-10"></span>[50] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 16709–16718, 2024.
- <span id="page-11-5"></span>[51] Duo Su, Junjie Hou, Weizhi Gao, Yingjie Tian, and Bowen Tang. Dˆ 4: Dataset distillation via disentangled diffusion model. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 5809–5818, 2024.
- <span id="page-11-6"></span>[52] Duo Su, Junjie Hou, Guang Li, Ren Togo, Rui Song, Takahiro Ogawa, and Miki Haseyama. Generative dataset distillation based on diffusion model. In *European Conference on Computer Vision Workshops*, 2024.

- <span id="page-12-2"></span>[53] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *International Conference on Machine Learning*, pages 9206–9216, 2020.
- <span id="page-12-12"></span>[54] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 9390–9399, 2024.
- <span id="page-12-16"></span>[55] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018.
- <span id="page-12-7"></span>[56] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196–12205, 2022.
- <span id="page-12-0"></span>[57] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-12-15"></span>[58] Max Welling. Herding dynamical weights to learn. In *International Conference on Machine Learning*, pages 1121–1128, 2009.
- <span id="page-12-14"></span>[59] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-12-8"></span>[60] Jiexi Yan, Lei Luo, Cheng Deng, and Heng Huang. Unsupervised hyperbolic metric learning. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12465–12474, 2021.
- <span id="page-12-3"></span>[61] Enneng Yang, Li Shen, Zhenyi Wang, Tongliang Liu, and Guibing Guo. An efficient dataset condensation plugin and its application to continual learning. *Advances in Neural Information Processing Systems*, 36, 2023.
- <span id="page-12-11"></span>[62] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *Advances in Neural Information Processing Systems*, 2023.
- <span id="page-12-9"></span>[63] Ruonan Yu, Songhua Liu, and Xinchao Wang. A comprehensive survey to dataset distillation. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 46(1):150–170, 2023.
- <span id="page-12-5"></span>[64] Hansong Zhang, Shikun Li, Fanzhao Lin, Weiping Wang, Zhenxing Qian, and Shiming Ge. Dance: Dual-view distribution alignment for dataset condensation. *arXiv preprint arXiv:2406.01063*, 2024.
- <span id="page-12-17"></span>[65] Hansong Zhang, Shikun Li, Pengju Wang, Dan Zeng, and Shiming Ge. M3d: Dataset condensation by minimizing maximum mean discrepancy. In *AAAI Conference on Artificial Intelligence*, pages 9314–9322, 2024.
- <span id="page-12-13"></span>[66] Yiding Zhang, Xiao Wang, Chuan Shi, Xunqiang Jiang, and Yanfang Ye. Hyperbolic graph attention network. *IEEE Transactions on Big Data*, 8(6):1690–1701, 2021.
- <span id="page-12-10"></span>[67] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685, 2021.
- <span id="page-12-1"></span>[68] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023.
- <span id="page-12-4"></span>[69] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.
- <span id="page-12-6"></span>[70] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7856–7865, 2023.

# A Complementary Details of the Lorentz Hyperbolic Space

## A.1 Tangent Space $T_x \mathcal{L}$

In the Lorentz model, hyperbolic space  $\mathcal L$  is realized as a sheet of the two-sheeted hyperboloid in  $\mathbb{R}^{n+1}$  with Minkowski metric. For any point  $\mathbf{x} = [x_t; x_s] \in \mathcal{L}$ , the tangent space captures all possible instantaneous directions at x. It is defined by

$$
T_{\mathbf{x}}\mathcal{L} = \{ \mathbf{v} \in \mathbb{R}^{n+1} \mid \langle \mathbf{x}, \mathbf{v} \rangle_{\mathcal{L}} = 0 \}.
$$
 (21)

This tangent space inherits the Lorentzian metric, and any tangent vector v has norm

$$
\|\mathbf{v}\|_{\mathbf{x}} = \sqrt{\langle \mathbf{v}, \mathbf{v} \rangle_{\mathcal{L}}},\tag{22}
$$

which is strictly positive, ensuring that tangent vectors are purely spatial and providing the metric foundation for the exponential map.

## A.2 Exponential and Logarithm Maps

The exponential map pushes vectors in the tangent space onto the manifold, yielding a local Euclideanlike parametrization. Let  $\kappa = \sqrt{-K}$ . For  $\mathbf{v} \in T_{\mathbf{x}} \mathcal{L}$ , define

$$
\exp_{\mathbf{x}}(\mathbf{v}) = \cosh(\kappa \|\mathbf{v}\|_{\mathbf{x}}) \mathbf{x} + \frac{\sinh(\kappa \|\mathbf{v}\|_{\mathbf{x}})}{\kappa \|\mathbf{v}\|_{\mathbf{x}}} \mathbf{v}.
$$
 (23)

This formula satisfies  $\exp_{\mathbf{x}}(0) = \mathbf{x}$  and ensures that the interpolation curve is a geodesic of constant curvature. The inverse (logarithm map) brings a point y back to the tangent space:

$$
Log_{\mathbf{x}}(\mathbf{y}) = \frac{\arccosh(K \langle \mathbf{x}, \mathbf{y} \rangle_{\mathcal{L}})}{\sqrt{-K(\langle \mathbf{x}, \mathbf{y} \rangle_{\mathcal{L}})^2 - 1}} (\mathbf{y} - \langle \mathbf{x}, \mathbf{y} \rangle_{\mathcal{L}} \mathbf{x}). \tag{24}
$$

### A.3 Bijection between the Lorentz and Poincaré Ball Models

For many applications (especially visualization) it is convenient to switch to the Poincaré ball. Given a Lorentz point  $\mathbf{x} = [x_t; x_s]$ , we map it to the unit ball  $\|\mathbf{p}\| < 1$  via

$$
\mathbf{p} = \frac{\kappa \, x_s}{1 + \kappa \, x_t}.\tag{25}
$$

Conversely, for any  $\mathbf{p} \in \mathbb{R}^n$  with  $\|\mathbf{p}\| < 1$ , set  $\alpha = 1 - \|\mathbf{p}\|^2$  and recover

$$
x_t = \frac{1 + \|\mathbf{p}\|^2}{\alpha} \frac{1}{\kappa}, \qquad x_s = \frac{2}{\alpha} \frac{\mathbf{p}}{\kappa}.
$$
 (26)

One verifies that the reconstructed x satisfies  $-x_t^2 + ||x_s||^2 = 1/K$  and  $x_t > 0$ .

# B Centroid Convergence Toward the Origin

Given a finite sample  $\{p_i\}_{i=1}^N \subset \mathbb{L}^n_K$ , define the Fréchet functional

$$
F(\mathbf{p}) = \sum_{i=1}^{N} d_L^2(\mathbf{p}, \mathbf{p}_i),
$$
 (27)

Using the Riemannian logarithm  $Log_{\mathbf{p}}: \mathbb{L}_K^n \to T_{\mathbf{p}} \mathbb{L}_K^n$ , one obtains

$$
\nabla F(\mathbf{p}) = -2 \sum_{i=1}^{N} Log_{\mathbf{p}}(\mathbf{p}_i), Log_{\mathbf{p}}(\mathbf{p}_i) \in T_{\mathbf{p}} \mathbb{L}_K^n,
$$
\n(28)

so that the unique Fréchet mean p<sup>\*</sup> satisfies

$$
\sum_{i=1}^{m} Log_{\mathbf{p}^*}(\mathbf{p}_i) = 0.
$$
 (29)

Since  $\mathbb{L}_K^n$  has constant curvature  $K < 0$ , each map  $\mathbf{p} \mapsto ||Log_{\mathbf{p}}(\mathbf{p}_i)||^2$  is strictly convex along geodesics, ensuring a single global minimizer. We choose the origin  $P_0$  to be the unique fixed point of a maximal compact subgroup of  $Isom(\mathbb{L}_K^n)$ , whose stabilizer is isomorphic to  $O(n)$ . A comparison-theorem argument then shows

$$
\| Log_{p_0}(\mathbf{p}_i) \| = d_L(p_0, \mathbf{p}_i) \ge \| Log_{\mathbf{p}}(\mathbf{p}_i) \| \text{ whenever } d_L(p_0, \mathbf{p}_i) \ge d_L(\mathbf{p}, \mathbf{p}_i), \quad (30)
$$

forcing the solution of  $\sum_i Log_{\mathbf{p}}(\mathbf{p}_i) = 0$  to lie radially closer to  $p_0$  than the Euclidean centroid. Moreover, as |K| increases, the lower bound on the second-derivative of  $t \mapsto ||Log_{\gamma(t)}(\mathbf{p}_i)||^2$  along any geodesic  $\gamma$  grows, making this radial bias toward  $p_0$  even more pronounced. This results in the centroids of both the original dataset and the synthetic dataset being biased towards  $p_0$ , while the distance between them is relatively small.

## C Hierarchical Weight

In the hyperboloid model of constant sectional curvature  $K < 0$ , one introduces the scale parameter  $\kappa = \sqrt{|K|}$  and radius  $R = 1/\kappa$ , so that the ambient space is

$$
\mathcal{L} := \{ x \in \mathbb{R}^{n+1} \mid \langle x, x \rangle_{\mathcal{L}} = -R^2, \ x_0 > 0 \},\tag{31}
$$

where  $\langle \cdot, \cdot \rangle_L$  denotes the Minkowski inner product of signature  $(- + \cdots +)$ . The geodesic distance between two points  $p, q \in \mathbb{H}_K^n$  is given by

$$
d_K(p,q) = R \ arccosh(-\frac{1}{R^2} \langle p, q \rangle_L)
$$
  
=  $\frac{1}{\kappa} \ arccosh(-K \langle p, q \rangle_L).$  (32)

In particular, choosing the basepoint  $o = (R, 0, \ldots, 0)$  and writing  $r_i = d_K(o, x_i)$ , one has

$$
r_i = \frac{1}{\kappa} \operatorname{arccosh}(-K \langle o, x_i \rangle_L), \tag{33}
$$

$$
\cosh(\kappa r_i) = \frac{-\langle o, x_i \rangle_L}{R^2}.
$$
\n(34)

The logarithmic map at o takes the form

$$
Log_o(x_i) = \frac{\kappa r_i}{\sinh(\kappa r_i)} (x_i - \cosh(\kappa r_i) o).
$$
 (35)

Defining

$$
w(r_i) = \frac{\kappa r_i}{\sinh(\kappa r_i)},
$$
\n(36)

$$
u_i = x_i - \cosh(\kappa r_i) o,\tag{37}
$$

one obtains

$$
Log_o(x_i) = w(r_i) u_i.
$$
\n(38)

## D Gradient Contributions in the Lorentz Model of Hyperbolic Space

Given N sample points  $\{p_i\}_{i=1}^N \subset \mathbb{L}_K^n$ , their Fréchet mean (centroid)  $\mu$  is defined by

$$
\mu = \arg \min_{x \in \mathbb{H}_K^n} \sum_{i=1}^N d(x, p_i)^2,
$$
\n(39)

so that the objective (loss) is

$$
L(x) = \sum_{i=1}^{N} \left[ ar \cosh(-\langle x, p_i \rangle_L) \right]^2.
$$
 (40)

To study how a single point  $p$  "pulls" on  $x$ , set

$$
t = -\langle x, p \rangle_L
$$
  
=  $\cosh(d(x, p)) \ge 1.$  (41)

A standard derivation shows

$$
\nabla_x d(x, p)^2 = -2 \frac{\operatorname{arcosh}(t)}{\sqrt{t^2 - 1}} \left( p + \langle x, p \rangle_L x \right),\tag{42}
$$

and hence the magnitude of this pull is proportional to

$$
f(t) = \frac{arcosh(t)}{\sqrt{t^2 - 1}}.\tag{43}
$$

### Asymptotic Behavior.

*Near the "origin"* ( $t \to 1^+$ ). Since  $arcosh(t) \sim \sqrt{2(t-1)}$  and  $\sqrt{t^2-1} \sim \sqrt{2(t-1)}$ , we have

$$
f(t) = \frac{\operatorname{arcosh}(t)}{\sqrt{t^2 - 1}} \longrightarrow 1. \tag{44}
$$

Thus points very close to  $x$  exert almost the maximal pull of magnitude 1.

*Near the boundary* ( $t \to \infty$ ). Using  $arcosh(t) \sim \ln(2t)$  and  $\sqrt{t^2 - 1} \sim t$  gives

$$
f(t) \sim \frac{\ln(2t)}{t} \longrightarrow 0,
$$
\n(45)

so points very far from  $x$  contribute almost no pull.

## Monotonicity.

Differentiating

$$
f'(t) = \frac{\sqrt{t^2 - 1} - t \, \arccosh(t)}{(t^2 - 1)^{3/2}},\tag{46}
$$

we note that for all  $t > 1$ ,

$$
\sqrt{t^2 - 1} < t \quad \text{and} \quad \operatorname{arcosh}(t) > 1 \quad \implies \quad t \, \operatorname{arcosh}(t) > \sqrt{t^2 - 1}, \tag{47}
$$

so the numerator is negative while the denominator is positive. Hence

$$
f'(t) < 0 \quad \forall \, t > 1,\tag{48}
$$

i.e.  $f(t)$  is strictly decreasing on  $(1, \infty)$ .

Since  $f(t)$  decreases from 1 to 0 as t runs from  $1^+$  to  $\infty$ , points closest to the current centroid x exert the largest gradient pull, whereas points near the hyperbolic boundary (very far away) exert the smallest pull.

# E Benchmark Datasets

We validate our hyperbolic dataset distillation method using six benchmark datasets: Fashion-MNIST [\[59\]](#page-12-14), SVHN [\[44\]](#page-11-13), CIFAR-10 [\[25\]](#page-10-13), CIFAR-100 [\[25\]](#page-10-13), Tiny ImageNet [\[28\]](#page-10-14), and Image-Woof [\[10\]](#page-9-14).

FashionMNIST is a drop-in replacement for the classic MNIST dataset, comprising 70,000 grayscale images of size  $28 \times 28$  pixels across 10 apparel categories (e.g., T-shirt/top, sneaker) with a 60,000/1,000 train/test split.

**SVHN** contains approximately 600,000 real-world  $32 \times 32$  RGB digit crops (0–9) collected from Google Street View images. It is partitioned into training (73,257), testing (26,032), and an extra set of 531131 samples for data augmentation.

**CIFAR-10** consists of 60,000 32  $\times$  32 color images evenly distributed over 10 object classes (airplane, car, bird, cat, deer, dog, frog, horse, ship, truck). There are five training batches of 10000 images each and one test batch, with exactly 1000 images per class.

**CIFAR-100** (building on CIFAR-10) contains  $60,000$  32  $\times$  32 color images in 100 fine classes (600) images each) grouped into 20 coarse superclasses. Each fine class has a 500/100 train/test split, enabling hierarchical and fine-grained classification studies.

Tiny ImageNet is a subset of the ILSVRC-2012 challenge, selecting 200 classes and resizing all images to  $64 \times 64$  pixels. It provides 100,000 images (500 train, 50 val, 50 test per class), offering a mid-scale benchmark between CIFAR and full ImageNet.

ImageWoof is a challenging subset of 10 visually similar dog breeds drawn from ImageNet (e.g., Beagle, Samoyed, Golden Retriever). It contains 9,025 training and 3,929 validation images, with optional noisy-label variants, and is commonly used to benchmark fine-grained recognition models.

# F Cross-architecture Generalization

Cross-architecture generalization capability serves as a critical metric for evaluating the effectiveness of dataset distillation, where significant performance degradation across different architectures is deemed unacceptable. To assess this capability, we evaluated our method by testing its performance on ConvNet, AlexNet, VGG11, and ResNet18. As demonstrated in Table [4,](#page-16-0) both DM with HDD and IDM with HDD exhibit robust adaptability across diverse architectures. Compared with baseline DM and IDM methods, the HDD-enhanced approach demonstrates superior generalization strength and more stable performance while maintaining architectural compatibility.

<span id="page-16-0"></span>Table 4: The distillation accuracy of CIFAR-10 (IPC = 10) for cross-architecture generalization.

| Model                              | ConvNet                                                  | AlexNet                                                  | VGG11                                                    | ResNet18                                                 |
|------------------------------------|----------------------------------------------------------|----------------------------------------------------------|----------------------------------------------------------|----------------------------------------------------------|
| <b>DSA</b> [67]                    | $52.1 	ext{ } 	ext{ } 0.5$                               | $35.9 	ext{ } 	ext{ } 1.3$                               | $43.2 	ext{ } 	ext{ } 0.5$                               | $35.9 	ext{ } 	ext{ } 1.3$                               |
| KIP [45]                           | $47.6 	ext{ } 	ext{ } 0.9$                               | $24.4 	ext{ } 	ext{ } 3.9$                               | $42.1 	ext{ } 	ext{ } 0.4$                               | $36.8 	ext{ } 	ext{ } 1.0$                               |
| DM [68]                            | $48.9 	ext{ } 	ext{ } 0.6$                               | $38.8 	ext{ } 	ext{ } 0.5$                               | $42.1 	ext{ } 	ext{ } 0.4$                               | $41.2 	ext{ } 	ext{ } 1.1$                               |
| <b>IDM</b> [70]                    | $53.0 	ext{ } 	ext{ } 0.3$                               | $44.6 	ext{ } 	ext{ } 0.8$                               | $47.8 	ext{ } 	ext{ } 1.1$                               | $44.6 	ext{ } 	ext{ } 0.4$                               |
| DM with HDD<br><b>IDM</b> with HDD | $50.3 	ext{ } 	ext{ } 0.3$<br>$61.3 	ext{ } 	ext{ } 0.1$ | $46.3 	ext{ } 	ext{ } 0.4$<br>$57.2 	ext{ } 	ext{ } 0.3$ | $45.7 	ext{ } 	ext{ } 0.3$<br>$58.6 	ext{ } 	ext{ } 0.4$ | $40.2 	ext{ } 	ext{ } 0.4$<br>$56.8 	ext{ } 	ext{ } 0.3$ |

# G Hyperparameter Details

For different experiments, we use distinct hyperbolic curvature K, gradient scaling factor  $\lambda$ , and synthetic image learning rate  $r$ , as shown in Table [5](#page-17-0) and Table [6.](#page-17-1) For the hyperbolic curvature  $K$ , we set it between 0.2 and 3. For the gradient scaling factor  $\lambda$ , we refer to the loss in Hilbert space and ensure that the hyperbolic distance loss maintains the same order of magnitude as the Hilbert space loss through  $\lambda$ . We make minor adjustments to the synthetic image learning rate r while respecting the original method.

# H Details of Baseline Methods

Dataset Condensation (DC) [\[69\]](#page-12-4) achieves this objective by learning a synthetic dataset that, when used alongside the large dataset to train a deep network, results in comparable weight gradients.

Differentiable Siamese Augmentation (DSA) [\[67\]](#page-12-10) enables learning synthetic training sets by applying identical random transformations to both real and synthetic data during training while supporting gradient backpropagation through differentiable augmentations.

Dataset Condensation with Contrastive signals (DCC) [\[30\]](#page-10-16) enhances dataset condensation by matching summed gradients across all classes (unlike class-wise matching in DC) and optimizing synthetic data with contrastive signals. It stabilizes training via kernel velocity tracking and bi-level warm-up, improving fine-grained classification.

Condense dataset by Aligning FEatures (CAFE) [\[56\]](#page-12-7) condenses data by aligning layer-wise features between real and synthetic data, explicitly encoding discriminative power into synthetic clusters, and adaptively adjusting SGD steps via a bi-level optimization scheme.

Dataset Distillation with Attention Matching (DataDAM)[\[48\]](#page-11-1) generates synthetic images by aligning the spatial attention maps of real and synthetic data, produced across various layers of a set of randomly initialized neural networks.

| Dataset      | IPC | DM with HDD |           |                  | IDM with HDD |           |                  |
|--------------|-----|-------------|-----------|------------------|--------------|-----------|------------------|
|              |     | K           | $\lambda$ | $\boldsymbol{r}$ | K            | $\lambda$ | $\boldsymbol{r}$ |
| FashionMNIST | 1   | 1           | 20        | 1                | 2            | 40        | 0.5              |
|              | 10  | 1           | 40        | 1                | 2            | 60        | 1                |
|              | 50  | 1           | 60        | 1                | 2            | 80        | 0.2              |
| SVHN         | 1   | 1           | 10        | 1                | 2            | 120       | 0.5              |
|              | 10  | 1           | 50        | 1                | 2            | 120       | 1                |
|              | 50  | 1           | 100       | 1                | 2            | 120       | 0.2              |
| CIFAR 10     | 1   | 1           | 1         | 1                | 3            | 80        | 0.5              |
|              | 10  | 1           | 20        | 1                | 3            | 100       | 1                |
|              | 50  | 1           | 80        | 1                | 3            | 120       | 0.2              |
| CIFAR 100    | 1   | 1           | 10        | 1                | 2            | 60        | 0.5              |
|              | 10  | 2           | 100       | 1                | 2            | 80        | 0.2              |
|              | 50  | 2           | 120       | 1                | 2            | 100       | 0.6              |
| TinyImageNet | 1   | -           | -         | -                | 2            | 80        | 0.5              |
|              | 10  | -           | -         | -                | 2            | 100       | 0.5              |
|              | 50  | -           | -         | -                | 2            | 120       | 0.6              |

<span id="page-17-0"></span>Table 5: Hyperparameter details of DM with HDD and IDM with HDD.

<span id="page-17-1"></span>Table 6: Hyperparameter details of Dance with HDD.

| Dataset          | <b>IPC</b> |                             | DM with HDD |            |  |  |
|------------------|------------|-----------------------------|-------------|------------|--|--|
|                  |            | К                           |             | r          |  |  |
|                  | 1          | 1.8                         | 20          | 0.02       |  |  |
| $CIFAR-10$       | 10<br>50   | 0.2<br>$\mathfrak{D}$       | 40<br>60    | 0.2<br>0.5 |  |  |
|                  |            |                             |             |            |  |  |
|                  | 1          | $\mathcal{D}_{\mathcal{L}}$ | 40          | 0.02       |  |  |
| $CIFAR-100$      | 10         | 1.5                         | 80          | 0.1        |  |  |
|                  | 50         | $\mathfrak{D}$              | 120         | 0.5        |  |  |
| <b>ImageWoof</b> | 1          | 0.6                         | 100         | 0.1        |  |  |
|                  | 10         | 0.5                         | 120         | 0.1        |  |  |

**Distribution Matching (DM)** [\[68\]](#page-12-1) is the first to use maximum mean discrepancy to optimize synthetic data to match the distribution of the original data.

Improved Distribution Matching (IDM) [\[70\]](#page-12-6) enhances DM by addressing feature imbalance through Partitioning and Expansion augmentation, and correcting invalid MMD estimation using enriched semi-trained model embeddings and class-aware distribution regularization, resulting in more accurate feature alignment and improved performance.

Generalized Various Backbone and Statistical Matching (G-VBSM) [\[50\]](#page-11-10) is a novel framework for generalized dataset condensation, comprising three key components: data densification enhances intraclass diversity by ensuring linear independence within each class; generalized statistical matching captures patch- and channel-level convolutional statistics without gradient updates for effective synthesis; and generalized backbone matching enforces consistency across diverse backbones, boosting generalization. Together, they enable efficient and robust generalized matching.

Difficulty-Aligned Trajectory Matching (DATM) [\[22\]](#page-10-17) dynamically adjusts the difficulty of synthetic data (matching the early or late training trajectories of the teacher network) to adapt to the scale of the synthetic dataset—small datasets correspond to simple modes (early trajectories), while large datasets correspond to complex modes (late trajectories). This approach achieves lossless dataset distillation for the first time.

Realistic, Diverse, and Efficient Dataset Distillation (RDED) [\[54\]](#page-12-12) is a non-optimization-based dataset distillation method that enhances realism by cropping realistic patches from original images and improves diversity by stitching these patches into new synthetic images, achieving high efficiency and superior performance on large-scale, high-resolution datasets.

Dataset Distillation via Disentangled Diffusion Model  $(D^4M)$  [\[51\]](#page-11-5) leverages a disentangled diffusion model with a novel training-time matching strategy to efficiently distill high-resolution, realistic datasets while improving cross-architecture generalization and reducing computational costs.

Inter-sample and Inter-feature Relations in Dataset Distillation (IID) [\[11\]](#page-9-16) introduces two key constraints to improve distribution matching: a class centralization constraint to enhance intra-class feature clustering, and a covariance matching constraint to accurately align feature distributions by considering both mean and covariance, even with limited synthetic samples.

Diversified Semantic Distribution Matching (DSDM) [\[37\]](#page-11-15) distills datasets by aligning the semantic distributions—represented as Gaussian prototypes and covariance matrices—of distilled data with those of original data.

Minimizing the Maximum Mean Discrepancy (M3D) [\[65\]](#page-12-17) enhances DM-based dataset condensation by aligning not only the first but also higher-order moments of feature distributions through kernel-based Maximum Mean Discrepancy, enabling more accurate distribution matching with theoretical guarantees and strong performance across diverse datasets.

Dual-view distribution AligNment for dataset CondEnsation (DANCE) [\[64\]](#page-12-5) introduces a dualview approach to dataset condensation by leveraging expert models: it performs pseudo long-term distribution alignment via a convex combination of initialized and trained models to align inner-class distributions without persistent training, and applies distribution calibration using expert models to mitigate inter-class distribution shift and preserve class boundaries.

| Table 7. Comparison on TinyImageNet with different IPCs. |                                       |                                       |                                       |
|----------------------------------------------------------|---------------------------------------|---------------------------------------|---------------------------------------|
| Method                                                   | IPC = 1 (0.2%)                        | IPC = 10 (2%)                         | IPC = 50 (10%)                        |
| Random [7]                                               | 1.4 <span>±</span> 0.1                | 5.0 <span>±</span> 0.2                | 15.0 <span>±</span> 0.4               |
| Herding [58]                                             | 2.8 <span>±</span> 0.2                | 6.3 <span>±</span> 0.2                | 16.7 <span>±</span> 0.3               |
| K-Center [49]                                            | 1.6 <span>±</span> 0.2                | 5.1 <span>±</span> 0.1                | 15.0 <span>±</span> 0.3               |
| Forgetting [55]                                          | 1.6 <span>±</span> 0.2                | 5.1 <span>±</span> 0.3                | 15.0 <span>±</span> 0.1               |
| DC [69]                                                  | 5.3 <span>±</span> 0.1                | 12.9 <span>±</span> 0.1               | 12.7 <span>±</span> 0.4               |
| <b>DSA</b> [67]                                          | 5.7 <span>±</span> 0.1                | 16.3 <span>±</span> 0.2               | 5.1 <span>±</span> 0.2                |
| DataDAM [48]                                             | 8.3 <span>±</span> 0.4                | 18.7 <span>±</span> 0.3               | <b>28.7</b> <span>±</span> <b>0.3</b> |
| MTT [4]                                                  | 6.2 <span>±</span> 0.4                | 17.3 <span>±</span> 0.2               | 26.5 <span>±</span> 0.3               |
| IDM [70]                                                 | 10.1 <span>±</span> 0.2               | 21.9 <span>±</span> 0.6               | 26.9 <span>±</span> 0.2               |
| <b>IDM with HDD</b>                                      | <b>11.9</b> <span>±</span> <b>0.2</b> | <b>22.4</b> <span>±</span> <b>0.3</b> | 27.8 <span>±</span> 0.3               |
| <b>Whole Dataset</b>                                     |                                       | 37.6 <span>±</span> 0.6               |                                       |

Table 7: Comparison on TinyImageNet with different IPCs.

<span id="page-18-0"></span>

# I Results on TinyImageNet

We compare IDM with HDD against DC [\[69\]](#page-12-4), DSA [\[67\]](#page-12-10), DataDAM [\[48\]](#page-11-1), MTT [\[4\]](#page-9-3), and IDM [\[70\]](#page-12-6)

on TinyImageNet, as shown in Table [7.](#page-18-0) Our method achieves superior performance at both IPC =  $1$ and IPC = 10. Furthermore, compared to IDM, IDM with HDD demonstrates improvements of 1.8%, 0.5%, and 0.9% at IPC = 1, IPC = 10, and IPC = 50, respectively.

# J Visualization of Distilled Images

We showcased a portion of the synthetic dataset distilled through HDD. Figure [5](#page-19-0) displays the FashionMNIST samples synthesized using the DM with HDD at IPC = 50, while Figure [6](#page-19-1) shows the analogous SVHN outputs under identical conditions. Figures 3 and 4 correspond to CIFAR-10: Figure [7](#page-19-2) (a) and (b) depict the IDM with HDD results at IPC = 1 and IPC = 10, respectively, and Figure [8](#page-20-0) demonstrates the IPC = 50 case. Figure [9](#page-21-0) extends this analysis to CIFAR-100, presenting IDM with HDD distillations at IPC = 1 (a), IPC = [10](#page-22-0) (b), and IPC = 50 (c). Finally, Figure 10 illustrates the ImageWoof distilled samples obtained via the Dance with HDD at  $IPC = 1$  (a) and IPC  $= 10$  (b).

<span id="page-19-0"></span>Image /page/19/Figure/1 description: A grid of fashion items on a black background. The items are arranged in rows and columns, with each item being a grayscale image of clothing or footwear. The top rows feature t-shirts and pants, followed by sweaters and shirts. The middle rows display dresses, more shirts, and sandals. The lower rows showcase sneakers, handbags, and boots. The overall impression is a catalog or dataset of various apparel types.

Figure 5: The distilled images of FashionMNIST with IPC = 50 using DM with HDD.

<span id="page-19-1"></span>Image /page/19/Picture/3 description: The image is a grid of 10 rows and 20 columns, with each cell containing a single digit. The digits appear to be from a dataset of handwritten or machine-printed numbers, possibly from street signs or house numbers, given the varied backgrounds and textures. The digits range from 0 to 9, and they are presented in a somewhat random order throughout the grid. Some digits are clearer than others, with some appearing slightly blurred or partially obscured by the background.

Figure 6: The distilled images of SVHN with IPC = 50 using DM with HDD.

<span id="page-19-2"></span>Image /page/19/Figure/5 description: The image displays two grids of smaller images, labeled (a) on the left and (b) on the right. Grid (a) is a vertical stack of 15 smaller images, each showing a different scene or object. Grid (b) is a larger, more densely packed grid of smaller images, arranged in approximately 15 rows and 15 columns. Both grids appear to contain a variety of subjects, including vehicles (cars, trucks, boats, airplanes), animals (dogs, cats, birds, deer), and possibly landscapes or abstract patterns. The overall impression is a collection of diverse visual samples, likely generated or categorized by a machine learning model.

Figure 7: The distilled images of CIFAR-10 with IPC = 1 (a) and IPC = 10 (b) using IDM with HDD.

<span id="page-20-0"></span>Image /page/20/Picture/0 description: The image is a collage of many small images, arranged in a grid. The collage appears to be a collection of diverse subjects, including animals, vehicles, and possibly landscapes or objects. The grid is densely packed, with no visible gaps between the individual images. The overall impression is one of a large dataset or a visual catalog of various items.

Figure 8: The distilled images of CIFAR-10 with IPC = 50 using IDM with HDD.

<span id="page-21-0"></span>Image /page/21/Figure/0 description: The image displays three vertical panels labeled (a), (b), and (c) from left to right. Each panel is filled with a grid of small, colorful images. Panel (a) is the narrowest, followed by panel (b), and panel (c) is the widest. The small images within the panels appear to be diverse, showing various objects, scenes, and possibly people, arranged in rows and columns. The overall impression is a collection of distilled images, likely representing data or samples from a larger dataset.

Figure 9: The distilled images of CIFAR-100 with IPC = 1 (a), IPC = 10 (b), and IPC = 50 (c) using IDM with HDD.

<span id="page-22-0"></span>Image /page/22/Picture/0 description: The image displays two grids of images, labeled (a) and (b). Grid (a) is a single horizontal row of small, square images, predominantly featuring dogs in various poses and settings. Grid (b) is a larger, square grid composed of many more small, square images, also primarily showing dogs. The images in both grids appear to be generated or sampled, with some exhibiting a painterly or abstract quality, while others are more photorealistic. The overall impression is a collection of diverse dog portraits.

Figure 10: The distilled images of ImageWoof with IPC = 1 (a) and IPC = 10 (b) using Dance with HDD.
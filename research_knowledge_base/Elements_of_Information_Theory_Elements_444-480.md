Theorem 14.6.1: The capacity region of a broadcast channel depends only on the conditional marginal distributions  $p(y_1|x)$  and  $p(y_2|x)$ .

**Proof:** See exercises.  $\Box$ 

# 14.6.2 Degraded Broadcast Channels

**Definition:** A broadcast channel is said to be *physically degraded* if  $p(y_1, y_2|x) = p(y_1|x)p(y_2|y_1).$ 

**Definition:** A broadcast channel is said to be *stochastically degraded* if its conditional marginal distributions are the same as that of a physically degraded broadcast channel, i.e., if there exists a distribution  $p'(y_2|y_1)$  such that

$$
p(y_2|x) = \sum_{y_1} p(y_1|x)p'(y_2|y_1).
$$
 (14.201)

Note that since the capacity of a broadcast channel depends only on the conditional marginals, the capacity region of the stochastically degraded broadcast channel is the same as that of the corresponding physically degraded channel. In much of the following, we will therefore assume that the channel is physically degraded.

## 14.6.3 Capacity Region for the Degraded Broadcast Channel

We now consider sending independent information over a degraded broadcast channel at rate  $R_1$  to  $Y_1$  and rate  $R_2$  to  $Y_2$ .

Theorem 14.6.2: The capacity region for sending independent informa $t_{\rm in}$  over the degraded broadcast channel  $X$ ,  $Y$ ,  $\overline{Y}$  is the convex hull  $\frac{1}{2}$  of the algorithm of all  $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2$ 

$$
R_2 \le I(U; Y_2), \tag{14.202}
$$

$$
R_1 \le I(X; Y_1 | U) \tag{14.203}
$$

for some joint distribution p(u)p(x I u)p(x  $\frac{1}{p}$  u) and an auxiliary range the auxiliary range  $\frac{1}{p}$ for some found also counting  $p(x | u)p(y, z | x)$ , where the dustituty run

Proof: The cardinality bounds for the auxiliary random variable U  $\bf r$  root. The cardinality bounds for the auxiliary random variable  $\bf o$ are derived using standard methods from convex set theory and will not be dealt with here.

We first give an outline of the basic idea of superposition coding for the broadcast channel. The auxiliary random variable U will serve as a cloud center that can be distinguished by both receivers  $Y_1$  and  $Y_2$ . Each cloud consists of  $2^{nR_1}$  codewords  $X^n$  distinguishable by the receiver  $Y_1$ . The worst receiver can only see the clouds, while the better receiver can see the individual codewords within the clouds.

The formal proof of the achievability of this region uses a random coding argument: Fix  $p(u)$  and  $p(x|u)$ .

Random codebook generation. Generate 2<sup>nera</sup> independent codewords of length n,  $U(w_2)$ ,  $w_2 \in \{1, 2, ..., 2^{m}n2}\}$ , according to  $\Pi_{i=1}^n p(u_i)$ . For each codeword  $U(w_2)$ , generate  $2^{m_1}$  independent codewords  $\mathbf{X}(w_1, w_2)$  according to  $\prod_{i=1}^n p(x_i|u_i(w_2)).$ 

Here  $\mathbf{u}(i)$  plays the role of the cloud center understandable to both  $Y_1$  and  $Y_2$ , while  $\mathbf{x}(i, j)$  is the jth satellite codeword in the *i*th cloud.

- *Encoding*. To send the pair  $(W_1, W_2)$ , send the corresponding codeword  $\mathbf{X}(W_1, W_2)$ .
- Decoding. Receiver 2 determines the unique  $\hat{W}_2$  such that  $(U(\hat{\hat{W}}_2), Y_2) \in A_{\epsilon}^{(n)}$ . If there are none such or more than one such, an error is declared.

Receiver 1 looks for the unique  $(\hat{W}_1, \hat{W}_2)$  such that  $(U(\hat{W}_2), X(\hat{W}_1, \hat{W}_2), Y_1) \in A_{\epsilon}^{(n)}$ . If there are none such or more than one such, an error is declared.

Analysis of the probability of error. By the symmetry of the code generation, the probability of error does not depend on which codeword was sent. Hence, without loss of generality, we can assume that the message pair  $(W_1, W_2) = (1, 1)$  was sent. Let  $P(\cdot)$ denote the conditional probability of an event given that  $(1,1)$  was sent.

Since we have essentially a single user channel from  $U$  to  $Y_2$ , we will be able to decode the  $U$  codewords with low probability of error if  $R_2 < I(U; Y_2)$ . To prove this, we define the events

$$
E_{Y_i} = \{ (\mathbf{U}(i), \mathbf{Y}_2) \in A_{\epsilon}^{(n)} \} .
$$
 (14.204)

Then the probability of error at receiver 2 is

$$
P_e^{(n)}(2) = P(E_{Y1}^c \cup \bigcup_{i \neq 1} E_{Yi})
$$
 (14.205)

$$
\leq P(E_{Y1}^c) + \sum_{i \neq 1} P(E_{Yi}) \tag{14.206}
$$

$$
\leq \epsilon + 2^{nR_2} 2^{-n(I(U; Y_2) - 2\epsilon)} \tag{14.207}
$$

$$
\leq 2\epsilon \tag{14.208}
$$

if n is large enough and  $R_2 < I(U; Y_2)$ , where (14.207) follows from the AEP.

Similarly, for decoding for receiver 1, we define the following events

$$
\widetilde{E}_{Yi} = \{ (\mathbf{U}(i), \mathbf{Y}_1) \in A_{\epsilon}^{(n)} \}, \qquad (14.209)
$$

$$
\widetilde{E}_{Yij} = \{ (\mathbf{U}(i), \mathbf{X}(i, j), \mathbf{Y}_1) \in A_{\epsilon}^{(n)} \}, \qquad (14.210)
$$

where the tilde refers to events defined at receiver 1. Then, we can bound the probability of error as

$$
P_e^{(n)}(1) = P\left(\tilde{E}_{Y1}^c \cup \bigcup_{i \neq 1} \tilde{E}_{Yi} \cup \bigcup_{j \neq 1} \tilde{E}_{Y1j}\right) \tag{14.211}
$$

$$
\leq P(\widetilde{E}_{Y1}^c) + \sum_{i \neq 1} P(\widetilde{E}_{Yi}) + \sum_{j \neq 1} P(\widetilde{E}_{Y1j}). \quad (14.212)
$$

By the same arguments as for receiver 2, we can bound  $P(\tilde{E}_{\cdots}) \leq 1$  $2^{-n(I(U; Y_1)-3\epsilon)}$  Hence the second term goes to 0 if  $R_s < I(U: Y_1)$ . But by the data processing inequality and the degraded nature of the channel,  $I(U; Y_1) \geq I(U; Y_2)$ , and hence the conditions of the theorem imply that the second term goes to 0. We can also bound the third term in the probability of error as

$$
P(\tilde{E}_{Y1j}) = P((\mathbf{U}(1), \mathbf{X}(1, j), \mathbf{Y}_1) \in A_{\epsilon}^{(n)})
$$
(14.213)

$$
= \sum_{(\mathbf{U}, \mathbf{X}, \mathbf{Y}_1) \in A_{\epsilon}^{(n)}} P((\mathbf{U}(1), \mathbf{X}(1, j), \mathbf{Y}_1))
$$
(14.214)

$$
= \sum_{(\mathbf{U}, \mathbf{X}, \mathbf{Y}_1) \in A_{\epsilon}^{(n)}} P(\mathbf{U}(1)) P(\mathbf{X}(1, j) | \mathbf{U}(1)) P(\mathbf{Y}_1 | \mathbf{U}(1)) \qquad (14.215)
$$

$$
\leq \sum_{(\mathbf{U},\mathbf{X},\mathbf{Y}_1)\in A_{\epsilon}^{(n)}} 2^{-n(H(U)-\epsilon)} 2^{-n(H(X|U)-\epsilon)} 2^{-n(H(Y_1|U)-\epsilon)} \qquad (14.216)
$$

$$
\leq 2^{n(H(U,X,Y_1)+\epsilon)}2^{-n(H(U)-\epsilon)}2^{-n(H(X|U)-\epsilon)}2^{-n(H(Y_1|U)-\epsilon)} (14.217)
$$

$$
=2^{-n(I(X; Y_1|U)-4\epsilon)}
$$
\n(14.218)

 $H_{\text{max}}$ , if  $D \geq I(V, V | II)$ , the third terms in the probability of error  $\frac{1}{2}$  or  $\frac{1}{2}$   $\frac{1}{2}$   $\frac{1}{2}$  or  $\frac{1}{2}$  is the probability of errors of errors of errors of errors of errors of extensions of the probability of extensions of the probability of the probability of the prob

$$
P_e^{(n)}(1) \leq \epsilon + 2^{nR_2} 2^{-n(I(U; Y_1) - 3\epsilon)} + 2^{nR_1} 2^{-n(I(X; Y_1 | U) - 4\epsilon)}
$$
(14.219)

$$
\leq 3\epsilon \tag{14.220}
$$

if n is large enough and  $R_2 < I(U; Y_1)$  and  $R_1 < I(X; Y_1 | U)$ . The above bounds show that we can decode the messages with total probability of error that goes to 0. Hence theres exists a sequence of good  $((2^{nR_1}, 2^{nR_2}), n)$  codes  $\mathscr{C}_n^*$  with probability of error going to 0.

With this, we complete the proof of the achievability of the capacity region for the degraded broadcast channel. The proof of the converse is outlined in the exercises.  $\Box$ 

So far we have considered sending independent information to both receivers. But in certain situations, we wish to send common information to both the receivers. Let the rate at which we send common information be  $R_0$ . Then we have the following obvious theorem:

**Theorem 14.6.3:** If the rate pair  $(R_1, R_2)$  is achievable for a broadcast channel with independent information, then the rate triple  $(R_0, R_1 R_0, R_2 - R_0$ ) with a common rate  $R_0$  is achievable, provided that  $R_0 \leq$  $min(R_1, R_2)$ .

In the case of a degraded broadcast channel, we can do even better. Since by our coding scheme the better receiver always decodes all the information that is sent to the worst receiver, one need not reduce the amount of information sent to the better receiver when we have common information. Hence we have the following theorem:

**Theorem 14.6.4:** If the rate pair  $(R_1, R_2)$  is achievable for a degraded broadcast channel, the rate triple  $(R_0, R_1, R_2 - R_0)$  is achievable for the channel with common information, provided that  $R_0 < R_2$ .

We will end this section by considering the example of the binary symmetric broadcast channel.

Example 14.6.6: Consider a pair of binary symmetric channels with parameters  $p_1$  and  $p_2$  that form a broadcast channel as shown in Figure 14.26.

Without loss of generality in the capacity calculation, we can recast this channel as a physically degraded channel. We will assume that  $p_1 < p_2 < \frac{1}{2}$ . Then we can express a binary symmetric channel with parameter  $p_2$  as a cascade of a binary symmetric channel with parameter  $p_1$  with another binary symmetric channel. Let the crossover probability of the new channel be  $\alpha$ . Then we must have

$$
p_1(1-\alpha) + (1-p_1)\alpha = p_2, \qquad (14.221)
$$

Image /page/4/Figure/1 description: This is a diagram illustrating a binary channel with two outputs, Y1 and Y2. The input X can be either 0 or 1. When X is 0, it can be transmitted as Y1=0 or Y1=1, and as Y2=0 or Y2=1. Similarly, when X is 1, it can be transmitted as Y1=0 or Y1=1, and as Y2=0 or Y2=1. The diagram shows arrows connecting the input states to the output states, indicating possible transmission paths.

Figure 14.26. Binary symmetric broadcast channel.

$$
\alpha = \frac{p_2 - p_1}{1 - 2p_1} \,. \tag{14.222}
$$

We now consider the auxiliary random variable in the definition of the capacity region. In this case, the cardinality of  $U$  is binary from the bound of the theorem. By symmetry, we connect  $U$  to  $X$  by another binary symmetric channel with parameter  $\beta$ , as illustrated in Figure 14.27.

We can now calculate the rates in the capacity region. It is clear by symmetry that the distribution on  $U$  that maximizes the rates is the uniform distribution on  $\{0, 1\}$ , so that

$$
I(U; Y_2) = H(Y_2) - H(Y_2|U)
$$
 (14.223)

$$
= 1 - H(\beta * p_2), \qquad (14.224)
$$

where

$$
\beta * p_2 = \beta (1 - p_2) + (1 - \beta) p_2. \qquad (14.225)
$$

Image /page/4/Figure/10 description: This is a diagram illustrating a series of probabilistic transitions. It begins with a variable labeled 'U', which transitions to a variable labeled 'X' through two possible paths, each with a probability of 1-beta or beta. From 'X', there are transitions to 'Y1' with probabilities p1 and 1-p1 for each path. Finally, 'Y1' transitions to 'Y2' with probabilities alpha and 1-alpha for each path.

Figure 14.27. Physically degraded binary symmetric broadcast channel.

Similarly,

$$
I(X; Y_1|U) = H(Y_1|U) - H(Y_1|X, U)
$$
\n(14.226)

$$
=H(Y_1|U) - H(Y_1|X) \tag{14.227}
$$

$$
= H(\beta * p_1) - H(p_1), \qquad (14.228)
$$

where

$$
\beta * p_1 = \beta (1 - p_1) + (1 - \beta) p_1. \tag{14.229}
$$

Plotting these points as a function of  $\beta$ , we obtain the capacity region in Figure 14.28.

When  $\beta = 0$ , we have maximum information transfer to  $Y_2$ , i.e.,  $R_2 = 1 - H(p_2)$  and  $R_1 = 0$ . When  $\beta = \frac{1}{2}$ , we have maximum information transfer to  $Y_1$ , i.e.,  $R_1 = 1 - H(p_1)$ , and no information transfer to  $Y_2$ . These values of  $\beta$  give us the corner points of the rate region.

**Example 14.6.6** (Gaussian broadcast channel): The Gaussian broadcast channel is illustrated in Figure 14.29. We have shown it in the case where one output is a degraded version of the other output. Later, we will show that all Gaussian broadcast channels are equivalent to this type of degraded channel.

$$
Y_1 = X + Z_1, \t(14.230)
$$

$$
Y_2 = X + Z_2 = Y_1 + Z'_2, \tag{14.231}
$$

where  $Z_1 \sim \mathcal{N}(0, N_1)$  and  $Z_2' \sim \mathcal{N}(0, N_2 - N_1)$ .

Extending the results of this section to the Gaussian case, we can show that the capacity region of this channel is given by

Image /page/5/Figure/14 description: The image is a graph with R1 on the x-axis and R2 on the y-axis. The graph shows a curve starting at the point (0, 1-H(p2)) and decreasing to the point (1-H(p1), 0). The curve is concave down.

Figure 14.28. Capacity region of binary symmetric broadcast channel.

Image /page/6/Figure/1 description: This is a block diagram illustrating a communication system. An input signal labeled 'X' enters a circle with a plus sign, representing an adder. A signal labeled 'Z1' is added to 'X' at this point, producing an intermediate signal labeled 'Y1'. 'Y1' then enters a second circle with a plus sign, where it is added to another signal labeled 'Z 2 '. The output of this second adder is labeled 'Y2'.

Figure 14.29. Gaussian broadcast channel.

$$
R_1 < C\left(\frac{\alpha P}{N_1}\right) \tag{14.232}
$$

$$
R_2 < C\left(\frac{(1-\alpha)P}{\alpha P + N_2}\right) \tag{14.233}
$$

where  $\alpha$  may be arbitrarily chosen ( $0 \leq \alpha \leq 1$ ). The coding scheme that achieves this capacity region is outlined in Section 14.1.3.

## 14.7 THE RELAY CHANNEL

The relay channel is a channel in which there is one sender and one receiver with a number of intermediate nodes which act as relays to help the communication from the sender to the receiver. The simplest relay channel has only one intermediate or relay node. In this case the channel consists of four finite sets  $\mathcal{X}, \mathcal{X}_1, \mathcal{Y}$  and  $\mathcal{Y}_1$  and a collection of probability mass functions  $p(\cdot, \cdot | x, x_1)$  on  $\mathcal{Y} \times \mathcal{Y}_1$ , one for each  $(x, x_1) \in$  $\mathscr{X} \times \mathscr{X}_1$ . The interpretation is that x is the input to the channel and y is the output of the channel,  $y_1$  is the relay's observation and  $x_1$  is the input symbol chosen by the relay, as shown in Figure 14.30. The problem is to find the capacity of the channel between the sender X and the receiver Y.

The relay channel combines a broadcast channel  $(X$  to Y and  $Y_1$ ) and a multiple access channel  $(X \text{ and } X_1 \text{ to } Y)$ . The capacity is known for the special case of the physically degraded relay channel. We will first prove an outer bound on the capacity of a general relay channel and later establish an achievable region for the degraded relay channel.

**Definition:** A  $(2^{n}, n)$  code for a relay channel consists of a set of integers  $W = \{1, 2, \ldots, 2^{n} \}$ , an encoding function

Image /page/6/Figure/10 description: A directed graph shows three nodes labeled X, Y, and Y1:X1. There is a directed edge from X to Y, and another directed edge from X to Y1:X1. There is also a directed edge from Y1:X1 to Y.

Figwe 14.30. The relay channel.

$$
X: \{1, 2, \ldots, 2^{nR}\} \to \mathcal{X}^n \,, \tag{14.234}
$$

a set of relay functions  ${f_i}_{i=1}^n$  such that

$$
x_{1i} = f_i(Y_{11}, Y_{12}, \dots, Y_{1i-1}), \qquad 1 \le i \le n , \qquad (14.235)
$$

and a decoding function,

$$
g: \mathcal{Y}^n \to \{1, 2, \dots, 2^{nR}\} \,. \tag{14.236}
$$

Note that the definition of the encoding functions includes the nonanticipatory condition on the relay. The relay channel input is allowed to depend only on the past observations  ${\bf y}_{11},\,{\bf y}_{12},\ldots$  ,  ${\bf y}_{1i-1}.$  The channel is memoryless in the sense that  $(Y_i, Y_{1i})$  depends on the past only through the current transmitted symbols  $(X_i, X_{1i})$ . Thus for any choice  $p(w)$ ,  $w \in W$ , and code choice  $X: \{1, 2, ..., 2^{m}\} \rightarrow \mathscr{X}_{i}^{n}$  and relay function  $\{f_i\}_{i=1}^n$ , the joint probability mass function on  $W \times \mathscr{X}^n \times \mathscr{X}^n_1 \times \mathscr{Y}^n \times \mathscr{Y}^n_1$ is given by

$$
p(w, \mathbf{x}, \mathbf{x}_1, \mathbf{y}, \mathbf{y}_1)
$$

$$
= p(w) \prod_{i=1}^n p(x_i|w)p(x_{1i}|y_{11}, y_{12}, \dots, y_{1i-1})p(y_i, y_{1i}|x_i, x_{1i}). (14.237)
$$

If the message  $w \in [1, 2^{nR}]$  is sent, let

$$
\lambda(w) = \Pr\{g(\mathbf{Y}) \neq w | w \text{ sent}\}\tag{14.238}
$$

denote the conditional probability of error. We define the average probability of error of the code as

$$
P_e^{(n)} = \frac{1}{2^{nR}} \sum_w \lambda(w) \,. \tag{14.239}
$$

The probability of error is calculated under the uniform distribution over the codewords  $w \in \{1, 2^{nR}\}\)$ . The rate R is said to be achievable by the relay channel if there exists a sequence of  $(2^{nR}, n)$  codes with  $P_e^{(n)} \rightarrow 0$ . The capacity C of a relay channel is the supremum of the set of achievable rates.

We first give an upper bound on the capacity of the relay channel.

**Theorem 14.7.1:** For any relay channel  $(\mathscr{X} \times \mathscr{X}_1, p(\mathsf{y}, \mathsf{y}_1 | \mathsf{x}, \mathsf{x}_1), \mathsf{Y} \times \mathsf{Y}_1)$ the capacity C is bounded above by

$$
C \leq \sup_{p(x, x_1)} \min\{I(X, X_1; Y), I(X; Y, Y_1|X_1)\}.
$$
 (14.240)

Proof: The proof is a direct consequence of a more general max flow min cut theorem to be given in Section 14.10.  $\Box$ 

This upper bound has a nice max flow min cut interpretation. The first term in (14.240) upper bounds the maximum rate of information transfer from senders  $X$  and  $X_1$  to receiver  $Y$ . The second terms bound the rate from  $X$  to  $Y$  and  $Y_1$ .

We now consider a family of relay channels in which the relay receiver is better than the ultimate receiver  $Y$  in the sense defined below. Here the max flow min cut upper bound in the (14.240) is achieved.

**Definition:** The relay channel  $(\mathscr{X} \times \mathscr{X}_1, p(y, y_1 | x, x_1), \mathscr{Y} \times \mathscr{Y}_1)$  is said to be *physically degraded* if  $p(y, y_1|x, x_1)$  can be written in the form

$$
p(y, y_1 | x, x_1) = p(y_1 | x, x_1) p(y | y_1, x_1).
$$
 (14.241)

Thus Y is a random degradation of the relay signal  $Y_1$ .

For the physically degraded relay channel, the capacity is given bY the following theorem.

**Theorem 14.7.2:** The capacity C of a physically degraded relay channel is given by

$$
C = \sup_{p(x, x_1)} \min\{I(X, X_1; Y), I(X; Y_1|X_1)\},
$$
 (14.242)

where the supremum is over all joint distributions on  $\mathcal{X} \times \mathcal{X}_1$ .

Proof (Converse): The proof follows from Theorem 14.7.1 and by degradedness, since for the degraded relay channel,  $I(X; Y, Y_1|X_1) =$  $I(X; Y_1 | X_1).$ 

Achievability. The proof of achievability involves a combination of the following basic techniques: (1) random coding, (2) list codes, (3) Slepian-Wolf partitioning, (4) coding for the cooperative multiple access channel, (5) superposition coding, and (6) block Markov encoding at the relay and transmitter.

We provide only an outline of the proof.

Outline of achievability. We consider B blocks of transmission, each of *n* symbols. A sequence of  $B-1$  indices,  $w_i \in \{1, \ldots, 2^{nR}\}, i =$  $1, 2, \ldots, B-1$  will be sent over the channel in nB transmissions. (Note that as  $B \to \infty$ , for a fixed n, the rate  $R(B - 1)/B$  is arbitrarily close to  $R$ .)

We define a doubly-indexed set of codewords:

$$
\mathscr{C} = \{ \mathbf{x}(w|s), \mathbf{x}_1(s) \} : w \in \{1, 2^{nR}\}, s \in \{1, 2^{nR_0}\}, \mathbf{x} \in \mathscr{X}^n, \mathbf{x}_1 \in \mathscr{X}_1^n. \tag{14.243}
$$

We will also need a partition

$$
\mathcal{G} = \{S_1, S_2, \dots, S_{2^{nR_0}}\} \text{ of } \mathcal{W} = \{1, 2, \dots, 2^{nR}\} \qquad (14.244)
$$

into  $2^{nR_0}$  cells, with  $S_i \cap S_j = \phi$ ,  $i \neq j$ , and  $\cup S_i = \mathcal{W}$ . The partition will enable us to send side information to the receiver in the manner of Slepian and Wolf [255].

Generation of random code. Fix  $p(x_1)p(x|x_1)$ .

First generate at random  $2^{nR}$  i.i.d. *n*-sequences in  $\mathscr{X}_1$ , each drawn according to  $p(\mathbf{x}_1) = \prod_{i=1}^n p(x_{1i})$ . Index them as  $\mathbf{x}_1(s)$ ,  $s \in$  $\{1,2,\ldots,2^{n\alpha_0}\}$ . For each  $\mathbf{x}_1(s)$ , generate  $2^{n\alpha_0}_1$  conditionally indepen dent n-sequences  $\mathbf{x}(w|s)$ ,  $w \in \{1, 2, ..., 2^{nR}\}$ , drawn independently according to  $p(\mathbf{x}|\mathbf{x}_{1}(s)) = \prod_{i=1}^{n} p(x_{i}|x_{1i}(s))$ . This defines the random codebook  $\mathscr{C} = {\mathbf{x}(w|s), \mathbf{x}_1(s)}.$ 

The random partition  $\mathcal{S} = \{S_1, S_2, \ldots, S_{nR_0}\}\$  of  $\{1, 2, \ldots, 2^{nR}\}\$ is defined as follows. Let each integer  $w \in \{1, 2, ..., 2^{nR}\}$  be assigned independently, according to a uniform distribution over the indices  $s = 1, 2, \ldots, 2^{nR_0}$ , to cells  $S_s$ .

- *Encoding.* Let  $w_i \in \{1, 2, ..., 2^{nR}\}\)$  be the new index to be sent in block i, and let  $s_i$  be defined as the partition corresponding to  $w_{i-1}$ , i.e.,  $w_{i-1} \in S_{s_i}$ . The encoder sends  $\mathbf{x}(w_i|s_i)$ . The relay has an estimate  $\hat{w}_{i-1}$  of the previous index  $w_{i-1}$ . (This will be made precise in the decoding section.) Assume that  $\hat{w}_{i-1} \in S_{\hat{s}}$ . The relay encoder sends  $\mathbf{x}_i(\hat{s}_i)$  in block i.
- *Decoding.* We assume that at the end of block  $i 1$ , the receiver knows  $(w_1, w_2, ..., w_{i-2})$  and  $(s_1, s_2, ..., s_{i-1})$  and the relay knows  $(w_1, w_2, \ldots, w_{i-1})$  and consequently  $(s_1, s_2, \ldots, s_i)$ .

The decoding procedures at the end of block *i* are as follows:

1. Knowing  $s_i$  and upon receiving  $y_1(i)$ , the relay receiver estimates the message of the transmitter  $\hat{w}_i = w$  if and only if there exists a unique w such that  $(\mathbf{x}(w|s_i), \mathbf{x}_1(s_i), \mathbf{y}_1(i))$  are jointly  $\epsilon$ -typical. Using Theorem 14.2.3, it can be shown that  $\hat{w}_i = w_i$  with an arbitrarily small probability of error if

$$
R < I(X; Y_1 | X_1) \tag{14.245}
$$

and  $n$  is sufficiently large.

2. The receiver declares that  $\hat{s}_i = s$  was sent iff there exists one and only one s such that  $(\mathbf{x}_1(s), \mathbf{y}(i))$  are jointly  $\epsilon$ -typical. From Theorem 14.2.1, we know that  $s_i$  can be decoded with arbitrarily small probability of error if

$$
R_0 < I(X_1; Y) \tag{14.246}
$$

and  $n$  is sufficiently large.

3. Assuming that  $s_i$  is decoded correctly at the receiver, the receiver constructs a list  $\mathcal{L}(\mathbf{y}(i-1))$  of indices that the receiver considers to be jointly typical with  $y(i - 1)$  in the  $(i - 1)$ th block. The receiver then declares  $\hat{w}_{i-1} = w$  as the index sent in block  $i - 1$  if there is a unique w in  $S_{s_i} \cap \mathcal{L}(\mathbf{y}(i-1))$ . If n is sufficiently large and if

$$
R < I(X; Y|X_1) + R_0 \,, \tag{14.247}
$$

then  $\hat{w}_{i-1} = w_{i-1}$  with arbitrarily small probability of error. Combining the two constraints (14.246) and (14.247),  $R_0$  drops out, leaving

$$
R < I(X; Y|X_1) + I(X_1; Y) = I(X, X_1; Y) \,. \tag{14.248}
$$

For a detailed analysis of the probability of error, the reader is referred to Cover and El Gamal [67].  $\Box$ 

Theorem 14.7.2 can also shown to be the capacity for the following classes of relay channels.

(i) Reversely degraded relay channel, i.e.,

$$
p(y, y_1 | x, x_1) = p(y | x, x_1) p(y_1 | y, x_1).
$$
 (14.249)

- (ii) Relay channel with feedback.
- (iii) Deterministic relay channel,

$$
y_1 = f(x, x_1),
$$
  $y = g(x, x_1).$  (14.250)

## 14.8 SOURCE CODING WITH SIDE INFORMATION

We now consider the distributed source coding problem where two random variables  $X$  and  $Y$  are encoded separately but only  $X$  is to be recovered. We now ask how many bits  $R_1$  are required to describe X if we are allowed  $R_2$  bits to describe Y.

If  $R_2 > H(Y)$ , then Y can be described perfectly, and by the results of Slepian-Wolf coding,  $R_1 = H(X|Y)$  bits suffice to describe X. At the other extreme, if  $R_2 = 0$ , we must describe X without any help, and  $R_1 = H(X)$ bits are then necessary to describe X. In general, we will use  $R_2 =$  $I(Y; \hat{Y})$  bits to describe an approximate version of Y. This will allow us to describe X using  $H(X|Y)$  bits in the presence of side information Y. The following theorem is consistent with this intuition.

**Theorem 14.8.1:** Let  $(X, Y) \sim p(x, y)$ . If Y is encoded at rate  $R<sub>2</sub>$  and X is encoded at rate  $R_1$ , we can recover X with an arbitrarily small probability of error if and only if

$$
R_1 \ge H(X|U), \tag{14.251}
$$

$$
R_2 \ge I(Y; U) \tag{14.252}
$$

for some joint probability mass function  $p(x, y)p(u|y)$ , where  $|u| \le$  $|y|+2.$ 

We prove this theorem in two parts. We begin with the converse, in which we show that for any encoding scheme that has a small probability of error, we can find a random variable  $U$  with a joint probability mass function as in the theorem.

Proof (Converse): Consider any source code for Figure 14.31. The source code consists of mappings  $f_n(X^n)$  and  $g_n(Y^n)$  such that the rates of  $f_n$  and  $g_n$  are less than  $R_1$  and  $R_2$ , respectively, and a decoding mapping  $h_n$  such that

$$
P_e^{(n)} = \Pr\{h_n(f_n(X^n), g_n(Y^n)) \neq X^n\} < \epsilon \tag{14.253}
$$

Define new random variables  $S = f_n(X^n)$  and  $T = g_n(Y^n)$ . Then since we can recover  $X<sup>n</sup>$  from S and T with low probability of error, we have, by Fano's inequality,

$$
H(X^n|S,T) \le n\epsilon_n. \tag{14.254}
$$

Image /page/11/Figure/11 description: This is a block diagram illustrating a communication system. An input labeled 'X' goes into an 'Encoder' block, which outputs a signal labeled 'R1'. This 'R1' signal is fed into a 'Decoder' block. Separately, an input labeled 'Y' goes into another 'Encoder' block, which outputs a signal labeled 'R2'. The 'R2' signal is also fed into the 'Decoder' block. The 'Decoder' block outputs a signal labeled 'X-hat'.

Figure 14.31. Encoding with side information.

Then

$$
nR_2 \stackrel{(a)}{\geq} H(T) \tag{14.255}
$$

$$
\stackrel{(b)}{\geq} I(Y^n;T) \tag{14.256}
$$

$$
= \sum_{i=1}^{n} I(Y_i; T | Y_1, \dots, Y_{i-1})
$$
 (14.257)

$$
\stackrel{(c)}{=} \sum_{i=1}^{n} I(Y_i; T, Y_1, \dots, Y_{i-1})
$$
 (14.258)

$$
\stackrel{(d)}{=} \sum_{i=1}^{n} I(Y_i; U_i) \tag{14.259}
$$

where

- (a) follows from the fact that the range of  $g_n$  is  $\{1, 2, \ldots, 2^{nR_2}\},$
- (b) follows from the properties of mutual information,
- (c) follows from the chain rule and the fact that  $Y_i$  is independent of  $Y_1, \ldots, Y_{i-1}$  and hence  $I(Y_i; Y_1, \ldots, Y_{i-1}) = 0$ , and
- (d) follows if we define  $U_i = (T, Y_1, \ldots, Y_{i-1}).$

We also have another chain for  $R_1$ ,

$$
nR_1 \stackrel{(a)}{\geq} H(S) \tag{14.260}
$$

$$
\stackrel{\scriptscriptstyle(b)}{\geq} H(S|T) \tag{14.261}
$$

$$
= H(S|T) + H(X^n|S, T) - H(X^n|S, T) \qquad (14.262)
$$

$$
\stackrel{(c)}{\geq} H(X^n, S|T) - n\epsilon_n \tag{14.263}
$$

$$
\stackrel{(d)}{=} H(X^n|T) - n\epsilon_n \tag{14.264}
$$

$$
\stackrel{(e)}{=} \sum_{i=1}^{n} H(X_i | T, X_1, \dots, X_{i-1}) - n\epsilon_n \tag{14.265}
$$

$$
\stackrel{(f)}{\geq} \sum_{i=1}^{n} H(X_i | T, X^{i-1}, Y^{i-1}) - n\epsilon_n \tag{14.266}
$$

$$
\stackrel{(g)}{=} \sum_{i=1}^{n} H(X_i | T, Y^{i-1}) - n\epsilon_n \tag{14.267}
$$

$$
\stackrel{(h)}{=} \sum_{i=1}^{n} H(X_i | U_i) - n\epsilon_n \tag{14.268}
$$

where

- (a) follows from the fact that the range of S is  $\{1, 2, ..., 2^{nR_1}\}\,$
- (b) follows from the fact that conditioning reduces entropy,
- (c) from Fano's inequality,
- (d) from the chain rule and the fact that S is a function of  $X^n$ ,
- (e) from the chain rule for entropy,
- (f) from the fact that conditioning reduces entropy,
- (g) from the (subtle) fact that  $X_i\to(T, Y^*)\to X$  -forms a Markov chain since  $X_i$  does not contain any information about  $X_i$  -that is not there in  $Y$ <sup>------</sup> and  $T$ , and
- (h) follows from the definition of U.

Also, since  $X_i$  contains no more information about  $U_i$  than is present in  $Y_i$ , it follows that  $X_i \rightarrow Y_i \rightarrow U_i$  forms a Markov chain. Thus we have the following inequalities:

$$
R_1 \ge \frac{1}{n} \sum_{i=1}^{n} H(X_i | U_i)
$$
 (14.269)

$$
R_2 \ge \frac{1}{n} \sum_{i=1}^{n} I(Y_i; U_i).
$$
 (14.270)

We now introduce an timesharing random variable  $Q$ , so that we can rewrite these equations as

$$
R_1 \ge \frac{1}{n} \sum_{i=1}^{n} H(X_i | U_i, Q = i) = H(X_Q | U_Q, Q)
$$
 (14.271)

$$
R_2 \ge \frac{1}{n} \sum_{i=1}^{n} I(Y_i; U_i | Q = i) = I(Y_Q; U_Q | Q)
$$
 (14.272)

Now since Q is independent of  $Y_{\mathcal{Q}}$  (the distribution of  $Y_i$  does not depend on  $i$ ), we have

$$
I(Y_{\mathbf{Q}}; U_{\mathbf{Q}} | Q) = I(Y_{\mathbf{Q}}; U_{\mathbf{Q}}, Q) - I(Y_{\mathbf{Q}}; Q) = I(Y_{\mathbf{Q}}; U_{\mathbf{Q}}, Q). \quad (14.273)
$$

Now  $X_Q$  and  $Y_Q$  have the joint distribution  $p(x, y)$  in the theorem.

Defining  $U = (U_Q, Q), X = X_Q$ , and  $Y = Y_Q$ , we have shown the existence of a random variable  $U$  such that

$$
R_1 \ge H(X|U), \tag{14.274}
$$

$$
R_2 \ge I(Y; U) \tag{14.275}
$$

for any encoding scheme that has a low probability of error. Thus the converse is proved.  $\Box$ 

Before we proceed to the proof of the achievability of this pair of rates, we will need a new lemma about strong typicality and Markov chains. Recall the definition of strong typicality for a triple of random variables X, Y and Z. A triplet of sequences  $x^n$ ,  $y^n$ ,  $z^n$  is said to be  $\epsilon$ -strongly typical if

$$
\left|\frac{1}{n} N(a,b,c|x^n,y^n,z^n)-p(a,b,c)\right|<\frac{\epsilon}{|\mathscr{X}||\mathscr{Y}||\mathscr{Z}|}\;.\qquad(14.276)
$$

In particular, this implies that  $(x^n, y^n)$  are jointly strongly typical and that  $(y^n, z^n)$  are also jointly strongly typical. But the converse is not true: the fact that  $(x^n, y^n) \in A_*^{*(n)}(X, Y)$  and  $(y^n, z^n) \in A_*^{*(n)}(Y, Z)$  does not in general imply that  $(x^n, y^n, z^n) \in A_{\epsilon}^{*(n)}(X, Y, Z)$ . But if  $X \to Y \to Z$ forms a Markov chain, this implication is true. We state this as a lemma without proof [28,83].

**Lemma 14.8.1:** Let  $(X, Y, Z)$  form a Markov chain  $X \rightarrow Y \rightarrow Z$ , i.e.,  $p(x, y, z) = p(x, y)p(z|y)$ . If for a given  $(y^n, z^n) \in A^{*(n)}(Y, Z)$ ,  $X^n$  is drawn  $-\prod_{i=1}^n p(x_i|y_i)$ , then  $\Pr\{(X^n, y^n, z^n) \in A_*^{*(n)}(X, Y, Z)\} > 1 - \epsilon$  for n sufficiently large.

Remark: The theorem is true from the strong law of large numbers if  $X^n \sim \prod_{i=1}^n p(x_i | y_i, z_i)$ . The Markovity of  $X \to Y \to Z$  is used to show that  $X^n \sim p(x_i | y_i)$  is sufficient for the same conclusion.

We now outline the proof of achievability in Theorem 14.8.1.

**Proof** (Achievability in Theorem 14.8.1): Fix  $p(u|y)$ . Calculate  $p(u) = \sum_{y} p(y)p(u|y).$ 

Generation of codebooks. Generate  $2^{m}$  independent codewords of length n,  $U(w_2)$ ,  $w_2 \in \{1, 2, ..., 2^{n_2}\}\)$  according to  $\prod_{i=1}^n p(u_i)$ .

Randomly bin all the  $X^{\prime\prime}$  sequences into  $2^{n+1}$  bins by indepen dently generating an index *b* uniformly distributed on  $\{1,2,\ldots,2^{nR_1}\}\$  for each  $X^n$ . Let  $B(i)$  denote the set of  $X^n$  sequences allotted to bin i.

- Encoding. The X sender sends the index  $i$  of the bin in which  $X<sup>n</sup>$  falls. The Y sender looks for an index s such that  $(Y^n, U^n(s)) \in$  $A_{\epsilon}^{*(n)}(Y, U)$ . If there is more than one such s, it sends the least. If there is no such  $U^{n}(s)$  in the codebook, it sends  $s = 1$ .
- *Decoding.* The receiver looks for a unique  $X^n \in B(i)$  such that  $(X^n, U^n(s)) \in A^{\ast(n)}(X, U)$ . If there is none or more than one, it declares an error.
- Analysis of the probability of error. The various sources of error are as follows:
- 1. The pair  $(X^n, Y^n)$  generated by the source is not typical. The probability of this is small if  $n$  is large. Hence, without loss of generality, we can condition on the event that the source produces a particular typical sequence  $(x^n, y^n) \in A^{*(n)}_*$ .
- 2. The sequence  $Y^n$  is typical, but there does not exist a  $U^n(s)$  in the codebook which is jointly typical with it. The probability of this is small from the arguments of Section 13.6, where we showed that if there are enough codewords, i.e., if

$$
R_2 > I(Y; U), \t(14.277)
$$

then we are very likely to find a codeword that is jointly strongly typical with the given source sequence.

- 3. The codeword  $U^{n}(s)$  is jointly typical with  $y^{n}$  but not with  $x^{n}$ . But by Lemma 14.8.1, the probability of this is small since  $X \rightarrow Y \rightarrow U$ forms a Markov chain.
- 4. We also have an error if there exists another typical  $X^n \in B(i)$ which is jointly typical with  $U^n(s)$ . The probability that any other  $X^n$  is jointly typical with  $U^n(s)$  is less than  $2^{-n(I(U;X)-3\epsilon)}$  and therefore the probability of this kind of error is bounded above by

$$
|B(i) \cap A_{\epsilon}^{*(n)}(X)|2^{-n(I(X; U)-3\epsilon)} \le 2^{n(H(X)+\epsilon)} 2^{-nR_1} 2^{-n(I(X; U)-3\epsilon)},
$$
\n(14.278)

which goes to 0 if  $R_1 > H(X|U)$ .

Hence it is likely that the actual source sequence  $X<sup>n</sup>$  is jointly typical with  $U<sup>n</sup>(s)$  and that no other typical source sequence in the same bin is also jointly typical with  $U^{n}(s)$ . We can achieve an arbitrarily low probability of error with an appropriate choice of n and  $\epsilon$ , and this completes the proof of achievability.  $\square$ 

## 14.9 RATE DISTORTION WITH SIDE INFORMATION

We know that  $R(D)$  bits are sufficient to describe X within distortion D. We now ask how many bits are required given side information Y.

We will begin with a few definitions. Let  $(X_i, Y_i)$  be i.i.d.  $\sim p(x, y)$  and encoded as shown in Figure 14.32.

**Definition:** The rate distortion function with side information  $R_{\rm v}(D)$  is defined as the minimum rate required to achieve distortion  $D$  if the side information Y is available to the decoder. Precisely,  $R_{\rm v}(D)$  is the infimum of rates R such that there exist maps  $i_n: \mathcal{X}^n \to \{1, \ldots, 2^{nR}\},$  $g_n: \mathcal{Y}^n \times \{1, \ldots, 2^{nR}\} \rightarrow \hat{\mathcal{X}}^n$  such that

$$
\limsup_{n\to\infty} Ed(X^n, g_n(Y^n, i_n(X^n))) \le D\,. \tag{14.279}
$$

Clearly, since the side information can only help, we have  $R_{\rm y}(D) \le$  $R(D)$ . For the case of zero distortion, this is the Slepian-Wolf problem and we will need  $H(X|Y)$  bits. Hence  $R_Y(0) = H(X|Y)$ . We wish to determine the entire curve  $R_{\rm v}(D)$ . The result can be expressed in the following theorem:

**Theorem 14.9.1** (Rate distortion with side information): Let  $(X, Y)$  be drawn i.i.d.  $\sim p(x, y)$  and let  $d(x^n, \hat{x}^n) = \frac{1}{n} \sum_{i=1}^n d(x_i, \hat{x}_i)$  be given. The rate distortion function with side information is

$$
R_{Y}(D) = \min_{p(w|x)} \min_{f} (I(X;W) - I(Y;W))
$$
 (14.280)

where the minimization is over all functions  $f: \mathcal{Y} \times \mathcal{W} \rightarrow \hat{\mathcal{X}}$  and conditional probability mass functions  $p(w|x)$ ,  $|\mathcal{W}| \leq |\mathcal{X}| + 1$ , such that

$$
\sum_{x}\sum_{w}\sum_{y}p(x, y)p(w|x) d(x, f(y, w)) \le D.
$$
 (14.281)

The function  $f$  in the theorem corresponds to the decoding map that maps the encoded version of the  $X$  symbols and the side information  $Y$  to

Image /page/16/Figure/12 description: This is a block diagram illustrating a communication system. An input signal labeled 'X' enters an 'Encoder' block. The output of the encoder, labeled 'R1', goes into a 'Decoder' block. The decoder also receives an input labeled 'Y' through a separate line. The output of the decoder is labeled 'X-hat', representing the reconstructed signal.

Figure 14.32. Rate distortion with side information.

the output alphabet. We minimize over all conditional distributions on W and functions  $f$  such that the expected distortion for the joint distribution is less than D.

We first prove the converse after considering some of the properties of the function  $R_{\rm y}(D)$  defined in (14.280).

Lemma 14.9.1: The rate distortion function with side information  $R_{\rm v}(D)$  defined in (14.280) is a non-increasing convex function of D.

**Proof:** The monotonicity of  $R<sub>y</sub>(D)$  follows immediately from the fact that the domain of minimization in the definition of  $R_{\gamma}(D)$  increases with D.

As in the case of rate distortion without side information, we expect  $R_{\rm v}(D)$  to be convex. However, the proof of convexity is more involved because of the double rather than single minimization in the definition of  $R_v(D)$  in (14.280). We outline the proof here.

Let  $D_1$  and  $D_2$  be two values of the distortion and let  $W_1$ ,  $f_1$  and  $W_2$ ,  $f_2$ be the corresponding random variables and functions that achieve the minima in the definitions of  $R_Y(D_1)$  and  $R_Y(D_2)$ , respectively. Let Q be a random variable independent of X, Y, W<sub>1</sub> and W<sub>2</sub> which takes on the value 1 with probability  $\lambda$  and the value 2 with probability  $1 - \lambda$ .

Define  $W = (Q, W_Q)$  and let  $f(W, Y) = f_Q(W_Q, Y)$ . Specifically  $f(W, Y) =$  $f_1(W_1, Y)$  with probability  $\lambda$  and  $f(W, Y) = f_2(W_2, Y)$  with probability  $1 - \lambda$ . Then the distortion becomes

$$
D = Ed(X, \hat{X}) \tag{14.282}
$$

$$
= \lambda Ed(X, f_1(W_1, Y)) + (1 - \lambda)Ed(X, f_2(W_2, Y)) \tag{14.283}
$$

$$
= \lambda D_1 + (1 - \lambda)D_2, \qquad (14.284)
$$

and (14.280) becomes

$$
I(W; X) - I(W; Y) = H(X) - H(X|W) - H(Y) + H(Y|W)
$$
\n(14.285)

$$
= H(X) - H(X|W_Q, Q) - H(Y) + H(Y|W_Q, Q) \quad (14.286)
$$

$$
= H(X) - \lambda H(X|W_1) - (1 - \lambda)H(X|W_2)
$$
  
$$
- H(Y) + \lambda H(Y|W_1) + (1 - \lambda)H(Y|W_2)
$$
  
 $(14.287)$ 

$$
= \lambda (I(W_1, X) - I(W_1; Y))
$$
  
+  $(1 - \lambda) (I(W_2, X) - I(W_2; Y)),$ 
$$
(14.288)
$$

and hence

$$
R_Y(D) = \min_{U \,:\, E \, d \le D} (I(U;X) - I(U;Y))
$$
\n(14.289)

$$
\leq I(W; X) - I(W; Y) \tag{14.290}
$$

$$
= \lambda (I(W_1, X) - I(W_1; Y)) + (1 - \lambda)(I(W_2, X) - I(W_2; Y))
$$

$$
= \lambda R_{Y}(D_1) + (1 - \lambda)R_{Y}(D_2), \qquad (14.291)
$$

proving the convexity of  $R_{\rm v}(D)$ .  $\Box$ 

We are now in a position to prove the converse to the conditional rate distortion theorem.

Proof (Converse to Theorem 14.9.1): Consider any rate distortion code with side information. Let the encoding function be  $f_n: \mathcal{X}^n$ - $\{1,2,\ldots,2^{nR}\}\$ . Let the decoding function be  $g_n:\mathcal{Y}^n\times\{1,2,\ldots,2^{nR}\}\rightarrow$  $\mathscr{X}^n$  and let  $g_{ni}: \mathscr{Y}^n \times \{1,2,\ldots,2^{nR}\} \rightarrow \mathscr{X}$  denote the *i*th symbol produce by the decoding function. Let  $T = f_n(X^n)$  denote the encoded version of  $X^n$ . We must show that if  $Ed(X^n, \overset{\prime}{g}_n(Y^n, f_n(X^n))) \leq D$ , then  $R \geq R_Y(D)$ .<br>We have the following chain of inequalities:

We have the following chain of inequalities:

$$
nR \stackrel{(a)}{\geq} H(T) \tag{14.292}
$$

$$
\stackrel{(b)}{\geq} H(T|Y^n) \tag{14.293}
$$

$$
\geq I(X^n;T|Y^n) \tag{14.294}
$$

$$
\stackrel{(c)}{=} \sum_{i=1}^{n} I(X_i; T | Y^n, X^{i-1}) \tag{14.295}
$$

$$
= \sum_{i=1}^{n} H(X_i|Y^n, X^{i-1}) - H(X_i|T, Y^n, X^{i-1})
$$
\n(14.296)

$$
\stackrel{(d)}{=} \sum_{i=1}^{n} H(X_i|Y_i) - H(X_i|T, Y^{i-1}, Y_i, Y_{i+1}^n, X^{i-1}) \qquad (14.297)
$$

$$
\stackrel{\text{\tiny (e)}}{\geq} \sum_{i=1}^{n} H(X_i|Y_i) - H(X_i|T, Y^{i-1}, Y_i, Y_{i+1}^n) \tag{14.298}
$$

$$
\stackrel{(f)}{=} \sum_{i=1}^{n} H(X_i|Y_i) - H(X_i|W_i, Y_i)
$$
\n(14.299)

$$
\stackrel{(g)}{=} \sum_{i=1}^{n} I(X_i; W_i | Y_i)
$$
 (14.300)

$$
= \sum_{i=1}^{n} H(W_i|Y_i) - H(W_i|X_i, Y_i)
$$
\n(14.301)

$$
\stackrel{(h)}{=} \sum_{i=1}^{n} H(W_i|Y_i) - H(W_i|X_i) \tag{14.302}
$$

$$
= \sum_{i=1}^{n} H(W_i) - H(W_i|X_i) - H(W_i) + H(W_i|Y_i)
$$
 (14.303)

$$
= \sum_{i=1}^{n} I(W_i; X_i) - I(W_i; Y_i)
$$
 (14.304)

$$
\stackrel{(i)}{\geq} \sum_{i=1}^{n} R_{Y}(Ed(X_{i}, g'_{ni}(W_{i}, Y_{i}))) \tag{14.305}
$$

$$
= n \frac{1}{n} \sum_{i=1}^{n} R_{Y}(Ed(X_{i}, g'_{ni}(W_{i}, Y_{i}))) \qquad (14.306)
$$

$$
\stackrel{(j)}{\geq} n R_{Y} \left( E \; \frac{1}{n} \; \sum_{i=1}^{n} d(X_{i}, g'_{ni}(W_{i}, Y_{i})) \right) \tag{14.307}
$$

$$
\stackrel{\text{\tiny (k)}}{\geq} n R_{\gamma}(D),\tag{14.308}
$$

where

- (a) follows from the fact that the range of T is  $\{1, 2, \ldots, 2^{nR}\}\,$
- (b) from the fact that conditioning reduces entropy,
- (c) from the chain rule for mutual information,
- (d) from the fact that  $X_i$  is independent of the past and future  $Y$ 's and  $X$ 's given  $Y_i$ ,
- (e) from the fact that conditioning reduces entropy,
- (f) follows by defining  $W_i = (T, Y^{i-1}, Y_{i+1}^n)$ ,
- (g) follows from the defintion of mutual information,
- (h) follows from the fact that since  $Y_i$  depends only on  $X_i$  and is conditionally independent of  $T$  and the past and future  $Y$ 's, and therefore  $W_i \rightarrow X_i \rightarrow Y_i$  forms a Markov chain,
- (i) follows from the definition of the (information) conditional rate distortion function, since  $\hat{X}_i = g_{ni}(T, Y^n) \stackrel{\triangle}{=} g'_{ni}(W_i, Y_i)$ , and hence  $I(W_i; X_i) - I(W_i; Y_i) \ge \min_{W : Ed(X, \hat{X}) \le D_i} I(W; \overline{X}) - I(W; Y) = R_Y(D_i),$
- (j> follows from Jensen's inequality and the convexity of the conditional rate distortion function (Lemma 14.9.1), and
- (k) follows from the definition of  $D = E^{\frac{1}{n}} \sum_{i=1}^{n} d(X_i, \hat{X}_i)$ .  $\Box$

It is easy to see the parallels between this converse and the converse for rate distortion without side information (Section 13.4). The proof of achievability is also parallel to the proof of the rate distortion theorem using strong typicality. However, instead of sending the index of the codeword that is jointly typical with the source, we divide these codewords into bins and send the bin index instead. If the number of codewords in each bin is small enough, then the side information can be used to isolate the particular codeword in the bin at the receiver. Hence again we are combining random binning with rate distortion encoding to find a jointly typical reproduction codeword. We outline the details of the proof below.

**Proof** (Achievability of Theorem 14.9.1): Fix  $p(w|x)$  and the function  $f(w, y)$ . Calculate  $p(w) = \sum_{x} p(x)p(w|x)$ .

Generation of codebook. Let  $R_1 = I(X; W) + \epsilon$ . Generate  $2^{nR}$  i.i.d. codewords  $W^{n}(s) \sim \prod_{i=1}^{n} p(w_{i}),$  and index them by  $s \in$  $\{1, 2, \ldots, 2^{nR_1}\}.$ 

Let  $R_2 = I(X; W) - I(Y; W) + 5\epsilon$ . Randomly assign the indices  $s \in \{1, 2, \ldots, 2^{nR_1}\}\$  to one of  $2^{nR_2}\$  bins using a uniform distribution over the bins. Let  $B(i)$  denote the indices assigned to bin i. There are approximately  $2^{n(R_1-R_2)}$  indices in each bin.

- *Encoding.* Given a source sequence  $X<sup>n</sup>$ , the encoder looks for a codeword  $W^{n}(s)$  such that  $(X^{n}, W^{n}(s)) \in A_{\epsilon}^{*(n)}$ . If there is no such  $W^{n}$ , the encoder sets  $s = 1$ . If there is more than one such s, the encoder uses the lowest s. The encoder sends the index of the bin in which s belongs.
- Decoding. The decoder looks for a  $W^{n}(s)$  such that  $s \in B(i)$  and  $(W^{n}(s), Y^{n}) \in A_{\epsilon}^{*(n)}$ . If he finds a unique s, he then calculates  $\hat{X}^{n}$ , where  $\hat{X}_i = f(W_i, Y_i)$ . If he does not find any such s or more than one such s, he sets  $\hat{X}^n = \hat{x}^n$ , where  $\hat{x}^n$  is an arbitrary sequence in  $\hat{\mathcal{X}}^n$ . It does not matter which default sequence is used; we will show that the probability of this event is small.
- Analysis of the probability of error. As usual, we have various error events:
- 1. The pair  $(X^n, Y^n) \not\in A^{*(n)}_*$ . The probability of this event is small for large enough  $n$  by the weak law of large numbers.
- 2. The sequence  $X<sup>n</sup>$  is typical, but there does not exist an s such that  $(X^n, W^n(s)) \in A^{*(n)}$ . As in the proof of the rate distortion theorem, the probability of this event is small if

$$
R_1 > I(W; X). \t(14.309)
$$

3. The pair of sequences  $(X^n, W^n(s)) \in A^{*(n)}_*$  but  $(W^n(s), Y^n) \not\in A^{*(n)}_*$ 

i.e., the codeword is not jointly typical with the  $Y<sup>n</sup>$  sequence. By the Markov lemma (Lemma 14.&l), the probability of this event is small if  $n$  is large enough.

4. There exists another s' with the same bin index such that  $(W^{n}(s'), Y^{n}) \in A_{\epsilon}^{*(n)}$ . Since the probability that a randomly chosen  $W<sup>n</sup>$  is jointly typical with  $Y<sup>n</sup>$  is  $\approx 2^{-nI(Y;W)}$ , the probability that there is another  $W<sup>n</sup>$  in the same bin that is typical with  $Y<sup>n</sup>$  is bounded by the number of codewords in the bin times the probability of joint typicality, i.e.,

$$
\Pr(\exists s' \in B(i) : (W^n(s'), Y^n) \in A_{\epsilon}^{*(n)}) \le 2^{n(R_1 - R_2)} 2^{-n(I(W; Y) - 3\epsilon)},
$$
\n(14.310)

which goes to zero since  $R_1 - R_2 < I(Y; W) - 3\epsilon$ .

5. If the index s is decoded correctly, then  $(X^n, W^n(s)) \in A_*^{*(n)}$ . By item 1, we can assume that  $(X^n, Y^n) \in A_*^{*(n)}$ . Thus by the Markov lemma, we have  $(X^n, Y^n, W^n) \in A_*^{*(n)}$  and therefore the empirical joint distribution is close to the original distribution  $p(x, y)p(w|x)$  that we started with, and hence  $(X^n, \hat{X}^n)$  will have a joint distribution that is close to the distribution that achieves distortion D.

Hence with high probability, the decoder will produce  $\hat{X}^n$  such that the distortion between  $X^n$  and  $\hat{X}^n$  is close to nD. This completes the proof of the theorem.  $\Box$ 

The reader is referred to Wyner and Ziv [284] for the details of the proof.

After the discussion of the various situations of compressing distributed data, it might be expected that the problem is almost completely solved. But unfortunately this is not true. An immediate generalization

Image /page/21/Figure/9 description: This is a block diagram illustrating a communication system with two encoders and one decoder. The top path shows Encoder 1 receiving an input X^n and producing an output i(x^n) which is less than or equal to 2^nR1. The bottom path shows Encoder 2 receiving an input Y^n and producing an output j(y^n) which is less than or equal to 2^nR2. Both encoder outputs are fed into a single Decoder, which produces an output (X^n\_hat, Y^n\_hat).

Figure 14.33. Rate distortion for two correlated sources.

of all the above problems is the rate distortion problem for correlated sources, illustrated in Figure 14.33. This is essentially the Slepian-Wolf problem with distortion in both  $X$  and  $Y$ . It is easy to see that the three distributed source coding problems considered above are all special cases of this setup. Unlike the earlier problems, though, this problem has not yet been solved and the general rate distortion region remains unknown.

# 14.10 GENERAL MULTITERMINAL NETWORKS

We conclude this chapter by considering a general multiterminal network of senders and receivers and deriving some bounds on the rates achievable for communication in such a network.

A general multiterminal network is illustrated in Figure 14.34. In this section, superscripts denote node indices and subscripts denote time indices. There are  $m$  nodes, and node  $i$  has an associated transmitted variable  $X^{(i)}$  and a received variable  $Y^{(i)}$ . The node *i* sends information at rate  $R^{(ij)}$  to node j. We assume that all the messages  $W^{(ij)}$  being sent from node i to node j are independent and uniformly distributed over their respective ranges  $\{1, 2, \ldots, 2^{nR^{(ij)}}\}.$ 

The channel is represented by the channel transition function  $p(y^{(1)}, \ldots, y^{(m)}|x^{(1)}, \ldots, x^{(m)})$ , which is the conditional probability mass function of the outputs given the inputs. This probability transition function captures the effects of the noise and the interference in the network. The channel is assumed to be memoryless, i.e., the outputs at any time instant depend only the current inputs and are conditionally independent of the past inputs.

Image /page/22/Figure/6 description: The image displays a diagram illustrating a transition or movement across a boundary. On the left side, labeled 'S', there are three black dots, with one labeled '(X1, Y1)'. On the right side, labeled 'Sc', there are also three black dots, with one labeled '(Xm, Ym)'. A curved line separates the two regions, and a thick, curved arrow points from the 'S' region towards the 'Sc' region, indicating a direction of movement or transformation.

Figure 14.34. A general multiterminal network.

Corresponding to each transmitter-receiver node pair is a message  $W^{(ij)} \in \{1, 2, \ldots, 2^{nR^{(ij)}}\}$ . The input symbol  $X^{(i)}$  at node *i* depends on  $W^{(ij)}$ ,  $j \in \{1, \ldots, m\}$ , and also on the past values of the received symbol  $Y^{(i)}$  at node i. Hence an encoding scheme of block length n consists of a set of encoding and decoding functions, one for each node:

- Encoders.  $X_k^{(i)}(W^{(i)}, W^{(i)}, \ldots, W^{(im)}, Y_1^{(i)}, Y_2^{(i)}, \ldots, Y_{k-1}^{(i)}), \qquad k =$  $1, \ldots, n$ . The encoder maps the messages and past received symbols into the symbol  $X_k^{(i)}$  transmitted at time k.
- Decoders.  $W^{(1)}(Y_1^{(1)}, \ldots, Y_n^{(1)}, W^{(1)}, \ldots, W^{(1m)}), j = 1, 2, \ldots, m$ . The decoder  $j$  at node  $i$  maps the received symbols in each block and his own transmitted information to form estimates of the messages intended for him from node j,  $j = 1, 2, \ldots, m$ .

Associated with every pair of nodes is a rate and a corresponding probability of error that the message will not be decoded correctly,

$$
P_e^{(n)(ij)} = \Pr(\hat{W}^{(ij)}(\mathbf{Y}^{(j)}, W^{(j1)}, \dots, W^{(jm)}) \neq W^{(ij)}), \qquad (14.311)
$$

where  $P_e^{(n)(ij)}$  is defined under the assumption that all the messages are independent and uniformly distributed over their respective ranges.

A set of rates  $\{R^{(ij)}\}$  is said to be achievable if there exist encoders and decoders with block length n with  $P_e^{(n)(ij)} \rightarrow 0$  as  $n \rightarrow \infty$  for all  $i, j \in \{1, 2, \ldots, m\}.$ 

We use this formulation to derive an upper bound on the flow of information in any multiterminal network. We divide the nodes into two sets, S and the complement  $S<sup>c</sup>$ . We now bound the rate of flow of information from nodes in  $S$  to nodes in  $S<sup>c</sup>$ .

**Theorem 14.10.1:** If the information rates  $\{R^{(ij)}\}$  are achievable, then there exists some joint probability distribution  $p(x^{(1)}, x^{(2)}, \ldots, x^{(m)})$ , such that

$$
\sum_{i \in S, \, j \in S^c} R^{(ij)} \le I(X^{(S)}; Y^{(S^c)} | X^{(S^c)}) \,, \tag{14.312}
$$

for all  $S \subset \{1, 2, \ldots, m\}$ . Thus the total rate of flow of information across cut-sets is bounded by the conditional mutual information.

**Proof:** The proof follows the same lines as the proof of the converse for the multiple access channel. Let  $T = \{(i, j): i \in S, j \in S^c\}$  be the set of links that cross from S to S<sup>c</sup>, and let  $T<sup>c</sup>$  be all the other links in the network. Then

$$
= nI(X_Q^{(S)}; Y_Q^{(S^c)} | X_Q^{(S^c)}| + n\epsilon_n , \qquad (14.330)
$$

$$
\begin{aligned} (l) &= n(H(Y_Q^{(S^c)} | X_Q^{(S^c)}) - H(Y_Q^{(S^c)} | X_Q^{(S)}, X_Q^{(S^c)}) + n\epsilon_n \end{aligned} \tag{14.329}
$$

$$
\stackrel{(k)}{\leq} n(H(Y_Q^{(S^c)}|X_Q^{(S^c)}) - H(Y_Q^{(S^c)}|X_Q^{(S)}, X_Q^{(S^c)}, Q)) + n\epsilon_n \tag{14.328}
$$

$$
= n(H(Y_Q^{(S^c)}|X_Q^{(S^c)},Q) - H(Y_Q^{(S^c)}|X_Q^{(S)},X_Q^{(S^c)},Q)) + n\epsilon_n
$$
 (14.327)

$$
\frac{d^{(j)}}{dt^{(j)}} = nI(X_Q^{(S)}; Y_Q^{(S^c)} | X_Q^{(S^c)}, Q) + n\epsilon_n
$$
\n(14.326)

$$
\stackrel{(i)}{=} n \frac{1}{n} \sum_{k=1}^{n} I(X_{Q}^{(S)}; Y_{Q}^{(S^c)} | X_{Q}^{(S^c)}, Q = k) + n\epsilon_n
$$
\n(14.325)

$$
= \sum_{k=1}^{n} I(X_k^{(S)}; Y_k^{(S^c)} | X_k^{(S^c)}) + n\epsilon_n
$$
 (14.324)

$$
\stackrel{(h)}{\leq} \sum_{k=1}^{n} H(Y_k^{(S^c)} | X_k^{(S^c)}) - H(Y_k^{(S^c)} | X_k^{(S^c)}, X_k^{(S)}) + n\epsilon_n \tag{14.323}
$$

$$
(g) \leq \sum_{k=1}^{n} H(Y_k^{(S^c)} | Y_1^{(S^c)}, ", ", Y_{k-1}^{(S^c)}, W^{(T^c)}, X_k^{(S^c)}) - H(Y_k^{(S^c)} | Y_1^{(S^c)}, ", ", Y_{k-1}^{(S^c)}, W^{(T^c)}, W^{(T)}, X_k^{(S)}, X_k^{(S^c)}) + n\epsilon_n \quad (14.322)
$$

$$
(f) = \sum_{k=1}^{n} H(Y_k^{(S^c)} | Y_1^{(S^c)}, \dots, Y_{k-1}^{(S^c)}, W^{(T^c)}) - H(Y_k^{(S^c)} | Y_1^{(S^c)}, \dots, Y_{k-1}^{(S^c)}, W^{(T^c)}, W^{(T)}) + n\epsilon_n \quad (14.321)
$$

$$
\stackrel{\scriptscriptstyle{(e)}}{=} \sum_{k=1}^n I(W^{(T)}; Y_k^{(S^c)} | Y_1^{(S^c)}, \ldots, Y_{k-1}^{(S^c)}, W^{(T^c)}) + n\epsilon_n
$$
\n(14.320)

$$
\stackrel{(d)}{\leq} I(W^{(T)}; Y_1^{(S^c)}, \dots, Y_n^{(S^c)} | W^{(T^c)}) + n\epsilon_n \tag{14.319}
$$

$$
+ H(W^{(T)}|Y_1^{(S^c)},\ldots,Y_n^{(S^c)},W^{(T^c)})
$$
\n(14.318)

$$
= I(W^{(T)}; Y_1^{(S^c)}, \dots, Y_n^{(S^c)} | W^{(T^c)})
$$
\n(14.317)

$$
\stackrel{(c)}{=} H(W^{(T)}|W^{(T^c)})\tag{14.316}
$$

$$
\stackrel{(b)}{=} H(W^{(T)}) \tag{14.315}
$$

$$
^{(a)} = \sum_{i \in S, \, j \in S^c} H(W^{(ij)}) \tag{14.314}
$$

$$
n\sum_{i\in S,\,j\in S^c}R^{(ij)}\tag{14.313}
$$

where

- (a) follows from the fact that the messages  $W^{\nu}$  are uniform distributed over their respective ranges  $\{1,2,\ldots,2^{m^2-}\}$
- (b) follows from the definition of  $W^{(T)} = \{W^{(ij)} : i \in S, j \in S^c\}$  and the fact that the messages are independent,
- (c) follows from the independence of the messages for  $T$  and  $T^c$ ,
- (d) follows from Fano's inequality since the messages  $W^*$  can be decoded from  $Y^{\infty}$  and  $W^{\infty}$ ,
- (e) is the chain rule for mutual information,
- (f) follows from the definition of mutual information,
- (g) follows from the fact that  $X_k^{\infty}$ symbols  $Y^{\sim}$ is a function of the past receive and the messages  $W^{\infty}$  and the fact that adding conditioning reduces the second term,
- (h) from the fact that  $Y_k^{\omega}$  depends only on the current input symbol  $X_k^{\sim}{}'$  and  $X_k^{\sim}$
- (i) follows after we introduce a new timesharing random variable Q uniformly distributed on  $\{1, 2, \ldots, n\}$ ,
- (j) follows from the definition of mutual information,
- (k) follows from the fact that conditioning reduces entropy, and
- (1) follows from the fact that  $\Upsilon_Q^{\infty}$  depends only the inputs  $\chi_Q^{\infty}$  and  $X_Q^{\infty}$  and is conditionally independent of Q.

Thus there exist random variables  $X^{(S)}$  and  $X^{(S^c)}$  with some arbitrary joint distribution which satisfy the inequalities of the theorem.  $\Box$ 

The theorem has a simple max-flow-min-cut interpretation. The rate of flow of information across any boundary is less than the mutual information between the inputs on one side of the boundary and the outputs on the other side, conditioned on the inputs on the other side.

The problem of information flow in networks would be solved if the bounds of the theorem were achievable. But unfortunately these bounds are not achievable even for some simple channels. We now apply these bounds to a few of the channels that we have considered earlier.

 $\cdot$  Multiple access channel. The multiple access channel is a network with many input nodes and one output node. For the case of a two-user multiple access channel, the bounds of Theorem 14.10.1 reduce to

$$
R_1 \le I(X_1; Y | X_2), \tag{14.331}
$$

$$
R_2 \le I(X_2; Y|X_1), \tag{14.332}
$$

$$
R_1 + R_2 \le I(X_1, X_2; Y) \tag{14.333}
$$

Image /page/26/Figure/1 description: A diagram shows two points, X and Y, connected by a horizontal line. Two curved lines, labeled S1 and S2, are positioned vertically on either side of the horizontal line. An arrow originates from X, passes through S1, and points towards a label Y1:X1. Another arrow originates from Y, passes through S2, and also points towards the label Y1:X1. This suggests a representation of paths or influences between X and Y, possibly mediated by S1 and S2, converging at Y1:X1.

Figure 14.35. The relay channel.

for some joint distribution  $p(x_1, x_2)p(y|x_1, x_2)$ . These bounds coincide with the capacity region if we restrict the input distribution to be a product distribution and take the convex hull (Theorem 14.3.1).

 $\cdot$  Relay channel. For the relay channel, these bounds give the upper bound of Theorem 14.7.1 with different choices of subsets as shown in Figure 14.35. Thus

$$
C \leq \sup_{p(x, x_1)} \min\{I(X, X_1; Y), I(X; Y, Y_1|X_1)\}.
$$
 (14.334)

This upper bound is the capacity of a physically degraded relay channel, and for the relay channel with feedback [67].

To complement our discussion of a general network, we should mention two features of single user channels that do not apply to a multi-user network.

. The source channel separation theorem. In Section 8.13, we discussed the source channel separation theorem, which proves that we can transmit the source noiselessly over the channel if and only if the entropy rate is less than the channel capacity. This allows us  $t_{\rm{tot}}$  and characterize a single number  $t_{\rm{tot}}$  and  $t_{\rm{tot}}$  and  $t_{\rm{tot}}$  and  $t_{\rm{tot}}$  and  $t_{\rm{tot}}$  and  $t_{\rm{tot}}$ the channel but a source by a single number (the capacity). the channel by a single number (the capacity).<br>What about the multi-user case? We would expect that a distrib-

what about the multi-user case. We would expect that a ulstribution rate region for the noise coding of the source and source country in the source of the source lay with the source lay with the source lay with the source lay with the source lay with the source lay with the source lay with rate region for the noiseless coding of the source lay within the capacity region of the channel. To be specific, consider the transmission of a distributed source over a multiple access channel, as shown in Figure 14.36. Combining the results of Slepian-Wolf encoding with the capacity results for the multiple access channel. we can show that we can transmit the source over the channel and recover it with a low probability of error if

Image /page/27/Figure/1 description: A diagram shows two input variables, U and V, which are transformed into X1 and X2 respectively. These are then fed into a box labeled with the conditional probability p(y|x1, x2). The output of this box is Y, which then leads to estimated variables (U-hat, V-hat).

Figure 14.36. Transmission of correlated sources over a multiple access channel.

$$
H(V|U) \le I(X_2; Y|X_1, Q), \tag{14.336}
$$

$$
H(U, V) \le I(X_1, X_2; Y, Q) \tag{14.337}
$$

for some distribution  $p(q)p(x_1|q)p(x_2|q)p(y|x_1, x_2)$ . This condition is equivalent to saying that the Slepian-Wolf rate region of the source has a non-empty intersection with the capacity region of the multiple access channel.

But is this condition also necessary? No, as a simple example illustrates. Consider the transmission of the source of Example 14.4 over the binary erasure multiple access channel (Example 14.3). The Slepian-Wolf region does not intersect the capacity region, yet it is simple to devise a scheme that allows the source to be transmitted over the channel. We just let  $X_1 = U$ , and  $X_2 = V$ , and the value of Y will tell us the pair  $(U, V)$  with no error. Thus the conditions (14.337) are not necessary.

The reason for the failure of the source channel separation theorem lies in the fact that the capacity of the multiple access channel increases with the correlation between the inputs of the channel. Therefore, to maximize the capacity, one should preserve the correlation between the inputs of the channel. Slepian-Wolf encoding, on the other hand, gets rid of the correlation. Cover, El Gamal and Salehi [69] proposed an achievable region for transmission of a correlated source over a multiple access channel based on the idea of preserving the correlation. Han and Costa [131] have proposed a similar region for the transmission of a correlated source over a broadcast channel.

Capacity regions with feedback. Theorem 8.12.1 shows that feedback does not increase the capacity of a single user discrete memoryless channel. For channels with memory, on the other hand, feedback enables the sender to predict something about the noise and to combat it more effectively, thus increasing capacity.

What about multi-user channels? Rather surprisingly, feedback does increase the capacity region of multi-user channels, even when the channels are memoryless. This was first shown by Gaarder and Wolf [117], who showed how feedback helps increase the capacity of the binary erasure multiple access channel. In essence, feedback from the receiver to the two senders acts as a separate channel between the two senders. The senders can decode each other's transmissions before the receiver does. They then cooperate to resolve the uncertainty at the receiver, sending information at the higher cooperative capacity rather than the noncooperative capacity. Using this scheme, Cover and Leung [73] established an achievable region for multiple access channel with feedback. Willems [273] showed that this region was the capacity for a class of multiple access channels that included the binary erasure multiple access channel. Ozarow [204] established the capacity region for the two user Gaussian multiple access channel. The problem of finding the capacity region for the multiple access channel with feedback is closely related to the capacity of a two-way channel with a common output.

There is as yet no unified theory of network information flow. But there can be no doubt that a complete theory of communication networks would have wide implications for the theory of communication and computation.

## SUMMARY OF CHAPTER 14

**Multiple access channel:** The capacity of a multiple access channel  $(\mathscr{X}_1 \times \mathscr{X}_2, \mathscr{Y}_2)$  $p(y|x_1, x_2), \mathcal{Y})$  is the closure of the convex hull of all  $(R_1, R_2)$  satisfying

$$
R_1 < I(X_1; Y | X_2), \tag{14.338}
$$

$$
R_2 < I(X_2; Y|X_1), \tag{14.339}
$$

$$
R_1 + R_2 < I(X_1, X_2; Y) \tag{14.340}
$$

for some distribution  $p_1(x_1)p_2(x_2)$  on  $\mathscr{X}_1 \times \mathscr{X}_2$ .

The capacity region of the *m*-user multiple access channel is the closure of the convex hull of the rate vectors satisfying

$$
R(S) \le I(X(S); Y|X(Sc)) \quad \text{for all } S \subseteq \{1, 2, ..., m\} \tag{14.341}
$$

for some product distribution  $p_1(x_1)p_2(x_2) \ldots p_m(x_m)$ .

Gaussian multiple access channel: The capacity region of a two user Gaussian multiple access channel is

$$
R_1 \le C\left(\frac{P_1}{N}\right),\tag{14.342}
$$

$$
R_2 \le C\left(\frac{P_2}{N}\right),\tag{14.343}
$$

$$
R_1 + R_2 \le C\left(\frac{P_1 + P_2}{N}\right),\tag{14.344}
$$

where

$$
C(x) = \frac{1}{2} \log(1 + x) \,. \tag{14.345}
$$

Slepian-Wolf coding: Correlated sources  $X$  and  $Y$  can be separately described at rates  $R_1$  and  $R_2$  and recovered with arbitrarily low probability of error by a common decoder if and only if

$$
R_1 > H(X|Y), \t(14.346)
$$

$$
R_2 > H(Y|X), \t(14.347)
$$

$$
R_1 + R_2 > H(X, Y). \tag{14.348}
$$

Broadcast channels: The capacity region of the degraded broadcast channel  $X \rightarrow Y_1 \rightarrow Y_2$  is the convex hull of the closure of all  $(R_1, R_2)$  satisfying

$$
R_2 \le I(U; Y_2), \tag{14.349}
$$

$$
R_1 \le I(X; Y_1 | U) \tag{14.350}
$$

for some joint distribution  $p(u)p(x|u)p(y_1, y_2|x)$ .

**Relay channel:** The capacity  $C$  of the physically degraded relay channel  $p(y, y, |x, x)$  is given by

$$
C = \sup_{p(x, x_1)} \min\{I(X, X_1; Y), I(X; Y_1|X_1)\},
$$
 (14.351)

where the supremum is over all joint distributions on  $\mathscr{X} \times \mathscr{X}$ .

 $\mathbf{S}$  source coding with a information: Let  $\mathbf{S}$  is encoded information: Let  $\mathbf{S}$ Source coding with side information: Let  $(X, Y) \sim p(x, y)$ . If Y is encoderat rate  $R_2$  and X is encoded at rate  $R_1$ , we can recover X with an arbitrarily small probability of error iff

$$
R_1 \ge H(X|U),\tag{14.352}
$$

$$
R_2 \ge I(Y; U) \tag{14.353}
$$

for some distribution  $p(y, u)$ , such that  $X \rightarrow Y \rightarrow U$ .

Rate distortion with side information: Let  $(X, Y) \sim p(x, y)$ . The rate distortion function with side information is given by

$$
R_{Y}(D) = \min_{p(w|x)f: \Psi \times W \to \hat{\mathscr{U}}} I(X;W) - I(Y;W), \qquad (14.354)
$$

where the minimization is over all functions  $f$  and conditional distributions  $p(w|x), |W| \leq |\mathcal{X}| + 1$ , such that

$$
\sum_{x} \sum_{w} \sum_{y} p(x, y) p(w|x) d(x, f(y, w)) \le D.
$$
 (14.355)

## PROBLEMS FOR CHAPTER 14

1. The cooperative capacity of a multiple access channel. (See Figure 14.37.)

Image /page/30/Figure/9 description: A block diagram shows a process where input variables (W1, W2) are split into two separate variables, X1 and X2. Both X1 and X2 are fed into a central box labeled p(y|x1, x2). An output variable Y emerges from this box and is then processed to produce estimated variables (W1-hat, W2-hat).

Figure 14.37. Multiple access channel with cooperating senders.

- (a) Suppose  $X_1$  and  $X_2$  have access to *both* indices  $W_1 \in \{1, 2^{nR}\}, W_2 \in$  ${1, 2^{nR_2}}$ . Thus the codewords  $\mathbf{X}_1(W_1, W_2), \mathbf{X}_2(W_1, W_2)$  depend on both indices. Find the capacity region.
- (b) Evaluate this region for the binary erasure multiple access channel  $Y = X_1 + X_2, X_i \in \{0, 1\}$ . Compare to the non-cooperative region.
- 2. Capacity of multiple access channels. Find the capacity region for each of the following multiple access channels:
  - (a) Additive modulo 2 multiple access access channel.  $X_1 \in \{0, 1\}$ ,  $X_2 \in \{0,1\}, Y=X_1 \oplus X_2.$
  - (b) Multiplicative multiple access channel.  $X_1 \in \{-1, 1\}$ ,  $X_2 \in \{-1, 1\}$ ,  $Y=X_1 \cdot X_2$ .
- 3. Cut-set interpretation of capacity region of multiple access channel. For the multiple access channel we know that  $(R_1, R_2)$  is achievable if

$$
R_1 < I(X_1; Y | X_2), \tag{14.356}
$$

$$
R_2 < I(X_2; Y|X_1), \tag{14.357}
$$

$$
R_1 + R_2 < I(X_1, X_2; Y), \tag{14.358}
$$

for  $X_1, X_2$  independent. Show, for  $X_1, X_2$  independent, that

Image /page/31/Figure/3 description: The image displays a diagram illustrating a concept in information theory. At the top, an equation is presented: I(X1; Y|X2) = I(X1; Y, X2). Below the equation, a diagram shows two input variables, X1 and X2, which are represented by lines. These lines intersect with two curved boundaries labeled S1 and S2. Both S1 and S2 then converge towards a single output variable, Y, which is depicted as a point or region on the right side of the diagram. Another curved boundary, S3, is also shown, originating from the intersection of X1 and S1, and also converging towards Y.

Interpret the information bounds as bounds on the rate of flow across cutsets  $S_1$ ,  $S_2$  and  $S_3$ .

4. Gaussian multiple access channel capacity. For the AWGN multiple access channel, prove, using typical sequences, the achievability of any rate pairs  $(R_1, R_2)$  satisfying

$$
R_1 < \frac{1}{2} \log \left( 1 + \frac{P_1}{N} \right),\tag{14.359}
$$

$$
R_2 < \frac{1}{2} \log \left( 1 + \frac{P_2}{N} \right),\tag{14.360}
$$

$$
R_1 + R_2 < \frac{1}{2} \log \left( 1 + \frac{P_1 + P_2}{N} \right). \tag{14.361}
$$

The proof extends the proof for the discrete multiple access channel in the same way as the proof for the single user Gaussian channel extends the proof for the discrete single user channel.

- 5. Converse for the Gaussian multiple uccess channel. Prove the converse for the Gaussian multiple access channel by extending the converse in the discrete case to take into account the power constraint on the codewords.
- 6. Unusual multiple uccess channel. Consider the following multiple access channel:  $\mathscr{X}_1 = \mathscr{X}_2 = \mathscr{Y} = \{0, 1\}$ . If  $(X_1, X_2) = (0, 0)$ , then  $Y = 0$ . If  $(X_1, X_2) = (0, 1)$ , then  $Y = 1$ . If  $(X_1, X_2) = (1, 0)$ , then  $Y = 1$ . If  $(X_1, X_2) = (1, 1)$ , then  $Y = 0$  with probability  $\frac{1}{2}$  and  $Y = 1$  with probability  $\frac{1}{2}$ .
  - (a) Show that the rate pairs  $(1,0)$  and  $(0,1)$  are achievable.
  - (b) Show that for any non-degenerate distribution  $p(x_1)p(x_2)$ , we have  $I(X_1, X_2; Y)$  < 1.
  - (c) Argue that there are points in the capacity region of this multiple access channel that can only be achieved by timesharing, i.e., there exist achievable rate pairs  $(R_1, R_2)$  which lie in the capacity region for the channel but not in the region defined by

$$
R_1 \le I(X_1; Y | X_2), \tag{14.362}
$$

$$
R_2 \le I(X_2; Y|X_1), \tag{14.363}
$$

$$
R_1 + R_2 \le I(X_1, X_2; Y) \tag{14.364}
$$

for any product distribution  $p(x_i)p(x_i)$ . Hence the operation of convexification strictly enlarges the capacity region. This channel was introduced independently by Csiszar and Körner [83] and Bierbaum and Wallmeier [33].

7. Convexity of capacity region of broadcast channel. Let  $C \subseteq \mathbb{R}^2$  be the capacity region of all achievable rate pairs  $\mathbf{R} = (R_1, R_2)$  for the broadcast channel. Show that  $C$  is a convex set by using a timesharing argument.

Specifically, show that if  $\mathbf{R}^{(1)}$  and  $\mathbf{R}^{(2)}$  are achievable, then  $\lambda \mathbf{R}^{(1)}$  +  $(1 - \lambda) \mathbf{R}^{(2)}$  is achievable for  $0 \le \lambda \le 1$ .

- 8. Slepian-Wolf for deterministically related sources. Find and sketch the Slepian-Wolf rate region for the simultaneous data compression of  $(X, Y)$ , where  $y = f(x)$  is some deterministic function of x.
- 9. Slepian-Wolf. Let  $X_i$  be i.i.d. Bernoulli(p). Let  $Z_i$  be i.i.d.  $\sim$  Bernoulli(*r*), and let **Z** be independent of **X**. Finally, let **Y** = **X** $\oplus$ **Z** (mod 2 addition). Let **X** be described at rate  $R_1$  and **Y** be described at rate  $R_2$ . What region of rates allows recovery of  $X, Y$  with probability of error tending to zero?
- 10. Broadcast capacity depends only on the conditional marginals. Consider the general broadcast channel  $(X, Y_1 \times Y_2, p(y_1, y_2|x))$ . Show that the capacity region depends only on  $p(y_1|x)$  and  $p(y_2|x)$ . To do this, for any given  $((2^{nR_1}, 2^{nR_2}), n)$  code, let

$$
P_1^{(n)} = P\{\tilde{W}_1(\mathbf{Y}_1) \neq W_1\},\tag{14.365}
$$

$$
P_2^{(n)} = P\{\hat{W}_2(\mathbf{Y}_2) \neq W_2\},\qquad(14.366)
$$

$$
P^{(n)} = P\{(\hat{W}_1, \hat{W}_2) \neq (W_1, W_2)\}.
$$
 (14.367)

Then show

$$
\max\{P_1^{(n)}, P_2^{(n)}\}\leq P^{(n)}\leq P_1^{(n)}+P_2^{(n)}.
$$

The result now follows by a simple argument.

Remark: The probability of error  $P^{(n)}$  does depend on the conditional joint distribution  $p(y_1, y_2|x)$ . But whether or not  $P^{(n)}$  can be driven to zero (at rates  $(R_1, R_2)$ ) does not (except through the conditional marginals  $p(y_1|x), p(y_2|x)$ .

11. Converse for the degraded broadcast channel. The following chain of inequalities proves the converse for the degraded discrete memoryless broadcast channel. Provide reasons for each of the labeled inequalities.

Setup for converse for degraded broadcast channel capacity

$$
(W_1, W_2)_{\text{indep.}} \to X^n(W_1, W_2) \to Y^n \to Z^n
$$

Encoding

$$
f_n: 2^{nR_1} \times 2^{nR_2} \to \mathcal{X}^n
$$

Decoding

$$
g_n: \mathscr{Y}^n \to 2^{nR_1}, \qquad h_n: \mathscr{Z}^n \to 2^{nR_2}
$$

Let  $U_i = (W_2, Y^{i-1})$ . Then

$$
nR_2 \stackrel{\scriptscriptstyle \perp}{=} \, F_{ano} \, I(W_2; Z^n) \tag{14.368}
$$

$$
\stackrel{(a)}{=} \sum_{i=1}^{n} I(W_2; Z_i | Z^{i-1})
$$
\n(14.369)

$$
\stackrel{\text{(b)}}{=} \sum_{i} \left( H(Z_i | Z^{i-1}) - H(Z_i | W_2, Z^{i-1}) \right) \tag{14.370}
$$

$$
\stackrel{\text{(c)}}{\leq} \sum_{i} \left( H(Z_i) - H(Z_i | W_2, Z^{i-1}, Y^{i-1}) \right) \tag{14.371}
$$

$$
\stackrel{(d)}{\leq} \sum_{i} \left( H(Z_i) - H(Z_i \mid W_2, Y^{i-1}) \right) \tag{14.372}
$$

$$
\stackrel{(e)}{=} \sum_{i=1}^{n} I(U_i; Z_i).
$$
 (14.373)

Continuation of converse. Give reasons for the labeled inequalities:

$$
nR_1 \leq_{Fano} I(W_1; Y^n) \tag{14.374}
$$

$$
\stackrel{(f)}{\leq} I(W_1;Y^n,W_2) \tag{14.375}
$$

$$
\stackrel{(g)}{\leq} I(W_1; Y^n \, |W_2) \tag{14.376}
$$

$$
\stackrel{(h)}{=} \sum_{i=1}^{n} I(W_1; Y_i | Y^{i-1}, W_2)
$$
 (14.377)

$$
\stackrel{\text{\tiny (i)}}{\leq} \sum_{i=1}^{n} I(X_i; Y_i | U_i).
$$
\n(14.378)

- 12. Capacity points.
  - (a) For the degraded broadcast channel  $X \rightarrow Y_1 \rightarrow Y_2$ , find the points a and b where the capacity region hits the  $R_1$  and  $R_2$  axes (Figure 14.38).
  - (b) Show that  $b \le a$ .

Image /page/34/Figure/1 description: The image shows a graph with R1 on the x-axis and R2 on the y-axis. The graph starts at the point (0, b) and curves downwards and to the right, intersecting the x-axis at the point (a, 0). The curve is concave down. The x-axis is labeled R1 and has an arrow indicating the positive direction. The y-axis is labeled R2 and has an arrow indicating the positive direction. The point b is marked on the y-axis, and the point a is marked on the x-axis.

Figure 14.38. Capacity region of a broadcast channel.

13. Degraded broadcast channel. Find the capacity region for the degraded broadcast channel in Figure 14.39.

Image /page/34/Figure/4 description: This is a diagram illustrating a communication channel. The input is labeled X, and the output is labeled Y2. There is an intermediate stage labeled Y1. The diagram shows two possible paths from X to Y1, each with a probability of 1-p or p. From Y1 to Y2, there are also two paths, each with a probability of 1-alpha or alpha. Specifically, if the input X is on the top path, it goes to Y1 with probability 1-p. If the input X is on the bottom path, it goes to Y1 with probability 1-p. The crossed paths from X to Y1 have probability p. From Y1, the top path goes to Y2 with probability 1-alpha, and the bottom path goes to Y2 with probability alpha. Similarly, from Y1, the bottom path goes to Y2 with probability 1-alpha, and the top path goes to Y2 with probability alpha.

Figure 14.39. Broadcast channel-BSC and erasure channel.

14. Channels with unknown parameters. We are given a binary symmetric channel with parameter p. The capacity is  $C = 1 - H(p)$ .

Now we change the problem slightly. The receiver knows only that  $p \in \{p_1, p_2\}$ , i.e.,  $p = p_1$  or  $p = p_2$ , where  $p_1$  and  $p_2$  are given real numbers. The transmitter knows the actual value of p. Devise two codes for use by the transmitter, one to be used if  $p = p_1$ , the other to be used if  $p = p_2$ , such that transmission to the receiver can take place at rate  $\approx C(p_1)$  if  $p = p_1$  and at rate  $\approx C(p_2)$  if  $p = p_2$ .

*Hint*: Devise a method for revealing  $p$  to the receiver without affecting the asymptotic rate. Prefixing the codeword by a sequence of l's of appropriate length should work.

- 15. Two-way channel. Consider the two-way channel shown in Figure 14.6. The outputs  $Y_1$  and  $Y_2$  depend only on the current inputs  $X_1$  and  $X_{\alpha}$ 
  - (a) By using independently generated codes for the two senders, show that the following rate region is achievable:

$$
R_1 < I(X_1; Y_2 | X_2), \tag{14.379}
$$

$$
R_2 < I(X_2; Y_1 | X_1) \tag{14.380}
$$

for some product distribution  $p(x_1)p(x_2)p(y_1, y_2|x_1, x_2)$ .

(b) Show that the rates for any code for a two-way channel with arbitrarily small probability of error must satisfy

$$
R_1 \le I(X_1; Y_2 | X_2), \tag{14.381}
$$

$$
R_2 \le I(X_2; Y_1 | X_1) \tag{14.382}
$$

for some joint distribution  $p(x_1, x_2)p(y_1, y_2|x_1, x_2)$ .

The inner and outer bounds on the capacity of the two-way channel are due to Shannon [246]. He also showed that the inner bound and the outer bound do not coincide in the case of the binary multiplying channel  $\mathcal{X}_1 = \mathcal{X}_2 = \mathcal{Y}_1 = \mathcal{Y}_2 = \{0, 1\}, Y_1 = Y_2 = X_1X_2$ . The capacity of the two-way channel is still an open problem.

# HISTORICAL NOTES

This chapter is based on the review in El Gamal and Cover [98]. The two-way channel was studied by Shannon [246] in 1961. He derived inner and outer bounds on the capacity region. Dueck [90] and Schalkwijk [232,233] suggested coding schemes for two-way channels which achieve rates exceeding Shannon's inner bound; outer bounds for this channel were derived by Zhang, Berger and Schalkwijk [287] and Willems and Hekstra [274].

The multiple access channel capacity region was found by Ahlswede [3] and Liao [178] and was extended to the case of the multiple access channel with common information by Slepian and Wolf [254]. Gaarder and Wolf [117] were the first to show that feedback increases the capacity of a discrete memoryless multiple access channel. Cover and Leung [73] proposed an achievable region for the multiple access channel with feedback, which was shown to be optimal for a class of multiple access channels by Willems [273]. Ozarow [204] has determined the capacity region for a two user Gaussian multiple access channel with feedback. Cover, El Gamal and Salehi [69] and Ahlswede and Han [6] have considered the problem of transmission of a correlated source over a multiple access channel.

The Slepian-Wolf theorem was proved by Slepian and Wolf [255], and was extended to jointly ergodic sources by a binning argument in Cover [63].

Broadcast channels were studied by Cover in 1972 [60]; the capacity region for the degraded broadcast channel was determined by Bergmans [31] and Gallager [119]. The superposition codes used for the degraded broadcast channel are also optimal for the less noisy broadcast channel (Körner and Marton [160]) and the more capable broadcast channel (El Gamal [97]) and the broadcast channel with degraded message sets (Körner and Marton [161]). Van der Meulen [26] and Cover [62] proposed achievable regions for the general broadcast channel. The best known achievable region for broadcast channel is due to Marton [189]; a simpler proof of Marton's region was given by El Gamal and Van der Meulen [loo]. The deterministic broadcast channel capacity was determined by Pinsker [211] and Marton [189]. El Gamal [96] showed that feedback does not increase the capacity of a physically degraded broadcast channel. Dueck [91] introduced an example to illustrate that feedback could increase the capacity of a memoryless

broadcast channel; Ozarow and Leung [205] described a coding procedure for the Gaussian broadcast channel with feedback which increased the capacity region.

The relay channel was introduced by Van der Meulen [262]; the capacity region for the degraded relay channel was found by Cover and El Gamal[67]. The interference channel was introduced by Shannon [246]. It was studied by Ahlswede [4], who gave an example to show that the region conjectured by Shannon was not the capacity region of the interference channel. Carleial [49] introduced the Gaussian interference channel with power constraints, and showed that very strong interference is equivalent to no interference at all. Sato and Tanabe [231] extended the work of Carleial to discrete interference channels with strong interference. Sato [229] and Benzel [26] dealt with degraded interference channels. The best known achievable region for the general interference channel is due to Han and Kobayashi [132]. This region gives the capacity for Gaussian interference channels with interference parameters greater than 1, as was shown in Han and Kobayashi [132] and Sato [230]. Carleial [48] proved new bounds on the capacity region for interference channels.

The problem of coding with side information was introduced by Wyner and Ziv [283] and Wyner [280]; the achievable region for this problem was described in Ahlswede and Körner [7] and in a series of papers by Gray and Wyner [125] and Wyner [281,282]. The problem of finding the rate distortion function with side information was solved by Wyner and Ziv [284]. The problem of multiple descriptions is treated in El Gamal and Cover [99].

The special problem of encoding a function of two random variables was discussed by Körner and Marton [162], who described a simple method to encode the modulo two sum of two binary random variables. A general framework for the description of source networks can be found in Csiszár and Körner [82], [83]. A common model which includes Slepian-Wolf encoding, coding with side information, and rate distortion with side information as special cases was described by Berger and Yeung [3O].

Comprehensive surveys of network information theory can be found in El Gamal and Cover [98], Van der Meulen [262,263,264], Berger [28] and Csiszar and Körner [83].
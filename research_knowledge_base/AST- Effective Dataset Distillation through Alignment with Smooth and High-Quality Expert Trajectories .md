# AST: Effective Dataset Distillation through Alignment with S<PERSON>oth and High-Quality Expert Trajectories

<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> Nanyang Technological University, Singapore

<EMAIL>, {we<PERSON>hu<PERSON>.yang, kwokyan.lam}@ntu.edu.sg

*Abstract*—Training large AI models typically requires largescale datasets in the machine learning process, making training and parameter-tuning process both time-consuming and costly. Some researchers address this problem by carefully synthesizing a very small number of highly representative and informative samples from real-world datasets. This approach, known as Dataset Distillation (DD), proposes a perspective for data-efficient learning. Despite recent progress in this field, the performance of existing methods still cannot meet expectations, and distilled datasets cannot effectively replace original datasets. In this paper, unlike previous methods that focus solely on improving the effectiveness of student distillation, we recognize and leverage the important mutual influence between expert and student models. We observed that the smoothness of expert trajectories has a significant impact on subsequent student parameter alignment. Based on this, we propose an effective DD framework named AST, standing for *A*lignment with *S*mooth and high-quality expert *T*rajectories. We devise the integration of clipping loss and gradient penalty to regulate the rate of parameter changes in expert trajectory generation. To further refine the student parameter alignment with expert trajectory, we put forward representative initialization for the synthetic dataset and balanced inner-loop loss in response to the sensitivity exhibited towards randomly initialized variables during distillation. We also propose two enhancement strategies, namely intermediate matching loss and weight perturbation, to mitigate the potential occurrence of cumulative errors. We conduct extensive experiments on datasets of different scales, sizes, and resolutions. The results demonstrate that the proposed method significantly outperforms prior methods.

*Index Terms*—Dataset Distillation, Information Integration, Knowledge Discovery and Data Synthesis

#### I. INTRODUCTION

<span id="page-0-1"></span>Nowadays, large machine learning models have demonstrated unprecedented results in many fields. Behind the success of large models, large-scale datasets become an indispensable driving force. Despite the great potential of such approaches, we still wonder about the underlying principles and limitations. Do datasets have to be big? Can the information from a large dataset be distilled and integrated? Or, can training on "small data" be equally successful?

As a result, some researchers explore data-efficient methods, including coreset selection [\[1\]](#page-11-0)–[\[3\]](#page-12-0), dataset pruning [\[4\]](#page-12-1)–[\[6\]](#page-12-2), and instance selection [\[7\]](#page-12-3). They summarize the entire dataset by only selecting the "valuable" data for model training. Yet, these methods rely on greedy algorithms guided by heuristics [\[1\]](#page-11-0), [\[2\]](#page-12-4), [\[8\]](#page-12-5), [\[9\]](#page-12-6), resulting in significant performance drops, and the selection process is not a way of unearthing and integrating high-level information.

<span id="page-0-0"></span>Image /page/0/Figure/9 description: The image illustrates the concept of dataset distillation in machine learning. On the left, a large grid of 'Full training images' is shown. An arrow labeled 'Dataset Distillation' points from this grid to a smaller grid of 10 images, labeled 'Distilled dataset (size = 10)'. These distilled images appear to be abstract representations. To the right, two parallel processes are depicted. The top process shows the distilled dataset being used to 'Train' a neural network, followed by a 'Backward' arrow indicating a reverse process. The bottom process shows the full training images being used to 'Train' another neural network. Both neural networks are represented by simplified diagrams with input, hidden, and output layers. A curved arrow connects the outputs of the two neural networks, with the text 'Similar Test Performance' indicating that both methods achieve comparable results.

Fig. 1: Dataset distillation aims to generate a compact synthetic dataset that allows a model trained on it to achieve a comparable test performance to a model trained on the entire real training set.

Recently, a new data-efficient method called Dataset Distillation (DD) has emerged as a competitive alternative with promising results [\[10\]](#page-12-7)–[\[13\]](#page-12-8). DD uses a learning method to distill a large training set (expert) into a small synthetic set (student), upon which machine learning models are trained from scratch, and similar testing performance on the validation set is expected to be preserved, as shown in Fig. [1.](#page-0-0) DD has aroused significant interest from the machine learning community with various downstream applications, such as federated learning [\[14\]](#page-12-9)–[\[16\]](#page-12-10), neural architecture search [\[17\]](#page-12-11), [\[18\]](#page-12-12), and privacy-preserving tasks [\[19\]](#page-12-13)–[\[21\]](#page-12-14), and etc.

As one of the state-of-the-art frameworks for dataset distillation, Matching Training Trajectories (MTT) [\[22\]](#page-12-15) distinguishes itself by long-range matching characteristics. It employs training trajectories from large training sets as experts, which is an efficient method of information extraction compared to using pixel-level data. Therefore, MTT encompasses two primary phases: expert trajectory generation (buffer) and student parameter alignment (distillation).

Nevertheless, despite recent advancements, the research works [\[22\]](#page-12-15)–[\[24\]](#page-12-16) continue to utilize simplistic optimizer for generating weak expert trajectories. Moreover, the alignment process suffers from insufficient supervision, as solely relying on the final weight matching loss is suboptimal for the longrange matching process. Furthermore, nearly all DD algorithms [\[25\]](#page-12-17)–[\[27\]](#page-12-18) fail to adequately consider the compatibility of experts for students. This oversight neglects potential relationships that could consistently enhance the performance of both students and experts.

To tackle the catastrophic discrepancy arising from a more competent expert and to optimize the subsequent distillation process further, thereby making DD more efficient and effective, we identify three primary reasons that contribute to

2

the reduced performance of the distilled dataset. First, we observe that the smoothness of expert trajectories plays an important role. For instance, when substituting the initial nonmomentum Stochastic Gradient Descent (SGD) with a superior optimizer, it does enhance the accuracy of expert model. However, this replacement markedly intensifies the rate of parameter changes in expert model, rendering it less smooth and moderate, ultimately resulting in difficulty of student parameter alignment and a significant decline in the distillation outcomes.

Besides, the student parameter alignment exhibits a heightened susceptibility to two randomly initialized variables, namely the initial samples and the initial starting epochs of expert trajectories. The above two factors together also result in optimization instability of the distillation process. Particularly when distilling datasets of larger scale and higher resolution, it may lead to the generation of images akin to random noise and "black holes".

Finally, we argue that cumulative errors have a significant impact. It's not just the discrepancy between distillation and evaluation proposed by [\[23\]](#page-12-19) that results in cumulative errors. The long interval between inner and outer loop under the bi-level optimization structure will also generate accumulated errors, which degrade the final performance.

Building on the identified limitations, we propose Alignment with Smooth expert Trajectories (AST), which devises enhancements for both the expert trajectory generation and student parameter alignment phases, illustrated in Fig. [2.](#page-3-0) Initially, we propose a combination of clipping loss and gradient penalty under a more proficient optimizer to modulate the parameter update rate, ensuring sustained high expert performance during the training of expert models. This refined trajectory, being smooth and high-quality, lays a solid foundation for optimizing student parameter alignment with expert. Secondly, we propose a method encompassing representative sample initialization and inner loop loss balancing, aimed at reducing sensitivity to random variable initialization and stabilizing the matching process. Finally, we propose two alleviation strategies: intermediate matching loss and expert model weight perturbation, devised to mitigate cumulative error propensity. Our proposed methods have been extensively evaluated on datasets varying in scale, size, and resolution, with results affirming their effectiveness, markedly outperforming state-ofthe-art works.

Contributions. In summary, our main contributions can be summarized as follows:

- We argue that the smoothness of expert trajectories significantly affects student parameter alignment. We propose incorporating clipping loss and gradient penalty to limit the rate of expert parameter change.
- For better alignment with expert trajectory, we propose representative sample initialization and inner loop loss balancing strategies to alleviate the impact of stochasticity from initialization variables.
- We propose two enhancement strategies, namely intermediate matching loss and the perturbation of expert model weights, to mitigate cumulative errors from various sources.

# II. RELATED WORKS

# *A. Dataset Distillation*

The existing DD frameworks can be divided into four parts [\[11\]](#page-12-20), namely Meta-model Learning, Gradient Matching, Distribution Matching, and Trajectory Matching.

Wang *et al.* [\[25\]](#page-12-17) first introduces the dataset distillation problem and uses bi-level optimization similar to Model-Agnostic Meta-Learning (MAML) [\[28\]](#page-12-21). Following the Meta-Learning frameworks, recent works include adding soft labels [\[29\]](#page-12-22), [\[30\]](#page-12-23), developing closed-form approximation KIP [\[31\]](#page-12-24), [\[32\]](#page-12-25) and FRePo [\[33\]](#page-12-26), and applying prior towards the synthetic dataest [\[34\]](#page-12-27), [\[35\]](#page-12-28). Gradient Matching conducts a single-step distance matching between the network trained on the original dataset and the identical network trained on synthetic data, methods including DC [\[26\]](#page-12-29), DSA [\[36\]](#page-12-30), DCC [\[37\]](#page-12-31) and IDC [\[38\]](#page-12-32). To alleviate complex bi-level optimization and second-order derivative computation, Distribution Matching directly matches the distribution of original data and synthetic data with a single-level optimization, methods include DM [\[27\]](#page-12-18), CAFE [\[39\]](#page-12-33), IT-GAN [\[40\]](#page-12-34). However, the abovementioned works are all short-range matching methods and may easily cause an overfitting problem [\[13\]](#page-12-8).

Trajectory Matching [\[22\]](#page-12-15), as a long-range framework, leverages the well-trained expert training trajectory as reliable experts and aims to minimize the distance between expert and student trajectories in the parameter alignment process of the second stage, which improves the performance of distilled dataset. Recent works include TESLA [\[24\]](#page-12-16) that reparameterizes the computation of matching loss and FTD [\[23\]](#page-12-19) that directly incorporates the calculated error term into the loss function used for buffer generation. Nevertheless, these works still have their own issues, such as ignoring the mutual influence between smoothness of expert trajectory and effectiveness of student parameter alignment, susceptibility to random initial variables, and cumulative errors from multiple sources.

#### *B. Relationship between Expert and Student*

Currently, there exist some methods in the field of Knowledge Distillation (KD) that explore the factors of what makes experts conducive to students learning or how to transfer more valuable knowledge from highly proficient experts. Huang *et al.* [\[41\]](#page-12-35) has empirically pointed out that simply enhancing an expert's capabilities or employing a stronger expert can, paradoxically, result in a decrease in student performance. In some cases, this decline can even be more pronounced than if students were to begin training from scratch with vanilla KD. Consequently, they have proposed a loose matching approach to replace the traditional Kullback-Leibler (KL) divergence.

Similarly, Yuan *et al.* [\[42\]](#page-12-36) argues that simplifying the expert model's outputs can offer greater advantages to the student's learning process. Meanwhile, Shao *et al.* [\[43\]](#page-12-37) proposes the principle that "experts should instruct on what ought to be taught' and introduces a data-based knowledge distillation method. All of the methods mentioned above underscore the intimate relationship between experts and students, necessitating a thorough exploration of how to enhance the abilities of both experts and students consistently. Nonetheless, there

remains a vacancy in this relationship under the context of Dataset Distillation.

### III. PRELIMINARIES

# *A. Problem Statement*

We introduce the problem statement of Dataset Distillation. Given a real dataset  $\mathcal{D}_{real} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{D}_{real}|}$ , where the examples  $x_i \in \mathbb{R}^d$  and the class labels  $y_i \in \mathcal{Y} = \{1, 2, ..., C\}$ and C is the number of classes. The essence of Dataset Distillation is to generate a distilled dataset  $\mathcal{D}_{syn}$  with significantly fewer synthetic example-label pairs than  $\mathcal{D}_{real}$ . The goal is for a model f, when trained on  $\mathcal{D}_{sun}$ , to maintain the comparable performance of a counterpart trained on the original dataset  $\mathcal{D}_{real}$ .

The distilled dataset  $\mathcal{D}_{syn}$  is defined as a set of pairs  $\{(s_i, y_i)\}_{i=1}^{|\mathcal{D}_{syn}|}$ , with  $s_i$  as elements in  $\mathbb{R}^d$  and  $y_i$  belonging to  $\mathcal Y$ . For  $\mathcal D_{syn}$ , each class is represented by *ipc* examples, thus giving  $\mathcal{D}_{syn}$  a cardinality of  $ipc \times C$ , which is significantly lesser than that of  $\mathcal{D}_{real}$  ( $|\mathcal{D}_{syn}| \ll |\mathcal{D}_{real}|$ ). The optimal weight parameters, denoted as  $\theta$ , are those which minimize the empirical loss across the synthetic dataset  $\mathcal{D}_{syn}$ , formalized by the following equation:

$$
\theta = \arg\min_{\theta} \sum_{(s_i, y_i) \in \mathcal{D}_{syn}} \ell(f_{\theta}, s_i, y_i)
$$
 (1)

where  $\ell$  can be an arbitrary loss function which is taken to be the cross entropy loss in this paper. The objective of Dataset Distillation is to construct an informative synthetic dataset  $\mathcal{D}_{sun}$  that serves as an approximate solution to the optimization problem outlined below:

<span id="page-2-0"></span>
$$
\mathcal{D}_{syn} = \arg \min \mathcal{L}_{\mathcal{T}_{Test}}(f_{\theta})
$$
  
where  $\mathcal{D}_{syn} \subseteq \mathbb{R}^d \times \mathcal{Y}, |\mathcal{D}_{syn}| = ipc \times C$  (2)

To address the optimization challenge presented in Eq. [\(2\)](#page-2-0), Wang et al. [\[25\]](#page-12-17) employed a straightforward approach by substituting  $\mathcal{T}_{Test}$  with  $\mathcal{D}_{real}$  due to the unavailability of  $\mathcal{T}_{Test}$ during the distillation phase. This forms the meta-learning based framework.

#### *B. Various Objective Functions of DD*

Gradient-based Matching. In addition to using the results on  $\mathcal{D}_{real}$  as the objective function mentioned above, the gradient on real data can also be chosen as a reference. Under this, the objective function can be expressed as follows:

$$
\arg\min_{\mathcal{D}_{syn}} \mathbb{E}_{\theta} \left[ \sum_{t=0}^{T} D\left(\nabla_{\theta} \mathcal{L}_{\mathcal{D}_{real}}(\theta_t), \nabla_{\theta} \mathcal{L}_{D_{syn}}(\theta_t) \right) \right] \tag{3}
$$

where  $D(\cdot)$  is a distance metric such as cosine distance.

Distribution-based Matching. Distribution matching uses feature maps obtained on real data and distilled dataset as a proxy task, which can be expressed as:

$$
\arg\min_{\mathcal{D}_{syn}} \mathbb{E}_{\psi} \left[ \left\| \mathbb{E}_{x_i \sim \mathcal{D}_{real}} [\psi(x_i)] - \mathbb{E}_{s_i \sim \mathcal{D}_{syn}} [\psi(s_i)] \right\|^2 \right] \tag{4}
$$

where  $\psi(\cdot)$  refers to the process of extracting feature maps.

Trajectory-based Matching. The objective of matching expert trajectory is to align the training trajectory that is on  $\mathcal{D}_{real}$  with those on  $\mathcal{D}_{syn}$ . This can be formulated as below:

<span id="page-2-3"></span>
$$
\arg\min_{\mathcal{D}_{syn}} \mathbb{E}_{\theta} \left[ \sum_{t=0}^{T-\mathcal{M}} D\left(\theta_{t+\mathcal{M}}^{\mathcal{D}_{real}}, \theta_{t+\mathcal{N}}^{\mathcal{D}_{syn}}\right) / D\left(\theta_{t+\mathcal{M}}^{\mathcal{D}_{real}}, \theta_{t}^{\mathcal{D}_{real}}\right) \right] \tag{5}
$$

similarly,  $D(\cdot)$  is a distance metric to calculate the divergence of model parameters.  $M$  and  $N$  denote the number of intervals in the trajectories of the expert and the student, respectively. Further details are provided in Sec. [IV-D1.](#page-5-0)

#### IV. DESIGN OF AST

#### *A. Overview*

As described earlier, we choose the long-range Matching Training Trajectories (MTT) as our framework. MTT uses expert training trajectories as a golden reference. Under this case, the expert trajectory is a means of high-level extracted information over the original large dataset. Then, the parameter of student model is aligned towards the expert trajectory and update the distilled dataset through backpropagation. However, through observation and analysis, we have still identified significant improvement space. By implementing a series of meticulous optimizations and providing more comprehensive guidance, the effectiveness of the distilled dataset and stability of distillation process can be substantially enhanced. Specifically, our proposed AST method encompasses three dimensions. In Sec. [IV-B,](#page-2-1) we illustrate the mutual connection between expert and student and propose a way to maintain the expert trajectory smoothness under a more potent optimizer. Building on this improved trajectory, an enhanced parameter alignment method is proposed to balance the stochasticity (in Sec. **IV-C**) and alleviate the accumulated error (in Sec. **IV-D**). The framework of AST is illustrated in Fig. [2](#page-3-0) and our whole training algorithm can be found in Sec. [IV-E.](#page-7-0)

# <span id="page-2-1"></span>*B. Generate Smooth Expert Trajectories*

*1) Catastrophic Discrepancy with Stronger Expert Trajectory:* As depicted in Sec. [I,](#page-0-1) the influence of an expert on DD has not been adequately investigated, especially as the performance of the expert model becomes stronger, for instance, through the use of better optimizers. With this regard, as Table [I,](#page-2-2) we demonstrate both results of the expert model and the distilled dataset achieved with various optimizers.

<span id="page-2-2"></span>

| Optimizer (Expert) | Acc. (Expert) | Acc. (Distill) |
|--------------------|---------------|----------------|
| Naïve SGD          | 48.6          | 39.7           |
| SGD w/ Mom         | 57.1          | 18.8           |
| ADAM               | 52.7          | 18.1           |

TABLE I: Expert model accuracy and distilled results on various expert trajectories using various optimizers on CIFAR-100 under IPC=10.

We have observed that when employing SGD with momentum or ADAM as optimizers, although they generate stronger expert trajectories, theoretically elevating the upper

<span id="page-3-0"></span>Image /page/3/Figure/1 description: This diagram illustrates a method for distilling knowledge from an expert model to a student model. The process involves three main steps: 1. Generating a smooth expert trajectory from a large dataset of full training images (size = 50k). This smooth trajectory is compared to a non-smooth trajectory, with a loss function LSC applied. 2. Creating a representative initial set of data points, visualized as a scatter plot with blue dots and red stars, which is then used to generate a distilled dataset of size 10. 3. Parameter matching, where the student model is trained (forward pass) and backward propagated to match the expert's trajectory. This step is labeled as Distillation. The right side of the diagram shows an 'Outer-loop' which involves weight perturbation. It depicts a student trajectory (red) and an expert trajectory (blue dashed line). The difference between the student and expert trajectories is quantified by LTM and epsilon\_t.

Fig. 2: Illustration of AST method: (a) Buffer: We generate the smooth and high-quality expert trajectory (blue dash curve) on the real dataset  $\mathcal{D}_{real}$ . The grey dash curve is a much steeper expert trajectory without applying  $\mathcal{L}_{SC}$ . (b) Distillation: We first select representative initialization samples from the original training images. Then, the synthetic dataset  $\mathcal{D}_{syn}$  is optimized to match the segments of the expert trajectory through parameter alignment. We apply a balanced inner-loop loss  $\ell_{BIL}$  to mitigate the influence of random initialized expert starting epoch t. The red solid curve denotes student trajectory. (c) Outer-loop in distillation: We show how student trajectories are matched with expert trajectories within a single iteration. We introduce weight perturbation  $\mathbf{d}_{l,j}$  to the well-trained expert model parameters. Subsequently, we calculate the intermediate matching loss  $\mathcal{L}_{TM}$  in the middle of the loop. These two methods can help mitigate the accumulated errors  $\varepsilon_t$ .

limit of distillation dataset performance, this increment does not directly connect with improved results of the distilled dataset. On the contrary, the accuracy of the distilled dataset on the validation set experiences a substantial decline, even falling behind the vanilla MTT method. This indicates that the knowledge encapsulated within the more potent expert trajectories is not effectively assimilated during following distillation. The underlying reason is that the utilization of more potent optimizers often incorporates the previously accumulated gradient into the optimization process, which facilitates faster convergence of expert model [\[44\]](#page-12-38). However, this causes the expert model's parameters to update too rapidly, surpassing the limitation of what can be distilled during the second stage. It is detrimental to the student parameter alignment. For specific experimental details, please refer to Sec. [V-F1.](#page-10-0) For theoretical analysis, please refer to Sec. [IV-B2.](#page-3-1)

Consequently, our proposed approach aims to enhance the performance of the expert model while simultaneously moderating the rate of parameter updates in the training process of expert models. Our strategy allows us to obtain a series of smooth and high-quality expert trajectories.

<span id="page-3-1"></span>*2) Why do Stronger Expert Trajectories Perform Even Poorly?:* We hope to theoretically explain why stronger expert trajectories cannot contribute to consistent improvement of distilled datasets or even cause model collapse. Here, the stronger expert trajectories refer to using ADAM or SGD with momentum as an optimizer. Applying potent optimizers will accelerate the convergence due to the momentum. ADAM uses both first and second-order momentum. We take SGD with momentum as an example.

In the buffer phase, our objective function is as follows:

$$
\theta_t^* = \underset{\theta_t}{\text{arg min}} \quad \mathcal{L}(\theta_t^*, x, y)
$$

where 
$$
\mathcal{L}(\theta_t^*, x, y) = \frac{1}{|\mathcal{D}_{real}|} \sum_{(x, y) \in \mathcal{D}_{real}} [y \log(\theta_t^*(x))] \tag{6}
$$

in which  $\theta_t^*$  denotes the expert model at the t epoch. Then, we depict the optimization process:

$$
g_t = \nabla_{\theta} \mathcal{L}(\theta_t^*) = \frac{\partial \mathcal{L}}{\partial(\theta_t^*)}
$$
  
$$
\theta_{t+1}^* = \theta_t^* - v_t
$$
 (7)

in which  $v_t$  represents the momentum:

<span id="page-3-2"></span>
$$
v_t = \gamma \cdot v_{t-1} + \eta \cdot g_t \tag{8}
$$

here,  $\eta$  is the learning rate and  $\gamma$  is the momentum factor.  $v_t$ is the velocity update that contains the current gradient  $g_t$  and the exponential moving average of all the past time's gradients. We expand the Eq.  $(8)$  explicitly:

$$
v_t = \eta \cdot g_t + \gamma \cdot v_{t-1}
$$
  

$$
= \eta \cdot g_t + \gamma [\eta \cdot g_{t-1} + \gamma \cdot v_{t-2}]
$$
  
...

  

$$
= \eta \cdot [\sum_{k=1}^{t-1} \gamma^{t-k} \cdot g_k + g_t] (9)
$$

Compared to the normal SGD without momentum used in all the prior works, each update contains a cumulative item:

<span id="page-3-3"></span>
$$
\delta_t = v_t - \eta \cdot g_t = \sum_{k=1}^{t-1} \gamma^{t-k} \cdot g_k \tag{10}
$$

This cumulative term  $\delta_t$  can help the model converge faster during the training of expert models, avoiding repeated oscillations at saddle points. However, during the second stage of distillation, it may cause significant errors. The gradient's moving average term  $\delta_t$  contains a lot of redundant information, which will increase the difficulty of student parameters alignment.

In the distillation process, we only take a segment of the expert trajectories. Assume the expert starting point is  $t$  and

the endpoint is  $t + \xi_i$ . We use the expert model  $\theta_t^*$  to initialize the student network  $\hat{\theta}_t$ . We can then formulate the expert parameter changing as follows:

$$
\theta_{t+1}^{*} = \theta_{t}^{*} - v_{t-1}
$$

$$
\theta_{t+2}^{*} = \theta_{t+1}^{*} - v_{t}
$$

...

$$
\theta_{t+\xi_{i}}^{*} = \theta_{t+\xi_{i}-1}^{*} - v_{t+\xi_{i}-2}
$$

(11)

After sum up, we can get

<span id="page-4-0"></span>
$$
\theta_{t+\xi_i}^* - \theta_t^* = \sum_{p=t-1}^{t+\xi_i-2} v_p \tag{12}
$$

We take the Eq.  $(10)$  into the former equation so we get the extra item that needs to be alignment during the distillation:

$$
\Delta_{t-1}^* = \sum_{p=t-1}^{t+\xi_i-2} \delta_p = \sum_{p=t-1}^{t+\xi_i-2} \left[ \sum_{k=1}^{p-1} \gamma^{p-k} \cdot g_k \right]
$$
(13)

However, the gradient accumulation term used in the expert model during distillation is a kind of truncated accumulation term in the second phase, whose starting point already includes the momentum of  $v_{t-1}$ . But for the student model, the gradient accumulation starts from the zero origin and ends at  $N$ , as shown in Algorithm [2](#page-6-0) line 8-15. The origin does not have the initial gradient accumulation term. This leads to inconsistencies in parameter alignment, even if we also incorporate momentum during the distillation process. At the same time, this inconsistency will continue to amplify with iterations, resulting in difficulties in final convergence. The following formula represents the parameter updates in the student network:

$$
\hat{\theta}_{t+1} = \hat{\theta}_t - v'_0
$$

$$
\hat{\theta}_{t+2} = \hat{\theta}_{t+1} - v'_1
$$

$$
\vdots
$$

$$
\hat{\theta}_{t+n} = \hat{\theta}_{t+n-1} - v'_n
$$
 $(14)$ 

After sum up, we can get

<span id="page-4-1"></span>
$$
\hat{\theta}_{t+n} - \hat{\theta}_t = \sum_{p=0}^{n-1} v'_p \tag{15}
$$

Similarly, We take the Eq.  $(10)$  into the former equation so we get the extra gradient moving average item for the student model during the distillation:

$$
\hat{\Delta}_0 = \sum_{p=0}^{n-1} \delta'_p = \sum_{p=0}^{n-1} \left[ \sum_{k=1}^{p-1} \gamma^{p-k} \cdot g_k \right] \tag{16}
$$

When we calculate the numerator of alignment loss in Eq. [\(5\)](#page-2-3), it is equivalent to calculating the difference between Eq. [\(12\)](#page-4-0) and Eq. [\(15\)](#page-4-1).

<span id="page-4-2"></span>
$$
\left\|\hat{\theta}_{t+n} - \theta_{t+\xi_i}^*\right\|_2^2 = \left\|\sum_{p=0}^{n-1} v'_p - \sum_{p=t-1}^{t+\xi_i-2} v_p\right\|_2^2 \tag{17}
$$

and the additional gradient average term inside Eq. [\(17\)](#page-4-2) that

needs to be aligned is:

$$
\varepsilon_t = \left\| \Delta_{t-1}^* - \hat{\Delta}_0 \right\|_2^2 \tag{18}
$$

The above has demonstrated the errors  $\varepsilon_t$  introduced by incorporating momentum into the optimizer. The source of these errors is twofold. On one hand, the student trajectory, to be aligned in the distillation phase, is only one truncated segment from  $\theta_t$  to  $\theta_{t+n}$  initialized by a starting point of expert trajectory  $\theta_t^*$ . The student network lacks the initial gradient accumulation term, resulting in a divergence gap at the beginning. On the other hand, the presence of momentum during iterations exacerbates and magnifies the inherent inconsistencies, leading to further amplified errors. These two factors contribute to the challenge of student parameters alignment, ultimately failing to consistently improve distillation outcomes from better expert trajectories. The clipping loss and gradient penalty we propose in the next section can restrict the gradient accumulation term, keeping it within a smaller range from beginning to end. Specifically, we generate a series of smooth and high-quality expert trajectories, where the changing of parameters exhibits a flat trend while expert performance maintains improvement.

*3) Better Expert Trajectory under Smoothness Constraint:* To constrain the changing speed of expert parameters, the straightforward method involves the utilization of value clipping on the cross-entropy loss, with a specific focus on the initial epochs. Gradually, the clipping coefficient is incremented until it reaches a value of 1, which is then held constant. Our findings indicate that although loss clipping may impact the final expert performance to some extent, its key benefit lies in mitigating the rapid changes in model parameters, particularly during the initial few epochs.

Besides, to maintain the overall smoothness of the expert trajectory, we introduce the concept of gradient penalty, building upon the insights from the Wasserstein GAN (WGAN) [\[45\]](#page-12-39), [\[46\]](#page-12-40). The goal of employing gradient penalty is to force the model to satisfy Lipschitz continuity, preventing abrupt and erratic changes in the model's parameters. We incorporate gradient penalty by adding a regularization term to the loss function. This term is formulated as the squared norm of the gradient  $\nabla_{x_i} \mathcal{W}(x_i)$  of the model's output with respect to its input. By penalizing large gradients, we incentivize the model to produce outputs that change gradually as inputs vary slightly. This has the effect of maintaining a smoother transition between data points and preventing sudden shifts that could lead to instability. We formulate the final loss equation as below:

<span id="page-4-3"></span>
$$
\mathcal{L}_{SC} = \lambda \log \frac{\exp (x_{i,y_i})}{\sum_{c=1}^{C} \exp (x_{i,c})} + \underbrace{\mu \mathbb{E}_{x_i \sim \mathbb{P}_x} \left[ \left( \left\| \nabla_{x_i} \mathcal{W}(x_i) \right\|_2 - \mathcal{K} \right)^2 \right]}_{\text{Gradient Penalty}}
$$
(19)

where  $\|\nabla_{x_i} \mathcal{W}(x_i)\|_2$  represents a dual-sided penalty that aims to constrain the gradient norm to values below a predetermined threshold denoted as  $K$ . Typically,  $K$  is set at 1. The coefficient  $\lambda$  operates within a range of 0.5 to 1 during the initial few epochs, while the coefficient  $\mu$  is responsible for scaling the gradient penalty effect.

<span id="page-5-4"></span>

| <b>Initial Samples</b> | <b>Starting Epoch</b>    | <b>Acc. (Distill)</b> |
|------------------------|--------------------------|-----------------------|
| Random                 | Random                   | 39.7                  |
| Representative         | Random                   | 40.3                  |
| Random                 | <b>Balanced Strategy</b> | 40.1                  |

TABLE II: After using the stochasticity balancing strategy during parameter matching, there has been a consistent improvement in the performance of the distilled dataset.

#### <span id="page-5-1"></span>*C. Balancing Stochasticity from Initial Variables*

<span id="page-5-5"></span>*1) Representative Initial for Synthetic Dataset:* Many existing dataset distillation algorithms initialize  $\mathcal{D}_{syn}$  either by employing random initialization with Gaussian noise or by randomly selecting a subset of images from the real dataset. However, this approach can inadvertently introduce outliers, lead to the inclusion of samples with similar features, or overlook certain aspects of the feature space, which may introduce huge biases and stochasticity. Moreover, as distilled datasets often end up with a limited number of samples per class, it becomes crucial to efficiently encapsulate the inherent information of the original dataset within these limited samples. To mitigate these limitations, we propose representative initial samples for  $\mathcal{D}_{syn}$ .

Leveraging the benefits of the MTT framework can simplify the process of selecting representative initialization samples for  $\mathcal{D}_{sun}$ . In the buffer stage, we have already obtained the training trajectory of the expert model. Then, we can acquire a well-trained model easily by loading the parameters of expert trajectory. Next, we input all real data of the same class into the model, obtaining feature vectors before entering the fully connected layer. Subsequently, we perform clustering on these feature maps, utilizing the K-Means algorithm to partition them into multiple sub-clusters. The value of K is chosen based on the desired number of distilled samples. These sub-cluster centroids are then selected as exemplary initialization samples. The primary objective is to enhance the initialization process by strategically selecting representative samples that are better suited for distillation.

<span id="page-5-8"></span>*2) Balanced Inner-loop Loss:* During the second stage, student parameter alignment exhibits sensitivity to the randomly initialized expert starting epochs  $t$  in each iteration. This sensitivity is specifically manifested in significant fluctuations of the inner-loop loss  $\ell_{IL}$ . Depending on the various starting points shown in Fig.  $3a$ , the largest loss is nearly 60 times greater than the smallest loss shown in Fig. [3b.](#page-5-3) The fluctuations in the inner-loop loss introduce a notable level of instability into the distillation procedure, which impedes the parameters' alignment and hinders the consistent acquisition of accurate distilled data.

One naive method is to discard the practice of entirely random selection for the starting epoch and instead opt for a method that references the preceding starting point and selects the subsequent starting point within a reasonable range. However, experiments (details in Sec. [V-F2\)](#page-11-1) have demonstrated that this dynamic selection approach fails to enhance the final performance. We speculate that employing a range selection may introduce potential bias into the distillation process, which in turn causes the model to learn incorrect inductive bias.

<span id="page-5-3"></span>Image /page/5/Figure/7 description: The image displays two line graphs side-by-side, both plotting data against "Distill Epochs on CIFAR100 (ipc=10)" on the x-axis. The left graph, labeled "(a) Randomly Start Epoch", shows a dashed blue line representing "Start Epoch" on the y-axis, ranging from 0 to 30. The right graph, labeled "(b) Inner-loop Loss", presents two solid lines: a black line for "Naive" and a red line for "Balanced", plotting "Inner-loop Loss" on the y-axis, ranging from 0.0 to 3.5. Both graphs show fluctuating trends across the epochs.

Fig. 3: Because of the random variable of starting epoch for each iteration (as depicted in the blue dashed plot in (a)), it leads to substantial fluctuations in the loss scale within the inner loop, as indicated by the solid grey plot in (b). After employing a balanced inner-loop loss, the extent of loss variation is constrained to a lesser degree during the early starting epochs, while allocating more weight to the later starting epochs, as illustrated by the red plot in (b).

Hence, we propose to balance the loss within the inner loop.

When the starting epoch  $t$  is much smaller, which means the network is under convergence, it will typically generate a much larger loss  $\ell_{IL}$  leading the gradient descent towards this direction. Thus, we add a penalty coefficient  $\nu$  to narrow the loss, clipping the loss to a small extent. Conversely, when the starting epoch t is large (close to  $\mathcal{T}_+$ ), the optimized steps become much smaller due to the decreased loss. We also add a coefficient  $\nu$  to encourage a much bigger gradient. Under this circumstance, the balanced inner-loop loss  $\ell_{BIL}$  can be formulated as:

<span id="page-5-7"></span>
$$
\nu = \begin{cases}\n\log(|start - middle| + \vartheta), & start \geq middle \\
1/log(|middle - start| + \vartheta), & start < middle \\
\hat{\ell}_{BL} = \nu \times \ell_{IL}\n\end{cases}
$$
\n(20)

where *start* represents the starting epoch, *middle* refers to half of the maximum starting epoch and  $\vartheta$  is included to balance the log scale and prevent negative values. As shown in Fig. [3,](#page-5-3) using balanced loss  $\ell_{BIL}$  can reduce the loss fluctuation in the inner loop. For  $\ell_{IL}$ , it is similar to cross-entropy loss:

<span id="page-5-6"></span>
$$
\ell_{IL} = \frac{1}{|\mathcal{D}_{syn}|} \sum_{(s_i, y_i) \in \mathcal{D}_{syn}} \left[ y_i \log(\hat{\theta}_t(\mathcal{A}(s_i))) \right] \tag{21}
$$

where A represents the differentiable siamese augmenta-tion [\[36\]](#page-12-30), while  $\hat{\theta}_t$  denotes the student network parameterized using the expert's  $t$ -th epoch.network,  $t$  is smaller than the defined maximum start epoch  $\mathcal{T}_+$  and  $\mathcal{D}_{syn}$  is the distilled samples. From Table  $II$ , a consistent improvement of the distilled dataset is observed after using the stochasticity balancing strategy.

#### <span id="page-5-2"></span>*D. Alleviate the Propensity for Accumulated Error*

<span id="page-5-0"></span>*1) Intermediate Matching Loss:* Trajectory matching is a long-range framework, typically involving a larger number of steps denoted as  $\mathcal N$  to match a smaller number of epochs denoted as  $M$ . Here,  $M$  signifies the number of intervals between the starting and target point of the expert trajectory, while  $N$  represents the training steps of the student model on the distilled dataset.  $N$  is also equivalent to the number of iterations in the inner loop. After every  $N$  steps, it will execute a matching interaction with the expert trajectory. However,

<span id="page-6-2"></span>

| <b>Algorithm 1:</b> Dataset Distillation in Expert Trajectory<br>Generation |
|-----------------------------------------------------------------------------|
| <b>Input:</b> $\mathcal{D}_{real}$ : real dataset.                          |
| <b>Input:</b> f: expert network with weights $\theta$ .                     |
| <b>Input:</b> $\eta$ : learning rate.                                       |

Input: E: training epochs.

Input: T: iterations per epoch.

1 for  $e = 1$  to  $E$  do

| 2 | <b>for</b> t = 1 to T, mini-batch $B \subset D_{real}$ <b>do</b>      |
|---|-----------------------------------------------------------------------|
| 3 | Compute loss $L$ based on Eq. (19);                                   |
| 4 | Compute gradients: $g_L = \nabla_{\theta} L_B (f_{\theta_t})$ ;       |
| 5 | Compute the momentum: $v_t = \gamma \cdot v_{t-1} + \eta \cdot g_L$ ; |
| 6 | Update weights: $\theta_{t+1} \leftarrow \theta_t - v_t$ ;            |
| 7 | end                                                                   |
| 8 | Record weights $\theta_T$ of expert trajectory at the end             |
|   | of each epoch                                                         |
| 9 | end                                                                   |
|   | <b>Output:</b> Smooth expert trajectory $\tau := \{\theta_T\}$        |

extensive intervals of interaction can lead to the inner loop deviating from the correct direction prematurely. It will result in the accumulation of errors without sufficient and timely supervision guidance. Therefore, we propose intermediate matching loss shown in Fig. [2](#page-3-0) "Outer-loop", which optionally performs an additional parameter matching step within the inner loop.

Specifically, we establish a set of intermediate matching points denoted as  $\xi$ , which consists of two parameters,  $M$  and  $N$ . The matching points are selected on an average basis and can be represented as  $\{\xi\}$  $\{|\mathcal{N}/\mathcal{M}|, |2 \times \mathcal{N}/\mathcal{M}|, ..., |(\mathcal{M}-1) \times \mathcal{N}/\mathcal{M}|\}$ , where  $|\cdot|$ is floor function. When the inner loop n (from 1 to N) precisely aligns with  $\xi_i \in {\{\xi\}}$ , the intermediate matching loss function is computed as:

$$
\mathcal{L}_{TM} = \frac{\left\| \hat{\theta}_{t+n} - \theta_{t+\xi_i}^* \right\|_2^2}{\left\| \theta_t^* - \theta_{t+\xi_i}^* \right\|_2^2}
$$
(22)

in which  $\theta_t^*$  is the expert training trajectory at randomly starting epoch  $t$ , also known as the initial expert weights. Starting from  $\theta_t^*$ ,  $\theta_{t+\xi_i}^*$  denotes  $\{\xi\}$  steps trained by the expert network on real data and  $\hat{\theta}_{t+n}$  stands for n steps  $(n < N)$  by student network trained on synthetic data correspondingly. The goal is to minimize the divergence between  $\hat{\theta}_{t+n}$  and  $\theta_{t+\xi_i}^*$ , and  $\left\|\theta_t^* - \theta_{t+\xi_i}^*\right\|$ 2 2 is used to self-calibrate the magnitude across different iterations.  $\mathcal{L}_{TM}$  is then utilized to update the student parameters.

<span id="page-6-1"></span>*2) Weight Perturbation on Initial Expert Model:* Another potential source of cumulative error stems from the disparity between the distillation and evaluation, as pointed out in [\[23\]](#page-12-19). Due to the discrete nature of the distillation process versus the continuous nature of the evaluation, cumulative errors can thus arise. Therefore, unlike incorporating the calculated error term into the buffer loss function [\[23\]](#page-12-19), we introduce a simpler approach to simulate the potential expert weight deviation

<span id="page-6-0"></span>

| Algorithm 2: Dataset Distillation in Student Parameter                                                                             |
|------------------------------------------------------------------------------------------------------------------------------------|
| Alignment                                                                                                                          |
| <b>Input:</b> $\{\tau_i\}$ : set of smoothed expert parameter                                                                      |
| trajectories trained on real dataset $\mathcal{D}_{real}$ .                                                                        |
| Input: M: number of intervals between starting and                                                                                 |
| target expert trajectory.                                                                                                          |
| Input: $N$ : distillation steps of student network.                                                                                |
| Input: A: differentiable siamese augmentation.                                                                                     |
| <b>Input:</b> $\mathcal{T}_+$ : maximum start epoch.                                                                               |
| <b>Input:</b> $\{\xi\} = \{[\mathcal{N}/\mathcal{M}], ..., [(\mathcal{M}-1)\times\mathcal{N}/\mathcal{M}]\}$ : set                 |
| of intermediate matching epochs.                                                                                                   |
| 1 Select representative initialization samples                                                                                     |
| $\mathcal{D}_{syn} \sim \mathcal{D}_{real}$ ; // Method IV-C1                                                                      |
| 2 Initialize trainable learning rate $\alpha := \alpha_0$ ;                                                                        |
| 3 for each distillation step do                                                                                                    |
| Sample smooth expert trajectory $\tau \sim \{\tau_i\}$ with                                                                        |
| 4                                                                                                                                  |
| $\tau := \{\theta_t\};$                                                                                                            |
| Choose random start epoch, $t \leq \mathcal{T}_+$ ;                                                                                |
| 5                                                                                                                                  |
| Perturb weight on initial expert model with                                                                                        |
| 6                                                                                                                                  |
| $\tau^* := \{\theta_t^*\};$ // Method IV-D2                                                                                        |
| Initialize student network with expert parameters                                                                                  |
| 7                                                                                                                                  |
| $\hat{\theta}_t := \theta_t^*;$                                                                                                    |
| for $n = 1$ to $\mathcal N$ do                                                                                                     |
| 8                                                                                                                                  |
| Sample a mini-batch of distilled images:                                                                                           |
| 9                                                                                                                                  |
| $b_{t+n} \sim \mathcal{D}_{syn};$                                                                                                  |
| Compute the cross-entropy loss based on                                                                                            |
| 10                                                                                                                                 |
| Eq. $(21)$ .;                                                                                                                      |
| Get $\nu$ based on Eq. (20) and balance $\ell_{IL}$ :                                                                              |
| 11                                                                                                                                 |
| $\ell_{BIL} = \nu \times \ell_{IL}; //$ Method IV-C2                                                                               |
| Update student model:                                                                                                              |
| 12                                                                                                                                 |
| $\hat{\theta}_{t+n+1} = \hat{\theta}_{t+n} - \alpha \nabla \hat{\ell}_{BIL};$                                                      |
| if <i>n</i> in $\{\xi\}$ then                                                                                                      |
| 13                                                                                                                                 |
| Calculate intermediate matching loss                                                                                               |
| $\mathcal{L}_i = \left\ \hat{\theta}_{t+n} - \theta_{t+\xi_i}^*\right\ _2^2 / \left\ \theta_t^* - \theta_{t+\xi_i}^*\right\ _2^2;$ |
| 14                                                                                                                                 |
| // Method IV-D1                                                                                                                    |
| end                                                                                                                                |
| 15                                                                                                                                 |
| end                                                                                                                                |
| 16                                                                                                                                 |
| end                                                                                                                                |
| Get the final loss $\hat{\mathcal{L}} = \sum_{\xi_i} \beta_i \times \mathcal{L}_i$ ;                                               |
| 17                                                                                                                                 |
| Update $\mathcal{D}_{syn}$ and $\alpha$ with respect to $\hat{\mathcal{L}}$ ;                                                      |
| 18                                                                                                                                 |
| 19                                                                                                                                 |
| end                                                                                                                                |
| <b>Output:</b> Distilled dataset $\mathcal{D}_{syn}$ and learning rate $\alpha$                                                    |

during the parameter alignment, named weight perturbation. The perturbation value can be calculated as follows:

$$
\mathbf{d}_{l,j} = \frac{\mathbf{d}_{l,j}}{\|\mathbf{d}_{l,j}\|_F} \|\theta_{l,j}\|_F
$$
  
\n
$$
\theta_t^* = \theta_t + \rho * \mathbf{d}_{l,j}
$$
\n(23)

where  $\mathbf{d}_{l,j}$  is sampled from a Gaussian distribution  $N(0, 1)$ with dimensions same as  $\theta_{l,j}$ .  $\mathbf{d}_{l,j}$  is the j-th filter at the l-th layer of d and  $\lVert \cdot \rVert_F$  refers to the Frobenius norm. Finally, a coefficient  $\rho$  is added to obtain the final  $\theta_t^*$ .

|  |  | ł<br>I<br>in 19<br>۰. |
|--|--|-----------------------|
|  |  |                       |

<span id="page-7-1"></span>

| <b>IPC</b>                                                                            |                                                                                                            | <b>CIFAR-10</b><br>1                                                                                       | 10                                                                                                         | 50                                                                                                           | <b>CIFAR-100</b><br>1                                                                                      | 10                                                               | 50                                                           | <b>Tiny ImageNet</b><br>1                                        | 10 |
|---------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------|--------------------------------------------------------------|------------------------------------------------------------------|----|
| <b>Full Dataset</b>                                                                   |                                                                                                            | $84.8 	ext{±} 0.1$                                                                                         |                                                                                                            |                                                                                                              | $56.2 	ext{±} 0.3$                                                                                         |                                                                  |                                                              | $39.5 	ext{±} 0.4$                                               |    |
| Random<br>Herding [8]<br>Forgetting [9]                                               | $14.4 	ext{±} 2.0$<br>$21.5 	ext{±} 1.2$<br>$13.5 	ext{±} 1.2$                                             | $26.0 	ext{±} 1.2$<br>$31.6 	ext{±} 0.7$<br>$23.3 	ext{±} 1.0$                                             | $43.4 	ext{±} 1.0$<br>$40.4 	ext{±} 0.6$<br>$23.3 	ext{±} 1.1$                                             | $4.2 	ext{±} 0.3$<br>$8.4 	ext{±} 0.3$<br>$4.5 	ext{±} 0.2$                                                  | $14.6 	ext{±} 0.5$<br>$17.3 	ext{±} 0.3$<br>$15.1 	ext{±} 0.3$                                             | $30.0 	ext{±} 0.4$<br>$33.7 	ext{±} 0.5$<br>$30.5 	ext{±} 0.3$   | $1.4 	ext{±} 0.1$<br>$2.8 	ext{±} 0.2$<br>$1.6 	ext{±} 0.1$  | $5.0 	ext{±} 0.2$<br>$6.3 	ext{±} 0.2$<br>$5.1 	ext{±} 0.2$      |    |
| <b>DC</b> [26]<br>DM [27]<br><b>DSA</b> [36]<br><b>CAFE</b> [39]<br><b>FRePo</b> [33] | $28.3 	ext{±} 0.5$<br>$26.0 	ext{±} 0.8$<br>$28.8 	ext{±} 0.7$<br>$30.3 	ext{±} 1.1$<br>$45.1 	ext{±} 0.5$ | $44.9 	ext{±} 0.5$<br>$48.9 	ext{±} 0.6$<br>$52.1 	ext{±} 0.5$<br>$46.3 	ext{±} 0.6$<br>$59.1 	ext{±} 0.3$ | $53.9 	ext{±} 0.5$<br>$63.0 	ext{±} 0.4$<br>$60.6 	ext{±} 0.5$<br>$55.5 	ext{±} 0.6$<br>$69.6 	ext{±} 0.4$ | $12.8 	ext{±} 0.3$<br>$11.4 	ext{±} 0.3$<br>$13.9 	ext{±} 0.3$<br>$12.9 	ext{±} 0.3$<br>$25.9 	ext{±} 0.1$ T | $25.2 	ext{±} 0.3$<br>$29.7 	ext{±} 0.3$<br>$32.3 	ext{±} 0.3$<br>$27.8 	ext{±} 0.3$<br>$40.9 	ext{±} 0.1$ | $43.6 	ext{±} 0.4$<br>$42.8 	ext{±} 0.4$<br>$37.9 	ext{±} 0.3$   | $3.9 	ext{±} 0.2$<br>$13.5 	ext{±} 0.1$ T                    | $12.9 	ext{±} 0.4$<br>$20.4 	ext{±} 0.1$                         |    |
| <b>MTT</b> [22]<br><b>TESLA</b> [24]<br>FTD [23]                                      | $46.2 	ext{±} 0.8$<br>$48.5 	ext{±} 0.8$ †<br>$46.8 	ext{±} 0.3$                                           | $65.4 	ext{±} 0.7$<br>$66.4 	ext{±} 0.8$<br>$66.6 	ext{±} 0.3$ †                                           | $71.6 	ext{±} 0.2$<br>$72.6 	ext{±} 0.7$<br>$73.8 	ext{±} 0.2$ †                                           | $24.3 	ext{±} 0.3$<br>$24.8 	ext{±} 0.4$<br>$25.2 	ext{±} 0.2$                                               | $39.7 	ext{±} 0.4$<br>$41.7 	ext{±} 0.3$<br>$43.4 	ext{±} 0.3$ †                                           | $47.7 	ext{±} 0.2$<br>$47.9 	ext{±} 0.3$<br>$50.7 	ext{±} 0.3$ † | $8.8 	ext{±} 0.3$<br>$9.8 	ext{±} 0.4$<br>$10.4 	ext{±} 0.3$ | $23.2 	ext{±} 0.2$<br>$24.4 	ext{±} 0.6$<br>$24.5 	ext{±} 0.2$ † |    |
| Ours                                                                                  | $48.8 	ext{±} 0.9$                                                                                         | $67.1 	ext{±} 0.4$                                                                                         | $74.6 	ext{±} 0.5$                                                                                         | $26.6 	ext{±} 0.4$                                                                                           | $44.4 	ext{±} 0.6$                                                                                         | $51.7 	ext{±} 0.7$                                               | $13.7 	ext{±} 1.4$                                           | $25.7 	ext{±} 1.1$                                               |    |

TABLE III: Performance comparision trained with 128 width-ConvNet [\[49\]](#page-12-41) to other state-of-the-art methods on the CIFAR and Tiny ImageNet. We cite the reported results from Sachdeva *et al.* [\[11\]](#page-12-20) and Du *et al.* [\[23\]](#page-12-19). IPC: Images Per Class. Bold digits represent the best results and † refers to the second-best results among all the methods..

#### <span id="page-7-0"></span>*E. Training Algorithm*

We show the algorithm of expert trajectory generation under smoothness constraint in Algorithm [1.](#page-6-2) We incorporate the methods in Sec. [IV-C](#page-5-1) and Sec. [IV-D](#page-5-2) into the student parameter alignment stage and depict our detailed algorithm in Algorithm [2.](#page-6-0)

#### V. EXPERIMENTS

#### *A. Experiments Setup*

The majority of our experimental procedures closely follow the previous works [\[22\]](#page-12-15)–[\[24\]](#page-12-16), which ensure a fair and equitable basis for comparison. Each of our experiments comprises three essential phases: buffer (generating expert trajectories), distillation (student parameters alignment), and evaluation phase. First, we generate 50 distinct expert training trajectories, with each trajectory encompassing 50 training epochs. Second, we synthesize a small distilled dataset (e.g., 10 images per class) from a given large real training set. Finally, we employ this learned distilled dataset to train randomly initialized neural networks and assess the performance of these trained networks on the real test dataset.

Datasets. We verify the effectiveness of our method on both low- and high- resolution datasets distillation benchmarks, including CIFAR-10 & CIFAR-100 [\[47\]](#page-12-42), Tiny ImageNet [\[48\]](#page-12-43) and ImageNet subsets [\[22\]](#page-12-15). Top-1 accuracy is reported to show the performance.

Models. We employ the ConvNet architecture in the distillation process, in line with other methods except for FrePo, which utilizes a different model that doubles the number of filters when the feature map size is halved. Our architecture comprises 128 filters in the convolutional layer with a  $3 \times 3$ kernel, followed by instance normalization, ReLU activation, and an average pooling layer with a  $2 \times 2$  kernel and stride 2. For CIFAR-10 and CIFAR-100, we adopt 3-layer convolutional networks (ConvNet-3). In the case of the Tiny ImageNet dataset with a resolution of  $64 \times 64$ , we employ a depth-4 ConvNet. For the ImageNet subsets with a resolution of  $128 \times 128$ , a depth-5 ConvNet is used.

Implementation Details. In the buffer phase, we utilize SGD with momentum as the optimizer, setting  $\lambda$  as an array ranging from 0.5 to 1. This range is applied individually for the first 5 training epochs, while  $\mu$  is set to 1. We train each expert model for 50 epochs, with the learning rate reduced by half in the 25th epoch. During the distillation process, the value of K in the K-Means algorithm is determined based on the ipc (images per class) value. However, when ipc equals 50, we opt to cluster only 10 sub-clusters, each selecting extra 4 points approximating the sub-cluster centroid. For the balanced loss, we set  $\theta$  to 8. Concerning the intermediate matching loss, we introduce a hyperparameter  $\beta$  to control the scale of several losses, encompassing two strategies: equal scale  $\beta=1$ or varied scale  $\beta$  based on the value of  $\{\xi\}$ . In terms of weight perturbation, we set  $\rho$  to 0.1, and we also conduct experiments to evaluate the performance with dropout added. Throughout the evaluation phase, the number of training iterations is set to 1000, with a learning rate reduction by half at the 500 th iteration. Furthermore, we employ the same Differentiable Siamese Augmentation (DSA) during both the distillation and evaluation processes.

#### *B. Comparison with State-of-the-Art Methods*

Competitors: We categorize the current works into three main parts, shown in Table [III.](#page-7-1) The first part focuses on coreset selection and includes non-learning methods such as random selection, herding methods [\[8\]](#page-12-5), and example forgetting [\[9\]](#page-12-6). These techniques aim to select "valuable" instances from a large dataset. The second part comprises methods like DC, DM, DSA, CAFE, and FrePo, which primarily address shortrange matching and truncated gradient backpropagation. They employ gradient matching or distribution matching as the optimization objective. Within the third part, MTT, TESLA, and FTD all utilize long-range matching characteristics based on the trajectory matching framework. However, MTT remains

<span id="page-8-0"></span>Image /page/8/Picture/1 description: The image displays three grids of images, each containing 48 smaller images arranged in six rows and eight columns. The grids appear to be generated by a machine learning model, as some images are clear and recognizable, while others are blurry or abstract. The top row of each grid predominantly features images of apples. Subsequent rows showcase a variety of subjects including fish, babies, bears, beds, flowers, insects, bicycles, and bottles. The overall impression is a collection of diverse visual samples, possibly representing different categories or outputs from an AI model.

(a) Original CIFAR100 images. (b) The synthetic images of MTT. (c) The synthetic images of ours.

(c) The synthetic images of ours.

Fig. 4: Visualizations of original images, and synthetic images generated by MTT and our proposed methods.

<span id="page-8-2"></span>

| Method | ConvNet            | Evaluation Model<br>ResNet | VGG                | AlexNet            |
|--------|--------------------|----------------------------|--------------------|--------------------|
| DC     | $53.9 	ext{±} 0.5$ | $20.8 	ext{±} 1.0$         | $38.8 	ext{±} 1.1$ | $28.7 	ext{±} 0.7$ |
| CAFE   | $55.5 	ext{±} 0.4$ | $25.3 	ext{±} 0.9$         | $40.5 	ext{±} 0.8$ | $34.0 	ext{±} 0.6$ |
| MTT    | $71.6 	ext{±} 0.2$ | $61.9 	ext{±} 0.7$         | $55.4 	ext{±} 0.8$ | $48.2 	ext{±} 1.0$ |
| FTD    | $73.8 	ext{±} 0.2$ | $65.7 	ext{±} 0.3$         | $58.4 	ext{±} 1.6$ | $53.8 	ext{±} 0.9$ |
| Ours   | $74.6 	ext{±} 0.5$ | $67.3 	ext{±} 0.4$         | $60.3 	ext{±} 0.5$ | $56.7 	ext{±} 0.3$ |

TABLE IV: Generalization testing of different architectures on CIFAR-10 dataset with IPC 50.

our primary competitor since TESLA and FTD integrate additional optimization techniques.

Results with Coreset & Short-range: Our proposed method outperforms the coreset selection baselines significantly. The non-learning methods achieve inferior performance compared to our methods. When comparing with the second part works, our method demonstrates the same superiority over all other methods. Moreover, we improve one of the strong baseline DSA to nearly 15% accuracy on both CIFAR-10 and CIFAR-100 under all ipc settings.

Results with Long-range: Regarding the comparison of experimental results in the third part, we observe consistently positive outcomes with significant improvements in all settings. We specifically compare our method with the original MTT. For example, in CIFAR-10, at a compression rate of fifty images, we achieve a score of 74.6%, which is 3% higher than the accuracy score of MTT. Compared to the results obtained from the full dataset, we are only around 10% behind. Similar improvements are observed in CIFAR-100, where we achieve a 4% increase over MTT and our results are only 4.5% lower than the results obtained from the full dataset. We visualize parts of the distilled images in Fig. [4](#page-8-0) and discover that our distilled samples Fig. [4c](#page-8-0) can focus more on the classified object itself while diluting the background information.

In the more intricate Tiny ImageNet dataset, our approach demonstrates consistent and significant improvements, achieving 13.7% and 25.7% in ipc values of one and ten. These empirical findings substantiate the efficacy of our proposed method. We visualize parts of the distilled images in Fig. [5.](#page-8-1)

Cross-Architecture Generalization: We evaluate gener- Fig. 5: Visualizations of synthetic images in Tiny ImageNet.

<span id="page-8-1"></span>Image /page/8/Picture/13 description: The image displays a grid of 8x8, totaling 64 small images, each representing a synthetic image generated by Tiny ImageNet. The images are diverse, featuring various subjects such as people in different poses and clothing, vehicles like trucks and vans, cityscapes with landmarks, natural scenes with fences and trees, and everyday objects like bottles and boxing bags. The overall quality of the images is somewhat abstract and impressionistic, with colors and shapes blending together, suggesting the generative nature of the AI model.

<span id="page-9-2"></span>Image /page/9/Figure/0 description: This image contains three line graphs side-by-side, each plotting "Max Accuracy" on the y-axis against "Evaluation" on the x-axis. The first graph is labeled "Evaluation on CIFAR100 (ipc=10)" and shows the y-axis ranging from 0.20 to 0.45. The second graph is labeled "Evaluation on CIFAR100 (ipc=50)" and shows the y-axis ranging from 0.36 to 0.52. The third graph is labeled "Evaluation on Tiny ImageNet (ipc=10)" and shows the y-axis ranging from 0.075 to 0.250. Each graph displays two lines: a solid red line labeled "Ours" and a dashed blue line labeled "MTT". In all three graphs, the "Ours" line consistently shows higher accuracy than the "MTT" line across the evaluated range.

Fig. 6: Applying our proposed methods brings stable performance and efficiency improvements.

<span id="page-9-0"></span>

| IPC             | ImageNette     |                | ImageWoof      |                | ImageFruit     |                | ImageMeow      |                |
|-----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
|                 | 1              | 10             | 1              | 10             | 1              | 10             | 1              | 10             |
| Full dataset    | $87.4 \pm 1.0$ |                | $67.0 \pm 1.3$ |                | $63.9 \pm 2.0$ |                | $66.7 \pm 1.1$ |                |
| <b>MTT [22]</b> | $47.7 \pm 0.9$ | $63.0 \pm 1.3$ | $28.6 \pm 0.8$ | $35.8 \pm 1.8$ | $26.6 \pm 0.8$ | $40.3 \pm 1.3$ | $30.7 \pm 1.6$ | $40.4 \pm 2.2$ |
| FTD [23]        | $52.2 \pm 1.0$ | $67.7 \pm 0.7$ | $30.1 \pm 1.0$ | $38.8 \pm 1.4$ | $29.1 \pm 0.9$ | $44.9 \pm 1.5$ | $33.8 \pm 1.5$ | $43.3 \pm 0.6$ |
| <b>Ours</b>     | $53.1 \pm 0.8$ | $68.4 \pm 1.2$ | $31.6 \pm 0.6$ | $39.5 \pm 1.5$ | $30.0 \pm 1.2$ | $45.4 \pm 1.5$ | $34.6 \pm 1.5$ | $44.9 \pm 1.7$ |

TABLE V: Applying our methods to 128×128 resolution ImageNet subsets. Bold digits represent the best results.

alization capacity across various architectures. Initially, we distilled the synthetic dataset using ConvNet. Subsequently, we trained several architectures, namely AlexNet, VGG11, and ResNet18, on the distilled dataset. Table [IV](#page-8-2) presents the results of our evaluations. Our method outperforms MTT significantly in generalization performance.

# *C. Results on ImageNet Subsets (128*×*128)*

To further evaluate our method, we present results on a larger and more challenging dataset in Table [V.](#page-9-0) The ImageNet subsets pose a greater difficulty compared to CIFAR-10/100 and Tiny ImageNet, primarily due to their higher resolutions. The higher resolution makes it challenging for the distillation procedure to converge. The ImageNet subsets consist of 10 categories selected from ImageNet-1k, following the setting of MTT. These subsets include ImageNette (assorted objects), ImageWoof (dog breeds), ImageFruits (fruits), and Image-Meow (cats). As shown in Table  $V$ , our method significantly improves MTT in every subset. For instance, we achieve a significant performance boost on the ImageNette subset with  $\text{inc} = 1$  and 10, surpassing MTT by more than 5.0%. We also record the FTD results for fair comparison and our method achieves much better.

<span id="page-9-1"></span>

| Dataset       | Image per class | 1 Iter. (sec) | 1k Iter. (min) | 5k Iter. (min) |
|---------------|-----------------|---------------|----------------|----------------|
| CIFAR-10      | 1               | 0.5           | 8.3            | 41             |
|               | 10              | 0.6           | 11             | 50             |
|               | 50              | 0.9           | 15             | 75             |
| CIFAR-100     | 1               | 0.6           | 11             | 50             |
|               | 10              | 0.85          | 14             | 70             |
|               | 50              | 1.97          | 33             | 163            |
| Tiny ImageNet | 1               | 1.15          | 20             | 95             |
|               | 10              | 2.42          | 40             | 200            |

TABLE VI: Distillation time for each dataset and support IPC.

<span id="page-9-3"></span>Image /page/9/Figure/9 description: The image is a line graph titled "Final Loss". The x-axis represents iterations, and the y-axis represents loss values ranging from 0.4 to 1.2. Two lines are plotted: one orange line labeled "Ours" and one blue line labeled "MTT". Both lines show a fluctuating trend, with the orange line generally staying below the blue line, indicating lower loss for "Ours" compared to "MTT" over the iterations.

Fig. 7: Comparison between proposed methods and original MTT. Our methods can generate a much lower final loss.

<span id="page-9-4"></span>Image /page/9/Figure/11 description: The image displays two line graphs side-by-side, both titled "Learning Rate". The left graph, labeled "(a) Smooth expert trajectory", shows a learning rate that starts near 0, rapidly increases to approximately 0.03 within the first 1000 iterations, and then fluctuates slightly around 0.035 until 5000 iterations. The right graph, labeled "(b) Non-smooth expert trajectory", shows a learning rate that starts near 0, spikes to about 0.008 in the first few iterations, then fluctuates erratically between 0.002 and 0.006 until around 1500 iterations, after which it drops to 0 and remains at 0 for the rest of the 5000 iterations.

Fig. 8: Learning curve for student model learning rate. When applying a nonsmooth expert trajectory, the output of the learning rate may encounter NaN which will lead to the collapse of the training process.

#### *D. Training Burden*

While we have introduced additional plug-in modules into the distillation process, the training time for each image in our approach remains comparable to that of the original MTT. It only requires a slight increase in time for each iteration, as illustrated in Table [VI.](#page-9-1)

However, notably, our method exhibits faster convergence and superior results. As depicted in Fig. [6,](#page-9-2) our approach on CIFAR100 essentially attains the final performance of the original MTT after just 500 iterations, while the original MTT requires 5000 iterations to achieve the same level of performance. Moreover, our method consistently demonstrates improvement. For example on TinyImageNet, our approach's performance continues to ascend, in contrast to MTT, which essentially plateaus after 3000 iterations.

<span id="page-10-1"></span>Image /page/10/Figure/0 description: This image contains two plots comparing the performance of different optimization methods (NO\_MOM, MOM, Flat\_MOM, ADAM) on CIFAR-100 and Tiny ImageNet datasets. Both plots show 'Expert Trajectory Epoch' on the x-axis and have two y-axes. The left y-axis represents ||θt - θt+1||^2, and the right y-axis represents Accuracy. Plot (a) for CIFAR-100 shows that MOM and ADAM achieve high accuracy (around 0.55-0.6) by the end of the training, while NO\_MOM and Flat\_MOM have lower accuracies. The ||θt - θt+1||^2 values are generally higher for MOM and ADAM in the early stages and then decrease. Plot (b) for Tiny ImageNet shows similar trends, with MOM and ADAM achieving higher accuracies (around 0.35-0.4) compared to NO\_MOM and Flat\_MOM. The ||θt - θt+1||^2 values also show initial increases followed by decreases for MOM and ADAM.

Fig. 9:  $\|\theta_t - \theta_{t+1}\|_2^2$  refers to the change of the model weights on two consecutive iterations, shown by dash curve. Correspondingly, the solid curve refers to the metric of evaluation accuracy. NO\_MOM refers to SGD without momentum. MOM means using SGD with momentum alone. Flat\_MOM denotes smooth expert trajectories that apply gradient penalty and clipped loss under the usage of SGD with momentum. ADAM means using ADAM as an optimizer alone. We view the red curve as a much smoother and higher-quality expert trajectory. The  $\|\theta_t - \theta_{t+1}\|_2^2$  of Adam is so huge that it cannot fully appear in both CIFAR-100 and Tiny ImageNet.

<span id="page-10-2"></span>

| Momentum     | Gradient<br>Penalty | Clipped<br>Loss | Avg_Var $\downarrow$ | Acc. (Expert) $\uparrow$ | Acc. (Distill) $\uparrow$ | Momentum     | Gradient<br>Penalty | Clipped<br>Loss | Avg_Var $\downarrow$ | Acc. (Expert) $\uparrow$ | Acc. (Distill) $\uparrow$ |
|--------------|---------------------|-----------------|----------------------|--------------------------|---------------------------|--------------|---------------------|-----------------|----------------------|--------------------------|---------------------------|
| $\times$     | $\times$            | $\times$        | 0.1726               | 48.6                     | 39.7                      | $\times$     | $\times$            | $\times$        | 0.0705               | 25.8                     | 8.8                       |
| $\checkmark$ | $\times$            | $\times$        | 7.9611 (x46)         | 57.1 (+8.5)              | 18.8 (-20.9)              | $\checkmark$ | $\times$            | $\times$        | 2.2950 (x33)         | 39.4 (+13.6)             | 1.8 (-7.0)                |
| $\checkmark$ | $\checkmark$        | $\times$        | 0.9331 (x5.4)        | 54.1 (+5.5)              | 41.7 (+2.0)               | $\checkmark$ | $\checkmark$        | $\times$        | 1.7358 (x24)         | 39.0 (+13.2)             | 10.0 (+1.2)               |
| $\checkmark$ | $\checkmark$        | $\checkmark$    | <b>0.5899</b> (x3.4) | <b>54.4</b> (+5.8)       | <b>42.0</b> (+2.3)        | $\checkmark$ | $\checkmark$        | $\checkmark$    | <b>1.3066</b> (x18)  | <b>39.5</b> (+13.7)      | <b>10.8</b> (+2.0)        |

(a) CIFAR-100

(b) Tiny ImageNet

TABLE VII: Comparison between different buffer generation methods using SGD as base optimizer.

#### *E. Examples of Training Instability*

The sources of training instability can be attributed to two main factors. Firstly, the original MTT itself is prone to encountering sudden spikes in gradients, which can lead to training collapse. However, by incorporating the proposed methods, our training process becomes more stable. As illustrated in Fig. [7,](#page-9-3) our final loss is substantially lower than that of the original MTT, indicating a more effective transfer of expert network knowledge into the target compressed dataset.

The second source of instability stems from the parameter variations in the expert model trajectories. We utilize simple momentum-based SGD as the optimizer for training the expert model. Subsequently, we compare a crucial output during each iteration: the learnable variable "learning rate". From Fig. [8,](#page-9-4) it becomes evident that as the expert trajectories become less smooth, the learnable quantity experiences huge fluctuations. At around 1800 iterations, it even reaches NaN values due to sudden gradient explosions. As the previous theoretical analysis Sec. [IV-B2](#page-3-1) indicated, the occurrence of alignment failure is due to the continuous amplification of the accumulated gradient by the momentum. This also emphasizes the importance of generating smooth, slowly changing, and high-quality expert trajectories.

# *F. Analysis*

<span id="page-10-0"></span>*1) The Impact of Expert Trajectory Smoothness:* Fig. [9](#page-10-1) and Table [VII](#page-10-2) elucidate why previous works have invariably opted for naive SGD as the optimizer. This choice of SGD strikes an unavoidable trade-off between the performance of expert model and outcome of the distilled dataset. For instance, as shown in the Table [VIIa,](#page-10-2) the expert model alone achieves a modest score of 48.6%, whereas distillation yields a respectable 39.7%. However, despite adopting SGD with momentum or Adam enhancing expert performance (shown in blue and yellow solid lines), it leads to the variation of parameters changing so fast (shown in blue and yellow dash lines) that surpasses the limitation for distillation. Finally, it will cause significant declines in distillation results, even gradient explosions and training collapses, especially for Adam.

The essence of our proposed method for generating smooth expert trajectories lies in constraining the speed of parameter variation. The ideal expert trajectory exhibits slow parameter variations with consistent performance improvements along the iterations. To quantify smoothness, we employ a metric called  $Avg\_Var$  to measure the average weight variation magnitude between two iterations along the whole training process:

$$
Avg\_Var = \mathbb{E}_t \left[ \left\| \theta_t - \theta_{t+1} \right\|_2^2 \right]
$$

Fig. [9](#page-10-1) shows  $\|\theta_t - \theta_{t+1}\|_2^2$  of each epoch and Table [VII](#page-10-2) demonstrates the average result. Through employing gradient penalty and loss clipping, we achieve significant improvements in both expert performance (an increase of 5.8% and 13.7%) and distilled dataset performance (an increase of 2.3% and 2.0%) while only increasing  $Avg\_Var$  by a factor of 3.4 and 18 (compared to  $46\times$  and  $33\times$  for direct momentum addition, which is just a small increment).

<span id="page-11-3"></span>Image /page/11/Figure/0 description: The image displays five scatter plots arranged horizontally, each representing a different class (Class 0 through Class 4). Each plot shows data points colored in shades of blue and purple, with some points marked by red stars and black crosses. The x-axis ranges from approximately -20 to 30, and the y-axis ranges from approximately -20 to 20 in all plots. The distribution of points varies across the classes, with some overlap and some distinct clusters. The red stars and black crosses appear to represent specific points of interest within each class's distribution.

<span id="page-11-2"></span>Fig. 10: Example clustering and sub-cluster centre results.  $\star$  denotes the representative initialization samples while  $\times$  means the random initialization samples.

| <b>Methods</b>                                                                          | Acc. Distill (Gain)        |
|-----------------------------------------------------------------------------------------|----------------------------|
| Base                                                                                    | 42.0                       |
| Balance Stochasticity:<br>+ Representative Initialization<br>+ Balanced Inner-loop Loss | 42.5 (+0.5)<br>42.9 (+0.4) |
| Alleviate Errors:<br>+ Intermediate Matching Loss<br>+ Weight Perturbation              | 43.6 (+0.7)<br>44.4 (+0.8) |

TABLE VIII: We use the distilled results obtained by applying smooth expert trajectory as the "Base". Following that, we conduct two parts ablation studies (stochasticity and errors) on the CIFAR-100 dataset under IPC=10.

<span id="page-11-4"></span>Image /page/11/Figure/4 description: A horizontal bar chart displays performance metrics for different strategies, with a dashed red line indicating a baseline at approximately 42. The chart is divided into three main sections: 'Weight Perturb', 'Combined Strategy', and 'Balanced Strategy'. Each section contains two bars, one dark blue and one light blue, representing different conditions. For 'Weight Perturb', the dark blue bar ('w/ Dropout') extends to about 42.5, while the light blue bar ('w/o Dropout') reaches approximately 41.8. Under 'Combined Strategy', the dark blue bar ('Equal Scale') ends around 42.2, and the light blue bar ('Varied Scale') is at about 41.5. The 'Balanced Strategy' section shows a dark blue bar ('Balanced Loss') reaching roughly 42.5, and a light blue bar ('Range Selection') ending at approximately 39.2. The x-axis is labeled with numbers from 35 to 45, indicating the performance metric.

Fig. 11: Results of ablation study on weight perturbation, intermediate matching loss and balanced strategy.

<span id="page-11-1"></span>*2) The Impact of Balance Strategy:* As demonstrated in Table [VIII,](#page-11-2) we conduct ablation experiments on each proposed module based on the usage of smooth expert trajectory. In the "Balance Stochasticity" part, the act of selecting representative samples for initialization resulted in an incremental gain of 0.5%. In order to visualize the effectiveness of the selection strategy, We randomly choose five classes and apply PCA [\[50\]](#page-12-44) to reduce the high-dimensional features to two dimensions. As we can see from Fig. [10,](#page-11-3) compared to random initialization, our proposed method avoids introducing huge bias coming from outliers. Additionally, the distribution of distilled samples is more uniform, and there is no over-concentration in a specific area.

Moreover, the application of a balanced inner-loop loss leads to a further enhancement, yielding an improvement of 0.4%. As mentioned in Sec. [IV-C2,](#page-5-8) we compared another method: random initialization of  $t$  within a certain range. We set the range to be within 5 when selecting the next expert starting point. From Fig. [11,](#page-11-4) the experiments indicate that initialization within a range not only lacks a positive effect but, conversely, introduces a potential erroneous inductive bias that results in a decline in distillation outcomes.

*3) The Impact of Accumulated Error:* To explore the contributions of the two error reduction methods, we conduct ablation experiments as depicted in Table [VIII.](#page-11-2) In the "Alleviate Errors" part, the experiments indicate that using weight perturbation leads to the most improvement  $(+0.8\%)$ in distilled dataset performance. However, when applying Dropout after weight perturbation shown in Fig. [11,](#page-11-4) which introduces another type of drop noise, we have not observed any improvement in the results.

Besides, intermediate matching loss contributes to an increase of 0.7% to the final outcome, which elucidates that extended-range interactions indeed may lead to a divergence in the optimization direction within the inner loop. Reducing this part of the accumulated error is equally significant. We also experiment with two different strategies for combining multiple intermediate matching losses. As illustrated in Fig. [11,](#page-11-4) we observe that a straightforward approach, where the same scale of  $\beta$  coefficient is used, yielded superior results.

#### VI. CONCLUSION

In this paper, we propose a novel dataset distillation strategy AST to address the challenges associated with the mutual influence between expert and student models, sensitivity to stochastic variables, and accumulated errors. Building upon this, we argue the significant effect of trajectory smoothness and propose clipping loss and gradient penalty to smooth the expert trajectories under a more potent optimizer. The improved trajectories pave the way for optimizing the parameter alignment process from two perspectives. To temper the influence of two stochastic variables, we propose using representative initial samples for  $\mathcal{D}_{syn}$  and replacing normal cross-entropy loss for balanced inner-loop loss. Besides, we propose intermediate matching loss and weight perturbation strategies to alleviate errors stemming from long-range inner steps  $\mathcal N$  and discrepancies between distillation and evaluation. Furthermore, our methods are designed to be easily implemented and plugged in, which broadens the scope of applicability. We hope AST can pave the way for future work on dataset distillation.

#### **REFERENCES**

<span id="page-11-0"></span>[1] O. Sener and S. Savarese, "Active learning for convolutional neural networks: A core-set approach," *arXiv preprint arXiv:1708.00489*, 2017.

- <span id="page-12-4"></span>[2] R. Aljundi, M. Lin, B. Goujaud, and Y. Bengio, "Gradient based sample selection for online continual learning," *Advances in Neural Information Processing Systems*, vol. 32, 2019.
- <span id="page-12-0"></span>[3] P. K. Agarwal, S. Har-Peled, and K. R. Varadarajan, "Approximating extent measures of points," *Journal of the ACM (JACM)*, vol. 51, no. 4, pp. 606–635, 2004.
- <span id="page-12-1"></span>[4] D. A. Cohn, Z. Ghahramani, and M. I. Jordan, "Active learning with statistical models," *Journal of artificial intelligence research*, vol. 4, pp. 129–145, 1996.
- [5] P. F. Felzenszwalb, R. B. Girshick, D. McAllester, and D. Ramanan, "Object detection with discriminatively trained part-based models," *IEEE transactions on pattern analysis and machine intelligence*, vol. 32, no. 9, pp. 1627–1645, 2010.
- <span id="page-12-2"></span>[6] A. Lapedriza, H. Pirsiavash, Z. Bylinskii, and A. Torralba, "Are all training examples equally valuable?" *arXiv preprint arXiv:1311.6510*, 2013.
- <span id="page-12-3"></span>[7] J. A. Olvera-López, J. A. Carrasco-Ochoa, J. Martínez-Trinidad, and J. Kittler, "A review of instance selection methods," *Artificial Intelligence Review*, vol. 34, no. 2, pp. 133–143, 2010.
- <span id="page-12-5"></span>Y. Chen, M. Welling, and A. Smola, "Super-samples from kernel herding," *arXiv preprint arXiv:1203.3472*, 2012.
- <span id="page-12-6"></span>[9] M. Toneva, A. Sordoni, R. T. d. Combes, A. Trischler, Y. Bengio, and G. J. Gordon, "An empirical study of example forgetting during deep neural network learning," in *ICLR*, 2019.
- <span id="page-12-7"></span>[10] Z. Chen, J. Geng, H. Woisetschlaeger, S. Schimmler, R. Mayer, and C. Rong, "A comprehensive study on dataset distillation: Performance, privacy, robustness and fairness," *arXiv preprint arXiv:2305.03355*, 2023.
- <span id="page-12-20"></span>[11] N. Sachdeva and J. McAuley, "Data distillation: A survey," *arXiv preprint arXiv:2301.04272*, 2023.
- [12] J. Cui, R. Wang, S. Si, and C.-J. Hsieh, "Dc-bench: Dataset condensation benchmark," *Advances in Neural Information Processing Systems*, vol. 35, pp. 810–822, 2022.
- <span id="page-12-8"></span>[13] R. Yu, S. Liu, and X. Wang, "Dataset distillation: A comprehensive review," *arXiv preprint arXiv:2301.07014*, 2023.
- <span id="page-12-9"></span>[14] R. Pi, W. Zhang, Y. Xie, J. Gao, X. Wang, S. Kim, and Q. Chen, "DYNAFED: Tackling client data heterogeneity with global dynamics," *arXiv preprint arXiv:2211.10878*, 2022.
- [15] P. Liu, X. Yu, and J. T. Zhou, "Meta knowledge condensation for federated learning," in *ICLR*, 2023.
- <span id="page-12-10"></span>[16] Y. Zhou, G. Pu, X. Ma, X. Li, and D. Wu, "Distilled one-shot federated learning," *arXiv preprint arXiv:2009.07999*, 2020.
- <span id="page-12-11"></span>[17] D. Medvedev and A. D'yakonov, "Learning to generate synthetic training data using gradient matching and implicit differentiation," in *Proceedings of the International Conference on Analysis of Images, Social Networks and Texts*, 2021, pp. 138–150.
- <span id="page-12-12"></span>[18] F. P. Such, A. Rawal, J. Lehman, K. Stanley, and J. Clune, "Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data," in *International Conference on Machine Learning*. PMLR, 2020, pp. 9206–9216.
- <span id="page-12-13"></span>[19] N. Loo, R. Hasani, M. Lechner, and D. Rus, "Dataset distillation fixes dataset reconstruction attacks," *arXiv preprint arXiv:2302.01428*, 2023.
- [20] Y. Liu, Z. Li, M. Backes, Y. Shen, and Y. Zhang, "Backdoor attacks against dataset distillation," in *Proceedings of the Network and Distributed System Security Symposium*, 2023.
- <span id="page-12-14"></span>[21] D. Chen, R. Kerkouche, and M. Fritz, "Private set generation with discriminative information," *Advances in Neural Information Processing Systems*, vol. 35, pp. 14 678–14 690, 2022.
- <span id="page-12-15"></span>[22] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 4750–4759.
- <span id="page-12-19"></span>[23] J. Du, Y. Jiang, V. T. Tan, J. T. Zhou, and H. Li, "Minimizing the accumulated trajectory error to improve dataset distillation," *arXiv preprint arXiv:2211.11004*, 2022.
- <span id="page-12-16"></span>[24] J. Cui, R. Wang, S. Si, and C.-J. Hsieh, "Scaling up dataset distillation to imagenet-1k with constant memory," *arXiv preprint arXiv:2211.10586*, 2022.
- <span id="page-12-17"></span>[25] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-12-29"></span>[26] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching." in *ICLR*, 2021.
- <span id="page-12-18"></span>[27] B. Zhao and H. Bilen, "Dataset condensation with distribution matching," in *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, 2023.

- <span id="page-12-21"></span>[28] C. Finn, P. Abbeel, and S. Levine, "Model-agnostic meta-learning for fast adaptation of deep networks," in *International conference on machine learning*. PMLR, 2017, pp. 1126–1135.
- <span id="page-12-22"></span>[29] O. Bohdal, Y. Yang, and T. Hospedales, "Flexible dataset distillation: Learn labels instead of images," *arXiv preprint arXiv:2006.08572*, 2020.
- <span id="page-12-23"></span>[30] I. Sucholutsky and M. Schonlau, "Soft-label dataset distillation and text dataset distillation," in *International Joint Conference on Neural Networks*. IEEE, 2021, pp. 1–8.
- <span id="page-12-24"></span>[31] T. Nguyen, Z. Chen, and J. Lee, "Dataset meta-learning from kernel ridge-regression," in *ICLR*, 2021. [Online]. Available: [https:](https://openreview.net/forum?id=l-PrrQrK0QR) [//openreview.net/forum?id=l-PrrQrK0QR](https://openreview.net/forum?id=l-PrrQrK0QR)
- <span id="page-12-25"></span>[32] T. Nguyen, R. Novak, L. Xiao, and J. Lee, "Dataset distillation with infinitely wide convolutional networks," *Advances in Neural Information Processing Systems*, vol. 34, pp. 5186–5198, 2021.
- <span id="page-12-26"></span>[33] Y. Zhou, E. Nezhadarya, and J. Ba, "Dataset distillation using neural feature regression," *arXiv preprint arXiv:2206.00719*, 2022.
- <span id="page-12-27"></span>[34] S. Liu, K. Wang, X. Yang, J. Ye, and X. Wang, "Dataset distillation via factorization," *Advances in Neural Information Processing Systems*, 2022.
- <span id="page-12-28"></span>[35] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Generalizing dataset distillation via deep generative prior," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2023, pp. 3739–3748.
- <span id="page-12-30"></span>[36] B. Zhao and H. Bilen, "Dataset condensation with differentiable siamese augmentation," in *International Conference on Machine Learning*. PMLR, 2021, pp. 12 674–12 685.
- <span id="page-12-31"></span>[37] S. Lee, S. Chun, S. Jung, S. Yun, and S. Yoon, "Dataset condensation with contrastive signals," in *International Conference on Machine Learning*. PMLR, 2022, pp. 12 352–12 364.
- <span id="page-12-32"></span>[38] J.-H. Kim, J. Kim, S. J. Oh, S. Yun, H. Song, J. Jeong, J.-W. Ha, and H. O. Song, "Dataset condensation via efficient synthetic-data parameterization," in *International Conference on Machine Learning*. PMLR, 2022, pp. 11 102–11 118.
- <span id="page-12-33"></span>[39] K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You, "Cafe: Learning to condense dataset by aligning features," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 12 196–12 205.
- <span id="page-12-34"></span>[40] B. Zhao and H. Bilen, "Synthesizing informative training samples with gan," *NeurIPS 2022 Workshop on Synthetic Data for Empowering ML Research*, 2022.
- <span id="page-12-35"></span>[41] T. Huang, S. You, F. Wang, C. Qian, and C. Xu, "Knowledge distillation from a stronger teacher," *Advances in Neural Information Processing Systems*, vol. 35, pp. 33 716–33 727, 2022.
- <span id="page-12-36"></span>[42] M. Yuan, B. Lang, and F. Quan, "Student-friendly knowledge distillation," *arXiv preprint arXiv:2305.10893*, 2023.
- <span id="page-12-37"></span>[43] S. Shao, H. Chen, Z. Huang, L. Gong, S. Wang, and X. Wu, "Teaching what you should teach: A data-based distillation method," *International Joint Conference on Artificial Intelligence*, 2023.
- <span id="page-12-38"></span>[44] G. Goh, "Why momentum really works," *Distill*, 2017. [Online]. Available: <http://distill.pub/2017/momentum>
- <span id="page-12-39"></span>[45] M. Arjovsky, S. Chintala, and L. Bottou, "Wasserstein generative adversarial networks," in *International conference on machine learning*. PMLR, 2017, pp. 214–223.
- <span id="page-12-40"></span>[46] I. Gulrajani, F. Ahmed, M. Arjovsky, V. Dumoulin, and A. C. Courville, "Improved training of wasserstein gans," *Advances in Neural Information Processing Systems*, vol. 30, 2017.
- <span id="page-12-42"></span>[47] A. Krizhevsky, G. Hinton *et al.*, "Learning multiple layers of features from tiny images," 2009.
- <span id="page-12-43"></span>[48] Y. Le and X. Yang, "Tiny imagenet visual recognition challenge," *CS 231N*, vol. 7, no. 7, p. 3, 2015.
- <span id="page-12-41"></span>[49] S. Gidaris and N. Komodakis, "Dynamic few-shot visual learning without forgetting," in *Proceedings of the IEEE conference on computer vision and pattern recognition*, 2018, pp. 4367–4375.
- <span id="page-12-44"></span>[50] S. Wold, K. Esbensen, and P. Geladi, "Principal component analysis," *Chemometrics and intelligent laboratory systems*, vol. 2, no. 1-3, pp. 37–52, 1987.
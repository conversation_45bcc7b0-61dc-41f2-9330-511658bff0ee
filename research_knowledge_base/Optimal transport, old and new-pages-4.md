# Qualitative description of optimal transport

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.

The first part of this course is devoted to the description and characterization of optimal transport under certain regularity assumptions on the measures and the cost function.

As a start, some general theorems about optimal transport plans are established in Chapters 4 and 5, in particular the Kanto<PERSON>ich duality theorem. The emphasis is on c-cyclically monotone maps, both in the statements and in the proofs. The assumptions on the cost function and the spaces will be very general.

From the Monge–<PERSON>nto<PERSON>ich problem one can derive natural distance functions on spaces of probability measures, by choosing the cost function as a power of the distance. The main properties of these distances are established in Chapter 6.

In Chapter 7 a time-dependent version of the Monge–<PERSON> problem is investigated, which leads to an interpolation procedure between probability measures, called displacement interpolation. The natural assumption is that the cost function derives from a Lagrangian action, in the sense of classical mechanics; still (almost) no smoothness is required at that level. In Chapter 8 I shall make further assumptions of smoothness and convexity, and recover some regularity properties of the displacement interpolant by a strategy due to <PERSON><PERSON>.

Then in Chapters 9 and 10 it is shown how to establish the existence of deterministic optimal couplings, and characterize the associated transport maps, again under adequate regularity and convexity assumptions. The Change of variables Formula is considered in Chapter 11. Finally, in Chapter 12 I shall discuss the regularity of the transport map, which in general is not smooth.

The main results of this part are synthetized and summarized in Chapter 13. A good understanding of this chapter is sufficient to go through Part II of this course.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.

# Basic properties

## Existence

The first good thing about optimal couplings is that they exist:

Theorem 4.1 (Existence of an optimal coupling). Let  $(\mathcal{X}, \mu)$ and  $(\mathcal{Y}, \nu)$  be two Polish probability spaces; let  $a : \mathcal{X} \to \mathbb{R} \cup \{-\infty\}$ and  $b: \mathcal{Y} \to \mathbb{R} \cup \{-\infty\}$  be two upper semicontinuous functions such that  $a \in L^1(\mu)$ ,  $b \in L^1(\nu)$ . Let  $c : \mathcal{X} \times \mathcal{Y} \to \mathbb{R} \cup \{+\infty\}$  be a lower semicontinuous cost function, such that  $c(x,y) \geq a(x) + b(y)$  for all x,y. Then there is a coupling of  $(\mu, \nu)$  which minimizes the total cost  $\mathbb{E} c(X, Y)$  among all possible couplings  $(X, Y)$ .

**Remark 4.2.** The lower bound assumption on  $c$  guarantees that the expected cost  $\mathbb{E} c(X, Y)$  is well-defined in  $\mathbb{R} \cup \{+\infty\}$ . In most cases of applications — but not all — one may choose  $a = 0, b = 0$ .

The proof relies on basic variational arguments involving the topology of weak convergence (i.e. imposed by bounded continuous test functions). There are two key properties to check: (a) lower semicontinuity, (b) compactness. These issues are taken care of respectively in Lemmas 4.3 and 4.4 below, which will be used again in the sequel. Before going on, I recall **Prokhorov's theorem:** If  $X$  is a Polish space, then a set  $\mathcal{P} \subset P(\mathcal{X})$  is precompact for the weak topology if and only if it is tight, *i.e.* for any  $\varepsilon > 0$  there is a compact set  $K_{\varepsilon}$  such that  $\mu[\mathcal{X} \setminus K_{\varepsilon}] \leq \varepsilon$ for all  $\mu \in \mathcal{P}$ .

Lemma 4.3 (Lower semicontinuity of the cost functional). Let X and Y be two Polish spaces, and  $c: X \times Y \to \mathbb{R} \cup \{+\infty\}$  a lower semicontinuous cost function. Let  $h : \mathcal{X} \times \mathcal{Y} \to \mathbb{R} \cup \{-\infty\}$  be an upper semicontinuous function such that  $c \geq h$ . Let  $(\pi_k)_{k \in \mathbb{N}}$  be a sequence of probability measures on  $\mathcal{X}\times\mathcal{Y}$ , converging weakly to some  $\pi \in P(\mathcal{X}\times\mathcal{Y})$ , in such a way that  $h \in L^1(\pi_k)$ ,  $h \in L^1(\pi)$ , and

$$
\int_{\mathcal{X}\times\mathcal{Y}} h \, d\pi_k \xrightarrow[k \to \infty]{} \int_{\mathcal{X}\times\mathcal{Y}} h \, d\pi.
$$

Then

$$
\int_{\mathcal{X}\times\mathcal{Y}} c\,d\pi \le \liminf_{k\to\infty} \int_{\mathcal{X}\times\mathcal{Y}} c\,d\pi_k.
$$

In particular, if c is nonnegative, then  $F : \pi \to \int c d\pi$  is lower semicontinuous on  $P(X \times Y)$ , equipped with the topology of weak convergence.

Lemma 4.4 (Tightness of transference plans). Let X and Y be two Polish spaces. Let  $P \subset P(X)$  and  $\mathcal{Q} \subset P(Y)$  be tight subsets of  $P(X)$  and  $P(Y)$  respectively. Then the set  $\Pi(\mathcal{P}, \mathcal{Q})$  of all transference plans whose marginals lie in  $P$  and  $Q$  respectively, is itself tight in  $P(X \times Y)$ .

*Proof of Lemma 4.3.* Replacing c by  $c - h$ , we may assume that c is a nonnegative lower semicontinuous function. Then  $c$  can be written as the pointwise limit of a nondecreasing family  $(c_{\ell})_{\ell \in \mathbb{N}}$  of continuous real-valued functions. By monotone convergence,

$$
\int c d\pi = \lim_{\ell \to \infty} \int c_{\ell} d\pi = \lim_{\ell \to \infty} \lim_{k \to \infty} \int c_{\ell} d\pi_k \le \liminf_{k \to \infty} \int c d\pi_k.
$$

*Proof of Lemma 4.4.* Let  $\mu \in \mathcal{P}$ ,  $\nu \in \mathcal{Q}$ , and  $\pi \in \Pi(\mu, \nu)$ . By assumption, for any  $\varepsilon > 0$  there is a compact set  $K_{\varepsilon} \subset \mathcal{X}$ , independent of the choice of  $\mu$  in  $\mathcal{P}$ , such that  $\mu[\mathcal{X} \setminus K_{\varepsilon}] \leq \varepsilon$ ; and similarly there is a compact set  $L_{\varepsilon} \subset \mathcal{Y}$ , independent of the choice of  $\nu$  in  $\mathcal{Q}$ , such that  $\nu[\mathcal{Y} \setminus L_{\varepsilon}] \leq \varepsilon$ . Then for any coupling  $(X, Y)$  of  $(\mu, \nu)$ ,

$$
\mathbb{P}\left[ (X,Y) \notin K_{\varepsilon} \times L_{\varepsilon} \right] \leq \mathbb{P}\left[ X \notin K_{\varepsilon} \right] + \mathbb{P}\left[ Y \notin L_{\varepsilon} \right] \leq 2 \varepsilon.
$$

The desired result follows since this bound is independent of the coupling, and  $K_{\varepsilon} \times L_{\varepsilon}$  is compact in  $\mathcal{X} \times \mathcal{Y}$ . □

*Proof of Theorem 4.1.* Since X is Polish,  $\{\mu\}$  is tight in  $P(\mathcal{X})$ ; similarly,  $\{\nu\}$  is tight in  $P(\mathcal{Y})$ . By Lemma 4.4,  $\Pi(\mu, \nu)$  is tight in  $P(\mathcal{X} \times \mathcal{Y})$ , and by Prokhorov's theorem this set has a compact closure. By passing to the limit in the equation for marginals, we see that  $\Pi(\mu,\nu)$  is closed, so it is in fact compact.

Then let  $(\pi_k)_{k\in\mathbb{N}}$  be a sequence of probability measures on  $\mathcal{X}\times\mathcal{Y}$ , such that  $\int c d\pi_k$  converges to the infimum transport cost. Extracting a subsequence if necessary, we may assume that  $\pi_k$  converges to some  $\pi \in \Pi(\mu, \nu)$ . The function  $h : (x, y) \longmapsto a(x) + b(y)$  lies in  $L^1(\pi_k)$ and in  $L^1(\pi)$ , and  $c \ge h$  by assumption; moreover,  $\int h d\pi_k = \int h d\pi =$ <br> $\int a du + \int b dv$ : so Lemma 4.3 implies  $a d\mu + \int b d\nu$ ; so Lemma 4.3 implies

$$
\int c d\pi \le \liminf_{k \to \infty} \int c d\pi_k.
$$

Thus  $\pi$  is minimizing. □

Remark 4.5. This existence theorem does not imply that the optimal cost is finite. It might be that all transport plans lead to an infinite total cost, i.e.  $\int c d\pi = +\infty$  for all  $\pi \in \Pi(\mu, \nu)$ . A simple condition to rule out this annoying possibility is

$$
\int c(x,y) \, d\mu(x) \, d\nu(y) < +\infty,
$$

which guarantees that at least the independent coupling has finite total cost. In the sequel, I shall sometimes make the stronger assumption

$$
c(x,y) \le c_{\mathcal{X}}(x) + c_{\mathcal{Y}}(y), \qquad (c_{\mathcal{X}}, c_{\mathcal{Y}}) \in L^1(\mu) \times L^1(\nu),
$$

which implies that *any* coupling has finite total cost, and has other nice consequences (see e.g. Theorem 5.10).

### Restriction property

The second good thing about optimal couplings is that any sub-coupling is still optimal. In words: If you have an optimal transport plan, then any induced sub-plan (transferring part of the initial mass to part of the final mass) has to be optimal too — otherwise you would be able to lower the cost of the sub-plan, and as a consequence the cost of the whole plan. This is the content of the next theorem.

Theorem 4.6 (Optimality is inherited by restriction). Let  $(\mathcal{X}, \mu)$ and  $(\mathcal{Y}, \nu)$  be two Polish spaces,  $a \in L^1(\mu)$ ,  $b \in L^1(\nu)$ , let  $c : \mathcal{X} \times \mathcal{Y} \rightarrow$  $\mathbb{R} \cup \{+\infty\}$  be a measurable cost function such that  $c(x,y) \geq a(x)+b(y)$ for all x, y; and let  $C(\mu, \nu)$  be the optimal transport cost from  $\mu$  to  $\nu$ . Assume that  $C(\mu,\nu) < +\infty$  and let  $\pi \in \Pi(\mu,\nu)$  be an optimal transport plan. Let  $\widetilde{\pi}$  be a nonnegative measure on  $\mathcal{X} \times \mathcal{Y}$ , such that  $\widetilde{\pi} \leq \pi$ and  $\widetilde{\pi}[\mathcal{X} \times \mathcal{Y}] > 0$ . Then the probability measure

$$
\pi':=\frac{\widetilde{\pi}}{\widetilde{\pi}[\mathcal{X}\times\mathcal{Y}]}
$$

is an optimal transference plan between its marginals  $\mu'$  and  $\nu'$ .

Moreover, if  $\pi$  is the unique optimal transference plan between  $\mu$ and  $\nu$ , then also  $\pi'$  is the unique optimal transference plan between  $\mu'$ and  $\nu'$ .

**Example 4.7.** If  $(X, Y)$  is an optimal coupling of  $(\mu, \nu)$ , and  $\mathcal{Z} \subset \mathcal{X} \times \mathcal{Y}$ is such that  $\mathbb{P}[(X,Y) \in \mathcal{Z}] > 0$ , then the pair  $(X,Y)$ , conditioned to lie in Z, is an optimal coupling of  $(\mu', \nu')$ , where  $\mu'$  is the law of X conditioned by the event " $(X, Y) \in \mathcal{Z}$ ", and  $\nu'$  is the law of Y conditioned by the same event.

*Proof of Theorem 4.6.* Assume that  $\pi'$  is not optimal; then there exists  $\pi''$  such that

$$
(\text{proj}_{\mathcal{X}})_{\#} \pi'' = (\text{proj}_{\mathcal{X}})_{\#} \pi' = \mu', \qquad (\text{proj}_{\mathcal{Y}})_{\#} \pi'' = (\text{proj}_{\mathcal{Y}})_{\#} \pi' = \nu',
$$
\n
$$
\tag{4.1}
$$

yet

$$
\int c(x, y) d\pi''(x, y) < \int c(x, y) d\pi'(x, y).
$$
 (4.2)

Then consider

$$
\widehat{\pi} := (\pi - \widetilde{\pi}) + \widetilde{Z}\pi'', \tag{4.3}
$$

where  $\mathcal{Z} = \tilde{\pi}[\mathcal{X} \times \mathcal{Y}] > 0$ . Clearly,  $\hat{\pi}$  is a nonnegative measure. On the other hand, it can be written as

$$
\widehat{\pi} = \pi + \widetilde{Z}(\pi'' - \pi');
$$

then (4.1) shows that  $\hat{\pi}$  has the same marginals as  $\pi$ , while (4.2) implies that it has a lower transport cost than  $\pi$ . (Here I use the fact that the total cost is finite.) This contradicts the optimality of  $\pi$ . The conclusion is that  $\pi'$  is in fact optimal.

It remains to prove the last statement of Theorem 4.6. Assume that  $\pi$  is the unique optimal transference plan between  $\mu$  and  $\nu$ ; and let  $\pi''$ be any optimal transference plan between  $\mu'$  and  $\nu'$ . Define again  $\hat{\pi}$ by (4.3). Then  $\hat{\pi}$  has the same cost as  $\pi$ , so  $\hat{\pi} = \pi$ , which implies that  $\tilde{\pi} = \tilde{Z}\pi''$ , i.e.  $\pi'' = \pi'$ .  $\widetilde{\pi} = Z\pi''$ , i.e.  $\pi'' = \pi'$ . ⊔□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□□

# Convexity properties

The following estimates are of constant use:

Theorem 4.8 (Convexity of the optimal cost). Let X and Y be two Polish spaces, let  $c : \mathcal{X} \times \mathcal{Y} \to \mathbb{R} \cup \{+\infty\}$  be a lower semicontinuous function, and let  $C$  be the associated optimal transport cost functional on  $P(\mathcal{X}) \times P(\mathcal{Y})$ . Let  $(\Theta, \lambda)$  be a probability space, and let  $\mu_{\theta}$ ,  $\nu_{\theta}$  be two measurable functions defined on  $\Theta$ , with values in  $P(\mathcal{X})$  and  $P(\mathcal{Y})$  respectively. Assume that  $c(x, y) \ge a(x) + b(y)$ , where  $a \in L^1(d\mu_\theta d\lambda(\theta))$ ,  $b \in L^1(d\nu_\theta \, d\lambda(\theta)).$  Then

$$
C\left(\int_{\Theta} \mu_{\theta} \lambda(d\theta), \int_{\Theta} \nu_{\theta} \lambda(d\theta)\right) \leq \left(\int_{\Theta} C(\mu_{\theta}, \nu_{\theta}) \lambda(d\theta)\right).
$$

Proof of Theorem 4.8. First notice that  $a \in L^1(\mu_\theta)$ ,  $b \in L^1(\nu_\theta)$  for  $\lambda$ almost all values of  $\theta$ . For each such  $\theta$ , Theorem 4.1 guarantees the existence of an optimal transport plan  $\pi_{\theta} \in \Pi(\mu_{\theta}, \nu_{\theta})$ , for the cost c. Then  $\pi := \int \pi_{\theta} \lambda(d\theta)$  has marginals  $\mu := \int \mu_{\theta} \lambda(d\theta)$  and  $\nu := \int \nu_{\theta} \lambda(d\theta)$ . Admitting temporarily Corollary 5.22, we may assume that  $\pi_{\theta}$  is a measurable function of  $\theta$ . So

$$
C(\mu, \nu) \leq \int_{\mathcal{X} \times \mathcal{Y}} c(x, y) \pi(dx dy)
$$
  
$$
= \int_{\mathcal{X} \times \mathcal{Y}} c(x, y) \left( \int_{\Theta} \pi_{\theta} \lambda(d\theta) \right) (dx dy)
$$
  
$$
= \int_{\Theta} \left( \int_{\mathcal{X} \times \mathcal{Y}} c(x, y) \pi_{\theta}(dx dy) \right) \lambda(d\theta)
$$
  
$$
= \int_{\Theta} C(\mu_{\theta}, \nu_{\theta}) \lambda(d\theta),
$$

and the conclusion follows. □

[]

# Description of optimal plans

Obtaining more precise information about minimizers will be much more of a sport. Here is a short list of questions that one might ask:

- Is the optimal coupling unique? smooth in some sense?<br>• Is there a *Monne coupling* i.e. a deterministic optimal  $\alpha$
- Is there a *Monge coupling*, i.e. a deterministic optimal coupling?
- Is there a geometrical way to characterize optimal couplings? Can one check in practice that a certain coupling is optimal?

About the second question: Why don't we try to apply the same reasoning as in the proof of Theorem 4.1? The problem is that the set of deterministic couplings is in general not compact; in fact, this set is often dense in the larger space of all couplings! So we may expect that the value of the infimum in the Monge problem coincides with the value of the minimum in the Kantorovich problem; but there is no a priori reason to expect the existence of a Monge minimizer.

**Example 4.9.** Let  $\mathcal{X} = \mathcal{Y} = \mathbb{R}^2$ , let  $c(x, y) = |x - y|^2$ , let  $\mu$  be  $\mathcal{H}^1$ restricted to  $\{0\} \times [-1, 1]$ , and let  $\nu$  be  $(1/2)$   $\mathcal{H}^1$  restricted to  $\{-1, 1\} \times$  $[-1, 1]$ , where  $\mathcal{H}^1$  is the one-dimensional Hausdorff measure. Then there is a unique optimal transport, which for each point  $(0, a)$  sends one half of the mass at  $(0, a)$  to  $(-1, a)$ , and the other half to  $(1, a)$ . This is not a Monge transport, but it is easy to approximate it by (nonoptimal) deterministic transports (see Figure 4.1).

|  | <b>STATISTICS</b> |
|--|-------------------|

Fig. 4.1. The optimal plan, represented in the left image, consists in splitting the mass in the center into two halves and transporting mass horizontally. On the right the filled regions represent the lines of transport for a deterministic (without splitting of mass) approximation of the optimum.

## Bibliographical notes

Theorem 4.1 has probably been known from time immemorial; it is usually stated for nonnegative cost functions.

Prokhorov's theorem is a most classical result that can be found e.g. in [120, Theorems 6.1 and 6.2], or in my own course on integration [819, Section VII-5].

Theorems of the form "infimum cost in the Monge problem = minimum cost in the Kantorovich problem" have been established by Gangbo [396, Appendix A], Ambrosio [20, Theorem 2.1], and Pratelli [687, Theorem B]. The most general results to this date are those which appear in Pratelli's work: Equality holds true if the source space  $(\mathcal{X}, \mu)$ is Polish without atoms, and the cost is continuous  $\mathcal{X} \times \mathcal{Y} \to \mathbb{R} \cup \{+\infty\},\$ with the value  $+\infty$  allowed. (In [687] the cost c is bounded below, but it is sufficient that  $c(x, y) \ge a(x) + b(y)$ , where  $a \in L^1(\mu)$  and  $b \in L^1(\nu)$ are continuous.)

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
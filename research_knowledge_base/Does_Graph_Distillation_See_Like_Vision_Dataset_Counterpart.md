# Does Graph Distillation See Like Vision Dataset Counterpart?

<PERSON><PERSON><sup>1,2</sup>\*, <PERSON><sup>3</sup>\*, <PERSON><PERSON><sup>1,2†</sup>, <PERSON><sup>1,2</sup>, <PERSON><PERSON><PERSON> Fu<sup>1,2</sup>, <PERSON><PERSON> $^4$ , <PERSON> $^3$ , <PERSON><PERSON><PERSON> Li $^{1,2\ddagger}$ 

<sup>1</sup> School of Computer Science and Engineering, Beihang University <sup>2</sup> Advanced Innovation Center for Big Data and Brain Computing, Beihang University <sup>3</sup>National University of Singapore <sup>4</sup>Carnegie Mellon University

National University of Singapore Carnegie Mellon University

# Abstract

Training on large-scale graphs has achieved remarkable results in graph representation learning, but its cost and storage have attracted increasing concerns. Existing graph condensation methods primarily focus on optimizing the feature matrices of condensed graphs while overlooking the impact of the structure information from the original graphs. To investigate the impact of the structure information, we conduct analysis from the spectral domain and empirically identify substantial Laplacian Energy Distribution (LED) shifts in previous works. Such shifts lead to poor performance in cross-architecture generalization and specific tasks, including anomaly detection and link prediction. In this paper, we propose a novel Structure-broadcasting Graph Dataset Distillation (SGDD) scheme for broadcasting the original structure information to the generation of the synthetic one, which explicitly prevents overlooking the original structure information. Theoretically, the synthetic graphs by SGDD are expected to have smaller LED shifts than previous works, leading to superior performance in both cross-architecture settings and specific tasks. We validate the proposed SGDD across 9 datasets and achieve state-of-the-art results on all of them: for example, on the YelpChi dataset, our approach maintains 98.6% test accuracy of training on the original graph dataset with 1,000 times saving on the scale of the graph. Moreover, we empirically evaluate there exist 17.6% ∼ 31.4% reductions in LED shift crossing 9 datasets. Extensive experiments and analysis verify the effectiveness and necessity of the proposed designs. The code is available in the [https://github.com/RingBDStack/SGDD.](https://github.com/Suchun-sv/SGDD)

# 1 Introduction

Graphs have been applied in many research areas and achieved remarkable results, including social networks [\[26,](#page-11-0) [65,](#page-13-0) [71,](#page-14-0) [77\]](#page-14-1), physical [\[15,](#page-10-0) [3,](#page-10-1) [55,](#page-13-1) [10\]](#page-10-2), and chemical interactions [\[2,](#page-10-3) [91,](#page-15-0) [109,](#page-16-0) [78\]](#page-14-2). Graph neural networks (GNNs), a classical and wide-studied graph representation learning method [\[38,](#page-12-0) [82,](#page-14-3) [97,](#page-15-1) [61\]](#page-13-2), is proposed to extract information via modeling the features and structures from the given graph. Nevertheless, the computational and memory costs are extremely heavy when training on a large graph [\[46,](#page-12-1) [87,](#page-14-4) [98,](#page-15-2) [73\]](#page-14-5). One of the most straightforward ideas is to reduce the redundancy of the large graph. For example, graph sparsification  $[64, 75]$  $[64, 75]$  $[64, 75]$  and coarsening  $[51, 50, 44]$  $[51, 50, 44]$  $[51, 50, 44]$  $[51, 50, 44]$  $[51, 50, 44]$  are proposed to achieve this goal by dropping redundant edges and grouping similar nodes. These methods have shown promising results in reducing the size and complexity of large graphs while preserving their essential properties.

<sup>∗</sup>Equal contribution (<EMAIL>, <EMAIL>).

<sup>†</sup> <NAME_EMAIL>

<sup>‡</sup><NAME_EMAIL>.

<span id="page-1-3"></span><span id="page-1-2"></span><span id="page-1-1"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays two comparative schemes for graph condensation. The top row, labeled (a), (b), and (c), illustrates the "Condense scheme of GCond." Scheme (a) shows a process where input features X and adjacency matrix A are processed through "Gradient Matching" to produce X', which is then fed into a function f(X') to yield A'. Image (b) shows a graph structure labeled "A' of GCond" with a self-consistency (SC) score of 0.46. Image (c) presents a heatmap titled "Cross-arch. Acc. of GCond," showing accuracy values ranging from 84.2 to 89.8 across a 5x5 grid. The bottom row, labeled (d), (e), and (f), illustrates the "Condense scheme of SGDD." Scheme (d) shows input features X and adjacency matrix A processed through "Gradient Matching" to produce X', which is then fed into a generator (GEN) along with broadcasting information to produce A'. Image (e) shows a graph structure labeled "A' of SGDD" with an SC score of 0.18. Image (f) presents a heatmap titled "Cross-arch. Acc. of SGDD," showing accuracy values ranging from 86.7 to 91.7 across a 5x5 grid.

<span id="page-1-4"></span>Figure 1: (a) and (d) illustrate that the pipelines of SGDD and GCond [\[35\]](#page-12-5). One can find that SGDD broadcast A from the original graph to the generation of  $A'$  while GCond synthesizes  $A'$ via the operation  $f(\cdot)$  (e.g., pair-wise feature similarity [\[72\]](#page-14-7)) on **X'**. We also show the condensed graph and its shift coefficient (SC) of GCond and SGDD in (b) and (e), respectively. Note that we introduce  $SC$  as an approximation of the LED shift. We show the cross-architecture performance of GCond and SGDD in (c) and (f), 1, 2, 3, 4, and 5 denote APPNP  $[40]$ , Cheby  $[13]$ , GCN  $[39]$ , SAGE [\[25\]](#page-11-1), and SGC [\[90\]](#page-15-3), more cross-architecture results can be found in Tab. [2.](#page-7-0)

However, graph sparsification and coarsening heavily rely on heuristics [\[5\]](#page-10-5) (*e.g.*, the largest principle eigenvalues  $[76]$ , pairwise distances  $[64, 79]$  $[64, 79]$  $[64, 79]$ ), which may lead to poor generalization on various architectures or tasks and sub-optimal for the downstream GNNs training  $[35]$ . Most recently, as shown in Fig.  $1(a)$ , GCond [\[35\]](#page-12-5) follows the vision dataset distillation methods [\[86,](#page-14-10) [104,](#page-15-4) [102\]](#page-15-5) and proposes to condense the original graph by the gradient matching strategy. The feature of the synthetic graph dataset is optimized by minimizing the gradient differences between training on original and synthetic graph datasets. Then, the synthetic feature is fed into the function  $f(\mathbf{X}^f)$  (e.g., pair-wise feature similarity [\[72\]](#page-14-7)) to obtain the structure of the graph. The synthetic graph dataset (including feature and structure) is expected to preserve the task-relevant information and achieve comparable results as training on the original graph dataset at an extremely small cost.

Although the gradient matching strategy has achieved significant success in graph dataset condensation, most of these works [\[35,](#page-12-5) [34,](#page-11-2) [48\]](#page-12-8) follow the previous vision dataset distillation methods to synthesize the condensed graphs, which results in the following limitations: 1) In Fig.  $1(b)$ , we can find that the original graph structure information is not well preserved in the condensed graph. That's because they build the structure  $(A')$  upon the learned feature  $(X')$  (Fig. [1\(a\)\)](#page-1-0), which may cause the loss of original structure information. 2) In vision dataset distillation, applying the gradient matching strategy may result in an entanglement between the synthetic dataset and the architecture that is used for condensing [\[105,](#page-15-6) [103,](#page-15-7) [37\]](#page-12-9). This can detrimentally impact their performance, especially when it comes to generalizing to unseen architectures  $[21]$ . This issue is compounded when dealing with graph data that comprise both features and structures. As shown in Fig.  $1(c)$  and Tab. [2,](#page-7-0) GCond [\[35\]](#page-12-5) explicitly displays inferior performance, demonstrating that generating structure only based on the feature degrades the generalization of the condensed graph.

To investigate the relation between the structure of the condensed graph and the performance of training on it, we follow previous works  $[80, 1, 13, 76]$  $[80, 1, 13, 76]$  $[80, 1, 13, 76]$  $[80, 1, 13, 76]$  $[80, 1, 13, 76]$  $[80, 1, 13, 76]$  $[80, 1, 13, 76]$  to analyze it from a spectral view. Specifically, we explore the relationship between the Laplacian Energy Distribution (LED) shift [\[80,](#page-14-11) [24,](#page-11-4) [12\]](#page-10-7) and the generalization performance of the condensed graph. We empirically find a positive correlation between LED shift and the performance in cross-architecture settings. To address these issues, we introduce a novel Structure-broadcasting Graph Dataset Distillation (SGDD) scheme to condense the original graph dataset by broadcasting the original adjacency matrix to the generation of the synthetic one, named SGDD, which explicitly prevents overlooking the original structure information. As shown in Fig.  $1(d)$ , we propose the graphon approximation to broadcast the original structure A as supervision to the generation of condensed graph structure  $A'$ . Then, we optimize  $A'$  by minimizing

the optimal transport distance of these two structures. For  $X'$ , we follow the  $[35]$  to synthesize the feature of the condensed graph. The  $A'$  and  $X'$  are jointly optimized in a bi-level loop. Both theoretical analysis (Sec. [3.2\)](#page-3-0) and empirical studies (Fig.  $1(c)$  and  $1(f)$ ) consistently show that our method reduces the LED shift significantly  $(0.46 \rightarrow 0.18)$ .

We further evaluate the impact of LED shifts and conduct experiments in the cross-architecture setting. Comparing Fig.  $1(f)$ ,  $1(c)$ , and Tab. [2,](#page-7-0) the improvement of SGDD is up to 11.3%. To evaluate the generalization of our method, we extend SGDD to node classification, anomaly detection, and link prediction tasks. SGDD achieves state-of-the-art results on these tasks in most cases. Notably, we obtain new state-of-the-art results on YelpChi and Amazon with 9.6% and 7.7% improvements, respectively. Our main contributions can be summarized as follows:

- Based on the analysis of the difference between graph and vision dataset distillation, we introduce SGDD, a novel framework for graph dataset distillation via broadcasting the original structure information to the generation of a condensed graph.
- In SGDD, the graphon approximation provides a novel scheme to broadcast the original structure information to condensed graph generation, and the optimal transport is proposed to minimize the LED shifts between original and condensed graph structures.
- SGDD effectively reduces the LED shift between the original and the condensed graphs, consistently surpassing the performance of current state-of-the-art methods across a variety of datasets.

## 2 Related Work

Dataset Distillation & Dataset Condensation. Dataset distillation (DD) [\[86,](#page-14-10) [105,](#page-15-6) [23,](#page-11-5) [49,](#page-12-10) [108,](#page-15-8) [22,](#page-11-6) [84,](#page-14-12) [47,](#page-12-11) [85,](#page-14-13) [6,](#page-10-8) [7,](#page-10-9) [23\]](#page-11-5) aims to distill a large dataset into a smaller but informative synthetic one. The proposed method imposes constraints on the synthetic samples by minimizing the training loss difference, while ensuring that the samples remain informative. This technique is useful for applications such as continual learning  $[104, 105, 37, 42, 103]$  $[104, 105, 37, 42, 103]$  $[104, 105, 37, 42, 103]$  $[104, 105, 37, 42, 103]$  $[104, 105, 37, 42, 103]$  $[104, 105, 37, 42, 103]$  $[104, 105, 37, 42, 103]$  $[104, 105, 37, 42, 103]$  $[104, 105, 37, 42, 103]$ , as well as neural architecture search [\[59,](#page-13-4) [60,](#page-13-5) [94\]](#page-15-9). Recently, GCond [\[35\]](#page-12-5) is proposed to reduce a large-scale graph to a smaller one for node classification using the gradient matching scheme in DC  $[104]$ . Unlike GCond  $[35]$ , who build the structure only through the learned feature, we explicitly broadcast the original graph structure to the generations to prevent the overlooking of the original graph structure information.

Graph Coarsening & Graph Sparsification. Graph Coarsening  $[51, 50, 14]$  $[51, 50, 14]$  $[51, 50, 14]$  $[51, 50, 14]$  $[51, 50, 14]$  follows the intuition that nodes in the original graph can naturally group the similiar node to a super-nodes. Graph Sparsification  $[64, 36, 76]$  $[64, 36, 76]$  $[64, 36, 76]$  $[64, 36, 76]$  $[64, 36, 76]$  is aimed at reducing the edges in the original graph, as there are many redundant relations in the graphs. We can simply sum up both two methods by trying to reduce the "useless" component in the graph. Nevertheless, these methods primarily rely on unsupervised techniques such as the largest k eigenvalue [\[64\]](#page-13-3) or the multilevel incomplete LU factorization [\[14\]](#page-10-10), which cannot guarantee the behavior of the synthetic graph in downstream tasks. Moreover, they fail to reduce the graph size to an extremely small degree (*e.g.*, reduce the number of nodes to 0.1% of the original). In contrast, graph condensation can aggregate information into a smaller yet informative graph in a supervised way, thereby overcoming the aforementioned limitations.

# 3 Preliminary and Analysis

### 3.1 Formulation of graph condensation

Consider a graph dataset  $G = \{A, X, Y\}$ , where  $A \in \mathbb{R}^{N \times N}$  denotes the adjacency matrix,  $\mathbf{X} \in \mathbb{R}^{N \times d}$  is the feature, and  $\mathbf{Y} \in \mathbb{R}^{N \times 1}$  represents the labels.  $\mathcal{S} = \{ \mathbf{A}', \mathbf{X}', \mathbf{Y}' \}$  is defined as the synthetic dataset, the first dimension of  $\mathbf{A}', \mathbf{X}'$ , and  $\mathbf{Y}'$  are  $N'$  ( $N' \ll N$ ).

The goal of graph condensation is achieving comparable results as training on the original graph dataset  $G$  via training on the synthetic one  $S$ . The optimization process can be formulated as follow,

$$
S^* = \arg\min_{S} M(\theta_{\mathcal{S}}^*, \theta_{\mathcal{G}}^*) \quad \text{s.t} \quad \theta_t^* = \arg\min_{\theta} \text{Loss}(\text{GNN}_{\theta}(t)), \tag{1}
$$

where GNN $\theta(t)$  denotes a GNN parameterized with  $\theta$ ,  $\theta$ <sub>S</sub> and  $\theta$ <sub>G</sub> are the parameters that are trained on S and G, and  $t \in \{S, \mathcal{G}\}\,$ , M( $\cdot$ ) denotes a matching function and  $\text{Loss}(\cdot)$  is the loss function.

<span id="page-3-3"></span>Image /page/3/Figure/0 description: The image displays two plots, (a) and (b), illustrating the distribution of lambda values for different graph neural network architectures and configurations. Plot (a) shows the distributions for "Original", GCN, SGC, APPNP, and SAGE, with corresponding SC values and averages. For GCN, SC is 0.21 and Avg. is 75.16. For SGC, SC is 0.27 and Avg. is 73.32. For APPNP, SC is 0.37 and Avg. is 70.36. For SAGE, SC is 0.42 and Avg. is 62.50. Plot (b) shows the distributions for "Original" and configurations of BWGNN with different p and q values: p=2, q=2 (SC: 0.19, Avg.: 76.30), p=3, q=1 (SC: 0.24, Avg.: 73.30), p=1, q=3 (SC: 0.39, Avg.: 67.40), and p=0, q=4 (SC: 0.44, Avg.: 63.30). Both plots have the x-axis labeled as "lambda" ranging from 0.0 to 2.0.

<span id="page-3-4"></span><span id="page-3-2"></span>Figure 2: (a): Illustrations of the LEDs (use the density plotting, each peak represents the LED of a graph), SC, and Avg. (average test performances on GCN, SGC, APPNP, and SAGE) in various architectures. (b): Evaluation of these three metrics in frequency-adaptive BWGNN. We empirically find that the LED shifts (*i.e.*, SC) are high-consistent with Avg. performances, thus SC could be a good indicator to evaluate the performance of the condensed graph.

<span id="page-3-0"></span>

#### 3.2 Analysis of the structure of the condensed graph and its effects

In this section, we first introduce the definition of Laplacian Energy Distribution (LED), then analyze the LED in previous work, and finally explore its influence on generalization performance.

Laplacian Energy Distribution. Following the previous works [\[24,](#page-11-4) [12,](#page-10-7) [80\]](#page-14-11), we introduce Laplacian energy distribution (LED) to analyze the structure of graph data. Given an adjacency matrix  $A \in$  $\mathbb{R}^{N \times \tilde{N}}$ , the Laplacian matrix L is defined as  $D - A$ , where the D is the degree matrix of A. We utilize the normalized Lapalacian matrix  $\tilde{\bf L} = {\bf I} - {\bf D}^{-1/2} {\bf A} {\bf D}^{-1/2}$ , where I is an identity matrix. The eigenvalues of  $\tilde{\bf L}$  are then defined as  $\lambda_1, \ldots, \lambda_n, \ldots, \lambda_N$  by ascending order, *i.e.*,  $\lambda_1 \leq \cdots \lambda_n \leq \cdots \leq$  $\lambda_N$ , with orthonormal eigenvectors  $\bm{U} = (\mathbf{u}_1, \cdots \mathbf{u}_n, \cdots \mathbf{u}_N)$  correspond to these eigenvalues.

**Definition 1** (Laplacian Energy Distribution [\[24,](#page-11-4) [12,](#page-10-7) [80\]](#page-14-11)). Let  $\mathbf{X} = (x_1, \dotsm x_n, \dotsm x_N)^\top \in \mathbb{R}^{N \times d}$ *be the feature of graph, we have*  $\hat{\bf X} = (\hat x_1 \cdots \hat x_n \cdots \hat x_N)^\top = {\bf U}^\top {\bf X}$  *as the post-Graph-Fourier-Transform of* X*. The formulation of LED is defined as:*

$$
\eta_n(\mathbf{X}, \tilde{\mathbf{L}}) = \frac{\hat{x}_n^2}{\sum_{i=1}^N \hat{x}_i^2} = \bar{x}_n.
$$
\n(2)

**LED analysis of previous work.** First, we define  $\eta^{\mathcal{G}}$  and  $\eta^{\mathcal{S}}$  as the LED of the original graph and condensed graph, respectively. Then the LED shift of the above two graphs can be formulated as:  $||\eta^{\mathcal{G}} - \eta^{\mathcal{S}}|| = ||\sum_{i=1}^{N} \eta_i(\mathbf{X}, \tilde{\mathbf{L}}) - \sum_{j=1}^{N'} \eta_j(\mathbf{X}', \tilde{\mathbf{L}}')||$ . In GCond [\[35\]](#page-12-5), the matching function M defaults as the MSE loss. Therefore, the objective of GCond can be written as  $||\theta_G - \theta_S|| \le \epsilon$ , where  $\epsilon$  is a small number as expected. Incorporating such an objective in the LED shift formula, the lower bound is shown as follows.

Proposition 1. *Refer to [\[1,](#page-10-6) [4,](#page-10-11) [80\]](#page-14-11), the GNN can be recognized as the bandpass filter. Assume the frequency response area of GNN is* (a, b)*, where* (a, b) *is architecture-specific. The lower bound of GCond is shown in Eq.* [\(3\)](#page-3-1)*. Detailed proof can be found in Appendix [B.1.](#page-17-0)*

<span id="page-3-1"></span>
$$
||\eta^{\mathcal{G}} - \eta^{\mathcal{S}}|| \ge \epsilon + \sum_{i=1}^{a} ||\bar{x}_i^2 - \bar{x}_i'^2|| + \sum_{j=b}^{N'} ||\bar{x}_j^2 - \bar{x}_j'^2||. \tag{3}
$$

According to Eq. [\(3\)](#page-3-1), we find the lower bond of LED in GCond is related to the frequency response area  $(a, b)$  (*i.e.*, specific GNN). For example, GCN  $\lceil 38 \rceil$  or SGC  $\lceil 90 \rceil$  (utilized in GCond) is a low-pass filter [\[1\]](#page-10-6), which emphasizes the lower Laplacian energy. As shown in Fig. [1\(a\),](#page-1-0) the  $A'$  is built upon the learned "low-frequency" feature  $X'$ , which may fail to generalize to cross-architectures (*i.e.*, high-pass filters) and specific tasks.

Exploration of the effects of LED shift on the generalization performance. Although the LED shift phenomenon occurs in GCond, quantifying the LED shift is challenging because  $\mathcal G$  and  $\mathcal S$  have

Image /page/4/Figure/0 description: This is a flowchart illustrating a process with two main stages: 'Optimizing X'' and 'Optimizing A''. In the first stage, 'X' and 'A' are inputs to a 'Gradient Matching' process, which outputs 'X'' and 'A''. The 'A'' output is marked with a snowflake, indicating frozen parameters. In the second stage, 'X'' (with frozen parameters) and 'Z' (random noise) are fed into a 'GEN' (generator) which outputs 'A''. 'A'' is then processed by 'LED Matching'. The 'LED Matching' process takes 'A'' and 'A' as inputs, applies a function 'f' to both, and generates histograms. These histograms, along with their respective ranges (0 to 2, with a lambda value indicated), are then used to calculate an 'OT Distance'. The flowchart also includes labels for 'Broadcasting', 'Z: Random noise', '\* Freeze parameters', and 'Laplacian pseudo-inverse'.

<span id="page-4-1"></span>Figure 3: The Training pipeline of the SGDD (left). We first fix  $A'$  to optimize  $X'$  through the gradient matching strategy and we broadcast the supervision of A to the generation of the graph structure A′ . To mitigate the Laplacian Energy Distribution (LED) shift phenomenon, we propose the LED Matching strategy to optimize the A', which optimizes the learned structure with the optimal transport (OT) distance (right).

different numbers of nodes. To enable comparison, we follow an intuitive assumption that two nodes with similar eigenvalue distribution proportions can be aligned in comparison. Thus, we first convert LEDs of  $G$  and  $S$  into probability distributions using the Kernel Density Estimation (KDE) method [\[58\]](#page-13-6). Then we quantify the LED shifts as the distance of the probability distributions using the Jensen-Shannon (JS) divergence [\[45\]](#page-12-14). We define the LED shift coefficient ( $SC$ ) in Definition [2:](#page-4-0)

<span id="page-4-0"></span>Definition 2 (LED shift coefficient, SC). *The LED shift coefficient between* G *and* S *is:*

<span id="page-4-3"></span>
$$
SC = JS \left( \frac{1}{|V_{\mathcal{G}}|h} \sum_{\bar{x}_i \in X_{\mathcal{G}}} K(\frac{x - \bar{x}_i}{h}) \middle| \left| \frac{1}{|V_{\mathcal{S}}|h} \sum_{\bar{x}_j \in X_{\mathcal{S}}} K(\frac{x - \bar{x}_j}{h}) \right| \right), \tag{4}
$$

*where the*  $JS(\cdot||\cdot)$  *denotes the JS-divergence, the*  $|V_G|$  *and the*  $|V_S|$  *is the number of the nodes to corresponding graphs, the* K(·) *represents the Gaussian kernel function with bandwidth parameter h*.  $SC ∈ [0, 1]$  *reflects the divergence between*  $G$  *and*  $S$  *(a smaller SC indicates more similar).* 

In Fig. [2,](#page-3-2) we empirically study the influences of  $SC$  in two settings: various GNNs in Fig.  $2(a)$  and fixed BWGNN  $[80]$  with adaptive bandpass in Fig. [2\(b\).](#page-3-4) **Based on the results in Fig. [2,](#page-3-2) We have** several observations: (1) The entangled learning paradigm that building structure (*i.e.*, adjacency matrix) upon on feature matrix will significantly lead to the LED shift phenomenon. (2) The positive correlation exists between the LED shift and the generalization performance of the condensed graph. (3) Preserving more information about the original graph structure may alleviate the LED shift phenomenon and improve the generalization performance of the condensed graph.

# 4 Structure-broadcasting Graph Dataset Distillation

In this section, we first present the pipeline and overview of SGDD in Fig. [3.](#page-4-1) Then, we introduce two modules of SGDD. Finally, we summarize the training pipeline of our SGDD.

<span id="page-4-4"></span>

#### 4.1 Learning graph structure via graphon approximation

To prevent overlooking the original structure A from  $G$ , we broadcast A as supervision for the generation of A'. Considering the different shapes between A and A' ( $N' \ll N$ ), we introduce graphon  $[20, 67, 16, 31, 92]$  $[20, 67, 16, 31, 92]$  $[20, 67, 16, 31, 92]$  $[20, 67, 16, 31, 92]$  $[20, 67, 16, 31, 92]$  $[20, 67, 16, 31, 92]$  $[20, 67, 16, 31, 92]$  $[20, 67, 16, 31, 92]$  $[20, 67, 16, 31, 92]$  to distill the original structure information to the condensed structure  $A'$ . Specifically, given random noise  $\mathcal{Z}(N') \in \mathbb{R}^{N' \times N'}$  as the input coordinates, through the generative model, we then synthesize an adjacency matrix  $A'$  with  $N'$  nodes. This process can be formulated as  $A' = \text{GEN}(\mathcal{Z}(N'); \Phi)$ , where the GEN( $\cdot$ ) is a generative model with parameter  $\Phi$ , and the optimization process is then defined as:

<span id="page-4-2"></span>
$$
\mathcal{L}_{structure} = \text{Distance}(\mathbf{A}, \text{GEN}(\mathcal{Z}(N'); \Phi)),\tag{5}
$$

where A is supervision and Distance( $\cdot$ ) is a metric that measure the difference of A and A'. The details of Distance( $\cdot$ ) can be found in Sec. [4.2.](#page-5-0) To avoid overlooking of the inherent relation [\[30,](#page-11-10) [71\]](#page-14-0) between  $A'$  and the corresponding node information (*i.e.*,  $X'$  and  $Y'$ ), we jointly input  $X'$  and  $Y'$ as conditions to generate  $\mathbf{A}'$ . Therefore, the final version of the generative model can be written as  $\mathbf{A}' = \text{GEN}(\mathcal{Z}(N') \oplus \mathbf{X}' \oplus \mathbf{Y}'; \Phi)$ , the  $\oplus$  denotes the concatenate operation.

To study the performance of SGDD in the above paradigm, we theoretically prove the upper bound of LED shift in SGDD by invoking graphon theory. The result can be presented as follows.

Proposition 2. *The upper bound of the LED shift on SGDD is shown as:*

<span id="page-5-1"></span>
$$
||\eta^{\mathcal{G}} - \eta^{\mathcal{S}}|| \le \delta_{\square}(W_{\mathbf{A}}, \mathbf{A}'), \tag{6}
$$

*where*  $\delta_{\Box}$  *denotes the cut distance* [\[52\]](#page-12-15) *and*  $W_{\mathbf{A}}$  *is the graphon of* **A**. See details in Appendix **[B.2.](#page-18-0)** 

Note minimizing the upper bound of Eq. [\(6\)](#page-5-1) is equal to optimizing the  $L_{structure}$  on Eq. [\(5\)](#page-4-2). Compared to the lower bound in (Eq. [\(3\)](#page-3-1)), our upper bound is not related to any frequency response of specific GNN (*i.e.*, the terms of  $\sum_{i=1}^{a} ||\bar{x}_i^2 - \bar{x}_i^{'2}|| + \sum_{i=b}^{N'} ||\bar{x}_i^2 - \bar{x}_i^{'2}||$ ). As a result, SGDD may perform better than the previous work, especially in the cross-architecture setting and specific tasks.

<span id="page-5-0"></span>

### 4.2 Optimizing the graph structure via optimal transport

To mitigate the LED shift between  $A$  and  $A'$ , ideally, we can directly minimize the proposed  $SC$ . However, SC requires an extremely time-consuming  $(O(N^3))$  [\[4,](#page-10-11) [56\]](#page-13-8) eigenvalue decomposition operation. Therefore, we propose the LED Matching strategy based on the *optimal transport* theory to form an efficient optimizing process.

Recall in the Eq.[\(4\)](#page-4-3) of calculating  $SC$ , we first decompose the eigenvalue, followed by aligning the node through the JS divergence, which essentially compares the distribution proportions. The key point is to decide the node mapping strategy to align such two graphs. Alternatively, assuming we know the prior distribution of the alignment of  $S$  (*i.e.*, we know the bijection of nodes in  $S$  to the G) and denoting such alignment as  $S^*$ , we can directly measure the distance between G and  $S^*$  by employing the 2-Wasserstein metric<sup>[\[96,](#page-15-11) [54\]](#page-13-9)</sup>.

<span id="page-5-2"></span>
$$
||\eta^{\mathcal{G}} - \eta^{\mathcal{S}*}|| = W_2^2 \left( \eta^{\mathcal{G}}, \eta^{\mathcal{S}*} \right) \tag{7}
$$

Furthermore, following the assumption in previous work[\[54,](#page-13-9) [96\]](#page-15-11), we have  $\eta^G \sim \mathcal{N}\left(0,L_{\mathcal{G}}^{\dagger}\right)$  and  $\eta^S \sim \mathcal{N}\left(0,L_{\mathcal{S}*}^{\dagger}\right)$ . Then, the Eq.[\(7\)](#page-5-2) have a closed-form expression[\[96\]](#page-15-11) as follows:

$$
||\eta^{\mathcal{G}} - \eta^{\mathcal{S}*}|| = N \operatorname{tr}\left(L_{\mathcal{G}}^{\dagger}\right) + N' \operatorname{tr}\left(L_{\mathcal{S}*}^{\dagger}\right) - 2 \operatorname{tr}\left(\sqrt{L_{\mathcal{S}*}^{\frac{1}{2}} L_{\mathcal{G}}^{\dagger} L_{\mathcal{S}*}^{\frac{1}{2}}}\right),\tag{8}
$$

the  $L^{\dagger}$  denotes the Laplacian pseudo-inverse operation and the tr indicates the trace operation of matrix. Therefore, even though we could not know the actual mapping strategy  $S^*$ , we can use the infimum of all possible strategies as a proxy solution. Formally, following the prior work [\[54\]](#page-13-9), we employ the function T as a transport plan in the metric space  $\mathcal X$  to represent all feasible mapping strategies. Then, we use the  $T_{\#} \eta^{\mathcal{S}}$  to represent the pushing forward process of transferring the distribution of  $\eta^{\mathcal{S}}$  to the  $\eta^{\mathcal{G}}$ . As a result, the distance can be regarded as finding the infimum.

<span id="page-5-3"></span>
$$
||\eta^{G} - \eta^{S}|| = \inf_{T_{\#}\eta^{S} = \eta^{G}} \int_{\mathcal{X}} ||x - T(x)||^{2} d\eta^{S}(x)
$$

$$
= N' \text{tr}(L_{S}^{\dagger}) - 2 \text{tr}\left(\left((L_{S}^{\dagger})^{1/2} P^{T} L_{G}^{\dagger} P\left(L_{S}^{\dagger}\right)^{1/2}\right)^{1/2}\right).
$$
 (9)

Here, due to the transport plan  $T$  is impractical in optimizing, following the previous work[\[96\]](#page-15-11), we use  $P \in \mathbb{R}^{N' \times N}$  denotes as a free parameter serving as the direct mapping strategy between nodes. Thus, the Distance in the Eq. [\(5\)](#page-4-2) could be directly optimized by the Eq.[\(9\)](#page-5-3) (*i.e.*, use the P represents all possible mapping strategies, the optimizing of  $P$  is equal to choosing a more optimal mapping strategy). In the experimental setting, we use the Sinkhorn-Knopp $[9]$  algorithm to optimize  $P$ .

The overall time complexity is reduced to the  $O(N^{\omega}) \le O(N^{2.373})$  [\[96\]](#page-15-11). Note that the  $L_G^{\dagger}$  may be too large for computing, so we empirically sample a medium size (*e.g.*, 2,000 nodes) sub-structure in the experiment and ablate its influence in Appendix [C.7.](#page-23-0)

<span id="page-6-0"></span>Table 1: Comparisons to state-of-the-art methods. SGDD achieves the highest results in most cases on node classification (NC), anomaly detection (AD), and link prediction (LP) tasks. We report test accuracy (%) on NC datasets (including Citeseer, Cora, Ogbn-arxiv, Flickr, and Reddit), F1-macro (%) on AD datasets (including YelpChi and Amazon), and AUC (%) on LP datasets (including Citeseer-L and DBLP). Bold entries are best results, underline mark the runner-ups.

|    | <b>Dataset</b>             | <b>Ratio</b> $(r)$         | Random                                                          | Herding                                                            | K-Center                                                | Coarsening                                              | <b>GDC</b>                                                        | Gcond                                                                           | <b>SGDD</b>                                                                            | Whole                        |
|----|----------------------------|----------------------------|-----------------------------------------------------------------|--------------------------------------------------------------------|---------------------------------------------------------|---------------------------------------------------------|-------------------------------------------------------------------|---------------------------------------------------------------------------------|----------------------------------------------------------------------------------------|------------------------------|
| NC | Citeseer [39]              | 0.90%<br>1.80%<br>3.60%    | $54.4_{\pm 4.4}$<br>$64.2_{\pm1.7}$<br>$69.1_{\pm0.1}$          | $57.1_{\pm 1.5}$<br>$66.7_{+1.0}$<br>$69.0_{\pm 0.1}$              | $52.4_{\pm 2.8}$<br>$64.3_{\pm1.0}$<br>$69.1_{\pm0.1}$  | $52.2_{\pm0.4}$<br>$59.0_{\pm 0.5}$<br>$65.3_{\pm 0.5}$ | $66.8_{\pm 1.5}$<br>$66.9_{\pm0.9}$<br>$66.3_{\pm1.5}$            | $\textbf{70.5}_{\pm1.2}$<br>$70.6_{\pm 0.9}$<br>$69.8_{\pm 1.4}$                | $69.5_{\pm 0.4}$<br>$70.2_{\pm 0.8}$<br>$70.3_{\pm 1.7}$                               | $71.7_{\pm 0.1}$             |
|    | Cora $[39]$                | 1.30%<br>2.60%<br>5.20%    | $63.6_{\pm3.7}$<br>$72.8_{\pm 1.1}$<br>$76.8_{\pm 0.1}$         | $67.0_{\pm 1.3}$<br>$73.4_{\pm1.0}$<br>$76.8_{\pm0.1}$             | $64.0_{\pm2.3}$<br>$73.2_{\pm1.2}$<br>$76.7_{\pm0.1}$   | $31.2_{\pm0.2}$<br>$65.2_{\pm0.6}$<br>$70.6_{\pm0.1}$   | $67.3_{\pm1.9}$<br>$67.6_{\pm 3.5}$<br>$67.7_{\pm 2.2}$           | $79.8_{\pm{1.3}}$<br>$80.1_{\pm 0.6}$<br>$79.3_{\pm 0.3}$                       | $80.1_{\pm 0.7}$<br>$\textbf{80.6}_{\pm 0.8}$<br>$80.4_{\pm 1.6}$                      | $81.2_{\pm 0.2}$             |
|    | Ogbn-arxiv <sup>[28]</sup> | 0.05%<br>0.25%<br>$0.50\%$ | $47.1_{\pm 3.9}$<br>$57.3_{\pm1.1}$<br>$60.0_{\pm 0.9}$         | $52.4_{\pm1.8}$<br>$58.6_{\pm1.2}$<br>$60.4_{\pm 0.8}$             | $47.2_{\pm3.0}$<br>$56.8_{\pm 0.8}$<br>$60.3_{\pm 0.4}$ | $35.4_{\pm0.3}$<br>$43.5_{\pm0.2}$<br>$50.4_{\pm 0.1}$  | $58.6_{\pm0.4}$<br>$59.9_{\pm0.3}$<br>$59.5_{\pm0.3}$             | $59.2_{\pm 1.1}$<br>$63.2_{\pm 0.3}$<br>$64.0_{\pm 0.4}$                        | $60.8_{\pm 1.3}$<br>$\textbf{65.8}_{\pm1.2}$<br>$\textbf{66.3}_{\pm 0.7}$              | $71.4_{\pm 0.1}$             |
|    | Flickr [99]                | 0.10%<br>0.50%<br>1.00%    | $41.8_{\pm2.0}$<br>$44.0_{\pm0.4}$<br>$44.6_{\pm 0.2}$          | $42.5_{\pm1.8}$<br>$43.9_{\pm0.9}$<br>$44.4_{\pm0.6}$              | $42.0_{\pm0.7}$<br>$43.2_{\pm0.1}$<br>$44.1_{\pm 0.4}$  | $41.9_{\pm0.2}$<br>$44.5_{\pm0.1}$<br>$44.6_{\pm 0.1}$  | $46.3_{\pm0.2}$<br>$45.9_{\pm0.1}$<br>$45.8_{\pm0.1}$             | $46.5{\scriptstyle \pm 0.4}$<br>$47.1_{\pm 0.1}$<br>$47.1_{\pm 0.1}$            | $46.9_{+0.1}$<br>$47.1_{\pm 0.3}$<br>47.1 $_{\pm 0.1}$                                 | $47.2_{\pm 0.1}$             |
|    | Reddit $[25]$              | 0.01%<br>0.05%<br>0.50%    | $46.1_{\pm 4.4}$<br>$58.0_{\pm2.2}$<br>$66.3_{\pm1.9}$          | $53.1_{\pm2.5}$<br>$62.7_{\pm1.0}$<br>$71.0_{\pm1.6}$              | $46.6_{\pm2.3}$<br>$53.0_{\pm3.3}$<br>$58.5_{\pm2.1}$   | $40.9_{\pm 0.5}$<br>$42.8_{\pm0.8}$<br>$47.4_{\pm 0.9}$ | $88.2_{\pm0.2}$<br>$89.5_{\pm0.1}$<br>$90.5_{\pm 1.2}$            | $88.0{\scriptstyle \pm1.8}$<br>$89.6{\scriptstyle \pm 0.7}$<br>$90.1_{\pm 0.5}$ | $90.5{\scriptstyle~\pm 2.1}$<br>$\textbf{91.8}_{\pm 1.9}$<br>$\mathbf{91.6}_{\pm 1.8}$ | $93.9_{\pm 0.0}$             |
| AD | YelpChi [66]               | 0.05%<br>0.10%<br>0.20%    | $41.8_{\pm 0.3}$<br>$43.7_{\pm1.2}$<br>$\mathbf{45.9}_{\pm2.2}$ | $46.1_{\pm0.9}$<br>$47.1_{\pm1.2}$<br>$46.4_{\pm0.8}$              | $49.3_{\pm1.1}$<br>$44.2_{\pm1.8}$<br>$47.5_{\pm0.4}$   | $46.2_{\pm2.1}$<br>$47.5_{\pm1.8}$<br>$49.1_{\pm1.2}$   | $47.9_{\pm1.1}$<br>$50.2_{+2.1}$<br>49.7 $\pm$ <sub>2.0</sub>     | $48.6{\scriptstyle \pm3.7}$<br>$49.6{\scriptstyle \pm1.8}$<br>$50.1 + 2.8$      | $56.2_{+1.8}$<br>$\textbf{58.1}_{\pm2.3}$<br>$\textbf{59.7}_{\pm 1.8}$                 | $61.1_{\pm 1.8}$             |
|    | Amazon [101]               | 0.02%<br>0.20%<br>2.00%    | $76.2_{\pm 0.8}$<br>$76.4_{\pm 1.6}$<br>$78.2_{\pm 0.9}$        | $74.1_{\pm0.9}$<br>$76.5_{\pm2.3}$<br>$77.2_{\pm 1.8}$             | $73.4_{\pm2.1}$<br>$74.2_{\pm1.1}$<br>$73.8_{\pm2.2}$   | $75.2_{\pm2.9}$<br>$76.8_{\pm 1.0}$<br>$77.8_{\pm2.8}$  | $74.1_{\pm1.9}$<br>$78.2_{\pm 2.1}$<br>$79.3_{\pm 3.1}$           | $77.9_{\pm 3.1}$<br>$78.1_{\pm 1.9}$<br>$79.2_{\pm 2.0}$                        | $83.3_{\pm 2.6}$<br>$84.8_{\pm 1.7}$<br>$86.9_{\pm 2.1}$                               | $89.5{\scriptstyle \pm 0.9}$ |
| LP | Citeseer-L [95]            | 0.90%<br>1.80%<br>3.60%    | $56.8_{\pm 0.8}$<br>$61.4_{\pm1.8}$<br>$63.5_{\pm0.9}$          | $63.1_{\pm1.3}$<br>$63.3_{\pm1.8}$<br>$64.7_{\pm1.7}$              | $78.3_{\pm2.6}$<br>$79.1_{\pm1.8}$<br>$80.6_{\pm 2.7}$  | $76.9_{\pm 0.8}$<br>$77.4_{\pm1.7}$<br>$78.4_{\pm0.4}$  | $81.5_{\pm2.5}$<br>$83.4_{\pm1.6}$<br>$84.2{\scriptstyle \pm2.1}$ | $83.4{\scriptstyle \pm1.9}$<br>$83.8 + 2.1$<br>$83.1_{\pm 2.7}$                 | $86.4_{\pm 1.6}$<br>$\textbf{87.2}_{\pm 2.1}$<br>$\textbf{87.1}_{\pm1.2}$              | $96.8_{\pm1.8}$              |
|    | <b>DBLP</b> [81]           | 0.05%<br>0.25%<br>0.50%    | $64.1_{\pm2.6}$<br>$68.3_{\pm 1.4}$<br>$68.7_{\pm2.6}$          | $65.8{\scriptstyle \pm 0.7}$<br>$71.2_{\pm1.7}$<br>$74.3_{\pm0.8}$ | $72.4_{\pm1.6}$<br>$74.9_{\pm2.8}$<br>$75.2_{\pm 0.6}$  | $77.8_{\pm1.3}$<br>$76.9_{\pm 0.9}$<br>$76.8_{\pm 2.1}$ | $78.9 + 2.1$<br>$77.5_{\pm 1.9}$<br>$77.6_{\pm2.1}$               | $77.2_{\pm2.1}$<br>$78.6{\scriptstyle\pm1.2}$<br>$79.9_{\pm 1.9}$               | $81.3{\scriptstyle~\pm2.8}$<br>$\textbf{82.1}_{\pm1.9}$<br>$\textbf{82.1}_{\pm1.8}$    | $84.2_{\pm 0.7}$             |

### 4.3 Training pipeline of SGDD

As illustrated in Fig.  $1(d)$  and Fig. [3,](#page-4-1) we commence by introducing a novel graph structure learning paradigm termed "graphon approximation". This paradigm integrates both the feature  $X'$  and auxiliary information  $\overline{Z}$  to generate the structure. Subsequently, the learned structure  $A'$  is forced to be closer to the original graph structure A in terms of Laplacian energy distribution.

Additionally, our proposed methodology, SGDD, implements a bi-loop optimization schema. Within this framework, we concurrently optimize the parameters  $X'$  and  $A'$ . More specifically, the refinement of  $X'$  is achieved through a gradient matching strategy, whereas the  $A'$  is enhanced using the LED matching technique. During each step, the other component is frozen to ensure effective refinement.

Our overall training loss function can be summarized as  $\mathcal{L} = \mathcal{L}_{feature} + \alpha \mathcal{L}_{structure} + \beta ||A||_2$ , where the  $||A||_2$  is proposed as a sparsity regularization term and  $\mathcal{L}_{feature}$  denotes the gradient matching strategy [\[35\]](#page-12-5).  $\alpha$  and  $\beta$  are trade-off parameters, we study their sensitiveness in the Sec. [5.3.](#page-7-1) The algorithm can be found in Appendix [C.3.](#page-21-0)

# 5 Experiments

#### 5.1 Datasets and implementation details

**Datasets.** We evaluate SGDD on five node classification datasets: Cora [\[39\]](#page-12-7), Citeseer [39], Ogbnarxiv [\[28\]](#page-11-11), Flickr [\[99\]](#page-15-12), Reddit [\[25\]](#page-11-1), two anomaly detection datasets: YelpChi [\[66\]](#page-13-10), Amazon [\[101\]](#page-15-13), and two link prediction datasets: Citeseer-L [\[95\]](#page-15-14), DBLP [\[81\]](#page-14-14). For the node classification and anomaly detection tasks, we follow the public settings [\[17\]](#page-11-12) of train and test. To make a fair comparison, we also follow the previous setting  $[100, 81]$  $[100, 81]$  $[100, 81]$ , we randomly split 80% nodes for training, 10% nodes for validation, and the remaining 10% for testing. To avoid data leakage, we only utilize 80% training samples for condensation. More details of each dataset can be found in Appendix [C.1.](#page-20-0)

<span id="page-7-2"></span>Image /page/7/Figure/0 description: The image contains four plots. Plot (a) is a bar chart comparing the performance of GCond, SGDD w/o A, SGDD w/o X', and SGDD on Ogbn-arxiv and YelpChi datasets. On Ogbn-arxiv, the performances are 64.1, 63.7, 66.0, and 67.2 respectively. On YelpChi, the performances are 50.1, 50.1, 56.7, and 58.1 respectively. Plot (b) shows the F1-macro score as a function of condensing ratio for Original, GDC, GCond, and Ours methods. The x-axis ranges from 0.02 to 20, and the y-axis ranges from 40 to 60. Plot (c) displays the performance on YelpChi, Ogbn-arxiv, and Cora datasets as a function of alpha. The x-axis is on a logarithmic scale from 1e-3 to 10, and the y-axis ranges from 40 to 70. Plot (d) shows the F1-macro and Sparsity as a function of beta for an unspecified dataset. The x-axis is on a logarithmic scale from 1e-3 to 10, and the y-axis ranges from 0 to 80.

<span id="page-7-5"></span><span id="page-7-4"></span><span id="page-7-3"></span>Figure 4: (a) Ablation of components in SGDD. (b) Evaluation of the scalability of SGDD. (c) and (d): the trade-off parameters analysis on  $\alpha$  and  $\beta$ .

Implementation details. Without specific designation, in the condense stage, we adopt the 2-layer GCN with 128 hidden units as the backbone, and we adopt the settings on [\[92\]](#page-15-10), which use 2-layer MLP to represent the structure generative model (*i.e.*, GEN). The learning rates for structure and feature are set to 0.001 (0.0001 for Ogbn-arxiv and Reddit) and 0.0001, respectively. We set  $\alpha$  to 0.1, and  $\beta$  to 0.1. In the evaluation stage, we train the same network for 1,000 epochs on the condensed graph with a learning rate of 0.001. Following the settings in [\[35\]](#page-12-5), we repeat all experiments ten times and report average performance and variance. More details can be found in Appendix [C.2.](#page-20-1)

### 5.2 Comparison with state-of-the-art methods

We compare our proposed SGDD with six baselines: Random, which randomly selected nodes to form the original graph, corset methods (Herding  $[88]$  and K-Center  $[70]$ ), graph coarsening methods (Corasening [\[29\]](#page-11-13)), and the state-of-the-art graph condensation methods (GCond [\[35\]](#page-12-5)). GDC is proposed as a baseline in [\[35\]](#page-12-5), where cosine similarity is added as a constraint to generate the structure of the condensed graph.

In Table [1,](#page-6-0) we present the performance metrics including accuracy, F1-macro, and AUC. For clarity and simplicity, percentages are represented without the % symbol, and variance values are also provided.

Based on the results, we have the following observations: 1) Our proposed SGDD achieves the highest results in most settings, which shows the superiority and generalization of our method. 2) On anomaly detection datasets, the improvements are more significant than other tasks, *i.e.*, improving GCond with 9.6% and 7.7% on YelpChi and Amazon datasets, which can be explained that our method captures the structure information from the original graph dataset more efficiently.

<span id="page-7-0"></span>Table 2: Results of cross-architecture setting, we test condensed graphs in APPNP, Cheby, GCN, GraphSAGE, and SGC. Avg. and Std. : the average performance and the standard deviation of the results, the  $\Delta(\%)$  denotes the improvements upon the GDC. We mark the best performance by **bold**.

| Datasets     | Methods | Architectures |          |            |            |          |           |          | Statistics  |             |        |
|--------------|---------|---------------|----------|------------|------------|----------|-----------|----------|-------------|-------------|--------|
|              |         | MLP [11]      | GAT [82] | APPNP [40] | Cheby [13] | GCN [39] | SAGE [25] | SGC [90] | Avg.        | Std.        | Δ (%)  |
| Reddit [25]  | GDC     | 50.3          | 54.8     | 81.2       | 77.5       | 89.5     | 89.7      | 90.5     | 76.2        | 16.9        | -      |
|              | GCond   | 42.5          | 60.2     | 87.8       | 75.5       | 89.4     | 89.1      | 89.6     | 76.3        | 18.5        | ↑ 0.1  |
|              | Ours    | 56.1          | 74.4     | 89.2       | 78.4       | 89.4     | 89.4      | 89.4     | <b>80.9</b> | <b>12.6</b> | ↑ 4.7  |
| Cora [39]    | GDC     | 67.2          | 64.2     | 67.1       | 67.7       | 67.9     | 66.2      | 72.8     | 67.6        | 2.6         | -      |
|              | GCond   | 73.1          | 66.2     | 78.5       | 76         | 80.1     | 78.2      | 79.3     | 75.9        | 4.9         | ↑ 8.3  |
|              | Ours    | 76.7          | 75.8     | 78.4       | 78.5       | 79.8     | 80.4      | 78.5     | <b>78.3</b> | <b>1.6</b>  | ↑ 10.7 |
| DBLP [81]    | GDC     | 74.4          | 76.8     | 77.4       | 76.7       | 78.9     | 74.8      | 78.4     | 76.8        | 1.7         | -      |
|              | GCond   | 75.3          | 77.6     | 78.9       | 76.1       | 79.6     | 77.4      | 79.9     | 77.8        | 1.7         | ↑ 1.1  |
|              | Ours    | 78.4          | 79.6     | 80.1       | 80.6       | 82.1     | 80.7      | 81.4     | <b>80.4</b> | <b>1.2</b>  | ↑ 3.6  |
| YelpChi [66] | GDC     | 30.7          | 36.4     | 43.7       | 41.5       | 49.6     | 47.4      | 50.1     | 42.8        | 7.2         | -      |
|              | GCond   | 48.9          | 31.8     | 46.7       | 48.6       | 50.1     | 42.5      | 48.7     | 45.3        | 6.5         | ↑ 2.6  |
|              | Ours    | 54.2          | 56.4     | 58.2       | 56.8       | 59.7     | 54.1      | 56.7     | <b>56.6</b> | <b>2.0</b>  | ↑ 13.8 |

<span id="page-7-1"></span>

#### 5.3 Ablation Study

Cross-architecture generalization analysis. To evaluate the generalization ability of SGDD on unseen architectures, we conduct experiments that train on the condensed graph with different architectures and report their performances in Tab. [2.](#page-7-0) Here, the condensed graph is obtained by optimizing SGDD with SGC  $\boxed{90}$ . We test its cross-architecture generalization performances on

<span id="page-8-0"></span>Table 3: Comparison of the cross-architecture generalization performance between GCond and SGDD on Ogbn-arxiv. Bold entries are the best results. ↑/↓: our method show increase or decrease performance.

<span id="page-8-1"></span>Table 4: Neural Architecture Search. Methods are compared in validation accuracy correlation and test accuracy on obtained architecture.

| <b>CVT</b>         | <b>APPNP</b><br>GCond / SGDD | Chebv<br>GCond / SGDD        | GCN<br>GCond / SGDD                    | <b>SAGE</b><br>GCond / SGDD                             | SGC<br>GCond / SGDD      |            | Pearson Correlation / Performance (%) |             |           | Whole       |
|--------------------|------------------------------|------------------------------|----------------------------------------|---------------------------------------------------------|--------------------------|------------|---------------------------------------|-------------|-----------|-------------|
| <b>APPNP</b>       | 60.3/60.21                   | $51.8/53.2$ <sup>+</sup>     | $59.9/62.4$ <sup>+</sup>               | $59.0 / 60.2$ <sup>+</sup>                              | 61.2/60.4                | Dataset    | Random                                | GCond       | SGDD      | Per. $(\%)$ |
| Cheby              | 57.4 / 58.51                 | 53.5 / 55.81                 | $57.4/65.3$ <sup>t</sup>               | 57.1/57.0                                               | $58.2/60.2$ <sup>+</sup> | Ogbn-arxiv | 0.63/71.1                             | 0.64/71.2   | 0.67/71.6 | 71.9        |
| GCN                | 59.3/60.11                   | 51.8/53.71                   | $60.3/64.2$ <sup>+</sup>               | $60.2 / 61.2$ <sup>+</sup>                              | 59.2 / 59.81             | YelpChi    | 0.43/56.7                             | 0.48/58.4   | 0.56/60.6 | 61.1        |
| <b>SAGE</b><br>SGC | 57.6 / 58.91<br>59.7/60.01   | 53.9 / 53.81<br>49.5 / 52.31 | 58.1/63.81<br>$59.2/62.2$ <sup>+</sup> | 57.8/61.8 <sup><math>\dagger</math></sup><br>58.9/62.91 | 59.0/61.11<br>60.5/61.51 | DBLP       | 0.58/81.2                             | 0.62 / 83.8 | 0.68/84.0 | 84.2        |

2-layer-MLP [\[11\]](#page-10-13), GAT [\[82\]](#page-14-3), APPNP [\[40\]](#page-12-6), Cheby [\[13\]](#page-10-4), GCN [\[39\]](#page-12-7), and SAGE [\[25\]](#page-11-1). To better understand, we also show several statistics metrics, including Avg., Std., and  $\Delta$ . SGDD and GCond improves GDC significantly, which indicates there exists a large difference between graph dataset condensation and vision dataset distillation, *i.e.*, the structure information should be specially considered. Compared to GCond, the improvement of our method is up to 11.3%, demonstrating the effectiveness of broadcasting the original structure to the condensed graph structure generation. More experiments on other datasets can be found in Appendix [C.6.](#page-23-1)

Versatility of SGDD. Following the setting of GCond [\[35\]](#page-12-5), we also study whether our proposed SGDD is robust on various architectures. We first condense the Ogbn-arxiv graph dataset with five architectures, including APPNP  $[40]$ , Cheby  $[13]$ , GCN  $[39]$ , SAGE  $[25]$ , and SGC  $[90]$ , respectively. Then, we evaluate these condensed graphs on the above five architectures and report their performances in Tab. [3.](#page-8-0) The experiment results show that SGDD achieves non-trivial improvements than GCond in most cases, which demonstrates the strong versatility of our method.

Evaluation on neural architecture search. Similar to vision dataset distillation, graph dataset distillation is also expected to reduce the high cost of neural architecture search (NAS). In order to make a fair comparison, we follow the experimental setting in [\[35\]](#page-12-5): searching architectures on condensed Obgn-arxiv, YelpChi, and DBLP datasets with 0.25%, 0.2%, and 0.5% condensing ratios. We report Pearson correlation [\[43\]](#page-12-16) and performance of random, GCond, and SGDD in Tab. [4.](#page-8-1) Our SGDD consistently achieves the highest Pearson correlations as well as performances, which indicates the architectures searched by our method are efficient for the whole graph dataset training.

Evaluation of components in SGDD. To explore the effect of the conditions (mentioned in Sec. [4.1\)](#page-4-4) in the condensed graph generation, we design the ablation study of  $X'$  and  $A$ . As shown in Fig [4\(a\),](#page-7-2) X' and A are complementary with each other. SGDD w/o A performs poorly on Obgn-arxiv and YelpChi datasets, which demonstrates the effect of original graph structure information. Jointly using  $\overline{X}'$  and A achieves the highest performances on both datasets, improves GCond with 3.1% on Ogbn-arxiv, and 8.0% on YelpChi.

### 5.4 Evaluation of the scalability of SGDD.

To investigate the scalability of SGDD, we evaluate the SGDD on various condensing ratios with  $r \in \{0.02, 0.2, 2, 5, 10, 20\}$ . As shown in Fig. [4\(b\),](#page-7-3) the performance of our method continuously increase as the condensing ratio rises, which indicates the strong scalability of our method. GCond obtains marginal improvements than GDC at all ratios while our SGDD outperforms them significantly. More important, SGDD achieves lossless performance as training on the original graph data when the condensing ratio is 10%.

Exploring the sensitivity of  $\alpha$  and  $\beta$ .  $\alpha$  is a parameter of  $\mathcal{L}_{structure}$  that reflects the weight of original graph structure. We conduct experiments to test its sensitivity on YelpChi, Cora, and Ogbnarxiv. As shown in Fig.  $4(c)$ , we empirically find that the performance of our SGDD is not sensitive to the  $\alpha$ . Specifically, compared to the case that  $\alpha$  is zero, we have a significant improvement, which proves the effectiveness of our method. Another finding is that the  $\alpha$  should be set higher on anomaly detection than on node classification tasks. It could be explained by the original graph structure information being more important on the complex task (such as anomaly detection). We define  $\beta$ as a regularization coefficient to control the sparsity of the condensed graph. As shown in Fig.  $4(d)$ , we evaluate the  $\beta$  from 0 to 10 on the YelpChi dataset. The results illustrate that the performance is not sensitive with  $\beta$  and achieve the highest result (F1-macro) when  $\beta$  is set to our default value  $(\beta = 0.1)$ . More experiments can be found in Appendix [C.5.](#page-22-0)

<span id="page-9-3"></span><span id="page-9-2"></span>Image /page/9/Figure/0 description: The image displays visualizations of graph data, comparing original and condensed versions of both a real dataset (Ogbn-arxiv) and a synthetic dataset. The top row shows node-link diagrams for the Ogbn-arxiv dataset: (a) the original graph with 0.0 SC and 64.1 ACC, colored red; (b) a version condensed by GCond with 0.31 SC and 64.1 ACC, showing nodes colored by community; and (c) a version condensed by SGDD with 0.22 SC and 67.3 ACC, also colored by community. The bottom row presents adjacency matrices for the synthetic dataset: (d) the original synthetic graph with 0.0 SC and no ACC specified, showing distinct blocks; (e) a version condensed by GCond with 0.45 SC and 66.5 ACC, appearing more uniformly colored; and (f) a version condensed by SGDD with 0.17 SC and 87.6 ACC, also showing distinct blocks. A color bar indicates values from 0.0 to 1.0 for the adjacency matrices.

<span id="page-9-5"></span><span id="page-9-4"></span><span id="page-9-1"></span><span id="page-9-0"></span>Figure 5: Visualizations of the real dataset (a), synthetic dataset (d), and the corresponding condensed graph obtained by GCond (b, e) and SGDD (c, f). The condensing ratio  $r$  is set to 0.5%.

#### 5.5 Visualizations

To better understand the effectiveness of our SGDD, we visualize the condensed graphs of GCond and SGDD that are synthesized from real and synthetic graph datasets. For the synthetic graph dataset, we use the Stochastic Block Model (SBM) [\[27\]](#page-11-14) to synthesize graphs with 5 community (Fig.  $5(d)$ ). As shown in Fig. [5,](#page-9-1) one can find that our method consistently achieves better performances and SC. SGDD reduces 29.0% (comparing [5\(b\)](#page-9-2) and [5\(c\)\)](#page-9-3) and 62.2% (comparing [5\(e\)](#page-9-4) and [5\(f\)\)](#page-9-5) SC on Ogbn-arxiv and synthetic datasets.

Visually, the condensed graphs of our method preserve the original graph structure information obviously better than GCond (see the second row of Fig. [5\)](#page-9-1), which proves SGDD is a powerful graph dataset distillation method.

# 6 Conclusion

We present SGDD, a novel framework for graph dataset distillation via broadcasting the original structure information to the generation of the synthetic one. SGDD shows its robustness on various tasks and datasets, achieving state-of-the-art results on YelpChi, Amazon, Ogbn-arxiv, and DBLP. SGDD reduces the scale of the Yelpchi dataset by 1,000 times while maintaining 98.6% as training on the original data. We provide sufficient experiments and theoretical analysis in this paper and hope it can help the following research in this area.

Limitations and future work: Although broadcasting the original information to the generated graph shows remarkable success, some informative properties (*e.g.*, the heterogeneity) may lose during the current condense process, which results in sub-optimal performance in the downstream tasks. We are going to explore a more general method in the future.

# Acknowledgement

The corresponding author is Jianxin Li. This work is supported by the NSFC through grant No.62225202. This research is supported by the National Research Foundation, Singapore under its AI Singapore Programme (AISG Award No: AISG2-PhD-2021-08-008). Yang You's research group is being sponsored by NUS startup grant (Presidential Young Professorship), Singapore MOE Tier-1 grant, ByteDance grant, ARCTIC grant, SMI grant, and Alibaba grant.

# References

- <span id="page-10-6"></span>[1] Muhammet Balcilar, Guillaume Renton, Pierre Héroux, Benoit Gaüzère, Sébastien Adam, and Paul Honeine. Analyzing the expressive power of graph neural networks in a spectral perspective. In *9th International Conference on Learning Representations, ICLR 2021, Virtual Event, Austria, May 3-7, 2021*, 2021.
- <span id="page-10-3"></span>[2] Peter W Battaglia, Jessica B Hamrick, Victor Bapst, Alvaro Sanchez-Gonzalez, Vinicius Zambaldi, Mateusz Malinowski, Andrea Tacchetti, David Raposo, Adam Santoro, Ryan Faulkner, et al. Relational inductive biases, deep learning, and graph networks. *ArXiv preprint*, 2018.
- <span id="page-10-1"></span>[3] Daniel Bear, Chaofei Fan, Damian Mrowca, Yunzhu Li, Seth Alter, Aran Nayebi, Jeremy Schwartz, Li Fei-Fei, Jiajun Wu, Josh Tenenbaum, and Daniel L. K. Yamins. Learning physical graph representations from visual scenes. In *Advances in Neural Information Processing Systems 33: Annual Conference on Neural Information Processing Systems 2020, NeurIPS 2020, December 6-12, 2020, virtual*, 2020.
- <span id="page-10-11"></span>[4] Joan Bruna, Wojciech Zaremba, Arthur Szlam, and Yann LeCun. Spectral networks and locally connected networks on graphs. In *2nd International Conference on Learning Representations, ICLR 2014, Banff, AB, Canada, April 14-16, 2014, Conference Track Proceedings*, 2014.
- <span id="page-10-5"></span>[5] Chen Cai, Dingkang Wang, and Yusu Wang. Graph coarsening with neural networks. In *9th International Conference on Learning Representations, ICLR 2021, Virtual Event, Austria, May 3-7, 2021*, 2021.
- <span id="page-10-8"></span>[6] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022.
- <span id="page-10-9"></span>[7] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3739–3748, 2023.
- <span id="page-10-14"></span>[8] Yu Chen, Lingfei Wu, and Mohammed J. Zaki. Iterative deep graph learning for graph neural networks: Better and robust node embeddings. In *Advances in Neural Information Processing Systems 33: Annual Conference on Neural Information Processing Systems 2020, NeurIPS 2020, December 6-12, 2020, virtual*, 2020.
- <span id="page-10-12"></span>[9] Marco Cuturi. Sinkhorn distances: Lightspeed computation of optimal transport. In *NIPS*, pages 2292–2300, 2013.
- <span id="page-10-2"></span>[10] Évariste Daller, Sébastien Bougleux, Luc Brun, and Olivier Lézoray. Local patterns and supergraph for chemical graph classification with convolutional networks. In *Structural, Syntactic, and Statistical Pattern Recognition - Joint IAPR International Workshop, S+SSPR 2018, Beijing, China, August 17-19, 2018, Proceedings*, volume 11004, pages 97–106, 2018.
- <span id="page-10-13"></span>[11] Tuan Van Dao, Hiroshi Sato, and Masao Kubo. Mlp-mixer-autoencoder: A lightweight ensemble architecture for malware classification. *Inf.*, 14(3):167, 2023.
- <span id="page-10-7"></span>[12] Kinkar Ch. Das, Seyed Ahmad Mojallal, and Vilmar Trevisan. Distribution of laplacian eigenvalues of graphs. *Linear Algebra and its Applications*, 508:48–61, 2016.
- <span id="page-10-4"></span>[13] Michaël Defferrard, Xavier Bresson, and Pierre Vandergheynst. Convolutional neural networks on graphs with fast localized spectral filtering. In *Advances in Neural Information Processing Systems 29: Annual Conference on Neural Information Processing Systems 2016, December 5-10, 2016, Barcelona, Spain*, 2016.
- <span id="page-10-10"></span>[14] Chenhui Deng, Zhiqiang Zhao, Yongyu Wang, Zhiru Zhang, and Zhuo Feng. Graphzoom: A multi-level spectral approach for accurate and scalable graph embedding. In *8th International Conference on Learning Representations, ICLR 2020, Addis Ababa, Ethiopia, April 26-30, 2020*, 2020.
- <span id="page-10-0"></span>[15] Holger Ebel and Stefan Bornholdt. Coevolutionary games on networks. *Physical Review E*, 66(5):056118, 2002.

- <span id="page-11-8"></span>[16] Justin Eldridge, Mikhail Belkin, and Yusu Wang. Graphons, mergeons, and so on! *Advances in Neural Information Processing Systems*, 29, 2016.
- <span id="page-11-12"></span>[17] Matthias Fey and JanEric Lenssen. Fast graph representation learning with pytorch geometric, Mar 2019.
- <span id="page-11-16"></span>[18] Luca Franceschi, Mathias Niepert, Massimiliano Pontil, and Xiao He. Learning discrete structures for graph neural networks. In *Proceedings of the 36th International Conference on Machine Learning, ICML 2019, 9-15 June 2019, Long Beach, California, USA*, Proceedings of Machine Learning Research, 2019.
- <span id="page-11-18"></span>[19] Alan Frieze and Ravi Kannan. Quick approximation to matrices and applications. *Combinatorica*, 19(2):175–220, 1999.
- <span id="page-11-7"></span>[20] Shuang Gao and Peter E Caines. Graphon control of large-scale networks of linear systems. *IEEE Transactions on Automatic Control*, 2019.
- <span id="page-11-3"></span>[21] Jiahui Geng, Zongxiong Chen, Yuandou Wang, Herbert Woisetschlaeger, Sonja Schimmler, Ruben Mayer, Zhiming Zhao, and Chunming Rong. A survey on dataset distillation: Approaches, applications and future directions. *ArXiv*, abs/2305.01975, 2023.
- <span id="page-11-6"></span>[22] Jianyang Gu, Kai Wang, Wei Jiang, and Yang You. Summarizing stream data for memoryrestricted online continual learning. *arXiv preprint arXiv:2305.16645*, 2023.
- <span id="page-11-5"></span>[23] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023.
- <span id="page-11-4"></span>[24] Ivan Gutman and Bo Zhou. Laplacian energy of a graph. *Linear Algebra and its Applications*, 414(1):29–37, 2006.
- <span id="page-11-1"></span>[25] William L. Hamilton, Zhitao Ying, and Jure Leskovec. Inductive representation learning on large graphs. In *NeurIPS*, 2017.
- <span id="page-11-0"></span>[26] Peter D Hoff, Adrian E Raftery, and Mark S Handcock. Latent space approaches to social network analysis. *Journal of the american Statistical association*, 97(460):1090–1098, 2002.
- <span id="page-11-14"></span>[27] Paul W Holland, Kathryn Blackmond Laskey, and Samuel Leinhardt. Stochastic blockmodels: First steps. *Social networks*, 5(2):109–137, 1983.
- <span id="page-11-11"></span>[28] Weihua Hu, Matthias Fey, Marinka Zitnik, Yuxiao Dong, Hongyu Ren, Bowen Liu, Michele Catasta, and Jure Leskovec. Open graph benchmark: Datasets for machine learning on graphs. In *NeurIPS*, 2020.
- <span id="page-11-13"></span>[29] Zengfeng Huang, Shengzhong Zhang, Chong Xi, Tang Liu, and Min Zhou. Scaling up graph neural networks via graph coarsening. In *In Proceedings of the 27th ACM SIGKDD Conference on Knowledge Discovery and Data Mining (KDD '21)*, 2021.
- <span id="page-11-10"></span>[30] Joseph J. Pfeiffer III, Sebastián Moreno, Timothy La Fond, Jennifer Neville, and Brian Gallagher. Attributed graph models: modeling network structure with correlated attributes. In *WWW*, 2014.
- <span id="page-11-9"></span>[31] Svante Janson. Graphons, cut norm and distance, couplings and rearrangements. *New York journal of mathematics*, 2013.
- <span id="page-11-15"></span>[32] Svante Janson and Persi Diaconis. Graph limits and exchangeable random graphs. *Rendiconti di Matematica e delle sue Applicazioni. Serie VII*, pages 33–61, 2008.
- <span id="page-11-17"></span>[33] Wei Jin, Yao Ma, Xiaorui Liu, Xianfeng Tang, Suhang Wang, and Jiliang Tang. Graph structure learning for robust graph neural networks. In *KDD*, 2020.
- <span id="page-11-2"></span>[34] Wei Jin, Xianfeng Tang, Haoming Jiang, Zheng Li, Danqing Zhang, Jiliang Tang, and Bing Yin. Condensing graphs via one-step gradient matching. In *Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, Aug 2022.

- <span id="page-12-5"></span>[35] Wei Jin, Lingxiao Zhao, Shichang Zhang, Yozen Liu, Jiliang Tang, and Neil Shah. Graph condensation for graph neural networks. In *ICLR*, 2022.
- <span id="page-12-13"></span>[36] David R. Karger. Random sampling in cut, flow, and network design problems. *Math. Oper. Res.*, 24(2):383–413, 1999.
- <span id="page-12-9"></span>[37] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. *arXiv:2205.14959*, 2022.
- <span id="page-12-0"></span>[38] Thomas N. Kipf and Max Welling. Semi-supervised classification with graph convolutional networks. In *5th International Conference on Learning Representations, ICLR 2017, Toulon, France, April 24-26, 2017, Conference Track Proceedings*. OpenReview.net, 2017.
- <span id="page-12-7"></span>[39] Thomas N. Kipf and Max Welling. Semi-supervised classification with graph convolutional networks. In *ICLR*, 2017.
- <span id="page-12-6"></span>[40] Johannes Klicpera, Aleksandar Bojchevski, and Stephan Günnemann. Predict then propagate: Graph neural networks meet personalized pagerank. In *ICLR 2019*, 2019.
- <span id="page-12-17"></span>[41] Eric D. Kolaczyk. *Statistical Analysis of Network Data: Methods and Models*. Springer, 2009.
- <span id="page-12-12"></span>[42] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *ICML*, 2022.
- <span id="page-12-16"></span>[43] Guanzhi Li, Aining Zhang, Qizhi Zhang, Di Wu, and Choujun Zhan. Pearson correlation coefficient-based performance enhancement of broad learning system for stock price prediction. *IEEE Trans. Circuits Syst. II Express Briefs*, 69(5):2413–2417, 2022.
- <span id="page-12-4"></span>[44] Jianxin Li, Qingyun Sun, Hao Peng, Beining Yang, Jia Wu, and S Yu Phillp. Adaptive subgraph neural network with reinforced critical structure mining. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023.
- <span id="page-12-14"></span>[45] Jianhua Lin. Divergence measures based on the shannon entropy. *IEEE Trans. Inf. Theory*, 37(1):145–151, 1991.
- <span id="page-12-1"></span>[46] Zhiqi Lin, Cheng Li, Youshan Miao, Yunxin Liu, and Yinlong Xu. Pagraph: Scaling gnn training on large graphs via computation-aware caching. In *Proceedings of the 11th ACM Symposium on Cloud Computing*, Oct 2020.
- <span id="page-12-11"></span>[47] Hanxiao Liu, Karen Simonyan, and Yiming Yang. DARTS: differentiable architecture search. In *ICLR*, 2019.
- <span id="page-12-8"></span>[48] Mengyang Liu, Shanchuan Li, Xinshi Chen, and Le Song. Graph condensation via receptive field distribution matching. *arXiv preprint arXiv:2206.13697*, 2022.
- <span id="page-12-10"></span>[49] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *arXiv preprint arXiv:2302.14416*, 2023.
- <span id="page-12-3"></span>[50] Andreas Loukas. Graph reduction with spectral and cut guarantees. *J. Mach. Learn. Res.*, 20:116:1–116:42, 2019.
- <span id="page-12-2"></span>[51] Andreas Loukas and Pierre Vandergheynst. Spectrally approximating large graphs with smaller graphs. In *Proceedings of the 35th International Conference on Machine Learning, ICML 2018, Stockholmsmässan, Stockholm, Sweden, July 10-15, 2018*, Proceedings of Machine Learning Research, 2018.
- <span id="page-12-15"></span>[52] László Lovász. *Large networks and graph limits*, volume 60. American Mathematical Soc., 2012.
- <span id="page-12-18"></span>[53] Dongsheng Luo, Wei Cheng, Wenchao Yu, Bo Zong, Jingchao Ni, Haifeng Chen, and Xiang Zhang. Learning to drop: Robust graph neural network via topological denoising. In *WSDM*, pages 779–787, 2021.

- <span id="page-13-9"></span>[54] Hermina Petric Maretic, Mireille El Gheche, Giovanni Chierchia, and Pascal Frossard. Got: An optimal transport framework for graph comparison. In *NeurIPS*, 2019.
- <span id="page-13-1"></span>[55] Brendan D. McKay, Mehmet Aziz Yirik, and Christoph Steinbeck. Surge: a fast open-source chemical graph generator. *J. Cheminformatics*, 2022.
- <span id="page-13-8"></span>[56] Facundo Mémoli. Spectral Gromov-Wasserstein distances for shape matching. In *ICCV Workshops*, pages 256–263, 2009.
- <span id="page-13-13"></span>[57] Matthew W. Morency and Geert Leus. Graphon filters: Graph signal processing in the limit. *IEEE Trans. Signal Process.*, 69:1740–1754, 2021.
- <span id="page-13-6"></span>[58] Bilal Nehme, Olivier Strauss, and Kevin Loquin. Estimating the variance of a kernel density estimation. In Christian Borgelt, Gil González-Rodríguez, Wolfgang Trutschnig, María Asunción Lubiano, María Ángeles Gil, Przemyslaw Grzegorzewski, and Olgierd Hryniewicz, editors, *Combining Soft Computing and Statistical Methods in Data Analysis, SMPS 2010, Oviedo, Spain, September 29 - October 1, 2010*, volume 77 of *Advances in Intelligent and Soft Computing*, pages 483–490. Springer, 2010.
- <span id="page-13-4"></span>[59] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *ICLR*, 2021.
- <span id="page-13-5"></span>[60] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *NeurIPS*, 34, 2021.
- <span id="page-13-2"></span>[61] Shirui Pan, Ruiqi Hu, Guodong Long, Jing Jiang, Lina Yao, and Chengqi Zhang. Adversarially regularized graph autoencoder for graph embedding. In *Proceedings of the Twenty-Seventh International Joint Conference on Artificial Intelligence, IJCAI 2018, July 13-19, 2018, Stockholm, Sweden*, 2018.
- <span id="page-13-15"></span>[62] Francesca Parise and Asuman Ozdaglar. Graphon games: A statistical framework for network games and interventions. *arXiv preprint arXiv:1802.00080*, 2018.
- <span id="page-13-16"></span>[63] Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, et al. Pytorch: An imperative style, high-performance deep learning library. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-13-3"></span>[64] David Peleg and Alejandro A. Schäffer. Graph spanners. *J. Graph Theory*, 13(1):99–116, 1989.
- <span id="page-13-0"></span>[65] Bryan Perozzi, Rami Al-Rfou, and Steven Skiena. Deepwalk: online learning of social representations. In *The 20th ACM SIGKDD International Conference on Knowledge Discovery and Data Mining, KDD '14, New York, NY, USA - August 24 - 27, 2014*, 2014.
- <span id="page-13-10"></span>[66] Shebuti Rayana and Leman Akoglu. Collective opinion spam detection: Bridging review networks and metadata. In *Proceedings of the 21th ACM SIGKDD International Conference on Knowledge Discovery and Data Mining*, KDD '15, page 985–994, New York, NY, USA, 2015. Association for Computing Machinery.
- <span id="page-13-7"></span>[67] Luana Ruiz, Luiz Chamon, and Alejandro Ribeiro. Graphon neural networks and the transferability of graph neural networks. *Advances in Neural Information Processing Systems*, 33:1702–1712, 2020.
- <span id="page-13-14"></span>[68] Luana Ruiz, Luiz F. O. Chamon, and Alejandro Ribeiro. The graphon fourier transform. In *ICASSP*, 2020.
- <span id="page-13-12"></span>[69] Luana Ruiz, Luiz FO Chamon, and Alejandro Ribeiro. Graphon signal processing. *IEEE Transactions on Signal Processing*, 69:4961–4976, 2021.
- <span id="page-13-11"></span>[70] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018.

- <span id="page-14-0"></span>[71] Cosma Rohilla Shalizi and Andrew C. Thomas. Homophily and contagion are generically confounded in observational social network studies. *CoRR*, abs/1004.4704, 2010.
- <span id="page-14-7"></span>[72] Chao Shang, Jie Chen, and Jinbo Bi. Discrete graph structure learning for forecasting multiple time series. In *International Conference on Learning Representations*, 2021.
- <span id="page-14-5"></span>[73] Mingjia Shi, Yuhao Zhou, Kai Wang, Huaizheng Zhang, Shudong Huang, Qing Ye, and Jiancheng Lv. PRIOR: Personalized prior for reactivating the information overlooked in federated learning. In *Proceedings of the 37th NeurIPS*, 2023.
- <span id="page-14-18"></span>[74] Vincent Sitzmann, Julien N. P. Martel, Alexander W. Bergman, David B. Lindell, and Gordon Wetzstein. Implicit neural representations with periodic activation functions. In *NeurIPS*, 2020.
- <span id="page-14-6"></span>[75] Daniel A. Spielman and Nikhil Srivastava. Graph sparsification by effective resistances. *SIAM J. Comput.*, 40(6):1913–1926, 2011.
- <span id="page-14-8"></span>[76] Daniel A. Spielman and Shang-Hua Teng. Spectral sparsification of graphs. *SIAM J. Comput.*, 40(4):981–1025, 2011.
- <span id="page-14-1"></span>[77] Qingyun Sun, Jianxin Li, Hao Peng, Jia Wu, Xingcheng Fu, Cheng Ji, and S Yu Philip. Graph structure learning with variational information bottleneck. In *AAAI*, volume 36, pages 4165–4174, 2022.
- <span id="page-14-2"></span>[78] Qingyun Sun, Jianxin Li, Hao Peng, Jia Wu, Yuanxing Ning, Philip S Yu, and Lifang He. Sugar: Subgraph neural network with reinforcement pooling and self-supervised mutual information mechanism. In *WWW 2021*, pages 2081–2091, 2021.
- <span id="page-14-9"></span>[79] Qingyun Sun, Jianxin Li, Haonan Yuan, Xingcheng Fu, Hao Peng, Cheng Ji, Qian Li, and Philip S Yu. Position-aware structure learning for graph topology-imbalance by relieving under-reaching and over-squashing. In *CIKM*, pages 1848–1857, 2022.
- <span id="page-14-11"></span>[80] Jianheng Tang, Jiajin Li, Ziqi Gao, and Jia Li. Rethinking graph neural networks for anomaly detection. In *ICML*, May 2022.
- <span id="page-14-14"></span>[81] Jie Tang, Jing Zhang, Limin Yao, Juanzi Li, Li Zhang, and Zhong Su. Arnetminer: Extraction and mining of academic social networks. In *Proceedings of the 14th ACM SIGKDD International Conference on Knowledge Discovery and Data Mining*, KDD '08, page 990–998, New York, NY, USA, 2008. Association for Computing Machinery.
- <span id="page-14-3"></span>[82] Petar Velickovic, Guillem Cucurull, Arantxa Casanova, Adriana Romero, Pietro Liò, and Yoshua Bengio. Graph attention networks. In *ICLR*, 2018.
- <span id="page-14-17"></span>[83] Renato Vizuete, Federica Garin, and Paolo Frasca. The laplacian spectrum of large graphs sampled from graphons. *IEEE Transactions on Network Science and Engineering*, page 1711–1721, Mar 2021.
- <span id="page-14-12"></span>[84] Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023.
- <span id="page-14-13"></span>[85] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022.
- <span id="page-14-10"></span>[86] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *ArXiv preprint*, 2018.
- <span id="page-14-4"></span>[87] Yili Wang, Kaixiong Zhou, Rui Miao, Ninghao Liu, and Xin Wang. Adagcl: Adaptive subgraph contrastive learning to generalize large-scale graph training.
- <span id="page-14-15"></span>[88] Max Welling. Herding dynamical weights to learn. In *ICML*, 2009.
- <span id="page-14-16"></span>[89] Gert W. Wolf. Facility location: concepts, models, algorithms and case studies. series: Contributions to management science. *Int. J. Geogr. Inf. Sci.*, 25(2):331–333, 2011.

- <span id="page-15-3"></span>[90] Felix Wu, Amauri H. Souza Jr., Tianyi Zhang, Christopher Fifty, Tao Yu, and Kilian Q. Weinberger. Simplifying graph convolutional networks. In *ICML*, 2019.
- <span id="page-15-0"></span>[91] Zonghan Wu, Shirui Pan, Fengwen Chen, Guodong Long, Chengqi Zhang, and Philip S Yu. A comprehensive survey on graph neural networks. *ArXiv preprint*, 2019.
- <span id="page-15-10"></span>[92] Xinyue Xia, Gal Mishne, and Yusu Wang. Implicit graphon neural representation. In *Proceedings of the 26th International Conference on Artificial Intelligence and Statistics, AISTATS*, Nov 2023.
- <span id="page-15-18"></span>[93] Hongteng Xu, Dixin Luo, Lawrence Carin, and Hongyuan Zha. Learning graphons via structured Gromov-Wasserstein barycenters. In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 35, pages 10505–10513, 2021.
- <span id="page-15-9"></span>[94] Shuo Yang, Zeke Xie, Hanyu Peng, Min Xu, Mingming Sun, and Ping Li. Dataset pruning: Reducing training data by examining generalization influence. *arXiv preprint arXiv:2205.09329*, 2022.
- <span id="page-15-14"></span>[95] Zhilin Yang, William W. Cohen, and Ruslan Salakhutdinov. Revisiting semi-supervised learning with graph embeddings. In *ICML*, JMLR Workshop and Conference Proceedings, 2016.
- <span id="page-15-11"></span>[96] W. Sawin Yihe Dong. Copt: Coordinated optimal transport on graphs. In *34th Conference on Neural Information Processing Systems, Vancouver, Canada*, 2020.
- <span id="page-15-1"></span>[97] Rex Ying, Ruining He, Kaifeng Chen, Pong Eksombatchai, William L. Hamilton, and Jure Leskovec. Graph convolutional neural networks for web-scale recommender systems. In *KDD*, 2018.
- <span id="page-15-2"></span>[98] Haiyang Yu, Limei Wang, Bokun Wang, Meng Liu, Tianbao Yang, and Shuiwang Ji. Graphfm: Improving large-scale gnn training via feature momentum. In *ICML*, Jun 2022.
- <span id="page-15-12"></span>[99] Hanqing Zeng, Hongkuan Zhou, Ajitesh Srivastava, Rajgopal Kannan, and Viktor K. Prasanna. Graphsaint: Graph sampling based inductive learning method. In *ICLR*, 2020.
- <span id="page-15-15"></span>[100] Muhan Zhang and Yixin Chen. Link prediction based on graph neural networks. In Samy Bengio, Hanna M. Wallach, Hugo Larochelle, Kristen Grauman, Nicolò Cesa-Bianchi, and Roman Garnett, editors, *NeurIPS*, pages 5171–5181, 2018.
- <span id="page-15-13"></span>[101] Shijie Zhang, Hongzhi Yin, Tong Chen, Quoc Viet Hung Nguyen, Zi Huang, and Lizhen Cui. Gcn-based user representation learning for unifying robust recommendation and fraudster detection. In *SIGIR*, pages 689–698. ACM, 2020.
- <span id="page-15-5"></span>[102] Yifan Zhang, Daquan Zhou, Bryan Hooi, Kai Wang, and Jiashi Feng. Expanding small-scale datasets with guided imagination. *arXiv preprint arXiv:2211.13976*, 2022.
- <span id="page-15-7"></span>[103] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021.
- <span id="page-15-4"></span>[104] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021.
- <span id="page-15-6"></span>[105] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *Ninth International Conference on Learning Representations 2021*, 2021.
- <span id="page-15-17"></span>[106] Tong Zhao, Yozen Liu, Leonardo Neves, Oliver Woodford, Meng Jiang, and Neil Shah. Data augmentation for graph neural networks. In *AAAI*, 2021.
- <span id="page-15-16"></span>[107] Cheng Zheng, Bo Zong, Wei Cheng, Dongjin Song, Jingchao Ni, Wenchao Yu, Haifeng Chen, and Wei Wang. Robust graph representation learning via neural sparsification. In *ICML*, pages 11458–11468, 2020.
- <span id="page-15-8"></span>[108] Daquan Zhou, Kai Wang, Jianyang Gu, Xiangyu Peng, Dongze Lian, Yifan Zhang, Yang You, and Jiashi Feng. Dataset quantization. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 17205–17216, 2023.

<span id="page-16-0"></span>[109] Jie Zhou, Ganqu Cui, Zhengyan Zhang, Cheng Yang, Zhiyuan Liu, Lifeng Wang, Changcheng Li, and Maosong Sun. Graph neural networks: A review of methods and applications. *ArXiv preprint*, 2018.

# A More Preliminary and Related Work

<span id="page-17-2"></span>

### A.1 More preliminary

**Graphon.** A graphon  $[41, 32, 52]$  $[41, 32, 52]$  $[41, 32, 52]$  $[41, 32, 52]$  $[41, 32, 52]$  is a bounded, symmetric, and Lebesgue measurable function, denote as  $W : \Omega^2 \to [0, 1]$ , where  $\Omega$  is a probability measure space. By randomly selecting points  $\{v_0, v_1, \ldots, v_n\}$  from  $\Omega$ , we can create a graph of any size by connecting each point with an edge weight  $e_{ij} = W(v_i, v_j)$ . Formally, we can follow the random sampling process to get arbitrarily sized  $(n)$  graphs :

$$
v_i \sim \text{Uniform}(\Omega), \text{ for } i = 1, \cdots, n,
$$
  
\n
$$
e_{ij} \sim \text{Bernoulli}(W(v_i, v_j)), \text{ for } i, j = 1, \cdots, n.
$$
 (10)

Conventionally, we use a deterministic setting to select the node, *e.g.*, the fixed grid  $v_i = \frac{i-1}{n}$ . Graphs derived from the same graphon share several important properties for statistical analysis: such as density [\[20\]](#page-11-7), clustering coefficient [\[67\]](#page-13-7), and degree distribution [\[69\]](#page-13-12), which motivate us to broadcast the original graph structure information to the condensed graph via the graphon approximating.

### A.2 More related work

Graph structure learning. Graph structure learning [\[18,](#page-11-16) [33,](#page-11-17) [8,](#page-10-14) [107,](#page-15-16) [53\]](#page-12-18) also aims at jointly optimizing the graph structure and the corresponding node features. Most of these methods learn the new structure based on the certain constraints (*e.g.*, low-rank [\[89\]](#page-14-16), sparsity [\[8\]](#page-10-14), feature smoothness [\[33\]](#page-11-17), and homophily  $[107]$ ). However, these methods are unable to learn a new structure with a significantly reduced node size. In contrast, our method can synthesize a smaller graph structure while simultaneously constructing new node features. The obtained small but informative graph can reduce the training cost in the downstream tasks.

# B Proofs

<span id="page-17-0"></span>

#### B.1 Proof of Proposition 1

*Proof.* First, the gradient matching objective in GCond [\[35\]](#page-12-5) can be shown as:

$$
\text{Match}(\nabla_{\theta} \mathcal{L}_{cls}(\text{GNN}_{\theta}(\mathbf{A}, \mathbf{X}), \mathbf{Y})), \quad \nabla_{\theta} \mathcal{L}_{cls}(\text{GNN}_{\theta}(\mathbf{A}', \mathbf{X}'), \mathbf{Y}')), \tag{11}
$$

where Match( $\cdot$ ) is the matching function that measures the distance of the two gradients.  $\theta$  represents the parameters of the GNN, the  $\nabla$  denotes the gradient in the backpropagation process, and the  $\mathcal{L}_{cls}$ represents the loss function that is used in the supervised tasks, *i.e.*, cross-entropy loss. Following the discussion in the  $[4, 1, 80]$  $[4, 1, 80]$  $[4, 1, 80]$  $[4, 1, 80]$  $[4, 1, 80]$ , the GNN can be viewed as a bandpass filter in the spectral domain. Expanding the GNN in the spectral domain, we can rewrite the objective as:

<span id="page-17-1"></span>
$$
\Big|\Big|\int_{i=0}^{N} \mathbf{U}_{\mathbf{A}} \operatorname{diag}(\mathrm{T}_{i}(\boldsymbol{\lambda}_{\mathbf{A}})) \mathbf{U}_{\mathbf{A}}^{\top} \mathbf{X} - \int_{j=1}^{N'} \mathbf{U}_{\mathbf{A}'} \operatorname{diag}(\mathrm{T}_{j}(\boldsymbol{\lambda}_{\mathbf{A}'})) \mathbf{U}_{\mathbf{A}'}^{\top} \mathbf{X}'\Big|\Big| \le \epsilon,
$$
 (12)

where the  $T(\lambda)$  denotes the frequency response of the given GNN, the  $\lambda$  and the U is the eigenvalue and eigenvector of the corresponding graph, respectively. Note we drop the  $\nabla$ , the  $\mathcal{L}_{cls}$ , and the Y  $(Y')$  terms because they can be viewed as the intermediate process  $[104, 106]$  $[104, 106]$  $[104, 106]$  in approximating to Eq.[\(12\)](#page-17-1). We use the MSE [\[35\]](#page-12-5) as the matching function  $\text{Match}(\cdot)$  for simplicity.

To simplify  $T(\lambda)$ , without loss of generality, we assume the bandwidth of the specific GNN's frequency response is (a, b), which intuitively indicates that only the signal of frequency in (a, b) can pass through the filter on the graph. Then the Eq. [\(12\)](#page-17-1) can be written as:

$$
\Big|\Big|\int_{i=0}^{N} \int_{k=a}^{b} \mathbf{U}_{\mathbf{A}} \operatorname{diag}(\mathbf{\lambda}_{\mathbf{A}}^{k}) \mathbf{U}_{\mathbf{A}}^{\top} \mathbf{X} - \int_{j=1}^{N'} \int_{k=a}^{b} \mathbf{U}_{\mathbf{A}'} \operatorname{diag}(\mathbf{\lambda}_{\mathbf{A}'}^{k}) \mathbf{U}_{\mathbf{A}'}^{\top} \mathbf{X}'\Big|\Big| \le \epsilon,
$$
 (13)

we further combine the first integration, the objective can thus be summarized as:

$$
\left| \left| \int_{k=a}^{b} \left( \mathbf{U}_{\mathbf{A}} \operatorname{diag}(\boldsymbol{\lambda}_{\mathbf{A}}^{k}) \mathbf{U}_{\mathbf{A}}^{\top} \mathbf{X} - \mathbf{U}_{\mathbf{A}} \operatorname{diag}(\boldsymbol{\lambda}_{\mathbf{A'}}^{k}) \mathbf{U}_{\mathbf{A'}}^{\top} \mathbf{X'} \right) \right| \right| \le \epsilon.
$$
 (14)

Then following the transformation in the spectral domain [\[1\]](#page-10-6), we can briefly summarized:

<span id="page-18-1"></span>
$$
\int_{i=a}^{b} \left| \left| \bar{x}_i^2 - \bar{x}_i^{'2} \right| \right| \le \epsilon.
$$
\n(15)

Take the Eq. [\(15\)](#page-18-1) into the  $\|\eta^{\mathcal{G}} - \eta^{\mathcal{S}}\|$ :

$$
||\eta^{\mathcal{G}} - \eta^{\mathcal{S}}|| = ||\sum_{i=1}^{N} \bar{x}_{i}^{2} - \sum_{i=j}^{N'} \bar{x}_{i}^{2}||
$$
  

$$
\geq \epsilon + ||\sum_{i=1}^{a} \bar{x}_{i}^{2} - \sum_{j=1}^{a} \bar{x}_{j}^{2}|| + ||\sum_{i=b}^{N} \bar{x}_{i}^{2} - \sum_{j=b}^{N'} \bar{x}_{j}^{2}|| \quad (16)
$$
  

$$
\geq \epsilon + \sum_{i=1}^{a} ||\bar{x}_{i}^{2} - \bar{x}_{i}^{\prime 2}|| + \sum_{i=b}^{N'} ||\bar{x}_{i}^{2} - \bar{x}_{i}^{\prime 2}||.
$$

Note the second inequality is under the assumption: intuitively, the overall distance of  $\eta^{\mathcal{G}}$  and  $\eta^{\mathcal{S}}$ should be larger than the condition that two graphs have the same size  $(i.e., N = N')$ .

<span id="page-18-0"></span>

#### B.2 Proof of Proposition 2

To prove Proposition 2, we first introduce the following notations and theorems in graphon theory.

The cut norm  $[19, 52]$  $[19, 52]$  $[19, 52]$  is defined as:

$$
||W||_{\Box} := \sup_{\mathcal{X}, \mathcal{Y} \subset \Omega} \left| \int_{\mathcal{X} \times \mathcal{Y}} W(x, y) \mathrm{d}x \mathrm{d}y \right|,\tag{17}
$$

where the  $\Omega$  is the probability space of a graphon W, the supremum is taken over all measurable subsets X and  $\mathcal{Y}$  [\[93\]](#page-15-18). Then the cut distance between  $W_1, W_2$  [\[52\]](#page-12-15) can be defined as:

$$
\delta_{\square} (W_1, W_2) := \inf_{\phi \in \mathcal{S}_{\Omega}} \left\| W_1 - W_2^{\phi} \right\|_{\square}, \tag{18}
$$

where the  $S_{\Omega}$  is the set of measure-preserving mappings from  $\Omega$  to  $\Omega$ . When two graphons  $W_1, W_2$ have  $\delta_{\square}(W_1, W_2) = 0$ , they can be seen equivalent, denoted as  $W_1 \cong W_2$ .

To effectively approximate W in the real world graphs, works  $[52, 31, 16]$  $[52, 31, 16]$  $[52, 31, 16]$  $[52, 31, 16]$  $[52, 31, 16]$  introduce an approximation named step function. Let  $\mathcal{P} = (\mathcal{P}_1, \ldots, \mathcal{P}_K)$  be a partition of  $\Omega$  into K measurable sets. The step function  $\tilde{W}_{\mathcal{P}} : \Omega^2 \mapsto [0,1]$  is defined as:

$$
W_{\mathcal{P}}(x,y) = \sum_{k,k'=1}^{K} w_{kk'} 1_{\mathcal{P}_k \times \mathcal{P}_{k'}}(x,y),
$$
\n(19)

where the  $w_{kk'} \in [0, 1]$  and the indicator function  $1_{\mathcal{P}_k \times \mathcal{P}_{k'}}(x, y)$  is 1 if  $(x, y) \in \mathcal{P}_k \times \mathcal{P}_{k'}$ , or it is 0.

To explore the relationship between step function and the  $W$ , we introduce the Weak Regularity Lemma as follows.

<span id="page-18-3"></span>**Theorem 1** (Weak Regularity Lemma [\[52\]](#page-12-15)). *For every graphon*  $W \in \mathcal{W}$  and  $K \leq 1$ , there always *exists a step function*  $W_{\mathcal{P}}$  *with*  $|\mathcal{P}| = K$  *steps such that* 

$$
||W - W_{\mathcal{P}}||_{\square} \le \frac{2}{\sqrt{\log K}} ||W||_{L_2}.
$$
\n(20)

We can further obtain the corollary that  $\delta_{\Box}(W, W_{\mathcal{P}}) \leq \frac{2}{\sqrt{\log K}} \|W\|_{L_2}$  as the  $\delta_{\Box}(W, W_{\mathcal{P}}) \leq$  $||W - W_{\mathcal{P}}||_{\square}$ . Intuitively, we can use any step function to approximate the ideal W of real graphs.

Then to investigate the properties of the graphon in the spectral domain, following [\[69,](#page-13-12) [83,](#page-14-17) [57,](#page-13-13) [68\]](#page-13-14), we have the conclusion that:

<span id="page-18-2"></span>
$$
||S_W - S_{W_{\mathcal{P}}}|| \le ||W - W_{\mathcal{P}}||_{\square},
$$
\n(21)

where the S denotes the signal spectrum of the graphs [\[83\]](#page-14-17) (*i.e.*, the post-Graph-Fourier-Transform  $U^{\top}X$ ), Intuitively, the Eq. [\(21\)](#page-18-2) shows that when the step function  $W_{\mathcal{P}}$  is approximated to the W, they have similar signal spectrum.

*Proof.* By combining the Theorem [1](#page-18-3) and Eq.[\(21\)](#page-18-2), the Proposition 2 replace the  $W_P$  with the  $W'_A$ , that's because we use the generative model (*i.e.*,  $GEN(\cdot)$ ) to approximate the step function  $W_{\mathcal{P}}$ . As we aim to learn the graphon of the original structure A, the graphon W can be rewrite as  $W_A$ ,

$$
||S_{W_{\mathbf{A}}}-S_{W_{\mathbf{A}}'}|| \le ||W_{\mathbf{A}}'-W_{\mathbf{A}}||_{\square}.
$$
\n(22)

We notice that the  $S$  here is related to the Laplacian energy distribution (LED), where the LED represents the probability distribution of the S, then we use the  $\sum_{i=1}^{N} \bar{x}_i$  and  $\sum_{j=1}^{N'} \bar{x}_j$  to represent the summation of the original signal spectrum and the condensed one, respectively, we have:

$$
||\eta^{\mathcal{G}} - \eta^{\mathcal{S}}|| = ||\frac{S_{W_{\mathbf{A}}}}{\sum_{i=1}^{N} \bar{x}_{i}} - \frac{S_{W_{\mathbf{A}'}}}{\sum_{j=1}^{N'} \bar{x}_{j}}||
$$
  

$$
\leq \max(\frac{1}{\sum_{i=1}^{N} \bar{x}_{i}}, \frac{1}{\sum_{j=1}^{N'} \bar{x}_{j}}) ||S_{W_{\mathbf{A}}} - S_{W_{\mathbf{A}'}}||
$$
  

$$
\leq ||W_{\mathbf{A}}' - W_{\mathbf{A}}||_{\square}, (23)
$$

where in the second inequality, we drop the max( $\cdot$ ) term since it always lower than 1. As the  $W_{\mathbf{A}'}$ here represents the graphon of the synthetic  $A'$ , we can use the  $A'$  directly in the  $\delta_{\Box}$  to form a compact upper bound.

$$
||\eta^{\mathcal{G}} - \eta^{\mathcal{S}}|| \le \delta_{\square}(\mathbf{A}', W_{\mathbf{A}}).
$$
\n(24)

$$
\Box
$$

Note that minimizing the upper bound has been proven to be equivalent to minimizing the optimal transport distance between the two graphs [\[93\]](#page-15-18).

### B.3 Time complexity analysis and running time

**Time complexity.** For simplicity, let the number of MLP layers in  $GEN(\cdot)$  be  $L$ , and all the hidden units are d. In the forward process, we have three steps: first, we calculate the A' by the  $GEN(\cdot)$ , which have the complexity of  $O(N'^2d^2)$ . Second, the forward process of GNN on the original graph has a complexity of  $\mathcal{O}(m^L N d^2)$ , where the m denotes the sampled size per node in training. Third, the complexity of training on the condensed graph is  $\mathcal{O}(LN<sup>t</sup>)$ . *In the backward process*, the complexity of gradient matching strategy (*i.e.*,  $\mathcal{L}_{feature}$ ) is  $\mathcal{O}(|\theta||\mathbf{X}'|)$  [\[35\]](#page-12-5). For the structure optimization term (*i.e.*,  $\mathcal{L}_{structure}$ ), the complexity is  $\mathcal{O}(N'^2k + NN'^2)$ . The overall complexity of SGDD can be represented as  $\mathcal{O}(N'^2d^2) + \mathcal{O}(m^LNd^2) + \mathcal{O}(LN'd) + \mathcal{O}(|\theta||X'|) + \mathcal{O}(N'^2k + N'^2N)$ . Note  $N' \ll N$ , we can drop the terms that only involve  $N'$  and constants (e.g., the number of L and m). The final complexity can be simplified as  $\mathcal{O}(m^L N d^2) + \mathcal{O}(N'^2 N)$ , thus the complexity of SGDD still be linear to the number of nodes in the original graph.

Running time. We report the running time of the SGDD in the two datasets: Ogbn-arxiv, and YelpChi. We vary the condensing ratio r in the range of  $\{0.05\%, 0.25\%, 0.50\%\}$  for Ogbn-arxiv and {0.05%, 0.10%, 0.20%} for YelpChi. All experiments are conducted five times on one single A100-SXM4 GPU. We also compare our results to those obtained using GCond [\[35\]](#page-12-5) under the same settings. As shown in the Tab. [5,](#page-19-0) our approach achieves a similar running time to GCond when the condensing ratio was low (*i.e.*,  $r = 0.05\%$  for both datasets), and is 10% faster when the condensing ratio increased. The difference can be explained by the efficient generative model we employed for generating structure, which prevents the consumption of time-complex operations such as calculating the pair-wised feature similarity  $(\mathcal{O}(N^2))$ .

<span id="page-19-0"></span>Table 5: Runing time on Ogbn-arxiv and YelpChi for 50 epochs.

| Dataset     | r     | GCond       | SGDD        | Dataset | r     | GCond       | SGDD       |
|-------------|-------|-------------|-------------|---------|-------|-------------|------------|
| Ogbon-arxiv | 0.05% | 315 $±1.8s$ | 308 $±1.6s$ | YelpChi | 0.05% | 67 $±2.6s$  | 47 $±2.8s$ |
|             | 0.25% | 413 $±2.6s$ | 374 $±3.2s$ |         | 0.10% | 96 $±2.8s$  | 74 $±1.7s$ |
|             | 0.50% | 527 $±2.7s$ | 467 $±2.1s$ |         | 0.20% | 110 $±0.8s$ | 93 $±2.6s$ |

# C Experimental Details and More Experiments

<span id="page-20-0"></span>

### C.1 Dataset statistics

We evaluate the proposed SGDD on nine datasets, including five node classification datasets: Cora [\[39\]](#page-12-7), Citeseer [39], Ogbn-arxiv [\[28\]](#page-11-11), Flickr [\[99\]](#page-15-12), and Reddit [\[25\]](#page-11-1); two anomaly detection datasets: YelpChi [\[66\]](#page-13-10) and Amazon [\[101\]](#page-15-13); two link prediction datasets Citeseer-L [\[95\]](#page-15-14) and DBLP [\[81\]](#page-14-14). We report the dataset statistics in Tab. [6.](#page-20-2)

Table 6: Dataset statics, including five node classification datasets, two anomaly detection datasets, and two link prediction datasets.

<span id="page-20-2"></span>

|    | <b>Datasets</b> | #Nodes  | #Edges     | #Classes | <b>#Features</b> |
|----|-----------------|---------|------------|----------|------------------|
| ND | Cora [39]       | 2,708   | 5,429      | 7        | 1,433            |
|    | Citeseer [39]   | 3,327   | 4,732      | 6        | 3,703            |
|    | Ogbn-arxiv [28] | 169,343 | 1,166,243  | 40       | 128              |
|    | Flickr [99]     | 89,250  | 899,756    | 7        | 500              |
|    | Reddit [25]     | 232,965 | 57,307,946 | 210      | 602              |
| AD | YelpChi [66]    | 45,954  | 3,846,979  | 2        | 32               |
|    | Amazon [101]    | 11,944  | 4,398,392  | 2        | 25               |
| LP | Citeseer-L [39] | 3,327   | 4,732      | 2        | 3,703            |
|    | DBLP [81]       | 26,128  | 105,734    | 2        | 4,057            |

Citeseer-L: We use the Citeseer in the link prediction setting, named Citeseer-L. We randomly sample 80% nodes in training, 10% nodes in validation, and the remaining 10% nodes for testing. The classes here denote "have edge" and "do not have edge".

DBLP: We treat the original graph as a homogeneous graph here.

<span id="page-20-1"></span>

### C.2 Implementation details

**Structure in GDC.** We utilize the DC [\[106\]](#page-15-17) as our baseline, and to incorporate the structure information, we add the constraint to produce a graph structure, named Graph DC (GDC). Specifically, we use the cosine similarity function [\[8\]](#page-10-14) (formally,  $A'_{ij} = \cos(X'_i, X'_j)$ ) to generate structure, where  $X'_{i}$  and  $X'_{j}$  are the learned features obtained through the vanilla gradient matching strategy.

 $GEN(\cdot)$  in SGDD. We introduce the  $GEN(\cdot)$  as our generative model in Sec. 4.1. Here, we show the implementation details of this module.

To start, we aim to find a method to broadcast the original graph structure  $A$  to condensed graph  $A'$ . Motivated by that all graphs with the same graphon  $W$  will exhibit similar properties, we can simply calculate the graphon  $W$  of  $A$  and leverage it in the condensing process. Nevertheless, directly calculating  $W$  of  $A$  is not feasible since conventional graphon learning methods should summarize graphon from a set of graphs  $[20, 31, 62]$  $[20, 31, 62]$  $[20, 31, 62]$  $[20, 31, 62]$  $[20, 31, 62]$  (We only have one graph A). Recent advancements of IGNR  $[92]$  demonstrate the potential of utilizing the generative approach to approximate the  $W$ . Specifically, given that the graphon W is defined as  $\Omega^2 \to [0, 1]$  (as described in Appendix [A.1\)](#page-17-2), we can similarly construct the function  $f$  as follows.

<span id="page-20-3"></span>
$$
f: \mathbb{R}^2 \to [0, 1], \tag{25}
$$

the continuous space  $\Omega^2$  is defined by  $\mathbb{R}^2$ . For computational convenience, the input space can further be limited to  $[0, 1]^2$  [\[92\]](#page-15-10), then the Eq. [\(25\)](#page-20-3) is transformed to sample points to reconstruct data, following IGNR[\[92\]](#page-15-10), we use SIREN[\[74\]](#page-14-18) as  $f$ ,

$$
\mathbf{h}_0 = \text{PostionalEncoding}(\mathcal{Z}(N')),\n\mathbf{h}_i = \text{Sin}(\mathbf{W}_i(\mathbf{h}_{i-1}) + \mathbf{b}_i), \ i = 1, \cdots, l-1,\n\mathbf{h}_l = \text{Sigmoid}(\mathbf{W}_l \mathbf{h}_{l-1} + \mathbf{b}_l),
$$
\n(26)

where the  $\mathcal{Z}(N') \in \mathbb{R}^{N' \times N'}$  is a random noise that plays as the coordinates, and the learnable weights  $\Phi = \{ \mathbf{W}_i \in \mathbb{R}^{l_i \times l_{i+1}}, \mathbf{b}_i \in \mathbb{R}^{l_i}, \text{ for } i = 1, \dots, l \}$  map the pair of points to the edge probability.  $\text{GEN}(\mathcal{Z}(N'); \Phi) \in \mathbb{R}^{N' \times N'}$  is equal to represent the adjacency matrix  $\mathbf{A}' \in \mathbb{R}^{N' \times N'}$ 

after transformation, where each entry represents a probability that each node pair should be connected. To incorporate the node information into the structure generation, we adopt them as conditional information that leads the generation process. We can then rewrite Eq.  $(25)$  to:

$$
f: \mathbb{R}^d \times \mathbb{R}^2 \to [0, 1], \tag{27}
$$

where the  $\mathbb{R}^d$  here is to present the conditional information, thus the learned model considers both the node's coordinates message along with the node's specific information. The basic implements can be:

$$
\mathbf{h}_{i} = \mathrm{MLP}_{i}(\mathbf{X}' \oplus \mathbf{Y}') \oplus \mathrm{Sin}(\mathbf{W}_{i}\mathbf{h}_{i-1} + \mathbf{b}_{i}), \quad i = 1, \cdots, l,
$$
\n(28)

where the  $\oplus$  denotes the concatenate operation, the Y' is treated as a one-hot vector for dimensionality fit through a multilayer perceptron (MLP). This method allows for the incorporation of significant node information into the resulting synthetic graph.

Condensation stage. For GCond [\[35\]](#page-12-5), we use the 2-layer SGC [\[90\]](#page-15-3) to serve as the condensing architecture with 256 units, and tune the number of epochs in a range of {400, 500, 6000, 1000,  $2000$ }. For GDC [\[106,](#page-15-17) [104\]](#page-15-4), we tune the number of hidden layers in the range of {1, 2, 3} and the number of hidden units in the range of  $\{128, 256\}$ . For SGDD, we use the GCN  $\{38\}$  as the default condensing architecture and tune the number of hidden layers in a range of {1, 2, 3}. We further tune the number of epochs in a range {400, 500, 600, 1000, 2000}, and tune the learning rate in a range of  $\{0.1, 0.01, 0.001, 0.0001\}.$ 

Evaluation stage. We set the training epoch to 1000 with an early stopping strategy for evaluating GNNs and set the dropout rate to 0 with the learning rate of 0.1.

Configurations. We conduct all experiments with:

- Operating System: Ubuntu 20.04 LTS.
- CPU: Intel(R) Xeon(R) Platinum 8358 CPU@2.60GHz with 1TB DDR4 of Memory.
- GPU: NVIDIA Tesla A100 SMX4 with 40GB of Memory.
- Software: CUDA 10.1, Python 3.8.12, PyTorch [\[63\]](#page-13-16) 1.7.0.

<span id="page-21-0"></span>

### C.3 Objective loss function and training algorithm

In this subsection, we present the objective function and provide a detailed training algorithm. Our objective is to jointly learn  $X'$  and  $A'$ . We follow GCond[\[35\]](#page-12-5) to optimize  $X'$  as a free parameter using the gradient matching strategy $[103]$ , the loss can be expressed by Eq. [\(29\)](#page-21-1),

<span id="page-21-1"></span>
$$
\mathcal{L}_{feature} = \text{Match}(\nabla_{\theta} \mathcal{L}_{cls}(\text{GNN}_{\theta}(\mathbf{A}, \mathbf{X}), \mathbf{Y})), \quad \nabla_{\theta} \mathcal{L}_{cls}(\text{GNN}_{\theta}(\mathbf{A}', \mathbf{X}'), \mathbf{Y}')).
$$
 (29)

Here,  $\text{Match}(\cdot)$  is the matching function that measures the distance of the two gradients, we use the MSE [\[35\]](#page-12-5) in practice.  $\theta$  represents the parameters of the specific backbone GNN, the  $\nabla$  denotes the gradient in the backpropagation process, and the  $\mathcal{L}_{cls}$  represents the loss function that is used in the supervised tasks, *i.e.*, cross-entropy loss. Our objective loss function can be written as:

$$
\mathcal{L} = \mathcal{L}_{feature} + \alpha \mathcal{L}_{structure} + \beta ||\mathbf{A}'||_2,
$$
\n(30)

where the  $\alpha$  controls the contribution of the  $L_{structure}$  term. To model the low-rank properties of real-world graphs, we use the  $||A'||_2$  as a regularity to control the sparsification of  $A'$  with  $\beta$ .

We summarize our pipeline in Algorithm. [1.](#page-22-1)

### C.4 Stochastic Block Model experments setting

In Sec. 5.4, we generate a synthetic graph dataset with different community structures using the Stochastic Block Model (SBM)  $(N, C, p, q)$  [\[27\]](#page-11-14). Here, we show the parameters settings, we set the number of nodes N to 100, while setting the number of communities C to 5. The parameter  $p$ represents the edge probability within the same community and  $q$  represents the edge probability between communities, we set them to 0.8 and 0.1 in practice, respectively.

# Algorithm 1: SGDD for Graph Condensation

<span id="page-22-1"></span>1 Input: Training data  $G = (\mathbf{A}, \mathbf{X}, \mathbf{Y})$ , pre-defined condensed labels  $\mathbf{Y}'$ <sup>2</sup> Initialize GEN as the structure learning model 3 Initialize  $X'$  by randomly selecet node feature from each class 4 for  $k = 0, ..., K - 1$  do 5 | Randomly initialize  $GNN_{\theta}$ 6 **for**  $t = 0, ..., T - 1$  do  $7 \mid D' = 0$ 8 **for**  $c = 0, ..., C - 1$  do 9 | | Initialize  $\mathcal{Z}(N')$ 10  $\Box$  Compute  $\mathbf{A}' = \text{GEN}(\mathcal{Z}(N') \oplus \mathbf{X}' \oplus \mathbf{Y}'; \Phi)$  then  $\mathcal{S} = \{\mathbf{A}', \mathbf{X}', \mathbf{Y}'\}$ 11 Sample  $(\mathbf{A}_c, \mathbf{X}_c, \mathbf{Y}_c) \sim \mathcal{G}$  and  $(\mathbf{A}'_c, \mathbf{X}'_c, \mathbf{Y}'_c) \sim \mathcal{S}$ 12 Compute  $\mathcal{L}_{structure}$   $\triangleright$  detailed in Eq. [\(9\)](#page-5-3)<br>  $\triangleright$  detailed in Eq. (9)<br>  $\triangleright$  detailed in Eq. (29) 13 Compute  $\mathcal{L}_{feature}$   $\triangleright$  detailed in Eq. [\(29\)](#page-21-1) 14  $\vert \quad \vert \quad D' \leftarrow D' + \mathcal{L}_{feature} + \alpha \mathcal{L}_{structure} + \beta ||A'||_2$ 15 **if**  $t\%(\tau_1 + \tau_2) < \tau_1$  then 16 | | Update  $\mathbf{X}' \leftarrow \mathbf{X}' - \eta_1 \nabla_{\mathbf{X}'} D'$ 17 else 18 | | Update  $\Phi \leftarrow \Phi - \eta_2 \nabla_{\Phi} D'$ 19 | Update  $\theta_{t+1} \leftarrow \text{opt}_{\theta}(\theta_t, \mathcal{S}, \tau_{\theta})$  $\triangleright \tau_{\theta}$  is the number of steps for updating  $\theta$ 20  $\mathbf{A}' = \text{GEN}(\mathcal{Z}(N') \oplus \mathbf{X}' \oplus \mathbf{Y}'; \Phi)$ 21  $\mathbf{A}'_{ij} = \mathbf{A}'_{ij}$  if  $\mathbf{A}'_{ij} > 0.5$ , otherwise 0 22 Return:  $(\mathbf{A}', \mathbf{X}', \mathbf{Y}')$ 

<span id="page-22-0"></span>

### C.5 More explorations of the sensitivity of $\beta$

In Fig. 4(d), we demonstrate that SGDD is not sensitive to the coefficient  $\beta$  on the YelpChi dataset. Here, we provide more experiments on the other datasets. In Fig. [6,](#page-22-2) we can see that increasing  $\beta$ leads to more sparsity of the condensed graph, and the corresponding performance does not have a severe drop. These observations demonstrate the effectiveness of the regularity term in our objective.

Image /page/22/Figure/4 description: The image displays seven line graphs, each illustrating the relationship between a parameter 'beta' on the x-axis and performance metrics (Sparsity/ACC, Sparsity/F1-macro, Sparsity/AUC) on the y-axis. Each graph is labeled with a dataset name: (a) Cora, (b) Citeseer, (c) Ogbn-arxiv, (d) Amazon, (e) DBLP, (f) Flickr, and (g) Reddit. For each dataset, two lines are plotted: 'ACC' (or 'AUC'/'F1-macro') and 'Sparsity'. The 'beta' values on the x-axis range from 0 to 10, with specific points at 0, 1e-3, 1e-2, 0.1, 1, and 10. The y-axis scales vary for each graph. In graph (a) Cora, ACC ranges from approximately 78% to 82%, and Sparsity ranges from approximately 82% to 94%. In graph (b) Citeseer, ACC ranges from approximately 70% to 74%, and Sparsity ranges from approximately 52% to 86%. In graph (c) Ogbn-arxiv, ACC ranges from approximately 64% to 68%, and Sparsity ranges from approximately 10% to 18%. In graph (d) Amazon, F1-macro ranges from approximately 82% to 86%, and Sparsity ranges from approximately 25% to 45%. In graph (e) DBLP, AUC ranges from approximately 75% to 82%, and Sparsity ranges from approximately 12% to 45%. In graph (f) Flickr, ACC ranges from approximately 44% to 47.2%, and Sparsity ranges from approximately 0.02 to 0.16. In graph (g) Reddit, ACC ranges from approximately 90.5% to 99%, and Sparsity ranges from approximately 0.01 to 0.22.

<span id="page-22-2"></span>Figure 6: Evaluations of  $\beta$  in seven datasets. For the Flickr and Reddit datasets, due to the datasets' low sparsity, we use the shared x-axis form for the Flickr and Reddit figures.

<span id="page-23-2"></span>Table 7: Comparison of the cross-architecture generalization performance between GCond and SGDD on YelpChi. **Bold entries** are the best results.  $\uparrow/\downarrow$ : our method show increase or decrease performance.

|              | <b>APPNP</b>                                 | Cheby                    | <b>GCN</b>                                  | <b>SAGE</b>                                 | <b>SGC</b>                                  |
|--------------|----------------------------------------------|--------------------------|---------------------------------------------|---------------------------------------------|---------------------------------------------|
| C/T          | GCond / SGDD                                 | GCond / SGDD             | GCond / SGDD                                | GCond / SGDD                                | GCond / SGDD                                |
| <b>APPNP</b> | 48.1 / 55.1                                  | 46.5/57.4                | 50.1/58.6 <sup><math>\dagger</math></sup>   | 46.7/57.1                                   | 49.6 / 57.6 <sup><math>\dagger</math></sup> |
| <b>Cheby</b> | 48.0 / 56.2                                  | $45.9/56.8$ <sup>†</sup> | 49.8 / 58.7 <sup><math>\dagger</math></sup> | $46.8 / 58.3$ <sup>+</sup>                  | 49.8 / 58.4 <sup><math>\dagger</math></sup> |
| <b>GCN</b>   | 47.6 / 56.5 <sup><math>\uparrow</math></sup> | 46.6 / 56.8              | $48.6 / 59.7 \text{°}$                      | 47.4/57.6                                   | 50.1 / 57.4 $\dagger$                       |
| <b>SAGE</b>  | 46.7/57.6                                    | 46.8 / 57.5              | $48.9/58.7$ <sup>+</sup>                    | 48.6 / 58.6 <sup><math>\dagger</math></sup> | 48.9 / 58.6 <sup><math>\dagger</math></sup> |
| <b>SGC</b>   | 47.6/57.6                                    | 47.7/57.2                | $48.6/57.8$ <sup>+</sup>                    | 47.4/59.0                                   | 48.7/57.6                                   |

<span id="page-23-1"></span>

### C.6 More cross-architecture experiments

In this subsection, we further report the results of cross-architecture experiments on the YelpChi. As shown in Tab. [7,](#page-23-2) our SGDD improves the performance in all cases, the average improvements compared to the GCond [\[35\]](#page-12-5) is 9.6%, which indicates the strong generalization performance of the condensed graph by SGDD.

<span id="page-23-0"></span>

#### C.7 Ablation of the sampling operation in OT distance

To further reduce condensing time and memory usage, we follow GCond [\[35\]](#page-12-5) to sample from original graph  $\bf{A}$  in the condensing stage (line 11 in the Algorithm [1\)](#page-22-1). We evaluate the SGDD on various sample sizes, *i.e.*, {100, 500, 1000, 2000, 5000}, and report the corresponding performance and SC. As shown in Fig. [7,](#page-23-3) with the increase in the number of nodes, the performance obtains marginal improvements while the  $SC$  is decreasing. However, larger sample sizes are not always beneficial to performance. In this study, the highest results are obtained when the sample size is 2000. This result empirically demonstrates the scalability of the structure learning module. Specifically, the module enables efficient condensing on large graphs.

Image /page/23/Figure/6 description: The image displays three line graphs, each plotting performance metrics against the number of nodes. Graph (a), labeled 'Ogbn-arxiv', shows 'ACC (%)' and 'SC' on the left and right y-axes, respectively. The x-axis represents the 'Number of Nodes' with values 100, 500, 1000, 2000, and 5000. The ACC line, marked with red triangles, starts at approximately 66.6% for 100 nodes, rises to about 67.2% for 1000 nodes, and reaches around 67.4% for 5000 nodes. The SC line, marked with blue triangles, starts at approximately 66.7% for 100 nodes, drops to about 66.2% for 1000 nodes, and ends around 66.1% for 5000 nodes. Graph (b), labeled 'YelpChi', plots 'F1-marco (%)' and 'SC' on the left and right y-axes. The x-axis is the same as in graph (a). The F1-marco line, with red triangles, starts at about 59% for 100 nodes, increases to approximately 62.2% for 1000 nodes, and stays around 62.2% for 5000 nodes. The SC line, with blue triangles, starts at about 55.2% for 100 nodes, slightly decreases to around 54.2% for 1000 nodes, and ends at approximately 54.2% for 5000 nodes. Graph (c), labeled 'DBLP', plots 'AUC (%)' and 'SC' on the left and right y-axes. The x-axis is again the same. The AUC line, with red triangles, starts at about 81.1% for 100 nodes, rises to approximately 82.2% for 2000 nodes, and ends around 82.2% for 5000 nodes. The SC line, with blue triangles, starts at about 81.9% for 100 nodes, drops to around 81.5% for 500 nodes, and then fluctuates between 81.5% and 81.8% for subsequent points.

<span id="page-23-3"></span>Figure 7: Evaluation of the number of sampled nodes to the performance.

### C.8 More discussion of the condensed graphs.

We next show the comparison of condensed graphs and original graphs. As shown in Tab. [8,](#page-24-0) the condensed graphs obviously have fewer nodes and edges, while maintaining comparable accuracy, which shows the effectiveness of our SGDD . We also represent the visualizations of some condensed graphs. In Fig. [8,](#page-24-1) the black lines denote that edge weights are larger than 0.5 and the gray lines represent as smaller than 0.5. We can observe that there are some patterns in the condensed graph, *e.g.*, the homophily patterns on Cora and Citeseer. However, for the remaining datasets, the visualizations are inadequate in revealing the properties of the condensed graph, which proves the superiority of analyzing structure in spectral view.

|             | Citeseer, r=0.9% |      | Cora, r=1.3% |      | Ogbn-arxiv, r=0.5% |      | Flickr, r=0.1% |      | Reddit, r=0.1% |      |
|-------------|------------------|------|--------------|------|--------------------|------|----------------|------|----------------|------|
|             | Whole            | SGDD | Whole        | SGDD | Whole              | SGDD | Whole          | SGDD | Whole          | SGDD |
| Accuracy    | 70.7             | 69.5 | 81.5         | 79.6 | 71.4               | 65.3 | 47.1           | 47.1 | 94.1           | 90.5 |
| #Nodes      | 3k3              | 60   | 2k7          | 70   | 169k               | 454  | 44k            | 44   | 153k           | 153  |
| #Edges      | 4k7              | 1k   | 5k4          | 2k   | 1,166k             | 8k6  | 218k           | 331  | 10,753k        | 3k   |
| Storage(MB) | 47.1             | 0.8  | 14.9         | 0.4  | 100.4              | 1.0  | 86.8           | 0.1  | 435.5          | 0.7  |

<span id="page-24-0"></span>Table 8: The comparison between condensed graphs and original graphs.

Image /page/24/Figure/2 description: The image displays four network graphs labeled (a) Cora, (b) Citeseer, (c) Ogbn-arxiv, and (d) Flickr. Each graph consists of multiple colored nodes connected by gray lines, representing edges. The nodes in each graph are colored with a variety of pastel shades including pink, purple, blue, green, yellow, and orange. The graphs vary in density and structure, with Cora and Flickr appearing more structured and Citeseer and Ogbn-arxiv appearing more randomly distributed.

<span id="page-24-1"></span>Figure 8: Visualizations of condensed graphs, the different colors on the graphs denote the classes.
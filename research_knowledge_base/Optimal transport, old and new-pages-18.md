$$
d(\overline{x}, \overline{x}'_k) \le \delta; \quad T_k(\overline{x}'_k) = y_k \in C; \quad y_k \in \partial_c \psi(x_k);
$$
  
\n
$$
d(x_k, x'_k) \le 2\delta; \quad d(T_k(x'_k), \overline{y}_1) \le \delta.
$$
\n(12.14)

By c-cyclical monotonicity of optimal transport and Lemma 12.2,

$$
c(\overline{x}'_k, T_k(\overline{x}'_k)) + c(x'_k, T_k(x'_k)) \leq c(\overline{x}'_k, T_k(x'_k)) + c(x'_k, T_k(\overline{x}'_k)).
$$

From the inequalities in (12.14) and (12.11), we deduce

$$
c(\overline{x}, y_k) + c(x_k, \overline{y}_1) \le c(\overline{x}, \overline{y}_1) + c(x_k, y_k) + \frac{4\,\varepsilon}{10}.
$$

Since  $y_k \in C \cap \partial_c \psi(x_k)$  and  $x_k \in K$ , this contradicts (12.10). The proof is complete. is complete. ⊓⊔

# Regular cost functions

In the previous section we dealt with plainly continuous cost functions; now we shall come back to the differentiable setting used in Chapter 10 for the solution of the Monge problem. Throughout this section,  $\mathcal{X}$  will be a closed subset of a Riemannian manifold M and  $Dom(\nabla_x c)$  will stand for the set of points  $(x,y) \in \mathcal{X} \times \mathcal{Y}$  such that  $\nabla_x c(x,y)$  is welldefined. (A priori x should belong to the interior of  $\mathcal X$  in M.) It will be assumed that  $\nabla_x c(x, \cdot)$  is one-to-one on its domain of definition (Assumption (Twist) in Chapter 10).

**Definition 12.10 (c-segment).** A continuous curve  $(y_t)_{t\in[0,1]}$  in  $\mathcal{Y}$ is said to be a c-segment with base  $\bar{x}$  if (a)  $(\bar{x}, y_t) \in \mathrm{Dom}(\nabla_x c)$  for all t; (b) there are  $p_0, p_1 \in T_{\overline{x}}M$  such that  $\nabla_x c(\overline{x}, y_t) + p_t = 0$ , where  $p_t = (1-t) p_0 + t p_1.$ 

In other words a c-segment is the image of a usual segment by a map  $(\nabla_x c(\overline{x}, \cdot))^{-1}$ . Since  $(\overline{x}, y_t)$  in the definition always lies in Dom  $(\nabla_x c)$ , a c-segment is uniquely determined by its base point  $\bar{x}$  and its endpoints  $y_0, y_1$ ; I shall denote it by  $[y_0, y_1]_{\overline{x}}$ .

Definition 12.11 (c-convexity). A set  $C \subset \mathcal{Y}$  is said to be c-convex with respect to  $\overline{x} \in \mathcal{X}$  if for any two points  $y_0, y_1 \in C$  there is a c-segment  $(y_t)_{0 \le t \le 1} = [y_0, y_1]_{\overline{x}}$  (necessarily unique) which is entirely contained in C.

More generally, a set  $C \subset \mathcal{Y}$  is said to be c-convex with respect to a subset X of X if C is c-convex with respect to any  $x \in \mathcal{X}$ .

A set  $D \subset \text{Dom}(\nabla_x c)$  is said to be totally c-convex if for any two points  $(\overline{x},y_0)$  and  $(\overline{x},y_1)$  in D, there is a c-segment  $(y_t)_{0 \le t \le 1}$  with base  $\overline{x}$ , such that  $(\overline{x}, y_t) \in D$  for all t.

Similar notions of strict c-convexity are defined by imposing that  $y_t$ belong to the interior of C if  $y_0 \neq y_1$  and  $t \in (0,1)$ .

**Example 12.12.** When  $\mathcal{X} = \mathcal{Y} = \mathbb{R}^n$  and  $c(x, y) = -x \cdot y$  (or  $x \cdot y$ ), cconvexity is just plain convexity in  $\mathbb{R}^n$ . If  $\mathcal{X} = \mathcal{Y} = S^{n-1}$  and  $c(x, y) =$  $d(x,y)^2/2$  then Dom  $(\nabla_x c(\overline{x},\cdot)) = S^{n-1} \setminus \{-\overline{x}\}\)$  (the cut locus is the antipodal point), and  $\nabla_x c(\overline{x}, S^{n-1}\setminus\{-\overline{x}\}) = B(0,\pi) \subset T_{\overline{x}}M$  is a convex set. So for any point  $\overline{x}$ ,  $S^{n-1}$  minus the cut locus of  $\overline{x}$  is c-convex with respect to  $\overline{x}$ . An equivalent statement is that  $S^{n-1} \times S^{n-1} \setminus \{(x, -x)\}$ (which is the whole of Dom  $(\nabla_x c)$ ) is totally  $d^2/2$ -convex. By abuse of language, one may say that  $S^{n-1}$  is  $d^2/2$ -convex with respect to itself. The same is true of a flat torus with arbitrary sidelengths.

Example 12.13. To construct a Riemannian manifold which is not  $(d^2/2)$ -convex with respect to itself, start with the Euclidean plane (embedded in  $\mathbb{R}^3$ ), and from the origin draw three lines  $L_-, L_0$  and  $L_+$ directed respectively by the vectors  $(1, -1)$ ,  $(1, 0)$  and  $(1, 1)$ . Put a high enough mountain on the pathway of  $L_0$ , without affecting  $L_+$ or  $L_$ , so that  $L_0$  is minimizing only for a finite time, while  $L_{\pm}$  are still minimizing at all times. The resulting surface is not  $d^2/2$ -convex with respect to the origin.

Before stating the main definition of this section, I shall now introduce some more notation. If  $\mathcal X$  is a closed subset of a Riemannian manifold M and  $c: \mathcal{X} \to \mathcal{Y} \to \mathbb{R}$  is a continuous cost function, for any x in the interior of X I shall denote by  $Dom'(\nabla_x c(x, \cdot))$  the *in*terior of Dom  $(\nabla_x c(x, \cdot))$ . Moreover I shall write Dom' $(\nabla_x c)$  for the union of all sets  $\{x\} \times \text{Dom}'(\nabla_x c(x, \cdot))$ , where x varies in the interior of X. For instance, if  $X = Y = M$  is a complete Riemannian manifold and  $c(x, y) = d(x, y)^2$  is the square of the geodesic distance, then  $Dom'(\nabla_x c)$  is obtained from  $M \times M$  by removing the cut locus, while Dom  $(\nabla_x c)$  might be slightly bigger (these facts are recalled in the Appendix).

Definition 12.14 (regular cost function). A cost  $c : \mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  is said to be regular if for any  $\overline{x}$  in the interior of X and for any c-convex

function  $\psi : \mathcal{X} \to \mathbb{R}$ , the set  $\partial_c \psi(\overline{x}) \cap \text{Dom}'(\nabla_x c(\overline{x}, \cdot))$  is c-convex with respect to  $\overline{x}$ .

The cost c is said to be strictly regular if moreover, for any nontrivial c-segment  $(y_t)_{0 \le t \le 1} = [y_0, y_1]_{\overline{x}}$  in  $\partial_c \psi(\overline{x})$  and for any  $t \in (0, 1)$ ,  $\overline{x}$  is the only contact point of  $\psi^c$  at  $y_t$ , i.e. the only  $x \in \mathcal{X}$  such that  $y_t \in \partial_c \psi(x)$ .

More generally, if D is a subset of  $Dom'(\nabla_x c)$ , I shall say that c is regular in D if for any  $\bar{x}$  in  $\text{proj}_{\mathcal{X}} D$  and any c-convex function  $\psi$ :  $\mathcal{X} \to \mathbb{R}$ , the set  $\partial_c \psi(\overline{x}) \cap \{y; (\overline{x}, y) \in D\}$  is c-convex. Equivalently, the intersection of D and the graph of  $\partial_c \psi$  should be totally c-convex. The notion of strict regularity in  $D$  is obtained by modifying this definition in an obvious way.

What does regularity mean? Let  $\psi$  be a c-convex function and let  $-c(\cdot, y_0) + a_0$  and  $-c(\cdot, y_1) + a_1$  touch the graph of  $\psi$  from below at  $\overline{x}$ , take any  $y_t \in [y_0, y_1]_{\overline{x}}$ ,  $0 < t < 1$ , and increase a from  $-\infty$  until the function  $-c(\cdot, y_t) + a$  touches the graph of  $\psi$ : the regularity property means that  $\bar{x}$  should be a contact point (and the only one if the cost is strictly regular).

Before going further I shall discuss several convenient reformulations of the regularity property, in terms of (i) elementary c-convex functions; (ii) subgradients; (iii) connectedness of c-subdifferentials. Assumptions (Twist) (twist condition), (locSC) (local semiconcavity) and  $(H\infty)$  (adequate behavior at infinity) will be the same as in Chapter 10 (cf. p. 246).

**Proposition 12.15 (Reformulation of regularity).** Let X be a closed subset of a Riemannian manifold  $M$  and let  $\mathcal Y$  be a Polish space. Let  $c : \mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  be a continuous cost function satisfying (Twist). Then:

(i) c is regular if and only if for any  $(\overline{x}, y_0)$ ,  $(\overline{x}, y_1) \in \text{Dom}'(\nabla_x c)$ , the c-segment  $(y_t)_{0 \le t \le 1} = [y_0, y_1]_{\overline{x}}$  is well-defined, and for any  $t \in [0, 1]$ ,

$$
-c(x, y_t) + c(\overline{x}, y_t) \le \max\Bigl(-c(x, y_0) + c(\overline{x}, y_0), -c(x, y_1) + c(\overline{x}, y_1)\Bigr) \tag{12.15}
$$

(with strict inequality if c is strictly regular,  $y_0 \neq y_1$ ,  $t \in (0,1)$  and  $\overline{x} \neq x$ .

(ii) If c is a regular cost function satisfying (locSC) and  $(H\infty)$ , then for any c-convex  $\psi : \mathcal{X} \to \mathbb{R}$  and any  $\overline{x}$  in the interior of X such that  $\partial_c \psi(\overline{x}) \subset \text{Dom}'(\nabla_x c(\overline{x}, \cdot))$ , one has

$$
\nabla_c^- \psi(\overline{x}) = \nabla^- \psi(\overline{x}),
$$

where the c-subgradient  $\nabla_{c}^{-}\psi(\overline{x})$  is defined by

$$
\nabla^-_c\psi(\overline{x}):=-\nabla_x c(\overline{x},\partial_c\psi(\overline{x})).
$$

(iii) If c satisfies (locSC) and Dom' $(\nabla_x c)$  is totally c-convex, then c is regular if and only if for any c-convex  $\psi : \mathcal{X} \to \mathbb{R}$  and any  $\overline{x}$  in the interior of  $\mathcal{X}$ ,

$$
\partial_c \psi(\overline{x}) \cap \text{Dom}'(\nabla_x c(\overline{x}, \cdot)) \text{ is connected.} \tag{12.16}
$$

Remark 12.16. Statement (i) in Proposition 12.15 means that it is sufficient to test Definition 12.14 on the particular functions

$$
\psi_{\overline{x},y_0,y_1}(x) := \max(-c(x,y_0) + c(\overline{x},y_0), -c(x,y_1) + c(\overline{x},y_1)), \quad (12.17)
$$

where  $(\overline{x}, y_0)$  and  $(\overline{x}, y_1)$  belong to Dom'( $\nabla_x c$ ). (The functions  $\psi_{\overline{x}, y_0, y_1}$ play in some sense the role of  $x \to |x_1|$  in usual convexity theory.) See Figure 12.3 for an illustration of the resulting "recipe".

**Example 12.17.** Obviously  $c(x, y) = -x \cdot y$ , or equivalently  $|x - y|^2$ , is a regular cost function, but it is not strictly regular. The same is true of  $c(x, y) = -|x - y|^2$ , although the qualitative properties of optimal transport are quite different in this case. We shall consider other examples later.

Remark 12.18. It follows from the definition of c-subgradient and Theorem 10.24 that for any  $\bar{x}$  in the interior of X and any c-convex  $\psi: \mathcal{X} \to \mathbb{R},$ 

$$
\nabla_c^- \psi(\overline{x}) \subset \nabla^- \psi(\overline{x}).
$$

So Proposition 12.15(ii) means that, modulo issues about the domain of differentiability of  $c$ , it is equivalent to require that  $c$  satisfies the regularity property, or that  $\nabla_{c}^{-}\psi(\overline{x})$  fills the whole of the convex set  $\nabla^- \psi(\overline{x}).$ 

Remark 12.19. Proposition 12.15(iii) shows that, again modulo issues about the differentiability of  $c$ , the regularity property is morally equivalent to Assumption (C) in Chapter 9. See Theorem 12.42 for more.

Image /page/4/Figure/1 description: The image displays a graph with a thick black line representing a function that has two peaks and a valley in between. Three dashed vertical lines are labeled y0, y1/2, and y1, indicating specific points on the x-axis. Below the main curve, several thinner curved lines are drawn, all arching upwards and converging towards the valley of the main curve. A label 'x' with a bar over it is positioned below the lowest point of these thinner curves.

Fig. 12.3. Regular cost function. Take two cost-shaped mountains peaked at  $y_0$ and  $y_1$ , let  $\bar{x}$  be a pass, choose an intermediate point  $y_t$  on  $(y_0, y_1)_{\bar{x}}$ , and grow a mountain peaked at  $y_t$  from below; the mountain should emerge at  $\overline{x}$ . (Note: the shape of the mountain is the negative of the cost function.)

Proof of Proposition 12.15. Let us start with (i). The necessity of the condition is obvious since  $y_0, y_1$  both belong to  $\partial_c \psi_{\overline{x}, y_0, y_1}(\overline{x})$ . Conversely, if the condition is satisfied, let  $\psi$  be any c-convex function  $\mathcal{X} \to \mathbb{R}$ , let  $\overline{x}$  belong to the interior of X and let  $y_0, y_1 \in \partial_c \psi(\overline{x})$ . By adding a suitable constant to  $\psi$  (which will not change the subdifferential), we may assume that  $\psi(\overline{x}) = 0$ . Since  $\psi$  is c-convex,  $\psi \geq \psi_{\overline{x},y_0,y_1}$ , so for any  $t \in [0, 1]$  and  $x \in \mathcal{X}$ ,

$$
c(x, y_t) + \psi(x) \geq c(x, y_t) + \psi_{\overline{x}, y_0, y_1}(x)
$$
  
 
$$
\geq c(\overline{x}, y_t) + \psi_{\overline{x}, y_0, y_1}(\overline{x}) = c(\overline{x}, y_t) + \psi(\overline{x}),
$$

which shows that  $y_t \in \partial_c \psi(\overline{x})$ , as desired.

Now let c,  $\psi$  and  $\bar{x}$  be as in Statement (ii). By Theorem 10.25,  $-\nabla_x c(\overline{x},\partial_c\psi(\overline{x}))$  is included in the convex set  $\nabla^-\psi(\overline{x})$ . Moreover,  $\psi$  is locally semiconvex (Theorem 10.26), so  $\nabla^-\psi(\overline{x})$  is the convex hull of cluster points of  $\nabla \psi(x)$ , as  $x \to \overline{x}$ . (This comes from a localization argument and a similar result for convex functions, recall Remark 10.51.) It follows that any extremal point p of  $\nabla^- \psi(\overline{x})$  is the limit of  $\nabla \psi(x_k)$ for some sequence  $x_k \to \overline{x}$ . Then let  $y_k \in \partial_c \psi(x_k)$  (these  $y_k$  exist by Theorem 10.24 and form a compact set). By Theorem 10.25,  $\nabla_x c$  is well-defined at  $(x_k, y_k)$  and  $-\nabla c(x_k, y_k) = \nabla \psi(x_k)$ . Up to extraction of a subsequence, we have  $y_k \to y \in \partial_c \psi(\overline{x})$ , in particular  $(\overline{x}, y)$  lies in the domain of  $\nabla_x c$  and  $-\nabla c(\overline{x},y) = p$ . The conclusion is that

$$
\mathcal{E}(\nabla^-\psi(\overline{x}))\subset \nabla_c^-\psi(\overline{x})\subset \nabla^-\psi(\overline{x}),
$$

where  $\mathcal E$  stands for "extremal points". In particular,  $\nabla_c^-\psi(\overline{x})$  is convex if and only if it coincides with  $\nabla^-\psi(\overline{x})$ . Part (ii) of the Proposition follows easily.

It remains to prove (iii). Whenever  $(\overline{x},y_0)$  and  $(\overline{x},y_1)$  belong to Dom'( $\nabla_x c$ ), let  $\psi_{\overline{x},y_0,y_1}$  be defined by (12.17). Both  $y_0$  and  $y_1$  belong to  $\partial_c \psi_{\overline{x},y_0,y_1}(\overline{x})$ . If c is regular, then  $y_0$  and  $y_1$  can be connected by a c-segment with base  $\overline{x}$ , which lies inside Dom ( $\nabla_x c$ ) ∩  $\partial_c \psi(\overline{x})$ . This proves (12.16).

Conversely, let us assume that (12.16) holds true, and prove that c is regular. If  $\overline{x}, y_0, y_1$  are as above, the c-segment  $[y_0, y_1]_{\overline{x}}$  is welldefined by (a), so by part (i) of the Proposition we just have to prove the *c*-convexity of  $\partial_c \psi_{\overline{x},y_0,y_1}$ .

Then let  $h_0(x) = -c(x,y_0) + c(\overline{x},y_0), h_1(x) = -c(x,y_1) + c(\overline{x},y_1).$ By the twist condition,  $\nabla h_0(\overline{x}) \neq \nabla h_1(\overline{x})$ . Since  $h_0$  and  $h_1$  are semiconvex, we can apply the nonsmooth implicit function theorem (Corollary 10.52) to conclude that the equation  $(h_0 = h_1)$  defines an  $(n - 1)$ dimensional Lipschitz graph G in the neighborhood of  $\overline{x}$ . This graph is a level set of  $\psi = \psi_{\overline{x},y_0,y_1}$ , so  $\nabla^-\psi(\overline{x})$  is included in the orthogonal of  $G$ , which is the line directed by  $\nabla h_0(\overline{x}) - \nabla h_1(\overline{x})$ . In particular,  $\nabla^-\psi(\overline{x})$ is a one-dimensional set.

Let  $S = \partial_c \psi(\overline{x}) \cap \text{Dom}(\nabla_x c(\overline{x}, \cdot))$ . By (b), S is connected, so  $S' =$  $\nabla_x c(\overline{x}, S)$  is connected too, and by Theorem 10.25, S' is included in the line  $\nabla^- \psi(\overline{x})$ . Thus S' is a convex line segment (closed or not) containing  $-\nabla_x c(\overline{x},y_0)$  and  $-\nabla_x c(\overline{x},y_1)$ . This finishes the proof of the regularity of c.  $□$ 

The next result will show that the regularity property of the cost function is a necessary condition for a general theory of regularity of optimal transport. In view of Remark 12.19 it is close in spirit to Theorem 12.7.

Theorem 12.20 (Nonregularity implies nondensity of differentiable c-convex functions). Let  $c : \mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  satisfy (Twist), (locSC) and  $(H\infty)$ . Let C be a totally c-convex set contained in  $\mathrm{Dom}'(\nabla_x c)$ , such that (a) c is not regular in C; and (b) for any  $(\overline{x},y_0),(\overline{x},y_1)$  in  $C, \partial_c\psi_{\overline{x},y_0,y_1}(\overline{x}) \subset \mathrm{Dom}'(\nabla_x c(\overline{x},\cdot))$ . Then for some  $(\overline{x}, y_0)$ ,  $(\overline{x}, y_1)$  in C the c-convex function  $\psi = \psi_{\overline{x}, y_0, y_1}$  cannot be the locally uniform limit of differentiable c-convex functions  $\psi_k$ .

The short version of Theorem 12.20 is that if  $c$  is not regular, then differentiable c-convex functions are not dense, for the topology of local uniform convergence, in the set of all c-convex functions. What is happening here can be explained informally as follows. Take a function such as the one pictured in Figure 12.3, and try to tame up the singularity at the "saddle" by letting mountains grow from below and gently surelevate the pass. To do this without creating a new singularity you would like to be able to touch only the pass. Without the regularity assumption, you might not be able to do so.

Before proving Theorem 12.20, let us see how it can be used to prove a nonsmoothness result of the same kind as Theorem 12.7. Let  $\overline{x}, y_0, y_1$ be as in the conclusion of Theorem 12.20. We may partition  $\mathcal X$  in two sets  $\mathcal{X}_0$  and  $\mathcal{X}_1$  such that  $y_i \in \partial_c \psi(x)$  for each  $x \in \mathcal{X}_i$   $(i = 0, 1)$ . Fix an arbitrary smooth positive probability density f on  $\mathcal{X}$ , let  $a_i = \mu[\mathcal{X}_i]$  and  $\nu = a_0 \, \delta_{y_0} + a_1 \, \delta_{y_1}$ . Let T be the transport map defined by  $T(\mathcal{X}_i) = y_i$ ; by construction the associated transport plan has its support included in  $\partial_c \psi$ , so  $\pi$  is an optimal transport plan and  $(\psi, \psi^c)$  is optimal in the dual Kantorovich problem associated with  $(\mu, \nu)$ . By Remark 10.30,  $\psi$  is the unique optimal map, up to an additive constant, which can be fixed by requiring  $\psi(x_0) = 0$  for some  $x_0 \in \mathcal{X}$ . Then let  $(f_k)_{k \in \mathbb{N}}$ be a sequence of smooth probability densities, such that  $\nu_k := f_k$  vol converges weakly to  $\nu$ . For any k let  $\psi_k$  be a c-convex function, optimal in the dual Kantorovich problem. Without loss of generality we can assume  $\psi_k(x_0) = 0$ . Since the functions  $\psi_k$  are uniformly continuous, we can extract a subsequence converging to some function  $\psi$ . By passing to the limit in both sides of the Kantorovich problem, we deduce that  $\psi$  is optimal in the dual Kantorovich problem, so  $\psi = \psi$ . By Theorem 12.20, the  $\psi_k$  cannot all be differentiable. So we have proven the following corollary:

Corollary 12.21 (Nonsmoothness of the Kantorovich potential). With the same assumptions as in Theorem 12.20, if  $\mathcal Y$  is a closed subset of a Riemannian manifold, then there are smooth positive probability densities f on  $\mathcal X$  and q on  $\mathcal Y$ , such that the associated Kantorovich potential  $\psi$  is not differentiable.

Now let us prove Theorem 12.20. The following lemma will be useful.

Lemma 12.22. Let U be an open set of a Riemannian manifold, and let  $(\psi_k)_{k\in\mathbb{N}}$  be a sequence of semi-convex functions converging uniformly to  $\psi$  on U. Let  $\overline{x} \in U$  and  $p \in \nabla^-\psi(\overline{x})$ . Then there exist sequences  $x_k \to \overline{x}$ , and  $p_k \in \nabla^- \psi_k(x_k)$ , such that  $p_k \to p$ .

Proof of Lemma 12.22. Since this statement is local, we may pretend that U is a small open ball in  $\mathbb{R}^n$ , centered at  $\overline{x}$ . By adding a well-chosen quadratic function we may also pretend that  $\psi$  is convex. Let  $\delta > 0$  and  $\psi_k(x) = \psi_k(x) - \psi(\overline{x}) + \delta|x - \overline{x}|^2/2 - p \cdot (x - \overline{x})$ . Clearly  $\psi_k$  converges uniformly to  $\psi(x) = \psi(x) - \psi(\overline{x}) + \delta|x - \overline{x}|^2/2 - p \cdot (x - \overline{x})$ . Let  $x_k \in \overline{U}$  be a point where  $\psi_k$  achieves its minimum. Since  $\psi$  has a unique minimum at  $\overline{x}$ , by uniform convergence  $x_k$  approaches  $\overline{x}$ , in particular  $x_k$  belongs to U for k large enough. Since  $\psi_k$  has a minimum at  $x_k$ , necessarily  $0 \in \nabla^-\widetilde{\psi}(x_k)$ , or equivalently  $p_k := p - \delta(x_k - \overline{x}) \in \nabla^-\psi_k(x_k)$ , so  $p_k \to p$ , which proves the result. □

*Proof of Theorem 12.20.* Since c is not regular in C, there are  $(\overline{x}, y_0)$ and  $(\overline{x}, y_1)$  in C such that  $\partial_c \psi(\overline{x}) \cap C$  is not c-convex, where  $\psi = \psi_{\overline{x}, y_0, y_1}$ . Assume that  $\psi$  is the limit of differentiable c-convex functions  $\psi_k$ . Let  $p \in \nabla^- \psi(\overline{x})$ . By Lemma 12.22 there are sequences  $x_k \to \overline{x}$  and  $p_k \to p$  such that  $p_k \in \nabla^- \psi_k(x_k)$ , i.e.  $p_k = \nabla \psi_k(x_k)$ . Since  $\partial_c \psi_k(x_k)$ is nonempty (Proposition 10.24), in fact  $-\nabla c(x_k, \partial_c \psi_k(x_k)) = \{p_k\},\$ in particular  $\partial_c \psi_k(x_k)$  contains a single point, say  $y_k$ . By Proposition 10.24 again,  $y_k$  stays in a compact set, so we may assume  $y_k \to y$ . The pointwise convergence of  $\psi_k$  to  $\psi$  implies  $y \in \partial_c \psi(\overline{x})$ . Since c is semiconcave,  $\nabla_x c$  is continuous on its domain of definition, so  $-\nabla_x c(\overline{x},y) = \lim p_k = p.$ 

So  $-\nabla_x c(\overline{x},\partial_c\psi(\overline{x}))$  contains the whole of  $\nabla^-\psi(\overline{x})$ ; combining this with Remark 12.18 we conclude that  $\partial_c \psi(\overline{x})$  is c-convex, in contradiction with our assumption. □

Now that we are convinced of the importance of the regularity condition, the question naturally arises whether it is possible to translate it in analytical terms, and to check it in practice. The next section will bring a partial answer.

# The Ma–Trudinger–Wang condition

Ma, Trudinger and X.-J. Wang discovered a *differential* condition on the cost function, which plays a key role in smoothness estimates, but in the end turns out to be a local version of the regularity property. Before explaining this condition, I shall introduce a new key assumption which will be called the "strong twist" condition. (Recall that the "plain" twist condition is the injectivity of  $\nabla_x c(x, \cdot)$  on its domain of definition.)

Let  $\mathcal{X}, \mathcal{Y}$  be closed sets in Riemannian manifolds  $M, N$  respectively, and as before let  $Dom'(\nabla_x c)$  stand for the set of all  $(x, y) \in \mathcal{X} \times \mathcal{Y}$  such that x lies in the interior of X and y in the interior of Dom  $(\nabla_x c(x, \cdot))$ . It will be said that  $c$  satisfies the **strong twist** condition if

 $(\textbf{STwist})$  Dom' $(\nabla_x c)$  is an open set on which c is smooth,  $\nabla_x c$  is one-to-one, and the mixed Hessian  $\nabla_{x,y}^2 c$  is nonsingular.

**Remark 12.23.** The invertibility of  $\nabla_{x,y}^2 c$  implies the local injectivity of  $\nabla_x c(x, \cdot)$ , by the implicit function theorem; but alone it does not a priori guarantee the global injectivity.

Remark 12.24. One can refine Proposition 10.15 to show that cost functions deriving from a well-behaved Lagrangian do satisfy the strong twist condition. In the Appendix I shall give more details for the important particular case of the squared geodesic distance.

**Remark 12.25.** One should think of  $\nabla^2_{x,y} c$  as a bilinear form on  $T_xM \times$  $T_yN$ : It takes a pair of tangent vectors  $(\xi, \eta) \in T_xM \times T_yN$ , and gives back a number,  $(\nabla^2 c(x, y)) \cdot (\xi, \eta) = \langle \nabla^2 x, y \cdot \xi, \eta \rangle$ . It will play the role of a Riemannian metric (or rather the negative of a Riemannian metric), except that  $\xi$  and  $\eta$  do not necessarily belong to the same tangent space!

The Ma–Trudinger–Wang condition is not so simple and involves fourth-order derivatives of the cost. To write it in an unambiguous way, it will be convenient to use coordinates, with some care. If  $x$  and y are given points, let us introduce geodesic coordinates  $x_1, \ldots, x_n$  and  $y_1, \ldots, y_n$  in the neighborhood of x and y respectively. (This means that one chooses Euclidean coordinates in, say,  $T_xM$  and then parameterizes a point  $\tilde{x}$  in the neighborhood of x by the coordinates of the vector  $\xi$  such that  $\tilde{x} = \exp_x(\xi)$ .) The technical advantage of using geodesic coordinates is that geodesic paths starting from  $x$  or  $y$  will be straight

curves, in particular the Hessian can be computed by just differentiating twice with respect to the coordinate variables. (This advantage is nonessential, as we shall see.)

If u is a function of x, then  $u_j = \partial_j u = \partial u / \partial x_j$  will stand for the partial derivative of u with respect to  $x_j$ . Indices corresponding to the derivation in y will be written after a comma; so if  $a(x, y)$  is a function of x and y, then  $a_{i,j}$  stands for  $\partial^2 a/\partial x_i \partial y_j$ . Sometimes I shall use the convention of summation over repeated indices  $(a_k b^k = \sum_k a_k b^k,$  etc.), and often the arguments  $x$  and  $y$  will be implicit.

As noted before, the matrix  $(-c_{i,j})$  defined by  $c_{i,j} \xi^i \eta^j = \langle \nabla_{x,y}^2 c \cdot \xi, \eta \rangle$ will play the role of a Riemannian metric; in agreement with classical conventions of Riemannian geometry, I shall denote by  $(c^{i,j})$  the coordinates of the inverse of  $(c_{i,j})$ , and sometimes raise and lower indices according to the rules  $-c^{i,j}\xi_i = \xi^j$ ,  $-c_{i,j}\xi^j = \xi_i$ , etc. In this case  $(\xi_i)$  are the coordinates of a 1-form (an element of  $(T_xM)^*$ ), while  $(\xi^j)$  are the coordinates of a tangent vector in  $T_yM$ . (I shall try to be clear enough to avoid devastating confusions with the operation of the metric g.)

Definition 12.26 (c-second fundamental form). Let  $c : \mathcal{X} \times \mathcal{Y} \rightarrow$ R satisfy (STwist). Let  $\Omega \subset \mathcal{Y}$  be open (in the ambient manifold) with  $C^2$  boundary  $\partial \Omega$  contained in the interior of  $\mathcal{Y}$ . Let  $(x, y) \in$  $\text{Dom}'(\nabla_x c)$ , with  $y \in \partial \Omega$ , and let n be the outward unit normal vector to  $\partial\Omega$ , defined close to y. Let  $(n_i)$  be defined by  $\langle n,\xi\rangle_y = \sum n_i \xi^i$  $(\forall \xi \in T_y M)$ . Define the quadratic form  $\mathbb{I}_c(x, y)$  on  $T_y \Omega$  by the formula

$$
$\mathbb{I}_c(x, y)(\xi)$  = \sum_{ijk\ell} (\partial_j n_i - c^{k,\ell} c_{ij,k} n_\ell) \xi^i \xi^j
$$
$$
= \sum_{ijk\ell} c_{i,k} \partial_j (c^{k,\ell} n_\ell) \xi^i \xi^j.
$$
(12.18)

Definition 12.27 (Ma–Trudinger–Wang tensor, or c-curvature operator). Let  $c : \mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  satisfy (STwist). For any  $(x, y) \in$ Dom'( $\nabla_x c$ ), define a quadrilinear form  $\mathfrak{S}_c(x,y)$  on the space of bivectors  $(\xi, \eta) \in T_xM \times T_yN$  satisfying

$$
\langle \nabla_{x,y}^2 c(x,y) \cdot \xi, \eta \rangle = 0 \tag{12.19}
$$

by the formula

$$
\mathfrak{S}_c(x,y)(\xi,\eta) = \frac{3}{2} \sum_{ijk\ell rs} \left( c_{ij,r} c^{r,s} c_{s,k\ell} - c_{ij,k\ell} \right) \xi^i \xi^j \eta^k \eta^\ell. \tag{12.20}
$$

Remark 12.28. Both for practical and technical purposes, it is often better to use another equivalent definition of  $\mathfrak{S}_c$ , see formula (12.21) below.

To understand where  $\mathfrak{S}_c$  comes from, and to compute it in practice, the key is the change of variables  $p = -\nabla_x c(x, y)$ . This leads to the following natural definition.

**Definition 12.29 (c-exponential).** Let c be a cost function satisfying (Twist), define the c-exponential map on the image of  $-\nabla_x c$  by the formula  $c\text{-}exp_x(p) = (\nabla_x c)^{-1}(x, -p).$ 

In other words,  $c$ -exp<sub>x</sub> $(p)$  is the unique y such that  $\nabla_x c(x,y) + p = 0$ . When  $c(x, y) = d(x, y)^2/2$  on  $\mathcal{X} = \mathcal{Y} = M$ , a complete Riemannian manifold, one recovers the usual exponential of Riemannian geometry, whose domain of definition can be extended to the whole of  $TM$ . More generally, if c comes from a time-independent Lagrangian, under suitable assumptions the c-exponential can be defined as the solution at time 1 of the Lagrangian system starting at x with initial velocity  $v$ , in such a way that  $\nabla_v L(x,v) = p$ .

Then, with the notation  $p = -\nabla_x c(x, y)$ , we have the following reformulation of the c-curvature operator:

$$
\mathfrak{S}_c(x,y)(\xi,\eta) = -\frac{3}{2}\frac{d^2}{ds^2}\frac{d^2}{dt^2}\Big(c(\exp_x(t\xi), (c\text{-}exp)_x(p+s\eta))\Big), (12.21)
$$

where  $\eta$  in the right-hand side is an abuse of notation for the tangent vector at x obtained from  $\eta$  by the operation of  $-\nabla_{xy}^2 c(x, y)$  (viewed as an operator  $T_yM \rightarrow T_xM$ ). In other words,  $\mathfrak{S}_c$  is obtained by differentiating the cost function  $c(x,y)$  twice with respect to x and twice with respect to  $p$ , not with respect to  $y$ . Getting formula (12.20) from (12.21) is just an exercise, albeit complicated, in classical differential calculus; it involves the differentiation formula for the matrix inverse:  $d(M^{-1}) \cdot H = -M^{-1}HM^{-1}$ .

Particular Case 12.30 (Loeper's identity). If  $\mathcal{X} = \mathcal{Y} = M$  is a smooth complete Riemannian manifold,  $c(x, y) = d(x, y)^2/2$ , and  $\xi, \eta$ are two unit orthogonal vectors in  $T_xM$ , then

$$
\mathfrak{S}_c(x,x)(\xi,\eta) = \sigma_x(P) \tag{12.22}
$$

is the **sectional curvature** of M at x along the plane P generated by  $\xi$ and  $\eta$ . (See Chapter 14 for basic reminders about sectional curvature.)

To establish  $(12.22)$ , first note that for any fixed small t, the geodesic curve joining x to  $\exp_x(t\xi)$  is orthogonal to  $s \to \exp_x(s\eta)$  at  $s = 0$ , so  $(d/ds)_{s=0}F(t,s) = 0$ . Similarly  $(d/dt)_{t=0}F(t,s) = 0$  for any fixed s, so the Taylor expansion of  $F$  at  $(0,0)$  takes the form

$$
\frac{d(\exp_x(t\xi), \exp_x(s\eta))^2}{2} = A t^2 + B s^2 + C t^4 + D t^2 s^2 + E s^4
$$
  
+  $O(t^6 + s^6)$ .

Since  $F(t, 0)$  and  $F(0, s)$  are quadratic functions of t and s respectively, necessarily  $C = E = 0$ , so  $\mathfrak{S}_c(x, x) = -6D$  is  $-6$  times the coefficient of  $t^4$  in the expansion of  $d(\exp_x(t\xi), \exp_x(t\eta))^2/2$ . Then the result follows from formula (14.1) in Chapter 14.

**Remark 12.31.** Formula (12.21) shows that  $\mathfrak{S}_c(x,y)$  is intrinsic, in the sense that it is independent of any choice of geodesic coordinates (this was not obvious from Definition 12.20). However, the geometric interpretation of  $\mathfrak{S}_c$  is related to the regularity property, which is independent of the choice of Riemannian structure; so we may suspect that the choice to work in geodesic coordinates is nonessential. It turns out indeed that Definition 12.20 is independent of any choice of coordinates, geodesic or not: We may apply Formula (12.20) by just letting  $c_{i,j} = \frac{\partial^2 c(x,y)}{\partial x_i \partial y_j}$ ,  $p_i = -c_i$  (partial derivative of  $c(x,y)$  with respect to  $x_i$ ), and replace (12.21) by

$$
\mathfrak{S}_c(\overline{x}, y)(\xi, \eta) = -\frac{3}{2} \frac{\partial^2}{\partial p_\eta^2} \frac{\partial^2}{\partial x_\xi^2} c\left(x, c\text{-exp}_{\overline{x}}(p)\right)\Big|_{x=\overline{x}, p=-d_x c(\overline{x}, y)}, \tag{12.23}
$$

where the c-exponential is defined in an obvious way in terms of 1-forms (differentials) rather than tangent vectors. This requires some justification since the second differential is not an intrisical concept (except when the first differential vanishes) and might a priori depend on the choice of coordinates. When we differentiate (12.23) twice with respect to  $x_{\xi}$ , there might be an additional term which will be a combination of  $\Gamma^k_{ij}(\overline{x}) \partial_k c(\overline{x}, (c\text{-exp}_{\overline{x}})(p)) = -\Gamma^k_{ij}(\overline{x}) p_k$ , where the  $\Gamma^k_{ij}$  are the Christoffel symbols. But this additional term is linear in  $p$ , so anyway it disappears when we differentiate twice with respect to  $p_n$ . (This argument does not need the "orthogonality" condition  $\nabla^2 c(x, y) \cdot (\xi, \eta) = 0.$ )

Remark 12.32. Even if it is intrinsically defined, from the point of view of Riemannian geometry  $\mathfrak{S}_c$  is not a standard curvature-type operator, for at least two reasons. First it involves derivatives of order

greater than 2; and secondly it is nonlocal, in a strong sense. Take for instance  $c(x, y) = d(x, y)^2/2$  on a Riemannian manifold  $(M, g)$ , fix x and y, compute  $\mathfrak{S}_c(x,y)$ , then a change of the Riemannian metric g can affect the value of  $\mathfrak{S}_c(x,y)$ , even if the metric g is left unchanged in a neighborhood of  $x$  and  $y$ , and even if it is unchanged in a neighborhood of the geodesics joining  $x$  to  $y$ ! Here we are facing the fact that geodesic distance is a highly nonlocal notion.

**Remark 12.33.** The operator  $\mathfrak{S}$  is symmetric under the exchange of x and y, in the sense that if  $\check{c}(x,y) = c(y,x)$ , then  $\mathfrak{S}_{\check{c}}(y,x)(\eta,\xi) =$  $\mathfrak{S}_c(x,y)(\xi,\eta)$ . (Here I am assuming implicitly that also  $\check{c}$  satisfies the strong twist condition.) This symmetry can be seen from (12.20) by just rearranging indices. To see it directly from (12.21), we may apply Remark 12.31 to change the geodesic coordinate system around  $\overline{x}$  and parameterize x by  $q = -\nabla_y c(x, \overline{y})$ . Then we obtain the nicely symmetric expression for  $\mathfrak{S}_c(\overline{x}, \overline{y})(\xi, \eta)$ :

$$
-\frac{3}{2}\frac{\partial^2}{\partial p_\eta^2}\frac{\partial^2}{\partial q_\xi^2}c\left(\check{c}\text{-}\exp_{\overline{y}}(q),c\text{-}\exp_{\overline{x}}(p)\right)\Big|_{p=-d_xc(\overline{x},\overline{y}),q=-d_yc(\overline{x},\overline{y})}.\tag{12.24}
$$

(Caution:  $\xi$  is tangent at x and q at y, so differentiating with respect to q in direction  $\xi$  means in fact differentiating in the direction  $-\nabla_{xy}^2 c \cdot \xi$ ; recall (12.21). The same for  $p_{\eta}$ .)

In the sequel, I shall stick to the same conventions as in the beginning of the chapter, so I shall use a fixed Riemannian metric and use Hessians and gradients with respect to this metric, rather than differentials of order 1 and 2.

Theorems 12.35 and 12.36 will provide necessary and sufficient differential conditions for c-convexity and regularity; one should note carefully that these conditions are valid only inside Dom  $(\nabla_x c)$ . I shall use the notation  $\check{c}(y,x) = c(x,y), \check{D} = \{(y,x); (x,y) \in D\}$ . I shall also be led to introduce an additional assumption:

 $(Cut^{n-1})$  For any x in the interior of  $proj_{\mathcal{X}}(D)$ , the "cut locus"  $\text{cut}_D(x) = \text{proj}_{\mathcal{Y}}(D) \setminus \text{Dom}'(\nabla_x c(x, \cdot))$  locally has finite  $(n-1)$ -dimensional Hausdorff measure.

**Example 12.34.** Condition  $(\text{Cut}^{n-1})$  trivially holds when c satisfies the strong twist condition,  $D$  is totally c-convex and product. It also holds when c is the squared distance on a Riemannian manifold M, and  $D = M \setminus \text{cut}(M)$  is the domain of smoothness of c (see the Appendix). Now come the main results of this section:

Theorem 12.35 (Differential formulation of  $c$ -convexity). Let  $c: \mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  be a cost function satisfying (STwist). Let  $x \in \mathcal{X}$  and let C be a connected open subset of  $\mathcal{Y}$  with  $C^2$  boundary. Let  $x \in \mathcal{X}$ , such that  $\{x\} \times C \subset \text{Dom}'(\nabla_x c)$ . Then C is c-convex with respect to x if and only if  $\mathbb{I}_c(x,y) \geq 0$  for all  $y \in \partial C$ .

If moreover  $\mathbb{I}_c(x,y) > 0$  for all  $y \in \partial \Omega$  then  $\Omega$  is strictly c-convex with respect to x.

Theorem 12.36 (Differential formulation of regularity). Let  $c$ :  $\mathcal{X} \times \mathcal{Y} \rightarrow \mathbb{R}$  be a cost function satisfying (STwist), and let D be a totally c-convex open subset of  $\text{Dom}'(\nabla_x c)$ . Then:

(i) If c is regular in D, then  $\mathfrak{S}_c(x,y) \geq 0$  for all  $(x,y) \in D$ .

(ii) Conversely, if  $\mathfrak{S}_c(x,y) \geq 0$  (resp.  $> 0$ ) for all  $(x,y) \in D$ , č satisfies (STwist),  $\check{D}$  is totally č-convex, and c satisfies (Cut<sup>n-1</sup>) on D, then c is regular (resp. strictly regular) in D.

Remark 12.37. For Theorem 12.36 to hold true, it is important that no condition be imposed on the sign of  $\mathfrak{S}_c(x,y) \cdot (\xi,\eta)$  (or more rigorously, the expression in (12.20)) when  $\xi$  and  $\eta$  are not "orthogonal" to each other. For instance,  $c(x, y) = \sqrt{1 + |x - y|^2}$  is regular, still  $\mathfrak{S}_c(x,y) \cdot (\xi,\xi) < 0$  for  $\xi = x - y$  and  $|x - y|$  large enough.

Remark 12.38. A corollary of Theorem 12.36 and Remark 12.33 is that the regularity of  $c$  is more or less equivalent to the regularity of  $\check{c}$ .

Proof of Theorem 12.35. Let us start with some reminders about classical convexity in Euclidean space. If  $\Omega$  is an open set in  $\mathbb{R}^n$  with  $C^2$ boundary and  $x \in \partial\Omega$ , let  $T_x\Omega$  stand for the tangent space to  $\partial\Omega$  at x, and n for the outward normal on  $\partial\Omega$  (extended smoothly in a neighborhood of x). The second fundamental form of  $\Omega$ , evaluated at x, is defined on  $T_x \Omega$  by  $\mathbb{I}(x)(\xi) = \sum_{ij} \partial_i n_j \xi^i \xi^j$ . A defining function for  $\Omega$ at x is a function  $\Phi$  defined in a neighborhood of x, such that  $\Phi < 0$ in  $\Omega$ ,  $\Phi > 0$  outside  $\Omega$ , and  $|\nabla \Phi| > 0$  on  $\partial \Omega$ . Such a function always exists (locally), for instance one can choose  $\Phi(x) = \pm d(x, \partial\Omega)$ , with + sign when x is outside  $\Omega$  and – when x is in  $\Omega$ . (In that case  $\nabla \Phi$  is the outward normal on  $\partial\Omega$ .) If  $\Phi$  is a defining function, then  $n = \nabla \Phi / |\nabla \Phi|$ on  $\partial\Omega$ , and for all  $\xi\perp n$ ,

$$
\partial_i n_j \, \xi^i \, \xi^j = \left( \frac{\varPhi_{ij}}{|\nabla \varPhi|} - \frac{\varPhi_j \, \varPhi_{ik}}{|\nabla \varPhi|^3} \right) \, \xi^i \, \xi^j = \frac{\varPhi_{ij}}{|\nabla \varPhi|} \, \xi^i \, \xi^j.
$$

So the condition  $\mathbb{I}(x) \geq 0$  on  $\partial\Omega$  is equivalent to the condition that  $\nabla^2 \Phi(x)$  is always nonnegative when evaluated on tangent vectors. In that case, we can always choose a defining function of the form  $g(\pm d(x,\Omega))$ , with  $g(0) = g'(0) = 1$ , g strictly convex, so that  $\nabla^2 \Phi(x)$ is nonnegative in all directions. With a bit more work, one can show that  $\Omega$  is convex if and only if it is connected and its second fundamental form is nonnegative on the whole of  $\partial\Omega$ . Moreover, if the second fundamental form is positive then  $\Omega$  is strictly convex.

Now let C be as in the assumptions of Theorem 12.35 and let  $\Omega = -\nabla_x c(x, C) \subset T_x M$ . Since  $\nabla_x c(x, \cdot)$  is smooth, one-to-one with nonsingular differential,  $\partial\Omega$  is smooth and coincides with  $-\nabla_x c(x, \partial C)$ . Since C is connected, so is  $\Omega$ , so to prove the convexity of  $\Omega$  we just have to worry about the sign of its second fundamental form. Let  $\overline{y} \in \partial C$  be fixed, and let  $\Phi = \Phi(y)$  be a defining function for C in a neighborhood of  $\overline{y}$ . Then  $\Psi = \Phi \circ (-\nabla_x c(x, \cdot))^{-1}$  is a defining function for  $\Omega$ . By direct computation,

$$
\Psi^{ij} = \left(\Phi_{rs} - c_{rs,k} \, c^{k,\ell} \, \Phi_{\ell}\right) c^{r,i} \, c^{s,j}.
$$

(The derivation indices are raised because these are derivatives with respect to the p variables.) Let  $\xi = (\xi^j) \in T_yM$  be tangent to C; the bilinear form  $\nabla^2 c$  identifies  $\xi$  with an element in  $(T_xM)^*$  whose coordinates are denoted by  $(\xi_i) = (-c_{i,j} \xi^j)$ . Then

$$
\Psi^{ij}\xi_i\,\xi_j=\left(\Phi_{rs}-c_{rs,k}\,c^{k,\ell}\,\Phi_\ell\right)\xi^r\,\xi^s.
$$

Since  $\xi^s n_s = 0$ , the nonnegativity of this expression is equivalent to the nonnegativity of  $(\partial_r n_s - c^{k,\ell} c_{rs,k} n_\ell) \xi^r \xi^s$ , for all tangent vectors  $\xi$ . This establishes the first part of Theorem 12.35, and at the same time justifies formula (12.18). (To prove the equivalence between the two expressions in (12.18), use the temporary notation  $n^k = -c^{k,\ell} n_\ell$  and note that  $\partial_j n_i - c^{k,\ell} c_{ij,k} n_\ell = -\partial_j (c_{i,k} n^k) + c_{ij,k} n^k = -c_{i,k} \partial_j n^k$ .) The statement about strict convexity follows the same lines.

In the proof of Theorem 12.36 I shall use the following technical lemma:

Lemma 12.39. If c satisfies (STwist) and ( $\mathrm{Cut}^{n-1}$ ) then it satisfies the following property of "transversal approximation":

(TA) For any  $\overline{x}, x$  in the interior of  $\text{proj}_{\mathcal{X}}(D)$ , any  $C^2$  path  $(y_t)_{0 \leq t \leq 1}$  drawn in Dom' $(\nabla_x c(\overline{x},\cdot)) \cap \text{proj}_{\mathcal{Y}}(D)$  can be approximated in  $C^2$  topology by a path  $(\widehat{y}_t)_{0 \le t \le 1}$  such that  $\{t \in (0,1); \hat{y}_t \notin \text{Dom}'(\nabla_x c(x,\cdot))\}$  is discrete.

Lemma 12.39 applies for instance to the squared geodesic distance on a Riemannian manifold. The detailed proof of Lemma 12.39 is a bit tedious, so I shall be slightly sketchy:

Sketch of proof of Lemma 12.39. As in the statement of  $(\text{Cut}^{n-1})$ , let  $\text{cut}_D(x) = \text{proj}_{\mathcal{Y}}(D) \setminus \text{Dom}'(\nabla_x c(x, \cdot)).$  Since  $\text{cut}_D(x)$  has empty interior, for any fixed  $t_0 \in [0, 1]$  we can perturb the path y in  $C^2$  topology into a path  $\hat{y}$ , in such a way that  $\hat{y}(t_0) \notin \text{cut}_D(x)$ . Repeating this operation finitely many times, we can ensure that  $\hat{y}(t_i)$  lies outside  $\text{cut}_D(x)$ for each  $t_j = j/2^k$ , where  $k \in \mathbb{N}$  and  $j \in \{0, \ldots, 2^k\}$ . If k is large enough, then for each j the path  $\hat{y}$  can be written, on the time-interval  $[t_i, t_{i+1}]$ , in some well-chosen local chart, as a straight line. Moreover, since  $\text{cut}_D(x)$  is closed, there will be a small ball  $B_i = B(\hat{y}(t_i), r_i)$ ,  $r_j > 0$ , such that on the interval  $[t_j - \varepsilon_j, t_j + \varepsilon_j]$  the path  $\hat{y}$  is entirely contained in  $B_j$ , and the larger ball  $2B_j = B(\hat{y}(t_j), 2r_j)$ does not meet  $\text{cut}_D(x)$ . If we prove the approximation property on each interval  $[t_{j-1} + \varepsilon_{j-1}, t_j - \varepsilon_j]$ , we shall get suitable paths  $(\widehat{y}_t)$  on  $[t_{j-1} + \varepsilon_{j-1}, t_j - \varepsilon_j]$ , approximating  $(y_t)$  in  $C^2$ ; in particular  $\hat{y}_{t_i} \in B_i$ , and we can easily "patch together" these pieces of  $(\widehat{y}_t)$  in the intervals  $[t_i - \varepsilon_i, t_i + \varepsilon_i]$  while staying within  $2B_i$ .

All this shows that we just have to treat the case when  $(y_t)$  takes values in a small open subset U of  $\mathbb{R}^n$  and is a straight line. In these coordinates,  $\Sigma := \text{cut}_D(x) \cap U$  will have finite  $\mathcal{H}^{n-1}$  measure, with  $\mathcal{H}^k$  standing for the k-dimensional Hausdorff measure. Without loss of generality,  $y_t = t e_n$ , where  $(e_1, \ldots, e_n)$  is an orthonormal basis of  $\mathbb{R}^n$ and  $-\tau < t < \tau$ ; and U is the cylinder  $B(0,\sigma) \times (-\tau,\tau)$ , for some  $\sigma > 0$ .

For any  $z \in B(0, \sigma) \subset \mathbb{R}^{n-1}$ , let  $y_t^z = (z, t)$ . The goal is to show that  $\mathcal{H}^{n-1}(dz)$ -almost surely,  $y_t^z$  intersects  $\Sigma$  in at most finitely many points. To do this one can apply the co-area formula (see the bibliographical notes) in the following form: let  $f : (z,t) \longmapsto z$  (defined on U), then

$$
\mathcal{H}^{n-1}[\Sigma] \ge \int_{f(\Sigma)} \mathcal{H}^0[\Sigma \cap f^{-1}(z)] \, \mathcal{H}^{n-1}(dz).
$$

By assumption the left-hand side is finite, and the right-hand side is exactly  $\int \# \{t; y_t^z \in \Sigma\} \mathcal{H}^{n-1}(dz)$ ; so the integrand is finite for almost all z, and in particular there is a sequence  $z_k \to 0$  such that each  $(y_t^{z_k})$ intersects  $\Sigma$  finitely many often. □

*Proof of Theorem 12.36.* Let us first assume that the cost  $c$  is regular on D, and prove the nonnegativity of  $\mathfrak{S}_c$ .

Let  $(\overline{x},y) \in D$  be given, and let  $p \in T_{\overline{x}}M$  be such that  $\nabla_x c(\overline{x},y)$  +  $p = 0$ . Let  $\nu$  be a unit vector in  $T_{\overline{x}}M$ . For  $\varepsilon \in [-\varepsilon_0, \varepsilon_0]$  one can define a c-segment by the formula  $y(\varepsilon) = (\nabla_{\overline{x}} c)^{-1}(\overline{x}, -p(\varepsilon)), p(\varepsilon) = p + \varepsilon \nu;$ let  $y_0 = y(-\varepsilon_0)$  and  $y_1 = y(\varepsilon_0)$ . Further, let  $h_0(x) = -c(x,y_0) + c(x,y_0)$  $c(\overline{x},y_0)$  and  $h_1(x) = -c(x,y_1)+c(\overline{x},y_1)$ . By construction,  $\nabla_x c(\overline{x},y_0)$  –  $\nabla_x c(\overline{x}, y_1)$  is colinear to  $\nu$  and nonzero, and  $h_0(\overline{x}) = h_1(\overline{x})$ .

By the implicit function theorem, the equation  $(h_0(x) = h_1(x))$ defines close to  $\bar{x}$  an  $(n-1)$ -dimensional submanifold M, orthogonal to  $\nu$  at  $\overline{x}$ . For any  $\xi \in T_{\overline{x}}M = \nu^{\perp}$  one can define in the neighborhood of  $\overline{x}$  a smooth curve  $(\gamma(t))_{-\tau \leq t \leq \tau}$ , valued in  $\overline{M}$ , such that  $\gamma(0) = \overline{x}$  and  $\dot{\gamma}(0) = \xi.$ 

Let  $\psi(x) = \max(h_0(x), h_1(x))$ . By construction  $\psi$  is c-convex and  $y_0, y_1$  both belong to  $\partial_c \psi(\overline{x})$ . So

$$
\psi(\overline{x}) + c(\overline{x}, y) \leq \psi(\gamma(t)) + c(\gamma(t), y)
$$
  
= 
$$
\frac{1}{2} (h_0(\gamma(t)) + h_1(\gamma(t))) + c(\gamma(t), y)
$$
  
= 
$$
\frac{1}{2} \Big[ -c(\gamma(t), y_0) + c(\overline{x}, y_0) - c(\gamma(t), y_1) + c(\overline{x}, y_1) \Big] + c(\gamma(t), y).
$$

Since  $\psi(\overline{x}) = 0$ , this can be recast as

$$
0 \ge \frac{1}{2} \Big( c(\gamma(t), y_0) + c(\gamma(t), y_1) \Big) - \frac{1}{2} \Big( c(\overline{x}, y_0) + c(\overline{x}, y_1) \Big) - c(\gamma(t), y) + c(\overline{x}, y). \tag{12.25}
$$

At  $t = 0$  the expression on the right-hand side achieves its maximum value 0, so the second t-derivative is nonnegative. In other words,

$$
\left. \frac{d^2}{dt^2} \right|_{t=0} \left( \frac{1}{2} \left[ c(\gamma(t), y_0) + c(\gamma(t), y_1) \right] - c(\gamma(t), y) \right) \le 0.
$$

This is equivalent to saying that

$$
\left\langle \nabla_x^2 c(\overline{x}, y) \cdot \xi, \xi \right\rangle \ge \frac{1}{2} \Big( \left\langle \nabla_x^2 c(\overline{x}, y_0) \cdot \xi, \xi \right\rangle + \left\langle \nabla_x^2 c(\overline{x}, y_1) \cdot \xi, \xi \right\rangle \Big).
$$

(Note: The path  $\gamma(t)$  is not geodesic, but this does not matter because the first derivative of the right-hand side in  $(12.25)$  vanishes at  $t = 0$ .)

Since  $p = (p_0 + p_1)/2$  and  $p_0 - p_1$  was along an arbitrary direction (orthogonal to  $\xi$ ), this shows precisely that  $\langle \nabla_x^2 c(\overline{x}, y) \cdot \xi, \xi \rangle$  is concave as a function of p, after the change of variables  $y = y(x, p)$ . In particular, the expression in (12.21) is  $\geq$  0. To see that the expressions in (12.21)

and (12.20) are the same, one performs a direct (tedious) computation to check that if  $\eta$  is a tangent vector in the p-space and  $\xi$  is a tangent vector in the  $x$ -space, then

$$
\left(\frac{\partial^4 c(x, y(x, p))}{\partial p_k \partial p_\ell \partial x_i \partial x_j}\right) \xi^i \xi^j \eta_k \eta_\ell = \left(c_{ij,r} c^{r,s} c_{s,k\ell} - c_{ij,k\ell}\right) c^{k,m} c^{\ell,n} \xi^i \xi^j \eta_m \eta_n
$$
$$
= \left(c_{ij,r} c^{r,s} c_{s,k\ell} - c_{ij,k\ell}\right) \xi^i \xi^j \eta^k \eta^\ell.
$$

(Here  $\eta^k = -c^{k,m} \eta_m$  stand for the coordinates of a tangent vector at y, obtained from  $\eta$  after changing variables  $p \to y$ , and still denoted  $\eta$ by abuse of notation.) To conclude, one should note that the condition  $\xi \perp \eta$ , i.e.  $\xi^i \eta_i = 0$ , is equivalent to  $c_{i,j} \xi^i \eta^j = 0$ .

Next let us consider the converse implication. Let  $(\overline{x}, y_0)$  and  $(\overline{x}, y_1)$ belong to D;  $p = p_0 = -\nabla_x c(\overline{x}, y_0), p_1 = -\nabla_x c(\overline{x}, y_1), \zeta = p_1 - p_0 \in$  $T_{\overline{x}}M$ ,  $p_t = p + t\zeta$ , and  $y_t = (\nabla_x c)^{-1}(\overline{x}, -p_t)$ . By assumption  $(\overline{x}, y_t)$ belongs to D for all  $t \in [0, 1]$ .

For  $t \in [0,1]$ , let  $h(t) = -c(x,y_t) + c(\overline{x},y_t)$ . Let us first assume that  $x \in \text{Dom}'(\nabla_y c(\cdot, y_t))$  for all t; then h is a smooth function of  $t \in (0, 1)$ , and we can compute its first and second derivatives. First,

$$
(\dot{y})^i = -c^{i,r}(\overline{x}, y_t) \zeta_r =: \zeta^i.
$$

Similarly, since  $(y_t)$  defines a straight curve in p-space,

$$
(\ddot{y})^i = -c^{i,k} c_{k,\ell j} c^{\ell,r} c^{j,s} \zeta_r \zeta_s = -c^{i,k} c_{k,\ell j} \zeta^{\ell} \zeta^j.
$$

So

$$
\dot{h}(t) = -[c_{,j}(x, y_t) - c_{,j}(\overline{x}, y_t)] \zeta^{j} = c_{i,j} \eta^{i} \zeta^{j},
$$

where  $\eta_j = -c_{,j}(x, y_t) + c_{,j}(\overline{x}, y_t)$  and  $\eta^i = -c^{j,i}\eta_j$ . Next,

$$
\ddot{h}(t) = -[c_{,ij}(x, y_t) - c_{,ij}(\overline{x}, y_t)] \zeta^i \zeta^j \\ + [c_{,j}(x, y_t) - c_{,j}(\overline{x}, y_t)] c^{j,k} c_{k,\ell i} \zeta^{\ell} \zeta^i \\ = -([c_{,ij}(x, y_t) - c_{,ij}(\overline{x}, y_t)] - \eta_{\ell} c^{\ell,k} c_{k,ij}) \zeta^i \zeta^j \\ = -([c_{,ij}(x, y_t) - c_{,ij}(\overline{x}, y_t)] - \eta^k c_{k,ij}) \zeta^i \zeta^j.
$$

Now freeze  $t, y_t, \zeta$ , and let  $\Phi(x) = c_{ij}(x, y_t) \zeta^i \zeta^j = \langle \nabla_y^2 c(x, y_t) \cdot \zeta, \zeta \rangle$ . This can be seen either as a function of x or as a function of  $q =$   $-\nabla_y c(x,y_t)$ , viewed as a 1-form on  $T_{y_t}M$ . Computations to go back and forth between the  $q$ -space and the x-space are the same as those between the p-space and the y-space. If q and  $\overline{q}$  are associated to x and  $\overline{x}$  respectively, then  $\eta = q - \overline{q}$ , and

$$
\left( \left[c_{,ij}(x, y_t) - c_{,ij}(\overline{x}, y_t) \right] - \eta^k c_{k,ij} \right) \zeta^i \zeta^j = \Phi(q) - \Phi(\overline{q}) - d_{\overline{q}} \Phi \cdot (q - \overline{q})
$$
$$
= \int_0^1 \left( \nabla_q^2 \Phi \right) \left( (1 - s)q + s\overline{q} \right) \cdot \left( q - \overline{q}, q - \overline{q} \right) (1 - s) \, ds.
$$

Computing  $\nabla_q^2 \Phi$  means differentiating  $c(x, y)$  twice with respect to y and then twice with respect to q, with  $\nabla_y c(x, y) + q = 0$ . According to Remark 12.33, the result is the same as when we first differentiate with respect to x and then with respect to p, so this gives  $(-2/3)\mathfrak{S}_c$ . Since  $q - \overline{q} = -\eta$ , we end up with

$$
\dot{h}(t) = \left(\nabla_{x,y}^2 c(x,y_t)\right) \cdot (\eta,\zeta);
$$

$$
\ddot{h}(t) = \frac{2}{3} \int_0^1 \mathfrak{S}_c \left( (\nabla_y c)^{-1} \left( (1-s)q + s\overline{q}, y_t \right), y_t \right) \cdot (\eta,\zeta) \left( 1-s \right) ds,
$$
(12.26)

where now  $\nabla_y c$  is inverted with respect to the x variable. Here I have slightly abused notation since the vectors  $\eta$  and  $\zeta$  do not necessarily satisfy  $\nabla_{x,y}^2 c \cdot (\eta,\zeta) = 0$ ; but  $\mathfrak{S}_c$  stands for the same analytic expression as in (12.20). Note that the argument of  $\mathfrak{S}_c$  in (12.26) is always well-defined because  $\dot{D}$  was assumed to be  $\check{c}$ -convex and  $x \in \text{Dom}'(\nabla_y c(\cdot, y_t))$ . (So  $[\overline{x}, x]_{y_t}$  is contained in  $\text{Dom}'(\nabla_y c(\cdot, y_t))$ .)

The goal is to show that h achieves its maximum value at  $t = 0$  or  $t = 1$ . Indeed, the inequality  $h(t) \leq \max(h(0), h(1))$  is precisely (12.15).

Let us first consider the simpler case when  $\mathfrak{S}_c > 0$ . If h achieves its maximum at  $t_0 \in (0,1)$ , then  $\dot{h}(t_0) = 0$ , so  $\nabla_{x,y}^2 c \cdot (\eta,\zeta) = 0$ ,  $\mathfrak{S}_{c}(\ldots)(\eta,\zeta) > 0$ , and by (12.26) we have  $\ddot{h}(t_0) > 0$  (unless  $x = \overline{x}$ ), which contradicts the fact that  $t_0$  is a maximum. So h has a maximum at  $t = 0$  or  $t = 1$ , which proves (12.15), and this inequality is strict unless  $t = 0$  or  $t = 1$  or  $x = \overline{x}$ .

To work out the borderline case where  $\mathfrak{S}_c$  is only assumed to be nonnegative we shall have to refine the analysis just a bit. By the same density argument as above, we can assume that h is smooth.

Freeze  $\zeta$  and let  $\eta$  vary in a ball. Since  $\mathfrak{S}_c(x,y) \cdot (\eta, \zeta)$  is a quadratic function of  $\eta$ , nonnegative on the hyperplane  $\{\zeta_{\ell} \eta^{\ell} = 0\}$ , there is a

constant C such that  $\mathfrak{S}_c(x,y) \cdot (\eta,\zeta) \geq -C |\zeta_{\ell} \eta^{\ell}| = -C |\nabla_{x,y}^2 c \cdot (\zeta, \eta)|.$ This constant only depends on an upper bound on the functions of  $x, y$ appearing in  $\mathfrak{S}_c(x,y)$ , and on upper bounds on the norm of  $\nabla_{x,y}^2 c$  and its inverse. By homogeneity,  $\mathfrak{S}_c(x,y) \cdot (\eta,\zeta) \geq -C \, |\nabla^2_{x,y} c \cdot (\zeta,\eta)| |\zeta| |\eta|,$ where the constant C is uniform when  $x, \overline{x}, y_0, y_1$  vary in compact domains. The norms  $|\zeta|$  and  $|\eta|$  remain bounded as t varies in [0, 1], so (12.26) implies

$$
\ddot{h}(t) \ge -C |\dot{h}(t)|. \tag{12.27}
$$

Now let  $h_{\varepsilon}(t) = h(t) + \varepsilon (t - 1/2)^{k}$ , where  $\varepsilon > 0$  and  $k \in 2\mathbb{N}$  will be chosen later. Let  $t_0$  be such that  $h_{\varepsilon}$  admits a maximum at  $t_0$ . If  $t_0 \in (0,1)$ , then  $\dot{h}_{\varepsilon}(t_0) = 0$ ,  $\ddot{h}_{\varepsilon}(t_0) \leq 0$ , so

$$
\ddot{h}(t_0) = \ddot{h}_{\varepsilon}(t_0) - \varepsilon k(k-1) (t_0 - 1/2)^{k-2} \le -\varepsilon k(k-1) (t_0 - 1/2)^{k-2};
$$
  
$$
\dot{h}(t_0) = -\varepsilon k (t_0 - 1/2)^{k-1}.
$$

Plugging these formulas back in (12.27) we deduce  $C |t_0 - 1/2| \geq k - 1$ , which is impossible if k has been chosen greater than  $1+C/2$ . Thus  $h_{\varepsilon}$ has to reach its maximum either at  $t = 0$  or  $t = 1$ , i.e.

$$
h(t) + \varepsilon (t - 1/2)^k \le \max(h(0), h(1)) + \varepsilon 2^{-k}.
$$

Letting  $\varepsilon \to 0$ , we conclude again to (12.15).

To conclude the proof of the theorem, it only remains to treat the case when x does not belong to  $Dom'(\nabla_y c(\cdot, y_t))$  for all t, or equivalently when the path  $(y_t)$  is not contained in Dom' $(\nabla_x c(x, \cdot)$ . Let us consider for instance the case  $\mathfrak{S}_c > 0$ . Thanks to Lemma 12.39, we can approximate  $(y_t)$  by a very close path  $(\widehat{y}_t)$ , in such a way that  $(y_t)$ leaves  $Dom'(\nabla_x c(x, \cdot))$  only on a discrete set of times  $t_j$ .

Outside of these times, the same computations as before can be repeated with  $\hat{y}_t$  in place of  $y_t$  (here I am cheating a bit since  $(\hat{y}_t)$  is not a c-segment any longer, but it is no big deal to handle correction terms). So h cannot achieve a maximum in  $(0, 1)$  except maybe at some time  $t_i$ , and it all amounts to proving that  $t_i$  cannot be a maximum of h either. This is obvious if h is continuous at  $t_j$  and  $\dot{h}(t_j) \neq 0$ . This is also obvious if  $\dot{h}$  is discontinuous at  $t_j$ , because by semiconvexity of  $t \to -c(x, y_t)$ , necessarily  $\dot{h}(t_j^+) > \dot{h}(t_j^-)$ . Finally, if  $\dot{h}$  is continuous at  $t_i$  and  $\dot{h}(t_i) = 0$ , the same computations as before show that  $\ddot{h}(t)$ is strictly positive when t is close to (but different from)  $t_j$ , then the continuity of  $\dot{h}$  implies that h is strictly convex around  $t_j$ , so it cannot have a maximum at  $t_j$ .

With Theorems 12.35 and 12.36 at hand it becomes possible to prove or disprove the regularity of certain simple cost functions. Typically, one first tries to exhaust  $Dom'(\nabla_x c(x, \cdot))$  by smooth open sets compactly included in its interior, and one checks the c-convexity of these sets by use of the c-second fundamental form; and similarly for the x variable. Then one checks the sign condition on  $\mathfrak{S}_c$ . In simple enough situations, this strategy can be worked out successfully.

**Example 12.40.** If f and g are  $C^2$  convex functions  $\mathbb{R}^n \to \mathbb{R}$  with  $|\nabla f| < 1, |\nabla g| < 1$ , then  $c(x, y) = |x - y|^2 + |f(x) - g(y)|^2$  satisfies  $\mathfrak{S}_c \geq 0$  on  $\mathbb{R}^n \times \mathbb{R}^n$ , so it is regular on  $\mathbb{R}^n \times \mathbb{R}^n$ . If  $\nabla^2 f$  and  $\nabla^2 g$  are positive everywhere, then  $\mathfrak{S}_c > 0$ , so c is strictly regular on  $\mathbb{R}^n \times \mathbb{R}^n$ .

**Examples 12.41.** The cost functions  $c(x, y) = \sqrt{1 + |x - y|^2}$  in  $\mathbb{R}^n \times$  $\mathbb{R}^n$ ,  $\sqrt{1-|x-y|^2}$  in  $B(0,1) \times B(0,1) \setminus \{|x-y| \ge 1\}$ ,  $|x-y|^p$  on  $\mathbb{R}^n \times \mathbb{R}^n \setminus \{y = x\}$  for  $0 < p < 1$ ,  $d(x, y)^2$  on  $S^{n-1} \times S^{n-1} \setminus \{y = -x\}$ , all satisfy  $\mathfrak{S}_c > 0$ , and are therefore strictly regular on any totally c-convex subdomain (for instance,  $B(0, 1/2) \times B(0, 1/2)$  for  $|x - y|^p$ ). The same is true of the singular cost functions  $|x-y|^p$   $(-2 < p < 0), -\log |x-y|$ on  $\mathbb{R}^n \times \mathbb{R}^n \setminus \{y = x\}$ , or  $-\log|x - y|$  on  $S^{n-1} \times S^{n-1} \setminus \{y = \pm x\}$ . Also the limit case  $c(x, y) = |x - y|^{-2}$  satisfies  $\mathfrak{S}_c \geq 0$  on  $\mathbb{R}^n \times \mathbb{R}^n \setminus \{x = y\}.$ 

Theorem 12.42 (Equivalence of regularity conditions). Let M, N be Riemannian manifolds and let  $c: M \times N \to \mathbb{R}$  be a locally semiconcave cost function such that c and č satisfy  $(STwist)$ , c satisfies  $(\text{Cut}^{n-1})$ , and the c-exponential map extends into a continuous map  $TM \rightarrow N$ . Further, assume that  $Dom'(\nabla_x c)$  is totally c-convex and  $\text{Dom}'(\nabla_x \check{c})$  is totally  $\check{c}$ -convex. Then the following three properties are equivalent:

- (i) c satisfies Assumption  $(C)$ ;
- $(ii)$  c is regular;
- (iii) c satisfies the Ma–Trudinger–Wang condition  $\mathfrak{S}_c \geq 0$ .

**Remark 12.43.** The implications (i)  $\Rightarrow$  (ii) and (ii)  $\Rightarrow$  (iii) remain true without the convexity assumptions. It is a natural open problem whether these assumptions can be completely dispensed with in Theorem 12.42. A bold conjecture would be that (i), (ii) and (iii) are always equivalent and automatically imply the total c-convexity of Dom $'(\nabla_x c)$ .

Proof of Theorem 12.42. Theorem 12.36 ensures the equivalence of (ii) and (iii). We shall now see that (i) and (ii) are also equivalent.

Assume (ii) is satisfied. Let  $\psi : M \to \mathbb{R}$  be a c-convex function, and let  $\overline{x} \in M$ ; the goal is to show that  $\partial_c \psi(\overline{x})$  is connected. Without loss of generality we may assume  $\psi(\overline{x}) = 0$ . Let  $\psi_{\overline{x},y_0,y_1}$  be defined as in (12.17). The same reasoning as in the proof of Proposition 12.15(i) shows that  $\partial_c \psi_{\overline{x},y_0,y_1} \subset \partial_c \psi(\overline{x})$ , so it is sufficient to show that  $\partial_c \psi_{\overline{x},y_0,y_1}$ is connected. Even if  $y_0, y_1$  do not belong to Dom' $(\nabla_x c(\overline{x}, \cdot))$ , the latter set has an empty interior as a consequence of  $(\text{Cut}^{n-1})$ , so we can find sequences  $(y_0^{(k)}$  $\binom{k}{0}$ <sub>k∈N</sub> and  $\binom{y^{(k)}_1}{1}$  $\binom{k}{1}$ <sub>k∈N</sub> such that  $(\overline{x}, y_i^{(k)})$  $\binom{k}{i}$   $\in$  Dom<sup>'</sup> $(\nabla_x c)$  and  $y_i^{(k)} \to y_i$  (i = 0, 1). In particular, there are  $p_i^{(k)} \in T_{\overline{x}}M$ , uniquely determined, such that  $\nabla_x c(\overline{x}, y_i^{(k)})$  $i^{(k)}$  +  $p_i^{(k)} = 0$ .

Then let  $\psi^{(k)} = \psi_{\overline{x}, y_0^{(k)}, y_1^{(k)}}$ . The c-segment  $[y_0^{(k)}]$  $y_0^{(k)}, y_1^{(k)}$  $\left[\begin{matrix}1 \end{matrix}\right]$   $\overline{x}$  is welldefined and included in  $\partial_c \psi^{(k)}(\overline{x})$  (because c is regular). In other words,  $c$ -exp<sub> $\overline{x}((1-t)p_0^{(k)}+tp_1^{(k)}) \in \partial_c\psi^{(k)}(\overline{x})$ . Passing to the limit as</sub>  $k \to \infty$ , after extraction of a subsequence if necessary, we find vectors  $p_0, p_1$  (not necessarily uniquely determined) such that  $c$ -exp<sub> $\overline{x}(p_0) = y_0$ ,</sub>  $c$ -exp<sub> $\overline{x}(p_1) = y_1$ , and  $c$ -exp<sub> $\overline{x}((1-t)p_0 + tp_1) \in \partial_c \psi(\overline{x})$ . This proves the</sub></sub> desired connectedness property.

Conversely, assume that (i) holds true. Let  $\overline{x}, y$  be such that  $(\overline{x}, y) \in$ Dom'( $\nabla_x c$ ), and let  $p = -\nabla_x c(\overline{x}, y)$ . Let  $\nu$  be a unit vector in  $T_y M$ ; for  $\varepsilon > 0$  small enough,  $y_0^{(\varepsilon)} = c \cdot \exp_{\overline{x}}(p + \varepsilon \nu)$  and  $y_1^{(\varepsilon)} = c \cdot \exp_{\overline{x}}(p - \varepsilon \nu)$ belong to Dom  $'(\nabla_x c(\overline{x}, \cdot))$ . Let  $\psi^{(\varepsilon)} = \psi_{\overline{x}, y_0^{(\varepsilon)}, y_1^{(\varepsilon)}}$ . As  $\varepsilon \to 0$ ,  $\partial_c \psi^{(\varepsilon)}(\overline{x})$ shrinks to  $\partial_c \psi_{\overline{x},y,y}(\overline{x}) = \{y\}$  (because  $(\overline{x},y) \in \text{Dom}'(\nabla_x c)$ ). Since Dom  $'(\nabla_x c)$  is open, for  $\varepsilon$  small enough the whole set  $\partial_c \psi^{\varepsilon}(\overline{x})$  is included in  $Dom'(\nabla_x c)$ . By the same reasoning as in the proof of Proposition 12.15(iii), the connectedness of  $\partial_c \psi^{\varepsilon}(\overline{x})$  implies its c-convexity. Then the proof of Theorem 12.36(i) can be repeated to show that  $\mathfrak{S}_c(\overline{x},y) \ge 0$  along pairs of vectors satisfying the correct orthogonality condition. condition. □

I shall conclude this section with a negative result displaying the power of the differential reformulation of regularity.

Theorem 12.44 (Smoothness of the optimal transport needs nonnegative curvature). Let  $M$  be a compact Riemannian manifold such that the sectional curvature  $\sigma_{\overline{x}}(P)$  is negative for some  $\overline{x} \in M$ and some plane  $P \subset T_{\overline{x}}M$ . Then there exist smooth positive probability

densities f and g on M such that the optimal transport map T from f vol to g vol, with cost  $c(x, y) = d(x, y)^2$ , is discontinuous.

The same conclusion holds true under the weaker assumption that  $\mathfrak{S}_{c}(\overline{x},\overline{y})\cdot(\xi,\eta)$  < 0 for some  $(\overline{x},\overline{y})\in M\times M$  such that  $\overline{y}$  does not belong to the cut locus of  $\overline{x}$  and  $\nabla_{xy}^2 c(\overline{x}, \overline{y}) \cdot (\xi, \eta) = 0$ .

Remark 12.45. A counterexample by Y.-H. Kim shows that the second assumption is strictly weaker than the first one.

*Proof of Theorem 12.44.* Let  $\xi, \eta$  be orthogonal tangent vectors at x generating P. By Particular Case 12.30,  $\mathfrak{S}_c(\overline{x},\overline{x})(\xi,\eta) < 0$ . If we fix a neighborhood V of  $\overline{x}$  we can find  $r > 0$  such that for any x the set  $C_x = (\nabla_x c)^{-1}(x, B_r(0))$  is well-defined. If we take a small enough subdomain of U, containing  $\overline{x}$  and define  $C = \bigcap_{x \in U} C_x$ , then  $U \times C$  is totally c-convex and open. By Theorem 12.36, c is not regular in  $U \times C$ .

For any  $y_0, y_1$  in C, define  $\psi_{\overline{x},y_0,y_1}$  as in (12.17). If we let  $U \times C$ shrink to  $\{\overline{x}\}\times\{\overline{x}\}, \partial_c\psi_{\overline{x},y_0,y_1}(\overline{x})$  will converge to  $\partial_c\psi_{\overline{x},\overline{x},\overline{x}}(\overline{x}) = \{\overline{x}\}.$ So if U and C are small enough,  $\partial_c \psi_{\overline{x},y_0,y_1}(\overline{x})$  will be contained in an arbitrarily small ball around  $\overline{x}$ , a fortiori in Dom<sup>'</sup>( $\nabla_x c(\overline{x}, \cdot)$ ). Then we can apply Theorem 12.20 and Corollary 12.21.

A similar reasoning works for the more general case when  $\mathfrak{S}_c(\overline{x}, \overline{y})$ .  $(\xi, \eta) < 0$ , since  $\partial_c \psi_{\overline{x}, \overline{y}, \overline{y}} = {\overline{y}}$  (as long as  $\overline{y}$  is not a cut point of  $\overline{x}$ ). □

# Differential formulation of c-convexity

Its definition makes the *c*-convexity property quite difficult to check in general. In contrast, to establish the plain convexity of a smooth function  $\mathbb{R}^n \to \mathbb{R}$  it is sufficient to just prove the nonnegativity of its Hessian. If  $c$  is an arbitrary cost function, there does not seem to be such a simple differential characterization for  $c$ -convexity; but if  $c$  is a regular cost function there is one, as the next result will demonstrate. The notation is the same as in the previous section.

Theorem 12.46 (Differential criterion for  $c$ -convexity). Let  $c$ :  $\mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  be a cost function such that c and č satisfy (STwist), and let D be a totally c-convex closed subset of  $\text{Dom}'(\nabla_x c)$  such that D is totally c-convex and  $\mathfrak{S}_c \geq 0$  on D. Let  $\mathcal{X}' = \text{proj}_{\mathcal{X}}(D)$  and let  $\psi \in C^2(\mathcal{X}'; \mathbb{R})$  (meaning that  $\psi$  is twice continuously differentiable on

 $\mathcal{X}'$ , up to the boundary). If for any  $x \in \mathcal{X}'$  there is  $y \in \mathcal{Y}$  such that  $(x,y) \in D$  and

$$
\begin{cases}
\nabla \psi(x) + \nabla_x c(x, y) = 0 \\
\nabla^2 \psi(x) + \nabla_x^2 c(x, y) \ge 0,
\end{cases}
$$
 (12.28)

then  $\psi$  is c-convex on  $\mathcal{X}'$  (or more rigorously, c'-convex, where c' is the restriction of c to  $D$ ).

Remark 12.47. In view of the discussion at the beginning of this chapter, (12.28) is a necessary and sufficient condition for c-convexity, up to issues about the smoothness of  $\psi$  and the domain of differentiability of c. Note that the set of y's appearing in  $(12.28)$  is not required to be the whole of  $proj_{\mathcal{V}}(D)$ , so in practice one may often enlarge D in the y variable before applying Theorem 12.46.

**Remark 12.48.** Theorem 12.46 shows that if  $c$  is a regular cost, then (up to issues about the domain of definition) c-convexity is a local notion.

*Proof of Theorem 12.46.* Let  $\psi$  satisfy the assumptions of the theorem, and let  $(\overline{x}, \overline{y}) \in D$  such that  $\nabla \psi(\overline{x}) + \nabla_x c(\overline{x}, \overline{y}) = 0$ . The goal is

$$
\forall x \in \mathcal{X}', \qquad \psi(x) + c(x, \overline{y}) \ge \psi(\overline{x}) + c(\overline{x}, \overline{y}). \tag{12.29}
$$

If this is true then  $\psi^c(\overline{y}) = \psi(\overline{x}) + c(\overline{x}, \overline{y})$ , in particular  $\psi^{cc}(\overline{x}) =$  $\sup_y[\psi^c(y) - c(\overline{x}, y)] \ge \psi(\overline{x})$ , and since this is true for any  $\overline{x} \in \mathcal{X}'$ we will have  $\psi^{cc} \geq \psi$ , therefore  $\psi^{cc} = \psi$ , so  $\psi$  will be *c*-convex.

The proof of (12.29) is in the same spirit as the proof of the converse implication in Theorem 12.36. Let  $x \in \mathcal{X}'$ , and let  $(x_t)_{0 \le t \le 1} = [\overline{x}, x]_{\overline{y}}$ be the  $\check{c}$ -segment with base  $\overline{y}$  and endpoints  $x_0 = \overline{x}$  and  $x_1 = x$ . Let

$$
h(t) = \psi(x_t) + c(x_t, \overline{y}).
$$

To prove (12.29) it is sufficient to show that  $h(t) \geq h(0)$  for all  $t \in [0, 1]$ .

The č-convexity of  $\check{D}$  implies that  $(x_t, \overline{y})$  always lies in  $D$ . Let  $q = -\nabla_y c(x, \overline{y}), \overline{q} = -\nabla_y c(\overline{x}, \overline{y}), \eta = q - \overline{q}$ , then as in the proof of Theorem 12.36 we have  $(\dot{x})^j = -c^{k,j} \eta_k = \eta^j$ ,

$$
\dot{h}(t) = \left[\psi_i(x_t) + c_i(x_t, \overline{y})\right] \eta^i = -c_{i,j}(x_t, \overline{y}) \eta^i \zeta^j
$$

where  $\zeta_i = \psi_i(x_t) + c_i(x_t, \overline{y})$  and  $\zeta^j = -c^{j,i}\zeta_i$ ; similarly,

$$
\ddot{h}(t) = \left( \left[ \psi_{ij}(x_t) + c_{ij}(x_t, \overline{y}) \right] + c_{ij,k}(x_t, \overline{y}) \zeta^k \right) \eta^i \eta^j. \tag{12.30}
$$

By assumption there is  $y_t$  such that  $\nabla \psi(x_t) + \nabla_x c(x_t, y_t) = 0$ , in particular  $\zeta_i = c_i(x_t, \overline{y}) - c_i(x_t, y_t)$ . Then (12.30) can be rewritten as  $\Phi(\overline{p}_t) + d_p \Phi(\overline{p}_t) \cdot (p_t - \overline{p}_t)$ , where

$$
\Phi(y) := \left[\psi_{ij}(x_t) + c_{ij}(x_t, y)\right] \eta^i \eta^j
$$

is seen as a function of  $p = -\nabla_x c(x_t, y)$ , and of course  $p_t = -\nabla_x c(x_t, y_t)$ ,  $\overline{p}_t = -\nabla_x c(x_t, \overline{y})$ . (Note that  $\psi$  does not contribute to  $d_p \Phi$ .) After using the c-convexity of  $D$  and a Taylor formula, we end up with formulas that are quite similar to (12.26):

The following system of equations is presented:

$$
\begin{cases}
\dot{h}(t) = \nabla_{x,y}^2 c(x_t, \overline{y}) \cdot (\eta, \zeta) \\
\ddot{h}(t) = \left[\nabla^2 \psi(x_t) + \nabla_x^2 c(x_t, y_t)\right] \cdot (\zeta, \zeta) \\
+ \frac{2}{3} \int_0^1 \mathfrak{S}_c \Big(x_t, (\nabla_x c)^{-1} \big(x_t, (1-s)p_t + s\overline{p}_t\big)\Big) \cdot (\eta, \zeta) \left(1-s\right) ds.
\end{cases}
$$

$$
(12.31)
$$

By assumption the first term in the right-hand side of  $\ddot{h}$  is nonnegative, so, reasoning as in the proof of Theorem 12.36 we arrive at

$$
\ddot{h} \ge -C |\dot{h}(t)|,\tag{12.32}
$$

where C is a positive constant depending on c,  $\psi$ ,  $\overline{x}$ ,  $\overline{y}$ , x. We shall see that (12.32), combined with  $h(0) = 0$ , implies that h is nondecreasing on [0, 1], and therefore  $h(t) \geq h(0)$ , which was our goal.

Assume indeed that  $h(t_*)$  < 0 for some  $t_* \in (0,1]$ , and let  $t_0 =$ sup  $\{t \leq t_*; h(t) = 0\} \geq 0$ . For  $t \in (t_0, t_*)$  we have  $h(t) < 0$  and  $(d/dt)\log |h(t)| = h/h \leq C$ , so  $\log |h(t)| \geq \log |h(t_*)| - C(t_* - t)$ , and as  $t \to t_0$  we obtain a contradiction since  $\log |h(t_0)| = -\infty$ . The conclusion is that  $\dot{h}(t) \geq 0$  for all  $t \in [0, 1]$ , and we are done. conclusion is that  $h(t) \geq 0$  for all  $t \in [0, 1]$ , and we are done.

# Control of the gradient via c-convexity

The property of c-convexity of the target is the key to get good control of the localization of the gradient of the solution to (12.2). This assertion might seem awkward: After all, we already know that under general assumptions,  $T(\text{Spt }\mu) = \text{Spt }\nu$  (recall the end of Theorem 10.28), where the transport T is related to the gradient of  $\psi$  by  $T(x) =$  $(\nabla_x c)^{-1}(x, -\nabla \psi(x));$  so  $\nabla \psi(x)$  always belongs to  $-\nabla_x c(\text{Spt }\mu, \text{Spt }\nu)$ when x varies in Spt  $\mu$ .

To understand why this is not enough, assume that you are approximating  $\psi$  by smooth approximate solutions  $\psi_k$ . Then  $\nabla \psi_k \longrightarrow \nabla \psi$ at all points of differentiability of  $\psi$ , but you have no control of the behavior of  $\nabla \psi_k(x)$  if  $\psi$  is not differentiable at x! In particular, in the approximation process the point  $y$  might very well get beyond the support of  $\nu$ , putting you in trouble. To guarantee good control of smooth approximations of  $\psi$ , you need an information on the whole c-subdifferential  $\partial_c \psi(x)$ . The next theorem says that such control is available as soon as the target is c-convex.

Theorem 12.49 (Control of  $c$ -subdifferential by  $c$ -convexity of target). Let  $\mathcal{X}, \mathcal{Y}, c : \mathcal{X} \times \mathcal{Y} \to \mathbb{R}, \mu \in P(\mathcal{X}), \nu \in P(\mathcal{Y})$  and  $\psi : \mathcal{X} \to \mathbb{R} \cup \{+\infty\}$  satisfy the same assumptions as in Theorem 10.28 (including  $(H\infty)$ ). Let  $\Omega \subset \mathcal{X}$  be an open set such that  $\text{Spt}\,\mu = \overline{\Omega}$ , and let  $C \subset \mathcal{Y}$  be a closed set such that  $\text{Spt } \nu \subset C$ . Assume that:

(a)  $\Omega \times C \subset \text{Dom}'(\nabla_x c)$ ;

(b) C is c-convex with respect to  $\Omega$ .

Then  $\partial_c \psi(\Omega) \subset C$ .

Proof of Theorem 12.49. We already know from Theorem 10.28 that  $T(\overline{\Omega}) \subset C$ , where  $T(x) = (\nabla_x c)^{-1}(x, -\nabla \psi(x))$  stands for the optimal transport. In particular,  $\partial_c \psi(\Omega \cap \text{Dom}(\nabla \psi)) \subset C$ . The problem is to control  $\partial_c \psi(x)$  when  $\psi$  is not differentiable at x.

Let  $x \in \Omega$  be such a point, and let  $y \in \partial_c \psi(x)$ . By Theorem 10.25,  $-\nabla_x c(x,y) \in \nabla^- \psi(x)$ . Since  $\psi$  is locally semiconvex, we can apply Remark 10.51 to deduce that  $\nabla^- \psi(x)$  is the convex hull of limits of  $\nabla \psi(x_k)$  when  $x_k \to x$ . Then by Proposition 12.15(ii), there are  $L \in \mathbb{N}$  $(L = n + 1 \text{ would do}), \alpha_{\ell} \geq 0 \text{ and } (x_{k,\ell})_{k \in \mathbb{N}} (1 \leq \ell \leq L) \text{ such that}$  $\sum \alpha_{\ell} = 1, x_{k,\ell} \to x$  as  $k \to \infty$ , and

$$
\sum_{\ell=1}^{L} \alpha_{\ell} \nabla \psi(x_{k,\ell}) \xrightarrow[k \to \infty]{} -\nabla_x c(x,y). \tag{12.33}
$$

From the observation at the beginning of the proof,  $\nabla \psi(x_{k,\ell}) \in$  $-\nabla_x c(x_{k,\ell},C)$ , and as  $k \to \infty$  this set converges uniformly (say in the sense of Hausdorff distance) to  $-\nabla_x c(x, C)$  which is convex. By passing to the limit in (12.33) we get  $-\nabla_x c(x,y) \in -\nabla_x c(x,C)$ , so  $y \in C$ .  $y \in C.$
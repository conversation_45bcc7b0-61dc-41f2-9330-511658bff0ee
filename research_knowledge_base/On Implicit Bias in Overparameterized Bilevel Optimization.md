# On Implicit Bias in Overparameterized Bilevel Optimization

<PERSON><sup>12</sup> <PERSON><sup>12</sup> <PERSON><sup>3</sup> <PERSON><sup>12</sup> <PERSON><sup>12</sup>

# Abstract

Many problems in machine learning involve bilevel optimization (BLO), including hyperparameter optimization, meta-learning, and dataset distillation. Bilevel problems involve inner and outer parameters, each optimized for its own objective. Often, at least one of the two levels is underspecified and there are multiple ways to choose among equivalent optima. Inspired by recent studies of the implicit bias induced by optimization algorithms in single-level optimization, we investigate the implicit bias of different gradient-based algorithms for jointly optimizing the inner and outer parameters. We delineate two standard BLO methods—cold-start and warm-start BLO—and show that the converged solution or long-run behavior depends to a large degree on these and other algorithmic choices, such as the hypergradient approximation. We also show that the solutions from warm-start BLO can encode a surprising amount of information about the outer objective, even when the outer optimization variables are low-dimensional. We believe that implicit bias deserves as central a role in the study of bilevel optimization as it has attained in the study of single-level neural net optimization.

## 1. Introduction

Bilevel optimization (BLO) problems consist of two nested sub-problems, called the *outer* and *inner* problems, where the outer problem must be solved subject to the optimality of the inner problem. Let  $u \in \mathcal{U}$ ,  $w \in \mathcal{W}$  denote the outer and inner parameters, and let  $F, f : U \times W \rightarrow \mathbb{R}$  denote the outer and inner objectives, respectively. The BLO problem is defined as:

$$
\mathbf{u}^{\star} \in \text{``arg}\min_{\mathbf{u} \in \mathcal{U}} \text{''}F(\mathbf{u}, \mathbf{w}^{\star})
$$
 (1)

$$
\mathbf{w}^{\star} \in \mathcal{S}(\mathbf{u}^{\star}) = \underset{\mathbf{w} \in \mathcal{W}}{\arg \min} f(\mathbf{u}^{\star}, \mathbf{w}) \tag{2}
$$

where  $S: U \rightrightarrows W$  is a set-valued *response mapping*, from each outer parameter u to a set of optimal inner parameters  $S(\mathbf{u})$ . We use quotes around the outer "arg min" to denote the ambiguity in the definition of the bilevel problem when the inner objective has multiple solutions, a point we expand upon in Section [2.](#page-1-0) Examples of bilevel optimization in machine learning include hyperparameter optimization [\(Domke,](#page-9-0) [2012;](#page-9-0) [Maclaurin et al.,](#page-10-0) [2015;](#page-10-0) [MacKay](#page-10-0) [et al.,](#page-10-0) [2019;](#page-10-0) [Lorraine et al.,](#page-10-0) [2020;](#page-10-0) [Shaban et al.,](#page-11-0) [2019\)](#page-11-0), dataset distillation [\(Wang et al.,](#page-11-0) [2018;](#page-11-0) [Zhao et al.,](#page-12-0) [2021\)](#page-12-0), influence function estimation [\(Koh & Liang,](#page-10-0) [2017\)](#page-10-0), metalearning [\(Finn et al.,](#page-9-0) [2017;](#page-9-0) [Franceschi et al.,](#page-9-0) [2018;](#page-9-0) [Ra](#page-11-0)[jeswaran et al.,](#page-11-0) [2019\)](#page-11-0), example reweighting [\(Bengio et al.,](#page-9-0) [2009;](#page-9-0) [Ren et al.,](#page-11-0) [2018\)](#page-11-0), neural architecture search [\(Zoph &](#page-12-0) [Le,](#page-12-0) [2017;](#page-12-0) [Liu et al.,](#page-10-0) [2019\)](#page-10-0) and adversarial learning [\(Good](#page-9-0)[fellow et al.,](#page-9-0) [2014;](#page-9-0) [Pfau & Vinyals,](#page-11-0) [2016\)](#page-11-0) (see Table [1\)](#page-1-0).

Most algorithmic contributions to BLO make the simplifying assumption that the inner problem has a unique solution (that is,  $|S(u)| = 1$ ,  $\forall u \in \mathcal{U}$ ), and give approximation methods that provably get close to the solution [\(Shaban et al.,](#page-11-0) [2019;](#page-11-0) [Pedregosa,](#page-11-0) [2016;](#page-11-0) [Yang et al.,](#page-12-0) [2021;](#page-12-0) [Hong et al.,](#page-10-0) [2020;](#page-10-0) [Grazzi et al.,](#page-9-0) [2020a\)](#page-9-0). In practice, however, these algorithms are often run in settings where either the inner or outer problem is *underspecified*; i.e., the set of optima forms a non-trivial manifold. Underspecification often occurs due to overparameterization, e.g., given a learning problem with more parameters than datapoints, there are typically infinitely many solutions that achieve the optimal objective value [\(Belkin,](#page-9-0) [2021\)](#page-9-0). Analyses of the dynamics of overparameterized single-objective optimization have shown that the nature of the converged solution, such as its pattern of generalization, depends in subtle ways on the details of the optimization dynamics [\(Lee et al.,](#page-10-0) [2019;](#page-10-0) [Arora et al.,](#page-9-0) [2019;](#page-9-0) [Bartlett et al.,](#page-9-0) [2020;](#page-9-0) [Amari et al.,](#page-9-0) [2020\)](#page-9-0); this general phenomenon is known as *implicit bias*. We extend this investigation to the bilevel setting: What are the implicit biases of practical BLO algorithms? What types of solutions do they favor, and what are the implications for how the trained models generalize?

We identify two sources of implicit bias in underspecified BLO: 1) implicit bias resulting from the optimization algorithm, either cold-start or warm-start BLO (described below); and 2) implicit bias resulting from the hypergradient approximation. Classic literature on bilevel optimiza-

<sup>&</sup>lt;sup>1</sup>University of Toronto <sup>2</sup>Vector Institute  ${}^{3}$ Google Research. Correspondence to: Paul Vicol <<EMAIL>>.

*Proceedings of the*  $39<sup>th</sup>$  *International Conference on Machine Learning*, Baltimore, Maryland, USA, PMLR 162, 2022. Copyright 2022 by the author(s).

On Implicit Bias in Overparameterized Bilevel Optimization

<span id="page-1-0"></span>Image /page/1/Figure/1 description: The image displays four related plots labeled (a), (b), (c), and (d). Plot (a) is a 3D surface plot showing a function f(u, w) with 'Outer Params u' and 'Inner Params w' as axes. An orange shaded region labeled 'S(u)' highlights a part of the surface. Plot (b) is a 2D plot titled 'Set-Valued Response Mapping', with 'u' on the x-axis and 'f(u, w)' and 'S(u)' on the y-axis. It shows a curve representing the lower bound of a set-valued response, with a shaded gray region above it, and a vertical line at u=1 indicating 'S(1)'. Plots (c) and (d) are schematic diagrams illustrating mappings. In (c), elements from set U (u1, u2, u3) map to set W (w1, w2, w3), and then to the real numbers (R). Specifically, u1 maps to w1, u2 maps to w2, and u3 maps to w3. From W to R, w1 maps to a single value, w2 maps to a single value, and w3 maps to a single value. Plot (d) shows a similar structure but with different mappings: u1 maps to w1, u2 maps to w2, and u3 maps to w3. From W to R, w1 maps to a single value, w2 maps to a single value, and w3 maps to a single value. Purple arrows in (c) and (d) indicate specific mappings from W to R.

Figure 1: Underspecification in BLO. (a) Simplified visualization of inner underspecification, yielding a manifold of optimal solutions  $S(u) = \arg\min_{w} f(u, w)$  for each u (a slice is highlighted in blue for a fixed u). The orange region along the floor of the valley highlights the set-valued best-response mapping. (b) The set-valued best-response for  $u, w \in \mathbb{R}$ , where the shaded gray region contains values of w that achieve equivalent performance on the inner objective. The green curve highlights the minimum-norm inner solutions for each u. (c) Outer underspecification due to F mapping a range of inner parameters to the same value; or (d) from  $\mathcal{S}(u)$  associating a range of outer parameters with the same inner parameters.

| Task                   | Inner (w)     | Outer (u)       | InnerU | OuterU |
|------------------------|---------------|-----------------|--------|--------|
| Data Distil.           | NN weights    | Synthetic data  | ✓      | X      |
| Data Aug.              | NN weights    | Aug params      | ✓      | X      |
| GANs                   | Discriminator | Generator       | ✓      | ✓      |
| Meta-Learn.            | NN weights    | NN weights      | ✓      | ✓      |
| <b>NAS</b>             | NN weights    | Architectures   | ✓      | ✓      |
| Hyperopt               | NN weights    | Hyperparams     | ✓      | X      |
| Example<br>reweighting | NN weights    | Example weights | ✓      | ✓      |

Table 1: Inner and outer overparameterization in common bilevel tasks. For each task, we reference whether the common use-case includes inner/outer underspecification (InnerU/OuterU).

tion [\(Dempe,](#page-9-0) [2002\)](#page-9-0) considers two ways to break ties between solutions in the set  $\mathcal{S}(\mathbf{u})$ : optimistic and pessimistic equilibria (discussed in Section 2). However, as we will show, neither describes the solutions obtained by the most common BLO algorithms. We focus on two algorithms that are relevant for practical machine learning, which we term *cold-start* and *warm-start* BLO. Cold-start BLO [\(Maclaurin](#page-10-0) [et al.,](#page-10-0) [2015;](#page-10-0) [Metz et al.,](#page-10-0) [2019;](#page-10-0) [Micaelli & Storkey,](#page-10-0) [2020\)](#page-10-0) defines the inner solution  $w^*$  as the fixpoint of an update rule, which captures the notion of running an optimization algorithm to convergence. For each outer update, the inner parameters are re-initialized to  $w_0$  and the inner optimization is run from scratch. This is computationally expensive, as it requires full unrolls of the inner optimization for each outer step. Warm-start BLO is a tractable and widely-used alternative [\(Luketina et al.,](#page-10-0) [2016;](#page-10-0) [MacKay et al.,](#page-10-0) [2019;](#page-10-0) [Lor](#page-10-0)[raine et al.,](#page-10-0) [2020;](#page-10-0) [Tang et al.,](#page-11-0) [2020\)](#page-11-0) that alternates gradient descent steps on the inner and outer parameters: in each step of warm-start BLO, the inner parameters are optimized from their *current values*, rather than the initialization  $w_0$ .

In Section [3,](#page-3-0) we characterize solution concepts that capture the behavior of the cold- and warm-start BLO algorithms and show that, for quadratic inner and outer objectives, cold-start BLO yields minimum-norm outer parameters. In Section [4,](#page-5-0) we show that warm-start BLO induces an implicit bias on the inner parameter iterates, regularizing the updates to maintain proximity to previous solutions.

In addition to the BLO algorithm, another source of implicit bias is the hypergradient approximation used. In Sections [3](#page-3-0) and [4,](#page-5-0) we investigate the effect of using approximate implicit differentiation to compute the hypergradient  $\frac{dF}{du}$ , where different approximations can lead to vastly different outer solutions.

Inner Underspecification. Most bilevel tasks in machine learning involve training a neural network in the inner level, which typically yields an underspecified problem, as shown in Table 1. Figure 1(a,b) illustrates set-valued response mappings in underspecified BLO.

Outer Underspecification. Outer underspecification can arise in two ways: 1) the mapping  $\mathcal{S}(\mathbf{u})$  is a function (e.g., not set-valued) and maps a range of outer parameters to the same inner parameter; or 2) the outer objective  $F$  maps a range of inner parameters to the same objective value. These two pathways are illustrated in Figure 1(c,d).

## 2. Background

In this section, we provide an overview of key concepts that we build on. $<sup>1</sup>$ </sup>

**Hypergradient Approximation.** We assume that  $F$  and f are differentiable, and consider gradient-based BLO algorithms, which require the hypergradient  $\frac{dF(\mathbf{u}, \mathbf{w}^*)}{d\mathbf{u}}$  =  $\frac{\partial F(\mathbf{u}, \mathbf{w}^*)}{\partial \mathbf{u}} + \left(\frac{d\mathbf{w}^*}{d\mathbf{u}}\right)^\top \frac{\partial F(\mathbf{u}, \mathbf{w}^*)}{\partial \mathbf{w}^*}.$  The main challenge to computing the hypergradient lies in computing the *response Jacobian*  $\frac{d\mathbf{w}^*}{d\mathbf{u}}$ , which captures how the converged inner parameters depend on the outer parameters. Exactly computing this Jacobian is often intractable for large-scale problems. The two most common approaches to approximate it are: 1) *iterative differentiation*, which unrolls the inner optimization to reach an approximate best-response (BR) and backpropagates through the unrolled computation graph to compute the approximate BR Jacobian [\(Domke,](#page-9-0) [2012;](#page-9-0)

<sup>&</sup>lt;sup>1</sup>We provide a table of notation in Appendix [A.](#page-14-0)

On Implicit Bias in Overparameterized Bilevel Optimization

<span id="page-2-0"></span>Image /page/2/Figure/1 description: The image displays five plots side-by-side, illustrating different decision boundaries for a classification task. The first plot, labeled "Original Data," shows a scatter plot of red dots (Class 0) and green dots (Class 1) with no clear separation. The subsequent plots, titled "Optimistic," "Pessimistic," "Cold-Start," and "Warm-Start," each show a decision boundary (blue line) separating regions colored red and green, representing the predicted classes. Each of these plots also includes a red cross and a green cross, labeled as "Learned Datapoints," indicating specific points used in the learning process. The "Optimistic" and "Pessimistic" plots show complex, wavy decision boundaries. The "Cold-Start" plot features a simple horizontal decision boundary. The "Warm-Start" plot exhibits two wavy decision boundaries, one green and one red, with arrows indicating a progression or change over time, suggesting a dynamic process. A legend at the top clarifies that red squares represent Class 0, green squares represent Class 1, blue lines are decision boundaries, and black crosses are learned datapoints.

Figure 2: Dataset distillation sketch illustrating four solution concepts for BLO: optimistic, pessimistic, cold-start, and warm-start. We distill the dataset consisting of red and green points into two synthetic datapoints denoted by <sup>★</sup> and ★ (one per class). The learned datapoints ✖ and ✖ are the *outer parameters* and the inner parameters correspond to a model (classifier) trained on these synthetic datapoints. We assume an overparameterized inner model, which can fit the synthetic datapoints with many different decision boundaries. All of the solutions here correctly classify the synthetic datapoints (and are thus valid solutions to the inner problem) but they differ in their behavior on the original dataset. Optimistic: finds the decision boundary that correctly classifies all the original datapoints; **Pessimistic:** finds the decision boundary that achieves the worst loss on the original datapoints—it correctly classifies the synthetic datapoints but *incorrectly classifies* the original datapoints (note the *flipped red/green shading* on either side of the decision boundary); Cold-start: finds the min-norm solution that correctly classifies the synthetic datapoints; Warm-start: yields a trajectory of synthetic datapoints over time, which can allow for a model trained on two learned datapoints to fit the original data.

[Maclaurin et al.,](#page-10-0) [2015;](#page-10-0) [Shaban et al.,](#page-11-0) [2019\)](#page-11-0); and 2) *implicit differentiation*, which leverages the implicit function theorem (IFT) to compute the BR Jacobian, assuming that the inner parameters are at a stationary point of the inner objective [\(Larsen et al.,](#page-10-0) [1996;](#page-10-0) [Chen & Hagan,](#page-9-0) [1999;](#page-9-0) [Bengio,](#page-9-0) [2000;](#page-9-0) [Foo et al.,](#page-9-0) [2008;](#page-9-0) [Pedregosa,](#page-11-0) [2016;](#page-11-0) [Lorraine et al.,](#page-10-0) [2020;](#page-10-0) [Hataya et al.,](#page-10-0) [2020b;](#page-10-0) [Raghu et al.,](#page-11-0) [2020\)](#page-11-0). The IFT states that, assuming uniqueness of the inner solution and under certain regularity conditions, we can compute the response Jacobian as  $\frac{\partial w^*(u)}{\partial u} = \left(\frac{\partial^2 f}{\partial w \partial w^{\top}}\right)^{-1} \frac{\partial^2 f}{\partial w \partial u}$ . The term inside the inverse is the Hessian of the inner objective, which is typically intractable to store or invert directly (e.g., neural nets can easily have millions of parameters). Thus, multiplication by the inverse Hessian is typically approximated using an iterative linear solver, such as truncated conjugate gradient (CG) [\(Pedregosa,](#page-11-0) [2016\)](#page-11-0), GMRES [\(Blon](#page-9-0)[del et al.,](#page-9-0) [2021\)](#page-9-0), or the Neumann series [\(Liao et al.,](#page-10-0) [2018;](#page-10-0) [Lorraine et al.,](#page-10-0) [2020\)](#page-10-0). The (un-truncated) Neumann series for an invertible Hessian  $\frac{\partial^2 f}{\partial w \partial w^\top}$  is defined as:

$$
\left(\frac{\partial^2 f}{\partial \mathbf{w} \partial \mathbf{w}^\top}\right)^{-1} = \alpha \sum_{j=0}^{\infty} \left(\mathbf{I} - \alpha \frac{\partial^2 f}{\partial \mathbf{w} \partial \mathbf{w}^\top}\right)^j. \tag{3}
$$

This series is known to converge when the largest eigenvalue of  $I - \alpha \frac{\partial^2 f}{\partial \mathbf{w} \partial \mathbf{w}^\top}$  is < 1. In practice, the Neumann series is typically truncated to the first  $K$  terms. [Lorraine](#page-10-0) [et al.](#page-10-0) [\(2020\)](#page-10-0) showed that differentiating through  $i$  steps of unrolling starting from optimal inner parameters  $w^*$  is equivalent to approximating the inverse Hessian with the first *i* terms in the Neumann series, a result we review in Appendix [F.](#page-20-0) Approximate implicit differentiation can be implemented with efficient Hessian-vector products using modern autodiff libraries [\(Pearlmutter,](#page-11-0) [1994;](#page-11-0) [Abadi et al.,](#page-9-0) [2015;](#page-9-0) [Paszke et al.,](#page-11-0) [2017;](#page-11-0) [Bradbury et al.,](#page-9-0) [2018\)](#page-9-0). However, when the inner problem is overparameterized, the Hessian

 $\frac{\partial^2 f}{\partial \mathbf{w} \partial \mathbf{w}^\top}$  is singular. In Section [3,](#page-3-0) we discuss how the Kterm truncated Neumann series approximates the inverse of the *damped Hessian*  $(H + \epsilon I)^{-1}$ , and we discuss the implications of this approximation.

Optimistic and Pessimistic BLO. For scenarios where the inner problem has multiple solutions, i.e.,  $|S(u)| > 1$ , two solution concepts have been widely studied in the classical BLO literature: the *optimistic* [\(Sinha et al.,](#page-11-0) [2017;](#page-11-0) [Dempe](#page-9-0) [et al.,](#page-9-0) [2007;](#page-9-0) [Dempe,](#page-9-0) [2002;](#page-9-0) [Harker & Pang,](#page-10-0) [1988;](#page-10-0) [Lignola &](#page-10-0) [Morgan,](#page-10-0) [1995;](#page-10-0) [2001;](#page-10-0) [Outrata,](#page-11-0) [1993\)](#page-11-0) and *pessimistic* [\(Sinha](#page-11-0) [et al.,](#page-11-0) [2017;](#page-11-0) [Dempe,](#page-9-0) [2002;](#page-9-0) [Dempe et al.,](#page-9-0) [2014;](#page-9-0) [Loridan](#page-10-0) [& Morgan,](#page-10-0) [1996;](#page-10-0) [Lucchetti et al.,](#page-10-0) [1987;](#page-10-0) [Wiesemann et al.,](#page-11-0) [2013;](#page-11-0) [Liu et al.,](#page-10-0) [2018;](#page-10-0) [2020\)](#page-10-0) solutions.<sup>2</sup> In *optimistic* BLO, w is chosen such that it leads to the best outer objective value. In contrast, in *pessimistic* BLO, w is chosen such that it leads to the worst outer objective value. The corresponding equilibria are defined below, with the differences highlighted in purple and teal.

**Definition 2.1.** *Let*  $S : U \Rightarrow W$  *be the set-valued response mapping*  $S(\mathbf{u}) = \arg \min_{\mathbf{w} \in \mathcal{W}} f(\mathbf{u}, \mathbf{w})$ *. Then*  $(\mathbf{u}^*, \mathbf{w}^*)$  *is an* optimistic/pessimistic equilibrium *if:*

$$
\mathbf{u}^\star \in \argmin_{\mathbf{u}} F(\mathbf{u}, \mathbf{w}^\star) \text{ s.t. } \mathbf{w}^\star \in \arg\min_{\mathbf{w} \in \mathcal{S}(\mathbf{u}^\star)} \text{max} F(\mathbf{u}^\star, \mathbf{w})
$$

Dataset Distillation. Given an original (typically large) dataset, the task of *dataset distillation* [\(Maclaurin et al.,](#page-10-0) [2015;](#page-10-0) [Wang et al.,](#page-11-0) [2018;](#page-11-0) [Lorraine et al.,](#page-10-0) [2020\)](#page-10-0) is to learn a smaller *synthetic dataset* such that a model trained on the synthetic data will generalize well to the original data. In this problem, the inner parameters are the weights of a model, and the outer parameters are the synthetic datapoints. The inner objective is the loss of the inner model trained on the synthetic datapoints, and the outer objective is the loss

 $2$ See [Sinha et al.](#page-11-0) [\(2017\)](#page-11-0) for a comprehensive review.

<span id="page-3-0"></span>of the inner model on the original dataset:

$$
\mathbf{u}^\star \in ``\mathop{\arg\min}_{\mathbf{u}}" \mathcal{L}(\mathbf{w}^\star(\mathbf{u}), \mathcal{D}_{original}), \qquad (4)
$$

$$
\mathbf{w}^{\star}(\mathbf{u}) \in \underset{\mathbf{w}}{\operatorname{arg\,min}} \mathcal{L}(\mathbf{w}, \mathcal{D}(\mathbf{u}))
$$
 (5)

Here,  $\mathcal{L}$  denotes a loss function,  $\mathcal{D}_{original}$  denotes the dataset we aim to distill, and  $\mathcal{D}(\mathbf{u})$  denotes a synthetic dataset parameterized by u. We focus on dataset distillation throughout this paper because the degree of inner and outer overparameterization can be modified by varying the number of original versus synthetic datapoints. The solutions for this task are also easy to visualize and interpret.

Visualizing Solution Concepts. Figure [2](#page-2-0) illustrates four different solution concepts for a toy 2D problem: the optimistic and pessimistic solutions, as well as two new solution concepts—cold- and warm-start equilibria—defined in Section 3. We consider dataset distillation for binary classification, with red and green datapoints representing the two classes, and a sinusoidal ground-truth decision boundary. We distill this dataset into two synthetic datapoints, represented by  $\star$  and  $\star$ . In each subplot, the blue curve shows the decision boundary of the inner classifier, and the background colors show which class is predicted on each side of the decision boundary. See the caption of Figure [2](#page-2-0) for a description of each solution concept on this task.

The optimistic solution can be tractable to compute [\(Mehra](#page-10-0) [& Hamm,](#page-10-0) [2019;](#page-10-0) [Ji & Liang,](#page-10-0) [2021\)](#page-10-0), while the pessimistic solution is known to be less tractable [\(Sinha et al.,](#page-11-0) [2017\)](#page-11-0). From Figure [2,](#page-2-0) however, it is unclear whether either of these solution concepts is useful. For example, in hyperparameter optimization, the optimistic and pessimistic solutions correspond to choosing hyperparameters such that the inner training loop achieves minimum or maximum performance on the validation set, and it is unclear why this would be beneficial. Most widely-used BLO algorithms do not aim to find either of these solution concepts, which motivates our study of the behavior of the algorithms used in practice.

# 3. Equilibrium Concepts

We aim to understand the behavior of two popular BLO algorithms: *cold-start* (App. [H,](#page-24-0) Algorithm [1\)](#page-25-0) and *warmstart* (App. [H,](#page-24-0) Algorithm [2\)](#page-25-0). A summary of the updates for each algorithm is shown in Table 2. In this section, we first define equilibrium notions that capture the solutions found by warm-start and cold-start BLO. We then discuss properties of these equilibria, in particular focusing on the norms of the resulting inner and outer parameters. Finally, we analyze the implicit bias induced by approximating the hypergradient with the truncated Neumann series.

| Method                    | <b>Inner Update</b>                                                                 |
|---------------------------|-------------------------------------------------------------------------------------|
| <b>Full Cold-Start</b>    | $\mathbf{w}_{k+1}^{\star} = \Xi^{(\infty)}(\mathbf{u}_{k+1}, \mathbf{w}_0)$         |
| <b>Full Warm-Start</b>    | $\mathbf{w}_{k+1}^{\star} = \Xi^{(\infty)}(\mathbf{u}_{k+1}, \mathbf{w}_k^{\star})$ |
| <b>Partial Warm-Start</b> | $\mathbf{w}_{k+1}^{\star} = \Xi^{(T)}(\mathbf{u}_{k+1}, \mathbf{w}_k^{\star})$      |

Table 2: Inner parameter updates for cold-start, full warm-start, and partial warm-start BLO.

### 3.1. Cold-Start Equilibrium

Here, we introduce a solution concept that captures the behavior of cold-start BLO. We consider an iterative optimization algorithm used to compute an approximate solution to the inner objective. We denote a step of inner optimization by  $w_{t+1} = \Xi(\mathbf{u}, \mathbf{w}_t)$ ; for gradient descent, we have  $\Xi(\mathbf{u}, \mathbf{w}_t) = \mathbf{w}_t - \alpha \nabla_{\mathbf{w}} f(\mathbf{u}, \mathbf{w}_t)$ . We denote K steps of inner optimization from  $w_0$  by  $\Xi^{(K)}(\mathbf{u}, \mathbf{w}_0)$ . Under certain assumptions (e.g., that  $f$  has a unique finite root and that the step size  $\alpha$  for the update is chosen appropriately), repeated application of  $\Xi$  will converge to a fixpoint. We denote the fixpoint for an initialization  $\mathbf{w}_0$  by  $\Xi^{(\infty)}(\mathbf{u}, \mathbf{w}_0)$ .

**Definition 3.1.** Let  $\mathbf{r}(\mathbf{u}, \mathbf{w}) \triangleq \Xi^{(\infty)}(\mathbf{u}, \mathbf{w})$ . Then  $(\mathbf{u}^*, \mathbf{w}^*)$ *is a cold-start equilibrium for an initialization*  $\mathbf{w}_0$  *if:* 

$$
\mathbf{u}^{\star} \in \argmin_{\mathbf{u}} F(\mathbf{u}, \mathbf{w}^{\star}) \quad s.t. \quad \mathbf{w}^{\star} = \mathbf{r}(\mathbf{u}^{\star}, \mathbf{w}_0)
$$

In some cases, we can compute the fixpoint of the inner optimization analytically. In particular, when  $f$  is quadratic, the analytic solution minimizes the displacement from  $w_0$ :  $\Xi^{(\infty)}(\mathbf{u}, \mathbf{w}_0) = \arg \min_{\mathbf{w} \in \mathcal{S}(\mathbf{u})} \|\mathbf{w} - \mathbf{w}_0\|_2^2.$ 

### 3.2. Warm-Start Equilibrium

Next, we introduce a solution concept intended to capture the behavior of warm-start BLO. Warm-starting refers to initializing the inner optimization from the inner parameters obtained in the previous hypergradient computation. One can consider two variants of warm-starting: (1) using *full inner optimization*, that is, running the inner optimization to convergence starting from  $w_t$  to obtain the next iterate  $w_{t+1}$ ; or (2) *partial inner optimization*, where we approximate the solution to the inner problem via a few gradient steps. Full warm-start can be expressed as computing  $\mathbf{w}_{k+1} = \Xi^{(\infty)}(\mathbf{u}, \mathbf{w}_k)$  in each iteration, starting from the previous inner solution  $w_k$  rather than  $w_0$ . Similarly, partial warm-start can be expressed as  $\mathbf{w}_k = \Xi^{(T)}(\mathbf{u}, \mathbf{w}_k)$ , where T is often small (e.g.,  $T < 10$ ).

**Definition 3.2.** Let  $\mathbf{r}(\mathbf{u}, \mathbf{w}) \triangleq \Xi^{(T)}(\mathbf{u}, \mathbf{w})$ . Then  $(\mathbf{u}^*, \mathbf{w}^*)$ *is a* (full or partial) warm-start equilibrium *if:*

$$
\mathbf{u}^{\star} \in \argmin_{\mathbf{u}} F(\mathbf{u}, \mathbf{w}^{\star}) \quad s.t. \quad \mathbf{w}^{\star} = \mathbf{r}(\mathbf{u}^{\star}, \mathbf{w}^{\star})
$$

*For finite* T*, the solution is a partial warm-start equilibrium. As*  $T \rightarrow \infty$ *, we obtain the full warm-start equilibrium.* 

<span id="page-4-0"></span>

### 3.3. Solution Properties

In this section, we examine properties of cold-start and warm-start equilibria—in particular, we analyze the norms of the inner and outer parameters given by different methods. First, we show that cold-start equilibria (with exact hypergradient computation) yield simple solutions for both the inner and outer problems: when  $F$  and  $f$  are quadratic, cold-start yields minimum-norm parameters at both levels. Then, we analyze the implicit bias induced by the hypergradient approximation, in particular by using the truncated Neumann series to estimate the inverse Hessian for implicit differentiation. To make the analysis tractable, we make the following assumption:

Assumption 3.1 (Quadratic Objectives). *We assume that both the inner and outer objectives,* f *and* F*, are convex lower-bounded quadratics. We let:*

$$
f(\mathbf{u}, \mathbf{w}) = \frac{1}{2} \begin{bmatrix} \mathbf{w}^{\top} & \mathbf{u}^{\top} \end{bmatrix} \begin{bmatrix} \mathbf{A} & \mathbf{B} \\ \mathbf{B}^{\top} & \mathbf{C} \end{bmatrix} \begin{bmatrix} \mathbf{w} \\ \mathbf{u} \end{bmatrix} + \mathbf{d}^{\top} \mathbf{w} + \mathbf{e}^{\top} \mathbf{u} + c
$$

 $where \mathbf{A} \in \mathbb{R}^{|\mathcal{W}| \times |\mathcal{W}|}$  *is positive semi-definite*,  $\mathbf{B} \in \mathbb{R}^{|\mathcal{W}| \times |\mathcal{W}|}$  $\mathbb{R}^{|\mathcal{W}|\times |\mathcal{U}|}$ ,  $\mathbf{C}\in \mathbb{R}^{|\mathcal{U}|\times |\mathcal{U}|}$ . The positive-definite assumption *implies that* f *is a convex quadratic for a fixed* u *. For the outer objective, we restrict our analysis to the case where* F *only depends directly on* w *(corresponding to a* pure response *BLO problem, which encompasses many common applications including hyperparameter optimization):*

$$
F(\mathbf{u}, \mathbf{w}) = \frac{1}{2} \mathbf{w}^\top \mathbf{P} \mathbf{w} + \mathbf{f}^\top \mathbf{w} + h,
$$

where  $P \in \mathbb{R}^{|\mathcal{W}| \times |\mathcal{W}|}$  is positive semi-definite.

Note that we assume that the inner objective is a convex (but not strongly-convex) quadratic, which may have *many global minima*, providing a tractable setting to analyze implicit bias. This allows us to model an overparameterized linear regression problem with data matrix  $\mathbf{\Phi} \in \mathbb{R}^{n \times p}$ , which corresponds to a quadratic with curvature matrix  $\Phi^{\top}\Phi$ . When the number of parameters is greater than training examples  $(p > n)$ ,  $\mathbf{\Phi}^\top \mathbf{\Phi}$  is PSD. In this case, f has a manifold of valid solutions [\(Wu & Xu,](#page-11-0) [2020\)](#page-11-0).

Statement 3.1 (Cold-start BLO converges to a cold-start equilibrium.). *Suppose* f *and* F *satisfy Assumption 3.1. If the inner parameters are initialized to*  $w_0$  *and learning rates are set appropriately to guarantee convergence, then the cold-start algorithm (Algorithm [1\)](#page-25-0) using exact hypergradients converges to a cold-start equilibrium.*

*Proof.* The proof is provided in Appendix [D.1.](#page-18-0)  $\Box$ 

Implicit Bias of Cold-Start for Inner Solutions. Because cold-start BLO trains the inner parameters *from initializa-* *tion to convergence* for each outer iteration, the inner solution inherits properties from the single-level optimization literature. For example, in linear regression trained with gradient descent, the inner solution will minimize displacement from the initialization  $\|\mathbf{w} - \mathbf{w}_0\|_2^2$ . Recent work aims to generalize such statements to other model classes and optimization algorithms – e.g., obtaining min-norm solutions with algorithm-specific norms [\(Gunasekar et al.,](#page-9-0) [2018\)](#page-9-0). Generalizing the results for various types of neural nets is an active research area [\(Vardi & Shamir,](#page-11-0) [2021\)](#page-11-0).

Implicit Bias of Cold-Start for Outer Solutions. In the following theorem, we show that cold-start BLO with exact hypergradients, from outer initialization  $\mathbf{u}_0$ , converges to the outer solution  $\mathbf{u}^*$  that minimizes displacement from  $\mathbf{u}_0$ .

Theorem 3.1 (Min-Norm Outer Parameters). *Consider cold-start BLO (Algorithm [1\)](#page-25-0) with exact hypergradients starting from outer initialization*  $\mathbf{u}_0$ *. Assume that for each outer iteration, the inner parameters are re-initialized to*  $w_0 = 0$  *and optimized with an appropriate learning rate that ensures convergence. Then cold-start BLO converges to an outer solution*  $\mathbf{u}^*$  *with minimum*  $L_2$  *distance from*  $\mathbf{u}_0$ *:* 

$$
\mathbf{u}^{\star} = \underset{\mathbf{u} \in \arg\min_{\mathbf{u}} F^{\star}(\mathbf{u})}{\arg\min_{\mathbf{u}} F^{\star}(\mathbf{u})} ||\mathbf{u} - \mathbf{u}_0||^2.
$$
 (6)

*Proof.* The proof is provided in Appendix [D.2.](#page-19-0)  $\Box$ 

Next, we observe that the full warm-start and cold-start algorithms are equivalent for strongly-convex inner problems. Remark 3.2 (Equivalence of Full Warm-Start and Cold-Start in the Strongly Convex Regime). *When the inner problem* f(u, w) *is strongly convex in* w *for each* u*, then full warm-start (Alg. [2\)](#page-25-0) and cold-start (Alg. [1\)](#page-25-0) are equivalent.*

*Proof.* The proof is provided in Appendix [D.3](#page-19-0)  $\Box$ 

We relate partial and full warm-start equilibria as follows. Statement 3.2 (Inclusion of Partial Warm-Start Equilibria.). *Every partial warm-start equilibrium is a full warm-start equilibrium. In addition, if*  $\Xi(\mathbf{u}, \mathbf{w}) = \mathbf{w} - \alpha \nabla_{\mathbf{w}} f(\mathbf{u}, \mathbf{w})$ *with a fixed (non-decayed) step size* α*, then full-warm start equilibria are also partial warm-start equilibria.*

*Proof.* The proof is provided in Appendix [D.4.](#page-19-0)  $\Box$ 

#### 3.3.1. IMPLICIT BIAS FROM HYPERGRAD APPROX.

Neumann Series. Next, we investigate the impact of the hypergradient approximation on the converged outer solution. In practice, we use the truncated Neumann series to estimate the inverse Hessian. Note that:

$$
\alpha \sum_{j=0}^{K} (\mathbf{I} - \alpha \mathbf{H})^j \approx (\mathbf{H} + \epsilon \mathbf{I})^{-1}
$$
 (7)

<span id="page-5-0"></span>Image /page/5/Figure/1 description: The image is a plot showing the relationship between the eigenvalue of H and the eigenvalue of H\*. The x-axis represents the eigenvalue of H and is on a logarithmic scale, ranging from 10^-5 to 10^1. The y-axis represents the eigenvalue of H\* and is also on a logarithmic scale, ranging from 10^-1 to 10^5. Three lines are plotted: H^-1 (blue line), (H + εI)^-1 (red line), and Neumann (dashed gray line). The blue line (H^-1) shows a decreasing trend, starting from approximately 10^5 at 10^-5 on the x-axis and decreasing to approximately 10^-1 at 10^1 on the x-axis. The red line ((H + εI)^-1) starts at approximately 10^1 at 10^-5 on the x-axis, stays relatively constant around 10^1 for smaller eigenvalues of H, and then decreases, closely following the blue line for larger eigenvalues of H. The dashed gray line (Neumann) is also plotted, appearing to overlap with the red line for most of the range shown.

Figure 3: Eigenvalues of various matrix functions of H (denoted  $\mathbf{H}^{\star}$  in the diagram):  $\mathbf{H}^{-1}$ ,  $(\mathbf{H} + \epsilon \mathbf{I})^{-1}$ , and the Neumann series approximation  $\alpha \sum_{j=0}^{K} (\mathbf{I} - \alpha \mathbf{H})^j$ . Here,  $\epsilon = \frac{1}{\alpha K}$ .

where  $\epsilon = \frac{1}{\alpha K}$ . This is known from the spectral regularization literature [\(Gerfo et al.,](#page-9-0) [2008;](#page-9-0) [Rosasco,](#page-11-0) [2009\)](#page-11-0). We show in Appendix [E](#page-20-0) that the damped Hessian inverse  $(H+\epsilon I)^{-1}$  corresponds to the hypergradient of a proximallyregularized inner objective:  $\hat{f}(\mathbf{u}, \mathbf{w}) = f(\mathbf{u}, \mathbf{w}) + \frac{\epsilon}{2} ||\mathbf{w} - \mathbf{w}||$  $\mathbf{w}'\|_2^2$ , where  $\mathbf{w}' \in \argmin_{\mathbf{w}} f(\mathbf{u}, \mathbf{w})$ . The damping prevents the inner optimization from moving far in lowcurvature directions. Consider the spectral decomposition of the real symmetric matrix  $H = UDU<sup>T</sup>$ , where D is a diagonal matrix containing the eigenvalues of H, and U is an orthogonal matrix. Then,  $(H + \epsilon I)^{-1} = (UDU^{\top} + \epsilon I)^{-1} =$  $U^{\top}$ (D +  $\epsilon I$ )<sup>-1</sup>U. If  $\lambda$  is an eigenvalue of **H**, then the corresponding eigenvalue of  $H^{-1}$  is  $\frac{1}{\lambda}$ . In contrast, the corresponding eigenvalue of  $(H + \epsilon I)^{-1}$  is  $\frac{1}{\lambda + \epsilon}$ . When  $\epsilon \ll \lambda$ ,  $\frac{1}{\lambda+\epsilon} \approx \frac{1}{\lambda}$ ; when  $\lambda \ll \epsilon$ ,  $\frac{1}{\lambda+\epsilon} \approx \frac{1}{\epsilon}$ . Thus, the influence of small eigenvalues (associated with low-curvature directions) is diminished, and the truncated Neumann series primarily takes into account high-curvature directions. Figure 3 illustrates the relationship between the eigenvalues of  $H^{-1}$ ,  $(\mathbf{H} + \epsilon \mathbf{I})^{-1}$ , and  $\alpha \sum_{j=0}^{K} (\mathbf{I} - \alpha \mathbf{H})^{j}$ .

Unrolling. The implicit bias induced by approximating the hypergradient via  $K$ -step unrolled differentiation is qualitatively similar to the bias induced by the truncated Neumann series. For quadratic inner objectives  $f$ , the truncated Neumann series coincides with the result of differentiating through  $K$  steps of unrolled gradient descent, because the Hessian of a quadratic is constant over the inner optimization trajectory. Note that gradient descent on a quadratic converges (for step-size  $\alpha \leq 1/||\mathbf{H}||_2$ ) more rapidly in highcurvature directions than in low-curvature directions. Thus, the approximate best response obtained by truncated unrolling only takes into account high-curvature directions of the inner objective, and is less sensitive to low-curvature directions. In turn, the response Jacobian will only capture how the inner parameters depend on the outer parameters in these high-curvature directions. Note that for general objectives  $f$  (e.g., when training neural networks), differentiating through unrolling only coincides with the Neumann series when the inner parameters are at a stationary point of  $f$ .

# 4. Empirical Overparameterization Results

Many large-scale empirical studies—in particular in the areas of hyperparameter optimization and data augmentation use warm-start bilevel optimization [\(Hataya et al.,](#page-10-0) [2020b;](#page-10-0) [Ho et al.,](#page-10-0) [2019;](#page-10-0) [Mounsaveng et al.,](#page-11-0) [2021;](#page-11-0) [Peng et al.,](#page-11-0) [2018;](#page-11-0) [Tang et al.,](#page-11-0) [2020\)](#page-11-0). In this section, we introduce simple tasks based on dataset distillation, designed to provide insights into the phenomena at play. First, we show that when the inner problem is overparameterized, the inner parameters w can retain information associated with different settings of the outer parameters over the course of joint optimization. Then, we show that when the outer problem is overparameterized, the choice of hypergradient approximation can affect which outer solutions is found. Experimental details and extended results are provided in Appendix [G.](#page-21-0)

### 4.1. Inner Overparameterization: Dataset Distillation

In dataset distillation, the original dataset is only accessed in the outer objective; thus, one might expect that the lowerdimensional distilled dataset would act as an information bottleneck between the objectives. Because the outer objective is only used directly to update the outer variables, it would seem intuitive that all of the information about the outer objective is compressed into the outer variables. While this is correct for the cold-start equilibrium, we show that it does not hold for the warm-start equilibrium: *a surprisingly large amount of information can leak from the original dataset to the inner variables (e.g., network weights)*.

Consider a 2D binary classification task where the classes form concentric rings (Figure [4\)](#page-6-0). We aim to learn *two distilled datapoints* (one per class) to model the circular decision boundary. One may *a priori* expect that this would not be possible when training a neural network on only the two distilled points; indeed, we observe poor decision boundaries for the original dataset when training a model to convergence on the synthetic datapoints (Figure [4,](#page-6-0) Top Right). However, when performing warm-start alternating updates on the MLP parameters and the learned datapoints, the datapoints follow a nontrivial *trajectory*, tracing out the decision boundary between classes over time (Fig. [4,](#page-6-0) middle three plots). The model trained jointly with the two learned datapoints fits to the full trajectory of those datapoints. Thus, warm-started BLO yields a model that achieves nearly the same outer loss as one trained directly on the original data, despite only using a single datapoint per class. See the caption of Figure [4](#page-6-0) for details on interpreting this result.

Warm-Start Memory. Here we provide intuition for the warm-start behavior observed in Figure [4.](#page-6-0) In particular, we discuss how warm-start BLO induces a *memory effect*.

Figure [5](#page-6-0) illustrates warm- and cold-start algorithms on a toy linear regression example where we can visualize the steps

On Implicit Bias in Overparameterized Bilevel Optimization

<span id="page-6-0"></span>Image /page/6/Figure/1 description: The image displays a comparison of different training stages for a classification task, likely involving a neural network. The top row shows five plots illustrating the data distribution and decision boundaries at different stages. The first plot, 'Original Data', shows red and green data points scattered in a circular pattern. The subsequent plots, 'Warm-Start t=100', 'Warm-Start t=1000', and 'Warm-Start t=7000', show the evolution of the decision boundary (blue line) and learned data points (red and green crosses) as training progresses. The 'Re-Training' plot shows the final state of the decision boundary. The bottom part of the image features a line graph plotting 'Outer Loss' against 'Iteration (t)'. Two lines are shown: a yellow line labeled 'Warm-Start' and a purple line labeled 'Re-Train'. The 'Warm-Start' line shows a steady decrease in loss, while the 'Re-Train' line shows a more volatile but generally decreasing trend, with spikes and dips. The x-axis of the loss plot ranges from 0 to 8000 iterations, and the y-axis is on a logarithmic scale, showing values from 10^-1 to 10^0. A legend at the top indicates that red squares represent 'Classified Red', green squares represent 'Classified Green', black crosses represent 'Learned Datapoints', red lines represent 'Red Trajectory', and green lines represent 'Green Trajectory'.

Figure 4: Dataset distillation for binary classification, with two learned datapoints (outer parameters) adapted jointly with the model weights (inner parameters). Top left: The original data distribution we wish to distill; Top middle three plots: Visualizations of snapshots during training with warm-start BLO, at iterations  $t \in \{100, 1000, 7000\}$  (indicated by the dashed vertical lines in the bottom plot). In each middle figure, we show the *new portion of the synthetic datapoint trajectory* since the previous snapshot, shown by a curve with arrows; Top right: Decision boundary when re-training to convergence on the final values of the two distilled datapoints yields a poor solution for the original data. Bottom: We show the outer loss over the course of a single run of warm-start BLO corresponding to the middle three plots in the top row. We also demonstrate that none of the intermediate synthetic datapoint pairs along the trajectory is sufficient to fit the original data well, by re-training on each intermediate point (shown by the purple curve).

Image /page/6/Figure/3 description: The image displays two plots side-by-side. The left plot, titled 'Data Space (Outer)', is a scatter plot showing four data points labeled u1, u2, u3, and u4. The x-axis ranges from -1 to 3, and the y-axis ranges from -2 to 12. The points are located at approximately (0, 3), (0.5, 5), (1, 7), and (2, 9). The right plot, titled 'Parameter Space (Inner)', shows contour lines representing a probability distribution in a 2D space with axes labeled w1 and w2. Several lines, labeled S(u1), S(u2), S(u3), and S(u4), intersect in this space. Three paths are plotted, representing different optimization strategies: 'Warm (Full)' in red, 'Warm (Online)' in orange, and 'Cold' in dotted cyan. All paths converge towards a central region of the contour plot.

Figure 5: Parameter-space view of warm-start with full inner optimization, warm-start with partial inner optimization (denoted the "online" setting, which most closely resembles what is done in practice), and cold-start optimization.

of each algorithm in the inner parameter space. The solution sets for four *fixed* values of a single synthetic datapoint are shown by the solid black lines in Figure 5, and outer loss contours are shown in the background.

We assume the inner parameters are initialized at the origin. Cold-start projects from the initialization onto the solution set corresponding to the current outer parameter:  $\mathbf{w}_{k+1} = \Pi(\mathbf{0}, \mathcal{S}(\mathbf{u}_k))$ , where  $\Pi(\cdot, \mathcal{C})$  denotes projection onto the (convex) set  $C$ . In contrast, full warm-start projects the previous inner parameters onto the current solution set:  $\mathbf{w}_{k+1} = \Pi(\mathbf{w}_k, \mathcal{S}(\mathbf{u}_k))$ . If we cycle through the solution sets repeatedly, full warm-start BLO is equivalent to the Kaczmarz algorithm [\(Karczmarz,](#page-10-0) [1937\)](#page-10-0), a classic alternating projection algorithm for finding a point in the intersection of the constraint sets (see App. [C.2](#page-16-0) for details). In this case, the inner parameters will converge to the intersection of the solution sets  $\{S(u_i)\}_{i=1}^4$ , in effect yielding inner parameters that perform well for several outer parameters simultaneously. In the case of dataset distillation, this corresponds to model weights which fit all of the distilled datapoints over the outer optimization trajectory.

Warm-Start vs Cold-Start in High-Dimensions. To illustrate the warm-start phenomena in high-dimensional problems, we ran a dataset distillation task on MNIST using a linear classifier. Here, the BLO methods are provided with a single  $28 \times 28$  image "canvas" whose pixels are learned together with a 10-dimensional vector of soft labels. Figure [6a](#page-7-0) shows accuracies when optimizing a *single synthetic datapoint and its soft label* on 10000 MNIST images. We visualize the soft label evolution in Figures [6b](#page-7-0) and [6c,](#page-7-0) showing classes 0-9 as colored regions. While the cold-start soft label quickly converges, warm-start continues to adapt the soft label over the course of joint optimization, effectively training the inner model on all 10 classes (e.g., by placing more weight on different classes at different points in time). We obtained similar results on MNIST, FashionMNIST, and CIFAR-10, with 1 or 10 synthetic datapoints (see Table [3\)](#page-7-0). In addition to dataset distillation, we observe similar phenomena in hyperparameter optimization. We trained a linear data augmentation (DA) network that transforms inputs before feeding them into a classifier; here, the DA net parameters are hyperparameters u, tuned on the validation set. We used subsampled training sets consisting of 50 datapoints—so that data augmentation is beneficial—and evaluated performance on the full validation set. Table [3](#page-7-0) compares warm- and cold-start BLO on this task. Note that in order to compare with cold-start solutions (which need 10<sup>3</sup>–10<sup>4</sup> inner optimization steps) we *require* tractable inner problems like linear models or MLPs.

On Implicit Bias in Overparameterized Bilevel Optimization

<span id="page-7-0"></span>Image /page/7/Figure/1 description: The image displays three plots related to dataset distillation. Plot (a) shows the accuracy on the original dataset over outer iterations for three methods: Cold-Start (blue line), Warm-Start (orange line), and Warm-Start w/ Re-Training (green line). The accuracy for Cold-Start stabilizes around 0.3, Warm-Start reaches approximately 0.85, and Warm-Start w/ Re-Training fluctuates between 0.1 and 0.25. Plots (b) and (c) show the label distribution over outer iterations for Cold-start soft labels and Warm-start soft labels, respectively. These are stacked area charts with multiple colored regions representing different label distributions, showing how these distributions evolve over time.

Figure 6: Dataset distillation using a linear classifier on MNIST.

| <b>Method</b>                          | <b>Dataset Distillation</b> |             | <b>Data Aug. Net</b> |          |
|----------------------------------------|-----------------------------|-------------|----------------------|----------|
|                                        | MNIST                       | CIFAR-10    | MNIST                | CIFAR-10 |
| <b>Cold-Start</b>                      | 30.7 / 89.1                 | 17.6 / 46.9 | 84.86                | 45.38    |
| <b>Warm-Start</b>                      | 90.8 / 97.5                 | 50.3 / 59.8 | 92.81                | 59.30    |
| <b>Warm-Start</b> +<br><b>Re-Train</b> | 12.9 / 17.1                 | 10.2 / 8.9  | 9.15                 | 11.12    |

Table 3: Cols 1-3: Accuracy on original data with 1 or 10 synthetic samples. Cols 4-6: Learning a data augmentation network. Extended table in App. [G.](#page-21-0)

Warm-Start Takeaways. Warm-start BLO yields outer parameters that *fail to generalize* under re-initialization of the inner problem. In both toy and high-dimensional problems, re-training with the final outer parameters (e.g., discarding the outer optimization trajectory) yields a model that performs poorly on the outer objective. In addition, warmstart BLO *leaks information* about the outer objective to the inner parameters, which can lead to overestimation of performance. For example, when adapting a small number of hyperparameters online, warm-start BLO may overfit the validation set, yielding a model that fails to generalize to the test set.

#### 4.2. Outer Overparameterization: Anti-Distillation

Our previous discussion focused on implicit bias resulting from the choice of cold- or warm-start BLO. Next, we show that when the outer problem is overparameterized, the hypergradient approximation we use can lead to different outer solutions. We propose a task related to dataset distillation, but where we have *more* learned datapoints than original dataset examples, which we term *anti-distillation* (Figure [7\)](#page-8-0). Here, there are many valid ways to set the learned datapoints such that a model trained on those points achieves good performance on the original data.

Anti-Distillation. Consider the linear regression problem  $f(\mathbf{u}, \mathbf{w}) = \frac{1}{2} || \mathbf{\Phi} \mathbf{w} - \mathbf{u} ||_2^2$  where, for simplicity, **u** parameterizes only the targets, not the inputs. The minimum Frobenius norm best-response Jacobian is the Moore-Penrose pseudoinverse of the feature matrix,  $\Phi^+$  (see Appendix [C.1\)](#page-15-0), which we will approximate in different

ways. For this problem, we have one original datapoint, and we learn 13 synthetic datapoints (Figure [7\)](#page-8-0). Any solution that places one learned datapoint on top of the original point perfectly fits the outer objective. We use Fourier-basis regression with the feature function  $\phi(x)$  =  $a_0 + \sum_{n=1}^{N} (a_n 2^{N-n} \cos(nx) + b_n 2^{N-n} \sin(nx))$ , where low frequency terms have larger magnitude than high frequency terms—this yields an inductive bias for smooth functions. Because of this difference in magnitude, the inner optimizer can more easily (i.e., with a smaller-magnitude weight update) fit the data by adjusting the regression coefficients on the low-frequency terms. Thus, the minimumnorm solution found by gradient descent will explain as much as possible using the low frequency terms.

When we estimate the best-response Jacobian with a few of steps of unrolling, the inner optimizer will do its best to fit the data using the low-frequency basis terms, which correspond to high-curvature directions of the inner loss.

In Figure [7a,](#page-8-0) we show the solutions obtained for different truncations of the Neumann series; because this problem is quadratic, the  $K$ -step unrolled hypergradient coincides with the K-step Neumann series hypergradient. To demonstrate the relationship  $\alpha \sum_{j=0}^{K} (\mathbf{I} - \alpha \mathbf{H})^j \approx (\mathbf{H} + \frac{1}{\alpha K} \mathbf{I})^{-1}$  empirically, we also show the distilled datasets obtained with the proximal best-response Jacobian, for various damping factors  $\epsilon \in \{1e-5, 1e-6, 1e-7, 1e-8\}$ , in Figure [10.](#page-23-0) We observe similar behavior to the Neumann series. In Figure [7b](#page-8-0) we plot the norms of the converged inner and outer parameters for a range of Neumann truncation lengths  $K$  and damping factors  $\epsilon$ . We see that while the Neumann and proximal approximate hypergradients behave similarly, they are not equivalent. In Appendix [G,](#page-21-0) Figure [13,](#page-24-0) we show similar behavior when using an MLP rather than a linear model.

Figure [7c](#page-8-0) visualizes optimization trajectories in the outer parameter space to provide additional intuition for the behavior of the outer parameter norms in Figure [7b.](#page-8-0) We consider antidistillation with 1 original and 2 learned datapoints. We use  $\hat{\nabla}_{\mathbf{u}}^K F(\mathbf{u}, \mathbf{w})$  to denote the approximate hypergradient obtained via implicit differentiation with the  $K$ -term truncated Neumann series. We show the true hypergradi-

<span id="page-8-0"></span>Image /page/8/Figure/1 description: The image displays three plots related to optimization algorithms. Plot (a) is a line graph titled "Neumann/unrolling" showing the relationship between 'x' and 'y'. It features several colored lines with markers representing different values of 'K' (1, 10, 100, 1000) and a "Pseudoinverse" condition, along with an "Orig Point" indicated by a green dot. Plot (b) is a dual-axis graph titled "Parameter norms" plotting two metrics, "||w||^2" (in magenta) and "||u||^2" (in green), against 'ε' on a logarithmic scale. Both metrics are shown for "Neumann" (solid lines) and "Proximal" (dashed lines) methods. Plot (c) is a heatmap titled "Approximate hypergradients" with axes labeled 'u1' and 'u2'. It illustrates the optimization path with vectors representing gradients and intermediate points like "u\_init", "∇\_u F", "∇\_u^K1 F", and estimated solutions "û\_K1^\*", "û\_K100^\*", "û\_K200^\*", and "u\_exact^\*" marked with stars of different colors. The plot also indicates the final solution as "argmin\_u F\*(u)".

Figure 7: Antidistillation task for linear regression with an overparametrized outer objective. We learn the y-component of 13 synthetic datapoints such that a regressor trained on those points will fit a single original dataset point, shown by the green dot at  $(0, 2)$ . Fig. (a) shows the learned datapoints (outer parameters) obtained via different truncated Neumann approximations to the hypergradient. Fig. (b) shows the norms of the outer parameters  $||u||^2$  as a function of K (for Neumann/unrolling) or  $\epsilon$  (for proximal). We observe that better hypergradient approximations (e.g., larger K or smaller  $\epsilon$ ) lead to smaller norm outer parameters, because they account for both high- and low-curvature directions of the inner objective. Fig. (c) visualizes outer optimization trajectories in the outer parameter space, to provide intuition for the behavior in (a) and (b). We consider antidistillation with 1 original datapoint and 2 learned datapoints. We show the true hypergradient  $\nabla_{\mathbf{u}}F$ , approximations using truncated Neumann series  $\hat{\nabla}_{\mathbf{u}}F$ , and the converged outer parameters for each setting, e.g.,  $\hat{\mathbf{u}}_{\star}^{K1}$ . We observe that: 1) when the outer problem is overparameterized, approximate hypergradients converge to valid solutions to the outer objective; and 2) the exact hypergradient converges to the min-norm solution to the outer problem.

ent  $\nabla_{\mathbf{u}} F$ , approximations using truncated Neumann series  $\hat{\nabla}_{\mathbf{u}} F$ , and the converged outer parameters for each setting, e.g.,  $\hat{\mathbf{u}}_{\star}^{K1}$ . We observe that: 1) when the outer problem is overparameterized, approximate hypergradients converge to valid solutions in  $\arg \min_{\mathbf{u}} F^*(\mathbf{u})$ ; and 2) the exact hypergradient converges to the min-norm outer solution.

### 5. Related Work

Extended related work is provided in Appendix [B.](#page-15-0)

Overparameterization. Overparameterization has long been studied in single-level optimization, generating key insights such as neural network behavior in the infinitewidth limit [\(Jacot et al.,](#page-10-0) [2018;](#page-10-0) [Sohl-Dickstein et al.,](#page-11-0) [2020;](#page-11-0) [Lee et al.,](#page-10-0) [2019\)](#page-10-0), double descent phenomena [\(Nakkiran](#page-11-0) [et al.,](#page-11-0) [2020;](#page-11-0) [Belkin et al.,](#page-9-0) [2019\)](#page-9-0), the ability to fit random labels [\(Zhang et al.,](#page-12-0) [2016\)](#page-12-0), and the inductive biases of optimizers. However, prior work has not investigated implicit bias in bilevel optimization. Implicit bias has a long history in machine learning: many works have observed and studied the connection between early stopping and  $L_2$  bias [\(Strand,](#page-11-0) [1974;](#page-11-0) [Morgan & Bourlard,](#page-11-0) [1989;](#page-11-0) [Friedman & Popescu,](#page-9-0) [2003;](#page-9-0) [Yao et al.,](#page-12-0) [2007\)](#page-12-0). Interest in implicit bias has increased over the past few years [\(Nacson et al.,](#page-11-0) [2019;](#page-11-0) [Soudry](#page-11-0) [et al.,](#page-11-0) [2018;](#page-11-0) [Suggala et al.,](#page-11-0) [2018;](#page-11-0) [Poggio et al.,](#page-11-0) [2019;](#page-11-0) [Ji &](#page-10-0) [Telgarsky,](#page-10-0) [2019;](#page-10-0) [Ali et al.,](#page-9-0) [2019;](#page-9-0) [2020\)](#page-9-0).

Prior Work using Warm Start. Many papers perform joint optimization of the inner and outer parameters (e.g., data augmentations together with a base model), such as [Hataya](#page-10-0) [et al.](#page-10-0) [\(2020a;b\)](#page-10-0); [Ho et al.](#page-10-0) [\(2019\)](#page-10-0); [Mounsaveng et al.](#page-11-0) [\(2021\)](#page-11-0); [Peng et al.](#page-11-0) [\(2018\)](#page-11-0); [Tang et al.](#page-11-0) [\(2020\)](#page-11-0). [Ghadimi & Wang](#page-9-0) [\(2018\)](#page-9-0), [Hong et al.](#page-10-0) [\(2020\)](#page-10-0), and [Ji et al.](#page-10-0) [\(2020\)](#page-10-0) propose bilevel optimization algorithms that use warm-starting; however, they focus on analyzing the convergence rates of their algorithms and do not consider inner underspecification.

Gap Between Theory & Practice. Existing BLO theory typically assumes unique solutions to the inner (and sometimes outer) problem, and focuses on showing that approximation methods get (provably) close to the solution. [Shaban](#page-11-0) [et al.](#page-11-0) [\(2019\)](#page-11-0) provide conditions where optimization with an approximate hypergradient h from truncated unrolling converges to a BLO solution, but they assume uniqueness of the inner solution. [Grazzi et al.](#page-9-0) [\(2020a;b\)](#page-9-0) study iteration complexity and convergence of hypergradient approximations in the strongly-convex inner problem setting. Several other works focus on convergence rate analyses [\(Ji et al.,](#page-10-0) [2021;](#page-10-0) [Yang et al.,](#page-12-0) [2021;](#page-12-0) [Ji & Liang,](#page-10-0) [2021\)](#page-10-0).

### 6. Conclusion

Most work on bilevel optimization has made the simplifying assumption that the solutions to the inner and outer problems are unique. However, this does not hold in many practical applications, such as hyperparameter optimization for overparameterized neural networks. We investigated overparameterized bilevel optimization, where either the inner or outer problems may admit non-unique solutions. We formalized warm- and cold-start equilibria, which correspond to common BLO algorithms. We analyzed the properties of these equilibria, and algorithmic choices such as the number of Neumann series terms used to approximate the hypergradient. We presented several tasks illustrating that these choices can significantly affect the solutions obtained in practice. More generally, we highlighted the importance of, and laid groundwork for, analyzing the effects of overparameterization in nested optimization problems.

<span id="page-9-0"></span>

### References

- Abadi, M., Agarwal, A., Barham, P., Brevdo, E., Chen, Z., Citro, C., Corrado, G. S., Davis, A., Dean, J., Devin, M., Ghemawat, S., Goodfellow, I., Harp, A., Irving, G., Isard, M., Jia, Y., Jozefowicz, R., Kaiser, L., Kudlur, M., Levenberg, J., Mané, D., Monga, R., Moore, S., Murray, D., Olah, C., Schuster, M., Shlens, J., Steiner, B., Sutskever, I., Talwar, K., Tucker, P., Vanhoucke, V., Vasudevan, V., Viégas, F., Vinyals, O., Warden, P., Wattenberg, M., Wicke, M., Yu, Y., and Zheng, X. Tensor-Flow: Large-scale machine learning on heterogeneous systems, 2015. URL <https://www.tensorflow.org/>. Software available from tensorflow.org.
- Ali, A., Kolter, J. Z., and Tibshirani, R. J. A continuous-time view of early stopping for least squares regression. In *International Conference on Artificial Intelligence and Statistics (AISTATS)*, 2019.
- Ali, A., Dobriban, E., and Tibshirani, R. The implicit regularization of stochastic gradient flow for least squares. In *International Conference on Machine Learning (ICML)*, pp. 233–244, 2020.
- Amari, S.-i., Ba, J., Grosse, R., Li, X., Nitanda, A., Suzuki, T., Wu, D., and Xu, J. When does preconditioning help or hurt generalization? *arXiv preprint arXiv:2006.10732*, 2020.
- Angelos, J., Grossman, G., Kaufman, E., Lenker, T., and Rakesh, L. Limit cycles for successive projections onto hyperplanes in RN. *Linear Algebra and its Applications*, 285, 1998.
- Arora, S., Du, S., Hu, W., Li, Z., and Wang, R. Fine-grained analysis of optimization and generalization for overparameterized two-layer neural networks. In *International Conference on Machine Learning (ICML)*, 2019.
- Bae, J. and Grosse, R. Delta-STN: Efficient bilevel optimization for neural networks using structured response Jacobians. *Advances in Neural Information Processing Systems (NeurIPS)*, 2020.
- Bartlett, P. L., Long, P. M., Lugosi, G., and Tsigler, A. Benign overfitting in linear regression. *Proceedings of the National Academy of Sciences*, 2020.
- Belkin, M. Fit without fear: remarkable mathematical phenomena of deep learning through the prism of interpolation. *Acta Numerica*, 30:203–248, 2021.
- Belkin, M., Hsu, D., Ma, S., and Mandal, S. Reconciling modern machine-learning practice and the classical bias–variance tradeoff. *Proceedings of the National Academy of Sciences*, pp. 15849–15854, 2019.
- Bengio, Y. Gradient-based optimization of hyperparameters. *Neural Computation*, 12(8):1889–1900, 2000.
- Bengio, Y., Louradour, J., Collobert, R., and Weston, J. Curriculum learning. In *International Conference on Machine Learning (ICML)*, pp. 41–48, 2009.
- Blondel, M., Berthet, Q., Cuturi, M., Frostig, R., Hoyer, S., Llinares-López, F., Pedregosa, F., and Vert, J.-P. Efficient and modular implicit differentiation. *arXiv preprint arXiv:2105.15183*, 2021.
- Bradbury, J., Frostig, R., Hawkins, P., Johnson, M. J., Leary, C., Maclaurin, D., Necula, G., Paszke, A., VanderPlas, J., Wanderman-Milne, S., and Zhang, Q. JAX: Composable transformations of Python+NumPy programs, 2018. URL <http://github.com/google/jax>.

- Chen, D. and Hagan, M. T. Optimal use of regularization and cross-validation in neural network modeling. In *International Joint Conference on Neural Networks (IJCNN)*, volume 2, pp. 1275–1280, 1999.
- Cheung, T.-H. and Yeung, D.-Y. MODALS: Modality-agnostic automated data augmentation in the latent space. In *International Conference on Learning Representations (ICLR)*, 2021.
- Dempe, S. *Foundations of bilevel programming*. Springer Science & Business Media, 2002.
- Dempe, S., Dutta, J., and Mordukhovich, B. New necessary optimality conditions in optimistic bilevel programming. *Optimization*, 56, 2007.
- Dempe, S., Mordukhovich, B. S., and Zemkoho, A. B. Necessary optimality conditions in pessimistic bilevel programming. *Optimization*, 2014.
- Domke, J. Generic methods for optimization-based modeling. In *Artificial Intelligence and Statistics*, pp. 318–326, 2012.
- Finn, C., Abbeel, P., and Levine, S. Model-agnostic meta-learning for fast adaptation of deep networks. In *International Conference on Machine Learning (ICML)*, pp. 1126–1135, 2017.
- Foo, C.-s., Do, C. B., and Ng, A. Y. Efficient multiple hyperparameter learning for log-linear models. In *Advances in Neural Information Processing Systems (NeurIPS)*, pp. 377–384, 2008.
- Franceschi, L., Frasconi, P., Salzo, S., Grazzi, R., and Pontil, M. Bilevel programming for hyperparameter optimization and meta-learning. *arXiv preprint arXiv:1806.04910*, 2018.
- Friedman, J. and Popescu, B. E. Gradient directed regularization for linear regression and classification. Technical report, Statistics Department, Stanford University, 2003.
- Gerfo, L. L., Rosasco, L., Odone, F., Vito, E. D., and Verri, A. Spectral algorithms for supervised learning. *Neural Computation*, 20(7):1873–1897, 2008.
- Ghadimi, S. and Wang, M. Approximation methods for bilevel programming. *arXiv preprint arXiv:1802.02246*, 2018.
- Goodfellow, I. J., Pouget-Abadie, J., Mirza, M., Xu, B., Warde-Farley, D., Ozair, S., Courville, A., and Bengio, Y. Generative adversarial networks. In *Advances in Neural Information Processing Systems (NeurIPS)*, 2014.
- Grazzi, R., Franceschi, L., Pontil, M., and Salzo, S. On the iteration complexity of hypergradient computation. In *International Conference on Machine Learning (ICML)*, pp. 3748–3758, 2020a.
- Grazzi, R., Pontil, M., and Salzo, S. Convergence properties of stochastic hypergradients. *arXiv preprint arXiv:2011.07122*, 2020b.
- Gunasekar, S., Lee, J., Soudry, D., and Srebro, N. Characterizing implicit bias in terms of optimization geometry. In *International Conference on Machine Learning (ICML)*, pp. 1832– 1841, 2018.
- Ha, D., Dai, A., and Le, Q. V. Hypernetworks. In *International Conference on Learning Representations (ICLR)*, 2017.

- <span id="page-10-0"></span>Harker, P. T. and Pang, J.-S. Existence of optimal solutions to mathematical programs with equilibrium constraints. *Operations Research Letters*, 7(2):61–64, 1988.
- Hataya, R., Zdenek, J., Yoshizoe, K., and Nakayama, H. Faster Autoaugment: Learning augmentation strategies using backpropagation. In *European Conference on Computer Vision (ECCV)*, pp. 1–16, 2020a.
- Hataya, R., Zdenek, J., Yoshizoe, K., and Nakayama, H. Meta approach to data augmentation optimization. *arXiv preprint arXiv:2006.07965*, 2020b.
- Ho, D., Liang, E., Chen, X., Stoica, I., and Abbeel, P. Population based augmentation: Efficient learning of augmentation policy schedules. In *International Conference on Machine Learning (ICML)*, pp. 2731–2741, 2019.
- Hong, M., Wai, H.-T., Wang, Z., and Yang, Z. A two-timescale framework for bilevel optimization: Complexity analysis and application to actor-critic. *arXiv preprint arXiv:2007.05170*, 2020.
- Jacot, A., Gabriel, F., and Hongler, C. Neural tangent kernel: Convergence and generalization in neural networks. In *Advances in Neural Information Processing Systems (NeurIPS)*, 2018.
- Jaderberg, M., Dalibard, V., Osindero, S., Czarnecki, W. M., Donahue, J., Razavi, A., Vinyals, O., Green, T., Dunning, I., Simonyan, K., et al. Population based training of neural networks. *arXiv preprint arXiv:1711.09846*, 2017.
- Ji, K. and Liang, Y. Lower bounds and accelerated algorithms for bilevel optimization. *arXiv preprint arXiv:2102.03926*, 2021.
- Ji, K., Yang, J., and Liang, Y. Bilevel optimization: Nonasymptotic analysis and faster algorithms. *arXiv preprint arXiv:2010.07962*, 2020.
- Ji, K., Yang, J., and Liang, Y. Bilevel optimization: Convergence analysis and enhanced design. In *International Conference on Machine Learning (ICML)*, pp. 4882–4892, 2021.
- Ji, Z. and Telgarsky, M. The implicit bias of gradient descent on nonseparable data. In *Conference on Learning Theory*, pp. 1772–1798, 2019.
- Karczmarz, S. Angenaherte Auflosung von Systemen linearer Gleichungen. *Bull. Int. Acad. Pol. Sic. Let., Cl. Sci. Math. Nat.*, pp. 355–357, 1937.
- Koh, P. W. and Liang, P. Understanding black-box predictions via influence functions. In *International Conference on Machine Learning (ICML)*, pp. 1885–1894, 2017.
- Larsen, J., Hansen, L. K., Svarer, C., and Ohlsson, M. Design and regularization of neural networks: The optimal use of a validation set. In *IEEE Signal Processing Society Workshop*, pp. 62–71, 1996.
- Lee, J., Xiao, L., Schoenholz, S. S., Bahri, Y., Novak, R., Sohl-Dickstein, J., and Pennington, J. Wide neural networks of any depth evolve as linear models under gradient descent. In *Advances in Neural Information Processing Systems (NeurIPS)*, 2019.

- Liao, R., Xiong, Y., Fetaya, E., Zhang, L., Yoon, K., Pitkow, X., Urtasun, R., and Zemel, R. Reviving and improving recurrent back-propagation. In *International Conference on Machine Learning (ICML)*, 2018.
- Lignola, M. B. and Morgan, J. Topological existence and stability for Stackelberg problems. *Journal of Optimization Theory and Applications*, 84(1):145–169, 1995.
- Lignola, M. B. and Morgan, J. Existence of solutions to bilevel variational problems in Banach spaces. In *Equilibrium Problems: Nonsmooth Optimization and Variational Inequality Models*, pp. 161–174. Springer, 2001.
- Liu, H., Simonyan, K., and Yang, Y. DARTS: Differentiable architecture search. In *International Conference on Learning Representations (ICLR)*, 2019.
- Liu, J., Fan, Y., Chen, Z., and Zheng, Y. Pessimistic bilevel optimization: A survey. *International Journal of Computational Intelligence Systems*, 11(1):725–736, 2018.
- Liu, J., Fan, Y., Chen, Z., and Zheng, Y. Methods for pessimistic bilevel optimization. In *Bilevel Optimization*, pp. 403–420. Springer, 2020.
- Loridan, P. and Morgan, J. Weak via strong Stackelberg problem: New results. *Journal of Global Optimization*, 1996.
- Lorraine, J. and Duvenaud, D. Stochastic hyperparameter optimization through hypernetworks. In *NIPS Meta-Learning Workshop*, 2017.
- Lorraine, J., Vicol, P., and Duvenaud, D. Optimizing millions of hyperparameters by implicit differentiation. In *International Conference on Artificial Intelligence and Statistics (AISTATS)*, pp. 1540–1552, 2020.
- Lucchetti, R., Mignanego, F., and Pieri, G. Existence theorems of equilibrium points in Stackelberg games with constraints. *Optimization*, 1987.
- Luketina, J., Berglund, M., Greff, K., and Raiko, T. Scalable gradient-based tuning of continuous regularization hyperparameters. In *International Conference on Machine Learning (ICML)*, pp. 2952–2960, 2016.
- MacKay, M., Vicol, P., Lorraine, J., Duvenaud, D., and Grosse, R. Self-Tuning Networks: Bilevel optimization of hyperparameters using structured best-response functions. In *International Conference on Learning Representations (ICLR)*, 2019.
- Maclaurin, D., Duvenaud, D., and Adams, R. Gradient-based hyperparameter optimization through reversible learning. In *International Conference on Machine Learning (ICML)*, pp. 2113–2122, 2015.
- Mehra, A. and Hamm, J. Penalty method for inversion-free deep bilevel optimization. *arXiv preprint arXiv:1911.03432*, 2019.
- Metz, L., Maheswaranathan, N., Nixon, J., Freeman, D., and Sohl-Dickstein, J. Understanding and correcting pathologies in the training of learned optimizers. In *International Conference on Machine Learning (ICML)*, pp. 4556–4565, 2019.
- Micaelli, P. and Storkey, A. Non-greedy gradient-based hyperparameter optimization over long horizons. *arXiv preprint arXiv:2007.07869*, 2020.

- <span id="page-11-0"></span>Mishachev, N. and Shmyrin, A. M. On realization of limit polygons in sequential projection method. *International Transaction Journal of Engineering, Management and Applied Sciences and Technologies*, 10(15):1015–1015, 2019.
- Morgan, N. and Bourlard, H. Generalization and parameter estimation in feedforward nets: Some experiments. In *Advances in Neural Information Processing Systems (NeurIPS)*, volume 2, pp. 630–637, 1989.
- Mounsaveng, S., Laradji, I., Ben Ayed, I., Vazquez, D., and Pedersoli, M. Learning data augmentation with online bilevel optimization for image classification. In *Winter Conference on Applications of Computer Vision*, pp. 1691–1700, 2021.
- Nacson, M. S., Srebro, N., and Soudry, D. Stochastic gradient descent on separable data: Exact convergence with a fixed learning rate. In *International Conference on Artificial Intelligence and Statistics (AISTATS)*, pp. 3051–3059, 2019.
- Nakkiran, P., Kaplun, G., Bansal, Y., Yang, T., Barak, B., and Sutskever, I. Deep double descent: Where bigger models and more data hurt. In *International Conference on Learning Representations (ICLR)*, 2020.
- Outrata, J. V. Necessary optimality conditions for Stackelberg problems. *Journal of Optimization Theory and Applications*, 76 (2):305–320, 1993.
- Paszke, A., Gross, S., Chintala, S., Chanan, G., Yang, E., DeVito, Z., Lin, Z., Desmaison, A., Antiga, L., and Lerer, A. Automatic differentiation in PyTorch. In *NIPS Workshop Autodifferentiation*, 2017.
- Pearlmutter, B. A. Fast exact multiplication by the Hessian. *Neural Computation*, 6(1):147–160, 1994.
- Pedregosa, F. Hyperparameter optimization with approximate gradient. In *International Conference on Machine Learning (ICML)*, 2016.
- Peng, X., Tang, Z., Yang, F., Feris, R. S., and Metaxas, D. Jointly optimize data augmentation and network training: Adversarial data augmentation in human pose estimation. In *Conference on Computer Vision and Pattern Recognition (CVPR)*, pp. 2226– 2234, 2018.
- Pfau, D. and Vinyals, O. Connecting generative adversarial networks and actor-critic methods. In *NeurIPS Workshop on Adversarial Training*, 2016.
- Poggio, T., Banburski, A., and Liao, Q. Theoretical issues in deep networks: Approximation, optimization and generalization. In *Proceedings of the National Academy of Sciences*, 2019.
- Raghu, A., Raghu, M., Kornblith, S., Duvenaud, D., and Hinton, G. Teaching with commentaries. In *International Conference on Learning Representations (ICLR)*, 2020.
- Rajeswaran, A., Finn, C., Kakade, S., and Levine, S. Meta-learning with implicit gradients. In *Advances in Neural Information Processing Systems (NeurIPS)*, 2019.
- Ren, M., Zeng, W., Yang, B., and Urtasun, R. Learning to reweight examples for robust deep learning. In *International Conference on Machine Learning (ICML)*, pp. 4334–4343, 2018.

- Riba, E., Mishkin, D., Ponsa, D., Rublee, E., and Bradski, G. Kornia: An open source differentiable computer vision library for PyTorch. In *Winter Conference on Applications of Computer Vision*, 2020. URL [https://arxiv.org/pdf/](https://arxiv.org/pdf/1910.02190.pdf) [1910.02190.pdf](https://arxiv.org/pdf/1910.02190.pdf).
- Rosasco, L. 9.520: Statistical Learning Theory and Applications, Lecture 7. Lecture Notes, 2009. URL [https://www.mit.edu/~9.520/spring09/](https://www.mit.edu/~9.520/spring09/Classes/class07_spectral.pdf) [Classes/class07\\_spectral.pdf](https://www.mit.edu/~9.520/spring09/Classes/class07_spectral.pdf).
- Shaban, A., Cheng, C.-A., Hatch, N., and Boots, B. Truncated back-propagation for bilevel optimization. In *International Conference on Artificial Intelligence and Statistics (AISTATS)*, pp. 1723–1732, 2019.
- Sinha, A., Malo, P., and Deb, K. A review on bilevel optimization: From classical to evolutionary approaches and applications. *IEEE Transactions on Evolutionary Computation*, 22(2):276– 295, 2017.
- Sohl-Dickstein, J., Novak, R., Schoenholz, S. S., and Lee, J. On the infinite width limit of neural networks with a standard parameterization. *arXiv preprint arXiv:2001.07301*, 2020.
- Soudry, D., Hoffer, E., Nacson, M. S., Gunasekar, S., and Srebro, N. The implicit bias of gradient descent on separable data. *The Journal of Machine Learning Research*, 19(1):2822–2878, 2018.
- Stošic, M., Xavier, J., and Dodig, M. Projection on the intersection ´ of convex sets. *Linear Algebra and its Applications*, 509:191– 205, 2016.
- Strand, O. N. Theory and methods related to the singular-function expansion and Landweber's iteration for integral equations of the first kind. *SIAM Journal on Numerical Analysis*, 11(4): 798–825, 1974.
- Sucholutsky, I. and Schonlau, M. 'Less than one'-shot learning: Learning N classes from M< N samples. *arXiv preprint arXiv:2009.08449*, 2020.
- Suggala, A., Prasad, A., and Ravikumar, P. K. Connecting optimization and regularization paths. In *Advances in Neural Information Processing Systems (NeurIPS)*, volume 31, pp. 10608– 10619, 2018.
- Tang, Z., Gao, Y., Karlinsky, L., Sattigeri, P., Feris, R., and Metaxas, D. OnlineAugment: Online data augmentation with less domain knowledge. In *European Conference on Computer Vision (ECCV)*, 2020.
- Vardi, G. and Shamir, O. Implicit regularization in ReLU networks with the square loss. In *Conference on Learning Theory*, pp. 4224–4258, 2021.
- Wang, T., Zhu, J.-Y., Torralba, A., and Efros, A. A. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- Wiesemann, W., Tsoukalas, A., Kleniati, P.-M., and Rustem, B. Pessimistic bilevel optimization. *SIAM Journal on Optimization*, 23(1):353–380, 2013.
- Wu, D. and Xu, J. On the optimal weighted  $\ell_2$  regularization in overparameterized linear regression. *arXiv preprint arXiv:2006.05800*, 2020.

- <span id="page-12-0"></span>Wu, Y., Ren, M., Liao, R., and Grosse, R. Understanding shorthorizon bias in stochastic meta-optimization. In *International Conference on Learning Representations (ICLR)*, 2018.
- Yang, J., Ji, K., and Liang, Y. Provably faster algorithms for bilevel optimization. *arXiv preprint arXiv:2106.04692*, 2021.
- Yao, Y., Rosasco, L., and Caponnetto, A. On early stopping in gradient descent learning. *Constructive Approximation*, 26(2): 289–315, 2007.
- Zhang, C., Bengio, S., Hardt, M., Recht, B., and Vinyals, O. Understanding deep learning requires rethinking generalization. In *International Conference on Learning Representations (ICLR)*, 2016.
- Zhao, B., Mopuri, K. R., and Bilen, H. Dataset condensation with gradient matching. In *International Conference on Learning Representations (ICLR)*, 2021.
- Zoph, B. and Le, Q. V. Neural architecture search with reinforcement learning. In *International Conference on Learning Representations (ICLR)*, 2017.

## Acknowledgements

We thank James Lucas, Guodong Zhang, and David Acuna for helpful feedback on the manuscript. Resources used in this research were provided, in part, by the Province of Ontario, the Government of Canada through CIFAR, and companies sponsoring the Vector Institute (<www.vectorinstitute.ai/partners>).

# Appendix

This appendix is structured as follows:

- In Section [A,](#page-14-0) we provide an overview of the notation we use throughout the paper.
- In Section [B,](#page-15-0) we provide an extended discussion of background and related work.
- In Section [C,](#page-15-0) we provide derivations of formulas used in the main body.
- In Section [D,](#page-17-0) we provide proofs of the theorems in the main paper.
- In Section [E,](#page-20-0) we derive the response Jacobian for a proximal inner objective, recovering a form of the IFT using the damped Hessian inverse.
- In Section [F,](#page-20-0) we provide an overview of the result from [Lorraine et al.](#page-10-0) [\(2020\)](#page-10-0) that shows that differentiating through  $i$ steps of unrolling starting from optimal inner parameters  $w^*$  is equivalent to approximating the inverse Hessian with the first  $i$  terms of the Neumann series.
- In Section [G,](#page-21-0) we provide experimental details and extended results, as well as a [link to an animation](https://docs.google.com/document/d/1whS9xmU_gfwsqtpUchf3ltlXSxcA_8iPoMwAfNqXNMs/edit?usp=sharing) of the bilevel training dynamics for the dataset distillation task from Section [4.1.](#page-5-0)
- In Section [H,](#page-24-0) we describe algorithms for cold-start and warm-start bilevel optimization.

<span id="page-14-0"></span>

# A. Notation

| <b>BLO</b>                                              | Bilevel optimization                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
|---------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| $F$                                                     | Outer objective                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| $oldsymbol{f}$                                         | Inner objective                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| $f u$                                                  | Outer variables                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| $f w$                                                  | Inner variables                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |
| $oldsymbol{\mathcal U}$                                | Outer parameter space                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| $oldsymbol{\mathcal W}$                                | Inner parameter space                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| $oldsymbol{\Rightarrow}$                               | Multi-valued mapping between two sets                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| $oldsymbol{\mathcal{S}(\mathbf{u})}$                   | Set-valued response mapping for u: $oldsymbol{\mathcal{S}(\mathbf{u}) = \arg \min_{\mathbf{w}} f(\mathbf{u}, \mathbf{w})}$                                                                                                                                                                                                                                                                                                                                                                                         |
| $oldsymbol{\Phi}$                                      | Design matrix, where each row corresponds to an example                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| $   \cdot   _2^2$                                       | (Squared) Euclidean norm                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| $   \cdot   _F^2$                                       | (Squared) Frobenius norm                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| $oldsymbol{\mathbf{A}^+}$                              | Moore-Penrose Pseudoinverse of A                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| $A^{+k}$                                                | Shorthand for k-term Neumann series approximate inverse, $oldsymbol{\sum_{j=0}^{k} (\mathbf{I} - \alpha \mathbf{A})^{j}}$                                                                                                                                                                                                                                                                                                                                                                                          |
| $oldsymbol{\mathbf{A}^{\dagger}}$                      | Any matrix such that $oldsymbol{x = A^{\dagger}b}$ is a solution to $oldsymbol{Ax = b}$                                                                                                                                                                                                                                                                                                                                                                                                                           |
| $oldsymbol{u}_k^{\star}$                               | A fixpoint of the outer problem obtained via the k-step unrolled hypergradient                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| $oldsymbol{\hat{\nabla}^{K10}_{\mathbf{u}}F}$          | Hypergradient approximation using $K = 10$ terms of the Neumann series                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| $oldsymbol{\alpha}$                                    | Learning rate for inner optimization or step size for the Neumann series                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| $oldsymbol{\beta}$                                     | Learning rate for outer optimization                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| $oldsymbol{k}$                                         | Number of unrolling iterations / Neumann steps                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| $F^{\star}(\mathbf{u})$                                 | $F(\mathbf{u}, \mathbf{w}^*)$ where $oldsymbol{\mathbf{w}^* \in \mathcal{S}(\mathbf{u})}$                                                                                                                                                                                                                                                                                                                                                                                                                          |
| $oldsymbol{\mathbf{w}^{\star}}$                        | An inner solution, $oldsymbol{\mathbf{w}^* \in \mathcal{S}(\mathbf{u}) = \arg\min_{\mathbf{w}} f(\mathbf{u}, \mathbf{w})}$                                                                                                                                                                                                                                                                                                                                                                                         |
| $oldsymbol{\mathbf{w}_0}$                              | An inner parameter initialization                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| $oldsymbol{\mathcal{N}(\mathbf{A})}$                   | Nullspace of A                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |
| <b>PSD</b>                                              | Positive semi-definite                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| Neumann series                                          | If $(I - A)$ is contractive, then $A^{-1} = oldsymbol{\sum_{j=0}^{\infty} (I - A)^j}$                                                                                                                                                                                                                                                                                                                                                                                                                              |
| <b>Implicit Differentiation</b><br><b>Hypergradient</b> | $rac{dF}{du} = oldsymbol{\left(\frac{\partial \mathbf{w}^{\star}}{\partial \mathbf{u}}\right)^{\top} \left(\frac{\partial F(\mathbf{u}, \mathbf{w}^{\star})}{\partial \mathbf{w}}\right) = -\left(\frac{\partial^2 f(\mathbf{w}^{\star}, \mathbf{u})}{\partial \mathbf{w} \partial \mathbf{w}^{\top}}\right)^{-1} \left(\frac{\partial^2 f(\mathbf{w}^{\star}, \mathbf{u})}{\partial \mathbf{u} \partial \mathbf{w}}\right) \left(\frac{\partial F(\mathbf{u}, \mathbf{w}^{\star})}{\partial \mathbf{w}}\right)}$ |
| Optimize $(f(\\,\cdot\\), \\mathbf{w}_k)$               | Perform an optimization process on $f$ with initialization $w_k$                                                                                                                                                                                                                                                                                                                                                                                                                                                    |
| HypergradApprox $(\\,\mathbf{u}, \\mathbf{w})$          | Compute a hypergradient approximation at given inner and outer parameters                                                                                                                                                                                                                                                                                                                                                                                                                                           |
| Optimistic BLO                                          | $oldsymbol{u}^{\star} \in \arg\min_{\mathbf{u}} F(\mathbf{u}, \mathbf{w}^{\star})$ s.t. $oldsymbol{w}^{\star} \in \arg\min_{\mathbf{w} \in \mathcal{S}(\mathbf{u}^{\star})} F(\mathbf{u}^{\star}, \mathbf{w})$                                                                                                                                                                                                                                                                                                    |
| Pessimistic BLO                                         | $oldsymbol{u}^{\star} \in \arg\min_{\mathbf{u}} F(\mathbf{u}, \mathbf{w}^{\star})$ s.t. $oldsymbol{w}^{\star} \in \arg\max_{\mathbf{w} \in \mathcal{S}(\mathbf{u}^{\star})} F(\mathbf{u}^{\star}, \mathbf{w})$                                                                                                                                                                                                                                                                                                    |
| HO                                                      | Hyperparameter optimization                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         |
| <b>NAS</b>                                              | Neural architecture search                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |
| DD                                                      | Dataset distillation                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| <b>BR</b>                                               | Best-response                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |
| <b>IFT</b>                                              | <b>Implicit Function Theorem</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |

Table 4: Summary of the notation and abbreviations used in this paper.

<span id="page-15-0"></span>

# B. Extended Related Work

Hyperparameter Optimization (HO). There are three main approaches for gradient-based HO: 1) differentiating through unrolls of the inner problem, sometimes called *iterative differentiation* [\(Domke,](#page-9-0) [2012;](#page-9-0) [Maclaurin et al.,](#page-10-0) [2015;](#page-10-0) [Shaban](#page-11-0) [et al.,](#page-11-0) [2019\)](#page-11-0); 2) using *implicit differentiation* to compute the response Jacobian assuming that the inner optimization has converged [\(Larsen et al.,](#page-10-0) [1996;](#page-10-0) [Bengio,](#page-9-0) [2000;](#page-9-0) [Foo et al.,](#page-9-0) [2008;](#page-9-0) [Pedregosa,](#page-11-0) [2016\)](#page-11-0); and 3) using a *hypernetwork* [\(Ha](#page-9-0) [et al.,](#page-9-0) [2017\)](#page-9-0) to approximate the best-response function locally,  $\hat{w}_{\phi}(\lambda) \approx w^*(\lambda)$ , such that the outer gradient can be computed using the chain rule through the hypernetwork,  $\frac{\partial \mathcal{L}_V}{\partial \lambda} = \frac{\partial \mathcal{L}_V}{\partial \hat{w}\phi(\lambda)}$  $\partial \hat{\mathbf{w}} \phi(\boldsymbol{\lambda})$  $\frac{\partial \phi(\lambda)}{\partial \lambda}$ . Hypernetworks have been applied to HO in [\(Lorraine & Duvenaud,](#page-10-0) [2017;](#page-10-0) [MacKay et al.,](#page-10-0) [2019;](#page-10-0) [Bae & Grosse,](#page-9-0) [2020\)](#page-9-0). [MacKay et al.](#page-10-0) [\(2019\)](#page-10-0) and [Bae & Grosse](#page-9-0) [\(2020\)](#page-9-0) have observed that STNs learn online hyperparameter schedules (e.g., for dropout rates and augmentations) that can outperform any fixed hyperparameter value. We believe that warm-start effects at least partially explain the observed improvements from hyperparameter schedules.

Truncation Bias. We restrict our focus to bilevel problems in which the outer parameters *affect the fixed points* of the inner problem—this includes dataset distillation and HO for most regularization hyperparameters, but not the *optimization hyperparameters* such as the learning rate and momentum. Truncation bias has been shown to lead to critical failures when used for tuning such hyperparameters [\(Wu et al.,](#page-12-0) [2018;](#page-12-0) [Metz et al.,](#page-10-0) [2019\)](#page-10-0). In contrast, greedy adaptation of regularization hyperparameters has been successful empirically, via population-based training [\(Jaderberg et al.,](#page-10-0) [2017\)](#page-10-0), hypernetwork-based HO [\(Lorraine & Duvenaud,](#page-10-0) [2017;](#page-10-0) [MacKay et al.,](#page-10-0) [2019;](#page-10-0) [Bae & Grosse,](#page-9-0) [2020\)](#page-9-0), and online implicit differentiation [\(Lorraine](#page-10-0) [et al.,](#page-10-0) [2020;](#page-10-0) [Hataya et al.,](#page-10-0) [2020b\)](#page-10-0).

Data Augmentation. A special case of hyperparameter optimization that has received widespread attention is *automatic data augmentation* [\(Hataya et al.,](#page-10-0) [2020b;a;](#page-10-0) [Riba et al.,](#page-11-0) [2020;](#page-11-0) [Peng et al.,](#page-11-0) [2018;](#page-11-0) [Mounsaveng et al.,](#page-11-0) [2021;](#page-11-0) [Cheung & Yeung,](#page-9-0) [2021\)](#page-9-0), where the aim is either to learn the strengths with which to apply different augmentations, or an *augmentation network* that takes an input image and potentially a source of random noise, and outputs an augmented example [\(Lorraine et al.,](#page-10-0) [2020;](#page-10-0) [Tang et al.,](#page-11-0) [2020\)](#page-11-0). The former approach typically involves tens to hundreds of outer parameters (the coefficients of prespecified augmentations), while the latter approach involves millions of outer parameters (the weights of the augmentation network). The Population-Based Augmentation (PBA) algorithm [\(Ho et al.,](#page-10-0) [2019\)](#page-10-0) searches for augmentation schedules using Population-Based Training [\(Jaderberg et al.,](#page-10-0) [2017\)](#page-10-0); the authors found that training with the PBA schedule outperformed using the fixed final hyperparameters or training with a shuffled schedule (e.g., using the magnitudes of augmentations from the schedule, but in a random order). Dataset distillation [\(Wang et al.,](#page-11-0) [2018\)](#page-11-0) can be thought of as a special case of data augmentation, where the "augmentation" operation replaces an original dataset example with a learned, synthetic example. [Maclaurin et al.](#page-10-0) [\(2015\)](#page-10-0) were among the first to consider learning a training dataset in a bilevel formulation. Recent work has also looked at learning synthetic examples with soft labels, aiming to compress a dataset with N classes into  $M < N$ synthetic examples [\(Sucholutsky & Schonlau,](#page-11-0) [2020\)](#page-11-0).

Hysteresis. [Luketina et al.](#page-10-0) [\(2016\)](#page-10-0) are among the first we are aware of, who explicitly considered the effect of hysteresis (e.g., path-dependence) on the model obtained via alternating optimization of the model parameters and hyperparameters. The HO algorithm they introduce, dubbed T1-T2, uses the IFT with the identity matrix as an approximation to the inverse Hessian (e.g., equivalent to using  $k = 0$  terms of the Neumann series). Interestingly, in contrast to more recent HO papers (particularly ones that focus on data augmentation), [Luketina et al.](#page-10-0) [\(2016\)](#page-10-0) found that re-training the model from scratch performed better than the online model. One potential explanation for this is the choice of hyperparameters: [Luketina et al.](#page-10-0) [\(2016\)](#page-10-0) adapted L2 coefficients and Gaussian noise added to inputs and activations, rather than augmentations.

# C. Derivations

### C.1. Minimum-Norm Response Jacobian

Suppose we have the following inner problem:

$$
f(\mathbf{w}, \mathbf{u}) = \frac{1}{2} ||\mathbf{\Phi} \mathbf{w} - \mathbf{u}||_2^2
$$

The gradient, Hessian, and second-order mixed partial derivatives of  $f$  are:

$$
\frac{\partial f(\mathbf{w}, \mathbf{u})}{\partial \mathbf{w}} = \mathbf{\Phi}^\top \mathbf{\Phi} \mathbf{w} - \mathbf{\Phi}^\top \mathbf{u} \qquad \frac{\partial^2 f(\mathbf{w}, \mathbf{u})}{\partial \mathbf{w} \partial \mathbf{w}^\top} = \mathbf{H} = \mathbf{\Phi}^\top \mathbf{\Phi} \qquad \frac{\partial^2 f(\mathbf{w}, \mathbf{u})}{\partial \mathbf{u} \partial \mathbf{w}} = \mathbf{\Phi}^\top
$$

<span id="page-16-0"></span>The minimum-norm response Jacobian is:

$$
\frac{\partial \mathbf{w}^{\star}(\mathbf{u})}{\partial \mathbf{u}} = \underset{\mathbf{HM} = \mathbf{\Phi}}{\arg \min} ||\mathbf{M}||_F^2
$$
(8)

We need a solution to the linear system  $HM = \Phi$ . Assuming this system is satisfiable, the matrix  $Q = H^+ \Phi$  is a solution that satisfies  $||\mathbf{Q}||_F^2 \le ||\mathbf{M}||_F^2$  for any matrix M:

$$
\mathbf{Q} = \mathbf{H}^+ \mathbf{\Phi} = (\mathbf{\Phi}^\top \mathbf{\Phi})^+ \mathbf{\Phi} = (\mathbf{\Phi}^\top \mathbf{\Phi})^{-1} \mathbf{\Phi} = \mathbf{\Phi}^+ \tag{9}
$$

Thus, the minimum-norm response Jacobian is the Moore-Penrose pseudoinverse of the feature matrix,  $\Phi^+$ .

#### C.2. Iterated Projection.

Alternating projections is a well-known algorithm for computing a point in the intersection of convex sets. Dijkstra's projection algorithm is a modified version which finds a specific point in the intersection of the convex sets, namely the point obtained by projecting the initial iterate onto the intersection. Iterated algorithms for projection onto the intersection of a set of convex sets are studied in (Stošić et al., [2016\)](#page-11-0). [Angelos et al.](#page-9-0) [\(1998\)](#page-9-0) show that successive projections onto hyperplanes can converge to limit cycles. [Mishachev & Shmyrin](#page-11-0) [\(2019\)](#page-11-0) study algorithms based on sequential projection onto hyperplanes defined by equations of a linear system (like Kaczmarz), and show that with specifically-chosen systems of equations, the limit polygon of such an algorithm can be any predefined polygon.

Closed-Form Projection Onto the Solution Set Here, we provide a derivation of the formula we use to compute the analytic projections onto the solution sets given by different hyperparameters in Figure [5.](#page-6-0) This derivation is known in the literature on the Kaczmarz algorithm [\(Karczmarz,](#page-10-0) [1937\)](#page-10-0); we provide it here for clarity.

Consider homogeneous coordinates for the data  $x = [x, 1]$  such that we can write the weights as  $w = [w_1, w_2]$  and the model as  $\mathbf{w}^\top \mathbf{x} = y$ . Given an initialization  $\mathbf{w}_0$ , we would like to find the point  $\mathbf{w}^*$  such that:

$$
\mathbf{w}^* = \arg\min_{\{\mathbf{w} \mid \mathbf{w}^\top \mathbf{x} = y\}} \frac{1}{2} ||\mathbf{w} - \mathbf{w}_0||^2
$$
 (10)

To find the solution to this problem, we write the Lagrangian:

$$
L(\mathbf{w}, \lambda) = \frac{1}{2} ||\mathbf{w} - \mathbf{w}_0||^2 + \lambda (\mathbf{w}^\top \mathbf{x} - y)
$$
\n(11)

The solution to this problem is the stationary point of the Lagrangian. Taking the gradient and equating its components to 0 gives:

$$
\nabla_{\mathbf{w}} L(\mathbf{w}, \lambda) = \mathbf{w} - \mathbf{w}_0 + \lambda \mathbf{x} = 0
$$
 (12)

$$
\nabla_{\lambda} L(\mathbf{w}, \lambda) = \mathbf{w}^{\top} \mathbf{x} - y = 0
$$
\n(13)

From the first equation, we have:

$$
\mathbf{w} = \mathbf{w}_0 - \lambda \mathbf{x} \tag{14}
$$

Plugging this into the second equation gives:

$$
\mathbf{x}^{\top}\mathbf{w} - y = 0\tag{15}
$$

$$
\mathbf{x}^{\top}(\mathbf{w}_0 - \lambda \mathbf{x}) - y = 0 \tag{16}
$$

$$
\mathbf{x}^{\top}\mathbf{w}_0 - \lambda \mathbf{x}^{\top}\mathbf{x} - y = 0 \tag{17}
$$

$$
\lambda \mathbf{x}^{\top} \mathbf{x} = \mathbf{x}^{\top} \mathbf{w}_0 - y \tag{18}
$$

$$
\lambda = \frac{\mathbf{x}^\top \mathbf{w}_0 - y}{\mathbf{x}^\top \mathbf{x}} \tag{19}
$$

Finally, plugging this expression for  $\lambda$  back into the equation  $\mathbf{w} = \mathbf{w}_0 - \lambda \mathbf{x}$ , we have:

$$
\mathbf{w} = \mathbf{w}_0 - \frac{\mathbf{x}^\top \mathbf{w}_0 - y}{\mathbf{x}^\top \mathbf{x}} \mathbf{x}
$$
 (20)

<span id="page-17-0"></span>

# D. Proofs

First, we prove a well-known result on the implicit bias of gradient descent used to optimize convex quadratic functions (Lemma D.1), which we use in this section.

Lemma D.1 (Gradient Descent on a Quadratic Function Finds a Min-Norm Solution). *Suppose we have a convex quadratic function:*

$$
g(\mathbf{w}) = \frac{1}{2}\mathbf{w}^\top \mathbf{A} \mathbf{w} + \mathbf{b}^\top \mathbf{w} + c,\tag{21}
$$

 $\mathbf{A} \in \mathbb{R}^{|\mathcal{W}| \times |\mathcal{W}|}$  is symmetric positive semidefinite,  $\mathbf{b} \in \mathbb{R}^{|\mathcal{W}|}$ , and  $c \in \mathbb{R}$ . If we start from initialization  $\mathbf{w}_0$ , then *gradient descent with an appropriate learning rate will converge to a solution*  $w^* \in \arg\min_w g(w)$  *which has minimum*  $L_2$  *distance from*  $w_0$ :

$$
\mathbf{w}^{\star} \in \underset{\mathbf{w} \in \arg\min_{\mathbf{w}} g(\mathbf{w})}{\arg\min_{\mathbf{z}} g(\mathbf{w})} \frac{1}{2} \|\mathbf{w} - \mathbf{w}_0\|_2^2.
$$
 (22)

*Proof.* Note that  $\nabla_{\mathbf{w}} g(\mathbf{w}) = \mathbf{A} \mathbf{w} + \mathbf{b}$ . By the lower-bounded assumption, the minimum of g is reached when  $\nabla_{\mathbf{w}} g(\mathbf{w}) = 0$ ; one solution, which is the minimum-norm solution with respect to the origin, is  $w = -A^+b$ , where  $A^+$  denotes the Moore-Penrose pseudoinverse of A. The minimum-cost subspace is defined by:

$$
\underset{\mathbf{w}}{\arg\min} g(\mathbf{w}) = \{ \mathbf{w}^{\star} + \mathbf{w}' \mid \mathbf{w}' \in \mathcal{N}(\mathbf{A}) \},\tag{23}
$$

where  $w^*$  is any specific minimizer of g and  $\mathcal{N}(A)$  denotes the nullspace of A. The closed-form solution for the minimizer of g which minimizes the  $L_2$  distance from some initialization  $w_0$  is:

$$
\mathbf{w}^* = -\mathbf{A}^+ \mathbf{b} + (\mathbf{I} - \mathbf{A}^+ \mathbf{A}) \mathbf{w}_0.
$$
 (24)

Note that  $(I - A^+A)w_0$  is in the nullspace of A, since  $A(I - A^+A)w_0 = Aw_0 - AA^+Aw_0 = Aw_0 - Aw_0 = 0$ . Next, we derive a closed-form expression for the result of k steps of gradient descent with a fixed learning rate  $\alpha$ . We write the recurrence:

$$
\mathbf{w}_{k+1} = \mathbf{w}_k - \alpha \nabla_{\mathbf{w}} g(\mathbf{w}_k)
$$
 (25)

$$
= \mathbf{w}_k - \alpha (\mathbf{A} \mathbf{w}_k + \mathbf{b}) \tag{26}
$$

We can subtract the optimum from both sides, yielding:

$$
\mathbf{w}_{k+1} + \mathbf{A}^+ \mathbf{b} = \mathbf{w}_k - \alpha (\mathbf{A} \mathbf{w}_k + \mathbf{b}) + \mathbf{A}^+ \mathbf{b}
$$
 (27)

$$
= \mathbf{w}_k - \alpha \mathbf{A} \mathbf{w}_k - \alpha \mathbf{b} + \mathbf{A}^+ \mathbf{b}
$$
 (28)

$$
= \mathbf{w}_k - \alpha \mathbf{A} \mathbf{w}_k - \alpha \mathbf{A} \mathbf{A}^+ \mathbf{b} + \mathbf{A}^+ \mathbf{b}
$$
 (29)

$$
= (\mathbf{I} - \alpha \mathbf{A})\mathbf{w}_k + \mathbf{A}^+ \mathbf{b} - \alpha \mathbf{A} \mathbf{A}^+ \mathbf{b}
$$
 (30)

$$
= (\mathbf{I} - \alpha \mathbf{A})\mathbf{w}_k + (\mathbf{I} - \alpha \mathbf{A})\mathbf{A}^+ \mathbf{b}
$$
 (31)

$$
= (\mathbf{I} - \alpha \mathbf{A})(\mathbf{w}_k + \mathbf{A}^+ \mathbf{b})
$$
 (32)

Thus, to obtain  $w_{k+1} + A^+b$ , we simply multiply  $w_k + A^+b$  by  $(I - \alpha A)$ . This allows us to write  $w_{k+1}$  as a function of the initialization  $w_0$ :

$$
\mathbf{w}_{k+1} + \mathbf{A}^+ \mathbf{b} = (\mathbf{I} - \alpha \mathbf{A})^k (\mathbf{w}_0 + \mathbf{A}^+ \mathbf{b})
$$
\n(33)

$$
\mathbf{w}_{k+1} = -\mathbf{A}^+ \mathbf{b} + (\mathbf{I} - \alpha \mathbf{A})^k (\mathbf{w}_0 + \mathbf{A}^+ \mathbf{b})
$$
(34)

$$
= -\mathbf{A}^{+}\mathbf{b} + (\mathbf{I} - \alpha \mathbf{A})^{k} ((\mathbf{I} - \mathbf{A}^{+}\mathbf{A})\mathbf{w}_{0} + \mathbf{A}^{+}\mathbf{A}\mathbf{w}_{0} + \mathbf{A}^{+}\mathbf{b})
$$
\n(35)

$$
= -\mathbf{A}^{+}\mathbf{b} + (\mathbf{I} - \alpha \mathbf{A})^{k} ((\mathbf{I} - \mathbf{A}^{+}\mathbf{A})\mathbf{w}_{0}) + (\mathbf{I} - \alpha \mathbf{A})^{k} (\mathbf{A}^{+}\mathbf{A}\mathbf{w}_{0} + \mathbf{A}^{+}\mathbf{b})
$$
(36)

$$
= -\mathbf{A}^+ \mathbf{b} + (\mathbf{I} - \mathbf{A}^+ \mathbf{A}) \mathbf{w}_0,
$$
\n(37)

where the last equation follows from:

$$
(\mathbf{I} - \alpha \mathbf{A})(\mathbf{I} - \mathbf{A}^+ \mathbf{A})\mathbf{w}_0 = (\mathbf{I} - \alpha \mathbf{A} - \mathbf{A}^+ \mathbf{A} + \alpha \mathbf{A} \mathbf{A}^+ \mathbf{A})\mathbf{w}_0 = (\mathbf{I} - \mathbf{A}^+ \mathbf{A})\mathbf{w}_0
$$
\n(38)

<span id="page-18-0"></span>and thus  $(\mathbf{I} - \alpha \mathbf{A})^k (\mathbf{I} - \mathbf{A}^+ \mathbf{A}) \mathbf{w}_0 = (\mathbf{I} - \mathbf{A}^+ \mathbf{A}) \mathbf{w}_0$ , and the term  $((\mathbf{I} - \mathbf{A}^+ \mathbf{A}) \mathbf{w}_0) + (\mathbf{I} - \alpha \mathbf{A})^k (\mathbf{A}^+ \mathbf{A} \mathbf{w}_0 + \mathbf{A}^+ \mathbf{b})$ goes to 0 as  $k \to \infty$  because  $A^+Aw_0$  and  $A^+b$  are in the span of A. Gradient descent will converge for learning rates  $\alpha < 2\lambda_{\text{max}}^{-1}$ , where  $\lambda_{\text{max}}$  is the maximum eigenvalue of **A**. Thus, from Eq. [37](#page-17-0) we see that gradient descent on the quadratic converges to the solution which minimizes the  $L_2$  distance to the initialization  $w_0$ .

We also prove the following Lemma D.2, which we use in Theorem [3.1.](#page-4-0)

**Lemma D.2.** Suppose f and F satisfy Assumption [3.1.](#page-4-0) Then, the function  $F^*(u) \equiv F(u, w^*)$ , where  $w^*$  is a solution to *the inner problem, is a convex quadratic in* u *with a positive semi-definite curvature matrix.*

*Proof.* Any solution to the inner optimization problem for a given outer parameter u, arg min<sub>w</sub>  $f(\mathbf{u}, \mathbf{w})$ , can be expressed as:

$$
\mathbf{w}^* = -\mathbf{A}^+(\mathbf{B}\mathbf{u} + \mathbf{d}) + \mathbf{c},\tag{39}
$$

where c is any element the kernel (or nullspace) of A and  $A^+$  is A's pseudoinverse. Plugging this inner solution into the outer objective  $F$ , we have:

$$
F^{\star}(\mathbf{u}) \equiv F(\mathbf{w}^{\star}) = \frac{1}{2} \mathbf{w}^{\star \top} \mathbf{P} \mathbf{w}^{\star} + \mathbf{f}^{\top} \mathbf{w}^{\star} + h
$$
\n(40)

$$
= \frac{1}{2} \left( -\mathbf{A}^+ \mathbf{B} \mathbf{u} - \mathbf{A}^+ \mathbf{d} + \mathbf{c} \right)^{\top} \mathbf{P} \left( -\mathbf{A}^+ \mathbf{B} \mathbf{u} - \mathbf{A}^+ \mathbf{d} + \mathbf{c} \right) + \mathbf{f}^{\top} (-\mathbf{A}^+ \mathbf{B} \mathbf{u} - \mathbf{A}^+ \mathbf{d} + \mathbf{c}) + h \tag{41}
$$

$$
= \frac{1}{2} \mathbf{u}^{\top} \underbrace{\mathbf{B}^{\top} \mathbf{A}^+ \mathbf{P} \mathbf{A}^+ \mathbf{B}}_{PSD} \mathbf{u} + \mathbf{u}^{\top} (\mathbf{B}^{\top} \mathbf{A}^+ \mathbf{P} \mathbf{A}^+ \mathbf{d} - \mathbf{B}^{\top} \mathbf{A}^+ \mathbf{f} - \mathbf{B}^{\top} \mathbf{A}^+ \mathbf{P} \mathbf{c})
$$
(42)

$$
+\left(\frac{1}{2}\mathbf{d}^{\top}\mathbf{A}^+\mathbf{P}\mathbf{A}^+\mathbf{d}-\mathbf{f}^{\top}\mathbf{A}^+\mathbf{d}-\frac{1}{2}\mathbf{d}^{\top}\mathbf{A}^+\mathbf{P}\mathbf{c}+\frac{1}{2}\mathbf{c}^{\top}\mathbf{P}\mathbf{c}+\mathbf{f}^{\top}\mathbf{c}+h\right).
$$
 (43)

The final equation is a quadratic form in u; we wish to show that the curvature matrix,  $B^{\top}A^+PA^+B$ , is positive semidefinite. Note that  $A^+PA^+$  is PSD because P is PSD by assumption, and thus for any vector v we have:

$$
\mathbf{v}^\top (\mathbf{A}^+ \mathbf{P} \mathbf{A}^+) \mathbf{v} = \mathbf{v}^\top (\mathbf{A}^+)^\top \mathbf{P} \mathbf{A}^+ \mathbf{v} = (\mathbf{A}^+ \mathbf{v})^\top \mathbf{P} (\mathbf{A}^+ \mathbf{v}) \ge 0.
$$
 (44)

 $\Box$ 

Next, because  $A^+PA^+$  is a PSD matrix, it can be expressed in the form  $M^{\top}M$  for some PSD matrix M (e.g., the matrix square root of  $A^+PA^+$ ). Then, for any vector u, we have:

$$
\mathbf{u}^{\top} \mathbf{B}^{\top} \mathbf{A}^+ \mathbf{P} \mathbf{A}^+ \mathbf{B} \mathbf{u} = \mathbf{u}^{\top} \mathbf{B}^{\top} \mathbf{M}^{\top} \mathbf{M} \mathbf{B} \mathbf{u} = (\mathbf{M} \mathbf{B} \mathbf{u})^{\top} (\mathbf{M} \mathbf{B} \mathbf{u}) = ||\mathbf{M} \mathbf{B} \mathbf{u}||_2^2 \ge 0
$$
 (45)

Thus,  $B^{\top}A^+PA^+B$  is PSD.

#### D.1. Proof of Statement [3.1](#page-4-0)

Statement D.1 (Cold-start BLO converges to a cold-start equilibrium.). *Suppose* f *and* F *satisfy Assumption [3.1,](#page-4-0) and assume* we have inner parameter initialization  $w_0$ . Then, given appropriate learning rates for the inner and outer optimizations, the *cold-start algorithm (Algorithm [1\)](#page-25-0) using exact hypergradients converges to a cold-start equilibrium.*

*Proof.* By assumption, the inner objective f is a convex quadratic in w for each fixed u, with positive semi-definite curvature matrix A. By Lemma [D.1,](#page-17-0) with an appropriately-chosen learning rate, the iterates of gradient descent in the inner loop of Algorithm [1](#page-25-0) converge to the solution with minimum  $L_2$  norm from the inner initialization  $w_0$ :  $w_{k+1}^* =$  $\arg \min_{\mathbf{w} \in \mathcal{S}(\mathbf{u}_k)} \frac{1}{2} \|\mathbf{w}-\mathbf{w}_0\|^2$ . Because  $\mathbf{w}_{k+1}^* \in \arg \min_{\mathbf{w}} f(\mathbf{w}, \mathbf{u}_k)$  and assuming that we compute the exact hypergradient, each outer step performs gradient descent on the objective  $F^*(\mathbf{u}) \equiv F(\mathbf{u}, \mathbf{w}^*)$ . By Lemma D.2, the outer objective  $F^*(\mathbf{u})$  is quadratic in u with a PSD curvature matrix, so that with an appropriate outer learning rate, the outer loop of Algorithm [1](#page-25-0) will converge to a solution of  $F^*(u)$ . Thus, we will have a final iterate  $u^* \in \argmin_u F(u, w^*)$  for which the corresponding inner solution is  $w^* \in \arg\min_{w \in S(u^*)} \frac{1}{2} ||w^* - w_0||^2$ , yielding a pair of inner and outer parameters  $(u^*, w^*)$  that are a cold-start equilibrium. $\Box$ 

<span id="page-19-0"></span>

#### D.2. Proof of Theorem [3.1](#page-4-0)

Theorem D.3 (Cold-Start Outer Parameter Norm.). *Suppose* f *and* F *satisfy Assumption [3.1,](#page-4-0) and suppose we run cold-start BLO* (Algorithm [1\)](#page-25-0) using the exact hypergradient, starting from outer parameter initialization  $u_0$ . Assume that for each *outer iteration, the inner parameters are re-initialized to*  $w_0 = 0$  *and optimized with an appropriate learning rate to convergence. Then cold-start BLO—with an appropriate learning rate for the outer optimization—converges to an outer* solution  $\mathbf{u}^*$  with minimum  $L_2$  distance from  $u_0$ :

$$
\mathbf{u}^* = \underset{\mathbf{u} \in \arg\min_{\mathbf{u}} F^*(\mathbf{u})}{\arg\min_{\mathbf{u}} F^*(\mathbf{u})} \frac{1}{2} \|\mathbf{u} - \mathbf{u}_0\|^2 \tag{46}
$$

*Proof.* Since the inner parameters are initialized at  $w_0 = 0$ , the solution to the inner optimization problem found by gradient descent for a given outer parameter u,  $\arg \min_{\mathbf{w}} f(\mathbf{u}, \mathbf{w})$ , can be expressed in closed-form as:

$$
\mathbf{w}^* = -\mathbf{A}^+(\mathbf{B}\mathbf{u} + \mathbf{d}),\tag{47}
$$

where  $A^+$  denotes the Moore-Penrose pseudoinverse of A. Plugging this min-norm inner solution into the outer objective F, we have:

The following equation illustrates the Pythagorean theorem:

$$
F^{\star}(\mathbf{u}) \equiv F(\mathbf{w}^{\star}) = \frac{1}{2} \mathbf{w}^{\star \top} \mathbf{P} \mathbf{w}^{\star} + \mathbf{f}^{\top} \mathbf{w}^{\star} + h
$$

$$
= \frac{1}{2} \mathbf{u}^{\top} \underbrace{\mathbf{B}^{\top} \mathbf{A}^{\ast} \mathbf{P} \mathbf{A}^{\ast} \mathbf{B}}_{\text{PSD}} \mathbf{u} + \mathbf{u}^{\top} (\mathbf{B}^{\top} \mathbf{A}^{\ast} \mathbf{P} \mathbf{A}^{\ast} \mathbf{d} - \mathbf{B}^{\top} \mathbf{A}^{\ast} \mathbf{f}) + \left( \frac{1}{2} \mathbf{d}^{\top} \mathbf{A}^{\ast} \mathbf{P} \mathbf{A}^{\ast} \mathbf{d} - \mathbf{f}^{\top} \mathbf{A}^{\ast} \mathbf{d} + h \right) (49)
$$

Then  $F^*(u)$  is quadratic in u, and by Lemma [D.2,](#page-18-0) the curvature matrix  $B^{\top}A^+PA^+B$  is positive semi-definite. Let  $\mathbf{Z} \equiv \mathbf{B}^\top \mathbf{A}^+ \mathbf{P} \mathbf{A}^+ \mathbf{B}$ . Similarly to the analysis in Lemma [D.1,](#page-17-0) the iterates  $\mathbf{u}_k$  of gradient descent with learning rate  $\alpha$  are given by:

$$
\mathbf{u}_{k} = \mathbf{u}^{\star} + (\mathbf{I} - \alpha \mathbf{Z})^{k} (\mathbf{u}_{0} - \mathbf{u}^{\star}), \qquad (50)
$$

where

$$
\mathbf{u}^{\star} = \underset{\mathbf{u} \in \arg\min_{\mathbf{u}} F^{\star}(\mathbf{u})}{\arg\min_{\mathbf{u}} F^{\star}(\mathbf{u})} \frac{1}{2} ||\mathbf{u} - \mathbf{u}_0||^2.
$$
 (51)

From Eq. (50), we see that the iterates of gradient descent converge exponentially to  $u^*$ , which is the outer solution with minimum  $L_2$  distance from the outer initialization  $\mathbf{u}_0$ .  $\Box$ 

### D.3. Proof of Remark [3.2](#page-4-0)

Remark D.4 (Equivalence of Full Warm-Start and Cold-Start in the Strongly Convex Regime). *When the inner problem* f(u, w) *is strongly convex in* w *for each* u*, then the solution to the inner problem is unique. In this case, full warm-start (Algorithm* [2](#page-25-0) *with*  $T \rightarrow \infty$ ) and *cold-start (Algorithm [1\)](#page-25-0), using exact hypergradients, are equivalent.* 

*Proof.* If  $f(\mathbf{u}, \mathbf{w})$  is strongly convex in w for each u, then it has a unique global minimum for each u. Thus, given an appropriate learning rate for the inner optimization, repeated application of the update  $\Xi$  will converge to this unique solution for any inner parameter initialization. That is,  $\Xi^{(\infty)}(\mathbf{u}, \mathbf{w}_{\text{init}}) = \arg \min_{\mathbf{w}} f(\mathbf{u}, \mathbf{w})$  for any initialization  $\mathbf{w}_{\text{init}} \in \mathcal{W}$ . In particular, the fixpoint will be identical for cold-start and full warm-start,  $\Xi^{(\infty)}(\mathbf{u},\mathbf{w}_0)=\Xi^{(\infty)}(\mathbf{u},\mathbf{w}_k)$  for any  $\mathbf{w}_0$  and  $\mathbf{w}_k$ . Therefore, the inner solutions are identical, and yield identical hypergradients, so the iterates of the full warm-start and cold-start algorithms are equivalent.  $\Box$ 

### D.4. Proof of Statement [3.2](#page-4-0)

Statement D.2 (Inclusion of Partial Warm-Start Equilibria). *Every partial warm-start equilibrium (with* T = 1*) is a full warm-start equilibrium* ( $T \to \infty$ ). In addition, if  $\Xi(\mathbf{u}, \mathbf{w}) = \mathbf{w} - \alpha \nabla_{\mathbf{w}} f(\mathbf{u}, \mathbf{w})$  *with a fixed (non-decayed) step size*  $\alpha$ , *then the corresponding full-warm start equilibria are also partial warm-start equilibria.*

<span id="page-20-0"></span>*Proof.* Let  $(\mathbf{u}^*, \mathbf{w}^*)$  be an arbitrary partial warm-start equilibrium for  $T = 1$ . By definition,  $\mathbf{u}^* \in \arg\min_{\mathbf{u}} F(\mathbf{u}, \mathbf{w}^*)$  and  $\mathbf{w}^* = \Xi^{(1)}(\mathbf{u}, \mathbf{w}^*)$ . Thus,  $\nabla_{\mathbf{w}} f(\mathbf{u}, \mathbf{w}) = 0$ , which entails that  $\mathbf{w}^* = \Xi^{(\infty)}(\mathbf{u}, \mathbf{w}^*)$ . Hence,  $(\mathbf{u}^*, \mathbf{w}^*)$  is a full warm-start equilibrium.

Next, let  $(\mathbf{u}^*, \mathbf{w}^*)$  be an arbitrary full warm-start equilibrium. By definition, this means that  $\mathbf{w}^* = \Xi^{(\infty)}(\mathbf{u}^*, \mathbf{w}^*)$ . By assumption,  $\Xi(\mathbf{u}, \mathbf{w}) = \mathbf{w} - \alpha \nabla_{\mathbf{w}} f(\mathbf{u}, \mathbf{w})$  with a fixed step size  $\alpha$ . The fixed step size combined with the  $T \to \infty$  limit excludes the possibility that there is a finite-length cycle such that the inner optimization arrives back to the initial point  $\mathbf{w}^*$  after a finite number of gradient steps T. Thus, we must have  $\Xi^{(1)}(\mathbf{u},\mathbf{w}) = \mathbf{w}$ , so  $(\mathbf{u}^*,\mathbf{w}^*)$  is a partial warm-start equilibrium.  $\Box$ 

### E. Proximal Best-Response

Consider the proximal objective  $\hat{f}(\mathbf{u}, \mathbf{w}) = f(\mathbf{u}, \mathbf{w}) + \frac{\epsilon}{2} ||\mathbf{w} - \mathbf{w}'||^2$ . Here we will treat  $\mathbf{w}_k$  as a constant (e.g., we won't consider its dependence on **u**). Let  $\mathbf{w}^*(\mathbf{u}) \in \arg\min_{\mathbf{w}} \hat{f}(\mathbf{u}, \mathbf{w})$  be a fixed point of  $\hat{f}$ . We want to compute the response Jacobian  $\frac{\partial \mathbf{w}^{\star}(\mathbf{u})}{\partial \mathbf{u}}$  $\frac{\partial \mathbf{v}^{\star}(\mathbf{u})}{\partial \mathbf{u}}$ . Since  $\mathbf{w}^{\star}$  is a fixed point, we have:

$$
\frac{\partial \hat{f}(\mathbf{u}, \mathbf{w}^{\star}(\mathbf{u}))}{\partial \mathbf{w}} = 0
$$
\n(52)

$$
\frac{\partial f(\mathbf{u}, \mathbf{w}^{\star}(\mathbf{u}))}{\partial \mathbf{w}} + \epsilon(\mathbf{w}^{\star}(\mathbf{u}) - \mathbf{w}') = 0
$$
\n(53)

$$
\frac{\partial}{\partial \mathbf{u}} \frac{\partial f(\mathbf{u}, \mathbf{w}^*(\mathbf{u}))}{\partial \mathbf{w}} + \epsilon \frac{\partial \mathbf{w}^*(\mathbf{u})}{\partial \mathbf{u}} = 0
$$
\n(54)

$$
\frac{\partial^2 f(\mathbf{u}, \mathbf{w}^\star(\mathbf{u}))}{\partial \mathbf{u} \partial \mathbf{w}} + \frac{\partial^2 f}{\partial \mathbf{w} \partial \mathbf{w}^\top} \frac{\partial \mathbf{w}^\star(\mathbf{u})}{\partial \mathbf{u}} + \epsilon \frac{\partial \mathbf{w}^\star(\mathbf{u})}{\partial \mathbf{u}} = 0 \tag{55}
$$

$$
\left(\frac{\partial^2 f}{\partial \mathbf{w}^2} + \epsilon I\right) \frac{\partial \mathbf{w}^{\star}(\mathbf{u})}{\partial \mathbf{u}} = -\frac{\partial^2 f(\mathbf{u}, \mathbf{w}^{\star}(\mathbf{u}))}{\partial \mathbf{u} \partial \mathbf{w}}
$$
(56)

$$
\frac{\partial \mathbf{w}^{\star}}{\partial \mathbf{u}} = -\left(\frac{\partial^2 f}{\partial \mathbf{w} \partial \mathbf{w}^\top} + \epsilon I\right)^{-1} \frac{\partial^2 f}{\partial \mathbf{u} \partial \mathbf{w}} \tag{57}
$$

# F. Equivalence Between Unrolling and Neumann Hypergradients

In this section, we review the result from [\(Lorraine et al.,](#page-10-0) [2020\)](#page-10-0), which shows that when we are at a converged solution to the inner problem  $w^* \in \mathcal{S}(u)$ , then computing the hypergradient by differentiating through k steps of unrolled gradient descent on the inner objective is equivalent to computing the hypergradient with the k-term truncated Neumann series approximation to the inverse Hessian.

In this derivation, the inner and outer objectives are arbitrary—we do not need to assume that they are quadratic. The SGD recurrence for unrolling the inner optimization is:

$$
\mathbf{w}_{i+1} = \mathbf{w}_i(\mathbf{u}) - \alpha \frac{\partial f(\mathbf{w}_i(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_i(\mathbf{u})}
$$
(58)

Then,

$$
\frac{\partial \mathbf{w}_{i+1}}{\partial \mathbf{u}} = \frac{\partial \mathbf{w}_i(\mathbf{u})}{\partial \mathbf{u}} - \alpha \frac{\partial}{\partial \mathbf{w}_i(\mathbf{u})} \left( \frac{\partial f(\mathbf{w}_i(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_i(\mathbf{u})} \frac{\partial \mathbf{w}_i(\mathbf{u})}{\partial \mathbf{u}} + \frac{\partial f(\mathbf{w}_i(\mathbf{u}), \mathbf{u})}{\partial \mathbf{u}} \right)
$$
(59)

$$
= \frac{\partial \mathbf{w}_i(\mathbf{u})}{\partial \mathbf{u}} - \alpha \frac{\partial^2 f(\mathbf{w}_i(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_i(\mathbf{u}) \partial \mathbf{w}_i(\mathbf{u})} - \alpha \frac{\partial^2 f(\mathbf{w}_i(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_i(\mathbf{u}) \partial \mathbf{u}}
$$
(60)

$$
= -\alpha \frac{\partial^2 f(\mathbf{w}_i(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_i(\mathbf{u}) \partial \mathbf{u}} + \left(\mathbf{I} - \alpha \frac{\partial^2 f(\mathbf{w}_i(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_i(\mathbf{u}) \partial \mathbf{w}_i(\mathbf{u})}\right) \frac{\partial \mathbf{w}_i(\mathbf{u})}{\partial \mathbf{u}}
$$
(61)

We can similarly expand out  $\frac{\partial w_i(u)}{\partial u}$  as:

$$
\frac{\partial \mathbf{w}_i(\mathbf{u})}{\partial \mathbf{u}} = -\alpha \frac{\partial^2 f(\mathbf{w}_{i-1}(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_{i-1} \partial \mathbf{u}} + \left(\mathbf{I} - \alpha \frac{\partial^2 f(\mathbf{w}_{i-1}(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_{i-1}(\mathbf{u}) \partial \mathbf{w}_{i-1}(\mathbf{u})}\right) \frac{\partial \mathbf{w}_{i-1}(\mathbf{u})}{\partial \mathbf{u}} \tag{62}
$$

On Implicit Bias in Overparameterized Bilevel Optimization

<span id="page-21-0"></span>Plugging in this expression for  $\frac{\partial w_i(u)}{\partial u}$  into the expression for  $\frac{\partial w_{i+1}(u)}{\partial u}$ , we have:

$$
\frac{\partial \mathbf{w}_{i+1}(\mathbf{u})}{\partial \mathbf{u}} = -\alpha \frac{\partial^2 f(\mathbf{w}_i(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_i(\mathbf{u}) \partial \mathbf{u}} + \left(\mathbf{I} - \alpha \frac{\partial^2 f(\mathbf{w}_i(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_i(\mathbf{u}) \partial \mathbf{w}_i(\mathbf{u})}\right)
$$
(63)

$$
\times \left[ -\alpha \frac{\partial^2 f(\mathbf{w}_{i-1}(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_{i-1}(\mathbf{u}) \partial \mathbf{u}} + \left( \mathbf{I} - \alpha \frac{\partial^2 f(\mathbf{w}_{i-1}(\mathbf{u}), \mathbf{u})}{\partial \mathbf{u}_{i-1}(\mathbf{u}) \partial \mathbf{w}_{i-1}(\mathbf{u})} \right) \frac{\partial \mathbf{w}_{i-1}(\mathbf{u})}{\partial \mathbf{u}} \right]
$$
(64)

Expanding, we have:

$$
\frac{\partial \mathbf{w}_{i+1}(\mathbf{u})}{\partial \mathbf{u}} = -\alpha \frac{\partial^2 f(\mathbf{w}_i(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_i(\mathbf{u}) \partial \mathbf{u}} + \left(\mathbf{I} - \alpha \frac{\partial^2 f(\mathbf{w}_i(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_i(\mathbf{u}) \partial \mathbf{w}_i(\mathbf{u})}\right) \left(-\alpha \frac{\partial^2 f(\mathbf{w}_{i-1}(\mathbf{u}), \mathbf{u})}{\partial \mathbf{w}_{i-1}(\mathbf{u}) \partial \mathbf{u}}\right)
$$
(65)

$$
+\prod_{k< j}\left(\mathbf{I}-\alpha\frac{\partial^2 f(\mathbf{w}, \mathbf{u})}{\partial \mathbf{w}\partial \mathbf{w}^\top}\bigg|_{\mathbf{u}, \mathbf{w}_{i-k}(\mathbf{u})}\right)\frac{\partial \mathbf{w}_{i-1}(\mathbf{u})}{\partial \mathbf{u}}\tag{66}
$$

Telescoping this sum, we have:

$$
\frac{\partial \mathbf{w}_{i+1}(\mathbf{u})}{\partial \mathbf{u}} = \sum_{j \leq i} \left[ \prod_{k < j} \left( \mathbf{I} - \alpha \frac{\partial^2 f(\mathbf{w}, \mathbf{u})}{\partial \mathbf{w} \partial \mathbf{w}^\top} \bigg|_{\mathbf{u}, \mathbf{w}_{i-k}(\mathbf{u})} \right) \right] \left( -\alpha \frac{\partial^2 f(\mathbf{u})}{\partial \mathbf{w} \partial \mathbf{u}} \bigg|_{\mathbf{u}, \mathbf{w}_{i-j}(\mathbf{u})} \right) \tag{67}
$$

If we start unrolling from a stationary point of the proximal objective, then all the  $w_i$  will be equal, so this simplifies to:

$$
\frac{\partial \mathbf{w}_{i+1}(\mathbf{u})}{\partial \mathbf{u}} = \left[ \sum_{j \leq i} \left( \mathbf{I} - \alpha \frac{\partial^2 f(\mathbf{w}, \mathbf{u})}{\partial \mathbf{w} \partial \mathbf{w}^\top} \right)^j \right] \left( -\alpha \frac{\partial^2 f(\mathbf{w}, \mathbf{u})}{\partial \mathbf{w} \partial \mathbf{u}} \right)
$$
(68)

This recovers the Neumann series approximation to the inverse Hessian.

# G. Experimental Details and Extended Results

Compute Environment. All experiments were implemented using JAX [\(Bradbury et al.,](#page-9-0) [2018\)](#page-9-0), and were run on NVIDIA P100 GPUs. Each instance of the dataset distillation and antidistillation task took approximately 5 minutes of compute on a single GPU.

### G.1. Details and Extended Results for Dataset Distillation.

Details. For our dataset distillation experiments, we trained a 4-layer MLP with 200 hidden units per layer and ReLU activations. For warm-start joint optimization, we computed hypergradients by differentiating through  $K = 1$  steps of unrolling, and updated the hyperparameters (learned datapoints) and MLP parameters using alternating gradient descent, with one step on each. We used SGD with learning rate 0.001 for the inner optimization and Adam with learning rate 0.01 for the outer optimization.

Link to Animations. Here is a [link to an document containing animations](https://docs.google.com/document/d/1whS9xmU_gfwsqtpUchf3ltlXSxcA_8iPoMwAfNqXNMs/edit?usp=sharing) of the bilevel training dynamics for the dataset distillation task. We visualize the dynamics of warm-started bilevel optimization in the setting where the inner problem is overparameterized, by animating the trajectories of the learned datapoints over time, and showing how the decision boundary of the model changes over the course of joint optimization.

Extended Results. Here, we show additional dataset distillation results, using a similar setup to Section [4.1.](#page-5-0)

Figure [8](#page-22-0) shows the results where we fit a three-class problem (e.g., three concentric rings) using three learned datapoints. Figure [9](#page-22-0) shows the results for fitting three classes using *only two datapoints*, where both the coordinates and *soft labels* are learned. For each training datapoint, in addition to learning its x- an y-coordinates, we learn a C-dimensional vector (where C is the number of classes, in this example  $C = 3$ ) representing the unnormalized class label: this vector is normalized with a softmax when we perform cross-entropy training of the inner model (e.g., we do not train on one-hot labels). Joint adaptation of the model parameters and learned data is able to fit three classes by changing the learned class label for one of the datapoints during training.

<span id="page-22-0"></span>

| Method                   | Dataset Distillation |             |             | Data Augmentation Net |         |       |
|--------------------------|----------------------|-------------|-------------|-----------------------|---------|-------|
|                          | MNIST                | Fashion     | C-10        | MNIST                 | Fashion | C-10  |
| Cold-Start               | 30.7 / 89.1          | 33.2 / 83.0 | 17.6 / 46.9 | 84.86                 | 84.04   | 45.38 |
| Warm-Start               | 90.8 / 97.5          | 88.2 / 94.2 | 50.3 / 59.8 | 92.81                 | 89.51   | 59.30 |
| Warm-Start +<br>Re-Train | 12.9 / 17.1          | 7.0 / 12.6  | 10.2 / 8.9  | 9.15                  | 25.32   | 11.12 |

Table 5: Cols 1-3: Accuracy on original data with 1/10 synthetic samples. Cols 4-6: Learning a data augmentation network.

Image /page/22/Figure/3 description: The image displays three scatter plots, each illustrating a different stage of a machine learning process. All plots are on a 2D plane with axes labeled x1 and x2, ranging from -3 to 3. Plot (a), titled "Training on original data," shows data points arranged in concentric circles. The innermost circle is yellow, surrounded by a teal circle, and then a purple circle. The background is shaded in light blue, with darker blue contours indicating decision boundaries. Plot (b), titled "Warm-start joint optimization," shows a similar arrangement of data points but with added light blue and pink lines representing optimization paths. These paths are erratic, suggesting a complex optimization process. Plot (c), titled "Re-training on final points," shows the same data points as (a), but the decision boundaries are now sharp, with distinct regions of dark blue and light blue, indicating a different classification outcome compared to plot (a).

Figure 8: Dataset distillation results fitting three classes with three learned datapoints. Similarly to the results in Sec. [4.1,](#page-5-0) when using warm-start joint optimization, the three learned datapoints are adapted during training to trace out the data in their respective classes, guiding the network to learn a decision boundary that performs well on the original data. Cold-start re-training yields a model that correctly classifies the three learned datapoints, but has poor validation performance.

Image /page/22/Figure/5 description: The image displays three plots, labeled (a), (b), and (c), each illustrating a 2D decision boundary with data points. All plots have x1 and x2 axes ranging from -3 to 3. Plot (a), titled "Training on original data," shows concentric circular data distributions colored purple, teal, and yellow, with corresponding decision boundaries in light blue, teal, and dark blue. Plot (b), titled "Warm-start joint optimization," presents a similar data distribution but includes colored lines representing an optimization path, starting from the outer purple points and moving inwards towards the yellow cluster. Plot (c), titled "Re-training on final points," shows a different decision boundary, with a sharp diagonal split separating the purple and teal points, and a smaller region for the yellow cluster. A blue 'x' marker is present in the upper left quadrant.

Figure 9: Dataset distillation results fitting three classes with *only two* learned datapoints. In the warm-start plot, the color of the trajectory of each learned datapoint indicates its soft class membership, with magenta, green, and blue corresponding to the inner, middle, and outer rings, respectively. Darker/gray colors indicate soft labels that place approximately equal probability on each class. We see that although we only have two learned datapoints, the class labels change over the course of training such that all three classes are covered. Cold-start re-training yields a model that correctly classifies the three learned datapoints, but has poor validation performance.

<span id="page-23-0"></span>Image /page/23/Figure/1 description: This is a line graph showing the relationship between x and y values. The graph displays several lines with different colors and markers, representing different parameters. The legend indicates that a green dot represents the 'Orig Point', orange crosses represent 'ε=1e8', lighter orange crosses represent 'ε=1e7', darker orange crosses represent 'ε=1e6', and brown crosses represent 'ε=1e5'. A black line with black crosses represents the 'Pseudoinverse'. The x-axis ranges from -2 to 2, labeled 'x'. The y-axis ranges from 0 to 2, labeled 'y'. The lines generally peak at x=0 and decrease as x moves away from 0, with the 'Pseudoinverse' line showing oscillations.

Figure 10: Antidistillation task for linear regression with an overparameterized outer objective. This plot uses the same setup as Figure [7,](#page-8-0) but shows learned datapoints obtained via the proximal best-response Jacobian, for various damping factors  $\epsilon$ .

#### G.2. Details and Extended Results for Anti-Distillation.

Details. For the anti-distillation results in Section [4.2,](#page-7-0) we used a Fourier basis consisting of 10 sin terms, 10 cos terms, and a bias, yielding 21 total inner parameters. For the exponential-amplitude Fourier basis used in Section [4.2,](#page-7-0) we used SGD with learning rates 1e-8 and 1e-2 for the inner and outer parameters, respectively; for the  $1/n$  amplitude Fourier basis (discussed below, and used for Figure [12\)](#page-24-0), we used SGD with learning rates 1e-3 and 1e-2 for the inner and outer parameters, respectively.

Approximate Hypergradient Visualization. For the visualization in Figure [7c,](#page-8-0) the experiment setup is as follows: we have a single original dataset point at xy coordinate  $(1, 1)$ , and we learn the y-coordinates of two synthetic datapoints, which we initialize at xy-coordinates  $(0, 0)$  and  $(2, 0)$ , respectively. The inner model trained on these two synthetic datapoints is a linear regressor with one weight and one bias, e.g.,  $y = wx + b$ . The outer problem is overparameterized, because there are many valid settings of the learned datapoints such that the inner model trained on those datapoints will fit the point  $(1, 1)$ . For example, three valid solutions for the learned datapoints are  $\{(0,0),(2,2)\}$ ,  $\{(0,1),(2,1)\}$ , and  $\{(0,2),(2,0)\}$ . The set of such valid solutions is visualized in Figure [7c,](#page-8-0) as well as the gradients using different hypergradient approximations, outer optimization trajectories, and converged outer solutions with each approximation. We focused on Neumann series approximations with different  $k$ , and ran the Neumann series at the converged inner parameters in each case.

Conjugate Gradient. Here, we present results using truncated conjugate gradient to approximate the inverse Hessian vector product when using the implicit function theorem to compute the hypergradient. We use the same problem setup as in Section [4.2,](#page-7-0) where we have a single validation datapoint and 13 learned datapoints. Because our Fourier basis consists of 10 sin terms, 10 cos terms, and a bias, we have 21 inner parameters, and using 21 steps of CG is guaranteed to yield the true inverse-Hessian vector product (barring any numerical issues). In Figure 11, we show the effect of using truncated CG iterations on the learned datapoints: we found that while Neumann iterations, truncated unrolling, and proximal optimization all yield nearly identical results, CG produces a very different inductive bias.

Image /page/23/Figure/7 description: This is a line graph showing the relationship between x and y values. The graph displays several lines representing different values of K, including K=1, K=2, K=5, and K=10, as well as a line labeled 'Pseudoinverse'. There is also a single green point labeled 'Orig Point'. The x-axis ranges from -2 to 2, and the y-axis ranges from 0 to 2. The lines and points are plotted to illustrate the behavior of these different K values and the pseudoinverse in relation to the original point.

Figure 11: Truncated CG used to compute hypergradients for the anti-distillation task.

An Alternate Set of Fourier Basis Functions. In Section [4.2,](#page-7-0) we used a Fourier basis in which the lower-frequency terms had exponentially larger

amplitudes than the high-frequency terms. Figure [12](#page-24-0) presents results using an alternative feature function:  $\phi(x)$  =  $a_0 + \sum_{n=1}^{N} (a_n \left(\frac{1}{n}\right) \cos(nx) + b_n \left(\frac{1}{n}\right) \sin(nx)).$ 

Using an MLP. Finally, we show that similar conclusions hold when training a multi-layer perceptron (MLP) on the antidistillation task. We used a 2-layer MLP with 10 hidden units per layer and ReLU activations. We used SGD with learning rate 0.01 for both the inner and outer parameters. Figure [13](#page-24-0) shows the learned datapoints and model fits resulting from

<span id="page-24-0"></span>Image /page/24/Figure/1 description: The image displays three plots. Plot (a), titled "Neumann/unrolling", and plot (b), titled "Proximal", are both scatter plots with lines showing the relationship between 'x' on the x-axis and 'y' on the y-axis. Plot (a) shows data points for 'Orig Point' (green circle), 'K=1' (orange cross), 'K=10' (orange cross), 'K=100' (orange cross), 'K=1000' (orange cross), and 'Pseudoinverse' (black cross). Plot (b) shows data points for 'Val Point' (green cross), 'ε=1000' (orange circle), 'ε=100' (orange circle), 'ε=10' (orange circle), 'ε=1' (orange circle), and 'Pseudoinverse' (black circle). Plot (c), titled "Outer parameter norms.", is a line plot showing the relationship between 'K' on the x-axis (on a logarithmic scale) and '||u||^2' on the y-axis. The y-axis ranges from 6 to 16, and the x-axis ranges from 10^1 to 10^3. A single purple line is plotted, starting at approximately 16.5 on the y-axis when K is 10, and decreasing to approximately 6.5 on the y-axis when K is 1000.

Figure 12: Fourier-basis 1D linear regression, where the outer objective is overparameterized. We learn 13 synthetic datapoints such that a regressor trained on those points will fit a single "validation" datapoint, shown by the green  $X$  at  $(0, 2)$ . The synthetic datapoints are initialized at linearly-spaced x-coordinates, with y-coordinate 0, and we only learn the targets y. In the Fourier basis we use, lower frequency components have larger amplitudes. Here, we use the  $1/n$  amplitude scheme.

running several different steps of Neumann iterations or unrolling, as well as the norms of the inner and outer parameters as a function of  $K$ . For the Neumann experiments, we first optimize the MLP for 5000 steps to reach approximate convergence of the inner problem, before running K Neumann iterations—the MLP is re-trained from scratch for 5000 steps for each outer parameter update (e.g., cold-started).

Image /page/24/Figure/4 description: This image contains three plots. The first two plots, labeled (a) Neumann and (b) Unrolling, are line graphs with x and y axes. The x-axis ranges from -2 to 2, and the y-axis ranges from 0 to 2. Both plots show four lines representing different values of K: K=1, K=10, K=100, and K=1000. There is also a green dot labeled 'Orig Point'. The lines in plot (a) show a peak at x=0 for K=1 and K=10, and a flatter curve for K=100 and K=1000. The lines in plot (b) show a similar pattern, with K=1 and K=10 peaking at x=0 and K=100 and K=1000 showing flatter curves. The third plot, labeled (c) Outer parameter norms, is a line graph with a logarithmic x-axis labeled 'K' ranging from 10^0 to 10^3, and a y-axis labeled '||u||^2' ranging from 0 to 40. This plot shows a decreasing trend, with the value of ||u||^2 dropping from approximately 45 at K=10^0 to approximately 5 at K=10^3.

Figure 13: 1D linear regression with an overparameterized outer objective, where we train an MLP rather than performing Fourier-basis function regression. Note that here we cannot analytically compute the proximal solution as in the linear regression case, so we only include results for full-unrolls with truncated Neumann approximations of the inverse Hessian, and alternating gradient descent with various numbers of unroll steps.

# H. Algorithms

<span id="page-25-0"></span>

| Algorithm 1 Cold-Start BLO                                                                  | Algorithm 2 Warm-start BLO                                                                  |
|---------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------|
| 1: <b>Input:</b> $\alpha$ : inner LR, $\beta$ : outer LR                                    | 1: <b>Input:</b> $\alpha$ : inner LR, $\beta$ : outer LR                                    |
| 2: $T$ : number of inner optimization steps                                                 | 2: $T$ : number of inner optimization steps                                                 |
| 3: $K$ : number of Neumann series terms                                                     | 3: $K$ : number of Neumann series terms                                                     |
| 4: while $  \hat{\nabla}_{{\bf u}} F({\bf u}, {\bf w})  ^2 > 0$ , iteration t do            | 4: while $  \nabla_{{\bf u}} F({\bf u}, {\bf w})  ^2 > 0$ , iteration t do                  |
| 5: $\nabla_{{\bf u}} F \leftarrow \text{HypergradApprox}(\mathbf{u}_t, \mathbf{w}_0, T, K)$ | 5: $\nabla_{{\bf u}} F \leftarrow \text{HypergradApprox}(\mathbf{u}_t, \mathbf{w}_t, T, K)$ |
| 6: $\mathbf{u}_{t+1} \leftarrow \mathbf{u}_t - \beta \hat{\nabla}_{{\bf u}} F$              | 6: $\mathbf{u}_{t+1} \leftarrow \mathbf{u}_t - \beta \nabla_{{\bf u}} F$                    |
| 7:                                                                                          | 7: $\mathbf{w}_{t+1} \leftarrow \text{Optimize}(\mathbf{u}_{t+1}, \mathbf{w}_t, T)$         |
| 8: end while                                                                                | 8: end while                                                                                |
| 9: return $(\mathbf{u}_t, \text{Optimize}(\mathbf{u}_t, \mathbf{w}_0, T))$                  | 9: return $(\mathbf{u}_t, \mathbf{w}_t)$                                                    |

Figure 14: Algorithms for cold-start and warm-start bilevel optimization. We highlight the key differences in cyan for cold-start and orange for warm-start.

| <b>Algorithm 3</b> NeumannHypergrad                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | <b>Algorithm 4</b> Optimize $(\mathbf{u}, \mathbf{w}, T)$                                                                                                                                                                                               | <b>Algorithm 5 BRJ-Approx</b>                                                                                                                             |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1: <b>Input:</b> $\alpha$ : inner LR, $\beta$ : outer LR<br>$T:$ inner steps<br>2:<br>3:<br>$K$ : Neumann terms<br>4: $\hat{\mathbf{w}}_T^* \leftarrow \text{Optimize}(\mathbf{u}_t, \mathbf{w}_t, T)$<br>5: $\frac{d\hat{\mathbf{w}}_T^*}{du} \leftarrow$ BRJ-Approx $(\hat{\mathbf{w}}_T^*, \mathbf{u}_t, K)$<br>6: $\hat{\nabla}_{\mathbf{u}}F \leftarrow \frac{\partial F}{\partial \mathbf{u}} + \left(\frac{d\hat{\mathbf{w}}_T^*}{d\mathbf{u}}\right)^{\top} \frac{\partial F(\mathbf{u}, \hat{\mathbf{w}}_T^*)}{\partial \hat{\mathbf{w}}_T^*}$<br>7: return $\hat{\nabla}_{\mathbf{u}}F$ | 1: <b>Input:</b> $\gamma$ : learning rate<br>2: $T:$ unroll steps<br>w: initialization<br>3:<br>4: for $t = 1, , T$ do<br>5: $\mathbf{w} \leftarrow \mathbf{w} - \gamma \nabla_{\mathbf{w}} f(\mathbf{u}, \mathbf{w})$<br>$6:$ end for<br>$7:$ return w | $1:$ return<br>$\alpha \sum_{j=0}^{K}\left(I - \alpha \frac{\partial^2 f(\mathbf{u}, \mathbf{w})}{\partial \mathbf{w} \partial \mathbf{w}^\top}\right)^j$ |

Figure 15: Helper functions to compute the implicit hypergradient using the Neumann series to approximate the inverse inner loss Hessian.

Algorithm 6 IterativeDiffHypergrad

- 1: Input:  $\alpha$ : inner LR,  $\beta$ : outer LR
- 2: T: inner steps
- 3:  $\hat{\mathbf{w}}_T^* \leftarrow \text{Optimize}(\mathbf{u}_t, \mathbf{w}_t, T)$
- 4:  $\hat{F} \leftarrow F(\mathbf{u}, \hat{\mathbf{w}}_{\mathcal{I}}^*)$
- 5: Compute  $\nabla_{\mathbf{u}} \hat{F}$  using auto-diff.
Image /page/0/Picture/0 description: The image is a logo for the Department of Health & Human Services, USA. The logo features a blue eagle with two human profiles within its wings. The text "DEPARTMENT OF HEALTH & HUMAN SERVICES "."USA" is written in a circle around the eagle.

# **HHS Public Access**

Author manuscript IEEE Trans Pattern Anal Mach Intell. Author manuscript; available in PMC 2024 April 15.

Published in final edited form as:

IEEE Trans Pattern Anal Mach Intell. 2023 November ; 45(11): 13344–13362. doi:10.1109/ TPAMI.2023.3292075.

# **Transfer Learning in Deep Reinforcement Learning: A Survey**

# **<PERSON><PERSON><PERSON>**,

Department of Computer Science and Engineering, Michigan State University, East Lansing, MI, 48824.

## **<PERSON><PERSON>ng Lin**,

Amazon Alexa AI.

## **<PERSON><PERSON>**,

Department of Computer Science and Engineering, Michigan State University, East Lansing, MI, 48824.

## **<PERSON><PERSON><PERSON>**

Department of Computer Science and Engineering, Michigan State University, East Lansing, MI, 48824.

# **Abstract**

Reinforcement learning is a learning paradigm for solving sequential decision-making problems. Recent years have witnessed remarkable progress in reinforcement learning upon the fast development of deep neural networks. Along with the promising prospects of reinforcement learning in numerous domains such as robotics and game-playing, transfer learning has arisen to tackle various challenges faced by reinforcement learning, by transferring knowledge from external expertise to facilitate the efficiency and effectiveness of the learning process. In this survey, we systematically investigate the recent progress of transfer learning approaches in the context of deep reinforcement learning. Specifically, we provide a framework for categorizing the state-of-the-art transfer learning approaches, under which we analyze their goals, methodologies, compatible reinforcement learning backbones, and practical applications. We also draw connections between transfer learning and other relevant topics from the reinforcement learning perspective and explore their potential challenges that await future research progress.

# **Keywords**

Transfer Learning; Reinforcement Learning; Deep Learning; Survey

# **1 Introduction**

REinforcement Learning (RL) is an effective framework to solve sequential decision-making tasks, where a learning agent interacts with the environment to improve its performance through trial and error [1]. Originated from cybernetics and thriving in computer science, RL has been widely applied to tackle challenging tasks which were previously intractable.

Traditional RL algorithms were mostly designed for tabular cases, which provide principled solutions to simple tasks but face difficulties when handling highly complex domains, e.g. tasks with 3D environments. With the recent advances in deep learning research, the combination of RL and deep neural networks is developed to address challenging tasks. The combination of deep learning with RL is hence referred to as *Deep Reinforcement Learning*  (DRL) [2], which learns powerful function approximators using deep neural networks to address complicated domains. DRL has achieved notable success in applications such as robotics control [3, 4] and game playing [5]. It also thrives in domains such as health informatics [6], electricity networks [7], intelligent transportation systems[8, 9], to name just a few.

Besides its remarkable advancement, RL still faces intriguing difficulties induced by the exploration-exploitation dilemma [1]. Specifically, for practical RL problems, the environment dynamics are usually unknown, and the agent cannot exploit knowledge about the environment until enough interaction experiences are collected via exploration. Due to the partial observability, sparse feedbacks, and the high complexity of state and action spaces, acquiring sufficient interaction samples can be prohibitive or even incur safety concerns for domains such as automatic-driving and health informatics. The abovementioned challenges have motivated various efforts to improve the current RL procedure. As a result, *transfer learning* (TL), or equivalently referred as *knowledge transfer*, which is a technique to utilize external expertise to benefit the learning process of the target domain, becomes a crucial topic in RL.

While TL techniques have been extensively studied in *supervised learning* [10], it is still an emerging topic for RL. Transfer learning can be more complicated for RL, in that the knowledge needs to transfer in the context of a Markov Decision Process. Moreover, due to the delicate components of the Markov decision process, expert knowledge may take different forms that need to transfer in different ways. Noticing that previous efforts on summarizing TL in the RL domain did not cover research of the last decade [11, 12], during which time considerate TL breakthroughs have been achieved empowered with deep learning techniques. Hence, in this survey, we make a comprehensive investigation of the latest TL approaches in RL.

The contributions of our survey are multifold: 1) we investigated up-to-date research involving new DRL backbones and TL algorithms over the recent decade. To the best of our knowledge, this survey is the first attempt to survey TL approaches in the context of *deep*  reinforcement learning. We reviewed TL methods that can tackle more evolved RL tasks, and also studied new TL schemes that are not deeply discussed by prior literatures, such as representation disentanglement (Sec 5.5) and policy distillation (Sec 5.3). 2) We provided systematic categorizations that cover a broader and deeper view of TL developments in DRL. Our main analysis is anchored on a fundamental question, *i.e. what is the transferred* knowledge in RL, following which we conducted more refined analysis. Most TL strategies, including those discussed in prior surveys are well suited in our categorization framework. 3) Reflecting on the developments of TL methods in DRL, we brought new thoughts on its future directions, including how to do *reasoning* over miscellaneous knowledge forms and how to *leverage* knowledge in more efficient and principled manner. We also pointed out

The rest of this survey is organized as follows: In Section 2 we introduce RL preliminaries, including the recent key development based on deep neural networks. Next, we discuss the definition of TL in the context of RL and its relevant topics (Section 2.4). In Section 3, we provide a framework to categorize TL approaches from multiple perspectives, analyze their fundamental differences, and summarize their evaluation metrics (Section 3.3). In Section 5, we elaborate on different TL approaches in the context of DRL, organized by the format of transferred knowledge, such as reward shaping (Section 5.1), learning from demonstrations (Section 5.2), or learning from teacher policies (Section 5.3). We also investigate TL approaches by the way that knowledge transfer occurs, such as inter-task mapping (Section 5.4), or learning transferrable representations (Section 5.5), etc. We discuss contemporary applications of TL in the context of DRL in Section 6 and provide some future perspectives and open questions in Section 7.

# **2 Deep Reinforcement Learning and Transfer Learning**

## **2.1 Reinforcement Learning Basics**

**Markov Decision Process:** A typical RL problem can be considered as training an agent to interact with an environment that follows a Markov Decision Process (MPD) [13]. The agent starts with an initial *state* and performs an *action* accordingly, which yields a reward to guide the agent actions. Once the action is taken, the MDP transits to the next state by following the underlying *transition dynamics* of the MDP. The agent accumulates the time-*discounted* rewards along with its interactions. A subsequence of interactions is referred to as an episode. The above-mentioned components in an MDP can be represented using a tuple, *i.e.*  $M = (\mu_0, \mathcal{S}, \mathcal{A}, \mathcal{T}, \gamma, \mathcal{R})$ , in which:

- $\mu_0$  is the set of *initial states*.
- **•** S is the *state* space.
- $\mathscr A$  is the *action* space.
- $\mathcal{T}: S \times A \times S \rightarrow \mathbb{R}$  is the *transition probability distribution*, where  $\mathcal{T}(s' \mid s, a)$ specifies the probability of the state transitioning to s' upon taking action a from state s.
- $\mathcal{R}: S \times A \times S \to \mathbb{R}$  is the *reward distribution*, where  $\mathcal{R}(s, a, s')$  is the reward that an agent can get by taking action  $\alpha$  from state s with the next state being  $s'$ .
- $\gamma$  is a discounted factor, with  $\gamma \in (0, 1]$ .

A RL agent behaves in  $M$  by following its policy  $\pi$ , which is a mapping from states to actions:  $\pi: \mathcal{S} \to \mathcal{A}$ . For a stochastic policy  $\pi$ ,  $\pi(a \mid s)$  denotes the probability of taking action a from state s. Given an MDP  $\mathcal M$  and a policy  $\pi$ , one can derive a *value function*  $V^{\pi}_{\mathcal M}(s)$ , which is defined over the state space:  $V_{\mathcal{M}}^{\pi}(s) = \mathbb{E}[r_0 + \gamma r_1 + \gamma^2 r_2 + \dots; \pi, s]$ , where  $r_i = \mathcal{R}(s_i, a_i, s_{i+1})$  is the reward that an agent receives by taking action  $a_i$  in the *i*-th state  $s_i$ , and the next

state transits to  $s_{i+1}$ . The expectation  $E$  is taken over  $s_0 \sim \mu_0$ <br>The value function estimates the *quality* of being in state<br>rewards that an agent can get from *s* following policy  $\pi$ .  $, a_i \sim \pi(\cdot \mid s_i), s_{i+1} \sim \mathcal{T}(\cdot \mid s_i, a_i).$ The value function estimates the *quality* of being in state s, by evaluating the expected rewards that an agent can get from s following policy  $\pi$ . Similar to the value function, a policy also carries a Q-**function**, which estimates the quality of taking action a from state  $s: Q_{\mathcal{M}}^{\pi}(s, a) = \mathbb{E}_{s' \sim \mathcal{T}(\cdot \mid s, a)}[\mathcal{R}(s, a, s') + \gamma V_{\mathcal{M}}^{\pi}(s')]$ .

**Reinforcement Learning Goals:** Standard RL aims to learn an optimal policy  $\pi_{\mathcal{M}}^*$  with the optimal value and Q-function, s.t.  $\forall s \in \mathcal{S}, \pi_{\mathcal{M}}^*(s) = \argmax Q_{\mathcal{M}}^*(s, a)$ , where  $a \in A$ 

 $Q^*_{\mathcal{M}}(s, a)$  = sup  $Q^*_{\mathcal{M}}(s, a)$ . The learning objective can be reduced as maximizing the expected π return:

$$
J(\pi):=\mathbb{E}_{(s,a)\sim\mu^{\pi}(s,a)}\bigg[\sum_t\gamma^t r_t\bigg],
$$

where  $\mu^{\pi}(s, a)$  is the *stationary state-action distribution* induced by  $\pi$  [14].

Built upon recent progress of DRL, some literature has extended the RL objective to achieving miscellaneous goals under different conditions, referred to as *Goal-Conditional RL* (GCRL). In GCRL, the agent policy  $\pi(\cdot | s, g)$  is dependent not only on state observations s but also the goal g being optimized. Each individual goal  $g \sim \mathcal{G}$  can be differentiated by its reward function  $r(s_i, a_i, g)$ , hence the objective for GCRL becomes maximizing the expected return over the distribution of goals:  $J(\pi)$ : =  $\mathbb{E}_{(s_t, a_t) \sim \mu^{\pi}, g \sim \mathcal{G}}[\sum_t \gamma^t r(s, a, g)]$  [15]. A prototype example of GCRL can be maze locomotion tasks, where the learning goals are manifested as desired locations in the maze [16].

**Episodic vs. Non-episodic Reinforcement Learning:** In episodic RL, the agent performs in finite episodes of length H, and will be reset to an initial state  $\in \mu_0$  upon the episode ends [1]. Whereas in non-episodic RL, the learning agent continuously interacts with the MDP without any state reset [17]. To encompass the episodic concept in infinite MDPs, episodic RL tasks usually assume the existence of a set of *absorbing states*  $S_0$ , which indicates the termination of episodic tasks [18, 19], and any action taken upon an absorbing state will only transit to itself with zero rewards.

## **2.2 Reinforcement Learning Algorithms**

There are two major methods to conduct RL: *Model-Based* and *Model-Free*. In model-based RL, a learned or provided model of the MDP is used for policy learning. In model-free RL, optimal policy is learned without modeling the transition dynamics or reward functions. In this section, we start introducing RL techniques from a *model-free* perspective, due to its relatively simplicity, which also provides foundations for many model-based methods.

**Prediction and Control:** an RL problem can be disassembled into two subtasks: prediction and control [1]. In the prediction phase, the quality of the current policy is

being evaluated. In the *control* phase or the *policy improvement* phase, the learning policy is adjusted based on evaluation results from the *prediction* step. Policies can be improved by iterating through these two steps, known as policy iteration.

For model-free policy iterations, the target policy is optimized without requiring knowledge of the MDP transition dynamics. Traditional model-free RL includes *Monte-Carlo* methods, which estimates the value of each state using *samples of episodes* starting from that state. Monte-Carlo methods can be *on-policy* if the samples are collected by following the target policy, or off-policy if the episodic samples are collected by following a behavior policy that is different from the target policy.

*Temporal Difference (TD) Learning* is an alternative to Monte-Carlo for solving the prediction problem. The key idea behind TD-learning is to learn the state quality function by *bootstrapping*. It can also be extended to solve the *control* problem so that both value function and policy can get improved simultaneously. Examples of on-policy TD-learning algorithms include SARSA [20], Expected SARSA [21], Actor-Critic [22], and its deep neural network extension called A3C [23]. The *off-policy* TD-learning approaches include SAC [24] for continuous state-action spaces, and  $Q$ -learning [25] for discrete state-action spaces, along with its variants built on deep-neural networks, such as DQN [26], Double-DQN [26], Rainbow [27], etc. TD-learning approaches focus more on estimating the stateaction value functions.

*Policy Gradient*, on the other hand, is a mechanism that emphasizes on direct optimization of a parameterizable policy. Traditional policy-gradient approaches include REINFORCE [28]. Recent years have witnessed the joint presence of TD-learning and policy-gradient approaches. Representative algorithms along this line include Trust region policy optimization (TRPO) [29], Proximal Policy optimization (PPO) [30], Deterministic policy gradient (DPG) [31] and its extensions such as DDPG [32] and Twin Delayed DDPG [33].

Unlike model-free methods that learn purely from trial-and-error, *Model-Based RL* (MBRL) explicitly learns the transition dynamics or cost functions of the environment. The dynamics model can sometimes be treated as a *black-box* for better sampling-based planning. Representative examples include the Monte-Carlo method dubbed random shooting [34] and its cross-entropy method (CEM) variants [35, 36]. The modeled dynamics can also facilitate learning with data generation [37] and value estimation [38]. For MBRL with *white-box* modeling, the transition models become differentiable and can facilitate planning with direct gradient propogation. Methods along this line include *differential planning* for policy gradient [39] and action sequences search [40], and value gradient methods [41, 42]. One advantage of MBRL is its higher sample efficiency than model-free RL, although it can be challenging for complex domains, where it is usually more difficult to learn the dynamics than learning a policy.

## **2.3 Transfer Learning in the Context of Reinforcement Learning**

**Remark 1.—** Without losing clarify, for the rest of this survey, we refer to MDPs, domains, and tasks equivalently.

**Remark 2.—[Transfer Learning in the Context of RL]** Given a set of *source* domains  $\mathcal{M}_s = \{ \mathcal{M}_s \mid \mathcal{M}_s \in \mathcal{M}_s \}$  and a **target** domain  $\mathcal{M}_t$ , Transfer Learning aims to learn an optimal policy  $\pi^*$  for the target domain, by leveraging exterior information  $\mathcal{I}_s$  from  $\mathcal{M}_s$  as well as interior information  $\mathcal{I}_t$  from  $\mathcal{M}_t$ :

$$
\pi^* = \arg \max_{\pi} \mathbb{E}_{s \sim \mu_0^t, a \sim \pi} [Q_{\mathcal{M}}^{\pi}(s, a)],
$$

where  $\pi = \phi(\mathcal{I}_s \sim \mathcal{M}_s, \mathcal{I}_t \sim \mathcal{M}_t)$ :  $\mathcal{S}^t \to \mathcal{A}^t$  is a policy learned for the target domain  $\mathcal{M}_t$  based on information from both  $\mathcal{I}_t$  and  $\mathcal{I}_s$ .

In the above definition, we use  $\phi(\mathcal{I})$  to denote the learned policy based on information  $\mathcal{I}$ , which is usually approximated with deep neural networks in DRL. For the simplistic case, knowledge can transfer between two agents within the same domain, resulting in  $|\mathcal{M}_s| = 1$ , and  $\mathcal{M}_s = \mathcal{M}_t$ . One can consider regular RL without TL as a special case of the above which is usually approximated with deep neural networks in DRL. For the simplistic case,<br>knowledge can transfer between two agents within the same domain, resulting in  $|\mathcal{M}_s| = 1$ ,<br>and  $\mathcal{M}_s = \mathcal{M}_t$ . One can conside by the target domain, *i.e.*  $\pi = \phi(\mathcal{I}_t)$ .

## **2.4 Related Topics**

In addition to TL, other efforts have been made to benefit RL by leveraging different forms of supervision. In this section, we briefly discuss other techniques that are relevant to TL by analyzing the differences and connections between transfer learning and these relevant techniques, which we hope can further clarify the scope of this survey.

*Continual Learning* is the ability of sequentially learning multiple tasks that are temporally or spatially related, without forgetting the previously acquired knowledge. Continual Learning is a specialized yet more challenging scenario of TL, in that the learned knowledge needs to be transferred along a sequence of dynamically-changing tasks that cannot be foreseen, rather than learning a fixed group of tasks. Hence, different from most TL methods discussed in this survey, the ability of automatic task detection and avoiding catastrophic forgetting is usually indispensable in continual learning [43].

*Hierarchical RL* has been proposed to resolve complex real-world tasks. Different from traditional RL, for hierarchical RL, the action space is grouped into different granularities to form higher-level macro actions. Accordingly, the learning task is also decomposed into hierarchically dependent subgoals. Well-known hierarchical RL frameworks include Feudal learning [44], Options framework[45], Hierarchical Abstract Machines [46], and MAXQ [47]. Given the higher-level abstraction on tasks, actions, and state spaces, hierarchical RL can facilitate knowledge transfer across similar domains.

*Multi-task RL* learns an agent with generalized skills across various tasks, hence it can solve MDPs randomly sampled from a fixed yet unknown distribution [48]. A larger concept of multi-task learning also incorporates multi-task supervised learning and unsupervised learning [49]. Multi-task learning is naturally related to TL, in that the learned skills, typically manifested as representations, need to be effectively shared among domains. Many

TL techniques later discussed in this survey can be readily applied to solve multi-task RL scenarios, such as policy distillation [50], and representation sharing [51]. One notable challenges in multi-task learning is *negative transfer*, which is induced by the irrelevance or conflicting property for learned tasks. Hence, some recent work in multi-task RL focused on a trade-off between sharing and individualizing function modules [52–54].

*Generalization in RL* refers to the ability of learning agents to adapt to *unseen* domains. Generalization is a crucial property for RL to achieve, especially when classical RL assumes identical training and inference MDPs, whereas the real world is constantly changing. Generalization in RL is considered more challenging than in supervised learning due to the non-stationarity of MDPs, where the latter has provided inspirations for the former [55]. *Meta-learning* is an effective direction towards generalization, which also draws close connections to TL. Some TL techniques discussed in this survey are actually designed for meta-RL. However, meta-learning is particularly focused on the learning methods that lead to fast adaptation to unseen domains, whereas TL is a broader concept and covers scenarios where the target environment can be (partially) observable. To tackle unseen tasks in RL, some meta-RL methods focused on training MDPs generation [56] and variations estimation [57]. We refer readers to [58] for a more focused survey on meta RL.

# **3 Analyzing Transfer Learning**

In this section, we discuss TL approaches in RL from different angles. We also use a prototype to illustrate the potential variants residing in knowledge transfer among domains, then summarize important metrics for TL evaluation.

## **3.1 Categorization of Transfer Learning Approaches**

TL approaches can be organized by answering the following key questions:

- **1.** *What knowledge is transferred*: Knowledge from the source domain can take different forms, such as expert experiences [59], the action probability distribution of an expert policy [60], or even a potential function that estimates the quality of demonstrations in the target MDP [61]. The divergence in representations and granularities of knowledge fundamentally influences how TL is performed. The *quality* of the transferred knowledge,  $e.g.$  whether it comes from an oracle [62] or a suboptimal teacher [63] also affects the way TL methods are designed.
- **2.** *What RL frameworks fit the TL approach:* We can rephrase this question into other forms, e.g., is the TL approach policy-agnostic, or only applicable to certain RL backbones, such as the Temporal Difference (TD) methods? Answers to this question are closely related to the representaion of knowledge. For example, transferring knowledge from expert demonstrations are usually policy-agnostic (see Section 5.2), while policy distillation, to be discussed in Section 5.3, may not be suitable for DQN backbone which does not explicitly learn a policy function.

- **3.** *What is the difference between the source and the target domain*: Some TL approaches fit where the source domain  $\mathcal{M}_s$  and the target domain  $\mathcal{M}_t$  are equivalent, whereas others are designed to transfer knowledge between different domains. For example, in video gaming tasks where observations are RGB pixels,  $\mathcal{M}_s$  and  $\mathcal{M}_t$  may share the same action space ( $\mathcal{A}$ ) but differs in their observation spaces ( $\mathcal{S}$ ). For goal-conditioned RL [64], the two domains may equivalent, whereas others are designed to transfer domains. For example, in video gaming tasks whe pixels,  $\mathcal{M}_s$  and  $\mathcal{M}_t$  may share the same action space observation spaces ( $\mathcal{S}$ ). For goal-conditioned RL [d differ only by the reward distribution:  $\mathcal{R} \neq \mathcal{R}$ .
- **4.** *What information is available in the target domain:* While knowledge from source domains is usually accessible, it can be prohibitive to sample from the target domain, or the reward signal can be sparse or delayed. Examples include adapting an auto-driving agent pre-trained in simulated platforms to real environments [65], The accessibility of information in the target domain can affect the way that TL approaches are designed.
- **5.** *How sample-efficient the TL approach is:* TL enables the RL with better initial performance, hence usually requires fewer interactions compared with learning from scratch. Based on the sampling cost, we can categorize TL approaches into the following classes: (i) Zero-shot transfer, which learns an agent that is directly applicable to the target domain without requiring any training interactions; (ii) Few-shot transfer, which only requires a few samples (interactions) from the target domain; (iii) Sample-efficient transfer, where an agent can benefit by TL to be more sample efficient compared to normal RL.

## **3.2 Case Analysis of Transfer Learning in the context of Reinforcement Learning**

We now use  $\textit{HalfCheckah}^1$  as a working example to illustrate how TL can occur between the source and the target domain. *HalfCheetah* is a standard DRL benchmark for solving physical locomotion tasks, in which the objective is to train a two-leg agent to run fast without losing control of itself.

**3.2.1 Potential Domain Differences:** During TL, the differences between the source and target domain may reside in any component of an MDP:

- S (State-space): domains can be made different by extending or constraining the available positions for the *HalfCheetah* agent to move.
- **•** A (Action-space) can be adjusted by changing the range of available torques for the thigh, shin, or foot of the agent.
- **•** ℛ (Reward function): a domain can be simplified by using only the distance moved forward as rewards or be perplexed by using the scale of accelerated velocity in each direction as extra penalty costs.
- $\mathcal{T}$  (Transition dynamics): two domains can differ by following different physical rules, leading to different transition probabilities given the same state-action pairs.

<sup>1.</sup> <https://gym.openai.com/envs/HalfCheetah-v2/>

IEEE Trans Pattern Anal Mach Intell. Author manuscript; available in PMC 2024 April 15.

**•** τ (Trajectories): the source and target domains may allow a different number of steps for the agent to move before a task is done.

**3.2.2 Transferrable Knowledge:** Without losing generality, we list below some transferrable knowledge assuming that the source and target domains are variants of HalfCheetah:

- **•** *Demonstrated trajectories*: the target agent can learn from the behavior of a pre-trained expert, e.g. a sequence of running demonstrations.
- **•** *Model dynamics*: the RL agent may access a model of the physical dynamics for the source domain that is also partly applicable to the target domain. It can perform dynamic programming based on the physical rules, running fast without losing its control due to the accelerated velocity.
- *Teacher policies*: an expert policy may be consulted by the learning agent, which outputs the probability of taking different actions upon a given state example.
- *Teacher value functions:* besides teacher policy, the learning agent may also refer to the value function derived by a teacher policy, which implies the quality of state-actions from the teacher's point of view.

## **3.3 Evaluation metrics**

In this section, we present some representative metrics for evaluating TL approaches, which have also been partly summarized in prior work [11, 66]:

- *Jumpstart performance( jp): the initial performance (returns) of the agent.*
- Asymptotic performance (ap): the ultimate performance (returns) of the agent.
- **•** Accumulated rewards (ar): the area under the learning curve of the agent.
- *Transfer ratio (tr)*: the ratio between *asymptotic performance* of the agent with TL and asymptotic performance of the agent without TL.
- Time to threshold (tt): the learning time (iterations) needed for the target agent to reach certain performance threshold.
- Performance with fixed training epochs (pe): the performance achieved by the target agent after a specific number of training iterations.
- Performance sensitivity (ps): the variance in returns using different hyperparameter settings.

The above criteria mainly focus on the *learning process* of the target agent. In addition, we introduce the following metrics from the perspective of *transferred knowledge*, which, although commensurately important for evaluation, have not been explicitly discussed by prior art:

- **•** Necessary knowledge amount (nka): the necessary amount of the knowledge required for TL in order to achieve certain performance thresholds. Examples along this line include the number of designed source tasks [67], the number of expert policies, or the number of demonstrated interactions [68] required to enable knowledge transfer.
- **•** Necessary knowledge quality (nkq): the guaranteed quality of the knowledge required to enable effective TL. This metric helps in answering questions such as (i) Does the TL approach rely on near-oracle knowledge, such as expert demonstrations/policies [69], or (ii) is the TL technique feasible even given suboptimal knowledge [63]?

TL approaches differ in various perspectives, including the forms of transferred knowledge, the RL frameworks utilized to enable such transfer, and the gaps between the source and the target domain. It maybe biased to evaluate TL from just one viewpoint. We believe that explicating these TL related metrics helps in designing more generalizable and efficient TL approaches.

In general, most of the abovementioned metrics can be considered as evaluating two abilities of a TL approach: the *mastery* and *generalization*. Mastery refers to how well the learned agent can ultimately perform in the target domain, while *generalization* refers to the ability of the learning agent to quickly adapt to the target domain.

# **4 Related Work**

There are prior efforts in summarizing TL research in RL. One of the earliest literatures is [11]. Their main categorization is from the perspective of problem setting, in which the TL scenarios may vary in the number of domains involved, and the difference of state-action space among domains. Similar categorization is adopted by [12], with more refined analysis dimensions including the objective of TL. As pioneer surveys for TL in RL, neither [11] nor [12] covered recent research over the last decade. For instance, [11] emphasized on different task-mapping methods, which are more suitable for domains with tabular or mild state-action space dimensions.

There are other surveys focused on specific subtopics that interplay between RL and TL. For instance, [70] consolidated sim-to-real TL methods. They explored work that is more tailored for robotics domains, including domain generalization and zero-shot transfer, which is a favored application field of DRL as we discussed in Sec 6. [71] conducted extensive database search and summarized benchmarks for evaluating TL algorithms in RL. [72] surveyed recent progress in multi-task RL. They partially shared research focus with us by studying certain TL oriented solutions towards multitask RL, such as learning shared representations, pathNets, etc. We surveyed TL for RL with a broader spectrum in methodologies, applications, evaluations, which naturally draws connections to the above literatures.

## **5 Transfer Learning Approaches Deep Dive**

In this section, we elaborate on various TL approaches and organize them into different sub-topics, mostly by answering the question of "what knowledge is transferred". For each type of TL approach, we analyze them by following the other criteria mentioned in Section 3 and and summarize the key evaluation metrics that are applicable to the discussed work. Figure 1 presents an overview of different TL approaches discussed in this survey.

### **5.1 Reward Shaping**

We start by introducing the *Reward Shaping* approach, as it is applicable to most RL backbones and also largely overlaps with the other TL approaches discussed later. Reward Shaping (RS) is a technique that leverages the exterior knowledge to reconstruct the reward distribution of the target domain to guide the agent's policy learning. More specifically, in addition to the environment reward signals, RS learns a reward-shaping function  $\mathcal{F}: \mathcal{S} \times \mathcal{S} \times \mathcal{A} \rightarrow \mathbb{R}$  to render auxiliary rewards, provided that the additional rewards contain external knowledge to guide the agent for better action selections. Intuitively, an RS strategy will assign higher rewards to more beneficial state-actions to navigate the agent to desired trajectories. As a result, the agent will learn its policy using the newly shaped rewards  $\mathcal{R}'$ :  $\mathcal{R}' = \mathcal{R} + \mathcal{F}$ , which means that RS has altered the target domain with a different reward function:

$$
\mathcal{M} = (\mathcal{S}, \mathcal{A}, \mathcal{T}, \gamma, \mathcal{R})) \to \mathcal{M}' = (\mathcal{S}, \mathcal{A}, \mathcal{T}, \gamma, \mathcal{R}').
$$
<sup>(1)</sup>

Along the line of RS, Potential based Reward Shaping (PBRS) is one of the most classical approaches. [61] proposed PBRS to form a shaping function  $F$  as the difference between two Along the line of RS, *Pote* approaches. [61] proposed *potential functions* ( $\Phi$ ( ⋅ )):

$$
F(s, a, s') = \gamma \Phi(s') - \Phi(s),
$$

(2)

where the potential function  $\Phi(\cdot)$  comes from the knowledge of expertise and evaluates the quality of a given state. It has been proved that, without further restrictions on the underlying MDP or the shaping function  $F$ , PBRS is sufficient and necessary to preserve the policy invariance. Moreover, the optimal Q-function in the original and transformed MDP are related by the potential function:  $Q^*_{\mathcal{M}}(s, a) = Q^*_{\mathcal{M}}(s, a) - \Phi(s)$ , which draws a connection between potential based reward-shaping and advantagebased learning approaches [73].

The idea of PBRS was extended to [74], which formulated the potential as a function over both the state and the action spaces. This approach is called *Potential Based state-action* Advice (PBA). The potential function  $\Phi(s, a)$  therefore evaluates how beneficial an action a is to take from state s:

$$
F(s, a, s', a') = \gamma \Phi(s', a') - \Phi(s, a).
$$

PBA requires on-policy learning and can be *sample-costly*, as in Equation (3),  $a'$  is the action to take upon state s is transitioning to s′ by following the learning policy.

Traditional RS approaches assumed a static potential function, until [75] proposed a Dynamic Potential Based (DPB) approach which makes the potential a function of both states and time:  $F(s, t, s', t') = \gamma \Phi(s', t') - \Phi(s, t)$ . They proved that this dynamic approach can still maintain policy invariance:  $Q^*_{\mathcal{M}}(s, a) = Q^*_{\mathcal{M}}(s, a) - \Phi(s, t)$ , where t is the current tilmestep. [76] later introduced a way to incorporate any prior knowledge into a dynamic potential function structure, which is called *Dynamic Value Function Advice (DPBA)*. The rationale behind DPBA is that, given any extra reward function  $R^+$  from prior knowledge, in order to add this extra reward to the original reward function, the potential function should satisfy:  $\gamma \Phi(s', a') - \Phi(s, a) = F(s, a) = R^+(s, a).$ 

If  $\Phi$  is not static but learned as an extra state-action *Value* function overtime, then the Bellman equation for  $\Phi$  is :  $\Phi^{\pi}(s, a) = r^{\Phi}(s, a) + \gamma \Phi(s', a')$ . The shaping rewards  $F(s, a)$  is therefore the negation of  $r^{\Phi}(s, a)$ :

$$
F(s, a) = \gamma \Phi(s', a') - \Phi(s, a) = -r \Phi(s, a).
$$
\n(4)

This leads to the approach of using the negation of  $R^+$  as the immediate reward to train an extra state-action *Value* function  $\Phi$  and the policy simultaneously. Accordingly, the dynamic potential function  $F$  becomes:

$$
F_t(s, a) = \gamma \Phi_{t+1}(s', a') - \Phi_t(s, a).
$$
 (5)

The advantage of *DPBA* is that it provides a framework to allow arbitrary knowledge to be shaped as auxiliary rewards.

Research along this line mainly focus on designing different shaping functions  $F(s, a)$ , while not much work has tackled the question of what knowledge can be used to derive this potential function. One work by [77] proposed to use RS to transfer an expert policy from the source domain  $\mathcal{M}_s$  to the target domain  $\mathcal{M}_t$ . This approach assumed the existence of two mapping functions  $M_s$  and  $M_A$  that can transform the state and action from the source to the target domain. Another work used demonstrated state-action samples from an expert policy to shape rewards [78]. Learning the augmented reward involves learning a discriminator to distinguish samples generated by an expert policy from samples generated by the target policy. The loss of the discriminator is applied to shape rewards to incentivize the learning agent to mimic the expert behavior. This work combines two TL approaches: RS and Learning from Demonstrations, the latter of which will be elaborated in Section 5.2.

The above-mentioned RS approaches are summarized in Table 1. They follow the potential based RS principle that has been developed systematically: from the classical PBRS which is built on a *static* potential shaping function of *states*, to *PBA* which generates the potential as a function of both states and actions, and DPB which learns a dynamic potential function of *states* and *time*, to the most recent *DPBA*, which involves a dynamic potential function of states and actions to be learned as an extra state-action Value function in parallel with the environment Value function. As an effective TL paradigm, RS has been widely applied to fields including robot training [79], spoken dialogue systems [80], and question answering [81]. It provides a feasible framework for transferring knowledge as the augmented reward and is generally applicable to various RL algorithms. RS has also been applied to *multi-agent* RL [82] and *model-based* RL [83]. Principled integration of RS with other TL approaches, such as *Learning from demonstrations* (Section 5.2) and *Policy* Transfer (Section 5.3) will be an intriguing question for ongoing research.

Note that RS approaches discussed so far are built upon a consensus that the source information for shaping the reward comes *externally*, which coincides with the notion of knowledge transfer. Some RS work also tackles the scenario where the shaped reward comes intrinsically. For instance, Belief Reward Shaping was proposed by [84], which utilizes a Bayesian reward shaping framework to generate the potential value that decays with experience, where the potential value comes from the critic itself.

### **5.2 Learning from Demonstrations**

Learning from Demonstrations (LfD) is a technique to assist RL by utilizing external demonstrations for more efficient exploration. The demonstrations may come from different sources with varying qualities. Research along this line usually address a scenario where the source and the target MDPs are the same:  $\mathcal{M}_s = \mathcal{M}_t$ , although there has been work that learns from demonstrations generated in a different domain [85, 86].

Depending on *when* the demonstrations are used for knowledge transfer, approaches can be organized into *offline* and *online* methods. For *offline* approaches, demonstrations are either used for pre-training RL components, or for *offline* RL [87, 88]. When leveraging demonstrations for pre-training, RL components such as the value function  $V(s)$  [89], the policy  $\pi$  [90], or the model of transition dynamics [91], can be initialized by learning from demonstrations. For the online approach, demonstrations are directly used to guide agent actions for efficient explorations [92]. Most work discussed in this section follows the online transfer paradigm or combines offline pre-training with online RL [93].

Work along this line can also be categorized depending on *what* RL frameworks are compatible: some adopts the policy-iteration framework [59, 94, 95], some follow a Q-learning framework [92, 96], while recent work usually follows the policy-gradient framework [63, 78, 93, 97]. Demonstrations have been leveraged in the policy iterations framework by [98]. Later, [94] introduced the Direct Policy Iteration with Demonstrations (DPID) algorithm. This approach samples complete demonstrated rollouts  $D<sub>E</sub>$  from an expert policy  $\pi_E$ , in combination with the self-generated rollouts  $D_{\pi}$  gathered from the learning agent.  $D_{\pi} \cup D_{E}$  are used to learn a Monte-Carlo estimation of the Q-value:  $\hat{Q}$ , from which

a learning policy can be derived greedily:  $\pi(s) = \arg \max_{a \in \mathcal{A}} \hat{Q}(s, a)$ . This policy  $\pi$  is further

regularized by a loss function  $\mathcal{L}(s, \pi_E)$  to minimize its discrepancy from the expert policy decision.

Another example is the Approximate Policy Iteration with Demonstration (APID) algorithm, which was proposed by [59] and extended by [95]. Different from *DPID* where both  $D<sub>E</sub>$  and  $D_{\tau}$  are used for value estimation, the *APID* algorithm solely applies  $D_{\tau}$  to approximate on the Q function. Expert demonstrations  $D<sub>E</sub>$  are used to learn the value function, which, given any state  $s_i$ , renders expert actions  $\pi_E(s_i)$  with higher Q-value margins compared with other actions that are not shown in  $D<sub>E</sub>$ :

$$
Q(s_i, \pi_E(s_i)) - \max_{a \in \mathcal{A} \setminus \pi_E(s_i)} Q(s_i, a) \ge 1 - \xi_i.
$$
\n
$$
(6)
$$

The term  $\xi$  is used to account for the case of imperfect demonstrations. [95] further extended the work of APID with a different evaluation loss:

$$
\mathscr{L}^{\pi} = \mathbb{E}_{(s,a)\sim D_{\pi}} \parallel \mathscr{T}^*Q(s,a) - Q(s,a) \parallel ,
$$
\n(7)

where  $\mathcal{T}^*Q(s, a) = R(s, a) + \gamma \mathbb{E}_{s \sim p(. \mid s, a)}[\max_{a'} Q(s', a')].$  Their work theoretically converges to the optimal Q-function compared with APID, as  $\mathscr{L}_n$  is minimizing the optimal Bellman residual instead of the empirical norm.

In addition to policy iteration, the following two approaches integrate demonstration data into the TD-learning framework, such as Q-learning. Specifically, [92] proposed the DQfD algorithm, which maintains two separate replay buffers to store demonstrated data and self-generated data, respectively, so that expert demonstrations can always be sampled with a certain probability. Their method leverages the refined priority replay mechanism [99] where the probability of sampling a transition *i* is based on its priority  $p_i$  with a temperature parameter  $\alpha$ :  $P(i) = \frac{p_i^{a_i}}{\sum_{i=1}^{n_i}}$  $\frac{p_i}{\sum_k p_k^{\alpha}}$ . Another algorithm named *LfDS* was proposed by

[96], which draws a close connection to *reward shaping* (Section 5.1). *LfDS* builds the potential value of a state-action pair as the highest similarity between the given pair and the expert demonstrations. This augmented reward assigns more credits to state-actions that are more similar to expert demonstrations, encouraging the agent for expert-like behavior.

Besides Q-learning, recent work has integrated LfD into *policy gradient* [63, 69, 78, 93, 97]. A representative work along this line is Generative Adversarial Imitation Learning (GAIL) [69]. GAIL introduced the notion of *occupancy measure d<sub>n</sub>*, which is the stationary stateaction distributions derived from a policy  $\pi$ . Based on this notion, a new reward function is designed such that maximizing the accumulated new rewards encourages minimizing the distribution divergence between the *occupancy measure* of the current policy  $\pi$  and

(8)

the expert policy  $\pi_E$ . Specifically, the new reward is learned by adversarial training [62]: a discriminator D is learned to distinguish interactions sampled from the current policy  $\pi$  and the expert policy  $\pi_E$ :

$$
J_D = \max_{D:\,\mathcal{S}\times\mathcal{A}\to(0,1)} \mathbb{E}_{d_{\pi}}log[1 - D(s,a)] + \mathbb{E}_{d_E}log[D(s,a)]
$$

Since  $\pi_E$  is unknown, its state-action distribution  $d_E$  is estimated based on the given expert demonstrations  $D<sub>E</sub>$ . The output of the discriminator is used as new rewards to encourage distribution matching, with  $r'(s, a) = -\log(1 - D(s, a))$ . The RL process is naturally altered to perform distribution matching by min-max optimization:

$$
\max_{\pi} \min_{D} J(\pi, D) := \mathbb{E}_{d_{\pi}} \log[1 - D(s, a)] + \mathbb{E}_{d_{E}} \log[D(s, a)].
$$

The philosophy in GAIL of using expert demonstrations for distribution matching has inspired other LfD algorithms. For example, [97] extended GAIL with an algorithm called Policy Optimization from Demonstrations (POfD), which combines the discriminator reward with the environment reward:

$$
\max_{\theta} = \mathbb{E}_{d_{\pi}}[r(s, a)] - \lambda D_{JS}[d_{\pi} \parallel d_{E}].
$$
\n(9)

Both GAIL and POfD are under an on-policy RL framework. To further improve the sample efficiency of TL, some *off-policy* algorithms have been proposed, such as *DDPGfD* [78] which is built upon the *DDPG* framework. *DDPGfD* shares a similar idea as *DQfD* in that they both use a second replay buffer for storing demonstrated data, and each demonstrated sample holds a sampling priority  $p_i$ . For a demonstrated sample, its priority  $p_i$  is augmented with If all the solution of TL, some *on-poncy* algorithms have been proposed, such as *DDPGID* (18) which is built upon the *DDPG* framework. *DDPGfD* shares a similar idea as *DQfD* in that they both use a second replay buff demonstrations:

$$
p_i = \delta_i^2 + \lambda \parallel \nabla_a Q(s_i, a_i \mid \theta^Q) \parallel^2 + \epsilon + \epsilon_D,
$$

where  $\delta_i$  is the TD-residual for transition,  $\|\nabla_a Q(s_i, a_i \mid \theta^Q)\|^2$  is the loss applied to the actor, and  $\epsilon$  is a small positive constant to ensure all transitions are sampled with some probability. Another work also adopted the DDPG framework to learn from demonstrations [93]. Their approach differs from *DDPGfD* in that its objective function is augmented with a Behavior Cloning Loss to encourage imitating on provided demonstrations:  $\mathscr{L}_{BC} = \sum_{i=1}^{|D_E|} || \pi(s_i || \theta_{\pi}) - a_i ||^2.$ 

To further address the issue of *suboptimal* demonstrations, in [93] the form of *Behavior* Cloning Loss is altered based on the critic output, so that only demonstration actions with higher Q values will lead to the loss penalty:

$$
\mathcal{L}_{BC} = \sum_{i=1}^{|D_E|} \|\pi(s_i \mid \theta_{\pi}) - a_i\|^2 \mathbb{1}[Q(s_i, a_i) > Q(s_i, \pi(s_i))].
$$
\n(10)

There are several challenges faced by *LfD*, one of which is the *imperfect demonstrations*. Previous approaches usually presume near-oracle demonstrations. Towards tackling suboptimal demonstrations, [59] leveraged the hinge-loss function to allow occasional violations of the property that  $Q(s_i, \pi_E(s_i))$  – max  $a \in \mathcal{A} \setminus \pi_E(s_i)$  $Q(s_i, a) \geq 1$ . Some other work uses

regularized objective to alleviate overfitting on biased data [92, 99]. A different strategy is to leverage those suboptimal demonstrations only to boost the initial learning stage. For instance, [63] proposed Self-Adaptive Imitation Learning (SAIL), which learns from suboptimal demonstrations using generative adversarial training while gradually selecting self-generated trajectories with high qualities to replace less superior demonstrations.

Another challenge faced by *LfD* is *covariate drift* ([100]): demonstrations may be provided in limited numbers, which results in the learning agent lacking guidance on states that are unseen in the demonstration dataset. This challenge is aggravated in MDPs with sparse reward feedbacks, as the learning agent cannot obtain much supervision information from the environment either. Current efforts to address this challenge include encouraging explorations by using an entropy-regularized objective [101], decaying the effects of demonstration guidance by softening its regularization on policy learning over time [102], and introducing *disagreement regularizations* by training an ensemble of policies based on the given demonstrations, where the variance among policies serves as a negative reward function [103].

We summarize the above-discussed approaches in Table 2. In general, demonstration data can help in both offline pretraining for better initialization and online RL for efficient exploration. During the RL phase, demonstration data can be used together with self-generated data to encourage expert-like behaviors (*DDPGfD*, *DOFD*), to shape value functions (APID), or to guide the policy update in the form of an auxiliary objective function (PID,GAIL, POfD). To validate the algorithm robustness given different knowledge resources, most LfD methods are evaluated using metrics that either indicate the performance under *limited* demonstrations (*nka*) or *suboptimal* demonstrations (*nka*). The integration of LfD with off-policy RL backbone makes it natural to adopt pe metrics for evaluating how learning efficiency can be further improved by knowledge transfer. Developing more general *LfD* approaches that are agnostic to RL frameworks and can learn from sub-optimal or limited demonstrations would be the ongoing focus for this research domain.

### **5.3 Policy Transfer**

Policy transfer is a TL approach where the external knowledge takes the form of pre-trained policies from one or multiple source domains. Work discussed in this section is built upon a many-to-one problem setting, described as below:

**Policy Transfer.**—A set of teacher policies  $\pi_{E_1}, \pi_{E_2}, ..., \pi_{E_K}$  are trained on a set of source domains  $\mathcal{M}_1, \mathcal{M}_2, ..., \mathcal{M}_K$ , respectively. A student policy  $\pi$  is learned for a target domain by domains  $M_1, M_2, ..., M_k$ , respectively. A st<br>leveraging knowledge from  $\{\pi_{E_i}\}_{i=1}^K$ . leveraging knowledge from  $\{\pi_{E_i}\}_{i=1}^K$ .

For the *one-to-one* scenario with only one teacher policy, one can consider it as a special case of the above with  $K = 1$ . Next, we categorize recent work of policy transfer into two techniques: policy distillation and policy reuse.

**5.3.1 Transfer Learning via Policy Distillation—The idea of knowledge distillation** has been applied to the field of RL to enable *policy distillation*. Knowledge distillation was first proposed by [104] as an approach of knowledge ensemble from multiple teacher models into a single student model. Conventional policy distillation approaches transfer the teacher policy following a supervised learning paradigm [105, 106]. Specifically, a student policy is learned by minimizing the divergence of action distributions between the teacher policy  $\pi_E$ and student policy  $\pi_{\theta}$ , which is denoted as  $\mathcal{H}^{\times}(\pi_E(\tau_i) | \pi_{\theta}(\tau_i))$ :

$$
\min_{\theta} \mathbb{E}_{\tau \sim \pi_E} \left[ \sum_{t=1}^{\left| \tau \right|} \nabla_{\theta} \mathcal{H}^{\times}(\pi_E(\tau_t) \mid \pi_{\theta}(\tau_t)) \right].
$$
\n(11)

The above expectation is taken over trajectories sampled from the teacher policy  $\pi_E$ , hence this approach is called *teacher distillation*. One example along this line is  $[105]$ , in which N teacher policies are learned for N source tasks separately, and each teacher yields a dataset  $D^{E} = \{s_i, q_i\}_{i=0}^{N}$  consisting of observations s and vectors of the corresponding Q-values q, such that  $q_i = [Q(s_i, a_1), Q(s_i, a_2), \dots | a_i \in \mathcal{A}]$ . Teacher policies are further distilled to a single student  $\pi_{\theta}$  by minimizing the KL-Divergence between each teacher  $\pi_{E_i}(a \mid s)$  and the student  $\pi_{\theta}$ , approximated using the dataset  $D^E$ : min<sub> $\theta \mathcal{D}_{KL}(\pi^E | \pi_{\theta}) \approx \sum_{i=1}^{|D^E|} {\text{softmax}(\frac{q_i^E}{\tau_{i}})}$ </sub>  $\left(\frac{q_i^E}{\tau}\right)$ ln $\left(\frac{\text{softmax}(q_i^E)}{\text{softmax}(q_i^E)}\right)$  $\frac{\text{softmax}(\boldsymbol{q}_i)}{\text{softmax}(\boldsymbol{q}_i^{\theta})}$ .

Another policy distillation approach is student distillation [51, 60], which is resemblant to teacher distillation except that during the optimization step, the objective expectation is taken over trajectories sampled from the student policy instead of the teacher policy, *i.e.*:  $\min_{\theta} \mathbb{E}_{\tau \sim \pi_{\theta}} \Big[ \sum_{t=1}^{|t|} \nabla_{\theta} \mathcal{H}^{\times}(\pi_E(\tau_t) \mid \pi_{\theta}(\tau_t)) \Big]$ . [60] summarized related work on both kinds of distillation approaches. Although it is feasible to combine both distillation approaches [100], we observe that more recent work focuses on student distillation, which empirically shows better exploration ability compared to teacher distillation, especially when the teacher policies are deterministic.

Taking an alternative perspective, there are two approaches of policy distillation: (1) minimizing the cross-entropy between the teacher and student policy distributions over actions [51, 107]; and (2) maximizing the probability that the teacher policy will visit trajectories generated by the student, i.e.  $\max_{\theta} P(\tau \sim \pi_E | \tau \sim \pi_{\theta})$  [50, 108]. One example of approach  $(1)$  is the *Actor-mimic* algorithm [51]. This algorithm distills the knowledge of expert agents into the student by minimizing the cross entropy between the student policy  $\pi_{\theta}$  and each teacher policy  $\pi_{E_i}$  over actions:  $\mathscr{L}^i(\theta) = \sum_{a \in \mathscr{A}_{E_i}} \pi_{E_i}(a \mid s) \log_{\pi_{\theta}}(a \mid s)$ , where each teacher agent is learned using a DQN framework. The teacher policy is therefore derived from the Boltzmann distributions over the  $O$ -function output:

$$
\pi_{E_i}(a \mid s) = \frac{e^{\tau - 1} Q_{E_i}(s, a)}{\sum_{a' \in \mathcal{A}_{E_i}} e^{\tau - 1} Q_{E_i}(s, a')}
$$
 An instantiation of approach (2) is the *Distral* algorithm

[50]. which learns a *centroid* policy  $\pi_{\theta}$  that is derived from K teacher policies. The knowledge in each teacher  $\pi_{E_i}$  is distilled to the centroid and get transferred to the student, while both the transition dynamics  $\mathcal{T}_i$  and reward distributions  $\mathcal{R}_i$  for source domain  $\mathcal{M}_i$  are heterogeneous. The student policy is learned by maximizing a multi-task learning objective  $\max_{\theta} \sum_{i=1}^{K} J(\pi_{\theta}, \pi_{E_i})$ , where

$$
J(\pi_{\theta}, \pi_{E_i}) = \sum_{t} \mathbb{E}_{(s_t, a_t) \sim \pi_{\theta}} \Biggl[ \sum_{t \geq 0} \gamma^t(r_i(a_t, s_t) + \frac{\alpha}{\beta} \log \pi_{\theta}(a_t \mid s_t)) - \frac{1}{\beta} \log(\pi_{E_i}(a_t \mid s_t))) \Biggr],
$$

in which both log  $\pi_{\theta}(a_t | s_t)$  and  $\pi_{\theta}$  are used as augmented rewards. Therefore, the above approach also draws a close connection to *Reward Shaping* (Section 5.1). In effect, the log  $\pi_{\theta}(a_{t} | s_{t})$  term guides the learning policy  $\pi_{\theta}$  to yield actions that are more likely to be generated by the teacher policy, whereas the entropy term  $-\log(\pi_{E_i}(a_t \mid s_i))$  encourages exploration. A similar approach was proposed by [107] which only uses the cross-entropy between teacher and student policy  $\lambda \mathcal{H}(\pi_E(a_t | s_t) || \pi_{\theta}(a_t | s_t))$  to reshape rewards. Moreover, they adopted a dynamically fading coefficient to alleviate the effect of the augmented reward so that the student policy becomes independent of the teachers after certain optimization iterations.

**5.3.2 Transfer Learning via Policy Reuse—**Policy reuse directly reuses policies from source tasks to build the target policy. The notion of policy reuse was proposed by [109], which directly learns the target policy as a weighted combination of different source-domain policies, and the probability for each source domain policy to be used is related to its expected performance gain in the target domain:  $P(\pi_{E_i}) = \frac{\exp (tW_i)}{\sum_{k=1}^{K} x_k \cdot n}$  $\frac{\exp(tW_i)}{\sum_{j=0}^{K} \exp(tW_j)}$ , where t

is a dynamic temperature parameter that increases over time. Under a Q-learning framework, the Q-function of the target policy is learned in an iterative scheme: during every learning episode,  $W_i$  is evaluated for each expert policy  $\pi_{E_i}$ , and  $W_0$  is obtained for the learning policy, from which a reuse probability  $P$  is derived. Next, a behavior policy is sampled from this probability P. After each training episode, both  $W_i$  and the temperature t for calculating the reuse probability is updated accordingly. One limitation of this approach is that the  $W_i$ , i.e.

the expected return of each expert policy on the target task, needs to be evaluated frequently. This work was implemented in a tabular case, leaving the scalability issue unresolved. More recent work by [110] extended the *policy improvement* theorem [111] from one to multiple policies, which is named as Generalized Policy Improvement. We refer its main theorem as follows:

**Theorem.:** [Generalized Policy Improvement (GPI)] Let  $\{\pi_i\}_{i=1}^n$  be n<sub>1</sub><br>he their approximated action-value functions  $s t \cdot |O^{\pi_i}(s, a) - \hat{O}^{\pi_i}(s, a)|$  $n h_0$ be n policies and let  ${\{\hat{Q}^{\pi_i}\}}_{i=1}^n$ <br>s a)  $\leq e \forall s \in \mathcal{S}$  a  $\in \mathcal{A}$  and n<sub>n</sub> be their approximated action-value functions, s.t:  $|Q^{\pi_i}(s, a) - \hat{Q}^{\pi_i}(s, a)| \le \epsilon \forall s \in \mathcal{S}, a \in \mathcal{A}$ , and  $i \in [n]$ . Define  $\pi(s) = \arg \max_{a} \max_{i} \hat{Q}^{\pi_i}(s, a)$ , then:  $Q^{\pi}(s, a) \ge \max_{i} Q^{\pi_i}(s, a) - \frac{2}{1 - \gamma} \epsilon$ ,  $\forall s \in \mathcal{S}, a \in \mathcal{A}$ 

Based on this theorem, a policy improvement approach can be naturally derived by greedily choosing the action which renders the highest  $Q$ -value among all policies for a given state. Another work along this line is [110], in which an expert policy  $\pi_{E_i}$  is also trained on a different source domain  $\mathcal{M}_i$  with reward function  $\mathcal{R}_i$ , so that  $Q^{\pi}_{\mathcal{M}_0}(s, a) \neq Q^{\pi}_{\mathcal{M}_i}(s, a)$ . To efficiently evaluate the Q-functions of different source policies in the target MDP, a disentangled representation  $\psi(s, a)$  over the states and actions is learned using neural networks and is generalized across multiple tasks. Next, a task (reward) mapper  $w_i$  is learned, based on which the Q-function can be derived:  $Q_i^r(s, a) = \psi(s, a)^T w_i$ . [110] proved that the loss of GPI is bounded by the difference between the source and the target tasks. In addition to policy-reuse, their approach involves learning a shared representation  $\psi(s, a)$ , which is also a form of transferred knowledge and will be elaborated more in Section 5.5.2.

We summarize the abovementioned policy transfer approaches in Table 3. In general, policy transfer can be realized by knowledge distillation, which can be either optimized from the student's perspecive (*student distillation*), or from the teacher's perspective (*teacher* distillation) Alternatively, teacher policies can also be directly *reused* to update the target policy. Regarding evaluation, most of the abovementioned work has investigated a multiteacher transfer scenario, hence the *generalization* ability or *robustness* is largely evaluated on metrics such as *performance sensitivity(ps)* (*e.g.* performance given different numbers of teacher policies or source tasks). Performance with fixed epochs (pe) is another commonly shared metric to evaluate how the learned policy can quickly adapt to the target domain. All approaches discussed so far presumed one or multiple expert policies, which are always at the disposal of the learning agent. Open questions along this line include *How to leverage* imperfect policies for knowledge transfer, or How to refer to teacher policies within a budget.

### **5.4 Inter-Task Mapping**

In this section, we review TL approaches that utilize *mapping functions* between the source and the target domains to assist knowledge transfer. Research in this domain can be analyzed from two perspectives: (1) which domain does the mapping function apply to, and (2) how is the mapped representation utilized. Most work discussed in this section shares a common assumption as below:

Assumption.—*One-to-one mappings exist between the source domain M*<sub>s</sub> and the target  $domain \mathcal{M}_t$ .

Earlier work along this line requires a given mapping function [66, 112]. One examples is [66] which assumes that each target state (action) has a unique correspondence in the source domain, and two mapping functions  $X_s$ ,  $X_A$  are provided over the state space and the action space, respectively, so that  $X_s(\mathcal{S}^t) \to \mathcal{S}^s$ ,  $X_A(\mathcal{A}^t) \to \mathcal{A}^s$ . Based on  $X_s$  and  $X_A$ , a mapping function over the Q-values  $M(Q_s) \rightarrow Q_t$  can be derived accordingly. Another work is done by [112] which transfers *advice* as the knowledge between two domains. In their settings, the *advice* comes from a human expert who provides the mapping function over the Q-values in the source domain and transfers it to the learning policy for the target domain. This advice encourages the learning agent to prefer certain good actions over others, which equivalently provides a relative ranking of actions in the new task.

More later research tackles the inter-task mapping problem by *learning* a mapping function [113–115]. Most work learns a mapping function over the *state* space or a subset of the state space. In their work, state representations are usually divided into *agent-specific* and task-specific representations, denoted as  $s_{\text{agent}}$  and  $s_{\text{env}}$ , respectively. In [113] and [114], the mapping function is learned on the *agent-specific* sub state, and the mapped representation is applied to reshape the immediate reward. For [113], the invariant feature space mapped from  $s_{agent}$  can be applied across agents who have distinct action space but share some morphological similarity. Specifically, they assume that both agents have been trained on the same *proxy* task, based on which the mapping function is learned. The mapping function is learned using an encoder-decoder structure [116] to largely reserve information about the source domain. For transferring knowledge from the source agent to a new task, the environment reward is augmented with a shaped reward term to encourage the target agent to imitate the source agent on an embedded feature space:

$$
r'(s, \cdot) = \alpha \parallel f(s_{agent}^s; \theta_f) - g(s_{agent}^t; \theta_g) \parallel,
$$

(12)

where  $f(s_{agent}^s)$  is the agent-specific state in the source domain, and  $g(s_{agent}^t)$  is for the target domain.

Another work is [115] which applied the *Unsupervised Manifold Alignment (UMA)* method [117] to automatically learn the state mapping. Their approach requires collecting trajectories from both the source and the target domain to learn such a mapping. While applying policy gradient learning, trajectories from the target domain  $\mathcal{M}_t$  are first mapped back to the source:  $\tau_t \rightarrow \tau_s$ , then an expert policy in the source domain is applied to each initial state of those trajectories to generate near-optimal trajectories  $\widetilde{\tau_s}$ , which are further mapped to the target domain:  $\tilde{\tau}_s \to \tilde{\tau}_t$ . The deviation between  $\tilde{\tau}_t$  and  $\tau_t$  are used as a loss to be minimized in order to improve the target policy. Similar ideas of using UMA for inter-task mapping can also be found in [118] and [119].

In addition to approaches that utilizes mapping over states or actions, [120] proposed to learn an inter-task mapping over the *transition dynamics* space:  $S \times \mathcal{A} \times \mathcal{S}$ . Their work assumes that the source and target domains are different in terms of the transition space dimensionality. Transitions from both the source domain  $\langle s^s, a^s, s^s \rangle$  and the target domain  $s<sup>t</sup>, a<sup>t</sup>, s<sup>'t</sup>$  are mapped to a latent space Z. Given the latent feature representations, a similarity measure can be applied to find a correspondence between the source and target task triplets. Triplet pairs with the highest similarity in this feature space Z are used to learn a mapping function  $\mathcal{X}$ :  $\langle s^t, a^t, s^t \rangle = \mathcal{X}(\langle s^s, a^s, s' \rangle)$ . After the transition mapping, states sampled from the expert policy in the source domain can be leveraged to render beneficial states in the target domain, which assists the target agent learning with a better initialization performance. A similar idea of mapping transition dynamics can be found in [121], which, however, requires a stronger assumption on the similarity of the transition probability and the state representations between the source and the target domains.

As summarized in Table 4, for TL approaches that utilize an inter-task mapping, the mapped knowledge can be (a subset of) the state space [113, 114], the Q-function [66], or (representations of) the state-action-sate transitions [120]. In addition to being directly applicable in the target domain [120], the mapped representation can also be used as an augmented shaping reward [113, 114] or a loss objective [115] in order to guide the agent learning in the target domain. Most inter-task mapping methods tackle domains with moderate state-action space dimensions, such as maze tasks or tabular MDPs, where the goal can be reaching a target state with a minimal number of transitions. Accordingly,  $tt$ has been used to measure TL performance. For tasks with limited and discrete state-action space, evaluation is also conducted with different number of initial states collected in the target domain (nka).

### **5.5 Representation Transfer**

This section review approaches that transfer knowledge in the form of representations learned by deep neural networks. They are built upon the following consensual assumption:

**Assumption. [Existence of Task-Invariance Subspace]—**The state space (S), action space  $(A)$ , or the reward space  $(\mathcal{R})$  can be disentangled into orthogonal subspaces, which are taskinvariant such that knowledge can be transferred between domains on the universal subspace.

We organize recent work along this line into two subtopics: 1) approaches that directly reuse representations from the source domain (Section 5.5.1), and 2) approaches that learn to disentangle the source domain representations into independent sub-feature representations, some of which are on the universal feature space shared by both the source and the target domains (Section 5.5.2).

**5.5.1 Reusing Representations—**A representative work of reusing representations is [122], which proposed the *progressive neural network* structure to enable knowledge transfer across multiple RL tasks in a progressive way. A progressive network is composed of multiple columns, and each column is a policy network for one specific task. It starts with

one single column for training the first task, and then the number of columns increases with the number of new tasks. While training on a new task, neuron weights on the previous columns are frozen, and representations from those frozen tasks are applied to the new column via a collateral connection to assist in learning the new task.

Progressive network comes with a cost of large network structures, as the network grows proportionally with the number of incoming tasks. A later framework called PathNet alleviates this issue by learning a network with a fixed size [123]. PathNet contains pathways, which are subsets of neurons whose weights contain the knowledge of previous tasks and are frozen during training on new tasks. The population of pathway is evolved using a tournament selection genetic algorithm [124].

Another approach of reusing representations for TL is modular networks [52, 53, 125]. For example, [52] proposed to decompose the policy network into a task-specific module and agent-specific module. Specifically, let  $\pi$  be a policy performed by any agent (robot) r over the task  $\mathcal{M}_k$  as a function  $\phi$  over states s, it can be decomposed into two sub-modules  $g_k$  and  $f_r$ , *i.e.*:

 $\pi(s)$ : =  $\phi(s_{env}, s_{agent}) = f_r(g_k(s_{env}), s_{agent}),$ 

where  $f_r$  is the agent-specific module and  $g_k$  is the task-specific module. Their core idea is that the task-specific module can be applied to different agents performing the same task, which serves as a transferred knowledge. Accordingly, the agent-specific module can be applied to different tasks for the same agent.

A model-based approach along this line is [125], which learns a model to map the state observation  $s$  to a latent-representation  $z$ . The transition probability is modeled on the latent space instead of the original state space, *i.e.*  $\hat{z}_{t+1} = f_{\theta}(z_t, a_t)$ , where  $\theta$  is the parameter of the transition model,  $z_t$  is the latent-representation of the state observation, and  $a_t$  is the action accompanying that state. Next, a reward module learns the value function as well as the policy from the latent space z using an actor-critic framework. One potential benefit of this latent representation is that knowledge can be transferred across tasks that have different rewards but share the same transition dynamics.

**5.5.2 Disentangling Representations—**Methods discussed in this section mostly focus on learning a disentangled representation. Specifically, we elaborate on TL approaches that are derived from two techniques: Successor Representation (SR) and Universal Value Function Approximating (UVFA).

*Successor Representations (SR)* is an approach to decouple the state features of a domain from its reward distributions. It enables knowledge transfer across multiple domains:  $\mathcal{M} = \{M_1, M_2, ..., M_K\}$ , so long as the only difference among them is the reward Successor Representation<br>domain from its reward di<br>domains:  $M = \{M_1, M_2, ...$ <br>distributions:  $\Re_i \neq \Re_j$ . SR<br>leverage it as a generalizat distributions:  $\mathcal{R}_i \neq \mathcal{R}_i$ . SR was originally derived from neuroscience, until [126] proposed to leverage it as a generalization mechanism for state representations in the RL domain.

Different from the  $\nu$ -value or  $Q$ -value that describes states as dependent on the reward function, SR features a state based on the *occupancy measure* of its *successor* states. Specifically, SR decomposes the value function of any policy into two independent components,  $\psi$  and  $R: V^{\pi}(s) = \sum_{s'} \psi(s, s') \mathbf{w}(s')$ , where  $\mathbf{w}(s')$  is a reward mapping function that maps states to scalar rewards, and  $\psi$  is the SR which describes any state s as the occupancy measure of the future occurred states when following  $\pi$ , with  $1\{S = s'\} = 1$  as an indicator function:

$$
\psi(s,s') = \mathbb{E}_s \left[ \sum_{i=1}^{\infty} \gamma^{i} - t \mathbb{1}[S_i = s'] \mid S_i = s \right].
$$

The *successor* nature of *SR* makes it learnable using any TD-learning algorithms. Especially, [126] proved the feasibility of learning such representation in a tabular case, in which the state transitions can be described using a matrix.  $SR$  was later extended by [110] from three perspectives: (i) the feature domain of  $SR$  is extended from states to state-action pairs; (ii) deep neural networks are used as function approximators to represent the  $SR\psi^{\pi}(s, a)$  and the reward mapper w; (iii) Generalized policy improvement (GPI) algorithm is introduced to accelerate policy transfer for multi-tasks (Section 5.3.2). These extensions, however, are built upon a stronger assumption about the MDP:

**Assumption. [Linearity of Reward Distributions]—**The reward functions of all tasks can be computed as a linear combination of a fixed set of features:  $r(s, a, s') = \phi(s, a, s')^{\mathsf{T}} w$ , where  $\phi(s, a, s') \in \mathbb{R}^d$ antity of Reward Distributions]— The reward functions of all tasks<br>linear combination of a fixed set of features:  $r(s, a, s') = \phi(s, a, s')^\mathsf{T} w$ ,<br>denotes the latent representation of the state transition, and  $w \in \mathbb{R}^d$  is<br>a is the task-specific reward mapper.

Based on this assumption, SR can be decoupled from the rewards when evaluating the Q-function of any policy  $\pi$  in a task. The advantage of SR is that, when the knowledge of  $\psi^{\pi}(s, a)$  in the source domain  $\mathcal{M}_s$  is observed, one can quickly get the performance evaluation of the same policy in the target domain  $\mathcal{M}_t$  by replacing  $w_s$  with  $\mathbf{w}_t$ :  $Q_{\mathcal{M}_t}^{\pi} = \psi^{\pi}(s, a)\mathbf{w}_t$ . Similar ideas of learning *SR* as a TD-algorithm on a latent representation  $\phi(s, a, s')$  can also be found in [127, 128]. Specifically, the work of [127] was developed based on a weaker assumption about the reward function: Instead of requiring linearlydecoupled rewards, the latent space  $\phi(s, a, s')$  is learned in an encoder-decoder structure to ensure that the information loss is minimized when mapping states to the latent space. This structure, therefore, comes with an extra cost of learning a decoder  $f_d$  to reconstruct the state:  $f_d(\phi(s_t)) \approx s_t$ .

An intriguing question faced by the  $SR$  approach is: Is there a way that evades the linearity assumption about reward functions and still enables learning the SR without extra modular cost? An extended work of SR [67] answered this question affirmatively, which proved that the reward functions does not necessarily have to follow the linear structure, yet at the cost of a looser performance lower-bound while applying the GPI approach for policy improvement. Especially, rather than learning a reward-agnostic latent feature  $\phi(s, a, s') \in \mathbb{R}^d$ 

for multiple tasks, [67] aims to learn a matrix  $\phi(s, a, s') \in \mathbb{R}^{D \times d}$  to interpret the basis functions of the latent space instead, where  $D$  is the number of seen tasks. Assuming  $k$  out of  $D$  tasks are linearly independent, this matrix forms  $k$  basis functions for the latent space. Therefore, for any unseen task  $\mathcal{M}_i$ , its latent features can be built as a linear combination of these basis functions, as well as its reward functions  $r<sub>i</sub>(s, a, s')$ . Based on the idea of basis-functions for a task's latent space, they proposed that  $\phi(s, a, s')$  can be approximated as learning ℝ(s, a, s') directly, where ℝ(s, a, s') ∈ ℝ<sup>D</sup> is a vector of reward functions for each seen task:

$$
\mathbb{R}(s, a, s') = [r_1(s, a, s'); r_2(s, a, s'), ..., r_D(s, a, s')].
$$

Accordingly, learning  $\psi(s, a)$  for any policy  $\pi_i$  in  $\mathcal{M}_i$  becomes equivalent to learning a collection of Q-functions:

$$
\psi^{\pi_i}(s, a) = \left[Q_1^{\pi_i}(s, a), Q_2^{\pi_i}(s, a), \ldots, Q_D^{\pi_i}(s, a)\right].
$$

A similar idea of using reward functions as features to represent unseen tasks is also proposed by [129], which assumes the  $\psi$  and w as observable quantities from the environment.

*Universal Function Approximation* **(UVFA)** is an alternative approach of learning disentangled state representations [64]. Same as SR, UVFA allows TL for multiple tasks which differ only by their reward functions (goals). Different from  $SR$  which focuses on learning a reward-agnostic state representation, UVFA aims to find a function approximator that is generalized for both states and goals. The UVFA framework is built on a specific problem setting of goal conditional RL: task goals are defined in terms of states, e.g. given the state space S and the goal space  $\mathcal{G}$ , it satisfies that  $\mathcal{G} \subseteq \mathcal{S}$ . One instantiation of this problem setting can be an agent exploring different locations in a maze, where the goals are described as certain locations inside the maze. Under this problem setting, a UVFA module can be decoupled into a state embedding  $\phi(s)$  and a goal embedding  $\psi(g)$ , by applying the technique of matrix factorization to a reward matrix describing the goal-conditional task.

One merit of *UVFA* resides in its transferrable embedding  $\phi(s)$  across tasks which only differ by goals. Another benefit is its ability of continual learning when the set of goals keeps expanding over time. On the other hand, a key challenge of UVFA is that applying the matrix factorization is time-consuming, which makes it a practical concern for complex environments with large state space  $|\mathcal{S}|$ . Even with the learned embedding networks, the third stage of fine-tuning these networks via end-to-end training is still necessary.

UVFA has been connected to  $SR$  by [67], in which a set of independent rewards (tasks) themselves can be used as features for state representations. Another extended work that combines UVFA with SR is called Universal Successor Feature Approximator (USFA), which is proposed by [130]. Following the same linearity assumption, USFA is proposed as a function over a triplet of the state, action, and a policy embedding

 $z:\phi(s, a, z): \mathcal{S} \times \mathcal{A} \times \mathbb{R}^k \to \mathbb{R}^d$ , where z is the output of a *policy-encoding mapping*  $z = e(\pi): S \times \mathcal{A} \to \mathbb{R}^k$ . Based on *USFA*, the *Q*-function of any policy  $\pi$  for a task specified by w can be formularized as the product of a reward-agnostic Universal Successor Feature (USF)  $\psi$  and a reward mapper  $\mathbf{w}: Q(s, a, \mathbf{w}, z) = \psi(s, a, z)^\top \mathbf{w}$ . Facilitated by the disentangled rewards and policy generalization, [130] further introduced a generalized TD-error as a function over tasks w and policy z, which allows them to approximate the  $Q$ -function of any policy on any task using a TD-algorithm.

**5.5.3 Summary and Discussion—**We provide a summary of the discussed work in this section in Table 5. Representation transfer can facilitate TL in multiple ways based on assumptions about certain taskinvariant property. Some assume that tasks are different only in terms of their reward distributions. Other stronger assumptions include (i) decoupled dynamics, rewards [110], or policies [130] from the Q-function representations, and (ii) the feasibility of defining tasks in terms of states [130]. Based on those assumptions, approaches such as TD-algorithms [67] or matrix-factorization [64] become applicable to learn such disentangled representations. To further exploit the effectiveness of disentangled structure, we consider that *generalization* approaches, which allow changing dynamics or state distributions, are important future work that is worth more attention in this domain.

Most discussed work in this section tackles multi-task RL or meta-RL scenarios, hence the agent's *generalization* ability is extensively investigated. For instance, methods of modular networks largely evaluated the zero-shot performance from the meta-RL perspective [52, 130]. Given a fixed number of training epochs  $(\rho e)$ , *Transfer ratio (tr)* is manifested differently among these methods. It can be the relative performance of a modular net architecture compared with a baseline, or the accumulated return in modified target domains, where reward scores are negated for evaluating the dynamics transfer. Performance sensitivity  $(ps)$  is also broadly studied to estimate the *robustness* of TL. [110] analyzed the performance sensitivity given varying source tasks, while [130] studied the performance on different unseen target domains.

There are unresolved questions in this intriguing research topic. One is *how to handle drastic changes of reward functions between domains*. As discussed in [131], good policies in one MDP may perform poorly in another due to the fact that beneficial states or actions in  $\mathcal{M}_s$  may become detrimental in  $\mathcal{M}_t$  with totally different reward functions. Learning a set of basis functions [67] to represent unseen tasks (reward functions), or decoupling policies from Q-function representation [130] may serve as a good start to address this issue, as they propose a generalized latent space, from which different tasks (reward functions) can be interpreted. However, the limitation of this work is that it is not clear how many and what kind of sub-tasks need to be learned to make the latent space generalizable enough.

Another question is *how to generalize the representation learning for TL across domains with different dynamics or state-action spaces*. A learned SR might not be transferrable to an MDP with different transition dynamics, as the distribution of occupancy measure for SR may no longer hold. Potential solutions may include model-based approaches that approximate the dynamics directly or training a latent representation space for states using

multiple tasks with different dynamics for better generalization [132]. Alternatively, TL mechanisms from the supervised learning domain, such as meta-learning, which enables the ability of fast adaptation to new tasks [133], or importance sampling [134], which can compensate for the prior distribution changes [10], may also shed light on this question.

# **6 Applications**

In this section we summarize recent applications that are closely related to using TL techniques for tackling RL domains.

*Robotics learning* is a prominent application domain of RL. TL approaches in this field include *robotics learning from demonstrations*, where expert demonstrations from humans or other robots are leveraged [135] Another is collaborative robotic training [136, 137], in which knowledge from different robots is transferred by sharing their policies and episodic demonstrations. Recent research focus is this domain is fast and robust adaptation to unseen tasks. One example towards this goal is [138], in which robust robotics policies are trained using synthetic demonstrations to handle dynamic environments. Another solution is to learn domain-invariant latent representations. Examples include [139], which learns the latent representation using 3D CAD models, and [140, 141] which are derived based on the Generative-Adversarial Network. Another example is DARLA [142], which is a zero-shot transfer approach to learn disentangled representations that are robust against domain shifts. We refer readers to [70, 143] for detailed surveys along this direction.

*Game Playing* is a common test-bed for TL and RL algorithms. It has evolved from classical benchmarks such as grid-world games to more complex settings such as online-strategy games or video games with multimodal inputs. One example is AlphaGo, which is an algorithm for learning the online chessboard games using both TL and RL techniques [90]. AlphaGo is first pre-trained offline using expert demonstrations and then learns to optimize its policy using Monte-Carlo Tree Search. Its successor, *AlphaGo Master* [144], even beat the world's first ranked human player. TL-DRL approaches are also thriving in video game playing. Especially, OpenAI has trained Dota2 agents that can surpass human experts [145]. State-of-the-art platforms include MineCraft, Atari, and Starcraft. [146] designed new RL benchmarks under the MineCraft platform. [147] provided a comprehensive survey on DL applications in video game playing, which also covers TL and RL strategies from certain perspectives. A large portion of TL approaches reviewed in this survey have been applied to the Atari platforms [148].

*Natural Language Processing (NLP)* has evolved rapidly along with the advancement of DL and RL. Applications of RL to NLP range widely, from *Question Answering (QA)* [149], *Dialogue systems* [150], *Machine Translation* [151], to an integration of NLP and Computer Vision tasks, such as Visual Question Answering (VQA) [152], Image Caption [153], etc. Many NLP applications have implicitly applied TL approaches. Examples include learning from expert demonstrations for Spoken Dialogue Systems [154], VQA [152]; or reward shaping for Sequence Generation [155], Spoken Dialogue Systems [80],QA [81, 156], and Image Caption [153], or transferring policies for Structured Prediction [157] and VQA [158].

### **Large Model Training:**

RL from human and model-assisted feedback becomes indispensable in training large models (LMM), such as GPT4 [159], Sparrow [160], PaLM [161], LaMDA [162], which have shown tremendous breakthrough in dialogue applications, search engine answer improvement, artwork generation, etc. The TL method at the core of them is using human preferences as a reward signal for model fine-tuning, where the preference ranking itself is considered as shaped rewards. We believe that TL with carefully crafted human knowledge will help better align large models with human intent and hence achieve trustworthy and de-biased AI.

### **Health Informatics:**

RL has been applied to various healthcare tasks [163], including automatic medical diagnosis [164, 165], health resource scheduling [166], and drug discovery and development, [167, 168], etc. Among these applications we observe emerging trends of leveraging prior knowledge to improve the RL procedure, especially given the difficulty of accessing large amounts of clinical data. Specifically, [169] utilized Q-learning for drug delivery individualization. They integrated the prior knowledge of the dose-response characteristics into their Q-learning framework to avoid random exploration. [170] applied a DQN framework for prescribing effective HIV treatments, in which they learned a latent representation to estimate the uncertainty when transferring a pertained policy to the unseen domains. [171] studied applying human-involved interactive RL training for health informatics.

### **Others:**

RL has also been utilized in many other real-life applications. Applications in the *Transportation Systems* have adopted RL to address traffic congestion issues with better traffic signal scheduling and transportation resource allocation [8, 9, 172, 173]. We refer readers to [174] for a review along this line. Deep RL are also effective solutions to problems in *Finance*, including portfolio management [175, 176], asset allocation [177], and trading optimization [178]. Another application is the *Electricity Systems*, especially the intelligent electricity networks, which can benefit from RL techniques for improved power-delivery decisions [179, 180] and active resource management [181]. [7] provides a detailed survey of RL techniques for electric power system applications.

## **7 Future Perspectives**

In this section, we present some open challenges and future directions in TL that are closely related to the DRL domain, based on both retrospectives of the methods discussed in this survey and outlooks to the emerging trends of AI.

### **Transfer Learning from Black-Box:**

Ranging from exterior teacher demonstrations to pre-trained function approximators, black-box resource is more accessible and predominant than well-articulated knowledge. Therefore, leveraging such black-box resource is indispensable for practical TL in DRL. One of its main challenges resides in *estimating the optimality* of black-box resource, which

can be potentially noisy or biased. We consider that efforts can be made from the following perspectives:

- **1.** Inferring the *reasoning* mechanism inside the black-box. Resemblant ideas have been explored in *inverse RL* and *model-based RL*, where the goal is to approximate the reward function or to learn the dynamics model under which the demonstrated knowledge becomes reasonable.
- **2.** Designing effective feedback schemes, including leveraging domain-provided rewards, intrinsic reward feedback, or using human preference as feedback.
- **3.** Improving the interpretability of the transferred knowledge [182, 183], which benefits in evaluating and explaining the process of TL from black-box. It can also alleviate catastrophic decision-making for high-stake tasks such as autodriving.

*Knowledge Disentanglement and Fusion* are both towards better knowledge sharing across domains. Disentangling knowledge is usually a *prerequisite* for efficient knowledge fusion, which may involve external knowledge from multiple source *domains*, with diverging qualities, presented in different *modalities*, etc. Disentangling knowledge in RL can be interpreted from different perspectives: i) disentangling the action, state, or reward representations, as discussed in Sec 5.5; 2) decomposing complex tasks into multiple skill snippets. The former is an effective direction in tackling meta-RL and multi-task RL, although some solutions hinge on strict assumptions of the problem setting, such as linear dependence among domain dynamics or learning goals. The latter is relevant to hierarchical RL and prototype learning from sequence data [184]. It is relatively less discussed besides few pioneer research [132]. We believe that this direction is worth more research efforts, which not only benefits interpretable knowledge learning, but also aligns with human perception.

### **Framework-Agnostic Knowledge Transfer:**

Most contemporary TL approaches are designed for certain RL frameworks. Some are applicable to RL algorithms designed for the discrete-action space, while others may only be feasible given a continuous action space. One fundamental reason behind is the diversified development of RL algorithms. We expect that unified RL frameworks would contribute to the standardization of TL approaches in this field.

### **Evaluation and Benchmarking:**

Variant evaluation metrics have been proposed to measure TL from different but complementary perspectives, although no single metric can summarize the efficacy of a TL approach. Designing a set of generalized, novel metrics is beneficial for the development of TL in DRL. Moreover, with the effervescent development of large-scale models, it is crucial to standardize evaluation from the perspectives of ethics and groundedness. The *appropriateness* of the transferred knowledge, such as potential *stereotypes* in human preference, and the bias in the model itself should also be quantified as metrics.

### **Knowledge Transfer to and from Pre-Trained Large Models:**

By the time of this survey being finalized, unprecedented DL breakthroughs have been achieved in learning large-scale models built on massive computation resource and attributed data. One representative example is the Generative Pre-trained Transformer (GPT) [159]. Considering them as complete *knowledge graphs* whose training process maybe inaccessible, there are more challenges in this direction besides learning from a *black-box*, which are faced by a larger AI community including the RL domain. We briefly point out two directions that are worth ongoing attention:

- **1.** Efficient model fine-tuning with knowledge distillation. One important method for fine-tuning large models is RL with human feedback, in which the quantity and quality of human ratings are critical for realizing a good reward model. We anticipate other forms of TL methods in RL to be explored to further improve the efficiency of fine-tuning, such as imitation learning with adversarial training to achieve human-level performance.
- **2.** Principled prompt-engineering for knowledge extraction. More often the large model itself cannot be accessed, but only input and output of models are allowed. Such inference based knowledge extraction requires delicate prompt designs. Some efficacious efforts include designing prompts with mini task examples as one-shot learning, decomposing complex tasks into architectural, contextual prompts. Prompt engineering is being proved an important direction for effective knowledge extraction, which with proper design, can largely benefit downstream tasks that depend on large model resources.

### **Acknowledgements**

This research was supported by the National Science Foundation (IIS-2212174, IIS-1749940), National Institute of Aging (IRF1AG072449), and the Office of Naval Research (N00014-20-1-2382).

### **Biographies**

Image /page/28/Picture/9 description: A young Asian girl with long dark hair and glasses smiles at the camera. She is wearing a light blue puffer jacket and a teal crossbody bag. The background is blurred, showing a city street with tall buildings and a traffic light.

**Zhuangdi Zhu** is currently a senior data and applied scientist with Microsoft. She obtained her Ph.D. degree from the Computer Science department of Michigan State University. Zhuangdi has regularly published on prestigious machine learning conferences including NeurIPs, ICML, KDD, AAAI, etc. Her research interests reside in both fundamental and applied machine learning. Her current research involves reinforcement learning and distributed machine learning.

Image /page/29/Picture/2 description: A headshot of a young East Asian man with short, dark hair and a neutral expression. He is wearing a dark blue collared shirt. The background is plain white.

**Kaixiang Lin** is an applied scientist at Amazon web services. He obtained his Ph.D. from Michigan State University. He has broad research interests across multiple fields, including reinforcement learning, human-robot interactions, and natural language processing. His research has been published on multiple top-tiered machine learning and data mining conferences such as ICLR, KDD, NeurIPS, etc. He serves as a reviewer for top machine learning conferences regularly.

Image /page/29/Picture/4 description: A headshot of an older Indian man with short, graying hair and glasses. He is wearing a light-colored collared shirt and looking directly at the camera with a slight smile. The background is a plain, dark gray.

**Anil K. Jain** is a University distinguished professor in the Department of Computer Science and Engineering at Michigan State University. His research interests include pattern recognition and biometric authentication. He served as the editor-in-chief of the IEEE Transactions on Pattern Analysis and Machine Intelligence and was a member of the United States Defense Science Board. He has received Fulbright, Guggenheim, Alexander von Humboldt, and IAPR King Sun Fu awards. He is a member of the National Academy of Engineering and a foreign fellow of the Indian National Academy of Engineering and the Chinese Academy of Sciences.

Image /page/29/Picture/6 description: A headshot of an Asian man wearing glasses, a grey suit, a white dress shirt, and a dark tie against a grey background.

**Jiayu Zhou** is an associate professor in the Department of Computer Science and Engineering at Michigan State University. He received his Ph.D. degree in computer science from Arizona State University in 2014. He has broad research interests in the fields of large-scale machine learning and data mining as well as biomedical informatics. He has served as a technical program committee member for premier conferences such as NIPS, ICML, and SIGKDD. His papers have received the Best Student Paper Award at the 2014 IEEE International Conference on Data Mining (ICDM), the Best Student Paper Award at the 2016 International Symposium on Biomedical Imaging (ISBI) and the Best Paper Award at IEEE Big Data 2016.

## **REFERENCES**

- [1]. Sutton RS and Barto AG, Reinforcement learning: An introduction. MIT press, 2018.
- [2]. Arulkumaran K, Deisenroth MP, Brundage M, and Bharath AA, "A brief survey of deep reinforcement learning," arXiv preprint arXiv:1708.05866, 2017.

- [3]. Levine S, Finn C, Darrell T, and Abbeel P, "End-to-end training of deep visuomotor policies," The Journal of Machine Learning Research, 2016.
- [4]. Levine S, Pastor P, Krizhevsky A, Ibarz J, and Quillen D, "Learning hand-eye coordination for robotic grasping with deep learning and large-scale data collection," The International Journal of Robotics Research, 2018.
- [5]. Bellemare MG, Naddaf Y, Veness J, and Bowling M, "The arcade learning environment: An evaluation platform for general agents," Journal of Artificial Intelligence Research, 2013.
- [6]. Kosorok MR and Moodie EE, Adaptive TreatmentStrategies in Practice: Planning Trials and Analyzing Data for Personalized Medicine. SIAM, 2015.
- [7]. Glavic M, Fonteneau R, and Ernst D, "Reinforcement learning for electric power system decision and control: Past considerations and perspectives," IFAC-PapersOnLine, 2017.
- [8]. El-Tantawy S, Abdulhai B, and Abdelgawad H, "Multiagent reinforcement learning for integrated network of adaptive traffic signal controllers (marlin-atsc): methodology and large-scale application on downtown toronto," IEEE Transactions on Intelligent Transportation Systems, 2013.
- [9]. Wei H, Zheng G, Yao H, and Li Z, "Intellilight: A reinforcement learning approach for intelligent traffic light control," ACM SIGKDD International Conference on Knowledge Discovery and Data Mining, 2018.
- [10]. Pan SJ and Yang Q, "A survey on transfer learning," IEEE Transactions on knowledge and data engineering, 2009.
- [11]. Taylor ME and Stone P, "Transfer learning for reinforcement learning domains: A survey," Journal of Machine Learning Research, 2009.
- [12]. Lazaric A, "Transfer in reinforcement learning: a framework and a survey." Springer, 2012.
- [13]. Bellman R, "A markovian decision process," Journal of mathematics and mechanics, 1957.
- [14]. Bellemare MG, Dabney W, and Munos R, "A distributional perspective on reinforcement learning," in International conference on machine learning. PMLR, 2017, pp. 449–458.
- [15]. Liu M, Zhu M, and Zhang W, "Goal-conditioned reinforcement learning: Problems and solutions," arXiv preprint arXiv:2201.08299, 2022.
- [16]. Florensa C, Held D, Geng X, and Abbeel P, "Automatic goal generation for reinforcement learning agents," in International conference on machine learning. PMLR, 2018, pp. 1515–1528.
- [17]. Xu Z and Tewari A, "Reinforcement learning in factored mdps: Oracle-efficient algorithms and tighter regret bounds for the non-episodic setting," NeurIPS, vol. 33, pp. 18 226–18 236, 2020.
- [18]. Yu C, Velu A, Vinitsky E, Gao J, Wang Y, Bayen A, and Wu Y, "The surprising effectiveness of ppo in cooperative multi-agent games," NeurIPS, vol. 35, pp. 24 611–24 624, 2022.
- [19]. Kostrikov I, Agrawal KK, Dwibedi D, Levine S, and Tompson J, "Discriminator-actor-critic: Addressing sample inefficiency and reward bias in adversarial imitation learning," arXiv preprint arXiv:1809.02925, 2018.
- [20]. Rummery GA and Niranjan M, On-line Q-learning using connectionist systems. University of Cambridge, Department of Engineering Cambridge, England, 1994.
- [21]. Van Seijen H, Van Hasselt H, Whiteson S, and Wiering M, "A theoretical and empirical analysis of expected sarsa," IEEE Symposium on Adaptive Dynamic Programming and Reinforcement Learning, 2009.
- [22]. Konda V and Tsitsiklis J, "Actor-critic algorithms," NeurIPS, 2000.
- [23]. Mnih V, Badia AP, Mirza M, Graves A, Lillicrap T, Harley T, Silver D, and Kavukcuoglu K, "Asynchronous methods for deep reinforcement learning," ICML, 2016.
- [24]. Haarnoja T, Zhou A, Abbeel P, and Levine S, "Soft actor-critic: Off-policy maximum entropy deep reinforcement learning with a stochastic actor," International Conference on Machine Learning, 2018.
- [25]. Watkins CJ and Dayan P, "Q-learning," Machine learning, 1992.
- [26]. Mnih V, Kavukcuoglu K, Silver D, Rusu AA, Veness J, Bellemare MG, Graves A, Riedmiller M, Fidjeland AK, Ostrovski G et al. , "Human-level control through deep reinforcement learning," Nature, 2015.

- [27]. Hessel M, Modayil J, Van Hasselt H, Schaul T, Ostrovski G, Dabney W, Horgan D, Piot B, Azar M, and Silver D, "Rainbow: Combining improvements in deep reinforcement learning," AAAI, 2018.
- [28]. Williams RJ, "Simple statistical gradient-following algorithms for connectionist reinforcement learning," Machine learning, 1992.
- [29]. Schulman J, Levine S, Abbeel P, Jordan M, and Moritz P, "Trust region policy optimization," ICML, 2015.
- [30]. Schulman J, Wolski F, Dhariwal P, Radford A, and Klimov O, "Proximal policy optimization algorithms," arXiv preprint arXiv:1707.06347, 2017.
- [31]. Silver D, Lever G, Heess N, Degris T, Wierstra D, and Riedmiller M, "Deterministic policy gradient algorithms," 2014.
- [32]. Lillicrap TP, Hunt JJ, Pritzel A, Heess N, Erez T, Tassa Y, Silver D, and Wierstra D, "Continuous control with deep reinforcement learning," arXiv preprint arXiv:1509.02971, 2015.
- [33]. Fujimoto S, Van Hoof H, and Meger D, "Addressing function approximation error in actor-critic methods," arXiv preprint arXiv:1802.09477, 2018.
- [34]. Nagabandi A, Kahn G, Fearing RS, and Levine S, "Neural network dynamics for model-based deep reinforcement learning with model-free fine-tuning," in 2018 IEEE international conference on robotics and automation (ICRA). IEEE, 2018, pp. 7559–7566.
- [35]. Botev ZI, Kroese DP, Rubinstein RY, and L'Ecuyer P, "The cross-entropy method for optimization," in Handbook of statistics. Elsevier, 2013, vol. 31, pp. 35–59.
- [36]. Chua K, Calandra R, McAllister R, and Levine S, "Deep reinforcement learning in a handful of trials using probabilistic dynamics models," NeurIPS, vol. 31, 2018.
- [37]. Sutton RS, "Integrated architectures for learning, planning, and reacting based on approximating dynamic programming," in Machine learning proceedings 1990. Elsevier, 1990, pp. 216–224.
- [38]. Feinberg V, Wan A, Stoica I, Jordan MI, Gonzalez JE, and Levine S, "Model-based value estimation for efficient model-free reinforcement learning," arXiv preprint arXiv:1803.00101, 2018.
- [39]. Levine S and Koltun V, "Guided policy search," in International conference on machine learning. PMLR, 2013, pp. 1–9.
- [40]. Bharadhwaj H, Xie K, and Shkurti F, "Model-predictive control via cross-entropy and gradientbased optimization," in Learning for Dynamics and Control. PMLR, 2020, pp. 277–286.
- [41]. Deisenroth M and Rasmussen CE, "Pilco: A model-based and data-efficient approach to policy search," in Proceedings of the 28th International Conference on machine learning (ICML-11), 2011, pp. 465–472.
- [42]. Gal Y, McAllister R, and Rasmussen CE, "Improving pilco with bayesian neural network dynamics models," in Data-efficient machine learning workshop, ICML, vol. 4, no. 34, 2016, p. 25.
- [43]. Lampert CH, Nickisch H, and Harmeling S, "Learning to detect unseen object classes by between-class attribute transfer," IEEE Conference on Computer Vision and Pattern Recognition, 2009.
- [44]. Dayan P and Hinton GE, "Feudal reinforcement learning," NeurIPS, 1993.
- [45]. Sutton RS, Precup D, and Singh S, "Between mdps and semi-mdps: A framework for temporal abstraction in reinforcement learning," Artificial intelligence, 1999.
- [46]. Parr R and Russell SJ, "Reinforcement learning with hierarchies of machines," NeurIPS, 1998.
- [47]. Dietterich TG, "Hierarchical reinforcement learning with the maxq value function decomposition," Journal of artificial intelligence research, 2000.
- [48]. Lazaric A and Ghavamzadeh M, "Bayesian multi-task reinforcement learning," in ICML-27th international conference on machine learning. Omnipress, 2010, pp. 599–606.
- [49]. Zhang Y and Yang Q, "A survey on multi-task learning," IEEE Transactions on Knowledge and Data Engineering, vol. 34, no. 12, pp. 5586–5609, 2021.
- [50]. Teh Y, Bapst V, Czarnecki WM, Quan J, Kirkpatrick J, Hadsell R, Heess N, and Pascanu R, "Distral: Robust multitask reinforcement learning," NeurIPS, 2017.

- [51]. Parisotto E, Ba JL, and Salakhutdinov R, "Actormimic: Deep multitask and transfer reinforcement learning," ICLR, 2016.
- [52]. Devin C, Gupta A, Darrell T, Abbeel P, and Levine S, "Learning modular neural network policies for multi-task and multi-robot transfer," 2017 IEEE International Conference on Robotics and Automation (ICRA), 2017.
- [53]. Andreas J, Klein D, and Levine S, "Modular multitask reinforcement learning with policy sketches," ICML, 2017.
- [54]. Yang R, Xu H, Wu Y, and Wang X, "Multi-task reinforcement learning with soft modularization," NeurIPS, vol. 33, pp. 4767–4777, 2020.
- [55]. Hospedales T, Antoniou A, Micaelli P, and Storkey A, "Meta-learning in neural networks: A survey," IEEE transactions on pattern analysis and machine intelligence, vol. 44, no. 9, pp. 5149– 5169, 2021.
- [56]. Jia Z, Li X, Ling Z, Liu S, Wu Y, and Su H, "Improving policy optimization with generalistspecialist learning," in International Conference on Machine Learning. PMLR, 2022, pp. 10 104–10 119.
- [57]. Ding W, Lin H, Li B, and Zhao D, "Generalizing goal-conditioned reinforcement learning with variational causal reasoning," arXiv preprint arXiv:2207.09081, 2022.
- [58]. Kirk R, Zhang A, Grefenstette E, and Rocktäschel T, "A survey of zero-shot generalisation in deep reinforcement learning," Journal of Artificial Intelligence Research, vol. 76, pp. 201–264, 2023.
- [59]. Kim B, Farahmand A.-m., Pineau J, and Precup D, "Learning from limited demonstrations," NeurIPS, 2013.
- [60]. Czarnecki W, Pascanu R, Osindero S, Jayakumar S, Swirszcz G, and Jaderberg M, "Distilling policy distillation," The 22nd International Conference on Artificial Intelligence and Statistics, 2019.
- [61]. Ng AY, Harada D, and Russell S, "Policy invariance under reward transformations: Theory and application to reward shaping," ICML, 1999.
- [62]. Goodfellow I, Pouget-Abadie J, Mirza M, Xu B, Warde-Farley D, Ozair S, Courville A, and Bengio Y, "Generative adversarial nets," NeurIPS, pp. 2672–2680, 2014.
- [63]. Zhu Z, Lin K, Dai B, and Zhou J, "Learning sparse rewarded tasks from sub-optimal demonstrations," arXiv preprint arXiv:2004.00530, 2020.
- [64]. Schaul T, Horgan D, Gregor K, and Silver D, "Universal value function approximators," ICML, 2015.
- [65]. Finn C and Levine S, "Meta-learning: from few-shot learning to rapid reinforcement learning," ICML, 2019.
- [66]. Taylor ME, Stone P, and Liu Y, "Transfer learning via inter-task mappings for temporal difference learning," Journal of Machine Learning Research, 2007.
- [67]. Barreto A, Borsa D, Quan J, Schaul T, Silver D, Hessel M, Mankowitz D, Žíıdek A, and Munos R, "Transfer in deep reinforcement learning using successor features and generalised policy improvement," ICML, 2018.
- [68]. Zhu Z, Lin K, Dai B, and Zhou J, "Off-policy imitation learning from observations," NeurIPS, 2020.
- [69]. Ho J and Ermon S, "Generative adversarial imitation learning," NeurIPS, 2016.
- [70]. Zhao W, Queralta JP, and Westerlund T, "Sim-to-real transfer in deep reinforcement learning for robotics: a survey," in 2020 IEEE symposium series on computational intelligence (SSCI). IEEE, 2020, pp. 737–744.
- [71]. Muller-Brockhausen M, Preuss M, and Plaat A, "Procedural content generation: Better benchmarks for transfer reinforcement learning," in 2021 IEEE Conference on games (CoG). IEEE, 2021, pp. 01–08.
- [72]. Vithayathil Varghese N and Mahmoud QH, "A survey of multi-task deep reinforcement learning," Electronics, vol. 9, no. 9, p. 1363, 2020.
- [73]. Williams RJ and Baird LC, "Tight performance bounds on greedy policies based on imperfect value functions," Tech. Rep, 1993.

- [74]. Wiewiora E, Cottrell GW, and Elkan C, "Principled methods for advising reinforcement learning agents," ICML, 2003.
- [75]. Devlin SM and Kudenko D, "Dynamic potential-based reward shaping," ICAAMAS, 2012.
- [76]. Harutyunyan A, Devlin S, Vrancx P, and Nowé A, "Expressing arbitrary reward functions as potential-based advice," AAAI, 2015.
- [77]. Brys T, Harutyunyan A, Taylor ME, and Nowé A, "Policy transfer using reward shaping," ICAAMS, 2015.
- [78]. Ve erík M, Hester T, Scholz J, Wang F, Pietquin O, Piot B, Heess N, Rothörl T, Lampe T, and Riedmiller M, "Leveraging demonstrations for deep reinforcement learning on robotics problems with sparse rewards," arXiv preprint arXiv:1707.08817, 2017.
- [79]. Tenorio-Gonzalez AC, Morales EF, and Villaseñor-Pineda L, "Dynamic reward shaping: Training a robot by voice," Advances in Artificial Intelligence – IBERAMIA, 2010.
- [80]. Su P-H, Vandyke D, Gasic M, Mrksic N, Wen T-H, and Young S, "Reward shaping with recurrent neural networks for speeding up on-line policy learning in spoken dialogue systems," arXiv preprint arXiv:1508.03391, 2015.
- [81]. Lin XV, Socher R, and Xiong C, "Multi-hop knowledge graph reasoning with reward shaping," arXiv preprint arXiv:1808.10568, 2018.
- [82]. Devlin S, Yliniemi L, Kudenko D, and Tumer K, "Potential-based difference rewards for multiagent reinforcement learning," ICAAMS, 2014.
- [83]. Grzes M and Kudenko D, "Learning shaping rewards in model-based reinforcement learning," Proc. AAMAS Workshop on Adaptive Learning Agents, 2009.
- [84]. Marom O and Rosman B, "Belief reward shaping in reinforcement learning," AAAI, 2018.
- [85]. Liu F, Ling Z, Mu T, and Su H, "State alignment-based imitation learning," arXiv preprint arXiv:1911.10947, 2019.
- [86]. Kim K, Gu Y, Song J, Zhao S, and Ermon S, "Domain adaptive imitation learning," ICML, 2020.
- [87]. Ma Y, Wang Y-X, and Narayanaswamy B, "Imitation-regularized offline learning," International Conference on Artificial Intelligence and Statistics, 2019.
- [88]. Yang M and Nachum O, "Representation matters: Offline pretraining for sequential decision making," arXiv preprint arXiv:2102.05815, 2021.
- [89]. Zhang X and Ma H, "Pretraining deep actor-critic reinforcement learning algorithms with expert demonstrations," arXiv preprint arXiv:1801.10459, 2018.
- [90]. Silver D, Huang A, Maddison CJ, Guez A, Sifre L, Van Den Driessche G, Schrittwieser J, Antonoglou I, Panneershelvam V, Lanctot M et al. , "Mastering the game of go with deep neural networks and tree search," Nature, 2016.
- [91]. Schaal S, "Learning from demonstration," NeurIPS, 1997.
- [92]. Hester T, Vecerik M, Pietquin O, Lanctot M, Schaul T, Piot B, Horgan D, Quan J, Sendonaris A, Osband I et al. , "Deep q-learning from demonstrations," AAAI, 2018.
- [93]. Nair A, McGrew B, Andrychowicz M, Zaremba W, and Abbeel P, "Overcoming exploration in reinforcement learning with demonstrations," IEEE International Conference on Robotics and Automation (ICRA), 2018.
- [94]. Chemali J and Lazaric A, "Direct policy iteration with demonstrations," International Joint Conference on Artificial Intelligence, 2015.
- [95]. Piot B, Geist M, and Pietquin O, "Boosted bellman residual minimization handling expert demonstrations," Joint European Conference on Machine Learning and Knowledge Discovery in Databases, 2014.
- [96]. Brys T, Harutyunyan A, Suay HB, Chernova S, Taylor ME, and Nowé A, "Reinforcement learning from demonstration through shaping," International Joint Conference on Artificial Intelligence, 2015.
- [97]. Kang B, Jie Z, and Feng J, "Policy optimization with demonstrations," ICML, 2018.
- [98]. Bertsekas DP, "Approximate policy iteration: A survey and some new methods," Journal of Control Theory and Applications, 2011.
- [99]. Schaul T, Quan J, Antonoglou I, and Silver D, "Prioritized experience replay," ICLR, 2016.

- [100]. Ross S, Gordon G, and Bagnell D, "A reduction of imitation learning and structured prediction to noregret online learning," AISTATS, 2011.
- [101]. Gao Y, Lin J, Yu F, Levine S, Darrell T et al. , "Reinforcement learning from imperfect demonstrations," arXiv preprint arXiv:1802.05313, 2018.
- [102]. Jing M, Ma X, Huang W, Sun F, Yang C, Fang B, and Liu H, "Reinforcement learning from imperfect demonstrations under soft expert guidance." AAAI, 2020.
- [103]. Brantley K, Sun W, and Henaff M, "Disagreement-regularized imitation learning," ICLR, 2019.
- [104]. Hinton G, Vinyals O, and Dean J, "Distilling the knowledge in a neural network," Deep Learning and Representation Learning Workshop, NeurIPS, 2014.
- [105]. Rusu AA, Colmenarejo SG, Gulcehre C, Desjardins G, Kirkpatrick J, Pascanu R, Mnih V, Kavukcuoglu K, and Hadsell R, "Policy distillation," arXiv preprint arXiv:1511.06295, 2015.
- [106]. Yin H and Pan SJ, "Knowledge transfer for deep reinforcement learning with hierarchical experience replay," AAAI, 2017.
- [107]. Schmitt S, Hudson JJ, Zidek A, Osindero S, Doersch C, Czarnecki WM, Leibo JZ, Kuttler H, Zisserman A, Simonyan K et al. , "Kickstarting deep reinforcement learning," arXiv preprint arXiv:1803.03835, 2018.
- [108]. Schulman J, Chen X, and Abbeel P, "Equivalence between policy gradients and soft q-learning," arXiv preprint arXiv:1704.06440, 2017.
- [109]. Fernández F and Veloso M, "Probabilistic policy reuse in a reinforcement learning agent," Proceedings of the fifth international joint conference on Autonomous agents and multiagent systems, 2006.
- [110]. Barreto A, Dabney W, Munos R, Hunt JJ, Schaul T, van Hasselt HP, and Silver D, "Successor features for transfer in reinforcement learning," NuerIPS, 2017.
- [111]. Bellman R, "Dynamic programming," Science, 1966.
- [112]. Torrey L, Walker T, Shavlik J, and Maclin R, "Using advice to transfer knowledge acquired in one reinforcement learning task to another," European Conference on Machine Learning, 2005.
- [113]. Gupta A, Devin C, Liu Y, Abbeel P, and Levine S, "Learning invariant feature spaces to transfer skills with reinforcement learning," ICLR, 2017.
- [114]. Konidaris G and Barto A, "Autonomous shaping: Knowledge transfer in reinforcement learning," ICML, 2006.
- [115]. Ammar HB and Taylor ME, "Reinforcement learning transfer via common subspaces," Proceedings of the 11th International Conference on Adaptive and Learning Agents, 2012.
- [116]. Badrinarayanan V, Kendall A, and Cipolla R, "Segnet: A deep convolutional encoder-decoder architecture for image segmentation," IEEE transactions on pattern analysis and machine intelligence, 2017.
- [117]. Wang C and Mahadevan S, "Manifold alignment without correspondence," International Joint Conference on Artificial Intelligence, 2009.
- [118]. Bocsi B, Csató L, and Peters J, "Alignment-based transfer learning for robot models," The 2013 International Joint Conference on Neural Networks (IJCNN), 2013.
- [119]. Ammar HB, Eaton E, Ruvolo P, and Taylor ME, "Unsupervised cross-domain transfer in policy gradient reinforcement learning via manifold alignment," AAAI, 2015.
- [120]. Ammar HB, Tuyls K, Taylor ME, Driessens K, and Weiss G, "Reinforcement learning transfer via sparse coding," ICAAMS, 2012.
- [121]. Lazaric A, Restelli M, and Bonarini A, "Transfer of samples in batch reinforcement learning," ICML, 2008.
- [122]. Rusu AA, Rabinowitz NC, Desjardins G, Soyer H, Kirkpatrick J, Kavukcuoglu K, Pascanu R, and Hadsell R, "Progressive neural networks," arXiv preprint arXiv:1606.04671, 2016.
- [123]. Fernando C, Banarse D, Blundell C, Zwols Y, Ha D, Rusu AA, Pritzel A, and Wierstra D, "Pathnet: Evolution channels gradient descent in super neural networks," arXiv preprint arXiv:1701.08734, 2017.
- [124]. Harvey I, "The microbial genetic algorithm," European Conference on Artificial Life, 2009.
- [125]. Zhang A, Satija H, and Pineau J, "Decoupling dynamics and reward for transfer learning," arXiv preprint arXiv:1804.10689, 2018.

- [126]. Dayan P, "Improving generalization for temporal difference learning: The successor representation," Neural Computation, 1993.
- [127]. Kulkarni TD, Saeedi A, Gautam S, and Gershman SJ, "Deep successor reinforcement learning," arXiv preprint arXiv:1606.02396, 2016.
- [128]. Zhang J, Springenberg JT, Boedecker J, and Burgard W, "Deep reinforcement learning with successor features for navigation across similar environments," IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS), 2017.
- [129]. Mehta N, Natarajan S, Tadepalli P, and Fern A, "Transfer in variable-reward hierarchical reinforcement learning," Machine Learning, 2008.
- [130]. Borsa D, Barreto A, Quan J, Mankowitz D, Munos R, van Hasselt H, Silver D, and Schaul T, "Universal successor features approximators," ICLR, 2019.
- [131]. Lehnert L, Tellex S, and Littman ML, "Advantages and limitations of using successor features for transfer in reinforcement learning," arXiv preprint arXiv:1708.00102, 2017.
- [132]. Petangoda JC, Pascual-Diaz S, Adam V, Vrancx P, and Grau-Moya J, "Disentangled skill embeddings for reinforcement learning," arXiv preprint arXiv:1906.09223, 2019.
- [133]. Finn C, Abbeel P, and Levine S, "Model-agnostic meta-learning for fast adaptation of deep networks," ICML, 2017.
- [134]. Zadrozny B, "Learning and evaluating classifiers under sample selection bias," ICML, 2004.
- [135]. Argall BD, Chernova S, Veloso M, and Browning B, "A survey of robot learning from demonstration," Robotics and autonomous systems, 2009.
- [136]. Kehoe B, Patil S, Abbeel P, and Goldberg K, "A survey of research on cloud robotics and automation," IEEE Transactions on automation science and engineering, 2015.
- [137]. Gu S, Holly E, Lillicrap T, and Levine S, "Deep reinforcement learning for robotic manipulation with asynchronous off-policy updates," IEEE international conference on robotics and automation (ICRA), 2017.
- [138]. Yu W, Tan J, Liu CK, and Turk G, "Preparing for the unknown: Learning a universal policy with online system identification," arXiv preprint arXiv:1702.02453, 2017.
- [139]. Sadeghi F and Levine S, "Cad2rl: Real single-image flight without a single real image," arXiv preprint arXiv:1611.04201, 2016.
- [140]. Bousmalis K, Irpan A, Wohlhart P, Bai Y, Kelcey M, Kalakrishnan M, Downs L, Ibarz J, Pastor P, Konolige K et al., "Using simulation and domain adaptation to improve efficiency of deep robotic grasping," IEEE International Conference on Robotics and Automation (ICRA), 2018.
- [141]. Bharadhwaj H, Wang Z, Bengio Y, and Paull L, "A data-efficient framework for training and sim-to-real transfer of navigation policies," International Conference on Robotics and Automation (ICRA), 2019.
- [142]. Higgins I, Pal A, Rusu A, Matthey L, Burgess C, Pritzel A, Botvinick M, Blundell C, and Lerchner A, "Darla: Improving zero-shot transfer in reinforcement learning," ICML, 2017.
- [143]. Kober J, Bagnell JA, and Peters J, "Reinforcement learning in robotics: A survey," The International Journal of Robotics Research, 2013.
- [144]. Silver D, Schrittwieser J, Simonyan K, Antonoglou I, Huang A, Guez A, Hubert T, Baker L, Lai M, Bolton A et al. , "Mastering the game of go without human knowledge," Nature, 2017.
- [145]. OpenAI. (2019) Dotal2 blog. [Online]. Available: <https://openai.com/blog/openai-five/>
- [146]. Oh J, Chockalingam V, Singh S, and Lee H, "Control of memory, active perception, and action in minecraft," arXiv preprint arXiv:1605.09128, 2016.
- [147]. Justesen N, Bontrager P, Togelius J, and Risi S, "Deep learning for video game playing," IEEE Transactions on Games, 2019.
- [148]. Mnih V, Kavukcuoglu K, Silver D, Graves A, Antonoglou I, Wierstra D, and Riedmiller M, "Playing atari with deep reinforcement learning," arXiv preprint arXiv:1312.5602, 2013.
- [149]. Chen H, Liu X, Yin D, and Tang J, "A survey on dialogue systems: Recent advances and new frontiers," Acm Sigkdd Explorations Newsletter, 2017.
- [150]. Singh SP, Kearns MJ, Litman DJ, and Walker MA, "Reinforcement learning for spoken dialogue systems," NeurIPS, 2000.

- [151]. Zoph B and Le QV, "Neural architecture search with reinforcement learning," arXiv preprint arXiv:1611.01578, 2016.
- [152]. Hu R, Andreas J, Rohrbach M, Darrell T, and Saenko K, "Learning to reason: End-to-end module networks for visual question answering," IEEE International Conference on Computer Vision, 2017.
- [153]. Ren Z, Wang X, Zhang N, Lv X, and Li L-J, "Deep reinforcement learning-based image captioning with embedding reward," IEEE Conference on Computer Vision and Pattern Recognition, 2017.
- [154]. Andreas J, Rohrbach M, Darrell T, and Klein D, "Learning to compose neural networks for question answering," arXiv preprint arXiv:1601.01705, 2016.
- [155]. Bahdanau D, Brakel P, Xu K, Goyal A, Lowe R, Pineau J, Courville A, and Bengio Y, "An actor-critic algorithm for sequence prediction," arXiv preprint arXiv:1607.07086, 2016.
- [156]. Godin F, Kumar A, and Mittal A, "Learning when not to answer: a ternary reward structure for reinforcement learning based question answering," Proceedings of the 2019 Conference of the North American Chapter of the Association for Computational Linguistics: Human Language Technologies, 2019.
- [157]. Chang K-W, Krishnamurthy A, Agarwal A, Langford J, and Daumé III H, "Learning to search better than your teacher," 2015.
- [158]. Lu J, Kannan A, Yang J, Parikh D, and Batra D, "Best of both worlds: Transferring knowledge from discriminative learning to a generative visual dialog model," NeurIPS, 2017.
- [159]. OpenAI, "Gpt-4 technical report," arXiv, 2023.
- [160]. Glaese A, McAleese N, Trebacz M, Aslanides J, Firoiu V, Ewalds T, Rauh M, Weidinger L, Chadwick M, Thacker P et al. , "Improving alignment of dialogue agents via targeted human judgements," arXiv preprint arXiv:2209.14375, 2022.
- [161]. Chowdhery A, Narang S, Devlin J, Bosma M, Mishra G, Roberts A, Barham P, Chung HW, Sutton C, Gehrmann S et al., "Palm: Scaling language modeling with pathways," arXiv preprint arXiv:2204.02311, 2022.
- [162]. Thoppilan R, De Freitas D, Hall J, Shazeer N, Kulshreshtha A, Cheng H-T, Jin A, Bos T, Baker L, Du Y et al. , "Lamda: Language models for dialog applications," arXiv preprint arXiv:2201.08239, 2022.
- [163]. Yu C, Liu J, and Nemati S, "Reinforcement learning in healthcare: A survey," arXiv preprint arXiv:1908.08796, 2019.
- [164]. Alansary A, Oktay O, Li Y, Le Folgoc L, Hou B, Vaillant G, Kamnitsas K, Vlontzos A, Glocker B,Kainz B et al., "Evaluating reinforcement learning agents for anatomical landmark detection," 2019.
- [165]. Ma K, Wang J, Singh V, Tamersoy B, Chang Y-J, Wimmer A, and Chen T, "Multimodal image registration with deep context reinforcement learning," International Conference on Medical Image Computing and Computer-Assisted Intervention, 2017.
- [166]. Gomes TSMT, "Reinforcement learning for primary care e appointment scheduling," 2017.
- [167]. Serrano A, Imbernón B, Pérez-Sánchez H, Cecilia JM, Bueno-Crespo A, and Abellán JL, "Accelerating drugs discovery with deep reinforcement learning: An early approach," International Conference on Parallel Processing Companion, 2018.
- [168]. Popova M, Isayev O, and Tropsha A, "Deep reinforcement learning for de novo drug design," Science advances, 2018.
- [169]. Gaweda AE, Muezzinoglu MK, Aronoff GR, Jacobs AA, Zurada JM, and Brier ME, "Incorporating prior knowledge into q-learning for drug delivery individualization," Fourth International Conference on Machine Learning and Applications, 2005.
- [170]. Killian TW, Daulton S, Konidaris G, and Doshi-Velez F, "Robust and efficient transfer learning with hidden parameter markov decision processes," NeurIPS, 2017.
- [171]. Holzinger A, "Interactive machine learning for health informatics: when do we need the human-in-the-loop?" Brain Informatics, 2016.
- [172]. Li L, Lv Y, and Wang F-Y, "Traffic signal timing via deep reinforcement learning," IEEE/CAA Journal of Automatica Sinica, 2016.

- [173]. Lin K, Zhao R, Xu Z, and Zhou J, "Efficient large-scale fleet management via multi-agent deep reinforcement learning," ACM SIGKDD International Conference on Knowledge Discovery & Data Mining, 2018.
- [174]. Yau K-LA, Qadir J, Khoo HL, Ling MH, and Komisarczuk P, "A survey on reinforcement learning models and algorithms for traffic signal control," ACM Computing Surveys (CSUR), 2017.
- [175]. Moody J, Wu L, Liao Y, and Saffell M, "Performance functions and reinforcement learning for trading systems and portfolios," Journal of Forecasting, 1998.
- [176]. Jiang Z and Liang J, "Cryptocurrency portfolio management with deep reinforcement learning," IEEE Intelligent Systems Conference (IntelliSys), 2017.
- [177]. Neuneier R, "Enhancing q-learning for optimal asset allocation," NeurIPS, 1998.
- [178]. Deng Y, Bao F, Kong Y, Ren Z, and Dai Q, "Deep direct reinforcement learning for financial signal representation and trading," IEEE transactions on neural networks and learning systems, 2016.
- [179]. Dalal G, Gilboa E, and Mannor S, "Hierarchical decision making in electricity grid management," International Conference on Machine Learning, 2016.
- [180]. Ruelens F, Claessens BJ, Vandael S, De Schutter B, Babuška R, and Belmans R, "Residential demand response of thermostatically controlled loads using batch reinforcement learning," IEEE Transactions on Smart Grid, 2016.
- [181]. Wen Z, O'Neill D, and Maei H, "Optimal demand response using device-based reinforcement learning," IEEE Transactions on Smart Grid, 2015.
- [182]. Li Y, Song J, and Ermon S, "Infogail: Interpretable imitation learning from visual demonstrations," NeurIPS, 2017.
- [183]. Ramakrishnan R and Shah J, "Towards interpretable explanations for transfer learning in sequential tasks," AAAI Spring Symposium Series, 2016.
- [184]. Choi E, Bahadori MT, Sun J, Kulas J, Schuetz A, and Stewart W, "Retain: An interpretable predictive model for healthcare using reverse time attention mechanism," NeurIPS, vol. 29, 2016.

Ō

Image /page/38/Figure/2 description: This is a diagram illustrating a reinforcement learning framework for transferring knowledge between domains. The process begins with 'Demonstrations' (DE) which are used for 'Learning from Demonstrations' and feeding into a 'Deep Neural Network' represented by f(theta, phi). This network has 'Actor' and 'Critic' components. The 'Expert' interacts with the 'Source Domain' (MS) to produce states (s), actions (a), and rewards (r). The 'Deep Neural Network' outputs a policy pi(a|s) and a Q-value function Q\_phi(s,a). 'Policy Transfer' and 'Representations Transfer' feed into 'Transferred Knowledge', which is then used for 'Reward Shaping'. 'Inter-Task Mapping' also contributes to 'Transferred Knowledge'. A 'Learning Agent' interacts with the 'Target Domain' (Mt), receiving states (s) and rewards (r), and producing actions (a). The 'Reward Shaping' influences the reward (r') received by the learning agent. The overall goal is to transfer knowledge from a source domain to a target domain to improve the learning agent's performance.

Image /page/38/Figure/3 description: The image contains the text "Fig. 1:" in bold, black font.

Image /page/38/Figure/4 description: The image contains text that reads "An overview of different TL approaches, organized by the format of transferred knowledge."

Author Manuscript Author Manuscript

Author Manuscript

Author Manuscript

A comparison of reward shaping approaches. *x* denotes that the information is not revealed in the paper.  $\boldsymbol{\chi}$  denotes that the information is not revealed in the paper. A comparison of reward shaping approaches.

# **TABLE 2:**

A comparison of learning from demonstration approaches. A comparison of learning from demonstration approaches.

| Methods     | Optimality guarantee | Format of transferred demonstrations                                                | RL framework | Evaluation metrics       |
|-------------|----------------------|-------------------------------------------------------------------------------------|--------------|--------------------------|
| DPID        | ✓                    | Indicator binary-loss : $\mathscr{L}(s_i) = \mathbb{1}\{\pi_E(s_i) \neq \pi(s_i)\}$ | API          | ap, ar, nka              |
| APID        | ✗                    | Hinge loss on the marginal-loss: $\mathscr{L}(Q, \pi, \pi_E) _+$                    | API          | ap, ar, nta, nkq         |
| APID extend | ✓                    | Marginal-loss: $\mathscr{L}(\mathcal{Q},\pi,\pi_E)$                                 | API          | ap, ar, nta, nkq         |
| [93]        | ✓                    | Increasing sampling priority and behavior cloning loss                              | DDPG         | ap, ar, tr, pe, nkq      |
| DQfD        | ✗                    | Cached transitions in the replay buffer                                             | DQN          | ap, ar, tr               |
| LfDS        | ✗                    | Reward shaping function                                                             | DQN          | ap, ar, tr               |
| GAIL        | ✓                    | Reward shaping function: $-\lambda \log(1 - D(s, a))$                               | TRPO         | ap, ar, tr, pe, nka      |
| POfD        | ✓                    | Reward shaping function: $r(s, a) - \lambda \log(1 - D(s, a))$                      | TRPO,PPO     | ap, ar, tr, pe, nka      |
| DDPGfD (pe) | ✓                    | Increasing sampling priority                                                        | DDPG         | ap, ar, tr, pe           |
| SAIL        | ✗                    | Reward shaping function: $r(s, a) - \lambda \log(1 - D(s, a))$                      | DDPG         | ap, ar, tr, pe, nkq, nka |

# **TABLE 3:**

A comparison of policy transfer approaches. A comparison of *policy transfer* approaches.

| l<br>Ē                    |  |
|---------------------------|--|
| $"".$<br>$":$             |  |
| :<br>i<br>֠<br>ı<br>ׇׇ֘֝֡ |  |
| ä<br>ĺ                    |  |

# **TABLE 4:**

A comparison of inter-task mapping approaches. "-" indicates no RL framework constraints. A comparison of inter-task mapping approaches. "-" indicates no RL framework constraints.

|       | nework<br><b><i><u>Aethods</u></i></b> RL fram | <b>MDP</b> difference                                                                   | Mapping function                                                                                                                    | Usage of mapping                                   | <b>Evaluation metrics</b> |
|-------|------------------------------------------------|-----------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------|---------------------------|
| [66]  | SARSA                                          | $S_i \neq S_i$ , $\mathscr{A}_i \neq \mathscr{A}_i$ $M(Q_i) \rightarrow Q_i$            |                                                                                                                                     | Q value reuse                                      | ap, ar, tt, tr            |
| 112   | O-learning                                     | $\mathscr{A}, \neq \mathscr{A}, \mathscr{B}, \neq \mathscr{B}, M(Q) \rightarrow advice$ |                                                                                                                                     | Relative Qranking                                  | ap, ar, tr                |
| [113] |                                                | $S_s \neq S_i$                                                                          | $M(s_i) \rightarrow r'$                                                                                                             | Reward shaping                                     | ap, ar, pe, tr            |
|       | [14] $SARSA(\lambda$                           | $S_s \neq S_t$ , $\mathscr{R}_s \neq \mathscr{R}_t$ , $M(s_t) \rightarrow r'$           |                                                                                                                                     | Reward shaping                                     | ap, ar, pe, tt            |
|       | [115] Fitted Value Iteration $S_s \neq S_t$    |                                                                                         | $M(s_s) \rightarrow s_t$                                                                                                            | Penalty loss on state deviation from expert policy | ap, ar, pe, tr            |
|       | [121] Fitted Q Iteration                       |                                                                                         | $S_s \times \mathcal{A}, \neq S_t \times \mathcal{A}_t$ $M((s_s, a_s, s_s) \rightarrow (s_t, a_t, s_t))$ Reduce random exploration  |                                                    | ap, ar, pe, tr, nta       |
|       |                                                |                                                                                         | $S_s \times \mathcal{A}_s \neq S_t \times \mathcal{A}_t$ $M((s_s, a_s, s_s) \rightarrow (s_t, a_t, s_t))$ Reduce random exploration |                                                    | ap, ar, pe, tr, nta       |

 Author Manuscript Author Manuscript

# **TABLE 5:**

A comparison of TL approaches of representation transfer. A comparison of TL approaches of representation transfer.
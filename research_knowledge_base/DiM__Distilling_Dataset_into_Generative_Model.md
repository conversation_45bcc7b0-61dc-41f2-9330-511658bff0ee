# DiM: Distilling Dataset into Generative Model

<span id="page-0-1"></span><PERSON><sup>1\*</sup> <PERSON><PERSON><PERSON><sup>1,2\*</sup> <PERSON><PERSON><PERSON><sup>1</sup> <PERSON><sup>3</sup> <PERSON><sup>2</sup> <PERSON><sup>1</sup> <sup>1</sup>National University of Singapore  $2$ Zhejiang University  $3$ Tsinghua University

> {gu jianyang, jiangwei zju}@zju.edu.cn <EMAIL> {kai.wang, youy}@comp.nus.edu.sg <EMAIL>

> > Code: <https://github.com/vimar-gu/DiM>

### Abstract

*Dataset distillation reduces the network training cost by synthesizing small and informative datasets from largescale ones. Despite the success of the recent dataset distillation algorithms, three drawbacks still limit their wider application: i). the synthetic images perform poorly on large architectures; ii). they need to be re-optimized when the distillation ratio changes; iii). the limited diversity restricts the performance when the distillation ratio is large. In this paper, we propose a novel distillation scheme to Distill information of large train sets into generative Models, named DiM. Specifically, Di<PERSON> learns to use a generative model to store the information of the target dataset. During the distillation phase, we minimize the differences in logits predicted by a models pool between real and generated images. At the deployment stage, the generative model synthesizes various training samples from random noises on the fly. Due to the simple yet effective designs, the trained DiM can be directly applied to different distillation ratios and large architectures without extra cost. We validate the proposed DiM across 4 datasets and achieve state-of-the-art results on all of them. To the best of our knowledge, we are the first to achieve higher accuracy on complex architectures than simple ones, such as 75.1% with ResNet-18 and 72.6% with ConvNet-3 on ten images per class of CIFAR-10. Besides, DiM outperforms previous methods with 10%* ∼ *22% when images per class are 1 and 10 on the SVHN dataset.*

### 1. Introduction

Deep learning models have shown superior performance in the computer vision area [\[14,](#page-8-0) [25,](#page-8-1) [7,](#page-8-2) [26,](#page-8-3) [17,](#page-8-4) [9,](#page-8-5) [31,](#page-9-0) [12,](#page-8-6) [13,](#page-8-7) [35,](#page-9-1) [34,](#page-9-2) [30\]](#page-9-3). However, the success of these state-ofthe-art models largely relies on ultra-large-scale datasets, which are hardly affordable for training with limited computational resources. Dataset distillation (DD) [\[39,](#page-9-4) [46,](#page-9-5) [38,](#page-9-6)

<span id="page-0-0"></span>Image /page/0/Figure/9 description: The image displays a flowchart illustrating the "Paradigm of Distill knowledge into Images (DiI)". The process is divided into two stages: Distillation Stage and Deployment Stage. In the Distillation Stage, a "Large Dataset" is fed into a "Distillation" process, which outputs "Synthetic Images". The Deployment Stage begins with these "Synthetic Images", which are then used by "Downstream Architectures". The synthetic images are depicted as a grid of six smaller images, showing various objects like a car, a bird, and a spider.

Image /page/0/Figure/10 description: The image depicts a two-stage process: Distillation Stage and Deployment Stage. In the Distillation Stage, a 'Large Dataset' is fed into a 'Distillation' process, which then outputs a 'Generative Model'. The Deployment Stage takes 'Embeddings' (represented by vertical bars of varying colors) and feeds them into a 'Generative Model', which then connects to 'Downstream Architectures'.

Image /page/0/Figure/11 description: The image displays a bar chart and a table, both related to a method called "Distill knowledge into Model (DiM)". The bar chart, titled "(b) Paradigm of Distill knowledge into Model (DiM)", plots "Acc. (%)" on the y-axis against "IPC/INPC" on the x-axis, with values 1, 10, and 50. It compares four methods: DC (grey), CAFE (red), MTT (teal), and DiM (orange). For IPC/INPC = 1, the accuracies are approximately 28% for DC, 31% for CAFE, 46% for MTT, and 52% for DiM. For IPC/INPC = 10, the accuracies are approximately 45% for DC, 47% for CAFE, 65% for MTT, and 66% for DiM. For IPC/INPC = 50, the accuracies are approximately 54% for DC, 57% for CAFE, 71% for MTT, and 72% for DiM. To the right of the bar chart is a table with two columns: "Method" and "Redeployment" (with sub-columns "1 → 10" and "1 → 50"). The table lists several methods: DC [46], DSA [43], CAFE [38], DM [44], IDC [19], MTT\* [3], and DiM. The redeployment values for DiM are 0.1 for "1 → 10" and 0.5 for "1 → 50". Other methods have significantly higher redeployment values.

(c) Performance comparisons. (d) GPU hours of redeployment.

Figure 1: (a) and (b): Illustrations of DiI and our DiM dataset distillation paradigms.  $Z$  and  $Y$  represent random noises and labels. (c): Comparisons of performances of DiI and DiM methods under different image number settings. (d): Comparisons of redeployment GPU hours of DiI and DiM methods. All results are obtained on CIFAR-10 with ConvNet-3. <sup>∗</sup> denotes that the training time for the 2,000 checkpoints in MTT [\[3\]](#page-8-9) is not included.

[44,](#page-9-8) [43\]](#page-9-7), as a promising approach, aims to reduce the heavy training cost through distilling discriminative information from large-scale datastes into small but informative ones.

Current state-of-the-art dataset distillation methods [\[39,](#page-9-4) [46,](#page-9-5) [38,](#page-9-6) [3,](#page-8-9) [29,](#page-8-10) [19\]](#page-8-8) are based on an intuitive idea to match the status between synthetic and original sets. There are mainly three types of matching strategies: gradient matching [\[46,](#page-9-5) [43,](#page-9-7) [19\]](#page-8-8), training trajectory matching [\[3\]](#page-8-9), and feature or distribution matching [\[44,](#page-9-8) [38\]](#page-9-6). We note the above methods as DiI since they aim to Distill knowledge into

<sup>\*</sup>Equal contribution.

<span id="page-1-1"></span><span id="page-1-0"></span>Table 1: The comparisons between DiI and DiM.

| Comparison Item             | DiI        | DiM          |
|-----------------------------|------------|--------------|
| Information Container       | Images     | Models       |
| Cross-arch. Performance     | Unstable   | Stable       |
| Cross-IPC/INPC Redeployment | Every time | Once for all |

Images (as shown in Fig. [1a\)](#page-0-0).

Although these DiI strategies achieve remarkable performances in the dataset distillation area, they have the following shortcomings: 1). Most previous DiI works [\[46,](#page-9-5) [43,](#page-9-7) [44,](#page-9-8) [3,](#page-8-9) [19,](#page-8-8) [36\]](#page-9-9) conduct distillation based on 3-layer convolutional networks. The synthesized dataset performs poorly on unseen architectures, especially larger ones, such as ResNet [\[14\]](#page-8-0), VGG [\[35\]](#page-9-1), and DenseNet [\[16\]](#page-8-11). 2). DiI methods store information in synthetic images. When the Image Per Class (IPC) setting or distillation ratio changes, DiI methods usually need to retrain the distillation stage. We compare performances and redeployment costs of several DiI methods in Fig. [1c](#page-0-0) and [1d.](#page-0-0) One can find DiI methods perform poorly on small IPC settings. Besides, redeploying IPC = 1 to 10 costs about 8.3 hours for MTT  $[3]$  and 18.2 hours for IDC [\[19\]](#page-8-8), which is a heavy burden.

As illustrated in Fig. [1b,](#page-0-0) in this paper, we aim to Distill information of original datasets into a Model (DiM) instead of images. Specifically, given labels, we utilize a conditional generative model [\[27\]](#page-8-12) to reconstruct training images from latent space and simultaneously distill knowledge from original datasets. To make the generated images helpful to the classification task, we propose a logits-based matching strategy to minimize the differences of prediction logits between real and generated images. Considering various supervisions are crucial for discriminative feature learning  $[48]$ , the logits is extracted by a model that is randomly selected from a models pool in each epoch. At the deployment stage, the generative model synthesizes various training samples from random noises on the fly. We summarize the differences between DiI and DiM in Tab. [1.](#page-1-0) One can find that DiM has obvious advantages in cross-architecture generalization and redeployment efficiency. As DiM stores information in models rather than images, directly using IPC as a budget leads to insufficient utilization. To sufficiently demonstrate the learned knowledge of DiM in the distillation stage, we define Image Numbers of Per Class (INPC) to keep the same forward number as DiI for comparison.

DiM can be easily redeployed with different distillation ratios and architectures thanks to simple yet efficient designs. We conduct experiments on several popular benchmarks and demonstrate that the results yielded by DiM have the following benefits. First, the results yielded by DiM are significantly superior to state-of-the-art methods in all settings, especially on large architectures. Second, as illustrated in Fig. [1d,](#page-0-0) DiM saves  $13 \times \sim 160 \times$  redeployment cost when compared with DiI methods. Third, with rich information from models pool, DiM shows more robust crossarchitecture generalization than DiI methods.

The main contributions of this paper are summarised as:

- We propose DiM, a novel scheme that distills the dataset into a generative model instead of images, with significant improvements over previous DiI methods in large architecture scalability, redeployment efficiency, and cross-architecture generality.
- In DiM, models pool and logits matching are proposed to distill knowledge from the original dataset, where models pool provides rich supervisions and logits matching aims to minimize the logits differences of real and synthetic images.
- DiM consistently outperforms state-of-the-art DiI methods on various datasets and architectures with  $13\times \sim 160\times$  lower redeployment cost, showing its effectiveness and generality for dataset distillation.

## 2. Related Works

#### 2.1. Dataset Distillation.

Wang *et.al* is the first to propose the definition of Dataset Distillation (DD) [\[39\]](#page-9-4): synthesize a small-scale set from a large one and achieve comparable training results. Specifically, they propose a model to generate synthetic samples that are subject to minimization of the training loss differences between synthetic and original data. After that, DC [\[46\]](#page-9-5), DSA [\[43\]](#page-9-7), and IDC [\[19\]](#page-8-8) are proposed to match gradients between synthetic and original samples. CAFE [\[38\]](#page-9-6), and DM [\[44\]](#page-9-8) introduce a feature matching paradigm to reduce the influence of synthetic images that bias to largegradient samples from the original set. MTT [\[3\]](#page-8-9) proposes to minimize the loss of training trajectories between synthetic and original images. HaBa [\[36\]](#page-9-9) divides the original dataset into bases and several hallucinators to keep the diversity of synthetic images. FRePo [\[48\]](#page-9-10) proposes neural feature regression with pooling to reduce the computation and memory costs of DD. MAP [\[6\]](#page-8-13) designs a method where the information of the original dataset is stored in bases and addressing matrices. Although these methods achieve stateof-the-art results in the dataset distillation area, they are still faced with the cross-architecture generalization and redeployment cost issues. Our work is concurrent with [\[42\]](#page-9-11).

## 2.2. Generative Models

Our work is also related to generative adversarial networks (GANs) [\[11,](#page-8-14) [40,](#page-9-12) [18,](#page-8-15) [33,](#page-9-13) [10\]](#page-8-16). [\[11\]](#page-8-14) is the first proposal to generate real-looking images. After that, many GANbased methods have been proposed for other tasks, such as

<span id="page-2-1"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: The image displays a two-stage process for a method called DiM. The left side, labeled "Distillation Stage of DiM," shows a generator receiving noise (Z) and a label (Y) to produce synthetic samples. These synthetic samples, along with real samples and their labels, are fed into a "Models Pool." A "Logits Matching" component interacts with the Models Pool, receiving input from and sending output to it via forward (red arrows) and backward (gray dashed arrows) connections. The right side, labeled "Deployment Stage of DiM," shows a generator receiving noise (Z) and a label (Y) to produce synthetic samples. These samples are then processed by a CNN, which leads to a "CE Loss" calculation. A legend at the bottom clarifies that red arrows represent forward connections and gray dashed arrows represent backward connections, and indicates that the "Models Pool" contains "Random Initialized Models."

Figure 2: Illustration of proposed 'Distill into Model' (DiM) framework. DiM consists of two stages the same as previous DiI methods, named distillation and deployment stages. Distillation stage has two special designs: models pool and logits matching (LM). Models pool includes several randomly initialized models on target dataset from open-source community. LM is regarded as bridge to distill knowledge from target dataset to generator.  $Z$  and  $Y$  denote the random noises and labels.

cycleGAN [\[17\]](#page-8-4) introduces a bi-directional GANs for style transform, InfoGAN [\[4\]](#page-8-17) maximizes the mutual information between a small subset of the latent variables and the observation, and BigGAN [\[2\]](#page-8-18) studies training GANs on largest scale datasets. The main difference is that our generation model aims to distill discriminate information from the original dataset while GANs merely target to synthesize real-looking images. GAN Inversion [\[40,](#page-9-12) [18,](#page-8-15) [33\]](#page-9-13) aims to find the inputs of latent space for the pre-trained GAN model, which can faithfully recover a given image. ITGAN [\[45\]](#page-9-14) optimizes a fixed number of inputs for a pre-trained generator, but it needs to retrain when INPC changes.

### 3. Method

In this section, we first review preliminaries of dataset distillation [\[39,](#page-9-4) [46,](#page-9-5) [43,](#page-9-7) [38,](#page-9-6) [19,](#page-8-8) [6\]](#page-8-13) and GANs [\[11,](#page-8-14) [27\]](#page-8-12). Then we introduce DiM and its components. Finally, we discuss the differences between GANs, GAN Inversion, and DiM.

#### 3.1. Preliminaries

We briefly introduce preliminaries of dataset distillation (DD) and generative adversarial networks (GANs).

DD. DD aims to synthesize a small yet informative dataset that achieves comparable training results to the original large dataset. Given a large dataset  $\mathcal{T} = (x_i, y_i)|_{i=1}^{|\mathcal{T}|}$ , DD designs algorithms to distill the information into a small dataset  $S = (s_j, y_j)|_{j=1}^{|S|} (|S| \ll |\mathcal{T}|)$ . The distillation operation can be formulated as follows,

$$
S = f(\mathcal{T}; \mathcal{S}_0; \theta), \tag{1}
$$

where  $f$  denotes the distillation algorithm with parameter  $\theta$ ,  $S_0$  represents the initialization of synthetic data, and S denotes the final synthetic image dataset. The knowledge of the original train set is distilled into  $S$  during the distillation process. After the distillation stage,  $S$  is deployed

on downstream tasks, such as classification, to evaluate its performance, denoted as the deployment stage.

GANs. The goal of GANs [\[11\]](#page-8-14) is to synthesize photorealistic images, which includes a generator  $\mathcal G$  and discriminator D. In GANs training, the generator and discriminator are jointly optimized for the mini-max loss function as,

$$
\min_{\mathcal{G}} \max_{\mathcal{D}} \mathbb{E}_{\mathcal{X} \sim P(\mathcal{X})}[\log \mathcal{D}(\mathcal{X})] + \mathbb{E}_{\mathcal{Z} \sim P(\mathcal{Z})}[\log(1 - \mathcal{D}(\mathcal{G}(\mathcal{Z})))],
$$
\n(2)

where  $P(\mathcal{Z})$  and  $P(\mathcal{X})$  denote the distribution of latent vector  $Z$  and real images. Conditional GANs [\[27\]](#page-8-12) are proposed to introduce the labels  $Y$  into the generation process for more specific generation targets. The optimization loss of conditional GANs can be written as,

$$
\min_{\mathcal{G}} \max_{\mathcal{D}} \mathbb{E}_{\mathcal{X} \sim P(\mathcal{X})} [\log \mathcal{D}(\mathcal{X}|\mathcal{Y})] + \mathbb{E}_{\mathcal{Z} \sim P(\mathcal{Z})} [\log(1 - \mathcal{D}(\mathcal{G}(\mathcal{Z}|\mathcal{Y})))].
$$
\n(3)

In this paper, we utilize conditional GANs [\[27\]](#page-8-12) for generating images. However, our motivation is different from current conditional GAN methods. We aim to encourage the generator to synthesize images that are helpful for downstream classification training.

#### 3.2. Overview of DiM

We propose a novel scheme, 'Distill into Models' (DiM), for the dataset distillation task. The most significant difference between DiI and DiM is the information container (Images or Model). The pipeline of our proposed DiM is illustrated in Fig. [2.](#page-2-0) DiM also consists of the same two stages as DiI: distillation and deployment. The distillation stage aims to train a generator that can synthesize discriminative images from random noises and labels. The deployment stage is designed for evaluating the trained generator. For each batch, we randomly sample  $B$  paired random noises  $Z$  and labels  $Y$ . Then, the paired  $Z$  and  $Y$  are fed into the

<span id="page-3-1"></span>generator to generate images. After that, we sample  $B$  real images with the same labels of  $Y$  from the original dataset. A model is randomly sampled from models pool to predict the classification logits of real and synthetic images, respectively. Minimizing the logits differences is served as the optimization target for distilling the knowledge from real images into the generative model. At the deployment stage, the generative model synthesizes various training samples from random noises on the fly.

#### 3.3. Distill into Model

Synthetic Images Generation. Given a batch of random noises  $\mathcal{Z} \in \mathbb{R}^{B \times K}$  and corresponding labels  $\mathcal{Y} \in \mathbb{R}^{B \times C}$ , a generator model is utilized to synthesize images. This process can be formulated as,

$$
S_{\text{DiM}} = \mathcal{G}([\mathcal{Z} \oplus \mathcal{Y}]; \beta), \tag{4}
$$

where  $\oplus$  represents the concatenation operation,  $\beta$  is the parameters of  $\mathcal{G}, B$  is the batch size, and C denotes the number of categories.  $\mathcal Y$  is transformed into one-hot embedding for convenience.  $K$  is the dimension of random noise, and we evaluate its impact in supplementary materials.

Logits Matching. Different from previous works [\[46,](#page-9-5) [43,](#page-9-7) [3,](#page-8-9) [38\]](#page-9-6), we utilize a Logits Matching (LM) strategy instead of matching the gradients, features/distributions, or training trajectories. The modification is based on the following considerations: 1). These three matching strategies perform well only when optimized category by category, which reduces the efficiency of DD; 2). Gradient matching is easily biased to samples with large gradients in the original train set [\[38\]](#page-9-6). 3). Feature/distribution matching is designed to minimize the distance of mean features/distribution in each batch, where the diversity of synthetic images is not constrained; 4). Training trajectory matching is time-consuming for pre-training a large number of models (*i.e.* 2, 000 checkpoints). Hence, we match the prediction logits for real and synthetic images. For a batch of real images  $I_r$  and synthetic images  $S_{\text{DiM}}$ , we apply a model  $m$  to predict the classification logits of these images. Formally, logits matching can be written as follows,

$$
L_m = \text{MSE}(m(\mathcal{S}_{\text{DiM}}); m(I_r)),\tag{5}
$$

where  $MSE(\cdot;\cdot)$  denotes the mean-square error loss. LM aims to minimize the prediction logits that influences the results of downstream classification task directly. Therefore, LM provides more specific supervisions when compared to previous matching strategies. Besides, logits matching does not need to be optimized category by category, which largely improves training efficiency in the distillation stage.

Models Pool. Previous works [\[38,](#page-9-6) [46,](#page-9-5) [43\]](#page-9-7) only use one model to match the difference between real and synthetic images, which provides limited supervision for informative <span id="page-3-0"></span>Algorithm 1 Optimization of DiM.

| <b>Input:</b> N denotes the number of training epoch for                                                                    |
|-----------------------------------------------------------------------------------------------------------------------------|
| vanilla GANs. $J$ is the total iteration numbers for each                                                                   |
| epoch. $\epsilon$ is learning rates. The optimizer is SGD.                                                                  |
| For $i = 1, \ldots, N:$ $\triangleright$ Train generator with GAN loss.                                                     |
| For $j = 1, \ldots, J:$ $\triangleright$ Iterations of each epoch.                                                          |
| Calculating $L_g$ .                                                                                                         |
| $\beta \leftarrow \beta - \epsilon \frac{\partial L_g}{\partial \beta}.$ $\triangleright$ Update generator.                 |
| For $q = 1, \ldots, Q$                                                                                                      |
| $\triangleright$ Add $L_m$ for $Q$ epochs.                                                                                  |
| For $j = 1, \ldots, J:$                                                                                                     |
| $\triangleright$ Iterations of each epoch.                                                                                  |
| Calculating $L_g + \lambda L_m$ .                                                                                           |
| $\beta \leftarrow \beta - \epsilon \frac{\partial (L_g + \lambda L_m)}{\partial \beta}.$ $\triangleright$ Update generator. |
| <b>Output:</b> Saving the parameters $\beta$ of generator.                                                                  |

image synthesis. MTT [\[3\]](#page-8-9) trains 2,000 checkpoints as a trajectory buffer in advance, which results in heavy computational and memory cost. Here, we argue that supervisions diversified in the architecture dimension are beneficial for distilling the information from original train dataset. Therefore, we extend the matching model to a models pool. In each epoch, we randomly select one random-initialized model from the models pool. The selection operation is formulated as follows,

$$
m = \mathcal{M}[\text{Random-choice}(|\mathcal{M}|, 1)],\tag{6}
$$

where  $M$  denotes the models pool,  $m$  is the selected model. Our models pool has two benefits: 1). Models pool provides various views for logits matching, which helps to distill more discriminative information from original dataset. 2). Models pool can also mitigate the over-fitting on one architecture [\[48\]](#page-9-10), leading to superior generalization perfor-mances over previous works [\[46,](#page-9-5) [44,](#page-9-8) [43,](#page-9-7) [38,](#page-9-6) [3,](#page-8-9) [19\]](#page-8-8) largely.

Multi-task Optimization. As a randomly initialized generator produces almost noise outputs, which is unable to form valid matching with real images, we first train vanilla GANs for  $N$  epochs. In this stage, only GAN loss is used to optimize the network. Then, we jointly use  $L_m$  and GAN loss to optimize the networks. The total loss is written as,

$$
L_{total} = L_g + \lambda L_m,\tag{7}
$$

where  $L_q$  represents the GAN loss and  $\lambda$  is the trade-off parameter between  $L_q$  and  $L_m$ . We study the influence of  $\lambda$ in the experiment section. After Nth epoch, the parameters of generator  $\beta$  is updated by minimizing  $L_{total}$ :

$$
\beta \leftarrow \underset{\beta}{\arg\min} L_{total}.\tag{8}
$$

To better understand our DiM, we summarize the training algorithm in Alg. [1](#page-3-0)

<span id="page-4-1"></span><span id="page-4-0"></span>Table 2: The performance (%) comparison to state-of-the-art methods.  $LD^{\dagger}$  and  $DD^{\dagger}$  use AlexNet for CIFAR10, while the rest use ConvNet for training and testing. Underline denotes results from [\[36\]](#page-9-9). Bold entries are best results.

|         | Dataset                                                                                                                                             |                                                                                                              | <b>SVHN</b>                                                                                        |                                                                                                          |                                                                                                                                                                                            | CIFAR10                                                                                                                                                                     |                                                                                                                            |                                                                                                                                                      | <b>MNIST</b>                                                                                                                                 |                                                                                                                            |                                                                                                                                                                                     | FashionMNIST                                                                                                                                          |                                                                                  |
|---------|-----------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------|
|         | <b>IPC</b><br>Ratio %                                                                                                                               | 0.014                                                                                                        | 10<br>0.14                                                                                         | 50<br>0.7                                                                                                | 0.02                                                                                                                                                                                       | 10<br>0.2                                                                                                                                                                   | 50                                                                                                                         | 0.2                                                                                                                                                  | 10<br>$\overline{c}$                                                                                                                         | 50<br>10                                                                                                                   | 0.2                                                                                                                                                                                 | 10<br>$\overline{c}$                                                                                                                                  | 50<br>10                                                                         |
| Coreset | Random<br>Herding<br>K-Center<br>Forgetting                                                                                                         | $14.6 + 1.6$<br>$20.9 + 1.3$<br>$21.0 \pm 1.5$<br>$12.1 \pm 1.7$                                             | $35.1 \pm 4.1$<br>$50.5 + 3.3$<br>$14.0 \pm 1.3$                                                   | $70.9 + 0.9$<br>$72.6 \pm 0.8$<br>$20.1 \pm 1.4$                                                         | $14.4 + 2.0$<br>$121.5 \pm 1.3$<br>$ 21.5 \pm 1.3$<br>$16.8 \pm 1.2$ $27.2 \pm 1.5$ $13.5 \pm 1.2$ $23.3 \pm 1.0$ $23.3 \pm 1.1$ $35.5 \pm 5.6$ $68.1 \pm 3.3$ $88.2 \pm 1.2$              | $26.0 \pm 1.2$<br>$31.6 \pm 0.7$<br>$14.7 \pm 0.9$                                                                                                                          | $40.4 \pm 0.6$<br>$27.0 \pm 1.4$                                                                                           | $43.4 \pm 1.0$ 64.9 $\pm 3.5$ 95.1 $\pm 0.9$<br>$89.2 \pm 1.6$ 93.7 $\pm$ 0.3<br>$89.3 \pm 1.5$                                                      | $84.4 \pm 1.7$                                                                                                                               | $97.9 \pm 0.2$<br>$94.8 \pm 0.2$<br>$97.4 \pm 0.3$                                                                         | $51.4 + 3.8$<br>$67.0 + 1.9$<br>$66.9 \pm 1.8$                                                                                                                                      | $73.8 + 0.7$<br>$71.1 + 0.7$<br>$54.7 \pm 1.5$<br>$42.0 \pm 5.5$ 53.9 $\pm 2.0$                                                                       | $82.5 + 0.7$<br>$71.9 + 0.8$<br>$68.3 \pm 0.8$<br>$55.0 \pm 1.1$                 |
| DiI     | $DD^{\dagger}$ [39]<br>$LD^{\dagger}$ [1]<br>DC[46]<br>DSA [43]<br>DM [44]<br><b>CAFE</b> [38]<br>CAFE+DSA [38]<br>KIP [29]<br>MTT[3]<br>FRePo [48] | $31.2 + 1.4$<br>$27.5 \pm 1.4$<br>$42.6 + 3.3$<br>$42.9 + 3.0$<br>$57.3 \pm 0.1$<br>$58.5 \pm 1.4$<br>$\sim$ | $76.1 + 0.6$<br>$79.2 + 0.5$<br>$75.9 \pm 0.6$<br>$77.9 + 0.6$<br>$75.0 \pm 0.1$<br>$70.8 \pm 1.8$ | $82.3 \pm 0.3$<br>$84.4 \pm 0.4$<br>$81.3 \pm 0.3$<br>$82.3 \pm 0.4$<br>$80.5 \pm 0.1$<br>$85.7 \pm 0.1$ | $\overline{\phantom{a}}$<br>$25.7 \pm 0.7$<br>$28.3 \pm 0.5$<br>$28.8 \pm 0.7$<br>$26.0 \pm 0.8$<br>$30.3 \pm 1.1$<br>$31.6 \pm 0.8$<br>$49.9 \pm 0.2$<br>$46.3 \pm 0.8$<br>$46.8 \pm 0.7$ | $36.8 \pm 1.2$<br>$38.3 \pm 0.4$<br>$44.9 \pm 0.5$<br>$52.1 \pm 0.5$<br>$48.9 \pm 0.6$ 63.0 $\pm$ 0.4<br>$46.3 \pm 0.6$<br>$50.9 + 0.5$<br>$62.7 \pm 0.3$<br>$65.3 \pm 0.7$ | $42.5 \pm 0.4$<br>$53.9 \pm 0.5$<br>$60.6 \pm 0.5$<br>$55.5 \pm 0.6$<br>$62.3 \pm 0.4$<br>$68.6 \pm 0.2$<br>$71.6 \pm 0.2$ | $\overline{\phantom{a}}$<br>$60.9 \pm 3.2$<br>$91.7 \pm 0.5$<br>$88.7 \pm 0.6$<br>$89.7 + 0.6$<br>$93.1 \pm 0.3$<br>$90.8 \pm 0.5$<br>$90.1 \pm 0.1$ | $79.5 \pm 8.1$<br>$87.3 \pm 0.7$<br>$97.4 \pm 0.2$<br>$97.8 \pm 0.1$<br>$97.5 \pm 0.1$<br>$97.2 \pm 0.2$<br>$97.5 \pm 0.1$<br>97.5 $\pm 0.0$ | $93.3 \pm 0.3$<br>$98.8 \pm 0.2$<br>$99.2 \pm 0.1$<br>$98.6 \pm 0.1$<br>$98.6 \pm 0.2$<br>$98.9 \pm 0.2$<br>$98.3 \pm 0.1$ | $77.1 \pm 0.9$<br>$73.7 + 0.7$<br>$73.5 \pm 0.5$<br>$65.5\pm0.4$ $71.7\pm0.2$   $93.0\pm0.4$ $98.6\pm0.1$ $99.2\pm0.0$   $75.6\pm0.3$                                               | $70.5 \pm 0.6$ 82.3 $\pm 0.4$<br>$70.6 \pm 0.6$ 84.6 $\pm 0.3$<br>$83.0 \pm 0.4$<br>$83.0 \pm 0.3$<br>$86.8 \pm 0.1$<br>$86.2 \pm 0.2$ $89.6 \pm 0.1$ | $83.6 + 0.4$<br>$88.7 + 0.2$<br>$84.8 \pm 0.4$<br>$88.2 + 0.3$<br>$88.0 \pm 0.1$ |
|         | <b>INPC</b>                                                                                                                                         |                                                                                                              | 10                                                                                                 | 50                                                                                                       |                                                                                                                                                                                            | 10                                                                                                                                                                          | 50                                                                                                                         |                                                                                                                                                      | 10                                                                                                                                           | 50                                                                                                                         |                                                                                                                                                                                     | 10                                                                                                                                                    | 50                                                                               |
| GAN     | Conditional GAN [27]   78.3±0.9 83.8±0.6 86.6±0.5   46.4±1.2 62.7±0.9 68.1±0.8   96.1±0.7 97.8±0.3 98.4±0.3   81.5±0.5 84.0±0.2 86.3±0.3            |                                                                                                              |                                                                                                    |                                                                                                          |                                                                                                                                                                                            |                                                                                                                                                                             |                                                                                                                            |                                                                                                                                                      |                                                                                                                                              |                                                                                                                            |                                                                                                                                                                                     |                                                                                                                                                       |                                                                                  |
| DiM     | DiM                                                                                                                                                 |                                                                                                              |                                                                                                    |                                                                                                          |                                                                                                                                                                                            |                                                                                                                                                                             |                                                                                                                            |                                                                                                                                                      |                                                                                                                                              |                                                                                                                            | $80.9 \pm 1.2$ $87.9 \pm 0.5$ $90.4 \pm 0.3$ $51.3 \pm 1.0$ $66.2 \pm 0.5$ $72.6 \pm 0.4$ $96.5 \pm 0.6$ $98.6 \pm 0.2$ $99.2 \pm 0.2$ $84.5 \pm 0.4$ $88.2 \pm 0.2$ $89.8 \pm 0.1$ |                                                                                                                                                       |                                                                                  |
|         | <b>Whole Dataset</b>                                                                                                                                |                                                                                                              | $95.4 \pm 0.1$                                                                                     |                                                                                                          |                                                                                                                                                                                            | $84.8 \pm 0.1$                                                                                                                                                              |                                                                                                                            |                                                                                                                                                      | $99.6 \pm 0.0$                                                                                                                               |                                                                                                                            |                                                                                                                                                                                     | $93.5 \pm 0.1$                                                                                                                                        |                                                                                  |

### 3.4. Discussions with GANs and GAN Inversion

Our DiM is different from GANs [\[11,](#page-8-14) [10\]](#page-8-16) in the optimization targets. DiM aims to optimize a generator that can synthesize images usable for downstream tasks, while vanilla GANs only generates visually real images. GAN In-version methods [\[40,](#page-9-12) [18,](#page-8-15) [33\]](#page-9-13) are designed to find the  $\mathcal Z$  in the latent space with original real images for a pre-trained GAN model. During the inversion stage,  $\mathcal Z$  is optimized by the following equation,

$$
\mathcal{Z}^* \leftarrow \underset{\mathcal{Z}}{\arg\min} \, ||\mathcal{G}(\mathcal{Z}) - \mathcal{I}||, \tag{9}
$$

where  $\mathcal I$  denotes the images from training dataset and  $\mathcal G$  is the generator. Note that the parameters of  $G$  are fixed in inversion training. Our DiM is different from GAN Inversionbased methods in the following aspects: First, the goals of these two methods are different. DiM aims to optimize a generator to generate discriminate images for training, while GAN Inversion is designed to learn a reverse mapping between real images and the latent embeddings. Second, the initialization for the generator is different. GAN Inversion optimizes latent embeddings while fixes the trained generator. Our DiM optimizes a randomly initialized generator and expects the generator to synthesize discriminate images from random noises. Third, the stored information is different. DiM only stores the generative model. Oppositely, GAN Inversion is required to store both the latent embeddings and the generative model.

### 4. Experiments

In this section, we conduct extensive experiments with DiM on a variety of datasets and architectures to demonstrate the effectiveness and generality of DiM.

#### 4.1. Datasets and Implementation Details

Following previous works, we evaluate the effectiveness of DiM on MNIST  $[23]$ , FashionMNIST  $[41]$ , SVHN  $[32]$ , and CIFAR-10 [\[21\]](#page-8-21). MNIST is a digital handwritten number (0 to 9) dataset. It contains 60,000 training samples and 10,000 testing samples with  $28 \times 28$  resolution. FashionM-NIST consists of 60,000 training images and 10,000 testing images. All images are with  $28 \times 28$  resolution and are from 10 categories. SVHN is captured from street view, containing 73,257 training digits and 26,032 testing ones from 10 classes. CIFAR-10 contains colored natural images with the size of  $32 \times 32$  of 10 categories. There are 50,000 images for training and 10,000 images for testing.

Implementation Details. We here present the details of distillation and deployment stages, respectively. In distillation stage, we first train a conditional GANs for  $N$  epochs. We defaultly set  $N = 120$ . For hyper parameters of conditional GANs, we employ Adam [\[20\]](#page-8-22) with a learning rate  $\epsilon = 1e^{-4}$ . The batch size B is 64. The trade-off  $\lambda$  is set to 0.01. The sensitiveness of N and  $\lambda$  are investigated in Sec. [4.3.](#page-6-0) In the deployment stage, we save the trained generator  $G$ . Random noises and labels are sampled as inputs for  $G$ . Following the setting in IDC [\[19\]](#page-8-8), we train the validation model for 1,000 epochs. The optimizer is set as SGD, with a learning rate of 0.01. For data augmentation, we adopt

<span id="page-5-3"></span><span id="page-5-0"></span>Table 3: Test performance comparisons among HaBa [\[36\]](#page-9-9), IDC [\[19\]](#page-8-8), and DiM. To make fair comparisons, we keep the same INPC<sup>\*</sup> in each epoch. Bold entries are best results.

|                     | $INPC^*$ | Condensation |      |      | Whole Dataset |
|---------------------|----------|--------------|------|------|---------------|
|                     |          | HaBa         | IDC  | DiM  |               |
| <b>MNIST</b>        | 1        | -            | 94.2 | 97.3 | 99.6          |
|                     | 10       | -            | 98.4 | 98.7 |               |
|                     | 50       | -            | 99.1 | 99.2 |               |
| <b>FashionMNIST</b> | 1        | -            | 81.0 | 86.0 | 93.5          |
|                     | 10       | -            | 86.0 | 89.0 |               |
|                     | 50       | -            | 86.2 | 90.2 |               |
| <b>SVHN</b>         | 1        | 69.8         | 68.5 | 83.8 | 95.4          |
|                     | 10       | 83.2         | 87.5 | 89.1 |               |
|                     | 50       | 88.3         | 90.1 | 91.0 |               |
| <b>CIFAR10</b>      | 1        | 48.3         | 50.6 | 55.8 | 84.8          |
|                     | 10       | 69.9         | 67.5 | 70.8 |               |
|                     | 50       | 74.0         | 74.5 | 74.9 |               |

the settings in DSA [\[43\]](#page-9-7). As our DiM aims to distill knowledge from original datasets and memorize it into a model, we conduct the same forward numbers for fair comparison.

#### 4.2. Comparison to coreset and DiI Methods

As shown in Tab. [2,](#page-4-0) we compare our DiM with coreset selection, DiI methods, and vanilla GAN. It should be noted that INPC is not proposed for unfair comparison but for sufficiently demonstrating the knowledge learned from vanilla GAN and DiM. We list the results of state-of-the-art coreset and DiI methods. Based on previous works' results in Tab. [2,](#page-4-0) we have following observations: 1). Coreset or randomly selection perform poorly as the information diversity of corset or subset is poor in the dataset distillation setting (*i.e.*  $|\mathcal{S}| \ll |\mathcal{T}|$ ). 2). The performances of DiI-based methods largely rely on the number of IPC. The performance drops significantly when IPC is small, such as IPC = 1. Our proposed DiM achieves state-of-the-art performances on all datasets and INPCs. Especially when INPC is equal to 1, the improvement of DiM is up to 22%, which demonstrates the strong information capacity of the generator.

Comparisons to HaBa and IDC. HaBa [\[36\]](#page-9-9) divides the original dataset into bases and transformation functions (*i.e.* style transform network). For example, HaBa [\[36\]](#page-9-9) takes consideration of memory as a budget and only saves single-channel synthetic images. IDC [\[19\]](#page-8-8) defaults to set factor as 2 for training, so a single image consists of two sub-images. Therefore, the forward number of these methods is different. To make a fair comparison to them, we increase the forward number to the same. The comparison results are shown in Tab. [3.](#page-5-0) One can find that DiM achieves the highest results on all datasets, which shows the strong effectiveness of our proposed method.

<span id="page-5-1"></span>Table 4: Evaluation of feature matching, gradient matching, and logits matching strategies in the proposed framework. Bold entries are best results.

|                                       | <b>Match Strategy</b>                 |                                       |                                                             | Acc.                                                        |                                                             |      |
|---------------------------------------|---------------------------------------|---------------------------------------|-------------------------------------------------------------|-------------------------------------------------------------|-------------------------------------------------------------|------|
|                                       | feature                               | gradient                              | logits                                                      | $C-3$                                                       | R-10                                                        | R-18 |
| <span style="color: orange;">✓</span> |                                       |                                       | 68.1                                                        | 68.7                                                        | 69.1                                                        |      |
|                                       | <span style="color: orange;">✓</span> |                                       | 72.0                                                        | 73.2                                                        | 73.8                                                        |      |
|                                       |                                       | <span style="color: orange;">✓</span> | 71.3                                                        | 71.1                                                        | 71.7                                                        |      |
|                                       |                                       |                                       | <span style="font-weight: bold; color: orange;">72.6</span> | <span style="font-weight: bold; color: orange;">74.1</span> | <span style="font-weight: bold; color: orange;">75.0</span> |      |

<span id="page-5-2"></span>Table 5: Exploring which epoch(s) to perform logits matching. The best result is marked in bold.

| Acc. / N | 20   | 50   | 80   | 100  | 120         | 150  |
|----------|------|------|------|------|-------------|------|
| C-3 (%)  | 65.6 | 70.4 | 71.7 | 71.8 | <b>72.6</b> | 72.5 |
| R-10 (%) | 67.0 | 71.4 | 73.0 | 73.2 | <b>74.1</b> | 74.1 |

#### 4.3. Ablation Studies

In this section, we perform extensive ablation studies to illustrate the effects of our method. For better evaluation, we conduct experiments on CIFAR-10 with INPC  $= 50$  if not otherwise stated.

Evaluation of matching strategies. Vanilla GANs synthesize real-looking images, but it lacks supervision for generating discriminative images. Here, we investigate the effects of three matching strategies (feature, gradient, and logits matching) for DiM on CIFAR-10. As shown in Tab. [4,](#page-5-1) one can find that three matching strategies consistently improve vanilla GANs with  $3\% \sim 6\%$  on ConvNet-3 (C-3), ResNet-10 (R-10), and ResNet-18 (R-18). It proves matching strategies enhance the discrimination of generated images. Among them, gradient matching obtains the worst performances. We attribute it to the fact that the gradient difference fails to provide proper supervision when the distribution gap between synthetic and real images is small. For example, even for two random batches from the original dataset, the gradient difference is still large. Feature matching only constrains the overall distribution of synthetic images, so it performs worse than our logits matching. In DiM, the logits of original batches are regarded as soft labels for synthetic images, which provides more abundant supervision for the generator  $[15]$ . Meanwhile, logits matching is not required to be optimized category by category, which largely improves the training efficiency.

Exploring when to perform matching.  $N$  is a hyperparameter that determines in which epoch starts the training with logit matching loss. We vary  $N$  from 20 to 150 and show the results in Tab. [5.](#page-5-2) We report the results of ConvNet-3 (C-3) and ResNet-10 (R-10), respectively. The

<span id="page-6-3"></span><span id="page-6-2"></span>Image /page/6/Figure/0 description: This image contains three plots. Plot (a) is a bar chart titled "Evaluation of λ." It shows the accuracy (%) on the y-axis and λ on the x-axis. There are two sets of bars, one for ConvNet-3 (gray) and one for ResNet-10 (red). The accuracies for ConvNet-3 at λ values of 0.0, 0.005, 0.01, 0.05, 0.2, 0.5, and 1.0 are approximately 68.2, 72.2, 72.5, 72.0, 70.5, 68.5, and 67.0 respectively. The accuracies for ResNet-10 at the same λ values are approximately 69.0, 73.5, 74.2, 71.5, 70.0, 69.5, and 69.0 respectively. Plot (b) is a line graph titled "Ablation of batch size." It shows the accuracy (%) on the y-axis and Batch size B on the x-axis. The x-axis is on a logarithmic scale. There are three lines representing ConvNet-3 (yellow), ResNet-10 (red), and ResNet-18 (blue). The accuracies for ConvNet-3 at batch sizes of 16, 32, 64, 128, 256, and 384 are approximately 70.5, 71.5, 72.0, 71.0, 70.5, and 70.0 respectively. The accuracies for ResNet-10 at the same batch sizes are approximately 72.0, 73.5, 74.5, 72.5, 70.5, and 69.5 respectively. The accuracies for ResNet-18 at the same batch sizes are approximately 73.5, 75.0, 74.5, 73.0, 72.0, and 71.5 respectively. Plot (c) is a line graph titled "Evaluation of scalability and efficiency." It shows the accuracy (%) on the y-axis and INPC on the x-axis. The x-axis is on a logarithmic scale. There are six lines representing ConvNet-3 (yellow), ResNet-10 (green), ResNet-18 (blue), ResNet-34 (dark green), ResNet-50 (pink), and DenseNet-121 (red). The accuracies for all models increase with INPC, generally reaching a plateau around INPC=100. For example, at INPC=1, ConvNet-3 has an accuracy of about 50%, ResNet-10 about 42%, ResNet-18 about 40%, ResNet-34 about 38%, ResNet-50 about 35%, and DenseNet-121 about 40%. At INPC=200, all models have accuracies around 77-78%.

Figure 3: (a) evaluates the sensitiveness of  $\lambda$  in DiM with ConvNet-3 (C-3) and ResNet-10 (R-10). (b) shows ablations of different batch sizes on C-3, R-10, and R-18. (c) explores scalability to large architectures and re-deployment efficiency of DiM on C-3, R-10, R-18, R-34, R-50, and DenseNet-121. All the results are implemented on CIFAR-10 with INPC = 50. DiM achieves the highest results with  $\lambda = 0.01$  and  $B = 64$ . Our method also peform strong scalability to large architectures and high efficiency of redeployment. Best viewed in color.

<span id="page-6-1"></span>Table 6: Explorations of the diversity of models pool. C-3 denotes ConvNet-3. We utilize both average and max pooling for ResNet-10 (R-10) and ResNet-18 (R-18). Bold entries are best results.

| L-Match. Models |      |      | Deployment Acc. (%) |             |             | Avg.        |
|-----------------|------|------|---------------------|-------------|-------------|-------------|
| C-3             | R-10 | R-18 | C-3                 | R-10        | R-18        | Avg.        |
|                 |      |      | 72.0                | 73.5        | 73.9        | 73.1        |
|                 | ✓    |      | 72.1                | 73.2        | 74.2        | 73.2        |
|                 |      | ✓    | 72.2                | 73.4        | 74.3        | 73.3        |
| ✓               | ✓    |      | 72.4                | 73.6        | 73.7        | 73.2        |
| ✓               |      | ✓    | 72.4                | 73.4        | 74.5        | 73.4        |
|                 | ✓    | ✓    | 72.5                | 73.6        | 74.6        | 73.6        |
| ✓               | ✓    | ✓    | <b>72.6</b>         | <b>74.1</b> | <b>75.0</b> | <b>73.9</b> |

performance goes up gradually from the 20th to the 120th epoch. The best result is achieved at  $N = 120$ . After the 120th epoch, the performances converge in the range around 72.5 on C-3 and 74.1 on R-10. It can be explained by that adding logits matching too early disturbs the optimization of GANs and too late obtains few extra gains from GANs.

Exploring the diversity of models pool. We expect the model pool to provide various supervision that facilitates to synthesize images helpful for downstream classification tasks. To explore the effect of the diversity in the models pool, we design an experiment to ablate it in Tab. [6.](#page-6-1) We employ three architectures (C-3, R-10, R-18) in our models pool for logits matching. For testing, we evaluate trained  $G$  on random initializated architectures. The high diversity models pool obtains higher results than low diversity ones, which demonstrates various supervisions are crucial for the generator to distill information from the original dataset.

<span id="page-6-0"></span>Table 7: Illustration of the performance (%) on unseen architectures. DiM shows strong generalization on various architectures. The distillation architecture is ConvNet-3. All results are evaluated on CIFAR-10 with 10 INPC or IPC. Bold entries are best results.

| Method | Evaluation Model |                |                |                |
|--------|------------------|----------------|----------------|----------------|
|        | DiM              | ConvNet-3      | ResNet-18      | VGG-11         |
| DiM    | $66.2 \pm 0.5$   | $69.2 \pm 0.3$ | $66.8 \pm 0.5$ | $67.3 \pm 0.9$ |
| FRePo  | $65.5 \pm 0.4$   | $57.7 \pm 0.7$ | $59.4 \pm 0.7$ | $61.9 \pm 0.7$ |
| MTT    | $64.3 \pm 0.7$   | $46.4 \pm 0.6$ | $50.3 \pm 0.8$ | $34.2 \pm 2.6$ |
| DSA    | $52.1 \pm 0.4$   | $42.8 \pm 1.0$ | $43.2 \pm 0.5$ | $35.9 \pm 1.3$ |
| KIP    | $47.6 \pm 0.9$   | $36.8 \pm 1.0$ | $42.1 \pm 0.4$ | $24.4 \pm 3.9$ |

Another interesting finding is DiM can distill datasets using a small-scale architecture and be deployed with large-scale architectures to obtain higher downstream performances, such as distillation with C-3 and deployment on R-10 or R-18. It largely saves the cost of the most time-consuming re-distillation. We also investigate our DiM with more complex architectures, such as R-10 and R-18. Previous results in [\[19,](#page-8-8) [46,](#page-9-5) [43,](#page-9-7) [38\]](#page-9-6) show that matching with complex architectures perform worse than C-3 in DD settings. However, our DiM performs better matched with complex architectures, which is the first time the performance on complex architectures outperforms simple architectures.

Cross-Architecture Generalization. Previous works [\[38,](#page-9-6) [46,](#page-9-5) [43,](#page-9-7) [28,](#page-8-24) [3\]](#page-8-9) show the cross-architecture generalization among ConvNet-3 [\[8\]](#page-8-25), ResNet-18 [\[14\]](#page-8-0), VGG-11 [\[35\]](#page-9-1), and AlexNet [\[22\]](#page-8-26). They distill on CIFAR-10 using ConvNet-3 and evaluate synthetic images on remained architectures. We compare cross-architecture generalization performances to previous state-of-the-art methods in Tab.

Table 8: Performance comparison to GANs.

<span id="page-7-2"></span><span id="page-7-1"></span>

| <b>INPC</b> | C-3 / R-10 + GANs | C-3 / R-10 + DiM   |
|-------------|-------------------|--------------------|
| 1           | 46.4 / 38.9       | <b>51.3 / 42.5</b> |
| 10          | 62.7 / 60.7       | <b>66.2 / 66.7</b> |
| 50          | 68.1 / 68.7       | <b>72.6 / 74.1</b> |

[7.](#page-6-0) We follow the setting of MTT [\[3\]](#page-8-9) to evaluate crossarchitecture generalization on CIFAR-10 with INPC=10. DiM shows a significant improvement in cross-architecture generalization. Specifically, DiM outperforms MTT [\[3\]](#page-8-9), DSA [\[43\]](#page-9-7), FrePo [\[48\]](#page-9-10), and KIP [\[29\]](#page-8-10) with 6% ~ 43% among all evaluation architectures. Thanks to the strong information capacity of the generative model, DiM is the first work where ResNet-18 performs better than ConvNet-3.

Evaluation of  $\lambda$ . As default, logits matching loss  $L_m$  is added to train generator from 120th epoch. We evaluate the sensitiveness of the trade-off hyper-parameter  $\lambda$  between  $L_g$ and  $L_m$ . As shown in Fig. [3a,](#page-6-2) one can find that  $\lambda = 0.01$ perform the highest result on CIFAR-10. Too large  $\lambda$ , such as  $\lambda = 1.0$ , reduces the effect of  $L_g$ , so the semantic information of synthetic images can not be guaranteed. Conversely, too small  $\lambda$  degrades the generator to vanilla GANs. Thus the discrimination of synthetic images may be lost. To better understand the relation between synthetic images and  $\lambda$ , we show more visualizations in the supplementary file.

**Exploring the influence of B.** We use  $B$  to represent the batch size of real images during the logits matching. The experiment results are shown in Fig. [3b.](#page-6-2) To explore the general characteristic of  $B$ , we study the sensitiveness of  $B$  from 16 to 384 on C-3, R-10, and R-18. Our defaulted  $B = 64$  consistently obtains the highest result with C-3, R-10, and R-18. Too large B provides more information in each iteration, but it also increases the difficulties of optimization. Therefore, the performance drops significantly. Too small B, such as  $B = 16$ , can only provide limited supervision from the original dataset in each iteration. Thus it also performs poorly.

Evaluating the effort of generation in DiM. Compared to previous works [\[46,](#page-9-5) [38,](#page-9-6) [3,](#page-8-9) [19,](#page-8-8) [36\]](#page-9-9), DiM needs to generate training images on the fly. To evaluate the effort of this step, we design a experiment to compare the time cost of image generating and training on different architectures. As shown in Tab. [9,](#page-7-0) the extra cost of DiM is light and can be ignored in most cases (R-50 and D-121). The effort of images generation takes up 2%  $\sim$  6% of the whole cost on D-121, R-50, and R-34. Even on R-18, R-10, and C-3, the cost of generator only takes up  $11\% \sim 20\%$ .

Scalability to large model and efficiency of redeployment. As aforementioned, there are mainly two limitations of previous works. The first one is poor scalability. For example, the images synthesized by small archi-

<span id="page-7-0"></span>Table 9: Evaluating the effort of generator. We show the effort of image generation and training separately. Gen. denotes the generation step. Performed with batch size of 128.

| Part / Arch. | C-3  | R-10 | R-18 | R-34 | R-50 | D-121 |
|--------------|------|------|------|------|------|-------|
| Gen. $(ms)$  | 2    | 2    | 2    | 2    | 2    | 2     |
| Train $(ms)$ | 8    | 12   | 17   | 32   | 45   | 80    |
| Ratio        | 0.20 | 0.14 | 0.11 | 0.06 | 0.04 | 0.02  |

tectures always perform worse on larger ones [\[46,](#page-9-5) [43,](#page-9-7) [38\]](#page-9-6). The second one is low re-deployment efficiency. Previous DD methods [\[46,](#page-9-5) [43,](#page-9-7) [38\]](#page-9-6) need to re-distill when INPC changes, which is time-consuming and results in high computational costs. To investigate the architecture scalability and re-deployment efficiency of DiM, we evaluate DiM on ResNets  $[14]$  and DenseNet-121  $[16]$  with INPC = 1, 10, 20, 50, 100, 200 in Fig.  $3c$ . We are able to conclude that: i). The model capacity matches the size of INPC. Simple architectures perform better with small INPC, while complex architectures perform better with large INPC. For example, under INPC = 1, ConvNet-3 outperforms ResNet-34, ResNet-50, and DenseNet-121 by 12.6%, 15.8%, and 9.4%, respectively. ii). Except INPC =  $1$ , DiM shows strong scalability to larger model, while previous works drop largely when applied on large model, see in Tab. [7.](#page-6-0) iii). As the optimization is conducted on generative models instead of images, our Dim is only required to be trained once for various subsequent applications. (*i.e*. no re-distillation requirement). Meanwhile, the performance on INPC=100, 150, and 200 increases significantly than that on small INPC.

Comparisons to GANs. We compare the performances of 1, 10, and 50 INPC of GANs and DiM on ConvNet-3  $(C-3)$  and ResNet-10 (R-10). As shown in Tab. [8,](#page-7-1) DiM achieves the highest results in all cases. Compare to GANs, the improvement of DiM is up to 5%, which indicates DiM distills more discriminative knowledge from original train dataset. This conclusion is also aligned with our analysis: GAN aims to generate images that look real while DiM targets for synthesizing samples helpful for downstream classification training.

### 5. Conclusion and Discussion

In this work, we propose a novel paradigm to distill information from original train set to model instead of synthetic images, termed as DiM. The DiM consists of a models pool to provide various supervisions for distilling, and a logits matching loss that minimizes the logits differences between real and synthetic images. The simple yet effective design brings DiM significant improvements over previous DiI methods in large architecture scalability, redeployment efficiency, and cross-architecture generality.

Limitations and Broader Impacts. Our DiM needs to

<span id="page-8-29"></span>generate training samples at the deployment stage, which leads to extra efforts for downstream tasks. We also evaluate the influence of it in supplementary materials. In the future, we aim to design light-weighted generative model for DiM. Meanwhile, we plan to explore the effects of DiM on largescale dataset such as ImageNet [\[5\]](#page-8-27) and other tasks such as object detection  $[24]$ , semantic segmentation  $[47]$ , video understanding [\[37\]](#page-9-18), and so on. The proposed method does not contain any studies involving affecting ethics or human rights performed by any of the authors.

### References

- <span id="page-8-19"></span>[1] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020. [5](#page-4-1)
- <span id="page-8-18"></span>[2] Andrew Brock, Jeff Donahue, and Karen Simonyan. Large scale gan training for high fidelity natural image synthesis. *arXiv preprint arXiv:1809.11096*, 2018. [3](#page-2-1)
- <span id="page-8-9"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022. [1,](#page-0-1) [2,](#page-1-1) [4,](#page-3-1) [5,](#page-4-1) [7,](#page-6-3) [8](#page-7-2)
- <span id="page-8-17"></span>[4] Xi Chen, Yan Duan, Rein Houthooft, John Schulman, Ilya Sutskever, and Pieter Abbeel. Infogan: Interpretable representation learning by information maximizing generative adversarial nets. *NeurIPS*, 29, 2016. [3](#page-2-1)
- <span id="page-8-27"></span>[5] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, pages 248–255. Ieee, 2009. [9](#page-8-29)
- <span id="page-8-13"></span>[6] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. *arXiv preprint arXiv:2206.02916*, 2022. [2,](#page-1-1) [3](#page-2-1)
- <span id="page-8-2"></span>[7] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020. [1](#page-0-1)
- <span id="page-8-25"></span>[8] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4367–4375, 2018. [7](#page-6-3)
- <span id="page-8-5"></span>[9] Ross Girshick, Jeff Donahue, Trevor Darrell, and Jitendra Malik. Rich feature hierarchies for accurate object detection and semantic segmentation. In *CVPR*, pages 580–587, 2014. [1](#page-0-1)
- <span id="page-8-16"></span>[10] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. In *NeurIPS*, 2014. [2,](#page-1-1) [5](#page-4-1)
- <span id="page-8-14"></span>[11] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial networks. *Communications of the ACM*, 63(11):139–144, 2020. [2,](#page-1-1) [3,](#page-2-1) [5](#page-4-1)
- <span id="page-8-6"></span>[12] Kaiming He, Georgia Gkioxari, Piotr Dollár, and Ross Girshick. Mask r-cnn. In *ICCV*, pages 2961–2969, 2017. [1](#page-0-1)

- <span id="page-8-7"></span>[13] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Delving deep into rectifiers: Surpassing human-level performance on imagenet classification. In *ICCV*, pages 1026– 1034, 2015. [1](#page-0-1)
- <span id="page-8-0"></span>[14] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, pages 770–778, 2016. [1,](#page-0-1) [2,](#page-1-1) [7,](#page-6-3) [8](#page-7-2)
- <span id="page-8-23"></span>[15] Geoffrey Hinton, Oriol Vinyals, Jeff Dean, et al. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2(7), 2015. [6](#page-5-3)
- <span id="page-8-11"></span>[16] Gao Huang, Zhuang Liu, Laurens Van Der Maaten, and Kilian Q Weinberger. Densely connected convolutional networks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4700–4708, 2017. [2,](#page-1-1) [8](#page-7-2)
- <span id="page-8-4"></span>[17] Phillip Isola, Jun-Yan Zhu, Tinghui Zhou, and Alexei A Efros. Image-to-image translation with conditional adversarial networks. In *CVPR*, pages 1125–1134, 2017. [1,](#page-0-1) [3](#page-2-1)
- <span id="page-8-15"></span>[18] Tero Karras, Samuli Laine, Miika Aittala, Janne Hellsten, Jaakko Lehtinen, and Timo Aila. Analyzing and improving the image quality of stylegan. In *CVPR*, pages 8110–8119, 2020. [2,](#page-1-1) [3,](#page-2-1) [5](#page-4-1)
- <span id="page-8-8"></span>[19] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *ICML*, 2022. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [5,](#page-4-1) [6,](#page-5-3) [7,](#page-6-3) [8,](#page-7-2) [11](#page-10-0)
- <span id="page-8-22"></span>[20] Diederik P Kingma and Jimmy Ba. Adam: A method for stochastic optimization. In *ICLR*, 2015. [5](#page-4-1)
- <span id="page-8-21"></span>[21] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [5](#page-4-1)
- <span id="page-8-26"></span>[22] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Communications of the ACM*, 60(6):84–90, 2017. [7](#page-6-3)
- <span id="page-8-20"></span>[23] Yann LeCun, Léon Bottou, Yoshua Bengio, Patrick Haffner, et al. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998. [5](#page-4-1)
- <span id="page-8-28"></span>[24] Tsung-Yi Lin, Michael Maire, Serge Belongie, James Hays, Pietro Perona, Deva Ramanan, Piotr Dollár, and C Lawrence Zitnick. Microsoft coco: Common objects in context. In *ECCV*, pages 740–755. Springer, 2014. [9](#page-8-29)
- <span id="page-8-1"></span>[25] Ze Liu, Yutong Lin, Yue Cao, Han Hu, Yixuan Wei, Zheng Zhang, Stephen Lin, and Baining Guo. Swin transformer: Hierarchical vision transformer using shifted windows. In *ICCV*, pages 10012–10022, 2021. [1](#page-0-1)
- <span id="page-8-3"></span>[26] Jonathan Long, Evan Shelhamer, and Trevor Darrell. Fully convolutional networks for semantic segmentation. In *CVPR*, pages 3431–3440, 2015. [1](#page-0-1)
- <span id="page-8-12"></span>[27] Mehdi Mirza and Simon Osindero. Conditional generative adversarial nets. *arXiv preprint arXiv:1411.1784*, 2014. [2,](#page-1-1) [3,](#page-2-1) [5,](#page-4-1) [11](#page-10-0)
- <span id="page-8-24"></span>[28] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [7](#page-6-3)
- <span id="page-8-10"></span>[29] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *NeurIPS*, 34:5186–5198, 2021. [1,](#page-0-1) [5,](#page-4-1) [8](#page-7-2)

- <span id="page-9-3"></span>[30] Omkar M Parkhi, Andrea Vedaldi, and Andrew Zisserman. Deep face recognition. 20[1](#page-0-1)5. 1
- <span id="page-9-0"></span>[31] Shaoqing Ren, Kaiming He, Ross Girshick, and Jian Sun. Faster r-cnn: Towards real-time object detection with region proposal networks. *NeurIPS*, 28, 2015. [1](#page-0-1)
- <span id="page-9-16"></span>[32] Pierre Sermanet, Soumith Chintala, and Yann LeCun. Convolutional neural networks applied to house numbers digit classification. In *ICPR*, 2012. [5](#page-4-1)
- <span id="page-9-13"></span>[33] Yujun Shen, Jinjin Gu, Xiaoou Tang, and Bolei Zhou. Interpreting the latent space of gans for semantic face editing. In *CVPR*, pages 9243–9252, 2020. [2,](#page-1-1) [3,](#page-2-1) [5](#page-4-1)
- <span id="page-9-2"></span>[34] Karen Simonyan and Andrew Zisserman. Two-stream convolutional networks for action recognition in videos. *NeurIPS*, 27, 2014. [1](#page-0-1)
- <span id="page-9-1"></span>[35] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014. [1,](#page-0-1) [2,](#page-1-1) [7](#page-6-3)
- <span id="page-9-9"></span>[36] Liu Songhua, Wang Kai, Yang Xingyi, Ye Jingwen, and Wang Xinchao. Dataset distillation via factorization. *NeurIPS*, 2022. [2,](#page-1-1) [5,](#page-4-1) [6,](#page-5-3) [8](#page-7-2)
- <span id="page-9-18"></span>[37] Khurram Soomro, Amir Roshan Zamir, and Mubarak Shah. Ucf101: A dataset of 101 human actions classes from videos in the wild. *arXiv preprint arXiv:1212.0402*, 2012. [9](#page-8-29)
- <span id="page-9-6"></span>[38] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, pages 12196–12205, 2022. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [5,](#page-4-1) [7,](#page-6-3) [8,](#page-7-2) [11](#page-10-0)
- <span id="page-9-4"></span>[39] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-1) [5](#page-4-1)
- <span id="page-9-12"></span>[40] Weihao Xia, Yulun Zhang, Yujiu Yang, Jing-Hao Xue, Bolei Zhou, and Ming-Hsuan Yang. Gan inversion: A survey. *T-PAMI*, 2022. [2,](#page-1-1) [3,](#page-2-1) [5](#page-4-1)
- <span id="page-9-15"></span>[41] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashionmnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017. [5](#page-4-1)
- <span id="page-9-11"></span>[42] David Junhao Zhang, Heng Wang, Chuhui Xue, Rui Yan, Wenqing Zhang, Song Bai, and Mike Zheng Shou. Dataset condensation via generative model, 2023. [2](#page-1-1)
- <span id="page-9-7"></span>[43] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, pages 12674– 12685. PMLR, 2021. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [5,](#page-4-1) [6,](#page-5-3) [7,](#page-6-3) [8,](#page-7-2) [11](#page-10-0)
- <span id="page-9-8"></span>[44] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv*, 1(2):3, 2021. [1,](#page-0-1) [2,](#page-1-1) [4,](#page-3-1) [5](#page-4-1)
- <span id="page-9-14"></span>[45] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. *arXiv preprint arXiv:2204.07513*, 2022. [3](#page-2-1)
- <span id="page-9-5"></span>[46] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [5,](#page-4-1) [7,](#page-6-3) [8,](#page-7-2) [11](#page-10-0)
- <span id="page-9-17"></span>[47] Bolei Zhou, Hang Zhao, Xavier Puig, Sanja Fidler, Adela Barriuso, and Antonio Torralba. Scene parsing through ade20k dataset. In *CVPR*, pages 633–641, 2017. [9](#page-8-29)

<span id="page-9-10"></span>[48] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *NeurIPS*, 2022. [2,](#page-1-1) [4,](#page-3-1) [5,](#page-4-1) [8](#page-7-2)

### <span id="page-10-0"></span>6. More Evaluations and Comparisons

In this section, we provide more evaluations and comparisons of DiM. Firstly, we explore the influence of the dimension of random noise  $K$ . Secondly, we evaluate the effort of image generation in deployment stage. Finally, the details of implementation hardware are shown.

Evaluation of  $K$ . We default to set the dimension of  $K = 100$  for the conditional GAN [\[27\]](#page-8-12) in experiments. To investigate the influence of the dimension, we design an ab-lation of K. As shown in Tab. [10,](#page-10-1) the defaulted  $K = 100$ achieves the highest results on ConvNet-3 (C-3), ResNet-10  $(R-10)$ , and ResNet-18  $(R-18)$ . For K from 32 to 100, the average performance increases from 71.8% to 73.9% and drops gradually when  $K > 100$ . Both too small and too large K leads difficulties for optimization.

<span id="page-10-1"></span>Table 10: Exploring the dimension of  $K$ . The best result is marked in bold.

| Acc. / Dim. of $K$   32   64   100   128   200 |                                                                                                                                                                                                               |  |  |
|------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|
| $C-3$ (%)                                      | $\begin{array}{ c c c c c c } \hline 70.5 & 71.0 & \textbf{72.6} & 68.9 & 68.2 \\ \hline 72.3 & 72.5 & \textbf{74.1} & 69.7 & 69.4 \\ \hline 72.6 & 72.8 & \textbf{75.0} & 72.1 & 70.2 \\ \hline \end{array}$ |  |  |
| $R-10(%)$                                      |                                                                                                                                                                                                               |  |  |
| $R-18(%)$                                      |                                                                                                                                                                                                               |  |  |
| Avg. Acc.                                      | 71.8   72.1   <b>73.9</b>   70.2   69.3                                                                                                                                                                       |  |  |

Details of implementation hardware We perform all experiments on single Tesla V100 32G GPU of Linux Cluster with a 2.40 GHz Intel (R) Xeon (R) Platinum 8260 CPU (24 cores).

### 7. More Visualizations

The architecture of GAN. To better understand our DiM, we also visualize the detailed architecture of the generator. As illustrated in Fig. [4,](#page-11-0) the generator consists of a Fully-Connected (FC) layer, BN, ReLU, 3 ConvTranspose2d layers, BN, ReLu, 4 blocks of CONV-BN-ReLU, CONV, and tanh activation. The input and output dimensions of FC are 100 and 194\*4\*4. The input and output channels of the last CONV are 196 and 3. More details can be checked from the zip file of our code.

Synthetic images during the training. We visualize the synthetic images during the training process and show them in Fig. [5.](#page-11-1) One can find that the image quality gradually increase with the training.

Synthetic images of GANs and DiM. We visualize the synthetic images during the training and the evaluation performances of GAN and DiM, respectively. We show the results of C-3 and R-10 at 130, 150, 170, and 190 epochs. As shown in Fig. [6,](#page-12-0) DiM outperforms GAN at all epochs with 4%∼5.5%, which indicates the strong discrimination of training images generated by DiM.

Generated images with other matching strategies. Previous works [\[46,](#page-9-5) [43,](#page-9-7) [38,](#page-9-6) [19\]](#page-8-8) have explored gradient and feature/distribution matching strategies. We also compare our logits matching to these strategies in experiment section. The images generated by gradient and feature matching are shown in Fig. [7.](#page-12-1)

<span id="page-11-0"></span>Image /page/11/Figure/0 description: This is a diagram of a neural network architecture. It begins with an input labeled "Noises:Z" with a dimension of 100. This input is fed into a Fully Connected (FC) layer, followed by Batch Normalization (BN) and a ReLU activation. Next, there is a Conv Transpose2d layer with dimensions 196x196, followed by BN and ReLU. This is followed by three consecutive CONV-BN-ReLU blocks. Then, another Conv Transpose2d layer (196x196) is applied, followed by BN and ReLU. This is succeeded by a CONV-BN-ReLU block, another Conv Transpose2d layer (196x196), BN, and ReLU. The network concludes with a CONV layer with dimensions 196x3, followed by a Tanh activation.

Figure 4: Visualization of the architecture of generator in DiM.

<span id="page-11-1"></span>Image /page/11/Figure/2 description: The image displays a grid of six smaller grids, each labeled with an epoch number: Epoch 0, Epoch 40, Epoch 80, Epoch 120, Epoch 160, and Epoch 200. Each of these smaller grids contains numerous square images, likely representing generated samples from a machine learning model at different stages of training. The images in Epoch 0 appear abstract and noisy, while the images in subsequent epochs show increasing clarity and recognizable objects such as cars, animals (cats, dogs, deer, birds, horses), and possibly airplanes and boats.

Figure 5: Visualization of synthetic images during training. We show the 10 images per class synthetic set as an example.

<span id="page-12-0"></span>Image /page/12/Figure/0 description: This image displays a grid of generated images from two different models, GAN and DiM, at various training epochs (130, 150, 170, and 190). Each model's results are presented in two rows. The top row shows GAN results with corresponding C3 and R10 scores for each epoch: Epoch 130 (C3: 67.3; R10: 68.1), Epoch 150 (C3: 67.9; R10: 68.5), Epoch 170 (C3: 68.1; R10: 68.7), and Epoch 190 (C3: 68.0; R10: 68.6). The bottom row shows DiM results with their respective scores: Epoch 130 (C3: 71.5; R10: 72.9), Epoch 150 (C3: 72.1; R10: 73.8), Epoch 170 (C3: 72.5; R10: 74.0), and Epoch 190 (C3: 72.6; R10: 74.1). The generated images within each grid appear to be diverse, showcasing various objects and scenes.

Figure 6: Visualization of synthetic images during training. Due to the limitation of space, we only show the 10 images per class synthetic set as an example.

<span id="page-12-1"></span>Image /page/12/Figure/2 description: The image displays a grid of images organized into two rows and four columns, each column representing a different epoch (130, 150, 170, and 190). The top row is labeled "Gradient Matching" and the bottom row is labeled "Feature Matching". Each cell within the grid contains a small image, likely representing samples from a dataset. The images appear to be diverse, featuring various objects and animals such as cars, airplanes, dogs, cats, horses, and birds.

**Feature Matching**

Figure 7: Visualization of the images generated by gradient and feature matching at 130, 150, 170, and 190 epochs.
Remark 23.25. As I already said at the beginning of this chapter, the heat equation can be seen as a gradient flow in various ways. For instance, take for simplicity the basic heat equation in  $\mathbb{R}^n$ , in the form  $\partial_t u = \Delta u$ , then it can be interpreted as the gradient flow of the functional  $E(u) = (1/2) \int |\nabla u|^2$  for the usual Hilbert structure imposed by the  $L^2$  norm; or as the gradient flow of the functional  $E(u) = \int u^2$  for the Hilbert structure induced by the  $H^{-1}$  norm (say on the subspace  $\int u = 0$ ). But the interesting new feature coming from Theorem 23.19 is that now the heat equation can be seen as the gradient flow of a nice functional which has statistical (or thermodynamical) meaning; and in such a way that it is naturally set in the space of probability measures. There are other reasons why this new interpretation seems "natural"; see the bibliographical notes for more information.

## Stability

A good point of our weak formulation of gradient flows is that it comes with stability estimates almost for free. This is illustrated by the next theorem, in which regularity assumptions are far from optimal.

Theorem 23.26 (Stability of gradient flows in the Wasserstein space). Let  $\mu_t$ ,  $\hat{\mu}_t$  be two solutions of (23.79), satisfying the assumptions of Theorem 23.19 with either  $K \geq 0$  or  $N = \infty$ . Let  $\lambda = K_{\infty, U}$  if  $N = \infty$ ; and  $\lambda = 0$  if  $N < \infty$  and  $K \geq 0$ . Then, for all  $t \geq 0$ ,

$$
W_2(\mu_t, \widehat{\mu}_t) \le e^{-\lambda t} W_2(\mu_0, \widehat{\mu}_0).
$$

Remark 23.27. I don't know how seriously the restrictions on K and  $N$  should be taken.

*Proof of Theorem 23.26.* By Theorem 23.9, for almost any  $t$ ,

$$
\frac{d}{dt}\left(\frac{W_2(\mu_t,\widehat{\mu}_t)^2}{2}\right) = \int \langle \widetilde{\nabla}\psi_t, \nabla U'(\rho_t) \rangle \, d\mu_t + \int \langle \widetilde{\nabla}\widehat{\psi}_t, \nabla U'(\widehat{\rho}_t) \rangle \, d\widehat{\mu}_t,\tag{23.82}
$$

where  $\exp(\nabla \psi_t)$  (resp.  $\exp(\nabla \psi_t)$ ) is the optimal transport  $\mu_t \to \hat{\mu}_t$ (resp.  $\hat{\mu}_t \rightarrow \mu_t$ ).

By the chain-rule and Theorem 23.14,

692 23 Gradient flows I

$$
\int \langle \widetilde{\nabla} \psi_t, \nabla U'(\rho_t) \rangle d\mu_t = \int \langle \widetilde{\nabla} \psi_t, \nabla p(\rho_t) \rangle d\nu
$$
  
$$
\leq U_{\nu}(\widehat{\mu}_t) - U_{\nu}(\mu_t) - \lambda \frac{W_2(\mu_t, \widehat{\mu}_t)^2}{2} \quad (23.83)
$$

Similarly,

$$
\int \langle \widetilde{\nabla} \widehat{\psi}_t, \nabla U'(\widehat{\rho}_t) \rangle d\widehat{\mu}_t \le U_{\nu}(\mu_t) - U_{\nu}(\widehat{\mu}_t) - \lambda \frac{W_2(\widehat{\mu}_t, \mu_t)^2}{2}.
$$
 (23.84)

The combination of  $(23.82)$ ,  $(23.83)$  and  $(23.84)$  implies

$$
\frac{d}{dt}\left(\frac{W_2(\mu_t,\widehat{\mu}_t)^2}{2}\right) \leq -2\lambda \left(\frac{W_2(\mu_t,\widehat{\mu}_t)^2}{2}\right).
$$

Then the result follows from Gronwall's lemma. □

This is a blank image.

## General theory and time-discretization

This last section evokes some key issues which I shall not develop, although they are closely related to the material in the rest of this chapter.

There is a general theory of gradient flows in metric spaces, based for instance on Definition 23.7, or other variants appearing in Proposition 23.1. Motivations for these developments come from both pure and applied mathematics. This theory was pushed to a high degree of sophistication by many researchers, in particular De Giorgi and his school. A key role is played by discrete-time **approximation schemes**, the simplest of which can be stated as follows:

1. Choose your initial datum  $X_0$ ;

- 2. Choose a time step  $\tau$ , which in the end will decrease to 0;
- 3. Let  $X_1^{(\tau)}$  $\mathcal{A}_1^{(\tau)}$  be a minimizer of  $X \longmapsto \Phi(X) + \frac{d(X_0, X)^2}{2\tau}$  $\frac{1}{2\tau}$ ; then define inductively  $X_{k+1}^{(\tau)}$  as a minimizer of  $X \longmapsto \Phi(X) + \frac{d(X_k^{(\tau)})}{2\tau}$  $_k^{(\tau)}, X)^2$  $rac{1}{2\tau}$ .

4. Pass to the limit in  $X_k^{(\tau)}$  $k$ <sup>(1)</sup> as  $\tau \to 0$ ,  $k\tau \to t$ , hopefully recover a function  $X(t)$  which is the value of the gradient flow at time t.

Such schemes sometimes provide an excellent way to construct the gradient flow, and they may be useful in numerical simulations. They also give a more precise formulation of the statement according to which gradient flows make the energy decrease "as fast as possible".

There are strong results for the convergence of time-discretized gradient flow as  $\tau \to 0$ ; see the bibliographical notes for details.<sup>1</sup>

The time-discretization procedure also suggests a better understanding of the gradient flow in Wasserstein distance, as I shall explain in a slightly informal way. Consider, as in Theorem 23.19, the partial differential equation

$$
\frac{\partial \rho}{\partial t} = L p(\rho).
$$

Suppose you know the density  $\rho(t)$  at some time t, and look for the density  $\rho(t + dt)$  at a later time, where dt is infinitesimally small. To do this, minimize the quantity

$$
U_{\nu}(\mu_{t+dt}) - U_{\nu}(\mu_t) + \frac{W_2(\mu_t, \mu_{t+dt})^2}{2 dt}.
$$

By using the interpretation of the Wasserstein distance between infinitesimally close probability measures, this can also be rewritten as

$$
\frac{W_2(\mu_t, \mu_{t+dt})^2}{dt} \simeq \inf \left\{ \int |v|^2 \, d\mu_t; \quad \frac{\partial \mu}{\partial t} + \nabla \cdot (\mu v) = 0 \right\}.
$$

To go from  $\mu(t)$  to  $\mu(t + dt)$ , what you have to do is find a velocity field v inducing an infinitesimal variation  $d\mu = -\nabla \cdot (\mu v) dt$ , so as to minimize the infinitesimal quantity

$$
dU_{\nu} + Kdt, \tag{23.85}
$$

where  $U_{\nu}(\mu) = \int U(\rho) d\nu$ , and K is the kinetic energy  $(1/2) \int |\nu|^2 d\mu$ (so Kdt is the infinitesimal action). For the heat equation  $\frac{\partial \rho}{\partial t} = \Delta \rho$ ,  $\nu = \text{vol}, U_{\nu}(\mu) = \int \rho \log \rho \, d\nu$ , we are back to the example discussed at the beginning of this chapter, and (23.85) can be rewritten as an "infinitesimal variation of free energy", say

$$
Kdt - dS,
$$

with  $S$  standing for the entropy.

 $1$  These notes stop before what some readers might consider as the most interesting part, namely try to construct solutions by use of the gradient flow interpretation.

There is an important moral here: Behind many *nonequilibrium* equations of statistical mechanics, there is a variational principle involving entropy and energy, or functionals alike — just as in equilibrium statistical mechanics.

## Appendix: A lemma about doubling variables

The following important lemma was used in the proof of Theorems 23.9 and 23.26.

Lemma 23.28 (Differentiation through doubling of variables). Let  $F = F(s,t)$  be a function  $[0,T] \times [0,T] \rightarrow \mathbb{R}$ , locally absolutely continuous in s, uniformly in t; and locally absolutely continuous in t, uniformly in s. Then  $t \to F(t,t)$  is absolutely continuous, and for almost all  $t_0$ ,

$$
\frac{d}{dt}\Big|_{t=t_0} F(t,t) \le \limsup_{t \uparrow t_0} \left( \frac{F(t,t_0) - F(t_0,t_0)}{t-t_0} \right) + \limsup_{t \downarrow t_0} \left( \frac{F(t_0,t) - F(t_0,t_0)}{t-t_0} \right). \tag{23.86}
$$

If moreover  $F(t_0, \cdot)$  and  $F(\cdot, t_0)$  are differentiable at all times, for almost all  $t_0$ , then the inequality  $(23.86)$  can be reinforced into the equality

$$
\left. \frac{d}{dt} \right|_{t=t_0} F(t, t) = \left. \frac{d}{dt} \right|_{t=t_0} F(t, t_0) + \left. \frac{d}{dt} \right|_{t=t_0} F(t_0, t). \tag{23.87}
$$

Explicitly, to say that  $F$  is locally absolutely continuous in  $s$ , uniformly in t, means that there is a fixed function  $u \in L^1_{loc}(dt)$  such that

$$
\sup_{0\leq t\leq T} \left| F(s,t) - F(s',t) \right| \leq \int_s^{s'} u(\tau) d\tau.
$$

Remark 23.29. Lemma 23.28 does not allow us to conclude to (23.87) if it is only known that for any  $t_0$ ,  $F(t,t_0)$  and  $F(t_0,t)$  are differentiable almost everywhere as functions of  $t$ . Indeed, it might be a priori that differentiability fails precisely at  $t = t_0$ , for all  $t_0$ .

*Proof of Lemma 23.28.* By assumption there are functions  $u \in L^1_{loc}(dt)$ and  $v \in L^1_{loc}(ds)$  such that

$$
\begin{cases} \sup_{0 \le t \le T} |F(s, t) - F(s', t)| \le \int_s^{s'} u(\tau) d\tau \\ \sup_{0 \le s \le T} |F(s, t) - F(s, t')| \le \int_t^{t'} v(\tau) d\tau. \end{cases}
$$

Without loss of generality we may take  $u = v$ .

Let  $f(t) = F(t, t)$ . Then we have  $|f(s) - f(t)| \leq |F(s, s) - F(s, t)| +$  $|F(s,t) - F(t,t)| \leq 2 \int_s^t u(\tau) d\tau$ ; so f is locally absolutely continuous.

Let  $\dot{f}$  stand for the derivative of  $f$ . Since  $f$  is absolutely continuous,  $\hat{f}$  is also (almost everywhere) the distributional derivative of  $f$ . The goal is to show that  $\dot{f}(t)$  is bounded above by the right-hand side of (23.86). If this is true, then the rest of the proof follows easily: Indeed, if  $F(s,t)$ is differentiable in  $s$  and  $t$  then

$$
\left. \frac{d}{dt} \right|_{t=t_0} F(t,t) \leq \left. \frac{d}{dt} \right|_{t=t_0} F(t,t_0) + \left. \frac{d}{dt} \right|_{t=t_0} F(t_0,t),
$$

and the reverse inequality will be obtained by changing  $F$  for  $-F$ .

Let  $\zeta$  be a  $C^{\infty}$  nonnegative function supported in  $(0, 1)$ . For h small enough,  $\zeta(\cdot + h)$  is also supported in  $(0, 1)$ , and

$$
\int \dot{f}\zeta = -\int f\dot{\zeta} = \lim_{h \downarrow 0} \int_0^1 f(t) \left[ \frac{\zeta(t-h) - \zeta(t)}{h} \right] dt
$$
$$
= \lim_{h \downarrow 0} \int_0^1 \zeta(t) \left[ \frac{f(t+h) - f(t)}{h} \right] dt.
$$

Replacing  $f$  by its expression in terms of  $F$ , we get

$$
\int \dot{f}\zeta = \lim_{h \downarrow 0} \left\{ \int_0^1 \zeta(t) \left[ \frac{F(t+h, t+h) - F(t, t+h)}{h} \right] dt \right. \\ \left. + \int_0^1 \zeta(t) \left[ \frac{F(t, t+h) - F(t, t)}{h} \right] dt \right\} \\ \leq \limsup_{h \downarrow 0} \int_0^1 \zeta(t-h) \left[ \frac{F(t, t) - F(t-h, t)}{h} \right] dt \\ \quad + \limsup_{h \downarrow 0} \int_0^1 \zeta(t) \left[ \frac{F(t, t+h) - F(t, t)}{h} \right] dt.
$$
\n(23.88)

In the first integral on the right-hand side of (23.88), it is possible to replace  $\zeta(t-h)$  by  $\zeta(t)$ ; indeed, since  $\zeta$  and  $\zeta(\cdot-h)$  are supported in  $(\delta, 1 - \delta)$  for some  $\delta > 0$ , we may write

$$
\left| \int_0^1 \left[ \zeta(t-h) - \zeta(t) \right] \left( \frac{F(t,t) - F(t-h,t)}{h} \right) dt \right|
$$
  
\n
$$
\leq ||\zeta||_{\text{Lip}} \int_{\delta}^{1-\delta} |F(t,t) - F(t-h,t)| dt
$$
  
\n
$$
\leq ||\zeta||_{\text{Lip}} \int_0^1 \left( \int_{t-h}^t u(\tau) d\tau \right) dt
$$
  
\n
$$
= ||\zeta||_{\text{Lip}} \int_{\delta}^{1-\delta} \left( \int_{\tau}^{\min(\tau+h,1)} dt \right) u(\tau) d\tau
$$
  
\n
$$
\leq ||\zeta||_{\text{Lip}} h \int_{\delta}^{1-\delta} u(\tau) d\tau = O(h).
$$

To summarize:

$$
\int_0^1 \zeta \dot{f} \le \limsup_{h \downarrow 0} \int_0^1 \zeta(t) \left[ \frac{F(t,t) - F(t-h,t)}{h} \right] dt + \limsup_{h \downarrow 0} \int_0^1 \zeta(t) \left[ \frac{F(t,t+h) - F(t,t)}{h} \right] dt. \quad (23.89)
$$

By assumption,

$$
\left|\frac{F(t,t)-F(t-h,t)}{h}\right| \leq \frac{1}{h} \int_{t-h}^t u(\tau) d\tau;
$$

and by Lebesgue's density theorem, the right-hand side converges to  $\boldsymbol{u}$ in  $L^1_{\text{loc}}(dt)$  as  $h \to 0$ . This makes it possible to apply Fatou's lemma, in the form

$$
\limsup_{h \downarrow 0} \int_0^1 \zeta(t) \left[ \frac{F(t,t) - F(t-h,t)}{h} \right] dt
$$

$$
\leq \int_0^1 \zeta(t) \limsup_{h \downarrow 0} \left[ \frac{F(t,t) - F(t-h,t)}{h} \right] dt. \quad (23.90)
$$

Similarly,

$$
\limsup_{h \downarrow 0} \int_0^1 \zeta(t) \left[ \frac{F(t, t+h) - F(t, t)}{h} \right] dt
$$

$$
\leq \int_0^1 \zeta(t) \limsup_{h \downarrow 0} \left[ \frac{F(t, t+h) - F(t, t)}{h} \right] dt. \quad (23.91)
$$

Plugging (23.90) and (23.91) back in (23.89), we find

$$
\int_0^1 \zeta \dot{f} \le \int \zeta(t) \left\{ \limsup_{h \downarrow 0} \left( \frac{F(t,t) - F(t-h,t)}{h} \right) + \limsup_{h \downarrow 0} \left( \frac{F(t,t+h) - F(t,t)}{h} \right) \right\} dt.
$$

Since  $\zeta$  is arbitrary,  $\dot{f}$  is bounded above by the expression in curly brackets, almost everywhere. This concludes the proof. ⊓⊔

## Bibliographical notes

Historically, the development of the theory of abstract gradient flows was initiated by De Giorgi and coworkers (see e.g. [19, 275, 276]) on the basis of the time-discretized variational scheme; and by Bénilan [95] on the basis of the variational inequalities involving the square distance, as in Proposition  $23.1(iv)$ –(vi). The latter approach has the advantage of incorporating stability and uniqueness as a built-in feature, while the former is more efficient in establishing existence. Bénilan introduced his method in the setting of Banach spaces, but it applies just as well to abstract metric spaces. Both approaches work in the Wasserstein space. De Giorgi also introduced the formulation in Proposition 23.1(ii), which is an alternative "intrinsic" definition for gradient flows in metric spaces.

Basically all methods used to construct abstract gradient flows rely on some convexity property. But the interplay is in both directions: the existence of an energy-decreasing flow satisfying (23.2) implies the λ-convexity of the energy Φ. When (23.2) is replaced by a suitable timeintegrated formulation this becomes a powerful way to study convexity properties in metric spaces; see [271] for a neat application to the study of displacement convexity.

Currently, the reference for abstract gradient flows is the recent monograph by Ambrosio, Gigli and Savaré [30]. (There is a short version in Ambrosio's Santander lecture notes [22], and a review by Gangbo [397].) There the reader will find the most precise results known to this day, apart from some very recent refinements. More than half of the book is devoted to gradient flows in the space of probability

measures on  $\mathbb{R}^n$  (or a separable Hilbert space). Issues about the replacement of  $P_2(\mathbb{R}^n)$  by  $P_2^{\text{ac}}(\mathbb{R}^n)$  are carefully discussed there. Some abstract results from [30] are improved by Gigli [415] (with the help of an interesting quasi-minimization theorem which can be seen as a consequence of the Ekeland variational principle).

The results presented in this chapter extend some of the results in [30] to  $P_2(M)$ , where M is a Riemannian manifold, sometimes at the price of less precise conclusions. In another direction of generalization, Fang, Shao and Sturm [342] have considered gradient flows in  $P_2(\mathcal{W})$ , where  $W$  is an abstract Wiener space.

Other treatments of gradient flows in nonsmooth structures, under various curvature assumptions, are due to Perelman and Petrunin [678], Lytchak  $[584]$ , Ohta  $[655]$  and Savaré  $[735]$ ; the first two references are concerned with Alexandrov spaces, while the latter two deal with so-called 2-uniform spaces. The assumption of 2-uniform smoothness is relevant for optimal transport, since the Wasserstein space over a Riemannian manifold is not an Alexandrov space in general (except for nonnegative curvature). All these works address the construction of gradient flows under various sets of geometric assumptions.

The classical theory of gradient flows in Hilbert spaces, mostly for convex functionals, based on Remark  $23.3$ , is developed in Brézis [171] and other sources; it is also implicitly used in several parts of the popular book by J.-L. Lions [557].

The differentiability of the Wasserstein distance in  $P_2^{\text{ac}}(\mathbb{R}^n)$ , and in fact in  $P_p^{\text{ac}}(\mathbb{R}^n)$  (for  $1 < p < \infty$ ), is proven in [30, Theorems 10.2.2 and 10.2.6, Corollary 10.2.7]. The assumption of absolute continuity of the probability measures is not crucial for the superdifferentiability (actually in [30, Theorem 10.2.2] there is no such assumption). For the subdifferentiability, this assumption is only used to guarantee the uniqueness of the transference plan. Proofs in [30] slightly differ from the proofs in the present chapter.

There is a more general statement that the Wasserstein distance  $W_2(\sigma,\mu_t)$  is almost surely (in t) differentiable along any absolutely continuous curve  $(\mu_t)_{0 \leq t \leq 1}$ , without any assumption of absolute continuity of the measures [30, Theorem 8.4.7]. (In this reference, only the Euclidean space is considered, but this should not be a problem.)

There is a lot to say about the genesis of Theorem 23.14, which can be considered as a refinement of Theorem 20.1. The exact computation of Step 1 appears in [669, 671] for particular functions  $U$ , and in [814, Theorem 5.30] for general functions  $U$ ; all these references only consider  $M = \mathbb{R}^n$ . The procedure of extension of  $\nabla \psi$  (Step 2) appears e.g. in [248, Proof of Theorem 2] (in the particular case of convex functions). The integration by parts of Step 3 appears in many papers; under adequate assumptions, it can be justified in the whole of  $\mathbb{R}^n$  (without any assumption of compact support): see [248, Lemma 7], [214, Lemma 5.12], [30, Lemma 10.4.5]. The proof in [214, 248] relies on the possibility to find an exhaustive sequence of cutoff functions with Hessian uniformly bounded, while the proof in [30] uses the fact that in  $\mathbb{R}^n$ , the distance to a convex set is a convex function. None of these arguments seems to apply in more general noncompact Riemannian manifolds (the second proof would probably work in nonnegative curvature), so I have no idea whether the integration by parts in the proof of Theorem 23.14 could be performed without compactness assumption; this is the reason why I went through the painful<sup>2</sup> approximation procedure used in the end of the proof of Theorem 23.14.

It is interesting to compare the two strategies used in the extension from compact to noncompact situations, in Theorem 17.15 on the one hand, and in Theorem 23.14 on the other. In the former case, I could use the standard approximation scheme of Proposition 13.2, with an excellent control of the displacement interpolation and the optimal transport. But for Theorem 23.14, this seems to be impossible because of the need to control the smoothness of the approximation of  $\rho_0$ ; as a consequence, passing to the limit is more delicate. Further, note that Theorem 17.15 was used in the proof of Theorem 23.14, since it is the convexity properties of  $U_{\nu}$  along displacement interpolation which allows us to go back and forth between the integral and the differential (in the t variable) formulations.

The argument used to prove that the first term of (23.61) converges to 0 is reminiscent of the well-known argument from functional analysis, according to which convergence in weak  $L^2$  combined with convergence of the  $L^2$  norm imply convergence in strong  $L^2$ .

At some point I have used the following theorem: If  $u \in W^{1,1}_{loc}(M)$ , then for any constant c,  $\nabla u = 0$  almost everywhere on  $\{u = c\}$ . This classical result can be found e.g. in [554, Theorem 6.19].

Another strategy to attack Theorem 23.14 would have been to start from the "curve-above-tangent" formulation of the convexity of  $\mathcal{J}_t^{1/N}$  $t^{1/N},$ where  $\mathcal{J}_t$  is the Jacobian determinant. (Instead I used the "curve-below-

<sup>2</sup> Still an intense experience!

chord" formulation of convexity via Theorem 17.15.) I don't know if technical details can be completed with this approach.

The interpretation of the linear Fokker–Planck equation  $\partial_t \rho =$  $\Delta \rho + \nabla \cdot (\rho \nabla V)$  as the limit of a discretized scheme goes back to the pioneering work of Jordan, Kinderlehrer and Otto [493]. In that sense the Fokker–Planck equation can be considered as the abstract gradient flow corresponding to the free energy  $\Phi(\rho) = \int \rho \log \rho + \int \rho V$ . The proof (slightly rewritten) appears in my book [814, Section 8.4]. It is based on the three main estimates which are more or less at the basis of the whole theory of abstract gradient flows: If  $\tau$  is the time step, and  $X_k^{(\tau)}$  $\binom{N}{k}$  the position at step k of the discretized system, then

$$
\begin{cases} \Phi(X_n^{(\tau)}) = O(1); \\ \sum_{j=1}^{\infty} \frac{d(X_j^{(\tau)}, X_{j+1}^{(\tau)})^2}{2\tau} = O(1); \\ \tau \sum_{j=1}^{\infty} \|\text{grad}\Phi(X_j^{(\tau)})\|^2 = O(1). \end{cases}
$$

Here I have assumed that  $\Phi$  is bounded below (which is the case when  $\Phi$  is the free energy functional). When inf  $\Phi = -\infty$ , there are still estimates of the same type, only quite more complicated [30, Section 3.2]. Ambrosio and Savaré recently found a simplified proof of error estimates and convergence for time-discretized gradient flows [34].

Otto applied the same method to various classes of nonlinear diffusion equations, including porous medium and fast diffusion equations [669], and parabolic p-Laplace type equations [666], but also more exotic models [667, 668] (see also [737]). For background about the theory of porous medium and fast diffusion equations, the reader may consult the review texts by Vázquez  $[804, 806]$ .

In his work about porous medium equations, Otto also made two important conceptual contributions: First, he introduced the abstract formalism allowing him to interpret these equations as gradient flows, directly at the continuous level (without going through the timediscretization). Secondly, he showed that certain features of the porous medium equations (qualitative behavior, rates of convergence to equilibrium) were best seen via the new gradient flow interpretation. The psychological impact of this work on specialists of optimal transport was important. Otto's approach was developed by various authors, including Carrillo, McCann and myself [213, 214], Ambrosio, Gigli and Savaré [30], and others. As an example of recent application, Carrillo and Calvez [198] applied the same methodology to a one-dimensional variant of the Keller–Segel chemotaxis model, and showed that its qualitative behavior can be conveyed by diagrams depicting the behavior of a free energy in the Wasserstein space.

The setting adopted in [30, 214, 814] is the following: Let  $E$  denote an energy functional of the form

$$
E(\mu) = \int_{\mathbb{R}^n} U(\rho(x)) dx + \int_{\mathbb{R}^n} V(x) d\mu(x) + \frac{1}{2} \int_{\mathbb{R}^n \times \mathbb{R}^n} W(x - y) d\mu(x) d\mu(y),
$$

where as usual  $\rho$  is the density of  $\mu$ , and  $U(0) = 0$ ; then under certain regularity assumptions, the associated gradient flow with respect to the 2-Wasserstein distance  $W_2$  is

$$
\frac{\partial \rho}{\partial t} = \Delta p(\rho) + \nabla \cdot (\rho \, \nabla V) + \nabla \cdot (\rho \, \nabla (\rho * W)),
$$

where as usual  $p(r) = r U'(r) - U(r)$ . (When  $p(r) = r$ , the above equation is a special case of McKean–Vlasov equation.) The most general results of this kind can be found in [30]. Such equations arise in a number of physical models; see e.g. [214]. As an interesting particular case, the logarithmic interaction in dimension 2 gives rise to a form of the Keller–Segel model for chemotaxis; variants of this model are studied by means of optimal transport in [121, 198]; (see also [196, Chapter 7]).

Other interesting gradient flows are obtained by choosing for the energy functional:

• the Fisher information

$$
I(\mu) = \int \frac{|\nabla \rho|^2}{\rho};
$$

then the resulting fourth-order, nonlinear partial differential equation is a quantum drift-diffusion equation [30, Example 11.1.10], which also appears in the modeling of interfaces in spin systems. The gradient flow interpretation of this equation was recently studied rigorously by Gianazza, Savaré and Toscani [414]. See [497] and the many references there quoted for other recent contributions on this model.

the squared  $H^{-1}$  norm

$$
\|\mu\|_{H^{-1}}^2 = \|\nabla \Delta^{-1} \rho\|_{L^2}^2;
$$

then the resulting equation appears in the Ginzburg–Landau dynamics. This idea has been in the air for a few years at a purely formal level; recently, Ambrosio and Serfaty [36] have justified this guess rigorously. (The functional treated there is not exactly the squared  $H^{-1}$  norm, but it is related; at the same time they showed how this problem gives some insight into the tricky issue of boundary conditions.)

• the negative squared  $W_2$  distance,  $\mu \mapsto -W_2(\sigma, \mu)^2/2$ , where  $\sigma$  is a reference measure. (In dimension 2, this is as a kind of dissipative variant of the semi-geostrophic equations.) The resulting gradient flow can be considered as the geodesic flow on  $P_2(M)$  (the case  $M = \mathbb{R}^n$  is treated in [30, Example 11.2.9 and Theorem 11.2.10]).

Gradient flows with respect to the Wasserstein distances  $W_p$  with  $p \neq 2$  were considered in [666] and lead to other classes of well-known diffusion equations, such as *p*-Laplace equations  $\partial_t \rho = \nabla \cdot (|\nabla \rho|^{p-2} \nabla \rho)$ . A large part of the discussion can be transposed to that case [4, 594], but things become quite more difficult.

Brenier [164] has suggested that certain cost functions with "relativistic" features could be physically relevant, say  $c(x,y) = c(x - y)$ with

$$
c(v) = 1 - \sqrt{1 - \frac{|v|^2}{c^2}}
$$
 or  $c(v) = \sqrt{1 + \frac{|v|^2}{c^2}} - 1$ .

By applying the general formalism of gradient flows with such cost functions, he derived relativistic-like heat equations, such as

$$
\frac{\partial \rho}{\partial t} = \nabla \cdot \left( \frac{\rho \, \nabla \rho}{\sqrt{\rho^2 + \varepsilon^2 |\nabla \rho|^2}} \right).
$$

This looked a bit like a formal game, but it was later found out that related equations were common in the physical literature about flux-limited diffusion processes [627], and that in fact Brenier's very equation had already been considered by Rosenau [708]. A rigorous treatment of these equations leads to challenging analytical difficulties, which triggered several recent technical works, see e.g. [39, 40, 618] and

the references therein. By the way, the cost function  $1 - \sqrt{1 - |x - y|^2}$ was later found to have applications in the design of lenses [712].

Lemma 23.28 in the Appendix is borrowed from [30, Lemma 4.3.4]. As Ambrosio pointed out to me, the argument is quite reminiscent of Kruzkhov's doubling method for the proof of uniqueness in the theory of scalar conservation laws, see for instance the nice presentation in [327, Sections 10.2 and 11.4]. It is important to note that the almost everywhere differentiability of  $F$  in both variables separately is not enough to apply this lemma.

The stability theorem (Theorem 23.26) is a particular case of more abstract general results, see for instance [30, Theorem 4.0.4(iv)].

In their study of gradient flows, Ambrosio, Gigli and Savaré point out that it is useful to construct curves  $(\mu_t)$  satisfying the convexitytype inequality

$$
W_2(\sigma, \mu_t)^2 \ge (1 - t) W_2(\sigma, \mu_0)^2 + t W_2(\sigma, \mu_1)^2
$$
$$
- t(1 - t) \int d(T_0(x), T_1(x))^2 \sigma(dx), \quad (23.92)
$$

where  $T_i$   $(i = 0, 1)$  is the optimal transport between  $\sigma$  and  $\mu_i$ . (Their construction is performed only in  $\mathbb{R}^n$  [30, Proposition 9.3.12].) At first sight (23.92) seems to contradict the fact that  $P_2(M)$  does not have sectional curvature bounded above in the Alexandrov sense; but there is in fact no contradiction since the curve  $(\mu_t)_{0 \leq t \leq 1}$  will depend on the measure  $\sigma$ .

I shall now make some more speculative comments about the interpretation of the results, and their possible extensions.

For the most part, equilibrium statistical mechanics rests on the idea that the equilibrium measure is obtained by the minimization of a thermodynamical functional such as the free energy. The principle according to which nonequilibrium statistical mechanics may also be understood through variational principles is much more original; I first heard it explicitly in a seminar by Kinderlehrer (June 1997 in Paris), about the interpretation of the Fokker–Planck equation by means of Wasserstein distance. Independently of optimal transport theory, the same idea has been making its way in the community of physicists, where it may be attributed to Prigogine. There is ongoing research in that direction, in relation to large deviations and fluctuation of currents, performed by Gabrieli, Landim, Derrida, Lebowitz, Speer, Jona Lasinio and others. It seems to me that both approaches (optimal transport on the one hand, large deviations on the other) have a lot in common, although the formalisms look very different. By the way, some links between optimal transport and large deviations have recently been explored in a book by Feng and Kurtz [355] as well as in [429, 430, 448].

As for the idea to rewrite (possibly linear) diffusion equations as nonlinear transport equations, it may at first sight seem odd, but looks like the right point of view in many problems, and has been used for some time in numerical analysis.

So far I have mainly discussed gradient flows associated with cost functions that are quadratic  $(p = 2)$ , or at least strictly convex. But there are some quite interesting models of gradient flows for, say, the cost function which is equal to the distance  $(p = 1)$ . Such equations have been used for instance in the modeling of sandpiles [46, 48, 329, 336, 691] or compression molding [47]. These issues are briefly reviewed by Evans [328].

Also gradient flows of certain "energy" functionals in the Wasserstein space may be used to define certain diffusive semigroups; think for instance of the problem of constructing the heat semigroup on a nonsmooth metric space. Recently Ohta [655] showed that the heat equation could be introduced in this way on Alexandrov spaces of nonnegative sectional curvature. An independent contribution by Savaré [735] addresses the same problem on certain classes of metric spaces including Alexandrov spaces with curvature bounded below. On such spaces, the heat equation can be constructed by other means [537], but it might be that the optimal transport approach will apply to even more singular situations, and in any case this provides a genuinely different approach of the problem.

One can also hope to treat subriemannian situations in the same way; at least from a formal point of view, this is addressed in [514].

It can also be expected that the gradient flow interpretation will provide powerful general tools to study the stability of diffusion equations. In a striking recent application of this principle, Ambrosio, Savaré and Zambotti [35] proved the stability of the linear diffusion process associated with the Fokker–Planck equation, in finite or infinite dimension, with respect to the weak convergence of the invariant probability measure, as soon as the latter is log concave. Savaré [735, 736] also used this interpretation to prove the stability of the heat semigroup (on manifolds, or on certain metric-measure spaces) under measured Gromov–Hausdorff convergence.

Very recently, variational problems taking the form of a discretized gradient flow have made their way in mathematical economics or decision theory; in these models the negative of the energy can be thought of as, say, the reward or the benefits obtained from a certain skill or method or decision, while the cost function can be interpreted as the effort or difficulty which one has to spend or endure in order to learn this skill or change one's habits or take the decision. As an entry point to that literature, the reader may take a look at a paper by Attouch and Soubeyran [49]. It is interesting to note that the gradient flows in this kind of literature would be more of the kind  $p = 1$  than of the kind  $p=2$ .

One may speculate about the possible applications of all these gradient flow interpretations in numerical simulation. In [121] the authors studied a numerical scheme based on the discretization of a gradient flow in Wasserstein space, which has the striking advantage of preserving qualitative features such as finite-time blow-up or long-time extinction. The simulation is however very costly.

This chapter was only concerned with gradient flows. The situation about Hamiltonian flows is anything but clear. In [814, Section 8.3.2] one can find some examples of equations that one would like to intepret as Hamiltonian equations with respect to the distance  $W_2$ , and other equations that one would like to interpret as dissipative Hamiltonian equations. There are many other ones.

An important example of a "Hamiltonian equation" is the semigeostrophic system and its variants [86, 87, 265, 266, 268, 421, 569]. The well-known Euler–Poisson and Vlasov–Poisson models should belong to this class too, but also some strange variants suggested by Brenier and Loeper such as the Euler–Monge–Ampère equation  $[567]$  or its kinetic counterpart, the Vlasov–Monge–Ampère equation [170].

Among many examples of "dissipative Hamiltonian equations", I shall mention the rescaled two-dimensional incompressible Navier– Stokes equation in vorticity formulation (for nonnegative vorticity), as studied by Gallay and Wayne [392]. Caglioti, Pulvirenti and Rousset [193, 194] have used this interpretation of Navier–Stokes in terms of optimal transport, to derive related equations attempting to describe "quasi-stationary states", or intermediate time-asymptotics.

About the rigorous justification of the Hamiltonian formalism in the Wasserstein space, there are recent works by Ambrosio and Gangbo [27, 28] covering certain classes of Hamiltonian equations, yet not as wide as one could wish. Cullen, Gangbo and Pisante [267] have studied the approximation of some of these equations by particle systems. There is also a work by Gangbo, Nguyen and Tudorascu on the one-dimensional Euler–Poisson model [401]. Their study provides evidence of striking "pathological" behavior: If one defines variational solutions of Euler– Poisson as the minimizing paths for the natural action, then a solution might very well start absolutely continuous at initial time, collapse on a Dirac mass in finite time, stay in this state for a positive time, and then spread again. Also Wolansky [837] obtained coagulation–fragmentation phenomena through a Hamiltonian formalism (based on an internal energy rather than an interaction energy).

The precise sense of the "Hamiltonian structure" should be taken with some care. It was suggested to me some time ago by Ghys that this really is a Poisson structure, in the spirit of Kirillov. This guess was justified and completely clarified (at least formally) by Lott [575], who also made the link with previous work by Weinstein and collaborators (see [575, Section 6] for explanations). Further contributions are due to Khesin and Lee [514], who also studied the sense in which the semigeostrophic system defines a Hamiltonian flow [513].

A particularly interesting "dissipative Hamiltonian equation" that should have an interpretation in terms of optimal transport is the kinetic Fokker–Planck equation, with or without self-interaction. Huang and Jordan [485] studied this model in the setting of gradient flows (which is not natural here since we are rather dealing with a Hamiltonian flow with dissipation). A quite different contribution by Carlen and Gangbo [204] approaches the kinetic Fokker–Planck equation by a splitting scheme alternating free transport and time-discretized gradient flow in the velocity variable.

More recently, Gangbo and Westdickenberg [404] suggested an approximation scheme for the isentropic Euler system based on a kinetic approximation (so one works with probability measures on  $\mathbb{R}^n \times \mathbb{R}^n$ ), with a splitting scheme alternating free transport, time-discretized gradient flow in the position variable, and an optimal transport problem to reconstruct the velocity variable. Their scheme, which has some hidden relation to the Huang–Jordan contribution, can be reinterpreted in terms of minimization of an action that would involve the squared acceleration rather than the squared velocity; and there are heuristic arguments to believe that this system should converge to a physical solution satisfying Dafermos's entropy criterion (the physical energy, which is formally conserved, should decrease as much as possible). Numerical simulations based on this scheme perform surprisingly well, at least in dimension 1.

In the big picture also lies the work of Nelson [201, 647, 648, 650, 651, 652] on the foundations of quantum mechanics. Nelson showed that the usual Schrödinger equation can be derived from a principle of least action over solutions of a stochastic differential equation, where the noise is fixed but the drift is unknown. Other names associated with this approach are Guerra, Morato and Carlen. The reader may consult [343, Chapter 5] for more information. In Chapter 7 of the same reference, I have briefly made the link with the optimal transport problem. Von Renesse [826] explicitly reformulated the Schrödinger equation as a Hamiltonian system in Wasserstein space.

A more or less equivalent way to see Nelson's point of view (explained to me by Carlen) is to study the critical points of the action

$$
\mathcal{A}(\rho,m) = \int_0^1 \left( K(\rho_t, m_t) - F(\rho_t) \right) dt, \tag{23.93}
$$

where  $\rho = \rho(t, x)$  is a time-dependent density (say on  $\mathbb{R}^n$ ),  $m = m(t, x)$ is a time-dependent momentum density,  $K(\rho, m) = \int |m|^2/(2\rho)$  is the kinetic energy and  $F(\rho) = I(\rho) = \int |\nabla \rho|^2 / \rho$  is the Fisher information. The density and momentum should satisfy the equation of mass conservation, namely  $\partial_t \rho + \nabla \cdot m = 0$ . At least formally, critical points of (23.93), for fixed  $\rho_0$ ,  $\rho_1$ , satisfy the Euler–Lagrange equation

$$
\partial_t \rho + \nabla \cdot (\rho \nabla \varphi) = 0
$$

$$
\partial_t \varphi + \frac{|\nabla \varphi|^2}{2} = 2 \frac{\Delta \sqrt{\rho}}{\sqrt{\rho}};
$$

(23.94)

the pressure term  $(\Delta \sqrt{\rho})/\sqrt{\rho}$  is sometimes called the "Bohm potential". Then the change of unknown  $\psi = \sqrt{\rho} e^{i\varphi}$  transforms (23.94) into the usual linear Schrödinger equation.

Variational problems of the form of (23.93) can be used to derive many systems of Hamiltonian type, and some of these actions are interesting in their own right. The choice  $F = 0$  gives just the squared 2-Wasserstein distance; this is the Benamou–Brenier formula [814, Theorem 8.1]. The functional  $F(\rho) = -\int |\nabla \Delta^{-1}(\rho - 1)|^2$  appears in the so-called reconstruction problem in cosmology [568] and leads to the Euler–Poisson equations (see also [401]). As a final example,  $F(\rho) = -(\pi^2/6) \int \rho^3$  appears (in dimension  $n = 1$ ) in the qualitative description of certain random matrix models, and leads to the isentropic Euler equations with negative cubic pressure law, as first realized by Matytsin; see [449] for rigorous justification and references. (Some simple remarks about uniqueness and duality for such negative pressure models can be found in [811].) Unexpectedly, the same variational problem appears in a seemingly unrelated problem of stochastic control [563].
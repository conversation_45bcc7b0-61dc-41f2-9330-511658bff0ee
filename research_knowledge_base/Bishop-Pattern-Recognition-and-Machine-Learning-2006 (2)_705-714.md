# **Appendix B. Probability Distributions**

In this appendix, we summarize the main properties of some of the most widely used probability distributions, and for each distribution we list some key statistics such as the expectation  $\mathbb{E}[\mathbf{x}]$ , the variance (or covariance), the mode, and the entropy  $H[\mathbf{x}]$ . All of these distributions are members of the exponential family and are widely used as building blocks for more sophisticated probabilistic models.

## **Bernoulli**

This is the distribution for a single binary variable  $x \in \{0, 1\}$  representing, for example, the result of flipping a coin. It is governed by a single continuous parameter  $\mu \in [0, 1]$  that represents the probability of  $x = 1$ .

$$
Bern(x|\mu) = \mu^x (1 - \mu)^{1 - x}
$$
 (B.1)

$$
\mathbb{E}[x] = \mu \tag{B.2}
$$

$$
var[x] = \mu(1 - \mu) \tag{B.3}
$$

$$
mode[x] = \begin{cases} 1 & \text{if } \mu \geqslant 0.5, \\ 0 & \text{otherwise} \end{cases}
$$
 (B.4)

$$
H[x] = -\mu \ln \mu - (1 - \mu) \ln(1 - \mu). \tag{B.5}
$$

The <PERSON>oulli is a special case of the binomial distribution for the case of a single observation. Its conjugate prior for  $\mu$  is the beta distribution.

## **Beta**

Image /page/1/Picture/2 description: A black rectangle encloses a white background. Inside the rectangle, there are four curves. A red curve starts at the bottom left, goes up to a peak, then down to the bottom right. A blue curve starts at the bottom left, goes up to a peak, then down to the bottom right, crossing the red curve. A magenta curve starts at the bottom left, goes up to a high peak, then down to the bottom right, crossing the blue curve. A horizontal green line is drawn across the middle of the rectangle, intersecting all three curves.

This is a distribution over a continuous variable  $\mu \in [0, 1]$ , which is often used to represent the probability for some binary event. It is governed by two parameters  $\alpha$ and b that are constrained by  $a > 0$  and  $b > 0$  to ensure that the distribution can be normalized.

Beta(
$$
\mu|a, b
$$
) =  $\frac{\Gamma(a+b)}{\Gamma(a)\Gamma(b)} \mu^{a-1} (1-\mu)^{b-1}$  (B.6)

$$
\mathbb{E}[\mu] = \frac{a}{a+b} \tag{B.7}
$$

$$
\text{var}[\mu] = \frac{ab}{(a+b)^2(a+b+1)} \tag{B.8}
$$

$$
mode[\mu] = \frac{a-1}{a+b-2}.
$$
 (B.9)

The beta is the conjugate prior for the Bernoulli distribution, for which  $a$  and  $b$  can be interpreted as the effective prior number of observations of  $x = 1$  and  $x = 0$ , respectively. Its density is finite if  $a \geq 1$  and  $b \geq 1$ , otherwise there is a singularity at  $\mu = 0$  and/or  $\mu = 1$ . For  $a = b = 1$ , it reduces to a uniform distribution. The beta distribution is a special case of the K-state Dirichlet distribution for  $K = 2$ .

Image /page/1/Picture/9 description: A histogram with blue bars is displayed against a white background. The bars are of varying heights and are arranged from left to right, with the tallest bar in the second position from the left. The heights of the bars decrease as they move to the right, with the last few bars being very short. The histogram appears to represent a distribution of data, possibly skewed to the right.

## **Binomial**

The binomial distribution gives the probability of observing m occurrences of  $x = 1$ in a set of  $N$  samples from a Bernoulli distribution, where the probability of observing  $x = 1$  is  $\mu \in [0, 1]$ .

$$
Bin(m|N,\mu) = {N \choose m} \mu^m (1-\mu)^{N-m}
$$
 (B.10)

$$
\mathbb{E}[m] = N\mu \tag{B.11}
$$

$$
var[m] = N\mu(1-\mu) \tag{B.12}
$$

$$
mode[m] = \lfloor (N+1)\mu \rfloor \tag{B.13}
$$

where  $\left|(N+1)\mu\right|$  denotes the largest integer that is less than or equal to  $(N+1)\mu$ , and the quantity

$$
\binom{N}{m} = \frac{N!}{m!(N-m)!}
$$
\n(B.14)

denotes the number of ways of choosing  $m$  objects out of a total of  $N$  identical objects. Here m!, pronounced 'factorial m', denotes the product  $m \times (m - 1) \times$  $\ldots$ ,  $\times$  2  $\times$  1. The particular case of the binomial distribution for  $N = 1$  is known as the Bernoulli distribution, and for large  $N$  the binomial distribution is approximately Gaussian. The conjugate prior for  $\mu$  is the beta distribution.

# **Dirichlet**

Image /page/2/Picture/2 description: A 3D plot shows a bell-shaped curve peaking in the center and tapering off towards the edges. The curve is colored with a gradient from purple at the peak to blue and then teal at the base. The plot is set against a wireframe grid, suggesting a mathematical or statistical representation.

The Dirichlet is a multivariate distribution over K random variables  $0 \le \mu_k \le 1$ , where  $k = 1, \ldots, K$ , subject to the constraints

$$
0 \le \mu_k \le 1,
$$
  $\sum_{k=1}^{K} \mu_k = 1.$  (B.15)

Denoting  $\boldsymbol{\mu} = (\mu_1, \dots, \mu_K)^T$  and  $\boldsymbol{\alpha} = (\alpha_1, \dots, \alpha_K)^T$ , we have

$$
Dir(\boldsymbol{\mu}|\boldsymbol{\alpha}) = C(\boldsymbol{\alpha}) \prod_{k=1}^{K} \mu_k^{\alpha_k - 1}
$$
 (B.16)

$$
\mathbb{E}[\mu_k] = \frac{\alpha_k}{\hat{\alpha}} \tag{B.17}
$$

$$
\text{var}[\mu_k] = \frac{\alpha_k(\widehat{\alpha} - \alpha_k)}{\widehat{\alpha}^2(\widehat{\alpha} + 1)}
$$
(B.18)

$$
cov[\mu_j \mu_k] = -\frac{\alpha_j \alpha_k}{\widehat{\alpha}^2(\widehat{\alpha} + 1)}
$$
(B.19)

$$
\text{mode}[\mu_k] = \frac{\alpha_k - 1}{\hat{\alpha} - K} \tag{B.20}
$$
\n
$$
\mathbb{E}[\mu_k] = \frac{\alpha_k - 1}{\hat{\alpha} - K} \tag{B.21}
$$

$$
\mathbb{E}[\ln \mu_k] = \psi(\alpha_k) - \psi(\widehat{\alpha}) \tag{B.21}
$$

$$
H[\boldsymbol{\mu}] = -\sum_{k=1}^{K} (\alpha_k - 1) \left\{ \psi(\alpha_k) - \psi(\widehat{\alpha}) \right\} - \ln C(\boldsymbol{\alpha}) \qquad (B.22)
$$

where

$$
C(\alpha) = \frac{\Gamma(\widehat{\alpha})}{\Gamma(\alpha_1)\cdots\Gamma(\alpha_K)}
$$
(B.23)

and

$$
\widehat{\alpha} = \sum_{k=1}^{K} \alpha_k.
$$
 (B.24)

Here

$$
\psi(a) \equiv \frac{d}{da} \ln \Gamma(a) \tag{B.25}
$$

is known as the *digamma* function (Abramowitz and Stegun, 1965). The parameters  $\alpha_k$  are subject to the constraint  $\alpha_k > 0$  in order to ensure that the distribution can be normalized.

The Dirichlet forms the conjugate prior for the multinomial distribution and represents a generalization of the beta distribution. In this case, the parameters  $\alpha_k$  can be interpreted as effective numbers of observations of the corresponding values of the K-dimensional binary observation vector **x**. As with the beta distribution, the Dirichlet has finite density everywhere provided  $\alpha_k \geq 1$  for all k.

## **Gamma**

Image /page/3/Picture/2 description: A graph displays three curves: a green curve that starts high on the left and quickly drops to zero, a red curve that starts at zero, rises to a peak, and then drops to zero, and a blue curve that starts high on the left and gradually drops to zero. The x-axis is not labeled, and the y-axis is not labeled, but the curves appear to represent probability distributions.

The Gamma is a probability distribution over a positive random variable  $\tau > 0$ governed by parameters a and b that are subject to the constraints  $a > 0$  and  $b > 0$ to ensure that the distribution can be normalized.

Gam
$$
(\tau|a, b)
$$
 =  $\frac{1}{\Gamma(a)} b^a \tau^{a-1} e^{-b\tau}$  (B.26)

$$
\mathbb{E}[\tau] = \frac{a}{b} \tag{B.27}
$$

$$
var[\tau] = \frac{a}{b^2} \tag{B.28}
$$

$$
mode[\tau] = \frac{a-1}{b} \quad \text{for } \alpha \geqslant 1 \tag{B.29}
$$

$$
\mathbb{E}[\ln \tau] = \psi(a) - \ln b \tag{B.30}
$$

$$
H[\tau] = \ln \Gamma(a) - (a-1)\psi(a) - \ln b + a
$$
 (B.31)

where  $\psi(\cdot)$  is the digamma function defined by (B.25). The gamma distribution is the conjugate prior for the precision (inverse variance) of a univariate Gaussian. For  $a \geq 1$  the density is everywhere finite, and the special case of  $a = 1$  is known as the *exponential* distribution.

## **Gaussian**

Image /page/3/Picture/12 description: A graph displays three bell curves, representing probability distributions. The curves are colored red, blue, and green. The red curve is the widest and shortest, indicating a large standard deviation and low peak. The blue curve is narrower and taller than the red curve, suggesting a smaller standard deviation and higher peak. The green curve is the narrowest and tallest, signifying the smallest standard deviation and the highest peak. All three curves are centered at the same point on the x-axis, implying they share the same mean.

The Gaussian is the most widely used distribution for continuous variables. It is also known as the *normal* distribution. In the case of a single variable  $x \in (-\infty, \infty)$  it is governed by two parameters, the mean  $\mu \in (-\infty, \infty)$  and the variance  $\sigma^2 > 0$ .

$$
\mathcal{N}(x|\mu, \sigma^2) = \frac{1}{(2\pi\sigma^2)^{1/2}} \exp\left\{-\frac{1}{2\sigma^2}(x-\mu)^2\right\}
$$
 (B.32)

$$
\mathbb{E}[x] = \mu \tag{B.33}
$$

$$
var[x] = \sigma^2 \tag{B.34}
$$

$$
mode[x] = \mu \tag{B.35}
$$

$$
H[x] = \frac{1}{2} \ln \sigma^2 + \frac{1}{2} (1 + \ln(2\pi)).
$$
 (B.36)

The inverse of the variance  $\tau = 1/\sigma^2$  is called the precision, and the square root of the variance  $\sigma$  is called the standard deviation. The conjugate prior for  $\mu$  is the Gaussian, and the conjugate prior for  $\tau$  is the gamma distribution. If both  $\mu$  and  $\tau$ are unknown, their joint conjugate prior is the Gaussian-gamma distribution.

For a <sup>D</sup>-dimensional vector **x**, the Gaussian is governed by a <sup>D</sup>-dimensional mean vector  $\mu$  and a  $D \times D$  covariance matrix  $\Sigma$  that must be symmetric and positive-definite.

$$
\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma}) = \frac{1}{(2\pi)^{D/2}} \frac{1}{|\boldsymbol{\Sigma}|^{1/2}} \exp\left\{-\frac{1}{2}(\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} \boldsymbol{\Sigma}^{-1}(\mathbf{x} - \boldsymbol{\mu})\right\} (B.37)
$$

$$
\mathbb{E}[\mathbf{x}] = \mu \tag{B.38}
$$

$$
cov[\mathbf{x}] = \Sigma \tag{B.39}
$$

$$
mode[x] = \mu \tag{B.40}
$$

$$
H[x] = \frac{1}{2} \ln |\Sigma| + \frac{D}{2} (1 + \ln(2\pi)).
$$
 (B.41)

The inverse of the covariance matrix  $\Lambda = \Sigma^{-1}$  is the precision matrix, which is also symmetric and positive definite. Averages of random variables tend to a Gaussian, by the central limit theorem, and the sum of two Gaussian variables is again Gaussian. The Gaussian is the distribution that maximizes the entropy for a given variance (or covariance). Any linear transformation of a Gaussian random variable is again Gaussian. The marginal distribution of a multivariate Gaussian with respect to a subset of the variables is itself Gaussian, and similarly the conditional distribution is also Gaussian. The conjugate prior for  $\mu$  is the Gaussian, the conjugate prior for  $\Lambda$ is the Wishart, and the conjugate prior for  $(\mu, \Lambda)$  is the Gaussian-Wishart.

If we have a marginal Gaussian distribution for **x** and a conditional Gaussian distribution for **y** given **x** in the form

$$
p(\mathbf{x}) = \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Lambda}^{-1})
$$
\n(B.42)\n  
\n
$$
\mathcal{N}(\mathbf{x}|\boldsymbol{\Lambda}^{-1}, \mathbf{L}^{-1})
$$
\n(B.43)

$$
p(\mathbf{y}|\mathbf{x}) = \mathcal{N}(\mathbf{y}|\mathbf{A}\mathbf{x} + \mathbf{b}, \mathbf{L}^{-1})
$$
 (B.43)

then the marginal distribution of **y**, and the conditional distribution of **x** given **y**, are given by

$$
p(\mathbf{y}) = \mathcal{N}(\mathbf{y}|\mathbf{A}\boldsymbol{\mu} + \mathbf{b}, \mathbf{L}^{-1} + \mathbf{A}\boldsymbol{\Lambda}^{-1}\mathbf{A}^{\mathrm{T}})
$$
(B.44)

$$
p(\mathbf{x}|\mathbf{y}) = \mathcal{N}(\mathbf{x}|\mathbf{\Sigma}\{\mathbf{A}^{\mathrm{T}}\mathbf{L}(\mathbf{y}-\mathbf{b})+\mathbf{\Lambda}\boldsymbol{\mu}\}, \mathbf{\Sigma})
$$
 (B.45)

where

$$
\Sigma = (\Lambda + A^{T}LA)^{-1}.
$$
 (B.46)

If we have a joint Gaussian distribution  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma})$  with  $\boldsymbol{\Lambda} \equiv \boldsymbol{\Sigma}^{-1}$  and we the following partitions define the following partitions

$$
\mathbf{x} = \begin{pmatrix} \mathbf{x}_a \\ \mathbf{x}_b \end{pmatrix}, \quad \boldsymbol{\mu} = \begin{pmatrix} \boldsymbol{\mu}_a \\ \boldsymbol{\mu}_b \end{pmatrix}
$$
 (B.47)

$$
\Sigma = \begin{pmatrix} \Sigma_{aa} & \Sigma_{ab} \\ \Sigma_{ba} & \Sigma_{bb} \end{pmatrix}, \quad \Lambda = \begin{pmatrix} \Lambda_{aa} & \Lambda_{ab} \\ \Lambda_{ba} & \Lambda_{bb} \end{pmatrix}
$$
 (B.48)

then the conditional distribution  $p(\mathbf{x}_a|\mathbf{x}_b)$  is given by

$$
p(\mathbf{x}_a|\mathbf{x}_b) = \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_{a|b}, \boldsymbol{\Lambda}_{aa}^{-1})
$$
 (B.49)

$$
\mu_{a|b} = \mu_a - \Lambda_{aa}^{-1} \Lambda_{ab} (\mathbf{x}_b - \mu_b) \tag{B.50}
$$

and the marginal distribution  $p(\mathbf{x}_a)$  is given by

$$
p(\mathbf{x}_a) = \mathcal{N}(\mathbf{x}_a | \boldsymbol{\mu}_a, \boldsymbol{\Sigma}_{aa}).
$$
 (B.51)

## **Gaussian-Gamma**

Image /page/5/Picture/4 description: A contour plot shows a series of nested, closed curves that are roughly triangular in shape. The curves are red and are set against a white background with a black border. The innermost curve is small and somewhat oval, while the outer curves expand outwards, maintaining a general triangular form with rounded corners. The curves are evenly spaced, suggesting a gradient or distribution of some quantity.

This is the conjugate prior distribution for a univariate Gaussian  $\mathcal{N}(x|\mu, \lambda^{-1})$  in which the mean  $\mu$  and the precision  $\lambda$  are both unknown and is also called the *normal-gamma* distribution. It comprises the product of a Gaussian distribution for  $\mu$ , whose precision is proportional to  $\lambda$ , and a gamma distribution over  $\lambda$ .

$$
p(\mu, \lambda | \mu_0, \beta, a, b) = \mathcal{N}\left(\mu | \mu_0, (\beta \lambda)^{-1}\right) \text{Gam}(\lambda | a, b).
$$
 (B.52)

## **Gaussian-Wishart**

This is the conjugate prior distribution for a multivariate Gaussian  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Lambda})$  in which both the mean  $\mu$  and the precision  $\Lambda$  are unknown, and is also called the normal-Wishart distribution. It comprises the product of a Gaussian distribution for  $\mu$ , whose precision is proportional to  $\Lambda$ , and a Wishart distribution over  $\Lambda$ .

$$
p(\boldsymbol{\mu}, \boldsymbol{\Lambda} | \boldsymbol{\mu}_0, \boldsymbol{\beta}, \mathbf{W}, \nu) = \mathcal{N}\left(\boldsymbol{\mu} | \boldsymbol{\mu}_0, (\boldsymbol{\beta} \boldsymbol{\Lambda})^{-1}\right) \mathcal{W}(\boldsymbol{\Lambda} | \mathbf{W}, \nu).
$$
 (B.53)

For the particular case of a scalar  $x$ , this is equivalent to the Gaussian-gamma distribution.

# **Multinomial**

If we generalize the Bernoulli distribution to an  $K$ -dimensional binary variable  $x$ with components  $x_k \in \{0, 1\}$  such that  $\sum_k x_k = 1$ , then we obtain the following discrete distribution

$$
p(\mathbf{x}) = \prod_{k=1}^{K} \mu_k^{x_k}
$$
 (B.54)

$$
\mathbb{E}[x_k] = \mu_k \tag{B.55}
$$

$$
var[x_k] = \mu_k (1 - \mu_k) \tag{B.56}
$$

$$
cov[x_j x_k] = I_{jk} \mu_k
$$
 (B.57)

$$
H[\mathbf{x}] = -\sum_{k=1}^{N} \mu_k \ln \mu_k
$$
 (B.58)

where  $I_{jk}$  is the j, k element of the identity matrix. Because  $p(x_k = 1) = \mu_k$ , the parameters must satisfy  $0 \le \mu_k \le 1$  and  $\sum_k \mu_k = 1$ .

The multinomial distribution is a multivariate generalization of the binomial and gives the distribution over counts  $m_k$  for a K-state discrete variable to be in state k given a total number of observations N.

Mult
$$
(m_1, m_2, ..., m_K | \boldsymbol{\mu}, N)
$$
 =  $\binom{N}{m_1 m_2 ... m_M} \prod_{k=1}^{M} \mu_k^{m_k}$  (B.59)

$$
\mathbb{E}[m_k] = N\mu_k \tag{B.60}
$$

$$
var[m_k] = N\mu_k(1 - \mu_k)
$$
 (B.61)

$$
cov[m_j m_k] = -N\mu_j \mu_k \qquad (B.62)
$$

where  $\mu = (\mu_1, \dots, \mu_K)^T$ , and the quantity

$$
\binom{N}{m_1 m_2 \dots m_K} = \frac{N!}{m_1! \dots m_K!}
$$
\n(B.63)

gives the number of ways of taking N identical objects and assigning  $m_k$  of them to bin k for  $k = 1, \ldots, K$ . The value of  $\mu_k$  gives the probability of the random variable taking state k, and so these parameters are subject to the constraints  $0 \le \mu_k \le 1$ and  $\sum_{k} \mu_k = 1$ . The conjugate prior distribution for the parameters  $\{\mu_k\}$  is the Dirichlet.

## **Normal**

The normal distribution is simply another name for the Gaussian. In this book, we use the term Gaussian throughout, although we retain the conventional use of the symbol  $N$  to denote this distribution. For consistency, we shall refer to the normalgamma distribution as the Gaussian-gamma distribution, and similarly the normal-Wishart is called the Gaussian-Wishart.

## **Student's t**

Image /page/6/Picture/13 description: A graph displays three bell curves, each representing a different distribution. The red curve is the widest and shortest, indicating a broad distribution with low peak amplitude. The blue curve is narrower and taller than the red curve, suggesting a more concentrated distribution with a higher peak. The green curve is the narrowest and tallest of the three, signifying a highly concentrated distribution with the greatest peak amplitude. All curves share the same central peak on the x-axis, but their spread and height vary, illustrating differences in variance and standard deviation.

This distribution was published by William Gosset in 1908, but his employer, Guiness Breweries, required him to publish under a pseudonym, so he chose 'Student'. In the univariate form, Student's t-distribution is obtained by placing a conjugate gamma prior over the precision of a univariate Gaussian distribution and then integrating out the precision variable. It can therefore be viewed as an infinite mixture

of Gaussians having the same mean but different variances.

$$
St(x|\mu,\lambda,\nu) = \frac{\Gamma(\nu/2 + 1/2)}{\Gamma(\nu/2)} \left(\frac{\lambda}{\pi \nu}\right)^{1/2} \left[1 + \frac{\lambda(x-\mu)^2}{\nu}\right]^{-\nu/2 - 1/2}
$$
(B.64)

$$
\mathbb{E}[x] = \mu \quad \text{for } \nu > 1
$$
\n(B.65)\n  
\n
$$
\begin{array}{ccc}\n1 & \nu & \text{if } \\
\end{array}
$$
\n(B.66)

$$
\text{var}[x] = \frac{1}{\lambda} \frac{\nu}{\nu - 2} \quad \text{for } \nu > 2 \tag{B.66}
$$

$$
mode[x] = \mu. \tag{B.67}
$$

Here  $\nu > 0$  is called the number of degrees of freedom of the distribution. The particular case of  $\nu = 1$  is called the *Cauchy* distribution.

For a <sup>D</sup>-dimensional variable **x**, Student's t-distribution corresponds to marginalizing the precision matrix of a multivariate Gaussian with respect to a conjugate Wishart prior and takes the form

$$
St(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Lambda}, \nu) = \frac{\Gamma(\nu/2 + D/2)}{\Gamma(\nu/2)} \frac{|\boldsymbol{\Lambda}|^{1/2}}{(\nu \pi)^{D/2}} \left[1 + \frac{\Delta^2}{\nu}\right]^{-\nu/2 - D/2}
$$
(B.68)

$$
\mathbb{E}[\mathbf{x}] = \mu \quad \text{for } \nu > 1
$$
 (B.69)

$$
cov[\mathbf{x}] = \frac{\nu}{\nu - 2} \mathbf{\Lambda}^{-1} \quad \text{for } \nu > 2
$$
 (B.70)

$$
mode[x] = \mu \tag{B.71}
$$

where  $\Delta^2$  is the squared Mahalanobis distance defined by

$$
\Delta^2 = (\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} \boldsymbol{\Lambda} (\mathbf{x} - \boldsymbol{\mu}).
$$
 (B.72)

In the limit  $\nu \to \infty$ , the t-distribution reduces to a Gaussian with mean  $\mu$  and precision **Λ**. Student's t-distribution provides a generalization of the Gaussian whose maximum likelihood parameter values are robust to outliers.

## **Uniform**

Image /page/7/Picture/16 description: A diagram shows three overlapping rectangles of different colors and widths. A red rectangle is the widest and is positioned at the bottom. A blue rectangle is narrower than the red one and is centered above the red rectangle. A green rectangle is the narrowest and is centered above the blue rectangle. The rectangles are outlined in black and appear to be stacked on top of each other, with the green rectangle being the tallest.

This is a simple distribution for a continuous variable  $x$  defined over a finite interval  $x \in [a, b]$  where  $b > a$ .

$$
U(x|a,b) = \frac{1}{b-a}
$$
 (B.73)

$$
\mathbb{E}[x] = \frac{(b+a)}{2} \tag{B.74}
$$

$$
var[x] = \frac{(b-a)^2}{12}
$$
 (B.75)

$$
H[x] = \ln(b - a). \tag{B.76}
$$

If x has distribution 
$$
U(x|0, 1)
$$
, then  $a + (b - a)x$  will have distribution  $U(x|a, b)$ .

## **Von Mises**

Image /page/8/Picture/2 description: A red ellipse is plotted on a graph with dotted grid lines. The ellipse is tilted, with its major axis running from the bottom left to the top right. The center of the ellipse appears to be near the origin of the graph.

The von Mises distribution, also known as the circular normal or the circular Gaussian, is a univariate Gaussian-like periodic distribution for a variable  $\theta \in [0, 2\pi)$ .

$$
p(\theta|\theta_0, m) = \frac{1}{2\pi I_0(m)} \exp\left\{m\cos(\theta - \theta_0)\right\}
$$
 (B.77)

where  $I_0(m)$  is the zeroth-order Bessel function of the first kind. The distribution has period  $2\pi$  so that  $p(\theta + 2\pi) = p(\theta)$  for all  $\theta$ . Care must be taken in interpreting this distribution because simple expectations will be dependent on the (arbitrary) choice of origin for the variable  $\theta$ . The parameter  $\theta_0$  is analogous to the mean of a univariate Gaussian, and the parameter  $m > 0$ , known as the *concentration* parameter, is analogous to the precision (inverse variance). For large  $m$ , the von Mises distribution is approximately a Gaussian centred on  $\theta_0$ .

## **Wishart**

The Wishart distribution is the conjugate prior for the precision matrix of a multivariate Gaussian.

$$
W(\mathbf{\Lambda}|\mathbf{W},\nu) = B(\mathbf{W},\nu)|\mathbf{\Lambda}|^{(\nu-D-1)/2} \exp\left(-\frac{1}{2}\text{Tr}(\mathbf{W}^{-1}\mathbf{\Lambda})\right) \tag{B.78}
$$

where

$$
B(\mathbf{W}, \nu) \equiv |\mathbf{W}|^{-\nu/2} \left( 2^{\nu D/2} \pi^{D(D-1)/4} \prod_{i=1}^{D} \Gamma\left(\frac{\nu+1-i}{2}\right) \right)^{-1}
$$
(B.79)

$$
\mathbb{E}[\Lambda] = \nu \mathbf{W} \tag{B.80}
$$

$$
\mathbb{E}\left[\ln|\mathbf{\Lambda}|\right] = \sum_{i=1}^{D} \psi\left(\frac{\nu+1-i}{2}\right) + D\ln 2 + \ln|\mathbf{W}| \tag{B.81}
$$

$$
H[\Lambda] = -\ln B(\mathbf{W}, \nu) - \frac{(\nu - D - 1)}{2} \mathbb{E} [\ln |\Lambda|] + \frac{\nu D}{2}
$$
 (B.82)

where **W** is a  $D \times D$  symmetric, positive definite matrix, and  $\psi(\cdot)$  is the digamma function defined by (B.25). The parameter  $\nu$  is called the *number of degrees of freedom* of the distribution and is restricted to  $\nu > D - 1$  to ensure that the Gamma function in the normalization factor is well-defined. In one dimension, the Wishart reduces to the gamma distribution  $Gam(\lambda|a, b)$  given by (B.26) with parameters  $a = \nu/2$  and  $b = 1/2W$ .

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
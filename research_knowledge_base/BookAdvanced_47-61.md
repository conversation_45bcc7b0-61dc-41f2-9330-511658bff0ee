## 2.6 Statistical Models, Supervised Learning and Function Approximation

Our goal is to find a useful approximation  $\hat{f}(x)$  to the function  $f(x)$  that underlies the predictive relationship between the inputs and outputs. In the theoretical setting of Section 2.4, we saw that squared error loss lead us to the regression function  $f(x) = E(Y|X=x)$  for a quantitative response. The class of nearest-neighbor methods can be viewed as direct estimates of this conditional expectation, but we have seen that they can fail in at least two ways:

- if the dimension of the input space is high, the nearest neighbors need not be close to the target point, and can result in large errors;
- if special structure is known to exist, this can be used to reduce both the bias and the variance of the estimates.

We anticipate using other classes of models for  $f(x)$ , in many cases specifically designed to overcome the dimensionality problems, and here we discuss a framework for incorporating them into the prediction problem.

### 2.6.1 A Statistical Model for the Joint Distribution $Pr(X, Y)$

Suppose in fact that our data arose from a statistical model

$$
Y = f(X) + \varepsilon,\tag{2.29}
$$

where the random error  $\varepsilon$  has  $E(\varepsilon) = 0$  and is independent of X. Note that for this model,  $f(x) = E(Y|X = x)$ , and in fact the conditional distribution  $Pr(Y|X)$  depends on X only through the conditional mean  $f(x)$ .

The additive error model is a useful approximation to the truth. For most systems the input–output pairs  $(X, Y)$  will not have a deterministic relationship  $Y = f(X)$ . Generally there will be other unmeasured variables that also contribute to  $Y$ , including measurement error. The additive model assumes that we can capture all these departures from a deterministic relationship via the error  $\varepsilon$ .

For some problems a deterministic relationship does hold. Many of the classification problems studied in machine learning are of this form, where the response surface can be thought of as a colored map defined in  $\mathbb{R}^p$ . The training data consist of colored examples from the map  $\{x_i, g_i\}$ , and the goal is to be able to color any point. Here the function is deterministic, and the randomness enters through the  $x$  location of the training points. For the moment we will not pursue such problems, but will see that they can be handled by techniques appropriate for the error-based models.

The assumption in (2.29) that the errors are independent and identically distributed is not strictly necessary, but seems to be at the back of our mind when we average squared errors uniformly in our EPE criterion. With such a model it becomes natural to use least squares as a data criterion for model estimation as in (2.1). Simple modifications can be made to avoid the independence assumption; for example, we can have  $Var(Y|X=x)$  $\sigma(x)$ , and now both the mean and variance depend on X. In general the conditional distribution  $Pr(Y|X)$  can depend on X in complicated ways, but the additive error model precludes these.

So far we have concentrated on the quantitative response. Additive error models are typically not used for qualitative outputs  $G$ ; in this case the target function  $p(X)$  is the conditional density  $Pr(G|X)$ , and this is modeled directly. For example, for two-class data, it is often reasonable to assume that the data arise from independent binary trials, with the probability of one particular outcome being  $p(X)$ , and the other  $1 - p(X)$ . Thus if Y is the 0–1 coded version of G, then  $E(Y|X=x) = p(x)$ , but the variance depends on x as well:  $Var(Y|X=x) = p(x)[1 - p(x)].$ 

### 2.6.2 Supervised Learning

Before we launch into more statistically oriented jargon, we present the function-fitting paradigm from a machine learning point of view. Suppose for simplicity that the errors are additive and that the model  $Y = f(X) + \varepsilon$ is a reasonable assumption. Supervised learning attempts to learn  $f$  by example through a teacher. One observes the system under study, both the inputs and outputs, and assembles a *training* set of observations  $\mathcal{T} =$  $(x_i, y_i), i = 1, \ldots, N$ . The observed input values to the system  $x_i$  are also fed into an artificial system, known as a learning algorithm (usually a computer program), which also produces outputs  $\hat{f}(x_i)$  in response to the inputs. The learning algorithm has the property that it can modify its input/output relationship  $\hat{f}$  in response to differences  $y_i - \hat{f}(x_i)$  between the original and generated outputs. This process is known as learning by example. Upon completion of the learning process the hope is that the artificial and real outputs will be close enough to be useful for all sets of inputs likely to be encountered in practice.

### 2.6.3 Function Approximation

The learning paradigm of the previous section has been the motivation for research into the supervised learning problem in the fields of machine learning (with analogies to human reasoning) and neural networks (with biological analogies to the brain). The approach taken in applied mathematics and statistics has been from the perspective of function approximation and estimation. Here the data pairs  $\{x_i, y_i\}$  are viewed as points in a  $(p+1)$ -dimensional Euclidean space. The function  $f(x)$  has domain equal to the p-dimensional input subspace, and is related to the data via a model

such as  $y_i = f(x_i) + \varepsilon_i$ . For convenience in this chapter we will assume the domain is  $\mathbb{R}^p$ , a *p*-dimensional Euclidean space, although in general the inputs can be of mixed type. The goal is to obtain a useful approximation to  $f(x)$  for all x in some region of  $\mathbb{R}^p$ , given the representations in  $\mathcal{T}$ . Although somewhat less glamorous than the learning paradigm, treating supervised learning as a problem in function approximation encourages the geometrical concepts of Euclidean spaces and mathematical concepts of probabilistic inference to be applied to the problem. This is the approach taken in this book.

Many of the approximations we will encounter have associated a set of parameters  $\theta$  that can be modified to suit the data at hand. For example, the linear model  $f(x) = x^T \beta$  has  $\theta = \beta$ . Another class of useful approximators can be expressed as linear basis expansions

$$
f_{\theta}(x) = \sum_{k=1}^{K} h_k(x)\theta_k,
$$
\n(2.30)

where the  $h_k$  are a suitable set of functions or transformations of the input vector x. Traditional examples are polynomial and trigonometric expansions, where for example  $h_k$  might be  $x_1^2$ ,  $x_1x_2^2$ ,  $\cos(x_1)$  and so on. We also encounter nonlinear expansions, such as the sigmoid transformation common to neural network models,

$$
h_k(x) = \frac{1}{1 + \exp(-x^T \beta_k)}.
$$
\n(2.31)

We can use least squares to estimate the parameters  $\theta$  in  $f_{\theta}$  as we did for the linear model, by minimizing the residual sum-of-squares

$$
RSS(\theta) = \sum_{i=1}^{N} (y_i - f_{\theta}(x_i))^2
$$
\n(2.32)

as a function of  $\theta$ . This seems a reasonable criterion for an additive error model. In terms of function approximation, we imagine our parameterized function as a surface in  $p + 1$  space, and what we observe are noisy realizations from it. This is easy to visualize when  $p = 2$  and the vertical coordinate is the output  $y$ , as in Figure 2.10. The noise is in the output coordinate, so we find the set of parameters such that the fitted surface gets as close to the observed points as possible, where close is measured by the sum of squared vertical errors in  $RSS(\theta)$ .

For the linear model we get a simple closed form solution to the minimization problem. This is also true for the basis function methods, if the basis functions themselves do not have any hidden parameters. Otherwise the solution requires either iterative methods or numerical optimization.

While least squares is generally very convenient, it is not the only criterion used and in some cases would not make much sense. A more general

Image /page/3/Figure/1 description: A 3D surface plot shows a wavy, grid-like surface colored in shades of blue and green. Red dots are scattered across the surface, with thin black lines extending vertically from some of the dots to a lower plane, indicating data points and their corresponding values on the z-axis. The plot is viewed from an angle, revealing the curvature and contours of the surface.

FIGURE 2.10. Least squares fitting of a function of two inputs. The parameters of  $f_{\theta}(x)$  are chosen so as to minimize the sum-of-squared vertical errors.

principle for estimation is maximum likelihood estimation. Suppose we have a random sample  $y_i$ ,  $i = 1, ..., N$  from a density  $Pr_{\theta}(y)$  indexed by some parameters  $\theta$ . The log-probability of the observed sample is

$$
L(\theta) = \sum_{i=1}^{N} \log \Pr_{\theta}(y_i).
$$
 (2.33)

The principle of maximum likelihood assumes that the most reasonable values for  $\theta$  are those for which the probability of the observed sample is largest. Least squares for the additive error model  $Y = f_{\theta}(X) + \varepsilon$ , with  $\varepsilon \sim N(0, \sigma^2)$ , is equivalent to maximum likelihood using the conditional likelihood

$$
\Pr(Y|X,\theta) = N(f_{\theta}(X),\sigma^2). \tag{2.34}
$$

So although the additional assumption of normality seems more restrictive, the results are the same. The log-likelihood of the data is

$$
L(\theta) = -\frac{N}{2}\log(2\pi) - N\log\sigma - \frac{1}{2\sigma^2}\sum_{i=1}^{N}(y_i - f_{\theta}(x_i))^2, \qquad (2.35)
$$

and the only term involving  $\theta$  is the last, which is  $RSS(\theta)$  up to a scalar negative multiplier.

A more interesting example is the multinomial likelihood for the regression function  $Pr(G|X)$  for a qualitative output G. Suppose we have a model  $Pr(G = \mathcal{G}_k | X = x) = p_{k,\theta}(x), k = 1,\ldots,K$  for the conditional probability of each class given X, indexed by the parameter vector  $\theta$ . Then the

log-likelihood (also referred to as the cross-entropy) is

$$
L(\theta) = \sum_{i=1}^{N} \log p_{g_i, \theta}(x_i), \qquad (2.36)
$$

and when maximized it delivers values of  $\theta$  that best conform with the data in this likelihood sense.

## 2.7 Structured Regression Models

We have seen that although nearest-neighbor and other local methods focus directly on estimating the function at a point, they face problems in high dimensions. They may also be inappropriate even in low dimensions in cases where more structured approaches can make more efficient use of the data. This section introduces classes of such structured approaches. Before we proceed, though, we discuss further the need for such classes.

### 2.7.1 Difficulty of the Problem

Consider the RSS criterion for an arbitrary function  $f$ ,

$$
RSS(f) = \sum_{i=1}^{N} (y_i - f(x_i))^2.
$$
 (2.37)

Minimizing (2.37) leads to infinitely many solutions: any function  $\hat{f}$  passing through the training points  $(x_i, y_i)$  is a solution. Any particular solution chosen might be a poor predictor at test points different from the training points. If there are multiple observation pairs  $x_i, y_{i\ell}, \ell = 1, \ldots, N_i$  at each value of  $x_i$ , the risk is limited. In this case, the solutions pass through the average values of the  $y_{i\ell}$  at each  $x_i$ ; see Exercise 2.6. The situation is similar to the one we have already visited in Section 2.4; indeed, (2.37) is the finite sample version of  $(2.11)$  on page 18. If the sample size N were sufficiently large such that repeats were guaranteed and densely arranged, it would seem that these solutions might all tend to the limiting conditional expectation.

In order to obtain useful results for finite  $N$ , we must restrict the eligible solutions to (2.37) to a smaller set of functions. How to decide on the nature of the restrictions is based on considerations outside of the data. These restrictions are sometimes encoded via the parametric representation of  $f_{\theta}$ , or may be built into the learning method itself, either implicitly or explicitly. These restricted classes of solutions are the major topic of this book. One thing should be clear, though. Any restrictions imposed on f that lead to a unique solution to  $(2.37)$  do not really remove the ambiguity caused by the multiplicity of solutions. There are infinitely many possible restrictions, each leading to a unique solution, so the ambiguity has simply been transferred to the choice of constraint.

In general the constraints imposed by most learning methods can be described as complexity restrictions of one kind or another. This usually means some kind of regular behavior in small neighborhoods of the input space. That is, for all input points  $x$  sufficiently close to each other in some metric,  $\hat{f}$  exhibits some special structure such as nearly constant, linear or low-order polynomial behavior. The estimator is then obtained by averaging or polynomial fitting in that neighborhood.

The strength of the constraint is dictated by the neighborhood size. The larger the size of the neighborhood, the stronger the constraint, and the more sensitive the solution is to the particular choice of constraint. For example, local constant fits in infinitesimally small neighborhoods is no constraint at all; local linear fits in very large neighborhoods is almost a globally linear model, and is very restrictive.

The nature of the constraint depends on the metric used. Some methods, such as kernel and local regression and tree-based methods, directly specify the metric and size of the neighborhood. The nearest-neighbor methods discussed so far are based on the assumption that locally the function is constant; close to a target input  $x_0$ , the function does not change much, and so close outputs can be averaged to produce  $f(x_0)$ . Other methods such as splines, neural networks and basis-function methods implicitly define neighborhoods of local behavior. In Section 5.4.1 we discuss the concept of an equivalent kernel (see Figure 5.8 on page 157), which describes this local dependence for any method linear in the outputs. These equivalent kernels in many cases look just like the explicitly defined weighting kernels discussed above—peaked at the target point and falling away smoothly away from it.

One fact should be clear by now. Any method that attempts to produce locally varying functions in small isotropic neighborhoods will run into problems in high dimensions—again the curse of dimensionality. And conversely, all methods that overcome the dimensionality problems have an associated—and often implicit or adaptive—metric for measuring neighborhoods, which basically does not allow the neighborhood to be simultaneously small in all directions.

## 2.8 Classes of Restricted Estimators

The variety of nonparametric regression techniques or learning methods fall into a number of different classes depending on the nature of the restrictions imposed. These classes are not distinct, and indeed some methods fall in several classes. Here we give a brief summary, since detailed descriptions

are given in later chapters. Each of the classes has associated with it one or more parameters, sometimes appropriately called smoothing parameters, that control the effective size of the local neighborhood. Here we describe three broad classes.

### 2.8.1 Roughness Penalty and Bayesian Methods

Here the class of functions is controlled by explicitly penalizing  $RSS(f)$ with a roughness penalty

$$
PRSS(f; \lambda) = RSS(f) + \lambda J(f). \qquad (2.38)
$$

The user-selected functional  $J(f)$  will be large for functions f that vary too rapidly over small regions of input space. For example, the popular cubic smoothing spline for one-dimensional inputs is the solution to the penalized least-squares criterion

$$
PRSS(f; \lambda) = \sum_{i=1}^{N} (y_i - f(x_i))^2 + \lambda \int [f''(x)]^2 dx.
$$
 (2.39)

The roughness penalty here controls large values of the second derivative of f, and the amount of penalty is dictated by  $\lambda \geq 0$ . For  $\lambda = 0$  no penalty is imposed, and any interpolating function will do, while for  $\lambda = \infty$  only functions linear in  $x$  are permitted.

Penalty functionals J can be constructed for functions in any dimension, and special versions can be created to impose special structure. For example, additive penalties  $J(f) = \sum_{j=1}^{p} J(f_j)$  are used in conjunction with additive functions  $f(X) = \sum_{j=1}^p f_j(X_j)$  to create additive models with smooth coordinate functions. Similarly, *projection pursuit regression* models have  $f(X) = \sum_{m=1}^{M} g_m(\alpha_m^T X)$  for adaptively chosen directions  $\alpha_m$ , and the functions  $g_m$  can each have an associated roughness penalty.

Penalty function, or regularization methods, express our prior belief that the type of functions we seek exhibit a certain type of smooth behavior, and indeed can usually be cast in a Bayesian framework. The penalty J corresponds to a log-prior, and  $PRSS(f; \lambda)$  the log-posterior distribution, and minimizing  $PROS(f; \lambda)$  amounts to finding the posterior mode. We discuss roughness-penalty approaches in Chapter 5 and the Bayesian paradigm in Chapter 8.

### 2.8.2 Kernel Methods and Local Regression

These methods can be thought of as explicitly providing estimates of the regression function or conditional expectation by specifying the nature of the local neighborhood, and of the class of regular functions fitted locally. The local neighborhood is specified by a kernel function  $K_{\lambda}(x_0, x)$  which assigns weights to points x in a region around  $x_0$  (see Figure 6.1 on page 192). For example, the Gaussian kernel has a weight function based on the Gaussian density function

$$
K_{\lambda}(x_0, x) = \frac{1}{\lambda} \exp\left[-\frac{||x - x_0||^2}{2\lambda}\right]
$$
 (2.40)

and assigns weights to points that die exponentially with their squared Euclidean distance from  $x_0$ . The parameter  $\lambda$  corresponds to the variance of the Gaussian density, and controls the width of the neighborhood. The simplest form of kernel estimate is the Nadaraya–Watson weighted average

$$
\hat{f}(x_0) = \frac{\sum_{i=1}^{N} K_{\lambda}(x_0, x_i) y_i}{\sum_{i=1}^{N} K_{\lambda}(x_0, x_i)}.
$$
\n(2.41)

In general we can define a local regression estimate of  $f(x_0)$  as  $f_{\hat{\theta}}(x_0)$ , where  $\hat{\theta}$  minimizes

$$
RSS(f_{\theta}, x_0) = \sum_{i=1}^{N} K_{\lambda}(x_0, x_i) (y_i - f_{\theta}(x_i))^2,
$$
\n(2.42)

and  $f_{\theta}$  is some parameterized function, such as a low-order polynomial. Some examples are:

- $f_{\theta}(x) = \theta_0$ , the constant function; this results in the Nadaraya-Watson estimate in (2.41) above.
- $f_{\theta}(x) = \theta_0 + \theta_1 x$  gives the popular local linear regression model.

Nearest-neighbor methods can be thought of as kernel methods having a more data-dependent metric. Indeed, the metric for k-nearest neighbors is

$$
K_k(x, x_0) = I(||x - x_0|| \le ||x_{(k)} - x_0||),
$$

where  $x_{(k)}$  is the training observation ranked kth in distance from  $x_0$ , and  $I(S)$  is the indicator of the set S.

These methods of course need to be modified in high dimensions, to avoid the curse of dimensionality. Various adaptations are discussed in Chapter 6.

### 2.8.3 Basis Functions and Dictionary Methods

This class of methods includes the familiar linear and polynomial expansions, but more importantly a wide variety of more flexible models. The model for  $f$  is a linear expansion of basis functions

$$
f_{\theta}(x) = \sum_{m=1}^{M} \theta_m h_m(x),
$$
 (2.43)

where each of the  $h_m$  is a function of the input x, and the term linear here refers to the action of the parameters  $\theta$ . This class covers a wide variety of methods. In some cases the sequence of basis functions is prescribed, such as a basis for polynomials in  $x$  of total degree  $M$ .

For one-dimensional x, polynomial splines of degree  $K$  can be represented by an appropriate sequence of  $M$  spline basis functions, determined in turn by  $M - K$  knots. These produce functions that are piecewise polynomials of degree  $K$  between the knots, and joined up with continuity of degree  $K - 1$  at the knots. As an example consider linear splines, or piecewise linear functions. One intuitively satisfying basis consists of the functions  $b_1(x) = 1, b_2(x) = x, \text{ and } b_{m+2}(x) = (x - t_m)_+, m = 1, \ldots, M - 2,$ where  $t_m$  is the mth knot, and  $z_+$  denotes positive part. Tensor products of spline bases can be used for inputs with dimensions larger than one (see Section 5.2, and the CART and MARS models in Chapter 9.) The parameter  $\theta$  can be the total degree of the polynomial or the number of knots in the case of splines.

Radial basis functions are symmetric p-dimensional kernels located at particular centroids,

$$
f_{\theta}(x) = \sum_{m=1}^{M} K_{\lambda_m}(\mu_m, x)\theta_m; \qquad (2.44)
$$

for example, the Gaussian kernel  $K_{\lambda}(\mu, x) = e^{-||x-\mu||^2/2\lambda}$  is popular.

Radial basis functions have centroids  $\mu_m$  and scales  $\lambda_m$  that have to be determined. The spline basis functions have knots. In general we would like the data to dictate them as well. Including these as parameters changes the regression problem from a straightforward linear problem to a combinatorially hard nonlinear problem. In practice, shortcuts such as greedy algorithms or two stage processes are used. Section 6.7 describes some such approaches.

A single-layer feed-forward neural network model with linear output weights can be thought of as an adaptive basis function method. The model has the form

$$
f_{\theta}(x) = \sum_{m=1}^{M} \beta_m \sigma(\alpha_m^T x + b_m), \qquad (2.45)
$$

where  $\sigma(x) = 1/(1 + e^{-x})$  is known as the *activation* function. Here, as in the projection pursuit model, the directions  $\alpha_m$  and the bias terms  $b_m$ have to be determined, and their estimation is the meat of the computation. Details are give in Chapter 11.

These adaptively chosen basis function methods are also known as dictionary methods, where one has available a possibly infinite set or dictionary  $D$  of candidate basis functions from which to choose, and models are built up by employing some kind of search mechanism.

## 2.9 Model Selection and the Bias – Variance Tradeoff

All the models described above and many others discussed in later chapters have a *smoothing* or *complexity* parameter that has to be determined:

- the multiplier of the penalty term;
- the width of the kernel;
- or the number of basis functions.

In the case of the smoothing spline, the parameter  $\lambda$  indexes models ranging from a straight line fit to the interpolating model. Similarly a local degreem polynomial model ranges between a degree- $m$  global polynomial when the window size is infinitely large, to an interpolating fit when the window size shrinks to zero. This means that we cannot use residual sum-of-squares on the training data to determine these parameters as well, since we would always pick those that gave interpolating fits and hence zero residuals. Such a model is unlikely to predict future data well at all.

The k-nearest-neighbor regression fit  $f_k(x_0)$  usefully illustrates the competing forces that affect the predictive ability of such approximations. Suppose the data arise from a model  $Y = f(X) + \varepsilon$ , with  $E(\varepsilon) = 0$  and  $\text{Var}(\varepsilon) = \sigma^2$ . For simplicity here we assume that the values of  $x_i$  in the sample are fixed in advance (nonrandom). The expected prediction error at  $x_0$ , also known as *test* or *generalization* error, can be decomposed:

$$
\begin{array}{rcl}\n\text{EPE}_k(x_0) & = & \text{E}[(Y - \hat{f}_k(x_0))^2 | X = x_0] \\
& = & \sigma^2 + [\text{Bias}^2(\hat{f}_k(x_0)) + \text{Var}_{\mathcal{T}}(\hat{f}_k(x_0))] \tag{2.46}\n\end{array}
$$

$$
= \sigma^{2} + \left[f(x_{0}) - \frac{1}{k} \sum_{\ell=1}^{k} f(x_{(\ell)})\right]^{2} + \frac{\sigma^{2}}{k}.
$$
 (2.47)

The subscripts in parentheses  $(\ell)$  indicate the sequence of nearest neighbors to  $x_0$ .

There are three terms in this expression. The first term  $\sigma^2$  is the *ir*reducible error—the variance of the new test target—and is beyond our control, even if we know the true  $f(x_0)$ .

The second and third terms are under our control, and make up the *mean squared error* of  $f_k(x_0)$  in estimating  $f(x_0)$ , which is broken down into a bias component and a variance component. The bias term is the squared difference between the true mean  $f(x_0)$  and the expected value of the estimate— $[\mathbf{E}_{\mathcal{T}}(\hat{f}_k(x_0)) - f(x_0)]^2$ —where the expectation averages the randomness in the training data. This term will most likely increase with  $k$ , if the true function is reasonably smooth. For small  $k$  the few closest neighbors will have values  $f(x_{(\ell)})$  close to  $f(x_0)$ , so their average should

Image /page/10/Figure/1 description: This is a line graph illustrating the relationship between model complexity and prediction error. The x-axis is labeled 'Model Complexity' and ranges from 'Low' to 'High'. The y-axis is labeled 'Prediction Error'. Two lines are plotted: a cyan line representing the 'Training Sample' and a red line representing the 'Test Sample'. The cyan line shows a decreasing trend in prediction error as model complexity increases, eventually leveling off. The red line shows a decrease in prediction error initially with increasing model complexity, reaching a minimum, and then increasing sharply. At the top of the graph, there are annotations indicating 'High Bias Low Variance' with a dashed arrow pointing left towards lower model complexity, and 'Low Bias High Variance' with a dashed arrow pointing right towards higher model complexity.

FIGURE 2.11. Test and training error as a function of model complexity.

be close to  $f(x_0)$ . As k grows, the neighbors are further away, and then anything can happen.

The variance term is simply the variance of an average here, and decreases as the inverse of k. So as k varies, there is a bias-variance tradeoff.

More generally, as the model complexity of our procedure is increased, the variance tends to increase and the squared bias tends to decrease. The opposite behavior occurs as the model complexity is decreased. For k-nearest neighbors, the model complexity is controlled by  $k$ .

Typically we would like to choose our model complexity to trade bias off with variance in such a way as to minimize the test error. An obvious estimate of test error is the training error  $\frac{1}{N} \sum_i (y_i - \hat{y}_i)^2$ . Unfortunately training error is not a good estimate of test error, as it does not properly account for model complexity.

Figure 2.11 shows the typical behavior of the test and training error, as model complexity is varied. The training error tends to decrease whenever we increase the model complexity, that is, whenever we fit the data harder. However with too much fitting, the model adapts itself too closely to the training data, and will not generalize well (i.e., have large test error). In that case the predictions  $f(x_0)$  will have large variance, as reflected in the last term of expression (2.46). In contrast, if the model is not complex enough, it will underfit and may have large bias, again resulting in poor generalization. In Chapter 7 we discuss methods for estimating the test error of a prediction method, and hence estimating the optimal amount of model complexity for a given prediction method and training set.

# Bibliographic Notes

Some good general books on the learning problem are Duda et al. (2000), Bishop (1995),(Bishop, 2006), Ripley (1996), Cherkassky and Mulier (2007) and Vapnik (1996). Parts of this chapter are based on Friedman (1994b).

## Exercises

Ex. 2.1 Suppose each of K-classes has an associated target  $t_k$ , which is a vector of all zeros, except a one in the kth position. Show that classifying to the largest element of  $\hat{y}$  amounts to choosing the closest target, min<sub>k</sub>  $||t_k \hat{y}$ , if the elements of  $\hat{y}$  sum to one.

Ex. 2.2 Show how to compute the Bayes decision boundary for the simulation example in Figure 2.5.

Ex. 2.3 Derive equation (2.24).

Ex. 2.4 The edge effect problem discussed on page 23 is not peculiar to uniform sampling from bounded domains. Consider inputs drawn from a spherical multinormal distribution  $X \sim N(0, I_p)$ . The squared distance from any sample point to the origin has a  $\chi_p^2$  distribution with mean p. Consider a prediction point  $x_0$  drawn from this distribution, and let  $a =$  $x_0/||x_0||$  be an associated unit vector. Let  $z_i = a^T x_i$  be the projection of each of the training points on this direction.

Show that the  $z_i$  are distributed  $N(0, 1)$  with expected squared distance from the origin 1, while the target point has expected squared distance  $p$ from the origin.

Hence for  $p = 10$ , a randomly drawn test point is about 3.1 standard deviations from the origin, while all the training points are on average one standard deviation along direction a. So most prediction points see themselves as lying on the edge of the training set.

### Ex. 2.5

- (a) Derive equation (2.27). The last line makes use of (3.8) through a conditioning argument.
- (b) Derive equation (2.28), making use of the cyclic property of the trace operator  $[\text{trace}(AB) = \text{trace}(BA)]$ , and its linearity (which allows us to interchange the order of trace and expectation).

Ex. 2.6 Consider a regression problem with inputs  $x_i$  and outputs  $y_i$ , and a parameterized model  $f_{\theta}(x)$  to be fit by least squares. Show that if there are observations with *tied* or *identical* values of  $x$ , then the fit can be obtained from a reduced weighted least squares problem.

Ex. 2.7 Suppose we have a sample of N pairs  $x_i, y_i$  drawn i.i.d. from the distribution characterized as follows:

> $x_i \sim h(x)$ , the design density  $y_i = f(x_i) + \varepsilon_i$ , f is the regression function  $\varepsilon_i \sim (0, \sigma^2)$  (mean zero, variance  $\sigma^2$ )

We construct an estimator for  $f$  linear in the  $y_i$ ,

$$
\hat{f}(x_0) = \sum_{i=1}^N \ell_i(x_0; \mathcal{X}) y_i,
$$

where the weights  $\ell_i(x_0; \mathcal{X})$  do not depend on the  $y_i$ , but do depend on the entire training sequence of  $x_i$ , denoted here by  $\mathcal{X}$ .

- (a) Show that linear regression and k-nearest-neighbor regression are members of this class of estimators. Describe explicitly the weights  $\ell_i(x_0; \mathcal{X})$ in each of these cases.
- (b) Decompose the conditional mean-squared error

$$
\mathbf{E}_{\mathcal{Y}|\mathcal{X}}(f(x_0) - \hat{f}(x_0))^2
$$

into a conditional squared bias and a conditional variance component. Like  $\mathcal{X}, \mathcal{Y}$  represents the entire training sequence of  $y_i$ .

(c) Decompose the (unconditional) mean-squared error

 $E_{\mathcal{V},\mathcal{X}}(f(x_0) - \hat{f}(x_0))^2$ 

into a squared bias and a variance component.

(d) Establish a relationship between the squared biases and variances in the above two cases.

Ex. 2.8 Compare the classification performance of linear regression and  $k$ nearest neighbor classification on the zipcode data. In particular, consider only the 2's and 3's, and  $k = 1, 3, 5, 7$  and 15. Show both the training and test error for each choice. The zipcode data are available from the book website www-stat.stanford.edu/ElemStatLearn.

Ex. 2.9 Consider a linear regression model with p parameters, fit by least squares to a set of training data  $(x_1, y_1), \ldots, (x_N, y_N)$  drawn at random from a population. Let  $\hat{\beta}$  be the least squares estimate. Suppose we have some test data  $(\tilde{x}_1, \tilde{y}_1), \ldots, (\tilde{x}_M, \tilde{y}_M)$  drawn at random from the same population as the training data. If  $R_{tr}(\beta) = \frac{1}{N} \sum_{i=1}^{N} (y_i - \beta^T x_i)^2$  and  $R_{te}(\beta) =$  $\frac{1}{M} \sum_{1}^{M} (\tilde{y}_i - \beta^T \tilde{x}_i)^2$ , prove that

$$
E[R_{tr}(\hat{\beta})] \le E[R_{te}(\hat{\beta})],
$$

### Exercises 41

where the expectations are over all that is random in each expression. [This exercise was brought to our attention by Ryan Tibshirani, from a homework assignment given by Andrew Ng.]
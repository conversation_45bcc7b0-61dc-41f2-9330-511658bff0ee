# Towards Efficient Deep Hashing Retrieval: Condensing Your Data via Feature-Embedding Matching

<PERSON><sup>1\*</sup> <PERSON><PERSON><sup>2\*</sup> <PERSON><PERSON><PERSON><sup>1†</sup> <PERSON><PERSON><PERSON><PERSON><sup>1</sup> <PERSON><PERSON><PERSON><sup>3</sup>

<sup>1</sup> College of Information Science and Technology, Donghua University, Shanghai, China

<sup>2</sup> Department of Computer Science, ETH Zurich, Zurich, Switzerland

<sup>3</sup> College of Electrical Engineering, Zhejiang University, Hangzhou, China

*Abstract*—Deep hashing retrieval has gained widespread use in big data retrieval due to its robust feature extraction and efficient hashing process. However, training advanced deep hashing models has become more expensive due to complex optimizations and large datasets. Coreset selection and Dataset Condensation lower overall training costs by reducing the volume of training data without significantly compromising model accuracy for classification task. In this paper, we explore the effect of mainstream dataset condensation methods for deep hashing retrieval and propose IEM (Information-intensive feature-Embedding Matching), which is centered on distribution matching and incorporates model and data augmentation techniques to further enhance the feature of hashing space. Extensive experiments demonstrate the superior performance and efficiency of our approach.

*Index Terms*—Deep Hashing Retrieval, Dataset Condensation.

## I. INTRODUCTION

In recent years, hashing algorithms have exhibited considerable efficacy in large-scale and high-dimensional data retrieval, owing to their computational efficiency and low storage overhead in practical applications [\[1,](#page-4-0) [12,](#page-4-1) [14,](#page-4-2) [6\]](#page-4-3). With the evolution of deep learning, there has been a growing interest in combining it with hashing techniques for image retrievals [\[2,](#page-4-4) [7,](#page-4-5) [5\]](#page-4-6), a method known as Deep Hashing Retrieval (DHR).

DHR aims to represent the content of an image using compact hashing code and is achieved by comparing the Hamming distance between the query image and the images of database. The training process of DHR is complex and typically involves multiple optimization objectives [\[20,](#page-4-7) [10,](#page-4-8) [5\]](#page-4-6). The complexity and time demands of training a state-of-theart retrieval model have escalated due to multiple optimization objectives and the large-scale nature of real-world datasets. To overcome these challenges, we consider minimizing the amount of training data to ease the demand on computational resources and accelerate the training process. However, a substantial reduction in training data will unavoidably compromise the effect of retrieval model, making it necessary to find a balance between them.

Recent studies have utilized coreset selection [\[4,](#page-4-9) [15,](#page-4-10) [18\]](#page-4-11) to choose representative samples in order to reduce training costs. However, the effectiveness of coreset selection diminishes when applied to small sample sizes. Besides, Dataset Condensation (DC) [\[24,](#page-4-12) [19,](#page-4-13) [23,](#page-4-14) [11,](#page-4-15) [3\]](#page-4-16) encapsulates the rich information of the original dataset into a compact synthetic set, enabling a model trained on it to achieve comparable test accuracy to one trained on the full original dataset. DC has been widely applied in classification tasks. In order to reduce the training costs associated with hashing retrieval, it is worth exploring the potential of DC for reducing the retrieval training set. This leads us to the following question:

*Question: Is it possible to adopt existing techniques to develop a dataset condensation method effectively for deep hashing retrieval?*

To address this question, we conduct an empirical study and observe that DC with distribution matching is more effective for deep hashing retrieval tasks (see Section [II\)](#page-0-0). Based on this, we take perspectives from augmentations on initial network and dataset to further achieve better DC on DHR. We term this approach IEM (Information-intensive Feature-Embedding Matching).

Our contributions can be concluded as follows:

- We highlight that this investigation is the first to address dataset condensation in the context of DHR, establishing the initial baselines for this task.
- We explore that DC with distribution matching proves to be more effective for DHR compared to other mainstream methods. Based on this, we introduce model and data augmentations, leading to the development of our method IEM, a dataset condensation method for DHR.
- Extensive experiments have demonstrated that IEM exhibits exceptional condensation efficiency and can be adapt to existing deep hashing retrieval methods.

<span id="page-0-0"></span>

## II. DATASET CONDENSATION IN DEEP HASHING RETRIEVAL

### *A. Problem Setup*

The original training set of DHR is  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$ , where  $x_i \in \mathbb{R}^d$  denotes the real set, and  $y_i \in \mathcal{Y} =$  $\{0, 1, \ldots, c - 1\}$  denotes the corresponding labels of the real

<sup>&</sup>lt;sup>†</sup>Corresponding author. \*Equal contribution. This work was sponsored in part by the National Natural Science Foundation of China under Grant 62373095, Discipline Innovation Cultivation Program under Grant XKCX202304 and the Fundamental Research Funds for the Central Universities under Grant 2232022G-09.

data, c represents the number of classes. Our goal is to condense real set into smaller synthetic set  $S = \{(s_j, y_j)\}_{j=1}^{|S|}$ , where  $s_j \in \mathbb{R}^d$  denotes the synthetic training set and  $y_i \in \mathcal{Y}$ . The number of each class in synthetic data is smaller than the number of each class in real data, therefore  $|S| \ll |\mathcal{T}|$ . We define that the deep hashing network as:  $f(\theta_a, \theta_b) \triangleq$  $g(\theta_q) \circ h(\theta_h)$ , where  $h : \mathcal{X} \to \mathcal{Z}$  is a feature extractor, and  $g : \mathcal{Z} \to \mathcal{V}$  is a hash layer.  $\mathcal{X}, \mathcal{Z}$  and  $\mathcal{V}$  denote the input of image, the output of feature extractor and the hashing code, respectively. The whole network parameters are  $\theta = {\theta_a, \theta_h}$ .

We denote the optimized weight parameters obtained by minimizing an empirical loss term over the real set  $\mathcal T$  and synthetic training set  $S$  as:

$$
\theta^{\mathcal{T}} = \underset{\theta^{\mathcal{T}}}{\arg\min} \sum_{(x_i, y_i) \in \mathcal{T}} l_{hash} \left( f_{\theta^{\mathcal{T}}}, x_i, y_i \right), \tag{1}
$$

$$
\theta^{S} = \underset{\theta^{S}}{\arg \min} \sum_{(s_{j}, y_{j}) \in S} l_{hash}(f_{\theta^{S}}, s_{j}, y_{j}), \qquad (2)
$$

where  $l_{hash}$  denotes the losses of DHR. We aim to generate the smaller synthetic set S so that  $\theta^{\mathcal{T}}$  can obtain comparable retrieval capacity compared with  $\theta^S$ . Following previous works [\[10,](#page-4-8) [9,](#page-4-17) [17\]](#page-4-18), we take the mean Average Precision (mAP) as evaluation metrics of retrieval quality. In this case, given the synthetic set  $S$ , our objective can be defined as follows:

$$
\mathbb{E}_{\boldsymbol{x}\sim\mathbf{D}_{qu}}[\mathbf{m}\left(f\left(x;\theta^{\mathcal{T}}\right)\right)] \simeq \mathbb{E}_{\boldsymbol{x}\sim\mathbf{D}_{qu}}[\mathbf{m}\left(f\left(x;\theta^{\mathcal{S}}\right)\right)],\quad(3)
$$

where the m denotes the mAP and we employ the same query set  $D_{qu}$  to fairly assess the retrieval quality of both  $\theta^{\mathcal{T}}$  and  $\theta^{\mathcal{S}}$ . Due to space limitations, we encourage readers to consult the relevant literature about DHR for further information [\[5\]](#page-4-6).

<span id="page-1-1"></span>

### *B. Distribution Matching is Better to Condense for DHR*

In order to reduce the training set size of DHR, we explore the effect of coreset selection such as Random (random selection) and Herding [\[4\]](#page-4-9)) and dataset condensation (DSA [\[22\]](#page-4-19) and DM [\[23\]](#page-4-14)), where DSA denotes DC via gradient matching and DM denotes DC via distribution matching. Furthermore, to gain a deeper understanding of how different methods perform in classification versus hashing retrieval, we systematically verify their impacts on these tasks separately. We evaluate the synthetic data of 1/10/50 image(s) per class from CIFAR-10 through ConvNet-3. We employ DHD [\[10\]](#page-4-8) as the benchmark of DHR.

As shown in Fig. [1,](#page-1-0) we observe that (1) irrespective of the task— classification or hashing retrieval—coreset selection is markedly less effective than DC when using smaller synthetic data. Notably, Herding even underperforms compared to Random in hashing retrieval. Therefore, to substantially reduce training costs by reducing training data, coreset selection is not a viable approach here. Furthermore, we find that (2) condensation based on distribution matching is more appropriate for retrieval task. Specifically, DSA outperforms DM in classification task, whereas DM leads in retrieval task. Unlike classification, the complex optimization strategies in DHR may lead to increased bias of gradient-matching, though this is an inherent challenge in DHR.

<span id="page-1-0"></span>Image /page/1/Figure/10 description: The image contains two bar charts side-by-side, labeled (a) Classification and (b) Hashing Retrieval. Both charts have the x-axis labeled "The number of images per class" with values 1, 10, and 50. The y-axis of chart (a) is labeled "Accuracy(%)" and ranges from 0 to 50. The y-axis of chart (b) is labeled "mAP(%)" and ranges from 0 to 40. Each chart displays four bars representing different methods: Random (light gray), Herding (light blue), DSA (orange), and DM (teal). In chart (a), for 1 image per class, the accuracies are approximately 15%, 22%, 27%, and 26%. For 10 images per class, the accuracies are approximately 26%, 30%, 51%, and 49%. For 50 images per class, the accuracies are approximately 55%, 57%, 59%, and 59%. In chart (b), for 1 image per class, the mAP values are approximately 15%, 16%, 18%, and 19%. For 10 images per class, the mAP values are approximately 25%, 28%, 32%, and 34%. For 50 images per class, the mAP values are approximately 38%, 40%, 42%, and 43%.

Fig. 1: (a) The classified performance of condensed data. (b) The retrieval performance of condensed data.

Building on these observations, we intend to apply distribution matching to realize the dataset condensation for the training set of DHR.

## III. METHOD

In order to develop a dataset condensation method that is compatible with DHR, we utilize distribution matching as in Section [II-B.](#page-1-1) However, To fully exploit the benefits of distribution matching in condensing dataset for DHR, we present a method called IEM (Information-intensive Feature-Embedding Matching). The detailed procedure is presented in Fig. [2.](#page-2-0)

The core idea of distribution matching is to match the distribution of feature-embedding between real and synthetic set. From the perspective of DHR, high dimensional featureembedding  $\mathcal{Z} = h(\mathcal{X}, \theta_h)$  is projected through hash layer  $g(\theta_q)$  to generate low dimensional continuous hashing code  $V \in \mathbb{R}^K$  (eg: $k \leq 128$  in almost cases). It is evident that high-quality hashing codes are critical for effective hashing retrieval. Consequently, we aim to transfer the information about the hashing distribution of the original data to the synthetic data from the perspective of DM. However, owing to the reduced semantic information in the continuous hashing code space, we restrict our distribution matching to the featureembedding space, which contains richer semantic information.

In our scenario, feature-embedding space represents the distribution in the continuous hashing codes space approximatively. Despite this, the feature distribution in a small synthetic dataset is insufficiently representative of the real data distribution, causing a degradation in the effectiveness of distribution matching. Therefore, in order to better condense informative synthetic data for training retrieval model, we must increase the diversity within the feature-embedding space. Our approach is grounded in the following two perspectives:

*Network Augmentation.* Naive DM gets the distribution of feature-embedding through the initial networks and proves the pretrained networks performs not better than initial networks through experiments. However, the state of networks influence the obtained feature-embedding directly [\[21\]](#page-4-20). Therefore, we implement a Network augmentation approach to enhance the feature extraction capacity of the initialized network within DM. Inspired by [\[13,](#page-4-21) [21\]](#page-4-20), we use weight perturbation on networks. We observe that adding perturbations to the pretrained models is less effective than perturbing the initialized model  $\theta_{init}$  alone. Hence, we limit the perturbations to the initialized model. The specific process is as follows:

$$
\theta_{aug} = \theta_{init} + \alpha \times \mathbf{d}, \quad \mathbf{d} \leftarrow \frac{\mathbf{d} \times \theta_{init}}{\|\mathbf{d}\|_F}, \quad (4)
$$

<span id="page-2-0"></span>Image /page/2/Figure/0 description: The image depicts a diagram illustrating a machine learning process. On the left, a 'Real Set' of dog images is shown. Below this, an 'Augmented Synthetic Set' is created by resizing and assembling several dog images. These two sets are fed into an 'Augmented Initial Network', represented by interconnected nodes with gradient colors. The network's output is shown as two clusters of data points: one with orange circles and green triangles, and another with blue circles, orange circles, and green triangles. A loss function, labeled 'L\_IEM', connects the outputs of the network to the augmented synthetic set, indicating a feedback loop for backpropagation.

Fig. 2: The illustration of our proposed method (IEM) for DHR. First, we introduce perturbations to the initialized model and enhance the initialized synthetic data. Subsequently, we updata the synthetic data by feature matching between the distributions of the original and synthetic data.

where d is sampled from a Gaussian distribution  $\mathcal{N}(0, 1)$ , and  $\|\cdot\|_F$  represents the Frobenius norm.

*Dataset Augmentation.* In order to substantially increase the characteristic diversity from limited synthetic data, it is essential to consider the synthetic data itself. Spontaneously, we leverage data augmentation to enrich feature embedding. Inspired by IDC [\[11\]](#page-4-15), we utilize mutil-formation to resize the images from the same class and inject these images to each single synthetic data  $s_i$  as in Fig. [2,](#page-2-0) the detailed process is as follows:

<span id="page-2-1"></span>
$$
s_j^c = \text{Assemble}(\text{Resize}\left((s_1^c, \cdots s_i^c), 2l/|i|)\right),\tag{5}
$$

where the  $l$  represents the resolution of real images,  $|i|$ represents the number of real data, usually  $|i| = 4$  for low resolution, and  $|i| = 6$  for high resolution. As in Eq. [5,](#page-2-1) we first start with resizing each image of the same class to  $(2l/|i| \times 2l/|i|)$ , and then assemble the resized segments into a complete synthetic image of  $(l \times l)$ . Here we define the augmented synthetic set as:

$$
S^{J} = \{(s_j, y_j)\}_{j=1}^{|\mathcal{S}^{J}|}
$$
 (6)

After augmentations on both network and dataset, We update the synthetic data by distribution matching loss between real and synthetic set. The loss is as follows:

$$
L_{IEM} = \left\| \frac{1}{|T_c^{\mathcal{T}}|} \sum_{(\mathbf{x}, y) \in T_c^{\mathcal{T}}} \psi_{\theta_{aug}}(a_w(x)) - \frac{1}{|S_c^{\mathcal{T}}|} \sum_{(\mathbf{s}, y) \in S_c^{\mathcal{T}}} \psi_{\theta_{aug}}(a_w(s)) \right\|^2 \right. (7)
$$

The full details are described in Algorithm [1.](#page-2-2) During the training process, synthetic data  $S$  is trained over  $R$  iterations. Each iteration begins with perturbing the initialized network  $\theta_{init}$ , followed by sampling pairs of real set  $T_c$  and synthetic data  $S_c$  batches and augmenting the synthetic data. The mean discrepancy is calculated for each class and aggregated to form the loss  $L_{IEM}$ . The synthetic data is subsequently updated by minimizing  $L_{IEM}$  using stochastic gradient descent with a learning rate  $\eta$ . The number of iterations R is 200 for CIFAR10 and 300 for ImageNet-subset.

## Algorithm 1: IEM

<span id="page-2-2"></span>**Input:** Training set  $T$ 

**Output:** Synthetic set  $S$ 

**Notation:** Number of classes  $N_c$ , Perturbation function  $\mathcal{P}$ , Assemble-Resize function  $\mathcal{A}_{\mathcal{R}}$  Initialize synthetic dataset S

### 2 repeat

3 **for**  $r \leftarrow 0$  *to* R **do** 4 | Perturbation on initial network:  $\theta_{auq} = \mathcal{P}(\theta_{init})$ 5 **for**  $c \leftarrow 0$  to  $N_c$  do 6 | | Sample an intra-class mini-batch:  $T_c \sim \mathcal{T}$ ,  $S_c \sim S$  $7 \mid \cdot \mid$  Apply augmentation on  $S_c^J$ :  $S_c^J = A_{\mathcal{R}}(S_c)$ Update  $S_c^J \leftarrow S_c^J - \eta \nabla_c (L_{IEM})$ 8 end 9 end <sup>10</sup> until *convergence*;

## IV. EXPERIMENTS

### *A. Implementation Details*

Detailed settings. In alignment with previous condensation methodologies [\[11,](#page-4-15) [23\]](#page-4-14), we employ ConvNet-3 [\[16\]](#page-4-22) for the low-resolution dataset CIFAR10, and ResNetAP-10 [\[8\]](#page-4-23) for the high-resolution datasets ImageNet. To assess the performance of condensed datasets in retrieval tasks [\[10,](#page-4-8) [7,](#page-4-5) [9\]](#page-4-17), we train a deep hashing network using condensed data and subsequently test its performance on the query set. Mean Average Precision (mAP) is employed to measure retrieval performance at hashing code lengths of 32 and 64 bits.

Baselines. To evaluate the effectiveness of IEM in hashing retrieval tasks, we utilize two widely recognized dataset condensation methods as baselines, including (1) gradient matching methods: DSA [\[22\]](#page-4-19) and IDC [\[11\]](#page-4-15), and (2) distribution matching method : DM [\[23\]](#page-4-14). As for DHR, we utilize the stateof-the-art method DHD [\[10\]](#page-4-8) as the benchmark.

### *B. Synthetic Set Evaluation.*

CIFAR10. Following previous works [\[22,](#page-4-19) [23,](#page-4-14) [11\]](#page-4-15), we condense CIFAR10 to 1, 10, and 50 image(s) per class. The results provided in Table [I](#page-3-0) indicate that methods such as DSA, DM, and IDC significantly lag behind the performance of the original dataset (Whole set). Nevertheless, IEM exhibits remarkable proficiency across various synthetic data sizes, outperforming IDC by approximately 6% and 12% at 10 and 50 images per class, respectively.

ImageNet-subset. We condense ImageNet-subset to 1 and 3 image(s) per class, with each class containing 1200 images. It is noteworthy that we intentionally avoid employing the pre-trained network trained on the entirety of the ImageNet, which ensures a just assessment of the effects for condensation methods. As illustrated in Table [I,](#page-3-0) IEM demonstrates superior performance, outperforming most of the baseline. However, IDC achieves comparable results with IEM on ImageNet20.

<span id="page-3-0"></span>TABLE I: Comparison of different dataset condensation methods for DHR on CIFAR10 and ImageNet-subset. Img(s)/cls and Ratio (%) refer to the number of images per class and the size of the synthetic set as a percentage of the total training set, respectively.

|                   | <b>Img(s)/cls</b> | <b>Ratio</b> | <b>Bites</b> | <b>Random</b> | <b>DM</b> | <b>DSA</b> | <b>IDC</b> | <b>IEM</b> | <b>Whole set</b> |
|-------------------|-------------------|--------------|--------------|---------------|-----------|------------|------------|------------|------------------|
| <b>CIFAR10</b>    | 1                 | 0.2%         | 32           | 14.83         | 18.46     | 15.12      | 23.67      | 24.21      | 68.15            |
|                   |                   |              | 64           | 15.35         | 18.62     | 15.73      | 24.65      | 25.82      | 70.21            |
|                   | 10                | 2%           | 32           | 22.99         | 29.95     | 27.23      | 38.92      | 45.32      | 68.15            |
|                   |                   |              | 64           | 23.54         | 30.23     | 28.32      | 40.32      | 45.85      | 70.21            |
|                   | 50                | 10%          | 32           | 35.47         | 41.12     | 39.83      | 46.78      | 55.72      | 68.15            |
|                   |                   |              | 64           | 38.82         | 43.43     | 40.58      | 46.21      | 57.49      | 70.21            |
| <b>ImageNet10</b> | 1                 | 0.77%        | 32           | 16.46         | 19.84     | 18.63      | 37.21      | 38.32      | 63.87            |
|                   |                   |              | 64           | 17.62         | 20.43     | 18.42      | 39.55      | 40.32      | 66.05            |
|                   | 3                 | 2.3%         | 32           | 20.48         | 21.95     | 20.93      | 45.78      | 48.69      | 63.87            |
|                   |                   |              | 64           | 21.22         | 24.10     | 21.78      | 48.14      | 52.79      | 66.05            |
| <b>ImageNet20</b> | 1                 | 0.77%        | 32           | 10.47         | 10.22     | 10.94      | 24.89      | 24.23      | 52.05            |
|                   |                   |              | 64           | 10.14         | 11.22     | 10.84      | 26.60      | 25.70      | 57.18            |
|                   | 3                 | 2.3%         | 32           | 10.88         | 12.88     | 11.73      | 32.89      | 33.34      | 52.05            |
|                   |                   |              | 64           | 10.71         | 12.96     | 11.78      | 34.48      | 35.12      | 57.18            |

<span id="page-3-2"></span>Image /page/3/Figure/2 description: The image contains four plots. The top two plots are line graphs showing mAP versus training time in minutes for CIFAR10 and ImageNet20 datasets. The CIFAR10 plot shows that IEM (solid blue line) reaches a mAP of around 0.42 after 200 minutes, while IDC (dashed orange line) reaches a mAP of around 0.39. The ImageNet20 plot shows that both IEM and IDC reach a mAP of around 0.23 after 200 minutes, with IEM slightly higher. The bottom two plots are scatter plots with lines showing mAP versus synthetic images per class for CIFAR10 and ImageNet20 datasets. The CIFAR10 plot shows that IEM (blue circles) reaches a mAP of approximately 55 with 50 synthetic images per class, while IDC (orange squares) reaches a mAP of approximately 47. For 10 synthetic images per class, IEM has a mAP of around 47 and IDC has a mAP of around 41. The ImageNet20 plot shows that both IEM and IDC reach a mAP of approximately 35 with 3 synthetic images per class. For 1 synthetic image per class, IEM has a mAP of around 40 and IDC has a mAP of around 40.

Fig. 3: Comparison of performance across varying training times for condensation on CIFAR10 (a) and ImageNet20 (b). The performance metrics for achieving convergence and their associated times for CIFAR10 (c) and ImageNet20 (d). The diameters of the circles represent the time needed for convergence.

Nonetheless, it is important to acknowledge that the benefits of IDC come at the expense of its own costly condensation process. We provide an in-depth analysis in Section [IV-C.](#page-3-1)

<span id="page-3-1"></span>

### *C. Efficiency Comparison.*

The efficiency of dataset condensation has a significant impact on training time and computational expenses. In order to assess the condensation efficiency, we measure the training time for condensing CIFAR-10 and the ImageNet-subset, utilizing an RTX-2080 Ti and RTX 3090, respectively. As depicted in Fig. [3,](#page-3-2) IEM consistently outperforms IDC across different training times and IEM converges more quickly. Besides, although IDC slightly surpasses IEM on ImageNet20, this advantage is accompanied by substantially higher time and computational expenses (e.g., taking 500 hours for condensing ImageNet10 on RTX 3090). The high efficiency of IEM allows for its efficient deployment while minimizing increases in various operational burdens.

### *D. Generalization Comparison.*

To evaluate the extensive applicability of IEM, we compare its condensation performance using several methods of DHR, including DPN [\[7\]](#page-4-5), OrthoHash [\[9\]](#page-4-17), and DHD [\[10\]](#page-4-8). As illustrated in Fig. [4,](#page-3-3) IEM demonstrates superior performance across all three DHR models in both CIFAR10 and ImageNet10.

<span id="page-3-3"></span>Image /page/3/Figure/9 description: This image contains two bar charts side-by-side, labeled (a) CIFAR10 and (b) ImageNet10. Both charts display the mean average precision (mAP) on the y-axis, ranging from 0 to 60%. The x-axis for both charts is labeled "Deep hashing retrieval methods" and shows three methods: OrthoHash, DPN, and DHD. Each method has three bars representing "Random" (grey), "IDC" (orange), and "IEM" (blue). In chart (a) CIFAR10, for OrthoHash, Random is approximately 35%, IDC is approximately 41%, and IEM is approximately 55%. For DPN, Random is approximately 38%, IDC is approximately 23%, and IEM is approximately 53%. For DHD, Random is approximately 45%, IDC is approximately 50%, and IEM is approximately 58%. In chart (b) ImageNet10, for OrthoHash, Random is approximately 22%, IDC is approximately 25%, and IEM is approximately 43%. For DPN, Random is approximately 23%, IDC is approximately 27%, and IEM is approximately 45%. For DHD, Random is approximately 24%, IDC is approximately 48%, and IEM is approximately 52%.

Fig. 4: The generalization performance of IEM and IDC across DHR methods on CIFAR10 (a) and ImageNet10 (b).

<span id="page-3-4"></span>TABLE II: The results of ablation study. The imgs/cls denotes the number of synthetic images per class.

| Dataset       | NA | DA | mAP          |
|---------------|----|----|--------------|
| CIFAR10       | ✓  | X  | 43.22        |
| (50 imgs/cls) | ✓  | X  | 47.99        |
|               | X  | ✓  | 57.73        |
|               | ✓  | ✓  | <b>57.88</b> |
| ImageNet10    | X  | X  | 25.83        |
| (3imgs/cls)   | ✓  | X  | 30.23        |
|               | X  | ✓  | 47.63        |
|               | ✓  | ✓  | <b>50.38</b> |

These results highlight IEM is compatible with current DHR methods, thereby expanding its potential for deployment in real-world retrieval systems.

### *E. Ablation Study.*

To evaluate the impact of two augmentation strategies in IEM, we perform ablation experiments on Network Augmentation (NA) and Dataset Augmentation (DA), respectively. The results shown in Table [II](#page-3-4) indicate that the performance of IEM is significantly diminished without NA and DA. The concentration effect improves to varying degrees when either DA or NA is used in isolation, but the best results are obtained when both are employed simultaneously.

## V. CONCLUSION

In conclusion, we propose IEM, a novel approach that represents the first endeavor in condensing datasets for DHR. Extensive experimental results demonstrate significant improvements in both performance and efficiency. Notably, IEM integrates seamlessly with existing image hashing retrieval frameworks, enhancing the efficiency of real-world hashing retrieval systems.

# REFERENCES

- <span id="page-4-0"></span>[1] Riccardo Cantini, Fabrizio Marozzo, Giovanni Bruno, and Paolo Trunfio. Learning sentence-to-hashtags semantic mapping for hashtag recommendation on microblogs. *ACM Transactions on Knowledge Discovery from Data (TKDD)*, 16(2):1–26, 2021.
- <span id="page-4-4"></span>[2] Zhangjie Cao, Mingsheng Long, Jianmin Wang, and Philip S Yu. Hashnet: Deep learning to hash by continuation. In *Proceedings of the IEEE international conference on computer vision*, pages 5608–5617, 2017.
- <span id="page-4-16"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022.
- <span id="page-4-9"></span>[4] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. *arXiv preprint arXiv:1203.3472*, 2012.
- <span id="page-4-6"></span>[5] Shiv Ram Dubey. A decade survey of content based image retrieval using deep learning. *IEEE Transactions on Circuits and Systems for Video Technology*, 32(5): 2687–2704, 2021.
- <span id="page-4-3"></span>[6] Bin Fan, Dave G Andersen, Michael Kaminsky, and Michael D Mitzenmacher. Cuckoo filter: Practically better than bloom. In *Proceedings of the 10th ACM International on Conference on emerging Networking Experiments and Technologies*, pages 75–88, 2014.
- <span id="page-4-5"></span>[7] Lixin Fan, Kam Woh Ng, Ce Ju, Tianyu Zhang, and Chee Seng Chan. Deep polarized network for supervised learning of accurate binary hashing codes. In *IJCAI*, pages 825–831, 2020.
- <span id="page-4-23"></span>[8] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016.
- <span id="page-4-17"></span>[9] Jiun Tian Hoe, Kam Woh Ng, Tianyu Zhang, Chee Seng Chan, Yi-Zhe Song, and Tao Xiang. One loss for all: Deep hashing with a single cosine similarity based learning objective. *Advances in Neural Information Processing Systems*, 34:24286–24298, 2021.
- <span id="page-4-8"></span>[10] Young Kyun Jang, Geonmo Gu, ByungSoo Ko, Isaac Kang, and Nam Ik Cho. Deep hash distillation for image retrieval. *arXiv preprint arXiv:2112.08816*, 2021.
- <span id="page-4-15"></span>[11] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022.
- <span id="page-4-1"></span>[12] Ping Li, Arnd Konig, and Wenhao Gui. b-bit minwise hashing for estimating three-way similarities. *Advances in Neural Information Processing Systems*, 23, 2010.
- <span id="page-4-21"></span>[13] Giung Nam, Hyungi Lee, Byeongho Heo, and Juho Lee. Improving ensemble distillation with weight averaging and diversifying perturbation. *arXiv preprint*

*arXiv:2206.15047*, 2022.

- <span id="page-4-2"></span>[14] Prashant Pandey, Michael A Bender, Rob Johnson, and Rob Patro. A general-purpose counting filter: Making every bit count. In *Proceedings of the 2017 ACM international conference on Management of Data*, pages 775–787, 2017.
- <span id="page-4-10"></span>[15] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H Lampert. icarl: Incremental classifier and representation learning. In *Proceedings of the IEEE conference on Computer Vision and Pattern Recognition*, pages 2001–2010, 2017.
- <span id="page-4-22"></span>[16] Levent Sagun, Utku Evci, V Ugur Guney, Yann Dauphin, and Leon Bottou. Empirical analysis of the hessian of over-parametrized neural networks. *arXiv preprint arXiv:1706.04454*, 2017.
- <span id="page-4-18"></span>[17] Shupeng Su, Zhisheng Zhong, and Chao Zhang. Deep joint-semantics reconstructing hashing for large-scale unsupervised cross-modal retrieval. In *Proceedings of the IEEE/CVF international conference on computer vision*, pages 3027–3035, 2019.
- <span id="page-4-11"></span>[18] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018.
- <span id="page-4-13"></span>[19] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-4-7"></span>[20] Li Yuan, Tao Wang, Xiaopeng Zhang, Francis EH Tay, Zequn Jie, Wei Liu, and Jiashi Feng. Central similarity quantization for efficient image and video retrieval. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 3083–3092, 2020.
- <span id="page-4-20"></span>[21] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. *arXiv preprint arXiv:2212.06152*, 2022.
- <span id="page-4-19"></span>[22] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021.
- <span id="page-4-14"></span>[23] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023.
- <span id="page-4-12"></span>[24] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.
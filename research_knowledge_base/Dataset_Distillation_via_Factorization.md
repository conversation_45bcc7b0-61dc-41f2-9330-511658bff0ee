# Dataset Distillation via Factorization

Songhua <PERSON> Wang National University of Singapore {songhua.liu,e0823044,xyang}@u.nus.edu, {jingweny,xinchao}@nus.edu.sg

## Abstract

In this paper, we study dataset distillation (DD), from a novel perspective and introduce a *dataset factorization* approach, termed *HaBa*, which is a plug-andplay strategy portable to any existing DD baseline. Unlike conventional DD approaches that aim to produce distilled and representative samples, *HaBa* explores decomposing a dataset into two components: data *Ha*llucination networks and *Ba*ses, where the latter is fed into the former to reconstruct image samples. The flexible combinations between bases and hallucination networks, therefore, equip the distilled data with exponential informativeness gain, which largely increase the representation capability of distilled datasets. To furthermore increase the data efficiency of compression results, we further introduce a pair of adversarial contrastive constraints on the resultant hallucination networks and bases, which increase the diversity of generated images and inject more discriminant information into the factorization. Extensive comparisons and experiments demonstrate that our method can yield significant improvement on downstream classification tasks compared with previous state of the arts, while reducing the total number of compressed parameters by up to 65%. Moreover, distilled datasets by our approach also achieve ~10% higher accuracy than baseline methods in cross-architecture generalization. Our code is available [here.](https://github.com/Huage001/DatasetFactorization)

# 1 Introduction

The success of deep models on a variety of vision tasks, such as image classification [\[26,](#page-11-0) [11,](#page-10-0) [38\]](#page-11-1), object detection  $[37, 36]$  $[37, 36]$  $[37, 36]$ , and semantic segmentation  $[43, 56, 29]$  $[43, 56, 29]$  $[43, 56, 29]$  $[43, 56, 29]$  $[43, 56, 29]$ , is largely attributed to the huge amount of data used for training and various pre-trained models [\[57\]](#page-12-1). However, the sheer amount of data introduces significant obstacles for storage, transmission, and data pre-processing. Besides, publishing raw data inevitably brings about privacy or copyright issue in practice [\[44,](#page-11-6) [10\]](#page-10-1). To alleviate these problems, Wang *et al*. [\[52\]](#page-12-2) pioneer the research of dataset distillation (DD), to distill a large dataset into a synthetic one with only a limited number of samples, so that the training efforts with the distilled dataset for downstream models on the original dataset can be largely reduced, which facilitates a series of applications like continual learning [\[41,](#page-11-7) [40,](#page-11-8) [54,](#page-12-3) [31\]](#page-11-9) and black-box optimization [\[7\]](#page-10-2). Due the significant practical value of DD, many endeavours have been made on this area [\[62,](#page-12-4) [60,](#page-12-5) [61,](#page-12-6) [51,](#page-12-7) [24,](#page-11-10) [6,](#page-10-3) [63\]](#page-12-8) to design novel supervision signals to train the synthetic datasets and to further improve their performances.

Nevertheless, there is a potential drawback in conventional settings of DD: it largely treats each synthetic sample independently and ignores the inter coherence and relationship between different instances. As such, the information embraced by each sample, despite distilled, is by nature limited. Using the synthetic samples for training downstream models, therefore, inevitably leads to the loss of dataset information. Moreover, the few distilled samples are incompatible with the enormous number of parameters in a deep model and may yield the risk of overfitting.

To verify these potential issues, we conduct a pre-experiment on CIFAR10 dataset with 10 synthetic images per class, using MTT [\[6\]](#page-10-3), the current SOTA solution on DD, as the baseline. In addition

36th Conference on Neural Information Processing Systems (NeurIPS 2022).

<span id="page-1-1"></span>Image /page/1/Figure/0 description: This is a diagram illustrating a process called Dataset Factorization. On the top left, a red arrow points down from the text "Dataset Factorization" to a dashed box labeled "Real Dataset". Inside this box are several images of horses and birds. An arrow leads from the "Real Dataset" box to a red rectangle labeled "Performance Matching". Below the "Dataset Factorization" text, there are two dashed boxes. The left box contains two noisy, abstract images labeled "Bases". An arrow connects these "Bases" to a series of interconnected shapes labeled H1, H2, H3, and so on, representing "Hallucinators". An arrow leads from these "Hallucinators" to a dashed box labeled "Synthetic Dataset", which contains images that resemble the horses and birds from the "Real Dataset" but are more abstract or distorted. An arrow also leads from the "Synthetic Dataset" to the "Performance Matching" box.

Figure 1: Intuition of our hallucinator-basis factorization for dataset distillation.

to the baseline setting, we also incorporate all the checkpoint synthetic datasets after each 100 DD iterations in the convergent stage to train the downstream model. Since the synthetic images are fine-tuned during this stage, multiple checkpoints can be viewed as related but different, which may somehow increase the diversity. As a result, it yields overall lower test loss and hence better final results in downstream training, as shown in the blue and green curves in Fig. [2,](#page-1-0) which indicates that current DD solutions can be potentially improved by leveraging some sample-wise relationships to diversify the distilled data. Nevertheless, simply involving more data samples may also increase the memory overhead. This fact motivates us to ask: *is it possible to encode some shared relationships in a dataset implicitly, instead of storing samples directly, to avoid such additional storage costs?*

We show in this paper that, it can indeed be made possible through reformulating the DD task as a *factorization* problem. As shown in Fig. [1,](#page-1-1) we propose a novel perspective dubbed *HaBa*, to factorize a dataset into two compositions: data *Ha*llucination networks and *Ba*ses. A data hallucination network, or hallucinator, can take any basis as input and output the corresponding hallucinated image. Supervised by the training objective of DD, a set of hallucinators can synthesize multiple samples from a common basis and are optimized to extract effective relationships among different samples in original datasets explicitly. In this way, information of  $|\mathcal{H}| \times |\mathcal{B}|$  images can be included for a factorization result with  $|\mathcal{H}|$  hallucinators and  $|\mathcal{B}|$  bases via arbitrary pair-wise combination, which improves the data efficiency of traditional DD exponentially. As shown in the yellow curve in Fig. [2,](#page-1-0) with the same budget on the storage, our strategy achieves better test performance compared with the MTT baseline.

<span id="page-1-0"></span>Image /page/1/Figure/4 description: A line graph shows the test loss over 500 epochs. The x-axis is labeled "Epochs" and ranges from 100 to 500. The y-axis is labeled "Test Loss" and ranges from 1.0 to 1.3. Three lines represent different methods: MTT (blue), MTT\_more (green), and Ours (yellow). All three lines show a decreasing trend in test loss as the number of epochs increases. The "Ours" line consistently shows the lowest test loss throughout the epochs, followed by "MTT\_more", and then "MTT" which has the highest test loss.

Figure 2: Visualization of test loss using synthetic datasets generated by MTT, MTT with multiple checkpoints, and ours.

To further increase the informativeness of factorized results, we introduce a pair of adversarial contrastive constraints to promote sample-wise diversity. The goal of HaBa is to minimize the correlation among images composed of different hallucinators but a common basis, while an adversary tries to maximize it. Such an adversarial scheme, in turn, enforces the hallucinators to produce diversified images and increases the amount of useful information.

Notably, HaBa is a versatile strategy that can be built upon existing DD baselines, since it is compatible with any training objective for measuring the similarity between downstream performances as shown in Fig. [1,](#page-1-1) We conduct extensive experiments to demonstrate the advantages of the proposed method over baseline ones. In all benchmarks and comparisons, HaBa produces significant and consistent improvement on training downstream models, while reducing the total number of compressed parameters by up to 65%. Furthermore, it demonstrates strong cross-architecture generalization ability with accuracy improvement higher than 10%. Our contributions are summarized as follows:

- We study dataset factorization, a novel perspective to explore dataset distillation, and propose a novel approach termed HaBa for hallucinator-basis factorization.
- We present a pair of adversarial contrastive objectives to further increase the data diversity and information capability.

• HaBa is a plug-and-play scheme compatible with all existing training objectives of DD and can yield significant and consistent improvement over the state of the arts.

# 2 Related Works

The goal of dataset distillation (DD) is to optimize a smaller synthetic dataset such that it is capable to take place of original one for training downstream tasks, which is different from coreset selection [\[1,](#page-10-4) [8,](#page-10-5) [15,](#page-10-6) [42,](#page-11-11) [48\]](#page-11-12), another branch for dataset compression, directly selecting samples from raw datasets. In this section, we provide a detailed review of previous methods in DD.

Motivated from knowledge distillation [\[18,](#page-10-7) [14,](#page-10-8) [59,](#page-12-9) [58\]](#page-12-10) aiming at model compression, Wang *et al*. [\[52\]](#page-12-2) introduce the concept of dataset distillation for dataset compression. The idea is to optimize the synthetic images so that they can minimize loss functions of downstream tasks, where a bilevel optimization algorithm [\[13\]](#page-10-9) is involved. Following this routine, several works further consider learnable labels beyond samples [\[4,](#page-10-10) [46\]](#page-11-13). Subsequently, Zhao *et al*. [\[62\]](#page-12-4) and several following approaches [\[60,](#page-12-5) [28\]](#page-11-14) consider matching gradients of a downstream model produced by synthetic samples and real images, which improve the performance significantly. Most recently, Cazenavette *et al*. [\[6\]](#page-10-3) argue that single-iteration gradient matching may lead to inferior performance due to error accumulation across multiple steps and thereby propose to match long-range training dynamics of an expert trained on the original dataset. As an alternative method to profile training effects produced by different sets, Nguyen *et al*. [\[33,](#page-11-15) [34\]](#page-11-16) also introduce the kernel ridge-regression approach based on the Neural Tangent Kernel (NTK) in infinitely wide convolutional networks [\[20\]](#page-10-11).

Apart from matching training effects, there are also methods matching data distributions between original and synthetic datasets. For instance, Zhao *et al*. [\[61\]](#page-12-6) propose a simple but effective Maximum Mean Discrepancy (MMD) constraint for DD, which does not involve the training of downstream models and enjoys superior training efficiency. Wang *et al*. [\[51\]](#page-12-7) propose CAFE, explicitly attempting to align the synthetic and real distributions in the feature space of a downstream network.

Above mentioned methods are dedicated to exploring suitable training objectives and pipelines for DD. However, there are few works concerning improving the data efficiency for distilled samples. Although Zhao *et al*. [\[60\]](#page-12-5) propose differentiable siamese augmentation (DSA) to enrich the training data, the augmentation operations used, *e.g.*, crop, flip, scale, and rotation, cannot encode any information about the target datasets. In this paper, we study the task in a factorization perspective, to factorize a dataset into two different compositions: data hallucination networks and bases. Both parts carry important knowledge of the raw dataset. For downstream training, hallucinators and bases can perform arbitrary pair-wise combination, *i.e.*, sending any basis to any hallucinator, to create a training sample. The idea of factorization can improve the diversity of distilled training datasets significantly, without introducing additional costs for storage. It is also a versatile strategy compatible with all aforementioned DD methods, which will be demonstrated in the experiment part.

Concurrent Works on Efficient Distilled Dataset Parameterization: As a concurrent work, Kim *et al*. [\[24\]](#page-11-10) propose IDC for efficient synthetic data parameterization. It reveals that only storing down-sample version of synthetic images and conducting bilinear upsampling in downstream training would not hurt the performance much. Thus, given the same budget of storage, it can store  $4\times$ number of  $2\times$  down-sample synthetic images compared with the baseline. Both IDC and HaBa in this paper are dedicated to improving the data efficiency of synthetic parameters. Interestingly, according to the definition of our hallucinator-basis factorization, IDC can in fact be treated as a special case of HaBa, where the hallucinator is a parameter-free upsampling function and each basis has a smaller spatial size. Nevertheless, the main focuses for IDC and HaBa are different and they are in fact two orthogonal techniques, which can readily join force to enhance the baseline performance, as discussed in Sec. [4.2.](#page-6-0)

## 3 Methods

In this section, we elaborate our proposed method *HaBa* for dataset distillation (DD). Assume that there is an original dataset  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  with  $|\mathcal{T}|$  pairs of a training sample  $x_i$  and the corresponding label  $y_i$ . DD targets a synthetic dataset  $\mathcal{S} = \{(\hat{x}_i, \hat{y}_i)\}_{i=1}^{|\mathcal{S}|}$  with  $|\mathcal{S}| \ll |\mathcal{T}|$  and expects that a model trained on  $S$  can have similar performance than that trained on  $T$ .

<span id="page-3-0"></span>Image /page/3/Figure/0 description: This figure illustrates a deep learning architecture for image generation and manipulation. It shows three datasets labeled B, H, and T, from which samples are drawn. The samples from B and H are processed through two branches, one labeled 'Down Stream' and another labeled 'Adv.' (Adversarial). The 'Down Stream' branch outputs losses L\_DD and L\_cos. for B and H. The 'Adv.' branch processes samples from H and outputs losses L\_task and L\_con. for Adv. The figure also depicts a component labeled H, which includes an Encoder, a Decoder, and operations involving sigma (σ) and mu (μ), suggesting a variational autoencoder structure. The overall diagram visualizes a system that likely involves domain adaptation or generative modeling, with distinct loss functions guiding different parts of the network.

Figure 3: Left: Overall pipeline of the proposed hallucinator-basis factorization.  $\beta$ ,  $\mathcal{H}$ , and  $\mathcal{T}$ denote sets of bases, hallucinators, and original data respectively. *Adv.* denotes an adversary model. We adopt batch size 2 here for clarity; Right: Architecture of a hallucinator in detail.

Traditional DD methods treat each synthetic sample independently and ignore the inner relationship between different samples within a dataset, which results in poor data/information efficiency. Focusing on such drawback, we study DD from a novel perspective and redefine it as a hallucinator-basis factorization problem:

$$
S = \{H_{\theta_j}\}_{j=1}^{|\mathcal{H}|} \cup \{(\hat{x}_i, \hat{y}_i)\}_{i=1}^{|\mathcal{B}|},\tag{1}
$$

where there are  $|\mathcal{H}|$  hallucination networks and  $|\mathcal{B}|$  bases. The j-th hallucinator is parameterized by  $\theta_i$  and we denote it by  $H_{\theta_i}$  for  $1 \leq j \leq |\mathcal{H}|$ . For downstream training, a training data pair  $(\tilde{x}_{ij}, \tilde{y}_{ij})$ is created online via sending the *i*-th basis, with any  $1 \le i \le |\mathcal{B}|$ , to the *j*-th hallucinator, with any  $1 \le j \le |\mathcal{H}|$ , *i.e.*,  $\tilde{x}_{ij} = H_{\theta_j}(\hat{x}_i)$ . In this paper, the label  $\tilde{y}_{ij}$  is simply taken as  $\hat{y}_i$ .

An overview of our method is shown in Fig. [3\(](#page-3-0)Left). To go deeper into the technical details, we first start with the introduction of our basis and data hallucination network in Sec. [3.1.](#page-3-1) Then, we propose an adversarial contrastive constraint to increase data diversity in Sec. [3.2.](#page-3-2) Finally, we present the whole training pipeline of the hallucinator-basis factorization for DD in Sec. [3.3.](#page-4-0)

#### <span id="page-3-1"></span>3.1 Basis and Hallucinator

**Basis:** Typically, for an image classification dataset  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$ ,  $x_i \in \mathbb{R}^{h \times w \times c}$  and  $y_i \in$  $\{0, 1, ..., C-1\}$  for each  $1 \le i \le |\mathcal{T}|$ , where each  $x_i$  is a c-channel image with a resolution of  $h \times w$ , and  $C$  is the total number of classes. In previous DD methods, the format/shape of synthetic data pairs  $(\hat{x}, \hat{y})$  has to be held the same as that of real data, so as to make sure the consistency between input and output formats in the training and test time for downstream models. By contrast, since hallucinator networks are capable of spatial-wise and channel-wise transformation, the shape of each  $\hat{x}_i, 1 \leq i \leq |\mathcal{B}|$ , denoted as  $\hat{h}' \times w' \times c'$ , is not necessarily the same as that of original samples and thus more flexible. And for a classification problem, we do not modify its label space in this paper for simplicity and maintain the categorical format.

**Hallucinator:** Given a basis  $\hat{x} \in \mathbb{R}^{h' \times w' \times c'}$ , a data hallucination network, aims to create a new image  $\tilde{x} \in \mathbb{R}^{h \times w \times c}$  based on  $\hat{x}$ , which can be viewed as a conditional image generation problem. Inspired by image style transfer  $[22, 19, 21, 30]$  $[22, 19, 21, 30]$  $[22, 19, 21, 30]$  $[22, 19, 21, 30]$  $[22, 19, 21, 30]$  $[22, 19, 21, 30]$  $[22, 19, 21, 30]$ , a typical conditional image generation problem, we devise an encoder-transformation-decoder based architecture for hallucinators, as shown in Fig.  $3(Right)$  $3(Right)$ . Specifically, the encoder, denoted as enc, is composed of CNN blocks, which non-linearly maps an input  $\hat{x}$  to a feature space  $\mathbb{R}^{h'' \times w'' \times c''}$ . Then, an affine transformation with scale  $\sigma$  and shift  $\mu$  is conducted on the derived feature, where  $\sigma$  and  $\mu$  are treated as network parameters in this paper. At last, the decoder dec under a symmetric CNN architecture with enc projects the transformed feature back to the image space. Formally, this process can be written as:

$$
\hat{f} = enc(\hat{x}), \quad \tilde{f} = \sigma \times \hat{f} + \mu, \quad \tilde{x} = dec(\tilde{f}), \tag{2}
$$

where the multiplication is element-wise operation. There are  $|\mathcal{H}|$  hallucinators in the whole factorization pipeline and each would be trained to implicitly encode some sample-wise relations by its network parameters.

#### <span id="page-3-2"></span>3.2 Adversarial Contrastive Constraint

Ideally, the knowledge encoded by different hallucinators should be as different/orthogonal as possible to get the most benefits for each individual. To instantiate such regularization, let's consider two

composed images  $\tilde{x}_{ij}$  and  $\tilde{x}_{ik}$  from two different hallucinators  $H_{\theta_j}$  and  $H_{\theta_k}$  but a common basis  $\hat{x}_i$ . The divergence between  $\tilde{x}_{ij}$  and  $\tilde{x}_{ik}$  is expected to be large. To measure the divergence, a feature extractor is required to map an input image to a feature space, and how to train such a feature extractor to find an appropriate feature space is of great importance.

In this paper, we formalize the training of hallucinators and the feature extractor as a min-max game in a self-consistent manner, where the feature extractor desires to minimize the divergence between  $\tilde{x}_{ij}$  and  $\tilde{x}_{ik}$  while hallucinators, as well as bases, are optimized to maximize it so that the two players can reinforce each other. In specific, the feature extractor, denoted as F and parameterized by  $\psi$ , is typically a CNN structure for the downstream task and we adopt features at the last hidden layer before the output layer, denoted as  $F_{-1}(\tilde{x}_{ij})$  and  $F_{-1}(\tilde{x}_{ik})$ . F is optimized to maximize the correlation between the two feature vectors, which can be quantified by the metric of mutual information (MI). Inspired by the lower bound of MI  $[49]$ , the objective to minimize the divergence for F is given by the following contrastive form:

<span id="page-4-1"></span>
$$
\mathcal{L}_{con.} = -\frac{1}{|\mathcal{H}|^2} \frac{1}{|\mathcal{B}|} \sum_{\substack{1 \le j,k \le |\mathcal{H}|, \\ j \ne k}} \sum_{i=1}^{|\mathcal{B}|} \log \frac{\exp \{ F_{-1}^\top(\tilde{x}_{ij}) F_{-1}(\tilde{x}_{ik}) / \tau \}}{\sum_{u=1}^{|\mathcal{B}|} \exp \{ F_{-1}^\top(\tilde{x}_{ij}) F_{-1}(\tilde{x}_{uk}) / \tau \}}, \tag{3}
$$

where  $\tau$  is a scalar temperature coefficient. For the classification problem, we can alternatively adopt the supervised form of the contrastive loss  $\mathcal{L}_{con.}$ , where  $\tilde{x}_{uk}$  with the same class label as  $\tilde{x}_{ij}$ are also taken into consideration as positive samples in Eq. [3.](#page-4-1) The supervised contrastive loss can benefit to increase the correlation of samples from the same class [\[23\]](#page-10-15) for a more reasonable feature representation.

In addition, the feature space is expected to reflect the task-specific property for a meaningful representation. Thus, we also incorporate the task loss  $\mathcal{L}_{task}$ , *e.g.*, cross-entropy loss in classification tasks, over the synthetic dataset as a supervision signal for  $F$ . In this way, the overall training objective for  $F$  is defined as:

<span id="page-4-2"></span>
$$
\min_{\psi} \mathcal{L}_F = \lambda_{con} \mathcal{L}_{con.} + \lambda_{task} \mathcal{L}_{task},\tag{4}
$$

where  $\lambda_{con.}$  and  $\lambda_{task}$  are hyper-parameters controlling the weight for each term.

F acts as an adversary to minimize the divergence between  $\tilde{x}_{ij}$  and  $\tilde{x}_{ik}$ , while the synthetic dataset is expected to maximize it to increase data diversity. To this ends, the similarity between  $F_{-1}(\tilde{x}_{ij})$ and  $F_{-1}(\tilde{x}_{ik})$  becomes one loss term for hallucinator-basis factorization. In this paper, we adopt the cosine-similarity and the objective  $\mathcal{L}_{\cos}$  is given by:

$$
\mathcal{L}_{cos.} = \frac{1}{|\mathcal{H}|^2} \frac{1}{|\mathcal{B}|} \sum_{\substack{1 \le j,k \le |\mathcal{H}| \\ j \ne k}} \sum_{i=1}^{|\mathcal{B}|} \frac{F_{-1}^{\top}(\tilde{x}_{ij}) F_{-1}(\tilde{x}_{ik})}{\|F_{-1}(\tilde{x}_{ij})\|_2 \|F_{-1}(\tilde{x}_{ik})\|_2}.
$$
(5)

During training, the feature extractor and the factorized components are updated alternately to play this min-max game.

#### <span id="page-4-0"></span>3.3 Factorization Training Pipeline

Following previous paradigms [\[62,](#page-12-4) [61,](#page-12-6) [6,](#page-10-3) [51\]](#page-12-7), the synthetic dataset S is updated in an iterative algorithm. In each iteration, we randomly sample a batch of hallucinators and bases and conduct pair-wise combinations. The composed images are evaluated by the objective of dataset distillation  $\mathcal{L}_{DD}$  and the similarity metric in Eq. [5:](#page-4-2)

<span id="page-4-3"></span>
$$
\min_{S} \mathcal{L}_{S} = \lambda_{DD} \mathcal{L}_{DD} + \lambda_{cos} \mathcal{L}_{cos},
$$
\n(6)

where hyper-parameters  $\lambda_{DD}$  and  $\lambda_{cos}$  balance the loss.

Notably, the hallucinator-basis factorization is compatible with a variety of configurations of  $\mathcal{L}_{DD}$ by previous arts, which makes it a versatile and effective strategy for DD. In this paper, we adopt the trajectories matching loss in Cazenavette *et al.* [\[6\]](#page-10-3) as  $\mathcal{L}_{DD}$  by default thanks to its superior performance. The basic idea is to update a downstream model from a cached checkpoint  $\phi_t^*$  at iteration t, using the synthetic dataset S for N times, and using the real dataset  $T$  for M times

respectively. The updated parameters by the two cases,  $\hat{\phi}_{t+N}$  and  $\phi^*_{t+M}$  are enforced to be consistent:

$$
$\hat{\phi}_{t+n+1} \leftarrow \hat{\phi}_{t+n} - \alpha \nabla_{\hat{\phi}_{t+n}} \mathcal{L}_{task}(\mathcal{S})$ ,  $\hat{\phi}_t \leftarrow \phi_t^*$ ,  $0 \le n < N$
$$

$$
$\phi_{t+m+1}^* \leftarrow \phi_{t+m}^* - \beta \nabla_{\phi_{t+m}^*} \mathcal{L}_{task}(\mathcal{T})$ ,  $0 \le m < M$
$$

$$
\mathcal{L}_{DD} = \frac{\|\hat{\phi}_{t+N} - \phi_{t+M}^*\|_2^2}{\|\phi_t^* - \phi_{t+M}^*\|_2^2}
$$
(7)

where  $\alpha$  and  $\beta$  are learning rates with S and T respectively.  $\alpha$  is learnable in the framework while  $\beta$ is a hyper-parameter. In Sec. [4.2,](#page-6-0) we also experiment with other settings of  $\mathcal{L}_{DD}$ .

Based on the supervised signals in Eq. [6,](#page-4-3) the gradients are backward propagated to the composed images and finally to the sampled hallucinators and bases so as to be updated using a decent algorithm such as SGD. Since all the operations are differentiable, the training can be completed end-to-end.

## 4 Experiments

<span id="page-5-0"></span>

### 4.1 Datasets and Implementing Details

We conduct evaluations of our method on three standard image classification benchmarks: SVHN [\[32\]](#page-11-18), CIFAR10, and CIFAR100 [\[25\]](#page-11-19). There are 60,000 images for real-world digit recognition in SVHN. For CIFAR10 and CIFAR100, there are 50,000 training images in total. The number of classes for the three datasets are 10, 10, and 100 respectively. All the images are under  $32 \times 32$  resolution in 3-channel RGB format. Following previous works [\[6\]](#page-10-3), we use ZCA for image preprocessing with Kornia implementation [\[39\]](#page-11-20) before all the experiments. Experiments with more datasets, including images in larger spatial scales, can be found in the supplement.

In this paper, for convenience of comparisons with prior works, we maintain the same size with images in original datasets, *i.e.*,  $h' = \hat{h}$ ,  $w' = w$ , and  $c' = 3$  for bases. We also experiment with other sizes of bases in Sec. [4.3.](#page-7-0) For hallucinators, the encoder and decoder contain 1 Conv-ReLU blocks. The number of feature channel  $c''$  is 3. We use 5 hallucinators by default. The learning rates of hallucinators and bases,  $\eta_H$  and  $\eta_B$ , are the same and for the feature extractor, the learning rate  $\eta_F$  is 0.001. Hyper-parameters  $\lambda_{con.}, \lambda_{task}, \lambda_{DD}$ , and  $\lambda_{cos.}$  are set as 0.1, 1, 1, and 0.1 empirically. Sensitivities of these hyper-parameters are analyzed in Sec. [4.3.](#page-7-0) The adversary network has the same architecture as that for computing  $\mathcal{L}_{DD}$ . In experiments on SVHN and CIFAR10, we incorporate all the bases in each iteration, while in experiments on CIFAR100, we adopt a batch size of 300 when the total number of bases is greater than 1,000. We only consider random 2 hallucinators in one iteration for simplicity. The maximal configuration of computational resources is 4 24GB 3090 GPUs. The GPU memory consumption is dependent on that of the baseline method for  $\mathcal{L}_{DD}$  and is slightly higher than it due to the computation of  $\mathcal{L}_{cos.}$  and  $\mathcal{L}_{con.}$ . The baseline method for  $\mathcal{L}_{DD}$  is MTT [\[6\]](#page-10-3) if not specified. Other settings related to DD hold the same as the baseline. All the quantitative results are based on the mean and standard deviation over 5 repeated experiments. To make sure fair comparisons, the dataset size in our method is equal to the number of bases  $|\mathcal{B}|$  and the hallucinators are treated as parameterized data augmentors working online in downstream training, just as general data augmentations, which means that the dataset size does not increase compared with the baselines.

### 4.2 Comparisons

Comparisons with State of the Arts: We compare HaBa with previous state of the arts for DD in standard settings, to synthesize 1, 10, and 50 images per class (IPC) respectively. In our setting, the number of parameters in a hallucinator is is approximately equal to that for 2 synthetic images, while the size of a basis is equal to that of an image. Taking the storage cost of 5 hallucinators into consideration, we set the number of bases per class (BPC) as IPC minus 1 in each IPC configuration when IPC is greater than 1, to make the comparisons as fair as possible. Candidates are coreset based methods including Random  $[8, 35]$  $[8, 35]$  $[8, 35]$ , Herding  $[5, 2]$  $[5, 2]$  $[5, 2]$ , K-Center  $[12, 42]$  $[12, 42]$  $[12, 42]$ , and Forgetting [\[47\]](#page-11-22), meta learning based methods including DD [\[52\]](#page-12-2) and LD [\[4\]](#page-10-10), training matching based methods including DC  $[62]$ , DSA  $[60]$ , and MTT  $[6]$ , and distribution matching based methods including DM  $[61]$  and CAFE  $[51]$ . The comparisons follow the standard protocol adopting a 3-layer Conv-InstanceNorm-ReLU-AvgPool ConvNet with 128 channels in training and testing.

<span id="page-6-0"></span>

|                      | Dataset                                                                                                                         |       | <b>SVHN</b>                                                                                  |                                              |      | CIFAR10        |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              | CIFAR100                                                         |                                                                               |                                                                   |
|----------------------|---------------------------------------------------------------------------------------------------------------------------------|-------|----------------------------------------------------------------------------------------------|----------------------------------------------|------|----------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------|-------------------------------------------------------------------------------|-------------------------------------------------------------------|
|                      | <b>IPC</b><br>Ratio %                                                                                                           | 0.014 | 10<br>0.14                                                                                   | 50<br>0.7                                    | 0.02 | 10<br>0.2      | 50                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | 0.2                                                              | 10<br>$\overline{c}$                                                          | 50<br>10                                                          |
| Coreset              | Random<br>Herding<br>K-Center<br>Forgetting                                                                                     |       |                                                                                              |                                              |      |                | $14.6 \pm 1.6$ 35.1 $\pm$ 4.1 70.9 $\pm$ 0.9 14.4 $\pm$ 2.0 26.0 $\pm$ 1.2 43.4 $\pm$ 1.0<br>$20.9 \pm 1.3$ 50.5 $\pm$ 3.3 72.6 $\pm$ 0.8 21.5 $\pm$ 1.3 31.6 $\pm$ 0.7 40.4 $\pm$ 0.6<br>$21.0 \pm 1.5$ 14.0 $\pm 1.3$ $20.1 \pm 1.4$ $21.5 \pm 1.3$ 14.7 $\pm 0.9$ 27.0 $\pm 1.4$<br>$12.1 \pm 1.7$ $16.8 \pm 1.2$ $27.2 \pm 1.5$ $13.5 \pm 1.2$ $23.3 \pm 1.0$ $23.3 \pm 1.1$                                                                                                                                                                                                                             | $4.2 \pm 0.3$<br>$8.4 \pm 0.3$<br>$8.3 \pm 0.3$<br>$4.5 \pm 0.3$ | $17.3 \pm 0.3$<br>$7.1 \pm 0.2$<br>$9.8 \pm 0.2$                              | $14.6 \pm 0.5$ 30.0 $\pm$ 0.4<br>$33.7 \pm 0.5$<br>$30.5 \pm 0.3$ |
| <b>Distillation</b>  | $DD^{\dagger}$ [52]<br>$LD^{\dagger}$ [4]<br>DC [62]<br>DSA [60]<br>DM [61]<br>CAFE $[51]$<br>$CAFE+DSA$ [51]<br><b>MTT</b> [6] |       | $31.2 \pm 1.4$ 76.1 $\pm$ 0.6 82.3 $\pm$ 0.3<br>$27.5 \pm 1.4$ $79.2 \pm 0.5$ $84.4 \pm 0.4$ | $42.9 \pm 3.0$ 77.9 $\pm$ 0.6 82.3 $\pm$ 0.4 |      | $36.8 \pm 1.2$ | $25.7 \pm 0.7$ 38.3 $\pm$ 0.4 42.5 $\pm$ 0.4 11.5 $\pm$ 0.4<br>$128.3 \pm 0.5$ 44.9 $\pm$ 0.5 53.9 $\pm$ 0.5 12.8 $\pm$ 0.3 25.2 $\pm$ 0.3<br>$ 28.8 \pm 0.7 \t52.1 \pm 0.5 \t60.6 \pm 0.5 $<br>$26.0 \pm 0.8$ 48.9 $\pm 0.6$ 63.0 $\pm 0.4$<br>$42.6\pm3.3$ $75.9\pm0.6$ $81.3\pm0.3$ $30.3\pm1.1$ $46.3\pm0.6$ $55.5\pm0.6$ $12.9\pm0.3$ $27.8\pm0.3$ $37.9\pm0.3$<br>$131.6 \pm 0.8$ 50.9 $\pm$ 0.5 62.3 $\pm$ 0.4 14.0 $\pm$ 0.3 31.5 $\pm$ 0.2 42.9 $\pm$ 0.2<br>$58.5 \pm 1.4$ $70.8 \pm 1.8$ $85.7 \pm 0.1$ $46.3 \pm 0.8$ $65.3 \pm 0.7$ $71.6 \pm 0.2$ $24.3 \pm 0.3$ $39.0 \pm 0.1$ $46.1 \pm 0.2$ |                                                                  | $13.9 \pm 0.3$ 32.3 $\pm$ 0.3 42.8 $\pm$ 0.4<br>$11.4 \pm 0.3$ 29.7 $\pm$ 0.3 | $43.6 + 0.4$                                                      |
| Factorization        | <b>BPC</b><br>Ratio %<br>HaBa                                                                                                   | 0.028 | 9<br>0.14<br>$69.8 \pm 1.3$ $83.2 \pm 0.4$                                                   | 49<br>0.7                                    | 0.04 | 9<br>0.2       | 49<br>$88.3 \pm 0.1$   48.3 $\pm$ 0.8 69.9 $\pm$ 0.4 74.0 $\pm$ 0.2   33.4 $\pm$ 0.4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         | 0.22                                                             | 9<br>1.82<br>$40.2 \pm 0.2$                                                   | 49<br>9.82<br>$47.0 \pm 0.2$                                      |
| <b>Whole Dataset</b> |                                                                                                                                 |       | $95.4 \pm 0.1$                                                                               |                                              |      | $84.8 \pm 0.1$ |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |                                                                  | $56.2 \pm 0.3$                                                                |                                                                   |

Table 1: The performance (test accuracy %) comparison to state-of-the-art methods.  $LD^{\dagger}$  and  $DD^{\dagger}$ use AlexNet for CIFAR10, while the rest use ConvNet for training and testing. IPC: Number of Images Per Class; BPC: Number of Bases Per Class; Ratio (%): the ratio of distilled images to whole training set. Underline denotes results by our implementation.

<span id="page-6-1"></span>Image /page/6/Figure/2 description: This image displays four grids of smaller images, labeled (a) Bases, (b) Images by H1, (c) Images by H2, and (d) Images by Baseline. Each grid contains numerous small, colorful, and somewhat abstract images, likely representing features or generated content from a machine learning model. Grid (a) appears to show foundational elements or bases, while grids (b), (c), and (d) show images generated by different methods, labeled H1, H2, and Baseline, respectively. The overall presentation suggests a comparison of image generation or feature representation techniques.

Figure 4: Visualization of factorized results by our HaBa (70.27% test acc.) and baseline MTT (65.92% test acc.). Zoom-in for better comparisons.

The comparison results are shown in Tab. [1](#page-6-0) and we can observe that HaBa achieves state-of-the-art performance in all datasets and settings. Especially when the ratio of distilled images to the whole training set is less than 1%, our method can yield significant improvement over all the candidate methods, which demonstrates that the scheme of hallucinator-basis factorization improves the data efficiency for the task of dataset distillation.

Qualitative Comparisons: We visualize the factorized results by our method as well as the baseline on CIFAR10 dataset with 10 BPC in Fig. [4.](#page-6-1) Due to the space limitation, we only provide images generated by 2 hallucinators here. More results can be found in the supplement. As shown in the figure, we can find that bases mainly store some main structures and contour information. Different hallucinators would render a basis with diverse styles and details. Thanks to the dataset factorization scheme, the diversity of distilled images by our method is higher than that by the baseline.

Building upon Different Baselines: To reflect the versatility of the insight, we implement HaBa on multiple state-of-the-art training pipelines of DD, including DC, DM, and MTT. We evaluate the performance of synthetic datasets on CIFAR10 and maintain the IPC of baseline methods as BPC plus 1, which makes storage costs for synthetic datasets as close as possible for fairness. As shown in Tab. [2,](#page-7-1) when training and testing on ConvNet, the strategy of HaBa can make a consistent improvement over all the baselines, which demonstrates that factorization is a general idea to improve the data efficiency in DD.

Cross-Architecture Performance: For DD, a satisfactory distilled dataset should have similar training effects to the original one on downstream models with arbitrary architectures. Thus, crossarchitecture generalization performance is an important metric for DD. We use the synthetic datasets trained on ConvNet to train models with different structures including ResNet [\[16\]](#page-10-19), VGG [\[45\]](#page-11-23), and AlexNet [\[26\]](#page-11-0). The results can be found in Tab. [2.](#page-7-1) Benefiting from the increased data diversity, HaBa can improve the across-architecture accuracy significantly with a performance gain up to 17.57%. The consistent and significant improvement validates the superior ability of our method to capture the informative features and thus original datasets can be replaced by the synthetic ones better.

<span id="page-7-1"></span>

|            | Method                   |                             | DC [62]  |          |                | DM [61]                                                                                                                                                                                                                                                                                                                           |          |                | MTT[6]   |          |
|------------|--------------------------|-----------------------------|----------|----------|----------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|----------------|----------|----------|
|            | <b>IPC</b><br><b>BPC</b> | $\mathcal{D}_{\mathcal{A}}$ | 11<br>10 | 51<br>50 | $\overline{c}$ | 11<br>10                                                                                                                                                                                                                                                                                                                          | 51<br>50 | $\overline{c}$ | 11<br>10 | 51<br>50 |
|            | Gain                     | $+2.75$                     | $+4.59$  | $+4.67$  | $+2.75$        | Baseline 31.36±0.16 45.29±0.30 54.24±0.61 34.57±0.52 50.35±0.36 62.03±0.29 50.59±0.95 63.90±0.29 69.81±0.48<br>ConvNet w. HaBa 34.11 $\pm$ 0.47 49.88 $\pm$ 0.52 58.91 $\pm$ 0.23 37.32 $\pm$ 0.13 56.83 $\pm$ 0.11 64.44 $\pm$ 0.40 56.76 $\pm$ 0.38 69.48 $\pm$ 0.26 73.25 $\pm$ 0.21<br>$+6.48$                                | $+2.41$  | $+6.17$        | $+5.58$  | $+3.44$  |
| ResNet     | Gain                     | $+6.39$                     | $+6.11$  | $+8.94$  | $+9.09$        | $ $ Baseline $ 18.10 \pm 0.76 18.36 \pm 0.36 22.14 \pm 0.38 22.25 \pm 1.00 40.00 \pm 1.49 53.40 \pm 0.68 35.15 \pm 0.96 45.05 \pm 1.46 54.47 \pm 0.95 $<br>w. HaBa 24.49 ± 0.55 24.27 ± 0.56 31.08 ± 0.32 31.34 ± 0.72 47.57 ± 0.49 59.61 ± 0.35 47.39 ± 0.71 57.97 ± 0.88 64.35 ± 0.60<br>$+7.57$                                | $+6.21$  | $+12.24$       | $+12.92$ | $+9.88$  |
| <b>VGG</b> | Gain                     | $+1.40$                     | $+1.15$  | $+3.18$  | $+4.65$        | $ $ Baseline $ 28.02 \pm 0.2635.88 \pm 0.6738.73 \pm 0.48 22.28 \pm 1.0341.64 \pm 0.6455.17 \pm 0.54 38.04 \pm 1.1950.49 \pm 1.0261.36 \pm 0.305$<br>w. HaBa $29.42 \pm 0.93$ 37.03 $\pm$ 0.42 41.91 $\pm$ 0.55 $26.93 \pm 0.62$ 49.41 $\pm$ 0.36 67.47 $\pm$ 0.43 $ 48.26 \pm 0.54$ 60.47 $\pm$ 0.56 67.47 $\pm$ 0.43<br>$+7.77$ | $+12.30$ | $+10.22$       | $+9.98$  | $+6.11$  |
|            | Gain                     | $+2.22$                     | $+10.60$ | $+3.94$  | $+11.47$       | Baseline  20.02±1.31 22.42±1.35 29.48±0.87  20.67±3.64 37.04±0.92 49.14±0.94  26.06±1.01 35.95±1.52 49.20±1.27<br>AlexNet  w. HaBa 22.24 ± 1.14 33.02 ± 0.91 33.42 ± 1.39 32.14 ± 0.60 44.14 ± 0.67 53.09 ± 0.89 43.63 ± 1.46 48.96 ± 3.00 60.07 ± 1.37<br>$+7.10$                                                                | $+3.95$  | $+17.57$       | $+13.01$ | $+10.87$ |

Table 2: Cross-architecture performance (test accuracy %) comparison to different baseline methods of DD HaBa built upon.

Comparisons under the Same Number of Final Images: In the default comparison protocol, we compare our method with the baselines using the same budget of storage, where our method can store information of exponentially more images than the baselines with the same number of parameters. In this part, we also examine the performance of HaBa under the condition that the number of final images, *i.e.*,  $|\mathcal{H}| \times |\mathcal{B}|$ , is equal to that used by the baseline. Intuitively, given that the objective functions of our method and the baseline are the same exactly, the performance of the baseline can be viewed as an upper bound of ours, since there are significantly less parameters in our method to carry the information of final images in this case. Therefore, we first remove the term  $\mathcal{L}_{cos}$ , from the loss function of DD in Eq. [6](#page-4-3) to guarantee a consistent optimization objective with the baseline. Then, we compare the performance of HaBa and the baseline using 10, 20, 30, 40, and 50 final images respectively. Here, the number of hallucinators  $|\mathcal{H}|$  is 2 and the number of bases is thus half of the number of final images. As shown in the red and green curves in Fig. [7,](#page-9-0) performance of the baseline can be well approximated by ours with only half of the number of parameters, especially when the number of images is relatively large. Remarkably, with the proposed adversary contrastive constraint, our method can even outperform the baseline consistently, as shown in the blue curve, which further demonstrates the effectiveness of the proposed solution.

Comparisons with Concurrent Works on Efficient Distilled Dataset Parameterization: As a concurrent work on efficient distilled dataset parameterization, IDC [\[24\]](#page-11-10) is proposed to store  $4\times$ number of  $2\times$  down-sample synthetic images compared with the baseline. The core is to reduce the spatial size for efficient parameterization. For HaBa of this paper, instead, we do not modify the spatial size of bases in the default setting for better qualitative explainablity and more intuitive comparisons with the baselines. In this sense, IDC and HaBa are in fact two orthogonal techniques and they can readily join force to enhance the baseline performance. Here, we try using the technique of IDC and adopting  $2\times$  down-sample synthetic images on the baseline MTT, based on which we further consider adding our HaBa and involving 5 hallucinators. As shown in Tab. [3,](#page-8-0) with the efficient parameterization of IDC, the performance of baseline can be improved. With HaBa in this paper, the performance can even be further improved a lot: 5.14%, 1.29%, and 4.30% in the three settings respectively, which demonstrates that IDC and HaBa work in different ways.

Applications in Continual Learning: To further demonstrate the advantage of the proposed method for improving data efficiency, following the setting of DM  $[61]$ , we conduct experiments on the setting of continual learning on CIFAR-100, with 20 random classes per stage. The average number of parameters per class is  $20 \times 32 \times 32 \times 3$ . The synthetic datasets are trained with a ConvNet with 3 blocks. We evaluate synthetic datasets by our method and the DM baseline on the same ConvNet architecture and ResNet18. The results in Fig. [5](#page-8-0) demonstrate that the proposed method increases the informativeness of synthetic datasets and thus produce significantly better performance, especially in the cross-architecture setting.

<span id="page-7-0"></span>

### 4.3 Ablation Studies

Loss Terms: To validate the effectiveness of the proposed adversarial contrastive constraints, we design ablation studies on the CIFAR10 dataset over three loss terms:  $\mathcal{L}_{cos}$ , in Eq. [5,](#page-4-2)  $\mathcal{L}_{con}$ , in Eq. [3,](#page-4-1) and the task-specific loss  $\mathcal{L}_{task}$ . Through the results in Tab. [4,](#page-8-1) we can find that deleting any one of

<span id="page-8-0"></span>

| # of Param. / Class $2\times32\times32\times3$ $11\times32\times32\times3$ $51\times32\times32\times3$ |                |                |                  |
|--------------------------------------------------------------------------------------------------------|----------------|----------------|------------------|
| <b>Baseline</b>                                                                                        | $49.89 + 0.95$ | $65.92 + 0.62$ | $70.73 + 0.52$   |
| w IDC                                                                                                  | $56.13 + 0.38$ | $70.85 + 0.43$ | $71.01 \pm 0.41$ |
| w. IDC & HaBa                                                                                          | $61.27 + 0.34$ | $72.14 + 0.22$ | $75.31 + 0.27$   |

Table 3: Comparisons with concurrent work IDC [\[24\]](#page-11-10) on efficient synthetic parameterization.

<span id="page-8-1"></span>

| BPC                                     | 1                | 10               | 50               |
|-----------------------------------------|------------------|------------------|------------------|
| HaBa w/o $\mathcal{L}_{cos}$ .          | $54.56 \pm 0.61$ | $70.16 \pm 0.44$ | $73.93 \pm 0.21$ |
| HaBa w/o $\mathcal{L}_{con}$ .          | $54.91 \pm 0.49$ | $70.07 \pm 0.48$ | $72.50 \pm 0.39$ |
| HaBa w/o $\mathcal{L}_{task}$           | $54.62 \pm 0.42$ | $70.07 \pm 0.16$ | $72.74 \pm 0.20$ |
| HaBa Full                               | $55.66 \pm 0.29$ | $70.27 \pm 0.63$ | $74.04 \pm 0.16$ |
| HaBa w $\mathcal{L}_{con}$ . Downstream | $56.78 \pm 0.22$ | $70.44 \pm 0.15$ | $75.00 \pm 0.52$ |

Image /page/8/Figure/3 description: The image contains two line graphs side-by-side. Both graphs plot accuracy (%) against the number of classes on the x-axis, ranging from 20 to 100. The left graph is titled "ConvNet3 Accuracy (%)" and shows two lines: a red line labeled "Baseline" and a blue line labeled "w. HaBa". The red line starts at approximately 56% accuracy at 20 classes and decreases to about 40% at 100 classes. The blue line starts at approximately 64% accuracy at 20 classes and decreases to about 37% at 100 classes. The right graph is titled "ResNet18 Accuracy (%)" and also shows two lines: a red line labeled "Baseline" and a blue line labeled "w. HaBa". The red line starts at approximately 49% accuracy at 20 classes and decreases to about 29% at 100 classes. The blue line starts at approximately 59% accuracy at 20 classes and decreases to about 37% at 100 classes. Both graphs show that the "w. HaBa" model consistently achieves higher accuracy than the "Baseline" model across different numbers of classes.

Figure 5: Comparisons on the setting of continual setting. Results on the ConvNet3 (Left) and ResNet18 (Right) architectures are shown.

Image /page/8/Figure/5 description: The image displays two bar charts side-by-side, both titled "Test Accuracy (%)" on the y-axis. The x-axis of the left chart is labeled with values 0, 0.01, 0.1, 0.25, 0.5, and 1, followed by "λcos.". The x-axis of the right chart is labeled with values 0, 0.01, 0.1, 0.5, 1, and 2, followed by "λcon.". Both charts show blue bars representing "Test Acc. of HaBa" with specific accuracy values labeled above each bar: 70.2, 70.2, 70.3, 70.2, 69.8, and 69.6 for the left chart, and 70.2, 70.2, 70.3, 70.2, 69.8, and 69.6 for the right chart. A dashed orange line labeled "Baseline" is present at approximately 66% accuracy across both charts.

Table 4: Results of ablation study on loss terms Figure 6: Impacts of different  $\lambda_{cos}$  and  $\lambda_{con}$  on in HaBa:  $\mathcal{L}_{cos}$ ,  $\mathcal{L}_{con}$ , and  $\mathcal{L}_{task}$ .

the test accuracy.

them would hurt the performance. We also experiment with involving  $\mathcal{L}_{con.}$  for downstream training, to enforce the similarity among images composed of different hallucinators and a common basis. Observed from the last row of Tab. [4,](#page-8-1) the performance can be further improved, since  $\mathcal{L}_{con.}$  helps the representation learning of related samples [\[23\]](#page-10-15). Note that we do not use this loss term for downstream training in other experiments for fair and standard comparison.

We examine the sensitivities of hyper-parameters  $\lambda_{cos.}$  and  $\lambda_{con.}$  used to balance the weights of loss terms  $\mathcal{L}_{cos}$  and  $\mathcal{L}_{con}$  respectively in Fig. [6.](#page-8-1) The results are evaluated on the CIFAR10 dataset with 10 BPC. We can observe that the overall performance is not sensitive to the selection of these hyper-parameters and our method makes a consistent improvement over the baseline with 11 IPC.

Class-Independent Hallucinators v.s. Shared Hallucinators: In the default setting of HaBa, each class maintains a certain number of bases independently and all the classes share the same set of hallucinators. But what about the case that hallucinators are also made class-independent? We study this problem experimentally in Tab. [5.](#page-9-1) Given the same BPC, class-independent hallucinators can indeed somehow improve the performance when there are fewer synthetic samples, *e.g.*, 1 BPC. However, when BPC is higher, equipping each class with an independent set of hallucinators would not benefit the performance. There are probably two reasons: (1) shared hallucinators across all the classes extract global information of the whole dataset, which encodes more representative and universal knowledge; and (2) the class-independent case would make the number of hallucinators 10 times for the CIFAR10 dataset, which leaves a heavy burden for the optimization process. Thus, as indicated in Tab. [5,](#page-9-1) a better solution is to make room for more bases using the memory allocated to store class-independent hallucinators initially, which would result in more satisfactory data efficiency.

Number of Channels Used by Basis: By default, the shape of a basis is the same as that of a real image, which is generally in RGB 3-channel format. In fact, in Fig. [8\(](#page-9-0)Left), we also verify that it is also possible to use single-channel basis, which can reduce the memory cost by nearly 2/3 without hurting the performance too much. Interestingly, if the memory cost is held the same, we can choose to use 3 times BPC to store single-channel bases, rather than 3-channel ones. This would yield impressive improvement on the test accuracy when BPC is small. Note that for baseline results, IPC is set as the corresponding BPC plus 1.

Number of Hallucinators: We study the impact of the number of hallucinators, *i.e.*,  $|\mathcal{H}|$ , in Fig. [8\(](#page-9-0)Right). We can observe that when BPC is small, including more hallucinators is helpful for the performance. Nevertheless, when BPC is 10 or 50, the performance would not improve with more hallucinators when  $|\mathcal{H}| > 10$ . One reason is that when  $|\mathcal{H}|$  is large, the sampling of hallucinators in each iteration is sparse, which makes the joint optimization of all the hallucinators more difficult.

Data Augmentation: The similarity between our hallucinator set and data augmentation lies that both of them can contribute to generating more samples and increasing the diversity. However, the essential difference is that our hallucinators are optimized to encode sample-wise relationships in

<span id="page-9-0"></span>Image /page/9/Figure/0 description: A line graph shows the test accuracy (%) on the y-axis against the number of images on the x-axis. The x-axis ranges from 10 to 50, with increments of 10. The y-axis ranges from 64 to 72, with increments of 2. Three lines are plotted: 'Baseline' (red), 'Ours w/o Lcon.' (green), and 'Ours' (blue). All three lines show an increasing trend in test accuracy as the number of images increases. The 'Ours' line consistently shows the highest test accuracy, followed by 'Baseline', and then 'Ours w/o Lcon.' The shaded areas around each line represent a confidence interval. At 10 images, the accuracies are approximately 65% for 'Ours', 64.5% for 'Baseline', and 64% for 'Ours w/o Lcon.'. At 50 images, the accuracies are approximately 72% for 'Ours', 71.5% for 'Baseline', and 71% for 'Ours w/o Lcon.'.

Figure 7: Comparisons with the baseline under the same number of final images.

<span id="page-9-1"></span>

| <b>BPC</b>                                                                                       | 10                                                                                                                                                 | 50 |
|--------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------|----|
| w/o Share<br>Share<br>Baseline (IPC=BPC)                                                         | $55.96 \pm 0.51$ 69.00 $\pm$ 0.20 69.81 $\pm$ 0.56<br>$55.66 \pm 0.29$ $70.27 \pm 0.63$ $74.04 \pm 0.16$<br>45.29 ± 0.86 62.77 ± 0.56 71.09 ± 0.34 |    |
| Share (Same Memory)<br>Baseline (Same Memory) $65.92 \pm 0.62$ 68.58 $\pm$ 0.49 73.55 $\pm$ 0.48 | 70.27±0.63 72.17±0.30 74.89±0.15                                                                                                                   |    |

Image /page/9/Figure/3 description: The image contains two plots. The left plot is a bar chart showing test accuracy (%) for different BPC values (1, 10, 50). The bars represent '1 Channel' (blue), '3 Channel' (green), '1 Channel (3x BPC)' (yellow), and 'Baseline' (red). For BPC=1, accuracies are approximately 48%, 48%, 56%, 50%. For BPC=10, accuracies are approximately 69%, 70%, 71%, 66%. For BPC=50, accuracies are approximately 72%, 74%, 73%, 71%. The right plot is a line graph showing test accuracy (%) against |H| values (2, 5, 10, 20). It displays three lines representing different BPC values: BPC=1 (red), BPC=10 (green), and BPC=50 (blue). For BPC=1, accuracies range from about 54% to 57%. For BPC=10, accuracies range from about 69% to 71%. For BPC=50, accuracies range from about 73% to 75%.

Figure 8: Study on the number of channels used by bases and the number of hallucinators.

|          | ConvNet             | ResNet              | VGG                 | AlexNet             |
|----------|---------------------|---------------------|---------------------|---------------------|
| w/o aug. | 60.63 $	extpm$ 0.21 | 43.24 $	extpm$ 0.83 | 48.02 $	extpm$ 0.53 | 30.58 $	extpm$ 1.44 |
| Baseline | 63.90 $	extpm$ 0.29 | 45.05 $	extpm$ 1.46 | 50.49 $	extpm$ 1.02 | 35.95 $	extpm$ 1.52 |
| w/o aug. | 68.08 $	extpm$ 0.23 | 56.37 $	extpm$ 0.11 | 59.04 $	extpm$ 0.50 | 48.27 $	extpm$ 3.04 |
| Ours     | 69.48 $	extpm$ 0.26 | 57.97 $	extpm$ 0.88 | 60.47 $	extpm$ 0.56 | 48.96 $	extpm$ 3.00 |

Table 5: Study on whether all the classes should share the same set of hallucinators.

Table 6: Impact of data augmentation.

a dataset, while data augmentation is based on some prior and heuristic knowledge of images. By default, both our method and the baseline adopt the data augmentation strategy DSA [\[60\]](#page-12-5). To study the relationship between the two schemes experimentally, we attempt to remove DSA from baseline and our method and report the corresponding results in Tab. [6.](#page-9-1) The evaluation is on CIFAR10 with 11 IPC for baseline and 10 BPC for ours. Through the results, we can find that (1) our method without data augmentation can also outperform the baseline method with augmentation significantly, which means that the mechanism of HaBa can benefit the dataset distillation task more with the learning of global information of a dataset in hallucinators; and (2) with data augmentation, our performance can be further improved, which indicates that HaBa and DSA work in different manners.

## 5 Conclusions, Limitations, and Future Works

This paper proposes a novel hallucinator-basis factorization method dubbed HaBa for dataset distillation (DD). It uses hallucinators to encode inner relations between different samples in original datasets, which can largely improve the data efficiency of distilled results. To diversify the knowledge captured by different hallucinators, a pair of adversarial contrastive constraints is further introduced. Extensive evaluations and comparisons on multiple benchmark datasets demonstrate that HaBa is capable of significantly improving the performance of downstream models trained on the synthetic dataset, using only 35% cost of memory for storage. Moreover, it is a versatile strategy that is compatible with different configurations of DD frameworks and yields consistent improvement.

Despite the superior performance of the proposed hallucinator-basis factorization (HaBa) scheme, there are also some potential limitations. On the one hand, compared with the baseline method HaBa built upon, the process of online pairwise combination between hallucinators and bases in training increases the cost of time and GPU memory slightly, although light-weight hallucinators are adopted. On the other hand, it may inherited the limitations of baseline methods. For example, when the number of images is large, further increasing the number would produce limited performance gain.

For future works, beyond the training efficiency of HaBa, introducing class-wise relationship may also be a potential research direction. For example, it is probably optimal that one class shares hallucinators with some specific classes but does not share with others. It is also promising to explore more advance factorization for a dataset to further improve the performance.

## Acknowledgement

This research is supported by the National Research Foundation, Singapore under its Medium Sized Centre for Advanced Robotics Technology Innovation (WBS: A-0009428-09-00). Xinchao Wang is the corresponding author.

## References

- <span id="page-10-4"></span>[1] Olivier Bachem, Mario Lucic, and Andreas Krause. Practical coreset constructions for machine learning. *arXiv preprint arXiv:1703.06476*, 2017.
- <span id="page-10-17"></span>[2] Eden Belouadah and Adrian Popescu. Scail: Classifier weights scaling for class incremental learning. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 1266–1275, 2020.
- <span id="page-10-22"></span>[3] Shai Ben-David, John Blitzer, Koby Crammer, Alex Kulesza, Fernando Pereira, and Jennifer Wortman Vaughan. A theory of learning from different domains. *Machine learning*, 79(1):151–175, 2010.
- <span id="page-10-10"></span>[4] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020.
- <span id="page-10-16"></span>[5] Francisco M Castro, Manuel J Marín-Jiménez, Nicolás Guil, Cordelia Schmid, and Karteek Alahari. End-to-end incremental learning. In *Proceedings of the European conference on computer vision (ECCV)*, pages 233–248, 2018.
- <span id="page-10-3"></span>[6] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. *arXiv preprint arXiv:2203.11932*, 2022.
- <span id="page-10-2"></span>[7] Can Chen, Yingxue Zhang, Jie Fu, Xue Liu, and Mark Coates. Bidirectional learning for offline infinitewidth model-based optimization. In *Thirty-Sixth Conference on Neural Information Processing Systems*, 2022.
- <span id="page-10-5"></span>[8] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. *arXiv preprint arXiv:1203.3472*, 2012.
- <span id="page-10-20"></span>[9] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009.
- <span id="page-10-1"></span>[10] Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? *arXiv preprint arXiv:2206.00240*, 2022.
- <span id="page-10-0"></span>[11] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020.
- <span id="page-10-18"></span>[12] Reza Zanjirani Farahani and Masoud Hekmatfar. *Facility location: concepts, models, algorithms and case studies*. Springer Science & Business Media, 2009.
- <span id="page-10-9"></span>[13] Chelsea Finn, Pieter Abbeel, and Sergey Levine. Model-agnostic meta-learning for fast adaptation of deep networks. In *International conference on machine learning*, pages 1126–1135. PMLR, 2017.
- <span id="page-10-8"></span>[14] Jianping Gou, Baosheng Yu, Stephen J Maybank, and Dacheng Tao. Knowledge distillation: A survey. *International Journal of Computer Vision*, 129(6):1789–1819, 2021.
- <span id="page-10-6"></span>[15] Sariel Har-Peled and Akash Kushal. Smaller coresets for k-median and k-means clustering. *Discrete & Computational Geometry*, 37(1):3–19, 2007.
- <span id="page-10-19"></span>[16] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016.
- <span id="page-10-21"></span>[17] Dan Hendrycks and Thomas Dietterich. Benchmarking neural network robustness to common corruptions and perturbations. *Proceedings of the International Conference on Learning Representations*, 2019.
- <span id="page-10-7"></span>[18] Geoffrey Hinton, Oriol Vinyals, Jeff Dean, et al. Distilling the knowledge in a neural network. *arXiv preprint arXiv:1503.02531*, 2(7), 2015.
- <span id="page-10-13"></span>[19] Xun Huang and Serge Belongie. Arbitrary style transfer in real-time with adaptive instance normalization. In *Proceedings of the IEEE international conference on computer vision*, pages 1501–1510, 2017.
- <span id="page-10-11"></span>[20] Arthur Jacot, Franck Gabriel, and Clément Hongler. Neural tangent kernel: Convergence and generalization in neural networks. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-10-14"></span>[21] Yongcheng Jing, Xiao Liu, Yukang Ding, Xinchao Wang, Errui Ding, Mingli Song, and Shilei Wen. Dynamic instance normalization for arbitrary style transfer. In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 34, pages 4369–4376, 2020.
- <span id="page-10-12"></span>[22] Yongcheng Jing, Yang Liu, Yezhou Yang, Zunlei Feng, Yizhou Yu, Dacheng Tao, and Mingli Song. Stroke controllable fast style transfer with adaptive receptive fields. In *Proceedings of the European Conference on Computer Vision (ECCV)*, September 2018.
- <span id="page-10-15"></span>[23] Prannay Khosla, Piotr Teterwak, Chen Wang, Aaron Sarna, Yonglong Tian, Phillip Isola, Aaron Maschinot, Ce Liu, and Dilip Krishnan. Supervised contrastive learning. *Advances in Neural Information Processing Systems*, 33:18661–18673, 2020.

- <span id="page-11-10"></span>[24] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. *arXiv preprint arXiv:2205.14959*, 2022.
- <span id="page-11-19"></span>[25] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-11-0"></span>[26] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Advances in neural information processing systems*, 25, 2012.
- <span id="page-11-24"></span>[27] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998.
- <span id="page-11-14"></span>[28] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. *arXiv preprint arXiv:2202.02916*, 2022.
- <span id="page-11-5"></span>[29] Huihui Liu, Yiding Yang, and Xinchao Wang. Overcoming catastrophic forgetting in graph neural networks. In *AAAI Conference on Artificial Intelligence*, 2021.
- <span id="page-11-17"></span>[30] Songhua Liu, Jingwen Ye, Sucheng Ren, and Xinchao Wang. Dynast: Dynamic sparse transformer for exemplar-guided image generation. In *Proceedings of the European Conference on Computer Vision*, 2022.
- <span id="page-11-9"></span>[31] Wojciech Masarczyk and Ivona Tautkute. Reducing catastrophic forgetting with learning on synthetic data. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), Workshop*, 2020.
- <span id="page-11-18"></span>[32] Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Bo Wu, and Andrew Y Ng. Reading digits in natural images with unsupervised feature learning. 2011.
- <span id="page-11-15"></span>[33] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020.
- <span id="page-11-16"></span>[34] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34, 2021.
- <span id="page-11-21"></span>[35] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H Lampert. icarl: Incremental classifier and representation learning. In *Proceedings of the IEEE conference on Computer Vision and Pattern Recognition*, pages 2001–2010, 2017.
- <span id="page-11-3"></span>[36] Joseph Redmon and Ali Farhadi. Yolov3: An incremental improvement. *arXiv preprint arXiv:1804.02767*, 2018.
- <span id="page-11-2"></span>[37] Shaoqing Ren, Kaiming He, Ross Girshick, and Jian Sun. Faster r-cnn: Towards real-time object detection with region proposal networks. *Advances in neural information processing systems*, 28, 2015.
- <span id="page-11-1"></span>[38] Sucheng Ren, Daquan Zhou, Shengfeng He, Jiashi Feng, and Xinchao Wang. Shunted self-attention via multi-scale token aggregation. In *IEEE Conference on Computer Vision and Pattern Recognition*, 2022.
- <span id="page-11-20"></span>[39] Edgar Riba, Dmytro Mishkin, Daniel Ponsa, Ethan Rublee, and Gary Bradski. Kornia: an open source differentiable computer vision library for pytorch. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 3674–3683, 2020.
- <span id="page-11-8"></span>[40] Andrea Rosasco, Antonio Carta, Andrea Cossu, Vincenzo Lomonaco, and Davide Bacciu. Distilled replay: Overcoming forgetting through synthetic samples. *arXiv preprint arXiv:2103.15851*, 2021.
- <span id="page-11-7"></span>[41] Mattia Sangermano, Antonio Carta, Andrea Cossu, and Davide Bacciu. Sample condensation in online continual learning. In *Proceedings of the International Joint Conference on Neural Networks (IJCNN)*, pages 1–8, 2022.
- <span id="page-11-11"></span>[42] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. *arXiv preprint arXiv:1708.00489*, 2017.
- <span id="page-11-4"></span>[43] Evan Shelhamer, Jonathan Long, and Trevor Darrell. Fully convolutional networks for semantic segmentation. *IEEE transactions on pattern analysis and machine intelligence*, 39(4):640–651, 2017.
- <span id="page-11-6"></span>[44] Reza Shokri and Vitaly Shmatikov. Privacy-preserving deep learning. In *Proceedings of the 22nd ACM SIGSAC conference on computer and communications security*, pages 1310–1321, 2015.
- <span id="page-11-23"></span>[45] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014.
- <span id="page-11-13"></span>[46] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *2021 International Joint Conference on Neural Networks (IJCNN)*, pages 1–8. IEEE, 2021.
- <span id="page-11-22"></span>[47] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018.
- <span id="page-11-12"></span>[48] Ivor W Tsang, James T Kwok, Pak-Ming Cheung, and Nello Cristianini. Core vector machines: Fast svm training on very large data sets. *Journal of Machine Learning Research*, 6(4), 2005.

- <span id="page-12-11"></span>[49] Aaron Van den Oord, Yazhe Li, and Oriol Vinyals. Representation learning with contrastive predictive coding. *arXiv e-prints*, pages arXiv–1807, 2018.
- <span id="page-12-14"></span>[50] Laurens Van der Maaten and Geoffrey Hinton. Visualizing data using t-sne. *Journal of machine learning research*, 9(11), 2008.
- <span id="page-12-7"></span>[51] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. *arXiv preprint arXiv:2203.01531*, 2022.
- <span id="page-12-2"></span>[52] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-12-13"></span>[53] Pete Warden. Speech commands: A dataset for limited-vocabulary speech recognition. *arXiv preprint arXiv:1804.03209*, 2018.
- <span id="page-12-3"></span>[54] Felix Wiewel and Bin Yang. Condensed composite memory continual learning. In *Proceedings of the International Joint Conference on Neural Networks (IJCNN)*, pages 1–8, 2021.
- <span id="page-12-12"></span>[55] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-12-0"></span>[56] Enze Xie, Wenhai Wang, Zhiding Yu, Anima Anandkumar, Jose M Alvarez, and Ping Luo. Segformer: Simple and efficient design for semantic segmentation with transformers. *arXiv preprint arXiv:2105.15203*, 2021.
- <span id="page-12-1"></span>[57] Xingyi Yang, Jingwen Ye, and Xinchao Wang. Factorizing knowledge in neural networks. In *Proceedings of the European Conference on Computer Vision*, 2022.
- <span id="page-12-10"></span>[58] Yiding Yang, Zunlei Feng, Mingli Song, and Xinchao Wang. Factorizable graph convolutional networks. In *Conference on Neural Information Processing Systems*, 2020.
- <span id="page-12-9"></span>[59] Yiding Yang, Jiayan Qiu, Mingli Song, Dacheng Tao, and Xinchao Wang. Distilling knowledge from graph convolutional networks. In *IEEE Conference on Computer Vision and Pattern Recognition*, 2020.
- <span id="page-12-5"></span>[60] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021.
- <span id="page-12-6"></span>[61] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021.
- <span id="page-12-4"></span>[62] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.
- <span id="page-12-8"></span>[63] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *arXiv preprint arXiv:2206.00719*, 2022.

# Checklist

- 1. For all authors...
  - (a) Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope? [Yes]
  - (b) Did you describe the limitations of your work? [Yes] Please refer to the supplement.
  - (c) Did you discuss any potential negative societal impacts of your work? [N/A]
  - (d) Have you read the ethics review guidelines and ensured that your paper conforms to them? [Yes]
- 2. If you are including theoretical results...
  - (a) Did you state the full set of assumptions of all theoretical results? [N/A]
  - (b) Did you include complete proofs of all theoretical results? [N/A]
- 3. If you ran experiments...
  - (a) Did you include the code, data, and instructions needed to reproduce the main experimental results (either in the supplemental material or as a URL)? [Yes] Please refer to the supplement.
  - (b) Did you specify all the training details (e.g., data splits, hyperparameters, how they were chosen)? [Yes] Please refer to Sec. [4.1.](#page-5-0)
  - (c) Did you report error bars (e.g., with respect to the random seed after running experiments multiple times)? [Yes] We run all the experiments 5 times and report the mean and standard deviation of the performance.
  - (d) Did you include the total amount of compute and the type of resources used (e.g., type of GPUs, internal cluster, or cloud provider)? [Yes] Please refer to Sec. [4.1.](#page-5-0)
- 4. If you are using existing assets (e.g., code, data, models) or curating/releasing new assets...
  - (a) If your work uses existing assets, did you cite the creators? [Yes]
  - (b) Did you mention the license of the assets? [Yes]
  - (c) Did you include any new assets either in the supplemental material or as a URL? [Yes]
  - (d) Did you discuss whether and how consent was obtained from people whose data you're using/curating? [N/A]
  - (e) Did you discuss whether the data you are using/curating contains personally identifiable information or offensive content? [N/A]
- 5. If you used crowdsourcing or conducted research with human subjects...
  - (a) Did you include the full text of instructions given to participants and screenshots, if applicable? [N/A]
  - (b) Did you describe any potential participant risks, with links to Institutional Review Board (IRB) approvals, if applicable? [N/A]
  - (c) Did you include the estimated hourly wage paid to participants and the total amount spent on participant compensation? [N/A]

# Appendices

In this part, we provide additional details, more results, potential limitations, and future directions of the proposed Hallucinator-Basis factorization (HaBa) for dataset disllation (DD). First, we provide more details on the pipeline of HaBa. Then, we conduct more experiments to demonstrate and analyze performance of our method, including results on more benchmarks with larger resolutions, as supplement to the quantitative study in the main paper. We also provide more qualitative results by HaBa and additional ablation studies. Finally, we discuss some limitations and future works of our method.

<span id="page-14-0"></span>**Input:** T: original dataset;  $|\mathcal{H}|$ : total number of hallucinators;  $|\mathcal{B}|$ : total number of bases;  $\eta_H$ : learning rate of hallucinators;  $\eta_B$ : learning rate of bases;  $\eta_F$ : learning rate of feature extractor.

**Output:**  $H:$  a set of hallucinators;  $B:$  a set of bases;

- 1: Randomly initialize hallucinators  $\mathcal{H} = \{H_{\theta_j}\}_{j=1}^{|\mathcal{H}|}$ , bases  $\mathcal{B} = \{(\hat{x}_i, \hat{y}_i)\}_{i=1}^{|\mathcal{B}|}$ , and parameters  $\psi$ of the feature extractor  $F$ ;
- 2: while not done do 3:  $\mathcal{H}' \leftarrow$  a random batch of hallucinators from  $\mathcal{H}$ ; 4:  $B' \leftarrow$  a random batch of bases from B; 5: for each  $1 \leq i \leq |\mathcal{B}'|$  do 6: **for** each  $1 \leq j \leq |\mathcal{H}'|$  **do** 7:  $\tilde{x}_{ij} = H_{\theta_j}(\hat{x}_i);$ 8: end for 9: end for 10: Compute  $\mathcal{L}_{\mathcal{S}}$  using Eq. 6 of the main paper; 11: Update  $\mathcal{H}$ :  $\theta_i \leftarrow \tilde{\theta}_i - \eta_H \nabla_{\theta_i} \mathcal{L}_{\mathcal{S}}$  for  $1 \leq j \leq |\mathcal{H}'|$ ; 12: Update B:  $\hat{x}_i \leftarrow \hat{x}_i - \eta_B \nabla_{\hat{x}_i} \mathcal{L}_{\mathcal{S}}$  and  $\hat{y}_i \leftarrow \hat{y}_i - \eta_B \nabla_{\hat{y}_i} \mathcal{L}_{\mathcal{S}}$  (optional) for  $1 \le i \le |\mathcal{B}'|$ ; 13: Compute  $\mathcal{L}_F$  using Eq. 4 of the main paper;<br>14: Update  $F: \psi \leftarrow \psi - n_F \nabla_{\psi} \mathcal{L}_F$ : Update  $F: \psi \leftarrow \psi - \eta_F \nabla_{\psi} \mathcal{L}_F;$ 15: end while

## Appendix A Algorithm Details

To better elaborate the details of the proposed HaBa for DD, we provide an algorithmic illustration for the whole pipeline in Alg. [1,](#page-14-0) as a supplement to Sec. 3 of the main paper. The overall algorithm takes an original dataset as well as some hyper-parameters shown in Alg. [1](#page-14-0) as input. The output is the distilled result including a set of hallucinators  $H$  and a set of bases  $B$ , as defined in Eq. 1 of the main paper. The goal is to equip the distilled dataset with similar downstream performance to the original one.

## Appendix B More Results

Low-Resolution Data: We provide results on the more-common benchmark datasets in DD in Tab. [7:](#page-15-0) MNIST [\[27\]](#page-11-24) and FashionMNIST [\[55\]](#page-12-12). Both datasets contain 60,000 images for training and 10,000 images for testing in 10 classes. The images are under  $28 \times 28$  resolution with 1 channel. We build our HaBa on MTT [\[6\]](#page-10-3) in this part. Although the performances of DD on these two dataset seem to be saturated, our method may still yield consistent improvement over the baseline, especially when the ratio of distilled images to the whole training set is small.

ImageNet Subsets: We also evaluate the proposed scheme on the more-challenging settings of ImageNet [\[9\]](#page-10-20) subsets. We follow the baseline MTT [\[6\]](#page-10-3) for the divisions of subsets. The 6 subsets include ImageFruit, ImageMeow, ImageNette, ImageSquawk, ImageWoof, and ImageYellow. Each subset contains over 10,000 images, and we resize all the images to  $128 \times 128$  resolution following the original setting. We use ConvNet with 5 Conv-InstanceNorm-ReLU-AvgPool layers for training. For testing, in addition to the same structure of ConvNet, we also evaluate the results under 3 other

<span id="page-15-0"></span>

|                      | Dataset                                                                                             |                                                                                                                            | <b>MNIST</b>                                                                                                                               |                                                                                                                                 |                                                                                      | FashionMNIST                                                                           |                                                                                        |
|----------------------|-----------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------|
|                      | <b>IPC</b><br>Ratio %                                                                               | 1<br>0.017                                                                                                                 | 10<br>0.17                                                                                                                                 | 50<br>0.83                                                                                                                      | 1<br>0.017                                                                           | 10<br>0.17                                                                             | 50<br>0.83                                                                             |
| Coreset              | Random<br>Herding<br>K-Center<br>Forgetting                                                         | $64.9 \pm 3.5$<br>$89.2 \pm 1.6$<br>$89.3 \pm 1.5$<br>$35.5 \pm 5.6$                                                       | $95.1 \pm 0.9$<br>$93.7 \pm 0.3$<br>$84.4 \pm 1.7$<br>$68.1 \pm 3.3$                                                                       | $97.9 \pm 0.2$<br>$94.8 \pm 0.2$<br>$97.4 \pm 0.3$<br>$88.2 \pm 1.2$                                                            | $51.4 \pm 3.8$<br>$67.0 \pm 1.9$<br>$66.9 \pm 1.8$<br>$42.0 \pm 5.5$                 | $73.8 \pm 0.7$<br>$71.1 \pm 0.7$<br>$54.7 \pm 1.5$<br>$53.9 \pm 2.0$                   | $82.5 \pm 0.7$<br>$71.9 \pm 0.8$<br>$68.3 \pm 0.8$<br>$55.0 \pm 1.1$                   |
| <b>Distillation</b>  | DD [52]<br>LD[4]<br>DC [62]<br><b>DSA</b> [60]<br>DM [61]<br>CAFE $[51]$<br>CAFE+DSA [51]<br>MTT[6] | $60.9 \pm 3.2$<br>$91.7 \pm 0.5$<br>$88.7 \pm 0.6$<br>$89.7 \pm 0.6$<br>$93.1 \pm 0.3$<br>$90.8 \pm 0.5$<br>$88.7 \pm 1.0$ | $79.5 \pm 8.1$<br>$87.3 \pm 0.7$<br>$97.4 \pm 0.2$<br>$97.8 + 0.1$<br>$97.5 \pm 0.1$<br>$97.2 \pm 0.2$<br>$97.5 \pm 0.1$<br>$96.6 \pm 0.4$ | ٠<br>$93.3 \pm 0.3$<br>$98.8 \pm 0.2$<br>$99.2 \pm 0.1$<br>$98.6 \pm 0.1$<br>$98.6 \pm 0.2$<br>$98.9 \pm 0.2$<br>$98.1 \pm 0.1$ | $70.5 \pm 0.6$<br>$70.6 \pm 0.6$<br>$77.1 + 0.9$<br>$73.7 \pm 0.7$<br>$75.7 \pm 1.5$ | $82.3 \pm 0.4$<br>$84.6 \pm 0.3$<br>$83.0 \pm 0.4$<br>$83.0 \pm 0.3$<br>$88.4 \pm 0.4$ | $83.6 \pm 0.4$<br>$88.7 \pm 0.2$<br>$84.8 \pm 0.4$<br>$88.2 \pm 0.3$<br>$90.0 \pm 0.1$ |
| Factorization        | <b>BPC</b><br>Ratio $%$<br>HaBa                                                                     | 0.034<br>$92.4 \pm 0.4$                                                                                                    | $\mathbf Q$<br>0.17<br>$97.4 \pm 0.2$                                                                                                      | 49<br>0.83<br>$98.1 \pm 0.1$                                                                                                    | 0.034<br>$80.9 \pm 0.7$                                                              | $\mathbf Q$<br>0.17<br>$88.6 \pm 0.2$                                                  | 49<br>0.83<br>$90.3 \pm 0.1$                                                           |
| <b>Whole Dataset</b> |                                                                                                     |                                                                                                                            | $99.6 \pm 0.0$                                                                                                                             |                                                                                                                                 |                                                                                      | $93.5 \pm 0.1$                                                                         |                                                                                        |

Table 7: The performance (test accuracy %) comparison with state-of-the-art methods on MNIST and FashionMNIST datasets. IPC: Number of Images Per Class; BPC: Number of Bases Per Class; Ratio (%): the ratio of distilled images to the whole training set.

<span id="page-15-1"></span>

|                                                                                                                                       | Method                   |                        | ConvNet  |                                                                                                                                                                                                                                                                                                                                                                                   | ResNet   |                        | VGG                                                           |                             | AlexNet  |
|---------------------------------------------------------------------------------------------------------------------------------------|--------------------------|------------------------|----------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------|------------------------|---------------------------------------------------------------|-----------------------------|----------|
|                                                                                                                                       | <b>IPC</b><br><b>BPC</b> | $\mathcal{D}_{\alpha}$ | 11<br>10 | $\mathfrak{D}$                                                                                                                                                                                                                                                                                                                                                                    | 11<br>10 | $\mathcal{D}_{\alpha}$ | 11<br>10                                                      | $\mathcal{D}_{\mathcal{A}}$ | 11<br>10 |
| ImageFruit                                                                                                                            | Gain                     | $+2.92$                | $+2.40$  | Baseline 31.76 ± 1.64 40.12 ± 1.87 24.36 ± 2.20 31.24 ± 1.71 30.20 ± 1.43 42.52 ± 1.16 27.92 ± 1.84 29.88 ± 1.60<br>w. HaBa $34.68 \pm 1.13$ $42.52 \pm 1.56$ $26.60 \pm 2.48$ $33.08 \pm 1.02$ $31.92 \pm 1.91$ $45.12 \pm 1.18$ $28.16 \pm 1.29$ $32.84 \pm 1.69$<br>$+2.24$                                                                                                    | $+1.84$  | $+1.72$                | $+2.60$                                                       | $+0.24$                     | $+2.96$  |
| ImageMeow                                                                                                                             | Gain                     | $+1.64$                | $+1.92$  | Baseline $ 35.28 \pm 2.23 \, 41.00 \pm 1.45 \,  17.64 \pm 1.51 \, 19.64 \pm 0.93 \,  31.52 \pm 1.27 \, 39.44 \pm 1.23 \,  21.04 \pm 1.64 \, 22.04 \pm 1.72$<br>w. HaBa $36.92 \pm 0.93$ 42.92 $\pm 0.86$ (25.44 $\pm 1.02$ 26.28 $\pm 2.61$ (35.00 $\pm 0.76$ 47.68 $\pm 0.57$ (23.76 $\pm 2.06$ 24.04 $\pm 1.94$<br>$+7.80$                                                      | $+6.64$  | $+3.48$                | $+8.24$                                                       | $+2.72$                     | $+2.00$  |
| ImageNette                                                                                                                            | Gain                     | $-3.24$                | $+0.84$  | Baseline $\frac{55.16 \pm 1.08}{63.88 \pm 0.48}$ $\frac{25.52 \pm 1.31}{25.52 \pm 1.31}$ 42.80 $\pm 1.49$ $\frac{47.48 \pm 1.67}{25.80 \pm 1.59}$ 30.96 $\pm 0.97$ 34.60 $\pm 2.95$<br>w. HaBa $\left  51.92 \pm 1.65 \right $ 64.72 $\pm$ 1.60 $\left  28.88 \pm 2.61 \right $ 46.84 $\pm$ 1.25 $\left  47.80 \pm 1.21 \right $<br>$+3.36$                                       | $+4.04$  | $+0.32$                | $63.76 \pm 1.05$ 33.28 $\pm 1.98$ 40.84 $\pm 1.80$<br>$+0.96$ | $+2.68$                     | $+6.24$  |
| ImageSquawk   w. HaBa   41.88 ± 1.37 56.80 ± 1.04   31.52 ± 2.39 48.92 ± 1.77   39.64 ± 1.78 56.88 ± 0.84   23.28 ± 0.55 35.00 ± 1.72 | Gain                     | $-2.04$                | $+2.16$  | Baseline $ 43.92 \pm 0.63 \, 54.64 \pm 0.96 30.64 \pm 1.47 \, 46.40 \pm 1.85 39.36 \pm 1.83 \, 52.00 \pm 1.91 22.04 \pm 1.80 \, 34.20 \pm 2.08$<br>$+0.88$                                                                                                                                                                                                                        | $+2.52$  | $+0.28$                | $+4.88$                                                       | $+1.24$                     | $+0.80$  |
| ImageWoof                                                                                                                             | Gain                     | $+1.48$                | $+2.04$  | Baseline $ 30.92 \pm 1.26 \cdot 36.56 \pm 0.75 16.24 \pm 1.48 \cdot 18.12 \pm 0.47 25.60 \pm 0.69 \cdot 29.36 \pm 1.23 22.68 \pm 1.42 \cdot 23.68 \pm 1.37$<br>w. HaBa 32.40 $\pm$ 0.67 38.60 $\pm$ 1.26 20.20 $\pm$ 1.55 25.20 $\pm$ 0.95 27.08 $\pm$ 1.81 37.44 $\pm$ 1.08 24.88 $\pm$ 1.20 27.72 $\pm$ 1.12<br>$+3.96$                                                         | $+7.08$  | $+1.48$                | $+8.08$                                                       | $+2.20$                     | $+4.04$  |
| Image Yellow                                                                                                                          | Gain                     | $+0.72$                | $+2.60$  | Baseline $ 49.72 \pm 1.38$ 60.40 $\pm 1.46$   29.08 $\pm 1.99$ 42.72 $\pm 1.24$   44.04 $\pm 1.46$ 50.84 $\pm 0.56$   28.60 $\pm 1.48$ 35.60 $\pm 2.03$<br>w. HaBa $\left  50.44 \pm 1.56 \right.$ 63.00 $\pm$ 1.61 $\left  36.32 \pm 0.65 \right.$ 48.48 $\pm$ 1.55 $\left  47.28 \pm 1.59 \right.$ 57.24 $\pm$ 1.01 $\left  29.08 \pm 1.19 \right.$ 36.44 $\pm$ 1.21<br>$+7.24$ | $+5.76$  | $+3.24$                | $+6.40$                                                       | $+0.48$                     | $+0.84$  |

Table 8: Cross-architecture performance (test accuracy %) comparison with the baseline on various subsets of ImageNet dataset.

architectures: ResNet, VGG, and AlexNet. To ensure the same number of parameters used for the distilled datasets, we set the number of images per class used by the baseline as the number of bases per class used by HaBa plus 1, *i.e.*, 2 IPC v.s. 1 BPC and 11 IPC v.s. 10 BPC. Other settings follow the same configuration in the main paper.

The test performances of models trained by the distilled datasets are shown in Tab. [8.](#page-15-1) We can observe that HaBa outperforms the baseline in almost all cases except several experiments when IPC and BPC are small and the architectures of training and testing are the same. Notably, in all the cross-architecture generalization settings, HaBa achieves superior performance over the baseline, which further demonstrates the improvement of data efficiency introduced by the factorization and online pair-wise combination.

More Ablations on Hallucinators: In the default setting, the encoder and decoder of hallucinators have 1 Conv-ReLU block and the number of feature channels is 3. In this part, we provide more results when we consider increasing the capacity of the hallucination networks. As shown in Tab. [9,](#page-16-0) we try increasing the depth of the networks by adding more nonlinear blocks. Although the performance

<span id="page-16-0"></span>

| Depth            | 0            | 1            | 2            | 3            |
|------------------|--------------|--------------|--------------|--------------|
| Accuracy (%)     | 68.43 ± 0.37 | 70.27 ± 0.63 | 71.17 ± 0.29 | 71.55 ± 0.27 |
| Downstream Speed | 144.54       | 140.11       | 125.04       | 115.62       |
| # of Parameters  | 6,144        | 6,312        | 10,963       | 16,131       |

 $#$  of Channels  $3$  8 16 Accuracy 70.27±0.63 70.47±0.37 71.28±0.35 Downstream Speed 140.11 138.48 135.12<br>
# of Parameters 6.312 16.827 33.651 # of Parameters 6,312

Table 9: Ablation studies on the depth (number of nonlinear blocks) of hallucinator.

<span id="page-16-2"></span>

| BPC               | 1                   | 10                  | 50                  |
|-------------------|---------------------|---------------------|---------------------|
| Ours              | $55.66 	extpm 0.29$ | $70.27 	extpm 0.63$ | $74.04 	extpm 0.16$ |
| Share Enc. & Dec. | $55.14 	extpm 0.44$ | $69.47 	extpm 0.09$ | $72.69 	extpm 0.39$ |
| Baseline          | $49.89 	extpm 0.95$ | $65.92 	extpm 0.62$ | $70.73 	extpm 0.52$ |

Table 10: Ablation studies on the number of feature channels in hallucinator.

| SPC | Rand | Herd | DSA  | DM   | IDC  | IDC w. HaBa | Whole Dataset |
|-----|------|------|------|------|------|-------------|---------------|
| 10  | 42.6 | 56.2 | 65.0 | 69.1 | 73.3 | 74.5        | 93.4          |
| 20  | 57.0 | 72.9 | 74.0 | 77.2 | 83.0 | 84.3        |               |

Table 11: Impact on sharing encoder and decoder across all hallucinators.

Table 12: Results of speech recognition on Mini Speech Commands.

<span id="page-16-1"></span>Image /page/16/Figure/8 description: This image displays three 3D scatter plots, each representing a different storage budget: Small, Medium, and Large. Each plot visualizes the performance of a model across different configurations of "# of Ha.", "Depth of Ha.", and "Channel of Ba.". The points are colored based on their performance values, with darker shades indicating higher values. For the Small Storage Budget (a), the parameters are 10x2x32x32x3, and the baseline accuracy is 49.9%. The performance values shown are 55.7, 48.3, 56.6, 52.0, 61.0, NA, 62.0, and 58.4. For the Medium Storage Budget (b), the parameters are 10x11x32x32x3, and the baseline accuracy is 65.9%. The performance values are 70.6, 70.3, 72.1, 69.0, 72.5, 71.8, 71.7, and 73.8. For the Large Storage Budget (c), the parameters are 10x2x51x32x3, and the baseline accuracy is 70.7%. The performance values are 73.3, 74.0, 73.1, 74.6, 73.1, 75.4, 73.8, and 73.3.

Figure 9: Exploration on the configurations of different factors in hallucinators and bases.

can indeed be improved, it results in nonnegligible latency to downstream training speed, measured by the number of epochs per second. Taking both training speed and performance into consideration, we consider using only 1 nonlinear block by default, which yields best trade-off between the two factors. Likewise, we also try increasing the number of feature channels in halluciantors as shown in Tab. [10.](#page-16-0) The number of parameters is almost proportional to the number of channels. However, the performance gain is very limited. Thus, we simply take the number of channels in images, which is 3 for RGB images, as the number of feature channels in hallucinators.

More Insights on the Configurations of Hallucinators and Bases: As shown in the ablation studies in the main paper and the supplement, under the framework of hallucinator-basis factorization, there are many factors that affect the performance. Given a fixed storage budget, how to scale the bases and hallucinators is an important topic. Among all the factors, we empirically find that the depth of hallucinators, the number of hallucinators, the number of channels in each basis, and the number of bases are the most important ones, which will be studied in the following exploration. Here, we consider three types of storage budget: small, medium, and large, corresponding to the cost of IPC=2, 11, and 51 for the baseline method respectively. We consider cases of 1 and 2 convolution blocks for the depth of hallucinators, 2 and 5 for the number of hallucinators, and 1 and 3 for the number of channels in each basis. For each setting, we adjust the number of bases to fit the given budget. Enumerating all the configurations, there are totally 8 settings for each kind of budget. Their results are visualized in Fig. [9.](#page-16-1) Based on the results, we have the following observations:

- For all the three types of budget, the best performance is achieved by using deeper hallucinators. Especially under small and medium budgets, using depth 2 can outperform using depth 1 almost consistently. This can be explained by the more complex sample-wise relationship extracted by hallucinators.
- In our framework, bases are expected to store sample-independent information while hallucinators are used to encode shared relationship across all the samples. When the budget is small, using 1-channel bases can achieve significantly better results. This is because small storage budget would more rely on increasing the number of independent data samples for a better diversity. The informativeness of each basis appears less important.
- When the budget increases, the advantage of 1-channel bases mentioned before would diminish gradually. Especially under the large budgets, 3-channel bases outperform

<span id="page-17-0"></span>Image /page/17/Figure/0 description: The image contains a line graph and a table. The line graph plots "Test Accuracy (%)" on the y-axis against "Corrupted Level" on the x-axis, with values ranging from 1 to 5. There are six lines representing different experimental conditions: "Baseline IPC=2" (red dashed line), "Ours BPC=1" (red solid line), "Baseline IPC=11" (green dashed line), "Ours BPC=10" (green solid line), "Baseline IPC=51" (blue dashed line), "Ours BPC=50" (blue solid line), and "Whole Dataset" (black solid line with circles). The table lists "Hyper-Parameter", "Notation", and "Value". Key parameters include "Height of Basis" (h'), "Width of Basis" (w'), "Channel of Basis" (c'), "Channel of Hallucinator" (c"), "Depth of Hallucinator" (-), "Learning Rate of Feature Extractor" (ηF), "Weight of Lcon." (λcon.), "Weight of Ltask" (λtask), "Weight of LDD" (λDD), and "Weight of Lcos." (λcos.). The corresponding values are: h'=Height of Image h, w'=Width of Image w, c'=Channel of Image c, c"=Channel of Image c, - = 1, ηF = 0.001, λcon. = 0.1, λtask = 1, λDD = 1, and λcos. = 0.1.

Figure 10: Generalization performance on images with different corrupted levels.

Table 13: List of hyper-parameters.

1-channel ones consistently. The reason is that when the number of bases is adequate, focusing on the informativeness of each basis can produce more benefit than increasing the number.

- When the budget is large, using more hallucinators can yield slightly better results, which can probably be attributed to the further improvement on the diversity.
- The larger the budget is, the less insensitive the performance is, to different configurations.

Note that the above exploration is conducted without taking the downstream training speed into consideration, which is also an important metric in the task of dataset distillation. Our opinion on the scalability is that, when downstream training overhead is not a issue, deeper hallucinators are recommended for better performance; otherwise if downstream efficiency is desired, we find that 1 nonlinear block is sufficient, since heavier hallucination networks can result in nonnegligible latency, especially when the total number of images is large.

Sharing Encoder and Decoder across all Hallucinators: As a variant of our default case which uses different hallucination networks, it is also feasible for the halluciantors to share a common group of encoder and decoder but use different parameters ( $\sigma, \mu$ ) for affine transformation, which is potential to further boost the data efficiency. As shown in Tab.  $11$ , the performance becomes slightly worse. We conjecture that different convolution encoders and decoders may contribute to the diversity of the extracted patterns, which increases the representation ability of the hallucinator set. Moreover, since we only use 1 convolution block for encoders and decoders, the number of parameters is not so significant compared with that of a basis. Therefore, we consider making the whole network independent with each other for all hallucinators.

Results on Speech Domain: To validate the versatility of the proposed hallucinator-basis factorization solution, we further conduct experiments on the speech domain using Mini Speech Commands [\[53\]](#page-12-13), which contains 8,000 audio clips for 8 command classes. We adopt IDC [\[24\]](#page-11-10) as the baseline and all the protocols for comparisons follow the official settings. We compare our method with the coreset selection based Random and Herding, DSA [\[60\]](#page-12-5), DM [\[61\]](#page-12-6), and the IDC baseline [\[24\]](#page-11-10). The results in Tab. [12](#page-16-2) shows that our method can produce consistent improvement on the downstream test accuracy, which further reflects the generality of our method for different modalities. Here, SPC denotes the number of speech spectrograms per class.

Robustness to Corruption: We further examine the generalization performance of our method and the baseline one on CIFAR10-C [\[17\]](#page-10-21), the corrupted version of CIFAR10 dataset with 19 different types of corruption. There are five corrupted levels from 1 (mildest) to 5 (severest) and we report the mean test accuracy across 19 domains on different levels. Since the proposed method can increase the accuracy and alleviate the under-fitting problem on the original domain, which is one dominant component of cross-domain generalization [\[3\]](#page-10-22), it can also demonstrate superior robustness in all corrupted data as demonstrated in Fig. [10.](#page-17-0) Also, the gap between performance using distilled dataset and original dataset becomes smaller with the increase of corrupted level, which suggests that our method improves the domain generalization ability potentially, thanks to the diverse training data composed of hallucinators and bases.

List of Hyper-Parameters: In Tab. [13,](#page-17-0) we provide a clear view of the hyper-parameters used in this paper. All the experiments follows these settings if not specified. The performance of our method is insensitive to the values of these hyper-parameters as analyzed in the ablation studies in both the

<span id="page-18-0"></span>Image /page/18/Figure/0 description: The image displays four scatter plots, each representing a different dataset or method. The plots are arranged horizontally and labeled (a) MTT-CIFAR10, (b) HLP-CIFAR10, (c) MTT-ImageNet, and (d) HLP-ImageNet. Each plot shows a dense cloud of small gray dots, with some larger, colored star-shaped markers scattered throughout. The stars in plot (a) are predominantly blue. Plot (b) features stars in blue, green, red, and orange. Plot (c) has mostly blue stars, similar to (a), but with fewer stars overall. Plot (d) shows a sparser distribution of stars in blue, green, and yellow.

(a) MTT on CIFAR10 (b) HaBa on CIFAR10 (c) MTT on ImageSquawk (d) HaBa on ImageSquawk Figure 11: TSNE visualization of results by our HaBa and baseline MTT on CIFAR10 and ImageSquawk datasets. Markers with different colors in our results denote images generated by different hallucinators. Gray dots denote real images.

<span id="page-18-1"></span>Image /page/18/Figure/2 description: The image displays six grids of smaller images, arranged in two rows and three columns. Each grid is labeled with a letter and a description: (a) Bases, (b) Images by H1, (c) Images by H2, (d) Images by H3, (e) Images by H4, and (f) Images by H5. The "Bases" grid contains abstract, colorful patterns. The other five grids, "Images by H1" through "Images by H5", contain numerous small images that appear to be generated or representative of different categories or features, with many showing elements resembling animals, faces, or objects.

Figure 12: Visualization of factorized results by our HaBa on CIFAR10. Please zoom-in for better visualization.

main paper and the appendix. Other hyper-parameters not listed come from the adopted baseline methods and we follow their original settings.

TSNE Visualizations: To provide a better understanding on why the HaBa factorization can help on data efficiency in dataset distillation, we adopt TSNE [\[50\]](#page-12-14) to visualize the features before the last linear layer of a teacher model trained on the original datasets. In Fig. [11,](#page-18-0) we plot features of both original images and the distilled ones. The results reveal that datasets restored from our hallucinators and bases can describe the original data distribution more finely, which means that the original datasets can be represented with the distilled ones with less information loss. Given that the total numbers of parameters used for storing distilled datasets are the same, our method can improve the data efficiency significantly.

Visualizations of Factorized Results: We first provide the full results of HaBa factorization on CIFAR10 dataset with 5 hallucinators and 10 BPC in Fig. [12,](#page-18-1) as a supplement to Fig. 4 in the main paper. We also provide the distilled results on datasets with larger resolutions in Fig. [13](#page-19-0) and [14](#page-20-0) for the above 6 ImageNet subsets. Here, we use 1 BPC and 2 hallucinators for visualization. Through these results, we can find that bases in our scheme mainly define the basic contents, while different hallucinators may transform each basis to different appearances and styles. Such difference is encouraged to be as large as possible to diversify the distilled data and thus improve data efficiency during the end-to-end training pipeline of DD.

<span id="page-19-0"></span>Image /page/19/Picture/0 description: A grid of ten square images, each displaying abstract patterns of color. The colors are predominantly muted purples, greens, and yellows, with some hints of pink and blue. The patterns within each square are varied, with some appearing more textured and chaotic, while others have softer, more blended areas. The images are arranged in a single row, separated by thin black lines.

(a) Bases on ImageFruit.

| Image: a cluster of small, light brown and greyish-brown objects | Image: a cluster of small, yellow and green objects | Image: a cluster of small, bright pink and green objects | Image: a cluster of small, orange and yellow objects | Image: a cluster of small, yellow and brown objects | Image: a cluster of small, pink and purple objects | Image: a cluster of small, greyish-brown and purple objects | Image: a cluster of small, pink and orange objects | Image: a cluster of small, green and brown objects | Image: a cluster of small, bright green objects |
|------------------------------------------------------------------|-----------------------------------------------------|----------------------------------------------------------|------------------------------------------------------|-----------------------------------------------------|----------------------------------------------------|-------------------------------------------------------------|----------------------------------------------------|----------------------------------------------------|-------------------------------------------------|
|------------------------------------------------------------------|-----------------------------------------------------|----------------------------------------------------------|------------------------------------------------------|-----------------------------------------------------|----------------------------------------------------|-------------------------------------------------------------|----------------------------------------------------|----------------------------------------------------|-------------------------------------------------|

(b) Images by  $H_1$  on ImageFruit.

Image /page/19/Figure/4 description: The image displays a grid of 12 small, abstract images, each separated by a thin black line. The images appear to be visualizations or representations of data, possibly from a machine learning model, as they show textured patterns and color variations. Some images have a predominantly grey or beige tone with subtle color shifts, while others exhibit more distinct colors like red, orange, and yellow, suggesting different features or categories. The overall impression is a collection of diverse visual patterns.

(c) Images by  $H_2$  on ImageFruit.

(d) Bases on ImageMeow.

| Albert 120 Fall |  |  |
|-----------------|--|--|
|                 |  |  |

(e) Images by  $H_1$  on ImageMeow.

(f) Images by  $H_2$  on ImageMeow.

(g) Bases on ImageNette.

Image /page/19/Picture/14 description: The image displays a grid of 12 abstract, blurry images. Each image is a square and they are arranged in a single row. The colors are predominantly blues, grays, and some warmer tones like orange and brown. The images appear to be generated by a neural network, possibly representing features learned by the network. Some images have a somewhat recognizable shape, like a face or a figure, while others are more chaotic patterns of color and texture.

(h) Images by  $H_1$  on ImageNette.

(i) Images by  $H_2$  on ImageNette.

Figure 13: Visualization of factorized results by our HaBa on ImageNet subsets.

<span id="page-20-0"></span>Image /page/20/Picture/0 description: A grid of 10 images, each displaying abstract patterns of color and light. The images appear to be visualizations of neural network activations or feature representations, with swirling textures and gradients of purple, blue, green, yellow, and pink. Some images show more defined shapes or lines, suggesting specific features being highlighted, while others are more diffuse and blended.

(a) Bases on ImageSquawk.

Image /page/20/Picture/2 description: The image displays a grid of 10 small, square images arranged horizontally. Each small image appears to be a close-up or a stylized representation of an animal, possibly penguins or similar birds, with some abstract or blurry elements. The colors are muted, with shades of blue, gray, brown, and white dominating. The images are separated by thin black lines, creating a mosaic-like effect.

(b) Images by  $H_1$  on ImageSquawk.

Image /page/20/Figure/4 description: A grid of 10 images, each showing a different scene. The images are mostly abstract and blurry, with some appearing to depict natural landscapes or groups of animals. Some images have vibrant colors, while others are muted and grayscale. The overall impression is of a collection of diverse visual elements, possibly generated by an AI or representing different stages of a process.

(c) Images by  $H_2$  on ImageSquawk.

Image /page/20/Figure/6 description: A grid of ten small, abstract images, each with a different color palette and pattern. The images are arranged in a single row, with thin black borders separating them. The colors range from muted purples, blues, and greens to brighter pinks and yellows, creating a psychedelic or impressionistic effect. Some images appear to have swirling or circular patterns, while others are more fragmented and textured. The overall impression is one of abstract art or visualizations of complex data.

(d) Bases on ImageWoof.

Image /page/20/Figure/8 description: A collage of ten images, each depicting a close-up of an animal, possibly a dog or a deer, in a natural setting. The images are arranged in a horizontal row, with thin black borders separating each individual picture. The overall quality of the images is somewhat blurry, making it difficult to discern fine details, but the general forms and colors of the animals are visible. Some images show the animals' faces, while others focus on their bodies or legs. The backgrounds appear to be foliage or natural terrain.

(e) Images by  $\mathcal{H}_1$  on ImageWoof.

(f) Images by  $H_2$  on ImageWoof.

![image](https://raw.githubusercontent.com/google/styleguide/gh-pages/images/image.png)

(g) Bases on ImageYellow.

Image /page/20/Picture/14 description: The image displays a grid of 10 small, square images arranged in a single row. Each small image is a colorful, abstract representation, possibly generated by a neural network. The colors vary across the images, with some featuring purples, greens, yellows, and browns. Some images appear to have a central focus or shape, while others are more textured and diffuse. The overall impression is a collection of visual patterns or features.

(h) Images by  $H_1$  on ImageYellow.

(i) Images by  $H_2$  on ImageYellow.

Figure 14: Visualization of factorized results by our HaBa on ImageNet subsets (Cont.).
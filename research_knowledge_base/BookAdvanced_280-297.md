This is page 261 Printer: Opaque this

# 8 Model Inference and Averaging

## 8.1 Introduction

For most of this book, the fitting (learning) of models has been achieved by minimizing a sum of squares for regression, or by minimizing cross-entropy for classification. In fact, both of these minimizations are instances of the maximum likelihood approach to fitting.

In this chapter we provide a general exposition of the maximum likelihood approach, as well as the Bayesian method for inference. The bootstrap, introduced in Chapter 7, is discussed in this context, and its relation to maximum likelihood and Bayes is described. Finally, we present some related techniques for model averaging and improvement, including committee methods, bagging, stacking and bumping.

# 8.2 The Bootstrap and Maximum Likelihood Methods

### 8.2.1 A Smoothing Example

The bootstrap method provides a direct computational way of assessing uncertainty, by sampling from the training data. Here we illustrate the bootstrap in a simple one-dimensional smoothing problem, and show its connection to maximum likelihood.

Image /page/1/Figure/0 description: The image contains the text "262 8. Model Inference and Averaging".

Image /page/1/Figure/1 description: The image displays two plots side-by-side. The left plot is a scatter plot with 'x' on the horizontal axis and 'y' on the vertical axis. The x-axis ranges from 0.0 to 3.0, and the y-axis ranges from -1 to 5. There are numerous red dots scattered across this plot. The right plot is titled 'B-spline Basis' and also has 'x' on the horizontal axis, ranging from 0.0 to 3.0. The vertical axis ranges from 0.0 to 1.0. This plot shows several colored curves (red, green, blue, purple, orange, cyan, gray) representing B-spline basis functions. Vertical dashed lines are present at x = 0.5, 1.0, 1.5, 2.0, and 2.5.

FIGURE 8.1. (Left panel): Data for smoothing example. (Right panel:) Set of seven B-spline basis functions. The broken vertical lines indicate the placement of the three knots.

Denote the training data by  $\mathbf{Z} = \{z_1, z_2, \ldots, z_N\}$ , with  $z_i = (x_i, y_i)$ ,  $i = 1, 2, \ldots, N$ . Here  $x_i$  is a one-dimensional input, and  $y_i$  the outcome, either continuous or categorical. As an example, consider the  $N = 50$  data points shown in the left panel of Figure 8.1.

Suppose we decide to fit a cubic spline to the data, with three knots placed at the quartiles of the  $X$  values. This is a seven-dimensional linear space of functions, and can be represented, for example, by a linear expansion of B-spline basis functions (see Section 5.9.2):

$$
\mu(x) = \sum_{j=1}^{7} \beta_j h_j(x). \tag{8.1}
$$

Here the  $h_i(x)$ ,  $j = 1, 2, ..., 7$  are the seven functions shown in the right panel of Figure 8.1. We can think of  $\mu(x)$  as representing the conditional mean  $E(Y|X=x)$ .

Let **H** be the  $N \times 7$  matrix with *ij*th element  $h_i(x_i)$ . The usual estimate of  $\beta$ , obtained by minimizing the squared error over the training set, is given by

$$
\hat{\beta} = (\mathbf{H}^T \mathbf{H})^{-1} \mathbf{H}^T \mathbf{y}.
$$
\n(8.2)

The corresponding fit  $\hat{\mu}(x) = \sum_{j=1}^{7} \hat{\beta}_j h_j(x)$  is shown in the top left panel of Figure 8.2.

The estimated covariance matrix of  $\hat{\beta}$  is

$$
\widehat{\text{Var}}(\hat{\beta}) = (\mathbf{H}^T \mathbf{H})^{-1} \hat{\sigma}^2,\tag{8.3}
$$

where we have estimated the noise variance by  $\hat{\sigma}^2 = \sum_{i=1}^N (y_i - \hat{\mu}(x_i))^2/N$ . Letting  $h(x)^T = (h_1(x), h_2(x), \ldots, h_7(x))$ , the standard error of a predic-

Image /page/2/Figure/1 description: The image displays four scatter plots arranged in a 2x2 grid. Each plot shows red dots representing data points, with an x-axis labeled 'x' ranging from 0.0 to 3.0 and a y-axis labeled 'y' ranging from -1 to 5. The top-left plot shows a smooth green curve fitting the data. The top-right plot shows the same smooth green curve with dashed gray lines indicating confidence intervals. The bottom-left plot displays multiple light blue curves, suggesting a range of possible fits or bootstrap samples. The bottom-right plot is similar to the top-right, showing a smooth green curve with dashed gray confidence intervals.

FIGURE 8.2. (Top left:) B-spline smooth of data. (Top right:) B-spline smooth plus and minus 1.96× standard error bands. (Bottom left:) Ten bootstrap replicates of the B-spline smooth. (Bottom right:) B-spline smooth with  $95\%$  standard error bands computed from the bootstrap distribution.

tion  $\hat{\mu}(x) = h(x)^T \hat{\beta}$  is

$$
\widehat{\text{se}}[\widehat{\mu}(x)] = [h(x)^T (\mathbf{H}^T \mathbf{H})^{-1} h(x)]^{\frac{1}{2}} \widehat{\sigma}.
$$
\n(8.4)

In the top right panel of Figure 8.2 we have plotted  $\hat{\mu}(x) \pm 1.96 \cdot \hat{\rm se}[\hat{\mu}(x)].$ Since 1.96 is the 97.5% point of the standard normal distribution, these represent approximate  $100 - 2 \times 2.5\% = 95\%$  pointwise confidence bands for  $\mu(x)$ .

Here is how we could apply the bootstrap in this example. We draw B datasets each of size  $N = 50$  with replacement from our training data, the sampling unit being the pair  $z_i = (x_i, y_i)$ . To each bootstrap dataset  $\mathbf{Z}^*$ we fit a cubic spline  $\hat{\mu}^*(x)$ ; the fits from ten such samples are shown in the bottom left panel of Figure 8.2. Using  $B = 200$  bootstrap samples, we can form a 95% pointwise confidence band from the percentiles at each  $x$ : we find the  $2.5\% \times 200 =$  fifth largest and smallest values at each x. These are plotted in the bottom right panel of Figure 8.2. The bands look similar to those in the top right, being a little wider at the endpoints.

There is actually a close connection between the least squares estimates (8.2) and (8.3), the bootstrap, and maximum likelihood. Suppose we further assume that the model errors are Gaussian,

$$
Y = \mu(X) + \varepsilon; \varepsilon \sim N(0, \sigma^2),
$$
  
$$
\mu(x) = \sum_{j=1}^{7} \beta_j h_j(x).
$$
 (8.5)

The bootstrap method described above, in which we sample with replacement from the training data, is called the nonparametric bootstrap. This really means that the method is "model-free," since it uses the raw data, not a specific parametric model, to generate new datasets. Consider a variation of the bootstrap, called the parametric bootstrap, in which we simulate new responses by adding Gaussian noise to the predicted values:

$$
y_i^* = \hat{\mu}(x_i) + \varepsilon_i^*; \quad \varepsilon_i^* \sim N(0, \hat{\sigma}^2); \quad i = 1, 2, \dots, N. \tag{8.6}
$$

This process is repeated B times, where  $B = 200$  say. The resulting bootstrap datasets have the form  $(x_1, y_1^*), \ldots, (x_N, y_N^*)$  and we recompute the B-spline smooth on each. The confidence bands from this method will exactly equal the least squares bands in the top right panel, as the number of bootstrap samples goes to infinity. A function estimated from a bootstrap sample  $y^*$  is given by  $\hat{\mu}^*(x) = h(x)^T (\mathbf{H}^T \mathbf{H})^{-1} \mathbf{H}^T \mathbf{y}^*$ , and has distribution

$$
\hat{\mu}^*(x) \sim N(\hat{\mu}(x), h(x)^T (\mathbf{H}^T \mathbf{H})^{-1} h(x) \hat{\sigma}^2). \tag{8.7}
$$

Notice that the mean of this distribution is the least squares estimate, and the standard deviation is the same as the approximate formula (8.4).

### 8.2.2 Maximum Likelihood Inference

It turns out that the parametric bootstrap agrees with least squares in the previous example because the model (8.5) has additive Gaussian errors. In general, the parametric bootstrap agrees not with least squares but with maximum likelihood, which we now review.

We begin by specifying a probability density or probability mass function for our observations

$$
z_i \sim g_\theta(z). \tag{8.8}
$$

In this expression  $\theta$  represents one or more unknown parameters that govern the distribution of Z. This is called a parametric model for Z. As an example, if Z has a normal distribution with mean  $\mu$  and variance  $\sigma^2$ , then

$$
\theta = (\mu, \sigma^2),\tag{8.9}
$$

and

$$
g_{\theta}(z) = \frac{1}{\sqrt{2\pi}\sigma} e^{-\frac{1}{2}(z-\mu)^2/\sigma^2}.
$$
 (8.10)

Maximum likelihood is based on the likelihood function, given by

$$
L(\theta; \mathbf{Z}) = \prod_{i=1}^{N} g_{\theta}(z_i), \qquad (8.11)
$$

the probability of the observed data under the model  $g_{\theta}$ . The likelihood is defined only up to a positive multiplier, which we have taken to be one. We think of  $L(\theta; \mathbf{Z})$  as a function of  $\theta$ , with our data **Z** fixed.

Denote the logarithm of  $L(\theta; \mathbf{Z})$  by

$$
\ell(\theta; \mathbf{Z}) = \sum_{i=1}^{N} \ell(\theta; z_i)
$$
$$
= \sum_{i=1}^{N} \log g_{\theta}(z_i), \qquad (8.12)
$$

which we will sometimes abbreviate as  $\ell(\theta)$ . This expression is called the log-likelihood, and each value  $\ell(\theta; z_i) = \log g_\theta(z_i)$  is called a log-likelihood component. The method of maximum likelihood chooses the value  $\theta = \hat{\theta}$ to maximize  $\ell(\theta; \mathbf{Z})$ .

The likelihood function can be used to assess the precision of  $\hat{\theta}$ . We need a few more definitions. The score function is defined by

$$
\dot{\ell}(\theta; \mathbf{Z}) = \sum_{i=1}^{N} \dot{\ell}(\theta; z_i),
$$
\n(8.13)

where  $\dot{\ell}(\theta; z_i) = \partial \ell(\theta; z_i)/\partial \theta$ . Assuming that the likelihood takes its maximum in the interior of the parameter space,  $\dot{\ell}(\hat{\theta}; \mathbf{Z}) = 0$ . The *information* matrix is

$$
\mathbf{I}(\theta) = -\sum_{i=1}^{N} \frac{\partial^2 \ell(\theta; z_i)}{\partial \theta \partial \theta^T}.
$$
\n(8.14)

When  $\mathbf{I}(\theta)$  is evaluated at  $\theta = \hat{\theta}$ , it is often called the *observed information*. The Fisher information (or expected information) is

$$
\mathbf{i}(\theta) = \mathbf{E}_{\theta}[\mathbf{I}(\theta)].\tag{8.15}
$$

Finally, let  $\theta_0$  denote the true value of  $\theta$ .

A standard result says that the sampling distribution of the maximum likelihood estimator has a limiting normal distribution

$$
\hat{\theta} \to N(\theta_0, \mathbf{i}(\theta_0)^{-1}), \tag{8.16}
$$

as  $N \to \infty$ . Here we are independently sampling from  $g_{\theta_0}(z)$ . This suggests that the sampling distribution of  $\hat{\theta}$  may be approximated by

$$
N(\hat{\theta}, \mathbf{i}(\hat{\theta})^{-1}) \text{ or } N(\hat{\theta}, \mathbf{I}(\hat{\theta})^{-1}), \tag{8.17}
$$

where  $\hat{\theta}$  represents the maximum likelihood estimate from the observed data.

The corresponding estimates for the standard errors of  $\hat{\theta}_j$  are obtained from

$$
\sqrt{\mathbf{i}(\hat{\theta})_{jj}^{-1}} \qquad \text{and} \qquad \sqrt{\mathbf{I}(\hat{\theta})_{jj}^{-1}}. \tag{8.18}
$$

Confidence points for  $\theta_j$  can be constructed from either approximation in (8.17). Such a confidence point has the form

$$
\label{eq:theta_j} \hat{\theta}_j - z^{(1-\alpha)} \cdot \sqrt{\textbf{i}(\hat{\theta})^{-1}_{jj}} \qquad \text{or} \qquad \hat{\theta}_j - z^{(1-\alpha)} \cdot \sqrt{\textbf{I}(\hat{\theta})^{-1}_{jj}},
$$

respectively, where  $z^{(1-\alpha)}$  is the  $1-\alpha$  percentile of the standard normal distribution. More accurate confidence intervals can be derived from the likelihood function, by using the chi-squared approximation

$$
2[\ell(\hat{\theta}) - \ell(\theta_0)] \sim \chi_p^2,\tag{8.19}
$$

where p is the number of components in  $\theta$ . The resulting  $1 - 2\alpha$  confidence interval is the set of all  $\theta_0$  such that  $2[\ell(\hat{\theta}) - \ell(\theta_0)] \leq \chi_p^2$  $\frac{(1-2\alpha)}{\alpha}$ where  $\chi_p^2$  $(1-2\alpha)$  is the  $1-2\alpha$  percentile of the chi-squared distribution with p degrees of freedom.

Let's return to our smoothing example to see what maximum likelihood yields. The parameters are  $\theta = (\beta, \sigma^2)$ . The log-likelihood is

$$
\ell(\theta) = -\frac{N}{2}\log \sigma^2 2\pi - \frac{1}{2\sigma^2} \sum_{i=1}^{N} (y_i - h(x_i)^T \beta)^2.
$$
 (8.20)

The maximum likelihood estimate is obtained by setting  $\partial \ell / \partial \beta = 0$  and  $\partial \ell / \partial \sigma^2 = 0$ , giving

$$
\hat{\beta} = (\mathbf{H}^T \mathbf{H})^{-1} \mathbf{H}^T \mathbf{y},
$$
  
\n
$$
\hat{\sigma}^2 = \frac{1}{N} \sum (y_i - \hat{\mu}(x_i))^2,
$$
\n(8.21)

which are the same as the usual estimates given in  $(8.2)$  and below  $(8.3)$ .

The information matrix for  $\theta = (\beta, \sigma^2)$  is block-diagonal, and the block corresponding to  $\beta$  is

$$
\mathbf{I}(\beta) = (\mathbf{H}^T \mathbf{H})/\sigma^2, \tag{8.22}
$$

so that the estimated variance  $(\mathbf{H}^T \mathbf{H})^{-1} \hat{\sigma}^2$  agrees with the least squares estimate (8.3).

### 8.2.3 Bootstrap versus Maximum Likelihood

In essence the bootstrap is a computer implementation of nonparametric or parametric maximum likelihood. The advantage of the bootstrap over the maximum likelihood formula is that it allows us to compute maximum likelihood estimates of standard errors and other quantities in settings where no formulas are available.

In our example, suppose that we adaptively choose by cross-validation the number and position of the knots that define the B-splines, rather than fix them in advance. Denote by  $\lambda$  the collection of knots and their positions. Then the standard errors and confidence bands should account for the adaptive choice of  $\lambda$ , but there is no way to do this analytically. With the bootstrap, we compute the B-spline smooth with an adaptive choice of knots for each bootstrap sample. The percentiles of the resulting curves capture the variability from both the noise in the targets as well as that from  $\lambda$ . In this particular example the confidence bands (not shown) don't look much different than the fixed  $\lambda$  bands. But in other problems, where more adaptation is used, this can be an important effect to capture.

## 8.3 Bayesian Methods

In the Bayesian approach to inference, we specify a sampling model  $Pr(Z|\theta)$ (density or probability mass function) for our data given the parameters,

and a prior distribution for the parameters  $Pr(\theta)$  reflecting our knowledge about  $\theta$  before we see the data. We then compute the posterior distribution

$$
Pr(\theta|\mathbf{Z}) = \frac{Pr(\mathbf{Z}|\theta) \cdot Pr(\theta)}{\int Pr(\mathbf{Z}|\theta) \cdot Pr(\theta) d\theta},
$$
\n(8.23)

which represents our updated knowledge about  $\theta$  after we see the data. To understand this posterior distribution, one might draw samples from it or summarize by computing its mean or mode. The Bayesian approach differs from the standard ("frequentist") method for inference in its use of a prior distribution to express the uncertainty present before seeing the data, and to allow the uncertainty remaining after seeing the data to be expressed in the form of a posterior distribution.

The posterior distribution also provides the basis for predicting the values of a future observation  $z^{\text{new}}$ , via the *predictive distribution*:

$$
Pr(z^{new}|\mathbf{Z}) = \int Pr(z^{new}|\theta) \cdot Pr(\theta|\mathbf{Z}) d\theta.
$$
 (8.24)

In contrast, the maximum likelihood approach would use  $Pr(z^{new}|\hat{\theta}),$ the data density evaluated at the maximum likelihood estimate, to predict future data. Unlike the predictive distribution (8.24), this does not account for the uncertainty in estimating  $\theta$ .

Let's walk through the Bayesian approach in our smoothing example. We start with the parametric model given by equation (8.5), and assume for the moment that  $\sigma^2$  is known. We assume that the observed feature values  $x_1, x_2, \ldots, x_N$  are fixed, so that the randomness in the data comes solely from y varying around its mean  $\mu(x)$ .

The second ingredient we need is a prior distribution. Distributions on functions are fairly complex entities: one approach is to use a Gaussian process prior in which we specify the prior covariance between any two function values  $\mu(x)$  and  $\mu(x')$  (Wahba, 1990; Neal, 1996).

Here we take a simpler route: by considering a finite B-spline basis for  $\mu(x)$ , we can instead provide a prior for the coefficients  $\beta$ , and this implicitly defines a prior for  $\mu(x)$ . We choose a Gaussian prior centered at zero

$$
\beta \sim N(0, \tau \Sigma) \tag{8.25}
$$

with the choices of the prior correlation matrix  $\Sigma$  and variance  $\tau$  to be discussed below. The implicit process prior for  $\mu(x)$  is hence Gaussian, with covariance kernel

$$
K(x, x') = \text{cov}[\mu(x), \mu(x')]
$$
  
=  $\tau \cdot h(x)^T \Sigma h(x').$  (8.26)

Image /page/8/Figure/1 description: This is a plot of several green curves representing functions of x, plotted against the x-axis which ranges from 0.0 to 3.0. The y-axis, labeled \"µ(x)\", ranges from -3 to 3. A dashed horizontal line is present at y=0. The curves show various oscillations and trends across the x-axis, with some starting high and decreasing, others starting low and increasing, and many fluctuating in between.

FIGURE 8.3. Smoothing example: Ten draws from the Gaussian prior distribution for the function  $\mu(x)$ .

The posterior distribution for  $\beta$  is also Gaussian, with mean and covariance

$$
E(\beta|\mathbf{Z}) = \left(\mathbf{H}^T \mathbf{H} + \frac{\sigma^2}{\tau} \mathbf{\Sigma}^{-1}\right)^{-1} \mathbf{H}^T \mathbf{y},
$$
  
$$
cov(\beta|\mathbf{Z}) = \left(\mathbf{H}^T \mathbf{H} + \frac{\sigma^2}{\tau} \mathbf{\Sigma}^{-1}\right)^{-1} \sigma^2,
$$
 (8.27)

with the corresponding posterior values for  $\mu(x)$ ,

$$
E(\mu(x)|\mathbf{Z}) = h(x)^T \left(\mathbf{H}^T \mathbf{H} + \frac{\sigma^2}{\tau} \mathbf{\Sigma}^{-1}\right)^{-1} \mathbf{H}^T \mathbf{y},
$$
  

$$
cov[\mu(x), \mu(x')|\mathbf{Z}] = h(x)^T \left(\mathbf{H}^T \mathbf{H} + \frac{\sigma^2}{\tau} \mathbf{\Sigma}^{-1}\right)^{-1} h(x')\sigma^2.
$$
(8.28)

How do we choose the prior correlation matrix  $\Sigma$ ? In some settings the prior can be chosen from subject matter knowledge about the parameters. Here we are willing to say the function  $\mu(x)$  should be smooth, and have guaranteed this by expressing  $\mu$  in a smooth low-dimensional basis of  $B$ splines. Hence we can take the prior correlation matrix to be the identity  $\Sigma = I$ . When the number of basis functions is large, this might not be sufficient, and additional smoothness can be enforced by imposing restrictions on  $\Sigma$ ; this is exactly the case with smoothing splines (Section 5.8.1).

Figure 8.3 shows ten draws from the corresponding prior for  $\mu(x)$ . To generate posterior values of the function  $\mu(x)$ , we generate values  $\beta'$  from its posterior (8.27), giving corresponding posterior value  $\mu'(x) = \sum_{i=1}^{7} \beta'_{j} h_{j}(x)$ . Ten such posterior curves are shown in Figure 8.4. Two different values were used for the prior variance  $\tau$ , 1 and 1000. Notice how similar the right panel looks to the bootstrap distribution in the bottom left panel

269

Image /page/9/Figure/1 description: This image displays two scatter plots side-by-side, each illustrating the relationship between x and mu(x) with different values of tau. The left plot is labeled "τ = 1" and the right plot is labeled "τ = 1000". Both plots show red dots representing data points, a purple line representing a mean function, and multiple green lines representing individual functions. The x-axis in both plots ranges from 0.0 to 3.0, with tick marks at intervals of 0.5. The y-axis in both plots ranges from -1 to 5, with tick marks at intervals of 1. The plots suggest that with a higher tau value (τ = 1000), the individual functions (green lines) are more tightly clustered around the mean function (purple line) compared to the lower tau value (τ = 1), indicating less variability or a smoother fit.

FIGURE 8.4. Smoothing example: Ten draws from the posterior distribution for the function  $\mu(x)$ , for two different values of the prior variance  $\tau$ . The purple curves are the posterior means.

of Figure 8.2 on page 263. This similarity is no accident. As  $\tau \to \infty$ , the posterior distribution (8.27) and the bootstrap distribution (8.7) coincide. On the other hand, for  $\tau = 1$ , the posterior curves  $\mu(x)$  in the left panel of Figure 8.4 are smoother than the bootstrap curves, because we have imposed more prior weight on smoothness.

The distribution (8.25) with  $\tau \to \infty$  is called a *noninformative prior* for θ. In Gaussian models, maximum likelihood and parametric bootstrap analyses tend to agree with Bayesian analyses that use a noninformative prior for the free parameters. These tend to agree, because with a constant prior, the posterior distribution is proportional to the likelihood. This correspondence also extends to the nonparametric case, where the nonparametric bootstrap approximates a noninformative Bayes analysis; Section 8.4 has the details.

We have, however, done some things that are not proper from a Bayesian point of view. We have used a noninformative (constant) prior for  $\sigma^2$  and replaced it with the maximum likelihood estimate  $\hat{\sigma}^2$  in the posterior. A more standard Bayesian analysis would also put a prior on  $\sigma$  (typically  $g(\sigma) \propto 1/\sigma$ , calculate a joint posterior for  $\mu(x)$  and  $\sigma$ , and then integrate out  $\sigma$ , rather than just extract the maximum of the posterior distribution ("MAP" estimate).

# 8.4 Relationship Between the Bootstrap and Bayesian Inference

Consider first a very simple example, in which we observe a single observation z from a normal distribution

$$
z \sim N(\theta, 1). \tag{8.29}
$$

To carry out a Bayesian analysis for  $\theta$ , we need to specify a prior. The most convenient and common choice would be  $\theta \sim N(0, \tau)$  giving posterior distribution

$$
\theta|z \sim N\left(\frac{z}{1+1/\tau}, \frac{1}{1+1/\tau}\right). \tag{8.30}
$$

Now the larger we take  $\tau$ , the more concentrated the posterior becomes around the maximum likelihood estimate  $\hat{\theta} = z$ . In the limit as  $\tau \to \infty$  we obtain a noninformative (constant) prior, and the posterior distribution is

$$
\theta|z \sim N(z, 1). \tag{8.31}
$$

This is the same as a parametric bootstrap distribution in which we generate bootstrap values  $z^*$  from the maximum likelihood estimate of the sampling density  $N(z, 1)$ .

There are three ingredients that make this correspondence work:

- 1. The choice of noninformative prior for  $\theta$ .
- 2. The dependence of the log-likelihood  $\ell(\theta; \mathbf{Z})$  on the data **Z** only through the maximum likelihood estimate  $\hat{\theta}$ . Hence we can write the log-likelihood as  $\ell(\theta; \hat{\theta})$ .
- 3. The symmetry of the log-likelihood in  $\theta$  and  $\hat{\theta}$ , that is,  $\ell(\theta; \hat{\theta}) =$  $\ell(\hat{\theta};\theta) + \text{constant}.$

Properties (2) and (3) essentially only hold for the Gaussian distribution. However, they also hold approximately for the multinomial distribution, leading to a correspondence between the nonparametric bootstrap and Bayes inference, which we outline next.

Assume that we have a discrete sample space with  $L$  categories. Let  $w_i$  be the probability that a sample point falls in category j, and  $\hat{w}_j$  the observed proportion in category j. Let  $w = (w_1, w_2, \ldots, w_L), \hat{w} = (\hat{w}_1, \hat{w}_2, \ldots, \hat{w}_L).$ Denote our estimator by  $S(\hat{w})$ ; take as a prior distribution for w a symmetric Dirichlet distribution with parameter a:

$$
w \sim \text{Di}_{L}(a1),\tag{8.32}
$$

that is, the prior probability mass function is proportional to  $\prod_{\ell=1}^{L} w_{\ell}^{a-1}$ . Then the posterior density of  $w$  is

$$
w \sim \text{Di}_{L}(a1 + N\hat{w}),\tag{8.33}
$$

where N is the sample size. Letting  $a \to 0$  to obtain a noninformative prior gives

$$
w \sim \text{Di}_{L}(N\hat{w}).\tag{8.34}
$$

Now the bootstrap distribution, obtained by sampling with replacement from the data, can be expressed as sampling the category proportions from a multinomial distribution. Specifically,

$$
N\hat{w}^* \sim \text{Mult}(N, \hat{w}),\tag{8.35}
$$

where  $Mult(N, \hat{w})$  denotes a multinomial distribution, having probability mass function  $\binom{N}{N\hat{w}_1^*,\dots,N\hat{w}_L^*} \prod \hat{w}_{\ell}^{N\hat{w}_{\ell}^*}$ . This distribution is similar to the posterior distribution above, having the same support, same mean, and nearly the same covariance matrix. Hence the bootstrap distribution of  $S(\hat{w}^*)$  will closely approximate the posterior distribution of  $S(w)$ .

In this sense, the bootstrap distribution represents an (approximate) nonparametric, noninformative posterior distribution for our parameter. But this bootstrap distribution is obtained painlessly—without having to formally specify a prior and without having to sample from the posterior distribution. Hence we might think of the bootstrap distribution as a "poor man's" Bayes posterior. By perturbing the data, the bootstrap approximates the Bayesian effect of perturbing the parameters, and is typically much simpler to carry out.

# 8.5 The EM Algorithm

The EM algorithm is a popular tool for simplifying difficult maximum likelihood problems. We first describe it in the context of a simple mixture model.

### 8.5.1 Two-Component Mixture Model

In this section we describe a simple mixture model for density estimation, and the associated EM algorithm for carrying out maximum likelihood estimation. This has a natural connection to Gibbs sampling methods for Bayesian inference. Mixture models are discussed and demonstrated in several other parts of the book, in particular Sections 6.8, 12.7 and 13.2.3.

The left panel of Figure 8.5 shows a histogram of the 20 fictitious data points in Table 8.1.

Image /page/12/Figure/1 description: The image displays two plots side-by-side. The left plot is a histogram with red bars, showing data points distributed across the x-axis labeled 'y'. The y-axis is labeled with values from 0.0 to 1.0. The bars in the histogram are of varying heights, indicating different frequencies. The right plot has 'y' on the x-axis and 'density' on the y-axis, also ranging from 0.0 to 1.0. This plot features a dashed green line with circular markers, which starts at a density of 1.0 and sharply decreases to near 0.0 around y=3, then rises again. A solid red curve, resembling a bell shape, is also present, peaking around y=1.5. Below the x-axis, there are small purple tick marks, and above the x-axis, there are green tick marks corresponding to the data points of the dashed green line.

FIGURE 8.5. Mixture example. (Left panel:) Histogram of data. (Right panel:) Maximum likelihood fit of Gaussian densities (solid red) and responsibility (dotted green) of the left component density for observation y, as a function of y.

TABLE 8.1. Twenty fictitious data points used in the two-component mixture example in Figure 8.5.

| $-0.39$ $0.12$ $0.94$ $1.67$ $1.76$ $2.44$ $3.72$ $4.28$ $4.92$ $5.53$ |  |  |  |  |  |
|------------------------------------------------------------------------|--|--|--|--|--|
| $0.06$ $0.48$ $1.01$ $1.68$ $1.80$ $3.25$ $4.12$ $4.60$ $5.28$ $6.22$  |  |  |  |  |  |

We would like to model the density of the data points, and due to the apparent bi-modality, a Gaussian distribution would not be appropriate. There seems to be two separate underlying regimes, so instead we model Y as a mixture of two normal distributions:

$$
Y_1 \sim N(\mu_1, \sigma_1^2), Y_2 \sim N(\mu_2, \sigma_2^2), Y = (1 - \Delta) \cdot Y_1 + \Delta \cdot Y_2,
$$
 (8.36)

where  $\Delta \in \{0,1\}$  with  $Pr(\Delta = 1) = \pi$ . This generative representation is explicit: generate a  $\Delta \in \{0,1\}$  with probability  $\pi$ , and then depending on the outcome, deliver either  $Y_1$  or  $Y_2$ . Let  $\phi_{\theta}(x)$  denote the normal density with parameters  $\theta = (\mu, \sigma^2)$ . Then the density of Y is

$$
g_Y(y) = (1 - \pi)\phi_{\theta_1}(y) + \pi\phi_{\theta_2}(y). \tag{8.37}
$$

Now suppose we wish to fit this model to the data in Figure 8.5 by maximum likelihood. The parameters are

$$
\theta = (\pi, \theta_1, \theta_2) = (\pi, \mu_1, \sigma_1^2, \mu_2, \sigma_2^2). \tag{8.38}
$$

The log-likelihood based on the  $N$  training cases is

$$
\ell(\theta; \mathbf{Z}) = \sum_{i=1}^{N} \log[(1-\pi)\phi_{\theta_1}(y_i) + \pi\phi_{\theta_2}(y_i)].
$$
\n(8.39)

Direct maximization of  $\ell(\theta; \mathbf{Z})$  is quite difficult numerically, because of the sum of terms inside the logarithm. There is, however, a simpler approach. We consider unobserved latent variables  $\Delta_i$  taking values 0 or 1 as in (8.36): if  $\Delta_i = 1$  then  $Y_i$  comes from model 2, otherwise it comes from model 1. Suppose we knew the values of the  $\Delta_i$ 's. Then the log-likelihood would be

$$
\ell_0(\theta; \mathbf{Z}, \Delta) = \sum_{i=1}^N \left[ (1 - \Delta_i) \log \phi_{\theta_1}(y_i) + \Delta_i \log \phi_{\theta_2}(y_i) \right] + \sum_{i=1}^N \left[ (1 - \Delta_i) \log (1 - \pi) + \Delta_i \log \pi \right], \quad (8.40)
$$

and the maximum likelihood estimates of  $\mu_1$  and  $\sigma_1^2$  would be the sample mean and variance for those data with  $\Delta_i = 0$ , and similarly those for  $\mu_2$ and  $\sigma_2^2$  would be the sample mean and variance of the data with  $\Delta_i = 1$ . The estimate of  $\pi$  would be the proportion of  $\Delta_i = 1$ .

Since the values of the  $\Delta_i$ 's are actually unknown, we proceed in an iterative fashion, substituting for each  $\Delta_i$  in (8.40) its expected value

$$
\gamma_i(\theta) = \mathcal{E}(\Delta_i|\theta, \mathbf{Z}) = \Pr(\Delta_i = 1|\theta, \mathbf{Z}),\tag{8.41}
$$

also called the *responsibility* of model 2 for observation  $i$ . We use a procedure called the EM algorithm, given in Algorithm 8.1 for the special case of Gaussian mixtures. In the *expectation* step, we do a soft assignment of each observation to each model: the current estimates of the parameters are used to assign responsibilities according to the relative density of the training points under each model. In the maximization step, these responsibilities are used in weighted maximum-likelihood fits to update the estimates of the parameters.

A good way to construct initial guesses for  $\hat{\mu}_1$  and  $\hat{\mu}_2$  is simply to choose two of the  $y_i$  at random. Both  $\hat{\sigma}_1^2$  and  $\hat{\sigma}_2^2$  can be set equal to the overall sample variance  $\sum_{i=1}^{N} (y_i - \bar{y})^2/N$ . The mixing proportion  $\hat{\pi}$  can be started at the value 0.5.

Note that the actual maximizer of the likelihood occurs when we put a spike of infinite height at any one data point, that is,  $\hat{\mu}_1 = y_i$  for some i and  $\hat{\sigma}_1^2 = 0$ . This gives infinite likelihood, but is not a useful solution. Hence we are actually looking for a good local maximum of the likelihood, one for which  $\hat{\sigma}_1^2, \hat{\sigma}_2^2 > 0$ . To further complicate matters, there can be more than one local maximum having  $\hat{\sigma}_1^2, \hat{\sigma}_2^2 > 0$ . In our example, we ran the EM algorithm with a number of different initial guesses for the parameters, all having  $\hat{\sigma}_k^2 > 0.5$ , and chose the run that gave us the highest maximized likelihood. Figure 8.6 shows the progress of the EM algorithm in maximizing the log-likelihood. Table 8.2 shows  $\hat{\pi} = \sum_i \hat{\gamma}_i/N$ , the maximum likelihood estimate of the proportion of observations in class 2, at selected iterations of the EM procedure.

Algorithm 8.1 EM Algorithm for Two-component Gaussian Mixture.

1. Take initial guesses for the parameters  $\hat{\mu}_1$ ,  $\hat{\sigma}_1^2$ ,  $\hat{\mu}_2$ ,  $\hat{\sigma}_2^2$ ,  $\hat{\pi}$  (see text).

2. Expectation Step: compute the responsibilities

$$
\hat{\gamma}_i = \frac{\hat{\pi}\phi_{\hat{\theta}_2}(y_i)}{(1-\hat{\pi})\phi_{\hat{\theta}_1}(y_i) + \hat{\pi}\phi_{\hat{\theta}_2}(y_i)}, \quad i = 1, 2, \dots, N. \tag{8.42}
$$

3. Maximization Step: compute the weighted means and variances:

$$
\hat{\mu}_1 = \frac{\sum_{i=1}^N (1 - \hat{\gamma}_i) y_i}{\sum_{i=1}^N (1 - \hat{\gamma}_i)}, \qquad \hat{\sigma}_1^2 = \frac{\sum_{i=1}^N (1 - \hat{\gamma}_i) (y_i - \hat{\mu}_1)^2}{\sum_{i=1}^N (1 - \hat{\gamma}_i)},
$$
$$
\hat{\mu}_2 = \frac{\sum_{i=1}^N \hat{\gamma}_i y_i}{\sum_{i=1}^N \hat{\gamma}_i}, \qquad \hat{\sigma}_2^2 = \frac{\sum_{i=1}^N \hat{\gamma}_i (y_i - \hat{\mu}_2)^2}{\sum_{i=1}^N \hat{\gamma}_i},
$$

and the mixing probability  $\hat{\pi} = \sum_{i=1}^{N} \hat{\gamma}_i / N$ .

 $\overline{a}$ 

4. Iterate steps 2 and 3 until convergence.

TABLE 8.2. Selected iterations of the EM algorithm for mixture example.

| Iteration | $\hat{\pi}$ |
|-----------|-------------|
| 1         | 0.485       |
| 5         | 0.493       |
| 10        | 0.523       |
| 15        | 0.544       |
| 20        | 0.546       |

The final maximum likelihood estimates are

$$
\hat{\mu}_1 = 4.62, \n\hat{\mu}_2 = 1.06, \n\hat{\pi} = 0.546.
$$
\n
$$
\hat{\sigma}_2^2 = 0.77, \n\hat{\pi} = 0.546.
$$

The right panel of Figure 8.5 shows the estimated Gaussian mixture density from this procedure (solid red curve), along with the responsibilities (dotted green curve). Note that mixtures are also useful for supervised learning; in Section 6.7 we show how the Gaussian mixture model leads to a version of radial basis functions.

Image /page/15/Figure/1 description: The image is a line graph showing the observed data log-likelihood on the y-axis, ranging from -44 to -39, plotted against the iteration number on the x-axis, ranging from 0 to 20. The line starts at approximately -44.2 at iteration 1, increases sharply to -41.8 at iteration 2, and then continues to increase gradually, reaching -39.3 at iteration 10. From iteration 10 onwards, the line plateaus, remaining at approximately -39.3 until iteration 20. The points on the line are marked with green circles.

FIGURE 8.6. EM algorithm: observed data log-likelihood as a function of the iteration number.

### 8.5.2 The EM Algorithm in General

Image /page/15/Picture/4 description: A yellow, cartoonish depiction of Edvard Munch's "The Scream" painting. The figure has wide, staring eyes, an open mouth, and is holding its hands to its head. The background is black.

The above procedure is an example of the EM (or Baum–Welch) algorithm for maximizing likelihoods in certain classes of problems. These problems are ones for which maximization of the likelihood is difficult, but made easier by enlarging the sample with latent (unobserved) data. This is called data augmentation. Here the latent data are the model memberships  $\Delta_i$ . In other problems, the latent data are actual data that should have been observed but are missing.

Algorithm 8.2 gives the general formulation of the EM algorithm. Our observed data is **Z**, having log-likelihood  $\ell(\theta; \mathbf{Z})$  depending on parameters θ. The latent or missing data is  $\mathbf{Z}^m$ , so that the complete data is  $\mathbf{T} =$  $(\mathbf{Z}, \mathbf{Z}^m)$  with log-likelihood  $\ell_0(\theta; \mathbf{T})$ ,  $\ell_0$  based on the complete density. In the mixture problem  $(\mathbf{Z}, \mathbf{Z}^m) = (\mathbf{y}, \Delta)$ , and  $\ell_0(\theta; \mathbf{T})$  is given in (8.40).

In our mixture example,  $E(\ell_0(\theta';\mathbf{T})|\mathbf{Z},\hat{\theta}^{(j)})$  is simply (8.40) with the  $\Delta_i$ replaced by the responsibilities  $\hat{\gamma}_i(\hat{\theta})$ , and the maximizers in step 3 are just weighted means and variances.

We now give an explanation of why the EM algorithm works in general. Since

$$
\Pr(\mathbf{Z}^m|\mathbf{Z},\theta') = \frac{\Pr(\mathbf{Z}^m,\mathbf{Z}|\theta')}{\Pr(\mathbf{Z}|\theta')},\tag{8.44}
$$

we can write

$$
Pr(\mathbf{Z}|\theta') = \frac{Pr(\mathbf{T}|\theta')}{Pr(\mathbf{Z}^m|\mathbf{Z}, \theta')}.
$$
\n(8.45)

In terms of log-likelihoods, we have  $\ell(\theta'; \mathbf{Z}) = \ell_0(\theta'; \mathbf{T}) - \ell_1(\theta'; \mathbf{Z}^m | \mathbf{Z})$ , where  $\ell_1$  is based on the conditional density  $Pr(\mathbf{Z}^m|\mathbf{Z},\theta')$ . Taking conditional expectations with respect to the distribution of  $T/Z$  governed by parameter  $\theta$  gives

$$
\ell(\theta';\mathbf{Z}) = \mathbb{E}[\ell_0(\theta';\mathbf{T})|\mathbf{Z},\theta] - \mathbb{E}[\ell_1(\theta';\mathbf{Z}^m|\mathbf{Z})|\mathbf{Z},\theta]
$$

Algorithm 8.2 The EM Algorithm.

- 1. Start with initial guesses for the parameters  $\hat{\theta}^{(0)}$ .
- 2. Expectation Step: at the jth step, compute

$$
Q(\theta', \hat{\theta}^{(j)}) = \mathcal{E}(\ell_0(\theta'; \mathbf{T}) | \mathbf{Z}, \hat{\theta}^{(j)})
$$
\n(8.43)

as a function of the dummy argument  $\theta'$ .

- 3. Maximization Step: determine the new estimate  $\hat{\theta}^{(j+1)}$  as the maximizer of  $Q(\theta', \hat{\theta}^{(j)})$  over  $\theta'$ .
- 4. Iterate steps 2 and 3 until convergence.

$$
\equiv Q(\theta', \theta) - R(\theta', \theta). \tag{8.46}
$$

In the M step, the EM algorithm maximizes  $Q(\theta', \theta)$  over  $\theta'$ , rather than the actual objective function  $\ell(\theta';\mathbf{Z})$ . Why does it succeed in maximizing  $\ell(\theta';\mathbf{Z})$ ? Note that  $R(\theta^*,\theta)$  is the expectation of a log-likelihood of a density (indexed by  $\theta^*$ ), with respect to the same density indexed by  $\theta$ , and hence (by Jensen's inequality) is maximized as a function of  $\theta^*$ , when  $\theta^* = \theta$  (see Exercise 8.1). So if  $\theta'$  maximizes  $Q(\theta', \theta)$ , we see that

$$
\ell(\theta'; \mathbf{Z}) - \ell(\theta; \mathbf{Z}) = [Q(\theta', \theta) - Q(\theta, \theta)] - [R(\theta', \theta) - R(\theta, \theta)]
$$
  
\n
$$
\geq 0.
$$
 (8.47)

Hence the EM iteration never decreases the log-likelihood.

This argument also makes it clear that a full maximization in the M step is not necessary: we need only to find a value  $\hat{\theta}^{(j+1)}$  so that  $Q(\theta', \hat{\theta}^{(j)})$ increases as a function of the first argument, that is,  $Q(\hat{\theta}^{(j+1)}, \hat{\theta}^{(j)}) >$  $Q(\hat{\theta}^{(j)}, \hat{\theta}^{(j)})$ . Such procedures are called *GEM (generalized EM)* algorithms. The EM algorithm can also be viewed as a minorization procedure: see Exercise 8.7.

### 8.5.3 EM as a Maximization –Maximization Procedure

Image /page/16/Picture/14 description: A yellow, cartoonish face with wide, shocked eyes and an open mouth is depicted. The figure's hands are pressed against its temples, and it appears to be screaming or in distress. The background is white.

Here is a different view of the EM procedure, as a joint maximization algorithm. Consider the function

$$
F(\theta', \tilde{P}) = \mathcal{E}_{\tilde{P}}[\ell_0(\theta'; \mathbf{T})] - \mathcal{E}_{\tilde{P}}[\log \tilde{P}(\mathbf{Z}^m)].
$$
\n(8.48)

Here  $\tilde{P}(\mathbf{Z}^m)$  is any distribution over the latent data  $\mathbf{Z}^m$ . In the mixture example,  $\tilde{P}(\mathbf{Z}^m)$  comprises the set of probabilities  $\gamma_i = \Pr(\Delta_i = 1 | \theta, \mathbf{Z}).$ Note that F evaluated at  $\tilde{P}(\mathbf{Z}^m) = \Pr(\mathbf{Z}^m | \mathbf{Z}, \theta')$ , is the log-likelihood of

Image /page/17/Figure/1 description: This is a contour plot illustrating the EM algorithm. The x-axis represents 'Latent Data Parameters' ranging from 1 to 5, and the y-axis represents 'Model Parameters' ranging from 0 to 4. Several concentric ellipses, representing contour lines, are plotted, with labels indicating values of 0.1, 0.3, 0.5, 0.7, and 0.9. A red line passes diagonally through the plot. Two sets of arrows, labeled 'E' (blue) and 'M' (green), demonstrate steps in the EM algorithm. One 'E' arrow extends horizontally from approximately (1.5, 1) to (2.5, 1). A subsequent 'M' arrow extends vertically from (2.5, 1) to (2.5, 1.75). Another 'E' arrow extends horizontally from approximately (2.5, 1.75) to (2.75, 1.75). A final 'M' arrow extends vertically from (2.75, 1.75) to (2.75, 2.25).

FIGURE 8.7. Maximization–maximization view of the EM algorithm. Shown are the contours of the (augmented) observed data log-likelihood  $F(\theta', \tilde{P})$ . The  $E$  step is equivalent to maximizing the log-likelihood over the parameters of the latent data distribution. The M step maximizes it over the parameters of the log-likelihood. The red curve corresponds to the observed data log-likelihood, a profile obtained by maximizing  $F(\theta', \tilde{P})$  for each value of  $\theta'$ .

the observed data, from  $(8.46)^1$ . The function F expands the domain of the log-likelihood, to facilitate its maximization.

The EM algorithm can be viewed as a joint maximization method for F over  $\theta'$  and  $\tilde{P}(\mathbf{Z}^m)$ , by fixing one argument and maximizing over the other. The maximizer over  $\tilde{P}(\mathbf{Z}^m)$  for fixed  $\theta'$  can be shown to be

$$
\tilde{P}(\mathbf{Z}^m) = \Pr(\mathbf{Z}^m | \mathbf{Z}, \theta') \tag{8.49}
$$

(Exercise 8.2). This is the distribution computed by the  $E$  step, for example, (8.42) in the mixture example. In the M step, we maximize  $F(\theta', \tilde{P})$  over  $\theta'$ with  $\tilde{P}$  fixed: this is the same as maximizing the first term  $E_{\tilde{P}}[\ell_0(\theta';\mathbf{T})|\mathbf{Z},\theta]$ since the second term does not involve  $\theta'$ .

Finally, since  $F(\theta', \tilde{P})$  and the observed data log-likelihood agree when  $\tilde{P}(\mathbf{Z}^m) = \Pr(\mathbf{Z}^m | \mathbf{Z}, \theta')$ , maximization of the former accomplishes maximization of the latter. Figure 8.7 shows a schematic view of this process. This view of the EM algorithm leads to alternative maximization proce-

<sup>&</sup>lt;sup>1</sup> (8.46) holds for all  $\theta$ , including  $\theta = \theta'$ .
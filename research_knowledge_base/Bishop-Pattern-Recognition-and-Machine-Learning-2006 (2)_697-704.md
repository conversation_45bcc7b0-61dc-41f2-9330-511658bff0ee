## **Appendix A. Data Sets**

In this appendix, we give a brief introduction to the data sets used to illustrate some of the algorithms described in this book. Detailed information on file formats for these data sets, as well as the data files themselves, can be obtained from the book web site:

http://research.microsoft.com/∼cmbishop/PRML

### **Handwritten Digits**

The digits data used in this book is taken from the MNIST data set (<PERSON><PERSON><PERSON> *et al.*, 1998), which itself was constructed by modifying a subset of the much larger data set produced by NIST (the National Institute of Standards and Technology). It comprises a training set of 60, 000 examples and a test set of 10, 000 examples. Some of the data was collected from Census Bureau employees and the rest was collected from high-school children, and care was taken to ensure that the test examples were written by different individuals to the training examples.

The original NIST data had binary (black or white) pixels. To create MNIST, these images were size normalized to fit in a  $20 \times 20$  pixel box while preserving their aspect ratio. As a consequence of the anti-aliasing used to change the resolution of the images, the resulting MNIST digits are grey scale. These images were then centred in a  $28 \times 28$  box. Examples of the MNIST digits are shown in Figure A.1.

Error rates for classifying the digits range from 12% for a simple linear classifier, through 0.56% for a carefully designed support vector machine, to 0.4% for a convolutional neural network (<PERSON><PERSON><PERSON> *et al.*, 1998).

## **A. DATA SETS**

**Figure A.1** One hundred examples of the MNIST digits chosen at random from the training set.

Image /page/1/Picture/2 description: The image displays a grid of handwritten digits in blue ink on a white background. The grid is organized into 10 rows and 10 columns, with each cell containing a single digit. The digits appear to be from a dataset like MNIST, with variations in handwriting style and clarity. The digits are arranged as follows: Row 1: 7 2 1 0 4 1 4 9 5 9. Row 2: 0 6 9 0 1 5 9 7 8 4. Row 3: 9 6 6 5 4 0 7 4 0 1. Row 4: 3 1 3 4 7 2 7 1 2 1. Row 5: 1 7 4 2 3 5 1 2 4 4. Row 6: 6 3 5 5 6 0 4 1 9 5. Row 7: 7 8 9 3 7 4 6 4 3 0. Row 8: 7 0 2 9 1 7 3 2 9 7. Row 9: 7 6 2 7 8 4 7 3 6 1. Row 10: 3 6 9 3 1 4 1 7 6 9.

### **Oil Flow**

This is a synthetic data set that arose out of a project aimed at measuring noninvasively the proportions of oil, water, and gas in North Sea oil transfer pipelines (Bishop and James, 1993). It is based on the principle of *dual-energy gamma densitometry*. The ideas is that if a narrow beam of gamma rays is passed through the pipe, the attenuation in the intensity of the beam provides information about the density of material along its path. Thus, for instance, the beam will be attenuated more strongly by oil than by gas.

A single attenuation measurement alone is not sufficient because there are two degrees of freedom corresponding to the fraction of oil and the fraction of water (the fraction of gas is redundant because the three fractions must add to one). To address this, two gamma beams of different energies (in other words different frequencies or wavelengths) are passed through the pipe along the same path, and the attenuation of each is measured. Because the absorbtion properties of different materials vary differently as a function of energy, measurement of the attenuations at the two energies provides two independent pieces of information. Given the known absorbtion properties of oil, water, and gas at the two energies, it is then a simple matter to calculate the average fractions of oil and water (and hence of gas) measured *along the path* of the gamma beams.

There is a further complication, however, associated with the motion of the materials along the pipe. If the flow velocity is small, then the oil floats on top of the water with the gas sitting above the oil. This is known as a *laminar* or *stratified*

**Figure A.2** The three geometrical configurations of the oil, water, and gas phases used to generate the oilflow data set. For each configuration, the proportions of the three phases can vary.

Image /page/2/Figure/2 description: This image displays three circular diagrams illustrating different flow patterns: Stratified, Annular, and Homogeneous. A legend on the right indicates that black represents Oil, blue represents Water, light green represents Gas, and dark blue represents Mix. The Stratified diagram shows distinct horizontal layers of Gas (top), Oil (middle), and Water (bottom). The Annular diagram shows a central core of Gas, surrounded by a layer of Oil, then a layer of Water, and finally the outer boundary. The Homogeneous diagram shows a uniform mixture of all components, depicted as a single dark blue circle.

flow configuration and is illustrated in Figure A.2. As the flow velocity is increased, more complex geometrical configurations of the oil, water, and gas can arise. For the purposes of this data set, two specific idealizations are considered. In the *annular* configuration the oil, water, and gas form concentric cylinders with the water around the outside and the gas in the centre, whereas in the *homogeneous* configuration the oil, water and gas are assumed to be intimately mixed as might occur at high flow velocities under turbulent conditions. These configurations are also illustrated in Figure A.2.

We have seen that a single dual-energy beam gives the oil and water fractions measured along the path length, whereas we are interested in the volume fractions of oil and water. This can be addressed by using multiple dual-energy gamma densitometers whose beams pass through different regions of the pipe. For this particular data set, there are six such beams, and their spatial arrangement is shown in Figure A.3. A single observation is therefore represented by a 12-dimensional vector comprising the fractions of oil and water measured along the paths of each of the beams. We are, however, interested in obtaining the overall volume fractions of the three phases in the pipe. This is much like the classical problem of tomographic reconstruction, used in medical imaging for example, in which a two-dimensional dis-

Image /page/2/Figure/5 description: Figure A.3 shows a cross-section of a pipe with six beam lines. Each beam line contains a single dual-energy gamma densitometer. The vertical beams are arranged asymmetrically with respect to the central axis, which is indicated by a dotted line.

Image /page/2/Figure/6 description: A red circle is intersected by a grid of blue lines. Three horizontal lines and three vertical lines form a grid. The horizontal lines are parallel to each other and extend to the right with arrows. The vertical lines are parallel to each other and extend downwards with arrows. A dashed vertical line is located to the right of the center of the circle.

tribution is to be reconstructed from an number of one-dimensional averages. Here there are far fewer line measurements than in a typical tomography application. On the other hand the range of geometrical configurations is much more limited, and so the configuration, as well as the phase fractions, can be predicted with reasonable accuracy from the densitometer data.

For safety reasons, the intensity of the gamma beams is kept relatively weak and so to obtain an accurate measurement of the attenuation, the measured beam intensity is integrated over a specific time interval. For a finite integration time, there are random fluctuations in the measured intensity due to the fact that the gamma beams comprise discrete packets of energy called photons. In practice, the integration time is chosen as a compromise between reducing the noise level (which requires a long integration time) and detecting temporal variations in the flow (which requires a short integration time). The oil flow data set is generated using realistic known values for the absorption properties of oil, water, and gas at the two gamma energies used, and with a specific choice of integration time (10 seconds) chosen as characteristic of a typical practical setup.

Each point in the data set is generated independently using the following steps:

- 1. Choose one of the three phase configurations at random with equal probability.
- 2. Choose three random numbers  $f_1$ ,  $f_2$  and  $f_3$  from the uniform distribution over  $(0, 1)$  and define

$$
f_{\text{oil}} = \frac{f_1}{f_1 + f_2 + f_3}, \qquad f_{\text{water}} = \frac{f_2}{f_1 + f_2 + f_3}.
$$
 (A.1)

This treats the three phases on an equal footing and ensures that the volume fractions add to one.

- 3. For each of the six beam lines, calculate the effective path lengths through oil and water for the given phase configuration.
- 4. Perturb the path lengths using the Poisson distribution based on the known beam intensities and integration time to allow for the effect of photon statistics.

Each point in the data set comprises the 12 path length measurements, together with the fractions of oil and water and a binary label describing the phase configuration. The data set is divided into training, validation, and test sets, each of which comprises 1, 000 independent data points. Details of the data format are available from the book web site.

In Bishop and James (1993), statistical machine learning techniques were used to predict the volume fractions and also the geometrical configuration of the phases shown in Figure A.2, from the 12-dimensional vector of measurements. The 12 dimensional observation vectors can also be used to test data visualization algorithms.

This data set has a rich and interesting structure, as follows. For any given configuration there are two degrees of freedom corresponding to the fractions of oil and water, and so for infinite integration time the data will locally live on a twodimensional manifold. For a finite integration time, the individual data points will be perturbed away from the manifold by the photon noise. In the homogeneous phase configuration, the path lengths in oil and water are linearly related to the fractions of oil and water, and so the data points lie close to a linear manifold. For the annular configuration, the relationship between phase fraction and path length is nonlinear and so the manifold will be nonlinear. In the case of the laminar configuration the situation is even more complex because small variations in the phase fractions can cause one of the horizontal phase boundaries to move across one of the horizontal beam lines leading to a discontinuous jump in the 12-dimensional observation space. In this way, the two-dimensional nonlinear manifold for the laminar configuration is broken into six distinct segments. Note also that some of the manifolds for different phase configurations meet at specific points, for example if the pipe is filled entirely with oil, it corresponds to specific instances of the laminar, annular, and homogeneous configurations.

### **Old Faithful**

Old Faithful, shown in Figure A.4, is a hydrothermal geyser in Yellowstone National Park in the state of Wyoming, U.S.A., and is a popular tourist attraction. Its name stems from the supposed regularity of its eruptions.

The data set comprises 272 observations, each of which represents a single eruption and contains two variables corresponding to the duration in minutes of the eruption, and the time until the next eruption, also in minutes. Figure A.5 shows a plot of the time to the next eruption versus the duration of the eruptions. It can be seen that the time to the next eruption varies considerably, although knowledge of the duration of the current eruption allows it to be predicted more accurately. Note that there exist several other data sets relating to the eruptions of Old Faithful.

**Figure A.4** The Old Faithful geyser in Yellowstone National Park. ©Bruce T. Gourley www.brucegourley.com.

Image /page/4/Picture/6 description: A geyser erupts in a snowy landscape with a small building and a lone tree visible in the distance under a bright blue sky.

## **A. DATA SETS**

**Figure A.5** Plot of the time to the next eruption in minutes (vertical axis) versus the duration of the eruption in minutes (horizontal axis) for the Old Faithful data set.

Image /page/5/Figure/2 description: A scatter plot shows two clusters of green circles. The x-axis ranges from 1 to 6, and the y-axis ranges from 40 to 100. The left cluster of points is centered around x=2 and y=55, with points ranging from approximately x=1.5 to x=2.5 and y=45 to y=65. The right cluster of points is centered around x=4.5 and y=80, with points ranging from approximately x=3.5 to x=5.5 and y=70 to y=95. There is a gap between the two clusters, with a few scattered points between x=2.5 and x=3.5.

### **Synthetic Data**

Throughout the book, we use two simple synthetic data sets to illustrate many of the algorithms. The first of these is a regression problem, based on the sinusoidal function, shown in Figure A.6. The input values  $\{x_n\}$  are generated uniformly in range  $(0, 1)$ , and the corresponding target values  $\{t_n\}$  are obtained by first computing the corresponding values of the function  $sin(2\pi x)$ , and then adding random noise with a Gaussian distribution having standard deviation 0.3. Various forms of this data set, having different numbers of data points, are used in the book.

The second data set is a classification problem having two classes, with equal prior probabilities, and is shown in Figure A.7. The blue class is generated from a single Gaussian while the red class comes from a mixture of two Gaussians. Because we know the class priors and the class-conditional densities, it is straightforward to evaluate and plot the true posterior probabilities as well as the minimum misclassification-rate decision boundary, as shown in Figure A.7.

Image /page/6/Figure/1 description: The image contains two plots side-by-side. Both plots have 'x' on the horizontal axis and 't' on the vertical axis, with values ranging from 0 to 1 on the horizontal axis and -1 to 1 on the vertical axis. The left plot shows several blue circles representing data points scattered around a green curve that resembles a sine wave. The right plot shows a similar green curve, but it is shaded with a light pink color, indicating a confidence interval or uncertainty band around the curve.

**Figure A.6** The left-hand plot shows the synthetic regression data set along with the underlying sinusoidal function from which the data points were generated. The right-hand plot shows the true conditional distribution  $p(t|x)$  from which the labels are generated, in which the green curve denotes the mean, and the shaded region spans one standard deviation on each side of the mean.

Image /page/6/Figure/3 description: The image displays two scatter plots side-by-side. The left plot shows blue circles and red crosses scattered across a 2D plane with axes labeled from -2 to 2. Green lines indicate decision boundaries. The right plot shows a color-coded heatmap with blue and red regions, also with axes labeled from -2 to 2, and green lines representing decision boundaries. The heatmap visually represents the classification regions corresponding to the data points in the left plot.

**Figure A.7** The left plot shows the synthetic classification data set with data from the two classes shown in red and blue. On the right is a plot of the true posterior probabilities, shown on a colour scale going from pure red denoting probability of the red class is 1 to pure blue denoting probability of the red class is 0. Because these probabilities are known, the optimal decision boundary for minimizing the misclassification rate (which corresponds to the contour along which the posterior probabilities for each class equal 0.5) can be evaluated and is shown by the green curve. This decision boundary is also plotted on the left-hand figure.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
## The Jacobian equation

Transport is but a change of variables, and in many problems involving changes of variables, it is useful to write the Jacobian equation

$$
f(x) = g(T(x)) \mathcal{J}_T(x),
$$

where  $f$  and  $g$  are the respective densities of the probability measures  $\mu$  and  $\nu$  with respect to the volume measure (in  $\mathbb{R}^n$ , the Le<PERSON>gue measure), and  $\mathcal{J}_T(x)$  is the absolute value of the Jacobian determinant associated with T:

$$
\mathcal{J}_T(x) = |\det(\nabla T(x))| = \lim_{r \to 0} \frac{\text{vol}[T(B_r(x))]}{\text{vol}[B_r(x)]}.
$$

There are two important things that one should check before writing the Jacobian equation: First,  $T$  should be *injective* on its domain of definition; secondly, it should possess some minimal regularity.

So how smooth should T be for the Jacobian equation to hold true? We learn in elementary school that it is sufficient for  $T$  to be continuously differentiable, and a bit later that it is actually enough to have  $T$ Lipschitz continuous. But that degree of regularity is not always available in optimal transport! As we shall see in Chapter 12, the transport map T might fail to be even continuous.

There are (at least) three ways out of this situation:

(i) Only use the Jacobian equation in situations where the optimal map is smooth. Such situations are rare; this will be discussed in Chapter 12.

(ii) Only use the Jacobian equation for the optimal map between  $\mu_{t_0}$  and  $\mu_t$ , where  $(\mu_t)_{0 \leq t \leq 1}$  is a compactly supported displacement interpolation, and  $t_0$  is fixed in  $(0, 1)$ . Then, according to Theorem 8.5, the transport map is essentially Lipschitz. This is the strategy that I shall use in most of this course.

(iii) Apply a more sophisticated theorem of change of variables, covering for instance changes of variables with bounded variation (possibly discontinuous). It is in fact sufficient that the map  $T$  be differentiable almost everywhere, or even just approximately differentiable almost everywhere, in the sense of Definition 10.2. Such a theorem is stated below without proof; I shall use it in Chapter 23. The volume measure on M will be denoted by vol.

**Theorem 11.1 (Jacobian equation).** Let M be a Riemannian manifold, let  $f \in L^1(M)$  be a nonnegative integrable function on M, and let  $T : M \to M$  be a Borel map. Define  $\mu(dx) = f(x) \text{ vol}(dx)$  and  $\nu := T_{\#}\mu$ . Assume that:

(i) There exists a measurable set  $\Sigma \subset M$ , such that  $f = 0$  almost everywhere outside of  $\Sigma$ , and T is injective on  $\Sigma$ ;

(ii) T is approximately differentiable almost everywhere on  $\Sigma$ . Let  $\nabla T$  be the approximate gradient of T, and let  $\mathcal{J}_T$  be defined almost everywhere on  $\Sigma$  by the equation  $\mathcal{J}_T(x) := |\det(\nabla T(x))|$ . Then  $\nu$  is absolutely continuous with respect to the volume measure if and only if  $\mathcal{J}_T > 0$  almost everywhere. In that case  $\nu$  is concentrated on  $T(\Sigma)$ , and its density is determined by the equation

$$
f(x) = g(T(x)) \mathcal{J}_T(x). \tag{11.1}
$$

In an informal writing:

$$
\frac{d(T^{-1})_{\#}(g \text{ vol})}{d \text{vol}} = \mathcal{J}_T(g \circ T) \text{ vol}.
$$

Theorem 11.1 establishes the Jacobian equation as soon as, say, the optimal transport has locally bounded variation. Indeed, in this case the map  $T$  is almost everywhere differentiable, and its gradient coincides with the absolutely continuous part of the distributional gradient  $\nabla_{\mathcal{D}'}T$ . The property of bounded variation is obviously satisfied for the quadratic cost in Euclidean space, since the second derivative of a convex function is a nonnegative measure.

**Example 11.2.** Consider two probability measures  $\mu_0$  and  $\mu_1$  on  $\mathbb{R}^n$ , with finite second moments; assume that  $\mu_0$  and  $\mu_1$  are absolutely continuous with respect to the Lebesgue measure, with respective densities  $f_0$  and  $f_1$ . Under these assumptions there exists a unique optimal transport map between  $\mu_0$  and  $\mu_1$ , and it takes the form  $T(x) = \nabla \Psi(x)$  for some lower semicontinuous convex function  $\Psi$ . There is a unique displacement interpolation  $(\mu_t)_{0 \leq t \leq 1}$ , and it is defined by

$$
\mu_t = (T_t)_{\#}\mu_0,
$$
\n $T_t(x) = (1-t)x + tT(x) = (1-t)x + t\nabla\Psi(x).$ 

By Theorem 8.7, each  $\mu_t$  is absolutely continuous, so let  $f_t$  be its density. The map  $\nabla T$  is of locally bounded variation, and it is differentiable almost everywhere, with Jacobian matrix  $\nabla T = \nabla^2 \Psi$ , where  $\nabla^2 \Psi$  is the Alexandrov Hessian of  $\Psi$  (see Theorem 14.25 later in this course). Then, it follows from Theorem 11.1 that,  $\mu_0(dx)$ -almost surely,

$$
f_0(x) = f_1(\nabla \Psi(x)) \, \det(\nabla^2 \Psi(x)).
$$

Also, for any  $t \in [0, 1]$ ,

$$
f_0(x) = f_t(T_t(x)) \det(\nabla T_t(x))
$$
  
=  $f_t((1-t)x + t\nabla\Psi(x)) \det((1-t)I_n + t\nabla^2\Psi(x)).$ 

If  $T_{t_0 \to t} = T_t \circ T_{t_0}^{-1}$  stands for the transport map between  $\mu_{t_0}$  and  $\mu_t$ , then the equation

$$
f_{t_0}(x) = f_t(T_{t_0 \to t}(x)) \ \det(\nabla T_{t_0 \to t}(x))
$$

also holds true for  $t_0 \in (0,1)$ ; but now this is just the theorem of change of variables for Lipschitz maps.

In the sequel of this course, with the noticeable expression of Chapter 23, it will be sufficient to use the following theorem of change of variables.

**Theorem 11.3 (Change of variables).** Let M be a Riemannian manifold, and  $c(x, y)$  a cost function deriving from a  $C<sup>2</sup>$  Lagrangian  $L(x, v, t)$  on TM  $\times$  [0,1], where L satisfies the classical conditions of Definition 7.6, together with  $\nabla_v^2 L > 0$ . Let  $(\mu_t)_{0 \le t \le 1}$  be a displacement  $interpolation, such that each  $\mu_t$  is absolutely continuous and has density$  $f_t$ . Let  $t_0 \in (0,1)$ , and  $t \in [0,1]$ ; further, let  $T_{t_0 \to t}$  be the  $(\mu_{t_0}$ -almost surely) unique optimal transport from  $\mu_{t_0}$  to  $\mu_t$ , and let  $\mathcal{J}_{t_0 \to t}$  be the associated Jacobian determinant. Let F be a nonnegative measurable function on  $M \times \mathbb{R}_+$  such that

290 11 The Jacobian equation

$$
[f_t(y) = 0] \implies F(y, f_t(y)) = 0.
$$

Then,

$$
\int_M F(y, f_t(y)) \operatorname{vol}(dy) = \int_M F\Big(T_{t_0 \to t}(x), \, \frac{f_{t_0}(x)}{\mathcal{J}_{t_0 \to t}(x)}\Big) \, \mathcal{J}_{t_0 \to t}(x) \operatorname{vol}(dx).
$$

Furthermore,  $\mu_{t_0}(dx)$ -almost surely,  $\mathcal{J}_{t_0 \to t}(x) > 0$  for all  $t \in [0,1]$ .

*Proof of Theorem 11.3.* For brevity I shall abbreviate vol( $dx$ ) into just dx. Let us first consider the case when  $(\mu_t)_{0 \leq t \leq 1}$  is compactly supported. Let  $\Pi$  be a probability measure on the set of minimizing curves, such that  $\mu_t = (e_t)_\# \Pi$ . Let  $K_t = e_t(\text{Spt }\Pi)$  and  $K_{t_0} = e_{t_0}(\text{Spt }\Pi)$ . By Theorem 8.5, the map  $\gamma_{t_0} \to \gamma_t$  is well-defined and Lipschitz for all  $\gamma \in \text{Spt } H$ . So  $T_{t_0 \to t}(\gamma_{t_0}) = \gamma_t$  is a Lipschitz map  $K_{t_0} \to K_t$ . By assumption  $\mu_t$  is absolutely continuous, so Theorem 10.28 (applied with the cost function  $c^{t_0,t}(x,y)$ , or maybe  $c^{t,t_0}(x,y)$  if  $t < t_0$ ) guarantees that the coupling  $(\gamma_t, \gamma_{t_0})$  is deterministic, which amounts to saying that  $\gamma_{t_0} \to \gamma_t$  is injective apart from a set of zero probability.

Then we can use the change of variables formula with  $g = 1_{K_t}$ ,  $T = T_{t_0 \to t}$ , and we find  $f(x) = \mathcal{J}_{t_0 \to t}(x)$ . Therefore, for any nonnegative measurable function  $G$  on  $M$ ,

$$
\int_{K_t} G(y) dy = \int_{K_t} G(y) d((T_{t_0 \to t})_{\#} \mu)(y)
$$
$$
= \int_{K_{t_0}} (G \circ T_{t_0 \to t}(x)) f(x) dx
$$
$$
= \int_{K_{t_0}} G(T_{t_0 \to t}(x)) \mathcal{J}_{t_0 \to t}(x) dx.
$$

We can apply this to  $G(y) = F(y, f_t(y))$  and replace  $f_t(T_{t_0 \to t}(x))$  by  $f_{t_0}(x)/\mathcal{J}_{t_0\to t}(x)$ ; this is allowed since in the right-hand side the contribution of those x with  $f_t(T_{t_0\to t}(x)) = 0$  is negligible, and  $\mathcal{J}_{t_0\to t}(x) = 0$ implies (almost surely)  $f_{t_0}(x) = 0$ . So in the end

$$
\int_{K_t} F(y, f_t(y)) dy = \int_{K_{t_0}} F(T_{t_0 \to t}(x), \frac{f_{t_0}(x)}{\mathcal{J}_{t_0 \to t}(x)}) \mathcal{J}_{t_0 \to t}(x) dx.
$$

Since  $f_t(y) = 0$  almost surely outside of  $K_t$  and  $f_{t_0}(x) = 0$  almost surely outside of  $K_{t_0}$ , these two integrals can be extended to the whole of M.

Now it remains to generalize this to the case when  $\Pi$  is not compactly supported. (Skip this bit at first reading.) Let  $(K_{\ell})_{\ell \in \mathbb{N}}$  be a nondecreasing sequence of compact sets, such that  $\Pi[\cup K_\ell] = 1$ . For  $\ell$ large enough,  $\Pi[K_\ell] > 0$ , so we can consider the restriction  $\Pi_\ell$  of  $\Pi$  to  $K_{\ell}$ . Then let  $K_{t,\ell}$  and  $K_{t_0,\ell}$  be the images of  $K_{\ell}$  by  $e_t$  and  $e_{t_0}$ , and of course  $\mu_{t,\ell} = (e_t)_{\#} \Pi_{\ell}, \mu_{t_0,\ell} = (e_{t_0})_{\#} \Pi_{\ell}$ . Since  $\mu_t$  and  $\mu_{t_0}$  are absolutely continuous, so are  $\mu_{t,\ell}$  and  $\mu_{t_0,\ell}$ ; let  $f_{t,\ell}$  and  $f_{t_0,\ell}$  be their respective densities. The optimal map  $T_{t_0\to t,\ell}$  for the transport problem between  $\mu_{t_0,\ell}$  and  $\mu_{t,\ell}$  is obtained as before by the map  $\gamma_{t_0} \to \gamma_t$ , so this is actually the restriction of  $T_{t_0\to t}$  to  $K_{t_0,\ell}$ . Thus we have the Jacobian equation

$$
f_{t_0,\ell}(x) = f_{t,\ell}(T_{t_0 \to t}(x)) \mathcal{J}_{t_0 \to t}(x), \qquad (11.2)
$$

where the Jacobian determinant does not depend on  $\ell$ . This equation holds true almost surely for  $x \in K_{\ell'}$ , as soon as  $\ell' \leq \ell$ , so we may pass to the limit as  $\ell \to \infty$  to get

$$
f_{t_0}(x) = f_t(T_{t_0 \to t}(x)) \mathcal{J}_{t_0 \to t}(x).
$$
 (11.3)

Since this is true almost surely on  $K_{\ell'}$ , for each  $\ell'$ , it is also true almost surely.

Next, for any nonnegative measurable function  $G$ , by monotone convergence and the first part of the proof one has

$$
\int_{UK_{t,\ell}} G(y) dy = \lim_{\ell \to \infty} \int_{K_{t,\ell}} G(y) dy
$$
$$
= \lim_{\ell \to \infty} \int_{K_{t_0,\ell}} G(T_{t_0 \to t}(x)) \mathcal{J}_{t_0 \to t}(x) dx
$$
$$
= \int_{UK_{\ell,t_0}} G(T_{t_0 \to t}(x)) \mathcal{J}_{t_0 \to t}(x) dx.
$$

The conclusion follows as before by choosing  $G(y) = F(y, f_t(x))$  and using the Jacobian equation (11.3), then extending the integrals to the whole of M.

It remains to prove the assertion about  $\mathcal{J}_{t_0\to t}(x)$  being positive for all values of  $t \in [0,1]$ , and not just for  $t = 1$ , or for almost all values of t. The transport map  $T_{t_0\to t}$  can be written  $\gamma(t_0) \to \gamma(t)$ , where  $\gamma$ is a minimizing curve determined uniquely by  $\gamma(t_0)$ . Since  $\gamma$  is minimizing, we know (recall Problem 8.8) that the map  $(\gamma_0, \dot{\gamma}_0) \rightarrow (\gamma_0, \gamma_{t_0})$ is locally invertible. So  $T_{t_0\to t}$  can be written as the composition of the

maps  $F_1 : \gamma(t_0) \to (\gamma(0), \gamma(t_0)), F_2 : (\gamma(0), \gamma(t_0)) \to (\gamma(0), \dot{\gamma}(0))$  and  $F_3 : (\gamma(0), \dot{\gamma}(0)) \rightarrow \gamma(t)$ . Both  $F_2$  and  $F_3$  have a positive Jacobian determinant, at least if  $t < 1$ ; so if x is chosen in such a way that  $F_1$  has a positive Jacobian determinant at x, then also  $T_{t_0 \to t} = F_3 \circ F_2 \circ F_1$ will have a positive Jacobian determinant at x for  $t \in [0, 1)$ .

## Bibliographical notes

Theorem 11.1 can be obtained (in  $\mathbb{R}^n$ ) by combining Lemma 5.5.3 in [30] with Theorem 3.83 in [26].

In the context of optimal transport, the change of variables formula (11.1) was proven by McCann [614]. His argument is based on Lebesgue's density theory, and takes advantage of Alexandrov's theorem, alluded to in this chapter and proven later as Theorem 14.25: A convex function admits a Taylor expansion at order 2 at almost each x in its domain of definition. Since the gradient of a convex function has locally bounded variation, Alexandrov's theorem can be seen essentially as a particular case of the theorem of approximate differentiability of functions with bounded variation. McCann's argument is reproduced in [814, Theorem 4.8].

Along with Cordero-Erausquin and Schmuckenschläger, McCann later generalized his result to the case of Riemannian manifolds [246]. Modulo certain complications, the proof basically follows the same pattern as in  $\mathbb{R}^n$ . Then Cordero-Erausquin [243] treated the case of strictly convex cost functions in  $\mathbb{R}^n$  in a similar way.

Ambrosio pointed out that those results could be retrieved within the general framework of push-forward by approximately differentiable mappings. This point of view has the disadvantage of involving more subtle arguments, but the advantage of showing that it is not a special feature of optimal transport. It also applies to nonsmooth cost functions such as  $|x-y|^p$ . In fact it covers general strictly convex costs of the form  $c(x - y)$  as soon as c has superlinear growth, is  $C<sup>1</sup>$  everywhere and  $C<sup>2</sup>$  out of the origin. A more precise discussion of these subtle issues can be found in [30, Section 6.2.1].

It is a general feature of optimal transport with strictly convex cost in  $\mathbb{R}^n$  that if T stands for the optimal transport map, then the Jacobian matrix  $\nabla T$ , even if not necessarily nonnegative symmetric, is diagonalizable with nonnegative eigenvalues; see Cordero-Erausquin [243] and Ambrosio, Gigli and Savaré [30, Section 6.2]. From an Eulerian perspective, that diagonalizability property was already noticed by Otto [666, Proposition A.4]. I don't know if there is an analog on Riemannian manifolds.

Changes of variables of the form  $y = \exp_x(\nabla \psi(x))$  (where  $\psi$  is not necessarily  $d^2/2$ -convex) have been used in a remarkable paper by Cabré [181] to investigate qualitative properties of nondivergent elliptic equations (Liouville theorem, Alexandrov–Bakelman–Pucci estimates, Krylov–Safonov–Harnack inequality) on Riemannian manifolds with nonnegative sectional curvature. (See for instance [189, 416, 786] for classical proofs in  $\mathbb{R}^n$ .) It is mentioned in [181] that the methods extend to sectional curvature bounded below. For the Harnack inequality, Cabré's method was extended to nonnegative Ricci curvature by S. Kim [516].

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
**Figure 10.7** Plot of the variational lower bound  $\mathcal L$  versus the number  $K$  of components in the Gaussian mixture model, for the Old Faithful data, showing a distinct peak at  $K =$ 2 components. For each value of  $K$ , the model is trained from 100 different random starts, and the results shown as  $+$  symbols plotted with small random horizontal perturbations so that they can be distinguished. Note that some solutions find suboptimal local maxima, but that this happens infrequently.

Image /page/0/Figure/2 description: The image is a scatter plot with red box plots and plus signs. The x-axis is labeled "K" and has tick marks at 1, 2, 3, 4, 5, and 6. The y-axis is labeled "p(D|K)". There are several red box plots and plus signs scattered across the plot. For K=1, there is a box plot and a plus sign above it. For K=2, there are two box plots and a plus sign between them. For K=3, there is a box plot and two plus signs below it. For K=4, there are two box plots and two plus signs below them. For K=5, there are two box plots and two plus signs below them. For K=6, there is a single box plot.

parameter values. We have seen in Figure 10.2 that if the true posterior distribution is multimodal, variational inference based on the minimization of  $KL(q||p)$  will tend to approximate the distribution in the neighbourhood of one of the modes and ignore the others. Again, because equivalent modes have equivalent predictive densities, this is of no concern provided we are considering a model having a specific number K of components. If, however, we wish to compare different values of  $K$ , then we need to take account of this multimodality. A simple approximate solution is to add *Exercise 10.22* a term  $\ln K!$  onto the lower bound when used for model comparison and averaging.

Figure 10.7 shows a plot of the lower bound, including the multimodality factor, versus the number  $K$  of components for the Old Faithful data set. It is worth emphasizing once again that maximum likelihood would lead to values of the likelihood function that increase monotonically with  $K$  (assuming the singular solutions have been avoided, and discounting the effects of local maxima) and so cannot be used to determine an appropriate model complexity. By contrast, Bayesian inference *Section 3.4* automatically makes the trade-off between model complexity and fitting the data.

This approach to the determination of  $K$  requires that a range of models having different  $K$  values be trained and compared. An alternative approach to determining a suitable value for K is to treat the mixing coefficients  $\pi$  as parameters and make point estimates of their values by maximizing the lower bound (Corduneanu and Bishop, 2001) with respect to  $\pi$  instead of maintaining a probability distribution *Exercise 10.23* over them as in the fully Bayesian approach. This leads to the re-estimation equation

$$
\pi_k = \frac{1}{N} \sum_{n=1}^{N} r_{nk}
$$
\n(10.83)

and this maximization is interleaved with the variational updates for the  $q$  distribution over the remaining parameters. Components that provide insufficient contribution

Exercise 19.22

*Exercise* 10.23

to explaining the data will have their mixing coefficients driven to zero during the optimization, and so they are effectively removed from the model through *automatic relevance determination*. This allows us to make a single training run in which we start with a relatively large initial value of  $K$ , and allow surplus components to be pruned out of the model. The origins of the sparsity when optimizing with respect to *Section 7.2.2* hyperparameters is discussed in detail in the context of the relevance vector machine.

### **10.2.5 Induced factorizations**

In deriving these variational update equations for the Gaussian mixture model, we assumed a particular factorization of the variational posterior distribution given by (10.42). However, the optimal solutions for the various factors exhibit additional factorizations. In particular, the solution for  $q^{\star}(\mu, \Lambda)$  is given by the product of an independent distribution  $q^*(\mu_k, \Lambda_k)$  over each of the components k of the mixture, whereas the variational posterior distribution  $q^{\star}(\mathbf{Z})$  over the latent variables, given by (10.48), factorizes into an independent distribution  $q^*(\mathbf{z}_n)$  for each observation n (note that it does not further factorize with respect to  $k$  because, for each value of  $n$ , the  $z_{nk}$  are constrained to sum to one over k). These additional factorizations are a consequence of the interaction between the assumed factorization and the conditional independence properties of the true distribution, as characterized by the directed graph in Figure 10.5.

We shall refer to these additional factorizations as *induced factorizations* because they arise from an interaction between the factorization assumed in the variational posterior distribution and the conditional independence properties of the true joint distribution. In a numerical implementation of the variational approach it is important to take account of such additional factorizations. For instance, it would be very inefficient to maintain a full precision matrix for the Gaussian distribution over a set of variables if the optimal form for that distribution always had a diagonal precision matrix (corresponding to a factorization with respect to the individual variables described by that Gaussian).

Such induced factorizations can easily be detected using a simple graphical test based on d-separation as follows. We partition the latent variables into three disjoint groups **A**, **B**, **C** and then let us suppose that we are assuming a factorization between **C** and the remaining latent variables, so that

$$
q(\mathbf{A}, \mathbf{B}, \mathbf{C}) = q(\mathbf{A}, \mathbf{B})q(\mathbf{C}).
$$
 (10.84)

Using the general result (10.9), together with the product rule for probabilities, we see that the optimal solution for  $q(\mathbf{A}, \mathbf{B})$  is given by

$$
\ln q^{\star}(\mathbf{A}, \mathbf{B}) = \mathbb{E}_{\mathbf{C}}[\ln p(\mathbf{X}, \mathbf{A}, \mathbf{B}, \mathbf{C})] + \text{const}
$$
  
=  $\mathbb{E}_{\mathbf{C}}[\ln p(\mathbf{A}, \mathbf{B}|\mathbf{X}, \mathbf{C})] + \text{const.}$  (10.85)

We now ask whether this resulting solution will factorize between **A** and **B**, in other words whether  $q^*(\mathbf{A}, \mathbf{B}) = q^*(\mathbf{A})q^*(\mathbf{B})$ . This will happen if, and only if,  $\ln p(\mathbf{A}, \mathbf{B}|\mathbf{X}, \mathbf{C}) = \ln p(\mathbf{A}|\mathbf{X}, \mathbf{C}) + \ln p(\mathbf{B}|\mathbf{X}, \mathbf{C})$ , that is, if the conditional independence relation

$$
\mathbf{A} \perp \!\!\!\perp \mathbf{B} \mid \mathbf{X}, \mathbf{C} \tag{10.86}
$$

is satisfied. We can test to see if this relation does hold, for any choice of **A** and **B** by making use of the d-separation criterion.

To illustrate this, consider again the Bayesian mixture of Gaussians represented by the directed graph in Figure 10.5, in which we are assuming a variational factorization given by (10.42). We can see immediately that the variational posterior distribution over the parameters must factorize between  $\pi$  and the remaining parameters  $\mu$  and  $\Lambda$  because all paths connecting  $\pi$  to either  $\mu$  or  $\Lambda$  must pass through one of the nodes  $z_n$  all of which are in the conditioning set for our conditional independence test and all of which are head-to-tail with respect to such paths.

### **10.3. Variational Linear Regression**

As a second illustration of variational inference, we return to the Bayesian linear regression model of Section 3.3. In the evidence framework, we approximated the integration over  $\alpha$  and  $\beta$  by making point estimates obtained by maximizing the log marginal likelihood. A fully Bayesian approach would integrate over the hyperparameters as well as over the parameters. Although exact integration is intractable, we can use variational methods to find a tractable approximation. In order to simplify the discussion, we shall suppose that the noise precision parameter  $\beta$  is known, and is fixed to its true value, although the framework is easily extended to include *Exercise 10.26* the distribution over  $\beta$ . For the linear regression model, the variational treatment will turn out to be equivalent to the evidence framework. Nevertheless, it provides a good exercise in the use of variational methods and will also lay the foundation for variational treatment of Bayesian logistic regression in Section 10.6.

Recall that the likelihood function for **w**, and the prior over **w**, are given by

$$
p(\mathbf{t}|\mathbf{w}) = \prod_{n=1}^{N} \mathcal{N}(t_n|\mathbf{w}^{\mathrm{T}}\boldsymbol{\phi}_n, \beta^{-1})
$$
(10.87)

$$
p(\mathbf{w}|\alpha) = \mathcal{N}(\mathbf{w}|\mathbf{0}, \alpha^{-1}\mathbf{I}) \tag{10.88}
$$

where  $\phi_n = \phi(\mathbf{x}_n)$ . We now introduce a prior distribution over  $\alpha$ . From our discussion in Section 2.3.6, we know that the conjugate prior for the precision of a Gaussian is given by a gamma distribution, and so we choose

$$
p(\alpha) = \text{Gam}(\alpha|a_0, b_0) \tag{10.89}
$$

where  $Gam(\cdot|\cdot,\cdot)$  is defined by (B.26). Thus the joint distribution of all the variables is given by

$$
p(\mathbf{t}, \mathbf{w}, \alpha) = p(\mathbf{t}|\mathbf{w})p(\mathbf{w}|\alpha)p(\alpha).
$$
 (10.90)

This can be represented as a directed graphical model as shown in Figure 10.8.

#### **10.3.1 Variational distribution**

Our first goal is to find an approximation to the posterior distribution  $p(\mathbf{w}, \alpha | \mathbf{t})$ . To do this, we employ the variational framework of Section 10.1, with a variational

### **10.3. Variational Linear Regression 487**

**Figure 10.8** Probabilistic graphical model representing the joint distribution (10.90) for the Bayesian linear regression model.

Image /page/3/Figure/2 description: This is a graphical representation of a probabilistic model. It features a plate labeled 'N' which encloses variables 'tn' and 'φn'. An arrow points from 'β' to 'tn', and another arrow points from 'φn' to 'tn'. An arrow also points from 'tn' to 'w'. Separately, 'α' has an arrow pointing to 'w'. The variables 'tn' and 'w' are represented by shaded circles, while 'φn' and 'α' and 'β' are represented by unshaded circles.

posterior distribution given by the factorized expression

$$
q(\mathbf{w}, \alpha) = q(\mathbf{w})q(\alpha).
$$
 (10.91)

We can find re-estimation equations for the factors in this distribution by making use of the general result (10.9). Recall that for each factor, we take the log of the joint distribution over all variables and then average with respect to those variables not in that factor. Consider first the distribution over  $\alpha$ . Keeping only terms that have a functional dependence on  $\alpha$ , we have

$$
\ln q^{\star}(\alpha) = \ln p(\alpha) + \mathbb{E}_{\mathbf{w}} [\ln p(\mathbf{w}|\alpha)] + \text{const}
$$

$$
= (a_0 - 1) \ln \alpha - b_0 \alpha + \frac{M}{2} \ln \alpha - \frac{\alpha}{2} \mathbb{E}[\mathbf{w}^T \mathbf{w}] + \text{const.} \quad (10.92)
$$

We recognize this as the log of a gamma distribution, and so identifying the coefficients of  $\alpha$  and  $\ln \alpha$  we obtain

$$
q^{\star}(\alpha) = \text{Gam}(\alpha|a_N, b_N) \tag{10.93}
$$

where

$$
a_N = a_0 + \frac{M}{2} \tag{10.94}
$$

$$
b_N = b_0 + \frac{1}{2} \mathbb{E}[\mathbf{w}^{\mathrm{T}} \mathbf{w}]. \tag{10.95}
$$

Similarly, we can find the variational re-estimation equation for the posterior distribution over **w**. Again, using the general result (10.9), and keeping only those terms that have a functional dependence on **w**, we have

$$
\ln q^{\star}(\mathbf{w}) = \ln p(\mathbf{t}|\mathbf{w}) + \mathbb{E}_{\alpha} [\ln p(\mathbf{w}|\alpha)] + \text{const}
$$
 (10.96)

$$
= -\frac{\beta}{2} \sum_{n=1}^{N} \{ \mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}_n - t_n \}^2 - \frac{1}{2} \mathbb{E}[\alpha] \mathbf{w}^{\mathrm{T}} \mathbf{w} + \text{const}
$$
 (10.97)

$$
= -\frac{1}{2} \mathbf{w}^{\mathrm{T}} \left( \mathbb{E}[\alpha] \mathbf{I} + \beta \mathbf{\Phi}^{\mathrm{T}} \mathbf{\Phi} \right) \mathbf{w} + \beta \mathbf{w}^{\mathrm{T}} \mathbf{\Phi}^{\mathrm{T}} \mathbf{t} + \text{const.} \qquad (10.98)
$$

Because this is a quadratic form, the distribution  $q^*(w)$  is Gaussian, and so we can complete the square in the usual way to identify the mean and covariance, giving

$$
q^{\star}(\mathbf{w}) = \mathcal{N}(\mathbf{w}|\mathbf{m}_N, \mathbf{S}_N)
$$
 (10.99)

where

$$
\mathbf{m}_N = \beta \mathbf{S}_N \mathbf{\Phi}^{\mathrm{T}} \mathbf{t} \tag{10.100}
$$

$$
\mathbf{S}_N = (\mathbb{E}[\alpha] \mathbf{I} + \beta \mathbf{\Phi}^{\mathrm{T}} \mathbf{\Phi})^{-1}.
$$
 (10.101)

Note the close similarity to the posterior distribution (3.52) obtained when  $\alpha$  was treated as a fixed parameter. The difference is that here  $\alpha$  is replaced by its expectation  $\mathbb{E}[\alpha]$  under the variational distribution. Indeed, we have chosen to use the same notation for the covariance matrix  $\mathbf{S}_N$  in both cases.

Using the standard results (B.27), (B.38), and (B.39), we can obtain the required moments as follows

$$
\mathbb{E}[\alpha] = a_N/b_N \tag{10.102}
$$

$$
\mathbb{E}[\mathbf{w}\mathbf{w}^{\mathrm{T}}] = \mathbf{m}_{N}\mathbf{m}_{N}^{\mathrm{T}} + \mathbf{S}_{N}.
$$
 (10.103)

The evaluation of the variational posterior distribution begins by initializing the parameters of one of the distributions  $q(\mathbf{w})$  or  $q(\alpha)$ , and then alternately re-estimates these factors in turn until a suitable convergence criterion is satisfied (usually specified in terms of the lower bound to be discussed shortly).

It is instructive to relate the variational solution to that found using the evidence framework in Section 3.5. To do this consider the case  $a_0 = b_0 = 0$ , corresponding to the limit of an infinitely broad prior over  $\alpha$ . The mean of the variational posterior distribution  $q(\alpha)$  is then given by

$$
\mathbb{E}[\alpha] = \frac{a_N}{b_N} = \frac{M/2}{\mathbb{E}[\mathbf{w}^T \mathbf{w}]/2} = \frac{M}{\mathbf{m}_N^T \mathbf{m}_N + \text{Tr}(\mathbf{S}_N)}.
$$
(10.104)

Comparison with (9.63) shows that in the case of this particularly simple model, the variational approach gives precisely the same expression as that obtained by maximizing the evidence function using EM except that the point estimate for  $\alpha$ is replaced by its expected value. Because the distribution  $q(\mathbf{w})$  depends on  $q(\alpha)$ only through the expectation  $\mathbb{E}[\alpha]$ , we see that the two approaches will give identical results for the case of an infinitely broad prior.

#### **10.3.2 Predictive distribution**

The predictive distribution over  $t$ , given a new input  $x$ , is easily evaluated for this model using the Gaussian variational posterior for the parameters

$$
p(t|\mathbf{x}, \mathbf{t}) = \int p(t|\mathbf{x}, \mathbf{w}) p(\mathbf{w}|\mathbf{t}) \, d\mathbf{w}
$$
  

$$
\simeq \int p(t|\mathbf{x}, \mathbf{w}) q(\mathbf{w}) \, d\mathbf{w}
$$
  

$$
= \int \mathcal{N}(t|\mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}), \beta^{-1}) \mathcal{N}(\mathbf{w}|\mathbf{m}_{N}, \mathbf{S}_{N}) \, d\mathbf{w}
$$
  

$$
= \mathcal{N}(t|\mathbf{m}_{N}^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}), \sigma^{2}(\mathbf{x})) \qquad (10.105)
$$

where we have evaluated the integral by making use of the result (2.115) for the linear-Gaussian model. Here the input-dependent variance is given by

$$
\sigma^2(\mathbf{x}) = \frac{1}{\beta} + \phi(\mathbf{x})^T \mathbf{S}_N \phi(\mathbf{x}).
$$
\n(10.106)

Note that this takes the same form as the result (3.59) obtained with fixed  $\alpha$  except that now the expected value  $\mathbb{E}[\alpha]$  appears in the definition of  $\mathbf{S}_N$ .

#### **10.3.3 Lower bound**

Another quantity of importance is the lower bound  $\mathcal L$  defined by

$$
\mathcal{L}(q) = \mathbb{E}[\ln p(\mathbf{w}, \alpha, \mathbf{t})] - \mathbb{E}[\ln q(\mathbf{w}, \alpha)]
$$
  
\n
$$
= \mathbb{E}_{\mathbf{w}}[\ln p(\mathbf{t}|\mathbf{w})] + \mathbb{E}_{\mathbf{w}, \alpha}[\ln p(\mathbf{w}|\alpha)] + \mathbb{E}_{\alpha}[\ln p(\alpha)]
$$
  
\n
$$
- \mathbb{E}_{\alpha}[\ln q(\mathbf{w})]_{\mathbf{w}} - \mathbb{E}[\ln q(\alpha)]. \qquad (10.107)
$$

*Exercise 10.27* Evaluation of the various terms is straightforward, making use of results obtained in previous chapters, and gives

$$
\mathbb{E}[\ln p(\mathbf{t}|\mathbf{w})]_{\mathbf{w}} = \frac{N}{2} \ln \left( \frac{\beta}{2\pi} \right) - \frac{\beta}{2} \mathbf{t}^{\mathrm{T}} \mathbf{t} + \beta \mathbf{m}_{N}^{\mathrm{T}} \mathbf{\Phi}^{\mathrm{T}} \mathbf{t}
$$

$$
-\frac{\beta}{2} \mathrm{Tr} \left[ \mathbf{\Phi}^{\mathrm{T}} \mathbf{\Phi} (\mathbf{m}_{N} \mathbf{m}_{N}^{\mathrm{T}} + \mathbf{S}_{N}) \right] (10.108)
$$

$$
$\mathbb{E}[\ln p(\mathbf{w}|\alpha)]_{\mathbf{w},\alpha}$  = -\frac{M}{2}\ln(2\pi) + \frac{M}{2}(\psi(a_N) - \ln b_N)
$$
$$
-\frac{a_N}{2b_N} \left[ \mathbf{m}_N^{\mathrm{T}} \mathbf{m}_N + \text{Tr}(\mathbf{S}_N) \right] (10.109)
$$

$$
\mathbb{E}[\ln p(\alpha)]_{\alpha} = a_0 \ln b_0 + (a_0 - 1) [\psi(a_N) - \ln b_N] - b_0 \frac{a_N}{b_N} - \ln \Gamma(a_N)
$$
(10.110)

$$
-\mathbb{E}[\ln q(\mathbf{w})]_{\mathbf{w}} = \frac{1}{2}\ln|\mathbf{S}_N| + \frac{M}{2}[1 + \ln(2\pi)] \tag{10.111}
$$

$$
-\mathbb{E}[\ln q(\alpha)]_{\alpha} = \ln \Gamma(a_N) - (a_N - 1)\psi(a_N) - \ln b_N + a_N. (10.112)
$$

Figure 10.9 shows a plot of the lower bound  $\mathcal{L}(q)$  versus the degree of a polynomial model for a synthetic data set generated from a degree three polynomial. Here the prior parameters have been set to  $a_0 = b_0 = 0$ , corresponding to the noninformative prior  $p(\alpha) \propto 1/\alpha$ , which is uniform over  $\ln \alpha$  as discussed in Section 2.3.6. As we saw in Section 10.1, the quantity  $\mathcal L$  represents lower bound on the log marginal likelihood  $p(\mathbf{t}|M)$  for the model. If we assign equal prior probabilities  $p(M)$  to the different values of M, then we can interpret  $\mathcal L$  as an approximation to the posterior model probability  $p(M|\mathbf{t})$ . Thus the variational framework assigns the highest probability to the model with  $M = 3$ . This should be contrasted with the maximum likelihood result, which assigns ever smaller residual error to models of increasing complexity until the residual error is driven to zero, causing maximum likelihood to favour severely over-fitted models.

**Figure 10.9** Plot of the lower bound  $\mathcal{L}$  versus the order  $M$  of the polynomial, for a polynomial model, in which a set of 10 data points is generated from a polynomial with  $M = 3$  sampled over the interval  $(-5, 5)$  with additive Gaussian noise of variance 0.09. The value of the bound gives the log probability of the model, and we see that the value of the bound peaks at  $M = 3$ , corresponding to the true model from which the data set was generated.

Image /page/6/Figure/2 description: A line graph shows a series of data points connected by blue lines. The x-axis is labeled with numbers 1, 3, 5, 7, and 9. The data points form a peak at x=3 and then descend linearly towards the right. The y-axis is not labeled but shows increasing values from bottom to top.

### **10.4. Exponential Family Distributions**

In Chapter 2, we discussed the important role played by the exponential family of distributions and their conjugate priors. For many of the models discussed in this book, the complete-data likelihood is drawn from the exponential family. However, in general this will not be the case for the marginal likelihood function for the observed data. For example, in a mixture of Gaussians, the joint distribution of observations  $x_n$  and corresponding hidden variables  $z_n$  is a member of the exponential family, whereas the marginal distribution of  $x_n$  is a mixture of Gaussians and hence is not.

Up to now we have grouped the variables in the model into observed variables and hidden variables. We now make a further distinction between latent variables, denoted **Z**, and parameters, denoted *<sup>θ</sup>*, where parameters are *intensive* (fixed in number independent of the size of the data set), whereas latent variables are *extensive* (scale in number with the size of the data set). For example, in a Gaussian mixture model, the indicator variables  $z_{kn}$  (which specify which component k is responsible for generating data point  $\mathbf{x}_n$ ) represent the latent variables, whereas the means  $\mu_k$ , precisions  $\Lambda_k$  and mixing proportions  $\pi_k$  represent the parameters.

Consider the case of independent identically distributed data. We denote the data values by  $X = \{x_n\}$ , where  $n = 1, \ldots N$ , with corresponding latent variables  $\mathbf{Z} = \{z_n\}$ . Now suppose that the joint distribution of observed and latent variables is a member of the exponential family, parameterized by natural parameters  $\eta$  so that

$$
p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\eta}) = \prod_{n=1}^{N} h(\mathbf{x}_n, \mathbf{z}_n) g(\boldsymbol{\eta}) \exp \{ \boldsymbol{\eta}^{\mathrm{T}} \mathbf{u}(\mathbf{x}_n, \mathbf{z}_n) \}.
$$
 (10.113)

We shall also use a conjugate prior for *η*, which can be written as

$$
p(\boldsymbol{\eta}|\nu_0,\mathbf{v}_0) = f(\nu_0,\boldsymbol{\chi}_0)g(\boldsymbol{\eta})^{\nu_0}\exp\left\{\nu_0\boldsymbol{\eta}^{\mathrm{T}}\boldsymbol{\chi}_0\right\}.
$$
 (10.114)

Recall that the conjugate prior distribution can be interpreted as a prior number  $\nu_0$ of observations all having the value  $\chi_0$  for the **u** vector. Now consider a variational

### **10.4. Exponential Family Distributions 491**

distribution that factorizes between the latent variables and the parameters, so that  $q(\mathbf{Z}, \eta) = q(\mathbf{Z})q(\eta)$ . Using the general result (10.9), we can solve for the two factors as follows

$$
\ln q^{\star}(\mathbf{Z}) = \mathbb{E}_{\eta}[\ln p(\mathbf{X}, \mathbf{Z}|\eta)] + \text{const}
$$
  
= 
$$
\sum_{n=1}^{N} {\ln h(\mathbf{x}_n, \mathbf{z}_n) + \mathbb{E}[\eta^{\text{T}}] \mathbf{u}(\mathbf{x}_n, \mathbf{z}_n)} + \text{const.} \quad (10.115)
$$

Thus we see that this decomposes into a sum of independent terms, one for each value of n, and hence the solution for  $q^*(\mathbf{Z})$  will factorize over n so that  $q^*(\mathbf{Z}) = \prod_{\alpha} q^*(\mathbf{z})$ . This is an example of an induced factorization. Taking the exponential Section 10.2.5  $\prod_n q^*(\mathbf{z}_n)$ . This is an example of an induced factorization. Taking the exponential of both sides we have of both sides, we have

$$
q^{\star}(\mathbf{z}_n) = h(\mathbf{x}_n, \mathbf{z}_n)g\left(\mathbb{E}[\boldsymbol{\eta}]\right) \exp\left\{\mathbb{E}[\boldsymbol{\eta}^{\mathrm{T}}] \mathbf{u}(\mathbf{x}_n, \mathbf{z}_n)\right\} \tag{10.116}
$$

where the normalization coefficient has been re-instated by comparison with the standard form for the exponential family.

Similarly, for the variational distribution over the parameters, we have

$$
\ln q^{\star}(\boldsymbol{\eta}) = \ln p(\boldsymbol{\eta}|\nu_0, \boldsymbol{\chi}_0) + \mathbb{E}_{\mathbf{Z}}[\ln p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\eta})] + \text{const}
$$
 (10.117)

$$
= \nu_0 \ln g(\boldsymbol{\eta}) + \boldsymbol{\eta}^{\mathrm{T}} \boldsymbol{\chi}_0 + \sum_{n=1}^{N} \left\{ \ln g(\boldsymbol{\eta}) + \boldsymbol{\eta}^{\mathrm{T}} \mathbb{E}_{\mathbf{z}_n}[\mathbf{u}(\mathbf{x}_n, \mathbf{z}_n)] \right\} + \text{const.} \quad (10.118)
$$

Again, taking the exponential of both sides, and re-instating the normalization coefficient by inspection, we have

N

$$
q^{\star}(\boldsymbol{\eta}) = f(\nu_N, \boldsymbol{\chi}_N) g(\boldsymbol{\eta})^{\nu_N} \exp\left\{\boldsymbol{\eta}^{\mathrm{T}} \boldsymbol{\chi}_N\right\} \tag{10.119}
$$

where we have defined

$$
\nu_N = \nu_0 + N \tag{10.120}
$$

$$
\boldsymbol{\chi}_N = \boldsymbol{\chi}_0 + \sum_{n=1}^N \mathbb{E}_{\mathbf{z}_n}[\mathbf{u}(\mathbf{x}_n, \mathbf{z}_n)]. \qquad (10.121)
$$

Note that the solutions for  $q^*(\mathbf{z}_n)$  and  $q^*(\eta)$  are coupled, and so we solve them iteratively in a two-stage procedure. In the variational E step, we evaluate the expected sufficient statistics  $\mathbb{E}[\mathbf{u}(\mathbf{x}_n, \mathbf{z}_n)]$  using the current posterior distribution  $q(\mathbf{z}_n)$  over the latent variables and use this to compute a revised posterior distribution  $q(\eta)$  over the parameters. Then in the subsequent variational M step, we use this revised parameter posterior distribution to find the expected natural parameters  $\mathbb{E}[\eta^T]$ , which gives rise to a revised variational distribution over the latent variables.

#### **10.4.1 Variational message passing**

We have illustrated the application of variational methods by considering a specific model, the Bayesian mixture of Gaussians, in some detail. This model can be

*Section 10.2.5*

described by the directed graph shown in Figure 10.5. Here we consider more generally the use of variational methods for models described by directed graphs and derive a number of widely applicable results.

The joint distribution corresponding to a directed graph can be written using the decomposition

$$
p(\mathbf{x}) = \prod_{i} p(\mathbf{x}_i | \text{pa}_i)
$$
 (10.122)

where  $\mathbf{x}_i$  denotes the variable(s) associated with node i, and  $pa_i$  denotes the parent set corresponding to node i. Note that  $x_i$  may be a latent variable or it may belong to the set of observed variables. Now consider a variational approximation in which the distribution  $q(\mathbf{x})$  is assumed to factorize with respect to the  $\mathbf{x}_i$  so that

$$
q(\mathbf{x}) = \prod_i q_i(\mathbf{x}_i). \tag{10.123}
$$

Note that for observed nodes, there is no factor  $q(\mathbf{x}_i)$  in the variational distribution. We now substitute (10.122) into our general result (10.9) to give

$$
\ln q_j^{\star}(\mathbf{x}_j) = \mathbb{E}_{i \neq j} \left[ \sum_i \ln p(\mathbf{x}_i | \text{pa}_i) \right] + \text{const.}
$$
 (10.124)

Any terms on the right-hand side that do not depend on  $x_j$  can be absorbed into the additive constant. In fact, the only terms that do depend on  $x_j$  are the conditional distribution for  $\mathbf{x}_i$  given by  $p(\mathbf{x}_i | pa_i)$  together with any other conditional distributions that have  $x_j$  in the conditioning set. By definition, these conditional distributions correspond to the children of node  $j$ , and they therefore also depend on the *co-parents* of the child nodes, i.e., the other parents of the child nodes besides node  $\mathbf{x}_j$  itself. We see that the set of all nodes on which  $q^*(\mathbf{x}_j)$  depends corresponds to the Markov blanket of node  $x_j$ , as illustrated in Figure 8.26. Thus the update of the factors in the variational posterior distribution represents a local calculation on the graph. This makes possible the construction of general purpose software for variational inference in which the form of the model does not need to be specified in advance (Bishop *et al.*, 2003).

If we now specialize to the case of a model in which all of the conditional distributions have a conjugate-exponential structure, then the variational update procedure can be cast in terms of a local message passing algorithm (Winn and Bishop, 2005). In particular, the distribution associated with a particular node can be updated once that node has received messages from all of its parents and all of its children. This in turn requires that the children have already received messages from their coparents. The evaluation of the lower bound can also be simplified because many of the required quantities are already evaluated as part of the message passing scheme. This distributed message passing formulation has good scaling properties and is well suited to large networks.

### **10.5. Local Variational Methods**

The variational framework discussed in Sections 10.1 and 10.2 can be considered a 'global' method in the sense that it directly seeks an approximation to the full posterior distribution over all random variables. An alternative 'local' approach involves finding bounds on functions over individual variables or groups of variables within a model. For instance, we might seek a bound on a conditional distribution  $p(y|x)$ , which is itself just one factor in a much larger probabilistic model specified by a directed graph. The purpose of introducing the bound of course is to simplify the resulting distribution. This local approximation can be applied to multiple variables in turn until a tractable approximation is obtained, and in Section 10.6.1 we shall give a practical example of this approach in the context of logistic regression. Here we focus on developing the bounds themselves.

We have already seen in our discussion of the Kullback-Leibler divergence that the convexity of the logarithm function played a key role in developing the lower bound in the global variational approach. We have defined a (strictly) convex func-*Section 1.6.1* tion as one for which every chord lies above the function. Convexity also plays a central role in the local variational framework. Note that our discussion will apply equally to concave functions with 'min' and 'max' interchanged and with lower bounds replaced by upper bounds.

> Let us begin by considering a simple example, namely the function  $f(x) =$  $\exp(-x)$ , which is a convex function of x, and which is shown in the left-hand plot of Figure 10.10. Our goal is to approximate  $f(x)$  by a simpler function, in particular a linear function of  $x$ . From Figure 10.10, we see that this linear function will be a lower bound on  $f(x)$  if it corresponds to a tangent. We can obtain the tangent line  $y(x)$  at a specific value of x, say  $x = \xi$ , by making a first order Taylor expansion

$$
y(x) = f(\xi) + f'(\xi)(x - \xi)
$$
 (10.125)

so that  $y(x) \leq f(x)$  with equality when  $x = \xi$ . For our example function  $f(x) =$ 

**Figure 10.10** In the left-hand figure the red curve shows the function  $exp(-x)$ , and the blue line shows the tangent at  $x = \xi$  defined by (10.125) with  $\xi = 1$ . This line has slope  $\lambda = f'(\xi) = -\exp(-\xi)$ . Note that any other tangent line, for example the ones shown in green, will have a smaller value of  $y$  at  $x =$  $\xi$ . The right-hand figure shows the corresponding plot of the function  $\lambda \xi - g(\lambda)$ , where  $g(\lambda)$  is given by (10.131), versus  $\lambda$  for  $\xi = 1$ , in which the maximum corresponds to  $\lambda = -\exp(-\xi) = -1/e.$ 

Image /page/9/Figure/8 description: The image contains two plots. The left plot shows a red curve that starts at (0,1) and decreases as x increases, passing through approximately (1.5, 0.5) and (3, 0.1). There are also two green lines and one blue line that are tangent to the red curve at different points. A vertical black line is drawn at x = ξ. The x-axis is labeled from 0 to 3, with tick marks at 0, ξ, 1.5, and 3. The y-axis is labeled from 0 to 1, with tick marks at 0, 0.5, and 1. The right plot shows a red curve that starts at (0,0) and increases to a maximum at approximately (-0.3, 0.35), then decreases to (0,0). The x-axis is labeled from -1 to 0, with tick marks at -1, -0.5, and 0. The y-axis is labeled from 0 to 0.4, with tick marks at 0, 0.2, and 0.4. The title of the right plot is "λξ - g(λ)". A vertical black line is drawn at λ = -0.3.

Section 1.6.1

Image /page/10/Figure/1 description: The image displays two graphs side-by-side. Both graphs have the x and y axes labeled. The left graph shows a red curve labeled "f(x)" and a blue line labeled "λx". The blue line starts at the origin and slopes downwards. The red curve is above the blue line for small values of x and intersects the blue line at some point. Two dashed green lines are drawn from the x-axis to the red curve, indicating specific x-values. The right graph also shows a red curve labeled "f(x)" and a blue line labeled "λx - g(λ)". The blue line intersects the y-axis at "-g(λ)" and slopes downwards, intersecting the red curve at one point. The red curve in this graph appears to be the same as in the left graph.

**Figure 10.11** In the left-hand plot the red curve shows a convex function  $f(x)$ , and the blue line represents the linear function  $\lambda x$ , which is a lower bound on  $f(x)$  because  $f(x) > \lambda x$  for all x. For the given value of slope  $\lambda$  the contact point of the tangent line having the same slope is found by minimizing with respect to  $x$  the discrepancy (shown by the green dashed lines) given by  $f(x) - \lambda x$ . This defines the dual function  $g(\lambda)$ , which corresponds to the (negative of the) intercept of the tangent line having slope  $\lambda$ .

 $\exp(-x)$ , we therefore obtain the tangent line in the form

$$
y(x) = \exp(-\xi) - \exp(-\xi)(x - \xi)
$$
 (10.126)

which is a linear function parameterized by  $\xi$ . For consistency with subsequent discussion, let us define  $\lambda = -\exp(-\xi)$  so that

$$
y(x,\lambda) = \lambda x - \lambda + \lambda \ln(-\lambda). \tag{10.127}
$$

Different values of  $\lambda$  correspond to different tangent lines, and because all such lines are lower bounds on the function, we have  $f(x) \geq y(x, \lambda)$ . Thus we can write the function in the form

$$
f(x) = \max_{\lambda} \{ \lambda x - \lambda + \lambda \ln(-\lambda) \}.
$$
 (10.128)

We have succeeded in approximating the convex function  $f(x)$  by a simpler, linear function  $y(x, \lambda)$ . The price we have paid is that we have introduced a variational parameter  $\lambda$ , and to obtain the tightest bound we must optimize with respect to  $\lambda$ .

We can formulate this approach more generally using the framework of *convex duality* (Rockafellar, 1972; Jordan *et al.*, 1999). Consider the illustration of a convex function  $f(x)$  shown in the left-hand plot in Figure 10.11. In this example, the function  $\lambda x$  is a lower bound on  $f(x)$  but it is not the best lower bound that can be achieved by a linear function having slope  $\lambda$ , because the tightest bound is given by the tangent line. Let us write the equation of the tangent line, having slope  $\lambda$  as  $\lambda x - g(\lambda)$  where the (negative) intercept  $g(\lambda)$  clearly depends on the slope  $\lambda$  of the tangent. To determine the intercept, we note that the line must be moved vertically by an amount equal to the smallest vertical distance between the line and the function, as shown in Figure 10.11. Thus

$$
g(\lambda) = -\min_{x} \{f(x) - \lambda x\}
$$
  
= 
$$
\max_{x} \{\lambda x - f(x)\}.
$$
 (10.129)

Now, instead of fixing  $\lambda$  and varying x, we can consider a particular x and then adjust  $\lambda$  until the tangent plane is tangent at that particular x. Because the y value of the tangent line at a particular  $x$  is maximized when that value coincides with its contact point, we have

$$
f(x) = \max_{\lambda} \{ \lambda x - g(\lambda) \}.
$$
 (10.130)

We see that the functions  $f(x)$  and  $g(\lambda)$  play a dual role, and are related through (10.129) and (10.130).

Let us apply these duality relations to our simple example  $f(x) = \exp(-x)$ . From (10.129) we see that the maximizing value of x is given by  $\xi = -\ln(-\lambda)$ , and back-substituting we obtain the conjugate function  $g(\lambda)$  in the form

$$
g(\lambda) = \lambda - \lambda \ln(-\lambda) \tag{10.131}
$$

as obtained previously. The function  $\lambda \xi - g(\lambda)$  is shown, for  $\xi = 1$  in the right-hand plot in Figure 10.10. As a check, we can substitute (10.131) into (10.130), which gives the maximizing value of  $\lambda = -\exp(-x)$ , and back-substituting then recovers the original function  $f(x) = \exp(-x)$ .

For concave functions, we can follow a similar argument to obtain upper bounds, in which max' is replaced with 'min', so that

$$
f(x) = \min_{\lambda} \{ \lambda x - g(\lambda) \}
$$
 (10.132)

$$
g(\lambda) = \min_{x} \{ \lambda x - f(x) \}.
$$
 (10.133)

If the function of interest is not convex (or concave), then we cannot directly apply the method above to obtain a bound. However, we can first seek invertible transformations either of the function or of its argument which change it into a convex form. We then calculate the conjugate function and then transform back to the original variables.

An important example, which arises frequently in pattern recognition, is the logistic sigmoid function defined by

$$
\sigma(x) = \frac{1}{1 + e^{-x}}.\tag{10.134}
$$

As it stands this function is neither convex nor concave. However, if we take the logarithm we obtain a function which is concave, as is easily verified by finding the *Exercise 10.30* second derivative. From (10.133) the corresponding conjugate function then takes the form

$$
g(\lambda) = \min_{x} \left\{ \lambda x - f(x) \right\} = -\lambda \ln \lambda - (1 - \lambda) \ln(1 - \lambda)
$$
 (10.135)

which we recognize as the binary entropy function for a variable whose probability *Appendix B* of having the value 1 is λ. Using (10.132), we then obtain an upper bound on the log sigmoid

$$
\ln \sigma(x) \leqslant \lambda x - g(\lambda) \tag{10.136}
$$

Image /page/12/Figure/1 description: The image contains two plots. The left plot shows two blue curves and one red curve. The x-axis ranges from -6 to 6, and the y-axis ranges from 0 to 1. One blue curve is a straight line with a positive slope passing through the origin. The other two curves are sigmoid-shaped, with one labeled "λ = 0.2" and the other labeled "λ = 0.7". The right plot also shows a blue curve and a red curve, with the x-axis ranging from -6 to 6 and the y-axis ranging from 0 to 1. The blue curve in this plot is bell-shaped, peaking around x=0 and decreasing on either side. The red curve is a sigmoid shape, similar to the ones in the left plot. A dashed green line is shown at x = ξ, and the plot is labeled "ξ = 2.5". The x-axis also shows markings for -ξ and ξ.

**Figure 10.12** The left-hand plot shows the logistic sigmoid function  $\sigma(x)$  defined by (10.134) in red, together with two examples of the exponential upper bound (10.137) shown in blue. The right-hand plot shows the logistic sigmoid again in red together with the Gaussian lower bound (10.144) shown in blue. Here the parameter  $\xi = 2.5$ , and the bound is exact at  $x = \xi$  and  $x = -\xi$ , denoted by the dashed green lines.

and taking the exponential, we obtain an upper bound on the logistic sigmoid itself of the form

$$
\sigma(x) \le \exp(\lambda x - g(\lambda)) \tag{10.137}
$$

which is plotted for two values of  $\lambda$  on the left-hand plot in Figure 10.12.

We can also obtain a lower bound on the sigmoid having the functional form of a Gaussian. To do this, we follow Jaakkola and Jordan (2000) and make transformations both of the input variable and of the function itself. First we take the log of the logistic function and then decompose it so that

$$
\ln \sigma(x) = -\ln(1 + e^{-x}) = -\ln\left\{e^{-x/2}(e^{x/2} + e^{-x/2})\right\}
$$
  
=  $x/2 - \ln(e^{x/2} + e^{-x/2}).$  (10.138)

We now note that the function  $f(x) = -\ln(e^{x/2} + e^{-x/2})$  is a convex function of *Exercise 10.31* be variable  $x^2$ , as can again be verified by finding the second derivative. This leads to a lower bound on  $f(x)$ , which is a linear function of  $x^2$  whose conjugate function is given by

$$
g(\lambda) = \max_{x^2} \left\{ \lambda x^2 - f\left(\sqrt{x^2}\right) \right\}.
$$
 (10.139)

The stationarity condition leads to

$$
0 = \lambda - \frac{dx}{dx^2} \frac{d}{dx} f(x) = \lambda + \frac{1}{4x} \tanh\left(\frac{x}{2}\right). \tag{10.140}
$$

If we denote this value of  $x$ , corresponding to the contact point of the tangent line for this particular value of  $\lambda$ , by  $\xi$ , then we have

$$
\lambda(\xi) = -\frac{1}{4\xi} \tanh\left(\frac{\xi}{2}\right) = -\frac{1}{2\xi} \left[\sigma(\xi) - \frac{1}{2}\right].
$$
 (10.141)

Instead of thinking of  $\lambda$  as the variational parameter, we can let  $\xi$  play this role as this leads to simpler expressions for the conjugate function, which is then given by

$$
g(\lambda) = \lambda(\xi)\xi^2 - f(\xi) = \lambda(\xi)\xi^2 + \ln(e^{\xi/2} + e^{-\xi/2}).
$$
 (10.142)

Hence the bound on  $f(x)$  can be written as

$$
f(x) \ge \lambda x^2 - g(\lambda) = \lambda x^2 - \lambda \xi^2 - \ln(e^{\xi/2} + e^{-\xi/2}).
$$
 (10.143)

The bound on the sigmoid then becomes

$$
\sigma(x) \geqslant \sigma(\xi) \exp\left\{(x-\xi)/2 - \lambda(\xi)(x^2 - \xi^2)\right\}
$$
\n(10.144)

where  $\lambda(\xi)$  is defined by (10.141). This bound is illustrated in the right-hand plot of Figure 10.12. We see that the bound has the form of the exponential of a quadratic function of x, which will prove useful when we seek Gaussian representations of *Section 4.5* posterior distributions defined through logistic sigmoid functions.

The logistic sigmoid arises frequently in probabilistic models over binary variables because it is the function that transforms a log odds ratio into a posterior probability. The corresponding transformation for a multiclass distribution is given by *Section 4.3* the softmax function. Unfortunately, the lower bound derived here for the logistic sigmoid does not directly extend to the softmax. Gibbs (1997) proposes a method for constructing a Gaussian distribution that is conjectured to be a bound (although no rigorous proof is given), which may be used to apply local variational methods to multiclass problems.

> We shall see an example of the use of local variational bounds in Sections 10.6.1. For the moment, however, it is instructive to consider in general terms how these bounds can be used. Suppose we wish to evaluate an integral of the form

$$
I = \int \sigma(a)p(a) \, da \tag{10.145}
$$

where  $\sigma(a)$  is the logistic sigmoid, and  $p(a)$  is a Gaussian probability density. Such integrals arise in Bayesian models when, for instance, we wish to evaluate the predictive distribution, in which case  $p(a)$  represents a posterior parameter distribution. Because the integral is intractable, we employ the variational bound (10.144), which we write in the form  $\sigma(a) \geq f(a, \xi)$  where  $\xi$  is a variational parameter. The integral now becomes the product of two exponential-quadratic functions and so can be integrated analytically to give a bound on I

$$
I \geqslant \int f(a,\xi)p(a) da = F(\xi). \tag{10.146}
$$

We now have the freedom to choose the variational parameter  $\xi$ , which we do by finding the value  $\xi^*$  that maximizes the function  $F(\xi)$ . The resulting value  $F(\xi^*)$ represents the tightest bound within this family of bounds and can be used as an approximation to I. This optimized bound, however, will in general not be exact.

*Section 4.5*

*Section 4.3*

Although the bound  $\sigma(a) \geqslant f(a,\xi)$  on the logistic sigmoid can be optimized exactly, the required choice for  $\xi$  depends on the value of a, so that the bound is exact for one value of a only. Because the quantity  $F(\xi)$  is obtained by integrating over all values of a, the value of  $\xi^*$  represents a compromise, weighted by the distribution  $p(a)$ .

### **10.6. Variational Logistic Regression**

We now illustrate the use of local variational methods by returning to the Bayesian logistic regression model studied in Section 4.5. There we focussed on the use of the Laplace approximation, while here we consider a variational treatment based on the approach of Jaakkola and Jordan (2000). Like the Laplace method, this also leads to a Gaussian approximation to the posterior distribution. However, the greater flexibility of the variational approximation leads to improved accuracy compared to the Laplace method. Furthermore (unlike the Laplace method), the variational approach is optimizing a well defined objective function given by a rigourous bound on the model evidence. Logistic regression has also been treated by Dybowski and Roberts (2005) from a Bayesian perspective using Monte Carlo sampling techniques.

#### **10.6.1 Variational posterior distribution**

Here we shall make use of a variational approximation based on the local bounds introduced in Section 10.5. This allows the likelihood function for logistic regression, which is governed by the logistic sigmoid, to be approximated by the exponential of a quadratic form. It is therefore again convenient to choose a conjugate Gaussian prior of the form (4.140). For the moment, we shall treat the hyperparameters  $\mathbf{m}_0$  and  $\mathbf{S}_0$  as fixed constants. In Section 10.6.3, we shall demonstrate how the variational formalism can be extended to the case where there are unknown hyperparameters whose values are to be inferred from the data.

In the variational framework, we seek to maximize a lower bound on the marginal likelihood. For the Bayesian logistic regression model, the marginal likelihood takes the form

$$
p(\mathbf{t}) = \int p(\mathbf{t}|\mathbf{w})p(\mathbf{w}) \, d\mathbf{w} = \int \left[\prod_{n=1}^{N} p(t_n|\mathbf{w})\right] p(\mathbf{w}) \, d\mathbf{w}.
$$
 (10.147)

We first note that the conditional distribution for  $t$  can be written as

$$
p(t|\mathbf{w}) = \sigma(a)^{t} \{1 - \sigma(a)\}^{1-t}
$$
  
=  $\left(\frac{1}{1 + e^{-a}}\right)^{t} \left(1 - \frac{1}{1 + e^{-a}}\right)^{1-t}$   
=  $e^{at} \frac{e^{-a}}{1 + e^{-a}} = e^{at} \sigma(-a)$  (10.148)

where  $a = \mathbf{w}^T \boldsymbol{\phi}$ . In order to obtain a lower bound on  $p(\mathbf{t})$ , we make use of the variational lower bound on the logistic sigmoid function given by (10.144), which

### **10.6. Variational Logistic Regression 499**

we reproduce here for convenience

$$
\sigma(z) \geqslant \sigma(\xi) \exp\left\{(z-\xi)/2 - \lambda(\xi)(z^2 - \xi^2)\right\} \tag{10.149}
$$

where

$$
\lambda(\xi) = \frac{1}{2\xi} \left[ \sigma(\xi) - \frac{1}{2} \right].
$$
\n(10.150)

We can therefore write

$$
p(t|\mathbf{w}) = e^{at}\sigma(-a) \geq e^{at}\sigma(\xi) \exp\left\{-(a+\xi)/2 - \lambda(\xi)(a^2 - \xi^2)\right\}.
$$
 (10.151)

Note that because this bound is applied to each of the terms in the likelihood function separately, there is a variational parameter  $\xi_n$  corresponding to each training set observation  $(\phi_n, t_n)$ . Using  $a = \mathbf{w}^T \phi$ , and multiplying by the prior distribution, we obtain the following bound on the joint distribution of **<sup>t</sup>** and **w**

$$
p(\mathbf{t}, \mathbf{w}) = p(\mathbf{t}|\mathbf{w})p(\mathbf{w}) \ge h(\mathbf{w}, \xi)p(\mathbf{w})
$$
 (10.152)

where  $\xi$  denotes the set  $\{\xi_n\}$  of variational parameters, and

$$
h(\mathbf{w}, \boldsymbol{\xi}) = \prod_{n=1}^{N} \sigma(\xi_n) \exp \left\{ \mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}_n t_n - (\mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}_n + \xi_n) / 2 - \lambda(\xi_n) ([\mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}_n]^2 - \xi_n^2) \right\}.
$$
 (10.153)

Evaluation of the exact posterior distribution would require normalization of the lefthand side of this inequality. Because this is intractable, we work instead with the right-hand side. Note that the function on the right-hand side cannot be interpreted as a probability density because it is not normalized. Once it is normalized to give a variational posterior distribution  $q(\mathbf{w})$ , however, it no longer represents a bound.

Because the logarithm function is monotonically increasing, the inequality  $A \geqslant$ B implies  $\ln A \geqslant \ln B$ . This gives a lower bound on the log of the joint distribution of **<sup>t</sup>** and **w** of the form

$$
\ln \{p(\mathbf{t}|\mathbf{w})p(\mathbf{w})\} \ge \ln p(\mathbf{w}) + \sum_{n=1}^{N} \{ \ln \sigma(\xi_n) + \mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}_n t_n - (\mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}_n + \xi_n)/2 - \lambda(\xi_n) ([\mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}_n]^2 - \xi_n^2) \}.
$$
 (10.154)

Substituting for the prior  $p(\mathbf{w})$ , the right-hand side of this inequality becomes, as a function of **w**

$$
-\frac{1}{2}(\mathbf{w}-\mathbf{m}_0)^{\mathrm{T}}\mathbf{S}_0^{-1}(\mathbf{w}-\mathbf{m}_0)
$$
  
+
$$
\sum_{n=1}^N \left\{\mathbf{w}^{\mathrm{T}}\boldsymbol{\phi}_n(t_n-1/2) - \lambda(\xi_n)\mathbf{w}^{\mathrm{T}}(\boldsymbol{\phi}_n\boldsymbol{\phi}_n^{\mathrm{T}})\mathbf{w}\right\} + \text{const.} \qquad (10.155)
$$
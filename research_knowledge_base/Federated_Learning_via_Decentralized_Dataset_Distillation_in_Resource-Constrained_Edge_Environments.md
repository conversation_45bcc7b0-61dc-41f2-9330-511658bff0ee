# <span id="page-0-0"></span>Federated Learning via Decentralized Dataset Distillation in Resource-Constrained Edge Environments

Rui Song<sup>\*, 1, 2</sup>, <PERSON><sup>\*, 2</sup>, <PERSON><sup>2</sup>, <PERSON><sup>1, 3</sup>, <PERSON><PERSON><sup>2</sup>, <PERSON><sup>2</sup>, <PERSON><PERSON><sup>2</sup>

<sup>1</sup>Fraunhofer IVI <sup>2</sup>Technical University of Munich <sup>3</sup>Technische Hochschule Ingolstadt

{rui.song, andreas.festag}@ivi.fraunhofer.de {rui.song, dai.liu, zhenyu.chen, carsten.trinitis}@tum.de, {schulzm, knoll}@in.tum.de, <EMAIL>

*Abstract*—In federated learning, all networked clients contribute to the model training cooperatively. However, with model sizes increasing, even sharing the trained partial models often leads to severe communication bottlenecks in underlying networks, especially when communicated iteratively. In this paper, we introduce a federated learning framework *FedD3* requiring only one-shot communication by integrating dataset distillation instances. Instead of sharing model updates in other federated learning approaches, *FedD3* allows the connected clients to distill the local datasets independently, and then aggregates those decentralized distilled datasets (e.g. a few unrecognizable images) from networks for model training. Our experimental results show that *FedD3* significantly outperforms other federated learning frameworks in terms of needed communication volumes, while it provides the additional benefit to be able to balance the trade-off between accuracy and communication cost, depending on usage scenario or target dataset. For instance, for training an AlexNet model on CIFAR-10 with 10 clients under nonindependent and identically distributed (Non-IID) setting, *FedD3* can either increase the accuracy by over 71% with a similar communication volume, or save 98% of communication volume, while reaching the same accuracy, compared to other one-shot federated learning approaches.

## I. INTRODUCTION

Federated learning has become an emerging paradigm for collaborative learning in large-scale distributed systems with a massive number of networked clients, such as smartphones, connected vehicles or edge devices. Due to the limited bandwidth between clients [\[1\]](#page-6-0), previous research [\[2\]](#page-7-0)–[\[8\]](#page-7-1) attempts to speed up convergence and improve communication efficiency. However, for modern neural networks with over hundreds of million parameters, this kind of cooperative optimization still leads to extreme communication volumes, which require substantial network bandwidth (up to the Gbps level [\[9\]](#page-7-2)) in order to work reliably and efficiently. This drawback hinders any large-scale deployment of federated

<sup>∗</sup>Equal Contribution.

learning models in commercial wireless mobile networks, e.g., vehicular communication networks [\[10\]](#page-7-3) or industrial sensor networks [\[11\]](#page-8-0).

Motivated by this communication bottleneck, prior federated learning algorithms attempt to reduce the number of communication rounds and with that the communication volume, to reach a good learning performance. Guha et al. [\[12\]](#page-8-1) introduce a one-shot federated learning approach aimed at reducing communication overhead during the training process of a support vector machine (SVM). By exchanging information in a single communication round, it offers significant efficiency improvements. Kasturi et al. [\[13\]](#page-8-2) provide a fusion federated learning method that uploads both model and data distribution to the server, but characterizing a distribution of a real dataset can be difficult. The one-shot federated learning based on knowledge transfer from Li et al. [\[14\]](#page-8-3) is general, but it requires additional communication overhead to transmit multiple student models to the server.

Inspired by the one-shot scheme [\[12\]](#page-8-1), we introduce a federated learning training scheme with one-shot communication via dataset distillation [\[15\]](#page-8-4)–[\[18\]](#page-8-5). Intuitively, dramatically smaller but more informative datasets, which include dense features, are synthesized and transmitted. This way, more informative training data is transmitted across the limited bandwidth without any privacy violation.

Specifically, we introduce a novel federated learning framework incorporating dataset distillation, *FedD3*, which is shown in Fig. [1.](#page-1-0) It enables efficient federated learning via transmitting the locally distilled dataset to the server in a one-shot manner, which can be applied as a pre-trained model and used for personalized [\[19\]](#page-8-6), [\[20\]](#page-8-7) and fairness-aware [\[21\]](#page-8-8) learning. Note that dataset distillation can keep the advantage of privacy in federated learning [\[22\]](#page-8-9)–[\[30\]](#page-8-10). It anonymously maps distilled datasets from the original client data without any exposure, which is analogous to the shared model parameters in previous federated learning methods, but substantially more efficient and effective.

This work was supported by the German Federal Ministry for Digital and Transport (BMVI) in the projects "KIVI – KI im Verkehr Ingolstadt" and "5GoIng – 5G Innovation Concept Ingolstadt".

Image /page/1/Figure/0 description: The image depicts a federated learning system with 10 clients, showcasing the results of decentralized distilled datasets on CIFAR10 and MNIST. The top section illustrates a cloud server uploading a model for training, which then branches into applications for a pre-trained model, a personalized model, and a faired model. Below this, a gray bar labeled "One Shot Upload in Federation" connects to 10 client columns. Each client column displays two rows of image samples: the top row shows CIFAR10 images labeled with object classes like 'airplane', 'automobile', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', and 'truck'. The bottom row displays MNIST images, represented by grayscale digit images labeled with numbers 0 through 9. Arrows point from the MNIST images upwards to a gray bar labeled "Decentralized Dataset Distillation in each Federated Client", which in turn connects to individual "Raw dataset" icons for each client. The figure is captioned as "Fig. 1. Decentralized distilled datasets on federated CIFAR10 and MNIST divided into 10 clients. Each has only 2 classes of data and the class combination is different for each client."

<span id="page-1-0"></span>Fig. 1. Decentralized distilled datasets on federated CIFAR10 and MNIST divided into 10 clients. Each has only 2 classes of data and the class combination in each client is individual. We distill one image per class in each of 10 clients.

We perform an extensive analysis of our method in various scenarios to showcase the effectiveness of our method in massively distributed systems and on Non-IID (non-independent and identically distributed) datasets. Specifically, our experiments highlight the trade-off between accuracy and communication cost. To handle this trade-off, we propose a new evaluation metric, the  $\gamma$ -accuracy gain. By tuning the importance of accuracy gain  $\gamma$  to the communication cost, the communication efficiency in federated learning is scored accordingly. We also investigate the effects of specific external parameters, including Non-IID datasets, number of clients and local contributions, and demonstrate a great potential for our framework in networks with constrained communication budgets in federated learning. We experimentally show that *FedD3* has the following advantages: (*i*) Compared to conventional multi-shot federated learning, *FedD3* significantly reduces the amount of bits that needs to be communicated, making our approach practically feasible even in low-bandwidth environments; (*ii*) Compared to other approaches for one-shot

federated learning, *FedD3* achieves a much better performance even with less communication volume, where the accuracy in a distributed system with 500 clients is enhanced by over 2.3 $\times$  (from 42.08% to 94.74%) on Non-IID MNIST and 3.6 $\times$ (from 10.74% to 38.27%) on Non-IID CIFAR-10 compared to *FedAvg* in one single round; (*iii*) Compared to centralized dataset distillation, *FedD3* achieves much better results due to the broader data resource via federated learning.

Contributions To summarize, our contributions are as fourfold:

- We introduce a decentralized dataset distillation scheme in federated learning systems, where distilled data instead of models are uploaded to the server;
- We formulate and propose a novel framework, *FedD3*, for efficient federated learning in a one-shot manner, and demonstrate *FedD3* with two different dataset distillation instances in clients;
- We propose a novel evaluation metric  $\gamma$ -accuracy gain, which can be used to tune the importance of accuracy

and analyze communication efficiency;

• We conduct an extensive analysis of the proposed framework. The experiments showcase the great potentials of our framework in networks with constrained communication budget in federated learning, especially considering the trade-off between accuracy and communication costs. The software implementation of *FedD3* is publicly avail-able as open source at GitHub<sup>[1](#page-0-0)</sup>.

## II. BACKGROUND AND RELATED WORK

Federated learning Federated learning was first introduced by McMahan et al. [\[1\]](#page-6-0), where models can be learned collaboratively from decentralized data through model exchange between clients and a central server without violating privacy. The proposed federated learning scheme *FedAvg* [\[1\]](#page-6-0) aggregates the received models and updates global model by averaging their parameters.

Compared to other distributed optimization approaches, federated optimization addresses more practical challenges, e.g., communication efficiency [\[2\]](#page-7-0), data heterogeneity [\[31\]](#page-8-11), privacy protection [\[32\]](#page-8-12), system design [\[33\]](#page-8-13), which enables a largescale deployment in real-world application scenarios [\[34\]](#page-8-14)– [\[43\]](#page-8-15).

In a federated learning scenario, given a set of clients indexed by  $k$ , machine learning models with weights  $w_k$  are trained individually on local client datasets  $\mathcal{D}_k = \{(x_i)|i =$  $1, 2, \ldots, n_k$ , where  $x_i$  is one data point with its label  $y_i$  in client k and  $n_k$  is the number of the local data points. The goal of local training in client  $k$  is to minimize

<span id="page-2-0"></span>
$$
F_k(w) = \frac{1}{n_k} \sum_{i=1}^{n_k} f_i(w)
$$
 (1)

where  $f_i(w)$  is the loss function on one data point  $x_i$  with the label  $y_i$ . Finally, the goal is to minimize aggregated local goals  $F_k(w)$  in [\(1\)](#page-2-0):

$$
f(w) = \sum_{k=1}^{m} \frac{n_k}{n} F_k(w)
$$
 (2)

i.e.  $f(w) = \mathbb{E}_{\mathcal{D}_k}[F_k(w)]$ . Note that the datasets across clients can be Non-IID in federated learning.

However, most federated optimization methods exchange models or gradients for learning updates, which can still lead to excessive communication volumes when a model has numerous parameters. This is even more problematic in wireless networks common in many mobile applications, as frequently exchanging data leads to higher error likelihood when connections are unstable, which can cause federated learning to fail.

One-shot Federated Learning Federated learning with oneshot communication has been studied in several projects [\[12\]](#page-8-1)– [\[14\]](#page-8-3). Guha et al. [\[12\]](#page-8-1) introduce an algorithm for training a support vector machine in one-shot fashion. The framework proposed by Kasturi et al. [\[13\]](#page-8-2) uploads models and

additionally the local dataset distribution, which hard to do when training on real datasets. Li et al. [\[14\]](#page-8-3) utilize knowledge transfer to distill models in each client, which can change the original model structure and may cause much communication cost for sharing student models.

Instead of distilling models, our framework distills input data into smaller synthetic datasets in clients, which can be used for training a more general model by only relying on one-shot communication.

Dataset Distillation Dataset distillation [\[15\]](#page-8-4) has become an attractive paradigm. It attempts to synthesize a significantly smaller dataset from a large dataset, aiming to maintain the same training performance in terms of test accuracy. Dataset distillation was proposed by Wang et al. [\[15\]](#page-8-4). Methods like matching outputs or gradients [\[16\]](#page-8-16), [\[17\]](#page-8-17), [\[44\]](#page-8-18), [\[45\]](#page-8-19) have been achieving outstanding results. Beside updating synthetic datasets with forward and backward propagation, Nguyen et al. [\[18\]](#page-8-5) perform Kernel Inducing Points (KIP) and Label Solve (LS) for the optimal solution. Relating back to our work, dataset distillation has been successfully applied in centralized training in limited fashion.

Dataset Distillation in Federated Learning Though some previous works in dataset distillation, e.g. [\[44\]](#page-8-18), have mentioned the dataset distillation might be beneficial for federated learning, they have not provided detailed analysis and experimental evaluation on it. Only little research has explored the dataset distillation approaches in federated learning: Zhou et al. [\[46\]](#page-8-20) and Goetz et al. [\[47\]](#page-8-21) have attempted to employ the approaches proposed by Wang et al. [\[15\]](#page-8-4) ,and Sucholutsky and Schonlau [\[48\]](#page-8-22) in federated learning, respectively. However, more advanced methods, e.g. [\[18\]](#page-8-5) [\[16\]](#page-8-16) with more stable performance have been developed, and further studies on decentralized dataset distillation in federated settings are needed. Furthermore, both of them have not pointed out the dataset distillation can improve the training on heterogeneous data, which is one of the biggest challenges in federated learning.

In fact, the computation abilities in distributed edge devices are normally limited, while most dataset distillation methods require high computation power. For instance, the approach from [\[16\]](#page-8-16) can lead to computation overhead, though it can generate satisfactory distilled dataset. Therefore, in this work, we consider the coreset-based and KIP-based [\[18\]](#page-8-5), [\[49\]](#page-8-23) methods for decentralized dataset distillation in our federated learning framework, and focus on improving the communication efficiency while training on federated datasets.

## III. FEDD3: FEDERATED LEARNING FROM DECENTRALIZED DISTILLED DATASETS

To explore the dataset distillation in federated settings, we introduce *FedD3*, a federated learning framework from decentralized distilled datasets in this section. Specifically, we consider a joint learning task with  $m$  clients, where the client k owns local dataset  $\mathcal{D}_k$ . If we distill a synthetic dataset  $\tilde{\mathcal{D}}_k$  ( $\tilde{n}_k = |\tilde{\mathcal{D}}_k|$ ), in the client k from its local dataset  $\mathcal{D}_k$  $(n_k = |\mathcal{D}_k|)$ , the goal of the dataset distillation instance is to

<sup>1</sup><https://github.com/rruisong/FedD3.git>

<span id="page-3-0"></span>Algorithm 1 *FedD3*: Federated Learning from Decentralized Distilled Datasets

Server Input: number of the learning epochs E **Server Input:** learning rate  $\eta$ **Client Input:** number of the client  $m$ **Client Input:** number of the distillation epochs  $E_k$ Client Input: distillation rate  $\eta_k$ **Client Input:** decentralized dataset  $\mathcal{D}_k$ Output:  $\tilde{w}$ 1: Server: 2: select instance with hyper-parameters Θ 3: for  $k \in \{1, 2, ..., m\}$  in parallel do 4:  $\tilde{D}_k \leftarrow$  ClientDatasetDistillation(k,  $\Theta$ ) 5: end for 6:  $\tilde{\mathcal{D}} \leftarrow aggregate(\tilde{\mathcal{D}}_1, \tilde{\mathcal{D}}_2, ..., \tilde{\mathcal{D}}_m)$ 7: for epoch  $e = 1, 2, ..., E$  do 8: **for** each batch  $\tilde{\mathcal{B}} = (\tilde{X}_b, \tilde{y}_b)$  of  $\tilde{\mathcal{D}}$  **do** 9:  $\tilde{w} \leftarrow \tilde{w} - \eta (\nabla l(\tilde{X}_b, \tilde{y}_b; \tilde{w}))$ 10: end for 11: end for 12: **return**  $\tilde{w}$ 13: ClientDatasetDistillation(k, Θ) 14:  $\Theta_k \leftarrow \Theta$ 15: initialize  $\tilde{D}_k$  with a subset of  $D_k$  and  $\tilde{n}_k = |\tilde{D}_k|$ 16: **for** epoch  $e_k = 1, 2, ..., E_k$  **do** 17: **for** each pair of batches  $\tilde{\mathcal{B}}_k = (\tilde{X}_b, \tilde{y}_b)$  of  $\tilde{\mathcal{D}}_k$  and  $\mathcal{B}_k =$  $(X_b, y_b)$  of  $\mathcal{D}_k$  do 18:  $\tilde{X}_b \leftarrow \tilde{X}_b - \eta_k \left( \frac{\partial H_k(\tilde{X}_b, \tilde{y}_b; \Theta_k)}{\partial \tilde{Y}_b} \right)$  $\frac{\lambda_b, y_b; \Theta_k)}{\partial \tilde{X}_b}$ 19: update  $\tilde{\mathcal{B}}_k$  and  $\tilde{\mathcal{D}}_k$  with  $\tilde{X}_b$ 20: end for 21: if converged then 22: break 23: end if 24: end for 25: **return**  $\tilde{D}_k$ 

minimize  $H_k(\tilde{X}_k, \tilde{y}_k; \Theta_k)$ , where the  $\tilde{X}_k$  represents the matrix of stacked distilled data points in the client k,  $\tilde{x}_{k,i}$ ,  $\tilde{y}_k$  contains the corresponding labels, and  $\Theta_k$  indicates a set of parameters in the instance models. Note that  $H_k$  can vary, depending on the instance used in the client  $k$ .

### A. Coreset-based Methods

We start from coreset-based methods to distill the decentralized datasets. We assume that there exists a synthetic dataset  $\tilde{\mathcal{D}}_k$ , which can approximate the statistical distribution of the original dataset  $\mathcal{D}_k$ . Through minimizing the error in a small subset of  $\mathcal{D}_k$ , we can generate the coreset  $\tilde{\mathcal{D}}_k$  using a specific instance, e.g. Kernel Herding [\[50\]](#page-8-24). More generally, we consider a clustering-based methods to generate a coreset in the client k for one of classes  $s \in S_k$  ( $S_k$  is the set of local classes), then the goal is to minimize a clustering loss, for instance generating coreset using a Gaussian mixture model (GMM) [\[51\]](#page-9-0) for each class  $s$  in all clients.

### B. KIP-based Methods

We review and adopt KIP [\[18\]](#page-8-5), [\[49\]](#page-8-23) to federated fashion for its fast divide up gradient computation. Each client  $k$  aims to distill the original local dataset (a.k.a. target dataset)  $D$  to a synthetic dataset (a.k.a. support dataset)  $\tilde{\mathcal{D}}$  by minimizing the kernel ridge-regression (KRR) loss  $H_k(\tilde{X}_k)$  as follows:

$$
H_k(\tilde{X}_k) = \frac{1}{2} ||y_k - K_k(X_k, \tilde{X}_k)(K_k(\tilde{X}_k, \tilde{X}_k) + \lambda_k I)^{-1} \tilde{y}_k||_2^2 \quad (3)
$$

where  $K_k$  is the kernel used in the client k and  $\lambda_k$  is the regularization constant deduced by ridge regression in *KIP*.  $I$  is an identity matrix. We refer the readers to the work of Nguyen et al. [\[49\]](#page-8-23) for further details regarding kernels.

### C. Aggregation and Learning

After decentralized dataset distillation, the distilled datasets in all connected clients are transmitted to the server and aggregated as  $\tilde{\mathcal{D}} = {\{\tilde{\mathcal{D}}_k | k = 1, 2, ..., m\}}$ . We consider a non-convex neural network objective in the server and train a machine learning model on  $\overline{\mathcal{D}}$  instead of on original dataset  $D$ , the objective is then minimizing:

$$
f(w) = \frac{1}{\tilde{n}} \sum_{k=1}^{m} \sum_{i=1}^{\tilde{n}_k} f_i(w) = \mathbb{E}_{\tilde{D}}[f_i(w)] \tag{4}
$$

where  $f_i(w) = l(\tilde{x}_i, \tilde{y}_i; w)$  is the loss of the prediction on one distilled data point  $\tilde{x}_i$  with its label  $\tilde{y}_i$  and model weights w. If the  $\tilde{X}_k$  could be distilled from  $X_k$  perfectly, the learning result of minimizing  $f(w)$  on  $\ddot{D}$  should be similar to it on  $D$ . The *FedD3* pseudocode is given in Algorithm [1.](#page-3-0)

#### D. Gamma Communication Efficiency

In federated learning, communication cost is even more expensive than computational cost [\[1\]](#page-6-0). It is worth studying how much communication cost is needed for achieving a dedicated gain of the model performance in terms of accuracy. To tackle the trade-off between model performance and required communication cost, we define the *Gamma Communication Efficiency* (GCE) using γ-accuracy gain per binary logarithmic bit as follows:

$$
GCE = \frac{ACC}{(1 - ACC)^{\gamma} * \sum_{t=1}^{T} \log_2(V_t + 1)}
$$
(5)

where  $T \in \mathbb{N}_+$  is the total communication rounds and  $V_t \in$  $\mathbb{R}_+$  is the required communication volume in each round.

We use the binary logarithmic bit  $\sum_{t=1}^{T} \log_2(V_t + 1)$  to describe the communication cost from communication round  $t = 1$  to T. Then the gain per binary logarithmic bit can be defined by  $1/\sum_{t=1}^{T} \log_2(\hat{V}_t + 1)$ , where the 0 communication cost gives us infinitive high gain and infinitive high communication cost lead to  $0$  gain.  $ACC$  is the prediction accuracy value.  $\gamma \in \mathbb{R}_+$  is an tunable parameter to represent the importance of the prediction accuracy. If  $\gamma \to 0$ , the accuracy and communication cost in logarithmic bit is near proportional. The higher  $\gamma$  is defined, the more test accuracy

<span id="page-4-0"></span>TABLE I THE BASELINES AND THE CORRESPONDING BINARY LOGARITHMIC BIT FOR ONE COMMUNICATION ROUND, WHERE  $P$  is the BIT size of MODELS

| FedAvg        | FedProx       | FedNova       | SCAFFOLD        |
|---------------|---------------|---------------|-----------------|
| $\log_2(P+1)$ | $\log_2(P+1)$ | $\log_2(P+9)$ | $\log_2(2*P+1)$ |

is weighted. A tiny test accuracy gain can be scored very well with an infinitely high  $\gamma$ . Through selecting an appropriate  $\gamma$ , we can evaluate the performance of federated learning approaches, considering both test accuracy and communication cost based on the application scenarios.

## IV. EXPERIMENT

### A. Experimental Settings

We conduct experiments mainly on MNIST [\[52\]](#page-9-1) and CIFAR-10 [\[53\]](#page-9-2), as they are widely used for federated learning evaluation. For *FedD3*, we use GMM for coreset generation in coreset-based instance [\[51\]](#page-9-0) and employ a four-layer fully connected neural network model with the width of 1024 as instance for KIP-based instance [\[18\]](#page-8-5).

We compare *FedD3* with eight other baselines, where four federated learning methods are evaluated in both multi-shot federated learning (MSFL) and one-shot federated learning (OSFL). The federated learning methods are: FedAvg [\[1\]](#page-6-0), Fed-Prox [\[54\]](#page-9-3), FedNova [\[55\]](#page-9-4) and SCAFFOLD [\[31\]](#page-8-11). The required binary logarithmic bit for communication is shown in Tab. [I.](#page-4-0) We train an LeNet [\[56\]](#page-9-5) and an AlexNet [\[57\]](#page-9-6) on MNIST and CIFAR-10, respectively.

Considering properties in federated learning [\[1\]](#page-6-0), we demonstrate our methods and baselines on IID and Non-IID datasets in distributed systems with a different number of clients. We vary the number of classes in each client  $C_k$  to design Non-IID datasets [\[58\]](#page-9-7). Guided by federated learning benchmark datasets made by Caldas et al. [\[59\]](#page-9-8), we use Non-IID to denote  $C_k = 2$  (pathological Non-IID [\[19\]](#page-8-6)), unless specified otherwise.

### B. Robust Training on Heterogeneous Data

First, we focus on the performance of *FedD3* on data heterogeneity and consider *FedAvg* as the baseline here. The accuracy changing with increasing total communication volume required in each method is observed in Fig, [2.](#page-5-0) In experiment with IID decentralized datasets, though *FedAvg* with unlimited communication cost has higher accuracy than *FedD3*, *FedD3* outperforms *FedAvg* at the same communication volume. The best performance of one-shot *FedAvg* can be reached by using *FedD3* with only around half of the communication volume. Note that we consider the communication volume only for uploading. In fact, MSFL methods still require further cost for downloading the global models.

Additionally, as shown in Fig. [2,](#page-5-0) the performance of *FedD3* is obviously more robust than *FedAvg*, when the data heterogeneity across clients increases from left to right. With

decreasing number of classes in local datasets, the prediction accuracy *FedAvg* reduces notably, however, the results of *FedD3* are not much affected. In the scenario with extreme data heterogeneity, i.e.  $C_k = 1$ , the standard federated learning can even not easily converged and one-shot federated learning performances very badly, while *FedD3* can achieve the similar accuracy as in IID scenarios. We believe the reason is that aggregating distilled datasets allows server to train a model on a similar distribution of original datasets.

### C. Scalable Communication Efficiency

Then, we compare the results from 2 variants of *FedD3* with 8 baselines on both IID and Non-IID, MNIST and CIFAR10 distributed in 500 clients. As shown in Tab. [II,](#page-5-1) on IID datasets, *FedD3* can achieve a comparable test accuracy to other federated learning, while the GCE is significantly higher. On Non-IID datasets, *FedD3* is outperforms others for both test accuracy and GCE. We consider various  $\gamma$  to indicate the evaluation based on different importance of accuracy. We assign  $\gamma$  for CIFAR-10 with greater values than for MNIST, as a high accuracy for training model on CIFAR-10 is harder to achieve and should deserve to spend more communication cost for it.

We conduct further experiments for training a ResNet-18 [\[60\]](#page-9-9) on Fashion-MNIST [\[61\]](#page-9-10) and SVHN [\[62\]](#page-9-11), and training a CNN consisting of 5 convolutional and 3 fully connected layers on CIFAR-100 [\[62\]](#page-9-11), using MSFL and OSFL with FedAvg, and *FedD3* with KIP-instances. As shown in Tab. [III,](#page-6-1) we observe consistent results.

To perform the scalablity of communication efficiency, we meanwhile evaluate *FedD3* with increasing number of images per class in each client. Fig. [3](#page-6-2) shows the test accuracy raises and thereby GCE with higher  $\gamma$  increases, when each client contributes more distilled images to the server. However, GCE with  $\gamma = 0.01$  reduces, because much communication cost is required for only a small accuracy gain. *FedD3* provides the opportunity to optimize the GCE by adjusting the Img/Cls in decentralized dataset distallation, considering different importance of accuracy and constraint communication budget in practical applications.

#### D. Evaluation with System Parameters

Finally, we explore the performance of *FedD3* affected by federated system parameters, including the number of clients and distilled images. In Fig. [4,](#page-7-4) we run *FedD3* in federated systems containing various number of clients with IID and Non-IID decentralized datasets. We hold the total number of distilled images constant by varying the number of distilled images in each client. Fig. [4](#page-7-4) shows that the prediction accuracy decreases at a larger number of clients. This can be led by the following reason: When increasing the client number, both the local data volume and distilled image per client reduce in our experimental setup. This results in lower granularity and thereby decreases prediction accuracy.

In fact, federated learning considers massively distributed systems in practical applications. Thus, even if each client

Image /page/5/Figure/0 description: This figure displays four plots for MNIST and four plots for CIFAR-10, each comparing the test accuracy against the total communication volume in megabytes. The top row of plots is for MNIST, and the bottom row is for CIFAR-10. Each row has four columns, representing different communication costs: IID (Ck=10), Non-IID (Ck=5), Non-IID (Ck=2), and Non-IID (Ck=1). Each plot shows two sets of data points: 'OS-FedAvg' (teal dots) and 'Our Framework' (pink dots). An orange shaded area labeled 'Envelope area of FedAvg results' is present in all plots, indicating the range of results for FedAvg. The first plot in the MNIST row, labeled 'IID (Ck=10)', shows 'Our Framework' achieving high accuracy from the start, while 'OS-FedAvg' also reaches high accuracy with a small communication volume. An inset graph in this plot zooms into the initial part of the data. The other MNIST plots show similar trends, with 'Our Framework' generally performing well. The CIFAR-10 plots show that 'Our Framework' achieves higher test accuracy than 'OS-FedAvg' across different communication costs, especially at lower communication volumes. The dashed black lines in the plots represent target accuracy levels.

<span id="page-5-0"></span>Fig. 2. Test accuracy changes with increasing total communication volume in Mb required in each method on MNIST and CIFAR-10. The training results are shown in two rows of four figures, where the data heterogeneity is characterized by  $C_k = 10, 5, 2, 1$  from left to right. We demonstrate *FedD3* with different message sizes in one-shot communication as green points. The red points are the results in one-shot FedAvg (OS-FedAvg) with different number of local epochs as baselines here. Additionally, we run standard federated learning with corresponding epochs for local training and mark the envelope area of the results in orange area. Note that the communication volume caused in  $FedD3$  by transmitting distilled images (1×8 or 3×8 bits for each gray-scale or RGB pixel), however in other federated learning by transmitting model parameters (32 bits for each parameter). For multi-shot federated learning, the total communication volume raises with increasing communication rounds.

<span id="page-5-1"></span>TABLE II THE PREDICTION ACCURACY (ACC) AND γ−GCE COMPARISON BETWEEN *FedD3* AND OTHER BASELINES ON BOTH MNIST AND CIFAR-10 DISTRIBUTED IN 500 CLIENTS.

| Dataset      |              | Metric        | MSEL <sup>1</sup> |                  |                  | <b>OSFL</b>      |                  |                  | FedD3 $(Ours)^2$ |                  |                  |                  |
|--------------|--------------|---------------|-------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|------------------|
|              |              |               | FedAvg            | FedProx          | FedNova          | <b>SCAFFOLD</b>  | FedAvg           | FedProx          | FedNova          | <b>SCAFFOLD</b>  | Coreset          | <b>KIP</b>       |
| <b>MNIST</b> | $\mathsf{e}$ | $ACC\%$       | $96.97 \pm 0.02$  | $96.55 \pm 0.03$ | $85.50 \pm 0.01$ | $97.49 \pm 0.02$ | $85.34 \pm 0.02$ | $83.63 \pm 0.02$ | $74.78 \pm 0.02$ | $67.47 \pm 5.89$ | $86.82 \pm 0.46$ | $94.37 \pm 0.67$ |
|              |              | $0.01$ -GCE % | $0.27 \pm 0.01$   | $0.27 \pm 0.00$  | $0.69 \pm 0.00$  | $0.13 \pm 0.00$  | $4.16 \pm 0.00$  | $4.07 \pm 0.00$  | $3.63 \pm 0.00$  | $1.63 \pm 0.15$  | $5.56 \pm 0.03$  | $6.09 \pm 0.05$  |
|              |              | $0.5$ -GCE %  | $1.48 \pm 0.00$   | $1.38 \pm 0.01$  | $1.79 \pm 0.00$  | $0.82 \pm 0.00$  | $10.66 \pm 0.01$ | $9.88 \pm 0.01$  | $7.12 \pm 0.01$  | $2.85 \pm 0.54$  | $15.01 \pm 0.34$ | $24.99 \pm 1.78$ |
|              | Non-IID      | ACC $%$       | $71.29 \pm 0.02$  | $67.54 \pm 0.04$ | $71.33 \pm 0.08$ | $88.69 \pm 0.12$ | $42.08 \pm 0.03$ | 49.25±0.01       | $63.61 \pm 0.75$ | $36.92 \pm 0.03$ | $77.29 \pm 2.58$ | $94.74 \pm 0.64$ |
|              |              | $0.01$ -GCE % | $0.20 \pm 0.00$   | $0.20 \pm 0.00$  | $0.20 \pm 0.00$  | $0.12 \pm 0.00$  | $2.02 \pm 0.00$  | $2.37 \pm 0.00$  | $3.07 \pm 0.04$  | $0.89 \pm 0.00$  | $5.76 \pm 0.20$  | $7.17 \pm 0.06$  |
|              |              | $0.5$ -GCE %  | $0.31 \pm 0.00$   | $0.31 \pm 0.00$  | $0.30 \pm 0.00$  | $0.35 \pm 0.00$  | $2.64 \pm 0.00$  | $3.31 \pm 0.00$  | $5.04 \pm 0.11$  | $1.11 \pm 0.00$  | $11.95 \pm 1.13$ | $30.43 \pm 2.17$ |
| CIFAR-10     |              | ACC $%$       | $48.12 \pm 0.38$  | 47.89±0.27       | $47.69 \pm 0.80$ | $41.89 \pm 0.11$ | $36.23 \pm 0.60$ | $36.26 \pm 0.63$ | $36.59 \pm 0.27$ | $24.33 \pm 0.01$ | $46.18 \pm 0.08$ | $48.97 \pm 0.83$ |
|              | $\mathbf{r}$ | $0.1$ -GCE %  | $0.60 \pm 0.76$   | $0.33 \pm 0.00$  | $0.42 \pm 0.06$  | $0.14 \pm 0.00$  | $1.47 \pm 0.03$  | $1.47 \pm 0.03$  | $1.49 \pm 0.01$  | $0.49 \pm 0.00$  | $2.74 \pm 0.01$  | $2.92 \pm 0.05$  |
|              |              | 2-GCE $%$     | $1.60 \pm 1.35$   | $1.14 \pm 0.02$  | $1.34 \pm 0.21$  | $0.40 \pm 0.00$  | $3.46 \pm 0.12$  | $3.47 \pm 0.13$  | $3.54 \pm 0.06$  | $0.83 \pm 0.00$  | $8.91 \pm 0.04$  | $10.50 \pm 0.53$ |
|              | Non-IID      | ACC $%$       | $13.14 \pm 5.02$  | $18.46 \pm 0.83$ | $12.98 \pm 5.00$ | $34.27 \pm 0.04$ | $10.73 \pm 0.01$ | $10.71 \pm 0.01$ | $10.72 \pm 0.01$ | $10.05 \pm 0.00$ | $30.32 \pm 1.43$ | $38.27 \pm 1.45$ |
|              |              | $0.1$ -GCE %  | $0.35 \pm 0.04$   | $0.19 \pm 0.04$  | $0.39 \pm 0.00$  | $0.12 \pm 0.00$  | $0.42 \pm 0.00$  | $0.42 \pm 0.00$  | $0.42 \pm 0.00$  | $0.20 \pm 0.00$  | $2.02 \pm 0.10$  | $2.58 + 0.10$    |
|              |              | 2-GCE $%$     | $0.44 \pm 0.04$   | $0.24 \pm 0.05$  | $0.48 \pm 0.00$  | $0.26 \pm 0.00$  | $0.52 \pm 0.00$  | $0.52 \pm 0.00$  | $0.52 \pm 0.00$  | $0.24 \pm 0.00$  | $4.01 \pm 0.36$  | $6.45 \pm 0.56$  |

We select the best result in the first 18 and 6 communication rounds for the training on MNIST and CIFAR-10, respectively.

 $2$  Each client contributes 1 distilled image per class (Img/Cls = 1) from its local dataset.

provides a small number of distilled images, a promising training performance can be achieved, when the number of clients is large. As we can observe in Fig. [4,](#page-7-4) when each client provides the same number of distilled images, a better model can be trained with more clients, due to more distilled images received in the server. Moreover, additional clients in real applications can enrich the training dataset, and hence reach a better prediction accuracy, which is consistent with the motivation of deploying federated learning.

## V. DISCUSSION

Distilled Datasets in Multi-round Due to the robustness on data heterogeneity, we further explore the potential benefits of distilled datasets in federated learning. We believe that sharing such synthetic data might bridge the information silos. For that, we extend *FedD3* to multiple shots and consider a hybrid

#### TABLE III

<span id="page-6-1"></span>THE PREDICTION ACCURACY (ACC) AND  $\gamma$ −GCE COMPARISON BETWEEN *FedD3* AND OTHER BASELINES ON FASHION-MNIST AND CIFAR-100 DISTRIBUTED IN 200 CLIENTS, AND ON SVHN DISTRIBUTED IN 100 CLIENTS.

| Dataset<br>Metric<br><b>MSFL</b><br>OSFL<br>FedD3<br>$79.40 + 0.00$<br>$74.80 + 0.75$<br>Acc $%$<br>$69.69 + 0.07$<br>Fashion-MNIS7<br>且<br>$0.01$ -GCE %<br>$0.95 \pm 0.00$<br>$4.65 \pm 0.05$<br>$2.48 + 0.00$<br>$0.5$ -GCE %<br>$2.05 \pm 0.00$<br>$4.45 \pm 0.01$<br>$9.13 \pm 0.23$<br>Acc $%$<br>$40.69 \pm 0.01$<br>$27.92 \pm 0.00$<br>$76.78 \!\pm\! 0.98$<br>$Non-III$<br>$0.01$ -GCE %<br>$0.48 + 0.00$<br>$5.56 \pm 0.07$<br>$0.99 \pm 0.00$<br>$0.5$ -GCE %<br>$0.62 \pm 0.00$<br>$11.39 + 0.39$<br>$1.16 + 0.00$<br>$80.99 \pm 0.00$<br>Acc $%$<br>$25.00 \pm 0.49$<br>$80.42 \pm 0.63$<br>≘<br>0.01-GCE %<br>$0.16 + 0.00$<br>$3.85 + 0.03$<br>$0.44 + 0.01$<br><b>SVHN</b><br>$0.5$ -GCE %<br>$0.36 \pm 0.00$<br>$0.51 + 0.01$<br>$8.56 + 0.21$<br>$46.32 \pm 0.06$<br>$19.96 + 0.05$<br>$69.10 + 0.98$<br>Acc $%$<br>$\mathsf{Non-III}$<br>$0.01$ -GCE %<br>$0.10 \pm 0.01$<br>$0.35 \pm 0.00$<br>$3.69 \pm 0.05$<br>$0.5$ -GCE %<br>$6.56 \pm 0.02$<br>$0.13 \pm 0.02$<br>$0.39 \pm 0.01$<br>$41.15 + 0.03$<br>Acc $%$<br>$20.21 + 0.06$<br>$47.89 + 0.42$<br>$0.1$ -GCE %<br>$0.15 \pm 0.01$<br>$0.36 \pm 0.00$<br>$2.41 \pm 0.02$<br>$CIFAR-100$<br>2-GCE $%$<br>$0.05 \pm 0.00$<br>$0.55 \pm 0.00$<br>$8.31 \pm 0.21$<br>$29.79 + 0.03$<br>$10.27 + 0.00$<br>$38.05 + 0.49$<br>Acc $%$<br>$Non-ID$<br>$0.1$ -GCE %<br>$0.04 \pm 0.00$<br>$0.18 \pm 0.00$<br>$1.97 \pm 0.03$<br>2-GCE $%$<br>$0.07 + 0.00$<br>$0.22 \pm 0.00$<br>$4.90 \pm 0.14$ |  |  |  |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
|                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |

<sup>1</sup> Each client holds data with 50 classes  $(C_k = 50)$ .

Image /page/6/Figure/4 description: This figure contains two plots, one labeled "IID" and the other "Non-IID". Both plots show the relationship between "Img/Cls" on the x-axis and different metrics on the y-axis. The "IID" plot shows three lines: "Acc" (blue, with 'x' markers), "0.01-GCE" (orange, with 'o' markers), and "0.5-GCE" (green, with triangle markers). The "Acc" line increases from approximately 95% at 1 Img/Cls to over 99% at 5 Img/Cls. The "0.01-GCE" line decreases from approximately 6.5% at 1 Img/Cls to around 6.25% at 5 Img/Cls. The "0.5-GCE" line increases from approximately 93.5% at 1 Img/Cls to over 99% at 5 Img/Cls. The "Non-IID" plot also shows the same three lines. The "Acc" line increases from approximately 95% at 1 Img/Cls to over 99% at 5 Img/Cls. The "0.01-GCE" line decreases from approximately 7.1% at 1 Img/Cls to around 6.5% at 5 Img/Cls. The "0.5-GCE" line increases from approximately 40% at 1 Img/Cls to around 50% at 5 Img/Cls. The "IID" plot uses a left y-axis for "Acc %" ranging from 93 to 99, and a right y-axis for "0.01-GCE %" and "0.5-GCE %" ranging from 5.50 to 7.25 and 20 to 60 respectively. The "Non-IID" plot uses the same y-axes scales.

<span id="page-6-2"></span>Fig. 3. Scalable communication efficiency by adjusting Img/Cls in decentralized dataset distillation. We set two  $\gamma$  values to indicate the different importance of accuracy gain.

federated learning method by adding a spoon of distilled datasets from other clients via D2N (Device to Networks) or D2D (Device to Device) networks, before the first round in standard federated learning.

Network Assumption Compared to multi-shot schemes, a one-shot scheme is less affected by network heterogeneity. Despite, to evaluate the impact of network heterogeneity and address the network assumption in federated learning application scenarios, we consider the Quality of Service (QoS) of *at most once* for the one-shot scheme and compare the performance of *FedD3* and OSFL with different ratios of stragglers. As shown in Fig [5,](#page-7-5) FedD3 outperforms OSFL.

Computation Costs Most dataset distillation methods can

result in high computation loads. In our experiment setup, we take into account computation limitations in KIP and scale the number of distilled images on each client. In our *FedD3* setup, we use FC-4 kernel without pooling and convolutional kernels for dataset distillation. In each client, we only distill a small number of images, i.e. 1 Img/Cls. In Federated Learning, local training can result in a linear increase in computational cost with the number of epochs. We also disregard the computational cost caused by a large number of epochs and aim to achieve better results in the baselines for a fair comparison only for communication efficiency. Additionally, we measure the required time on a compact NVIDIA Quadro RTX 4000 GPU for training an AlexNet on CIFAR-10: On average *73.69s* are needed for *FedD3* with 800 distillation steps for 1 Img/Cls, and *89.96s* for one-shot FedAvg with 10 epochs.

Beyond Kernel: Individual DD-instances Because of stable performance, we mainly employ KIP methods as dataset distillation instances in *FedD3*. In fact, according to the actual local datasets, we can also consider individual instances to generate the synthetic dataset for uploading. Self-selecting instances in each client should be encouraged, which can be advantageous to the quality of distilled data, as the server and other clients should be not aware of the distribution of local data. Additionally, we believe the autonomy of local dataset distillation can enhance privacy.

Lessons Learned for Orchestration In *FedD3*, all local datasets should meet the requirement of the used dataset distillation method. Here we can think about two example scenarios: (*i*) If there is only one data point in one of the clients,  $\tilde{D}_k$  and  $D_k$  are the same at the first epoch in *ClientDatasetDistillation* in Algorithm [1;](#page-3-0) (*ii*) If there is only one data point with its label  $x_i$  for a specific class  $y_c$ , the distilled dataset  $\tilde{x}_i$  can be always the same as  $x_i$ , especially when only the loss is backpropagated on  $\ddot{X}$ . Both situations can break the privacy, as it will upload at least one raw data point to the server. To tackle this issue, we believe a good orchestration for dataset and client selection is recommended.

## VI. CONCLUSION

In this work, we introduce a novel federated learning framework, *FedD3*, which reduces the overall communication volume and with that opens up the concept of federated learning to more application scenarios in particular in network constrained environments. It achieves this by leveraging local dataset distillation instead of traditional learning approaches *(i)* to significantly reduce communication volumes and *(ii)* to limit transfers to one-shot communication, rather than iterative multi-way communication. Our conducted experimental evaluation shows that *FedD3* can well balance the trade-off between prediction accuracy and communication cost for federated learning. Compared to other federated learning, it provides a more robust training performance, especially on Non-IID data silos.

#### **REFERENCES**

<span id="page-6-0"></span>[1] B. McMahan, E. Moore, D. Ramage, S. Hampson, and B. A. y. Arcas, "Communication-efficient learning of deep networks from decentralized

Image /page/7/Figure/0 description: This figure contains four scatter plots, arranged in a 2x2 grid. The top row plots are for MNIST, and the bottom row plots are for CIFAR-10. The left column plots are for IID (C\_k=10), and the right column plots are for Non-IID (C\_k=2). All plots have the x-axis labeled "# of clients" and the y-axis labeled "# of distilled images per client". Each plot shows four lines, representing different numbers of images received by the server: 2000 (yellow), 1000 (teal), 500 (green), and 200 (blue). Each line has data points with specific accuracy values. For example, in the top-left plot (MNIST, IID), the yellow line (2000 images) starts at approximately 96.8% accuracy with 10 clients and decreases to about 95.9% with 100 clients. The bottom-left plot (CIFAR-10, IID) shows the teal line (1000 images) starting at approximately 50.4% accuracy with 10 clients and decreasing to about 40.0% with 100 clients. The top-right plot (MNIST, Non-IID) shows the yellow line starting at approximately 31.3% accuracy with 10 clients and decreasing to about 95.9% with 100 clients. The bottom-right plot (CIFAR-10, Non-IID) shows the teal line starting at approximately 22.6% accuracy with 10 clients and decreasing to about 43.5% with 100 clients. An arrow labeled "Increasing Acc" is present in each plot, indicating the general trend of accuracy improvement with more distilled images per client.

<span id="page-7-4"></span>Fig. 4. Number of clients effects the test accuracy (in %) of model. We consider test accuracy changing with the different number of clients. As it is naturally clear that the test accuracy of the final trained model can be improved by the increasing number of the final distilled images number at the server, through varying the number of the distilled images at each client with respect to the number of clients, we keep the size of the global distilled dataset always the same across the experiments, and hence eliminate the effects from it. Note that for Non-IID dataset, if the number of clients is less than 10 (as shown in the gray area), not all classes in the entire dataset can be covered, therefore, the test accuracy reduces. Apart from this impact, the results show that (*i*) the test accuracy increases with smaller client numbers; (*ii*) the test accuracy on Non-IID datasets and on IID datasets are similar, which indicates the *FedD3* is robust to data heterogeneity.

Image /page/7/Figure/2 description: This is a line graph showing the test accuracy of FedD3 and OSFL models with varying percentages of stragglers over communication rounds. The x-axis represents the communication round, ranging from 1 to 9. The y-axis represents the test accuracy, ranging from 0.20 to 0.40. There are three lines for FedD3: FedD3 70% Stragglers (yellow diamonds), FedD3 90% Stragglers (green squares), and FedD3 95% Stragglers (blue triangles). There are also three lines for OSFL: OSFL 70% Stragglers (yellow crosses), OSFL 90% Stragglers (green squares), and OSFL 95% Stragglers (blue stars). The FedD3 lines generally show higher accuracy than the OSFL lines. The FedD3 70% Stragglers line is the highest, starting around 0.38 and ending around 0.42. The OSFL 70% Stragglers line is the lowest, starting around 0.24 and fluctuating between 0.24 and 0.27.

<span id="page-7-5"></span>Fig. 5. Performance comparison of *FedD3* and OSFL for training a CNN model on distributed Non-IID CIFAR-100 in heterogeneous networks (with different ratios of stragglers).

data," in *International Conference on Artificial Intelligence and Statistics (AISTATS)*, vol. 54, 2017, pp. 1273–1282.

- <span id="page-7-0"></span>[2] R. Pathak and M. J. Wainwright, "Fedsplit: An algorithmic framework for fast federated optimization," *Advances in Neural Information Processing Systems*, vol. 33, pp. 7057–7066, 2020.
- [3] H. Yuan and T. Ma, "Federated accelerated stochastic gradient descent," in *Advances in Neural Information Processing Systems*,

H. Larochelle, M. Ranzato, R. Hadsell, M. Balcan, and H. Lin, Eds., vol. 33. Curran Associates, Inc., 2020, pp. 5332– 5344. [Online]. Available: [https://proceedings.neurips.cc/paper/2020/](https://proceedings.neurips.cc/paper/2020/file/39d0a8908fbe6c18039ea8227f827023-Paper.pdf) [file/39d0a8908fbe6c18039ea8227f827023-Paper.pdf](https://proceedings.neurips.cc/paper/2020/file/39d0a8908fbe6c18039ea8227f827023-Paper.pdf)

- [4] G. Malinovskiy, D. Kovalev, E. Gasanov, L. Condat, and P. Richtárik, "From local sgd to local fixed-point methods for federated learning," in *ICML*, 2020, pp. 6692–6701. [Online]. Available: [http://proceedings.](http://proceedings.mlr.press/v119/malinovskiy20a.html) [mlr.press/v119/malinovskiy20a.html](http://proceedings.mlr.press/v119/malinovskiy20a.html)
- [5] D. Rothchild, A. Panda, E. Ullah, N. Ivkin, I. Stoica, V. Braverman, J. Gonzalez, and R. Arora, "Fetchsgd: Communication-efficient federated learning with sketching," in *ICML*, 2020, pp. 8253–8265. [Online]. Available:<http://proceedings.mlr.press/v119/rothchild20a.html>
- [6] J. Hamer, M. Mohri, and A. T. Suresh, "FedBoost: A communicationefficient algorithm for federated learning," in *Proceedings of the 37th International Conference on Machine Learning*, ser. Proceedings of Machine Learning Research, H. D. III and A. Singh, Eds., vol. 119. PMLR, 13–18 Jul 2020, pp. 3973–3983. [Online]. Available: <https://proceedings.mlr.press/v119/hamer20a.html>
- [7] R. Song, L. Zhou, L. Lyu, A. Festag, and A. Knoll, "Resfed: Communication efficient federated learning by transmitting deep compressed residuals," *arXiv preprint arXiv:2212.05602*, 2022.
- <span id="page-7-1"></span>[8] H. Gao, A. Xu, and H. Huang, "On the convergence of communicationefficient local sgd for federated learning," in *Proceedings of the AAAI Conference on Artificial Intelligence*, 2021, pp. 7510–7518.
- <span id="page-7-2"></span>[9] Y. Lin, S. Han, H. Mao, Y. Wang, and W. J. Dally, "Deep gradient compression: Reducing the communication bandwidth for distributed training," *arXiv preprint arXiv:1712.01887*, 2017.
- <span id="page-7-3"></span>[10] A. Festag, "Standards for vehicular communication - from ieee 802.11p to 5g," *e & i Elektrotechnik und Informationstechnik, Springer Verlag*, vol. 132, no. 7, pp. 409–416, 2015. [Online]. Available: <https://link.springer.com/article/10.1007/s00502-015-0343-0>

- <span id="page-8-0"></span>[11] R. Karlstetter, A. Raoofy, M. Radev, C. Trinitis, J. Hermann, and M. Schulz, "Living on the edge: Efficient handling of large scale sensor data," in *2021 IEEE/ACM 21st International Symposium on Cluster, Cloud and Internet Computing (CCGrid)*, 2021, pp. 1–10.
- <span id="page-8-1"></span>[12] N. Guha, A. Talwalkar, and V. Smith, "One-shot federated learning," *arXiv preprint arXiv:1902.11175*, 2019.
- <span id="page-8-2"></span>[13] A. Kasturi, A. R. Ellore, and C. Hota, "Fusion learning: A one shot federated learning," in *International Conference on Computational Science*. Springer, 2020, pp. 424–436.
- <span id="page-8-3"></span>[14] Q. Li, B. He, and D. Song, "Practical one-shot federated learning for cross-silo setting," in *Proceedings of the Thirtieth International Joint Conference on Artificial Intelligence, IJCAI-21*, Z.-H. Zhou, Ed. International Joint Conferences on Artificial Intelligence Organization, 8 2021, pp. 1484–1490, main Track. [Online]. Available: [https:](https://doi.org/10.24963/ijcai.2021/205) [//doi.org/10.24963/ijcai.2021/205](https://doi.org/10.24963/ijcai.2021/205)
- <span id="page-8-4"></span>[15] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-8-16"></span>[16] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022.
- <span id="page-8-17"></span>[17] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," in *International Conference on Learning Representations*, 2021. [Online]. Available: [https://openreview.net/](https://openreview.net/forum?id=mSAKhLYLSsl) [forum?id=mSAKhLYLSsl](https://openreview.net/forum?id=mSAKhLYLSsl)
- <span id="page-8-5"></span>[18] T. Nguyen, R. Novak, L. Xiao, and J. Lee, "Dataset distillation with infinitely wide convolutional networks," *Advances in Neural Information Processing Systems*, vol. 34, 2021.
- <span id="page-8-6"></span>[19] Y. Huang, L. Chu, Z. Zhou, L. Wang, J. Liu, J. Pei, and Y. Zhang, "Personalized cross-silo federated learning on non-iid data." in *AAAI*, 2021, pp. 7865–7873.
- <span id="page-8-7"></span>[20] R. Song, R. Xu, A. Festag, J. Ma, and A. Knoll, "Fedbevt: Federated learning bird's eye view perception transformer in road traffic systems," *arXiv preprint arXiv:2304.01534*, 2023.
- <span id="page-8-8"></span>[21] H. Yu, Z. Liu, Y. Liu, T. Chen, M. Cong, X. Weng, D. Niyato, and Q. Yang, "A fairness-aware incentive scheme for federated learning," in *Proceedings of the AAAI/ACM Conference on AI, Ethics, and Society*, 2020, pp. 393–399.
- <span id="page-8-9"></span>[22] T. Dong, B. Zhao, and L. Lyu, "Privacy for free: How does dataset condensation help privacy?" in *Proceedings of the 39th International Conference on Machine Learning*, ser. Proceedings of Machine Learning Research, vol. 162. PMLR, 17–23 Jul 2022, pp. 5378–5396. [Online]. Available:<https://proceedings.mlr.press/v162/dong22c.html>
- [23] I. Sucholutsky and M. Schonlau, "Secdd: Efficient and secure method for remotely training neural networks (student abstract)," in *Proceedings of the AAAI Conference on Artificial Intelligence*, 2021, pp. 15 897–15 898.
- [24] G. Li, R. Togo, T. Ogawa, and M. Haseyama, "Soft-label anonymous gastric x-ray image distillation," in *2020 IEEE International Conference on Image Processing (ICIP)*. IEEE, 2020, pp. 305–309.
- [25] Y. Han and X. Zhang, "Robust federated learning via collaborative machine teaching," in *Proceedings of the AAAI Conference on Artificial Intelligence*, 2020, pp. 4075–4082.
- [26] Y. Wang, Y. Tong, and D. Shi, "Federated latent dirichlet allocation: A local differential privacy based framework," in *Proceedings of the AAAI Conference on Artificial Intelligence*, 2020, pp. 6283–6290.
- [27] Y. Zhou, X. Ma, D. Wu, and X. Li, "Communication-efficient and attack-resistant federated edge learning with dataset distillation," *IEEE Transactions on Cloud Computing*, 2022.
- [28] Y. Xiong, R. Wang, M. Cheng, F. Yu, and C.-J. Hsieh, "FedDM: Iterative distribution matching for communication-efficient federated learning," in *Workshop on Federated Learning: Recent Advances and New Challenges (in Conjunction with NeurIPS 2022)*, 2022. [Online]. Available:<https://openreview.net/forum?id=erV2t8ZLk2o>
- [29] G. Li, R. Togo, T. Ogawa, and M. Haseyama, "Dataset distillation for medical dataset sharing," in *Proceedings of the AAAI Conference on Artificial Intelligence (AAAI), Workshop*, 2023, pp. 1–6.
- <span id="page-8-10"></span>[30] ——, "Compressed gastric image generation based on soft-label dataset distillation for medical data sharing," *Computer Methods and Programs in Biomedicine*, vol. 227, p. 107189, 2022.
- <span id="page-8-11"></span>[31] S. P. Karimireddy, S. Kale, M. Mohri, S. Reddi, S. Stich, and A. T. Suresh, "SCAFFOLD: Stochastic controlled averaging for federated learning," in *Proceedings of the 37th International Conference on Machine Learning*, ser. Proceedings of Machine Learning Research, H. D. III and A. Singh, Eds., vol. 119. PMLR, 13–18 Jul 2020,

pp. 5132–5143. [Online]. Available: [https://proceedings.mlr.press/v119/](https://proceedings.mlr.press/v119/karimireddy20a.html) [karimireddy20a.html](https://proceedings.mlr.press/v119/karimireddy20a.html)

- <span id="page-8-12"></span>[32] A. Reisizadeh, F. Farnia, R. Pedarsani, and A. Jadbabaie, "Robust federated learning: The case of affine distribution shifts," *Advances in Neural Information Processing Systems*, vol. 33, pp. 21 554–21 565, 2020.
- <span id="page-8-13"></span>[33] K. Bonawitz, H. Eichner, W. Grieskamp, D. Huba, A. Ingerman, V. Ivanov, C. Kiddon, J. Konečný, S. Mazzocchi, B. McMahan et al., "Towards federated learning at scale: System design," *Proceedings of Machine Learning and Systems*, vol. 1, pp. 374–388, 2019.
- <span id="page-8-14"></span>[34] R. Xu, X. Xia, J. Li, H. Li, S. Zhang, Z. Tu, Z. Meng, H. Xiang, X. Dong, R. Song, H. Yu, B. Zhou, and J. Ma, "V2v4real: A real-world large-scale dataset for vehicle-to-vehicle cooperative perception," in *The IEEE/CVF Computer Vision and Pattern Recognition Conference (CVPR)*, 2023.
- [35] C. He, S. Li, J. So, X. Zeng, M. Zhang, H. Wang, X. Wang, P. Vepakomma, A. Singh, H. Qiu *et al.*, "Fedml: A research library and benchmark for federated machine learning," *arXiv preprint arXiv:2007.13518*, 2020.
- [36] L. U. Khan, W. Saad, Z. Han, E. Hossain, and C. S. Hong, "Federated learning for internet of things: Recent advances, taxonomy, and open challenges," *IEEE Communications Surveys & Tutorials*, 2021.
- [37] R. Song, L. Zhou, V. Lakshminarasimhan, A. Festag, and A. Knoll, "Federated learning framework coping with hierarchical heterogeneity in cooperative its," in *2022 IEEE 25th International Conference on Intelligent Transportation Systems (ITSC)*, 2022, pp. 3502–3508.
- [38] D. Z. Chen, A. X. Chang, and M. Nießner, "Scanrefer: 3d object localization in rgb-d scans using natural language," in *Computer Vision – ECCV 2020*, A. Vedaldi, H. Bischof, T. Brox, and J.-M. Frahm, Eds. Cham: Springer International Publishing, 2020, pp. 202–221.
- [39] Z. Chen, A. Gholami, M. Niessner, and A. X. Chang, "Scan2cap: Context-aware dense captioning in rgb-d scans," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, June 2021, pp. 3193–3203.
- [40] D. Z. Chen, Q. Wu, M. Nießner, and A. X. Chang, "D3net: A unified speaker-listener architecture for 3d dense captioning and visual grounding," in *Proceedings of European Conference on Computer Vision (ECCV)*, 2022.
- [41] R. Xu, H. Xiang, X. Xia, X. Han, J. Li, and J. Ma, "Opv2v: An open benchmark dataset and fusion pipeline for perception with vehicle-tovehicle communication," in *2022 International Conference on Robotics and Automation (ICRA)*. IEEE, 2022, pp. 2583–2589.
- [42] R. Xu, Y. Guo, X. Han, X. Xia, H. Xiang, and J. Ma, "Opencda: an open cooperative driving automation framework integrated with co-simulation," in *2021 IEEE International Intelligent Transportation Systems Conference (ITSC)*. IEEE, 2021, pp. 1155–1162.
- <span id="page-8-15"></span>[43] S. Banik, A. M. García, and A. Knoll, "3d human pose regression using graph convolutional network," in *2021 IEEE International Conference on Image Processing (ICIP)*. IEEE, 2021, pp. 924–928.
- <span id="page-8-18"></span>[44] B. Zhao and H. Bilen, "Dataset condensation with differentiable siamese augmentation," in *Proceedings of the 38th International Conference on Machine Learning*, ser. Proceedings of Machine Learning Research, M. Meila and T. Zhang, Eds., vol. 139. PMLR, 18–24 Jul 2021, pp. 12 674–12 685. [Online]. Available: [https://proceedings.mlr.press/v139/](https://proceedings.mlr.press/v139/zhao21a.html) [zhao21a.html](https://proceedings.mlr.press/v139/zhao21a.html)
- <span id="page-8-19"></span>[45] K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You, "Cafe: Learning to condense dataset by aligning features," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, June 2022, pp. 12 196–12 205.
- <span id="page-8-20"></span>[46] Y. Zhou, G. Pu, X. Ma, X. Li, and D. Wu, "Distilled one-shot federated learning," *arXiv preprint arXiv:2009.07999*, 2020.
- <span id="page-8-21"></span>[47] J. Goetz and A. Tewari, "Federated learning via synthetic data," *arXiv preprint arXiv:2008.04489*, 2020.
- <span id="page-8-22"></span>[48] I. Sucholutsky and M. Schonlau, "Soft-label dataset distillation and text dataset distillation," in *2021 International Joint Conference on Neural Networks (IJCNN)*, 2021, pp. 1–8.
- <span id="page-8-23"></span>[49] T. C. Nguyen, Z. Chen, and J. Lee, "Dataset meta-learning from kernel ridge-regression," in *ICLR 2021*, 2021. [Online]. Available: <https://openreview.net/forum?id=l-PrrQrK0QR>
- <span id="page-8-24"></span>[50] Y. Chen, M. Welling, and A. Smola, "Super-samples from kernel herding," in *Proceedings of the Twenty-Sixth Conference on Uncertainty in Artificial Intelligence*, ser. UAI'10. Arlington, Virginia, USA: AUAI Press, 2010, p. 109–116.

- <span id="page-9-0"></span>[51] J.-P. Baudry, A. E. Raftery, G. Celeux, K. Lo, and R. Gottardo, "Combining mixture components for clustering," *Journal of computational and graphical statistics*, vol. 19, no. 2, pp. 332–353, 2010.
- <span id="page-9-1"></span>[52] Y. LeCun, C. Cortes, and C. Burges, "Mnist handwritten digit database," *ATT Labs [Online]. Available: http://yann.lecun.com/exdb/mnist*, vol. 2, 2010.
- <span id="page-9-2"></span>[53] A. Krizhevsky, "Learning multiple layers of features from tiny images," *Technical report*, 2009.
- <span id="page-9-3"></span>[54] T. Li, A. K. Sahu, M. Zaheer, M. Sanjabi, A. Talwalkar, and V. Smith, "Federated optimization in heterogeneous networks," *Proceedings of Machine Learning and Systems*, vol. 2, pp. 429–450, 2020.
- <span id="page-9-4"></span>[55] J. Wang, Q. Liu, H. Liang, G. Joshi, and H. V. Poor, "Tackling the objective inconsistency problem in heterogeneous federated optimization," *Advances in neural information processing systems*, vol. 33, pp. 7611–7623, 2020.
- <span id="page-9-5"></span>[56] Y. Lecun, L. Bottou, Y. Bengio, and P. Haffner, "Gradient-based learning applied to document recognition," *Proceedings of the IEEE*, vol. 86, no. 11, pp. 2278–2324, 1998, doi:10.1109/5.726791.
- <span id="page-9-6"></span>[57] A. Krizhevsky, I. Sutskever, and G. E. Hinton, "Imagenet classification with deep convolutional neural networks," in *Advances in Neural Information Processing Systems*, F. Pereira, C. Burges, L. Bottou, and K. Weinberger, Eds., vol. 25. Curran Associates, Inc., 2012. [Online]. Available: [https://proceedings.neurips.cc/paper/2012/](https://proceedings.neurips.cc/paper/2012/file/c399862d3b9d6b76c8436e924a68c45b-Paper.pdf) [file/c399862d3b9d6b76c8436e924a68c45b-Paper.pdf](https://proceedings.neurips.cc/paper/2012/file/c399862d3b9d6b76c8436e924a68c45b-Paper.pdf)
- <span id="page-9-7"></span>[58] Q. Li, Y. Diao, Q. Chen, and B. He, "Federated learning on non-iid data silos: An experimental study," in *IEEE International Conference on Data Engineering*, 2022.
- <span id="page-9-8"></span>[59] S. Caldas, S. M. K. Duddu, P. Wu, T. Li, J. Konečnỳ, H. B. McMahan, V. Smith, and A. Talwalkar, "Leaf: A benchmark for federated settings," *arXiv preprint arXiv:1812.01097*, 2018.
- <span id="page-9-9"></span>[60] K. He, X. Zhang, S. Ren, and J. Sun, "Deep residual learning for image recognition," in *Proceedings of the IEEE conference on computer vision and pattern recognition*, 2016, pp. 770–778.
- <span id="page-9-10"></span>[61] H. Xiao, K. Rasul, and R. Vollgraf, "Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms," *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-9-11"></span>[62] Y. Netzer, T. Wang, A. Coates, A. Bissacco, B. Wu, and A. Y. Ng, "Reading digits in natural images with unsupervised feature learning," 2011.
# BREAKING CLASS BARRIERS: EFFICIENT DATASET DISTILLATION VIA INTER-CLASS FEATURE COMPENSATOR

<PERSON><PERSON><sup>1,2</sup> <PERSON><PERSON><PERSON><sup>1,2</sup> <PERSON><sup>3</sup> <PERSON><sup>1,2</sup>

<sup>1</sup> Centre for Frontier AI Research, Agency for Science, Technology and Research, Singapore <sup>2</sup>Institute of High Performance Computing, Agency for Science, Technology and Research, Singapore <sup>3</sup>University of Nevada, Reno

{zhangx7, dujw, <PERSON>}@cfar.astar.edu.sg <EMAIL>

## ABSTRACT

Dataset distillation has emerged as a technique aiming to condense informative features from large, natural datasets into a compact and synthetic form. While recent advancements have refined this technique, its performance is bottlenecked by the prevailing class-specific synthesis paradigm. Under this paradigm, synthetic data is optimized exclusively for a pre-assigned one-hot label, creating an implicit class barrier in feature condensation. This leads to inefficient utilization of the distillation budget and oversight of inter-class feature distributions, which ultimately limits the effectiveness and efficiency, as demonstrated in our analysis. To overcome these constraints, this paper presents the Inter-class Feature Compensator (INFER), an innovative distillation approach that transcends the classspecific data-label framework widely utilized in current dataset distillation methods. Specifically, INFER leverages a Universal Feature Compensator (UFC) to enhance feature integration across classes, enabling the generation of multiple additional synthetic instances from a single UFC input. This significantly improves the efficiency of the distillation budget. Moreover, INFER enriches inter-class interactions during the distillation, thereby enhancing the effectiveness and generalizability of the distilled data. By allowing for the linear interpolation of labels similar to those in the original dataset, INFER meticulously optimizes the synthetic data and dramatically reduces the size of soft labels in the synthetic dataset to almost zero, establishing a new benchmark for efficiency and effectiveness in dataset distillation. In practice, INFER demonstrates state-of-the-art performance across benchmark datasets. For instance, in the  $\rm ipc = 50$  setting on ImageNet-1k with the same compression level, it outperforms SRe2L by 34.5% using ResNet18. Codes are available at <https://github.com/zhangxin-xd/UFC>.

## 1 INTRODUCTION

The remarkable success of Deep Neural Networks (DNNs) [\(Yang et al.,](#page-12-0) [2024;](#page-12-0) [Cai et al.,](#page-10-0) [2024;](#page-10-0) [Zheng et al.,](#page-12-1) [2024;](#page-12-1) [Dosovitskiy et al.,](#page-10-1) [2021\)](#page-10-1) in recent years can largely be attributed to their ability to extract complex and representative features from vast real-world data [\(Gao et al.,](#page-10-2) [2020;](#page-10-2) [Benenson](#page-10-3) [et al.,](#page-10-3) [2019\)](#page-10-3). However, the extensive data requirements for training DNNs pose significant challenges. These challenges include not only the time-consuming training process [\(Liu et al.,](#page-11-0) [2021;](#page-11-0) [Touvron et al.,](#page-12-2) [2021;](#page-12-2) [Tolstikhin et al.,](#page-12-3) [2021\)](#page-12-3), but also the substantial costs associated with data storage and computational resources [\(Kaplan et al.,](#page-11-1) [2020;](#page-11-1) [Hoffmann et al.,](#page-10-4) [2022\)](#page-10-4).

In response to the rapid growth of computational and storage demands in training DNNs, dataset distillation (DD) [\(Sachdeva & McAuley,](#page-11-2) [2023;](#page-11-2) [Lei & Tao,](#page-11-3) [2023;](#page-11-3) [Yu et al.,](#page-12-4) [2023;](#page-12-4) [Liu & Du,](#page-11-4) [2025\)](#page-11-4) has emerged as an effective solution. Dataset distillation condenses essential features from extensive datasets into a compact, synthetic form, allowing models to maintain comparable performance levels with fewer resources [\(Wang et al.,](#page-12-5) [2018\)](#page-12-5). Recent advancements in dataset

 $\mathbb{B}$  represents the corresponding author.

distillation encompass techniques such as gradient matching [\(Zhao et al.,](#page-12-7) [2021;](#page-12-7) [Zhao & Bilen,](#page-12-8) [2021;](#page-12-8) [Lee et al.,](#page-11-6) [2022b;](#page-11-6) [Shin et al.,](#page-12-9) [2023\)](#page-12-9), trajectory matching [\(Cazenavette et al.,](#page-10-5) [2022;](#page-10-5) [Cui](#page-10-6) [et al.,](#page-10-6) [2023;](#page-10-6) [Du et al.,](#page-10-7) [2023a](#page-10-7)[;b\)](#page-10-8), data factorization [\(Liu et al.,](#page-11-7) [2022;](#page-11-7) [Kim et al.,](#page-11-8) [2022;](#page-11-8) [Wei et al.,](#page-12-10) [2023;](#page-12-10) [Shin et al.,](#page-11-9) [2024\)](#page-11-9), and kernel ridge regression [\(Nguyen et al.,](#page-11-10) [2020;](#page-11-10) [2021;](#page-11-11) [Loo et al.,](#page-11-12) [2022\)](#page-11-12). These approaches have significantly enhanced dataset distillation by compressing dense knowledge into single data instances. Despite the diversity of these methods, most of them adhere to a uniform paradigm: each synthetic data instance is class-specific and optimized exclusively for a pre-assigned one-hot label.

While this "one label per instance" paradigm aligns with the traditional data-label pair structure of original datasets, it presupposes that the most effective encapsulation of a dataset's knowledge can be achieved through individual

Image /page/1/Figure/3 description: This is a scatter plot showing the relationship between compression ratio and top-1 accuracy for different datasets and methods. The x-axis represents the compression ratio in percentage, ranging from 1 to 220. The y-axis represents the top-1 accuracy in percentage, ranging from 10 to 70. The plot displays four datasets: CIFAR-100 (represented by gray circles), Tiny-ImageNet (represented by gray triangles), and ImageNet-1K (represented by gray diamonds). There are three methods plotted: G-VBSM (dashed blue line connecting blue circles), SRe2L (dashed light blue line connecting light blue triangles), INFER+Dyn (ours) (solid purple line connecting purple triangles), and INFER (ours) (solid dark purple line connecting dark purple circles). Additionally, there are points labeled 'MTT\*' and 'Random' with dashed lines indicating a trend. The plot also includes a red star in the top right corner, possibly indicating a reference point or best performance.

Figure 1: Performance vs. compression ratio of SOTA dataset distillation methods (G-VBSM [\(Shao et al.,](#page-11-5) [2024\)](#page-11-5), SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6), MTT [\(Cazenavette et al.,](#page-10-5) [2022\)](#page-10-5)) on three benchmarks. Performance is measured as the Top-1 accuracy of ResNet-18 (ConvNet128 for MTT) on the respective validation sets, trained from scratch using synthetic datasets. The compression ratio, including the additional soft labels, is the proportion of the distilled dataset size to the original dataset size. The star indicates optimal performance.

instances representing discrete class identities. When the amount of synthetic data is limited, this class-specific paradigm benefits distillation by encouraging synthetic data to condense the most distinctive features of each class. However, as more synthetic instances are assigned to the same class, they tend to capture significant but similar features rather than diversifying to include unique, rarer features. This phenomenon, known as "feature duplication" [\(Jiang et al.,](#page-10-9) [2022;](#page-10-9) [Kim et al.,](#page-11-8) [2022;](#page-11-8) [Cazenavette et al.,](#page-10-5) [2022\)](#page-10-5), leads to *Inefficient Utilization of the Distillation Budget*, thereby limiting the creation of a more diverse and comprehensive synthetic representation.

Another critical downside of the class-specific synthesis paradigm is the *"Oversight of Inter-Class Features"*. By focusing on distinctive class-specific characteristics under the "one label per instance" approach, implicit "Class Barriers" are created between classes. These class barriers prevent the synthetic data instances from capturing inter-class features that bridge different classes in the original dataset. This oversight, inhibits the formation of thin and clear decision boundaries among classes, which are essential for models to generalize well across complex scenarios. We demonstrate the visualization of frormed decision boundaries in [Figure 2.](#page-2-0)

Recognizing the aforementioned limitations, we introduce a novel paradigm for dataset distillation, termed the Inter-class Feature compEnsatoR (INFER). Unlike traditional methods that follow the "one instance for one class" paradigm and generate separate synthetic instances for each class, INFER pioneers a "one instance for ALL classes" paradigm by introducing a Universal Feature Compensator (UFC). The UFC, designed to reflect the general representativeness across all classes of the original dataset, depreciates the importance of pre-assigned labels. This feature enables UFC to compensate for inter-class features while INFER randomly incorporates a few natural data instances to enhance intra-class features. Notably, INFER integrates one UFC with multiple natural data instances from different classes through a simple additive process without auxiliary generator networks [\(Liu et al.,](#page-11-7) [2022\)](#page-11-7), allowing the generation of multiple synthetic instances from a single input. This "one instance for ALL classes" paradigm significantly enhances the efficiency of the distillation budget.

Furthermore, we have meticulously designed the optimization of UFCs to encompass inter-class features. This optimization makes synthetic instances generated from UFCs compatible with MixUp data augmentation, which promotes inter-class interactions and aids in forming thin, clear decision boundaries among classes. Prior works applying MixUp to synthetic data [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6) involve dynamic generating and storing an extensive amount of soft labels, which can increase storage requirements up to 30-fold. INFER, however, dramatically eliminates the need for such extensive soft label storage by elegantly adopting the linear interpolation of labels used with natural datasets, decreasing the storage requirement by 99.3%. This enhancement not only preserves the distillation budget but also streamlines the entire training process, underscoring INFER's efficiency and effectiveness. Notably, INFER achieves 53.3% accuracy on ImageNet-1k with a ResNet-50 model, training solely on the synthetic dataset, which is only 4.04% the size of the original ImageNet-1k.

Image /page/2/Figure/1 description: The image displays a comparative analysis of two approaches for distilling knowledge from a teacher model to a student model, focusing on class separation. The left side illustrates the 'Existing Approach: One instance for one class,' showing a teacher model with multiple classes (C1, C2, C...) generating refined synthetic data (S1, S2, S...) for each class separately. The right side depicts 'Our INFER: One instance for All classes,' which uses a Universal Feature Compensator (UFC) to process initial synthetic data (u1, u2) and break class barriers, resulting in refined synthetic data (S1~S2, S2~S2, S~S2) that are shared across classes. The rightmost part of the image presents t-SNE visualizations of the decision boundaries of CIFAR-100 (7 classes) for both approaches. The 'Existing Approach' visualization shows more overlapping clusters, while 'Ours' visualization exhibits better separation between the clusters, indicating improved class discrimination.

<span id="page-2-0"></span>Figure 2: Left: Overview of dataset distillation paradigms. The first illustrates the traditional "one instance for one class" approach, where each instance is optimized exclusively for its pre-assigned label, creating implicit class barriers. The second illustrates our INFER method, designed for "one instance for ALL classes" distillation. **Right:** t-SNE visualization of the decision boundaries between the traditional approaches (*i.e.*, SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6)) and our INFER approach. We randomly select seven classes from CIFAR-100 dataset for the visualization. INFER forms thin and clear decision boundaries among classes, in contrast to the chaotic decision boundaries of the traditional approach.

This performance, which does not require dynamically generated soft labels, outperforms existing approaches in both accuracy and compression ratio.

Our contribution can be summarized as follows:

- We rethink the prevailing "one label per instance" paradigm that exclusively optimizes each synthetic data instance for a specific class. Through empirically analysis, we identify and address its two main limitations: inefficient utilization of the distillation budget and oversight of inter-class features.
- To overcome these issues, we introduce a new paradigm INFER, for "one instance for all classes" dataset distillation. Our INFER incorporates a novel Universal Feature Compensator (UFC) to efficiently condense and integrate features across multiple classes. Extensive experiments across CIFAR, tiny-ImageNet and ImageNet-1k datasets demonstrate the state-of-the-art performance of INFER.

<span id="page-2-3"></span>

## 2 PRELIMINARIES AND RELATED WORKS

The precursor to dataset distillation in condensing of datasets is coreset selection [\(Bachem et al.,](#page-10-10) [2017;](#page-10-10) [Chen et al.,](#page-10-11) [2010;](#page-10-11) [Har-Peled & Kushal,](#page-10-12) [2005;](#page-10-12) [Sener & Savarese,](#page-11-13) [2018;](#page-11-13) [Xin et al.,](#page-12-11) [2024\)](#page-12-11). This method involves selecting a coreset of the original dataset that ideally contains the entire representativeness of the population. However, this approach encounters a significant performance drop when compression rati[o](#page-2-1) is small. A plausible explanation for this could be the low density of representativeness within natural data instances. Therefore, dataset distillation seeks to synthesize data instances with densely packed features. We begin with a brief formulation of dataset distillation.

**Problem Formulation.** Assume we are given a natural and large dataset  $\mathcal{T} = \{(\bm{x}_i, \bm{y}_i)\}_{i=1}^{|\mathcal{T}|}$ , where each element  $x_i \in \mathbb{R}^d$  is drawn i.i.d. from a natural distribution D, and the class label  $y_i \in \mathcal{Y}$  $\{0, 1, \ldots, C-1\}$  with C representing the number of classes. Dataset distillation aims to synthesize a small dataset  $\mathcal{S} = \{(\mathbf{s}_i, \mathbf{y}_i)\}_{i=1}^{|\mathcal{S}|}$ , where  $\mathbf{s}_i \in \mathbb{R}^d$  and  $\mathbf{y}_i \in \mathcal{Y}$ , to serve as an approximate solution to the following optimization problem:

<span id="page-2-2"></span>
$$
S = \underset{S \subset \mathbb{R}^d \times \mathcal{Y}}{\arg \min} \mathbb{E} \left[ \ell \left( f_{\theta_S}, x, y \right) \right], \tag{1}
$$

where  $\theta_S$  represents the converged weights trained with S, and  $\ell$  is the loss function. The class label  $y_i$  is typically a pre-assigned one-hot label [\(Zhao & Bilen,](#page-12-12) [2023;](#page-12-12) [Zhao et al.,](#page-12-7) [2021;](#page-12-7) [Zhao & Bilen,](#page-12-8) [2021;](#page-12-8) [Jiang et al.,](#page-10-9) [2022;](#page-11-8) [Kim et al.,](#page-11-8) 2022; [Cazenavette et al.,](#page-10-5) [2022\)](#page-10-5) to encourage  $s_i$  to exhibit more distinct, class-specific features.

<span id="page-2-1"></span>Compression Ratio (CR) = synthetic dataset size / original dataset size [\(Cui et al.,](#page-10-13) [2022\)](#page-10-13). A lower ratio indicates a more condensed dataset.

Pioneering our approach, Wang et al. [\(Wang et al.,](#page-12-5) [2018\)](#page-12-5) is the first to propose DD, a method that optimizes S directly after substituting D with  $\mathcal T$  in [Equation 1.](#page-2-2) Due to the limited guidance, this approach often leads to suboptimal performance, prompting the development of gradient-matching methods [\(Zhao et al.,](#page-12-7) [2021;](#page-12-7) [Zhao & Bilen,](#page-12-8) [2021;](#page-12-8) [Lee et al.,](#page-11-6) [2022b;](#page-11-6) [Shin et al.,](#page-12-9) [2023\)](#page-12-9). These methods improve supervision by aligning the models' gradients. The success of gradient-matching has inspired further research into matching the trajectory of gradients [\(Cazenavette et al.,](#page-10-5) [2022;](#page-10-5) [Cui et al.,](#page-10-6) [2023;](#page-10-6) [Du et al.,](#page-10-7) [2023a](#page-10-7)[;b\)](#page-10-8), yielding even better performance. Despite these advancements, most of current dataset distillation methods still primarily focus on generating class-specific synthetic instances. This ongoing adherence to the class-specific paradigm not only constrains the efficiency of the distillation budget but also results in the neglect of critical inter-class features. These limitations drive our investigation into a new paradigm, aimed at developing a more efficient and effective dataset distillation solution.

Distillation Budget Consistency. As our INFER depreciates the class-specific paradigm, it becomes necessary to establish clear criteria for maintaining consistency in the distillation budget. Traditional methods adopt Images Per Class (IPC) as described by [\(Wang et al.,](#page-12-5) [2018;](#page-12-5) [Zhao et al.,](#page-12-7) [2021;](#page-12-7) [Cazenavette et al.,](#page-10-5) [2022\)](#page-10-5), such that  $|\mathcal{S}| = \text{ipc} \times C$ . This approach provides a uniform criterion across various datasets. Therefore, we continue to employ IPC as the criterion to measure the distillation budget.

However, IPC does not account for the auxiliary generator [\(Liu et al.,](#page-11-7) [2022;](#page-11-7) [Lee et al.,](#page-11-14) [2022a\)](#page-11-14) or additional soft labels [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6) that are used to enhance the performance of a synthetic dataset, resulting in an asymmetric advantage compared to methods that solely utilize the data-label pair. Therefore, we also employ the compression ratio, as described by [\(Cui et al.,](#page-10-13) [2022\)](#page-10-13), to measure the distillation budget. The total bit count of any auxiliary modules and soft labels will be considered part of the synthetic dataset. To compute the compression ratio, we divide the total bit count of the synthetic dataset, by that of the original dataset, *i.e.*,  $CR =$  synthetic dataset size/original dataset size.

## 3 METHODOLOGY

In this section, we introduce our novel distillation paradigm, INFER. We begin by detailing the limitations associated with the class-specific distillation paradigm, highlighting inefficiencies and oversight of inter-class feature distributions. Following this, we describe the Universal Feature Compensator (UFC), the cornerstone of our methodology, designed to integrate inter-class features. Lastly, we discuss our approach to augmenting synthetic datasets, which aims to facilitate the formation of thin and clear decision boundaries among classes.

#### 3.1 LIMITATIONS IN CLASS-SPECIFIC DISTILLATION

Wang et al. [\(Wang et al.,](#page-12-5) [2018\)](#page-12-5) established the general approach to solve S as outlined in [Equation 1:](#page-2-2) Each synthetic data instance  $s_i$  is assigned a one-hot label  $y_i$  and optimized to capture intra-class features by minimizing  $\sum_{(\bm x,\bm y)\in\mathcal{T}}\ell$   $(f_{\theta_{\mathcal{S}}},\bm x,\bm y)$ . Unlike [Equation 1,](#page-2-2)  $\bm{\mathcal{D}}$  is replaced by  $\bm{\mathcal{T}},$  given that  $\bm{\mathcal{D}}$ is inaccessible and  $\mathcal{T} \sim \mathcal{D}$ . Initially, this class-specific design achieved progress in the early stages. However, as dataset distillation research has evolved, two major limitations become prominent, compelling a rethinking of this design.

Inefficient Utilization of Distillation Budget. Recent advancements in dataset distillation [\(Yin](#page-12-6) [et al.,](#page-12-6) [2024;](#page-12-6) [Loo et al.,](#page-11-12) [2022;](#page-11-12) [Cui et al.,](#page-10-6) [2023\)](#page-10-6) have enabled individual synthetic data instances to capture more features specific to a class, particularly notable in highly compressed scenarios where  $ipc = 1$  [\(Cui et al.,](#page-10-6) [2023;](#page-10-6) [Loo et al.,](#page-11-12) [2022\)](#page-11-12). However, as  $ipc$  increases, additional synthetic data instances tend to capture distinctive yet duplicated intra-class features, leading to redundancy within the synthetic dataset. This redundancy explains the marginal performance gains observed in less compressed scenarios (ipc = 50). SeqMatch [\(Du et al.,](#page-10-8) [2023b\)](#page-10-8) and DATM [\(Guo et al.,](#page-10-14) [2023\)](#page-10-14) addressed this redundancy by dividing the synthetic data into several subsets optimized diversely. We validate this hypothesis through experiments shown in [Figure 4](#page-8-0) (a). Ideally, newly optimized synthetic data instances should capture rare and diversifying features that complement the distinctive class-specific features.

Image /page/4/Figure/1 description: The image illustrates a process involving data storage and integration during training. It shows a set S^k, defined as (P^k, U^k), which is to be stored. Below this, a plus sign indicates an addition operation with a set of elements labeled x1 through xC. An arrow curves from the plus sign to these elements. A dashed box encloses a grid of elements labeled s̃11, s̃1M, s̃C1, and s̃CM, with ellipses indicating intermediate values. Below this grid, the set S̃^k is defined as {(s̃i, ŷi) | s̃i = xi + uj}, and it is noted that this is integrated while training.

Figure 3: Illustration of the integration process between Universal Feature Compensators (UFCs) and natural data instances as described in [Equation 2.](#page-4-0) The integration is performed through a simple addition process. Consequently, only the sets  $\mathcal{S} = (\mathcal{P}^k, \mathcal{U}^k)$  need to be stored as the synthetic dataset. The synthetic dataset  $\tilde{\mathcal{S}}^k$  is generated on-the-fly during training.

Oversight of Inter-class Features. The prevalent focus on optimizing synthetic instances for the most distinctive intra-class features often leads to the oversight of inter-class features. This oversight significantly limits the synthetic data's ability to represent the feature distributions that span across different classes, which is crucial for complex classification tasks. Consequently, the potential for these synthetic datasets to support the training of models that can generalize across varied scenarios may be significantly hampered. As depicted in [Figure 2,](#page-2-0) this issue is evidenced by the chaotic decision boundaries formed by the "one label per instance" synthetic dataset, which neglects the inter-class features necessary for forming thin and clear decision boundaries.

<span id="page-4-1"></span>These limitations drive our investigation into a new paradigm, aimed at developing a more efficient and effective dataset distillation solution.

#### 3.2 UNIVERSAL FEATURE COMPENSATOR: BREAKING CLASS BARRIERS

The Universal Feature Compensators (UFCs), denoted as  $\mathcal{U} = \{\bm{u}_i\}_{i=1}^{|\mathcal{U}|}$ , forms the core of our novel INFER paradigm, designed specifically to address the inefficiencies and oversight inherent in the class-specific distillation approach.

Design and Functionality. The primary objective of designing the UFC is to enable the generation of multiple synthetic instances from a single compensator. To achieve this, our INFER divides the base synthetic dataset S into K subsets, such that  $S = S^1 \cup S^2 \cup \cdots \cup S^K$ . Each base subset  $S^k$ consists of a pair  $(\mathcal{P}^k, \mathcal{U}^k)$ , where  $\mathcal{U}^k$  represents the set of UFCs, and  $\mathcal{P}^k \subset \mathcal{T}$  contains natural instances to be integrated with UFCs and  $|\mathcal{P}^k| = C$ . For actual model training on the synthetic dataset, we first integrates UFCs with natural data instances as follows:

<span id="page-4-0"></span>
$$
\tilde{\mathcal{S}}^k = \{ (\tilde{s}_i, \tilde{y}_i) \mid \tilde{s}_i = x_i + u_j, \n\text{for each } x_i \in \mathcal{P}^k \text{ and each } u_j \in \mathcal{U}^k \},
$$
\n(2)

where  $\tilde{S}^k$  represents the UFCs integrated synthetic dataset. This process is illustrated in [Figure 3.](#page-4-1) In practice, multiple architectures participate in UFCs' generation. Assuming the number of architectures is M, *i.e.*,  $|\mathcal{U}^k| = M$ , each  $\mathcal{S}^k$  can be multiplied approximately M times by INFER. We repeat the intergration process described above  $K$  times, once for each subset of  $S$ . This structured approach allows INFER to utilize the distillation budget approximately  $M$  times more efficiently compared to the class-specific paradigm.

**Optimization.** Under the INFER paradigm, each  $\mathcal{P}^k$  contains exactly one instance for each class, randomly selected from the original dataset T. Consequently,  $\mathcal{P}^k$  effectively captures the intra-class features with minimal duplication. In contrast, the UFCs are designed to optimize the capture of inter-class features. Therefore, the elements in  $\mathcal{P}^k$  uniformly cover each class, allowing the element  $\mathcal{U}^k$  to focus on capturing inter-class features by solving the follow minimization problem:

$$
\underset{\boldsymbol{u}_{j}\in\mathbb{R}^{d}}{\arg\min} \sum_{(\boldsymbol{x}_{i},\boldsymbol{y}_{i})\in\mathcal{P}^{k}}\left[\ell\left(f_{\theta_{\mathcal{T}}},\boldsymbol{x}_{i}+\boldsymbol{u}_{j},\boldsymbol{y}_{i}\right)+\alpha\mathcal{L}_{\mathrm{BN}}\left(f_{\theta_{\mathcal{T}}},\boldsymbol{x}_{i}+\boldsymbol{u}_{j}\right)\right],\tag{3}
$$

<span id="page-4-2"></span>where 
$$
\mathcal{L}_{\text{BN}}(f_{\theta_{\mathcal{T}}}, \boldsymbol{x}_i + \boldsymbol{u}_j) = \sum_l ||\mu_l(\tilde{S}_j^k) - \mu_l(\mathcal{T})||_2
$$
  
 
$$
+ \sum_l ||\sigma_l^2(\tilde{S}_j^k) - \sigma_l^2(\mathcal{T})||_2.
$$
 (4)

we define  $\tilde{S}_j^k = \{(\tilde{s}_i, \tilde{y}_i) \mid \tilde{s}_i = x_i + u_j, \text{ for each } x_i \in \mathcal{P}^k\}$  which is the generated synthetic dataset by integrating  $u_j$  with the corresponding  $\mathcal{P}^k$ .  $\mathcal{L}_{\rm BN}$  is the BN loss inspired by SRe2L [\(Yin](#page-12-6) [et al.,](#page-12-6) [2024\)](#page-12-6) to regularize the values of generated  $\tilde{s}_i$  to fall within the same normalization distribution as those in T. Intuitively,  $u_i$  serves as the feature compensator for the natural instances in S, carrying universal inter-class features that are beneficial for classification.

<span id="page-5-0"></span>Algorithm 1 Distillation on synthetic dataset via Inter-class Feature Compensator (INFER) **Require:** Target dataset  $\mathcal{T}$ : Number of subsets  $K$ : Number of classes  $C$ : M networks with different architectures:  $\{f^1, f^2, \cdots, f^M\}.$ 1: Initialize  $S = \{ \}$ 2: for  $k = 1$  to  $K$  do 3: Initialize subset  $\mathcal{P}^k = \{\}$ , the UFCs set  $\mathcal{U}^k = \{\}$ , and the static labels set  $\mathcal{Y}^k = \{\}$ 4: Randomly select C instances, one for each class, to form  $\mathcal{P}^k$ , such that: 5:  $\mathcal{P}^k = \{(\boldsymbol{x}_i, \boldsymbol{y}_i) \mid (\boldsymbol{x}_i, \boldsymbol{y}_i) \in \mathcal{T} \text{ and each } \boldsymbol{y}_i \text{ is unique in } \mathcal{P}^k\}$ 6: Initialize  $\mathcal{U}^k$  with zeros, where each  $u_i$  has the same dimensions as x: 7:  $\mathcal{U}^k = {\mathbf{u}_j \mid \mathbf{u}_j = \mathbf{0}_{\dim(\bm{x})}, \text{ for } j = 1, \dots, M}$ 8: for each  $u_j$  in  $\mathcal{U}^k$  do 9:  $\triangleright$  Construct integrated synthetic instance  $\tilde{s}_i$ 10: Let  $\tilde{S}_j^k = \{(\tilde{s}_i, \tilde{y}_i) \mid \tilde{s}_i = x_i + u_j, \text{ for each } x_i \in \mathcal{P}^k\}$ 11: repeat 12: Optimize  $u_i$  to minimize loss defined in [Equation 3](#page-4-2) 13: until Converge 14: Generate static soft labels  $\tilde{y}_i$  by [Equation 6,](#page-6-0)  $\mathcal{Y}^k = \mathcal{Y}^k \cup {\tilde{y}_i}$ 15: end for  $16:$  $k^k = \{\mathcal{P}^k, \mathcal{U}^k, \mathcal{Y}^k\}$ 17:  $S = S \cup \{S^k\}$ 18: end for **Ensure:** Synthetic dataset  $S$ 

#### 3.3 ENHANCING SYNTHETIC DATA WITH INTER-CLASS AUGMENTATION

Although UFCs are encouraged to encapsulate more inter-class features, their limited size, especially compared to the size of the original dataset  $\mathcal T$ , restricts the breadth of features crucial for forming the decision boundaries of neural networks. Therefore, we also leverage MixUP [\(Zhang et al.,](#page-12-13) [2018\)](#page-12-13) as a technique to enhance inter-class augmentation.

Data augmentation [\(Shorten & Khoshgoftaar,](#page-12-14) [2019\)](#page-12-14) has been well-developed and proven effective in training neural networks with natural datasets. However, the advancements in data augmentation do not generalize well to synthetic datasets generated through dataset distillation. DSA [\(Zhao & Bilen,](#page-12-8) [2021\)](#page-12-8) designed an adapted augmentation method specialized for dataset distillation, but it is not as effective as standard data augmentation methods. SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6) applies MixUp [\(Zhang](#page-12-13) [et al.,](#page-12-13) [2018\)](#page-12-13) to the synthetic dataset, achieving superior performance across many datasets. Unfortunately, the cost of applying MixUp in SRe2L is expensive, due to the massive volume of soft labels. The soft labels are dynamically generated for each augmented instance in each validation epoch, resulting enormous storage requirements or extra training efforts. For example, the synthetic dataset generated by SRe2L for ImageNet-1k requires 0.7GB to store synthetic images, but requires additional 25.9GB to store the soft labels.

Motivated by this, we aim to apply MixUp to our INFER model in the same way it is used in natural datasets, which we refer to as "static" soft labels. The linear interpolation of labels can be represented mathematically as:

$$
f_{\theta_{\mathcal{T}}}[\lambda \tilde{s}_i + (1 - \lambda)\tilde{s}_j] \n\approx \lambda f_{\theta_{\mathcal{T}}}(\tilde{s}_i) + (1 - \lambda)f_{\theta_{\mathcal{T}}}(\tilde{s}_j), \forall \tilde{s}_i, \tilde{s}_j \in \tilde{\mathcal{S}} \tag{5}
$$

where  $f_{\theta_{\mathcal{T}}}(\cdot)$  represents the logits output,  $\lambda \sim \text{Beta}(\beta, \beta)$ , and  $\beta > 0$ . To achieve this, we propose three improvements in INFER: (a) We use  $\mathcal{P}^k$ , a subset of natural datasets, for integration with UFCs because natural instances inherently follow the linear interpolation of labels. (b) We make  $\mathcal{P}^k$ to span across all the classes, rather than limiting it to instances within the same class. As such, the optimized UFCs,  $U$ , which are integrated with  $\mathcal{P}^k$  for optimization, also embody the characteristic of linear label interpolation. (c) We employ M neural networks with various architectures ( $M = |\mathcal{U}^k|$ )

<span id="page-6-1"></span>Table 1: Comparison with SOTAs on CIFAR-10/100 and Tiny-ImageNet. Except for SRe2L [\(Yin](#page-12-6) [et al.,](#page-12-6) [2024\)](#page-12-6), G-VBSM [\(Shao et al.,](#page-11-5) [2024\)](#page-11-5), and our INFER, all other methods use ConvNet128 for distillation. The distilled synthetic datasets are then evaluated on ConvNet128 and ResNet18. "IN-FER+Dyn" denotes the application of INFER using dynamically generated soft labels, as described in SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6). The best performers in each setting are highlighted in red.

|            |                                |                   | CIFAR-10          | CIFAR-100         |                   |                   | Tiny-ImageNet     |                      |
|------------|--------------------------------|-------------------|-------------------|-------------------|-------------------|-------------------|-------------------|----------------------|
|            | ipc                            | 10                | 50                | 10                | 50                | 100               | 10                | 50                   |
|            | Random                         | 31.0<br>$\pm 0.5$ | 50.6<br>$\pm 0.3$ | 14.6<br>$\pm 0.5$ | 33.4<br>$\pm 0.4$ | 42.8<br>$\pm 0.3$ | 5.0<br>$\pm 0.2$  | 15.0<br>$\pm 0.4$    |
|            | DC (Zhao et al., 2021)         | 44.9<br>$\pm 0.5$ | 53.9<br>$\pm 0.5$ | 25.2<br>$\pm 0.3$ |                   |                   |                   |                      |
|            | DSA (Zhao & Bilen, 2021)       | 52.1<br>$\pm 0.5$ | 60.6<br>$\pm 0.5$ | 32.3<br>$\pm 0.3$ | 42.8<br>$\pm 0.4$ |                   |                   |                      |
|            | $KIP$ (Nguyen et al., 2021)    | 62.7<br>$\pm 0.3$ | 68.6<br>$\pm 0.2$ | 28.3<br>$\pm 0.1$ |                   |                   |                   |                      |
|            | RFAD (Loo et al., 2022)        | 66.3<br>±0.5      | 71.1<br>$\pm 0.4$ | 33.0<br>$\pm 0.3$ |                   |                   |                   |                      |
| ConvNet128 | MTT (Cazenavette et al., 2022) | 65.4<br>±0.7      | 71.6<br>$\pm 0.2$ | 39.7<br>$\pm 0.4$ | 47.7<br>$\pm 0.2$ | 49.2<br>$\pm 0.4$ | 23.2<br>$\pm 0.2$ | 28.0<br>$\pm 0.3$    |
|            | SeqMatch (Du et al., 2023b)    | 66.2<br>$\pm 0.6$ | 74.4<br>±0.5      | 41.9<br>±0.5      | 51.2<br>$\pm 0.3$ |                   | 23.8<br>$\pm 0.3$ |                      |
|            | G-VBSM (Shao et al., 2024)     | 46.5<br>$\pm 0.7$ | 54.3<br>$\pm 0.3$ | 38.7<br>$\pm 0.2$ | 45.7<br>$\pm 0.4$ |                   |                   |                      |
|            | <b>INFER</b>                   | 34.0<br>$+0.4$    | 57.0<br>$\pm 0.2$ | 41.0<br>$\pm 0.4$ | 53.8<br>$\pm 0.2$ | 57.0<br>±0.02     | 22.8<br>$+0.3$    | 32.3<br>$\pm 0.3$    |
|            | INFER+Dyn                      | 30.1<br>$\pm 0.8$ | 52.4<br>± 0.7     | 37.2<br>$\pm 0.3$ | 50.7<br>$\pm 0.3$ | 53.4<br>$\pm 0.2$ | 24.9<br>±0.3      | 33.9<br>$\pm 0.6$    |
|            | Random                         | 29.6<br>$+0.9$    | 36.7<br>±1.7      | 15.8<br>$\pm 0.2$ | 32.0<br>±0.0      | 47.5<br>$\pm 0.0$ | 12.1<br>$\pm 0.3$ | 17.7<br>$_{\pm 0.0}$ |
|            | SRe2L (Yin et al., 2024)       | 27.2<br>$\pm 0.5$ | 47.5<br>$\pm 0.6$ | 31.6<br>$\pm 0.5$ | 49.5<br>$\pm 0.3$ |                   |                   | 41.1<br>$\pm 0.4$    |
| ResNet18   | G-VBSM (Shao et al., 2024)     | 53.5<br>$\pm 0.6$ | 59.2<br>$\pm 0.4$ | 59.5<br>$\pm$ 0.4 | 65.0<br>$\pm 0.5$ |                   |                   | 47.6<br>$\pm 0.3$    |
|            | <b>INFER</b>                   | 32.0<br>$\pm 0.5$ | 60.4<br>$\pm 1.6$ | 45.2<br>±0.04     | 62.8<br>$\pm 0.4$ | 66.3<br>$\pm 0.1$ | 32.0<br>$\pm 0.1$ | 52.9<br>$\pm 0.1$    |
|            | INFER+Dyn                      | 30.7<br>$\pm 0.3$ | 60.7<br>±0.9      | 53.4<br>$\pm 0.6$ | 68.9<br>±0.1      | 73.3<br>$\pm 0.2$ | 41.0<br>±0.4      | 54.6<br>±0.4         |

to relabel the generated synthetic data instance  $\tilde{s}_i$  through averaging, *i.e.*,

<span id="page-6-0"></span>
$$
\tilde{\boldsymbol{y}}_i = \frac{1}{M} \sum_m f^m_{\theta \tau}(\tilde{\boldsymbol{s}}_i). \tag{6}
$$

By doing so, INFER does not require the dynamic soft labels for any combinations of  $[\lambda \tilde{s}_i + (1 \lambda$ ) $\tilde{s}_j$ ], but only the static soft labels of  $\tilde{s}_i$ ,  $\tilde{s}_j$ , which reduces the size of soft labels by up to 99.9%. Additionally, training on synthetic datasets can follow the same paradigm as training on natural datasets. More details of synthesizing and training  $S$  can be found in Algorithm [1](#page-5-0) and Algorithm [2.](#page-13-0)

## 4 EXPERIMENTS

To evaluate the effectiveness of our proposed INFER distillation paradigm, we conducted a series of experiments across multiple benchmark datasets and compared our results with several stateof-the-art approaches. In this section, we provide details on the experimental setup, the datasets used, and the results obtained. We summarize our main results in [Table 1](#page-6-1) and [Table 2.](#page-7-0) Following this, we perform ablation studies to assess the impact of individual components of our method. All experiments were conducted using two Nvidia 3090 GPUs and one Tesla A-100 GPU.

#### 4.1 EXPERIMENTAL SETUP

Baselines and Datasets. We conduct the comparison with several representative distillation methods, including Random, DC [\(Zhao et al.,](#page-12-7) [2021\)](#page-12-7), DSA [\(Zhao & Bilen,](#page-12-8) [2021\)](#page-12-8), KIP [\(Nguyen et al.,](#page-11-11) [2021\)](#page-11-11), RFAD [\(Loo et al.,](#page-11-12) [2022\)](#page-11-12), MTT [\(Cazenavette et al.,](#page-10-5) [2022\)](#page-10-5), SeqMatch [\(Du et al.,](#page-10-8) [2023b\)](#page-10-8), G-VBSM [\(Shao et al.,](#page-11-5) [2024\)](#page-11-5) and SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6). This evaluation is performed on four popular classification benchmarks, including CIFAR-10/100 [\(Krizhevsky et al.,](#page-11-15) [2009\)](#page-11-15), Tiny-ImageNet [\(Le](#page-11-16) [& Yang,](#page-11-16) [2015\)](#page-11-16), and ImageNet-1k [\(Deng et al.,](#page-10-15) [2009\)](#page-10-15).

**Implementation Details.** Our INFER uses  $M = 4$ , meaning it employs four different architectures for optimizing UFCs: ResNet18 [\(He et al.,](#page-10-16) [2016\)](#page-10-16), MobileNetv2 [\(Sandler et al.,](#page-11-17) [2018\)](#page-11-17), Efficient-

<span id="page-7-0"></span>Table 2: Comparison with SOTAs on ImageNet-1k. SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6), G-VBSM [\(Shao](#page-11-5) [et al.,](#page-11-5) [2024\)](#page-11-5), and our INFER use ResNet18 for distillation. The distilled synthetic datasets are then evaluated on ResNet18, 50, and 101. "INFER+Dyn" denotes the application of INFER using dynamically generated soft labels, as described in SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6). We also evaluate SRe2L and G-VBSM under the same compression ratio using our static labeling strategy, denoted as {}<sup>∗</sup> . The best performers in each setting are highlighted in red.

| ipc                            | <b>Compression Ratio</b> |        | ResNet18          |                   | ResNet-50         |                   | ResNet-101        |                   |
|--------------------------------|--------------------------|--------|-------------------|-------------------|-------------------|-------------------|-------------------|-------------------|
|                                | 10                       | 50     | 10                | 50                | 10                | 50                | 10                | 50                |
| Random                         | 0.78%                    | 3.90%  | 10.5<br>$\pm 0.4$ | 31.4<br>$\pm 0.3$ | 9.3<br>$\pm 0.3$  | 31.5<br>$\pm 0.2$ | 10.0<br>$\pm 0.4$ | 33.1<br>$\pm 0.1$ |
| SRe2L* (Yin et al., 2024)      | 0.81%                    | 4.04%  | 9.8<br>$\pm 0.1$  | 17.3<br>$\pm 0.5$ | 8.7<br>$\pm 0.3$  | 17.2<br>$\pm 0.4$ | 8.8<br>$\pm 0.2$  | 15.8<br>$\pm 0.2$ |
| $G-VBSM^*$ (Shao et al., 2024) | 0.81%                    | 4.04%  | 11.9<br>$\pm 0.2$ | 32.9<br>$\pm 0.1$ | 14.5<br>$\pm 0.2$ | 38.1<br>$\pm 0.2$ | 13.9<br>$\pm 0.1$ | 38.9<br>$\pm 0.4$ |
| <b>INFER</b>                   | 0.81%                    | 4.04%  | 28.7<br>$\pm 0.2$ | 51.8<br>$\pm 0.2$ | 26.9<br>$\pm 0.3$ | 53.3<br>$\pm 0.3$ | 26.5<br>$\pm 0.1$ | 52.2<br>$\pm 0.3$ |
| SRe2L (Yin et al., 2024)       | 4.53%                    | 22.67% | 21.3<br>$\pm 0.6$ | 46.8<br>$\pm 0.2$ | 28.4<br>$\pm 0.1$ | 55.6<br>$\pm 0.3$ | 30.9<br>$\pm 0.1$ | 60.8<br>$\pm 0.5$ |
| $G-VBSM$ (Shao et al., 2024)   | 4.53%                    | 22.67% | 31.4<br>$\pm 0.5$ | 51.8<br>$\pm 0.4$ | 35.4<br>$\pm 0.8$ | 58.7<br>$\pm 0.3$ | 38.2<br>$\pm 0.4$ | 61.0<br>$\pm 0.4$ |
| <b>INFER+Dyn</b>               | 4.53%                    | 22.67% | 36.3<br>$\pm 0.3$ | 55.6<br>$\pm 0.2$ | 38.3<br>$\pm 0.5$ | 63.4<br>$\pm 0.3$ | 38.9<br>$\pm 0.5$ | 60.7<br>$\pm 0.1$ |

NetB0 [\(Tan & Le,](#page-12-15) [2019\)](#page-12-15), and ShuffleNetv2 [\(Ma et al.,](#page-11-18) [2018\)](#page-11-18). When distilling ImageNet-1k, only the first three architectures ( $M = 3$ ) are involved. For reproducibility, the hyperparameter settings for the experimental datasets—CIFAR-10/100, Tiny-ImageNet, and ImageNet-1k, are provided in Appendix [A.3.](#page-14-0) These settings generally follow SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6), with the sole modification being a proportional reduction in the validation epoch number for the dynamic version to ensure fair comparison. All other critical hyperparameters remain unchanged.

Consistant Distillation Budget. As we stated in [Section 2,](#page-2-3) the baselines SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6) and G-VBSM [\(Shao et al.,](#page-11-5) [2024\)](#page-11-5) use the dynamic generated soft labels from a teacher model in each validation epoch for enhanced performance. However, these additional dynamic soft labels are not considered in the "Images Per Class (IPC)" distillation budget. Therefore, we also adopt the compression ratio ( $CR =$  synthetic dataset size/original dataset size [\(Cui et al.,](#page-10-13) [2022\)](#page-10-13)) for consistant distillation budget. As the size of the soft labels is proportional to the number of validation epochs, we only report the CR in ImageNet-1k dataset, as shown in [Table 2.](#page-7-0)

Our INFER also employs the soft labels as shown in [Equation 6.](#page-6-0) However, INFER only stores one soft label per instance, which equals **one** epoch dynamic soft labels. We term it as static soft labels in contrast to the dynamic soft labels generated across every validation epochs. Therefore, INFER reduces the size of soft labels by up to 99.3% in ImageNet-1k dataset (from 300 epoch to 1 epoch). We also implement the dynamic soft labels under INFER, denoted as "INFER+Dyn". As increasing the validation epoch can improve the performance, but it incurs a larger size of soft labels. Another point to note is, to ensure fair comparison, we reduce the validation epoch to  $\frac{1}{M}$ , as INFER generates M-fold synthetic instances by integrating UFC ( $M$  is 3 for ImageNet-1k). Lastly, we also average the number of UFCs into ipc and adjust  $K = \lfloor \text{ipc} \times \frac{C}{C+M} \rfloor$  for fair comparison.

#### 4.2 MAIN RESULTS

Performance results on CIFAR-10/100 and Tiny-ImageNet are summarized in [Table 1.](#page-6-1) While most previous methods rely on ConvNet128 due to resource constraints, SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6), G-VBSM [\(Shao et al.,](#page-11-5) [2024\)](#page-11-5), and our INFER method use ResNet18 for synthesis. INFER significantly outperforms competitors. With ipc=50, ResNet18 trained on the distilled CIFAR-100 achieves 68.9% and 62.8% accuracy, surpassing SRe2L by 19.4% and 13.3%, respectively. INFER also outperforms G-VBSM by 5.3% on Tiny-ImageNet. Despite reducing 99.3% of dynamic soft labels, INFER still outperforms methods using dynamic labels. Static labels suffice for simpler networks and datasets, but dynamic labels provide stronger supervision for complex architectures.

[Table 2](#page-7-0) provides a detailed performance comparison between SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6), the pioneering method for scaling dataset distillation to large datasets like ImageNet-1k, its extension G-VBSM [\(Shao et al.,](#page-11-5) [2024\)](#page-11-5), and our proposed INFER approach. These methods use ResNet18 for dataset distillation, and their performance is evaluated on three architectures: ResNet18, ResNet50, and ResNet101. The table highlights the advantages of our INFER method, which consistently outperforms SRe2L across all evaluation settings. Notably, when distilling datasets with  $ipc=50$ ,

<span id="page-8-0"></span>Image /page/8/Figure/1 description: The image contains two plots. Plot (a) is a line graph titled "Feature Duplication vs ipc". The x-axis is labeled "ipc" and ranges from 0 to 100. The y-axis is labeled "Feature Duplication (Lower is better)" and ranges from 0.50 to 0.85. There are three lines plotted: "Original" (light purple circles), "INFER" (medium purple stars), and "SRe2L" (dark purple triangles). All lines show an increasing trend as "ipc" increases, with "SRe2L" consistently having the lowest feature duplication, followed by "INFER", and then "Original". Plot (b) is a bar chart titled "Ablation study of UFC". The x-axis shows two categories: "INFER" and "INFER+Dyn". The y-axis is labeled "Top-1 Acc. (%)" and ranges from 0.0 to 75.0. For each category, there are two bars: "w/o UFC" (light purple) and "w/ UFC" (dark purple). For "INFER", the "w/o UFC" bar reaches approximately 17.0% and the "w/ UFC" bar reaches approximately 42.0%, with a +25.6% difference indicated above the "w/ UFC" bar. For "INFER+Dyn", the "w/o UFC" bar reaches approximately 40.0% and the "w/ UFC" bar reaches approximately 57.0%, with a +16.8% difference indicated above the "w/ UFC" bar.

Figure 4: Left: The change in feature duplication with the increase of  $ipc$ . To measure the level of feature duplication, we employ the averaged cosine similarities between each pair of synthetic data instances within the same class. Therefore, a greater value represents higher feature duplication, as SRe2L [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6) shows. In contrast, our INFER obtains a lower feature duplication, which is closer to the level observed in natural datasets. **Right:** The ablation study of UFC. The first two groups are under the  $\text{inc} = 10$  setting, while the other two are under  $\text{inc} = 50$ . The purple annotations indicate the performance gains contributed by our UFC.

<span id="page-8-1"></span>Table 3: Ensemble of architectures for UFC generation. "R", "M", "E", and "S" represent ResNet18 [\(He et al.,](#page-10-16) [2016\)](#page-10-16), MobileNetv2 [\(Sandler et al.,](#page-11-17) [2018\)](#page-11-17), EfficientNetB0 [\(Tan & Le,](#page-12-15) [2019\)](#page-12-15), and ShuffleNetV2 [\(Ma et al.,](#page-11-18) [2018\)](#page-11-18), respectively.  $\checkmark$  indicates the network architectures participating in UFC generation. ↑ denotes the performance gain contributed by the current ensembles compared with the baseline (only ResNet18). These experiments are conducted on CIFAR-100 dataset.

|   |   |   |                      |        | $\texttt{inc} = 10$ |         | $\texttt{ipc} = 50$  |        |                |        |  |  |
|---|---|---|----------------------|--------|---------------------|---------|----------------------|--------|----------------|--------|--|--|
| R | M | E | <b>INFER</b>         | ᄉ      | INFER+Dyn           | ᠰ       | <b>INFER</b>         | ᠰ      | INFER+Dyn      |        |  |  |
| ✔ |   |   | $40.9 \pm 0.1$       | $+0.0$ | $38.1$<br>$\pm 1.7$ | $+0.0$  | $59.7$<br>$\pm 0.2$  | $+0.0$ | $65.3 \pm 0.2$ | $+0.0$ |  |  |
|   |   |   | 43.2                 | $+2.3$ | $46.2 \pm 0.4$      | $+8.1$  | $61.2$<br>$\pm 0.04$ | $+1.5$ | $67.3 \pm 0.1$ | $+2.0$ |  |  |
|   |   |   | $44.8$<br>$\pm 0.5$  | $+3.9$ | $50.6 \pm 0.7$      | $+12.5$ | $61.7 + 0.1$         | $+2.0$ | 68.3           | $+3.0$ |  |  |
|   |   |   | $45.2$<br>$\pm 0.04$ | $+4.3$ | 53.4<br>$+0.6$      | $+16.3$ | 62.8                 | $+3.1$ | $68.9 \pm 0.1$ | $+3.6$ |  |  |

INFER achieves a substantial 5.0% performance improvement on ResNet18 while maintaining an extremely compact synthetic dataset—only 4.04% of the original size. When matched to the same compression ratio, the performance gap further widens to 34.5%. This result showcases the superior efficiency and effectiveness of INFER in large-scale dataset distillation, particularly in compressing datasets while maintaining performance.

#### 4.3 ABLATION STUDY

Compensator Generation. We examine the effectiveness of the proposed UFC. As shown in [Fig](#page-8-0)[ure 4](#page-8-0) (b), regardless of the  $\pm$  pc setting and the labeling strategy, our UFC significantly enhances the quality of the distilled dataset. For example, the Top-1 classification accuracy of the model trained with static labels is improved by  $25.6\%$  with  $\text{ipc} = 10$ . We also study the influence of network architectures participating compensator generation. According to the results provided in [Table 3,](#page-8-1) performance consistently improves with the addition of more ensembled networks. This trend is particularly pronounced with dynamic labeling. For instance, the ensemble of four architectures enhances performance by 4.3% with static labeling and by 16.3% with dynamic labeling. To align with the multi-model-aided compensator generation, we also employ multiple networks for soft la-bel generation. [Table 6](#page-16-0) in Appendix [A.5](#page-15-0) presents the model performance trained with soft labels generated by various architecture ensembles.

Cross-Architecture Generalization. [Table 4](#page-9-0) presents the performance evaluation of synthetic CIFAR-100 dataset across different architectures, trained from scratch. When  $ipc=10$  , our IN-FER+Dyn and INFER methods outperform SRe2L across all architectures. For instance, IN-FER+Dyn achieves an accuracy of 52.3% on ResNet50, significantly higher than the 22.4% achieved by SRe2L. For ipc=50, the performance advantage of INFER+Dyn and INFER remains evident. INFER+Dyn reaches an accuracy of 70.0% on ResNet50, far surpassing SRe2L. Our INFER shows a well generalization abilities across different architectures. The cross-architecture results on ImageNet, shown in [Table 2,](#page-7-0) further confirm the effectiveness of our methods. Specifically, INFER+Dyn and INFER demonstrate superior performance compared to SRe2L and G-VBSM across various ResNet architectures on ImageNet-1k.

<span id="page-9-0"></span>Table 4: Cross-architecture performance of distilled dataset. Here, the synthetic CIFAR-100 datasets are evaluated by training ResNet-50, ResNet-101 [\(He et al.,](#page-10-16) [2016\)](#page-10-16), MobileNetV2 [\(Sandler et al.,](#page-11-17) [2018\)](#page-11-17), EfficientNetB0 [\(Tan & Le,](#page-12-15) [2019\)](#page-12-15), and ShuffleNetV2 [\(Ma et al.,](#page-11-18) [2018\)](#page-11-18) from scratch. The best performers in each setting are highlighted in red.

Image /page/9/Figure/2 description: The image displays a table comparing the performance of different networks (ResNet50, MobileNetV2, EfficientNetB0, ShuffleNetV2) under two conditions: ipc = 10 and ipc = 50. For each condition, performance is measured by SRe2L, INFER, and INFER+Dyn. The table shows numerical values with associated error margins. Below the table are three 3D surface plots illustrating the cross-entropy loss in pixel space. The first plot is labeled 'Original', the second 'SRe2L', and the third 'INFER (ours)'. Each plot indicates a 'Decision Boundary' and the 'Centroid of a class'. A color bar on the right side of the plots indicates the scale for the cross-entropy loss, ranging from 2 to 14.

<span id="page-9-1"></span>Figure 5: Visualizations of loss landscapes in pixel space on CIFAR-100 dataset. The optimal decision boundary is supposed to have a rapid change in cross-entropy loss at the edge, indicating a clear and distinctive decision boundary. Left: A distinctive decision boundary trained on the original dataset  $\mathcal{T}$ . **Middle:** A less distinctive decision boundary trained on the synthetic dataset of outstanding class-specific approach SRe2L. Right: An improved decision boundary trained on the synthetic dataset of INFER.

#### 4.4 MORE DISCUSSIONS WITH SOTA METHOD SRE2L

**Feature Duplication Study.** We verify our hypothesis that synthetic data instances tend to capture distinctive yet duplicated intra-class features under the traditional class-specific distillation paradigm. We measure feature duplication by averaging the cosine similarities between each pair of synthetic data instances within the same class. Our experimental results, as shown in Figure  $4(a)$ , support our hypothesis: the class-specific approach SRe2L exhibits higher feature duplication as the number of ipc increases. Conversely, our INFER, achieves improved feature uniqueness, more closely resembling natural datasets. This reduction in intra-class feature duplication significantly enhances the diversity of the distilled dataset, which, in turn, improves the training performance.

**Visualization on Decision Boundaries.** To verify our hypothesis regarding the "oversight of interclass features" in the traditional class-specific distillation paradigm, we visualize the decision boundaries of ResNet-18 models trained with synthetic datasets generated by SRe2L and our INFER, respectively. We randomly select seven classes from the CIFAR-100 dataset and use the t-SNE approach for visualization. As illustrated in [Figure 2,](#page-2-0) INFER forms thin and clear decision boundaries between classes, in contrast to the chaotic decision boundaries produced by the traditional approach. Additionally, we visualized the 3D loss landscape in pixel spaces of the decision boundaries in [Figure 5,](#page-9-1) which further supports our hypothesis from a different perspective.

For further analysis, refer to Appendix [A.6,](#page-15-1) where we provide additional insights into the performance improvements of INFER.

## 5 CONCLUSION

In this work, we rethink the current "one class per instance" paradigm in dataset distillation and identified its limitations, including inefficient utilization of the distillation budget and oversight of inter-class features. These issues arise as distillation techniques advance, leading to synthetic data that often captures duplicated class-specific features. To address these limitations, we introduce a novel paradigm INFER that employs a Universal Feature Compensator (UFC) for "one instance for all classes" distillation. Our experimental results demonstrate that INFER improves the efficiency and effectiveness of dataset distillation, achieving state-of-the-art results in several datasets, reducing resource requirements while maintaining high performance. Future work will focus on scaling INFER for extremely large dataset and exploring its application in various real-world scenarios.

# ACKNOWLEDGEMENT

This research is supported by Jiawei Du's A\*STAR Career Development Fund (CDF) C233312004.

#### **REFERENCES**

- <span id="page-10-10"></span>Olivier Bachem, Mario Lucic, and Andreas Krause. Practical coreset constructions for machine learning. *arXiv preprint arXiv:1703.06476*, 2017.
- <span id="page-10-3"></span>Rodrigo Benenson, Stefan Popov, and Vittorio Ferrari. Large-scale interactive object segmentation with human annotators. In *CVPR*, 2019.
- <span id="page-10-0"></span>Zhongang Cai, Wanqi Yin, Ailing Zeng, Chen Wei, Qingping Sun, Wang Yanjun, Hui En Pang, Haiyi Mei, Mingyuan Zhang, Lei Zhang, et al. Smpler-x: Scaling up expressive human pose and shape estimation. In *NeurIPS*, 2024.
- <span id="page-10-5"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022.
- <span id="page-10-11"></span>Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. In *UAI*, 2010.
- <span id="page-10-13"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. *arXiv preprint arXiv:2207.09639*, 2022.
- <span id="page-10-6"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *ICML*, 2023.
- <span id="page-10-15"></span>Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, 2009.
- <span id="page-10-17"></span>Wenxiao Deng, Wenbin Li, Tianyu Ding, Lei Wang, Hongguang Zhang, Kuihua Huang, Jing Huo, and Yang Gao. Exploiting inter-sample and inter-feature relations in dataset distillation. In *CVPR*, 2024.
- <span id="page-10-1"></span>Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. In *ICLR*, 2021.
- <span id="page-10-7"></span>Jiawei Du, Yidi Jiang, Vincent Y. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *CVPR*, 2023a.
- <span id="page-10-8"></span>Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. In *NeurIPS*, 2023b.
- <span id="page-10-2"></span>Leo Gao, Stella Biderman, Sid Black, Laurence Golding, Travis Hoppe, Charles Foster, Jason Phang, Horace He, Anish Thite, Noa Nabeshima, et al. The pile: An 800gb dataset of diverse text for language modeling. *arXiv preprint arXiv:2101.00027*, 2020.
- <span id="page-10-14"></span>Ziyao Guo, Kai Wang, George Cazenavette, HUI LI, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *ICLR*, 2023.
- <span id="page-10-12"></span>Sariel Har-Peled and Akash Kushal. Smaller coresets for k-median and k-means clustering. In *Proceedings of the twenty-first annual symposium on Computational geometry*, 2005.
- <span id="page-10-16"></span>Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, 2016.
- <span id="page-10-4"></span>Jordan Hoffmann, Sebastian Borgeaud, Arthur Mensch, Elena Buchatskaya, Trevor Cai, Eliza Rutherford, Diego de Las Casas, Lisa Anne Hendricks, Johannes Welbl, Aidan Clark, et al. Training compute-optimal large language models. *arXiv preprint arXiv:2203.15556*, 2022.
- <span id="page-10-9"></span>Zixuan Jiang, Jiaqi Gu, Mingjie Liu, and David Z Pan. Delving into effective gradient matching for dataset condensation. *arXiv preprint arXiv:2208.00311*, 2022.

- <span id="page-11-1"></span>Jared Kaplan, Sam McCandlish, Tom Henighan, Tom B Brown, Benjamin Chess, Rewon Child, Scott Gray, Alec Radford, Jeffrey Wu, and Dario Amodei. Scaling laws for neural language models. *arXiv preprint arXiv:2001.08361*, 2020.
- <span id="page-11-8"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *ICML*, 2022.
- <span id="page-11-15"></span>Alex Krizhevsky, Vinod Nair, and Geoffrey Hinton. CIFAR-10 and CIFAR-100 datasets. *URl: https://www. cs. toronto. edu/kriz/cifar. html*, 6(1):1, 2009.
- <span id="page-11-16"></span>Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-11-14"></span>Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*, 2022a.
- <span id="page-11-6"></span>Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *ICML*, 2022b.
- <span id="page-11-3"></span>Shiye Lei and Dacheng Tao. A comprehensive survey of dataset distillation. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023.
- <span id="page-11-4"></span>Ping Liu and Jiawei Du. The evolution of dataset distillation: Toward scalable and generalizable solutions. *arXiv preprint arXiv:2502.05673*, 2025.
- <span id="page-11-7"></span>Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *NeurIPS*, 2022.
- <span id="page-11-0"></span>Ze Liu, Yutong Lin, Yue Cao, Han Hu, Yixuan Wei, Zheng Zhang, Stephen Lin, and Baining Guo. Swin transformer: Hierarchical vision transformer using shifted windows. In *ICCV*, 2021.
- <span id="page-11-12"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, 2022.
- <span id="page-11-18"></span>Ningning Ma, Xiangyu Zhang, Hai-Tao Zheng, and Jian Sun. Shufflenet v2: Practical guidelines for efficient cnn architecture design. In *ECCV*, 2018.
- <span id="page-11-20"></span>Zhiheng Ma, Anjia Cao, Funing Yang, and Xing Wei. Curriculum dataset distillation. *arXiv preprint arXiv:2405.09150*, 2024.
- <span id="page-11-10"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. *arXiv preprint arXiv:2011.00050*, 2020.
- <span id="page-11-11"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021.
- <span id="page-11-2"></span>Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *Transactions on Machine Learning Research*, 2023.
- <span id="page-11-19"></span>Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z. Liu, Yuri A. Lawryshyn, and Konstantinos N. Plataniotis. DataDAM: Efficient dataset distillation with attention matching. In *ICCV*, 2023.
- <span id="page-11-17"></span>Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *CVPR*, 2018.
- <span id="page-11-13"></span>Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018.
- <span id="page-11-5"></span>Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized largescale data condensation via various backbone and statistical matching. In *CVPR*, 2024.
- <span id="page-11-9"></span>Donghyeok Shin, Seungjae Shin, and Il-Chul Moon. Frequency domain-based dataset distillation. In *NeurIPS*, 2024.

- <span id="page-12-9"></span>Seungjae Shin, Heesun Bae, Donghyeok Shin, Weonyoung Joo, and Il-Chul Moon. Loss-curvature matching for dataset selection and condensation. In *AISTAS*, 2023.
- <span id="page-12-14"></span>Connor Shorten and Taghi M Khoshgoftaar. A survey on image data augmentation for deep learning. *Journal of big data*, 6(1):1–48, 2019.
- <span id="page-12-17"></span>Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *CVPR*, 2024.
- <span id="page-12-15"></span>Mingxing Tan and Quoc Le. Efficientnet: Rethinking model scaling for convolutional neural networks. In *ICML*, 2019.
- <span id="page-12-3"></span>Ilya O Tolstikhin, Neil Houlsby, Alexander Kolesnikov, Lucas Beyer, Xiaohua Zhai, Thomas Unterthiner, Jessica Yung, Andreas Steiner, Daniel Keysers, Jakob Uszkoreit, et al. Mlp-mixer: An all-mlp architecture for vision. In *NeurIPS*, 2021.
- <span id="page-12-2"></span>Hugo Touvron, Matthieu Cord, Matthijs Douze, Francisco Massa, Alexandre Sablayrolles, and Hervé Jégou. Training data-efficient image transformers  $\&$  distillation through attention. In *ICML*, 2021.
- <span id="page-12-16"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022.
- <span id="page-12-5"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-12-10"></span>Xing Wei, Anjia Cao, Funing Yang, and Zhiheng Ma. Sparse parameterization for epitomic dataset distillation. In *NeurIPS*, 2023.
- <span id="page-12-11"></span>Zhang Xin, Du Jiawei, Li Yunsong, Xie Weiying, and Joey Tianyi Zhou. Spanning training progress: Temporal dual-depth scoring (tdds) for enhanced dataset pruning. In *CVPR*, 2024.
- <span id="page-12-20"></span>Eric Xue, Yijiang Li, Haoyang Liu, Yifan Shen, and Haohan Wang. Towards adversarially robust dataset distillation by curvature regularization. *arXiv preprint arXiv:2403.10045*, 2024.
- <span id="page-12-0"></span>Lihe Yang, Bingyi Kang, Zilong Huang, Xiaogang Xu, Jiashi Feng, and Hengshuang Zhao. Depth anything: Unleashing the power of large-scale unlabeled data. In *CVPR*, 2024.
- <span id="page-12-18"></span>Zeyuan Yin and Zhiqiang Shen. Dataset distillation in large data era. 2023.
- <span id="page-12-6"></span>Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *NeurIPS*, 2024.
- <span id="page-12-4"></span>Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023.
- <span id="page-12-13"></span>Hongyi Zhang, Moustapha Cisse, Yann N Dauphin, and David Lopez-Paz. mixup: Beyond empirical risk minimization. In *ICLR*, 2018.
- <span id="page-12-8"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021.
- <span id="page-12-12"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023.
- <span id="page-12-7"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021.
- <span id="page-12-1"></span>Guangze Zheng, Shijie Lin, Haobo Zuo, Changhong Fu, and Jia Pan. Nettrack: Tracking highly dynamic objects with a net. In *CVPR*, 2024.
- <span id="page-12-19"></span>Muxin Zhou, Zeyuan Yin, Shitong Shao, and Zhiqiang Shen. Self-supervised dataset distillation: A good compression is all you need. *arXiv preprint arXiv:2404.07976*, 2024.

# A APPENDIX

#### A.1 TRAINING WITH SYNTHETIC DATASET

#### Algorithm 2 Training with synthetic dataset via Inter-class Feature Compensator (INFER)

<span id="page-13-0"></span>**Require:** Synthetic dataset S; A network  $f_\theta$  with weights  $\theta$ ; Mini-batch size b; Learning rate  $\eta$ ; Parameter  $\beta$  for MixUP. 1: Initialize  $\tilde{\mathcal{S}} = \{\}$ 2: for each  $\{\mathcal{P}^k, \widetilde{\mathcal{U}}^k, \mathcal{Y}^k\}$  in S do  $3: \rightarrow$  Construct integrated synthetic instance 4:  $\tilde{\mathcal{S}}^k = \{ (\tilde{s}_i, \tilde{y}_i) \mid \tilde{s}_i = x_i + u_j, \text{ for each } x_i \in \mathcal{P}^k \text{ and each } u_j \in \mathcal{U}^k, \tilde{y}_i \in \mathcal{Y}^k \}$ 5:  $\tilde{S} = \tilde{S} \cup \tilde{S}^k$ 6: end for 7:  $\triangleright$  Start training network  $f_\theta$ 8: for  $e = 1$  to E do 9: Randomly shuffle the synthetic dataset  $\tilde{S}$ 10: **for** each mini-batch  $\{(\tilde{s}_i^{(b)}, \tilde{y}_i^{(b)})\}$  **do** 11:  $\triangleright$  Augmented synthetic dataset by MixUP without additional relabel 12:  $\{(\tilde{s}_i^{(b)}, \tilde{y}_i^{(b)})\} = \text{MixUP}(\{(\tilde{s}_i^{(b)}, \tilde{y}_i^{(b)})\})$ 13: Compute loss function  $\mathcal{L}(f_{\theta}; \tilde{s}_i^{(b)}, \tilde{y}_i^{(b)})$   $\triangleright$  KL Loss 14: Update the weights:  $\theta \leftarrow \theta - \eta \nabla_{\theta} \mathcal{L}$ 15: end for 16: end for **Ensure:** The network  $f_{\theta}$  with converged weights  $\theta$ 17: **function** MIXUP( $\{(\bm{x}_i^{(b)}, \bm{y}_i^{(b)})\}, \beta)$ 18:  $\{(\boldsymbol{x}^{\prime}{}_{i}^{(b)}, \boldsymbol{y}^{\prime}{}_{i}^{(b)})\} \leftarrow \text{shuffle}\big(\{(\boldsymbol{x}^{\left(b\right)}_{i}, \boldsymbol{y}^{\left(b\right)}_{i})\}\big)$ ▷ Shuffle the batch of inputs and labels 19: Sample  $\lambda$  from Beta $(\beta, \beta)$  for the batch  $20:$  $\lambda' \leftarrow \max(\lambda, 1 - \lambda)$  > Ensure symmetry 21: **for**  $i = 1$  to b **do** 22:  $\tilde{x}_i \leftarrow \lambda' x_i + (1 - \lambda') x'_i$ <br>
23:  $\tilde{y}_i \leftarrow \lambda' y_i + (1 - \lambda') y'_i$ <br>
24: end for ▷ Linear interpolation of labels return  $\{(\tilde{{\bm{x}}}_i^{(b)}, \tilde{{\bm{y}}}_i^{(b)})\}$ 25: end function

#### A.2 MORE RELATED WORKS

The goal of dataset distillation is to create a condensed dataset that, despite its significantly reduced scale, maintains comparable performance to the original dataset. This concept was first introduced by Wang et al. [\(Wang et al.,](#page-12-5) [2018\)](#page-12-5) as a bi-level optimization problem. Building on this foundational work, recent advancements have broadened the range of techniques available for effectively and efficiently condensing representative knowledge into compact synthetic datasets. Techniques such as gradient matching [\(Zhao et al.,](#page-12-7) [2021;](#page-12-7) [Zhao & Bilen,](#page-12-8) [2021;](#page-12-8) [Lee et al.,](#page-11-6) [2022b;](#page-11-6) [Shin et al.,](#page-12-9) [2023\)](#page-12-9) optimize synthetic data to emulate the weight parameter gradients of the original dataset, while trajectory matching [\(Cazenavette et al.,](#page-10-5) [2022;](#page-10-5) [Cui et al.,](#page-10-6) [2023;](#page-10-6) [Du et al.,](#page-10-7) [2023a;](#page-10-7)[b\)](#page-10-8) aims to replicate gradient trajectories to tighten synthesis constraints, showcasing the focus on effectiveness. Additionally, factorized methods [\(Liu et al.,](#page-11-7) [2022;](#page-11-7) [Kim et al.,](#page-11-8) [2022;](#page-11-8) [Wei et al.,](#page-12-10) [2023;](#page-12-10) [Shin et al.,](#page-11-9) [2024\)](#page-11-9) use specialized decoders to generate highly informative images from condensed features, enhancing both the utility and efficiency of distilled datasets. Another strategy, distribution matching [\(Wang](#page-12-16) [et al.,](#page-12-16) [2022;](#page-12-16) [Zhao & Bilen,](#page-12-12) [2023;](#page-12-12) [Sajedi et al.,](#page-11-19) [2023;](#page-11-19) [Sun et al.,](#page-12-17) [2024;](#page-12-17) [Deng et al.,](#page-10-17) [2024\)](#page-10-17), optimizes synthetic data to align its feature distribution with that of the original dataset on a class-wise basis. Although its efficiency has significantly improved, the performance of this method may still lag behind those using gradient or trajectory matching. Despite these innovations, most methods continue to grapple with balancing final accuracy and computational efficiency, presenting challenges for their application to large-scale and real-world datasets.

<span id="page-14-1"></span>

|               |         | Optimizer                                     | Learning Rate         | Batch Size | Epoch/Iteration | Augmentation                                       | Architectures                                            |
|---------------|---------|-----------------------------------------------|-----------------------|------------|-----------------|----------------------------------------------------|----------------------------------------------------------|
| CIFAR-10/100  | Syn     | Adam<br>$\{\beta_1, \beta_2\} = \{0.5, 0.9\}$ | 0.25<br>cosine decay  | 100        | Iteration:1000  | -                                                  | {ResNet18, MobileNetv2,<br>EfficientNetB0, ShuffleNetV2} |
|               | Val     | AdamW<br>weight decay $= 0.01$                | 0.001<br>cosine decay | 64         | Epoch:400       | RandomCrop<br>RandomHorizontalFlip<br>MixUp        | {ResNet18, MobileNetv2,<br>EfficientNetB0, ShuffleNetV2} |
|               | Val+dyn | AdamW<br>weight decay = $0.01$                | 0.001<br>cosine decay | 64         | Epoch:80        | RandomCrop<br>RandomHorizontalFlip<br>MixUp        | {ResNet18, MobileNetv2,<br>EfficientNetB0, ShuffleNetV2} |
| Tiny-ImageNet | Syn     | Adam<br>$\{\beta_1, \beta_2\} = \{0.5, 0.9\}$ | 0.1<br>cosine decay   | 200        | Iteration:2000  | RandomResizedCrop<br>RandomHorizontalFlip          | {ResNet18, MobileNetv2,<br>EfficientNetB0, ShuffleNetV2} |
|               | Val     | SGD<br>weight decay $= 0.9$                   | 0.2<br>cosine decay   | 64         | Epoch:200       | RandomResizedCrop<br>RandomHorizontalFlip<br>MixUp | {ResNet18, MobileNetv2,<br>EfficientNetB0, ShuffleNetV2} |
|               | Val+dyn | SGD<br>weight decay $= 0.9$                   | 0.2<br>cosine decay   | 64         | Epoch:50        | RandomResizedCrop<br>RandomHorizontalFlip<br>MixUp | {ResNet18, MobileNetv2,<br>EfficientNetB0, ShuffleNetV2} |
| ImageNet-1k   | Syn     | Adam<br>$\{\beta_1, \beta_2\} = \{0.5, 0.9\}$ | 0.25<br>cosine decay  | 1000       | Iteration:2000  | RandomResizedCrop<br>RandomHorizontalFlip          | {ResNet18, MobileNetv2,<br>EfficientNetB0}               |
|               | Val     | AdamW<br>weight decay $= 0.01$                | 0.001<br>cosine decay | 32         | Epoch:300       | RandomResizedCrop<br>RandomHorizontalFlip<br>MixUp | {ResNet18, MobileNetv2,<br>EfficientNetB0}               |
|               | Val+dyn | AdamW<br>weight decay $= 0.01$                | 0.001<br>cosine decay | 32         | Epoch:75        | RandomResizedCrop<br>RandomHorizontalFlip<br>MixUp | {ResNet18, MobileNetv2,<br>EfficientNetB0}               |

Table 5: Training recipes of CIFAR-10/100, Tiny-ImageNet, and ImageNet-1k.

To further enhance dataset distillation for large datasets like ImageNet-1k [\(Deng et al.,](#page-10-15) [2009\)](#page-10-15), researchers have developed various innovative strategies to overcome inherent challenges. Cui et al. [\(Cui et al.,](#page-10-6) [2023\)](#page-10-6) introduced unrolled gradient computation with constant memory usage to manage computational demands efficiently. Following this, Yin et al. [\(Yin et al.,](#page-12-6) [2024\)](#page-12-6) developed the SRe2L framework, which decouples the bi-level optimization of models and synthetic data during training to accommodate varying dataset scales. Recognized for its excellent performance and adaptability, this framework has spurred further research. Further, Yin et al. proposed the Curriculum Data Augmentation (CDA) [\(Yin & Shen,](#page-12-18) [2023\)](#page-12-18), a method that enhances accuracy without substantial increases in computational costs. Based on SRe2L, Zhou et al. [\(Zhou et al.,](#page-12-19) [2024\)](#page-12-19) developed SC-DD, a Self-supervised Compression framework for dataset distillation that enhances the compression and recovery of diverse information, leveraging the potential of large pretrained models. Additionally, Xuel et al. [\(Xue et al.,](#page-12-20) [2024\)](#page-12-20) focused on improving the robustness of distilled datasets by incorporating regularization during the squeezing stage of the SRe2L process. In Generalized Various Backbone and Statistical Matching (G-VBSM) [\(Shao et al.,](#page-11-5) [2024\)](#page-11-5), Shao et al. introduced a "local-match-global" matching technique based on SRe2L, which yields more precise and effective results, producing a synthetic dataset with richer information and enhanced generalization capabilities. Most recently, the Curriculum Dataset Distillation (CUDD) [\(Ma et al.,](#page-11-20) [2024\)](#page-11-20) was introduced, employing a strategic, curriculum-based approach to distillation that balances scalability and efficiency. This framework systematically distills synthetic images, progressing from simpler to more complex tasks.

#### <span id="page-14-0"></span>A.3 TRAINING RECIPES

The hyperparameter settings for the experimental datasets CIFAR-10/100, Tiny-ImageNet, and ImageNet-1k are listed in [Table 5.](#page-14-1)

#### A.4 VISUALIZATION OF GENERATED COMPENSATOR

To offer a clearer understanding of how the compensator enhances distillation outcomes, we visualize the compensators in [Figure 7](#page-15-2) and [Figure 6.](#page-15-3) These visualizations reveal varying compensator patterns across different models and initialization instances.

<span id="page-15-3"></span>Image /page/15/Picture/1 description: This image displays a grid of images, likely generated by different neural network models. The rows are labeled with model names: ResNes-18, MobileNetv2, and EfficientNetB0. Each row contains five images, suggesting a comparison of how these models process or generate different types of content. The first column shows abstract, textured patterns. The subsequent columns display recognizable objects: a red sports car, yellow school buses, a dog, and a stopwatch. The overall presentation suggests an evaluation of visual output quality or characteristics across different AI models.

Figure 6: Visualizations of generated inter-class compensators of different models.

Image /page/15/Picture/3 description: This is a grid of images, likely generated by different models, as indicated by the title 'Visualizations of generated inter-class compensations of different models.' The grid contains multiple rows and columns of images, each depicting various subjects such as rowing, coral reefs, American flags, clock towers, birds, wolves, and jack-o'-lanterns. The first column appears to be a consistent pattern or noise, possibly representing a baseline or a specific type of input. The subsequent columns show variations of the same themes, suggesting the output of different generative models or techniques.

<span id="page-15-2"></span>Figure 7: Visualizations of generated inter-class compensators using different initialization instances.

<span id="page-15-0"></span>A.5 ENSEMBLE OF ARCHITECTURES FOR SOFT LABEL GENERATION

<span id="page-15-1"></span>A.6 MORE DISCUSSIONS WITH SOTA METHOD SRE2L

Generalization to Varying Sample Difficulties. As illustrated in Figure  $8$ , the x-axis shows the cross-entropy loss from a pretrained model, indicating sample difficulty. The graph reveals that IN-FER consistently outperforms SRe2L across all levels of sample difficulty, from easy to challenging. This demonstrates that INFER's performance improvements are comprehensive, providing a robust enhancement regardless of sample complexity.

Adaptability to Static Labeling. [Figure 9](#page-16-2) shows the Kullback-Leibler divergence (KLD) between dynamic and static labels. A smaller KLD indicates that our method adapts better to static labeling, making high compression rates feasible. In contrast, SRe2L relies heavily on dynamic labeling, which is more memory-intensive. This explains why our method achieves high compression rates while maintaining satisfactory performance.

<span id="page-16-0"></span>Table 6: Ensemble of architectures for soft label generation. "R", "M", "E", and "S" represent ResNet18 [\(He et al.,](#page-10-16) [2016\)](#page-10-16), MobileNetv2 [\(Sandler et al.,](#page-11-17) [2018\)](#page-11-17), EfficientNetB0 [\(Tan & Le,](#page-12-15) [2019\)](#page-12-15), and ShuffleNetV2 [\(Ma et al.,](#page-11-18) [2018\)](#page-11-18), respectively.  $\checkmark$  indicates the network architectures participating in soft labels generation. ↑ denotes the performance gain contributed by the current ensembles compared with the baseline (only ResNet18). These experiments are conducted on CIFAR-100 dataset.

|   |   |   |   | $\texttt{ipc} = 10$ |         |                   |        | $\texttt{ipc} = 50$ |        |                   |        |
|---|---|---|---|---------------------|---------|-------------------|--------|---------------------|--------|-------------------|--------|
|   |   |   |   | R                   | M       | E                 | S      | INFER               | ↑      | INFER+Dyn         | ↑      |
| ✓ | ✓ |   |   | 37.6<br>$\pm 0.5$   | $+0.0$  | 46.4<br>$\pm 0.2$ | $+0.0$ | 57.8<br>$\pm 0.1$   | $+0.0$ | 69.0<br>$\pm 0.1$ | $+0.0$ |
| ✓ | ✓ |   |   | 42.7<br>$\pm 0.1$   | $+ 5.1$ | 53.2<br>$\pm 1.0$ | $+6.8$ | 61.3<br>$\pm 0.3$   | $+3.5$ | 70.2<br>$\pm 0.3$ | $+1.2$ |
| ✓ | ✓ | ✓ |   | 44.1<br>$\pm 0.5$   | $+6.5$  | 53.0<br>$\pm 0.6$ | $+6.6$ | 61.8<br>$\pm 0.2$   | $+4.0$ | 69.0<br>$\pm 0.1$ | $+0.0$ |
| ✓ | ✓ | ✓ | ✓ | 45.2<br>$\pm 0.4$   | $+7.6$  | 53.4<br>$\pm 0.6$ | $+7.0$ | 62.8<br>$\pm 0.4$   | $+5.0$ | 68.9<br>$\pm 0.1$ | $-0.1$ |

Image /page/16/Figure/3 description: The image is a line and bar chart showing the relationship between CE loss and sample number/Top-1 Accuracy. The x-axis is labeled "CE loss" and ranges from 0 to 2. The left y-axis is labeled "Sample num. (x10^2)" and ranges from 0 to 16. The right y-axis is labeled "Top-1 Acc. (%)" and ranges from 20.0 to 100.0. There are two lines plotted: a purple line with circular markers representing "INFER" and a light purple line with triangular markers representing "SRe2L". There are also two sets of bars: light purple bars representing "SRe2L" and purple bars representing "INFER". The "INFER" line starts at approximately 95% accuracy at 0 CE loss and decreases to about 25% accuracy at 2 CE loss. The "SRe2L" line starts at approximately 80% accuracy at 0 CE loss and decreases to about 15% accuracy at 2 CE loss. The "INFER" bars are generally higher than the "SRe2L" bars, with "INFER" bars peaking around 5.5 (x10^2) at 6e-05 CE loss and "SRe2L" bars peaking around 4.5 (x10^2) at 0 CE loss. Both bar sets decrease as CE loss increases.

Figure 8: Performance on the validation set. The bars represent the number of samples correctly classified by SRe2L and our INFER, in each CE loss interval.

<span id="page-16-1"></span>Image /page/16/Figure/5 description: This is a line graph showing the relationship between lambda and KLD. The x-axis is labeled lambda and ranges from 0 to 1. The y-axis is labeled KLD and ranges from 0 to 3.5. There are two lines on the graph. The first line, labeled 'INFER', is represented by dark purple circles and shows a curve that starts at (0,0), rises to a peak of approximately (0.5, 2.2), and then falls back to (1,0). The second line, labeled 'SRe2L', is represented by light purple triangles and shows a curve that starts at (0,0), rises to a peak of approximately (0.5, 3.0), and then falls back to (1,0). The SRe2L line is consistently above the INFER line for most values of lambda between 0 and 1.

<span id="page-16-2"></span>Figure 9: Kullback-Leibler divergence (KLD) between dynamic labels and static labels.
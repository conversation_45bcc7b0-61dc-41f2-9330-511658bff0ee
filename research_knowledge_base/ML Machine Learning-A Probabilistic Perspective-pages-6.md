# **5 Bayesian statistics**

## **5.1 Introduction**

We have now seen a variety of different probability models, and we have discussed how to fit them to data, i.e., we have discussed how to compute MAP parameter estimates  $\hat{\theta}$  =  $\argmax p(\theta|\mathcal{D})$ , using a variety of different priors. We have also discussed how to compute the full posterior  $p(\theta|\mathcal{D})$ , as well as the posterior predictive density,  $p(\mathbf{x}|\mathcal{D})$ , for certain special cases (and in later chapters, we will discuss algorithms for the general case).

Using the posterior distribution to summarize everything we know about a set of unknown variables is at the core of **Bayesian statistics**. In this chapter, we discuss this approach to statistics in more detail. In Chapter 6, we discuss an alternative approach to statistics known as frequentist or classical statistics.

## **5.2 Summarizing posterior distributions**

The posterior  $p(\theta|\mathcal{D})$  summarizes everything we know about the unknown quantities  $\theta$ . In this section, we discuss some simple quantities that can be derived from a probability distribution, such as a posterior. These summary statistics are often easier to understand and visualize than the full joint.

### **5.2.1 MAP estimation**

We can easily compute a **point estimate** of an unknown quantity by computing the posterior mean, median or mode. In Section 5.7, we discuss how to use decision theory to choose between these methods. Typically the posterior mean or median is the most appropriate choice for a realvalued quantity, and the vector of posterior marginals is the best choice for a discrete quantity. However, the posterior mode, aka the MAP estimate, is the most popular choice because it reduces to an optimization problem, for which efficient algorithms often exist. Futhermore, MAP estimation can be interpreted in non-Bayesian terms, by thinking of the log prior as a regularizer (see Section 6.5 for more details).

Although this approach is computationally appealing, it is important to point out that there are various drawbacks to MAP estimation, which we briefly discuss below. This will provide motivation for the more thoroughly Bayesian approach which we will study later in this chapter (and elsewhere in this book).

Image /page/1/Figure/1 description: The image contains two plots, labeled (a) and (b). Plot (a) is a probability density function with the x-axis ranging from -2 to 4 and the y-axis ranging from 0 to 4.5. It shows a broad, low peak around x=-0.5 and a narrow, tall peak around x=2. A vertical blue line is shown at x=0, extending to y=4. Plot (b) is a decaying curve with the x-axis ranging from 0 to 7 and the y-axis ranging from 0 to 0.9. The curve starts at approximately (0, 0.9) and decreases exponentially towards the x-axis.

**Figure 5.1** (a) A bimodal distribution in which the mode is very untypical of the distribution. The thin blue vertical line is the mean, which is arguably a better summary of the distribution, since it is near the majority of the probability mass. Figure generated by bimodalDemo. (b) A skewed distribution in which the mode is quite different from the mean. Figure generated by gammaPlotDemo.

#### ********* No measure of uncertainty**

The most obvious drawback of MAP estimation, and indeed of any other **point estimate** such as the posterior mean or median, is that it does not provide any measure of uncertainty. In many applications, it is important to know how much one can trust a given estimate. We can derive such confidence measures from the posterior, as we discuss in Section 5.2.2.

#### **5.2.1.2 Plugging in the MAP estimate can result in overfitting**

In machine learning, we often care more about predictive accuracy than in interpreting the parameters of our models. However, if we don't model the uncertainty in our parameters, then our predictive distribution will be overconfident. We saw several examples of this in Chapter 3, and we will see more examples later. Overconfidence in predictions is particularly problematic in situations where we may be risk averse; see Section 5.7 for details.

#### **5.2.1.3 The mode is an untypical point**

Choosing the mode as a summary of a posterior distribution is often a very poor choice, since the mode is usually quite untypical of the distribution, unlike the mean or median. This is illustrated in Figure 5.1(a) for a 1d continuous space. The basic problem is that the mode is a point of measure zero, whereas the mean and median take the volume of the space into account. Another example is shown in Figure 5.1(b): here the mode is 0, but the mean is non-zero. Such skewed distributions often arise when inferring variance parameters, especially in hierarchical models. In such cases the MAP estimate (and hence the MLE) is obviously a very bad estimate.

How should we summarize a posterior if the mode is not a good choice? The answer is to use decision theory, which we discuss in Section 5.7. The basic idea is to specify a loss function, where  $L(\theta, \hat{\theta})$  is the loss you incur if the truth is  $\theta$  and your estimate is  $\hat{\theta}$ . If we use 0-1 loss,  $L(\theta, \hat{\theta}) = \mathbb{I}(\theta \neq \hat{\theta})$ , then the optimal estimate is the posterior mode. 0-1 loss means you only get "points" if you make no errors, otherwise you get nothing: there is no "partial credit" under

Image /page/2/Figure/1 description: The image displays a plot with a blue curve labeled 'g' that starts at (0,0) and increases to (10,1). There are two histograms: a green histogram labeled 'pY' on the left, and a red histogram labeled 'pX' on the right. A light blue horizontal line extends from the y-axis at 0.75 to the curve 'g', and a light blue vertical line extends from the curve 'g' down to the x-axis at approximately 6. The x-axis ranges from 0 to 12, and the y-axis ranges from 0 to 1.

**Figure 5.2** Example of the transformation of a density under a nonlinear transform. Note how the mode of the transformed distribution is not the transform of the original mode. Based on Exercise 1.4 of (Bishop 2006b). Figure generated by bayesChangeOfVar.

this loss function! For continuous-valued quantities, we often prefer to use squared error loss,  $L(\theta, \hat{\theta}) = (\theta - \hat{\theta})^2$ ; the corresponding optimal estimator is then the posterior mean, as we show in Section 5.7. Or we can use a more robust loss function,  $L(\theta, \hat{\theta}) = |\theta - \hat{\theta}|$ , which gives rise to the posterior median.

##### **5.2.1.4 MAP estimation is not invariant to reparameterization \***

A more subtle problem with MAP estimation is that the result we get depends on how we parameterize the probability distribution. Changing from one representation to another equivalent representation changes the result, which is not very desirable, since the units of measurement are arbitrary (e.g., when measuring distance, we can use centimetres or inches).

To understand the problem, suppose we compute the posterior for x. If we define  $y = f(x)$ , the distribution for  $y$  is given by Equation 2.87, which we repeat here for convenience:

$$
p_y(y) = p_x(x) \left| \frac{dx}{dy} \right| \tag{5.1}
$$

The  $\left|\frac{dx}{dy}\right|$  term is called the Jacobian, and it measures the change in size of a unit volume passed through f. Let  $\hat{x} = \argmax_{x} p_x(x)$  be the MAP estimate for x. In general it is not the case that  $\hat{y} = \argmax_{y} p_y(y)$  is given by  $f(\hat{x})$ . For example, let  $x \sim \mathcal{N}(6, 1)$  and  $y = f(x)$ , where

$$
f(x) = \frac{1}{1 + \exp(-x + 5)}
$$
\n(5.2)

We can derive the distribution of  $y$  using Monte Carlo simulation (see Section 2.7.1). The result is shown in Figure 5.2. We see that the original Gaussian has become "squashed" by the sigmoid nonlinearity. In particular, we see that the mode of the transformed distribution is not equal to the transform of the original mode.

To see how this problem arises in the context of MAP estimation, consider the following example, due to Michael Jordan. The Bernoulli distribution is typically parameterized by its mean  $\mu$ , so  $p(y = 1 | \mu) = \mu$ , where  $y \in \{0, 1\}$ . Suppose we have a uniform prior on the unit interval:  $p_{\mu}(\mu)=1$  I( $0 \leq \mu \leq 1$ ). If there is no data, the MAP estimate is just the mode of the prior, which can be anywhere between 0 and 1. We will now show that different parameterizations can pick different points in this interval arbitrarily.

First let  $\theta = \sqrt{\mu}$  so  $\mu = \theta^2$ . The new prior is

$$
p_{\theta}(\theta) = p_{\mu}(\mu) \left| \frac{d\mu}{d\theta} \right| = 2\theta \tag{5.3}
$$

for  $\theta \in [0, 1]$  so the new mode is

$$
\hat{\theta}_{MAP} = \arg\max_{\theta \in [0,1]} 2\theta = 1 \tag{5.4}
$$

Now let  $\phi = 1 - \sqrt{1 - \mu}$ . The new prior is

$$
p_{\phi}(\phi) = p_{\mu}(\mu)|\frac{d\mu}{d\phi}| = 2(1 - \phi)
$$
\n(5.5)

for  $\phi \in [0, 1]$ , so the new mode is

$$
\hat{\phi}_{MAP} = \arg \max_{\phi \in [0,1]} 2 - 2\phi = 0
$$
\n(5.6)

Thus the MAP estimate depends on the parameterization. The MLE does not suffer from this since the likelihood is a function, not a probability density. Bayesian inference does not suffer from this problem either, since the change of measure is taken into account when integrating over the parameter space.

One solution to the problem is to optimize the following objective function:

$$
\hat{\boldsymbol{\theta}} = \underset{\boldsymbol{\theta}}{\operatorname{argmax}} p(\mathcal{D}|\boldsymbol{\theta}) p(\boldsymbol{\theta}) |\mathbf{I}(\boldsymbol{\theta})|^{-\frac{1}{2}}
$$
\n(5.7)

Here  $I(\theta)$  is the Fisher information matrix associated with  $p(\mathbf{x}|\theta)$  (see Section 6.2.2). This estimate is parameterization independent, for reasons explained in (Jermyn 2005; Druilhet and Marin 2007). Unfortunately, optimizing Equation 5.7 is often difficult, which minimizes the appeal of the whole approach.

### **5.2.2 Credible intervals**

In addition to point estimates, we often want a measure of confidence. A standard measure of confidence in some (scalar) quantity  $\theta$  is the "width" of its posterior distribution. This can be measured using a  $100(1 - \alpha)\%$  credible interval, which is a (contiguous) region  $C = (\ell, u)$ (standing for lower and upper) which contains  $1 - \alpha$  of the posterior probability mass, i.e.,

$$
C_{\alpha}(\mathcal{D}) = (\ell, u) : P(\ell \le \theta \le u | \mathcal{D}) = 1 - \alpha \tag{5.8}
$$

There may be many such intervals, so we choose one such that there is  $(1 - \alpha)/2$  mass in each tail; this is called a **central interval**.

Image /page/4/Figure/1 description: The image displays two plots, labeled (a) and (b), side-by-side. Both plots feature a black curve that resembles a probability distribution, peaking around an x-value of 0.15 and decreasing towards zero as x approaches 1. The y-axis in both plots ranges from 0 to 3.5. In plot (a), a blue line segment starts at (0, 0) and goes up to (0, 1.2), then extends horizontally to approximately (0.4, 1.2), and finally slopes down to intersect the black curve at approximately (0.55, 0.5). Two vertical blue lines are also present, one at x=0 extending from y=0 to y=1.2, and another at x=0.55 extending from y=0 to y=0.5. In plot (b), a blue line segment starts at (0, 0) and goes up to (0, 0.6), then extends horizontally to approximately (0.45, 0.6), and finally slopes down to intersect the black curve at approximately (0.55, 0.5). Two vertical blue lines are present, one at x=0 extending from y=0 to y=0.6, and another at x=0.45 extending from y=0 to y=0.6.

**Figure 5.3** (a) Central interval and (b) HPD region for a Beta(3,9) posterior. The CI is (0.06, 0.52) and the HPD is (0.04, 0.48). Based on Figure 3.6 of (Hoff 2009). Figure generated by betaHPD.

If the posterior has a known functional form, we can compute the posterior central interval using  $\ell = F^{-1}(\alpha/2)$  and  $u = F^{-1}(1-\alpha/2)$ , where F is the cdf of the posterior. For example, if the posterior is Gaussian,  $p(\theta|\mathcal{D}) = \mathcal{N}(0, 1)$ , and  $\alpha = 0.05$ , then we have  $\ell = \Phi(\alpha/2) = -1.96$ , and  $u = \Phi(1 - \alpha/2) = 1.96$ , where  $\Phi$  denotes the cdf of the Gaussian. This is illustrated in Figure 2.3(c). This justifies the common practice of quoting a credible interval in the form of  $\mu \pm 2\sigma$ , where  $\mu$  represents the posterior mean,  $\sigma$  represents the posterior standard deviation, and 2 is a good approximation to 1.96.

Of course, the posterior is not always Gaussian. For example, in our coin example, if we use a uniform prior and we observe  $N_1 = 47$  heads out of  $N = 100$  trials, then the posterior is a beta distribution,  $p(\theta|\mathcal{D}) = \text{Beta}(48, 54)$ . We find the 95% posterior credible interval is  $(0.3749, 0.5673)$  (see betaCredibleInt for the one line of Matlab code we used to compute this).

If we don't know the functional form, but we can draw samples from the posterior, then we can use a Monte Carlo approximation to the posterior quantiles: we simply sort the  $S$  samples, and find the one that occurs at location  $\alpha/S$  along the sorted list. As  $S \to \infty$ , this converges to the true quantile. See mcQuantileDemo for a demo.

People often confuse Bayesian credible intervals with frequentist confidence intervals. However, they are not the same thing, as we discuss in Section 6.6.1. In general, credible intervals are usually what people want to compute, but confidence intervals are usually what they actually compute, because most people are taught frequentist statistics but not Bayesian statistics. Fortunately, the mechanics of computing a credible interval is just as easy as computing a confidence interval (see e.g., betaCredibleInt for how to do it in Matlab).

#### **5.2.2.1 Highest posterior density regions \***

A problem with central intervals is that there might be points outside the CI which have higher probability density. This is illustrated in Figure 5.3(a), where we see that points outside the left-most CI boundary have higher density than those just inside the right-most CI boundary.

This motivates an alternative quantity known as the **highest posterior density** or **HPD** region. This is defined as the (set of) most probable points that in total constitute  $100(1 - \alpha)$ % of the

Image /page/5/Figure/1 description: The image displays two plots, labeled (a) and (b). Both plots show a red curve representing a probability distribution, with two peaks. In plot (a), the shaded blue area under the curve indicates a region defined by "α/2" on both sides, suggesting a two-tailed probability. In plot (b), a horizontal blue line labeled "pMIN" intersects the distribution, and the shaded blue areas represent the portions of the distribution above this line, also suggesting a two-tailed region.

**Figure 5.4** (a) Central interval and (b) HPD region for a hypothetical multimodal posterior. Based on Figure 2.2 of (Gelman et al. 2004). Figure generated by postDensityIntervals.

probability mass. More formally, we find the threshold  $p^*$  on the pdf such that

$$
1 - \alpha = \int_{\theta: p(\theta|\mathcal{D}) > p^*} p(\theta|\mathcal{D}) d\theta
$$
\n(5.9)

and then define the HPD as

$$
C_{\alpha}(\mathcal{D}) = \{ \theta : p(\theta|\mathcal{D}) \ge p^* \}
$$
\n
$$
(5.10)
$$

In 1d, the HPD region is sometimes called a **highest density interval** or **HDI**. For example, Figure 5.3(b) shows the 95% HDI of a  $Beta(3, 9)$  distribution, which is  $(0.04, 0.48)$ . We see that this is narrower than the CI, even though it still contains 95% of the mass; furthermore, every point inside of it has higher density than every point outside of it.

For a unimodal distribution, the HDI will be the narrowest interval around the mode containing 95% of the mass. To see this, imagine "water filling" in reverse, where we lower the level until 95% of the mass is revealed, and only 5% is submerged. This gives a simple algorithm for computing HDIs in the 1d case: simply search over points such that the interval contains 95% of the mass and has minimal width. This can be done by 1d numerical optimization if we know the inverse CDF of the distribution, or by search over the sorted data points if we have a bag of samples (see betaHPD for a demo).

If the posterior is multimodal, the HDI may not even be a connected region: see Figure 5.4(b) for an example. However, summarizing multimodal posteriors is always difficult.

### **5.2.3 Inference for a difference in proportions**

Sometimes we have multiple parameters, and we are interested in computing the posterior distribution of some function of these parameters. For example, suppose you are about to buy something from Amazon.com, and there are two sellers offering it for the same price. Seller 1 has 90 positive reviews and 10 negative reviews. Seller 2 has 2 positive reviews and 0 negative reviews. Who should you buy from?<sup>1</sup>

<sup>1.</sup> This example is from www.johndcook.com/blog/2011/09/27/bayesian-amazon. See also lingpipe-blog.c om/2009/10/13/bayesian-counterpart-to-fisher-exact-test-on-contingency-tables.

Image /page/6/Figure/1 description: The image displays two plots, labeled (a) and (b). Plot (a) shows two probability density functions (PDFs) plotted against a horizontal axis ranging from 0 to 1. The red solid line, labeled "p(θ1|data)", represents a sharply peaked distribution centered around 0.9. The green dashed line, labeled "p(θ2|data)", represents a broader, more spread-out distribution that starts near 0 and increases gradually towards the right. The vertical axis of plot (a) ranges from 0 to 14. Plot (b) shows a single PDF plotted against a horizontal axis labeled "δ", ranging from -0.4 to 1. The vertical axis of plot (b) is labeled "pdf" and ranges from 0 to 2.5. This plot features a skewed distribution with a peak around -0.1. Two vertical blue lines are present in plot (b), one at approximately -0.15 and another at approximately 0.6.

**Figure 5.5** (a) Exact posteriors  $p(\theta_i|\mathcal{D}_i)$ . (b) Monte Carlo approximation to  $p(\delta|\mathcal{D})$ . We use kernel density estimation to get a smooth plot. The vertical lines enclose the 95% central interval. Figure generated by amazonSellerDemo,

On the face of it, you should pick seller 2, but we cannot be very confident that seller 2 is better since it has had so few reviews. In this section, we sketch a Bayesian analysis of this problem. Similar methodology can be used to compare rates or proportions across groups for a variety of other settings.

Let  $\theta_1$  and  $\theta_2$  be the unknown reliabilities of the two sellers. Since we don't know much about them, we'll endow them both with uniform priors,  $\theta_i \sim \text{Beta}(1, 1)$ . The posteriors are  $p(\theta_1|\mathcal{D}_1) = \text{Beta}(91, 11)$  and  $p(\theta_2|\mathcal{D}_2) = \text{Beta}(3, 1)$ .

We want to compute  $p(\theta_1 > \theta_2 | \mathcal{D})$ . For convenience, let us define  $\delta = \theta_1 - \theta_2$  as the difference in the rates. (Alternatively we might want to work in terms of the log-odds ratio.) We can compute the desired quantity using numerical integration:

$$
p(\delta > 0|\mathcal{D}) = \int_0^1 \int_0^1 \mathbb{I}(\theta_1 > \theta_2) \text{Beta}(\theta_1 | y_1 + 1, N_1 - y_1 + 1) \text{Beta}(\theta_2 | y_2 + 1, N_2 - y_2 + 1) d\theta_1 d\theta_2 \quad (5.11)
$$

We find  $p(\delta > 0|\mathcal{D})=0.710$ , which means you are better off buying from seller 1! See amazonSellerDemo for the code. (It is also possible to solve the integral analytically (Cook 2005).)

A simpler way to solve the problem is to approximate the posterior  $p(\delta|\mathcal{D})$  by Monte Carlo sampling. This is easy, since  $\theta_1$  and  $\theta_2$  are independent in the posterior, and both have beta distributions, which can be sampled from using standard methods. The distributions  $p(\theta_i|\mathcal{D}_i)$ are shown in Figure 5.5(a), and a MC approximation to  $p(\delta|\mathcal{D})$ , together with a 95% HPD, is shown Figure 5.5(b). An MC approximation to  $p(\delta > 0|\mathcal{D})$  is obtained by counting the fraction of samples where  $\theta_1 > \theta_2$ ; this turns out to be 0.718, which is very close to the exact value. (See amazonSellerDemo for the code.)

## **5.3 Bayesian model selection**

In Figure 1.18, we saw that using too high a degree polynomial results in overfitting, and using too low a degree results in underfitting. Similarly, in Figure 7.8(a), we saw that using too small a regularization parameter results in overfitting, and too large a value results in underfitting. In general, when faced with a set of models (i.e., families of parametric distributions) of different complexity, how should we choose the best one? This is called the **model selection** problem.

One approach is to use cross-validation to estimate the generalization error of all the candiate models, and then to pick the model that seems the best. However, this requires fitting each model K times, where K is the number of CV folds. A more efficient approach is to compute the posterior over models,

$$
p(m|\mathcal{D}) = \frac{p(\mathcal{D}|m)p(m)}{\sum_{m \in \mathcal{M}} p(m, \mathcal{D})}
$$
\n(5.12)

From this, we can easily compute the MAP model,  $\hat{m} = \argmax p(m|\mathcal{D})$ . This is called **Bayesian model selection**.

If we use a uniform prior over models,  $p(m) \propto 1$ , this amounts to picking the model which maximizes

$$
p(\mathcal{D}|m) = \int p(\mathcal{D}|\boldsymbol{\theta})p(\boldsymbol{\theta}|m)d\boldsymbol{\theta}
$$
\n(5.13)

This quantity is called the **marginal likelihood**, the **integrated likelihood**, or the **evidence** for model  $m$ . The details on how to perform this integral will be discussed in Section 5.3.2. But first we give an intuitive interpretation of what this quantity means.

### **5.3.1 Bayesian Occam's razor**

One might think that using  $p(\mathcal{D}|m)$  to select models would always favor the model with the most parameters. This is true if we use  $p(\mathcal{D}|\theta_m)$  to select models, where  $\theta_m$  is the MLE or MAP estimate of the parameters for model  $m$ , because models with more parameters will fit the data better, and hence achieve higher likelihood. However, if we integrate out the parameters, rather than maximizing them, we are automatically protected from overfitting: models with more parameters do not necessarily have higher *marginal* likelihood. This is called the **Bayesian Occam's razor** effect (MacKay 1995b; Murray and Ghahramani 2005), named after the principle known as **Occam's razor**, which says one should pick the simplest model that adequately explains the data.

One way to understand the Bayesian Occam's razor is to notice that the marginal likelihood can be rewritten as follows, based on the chain rule of probability (Equation 2.5):

$$
p(\mathcal{D}) = p(y_1)p(y_2|y_1)p(y_3|y_{1:2})\dots p(y_N|y_{1:N-1})
$$
\n(5.14)

where we have dropped the conditioning on **x** for brevity. This is similar to a leave-one-out cross-validation estimate (Section 1.4.8) of the likelihood, since we predict each future point given all the previous ones. (Of course, the order of the data does not matter in the above expression.) If a model is too complex, it will overfit the "early" examples and will then predict the remaining ones poorly.

Another way to understand the Bayesian Occam's razor effect is to note that probabilities must sum to one. Hence  $\sum_{\mathcal{D}'} p(\mathcal{D}'|m) = 1$ , where the sum is over all possible data sets. Complex models which can predict many things, must spread their probability mass thinly and hence models, which can predict many things, must spread their probability mass thinly, and hence will not obtain as large a probability for any given data set as simpler models. This is sometimes

Image /page/8/Figure/1 description: A graph shows three curves labeled M1, M2, and M3 plotted against D on the x-axis and p(D) on the y-axis. M1 is a blue curve that starts high and drops sharply to zero. M2 is a red curve that starts at a medium height and drops sharply to zero. M3 is a green curve that starts at a low height and drops sharply to zero. A dotted red line indicates a point D0 on the x-axis, where M2 is at its peak height and intersects with M1. The curves represent probability distributions.

**Figure 5.6** A schematic illustration of the Bayesian Occam's razor. The broad (green) curve corresponds to a complex model, the narrow (blue) curve to a simple model, and the middle (red) curve is just right. Based on Figure 3.13 of (Bishop 2006a). See also (Murray and Ghahramani 2005, Figure 2) for a similar plot produced on real data.

called the **conservation of probability mass** principle, and is illustrated in Figure 5.6. On the horizontal axis we plot all possible data sets in order of increasing complexity (measured in some abstract sense). On the vertical axis we plot the predictions of 3 possible models: a simple one,  $M_1$ ; a medium one,  $M_2$ ; and a complex one,  $M_3$ . We also indicate the actually observed data  $\mathcal{D}_0$  by a vertical line. Model 1 is too simple and assigns low probability to  $\mathcal{D}_0$ . Model 3 also assigns  $\mathcal{D}_0$  relatively low probability, because it can predict many data sets, and hence it spreads its probability quite widely and thinly. Model 2 is "just right": it predicts the observed data with a reasonable degree of confidence, but does not predict too many other things. Hence model 2 is the most probable model.

As a concrete example of the Bayesian Occam's razor, consider the data in Figure 5.7. We plot polynomials of degrees 1, 2 and 3 fit to  $N = 5$  data points. It also shows the posterior over models, where we use a Gaussian prior (see Section 7.6 for details). There is not enough data to justify a complex model, so the MAP model is  $d = 1$ . Figure 5.8 shows what happens when  $N = 30$ . Now it is clear that  $d = 2$  is the right model (the data was in fact generated from a quadratic).

As another example, Figure 7.8(c) plots  $\log p(\mathcal{D}|\lambda)$  vs  $\log(\lambda)$ , for the polynomial ridge regression model, where  $\lambda$  ranges over the same set of values used in the CV experiment. We see that the maximum evidence occurs at roughly the same point as the minimum of the test MSE, which also corresponds to the point chosen by CV.

When using the Bayesian approach, we are not restricted to evaluating the evidence at a finite grid of values. Instead, we can use numerical optimization to find  $\lambda^* = \argmax_{\lambda} p(\mathcal{D}|\lambda)$ . This technique is called **empirical Bayes** or **type II maximum likelihood** (see Section 5.6 for details). An example is shown in Figure 7.8(b): we see that the curve has a similar shape to the CV estimate, but it can be computed more efficiently.

Image /page/9/Figure/1 description: This figure displays four plots labeled (a), (b), (c), and (d). Plots (a), (b), and (c) are scatter plots showing data points with fitted polynomial curves. Plot (a) shows a linear fit (dashed red line) and a quadratic fit (solid green line) with dotted blue lines representing confidence intervals, for data points with x-values ranging from approximately -2 to 12 and y-values from -20 to 70. The title indicates d=1, logev=-18.593, EB. Plot (b) shows a quadratic fit for data points with x-values from -2 to 12 and y-values from -80 to 80. The title indicates d=2, logev=-20.218, EB. Plot (c) shows a cubic fit for data points with x-values from -2 to 12 and y-values from -200 to 300. The title indicates d=3, logev=-21.718, EB. Plot (d) is a bar chart titled "N=5, method=EB". The x-axis is labeled "M" and ranges from 1 to 3. The y-axis is labeled "P(M|D)" and ranges from 0 to 1. The bar chart shows probabilities for M=1, M=2, and M=3, with the bar for M=1 reaching approximately 0.8, the bar for M=2 reaching approximately 0.15, and the bar for M=3 reaching approximately 0.05. Below the plots, there is text stating "Figure 5.7. (a-c) We plot polynomials of degrees 1, 2 and 3 fit to N = 5 data points using empirical".

**Figure 5.7** (a-c) We plot polynomials of degrees 1, 2 and 3 fit to  $N = 5$  data points using empirical Bayes. The solid green curve is the true function, the dashed red curve is the prediction (dotted blue lines represent  $\pm\sigma$  around the mean). (d) We plot the posterior over models,  $p(d|\mathcal{D})$ , assuming a uniform prior  $p(d) \propto 1$ . Based on a figure by Zoubin Ghahramani. Figure generated by linregEbModelSelVsN.

## **5.3.2 Computing the marginal likelihood (evidence)**

When discussing parameter inference for a fixed model, we often wrote

$$
p(\boldsymbol{\theta}|\mathcal{D}, m) \propto p(\boldsymbol{\theta}|m)p(\mathcal{D}|\boldsymbol{\theta}, m)
$$
\n(5.15)

thus ignoring the normalization constant  $p(\mathcal{D}|m)$ . This is valid since  $p(\mathcal{D}|m)$  is constant wrt  $\theta$ . However, when comparing models, we need to know how to compute the marginal likelihood,  $p(\mathcal{D}|m)$ . In general, this can be quite hard, since we have to integrate over all possible parameter values, but when we have a conjugate prior, it is easy to compute, as we now show.

Let  $p(\theta) = q(\theta)/Z_0$  be our prior, where  $q(\theta)$  is an unnormalized distribution, and  $Z_0$  is the normalization constant of the prior. Let  $p(\mathcal{D}|\theta) = q(\mathcal{D}|\theta)/Z_{\ell}$  be the likelihood, where  $Z_{\ell}$ contains any constant factors in the likelihood. Finally let  $p(\theta|\mathcal{D}) = q(\theta|\mathcal{D})/Z_N$  be our poste-

Image /page/10/Figure/1 description: This figure contains four subplots labeled (a), (b), (c), and (d). Subplots (a), (b), and (c) are scatter plots with fitted curves. Each subplot shows data points (black circles) and three fitted curves: a solid green curve, a dashed red curve, and a dotted blue curve, along with dashed black lines representing confidence intervals. Subplot (a) is titled 'd=1, logev=-106.110, EB' and shows data points scattered around a parabolic trend. Subplot (b) is titled 'd=2, logev=-103.025, EB' and also displays a parabolic trend with data points. Subplot (c) is titled 'd=3, logev=-107.410, EB' and presents a similar scatter plot with fitted curves. The x-axis for all three scatter plots ranges from -2 to 12, and the y-axis ranges from -20 to 80 (for (a) and (b)) and -20 to 100 (for (c)). Subplot (d) is a bar chart titled 'N=30, method=EB'. The x-axis is labeled 'M' with values 1, 2, and 3. The y-axis is labeled 'P(M|D)' and ranges from 0 to 1. The bar chart shows three bars: a very short bar at M=1, a tall bar reaching approximately 0.95 at M=2, and a very short bar at M=3. The figure caption below indicates that it is Figure 5.8, same as Figure 5.7 except N=30, and generated by linregEbModelSolvN.

**Figure 5.8** Same as Figure 5.7 except now  $N = 30$ . Figure generated by  $linregEDModelSe1VsM$ .

rior, where  $q(\theta|\mathcal{D}) = q(\mathcal{D}|\theta)q(\theta)$  is the unnormalized posterior, and  $Z_N$  is the normalization constant of the posterior. We have

$$
p(\theta|\mathcal{D}) = \frac{p(\mathcal{D}|\theta)p(\theta)}{p(\mathcal{D})}
$$
\n(5.16)

$$
\frac{q(\boldsymbol{\theta}|\mathcal{D})}{Z_N} = \frac{q(\mathcal{D}|\boldsymbol{\theta})q(\boldsymbol{\theta})}{Z_{\ell}Z_0p(\mathcal{D})}
$$
\n(5.17)

$$
p(\mathcal{D}) = \frac{Z_N}{Z_0 Z_\ell} \tag{5.18}
$$

So assuming the relevant normalization constants are tractable, we have an easy way to compute the marginal likelihood. We give some examples below.

### **5.3.2.1 Beta-binomial model**

Let us apply the above result to the Beta-binomial model. Since we know  $p(\theta|\mathcal{D}) = \text{Beta}(\theta|a', b')$ ,<br>where  $a' = a + N_1$  and  $b' = b + N_2$  we know the pormalization constant of the posterior is where  $a' = a + N_1$  and  $b' = b + N_0$ , we know the normalization constant of the posterior is  $B(a', b')$ . Hence  $B(a', b')$ . Hence

$$
p(\theta|\mathcal{D}) = \frac{p(\mathcal{D}|\theta)p(\theta)}{p(\mathcal{D})}
$$
\n(5.19)

$$
= \frac{1}{p(\mathcal{D})} \left[ \frac{1}{B(a,b)} \theta^{a-1} (1-\theta)^{b-1} \right] \left[ \binom{N}{N_1} \theta^{N_1} (1-\theta)^{N_0} \right]
$$
(5.20)

$$
= \left(\begin{array}{c} N \\ N_1 \end{array}\right) \frac{1}{p(\mathcal{D})} \frac{1}{B(a,b)} \left[\theta^{a+N_1-1} (1-\theta)^{b+N_0-1}\right]
$$
\n(5.21)

So

$$
\frac{1}{B(a+N_1, b+N_0)} = {N \choose N_1} \frac{1}{p(\mathcal{D})} \frac{1}{B(a, b)}
$$
\n(5.22)

$$
p(\mathcal{D}) = {N \choose N_1} \frac{B(a+N_1, b+N_0)}{B(a, b)}
$$
\n
$$
(5.23)
$$

The marginal likelihood for the Beta-Bernoulli model is the same as above, except it is missing the  $\binom{N}{N_1}$  term.

### **5.3.2.2 Dirichlet-multinoulli model**

By the same reasoning as the Beta-Bernoulli case, one can show that the marginal likelihood for the Dirichlet-multinoulli model is given by

$$
p(\mathcal{D}) = \frac{B(\mathbf{N} + \alpha)}{B(\alpha)}
$$
\n(5.24)

where

$$
B(\alpha) = \frac{\prod_{k=1}^{K} \Gamma(\alpha_k)}{\Gamma(\sum_{k} \alpha_k)}
$$
\n(5.25)

Hence we can rewrite the above result in the following form, which is what is usually presented in the literature:

$$
p(\mathcal{D}) = \frac{\Gamma(\sum_{k} \alpha_{k})}{\Gamma(N + \sum_{k} \alpha_{k})} \prod_{k} \frac{\Gamma(N_{k} + \alpha_{k})}{\Gamma(\alpha_{k})}
$$
(5.26)

We will see many applications of this equation later.

### **5.3.2.3 Gaussian-Gaussian-Wishart model**

Consider the case of an MVN with a conjugate NIW prior. Let  $Z_0$  be the normalizer for the prior,  $Z_N$  be normalizer for the posterior, and let  $Z_l = (2\pi)^{ND/2}$  be the normalizer for the likelihood. Then it is easy to see that

$$
p(\mathcal{D}) = \frac{Z_N}{Z_0 Z_l} \tag{5.27}
$$

$$
= \frac{1}{\pi^{ND/2}} \frac{1}{2^{ND/2}} \frac{\left(\frac{2\pi}{\kappa_N}\right)^{D/2} |\mathbf{S}_N|^{-\nu_N/2} 2^{(\nu_0+N)D/2} \Gamma_D(\nu_N/2)}{\left(\frac{2\pi}{\kappa_0}\right)^{D/2} |\mathbf{S}_0|^{-\nu_0/2} 2^{\nu_0 D/2} \Gamma_D(\nu_0/2)}
$$
(5.28)

$$
= \frac{1}{\pi^{ND/2}} \left(\frac{\kappa_0}{\kappa_N}\right)^{D/2} \frac{|\mathbf{S}_0|^{\nu_0/2}}{|\mathbf{S}_N|^{\nu_N/2}} \frac{\Gamma_D(\nu_N/2)}{\Gamma_D(\nu_0/2)} \tag{5.29}
$$

This equation will prove useful later.

### **5.3.2.4 BIC approximation to log marginal likelihood**

In general, computing the integral in Equation 5.13 can be quite difficult. One simple but popular approximation is known as the **Bayesian information criterion** or **BIC**, which has the following form (Schwarz 1978):

$$
\text{BIC} \triangleq \log p(\mathcal{D}|\hat{\boldsymbol{\theta}}) - \frac{\text{dof}(\hat{\boldsymbol{\theta}})}{2} \log N \approx \log p(\mathcal{D})
$$
\n(5.30)

where  $dof(\hat{\theta})$  is the number of **degrees of freedom** in the model, and  $\hat{\theta}$  is the MLE for the model.<sup>2</sup> We see that this has the form of a **penalized log likelihood**, where the penalty term depends on the model's complexity. See Section 8.4.2 for the derivation of the BIC score.

As an example, consider linear regression. As we show in Section 7.3, the MLE is given by  $\hat{\mathbf{w}} =$  $(\mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T \mathbf{y}$  and  $\hat{\sigma}^2 = \text{RSS}/N$ , where RSS =  $\sum_{i=1}^N (y_i - \hat{\mathbf{w}}_{mle}^T \mathbf{x}_i)^2$ . The corresponding log likelihood is given by

$$
\log p(\mathcal{D}|\hat{\boldsymbol{\theta}}) = -\frac{N}{2}\log(2\pi\hat{\sigma}^2) - \frac{N}{2}
$$
\n(5.31)

Hence the BIC score is as follows (dropping constant terms)

$$
BIC = -\frac{N}{2}\log(\hat{\sigma}^2) - \frac{D}{2}\log(N)
$$
\n
$$
(5.32)
$$

where  $D$  is the number of variables in the model. In the statistics literature, it is common to use an alternative definition of BIC, which we call the BIC *cost* (since we want to minimize it):

$$
\text{BIC-cost} \triangleq -2\log p(\mathcal{D}|\hat{\boldsymbol{\theta}}) + \text{dof}(\hat{\boldsymbol{\theta}})\log N \approx -2\log p(\mathcal{D})\tag{5.33}
$$

In the context of linear regression, this becomes

$$
BIC\text{-}cost = N \log(\hat{\sigma}^2) + D \log(N) \tag{5.34}
$$

<sup>2.</sup> Traditionally the BIC score is defined using the ML estimate  $\hat{\theta}$ , so it is independent of the prior. However, for models such as mixtures of Gaussians, the ML estimate can be poorly behaved, so it is better to evaluate the BIC score using the MAP estimate, as in (Fraley and Raftery 2007).

The BIC method is very closely related to the **minimum description length** or **MDL** principle, which characterizes the score for a model in terms of how well it fits the data, minus how complex the model is to define. See (Hansen and Yu 2001) for details.

There is a very similar expression to BIC/ MDL called the **Akaike information criterion** or **AIC**, defined as

$$
AIC(m, \mathcal{D}) \triangleq \log p(\mathcal{D}|\hat{\boldsymbol{\theta}}_{MLE}) - \text{dof}(m)
$$
\n(5.35)

This is derived from a frequentist framework, and cannot be interpreted as an approximation to the marginal likelihood. Nevertheless, the form of this expression is very similar to BIC. We see that the penalty for AIC is less than for BIC. This causes AIC to pick more complex models. However, this can result in better predictive accuracy. See e.g., (Clarke et al. 2009, sec 10.2) for further discussion on such information criteria.

### ********* Effect of the prior**

Sometimes it is not clear how to set the prior. When we are performing posterior inference, the details of the prior may not matter too much, since the likelihood often overwhelms the prior anyway. But when computing the marginal likelihood, the prior plays a much more important role, since we are averaging the likelihood over all possible parameter settings, as weighted by the prior.

In Figures 5.7 and 5.8, where we demonstrated model selection for linear regression, we used a prior of the form  $p(\mathbf{w}) = \mathcal{N}(\mathbf{0}, \alpha^{-1}\mathbf{I})$ . Here  $\alpha$  is a tuning parameter that controls how strong the prior is. This parameter can have a large effect, as we discuss in Section 7.5. Intuitively, if  $\alpha$  is large, the weights are "forced" to be small, so we need to use a complex model with many small parameters (e.g., a high degree polynomial) to fit the data. Conversely, if  $\alpha$  is small, we will favor simpler models, since each parameter is "allowed" to vary in magnitude by a lot.

If the prior is unknown, the correct Bayesian procedure is to put a prior on the prior. That is, we should put a prior on the hyper-parameter  $\alpha$  as well as the parametrs **w**. To compute the marginal likelihood, we should integrate out all unknowns, i.e., we should compute

$$
p(\mathcal{D}|m) = \int \int p(\mathcal{D}|\mathbf{w})p(\mathbf{w}|\alpha, m)p(\alpha|m)d\mathbf{w}d\alpha
$$
\n(5.36)

Of course, this requires specifying the hyper-prior. Fortunately, the higher up we go in the Bayesian hierarchy, the less sensitive are the results to the prior settings. So we can usually make the hyper-prior uninformative.

A computational shortcut is to optimize  $\alpha$  rather than integrating it out. That is, we use

$$
p(\mathcal{D}|m) \approx \int p(\mathcal{D}|\mathbf{w})p(\mathbf{w}|\hat{\alpha}, m)d\mathbf{w}
$$
\n(5.37)

where

$$
\hat{\alpha} = \underset{\alpha}{\operatorname{argmax}} p(\mathcal{D}|\alpha, m) = \underset{\alpha}{\operatorname{argmax}} \int p(\mathcal{D}|\mathbf{w}) p(\mathbf{w}|\alpha, m) d\mathbf{w}
$$
\n(5.38)

This approach is called empirical Bayes (EB), and is discussed in more detail in Section 5.6. This is the method used in Figures 5.7 and 5.8.

| Bayes factor $BF(1,0)$          | Interpretation              |
|---------------------------------|-----------------------------|
| $BF < rac{1}{100}$             | Decisive evidence for $M_0$ |
| $BF < rac{1}{10}$              | Strong evidence for $M_0$   |
| $rac{1}{10} < BF < rac{1}{3}$ | Moderate evidence for $M_0$ |
| $rac{1}{3} < BF < 1$           | Weak evidence for $M_0$     |
| $1 < BF < 3$                    | Weak evidence for $M_1$     |
| $3 < BF < 10$                   | Moderate evidence for $M_1$ |
| $BF > 10$                       | Strong evidence for $M_1$   |
| $BF > 100$                      | Decisive evidence for $M_1$ |

**Table 5.1** Jeffreys' scale of evidence for interpreting Bayes factors.

#### **5.3.3 Bayes factors**

Suppose our prior on models is uniform,  $p(m) \propto 1$ . Then model selection is equivalent to picking the model with the highest marginal likelihood. Now suppose we just have two models we are considering, call them the **null hypothesis**,  $M_0$ , and the **alternative hypothesis**,  $M_1$ . Define the **Bayes factor** as the ratio of marginal likelihoods:

$$
BF_{1,0} \triangleq \frac{p(\mathcal{D}|M_1)}{p(\mathcal{D}|M_0)} = \frac{p(M_1|\mathcal{D})}{p(M_0|\mathcal{D})} / \frac{p(M_1)}{p(M_0)}
$$
(5.39)

(This is like a **likelihood ratio**, except we integrate out the parameters, which allows us to compare models of different complexity.) If  $BF_{1,0} > 1$  then we prefer model 1, otherwise we prefer model 0.

Of course, it might be that  $BF_{1,0}$  is only slightly greater than 1. In that case, we are not very confident that model 1 is better. Jeffreys (1961) proposed a scale of evidence for interpreting the magnitude of a Bayes factor, which is shown in Table 5.1. This is a Bayesian alternative to the frequentist concept of a p-value.<sup>3</sup> Alternatively, we can just convert the Bayes factor to a posterior over models. If  $p(M_1) = p(M_0) = 0.5$ , we have

$$
p(M_0|\mathcal{D}) = \frac{BF_{0,1}}{1 + BF_{0,1}} = \frac{1}{BF_{1,0} + 1}
$$
\n(5.40)

### **5.3.3.1 Example: Testing if a coin is fair**

Suppose we observe some coin tosses, and want to decide if the data was generated by a fair coin,  $\theta = 0.5$ , or a potentially biased coin, where  $\theta$  could be any value in [0, 1]. Let us denote the first model by  $M_0$  and the second model by  $M_1$ . The marginal likelihood under  $M_0$  is simply

$$
p(\mathcal{D}|M_0) = \left(\frac{1}{2}\right)^N \tag{5.41}
$$

<sup>3.</sup> A **p-value**, is defined as the probability (under the null hypothesis) of observing some **test statistic**  $f(\mathcal{D})$  (such as the **chi-squared statistic**) that is as large *or larger* than that actually observed, i.e., pvalue( $\mathcal{D}$ )  $\triangleq P(f(\mathcal{D}) \geq f(\mathcal{D}) | \mathcal{D} \sim H_0$ ). Note that has almost pothing to do with what we really want to know which is  $H_0$ ). Note that has almost nothing to do with what we really want to know, which is  $p(H_0|\mathcal{D})$ .

Image /page/15/Figure/1 description: The image contains two line graphs, labeled (a) and (b). Graph (a) is titled "log10 p(D|M1)" and shows values on the y-axis ranging from -0.4 to -1.8. The x-axis is labeled with numbers from 0 to 5, with multiple instances of 1, 2, 3, and 4. The line starts at approximately -0.75, drops sharply to -1.45, stays at -1.45 for several points, then drops to -1.75, and finally rises sharply to approximately -0.75 at the end. Graph (b) is titled "BIC approximation to log10 p(D|M1)" and shows values on the y-axis ranging from -2 to -2.5. The x-axis is labeled identically to graph (a). The line starts at approximately -2.05, drops sharply to -2.45, stays at -2.45 for several points, and then rises sharply to approximately -2.05 at the end. Both graphs use blue lines and circular markers to indicate data points.

**Figure 5.9** (a) Log marginal likelihood for the coins example. (b) BIC approximation. Figure generated by coinsModelSelDemo.

where N is the number of coin tosses. The marginal likelihood under  $M_1$ , using a Beta prior, is

$$
p(\mathcal{D}|M_1) = \int p(\mathcal{D}|\theta)p(\theta)d\theta = \frac{B(\alpha_1 + N_1, \alpha_0 + N_0)}{B(\alpha_1, \alpha_0)}
$$
(5.42)

We plot  $\log p(\mathcal{D}|M_1)$  vs the number of heads  $N_1$  in Figure 5.9(a), assuming  $N = 5$  and  $\alpha_1 = \alpha_0 = 1$ . (The shape of the curve is not very sensitive to  $\alpha_1$  and  $\alpha_0$ , as long as  $\alpha_0 = \alpha_1$ .) If we observe 2 or 3 heads, the unbiased coin hypothesis  $M_0$  is more likely than  $M_1$ , since  $M_0$ is a simpler model (it has no free parameters) — it would be a suspicious coincidence if the coin were biased but happened to produce almost exactly 50/50 heads/tails. However, as the counts become more extreme, we favor the biased coin hypothesis. Note that, if we plot the log Bayes factor,  $\log BF_{1,0}$ , it will have exactly the same shape, since  $\log p(\mathcal{D}|M_0)$  is a constant. See also Exercise 3.18.

In Figure 5.9(b) shows the BIC approximation to  $\log p(\mathcal{D}|M_1)$  for our biased coin example from Section 5.3.3.1. We see that the curve has approximately the same shape as the exact log marginal likelihood, which is all that matters for model selection purposes, since the absolute scale is irrelevant. In particular, it favors the simpler model unless the data is overwhelmingly in support of the more complex model.

### **5.3.4 Jeffreys-Lindley paradox \***

Problems can arise when we use improper priors (i.e., priors that do not integrate to 1) for model selection/ hypothesis testing, even though such priors may be acceptable for other purposes. For example, consider testing the hypotheses  $M_0$ :  $\theta \in \Theta_0$  vs  $M_1$ :  $\theta \in \Theta_1$ . To define the marginal density on  $\theta$ , we use the following mixture model

$$
p(\theta) = p(\theta|M_0)p(M_0) + p(\theta|M_1)p(M_1)
$$
\n(5.43)

This is only meaningful if  $p(\theta|M_0)$  and  $p(\theta|M_1)$  are proper (normalized) density functions. In this case, the posterior is given by

$$
p(M_0|\mathcal{D}) = \frac{p(M_0)p(\mathcal{D}|M_0)}{p(M_0)p(\mathcal{D}|M_0) + p(M_1)p(\mathcal{D}|M_1)}
$$
\n(5.44)

$$
= \frac{p(M_0) \int_{\Theta_0} p(\mathcal{D}|\theta) p(\theta|M_0) d\theta}{p(M_0) \int_{\Theta_0} p(\mathcal{D}|\theta) p(\theta|M_0) d\theta + p(M_1) \int_{\Theta_1} p(\mathcal{D}|\theta) p(\theta|M_1) d\theta} \tag{5.45}
$$

Now suppose we use improper priors,  $p(\theta|M_0) \propto c_0$  and  $p(\theta|M_1) \propto c_1$ . Then

$$
p(M_0|\mathcal{D}) = \frac{p(M_0)c_0 \int_{\Theta_0} p(\mathcal{D}|\theta) d\theta}{p(M_0)c_0 \int_{\Theta_0} p(\mathcal{D}|\theta) d\theta + p(M_1)c_1 \int_{\Theta_1} p(\mathcal{D}|\theta) d\theta}
$$
(5.46)

$$
= \frac{p(M_0)c_0\ell_0}{p(M_0)c_0\ell_0 + p(M_1)c_1\ell_1} \tag{5.47}
$$

where  $\ell_i = \int_{\Theta_i} p(\mathcal{D}|\theta) d\theta$  is the integrated or marginal likelihood for model *i*. Now let  $p(M_0) =$  $p(M_1) = \frac{1}{2}$ . Hence

$$
p(M_0|\mathcal{D}) = \frac{c_0 \ell_0}{c_0 \ell_0 + c_1 \ell_1} = \frac{\ell_0}{\ell_0 + (c_1/c_0)\ell_1}
$$
\n(5.48)

Thus we can change the posterior arbitrarily by choosing  $c_1$  and  $c_0$  as we please. Note that using proper, but very vague, priors can cause similar problems. In particular, the Bayes factor will always favor the simpler model, since the probability of the observed data under a complex model with a very diffuse prior will be very small. This is called the **Jeffreys-Lindley paradox**.

Thus it is important to use proper priors when performing model selection. Note, however, that, if  $M_0$  and  $M_1$  share the same prior over a subset of the parameters, this part of the prior can be improper, since the corresponding normalization constant will cancel out.

## **5.4 Priors**

The most controversial aspect of Bayesian statistics is its reliance on priors. Bayesians argue this is unavoidable, since nobody is a **tabula rasa** or **blank slate**: all inference must be done conditional on certain assumptions about the world. Nevertheless, one might be interested in minimizing the impact of one's prior assumptions. We briefly discuss some ways to do this below.

### **5.4.1 Uninformative priors**

If we don't have strong beliefs about what  $\theta$  should be, it is common to use an **uninformative** or **non-informative** prior, and to "let the data speak for itself".

The issue of designing uninformative priors is actually somewhat tricky. As an example of the difficulty, consider a Bernoulli parameter,  $\theta \in [0, 1]$ . One might think that the most uninformative prior would be the uniform distribution,  $Beta(1, 1)$ . But the posterior mean in this case is  $\mathbb{E}[\theta|\mathcal{D}] = \frac{N_1+1}{N_1+N_0+2}$ , whereas the MLE is  $\frac{N_1}{N_1+N_0}$ . Hence one could argue that the prior wasn't completely uninformative after all.

Clearly by decreasing the magnitude of the pseudo counts, we can lessen the impact of the prior. By the above argument, the most non-informative prior is

$$
\lim_{c \to 0} \text{Beta}(c, c) = \text{Beta}(0, 0) \tag{5.49}
$$

which is a mixture of two equal point masses at 0 and 1 (see (Zhu and Lu 2004)). This is also called the **Haldane prior**. Note that the Haldane prior is an improper prior, meaning it does not integrate to 1. However, as long as we see at least one head and at least one tail, the posterior will be proper.

In Section 5.4.2.1 we will argue that the "right" uninformative prior is in fact  $\text{Beta}(\frac{1}{2}, \frac{1}{2})$ . Clearly the difference in practice between these three priors is very likely negligible. In general, it is advisable to perform some kind of **sensitivity analysis**, in which one checks how much one's conclusions or predictions change in response to change in the modeling assumptions, which includes the choice of prior, but also the choice of likelihood and any kind of data preprocessing. If the conclusions are relatively insensitive to the modeling assumptions, one can have more confidence in the results.

### **5.4.2 Jeffreys priors \***

Harold Jeffreys<sup>4</sup> designed a general purpose technique for creating non-informative priors. The result is known as the **Jeffreys prior**. The key observation is that if  $p(\phi)$  is non-informative, then any re-parameterization of the prior, such as  $\theta = h(\phi)$  for some function h, should also be non-informative. Now, by the change of variables formula,

$$
p_{\theta}(\theta) = p_{\phi}(\phi) \left| \frac{d\phi}{d\theta} \right| \tag{5.50}
$$

so the prior will in general change. However, let us pick

$$
p_{\phi}(\phi) \propto (I(\phi))^{\frac{1}{2}}
$$
\n(5.5)

where  $I(\phi)$  is the **Fisher information**:

$$
I(\phi) \triangleq -\mathbb{E}\left[\left(\frac{d\log p(X|\phi)}{d\phi}\right)2\right]
$$
\n(5.52)

This is a measure of curvature of the expected negative log likelihood and hence a measure of stability of the MLE (see Section 6.2.2). Now

$$
\frac{d \log p(x|\theta)}{d\theta} = \frac{d \log p(x|\phi)}{d\phi} \frac{d\phi}{d\theta} \tag{5.53}
$$

Squaring and taking expectations over  $x$ , we have

$$
I(\theta) = -\mathbb{E}\left[\left(\frac{d\log p(X|\theta)}{d\theta}\right)^2\right] = I(\phi)\left(\frac{d\phi}{d\theta}\right)^2 \tag{5.54}
$$

$$
I(\theta)^{\frac{1}{2}} = I(\phi)^{\frac{1}{2}} \Big| \frac{d\phi}{d\theta} \Big| \tag{5.55}
$$

<sup>4.</sup> Harold Jeffreys, 1891 – 1989, was an English mathematician, statistician, geophysicist, and astronomer.

so we find the transformed prior is

$$
p_{\theta}(\theta) = p_{\phi}(\phi) \left| \frac{d\phi}{d\theta} \right| \propto (I(\phi))^{\frac{1}{2}} \left| \frac{d\phi}{d\theta} \right| = I(\theta)^{\frac{1}{2}} \tag{5.56}
$$

So  $p_{\theta}(\theta)$  and  $p_{\phi}(\phi)$  are the same.

Some examples will make this clearer.

#### **5.4.2.1 Example: Jeffreys prior for the Bernoulli and multinoulli**

Suppose  $X \sim \text{Ber}(\theta)$ . The log likelihood for a single sample is

$$
\log p(X|\theta) = X \log \theta + (1 - X) \log(1 - \theta) \tag{5.57}
$$

The **score function** is just the gradient of the log-likelihood:

$$
s(\theta) \triangleq \frac{d}{d\theta} \log p(X|\theta) = \frac{X}{\theta} - \frac{1 - X}{1 - \theta} \tag{5.58}
$$

The **observed information** is the second derivative of the log-likelihood:

$$
J(\theta) = -\frac{d^2}{d\theta^2} \log p(X|\theta) = -s'(\theta|X) = \frac{X}{\theta^2} + \frac{1-X}{(1-\theta)^2}
$$
(5.59)

The Fisher information is the expected information:

$$
I(\theta) = E[J(\theta|X)|X \sim \theta] = \frac{\theta}{\theta^2} + \frac{1-\theta}{(1-\theta)^2} = \frac{1}{\theta(1-\theta)}
$$
(5.60)

Hence Jeffreys' prior is

$$
p(\theta) \propto \theta^{-\frac{1}{2}} (1 - \theta)^{-\frac{1}{2}} = \frac{1}{\sqrt{\theta(1 - \theta)}} \propto \text{Beta}(\frac{1}{2}, \frac{1}{2})
$$
\n(5.61)

Now consider a multinoulli random variable with  $K$  states. One can show that the Jeffreys' prior is given by

$$
p(\boldsymbol{\theta}) \propto \text{Dir}(\frac{1}{2}, \dots, \frac{1}{2})
$$
\n<sup>(5.62)</sup>

Note that this is different from the more obvious choices of  $\text{Dir}(\frac{1}{K}, \ldots, \frac{1}{K})$  or  $\text{Dir}(1, \ldots, 1)$ .

#### **5.4.2.2 Example: Jeffreys prior for location and scale parameters**

One can show that the Jeffreys prior for a location parameter, such as the Gaussian mean, is  $p(\mu) \propto 1$ . Thus is an example of a **translation invariant prior**, which satisfies the property that the probability mass assigned to any interval,  $[A, B]$  is the same as that assigned to any other shifted interval of the same width, such as  $[A - c, B - c]$ . That is,

$$
\int_{A-c}^{B-c} p(\mu)d\mu = (A-c) - (B-c) = (A-B) = \int_{A}^{B} p(\mu)d\mu
$$
\n(5.63)

This can be achieved using  $p(\mu) \propto 1$ , which we can approximate by using a Gaussian with infinite variance,  $p(\mu) = \mathcal{N}(\mu|0,\infty)$ . Note that this is an **improper prior**, since it does not integrate to 1. Using improper priors is fine as long as the posterior is proper, which will be the case provided we have seen  $N > 1$  data points, since we can "nail down" the location as soon as we have seen a single data point.

Similarly, one can show that the Jeffreys prior for a scale parameter, such as the Gaussian variance, is  $p(\sigma^2) \propto 1/\sigma^2$ . This is an example of a **scale invariant prior**, which satisfies the property that the probability mass assigned to any interval  $[A, B]$  is the same as that assigned to any other interval  $[A/c, B/c]$  which is scaled in size by some constant factor  $c > 0$ . (For example, if we change units from meters to feet we do not want that to affect our inferences.) This can be achieved by using

$$
p(s) \propto 1/s \tag{5.64}
$$

To see this, note that

 $\sim$ 

$$
\int_{A/c}^{B/c} p(s)ds = [\log s]_{A/c}^{B/c} = \log(B/c) - \log(A/c)
$$
\n(5.65)

$$
= \log(B) - \log(A) = \int_{A}^{B} p(s)ds
$$
\n(5.66)

We can approximate this using a degenerate Gamma distribution (Section 2.4.4),  $p(s) = Ga(s|0, 0)$ . The prior  $p(s) \propto 1/s$  is also improper, but the posterior is proper as soon as we have seen  $N \geq 2$  data points (since we need at least two data points to estimate a variance).

### **5.4.3 Robust priors**

In many cases, we are not very confident in our prior, so we want to make sure it does not have an undue influence on the result. This can be done by using **robust priors** (Insua and Ruggeri 2000), which typically have heavy tails, which avoids forcing things to be too close to the prior mean.

Let us consider an example from (Berger 1985, p7). Suppose  $x \sim \mathcal{N}(\theta, 1)$ . We observe that  $x = 5$  and we want to estimate  $\theta$ . The MLE is of course  $\hat{\theta} = 5$ , which seems reasonable. The posterior mean under a uniform prior is also  $\bar{\theta} = 5$ . But now suppose we know that the prior median is 0, and the prior quantiles are at -1 and 1, so  $p(\theta < -1) = p(-1 < \theta < 0) = p(0 <$  $\theta$  < 1) =  $p(1 < \theta)$  = 0.25. Let us also assume the prior is smooth and unimodal.

It is easy to show that a Gaussian prior of the form  $\mathcal{N}(\theta|0, 2.19^2)$  satisfies these prior constraints. But in this case the posterior mean is given by 3.43, which doesn't seem very satisfactory.

Now suppose we use as a Cauchy prior  $\mathcal{T}(\theta|0, 1, 1)$ . This also satisfies the prior constraints of our example. But this time we find (using numerical method integration: see robustPriorDemo for the code) that the posterior mean is about 4.6, which seems much more reasonable.

### **5.4.4 Mixtures of conjugate priors**

Robust priors are useful, but can be computationally expensive to use. Conjugate priors simplify the computation, but are often not robust, and not flexible enough to encode our prior knowledge. However, it turns out that a **mixture of conjugate priors** is also conjugate (Exercise 5.1), and can approximate any kind of prior (Dallal and Hall 1983; Diaconis and Ylvisaker 1985). Thus such priors provide a good compromise between computational convenience and flexibility.

For example, suppose we are modeling coin tosses, and we think the coin is either fair, or is biased towards heads. This cannot be represented by a beta distribution. However, we can model it using a mixture of two beta distributions. For example, we might use

$$
p(\theta) = 0.5 \text{ Beta}(\theta | 20, 20) + 0.5 \text{ Beta}(\theta | 30, 10)
$$
\n(5.67)

If  $\theta$  comes from the first distribution, the coin is fair, but if it comes from the second, it is biased towards heads.

We can represent a mixture by introducing a latent indicator variable z, where  $z = k$  means that  $\theta$  comes from mixture component k. The prior has the form

$$
p(\theta) = \sum_{k} p(z=k)p(\theta|z=k)
$$
\n(5.68)

where each  $p(\theta|z = k)$  is conjugate, and  $p(z = k)$  are called the (prior) mixing weights. One can show (Exercise 5.1) that the posterior can also be written as a mixture of conjugate distributions as follows:

$$
p(\theta|\mathcal{D}) = \sum_{k} p(z=k|\mathcal{D})p(\theta|\mathcal{D}, z=k)
$$
\n(5.69)

where  $p(Z = k|\mathcal{D})$  are the posterior mixing weights given by

$$
p(Z=k|\mathcal{D}) = \frac{p(Z=k)p(\mathcal{D}|Z=k)}{\sum_{k'} p(Z=k')p(\mathcal{D}|Z=k')} \tag{5.70}
$$

Here the quantity  $p(\mathcal{D}|Z = k)$  is the marginal likelihood for mixture component k (see Section 5.3.2.1).

#### **5.4.4.1 Example**

Suppose we use the mixture prior

$$
p(\theta) = 0.5 \text{Beta}(\theta | a_1, b_1) + 0.5 \text{Beta}(\theta | a_2, b_2)
$$
\n(5.7)

where  $a_1 = b_1 = 20$  and  $a_2 = b_2 = 10$ . and we observe  $N_1$  heads and  $N_0$  tails. The posterior becomes

$$
p(\theta|\mathcal{D}) = p(Z = 1|\mathcal{D})\text{Beta}(\theta|a_1 + N_1, b_1 + N_0) + p(Z = 2|\mathcal{D})\text{Beta}(\theta|a_2 + N_1, b_2 + N_0)
$$
(5.72)

If  $N_1 = 20$  heads and  $N_0 = 10$  tails, then, using Equation 5.23, the posterior becomes

$$
p(\theta|\mathcal{D}) = 0.346 \text{ Beta}(\theta|40, 30) + 0.654 \text{ Beta}(\theta|50, 20)
$$
\n(5.73)

See Figure 5.10 for an illustration.

Image /page/21/Figure/1 description: A line graph titled "mixture of Beta distributions" displays two curves: a dashed red line representing the "prior" distribution and a solid blue line representing the "posterior" distribution. The x-axis ranges from 0 to 1, with tick marks at intervals of 0.1. The y-axis ranges from 0 to 5, with tick marks at intervals of 0.5. The prior distribution has two peaks, one around x=0.5 with a height of approximately 2.4, and another smaller peak around x=0.75 with a height of approximately 2.9. The posterior distribution is unimodal, with a sharp peak around x=0.72 reaching a height of approximately 4.9. The posterior distribution is shifted to the right compared to the prior distribution.

Figure 5.10 A mixture of two Beta distributions. Figure generated by mixBetaDemo.

#### ********* Application: Finding conserved regions in DNA and protein sequences**

We mentioned that Dirichlet-multinomial models are widely used in biosequence analysis. Let us give a simple example to illustrate some of the machinery that has developed. Specifically, consider the sequence logo discussed in Section 2.3.2.1. Now suppose we want to find locations which represent coding regions of the genome. Such locations often have the same letter across all sequences, because of evolutionary pressure. So we need to find columns which are "pure", or nearly so, in the sense that they are mostly all As, mostly all Ts, mostly all Cs, or mostly all Gs. One approach is to look for low-entropy columns; these will be ones whose distribution is nearly deterministic (pure).

But suppose we want to associate a confidence measure with our estimates of purity. This can be useful if we believe adjacent locations are conserved together. In this case, we can let  $Z_1 = 1$  if location t is conserved, and let  $Z_t = 0$  otherwise. We can then add a dependence between adjacent  $Z_t$  variables using a Markov chain; see Chapter 17 for details.

In any case, we need to define a likelihood model,  $p(\mathbf{N}_t|Z_t)$ , where  $\mathbf{N}_t$  is the vector of  $(A,C,G,T)$  counts for column t. It is natural to make this be a multinomial distribution with parameter  $\theta_t$ . Since each column has a different distribution, we will want to integrate out  $\theta_t$ and thus compute the marginal likelihood

$$
p(\mathbf{N}_t|Z_t) = \int p(\mathbf{N}_t|\boldsymbol{\theta}_t)p(\boldsymbol{\theta}_t|Z_t)d\boldsymbol{\theta}_t
$$
\n(5.74)

But what prior should we use for  $\theta_t$ ? When  $Z_t = 0$  we can use a uniform prior,  $p(\theta | Z_t = 0)$  $Dir(1, 1, 1, 1)$ , but what should we use if  $Z_t = 1$ ? After all, if the column is conserved, it could be a (nearly) pure column of As, Cs, Gs, or Ts. A natural approach is to use a mixture of Dirichlet priors, each one of which is "tilted" towards the appropriate corner of the 4-dimensional simplex, e.g.,

$$
p(\boldsymbol{\theta}|Z_t=1) = \frac{1}{4} \text{Dir}(\boldsymbol{\theta}|(10,1,1,1)) + \dots + \frac{1}{4} \text{Dir}(\boldsymbol{\theta}|(1,1,1,10))
$$
\n(5.75)

Since this is conjugate, we can easily compute  $p(\mathbf{N}_t | Z_t)$ . See (Brown et al. 1993) for an
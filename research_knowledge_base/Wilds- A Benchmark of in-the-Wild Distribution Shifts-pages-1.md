# Wilds: A Benchmark of in-the-Wild Distribution Shifts

<PERSON><PERSON><sup>∗</sup> and <PERSON><PERSON><sup>∗</sup> {pangwei, ssagawa}@cs.stanford.edu <PERSON>@stanford.edu <PERSON>@cs.stanford.edu <PERSON>@eecs.berkeley.edu Aks<PERSON> <EMAIL> Weihua Hu <PERSON>@stanford.edu <PERSON><PERSON><PERSON>@stanford.edu <PERSON> **<EMAIL>** <EMAIL> **<PERSON><PERSON>** <EMAIL> <PERSON>@stanford.edu Etienne <PERSON> et<PERSON>.<EMAIL> <PERSON> <EMAIL> Wei <PERSON> guo<PERSON>@g.ecc.u-tokyo.ac.jp Berton <PERSON>. <PERSON>arns<PERSON> <EMAIL> Imran S. Haque **implement in the image of the immanum** <EMAIL> Sara Beery sbeery shows a state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the state of the s Jure Leskov<PERSON> <EMAIL> Anshul <NAME_EMAIL> <PERSON> <EMAIL> Sergey Levine svleving svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine svlevine s <NAME_EMAIL> <NAME_EMAIL>

Correspondence to: <EMAIL>

## Abstract

Distribution shifts—where the training distribution differs from the test distribution—can substantially degrade the accuracy of machine learning (ML) systems deployed in the wild. Despite their ubiquity in the real-world deployments, these distribution shifts are under-represented in the datasets widely used in the ML community today. To address this gap, we present WILDS, a curated benchmark of 10 datasets reflecting a diverse range of distribution shifts that naturally arise in real-world applications, such as shifts across hospitals for tumor identification; across camera traps for wildlife monitoring; and across time and location in satellite imaging and poverty mapping. On each dataset, we show that standard training yields substantially lower out-of-distribution than in-distribution performance. This gap remains even with models trained by existing methods for tackling distribution shifts, underscoring the need for new methods for training models that are more robust to the types of distribution shifts that arise in practice. To facilitate method development, we provide an open-source package that automates dataset loading, contains default model architectures and hyperparameters, and standardizes evaluations. Code and leaderboards are available at <https://wilds.stanford.edu>.

<sup>∗.</sup> These authors contributed equally to this work.

Proceedings of the 38<sup>th</sup> International Conference on Machine Learning, PMLR 139, 2021. Copyright 2021 by the authors.

## Contents

|                | 1 Introduction<br>$\overline{\mathbf{4}}$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |                                                                                   |  |  |  |  |  |
|----------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------|--|--|--|--|--|
| $\bf{2}$       | Existing ML benchmarks for distribution shifts                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | 6                                                                                 |  |  |  |  |  |
| 3              | <b>Problem settings</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |                                                                                   |  |  |  |  |  |
| $\overline{4}$ | <b>WILDS</b> datasets<br>Domain generalization datasets<br>4.1<br>IWILDCAM2020-WILDS: Species classification across different camera traps .<br>4.1.1<br>CAMELYON17-WILDS: Tumor identification across different hospitals $\hfill\ldots\ldots$<br>4.1.2<br>RXRX1-WILDS: Genetic perturbation classification across experimental batches<br>4.1.3<br>OGB-MOLPCBA: Molecular property prediction across different scaffolds .<br>4.1.4<br>$\textsc{GlobalWHEAT-WILDS}\xspace$ . Wheat head detection across regions of the world<br>4.1.5<br>Subpopulation shift datasets<br>4.2<br>CIVILCOMMENTS-WILDS: Toxicity classification across demographic identities<br>4.2.1<br>Hybrid datasets<br>4.3<br>FMOW-WILDS: Land use classification across different regions and years<br>4.3.1<br>4.3.2<br>POVERTYMAP-WILDS: Poverty mapping across different countries<br>AMAZON-WILDS: Sentiment classification across different users<br>4.3.3<br>PY150-WILDS: Code completion across different codebases<br>4.3.4 | 8<br>8<br>$\, 8$<br>8<br>10<br>11<br>11<br>13<br>13<br>13<br>13<br>14<br>15<br>16 |  |  |  |  |  |
| 5              | Performance drops from distribution shifts                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | 17                                                                                |  |  |  |  |  |
| 6              | Baseline algorithms for distribution shifts                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | 19                                                                                |  |  |  |  |  |
| 7              | <b>Empirical trends</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    | 22                                                                                |  |  |  |  |  |
| 8              | Distribution shifts in other application areas<br>Algorithmic fairness<br>8.1<br>Medicine and healthcare<br>8.2<br>8.3<br>Genomics<br>Natural language and speech processing<br>8.4<br>Education<br>8.5<br>8.6<br>Robotics<br>8.7<br>Feedback loops                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | 23<br>23<br>24<br>25<br>26<br>27<br>27<br>28                                      |  |  |  |  |  |
| 9              | Guidelines for method developers<br>General-purpose and specialized training algorithms<br>9.1<br>Methods beyond training algorithms<br>9.2<br>Avoiding overfitting to the test distribution $\dots \dots \dots \dots \dots \dots \dots \dots \dots$<br>9.3<br>Reporting both ID and OOD performance<br>9.4<br>9.5<br>Extensions to other problem settings                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | 28<br>28<br>29<br>29<br>29<br>29                                                  |  |  |  |  |  |
|                | 10 Using the WILDS package                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | 29                                                                                |  |  |  |  |  |
|                | A Dataset realism<br>61                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |                                                                                   |  |  |  |  |  |
|                | B Prior work on ML benchmarks for distribution shifts<br>62                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |                                                                                   |  |  |  |  |  |
|                | C Potential extensions to other problem settings                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           | 63                                                                                |  |  |  |  |  |

## D Additional experimental details 65

| E Additional dataset details and results                                                    | 67  |
|---------------------------------------------------------------------------------------------|-----|
| E.1 IWILDCAM2020-WILDS                                                                      | 67  |
| E.2 CAMELYON17-WILDS                                                                        | 72  |
| RXRX1-WILDS<br>E.3                                                                          | 76  |
| E.4 OGB-MOLPCBA                                                                             | 82  |
| E.5 GLOBALWHEAT-WILDS                                                                       | 85  |
| CIVILCOMMENTS-WILDS<br>E.6                                                                  | 90  |
| E.7 FMoW-WILDS                                                                              | 96  |
| E.8 POVERTYMAP-WILDS                                                                        | 101 |
| AMAZON-WILDS<br>E 9                                                                         | 107 |
| E.10 Py150-WILDS                                                                            | 110 |
| Datasets with distribution shifts that do not cause performance drops                       | 113 |
| F.1 SQF: Criminal possession of weapons across race and locations                           | 113 |
| ENCODE: Transcription factor binding across different cell types $\dots \dots \dots$<br>F.2 | 116 |
| BDD100K: Object recognition in autonomous driving across locations<br>F.3                   | 124 |
| Amazon: Sentiment classification across different categories and time 126<br>F.4            |     |
| F.5 Yelp: Sentiment classification across different users and time                          |     |

# <span id="page-3-0"></span>1. Introduction

Distribution shifts—where the training distribution differs from the test distribution—can significantly degrade the accuracy of machine learning (ML) systems deployed in the wild. In this work, we consider two types of distribution shifts that are ubiquitous in real-world settings: domain generalization and subpopulation shift (Figure [1\)](#page-3-1). In *domain generalization*, the training and test distributions comprise data from related but distinct domains. This problem arises naturally in many applications, as it is often infeasible to collect a training set that spans all domains of interest. For example, in medical applications, it is common to seek to train a model on patients from a few hospitals, and then deploy it more broadly to hospitals outside the training set (Zech et al., 2018); and in wildlife monitoring, we might seek to train an animal recognition model on images from one set of camera traps and then deploy it to new camera traps (Beery et al., 2018). In subpopulation shift, we consider test distributions that are subpopulations of the training distribution, with the goal of doing well even on the worst-case subpopulation. For example, it is well-documented that standard models often perform poorly on under-represented demographics (Buolamwini and Gebru, 2018; Koenecke et al., 2020), and so we might seek models that can perform well on all demographic subpopulations.

<span id="page-3-1"></span>Image /page/3/Figure/2 description: The image displays two distinct machine learning scenarios: Domain Generalization and Subpopulation Shift. The Domain Generalization section shows a training set composed of a mixture of domains, with examples of chemical structures labeled as 'active' or 'inactive' and associated with 'scaffold 1' or 'scaffold 44930'. The test set consists of unseen domains, with examples labeled 'scaffold 44931' and 'scaffold 90124'. The average precision for this scenario is 27.2%. The Subpopulation Shift section also has a training set with a mixture of domains, featuring images of buildings labeled 'mall' (Americas) and 'residential' (Africa). The test set is divided into 'Test (Americas)' with images of 'rec facility' and 'Test (Africa)' with images of 'school'. The accuracy for 'Test (Americas)' is 55.3%, and for 'Test (Africa)' is 32.8%. The worst-region accuracy is 32.8%.

Figure 1: In each WILDS dataset, each data point  $(x, y, d)$  is associated with a domain d. Each domain corresponds to a distribution  $P_d$  over data points which are similar in some way, e.g., molecules with the same scaffold, or satellite images from the same region. We study two types of distribution shifts. Top: In *domain* generalization, we train and test on disjoint sets of domains. The goal is to generalize to domains unseen during training, e.g., molecules with a new scaffold in OGB-MoLPCBA (Hu et al., 2020b). **Bottom:** In subpopulation shift, the training and test domains overlap, but their relative proportions differ. We typically assess models by their worst performance over test domains, each of which correspond to a subpopulation of interest, e.g., different geographical regions in FMoW-with s (Christie et al., 2018).

<span id="page-4-0"></span>Image /page/4/Figure/0 description: The image is a table summarizing datasets used for domain generalization and subpopulation shift tasks. The table has columns for different datasets: iWildCam, Camelyon17, RxRx1, OGB-MolPCBA, GlobalWheat, CivilComments, FMoW, PovertyMap, Amazon, and Py150. Each column details the Input (x), Prediction (y), Domain (d), number of domains, and number of examples for that dataset. Below the data, there are rows for 'Train example' and 'Test example', each showing a representative image or text snippet for each dataset. The 'Adapted from' row at the bottom lists the sources for each dataset. The top of the table is divided into three sections: 'Domain generalization', 'Subpopulation shift', and 'Domain generalization + subpopulation shift'.

Figure 2: The WILDS benchmark contains 10 datasets across a diverse set of application areas, data modalities, and dataset sizes. Each dataset comprises data from different domains, and the benchmark is set up to evaluate models on distribution shifts across these domains.

Despite their ubiquity in real-world deployments, these types of distribution shifts are underrepresented in the datasets widely used in the ML community today (Geirhos et al., 2020). Most of these datasets were designed for the standard i.i.d. setting, with training and test sets from the same distribution, and prior work on retrofitting them with distribution shifts has focused on shifts that are cleanly characterized but not always likely to arise in real-world deployments. For instance, many recent papers have studied datasets with shifts induced by synthetic transformations, such as changing the color of MNIST digits (Arjovsky et al., 2019), or by disparate data splits, such as generalizing from cartoons to photos (Li et al., 2017a). Datasets like these are important testbeds for systematic studies, but they do not generally reflect the kinds of shifts that are likely to arise in the wild. To develop and evaluate methods for real-world shifts, we need to complement these datasets with benchmarks that capture shifts in the wild, as model robustness need not transfer across shifts: e.g., models can be robust to image corruptions but not to shifts across datasets (Taori et al., 2020; Djolonga et al., 2020), and a method that improves robustness on a standard vision dataset can even consistently harm robustness on real-world satellite imagery datasets (Xie et al., 2020).

In this paper, we present WILDS, a curated benchmark of 10 datasets with evaluation metrics and train/test splits representing a broad array of distribution shifts that ML models face in the wild (Figure [2\)](#page-4-0). With Wilds, we seek to complement existing benchmarks by focusing on datasets with realistic shifts across a diverse set of data modalities and applications: animal species categorization (Beery et al., 2020a), tumor identification (Bandi et al., 2018), bioassay prediction (Wu et al., 2018; Hu et al., 2020b), genetic perturbation classification (Taylor et al., 2019), wheat head detection (David et al., 2020), text toxicity classification (Borkan et al., 2019b), land use classification (Christie et al., 2018), poverty mapping (Yeh et al., 2020), sentiment analysis (Ni et al., 2019), and code completion (Raychev et al., 2016; Lu et al., 2021). These datasets reflect natural distribution shifts arising from different cameras, hospitals, molecular scaffolds, experiments, demographics, countries, time periods, users, and codebases.

Wilds builds on extensive data-collection efforts by domain experts, who are often forced to grapple with distribution shifts to make progress in their applications. To design WILDS, we worked with them to identify, select, and adapt datasets that fulfilled the following criteria:

- 1. Distribution shifts with performance drops. The train/test splits reflect shifts that substantially degrade model performance, i.e., with a large gap between in-distribution and out-ofdistribution performance.
- 2. Real-world relevance. The training/test splits and evaluation metrics are motivated by realworld scenarios and chosen in conjunction with domain experts. In Appendix A, we further discuss the framework we use to assess the realism of a dataset.
- 3. Potential leverage. Distribution shift benchmarks must be non-trivial but also possible to solve, as models cannot be expected to generalize to arbitrary distribution shifts. We constructed each WILDS dataset to have training data from multiple domains, with domain annotations and other metadata available at training time. We hope that these can be used to learn robust models: e.g., for domain generalization, one could use these annotations to learn models that are invariant to domain-specific features (Sun and Saenko, 2016; Ganin et al., 2016), while for subpopulation shift, one could learn models that perform uniformly well across each subpopulation (Hu et al., 2018; Sagawa et al., 2020a).

We chose the WILDS datasets to collectively encompass a diverse set of tasks, data modalities, dataset sizes, and numbers of domains, so as to enable evaluation across a broad range of realworld distribution shifts. In Section 8, we further survey the distribution shifts that occur in other application areas—algorithmic fairness and policing, medicine and healthcare, genomics, natural language and speech processing, education, and robotics—and discuss examples of datasets from these areas that we considered but did not include in WILDS, as their distribution shifts did not cause an appreciable performance drop.

To make the WILDS datasets more accessible, we have substantially modified most of them to clarify the distribution shift, standardize the data splits, and preprocess the data for use in standard ML frameworks. In Section 10, we introduce our accompanying open-source Python package that fully automates data loading and evaluation. The package also includes default models appropriate for each dataset, allowing all of the baseline results reported in this paper to be easily replicated. To track the state-of-the-art in training algorithms and model architectures that are robust to these distribution shifts, we are also hosting a public leaderboard; we discuss guidelines for developers in Section 9. Code, leaderboards, and updates are available at <https://wilds.stanford.edu>.

Datasets are significant catalysts for ML research. Likewise, benchmarks that curate and standardize datasets—e.g., the GLUE and SuperGLUE benchmarks for language understanding (Wang et al., 2019a,b) and the Open Graph Benchmark for graph ML (Hu et al., 2020b)—can accelerate research by focusing community attention, easing development on multiple datasets, and enabling systematic comparisons between approaches. In this spirit, we hope that WILDS will facilitate the development of ML methods and models that are robust to real-world distribution shifts and can therefore be deployed reliably in the wild.

## <span id="page-5-0"></span>2. Existing ML benchmarks for distribution shifts

Distribution shifts have been a longstanding problem in the ML research community (Hand, 2006; Quiñonero-Candela et al., 2009). Earlier work studied shifts in datasets for tasks including part-ofspeech tagging (Marcus et al., 1993), sentiment analysis (Blitzer et al., 2007), land cover classification (Bruzzone and Marconcini, 2009), object recognition (Saenko et al., 2010), and flow cytometry (Blanchard et al., 2011). However, these datasets are not as widely used today, in part because they tend to be much smaller than modern datasets.

Instead, many recent papers have focused on object recognition datasets with shifts induced by synthetic transformations, such as ImageNet-C (Hendrycks and Dietterich, 2019), which corrupts images with noise; the Backgrounds Challenge (Xiao et al., 2020) and Waterbirds (Sagawa et al., 2020a), which alter image backgrounds; or Colored MNIST (Arjovsky et al., 2019), which changes the

colors of MNIST digits. It is also common to use data splits or combinations of disparate datasets to induce shifts, such as generalizing to photos solely from cartoons and other stylized images in PACS (Li et al., 2017a); generalizing to objects at different scales solely from a single scale in DeepFashion Remixed (Hendrycks et al., 2020b); or using training and test sets with disjoint subclasses in BREEDS (Santurkar et al., 2020) and similar datasets (Hendrycks and Dietterich, 2019). While our treatment here is necessarily brief, we discuss other similar datasets in Appendix B.

These existing benchmarks are useful and important testbeds for method development. As they typically target well-defined and isolated shifts, they facilitate clean analysis and controlled experimentation, e.g., studying the effect of backgrounds on image classification (Xiao et al., 2020), or showing that training with added Gaussian blur improves performance on real-world blurry images (Hendrycks et al., 2020b). Moreover, by studying how off-the-shelf models trained on standard datasets like ImageNet perform on different test datasets, we can better understand the robustness of these widely-used models (Geirhos et al., 2018b; Recht et al., 2019; Hendrycks and Dietterich, 2019; Taori et al., 2020; Djolonga et al., 2020; Hendrycks et al., 2020b).

However, as we discussed in the introduction, robustness to these synthetic shifts need not transfer to the kinds of shifts that arise in real-world deployments (Taori et al., 2020; Djolonga et al., 2020; Xie et al., 2020), and it is thus challenging to develop and evaluate methods for training models that are robust to real-world shifts on these datasets alone. With WILDS, we seek to complement existing benchmarks by curating datasets that reflect natural distribution shifts across a diverse set of data modalities and application.

#### <span id="page-6-0"></span>3. Problem settings

Each Wilds dataset is associated with a type of domain shift: domain generalization, subpopulation shift, or a hybrid of both (Figure [2\)](#page-4-0). We focus on these types of distribution shifts because they collectively capture the structure of most of the shifts in the applications we studied; see Section 8 for more discussion. In each setting, we can view the overall data distribution as a mixture of D domains  $\mathcal{D} = \{1, \ldots, D\}$ . Each domain  $d \in \mathcal{D}$  corresponds to a fixed data distribution  $P_d$  over  $(x, y, d)$ , where x is the input, y is the prediction target, and all points sampled from  $P_d$  have domain d. We encode the domain shift by assuming that the training distribution  $P^{train} = \sum_{d \in \mathcal{D}} q_d^{train} P_d$  has mixture weights  $q_d^{\text{train}}$  for each domain d, while the test distribution  $P^{\text{test}} = \sum_{d \in \mathcal{D}} q_d^{\text{test}} \overline{P_d}$  is a different mixture of domains with weights  $q_d^{\text{test}}$ . For convenience, we define the set of training domains as  $\mathcal{D}^{\text{train}} = \{d \in \mathcal{D} \mid q_d^{\text{train}} > 0\},\$ and likewise, the set of test domains as  $\mathcal{D}^{\text{test}} = \{d \in \mathcal{D} \mid q_d^{\text{test}} > 0\}.$ 

At training time, the learning algorithm gets to see the domain annotations  $d$ , i.e., the training set comprises points  $(x, y, d) \sim P^{\text{train}}$ . At test time, the model gets either x or  $(x, d)$  drawn from  $P<sup>test</sup>$ , depending on the application.

#### 3.1 Domain generalization (Figure [1-](#page-3-1)Top)

In domain generalization, we aim to generalize to test domains  $\mathcal{D}^{\text{test}}$  that are disjoint from the training domains  $\mathcal{D}^{train}$ , i.e.,  $\mathcal{D}^{train} \cap \mathcal{D}^{test} = \emptyset$ . To make this problem tractable, the training and test domains are typically similar to each other: e.g., in CAMELYON17-WILDS, we train on data from some hospitals and test on a different hospital, and in IWILDCAM2020-WILDS, we train on data from some camera traps and test on different camera traps. We typically seek to minimize the average error on the test distribution.

#### 3.2 Subpopulation shift (Figure [1-](#page-3-1)Bottom)

In subpopulation shift, we aim to perform well across a wide range of domains seen during training time. Concretely, all test domains are seen at training, with  $\mathcal{D}^{\text{test}} \subseteq \mathcal{D}^{\text{train}}$ , but the proportions of the domains can change, with  $q^{\text{test}} \neq q^{\text{train}}$ . We typically seek to minimize the maximum error

over all test domains. For example, in CIVILCOMMENTS-WILDS, the domains d represent particular demographics, some of which are a minority in the training set, and we seek high accuracy on each of these subpopulations without observing their demographic identity  $d$  at test time.

#### 3.3 Hybrid settings

The categories of domain generalization and subpopulation shift provide a general framework for thinking about domain shifts, and the methods that have been developed for each setting have been quite different, as we will discuss in Section 6. However, it is not always possible to cleanly define a problem as one or the other; for example, a test domain might be present in the training set but at a very low frequency. In Wilds, we consider some hybrid settings that combine both domain generalization and subpopulation shift. For example, in FMoW-wilds, the inputs are satellite images and the domains correspond to the year and geographical region in which they were taken. We simultaneously consider domain generalization across time (the training/test sets comprise images taken before/after a certain year) and subpopulation shift across regions (there are images from the same regions in the training and test sets, and we seek high performance across all regions).

## <span id="page-7-0"></span>4. WILDS datasets

We now briefly describe each WILDS dataset, as summarized in Figure [2.](#page-4-0) For each dataset, we consider a problem setting—domain generalization, subpopulation shift, or a hybrid—that we believe best reflects the real-world challenges in the corresponding application area; see Appendix A for more discussion of these considerations. To avoid confusion between our modified datasets and their original sources, we append -wilds to the dataset names. We provide more details and context on related distribution shifts for each dataset in Appendix E.

#### <span id="page-7-1"></span>4.1 Domain generalization datasets

#### <span id="page-7-2"></span>4.1.1 iWildCam2020-wilds: Species classification across different camera traps

Animal populations have declined 68% on average since 1970 (Grooten et al., 2020). To better understand and monitor wildlife biodiversity loss, ecologists commonly deploy camera traps—heat or motion-activated static cameras placed in the wild (Wearn and Glover-Kapfer, 2017)—and then use ML models to process the data collected (Weinstein, 2018; Norouzzadeh et al., 2019; Tabak et al., 2019; Beery et al., 2019; Ahumada et al., 2020). Typically, these models would be trained on photos from some existing camera traps and then used across new camera trap deployments. However, across different camera traps, there is drastic variation in illumination, color, camera angle, background, vegetation, and relative animal frequencies, which results in models generalizing poorly to new camera trap deployments (Beery et al., 2018).

We study this shift on a variant of the iWildCam 2020 dataset (Beery et al., 2020a), where the input x is a photo from a camera trap, the label y is one of 182 animal species, and the domain d specifies the identity of the camera trap (Figure [3\)](#page-8-0). The training and test sets comprise photos from disjoint sets of camera traps. As leverage, we include over 200 camera traps in the training set, capturing a wide range of variation. We evaluate models by their macro F1 scores, which emphasizes performance on rare species, as rare and endangered species are the most important to accurately monitor. Appendix E.1 provides additional details and context.

#### <span id="page-7-3"></span>4.1.2 Camelyon17-wilds: Tumor identification across different hospitals

Models for medical applications are often trained on data from a small number of hospitals, but with the goal of being deployed more generally across other hospitals. However, variations in data collection and processing can degrade model accuracy on data from new hospital deployments (Zech

<span id="page-8-0"></span>Image /page/8/Figure/0 description: The image is a table that categorizes wildlife photos into training and testing sets. The training set includes photos labeled "d = Location 1" showing Vulturine Guineafowl, "d = Location 2" showing African Bush Elephants, "d = Location 245" showing an unknown subject, and "d = Location 1" showing a Cow, and "d = Location 2" showing Cows. The Test (OOD) set includes photos labeled "d = Location 246" showing a Wild Horse, "d = Location 245" showing a Southern Pig-Tailed Macaque, and "d = Location 246" showing a Great Curassow. The Test (ID) set includes photos labeled "d = Location 1" showing a Giraffe, "d = Location 2" showing an Impala, and "d = Location 245" showing a Sun Bear.

Figure 3: The IWILDCAM2020-WILDS dataset comprises photos of wildlife taken by a variety of camera traps. The goal is to learn models that generalize to photos from new camera traps that are not in the training set. Each Wilds dataset contains both in-distribution (ID) and out-of-distribution (OOD) evaluation sets; for brevity, we omit the ID sets from the subsequent dataset figures.

<span id="page-8-1"></span>

|                | Train                               |                                     | Val (OOD)                           | Test (OOD)                          |                                     |
|----------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|
| d = Hospital 1 | d = Hospital 2                      | d = Hospital 3                      | d = Hospital 4                      | d = Hospital 5                      |                                     |
| Normal         | Image: Normal cells from Hospital 1 | Image: Normal cells from Hospital 2 | Image: Normal cells from Hospital 3 | Image: Normal cells from Hospital 4 | Image: Normal cells from Hospital 5 |
| Tumor          | Image: Tumor cells from Hospital 1  | Image: Tumor cells from Hospital 2  | Image: Tumor cells from Hospital 3  | Image: Tumor cells from Hospital 4  | Image: Tumor cells from Hospital 5  |

Figure 4: The Camelyon17-wilds dataset comprises tissue patches from different hospitals. The goal is to accurately predict the presence of tumor tissue in patches taken from hospitals that are not in the training set. In this figure, each column contains two patches, one of normal tissue and the other of tumor tissue, from the same slide.

et al., 2018; AlBadawy et al., 2018). In histopathology applications—studying tissue slides under a microscope—this variation can arise from sources like differences in the patient population or in slide staining and image acquisition (Veta et al., 2016; Komura and Ishikawa, 2018; Tellez et al., 2019).

We study this shift on a patch-based variant of the Camelyon 17 dataset (Bandi et al., 2018), where the input x is a 96x96 patch of a whole-slide image of a lymph node section from a patient with potentially metastatic breast cancer, the label  $y$  is whether the patch contains tumor, and the domain d specifies which of 5 hospitals the patch was from (Figure [4\)](#page-8-1). The training and test sets comprise class-balanced patches from separate hospitals, and we evaluate models by their average accuracy. Prior work suggests that staining differences are the main source of variation between hospitals in similar datasets (Tellez et al., 2019). As we have training data from multiple hospitals, a model could use that as leverage to learn to be robust to stain variation. Appendix E.2 provides additional details and context.

#### <span id="page-9-0"></span>4.1.3 RxRx1-wilds: Genetic perturbation classification across experimental batches

High-throughput screening techniques that can generate large amounts of data are now common in many fields of biology, including transcriptomics (Harrill et al., 2019), genomics (Echeverri and Perrimon, 2006; Zhou et al., 2014), proteomics and metabolomics (Taylor et al., 2021), and drug discovery (Broach et al., 1996; Macarron et al., 2011; Swinney and Anthony, 2011; Boutros et al., 2015). Such large volumes of data, however, need to be created in experimental batches, or groups of experiments executed at similar times under similar conditions. Despite attempts to carefully control experimental variables such as temperature, humidity, and reagent concentration, measurements from these screens are confounded by technical artifacts that arise from differences in the execution of each batch. These batch effects make it difficult to draw conclusions from data across experimental batches (Leek et al., 2010; Parker and Leek, 2012; Soneson et al., 2014; Nygaard et al., 2016; Caicedo et al., 2017).

We study the shift induced by batch effects on a variant of the RxRx1 dataset (Taylor et al.,  $2019$ ), where the input x is a 3-channel image of cells obtained by fluorescent microscopy (Bray et al., 2016), the label y indicates which of the 1,139 genetic treatments (including no treatment) the cells received, and the domain d specifies the batch in which the imaging experiment was run.

<span id="page-9-1"></span>Image /page/9/Figure/5 description: The image displays a grid of four experiments, divided into training, validation (out-of-distribution), and testing (out-of-distribution) sets. Each experiment is further categorized by siRNA type: siRNA A and siRNA B. Experiment 1 and 2 are labeled as 'Train', Experiment 3 as 'Val (OOD)', and Experiment 4 as 'Test (OOD)'. Each cell in the grid contains a microscopic image of cells, likely stained to visualize different cellular components. The images for siRNA A and siRNA B are presented in separate rows, while the experiments are arranged in columns.

Figure 5: The RxRx1-WILDS dataset comprises images of cells that have been genetically perturbed by siRNA (Tuschl, 2001). The goal is to predict which siRNA the cells have been treated with, where the images come from experimental batches not in the training set. Here, we show sample images from different batches for two of the 1,139 possible classes.

As summarized in Figure [5,](#page-9-1) the training and test sets consist of disjoint experimental batches. As leverage, the training set has images from 33 different batches, with each batch containing one sample for every class. We assess a model's ability to normalize batch effects while preserving biological signal by evaluating how well it can classify images of treated cells in the out-of-distribution test set. Appendix E.3 provides additional details and context.

#### <span id="page-10-0"></span>4.1.4 OGB-MolPCBA: Molecular property prediction across different scaffolds

Accurate prediction of the biochemical properties of small molecules can significantly accelerate drug discovery by reducing the need for expensive lab experiments (Shoichet, 2004; Hughes et al., 2011). However, the experimental data available for training such models is limited compared to the extremely diverse and combinatorially large universe of candidate molecules that we would want to make predictions on (Bohacek et al., 1996; Sterling and Irwin, 2015; Lyu et al., 2019; McCloskey et al., 2020). This means that models need to generalize to out-of-distribution molecules that are structurally different from those seen in the training set.

We study this shift on the OGB-MOLPCBA dataset, which is directly adopted from the Open Graph Benchmark (Hu et al., 2020b) and originally from MoleculeNet (Wu et al., 2018). As summarized in Figure [6,](#page-10-2) it is a multi-label classification dataset, where the input  $x$  is a molecular graph, the label y is a 128-dimensional binary vector where each component corresponds to a biochemical assay result, and the domain d specifies the scaffold (i.e., a cluster of molecules with similar structure). The training and test sets comprise molecules with disjoint scaffolds; for leverage, the training set has molecules from over 40,000 scaffolds. We evaluate models by averaging the Average Precision (AP) across each of the 128 assays. Appendix E.4 provides additional details and context.

<span id="page-10-2"></span>Image /page/10/Figure/4 description: The image displays a table with two main columns: "Train" and "Test". The "Train" column is further divided into four sub-columns labeled "Scaffold 11", "Scaffold 32", "Scaffold 321", and "Scaffold 4413". Each scaffold sub-column contains two chemical structures with associated numerical data in parentheses. For "Scaffold 11", the data are (1,0,?,0,?,..) and (?,0,0,0,?,..). For "Scaffold 32", the data are (?,0,0,0,?,..) and (?,0,?,1,0,..). For "Scaffold 321", the data are (0,1,1,0,0,..) and (?,0,0,0,1,..). For "Scaffold 4413", the data are (1,1,0,1,0,..) and (?,0,0,0,?,..). The "Test" column has two sub-columns labeled "Scaffold 54113" and "Scaffold 65912". "Scaffold 54113" shows one chemical structure with data (0,?,1,?,0,..). "Scaffold 65912" shows one chemical structure with data (0,1,0,0,0,..). Ellipses (...) indicate that there are more scaffolds not shown in the "Train" and "Test" sections.

Figure 6: The OGB-MolPCBA dataset comprises molecules with many different structural scaffolds. The goal is to predict biochemical assay results in molecules with scaffolds that are not in the training set. Here, we show sample molecules from each scaffold, together with target labels: each molecule is associated with 128 binary labels and '?' indicates that the label is not provided for the molecule.

#### <span id="page-10-1"></span>4.1.5 GlobalWheat-wilds: Wheat head detection across regions of the world

Models for automated, high-throughput plant phenotyping—measuring the physical characteristics of plants and crops, such as wheat head density and counts—are important tools for crop breeding (Thorp et al., 2018; Reynolds et al., 2020) and agricultural field management (Shi et al., 2016). These models are typically trained on data collected in a limited number of regions, even for crops grown worldwide such as wheat (Madec et al., 2019; Xiong et al., 2019; Ubbens et al., 2020; Ayalew et al., 2020). However, there can be substantial variation between regions, due to differences in crop varieties, growing conditions, and data collection protocols. Prior work on wheat head detection has shown that this variation can significantly degrade model performance on regions unseen during training (David et al., 2020).

We study this shift in an expanded version of the Global Wheat Head Dataset (David et al., 2020, 2021), a large set of wheat images collected from 12 countries around the world (Figure [7\)](#page-11-0). It is a detection dataset, where the input  $x$  is a cropped overhead image of a wheat field, the label  $y$  is the set of bounding boxes for each wheat head visible in the image, and the domain  $d$  specifies an image acquisition session (i.e., a specific location, time, and sensor with which a set of images was collected). The data split captures a shift in location, with training and test sets comprising images from disjoint countries. As leverage, we include images from 18 acquisition sessions over 5 countries in the training set. We evaluate model performance on unseen countries by measuring accuracy at a fixed Intersection over Union (IoU) threshold, and averaging across acquisition sessions to account for imbalances in the numbers of images in them. Additional details are provided in Appendix E.5.

<span id="page-11-0"></span>Image /page/11/Figure/2 description: The image displays a grid of images categorized into Train, Val (OOD), and Test (OOD) sets. Each category contains multiple images, with the Train set showing two columns of two images each, and the Val (OOD) and Test (OOD) sets each showing two columns of two images each. Below each column of images, there is a label indicating the dataset origin, such as 'd = Uliège\_1 Belgium', 'd = Arvalis\_1 France', 'd = RRES\_1 United Kingdom', 'd = NAU\_2 China', 'd = KSU\_2 United States', 'd = ETHZ\_1 Switzerland', 'd = Arvalis\_12 France', 'd = NMBU\_2 Norway', 'd = ARC\_1 Sudan', and 'd = UQ\_7 Australia'. All images are annotated with numerous blue bounding boxes, suggesting object detection or segmentation tasks.

Figure 7: The GLOBALWHEAT-WILDS dataset consists of overhead images of wheat fields, annotated with bounding boxes of wheat heads. The goal is to detect and predict the bounding boxes of wheat heads, where images are from new acquisition sessions. A set of wheat images are collected in each acquisition session, each corresponding to a specific wheat field location, time, and sensor. While acquisition sessions vary along multiple axes, from the aforementioned factors to wheat growth stage to illumination conditions, the dataset split primarily captures a shift in location; test images are taken from countries unseen during training time. In this figure, we show images with bounding boxes from different acquisition sessions.

#### <span id="page-12-0"></span>4.2 Subpopulation shift datasets

<span id="page-12-1"></span>4.2.1 CivilComments-wilds: Toxicity classification across demographic identities

Automatic review of user-generated text is an important tool for moderating the sheer volume of text written on the Internet. We focus here on the task of detecting toxic comments. Prior work has shown that toxicity classifiers can pick up on biases in the training data and spuriously associate toxicity with the mention of certain demographics (Park et al., 2018; Dixon et al., 2018). These types of spurious correlations can significantly degrade model performance on particular subpopulations (Sagawa et al., 2020a).

We study this problem on a variant of the CivilComments dataset (Borkan et al., 2019b), a large collection of comments on online articles taken from the Civil Comments platform (Figure [8\)](#page-12-4). The input x is a text comment, the label y is whether the comment was rated as toxic, and the domain  $d$  is a 8-dimensional binary vector where each component corresponds to whether the comment mentions one of the 8 demographic identities male, female, LGBTQ, Christian, Muslim, other religions, Black, and White. The training and test sets comprise comments on disjoint articles, and we evaluate models by the lowest true positive/negative rate over each of these 8 demographic groups; these groups overlap with each other, deviating slightly from the standard subpopulation shift framework in Section [3.](#page-6-0) Models can use the provided domain annotations as leverage to learn to perform well over each demographic group. Appendix E.6 provides additional details and context.

<span id="page-12-4"></span>

| Toxic | Comment Text                                                                                                                       | Male | Female | LGBTQ | White | Black ... | Christian |
|-------|------------------------------------------------------------------------------------------------------------------------------------|------|--------|-------|-------|-----------|-----------|
| 0     | I applaud your father. He was a good man!<br>We need more like him.                                                                | 1    | 0      | 0     | 0     | ...       | 0         |
| 0     | As a Christian, I will not be patronizing any of<br>those businesses.                                                              | 0    | 0      | 0     | 0     | ...       | 1         |
| 0     | What do Black and LGBT people have to do<br>with bicycle licensing?                                                                | 0    | 0      | 1     | 0     | ...       | 0         |
| 0     | Government agencies track down foreign<br>baddies and protect law-abiding white<br>citizens. How many shows does that<br>describe? | 0    | 0      | 0     | 1     | ...       | 0         |
| 1     | Maybe you should learn to write a coherent<br>sentence so we can understand WTF your<br>point is.                                  | 0    | 0      | 0     | 0     | ...       | 0         |

Figure 8: The CivilComments-wilds dataset involves classifying the toxicity of online comments. The goal is to learn models that avoid spuriously associating mentions of demographic identities (like male, female, etc.) with toxicity due to biases in the training data.

#### <span id="page-12-2"></span>4.3 Hybrid datasets

#### <span id="page-12-3"></span>4.3.1 FMoW-wilds: Land use classification across different regions and years

ML models for satellite imagery can enable global-scale monitoring of sustainability and economic challenges, aiding policy and humanitarian efforts in applications such as deforestation tracking (Hansen et al., 2013), population density mapping (Tiecke et al., 2017), crop yield prediction (Wang et al., 2020b), and other economic tracking applications (Katona et al., 2018). As satellite data constantly changes due to human activity and environmental processes, these models must be robust to distribution shifts over time. Moreover, as there can be disparities in the data available between regions, these models should ideally have uniformly high accuracies instead of only doing well on data-rich regions and countries.

<span id="page-13-1"></span>Image /page/13/Figure/0 description: This image is a table that categorizes satellite images into training and testing sets. The table has three rows and five columns. The first row indicates 'Train' and 'Test' as headers for the satellite image categories. The second row, labeled 'Satellite Image (x)', displays five satellite images. The third row, labeled 'Year / Region (d)', lists the year and region for each corresponding satellite image: '2002 / Americas', '2009 / Africa', '2012 / Europe' under 'Train', and '2016 / Americas', '2017 / Africa' under 'Test'. The fourth row, labeled 'Building / Land Type (y)', specifies the type of building or land depicted in each satellite image: 'shopping mall', 'multi-unit residential', 'road bridge' for the 'Train' set, and 'recreational facility', 'educational institution' for the 'Test' set.

Figure 9: The FMoW-will Ds dataset contains satellite images taken in different geographical regions and at different times. The goal is to generalize to satellite imagery taken in the future, which may be shifted due to infrastructure development across time, and to do equally well across geographic regions.

We study this problem on a variant of the Functional Map of the World dataset (Christie et al., 2018), where the input x is an RGB satellite image, the label y is one of 62 building or land use categories, and the domain d represents the year the image was taken and its geographical region (Africa, the Americas, Oceania, Asia, or Europe) (Figure [9\)](#page-13-1). The different regions have different numbers of examples, e.g., there are far fewer images from Africa than the Americas. The training set comprises data from before 2013, while the test set comprises data from 2016 and after; years 2013 to 2015 are reserved for the validation set. We evaluate models by their test accuracy on the worst geographical region, which combines both a domain generalization problem over time and a subpopulation shift problem over regions. As we provide both time and region annotations, models can leverage the structure across both space and time to improve robustness. Appendix E.7 provides additional details and context.

## <span id="page-13-0"></span>4.3.2 PovertyMap-wilds: Poverty mapping across different countries

Global-scale poverty estimation is a specific remote sensing application which is essential for targeted humanitarian efforts in poor regions (Abelson et al., 2014; Espey et al., 2015). However, ground truth measurements of poverty are lacking for much of the developing world, as field surveys for collecting the ground truth are expensive (Blumenstock et al., 2015). This motivates the approach of training ML models on countries with ground truth labels and then deploying them on different countries where we have satellite data but no labels (Xie et al., 2016; Jean et al., 2016; Yeh et al., 2020).

We study this shift through a variant of the poverty mapping dataset collected by Yeh et al.  $(2020)$ , where the input x is a multispectral satellite image, the output y is a real-valued asset wealth index from surveys, and the domain d represents the country the image was taken in and whether the image is of an urban or rural area (Figure [10\)](#page-14-1). The training and test set comprise data from disjoint sets of countries, and we evaluate models by the correlation of their predictions with the ground truth. Specifically, we take the lower of the correlations over the urban and rural subpopulations, as prior work has shown that accurately predicting poverty within these subpopulations is especially challenging. As poverty measures are highly correlated across space (Jean et al., 2018; Rolf et al., 2020), methods can utilize the provided location coordinates, and the country and urban/rural annotations, to improve robustness. Appendix E.8 provides additional details and context.

<span id="page-14-1"></span>

|                                 |                   | Train             |                   | <b>Test</b>      |                  |
|---------------------------------|-------------------|-------------------|-------------------|------------------|------------------|
| Satellite image<br>(x)          |                   |                   |                   |                  |                  |
| Country /<br>Urban-rural<br>(d) | Angola /<br>urban | Angola /<br>rural | Angola /<br>urban | Kenya /<br>urban | Kenya /<br>rural |
| Asset<br>index<br>(y)           | 0.259             | $-1.106$          | 2.347             | 0.827            | 0.130            |

Figure 10: The PovertyMap-willos dataset contains satellite images taken in different countries. The goal is to predict asset wealth in countries that are not present in the training set, while being accurate in both urban and rural areas. There may be significant economic and cultural differences across country borders that contribute to the spatial distribution shift.

<span id="page-14-2"></span>

|       | Reviewer ID (d)    | Review Text (x)                                                                                                                                         | Stars $(v)$ |
|-------|--------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|-------------|
|       | Reviewer 1         | They are decent shoes. Material quality is good but the<br>color fades very quickly. Not as black in person as<br>shown.                                | 5           |
|       |                    | Super easy to put together. Very well built.                                                                                                            | 5           |
|       | Reviewer 2         | This works well and was easy to install. The only thing I<br>don't like is that it tilts forward a little bit and I can't<br>figure out how to stop it. | 4           |
| Train |                    | Perfect for the trail camera                                                                                                                            | 5           |
|       |                    |                                                                                                                                                         |             |
|       | Reviewer<br>10,000 | I am disappointed in the quality of these. They have<br>significantly deteriorated in just a few uses. I am going<br>to stick with using foil.          | 1           |
|       |                    | Very sturdy especially at this price point. I have a<br>memory foam mattress on it with nothing underneath<br>and the slats perform well.               | 5           |
|       | Reviewer<br>10,001 | Solidly built plug in. I have had 4 devices plugged in<br>and all charge just fine.                                                                     | 5           |
| Test  |                    | Works perfectly on the wall to hang our wreath without<br>having to do any permanent damage.                                                            | 5           |
|       |                    |                                                                                                                                                         |             |

Figure 11: The Amazon-wilds dataset involves predicting star ratings from reviews of Amazon products. The goal is to do consistently well on new reviewers who are not in the training set.

## <span id="page-14-0"></span>4.3.3 Amazon-wilds: Sentiment classification across different users

In many consumer-facing ML applications, models are trained on data collected on one set of users and then deployed across a wide range of potentially new users. These models can perform well on average but poorly on some users (Tatman, 2017; Caldas et al., 2018; Li et al., 2019b; Koenecke et al., 2020). These large performance disparities across users are practical concerns in consumer-facing applications, and they can also indicate that models are exploiting biases or spurious correlations in the data (Badgeley et al., 2019; Geva et al., 2019).

We study this issue on a variant of the Amazon review dataset (Ni et al., 2019), where the input x is the review text, the label y is the corresponding 1-to-5 star rating, and the domain d identifies the user who wrote the review (Figure [11\)](#page-14-2). The training and test sets comprise reviews from disjoint sets of users; for leverage, the training set has reviews from 5,008 different users. As our goal is to train models with consistently high performance across users, we evaluate models by the 10th percentile of per-user accuracies. Appendix E.9 provides additional details and context. We discuss other distribution shifts on this dataset (e.g., by category) in Appendix F.4.

#### <span id="page-15-0"></span>4.3.4 Py150-wilds: Code completion across different codebases

Code completion models—autocomplete tools used by programmers to suggest subsequent source code tokens, such as the names of API calls—are commonly used to reduce the effort of software development (Robbes and Lanza, 2008; Bruch et al., 2009; Nguyen and Nguyen, 2015; Proksch et al., 2015; Franks et al., 2015). These models are typically trained on data collected from existing codebases but then deployed more generally across other codebases, which may have different distributions of API usages (Nita and Notkin, 2010; Proksch et al., 2016; Allamanis and Brockschmidt, 2017). This shift across codebases can cause substantial performance drops in code completion models. Moreover, prior studies of real-world usage of code completion models have noted that they can generalize poorly on some important subpopulations of tokens such as method names (Hellendoorn et al., 2019).

We study a variant of the Py150 Dataset (Raychev et al., 2016; Lu et al., 2021), where the goal is to predict the next token (e.g., "environ", "communicate" in Figure [12\)](#page-15-1) given the context of previous tokens. The input x is a sequence of source code tokens, the label y is the next token, and the domain d specifies the repository that the source code belongs to. The training and test sets comprise code from disjoint GitHub repositories. As leverage, we include over 5,300 repositories in the training set, capturing a wide range of source code variation. We evaluate models by their accuracy on the subpopulation of class and method tokens. Additional dataset and model details are provided in Appendix E.10.

<span id="page-15-1"></span>

|                              | Repository ID $(d)$ | Source code context $(x)$                                                                                                                     | Next tokens $(y)$ |
|------------------------------|---------------------|-----------------------------------------------------------------------------------------------------------------------------------------------|-------------------|
| <b>Train</b><br>Repository 1 |                     | from easyrec.gateway import EasyRec <eol> gateway =<br/><math>EasyRec('tenant', 'key') &lt; EOL&gt; item_type = gateway.</math></eol>         | get_item_type     |
|                              |                     | $\ldots$ response = gateway.get_other_users() <eol><br/><math>get_params = HTTPretty.</math></eol>                                            | last_request      |
|                              | Repository 2        | import numpy as np $\ldots$ <eol> if np.linalg.norm(target -<br/><math>prev\_target) &gt; far\_threshold: <eol> norm = np.</eol></math></eol> | linalg            |
|                              |                     | new_trans = np.zeros((n_beats + max_beats, n_beats)<br>$\leq$ EOL> new_trans[:n_beats,:n_beats] = np. ____                                    | max               |
|                              |                     |                                                                                                                                               |                   |
| Test                         | Repository 6,001    | if e.errno == errno.ENOENT: $\leq$ EOL> continue $\leq$ EOL> p =<br>subprocess. Popen () $\leq$ DL> stdout = p.                               | communicate       |
|                              |                     | $\ldots$ command = shlex.split(command) <eol> command =<br/><math>map(str, command) \leq DL</math> env = os.</eol>                            | environ           |
|                              |                     |                                                                                                                                               |                   |

Figure 12: The Py150-will both dataset comprises Python source code files taken from a variety of public repositories on GitHub. The task is code completion: predict token names given the context of previous tokens. We evaluate models on their accuracy on the subpopulation of API calls (i.e., method and class tokens), which are the most common code completion queries in real-world settings. Our goal is to learn code completion models that generalize to source code in new repositories that are not seen in the training set.
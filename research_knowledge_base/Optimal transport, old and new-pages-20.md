This chapter is devoted to a recap of the whole picture of optimal transport on a smooth Riemannian manifold M. For simplicity I shall not try to impose the most general assumptions. A good understanding of this chapter is sufficient to attack Part II of this course.

### Recap

Let  $M$  be a smooth complete connected Riemannian manifold,  $L(x, v, t)$ a  $C^2$  Lagrangian function on  $TM \times [0,1]$ , satisfying the classical conditions of Definition 7.6, together with  $\nabla_v^2 L > 0$ . Let  $c : M \times M \to \mathbb{R}$ be the induced cost function:

$$
c(x,y) = \inf \Big\{ \int_0^1 L(\gamma_t, \dot{\gamma}_t, t) dt; \quad \gamma_0 = x, \ \gamma_1 = y \Big\}.
$$

More generally, define

$$
c^{s,t}(x,y) = \inf \Big\{ \int_s^t L(\gamma_\tau, \dot{\gamma}_\tau, \tau) d\tau; \quad \gamma_s = x, \ \gamma_t = y \Big\}.
$$

So  $c^{s,t}(x,y)$  is the optimal cost to go from point x at time s, to point  $y$  at time  $t$ .

I shall consider three cases: (i)  $L(x, v, t)$  arbitrary on a compact manifold; (ii)  $L(x, v, t) = |v|^2/2$  on a complete manifold (so the cost is  $d^2/2$ , where d is the distance); (iii)  $L(x, v, t) = |v|^2/2$  in  $\mathbb{R}^n$  (so the cost is  $|x-y|^2/2$ ). Throughout the sequel, I denote by  $\mu_0$  the initial probability measure, and by  $\mu_1$  the final one. When I say "absolutely

continuous" or "singular" this is in reference with the volume measure on the manifold (Lebesgue measure in  $\mathbb{R}^n$ ).

Recall that a generalized optimal coupling is a c-cyclically monotone coupling. By analogy, I shall say that a generalized displacement interpolation is a path  $(\mu_t)_{0 \le t \le 1}$  valued in the space of probability measures, such that  $\mu_t = \text{law}(\gamma_t)$  and  $\gamma$  is a random minimizing curve such that  $(\gamma_0, \gamma_1)$  is a generalized optimal coupling. These notions are interesting only when the total cost between  $\mu_0$  and  $\mu_1$  is infinite.

By gathering the results from the previous chapters, we know:

1. There always exists:

- an optimal coupling (or generalized optimal coupling)  $(x_0, x_1)$ , with  $\text{law } \pi$ ;
- a displacement interpolation (or generalized displacement interpolation)  $(\mu_t)_{0 \leq t \leq 1}$ ;
- a random minimizing curve  $\gamma$  with law  $\Pi$ ;

such that law  $(\gamma_t) = \mu_t$ , and law  $(\gamma_0, \gamma_1) = \pi$ . Each curve  $\gamma$  is a solution of the Euler–Lagrange equation

$$
\frac{d}{dt}\nabla_v L(\gamma_t, \dot{\gamma}_t, t) = \nabla_x L(\gamma_t, \dot{\gamma}_t, t). \tag{13.1}
$$

In the case of a quadratic Lagrangian, this equation reduces to

$$
\frac{d^2\gamma_t}{dt^2} = 0,
$$

so trajectories are just geodesics, or straight lines in  $\mathbb{R}^n$ . Two trajectories in the support of  $\Pi$  may intersect at time  $t = 0$  or  $t = 1$ , but never at intermediate times.

**2.** If either  $\mu_0$  or  $\mu_1$  is absolutely continuous, then so is  $\mu_t$ , for all  $t \in (0,1)$ .

**3.** If  $\mu_0$  is absolutely continuous, then the optimal coupling  $(x_0, x_1)$ is unique (in law), deterministic  $(x_1 = T(x_0))$  and characterized by the equation

$$
\nabla \psi(x_0) = -\nabla_x c(x_0, x_1) = \nabla_v L(x_0, \dot{\gamma}_0, 0), \tag{13.2}
$$

where  $(\gamma_t)_{0 \leq t \leq 1}$  is the minimizing curve joining  $\gamma_0 = x_0$  to  $\gamma_1 = x_1$  (it is part of the theorem that this curve is almost surely unique), and  $\psi$ is a c-convex function, that is, it can be written as

$$
\psi(x) = \sup_{y \in M} [\phi(y) - c(x, y)]
$$

for some nontrivial (i.e. not identically  $-\infty$ , and never  $+\infty$ ) function  $\phi$ . In case (ii), if nothing is known about the behavior of the distance function at infinity, then the gradient  $\nabla$  in the left-hand side of (13.2) should be replaced by an approximate gradient  $\nabla$ .

4. Under the same assumptions, the (generalized) displacement interpolation  $(\mu_t)_{0 \leq t \leq 1}$  is unique. This follows from the almost sure uniqueness of the minimizing curve joining  $\gamma_0$  to  $\gamma_1$ , where  $(\gamma_0, \gamma_1)$  is the optimal coupling. (Corollary 7.23 applies when the total cost is finite; but even if the total cost is infinite, we can apply a reasoning similar to the one in Corollary 7.23. Note that the result does not follow from the vol  $\otimes$  vol  $(dx_0 dx_1)$ -uniqueness of the minimizing curve joining  $x_0$  to  $x_1$ .

5. Without loss of generality, one might assume that

$$
\phi(y) = \inf_{x \in M} \left[ \psi(x) + c(x, y) \right]
$$

(these are true supremum and true infimum, not just up to a negligible set). One can also assume without loss of generality that

$$
\forall x, y \in M, \qquad \phi(y) - \psi(x) \le c(x, y)
$$

and

$$
\phi(x_1) - \psi(x_0) = c(x_0, x_1) \quad \text{almost surely.}
$$

**6.** It is still possible that two minimizing curves meet at time  $t = 0$ or  $t = 1$ , but this event may occur only on a very small set, of dimension at most  $n-1$ .

7. All of the above remains true if one replaces  $\mu_0$  at time 0 by  $\mu_t$ at time t, with obvious changes of notation (e.g. replace  $c = c^{0,1}$  by  $c^{t,1}$ ); the function  $\phi$  is unchanged, but now  $\psi$  should be changed into  $\psi_t$  defined by

$$
\psi_t(y) = \inf_{x \in M} \left[ \psi_0(x) + c^{0,t}(x, y) \right]. \tag{13.3}
$$

This  $\psi_t$  is a (viscosity) solution of the forward Hamilton–Jacobi equation

$$
\partial_t \psi_t + L^*(x, \nabla \psi_t(x), t) = 0.
$$

**8.** The equation for the optimal transport  $T_t$  between  $\mu_0$  and  $\mu_t$  is as follows:  $T_t(x)$  is the solution at time t of the Euler–Lagrange equation starting from  $x$  with velocity

$$
v_0(x) = (\nabla_v L(x, \cdot, 0))^{-1} (\nabla \psi(x)).
$$
\n(13.4)

In particular,

- For the quadratic cost on a Riemannian manifold  $M, T_t(x) =$  $\exp_x(t\nabla\psi(x))$ : To obtain  $T_t$ , flow for time t along a geodesic starting at x with velocity  $\nabla \psi(x)$  (or rather  $\nabla \psi(x)$  if nothing is known about the behavior of  $M$  at infinity);
- For the quadratic cost in  $\mathbb{R}^n$ ,  $T_t(x) = (1-t)x + t \nabla \Psi(x)$ , where  $\Psi(x) = |x|^2/2 + \psi(x)$  defines a lower semicontinuous convex function in the usual sense. In particular, the optimal transport from  $\mu_0$  to  $\mu_1$  is a gradient of convex function, and this property characterizes it uniquely among all admissible transports.

**9.** Whenever  $0 \le t_0 < t_1 \le 1$ ,

$$
\int \psi_{t_1} d\mu_{t_1} - \int \psi_{t_0} d\mu_{t_0} = C^{t_0, t_1}(\mu_{t_0}, \mu_{t_1})
$$

$$
= \int_{t_0}^{t_1} \int L(x, [(\nabla_v L)(x, \cdot, t)]^{-1} (\nabla \psi_t(x)), t) d\mu_t(x) dt;
$$

recall indeed Theorems 7.21 and 7.36, Remarks 7.25 and 7.37, and (13.4).

Simple as they may seem by now, these statements summarize years of research. If the reader has understood them well, then he or she is ready to go on with the rest of this course. The picture is not really complete and some questions remain open, such as the following:

**Open Problem 13.1.** If the initial and final densities,  $\rho_0$  and  $\rho_1$ , are positive everywhere, does this imply that the intermediate densities  $\rho_t$ are also positive? Otherwise, can one identify simple sufficient conditions for the density of the displacement interpolant to be positive everywhere?

For general Lagrangian actions, the answer to this question seems to be negative, but it is not clear that one can also construct counterexamples for, say, the basic quadratic Lagrangian. My personal guess would be that the answer is about the same as for the smoothness: Positivity of the displacement interpolant is in general false except maybe for some particular manifolds satisfying an adequate structure condition.

#### Standard approximation procedure

In this last section I have gathered two useful approximation results which can be used in problems where the probability measures are either noncompactly supported, or singular.

In Chapter 10 we have seen how to treat the Monge problem in noncompact situations, without any condition at infinity, thanks to the notion of approximate differentiability. However, in practice, to treat noncompact situations, the simplest solution is often to use again a truncation argument similar to the one used in the proof of approximate differentiability. The next proposition displays the main scheme that one can use to deal with such situations.

Proposition 13.2 (Standard approximation scheme). Let M be a smooth complete Riemannian manifold, let  $c = c(x, y)$  be a cost function associated with a Lagrangian  $L(x, v, t)$  on  $TM \times [0, 1]$ , satisfying the classical conditions of Definition 7.6; and let  $\mu_0$ ,  $\mu_1$  be two probability measures on M. Let  $\pi$  be an optimal transference plan between  $\mu_0$  and  $\mu_1$ , let  $(\mu_t)_{0 \leq t \leq 1}$  be a displacement interpolation and let  $\Pi$  be a dynamical optimal transference plan such that  $(e_0, e_1)_\# \Pi = \pi$ ,  $(e_t)_\# \Pi = \mu_t$ . Let  $\Gamma$  be the set of all action-minimizing curves, equipped with the topology of uniform convergence; and let  $(K_{\ell})_{\ell \in \mathbb{N}}$  be a sequence of compact sets in  $\Gamma$ , such that  $\Pi[UK_{\ell}] = 1$ . For  $\ell$  large enough,  $\Pi[K_{\ell}] > 0$ ; then define

$$
Z_{\ell} := \Pi[K_{\ell}]; \qquad \Pi_{\ell} := \frac{1_{K_{\ell}} \Pi}{Z_{\ell}};
$$
$$
\mu_{t,\ell} := (e_t)_{\#} \Pi_{\ell}; \qquad \pi_{\ell} := (e_0, e_1)_{\#} \Pi_{\ell};
$$

and let  $c_{\ell}$  be the restriction of c to  $\text{proj}_{M\times M}(K_{\ell})$ . Then for each  $\ell$ ,  $(\mu_{t,\ell})_{0 \leq t \leq 1}$  is a displacement interpolation and  $\pi_{\ell}$  is an associated optimal transference plan;  $\mu_{t,\ell}$  is compactly supported, uniformly in  $t \in [0, 1]$ ; and the following monotone convergences hold true:

 $Z_{\ell} \uparrow 1; \qquad Z_{\ell} \pi_{\ell} \uparrow \pi; \qquad Z_{\ell} \mu_{t,\ell} \uparrow \mu_t; \qquad Z_{\ell} \Pi_{\ell} \uparrow \Pi.$ 

If moreover  $\mu_0$  is absolutely continuous, then there exists a c-convex function  $\psi : M \to \mathbb{R} \cup \{+\infty\}$  such that  $\pi$  is concentrated on the graph of the transport  $T: x \to (\nabla_x c)^{-1}(x, -\nabla \psi(x))$ . For any  $\ell$ ,  $\mu_{0,\ell}$  is absolutely continuous, and the optimal transference plan  $\pi_{\ell}$  is deterministic. Furthermore, there is a  $c_{\ell}$ -convex function  $\psi_{\ell}$  such that  $\psi_{\ell}$  coincides with

 $\psi$  everywhere on  $C_{\ell} := \text{proj}_{M}(\text{Spt}(\pi_{\ell}))$ ; and there is a set  $Z_{\ell}$  such that vol  $[Z_{\ell}] = 0$  and for any  $x \in C_{\ell} \setminus Z_{\ell}$ ,  $\nabla \psi(x) = \nabla \psi_{\ell}(x)$ .

Still under the assumption that  $\mu_0$  is absolutely continuous, the measures  $\mu_{t,\ell}$  are also absolutely continuous, and the optimal transport  $T_{t_0\to t,\ell}$  between  $\mu_{t_0,\ell}$  and  $\mu_{t,\ell}$  is deterministic, for any given  $t_0 \in [0,1)$ and  $t \in [0,1]$ . In addition,

$$
T_{t_0 \to t, \ell} = T_{t_0 \to t}, \qquad \mu_{t_0, \ell} \text{-almost surely},
$$

where  $T_{t_0 \to t}$  is the optimal transport from  $\mu_{t_0}$  to  $\mu_t$ .

Proof of Proposition 13.2. The proof is quite similar to the argument used in the proof of uniqueness in Theorem 10.42 in a time-independent context. It is no problem to make this into a time-dependent version, since displacement interpolation behaves well under restriction, recall Theorem 7.30. The last part of the theorem follows from the fact that the map  $T_{t_0 \to t,\ell}$  can be written as  $\gamma_{t_0} \mapsto \gamma_t$ . — ∏

Remark 13.3. Proposition 13.2 will be used several times throughout this course, for instance in Chapter 17. Its main drawback is that there is absolutely no control of the smoothness of the approximations: Even if the densities  $\rho_0$  and  $\rho_1$  are smooth, the approximate densities  $\rho_{0,\ell}$  and  $\rho_{1,\ell}$  will in general be discontinuous. In the proof of Theorem 23.14 in Chapter 23, I shall use another approximation scheme which respects the smoothness, but at the price of a loss of control on the approximation of the transport.

Let us now turn to the problem of approximating singular transport problems by smooth ones. If  $\mu_0$  and  $\mu_1$  are singular, there is a priori no uniqueness of the optimal transference plans, and actually there might be a large number (possibly uncountable) of them. However, the next theorem shows that singular optimal transference plans can always be approximated by nice ones.

Theorem 13.4 (Regularization of singular transport problems). Let M be a smooth complete Riemannian manifold, and  $c: M \times M \to \mathbb{R}$ be a cost function induced by a Lagrangian  $L(x, v, t)$  satisfying the classical conditions of Definition 7.6. Further, let  $\mu_0$  and  $\mu_1$  be two probability measures on M, such that the optimal transport cost between  $\mu_0$ and  $\mu_1$  is finite, and let  $\pi$  be an optimal transference plan between  $\mu_0$ and  $\mu_1$ . Then there are sequences  $(\mu_0^k)_{k \in \mathbb{N}}$ ,  $(\mu_1^k)_{k \in \mathbb{N}}$  and  $(\pi^k)_{k \in \mathbb{N}}$  such that

(i) each  $\pi^k$  is an optimal transference plan between  $\mu_0^k$  and  $\mu_1^k$ , and any one of the probability measures  $\mu_0^k$ ,  $\mu_1^k$  has a smooth, compactly supported density;

(ii)  $\mu_0^k \to \mu_0$ ,  $\mu_1^k \to \mu_1$ ,  $\pi^k \to \pi$  in the weak sense as  $k \to \infty$ .

Proof of Theorem 13.4. By Theorem 7.21, there exists a displacement interpolation  $(\mu_t)_{0 \leq t \leq 1}$  between  $\mu_0$  and  $\mu_1$ ; let  $(\gamma_t)_{0 \leq t \leq 1}$  be such that  $\mu_t = \text{law}(\gamma_t)$ . The assumptions on L imply that action-minimizing curves solve a differential equation with Lipschitz coefficients, and therefore are uniquely determined by their initial position and velocity, a fortiori by their restriction to some time-interval  $[0, t_0]$ . So for any  $t_0 \in (0, 1/2)$ , by Theorem 7.30(ii),  $(\gamma_{t_0}, \gamma_{1-t_0})$  is the unique optimal coupling between  $\mu_{t_0}$  and  $\mu_{1-t_0}$ . Now it is easy to construct a sequence  $(\mu_{t_0}^k)_{k \in \mathbb{N}}$  such that  $\mu_{t_0}^k$  converges weakly to  $\mu_{t_0}$  as  $k \to \infty$ , and each  $\mu_{t_0}^k$  is compactly supported with a smooth density. (To construct such a sequence, first truncate to ensure the property of compact support, then localize to charts by a partition of unity, and apply a regularization in each chart.) Similarly, construct a sequence  $(\mu_{1-t_0}^k)_{k \in \mathbb{N}}$  such that  $\mu_{1-t_0}^k$  converges weakly to  $\mu_{1-t_0}$ , and each  $\mu_{1-t_0}^k$  is compactly supported with a smooth density. Let  $\pi_{t_0,1-t_0}^k$  be the unique optimal transference plan between  $\mu_{t_0}$  and  $\mu_{1-t_0}$ . By stability of optimal transport (Theorem 5.20),  $\pi_{t_0,1-t_0}^k$  converges as  $k \to \infty$  to  $\pi_{t_0,1-t_0} = \text{law}(\gamma_{t_0}, \gamma_{1-t_0})$ . Then by continuity of  $\gamma$ , the random variable  $(\gamma_{t_0}, \gamma_{1-t_0})$  converges pointwise to  $(\gamma_0, \gamma_1)$  as  $t_0 \to 0$ , which implies that  $\pi_{t_0, 1-t_0}$  converges weakly to  $\pi$ . The conclusion follows by choosing  $t_0 = 1/n$ ,  $k = k(n)$ large enough. ⊓⊔

### Equations of displacement interpolation

In Chapter 7, we understood that a curve  $(\mu_t)_{0 \leq t \leq 1}$  obtained by displacement interpolation solves an action minimization problem in the space of measures, and we wondered whether we could obtain some nice equations for these curves. Here now is a possible answer. For simplicity I shall assume that there is enough control at infinity, that the notion of approximate differentiability can be dispensed with (this is the case for instance if  $M$  is compact).

Consider a displacement interpolation  $(\mu_t)_{0 \leq t \leq 1}$ . By Theorem 7.21,  $\mu_t$  can be seen as the law of  $\gamma_t$ , where the random path  $(\gamma_t)_{0 \leq t \leq 1}$  satisfies the Euler–Lagrange equation  $(13.1)$ , and so at time  $t$  has velocity  $\xi_t(\gamma_t)$ , where  $\xi_t(x) := (\nabla_v L(x, \cdot, t))^{-1} (\nabla \psi_t(x))$ . By the formula of conservation of mass,  $\mu_t$  satisfies

$$
\frac{\partial \mu_t}{\partial t} + \nabla \cdot (\xi_t \,\mu_t) = 0
$$

in the sense of distributions (be careful:  $\xi_t$  is not necessarily a gradient, unless  $L$  is quadratic). Then we can write down the **equations of** displacement interpolation:

$$
\begin{cases}
\frac{\partial \mu_t}{\partial t} + \nabla \cdot (\xi_t \,\mu_t) = 0; \\
\nabla_v L(x, \xi_t(x), t) = \nabla \psi_t(x); \\
\psi_0 \text{ is } c\text{-convex}; \\
\partial_t \psi_t + L^*(x, \nabla \psi_t(x), t) = 0.
\end{cases}
\qquad (13.5)
$$

If the cost function is just the square of the distance, then these equations become

$$
\begin{cases}
\frac{\partial \mu_t}{\partial t} + \nabla \cdot (\xi_t \mu_t) = 0; \\
\xi_t(x) = \nabla \psi_t(x); \\
\psi_0 \text{ is } d^2/2\text{-convex}; \\
\partial_t \psi_t + \frac{|\nabla \psi_t|^2}{2} = 0.
\end{cases}
$$

(13.6)

Finally, for the square of the Euclidean distance, this simplifies into

$$
\frac{\partial \mu_t}{\partial t} + \nabla \cdot (\xi_t \mu_t) = 0;
$$

$$
\xi_t(x) = \nabla \psi_t(x);
$$
 \$x \to \frac{|x|^2}{2} + \psi\_0(x)\$ is lower semicontinuous convex;

$$
\partial_t \psi_t + \frac{|\nabla \psi_t|^2}{2} = 0.
$$
(13.7)

Apart from the special choice of initial datum, the latter system is well-known in physics as the pressureless Euler equation, for a potential velocity field.

### Quadratic cost function

In a context of Riemannian geometry, it is natural to focus on the quadratic Lagrangian cost function, or equivalently on the cost function  $c(x, y) = d(x, y)^2$ , and consider the Wasserstein space  $P_2(M)$ . This will be the core of all the transport proofs in Part II of this course, so a key role will be played by  $d^2/2$ -convex functions (that is, c-convex functions for  $c = d^2/2$ . In Part III we shall consider metric structures that are not Riemannian, but still the square of the distance will be the only cost function. So in the remainder of this chapter I shall focus on that particular cost.

The class of  $d^2/2$ -convex functions might look a bit mysterious, and if they are so important it would be good to have simple characterizations of them. If  $\psi$  is  $d^2/2$ -convex, then  $z \to \psi(z) + d(z,y)^2/2$  should have a minimum at x when  $y = \exp_x(\nabla \psi(x))$ . If in addition  $\psi$  is twice differentiable at  $x$ , then necessarily

$$
\nabla^2 \psi(x) \ge -\nabla^2 \left[ \frac{d(\cdot \cdot, \exp_x \nabla \psi(x))^2}{2} \right](x).
$$
 (13.8)

However, this is only a necessary condition, and it is not clear that it would imply  $d^2/2$ -convexity, except for a manifold satisfying the very strong curvature condition  $\mathfrak{S}_{d^2/2} \geq 0$  as in Theorem 12.46.

On the other hand, there is a simple and useful general criterion according to which sufficiently small functions are  $d^2/2$ -convex. This statement will guarantee in particular that any tangent vector  $v \in TM$ can be represented as the gradient of a  $d^2/2$ -convex function.

Theorem 13.5 ( $C^2$ -small functions are  $d^2/2$ -convex). Let M be a Riemannian manifold, and let  $K$  be a compact subset of  $M$ . Then, there is  $\varepsilon > 0$  such that any function  $\psi \in C_c^2(M)$  satisfying

$$
\operatorname{Spt}(\psi) \subset K, \qquad \|\psi\|_{C_b^2} \le \varepsilon
$$

is  $d^2/2$ -convex.

**Example 13.6.** Let  $M = \mathbb{R}^n$ , then  $\psi$  is  $d^2/2$ -convex if  $\nabla^2 \psi \ge -I_n$ . (In this particular case there is no need for compact support, and a one-sided bound on the second derivative is sufficient.)

*Proof of Theorem 13.5.* Let  $(M, q)$  be a Riemannian manifold, and let K be a compact subset of M. Let  $K' = \{x \in M; d(x,K) \leq 1\}$ . For

any  $y \in M$ , the Hessian of  $x \to d(x,y)^2/2$  is equal to  $I_n$  (or, more rigorously, to the identity on  $T_xM$ ) at  $x=y$ ; so by compactness one may find  $\delta > 0$  such that the Hessian of  $x \to d(x, y)^2/2$  remains larger than  $I_n/2$  as long as y stays in K' and  $d(x,y) < 2\delta$ . Without loss of generality,  $\delta < 1/2$ .

Now let  $\psi$  be supported in K, and such that

$$
\forall x \in M \qquad |\psi(x)| < \frac{\delta^2}{4}, \quad |\nabla^2 \psi(x)| < \frac{1}{4};
$$

write

$$
f_y(x) = \psi(x) + \frac{d(x, y)^2}{2}
$$

and note that  $\nabla^2 f_y \geq I_n/4$  in  $B_{2\delta}(y)$ , so  $f_y$  is uniformly convex in that ball.

If  $y \in K'$  and  $d(x, y) \ge \delta$ , then obviously  $f_y(x) \ge \delta^2/4 > \psi(y) =$  $f_y(y)$ ; so the minimum of  $f_y$  can be achieved only in  $B_\delta(y)$ . If there are two distinct such minima, say  $x_0$  and  $x_1$ , then we can join them by a geodesic  $(\gamma_t)_{0 \leq t \leq 1}$  which stays within  $B_{2\delta}(y)$  and then the function  $t \to f_y(\gamma_t)$  is uniformly convex (because  $f_y$  is uniformly convex in  $B_{2\delta}(y)$ , and minimum at  $t = 0$  and  $t = 1$ , which is impossible.

If  $y \notin K'$ , then  $\psi(x) \neq 0$  implies  $d(x, y) \geq 1$ , so  $f_y(x) \geq (1/2) - \delta^2/4$ , while  $f_y(y) = 0$ . So the minimum of  $f_y$  can only be achieved at x such that  $\psi(x) = 0$ , and it has to be at  $x = y$ .

In any case,  $f_y$  has exactly one minimum, which lies in  $B_\delta(y)$ . We shall denote it by  $x = T(y)$ , and it is characterized as the unique solution of the equation

$$
\nabla \psi(x) + \nabla_x \left( \frac{d(x, y)^2}{2} \right) = 0,\tag{13.9}
$$

where  $x$  is the unknown.

Let x be arbitrary in M, and  $y = \exp_x(\nabla \psi(x))$ . Then (as a consequence of the first variation formula),  $\nabla_x[d(x,y)^2/2] = -\nabla\psi(x)$ , so equation (13.9) holds true, and  $x = T(y)$ . This means that, with the notation  $c(x,y) = d(x,y)^2/2$ , one has  $\psi^c(y) = \psi(x) + c(x,y)$ . Then  $\psi^{cc}(x) = \sup[\psi^{c}(y) - c(x, y)] \ge \psi(x)$ . Since x is arbitrary, actually we have shown that  $\psi^{cc} \geq \psi$ ; but the converse inequality is always true, so  $\psi^{cc} = \psi$ , and then  $\psi$  is *c*-convex. □

Remark 13.7. The end of the proof took advantage of a general principle, independent of the particular cost  $c$ : If there is a *surjective* map T such that  $f_y: x \to \psi(x) + c(x, y)$  is minimum at  $T(y)$ , then  $\psi$  is c-convex.

## The structure of $P_2(M)$

A striking discovery made by Otto at the end of the nineties is that the differentiable structure on a Riemannian manifold M induces a kind of differentiable structure in the space  $P_2(M)$ . This idea takes substance from the following remarks: All of the path  $(\mu_t)_{0 \leq t \leq 1}$  is determined from the initial velocity field  $\xi_0(x)$ , which in turn is determined by  $\nabla \psi$ as in (13.4). So it is natural to think of the function  $\nabla \psi$  as a kind of "initial velocity" for the path  $(\mu_t)$ . The conceptual shift here is about the same as when we decided that  $\mu_t$  could be seen either as the law of a random minimizing curve at time  $t$ , or as a path in the space of measures: Now we decide that  $\nabla \psi$  can be seen either as the field of the initial velocities of our minimizing curves, or as the (abstract) velocity of the path  $(\mu_t)$  at time  $t = 0$ .

There is an abstract notion of tangent space  $T_x\mathcal{X}$  (at point x) to a metric space  $(\mathcal{X}, d)$ : in technical language, this is the *pointed Gromov*-Hausdorff limit of the rescaled space. It is a rather natural notion: fix your point  $x$ , and zoom onto it, by multiplying all distances by a large factor  $\varepsilon^{-1}$ , while keeping x fixed. This gives a new metric space  $\mathcal{X}_{x,\varepsilon}$ , and if one is not too curious about what happens far away from  $x$ , then the space  $\mathcal{X}_{x,\varepsilon}$  might converge in some nice sense to some limit space, that may not be a vector space, but in any case is a cone. If that limit space exists, it is said to be the tangent space (or tangent cone) to  $\mathcal{X}$ at x. (I shall come back to these issues in Part III.)

In terms of that construction, the intuition sketched above is indeed correct: let  $P_2(M)$  be the metric space consisting of probability measures on M, equipped with the Wasserstein distance  $W_2$ . If  $\mu$  is absolutely continuous, then the tangent cone  $T_{\mu}P_2(M)$  exists and can be identified isometrically with the closed vector space generated by  $d^2/2$ -convex functions  $\psi$ , equipped with the norm

$$
\|\nabla\psi\|_{L^2(\mu;TM)} := \left(\int_M |\nabla\psi|_x^2 d\mu(x)\right)^{1/2}
$$

.

Actually, in view of Theorem 13.5, this is the same as the vector space generated by all smooth, compactly supported gradients, completed with respect to that norm.

With what we know about optimal transport, this theorem is not that hard to prove, but this would require a bit too much geometric machinery for now. Instead, I shall spend some time on an important related result by Ambrosio, Gigli and Savaré, according to which any Lipschitz curve in the space  $P_2(M)$  admits a velocity (which for all t lives in the tangent space at  $\mu_t$ ). Surprisingly, the proof will not require absolute continuity.

### Theorem 13.8 (Representation of Lipschitz paths in $P_2(M)$ ).

Let M be a smooth complete Riemannian manifold, and let  $P_2(M)$  be the metric space of all probability measures on  $M$ , with a finite second moment, equipped with the metric  $W_2$ . Further, let  $(\mu_t)_{0 \le t \le 1}$  be a Lipschitz-continuous path in  $P_2(M)$ :

$$
W_2(\mu_s, \mu_t) \leq L |t - s|.
$$

For any  $t \in [0,1]$ , let  $H_t$  be the Hilbert space generated by gradients of continuously differentiable, compactly supported  $\psi$ :

$$
H_t := \overline{\mathrm{Vect}\big(\{\nabla\psi; \ \psi \in C_c^1(M)\}\big)}^{L^2(\mu_t; TM)}.
$$

Then there exists a measurable vector field  $\xi_t(x) \in L^{\infty}(dt; L^2(d\mu_t(x))),$  $\mu_t(dx)$  dt-almost everywhere unique, such that  $\xi_t \in H_t$  for all t (i.e. the velocity field really is tangent along the path), and

$$
\partial_t \mu_t + \nabla \cdot (\xi_t \mu_t) = 0 \tag{13.10}
$$

in the weak sense.

Conversely, if the path  $(\mu_t)_{0 \leq t \leq 1}$  satisfies (13.10) for some measurable vector field  $(\xi_t(x))$  whose  $L^2(\mu_t)$ -norm is bounded by L, almost surely in t, then  $(\mu_t)$  is a Lipschitz-continuous curve with  $\|\mu\| \leq L$ .

The proof of Theorem 13.8 requires some analytical tools, and the reader might skip it at first reading.

*Proof of Theorem 13.8.* Let  $\psi : M \to \mathbb{R}$  be a  $C^1$  function, with Lipschitz constant at most 1. For all  $s < t$  in [0, 1],

$$
\left| \int_M \psi \, d\mu_t - \int_M \psi \, d\mu_s \right| \le W_1(\mu_s, \mu_t) \le W_2(\mu_s, \mu_t). \tag{13.11}
$$

In particular,  $\zeta(t) := \int_M \psi \, d\mu_t$  is a Lipschitz function of t. By Theorem 10.8(ii), the time-derivative of  $\zeta$  exists for almost all times  $t \in [0, 1]$ . Then let  $\pi_{s,t}$  be an optimal transference plan between  $\mu_s$  and  $\mu_t$  (for the squared distance cost function). Let

$$
\Psi(x,y) := \begin{cases} \frac{|\psi(x) - \psi(y)|}{d(x,y)} & \text{if } x \neq y \\ |\nabla \psi(x)| & \text{if } x = y. \end{cases}
$$

Obviously  $\Psi$  is bounded by 1, and moreover it is upper semicontinuous.

If t is a differentiability point of  $\zeta$ , then

$$
\left| \frac{d}{dt} \int \psi \, d\mu_t \right| \leq \liminf_{\varepsilon \downarrow 0} \frac{1}{\varepsilon} \left| \int \psi \, d\mu_t - \int \psi \, d\mu_{t+\varepsilon} \right|
$$

$$
\leq \liminf_{\varepsilon \downarrow 0} \frac{1}{\varepsilon} \int |\psi(y) - \psi(x)| \, d\pi_{t,t+\varepsilon}(x,y) \leq \left( \liminf_{\varepsilon \downarrow 0} \sqrt{\int \Psi(x,y)^2 \, d\pi_{t,t+\varepsilon}(x,y)} \right) \frac{\sqrt{\int d(x,y)^2 \, d\pi_{t,t+\varepsilon}(x,y)}}{\varepsilon}
$$

$$
= \left( \liminf_{\varepsilon \downarrow 0} \sqrt{\int \Psi(x,y)^2 \, d\pi_{t,t+\varepsilon}(x,y)} \right) \left( \frac{W_2(\mu_t, \mu_{t+\varepsilon})}{\varepsilon} \right)
$$

$$
\leq \left( \liminf_{\varepsilon \downarrow 0} \sqrt{\int \Psi(x,y)^2 \, d\pi_{t,t+\varepsilon}(x,y)} \right) L.
$$

Since  $\Psi$  is upper semicontinuous and  $\pi_{t,t+\varepsilon}$  converges weakly to  $\delta_{x=y}$ (the trivial transport plan where nothing moves) as  $\varepsilon \downarrow 0$ , it follows that

$$
\left| \frac{d}{dt} \int \psi \, d\mu_t \right| \leq L \sqrt{\int |\Psi(x, x)|^2 \, d\mu_t(x)}
$$
$$
= L \sqrt{\int |\nabla \psi(x)|^2 \, d\mu_t(x)}.
$$

Now the key remark is that the time-derivative  $(d/dt) \int (\psi + C) d\mu_t$ does not depend on the constant C. This shows that  $\left(\frac{d}{dt}\right) \int \psi \, d\mu_t$ really is a functional of  $\nabla \psi$ , obviously linear. The above estimate shows that this functional is *continuous* with respect to the norm in  $L^2(d\mu_t)$ .

Actually, this is not completely rigorous, since this functional is only defined for almost all t, and "almost all" here might depend on  $\psi$ . Here is a way to make things rigorous: Let  $\mathcal L$  be the set of all Lipschitz functions  $\psi$  on M with Lipschitz constant at most 1, such that, say,  $\psi(x_0) = 0$ , where  $x_0 \in M$  is arbitrary but fixed once for all, and  $\psi$  is supported in a fixed compact  $K \subset M$ . The set  $\mathcal L$  is compact in the norm of uniform convergence, and contains a dense sequence  $(\psi_k)_{k\in\mathbb{N}}$ . By a regularization argument, one can assume that all those functions are actually of class  $C^1$ . For each  $\psi_k$ , we know that  $\int \psi_k d\mu_t$  is differentiable for almost all  $t \in [0,1]$ ; and since there are only countably many  $\zeta_k$ 's, we know that for almost every t, each  $\zeta_k$  is differentiable at time t. The map  $(d/dt)$   $\int \alpha d\mu_t$  is well-defined at each of these times t, for all  $\alpha$  in the vector space  $H_t$  generated by all the  $\psi_k$ 's, and it is continuous if that vector space is equipped with the  $L^2(d\mu_t)$  norm. It follows from the Riesz representation theorem that for each differentiability time  $t$ there exists a unique vector  $\xi_t \in H_t \subset L^2(d\mu_t)$ , with norm at most L, such that

$$
\frac{d}{dt} \int \psi \, d\mu_t = \int \xi_t \cdot \nabla \psi \, d\mu_t. \tag{13.12}
$$

This identity should hold true for any  $\psi_k$ , and by density it should also hold true for any  $\psi \in C^1(M)$ , supported in K.

Let  $C_K^1(M)$  be the set of  $\psi \in C^1(M)$  that are supported in K. We just showed that there is a negligible set of times,  $\tau_K$ , such that (13.12) holds true for all  $\psi \in C_K^1(M)$  and  $t \notin \tau_K$ . Now choose an increasing family of compact sets  $(K_m)_{m\in\mathbb{N}}$ , with  $\cup K_m = M$ , so that any compact set is included in some  $K_m$ . Then (13.12) holds true for all  $\psi \in C_c^1(M)$ , as soon as t does not belong to the union of  $\tau_{K_m}$ , which is still a negligible set of times.

But equation (13.12) is really the weak formulation of (13.10). Since  $\xi_t$  is uniquely determined in  $L^2(d\mu_t)$ , for almost all t, actually the vector field  $\xi_t(x)$  is  $d\mu_t(x) dt$ -uniquely determined.

To conclude the proof of the theorem, it only remains to prove the converse implication. Let  $(\mu_t)$  and  $(\xi_t)$  solve (13.10). By the equation of conservation of mass,  $\mu_t = \text{law}(\gamma_t)$ , where  $\gamma_t$  is a (random) solution of

$$
\dot{\gamma}_t = \xi_t(\gamma_t).
$$

Let  $s < t$  be any two times in [0, 1]. From the formula

$$
d(\gamma_s, \gamma_t)^2 = (t - s) \inf \left\{ \int_s^t |\dot{\zeta}_\tau|^2 d\tau; \quad \zeta_s = \gamma_s, \ \zeta_t = \gamma_t \right\},\
$$

we deduce

$$
d(\gamma_s, \gamma_t)^2 \le (t-s) \int_s^t |\dot{\gamma}_\tau|^2 d\tau \le (t-s) \int_s^t |\xi_t(\gamma_t)|^2 d\tau.
$$

So

$$
\mathbb{E} d(\gamma_s, \gamma_t)^2 \le (t-s) \int_s^t |\xi_\tau(x)|^2 d\mu_\tau(x) d\tau \le (t-s)^2 \|\xi\|_{L^\infty(dt; L^2(d\mu_t))}.
$$

In particular

$$
W_2(\mu_s, \mu_t)^2 \leq \mathbb{E} d(\gamma_s, \gamma_t)^2 \leq L^2(t-s)^2
$$
,

where L is an upper bound for the norm of  $\xi$  in  $L^{\infty}(L^2)$ . This concludes the proof of Theorem 13.8. □

Remark 13.9. With hardly any more work, the preceding theorem can be extended to cover paths that are absolutely continuous of order 2, in the sense defined on p. 127. Then of course the velocity field will not live in  $L^{\infty}(dt; L^2(d\mu_t)),$  but in  $L^2(d\mu_t dt)$ .

Observe that in a displacement interpolation, the initial measure  $\mu_0$ and the initial velocity field  $\nabla \psi_0$  uniquely determine the final measure  $\mu_1$ : this implies that *geodesics in*  $P_2(M)$  are nonbranching, in the strong sense that their initial position and velocity determine uniquely their final position.

Finally, we can now derive an "explicit" formula for the action functional determining displacement interpolations as minimizing curves. Let  $\mu = (\mu_t)_{0 \leq t \leq 1}$  be any Lipschitz (or absolutely continuous) path in  $P_2(M)$ ; let  $\xi_t(x) = \nabla \psi_t(x)$  be the associated time-dependent velocity field. By the formula of conservation of mass,  $\mu_t$  can be interpreted as the law of  $\gamma_t$ , where  $\gamma$  is a random solution of  $\dot{\gamma}_t = \xi_t(\gamma_t)$ . Define

$$
\mathbb{A}(\mu) := \inf \int_0^1 \mathbb{E}_{\mu_t} |\xi_t(\gamma_t)|^2 \, dt,\tag{13.13}
$$

where the infimum is taken over all possible realizations of the random curves  $\gamma$ . By Fubini's theorem,

$$
\mathbb{A}(\mu) = \inf \mathbb{E} \int_0^1 |\xi_t(\gamma_t)|^2 dt = \inf \mathbb{E} \int_0^1 |\dot{\gamma}_t|^2 dt
$$
  
$$
\geq \mathbb{E} \inf \int_0^1 |\dot{\gamma}_t|^2 dt
$$
  
$$
= \mathbb{E} d(\gamma_0, \gamma_1)^2,
$$

and the infimum is achieved if and only if the coupling  $(\gamma_0, \gamma_1)$  is minimal, and the curves  $\gamma$  are (almost surely) action-minimizing. This shows that displacement interpolations are characterized as the minimizing curves for the action A. Actually A is the same as the action appearing in Theorem 7.21 (iii), the only improvement is that now we have produced a more explicit form in terms of vector fields.

The expression (13.13) can be made slightly more explicit by noting that the optimal choice of velocity field is the one provided by Theorem 13.8, which is a gradient, so we may restrict the action functional to gradient velocity fields:

$$
\mathbb{A}(\mu) := \int_0^1 \mathbb{E}_{\mu_t} |\nabla \psi_t|^2 dt; \qquad \frac{\partial \mu_t}{\partial t} + \nabla \cdot (\nabla \psi_t \mu_t) = 0. \tag{13.14}
$$

Note the formal resemblance to a Riemannian structure: What the formula above says is

$$
W_2(\mu_0, \mu_1)^2 = \inf \int_0^1 \|\dot{\mu}_t\|_{T_{\mu_t} P_2}^2 dt,
$$
\n(13.15)

where the norm on the tangent space  $T_{\mu}P_2$  is defined by

$$
\|\dot{\mu}\|_{T_{\mu}P_2}^2 = \inf \left\{ \int |v|^2 d\mu; \quad \dot{\mu} + \nabla \cdot (v\mu) = 0 \right\}
$$
$$
= \int |\nabla \psi|^2 d\mu; \quad \dot{\mu} + \nabla \cdot (\nabla \psi \mu) = 0.
$$

Remark 13.10. There is an appealing physical interpretation, which really is an infinitesimal version of the optimal transport problem. Imagine that you observe the (infinitesimal) evolution of the density of particles moving in a continuum, but don't know the actual velocities of these particles. There might be many velocity fields that are compatible with the observed evolution of density (many solutions of the continuity equation). Among all possible solutions, select the one with minimum kinetic energy. This energy is (up to a factor  $1/2$ ) the square norm of your infinitesimal evolution.

### Bibliographical notes

Formula (13.8) appears in [246]. It has an interesting consequence which can be described as follows: On a Riemannian manifold, the optimal transport starting from an absolutely continuous probability measure almost never hits the cut locus; that is, the set of points  $x$  such that the image  $T(x)$  belongs to the cut locus of x is of zero probability. Although we already know that almost surely, x and  $T(x)$  are joined by a unique geodesic, this alone does not imply that the cut locus is almost never hit, because it is possible that  $y$  belongs to the cut locus of  $x$  and still x and y are joined by a unique minimizing geodesic. (Recall the discussion after Problem 8.8.) But Cordero-Erausquin, McCann and Schmuckenschläger show that if such is the case, then  $d(x, z)^2/2$  fails to be semiconvex at  $z = y$ . On the other hand, by Alexandrov's second differentiability theorem (Theorem 14.1),  $\psi$  is twice differentiable almost everywhere; formula (13.8), suitably interpreted, says that  $d(x, \cdot)^2/2$ is semiconvex at  $T(x)$  whenever  $\psi$  is twice differentiable at x.

At least in the Euclidean case, and up to regularity issues, the explicit formulas for geodesic curves and action in the space of measures were known to Brenier, around the mid-nineties. Otto [669] took a conceptual step forward by considering formally  $P_2(M)$  as an infinitedimensional Riemannian manifold, in view of formula (13.15). For some time it was used as a purely formal, yet quite useful, heuristic method (as in [671], or later in Chapter 15). It is only recently that rigorous constructions were performed in several research papers, e.g. [30, 203, 214, 577]. The approach developed in this chapter relies heavily on the work of Ambrosio, Gigli and Savaré [30] (in  $\mathbb{R}^n$ ). A more geometric treatment can be found in [577, Appendix A]; see also [30, Section 12.4], and [655, Section 3] (I shall give a few more details in the bibliographical notes of Chapter 26). As I am completing this course, an important contribution to the subject has just been made by Lott [575] who established "explicit" formulas for the Riemannian connection and curvature in  $P_2^{\text{ac}}(M)$ , or rather in the subset made of smooth positive densities, when M is compact.

The pressureless Euler equations describe the evolution of a gas of particles which interact only when they meet, and then stick together (sticky particles). It is a very degenerate system whose study turns out to be tricky in general [169, 320, 745]. But in applications to optimal transport, it comes in the very particular case of potential flow (the

velocity field is a gradient), so the evolution is governed by a simple Hamilton–Jacobi equation.

There are two natural ways to extend a minimizing geodesic  $(\mu_t)_{0 \leq t \leq 1}$ in  $P_2(M)$  into a geodesic defined (but not necessarily minimizing) at all times. One is to solve the Hamilton–Jacobi equation for all times, in the viscosity sense; then the gradient of the solution will define a velocity field, and one can let  $(\mu_t)$  evolve by transport, as in (13.6). Another way [30, Example 11.2.9] is to construct a trajectory of the gradient flow of the energy  $-W_2(\sigma, \cdot)^2/2$ , where  $\sigma$  is, say,  $\mu_0$ , and the trajectory starts from an intermediate point, say  $\mu_{1/2}$ . The existence of this gradient flow follows from [30, Theorems  $10.4.12$  and  $11.2.1$ ], while [30, Theorem 11.2.1] guarantees that it coincides (up to time-reparameterization) with the original minimizing geodesic for short times. (This is close to the construction of quasigeodesics in [678].) It is natural to expect that both approaches give the same result, but as far as I know, this has not been established.

Khesin suggested to me the following nice problem. Let  $\mu = (\mu_t)_{t>0}$ be a geodesic in the Wasserstein space  $P_2(\mathbb{R}^n)$  (defined with the help of the pressureless Euler equation), and characterize the cut time of  $\mu$  as the "time of the first shock". If  $\mu_0$  is absolutely continuous with positive density, this essentially means the following: let  $t_c = \inf \{t_1;$  $(\mu_t)_{0 \le t \le t_1}$  is not a minimizing geodesic}, let  $t_s = \sup \{ t_0; \mu_t \text{ is also-} \}$ lutely continuous for  $t \leq t_0$ , and show that  $t_c = t_s$ . Since  $t_c$  should be equal to sup  $\{t_2; |x|^2/2 + t \psi(0, x)$  is convex for  $t \leq t_2\}$ , the solution of this problem is related to a qualitative study of the way in which convexity degenerates at the first shock. In dimension 1, this problem can be studied very precisely [642, Chapter 1] but in higher dimensions the problem is more tricky. Khesin and Misiotek obtain some results in this direction in [515].

Kloeckner [522] studied the isometries of  $P_2(\mathbb{R})$  and found that they are not all induced by isometries of  $\mathbb{R}$ ; roughly speaking, there is one additional "exotic" isometry.

In his PhD thesis, Agueh [4] studied the structure of  $P_p(M)$  for  $p > 1$  (not necessarily equal to 2). Ambrosio, Gigli and Savaré [30] pushed these investigations further.

Displacement interpolation becomes somewhat tricky in presence of boundaries. In his study of the porous medium equations, Otto [669] considered the case of a bounded open set of  $\mathbb{R}^n$  with  $C^2$  boundary.

For many years, the great majority of applications of optimal transport to problems of applied mathematics have taken place in Euclidean setting, but more recently some "genuinely Riemannian" applications have started to pop out. There was an original suggestion to use optimal transport in a three-dimensional Riemannian manifold (actually, a cube equipped with a varying metric) related to image perception and the matching of pictures with different contrasts [289]. In a meteorological context, it is natural to consider the sphere (as a model of the Earth), and in the study of the semi-geostrophic system one is naturally led to optimal transport on the sphere [263, 264]; actually, it is even natural to consider a conformal change of metric which "pinches" the sphere along its equator [263]! For completely different reasons, optimal transport on the sphere was recently used by Otto and Tzavaras [670] in the study of a coupled fluid-polymer model.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
Optimal transport and Riemannian geometry

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.

This second part is devoted to the exploration of Riemannian geometry through optimal transport. It will be shown that the geometry of a manifold influences the qualitative properties of optimal transport; this can be quantified in particular by the effect of Ricci curvature bounds on the convexity of certain well-chosen functionals along displacement interpolation. The first hints of this interplay between Ricci curvature and optimal transport appeared around 2000, in works by <PERSON> and myself, and shortly after by <PERSON><PERSON><PERSON><PERSON>, Mc<PERSON> and Schmuckenschlä<PERSON>.

Throughout, the emphasis will be on the quadratic cost (the transport cost is the square of the geodesic distance), with just a few exceptions. Also, most of the time I shall only handle measures which are absolutely continuous with respect to the Riemannian volume measure.

Chapter 14 is a preliminary chapter devoted to a short and tentatively self-contained exposition of the main properties of Ricci curvature. After going through this chapter, the reader should be able to understand all the rest without having to consult any extra source on Riemannian geometry. The estimates in this chapter will be used in Chapters 15, 16 and 17.

Chapter 15 presents a powerful formal differential calculus on the Wasserstein space, cooked up by Otto.

Chapters 16 and 17 establish the main relations between displacement convexity and Ricci curvature. Not only do Ricci curvature bounds imply certain properties of displacement convexity, but conversely these properties characterize Ricci curvature bounds. These results will play a key role in the rest of the course.

In Chapters 18 to 22 the main theme will be that many classical properties of Riemannian manifolds, that come from Ricci curvature estimates, can be conveniently derived from displacement convexity techniques. This includes in particular estimates about the growth of the volume of balls, Sobolev-type inequalities, concentration inequalities, and Poincaré inequalities.

Then in Chapter 23 it is explained how one can define certain *gradi*ent flows in the Wasserstein space, and recover in this way well-known equations such as the heat equation. In Chapter 24 some of the functional inequalities from the previous chapters are applied to the qualitative study of some of these gradient flows. Conversely, gradient flows provide alternative proofs to some of these inequalities, as shown in Chapter 25.

The issues discussed in this part are concisely reviewed in the surveys by Cordero-Erausquin [244] and myself [821] (both in French).

Convention: Throughout Part II, unless otherwise stated, a "Riemannian manifold" is a smooth, complete connected finite-dimensional Riemannian manifold distinct from a point, equipped with a smooth metric tensor.

Curvature is a generic name to designate a local invariant of a metric space that quantifies the deviation of this space from being Euclidean. (Here "local invariant" means a quantity which is invariant under local isometries.) It is standard to define and study curvature mainly on Riemannian manifolds, for in that setting definitions are rather simple, and the Riemannian structure allows for "explicit" computations. Throughout this chapter, M will stand for a complete connected Riemannian manifold, equipped with its metric  $q$ .

The most popular curvatures are: the **sectional curvature**  $\sigma$  (for each point x and each plane  $P \subset T_xM$ ,  $\sigma_x(P)$  is a number), the **Ricci** curvature Ric (for each point x,  $Ric<sub>x</sub>$  is a quadratic form on the tangent space  $T_xM$ ), and the **scalar curvature** S (for each point x,  $S_x$  is a number). All of them can be obtained by reduction of the Riemann curvature tensor. The latter is easy to define: If  $\nabla_X$  stands for the covariant derivation along the vector field  $X$ , then

$$
Riem(X,Y) := \nabla_Y \nabla_X - \nabla_X \nabla_Y + \nabla_{[X,Y]};
$$

but it is notoriously difficult, even for specialists, to get some understanding of its meaning. The Riemann curvature can be thought of as a tensor with four indices; it can be expressed in coordinates as a nonlinear function of the Christoffel symbols and their partial derivatives.

Of these three notions of curvature (sectional, Ricci, scalar), the sectional one is the most precise; in fact the knowledge of all sectional curvatures is equivalent to the knowledge of the Riemann curvature. The Ricci curvature is obtained by "tracing" the sectional curvature: If e is a given unit vector in  $T_xM$  and  $(e,e_2,\ldots,e_n)$  is an orthonormal basis of  $T_xM$ , then  $\text{Ric}_x(e,e) = \sum \sigma_x(P_j)$ , where  $P_j$   $(j = 2,...,n)$ 

is the plane generated by  $\{e, e_j\}$ . Finally, the scalar curvature is the trace of the Ricci curvature. So a control on the sectional curvature is stronger than a control on the Ricci curvature, which in turn is stronger than a control on the scalar curvature.

For a surface (manifold of dimension 2), these three notions reduce to just one, which is the Gauss curvature and whose definition is elementary. Let us first describe it from an extrinsic point of view. Let M be a two-dimensional submanifold of  $\mathbb{R}^3$ . In the neighborhood of a point x, choose a unit normal vector  $n = n(y)$ , then this defines locally a smooth map *n* with values in  $S^2 \subset \mathbb{R}^3$  (see Figure 14.1). The tangent spaces  $T_xM$  and  $T_{n(x)}S^2$  are parallel planes in  $\mathbb{R}^3$ , which can be identified unambiguously. So the determinant of the differential of  $n$ can also be defined without ambiguity, and this determinant is called the curvature. The fact that this quantity is invariant under isometries is one of Gauss's most famous results, a tour de force at the time. (To appreciate this theorem, the reader might try to prove it by elementary means.)

Image /page/5/Figure/3 description: The image displays a diagram illustrating a concept in differential geometry. On the left, a curved surface labeled 'M' is shown. A plane is depicted intersecting the surface at a point labeled 'x', with an arrow indicating a normal vector to the surface at that point. A dashed line, labeled 'n', connects this point to a sphere labeled 'S^2' on the right. A plane is also shown intersecting the sphere, with an arrow indicating a normal vector to the sphere at that intersection point.

Fig. 14.1. The dashed line gives the recipe for the construction of the Gauss map; its Jacobian determinant is the Gauss curvature.

As an illustration of this theorem: If you hold a sheet of paper straight, then its equation (as an embedded surface in  $\mathbb{R}^3$ , and assuming that it is infinite) is just the equation of a plane, so obviously it is not curved. Fine, but now bend the sheet of paper so that it looks like valleys and mountains, write down the horrible resulting equations, give it to a friend and ask him whether it is curved or not. One thing he can do is compute the Gauss curvature from your horrible equations, find that it is identically 0, and deduce that your surface was not curved

at all. Well, it looked curved as a surface which was embedded in  $\mathbb{R}^3$ , but from an intrinsic point of view it was not: A tiny creature living on the surface of the sheet, unable to measure the lengths of curves going outside of the surface, would never have noticed that you bent the sheet.

To construct isometries from  $(M, g)$  to something else, pick up any diffeomorphism  $\varphi : M \to M'$ , and equip  $M' = \varphi(M)$  with the metric  $g' = (\varphi^{-1})^* g$ , defined by  $g'_x(v) = g_{\varphi^{-1}(x)}(d_x\varphi^{-1}(v))$ . Then  $\varphi$  is an isometry between  $(M, g)$  and  $(M', g')$ . Gauss's theorem says that the curvature computed in  $(M, g)$  and the curvature computed in  $(M', g')$ are the same, modulo obvious changes (the curvature at point  $x$  along a plane P should be compared with the curvature at  $\varphi(x)$  along a plane  $d_x\varphi(P)$ . This is why one often says that the curvature is "invariant" under the action of diffeomorphisms".

Curvature is intimately related to the local behavior of geodesics. The general rule is that, in the presence of positive curvature, geodesics have a tendency to *converge* (at least for short times), while in the presence of negative curvature they have a tendency to diverge. This tendency can usually be felt only at second or third order in time: at first order, the convergence or divergence of geodesics is dictated by the initial conditions. So if, on a space of (strictly) positive curvature, you start two geodesics from the same point with velocities pointing in different directions, the geodesics will start to diverge, but then the tendency to diverge will diminish. Here is a more precise statement, which will show at the same time that the Gauss curvature is an intrinsic notion: From a point  $x \in M$ , start two constant-speed geodesics with unit speed, and respective velocities  $v, w$ . The two curves will spread apart; let  $\delta(t)$  be the distance between their respective positions at time t. In a first approximation,  $\delta(t) \simeq \sqrt{2(1 - \cos \theta)} t$ , where  $\theta$  is the angle between  $v$  and  $w$  (this is the same formula as in Euclidean space). But a more precise study shows that

$$
\delta(t) = \sqrt{2(1 - \cos \theta)} t \left( 1 - \frac{\kappa_x \cos^2(\theta/2)}{6} t^2 + O(t^4) \right), \quad (14.1)
$$

where  $\kappa_x$  is the Gauss curvature at x.

Once the intrinsic nature of the Gauss curvature has been established, it is easy to define the notion of sectional curvature for Riemannian manifolds of any dimension, embedded or not: If  $x \in M$  and  $P \subset T_xM$ , define  $\sigma_x(P)$  as the Gauss curvature of the surface which is obtained as the image of P by the exponential map  $\exp_x$  (that is, the

collection of all geodesics starting from  $x$  with a velocity in  $P$ ). Another equivalent definition is by reduction of the Riemann curvature tensor: If  $\{u, v\}$  is an orthonormal basis of P, then  $\sigma_x(P) = \langle \text{Riem}(u, v) \cdot u, v \rangle$ .

It is obvious from the first definition of Gauss curvature that the unit two-dimensional sphere  $S^2$  has curvature  $+1$ , and that the Euclidean plane  $\mathbb{R}^2$  has curvature 0. More generally, the sphere  $S^n(R)$ , of dimension n and radius R, has constant sectional curvature  $1/R^2$ ; while the ndimensional Euclidean space  $\mathbb{R}^n$  has curvature 0. The other classical example is the hyperbolic space, say  $\mathbb{H}^n(R) = \{(x, y) \in \mathbb{R}^{n-1} \times (0, +\infty)\}\$ equipped with the metric  $R^2(dx^2 + dy^2)/y^2$ , which has constant sectional curvature  $-1/R^2$ . These three families (spheres, Euclidean, hyperbolic) constitute the only simply connected Riemannian manifolds with constant sectional curvature, and they play an important role as comparison spaces.

The qualitative properties of optimal transport are also (of course) related to the behavior of geodesics, and so it is natural to believe that curvature has a strong influence on the solution of optimal transport. Conversely, some curvature properties can be read off on the solution of optimal transport. At the time of writing, these links have been best understood in terms of Ricci curvature; so this is the point of view that will be developed in the sequel.

This chapter is a tentative crash course on Ricci curvature. Hopefully, a reader who has never heard about that topic before should, by the end of the chapter, know enough about it to understand all the rest of the notes. This is by no means a complete course, since most proofs will only be sketched and many basic results will be taken for granted.

In practice, Ricci curvature usually appears from two points of view: (a) estimates of the Jacobian determinant of the exponential map; (b) **Bochner's formula**. These are two complementary points of view on the same phenomenon, and it is useful to know both. Before going on, I shall make some preliminary remarks about Riemannian calculus at second order, for functions which are not necessarily smooth.

## Preliminary: second-order differentiation

All curvature calculations involve second-order differentiation of certain expressions. The notion of covariant derivation lends itself well to those computations. A first thing to know is that the exchange of derivatives is still possible. To express this properly, consider a parametrized surface  $(s,t) \rightarrow \gamma(s,t)$  in M, and write  $d/dt$  (resp.  $d/ds$ ) for the differentiation along  $\gamma$ , viewed as a function of t with s frozen (resp. as a function of s with t frozen); and  $D/Dt$  (resp.  $D/Ds$ ) for the corresponding covariant differentiation. Then, if  $F \in C^2(M)$ , one has

$$
\frac{D}{Ds}\left(\frac{dF}{dt}\right) = \frac{D}{Dt}\left(\frac{dF}{ds}\right). \tag{14.2}
$$

Also a crucial concept is that of the **Hessian operator**. If  $f$  is twice differentiable on  $\mathbb{R}^n$ , its Hessian matrix is just  $(\partial^2 f/\partial x_i \partial x_j)_{1 \leq i,j \leq n}$ , that is, the array of all second-order partial derivatives. Now if  $f$  is defined on a Riemannian manifold  $M$ , the Hessian operator at  $x$  is the linear operator  $\nabla^2 f(x) : T_xM \to T_xM$  defined by the identity

$$
\nabla^2 f \cdot v = \nabla_v (\nabla f).
$$

(Recall that  $\nabla_v$  stands for the covariant derivation in the direction v.) In short,  $\nabla^2 f$  is the covariant gradient of the gradient of f.

A convenient way to compute the Hessian of a function is to differentiate it twice along a geodesic path. Indeed, if  $(\gamma_t)_{0 \leq t \leq 1}$  is a geodesic path, then  $\nabla_{\dot{\gamma}} \dot{\gamma} = 0$ , so

$$
\frac{d^2}{dt^2} f(\gamma_t) = \frac{d}{dt} \langle \nabla f(\gamma_t), \dot{\gamma}_t \rangle = \langle \nabla_{\dot{\gamma}} \nabla f(\gamma_t), \dot{\gamma}_t \rangle + \langle \nabla f(\gamma_t), \nabla_{\dot{\gamma}} \dot{\gamma}_t \rangle
$$
$$
= \langle \nabla^2 f(\gamma_t) \cdot \dot{\gamma}_t, \dot{\gamma}_t \rangle.
$$

In other words, if  $\gamma_0 = x$  and  $\dot{\gamma}_0 = v \in T_xM$ , then

$$
f(\gamma_t) = f(x) + t \langle \nabla f(x), v \rangle + \frac{t^2}{2} \langle \nabla^2 f(x) \cdot v, v \rangle + o(t^2). \tag{14.3}
$$

This identity can actually be used to define the Hessian operator.

A similar computation shows that for any two tangent vectors  $u, v$ at  $x$ ,

$$
\frac{D}{Ds} \left( \frac{d}{dt} f(\exp_x(su + tv)) \right) = \langle \nabla^2 f(x) \cdot u, v \rangle, \tag{14.4}
$$

where  $\exp_x v$  is the value at time 1 of the constant speed geodesic starting from x with velocity v. Identity  $(14.4)$  together with  $(14.2)$ shows that if  $f \in C^2(M)$ , then  $\nabla^2 f(x)$  is a symmetric operator:  $\langle \nabla^2 f(x) \cdot u, v \rangle_x = \langle \nabla^2 f(x) \cdot v, u \rangle_x$ . In that case it will often be convenient to think of  $\nabla^2 f(x)$  as a quadratic form on  $T_xM$ .

The Hessian is related to another fundamental second-order differential operator, the Laplacian, or Laplace–Beltrami operator. The Laplacian can be defined as the trace of the Hessian:

$$
\Delta f(x) = \text{tr}(\nabla^2 f(x)).
$$

Another possible definition is

$$
\Delta f = \nabla \cdot (\nabla f),
$$

where  $\nabla$  is the **divergence** operator, defined as the negative of the adjoint of the gradient in  $L^2(M)$ : More explicitly, if  $\xi$  is a  $C^1$  vector field on  $M$ , its divergence is defined by

$$
\forall \zeta \in C_c^{\infty}(M), \qquad \int_M (\nabla \cdot \xi) \zeta \, d\text{vol} = -\int_M \xi \cdot \nabla \zeta \, d\text{vol}.
$$

Both definitions are equivalent; in fact, the divergence of any vector field  $\xi$  coincides with the trace of the covariant gradient of  $\xi$ . When  $M = \mathbb{R}^n$ ,  $\Delta f$  is given by the usual expression  $\sum \partial_{ii}^2 f$ . More generally, in coordinates, the Laplacian reads

$$
\Delta f = (\det g)^{-1/2} \sum_{ij} \partial_i ((\det g)^{1/2} g^{ij} \partial_j f).
$$

In the context of optimal transport, we shall be led to consider Hessian operators for functions f that are not of class  $C^2$ , and not even continuously differentiable. However,  $\nabla f$  and  $\nabla^2 f$  will still be well-defined almost everywhere, and this will be sufficient to conduct the proofs. Here I should explain what it means for a function defined almost everywhere to be differentiable. Let  $\xi$  be a vector field defined on a domain of a neighborhood  $U$  of  $x$ ; when  $y$  is close enough to  $x$ , there is a unique velocity  $w \in T_xM$  such that  $y = \gamma_1$ , where  $\gamma$  is the constant-speed geodesic starting from  $x$  with initial velocity  $w$ ; for simplicity I shall write  $w = y - x$  (to be understood as  $y = \exp_x w$ ). Then  $\xi$  is said to be covariantly differentiable at x in the direction v, if

$$
\nabla_v \xi(x) := \lim_{y \to x; \ \frac{y - x}{|y - x|} \to \frac{v}{|v|}} |v| \left( \frac{\theta_{y \to x} \xi(y) - \xi(x)}{|y - x|} \right)
$$
(14.5)

exists, where y varies on the domain of definition of  $\xi$ , and  $\theta_{y\to x}$  is the parallel transport along the geodesic joining y to x. If  $\xi$  is defined everywhere in a neighborhood of  $x$ , then this is just the usual notion of covariant derivation. Formulas for (14.5) in coordinates are just the same as in the smooth case.

The following theorem is the main result of second differentiability for nonsmooth functions:

Theorem 14.1 (Second differentiability of semiconvex functions). Let M be a Riemannian manifold equipped with its volume measure, let U be an open subset of M, and let  $\psi : U \to \mathbb{R}$  be locally semiconvex with a quadratic modulus of semiconvexity, in the sense of Definition 10.10. Then, for almost every  $x \in U$ ,  $\psi$  is differentiable at x and there exists a symmetric operator  $A: T_xM \to T_xM$ , characterized by one of the following two equivalent properties:

(i) For any  $v \in T_xM$ ,  $\nabla_v(\nabla \psi)(x) = Av$ ;

(ii) 
$$
\psi(\exp_x v) = \psi(x) + \langle \nabla \psi(x), v \rangle + \frac{\langle A \cdot v, v \rangle}{2} + o(|v|^2) \text{ as } v \to 0.
$$

The operator A is denoted by  $\nabla^2 \psi(x)$  and called the Hessian of  $\psi$  at x. When no confusion is possible, the quadratic form defined by A is also called the Hessian of  $\psi$  at x.

The trace of A is denoted by  $\Delta \psi(x)$  and called the Laplacian of  $\psi$  at x. The function  $x \to \Delta \psi(x)$  coincides with the density of the absolutely continuous part of the distributional Laplacian of  $\psi$ ; while the singular part of this distributional Laplacian is a nonnegative measure.

**Remark 14.2.** The particular case when  $\psi$  is convex  $\mathbb{R}^n \to \mathbb{R}$  is known as Alexandrov's second differentiability theorem. By extension, I shall use the terminology "Alexandrov's theorem" for the general statement of Theorem 14.1. This theorem is more often stated in terms of Property (ii) than in terms of Property (i); but it is the latter that will be most useful for our purposes.

Remark 14.3. As the proof will show, Property (i) can be replaced by the following more precise statement involving the subdifferential of  $\psi$ : If  $\xi$  is any vector field valued in  $\nabla^- \psi$  (i.e.  $\xi(y) \in \nabla^- \psi(y)$  for all y), then  $\nabla_v \xi(x) = Av$ .

Remark 14.4. For the main part of this course, we shall not need the full strength of Theorem 14.1, but just the particular case when  $\psi$  is continuously differentiable and  $\nabla \psi$  is Lipschitz; then the proof becomes much simpler, and  $\nabla \psi$  is almost everywhere differentiable in

the usual sense. Still, on some occasions we shall need the full generality of Theorem 14.1.

Beginning of proof of Theorem  $14.1$ . The notion of local semiconvexity with quadratic modulus is invariant by  $C^2$  diffeomorphism, so it suffices to prove Theorem 14.1 when  $M = \mathbb{R}^n$ . But a semiconvex function in an open subset U of  $\mathbb{R}^n$  is just the sum of a quadratic form and a locally convex function (that is, a function which is convex in any convex subset of  $U$ ). So it is actually sufficient to consider the special case when  $\psi$  is a convex function in a convex subset of  $\mathbb{R}^n$ . Then if  $x \in U$  and B is a closed ball around x, included in U, let  $\psi_B$  be the restriction of  $\psi$  to B; since  $\psi$  is Lipschitz and convex, it can be extended into a Lipschitz convex function on the whole of  $\mathbb{R}^n$  (take for instance the supremum of all supporting hyperplanes for  $\psi_B$ ). In short, to prove Theorem 14.1 it is sufficient to treat the special case of a convex function  $\psi : \mathbb{R}^n \to \mathbb{R}$ . At this point the argument does not involve any more Riemannian geometry, but only convex analysis; so I shall postpone it to the Appendix (Theorem 14.25). ⊓⊔

## The Jacobian determinant of the exponential map

Let M be a Riemannian manifold, and let  $\xi$  be a vector field on M (so for each x,  $\xi(x)$  lies in  $T_xM$ ). Recall the definition of the exponential map  $T = \exp \xi$ : Start from point x a geodesic curve with initial velocity  $\xi(x) \in T_xM$ , and follow it up to time 1 (it is not required that the geodesic be minimizing all along); the position at time 1 is denoted by  $\exp_x \xi(x)$ . As a trivial example, in the Euclidean space,  $\exp_x \xi(x) =$  $x + \xi(x)$ .

The computation of the Jacobian determinant of such a map is a classical exercise in Riemannian geometry, whose solution involves the Ricci curvature. One can take this computation as a theorem about the Ricci curvature (previously defined in terms of sectional or Riemann curvature), or as the mere definition of the Ricci curvature.

So let  $x \in M$  be given, and let  $\xi$  be a vector field defined in a neighborhood of  $x$ , or almost everywhere in a neighborhood of  $x$ . Let  $(e_1,\ldots,e_n)$  be an orthonormal basis of  $T_xM$ , and consider small variations of x in these directions  $e_1, \ldots, e_n$ , denoted abusively by  $x + \delta e_1, \ldots, x + \delta e_n$ . (Here  $x + \delta e_j$  should be understood as, say,  $\exp_x(\delta e_j)$ ; but it might also be any path  $x(\delta)$  with  $\dot{x}(0) = e_i$ .) As  $\delta \to 0$ , the infinitesimal parallelepiped  $P_{\delta}$  built on  $(x + \delta e_1, \ldots, x + \delta e_n)$  has volume vol  $[P_\delta] \simeq \delta^n$ . (It is easy to make sense of that by using local charts.) The quantity of interest is

$$
\mathcal{J}(x) := \lim \frac{\text{vol}[T(P_\delta)]}{\text{vol}[P_\delta]}.
$$

For that purpose,  $T(P_\delta)$  can be approximated by the infinitesimal parallelogram built on  $T(x + \delta e_1), \ldots, T(x + \delta e_n)$ . Explicitly,

$$
T(x + \delta e_i) = \exp_{x + \delta e_i}(\xi(x + \delta e_i)).
$$

(If  $\xi$  is not defined at  $x+\delta e_i$  it is always possible to make an infinitesimal perturbation and replace  $x + \delta e_i$  by a point which is extremely close and at which  $\xi$  is well-defined. Let me skip this nonessential subtlety.)

Assume for a moment that we are in  $\mathbb{R}^n$ , so  $T(x) = x + \xi(x)$ ; then, by a classical result of real analysis,  $\mathcal{J}(x) = |\det(\nabla T(x))|$  $|\det(I_n + \nabla \xi(x))|$ . But in the genuinely Riemannian case, things are much more intricate (unless  $\xi(x) = 0$ ) because the measurement of infinitesimal volumes changes as we move along the geodesic path  $\gamma(t,x) = \exp_x(t\xi(x)).$ 

To appreciate this continuous change, let us parallel-transport along the geodesic  $\gamma$  to define a new family  $\mathbf{E}(t) = (e_1(t), \ldots, e_n(t))$  in  $T_{\gamma(t)}M$ . Since  $(d/dt)\langle e_i(t), e_j(t)\rangle = \langle \dot{e}_i(t), e_j(t)\rangle + \langle e_i(t), \dot{e}_j(t)\rangle = 0$ , the family  $\mathbf{E}(t)$  remains an orthonormal basis of  $T_{\gamma(t)}M$  for any t. (Here the dot symbol stands for the covariant derivation along  $\gamma$ .) Moreover,  $e_1(t) = \dot{\gamma}(t,x)/|\dot{\gamma}(t,x)|$ . (See Figure 14.2.)

Image /page/12/Figure/8 description: The image shows a curved line with three cubes positioned along it. Arrows are drawn tangent to the curve at different points, indicating direction. The first cube, labeled "E(0)", is at the beginning of the curve and has an arrow pointing forward and slightly upward. The second cube is in the middle of the curve, with a shorter arrow pointing forward. The third cube, labeled "E(t)", is at the end of the curve and has an arrow pointing forward and slightly upward, similar to the first cube. The cubes are depicted as simple 3D boxes.

Fig. 14.2. The orthonormal basis E, here represented by a small cube, goes along the geodesic by parallel transport.

To express the Jacobian of the map  $T = \exp \xi$ , it will be convenient to consider the whole collection of maps  $T_t = \exp(t \xi)$ . For brevity, let us write

$$
T_t(x+\delta \mathbf{E}) = (T_t(x+\delta e_1), \ldots, T_t(x+\delta e_n));
$$

then

$$
T_t(x+\delta \mathbf{E}) \simeq T_t(x) + \delta \mathbf{J},
$$

where

$$
\mathbf{J} = (J_1, \dots, J_n); \qquad J_i(t, x) := \frac{d}{d\delta} \bigg|_{\delta = 0} T_t(x + \delta e_i).
$$

(See Figure 14.3.)

The vector fields  $J_i$  have been obtained by differentiating a family of geodesics depending on a parameter (here  $\delta$ ); such vector fields are called Jacobi fields and they satisfy a characteristic linear secondorder equation known as the Jacobi equation. To write this equation, it will be convenient to express  $J_1, \ldots, J_n$  in terms of the basis  $e_1, \ldots, e_n$ ; so let  $J_{ij} = \langle J_i, e_j \rangle$  stand for the jth component of  $J_i$  in this basis. The matrix  $J = (J_{ij})_{1 \leq i,j \leq n}$  satisfies the differential equation

$$
\ddot{J}(t) + R(t) J(t) = 0,\t(14.6)
$$

where  $R(t)$  is a matrix which depends on the Riemannian structure at  $\gamma(t)$ , and can be expressed in terms of the Riemann curvature tensor:

$$
R_{ij}(t) = \left\langle \text{Riem}_{\gamma(t)}(\dot{\gamma}(t), e_i(t)) \dot{\gamma}(t), e_j(t) \right\rangle_{\gamma(t)}.
$$
 (14.7)

(All of these quantities depend implicitly on the starting point  $x$ .) The reader who prefers to stay away from the Riemann curvature tensor can take  $(14.6)$  as the equation defining the matrix R; the only things that one needs to know about it are (a)  $R(t)$  is symmetric; (b) the first row of  $R(t)$  vanishes (which is the same, modulo identification, as  $R(t)\dot{\gamma}(t) = 0$ ; (c) tr  $R(t) = \text{Ric}_{\gamma_t}(\dot{\gamma}_t, \dot{\gamma}_t)$  (which one can also adopt as a definition of the Ricci tensor); (d)  $R$  is invariant under the transform  $t \to 1-t$ ,  $E(t) \to -E(1-t)$ ,  $\gamma_t \to \gamma_{1-t}$ .

Equation (14.6) is of second order in time, so it should come with initial conditions for both  $J(0)$  and  $J(0)$ . On the one hand, since  $T_0(y) = y$ ,

$$
J_i(0) = \frac{d}{d\delta}\bigg|_{\delta=0} (x + \delta e_i) = e_i,
$$

Image /page/14/Figure/1 description: The image depicts a sequence of three cubes positioned along a curved path. At the beginning of the path, labeled "J(0) = E(0)", a cube is shown with three vectors originating from it. Further along the path, a second cube is shown. At the end of the path, a third cube is depicted, with dashed lines extending from the first two cubes to this final position. The dashed lines are labeled "J(t)" and "E(t)" respectively, suggesting a transformation or evolution of the initial state to the final state.

Fig. 14.3. At time  $t = 0$ , the matrices  $J(t)$  and  $E(t)$  coincide, but at later times they (may) differ, due to geodesic distortion.

so  $J(0)$  is just the identity matrix. On the other hand,

$$
\dot{J}_i(0) = \left. \frac{D}{Dt} \right|_{t=0} \left. \frac{d}{d\delta} \right|_{\delta=0} T_t(x + \delta e_i) = \left. \frac{D}{D\delta} \right|_{\delta=0} \left. \frac{d}{dt} \right|_{t=0} T_t(x + \delta e_i)
$$

$$
= \left. \frac{D}{D\delta} \right|_{\delta=0} \xi(x + \delta e_i) = (\nabla \xi) e_i,
$$

where  $\nabla \xi$  is the covariant gradient of  $\xi$ . (The exchange of derivatives is justified by the differentiability of  $\xi$  at x and the  $C^{\infty}$  regularity of  $(t, y, \xi) \rightarrow \exp_y(t\xi)$ .) So

$$
\frac{d}{dt}J_{ij} = \frac{d}{dt}\langle J_i, e_j \rangle = \langle \frac{DJ_i}{Dt}, e_j \rangle = \langle (\nabla \xi)e_i, e_j \rangle.
$$

We conclude that the initial conditions are

$$
J(0) = I_n, \t J(0) = \nabla \xi(x), \t (14.8)
$$

where in the second expression the linear operator  $\nabla \xi(x)$  is identified with its matrix in the basis **E**:  $(\nabla \xi)_{ij} = \langle (\nabla \xi) e_i, e_j \rangle = \langle e_i \cdot \nabla \xi, e_j \rangle$ . (Be careful, this is the converse of the usual convention  $A_{ij} = \langle Ae_j, e_i \rangle;$ anyway, later we shall work with symmetric operators, so it will not matter.)

From this point on, the problem is about a path  $J(t)$  valued in the space  $M_n(\mathbb{R})$  of real  $n \times n$  matrices, and we can forget about the geometry: Parallel transport has provided a consistent identification of

all the tangent spaces  $T_{\gamma(t)}M$  with  $\mathbb{R}^n$ . This path depends on x via the initial conditions (14.8), so in the sequel we shall put that dependence explicitly. It might be very rough as a function of  $x$ , but it is very smooth as a function of t. The Jacobian of the map  $T_t$  is defined by

$$
\mathcal{J}(t,x) = \det J(t,x),
$$

and the formula for the differential of the determinant yields

$$
\dot{\mathcal{J}}(t,x) = \mathcal{J}(t,x) \operatorname{tr} (\dot{J}(t,x) J(t,x)^{-1}), \qquad (14.9)
$$

at least as long as  $J(t, x)$  is invertible (let's forget about that problem for the moment).

So it is natural to set

$$
U := \dot{J} J^{-1}, \tag{14.10}
$$

and look for an equation on  $U$ . By differentiating  $(14.10)$  and using (14.6), we discover that

$$
\dot{U} = \ddot{J}J^{-1} - \dot{J}J^{-1}\dot{J}J^{-1} = -R - U^2
$$

(note that  $J$  and  $\dot{J}$  do not necessarily commute). So the change of variables (14.10) has turned the second-order equation (14.6) into the first-order equation

$$
\dot{U} + U^2 + R = 0,\t(14.11)
$$

which is of Ricatti type, that is, with a quadratic nonlinearity.

By taking the trace of (14.11), we arrive at

$$
\frac{d}{dt}(\text{tr } U) + \text{tr } (U^2) + \text{tr } R = 0.
$$

Now the trace of  $R(t, x)$  only depends on  $\gamma_t$  and  $\dot{\gamma}_t$ ; in fact, as noticed before, it is precisely the value of the Ricci curvature at  $\gamma(t)$ , evaluated in the direction  $\dot{\gamma}(t)$ . So we have arrived at our first important equation involving Ricci curvature:

$$
\frac{d}{dt}(\text{tr } U) + \text{tr}(U^2) + \text{Ric}(\dot{\gamma}) = 0,
$$
\n(14.12)

where of course Ric $(\dot{\gamma})$  is an abbreviation for Ric<sub> $\gamma(t)(\dot{\gamma}(t), \dot{\gamma}(t))$ .</sub>

Equation (14.12) holds true for any vector field  $\xi$ , as long as  $\xi$  is covariantly differentiable at  $x$ . But in the sequel, I shall only apply it in the particular case when  $\xi$  derives from a function:  $\xi = \nabla \psi$ ; and  $\psi$  is locally semiconvex with a quadratic modulus of semiconvexity. There are three reasons for this restriction:

(a) In the theory of optimal transport, one only needs to consider such maps;

(b) The semiconvexity of  $\psi$  guarantees the almost everywhere differentiability of  $\nabla \psi$ , by Theorem 14.1;

(c) If  $\xi = \nabla \psi$ , then  $\nabla \xi(x) = \nabla^2 \psi(x)$  is symmetric and this will imply the symmetry of  $U(t, x)$  at all times; this symmetry will allow to derive from (14.12) a closed inequality on tr  $U(t, x) = \mathcal{J}(t, x)$ .

So from now on,  $\xi = \nabla \psi$ , where  $\psi$  is semiconvex. To prove the symmetry of  $U(t, x)$ , note that  $U(0, x) = I_n$  and  $\dot{U}(0, x) = \nabla^2 \psi(x)$ (modulo identification) are symmetric, and the time-dependent matrix  $R(t, x)$  is also symmetric, so  $U(t, x)$  and its transpose  $U(t, x)^*$  solve the same differential equation, with the same initial conditions. Then, by the uniqueness statement in the Cauchy–Lipschitz theorem, they have to coincide at all times where they are defined.

Inequality (14.12) cannot be recast as a differential equation involving only the Jacobian determinant (or equivalently tr  $U(t, x)$ ), since the quantity  $tr(U^2)$  in (14.12) cannot be expressed in terms of  $tr U$ . However, the symmetry of  $U$  allows us to use the Cauchy–Schwarz inequality, in the form

$$
\operatorname{tr}\left( U^{2}\right) \geq\frac{(\operatorname{tr} U)^{2}}{n};
$$

then, by plugging this inequality into (14.12), we obtain an important differential inequality involving Ricci curvature:

$$
\frac{d}{dt}(\text{tr } U) + \frac{(\text{tr } U)^2}{n} + \text{Ric}(\dot{\gamma}) \le 0.
$$
\n(14.13)

There are several ways to rewrite this result in terms of the Jacobian determinant  $\mathcal{J}(t)$ . For instance, by differentiating the formula

$$
\operatorname{tr} U = \frac{\dot{\mathcal{J}}}{\mathcal{J}},
$$

one obtains easily

$$
\frac{d}{dt}(\text{tr } U) + \frac{(\text{tr } U)^2}{n} = \frac{\ddot{\mathcal{J}}}{\mathcal{J}} - \left(1 - \frac{1}{n}\right) \left(\frac{\dot{\mathcal{J}}}{\mathcal{J}}\right)^2.
$$

So (14.13) becomes

$$
\frac{\ddot{\mathcal{J}}}{\mathcal{J}} - \left(1 - \frac{1}{n}\right) \left(\frac{\dot{\mathcal{J}}}{\mathcal{J}}\right)^2 \le -\operatorname{Ric}(\dot{\gamma}).\tag{14.14}
$$

For later purposes, it will be convenient to define  $\mathcal{D}(t) := \mathcal{J}(t)^{1/n}$ (which one can think of as a coefficient of mean distortion); then the left-hand side of (14.14) is exactly  $n\mathcal{D}/\mathcal{D}$ . So

$$
\frac{\ddot{\mathcal{D}}}{\mathcal{D}} \le -\frac{\text{Ric}(\dot{\gamma})}{n}.\tag{14.15}
$$

Yet another useful formula is obtained by introducing  $\ell(t) :=$  $-\log \mathcal{J}(t)$ , and then (14.13) becomes

$$
\ddot{\ell}(t) \ge \frac{\dot{\ell}(t)^2}{n} + \text{Ric}(\dot{\gamma}).\tag{14.16}
$$

In all of these formulas, we have always taken  $t = 0$  as the starting time, but it is clear that we could do just the same with any starting time  $t_0 \in [0, 1]$ , that is, consider, instead of  $T_t(x) = \exp(t\nabla \psi(x))$ , the map  $T_{t_0\to t}(x) = \exp((t-t_0)\nabla\psi(x))$ . Then all the differential inequalities are unchanged; the only difference is that the Jacobian determinant at time  $t = 0$  is not necessarily 1.

### Taking out the direction of motion

The previous formulas are quite sufficient to derive many useful geometric consequences. However, one can refine them by taking advantage of the fact that curvature is not felt in the direction of motion. In other words, if one is traveling along some geodesic  $\gamma$ , one will never be able to detect some curvature by considering variations (in the initial position, or initial velocity) in the direction of  $\gamma$  itself: the path will always be the same, up to reparametrization. This corresponds to the property  $R(t)\dot{\gamma}(t) = 0$ , where  $R(t)$  is the matrix appearing in (14.6). In short, curvature is felt only in  $n-1$  directions out of n. This loose principle often leads to a refinement of estimates by a factor  $(n-1)/n$ .

Here is a recipe to "separate out" the direction of motion from the other directions. As before, assume that the first vector of the orthonormal basis  $\mathbf{J}(0)$  is  $e_1(0) = \dot{\gamma}(0)/|\dot{\gamma}(0)|$ . (The case when  $\dot{\gamma}(0) = 0$  can be treated separately.) Set  $u_{//} = u_{11}$  (this is the coefficient in U which corresponds to just the direction of motion), and define  $U_{\perp}$  as the  $(n-1) \times (n-1)$  matrix obtained by removing the first line and first column in U. Of course,  $tr(U) = u_{//} + tr(U_{\perp})$ . Next decompose the Jacobian determinant  $\mathcal J$  into a parallel and an orthogonal contributions:

$$
\mathcal{J} = \mathcal{J}_{//} \mathcal{J}_{\perp}, \qquad \mathcal{J}_{//}(t) = \exp \left( \int_0^t u_{//}(s) \, ds \right).
$$

Further define parallel and orthogonal distortions by

$$
\mathcal{D}_{//}=\mathcal{J}_{//},\qquad \mathcal{D}_{\perp}=\mathcal{J}_{\perp}^{\frac{1}{n-1}};
$$

and, of course,

$$
\ell_{//} = -\log \mathcal{J}_{//}, \qquad \ell_{\perp} = -\log \mathcal{J}_{\perp}.\tag{14.17}
$$

Since the first row of  $R(t)$  vanishes, equation (14.11) implies

$$
\dot{u}_{\text{in}} = -\sum_{j} u_{1j}^{2} \le -u_{11}^{2} = -u_{\text{in}}^{2}.
$$

It follows easily that

$$
\ddot{\ell}_{//} \ge \dot{\ell}_{//}^2,\tag{14.18}
$$

or equivalently

$$
\ddot{\mathcal{J}}_{\mathcal{J}} \leq 0,\tag{14.19}
$$

so  $\mathcal{D}_{//} = \mathcal{J}_{//}$  is always a concave function of t (this property does not depend on the curvature of  $M$ ), and the same holds true of course for  $\mathcal{D}_{//}$  which coincides with  $\mathcal{J}_{//}$ .

Now let us take care of the orthogonal part: Putting together (14.9), (14.10), (14.11), (14.18), we find

$$
\ddot{\ell}_{\perp} = -\frac{d}{dt}(\text{tr } U) - \ddot{\ell}_{\text{//}} = \text{tr}(U^2) + \text{Ric}(\dot{\gamma}) - \sum u_{1j}^2.
$$

Since tr  $U^2 = \text{tr} (U_\perp)^2 + 2 \sum u_{1j}^2$ , this implies

$$
\ddot{\ell}_{\perp} \ge \text{tr}\left(U_{\perp}^{2}\right) + \text{Ric}(\dot{\gamma}).\tag{14.20}
$$

Then in the same manner as before, one can obtain

$$
\ddot{\ell}_{\perp} \ge \frac{(\dot{\ell}_{\perp})^2}{n-1} + \text{Ric}(\dot{\gamma}),\tag{14.21}
$$
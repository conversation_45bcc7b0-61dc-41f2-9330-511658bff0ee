# FocusDD: Real-World Scene Infusion for Robust Dataset Distillation

Youbing Hu<sup>1</sup>, <PERSON><sup>2</sup>, <PERSON><sup>3,4</sup>, <PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><PERSON><PERSON><sup>1</sup>, and  $\rm Zhi$ iun  $\rm Li$ <sup>1</sup>

<sup>1</sup>Faculty of Computing, Harbin Institute of Technology

<sup>2</sup>Swiss Data Science Center, Zurich, Switzerland

<sup>3</sup>Graz University of Technology, Austria

<sup>4</sup>Complexity Science Hub Vienna, Austria

{youbing, zhiqiang\_cao, luanqi}@stu.hit.edu.cn, <EMAIL>,

<EMAIL>, <EMAIL>, lizhijun\<EMAIL>

## Abstract

Dataset distillation has emerged as a strategy to compress real-world datasets for efficient training. However, it struggles with large-scale and high-resolution datasets, limiting its practicality. This paper introduces a novel resolution-independent dataset distillation method Focused Dataset Distillation (FocusDD), which achieves diversity and realism in distilled data by identifying key information patches, thereby ensuring the generalization capability of the distilled dataset across different network architectures. Specifically, FocusDD leverages a pre-trained Vision Transformer (ViT) to extract key image patches, which are then synthesized into a single distilled image. These distilled images, which capture multiple targets, are suitable not only for classification tasks but also for dense tasks such as object detection. To further improve the generalization of the distilled dataset, each synthesized image is augmented with a downsampled view of the original image. Experimental results on the ImageNet-1K dataset demonstrate that, with 100 images per class (IPC), ResNet50 and MobileNet-v2 achieve validation accuracies of 71.0% and 62.6%, respectively, outperforming state-of-the-art methods by 2.8% and 4.7%. Notably, FocusDD is the first method to use distilled datasets for object detection tasks. On the COCO2017 dataset, with an IPC of 50, YOLOv11n and YOLOv11s achieve 24.4% and 32.1% mAP, respectively, further validating the effectiveness of our approach.

## 1 Introduction

Contemporary deep learning has achieved remarkable success largely due to the exponential growth in model sizes [\(Dosovitskiy et al.,](#page-11-0) [2020;](#page-11-0) [He et al.,](#page-12-0) [2016;](#page-12-0) [Radford et al.,](#page-13-0) [2021;](#page-13-0) [Szegedy et al.,](#page-13-0) [2015\)](#page-13-0) and data scales [\(Deng](#page-11-0) [et al.,](#page-11-0) [2009;](#page-11-0) [Kirillov et al.,](#page-12-0) [2023;](#page-12-0) [Ridnik et al.,](#page-13-0) [2021\)](#page-13-0). This growth has led to the development of advanced neural networks that achieve groundbreaking performance in tasks like image classification [\(Dosovitskiy et al.,](#page-11-0) [2020\)](#page-11-0), object detection [\(Carion et al.,](#page-11-0) [2020\)](#page-11-0), and natural language processing [\(Vaswani et al.,](#page-14-0) [2017\)](#page-14-0). However, this progress is not without its challenges. The rapid expansion of model complexities and data volumes has led to significantly increased computational costs and time expenses, in particular when training large neural networks on high-resolution and large-scale datasets [\(Jiang et al.,](#page-12-0) [2021;](#page-12-0) [Liu et al.,](#page-12-0) [2021;](#page-12-0) [Touvron et al.,](#page-13-0) [2021\)](#page-13-0). These challenges significantly hinder the practical deployment of deep learning models, especially in resource-limited environments [\(Ignatov et al.,](#page-12-0) [2019\)](#page-12-0).

| Model    | Method  | Flower102   | Food101     | CIFAR100    |
|----------|---------|-------------|-------------|-------------|
| ResNet18 | Random  | 22.4        | 57.8        | 54.5        |
|          | RDED    | 67.8        | 74.2        | 69.3        |
|          | FocusDD | <b>71.1</b> | <b>77.6</b> | <b>71.3</b> |

<span id="page-1-0"></span>Table 1: We evaluate the generalization performance of ResNet-18 [\(He et al.,](#page-12-0) [2016\)](#page-12-0) as a validation model trained on distilled data. With IPC set to 10, the model is first pre-trained on a dataset distilled by RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0) and FocusDD, then fine-tuned on the original data for 10 epochs. The datasets used are Flowers-102 [\(Nilsback and Zisserman,](#page-13-0) [2008\)](#page-13-0), Food-101 [\(Bossard et al.,](#page-11-0) [2014\)](#page-11-0), and CIFAR-100 [\(Krizhevsky](#page-12-0) [et al.,](#page-12-0) [2009\)](#page-12-0). "Random" refers to a model trained directly on the target datasets for 10 epochs without pre-training.

Dataset distillation [\(Wang et al.,](#page-14-0) [2018\)](#page-14-0) has emerged as a promising strategy to address these challenges. The core idea is to compress large, real-world datasets into smaller, more manageable representations that retain essential information while reducing the computational burden of ingesting them. Various methods have been proposed, including coreset selection-based distillation [Feldman and Zhang](#page-11-0) [\(2020\)](#page-11-0); [Meding](#page-13-0) [et al.](#page-13-0) [\(2021\)](#page-13-0); [Paul et al.](#page-13-0) [\(2021\)](#page-13-0); [Tan et al.](#page-13-0) [\(2024\)](#page-13-0); [Toneva et al.](#page-13-0) [\(2018\)](#page-13-0), which select representative samples from the original dataset; bi-level optimization-based distillation [\(Du et al.,](#page-11-0) [2023;](#page-11-0) [Guo et al.,](#page-12-0) [2023;](#page-12-0) [Zhang et al.,](#page-14-0) [2023;](#page-14-0) [Zhao and](#page-14-0) [Bilen,](#page-14-0) [2023\)](#page-14-0), which treats dataset distillation as a meta-learning problem involving two nested optimization loops—where the outer loop optimizes the meta-dataset and the inner loop trains a model with the distilled data; and distillation with prior regularization [\(Cazenavette et al.,](#page-11-0) [2023;](#page-11-0) [Cui et al.,](#page-11-0) [2023;](#page-11-0) [Lu et al.,](#page-12-0) [2023\)](#page-12-0), which leverages prior knowledge at the feature level to guide the generation of the condensed dataset.

Although traditional solutions have made significant progress in handling small-scale and low-resolution datasets (such as Tiny-ImageNet [\(Le and Yang,](#page-12-0) [2015\)](#page-12-0), downscaled ImageNet [\(Chrabaszcz et al.,](#page-11-0) [2017\)](#page-11-0), or subsets of ImageNet [\(Kim et al.,](#page-12-0) [2022\)](#page-12-0)), their high com-

Image /page/1/Figure/4 description: The image contains two plots. The left plot is a bar chart comparing the accuracy of different models (SRe2L, SCDD, RDED, FocusDD) on two different architectures (MobileNet-v2 and ResNet18). For MobileNet-v2, the accuracies are 42.8%, 52.7%, 57.9%, and 62.6% respectively. For ResNet18, the accuracies are 52.8%, 57.9%, 60.8%, and 62.0% respectively. The y-axis is labeled "Accuracy (%)" and ranges from 40% to 65%. The right plot is a line graph showing the mAP (%) for YOLO11n and YOLO11s at different IPC (iterations per second) values (20, 50, 100). For YOLO11n, the mAP values are approximately 12%, 24%, and 30%. For YOLO11s, the mAP values are approximately 17%, 33%, and 38%. The x-axis is labeled "IPC" and the y-axis is labeled "mAP (%)".

Figure 1: FocusDD performance on classification and detection tasks. Left: For classification with IPC=100, we use MobileNet-v2 [\(Sandler et al.,](#page-13-0) [2018\)](#page-13-0) and ResNet-18 [\(He](#page-12-0) [et al.,](#page-12-0) [2016\)](#page-12-0) as validation models to evaluate the ImageNet-1K [\(Deng et al.,](#page-11-0) [2009\)](#page-11-0) validation set. SCDD [\(Zhou et al.,](#page-14-0) [2024\)](#page-14-0),  $SRe<sup>2</sup>L$  [\(Yin et al.,](#page-14-0) 2024), and RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0) are the current SOTA methods. Right: In the detection task, we use YOLOv11 [\(Khanam and Hussain,](#page-12-0) [2024\)](#page-12-0) as the validation model to evaluate the COCO2017 [\(Lin](#page-12-0) [et al.,](#page-12-0) [2014\)](#page-12-0) validation set. FocusDD is the first method to explore dataset distillation for object detection tasks.

putational cost limits their practical application when scaled to high-resolution and large-scale datasets. To address this issue, SRe<sup>2</sup>L [\(Yin et al.,](#page-14-0) [2024\)](#page-14-0) proposed a decoupled approach for model updates and datasets, which was the first to extend dataset distillation techniques to the scale of ImageNet. Subsequently, several methods [\(Loo et al.;](#page-12-0) [Sun et al.,](#page-13-0) [2024;](#page-13-0) [Yin and Shen,](#page-14-0) [2023;](#page-14-0) [Zhou et al.,](#page-14-0) [2024\)](#page-14-0) have been proposed to improve the efficiency of  $\text{SRe}^2$ L and significantly enhance accuracy. For example, SCDD [\(Zhou et al.,](#page-14-0) [2024\)](#page-14-0) replaces the batch-level statistics used in SRe<sup>2</sup>L with statistics calculated over the entire distillation dataset. RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0) randomly crops a region from the original high-resolution image, selects multiple images with the highest authenticity scores, and merges them into a distilled image. While these methods effectively synthesize high-resolution images, they rely on specific network architectures during the distillation process, limiting the generalization ability of the distilled dataset. Furthermore, the datasets distilled by

Image /page/2/Picture/0 description: This is a collage of images, each with bounding boxes and labels indicating detected objects. The objects detected include birds, dogs, cows, elephants, boats, people, trucks, teddy bears, oranges, cats, chairs, trains, cars, keyboards, and mice. Some bounding boxes also include confidence scores, such as 'bird 0.93', 'dog 0.76', 'boat 0.90', 'person 0.54', 'teddy bear 0.97', 'orange 0.94', 'cat 0.92', 'train 0.91', 'car 0.44', 'keyboard 0.89', and 'mouse 0.56'.

Figure 2: Visualization of the FocusDD-distilled images on different tasks. Left: Visualization of training samples for object detection using FocusDD-distilled images. Using YOLOv11x [\(Khanam and Hussain,](#page-12-0) [2024\)](#page-12-0) as the teacher model, soft supervision is applied to train YOLOv11n and YOLOv11s, tested on the COCO2017 [\(Lin et al.,](#page-12-0) [2014\)](#page-12-0) validation set. The numbers in each image correspond to COCO categories. Right: Visualization of training samples for classification using FocusDD-distilled images. Soft supervision with ResNet-18 [\(He et al.,](#page-12-0) [2016\)](#page-12-0) as the teacher guides ResNet-18 and MobileNet-v2 [\(Sandler et al.,](#page-13-0) [2018\)](#page-13-0) training, tested on the ImageNet-1K [\(Deng et al.,](#page-11-0) [2009\)](#page-11-0) validation set. The performance is shown in Fig. [1.](#page-1-0)

these methods typically only apply to classification tasks and cannot be directly applied to dense tasks, such as object detection.

In this paper, we propose a novel dataset distillation method called Focused Dataset Distillation (FocusDD), which aims to improve the efficiency and realism of dataset distillation by focusing on key information patches within the data. FocusDD consists of two stages: (i) information extraction and (ii) image reconstruction. In the information extraction stage, we leverage a pre-trained Vision Transformer (ViT) [\(Dosovitskiy et al.,](#page-11-0) [2020\)](#page-11-0) to guide the selection of key image patches. By using ViT, we can accurately extract key image patches corresponding to foreground objects, thereby enhancing the realism of the distilled dataset and ensuring the relevance of the extracted information. Since these distilled images contain target regions, they are well-suited for downstream dense tasks such as object detection. As shown in Fig. [1,](#page-1-0) FocusDD demonstrates superior performance at different IPC levels on the COCO validation dataset when using the YOLOv11 model; Fig. 2 visualizes the training samples of FocusDD distillation images across different tasks. This is the first work to extend dataset distillation methods to object detection tasks. In the reconstruction stage, we combine downsampled versions of representative real images with the extracted key image patches to generate distilled images. This process not only preserves the diversity of the dataset but also ensures its realism, providing high-quality training data that enhances the generalization ability of the model. Table [1](#page-1-0) highlights the advantages of FocusDD in improving model generalization performance. Finally, an optional dynamic fine-tuning on a small subset of the original dataset can further boost performance and is investigated in Appendix [C.2.](#page-17-0)

Overall, this paper makes the following contributions to the field of dataset distillation:

• We are the first to integrate ViTs into the image distillation process. By selectively emphasizing critical regions and foreground objects, ViT ensures that the distilled dataset retains crucial contents of the data distribution for effective model training.

- Our method not only preserves the realism and diversity of the images but also enables effective application to downstream dense tasks, such as object detection. By leveraging Attention-guided distillation, we can clearly identify the image regions most critical for model learning. To the best of our knowledge, we are the first work to extend dataset distillation to object detection tasks.
- We provide a rigorous evaluation of our approach including multiple ablation studies and show improved model generalization capabilities across different network architectures. Compared to SOTA methods on classification tasks, FocusDD improves the accuracy of ResNet50 and MobileNetV2 at IPC level 50 by 2.8% and 4.7%, respectively. On object detection tasks, FocusDD achieves 24.4% mAP with YOLOv11n, and  $32.1\%$  mAP with YOLOv11s at an IPC of 50 on the COCO validation set.

### 2 Related Work

Data distillation [\(Wang et al.,](#page-14-0) [2018\)](#page-14-0) aims to reduce the computational costs of training deep learning models by condensing large datasets into smaller, information-rich subsets. Most previous dataset distillation methods [\(Cazenavette et al.,](#page-11-0) [2022;](#page-11-0) [Guo et al.,](#page-12-0) [2023;](#page-12-0) [Lee et al.,](#page-12-0) [2022;](#page-12-0) [Nguyen et al.,](#page-13-0) [2021;](#page-13-0) [Wang et al.,](#page-14-0) [2022,](#page-14-0) [2018;](#page-14-0) [Zhao and Bilen,](#page-14-0) [2023;](#page-14-0) [Zhao et al.,](#page-14-0) [2020;](#page-14-0) [Zhou et al.,](#page-14-0) [2022\)](#page-14-0) focus on small-scale and low-resolution datasets [\(Chrabaszcz et al.,](#page-11-0) [2017;](#page-11-0) [Kim et al.,](#page-12-0) [2022;](#page-12-0) [Le and Yang,](#page-12-0) [2015\)](#page-12-0) and can be classified into several categories: Bi-level optimization methods treat dataset distillation as a meta-learning problem, where an outer loop optimizes the synthetic dataset while an inner loop focuses on model training using distilled data, methods include FRePo [\(Zhou et al.,](#page-14-0) [2022\)](#page-14-0), DD [\(Wang et al.,](#page-14-0) [2018\)](#page-14-0), RFAD [\(Nguyen et al.,](#page-13-0) [2021\)](#page-13-0), KIP [\(Nguyen](#page-13-0) [et al.,](#page-13-0) [2021\)](#page-13-0), and LinBa [\(Deng and Russakovsky,](#page-11-0) [2022\)](#page-11-0). Trajectory-matching methods align model training trajectories on the original and distilled datasets over multiple iterations, methods include MTT [\(Cazenavette](#page-11-0) [et al.,](#page-11-0) [2022\)](#page-11-0), TESLA [\(Cui et al.,](#page-11-0) [2023\)](#page-11-0), and DATM [\(Guo et al.,](#page-12-0) [2023\)](#page-12-0). Distribution-matching methods match the distribution of the distilled dataset with that of the original in a single optimization step, with examples like KFS [\(Lee et al.,](#page-12-0) [2022\)](#page-12-0), DM [\(Zhao and Bilen,](#page-14-0) [2023\)](#page-14-0), CAFE [\(Wang et al.,](#page-14-0) [2022\)](#page-14-0), HaBa [\(Liu et al.,](#page-12-0) [2022\)](#page-12-0), and IT-GAN [\(Zhao and Bilen,](#page-14-0) [2022\)](#page-14-0). Gradient-matching methods align gradients of the network trained on original and synthesized data, with examples including DSA [\(Zhao and Bilen,](#page-14-0) [2021\)](#page-14-0), IDC [\(Kim et al.,](#page-12-0) [2022\)](#page-12-0), DC [\(Zhao et al.,](#page-14-0) [2020\)](#page-14-0), and DCC [\(Lee et al.,](#page-12-0) [2022\)](#page-12-0).

Building on these foundations, recent approaches have extended dataset distillation to large-scale, highresolution datasets. For example,  $\text{SRe}^2L$  [\(Yin et al.,](#page-14-0) [2024\)](#page-14-0) decouples model updates and dataset synthesis through "squeeze", "restore". and "relabel" stages, pioneering the expansion of dataset distillation to ImageNet-scale resolutions. SCDD [\(Zhou et al.,](#page-14-0) [2024\)](#page-14-0) further improves on  $S\text{Re}^2L$  by replacing batch-level statistics with global dataset statistics, achieving notable performance gains. D3S [\(Loo et al.\)](#page-12-0) reframes dataset distillation as a domain shift problem, introducing a scalable algorithm, while RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0) generates distilled images by randomly cropping and selecting high-realism image regions. Additionally, some dataset distillation methods [\(Gu et al.,](#page-12-0) [2024;](#page-12-0) [Su et al.,](#page-13-0) [2024\)](#page-13-0) employ the concept of diffusion models for distilling datasets.

Although previous methods excel with high-resolution images, they compress the original dataset into a specific architecture [\(Sun et al.,](#page-13-0) [2024;](#page-13-0) [Yin et al.,](#page-14-0) [2024;](#page-14-0) [Zhou et al.,](#page-14-0) [2024\)](#page-14-0), limiting the generalization of the distilled dataset. In contrast, FocusDD synthesizes datasets using the well-established Attention mechanism, which improves generalization, as shown in Table [1](#page-1-0) and Table [5](#page-9-0) across different ViT models. Furthermore, by synthesizing images focused on target locations, FocusDD extends its use to dense tasks like object detection, marking the first application of dataset distillation in this domain.

## 3 Approach

We first provide background knowledge on dataset distillation and ViT in Sec. [3.1.](#page-4-0) Next, we give a detailed description of our method FocusDD in Sec. [3.2,](#page-5-0) along with a theoretical analysis in Appendix [D.](#page-21-0) Finally, we discuss how to train models using the distilled dataset in Sec.  $3.3$ .

<span id="page-4-0"></span>Image /page/4/Figure/0 description: This is a flowchart illustrating an information area extraction process. The process begins with a set of 'Images' which are down-sampled. These down-sampled images, along with the original images, are processed to extract 'Original image key patches' and then mapped for 'Position mapping'. A 'Pre-trained Vision Transformer' is used to 'Localize key patches'. The localized key patches are then cropped to form 'Key Patches'. Separately, 'Selecting images with background' leads to 'Low Resolution Images'. Both 'Key Patches' and 'Low Resolution Images' are fed into a process that generates 'Reconstructed Images' and 'Soft Label' which is represented as a bar chart with labels like 'cat, dog, bird, flower, tiger'.

Figure 3: Overview of the FocusDD framework. FocusDD comprises two main stages: information extraction and image reconstruction. In the information extraction stage, a pre-trained ViT model guides the selection of key patches, identifying those containing key patches and representative real images with background details. During the image reconstruction stage, these patches are combined with images rich in background information to reconstruct a compiled, realistic image. Subsequently, these images are relabelled using a model with the same architecture as the validation model.

#### 3.1 Preliminaries

Data Distillation/Condensation. Dataset distillation [\(Wang et al.,](#page-14-0) [2018\)](#page-14-0) aims to compress information from a large-scale original dataset to a new compact dataset while striving to preserve the utmost degree of the original data informational essence. The resulting compressed dataset denoted as  $D'$ , should enable a model trained on it to perform comparably to a model trained on the original, full dataset D. Considering a large labeled dataset  $D = \{(\boldsymbol{x}_1, y_1), \dots, (\boldsymbol{x}_{|D|}, y_{|D|})\}$ , where  $|D|$  denotes the total number of samples, and each  $x_i$  is an image with its corresponding label  $y_i$ . The aim is to create a condensed dataset  $D'$  $\{(\tilde{\bm{x}}_1, \tilde{y}_1), \ldots, (\tilde{\bm{x}}_{|D'|}, \tilde{y}_{|D'|})\}$  that retains the key features of D, with  $|D'| \ll |D|$ , ensuring that this reduction in size does not compromise the dataset integrity. The learning objective is to minimize the performance disparity between the model trained on  $D'$  and the one trained on  $D$ , as expressed by the following constraint:

$$
\sup\{|\ell(\phi_{\theta_D}(\boldsymbol{x}),y) - \ell(\phi_{\theta_{D'}}(\boldsymbol{x}),y)|\}_{(\boldsymbol{x},y)\sim D} \leq \epsilon,\tag{1}
$$

where  $\epsilon$  represents the allowable performance disparity between models trained on  $D'$  versus those trained on D. Here,  $\theta_D$  parameterizes the neural network  $\phi$ , optimized on D as follows:

$$
\boldsymbol{\theta}_D = \arg\min_{\boldsymbol{\theta}} \mathbb{E}_{(\boldsymbol{x},y)\in D}[\ell(\phi_{\boldsymbol{\theta}}(\boldsymbol{x}),y)].
$$
\n(2)

In this formulation,  $\ell$  is the loss function, and  $\theta_{D'}$  is defined in a similar manner for the condensed dataset. This framework ensures that  $D'$  maintains the essential characteristics of  $D$ , allowing effective training on a smaller scale.

Vision Transformer. Vision Transformer (ViT) [\(Dosovitskiy et al.,](#page-11-0) [2020\)](#page-11-0) adapts the Transformer architecture [\(Vaswani et al.,](#page-14-0) [2017\)](#page-14-0), originally developed for natural language processing, to the domain of image analysis. They treat image patches as sequential inputs, allowing the model to capture global dependencies across the image. Each image is segmented into patches, which are embedded and supplemented with positional encodings to maintain spatial information, denoted as:  $\mathbf{x} = [\mathbf{x}_{cls}; \mathbf{E}(\mathbf{p}_1); \mathbf{E}(\mathbf{p}_2); \dots; \mathbf{E}(\mathbf{p}_K)] + \mathbf{E}_{pos}$ where E is the embedding function,  $p_i$  are the patches,  $x_{cls}$  is the class token, and  $E_{pos}$  represents the positional encodings. The self-attention mechanism then calculates attention scores to determine the relevance of each patch relative to others:

$$
\mathcal{A}(\mathbf{Q}, \mathbf{K}) = \text{Softmax}(\frac{\mathbf{Q}\mathbf{K}^T}{\sqrt{d}}) = [\mathbf{A}^1; \mathbf{A}^2; \dots; \mathbf{A}^K],
$$
  
Attention( $\mathbf{Q}, \mathbf{K}, \mathbf{V}$ ) =  $\mathcal{A}(\mathbf{Q}, \mathbf{K})\mathbf{V},$  (3)

<span id="page-5-0"></span>Image /page/5/Figure/0 description: This image illustrates a process involving image analysis and attention mechanisms. It begins with 'Original Images' showing a bird, followed by 'Downsample Images' which is a smaller version of the original. Next, 'Image Attention' displays a heatmap-like representation with a red bounding box highlighting areas of high attention. This is followed by 'Position Mapping', where the image is overlaid with a grid, and a red box indicates the region of maximum attention. The process continues with 'Cropping', leading to a 'Key Patches Set' which is a collection of cropped image patches, indicated by a dashed box and ellipses suggesting multiple patches.

Figure 4: The FocusDD process of selecting key image patches. Downsampling greatly reduces the computational cost of dataset distillation (see Table [16](#page-20-0) in the Appendix [C.3\)](#page-18-0) and allows the direct use of downsampled images to improve the generalization performance of the synthesized dataset (see Table [1\)](#page-1-0).

where  $\mathbf{Q}, \mathbf{K}$ , and  $\mathbf{V}$  are the query, key, and value matrices from  $\mathbf{x}, d$  is the embedding dimension of  $\mathbf{K}$ , and K is the number of patches. The average attention score s for an image reflects the outcome of a single-head self-attention mechanism. In multi-head self-attention, scores from all attention heads are averaged to yield the final image attention score. The class token  $x_{\text{cls}}$  is processed by a classifier F to derive the category prediction distribution  $p^c$ :

$$
\mathbf{s} = \frac{1}{K} \sum_{k=1}^{K} \mathbf{A}^{k} = [s^{1}, s^{2}, \dots, s^{K}],
$$
  
$$
\mathbf{p}^{c} = \mathcal{F}(\mathbf{x}_{cls}) = [p_{1}^{c}, p_{2}^{c}, \dots, p_{C}^{c}],
$$
 (4)

where C indicates the number of categories.

#### 3.2 Focused Dataset Distillation with Attention

This section introduces FocusDD, a dataset distillation method that reconstructs compiled images by focusing on the target and representative background information of real images. Fig. [3](#page-4-0) and Algorithm [1](#page-17-0) in Appendix [B](#page-16-0) provide an overview. Further details are provided below.

Attention-guided Information Extraction. We utilize an attention mechanism to identify and extract regions with the highest attention scores from multiple images, thereby compiling images with enhanced detail. These regions are then combined to form a detailed composite image set, as illustrated in Fig. [3.](#page-4-0) The process initiates by performing the following steps on each image  $x_i \in \mathbb{R}^{H \times W \times Ch}$  within each category-specific subset  $D_c$  of the dataset D: each  $x_i$  is downsampled to  $x'_i$  and segmented into non-overlapping patches of size  $P \times P$ . This downsampling produces  $K = \frac{H}{P} \times \frac{W}{P}$  patches per image, which are subsequently reorganized into the structured form  $\mathbb{R}^{\frac{H}{P} \times \frac{W}{P} \times P^2Ch}$ , with each row and column representing a token. These tokens are embedded and fed into a pre-trained ViT model, yielding predictive distributions  $\mathbf{p}_i^c$  and attention scores embedded and led mto a pre-trained VIT model, yielding predictive distributions  $\mathbf{p}_i$  and attention scores  $s_i \in \mathbb{R}^K$ . Likewise, we reorganize each attention score  $s_i$  into the format  $\frac{H}{P} \times \frac{W}{P}$ . To deter the highest attention score region for each image  $x_i'$ , we introduce an adjustable hyperparameter  $\alpha$ , which specifies the number of patches  $\lfloor \alpha \frac{H}{P} \times \alpha \frac{W}{P} \rfloor$ . We then introduce a realism score  $s_i^{\text{real}}$  to identify the key patch for each image. Specifically, our realism score combines the prediction distribution  $\mathbf{p}_i^c$  of each image with the highest attention region score  $s_i^{\mathrm{area}},$  defined as follows:

$$
s_i^{\text{real}} = \max(\text{softmax}(\mathbf{p}_i^c)) + \eta s_i^{\text{area}},\tag{5}
$$

where  $\eta$  is a balancing factor. Intuitively,  $s_i^{\text{real}}$  indicates the need to select a representative image with a focus on the target region within it. This implies that our selection process should prioritize images that represent the overall scene accurately and emphasize the specific area of interest, ensuring that the target region is well-captured and highlighted in the chosen image.

After calculating the realism score  $s_i^{\text{real}}$ , we associate each score with its corresponding image  $D_c$  and sort the scores in descending order. Based on these scores, we select the top- $M$  images from the sorted  $D<sub>c</sub>$  and <span id="page-6-0"></span>extract the regions with the highest attention scores. The center indices of these high attention regions are determined using the following formula:

$$
(i,j) = \underset{i,j}{\arg\max} \sum_{p,q} s^{i+p-\lfloor \frac{h}{2} \rfloor, j+q-\lfloor \frac{w}{2} \rfloor},\tag{6}
$$

where  $h = \lfloor \alpha \frac{H}{P} \rfloor, w = \lfloor \alpha \frac{W}{P} \rfloor, p \in \{0, 1, \ldots, h-1\},\$ and  $q \in \{0, 1, \ldots, w-1\}.$  Utilizing the positional mapping function  $\rho$ , we translate these indices to the dimensions of the original image  $x_i$ , marking the key information region  $x_i^*$  in the high-resolution image as:

```html
$$
\boldsymbol{x}_{i}^{\star} = \operatorname{area}(\rho((i - \lfloor \alpha \frac{H}{2P} \rfloor, j + \lfloor \alpha \frac{W}{2P} \rfloor), (i + \lfloor \alpha \frac{H}{2P} \rfloor, j - \lfloor \alpha \frac{W}{2P} \rfloor))))
$$
(7)

```

Finally, we compile the identified key patches into a set  $\tilde{T}_c = {\{\mathbf{x}_i^{\star}\}}_{i=1}^M$ , where each sample  $\mathbf{x}_i^{\star}$  is a crop of the high-resolution image containing fine details, thereby preserving maximum informational content for use in the compiled composites. To further enhance the diversity of the synthesized images, we randomly select N low-resolution sampled images from  $D<sub>c</sub>$  that were not chosen as key information patches. These images are weighted based on their prediction confidence scores and added to  $T_c' = \{x_i'\}_{i=1}^N$  as a background information set. Fig. [4](#page-5-0) illustrates the process of selecting the set of key patches.

Information Reconstruction. The size of key patches is typically smaller than the target distilled images. Directly using these key patches as distilled images can result in sparse information distribution in the pixel space, thereby reducing the effectiveness of the learning model [\(Shen and Xing,](#page-13-0) [2022;](#page-13-0) [Yin et al.,](#page-14-0) [2024;](#page-14-0) [Yun et al.,](#page-14-0) [2021\)](#page-14-0). As shown in Table [8,](#page-10-0) using distilled image sets composed solely of key patches leads to a decreased model performance. Therefore, we combine the set of images containing key information patches  $\tilde{T}_c$  with the set of low-resolution images  $T'_c$  to supplement the class category  $c$  information with the typical context in which they appear. Specifically, we randomly select m patches from  $\tilde{T}_c$  and n low-resolution images from  $T_c'$  each time. The selected images are then concatenated to compile the final composite image  $\tilde{x_j}$ :

$$
\tilde{\boldsymbol{x}}_j = \text{concat}((\{\boldsymbol{x}_j^{\star}\}_{j=1}^m \subset \tilde{T}_c), (\{\boldsymbol{x}_j'\}_{j=1}^n \subset T'_c)).
$$
\n(8)

By default, we set the combined total of patches and images to  $m+n=4$  (see Fig. [6](#page-21-0) in the Appendix [C.3\)](#page-18-0), where  $m=3$  represents the selection of three patches from the key information patch collection  $\tilde{T}_c$ , and  $n=1$ corresponds to selecting one low-resolution image from the background information collection  $T_c'$  (Table [8\)](#page-10-0). Following the RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0) and SRe<sup>2</sup>L [\(Yin et al.,](#page-14-0) [2024\)](#page-14-0), we apply a soft label approach [\(Shen](#page-13-0) [and Xing,](#page-13-0) [2022\)](#page-13-0) to the compiled images. This method generates region-level soft labels  $\tilde{y}_j^k = \ell(\phi_{\theta_{D'}}(\tilde{x}_j^k))$ , where  $\tilde{\boldsymbol{x}}_j^k$  is the k-th region in the distilled image, and  $\tilde{y}_j^k$  is its corresponding soft label.

By iterating over each category  $c$  in  $D$ , performing the information extraction and image reconstruction processes, and adding the generated images and labels  $\{\tilde{x}_j, y_j\}$  to the distilled dataset D', we ultimately obtain the complete distilled dataset  $D'$ .

### 3.3 Model Training on Distilled Datasets

After assembling the distillation dataset D', we initiate training of a student model  $\phi_{\theta_S}$  from random initialization using this dataset, in line with strategies proposed by [Yin et al.](#page-14-0) [\(2024\)](#page-14-0) and [Sun et al.](#page-13-0) [\(2024\)](#page-13-0). For classification tasks, the training employs a cross-entropy loss function defined as:

$$
\mathcal{L} = -\sum_{j} \sum_{k} \tilde{y}_{j}^{k} \log \phi_{\theta_{S}}(\tilde{x}_{j}^{k}).
$$
\n(9)

To optimize training efficiency for the detection task, we input the distilled images into YOLOv11x [\(Khanam](#page-12-0) [and Hussain,](#page-12-0) [2024\)](#page-12-0) to compute the classification and bounding box losses and supervise model updates using

<span id="page-7-0"></span>Table 2: Comparison with SOTA baseline dataset distillation methods on the ImageNet-1K dataset. Following the revalidation model, we present the accuracy  $(\%)$  achieved by various architectures on the full ImageNet-1K dataset. Our method significantly outperforms all compared baseline methods. The table highlights the highest accuracy in bold and underlines the second-highest accuracy. For the SCDD [\(Zhou et al.,](#page-14-0) [2024\)](#page-14-0), D3S [\(Loo et al.\)](#page-12-0), and GVBSM [\(Shao et al.,](#page-13-0) [2023\)](#page-13-0) methods, we list the results reported in the original papers.

| Method       |               |                |                             | <b>IPC</b>     |               |                                  |                            |                |
|--------------|---------------|----------------|-----------------------------|----------------|---------------|----------------------------------|----------------------------|----------------|
|              | 1             | 10             | 50                          | 100            | 1             | 10                               | 50                         | 100            |
|              |               |                | ResNet-18 $(69.8 \pm 0.1)$  |                |               |                                  | ResNet-50 $(76.2 \pm 0.1)$ |                |
| $SRe^2L$     | $0.1 \pm 0.1$ | $21.3 \pm 0.6$ | $46.8 \pm 0.2$              | $52.8 \pm 0.3$ | $0.3 \pm 0.1$ | $28.4 \pm 0.1$                   | $55.6 \pm 0.3$             | $61.0 \pm 0.4$ |
| <b>SCDD</b>  |               | $32.1 \pm 0.2$ | $53.1 \pm 0.1$              | $57.9 \pm 0.1$ |               | $38.9 \pm 0.1$                   | $60.9 \pm 0.2$             | $65.8 \pm 0.1$ |
| <b>GVBSM</b> |               | $31.4 \pm 0.5$ | $51.8 \pm 0.4$              | $55.7 \pm 0.4$ |               | $35.4 \pm 0.8$                   | $58.7 \pm 0.3$             | $62.2 \pm 0.3$ |
| <b>RDED</b>  | $6.6 \pm 0.2$ | $42.0 \pm 0.1$ | $56.5 \pm 0.1$              | $60.8 \pm 0.4$ | $5.7 \pm 0.1$ | $42.3 \pm 0.3$                   | $64.8 \pm 0.6$             | $68.2 \pm 0.2$ |
| DSS          |               | $39.1 \pm 0.3$ | $60.2 \pm 0.1$              | $63.0 \pm 0.2$ |               | $41.9 \pm 0.7$                   | $65.8 \pm 0.1$             | $68.2 \pm 0.1$ |
| FocusDD      | $8.8 \pm 0.2$ | $45.3 \pm 0.1$ | $61.7 \pm 0.1$              | $62.0 \pm 0.2$ | $6.8 \pm 0.1$ | $46.3 \pm 0.2$                   | $69.1 \pm 0.3$             | $71.0 \pm 0.1$ |
|              |               |                | MobileNet-V2 $(71.8\pm0.1)$ |                |               | EfficientNet-B0 $(76.3 \pm 0.1)$ |                            |                |
| $SRe^2L$     | $0.3 \pm 0.1$ | $10.2 \pm 2.6$ | $31.8 \pm 0.3$              | $42.8 \pm 0.6$ | $0.4 \pm 0.2$ | $11.4 \pm 2.5$                   | $34.8 \pm 0.4$             | $49.6 \pm 0.5$ |
| <b>RDED</b>  | $4.9 \pm 0.6$ | $33.8 \pm 0.6$ | $54.2 \pm 0.2$              | $57.9 \pm 0.6$ | $3.4 \pm 0.2$ | $33.3 \pm 0.9$                   | $57.7 \pm 0.1$             | $63.7 \pm 0.3$ |
| FocusDD      | $5.1 \pm 0.1$ | $34.6 \pm 0.1$ | $58.7 \pm 0.3$              | $62.6 \pm 0.1$ | $4.8 \pm 0.2$ | $40.1 \pm 0.2$                   | $60.7 \pm 0.1$             | $66.6 \pm 0.3$ |

Kullback–Leibler divergence loss. To accelerate training, we use YOLOv11x to generate ground truth (GT) boxes for each synthesized image and train the model following the standard YOLOv11 procedure.

In Appendix [C.2,](#page-17-0) we outline how a model, initially trained on a distilled dataset, undergoes Dynamic Fine-Tuning (DFT) on the data obtained by dynamically sampling the original dataset. This method leads to further performance enhancements across all architectures.

### 4 Experiments

#### 4.1 Experimental Setup

Datasets and Implementation Details. We conducted rigorous and extensive validation of FocusDD on the large-scale ImageNet-1K dataset [\(Deng et al.,](#page-11-0) [2009\)](#page-11-0) to comprehensively evaluate its performance. The ImageNet-1K dataset consists of approximately 1.2 million training images with a resolution of  $224 \times 224$ pixels, spanning 1000 categories. For key patch extraction, we utilized the Deit-S model [\(Touvron et al.,](#page-13-0) [2021\)](#page-13-0), pre-trained by [Hu et al.](#page-12-0) [\(2024\)](#page-12-0). We maintain a constant side ratio  $\alpha$  of 0.8 and  $\eta$  of 30. We set the value of N equal to IPC and M equal to  $3\times$ IPC, effectively limiting the size of the distillation dataset to the total number of pixels in the IPC image. We train target models including ResNet-{18, 50, 101} [\(He et al.,](#page-12-0) [2016\)](#page-12-0), MobileNet-v2 [\(Sandler et al.,](#page-13-0) [2018\)](#page-13-0), and EfficientNet-b0 [\(Tan and Le,](#page-13-0) [2019\)](#page-13-0) to validate the distilled datasets. All models are trained on the distilled dataset for 300 epochs with  $224 \times 224$  image resolution. Our experiments were conducted using an NVIDIA 4090 GPU. Additional experimental details and Tiny-ImageNet [\(Le and Yang,](#page-12-0) [2015\)](#page-12-0) experiments are provided in Appendix [A](#page-15-0) and Table [14](#page-19-0) Appendix [C.2,](#page-17-0) respectively.

Evaluation and Baselines. We compare our approach with several SOTA methods for distilling largescale, high-resolution datasets, including SRe<sup>2</sup>L [\(Yin et al.,](#page-14-0) [2024\)](#page-14-0), SCDD [\(Zhou et al.,](#page-14-0) [2024\)](#page-14-0), GVBSM [\(Shao](#page-13-0) [et al.,](#page-13-0) [2023\)](#page-13-0), D3S [\(Loo et al.\)](#page-12-0) and RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0). In our evaluation process, we generate a unique distillation dataset for each IPC level (1, 10, 50, 100) for FocusDD and reuse it across multiple network architectures.

| Method                    | IPC            |                 |                 |                 |
|---------------------------|----------------|-----------------|-----------------|-----------------|
|                           | 1              | 10              | 50              | 100             |
| SRe2L (Yin et al., 2024)  | 0.6±0.1        | 30.9±0.1        | 60.8±0.5        | 62.8±0.2        |
| SCDD (Zhou et al., 2024)  | -              | 39.6±0.4        | 61.0±0.3        | 65.6±0.2        |
| GVBSM (Shao et al., 2023) | -              | 38.2±0.4        | 61.0±0.4        | 63.7±0.2        |
| RDED (Sun et al., 2024)   | 5.9±0.4        | 42.1±1.0        | 61.2±0.4        | 69.5±0.5        |
| D3S (Loo et al.)          | -              | 42.1±3.8        | 65.3±0.5        | 68.9±0.1        |
| <b>FocusDD</b>            | <b>8.5±0.2</b> | <b>43.1±0.2</b> | <b>69.9±0.2</b> | <b>72.9±0.1</b> |

Table 3: Accuracy comparison  $(\%)$  of SOTA baseline dataset distillation methods using ResNet101 (77.4 $\pm$ 0.2) on ImageNet-1K.

Table 4: Comparison of classification accuracy (%) when training with diffusion-based network generated datasets and FocusDD. ResNet-18 was used as a validation model.

| IPC | DiT (Peebles and Xie, 2023) | MinmaxDiffusion (Gu et al., 2024) | FocusDD        |
|-----|-----------------------------|-----------------------------------|----------------|
| 10  | $39.6 \pm 0.4$              | $44.3 \pm 0.5$                    | $45.3 \pm 0.1$ |
| 50  | $52.9 \pm 0.6$              | $58.6 \pm 0.3$                    | $61.7 \pm 0.1$ |

### 4.2 Performance Evaluation

ImageNet-1K Classification. Tables [2](#page-7-0) and 3 present the experimental results of FocusDD on the ImageNet-1K dataset, showing its significant advantages across various architectures (e.g., ResNet-18, ResNet-50, ResNet-101, MobileNet-V2, EfficientNet-B0) and IPC settings. FocusDD consistently outperforms other methods, especially for low IPCs (1, 10, and 50), achieving higher accuracy, which is crucial for scenarios with limited samples or resource constraints. For instance, on ResNet-18, FocusDD achieves accuracies of 8.8% and 45.3% at IPCs of 1 and 10, respectively, significantly surpassing RDED and D3S. Even for higher IPCs (e.g.,  $IPC = 100$ , FocusDD maintains strong performance, often achieving or nearing the best results on ResNet-50 and EfficientNet-B0. This demonstrates FocusDD's ability to excel under minimal and small-sample data conditions, adapting effectively across different models and IPC configurations.

Additionally, we compare our method with diffusion-based image generation models [\(Gu et al.,](#page-12-0) [2024;](#page-12-0) [Peebles and Xie,](#page-13-0) [2023\)](#page-13-0) in Table 4. Appendix [C.1](#page-16-0) compares FocusDD with Coreset-based selection methods [\(Forgy,](#page-11-0) [1965;](#page-11-0) [Welling,](#page-14-0) [2009\)](#page-14-0) on ImageNet-1K, showing consistent superiority of FocusDD. Table [14](#page-19-0) in Appendix [C.2](#page-17-0) shows FocusDD's strong performance on Tiny-ImageNet, even at low IPCs, aligning with results on ImageNet-1K.

COCO Object Detection. In the object detection task, we use YOLOv11x [\(Khanam and Hussain,](#page-12-0) [2024\)](#page-12-0) as the teacher model to perform soft-supervised training on YOLOv11n and YOLOv11s models from scratch for a total of 100 epochs, with all experimental settings following the official YOLOv11 [\(Khanam and Hussain,](#page-12-0) [2024\)](#page-12-0) configuration. Fig. [1](#page-1-0) shows the mAP performance of the FocusDD-distilled dataset on the COCO validation set under different IPC settings. The figure indicates that as IPC increases, model performance also gradually improves. For example, when IPC is 50, YOLOv11s achieves an mAP of 32.1%. FocusDD performs effectively on object detection tasks because distilled images are composed of multiple patches containing targets, each of which may include objects of interest to the detection model.

#### 4.3 Performance Analysis

Cross-Architecture Generalization. Table [5](#page-9-0) evaluates the impact of different ViT models on FocusDD's performance on ImageNet-1K, using ResNet-18 for validation. The results demonstrate that our method maintains consistent performance across ViT architectures, corroborating the idea that the attention-based key patch selection in FocusDD is similarly effective for also different transformer architectures. Table [1](#page-1-0)

| Distillation                   |                                |                                            | <b>IPC</b>                       |                                         |
|--------------------------------|--------------------------------|--------------------------------------------|----------------------------------|-----------------------------------------|
| Architecture                   | 1                              | 10                                         | 50                               | 100                                     |
| $Deit-S$<br>LV-ViT-S           | $8.8 \pm 0.2$<br>$9.4 \pm 0.3$ | $45.3 \pm 0.1$<br>$45.8 \pm 0.2$           | $61.7 \pm 0.1$<br>$62.3 \pm 0.2$ | $62.0 \pm 0.2$<br>$62.8 \pm 0.1$        |
| RDED<br>50                     |                                | 55.0                                       |                                  |                                         |
| FocusDD<br>40<br>30            |                                | 52.5<br>50.0<br>47.5                       |                                  |                                         |
| Top-1 Accuracy (%)<br>20<br>10 |                                | Top-1 Accuracy (%)<br>45.0<br>42.5<br>40.0 |                                  |                                         |
| 0<br>100<br>200<br>$\Omega$    | 300<br>400<br>epochs           | 37.5<br>35.0<br>500<br>150                 | 200<br>250<br>resolution         | MobileNet-v2<br>ResNet-18<br>300<br>350 |

<span id="page-9-0"></span>Table 5: Impact of different ViT models on FocusDD accuracy.

Figure 5: Model accuracy with varying epoch and resolution. Left: Accuracy changes with training epochs using ResNet-18 as the validation model in the IPC-10 setting. Right: The impact of the image resolution of synthetic dataset on model accuracy.

presents results from fine-tuning pre-trained models for 10 epochs on CIFAR-100 [\(Krizhevsky et al.,](#page-12-0) [2009\)](#page-12-0), Flowers-102 [\(Nilsback and Zisserman,](#page-13-0) [2008\)](#page-13-0), and Food-101 [\(Bossard et al.,](#page-11-0) [2014\)](#page-11-0), with datasets distilled using different methods. Our method shows superior generalization and enhances downstream task performance. Training Epoch and Adaptive Resolution Synthesis. There is a lack of a unified benchmark for comparing methods, such as the number of training steps or used image resolution. This makes it hard to compare all SOTA baselines in the form they were initially presented to the community; e.g., those in [\(Gu et al.,](#page-12-0) [2024\)](#page-12-0) and [\(Kim et al.,](#page-12-0) [2022\)](#page-12-0), conducted across 1000 epochs. Nonetheless, we show the impact of training time on FocusDD and RDED in Fig. 5 (left). Accuracy improves with more training epochs, consistent with the findings of D3S [Loo et al..](#page-12-0) Fig. 5 (right) shows the FocusDD synthesis accuracy at different resolutions, with accuracy improving as resolution increases, demonstrating the effectiveness of adaptive resolution control in image synthesis. Additionally, Table [17](#page-21-0) in Appendix [C.3](#page-18-0) illustrates the impact of input ViT resolution on the curated dataset. Notably, during training, all images are resized to a fixed resolution of 224×224.

Qualitative Analysis. Fig. [13](#page-27-0) in Appendix [E](#page-24-0) visualizes compiled images generated by different SOTA methods. SRe<sup>2</sup>L, SCDD, and GVBSM produce blurrier images, likely due to overreliance on specific models during dataset compression, which hampers generalization. In contrast, RDED and our FocusDD method generate more realistic images by cropping key patches from real image locations. Unlike RDED, our method includes both key patches and contextual backgrounds, enhancing realism and diversity. The attention mechanism used in our method, validated in the vision community [\(Chen et al.,](#page-11-0) [2023a,b;](#page-11-0) [Hu et al.,](#page-12-0) [2024;](#page-12-0) [Rao](#page-13-0) [et al.,](#page-13-0) [2021\)](#page-13-0), improves interpretability and offers deeper insights into dataset distillation.

### 4.4 Ablation study

Effectiveness of Each Technique in FocusDD. To validate the effectiveness of all components within our FocusDD, we conduct ablation studies for each of them. Table [6](#page-10-0) illustrates that all techniques employed in FocusDD are essential for achieving a remarkable final performance. We observed that label reconstruction at

<span id="page-10-0"></span>Table 6: Effectiveness of different technologies in our method on ImageNet-1K. ResNet-18 is used as the validation model with an IPC of 10. From left to right, each column represents an incremental addition of technologies starting with the base method: Coreset Filtering (CF), Add Background Information (ABI), Extracting Key Patches (EKP), Image Reconstruction (IR), and Labels Reconstruction (LR).

| FocusDD (Base) | +CF      | +ABI     | +EKP     | +IR      | +LR      |
|----------------|----------|----------|----------|----------|----------|
| Accuracy (%)   | 18.4±0.3 | 23.6±0.1 | 28.2±0.2 | 30.9±0.1 | 45.3±0.1 |

Table 7: Comparing key patch selection strategies using various metrics, including Herding [\(Welling,](#page-14-0) [2009\)](#page-14-0), K-Means [\(Forgy,](#page-11-0) [1965\)](#page-11-0), and Realism [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0), which are current SOTA methods. All methods are evaluated using ResNet-18 on ImageNet-1K with IPC=10.

| Method       | Random   | Herding  | K-Means  | Realism  | Min-AS   | R-AS     | Max-AS   |
|--------------|----------|----------|----------|----------|----------|----------|----------|
| Accuracy (%) | 37.9±0.5 | 38.4±0.1 | 38.2±0.1 | 42.0±0.1 | 41.6±0.3 | 42.6±0.8 | 45.3±0.1 |

Table 8: The effect of the number of patches in each compiled image. Each synthesized image includes 4 patches, with m key patches and n low-resolution background images. We used ImageNet-1K and MobileNet-v2 with IPC=10 to evaluate different patch configurations.

| Patches  | $m=4, n=0$      | $m=3, n=1$      | $m=2, n=2$      | $m=1, n=3$      | $m=0, n=4$      |
|----------|-----------------|-----------------|-----------------|-----------------|-----------------|
| Accuracy | $32.6  \pm 0.3$ | $34.6  \pm 0.1$ | $34.2  \pm 0.2$ | $33.2  \pm 0.2$ | $31.8  \pm 0.5$ |

the patch level significantly improves accuracy, consistent with the findings of previous methods [\(Sun et al.,](#page-13-0) [2024;](#page-13-0) [Yin et al.,](#page-14-0) [2024;](#page-14-0) [Zhou et al.,](#page-14-0) [2024\)](#page-14-0).

Effectiveness of Selecting Key Patches Through Realism Score. Table 7 demonstrates the effectiveness of different key patch selection strategies using realism scores. Our method, which utilizes the maximum attention score (Max-AS) as a score metric, surpasses all compared methods. Specifically, Max-AS achieves a 14.3% accuracy improvement over the current SOTA methods—Herding [\(Welling,](#page-14-0) [2009\)](#page-14-0), K-Means [\(Forgy,](#page-11-0) [1965\)](#page-11-0), and Realism [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0). Compared to its variants, the minimum attention score (Min-AS) and random attention score (R-AS), Max-AS achieves the highest accuracy by focusing on target regions while selecting the same key patches and representative low-resolution images.

Impact of the number of patches in compiled images. By adjusting the number of key patches m and the number of low-resolution images n, each compiled image is composed of m key patches and n low-resolution images. We adopt the combination that achieves the highest accuracy as our default setting, namely, composing the final image with three key patches and one low-resolution image containing global information. Table [13](#page-18-0) in Appendix [C.3](#page-18-0) shows the impact of the balancing factor  $\eta$  on FocusDD's performance. We select  $\eta = 30$  as the default value.

### 5 Conclusion

In this paper, we introduce FocusDD, a novel method that employs attention mechanisms to guide data distillation effectively for large-scale and high-resolution datasets. FocusDD extracts key patches from image target regions, ensuring critical information and realism, and combines them with low-resolution contextual backgrounds to create distilled images for training. This diversifies the dataset and enhances model generalization. Additionally, FocusDD is invariant to the resolution of target images, making it a flexible and performant choice for data distillation regardless of the underlying image resolution requirements. Extensive experiments and ablation studies demonstrate FocusDD's effectiveness and offer insights into applying deep learning to large-scale data and complex models for both classification and object detection tasks.

## <span id="page-11-0"></span>References

- L. Bossard, M. Guillaumin, and L. Van Gool. Food-101–mining discriminative components with random forests. In Computer vision–ECCV 2014: 13th European conference, zurich, Switzerland, September 6-12,  $2014$ , proceedings, part VI 13, pages 446–461. Springer, 2014.
- N. Carion, F. Massa, G. Synnaeve, N. Usunier, A. Kirillov, and S. Zagoruyko. End-to-end object detection with transformers. In *European conference on computer vision*, pages 213–229. Springer, 2020.
- G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu. Dataset distillation by matching training trajectories. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 4750–4759, 2022.
- G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu. Generalizing dataset distillation via deep generative prior. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 3739–3748, 2023.
- M. Chen, M. Lin, K. Li, Y. Shen, Y. Wu, F. Chao, and R. Ji. Cf-vit: A general coarse-to-fine method for vision transformer. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 37, pages 7042–7052, 2023a.
- M. Chen, M. Lin, Z. Lin, Y. Zhang, F. Chao, and R. Ji. Smmix: Self-motivated image mixing for vision transformers. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 17260–17270, 2023b.
- P. Chrabaszcz, I. Loshchilov, and F. Hutter. A downsampled variant of imagenet as an alternative to the cifar datasets. arXiv preprint arXiv:1707.08819, 2017.
- J. Cui, R. Wang, S. Si, and C.-J. Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In International Conference on Machine Learning, pages 6565–6590. PMLR, 2023.
- J. Deng, W. Dong, R. Socher, L.-J. Li, K. Li, and L. Fei-Fei. Imagenet: A large-scale hierarchical image database. In 2009 IEEE conference on computer vision and pattern recognition, pages 248–255. Ieee, 2009.
- Z. Deng and O. Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. Advances in Neural Information Processing Systems, 35:34391–34404, 2022.
- A. Dosovitskiy, L. Beyer, A. Kolesnikov, D. Weissenborn, X. Zhai, T. Unterthiner, M. Dehghani, M. Minderer, G. Heigold, S. Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. In International Conference on Learning Representations, 2020.
- J. Du, Y. Jiang, V. Y. Tan, J. T. Zhou, and H. Li. Minimizing the accumulated trajectory error to improve dataset distillation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 3749–3758, 2023.
- V. Elvira, L. Martino, and C. P. Robert. Rethinking the effective sample size. International Statistical Review, 90(3):525–550, 2022.
- V. Feldman and C. Zhang. What neural networks memorize and why: Discovering the long tail via influence estimation. Advances in Neural Information Processing Systems, 33:2881–2891, 2020.
- E. W. Forgy. Cluster analysis of multivariate data: efficiency versus interpretability of classifications. biometrics, 21:768–769, 1965.
- R. Gontijo-Lopes, S. Smullin, E. D. Cubuk, and E. Dyer. Tradeoffs in data augmentation: An empirical study. In International Conference on Learning Representations, 2021.

- <span id="page-12-0"></span>J. Gu, S. Vahidian, V. Kungurtsev, H. Wang, W. Jiang, Y. You, and Y. Chen. Efficient dataset distillation via minimax diffusion. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), 2024.
- Z. Guo, K. Wang, G. Cazenavette, H. Li, K. Zhang, and Y. You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023.
- K. He, X. Zhang, S. Ren, and J. Sun. Deep residual learning for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 770–778, 2016.
- Y. Hu, Y. Cheng, A. Lu, Z. Cao, D. Wei, J. Liu, and Z. Li. Lf-vit: Reducing spatial redundancy in vision transformer for efficient image recognition. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 38, pages 2274–2284, 2024.
- A. Ignatov, R. Timofte, A. Kulik, S. Yang, K. Wang, F. Baum, M. Wu, L. Xu, and L. Van Gool. Ai benchmark: All about deep learning on smartphones in 2019. In 2019 IEEE/CVF International Conference on Computer Vision Workshop (ICCVW), pages 3617–3635. IEEE, 2019.
- Z.-H. Jiang, Q. Hou, L. Yuan, D. Zhou, Y. Shi, X. Jin, A. Wang, and J. Feng. All tokens matter: Token labeling for training better vision transformers. In M. Ranzato, A. Beygelzimer, Y. Dauphin, P. Liang, and J. W. Vaughan, editors, Advances in Neural Information Processing Systems, volume 34, pages 18590–18602, 2021.
- R. Khanam and M. Hussain. Yolov11: An overview of the key architectural enhancements. arXiv preprint arXiv:2410.17725, 2024.
- J.-H. Kim, J. Kim, S. J. Oh, S. Yun, H. Song, J. Jeong, J.-W. Ha, and H. O. Song. Dataset condensation via efficient synthetic-data parameterization. In International Conference on Machine Learning, pages 11102–11118. PMLR, 2022.
- A. Kirillov, E. Mintun, N. Ravi, H. Mao, C. Rolland, L. Gustafson, T. Xiao, S. Whitehead, A. C. Berg, W.-Y. Lo, et al. Segment anything. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 4015–4026, 2023.
- A. Krizhevsky, G. Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- Y. Le and X. Yang. Tiny imagenet visual recognition challenge. CS 231N, 7(7):3, 2015.
- S. Lee, S. Chun, S. Jung, S. Yun, and S. Yoon. Dataset condensation with contrastive signals. In International Conference on Machine Learning, pages 12352–12364. PMLR, 2022.
- T.-Y. Lin, M. Maire, S. Belongie, J. Hays, P. Perona, D. Ramanan, P. Dollár, and C. L. Zitnick. Microsoft coco: Common objects in context. In Computer Vision–ECCV 2014: 13th European Conference, Zurich, Switzerland, September 6-12, 2014, Proceedings, Part V 13, pages 740–755. Springer, 2014.
- S. Liu, K. Wang, X. Yang, J. Ye, and X. Wang. Dataset distillation via factorization. Advances in neural information processing systems, 35:1100–1113, 2022.
- Z. Liu, Y. Lin, Y. Cao, H. Hu, Y. Wei, Z. Zhang, S. Lin, and B. Guo. Swin transformer: Hierarchical vision transformer using shifted windows. In Proceedings of the IEEE/CVF international conference on computer vision, pages 10012–10022, 2021.
- N. Loo, A. Maalouf, R. Hasani, M. Lechner, A. Amini, and D. Rus. Large scale dataset distillation with domain shift. In Forty-first International Conference on Machine Learning.
- Y. Lu, X. Chen, Y. Zhang, J. Gu, T. Zhang, Y. Zhang, X. Yang, Q. Xuan, K. Wang, and Y. You. Can pre-trained models assist in dataset distillation? arXiv preprint arXiv:2310.03295, 2023.

- <span id="page-13-0"></span>K. Meding, L. M. S. Buschoff, R. Geirhos, and F. A. Wichmann. Trivial or impossible–dichotomous data difficulty masks model differences (on imagenet and beyond). *arXiv preprint arXiv:2110.05922*, 2021.
- T. Nguyen, R. Novak, L. Xiao, and J. Lee. Dataset distillation with infinitely wide convolutional networks. Advances in Neural Information Processing Systems, 34:5186–5198, 2021.
- M.-E. Nilsback and A. Zisserman. Automated flower classification over a large number of classes. In 2008 Sixth Indian conference on computer vision, graphics  $\mathscr B$  image processing, pages 722–729. IEEE, 2008.
- M. Paul, S. Ganguli, and G. K. Dziugaite. Deep learning on a data diet: Finding important examples early in training. Advances in Neural Information Processing Systems, 34:20596–20607, 2021.
- W. Peebles and S. Xie. Scalable diffusion models with transformers. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pages 4195–4205, 2023.
- A. Radford, J. W. Kim, C. Hallacy, A. Ramesh, G. Goh, S. Agarwal, G. Sastry, A. Askell, P. Mishkin, J. Clark, et al. Learning transferable visual models from natural language supervision. In International conference on machine learning, pages 8748–8763. PMLR, 2021.
- Y. Rao, W. Zhao, B. Liu, J. Lu, J. Zhou, and C.-J. Hsieh. Dynamicvit: Efficient vision transformers with dynamic token sparsification. Advances in neural information processing systems, 34:13937–13949, 2021.
- T. Ridnik, E. Ben-Baruch, A. Noy, and L. Zelnik-Manor. Imagenet-21k pretraining for the masses. arXiv preprint arXiv:2104.10972, 2021.
- M. Sandler, A. Howard, M. Zhu, A. Zhmoginov, and L.-C. Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In Proceedings of the IEEE conference on computer vision and pattern recognition, pages 4510–4520, 2018.
- S. Shao, Z. Yin, M. Zhou, X. Zhang, and Z. Shen. Generalized large-scale data condensation via various backbone and statistical matching. arXiv preprint arXiv:2311.17950, 2023.
- Z. Shen and E. Xing. A fast knowledge distillation framework for visual recognition. In European conference on computer vision, pages 673–690, 2022.
- D. Su, J. Hou, G. Li, R. Togo, R. Song, T. Ogawa, and M. Haseyama. Generative dataset distillation based on diffusion model. arXiv preprint arXiv:2408.08610, 2024.
- P. Sun, B. Shi, D. Yu, and T. Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, 2024.
- C. Szegedy, W. Liu, Y. Jia, P. Sermanet, S. Reed, D. Anguelov, D. Erhan, V. Vanhoucke, and A. Rabinovich. Going deeper with convolutions. In *Proceedings of the IEEE CVPR*, pages 1–9, 2015.
- H. Tan, S. Wu, F. Du, Y. Chen, Z. Wang, F. Wang, and X. Qi. Data pruning via moving-one-sample-out. Advances in Neural Information Processing Systems, 36, 2024.
- M. Tan and Q. Le. Efficientnet: Rethinking model scaling for convolutional neural networks. In International conference on machine learning, pages 6105–6114. PMLR, 2019.
- M. Toneva, A. Sordoni, R. T. d. Combes, A. Trischler, Y. Bengio, and G. J. Gordon. An empirical study of example forgetting during deep neural network learning. arXiv preprint arXiv:1812.05159, 2018.
- H. Touvron, M. Cord, M. Douze, F. Massa, A. Sablayrolles, and H. Jégou. Training data-efficient image transformers & distillation through attention. In International conference on machine learning, pages 10347–10357. PMLR, 2021.

- <span id="page-14-0"></span>A. Vaswani, N. Shazeer, N. Parmar, J. Uszkoreit, L. Jones, A. N. Gomez, Ł. Kaiser, and I. Polosukhin. Attention is all you need. Advances in neural information processing systems, 30, 2017.
- K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You. Cafe: Learning to condense dataset by aligning features. In 2022 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR), pages 12186–12195, 2022. doi: 10.1109/CVPR52688.2022.01188.
- T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros. Dataset distillation. arXiv preprint arXiv:1811.10959, 2018.
- M. Welling. Herding dynamical weights to learn. In Proceedings of the 26th annual international conference on machine learning, pages 1121–1128, 2009.
- W. Yang, Y. Zhu, Z. Deng, and O. Russakovsky. What is dataset distillation learning? arXiv preprint arXiv:2406.04284, 2024.
- D. Yin, R. Kannan, and P. Bartlett. Rademacher complexity for adversarially robust generalization. In International conference on machine learning, pages 7085–7094. PMLR, 2019.
- Z. Yin and Z. Shen. Dataset distillation in large data era. arXiv e-prints, pages arXiv–2311, 2023.
- Z. Yin, E. Xing, and Z. Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. Advances in Neural Information Processing Systems, 36, 2024.
- S. Yun, S. J. Oh, B. Heo, D. Han, J. Choe, and S. Chun. Re-labeling imagenet: from single to multi-labels, from global to localized labels. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 2340–2350, 2021.
- L. Zhang, J. Zhang, B. Lei, S. Mukherjee, X. Pan, B. Zhao, C. Ding, Y. Li, and D. Xu. Accelerating dataset distillation via model augmentation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 11950–11959, 2023.
- B. Zhao and H. Bilen. Dataset condensation with differentiable siamese augmentation. In International Conference on Machine Learning, pages 12674–12685. PMLR, 2021.
- B. Zhao and H. Bilen. Synthesizing informative training samples with gan. arXiv preprint arXiv:2204.07513, 2022.
- B. Zhao and H. Bilen. Dataset condensation with distribution matching. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision, pages 6514–6523, 2023.
- B. Zhao, K. R. Mopuri, and H. Bilen. Dataset condensation with gradient matching. arXiv preprint arXiv:2006.05929, 2020.
- M. Zhou, Z. Yin, S. Shao, and Z. Shen. Self-supervised dataset distillation: A good compression is all you need. arXiv preprint arXiv:2404.07976, 2024.
- Y. Zhou, E. Nezhadarya, and J. Ba. Dataset distillation using neural feature regression.  $arXiv$  2206.00719, 2022.

<span id="page-15-0"></span>

## Appendix

In the appendix, we provide details omitted in the main text, including:

- Section A: Implementation Details.
- Section [B:](#page-16-0) Focused Dataset Distillation Algorithm.
- Section [C:](#page-16-0) Further Experimental Results.
- Section [D:](#page-21-0) Theoretical Analysis.
- Section [E:](#page-24-0) Sample Visualizations of Synthetic Data.

### A Implementation Details

### A.1 Pre-training ViT Models

For the ImageNet-1K dataset, we directly use the model pre-trained by LF-ViT [\(Hu et al.,](#page-12-0) [2024\)](#page-12-0), which is based on the implementations of Deit-S [\(Hu et al.,](#page-12-0) [2024\)](#page-12-0) and LV-ViT-S [\(Jiang et al.,](#page-12-0) [2021\)](#page-12-0). This model performs inference at both the standard resolution of  $224 \times 224$  and a higher resolution of  $288 \times 288$ , efficiently extracting crucial information patches for dataset distillation. To further reduce inference time, we disable the Focus stage in the LF-ViT implementation. More details and features of LF-ViT can be found on the official website. For the lower resolution and smaller scale Tiny-ImageNet dataset, we train a modified version of the Deit-S-based LF-ViT [\(Hu et al.,](#page-12-0) [2024\)](#page-12-0) from scratch to extract key information patches. Specifically, we reduce the model's depth to 4 layers, set the patch size to  $4\times4$ , adjust the embedding dimension to 192, and reduce the number of heads to 3. This modified model is trained from scratch using the same hyperparameters as those used for ImageNet-1K.

### A.2 FocusDD Implementation Details

We maintain a fixed side ratio  $\alpha = 0.8$  and a balancing factor  $\eta = 30$  for both the ImageNet-1K and Tiny-ImageNet datasets. To compile each image  $\tilde{x}_j$  in the distilled dataset  $D'$ , we set N and M to IPC and 3×IPC, respectively. The compile process involves concatenating three key patches from the key information collection  $\tilde{T_c}$  and one low-resolution background image from  $T_c'$ , resulting in the compiled image as described by Eq. [8.](#page-6-0) For instance, at an IPC of 100, we select 300 key information patches and 100 downsampled low-resolution images with background information, ensuring the synthesis of a diverse and representative image. This approach adapts to different IPC values to accurately reflect the dataset's variability. Aligned with techniques from  $S\text{Re}^2L$  [\(Yin et al.,](#page-14-0) [2024\)](#page-13-0) and RDED [\(Sun et al.,](#page-13-0) 2024), we employ Fast Knowledge Distillation [\(Shen and Xing,](#page-13-0) [2022\)](#page-13-0) to relabel distilled images. Each distilled image  $\tilde{x}_j$  is randomly cropped into several patches, with their coordinates recorded within  $\tilde{x}_j$ . Soft labels  $\tilde{y}_j^k$  are generated and stored for each k-th patch. These labels are aggregated to construct a comprehensive label  $\tilde{y}_j$  for each image, facilitating nuanced and accurate labeling reflective of the diverse visual features captured in the compiled images.

Training on Distilled Dataset. We use a model with the same architecture as the validation model, pre-trained on the corresponding original and full datasets, to generate soft labels for the synthesized images. For Tiny-ImageNet, our teacher model is pre-trained on the complete Tiny-ImageNet dataset, following the hyperparameters in [\(Yin et al.,](#page-14-0) [2024\)](#page-14-0). When training the validation model on the distilled Tiny-ImageNet dataset, we use the hyperparameters shown in Table [9.](#page-16-0) For ImageNet-1K, all teacher models use pre-trained models from the torchvision library. When training the validation model on the distilled ImageNet-1K dataset, we follow the parameters in Table [10.](#page-16-0) Both datasets are augmented by CutMix with a mix probability  $p = 1.0$  and a beta distribution  $\beta = 1.0$ .

For the object detection task, we selected samples from the ImageNet-1K dataset corresponding to the categories in COCO2017 [\(Lin et al.,](#page-12-0) [2014\)](#page-12-0) and generated a dataset based on the IPC settings. YOLOv11x [\(Khanam](#page-12-0)

<span id="page-16-0"></span>

| Config                 | Value             |
|------------------------|-------------------|
| Optimizer              | SGD               |
| Base learning rate     | 0.2               |
| Weight decay           | $1e-4$            |
| Optimizer momentum     | 0.9               |
| Batch size             | 256               |
| Learning rate schedule | Cosine decay      |
| Training epoch         | 300               |
| Augmentation           | RandomResizedCrop |

Table 9: Tiny-ImageNet training hyper-parameters.

| Config                 | Value                           |
|------------------------|---------------------------------|
| Optimizer              | AdamW                           |
| Base learning rate     | 0.001                           |
| Weight decay           | 0.01                            |
| Optimizer momentum     | $\beta_1, \beta_2 = 0.9, 0.999$ |
| Batch size             | 128                             |
| Learning rate schedule | Cosine decay                    |
| Training epoch         | 300                             |
| Augmentation           | RandomResizedCrop               |

Table 10: ImageNet-1K training hyper-parameters.

[and Hussain,](#page-12-0) [2024\)](#page-12-0) was used as the teacher model to annotate this dataset. Then, YOLOv11s and YOLOv11n were trained from scratch on the annotated dataset, and their performance was evaluated on the COCO2017 validation set. All training hyperparameters were kept identical to the official YOLOv11 [\(Khanam and](#page-12-0) [Hussain,](#page-12-0) [2024\)](#page-12-0) configuration.

Dynamic Fine-Tuning Parameter Settings. During the Dynamic Fine-Tuning (DFT) process (detailed in Appendix [C.2\)](#page-17-0), we randomly select images with the same IPC from the original dataset in each iteration to form a new dataset for fine-tuning. The hyperparameters for fine-tuning match those used for training the validation model on the synthesized dataset. We set the learning rate to 0.00025, with 50 epochs and a batch size of 64. The learning rate for MobileNet-v2 during DFT is set to 0.001.

## B Focused Dataset Distillation Algorithm

Algorithm [1](#page-17-0) outlines FocusDD's key patch information extraction and image reconstruction. In the implementation, these tasks are executed in batches, allowing for parallel processing. Table [15](#page-20-0) shows the time required to synthesize 100 images.

### C Further Experimental Results

### C.1 Coreset Selection

Comparison with Coreset Selection Baselines. In this evaluation, we use ResNet-18 as a validation model on the ImageNet-1K dataset with IPC set to 10, comparing it to a dataset extraction strategy based on coreset selection. We evaluate the top-1 validation accuracy achieved by three distinct Coreset selection methodologies: (1) Random selection; (2) Herding, as introduced by [Welling](#page-14-0) [\(2009\)](#page-14-0); and (3) K-Means

<span id="page-17-0"></span>Table 11: Comparison of different Coreset selection-based dataset distillation baselines. All methods use ResNet-18 as the validation model and IPC=10.

| Dataset       | Random                 | Herding                | K-Means                | FocusDD                 |
|---------------|------------------------|------------------------|------------------------|-------------------------|
| Tiny-ImageNet | $7.5 	ext{	extpm} 0.1$ | $9.0 	ext{	extpm} 0.3$ | $8.9 	ext{	extpm} 0.2$ | $51.5 	ext{	extpm} 0.1$ |
| ImageNet-1K   | $4.4 	ext{	extpm} 0.1$ | $5.8 	ext{	extpm} 0.1$ | $5.5 	ext{	extpm} 0.1$ | $45.3 	ext{	extpm} 0.1$ |

clustering, based on [Forgy](#page-11-0) [\(1965\)](#page-11-0). The results detailed in Table 11 demonstrate that the performance of these selection techniques is significantly compromised when applied independently for dataset distillation. In contrast, our FocusDD achieves substantial accuracy improvements of 39.5% on ImageNet-1K and 38.5% on Tiny-ImageNet, respectively.

Algorithm 1 Focused Dataset Distillation with Attention **Input:** Dataset D, pre-trained ViT model,  $\alpha$ ,  $\eta$ ,  $M$ ,  $N$ ,  $m$ ,  $n$ Output: Distilled dataset D′ 1: for each category-specific subset  $D_c \subset D$  do 2: for each image  $x_i \in D_c$  do 3: Downsample  $x_i$  to  $x'_i$  and segment into non-overlapping patches of size  $P \times P$ 4: Embed patches and feed into ViT model 5: Obtain predictive distributions  $\mathbf{p}_i^c$  and attention scores  $\mathbf{s}_i \in \mathbb{R}^K$ 6: Use the predefined  $\alpha$  to determine the size of the patch 7: Calculate realism score  $s_i^{\text{real}}$  by Eq. [5](#page-5-0) and associate it with the corresponding image  $x_i$ . 8: end for 9: Sort image  $D<sub>c</sub>$  by each image's realism score in descending order 10: Select the top-M images and obtain the center indices of the key patch regions by Eq. [6](#page-6-0) 11: Extract key patches  $x_i^*$  by Eq. [7](#page-6-0) 12: Add key patches into set  $\tilde{T}_c$ 13: Randomly select N downsampled low-resolution images in  $D_c$  from non-top-M images 14: Add selected downsampled low-resolution images to set  $T_c'$ 15: **for**  $x_m^* \in \tilde{T}_c$  and  $x_n' \in T'_c$  **do** 16: Randomly select m key patches from  $\tilde{T}_c$  and n downsampled images from  $T'_c$ 17: Concatenate to compile composite image  $\tilde{x}_j$  by Eq. [8](#page-6-0) 18: Apply soft label approach to  $\tilde{x}_j$ 19: Add  $\{\tilde{\bm{x}}_i, y_i\}$  to distilled dataset D' 20: end for 21: end for 22: return Distilled dataset D′

### C.2 Dynamic Fine-Tuning

Following the training of model  $\phi_{\theta_S}$  on the distilled dataset D', we implement the Dynamic Fine-Tuning (DFT) process. The DFT process involves fine-tuning the model on subsets of the original dataset that are dynamically sampled at each epoch. To preserve consistency with the structural properties of the synthetic dataset, images are randomly selected at an IPC level from each category to form new datasets for fine-tuning. This strategy is systematically applied throughout each epoch, introducing variability and generating a unique dataset for fine-tuning in every cycle. This approach significantly enhances the diversity of the data without additional training overhead, thereby boosting the model's generalization ability across diverse data representations. Furthermore, the DFT methodology not only capitalizes on the attributes of synthetic data but also closely aligns the model's performance with real-world data distributions, culminating in notable

<span id="page-18-0"></span>Table 12: Our FocusDD incorporates dynamic fine-tuning to further improve performance. It is worth noting that to further highlight the accuracy improvement brought by dynamic fine-tuning, the accuracy of FocusDD is based on the results after training for 1000 epochs.

| Architecture             | Method             |                |                | <b>IPC</b>     |                |
|--------------------------|--------------------|----------------|----------------|----------------|----------------|
|                          |                    | 1              | 10             | 50             | 100            |
| ResNet-18 (69.8)         | FocusDD            | $10.7 \pm 0.2$ | $52.5 \pm 0.1$ | $63.1 \pm 0.1$ | $68.0 \pm 0.2$ |
|                          | $FocusingDD + DFT$ | $14.7 \pm 0.1$ | $57.6 + 0.1$   | $65.8 + 0.2$   | $69.1 \pm 0.1$ |
| $ResNet-50 (76.2)$       | FocusDD            | $6.92 \pm 0.1$ | $56.5 + 0.2$   | $70.1 + 0.3$   | $71.1 + 0.2$   |
|                          | $FocusingD + DFT$  | $12.3 + 0.2$   | $62.9+0.2$     | $72.8 + 0.2$   | $74.3 \pm 0.2$ |
| ResNet-101 (77.4)        | FocusDD            | $7.3 + 0.2$    | $53.8 + 0.2$   | $71.5 + 0.2$   | $73.5 + 0.1$   |
|                          | $FocusingDD + DFT$ | $14.7 + 0.3$   | $58.3 + 0.2$   | $72.6 + 0.3$   | $76.4 \pm 0.1$ |
| MobileNet-V2 $(71.8)$    | FocusDD            | $8.4 + 0.1$    | $49.5 + 0.1$   | $61.6 + 0.3$   | $66.0 + 0.1$   |
|                          | $FocusingDD + DFT$ | $12.1 + 0.3$   | $56.0 + 0.1$   | $66.4 + 0.1$   | $69.0 \pm 0.2$ |
| EfficientNet-B0 $(76.3)$ | FocusDD            | $12.7 + 0.2$   | $50.4 + 0.2$   | $67.9 + 0.1$   | $68.5 \pm 0.2$ |
|                          | $FocusingD + DFT$  | $17.6 \pm 0.4$ | $59.9 \pm 0.2$ | $73.4 \pm 0.1$ | $74.5 \pm 0.2$ |

Table 13: Impact of  $\eta$  on FocusDD performance. We use MobileNet-v2 as the validation model on the ImageNet-1k dataset, with IPC set to 10.

|                                                                                                                       | 10 | - 20 | - 30 | 40. | -50 | -100- |
|-----------------------------------------------------------------------------------------------------------------------|----|------|------|-----|-----|-------|
| Accuracy   $32.4\pm0.2$ $33.1\pm0.2$ $34.2\pm0.2$ <b>34.6</b> $\pm$ <b>0.1</b> $34.2\pm0.2$ $33.6\pm0.4$ $32.8\pm0.3$ |    |      |      |     |     |       |

enhancements in performance.

ImageNet-1K Datsset. Table 12 presents the experimental results of training FocusDD for 1000 epochs and combining it with DFT on the ImageNet-1K dataset. We find that DFT further improves the performance of FocusDD across all architectures. In particular, when  $IPC=100$ , FocusDD + DFT demonstrates exceptionally small declines in accuracy—0.7%, 1.9%, 1.0%, 2.8%, and 1.8% across the evaluated models—almost achieving performance equivalent to training with the complete dataset. These minimal accuracy losses highlight the robustness of FocusDD when augmented by DFT, effectively leveraging the combined strengths of focused data distillation and iterative fine-tuning. The success of this approach underscores that merging FocusDD with DFT offers a powerful and efficient strategy for minimizing accuracy losses in high-scale learning environments, making it particularly suitable for scenarios where resources are limited but high performance is imperative.

Tiny-ImageNet Dataset. Table [14](#page-19-0) evaluates our method, FocusDD, integrated with DFT on the Tiny-ImageNet dataset, showing similar trends as observed with the ImageNet-1K dataset. Notably, using EfficientNet-b0 at an IPC of 100, FocusDD not only matches but also exceeds the performance of baseline models by  $1.1\pm0.1\%$ . This improvement likely stems from DFT's random selection of IPC samples each round, enhancing the diversity of training data and thus boosting performance. This result highlights the benefits of combining FocusDD with DFT to optimize performance under data constraints.

### C.3 Additional Experiments

Compiled Time and Memory Consumption. Table [15](#page-20-0) presents the compiled time and memory consumption when utilizing a single RTX-4090 GPU on the ImageNet-1K dataset. Unlike  $SRe^{2}L$ , which consumes substantial resources, FocusDD significantly reduces both compiled time and memory usage. Specifically, FocusDD cuts the compiled time down to 8.67 seconds for Deit-S and 10.72 seconds for LV-ViT-S, while maintaining peak memory usage below 7 GB for Deit-S and slightly above 8 GB for LV-ViT-S [\(Jiang](#page-12-0)

<span id="page-19-0"></span>[Table 14: Comparison with SOTA baseline dataset distillation methods on the Tiny-ImageNet dataset. In the](#page-12-0) first column, we present the accuracy (%) achieved by various architectures on the full Tiny-ImageNet dataset. Our method significantly outperforms all compared baseline methods, as demonstrated in the table, even without the use of Dynamic Fine-Tuning (DFT). Incorporating DFT leads to a marked improvement in our method's accuracy. The table highlights the highest accuracy in bold and underlines the second-highest accuracy. For the SCDD [\(Zhou et al.,](#page-14-0) [2024\)](#page-14-0) and GVBSM [\(Shao et al.,](#page-13-0) [2023\)](#page-13-0) methods, we list the results reported in the original papers.

| Architecture             | Method                   |                |                  | <b>IPC</b>     |                |
|--------------------------|--------------------------|----------------|------------------|----------------|----------------|
|                          |                          | $\mathbf{1}$   | 10               | 50             | 100            |
|                          | $SRe^2L$                 | $2.62 \pm 0.1$ | $16.1 \pm 0.2$   | $41.1 \pm 0.4$ | $49.7{\pm}0.3$ |
|                          | <b>SCDD</b>              |                | $31.6 \pm 0.1$   | $45.9 \pm 0.2$ |                |
| $ResNet-18(59.6)$        | <b>GVBSM</b>             |                | $47.6 \pm 0.3$   | $51.0 \pm 0.4$ |                |
|                          | <b>RDED</b>              | $9.7 \pm 0.4$  | $41.9 \pm 0.2$   | $58.2 \pm 0.1$ | $59.1 \pm 0.1$ |
|                          | FocusDD (Ours)           | $16.5 \pm 0.2$ | $49.4 \pm 0.1$   | $56.7 \pm 0.1$ | $59.2 \pm 0.1$ |
|                          | $FocusDD + DFT$ (Ours)   | $21.2 \pm 0.1$ | $51.1 \pm 0.1$   | $56.9 \pm 0.1$ | $59.4 \pm 0.1$ |
|                          | $SRe^2L$                 | $2.0 \pm 0.4$  | $15.5 \pm 0.5$   | $42.2 \pm 0.5$ | $51.2 \pm 0.4$ |
|                          | <b>GVBSM</b>             |                | $48.7 \pm 0.2$   | $52.1 \pm 0.3$ |                |
| ResNet-50 (62.8)         | <b>RDED</b>              | $8.1 \pm 0.3$  | $45.3 \pm 0.2$   | $61.6 \pm 0.3$ | $62.6 \pm 0.1$ |
|                          | FocusDD (Ours)           | $14.6 \pm 0.3$ | $53.4 \pm 0.1$   | $59.8 \pm 0.2$ | $62.0 \pm 0.2$ |
|                          | $FocusDD + DFT$ (Ours)   | $19.9 \pm 0.2$ | $54.1 \pm 0.1$   | $60.9 \pm 0.2$ | $62.2 \pm 0.2$ |
|                          | $SRe^2L$                 | $1.9 + 0.1$    | $14.6 \pm 1.1$   | $42.5 \pm 0.2$ | $51.5 \pm 0.3$ |
| ResNet-101 (67.0)        | <b>GVBSM</b>             |                | $48.8 \pm 0.4$   | $52.3 \pm 0.1$ |                |
|                          | <b>RDED</b>              | $3.8 + 0.1$    | $22.9 \pm 3.3$   | $41.2 \pm 0.4$ | $65.2 \pm 1.1$ |
|                          | FocusDD (Ours)           | $13.2 \pm 0.2$ | $55.5 \pm 0.3$   | $63.2 \pm 0.2$ | $66.4 \pm 0.2$ |
|                          | $FocusingD + DFT$ (Ours) | $19.4 \pm 0.2$ | $56.3 {\pm} 0.2$ | $64.1 \pm 0.2$ | $67.0 \pm 0.1$ |
|                          | $SRe^2L$                 | $2.0 \pm 0.3$  | $7.3 \pm 0.2$    | $19.5 \pm 0.4$ | $22.7 \pm 0.6$ |
| MobileNet-V2 $(45.2)$    | <b>RDED</b>              | $4.1 \pm 0.3$  | $27.4 \pm 0.3$   | $40.1 \pm 0.2$ | $42.6 \pm 0.3$ |
|                          | FocusDD (Ours)           | $5.8 \pm 0.2$  | $34.8 \pm 0.2$   | $42.2 \pm 0.1$ | $44.6 \pm 0.2$ |
|                          | $FocusingD + DFT$ (Ours) | $5.9 + 0.3$    | $36.6 \pm 0.2$   | $43.6 \pm 0.1$ | $45.0{\pm}0.3$ |
|                          | $SRe^2L$                 | $1.0 \pm 0.3$  | $7.8 \pm 0.4$    | $17.5 \pm 0.7$ | $20.9 \pm 0.3$ |
| EfficientNet-B0 $(41.6)$ | <b>RDED</b>              | $1.3 \pm 0.1$  | $18.3 \pm 0.4$   | $38.2 \pm 0.3$ | $40.4 \pm 0.2$ |
|                          | FocusDD (Ours)           | $7.5 \pm 0.1$  | $32.9 \pm 0.2$   | $40.4 \pm 0.2$ | $41.4 \pm 0.1$ |
|                          | $FocusingD + DFT$ (Ours) | $9.0 \pm 0.1$  | $33.5 \pm 0.2$   | $41.2 \pm 0.3$ | $42.7 \pm 0.1$ |

[et al.,](#page-12-0) [2021\)](#page-12-0). Compared with RDED, FocusDD demonstrates a competitive advantage by achieving a more balanced utilization of time and GPU memory, thereby presenting a resource-efficient solution for dataset distillation.

The high efficiency of FocusDD is attained through a strategy of down-sampling images before their input into the ViT model. This approach not only reduces the computational load but also enables a more flexible allocation of GPU resources through adaptive resizing of mini-batches. This efficiency is primarily due to the memory demands in our distillation process, which occur mainly during the parallel extraction of key informative patches within a mini-batch. Furthermore, the optimization-free nature of FocusDD means that the distillation time per image depends on the size of the pre-trained ViT model used.

**Scaling up to Higher Resolutions.** When the input resolution of ViT is expanded from  $224 \times 224$  to  $288 \times 288$ , under the same hyperparameters, we evaluate the accuracy of compiled images using ResNet-18 and MobileNet-v2 on the ImageNet-1K dataset, as shown in Table [17.](#page-21-0) We discover that despite increasing the resolution of the image input to ViT from  $224 \times 224$  to  $288 \times 288$ , there is a slight decrease in accuracy. This phenomenon could be attributed to two factors. Firstly, a larger image resolution makes it more difficult to locate targets within the image, potentially leading to a decrease in the accuracy of the compiled dataset.

<span id="page-20-0"></span>Table 15: Compiled time and memory consumption on ImageNet-1K using a single RTX-4090 GPU. Time Cost is measured in seconds for generating 100 images simultaneously. Peak GPU memory usage is recorded for a batch size of 100, following the official  $SRe^{2}L$  [\(Yin et al.,](#page-14-0) [2024\)](#page-14-0) implementation. RDED-All indicates selection for all images in each category, whereas RDED only a random sample of 300 images per category.

| Distillation Architecture | Method         | Time Cost (s) | Peak Memory (GB) |
|---------------------------|----------------|---------------|------------------|
| ResNet-18                 | SRe2L          | 211.32        | 9.14             |
|                           | RDED           | 3.99          | 1.57             |
|                           | RDED-All       | 26.34         | 8.63             |
| MobileNet-V2              | SRe2L          | 378.32        | 12.93            |
|                           | RDED           | 6.50          | 2.35             |
|                           | RDED-All       | 31.27         | 11.06            |
| EfficientNet-B0           | SRe2L          | 441.24        | 11.92            |
|                           | RDED           | 7.32          | 2.34             |
|                           | RDED-All       | 37.83         | 10.96            |
| Deit-S                    | FocusDD (Ours) | 8.67          | 6.84             |
| LV-ViT-S                  | FocusDD (Ours) | 10.72         | 8.57             |

Table 16: Comparative analysis of the accuracy and computational cost (measured in FLOPs) of training Deit-S on original versus downsampled images of ImageNet-1K.

| Resolutions | $224 	imes 224$ | $112	imes 112$ |
|-------------|-----------------|----------------|
| Accuracy    | 79.8%           | 73.3%          |
| FLOPs       | 4.60G           | 1.10G          |

Secondly, when training the validation model from scratch, all images are resized to the resolution of  $224 \times 224$ . Reducing a higher-resolution image to this lower standard may result in more significant information loss.

**Impact of**  $\eta$  **on performance.** Table [13](#page-18-0) presents the accuracy of FocusDD across varying  $\eta$  values. A smaller  $\eta$  (e.g.,  $\eta$ =0) denotes that representative images are selected based exclusively on the ViT model's prediction confidence scores, with subsequent target area selection guided by the attention scores of these images. Conversely, a larger  $\eta$  (e.g.,  $\eta$ =100) implies that representative images are chosen solely based on the highest attention area scores, followed by target area localization using the same attention scores. We adopt a moderate  $\eta$  value of 30, which balances the representativeness of the images with the importance of their target areas, thereby achieving optimal accuracy.

The advantages of downsampling. The FocusDD synthetic dataset uses downsampled images to locate target regions for the following reasons: (1) Significant computational savings: As shown in Table 16, downsampling reduces FLOPs by 4.2 times. (2) Facilitates dataset synthesis: It allows us to directly select low-resolution background images from the downsampled images to synthesize the final distilled image.

Impact of the number of patches on performance. Fig. [6](#page-21-0) illustrates the impact of the number of patches in synthetic images on performance. We observed that as the number of patches increases, performance gradually decreases. This is because more patches reduce the resolution of each patch, making it difficult to accurately locate the target. Conversely, when the number of patches is 1, although the resolution is higher, the lack of diversity in the synthetic dataset leads to reduced performance. Considering these factors, we set the default number of patches to 4 to achieve optimal accuracy.

Applications of synthetic datasets in continuous learning. In Fig. [7,](#page-21-0) we used ResNet18 and performed a 5-step validation on TinyImageNet to demonstrate FocusDD's performance in continual learning. The results show that FocusDD consistently surpasses the random baseline and matches or slightly exceeds SRe<sup>2</sup>L [\(Yin et al.,](#page-14-0) [2024\)](#page-14-0) as the number of classes increases from 40 to 200. This highlights its effectiveness in maintaining high accuracy and robustly adapting to new classes.

Comparison of learning efficiency. Fig. [8](#page-21-0) clearly shows the practical results of our attention-based

<span id="page-21-0"></span>Table 17: When the input resolution for ViT is increased from  $224 \times 224$  to  $288 \times 288$ , we evaluate the accuracy of the compiled images generated by FocusDD. All accuracies were obtained after training for 1000 epochs on their respective datasets.

| Architec-            | <b>IPC</b>   |                |                |                |
|----------------------|--------------|----------------|----------------|----------------|
| ture                 |              | 10             | 50             | 100            |
| <b>R18</b>           | $10.7 + 0.2$ | $52.5 + 0.1$   | $63.1 + 0.1$   | $68.0 + 0.2$   |
| R <sub>18</sub> #288 | $9.6 + 0.2$  | $52.6 \pm 0.1$ | $64.0 \pm 0.1$ | $67.7 \pm 0.2$ |
| Mv2                  | $8.4 + 0.1$  | $49.5 + 0.1$   | $61.6 + 0.3$   | $66.0 + 0.1$   |
| $M2\#288$            | $7.7 + 0.1$  | $50.1 \pm 0.1$ | $61.5 + 0.2$   | $64.2 + 0.2$   |

approach, with FocusDD demonstrating higher learning efficiency compared to RDED. The higher Hessian matrix [\(Yang et al.,](#page-14-0) [2024\)](#page-14-0) trace values indicate that FocusDD not only adapts faster to new data but also absorbs basic data features more deeply, which is crucial for achieving high generalization in complex tasks.

Image /page/21/Figure/3 description: A line graph shows the Top-1 Accuracy (%) on the y-axis versus the Number of Patches on the x-axis. The graph displays two lines: a solid red line with square markers representing ResNet-18, and a dashed blue line with diamond markers representing MobileNet-v2. The ResNet-18 line starts at approximately 50% accuracy with 1 patch, peaks at about 52.5% with 3 patches, then decreases to around 42.5% with 10 patches, 41.5% with 16 patches, and 38.5% with 25 patches. The MobileNet-v2 line starts at approximately 47.5% accuracy with 1 patch, increases to about 49% with 3 patches, then drops sharply to 36.5% with 10 patches, continues to decrease to 35% with 16 patches, and ends at 33% with 25 patches. The x-axis ranges from 0 to 25, with tick marks at 0, 5, 10, 15, 20, and 25. The y-axis ranges from 32.5 to 52.5, with tick marks every 2.5 units.

Image /page/21/Figure/4 description: This is a line graph showing the Top-1 Accuracy (%) on the y-axis against the Number of classes on the x-axis. There are three lines representing different methods: FocusDD (red squares), SRe²L (green circles), and Random (blue diamonds). The FocusDD line starts at approximately 68% accuracy with 40 classes and decreases to about 57% with 200 classes. The SRe²L line starts at about 48% accuracy with 40 classes, increases to around 52% with 80 classes, and then gradually decreases to about 51% with 200 classes. The Random line starts at approximately 43% accuracy with 40 classes and decreases to about 41.5% with 200 classes, with a slight dip around 120 classes.

Image /page/21/Figure/5 description: A line graph displays the trace values over iterations for three different methods: FocusDD (Ours), RDED, and Real. The x-axis represents iterations from 0 to 1000, and the y-axis represents trace values from 0 to 35000. The FocusDD (Ours) line, shown in black, starts at 0, peaks at approximately 33000 at iteration 200, and then steadily decreases to around 4000 by iteration 1000. The RDED line, in yellow, also starts at 0, peaks at approximately 23000 at iteration 150, and then declines to around 3000 by iteration 1000. The Real line, in blue, begins at 0, gradually increases to a peak of about 15000 at iteration 650, and then slowly decreases to around 7500 by iteration 1000. The graph includes a legend identifying each line and grid lines for better readability.

of patches in each distilled image learning on Tiny-ImageNet. on accuracy.

Figure 6: The impact of the number Figure 7: 5-step class-incremental

Figure 8: Curvature of loss landscapes with real-vs-distilled data.

## D Theoretical Analysis

### D.1 Background and Definitions

To analyze how the dataset distillation with an attention-based region selection affects the generalization ability of models on a testing dataset, we employ Rademacher Complexity [Yin et al.](#page-14-0) [\(2019\)](#page-14-0) as a theoretical framework. We first present the setup and the analysis of our proposed FocusDD method, followed by the empirical validation and the insights.

**Original Dataset** D. The original dataset, denoted as  $D$ , consists of  $|D|$  samples, represented by  $\{\boldsymbol{x}_i\}_{i=1}^{|D|}.$ 

Distilled Dataset  $D'$ . The distilled dataset,  $D'$ , is created by merging m samples from D based on key regions identified by an attention mechanism such as a Vision Transformer (ViT) and  $n$  samples with background information. This results in  $D'$  samples,  $\{\tilde{x}_i\}_{i=1}^{|D'|}$ , where  $|D'| < |D|$ .

Rademacher Complexity. Rademacher Complexity measures the capacity of a class of functions to fit random noise, providing a metric for the complexity and generalization capability of hypothesis classes:

$$
\hat{R}_D(\mathcal{H}) = \mathbb{E}_{\sigma}\left[\sup_{h \in \mathcal{H}} \frac{1}{|D|} \sum_{i=1}^{|D|} \sigma_i h(\boldsymbol{x}_i)\right],
$$

Table 18: Rademacher Complexity Comparison with the same IPC. Naïve denotes randomly selecting |D'| samples from the original dataset, RDED concatenates  $(m + n)$  random sub-region samples.  $\tau$  is a regression parameter due to selecting only the sub-regions.

| Method         | $\tilde{\boldsymbol{x}}_i$                                                              | $ D'_\text{eff} $                  |
|----------------|-----------------------------------------------------------------------------------------|------------------------------------|
| Naïve          | $\boldsymbol{x}_i$                                                                      | $ D' $                             |
| <b>RDED</b>    | concatenate $(\{\boldsymbol{x}_j^{\text{rand}}\}_{j=1}^{m+n})$                          | $ D'  \times (m+n) \tau$           |
| FocusDD (Ours) | concatenate $(\{\boldsymbol{x}_q^\star\}_{q=1}^m, \{\boldsymbol{x}_l^\prime\}_{l=1}^n)$ | $ D'  \times (m \gamma + n \beta)$ |

where  $\sigma_i$  are independent random variables taking values +1 or −1 with equal probability. We apply this metric when evaluating the distilled datasets because it can provide insight into whether the distillation process preserves the richness of the hypothesis space or if it overly simplifies the dataset, potentially losing important variances needed for higher generalization.

### D.2 Impact of Dataset Distillation of FocusDD

For the distilled dataset  $S'$ , the Rademacher Complexity becomes:

$$
\hat{R}_{D'}(\mathcal{H}) = \mathbb{E}_{\sigma} \left[ \sup_{h \in \mathcal{H}} \frac{1}{|D'|} \sum_{i=1}^{|D'|} \sigma_i h(\tilde{x}_i) \right].
$$

Each distilled data instance  $\tilde{x}_i = \text{concatenate}(\{\boldsymbol{x}_q^*\}_{q=1}^m, \{\boldsymbol{x}_i'\}_{l=1}^n)$ , where  $\boldsymbol{x}^*$  represents the key sub-region data and  $x'$  means the down-scaled low resolution data with backgound information.

Note that the term  $1/|D'|$  determines the scaling of the sum of fits to random labels (noise) in the Rademacher Complexity formula. When analyzing a dataset that has undergone distillation to produce  $D'$ , where each sample  $\tilde{x}_i$  aggregates the informational content of multiple samples from the original dataset, the actual number of samples |D'| might not accurately reflect the dataset's complexity. Instead, the Efficient Sample Size (ESS) [\(Elvira et al.,](#page-11-0) [2022\)](#page-11-0) is applied to represent the number of independent observations in a dataset that would provide the same amount of information as the actual dataset, which can be noted as  $|D'_{\text{eff}}|$ . If  $|D'_{\text{eff}}|$  represents a more accurate measure of the independent information content in  $D'$ , the complexity measure can be adjusted to:

$$
\hat{R}_{D'}(\mathcal{H}) = \mathbb{E}_{\sigma} \left[ \sup_{h \in \mathcal{H}} \frac{1}{|D'_{\text{eff}}|} \sum_{i=1}^{|D'|} \sigma_i h(\tilde{\boldsymbol{x}}_i) \right].
$$

This adjustment recognizes that the effective diversity and informational independence in D′ might be greater than simply counting  $|D'|$ , hence potentially leading to a more accurate estimation of how the hypothesis class  $H$  will perform.

The complexity induced by each new sample  $\tilde{x}_i$  can reduce the variance among samples, as they inherently represent a more uniform distribution of the key features and contexts of the original dataset. The formula for Rademacher Complexity has to consider the effective sample size  $|D'_{\text{eff}}|$  that accounts for this aggregation:

$$
|D'_{\text{eff}}| = |D'| \times (m * \gamma + n * \beta),
$$

where  $\gamma$  and  $\beta$  represent the degression parameters due to selecting only the key regions or using down-scaled data, which range from 0 to 1. The setting  $\gamma = \beta = 1$  means that we naively concatenate  $m + n$  original data instances.

Similarly, we can determine  $\tilde{x}_i$  and  $|D'_{\text{eff}}|$  for two baseline methods as shown in Table. 18: Naïve and RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0). A higher  $|D'_{\text{eff}}|$  indicates that each sample in  $D'$  contains more "independent-like" information than initially apparent, suggesting that  $D'$  may exhibit a lower Rademacher Complexity than

Image /page/23/Figure/0 description: This image contains three plots labeled (a), (b), and (c). Plot (a) is a line graph showing training loss over epochs, with two lines representing 'FocusDD (Ours)' in blue and 'RDED' in red. Both lines start at a training loss of approximately 11 and decrease over 1000 epochs, with the red line showing a faster initial decrease. Plot (b) is a line graph showing training error (%) over epochs, also with two lines for 'FocusDD (Ours)' (blue) and 'RDED' (red). Both lines start at 100% error and decrease over 1000 epochs, with the red line consistently showing a lower error than the blue line. Plot (c) is a histogram titled 'Distribution of Image Values', showing the frequency of image values for 'Original Images' (blue), 'RDED Images' (orange), and 'FocusDD Images' (green). The x-axis represents the 'Value' and the y-axis represents 'Frequency'. The distributions are centered around values between 35 and 45.

Figure 9: The diversity of the compiled dataset is assessed by analyzing the training loss and accuracy metrics on the compiled image training set. (a) Training loss. (b) Training error. (c) The signal-to-noise distribution of images within the same category for the full dataset and those distilled by RDED and FocusDD. Training loss on compiled images. All methods employ MobileNet-v2 and are executed on the ImageNet-1K dataset with  $IPC = 10$ .

expected if assessed solely based on  $|D'|$ . Generally, a lower Rademacher Complexity correlates with better generalization capabilities, indicating that models trained on  $D'$  might generalize better than anticipated based solely on  $|D'|$ . This enhanced generalization is why RDED and our proposed method significantly outperform the Naïve approach, which relies on random sample selection.

Our method employs strategies to achieve a larger  $|D'_{\text{eff}}|$  than RDED. Our realism score  $s_i^{\text{real}}$  combines the predictive confidence score and the maximum attention region score. When selecting samples, it does not only consider the information richness of the samples but also the information density of the target regions within these samples. Together, these factors improve  $|D'_{\text{eff}}|$  and enhance generalization capabilities, as confirmed by the results in Table [8](#page-10-0) for  $m \neq 0$ , which reflect the combined effect of both strategies.

Our method, FocusDD, is also designed to reduce model complexity within the Hypothesis Space. Richer samples may enable the functions h in  $H$  to be less complex, as each sample encompasses a broader range of information, potentially simplifying the learning problem. This hypothesis is supported by the results in Table [2,](#page-7-0) which demonstrate that simpler backbone models using FocusDD data achieve outcomes comparable to those of more complex models.

Quantifying the Diversity and SNR of Synthetic Images. We employ the method outlined in [Gontijo-Lopes et al.](#page-11-0) [\(2021\)](#page-11-0) to assess the diversity of compiled images. According to [Gontijo-Lopes et al.](#page-11-0) [\(2021\)](#page-11-0), greater dataset diversity presents more challenges for the training process to converge, often resulting in larger loss values and longer training times. Fig.  $9(a)$  compares the training loss of our method with the SOTA method RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0) on compiled datasets. Initially, our FocusDD method starts with lower loss values but ends with higher losses than RDED after training. Moreover, Fig. 9(b) illustrates significant differences in accuracy tests on the training dataset, indicating that images synthesized using our method are more diverse and thus harder to train. This observation aligns with the conclusions in [Gontijo-Lopes et al.](#page-11-0) [\(2021\)](#page-11-0), confirming that our approach generates more diverse compiled images, making the training process more challenging but potentially leading to more robust models.

Fig.  $9(c)$  illustrates the distribution of signal-to-noise ratios  $(SNR)^1$  for the original dataset and datasets processed by two different distillation methods, within the same category. The SNR distribution of the original images is relatively concentrated, with most values ranging between 30 and 58. The SNR of images processed by RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0) shifts to the right, primarily distributed between 42 and 50. In contrast, images processed by FocusDD exhibit a wider SNR distribution, spanning from 36 to 53. Although the average SNR of RDED images is the highest at 45.1, the average SNR for FocusDD images is 44.0, closer to the

<sup>&</sup>lt;sup>1</sup>We applied a  $3\times3$  Laplacian kernel to filter the images to extract their high-frequency components. Then, we calculated the sum of the absolute values of the convolution results between the image and this matrix, using this to estimate the standard deviation of the noise. Finally, based on the definition of signal-to-noise ratio, we computed the SNR distribution for the entire dataset.

<span id="page-24-0"></span>original dataset's average SNR of 41.7. This indicates that the FocusDD method effectively enhances image quality while preserving the characteristics of the original data, thereby demonstrating superior balanced performance in practical applications.

### D.3 Remarks

The proposed distillation method, FocusDD, is expected to enhance generalization by utilizing more informative and representative samples. The associated reduction in Rademacher Complexity indicates a diminished capacity for fitting random noise, which typically suggests improved performance on unseen data.

The practical implementation may encounter challenges, such as increased computational overhead from processing larger  $\tilde{x}_i$  values. Additionally, there is a risk of information redundancy if the parameters m and n are not optimally selected.

## E Sample Visualizations of Synthetic Data

Fig. 10 presents visualization examples of object detection training samples generated by FocusDD. Fig. [11](#page-25-0) further compares FocusDD-compiled images at different resolutions, showing that as resolution increases, each image patch transitions from capturing only parts of objects to representing entire objects. This trend is quantified in Fig. [5,](#page-9-0) which also highlights a corresponding improvement in accuracy. In Fig. [12,](#page-26-0) we compare the Tiny-ImageNet samples compiled by  $S\text{Re}^2L$  [\(Yin et al.,](#page-14-0) [2024\)](#page-14-0), SCDD [\(Zhou et al.,](#page-14-0) 2024), GVBSM [\(Shao](#page-13-0) [et al.,](#page-13-0) [2023\)](#page-13-0), RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0), and FocusDD. To provide a more comprehensive perspective, Figs. [13](#page-27-0) and [14](#page-28-0) present visualizations of compiled samples from ImageNet-1K. Our compiled data, cropped directly from real image target areas, demonstrates superior realism in texture, shape, and detail compared to  $SRe<sup>2</sup>L$ , SCDD, and GVBSM. Unlike RDED, our method incorporates a low-resolution background in the compiled images, enriching them with additional semantic information. These results collectively demonstrate the higher quality of our compiled data.

Image /page/24/Picture/6 description: This is a grid of images, each containing bounding boxes and labels for various objects. The objects detected include horses, dogs, motorcycles, bowls, broccoli, teddy bears, buses, cars, bears, boats, people, ties, trucks, bottles, birds, bananas, dining tables, carrots, cakes, donuts, umbrellas, and oranges. Some images also include numerical confidence scores associated with the detected objects.

Figure 10: Visualization examples of training samples for object detection generated by FocusDD.

<span id="page-25-0"></span>Image /page/25/Picture/0 description: The image displays a grid of bird images, organized by resolution and species. The left side of the image has labels indicating resolutions: 128², 192², 256², 320², and 384². The grid contains multiple columns, each showcasing pairs of bird images. The top rows predominantly feature images of small birds, possibly dippers, in various natural settings with some snow and water. The bottom rows transition to images of African grey parrots, presented in close-ups and full body shots, often in indoor or less natural environments. The arrangement suggests a comparison of image quality or detail at different resolutions for different bird species.

Figure 11: FocusDD compiled images with different resolutions on the ImageNet-1K dataset. We can clearly see that as the resolution increases, each patch in the compiled image gradually expands from containing only a part of the target to including the entire target, thereby enhancing the accuracy of the image (Fig. [5\)](#page-9-0).

<span id="page-26-0"></span>Image /page/26/Picture/0 description: This image displays a grid of images, likely generated by different algorithms, categorized by object type. The rows are labeled with algorithm names: SRe2L, SCDD, GVBSM, RDED, and FocusDD. The columns are labeled with object categories: Tench, Dipper, Catamount, Butterfly, and Chimpanzee for the top grid, and Academic robe, Wagon, Pail, Miniskirt, and Viaduct for the bottom grid. Each cell in the grid contains a small image, presumably an output from the corresponding algorithm for the given object category. Some images appear to be distorted or corrupted, suggesting a comparison of image generation or reconstruction quality.

Figure 12: Compiled data visualization on Tiny-ImageNet from SRe<sup>2</sup>L [\(Yin et al.,](#page-14-0) [2024\)](#page-14-0), SCDD [\(Zhou et al.,](#page-14-0) [2024\)](#page-14-0), GVBSM [\(Shao et al.,](#page-13-0) [2023\)](#page-13-0), RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0) and FocusDD.

<span id="page-27-0"></span>Image /page/27/Picture/0 description: This image is a grid of images, organized into two main rows and five columns. The columns are labeled at the bottom with the categories: Tiger beetle, Ibex, Guenon, Candle, and Dipper. The rows are labeled on the left side with the categories: SRe2L, SCDD, GVBSM, RDED, and FocusDD. The top half of the grid shows images related to the first set of categories, and the bottom half shows images related to the second set of categories, which are Volcano, Orange, Water jug, Camera, and Pomegranate. Each cell in the grid contains a collage of smaller images, with the exception of the bottom row of the second half, which shows single images. For example, the 'Tiger beetle' column shows various images of tiger beetles in the top half and different views of tiger beetles in the bottom half. The 'Volcano' column in the bottom half shows multiple images of volcanoes erupting. The 'Camera' column in the bottom half displays close-ups of camera lenses and bodies.

Figure 13: Compiled data visualization on ImageNet-1K from SRe<sup>2</sup>L [\(Yin et al.,](#page-14-0) [2024\)](#page-14-0), SCDD [\(Zhou et al.,](#page-14-0) [2024\)](#page-14-0), GVBSM [\(Shao et al.,](#page-13-0) [2023\)](#page-13-0), RDED [\(Sun et al.,](#page-13-0) [2024\)](#page-13-0) and FocusDD.

<span id="page-28-0"></span>Image /page/28/Picture/0 description: The image is a grid of 42 smaller images, arranged in 7 columns and 6 rows. The images depict a variety of subjects, including animals (dogs, wolves, birds, foxes, cats, squirrels, gazelles, crabs), people (playing sports, wearing sombreros, children), objects (castles, cars, tools, locks, plumbing, food, drinks, furniture, electronics, puzzles, flags, boats, industrial equipment), and natural scenes (flamingos, landscapes, water). The overall presentation is a data visualization, likely from a dataset like ImageNet, as indicated by the caption at the bottom which reads "Figure 14: Compiled data visualization on ImageNet 1K from FocusDD."

Figure 14: Compiled data visualization on ImageNet-1K from FocusDD.
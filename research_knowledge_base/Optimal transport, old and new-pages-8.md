<PERSON><PERSON> himself made the following important observation. Consider the transport cost  $c(x,y) = |x - y|$  in the Euclidean plane, and two pairs  $(x_1,y_1), (x_2,y_2)$ , such that an optimal transport maps  $x_1$  to  $y_1$  and  $x_2$ to  $y_2$ . (In our language,  $(x_1, y_1)$  and  $(x_2, y_2)$  belong to the support of an optimal coupling  $\pi$ .) Then either all four points lie on a single line, or the two line segments  $[x_1,y_1], [x_2,y_2]$  do not cross, except maybe at their endpoints. The reason is easy to grasp: If the two lines would cross at a point which is not an endpoint of both lines, then, by triangle inequality we would have

$$
|x_1-y_2|+|x_2-y_1|<|x_1-y_1|+|x_2-y_2|,
$$

and this would contradict the fact that the support of  $\pi$  is c-cyclically monotone. Stated otherwise: Given two crossing line segments, we can shorten the total length of the paths by replacing these lines by the new transport lines  $[x_1,y_2]$  and  $[x_2,y_1]$  (see Figure 8.1).

## Quadratic cost function

For cost functions that do not satisfy a triangle inequality, <PERSON><PERSON>'s argument does not apply, and pathlines can cross. However, it is often the case that the crossing of the curves (with the time variable explicitly taken into account) is forbidden. Here is the most basic example: Consider the quadratic cost function in Euclidean space  $(c(x, y) = |x - y|^2)$ , and let  $(x_1, y_1)$  and  $(x_2, y_2)$  belong to the support of some optimal coupling. By cyclical monotonicity,

Image /page/1/Figure/1 description: This is a diagram showing two lines that intersect. The top left point is labeled x1 and has a dashed line going down and to the left to a point labeled y2. The top right point is labeled x2 and has a dashed line going down and to the right to a point labeled y1. The two solid lines connect x1 to y1 and x2 to y2.

Fig. 8.1. Monge's observation. The cost is Euclidean distance; if  $x_1$  is sent to  $y_1$ and  $x_2$  to  $y_2$ , then it is cheaper to send  $x_1$  to  $y_2$  and  $x_2$  to  $y_1$ .

$$
|x_1 - y_1|^2 + |x_2 - y_2|^2 \le |x_1 - y_2|^2 + |x_2 - y_1|^2. \tag{8.1}
$$

Then let

$$
\gamma_1(t) = (1-t)x_1 + ty_1, \qquad \gamma_2(t) = (1-t)x_2 + ty_2
$$

be the two line segments respectively joining  $x_1$  to  $y_1$ , and  $x_2$  to  $y_2$ . It may happen that  $\gamma_1(s) = \gamma_2(t)$  for some  $s, t \in [0, 1]$ . But if there is a  $t_0 \in (0,1)$  such that  $\gamma_1(t_0) = \gamma_2(t_0) =: X$ , then

$$
|x_1 - y_2|^2 + |x_2 - y_1|^2
$$

$$
= |x_1 - X|^2 + |X - y_2|^2 - 2\langle X - x_1, X - y_2 \rangle + |x_2 - X|^2 + |X - y_1|^2 - 2\langle X - x_2, X - y_1 \rangle
$$

$$
= [t_0^2 + (1 - t_0)^2] (|x_1 - y_1|^2 + |x_2 - y_2|^2) + 4t_0(1 - t_0) \langle x_1 - y_1, x_2 - y_2 \rangle
$$

$$
\leq [t_0^2 + (1 - t_0)^2 + 2t_0(1 - t_0)] (|x_1 - y_1|^2 + |x_2 - y_2|^2)
$$

$$
= |x_1 - y_1|^2 + |x_2 - y_2|^2,
$$

and the inequality is strict unless  $x_1 - y_1 = x_2 - y_2$ , in which case  $\gamma_1(t) = \gamma_2(t)$  for all  $t \in [0, 1]$ . But strict inequality contradicts (8.1). The conclusion is that two distinct interpolation trajectories cannot meet at intermediate times.

It is natural to ask whether this conclusion can be reinforced in a quantitative statement. The answer is yes; in fact there is a beautiful identity:

$$
\left| \left( (1-t)x_1 + ty_1 \right) - \left( (1-t)x_2 + ty_2 \right) \right|^2 = (1-t)^2 |x_1 - x_2|^2 + t^2 |y_1 - y_2|^2
$$
  
$$
+ t(1-t) \left( |x_1 - y_2|^2 + |x_2 - y_1|^2 - |x_1 - y_1|^2 - |x_2 - y_2|^2 \right). (8.2)
$$

To appreciate the consequences of (8.2), let

$$
\gamma_1(t) = (1-t)x_1 + ty_1, \qquad \gamma_2(t) = (1-t)x_2 + ty_2.
$$

Then  $(8.1)$  and  $(8.2)$  imply

$$
\max(|x_1 - x_2|, |y_1 - y_2|) \le \max\left(\frac{1}{t}, \frac{1}{1-t}\right) |\gamma_1(t) - \gamma_2(t)|.
$$

Since  $|\gamma_1(t) - \gamma_2(t)| \le \max(|x_1 - x_2|, |y_1 - y_2|)$  for all  $t \in [0, 1]$ , one can conclude that for any  $t_0 \in (0,1)$ ,

$$
\sup_{0 \le t \le 1} |\gamma_1(t) - \gamma_2(t)| \le \max\left(\frac{1}{t_0}, \frac{1}{1 - t_0}\right) |\gamma_1(t_0) - \gamma_2(t_0)|. \tag{8.3}
$$

(By the way, this inequality is easily seen to be optimal.) So the uniform distance between the *whole paths*  $\gamma_1$  and  $\gamma_2$  can be controlled by their distance at *some* time  $t_0 \in (0,1)$ .

### General statement and applications to optimal transport

For the purpose of a seemingly different problem, Mather (not aware of Monge's work, neither of optimal transport) established an estimate which relies on the same idea as Monge's shortening argument — only much more sophisticated — for general cost functions on Lagrangian manifolds. He obtained a quantitative version of these estimates, in a form quite similar to (8.3).

Mather's proof uses three kinds of assumption: (i) the existence of a second-order differential equation for minimizing curves; (ii) an assumption of regularity of the Lagrangian, and (iii) an assumption of strict convexity of the Lagrangian. To quantify the strict convexity, I shall use the following concept: A continuous function  $L$  on  $\mathbb{R}^n$  will be said to be  $(2+\kappa)$ -convex if it satisfies a (strict) convexity inequality of the form

$$
\frac{L(v) + L(w)}{2} - L\left(\frac{v+w}{2}\right) \ge K|v-w|^{2+\kappa}
$$

for some constant  $K > 0$ .

The next statement is a slight generalization of Mather's estimate; if the reader finds it too dense, he or she can go directly to Corollary 8.2 which is simpler, and sufficient for the rest of this course.

**Theorem 8.1 (Mather's shortening lemma).** Let M be a smooth Riemannian manifold, equipped with its geodesic distance d, and let  $c(x,y)$  be a cost function on  $M \times M$ , defined by a Lagrangian  $L(x,v,t)$ on  $TM \times [0,1]$ . Let  $x_1, x_2, y_1, y_2$  be four points on M such that

$$
c(x_1, y_1) + c(x_2, y_2) \leq c(x_1, y_2) + c(x_2, y_1).
$$

Further, let  $\gamma_1$  and  $\gamma_2$  be two action-minimizing curves respectively joining  $x_1$  to  $y_1$  and  $x_2$  to  $y_2$ . Let V be a bounded neighborhood of the graphs of  $\gamma_1$  and  $\gamma_2$  in  $M \times [0,1]$ , and S a strict upper bound on the maximal speed along these curves. Define

$$
\mathcal{V} := \bigcup_{(x,t)\in V} \Big( x, B_S(0), t \Big) \subset TM \times [0,1].
$$

In words, V is a neighborhood of  $\gamma_1$  and  $\gamma_2$ , convex in the velocity variable.

Assume that:

 $(i)$  minimizing curves for L are solutions of a Lipschitz flow, in the sense of Definition 7.6 (d);

(ii) L is of class  $C^{1,\alpha}$  in V with respect to the variables x and v, for some  $\alpha \in (0,1]$  (so  $\nabla_x L$  and  $\nabla_v L$  are Hölder- $\alpha$ ; Hölder-1 meaning Lipschitz);

(iii) L is  $(2 + \kappa)$ -convex in V, with respect to the v variable.

Then, for any  $t_0 \in (0,1)$ , there is a constant  $C_{t_0} = C(L, \mathcal{V}, t_0)$ , and a positive exponent  $\beta = \beta(\alpha, \kappa)$  such that

$$
\sup_{0 \le t \le 1} d(\gamma_1(t), \gamma_2(t)) \le C_{t_0} d(\gamma_1(t_0), \gamma_2(t_0))^{\beta}.
$$
 (8.4)

Furthermore, if  $\alpha = 1$  and  $\kappa = 0$ , then one can choose  $\beta = 1$  and  $C_{t_0} = C(L, V) / \min(t_0, 1 - t_0).$ 

If L is of class  $C^2$  and  $\nabla_v^2 L > 0$ , then Assumption (iii) will be true for  $\kappa = 0$ , so we have the next corollary:

Corollary 8.2 (Mather's shortening lemma again). Let M be a smooth Riemannian manifold and let  $L = L(x, v, t)$  be a  $C<sup>2</sup>$  Lagrangian on  $TM \times [0,1]$ , satisfying the classical assumptions of Definition 7.6, together with  $\nabla_v^2 L > 0$ . Let c be the cost function associated to L, and let d be the geodesic distance on M. Then, for any compact  $K \subset M$ there is a constant  $C_K$  such that, whenever  $x_1, y_1, x_2, y_2$  are four points in K with

$$
c(x_1, y_1) + c(x_2, y_2) \leq c(x_1, y_2) + c(x_2, y_1),
$$

and  $\gamma_1$ ,  $\gamma_2$  are action-minimizing curves joining respectively  $x_1$  to  $y_1$ and  $x_2$  to  $y_2$ , then for any  $t_0 \in (0,1)$ ,

$$
\sup_{0 \le t \le 1} d(\gamma_1(t), \gamma_2(t)) \le \frac{C_K}{\min(t_0, 1 - t_0)} d(\gamma_1(t_0), \gamma_2(t_0)). \tag{8.5}
$$

The short version of the conclusion is that the distance between  $\gamma_1$  and  $\gamma_2$  is controlled, uniformly in t, by the distance at any time  $t_0 \in (0, 1)$ . In particular, the initial and final distance between these curves is controlled by their distance at any intermediate time. (But the final distance is not controlled by the initial distance!) Once again, inequalities  $(8.4)$  or  $(8.5)$  are quantitative versions of the qualitative statement that the two curves, if distinct, cannot cross except at initial or final time.

**Example 8.3.** The cost function  $c(x, y) = d(x, y)^2$  corresponds to the Lagrangian function  $L(x, v, t) = |v|^2$ , which obviously satisfies the assumptions of Corollary 8.2. In that case the exponent  $\beta = 1$  is admissible. Moreover, it is natural to expect that the constant  $C_K$  can be controlled in terms of just a lower bound on the sectional curvature of M. I shall come back to this issue later in this chapter (see Open Problem 8.21).

**Example 8.4.** The cost function  $c(x, y) = d(x, y)^{1+\alpha}$  does not satisfy the assumptions of Corollary 8.2 for  $0 < \alpha < 1$ . Even if the associated Lagrangian  $L(x, v, t) = |v|^{1+\alpha}$  is not smooth, the equation for minimizing curves is just the geodesic equation, so Assumption (i) in Theorem 8.1 is still satisfied. Then, by tracking exponents in the proof of Theorem 8.1, one can find that (8.4) holds true with  $\beta = (1+\alpha)/(3-\alpha)$ . But this is far from optimal: By taking advantage of the homogeneity of the power function, one can prove that the exponent  $\beta = 1$  is also

admissible, for all  $\alpha \in (0,1)$ . (It is the constant, rather than the exponent, which deteriorates as  $\alpha \downarrow 0$ .) I shall explain this argument in the Appendix, in the Euclidean case, and leave the Riemannian case as a delicate exercise. This example suggests that Theorem 8.1 still leaves room for improvement.

The proof of Theorem 8.1 is a bit involved and before presenting it I prefer to discuss some applications in terms of optimal couplings. In the sequel, if K is a compact subset of  $M$ , I say that a dynamical optimal transport  $\Pi$  is supported in K if it is supported on geodesics whose image lies entirely inside K.

Theorem 8.5 (The transport from intermediate times is locally Lipschitz). On a Riemannian manifold  $M$ , let c be a cost function satisfying the assumptions of Corollary 8.2, let K be a compact subset of M, and let  $\Pi$  be a dynamical optimal transport supported in K. Then  $\Pi$  is supported on a set of geodesics S such that for any two  $\gamma, \widetilde{\gamma} \in S,$ 

$$
\sup_{0 \le t \le 1} d(\gamma(t), \widetilde{\gamma}(t)) \le C_K(t_0) d(\gamma(t_0), \widetilde{\gamma}(t_0)). \tag{8.6}
$$

In particular, if  $(\mu_t)_{0 \leq t \leq 1}$  is a displacement interpolation between any two compactly supported probability measures on M, and  $t_0 \in (0,1)$ is given, then for any  $t \in [0,1]$  the map

$$
T_{t_0 \to t}: \ \gamma(t_0) \longmapsto \gamma(t)
$$

is well-defined  $\mu_{t_0}$ -almost surely and Lipschitz continuous on its domain; and it is in fact the unique solution of the Monge problem between  $\mu_{t_0}$  and  $\mu_t$ . In other words, the coupling  $(\gamma(t_0),\gamma(t))$  is an optimal deterministic coupling.

**Example 8.6.** On  $\mathbb{R}^n$  with  $c(x, y) = |x - y|^2$ , let  $\mu_0 = \delta_0$  and let  $\mu = \text{law}(X)$  be arbitrary. Then it is easy to check that  $\mu_t = \text{law}(tX)$ , and in fact the random geodesic  $\gamma(t)$  is just  $t\gamma(1)$ . So  $\gamma(t) = t\gamma(t_0)/t_0$ , which obviously provides a deterministic coupling. This example is easily adapted to more general geometries (see Figure 8.2).

Proof of Theorem 8.5. The proof consists only in formalizing things that by now may look essentially obvious to the reader. First note

Image /page/6/Figure/1 description: The image shows a diagram illustrating a concept related to probability distributions or measures. On the left side, there is a label "µ0 = δx0", indicating a starting point or a Dirac delta measure at point x0. Several curved lines emanate from this point, fanning out and extending towards the right. On the right side, there is a shaded region labeled "µ1", which appears to be a target distribution or measure. The curved lines seem to represent paths or transformations connecting the initial measure µ0 to the target measure µ1. The shaded region has a grid-like pattern, suggesting it might represent a set or a distribution of points.

Fig. 8.2. In this example the map  $\gamma(0) \rightarrow \gamma(1/2)$  is not well-defined, but the map  $\gamma(1/2) \rightarrow \gamma(0)$  is well-defined and Lipschitz, just as the map  $\gamma(1/2) \rightarrow \gamma(1)$ . Also  $\mu_0$  is singular, but  $\mu_t$  is absolutely continuous as soon as  $t > 0$ .

that  $(e_0,e_1,e_0,e_1)_\#(\Pi\otimes\Pi)=\pi\otimes\pi$ , where  $\pi$  is an optimal coupling between  $\mu_0$  and  $\mu_1$ . So if a certain property holds true  $\pi \otimes \pi$ -almost surely for quadruples, it also holds true  $\Pi \otimes \Pi$ -almost surely for the endpoints of pairs of curves.

Since  $\pi$  is optimal, it is c-cyclically monotone (Theorem 5.10 (ii)), so,  $\pi \otimes \pi(dx\,dy\,d\widetilde{x}\,d\widetilde{y})$ -almost surely,

$$
c(x, y) + c(\widetilde{x}, \widetilde{y}) \le c(x, \widetilde{y}) + c(\widetilde{x}, y).
$$

Thus,  $\Pi \otimes \Pi(d\gamma d\tilde{\gamma})$ -almost surely,

$$
c(\gamma(0), \gamma(1)) + c(\widetilde{\gamma}(0), \widetilde{\gamma}(1)) \le c(\gamma(0), \widetilde{\gamma}(1)) + c(\widetilde{\gamma}(0), \gamma(1)).
$$

Then (8.6) follows from Corollary 8.2.

Let S be the support of  $\Pi$ ; by assumption this is a compact set. Since the inequality (8.6) defines a closed set of pairs of geodesics, actually it has to hold true for all pairs  $(\gamma, \tilde{\gamma}) \in S \times S$ .

Now define the map  $T_{t_0 \to t}$  on the compact set  $e_{t_0}(S)$  (that is, the union of all  $\gamma(t_0)$ , when  $\gamma$  varies over the compact set S), by the formula  $T_{t_0\to t}(\gamma(t_0)) = \gamma(t)$ . This map is well-defined, for if two geodesics  $\gamma$  and  $\tilde{\gamma}$  in the support of  $\Pi$  are such that  $\gamma(t_0) = \tilde{\gamma}(t_0)$ , then inequality (8.6) imposes  $\gamma = \tilde{\gamma}$ . The same inequality shows that  $T_{t_0 \to t}$  is actually Lipschitz-continuous, with Lipschitz constant  $C_K/\min(t_0, 1-t_0)$ .

All this shows that  $(\gamma(t_0), T_{t_0 \to t}(\gamma(t_0)))$  is indeed a Monge coupling of  $(\mu_{t_0}, \mu_t)$ , with a Lipschitz map. To complete the proof of the theorem, it only remains to check the uniqueness of the optimal coupling; but this follows from Theorem 7.30(iii). ⊓⊔

The second application is a result of "preservation of absolute continuity".

Theorem 8.7 (Absolute continuity of displacement interpolation). Let M be a Riemannian manifold, and let  $L(x, v, t)$  be a  $C<sup>2</sup>$ Lagrangian on TM  $\times$  [0,1], satisfying the classical conditions of Definition 7.6, with  $\nabla_v^2 L > 0$ ; let c be the associated cost function. Let  $\mu_0$ and  $\mu_1$  be two probability measures on M such that the optimal cost  $C(\mu_0,\mu_1)$  is finite, and let  $(\mu_t)_{0 \leq t \leq 1}$  be a displacement interpolation between  $\mu_0$  and  $\mu_1$ . If either  $\mu_0$  or  $\mu_1$  is absolutely continuous with respect to the volume on M, then also  $\mu_t$  is absolutely continuous for all  $t \in (0,1)$ .

*Proof of Theorem 8.7.* Let us assume for instance that  $\mu_1$  is absolutely continuous, and prove that  $\mu_{t_0}$  is absolutely continuous  $(0 < t_0 < 1)$ .

First consider the case when  $\mu_0$  and  $\mu_1$  are compactly supported. Then the whole displacement interpolation is compactly supported, and Theorem 8.5 applies, so there is a Lipschitz map  $T$  solving the Monge problem between  $\mu_{t_0}$  and  $\mu_{t_1}$ .

Now if N is a set of zero volume, the inclusion  $N \subset T^{-1}(T(N))$ implies

$$
\mu_{t_0}[N] \le \mu_{t_0}[T^{-1}(T(N))] = (T_{\#}\mu_{t_0})[T(N)] = \mu_1[T(N)],\tag{8.7}
$$

and the latter quantity is 0 since vol  $[T(N)] \leq ||T||_{\text{Lip}}^n$  vol  $[N] = 0$  and  $\mu_1$ is absolutely continuous. So (8.7) shows that  $\mu_{t_0}[N] = 0$  for any Borel set N of zero volume, and this means precisely that  $\mu_{t_0}$  is absolutely continuous.

Actually, the previous computation is not completely rigorous because  $T(N)$  is not necessarily Borel measurable; but this is not serious since  $T(N)$  can still be included in a negligible Borel set, and then the proof can be repaired in an obvious way.

Now let us turn to the general case where  $\mu_0$  and  $\mu_1$  are not assumed to be compactly supported. This situation will be handled by a restriction argument. Assume by contradiction that  $\mu_{t_0}$  is not absolutely continuous. Then there exists a set  $Z_{t_0}$  with zero volume, such that  $\mu_{t_0}[Z_{t_0}] > 0$ . Let  $\mathcal{Z} := \{ \gamma \in \Gamma(M); \ \gamma_{t_0} \in Z_{t_0} \}$ . Then

$$
\Pi[\mathcal{Z}] = \mathbb{P}\left[\gamma_{t_0} \in Z_{t_0}\right] = \mu_{t_0}[Z_{t_0}] > 0.
$$

By regularity, there exists a compact set  $\mathcal{K} \subset \mathcal{Z}$ , such that  $\Pi[\mathcal{K}] > 0$ . Let

$$
\Pi' := \frac{1_{\mathcal{K}} \Pi}{\Pi[\mathcal{K}]},
$$

and let  $\pi' := (e_0, e_1)_\# \Pi'$  be the associated transference plan, and  $\mu'_t =$  $(e_t)_\# \Pi'$  the marginal of  $\Pi'$  at time t. In particular,

$$
\mu'_1 \le \frac{(e_1)_\# \Pi}{\Pi[\mathcal{K}]} = \frac{\mu_1}{\Pi[\mathcal{K}]},
$$

so  $\mu'_1$  is still absolutely continuous.

By Theorem 7.30(ii),  $(\mu_t)$  is a displacement interpolation. Now,  $\mu_{t_0}'$ is concentrated on  $e_{t_0}(\mathcal{K}) \subset e_{t_0}(\mathcal{Z}) \subset Z_{t_0}$ , so  $\mu'_{t_0}$  is singular. But the first part of the proof rules out this possibility, because  $\mu'_0$  and  $\mu'_1$  are respectively supported in  $e_0(\mathcal{K})$  and  $e_1(\mathcal{K})$ , which are compact, and  $\mu'_1$ is absolutely continuous. ⊓⊔

## Proof of Mather's estimates

Now let us turn to the proof of Theorem 8.1. It is certainly more important to grasp the idea of the proof (Figure 8.3) than to follow the calculations, so the reader might be content with the informal explanations below and skip the rigorous proof at first reading.

Idea of the proof of Theorem 8.1. Assume, to fix the ideas, that  $\gamma_1$  and  $\gamma_2$  cross each other at a point  $m_0$  and at time  $t_0$ . Close to  $m_0$ , these two curves look like two straight lines crossing each other, with respective velocities  $v_1$  and  $v_2$ . Now cut these curves on the time interval  $[t_0 - \tau, t_0 + \tau]$  and on that interval introduce "deviations" (like a plumber installing a new piece of pipe to short-cut a damaged region of a channel) that join the first curve to the second, and vice versa.

This amounts to replacing (on a short interval of time) two curves with approximate velocity  $v_1$  and  $v_2$ , by two curves with approximate velocities  $(v_1+v_2)/2$ . Since the time-interval where the modification occurs is short, everything is concentrated in the neighborhood of  $(m_0, t_0)$ , so the modification in the Lagrangian action of the two curves is approximately

$$
(2\tau)\left(2\,L\left(m_0,\frac{v_1+v_2}{2},t_0\right)-\left[L(m_0,v_1,t_0)+L(m_0,v_2,t_0)\right]\right).
$$

Image /page/9/Figure/1 description: The image displays a diagram illustrating the concept of curves and their intersections. Two curves, labeled \gamma1(0) and \gamma2(0), originate from the top and curve downwards, crossing each other in the center. Below the intersection, the curves are labeled \gamma1 and \gamma2, continuing to curve downwards and outwards, with arrows indicating their direction. A dashed circle encloses the central intersection area, highlighting two thick vertical lines that appear to represent a specific region or measurement within the intersection. The overall diagram suggests a mathematical or physical representation of intersecting paths or flows.

Fig. 8.3. Principle of Mather's proof: Let  $\gamma_1$  and  $\gamma_2$  be two action-minimizing curves. If at time  $t_0$  the two curves  $\gamma_1$  and  $\gamma_2$  pass too close to each other, one can devise shortcuts (here drawn as straight lines).

Since  $L(m_0, \cdot, t_0)$  is strictly convex, this quantity is negative if  $v_1 \neq v_2$ , which means that the total action has been *strictly improved* by the modification. But then  $c(x_1,y_2) + c(x_2,y_1) < c(x_1,y_1) + c(x_2,y_2)$ , in contradiction to our assumptions. The only possibility available is that  $v_1 = v_2$ , i.e. at the crossing point the curves have the same position and the same velocity; but then, since they are solutions of a second-order differential inequality, they have to coincide at all times. ⊓⊔

It only remains to make this argument quantitative: If the two curves pass close to each other at time  $t_0$ , then their velocities at that time will also be close to each other, and so the trajectories have to be close to each other for all times in [0, 1]. Unfortunately this will not be so easy.

*Rigorous proof of Theorem 8.1.* Step 1: Localization. The goal of this step is to show that the problem reduces to a local computation that can be performed as if we were in Euclidean space, and that it is sufficient to control the difference of velocities at time  $t_0$  (as in the above sketchy explanation). If the reader is ready to believe in these two statements, then he or she can go directly to Step 2.

For brevity, let  $\gamma_1 \cup \gamma_2$  stand for the union of the images of the minimizing paths  $\gamma_1$  and  $\gamma_2$ . For any point x in  $\text{proj}_M(V)$ , there is a

small ball  $B_{r_x}(x)$  which is diffeomorphic to an open set in  $\mathbb{R}^n$ , and by compactness one can cover a neighborhood of  $\gamma_1 \cup \gamma_2$  by a finite number of such balls  $B_j$ , each of them having radius no less than  $\delta > 0$ . Without loss of generality, all these balls are included in  $proj<sub>M</sub>(V)$ , and it can be assumed that whenever two points  $X_1$  and  $X_2$  in  $\gamma_1 \cup \gamma_2$  are separated by a distance less than  $\delta/4$ , then one of the balls  $B_i$  contains  $B_{\delta/4}(X_1) \cup B_{\delta/4}(X_2).$ 

If  $\gamma_1(t_0)$  and  $\gamma_2(t_0)$  are separated by a distance at least  $\delta/4$ , then the conclusion is obvious. Otherwise, choose  $\tau$  small enough that  $\tau S \leq \delta/4$ (recall that  $S$  is a strict bound on the maximum speed along the curves); then on the time-interval  $[t_0 - \tau, t_0 + \tau]$  the curves never leave the balls  $B_{\delta/4}(X_1) \cup B_{\delta/4}(X_2)$ , and therefore the whole trajectories of  $\gamma_1$  and  $\gamma_2$  on that time-interval have to stay within a single ball  $B_j$ . If one takes into account positions, velocities and time, the system is confined within  $B_i \times B_S(0) \times [0,1] \subset \mathcal{V}$ .

On any of these balls  $B_i$ , one can introduce a Euclidean system of coordinates, and perform all computations in that system (write L in those coordinates, etc.). The distance induced on  $B_i$  by that system of coordinates will not be the same as the original Riemannian distance, but it can be bounded from above and below by multiples thereof. So we can pretend that we are really working with a Euclidean metric, and all conclusions that are obtained, involving only what happens inside the ball  $B_i$ , will remain true up to changing the bounds. Then, for the sake of all computations, we can freely add points as if we were working in Euclidean space.

If it can be shown, in that system of coordinates, that

$$
\left|\dot{\gamma}_1(t_0) - \dot{\gamma}_2(t_0)\right| \leq C \left|\gamma_1(t_0) - \gamma_2(t_0)\right|^\beta, \tag{8.8}
$$

then this means that  $(\gamma_1(t_0), \dot{\gamma}_1(t_0))$  and  $(\gamma_2(t_0), \dot{\gamma}_2(t_0))$  are very close to each other in TM; more precisely they are separated by a distance which is  $O(d(\gamma_1(t_0), \gamma_2(t_0))^{\beta})$ . Then by Assumption (i) and Cauchy-Lipschitz theory this bound will be propagated backward and forward in time, so the distance between  $(\gamma_1(t), \dot{\gamma}_1(t))$  and  $(\gamma_2(t), \dot{\gamma}_2(t))$  will remain bounded by  $O(d(\gamma_1(t_0),\gamma_2(t_0))^{\beta})$ . Thus to conclude the argument it is sufficient to prove (8.8).

Step 2: Construction of shortcuts. First some notation: Let us write  $x_1(t) = \gamma_1(t)$ ,  $x_2(t) = \gamma_2(t)$ ,  $v_1(t) = \dot{\gamma}_1(t)$ ,  $v_2(t) = \dot{\gamma}_2(t)$ , and also  $X_1 = x_1(t_0), V_1 = v_1(t_0), X_2 = x_2(t_0), V_2 = v_2(t_0).$  The goal is to control  $|V_1 - V_2|$  by  $|X_1 - X_2|$ .

Let  $x_{12}(t)$  be defined by

$$
x_{12}(t) = \begin{cases} x_1(t) & \text{for } t \in [0, t_0 - \tau]; \\ \frac{x_1(t) + x_2(t)}{2} + \left(\frac{\tau + t - t_0}{2\tau}\right) \left(\frac{x_2(t_0 + \tau) - x_1(t_0 + \tau)}{2}\right) \\ & \quad + \left(\frac{\tau - t + t_0}{2\tau}\right) \left(\frac{x_1(t_0 - \tau) - x_2(t_0 - \tau)}{2}\right) \\ & \text{for } t \in [t_0 - \tau, t_0 + \tau]; \\ x_2(t) & \text{for } t \in [t_0 + \tau, 1]. \end{cases}
$$

Note that  $x_{12}$  is a continuous function of t; it is a path that starts along  $\gamma_1$ , then switches to  $\gamma_2$ . Let  $v_{12}(t)$  stand for its time-derivative:

$$
v_{12}(t) = \begin{cases} v_1(t) & \text{for } t \in [0, t_0 - \tau]; \\ \frac{v_1(t) + v_2(t)}{2} + \frac{1}{2\tau} \left( \left[ \frac{x_2(t_0 - \tau) + x_2(t_0 + \tau)}{2} \right] - \left[ \frac{x_1(t_0 - \tau) + x_1(t_0 + \tau)}{2} \right] \right) & \text{for } t \in [t_0 - \tau, t_0 + \tau]; \\ v_2(t) & \text{for } t \in [t_0 + \tau, 1]. \end{cases}
$$

Then the path  $x_{21}(t)$  and its time-derivative  $v_{21}(t)$  are defined symetrically (see Figure 8.4). These definitions are rather natural: First we try to construct paths on  $[t_0 - \tau, t_0 + \tau]$  whose velocity is about the half of the velocities of  $\gamma_1$  and  $\gamma_2$ ; then we correct these paths by adding simple functions (linear in time) to make them match the correct endpoints.

I shall conclude this step with some basic estimates about the paths  $x_{12}$  and  $x_{21}$  on the time-interval  $[t_0 - \tau, t_0 + \tau]$ . For a start, note that

$$
\begin{cases} x_{12} - \frac{x_1 + x_2}{2} = -\left(x_{21} - \frac{x_1 + x_2}{2}\right), \\ v_{12} - \frac{v_1 + v_2}{2} = -\left(v_{21} - \frac{v_1 + v_2}{2}\right). \end{cases} \qquad (8.9)
$$

In the sequel, the symbol  $O(m)$  will stand for any expression which is bounded by  $Cm$ , where C only depends on V and on the regularity bounds on the Lagrangian  $L$  on  $\mathcal V$ . From Cauchy–Lipschitz theory and Assumption (i),

$$
|v_1 - v_2|(t) + |x_1 - x_2|(t) = O\Big(|X_1 - X_2| + |V_1 - V_2|\Big),\tag{8.10}
$$

Image /page/12/Figure/1 description: The image shows two curved lines, labeled x12 and x21, that converge towards the center and then diverge outwards. There are two thick black vertical lines in the center where the curves are closest. The lines are drawn in black on a white background. The overall impression is of a diagram illustrating some form of interaction or transformation.

Fig. 8.4. The paths  $x_{12}(t)$  and  $x_{21}(t)$  obtained by using the shortcuts to switch from one original path to the other.

and then by plugging this back into the equation for minimizing curves we obtain

$$
|\dot{v}_1 - \dot{v}_2|(t) = O(|X_1 - X_2| + |V_1 - V_2|).
$$

Upon integration in times, these bounds imply

$$
x_1(t) - x_2(t) = (X_1 - X_2) + O(\tau(|X_1 - X_2| + |V_1 - V_2|)); \quad (8.11)
$$

$$
v_1(t) - v_2(t) = (V_1 - V_2) + O(\tau(|X_1 - X_2| + |V_1 - V_2|)); \quad (8.12)
$$

and therefore also

$$
x_1(t) - x_2(t) = (X_1 - X_2) + (t - t_0) (V_1 - V_2) + O(\tau^2(|X_1 - X_2| + |V_1 - V_2|)).
$$
\n(8.13)

As a consequence of  $(8.12)$ , if  $\tau$  is small enough (depending only on the Lagrangian  $L$ ),

$$
|v_1 - v_2|(t) \ge \frac{|V_1 - V_2|}{2} - O(\tau |X_1 - X_2|). \tag{8.14}
$$

Next, from Cauchy–Lipschitz again,

$$
x_2(t_0+\tau)-x_1(t_0+\tau)=X_2-X_1+\tau(V_2-V_1)+O(\tau^2(|X_1-X_2|+|V_1-V_2|));
$$

and since a similar expression holds true with  $\tau$  replaced by  $-\tau$ , one has

$$
\left[\frac{x_2(t_0+\tau) - x_1(t_0+\tau)}{2}\right] - \left[\frac{x_1(t_0-\tau) - x_2(t_0-\tau)}{2}\right]
$$

$$
= (X_2 - X_1) + O(\tau^2(|X_1 - X_2| + |V_1 - V_2|)), \quad (8.15)
$$

and also

$$
\left[\frac{x_2(t_0+\tau) - x_1(t_0+\tau)}{2}\right] + \left[\frac{x_1(t_0-\tau) - x_2(t_0-\tau)}{2}\right] = \tau(V_2 - V_1) + O\left(\tau^2(|X_1 - X_2| + |V_1 - V_2|)\right) \quad (8.16)
$$

It follows from (8.15) that

$$
v_{12}(t) - \frac{v_1(t) + v_2(t)}{2} = O\left(\frac{|X_1 - X_2|}{\tau} + \tau |V_1 - V_2|\right).
$$
 (8.17)

After integration in time and use of (8.16), one obtains

$$
x_{12}(t) - \frac{x_1(t) + x_2(t)}{2} = \left[ x_{12}(t_0) - \frac{x_1(t_0) + x_2(t_0)}{2} \right] + O\left( |X_1 - X_2| + \tau^2 |V_1 - V_2| \right)
$$

$$
= O\left( |X_1 - X_2| + \tau |V_1 - V_2| \right) \quad (8.18)
$$

In particular,

$$
|x_{12} - x_{21}|(t) = O(|X_1 - X_2| + \tau |V_1 - V_2|).
$$
 (8.19)

Step 3: Taylor formulas and regularity of L. Now I shall evaluate the behavior of  $L$  along the old and the new paths, using the regularity assumption (ii). From that point on, I shall drop the time variable for simplicity (but it is implicit in all the computations). First,

$$
L(x_1, v_1) - L\left(\frac{x_1 + x_2}{2}, v_1\right)
$$

$$
= \nabla_x L\left(\frac{x_1 + x_2}{2}, v_1\right) \cdot \left(\frac{x_1 - x_2}{2}\right) + O(|x_1 - x_2|^{1+\alpha});
$$

similarly

$$
L(x_2, v_2) - L\left(\frac{x_1 + x_2}{2}, v_2\right)
$$

$$
= \nabla_x L\left(\frac{x_1 + x_2}{2}, v_2\right) \cdot \left(\frac{x_2 - x_1}{2}\right) + O(|x_1 - x_2|^{1+\alpha}).
$$

Moreover,

$$
\nabla_x L\left(\frac{x_1 + x_2}{2}, v_1\right) - \nabla_x L\left(\frac{x_1 + x_2}{2}, v_2\right) = O(|v_1 - v_2|^\alpha).
$$

The combination of these three identities, together with estimates (8.11) and (8.12), yields

$$
\left(L(x_1, v_1) + L(x_2, v_2)\right) - \left(L\left(\frac{x_1 + x_2}{2}, v_1\right) + L\left(\frac{x_1 + x_2}{2}, v_2\right)\right)
$$

$$
= O\left(|x_1 - x_2|^{1+\alpha} + |x_1 - x_2| |v_1 - v_2|^{\alpha}\right)
$$

$$
= O\left(|X_1 - X_2|^{1+\alpha} + \tau |V_1 - V_2|^{1+\alpha} + |X_1 - X_2| |V_1 - V_2|^{\alpha} + \tau^{1+\alpha} |V_1 - V_2| |X_1 - X_2|^{\alpha}\right).
$$

Next, in an analogous way,

$$
L(x_{12}, v_{12}) - L
pare(x_{12}, rac{v_1 + v_2}{2}
pare)
$$

$$
= 
abla_v L
pare(x_{12}, rac{v_1 + v_2}{2}
pare) 
cdot 
pare(v_{12} - rac{v_1 + v_2}{2}
pare) + O
pare(|v_{12} - rac{v_1 + v_2}{2}|^{1+\alpha})
pare,
$$

The following equations are presented:

$$
L(x_{21}, v_{21}) - L(x_{21}, \frac{v_1 + v_2}{2})
$$
  
$$
= \nabla_v L(x_{21}, \frac{v_1 + v_2}{2}) \cdot (v_{21} - \frac{v_1 + v_2}{2}) + O(|v_{21} - \frac{v_1 + v_2}{2}|^{1+\alpha}),
$$
  
$$
\nabla_v L(x_{12}, \frac{v_1 + v_2}{2}) - \nabla_v L(x_{21}, \frac{v_1 + v_2}{2}) = O(|x_{12} - x_{21}|^{\alpha}).
$$
  
Combining this with (8.9), (8.17) and (8.19), one finds

Combining this with  $(8.9)$ ,  $(8.17)$  and  $(8.19)$ , one finds

$$
(L(x_{12}, v_{12}) + L(x_{21}, v_{21})) - \left(L(x_{12}, \frac{v_1 + v_2}{2}) + L(x_{21}, \frac{v_1 + v_2}{2})\right)
$$
$$
= O\left(|v_{12} - \frac{v_1 + v_2}{2}|^{1+\alpha} + |v_{12} - \frac{v_1 + v_2}{2}| |x_{12} - x_{21}|^{\alpha}\right)
$$
$$
= O\left(\frac{|X_1 - X_2|^{1+\alpha}}{\tau^{1+\alpha}} + \tau^{1+\alpha}|V_1 - V_2|^{1+\alpha}\right).
$$

2

·

After that,

$$
L\left(x_{12}, \frac{v_1 + v_2}{2}\right) - L\left(\frac{x_1 + x_2}{2}, \frac{v_1 + v_2}{2}\right)
$$

$$
= \nabla_x L\left(\frac{x_1 + x_2}{2}, \frac{v_1 + v_2}{2}\right) \cdot \left(x_{12} - \frac{x_1 + x_2}{2}\right) + O\left(\|x_{12} - \frac{x_1 + x_2}{2}\|^{\text{1+\alpha}}\right),
$$

$$
L\left(x_{21}, \frac{v_1 + v_2}{2}\right) - L\left(\frac{x_1 + x_2}{2}, \frac{v_1 + v_2}{2}\right)
$$

$$
= \nabla_x L\left(\frac{x_1 + x_2}{2}, \frac{v_1 + v_2}{2}\right) \cdot \left(x_{21} - \frac{x_1 + x_2}{2}\right) + O\left(\|x_{21} - \frac{x_1 + x_2}{2}\|^{\text{1+\alpha}}\right),
$$

and now by (8.9) the terms in  $\nabla_x$  cancel each other exactly upon sommation, so the bound (8.18) leads to

2

2

$$
(L(x12,v1+v22)+L(x21,v1+v22))-2L(x1+x22,v1+v22)
$$
$$
=O(|x21-x1+x22|1+α)
$$
$$
=O(|X1-X2|1+α+τ1+α|V1-V2|1+α).
$$

Step 4: Comparison of actions and strict convexity. From our minimization assumption,

$$
\mathcal{A}(x_1)+\mathcal{A}(x_2)\leq \mathcal{A}(x_{12})+\mathcal{A}(x_{21}),
$$

which of course can be rewritten

$$
\int_{t_0-\tau}^{t_0+\tau} \left( L(x_1(t), v_1(t), t) + L(x_2(t), v_2(t), t) - L(x_{12}(t), v_{12}(t), t) - L(x_{21}(t), v_{21}(t), t) \right) dt \le 0. \quad (8.20)
$$

From Step 3, we can replace in the integrand all positions by  $(x_1+x_2)/2$ , and  $v_{12}$ ,  $v_{21}$  by  $(v_1 + v_2)/2$ , up to a small error. Collecting the various error terms, and taking into account the smallness of  $\tau$ , one obtains  $(d$ ropping the t variable again)

$$
\frac{1}{2\tau} \int_{t_0-\tau}^{t_0+\tau} \left\{ L\left(\frac{x_1+x_2}{2}, v_1\right) + L\left(\frac{x_1+x_2}{2}, v_2\right) - 2L\left(\frac{x_1+x_2}{2}, \frac{v_1+v_2}{2}\right) \right\} dt
$$
  
\n
$$
\leq C\left(\frac{|X_1 - X_2|^{1+\alpha}}{\tau^{1+\alpha}} + \tau|V_1 - V_2|^{1+\alpha}\right). \quad (8.21)
$$

On the other hand, from the convexity condition (iii) and (8.14), the left-hand side of (8.21) is bounded below by

$$
K\frac{1}{2\tau}\int_{t_0-\tau}^{t_0+\tau}|v_1-v_2|^{2+\kappa} dt \ge K'\Big(|V_1-V_2|-A\tau|X_1-X_2|\Big)^{2+\kappa}.\tag{8.22}
$$

If  $|V_1 - V_2| \leq 2A\tau |X_1 - X_2|$ , then the proof is finished. If this is not the case, this means that  $|V_1 - V_2| - A\tau |X_1 - X_2| \ge |V_1 - V_2|/2$ , and then the comparison of the upper bound (8.21) and the lower bound (8.22) yields

$$
|V_1 - V_2|^{2+\kappa} \le C\left(\frac{|X_1 - X_2|^{1+\alpha}}{\tau^{1+\alpha}} + \tau|V_1 - V_2|^{1+\alpha}\right). \tag{8.23}
$$

If  $|V_1 - V_2| = 0$ , then the proof is finished. Otherwise, the conclusion follows by choosing  $\tau$  small enough that  $C\tau|V_1 - V_2|^{1+\alpha} \leq (1/2)|V_1 - V_2|^{1+\alpha}$  $V_2|^{2+\kappa}$ ; then  $\tau = O(|V_1 - V_2|^{1+\kappa-\alpha})$  and (8.23) implies

$$
|V_1 - V_2| = O(|X_1 - X_2|^{\beta}), \qquad \beta = \frac{1 + \alpha}{(1 + \alpha)(1 + \kappa - \alpha) + 2 + \kappa}.
$$
\n(8.24)

In the particular case when  $\kappa = 0$  and  $\alpha = 1$ , one has

$$
|V_1 - V_2|^2 \le C\left(\frac{|X_1 - X_2|^2}{\tau^2} + \tau|V_1 - V_2|^2\right),
$$

and if  $\tau$  is small enough this implies just

$$
|V_1 - V_2| \le C \frac{|X_1 - X_2|}{\tau}.
$$
\n(8.25)

The upper bound on  $\tau$  depends on the regularity and strict convexity of  $\tau$  in  $\mathcal{V}$ , but also on  $t_0$  since  $\tau$  cannot be greater than  $\min(t_0, 1 - t_0)$ . This is actually the only way in which  $t_0$  explicitly enters the estimates. So inequality (8.25) concludes the argument. □

# Complement: Ruling out focalization by shortening

This section is about the application of the shortening technique to a classical problem in Riemannian geometry; it may be skipped at first reading.

Let M be a smooth Riemannian manifold and let  $L = L(x, v, t)$  be a  $C^2$  Lagrangian on  $TM \times [0,1]$ , satisfying the classical conditions of Definition 7.6, together with  $\nabla_v^2 L > 0$ . Let  $X_t(x_0, v_0) = X_t(x_0, v_0, 0)$ be the solution at time  $t$  of the flow associated with the Lagrangian  $L$ , starting from the initial position  $x_0$  at time 0.

It is said that there is **focalization** on another point  $x' = X_{t'}(x_0, v_0)$ ,  $t' > 0$ , if the differential map  $d_{v_0} X_{t'}(x_0, \cdot)$  is singular (not invertible). In words, this means that starting from  $x_0$  it is very difficult to make the curve explore a whole neighborhood of  $x'$  by varying its initial velocity; instead, trajectories have a tendency to "concentrate" at time  $t'$  along certain preferred directions around  $x'.$ 

The reader can test his or her understanding of the method presented in the previous section by working out the details of the following problem.

### Problem 8.8 (Focalization is impossible before the cut locus).

With the same notation as before, let  $\gamma : [0,1] \to M$  be a minimizing curve starting from some initial point  $x_0$ . By using the same strategy of proof as for Mather's estimates, show that, starting from  $x_0$ , focalization is impossible at  $\gamma(t_*)$  if  $0 < t_* < 1$ .

Hint: Here is a possible reasoning:

(a) Notice that the restriction of  $\gamma$  to  $[0, t_*]$  is the unique minimizing curve on the time-interval  $[0,t_*]$ , joining  $x_0$  to  $x_* = \gamma(t_*)$ .

(b) Take y close to  $x_*$  and introduce a minimizing curve  $\widetilde{\gamma}$  on  $[0,t_*],$ joining  $x_0$  to y; show that the initial velocity  $\widetilde{v}_0$  of  $\widetilde{\gamma}$  is close to the initial velocity  $v_0$  of  $\gamma$  if y is close enough to  $x_*$ .

(c) Bound the difference between the action of  $\gamma$  and the action of  $\widetilde{\gamma}$  by  $O(d(x_*,y))$  (recall that the speeds along  $\gamma$  and  $\widetilde{\gamma}$  are bounded by a uniform constant, depending only of the behavior of  $L$  in some compact set around  $\gamma$ ).

(d) Construct a path  $x_0 \rightarrow \gamma(1)$  by first going along  $\tilde{\gamma}$  up to time  $t = t_* - \tau$  ( $\tau$  small enough), then using a shortcut from  $\widetilde{\gamma}(t_* - \tau)$  to  $\gamma(t_* + \tau)$ , finally going along  $\gamma$  up to time 1. Show that the gain of action is at least of the order of  $\tau |V - \tilde{V}|^2$  –  $O(d(x_*, y)^2 / \tau)$ , where  $V = \dot{\gamma}(t_*)$  and  $\widetilde{V} = \dot{\widetilde{\gamma}}(t_*)$ . Deduce that  $|V - \widetilde{V}| = O(d(x_*, y)/\tau)$ .

(e) Conclude that  $|v_0 - \tilde{v}_0| = O(d(x_*, y)/\tau)$ . Use a contradiction argument to deduce that the differential map  $d_{v_0}X_t(x_0, \cdot)$  is invertible, and more precisely that its inverse is of size  $O((1-t_*)^{-1})$  as a function of  $t_∗$ .

In the important case when  $L(x, v, t) = |v|^2$ , what we have proven is a well-known result in Riemannian geometry; to explain it I shall first recall the notions of cut locus and focal points.

Let  $\gamma$  be a minimizing geodesic, and let  $t_c$  be the largest time such that for all  $t < t_c$ ,  $\gamma$  is minimizing between  $\gamma_0$  and  $\gamma_t$ . Roughly speaking,  $\gamma(t_c)$  is the first point at which the geodesic ceases to be minimizing;  $\gamma$  may or may not be minimizing between  $\gamma(0)$  and  $\gamma(t_c)$ , but it is certainly not minimizing between  $\gamma(0)$  and  $\gamma(t_c + \varepsilon)$ , for any  $\varepsilon > 0$ . Then the point  $\gamma(t_c)$  is said to be a cut point of  $\gamma_0$  along  $\gamma$ . When the initial position  $x_0$  of the geodesic is fixed and the geodesic varies, the set of all cut points constitutes the *cut locus* of  $x_0$ .

Next, two points  $x_0$  and  $x'$  are said to be *focal* (or conjugate) if  $x'$ can be written as  $\exp_{x_0}(t'v_0)$ , where the differential  $d_{v_0} \exp_{x_0}(t')$  is not *invertible*. As before, this means that  $x'$  can be obtained from  $x_0$  by a geodesic  $\gamma$  with  $\dot{\gamma}(0) = v_0$ , such that it is difficult to explore a whole neighborhood of  $x'$  by slightly changing the initial velocity  $v_0$ .

With these notions, the main result of Problem 8.8 can be summarized as follows: Focalization never occurs before the cut locus. It can occur either at the cut locus, or after.

**Example 8.9.** On the sphere  $S^2$ , the north pole N has only one cut point, which is also its only focal point, namely the south pole S. Fix a geodesic  $\gamma$  going from  $\gamma(0) = N$  to  $\gamma(1) = S$ , and deform your sphere out of a neighborhood of  $\gamma[0,1]$ , so as to dig a shortcut that allows you to go from N to  $\gamma(1/2)$  in a more efficient way than using  $\gamma$ . This will create a new cut point along  $\gamma$ , and S will not be a cut point along  $\gamma$ any longer (it might still be a cut point along some other geodesic). On the other hand, S will still be the only focal point along  $\gamma$ .

**Remark 8.10.** If x and y are not conjugate, and joined by a unique minimizing geodesic  $\gamma$ , then it is easy to show that there is a neighborhood U of y such that any  $z$  in U is also joined to x by a unique minimizing geodesic. Indeed, any minimizing geodesic has to be close to  $\gamma$ , therefore its initial velocity should be close to  $\dot{\gamma}_0$ ; and by the local inversion theorem, there are neighborhoods  $W_0$  of  $\dot{\gamma}_0$  and U of y such that there is a unique correspondence between the initial velocity  $\dot{\gamma} \in W_0$  of a minimizing curve starting from x, and the final point  $\gamma(1) \in U$ . Thus the cut locus of a point x can be separated into two sets:

(a) those points  $y$  for which there are at least two distinct minimizing geodesics going from  $x$  to  $y$ ;

(b) those points  $y$  for which there is a unique minimizing geodesic, but which are focal points of  $x$ .

#### Introduction to Mather's theory

In this section I shall present an application of Theorem 8.1 to the theory of Lagrangian dynamical systems. This is mainly to give the reader an idea of Mather's motivations, and to let him or her better understand the link between optimal transport and Mather's theory. These results will not play any role in the sequel of the notes.

Theorem 8.11 (Lipschitz graph theorem). Let  $M$  be a compact Riemannian manifold, let  $L = L(x, v, t)$  be a Lagrangian function on  $TM \times \mathbb{R}$ , and  $T > 0$ , such that

(a) L is T-periodic in the t variable, i.e.  $L(x, v, t + T) = L(x, v, t);$ 

- (b) L is of class  $C^2$  in all variables;
- (c)  $\nabla_v^2 L$  is (strictly) positive everywhere, and L is superlinear in v.

Define as usual the action by  $\mathcal{A}^{s,t}(\gamma) = \int_s^t L(\gamma_\tau, \dot{\gamma}_\tau, \tau) d\tau$ . Let  $c^{s,t}$  be the associated cost function on  $M \times M$ , and  $C^{s,t}$  the corresponding optimal cost functional on  $P(M) \times P(M)$ .

Let  $\overline{\mu}$  be a probability measure solving the minimization problem

$$
\inf_{\mu \in P(\mathcal{X})} C^{0,T}(\mu, \mu), \tag{8.26}
$$

and let  $(\mu_t)_{0 \leq t \leq T}$  be a displacement interpolation between  $\mu_0 = \overline{\mu}$  and  $\mu_T = \overline{\mu}$ . Extend  $(\mu_t)$  into a T-periodic curve  $\mathbb{R} \to P(M)$  defined for all times. Then

(i) For all  $t_0 < t_1$ , the curve  $(\mu_t)_{t_0 \leq t \leq t_1}$  still defines a displacement interpolation;

(ii) The optimal transport cost  $C^{t,t+T}(\mu_t, \mu_t)$  is independent of t;

(iii) For any  $t_0 \in \mathbb{R}$ , and for any  $k \in \mathbb{N}$ ,  $\mu_{t_0}$  is a minimizer for  $C^{t_0,t_0+kT}(\mu,\mu).$ 

Moreover, there is a random curve  $(\gamma_t)_{t \in \mathbb{R}}$ , such that

(iv) For all  $t \in \mathbb{R}$ , law  $(\gamma_t) = \mu_t$ ;

(v) For any  $t_0 < t_1$ , the curve  $(\gamma_t)_{t_0 \leq t \leq t_1}$  is action-minimizing;

(vi) The map  $\gamma_0 \rightarrow \dot{\gamma}_0$  is well-defined and Lipschitz.
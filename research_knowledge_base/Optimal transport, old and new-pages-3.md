# Three examples of coupling techniques

In this chapter I shall present three applications of coupling methods. The first one is classical and quite simple, the other two are more original but well-representative of the topics that will be considered later in these notes. The proofs are extremely variable in difficulty and will only be sketched here; see the references in the bibliographical notes for details.

### Convergence of the Lange<PERSON> process

Consider a particle subject to the force induced by a potential  $V \in$  $C^1(\mathbb{R}^n)$ , a friction and a random white noise agitation. If  $X_t$  stands for the position of the particle at time t, m for its mass,  $\lambda$  for the friction coefficient, k for the Boltzmann constant and T for the temperature of the heat bath, then <PERSON>'s equation of motion can be written

$$
m\frac{d^2X_t}{dt^2} = -\nabla V(X_t) - \lambda m\frac{dX_t}{dt} + \sqrt{kT}\frac{dB_t}{dt},\tag{2.1}
$$

where  $(B_t)_{t>0}$  is a standard Brownian motion. This is a second-order (stochastic) differential equation, so it should come with initial conditions for both the position X and the velocity  $\dot{X}$ .

Now consider a large cloud of particles evolving independently, according to (2.1); the question is whether the distribution of particles will converge to a definite limit as  $t \to \infty$ . In other words: Consider the stochastic differential equation (2.1) starting from some initial distribution  $\mu_0(dx\,dv) = \text{law}(X_0, \dot{X}_0)$ ; is it true that law  $(X_t)$ , or law  $(X_t, \dot{X}_t)$ , will converge to some given limit law as  $t \to \infty$ ?

# 34 2 Three examples of coupling techniques

Obviously, to solve this problem one has to make some assumptions on the potential  $V$ , which should prevent the particles from all escaping at infinity; for instance, we can make the very strong assumption that V is uniformly convex, i.e. there exists  $K > 0$  such that the Hessian  $\nabla^2 V$ satisfies  $\nabla^2 V \geq K I_n$ . Some assumptions on the initial distribution might also be needed; for instance, it is natural to assume that the Hamiltonian has finite expectation at initial time:

$$
\mathbb{E}\left(V(X_0) + \frac{|\dot{X}_0|^2}{2}\right) < +\infty
$$

Under these assumptions, it is true that there is exponential convergence to equilibrium, at least if  $V$  does not grow too wildly at infinity (for instance if the Hessian of  $V$  is also bounded above). However, I do not know of any simple method to prove this.

On the other hand, consider the limit where the friction coefficient is quite strong, and the motion of the particle is so slow that the acceleration term may be neglected in front of the others: then, up to resetting units, equation (2.1) becomes

$$
\frac{dX_t}{dt} = -\nabla V(X_t) + \sqrt{2}\frac{dB_t}{dt},\tag{2.2}
$$

which is often called a Langevin process. Now, to study the convergence of equilibrium for (2.2) there is an extremely simple solution by coupling. Consider another random position  $(Y_t)_{t>0}$  obeying the same equation as (2.2):

$$
\frac{dY_t}{dt} = -\nabla V(Y_t) + \sqrt{2}\frac{dB_t}{dt},\tag{2.3}
$$

where the random realization of the Brownian motion is the same as in (2.2) (this is the coupling). The initial positions  $X_0$  and  $Y_0$  may be coupled in an arbitrary way, but it is possible to assume that they are independent. In any case, since they are driven by the same Brownian motion,  $X_t$  and  $Y_t$  will be correlated for  $t > 0$ .

Since  $B_t$  is not differentiable as a function of time, neither  $X_t$  nor  $Y_t$  is differentiable (equations  $(2.2)$  and  $(2.3)$  hold only in the sense of solutions of stochastic differential equations); but it is easily checked that  $\alpha_t := X_t - Y_t$  is a continuously differentiable function of time, and

$$
\frac{d\alpha_t}{dt} = -\big(\nabla V(X_t) - \nabla V(Y_t)\big),
$$

so in particular

$$
\frac{d}{dt}\frac{|\alpha_t|^2}{2} = -\left\langle \nabla V(X_t) - \nabla V(Y_t), \ X_t - Y_t \right\rangle \le -K \left| X_t - Y_t \right|^2 = -K |\alpha_t|^2.
$$

It follows by Gronwall's lemma that

$$
|\alpha_t|^2 \le e^{-2Kt} |\alpha_0|^2.
$$

Assume for simplicity that  $\mathbb{E}|X_0|^2$  and  $\mathbb{E}|Y_0|^2$  are finite. Then

$$
\mathbb{E}|X_t - Y_t|^2 \le e^{-2Kt} \mathbb{E}|X_0 - Y_0|^2 \le 2 \left( \mathbb{E}|X_0|^2 + \mathbb{E}|Y_0|^2 \right) e^{-2Kt}.
$$
 (2.4)

In particular,  $X_t - Y_t$  converges to 0 almost surely, and this is independent of the distribution of  $Y_0$ .

This in itself would be essentially sufficient to guarantee the existence of a stationary distribution; but in any case, it is easy to check, by applying the diffusion formula, that

$$
\nu(dy) = \frac{e^{-V(y)} dy}{Z}
$$

(where  $Z = \int e^{-V}$  is a normalization constant) is stationary: If law  $(Y_0) = \nu$ , then also law  $(Y_t) = \nu$ . Then (2.4) easily implies that  $\mu_t := \text{law}(X_t)$  converges weakly to  $\nu$ ; in addition, the convergence is exponentially fast.

## Euclidean isoperimetry

Among all subsets of  $\mathbb{R}^n$  with given surface, which one has the largest volume? To simplify the problem, let us assume that we are looking for a bounded open set  $\Omega \subset \mathbb{R}^n$  with, say, Lipschitz boundary  $\partial\Omega$ , and that the measure of  $|\partial\Omega|$  is given; then the problem is to maximize the measure of  $|\Omega|$ . To measure  $\partial\Omega$  one should use the  $(n-1)$ -dimensional Hausdorff measure, and to measure  $\Omega$  the *n*-dimensional Hausdorff measure, which of course is the same as the Lebesgue measure in  $\mathbb{R}^n$ .

It has been known, at least since ancient times, that the solution to this "isoperimetric problem" is the ball. A simple scaling argument shows that this statement is equivalent to the Euclidean isoperimetric inequality:

36 2 Three examples of coupling techniques

$$
\frac{|\partial \varOmega|}{|\varOmega|^{\frac{n}{n-1}}} \geq \frac{|\partial B|}{|B|^{\frac{n}{n-1}}},
$$

where  $B$  is any ball.

There are very many proofs of the isoperimetric inequality, and many refinements as well. It is less known that there is a proof by coupling.

Here is a sketch of the argument, forgetting about regularity issues. Let B be a ball such that  $|\partial B| = |\partial \Omega|$ . Consider a random point X distributed uniformly in  $\Omega$ , and a random point Y distributed uniformly in  $B$ . Introduce the Knothe–Rosenblatt coupling of  $X$  and  $Y$ : This is a deterministic coupling of the form  $Y = T(X)$ , such that, at each  $x \in \Omega$ , the Jacobian matrix  $\nabla T(x)$  is triangular with nonnegative diagonal entries. Since the law of X (resp. Y) has uniform density  $1/|\Omega|$ (resp.  $1/|B|$ ), the change of variables formula yields

$$
\forall x \in \Omega \qquad \frac{1}{|\Omega|} = \left(\det \nabla T(x)\right) \frac{1}{|B|}.\tag{2.5}
$$

Since  $\nabla T$  is triangular, the Jacobian determinant of T is det( $\nabla T$ ) =  $\prod \lambda_i$ , and its divergence  $\nabla \cdot T = \sum \lambda_i$ , where the nonnegative numbers  $(\lambda_i)_{1 \leq i \leq n}$  are the eigenvalues of  $\nabla T$ . Then the arithmetic–geometric inequality  $(\prod \lambda_i)^{1/n} \leq (\sum \lambda_i)/n$  becomes

$$
\left(\det \nabla T(x)\right)^{1/n} \le \frac{\nabla \cdot T(x)}{n}.
$$

Combining this with (2.5) results in

$$
\frac{1}{|\varOmega|^{1/n}} \leq \frac{(\nabla \cdot T)(x)}{n \, |B|^{1/n}}.
$$

Integrate this over  $\Omega$  and then apply the divergence theorem:

$$
|\Omega|^{1-\frac{1}{n}} \le \frac{1}{n\,|B|^{\frac{1}{n}}} \int_{\Omega} (\nabla \cdot T)(x) \, dx = \frac{1}{n\,|B|^{\frac{1}{n}}} \int_{\partial \Omega} (T \cdot \sigma) \, d\mathcal{H}^{n-1}, \tag{2.6}
$$

where  $\sigma : \partial\Omega \to \mathbb{R}^n$  is the unit outer normal to  $\Omega$  and  $\mathcal{H}^{n-1}$  is the  $(n-1)$ -dimensional Hausdorff measure (restricted to  $\partial\Omega$ ). But T is valued in B, so  $|T \cdot \sigma| \leq 1$ , and  $(2.6)$  implies

$$
|\Omega|^{1-\frac{1}{n}} \le \frac{|\partial \Omega|}{n |B|^{\frac{1}{n}}}.
$$

Since  $|\partial\Omega| = |\partial B| = n|B|$ , the right-hand side is actually  $|B|^{1-\frac{1}{n}}$ , so the volume of  $\Omega$  is indeed bounded by the volume of  $B$ . This concludes the proof.

The above argument suggests the following problem:

Open Problem 2.1. Can one devise an optimal coupling between sets (in the sense of a coupling between the uniform probability measures on these sets) in such a way that the total cost of the coupling decreases under some evolution converging to balls, such as mean curvature motion?

### Caffarelli's log-concave perturbation theorem

The previous example was about transporting a set to another, now the present one is in some sense about transporting a whole space to another.

It is classical in geometry to compare a space  $\mathcal X$  with a "model" space" M that has nice properties and is, e.g., less curved than  $\mathcal{X}$ . A general principle is that certain inequalities which hold true on the model space can automatically be "transported" to  $\mathcal{X}$ . The theorem discussed in this section is a striking illustration of this idea.

Let  $F, G, H, J, L$  be nonnegative continuous functions on R, with H and J nondecreasing, and let  $\ell \in \mathbb{R}$ . For a given measure  $\mu$  on  $\mathbb{R}^n$ , let  $\lambda[\mu]$  be the largest  $\lambda \geq 0$  such that, for all Lipschitz functions  $h:\mathbb{R}^n\to\mathbb{R},$ 

$$
\int_{\mathbb{R}^n} L(h) \, d\mu = \ell \quad \Longrightarrow \quad F\left(\int_{\mathbb{R}^n} G(h) \, d\mu\right) \le \frac{1}{\lambda} \, H\left(\int_{\mathbb{R}^n} J(|\nabla h|) \, d\mu\right). \tag{2.7}
$$

Functional inequalities of the form (2.7) are variants of Sobolev inequalities; many of them are well-known and useful. Caffarelli's theorem states that they can only be improved by log-concave perturbation of the Gaussian distribution. More precisely, if  $\gamma$  is the standard Gaussian measure and  $\mu = e^{-v}\gamma$  is another probability measure, with v convex, then

$$
\lambda[\mu] \geq \lambda[\gamma].
$$

# 38 2 Three examples of coupling techniques

His proof is a simple consequence of the following remarkable fact, which I shall call Caffarelli's log-concave perturbation theo**rem:** If  $d\mu/d\gamma$  is log-concave, then there exists a 1-Lipschitz change of variables from the measure  $\gamma$  to the measure  $\mu$ . In other words, there is a deterministic coupling  $(X, Y = C(X))$  of  $(\gamma, \mu)$ , such that  $|\mathcal{C}(x) - \mathcal{C}(y)| \le |x - y|$ , or equivalently  $|\nabla \mathcal{C}| \le 1$  (almost everywhere). It follows in particular that

$$
\left|\nabla(h\circ\mathcal{C})\right| \leq |(\nabla h)\circ\mathcal{C}|,\tag{2.8}
$$

whatever the function  $h$ .

Now it is easy to understand why the existence of the map  $\mathcal C$  implies (2.7): On the one hand, the definition of change of variables implies

$$
\int G(h) d\mu = \int G(h \circ C) d\gamma, \qquad \int L(h) d\mu = \int L(h \circ C) d\gamma;
$$

on the other hand, by the definition of change of variables again, inequality  $(2.8)$  and the nondecreasing property of J,

$$
\int J(|\nabla h|) d\mu = \int J(|\nabla h \circ \mathcal{C}|) d\gamma \ge \int J(|\nabla (h \circ \mathcal{C})|) d\gamma.
$$

Thus, inequality (2.7) is indeed "transported" from the space  $(\mathbb{R}^n, \gamma)$ to the space  $(\mathbb{R}^n, \mu)$ .

# Bibliographical notes

It is very classical to use coupling arguments to prove convergence to equilibrium for stochastic differential equations and Markov chains; many examples are described by Rachev and Rüschendorf [696] and Thorisson [781]. Actually, the standard argument found in textbooks to prove the convergence to equilibrium for a positive aperiodic ergodic Markov chain is a coupling argument (but the null case can also be treated in a similar way, as I learnt from Thorisson). Optimal couplings are often well adapted to such situations, but definitely not the only ones to apply.

The coupling method is not limited to systems of independent particles, and sometimes works in presence of correlations, for instance if the law satisfies a nonlinear diffusion equation. This is exemplified in works by Tanaka [777] on the spatially homogeneous Boltzmann equation with Maxwell molecules (the core of Tanaka's argument is reproduced in my book [814, Section 7.5]), or some recent papers [138, 214, 379, 590].

Cattiaux and Guillin [221] found a simple and elegant coupling argument to prove the exponential convergence for the law of the stochastic process

$$
dX_t = \sqrt{2} dB_t - \widetilde{\mathbb{E}} \,\nabla V(X_t - \widetilde{X}_t) dt,
$$

where  $\widetilde{X}_t$  is an independent copy of  $X_t$ , the  $\widetilde{\mathbb{E}}$  expectation only bears on  $\widetilde{X}_t$ , and V is assumed to be a uniformly convex  $C^1$  potential on  $\mathbb{R}^n$ satisfying  $V(-x) = V(x)$ .

It is also classical to couple a system of particles with an auxiliary artificial system to study the limit when the number of particles becomes large. For the Vlasov equation in kinetic theory this was done by Dobrushin [309] and Neunzert [653] several decades ago. (The proof is reproduced in Spohn [757, Chapter 5], and also suggested as an exercise in my book [814, Problem 14].) Later Sznitman used this strategy in a systematic way for the propagation of chaos, and made it very popular, see e.g. his work on the Boltzmann equation [767] or his Saint-Flour lecture notes [768] and the many references included.

In all these works, the "philosophy" is always the same: Introduce some nice coupling and see how it evolves in a certain asymptotic regime (say, either the time, or the number of particles, or both, go to infinity).

It is possible to treat the convergence to equilibrium for the complete system (2.1) by methods that are either analytic [301, 472, 816, 818] or probabilistic [55, 559, 606, 701], but all methods known to me are much more delicate than the simple coupling argument which works for (2.2). It is certainly a nice open problem to find an elementary coupling argument which applies to (2.1). (The arguments in the abovementioned probabilistic proofs ultimately rely on coupling methods via theorems of convergence for Markov chains, but in a quite indirect way.)

Coupling techniques have also been used recently for proving rather spectacular uniqueness theorems for invariant measures in infinite dimension, see e.g. [321, 456, 457].

Classical references for the isoperimetric inequality and related topics are the books by Burago and Zalgaller [176], and Schneider [741]; and the survey by Osserman [664]. Knothe [523] had the idea to use a "coupling" method to prove geometric inequalities, and Gromov [635, Appendix] applied this method to prove the Euclidean isopetrimetric inequality. Trudinger [787] gave a closely related treatment of the same inequality and some of its generalizations, by means of a clever use of the Monge–Ampère equation (which more or less amounts to the construction of an optimal coupling with quadratic cost function, as will be seen in Chapter 11). Cabré [182] found a surprising simplification of Trudinger's method, based on the solution of just a linear elliptic equation. The "proof" which I gave in this chapter is a variation on Gromov's argument; although it is not rigorous, there is no real difficulty in turning it into a full proof, as was done by Figalli, Maggi and Pratelli [369]. These authors actually prove much more, since they use this strategy to establish a sharp quantitative stability of the isoperimetric inequality (if the shape of a set departs from the optimal shape, then its isoperimetric ratio departs from the optimal ratio in a quantifiable way). In the same work one can find a very interesting comparison of the respective performances of the couplings obtained by the Knothe method and by the optimal transport method (the comparison turns very much to the advantage of optimal transport).

Other links between coupling and isoperimetric-type inequalities are presented in Chapter 6 of my book [814], the research paper [587], the review paper [586] and the bibliographical notes at the end of Chapters 18 and 21.

The construction of Caffarelli's map  $\mathcal C$  is easy, at least conceptually: The optimal coupling of the Gaussian measure  $\gamma$  with the measure  $\mu =$  $e^{-v}\gamma$ , when the cost function is the square of the Euclidean distance, will do the job. But proving that  $\mathcal C$  is indeed 1-Lipschitz is much more of a sport, and involves some techniques from nonlinear partial differential equations [188]. An idea of the core of the proof is explained in [814, Problem 13]. It would be nice to find a softer argument.

Ustünel pointed out to me that, if  $v$  is convex and symmetric  $(v(-x) = v(x))$ , then the Moser transport T from  $\gamma$  to  $e^{-v}\gamma$  is contracting, in the sense that  $|T(x)| \leq |x|$ ; it is not clear however that T would be 1-Lipschitz.

Caffarelli's theorem has many analytic and probabilistic applications, see e.g. [242, 413, 465]. There is an infinite-dimensional version by Feyel and Ustünel  $[361]$ , where the Gaussian measure is replaced by the Wiener measure. Another variant was recently studied by Valdimarsson [801].

Like the present chapter, the lecture notes [813], written for a CIME Summer School in 2001, present some applications of optimal transport in various fields, with a slightly impressionistic style.

# The founding fathers of optimal transport

Like many other research subjects in mathematics, the field of optimal transport was born several times. The first of these births occurred at the end of the eighteenth century, by way of the French geometer Gaspard Monge.

Monge was born in 1746 under the French Ancient Régime. Because of his outstanding skills, he was admitted in a military training school from which he should have been excluded because of his modest origin. He invented descriptive geometry on his own, and the power of the method was so apparent that he was appointed professor at the age of 22, with the understanding that his theory would remain a military secret, for exclusive use of higher officers. He later was one of the most ardent warrior scientists of the French Revolution, served as a professor under several regimes, escaped a death sentence pronounced during the Terror, and became one of Napoleon's closest friends. He taught at Ecole Normale Supérieure and École Polytechnique in Paris. Most of his work was devoted to geometry.

In 1781 he published one of his famous works, *Mémoire sur la théorie* des déblais et des remblais (a "déblai" is an amount of material that is extracted from the earth or a mine; a "remblai" is a material that is input into a new construction). The problem considered by Monge is as follows: Assume you have a certain amount of soil to extract from the ground and transport to places where it should be incorporated in a construction (see Figure 3.1). The places where the material should be extracted, and the ones where it should be transported to, are all known. But the assignment has to be determined: To which destination should one send the material that has been extracted at a certain place? The answer does matter because transport is costly, and you want to

minimize the total cost. Monge assumed that the transport cost of one unit of mass along a certain distance was given by the product of the mass by the distance.

Image /page/9/Figure/2 description: The image depicts a diagram illustrating a transformation process. On the left, a shaded area labeled 'déblais' represents excavated material, with a point labeled 'x' indicating a location within this area. An arrow labeled 'T' curves from 'x' towards the right. On the right, a shaded area labeled 'remblais' represents filled material, forming a shape resembling a stylized cityscape or structure with battlements and a tall, pointed section. A point labeled 'y' is indicated within the 'remblais' area, with the arrow 'T' pointing towards it, signifying the transformation or movement of material from the 'déblais' to the 'remblais'.

Fig. 3.1. Monge's problem of déblais and remblais

Nowadays there is a Monge street in Paris, and therein one can find an excellent bakery called Le Boulanger de Monge. To acknowledge this, and to illustrate how Monge's problem can be recast in an economic perspective, I shall express the problem as follows. Consider a large number of bakeries, producing loaves, that should be transported each morning to cafés where consumers will eat them. The amount of bread that can be produced at each bakery, and the amount that will be consumed at each café are known in advance, and can be modeled as probability measures (there is a "density of production" and a "density of consumption") on a certain space, which in our case would be Paris (equipped with the natural metric such that the distance between two points is the length of the shortest path joining them). The problem is to find in practice where each unit of bread should go (see Figure 3.2), in such a way as to minimize the total transport cost. So Monge's problem really is the search of an optimal coupling; and to be more precise, he was looking for a deterministic optimal coupling.

Image /page/9/Figure/5 description: The image displays a white background with several black squares and white circles. Red arrows connect some of the squares to circles, indicating movement or direction. Specifically, there are three white circles and four black squares. One white circle is positioned to the left, with an arrow pointing from a black square to its left. Another white circle is below and to the left, with an arrow pointing from a black square below it. The third white circle is in the upper right, with an arrow pointing from a black square to its right. A fourth black square is in the upper center, with an arrow pointing from it to a white circle below and to the left.

Fig. 3.2. Economic illustration of Monge's problem: squares stand for production units, circles for consumption places.

Monge studied the problem in three dimensions for a continuous distribution of mass. Guided by his beautiful geometric intuition, he made the important observation that transport should go along straight lines that would be orthogonal to a family of surfaces. This study led him to the discovery of *lines of curvature*, a concept that by itself was a great contribution to the geometry of surfaces. His ideas were developed by Charles Dupin and later by Paul Appell. By current mathematical standards, all these arguments were flawed, yet it certainly would be worth looking up all these problems with modern tools.

Much later Monge's problem was rediscovered by the Russian mathematician Leonid Vitaliyevich Kantorovich. Born in 1912, Kantorovich was a very gifted mathematician who made his reputation as a firstclass researcher at the age of 18, and earned a position of professor at just the same age as Monge had. He worked in many areas of mathematics, with a strong taste for applications in economics, and later theoretical computer science. In 1938 a laboratory consulted him for the solution of a certain optimization problem, which he found out was representative of a whole class of linear problems arising in various areas of economics. Motivated by this discovery, he developed the tools of linear programming, that later became prominent in economics. The publication of some of his most important works was delayed because of the great care with which Soviet authorities of the time handled the divulgence of scientific research related to economics. In fact (and this is another common point with Monge) for many years it was strictly forbidden for Kantorovich to publicly discuss some of his main discoveries. In the end his work became well-known and in 1975 was awarded the Nobel Prize for economics, jointly with Tjalling Koopmans, "for their contributions to the theory of optimum allocation of resources".

In the case that is of direct interest for us, namely the problem of optimal coupling, Kantorovich stated and proved, by means of functional analytical tools, a duality theorem that would play a crucial role later. He also devised a convenient notion of distance between probability measures: the distance between two measures should be the optimal transport cost from one to the other, if the cost is chosen as the distance function. This distance between probability measures is nowadays called the Kantorovich–Rubinstein distance, and has proven to be particularly flexible and useful.

## 44 3 The founding fathers of optimal transport

It was only several years after his main results that Kantorovich made the connection with Monge's work. The problem of optimal coupling has since then been called the Monge–Kantorovich problem.

Throughout the second half of the twentieth century, optimal coupling techniques and variants of the Kantorovich–Rubinstein distance (nowadays often called Wasserstein distances, or other denominations) were used by statisticians and probabilists. The "basis" space could be finite-dimensional, or infinite-dimensional: For instance, optimal couplings give interesting notions of distance between probability measures on path spaces. Noticeable contributions from the seventies are due to Roland Dobrushin, who used such distances in the study of particle systems; and to Hiroshi Tanaka, who applied them to study the time-behavior of a simple variant of the Boltzmann equation. By the mid-eighties, specialists of the subject, like Svetlozar Rachev or Ludger Rüschendorf, were in possession of a large library of ideas, tools, techniques and applications related to optimal transport.

During that time, reparametrization techniques (yet another word for change of variables) were used by many researchers working on inequalities involving volumes or integrals. Only later would it be understood that optimal transport often provides useful reparametrizations.

At the end of the eighties, three directions of research emerged independently and almost simultaneously, which completely reshaped the whole picture of optimal transport.

One of them was John Mather's work on Lagrangian dynamical systems. Action-minimizing curves are basic important objects in the theory of dynamical systems, and the construction of closed actionminimizing curves satisfying certain qualitative properties is a classical problem. By the end of the eighties, Mather found it convenient to study not only action-minimizing curves, but action-minimizing stationary measures in phase space. Mather's measures are a generalization of action-minimizing curves, and they solve a variational problem which in effect is a Monge–Kantorovich problem. Under some conditions on the Lagrangian, Mather proved a celebrated result according to which (roughly speaking) certain action-minimizing measures are automatically concentrated on Lipschitz graphs. As we shall understand in Chapter 8, this problem is intimately related to the construction of a deterministic optimal coupling.

The second direction of research came from the work of Yann Brenier. While studying problems in incompressible fluid mechanics, Brenier needed to construct an operator that would act like the projection on the set of measure-preserving mappings in an open set (in probabilistic language, measure-preserving mappings are deterministic couplings of the Lebesgue measure with itself). He understood that he could do so by introducing an optimal coupling: If  $u$  is the map for which one wants to compute the projection, introduce a coupling of the Lebesgue measure  $\mathcal L$  with  $u_{\#}\mathcal L$ . This study revealed an unexpected link between optimal transport and fluid mechanics; at the same time, by pointing out the relation with the theory of Monge–Ampère equations, Brenier attracted the attention of the community working on partial differential equations.

The third direction of research, certainly the most surprising, came from outside mathematics. Mike Cullen was part of a group of meteorologists with a well-developed mathematical taste, working on semigeostrophic equations, used in meteorology for the modeling of atmospheric fronts. Cullen and his collaborators showed that a certain famous change of unknown due to Brian Hoskins could be re-interpreted in terms of an optimal coupling problem, and they identified the minimization property as a *stability* condition. A striking outcome of this work was that optimal transport could arise naturally in partial differential equations which seemed to have nothing to do with it.

All three contributions emphasized (in their respective domain) that important information can be gained by a qualitative description of optimal transport. These new directions of research attracted various mathematicians (among the first, Luis Caffarelli, Craig Evans, Wilfrid Gangbo, Robert McCann, and others), who worked on a better description of the structure of optimal transport and found other applications.

An important conceptual step was accomplished by Felix Otto, who discovered an appealing formalism introducing a *differential* point of view in optimal transport theory. This opened the way to a more geometric description of the space of probability measures, and connected optimal transport to the theory of diffusion equations, thus leading to a rich interplay of geometry, functional analysis and partial differential equations.

Nowadays optimal transport has become a thriving industry, involving many researchers and many trends. Apart from meteorology, fluid mechanics and diffusion equations, it has also been applied to such diverse topics as the collapse of sandpiles, the matching of images, and the design of networks or reflector antennas. My book, Topics in Optimal

Transportation, written between 2000 and 2003, was the first attempt to present a synthetic view of the modern theory. Since then the field has grown much faster than I expected, and it was never so active as it is now.

# Bibliographical notes

Before the twentieth century, the main references for the problem of "déblais et remblais" are the memoirs by Monge [636], Dupin [319] and Appell [42]. Besides achieving important mathematical results, Monge and Dupin were strongly committed to the development of society and it is interesting to browse some of their writings about economics and industry (a list can be found online at gallica.bnf.fr). A lively account of Monge's life and political commitments can be found in Bell's delightful treatise, Men of Mathematics [80, Chapter 12]. It seems however that Bell did dramatize the story a bit, at the expense of accuracy and neutrality. A more cold-blooded biography of Monge was written by de Launay [277]. Considered as one the greatest geologists of his time, not particularly sympathetic to the French Revolution, de Launay documented himself with remarkable rigor, going back to original sources whenever possible. Other biographies have been written since then by Taton [778, 779] and Aubry [50].

Monge originally formulated his transport problem in Euclidean space for the cost function  $c(x,y) = |x-y|$ ; he probably had no idea of the extreme difficulty of a rigorous treatment. It was only in 1979 that Sudakov [765] claimed a proof of the existence of a Monge transport for general probability densities with this particular cost function. But his proof was not completely correct, and was amended much later by Ambrosio [20]. In the meantime, alternative rigorous proofs had been devised first by Evans and Gangbo [330] (under rather strong assumptions on the data), then by Trudinger and Wang [791], and Caffarelli, Feldman and McCann [190].

Kantorovich defined linear programming in [499], introduced his minimization problem and duality theorem in [500], and in [501] applied his theory to the problem of optimal transport; this note can be considered as the act of birth of the modern formulation of optimal transport. Later he made the link with Monge's problem in [502]. His major work in economics is the book [503], including a reproduction of [499]. Another important contribution is a study of numerical schemes based on linear programming, joint with his student Gavurin [505].

Kantorovich wrote a short autobiography for his Nobel Prize [504]. Online at www.math.nsc.ru/LBRT/g2/english/ssk/legacy.html are some comments by Kutateladze, who edited his mathematical works. A recent special issue of the Journal of Mathematical Sciences, edited by Vershik, was devoted to Kantorovich [810]; this reference contains translations of [501] and [502], as well as much valuable information about the personality of Kantorovich, and the genesis and impact of his ideas in mathematics, economy and computer science. In another historical note [808] Vershik recollects memories of Kantorovich and tells some tragicomical stories illustrating the incredible ideological pressure put on him and other scientists by Soviet authorities at the time.

The "classical" probabilistic theory of optimal transport is exhaustively reviewed by Rachev and Rüschendorf [696, 721]; most notable applications include limit theorems for various random processes. Relations with game theory, economics, statistics, and hypotheses testing are also common (among many references see e.g. [323, 391]).

Mather introduced minimizing measures in [600], and proved his Lipschitz graph theorem in [601]. The explicit connection with the Monge–Kantorovich problem came only recently [105]: see Chapter 8.

Tanaka's contributions to kinetic theory go back to the mid-seventies [644, 776, 777]. His line of research was later taken up by Toscani and collaborators [133, 692]; these papers constituted my first contact with the optimal transport problem. More recent developments in the kinetic theory of granular media appear for instance in [138].

Brenier announced his main results in a short note [154], then published detailed proofs in [156]. Chapter 3 in [814] is entirely devoted to Brenier's polar factorization theorem (which includes the existence of the projection operator), its interpretation and consequences. For the sources of inspiration of Brenier, and various links between optimal transport and hydrodynamics, one may consult [155, 158, 159, 160, 163, 170]. Recent papers by Ambrosio and Figalli [24, 25] provide a complete and thorough rewriting of Brenier's theory of generalized incompressible flows.

The semi-geostrophic system was introduced by Eliassen [325] and Hoskins [480, 481, 482]; it is very briefly described in [814, Problem 9, pp. 323–326]. Cullen and collaborators wrote many papers on the subject, see in particular [269]; see also the review article [263], the works by Cullen and Gangbo [266], Cullen and Feldman [265] or the recent book by Cullen [262].

Further links between optimal transport and other fields of mathematics (or physics) can be found in my book [814], or in the treatise by Rachev and Rüschendorf [696]. An important source of inspiration was the relation with the qualitative behavior of certain diffusive equations arising from gas dynamics; this link was discovered by Jordan, Kinderlehrer and Otto at the end of the nineties [493], and then explored by several authors [208, 209, 210, 211, 212, 213, 214, 216, 669, 671].

Below is a nonexhaustive list of some other unexpected applications. Relations with the modeling of sandpiles are reviewed by Evans [328], as well as compression molding problems; see also Feldman [353] (this is for the cost function  $c(x,y) = |x - y|$ . Applications of optimal transport to image processing and shape recognition are discussed by Gangbo and McCann [400], Ahmad [6], Angenent, Haker, Tannenbaum, and Zhu [462, 463], Chazal, Cohen-Steiner and Mérigot [224], and many other contributors from the engineering community (see e.g. [700, 713]). X.-J. Wang [834], and independently Glimm and Oliker [419] (around 2000 and 2002 respectively) discovered that the theoretical problem of designing reflector antennas could be recast in terms of optimal transport for the cost function  $c(x,y) = -\log(1-x \cdot y)$ on  $S^2$ ; see [402, 419, 660] for further work in the area, and [420] for another version of this problem involving two reflectors.<sup>1</sup> Rubinstein and Wolansky adapted the strategy in [420] to study the optimal design of lenses  $[712]$ ; and Gutiérrez and Huang to treat a refraction problem [453]. In his PhD Thesis, Bernot [108] made the link between optimal transport, irrigation and the design of networks. Such topics were also considered by Santambrogio with various collaborators [152, 207, 731, 732, 733, 734]; in particular it is shown in [732] that optimal transport theory gives a rigorous basis to some variational constructions used by physicists and hydrologists to study river basin morphology [65, 706]. Buttazzo and collaborators [178, 179, 180] explored city planning via optimal transport. Brenier found a connection to the electrodynamic equations of Maxwell and related models in string theory [161, 162, 163, 164, 165, 166]. Frisch and collaborators

 $1$  According to Oliker, the connection between the two-reflector problem (as formulated in [661]) and optimal transport is in fact much older, since it was first formulated in a 1993 conference in which he and Caffarelli were participating.

linked optimal transport to the problem of reconstruction of the "conditions of the initial Universe" [168, 382, 755]. (The publication of [382] in the prestigious generalist scientific journal Nature is a good indication of the current visibility of optimal transport outside mathematics.)

Relations of optimal transport with geometry, in particular Ricci curvature, will be explored in detail in Parts II and III of these notes.

Many generalizations and variants have been studied in the literature, such as the optimal matching [323], the optimal transshipment (see [696] for a discussion and list of references), the optimal transport of a fraction of the mass [192, 365], or the optimal coupling with more than two prescribed marginals [403, 525, 718, 723, 725]; I learnt from Strulovici that the latter problem has applications in contract theory.

In spite of this avalanche of works, one certainly should not regard optimal transport as a kind of miraculous tool, for "there are no miracles in mathematics". In my opinion this abundance only reflects the fact that optimal transport is a simple, meaningful, natural and therefore universal concept.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
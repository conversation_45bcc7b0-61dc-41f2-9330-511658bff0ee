The following situation occurs in many problems of local regularity: Knowing a certain estimate on a certain ball  $B_r(x_0)$ , deduce a better estimate on a smaller ball, say  $B_{r/2}(x_0)$ . In the fifties, this point of view was put to a high degree of sophistication by <PERSON> in his famous proof of <PERSON><PERSON><PERSON> estimates for elliptic second-order partial differential equations in divergence form; and it also plays a role in the alternative solutions found at the same time by <PERSON>, and later by <PERSON><PERSON>. When fine analysis on metric spaces started to develop, it became an important issue to understand what were the key ingredients lying at the core of the methods of <PERSON>, <PERSON> and <PERSON>. It is now accepted by many that the two key inequalities are:

- a **doubling inequality** for the reference volume measure;
- $\bullet$  a local Poincaré inequality, controlling the deviation of a function on a smaller ball by the integral of its gradient on a larger ball. Here is a precise definition:

**Definition 19.1 (Local Poincaré inequality).** Let  $(\mathcal{X}, d)$  be a Polish metric space and let  $\nu$  be a Borel measure on X. It is said that  $\nu$ satisfies a local Poincaré inequality with constant  $C$  if, for any Lipschitz function u, any point  $x_0 \in \mathcal{X}$  and any radius  $r > 0$ ,

$$
\int_{B_r(x_0)} \left| u(x) - \langle u \rangle_{B_r(x_0)} \right| d\nu(x) \le Cr \int_{B_{2r}(x_0)} \left| \nabla u(x) \right| d\nu(x), \qquad (19.1)
$$

where  $f_B = (\nu[B])^{-1} \int_B$  stands for the averaged integral over B, and  $\langle u \rangle_B = \int_B u \, d\nu$  for the average of the function u on B.

Let B be a Borel subset of  $\mathcal X$ . It is said that  $\nu$  satisfies a local Poincaré inequality with constant C in B if inequality (19.1) holds true under the additional restriction that  $B_{2r}(x_0) \subset B$ .

**Remark 19.2.** The definition of  $|\nabla u|$  in a nonsmooth context will be discussed later (see Chapter 20). For the moment the reader does not need to know this notion since this chapter only considers Riemannian manifolds.

Remark 19.3. The word "local" in Definition 19.1 means that the inequality is interested in averages around some point  $x_0$ . This is in contrast with the "global" Poincaré inequalities that will be considered later in Chapter 21, in which averages are over the whole space.

There are an incredible number of variants of Poincaré inequalities, but I shall stick to the ones appearing in Definition 19.1. Sometimes I shall say that  $\nu$  satisfies a *uniform* local Poincaré inequality to stress the fact that the constant C is independent of  $x_0$  and r. For most applications this uniformity is not important, all that matters is that inequality (19.1) holds true in the neighborhood of any point  $x_0$ ; so it is sufficient to prove that  $\nu$  satisfies a local Poincaré inequality with constant  $C = C(R)$  on each ball  $B(z, R)$ , where z is fixed once for all.

Just as the doubling inequality, the local Poincaré inequality might be ruined by sharp spines, and Ricci curvature bounds will prevent those spines to occur, providing quantitative Poincaré constants (that will be uniform in nonnegative curvature). Again, the goal of this chapter is to prove these facts by using optimal transport. The strategy goes through pointwise bounds on the density of the displacement interpolant.

There are at least two ways to prove pointwise bounds on the displacement interpolant. The first one consists in combining the Jacobian equation involving the density of the interpolant (Chapter 11) with the Jacobian determinant estimates derived from the Ricci curvature bounds (Chapter 14). The second way goes via displacement convexity (Chapter 17); it is quite more indirect, but its interest will become apparent later in Chapter 30.

Of course, pointwise bounds do not result directly from displacement convexity, which only yields integral bounds on the interpolant; still, it is possible to deduce pointwise bounds from integral bounds by using the stability of optimal transport under restriction (Theorem 4.6). The idea is simple: a pointwise bound on  $\rho_t(x)$  will be achieved by considering integral bounds on a very small ball  $B_{\delta}(x)$ , as  $\delta \to 0$ .

Apart from the local Poincaré inequality, the pointwise control on the density will imply at once the Brunn–Minkowski inequality, and also its functional counterpart, the Prékopa–Leindler inequality. This is not surprising, since a pointwise control is morally stronger than an integral control.

#### Pointwise estimates on the interpolant density

The next theorem is the key result of this chapter. The notation  $[x,y]_t$ stands for the set of all *t*-barycenters of x and y (as in Theorem 18.5).

Theorem 19.4  $(CD(K, N)$  implies pointwise bounds on displacement interpolants). Let M be a Riemannian manifold equipped with a reference measure  $\nu = e^{-V}$ vol,  $V \in C^2(M)$ , satisfying a curvaturedimension  $CD(K, N)$  for some  $K \in \mathbb{R}$ ,  $N \in (1, \infty]$ . Further, let  $\mu_0 = \rho_0 \nu$  and  $\mu_1 = \rho_1 \nu$  be two probability measures in  $P_p^{\rm ac}(M)$ , where  $p \in [2, +\infty) \cup \{c\}$  satisfies the assumptions of Theorem 17.8. Let  $(\mu_t)_{0 \leq t \leq 1}$  be the unique displacement interpolation between  $\mu_0$  and  $\mu_1$ , and let  $\rho_t$  stand for the density of  $\mu_t$  with respect to  $\nu$ . Then for any  $t \in (0,1)$ ,

• If  $N < \infty$ , one has the pointwise bound

$$
\rho_t(x) \le \sup_{x \in [x_0, x_1]_t} \left( (1 - t) \left( \frac{\rho_0(x_0)}{\beta_{1-t}^{(K,N)}(x_0, x_1)} \right)^{-\frac{1}{N}} + t \left( \frac{\rho_1(x_1)}{\beta_t^{(K,N)}(x_0, x_1)} \right)^{-\frac{1}{N}} \right)^{-N}, \tag{19.2}
$$

where by convention  $((1-t)a^{-\frac{1}{N}}+tb^{-\frac{1}{N}})^{-N} = 0$  if either a or b is 0;

• If  $N = \infty$ , one has the pointwise bound

$$
\rho_t(x) \le \sup_{x \in [x_0, x_1]_t} \rho_0(x_0)^{1-t} \rho_1(x_1)^t \exp\left(-\frac{Kt(1-t)}{2} d(x_0, x_1)^2\right).
$$
\n(19.3)

Corollary 19.5 (Preservation of uniform bounds in nonnegative curvature). With the same notation as in Theorem 19.4, if  $K \geq 0$  then

$$
\|\rho_t\|_{L^{\infty}(\nu)} \leq \max\;\big(\|\rho_0\|_{L^{\infty}(\nu)}, \|\rho_1\|_{L^{\infty}(\nu)}\big).
$$

As I said before, there are (at least) two possible schemes of proof for Theorem 19.4. The first one is by direct application of the Jacobian estimates from Chapter 14; the second one is based on the displacement convexity estimates from Chapter 17. The first one is formally simpler, while the second one has the advantage of being based on very robust functional inequalities. I shall only sketch the first proof, forgetting about regularity issues, and give a detailed treatment of the second one.

Sketch of proof of Theorem 19.4 by Jacobian estimates. Let  $\psi : M \to$  $\mathbb{R} \cup \{+\infty\}$  be a  $(d^2/2)$ -convex function so that  $\mu_t = [\exp(t\widetilde{\nabla}\psi)]_{\#}\mu_0$ . Let  $\mathcal{J}(t,x)$  stand for the Jacobian determinant of  $\exp(t\overline{\nabla}\psi)$ ; then, with the shorthand  $x_t = \exp_{x_0}(t\nabla \psi(x_0))$ , the Jacobian equation of change of variables can be written

$$
\rho_0(x_0) = \rho_t(x_t) \mathcal{J}(t, x_0).
$$

Similarly,

$$
\rho_0(x_0) = \rho_1(x_1) \mathcal{J}(1, x_0).
$$

Then the result follows directly from Theorems 14.11 and 14.12: Apply equation (14.56) if  $N < \infty$ , (14.55) if  $N = \infty$  (recall that  $\mathcal{D} = \mathcal{J}\frac{1}{N}$ ,  $\ell = -\log \mathcal{J}$ ).

Proof of Theorem 19.4 by displacement interpolation. For simplicity I shall only consider the case  $N < \infty$ , and derive the conclusion from Theorem 17.37. Then the case  $N = \infty$  can be treated by adapting the proof of the case  $N < \infty$ , replacing Theorem 17.37 by Theorem 17.15, and using the function  $U_{\infty}$  defined in (16.17). (Formally, it amounts to taking the limit  $N \to \infty$  in (19.2).)

Let  $t \in (0,1)$  be given, let  $(\mu_s)_{0 \leq s \leq 1}$  be as in the statement of the theorem, and let  $\Pi$  be the law of a random geodesic  $\gamma$  such that law  $(\gamma_s) = \mu_s$ . Let y be an arbitrary point in M, and  $\delta > 0$ ; the goal is to estimate from above the probability  $\mathbb{P}\left[\gamma_t \in B_\delta(y)\right] = \mu_t[B_\delta(y)]$ , so as to recover a bound on  $\rho_t(y)$  as  $\delta \to 0$ .

If  $\mathbb{P}[\gamma_t \in B_\delta(y)] = 0$ , then there is nothing to prove. Otherwise we may condition  $\gamma$  by the event " $\gamma_t \in B_\delta(y)$ ". Explicitly, this means: Introduce  $\gamma'$  such that law  $(\gamma') = \Pi' = (1_{\mathcal{Z}}\Pi)/\Pi[\mathcal{Z}]$ , where

$$
\mathcal{Z} = \Big\{ \gamma \in \Gamma(M); \ \gamma_t \in B_\delta(y) \Big\}.
$$

Further, define  $\pi' = \text{law}(\gamma'_0, \gamma'_1), \mu'_s = \text{law}(\gamma'_s) = (e_s)_{\#}\Pi'.$  Obviously,

$$
\Pi' \leq \frac{\Pi}{\Pi[\mathcal{Z}]} = \frac{\Pi}{\mu_t[B_\delta(y)]},
$$

so for all  $s \in [0,1]$ ,

$$
\mu_s' \leq \frac{\mu_s}{\mu_t[B_\delta(y)]}.
$$

In particular,  $\mu'_{s}$  is absolutely continuous and its density  $\rho'_{s}$  satisfies  $(\nu\text{-almost surely})$ 

$$
\rho_s' \le \frac{\rho_s}{\mu_t[B_\delta(y)]}.\tag{19.4}
$$

When  $s = t$ , inequality (19.4) can be refined into

$$
\rho_t' = \frac{\rho_t \, 1_{B_\delta(y)}}{\mu_t[B_\delta(y)]},\tag{19.5}
$$

since

$$
(e_t)_\# \left( \frac{1_{\gamma_t \in B_\delta(y)}}{\mu_t[B_\delta(y)]} \right) = \frac{1_{x \in B_\delta(y)}((e_t)_\# \Pi)}{\mu_t[B_\delta(y)]}.
$$

(This is more difficult to write down than to understand!)

From the restriction property (Theorem 4.6),  $(\gamma_0', \gamma_1')$  is an optimal coupling of  $(\mu'_0, \mu'_1)$ , and therefore  $(\mu'_s)_{0 \le s \le 1}$  is a displacement interpolation. By Theorem 17.37 applied with  $U(r) = -r^{1-\frac{1}{N}}$ ,

$$
\int_{M} (\rho'_{t})^{1-\frac{1}{N}} d\nu \ge (1-t) \int_{M \times M} (\rho'_{0}(x_{0}))^{-\frac{1}{N}} \beta_{1-t}(x_{0}, x_{1})^{\frac{1}{N}} \pi'(dx_{0} dx_{1}) \\ + t \int_{M \times M} (\rho'_{1}(x_{1}))^{-\frac{1}{N}} \beta_{t}(x_{0}, x_{1})^{\frac{1}{N}} \pi'(dx_{0} dx_{1}). \quad (19.6)
$$

By definition,  $\mu'_t$  is supported in  $B_\delta(y)$ , so

$$
\int_{M} (\rho'_{t})^{1-\frac{1}{N}} d\nu = \int_{B_{\delta}(y)} (\rho'_{t})^{1-\frac{1}{N}} d\nu
$$

$$
= \nu[B_{\delta}(y)] \int_{B_{\delta}(y)} (\rho'_{t})^{1-\frac{1}{N}} \frac{d\nu}{\nu[B_{\delta}(y)]}. \tag{19.7}
$$

By Jensen's inequality, applied with the concave function  $r \to r^{1-\frac{1}{N}}$ ,

$$
\int_{B_{\delta}(y)} (\rho'_t)^{1-\frac{1}{N}} \, \frac{d\nu}{\nu[B_{\delta}(y)]} \le \left( \int \rho'_t \, \frac{d\nu}{\nu[B_{\delta}(y)]} \right)^{1-\frac{1}{N}} = \frac{1}{\nu[B_{\delta}(y)]^{1-\frac{1}{N}}}.
$$

Plugging this into (19.7), we find

$$
\int_{M} (\rho_t')^{1-\frac{1}{N}} \, d\nu \le \nu [B_\delta(y)]^{\frac{1}{N}}.
$$
\n(19.8)

On the other hand, from (19.4) the right-hand side of (19.6) can be bounded below by

$$
\mu_t[B_\delta(y)]^{\frac{1}{N}} \int_{M \times M} \left[ (1-t) \left( \rho_0(x_0) \right)^{-\frac{1}{N}} \beta_{1-t}(x_0, x_1)^{\frac{1}{N}} + t \left( \rho_1(x_1) \right)^{-\frac{1}{N}} \beta_t(x_0, x_1)^{\frac{1}{N}} \right] \pi'(dx_0 dx_1)
$$

$$
= \mu_t[B_\delta(y)]^{\frac{1}{N}} \mathbb{E}\left[ (1-t) \left( \rho_0(\gamma'_0) \right)^{-\frac{1}{N}} \beta_{1-t}(\gamma'_0, \gamma'_1)^{\frac{1}{N}} + t \left( \rho_1(\gamma'_1) \right)^{-\frac{1}{N}} \beta_t(\gamma'_0, \gamma'_1)^{\frac{1}{N}} \right]
$$

$$
\geq \mu_t[B_{\delta}(y)]^{\frac{1}{N}} \mathbb{E} \inf_{\gamma_t o [x_0, x_1]_t} \left[ (1-t) \left( \rho_0(\gamma'_0) \right)^{-\frac{1}{N}} \beta_{1-t}(\gamma'_0, \gamma'_1)^{\frac{1}{N}} + t \left( \rho_1(\gamma'_1) \right)^{-\frac{1}{N}} \beta_t(\gamma'_0, \gamma'_1)^{\frac{1}{N}} \right], \quad (19.9)
$$

where the last inequality follows just from the (obvious) remark that  $\gamma'_t \in [\gamma'_0, \gamma'_1]_t$ . In all of these inequalities, we can restrict  $\pi'$  to the set  ${\rho_0(x_0) > 0, \rho_1(x_1) > 0}$  which is of full measure. Let

$$
F(x) := \inf_{x \in [x_0, x_1]_t} \left[ (1 - t) \left( \rho_0(x_0) \right)^{-\frac{1}{N}} \beta_{1-t}(x_0, x_1)^{\frac{1}{N}} + t \left( \rho_1(x_1) \right)^{-\frac{1}{N}} \beta_t(x_0, x_1)^{\frac{1}{N}} \right];
$$

and by convention  $F(x) = 0$  if either  $\rho_0(x_0)$  or  $\rho_1(x_1)$  vanishes. (Forget about the measurability of  $F$  for the moment.) Then in view of  $(19.5)$ the lower bound in (19.9) can be rewritten as

$$
\mathbb{E} F(\gamma'_t) = \int_M F(x) d\mu'_t(x) = \frac{\int_{B_\delta(y)} F(x) d\mu_t(x)}{\mu_t[B_\delta(y)]}.
$$

Combined with the upper bound (19.8), this implies

$$
\left(\frac{\mu_t[B_\delta(y)]}{\nu[B_\delta(y)]}\right)^{-\frac{1}{N}} \ge \frac{\int_{B_\delta(y)} F(x) d\mu_t(x)}{\mu_t[B_\delta(y)]}.
$$
 (19.10)

**Lebesgue's density theorem** tells the following: If  $\varphi$  is a locally integrable function, then  $\nu(dy)$ -almost any y is a Lebesgue point of  $\varphi$ , which means

$$
\frac{1}{\nu[B_{\delta}(y)]}\int_{B_{\delta}(y)}\varphi(x)\,d\nu(x)\; \xrightarrow[\delta\downarrow 0]{}\varphi(y).
$$

In particular, if y is a Lebesgue point of  $\rho_t$ , then

$$
\frac{\mu_t[B_\delta(y)]}{\nu[B_\delta(y)]} = \frac{\displaystyle\int_{B_\delta(y)} \rho_t(x) \, d\nu(x)}{\nu[B_\delta(y)]} \xrightarrow[\delta \downarrow 0]{} \rho_t(y).
$$

The inequality in (19.10) proves that  $F_{\rho_t}$  is locally  $\nu$ -integrable; therefore also

$$
\frac{\int_{B_\delta(y)} F(x) \, d\mu_t(x)}{\nu[B_\delta(y)]} \xrightarrow[\delta \downarrow 0]{} F(y) \, \rho_t(y).
$$

If one plugs these two limits in (19.10), one obtains

$$
\rho_t(y)^{-\frac{1}{N}} \ge \frac{F(y)\,\rho_t(y)}{\rho_t(y)} = F(y),
$$

provided that  $\rho_t(y) > 0$ ; and then  $\rho_t(y) \leq F(y)^{-N}$ , as desired. In the case  $\rho_t(y) = 0$  the conclusion still holds true.

Some final words about measurability. It is not clear (at least to me) that  $F$  is measurable; but instead of  $F$  one may use the measurable function

$$
\widetilde{F}(x) = (1-t)\,\rho_0(\gamma_0)^{-\frac{1}{N}}\,\beta_{1-t}(\gamma_0,\gamma_1)^{\frac{1}{N}} + t\,\rho_1(\gamma_1)^{-\frac{1}{N}}\beta_t(\gamma_0,\gamma_1)^{\frac{1}{N}},
$$

where  $\gamma = F_t(x)$ , and  $F_t$  is the measurable map defined in Theorem 7.30(v). Then the same argument as before gives  $\rho_t(y) \leq \widetilde{F}(y)^{-N}$ , and this is obviously bounded above by  $F(y)^{-N}$ . □

It is useful to consider the particular case when the initial density  $\mu_0$  is a Dirac mass and the final mass is the uniform distribution on some set B:

Theorem 19.6 (Jacobian bounds revisited). Let M be a Riemannian manifold equipped with a reference measure  $\nu = e^{-V}$ vol,  $V \in C^2(M)$ , satisfying a curvature-dimension condition  $CD(K, N)$  for some  $K \in \mathbb{R}, N \in (1,\infty)$ . Let  $z_0 \in M$  and let B be a bounded set of positive measure. Further, let  $(\mu_t^{z_0})_{0 \leq t \leq 1}$  be the displacement interpolation joining  $\mu_0 = \delta_{z_0}$  to  $\mu_1 = (1_B \nu) \bar{\psi}[B]$ . Then the density  $\rho_t^{z_0}$  of  $\mu_t^{z_0}$ satisfies

$$
\rho_t^{z_0}(x) \le \frac{C(K, N, R)}{t^N \nu[B]},
$$

where

$$
C(K, N, R) = \exp(-\sqrt{(N-1)K_{-}}R), \qquad K_{-} = \max(-K, 0),
$$
\n(19.11)

and R is an upper bound on the distances between  $z_0$  and elements of B. In particular, if  $K \geq 0$ , then

$$
\rho_t^{z_0}(x) \le \frac{1}{t^N \nu[B]}.
$$

Remark 19.7. Theorem 19.6 is a classical estimate in Riemannian geometry; it is often stated as a bound on the Jacobian of the map  $(s, \xi) \mapsto \exp_x(s\xi)$ . It will be a good exercise for the reader to convert Theorem 19.6 into such a Jacobian bound.

*Proof of Theorem 19.6.* Let  $z_0$  and B be as in the statement of the lemma, and let  $\mu_1 = (1_B \nu)/\nu[B]$ . Consider a displacement interpolation  $(\mu_t)_{0 \le t \le 1}$  between  $\mu_0 = \delta_{z_0}$  and  $\mu_1$ . Recall from Chapter 13 that  $\mu_t$  is absolutely continuous for all  $t \in (0, 1]$ . So Theorem 19.4 can be applied to the (reparametrized) displacement interpolation  $(\mu_t')_{0 \leq t \leq 1}$ defined by  $\mu'_t = \mu_{t'}, t' = t_0 + (1 - t_0)t$ ; this yields

$$
\rho_{t'}(x) \le \sup_{x \in [x_0, x_1]_t} \left[ (1-t) \beta_{1-t}(x_0, x_1)^{\frac{1}{N}} \rho_{t_0}(x_0)^{-\frac{1}{N}} + t \beta_t(x_0, x_1)^{\frac{1}{N}} \rho_1(x_1)^{-\frac{1}{N}} \right]^{-N}. (19.12)
$$

Clearly, the sum above can be restricted to those pairs  $(x_0, x_1)$  such that  $x_1$  lies in the support of  $\mu_1$ , i.e.  $x_1 \in B$ ; and  $x_0$  lies in the support of  $\mu_{t_0}$ ,

which implies  $x_0 \in [z_0, B]_{t_0}$ . Moreover, since  $z \to z^{-N}$  is nonincreasing, one has the obvious bound

$$
\rho_{t'}(x) \le \sup_{x \in [x_0, x_1]_t; \ x_0 \in [z_0, B]_{t_0}; \ x_1 \in B} \left[ t \beta_t(x_0, x_1)^{\frac{1}{N}} \rho_1(x_1)^{-\frac{1}{N}} \right]^{-N}
$$
$$
= \sup_{x \in [x_0, x_1]_t; \ x_0 \in [z_0, B]_{t_0}; \ x_1 \in B} \frac{\rho_1(x_1)}{t^N \beta_t(x_0, x_1)}.
$$

Since  $\rho_1 = 1_B/\nu[B]$ , actually

$$
\rho_{t'}(x) \le \frac{S(t_0, z_0, B)}{t^N \nu[B]},
$$

where

$$
S(t_0, z_0, B) := \sup \Big\{ \beta_t(x_0, x_1)^{-\frac{1}{N}}; \quad x_0 \in [z_0, B]_{t_0}, \ x_1 \in B \Big\}. \tag{19.13}
$$

Now let  $t_0 \rightarrow 0$  and t go to t', in such a way that t' stays fixed. Since B is bounded, the geodesics linking  $z_0$  to an element of B have a uniformly bounded speed, so the set  $[z_0, B]_{t_0}$  is included in a ball  $B(z,V t_0)$  for some constant V; this shows that those  $x_0$  appearing in (19.13) converge uniformly to  $z_0$ . By continuity of  $\beta_t$ ,  $S(t_0, z_0, B)$ converges to  $S(0, z_0, B)$ . Then an elementary estimate of  $\beta_t$  shows that  $S(0, z_0, B) \leq C(K, N, R)$ . This finishes the proof. □

To conclude, I shall state a theorem which holds true with the intrinsic distortion coefficients of the manifold, without any reference to a choice of  $K$  and  $N$ , and without any assumption on the behavior of the manifold at infinity (if the total cost is infinite, we can appeal to the notion of generalized optimal coupling and generalized displacement interpolation, as in Chapter 13). Recall Definition 14.17.

Theorem 19.8 (Intrinsic pointwise bounds on the displacement interpolant). Let M be an n-dimensional Riemannian manifold equipped with some reference measure  $\nu = e^{-V}$  vol,  $V \in C^2(M)$ , and let  $\overline{\beta}$  be the associated distortion coefficients. Let  $\mu_0, \mu_1$  be two absolutely continuous probability measures on M, let  $(\mu_t)_{0 \leq t \leq 1}$  be the unique generalized displacement interpolation between  $\mu_0$  and  $\mu_1$ , and let  $\rho_t$  be the density of  $\mu_t$  with respect to  $\nu$ . Then one has the pointwise bound

$$
\rho_t(x) \le \sup_{x \in [x_0, x_1]_t} \left( (1-t) \left( \frac{\rho_0(x_0)}{\overline{\beta}_{1-t}(x_0, x_1)} \right)^{-\frac{1}{n}} + t \left( \frac{\rho_1(x_1)}{\overline{\beta}_{t}(x_0, x_1)} \right)^{-\frac{1}{n}} \right)^{-n},\tag{19.14}
$$

where by convention  $((1-t)a^{-\frac{1}{n}}+tb^{-\frac{1}{n}})^{-n}=0$  if either a or b is 0.

Proof of Theorem 19.8. First use the standard approximation procedure of Proposition 13.2 to define probability measures  $\mu_{t,\ell}$  with density  $\rho_{t,\ell}$ , and numbers  $Z_{\ell}$  such that  $Z_{\ell} \uparrow 1$ ,  $Z_{\ell} \rho_{t,\ell} \uparrow \rho_t$ , and  $\mu_{t,\ell}$  are compactly supported.

Then we can redo the proof of Theorem 19.4 with  $\mu_{0,\ell}$  and  $\mu_{1,\ell}$ instead of  $\mu_0$  and  $\mu_1$ , replacing Theorem 17.37 by Theorem 17.42. The result is

$$
\rho_{t,\ell}(x) \leq \sup_{x \in [x_0,x_1]_t} \left( (1-t) \left( \frac{\rho_{0,\ell}(x_0)}{\overline{\beta}_{1-t}(x_0,x_1)} \right)^{-\frac{1}{n}} + t \left( \frac{\rho_{1,\ell}(x_1)}{\overline{\beta}_{t}(x_0,x_1)} \right)^{-\frac{1}{n}} \right)^{-n}.
$$

Since  $Z_{\ell} \rho_{t,\ell} \leq \rho_t$ , it follows that

$$
Z_{\ell} \rho_{t,\ell}(x) \leq \sup_{x \in [x_0, x_1]_t} \left( (1-t) \left( \frac{Z_{\ell} \rho_{0,\ell}(x_0)}{\overline{\beta}_{1-t}(x_0, x_1)} \right)^{-\frac{1}{n}} + t \left( \frac{Z_{\ell} \rho_{1,\ell}(x_1)}{\overline{\beta}_{t}(x_0, x_1)} \right)^{-\frac{1}{n}} \right)^{-n}
$$
  
$$
\leq \sup_{x \in [x_0, x_1]_t} \left( (1-t) \left( \frac{\rho_0(x_0)}{\overline{\beta}_{1-t}(x_0, x_1)} \right)^{-\frac{1}{n}} + t \left( \frac{\rho_1(x_1)}{\overline{\beta}_{t}(x_0, x_1)} \right)^{-\frac{1}{n}} \right)^{-n}.
$$

The conclusion is obtained by letting  $\ell \to \infty$ . □

## Democratic condition

Local Poincaré inequalities are conditioned, loosely speaking, to the "richness" of the space of geodesics: One should be able to transfer mass between sets by going along geodesics, in such a way that different points use geodesics that do not get "too close to each other". This idea (which is reminiscent of the intuition behind the distorted Brunn– Minkowski inequality) will be more apparent in the following condition. It says that one can use geodesics to redistribute all the mass of a ball in such a way that each point in the ball sends all its mass uniformly over the ball, but no point is visited too often in the process. In the next definition, what I call "uniform distribution on  $B$ " is the reference measure  $\nu$ , conditioned on the ball, that is,  $(1_B\nu)/\nu[B]$ . The definition is formulated in the setting of a geodesic space (recall the definitions about length spaces in Chapter 7), but in this chapter I shall apply it only in Riemannian manifolds.

Definition 19.9 (Democratic condition). A Borel measure  $\nu$  on a geodesic space  $(\mathcal{X}, d)$  is said to satisfy the democratic condition  $Dm(C)$ for some constant  $C > 0$  if the following property holds true: For any closed ball B in X there is a random geodesic  $\gamma$  such that  $\gamma_0$  and  $\gamma_1$ are independent and distributed uniformly in B, and the time-integral of the density of  $\gamma_t$  (with respect  $\nu$ ) never exceeds  $C/\nu[B]$ .

The condition is said to hold uniformly if the constant  $C$  is independent of the ball  $B = B[x, r]$ , and locally uniformly if it is independent of B as long as  $B[x, 2r]$  remains inside a large fixed ball  $B[z, R]$ .

A more explicit formulation of the democratic condition is as follows: If  $\mu_t$  stands for the law of  $\gamma_t$ , then

$$
\int_{0}^{1} \mu_{t} dt \le C \frac{\nu}{\nu[B]}.
$$
\n(19.15)

**Theorem 19.10** (CD $(K, N)$  implies Dm). Let M be a Riemannian manifold equipped with a reference measure  $\nu = e^{-V}$ vol,  $V \in C^2(M)$ , satisfying a curvature-dimension condition  $CD(K, N)$  for some  $K \in \mathbb{R}$ ,  $N \in (1,\infty)$ . Then v satisfies a locally uniform democratic condition, with an admissible constant  $2^N C(K, N, R)$  in a large ball  $B[z, R]$ , where  $C(K, N, R)$  is defined in (19.11).

In particular, if  $K \geq 0$ , then v satisfies the uniform democratic condition  $Dm(2^N)$ .

Proof of Theorem 19.10. The proof is largely based on Theorem 19.6.

Let B be a ball of radius r. For any point  $x_0$ , let  $\mu_t^{x_0}$  be as in the statement of Theorem 19.6; then its density  $\rho_t^{x_0}$  (with respect to  $\nu$ ) is bounded above by  $C(K, N, R)/(t^N \nu[B]).$ 

On the other hand,  $\mu_t^{x_0}$  can be interpreted as the position at time t of a random geodesic  $\gamma^{x_0}$  starting at  $x_0$  and ending at  $x_1$ , which is distributed according to  $\mu$ . By integrating this against  $\mu(dx_0)$ , we obtain the position at time t of a random geodesic  $\gamma$  such that  $\gamma_0$  and  $\gamma_1$  are independent and both distributed according to  $\mu$ . Explicitly,

$$
\mu_t = \text{law}(\gamma_t) = \int_M \mu_t^{x_0} d\mu(x_0).
$$

Obviously, the uniform bound on  $\rho_t$  persists upon integration, so

$$
\mu_t \le \left[ \frac{C(K, N, R)}{t^N \nu[B]} \right] \nu.
$$
\n(19.16)

Recall that  $\mu_t = \text{law}(\gamma_t)$ , where  $\gamma_0, \gamma_1$  are independent and distributed according to  $\mu$ . Since geodesics in a Riemannian manifold are almost surely unique, we can throw away a set of zero volume in  $B \times B$ such that for each  $(x,y) \in (B \times B) \setminus Z$ , there is a unique geodesic  $(\gamma_t^{x_0,x_1})_{0\leq t\leq 1}$  going from  $x_0$  to  $x_1$ . Then  $\mu_t$  is characterized as the law of  $\gamma_t^{x_0,x_1}$ , where law  $(x_0,x_1) = \mu \otimes \mu$ . If we repeat the construction by exchanging the variables  $x_0$  and  $x_1$ , and replacing t by  $1-t$ , then we get the same path  $(\mu_t)$ , up to reparametrization of time. So

$$
\mu_t \le \left[ \frac{C(K, N, R)}{(1 - t)^N \nu[B]} \right] \nu.
$$
\n(19.17)

Combining (19.16) and (19.17) and passing to densities, one obtains that,  $\nu(dx)$ -almost surely,

$$
\rho_t(x) \le C(K, N, R) \min\left(\frac{1}{t^N}, \frac{1}{(1-t)^N}\right) \frac{1}{\nu[B]} \le \frac{2^N C(K, N, R)}{\nu[B]},
$$
\n(19.18)

which implies Theorem 19.10. □

Remark 19.11. The above bounds (19.18) can be improved as follows. Let  $\mu = \rho \nu$  be a probability measure which is absolutely continuous with respect to  $\nu$ , and otherwise arbitrary. Then there exists a random geodesic  $\gamma$  such that law  $(\gamma_0, \gamma_1) = \mu \otimes \mu$ , law  $(\gamma_t)$  admits a density  $\rho_t$ with respect to  $\nu$ , and

$$
\|\rho_t\|_{L^p(\nu)} \le C(K, N, R)^{\frac{1}{p'}} \min\left(\frac{1}{t^{N/p'}}, \frac{1}{(1-t)^{N/p'}}\right) \|\rho\|_{L^p(\nu)} \quad (19.19)
$$

for all  $p \in (1, \infty)$ , where  $p' = p/(p-1)$  is the conjugate exponent to p.

#### Local Poincaré inequality

**Convention 19.12.** If  $\nu$  satisfies a local Poincaré inequality for some constant  $C$ , I shall say that it satisfies a uniform local Poincaré inequality. If  $\nu$  satisfies a local Poincaré inequality in each ball  $B_R(z)$ , with a constant that may depend on z and R, I shall just say that  $\nu$  satisfies a local Poincaré inequality.

Theorem 19.13 (Doubling  $+$  democratic imply local Poincaré). Let  $(\mathcal{X}, d)$  be a length space equipped with a reference measure  $\nu$  satisfying a doubling condition with constant D, and a democratic condition with constant C. Then  $\nu$  satisfies a local Poincaré inequality with constant  $P = 2 C D$ .

If the doubling and democratic conditions hold true inside a ball  $B(z, R)$  with constants  $C = C(z, R)$  and  $D = D(z, R)$  respectively, then  $\nu$  satisfies a local Poincaré inequality in the ball  $B(z,R)$  with constant  $P(z, R) = 2 C(z, R) D(z, R).$ 

Before giving the proof of Theorem 19.13 I shall state a corollary which follows immediately from this theorem together with Corollary 18.11 and Theorem 19.10:

Corollary 19.14 ( $CD(K, N)$  implies local Poincaré). Let M be a Riemannian manifold equipped with a reference measure  $\nu = e^{-V}$ vol,  $V \in C^2(M)$ , satisfying a curvature-dimension condition  $CD(K, N)$  for some  $K \in \mathbb{R}$ ,  $N \in (1,\infty)$ . Then v satisfies a local Poincaré inequality with constant  $P(K, N, R) = 2^{2N+1} C(K, N, R) D(K, N, R)$ , inside any ball  $B[z, R]$ , where  $C(K, N, R)$  and  $D(K, N, R)$  are defined by (19.11) and (18.10).

In particular, if  $K \geq 0$  then  $\nu$  satisfies a local Poincaré inequality on the whole of M with constant  $2^{2N+1}$ .

*Proof of Theorem 19.13.* Let  $x_0$  be a given point in M. Given  $r > 0$ , write  $B = B_{r}(x_0)$ , and  $2B = B_{2r}(x_0)$ . As before, let  $\mu = (1_B \nu)/\nu[B]$ . Let  $u : B \to \mathbb{R}$  be an arbitrary Lipschitz function. For any  $y_0 \in M$ , we have

$$
u(y_0)-\langle u\rangle_B = \int_M \bigl(u(y_0)-u(y_1)\bigr)\,d\mu(y_1).
$$

Then

$$
\int_{B} |u - \langle u \rangle_{B}| \, d\nu = \int_{M} |u(y_{0}) - \langle u \rangle_{B}| \, d\mu(y_{0})
$$

$$
\leq \int_{B \times B} |u(y_{0}) - u(y_{1})| \, d\mu(y_{0}) \, d\mu(y_{1}). \tag{19.20}
$$

Next, let us estimate  $|u(y_0) - u(y_1)|$  in terms of a constant-speed geodesic path  $\gamma$  joining  $y_0$  to  $y_1$ , where  $y_0, y_1 \in B$ . The length of such a geodesic path is at most 2r. Then, with the shorthand  $g = |\nabla u|$ ,

$$
\left| u(y_0) - u(y_1) \right| \leq 2r \int_0^1 g(\gamma(t)) dt. \tag{19.21}
$$

By assumption there is a random geodesic  $\gamma : [0,1] \rightarrow M$  such that law  $(\gamma_0, \gamma_1) = \mu \otimes \mu$  and  $\mu_t = \text{law}(\gamma_t)$  satisfies (19.15). Integrating (19.21) against the law of  $\gamma$  yields

$$
\int_{M \times M} |u(y_0) - u(y_1)| d\mu(y_0) d\mu(y_1) \le \mathbb{E} \left( 2r \int_0^1 g(\gamma(t)) dt \right) (19.22)
$$
$$
= 2r \int_0^1 \mathbb{E} g(\gamma(t)) dt
$$
$$
= 2r \int_0^1 \int_M g d\mu_t dt.
$$

This, combined with (19.20), implies

$$
\int_{B} |u - \langle u \rangle_{B}| \, d\nu \leq 2r \int_{0}^{1} \int_{M} g \, d\mu_{t} \, dt. \tag{19.23}
$$

However, a geodesic joining two points in  $B$  cannot leave the ball  $2B$ , so (19.23) and the democratic condition together imply that

$$
\int_{B} |u - \langle u \rangle_B| \, d\nu \le \frac{2 \, C \, r}{\nu[B]} \int_{2B} g \, d\nu. \tag{19.24}
$$

By the doubling property,  $\frac{1}{\nu[B]} \leq \frac{D}{\nu[2]}$  $\frac{D}{\nu[2B]}$ . The conclusion is that

$$
\int_{B} |u - \langle u \rangle_B| d\nu \le 2CD \, r \int_{2B} g \, d\nu. \tag{19.25}
$$

This concludes the proof of Theorem 19.13. □

[]

Remark 19.15. With almost the same proof, it is easy to derive the following refinement of the local Poincaré inequality:

$$
\int_{B[x,r]} \frac{|u(x) - u(y)|}{d(x,y)} d\nu(x) d\nu(y) \le P(K, N, R) \int_{B[x,2r]} |\nabla u|(x) d\nu(x).
$$

## Back to Brunn–Minkowski and Prékopa–Leindler inequalities

To conclude this chapter I shall explain how the Brunn–Minkowski inequality (18.5) comes at once from the pointwise estimates on the interpolant density.

*Proof of Theorem 18.5, again.* Let  $\mu_0$  be the measure  $\nu$  conditioned on  $A_0$ , i.e.  $\mu_0 = \rho_0 \nu$  with  $\rho_0 = 1_{A_0}/\nu[A_0]$ . Similarly, let  $\mu_1 = \rho_1 \nu$  with  $\rho_1 = 1_{A_1}/\nu[A_1]$ . Let  $\rho_t$  be the density of the displacement interpolant at time t. Then, since  $\rho_0$  vanishes out of  $A_0$ , and  $\rho_1$  out of  $A_1$ , Theorem 19.4 implies

$$
\rho_t(x)^{-\frac{1}{N}} \ge (1-t) \left[ \inf_{x \in [A_0, A_1]_t} \beta_{1-t}(x_0, x_1)^{\frac{1}{N}} \right] \nu[A_0]^{\frac{1}{N}} + t \left[ \inf_{x \in [A_0, A_1]_t} \beta_t(x_0, x_1)^{\frac{1}{N}} \right] \nu[A_1]^{\frac{1}{N}}
$$

$$
\ge (1-t) \left[ \inf_{(x_0, x_1) \in A_0 \times A_1} \beta_{1-t}(x_0, x_1)^{\frac{1}{N}} \right] \nu[A_0]^{\frac{1}{N}} + t \left[ \inf_{(x_0, x_1) \in A_0 \times A_1} \beta_t(x_0, x_1)^{\frac{1}{N}} \right] \nu[A_1]^{\frac{1}{N}}.
$$

Now integrate this against  $\rho_t(x) d\nu(x)$ : since the right-hand side does not depend on  $x$  any longer,

$$
\int \rho_t(x)^{1-\frac{1}{N}} d\nu(x) \ge (1-t) \left[ \inf_{x \in [A_0, A_1]_t} \beta_{1-t}(x_0, x_1)^{\frac{1}{N}} \right] \nu[A_0]^{\frac{1}{N}} + t \left[ \inf_{x \in [A_0, A_1]_t} \beta_t(x_0, x_1)^{\frac{1}{N}} \right] \nu[A_1]^{\frac{1}{N}}.
$$

On the other hand,  $\rho_t$  is concentrated on  $[A_0, A_1]_t$ , so the same Jensen inequality that was used in the earlier proof of Theorem 18.5 implies

$$
\int \rho_t(x)^{1-\frac{1}{N}} \, d\nu(x) \le \nu \big[ [A_0, A_1]_t \big]^{\frac{1}{N}},
$$

and inequality (18.4) follows.  $□$ 

Interestingly enough, Theorem 19.4 also implies the distorted Prékopa–Leindler inequality. This is a functional variant of the Brunn–Minkowski inequality, which is sometimes much more convenient to handle. (Here I say that the inequality is "distorted" only

because the Prékopa–Leindler inequality is usually stated in  $\mathbb{R}^n$ , while the Riemannian generalization involves distortion coefficients.) I shall first consider the dimension-free case, which is simpler and does not need distortion coefficients.

Theorem 19.16 (Prékopa–Leindler inequalities). With the same notation as in Theorem 19.4, assume that  $(M, \nu)$  satisfies the curvaturedimension condition  $CD(K,\infty)$ . Let  $t \in (0,1)$ , and let f, g, h be three nonnegative functions such that the inequality

$$
h(x) \ge \sup_{x \in [x_0, x_1]_t} f(x_0)^{1-t} g(x_1)^t \exp\left(-\frac{Kt(1-t)}{2} d(x_0, x_1)^2\right)
$$
(19.26)

is satisfied for all  $x \in M$ . Then

$$
\int h \, d\nu \ge \left(\int f \, d\nu\right)^{1-t} \left(\int g \, d\nu\right)^t.
$$

Proof of Theorem 19.16. By an easy homogeneity argument, we may assume  $\int f = \int g = 1$ . Then write  $\rho_0 = f$ ,  $\rho_1 = g$ ; by Theorem 19.4, the displacement interpolant  $\rho_t$  between  $\rho_0 \nu$  and  $\rho_1 \nu$  satisfies (19.3). From (19.26),  $h \ge \rho_t$ . It follows that  $\int h \ge \int \rho_t = 1$ , as desired.  $\Box$ 

**Remark 19.17.** Let  $(M, \nu)$  be a Riemannian manifold satisfying a curvature-dimension bound  $CD(K, \infty)$  with  $K > 0$ , and let  $A \subset M$ be a compact set such that  $\nu[A] > 0$ . Apply the Prékopa–Leindler inequality with  $t = 1/2$ ,  $f = 1_A$ ,  $g = \exp(K d(x, A)^2/4)$  and  $h = 1$ : This shows that

$$
\int_{M} e^{\frac{Kd(x,A)^2}{4}} d\nu(x) < +\infty,
$$
\n(19.27)

and one easily deduces that  $\nu$  admits square-exponential moments (something which we already know from Theorem 18.12).

I shall conclude with the dimension-dependent form of the Prékopa– Leindler inequality, which will require some more notation. For any  $a,b \geq 0, t \in [0,1], q \in \mathbb{R} \setminus \{0\},$  define

$$
\mathcal{M}_t^q(a,b) := \left[ (1-t) a^q + t b^q \right]^{\frac{1}{q}},
$$

with the convention that  $\mathcal{M}_t^q(a,b) = 0$  if either a or b is 0; and  $\mathcal{M}_t^{-\infty}(a,b) = \min(a,b).$ 

Theorem 19.18 (Finite-dimension distorted Prékopa–Leindler inequality). With the same notation as in Theorem 19.4, assume that  $(M, \nu)$  satisfies a curvature-dimension condition  $CD(K, N)$  for some  $K \in \mathbb{R}, N \in (1,\infty)$ . Let f, g and h be three nonnegative functions on M satisfying

$$
h(x) \ge \sup_{x \in [x_0, x_1]_t} \mathcal{M}_t^q \left( \frac{f(x_0)}{\beta_{1-t}^{(K,N)}(x_0, x_1)}, \frac{g(x_1)}{\beta_t^{(K,N)}(x_0, x_1)} \right), \qquad q \ge -\frac{1}{N};
$$
\n(19.28)

then

$$
\int h \, d\nu \ge \mathcal{M}_t^{\frac{q}{1+Nq}} \left( \int f \, d\nu, \, \int g \, d\nu \right). \tag{19.29}
$$

Proof of Theorem 19.18. The proof is quite similar to the proof of Theorem 19.16, except that now N is finite. Let  $f, g$  and h satisfy the assumptions of the theorem, define  $\rho_0 = f/||f||_{L^1}$ ,  $\rho_1 = g/||g||_{L^1}$ , and let  $\rho_t$  be the density of the displacement interpolant at time t between  $\rho_0 \nu$  and  $\rho_1 \nu$ . Let M be the right-hand side of (19.29); the problem is to show that  $\int (h/M) \geq 1$ , and this is obviously true if  $h/M \geq \rho_t$ . In view of Theorem 19.4, it is sufficient to establish

$$
\frac{h(x)}{\mathcal{M}} \ge \sup_{x \in [x_0, x_1]_t} \mathcal{M}_t^{\frac{1}{N}} \left( \frac{\beta_{1-t}(x_0, x_1)}{\rho_0(x_0)}, \frac{\beta_t(x_0, x_1)}{\rho_1(x_1)} \right)^{-1}.
$$
 (19.30)

In view of the assumption of h and the form of  $M$ , it is sufficient to check that

$$
\frac{1}{\mathcal{M}_t^{\frac{1}{N}}\left(\frac{\beta_{1-t}(x_0,x_1)}{\rho_0(x_0)},\ \frac{\beta_t(x_0,x_1)}{\rho_1(x_1)}\right)} \leq \frac{\mathcal{M}_t^q\left(\frac{f(x_0)}{\beta_{1-t}(x_0,x_1)},\ \frac{g(x_1)}{\beta_t(x_0,x_1)}\right)}{\mathcal{M}_t^{\frac{q}{1+Nq}}(\|f\|_{L^1},\ \|g\|_{L^1})}.
$$

But this is a consequence of the following computation:

$$
\frac{1}{\mathcal{M}_t^{-s}(a^{-1}, b^{-1})} = \mathcal{M}_t^s(a, b)
$$

$$
\leq \mathcal{M}_t^q\left(\frac{a}{c}, \frac{b}{d}\right) \mathcal{M}_t^r(c, d) = \frac{\mathcal{M}_t^q\left(\frac{a}{c}, \frac{b}{d}\right)}{\mathcal{M}_t^{-r}(c, d)}, \quad (19.31)
$$

$$
\frac{1}{q} + \frac{1}{r} = \frac{1}{s}, \qquad q + r \geq 0,
$$

where the two equalities in (19.31) are obvious by homogeneity, and the central inequality is a consequence of the two-point Hölder inequality (see the bibliographical notes for references). ⊓⊔

### Bibliographical notes

The main historical references concerning interior regularity estimates are by De Giorgi [274], Nash [646] and Moser [638, 639]. Their methods were later considerably developed in the theory of elliptic partial differential equations, see e.g. [189, 416]. Moser's Harnack inequality is a handy technical tool to recover the most famous regularity results. The relations of this inequality with Poincaré and Sobolev inequalities, and the influence of Ricci curvature on it, were studied by many authors, including in particular Saloff-Coste [726] and Grigor'yan [433].

Lebesgue's density theorem can be found in most textbooks about measure theory, e.g. Rudin [714, Chapter 7].

Local Poincaré inequalities admit many variants and are known under many names in the literature, in particular "weak Poincaré inequalities", by contrast with "strong" Poincaré inequalities, in which the larger ball is not  $B[x, 2r]$  but  $B[x, r]$ . In spite of that terminology, both inequalities are in some sense equivalent [461]. Sometimes one replaces the ball  $B[x, 2r]$  by a smaller ball  $B[x, \lambda r]$ ,  $\lambda > 1$ . One sometimes says that the inequality  $(19.1)$  is of type  $(1, 1)$  because there are  $L<sup>1</sup>$  norms on both sides. Inequality (19.1) also implies the other main members of the family of local Poincaré inequalities, see for instance Heinonen [469, Chapters 4 and 9]. There are equivalent formulations of these inequalities in terms of modulus and capacity, see e.g. [510, 511] and the many references therein. The study of Poincaré inequalities in metric spaces has turned into a surprisingly large domain of research.

Local Poincaré inequalities are also used to study large-scale geometry, see e.g. [250]. Further, inequality (19.1), applied to the whole space, is equivalent to Cheeger's isoperimetric inequality:

$$
\nu[\Omega] \le \frac{1}{2} \Longrightarrow \quad |\partial \Omega|_{\nu} \ge K \nu[\Omega],\tag{19.32}
$$

where  $\nu$  is a reference probability measure on X,  $\Omega$  is a Borel subset of X, and  $|\partial\Omega|_{\nu}$  is the v-surface of  $\Omega$ . Cheeger's inequality in turn implies the usual Poincaré inequality  $[225, 609, 610]$ . See [633] and the references therein for more details.

Theorem 19.6 is a classical estimate, usually formulated in terms of Jacobian estimates, see e.g. Saloff-Coste [728, p. 179]; the differences in the formulas are due to the convention that geodesics might be parametrized by arc length rather than defined on [0, 1]. The transportbased proof was devised by Lott and myself [578].

The "intrinsic" bounds appearing in Theorem 19.8 go back to [246] (in the compactly supported case) and [363, Section 3] (in the general case). The methods applied in these references are different. The restriction strategy which I used to prove Theorems 19.4 and 19.8 is an amplification of the transport-based proof of Theorem 19.6 from [578]. A nice alternative strategy, also based on restriction, was suggested by Sturm [763, Proof of Proposition IV.2]. Instead of conditioning with respect to the values of  $\gamma_t$ , Sturm conditions with respect to the values of  $(\gamma_0, \gamma_1)$ ; this has the technical drawback to modify the values of  $\rho_t$ , but one can get around this difficulty by a two-step limit procedure.

The proofs of Theorems 19.10 and 19.13 closely follow [578]. It was Pajot who pointed out to me the usefulness of Jacobian estimates expressed by Theorem 19.6 (recall Remark 19.7) for proving local Poincaré inequalities. The democratic condition  $Dm(C)$  was explicitly introduced in [578], but it is somehow implicit in earlier works, such as Cheeger and Colding [230]. Other proofs of the local Poincaré inequality from optimal transport, based on slightly different but quite close arguments, were found independently by von Renesse [825] and Sturm [763].

The general strategy of proof behind Theorem 19.13 is rather classical, be it in the context of Riemannian manifolds or groups or graphs; see e.g. [249] and the references therein.

The classical Prékopa–Leindler inequality in Euclidean space goes back to [548, 690]; see [406] for references and its role in the Brunn– Minkowski theory. Although in principle equivalent to the Brunn– Minkowski inequality, it is sometimes rather more useful, see e.g. a famous application by Maurey [607] for concentration inequalities. Bobkov and Ledoux [131] have shown how to use this inequality to derive many functional inequalities such as logarithmic Sobolev inequalities, to be considered in Chapter 21.

In the Euclidean case, the stronger version of the Prékopa–Leindler inequality which corresponds to Theorem 19.18 was established by Borell [144], Brascamp and Lieb [153], and others. The proof of Theorem 19.18 from Theorem 19.4 follows the argument given at the very end of  $|246|$ . The inequality used in  $(19.31)$  appears in  $|406$ , Lemma 10.1].

The Prékopa–Leindler inequality on manifolds, Theorem 19.16, shows up in a recent research paper by Cordero-Erausquin, McCann and Schmuckenschläger [247]. In that reference, displacement convex-

ity is established independently of the Prékopa–Leindler inequality, but with similar tools (namely, the Jacobian estimates in Chapter 14). The presentation that I have adopted makes it clear that the Prékopa– Leindler inequality, and even the stronger pointwise bounds in Theorem 19.4, can really be seen as a consequence of displacement convexity inequalities (together with the restriction property). This determination to derive everything from displacement convexity, rather than directly from Jacobian estimates, will find a justification in Part III of this course: In some sense the notion of displacement convexity is softer and more robust.

In  $\mathbb{R}^N$ , there is also a "stronger" version of Theorem 19.18 in which the exponent q can go down to  $-1/(N-1)$  instead of  $-1/N$ ; it reads

$$
h\left((1-t)x_0 + tx_1\right) \ge \mathcal{M}_t^q(f(x_0), g(x_1)) \implies
$$

$$
\int h(z) dz \ge \mathcal{M}_t^{\frac{q}{1+q(N-1)}}(m_i(f), m_i(g)) \cdot \mathcal{M}_t^1\left(\frac{1}{m_i(f)} \int f, \frac{1}{m_i(g)} \int g\right),
$$
 $(19.33)$ 

where  $i \in \{1, \ldots, N\}$  is arbitrary and

$$
m_i(f) = \sup_{x_i \in \mathbb{R}} \int_{\mathbb{R}^{N-1}} f(x) \, dx_1 \, \dots \, dx_{i-1} \, dx_{i+1} \, \dots \, dx_N.
$$

It was recently shown by Bobkov and Ledoux [132] that this inequality can be used to establish optimal Sobolev inequalities in  $\mathbb{R}^N$  (with the usual Prékopa–Leindler inequality one can apparently reach only the logarithmic Sobolev inequality, that is, the dimension-free case [131]). See [132] for the history and derivation of (19.33).
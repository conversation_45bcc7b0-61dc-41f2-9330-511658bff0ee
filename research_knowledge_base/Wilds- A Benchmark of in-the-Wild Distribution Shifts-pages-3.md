# 6.4 Results

Table 2 shows that models trained with CORAL, IRM, and Group DRO generally fail to improve over models trained with ERM. The exception is the CIVILCOMMENTS-WILDS subpopulation shift dataset, where the worst-performing subpopulation is a minority domain. By upweighting the minority domain, Group DRO obtains an OOD accuracy of 70.0% (on the worst-performing subpopulation) compared to 56.0% for ERM, though this is still substantially below the ERM model's ID accuracy of 92.2% (on average over the entire test set). CORAL and IRM also perform well on CIVILCOMMENTS-WILDS, though the gains there stem from the fact that our implementation heuristically upsamples the minority domain (see Appendix E.6). All other datasets involve domain generalization; the failure of the baseline algorithms here is consistent with other recent findings on standard domain generalization datasets (<PERSON><PERSON><PERSON><PERSON> and <PERSON>, 2020).

These results indicate that training models to be robust to distribution shifts in the wild remains a significant open challenge. However, we are optimistic about future progress for two reasons. First, current methods were mostly designed for other problem settings besides domain generalization, e.g., CORAL for unsupervised domain adaptation and Group DRO for subpopulation shifts. Second, compared to existing distribution shift datasets, the WILDS datasets generally contain diverse training data from many more domains as well as metadata on these domains, which future algorithms might be able to leverage.

# 7. Empirical trends

We end our discussion of experimental results by briefly reporting on several trends that we observed across multiple datasets.

# 7.1 Underspecification

Prior work has shown that there is often insufficient information at training time to distinguish models that would generalize well under distribution shift; many models that perform similarly in-distribution (ID) can vary substantially out-of-distribution (OOD) (McCoy et al., 2019a; Zhou et al., 2020; D'Amour et al., 2020a). In WILDS, we attempt to alleviate this issue by providing multiple training domains in each dataset as well as an OOD validation set for model selection. Perhaps as a result, we do not observe significantly higher variance in OOD performance than ID performance in Table 1, with the exception of Amazon-wilds and CivilComments-wilds, where the OOD performance is measured on a smaller subpopulation and is therefore naturally more variable. Excluding those datasets, the average standard deviation from Table 1 is 2.6% for OOD performance and 2.0% for ID performance, which is comparable. These results raise the question of when underspecification, as reported in prior work, could be more of an issue.

## 7.2 Model selection with in-distribution versus out-of-distribution validation sets

All of the baseline results reported in this paper use an OOD validation set for model selection, as discussed in Section 5.3. To facilitate research into comparisons of ID versus OOD performance, most WILDS datasets also provide an ID validation and/or test set. For example, in  $IWILDCAM2020-WILDS$ , the ID validation set comprises photos from the same set of camera traps used for the training set. These ID sets are not used for model selection nor official evaluation.

Gulrajani and Lopez-Paz (2020) showed that on the DomainBed domain generalization datasets, selecting models with an ID validation set leads to higher OOD performance than using an OOD validation set. This contrasts with our approach of using OOD validation sets, which we find to generally provide a good estimate of OOD test performance. Specifically, in Appendix D.1, we show that for our baseline models, model selection using an OOD validation set results in comparable or higher OOD performance than model selection using an ID validation set. This difference

could stem from many factors: for example, WILDS datasets tend to have many more domains, whereas DomainBed datasets tend to have fewer domains that can be quite different from each other (e.g., cartoons vs. photos); and there are some differences in the exact procedures for comparing performance using ID versus OOD validation sets. Further study of the effects of these different model selection procedures and choices of validation sets would be a useful direction for future work.

## 7.3 The compounding effects of multiple distribution shifts

Several Wilds datasets consider hybrid settings, where the goal is to simultaneously generalize to unseen domains as well as to certain subpopulations. We observe that combining these types of shifts can exacerbate performance drops. For example, in PovertyMap-wilds and FMoW-wilds, the shift to unseen domains exacerbates the gap in subpopulation performance (and vice versa). Notably, in FMoW-wilds, the difference in subpopulation performance (across regions) is not even manifested until also considering another shift (across time). While we do not always observe the compounding effect of distribution shifts—e.g., in AMAZON-WILDS, subpopulation performance is similar whether we consider shifts to unseen users or not—these observations underscore the importance of evaluating models on the combination of distribution shifts that would occur in practice, instead of considering each shift in isolation.

# 8. Distribution shifts in other application areas

Beyond the datasets currently included in WILDS, there are many other applications where it is critical for models to be robust to distribution shifts. In this section, we discuss some of these applications and the challenges of finding appropriate benchmark datasets in those areas. We also highlight examples of datasets with distribution shifts that we considered but did not include in Wilds, because their distribution shifts did not lead to a significant performance drop. Constructing realistic benchmarks that reflect distribution shifts in these application areas is an important avenue of future work, and we would highly welcome community contributions of benchmark datasets in these areas.

### 8.1 Algorithmic fairness

Distribution shifts which degrade model performance on minority subpopulations are frequently discussed in the algorithmic fairness literature. Geographic inequities are one concern (Shankar et al., 2017; Atwood et al., 2020): e.g., publicly available image datasets overrepresent images from the US and Europe, degrading performance in the developing world (Shankar et al., 2017) and prompting the creation of more geographically diverse datasets (Atwood et al., 2020). Racial disparities are another concern: e.g., commercial gender classifiers are more likely to misclassify the gender of darker-skinned women, likely in part because training datasets overrepresent lighter-skinned subjects (Buolamwini and Gebru, 2018), and pedestrian detection systems fare worse on darker-skinned pedestrians (Wilson et al., 2019). As in Section 4.2.1, NLP models can also show racial bias.

Unfortunately, publicly available algorithmic fairness benchmarks (Mehrabi et al., 2019)—e.g., the COMPAS recidivism dataset (Larson et al., 2016)—suffer from several limitations. First, the datasets are often quite small by the standards of modern ML: the COMPAS dataset has only a few thousand rows (Larson et al., 2016). Second, they tend to have relatively few features, and disparities in subgroup performance are not always large (Larrazabal et al., 2020), limiting the benefit of more sophisticated approaches: on COMPAS, logistic regression performs comparably to a black-box commercial algorithm (Jung et al., 2020; Dressel and Farid, 2018). Third, the datasets sometimes represent "toy" problems: e.g., the UCI Adult Income dataset (Asuncion and Newman, 2007) is widely used as a fairness benchmark, but its task—classifying whether a person will have an income above \$50,000—does not represent a real-world application. Finally, because many of the domains in which algorithmic fairness is of most concern—e.g., criminal justice and healthcare—are high-stakes and disparities are politically sensitive, it can be difficult to make datasets publicly available.

Creating algorithmic fairness benchmarks which do not suffer from these limitations represents a promising direction for future work. In particular, such datasets would ideally have: 1) information about a sensitive attribute like race or gender; 2) a prediction task which is of immediate realworld interest; 3) enough samples, a rich enough feature set, and large enough disparities in group performance that more sophisticated machine learning approaches would plausibly produce improvement over naive approaches.

Dataset: New York stop-and-frisk. Predictive policing is a prominent example of a real-world application where fairness considerations are paramount: algorithms are increasingly being used in contexts such as predicting crime hotspots (Lum and Isaac, 2016) or a defendant's risk of reoffending (Larson et al., 2016; Corbett-Davies et al., 2016, 2017; Lum and Shah, 2019). There are numerous concerns about these applications (Larson et al., 2016; Corbett-Davies et al., 2016, 2017; Lum and Shah, 2019), one of which is that these ML models might not generalize beyond the distributions that they were trained on (Corbett-Davies and Goel, 2018; Slack et al., 2019). These distribution shifts include shifts over locations—e.g., a criminal risk assessment trained on several hundred defendants in Ohio was eventually used throughout the United States (Latessa et al., 2010)—and shifts over time, as sentencing and other criminal justice policies evolve (Corbett-Davies and Goel, 2018). There are, of course, also subpopulation shift concerns around whether models are biased against particular demographic groups.

We investigated these shifts using a dataset of pedestrian stops made by the New York City Police Department under its "stop-and-frisk" policy, where the task is to predict whether a pedestrian who was stopped on suspicion of weapon possession would in fact possess a weapon (Goel et al., 2016). This policy had a pronounced racial bias: Black people stopped by the police on suspicion of possessing a weapon were  $5\times$  less likely to actually possess one than their White counterparts (Goel et al., 2016). We emphasize that we oppose stop-and-frisk (and any "improved" ML-powered stop-and-frisk) since there is overwhelming evidence that the policy was racially discriminatory (Gelman et al., 2007; Goel et al., 2016; Pierson et al., 2018) and such massive inequities require more than algorithmic fixes. Rather, we use the dataset as a realistic example of the phenomena that arise in real policing contexts, including 1) substantial heterogeneity across locations and racial groups and 2) distributions that arise in part because of biased policing practices.

Overall, we found large performance disparities across race groups and locations. Interestingly, however, we also found that these disparities cannot be attributed to the distribution shift, as the disparities were not reduced when we trained models specifically on the race groups or locations that suffer the worst performance. Indeed, the groups that see the worst performance—Black and Hispanic pedestrians—comprise large majorities of the dataset, making up more than 90% of the stops. This contrasts with the typical setting in algorithmic fairness where models perform worse on minority groups in the training data. Our results suggest the disparities are due to the dataset being noisier for some race and location groups, potentially as a result of the biased policing practices underlying the dataset. We provide further details in Appendix F.1.

## 8.2 Medicine and healthcare

Substantial evidence indicates the potential for distribution shifts in medical settings (Finlayson et al., 2021). One concern is demographic subpopulation shifts (e.g., across race, gender, or socioeconomic status), since historically-disadvantaged populations are underrepresented in many medical datasets (Chen et al., 2020). Another concern is heterogeneity *across hospitals*; this might include differences in imaging, as in Section 4.1.2, and other operational protocols such as lab tests (D'Amour et al., 2020a; Subbaswamy et al., 2020). Finally, changes over time can also produce distribution shifts: for example, Nestor et al. (2019) showed that switching between two electronic health record (EHR) systems produced a drop in performance, and the COVID-19 epidemic has affected the distribution of chest radiographs (Wong et al., 2020).

Creating medical distribution shift benchmarks thus represents a promising direction for future work, if several challenges can be overcome. First, while there are large demographic disparities in healthcare outcomes (e.g., by race or socioeconomic status), many of them are not due to distribution shifts, but to disparities in non-algorithmic factors (e.g., access to care or prevalence of comorbidities (Chen et al., 2020)) or to algorithmic problems unrelated to distribution shift (e.g., choice of a biased outcome variable (Obermeyer et al., 2019)). Indeed, several previous investigations have found relatively small disparities in algorithmic performance (as opposed to healthcare outcomes) across demographic groups (Chen et al., 2019a; Larrazabal et al., 2020); Seyyed-Kalantari et al. (2020) finds larger disparities in true positive rates across demographic groups, but this might reflect the different underlying label distributions between groups.

Second, many distribution shifts in medicine arise from concept drifts, in which the relationship between the input and the label changes, for example due to changes in clinical procedures and the definition of the label (Widmer and Kubat, 1996; Beyene et al., 2015; Futoma et al., 2020). It can be difficult to ensure that a potential benchmark has sufficient leverage for models to learn how to handle, e.g., an abrupt change in the way a particular clinical procedure is carried out.

A last challenge is data availability, as stringent medical privacy laws often preclude data sharing (Price and Cohen, 2019). For example, EHR datasets are fundamental to medical decisionmaking, but there are few widely adopted EHR benchmarks—with the MIMIC database being a prominent exception (Johnson et al., 2016)—and relatively little progress in predictive performance has been made on them (Bellamy et al., 2020).

### 8.3 Genomics

Advances in high-throughput genomic and molecular profiling platforms have enabled systematic mapping of biochemical activity of genomes across diverse cellular contexts, populations, and species (Consortium et al., 2012; Ho et al., 2014; Kundaje et al., 2015; Regev et al., 2017; Consortium, 2019; Moore et al., 2020; Consortium et al., 2020). These datasets have powered ML models that have been fairly successful at deciphering functional DNA sequence patterns and predicting the consequences of genetic perturbations in cell types in which the models are trained (Libbrecht and Noble, 2015; Zhou and Troyanskaya, 2015; Kelley et al., 2016; Ching et al., 2018; Eraslan et al., 2019; Jaganathan et al., 2019; Avsec et al., 2021b). However, distribution shifts pose a significant obstacle to generalizing these predictions to new cell types.

A concrete example is the prediction of genome-wide profiles of regulatory protein-DNA binding interactions across cell types and tissues (Srivastava and Mahony, 2020). These regulatory maps are critical for understanding the fundamental mechanisms of dynamic gene regulation across healthy and diseased cell states, and predictive models are an essential complement to experimental approaches for comprehensively profiling these maps.

Regulatory proteins bind regulatory DNA elements in a sequence-specific manner to orchestrate gene expression programs. These proteins often form different complexes with each other in different cell types. These cell-type-specific protein complexes can recognize distinct combinatorial sequence syntax and thereby bind to different genomic locations in different cell types, even if all of these cell types share the same genomic sequence. Hence, ML models that aim to predict protein-DNA binding landscapes across cell types typically integrate DNA sequence and additional context-specific input data modalities, which provide auxiliary information about the regulatory state of DNA in each cell type (Srivastava and Mahony, 2020). The training cell-type specific sequence determinants of binding induce a distribution shift across cell types, which can in turn degrade model performance on new cell types (Balsubramani et al., 2017; Li et al., 2019a; Li and Guan, 2019; Keilwagen et al., 2019; Quang and Xie, 2019).

Dataset: Genome-wide protein-DNA binding profiles across different cell types. We studied the above problem in the context of the ENCODE-DREAM in-vivo Transcription Factor Binding Site Prediction Challenge (Balsubramani et al., 2020), which is an open community challenge introduced to systematically benchmark ML models for predicting genome-wide DNA binding maps of many regulatory proteins across cell types.

For each regulatory protein, regions of the genome are associated with binary labels (bound/unbound). The task is to predict these binary binding labels as a function of underlying DNA sequence and chromatin accessibility signal (an experimental measure of cell type-specific regulatory state) in test cell types that are not represented in the training set.

A systematic evaluation of the top-performing models in this challenge highlighted a significant gap in prediction performance across cell types, relative to cross-validation performance within training cell types (Li et al., 2019a; Li and Guan, 2019; Keilwagen et al., 2019; Quang and Xie, 2019). This performance gap was attributed to distribution shifts across cell types, due to regulatory proteins forming cell-type-specific complexes that can recognize different combinatorial sequence syntax. Hence, the same DNA sequence can be associated with different binding labels for a protein across contexts.

We investigated these distribution shifts in more detail for a restricted subset of the challenge's prediction tasks for two regulatory proteins, using a total of 14 genome-wide binding maps across different cell types. While we generally found a performance gap between in- and out-of-distribution settings, we did not include this dataset in the official benchmark for several reasons. For example, we were unable to learn a model that could generalize across all the cell types simultaneously, even in an in-distribution setting, which suggested that the model family and/or feature set might not be rich enough to fit the variation across different cell types. Another major complication was the significant variation in intrinsic difficulty across different splits, as measured by the performance of models we train in-distribution. Further work will be required to construct a rigorous benchmark for evaluating distribution shifts in the context of predicting regulatory binding maps. We discuss details in Appendix F.2.

### 8.4 Natural language and speech processing

Subpopulation shifts are an issue in automated speech recognition (ASR) systems, which have been shown to have higher error rates for Black speakers than for White speakers (Koenecke et al., 2020) and for speakers of some dialects (Tatman, 2017). These disparities were demonstrated using commercial ASR systems, and therefore do not have any accompanying training datasets that are publicly available. There are many public speech datasets with speaker metadata that could potentially be used to construct a benchmark, e.g., LibriSpeech (Panayotov et al., 2015), the Speech Accent Archive (Weinberger, 2015), VoxCeleb2 (Chung et al., 2018), the Spoken Wikipedia Corpus (Baumann et al., 2019), and Common Voice (Ardila et al., 2020). However, these datasets have their own challenges: some do not have a sufficiently diverse sample of speaker backgrounds and accents, and others focus on read speech (e.g., audiobooks) instead of more natural speech.

In natural language processing (NLP), a current focus is on challenge datasets that are crafted to test particular aspects of models, e.g., HANS (McCoy et al., 2019b), PAWS (Zhang et al., 2019), and CheckList (Ribeiro et al., 2020). These challenge datasets are drawn from test distributions that are often (deliberately) quite different from the data distributions that models are typically trained on. Counterfactually-augmented datasets (Kaushik et al., 2019) are a related type of challenge dataset where the training data is modified to make spurious correlates independent of the target, which can result in more robust models. Others have studied train/test sets that are drawn from different sources, e.g., Wikipedia, Reddit, news articles, travel reviews, and so on (Oren et al., 2019; Miller et al., 2020; Kamath et al., 2020).

Several synthetic datasets have also been designed to test compositional generalization, such as CLEVR (Johnson et al., 2017), SCAN (Lake and Baroni, 2018), and COGS (Kim and Linzen, 2020).

The test sets in these datasets are chosen such that models need to generalize to novel combinations of parts of training examples, e.g., familiar primitives and grammatical roles (Kim and Linzen, 2020). CLEVR is a visual question-answering (VQA) dataset; other examples of VQA datasets that are formulated as challenge datasets are the VQA-CP v1 and v2 datasets (Agrawal et al., 2018), which create subpopulation shifts by intentionally altering the distribution of answers per question type between the train and test splits.

These NLP examples involve English-language models; other languages typically have fewer and smaller datasets available for training and benchmarking models. Multi-lingual models and benchmarks (Conneau et al., 2018; Conneau and Lample, 2019; Hu et al., 2020a; Clark et al., 2020) are another source of subpopulation shifts with corresponding disparities in performance: training sets might contain fewer examples in low-resource languages (Nekoto et al., 2020), but we would still hope for high model performance on these minority groups.

Datasets: Other distribution shifts in Amazon and Yelp reviews. In addition to user shifts on the Amazon Reviews dataset (Ni et al., 2019), we also looked at category and time shifts on the same dataset, as well as user and time shifts on the Yelp Open Dataset<sup>5</sup>. However, for many of those shifts, we only found modest performance drops. We provide additional details on Amazon in Appendix F.4 and on Yelp in Appendix F.5.

### 8.5 Education

ML models can help in educational settings in a variety of ways: e.g., assisting in grading (Piech et al., 2013; Shermis, 2014; Kulkarni et al., 2014; Taghipour and Ng, 2016), estimating student knowledge (Desmarais and Baker, 2012; Wu et al., 2020), identifying students who need help (Ahadi et al., 2015), or automatically generating explanations (Williams et al., 2016; Wu et al., 2019a). However, there are substantial distribution shifts in these settings as well. For example, automatic essay scoring has been found to be affected by rater bias (Amorim et al., 2018) and spurious correlations like essay length (Perelman, 2014), leading to problems with subpopulation shift. Ideally, these systems would also generalize across different contexts, e.g., a model for scoring grammar should work well across multiple different essay prompts. Recent attempts at predicting grades algorithmically (BBC, 2020; Broussard, 2020) have also been found to be biased against certain subpopulations.

Unfortunately, there is a general lack of standardized education datasets, in part due to student privacy concerns and the proprietary nature of large-scale standardized tests. Datasets from massive open online courses are a potential source of large-scale data (Kulkarni et al., 2015). In general, dataset construction for ML in education is an active area—e.g., the NeurIPS 2020 workshop on Machine Learning for Education<sup>6</sup> has a segment devoted to finding "ImageNets for education"—and we hope to be able to include one in the future.

## 8.6 Robotics

Robot learning has emerged as a strong paradigm for automatically acquiring complex and skilled behaviors such as locomotion (Yang et al., 2019; Peng et al., 2020), navigation (Mirowski et al., 2017; Kahn et al., 2020), and manipulation (Gu et al., 2017; et al, 2019). However, the advent of learning-based techniques for robotics has not convincingly addressed, and has perhaps even exasperated, problems stemming from distribution shift. These problems have manifested in many ways, including shifts induced by weather and lighting changes (Wulfmeier et al., 2018), location changes (Gupta et al., 2018), and the simulation-to-real-world gap (Sadeghi and Levine, 2017; Tobin et al., 2017). Dealing with these challenging scenarios is critical to deploying robots in the real world, especially in high-stakes decision-making scenarios.

<sup>5.</sup> <https://www.yelp.com/dataset>

<sup>6.</sup> <https://www.ml4ed.org/>

For example, to safely deploy autonomous driving vehicles, it is critical that these systems work reliably and robustly across the huge variety of conditions that exist in the real world, such as locations, lighting and weather conditions, and sensor intrinsics. This is a challenging requirement, as many of these conditions may be underrepresented, or not represented at all, by the available training data. Indeed, prior work has shown that naively trained models can suffer at segmenting nighttime driving scenes (Dai and Van Gool, 2018), detecting relevant objects in new or challenging locations and settings (Yu et al., 2020; Sun et al., 2020a), and, as discussed earlier, detecting pedestrians with darker skin tones (Wilson et al., 2019).

Creating a benchmark for distribution shifts in robotics applications, such as autonomous driving, represents a promising direction for future work. Here, we briefly summarize our initial findings on distribution shifts in the BDD100K driving dataset (Yu et al., 2020), which is publicly available and widely used, including in some of the works listed above.

Dataset: BDD100K. We investigated the task of multi-label binary classification of the presence of each object category in each image. In general, we found no substantial performance drops across a wide range of different test scenarios, including user shifts, weather and time shifts, and location shifts. We provide additional details in Section F.3.

Our findings contrast with previous findings that other tasks, such as object detection and segmentation, can suffer under the same types of shifts on the same dataset (Yu et al., 2020; Dai and Van Gool, 2018). Currently, Wilds consists of datasets involving classification and regression tasks. However, most tasks of interest in autonomous driving, and robotics in general, are difficult to formulate as classification or regression. For example, autonomous driving applications may require models for object detection or lane and scene segmentation. These tasks are often more challenging than classification tasks, and we speculate that they may suffer more severely from distribution shift.

## 8.7 Feedback loops

Finally, we have restricted our attention to settings where the data distribution is independent of the model. When the data distribution does depend on the model, distribution shifts can arise from feedback loops between the data and the model. Examples include recommendation systems and other consumer products (Bottou et al., 2013; Hashimoto et al., 2018); dialogue agents (Li et al., 2017b); molecular compound optimization (Cuccarese et al., 2020; Reker, 2020); decision systems (Liu et al., 2018; D'Amour et al., 2020b); and adversarial settings like fraud or malware detection (Rigaki and Garcia, 2018). While these adaptive settings are outside the scope of our benchmark, dealing with these types of distribution shifts is an important area of ongoing work.

# 9. Guidelines for method developers

We now discuss some community guidelines for method development using WILDS. More specific submission guidelines for our leaderboard can be found at <https://wilds.stanford.edu>.

## 9.1 General-purpose and specialized training algorithms

WILDS is primarily designed as a benchmark for developing and evaluating algorithms for training models that are robust to distribution shifts. To facilitate systematic comparisons of these algorithms, we encourage algorithm developers to use the standardized datasets (i.e., with no external data) and default model architectures provided in WILDS, as doing so will help to isolate the contributions of the algorithm versus the training dataset or model architecture. Our primary leaderboard will focus on submissions that follow these guidelines.

Moreover, we encourage developers to test their algorithms on all applicable WILDS datasets, so as to assess how well they do across different types of data and distribution shifts. We emphasize that it is still an open question if a single general-purpose training algorithm can produce models that do well on all of the datasets without accounting for the particular structure of the distribution shift in each dataset. As such, it would still be a substantial advance if an algorithm significantly improves performance on one type of shift but not others; we aim for WILDS to facilitate research into both general-purpose algorithms as well as ones that are more specifically tailored to a particular application and type of distribution shift.

## 9.2 Methods beyond training algorithms

Beyond new training algorithms, there are many other promising directions for improving distributional robustness, including new model architectures and pre-training on additional external data beyond what is used in our default models. We encourage developers to test these approaches on WILDS as well, and we will track all such submissions on a separate leaderboard from the training algorithm leaderboard.

## 9.3 Avoiding overfitting to the test distribution

While each WILDS dataset aims to benchmark robustness to a type of distribution shift (e.g., shifts to unseen hospitals), practical limitations mean that for some datasets, we have data from only a limited number of domains (e.g., one OOD test hospital in CAMELYON17-WILDS). As there can be substantial variability in performance across domains, developers should be careful to avoid overfitting to the specific test sets in WILDS, especially on datasets like CAMELYON17-WILDS with limited test domains. We strongly encourage all model developers to use the provided OOD validation sets for development and model selection, and to only use the OOD test sets for their final evaluations.

### 9.4 Reporting both ID and OOD performance

Prior work has shown that for many tasks, ID and OOD performance can be highly correlated across different model architectures and hyperparameters (Taori et al., 2020; Liu et al., 2021b; Miller et al., 2021). It is reasonable to expect that methods for improving ID performance could also give corresponding improvements in OOD performance in Wilds, and we welcome submissions of such methods. To better understand the extent to which any gains in OOD performance can be attributed to improved ID performance versus a model that is more robust to (i.e., less affected by) the distribution shift, we encourage model developers to report both ID and OOD performance numbers. See Miller et al. (2021) for an in-depth discussion of this point.

#### 9.5 Extensions to other problem settings

In this paper, we focused on the domain generalization and subpopulation shift settings. In Appendix C, we discuss how Wilds can be used in other realistic problem settings that allow training algorithms to leverage additional information, such as unlabeled test data in unsupervised domain adaptation (Ben-David et al., 2006). These sources of leverage could be fruitful approaches to improving OOD performance, and we welcome community contributions towards this effort.

# 10. Using the WILDS package

Finally, we discuss our open-source PyTorch-based package that exposes a simple interface to our datasets and automatically handles data downloads, allowing users to get started on a WILDS dataset in just a few lines of code. In addition, the package provides various data loaders and utilities surrounding domain annotations and other metadata, which supports training algorithms that need access to these metadata. The package also provides standardized evaluations for each dataset. More documentation and installation information can be found at <https://wilds.stanford.edu>.

Datasets and data loading. The WILDS package provides a simple, standardized interface for all datasets in the benchmark as well as their data loaders, as summarized in Figure [13.](#page-8-0) This short code snippet covers all of the steps of getting started with a WILDS dataset, including dataset download and initialization, accessing various splits, and initializing the data loader. We also provide multiple data loaders in order to accommodate a wide array of algorithms, which often require specific data loading schemes.

```
>>> from wilds import get_dataset
>>> from wilds.common.data_loaders import get_train_loader
>>> import torchvision.transforms as transforms
# Load the full dataset
>>> dataset = get_dataset(dataset="iwildcam", download=True)
# Get the training set
>>> train_data = dataset.get_subset("train",
                                  transform=transforms.ToTensor())
# Prepare the "standard" data loader
>>> train_loader = get_train_loader("standard", train_data, 
... batch_size=16)
# Train loop
>>> for x, y_true, metadata in train_loader:
... ...
```

Figure 13: Dataset initialization and data loading.

Domain information. To allow algorithms to leverage domain annotations as well as other groupings over the available metadata, the Wilds package provides Grouper objects. Grouper objects (e.g., grouper in Figure [14\)](#page-8-1) extract group annotations from metadata, allowing users to specify the grouping scheme in a flexible fashion.

```
>>> from wilds.common.grouper import CombinatorialGrouper
# Initialize grouper, which extracts domain (location) information
>>> grouper = CombinatorialGrouper(dataset, ["location"])
# Train loop
>>> for x, y_true, metadata in train_loader:
... z = grouper.metadata_to_group(metadata)
... ...
```

Figure 14: Accessing domain and other group information via a Grouper object.

Evaluation. Finally, the WILDS package standardizes and automates the evaluation for each dataset. As summarized in Figure [15,](#page-9-0) invoking the eval method of each dataset yields all metrics reported in the paper and on the leaderboard.

```
>>> from wilds.common.data_loaders import get_eval_loader
# Get the test set
>>> test_data = dataset.get_subset("test",
                                   transform=transforms.ToTensor())
# Prepare the data loader
>>> test_loader = get_eval_loader("standard", test_data,
                                  batch_size=16)
# Get predictions for the full test set
>>> for x, y_true, metadata in test_loader:
\ldots y_pred = model(x)... [ accumulate y_true, y_pred, metadata]
# Evaluate
>>> dataset.eval(all_y_pred, all_y_true, all_metadata)
{"macro_recall": 0.66, ...}
```

Figure 15: Evaluation.

# Reproducibility

An executable version of our paper, hosted on CodaLab, can be found at [https://wilds.stanfo](https://wilds.stanford.edu/codalab) [rd.edu/codalab](https://wilds.stanford.edu/codalab). This contains the exact commands, code, environment, and data used for the experiments reported in our paper, as well as all trained model weights. The WILDS package is open-source and can be found at <https://github.com/p-lambda/wilds>.

# Acknowledgements

Many people generously volunteered their time and expertise to advise us on WILDS. We are grateful for all of the helpful suggestions and constructive feedback from: Aditya Khosla, Andreas Schlueter, Annie Chen, Aleksander Madry, Alexander D'Amour, Allison Koenecke, Alyssa Lees, Ananya Kumar, Andrew Beck, Behzad Haghgoo, Charles Sutton, Christopher Yeh, Cody Coleman, Dan Hendrycks, Dan Jurafsky, Daniel Levy, Daphne Koller, David Tellez, Erik Jones, Evan Liu, Fisher Yu, Georgi Marinov, Hongseok Namkoong, Irene Chen, Jacky Kang, Jacob Schreiber, Jacob Steinhardt, Jared Dunnmon, Jean Feng, Jeffrey Sorensen, Jianmo Ni, John Hewitt, John Miller, Kate Saenko, Kelly Cochran, Kensen Shi, Kyle Loh, Li Jiang, Lucy Vasserman, Ludwig Schmidt, Luke Oakden-Rayner, Marco Tulio Ribeiro, Matthew Lungren, Megha Srivastava, Nelson Liu, Nimit Sohoni, Pranav Rajpurkar, Robin Jia, Rohan Taori, Sarah Bird, Sharad Goel, Sherrie Wang, Shyamal Buch, Stefano Ermon, Steve Yadlowsky, Tatsunori Hashimoto, Tengyu Ma, Vincent Hellendoorn, Yair Carmon, Zachary Lipton, and Zhenghao Chen.

The design of the WILDS benchmark was inspired by the Open Graph Benchmark (Hu et al., 2020b), and we are grateful to the Open Graph Benchmark team for their advice and help in setting up our benchmark.

This project was funded by an Open Philanthropy Project Award and NSF Award Grant No. 1805310. Shiori Sagawa was supported by the Herbert Kunzel Stanford Graduate Fellowship. Henrik Marklund was supported by the Dr. Tech. Marcus Wallenberg Foundation for Education in International Industrial Entrepreneurship, CIFAR, and Google. Sang Michael Xie and Marvin Zhang were supported by NDSEG Graduate Fellowships. Weihua Hu was supported by the Funai Overseas Scholarship and the Masason Foundation Fellowship. Sara Beery was supported by an NSF Graduate Research Fellowship and is a PIMCO Fellow in Data Science. Jure Leskovec is a Chan Zuckerberg Biohub investigator. Chelsea Finn is a CIFAR Fellow in the Learning in Machines and Brains Program.

We also gratefully acknowledge the support of DARPA under Nos. N660011924033 (MCS); ARO under Nos. W911NF-16-1-0342 (MURI), W911NF-16-1-0171 (DURIP); NSF under Nos. OAC-1835598 (CINES), OAC-1934578 (HDR), CCF-1918940 (Expeditions), IIS-2030477 (RAPID); Stanford Data
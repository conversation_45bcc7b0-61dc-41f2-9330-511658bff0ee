## Smoothness results

After a long string of counterexamples (Theorems 12.3, 12.4, 12.7, Corollary 12.21, Theorem 12.44), we can at last turn to positive results about the smoothness of the transport map  $T$ . It is indeed absolutely remarkable that a good regularity theory can be developed once all the previously discussed obstructions are avoided, by:

- suitable assumptions of convexity of the domains;
- suitable assumptions of regularity of the cost function.

These results constitute a chapter in the theory of Monge–Am<PERSON>èretype equations, more precisely for the "second boundary value problem", which means that the boundary condition is not of Dirichlet type; instead, what plays the role of boundary condition is that the image of the source domain by the transport map should be the target domain.

Typically a convexity-type assumption on the target will be needed for local regularity results, while global regularity (up to the boundary) will request convexity of both domains. Throughout this theory the main problem is to get  $C^2$  estimates on the unknown  $\psi$ ; once these estimates are secured, the equation becomes "uniformly elliptic", and higher regularity follows from the well-developed machinery of uniformly elliptic fully nonlinear second-order partial differential equations combined with the linear theory (<PERSON><PERSON>ude<PERSON> estimates).

It would take much more space than I can afford here to give a fair account of the methods used, so I shall only list some of the main results proven so far, and refer to the bibliographical notes for more information. I shall distinguish three settings which roughly speaking are respectively the quadratic cost function in Euclidean space; regular cost functions; and strictly regular cost functions. The day may come when these results will all be unified in just two categories (regular and strictly regular), but we are not there yet.

In the sequel, I shall denote by  $C^{k,\alpha}(\Omega)$  (resp.  $C^{k,\alpha}(\overline{\Omega})$ ) the space of functions whose derivatives up to order k are locally  $\alpha$ -Hölder (resp. globally  $\alpha$ -Hölder in  $\Omega$ ) for some  $\alpha \in (0,1], \alpha = 1$  meaning Lipschitz continuity. I shall say that a  $C^2$ -smooth open set  $C \subset \mathbb{R}^n$  is uniformly convex if its second fundamental form is uniformly positive on the whole of ∂C. A similar notion of uniform c-convexity can be defined by use of the c-second fundamental form in Definition 12.26. I shall say that a cost function c is uniformly regular if it satisfies  $\mathfrak{S}_c(x, y) \geq \lambda |\xi|^2 |\eta|^2$ for some  $\lambda > 0$ , where  $\mathfrak{S}_c$  is defined by (12.20) and  $\langle \nabla_{xy}^2 c \cdot \xi, \eta \rangle = 0;$ 

I shall abbreviate this inequality into  $\mathfrak{S}_c(x,y) \geq \lambda \, \mathrm{Id}$ . When I say that a density is bounded from above and below, this means bounded by positive constants.

Theorem 12.50 (Caffarelli's regularity theory). Let  $c(x,y)$  =  $|x-y|^2$  in  $\mathbb{R}^n \times \mathbb{R}^n$ , and let  $\Omega, \Lambda$  be connected bounded open subsets of  $\mathbb{R}^n$ . Let f, g be probability densities on  $\Omega$  and  $\Lambda$  respectively, with f and g bounded from above and below. Let  $\psi : \Omega \to \mathbb{R}$  be the unique (up to an addive constant) Kantorovich potential associated with the probability measures  $\mu(dx) = f(x) dx$  and  $\nu(dy) = q(y) dy$ , and the cost c. Then:

(i) If  $\Lambda$  is convex, then  $\psi \in C^{1,\beta}(\Omega)$  for some  $\beta \in (0,1)$ .

(ii) If  $\Lambda$  is convex,  $f \in C^{0,\alpha}(\Omega)$ ,  $g \in C^{0,\alpha}(\Lambda)$  for some  $\alpha \in (0,1)$ , then  $\psi \in C^{2,\alpha}(\Omega)$ ; moreover, for any  $k \in \mathbb{N}$  and  $\alpha \in (0,1)$ ,

 $f \in C^{k,\alpha}(\Omega)$ ,  $g \in C^{k,\alpha}(\Lambda) \Longrightarrow \psi \in C^{k+2,\alpha}(\Omega)$ .

(iii) If  $\Lambda$  and  $\Omega$  are  $C^2$  and uniformly convex,  $f \in C^{0,\alpha}(\overline{\Omega})$  and  $g \in C^{0,\alpha}(\overline{\Lambda})$  for some  $\alpha \in (0,1)$ , then  $\psi \in C^{2,\alpha}(\overline{\Omega})$ ; more generally, for any  $k \in \mathbb{N}$  and  $\alpha \in (0,1)$ ,

$$
f \in C^{k,\alpha}(\overline{\Omega}), g \in C^{k,\alpha}(\overline{\Lambda}), \Omega, \Lambda \in C^{k+2} \Longrightarrow \psi \in C^{k+2,\alpha}(\overline{\Omega}).
$$

Theorem 12.51 (Urbas–Trudinger–Wang regularity theory). Let X and Y be the closures of bounded open sets in  $\mathbb{R}^n$ , and let c:  $\mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  be a smooth cost function satisfying (STwist) and  $\mathfrak{S}_c \geq 0$ in the interior of  $\mathcal{X} \times \mathcal{Y}$ . Let  $\Omega \subset \mathcal{X}$  and  $\Lambda \subset \mathcal{Y}$  be  $C^2$ -smooth connected open sets and let  $f \in C(\overline{\Omega})$ ,  $q \in C(\overline{\Lambda})$  be positive probability densities. Let  $\psi$  be the unique (up to an additive constant) Kantorovich potential associated with the probability measures  $\mu(dx) = f(x) dx$  and  $\nu(dy) =$  $g(y) dy$ , and the cost c. If (a) A is uniformly c-convex with respect to  $\Omega$ , and  $\Omega$  uniformly *č*-convex with respect to  $\Lambda$ , (b)  $f \in C^{1,1}(\overline{\Omega})$ ,  $g \in C^{1,1}(\overline{\Lambda})$ , and (c)  $\Lambda$  and  $\Omega$  are of class  $C^{3,1}$ , then  $\psi \in C^{3,\beta}(\overline{\Omega})$  for all  $\beta \in (0,1)$ .

If moreover for some  $k \in \mathbb{N}$  and  $\alpha \in (0,1)$  we have  $f \in C^{k,\alpha}(\overline{\Omega}),$  $g \in C^{k,\alpha}(\overline{\Omega})$  and  $\Omega$ ,  $\Lambda$  are of class  $C^{k+2,\alpha}$ , then  $\psi \in C^{k+2,\alpha}(\overline{\Omega})$ .

Theorem 12.52 (Loeper–Ma–Trudinger–Wang regularity theory). Let  $X$  and  $Y$  be the closures of bounded connected open sets in  $\mathbb{R}^n$ , and let  $c : \mathcal{X} \times \mathcal{Y} \to \mathbb{R}$  be a smooth cost function satisfying (STwist) and  $\mathfrak{S}_c \geq \lambda \mathrm{Id}, \lambda > 0$ , in the interior of  $\mathcal{X} \times \mathcal{Y}$ . Let  $\Omega \subset \mathcal{X}$  and  $\Lambda \subset \mathcal{Y}$  be two connected open sets, let  $\mu \in P(\Omega)$  such that  $d\mu/dx > 0$  almost everywhere in  $\Omega$ , and let q be a probability density on  $\Lambda$ , bounded from above and below. Let  $\psi$  be the unique (up to an additive constant) Kantorovich potential associated with the probability measures  $\mu(dx)$ ,  $\nu(dy) = g(y) dy$ , and the cost c. Then:

(i) If  $\Lambda$  is c-convex with respect to  $\Omega$  and if

$$
\exists m > n-1, \ \exists C > 0, \ \forall x \in \Omega, \ \forall r > 0, \ \mu[B_r(x)] \le C r^m,
$$

then  $\psi \in C^{1,\beta}(\Omega)$  for some  $\beta \in (0,1)$ .

(ii) If  $\Lambda$  is uniformly c-convex with respect to  $\Omega$ ,  $f \in C^{1,1}(\Omega)$  and  $g \in C^{1,1}(\Lambda)$ , then  $\psi \in C^{3,\beta}(\Omega)$  for all  $\beta \in (0,1)$ . If moreover for some  $k \in \mathbb{N}$  and  $\alpha \in (0,1)$  we have  $f \in C^{k,\alpha}(\Omega)$ ,  $g \in C^{k,\alpha}(\Lambda)$ , then  $\psi \in C^{k+2,\alpha}(\Omega).$ 

Remark 12.53. Theorem 12.51 shows that the regularity of the cost function is sufficient to build a strong regularity theory. These results are still not optimal and likely to be refined in the near future; in particular one can ask whether  $C^{\alpha} \to C^{2,\alpha}$  estimates are available for plainly regular cost functions (but Caffarelli's methods strongly use the affine invariance properties of the quadratic cost function); or whether interior estimates exist (Theorem 12.52(ii) shows that this is the case for uniformly regular costs).

Remark 12.54. On the other hand, the first part of Theorem 12.52 shows that a uniformly regular cost function behaves better, in certain ways, than the square Euclidean norm! For instance, the condition in Theorem 12.52(i) is automatically satisfied if  $\mu(dx) = f(x) dx$ ,  $f \in L^p$ for  $p > n$ ; but it also allows  $\mu$  to be a singular measure. (Such estimates are not even true for the linear Laplace equation!) As observed by specialists, uniform regularity makes the equation much more elliptic.

Remark 12.55. Theorems 12.52 and 12.51 imply a certain converse to Theorem 12.20: Roughly speaking, if the cost function is regular then any c-convex function  $\psi$  defined in a uniformly bounded convex domain can be approximated uniformly by smooth c-convex functions. In other words, the density of smooth c-convex functions is more or less a necessary and sufficient condition for the regularity property.

All these results are stated only for bounded subsets of  $\mathbb{R}^n \times \mathbb{R}^n$ , so the question arises whether they can be extended to more general

cost functions on Riemannian manifolds. One possibility is to redo the proofs of all these results in curved geometry (with probably additional complications and assumptions). Another possibility is to use a localization argument to reduce the general case to the particular case where the functions are defined in  $\mathbb{R}^n$ . At the level of the optimal transport problem, such an argument seems to be doomed: If you cut out a small piece  $\Omega \subset \mathcal{X}$  and a small piece  $\Lambda \subset \mathcal{Y}$ , there is in general no hope of being able to choose  $\Omega$  and  $\Lambda$  in such a way that the optimal transport sends  $\Omega$  to  $\Lambda$  and these domains satisfy adequate c-convexity properties. However, whenever interior a priori estimates are available besides the regularity results, this localization strategy is likely to work at the level of the partial differential equation. At least Theorems 12.50 and 12.52 can be complemented with such interior a priori estimates:

Theorem 12.56 (Caffarelli's interior a priori estimates). Let  $\Omega \subset \mathbb{R}^n$  be open and let  $\psi : \Omega \to \mathbb{R}$  be a smooth convex function satisfying the Monge–Ampère equation

$$
\det(\nabla^2 \psi(x)) = F(x, \nabla \psi(x)) \qquad \text{in } \Omega. \tag{12.34}
$$

Let  $\kappa_{\Omega}(\psi)$  stand for the modulus of (strict) convexity of  $\psi$  in  $\Omega$ . Then for any open subdomain  $\Omega'$  such that  $\Omega' \subset \Omega$ , one has the a priori estimates (for some  $\beta \in (0,1)$ , for all  $\alpha \in (0,1)$ , for all  $k \in \mathbb{N}$ )

$$
\|\psi\|_{C^{1,\beta}(\Omega')}\leq C\Big(\Omega,\Omega',\|F\|_{L^{\infty}(\Omega)},\|\nabla\psi\|_{L^{\infty}(\Omega)},\kappa_{\Omega}(\psi)\Big);
$$
  
$$
\|\psi\|_{C^{2,\alpha}(\Omega')}\leq C\Big(\alpha,\Omega,\Omega',\|F\|_{C^{0,\alpha}(\Omega)},\|\nabla\psi\|_{L^{\infty}(\Omega)},\kappa_{\Omega}(\psi)\Big);
$$
  
$$
\|\psi\|_{C^{k+2,\alpha}(\Omega')}\leq C\Big(k,\alpha,\Omega,\Omega',\|F\|_{C^{k,\alpha}(\Omega)},\|\nabla\psi\|_{L^{\infty}(\Omega)},\kappa_{\Omega}(\psi)\Big).
$$

With Theorem 12.56 and some more work to establish the strict convexity, it is possible to extend Caffarelli's theory to unbounded domains.

Theorem 12.57 (Loeper–Ma–Trudinger–Wang interior a priori estimates). Let  $X$ ,  $Y$  be the closures of bounded open sets in  $\mathbb{R}^n$ , and let  $c : \mathcal{X} \times \mathcal{Y} \rightarrow \mathbb{R}$  be a smooth cost function satisfying **(STwist)** and uniformly regular. Let  $\Omega \subset \mathcal{X}$  be a bounded open set and let  $\psi : \Omega \to \mathbb{R}$  be a smooth c-convex solution of the Monge–Ampère-type equation

$$
\det\left(\nabla^2\psi(x) + \nabla_x^2 c\big(x, (\nabla_x c)^{-1}(x, -\nabla\psi(x))\big)\right) = F(x, \nabla\psi(x)) \quad \text{in } \Omega. \tag{12.35}
$$

Let  $\Lambda \subset \mathcal{Y}$  be a strict neighborhood of  $\{(\nabla_x c)^{-1}(x, -\nabla \psi(x)); x \in \Omega\},\$ c-convex with respect to  $\Omega$ . Then for any open subset  $\Omega'$  such that  $\overline{\Omega'} \subset \Omega$ , one has the a priori estimates (for some  $\beta \in (0,1)$ , for all  $\alpha \in (0,1)$ , for all  $k \geq 2$ )

$$
\|"\psi"\|_{C^{1,\beta}(\Omega')} \leq C\Big(\Omega,\Omega',c|_{\Omega\times\Lambda},\|F\|_{L^{\infty}(\Omega)},\|\nabla\psi\|_{L^{\infty}(\Omega)}\Big);
$$
  
$$
\|"\psi"\|_{C^{3,\alpha}(\Omega')} \leq C\Big(\alpha,\Omega,\Omega',c|_{\Omega\times\Lambda},\|F\|_{C^{1,1}(\Omega)},\|\nabla\psi\|_{L^{\infty}(\Omega)}\Big);
$$
  
$$
\|"\psi"\|_{C^{k+2,\alpha}(\Omega')} \leq C\Big(k,\alpha,\Omega,\Omega',c|_{\Omega\times\Lambda},\|F\|_{C^{k,\alpha}(\Omega)},\|\nabla\psi\|_{L^{\infty}(\Omega)}\Big).
$$

These a priori estimates can then be extended to more general cost functions. A possible strategy is the following:

(1) Identify a totally c-convex domain  $D \subset \mathcal{X} \times \mathcal{Y}$  in which the cost is smooth and uniformly regular. (For instance in the case of  $d^2/2$  on  $S^{n-1}$  this could be  $S^{n-1} \times S^{n-1} \setminus \{d(y, -x) < \delta\}$ , i.e. one would remove a strip around the cut locus.)

(2) Prove the continuity of the optimal transport map.

(3) Working on the mass transport condition, prove that  $\partial_c \psi$  is entirely included in D. (Still in the case of the sphere, prove that the transport map stays a positive distance away from the cut locus.)

(4) For each small enough domain  $\Omega \subset \mathcal{X}$ , find a c-convex subset  $\Lambda$  of  $\mathcal Y$  such that the transport map sends  $\Omega$  into  $\Lambda$ ; deduce from Theorem 12.49 that  $\partial_c \psi(\Omega) \subset \Lambda$  and deduce an upper bound on  $\nabla \psi$ .

(5) Use coordinates to reduce to the case when  $\Omega$  and  $\Lambda$  are subsets of  $\mathbb{R}^n$ . (Reduce  $\Omega$  and  $\Lambda$  if necessary.) Since the tensor  $\mathfrak{S}_c$  is intrinsically defined, the uniform regularity property will be preserved by this operation.

(6) Regularize  $\psi$  on  $\partial\Omega$ , solve the associated Monge–Ampère-type equation with regularized boundary data (there is a set of useful techniques for that), and use Theorem 12.57 to obtain interior a priori estimates which are independent of the regularization. Then pass to the limit and get a smooth solution. This last step is well-understood by specialists but requires some skill.

To conclude this chapter, I shall state a regularity result for optimal transport on the sphere, which was obtained by means of the preceding strategy.

Theorem 12.58 (Smoothness of optimal transport on  $S^{n-1}$ ). Let  $S^{n-1}$  be the unit Euclidean sphere in  $\mathbb{R}^n$ , equipped with its volume measure, and let d be the geodesic distance on  $S^{n-1}$ . Let f and g be  $C^{1,1}$  positive probability densities on  $S^{n-1}$ . Let  $\psi$  be the unique (up to an additive constant) Kantorovich potential associated with the transport of  $\mu(dx) = f(x) \text{ vol}(dx)$  to  $\nu(dy) = g(y) \text{ vol}(dy)$  with cost  $c(x, y) = d(x, y)^2$ , and let T be the optimal transport map. Then  $\psi \in$  $C^{3,\beta}(S^{n-1})$  for all  $\beta \in (0,1)$ , and in particular  $T \in C^{2,\beta}(S^{n-1},S^{n-1})$ . If moreover  $f, g$  lie in  $C^{k,\alpha}(S^{n-1})$  for some  $k \in \mathbb{N}, \alpha \in (0,1)$ , then  $\psi \in C^{k+2,\alpha}(S^{n-1})$  and  $T \in C^{k+1,\alpha}(S^{n-1},S^{n-1})$ . (In particular, if f and g are positive and  $C^{\infty}$  then  $\psi$  and T are  $C^{\infty}$ .)

**Exercise 12.59.** Split  $S^{n-1}$  into two (geodesically convex) hemispheres  $S_+$  and  $S_-$ , according to, say, the sign of the first coordinate. Let  $f_{\pm}$ stand for the uniform probability density on  $S_{\pm}$ . Find out the optimal transport between  $f_+$  vol and  $f_-\text{vol}$  (for the cost  $c(x, y) = d(x, y)^2$ ) and explain why it was not a priori expected to be smooth.

# Appendix: Strong twist condition for squared distance, and rectifiability of cut locus

In this Appendix I shall explain why the squared geodesic distance satisfies the strong twist condition. The argument relies on a few key results about the cut locus (introduced just after Problem 8.8) which I shall recall without proof. Details can be found in mildly advanced textbooks in Riemannian geometry such as the ones quoted in the bibliographical notes of Chapter 14.

Let  $(M, g)$  be a complete smooth Riemannian manifold equipped with its geodesic distance d. Recall that  $d(x, \cdot)^2$  is not differentiable at  $y$  if and only if there are at least two distinct minimal geodesics going from  $x$  to  $y$ . It can be shown that the closure of such points y is exactly the cut locus cut(x) of x. In particular, if  $c = d^2$ , then Dom  $'(\nabla_x c(x, \cdot)) = M \setminus \text{cut}(x)$ . Also  $\text{cut}(M) := \bigcup_{x \in M} (\{x\} \times \text{cut}(x))$ is closed, so  $\text{Dom}'(\nabla_x c) = (M \times M) \setminus \text{cut}(M)$ .

It is easily checked that c is  $C^{\infty}$  outside cut(M).

Let now x and y be such that  $y \notin \text{cut}(x)$ ; in particular (as we know from Problem 8.8), y is not focal to x, which means that  $d \exp_x$  is

well-defined and nonsingular at  $v_{x,y} = (\exp_x)^{-1}(y)$ , which is the initial velocity of the unique geodesic going from x to y. But  $\nabla_x c(x, y)$  coincides with  $-v_{x\to y}$ ; so  $\nabla_{xy}^2 c(x, y) = -\nabla_y((\exp_x)^{-1}) = -(\nabla_v \exp_x)^{-1}$  is nonsingular. This concludes the proof of the strong twist condition.

It is also true that c satisfies  $(Cut^{n-1})$ ; in fact, for any compact subset K of M and any  $x \in M$  one has

$$
\mathcal{H}^{n-1}[K \cap \text{cut}(x)] < +\infty. \tag{12.36}
$$

This however is a much more delicate result, which will be found in recent research papers.

# Bibliographical notes

The denomination Monge–Ampère equation is used for any equation which resembles  $\det(\nabla^2 \psi) = f$ , or more generally

$$
\det(\nabla^2 \psi - A(x, \psi, \nabla \psi)) = F(x, \psi, \nabla \psi). \tag{12.37}
$$

Monge himself was probably not aware of the relation between the Monge problem and Monge–Ampère equations; this link was made much later, maybe in the work of Knott and Smith [524]. In any case it is Brenier [156] who made this connection popular among the community of partial differential equations. Accordingly, weak solutions of Monge–Ampère-type equations constructed by means of optimal transport are often called Brenier solutions in the literature. McCann [614] proved that such a solution automatically satisfies the Monge–Ampère equation almost everywhere (see the bibliographical notes for Chapter 11). Caffarelli [185] showed that for a convex target, Brenier's notion of solution is equivalent to the older concepts of Alexandrov solution and viscosity solution. These notions are reviewed in [814, Chapter 4] and a proof of the equivalence between Brenier and Alexandrov solutions is recast there. (The concept of Alexandrov solution is developed in  $[53, Section 11.2]$ .) Feyel and Ustünel  $[359, 361, 362]$  studied the infinite-dimensional Monge–Ampère equation induced by optimal transport with quadratic cost on the Wiener space.

The modern regularity theory of the Monge–Ampère equation was pioneered by Alexandrov [16, 17] and Pogorelov [684, 685]. Since then it has become one of the most prestigious subjects of fully nonlinear partial differential equations, in relation to geometric problems such as the construction of isometric embeddings, or convex hypersurfaces with prescribed (multi-dimensional) Gauss curvature (for instance the Minkowski problem is about the construction of a convex hypersurface whose Gauss curvature is a prescribed function of the normal; the Alexandrov problem is about prescribing the so-called integral curvature). These issues are described in Bakelman's monograph [53]. Recently Oliker [659] has shown that the Alexandrov problem can be recast as an optimal transport problem on the sphere.

A modern account of some parts of the theory of the Monge–Ampère equation can be found in the recent book by Gutiérrez  $[452]$ ; there is also an unpolished set of notes by Guan [446].

General references for fully nonlinear elliptic partial differential equations are the book by Caffarelli and Cabré [189], the last part of the one by Gilbarg and Trudinger [416], and the user-friendly notes by Krylov [531] or by Trudinger [786]. As a major development, the estimates derived by Evans, Krylov and Safonov [326, 530, 532] allow one to establish the regularity of fully nonlinear second-order equations under an assumption of uniform ellipticity. Eventually these techniques rely on Schauder-type estimates for certain linear equations. Monge– Ampère equations are uniformly elliptic only under a priori estimates on the second derivative of the unknown function; so these a priori estimates must first be established before applying the general theory. An elementary treatment of the basic linear Schauder estimates can be found, e.g., in [835] (together with many references), and a short treatment of the Monge–Ampère equation (based on [833] and on works by other authors) in [490].

Monge–Ampère equations arising in optimal transport have certain distinctive features; one of them is a particular type of boundary condition, sometimes referred to as the second boundary condition. The pioneering papers in this field are due to Delanoë in dimension  $2 \; [284]$ , then Caffarelli [185, 186, 187], Urbas [798, 799], and X.-J. Wang [833] in arbitrary dimension. The first three authors were interested in the case of a quadratic cost function in Euclidean space, while Wang considered the logarithmic cost function on the sphere, which appears in the so-called reflector antenna problem, at least in the particular case of "far field". (At the time of publication of [833] it was not yet understood that the problem treated there really was an optimal transport problem; it was only later that Wang made this remark.)

Wolfson [840] studied the optimal transport problem between two sets of equal area in the plane, with motivations in geometry, and identified obstructions to the existence of smooth solutions in terms of the curvature of the boundary.

Theorem 12.49 is one of the first steps in Caffarelli's regularity theory [185] for the quadratic cost function in Euclidean space; for more general cost functions it is due to Ma, Trudinger and Wang [585].

Theorem 12.50 (together with further refinements) appears in [185, 186, 187]. An informal introduction to the first steps of Caffarelli's techniques can be found in [814, Chapter 4]. The extension to unbounded domains is sketched in [15, Appendix]. Most of the theory was developed with nonconstructive arguments, but Forzani and Maldonado [376] were able to make at least the  $C^{1,\beta}$  estimates quantitative. Apart from the  $C^{\alpha} \to C^{2,\alpha}$  theory, there is also a difficult  $C^0 \to W^{2,p}$ result [184], where  $p$  is arbitrarily large and  $f, g$  should be positive continuous. (The necessity of both the continuity and the positivity assumptions are demonstrated by counterexamples due to Wang [832]; see also [490].) Caffarelli and McCann [192] studied the regularity of the optimal map in the problem of partial optimal transport, in which only a fraction of the mass is transferred from the source to the target (both measures need not have the same mass); this problem transforms into a (double) obstacle problem for the Monge–Ampère equation. Figalli [365] obtained refined results on this problem by more elementary methods (not relying on the Monge–Ampère equation) and showed that there is in general no regularity if the supports of the source and target measures overlap. Another obstacle problem involving optimal transport, arising from a physical model, has been studied in [737].

Cordero-Erausquin [240] adapted Caffarelli's theory to the case of the torus, and Delanoë [285] and studied the stability of this theory under small perturbations. Roughly speaking he showed the following: Given two smooth probability densities f and g on (say)  $\mathbb{T}^n$  and a smooth optimal transport T between  $\mu = f$  vol and  $\nu = g$  vol, it is possible to slightly perturb  $f, g$  and the Riemannian metric, in such a way that the resulting optimal transport is still smooth. (Note carefully: How much you are allowed to perturb the metric depends on  $f$  and  $q$ .)

Urbas [798] considered directly the boundary regularity for uniformly convex domains in  $\mathbb{R}^n$ , by first establishing a so-called oblique derivative boundary condition, which is a sort of nonlinear version of the Neumann condition. (Actually, the uniform convexity of the do-

main makes the oblique condition more elliptic in some sense than the Neumann condition.) Fully nonlinear elliptic equations with oblique boundary condition had been studied before in [555, 560, 561], and the connection with the second boundary value problem for the Monge– Ampère equation had been suggested in [562]. Compared to Caffarelli's method, this one only covers the global estimates, and requires higher initial regularity; but it is more elementary.

The generalization of these regularity estimates to nonquadratic cost functions stood as an open problem for some time. Then Ma, Trudinger and Wang [585] discovered that the older interior estimates by Wang [833] could be adapted to general cost functions satisfying the condition  $\mathfrak{S}_c > 0$  (this condition was called (A3) in their paper and in subsequent works). Theorem 12.52(ii) is extracted from this reference. A subtle caveat in [585] was corrected in [793] (see Theorem 1 there). A key property discovered in this study is that if  $c$  is a regular cost function and  $\psi$  is c-convex, then any local c-support function for  $\psi$  is also a global c-support function (which is nontrivial unless  $\psi$  is differentiable); an alternative proof can be derived from the method of Y.-H. Kim and McCann [519, 520].

Trudinger and Wang [794] later adapted the method of Urbas to treat the boundary regularity under the weaker condition  $\mathfrak{S}_c \geq 0$  (there called  $(A3w)$ . The proof of Theorem 12.51 can be found there.

At this point Loeper [570] made three crucial contributions to the theory. First he derived the very strong estimates in Theorem 12.52(i) which showed that the Ma–Trudinger–Wang  $(A3)$  condition (called (As) in Loeper's paper) leads to a theory which is stronger than the Euclidean one in some sense (this was already somehow implicit in [585]). Secondly, he found a geometric interpretation of this condition, namely the regularity property (Definition 12.14), and related it to well-known geometric concepts such as sectional curvature (Particular Case 12.30). Thirdly, he proved that the weak condition  $(A3w)$  (called (Aw) in his work) is mandatory to derive regularity (Theorem 12.21). The psychological impact of this work was important: before that, the Ma–Trudinger–Wang condition could be seen as an obscure ad hoc assumption, while now it became the natural condition.

The proof of Theorem 12.52(i) in [570] was based on approximation and used auxiliary results from [585] and [793] (which also used some of the arguments in [570]. . .but there is no loophole!)

Loeper [571] further proved that the squared distance on the sphere is a uniformly regular cost, and combined all the above elements to derive Theorem 12.58; the proof is simplified in [572]. In [571], Loeper derived smoothness estimates similar to those in Theorem 12.52 for the far-field reflector antenna problem.

The exponent  $\beta$  in Theorem 12.52(i) is explicit; for instance, in the case when  $f = d\mu/dx$  is bounded above and g is bounded below, Loeper obtained  $\beta = (4n - 1)^{-1}$ , *n* being the dimension. (See [572] for a simplified proof.) However, this is not optimal: Liu [566] improved this into  $\beta = (2n - 1)^{-1}$ , which is sharp.

In a different direction, Caffarelli, Gutiérrez and Huang [191] could get partial regularity for the far-field reflector antenna problem by very elaborate variants of Caffarelli's older techniques. This "direct" approach does not yet yield results as powerful as the a priori estimates by Loeper, Ma, Trudinger and Wang, since only  $C<sup>1</sup>$  regularity is obtained in [191], and only when the densities are bounded from above and below; but it gives new insights into the subject.

In dimension 2, the whole theory of Monge–Ampère equations becomes much simpler, and has been the object of numerous studies [744]. Old results by Alexandrov [17] and Heinz [471] imply  $C<sup>1</sup>$  regularity of the solution of  $\det(\nabla^2 \psi) = h$  as soon as h is bounded from above (and strict convexity if it is bounded from below). Loeper noticed that this implied strenghtened results for the solution of optimal transport with quadratic cost in dimension 2, and together with Figalli [368] extended this result to regular cost functions.

Now I shall briefly discuss counterexamples stories. Counterexamples by Pogorelov and Caffarelli (see for instance [814, pp. 128–129]) show that solutions of the usual Monge–Ampère equation are not smooth in general: some strict convexity on the solution is needed, and it has to come from boundary conditions in one way or the other.

The counterexample in Theorem 12.3 is taken from Caffarelli [185], where it is used to prove that the "Hessian measure" (a generalized formulation of the Hessian determinant) cannot be absolutely continuous if the bridge is thin enough; in the present notes I used a slightly different reasoning to directly prove the discontinuity of the optimal transport. The same can be said of Theorem 12.4, which is adapted from Loeper [570]. (In Loeper's paper the contradiction was obtained indirectly as in Theorem 12.44.)

Ma, Trudinger and Wang [585, Section 7.3] generalized Caffarelli's counterexample, showing that for any nonconvex target domain, there are smooth positive densities leading to nonsmooth optimal transport. Their result holds for more general cost functions, up to the replacement of convexity by c-convexity. The method of proof used in  $[585]$  is adapted from an older paper by Wang [833] on the reflector antenna problem. A similar strategy was rediscovered by Loeper [570] to construct counterexamples in the style of Theorem 12.44.

Theorem 12.7 was proven for these notes, with the aim of getting an elementary topological (as opposed to differential) version of Loeper's general nonsmoothness result.

An old counterexample by Lewy [744, Section 9.5] shows that the general equation (12.37) needs certain convexity-type assumptions; see [750, Section 3.3] for a rewriting of this construction. In view of Lewy's counterexample, specialists of regularity theory did expect that smoothness would need some assumptions on the cost function, although there was no hint of the geometric insights found by Loeper.

For nonregular cost functions, a natural question consists in describing the possible singularities of solutions of optimal transport problems. Do these singularities occur along reasonably nice curves, or with complicated, fractal-like geometries? No such result seems to be known.

Proposition 12.15, Theorems 12.20 and 12.44, Remarks 12.31 and 12.33, and the direct implication in Theorem 12.36 are all taken from Loeper [570] (up to issues about the domain of definition of  $\nabla_x c$ ). The fact that  $\mathfrak{S}_c$  is defined independently of the coordinate system was also noticed independently by Kim and McCann [520]. Theorem 12.35 is due to Trudinger and Wang [793], as well as Examples 12.40 and 12.41, except for the case of  $S^{n-1}$  which is due to Loeper [571]. The converse implication in Theorem 12.36 was proven independently by Trudinger and Wang [793] on the one hand, by Y.-H. Kim and McCann [519] on the other. (The result by Trudinger and Wang is slightly more general but the proof is more sophisticated since it involves delicate smoothness theorems, while the argument by Kim and McCann is much more direct.) The proof which I gave in these notes is a simplified version of the Kim–McCann argument, which evolved from discussions with Trudinger.

To go further, a refinement of the Ma–Trudinger–Wang conditions seemed to be necessary. The condition

$$
\mathfrak{S}_c(x,y) \cdot (\xi, \eta) \ge K_0 |\xi|^2 |\widetilde{\eta}|^2 + C_0 (\nabla_{x,y}^2 c) \cdot (\xi, \eta)
$$

is called MTW( $K_0$ ,  $C_0$ ) in [572]; here  $\widetilde{\eta} = -(\nabla^2_{x,y}c) \cdot \eta$ . In the particular case  $C_0 = +\infty$ , one finds again the Ma–Trudinger–Wang condition (strong if  $K_0 > 0$ , weak if  $K_0 = 0$ ). If c is the squared geodesic Riemannian distance, necessarily  $C_0 \geq K_0$ . According to [371], the squared distance on the sphere satisfies MTW( $K_0, K_0$ ) for some  $K_0 \in (0, 1)$ (this improves on [520, 571, 824]); numerical evidence even suggests that this cost satisfies the doubly optimal condition  $MTW(1, 1)$ .

The proof of Theorem 12.36 was generalized in [572] to the case when the cost is the squared distance and satisfies an  $MTW(K_0, C_0)$ condition. Moreover, one can afford to "slightly bend" the c-segments, and the inequality expressing regularity is also complemented with a remainder term proportional to  $K_0 t(1-t)$  (inf  $|\eta|^2$ ) | $\zeta$ <sup>2</sup> (in the notation of the proof of Theorem 12.36).

Figalli, Kim and McCann [367] have been working on the adaptation of Caffarelli's techniques to cost functions satisfying  $MTW(0, 0)$  (which is a reinforcement of the weak regularity property, satisfied by many examples which are not strictly regular).

Remark 12.45 is due to Kim [518], who constructed a smooth perturbation of a very thin cone, for which the squared distance is not a regular cost function, even though the sectional curvature is positive everywhere. (It is not known whether positive sectional curvature combined with some pinching assumption would be sufficient to guarantee the regularity.)

The transversal approximation property (TA) in Lemma 12.39 derives from a recent work of Loeper and myself [572], where it is proven for the squared distance on Riemannian manifolds with "nonfocal cut locus", i.e. no minimizing geodesic is focalizing. The denomination "transversal approximation" is because the approximating path  $(\hat{y}_t)$ is constructed in such a way that it goes transversally through the cut locus. Before that, Kim and McCann [519] had introduced a different density condition, namely that  $\bigcap_t \text{Dom}'(\nabla_y c(\cdot, y_t))$  be dense in M. The latter assumption clearly holds on  $S<sup>n</sup>$ , but is not true for general manifolds. On the contrary, Lemma 12.39 shows that (TA) holds with great generality; the principle of the proof of this lemma, based on the co-area formula, was suggested to me by Figalli. The particular co-area formula which I used can be found in [331, p. 109]; it is established in [352, Sections 2.10.25 and 2.10.26].

Property  $(\text{Cut}^{n-1})$  for the squared geodesic distance was proven in independent papers by Itoh and Tanaka [489] and Li and Nirenberg [551]; in particular, inequality (12.36) is a particular case of Corollary 1.3 in the latter reference. These results are also established there for quite more general classes of cost functions.

In relation to the conjecture evoked in Remark 12.43, Loeper and I [572] proved that when the cost function is the squared distance on a Riemannian manifold with nonfocal cut locus, the strict form of the Ma–Trudinger–Wang condition automatically implies the uniform  $c$ convexity of all  $Dom'(\nabla_x c(x, \cdot))$ , and a strict version of the regularity property.

It is not so easy to prove that the solution of a Monge–Ampère equation such as (12.1) is the solution of the Monge–Kantorovich problem. There is a standard method based on the strict maximum principle [416] to prove uniqueness of solutions of fully nonlinear elliptic equations, but it requires smoothness (up to the boundary), and cannot be used directly to assess the identity of the smooth solution with the Kantorovich potential, which solves (12.1) only in weak sense. To establish the desired property, the strategy is to prove the c-convexity of the smooth solution; then the result follows from the uniqueness of the Kantorovich potential. This was the initial motivation behind Theorem 12.46, which was first proven by Trudinger and Wang [794, Section 6] under slightly different assumptions (see also the remark in Section 7 of the same work). All in all, Trudinger and Wang suggested three different methods to establish the c-convexity; one of them is a global comparison argument between the strong and the weak solution, in the style of Alexandrov. Trudinger suggested to me that the Kim– McCann strategy from [519] would yield an alternative proof of the c-convexity, and this is the method which I implemented to establish Theorem 12.46. (The proof by Trudinger and Wang is more general in the sense that it does not need the  $\check{c}$ -convexity; however, this gain of generality might not be so important because the c-convexity of  $\psi$  automatically implies the c-convexity of  $\partial_c \psi$  and the č-convexity of  $\partial^c \psi^c$ , up to issues about the domain of differentiability of  $c$ .)

In [585] a local uniqueness statement is needed, but this is tricky since c-convexity is a global notion. So the problem arises whether a locally c-convex function (meaning that for each  $x$  there is  $y$  such that x is a *local* minimizer of  $\psi + c(\cdot, y)$  is automatically c-convex. This local-to-global problem, which is closely related to Theorem 12.46 (and to the possibility of localizing the study of the Monge–Ampère equation (12.35)), is solved affirmatively for uniformly regular cost functions in [793] where a number of variants of c-convexity (local c-convexity, full c-convexity, strict c-convexity, global c-convexity) are carefully discussed. See also [571] for the case of the sphere.

In this chapter I chose to start with geometric (more general) considerations, such as the regularity property, and end up with analytic conditions in terms of the Ma–Trudinger–Wang tensor; but actually the tensor was discovered before the geometric conditions. The role of the Riemannian structure (and geodesic coordinates) in the presentation of this chapter might also seem artificial since it was noticed in Remark 12.31 that the meaningful quantities are actually independent of these choices. As a matter of fact, Kim and McCann [520] develop a framework which avoids any reference to them and identifies the Ma– Trudinger–Wang tensor as the sectional curvature tensor of the mixed second derivative  $\partial^2 c/\partial x \, \partial y$ , considered as a pseudo-Riemannian metric (with signature  $(n,n)$ ) on the product manifold. In the same reference they also point out interesting connections with pseudo-Riemannian, Lagrangian and symplectic geometry (related to [840]).

Now some comments about terminology. The terminology of ccurvature was introduced by Loeper [570] after he made the connection between the Ma–Trudinger–Wang tensor and the sectional curvature. It was Trudinger who suggested the term "regular" to describe a matrixvalued function  $A$  in (12.37) that would satisfy adequate assumptions of the type  $\mathfrak{S}_c \geq 0$ . By extension, I used the same denomination for cost functions satisfying the property of Definition 12.14. Kim and Mc-Cann [519] call this property (DASM) (Double mountain Above Sliding Mountain).

To apply Theorems 12.51 or 12.52 to problems where the cost function is not everywhere differentiable, one first needs to make sure that the c-subdifferential of the unknown function lies within the domain of differentiability. Typically (for the cost function  $d(x, y)^2$  on a Riemannian manifold), this means controlling the distance of  $T(x)$  to the cut locus of x, where  $T$  is the optimal transport. ("Stay away from cut locus!") Until recently, the only manifolds for which this was known to be true independently of the probability densities (say bounded positive) were positively curved manifolds where all geodesics have the same length: the sphere treated in [286] and [571]; and the projective space considered in [287]. In the latter work, it was shown that the "stay-away" property still holds true if the variation of the length of geodesics is small with respect to certain other geometric quanti-

ties, and the probability densities satisfy certain size restrictions. Then Loeper and I [572] established smoothness estimates for the optimal transport on  $C<sup>4</sup>$  perturbations of the projective space, without any size restriction.

The cut locus is also a major issue in the study of the perturbation of these smoothness results. Because the dependence of the geodesic distance on the Riemannian metric is not smooth near the cut locus, it is not clear whether the Ma–Trudinger–Wang condition is stable under  $C^k$  perturbations of the metric, however large k may be. This stability problem, first formulated in [572], is in my opinion extremely interesting; it is solved by Figalli and Rifford  $[371]$  near  $S^2$ .

Without knowing the stability of the Ma–Trudinger–Wang condition, if pointwise a priori bounds on the probability densities are given, one can afford a  $C<sup>4</sup>$  perturbation of the metric and retain the Hölder continuity of optimal transport; or even afford a  $C<sup>2</sup>$  perturbation and retain a mesoscopic version of the Hölder continuity [822].

Some of the smoothness estimates discussed in these notes also hold for other more complicated fully nonlinear equations, such as the reflector antenna problem [507] (which in its general formulation does not seem to be equivalent to an optimal transport problem) or the so-called Hessian equations [789, 790, 792, 800], where the dominant term is a symmetric function of the eigenvalues of the Hessian of the unknown. The short survey by Trudinger [788] presents some results of this type, with applications to conformal geometry, and puts this into perspective together with optimal transport. In this reference Trudinger also notes that the problem of the prescribed Schouten tensor resembles an optimal transport problem with logarithmic cost function; this connection had also been made by McCann (see the remarks in [520]) who had long ago noticed the properties of conformal invariance of this cost function.

A topic which I did not address at all is the regularity of certain sets solving variational problems involving optimal transport; see [632].
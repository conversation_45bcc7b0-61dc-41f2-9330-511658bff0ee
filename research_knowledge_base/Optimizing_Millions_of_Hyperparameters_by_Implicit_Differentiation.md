# Optimizing Millions of Hyperparameters by Implicit Differentiation

Jonathan <PERSON>

University of Toronto, Vector Institute {lorraine, pvicol, duvenaud}@cs.toronto.edu

## Abstract

We propose an algorithm for inexpensive gradient-based hyperparameter optimization that combines the implicit function theorem (IFT) with efficient inverse Hessian approximations. We present results on the relationship between the IFT and differentiating through optimization, motivating our algorithm. We use the proposed approach to train modern network architectures with millions of weights and millions of hyperparameters. We learn a data-augmentation network where every weight is a hyperparameter tuned for validation performance—that outputs augmented training examples; we learn a distilled dataset where each feature in each datapoint is a hyperparameter; and we tune millions of regularization hyperparameters. Jointly tuning weights and hyperparameters with our approach is only a few times more costly in memory and compute than standard training.

## 1 Introduction

The generalization of neural networks (NNs) depends crucially on the choice of hyperparameters. Hyperparameter optimization (HO) has a rich history [\[1,](#page-8-0) [2\]](#page-8-1), and achieved recent success in scaling due to gradient-based optimizers [\[3–](#page-8-2)[11\]](#page-8-3). There are dozens of regularization techniques to combine in deep learning, and each may have multiple hyperparameters [\[12\]](#page-8-4). If we can scale HO to have as many—or more—hyperparameters as parameters, there are various exciting regularization strategies to investigate. For example, we could learn a distilled dataset with a hyperparameter for every feature of each input [\[4,](#page-8-5) [13\]](#page-8-6), weights on each loss term [\[14](#page-8-7)[–16\]](#page-8-8), or augmentation on each input [\[17,](#page-8-9) [18\]](#page-8-10).

When the hyperparameters are low-dimensional e.g., 1-5 dimensions—simple methods, like random search, work; however, these break down for mediumdimensional HO—e.g., 5-100 dimensions. We may use more scalable algorithms like Bayesian Optimization [\[19](#page-8-11)[–21\]](#page-8-12), but this often breaks down for highdimensional HO—e.g., >100 dimensions. We can solve

high-dimensional HO problems locally with gradientbased optimizers, but this is difficult because we must differentiate through the optimized weights as a function of the hyperparameters. In other words, we must approximate the Jacobian of the best-response function of the parameters to the hyperparameters.

We leverage the Implicit Function Theorem (IFT) to compute the optimized validation loss gradient with respect to the hyperparameters—hereafter denoted the hypergradient. The IFT requires inverting the training Hessian with respect to the NN weights, which is infeasible for modern, deep networks. Thus, we propose an approximate inverse, motivated by a link to unrolled differentiation [\[3\]](#page-8-2) that scales to Hessians of large NNs, is more stable than conjugate gradient [\[22,](#page-8-13) [7\]](#page-8-14), and only requires a constant amount of memory.

Finally, when fitting many parameters, the amount of data can limit generalization. There are ad hoc rules for partitioning data into training and validation sets e.g., using 10% for validation. Often, practitioners re-train their models from scratch on the combined training and validation partitions with optimized hyperparameters, which can provide marginal test-time performance increases. We verify empirically that standard partitioning and re-training procedures perform well when fitting few hyperparameters, but break down when fitting many. When fitting many hyperparameters, we need a large validation partition, which makes re-training our model with optimized hyperparameters vital for strong test performance.

#### Contributions

- We propose a stable inverse Hessian approximation with constant memory cost.
- We show that the IFT is the limit of differentiating through optimization.
- We scale IFT-based hyperparameter optimization to modern, large neural architectures, including AlexNet and LSTM-based language models.
- We demonstrate several uses for fitting hyperparameters almost as easily as weights, including perparameter regularization, data distillation, and learned-from-scratch data augmentation methods.
- We explore how training-validation splits should change when tuning many hyperparameters.

Image /page/1/Figure/1 description: The image displays two 3D plots illustrating the relationship between a parameter 'w' and a hyperparameter 'lambda' in the context of machine learning loss functions. The left plot shows the training loss, L\_Train(w, lambda), with a black curve representing L(lambda, w\*(lambda)), a blue curve representing the implicit best-response function w\*(lambda), and a dashed blue line indicating the implicit derivative dw\*/dlambda. A pink curve shows L(lambda\_0, w) and a black star marks the point (lambda\_0, w\*(lambda\_0)). The right plot shows the validation loss, L\_Valid(w), with a red curve representing L\*(lambda) and a dashed red line representing the hypergradient, which is the derivative of L\* with respect to lambda. Both plots use a grid to represent the loss surface.

Figure 1: Overview of gradient-based hyperparameter optimization (HO). Left: a training loss manifold; Right: a validation loss manifold. The implicit function  $w^*(\lambda)$  is the best-response of the weights to the hyperparameters and shown in blue projected onto the  $(\lambda, w)$ -plane. We get our desired objective function  $\mathcal{L}_{V}^{*}(\lambda)$  when the best-response is put into the validation loss, shown projected on the hyperparameter axis in red. The validation loss does not depend directly on the hyperparameters, as is typical in hyperparameter optimization. Instead, the hyperparameters only affect the validation loss by changing the weights' response. We show the best-response Jacobian in blue, and the hypergradient in red.

## 2 Overview of Proposed Algorithm

There are four essential components to understanding our proposed algorithm. Further background is provided in Appendix [A,](#page-11-0) and notation is shown in Table [5.](#page-12-0)

1. HO is nested optimization: Let  $\mathcal{L}_T$  and  $\mathcal{L}_V$ denote the training and validation losses, w the NN weights, and  $\lambda$  the hyperparameters. We aim to find optimal hyperparameters  $\lambda^*$  such that the NN minimizes the validation loss after training:

$$
\lambda^* := \underset{\lambda}{\arg\min} \mathcal{L}_{\mathcal{V}}^*(\lambda) \text{ where } (1)
$$

$$
\mathcal{L}_{V}^{*}(\lambda) := \mathcal{L}_{V}(\lambda, \mathbf{w}^{*}(\lambda)) \text{ and } \mathbf{w}^{*}(\lambda) := \argmin_{\mathbf{w}} \mathcal{L}_{T}(\lambda, \mathbf{w}) \quad (2)
$$

Our implicit function is  $\mathbf{w}^*\!({\boldsymbol{\lambda}})$ , which is the *best-response* of the weights to the hyperparameters. We assume unique solutions to arg min for simplicity.

2. Hypergradients have two terms: For gradientbased HO we want the hypergradient  $\frac{\partial \mathcal{L}_{\mathbf{v}}^*(\mathbf{A})}{\partial \mathbf{\lambda}}$ , which decomposes into:

$$
\frac{\partial \mathcal{L}_{v}^{*}(\lambda)}{\partial \lambda} = \left( \frac{\partial \mathcal{L}_{v}}{\partial \lambda} + \frac{\partial \mathcal{L}_{v}}{\partial w} \frac{\partial w^{*}}{\partial \lambda} \right) \bigg|_{\lambda, w^{*}(\lambda)} =
$$
hypergradient

$$
\frac{\partial \mathcal{L}_{v}(\lambda, w^{*}(\lambda))}{\partial \lambda} + \frac{\partial \mathcal{L}_{v}(\lambda, w^{*}(\lambda))}{\partial w^{*}(\lambda)} \times \frac{\partial w^{*}(\lambda)}{\partial \lambda}
$$
hyperparam direct grad. hyperparam indirect grad. parameter direct grad. best-response Jacobian

 $(3)$ 

The direct gradient is easy to compute, but the indirect gradient is difficult to compute because we must account for how the optimal weights change with respect<br>to the hyperparameters (i.e.,  $\frac{\partial w^*(\lambda)}{\partial \lambda}$ ). In HO the direct  $\frac{\mathbf{w}(\lambda)}{\partial \lambda}$ . In HO the direct gradient is often identically 0, necessitating an approx-

imation of the indirect gradient to make any progress

(visualized in Fig. [1\)](#page-1-0).

<span id="page-1-0"></span>3. We can estimate the implicit best-response with the IFT: We approximate the best-response Jacobian—how the optimal weights change with respect to the hyperparameters—using the IFT (Thm. [1\)](#page-1-1). We present the complete statement in Appendix [C,](#page-13-0) but highlight the key assumptions and results here.

Theorem 1 (Cauchy, Implicit Function Theorem). If for some  $(\mathcal{X}', \mathbf{w}')$ ,  $\frac{\partial \mathcal{L}_T}{\partial \mathbf{w}}|_{\mathbf{\lambda}', \mathbf{w}'} = 0$  and regularity conditions are satisfied, then surrounding  $(\lambda', \mathbf{w}')$  there is a function  $\mathbf{w}^*(\boldsymbol{\lambda})$  s.t.  $\frac{\partial \mathcal{L}_T}{\partial \mathbf{w}}|_{\boldsymbol{\lambda}, \mathbf{w}^*(\boldsymbol{\lambda})} = 0$  and we have:

<span id="page-1-2"></span><span id="page-1-1"></span>
$$
\frac{\partial \mathbf{w}^*}{\partial \lambda} \bigg|_{\lambda'} = -\left[ \underbrace{\frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \mathbf{w}^T}}_{\text{training Hessian training mixed partials}} \bigg|_{\lambda', \mathbf{w}^*(\lambda')} \begin{array}{c} \text{(IFT)} \\ \text{(IFT)} \end{array}
$$

The condition  $\frac{\partial \mathcal{L}_{\Gamma}}{\partial \mathbf{w}}|_{\mathbf{\lambda}'\mathbf{w}'} = 0$  is equivalent to  $\mathbf{\lambda}', \mathbf{w}'$  being a fixed point of the training gradient field. Since  $\mathbf{w}^*(\lambda')$  is a fixed point of the training gradient field, we can leverage the IFT to evaluate the best-response Jacobian locally. We only have access to an approximation of the true best-response—denoted  $\widehat{\mathbf{w}^*}$ —which we can find with gradient descent.

<span id="page-1-3"></span>4. Tractable inverse Hessian approximations: To exactly invert a general  $m \times m$  Hessian, we often require  $\mathcal{O}(m^3)$  operations, which is intractable for the matrix in Eq. [IFT](#page-1-1) in modern NNs. We can efficiently approximate the inverse with the Neumann series:

$$
\left[\frac{\partial^2 \mathcal{L}_\Gamma}{\partial \mathbf{w} \partial \mathbf{w}^T}\right]^{-1} = \lim_{i \to \infty} \sum_{j=0}^i \left[I - \frac{\partial^2 \mathcal{L}_\Gamma}{\partial \mathbf{w} \partial \mathbf{w}^T}\right]^j \tag{4}
$$

In Section [4](#page-3-0) we show that unrolling differentiation for i steps around locally optimal weights  $w^*$  is equivalent to approximating the inverse with the first  $i$  terms in the Neumann series. We then show how to use this approximation without instantiating any matrices by using efficient vector-Jacobian products.

Image /page/2/Figure/1 description: The image displays a series of mathematical equations presented in a stacked format. Each equation involves partial derivatives of L\_V and L\_T with respect to lambda and w. The first equation shows the partial derivative of L\*\_V with respect to lambda equaling the partial derivative of L\_V with respect to lambda plus the partial derivative of L\_V with respect to w multiplied by the partial derivative of w\* with respect to lambda. The second equation shows the partial derivative of L\_V with respect to lambda equaling the partial derivative of L\_V with respect to lambda plus the partial derivative of L\_V with respect to w multiplied by the inverse of the second partial derivative of L\_T with respect to w and w transpose, which is labeled as a 'vector-inverse Hessian product'. The third equation shows the partial derivative of L\_V with respect to lambda equaling the partial derivative of L\_V with respect to lambda plus the partial derivative of L\_V with respect to w multiplied by the inverse of the second partial derivative of L\_T with respect to w and w transpose, and then multiplied by the second partial derivative of L\_T with respect to w and lambda transpose. Each term in the equations is visually represented by colored rectangles or brackets, with red, green, white, blue, pink, and orange colors used to distinguish different components.

vector-Jacobian product

<span id="page-2-3"></span>Figure 2: Hypergradient computation. The entire computation can be performed efficiently using vector-Jacobian products, provided a cheap approximation to the inverse-Hessian-vector product is available.

<span id="page-2-0"></span>

| Algorithm 1 Gradient-based HO for $\lambda^*, w^*(\lambda^*)$                                                                      |
|------------------------------------------------------------------------------------------------------------------------------------|
| 1: Initialize hyperparameters $\lambda'$ and weights $w'$                                                                          |
| 2: while not converged do                                                                                                          |
| 3: for $k = 1...N$ do                                                                                                              |
| 4: $\mathbf{w}' = \alpha \cdot \frac{\partial \mathcal{L}_{\mathrm{T}}}{\partial \mathbf{w}} _{\boldsymbol{\lambda}',\mathbf{w}'}$ |
| 5: $\lambda'$ -= <span style="color:red">hypergradient</span> $(\mathcal{L}_V, \mathcal{L}_T, \lambda', \mathbf{w}')$              |
| 6: return $\lambda', w'$ $\triangleright \lambda^*, \mathbf{w}^*(\lambda^*)$ from Eq.1                                             |

<span id="page-2-1"></span> $\overline{\textbf{Algorithm 2 hypergradient}(\mathcal{L}_\text{V}, \mathcal{L}_\text{T}, \boldsymbol{\lambda}', \textbf{w}')}$ 1:  $\mathbf{v}_1 = \frac{\partial \mathcal{L}_V}{\partial \mathbf{w}}|_{\boldsymbol{\lambda}',\mathbf{w}'}$ 2:  $\mathbf{v}_2 = \texttt{approxInverseHVP}(\mathbf{v}_1, \frac{\partial \mathcal{L}_\text{T}}{\partial \mathbf{w}})$ 3:  $\mathbf{v}_3 = \text{grad}(\frac{\partial \mathcal{L}_T}{\partial \boldsymbol{\lambda}}, \mathbf{w}, \text{grad\_outputs} = \mathbf{v}_2)$ 4: return  $\frac{\partial \mathcal{L}_V}{\partial \lambda}\big|_{\lambda',\mathbf{w}'} - \mathbf{v}_3$  > Return to Alg. [1](#page-2-0)

Algorithm 3 approxInverseHVP( $v, f$ ): Neumann approximation of inverse-Hessian-vector product  $\mathbf{v}[\frac{\partial \mathbf{f}}{\partial \mathbf{w}}]^{-1}$ 

<span id="page-2-2"></span>1: Initialize sum  $\mathbf{p} = \mathbf{v}$ 2: for  $j = 1 \ldots i$  do 3:  $\mathbf{v} - = \alpha \cdot \text{grad}(\mathbf{f}, \mathbf{w}, \text{grad\_outputs} = \mathbf{v})$ 4:  $p == v$ 5: return p  $\triangleright$  Return to Alg. [2](#page-2-1).

#### 2.1 Proposed Algorithms

We outline our method in Algs. [1,](#page-2-0) [2,](#page-2-1) and [3,](#page-2-2) where  $\alpha$ denotes the learning rate. Alg. [3](#page-2-2) is also shown in [\[22\]](#page-8-13). We visualize the hypergradient computation in Fig. [2.](#page-2-3)

## 3 Related Work

Implicit Function Theorem. The IFT has been used for optimization in nested optimization problems [\[27,](#page-9-0) [28\]](#page-9-1), backpropagating through arbitrarily long RNNs [\[22\]](#page-8-13), or even efficient k-fold cross-validation [\[29\]](#page-9-2). Early work applied the IFT to regularization by explicitly computing the Hessian (or Gauss-Newton) inverse [\[23,](#page-9-3) [2\]](#page-8-1). In [\[24\]](#page-9-4), the identity matrix is used to approximate the inverse Hessian in the IFT. HOAG [\[30\]](#page-9-5) uses conjugate gradient (CG) to invert the Hessian approximately and provides convergence results given tolerances on the optimal parameter and inverse. In iMAML [\[9\]](#page-8-15), a center to the weights is fit to perform well on multiple tasks—contrasted with our use of validation loss. In DEQ [\[31\]](#page-9-6), implicit differentiation is used to add differentiable fixed-point methods into NN architectures. We use a Neumann approximation for the inverse-Hessian, instead of CG [\[30,](#page-9-5) [9\]](#page-8-15) or the identity.

Approximate inversion algorithms. CG is difficult to scale to modern, deep NNs. We use the Neumann inverse approximation, which was observed to be a stable alternative to CG in NNs [\[22,](#page-8-13) [7\]](#page-8-14). The stability is motivated by connections between the Neumann approximation and unrolled differentiation [\[7\]](#page-8-14). Alternatively, we could use prior knowledge about the NN structure to aid in the inversion—e.g., by using KFAC [\[32\]](#page-9-7). It is possible to approximate the Hessian with the Gauss-Newton matrix or Fisher Information matrix [\[23\]](#page-9-3). Various works use an identity approximation to the inverse, which is equivalent to 1-step unrolled differentiation [\[24,](#page-9-4) [14,](#page-8-7) [33,](#page-9-8) [10,](#page-8-16) [8,](#page-8-17) [34,](#page-9-9) [35\]](#page-9-10).

Unrolled differentiation for HO. A key difficulty in nested optimization is approximating how the optimized inner parameters (i.e., NN weights) change with respect to the outer parameters (i.e., hyperparameters). We often optimize the inner parameters with gradient descent, so we can simply differentiate through this optimization. Differentiation through optimization has been applied to nested optimization problems by [\[3\]](#page-8-2), was scaled to HO for NNs by [\[4\]](#page-8-5), and has been applied to various applications like learning optimizers [\[36\]](#page-9-11). [\[6\]](#page-8-18) provides convergence results for this class of algorithms, while [\[5\]](#page-8-19) discusses forward- and reverse-mode variants.

As the number of gradient steps we backpropagate through increases, so does the memory and computational cost. Often, gradient descent does not exactly minimize our objective after a finite number of steps it only approaches a local minimum. Thus, to see how the hyperparameters affect the local minima, we may have to unroll the optimization infeasibly far. Unrolling a small number of steps can be crucial for performance but may induce bias [\[37\]](#page-9-12). [\[7\]](#page-8-14) discusses connections between unrolling and the IFT, and proposes to unroll only the last L-steps. DrMAD [\[38\]](#page-9-13) proposes an interpolation scheme to save memory.

We compare hypergradient approximations in Table [1,](#page-3-1) and memory costs of gradient-based HO methods in Table [2.](#page-3-2) We survey gradient-free HO in Appendix [B.](#page-11-1)

| Jonathan Lorraine | Paul Vicol | David Duvenaud |
|-------------------|------------|----------------|
|-------------------|------------|----------------|

| Method                                      | <b>Steps</b> | Eval. | <b>Hypergradient Approximation</b>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     |  |  |  |
|---------------------------------------------|--------------|-------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|
| Exact IFT                                   |              |       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |  |  |  |
| Unrolled Diff. [4]                          |              |       | <b>1</b><br><b>1</b><br><b>1</b><br><b>1</b><br><b>1</b><br><b>1</b><br><b>1</b><br><b>1</b><br>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       |  |  |  |
| L-Step Truncated Unrolled Diff. [7]         |              |       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |  |  |  |
| Larsen et al. $[23]$                        | $\infty$     |       | $\begin{pmatrix}\n\widehat{\mathbf{w}^*}(\boldsymbol{\lambda}) & \frac{\partial \mathcal{L}_{\mathrm{V}}}{\partial \boldsymbol{\lambda}} - \frac{\partial \mathcal{L}_{\mathrm{V}}}{\partial \mathbf{w}} \times \\ \widehat{\mathbf{w}^*}(\boldsymbol{\lambda}) & \frac{\partial \mathcal{L}_{\mathrm{V}}}{\partial \boldsymbol{\lambda}} - \frac{\partial \mathcal{L}_{\mathrm{V}}}{\partial \mathbf{w}} \times \\ \widehat{\mathbf{w}^*}(\boldsymbol{\lambda}) & \frac{\partial \mathcal{L}_{\mathrm{V}}}{\partial \boldsymbol{\lambda}} - \frac{\partial \mathcal{L}_{\mathrm{V}}}{\partial \mathbf{w}} \times \\ $ |  |  |  |
| Bengio [2]                                  | $\infty$     |       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |  |  |  |
| $T1 - T2$ [24]                              | $\mathbf{1}$ |       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |  |  |  |
| Ours                                        |              |       | 1<br>$\hat{\mathbf{w}}^*(\boldsymbol{\lambda})$<br>$\hat{\mathbf{w}}^*(\boldsymbol{\lambda})$<br>$\hat{\mathbf{w}}^*(\boldsymbol{\lambda})$<br>$\hat{\mathbf{w}}^*(\boldsymbol{\lambda})$<br>$\hat{\mathbf{w}}^*(\boldsymbol{\lambda})$<br>$\hat{\mathbf{w}}^*(\boldsymbol{\lambda})$<br>$\hat{\mathbf{w}}^*(\boldsymbol{\lambda})$<br>$\hat{\mathbf{w}}^*(\boldsymbol{\lambda})$<br>$\hat{\mathbf{w}}^*(\boldsymbol{\lambda})$<br>$\hat{\mathbf{w}}^*(\boldsymbol{\lambda})$<br>$\hat{\mathbf{w}}^*(\boldsymbol{\lambda})$                                                                                            |  |  |  |
| Conjugate Gradient (CG) $\approx$           |              |       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |  |  |  |
| Hypernetwork $[25, 26]$                     |              |       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |  |  |  |
| Bayesian Optimization [19, 20, 7] $\approx$ |              |       |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |  |  |  |

<span id="page-3-1"></span>Table 1: An overview of methods to approximate hypergradients. Some methods can be viewed as using an approximate inverse in the IFT, or as differentiating through optimization around an evaluation point. Here,  $\mathbf{w}^*(\lambda)$  is an approximation of the best-response at a fixed  $\lambda$ , which is often found with gradient descent.

| Method                       | <b>Memory Cost</b>    |
|------------------------------|-----------------------|
| Diff. through Opt. [3, 4, 7] | $O(PI + H)$           |
| Linear Hypernet [25]         | $O(PH)$               |
| Self-Tuning Nets (STN) [26]  | $\mathcal{O}((P+H)K)$ |
| Neumann/CG IFT               | $\mathcal{O}(P+H)$    |

<span id="page-3-2"></span>Table 2: Gradient-based methods for HO. Differentiation through optimization scales with the number of unrolled iterations  $I$ ; the STN scales with bottleneck size  $K$ , while our method only scales with the weight and hyperparameter sizes P and H.

## <span id="page-3-0"></span>4 Method

In this section, we discuss how HO is a uniquely challenging nested optimization problem and how to combine the benefits of the IFT and unrolled differentiation.

## 4.1 Hyperparameter Opt. is Pure-Response

Eq. [3](#page-1-3) shows that the hypergradient decomposes into a direct and indirect gradient. The bottleneck in hypergradient computation is usually finding the indirect gradient because we must take into account how the optimized parameters vary with respect to the hyperparameters. A simple optimization approach is to neglect the indirect gradient and only use the direct gradient. This can be useful in zero-sum games like GANs [\[39\]](#page-9-16) because they always have a non-zero direct term.

However, using only the direct gradient does not work in general games [\[40\]](#page-9-17). In particular, it does not work for HO because the direct gradient is identically 0 when the hyperparameters  $\lambda$  can only influence the validation

loss by changing the optimized weights  $\mathbf{w}^*(\lambda)$ . For example, if we use regularization like weight decay when computing the training loss, but not the validation loss, then the direct gradient is always 0.

If the direct gradient is identically 0, we call the game pure-response. Pure-response games are uniquely difficult nested optimization problems for gradient-based optimization because we cannot use simple algorithms that rely on the direct gradient like simultaneous SGD. Thus, we must approximate the indirect gradient.

#### 4.2 Unrolled Optimization and the IFT

Here, we discuss the relationship between the IFT and differentiation through optimization. Specifically, we (1) introduce the recurrence relation that arises when we unroll SGD optimization, (2) give a formula for the derivative of the recurrence, and (3) establish conditions for the recurrence to converge. Notably, we show that the fixed points of the recurrence recover the IFT solution. We use these results to motivate a computationally tractable approximation scheme to the IFT solution. We give proofs of all results in Appendix [D.](#page-14-0)

Unrolling SGD optimization—given an initialization  $w_0$ —gives us the recurrence:

<span id="page-3-3"></span>
$$
\mathbf{w}_{i+1}(\boldsymbol{\lambda}) = T(\boldsymbol{\lambda}, \mathbf{w}_i) = \mathbf{w}_i(\boldsymbol{\lambda}) - \alpha \frac{\partial \mathcal{L}_T(\boldsymbol{\lambda}, \mathbf{w}_i(\boldsymbol{\lambda}))}{\partial \mathbf{w}} \qquad (5)
$$

In our exposition, assume that  $\alpha = 1$ . We provide a formula for the derivative of the recurrence, to show that it converges to the IFT under some conditions.

Lemma. Given the recurrence from unrolling SGD optimization in Eq. [5,](#page-3-3) we have:

$$
\frac{\partial \mathbf{w}_{i+1}}{\partial \lambda} = -\sum_{j \leq i} \left[ \prod_{k < j} I - \frac{\partial^2 \mathcal{L}_\Gamma}{\partial \mathbf{w} \partial \mathbf{w}^T} \bigg|_{\lambda, \mathbf{w}_{i-k}(\lambda)} \right] \frac{\partial^2 \mathcal{L}_\Gamma}{\partial \mathbf{w} \partial \lambda^T} \bigg|_{\lambda, \mathbf{w}_{i-j}(\lambda)} \tag{6}
$$

This recurrence converges to a fixed point if the transition Jacobian  $\frac{\partial T}{\partial \mathbf{w}}$  is contractive, by the Banach Fixed-Point Theorem [\[41\]](#page-9-18). Theorem [2](#page-4-0) shows that the recurrence converges to the IFT if we start at locally optimal weights  $\mathbf{w}_0 = \mathbf{w}^*(\boldsymbol{\lambda})$ , and the transition Jacobian  $\frac{\partial T}{\partial \mathbf{w}}$  is contractive. We leverage that if an operator  $U$  is contractive, then the Neumann series  $\sum_{i=0}^{\infty} U^k = (\text{Id}-U)^{-1}$ .

Theorem 2 (Neumann-SGD). Given the recurrence from unrolling SGD optimization in Eq. [5,](#page-3-3) if  $\mathbf{w}_0 =$  $\mathbf{w}^*(\boldsymbol{\lambda})$ :

$$
\frac{\partial \mathbf{w}_{i+1}}{\partial \lambda} = -\left(\sum_{j < i} \left[I - \frac{\partial^2 \mathcal{L}_\Gamma}{\partial \mathbf{w} \partial \mathbf{w}^T}\right]^j\right) \frac{\partial^2 \mathcal{L}_\Gamma}{\partial \mathbf{w} \partial \lambda^T}\Bigg|_{\mathbf{w}^*(\lambda)} \tag{7}
$$

and if  $I + \frac{\partial^2 \mathcal{L}_\Gamma}{\partial \mathbf{w} \partial \mathbf{w}^T}$  is contractive:

<span id="page-4-0"></span>
$$
\lim_{i \to \infty} \frac{\partial \mathbf{w}_{i+1}}{\partial \lambda} = -\left[ \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \mathbf{w}^T} \right]^{-1} \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \lambda^T} \Big|_{\mathbf{w}^*(\lambda)} \tag{8}
$$

This result is also shown in [\[7\]](#page-8-14), but they use a different approximation for computing the hypergradient—see Table [1.](#page-3-1) Instead, we use the following best-response Jacobian approximation, where i controls the trade-off between computation and error bounds:

$$
\frac{\partial \mathbf{w}^*}{\partial \mathbf{\lambda}} \approx -\left(\sum_{j < i} \left[I - \alpha \frac{\partial^2 \mathcal{L}_\mathbf{T}}{\partial \mathbf{w} \partial \mathbf{w}^T}\right]^j\right) \frac{\partial^2 \mathcal{L}_\mathbf{T}}{\partial \mathbf{w} \partial \mathbf{\lambda}^T}\Bigg|_{\mathbf{w}^*(\mathbf{\lambda})} \tag{9}
$$

Shaban et al. [\[7\]](#page-8-14) use an approximation that scales memory linearly in  $i$ , while ours is constant. We save memory because we reuse last  $w i$  times, while [\[7\]](#page-8-14) needs the last i w's. Scaling the Hessian by the learning rate  $\alpha$ is key for convergence. Our algorithm has the following main advantages relative to other approaches:

- It requires a constant amount of memory, unlike other unrolled differentiation methods [\[4,](#page-8-5) [7\]](#page-8-14).
- It is more stable than conjugate gradient, like unrolled differentiation methods [\[22,](#page-8-13) [7\]](#page-8-14).

#### 4.3 Scope and Limitations

The assumptions necessary to apply the IFT are as follows: (1)  $\mathcal{L}_V : \mathbf{\Lambda} \times \mathbf{W} \to \mathbb{R}$  is differentiable, (2)  $\mathcal{L}_{\text{T}} : \Lambda \times W \to \mathbb{R}$  is twice differentiable with an invertible Hessian at  $\mathbf{w}^*(\lambda)$ , and (3)  $\mathbf{w}^* : \Lambda \to \mathbf{W}$  is differentiable.

We need continuous hyperparameters to use gradientbased optimization, but many discrete hyperparameters (e.g., number of hidden units) have continuous relaxations [\[42,](#page-9-19) [43\]](#page-9-20). Also, we can only optimize hyperparameters that change the loss manifold, so our approach is not straightforwardly applicable to optimizer hyperparameters.

To exactly compute hypergradients, we must find  $(\lambda', \mathbf{w}')$  s.t.  $\frac{\partial \mathcal{L}_{\Gamma}}{\partial \mathbf{w}}|_{\lambda', \mathbf{w}'} = 0$ , which we can only solve to a tolerance with an approximate solution denoted  $\widehat{\mathbf{w}^*}(\lambda)$ . [\[30\]](#page-9-5) shows results for error in w<sup>∗</sup> and the inversion.

## 5 Experiments

We first compare the properties of Neumann inverse approximations and conjugate gradient, with experiments similar to [\[22,](#page-8-13) [4,](#page-8-5) [7,](#page-8-14) [30\]](#page-9-5). Then we demonstrate that our proposed approach can overfit the validation data with small training and validation sets. Finally, we apply our approach to high-dimensional HO tasks: (1) dataset distillation; (2) learning a data augmentation network; and (3) tuning regularization parameters for an LSTM language model.

HO algorithms that are not based on implicit differentiation or differentiation through optimization—such as [\[44](#page-9-21)[–48,](#page-10-0) [20\]](#page-8-20)—do not scale to the high-dimensional hyperparameters we use. Thus, we cannot sensibly compare to them for high-dimensional problems.

#### 5.1 Approximate Inversion Algorithms

In Fig. [3](#page-5-0) we investigate how close various approximations are to the true inverse. We calculate the distance between the approximate hypergradient and the true hypergradient. We can only do this for small-scale problems because we need the exact inverse for the true hypergradient. Thus, we use a linear network on the Boston housing dataset [\[49\]](#page-10-1), which makes finding the best-response w<sup>∗</sup> and inverse training Hessian feasible.

We measure the cosine similarity, which tells us how accurate the direction is, and the  $\ell_2$  (Euclidean) distance between the approximate and true hypergradients. The Neumann approximation does better than CG in cosine similarity if we take enough HO steps, while CG always does better for  $\ell_2$  distance.

In Fig. [4](#page-5-1) we show the inverse Hessian for a fullyconnected 1-layer NN on the Boston housing dataset. The true inverse Hessian has a dominant diagonal, motivating identity approximations, while using more Neumann terms yields structure closer to the true inverse.

Image /page/5/Figure/1 description: The image displays a 2x2 grid of plots. The top-left plot shows the cosine similarity over optimization iterations, with lines for '20 CG Steps', '20 Neumann', '5 Neumann', '1 Neumann', and '0 Neumann'. The y-axis ranges from 0.0 to 1.0, and the x-axis ranges from approximately 0 to 600. The top-right plot shows the cosine similarity over the number of inversion steps, with dashed lines for 'CG' and 'Neumann'. The y-axis appears to range from 0.0 to 1.0, and the x-axis ranges from 0 to 50. The bottom-left plot shows the l2 distance on a logarithmic scale over optimization iterations, with the same legend as the top-left plot. The y-axis ranges from 10^-3 to 10^3, and the x-axis ranges from approximately 0 to 600. The bottom-right plot shows the l2 distance on a logarithmic scale over the number of inversion steps, with dashed lines for 'CG' and 'Neumann'. The y-axis ranges from approximately 10^-1 to 10^3, and the x-axis ranges from 0 to 50.

<span id="page-5-0"></span>Figure 3: Comparing approximate hypergradients for inverse Hessian approximations to true hypergradients. The Neumann scheme often has greater cosine similarity than CG, but larger  $\ell_2$  distance for equal steps.

Image /page/5/Figure/3 description: The image displays three heatmaps side-by-side, each representing a different matrix. A color bar on the right indicates that red corresponds to positive values (up to 1.0), blue to negative values (down to -1.0), and white to zero. The first heatmap, labeled '1 Neumann', shows a sparse matrix with a prominent red diagonal and scattered red and blue points elsewhere, suggesting weak correlations. The second heatmap, labeled '5 Neumann', exhibits a more structured pattern with distinct blocks of red and blue squares, indicating stronger and more organized correlations. The third heatmap, labeled 'True Inverse', presents a dense and noisy pattern with a mix of red and blue points throughout, representing a complex and potentially less structured inverse matrix compared to the other two.

<span id="page-5-1"></span>Figure 4: Inverse Hessian approximations preprocessed by applying tanh for a 1-layer, fully-connected NN on the Boston housing dataset as in [\[50\]](#page-10-2).

#### 5.2 Overfitting a Small Validation Set

In Fig. [5,](#page-5-2) we check the capacity of our HO algorithm to overfit the validation dataset. We use the same restricted dataset as in [\[5,](#page-8-19) [6\]](#page-8-18) of 50 training and validation examples, which allows us to assess HO performance easily. We tune a separate weight decay hyperparameter for each NN parameter as in [\[33,](#page-9-8) [4\]](#page-8-5). We show the performance with a linear classifier, AlexNet [\[51\]](#page-10-3), and ResNet44 [\[52\]](#page-10-4). For AlexNet, this yields more than 50 000 000 hyperparameters, so we can perfectly classify our validation data by optimizing the hyperparameters.

Algorithm [1](#page-2-0) achieves  $100\%$  accuracy on the training and validation sets with significantly lower accuracy on the test set (Appendix [E,](#page-16-0) Fig. [10\)](#page-16-1), showing that we have a powerful HO algorithm. The same optimizer is used for weights and hyperparameters in all cases.

#### 5.3 Dataset Distillation

Dataset distillation [\[4,](#page-8-5) [13\]](#page-8-6) aims to learn a small, synthetic training dataset from scratch, that condenses the knowledge contained in the original full-sized training set. The goal is that a model trained on the synthetic

Image /page/5/Figure/10 description: The image is a line graph showing validation error versus iteration. The x-axis is labeled "Iteration" and is on a logarithmic scale, ranging from 10^0 to 10^4. The y-axis is labeled "Validation Error" and ranges from 0.0 to 1.0. There are three lines plotted: a red line labeled "Linear", a green line labeled "AlexNet", and a blue line labeled "ResNet44". The "Linear" line starts at a validation error of approximately 0.8 and decreases to near 0.0 by iteration 10^3. The "AlexNet" line starts at a validation error of approximately 0.9 and decreases to around 0.2 by iteration 10^4, with significant fluctuations. The "ResNet44" line starts at a validation error of approximately 0.9 and decreases sharply to near 0.0 by iteration 10^3, with a slight increase and then decrease around that point.

<span id="page-5-2"></span>Figure 5: Algorithm [1](#page-2-0) can overfit a small validation set on CIFAR-10. It optimizes for loss and achieves 100 % validation accuracy for standard, large models.

data generalizes to the original validation and test sets. Distillation is an interesting benchmark for HO as it allows us to introduce tens of thousands of hyperparameters, and visually inspect what is learned: here, every pixel value in each synthetic training example is a hyperparameter. We distill MNIST and CIFAR-10/100 [\[53\]](#page-10-5), yielding  $28 \times 28 \times 10 = 7840$ ,  $32 \times 32 \times 3 \times 10 = 30720$ , and  $32\times32\times3\times100 = 300720$  hyperparameters, respectively. For these experiments, all labeled data are in our validation set, while our distilled data are in the training set. We visualize the distilled images for each class in Fig. [6,](#page-6-0) recovering recognizable digits for MNIST and reasonable color averages for CIFAR-10/100.

## 5.4 Learned Data Augmentation

Data augmentation is a simple way to introduce invariances to a model—such as scale or contrast invariance that improve generalization [\[17,](#page-8-9) [18\]](#page-8-10). Taking advantage of the ability to optimize many hyperparameters, we learn data augmentation from scratch (Fig. [7\)](#page-6-1).

Specifically, we learn a data augmentation network  $\tilde{\mathbf{x}} = \mathbf{f}_{\lambda}(\mathbf{x}, \epsilon)$  that takes a training example x and noise  $\epsilon \sim \mathcal{N}(0, \mathbf{I})$ , and outputs an augmented example  $\tilde{\mathbf{x}}$ . The noise  $\epsilon$  allows us to learn stochastic augmentations. We parameterize f as a U-net [\[54\]](#page-10-6) with a residual connection from the input to the output, to make it easy to learn the identity mapping. The parameters of the U-net,  $\lambda$ , are hyperparameters tuned for the validation loss—thus, we have 6659 hyperparameters. We trained a ResNet18 [\[52\]](#page-10-4) on CIFAR-10 with augmented examples produced by the U-net (that is simultaneously trained on the validation set).

Results for the identity and Neumann inverse approximations are shown in Table [3.](#page-6-2) We omit CG because it performed no better than the identity. We found that using the data augmentation network improves validation and test accuracy by 2-3%, and yields smaller variance between multiple random restarts. In [\[55\]](#page-10-7), a different augmentation network architecture is learned with adversarial training.

Image /page/6/Figure/1 description: The image displays a grid of images categorized by dataset and class. The top row shows CIFAR-10 Distillation results with classes Plane, Car, Bird, Cat, Deer, Dog, Frog, Horse, Ship, and Truck. The middle row shows MNIST Distillation results, displaying distorted grayscale images of digits. The bottom two rows show CIFAR-100 Distillation results, with the first row listing classes Apple, Fish, Baby, Bear, Beaver, Bed, Bee, Beetle, Bicycle, and Bottle, and the second row listing Bowl, Boy, Bridge, Bus, Butterfly, Camel, Can, Castle, Caterpillar, and Cattle. Each class has two corresponding images, likely representing original and distilled versions or different aspects of the distillation process.

Figure 6: Distilled datasets for CIFAR-10, MNIST, and CIFAR-100. For CIFAR-100, we show the first 20 classes—the rest are in Appendix Fig. [11.](#page-17-0) We learn one distilled image per class, so after training a logistic regression classifier on the distillation, it generalizes to the rest of the data.

Image /page/6/Picture/3 description: The image displays a grid of images with the columns labeled "Original", "Sample 1", "Sample 2", and "Pixel Std.". Each row contains four images. The first row shows a blurry image of a frog, followed by two blurry samples of the frog with different colorations, and a "Pixel Std." image that appears to highlight variations in the frog image. The second row shows a blurry image of a horse, followed by two blurry samples of the horse with different colorations, and a "Pixel Std." image that highlights variations in the horse image. The third row shows a blurry image of a raccoon or similar animal, followed by two blurry samples of the animal with different colorations, and a "Pixel Std." image that highlights variations in the animal image. The caption below the grid reads "Figure 7: Image data representation."

Figure 7: Learned data augmentations. The original image is on the left, followed by two augmented samples and the standard deviation of the pixel intensities from the augmentation distribution.

<span id="page-6-1"></span>

| Inverse Approx.  | Validation                         | <b>Test</b>                        |
|------------------|------------------------------------|------------------------------------|
| $0$              | $92.5 pm 0.021$                   | $92.6 pm 0.017$                   |
| 3 Neumann        | <b><math>95.1 pm 0.002</math></b> | $94.6 pm 0.001$                   |
| 3 Unrolled Diff. | $95.0 pm 0.002$                   | <b><math>94.7 pm 0.001</math></b> |
| I                | $94.6 pm 0.002$                   | $94.1 pm 0.002$                   |

Table 3: Accuracy of different inverse approximations. Using 0 means that no HO occurs, and the augmentation is initially the identity. The Neumann approach performs similarly to unrolled differentiation [\[4,](#page-8-5) [7\]](#page-8-14) with equal steps and less memory. Using more terms does better than the identity, and the identity performed better than CG (not shown), which was unstable.

#### <span id="page-6-0"></span>5.5 RNN Hyperparameter Optimization

We also used our proposed algorithm to tune regularization hyperparameters for an LSTM [\[56\]](#page-10-8) trained on the Penn TreeBank (PTB) corpus [\[57\]](#page-10-9). As in [\[58\]](#page-10-10), we used a 2-layer LSTM with 650 hidden units per layer and 650-dimensional word embeddings. Additional details are provided in Appendix [E.4.](#page-16-2)

Overfitting Validation Data. We first verify that our algorithm can overfit the validation set in a smalldata setting with 10 training and 10 validation sequences (Fig. [8\)](#page-6-3). The LSTM architecture we use has 13 280 400 weights, and we tune a separate weight decay hyperparameter per weight. We overfit the validation set, reaching nearly 0 validation loss.

<span id="page-6-2"></span>Image /page/6/Figure/10 description: The image displays a line graph illustrating the training and validation loss over iterations. The x-axis is labeled 'Iteration' and ranges from 0k to 100k in increments of 25k. The y-axis is labeled 'Loss' and ranges from 0 to 7. Two lines are plotted: a blue line representing 'Train' loss and an orange line representing 'Valid' loss. Both lines show a sharp decrease in the initial iterations, with the 'Train' loss stabilizing around 0.2 and the 'Valid' loss stabilizing around 0.5 after approximately 50k iterations. The 'Valid' loss exhibits more fluctuations than the 'Train' loss.

<span id="page-6-3"></span>Figure 8: Alg. [1](#page-2-0) can overfit a small validation set with an LSTM on PTB.

Large-Scale HO. There are various forms of regularization used for training RNNs, including variational dropout [\[59\]](#page-10-11) on the input, hidden state, and output; embedding dropout that sets rows of the embedding matrix to 0, removing tokens from all sequences in a mini-batch; DropConnect [\[60\]](#page-10-12) on the hidden-to-hidden weights; and activation and temporal activation regularization. We tune these 7 hyperparameters simultaneously. Additionally, we experiment with tuning separate dropout/DropConnect rate for each activation/weight, giving 1 691 951 total hyperparameters. To allow for gradient-based optimization of dropout rates, we use concrete dropout [\[61\]](#page-10-13).

Instead of using the small dropout initialization as in [\[26\]](#page-9-15), we use a larger initialization of 0.5, which prevents early learning rate decay for our method. The results for our new initialization with no HO, our method tuning the same hyperparameters as [\[26\]](#page-9-15) ("Ours"), and our method tuning many more hyperparameters ("Ours, Many") are shown in Table [4.](#page-7-0) We are able to tune hyperparameters more quickly and achieve better perplexities than the alternatives.

| Method        | Validation   | Test         | Time(s) |
|---------------|--------------|--------------|---------|
| Grid Search   | 97.32        | 94.58        | 100k    |
| Random Search | 84.81        | 81.46        | 100k    |
| Bayesian Opt. | 72.13        | 69.29        | 100k    |
| STN           | 70.30        | 67.68        | 25k     |
| No HO         | 75.72        | 71.91        | 18.5k   |
| Ours          | 69.22        | 66.40        | 18.5k   |
| Ours, Many    | <b>68.18</b> | <b>66.14</b> | 18.5k   |

<span id="page-7-0"></span>Table 4: Comparing HO methods for LSTM training on PTB. We tune millions of hyperparameters faster and with comparable memory to competitors tuning a handful. Our method competitively optimizes the same 7 hyperparameters as baselines from [\[26\]](#page-9-15) (first four rows). We show a performance boost by tuning millions of hyperparameters, introduced with per-unit/weight dropout and DropConnect. "No HO" shows how the hyperparameter initialization affects training.

#### 5.6 Effects of Many Hyperparameters

Given the ability to tune high-dimensional hyperparameters and the potential risk of overfitting to the validation set, should we reconsider how our training and validation splits are structured? Do the same heuristics apply as for low-dimensional hyperparameters (e.g., use  $\sim 10\%$  of the data for validation)?

In Fig. [9](#page-7-1) we see how splitting our data into training and validation sets of different ratios affects test performance. We show the results of jointly optimizing the NN weights and hyperparameters, as well as the results of fixing the final optimized hyperparameters and re-training the NN weights from scratch, which is a common technique for boosting performance [\[62\]](#page-10-14).

We evaluate a high-dimensional regime with a separate weight decay hyperparameter per NN parameter, and a low-dimensional regime with a single, global weight decay. We observe that: (1) for few hyperparameters, the optimal combination of validation data and hyperparameters has similar test performance with and without re-training, because the optimal amount of validation data is small; and (2) for many hyperparameters, the optimal combination of validation data and hyperparameters is significantly affected by re-training, because the optimal amount of validation data needs to be large to fit our hyperparameters effectively.

For few hyperparameters, our results agree with the standard practice of using 10% of the data for validation and the other 90% for training. For many hyperparameters, our results show that we should use larger validation partitions for HO. If we use a large validation partition to fit the hyperparameters, it is critical to re-train our model with all of the data.

Image /page/7/Figure/10 description: This figure contains two plots side-by-side, both titled "MNIST with Logistic Regression". The left plot is titled "Without re-training" and the right plot is titled "With re-training". Both plots have "Test Accuracy" on the y-axis and "% Data in Validation" on the x-axis, ranging from 0 to 0.9. The left plot shows two lines: a solid blue line labeled "Global Decay" and a dotted blue line labeled "Decay per Weight". The solid blue line starts at approximately 0.8, rises slightly to 0.81 at 0.1, then drops to 0.79 at 0.25, 0.72 at 0.5, and finally to 0.68 at 0.75 and 0.69 at 0.9. The dotted blue line starts at approximately 0.79, rises to 0.81 at 0.1, then to 0.82 at 0.25, and stays around 0.82 until 0.9. The right plot also shows two lines: a solid blue line labeled "Global Decay" and a dotted blue line labeled "Decay per Weight". Both lines start at approximately 0.81 at 0.1 and remain around 0.81 to 0.82 across the x-axis range up to 0.9. Error bars are visible on the data points for both plots.

<span id="page-7-1"></span>Figure 9: Test accuracy of logistic regression on MNIST, with different size validation splits. Solid lines correspond to a single global weight decay (1 hyperparameter), while dotted lines correspond to a separate weight decay per weight (many hyperparameters). The best validation proportion for test performance is different after re-training for many hyperparameters, but similar for few hyperparameters.

### 6 Conclusion

We present a gradient-based hyperparameter optimization algorithm that scales to high-dimensional hyperparameters for modern, deep NNs. We use the implicit function theorem to formulate the hypergradient as a matrix equation, whose bottleneck is inverting the Hessian of the training loss with respect to the NN parameters. We scale the hypergradient computation to large NNs by approximately inverting the Hessian, leveraging a relationship with unrolled differentiation.

We believe algorithms of this nature provide a path for practical nested optimization, where we have Hessians with known structure. Examples of this include GANs [\[39\]](#page-9-16), and other multi-agent games [\[63,](#page-10-15) [64\]](#page-10-16).

## Acknowledgements

We thank Chris Pal for recommending we investigate re-training with all the data, Haoping Xu for discussing related experiments on inverse-approximation variants, Roger Grosse for guidance, and Cem Anil & Chris Cremer for their feedback on the paper. Paul Vicol was supported by a JP Morgan AI Fellowship. We also thank everyone else at Vector for helpful discussions and feedback.

## References

- <span id="page-8-0"></span>[1] Jürgen Schmidhuber. Evolutionary principles in self-referential learning, or on learning how to learn: The meta-meta-... hook. PhD thesis, Technische Universität München, 1987.
- <span id="page-8-1"></span>[2] Yoshua Bengio. Gradient-based optimization of hyperparameters. Neural Computation, 12(8):1889– 1900, 2000.
- <span id="page-8-2"></span>[3] Justin Domke. Generic methods for optimizationbased modeling. In Artificial Intelligence and Statistics, pages 318–326, 2012.
- <span id="page-8-5"></span>[4] Dougal Maclaurin, David Duvenaud, and Ryan Adams. Gradient-based hyperparameter optimization through reversible learning. In International Conference on Machine Learning, pages 2113–2122, 2015.
- <span id="page-8-19"></span>[5] Luca Franceschi, Michele Donini, Paolo Frasconi, and Massimiliano Pontil. Forward and reverse gradient-based hyperparameter optimization. In International Conference on Machine Learning, pages 1165–1173, 2017.
- <span id="page-8-18"></span>[6] Luca Franceschi, Paolo Frasconi, Saverio Salzo, Riccardo Grazzi, and Massimiliano Pontil. Bilevel programming for hyperparameter optimization and meta-learning. In International Conference on Machine Learning, pages 1563–1572, 2018.
- <span id="page-8-14"></span>[7] Amirreza Shaban, Ching-An Cheng, Nathan Hatch, and Byron Boots. Truncated backpropagation for bilevel optimization. In International Conference on Artificial Intelligence and Statistics, pages 1723–1732, 2019.
- <span id="page-8-17"></span>[8] Chelsea Finn, Pieter Abbeel, and Sergey Levine. Model-agnostic meta-learning for fast adaptation of deep networks. In International Conference on Machine Learning, pages 1126–1135, 2017.
- <span id="page-8-15"></span>[9] Aravind Rajeswaran, Chelsea Finn, Sham Kakade, and Sergey Levine. Meta-learning with implicit gradients. arXiv preprint arXiv:1909.04630, 2019.
- <span id="page-8-16"></span>[10] Hanxiao Liu, Karen Simonyan, and Yiming Yang. Darts: Differentiable architecture search. *arXiv* preprint arXiv:1806.09055, 2018.

- <span id="page-8-3"></span>[11] Edward Grefenstette, Brandon Amos, Denis Yarats, Phu Mon Htut, Artem Molchanov, Franziska Meier, Douwe Kiela, Kyunghyun Cho, and Soumith Chintala. Generalized inner loop meta-learning. arXiv preprint arXiv:1910.01727, 2019.
- <span id="page-8-4"></span>[12] Jan Kukačka, Vladimir Golkov, and Daniel Cremers. Regularization for deep learning: A taxonomy. arXiv preprint arXiv:1710.10686, 2017.
- <span id="page-8-6"></span>[13] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. arXiv preprint arXiv:1811.10959, 2018.
- <span id="page-8-7"></span>[14] Mengye Ren, Wenyuan Zeng, Bin Yang, and Raquel Urtasun. Learning to reweight examples for robust deep learning. In International Conference on Machine Learning, pages 4331–4340, 2018.
- [15] Tae-Hoon Kim and Jonghyun Choi. ScreenerNet: Learning self-paced curriculum for deep neural networks. arXiv preprint arXiv:1801.00904, 2018.
- <span id="page-8-8"></span>[16] Jiong Zhang, Hsiang-fu Yu, and Inderjit S Dhillon. AutoAssist: A framework to accelerate training of deep neural networks. arXiv preprint arXiv:1905.03381, 2019.
- <span id="page-8-9"></span>[17] Ekin D Cubuk, Barret Zoph, Dandelion Mane, Vijay Vasudevan, and Quoc V Le. Autoaugment: Learning augmentation policies from data. arXiv preprint arXiv:1805.09501, 2018.
- <span id="page-8-10"></span>[18] Qizhe Xie, Zihang Dai, Eduard Hovy, Minh-Thang Luong, and Quoc V Le. Unsupervised data augmentation. arXiv preprint arXiv:1904.12848, 2019.
- <span id="page-8-11"></span>[19] Jonas Močkus. On Bayesian methods for seeking the extremum. In Optimization Techniques IFIP Technical Conference, pages 400–404. Springer, 1975.
- <span id="page-8-20"></span>[20] Jasper Snoek, Hugo Larochelle, and Ryan P Adams. Practical Bayesian optimization of machine learning algorithms. In Advances in Neural Information Processing Systems, pages 2951–2959, 2012.
- <span id="page-8-12"></span>[21] Kirthevasan Kandasamy, Karun Raju Vysyaraju, Willie Neiswanger, Biswajit Paria, Christopher R Collins, Jeff Schneider, Barnabas Poczos, and Eric P Xing. Tuning hyperparameters without grad students: Scalable and robust Bayesian optimisation with Dragonfly. arXiv preprint arXiv:1903.06694, 2019.
- <span id="page-8-13"></span>[22] Renjie Liao, Yuwen Xiong, Ethan Fetaya, Lisa Zhang, KiJung Yoon, Xaq Pitkow, Raquel Urtasun, and Richard Zemel. Reviving and improving recurrent back-propagation. In International Conference on Machine Learning, pages 3088–3097, 2018.

- <span id="page-9-3"></span>[23] Jan Larsen, Lars Kai Hansen, Claus Svarer, and M Ohlsson. Design and regularization of neural networks: The optimal use of a validation set. In Neural Networks for Signal Processing VI. Proceedings of the 1996 IEEE Signal Processing Society Workshop, pages 62–71, 1996.
- <span id="page-9-4"></span>[24] Jelena Luketina, Mathias Berglund, Klaus Greff, and Tapani Raiko. Scalable gradient-based tuning of continuous regularization hyperparameters. In International Conference on Machine Learning, pages 2952–2960, 2016.
- <span id="page-9-14"></span>[25] Jonathan Lorraine and David Duvenaud. Stochastic hyperparameter optimization through hypernetworks. arXiv preprint arXiv:1802.09419, 2018.
- <span id="page-9-15"></span>[26] Matthew MacKay, Paul Vicol, Jon Lorraine, David Duvenaud, and Roger Grosse. Self-tuning networks: Bilevel optimization of hyperparameters using structured best-response functions. In International Conference on Learning Representations, 2019.
- <span id="page-9-0"></span>[27] Peter Ochs, René Ranftl, Thomas Brox, and Thomas Pock. Bilevel optimization with nonsmooth lower level problems. In International Conference on Scale Space and Variational Methods in Computer Vision, pages 654–665, 2015.
- <span id="page-9-1"></span>[28] Anonymous. On solving minimax optimization locally: A follow-the-ridge approach. International Conference on Learning Representations, 2019. URL [https://openreview.net/pdf?id=](https://openreview.net/pdf?id=Hkx7_1rKwS) [Hkx7\\_1rKwS](https://openreview.net/pdf?id=Hkx7_1rKwS).
- <span id="page-9-2"></span>[29] Ahmad Beirami, Meisam Razaviyayn, Shahin Shahrampour, and Vahid Tarokh. On optimal generalizability in parametric learning. In Advances in Neural Information Processing Systems, pages 3455–3465, 2017.
- <span id="page-9-5"></span>[30] Fabian Pedregosa. Hyperparameter optimization with approximate gradient. In *International Con*ference on Machine Learning, pages 737–746, 2016.
- <span id="page-9-6"></span>[31] Shaojie Bai, J Zico Kolter, and Vladlen Koltun. Deep equilibrium models. arXiv preprint arXiv:1909.01377, 2019.
- <span id="page-9-7"></span>[32] James Martens and Roger Grosse. Optimizing neural networks with Kronecker-factored approximate curvature. In International Conference on Machine Learning, pages 2408–2417, 2015.
- <span id="page-9-8"></span>[33] Yogesh Balaji, Swami Sankaranarayanan, and Rama Chellappa. Metareg: Towards domain generalization using meta-regularization. In Advances in Neural Information Processing Systems, pages 998–1008, 2018.
- <span id="page-9-9"></span>[34] Ira Shavitt and Eran Segal. Regularization learning networks: Deep learning for tabular datasets.

In Advances in Neural Information Processing Systems, pages 1379–1389, 2018.

- <span id="page-9-10"></span>[35] Alex Nichol, Joshua Achiam, and John Schulman. On first-order meta-learning algorithms. arXiv preprint arXiv:1803.02999, 2018.
- <span id="page-9-11"></span>[36] Marcin Andrychowicz, Misha Denil, Sergio Gomez, Matthew W Hoffman, David Pfau, Tom Schaul, Brendan Shillingford, and Nando De Freitas. Learning to learn by gradient descent by gradient descent. In Advances in Neural Information Processing Systems, pages 3981–3989, 2016.
- <span id="page-9-12"></span>[37] Yuhuai Wu, Mengye Ren, Renjie Liao, and Roger Grosse. Understanding short-horizon bias in stochastic meta-optimization. arXiv preprint arXiv:1803.02021, 2018.
- <span id="page-9-13"></span>[38] Jie Fu, Hongyin Luo, Jiashi Feng, Kian Hsiang Low, and Tat-Seng Chua. DrMAD: Distilling reverse-mode automatic differentiation for optimizing hyperparameters of deep neural networks. In International Joint Conference on Artificial Intelligence, pages 1469–1475, 2016.
- <span id="page-9-16"></span>[39] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. In Advances in Neural Information Processing Systems, pages 2672–2680, 2014.
- <span id="page-9-17"></span>[40] David Balduzzi, Sebastien Racaniere, James Martens, Jakob Foerster, Karl Tuyls, and Thore Graepel. The mechanics of n-player differentiable games. In International Conference on Machine Learning, pages 363–372, 2018.
- <span id="page-9-18"></span>[41] Stefan Banach. Sur les opérations dans les ensembles abstraits et leur application aux équations intégrales. Fundamenta Mathematicae, 3:133–181, 1922.
- <span id="page-9-19"></span>[42] C Maddison, A Mnih, and Y Teh. The concrete distribution: A continuous relaxation of discrete random variables. In International Conference on Learning Representations, 2017.
- <span id="page-9-20"></span>[43] Eric Jang, Shixiang Gu, and Ben Poole. Categorical reparameterization with Gumbel-softmax. arXiv preprint arXiv:1611.01144, 2016.
- <span id="page-9-21"></span>[44] Max Jaderberg, Valentin Dalibard, Simon Osindero, Wojciech M Czarnecki, Jeff Donahue, Ali Razavi, Oriol Vinyals, Tim Green, Iain Dunning, Karen Simonyan, et al. Population based training of neural networks. arXiv preprint arXiv:1711.09846, 2017.
- [45] Kevin Jamieson and Ameet Talwalkar. Nonstochastic best arm identification and hyperparameter optimization. In Artificial Intelligence and Statistics, pages 240–248, 2016.

- <span id="page-10-17"></span>[46] James Bergstra and Yoshua Bengio. Random search for hyper-parameter optimization. Journal of Machine Learning Research, 13:281–305, 2012.
- [47] Manoj Kumar, George E Dahl, Vijay Vasudevan, and Mohammad Norouzi. Parallel architecture and hyperparameter search via successive halving and classification. arXiv preprint arXiv:1805.10255, 2018.
- <span id="page-10-0"></span>[48] Lisha Li, Kevin Jamieson, Giulia DeSalvo, Afshin Rostamizadeh, and Ameet Talwalkar. Hyperband: a novel bandit-based approach to hyperparameter optimization. The Journal of Machine Learning Research, 18(1):6765–6816, 2017.
- <span id="page-10-1"></span>[49] David Harrison Jr and Daniel L Rubinfeld. Hedonic housing prices and the demand for clean air. Journal of Environmental Economics and Management, 5(1):81–102, 1978.
- <span id="page-10-2"></span>[50] Guodong Zhang, Shengyang Sun, David Duvenaud, and Roger Grosse. Noisy natural gradient as variational inference. In International Conference on Machine Learning, pages 5847–5856, 2018.
- <span id="page-10-3"></span>[51] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. ImageNet classification with deep convolutional neural networks. In Advances in Neural Information Processing Systems, pages 1097–1105, 2012.
- <span id="page-10-4"></span>[52] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In Conference on Computer Vision and Pattern Recognition, pages 770–778, 2016.
- <span id="page-10-5"></span>[53] Alex Krizhevsky. Learning multiple layers of features from tiny images. Technical report, 2009.
- <span id="page-10-6"></span>[54] Olaf Ronneberger, Philipp Fischer, and Thomas Brox. U-Net: Convolutional networks for biomedical image segmentation. In International Conference on Medical image Computing and Computer-Assisted Intervention, pages 234–241, 2015.
- <span id="page-10-7"></span>[55] Saypraseuth Mounsaveng, David Vazquez, Ismail Ben Ayed, and Marco Pedersoli. Adversarial learning of general transformations for data augmentation. International Conference on Learning Representations, 2019.
- <span id="page-10-8"></span>[56] Sepp Hochreiter and Jürgen Schmidhuber. Long short-term memory. Neural Computation,  $9(8)$ : 1735–1780, 1997.
- <span id="page-10-9"></span>[57] Mitchell P Marcus, Mary Ann Marcinkiewicz, and Beatrice Santorini. Building a large annotated corpus of English: The Penn Treebank. Computational Linguistics, 19(2):313–330, 1993.
- <span id="page-10-10"></span>[58] Yarin Gal and Zoubin Ghahramani. A theoretically grounded application of dropout in recurrent

neural networks. In Advances in Neural Information Processing Systems, pages 1027–1035, 2016.

- <span id="page-10-11"></span>[59] Durk P Kingma, Tim Salimans, and Max Welling. Variational dropout and the local reparameterization trick. In Advances in Neural Information Processing Systems, pages 2575–2583, 2015.
- <span id="page-10-12"></span>[60] Li Wan, Matthew Zeiler, Sixin Zhang, Yann Le Cun, and Rob Fergus. Regularization of neural networks using Dropconnect. In International Conference on Machine Learning, pages 1058–1066, 2013.
- <span id="page-10-13"></span>[61] Yarin Gal, Jiri Hron, and Alex Kendall. Concrete dropout. In Advances in Neural Information Processing Systems, pages 3581–3590, 2017.
- <span id="page-10-14"></span>[62] Ian Goodfellow, Yoshua Bengio, and Aaron Courville. Deep Learning. MIT Press, 2016. <http://www.deeplearningbook.org>.
- <span id="page-10-15"></span>[63] Jakob Foerster, Richard Y Chen, Maruan Al-Shedivat, Shimon Whiteson, Pieter Abbeel, and Igor Mordatch. Learning with opponent-learning awareness. In International Conference on Autonomous Agents and MultiAgent Systems, pages 122–130, 2018.
- <span id="page-10-16"></span>[64] Alistair Letcher, Jakob Foerster, David Balduzzi, Tim Rocktäschel, and Shimon Whiteson. Stable opponent shaping in differentiable games. arXiv preprint arXiv:1811.08469, 2018.
- <span id="page-10-18"></span>[65] Andrew Brock, Theodore Lim, James Millar Ritchie, and Nicholas J Weston. SMASH: One-Shot Model Architecture Search through Hyper-Networks. In International Conference on Learning Representations, 2018.
- <span id="page-10-19"></span>[66] Adam Paszke, Sam Gross, Soumith Chintala, Gregory Chanan, Edward Yang, Zachary DeVito, Zeming Lin, Alban Desmaison, Luca Antiga, and Adam Lerer. Automatic differentiation in PyTorch. 2017.
- <span id="page-10-20"></span>[67] Diederik P Kingma and Jimmy Ba. Adam: A method for stochastic optimization. International Conference on Learning Representations, 2014.
- <span id="page-10-21"></span>[68] Geoffrey Hinton, Nitish Srivastava, and Kevin Swersky. Neural networks for machine learning. Lecture 6a. Overview of mini-batch gradient descent. 2012.
- <span id="page-10-22"></span>[69] Yann LeCun, Léon Bottou, Yoshua Bengio, Patrick Haffner, et al. Gradient-based learning applied to document recognition. Proceedings of the IEEE, 86(11):2278–2324, 1998.
- <span id="page-10-23"></span>[70] Stephen Merity, Nitish Shirish Keskar, and Richard Socher. Regularizing and optimizing LSTM language models. International Conference on Learning Representations, 2018.

## Optimizing Millions of Hyperparameters by Implicit Differentiation Appendix

<span id="page-11-0"></span>

### A Extended Background

In this section we provide an outline of our notation (Table [5\)](#page-12-0), and the proposed algorithm. Here, we assume we have access to to a finite dataset  $\mathcal{D} = \{(\boldsymbol{x}_i, \boldsymbol{y}_i)|i =$  $1 \ldots n$ , with *n* examples drawn from the distribution  $p(x, y)$  with support P. We denote the input and target domains by X and Y, respectively. Assume  $y : \mathcal{X} \to \mathcal{Y}$ is a function and we wish to learn  $\hat{y}: \mathcal{X} \times \mathbf{W} \to \mathcal{Y}$ with a NN parameterized by  $\mathbf{w} \in \mathbf{W}$ , s.t.  $\hat{\mathbf{y}}$  is close to y. We measure how close a predicted value is to a target with the prediction loss  $\mathcal{L}: \mathcal{Y} \times \mathcal{Y} \to \mathbb{R}$ . Our goal is to minimize the expected prediction loss or population risk:  $\arg \min_{\mathbf{w}} \mathbb{E}_{\mathbf{x} \sim p(\mathbf{x})} [\mathcal{L}(\hat{\mathbf{y}}(\mathbf{x}, \mathbf{w}), \mathbf{y}(\mathbf{x}))].$ Since we only have access to a finite number of samples, we minimize the empirical risk:  $\arg \min_\mathbf{w} \frac{1}{n} \sum_{\bm{x},\bm{y} \in \mathcal{D}} \mathcal{L}(\hat{\bm{y}}(\bm{x}, \mathbf{w}), \bm{y}(\bm{x})).$ 

Due to a limited size dataset  $D$ , there may be a significant difference between the minimizer of the empirical risk and the population risk. We can estimate this difference by partitioning our dataset into training and validation datasets— $\mathcal{D}_{train}, \mathcal{D}_{valid}$ . We find the minimizer over the training dataset  $\mathcal{D}_{train}$ , and estimate its performance on the population risk by evaluating the empirical risk over the validation dataset  $\mathcal{D}_{valid}$ . We introduce modifications to the empirical training risk to decrease our population risk, parameterized by  $\lambda \in \Lambda$ . These parameters for generalization are called the hyperparameters. We call the modified empirical training risk our training loss for simplicity and denote it  $\mathcal{L}_{T}(\lambda, w)$ . Our validation empirical risk is called validation loss for simplicity and denoted by  $\mathcal{L}_V(\lambda, \mathbf{w})$ . Often the validation loss does not directly depend on the hyperparameters, and we just have  $\mathcal{L}_V(\mathbf{w})$ .

The population risk is estimated by plugging the training loss minimizer  $\mathbf{w}^*\!({\boldsymbol{\lambda}}) = \argmin_{\mathbf{w}} \mathcal{L}_T({\boldsymbol{\lambda}}, \mathbf{w})$ into the validation loss for the estimated population risk  $\mathcal{L}_{\mathrm{V}}^*(\lambda) = \mathcal{L}_{\mathrm{V}}(\lambda, \mathbf{w}^*(\lambda)).$  We want our hyperparameters to minimize the estimated population risk:  $\lambda^* = \arg \min_{\lambda} \mathcal{L}_{\mathrm{V}}^*(\lambda)$ . We can create a third partition of our dataset  $\mathcal{D}_{test}$  to assess if we have overfit the validation dataset  $\mathcal{D}_{valid}$  with our hyperparameters  $\lambda$ .

<span id="page-11-1"></span>

### B Extended Related Work

Independent HO: A simple class of HO algorithms involve making a number of independent hyperparameter selections, and training the model to completion on them. Popular examples include grid search and random search [\[46\]](#page-10-17). Since each hyperparameter selection is independent, these algorithms are trivial to parallelize.

Global HO: Some HO algorithms attempt to find a globally optimal hyperparameter setting, which can be important if the loss is non-convex. A simple example is random search, while a more sophisticated example is Bayesian optimization [\[19](#page-8-11)[–21\]](#page-8-12). These HO algorithms often involve re-initializing the hyperparameter and weights on each optimization iteration. This allows global optimization, at the cost of expensive re-training weights or hyperparameters.

Local HO: Other HO algorithms only attempt to find a locally optimal hyperparameter setting. Often these algorithms will maintain a current estimate of the best combination of hyperparameter and weights. On each optimization iteration, the hyperparameter is adjusted by a small amount, which allows us to avoid excessive re-training of the weights on each update. This is because the new optimal weights are near the old optimal weights due to a small change in the hyperparameters.

Learned proxy function based HO: Many HO algorithms attempt to learn a proxy function for optimization. The proxy function is used to estimate the loss for a hyperparameter selection. We could learn a proxy function for global or local HO . We can learn a useful proxy function over any node in our computational graph including the optimized weights. For example, we could learn how the optimized weights change w.r.t. the hyperparameters [\[25\]](#page-9-14), how the optimized predictions change w.r.t. the hyperparameters [\[26\]](#page-9-15), or how the optimized validation loss changes w.r.t. the hyperparameters as in Bayesian Optimization. It is possible to do gradient descent on the proxy function to find new hyperparameters to query as in Bayesian optimization. Alternatively, we could use a non-differentiable proxy function to get cheap estimates of the validation loss like SMASH [\[65\]](#page-10-18) for architecture choices.

Table 5: Notation

<span id="page-12-0"></span>

| HO                                                                                                                                                                                                                                                                                                                                         | Hyperparameter optimization                                            |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------|
| $\mathrm{NN}$                                                                                                                                                                                                                                                                                                                              | Neural network                                                         |
| <b>IFT</b>                                                                                                                                                                                                                                                                                                                                 | Implicit Function Theorem                                              |
| HVP / JVP                                                                                                                                                                                                                                                                                                                                  | Hessian/Jacobian-vector product                                        |
| $\lambda$ , w                                                                                                                                                                                                                                                                                                                              | Hyperparameters and NN parameters/weights                              |
| n, m                                                                                                                                                                                                                                                                                                                                       | Hyperparameter and NN parameter dimensionality                         |
| $\mathbf{\Lambda}\!\subseteq\! \mathbb{R}^n, \! \mathbf{W}\!\subseteq\! \mathbb{R}^m$                                                                                                                                                                                                                                                      | Hyperparameters and NN parameter domains                               |
| $\boldsymbol{\lambda}',\mathbf{w}'$                                                                                                                                                                                                                                                                                                        | Arbitrary, fixed hyperparameters and weights                           |
|                                                                                                                                                                                                                                                                                                                                            | Training loss $\&$ validation loss                                     |
|                                                                                                                                                                                                                                                                                                                                            | Best-response of the weights to the hyperparameters                    |
| $\mathcal{L}_T(\lambda, w), \mathcal{L}_V(\lambda, w)$<br>$\overrightarrow{w^*}(\lambda)$<br>$\mathcal{L}_V^*(\lambda) = \mathcal{L}_V(\lambda, w^*(\lambda))$<br>Red                                                                                                                                                                      | An approximate best-response of the weights to the hyperparameters     |
|                                                                                                                                                                                                                                                                                                                                            | The validation loss with best-responding weights                       |
|                                                                                                                                                                                                                                                                                                                                            | (Approximations to) The validation loss with best-responding weights   |
| $\mathbf{W}^* = \mathbf{w}^*(\mathbf{\Lambda})$                                                                                                                                                                                                                                                                                            | The domain of best-responding weights                                  |
| $\lambda^*$                                                                                                                                                                                                                                                                                                                                | The optimal hyperparameters                                            |
| $\boldsymbol{x},\boldsymbol{y}$                                                                                                                                                                                                                                                                                                            | An input and its associated target                                     |
| $\mathcal{X}, \mathcal{Y}$                                                                                                                                                                                                                                                                                                                 | The input and target domains respectively                              |
| $\mathcal{D}$                                                                                                                                                                                                                                                                                                                              | A data matrix consisting of tuples of inputs and targets               |
| y(x, w)                                                                                                                                                                                                                                                                                                                                    | A predicted target for a input data and weights                        |
| $\frac{\partial \mathcal{L}_V}{\partial \mathbf{\lambda}}, \frac{\partial \mathcal{L}_V}{\partial \mathbf{w}}$                                                                                                                                                                                                                             | The (validation loss hyperparameter / parameter) direct gradient       |
| Green                                                                                                                                                                                                                                                                                                                                      | (Approximations to) The validation loss direct gradient.               |
| $\frac{\partial \mathbf{w}^*}{\partial \boldsymbol{\lambda}}$                                                                                                                                                                                                                                                                              | The best-response Jacobian                                             |
| <b>Blue</b>                                                                                                                                                                                                                                                                                                                                | (Approximations to) The (Jacobian of the) best-response of the weights |
|                                                                                                                                                                                                                                                                                                                                            | to the hyperparameters                                                 |
|                                                                                                                                                                                                                                                                                                                                            | The indirect gradient                                                  |
|                                                                                                                                                                                                                                                                                                                                            | A hypergradient: sum of validation losses direct and indirect gradient |
| $\begin{array}{c} \frac{\partial \mathcal{L}_\mathrm{V}}{\partial \mathbf{w}} \frac{\partial \mathbf{w}^*}{\partial \boldsymbol{\lambda}} \\ \frac{\partial \mathcal{L}_\mathrm{V}^*}{\partial \boldsymbol{\lambda}} \\ \left[\frac{\partial^2 \mathcal{L}_\mathrm{T}}{\partial \mathbf{w} \partial \mathbf{w}^T}\right]^{-1} \end{array}$ | The training Hessian inverse                                           |
| Magenta                                                                                                                                                                                                                                                                                                                                    | (Approximations to) The training Hessian inverse                       |
| $\frac{\partial \mathcal{L}_V}{\partial \mathbf{w}}\left[\frac{\partial^2 \mathcal{L}_T}{\partial \mathbf{w}\partial \mathbf{w}^T}\right]^{-1}$                                                                                                                                                                                            | The vector - Inverse Hessian product.                                  |
| Orange                                                                                                                                                                                                                                                                                                                                     | (Approximations to) The vector - Inverse Hessian product.              |
| $\frac{\partial^2 \mathcal{L}_\text{T}}{\partial \textbf{w} \partial \textbf{\lambda}^T}$                                                                                                                                                                                                                                                  | The training mixed partial derivatives                                 |
|                                                                                                                                                                                                                                                                                                                                            | The identity matrix                                                    |

<span id="page-13-0"></span>

### C Implicit Function Theorem

**Theorem** (Augustin-Louis Cauchy, Implicit Function Theorem). Let  $\frac{\partial \mathcal{L}_{\Gamma}}{\partial \mathbf{w}}(\lambda, \mathbf{w}) : \Lambda \times \mathbf{W} \to \mathbf{W}$  be a continuously ∂w differentiable function. Fix a point  $(\lambda', \mathbf{w}')$  with  $\frac{\partial \mathcal{L}_{\Gamma}}{\partial \mathbf{w}}(\lambda', \mathbf{w}') = 0$ . If the Jacobian J  $\frac{\partial \mathcal{L}_{\text{T}}}{\partial \mathbf{w}}(\lambda', \mathbf{w}')$  is invertible, there exists an open set  $U \subseteq \Lambda$  containing  $\lambda'$  s.t. there exists a continuously differentiable function  $\mathbf{w}^*: U \to \mathbf{W}$  s.t.:

$$
\mathbf{w}^*(\boldsymbol{\lambda}') = \mathbf{w}' \text{ and } \forall \boldsymbol{\lambda} \in U, \frac{\partial \mathcal{L}_T}{\partial \mathbf{w}}(\boldsymbol{\lambda}, \mathbf{w}^*(\boldsymbol{\lambda}))) = 0
$$

Moreover, the partial derivatives of  $\mathbf{w}^*$  in U are given by the matrix product:

$$
\frac{\partial \mathbf{w}^*}{\partial \boldsymbol{\lambda}}(\boldsymbol{\lambda}) = -\left[ J_{\mathbf{w}}^{\frac{\partial \mathcal{L}_T}{\partial \mathbf{w}}}(\boldsymbol{\lambda}, \mathbf{w}^*(\boldsymbol{\lambda})) \right]^{-1} J_{\boldsymbol{\lambda}}^{\frac{\partial \mathcal{L}_T}{\partial \mathbf{w}}}(\boldsymbol{\lambda}, \mathbf{w}^*(\boldsymbol{\lambda})))
$$

Typically the IFT is presented with  $\frac{\partial \mathcal{L}_{\Gamma}}{\partial \mathbf{w}} = f$ ,  $\mathbf{w}^* = g$ ,  $\mathbf{\Lambda} = \mathbb{R}^m$ ,  $\mathbf{W} = \mathbb{R}^n$ ,  $\mathbf{\lambda} = x$ ,  $\mathbf{w} = y$ ,  $\mathbf{\lambda}' = a$ ,  $\mathbf{w}' = b$ .

<span id="page-14-0"></span>

## D Proofs

**Lemma** (1). If the recurrence given by unrolling SGD optimization in Eq. [5](#page-3-3) has a fixed point  $w_{\infty}$  (i.e.,  $0 = \frac{\partial \mathcal{L}_T}{\partial \mathbf{w}}|_{\boldsymbol{\lambda}, \mathbf{w}_{\infty}(\boldsymbol{\lambda})}), \text{ then:}$ 

$$
\frac{\partial \mathbf{w}_{\infty}}{\partial \boldsymbol{\lambda}} = -\left[\frac{\partial^2 \mathcal{L}_{\text{T}}}{\partial \mathbf{w} \partial \mathbf{w}^T}\right]^{-1} \frac{\partial^2 \mathcal{L}_{\text{T}}}{\partial \mathbf{w} \partial \boldsymbol{\lambda}^T}\Bigg|_{\mathbf{w}_{\infty}(\boldsymbol{\lambda})}
$$

Proof.

$$
\Rightarrow \frac{\partial}{\partial \lambda} \left( \frac{\partial \mathcal{L}_{\Gamma}}{\partial \mathbf{w}} \Big|_{\lambda, \mathbf{w}_{\infty}(\lambda)} \right) = 0
$$
  

$$
\Rightarrow \left( \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \lambda^T} I + \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \mathbf{w}^T} \frac{\partial \mathbf{w}_{\infty}}{\partial \lambda} \right) \Big|_{\lambda, \mathbf{w}_{\infty}(\lambda)} = 0
$$
  

$$
\Rightarrow \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \mathbf{w}^T} \frac{\partial \mathbf{w}_{\infty}}{\partial \lambda} \Big|_{\lambda, \mathbf{w}_{\infty}(\lambda)} = -\frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \lambda^T} \Big|_{\lambda, \mathbf{w}_{\infty}(\lambda)}
$$
  

$$
\Rightarrow \frac{\partial \mathbf{w}_{\infty}}{\partial \lambda} \Big|_{\lambda} = -\left[ \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \mathbf{w}^T} \right]^{-1} \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \lambda^T} \Big|_{\lambda}
$$

given

chain rule through  $|_{\lambda,\mathbf{w}_\infty(\lambda)}$ 

re-arrange terms

left-multiply by 
$$
\left[\frac{\partial^2 \mathcal{L}_T}{\partial \mathbf{w} \partial \mathbf{w}^T}\right]^{-1}\Big|_{\lambda, \mathbf{w}_{\infty}(\lambda)}
$$

Lemma (2). Given the recurrence from unrolling SGD optimization in Eq. [5](#page-3-3) we have:

$$
\frac{\partial \mathbf{w}_{i+1}}{\partial \lambda} = -\sum_{j \leq i} \left( \prod_{k < j} I - \frac{\partial^2 \mathcal{L}_T}{\partial \mathbf{w} \partial \mathbf{w}^T} \bigg|_{\mathbf{\lambda}, \mathbf{w}_{i-k}(\lambda)} \right) \frac{\partial^2 \mathcal{L}_T}{\partial \mathbf{w} \partial \lambda^T} \bigg|_{\mathbf{\lambda}, \mathbf{w}_{i-j}(\lambda)}
$$

Proof.

Proof.

$$
rac{\partial \mathbf{w}_{i+1}}{\partial \lambda} \Big|_{\lambda} = \frac{\partial}{\partial \lambda} \left( \mathbf{w}_{i}(\lambda) - \frac{\partial \mathcal{L}_{\Gamma}}{\partial \mathbf{w}} \Big|_{\lambda, \mathbf{w}_{i}(\lambda)} \right)
$$
take derivative w.r.t. \lambda

$$
= \frac{\partial \mathbf{w}_{i}}{\partial \lambda} \Big|_{\lambda} - \frac{\partial}{\partial \lambda} \left( \frac{\partial \mathcal{L}_{\Gamma}}{\partial \mathbf{w}} \Big|_{\lambda, \mathbf{w}_{i}(\lambda)} \right)
$$
chain rule

$$
= \frac{\partial \mathbf{w}_{i}}{\partial \lambda} \Big|_{\lambda} - \left( \frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \mathbf{w}^{T}} \frac{\partial \mathbf{w}_{i}}{\partial \lambda} + \frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \lambda^{T}} \right) \Big|_{\lambda, \mathbf{w}_{i}(\lambda)}
$$
chain rule through \big|\_{\lambda, \mathbf{w}\_{i}(\lambda)}

$$
= -\frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \lambda^{T}} \Big|_{\lambda, \mathbf{w}_{i}(\lambda)} + \left( I - \frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \mathbf{w}^{T}} \right) \frac{\partial \mathbf{w}_{i}}{\partial \lambda} \Big|_{\lambda, \mathbf{w}_{i}(\lambda)}
$$
re-arrange terms

$$
= -\frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \lambda^{T}} \Big|_{\lambda, \mathbf{w}_{i}(\lambda)} + \left( I - \frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \mathbf{w}^{T}} \right) \Big|_{\lambda, \mathbf{w}_{i}(\lambda)} \frac{\partial \mathbf{w}_{i}}{\partial \lambda} \Big|_{\lambda, \mathbf{w}_{i}(\lambda)}
$$
expand \frac{\partial \mathbf{w}\_{i}}{\partial \lambda}

$$
= -\frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \lambda^{T}} \Big|_{\lambda, \mathbf{w}_{i}(\lambda)} - \left( I - \frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \mathbf{w}^{T}} \right) \Big|_{\lambda, \mathbf{w}_{i}(\lambda)} \frac{\partial \mathbf{w}_{i-1}}{\partial \lambda} \Big|_{\lambda, \mathbf{w}_{i-1}(\lambda)} + \left( \left( I - \frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \mathbf{w}^{T}} \right) \frac{\partial \mathbf{w}_{i-1}}{\partial \lambda} - \frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \lambda^{T}} \right) \Big|_{\lambda, \mathbf{w}_{i-1}(\lambda)}
$$
re-arrange terms

$$
= \dots
$$
So,

$$
rac{\partial \mathbf{w}_{i+1}}{\partial \lambda} = - \sum_{j \leq i} \left[ \prod_{k < j} \left( I - \frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \mathbf{w}^{T}} \right) \Big|_{\lambda, \mathbf{w}_{i-k}(\lambda)} \right] \frac{\partial^{2} \mathcal{L}_{\Gamma}}{\partial \mathbf{w} \partial \lambda^{T}} \Big|_{\lambda, \mathbf{w}_{i-j}(\lambda)}
$$
telescope the recurrence

$$
\Box
$$

 $\Box$ 

**Theorem** (Neumann-SGD). Given the recurrence from unrolling SGD optimization in Eq. [5,](#page-3-3) if  $\mathbf{w}_0 = \mathbf{w}^*(\lambda)$ :

$$
\frac{\partial \mathbf{w}_{i+1}}{\partial \lambda} = -\left(\sum_{j\leq i} \left[I - \frac{\partial^2 \mathcal{L}_\mathbf{T}}{\partial \mathbf{w} \partial \mathbf{w}^T}\right]^j\right) \frac{\partial^2 \mathcal{L}_\mathbf{T}}{\partial \mathbf{w} \partial \lambda^T}\Bigg|_{\mathbf{w}^*(\lambda)}
$$

and if  $I + \frac{\partial^2 \mathcal{L}_\Gamma}{\partial \mathbf{w} \partial \mathbf{w}^T}$  is contractive:

$$
\lim_{i \to \infty} \frac{\partial w_{i+1}}{\partial \lambda} = - \left[ \frac{\partial^2 \mathcal{L}_T}{\partial w \partial w^T} \right]^{-1} \frac{\partial^2 \mathcal{L}_T}{\partial w \partial \lambda^T} \Bigg|_{w^*(\lambda)}
$$

Proof.

$$
\lim_{i \to \infty} \frac{\partial w_{i+1}}{\partial \lambda} \Big|_{\lambda}
$$
 $\text{take } \lim_{i \to \infty}$ 

$$
= \lim_{i \to \infty} \left( - \sum_{j \leq i} \left[ \prod_{k < j} I - \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial w \partial w} \Big|_{\lambda, w_{i-k}(\lambda)} \right] \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial w \partial \lambda^T} \Big|_{\lambda, w_{i-j}(\lambda)} \right)
$$
 $\text{by Lemma 2}$ 

$$
= - \lim_{i \to \infty} \left( \sum_{j \leq i} \left[ \prod_{k < j} I - \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial w \partial w} \right] \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial w \partial \lambda^T} \right) \Big|_{\lambda, w^{*}(\lambda)}
$$
 $\text{w}_0 = \text{w}^{*}(\lambda) = \text{w}_i$ 

$$
= - \lim_{i \to \infty} \left( \sum_{j \leq i} \left[ I - \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial w \partial w} \right]^j \right) \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial w \partial \lambda^T} \Big|_{\lambda, w^{*}(\lambda)}
$$
 $\text{simplify}$ 

$$
= - \left[ I - \left( I - \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial w \partial w} \right)^{-1} \right] \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial w \partial \lambda^T} \Big|_{\lambda, w^{*}(\lambda)}
$$
 $\text{contractive } \& \text{ Neumann series}$ 

$$
= - \left[ \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial w \partial w} \right]^{-1} \frac{\partial^2 \mathcal{L}_{\Gamma}}{\partial w \partial \lambda^T} \Big|_{\lambda, w^{*}(\lambda)}
$$
 $\text{simplify}$ 

 $\Box$ 

<span id="page-16-0"></span>

### E Experiments

We use PyTorch [\[66\]](#page-10-19) as our computational framework. All experiments were performed on NVIDIA TITAN Xp GPUs.

For all CNN experiments we use the following optimization setup: for the NN weights we use Adam [\[67\]](#page-10-20) with a learning rate of 1e-4. For the hyperparameters we use RMSprop [\[68\]](#page-10-21) with a learning rate of 1e-2.

### E.1 Overfitting a Small Validation Set

We see our algorithm's ability to overfit the validation data (see Fig. [10\)](#page-16-1). We use 50 training input, and 50 validation input with the standard testing partition for both MNIST and CIFAR-10. We check performance with logistic regression (Linear), a 1-Layer fullyconnected NN with as many hidden units as input size (ex.,  $28 \times 28 = 784$ , or  $32 \times 32 \times 3 = 3072$ ), LeNet [\[69\]](#page-10-22). AlexNet [\[51\]](#page-10-3), and ResNet44 [\[52\]](#page-10-4). In all examples we can achieve 100 % training and validation accuracy, while the testing accuracy is significantly lower.

Image /page/16/Figure/6 description: This image contains two plots side-by-side, both titled with dataset names: MNIST on the left and CIFAR-10 on the right. Both plots have 'Iteration' on the x-axis (on a logarithmic scale) and 'Classification Error' on the y-axis (ranging from 0.0 to 1.0). Each plot displays multiple lines representing different models and training stages. The legend indicates lines for 'Training', 'Validation', and 'Test' (all in black with different markers), and specific models: 'Linear' (purple), '1-Layer' (red), 'LeNet' (green), 'AlexNet' (yellow), and 'ResNet44' (blue). In the MNIST plot, most models show a decrease in classification error with increasing iterations, with some reaching near zero error. In the CIFAR-10 plot, the models also show varying degrees of error reduction, with some models like ResNet44 and Linear achieving low errors, while AlexNet shows a fluctuating high error.

<span id="page-16-1"></span>Figure 10: Overfitting validation data. Algorithm [1](#page-2-0) can overfit the validation dataset. We use 50 training input, and 50 validation input with the standard testing partition for both MNIST and CIFAR-10. We check the performance with logistic regression (Linear), a 1-Layer fully-connected NN with as many hidden units as input size (ex.,  $28 \times 28 = 784$ , or  $32 \times 32 \times 3 = 3072$ ), LeNet [\[69\]](#page-10-22), AlexNet [\[51\]](#page-10-3), and ResNet44 [\[52\]](#page-10-4). Separate lines are plotted for the training, validation, and testing error. In all examples we achieve 100 % training and validation accuracy, while the testing accuracy is significantly lower.

### E.2 Dataset Distillation

With MNIST we use the entire dataset in validation, while for CIFAR we use 300 validation data points.

### E.3 Learned Data Augmentation

Augmentation Network Details: Data augmentation can be framed as an image-to-image transformation

problem; inspired by this, we use a U-Net [\[54\]](#page-10-6) as the data augmentation network. To allow for stochastic transformations, we feed in random noise by concatenating a noise channel to the input image, so the resulting input has 4 channels.

<span id="page-16-2"></span>

### E.4 RNN Hyperparameter Optimization

Our base our implementation on the AWD-LSTM codebase [https://github.com/salesforce/](https://github.com/salesforce/awd-lstm-lm) [awd-lstm-lm](https://github.com/salesforce/awd-lstm-lm). Similar to [\[58\]](#page-10-10) we used a 2-layer LSTM with 650 hidden units per layer and 650-dimensional word embeddings.

Overfitting Validation Data: We used a subset of 10 training sequences and 10 validation sequences, and tuned separate weight decays per parameter. The LSTM architecture we use has 13 280 400 weights, and thus an equal number of weight decay hyperparameters.

Optimization Details: For the large-scale experiments, we follow the training setup proposed in [\[70\]](#page-10-23): for the NN weights, we use SGD with learning rate 30 and gradient clipping to magnitude 0.25. The learning rate was decayed by a factor of 4 based on the nonmonotonic criterion introduced by [\[70\]](#page-10-23) (i.e., when the validation loss fails to decrease for 5 epochs). To optimize the hyperparameters, we used Adam with learning rate 0.001. We trained on sequences of length 70 in mini-batches of size 40.

|  |  |  | $\mathcal{F}^{\mathcal{A}}$ |  |  |
|--|--|--|-----------------------------|--|--|
|  |  |  |                             |  |  |
|  |  |  |                             |  |  |
|  |  |  |                             |  |  |
|  |  |  |                             |  |  |
|  |  |  |                             |  |  |
|  |  |  |                             |  |  |

### CIFAR-100 Distillation

<span id="page-17-0"></span>Figure 11: The complete dataset distillation for CIFAR-100. Referenced in Fig. [6.](#page-6-0)
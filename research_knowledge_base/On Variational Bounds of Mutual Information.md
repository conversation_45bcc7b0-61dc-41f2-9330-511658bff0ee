<span id="page-0-0"></span><PERSON>  $^1\;$  <PERSON><PERSON><PERSON><PERSON>  $^{12}\;$  <PERSON><PERSON><PERSON>  $^3\;$  <PERSON>  $^1\;$  <PERSON>  $^1\;$ 

# Abstract

Estimating and optimizing Mutual Information (MI) is core to many problems in machine learning; however, bounding MI in high dimensions is challenging. To establish tractable and scalable objectives, recent work has turned to variational bounds parameterized by neural networks, but the relationships and tradeoffs between these bounds remains unclear. In this work, we unify these recent developments in a single framework. We find that the existing variational lower bounds degrade when the MI is large, exhibiting either high bias or high variance. To address this problem, we introduce a continuum of lower bounds that encompasses previous bounds and flexibly trades off bias and variance. On high-dimensional, controlled problems, we empirically characterize the bias and variance of the bounds and their gradients and demonstrate the effectiveness of our new bounds for estimation and representation learning.

# 1. Introduction

Estimating the relationship between pairs of variables is a fundamental problem in science and engineering. Quantifying the degree of the relationship requires a metric that captures a notion of dependency. Here, we focus on mutual information (MI), denoted  $I(X; Y)$ , which is a reparameterization-invariant measure of dependency:

$$
I(X;Y) = \mathbb{E}_{p(x,y)} \left[ \log \frac{p(x|y)}{p(x)} \right] = \mathbb{E}_{p(x,y)} \left[ \log \frac{p(y|x)}{p(y)} \right].
$$

Mutual information estimators are used in computational neuroscience [\(Palmer et al.,](#page-9-0) [2015\)](#page-9-0), Bayesian optimal experimental design [\(Ryan et al.,](#page-9-0) [2016;](#page-9-0) [Foster et al.,](#page-8-0) [2018\)](#page-8-0), understanding neural networks [\(Tishby et al.,](#page-9-0) [2000;](#page-9-0) [Tishby &](#page-9-0) [Zaslavsky,](#page-9-0) [2015;](#page-9-0) Gabrié et al., [2018\)](#page-8-0), and more. In practice, estimating MI is challenging as we typically have access to

Image /page/0/Figure/10 description: This is a flowchart illustrating different methods for estimating mutual information, categorized into Lower Bounds, Unbiased Estimates, and Upper Bounds. The central node is I(X;Y), representing mutual information. From I(X;Y), branches lead to I\_BA §2.1 (tractable q(x|y)) and KL(p(y|x)||q(y)) (unknown p(y|x) and p(y)). Another branch from I(X;Y) leads to Structured Bounds §2.5 (unknown p(y)). The lower bounds section includes I\_InfoNCE §2.3, I\_UBA §2.2, I\_alpha §2.4, I\_TUBA, I\_DV, I\_NWJ, and I\_MINE. The unbiased estimates section includes Monte-Carlo estimation. The flowchart shows dependencies and conditions (known/unknown probabilities) for each method.

Figure 1. Schematic of variational bounds of mutual information presented in this paper. Nodes are colored based on their tractability for estimation and optimization: green bounds can be used for both, yellow for optimization but not estimation, and red for neither. Children are derived from their parents by introducing new approximations or assumptions.

samples but not the underlying distributions [\(Paninski,](#page-9-0) [2003;](#page-9-0) [McAllester & Stratos,](#page-9-0) [2018\)](#page-9-0). Existing sample-based estimators are brittle, with the hyperparameters of the estimator impacting the scientific conclusions [\(Saxe et al.,](#page-9-0) [2018\)](#page-9-0).

Beyond estimation, many methods use upper bounds on MI to limit the capacity or contents of representations. For example in the information bottleneck method [\(Tishby et al.,](#page-9-0) [2000;](#page-9-0) [Alemi et al.,](#page-8-0) [2016\)](#page-8-0), the representation is optimized to solve a downstream task while being constrained to contain as little information as possible about the input. These techniques have proven useful in a variety of domains, from restricting the capacity of discriminators in GANs [\(Peng](#page-9-0) [et al.,](#page-9-0) [2018\)](#page-9-0) to preventing representations from containing information about protected attributes [\(Moyer et al.,](#page-9-0) [2018\)](#page-9-0).

Lastly, there are a growing set of methods in representation learning that maximize the mutual information between a learned representation and an aspect of the data. Specifically, given samples from a data distribution,  $x \sim p(x)$ , the goal is to learn a stochastic representation of the data  $p_{\theta}(y|x)$  that has maximal MI with X subject to constraints on the mapping (e.g. [Bell & Sejnowski,](#page-8-0) [1995;](#page-8-0) [Krause et al.,](#page-8-0) [2010;](#page-8-0) [Hu et al.,](#page-8-0) [2017;](#page-8-0) [van den Oord et al.,](#page-9-0) [2018;](#page-9-0) [Hjelm](#page-8-0) [et al.,](#page-8-0) [2018;](#page-8-0) [Alemi et al.,](#page-8-0) [2017\)](#page-8-0). To maximize MI, we can compute gradients of a lower bound on MI with respect to the parameters  $\theta$  of the stochastic encoder  $p_{\theta}(y|x)$ , which may not require directly estimating MI.

 $1^1$ Google Brain <sup>2</sup>MILA  $3$ DeepMind. Correspondence to: Ben Poole <<EMAIL>>.

*Proceedings of the*  $36<sup>th</sup>$  *International Conference on Machine Learning*, Long Beach, California, PMLR 97, 2019. Copyright  $2019$  by the author(s).

<span id="page-1-0"></span>While many parametric and non-parametric [\(Nemenman](#page-9-0) [et al.,](#page-9-0) [2004;](#page-9-0) [Kraskov et al.,](#page-8-0) [2004;](#page-8-0) [Reshef et al.,](#page-9-0) [2011;](#page-9-0) [Gao](#page-8-0) [et al.,](#page-8-0) [2015\)](#page-8-0) techniques have been proposed to address MI estimation and optimization problems, few of them scale up to the dataset size and dimensionality encountered in modern machine learning problems.

To overcome these scaling difficulties, recent work combines variational bounds [\(Blei et al.,](#page-8-0) [2017;](#page-8-0) [Donsker & Varad](#page-8-0)[han,](#page-8-0) [1983;](#page-8-0) [Barber & Agakov,](#page-8-0) [2003;](#page-8-0) [Nguyen et al.,](#page-9-0) [2010;](#page-9-0) [Foster et al.,](#page-8-0) [2018\)](#page-8-0) with deep learning [\(Alemi et al.,](#page-8-0) [2016;](#page-8-0) [2017;](#page-8-0) [van den Oord et al.,](#page-9-0) [2018;](#page-9-0) [Hjelm et al.,](#page-8-0) [2018;](#page-8-0) [Belghazi](#page-8-0) [et al.,](#page-8-0) [2018\)](#page-8-0) to enable differentiable and tractable estimation of mutual information. These papers introduce flexible parametric distributions or *critics* parameterized by neural networks that are used to approximate unkown densities  $(p(y), p(y|x))$  or density ratios  $(\frac{p(x|y)}{p(x)} = \frac{p(y|x)}{p(y)}$  $\frac{y(x)}{p(y)}$ ).

In spite of their effectiveness, the properties of existing variational estimators of MI are not well understood. In this paper, we introduce several results that begin to demystify these approaches and present novel bounds with improved properties (see Fig. [1](#page-0-0) for a schematic):

- We provide a review of existing estimators, discussing their relationships and tradeoffs, including the first proof that the noise contrastive loss in [van den Oord](#page-9-0) [et al.](#page-9-0) [\(2018\)](#page-9-0) is a lower bound on MI, and that the heuristic "bias corrected gradients" in [Belghazi et al.](#page-8-0) [\(2018\)](#page-8-0) can be justified as unbiased estimates of the gradients of a different lower bound on MI.
- We derive a new continuum of multi-sample lower bounds that can flexibly trade off bias and variance, generalizing the bounds of [\(Nguyen et al.,](#page-9-0) [2010;](#page-9-0) [van den Oord et al.,](#page-9-0) [2018\)](#page-9-0).
- We show how to leverage known conditional structure yielding simple lower and upper bounds that sandwich MI in the representation learning context when  $p_{\theta}(y|x)$ is tractable.
- We systematically evaluate the bias and variance of MI estimators and their gradients on controlled highdimensional problems.
- We demonstrate the utility of our variational upper and lower bounds in the context of decoder-free disentangled representation learning on dSprites [\(Matthey et al.,](#page-9-0) [2017\)](#page-9-0).

## 2. Variational bounds of MI

Here, we review existing variational bounds on MI in a unified framework, and present several new bounds that trade off bias and variance and naturally leverage known

conditional densities when they are available. A schematic of the bounds we consider is presented in Fig. [1.](#page-0-0) We begin by reviewing the classic upper and lower bounds of [Bar](#page-8-0)[ber & Agakov](#page-8-0) [\(2003\)](#page-8-0) and then show how to derive the lower bounds of [Donsker & Varadhan](#page-8-0) [\(1983\)](#page-8-0); [Nguyen](#page-9-0) [et al.](#page-9-0) [\(2010\)](#page-9-0); [Belghazi et al.](#page-8-0) [\(2018\)](#page-8-0) from an unnormalized variational distribution. Generalizing the unnormalized bounds to the multi-sample setting yields the bound proposed in [van den Oord et al.](#page-9-0) [\(2018\)](#page-9-0), and provides the basis for our interpolated bound.

### 2.1. Normalized upper and lower bounds

Upper bounding MI is challenging, but is possible when the conditional distribution  $p(y|x)$  is known (e.g. in deep representation learning where  $y$  is the stochastic representation). We can build a tractable variational upper bound by introducing a variational approximation  $q(y)$  to the intractable marginal  $p(y) = \int dx p(x)p(y|x)$ . By multiplying and dividing the integrand in MI by  $q(y)$  and dropping a negative KL term, we get a tractable variational upper bound [\(Barber](#page-8-0) [& Agakov,](#page-8-0) [2003\)](#page-8-0):

$$
I(X;Y) \equiv \mathbb{E}_{p(x,y)} \left[ \log \frac{p(y|x)}{p(y)} \right]
$$
  

$$
= \mathbb{E}_{p(x,y)} \left[ \log \frac{p(y|x)q(y)}{q(y)p(y)} \right]
$$
  

$$
= \mathbb{E}_{p(x,y)} \left[ \log \frac{p(y|x)}{q(y)} \right] - KL(p(y)||q(y))
$$
  

$$
\leq \mathbb{E}_{p(x)} \left[ KL(p(y|x)||q(y)) \right] \triangleq R,
$$
 (1)

which is often referred to as the *rate* in generative models [\(Alemi et al.,](#page-8-0) [2017\)](#page-8-0). This bound is tight when  $q(y) =$  $p(y)$ , and requires that computing  $\log q(y)$  is tractable. This variational upper bound is often used as a regularizer to limit the capacity of a stochastic representation (e.g. [Rezende](#page-9-0) [et al.,](#page-9-0) [2014;](#page-9-0) [Kingma & Welling,](#page-8-0) [2013;](#page-8-0) [Burgess et al.,](#page-8-0) [2018\)](#page-8-0). In [Alemi et al.](#page-8-0) [\(2016\)](#page-8-0), this upper bound is used to prevent the representation from carrying information about the input that is irrelevant for the downstream classification task.

Unlike the upper bound, most variational lower bounds on mutual information do not require direct knowledge of any conditional densities. To establish an initial lower bound on mutual information, we factor MI the opposite direction as the upper bound, and replace the intractable conditional distribution  $p(x|y)$  with a tractable optimization problem over a variational distribution  $q(x|y)$ . As shown in [Barber](#page-8-0) [& Agakov](#page-8-0) [\(2003\)](#page-8-0), this yields a lower bound on MI due to the non-negativity of the KL divergence:

$$
I(X;Y) = \mathbb{E}_{p(x,y)} \left[ \log \frac{q(x|y)}{p(x)} \right] + \mathbb{E}_{p(y)} \left[ KL(p(x|y)||q(x|y)) \right] \tag{2}
$$
  
$$
\geq \mathbb{E}_{p(x,y)} \left[ \log q(x|y) \right] + h(X) \triangleq I_{BA},
$$

where  $h(X)$  is the differential entropy of X. The bound is tight when  $q(x|y) = p(x|y)$ , in which case the first term equals the conditional entropy  $h(X|Y)$ .

Unfortunately, evaluating this objective is generally intractable as the differential entropy of  $X$  is often unknown. If  $h(X)$  is known, this provides a tractable estimate of a lower bound on MI. Otherwise, one can still compare the amount of information different variables (e.g.,  $Y_1$  and  $Y_2$ ) carry about  $X$ .

In the representation learning context where  $X$  is data and Y is a learned stochastic representation, the first term of  $I<sub>BA</sub>$  can be thought of as negative reconstruction error or distortion, and the gradient of  $I<sub>BA</sub>$  with respect to the "encoder"  $p(y|x)$  and variational "decoder"  $q(x|y)$  is tractable. Thus we can use this objective to learn an encoder  $p(y|x)$ that maximizes  $I(X; Y)$  as in [Alemi et al.](#page-8-0) [\(2017\)](#page-8-0). However, this approach to representation learning requires building a tractable decoder  $q(x|y)$ , which is challenging when X is high-dimensional and  $h(X|Y)$  is large, for example in video representation learning [\(van den Oord et al.,](#page-9-0) [2016\)](#page-9-0).

### 2.2. Unnormalized lower bounds

To derive tractable lower bounds that do not require a tractable decoder, we turn to *unnormalized* distributions for the variational family of  $q(x|y)$ , and show how this recovers the estimators of [Donsker & Varadhan](#page-8-0) [\(1983\)](#page-8-0); [Nguyen et al.](#page-9-0) [\(2010\)](#page-9-0).

We choose an energy-based variational family that uses a *critic*  $f(x, y)$  and is scaled by the data density  $p(x)$ :

$$
q(x|y) = \frac{p(x)}{Z(y)} e^{f(x,y)}, \text{ where } Z(y) = \mathbb{E}_{p(x)} \left[ e^{f(x,y)} \right].
$$
\n(3)

Substituting this distribution into  $I<sub>BA</sub>$  (Eq. [2\)](#page-1-0) gives a lower bound on MI which we refer to as  $I_{UBA}$  for the Unnormalized version of the Barber and Agakov bound:

$$
\mathbb{E}_{p(x,y)}\left[f(x,y)\right] - \mathbb{E}_{p(y)}\left[\log Z(y)\right] \triangleq I_{\text{UBA}}.\tag{4}
$$

This bound is tight when  $f(x, y) = \log p(y|x) + c(y)$ , where  $c(y)$  is solely a function of y (and not x). Note that by scaling  $q(x|y)$  by  $p(x)$ , the intractable differential entropy term in  $I_{BA}$  cancels, but we are still left with an intractable log partition function,  $\log Z(y)$ , that prevents evaluation or gradient computation. If we apply Jensen's inequality to  $\mathbb{E}_p(y)$  [log  $Z(y)$ ], we can lower bound Eq. 4 to recover the bound of [Donsker & Varadhan](#page-8-0) [\(1983\)](#page-8-0):

$$
I_{\text{UBA}} \geq \mathbb{E}_{p(x,y)} \left[ f(x,y) \right] - \log \mathbb{E}_{p(y)} \left[ Z(y) \right] \triangleq I_{\text{DV}}.
$$
 (5)

However, this objective is still intractable. Applying Jensen's the other direction by replacing  $\log Z(y)$  =  $\log \mathbb{E}_{p(x)}\left[e^{f(x,y)}\right]$  with  $\mathbb{E}_{p(x)}\left[f(x,y)\right]$  results in a tractable objective, but produces an upper bound on Eq. 4 (which is itself a lower bound on mutual information). Thus evaluating  $I_{\text{DV}}$  using a Monte-Carlo approximation of the expectations as in MINE [\(Belghazi et al.,](#page-8-0) [2018\)](#page-8-0) produces *estimates* that are *neither an upper or lower bound on MI*. Recent work has studied the convergence and asymptotic consistency of such nested Monte-Carlo estimators, but does not address the problem of building bounds that hold with finite samples [\(Rainforth et al.,](#page-9-0) [2018;](#page-9-0) [Mathieu et al.,](#page-9-0) [2018\)](#page-9-0).

To form a tractable bound, we can upper bound the log partition function using the inequality:  $\log(x) \leq \frac{x}{a} + \log(a) - 1$ for all  $x, a > 0$ . Applying this inequality to the second term of Eq. 4 gives:  $\log Z(y) \le \frac{Z(y)}{a(y)} + \log(a(y)) - 1$ , which is tight when  $a(y) = Z(y)$ . This results in a Tractable Unnormalized version of the Barber and Agakov (TUBA) lower bound on MI that admits unbiased estimates and gradients:

$$
I 
\ge I_{\text{UBA}} 
\ge \mathbb{E}_{p(x,y)} [ f(x,y) ] 
- \mathbb{E}_{p(y)} 
[ \frac{\mathbb{E}_{p(x)} [ e^{f(x,y)} ]}{a(y)} + \log(a(y)) - 1 ] 
\triangleq I_{\text{TUBA}}.
$$
 (6)

To tighten this lower bound, we maximize with respect to the variational parameters  $a(y)$  and f. In the InfoMax setting, we can maximize the bound with respect to the stochastic encoder  $p_{\theta}(y|x)$  to increase  $I(X; Y)$ . Unlike the min-max objective of GANs, all parameters are optimized towards the same objective.

This bound holds for any choice of  $a(y) > 0$ , with simplifications recovering existing bounds. Letting  $a(y)$  be the constant e recovers the bound of Nguyen, Wainwright, and Jordan [\(Nguyen et al.,](#page-9-0) [2010\)](#page-9-0) also known as f-GAN KL [\(Nowozin et al.,](#page-9-0) [2016\)](#page-9-0) and MINE-f [\(Belghazi et al.,](#page-8-0)  $2018$ <sup>1</sup>:

$$
\mathbb{E}_{p(x,y)}\left[f(x,y)\right] - e^{-1}\mathbb{E}_{p(y)}\left[Z(y)\right] \triangleq I_{\text{NWJ}}.\tag{7}
$$

This tractable bound no longer requires learning  $a(y)$ , but now  $f(x, y)$  must learn to self-normalize, yielding a unique optimal critic  $f^*(x, y) = 1 + \log \frac{p(x|y)}{p(x)}$ . This requirement of self-normalization is a common choice when learning log-linear models and empirically has been shown not to negatively impact performance [\(Mnih & Teh,](#page-9-0) [2012\)](#page-9-0).

Finally, we can set  $a(y)$  to be the scalar exponential moving average (EMA) of  $e^{f(x,y)}$  across minibatches. This pushes the normalization constant to be independent of  $y$ , but it no longer has to exactly self-normalize. With this choice of  $a(y)$ , the gradients of  $I_{\text{TUBA}}$  exactly yield the "improved" MINE gradient estimator" from [\(Belghazi et al.,](#page-8-0) [2018\)](#page-8-0). This provides sound justification for the heuristic optimization

 ${}^{1}I_{\text{TUBA}}$  can also be derived the opposite direction by plugging the critic  $f'(x, y) = f(x, y) - \log a(y) + 1$  into  $I_{\text{NWJ}}$ .

<span id="page-3-0"></span>procedure proposed by [Belghazi et al.](#page-8-0) [\(2018\)](#page-8-0). However, instead of using the critic in the  $I_{\text{DV}}$  bound to get an estimate that is not a bound on MI as in [Belghazi et al.](#page-8-0) [\(2018\)](#page-8-0), one can compute an estimate with  $I_{\text{TUBA}}$  which results in a valid lower bound.

To summarize, these unnormalized bounds are attractive because they provide tractable estimators which become tight with the optimal critic. However, in practice they exhibit high variance due to their reliance on high variance upper bounds on the log partition function.

### 2.3. Multi-sample unnormalized lower bounds

To reduce variance, we extend the unnormalized bounds to depend on multiple samples, and show how to recover the low-variance but high-bias MI estimator proposed by [van den Oord et al.](#page-9-0) [\(2018\)](#page-9-0).

Our goal is to estimate  $I(X_1, Y)$  given samples from  $p(x_1)p(y|x_1)$  and access to  $K-1$  additional samples  $x_{2:K} \sim r^{K-1}(x_{2:K})$  (potentially from a different distribution than  $X_1$ ). For any random variable Z independent from X and Y,  $I(X, Z; Y) = I(X; Y)$ , therefore:

$$
I(X_1;Y) = \mathbb{E}_{r^{K-1}(x_{2:K})} \left[ I(X_1;Y) \right] = I\left( X_1,X_{2:K};Y \right)
$$

This multi-sample mutual information can be estimated using any of the previous bounds, and has the same optimal critic as for  $I(X_1; Y)$ . For  $I_{NWJ}$ , we have that the optimal critic is  $f^*(x_{1:K}, y) = 1 + \log \frac{p(y|x_{1:K})}{p(y)} = 1 + \log \frac{p(y|x_1)}{p(y)}$ . However, the critic can now also depend on the additional samples  $x_{2:K}$ . In particular, setting the critic to  $1 + \log \frac{e^{f(x_1, y)}}{g(y, x_1, y)}$  $\frac{e^{f(x_1,y)}}{a(y;x_{1:K})}$  and  $r^{K-1}(x_{2:K}) = \prod_{j=2}^{K} p(x_j)$ ,  $I_{\text{NWJ}}$ becomes:

$$
I(X_1; Y) \ge 1 + \mathbb{E}_{p(x_{1:K})p(y|x_1)} \left[ \log \frac{e^{f(x_1, y)}}{a(y; x_{1:K})} \right] - \mathbb{E}_{p(x_{1:K})p(y)} \left[ \frac{e^{f(x_1, y)}}{a(y; x_{1:K})} \right],
$$
 (8)

where we have written the critic using parameters  $a(y; x_{1:K})$ to highlight the close connection to the variational parameters in  $I_{\text{TUBA}}$ . One way to leverage these additional samples from  $p(x)$  is to build a Monte-Carlo estimate of the partition function  $Z(y)$ :

$$
a(y; x_{1:K}) = m(y; x_{1:K}) = \frac{1}{K} \sum_{i=1}^{K} e^{f(x_i, y)}.
$$

Intriguingly, with this choice, the high-variance term in  $I<sub>NWI</sub>$ that estimates an upper bound on  $\log Z(y)$  is now upper bounded by  $\log K$  as  $e^{f(x_1, y)}$  appears in the numerator and also in the denominator (scaled by  $\frac{1}{K}$ ). If we average the bound over K replicates, reindexing  $x_1$  as  $x_i$  for each term, then the last term in Eq. 8 becomes the constant 1:

$$
Ep(x1:K)p(y)[ef(x1,y)m(y;x1:K)]=1K∑i=1Kⅉ[ef(xi,y)m(y;x1:K)]
$$
$$
= Ep(x1:K)p(y)[1K∑i=1Kef(xi,y)m(y;x1:K)]=1,(9)
$$

and we exactly recover the lower bound on MI proposed by [van den Oord et al.](#page-9-0) [\(2018\)](#page-9-0):

$$
I(X;Y) \geq \mathbb{E}\left[\frac{1}{K}\sum_{i=1}^{K}\log\frac{e^{f(x_i,y_i)}}{\frac{1}{K}\sum_{j=1}^{K}e^{f(x_i,y_j)}}\right] \triangleq I_{\text{NCE}},\tag{10}
$$

where the expectation is over  $K$  independent samples from the joint distribution:  $\prod_j p(x_j, y_j)$ . This provides a proof<sup>2</sup> that  $I_{NCE}$  is a lower bound on MI. Unlike  $I_{NWJ}$  where the optimal critic depends on both the conditional and marginal densities, the optimal critic for  $I_{NCE}$  is  $f(x, y) =$  $\log p(y|x) + c(y)$  where  $c(y)$  is any function that depends on y but not x [\(Ma & Collins,](#page-9-0) [2018\)](#page-9-0). Thus the critic only has to learn the conditional density and not the marginal density  $p(y)$ .

As pointed out in [van den Oord et al.](#page-9-0) [\(2018\)](#page-9-0),  $I_{NCE}$  is upper bounded by  $log K$ , meaning that this bound will be loose when  $I(X; Y) > \log K$ . Although the optimal critic does not depend on the batch size and can be fit with a smaller mini-batches, accurately estimating mutual information still needs a large batch size at test time if the mutual information is high.

## 2.4. Nonlinearly interpolated lower bounds

The multi-sample perspective on  $I<sub>NWI</sub>$  allows us to make other choices for the functional form of the critic. Here we propose one simple form for a critic that allows us to nonlinearly interpolate between  $I<sub>NWI</sub>$  and  $I<sub>NCE</sub>$ , effectively bridging the gap between the low-bias, high-variance  $I<sub>NWJ</sub>$  estimator and the high-bias, low-variance  $I_{NCE}$  estimator. Similarly to Eq. 8, we set the critic to  $1 + \log \frac{e^{f(x_1, y)}}{Cm(y_1, y) + (1, y)}$  $\alpha m(y;x_{1:K})+(1-\alpha)q(y)$ with  $\alpha \in [0, 1]$  to get a continuum of lower bounds:

$$
1 + \mathbb{E}_{p(x_{1:K})p(y|x_{1})} \left[ \log \frac{e^{f(x_{1},y)}}{\alpha m(y;x_{1:K}) + (1-\alpha)q(y)} \right]
$$

$$
- \mathbb{E}_{p(x_{1:K})p(y)} \left[ \frac{e^{f(x_{1},y)}}{\alpha m(y;x_{1:K}) + (1-\alpha)q(y)} \right] \triangleq I_{\alpha}. \quad (11)
$$

By interpolating between  $q(y)$  and  $m(y; x_{1:K})$ , we can recover  $I_{\text{NWJ}} (\alpha = 0)$  or  $I_{\text{NCE}} (\alpha = 1)$ . Unlike  $I_{\text{NCE}}$  which is

<sup>&</sup>lt;sup>2</sup>The derivation by [van den Oord et al.](#page-9-0)  $(2018)$  relied on an approximation, which we show is unnecessary.

<span id="page-4-0"></span>upper bounded by  $\log K$ , the interpolated bound is upper bounded by  $\log \frac{K}{\alpha}$ , allowing us to use  $\alpha$  to tune the tradeoff between bias and variance. We can maximize this lower bound in terms of  $q(y)$  and f. Note that unlike  $I_{NCE}$ , for  $\alpha > 0$  the last term does not vanish and we must sample  $y \sim p(y)$  independently from  $x_{1:K}$  to form a Monte Carlo approximation for that term. In practice we use a leave-oneout estimate, holding out an element from the minibatch for the independent  $y \sim p(y)$  in the second term. We conjecture that the optimal critic for the interpolated bound is achieved when  $f(x, y) = \log p(y|x)$  and  $q(y) = p(y)$  and use this choice when evaluating the accuracy of the estimates and gradients of  $I_{\alpha}$  with optimal critics.

## 2.5. Structured bounds with tractable encoders

In the previous sections we presented one variational upper bound and several variational lower bounds. While these bounds are flexible and can make use of any architecture or parameterization for the variational families, we can additionally take into account known problem structure. Here we present several special cases of the previous bounds that can be leveraged when the conditional distribution  $p(y|x)$ is known. This case is common in representation learning where  $x$  is data and  $y$  is a learned stochastic representation.

## InfoNCE with a tractable conditional.

An optimal critic for  $I_{NCE}$  is given by  $f(x, y) = \log p(y|x)$ , so we can simply use the  $p(y|x)$  when it is known. This gives us a lower bound on MI without additional variational parameters:

$$
I(X;Y) \geq \mathbb{E}\left[\frac{1}{K}\sum_{i=1}^{K}\log\frac{p(y_i|x_i)}{\frac{1}{K}\sum_{j=1}^{K}p(y_i|x_j)}\right],\quad(12)
$$

where the expectation is over  $\prod_j p(x_j, y_j)$ .

## Leave one out upper bound.

Recall that the variational upper bound (Eq. [1\)](#page-1-0) is minimized when our variational  $q(y)$  matches the true marginal distribution  $p(y) = \int dx p(x)p(y|x)$ . Given a minibatch of K  $(x_i, y_i)$  pairs, we can approximate  $p(y) \approx$  $\frac{1}{K} \sum_i p(y|x_i)$  [\(Chen et al.,](#page-8-0) [2018\)](#page-8-0). For each example  $x_i$ in the minibatch, we can approximate  $p(y)$  with the mixture over all other elements:  $q_i(y) = \frac{1}{K-1} \sum_{j \neq i} p(y|x_j)$ . With this choice of variational distribution, the variational upper bound is:

$$
I(X;Y) \leq \mathbb{E}\left[\frac{1}{K}\sum_{i=1}^{K}\left[\log\frac{p(y_i|x_i)}{\frac{1}{K-1}\sum_{j\neq i}p(y_i|x_j)}\right]\right]
$$
(13)

where the expectation is over  $\prod_i p(x_i, y_i)$ . Combining Eq. 12 and Eq. 13, we can sandwich MI without introducing learned variational distributions. Note that the only difference between these bounds is whether  $p(y_i|x_i)$  is included in the denominator. Similar mixture distributions

have been used in prior work but they require additional parameters [\(Tomczak & Welling,](#page-9-0) [2018;](#page-9-0) [Kolchinsky et al.,](#page-8-0) [2017\)](#page-8-0).

## Reparameterizing critics.

For  $I_{\text{NWJ}}$ , the optimal critic is given by  $1 + \log \frac{p(y|x)}{p(y)}$ , so it is possible to use a critic  $f(x, y) = 1 + \log \frac{p(y|x)}{q(y)}$  and optimize only over  $q(y)$  when  $p(y|x)$  is known. The resulting bound resembles the variational upper bound (Eq. [1\)](#page-1-0) with a correction term to make it a lower bound:

$$
I 
\geq 
\mathbb{E}_{p(x,y)} 
[ 
\log 
\frac{p(y|x)}{q(y)} 
] 
- 
\mathbb{E}_{p(y)} 
[ 
\frac{
\mathbb{E}_{p(x)} 
[ p(y|x) 
]}{q(y)} 
] 
+ 1
$$

$$
= R + 1 - 
\mathbb{E}_{p(y)} 
[ 
\frac{
\mathbb{E}_{p(x)} 
[ p(y|x) 
]}{q(y)} 
] 
(14)
$$

This bound is valid for any choice of  $q(y)$ , including unnormalized q.

Similarly, for the interpolated bounds we can use  $f(x, y) =$  $\log p(y|x)$  and only optimize over the  $q(y)$  in the denominator. In practice, we find reparameterizing the critic to be beneficial as the critic no longer needs to learn the mapping between  $x$  and  $y$ , and instead only has to learn an approximate marginal  $q(y)$  in the typically lower-dimensional representation space.

## Upper bounding total correlation.

Minimizing statistical dependency in representations is a common goal in disentangled representation learning. Prior work has focused on two approaches that both minimize lower bounds: (1) using adversarial learning [\(Kim & Mnih,](#page-8-0) [2018;](#page-8-0) [Hjelm et al.,](#page-8-0) [2018\)](#page-8-0), or (2) using minibatch approximations where again a lower bound is minimized [\(Chen et al.,](#page-8-0) [2018\)](#page-8-0). To measure and minimize statistical dependency, we would like an upper bound, not a lower bound. In the case of a mean field encoder  $p(y|x) = \prod_i p(y_i|x)$ , we can factor the total correlation into two information terms, and form a tractable upper bound. First, we can write the total correlation as:  $TC(Y) = \sum_i I(X; Y_i) - I(X; Y)$ . We can then use either the standard (Eq. [1\)](#page-1-0) or the leave one out upper bound (Eq. 13) for each term in the summation, and any of the lower bounds for  $I(X; Y)$ . If  $I(X; Y)$  is small, we can use the leave one out upper bound (Eq. 13) and  $I_{\text{NCE}}$ (Eq. 12) for the lower bound and get a tractable upper bound on total correlation without any variational distributions or critics. Broadly, we can convert lower bounds on mutual information into upper bounds on KL divergences when the conditional distribution is tractable.

## 2.6. From density ratio estimators to bounds

Note that the optimal critic for both  $I_{\text{NWJ}}$  and  $I_{\text{NCE}}$  are functions of the log density ratio  $\log \frac{p(y|x)}{p(y)}$ . So, given a log density ratio estimator, we can estimate the optimal critic and form a lower bound on MI. In practice, we find that

On Variational Bounds of Mutual Information

<span id="page-5-0"></span>Image /page/5/Figure/1 description: The image displays two rows of plots, each containing five subplots. The plots show the mutual information in 'nats' on the y-axis against the number of steps on the x-axis, ranging from 0 to 20,000. Each subplot has a black stepped line representing the true mutual information and a dashed black line representing log(bs). Two colored lines, 'Joint Critic' (blue) and 'Separable Critic' (orange), with shaded regions indicating variance, track the estimated mutual information over steps. The subplots are categorized by different methods: NWJ, \$\alpha\$=0.01, \$\alpha\$=0.50, \$\alpha\$=0.99, InfoNCE, and JS. The top row shows higher values for the true MI, while the bottom row shows lower values. The JS plot in the bottom row shows the 'Joint Critic' line closely following the true MI, while the 'Separable Critic' line is lower.

Figure 2. Performance of bounds at estimating mutual information. Top: The dataset  $p(x, y; \rho)$  is a correlated Gaussian with the correlation  $\rho$  stepping over time. Bottom: the dataset is created by drawing  $x, y \sim p(x, y; \rho)$  and then transforming y to get  $(Wy)^3$ where  $W_{ij} \sim \mathcal{N}(0, 1)$  and the cubing is elementwise. Critics are trained to maximize each lower bound on MI, and the objective (light) and smoothed objective (dark) are plotted for each technique and critic type. The single-sample bounds ( $I_{\text{NWJ}}$  and  $I_{\text{JS}}$ ) have higher variance than  $I_{NCE}$  and  $I_{\alpha}$ , but achieve competitive estimates on both datasets. While  $I_{NCE}$  is a poor estimator of MI with the small training batch size of 64, the interpolated bounds are able to provide less biased estimates than  $I_{NCE}$  with less variance than  $I_{NWI}$ . For the more challenging nonlinear relationship in the bottom set of panels, the best estimates of MI are with  $\alpha = 0.01$ . Using a joint critic (orange) outperforms a separable critic (blue) for  $I_{\text{NWJ}}$  and  $I_{\text{JS}}$ , while the multi-sample bounds are more robust to the choice of critic architecture.

training a critic using the Jensen-Shannon divergence (as in [Nowozin et al.](#page-9-0) [\(2016\)](#page-9-0); [Hjelm et al.](#page-8-0) [\(2018\)](#page-8-0)), yields an estimate of the log density ratio that is lower variance and as accurate as training with  $I<sub>NWI</sub>$ . Empirically we find that training the critic using gradients of  $I<sub>NWJ</sub>$  can be unstable due to the exp from the upper bound on the log partition function in the  $I<sub>NWJ</sub>$  objective. Instead, one can train a log density ratio estimator to maximize a lower bound on the Jensen-Shannon (JS) divergence, and use the density ratio estimate in  $I_{\text{NWJ}}$  (see Appendix [D](#page-12-0) for details). We call this approach  $I_{\text{JS}}$  as we update the critic using the JS as in [\(Hjelm et al.,](#page-8-0) [2018\)](#page-8-0), but still compute a MI lower bound with  $I<sub>NWI</sub>$ . This approach is similar to [\(Poole et al.,](#page-9-0) [2016;](#page-9-0) [Mescheder et al.,](#page-9-0) [2017\)](#page-9-0) but results in a bound instead of an unbounded estimate based on a Monte-Carlo approximation of the f-divergence.

# 3. Experiments

First, we evaluate the performance of MI bounds on two simple tractable toy problems. Then, we conduct a more thorough analysis of the bias/variance tradeoffs in MI estimates and gradient estimates given the optimal critic. Our goal in these experiments was to verify the theoretical results in Section 2, and show that the interpolated bounds can achieve better estimates of MI when the relationship between the variables is nonlinear. Finally, we highlight the utility of these bounds for disentangled representation learning on the dSprites datasets.

### Comparing estimates across different lower bounds.

We applied our estimators to two different toy problems,  $(1)$ a correlated Gaussian problem taken from [Belghazi et al.](#page-8-0) [\(2018\)](#page-8-0) where  $(x, y)$  are drawn from a 20-d Gaussian distribution with correlation  $\rho$  (see Appendix [B](#page-10-0) for details), and we vary  $\rho$  over time, and (2) the same as in (1) but we apply a random linear transformation followed by a cubic nonlinearity to y to get samples  $(x,(Wy)^3)$ . As long as the linear transformation is full rank,  $I(X;Y) = I(X;(WY)^3)$ . We find that the single-sample unnormalized critic estimates of MI exhibit high variance, and are challenging to tune for even these problems. In congtrast, the multi-sample estimates of  $I_{NCE}$  are low variance, but have estimates that saturate at log(batch size). The interpolated bounds trade off bias for variance, and achieve the best estimates of MI for the second problem. None of the estimators exhibit low variance *and* good estimates of MI at high rates, supporting the theoretical findings of [McAllester & Stratos](#page-9-0) [\(2018\)](#page-9-0).

Efficiency-accuracy tradeoffs for critic architectures. One major difference between the critic architectures used in [\(van den Oord et al.,](#page-9-0) [2018\)](#page-9-0) and [\(Belghazi et al.,](#page-8-0) [2018\)](#page-8-0) is the structure of the critic architecture. [van den Oord et al.](#page-9-0) [\(2018\)](#page-9-0) uses a separable critic  $f(x, y) = h(x)^T g(y)$  which requires only 2N forward passes through a neural network for a batch size of  $N$ . However, [Belghazi et al.](#page-8-0) [\(2018\)](#page-8-0) use a joint critic, where  $x, y$  are concatenated and fed as input to one network, thus requiring  $N^2$  forward passes. For both toy problems, we found that separable critics (orange)

Image /page/6/Figure/1 description: The image displays a grid of five plots arranged in columns, each representing a different method: NWJ, alpha = 0.05, alpha = 0.5, InfoNCE, and Minibatch Upper. Each column contains three rows of plots, showing Bias, Variance, and MSE on the y-axis, and Mutual Information or Batch Size on the x-axis. The Bias plots show values close to zero across all methods and batch sizes. The Variance and MSE plots are on a logarithmic scale, with different colored lines representing batch sizes of 8, 16, 64, 128, 256, and 512. In general, Variance and MSE tend to increase with Mutual Information or Batch Size, with some variations depending on the method and batch size.

Figure 3. Bias and variance of MI estimates with the optimal critic. While  $I_{\text{NWJ}}$  is unbiased when given the optimal critic,  $I_{\text{NCE}}$ can exhibit large bias that grows linearly with MI. The  $I_{\alpha}$  bounds trade off bias and variance to recover more accurate bounds in terms of MSE in certain regimes.

increased the variance of the estimator and generally performed worse than joint critics (blue) when using  $I<sub>NWJ</sub>$  or  $I_{\text{IS}}$  (Fig. [2\)](#page-5-0). However, joint critics scale poorly with batch size, and it is possible that separable critics require larger neural networks to get similar performance.

### Bias-variance tradeoff for optimal critics.

To better understand the behavior of different estimators, we analyzed the bias and variance of each estimator as a function of batch size given the *optimal critic* (Fig. 3). We again evaluated the estimators on the 20-d correlated Gaussian distribution and varied  $\rho$  to achieve different values of MI. While  $I_{\text{NWJ}}$  is an unbiased estimator of MI, it exhibits high variance when the MI is large and the batch size is small. As noted in [van den Oord et al.](#page-9-0) [\(2018\)](#page-9-0), the  $I_{NCE}$ estimate is upper bounded by log(batch size). This results in high bias but low variance when the batch size is small and the MI is large. In this regime, the absolute value of the bias grows linearly with MI because the objective saturates to a constant while the MI continues to grow linearly. In contrast, the  $I_{\alpha}$  bounds are less biased than  $I_{NCE}$  and lower variance than  $I<sub>NWI</sub>$ , resulting in a mean squared error (MSE) that can be smaller than either  $I_{\text{NWJ}}$  or  $I_{\text{NCE}}$ . We can also see that the leave one out upper bound (Eq. [13\)](#page-4-0) has large bias and variance when the batch size is too small.

### Bias-variance tradeoffs for representation learning.

To better understand whether the bias and variance of the estimated MI impact representation learning, we looked at the accuracy of the gradients of the estimates with respect to a stochastic encoder  $p(y|x)$  versus the true gradient of MI with respect to the encoder. In order to have access to ground truth gradients, we restrict our model to  $p_{\rho}(y_i|x_i) = \mathcal{N}(\rho_i x, \sqrt{1-\rho_i^2})$  where we have a separate

Image /page/6/Figure/8 description: The image displays three plots. The first two plots are line graphs showing Mean Squared Error (MSE) on the y-axis (on a logarithmic scale) against Mutual Information on the x-axis, ranging from 1 to 8. The top left plot is titled "NWJ" and shows several lines with varying colors, representing different values of alpha. The bottom left plot is also titled "NWJ" and shows similar lines, but is specifically for alpha = 0.05. The top right plot is titled "InfoNCE" and shows several lines with varying colors, representing different values of alpha. The bottom right plot is also titled "InfoNCE" and shows similar lines, but is specifically for alpha = 0.5. The third plot, on the right, is a heatmap titled "Optimal \alpha". The y-axis represents Batch Size on a logarithmic scale, and the x-axis represents Mutual Information from 1 to 8. The heatmap uses a color gradient to indicate the value of alpha, with a color bar on the right ranging from 0.0 to 1.0. The heatmap shows that for lower Mutual Information values (1-3), alpha is generally higher (greenish-yellow), and for higher Mutual Information values (4-8), alpha is lower (purple).

Figure 4. Gradient accuracy of MI estimators. Left: MSE between the true encoder gradients and approximate gradients as a function of mutual information and batch size (colors the same as in Fig. 3 ). Right: For each mutual information and batch size, we evaluated the  $I_{\alpha}$  bound with different  $\alpha$ s and found the  $\alpha$  that had the smallest gradient MSE. For small MI and small size,  $I_{NCE}$ -like objectives are preferred, while for large MI and large batch size,  $I<sub>NWJ</sub>$ -like objectives are preferred.

correlation parameter for each dimension  $i$ , and look at the gradient of MI with respect to the vector of parameters  $\rho$ . We evaluate the accuracy of the gradients by computing the MSE between the true and approximate gradients. For different settings of the parameters  $\rho$ , we identify which  $\alpha$  performs best as a function of batch size and mutual information. In Fig. 4, we show that the optimal  $\alpha$  for the interpolated bounds depends strongly on batch size and the true mutual information. For smaller batch sizes and MIs,  $\alpha$  close to 1 ( $I_{NCE}$ ) is preferred, while for larger batch sizes and MIs,  $\alpha$  closer to 0 ( $I<sub>NWJ</sub>$ ) is preferred. The reduced gradient MSE of the  $I_{\alpha}$  bounds points to their utility as an objective for training encoders in the InfoMax setting.

### 3.1. Decoder-free representation learning on dSprites

Many recent papers in representation learning have focused on learning latent representations in a generative model that correspond to human-interpretable or "disentangled" concepts [\(Higgins et al.,](#page-8-0) [2016;](#page-8-0) [Burgess et al.,](#page-8-0) [2018;](#page-8-0) [Chen et al.,](#page-8-0) [2018;](#page-8-0) [Kumar et al.,](#page-8-0) [2017\)](#page-8-0). While the exact definition of disentangling remains elusive [\(Locatello et al.,](#page-8-0) [2018;](#page-8-0) [Hig](#page-8-0)[gins et al.,](#page-8-0) [2018;](#page-8-0) [Mathieu et al.,](#page-9-0) [2018\)](#page-9-0), many papers have focused on reducing statistical dependency between latent variables as a proxy [\(Kim & Mnih,](#page-8-0) [2018;](#page-8-0) [Chen et al.,](#page-8-0) [2018;](#page-8-0) [Kumar et al.,](#page-8-0) [2017\)](#page-8-0). Here we show how a decoder-free information maximization approach subject to smoothness and independence constraints can retain much of the representation learning capabilities of latent-variable generative models on the dSprites dataset (a 2d dataset of white shapes on a black background with varying shape, rotation, scale, and position from [Matthey et al.](#page-9-0) [\(2017\)](#page-9-0)).

To estimate and maximize the information contained in the representation Y about the input X, we use the  $I_{\text{JS}}$  lower bound, with a structured critic that leverages the known stochastic encoder  $p(y|x)$  but learns an unnormalized variational approximation  $q(y)$  to the prior. To encourage independence, we form an upper bound on the total correlation of the representation,  $TC(Y)$ , by leveraging our novel variational bounds. In particular, we reuse the  $I_{\text{JS}}$  lower bound of  $I(X; Y)$ , and use the leave one out upper bounds (Eq. [13\)](#page-4-0) for each  $I(X; Y_i)$ . Unlike prior work in this area with VAEs, [\(Kim & Mnih,](#page-8-0) [2018;](#page-8-0) [Chen et al.,](#page-8-0) [2018;](#page-8-0) [Hjelm](#page-8-0) [et al.,](#page-8-0) [2018;](#page-8-0) [Kumar et al.,](#page-8-0) [2017\)](#page-8-0), this approach tractably estimates and removes statistical dependency in the representation without resorting to adversarial techniques, moment matching, or minibatch lower bounds in the wrong direction.

As demonstrated in [Krause et al.](#page-8-0) [\(2010\)](#page-8-0), information maximization alone is ineffective at learning useful representations from finite data. Furthermore, minimizing statistical dependency is also insufficient, as we can always find an invertible function that maintains the same amount of information and correlation structure, but scrambles the representation [\(Locatello et al.,](#page-8-0) [2018\)](#page-8-0). We can avoid these issues by introducing additional inductive biases into the representation learning problem. In particular, here we add a simple smoothness regularizer that forces nearby points in  $x$  space to be mapped to similar regions in  $y$  space:  $R(\theta) = KL(p_{\theta}(y|x)\|p_{\theta}(y|x + \epsilon))$  where  $\epsilon \sim \mathcal{N}(0, 0.5)$ .

The resulting regularized InfoMax objective we optimize is:

maximize  $I(X; Y)$  (15)

p(y|x)

subject to 
$$
TC(Y) = 
\sum_{i=1}^{K} I(X; Y_i) - I(X; Y) 
\le 
\delta
$$

$$
\mathbb{E}_{p(x)p(\epsilon)} [KL(p(y|x) \| p(y|x + \epsilon))] 
\le 
\gamma
$$

We use the convolutional encoder architecture from [Burgess](#page-8-0) [et al.](#page-8-0) [\(2018\)](#page-8-0); [Locatello et al.](#page-8-0) (2018) for  $p(y|x)$ , and a two hidden layer fully-connected neural network to parameterize the unnormalized variational marginal  $q(y)$  used by  $I_{\text{JS}}$ .

Empirically, we find that this variational regularized infomax objective is able to learn x and y position, and scale, but not rotation (Fig. 5, see [Chen et al.](#page-8-0) [\(2018\)](#page-8-0) for more details on the visualization). To the best of our knowledge, the only other decoder-free representation learning result on dSprites is [Pfau & Burgess](#page-9-0) [\(2018\)](#page-9-0), which recovers shape and rotation but not scale on a simplified version of the dSprites dataset with one shape.

## 4. Discussion

In this work, we reviewed and presented several new bounds on mutual information. We showed that our new interpolated bounds are able to trade off bias for variance to yield better estimates of MI. However, none of the approaches we considered here are capable of providing low-variance,

Image /page/7/Figure/9 description: The image is a 3x3 grid of plots. The rows are labeled Z0, Z1, and Z2 from top to bottom. The columns are labeled 'position', 'scale', and 'rotation' from left to right. The first column shows heatmaps. Z0 and Z1 heatmaps show a gradient from blue on the left to red on the right. The Z2 heatmap is a speckled pattern of red and blue dots. The second column shows three horizontal lines, one green, one orange, and one blue, for each row. The third column shows three noisy, fluctuating lines (green, orange, and blue) for each row. The Z2 row in the 'scale' column shows three downward sloping lines.

Figure 5. Feature selectivity on dSprites. The representation learned with our regularized InfoMax objective exhibits disentangled features for position and scale, but not rotation. Each row corresponds to a different active latent dimension. The first column depicts the position tuning of the latent variable, where the x and y axis correspond to x/y position, and the color corresponds to the average activation of the latent variable in response to an input at that position (red is high, blue is low). The scale and rotation columns show the average value of the latent on the  $y$  axis, and the value of the ground truth factor (scale or rotation) on the x axis.

low-bias estimates when the MI is large and the batch size is small. Future work should identify whether such estimators are impossible [\(McAllester & Stratos,](#page-9-0) [2018\)](#page-9-0), or whether certain distributional assumptions or neural network inductive biases can be leveraged to build tractable estimators. Alternatively, it may be easier to estimate gradients of MI than estimating MI. For example, maximizing  $I_{BA}$  is feasible even though we do not have access to the constant data entropy. There may be better approaches in this setting when we do not care about MI estimation and only care about computing gradients of MI for minimization or maximization.

A limitation of our analysis and experiments is that they focus on the regime where the dataset is infinite and there is no overfitting. In this setting, we do not have to worry about differences in MI on training vs. heldout data, nor do we have to tackle biases of finite samples. Addressing and understanding this regime is an important area for future work.

Another open question is whether mutual information maximization is a more useful objective for representation learning than other unsupervised or self-supervised approaches [\(Noroozi & Favaro,](#page-9-0) [2016;](#page-9-0) [Doersch et al.,](#page-8-0) [2015;](#page-8-0) [Dosovit](#page-8-0)[skiy et al.,](#page-8-0) [2014\)](#page-8-0). While deviating from mutual information maximization loses a number of connections to information theory, it may provide other mechanisms for learning features that are useful for downstream tasks. In future work, we hope to evaluate these estimators on larger-scale representation learning tasks to address these questions.

# <span id="page-8-0"></span>References

- Alemi, A. A., Fischer, I., Dillon, J. V., and Murphy, K. Deep variational information bottleneck. *arXiv preprint arXiv:1612.00410*, 2016.
- Alemi, A. A., Poole, B., Fischer, I., Dillon, J. V., Saurous, R. A., and Murphy, K. Fixing a broken elbo, 2017.
- Barber, D. and Agakov, F. The im algorithm: A variational approach to information maximization. In *NIPS*, pp. 201– 208. MIT Press, 2003.
- Belghazi, M. I., Baratin, A., Rajeshwar, S., Ozair, S., Bengio, Y., Hjelm, D., and Courville, A. Mutual information neural estimation. In *International Conference on Machine Learning*, pp. 530–539, 2018.
- Bell, A. J. and Sejnowski, T. J. An informationmaximization approach to blind separation and blind deconvolution. *Neural computation*, 7(6):1129–1159, 1995.
- Blei, D. M., Kucukelbir, A., and McAuliffe, J. D. Variational inference: A review for statisticians. *Journal of the American Statistical Association*, 112(518):859–877, 2017.
- Burgess, C. P., Higgins, I., Pal, A., Matthey, L., Watters, N., Desjardins, G., and Lerchner, A. Understanding disentangling in β-vae. *arXiv preprint arXiv:1804.03599*, 2018.
- Chen, T. Q., Li, X., Grosse, R., and Duvenaud, D. Isolating sources of disentanglement in variational autoencoders. *arXiv preprint arXiv:1802.04942*, 2018.
- Doersch, C., Gupta, A., and Efros, A. A. Unsupervised visual representation learning by context prediction. In *Proceedings of the IEEE International Conference on Computer Vision*, pp. 1422–1430, 2015.
- Donsker, M. D. and Varadhan, S. S. Asymptotic evaluation of certain markov process expectations for large time. iv. *Communications on Pure and Applied Mathematics*, 36 (2):183–212, 1983.
- Dosovitskiy, A., Springenberg, J. T., Riedmiller, M., and Brox, T. Discriminative unsupervised feature learning with convolutional neural networks. In *Advances in Neural Information Processing Systems*, pp. 766–774, 2014.
- Foster, A., Jankowiak, M., Bingham, E., Teh, Y. W., Rainforth, T., and Goodman, N. Variational optimal experiment design: Efficient automation of adaptive experiments. In *NeurIPS Bayesian Deep Learning Workshop*, 2018.

- Gabrie, M., Manoel, A., Luneau, C., Barbier, J., Macris, ´ N., Krzakala, F., and Zdeborová, L. Entropy and mutual information in models of deep neural networks. *arXiv preprint arXiv:1805.09785*, 2018.
- Gao, S., Ver Steeg, G., and Galstyan, A. Efficient estimation of mutual information for strongly dependent variables. In *Artificial Intelligence and Statistics*, pp. 277–286, 2015.
- Higgins, I., Matthey, L., Glorot, X., Pal, A., Uria, B., Blundell, C., Mohamed, S., and Lerchner, A. Early visual concept learning with unsupervised deep learning. *arXiv preprint arXiv:1606.05579*, 2016.
- Higgins, I., Amos, D., Pfau, D., Racanière, S., Matthey, L., Rezende, D. J., and Lerchner, A. Towards a definition of disentangled representations. *CoRR*, abs/1812.02230, 2018.
- Hjelm, R. D., Fedorov, A., Lavoie-Marchildon, S., Grewal, K., Trischler, A., and Bengio, Y. Learning deep representations by mutual information estimation and maximization. *arXiv preprint arXiv:1808.06670*, 2018.
- Hu, W., Miyato, T., Tokui, S., Matsumoto, E., and Sugiyama, M. Learning discrete representations via information maximizing self-augmented training. *arXiv preprint arXiv:1702.08720*, 2017.
- Kim, H. and Mnih, A. Disentangling by factorising. *arXiv preprint arXiv:1802.05983*, 2018.
- Kingma, D. P. and Welling, M. Auto-encoding variational bayes. *nternational Conference on Learning Representations*, 2013.
- Kolchinsky, A., Tracey, B. D., and Wolpert, D. H. Nonlinear information bottleneck. *arXiv preprint arXiv:1705.02436*, 2017.
- Kraskov, A., Stögbauer, H., and Grassberger, P. Estimating mutual information. *Physical review E*, 69(6):066138, 2004.
- Krause, A., Perona, P., and Gomes, R. G. Discriminative clustering by regularized information maximization. In *Advances in neural information processing systems*, pp. 775–783, 2010.
- Kumar, A., Sattigeri, P., and Balakrishnan, A. Variational inference of disentangled latent concepts from unlabeled observations. *arXiv preprint arXiv:1711.00848*, 2017.
- Locatello, F., Bauer, S., Lucic, M., Gelly, S., Schölkopf, B., and Bachem, O. Challenging common assumptions in the unsupervised learning of disentangled representations. *arXiv preprint arXiv:1811.12359*, 2018.

- <span id="page-9-0"></span>Ma, Z. and Collins, M. Noise contrastive estimation and negative sampling for conditional models: Consistency and statistical efficiency. *arXiv preprint arXiv:1809.01812*, 2018.
- Mathieu, E., Rainforth, T., Siddharth, N., and Teh, Y. W. Disentangling disentanglement in variational auto-encoders, 2018.
- Matthey, L., Higgins, I., Hassabis, D., and Lerchner, A. dsprites: Disentanglement testing sprites dataset. https://github.com/deepmind/dsprites-dataset/, 2017.
- McAllester, D. and Stratos, K. Formal limitations on the measurement of mutual information, 2018.
- Mescheder, L., Nowozin, S., and Geiger, A. Adversarial variational bayes: Unifying variational autoencoders and generative adversarial networks. *arXiv preprint arXiv:1701.04722*, 2017.
- Mnih, A. and Teh, Y. W. A fast and simple algorithm for training neural probabilistic language models. *arXiv preprint arXiv:1206.6426*, 2012.
- Moyer, D., Gao, S., Brekelmans, R., Galstyan, A., and Ver Steeg, G. Invariant representations without adversarial training. In *Advances in Neural Information Processing Systems*, pp. 9102–9111, 2018.
- Nemenman, I., Bialek, W., and van Steveninck, R. d. R. Entropy and information in neural spike trains: Progress on the sampling problem. *Physical Review E*, 69(5): 056111, 2004.
- Nguyen, X., Wainwright, M. J., and Jordan, M. I. Estimating divergence functionals and the likelihood ratio by convex risk minimization. *IEEE Transactions on Information Theory*, 56(11):5847–5861, 2010.
- Noroozi, M. and Favaro, P. Unsupervised learning of visual representations by solving jigsaw puzzles. In *European Conference on Computer Vision*, pp. 69–84. Springer, 2016.
- Nowozin, S., Cseke, B., and Tomioka, R. f-gan: Training generative neural samplers using variational divergence minimization. In *Advances in Neural Information Processing Systems*, pp. 271–279, 2016.
- Palmer, S. E., Marre, O., Berry, M. J., and Bialek, W. Predictive information in a sensory population. *Proceedings of the National Academy of Sciences*, 112(22):6908–6913, 2015.
- Paninski, L. Estimation of entropy and mutual information. *Neural computation*, 15(6):1191–1253, 2003.

- Peng, X. B., Kanazawa, A., Toyer, S., Abbeel, P., and Levine, S. Variational discriminator bottleneck: Improving imitation learning, inverse rl, and gans by constraining information flow. *arXiv preprint arXiv:1810.00821*, 2018.
- Pfau, D. and Burgess, C. P. Minimally redundant laplacian eigenmaps. 2018.
- Poole, B., Alemi, A. A., Sohl-Dickstein, J., and Angelova, A. Improved generator objectives for gans. *arXiv preprint arXiv:1612.02780*, 2016.
- Rainforth, T., Cornish, R., Yang, H., and Warrington, A. On nesting monte carlo estimators. In *International Conference on Machine Learning*, pp. 4264–4273, 2018.
- Reshef, D. N., Reshef, Y. A., Finucane, H. K., Grossman, S. R., McVean, G., Turnbaugh, P. J., Lander, E. S., Mitzenmacher, M., and Sabeti, P. C. Detecting novel associations in large data sets. *science*, 334(6062):1518–1524, 2011.
- Rezende, D. J., Mohamed, S., and Wierstra, D. Stochastic backpropagation and approximate inference in deep generative models. In *International Conference on Machine Learning*, pp. 1278–1286, 2014.
- Ryan, E. G., Drovandi, C. C., McGree, J. M., and Pettitt, A. N. A review of modern computational algorithms for bayesian optimal design. *International Statistical Review*, 84(1):128–154, 2016.
- Saxe, A. M., Bansal, Y., Dapello, J., Advani, M., Kolchinsky, A., Tracey, B. D., and Cox, D. D. On the information bottleneck theory of deep learning. In *International Conference on Learning Representations*, 2018.
- Tishby, N. and Zaslavsky, N. Deep learning and the information bottleneck principle. In *Information Theory Workshop (ITW), 2015 IEEE*, pp. 1–5. IEEE, 2015.
- Tishby, N., Pereira, F. C., and Bialek, W. The information bottleneck method. *arXiv preprint physics/0004057*, 2000.
- Tomczak, J. and Welling, M. Vae with a vampprior. In Storkey, A. and Perez-Cruz, F. (eds.), *Proceedings of the Twenty-First International Conference on Artificial Intelligence and Statistics*, volume 84 of *Proceedings of Machine Learning Research*, pp. 1214–1223, Playa Blanca, Lanzarote, Canary Islands, 09–11 Apr 2018. PMLR.
- van den Oord, A., Kalchbrenner, N., Espeholt, L., Vinyals, O., Graves, A., et al. Conditional image generation with pixelcnn decoders. In *Advances in Neural Information Processing Systems*, pp. 4790–4798, 2016.
- van den Oord, A., Li, Y., and Vinyals, O. Representation learning with contrastive predictive coding. *arXiv preprint arXiv:1807.03748*, 2018.

# <span id="page-10-0"></span>A. Summary of mutual information lower bounds

In Table 1, we summarize the characteristics of lower bounds on MI. The parameters and objectives used for each of these bounds is presented in Table 2.

| Lower Bound  |                            | L            | ∇L           | ⊥ BS         | Var.         | Norm.        |
|--------------|----------------------------|--------------|--------------|--------------|--------------|--------------|
| $I_{BA}$     | Barber & Agakov (2003)     | $\times$     | $\checkmark$ | $\checkmark$ | $\checkmark$ | $\times$     |
| $I_{DV}$     | Donsker & Varadhan (1983)  | $\times$     | $\times$     | -            | -            | -            |
| $I_{NWJ}$    | Nguyen et al. (2010)       | $\checkmark$ | $\checkmark$ | $\checkmark$ | $\times$     | $\checkmark$ |
| $I_{MINE}$   | Belghazi et al. (2018)     | $\times$     | $\checkmark$ | $\checkmark$ | $\times$     | $\checkmark$ |
| $I_{NCE}$    | van den Oord et al. (2018) | $\checkmark$ | $\checkmark$ | $\times$     | $\checkmark$ | $\checkmark$ |
| $I_{JS}$     | Appendix D                 | $\checkmark$ | $\checkmark$ | $\checkmark$ | $\times$     | $\checkmark$ |
| $I_{\alpha}$ | Eq. 11                     | $\checkmark$ | $\checkmark$ | $\times$     | $\checkmark$ | $\checkmark$ |

Table 1. Characterization of mutual information lower bounds. Estimators can have a tractable ( $\checkmark$ ) or intractable ( $\checkmark$ ) objective (L), tractable  $({\checkmark})$  or intractable  $({\checkmark})$  gradients  $(\nabla L)$ , be dependent  $({\checkmark})$  or independent  $({\checkmark})$  of batch size (⊥ BS), have high  $({\checkmark})$  or low  $({\checkmark})$ variance (Var.), and requires a normalized  $(X)$  vs unnormalized  $(\checkmark)$  critic (Norm.).

| Lower Bound       | Parameters                  | Objective                                                                                                                         |
|-------------------|-----------------------------|-----------------------------------------------------------------------------------------------------------------------------------|
| $I_{BA}$          | $q(x y)$ tractable decoder  | $\mathbb{E}_{p(x,y)}\left[\log q(x y)-\log p(x)\right]$                                                                           |
| $I_{\rm DV}$      | $f(x, y)$ critic            | $\mathbb{E}_{p(x,y)} [\log f(x,y)] - \log (\mathbb{E}_{p(x)p(y)} [f(x,y)])$                                                       |
| $I_{\text{NWJ}}$  | f(x,y)                      | $\mathbb{E}_{p(x,y)}\left[\log f(x,y)\right]-\frac{1}{e}\mathbb{E}_{p(x)p(y)}\left[f(x,y)\right]$                                 |
| $I_{\text{MINE}}$ | $f(x, y)$ , EMA( $\log f$ ) | $I_{\text{DV}}$ for evaluation, $I_{\text{TUBA}}(f, \text{EMA}(\log f))$ for gradient                                             |
| $I_{NCE}$         | f(x,y)                      | $\mathbb{E}_{p^{K}(x,y)}\left[\frac{1}{K}\sum_{i=1}^{K}\log\frac{f(y_{i},x_{i})}{\frac{1}{K}\sum_{i=1}^{K}f(y_{i},x_{j})}\right]$ |
| $I_{\rm JS}$      | f(x,y)                      | $INWJ$ for evaluation, $f$ -GAN JS for gradient                                                                                   |
| $I_{\text{TUBA}}$ | f(x, y), a(y) > 0           | $\mathbb{E}_{p(x,y)} [\log f(x,y)] - \mathbb{E}_{p(y)} \left[ \frac{\mathbb{E}_{p(x)} [f(x,y)]}{a(y)} + \log(a(y)) - 1 \right]$   |
| $I_{\text{TNCE}}$ | $e(y x)$ tractable encocder | $I_{NCE}$ with $f(x, y) = e(y x)$                                                                                                 |
| $I_{\alpha}$      | $f(x, y), \alpha, q(y)$     | $1 + \mathbb{E}_{p(x_{1:K},y)}\left[\log \frac{e^{f(x_{1},y)}}{\alpha m(y;x_{1:K}) + (1-\alpha)q(y)}\right]$                      |
|                   |                             | $-\mathbb{E}_{p(x_1,K)p(y)}\left[\frac{e^{f(x_1,y)}}{\alpha m(y;x_{1:K})+(1-\alpha)q(y)}\right]$                                  |

Table 2. Parameters and objectives for mutual information estimators.

## B. Experimental details

**Dataset**. For each dimension, we sampled  $(x_i, y_i)$  from a correlated Gaussian with mean 0 and correlation of  $\rho$ . We used a dimensionality of 20, i.e.  $x \in \mathbb{R}^{20}$ ,  $y \in \mathbb{R}^{20}$ . Given the correlation coefficient  $\rho$ , and dimensionality  $d = 20$ , we can compute the true mutual information:  $I(x, y) = -\frac{d}{2} \log(1 - \rho^2)$ . For Fig. [2,](#page-5-0) we increase  $\rho$  over time to show how the estimator behavior depends on the true mutual information.

Architectures. We experimented with two forms of architecture: separable and joint. Separable architectures independently mapped x and y to an embedding space and then took the inner product, i.e.  $f(x, y) = h(x)^T g(y)$  as in [\(van den Oord et al.,](#page-9-0) [2018\)](#page-9-0). Joint critics concatenate each x, y pair before feeding it into the network, i.e.  $f(x, y) = h([x, y])$  as in [\(Belghazi](#page-8-0) [et al.,](#page-8-0) [2018\)](#page-8-0). In practice, separable critics are much more efficient as we only have to perform 2N forward passes through neural networks for a batch size of N vs.  $N^2$  for joint critics. All networks were fully-connected networks with ReLU activations.

| <b>On Variational Bounds of Mutual Information</b> |  |  |
|----------------------------------------------------|--|--|
|----------------------------------------------------|--|--|

<span id="page-11-0"></span>

|                          | Mutual Information |     |     |     |      |
|--------------------------|--------------------|-----|-----|-----|------|
|                          | 2.0                | 4.0 | 6.0 | 8.0 | 10.0 |
| Gaussian, unstructured   |                    |     |     |     |      |
| $I_{\alpha}$             | 1.9                | 3.8 | 5.7 | 7.4 | 8.9  |
| $I_{NCE}$                | 1.9                | 3.6 | 4.9 | 5.7 | 6.0  |
| $I_{JS}$                 | 1.2                | 3.0 | 4.8 | 6.5 | 8.1  |
| $I_{NWJ}$                | 1.6                | 3.5 | 5.2 | 6.7 | 8.0  |
| Cubic, unstructured      |                    |     |     |     |      |
| $I_{\alpha}$             | 1.7                | 3.6 | 5.4 | 6.9 | 8.2  |
| $I_{NCE}$                | 1.7                | 3.2 | 4.1 | 4.6 | 4.8  |
| $I_{JS}$                 | 1.0                | 2.8 | 4.5 | 6.1 | 7.6  |
| $I_{NWJ}$                | 1.5                | 3.2 | 4.7 | 5.9 | 6.9  |
| Gaussian, known $p(y x)$ |                    |     |     |     |      |
| $I_{NCE}$ (Eq. 12)       | 1.9                | 3.3 | 4.2 | 4.6 | 4.8  |
| $I_{NWJ}$ (Eq. 14)       | 2.0                | 4.0 | 6.0 | 8.0 | 10.0 |

Table 3. Hyperparameter-optimizes results on the toy Gaussian and Cubic problem of Fig. [2.](#page-5-0)

# C. Additional experiments

## C.1. Exhaustive hyperparameter sweep.

To better evaluate the tradeoffs between different bounds, we performed more extensive experiments on the toy problems in Fig. [2.](#page-5-0) For each bound, we optimized over learning rate, architecture (separable vs. joint critic, number of hidden layers (1-3), hidden units per layer (256, 512, 1024, 2048), nonlinearity (ReLU or Tanh), and batch size (64, 128, 256, 512). In Table 3, we present the estimate of the best-performing hyperparameters for each technique. For both the Gaussian and Cubic problem,  $I_\alpha$  outperforms all approaches at all levels of mutual information between X and Y. While the absolute estimates are improved after this hyperparameter sweep, the ordering of the approaches is qualitatively the same as in Fig. [2.](#page-5-0) We also experimented with the bounds that leverage known conditional distribution, and found that Eq. [14](#page-4-0) that leverages a known  $p(y|x)$  is highly accurate as it only has to learn the marginal  $q(y)$ .

## C.2. Effective bias-variance tradeoffs with $I_{\alpha}$

To better understand the effectiveness of  $I_\alpha$  at trading off bias for variance, we plotted bias vs. variance for 3 levels of mutual information on the toy 20-dimensional Gaussian problem across a range of architecture settings. In Fig. 6, we see that  $I_\alpha$  is able to effectively interpolate between the high-bias low-variance  $I_{NCE}$ , and the low-bias high-variance  $I_{NWI}$ . We also find that  $I_{\text{JS}}$  is competitive at high rates, but exhibits higher bias and variance than  $I_{\alpha}$  at lower rates.

Image /page/11/Figure/8 description: The image displays three scatter plots, each titled with a different 'True MI' value: 4, 8, and 10. The x-axis for all plots is labeled 'Squared Bias' on a logarithmic scale, ranging from 10^-2 to 10^2. The y-axis for all plots is labeled 'Variance' on a logarithmic scale, ranging from 10^-3 to 10^4. Each plot contains four types of data points, indicated by a legend on the right: green circles labeled 'α', yellow diamonds labeled 'NCE', black triangles labeled 'NWJ', and purple squares labeled 'JS'. In all three plots, the 'NCE' data points are consistently located at the bottom right of the graph, near the x-axis. The 'α' data points are generally scattered across the plots, showing a trend of decreasing variance with increasing squared bias. The 'NWJ' and 'JS' data points are clustered in the upper left portions of the plots, with 'NWJ' points generally having higher variance than 'JS' points for similar squared bias values.

Figure 6.  $I_{\alpha}$  effectively interpolates between  $I_{NCE}$  and  $I_{NWJ}$ , trading off bias for variance.

In addition to  $I_\alpha$ , we compared to two alternative interpolation procedures, neither of which showed the improvements of  $I_{\alpha}$ :

1.  $I_{\alpha}$  interpolation: multisample bound that uses a critic with linear interpolation between the batch mixture  $m(y; x_{1:K})$ and the learned marginal  $q(y)$  in the denominator (Eqn. [11\)](#page-3-0).

<span id="page-12-0"></span>Image /page/12/Figure/1 description: Three scatter plots are displayed side-by-side, each with a logarithmic scale on both the x and y axes. The x-axis is labeled "Squared Bias" and ranges from 10^-2 to 10^2. The y-axis is labeled "Variance" and ranges from 10^-3 to 10^4. Each plot is titled "True MI =" followed by a number: 4, 8, and 10, respectively. Within each plot, there are three sets of data points represented by different markers and colors: green circles labeled "\alpha", blue crosses labeled "Product", and red plus signs labeled "Linear". The plots show the relationship between squared bias and variance for these three data types under different "True MI" conditions.

Figure 7. Comparing  $I_{\alpha}$  to other interpolations schemes.

- 2. Linear interpolation:  $\alpha I_{NCE} + (1 \alpha)I_{NWI}$
- 3. Product interpolation: same as  $I_\alpha$ , but uses the product  $m(y; x_{1:K})^\alpha q(y)^{(1-\alpha)}$  in the denominator.

We compared these approaches in the same setting as Fig. [6,](#page-11-0) evaluating the bias and variance for various hyperparameter settings at three different levels of mutual information. In Fig. 7, we can see that neither the product or linear interpolation approaches reduce the bias or variance as well as  $I_{\alpha}$ .

# D. $I_{\text{JS}}$ derivation

Given the high-variance of  $I_{\text{NWJ}}$ , optimizing the critic with this objective can be challenging. Instead, we can optimize the critic using the lower bound on Jensen-Shannon (JS) divergence as in GANs and [Hjelm et al.](#page-8-0) [\(2018\)](#page-8-0), and use the density ratio estimate from the JS critic to construct a critic for the KL lower bound.

The optimal critic for  $I_{\text{NWJ}}/f\text{-GAN KL}$  that saturates the lower bound on  $KL(p||q)$  is given by [\(Nowozin et al.,](#page-9-0) [2016\)](#page-9-0):

$$
T^*(x) = 1 + \log \frac{p(x)}{q(x)}.
$$

If we use the  $f$ -GAN formulation for parameterizing the critic with a softplus activation, then we can read out the density ratio from the real-valued logits  $V(x)$ :

$$
\frac{p(x)}{q(x)} \approx \exp(V(x))
$$

In [Poole et al.](#page-9-0) [\(2016\)](#page-9-0); [Mescheder et al.](#page-9-0) [\(2017\)](#page-9-0), they plug in this estimate of the density ratio into a Monte-Carlo approximation of the f-divergence. However, this is no longer a bound on the f-divergence, it is just an approximation. Instead, we can construct a critic for the KL divergence,  $T_{KL}(x) = 1 + V(x)$ , and use that to get a lower bound using the  $I_{\text{NWJ}}$  objective:

$$
KL(p||q) \geq \mathbb{E}_{x \sim p} \left[ T_{KL}(x) \right] - \mathbb{E}_{x \sim q} \left[ \exp(T_{KL}(x) - 1) \right] \tag{16}
$$

$$
= 1 + \mathbb{E}_{x \sim p} \left[ V(x) \right] - \mathbb{E}_{x \sim q} \left[ \exp(V(x)) \right] \tag{17}
$$

Note that if the log density ratio estimate  $V(x)$  is exact, i.e.  $V(x) = \log \frac{p(x)}{q(x)}$ , then the last term,  $\mathbb{E}_{x \sim q} [\exp(V(x))]$  will be one, and the first term is exactly  $KL(p||q)$ .

For the special case of mutual information estimation, p is the joint  $p(x, y)$  and q is the product of marginals  $p(x)p(y)$ , yielding:

$$
I(X;Y) \ge 1 + \mathbb{E}_{p(x,y)}\left[V(x,y)\right] - \mathbb{E}_{p(x)p(y)}\left[\exp(V(x,y))\right] \triangleq I_{\text{JS}}.\tag{18}
$$

# E. Alternative derivation of $I_{\text{TNCE}}$

In the main text, we derive  $I_{NCE}$  (and  $I_{TNCE}$ ) from a multi-sample variational lower bound. Here we present a simpler and more direct derivation of  $I_{\text{TNCE}}$ . Let  $p(x)$  be the data distribution, and  $p(x_{1:K})$  denote K samples drawn iid from  $p(x)$ . Let  $p(y|x)$  be a stochastic encoder, and  $p(y)$  be the intractable marginal  $p(y) = \int dx p(x)p(y|x)$ . First, we can write the mutual

information as a sum over K terms each of whose expectation is the mutual information:

$$
I(X; y) = \mathbb{E}_{x_{1:K}} \left[ \frac{1}{K} \sum_{i=1}^{K} \text{KL}(p(y|x_i) || p(y)) \right] = \mathbb{E}_{x_{1:K}} \left[ \frac{1}{K} \sum_{i=1}^{K} \int dy \, p(y|x_i) \log \frac{p(y|x_i)}{p(y)} \right]
$$
(19)

Let  $m(y; x_{1:K}) = \frac{1}{K} \sum_{i=1}^{K} p(y|x_i)$  be the minibatch estimate of the intractable marginal  $p(y)$ . We multiply and divide by  $m$  and then simplify:

$$
I(X; y) = \mathbb{E}_{x_{1:K}} \left[ \frac{1}{K} \sum_{i=1}^{K} \int dy \, p(y|x_i) \log \frac{p(y|x_i) m(y; x_{1:K})}{m(y; x_{1:K}) p(y)} \right]
$$
(21)

$$
= \mathbb{E}_{x_{1:K}} \left[ \frac{1}{K} \sum_{i=1}^{K} \left[ \int dy \, p(y|x_i) \log \frac{p(y|x_i)}{m(y; x_{1:K})} + \int dy \, p(y|x_i) \log \frac{m(y; x_{1:K})}{p(y)} \right] \right]
$$
(22)

$$
= \mathbb{E}_{x_{1:K}}\left[\frac{1}{K}\sum_{i=1}^{K}\text{KL}(p(y|x_i)\|m(y;x_{1:K})) + \int dy \frac{1}{K}\sum_{i=1}^{K}p(y|x_i)\log\frac{m(y;x_{1:K})}{p(y)}\right]
$$
(23)

$$
= \mathbb{E}_{x_{1:K}}\left[ \left( \frac{1}{K} \sum_{i=1}^{K} \text{KL}(p(y|x_i) \| m(y; x_{1:K})) \right) + \text{KL}(m(y; x_{1:K}) \| p(y)) \right]
$$
(24)

$$
\geq \mathbb{E}_{x_{1:K}}\left[\frac{1}{K}\sum_{i=1}^{K}\text{KL}(p(y|x_i)\| m(y; x_{1:K}))\right]
$$
\n(25)
## Sparse Parameterization for Epitomic Dataset Distillation

Xing Wei<sup>1</sup> <PERSON><PERSON><PERSON><sup>1</sup> <PERSON><PERSON><sup>1</sup> <PERSON><PERSON><PERSON><PERSON><sup>2</sup><sup>∗</sup>

<sup>1</sup>School of Software Engineering, Xi'an Jiaotong University <sup>2</sup>Shenzhen Institute of Advanced Technology, Chinese Academy <NAME_EMAIL> <EMAIL> {caoanjia7, moolink}@stu.xjtu.edu.cn

## Abstract

The success of deep learning relies heavily on large and diverse datasets, but the storage, preprocessing, and training of such data present significant challenges. To address these challenges, dataset distillation techniques have been proposed to obtain smaller synthetic datasets that capture the essential information of the originals. In this paper, we introduce a Sparse Parameterization for Epitomic datasEt Distillation (SPEED) framework, which leverages the concept of dictionary learning and sparse coding to distill epitomes that represent pivotal information of the dataset. SPEED prioritizes proper parameterization of the synthetic dataset and introduces techniques to capture spatial redundancy within and between synthetic images. We propose Spatial-Agnostic Epitomic Tokens (SAETs) and Sparse Coding Matrices (SCMs) to efficiently represent and select significant features. Additionally, we build a Feature-Recurrent Network (FReeNet) to generate hierarchical features with high compression and storage efficiency. Experimental results demonstrate the superiority of SPEED in handling high-resolution datasets, achieving state-of-the-art performance on multiple benchmarks and downstream applications. Our framework is compatible with a variety of dataset matching approaches, generally enhancing their performance. This work highlights the importance of proper parameterization in epitomic dataset distillation and opens avenues for efficient representation learning. Source code is available at <https://github.com/MIV-XJTU/SPEED>.

## 1 Introduction

Deep learning has achieved remarkable success across diverse domains, thanks to its ability to extract insightful representations from large and diverse datasets  $[1-5]$  $[1-5]$ . Nevertheless, the storage, preprocessing, and training of these massive datasets introduce significant challenges that strain storage and computational resources. In response to these challenges, dataset distillation techniques have arisen as a means to distill a more compact synthetic dataset that encapsulates the pivotal information of the original dataset. The central concept of dataset distillation is the extraction of an epitomic representation, capturing the core characteristics and patterns of the original dataset while minimizing storage demands. By doing so, deep learning models trained on the distilled dataset can attain performance levels similar to those trained on the original dataset but with significantly reduced storage and training costs. This approach opens new horizons for applications requiring cost-effective storage solutions and expedited training times, such as neural architecture search [\[6](#page-9-2)[–8\]](#page-10-0) and continual learning  $[9-11]$  $[9-11]$ .

The success of dataset distillation heavily relies on two essential factors: proper parameterization of the synthetic dataset and an effective design of the matching objective to align it with the original

<sup>∗</sup>Zhiheng Ma is the corresponding author.

<sup>37</sup>th Conference on Neural Information Processing Systems (NeurIPS 2023).

Image /page/1/Figure/0 description: This figure illustrates a deep learning architecture called Feature-Recurrent Network (FReeNet). The process begins with Sparse Coding Matrices (SCMs) and Spatial-Agnostic Epitomic Tokens (SAETs). These inputs are processed through a Feature-Recurrent Network (FReeNet) which includes a Concat & Linear layer, an MLP, and Add & Norm layers, with a recurrent connection. The output of FReeNet generates Synthetic Patches, which are then fed into a Matching Network along with Real Data. The Matching Network ultimately produces Gradient Feature Parameters. The SCMs are presented as a grid with numerical values, including 1.3, 1.1, 0.7, and 0.2, with other cells showing 0. The FReeNet block shows a loop with 'R' indicating recurrence. The Synthetic Patches are shown as a stack of images, and the Real Data is depicted as a collection of teddy bear images.

<span id="page-1-0"></span>Figure 1: **SPEED Overview.** We take spatial-agnostic epitomic tokens as the shared dictionary of the dataset and perform multi-head sparse combinations to synthesize instance-specific features. Subsequently, we utilize feature-recurrent blocks to generate hierarchical representations for non-linear synthesis of image patches, while reusing the sparse features. In this way, we sparsely parameterize the dataset, alleviating the storage burden and producing highly representative synthetic images.

dataset. While many dataset distillation methods have primarily focused on optimizing the matching objective [\[12](#page-10-3)[–18\]](#page-10-4), the critical aspect of synthetic dataset parameterization has often been overlooked. Typically, these methods employ a naive image-independent parameterization approach, where each learnable parameter basis (synthetic image) is optimized independently. Although some studies have recognized the inefficiency of image-independent parameterization and explored the mutual coherence and relationship between different synthetic images to improve compression efficiency [\[19–](#page-10-5)[22\]](#page-10-6), none of the previous methods have fully considered the spatial redundancy that exists within individual images and between different images.

In this paper, we present an efficient parameterization framework, named Sparse Parameterization for Epitomic datasEt Distillation (SPEED), which addresses the aforementioned limitations. SPEED leverages principles from representation learning paradigms, including convolutional neural network (CNN)  $[23, 1]$  $[23, 1]$  $[23, 1]$ , vision transformer (ViT)  $[3]$ , dictionary learning  $[24–30]$  $[24–30]$ , and sparse coding  $[31–36]$  $[31–36]$ .

SPEED introduces Spatial-Agnostic Epitomic Tokens (SAETs) that are shared among all synthetic image patches, and employs the Sparse Coding Matrices (SCMs) to select the most significant tokens. Subsequently, these selected tokens are assembled sequentially to form higher-level representations that facilitate the reconstruction of synthetic patches via non-linear mapping. To further minimize storage requirements, we propose a Feature-Recurrent Network (FReeNet) that utilizes recurrent blocks to generate hierarchical features, with SAETs and SCMs shared by all blocks, while leveraging a multi-head mechanism to enhance feature diversity.

In comparison to previous methods, our approach demonstrates significant advantages in handling high-resolution real-world datasets with substantial spatial redundancy. Notably, it achieves outstanding performance on ImageNet subsets, surpassing most of the previous state-of-the-art methods [\[21,](#page-10-10) [37\]](#page-11-2) by achieving an average improvement of 11.2% with 1 image per class storage space. Additionally, our sparse parameterization approach exhibits superior performance on unseen network architectures, outperforming previous state-of-the-art approaches [\[19,](#page-10-5) [21,](#page-10-10) [22\]](#page-10-6), and even surpassing our own full-element baseline, highlighting the potential of sparse representation for storage efficiency and improved generalization abilities.

Our method demonstrates competitive results across three standard dataset distillation benchmarks, such as surpassing the previous state-of-the-art  $[22]$  on CIFAR100 by 6.0% and on TinyImageNet by 10.9% when using 1 image per class storage space. It also exhibits strong performance on downstream applications, such as continual learning. Furthermore, our framework is compatible with multiple existing matching objectives [\[13,](#page-10-11) [16,](#page-10-12) [17\]](#page-10-13), generally enhancing their performance through the use of our sparse parameterization strategy.

In summary, our work highlights the importance of proper parameterization in epitomic dataset distillation and introduces the SPEED framework as a solution. We showcase its superiority in handling high-resolution datasets, achieving exceptional performance on benchmarks and downstream

applications. Our framework not only enhances storage efficiency but also improves generalization capabilities, opening new avenues for efficient representation learning in deep learning applications.

## 2 Method

The purpose of dataset distillation [\[12\]](#page-10-3) is to learn a synthetic dataset  $S = \{(\tilde{X}_i, y_i)\}_{i=1}^N$  that is much smaller in size than the original dataset  $\mathcal{T} = \{(X_i, y_i)\}_{i=1}^M$ , *i.e.*,  $N \ll M$ , while minimizing the loss of information. Formally, previous methods optimize the synthetic dataset by minimizing various matching objectives, all of which can be expressed using the following formulation:

$$
S^* = \underset{S}{\arg\min} \mathbb{E}_{\theta \sim \Theta} \Big[ \mathcal{D}(\varphi(\mathcal{T}, \theta), \varphi(\mathcal{S}, \theta)) \Big], \tag{1}
$$

where  $\Theta$  represents the distribution used for initializing the network parameters,  $\theta$  parameterize the training network,  $\mathcal{D}(\cdot, \cdot)$  is the dataset matching metric,  $\varphi(\cdot)$  maps the dataset to other informative spaces (e.g. gradient  $[13]$ , feature  $[17, 18]$  $[17, 18]$  $[17, 18]$ , and parameter spaces  $[16]$ ).

However, rather than exclusively focusing on matching objectives, this paper introduces a universal parameterization method for synthetic datasets that can be seamlessly integrated with most existing matching objectives. The naive parameterization method, which optimizes each synthetic image  $\overline{X}$ independently  $[12-14, 16-18]$  $[12-14, 16-18]$  $[12-14, 16-18]$ , fails to leverage shared information between images, resulting in unnecessary redundancy. In the following sections, we will present our parameterization framework, which decomposes the synthetic dataset into Spatial-Agnostic Epitomic Tokens, Sparse Coding Matrices, and a Feature-Recurrent Network. This framework significantly mitigates redundancy within and between images, irrespective of the spatial locations of features.

#### 2.1 Spatial-Agnostic Recurrent Parameterization

Taking inspiration from the Vision Transformer (ViT) [\[3\]](#page-9-3), we propose a more efficient parameterization approach applied at the patch level, utilizing cascade non-linear combinations to reduce complex and fine-level redundancy. To further minimize the storage footprint, we share Sparse Coding Matrices (SCMs) and Spatial-Agnostic Epitomic Tokens (SAETs) across all recurrent blocks. The main approach can be formulated as follows:

$$
\tilde{X}_i = \Phi_{\phi}(E, A_i). \tag{2}
$$

Here  $\tilde{X}_i = [\tilde{x}_i^{(1)}, \tilde{x}_i^{(2)}, ..., \tilde{x}_i^{(J)}] \in \mathbb{R}^{L \times J}$ , and  $\tilde{x}_i^j \in \mathbb{R}^L$  represents the *j*-th patch of  $\tilde{X}_i$ , which is flatten into a vector.  $L$  equals to the product of the patch height, width, and number of channels, and J is the total patch number. Similar to ViT, we divide the synthetic image into non-overlapping rectangular patches.  $E = [e_1, e_2, ..., e_K] \in \mathbb{R}^{D \times K}$  is the Spatial-Agnostic Epitomic Tokens (SAETs) shared by all synthetic patches, where  $D$  is the feature dimension, and  $K$  is the total number of tokens.  $A_i = [a_i^{(1)}, a_i^{(2)}, ..., a_i^{(J)}] \in \mathbb{R}^{K \times J}$  is the Sparse Coding Matrix (SCM) for the *i*-th synthetic image, where  $a_i^{(j)} \in \mathbb{R}^K$  is the specific coding vector for patch  $\tilde{x}_i^{(j)}$ .  $A_i$  will be further sparsified and saved in a storage-efficient format.  $\Phi_{\phi}(\cdot)$  is a non-linear recurrent transformer-style network that maps SAETs and the SCM to a synthetic image. Its learnable parameters are denoted as  $\phi$ . This network is referred to as the Feature-Recurrent Network (FReeNet), which is described in detail below.

Feature-Recurrent Network. In the Feature-Recurrent Network (FReeNet), each recurrent block shares the same SAETs and SCMs for the sake of parameter efficiency. However, this shared approach can lead to a lack of diversity in the resulting representations, as a single pool of SAETs must model multi-scale features. Drawing inspiration from the multi-head mechanism introduced in the transformer architecture, we introduce the concept of "multi-head" SAETs, aiming to strike a balance between storage efficiency and feature diversity. Initially, the original SAETs are split along the feature dimension to create multiple SAET pools, denoted as  $\{E^h\}_{h=1}^H$ , where  $E^h \in \mathbb{R}^{\frac{D}{H} \times K}$ . Additionally, each pool is assigned an independent SCM, denoted as  $\{A_i^h\}_{h=1}^H$ , where  $A_i^h \in \mathbb{R}^{K \times J}$ . The coding matrix  $A_i^h$  will undergo further sparsification to select the most significant tokens. We refer to the mechanism that combines the multi-head SAETs and SCMs as Multi-Head Sparse Coding (MHSC), which can be formulated as follows:

$$
\text{MHSC}_r\left(\{E^h\}_{h=1}^H, \{A_i^h\}_{h=1}^H\right) = W_r[E^1 A_i^1, E^2 A_i^2, ..., E^H A_i^H] + b_r,\tag{3}
$$

where  $W_r \in \mathbb{R}^{D \times D}$  is a linear projection and  $b_r$  is the bias, both of which are specific to each recurrent block and not shared across blocks. Using MHSC as the central component, we can construct the FReeNet in a recurrent manner, with SAETs and SCMs shared across different scales:

$$
Z'_{r} = \text{LN}_{r}^{1}(\text{MHSC}_{r}\left(\{E^{h}\}_{h=1}^{H}, \{A_{i}^{h}\}_{h=1}^{H}\right)) + Z_{r-1}), \quad r = 1, 2, ..., R,
$$
  
\n
$$
Z_{r} = \text{LN}_{r}^{2}(\text{MLP}_{r}(Z'_{r}) + Z'_{r}), \qquad r = 1, 2, ..., R,
$$
  
\n
$$
\tilde{X}_{i} = WZ_{R} + b,
$$
\n(4)

where R is the total number of recurrent blocks, and  $Z_R \in \mathbb{R}^{D \times J}$  is the output of the last block.  $Z_0$ is initialized with a zero matrix.  $W \in \mathbb{R}^{L \times D}$  and b make up the final linear projection layer, and  $\tilde{X}_i \in \mathbb{R}^{L \times J}$  is the output synthetic image, which is then rearranged into its original shape. MLP stands for the multi-layer perceptron. In our implementation, we set the MLP to have one hidden layer with the same dimension as the input, and incorporate layer normalization (LN) [\[38\]](#page-11-3) and residual connection [\[2\]](#page-9-4) in each block. It is worth noting that the parameters other than the SAETs and SCMs are not shared between different blocks to ensure that each block processes a different scale of features. Despite the use of shared SCMs across different blocks, our analysis demonstrates that SCMs still occupy the majority of the stored parameters. Therefore, we introduce a sparsification method to further enhance the storage efficiency of SCMs, motivated by the theories and techniques of sparse coding [\[31,](#page-11-0) [32\]](#page-11-4). In the subsequent section, we will reuse  $\tilde{X}_i = \Phi_{\phi}(\{E^h\}_{h=1}^H, \{A_i^h\}_{h=1}^H)$  to refer to the multi-head implementation of the FReeNet, without any ambiguity.

#### 2.2 Training Objective and Feature Sparsification

Since the  $\ell_0$  norm is not differentiable and difficult to optimize [\[39](#page-11-5)[–42\]](#page-11-6), we instead adopt the  $\ell_1$ norm as the sparsity penalty function. By promoting sparsity in solutions, it can effectively remove redundant features [\[40,](#page-11-7) [43–](#page-11-8)[45\]](#page-11-9). Our optimization objective can be expressed as follows:

<span id="page-3-0"></span>
$$
S = \{ (\Phi_{\phi}(\{E^{h}\}_{h=1}^{H}, \{A_{i}^{h}\}_{h=1}^{H}), y_{i}) \}_{i=1}^{N},
$$
  
arg min  
$$
\mathbb{E}_{\theta \sim \Theta} \left[ \mathcal{D}(\varphi(\mathcal{T}, \theta), \varphi(\mathcal{S}, \theta)) \right] + \lambda \sum_{i=1}^{N} \sum_{h=1}^{H} ||A_{i}^{h}||_{1},
$$
\n
$$
\{E^{h}\}_{h=1}^{H}, \{ \{A_{i}^{h}\}_{h=1}^{N}\}_{i=1}^{N}, \phi
$$
\n(5)

where  $|| \cdot ||_1$  is the  $\ell_1$  norm of a matrix,  $\lambda$  controls the amount of regularization. Using this approach, we decompose synthetic images into a multi-head SAET  $\{E^h\}_{h=1}^H$  and network parameters  $\phi$  that are shared by all synthetic images, and a multi-head SCM  $\{A_i^h\}_{h=1}^H$  for each synthetic image.

Feature Sparsification with Global Semantic Preservation. Sparse codes allow for the ranking of features [\[33,](#page-11-10) [46\]](#page-11-11), with higher coefficients indicating more important features. Therefore, we can select the most influential sub-parameters of SCM to achieve the desired sparsity  $||A_i^h||_0 \leq k$ . This process reduces storage inefficiency while preserving the global semantics of synthetic images. Moreover, all existing matching objectives [\[13,](#page-10-11) [16–](#page-10-12)[18\]](#page-10-4) optimize synthetic images using a specific training network, but these synthetic images are then used to train various agnostic network architectures. By pruning unnecessary features of synthetic images, we can further enhance their generalization ability on unseen network architectures. We experimentally validate this claim in Sec. [3.3.](#page-6-0) Specifically, given a learned SCM  $A \in \mathbb{R}^{K \times J}$ , we assign a binary mask  $B \in \mathbb{R}^{K \times J}$  to it:

<span id="page-3-1"></span>
$$
B[i,j] = \begin{cases} 1, & A[i,j] \in \text{topk}(\text{abs}(A)) \\ 0, & \text{otherwise} \end{cases}, \quad \bar{A} = B \odot A,
$$
 (6)

where topk( $\cdot$ ) obtains the largest k elements, and abs( $\cdot$ ) takes the absolute value of each element of the input matrix. We operate a Hadamard product on the two to preserve the top- $k$  efficient elements of the SCM, *i.e.*, select the most critical epitomic features. By operating on each learned SCM, we receive  $\{\{\vec{A}_i^h\}_{h=1}^H\}_{i=1}^N$ , which can be directly applied to synthesize images.

For the compressed storage of SCMs, we adopt the widely-used coordinate (COO) format, utilizing the uint8 data type to store the row and column coordinates of non-zero elements as the size of the sparse matrix is always smaller than or equal to  $256 \times 256$  in our implementation. Consequently, the storage needed for each non-zero element is 1.5 times that of a single float32 tensor. In this way, the storage complexity of SCMs can be greatly reduced from  $O(NHKJ)$  to  $O(NHK)$ , where  $k \ll KJ$ . The algorithm of our approach is summarized in Alg. [1.](#page-4-0)

#### Algorithm 1 Sparse Parameterization for Epitomic Dataset Distillation (SPEED).

<span id="page-4-0"></span>Input:  $T$ : original dataset; N: total number of synthetic images; H: number of heads; k: expected number of non-zero elements of SCM; SPARSIFY $(\cdot, \cdot)$ : feature sparsification.

1: Randomly initialize SAETs  $\{E^h\}_{h=1}^H$ , SCMs  $\{\{A_i^h\}_{h=1}^H\}_{i=1}^N$ , and the parameters  $\phi$  of FReeNet

- 2: for each distillation step... do
- 3: Get a random initialized training backbone for the matching strategy
- 4: Construct the synthetic dataset:  $S = \{ (\Phi_{\phi}(\lbrace E^h \rbrace_{h=1}^H, \lbrace A^h_i \rbrace_{h=1}^H), y_i) \}_{i=1}^N$ <br>5: Inner optimization of the training backbone with respect to the matching strategy (if necessary)
- 6: Compute the objective function combined matching loss with sparsity penalty as Eq. [\(5\)](#page-3-0)<br>7: Optimize  $\{E^h\}_{h=1}^H$ ,  $\{A^h_i\}_{h=1}^H$ ,  $\phi$  with respect to the objective function
- 7: Optimize  $\{E^h\}_{h=1}^H$ ,  $\{\{A_i^h\}_{h=1}^H\}_{i=1}^N$ ,  $\phi$  with respect to the objective function
- 8: end for
- 9: for  $i = 1$  to N do<br>10: for  $h = 1$  to H
- for  $h = 1$  to  $H$  do
- 11: Select most significant features:  $\bar{A}_i^h \leftarrow$  SPARSIFY( $A_i^h, k$ ), according to Eq. [\(6\)](#page-3-1)
- 12: Convert  $\bar{A}_i^h$  to compressed storage format
- 13: end for

```
14: end for
```

Output and Save:  $\{E^h\}_{h=1}^H$ ,  $\{\{\bar{A}_i^h\}_{h=1}^H\}_{i=1}^N$  and  $\phi$ .

Storage Analysis. Our storage budget is constrained by the upper bound determined by the storage requirements of the original synthetic images. Specifically, for a given budget of  $n$  images per class (IPC) [\[12,](#page-10-3) [13\]](#page-10-11) with c total classes, the maximum budget is limited to cnLJ, where LJ is the storage requirement for a single synthetic image. Therefore, the following inequality must be satisfied:

<span id="page-4-1"></span>SAETs:

 $\{E^h\}_{h=1}^H$ 
 $DK + 1.5NHk + R(3D^2 + 7D) + L(D+1) \leq cnLJ$ . (7)
SCMs:

 $\{\{\bar{A}_i^h\}_{h=1}^H\}_{i=1}^N$ FReeNet:  $\phi$ 

Budget: IPC = n

Noticing that SAETs and FReeNet are shared by all synthetic images, and  $1.5Hk \ll LJ$ , we can synthesize a much larger amount of synthetic images than the original one. For instance, considering the IPC 1 storage budget on CIFAR100, *i.e.*,  $c = 100$  and  $n = 1$ , we set  $D = 96$ ,  $K = 64$ ,  $H = 3$ ,  $k = 48$ ,  $R = 2$ ,  $L = 48$ , and  $J = 64$ , resulting in a more informative synthetic dataset with a size of  $N = 1100$  (11 final images for each class).

## 3 Experiments

In this section, we first evaluate our method and compare it with previous work. Then, we conduct generalization experiments and perform ablation studies. More results and detailed values for hyperparameters will be extensively discussed in the appendix. To quantify the performance and guarantee the fairness of the comparison, we use the default Conv-InstanceNorm-ReLU-AvgPool ConvNet with 128 channels as our training backbone, consistent with previous methods. We adopt trajectory matching [\[16\]](#page-10-12) as our default matching objective.

#### 3.1 Comparisons

Standard Benchmarks. We first conduct experiments on three standard dataset distillation bench-mark datasets: CIFAR10 [\[48\]](#page-11-12) and CIFAR100 [48] at a resolution of  $32 \times 32$ , and TinyImageNet [\[49\]](#page-11-13) at a resolution of  $64 \times 64$ . To adhere to the standard protocol, we employ a 3-layer ConvNet and a 4-layer ConvNet for training and evaluating CIFAR and TinyImageNet, respectively. As illustrated in Tab. [1,](#page-5-0) SPEED achieves highly competitive results on all three datasets. Remarkably, we achieve a test accuracy of 40.0% and 26.9% on CIFAR100 and TinyImageNet, respectively, using 1 image per class (IPC) storage space, representing improvements of 6.0% and 10.9% over the previous state-of-the-art [\[22\]](#page-10-6).

ImageNet Subsets. We evaluate the performance of SPEED on the high-resolution ImageNet [\[50\]](#page-11-14) subsets and achieve new state-of-the-art results, as shown in Tab. [2.](#page-5-1) Consistent with [\[16,](#page-10-12) [21\]](#page-10-10), we split

|          | Dataset              |                | CIFAR10        |                |                | CIFAR100       |                |                | TinyImageNet   |                |
|----------|----------------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
|          | IPC.                 | 1              | 10             | 50             |                | 10             | 50             | -1             | 10             | 50             |
|          | Random               | $14.4 \pm 2.0$ | $26.0 \pm 1.2$ | $43.4 \pm 1.0$ | $4.2 \pm 0.3$  | $14.6 \pm 0.5$ | $30.0 \pm 0.4$ | $1.4 \pm 0.1$  | $5.0 \pm 0.2$  | $15.0 \pm 0.4$ |
| Coreset  | Herding              | $21.5 \pm 1.3$ | $31.6 + 0.7$   | $40.4 + 0.6$   | $8.4 + 0.3$    | $17.3 + 0.3$   | $33.7 + 0.5$   | $2.8 + 0.2$    | $6.3 \pm 0.2$  | $16.7 + 0.3$   |
|          | K-Center             | $23.3 \pm 0.9$ | $36.4 \pm 0.6$ | $48.7 \pm 0.3$ | $8.6 \pm 0.3$  | $20.7 \pm 0.2$ | $33.6 \pm 0.4$ | $2.7 \pm 0.2$  | $7.8 \pm 0.4$  | $16.7 \pm 0.4$ |
|          | Forgetting           | $13.5 \pm 1.2$ | $23.3 \pm 1.0$ | $23.3 \pm 1.1$ | $4.5 \pm 0.3$  | $9.8 \pm 0.2$  |                | $1.6 \pm 0.1$  | $5.1 \pm 0.2$  | $15.0 \pm 0.3$ |
|          | DC[13]               | $28.3 \pm 0.5$ | $44.9 \pm 0.5$ | $53.9 \pm 0.5$ | $12.8 \pm 0.3$ | $25.2 \pm 0.3$ | $32.1 \pm 0.3$ |                |                |                |
|          | <b>DSA</b> [14]      | $28.8 \pm 0.7$ | $52.1 \pm 0.5$ | $60.6 \pm 0.5$ | $13.9 \pm 0.3$ | $32.3 \pm 0.3$ | $42.8 \pm 0.4$ |                |                |                |
|          | KIP $[47]$           | $49.9 \pm 0.2$ | $62.7 \pm 0.3$ | $68.6 \pm 0.2$ | $15.7 \pm 0.2$ | $28.3 \pm 0.1$ |                |                |                |                |
| Matching | <b>DM [17]</b>       | $26.0 \pm 0.8$ | $48.9 \pm 0.6$ | $63.0 \pm 0.4$ | $11.4 + 0.3$   | $29.7 + 0.3$   | $43.6 \pm 0.4$ | $3.9 \pm 0.2$  | $12.9 + 0.4$   | $24.1 \pm 0.3$ |
|          | TM [16]              | $46.3 \pm 0.8$ | $65.3 \pm 0.7$ | $71.6 \pm 0.2$ | $24.3 + 0.3$   | $40.1 \pm 0.4$ | $47.7 + 0.2$   | $8.8 \pm 0.3$  | $23.2 \pm 0.2$ | $28.0 \pm 0.3$ |
|          | FRePo [37]           | $46.8 \pm 0.7$ | $65.5 \pm 0.4$ | $71.7 \pm 0.2$ | $28.7 \pm 0.1$ | $42.5 \pm 0.2$ | $44.3 \pm 0.2$ | $15.4 \pm 0.3$ | $25.4 + 0.2$   |                |
|          | Parameters / Class   | 3.072          | 30.720         | 153,600        | 3,072          | 30,720         | 153,600        | 12,288         | 122,880        | 614.400        |
|          | IDC $[19]$           | $50.0 \pm 0.4$ | $67.5 + 0.5$   | $74.5 \pm 0.1$ |                | $44.8 \pm 0.2$ |                |                |                |                |
| Param.   | HaBa $[21]$          | $48.3 \pm 0.8$ | $69.9 + 0.4$   | $74.0 \pm 0.2$ | $33.4 + 0.4$   | $40.2 \pm 0.2$ | $47.0 + 0.2$   |                | ٠              |                |
|          | <b>RTP [22]</b>      | $66.4 \pm 0.4$ | $71.2 \pm 0.4$ | $73.6 \pm 0.5$ | $34.0 \pm 0.4$ | $42.9 \pm 0.7$ |                | $16.0 \pm 0.7$ |                |                |
|          | SPEED (Ours)         | $63.2 \pm 0.1$ | $73.5 \pm 0.2$ | $77.7 \pm 0.4$ | $40.0 \pm 0.4$ | $45.9 \pm 0.3$ | $49.1 \pm 0.2$ | $26.9 \pm 0.3$ | $28.8 \pm 0.2$ | $30.1 \pm 0.3$ |
|          | <b>Whole Dataset</b> |                | $84.8 \pm 0.1$ |                |                | $56.2 \pm 0.3$ |                |                | $37.6 \pm 0.4$ |                |

<span id="page-5-0"></span>Table 1: Comparisons with previous dataset distillation and coreset selection methods on standard benchmarks. "Matching" refers to dataset distillation methods with specific matching objectives. "Param." refers to dataset distillation methods with synthetic data parameterization. The bold numbers represent the highest accuracy achieved in each category.

| Dataset IPC        |        | ImageNette                     |                                | ImageWoof                      |                                | ImageFruit                     |                                | ImageMeow                      |                                | ImageSquawk                    |                                | <b>ImageYellow</b>             |                                |
|--------------------|--------|--------------------------------|--------------------------------|--------------------------------|--------------------------------|--------------------------------|--------------------------------|--------------------------------|--------------------------------|--------------------------------|--------------------------------|--------------------------------|--------------------------------|
|                    | 1      | 10                             | 1                              | 10                             | 1                              | 10                             | 1                              | 10                             | 1                              | 10                             | 1                              | 10                             |                                |
| TM [16]            |        | 47.7 $\pm$ 0.9                 | 63.0 $\pm$ 1.3                 | 28.6 $\pm$ 0.8                 | 35.8 $\pm$ 1.8                 | 26.6 $\pm$ 0.8                 | 40.3 $\pm$ 1.3                 | 30.7 $\pm$ 1.6                 | 40.4 $\pm$ 2.2                 | 39.4 $\pm$ 1.5                 | 52.3 $\pm$ 1.0                 | 45.2 $\pm$ 0.8                 | 60.0 $\pm$ 1.5                 |
| FRePo [37]         |        | <b>48.1<math>\pm</math>0.7</b> | <b>66.5<math>\pm</math>0.8</b> | <b>29.7<math>\pm</math>0.6</b> | <b>42.2<math>\pm</math>0.9</b> | -                              | -                              | -                              | -                              | -                              | -                              | -                              | -                              |
| Parameters / Class | 49,152 | 491,520                        | 49,152                         | 491,520                        | 49,152                         | 491,520                        | 49,152                         | 491,520                        | 49,152                         | 491,520                        | 49,152                         | 491,520                        |                                |
| HaBa [21]          |        | 51.9 $\pm$ 1.7                 | 64.7 $\pm$ 1.6                 | 32.4 $\pm$ 0.7                 | 38.6 $\pm$ 1.3                 | 34.7 $\pm$ 1.1                 | 42.5 $\pm$ 1.6                 | 36.9 $\pm$ 0.9                 | 42.9 $\pm$ 0.9                 | 41.9 $\pm$ 1.4                 | 56.8 $\pm$ 1.0                 | 50.4 $\pm$ 1.6                 | 63.0 $\pm$ 1.6                 |
| SPEED (Ours)       |        | <b>66.9<math>\pm</math>0.7</b> | <b>72.9<math>\pm</math>1.5</b> | <b>38.0<math>\pm</math>0.9</b> | <b>44.1<math>\pm</math>1.4</b> | <b>43.4<math>\pm</math>0.6</b> | <b>50.0<math>\pm</math>0.8</b> | <b>43.6<math>\pm</math>0.7</b> | <b>52.0<math>\pm</math>1.3</b> | <b>60.9<math>\pm</math>1.0</b> | <b>71.8<math>\pm</math>1.3</b> | <b>62.6<math>\pm</math>1.3</b> | <b>70.5<math>\pm</math>1.5</b> |
| Whole Dataset      |        | 87.4 $\pm$ 1.0                 | 67.0 $\pm$ 1.3                 |                                |                                | 63.9 $\pm$ 2.0                 |                                | 66.7 $\pm$ 1.1                 |                                | 87.5 $\pm$ 0.3                 |                                | 84.4 $\pm$ 0.6                 |                                |

<span id="page-5-1"></span>Table 2: Comparisons with previous methods on high-resolution ImageNet subsets.

ImageNet into 6 subsets, namely, ImageNette, ImageWoof, ImageFruit, ImageMeow, ImageSquawk, and ImageYellow, each consisting of 10 classes with resolutions of  $128 \times 128$ . And a 5-layer ConvNet is employed as the model for both training and evaluation.

Notably, our results achieved with IPC 1 storage space are highly competitive with the previous state-of-the-art results  $[21, 37]$  $[21, 37]$  $[21, 37]$  obtained with IPC 10. Specifically, we only need to employ 10% of their parameters to achieve similar or even better performance. Compared to the previous state-ofthe-art  $[21]$  using the same IPC 1 storage space, our approach exhibits an average improvement of 11.2% across all subsets. Moreover, we maintain a substantial lead for IPC 10 storage space. For instance, we achieve 71.8% accuracy on ImageSquawk, which is a 15.0% improvement over the previous state-of-the-art  $[21]$ . These outstanding outcomes are attributed to our design of sharing SAETs among patches, which enables SPEED to be more effective in reducing spatial redundancy as data resolution increases.

Continual Learning. Owing to its expressive representations and finer-grained image construction, SPEED has the ability to synthesize an informative dataset. Synthetic images of each class are of exceptional quality, thereby contributing to the dataset's overall richness and relevance. Following the DM [\[17\]](#page-10-13) setup based on GDumb [\[51\]](#page-11-16), we conduct continual learning experiments on CIFAR100 with IPC 20 storage space and use the default ConvNet and ResNet18 for evaluation. We randomly divide the 100 classes into 5 learning steps, that is, 20 classes per step. As illustrated in Fig. [2,](#page-6-1) SPEED maintains the highest test accuracy at all steps for both evaluation networks.

#### 3.2 Generalization

Universality to Matching Objectives. SPEED exhibits adaptability to multiple existing matching objectives, including those presented in [\[13,](#page-10-11) [16,](#page-10-12) [17\]](#page-10-13), and can be directly integrated with them. In line with [\[52\]](#page-11-17), we evaluate a diverse set of architectures, including the default ConvNet, ResNet [\[2\]](#page-9-4), MLP, and ViT [\[3\]](#page-9-3). The results and comparisons to corresponding baselines on CIFAR10 are presented in Tab. [3.](#page-6-2) For instance, when using MLP for evaluation under the IPC 1 budget, SPEED yields a

Image /page/6/Figure/0 description: The image displays two line graphs side-by-side, comparing the test accuracy of different methods (DM, HaBa, IDC, SPEED) against the number of classes. The left graph, labeled "(a) ConvNet", shows that for ConvNet, as the number of classes increases from 20 to 100, the test accuracy generally decreases for all methods. At 20 classes, SPEED has the highest accuracy at approximately 69%, followed by IDC at 66%, HaBa at 63%, and DM at 57%. At 100 classes, SPEED is at 44%, IDC is at 41%, HaBa is at 38%, and DM is at 30%. The right graph, labeled "(b) ResNet18", shows similar trends for ResNet18. At 20 classes, SPEED has the highest accuracy at approximately 65%, followed by IDC at 61%, HaBa at 59%, and DM at 49%. At 100 classes, SPEED is at 44%, IDC is at 42%, HaBa is at 38%, and DM is at 30%.

Image /page/6/Figure/1 description: The image displays two radar charts side-by-side, labeled (a) ConvNet and (b) ResNet18. Both charts have 12 axes representing different categories: FT, BS, ET, PIX, SP, GN, SN, IN, DB, GB, MB, and ZM. Each chart shows three data series: TM (green dots), HaBa (blue dots), and SPEED (orange dots). The radar chart for ConvNet shows SPEED values ranging from approximately 15 to 35, HaBa values ranging from approximately 10 to 20, and TM values ranging from approximately 5 to 15. The radar chart for ResNet18 shows SPEED values ranging from approximately 15 to 30, HaBa values ranging from approximately 5 to 20, and TM values ranging from approximately 5 to 15. A legend in the bottom right indicates that TM is represented by green circles, HaBa by blue circles, and SPEED by orange circles.

Figure 2: Test accuracy of continual learning. SPEED maintains the best performance on all steps.

<span id="page-6-4"></span><span id="page-6-2"></span><span id="page-6-1"></span>Figure 3: Robustness of the synthetic dataset on different architectures. Each direction represents each type of corruption.

|                     | Evaluation                         |                                                                                                       | ConvNet |                                         |                                                                                                                                                                                                      | MLP     |                                                        |                           | ResNet18 |                                                                                       |         | ViT                                                     |                                          |
|---------------------|------------------------------------|-------------------------------------------------------------------------------------------------------|---------|-----------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|--------------------------------------------------------|---------------------------|----------|---------------------------------------------------------------------------------------|---------|---------------------------------------------------------|------------------------------------------|
| Matching            | <b>IPC</b>                         |                                                                                                       | 10      | 50                                      |                                                                                                                                                                                                      | 10      | 50                                                     |                           | 10       | 50                                                                                    |         | 10                                                      | 50                                       |
| Gradient            | <b>DC</b> [13]<br>w. SPEED<br>Gain | $48.5 + 0.3$<br>$+19.2$                                                                               | $+1.6$  | $52.6 \pm 0.3$ $59.8 \pm 0.4$<br>$+3.0$ | $29.3 \pm 0.4$ $51.0 \pm 0.6$ $56.8 \pm 0.4$ $29.0 \pm 0.5$ $34.1 \pm 0.4$ $31.6 \pm 0.6$ $27.4 \pm 0.7$ $44.0 \pm 1.4$ $45.9 \pm 1.4$ $28.1 \pm 1.1$ $34.4 \pm 0.4$<br>$+6.5$                       | $+4.9$  | $35.5 \pm 0.2$ $39.0 \pm 0.4$ $41.5 \pm 0.3$<br>$+9.9$ | $42.5 \pm 0.5$<br>$+15.1$ | $+3.5$   | $47.5 \pm 0.5$ 55.0 $\pm$ 0.3<br>$+9.1$                                               | $+2.3$  | $30.4 \pm 0.6$ $38.4 \pm 0.4$<br>$+4.0$                 | $30.1 \pm 0.5$<br>$39.0 + 0.3$<br>$+8.9$ |
| <b>Distribution</b> | <b>DM</b> [17]<br>w. SPEED<br>Gain | $45.0 \pm 0.4$ 62.0 $\pm$ 0.3 66.4 $\pm$ 0.3 34.8 $\pm$ 0.3 42.2 $\pm$ 0.5 46.3 $\pm$ 0.4<br>$+18.5$  | $+14.4$ | $+4.4$                                  | $26.5 \pm 0.4$ $47.6 \pm 0.6$ $62.0 \pm 0.3$   $10.0 \pm 0.6$ $34.4 \pm 0.3$ $40.5 \pm 0.4$   $20.6 \pm 0.5$ $38.2 \pm 1.1$ $52.8 \pm 0.4$   $20.5 \pm 0.5$ $34.4 \pm 0.5$ $45.2 \pm 0.4$<br>$+24.8$ | $+7.8$  | $+5.8$                                                 | $+20.1$                   | $+19.4$  | $40.7 \pm 0.8$ 57.6 $\pm$ 0.5 65.2 $\pm$ 0.3 28.4 $\pm$ 0.6 43.2 $\pm$ 0.2<br>$+12.4$ | $+7.9$  | $+8.8$                                                  | $48.9 + 0.3$<br>$+3.7$                   |
| Traiectory          | <b>TM [16]</b><br>w. SPEED<br>Gain | $163.2 \pm 0.1$ $73.5 \pm 0.2$ $77.7 \pm 0.4$ $34.1 \pm 0.2$ $44.4 \pm 0.4$ $47.7 \pm 0.2$<br>$+19.0$ | $+9.8$  | $+7.4$                                  | $44.2 \pm 1.2$ $63.7 \pm 0.4$ $70.3 \pm 0.6$   $10.4 \pm 0.5$ $30.8 \pm 0.6$ $38.5 \pm 0.3$   $34.2 \pm 1.4$ $45.2 \pm 1.4$ $60.0 \pm 0.7$   $21.5 \pm 0.4$ $33.6 \pm 0.6$ $47.7 \pm 0.6$<br>$+23.7$ | $+13.6$ | $+9.2$                                                 | $53.9 \pm 0.7$<br>$+19.7$ | $+24.3$  | $69.5 \pm 0.4$ 76.4 $\pm$ 0.3<br>$+16.4$                                              | $+16.0$ | $37.5 \pm 0.8$ $51.5 \pm 0.3$ $54.5 \pm 0.5$<br>$+17.9$ | $+6.8$                                   |

Table 3: Universality to different matching objectives. SPEED is compatible with a variety of matching objectives and brings significant accuracy improvements over the corresponding baseline methods. All experiments are conducted with DSA augmentation [\[14\]](#page-10-14).

significant improvement of 24.8% and 23.7% in accuracy compared to the distribution [\[17\]](#page-10-13) and trajectory [\[16\]](#page-10-12) matching baselines, respectively. Our experiments reveal that the improvements in cross-architecture accuracy tend to be more significant than those on the ConvNet. For instance, with an IPC 10 budget for trajectory matching  $[16]$ , we achieve a 24.3% gain on ResNet18. This outcome further showcases the strengths of our sparse parameterization approach in terms of generalization.

Cross-Architecture Performance. The main purpose of dataset distillation is to distill a synthetic dataset that is effective on various even unseen architectures. In this study, we evaluate the cross-architecture performance of our method by comparing it with previous synthetic data parameterization approaches [\[19,](#page-10-5) [21,](#page-10-10) [22\]](#page-10-6) on CIFAR10, using IPC 10 storage space. The results presented in Tab. [4](#page-6-3) demonstrate that SPEED continues to

| Method       | ConvNet        | MLP            | ResNet18       | ViT            |
|--------------|----------------|----------------|----------------|----------------|
| IDC [19]     | $67.5 \pm 0.5$ | $41.4 \pm 0.2$ | $62.9 \pm 0.6$ | $47.9 \pm 0.8$ |
| HaBa [21]    | $69.9 \pm 0.4$ | $35.4 \pm 0.4$ | $60.2 \pm 0.9$ | $42.2 \pm 0.6$ |
| RTP [22]     | $71.2 \pm 0.4$ | $27.2 \pm 0.2$ | $67.5 \pm 0.1$ | $35.7 \pm 0.4$ |
| <b>SPEED</b> | $73.5 \pm 0.2$ | $44.4 \pm 0.4$ | $69.5 \pm 0.4$ | $51.5 \pm 0.3$ |

<span id="page-6-3"></span>Table 4: Comparision of cross-architecture generalization evaluation. SPEED exhibits outstanding leads on all architectures.

outperform other methods significantly in terms of generalization across unseen architectures. As an illustration, our method achieves an accuracy of  $51.5\%$  on ViT, which represents a  $3.6\%$  improvement over the previous state-of-the-art [\[19\]](#page-10-5). Although various existing dataset distillation matching objectives tend to overfit the training network, we address this challenge by pruning unnecessary features through sparse parameterization.

Robustness to Corruption. To explore the out-of-domain generalization of our synthetic dataset, we conduct experiments on CIFAR100-C [\[53\]](#page-11-18). In detail, we evaluate on ConvNet and ResNet18, using the synthetic dataset trained under the IPC 1 budget. Fig. [3](#page-6-4) shows the average accuracy of 14 types of corruption under 5 levels respectively. Compared with the previous methods, we achieve better performance under all kinds of corruption. Especially on ResNet18, SPEED outperforms previous methods significantly, achieving almost *double* the test accuracy under every corruption scenario. This demonstrates the generalization and robustness benefits brought by sparse parameterization.

#### <span id="page-6-0"></span>3.3 Ablation Study

Size of  $k$  for Feature Sparsification. Our total storage parameters are positively correlated with the size of  $k$ , and the representation ability of the SCM is also related to this  $k$ -winner. Therefore, we

Image /page/7/Picture/0 description: The image displays two grids of bird images, side-by-side. Each grid contains 10 rows and 10 columns of small, pixelated images of various birds. The birds appear to be categorized by type, with rows dedicated to specific species or groups. For instance, the top rows seem to feature peacocks and flamingos, followed by parrots, then penguins, eagles, ostriches, and finally smaller birds like ducks and swans. The overall impression is a collection of generated or sampled bird images, possibly from a machine learning model.

(a) Before feature sparsification (b) After feature sparsification (density  $= 0.3\%$ )

<span id="page-7-1"></span>Figure 4: ImageSquawk: synthetic data samples before and after feature sparsification. The process of sparsification does not lead to a significant loss of essential information, resulting in a high degree of similarity between the two images.

| k    | # Param     | Budget       | ConvNet                         | MLP                             | ResNet18                        | ViT                             |
|------|-------------|--------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|
| full | 15M         | -            | 74.0 $\pm 0.2$                  | 44.8 $\pm 0.3$                  | 69.0 $\pm 0.4$                  | 50.8 $\pm 0.5$                  |
| 96   | 575K        | -            | <b>74.6<math>\pm 0.3</math></b> | <b>44.9<math>\pm 0.1</math></b> | 69.3 $\pm 0.7$                  | <b>51.8<math>\pm 0.5</math></b> |
| 48   | 307K        | $\checkmark$ | 73.5 $\pm 0.2$                  | 44.4 $\pm 0.4$                  | <b>69.5<math>\pm 0.4</math></b> | 51.5 $\pm 0.3$                  |
| 24   | 173K        | $\checkmark$ | 68.2 $\pm 0.3$                  | 35.9 $\pm 0.7$                  | 66.4 $\pm 0.5$                  | 45.2 $\pm 0.4$                  |
| 12   | <b>106K</b> | $\checkmark$ | 57.8 $\pm 0.3$                  | 24.8 $\pm 0.4$                  | 59.0 $\pm 0.4$                  | 36.7 $\pm 0.9$                  |

<span id="page-7-0"></span>Table 5: Different  $k$  for feature sparsification.

<span id="page-7-4"></span><span id="page-7-3"></span>Table 6: Depth of FReeNet.

<span id="page-7-2"></span>

| K         | <i>N/c</i> | ConvNet         | ResNet18        | D         | <i>N/c</i> | ConvNet         | ResNet18        | H        | <i>N/c</i> | ConvNet         | ResNet18        |
|-----------|------------|-----------------|-----------------|-----------|------------|-----------------|-----------------|----------|------------|-----------------|-----------------|
| 32        | 11         | 38.5±0.2        | 29.8±0.5        | 48        | 13         | 38.0±0.1        | 29.0±0.4        | 1        | 33         | 38.7±0.1        | 30.0±0.4        |
| <b>64</b> | <b>11</b>  | <b>40.0±0.4</b> | <b>29.9±0.3</b> | 72        | 12         | 38.7±0.3        | 29.8±0.5        | 2        | 16         | 39.3±0.4        | 30.5±0.3        |
| 96        | 10         | 37.3±0.1        | 29.3±0.4        | <b>96</b> | <b>11</b>  | <b>40.0±0.4</b> | <b>29.9±0.3</b> | <b>3</b> | <b>11</b>  | <b>40.0±0.4</b> | <b>29.9±0.3</b> |
| 128       | 10         | 38.2±0.3        | 29.6±0.4        | 144       | 7          | 35.5±0.4        | 26.7±0.3        | 4        | 8          | 35.9±0.4        | 27.4±0.8        |

discuss the preference for the value of k. Specifically, for cross-architecture evaluation on CIFAR10 with IPC 10 storage space, we set k to  $\{4096, 96, 48, 24, 12\}$ , as shown in Tab. [5](#page-7-0) (Budget denotes whether the storage budget is met). We observe that taking a moderate value of  $k$  can effectively improve the cross-architecture generalization ability of the synthetic dataset while maintaining high test accuracy on the homogeneous network, *i.e.*, the training backbone. When k is set to 96, the overall accuracy exceeds that of the result obtained without feature sparsification, thereby demonstrating the effective removal of inefficient features and the improvement in generalization. In order to meet the storage budget (10  $\times$  10  $\times$  48  $\times$  64  $\approx$  307K), we finally set k to 48, which maintains competitive performance.

Feature Sparsification Visualizations. To compare the representation capabilities of SCMs after feature sparsification, we provide some visualization results on ImageSquawk, as shown in Fig. [4.](#page-7-1) We note that the visualization results using only the sparsified features are highly similar to the corresponding original ones. Differences are noticeable only in a few patches, which are insignificant for classification and mainly consist of meaningless backgrounds. This implies that synthetic images can be constructed effectively using only a few prominent features.

Trade-off between Quality and Quantity. Eq. [\(7\)](#page-4-1) illustrates that SPEED's parameters consist of three parts. The parameters of SAETs and FReeNet are shared among all synthetic images, while the parameters of SCMs are specific to each synthetic image. Thus, under the same budget, increasing shared parameters will lead to a decrease in the number of synthetic images. We conduct ablation experiments on CIFAR100 with IPC 1 storage space to study the trade-off between quality (more shared parameters) and quantity (more synthetic images). Tab. [7-](#page-7-2)[9](#page-7-3) study the size of SAETs. As can be seen, keeping a moderate number of synthetic images is important, *i.e.*,  $N/c > 10$ . A lack of diversity caused by too few images can impact the performance. However, once a certain number of images is reached, further increasing the quantity can lead to a decrease in the quality of each image and slightly reduce performance. In general, our performance is insensitive to the size of SAETs

and outperforms the previous state-of-the-art [\[22\]](#page-10-6) in multiple settings, except when the number of synthetic images is too small. Tab. [6](#page-7-4) studies the depth of FReeNet, we conduct ablation experiments on TinyImageNet with IPC 1 storage space. Although using only one block can maximize the number of synthetic images per class  $(N/c)$ , the lack of hierarchical features severely compromises the model's representational capacity, resulting in poor image quality. However, having too many blocks can result in increased reconstruction overhead. We find that FReeNet with two blocks already achieves a great compromise between performance and computation cost.

In line with the practices of previous methods that handle Table 10: Effects of increasing image Effects of Increasing Image Resolution. To study the effectiveness of our method in higher resolution scenarios, we performed experimental investigations on both the distribution matching [\[17\]](#page-10-13) baseline and our method, using the ImageNette dataset with image sizes of  $128 \times 128$  and  $256 \times 256$ . higher resolution images with deeper networks, we increased the depth of the ConvNet to 6 for the  $256\times256$  image size.

|                | $128 \times 128$ | $256 \times 256$ | Gain   |
|----------------|------------------|------------------|--------|
| <b>DM</b> [17] | $28.6  \pm 0.6$  | $29.5  \pm 1.1$  | $+0.9$ |
| w. SPEED       | $53.5  \pm 1.2$  | $57.7  \pm 0.9$  | $+4.2$ |
| Gain           | $+24.9$          | $+28.2$          | $+3.3$ |

<span id="page-8-0"></span>resolution on ImageNette.

As shown in Tab. [10,](#page-8-0) when the resolution increases from  $128 \times 128$  to  $256 \times 256$ , the gain brought by SPEED to the baseline also amplifies from 24.9% to 28.2%. The results demonstrate that our method achieves more substantial improvements when applied to higher-resolution images.

## 4 Related Work

Dataset Distillation. Dataset distillation, proposed by Wang *et al.* [\[12\]](#page-10-3), aims to learn a smaller synthetic dataset so that the test performance of the model on the synthetic dataset is similar to that of the original dataset. For better matching objectives, Zhao *et al.* [\[13\]](#page-10-11) present a single-step matching framework, encouraging the result gradient of the synthetic dataset and the original dataset to be similar, further extended by [\[15,](#page-10-15) [19,](#page-10-5) [54,](#page-11-19) [55\]](#page-12-0). Subsequently, Cazenavette *et al.* introduce TM [\[16\]](#page-10-12) to alleviate the cumulative error problem of single-step matching, which inspires a series of work [\[56–](#page-12-1) [58\]](#page-12-2). To avoid the expensive computational overhead brought by complex second-level optimization, Zhao *et al.* [\[17\]](#page-10-13) suggest DM, a distribution matching approach, and Wang *et al.* [\[18\]](#page-10-4) explicitly align the synthetic and real distributions in the feature space of a downstream network. There are also some methods based on kernel ridge regression [\[59,](#page-12-3) [47,](#page-11-15) [60,](#page-12-4) [37\]](#page-11-2), which can bring out a closed-form solution for the linear model, avoiding extensive inner loop training.

In terms of synthetic data parameterization, Kim *et al.* introduce IDC [\[19\]](#page-10-5), using downsampling strategies to synthesize more images under the same storage budget. Zhao *et al.* [\[20\]](#page-10-16) propose to synthesize informative data via GAN [\[61,](#page-12-5) [62\]](#page-12-6). Deng *et al.* [\[22\]](#page-10-6) have explored how to compress datasets into bases and recall them by linear combinations. Liu *et al.* [\[21\]](#page-10-10) propose a dataset factorization approach, utilizing image bases and hallucinators for image synthesis. Lee *et al.* [\[63\]](#page-12-7) further factorize the dataset into latent codes and decoders. These parameterization methods are designed to find shareable or low-resolution image bases. Still, they either only consider the connections between synthetic images [\[21,](#page-10-10) [22,](#page-10-6) [63\]](#page-12-7), or do not reduce the redundancy inside the synthetic image thoroughly [\[19\]](#page-10-5). So the storage space of their image bases is still proportional to the dataset resolution, bringing out unsatisfactory performance on high-resolution datasets. SPEED is a universal synthetic data parameterization framework, in which the distillation and construction are spatial-agnostic. It allows for joint modeling correlations between and within the synthetic images, leading to lower storage redundancy and finer-grained image synthesis, thus performing competently on high-resolution datasets.

Sparse Coding. Sparse coding calls for constructing efficient representations of data as a combination of a few high-level patterns  $\left[31-36\right]$ . It has been proven to be an effective approach in the field of image reconstruction and image classification  $[64–70]$  $[64–70]$ . Typically, a dictionary of basis functions (e.g. wavelets [\[71\]](#page-12-10) or curvelets [\[72\]](#page-12-11)) is used to decompose the image patches into coefficient vectors. By imposing sparsity constraints on the coefficient vectors, efficient sparse representations of the image patches can be obtained. In addition, the dictionary is not necessarily fixed, it can also be learned to adapt to different tasks  $[25-28]$  $[25-28]$ , and the penalty function can also be varied  $[33, 73, 74]$  $[33, 73, 74]$  $[33, 73, 74]$  $[33, 73, 74]$  $[33, 73, 74]$ . Prior sparse coding research has primarily focused on compressing individual images [\[75,](#page-12-14) [76\]](#page-12-15), with an emphasis on achieving high compression ratios while minimizing perceptual distortion. However, there has been limited exploration of how to apply sparse coding to compress entire datasets in a

way that enhances the training of downstream neural networks. Our work demonstrates that theories and techniques in sparse coding can provide valuable inspiration for developing dataset distillation methods.

Coreset Selection. Coreset selection identifies a representative subset of the original dataset [\[77–](#page-13-0) [79,](#page-13-1) [10\]](#page-10-19). Its objective is in line with the goals of dataset distillation and can be utilized to tackle challenges such as continual learning  $[10, 80, 81]$  $[10, 80, 81]$  $[10, 80, 81]$  $[10, 80, 81]$  $[10, 80, 81]$  and active learning tasks  $[82]$ . This technique typically performs better when the storage budget is relatively large, while dataset distillation demonstrates superior performance under extremely limited storage budgets [\[52\]](#page-11-17).

## 5 Conclusion and Limitations

In this paper, we introduce SPEED, an efficient and generalizable solution for dataset distillation that offers the following merits: First, the spatial-agnostic epitomic tokens distilled by our method are not only shared between the different classes but also shared among patches of every synthetic image, regardless of their spatial locations. Such efficient modeling enables us to perform well on high-resolution datasets with much less spatial redundancy. Second, the proposed feature-recurrent network promotes hierarchical representation in an efficient recurrent manner, resulting in more informative synthetic data. Finally, the proposed feature sparsification mechanism improves both the storage efficiency and the generalization ability. SPEED achieves outstanding performance on various datasets and architectures through extensive experiments.

**Limitations.** Similar to the previous parameterization methods  $[19–22]$  $[19–22]$ , decomposing the original synthetic datasets into different components will slightly increase distilling costs and incur reconstruction overhead. We alleviate this issue by designing the network to be as lightweight as possible. While the dataset synthesized by SPEED offers better privacy protection for users compared to the original dataset, there remains a possibility of privacy leakage.

## Acknowledgements

This work was supported in part by the National Natural Science Foundation of China under Grant 62006183 and Grant 62206271, in part by the National Key Research and Development Project of China under Grant 2020AAA0105600, and in part by the Shenzhen Key Technical Projects under Grant JSGG20220831105801004, CJGJZD2022051714160501, and JCYJ20220818101406014.

## References

- <span id="page-9-0"></span>[1] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. In *NeurIPS*, 2012.
- <span id="page-9-4"></span>[2] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, 2016.
- <span id="page-9-3"></span>[3] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. In *ICLR*, 2021.
- [4] Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. In *NeurIPS*, 2020.
- <span id="page-9-1"></span>[5] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, et al. Learning transferable visual models from natural language supervision. In *ICML*, 2021.
- <span id="page-9-2"></span>[6] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth O. Stanley, and Jeff Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *ICML*, 2020.
- [7] Liam Li and Ameet Talwalkar. Random search and reproducibility for neural architecture search. In *UAI*, 2020.

- <span id="page-10-0"></span>[8] Thomas Elsken, Jan Hendrik Metzen, and Frank Hutter. Neural architecture search: A survey. *The Journal of Machine Learning Research*, 20(1):1997–2017, 2019.
- <span id="page-10-1"></span>[9] Ian J Goodfellow, Mehdi Mirza, Da Xiao, Aaron Courville, and Yoshua Bengio. An empirical investigation of catastrophic forgetting in gradient-based neural networks. *arXiv preprint arXiv:1312.6211*, 2013.
- <span id="page-10-19"></span>[10] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H Lampert. icarl: Incremental classifier and representation learning. In *CVPR*, 2017.
- <span id="page-10-2"></span>[11] Xiaoyu Tao, Xiaopeng Hong, Xinyuan Chang, Songlin Dong, Xing Wei, and Yihong Gong. Few-shot class-incremental learning. In *CVPR*, 2020.
- <span id="page-10-3"></span>[12] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-10-11"></span>[13] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021.
- <span id="page-10-14"></span>[14] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021.
- <span id="page-10-15"></span>[15] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *ICML*, 2022.
- <span id="page-10-12"></span>[16] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022.
- <span id="page-10-13"></span>[17] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023.
- <span id="page-10-4"></span>[18] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022.
- <span id="page-10-5"></span>[19] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *ICML*, 2022.
- <span id="page-10-16"></span>[20] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. In *NeurIPS Workshop*, 2022.
- <span id="page-10-10"></span>[21] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *NeurIPS*, 2022.
- <span id="page-10-6"></span>[22] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In *NeurIPS*, 2022.
- <span id="page-10-7"></span>[23] Yann LeCun, Bernhard Boser, John S Denker, Donnie Henderson, Richard E Howard, Wayne Hubbard, and Lawrence D Jackel. Backpropagation applied to handwritten zip code recognition. *Neural computation*, 1(4):541–551, 1989.
- <span id="page-10-8"></span>[24] Kenneth Kreutz-Delgado, Joseph F Murray, Bhaskar D Rao, Kjersti Engan, Te-Won Lee, and Terrence J Sejnowski. Dictionary learning algorithms for sparse representation. *Neural computation*, 15(2):349–396, 2003.
- <span id="page-10-17"></span>[25] Ke Huang and Selin Aviyente. Sparse representation for signal classification. In *NeurIPS*, 2006.
- [26] John Wright, Allen Y Yang, Arvind Ganesh, S Shankar Sastry, and Yi Ma. Robust face recognition via sparse representation. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 31(2):210–227, 2008.
- [27] Rajat Raina, Alexis Battle, Honglak Lee, Benjamin Packer, and Andrew Y Ng. Self-taught learning: transfer learning from unlabeled data. In *ICML*, 2007.
- <span id="page-10-18"></span>[28] Julien Mairal, Francis Bach, Jean Ponce, Guillermo Sapiro, and Andrew Zisserman. Discriminative learned dictionaries for local image analysis. In *CVPR*, 2008.
- [29] Bruno A Olshausen, Charles F Cadieu, and David K Warland. Learning real and complex overcomplete representations from the statistics of natural images. In *Wavelets XIII*, volume 7446, pages 236–246, 2009.
- <span id="page-10-9"></span>[30] Xing Wei, Yifan Bai, Yongchao Zheng, Dahu Shi, and Yihong Gong. Autoregressive visual tracking. In *CVPR*, 2023.

- <span id="page-11-0"></span>[31] Stéphane G Mallat and Zhifeng Zhang. Matching pursuits with time-frequency dictionaries. *IEEE Transactions on Signal Processing*, 41(12):3397–3415, 1993.
- <span id="page-11-4"></span>[32] Bruno A Olshausen and David J Field. Emergence of simple-cell receptive field properties by learning a sparse code for natural images. *Nature*, 381(6583):607–609, 1996.
- <span id="page-11-10"></span>[33] Robert Tibshirani. Regression shrinkage and selection via the lasso. *Journal of the Royal Statistical Society Series B: Statistical Methodology*, 58(1):267–288, 1996.
- [34] Scott Shaobing Chen, David L Donoho, and Michael A Saunders. Atomic decomposition by basis pursuit. *SIAM review*, 43(1):129–159, 2001.
- [35] Joel A Tropp. Greed is good: Algorithmic results for sparse approximation. *IEEE Transactions on Information Theory*, 50(10):2231–2242, 2004.
- <span id="page-11-1"></span>[36] Michal Aharon, Michael Elad, and Alfred Bruckstein. K-svd: An algorithm for designing overcomplete dictionaries for sparse representation. *IEEE Transactions on Signal Processing*, 54(11):4311–4322, 2006.
- <span id="page-11-2"></span>[37] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *NeurIPS*, 2022.
- <span id="page-11-3"></span>[38] Jimmy Lei Ba, Jamie Ryan Kiros, and Geoffrey E Hinton. Layer normalization. *arXiv preprint arXiv:1607.06450*, 2016.
- <span id="page-11-5"></span>[39] Jason Weston, André Elisseeff, Bernhard Schölkopf, and Mike Tipping. Use of the zero norm with linear models and kernel methods. *The Journal of Machine Learning Research*, 3:1439–1461, 2003.
- <span id="page-11-7"></span>[40] Andrew Y Ng. Feature selection, l 1 vs. l 2 regularization, and rotational invariance. In *ICML*, 2004.
- [41] Christos Louizos, Max Welling, and Diederik P Kingma. Learning sparse neural networks through l\_0 regularization. In *ICLR*, 2018.
- <span id="page-11-6"></span>[42] Demi Guo, Alexander M Rush, and Yoon Kim. Parameter-efficient transfer learning with diff pruning. In *ACL-IJCNLP*, 2021.
- <span id="page-11-8"></span>[43] Jun Yu, Yong Rui, and Dacheng Tao. Click prediction for web image reranking using multimodal sparse coding. *IEEE Transactions on Image Processing*, 23(5):2019–2032, 2014.
- [44] Xavier Glorot, Antoine Bordes, and Yoshua Bengio. Deep sparse rectifier neural networks. In *AISTATS*, 2011.
- <span id="page-11-9"></span>[45] Alireza Aghasi, Afshin Abdi, Nam Nguyen, and Justin Romberg. Net-trim: Convex pruning of deep neural networks with performance guarantee. In *NeurIPS*, 2017.
- <span id="page-11-11"></span>[46] Jundong Li, Kewei Cheng, Suhang Wang, Fred Morstatter, Robert P Trevino, Jiliang Tang, and Huan Liu. Feature selection: A data perspective. *ACM Computing Surveys*, 50(6):1–45, 2017.
- <span id="page-11-15"></span>[47] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021.
- <span id="page-11-12"></span>[48] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. Technical report, 2009.
- <span id="page-11-13"></span>[49] cs231n.stanford.edu. Cs231n: Convolutional neural networks for visual recognition.
- <span id="page-11-14"></span>[50] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, 2009.
- <span id="page-11-16"></span>[51] Ameya Prabhu, Philip HS Torr, and Puneet K Dokania. Gdumb: A simple approach that questions our progress in continual learning. In *ECCV*, 2020.
- <span id="page-11-17"></span>[52] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. In *NeurIPS*, 2022.
- <span id="page-11-18"></span>[53] Dan Hendrycks and Thomas Dietterich. Benchmarking neural network robustness to common corruptions and perturbations. In *ICLR*, 2019.
- <span id="page-11-19"></span>[54] Zixuan Jiang, Jiaqi Gu, Mingjie Liu, and David Z Pan. Delving into effective gradient matching for dataset condensation. In *COINS*, 2023.

- <span id="page-12-0"></span>[55] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. In *CVPR*, 2023.
- <span id="page-12-1"></span>[56] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Dataset distillation using parameter pruning. *IEICE Transactions on Fundamentals of Electronics, Communications and Computer Sciences*, 2023.
- [57] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *CVPR*, 2023.
- <span id="page-12-2"></span>[58] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *ICML*, 2023.
- <span id="page-12-3"></span>[59] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *ICLR*, 2021.
- <span id="page-12-4"></span>[60] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, 2022.
- <span id="page-12-5"></span>[61] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial networks. *Communications of the ACM*, 63(11):139– 144, 2020.
- <span id="page-12-6"></span>[62] Rameen Abdal, Yipeng Qin, and Peter Wonka. Image2stylegan: How to embed images into the stylegan latent space? In *ICCV*, 2019.
- <span id="page-12-7"></span>[63] Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*, 2022.
- <span id="page-12-8"></span>[64] José M Bioucas-Dias and Mário AT Figueiredo. A new twist: Two-step iterative shrinkage/thresholding algorithms for image restoration. *IEEE Transactions on Image Processing*, 16(12):2992–3004, 2007.
- [65] Michael Elad and Michal Aharon. Image denoising via sparse and redundant representations over learned dictionaries. *IEEE Transactions on Image Processing*, 15(12):3736–3745, 2006.
- [66] Jianchao Yang, John Wright, Thomas Huang, and Yi Ma. Image super-resolution as sparse representation of raw image patches. In *CVPR*, 2008.
- [67] Jianchao Yang, John Wright, Thomas S Huang, and Yi Ma. Image super-resolution via sparse representation. *IEEE Transactions on Image Processing*, 19(11):2861–2873, 2010.
- [68] Julien Mairal, Francis Bach, Jean Ponce, Guillermo Sapiro, and Andrew Zisserman. Non-local sparse models for image restoration. In *ICCV*, 2009.
- [69] Julien Mairal, Jean Ponce, Guillermo Sapiro, Andrew Zisserman, and Francis Bach. Supervised dictionary learning. In *NeurIPS*, 2008.
- <span id="page-12-9"></span>[70] Jianchao Yang, Kai Yu, Yihong Gong, and Thomas Huang. Linear spatial pyramid matching using sparse coding for image classification. In *CVPR*, 2009.
- <span id="page-12-10"></span>[71] Antonin Chambolle, Ronald A De Vore, Nam-Yong Lee, and Bradley J Lucier. Nonlinear wavelet image processing: variational problems, compression, and noise removal through wavelet shrinkage. *IEEE Transactions on Image Processing*, 7(3):319–335, 1998.
- <span id="page-12-11"></span>[72] Emmanuel J Candes and David L Donoho. Recovering edges in ill-posed inverse problems: Optimality of curvelet frames. *The Annals of Statistics*, 30(3):784–842, 2002.
- <span id="page-12-12"></span>[73] Jianqing Fan and Runze Li. Variable selection via nonconcave penalized likelihood and its oracle properties. *Journal of the American Statistical Association*, 96(456):1348–1360, 2001.
- <span id="page-12-13"></span>[74] Yong Liang, Cheng Liu, Xin-Ze Luan, Kwong-Sak Leung, Tak-Ming Chan, Zong-Ben Xu, and Hai Zhang. Sparse logistic regression with a l1/2 penalty for gene selection in cancer classification. *BMC bioinformatics*, 14(1):1–12, 2013.
- <span id="page-12-14"></span>[75] Emmanuel J Candès, Justin Romberg, and Terence Tao. Robust uncertainty principles: Exact signal reconstruction from highly incomplete frequency information. *IEEE Transactions on Information Theory*, 52(2):489–509, 2006.
- <span id="page-12-15"></span>[76] David L Donoho. Compressed sensing. *IEEE Transactions on Information Theory*, 52(4):1289–1306, 2006.

- <span id="page-13-0"></span>[77] Max Welling. Herding dynamical weights to learn. In *ICML*, 2009.
- [78] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. In *UAI*, 2010.
- <span id="page-13-1"></span>[79] Dan Feldman, Matthew Faulkner, and Andreas Krause. Scalable training of mixture models via coresets. In *NeurIPS*, 2011.
- <span id="page-13-2"></span>[80] Rahaf Aljundi, Min Lin, Baptiste Goujaud, and Yoshua Bengio. Gradient based sample selection for online continual learning. In *NeurIPS*, 2019.
- <span id="page-13-3"></span>[81] Felix Wiewel and Bin Yang. Condensed composite memory continual learning. In *IJCNN*, 2021.
- <span id="page-13-4"></span>[82] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018.
- <span id="page-13-5"></span>[83] Fastai. Fastai/imagenette: A smaller subset of 10 easily classified classes from imagenet, and a little more french.
- <span id="page-13-6"></span>[84] Edgar Riba, Dmytro Mishkin, Daniel Ponsa, Ethan Rublee, and Gary Bradski. Kornia: an open source differentiable computer vision library for pytorch. In *WACV*, 2020.
- <span id="page-13-7"></span>[85] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014.
- <span id="page-13-8"></span>[86] Christian Szegedy, Wei Liu, Yangqing Jia, Pierre Sermanet, Scott Reed, Dragomir Anguelov, Dumitru Erhan, Vincent Vanhoucke, and Andrew Rabinovich. Going deeper with convolutions. In *CVPR*, 2015.
- <span id="page-13-9"></span>[87] Dmitry Ulyanov, Andrea Vedaldi, and Victor Lempitsky. Improved texture networks: Maximizing quality and diversity in feed-forward stylization and texture synthesis. In *CVPR*, 2017.
- <span id="page-13-10"></span>[88] Ashish Vaswani, Noam Shazeer, Niki Parmar, Jakob Uszkoreit, Llion Jones, Aidan N Gomez, Łukasz Kaiser, and Illia Polosukhin. Attention is all you need. In *NeurIPS*, 2017.

## I Experiment Details

Datasets and Preprocessing. We evaluate our methods on the following datasets: i) CIFAR10 [\[48\]](#page-11-12): A standard image dataset consists of  $60,000$  32 $\times$ 32 RGB images in 10 different classes, including airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck. For each class, 5000 images are used for training and 1000 images are used for testing. ii) CIFAR100 [\[48\]](#page-11-12): CIFAR100 contains 100 classes. It has a training set with 50,000 images and a testing set with 10,000 images. iii) **TinyImageNet [\[49\]](#page-11-13):** A  $64 \times 64$  image dataset with 200 classes. Each class has 500 images for training and 50 images for testing. iv) **ImageNet** [\[50\]](#page-11-14) subsets: High resolution (128 $\times$ 128) datasets from ILSVRC2012 [\[50\]](#page-11-14). ImageNette (assorted objects) and ImageWoof (dog breeds) are designed for easy and hard classification tasks  $[83]$ . ImageFruit, ImageMeow, ImageSquawk, and ImageYellow  $[16]$ consist of fruits, cats, birds, and yellowish things, respectively. All the above subsets contain 10 classes.

For fair comparisons, we follow the previous methods  $[16, 21]$  $[16, 21]$  $[16, 21]$ , adopting ZCA whitening on CIFAR10 and CIFAR100 with the default Kornia [\[84\]](#page-13-6) implementation, and no ZCA whitening is used on TinyImageNet and ImageNet subsets.

Evaluation Settings. The objective of dataset distillation is to learn a synthetic dataset that can be utilized across a range of network structures. This is achieved by matching the raw and synthetic datasets with the aid of a specific network, with the expectation that can generalize to unseen network structures. In accordance with the evaluation presented in [\[52\]](#page-11-17), we consider a diverse set of architectures, as shown below:

• ConvNet [\[1,](#page-9-0) [85,](#page-13-7) [86\]](#page-13-8): The standard architecture used for both distilling and evaluating synthetic datasets in previous distillation work. The default network contains three  $3\times3$  convolution layers, each followed by  $2\times 2$  average pooling and instance normalization. The hidden embedding size is set to 128. There are around 320K trainable parameters. For TinyImageNet, the number of layers is increased to 4 for improved performance, as suggested in previous work [\[16,](#page-10-12) [17\]](#page-10-13). Similarly, we increase it to 5 for ImageNet subsets, following [\[16\]](#page-10-12).

• MLP: The simple MLP architecture is applied for evaluation, which includes 3 fully connected layers, and the width is set to 128. For CIFAR10, CIFAR100, and TinyImageNet, the MLP has 411K, 423K, and 2M trainable parameters, respectively.

• ResNet18 [\[2\]](#page-9-4): We also evaluate synthetic datasets on the commonly used ResNet architecture with 4 residual blocks. In each block, there are 2 convolution layers followed by ReLU activation and instance normalization (IN)  $[87]$ . The number of trainable parameters is around 11M.

• ViT [\[3\]](#page-9-3): Vision Transformer applies standard transformer [\[88\]](#page-13-10) on non-overlapping image patches, demonstrating the variants of transformers can also be a competitive alternative to CNNs. We take it as one of the architectures for evaluating synthetic datasets. There are around 10M trainable parameters in the adopted implementation of ViT.

Following the mainstream evaluation settings, we train 5 randomly initialized evaluation networks on the synthetic dataset, using SGD optimizer, where the momentum and weight decay are set to 0.9 and 0.0005, respectively.

**Hyper-Parameters.** For CIFAR10 (32×32), CIFAR100 (32×32), and TinyImageNet (64×64), we fix the default number of patches J to 64, and increase it to 256 for ImageNet subsets ( $128 \times 128$ ). Then, the default patch dimension L can be directly derived.

In terms of SAETs, which has been studied in Sec.  $3.3$ , we set the number K according to the resolution of the dataset (e.g. we set  $K$  to 256 for ImageNet subsets, and decrease it to 128 and 64 for TinyImageNet and CIFAR100, respectively). When the budget is enlarged, we expand the dimension of SAETs D with its head H increased together, while guaranteeing that their divisor  $D/H \le K$ , so that each head  $E^h$  is a complete (or overcomplete) dictionary. We always build the FReeNet with shallow blocks, which provides sufficient nonlinear capabilities while making it lightweight enough (e.g. we set 1 block on CIFAR10 with IPC 1 storage space which is excessively stringent, and set 2 blocks for all experiments on TinyImageNet and ImageNet subsets).

Image /page/15/Picture/0 description: The image displays a grid of images categorized by patch size. The top row, labeled "(a) Patch size 4x4", shows clear images of a bicycle, can, clock, cup, hamster, house, lamp, motorcycle, pickup truck, rabbit, raccoon, and sunflower. The second row, labeled "(b) Patch size 8x8", shows slightly more blurred versions of the same objects. The third row, labeled "(c) Patch size 16x16", displays even more blurred and abstract representations of these objects. The labels for each object are provided above the first row.

(d) TM [\[16\]](#page-10-12) baseline (patch size  $32\times32$ )

Figure I: Synthetic data samples under different configurations of patch size. A small patch size can capture intricate details such as the black eye patch of the hamster, truck tires, and rabbit ears.

The number of non-zero elements k is always set to a value that ensures the matrix density  $k/JK$  <  $5\%$ , then the number of images N can be computed according to Eq. [\(7\)](#page-4-1). The setting of the penalty weight  $\lambda$  depends on the number of images, heads, and patches, satisfying  $\lambda \approx 0.064/NHJ$ .

Compute Resources. Our experiments were run on a mixture of RTX 3090, RTX A6000, and A800 GPUs. The GPU memory consumption is mainly dependent on that of the matching objective and is slightly higher than its baseline, due to the larger data amount. For instance, our experiments based on gradient matching [\[13\]](#page-10-11) and distribution matching [\[17\]](#page-10-13) need one 24GB 3090 GPU. In terms of trajectory matching [\[16\]](#page-10-12), the VRAM usage will be higher, ranging from one 24GB 3090 GPU to six 80GB A800 GPUs.

## II Additional Results and Analyses

**Patch Size.** The patch size plays an important role in determining the granularity of our distillation modeling. Let each patch  $\tilde{x}$  be of  $P \times P$  resolution and contains C channels, then its dimension L will be  $P^2C$ . Tab. [I](#page-5-0) presents the results on CIFAR100 with IPC 1 storage space. We observe that setting a relatively small patch size can lead to better performance, as it reduces redundancy within synthetic images and enables finer-grained synthesis. Therefore, we set a default patch size of  $4\times4$  for the 32 $\times32$  resolution datasets (CIFAR10 and CIFAR100) and increase it appropriately when the resolution of the dataset increases. For instance, we adopt a default patch size of  $8\times 8$  for TinyImageNet  $(64\times64)$ .

| Patch size    | N/c | ConvNet                             | MLP                                 | ResNet18                            | ViT                                 |
|---------------|-----|-------------------------------------|-------------------------------------|-------------------------------------|-------------------------------------|
| $4 	imes 4$   | 11  | <b><math>40.0 	extpm 0.4</math></b> | <b><math>15.5 	extpm 0.2</math></b> | <b><math>29.9 	extpm 0.3</math></b> | <b><math>20.7 	extpm 0.5</math></b> |
| $8 	imes 8$   | 10  | $39.3 	extpm 0.3$                   | $14.6 	extpm 0.3$                   | $29.4 	extpm 0.5$                   | $20.2 	extpm 0.2$                   |
| $16 	imes 16$ | 7   | $37.3 	extpm 0.3$                   | $12.9 	extpm 0.3$                   | $27.1 	extpm 0.8$                   | $16.5 	extpm 0.2$                   |

Table I: Patch size. Small patch sizes allow finer-grained modeling.

Furthermore, we provide a visualization of synthetic images with varying patch sizes, as depicted in Fig. [I.](#page-1-0) The utilization of small patches can facilitate the expression of finer-grained features, leading to synthetic images that contain more classifiable information. For instance, when a  $4\times4$  patch is employed, it can capture intricate details such as the black eye patch of the hamster, in addition to more noticeable features like truck tires and rabbit ears. Also, sparse parameterization allows for efficient storage of background information with limited significance by allocating fewer non-zero elements in SCMs to represent it.

Image /page/16/Figure/0 description: This image contains three plots. The first plot is a stacked bar chart showing test accuracy (%) versus penalty weight. The penalty weights on the x-axis are 1e-7, 2e-7, 3e-7, 5e-7, and 1e-6. The y-axis ranges from 25 to 40. The bars are stacked with 'TM' in blue at the bottom and 'w. SPEED' in pink on top. The second plot is a line graph showing test accuracy (%) versus size of k. The x-axis shows sizes of k: 64, 128, 192, and 256. The y-axis ranges from 39 to 42. There are two lines: 'Local' in purple and 'Global' in red, with shaded regions indicating variability. The third plot is a line graph showing test accuracy (%) versus distilling time (mins). The x-axis ranges from 0 to 80, with labels at 0, 20, 40, 60, and 80. The y-axis ranges from 0 to 60. There are four lines representing different methods: 'TM' in green, 'HaBa' in blue, 'RTP' in orange, and 'SPEED' in red. All lines show an increase in test accuracy with distilling time, with 'SPEED' achieving the highest accuracy.

Figure II: Left: Effects of different penalty weight  $\lambda$  on test accuracy. TM [\[16\]](#page-10-12) denotes the baseline method of the trajectory matching objective with naive image-independent parameterization. A moderate weight can trade off sparsity and accuracy. Middle: Comparison of different feature sparsification strategies. Global sparsification performs better in various settings, especially when the storage budget is stringent. Right: Test accuracy and distilling time.

Image /page/16/Figure/2 description: The image displays a grid of images, with each row containing four variations of a particular object. The columns are labeled 'full', 'top-48', 'top-24', and 'top-12', suggesting different levels of detail or processing. The objects depicted are oranges, lamps, clocks, flies, bicycles, fish, wine bottles, lions, chairs, cars, and tomatoes. Each object appears in a sequence of four images, with the 'full' version showing the complete object and the subsequent 'top-48', 'top-24', and 'top-12' versions showing progressively less detail, likely due to pixelation or data reduction.

Figure III: Visualization of our SCMs with varying values of  $k$  (patch corresponds to zero column vector in its SCMs is marked grey). SPEED learns to efficiently allocate more storage for foreground objects that contain more classifiable information. Therefore, a small  $k$  still allows for salient features of objects to be synthesized well.

Sparsity Penalty Weight  $\lambda$ . In SPEED, the sparsity penalty term constrains the overall scale of elements in SCMs. Ablation studies of the penalty weight  $\lambda$  are conducted on CIFAR100 with IPC 1 storage space, as shown in Fig. [II](#page-6-1) (left). For these experiments, we fix the number of sparsified features to 48. If the penalty weight is too small, our SCM tends to comprise a higher number of elements with larger absolute values, which necessitates more features to be maintained during feature sparsification, thereby guaranteeing the test performance. It consequently implies a larger storage burden. On the other hand, setting the penalty weight too high may limit the representation capability required for synthesizing informative images. Therefore, we prefer the moderate penalty weight, which allows for sufficient combinations of required epitomic tokens while efficiently eliminating inefficient ones without the risk of losing important information.

Local Feature Sparsification. To assess the efficacy of our feature sparsification with global semantic preservation on SCMs, we introduce the contrast, *local feature sparsification*, that attempts to store an equal number of non-zero elements for each patch, *i.e.*, each column of SCM has the same number of non-zero elements. Consequently, local feature sparsification constructs all patches using the same number of parameters (keep  $k/J$  elements for each column of SCM), while the feature sparsification with global semantic preservation does not (keep  $k$  elements for whole SCM). We evaluate the performance of these two methods using different values of  $k$ , while maintaining the other hyper-parameters constant.

As illustrated in Fig. [II](#page-6-1) (middle), we conduct tests on CIFAR100 with k set to  $\{64, 128, 192, 256\}$ , where the SCM size is  $64 \times 64$ . Accordingly, the local feature sparsification strategy preserves  $\{1, 2, 3, 4\}$  features for each synthetic patch (e.g. when k is set at 128,  $J = 64$ , then each patch is represented by the 2 most prominent SAETs). In all cases, the global feature sparsification outperforms the local one. Notably, the advantages of the global approach are more apparent when the storage

Image /page/17/Picture/0 description: The image displays a grid of generated images of dogs, categorized by the convolutional neural network architecture used. The categories are labeled (a) no convolutional layers, (b) 1 layer, kernel size = 3x3, (c) 1 layer, kernel size = 5x5, and (d) 2 layers, kernel size = 3x3. Each category contains multiple images of dogs, with varying degrees of clarity and detail depending on the network configuration. The images appear to be generated outputs from a machine learning model, likely demonstrating the effect of different convolutional layer parameters on image generation quality.

(e) 2 layers, kernel size =  $5 \times 5$ 

Figure IV: Visualizations (border terrier class in ImageNette) regarding the incorporation of convolutional layers after the rearrangement of patches. The presence of the chessboard artifact gradually diminishes with increases in the number of convolutional layers and the size of convolutional kernels.

budget is stringent, *i.e.*, for small k. This indicates that our global feature sparsification technique produces synthetic images with more classifiable information under the same storage budget, making it more efficient for dataset distillation. Similar to the conclusion drawn in Sec. [3.3,](#page-6-0) we observe that as k increases to a certain extent, the accuracy reaches an upper bound and then fluctuates.

Test Accuracy vs. Distilling Time. We observe the test accuracy and distilling time on CIFAR10 with IPC 1 storage space, and compare with the previous methods  $[16, 21, 22]$  $[16, 21, 22]$  $[16, 21, 22]$  $[16, 21, 22]$  $[16, 21, 22]$ , as illustrated in Fig. [II](#page-6-1) (right). Overall, SPEED consistently outperforms previous methods when compared at similar distilling time points.

**Visualization of SCMs.** We visualize our SCMs with varying values of  $k$  in Fig. [III,](#page-6-4) where the patch corresponds to zero column vector in its SCMs is marked grey. As k decreases, we observe a higher preference for column vectors that correspond to background patches to be zero vectors in SCMs. This indicates that SPEED automatically learns to allocate more storage budget to foreground objects that contain more useful clues for classification. Even when  $k$  is set as low as 12, the salient foreground object is still well synthesized. It should be noted that the zeroed-out patches do not occupy any storage budget since the SCMs are saved in the COO compressed format.

Incorporation of Additional Convolutional Layers. We find a chessboard (blocky) artifact on synthetic images of high-resolution datasets. To investigate its influence on test accuracy, we perform multiple experiments on ImageNette with IPC 1 storage space, adding 1 and 2 convolutional layers with kernel sizes 3 and 5, as shown in Tab. [II.](#page-5-1) As evident from the results, the incorporation of additional convolutional layers in our experiments does not yield a significant

| Kernel size | $3\times3$                     | $5\times5$   |
|-------------|--------------------------------|--------------|
| 1 layer     | $66.3\pm1.8$                   | $66.4\pm1.3$ |
| 2 layers    | $65.9\pm1.3$                   | $64.0\pm0.5$ |
| None        | <b><math>66.9\pm0.7</math></b> |              |

Table II: Enlargement of the image resolution on ImageNette.

improvement in downstream training. However, it does provide slight relief from the chessboard artifact, as depicted in Fig. [IV.](#page-7-1) The impact of the chessboard artifact on downstream training and the exploration of parameter-efficient methods to eliminate these artifacts warrant further investigation.

| Spar.                    | Block-shared |                          | Evaluation |              |              |                |                |  |  |
|--------------------------|--------------|--------------------------|------------|--------------|--------------|----------------|----------------|--|--|
|                          | <b>SAET</b>  | <b>SCM</b>               | # Param    | ConvNet      | MLP          | ResNet18       | ViT            |  |  |
|                          |              |                          | 27M        | $41.2 + 0.4$ | $15.6 + 0.2$ | $29.8 + 0.5$   | $21.1 + 0.3$   |  |  |
|                          | ✓            | $\overline{\phantom{0}}$ | 27M        | $41.3 + 0.2$ | $15.6 + 0.3$ | $29.4 + 0.4$   | $20.6 \pm 0.1$ |  |  |
| $\overline{\phantom{a}}$ | √            | ✓                        | 14M        | $41.2 + 0.4$ | $15.3 + 0.1$ | $29.6 + 0.6$   | $20.9 \pm 0.4$ |  |  |
|                          |              |                          | 549K       | $40.0 + 0.6$ | $15.6 + 0.1$ | $30.3 \pm 0.5$ | $20.7 + 0.4$   |  |  |
|                          | ✓            | $\overline{\phantom{0}}$ | 543K       | $40.3 + 0.7$ | $15.6 + 0.1$ | $29.7 + 0.7$   | $21.0 + 0.4$   |  |  |
| ✓                        | √            | ✓                        | 305K       | $40.0 + 0.4$ | $15.5 + 0.2$ | $29.9 + 0.3$   | $20.7 + 0.5$   |  |  |

Table III: Storage efficiency study. The checkmark  $\checkmark$  in the Spar. column indicates the use of sparsified SCMs, while the absence of a mark indicates the use of full-element SCMs. The checkmark  $\checkmark$  in the Block-shared SAET/SCM column indicates sharing SAETs/SCMs across different scales of blocks, while the absence of a mark indicates using block-specific SAETs/SCMs.

| IPC<br>Parameters / Class | 1<br>3,072                                        | 10<br>30,720                                      | 50<br>153,600                                      |
|---------------------------|---------------------------------------------------|---------------------------------------------------|----------------------------------------------------|
| IDC [19]                  | -                                                 | 40 (44.8 $ $ $ $ 0.2)                             | -                                                  |
| HaBa [21]                 | 5 (33.4 $ $ $ $ 0.4)                              | 45 (40.2 $ $ $ $ 0.2)                             | 245 (47.0 $ $ $ $ 0.2)                             |
| RTP [22]                  | 16 (34.0 $ $ $ $ 0.4)                             | 232 (42.9 $ $ $ $ 0.7)                            | -                                                  |
| <b>SPEED</b>              | <b>11 (40.0<math> </math> <math> </math> 0.4)</b> | <b>62 (45.9<math> </math> <math> </math> 0.3)</b> | <b>100 (49.1<math> </math> <math> </math> 0.2)</b> |

Table V: Comparisons on quantity  $(N/c)$  and quality (test accuracy) of synthetic images.

**Storage Efficiency Study.** To investigate the storage efficiency of recurrent blocks that involves utilizing block-shared SAETs and SCMs, we attempt to allocate specific SAETs and SCMs for each block, along with setting a uniform value for  $k$  in feature sparsification, as studied in Tab. [III.](#page-6-2) Our results indicate that applying recurrent blocks leads to the highest efficiency. While the blockspecific strategy shows a marginal improvement over the block-shared strategy with or without feature sparsification, it lacks a significant performance advantage. However, recurrent blocks using block-shared SAETs and SCMs come with significantly lower storage, saving about 44% parameters while achieving competitive results. Furthermore, we observe that feature sparsification can enhance both the overall cross-architecture generalization performance and storage efficiency.

with other methods operating under the IPC 10 budget, *i.e.*, Table IV: Number of synthetic images Number of Synthetic Images. The number of our synthetic images on ImageNet subsets is summarized in Tab. [IV.](#page-6-3) Our method synthesizes 15 images under the IPC 1 budget, and remarkably, it achieves performance that is competitive 10 synthetic images, while utilizing only 10% of the parameters. For instance, on ImageNette, our method achieves an

| IPC.<br>Parameters / Class | 1      | 10      |
|----------------------------|--------|---------|
|                            | 49,152 | 491,520 |
| # Synthetic images         | 15     | 111     |

on ImageNet subsets.

impressive accuracy of 66.9% with IPC 1, surpassing the previous state-of-the-art [\[37\]](#page-11-2) result of 66.5% achieved with IPC 10. These findings demonstrate the efficiency of our method and the high quality of the synthetic images it produces.

To further prove the above claim, we conclude the number of synthetic images on CIFAR100, compared with other parameterization work  $[19, 21, 22]$  $[19, 21, 22]$  $[19, 21, 22]$  $[19, 21, 22]$  $[19, 21, 22]$ , as shown in Tab. [V.](#page-7-0) As evident, while the number of our synthetic images is not the highest among all approaches, our outstanding performance clearly showcases the high quality of the synthetic images. This further emphasizes that our approach enhances performance by improving both the quality and quantity of the synthetic images. Moreover, it demonstrates the highly efficient reduction of spatial redundancy achieved by our method.

## III Additional Visualizations

We include additional visualizations here. Fig. [V-](#page-19-0)[VII](#page-21-0) show the synthetic datasets for CIFAR10 and CIFAR100. Higher-resolution datasets are visualized in Fig. [VIII-](#page-22-0)[XV.](#page-26-0) We find that visualizations of lower-resolution datasets are more human-friendly since they contain less spatial redundancy. For high-resolution datasets, storage budgets are primarily allocated to salient foreground features, resulting in a significant reduction of useless patches. Nevertheless, the main texture and rough shape can still be recognized.

<span id="page-19-0"></span>Image /page/19/Picture/0 description: This is a grid of 80 images, arranged in 8 rows and 10 columns. The images appear to be a collection of various objects and animals, possibly from a dataset like CIFAR-10. The top rows feature airplanes and cars. The middle rows contain birds, cats, dogs, and deer. The bottom rows show ships and trucks. The images are small and somewhat blurry, typical of a visualization of generated or sampled data.

Figure V: Synthetic images on CIFAR10.

Image /page/20/Picture/0 description: The image displays a grid of 100 synthetic images, arranged in 10 rows and 10 columns. Each small image is a colorful, low-resolution representation of various objects and scenes. The top rows feature images of fruits like apples, followed by what appear to be babies or children, then insects, and bicycles. Subsequent rows show a mix of objects such as bottles, cars, airplanes, and animals like cats, dogs, horses, and lions. The bottom rows include images of people, motorcycles, and landscapes with trees and mountains. The overall impression is a diverse collection of generated visual content.

Figure VI: Synthetic images on CIFAR100. (Classes 1-50)

<span id="page-21-0"></span>Image /page/21/Picture/0 description: The image displays a grid of 100 synthetic images, arranged in 10 rows and 10 columns. Each small image is a square and appears to be a generated sample from a dataset. The images depict a variety of subjects, including animals (cats, dogs, hedgehogs, squirrels, tigers), plants (flowers, trees, fruits like apples and pears), vehicles (cars, trucks, tanks), and natural scenes (oceans, skies, landscapes). The overall impression is a diverse collection of generated visual content.

Figure VII: Synthetic images on CIFAR100. (Classes 51-100)

<span id="page-22-0"></span>Image /page/22/Picture/0 description: This is a large grid of many small images, likely synthetic images from TinyImageNet. The images are arranged in rows and columns, and appear to be categorized by class. The overall impression is a mosaic of diverse visual content, with many different animals, objects, and scenes represented. The caption below the image reads "Figure VIII: Synthetic images on TinyImageNet (Classes 1, 100)."

Figure VIII: Synthetic images on TinyImageNet. (Classes 1-100)

Image /page/23/Figure/0 description: The image is a large collage of many small images, arranged in a grid. The collage appears to be a representation of different classes of objects, possibly from a dataset like TinyImageNet. The small images depict a wide variety of subjects, including people, animals (like teddy bears), food items (fruits, vegetables, pizzas, coffee cups), buildings, and various objects. The arrangement suggests a systematic organization, with rows and columns of similar or related images. The overall impression is a dense and diverse collection of visual information.

Figure IX: Synthetic images on TinyImageNet. (Classes 101-200)

Image /page/24/Picture/0 description: A grid of 8x8 images, each image is a small, pixelated representation of a scene. The top rows appear to show people and animals, possibly dogs. The middle rows show cars and buildings, possibly churches or temples. The bottom rows show outdoor scenes with grass, golf balls, and what look like parachutes or kites in the sky.

Figure X: Synthetic images on ImageNette.

Image /page/24/Picture/2 description: A grid of 80 images, each 64x64 pixels, displays various dog breeds. The images are arranged in 8 rows and 10 columns. The overall impression is a collage of dog portraits and full-body shots, with some images showing dogs in outdoor settings. The quality of the images varies, with some appearing more detailed than others, suggesting they might be generated or low-resolution samples.

Figure XI: Synthetic images on ImageWoof.

Image /page/25/Picture/0 description: The image displays a grid of small, pixelated images, arranged in rows and columns. Each small image appears to be a close-up of a different type of fruit or flower. The colors vary across the grid, with rows predominantly featuring yellow, red, orange, and green hues, suggesting different categories of produce. The overall impression is a mosaic or a collection of samples, possibly from a dataset or a visual representation of different items.

Figure XII: Synthetic images on ImageFruit.

Image /page/25/Picture/2 description: A grid of 80 images, each 80x80 pixels, displays various animals. The top four rows predominantly feature cats, with some images showing full cat faces and others showing partial views. The fifth row contains images of lions, mostly showing their heads and manes. The sixth row is filled with tigers, with clear stripes visible in many of the images. The seventh row showcases leopards, identifiable by their spotted coats. The bottom row appears to be a mix of animals, including what look like deer or other hoofed mammals, and some birds. The overall impression is a collection of animal portraits, likely generated by an AI, with varying degrees of clarity and detail.

Figure XIII: Synthetic images on ImageMeow.

Image /page/26/Picture/0 description: A grid of 80 small images, each depicting a different bird. The birds are arranged in rows and columns, with each image showing a close-up of a bird. The birds include peacocks, flamingos, parrots, penguins, eagles, toucans, ostriches, and swans. The images are pixelated, giving them a somewhat abstract appearance.

Figure XIV: Synthetic images on ImageSquawk.

<span id="page-26-0"></span>Image /page/26/Picture/2 description: The image is a grid of 42 images, arranged in 7 rows and 6 columns. Each image is a low-resolution representation of a different object or scene. The top row appears to show flowers and plants. The second row shows what look like fruits, possibly lemons or apples. The third row also seems to depict fruits. The fourth row displays images of yellow school buses. The fifth row shows what might be honeycomb or a similar textured pattern. The sixth row contains images of what appear to be big cats, possibly lions or tigers. The bottom row shows images of spiders and birds.

Figure XV: Synthetic images on ImageYellow.
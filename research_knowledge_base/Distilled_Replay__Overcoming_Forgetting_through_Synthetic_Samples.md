# Distilled Replay: Overcoming Forgetting through Synthetic Samples

<PERSON><sup>1</sup>\*, <PERSON><sup>1</sup>, <PERSON><sup>2</sup>, <PERSON><sup>1</sup> and <PERSON><PERSON><sup>1</sup>

<sup>1</sup>University of Pisa <sup>2</sup>Scuola Normale Superiore

## Abstract

Replay strategies are Continual Learning techniques which mitigate catastrophic forgetting by keeping a buffer of patterns from previous experiences, which are interleaved with new data during training. The amount of patterns stored in the buffer is a critical parameter which largely influences the final performance and the memory footprint of the approach. This work introduces Distilled Replay, a novel replay strategy for Continual Learning which is able to mitigate forgetting by keeping a very small buffer (1 pattern per class) of highly informative samples. Distilled Replay builds the buffer through a distillation process which compresses a large dataset into a tiny set of informative examples. We show the effectiveness of our Distilled Replay against popular replay-based strategies on four Continual Learning benchmarks.

## 1 Introduction

Deep learning models trained under the assumption that all training data is available from the beginning and each sample is independent and identically distributed manage to achieve impressive performance [\[<PERSON><PERSON><PERSON><PERSON>](#page-6-0) *et al.*, 2012]. This learning scenario is often called *offline training*. Contrary to offline training, *continual learning* (CL) requires the model to learn sequentially from a stream of experiences [\[Lesort](#page-6-1) *et al.*[, 2020\]](#page-6-1). Each experience is made of a batch of data, which may contain new knowledge such as novel classes that need to be distinguished by the model. Therefore, the model must be continually updated to incorporate knowledge coming from new experiences. However, when trained on new samples, neural networks tend to forget past knowledge: this phenomenon is called catastrophic forgetting [\[French,](#page-6-2) [1999\]](#page-6-2). Catastrophic forgetting emerges as a consequence of the stability-plasticity dilemma [\[Grossberg, 1980\]](#page-6-3), that is the difficulty of a model to be both plastic enough to acquire new information and stable enough to preserve previously acquired knowledge.

Continual learning may have a large impact on a variety of real world applications: Computer vision [\[Lomonaco and](#page-6-4) [Maltoni, 2017\]](#page-6-4), Natural Language Processing [Sun *[et al.](#page-6-5)*, [2020\]](#page-6-5) and Robotics [\[Thrun, 1995\]](#page-6-6) are examples of environments where the data is highly non stationary and may vary over time. A model able to learn continuously without forgetting would not need to be retrained from scratch every time a new experience is introduced (cumulative training). In fact, retraining is often the only viable alternative to continual learning in dynamic environments. However, retraining requires to store all the encountered data, which is often unfeasible under real world constraints. To address this problem, in this paper we focused on Replay strategies [\[Chaudhry](#page-6-7) *et al.*, [2019b;](#page-6-7) [Aljundi](#page-6-8) *et al.*, 2019], a family of CL techniques which leverages a buffer of patterns from previous experiences and uses it together with the current data to train the model.

We introduce a novel CL strategy called Distilled Replay to address the problem of building very small replay buffers with highly informative samples. Distilled Replay is based on the assumption that, if a replay pattern represents most of the features present in a dataset, it will be more effective against forgetting than a randomly sampled pattern from the dataset. Moreover, keeping a small buffer is useful to deploy continual learning solutions in real-world applications, since the memory footprint is drastically reduced with respect to cumulative approaches. Distilled Replay directly optimizes the memory consumption by keeping a buffer of only one pattern per class, while still retaining most of the original performance. The buffer is built using a distillation process based on Dataset Distillation [Wang *et al.*[, 2018\]](#page-6-9), which allows to condensate an entire dataset into few informative patterns. Dataset Distillation removes the need to select replay patterns from the real dataset. Instead, it learns patterns which summarize the main characteristics of the dataset. Distilled Replay acts by combining our modified version of Dataset Distillation with replay strategies. It shows that even one pattern per class is sufficient to mitigate forgetting. In contrast, other replay strategies need larger memory buffers to match the performance of our approach.

## 2 Continual Learning Scenario

In this work, we consider continual learning on a sequence of T experiences  $E_1, \ldots, E_T$ . Each experience  $E_t$  is associated to a training set  $D_t^{tr}$ , a validation set  $D_t^{vl}$  and a test set  $D_t^{ts}$ . A continual learning algorithm operating in the aforementioned

<sup>∗</sup>[Contact Author](#page-6-4)

scenario can be defined as follows [\[Lesort](#page-6-1) *et al.*, 2020]:

$$
\forall D_t^{tr} \in S, A_t^{CL} : \langle \boldsymbol{\theta}_{t-1}, D_t^{tr}, B_{t-1}, l_t \rangle \to \langle \boldsymbol{\theta}_t, B_t \rangle,
$$
 (1)

where:

- $\bullet$   $\theta_t$  are the model parameters at experience t, learned continually;
- $B_t$  is an external buffer to store additional knowledge (like previously seen patterns);
- $\bullet$   $l_t$  is an optional task label associated to each experience. The task label can be used to disentangle tasks and customize the model parameters (e.g. by using multiheaded models [\[Farquhar and Gal, 2019\]](#page-6-10));
- $D_t^{tr}$  is the training set of examples.

An algorithm respecting this formalization can be applied to different continual learning scenarios. In this paper, we used domain-incremental and class-incremental scenarios, identified in [\[van de Ven and Tolias, 2019\]](#page-6-11).

In Domain Incremental Learning (D-IL) the classes to learn are all present from the first experience, but their generating distribution is subjected to a drift from one experience to the other.

In Class Incremental Learning (C-IL) scenarios each experience provides patterns coming from classes which are not present in the other experiences.

We impose additional constraints to these scenarios, restricting the number of elements that can be stored from previous experiences to one per class and the number of epochs to one (single-pass).

## 3 Related Works

The challenge of learning continuously has been addressed from different point of views [Lesort *et al.*[, 2020\]](#page-6-1). Regularization approaches try to influence the learning trajectory of a model in order to mitigate forgetting of previous knowledge [\[Chaudhry](#page-6-12) *et al.*, 2018]. The regularization term is designed to increase the model stability across multiple experiences, for example by penalizing large changes in parameters deemed important for previous experiences [\[Kirkpatrick](#page-6-13) *et al.*[, 2017\]](#page-6-13).

Architectural strategies refer to a large set of techniques aimed at dynamically modifying the model structure . The addition of new components (e.g. layers [Rusu *et al.*[, 2016\]](#page-6-14)) favors learning of new information, while forgetting can be mitigated by freezing previously added modules [\[Asghar](#page-6-15) *et al.*[, 2019\]](#page-6-15) or by allocating separate components without interference [Rusu *et al.*[, 2016;](#page-6-14) Cossu *et al.*[, 2020\]](#page-6-16).

Dual memories strategies. This family of CL algorithms is loosely inspired by the Complementary Learning System theory (CLS) [\[McClelland](#page-6-17) *et al.*, 1995]. This theory explains the memory consolidation process as the interplay between two structures: the hippocampus, responsible for the storage of recent episodic memories, and the neocortex, responsible for the storage of long-term knowledge and for the generalization to unseen events.

The idea of having an episodic memory (i.e. a buffer of previous patterns) which replays examples to a long-term storage (i.e. the model) is very popular in continual learning [\[Chaudhry](#page-6-7) *et al.*, 2019b]. In fact, replay strategies are the most common representatives of dual memory strategies and very effective in class-incremental scenarios [\[van de Ven](#page-6-18) *et al.*[, 2020\]](#page-6-18).

Replay strategies are based on a sampling operation, which extracts a small buffer of patterns from each experience, and on a training algorithm which combines examples from the buffer with examples from the current experience. Sampling policies may vary from random selection to the use of heuristics to select patterns that are more likely to improve recall of previous experiences [\[Aljundi](#page-6-8) *et al.*, 2019]. Additionally, generative replay approaches [Shin *et al.*[, 2017\]](#page-6-19) do not rely directly on patterns sampled from the dataset. Instead, they train a generative model to produce patterns similar to the ones seen at training time. Our approach share loose similarities with generative replay, since we do not replay patterns directly sampled from the original training set either. One important aspect of all replay approaches is the size of the replay buffer. Real-world applications may be constrained to the use of small buffers since they allow to scale to a large number of experiences. For this reason, one important objective of replay techniques is to minimize the buffer size [\[Chaudhry](#page-6-7) *et al.*, 2019b]. However, relying on patterns sampled directly from the dataset or generated on-the-fly by a generative model may need many examples to mitigate forgetting. Our method, instead, leverages one of the simplest replay policies and manages to maintain one highly informative pattern per class in the buffer.

## <span id="page-1-1"></span>4 Distilled Replay

Our proposed approach, called Distilled Replay, belongs to the family of dual memory strategies. Distilled Replay combines a simple replay policy with a small buffer composed of highly informative patterns. Instead of using raw replay samples, Distilled Replay learns the patterns to be replayed via buffer distillation, a process based on dataset distillation [Wang *et al.*[, 2018\]](#page-6-9). In the remainder of this section, we describe the buffer distillation process and how to use it together with replay policies in continual learning.

Buffer distillation Replay strategies operating in the small buffer regime are forced to keep only few examples per class. These examples may not be representative of their own classes, thus reducing the effectiveness of the approach. Distilled Replay addresses this problem by implementing buffer distillation, a technique inspired by Dataset Distillation [Wang *et al.*[, 2018\]](#page-6-9). Our buffer distillation compresses a dataset into a small number of highly informative, synthetic examples. Given a dataset  $\mathbf{x} = \{x_i\}_{i=1}^N$ , and an initialization  $\theta_0$ , the goal is to learn a buffer of samples  $\tilde{x} = \{\tilde{x}_i\}_{i=1}^M$ , initialized with samples from the dataset, and with  $M \ll N$ . Performing S steps of SGD on the buffer  $\tilde{x}$  results in a model

$$
\boldsymbol{\theta}_{S} = \boldsymbol{\theta}_{S-1} - \eta \nabla_{\boldsymbol{\theta}_{S-1}} \ell(\tilde{\boldsymbol{x}}, \boldsymbol{\theta}_{S-1}),
$$
 (2)

<span id="page-1-0"></span>that performs well on the original dataset  $x$ . Buffer distillation achieves this result by solving the optimization problem:

<span id="page-2-0"></span>
$$
\tilde{\boldsymbol{x}}^* = \arg\min_{\tilde{\boldsymbol{x}}} \ \mathbb{E}_{\boldsymbol{\theta}_0 \sim p(\boldsymbol{\theta}_0)} \ \sum_{s=1}^S \ell(\boldsymbol{x}, \boldsymbol{\theta}_S), \tag{3}
$$

where  $\ell(x, \theta)$  is the loss for model  $\theta_S$  computed on x. We can solve the optimization problem defined in Eq. [3](#page-2-0) by stochastic gradient descent (the dependence of Eq. [3](#page-2-0) on  $\tilde{x}$ ) is obtained by expanding  $\theta_S$  as in Eq. [2\)](#page-1-0). A model trained on synthetic samples  $\tilde{x}^*$  reduces the prediction loss on the original training set. Moreover, using a distribution of initializations  $p(\theta_0)$  makes the distilled samples independent from the specific model initialization values.

Algorithm [1](#page-2-1) shows the pseudocode for buffer distillation. We refer to the loop that updates the distilled images as outer loop and to the one that updates the model as inner loop.

Our buffer distillation has some distinguishing characteristics with respect to the original Dataset Distillation [\[Wang](#page-6-9) *et al.*[, 2018\]](#page-6-9).

While Dataset Distillation learns the learning rate  $\eta$  used in the inner loop, we decided to fix it. In fact, Distilled Replay, during training, uses the same learning rate for past and current examples. If the examples were distilled to work with custom learning rates, they would lose their effectiveness when used with a different one. Therefore, during distillation the learning rate is the same used by the model during continual learning.

Another important difference with respect to Dataset Distillation is that our approach uses a different loss function. Instead of measuring the loss of the model on a minibatch of training data on the inner last step, our algorithm does that at each steps and backpropagate on their sum. As a result, buffer distillation is equivalent to backpropagating the gradient at each inner step and then updating the distilled images once the inner training is over. This process is graphically described by Figure [1](#page-2-2)

We can summarize our buffer distillation in three steps:

- 1. Do S steps of gradient descent on the distilled images, obtaining  $\theta_1, \ldots, \theta_s$ .
- 2. Evaluate each model on a minibatch of training data  $x_t$ getting the loss  $L_{tot} = \sum_{i=1}^{S} \ell(x_r, \theta_i)$ .
- 3. Compute  $\nabla_{\tilde{\boldsymbol{x}}} L_{tot}$  and update the distilled images.

Distilled Replay Training Distilled Replay combines a replay policy with the buffer learned by buffer distillation. Distilled Replay is designed to work with very small buffers, comprising as little as a single pattern per class.

During training, the replay policy builds each minibatch with elements sampled from the current dataset  $D_t^{tr}$  and patterns from the buffer  $B_t$ . The combination of old and new data allows the model to learn the current experience while mitigating forgetting on past ones.

At the end of the  $t$ -th experience, we randomly sample a certain amount of elements per class from the current training set and use them to initialise the memory  $m_t$ . We then apply buffer distillation to learn the synthetic memory  $\tilde{m}_t$ . Finally,

<span id="page-2-1"></span>

### Algorithm 1 Buffer Distillation

**Input:**  $p(\theta_0)$ : distribution of initial weights; M: number of distilled samples

**Input:**  $\alpha$ : step size; R: number of outer steps;  $\eta$ : learning rate Input: S: number of inner inner steps.

- 1: Initialize  $\tilde{\boldsymbol{x}} = {\{\tilde{x}_i\}}_{i=1}^M$
- 2: for all outer steps  $r = 1$  to R do<br>3: Get a minibatch of real training
- 3: Get a minibatch of real training data  $\boldsymbol{x}_r = \{x_j\}_{j=1}^n$
- 4: Sample a batch of initial weights  $\boldsymbol{\theta}_0^{(j)} \sim p(\boldsymbol{\theta}_0)$
- 5: **for all** sampled  $\theta_0^{(j)}$  do
- 6: for all inner steps  $s = 1$  to S do
- 7: Compute updated parameter with GD:  $\theta_s^{(j)} = \theta_{s-1}^{(j)}$   $\eta\nabla_{\boldsymbol{\theta}_{s-1}^{(j)}}\ell(\tilde{\boldsymbol{x}},\boldsymbol{\theta}_{s-1}^{(j)})$
- 8: Evaluate the objective function on real training data:  $\mathcal{L}^{(s,j)} = \ell(\bm{x}_r, \bm{\theta}_s^{(j)})$
- 9: end for
- 10: end for
- 11: Update  $\tilde{\boldsymbol{x}} \leftarrow \tilde{\boldsymbol{x}} \alpha \nabla_{\tilde{\boldsymbol{x}}} \sum_s \sum_j \mathcal{L}^{(s,j)}$

12: end for

Output: distilled data  $\tilde{x}$ 

<span id="page-2-2"></span>Image /page/2/Figure/29 description: This is a flowchart illustrating a process involving distilled examples and multiple functions. The process starts with 'Distilled examples' feeding into a function labeled 'f sub theta 0'. This function then feeds into a loss calculation 'L(f sub theta 0, x sub t)'. Similarly, 'f sub theta 0' updates 'f sub theta 1', which then feeds into 'L(f sub theta 1, x sub t)'. This pattern continues with 'f sub theta 1' updating 'f sub theta 2', which feeds into 'L(f sub theta 2, x sub t)'. All three loss calculations are then fed into a 'SUM' box, which in turn feeds into 'Accumulated Loss'.

Figure 1: Schematic representation of Buffer Distillation update. The gradient is backpropagated to the distilled examples following the computation path defined by the solid arrows. The accumulated loss sums the loss computed on each step.

as shown in Figure [2,](#page-3-0) we add the distillation result to the distilled buffer. Algorithm [2](#page-3-1) shows the pseudocode for the entire training loop.

## 5 Experiments

We used the Average Accuracy [\[Chaudhry](#page-6-20) *et al.*, 2019a] as the main metric to monitor forgetting of previous knowledge. After training on the t-th experience, the average accuracy is evaluated by averaging on all experiences encountered so far:

$$
\mathcal{A}_t = \frac{1}{t} \sum_{i=1}^t A(\boldsymbol{\theta}, D_i), \tag{4}
$$

where  $A(\theta, D_i)$  is the accuracy on dataset  $D_i$  from experience  $E_i$  obtained with a model parameterized by  $\theta$ .

We compared our approach against 5 different continual learning strategies:

<span id="page-3-0"></span>Image /page/3/Figure/0 description: The image depicts a diagram illustrating a distillation process. On the left, a green rectangle labeled 'Distilled Buffer' contains four small images of handwritten digits, appearing to be '2' and '3'. A dashed arrow points from the 'Current experience D2' box to a stack of two black rectangles, one with a '2' and the other with a '3'. A solid arrow goes from this stack to a yellow trapezoid labeled 'Distillation'. Another solid arrow originates from the 'Distillation' trapezoid and points to the 'Distilled Buffer' box. Finally, an arrow goes from the 'Current experience D2' box to the 'Distillation' trapezoid.

Figure 2: On experience  $t$ , a set of training examples is sampled from the current dataset, distilled and added to the buffer. The distilled samples will then be replayed alongside the next experience dataset.

<span id="page-3-1"></span>Algorithm 2 Distilled Replay Training 1:  $v$  ← list of datasets 2:  $B_0 \leftarrow$  list of memories 3: for  $t = 1$  to  $T$  do 4:  $D_t^{tr}, D_t^{ts} \sim E_t$ 5:  $v$  insert( $D_t^{ts}$ ) 6: for  $q = 1$  to Q do 7:  $mb_q = \text{sample}(D_t^{tr})$ ) {Sample from dataset} 8:  $b_q = B_t \cup mb_q$  {Create minibatch} 9:  $\qquad \boldsymbol{\theta}_q \leftarrow \boldsymbol{\theta}_{q-1} - \nabla_{\boldsymbol{\theta}_{q-1}} \ell(b_q, \, \boldsymbol{\theta}_{q-1})$ 10: end for 11:  $m_t \sim D_t^{tr}$ 12:  $\tilde{m}_t \leftarrow$  buffer distillation $(m_t, D_t^{tr})$ 13:  $B_t \leftarrow B_{t-1} \cup \tilde{m}_t$ 14: for all  $D_i^{ts}$  in v do 15: test $(\theta_Q, D_i^{ts})$ 16: end for 17: end for

- Naive (LB) trains the model continually without taking any measure to prevent forgetting. We use this strategy as a Lower Bound (LB) for continual learning performance.
- Simple Replay (SR) stores a buffer of examples randomly extracted from previous datasets and uses them to rehearse previous experiences.
- Cumulative (UB) trains the model continually on the union of the datasets from the current and all the past experiences. Cumulative training keeps all the past data. Therefore, we used it as Upper Bound (UB) for the continual learning performance.
- iCaRL [\[Rebuffi](#page-6-21) *et al.*, 2017] a dual-memory algorithm which combines knowledge distillation and nearest class mean classification.
- Maximal Interfered Retrieval (MIR) [\[Aljundi](#page-6-8) *et al.*, 2019] a dual-memory algorithm which selects the samples to be replayed based on how much their accuracy would drop after a training step on the current minibatch of data.

We measured the ability of the six strategies to prevent forgetting by using a single sample per class. We used a Multilayer Perceptron with one hidden layer of 500 units in the D-IL scenario and a LeNet5 [\[LeCun](#page-6-22) *et al.*, 1998] in the C-IL scenario.

We experimented with four popular continual learning benchmarks for image classification: Permuted MNIST [\[Goodfellow](#page-6-23) *et al.*, 2015], Split MNIST [Zenke *et al.*[, 2017\]](#page-6-24), Split Fashion MNIST [Xiao *et al.*[, 2017\]](#page-6-25) and Split CIFAR10 [\[Lopez-Paz and Ranzato, 2017\]](#page-6-26).

Permuted MNIST is a Domain-incremental scenario in which each experience is constructed by applying a fixed random permutation to all the MNIST images. The permutation only changes at the end of each experience. We used 10 experiences in total. The other benchmarks are class-incremental benchmarks in which each experience is composed by examples from two classes. In this setup, the number of classes increases every time a new experience is introduced. Therefore, the number of experiences is 5 for each benchmark.

### 5.1 Results

Table [1](#page-4-0) reports the Average Accuracy after training on each experience for all the evaluated strategies. Distilled Replay consistently outperforms the other methods, often by a large margin. In particular, neither iCaRL nor MIR are able to surpass Distilled Replay in the challenging continual learning scenario used in our experiments (single-epoch, replay buffers with one pattern per class). On Permuted MNIST, after the last experience, the accuracies of the compared methods drop between 83% and 89%. Our Distilled Replay is able to reach around 90% accuracy at the end of the last experience.

In C-IL benchmarks, Distilled Replay outperforms the other strategies by a larger margins than in the D-IL scenario. Table [3b](#page-4-0) shows the performance on Split MNIST. iCaRL and MIR obtained 77% and 59% accuracy respectively, while Distilled Replay achieves an accuracy of 82%. Figure [5](#page-4-1) shows the patterns in the replay buffer of Simple Replay and Distilled Replay. The ones used by standard replay are simply patterns taken from the training datasets. The patterns in the buffer of Distilled Replay shows a white background and a thicker digit contour. The performance of Distilled Replay and Simple Replay differs not only in accuracy values but also in their trajectories: Simple Replay accuracy degrades faster as the training progresses. To highlight this phenomenon, Figure [4](#page-4-2) reports the accuracies on each experience throughout learning. We can see how dataset distillation maintains a higher accuracy on past experiences. This results in a more stable average accuracy.

More challenging C-IL benchmarks such as Split Fashion MNIST (Table [3c\)](#page-4-0) and Split CIFAR-10 (Table [3d\)](#page-4-0) show similar differences between Distilled replay performances and the ones of the compared strategies. Distilled Replay outperforms the other methods, but the absolute performance of all six strategies is lower than on Split MNIST.

From Table [3d,](#page-4-0) we can observe that on Split CIFAR-10 there is a large drop in performance compared to the previous benchmarks. This is consistent for all the evaluated strategies. In Section [6](#page-5-0) we highlight some of the issues that may explain the reduced performance of Distilled Replay on challenging data consisting of complex patterns.

### 5.2 Ablation Study

Our buffer distillation process introduces significant differences as compared to the original Dataset Distillation technique [Wang *et al.*[, 2018\]](#page-6-9) (see Section [4\)](#page-1-1). Therefore, we ran

<span id="page-4-0"></span>

|                                                                        | <b>UB</b>                       | LB                              | DR                              | <b>SR</b>                       | iCaRL                           | <b>MIR</b>                      |  |                                                     | <b>UB</b>                | LB                       | <b>DR</b>                | <b>SR</b><br>$\mathbb{H}$ | iCaRL                    | MIR                      |  |
|------------------------------------------------------------------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|---------------------------------|--|-----------------------------------------------------|--------------------------|--------------------------|--------------------------|---------------------------|--------------------------|--------------------------|--|
| $\boldsymbol{E_6}$<br>$E_7$<br>$\boldsymbol{E_8}$<br>$E_9$<br>$E_{10}$ | .96<br>.96<br>.96<br>.96<br>.97 | .91<br>.89<br>.87<br>.84<br>.83 | .93<br>.92<br>.91<br>.91<br>.90 | .91<br>.90<br>.88<br>.88<br>.88 | .92<br>.91<br>.90<br>.89<br>.89 | .91<br>.90<br>.89<br>.85<br>.83 |  | $E_{2}$<br>$E_{3}$<br>$E_4$<br>$\boldsymbol{E_{5}}$ | .96<br>.94<br>.93<br>.91 | .49<br>.33<br>.25<br>.20 | .93<br>.89<br>.87<br>.82 | .88<br>.70<br>.66<br>.61  | .91<br>.85<br>.81<br>.77 | .92<br>.78<br>.68<br>.59 |  |
|                                                                        | (a) Permuted MNIST              |                                 |                                 |                                 |                                 |                                 |  |                                                     | (b) Split MNIST          |                          |                          |                           |                          |                          |  |
|                                                                        | UB                              | LB                              | DR                              | <b>SR</b>                       | iCaRL                           | <b>MIR</b>                      |  |                                                     | $_{\rm{UB}}$             | LB                       | DR                       | <b>SR</b>                 | iCaRL                    | <b>MIR</b>               |  |
| $E_{2}$<br>$E_{3}$<br>$E_4$<br>$E_{\rm 5}$                             | .93<br>.84<br>.76<br>.78        | .50<br>.33<br>.30<br>.19        | .84<br>.67<br>.63<br>.63        | .74<br>.55<br>.54<br>.48        | .82<br>.66<br>.59<br>.60        | .76<br>.55<br>.55<br>.45        |  | $E_{2}$<br>$E_{3}$<br>$E_4$<br>$E_{\rm 5}$          | .56<br>.43<br>.38<br>.35 | .28<br>.21<br>.18<br>.14 | .52<br>.34<br>.28<br>.24 | .43<br>.29<br>.21<br>.19  | .41<br>.29<br>.23<br>21  | .49<br>.32<br>.18<br>.19 |  |

(c) Split Fashion MNIST

(d) Split CIFAR-10

Table 1: Average accuracies of the six tested methods on the four benchmarks. The leftmost column of each table reports the experience up to which the accuracy is averaged. For Permuted MNIST, we report the average accuracy starting from the 6-th experience. In fact, the last experiences better represents the overall performance, since the accuracy is averaged over all experiences seen so far.

<span id="page-4-2"></span>Image /page/4/Figure/4 description: The image displays a plot with five subplots arranged vertically. Each subplot shows the average accuracy over training steps for three different methods: Distilled Replay (solid blue line), Simple Replay (dashed orange line), and Naive (dotted gray line). The x-axis for all subplots represents the training steps, ranging from 0 to 100. The y-axis for all subplots represents the average accuracy, ranging from 0 to 1. The subplots are labeled Exp 1 through Exp 5 from top to bottom. In Exp 1, Distilled Replay and Simple Replay maintain high accuracy throughout, while Naive starts lower and increases. In Exp 2, all three methods show an initial increase in accuracy, with Distilled Replay and Simple Replay reaching high levels, and Naive also improving but lagging slightly. Exp 3 shows a similar pattern to Exp 2, with a slight dip and recovery in accuracy for Distilled Replay and Simple Replay. Exp 4 demonstrates a sharp increase in accuracy for all methods around training step 40, with Distilled Replay and Simple Replay achieving high accuracy, and Naive also performing well. Exp 5 shows a delayed but rapid increase in accuracy for all methods, with Distilled Replay and Simple Replay reaching near-perfect accuracy by the end of the training steps.

Figure 4: Accuracies on each S-MNIST experience. Without replaying any data, the performance on previous experiences drop. By using distilled replay, we manage to get higher performance compared to standard replay.

<span id="page-4-1"></span>Image /page/4/Figure/6 description: The image displays two grids of handwritten digits on a black background. The left grid contains the digits 0, 1, 2, 3 in the top row and 4, 5, 6, 7 in the bottom row. The right grid contains eight images of digits, each appearing somewhat distorted or noisy, with the digits 0, 1, 2, 3 in the top row and 4, 5, 6, 7 in the bottom row.

Figure 5: Replay memory content of Simple Replay (left) and Distilled Replay (right). Distilled samples highlight the most representative features of the input patterns.

<span id="page-4-3"></span>Image /page/4/Figure/8 description: The image is a line graph showing the average accuracy versus buffer size. The x-axis is labeled "Buffer Size" and ranges from 0 to 100. The y-axis is labeled "Average Accuracy" and ranges from 0.0 to 1.0. There are two lines on the graph: a solid blue line representing "Buffer Distillation" and a dashed orange line representing "Dataset Distillation". Both lines start at an accuracy of 1.0 at a buffer size of 0. The accuracy for both methods drops significantly around buffer sizes of 25, 50, and 75, and then recovers to a certain level. The "Buffer Distillation" line generally shows slightly higher accuracy than the "Dataset Distillation" line, especially after the drops.

Figure 6: Comparison of two versions of the distilled replay algorithm on Split MNIST. One version is based on the original Dataset Distillation algorithm [Wang *et al.*[, 2018\]](#page-6-9), while the other uses our buffer distillation.

experiments on Split MNIST to validate the importance of these modifications in a continual learning scenario. The hyperparameters have been selected as follows. We kept the same number of inner and outer steps of distillation in both algorithms, so that the computation time is approximately equal. Instead, the learning rate of the outer update was selected by validating on different values (i.e. 0.05, 0.1, 0.5). In particular, we found that increasing the outer learning rates from 0.1 to 0.5 led to a better performance in Dataset Distillation.

Figure [6](#page-4-3) shows the learning curves of the Dataset Distillation and Buffer Distillation techniques. As soon as the model starts learning on the second experience, our distillation process outperforms Dataset Distillation, supporting the observation that the distilled samples have higher quality.

### 5.3 Computational Times

The Buffer Distillation process scales linearly with the number of inner steps. In a continual learning setting, we con-

<span id="page-5-1"></span>Image /page/5/Figure/0 description: The image contains two scatter plots with lines connecting the data points. The left plot is titled "Outer Steps" on the x-axis and "Distillation time (minutes)" on the y-axis. It shows four data points: (0, 9), (100, 24), (150, 40), and (300, 73). The right plot is titled "Inner Steps" on the x-axis and "Distillation time (minutes)" on the y-axis. It shows four data points: (10, 24), (20, 38), (30, 50), and (40, 66).

Figure 7: Average time for a single buffer distillation on Split CI-FAR10. On the left, we show the time as a function of the number of outer steps (inner steps fixed to 10). On the right, we show the time as a function of the inner steps (outer steps fixed to 80).

tinually adapt the model and, consequently, the buffer. This requires the model to be able to learn from multiple passes over data. Therefore, we experimented with large values for the inner steps. However, the high number of steps in the inner loop contributed to increase the computational cost of the distillation. Notice that, differently from other popular continual learning strategies (e.g. GEM [\[Lopez-Paz and Ran](#page-6-26)[zato, 2017\]](#page-6-26)), whose computation mainly occurs during continual training, the distillation process is independent of the training on the current experience. Therefore, it is possible to perform Buffer Distillation in parallel to training as soon as a new experience arrives. Figure [7](#page-5-1) reports the average time of the distillation process executed on a GPU Nvidia V100. The data comes from the distillation of Split CIFAR-10 experiences. In our experiments, the best configurations in terms of final average accuracy used 80 outer steps and 20 inner steps, with an average time of 38 minutes for each buffer distillation. While this is not a prohibitive amount of computational time, it has to be multiplied by the number of experiences in the stream (except for the last one which is not distilled), making the Distilled Replay a relatively expensive strategy in terms of computational times.

## <span id="page-5-0"></span>6 Discussion

The main objective of our experimental analysis was to test whether replay strategies with small buffers of one pattern per class were able to mitigate forgetting.  $\frac{1}{1}$  $\frac{1}{1}$  $\frac{1}{1}$  The results show that, in this small buffer regime, the use of real patterns sampled from the dataset may not be sufficient to recover performance of previous experiences. Instead, we show that highly informative samples generated by Buffer Distillation allow to mitigate forgetting. Building on our results, we can also identify some additional insights and issues of Distilled Replay worth exploring in further developments.

Independence of distillation processes. Since the buffer distillation process is applied separately for each experience, the synthetic samples are optimized without taking into account previous (and future) experiences. This makes the distillation process easier but it also brings possible downsides. For example, distilling samples of similar classes belonging to different experiences may introduce ambiguous features and increase the forgetting on such classes. Since we keep a single example per class, similar samples of different classes would negatively impact the final accuracy. Datasets such as Fashion MNIST, containing a high number of classes similar to each other (e.g. t-shirt, shirt, coat, dress, pullover), may be affected by this problem.

Distilled Replay with complex architectures. The results we showed for C-IL benchmarks use a LeNet5 architecture [\[LeCun](#page-6-22) *et al.*, 1998]. To improve the results on S-CIFAR-10 we did some preliminary experiments using a ResNet architecture [He *et al.*[, 2016\]](#page-6-27) together with Distilled Replay. However, we were not able to distill useful samples. The results (not shown in the paper) suggest that the buffer distillation process struggles with more complex architectures.We hypothesize that the optimization of the distilled examples is too challenging for gradient descent on sufficiently complex models. In fact, the distillation objective requires the backpropagation of the gradient through multiple gradient descent steps. For sufficiently complex architectures, this would result in a large computational graph which may suffer from vanishing or exploding gradients issues [\[Hochreiter, 1991\]](#page-6-28).

Robustness to continual training. Buffer Distillation is able to learn robust samples which better mitigate forgetting than the ones generated by the original Dataset Distillation. This is mainly due to the fact that buffer distillation optimizes the loss for each point of the learning trajectory. Therefore, the outer updates takes into consideration to what extent each distilled image influences the learning trajectory of the model. As a result, buffer distillation produces patterns which are robust to small parameter changes. This is important in a continual learning setting, where the model must continually adapt to novel experiences.

## 7 Conclusion and Future Work

Replay based methods are among the most effective continual learning strategies. In this work, we introduced a novel replay strategy called Distilled Replay which combines replay with Buffer Distillation, a process that generates a small buffer of highly informative samples. In particular, we studied whether keeping in the buffer a single pattern per class is sufficient to recover most of the original performance. When compared to other replay strategies like iCaRL and MIR, Distilled Replay shows superior results. The ability of Buffer Distillation to learn highly informative patterns is crucial to boost the performance of replay with small buffers.

By leveraging recent works about novel dataset condensation mechanisms [Zhao *et al.*[, 2021;](#page-6-29) [Zhao and Bilen, 2021\]](#page-6-30), it would be possible to improve the computational efficiency of Distilled Replay. Future works could also study the performance of Distilled Replay in very constrained settings where it is required to store less than one pattern per class, for example by iteratively applying distillation on the buffer itself. Ultimately, we hope that our work will foster the study of replay strategies in the small buffer regime, where it is only possible to store few patterns. This would help in the development of more efficient and sustainable continual learning systems, able to operate in the real-world constrained settings.

<span id="page-5-2"></span> $1$ The code along with the configuration files needed to reproduce our results are available at [https://github.com/andrew-r96/ Distille](https://github.com/andrew-r96/ DistilledReplay)[dReplay](https://github.com/andrew-r96/ DistilledReplay)

# References

- <span id="page-6-8"></span>[Aljundi *et al.*, 2019] Rahaf Aljundi, Eugene Belilovsky, Tinne Tuytelaars, Laurent Charlin, Massimo Caccia, Min Lin, and Lucas Page-Caccia. Online Continual Learning with Maximal Interfered Retrieval. In *Advances in Neural Information Processing Systems 32*, pages 11849–11860, 2019.
- <span id="page-6-15"></span>[Asghar *et al.*, 2019] Nabiha Asghar, Lili Mou, Kira A Selby, Kevin D Pantasdo, Pascal Poupart, and Xin Jiang. Progressive Memory Banks for Incremental Domain Adaptation. In *International Conference on Learning Representations*, 2019.
- <span id="page-6-12"></span>[Chaudhry et al., 2018] Arslan Chaudhry, Puneet K. Dokania, Thalaiyasingam Ajanthan, and Philip H. S. Torr. Riemannian Walk for Incremental Learning: Understanding Forgetting and Intransigence. In *Proceedings of the European Conference on Computer Vision (ECCV)*, pages 532–547, 2018.
- <span id="page-6-20"></span>[Chaudhry et al., 2019a] Arslan Chaudhry, Marc'Aurelio Ranzato, Marcus Rohrbach, and Mohamed Elhoseiny. Efficient Lifelong Learning with A-GEM. In *ICLR*, 2019.
- <span id="page-6-7"></span>[Chaudhry et al., 2019b] Arslan Chaudhry, Marcus Rohrbach, Mohamed Elhoseiny, Thalaiyasingam Ajanthan, Puneet K Dokania, Philip H S Torr, and Marc'Aurelio Ranzato. On Tiny Episodic Memories in Continual Learning. *arXiv*, 2019.
- <span id="page-6-16"></span>[Cossu *et al.*, 2020] Andrea Cossu, Antonio Carta, and Davide Bacciu. Continual Learning with Gated Incremental Memories for sequential data processing. In *Proceedings of the 2020 International Joint Conference on Neural Networks (IJCNN 2020)*, 2020.
- <span id="page-6-10"></span>[Farquhar and Gal, 2019] Sebastian Farquhar and Yarin Gal. Towards Robust Evaluations of Continual Learning. In *Privacy in Machine Learning and Artificial Intelligence Workshop, ICML*, 2019.
- <span id="page-6-2"></span>[French, 1999] Robert M. French. Catastrophic forgetting in connectionist networks. *Trends in Cognitive Sciences*, 3:128–135, 4 1999.
- <span id="page-6-23"></span>[Goodfellow *et al.*, 2015] Ian J. Goodfellow, Mehdi Mirza, Da Xiao, Aaron Courville, and Yoshua Bengio. An empirical investigation of catastrophic forgetting in gradient-based neural networks, 2015.
- <span id="page-6-3"></span>[Grossberg, 1980] Stephen Grossberg. How does a brain build a cognitive code? *Psychological Review*, 87(1):1–51, 1980.
- <span id="page-6-27"></span>[He *et al.*, 2016] K. He, X. Zhang, S. Ren, and J. Sun. Deep residual learning for image recognition. In *2016 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 770– 778, 2016.
- <span id="page-6-28"></span>[Hochreiter, 1991] Sepp Hochreiter. Untersuchungen zu dynamischen neuronalen netzen, 1991.
- <span id="page-6-13"></span>[Kirkpatrick *et al.*, 2017] James Kirkpatrick, Razvan Pascanu, Neil Rabinowitz, Joel Veness, Guillaume Desjardins, Andrei A Rusu, Kieran Milan, John Quan, Tiago Ramalho, Agnieszka Grabska-Barwinska, Demis Hassabis, Claudia Clopath, Dharshan Kumaran, and Raia Hadsell. Overcoming catastrophic forgetting in neural networks. *PNAS*, 114(13):3521–3526, 2017.
- <span id="page-6-0"></span>[Krizhevsky *et al.*, 2012] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. In F. Pereira, C. J. C. Burges, L. Bottou, and K. Q. Weinberger, editors, *Advances in Neural Information Processing Systems*, volume 25. Curran Associates, Inc., 2012.
- <span id="page-6-22"></span>[LeCun *et al.*, 1998] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86:2278–2323, 1998.

- <span id="page-6-1"></span>[Lesort et al., 2020] Timothée Lesort, Vincenzo Lomonaco, Andrei Stoian, Davide Maltoni, David Filliat, and Natalia Díaz-Rodríguez. Continual learning for robotics: Definition, framework, learning strategies, opportunities and challenges. *Information Fusion*, 58:52–68, 6 2020.
- <span id="page-6-4"></span>[Lomonaco and Maltoni, 2017] Vincenzo Lomonaco and Davide Maltoni. CORe50: A New Dataset and Benchmark for Continuous Object Recognition. In *Proceedings of the 1st Annual Conference on Robot Learning*, volume 78, pages 17–26, 2017.
- <span id="page-6-26"></span>[Lopez-Paz and Ranzato, 2017] David Lopez-Paz and Marc'Aurelio Ranzato. Gradient episodic memory for continual learning. *Advances in Neural Information Processing Systems*, 2017-December:6468–6477, 6 2017.
- <span id="page-6-17"></span>[McClelland *et al.*, 1995] James L. McClelland, Bruce L. Mc-Naughton, and Randall C. O'Reilly. Why there are complementary learning systems in the hippocampus and neocortex: Insights from the successes and failures of connectionist models of learning and memory. *Psychological Review*, 102:419–457, 1995.
- <span id="page-6-21"></span>[Rebuffi *et al.*, 2017] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H Lampert. iCaRL: Incremental Classifier and Representation Learning. In *The IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2017.
- <span id="page-6-14"></span>[Rusu *et al.*, 2016] Andrei A Rusu, Neil C Rabinowitz, Guillaume Desjardins, Hubert Soyer, James Kirkpatrick, Koray Kavukcuoglu, Razvan Pascanu, and Raia Hadsell. Progressive Neural Networks. *arXiv*, 2016.
- <span id="page-6-19"></span>[Shin *et al.*, 2017] Hanul Shin, Jung Kwon Lee, Jaehong Kim, and Jiwon Kim. Continual Learning with Deep Generative Replay. In I Guyon, U V Luxburg, S Bengio, H Wallach, R Fergus, S Vishwanathan, and R Garnett, editors, *Advances in Neural Information Processing Systems 30*, pages 2990–2999. Curran Associates, Inc., 2017.
- <span id="page-6-5"></span>[Sun *et al.*, 2020] Fan-Keng Sun, Cheng-Hao Ho, and Hung-Yi Lee. LAMOL: LAnguage MOdeling for Lifelong Language Learning. In *ICLR*, 2020.
- <span id="page-6-6"></span>[Thrun, 1995] Sebastian Thrun. A Lifelong Learning Perspective for Mobile Robot Control. In Volker Graefe, editor, *Intelligent Robots and Systems*, pages 201–214. Elsevier Science B.V., Amsterdam, 1995.
- <span id="page-6-11"></span>[van de Ven and Tolias, 2019] Gido M. van de Ven and Andreas S. Tolias. Three scenarios for continual learning. *arXiv*, 4 2019.
- <span id="page-6-18"></span>[van de Ven *et al.*, 2020] Gido M van de Ven, Hava T Siegelmann, and Andreas S Tolias. Brain-inspired replay for continual learning with artificial neural networks. *Nature Communications*, 11:4069, 2020.
- <span id="page-6-9"></span>[Wang *et al.*, 2018] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. *arXiv*, 11 2018.
- <span id="page-6-25"></span>[Xiao *et al.*, 2017] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv*, 8 2017.
- <span id="page-6-24"></span>[Zenke *et al.*, 2017] Friedemann Zenke, Ben Poole, and Surya Ganguli. Continual Learning Through Synaptic Intelligence. In *International Conference on Machine Learning*, pages 3987–3995, 2017.
- <span id="page-6-30"></span>[Zhao and Bilen, 2021] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation, 2021.
- <span id="page-6-29"></span>[Zhao et al., 2021] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching, 2021.
# Progressive trajectory matching for medical dataset distillation

Zhen Yu $^1$ , <PERSON> $^2$ , <PERSON><PERSON><PERSON> $^{1*}$ 

<sup>1</sup>National Institute of Health Data Science, Peking University <sup>2</sup>Wangxuan Institute of Computer Technology, Peking University

## Abstract

It is essential but challenging to share medical image datasets due to privacy issues, which prohibit building foundation models and knowledge transfer. In this paper, we propose a novel dataset distillation method to condense the original medical image datasets into a synthetic one that preserves useful information for building an analysis model without accessing the original datasets. Existing methods tackle only natural images by randomly matching parts of the training trajectories of the model parameters trained by the whole real datasets. However, through extensive experiments on medical image datasets, the training process is extremely unstable and achieves inferior distillation results. To solve these barriers, we propose to design a novel *progressive trajectory matching strategy* to improve the training stability for medical image dataset distillation. Additionally, it is observed that improved stability prevents the synthetic dataset diversity and final performance improvements. Therefore, we propose a dynamic overlap mitigation module that improves the synthetic dataset diversity by dynamically eliminating the overlap across different images and retraining parts of the synthetic images for better convergence. Finally, we propose a new medical image dataset distillation benchmark of various modalities and configurations to promote fair evaluations. It is validated that our proposed method achieves 8.33% improvement over previous state-of-the-art methods on average, and 11.7% improvement when  $ipc = 2$  (*i.e.*,, image per class is 2). Codes and benchmarks will be released.

## 1 Introduction

Effective accumulation, sharing, and management of largescale medical datasets across multi-cohorts/hospitals is key to improving the quality of medical services and research. However, issues such as privacy protection, data storage, transmission, preprocessing, and computational costs are extremely challenging. Dataset distillation [Geng *et al.*[, 2023;](#page-7-0) Yu *et al.*[, 2023;](#page-8-0) [Sachdeva and McAuley, 2023;](#page-8-1) [Lei and Tao,](#page-7-1)

<span id="page-0-0"></span>Image /page/0/Figure/7 description: The image displays a two-part figure. Part (a), titled "Overview," illustrates a process involving an "Original dataset" and a "Distilled dataset." The process includes "Cross-hospital sharing" with steps like "fusion," "transfer," and "verification," leading to a "Knowledge graph," "Vector database," and an "LLM diagnosis system." Part (b), titled "Performance," is a line graph showing "Evaluation Accuracy" on the y-axis against "Distillation Steps (K)" on the x-axis. The graph plots the performance of several methods: "OUR," "OUR (w/ overlap)," "DATM," and "FTD\_backend" at different percentages (0%, 30%, 60%, 100%). The accuracy ranges from approximately 0.40 to 0.65.

Figure 1: (a) overview for medical dataset distillation. (b) performance plot of various methods and using different probabilities of sampling the backend trajectories.

[2023\]](#page-7-1) seems a good candidate to solve previous barriers, which aims to condense a given dataset into a synthetic one, that preserves the information of the original one. Meanwhile training on the synthetic ones is expected to achieve similar performance compared with training on the original dataset.

Although existing dataset distillation methods have been evaluated on natural image datasets, including distributionbased matching in feature space and trajectory-based matching in parameter space, their effectiveness has not been comprehensively understood/verified for distilling medical image datasets. To our best knowledge, only [Li *et al.*[, 2022b\]](#page-7-2) follows the distillation paradigm of MTT [\[Cazenavette](#page-7-3) *et al.*, [2022\]](#page-7-3) and verified its effectiveness on sorely the chest X-ray images. Compared with natural images, as both the medical images and the analysis task exhibit extremely different characteristics and technical challenges, it is of the essence to establish a comprehensive benchmark for the medical image dataset distillation and identify the challenges with potential solutions.

We proposed a new benchmark, MDD with stats in Table [1,](#page-4-0) composed of six public medical datasets, covering a variety of modalities, analysis tasks, and resolutions. Through comprehensive analysis of the state-of-the-art methods as in Table [2](#page-4-1) and Table [3,](#page-5-0) we articulate *the summarized challenges in medical dataset distillation as follows.*

Degraded training stability of existing matching trajectory segments randomly. As shown in Figure [1b,](#page-0-0) we observed in medical image distillation that: (1) it is essential to match and distill the beginning/front-end part of expert trajectories; (2) random matching of trajectory segments fails to achieve stabilized and results of superior performances. As shown by the bottom four lines in Figure [1,](#page-0-0) they represent the probabilities of randomly matching the back-end trajectories at 0%, 30%, 60% and 100% respectively, and it can be observed that randomly matching more back-end trajectories results in poorer performance. In addition, even if the probability is 0, the lack of back-end trajectory information results in a significant performance gap compared to our method from the up two lines in Figure [1.](#page-0-0)

Lack of the diversity of synthetic images and saturated gradients in optimizations. As shown in Figure 1, when there are multiple synthetic images per class in the budget, the existing distillation methods tend to obtain similar synthetic images, a.k.a, their overlaps are high. This overlap is caused by the gradient update of the synthetic images as a whole, resulting in more saturated gradients of similar synthetic images as the distillation progresses. Lack of diversity in the synthetic ones tends to achieve inferior performances. We also observe using experiments that the overlap restricts the performance improvement at the end of distillation (in supplementary).

To address the aforementioned two challenges, we first design a novel progressive trajectory matching strategy to promote the stability of multi-step trajectory matching. Promoting stability incurs the lack of synthetic image diversity. To solve this, we propose a novel overlap prevention module, that maintains the diversity of synthetic images within classes and improves the convergence using the retraining strategy.

Our contributions are summarized as follows:

- We propose a new and comprehensive benchmark to evaluate the medical image dataset distillation. This benchmark covers a variety of conditions, including imaging modalities, image resolutions, and analysis tasks. Through this benchmark, we identify the challenges to distilling the medical image datasets.
- We propose a novel progressive trajectory matching strategy, scheduling the start and end points of the trajectory. This strategy proves to significantly improve the benchmark results.
- Stability incurs the diversity of learned synthetic datasets. We propose a novel module to prevent synthetic image overlaps and design a retraining strategy for better convergence.
- Our proposed method achieves the state-of-the-art results evaluated in various benchmarks.

## 2 Related Works

To our best knowledge, dataset distillation was first formally proposed by [Wang *et al.*[, 2018\]](#page-8-2) to synthesize a smaller synthetic dataset, so that once trained, it can match the test accuracy using the complete dataset. The mainstream framework can be further divided into distribution-based and trajectorybased matching methods.

Distribution Matching Methods: Distribution matching learns a synthetic dataset by approximating its feature distribution to the real feature distribution. The commonly used distance function includes metrics such as maximum mean difference (MMD). DM [\[Zhao and Bilen, 2023\]](#page-8-3) uses a classification encoder to extract features and pulls features of each class closer between the synthetic and real datasets. CAFE [Wang *et al.*[, 2022\]](#page-8-4) further improves the DM by aligning the layer-wise features formalized as a dynamic bi-level optimization. It seems to better capture the distribution of the entire dataset. Although DM ignores optimization steps in the parameter space matching and alleviates the memory requirements, imprecise estimation of the high-dimensional data distribution often leads to inferior performance. *Different from them, our work designs a novel strategy to progressively match the trajectory of parameter space.*

Single-step Trajectory Matching: The key idea under this regime is to train the synthetic parameters and synthetic data simultaneously by improving the consistency between the synthetic parameters and the buffer one trained by real datasets. This regime can be further divided into single-step and multi-step trajectory matching. DC [Zhao *et al.*[, 2020\]](#page-8-5) may be the first single-step matching method by maintaining consistent single-step gradients. Subsequently, DSA [\[Zhao](#page-8-6) [and Bilen, 2021\]](#page-8-6) improves the performance of DC by incorporating image augmentation functions in training(*e.g.,*scale, flip, etc). As these image enhancement methods are universally applicable, DSA has become a common part of all subsequent methods. *Different from them, our work belongs to the multi-step trajectory matching.*

Multi-step Trajectory Matching: MTT [\[Cazenavette](#page-7-3) *et al.*[, 2022\]](#page-7-3) may be the first multi-step matching method, which matches the long-range training dynamics. SMDD [\[Li](#page-7-2) *et al.*[, 2022b\]](#page-7-2) follows the MTT and prunes those difficult-tomatch parameters, ensuring the robustness of the synthesized dataset. FTD [Du *et al.*[, 2023a\]](#page-7-4) proposed to regularize the flatness of multiple trajectories, to avoid the accumulation of trajectory-matching errors and improve the network robustness. DATM [Guo *et al.*[, 2023\]](#page-7-5) finds that the training stage of the selected matching trajectory (*i.e.,* early or late stages) greatly affects the performance, and proposes using early trajectory for low-image per class (IPC) and later trajectory for high-IPC. SeqMatch [Du *et al.*[, 2023b\]](#page-7-6) finds it difficult to learn high-level features for high-IPC scenarios and proposes to divide the synthetic dataset into multiple subsets and distill each subset from the different stages of trajectories. From the perspective of *medical dataset distillation*: existing medical dataset distillation methods [Li *et al.*[, 2020;](#page-7-7) Li *et al.*[, 2022a;](#page-7-8) Li *et al.*[, 2022b\]](#page-7-2) are only verified on the X-ray modality and follow MTT. *Our work designs a novel strategy to progressively match the trajectory rather than randomly as the previous do. In addition, we proposed a novel overlapping prevention module involving the re-training strategy, while they only implicitly pre-set different subsets to update as SeqMatch did. Our methods exhibit less computational complexities. Besides all the method differences, we established a new medical dataset distillation benchmark enabling more diverse and* *comprehensive evaluations.*

## 3 Methods

### 3.1 Overall Framework

We define the original dataset and synthetic dataset as  $D =$  $\{(x_i, y_i)\}_{i=1}^{|\mathcal{D}|}$  and  $\mathcal{S} = \{(s_i, y_i)\}_{i=1}^{|\mathcal{S}|}$   $(|\mathcal{S}| \ll |\mathcal{D}|)$  respectively, where the data samples  $x_i, s_i \in \mathbb{R}^d$ , the class labels  $y_i \in \mathcal{Y} = \{0, 1, \dots, C - 1\}$  and C is the number of classes. Each class of  $S$  contains ipc (images per class) data pairs, and we evenly divide S into foundation subset  $S<sup>f</sup>$  and complement subset  $S^c$ . *i.e.*,  $|S| = |S^f| + |S^c| = \text{ipc} \times C$  and  $|\mathcal{S}^f| = |\mathcal{S}^c|$ . we define expert parameters  $\theta_t^{\mathcal{D}}$  as teacher network parameters trained on full images at the training step t, and synthetic parameters  $\theta_t^{\mathcal{S}}$  as the student network parameter trained on synthetic images, and a complete expert trajectory and synthetic trajectory can be represented as  $\left\{\theta_t^{\mathcal{D}}\right\}_{t=0}^T$  and  $\left\{\theta_{\frac{t}{L}}^{S}\right\}_{t=0}^{T}$ , where T denotes the training steps.

The overall architecture is shown in Figure [2.](#page-3-0) The goal of dataset distillation is to learn a synthetic dataset that *mimics the model performances using a real dataset* if training under the same configurations. To achieve this, we adopt the bi-level optimization framework of multi-step trajectory matching, where we simultaneously learn the synthetic student network trajectories using cross-entropy loss  $\mathcal{L}_c$ , meanwhile updating the synthetic images by matching the parameter trajectories between the synthetic student network and the buffer teacher network using  $\mathcal{L}_{match}$ .

As demonstrated in both analysis and introductions, existing trajectory matching methods experience *degraded training stability* on medical image datasets. To improve it, as shown in Figure 2, we propose a novel progressive matching strategy by scheduling the start point and end point of each synthetic trajectory under match. Specifically, instead of the random start/end point as existing methods did, we propose to *set the very beginning of the network parameters as the start point in each distillation step*. Additionally, we propose to *progressively increase the endpoint of the trajectory and reuse the intermediate synthetic images as in each start point* , which allows for the gentle learning of hard information in the back-end trajectory without disrupting the crucial earlystage information. More details are in section 3.2.

Improved stability strategies tend to promote similarities/overlaps among the distilled images of the same class. Therefore, the lack of diversity becomes an issue for improved performances and saturated gradients at the end of the distillation procedure (see supplementary). To solve it, as shown in Figure 2, we propose a novel image overlap prevention module, with a re-training strategy to improve the convergence using  $\mathcal{L}_{overlap}$ . More details are in section 3.3.

### 3.2 Progressive Trajectory Matching

The reasons for the inferior performance of random trajectory matching methods include: (1) the front-end information from the multi-expert trajectory is more stable and reliable compared to the back-end, which fails to utilize multi-expert. (2) the fitting of small segments between trajectories is fragmented and rigid, and classification loss can seriously conflict with matching loss. (3) learning from back-end trajectories can improve the recognition of hard samples, but it is important to ensure that previous information is not forgotten or overwritten.

The progressive matching mechanism we designed has the following characteristics: (1) Each distillation starts from 0, *i.e.,*  $\theta_0^S := \theta_0^D$ , progressively and non-forgetfully conducting ordered learning from easy to difficult. (2) The step size  $\overline{T}$ for each distillation gradually increases from 1 to  $T$ , which can greatly alleviate the conflict between classification and matching. (3) Our method can achieve a certain distillation effect in a very small time and storage cost, and even if trained to the last  $T$  step, our cost is also comparable to random matching. therefore,  $S$  can be an approximate solution of the following optimization problem:

$$
S = \underset{S \subset \mathbb{R}^d \times \mathcal{Y}, |S| = \text{ipc} \times C^{\theta_0 \sim P_{\theta_0}}}{\text{arg min}} \left[ \sum_{t=0}^T \frac{\mathbf{D}(\theta_t^{\mathcal{D}}, \theta_t^S)}{\mathbf{D}(\theta_0^{\mathcal{D}}, \theta_t^{\mathcal{D}})} \right] \tag{1}
$$

where  **is a distance metric of choice and t is a gradually** increasing trajectory step size. Each round of progressive matching reduces the distance between synthetic trajectory and multi-expert trajectories, and this fitting process is stable and orderly.

As shown in Figure [2,](#page-3-0) at the beginning of each distillation, we first sample initial parameters  $\theta_0^{\mathcal{D}}$  from multi-expert trajectories and use  $\theta_0^{\mathcal{D}}$  to initialize student parameters  $\theta_0^{\mathcal{S}}$ . Then, gradient descent updates are executed on the student parameters by reducing the classification loss of the synthetic data:

$$
\theta_t^{\mathcal{S}} = \theta_0^{\mathcal{S}} - \eta \cdot \nabla \ell \left( \mathcal{A} \left( \mathcal{S} \right); \theta_0^{\mathcal{S}} \right) \tag{2}
$$

where  $A$  is a differentiable data augmentation module pro-posed in DSA [\[Zhao and Bilen, 2021\]](#page-8-6), and  $\eta$  is the learning rate used to update the student network. Finally, at the end of each distillation, we update synthetic images  $\tilde{S}$  based on the parameter matching loss between  $\theta_t^{\mathcal{S}}$  and  $\bar{\theta}_t^{\mathcal{C}}$ :

$$
\mathcal{L}_{match} = \frac{\left\| \theta_t^{\mathcal{S}} - \theta_t^{\mathcal{D}} \right\|_2^2}{\left\| \theta_t^{\mathcal{D}} - \theta_0^{\mathcal{D}} \right\|_2^2}
$$
(3)

the updated synthetic images return to the initial stage and randomly sample expert trajectory parameters while increasing the step size for the next round of trajectory matching.

### 3.3 Dynamic Retraining and Overlap Mitigation

The progressive trajectory matching extracts more sufficient preliminary information from multi-experts, which is crucial for medical datasets. However, it introduces the issue of overlap, leading to lack of diversity. To address this, we divide S into foundation subset  $S^f$  and complement subset  $S^c$ , and amplify the distribution differences between  $S<sup>f</sup>$  and  $S<sup>c</sup>$  to enhance the diversity of synthetic images.

We proposed to use the maximum mean discrepancy (MMD) [\[Gretton](#page-7-9) *et al.*, 2012] to measure the distribution distance between the foundation subset  $S^f$  and complement subset  $\mathcal{S}^c$ . We define the overlap loss to minimize as:

$$
\mathcal{L}_{overlap} = \sum_{i=1}^{|\mathcal{S}^f|} \sum_{j=1}^{|\mathcal{S}^c|} \left( 2k \left( \mathcal{S}^f_i, \mathcal{S}^c_j \right) - k \left( \mathcal{S}^c_i, \mathcal{S}^c_j \right) - k \left( \mathcal{S}^f_i, \mathcal{S}^f_j \right) \right)
$$
\n(4)

<span id="page-3-0"></span>Image /page/3/Figure/0 description: This is a flowchart illustrating a process with multiple stages, labeled 'Start', '1 match', '2 match', 't match', 't + 1 match', and 'T match'. The flowchart depicts a series of interconnected nodes representing parameters (θ) and samples (S). There are three main operations indicated: 'Restart', 'Overlap', and 'Retraining'. The 'Restart' operation involves setting θ^S to θ^D and S^f to S^f. The 'Overlap' operation calculates L\_overlap between S^f and S^t. The 'Retraining' operation updates S^c to S^c[(m\_i + m\_{i+1})/2] when t equals m\_i. The diagram shows gradients flowing from parameter matching to sample updates, and iterative processes involving sample refinement and parameter updates across different stages.

Figure 2: Overall architectures. Using multiple buffer trajectories, the synthetic images and the labels are used to minimize cross-entropy loss and obtain the orange synthetic student trajectory parameters. By comparing the differences between synthetic and buffer trajectories, the gradients are back-propagated to the synthetic images and they are updated. We propose progressive trajectory matching, by iteratively going back to the original starting points for stable matching. We propose dynamic overlap mitigation and retraining techniques to improve synthetic image diversity.

where  $k(a, b) = \exp \left(-\frac{\|a-b\|^2}{2a^2}\right)$  $\frac{a-b\|^2}{2\sigma^2}$ )  $\cdot$  II $(a,b)$  denotes the kernel function, and  $\mathbb{I}(a, b)$  is an indicator function, taking the value 1 when the two items under comparison come from the same class and 0 otherwise.

Although overlap mitigation increases the diversity of synthetic images, we empirically found that its loss increases exponentially and is difficult to converge, as shown by the blue line in Figure [5a.](#page-6-0) To tackle this, we designed a retraining mechanism by replacing the complementary synthetic images with their historical ones and continuing the training as shown in Figure 2. Specifically, we divide the whole distillation procedure ranging from  $0$  to  $T$  proportionally into k retraining points, which are labeled as  $m_i$  and the set is defined as  $M = \{m_1, m_2, \ldots, m_k\}$ , and  $m_i$  is denoted as:  $m_i = T - \frac{T}{k+1} \cdot i$ . As shown in Figure [2,](#page-3-0) when the distillation reaches the  $t^{th}$  match, we verify whether t is within the retraining time point set M, *i.e.*,  $\mathbb{I}(\exists i : m_i = t)$  is equal to 1. We only re-train the complement subset  $S^c$  when it reaches the retraining point and replaces the synthetic image as the one in the middle of the previous retraining interval. *i.e.,*  $\mathcal{S}_t^c := \mathcal{S}_{(m_i + m_{i+1})/2}^c$ .

### 3.4 Overall Optimization

To sum up, the overall optimization is as follows, where  $\beta_1$ and  $\beta_2$  are hyperparameters.

$$
\min_{S,\theta^S} \mathcal{L}_c + \beta_1 \mathcal{L}_{match} + \beta_2 \mathcal{L}_{overlap}.
$$
 (5)

## 4 Experiments

### 4.1 Benchmark and Datasets

Due to the data modalities in medical datasets are diverse, the data dimensions are uncertain and the resolution is high, making it difficult to ensure that successful distillation methods in natural images can be well adapted to medical images. Therefore, a comprehensive medical dataset benchmark is needed to evaluate the performance of dataset distillation methods. We retrieved and divided public medical datasets [Yang *et al.*[, 2023;](#page-8-7) [Kather](#page-7-10) *et al.*, 2019; [Kermany](#page-7-11) *et al.*, 2018; Bilic *et al.*[, 2023;](#page-7-12) [Rahman](#page-7-13) *et al.*, 2021; [Tschandl](#page-8-8) *et al.*, 2018; [Al-Dhabyani](#page-7-14) *et al.*, 2020] into different and multiple subsets, aiming to clarify the evaluation objectives of each subset and cover a wide range of medical application scenarios. We only evaluate classification tasks and more complex tasks such as image detection and segmentation remain untapped, and this is a step-by-step process that we will explore next. The classification performance of all public datasets has been validated as effective in [Yang *et al.*[, 2023\]](#page-8-7), and the detailed information is shown in Table [1.](#page-4-0)

### High-Resolution Medical Benchmark

COVID19-CXR [\[Rahman](#page-7-13) *et al.*, 2021] is a chest X-ray dataset for COVID-19, consisting of 3616 COVID-19 positive cases along with 10192 normal, 6012 Lung Opacity and 1345 viral pneumonia images, which can serve as an excellent benchmark for high-density structure sensitive medical images. SKIN-HAM [\[Tschandl](#page-8-8) *et al.*, 2018] is a large collection of multi-source dermatoscopic images of pigmented lesions from different populations, which consists of 10,015 images with imbalanced distribution and can be considered as a suitable benchmark for dealing with imbalanced medical dataset. BREAST-ULS [\[Al-Dhabyani](#page-7-14) *et al.*, 2020] is a breast ultrasound dataset for breast cancer, which consists of 780 breast ultrasound images and is categorized into normal, benign and malignant, and can be used as a medical benchmark for high-resolution and low sample data scale.

### Mnist 2D/3D Medical Benchmark

PATHMNIST [\[Kather](#page-7-10) *et al.*, 2019] is a 9-class colon pathology dataset for predicting survival from colorectal cancer histology slides, which is resized to  $3 \times 28 \times 28$  similar to natural image datasets (CIFAR, Tiny ImageNet, *etc.*) and can serve as the standard MNIST dataset benchmark. OCTM-NIST [\[Kermany](#page-7-11) *et al.*, 2018] is a 4-class valid optical coherence tomography images for retinal diseases, which can be used as a single channel grayscale image benchmark in medical data. ORGAN3D [Bilic *et al.*[, 2023\]](#page-7-12) is a 11-class 3D computed tomography (CT) images from liver tumor segmentation dataset, and 3D bounding boxes are used to process these CT images into  $1 \times 28 \times 28 \times 28$  for body organ classification, which can be regarded as an appropriate benchmark for unique 3D data (CT, MRI, *etc.*) in medical field.

Table 1: Statistics of medical datasets used for the proposed benchmark, with comprehensive coverage.

<span id="page-4-0"></span>

| <b>Dataset Name</b>                   | Data Modality   | 2D/3D | <b>Classes Number</b> | <b>Sample Number</b> | <b>Image Shape</b>      |
|---------------------------------------|-----------------|-------|-----------------------|----------------------|-------------------------|
| PATHMNIST [Kather et al., 2019]       | Colon Pathology | 2D    | Multi-Class (9)       | 107,180              | $3 	imes 28 	imes 28$   |
| OCTMNIST [Kermany et al., 2018]       | Retinal OCT     | 2D    | Multi-Class (4)       | 109,309              | $1 	imes 28 	imes 28$   |
| ORGAN3D [Bilic et al., 2023]          | Abdominal CT    | 3D    | Multi-Class (11)      | 1,743                | $1 	imes 28 	imes 28$   |
| COVID19-CXR [Rahman et al., 2021]     | Chest X-ray     | 2D    | Multi-Class (4)       | 21,165               | $3 	imes 300 	imes 300$ |
| SKIN-HAM [Tschandl et al., 2018]      | Dermatoscope    | 2D    | Multi-Class (7)       | 10,015               | $3 	imes 600 	imes 450$ |
| BREAST-ULS [Al-Dhabyani et al., 2020] | Ultrasound      | 2D    | Multi-Class (3)       | 780                  | $3 	imes 500 	imes 500$ |

Table 2: Performances of ConvNet using various distillation methods on the **Mnist 2D/3D** medical datasets.

<span id="page-4-1"></span>

| IPC                                    | PATHMNIST $(28 \times 28)$ |                  | OCTMNIST $(28 \times 28)$ |                  | ORGAN3D $(28 \times 28 \times 28)$ |                  |
|----------------------------------------|----------------------------|------------------|---------------------------|------------------|------------------------------------|------------------|
|                                        | 2                          | 10               | 2                         | 10               | 2                                  | 10               |
| <b>Real Dataset</b>                    | $88.48 \pm 0.02$           |                  | $65.60 \pm 0.02$          |                  | $81.31 \pm 0.04$                   |                  |
| DC [Zhao <i>et al.</i> , 2020]         | $57.63 \pm 0.01$           | $69.79 \pm 0.01$ | $42.15 \pm 0.02$          | $60.00 \pm 0.01$ | $49.87 \pm 0.02$                   | $74.13 \pm 0.01$ |
| DM [Zhao and Bilen, 2023]              | $50.74 \pm 0.02$           | $78.20 \pm 0.01$ | $38.42 \pm 0.02$          | $58.84 \pm 0.01$ | $49.03 \pm 0.02$                   | $75.07 \pm 0.01$ |
| DSA [Zhao and Bilen, 2021]             | $58.56 \pm 0.02$           | $77.07 \pm 0.01$ | $48.56 \pm 0.02$          | $60.91 \pm 0.01$ | $50.13 \pm 0.02$                   | $74.82 \pm 0.01$ |
| CAFE [Wang <i>et al.</i> , 2022]       | $62.03 \pm 0.02$           | $73.17 \pm 0.01$ | $41.84 \pm 0.01$          | $62.67 \pm 0.01$ | $51.15 \pm 0.02$                   | $76.07 \pm 0.01$ |
| MTT [Cazenavette <i>et al.</i> , 2022] | $50.86 \pm 0.02$           | $69.47 \pm 0.01$ | $36.10 \pm 0.01$          | $54.67 \pm 0.01$ | $52.20 \pm 0.01$                   | $69.56 \pm 0.01$ |
| SMDD [Li <i>et al.</i> , 2022b]        | $51.53 \pm 0.02$           | $70.11 \pm 0.01$ | $37.63 \pm 0.01$          | $53.82 \pm 0.01$ | $52.46 \pm 0.02$                   | $70.27 \pm 0.01$ |
| HaBa [Liu <i>et al.</i> , 2022a]       | $49.10 \pm 0.04$           | $71.54 \pm 0.01$ | $35.18 \pm 0.03$          | $58.23 \pm 0.01$ | $51.54 \pm 0.01$                   | $70.49 \pm 0.01$ |
| FTD [Du <i>et al.</i> , 2023a]         | $53.47 \pm 0.02$           | $70.28 \pm 0.01$ | $42.61 \pm 0.02$          | $57.04 \pm 0.01$ | $53.13 \pm 0.01$                   | $71.34 \pm 0.01$ |
| DATM [Guo <i>et al.</i> , 2023]        | $54.75 \pm 0.02$           | $72.81 \pm 0.01$ | $43.67 \pm 0.02$          | $60.48 \pm 0.01$ | $54.07 \pm 0.01$                   | $72.42 \pm 0.01$ |
| SeqMatch [Du <i>et al.</i> , 2023b]    | $46.30 \pm 0.01$           | $72.54 \pm 0.01$ | $35.70 \pm 0.01$          | $59.21 \pm 0.01$ | $48.92 \pm 0.01$                   | $71.89 \pm 0.01$ |
| <b>OUR</b>                             | $67.01 \pm 0.01$           | $74.42 \pm 0.01$ | $49.59 \pm 0.03$          | $61.84 \pm 0.01$ | $56.28 \pm 0.01$                   | $73.95 \pm 0.01$ |

### 4.2 Baselines and Models

We compare our method with a series of latest and representative dataset distillation methods including DC [\[Zhao](#page-8-5) *et al.*, [2020\]](#page-8-5), DM [\[Zhao and Bilen, 2023\]](#page-8-3), DSA [\[Zhao and Bilen,](#page-8-6) [2021\]](#page-8-6), CAFE [Wang *et al.*[, 2022\]](#page-8-4), MTT [\[Cazenavette](#page-7-3) *et al.*, [2022\]](#page-7-3), SMDD [Li *et al.*[, 2022b\]](#page-7-2), HaBa [Liu *et al.*[, 2022a\]](#page-7-15), FTD [Du *et al.*[, 2023a\]](#page-7-4), DATM [Guo *et al.*[, 2023\]](#page-7-5) and Seq-Match [Du *et al.*[, 2023b\]](#page-7-6). Among them, SMDD is the only dataset distillation method for medical, which follows the work of MTT and uses parameter pruning to remove difficult to match parameters to ensure the robustness of synthesized dataset. We align with prior works in order to ensure a fair evaluation, we employ networks with instance normalization by default, and a 3-layer ConvNet [Liu *et al.*[, 2022b\]](#page-7-16) is used as the default distillation architecture, which can maximally adapt to the diversity of medical datasets and [\[Smith](#page-8-9) *et al.*[, 2023\]](#page-8-9) demonstrates its superior performance. We also evaluate the cross-architecture generalization performance of distilled images on other three standard deep network architectures: AlexNet [Alom *et al.*[, 2018\]](#page-7-17), LeNet [\[LeCun and](#page-7-18) [others, 2015\]](#page-7-18) and ResNet18 [He *et al.*[, 2016\]](#page-7-19).

### 4.3 Implementation Details

To ensure fairness in comparison, we follow up the experimental setup as stated in [Cui *et al.*[, 2022;](#page-7-20) [Lei and Tao, 2023\]](#page-7-1). In the buffer stage, we generate flat expert trajectories using the method similar to FTD [Du *et al.*[, 2023a\]](#page-7-4), and we also use the same suite of differentiable augmentations similar to DSA [\[Zhao and Bilen, 2021\]](#page-8-6) in the distillation and evaluation stage. Finally, in the evaluation stage after the synthetic dataset is generated, we train 10 randomly initialized network on it each time for 1000 training epochs using SGD optimizer, and report the mean and standard deviation of their accuracy on the real test set. Notice that all experiments were run on the server with four NVIDIA A40 (48G) GPUs.

## 4.4 Results

For the evaluation of distillation performance, we first generate synthetic datasets through candidate methods and train target networks using these datasets. Then, the performance of trained models is evaluated by the corresponding test set of the original dataset.

The distillation performance results for high-resolution and mnist 2D/3D medical datasets are shown in Table [3](#page-5-0) and Table [2,](#page-4-1) respectively. It is seen that our proposed method outperforms all state-of-the-art baseline methods in each medical dataset with a significant improvement. *e.g.,* we significantly improve the performance on COVID19-CXR by more than 13.9% on average. Similarly, on PathMNIST, the performance improvement is more substantial, exceeding 12.6% on average. Particularly, compared to the SMDD using the same chest X-ray dataset, we achieved an average improvement of 12.7%. In addition, we also compared our method with  $ipc = 2$  to the baseline methods with  $ipc = 2, 4, 6$ on PathMNIST dataset, as shown in Table [5.](#page-6-1) It can be intuitively seen that the performance of our synthetic dataset with  $inc = 2$  is similar to that of the other baseline methods with  $inc = 6$ , far exceeding the performance of the same  $inc = 2$ .

From the performance results, we have summarized the following observations: (1) the dataset distillation methods

<span id="page-5-0"></span>

|                                  | COVID19-CXR $(112 \times 112)$ |                  | BREAST-ULS $(112 \times 112)$ |                  | SKIN-HAM $(112 \times 112)$ |                  |
|----------------------------------|--------------------------------|------------------|-------------------------------|------------------|-----------------------------|------------------|
| <b>IPC</b>                       |                                | 10               |                               | 10               |                             | 10               |
| <b>Real Dataset</b>              | $90.22 \pm 0.01$               |                  | $74.00 \pm 0.07$              |                  | $70.17 \pm 0.02$            |                  |
| DC [Zhao <i>et al.</i> , 2020]   | $50.38 \pm 0.04$               | $55.29 \pm 0.01$ | $52.40 \pm 0.08$              | $55.00 \pm 0.03$ | $22.01 \pm 0.01$            | $36.84 \pm 0.03$ |
| DM [Zhao and Bilen, 2023]        | $49.84 \pm 0.03$               | $54.01 \pm 0.01$ | $50.07 \pm 0.03$              | $54.93 \pm 0.01$ | $34.74 \pm 0.01$            | $49.90 \pm 0.03$ |
| DSA [Zhao and Bilen, 2021]       | $57.51 \pm 0.01$               | $58.71 \pm 0.01$ | $51.93 \pm 0.07$              | $55.13 \pm 0.02$ | $38.84 \pm 0.03$            | $44.10 \pm 0.02$ |
| CAFE [Wang et al., 2022]         | $53.20 \pm 0.03$               | $55.78 \pm 0.01$ | $52.57 \pm 0.04$              | $53.80 \pm 0.02$ | $47.16 \pm 0.02$            | $50.35 \pm 0.01$ |
| MTT [Cazenavette et al., 2022]   | $50.27 \pm 0.03$               | $58.68 \pm 0.01$ | $57.67 \pm 0.05$              | $67.27 \pm 0.01$ | $31.33 \pm 0.04$            | $51.81 \pm 0.02$ |
| SMDD [Li et al., 2022b]          | $51.91 \pm 0.03$               | $58.55 \pm 0.01$ | $58.43 \pm 0.06$              | $67.60 \pm 0.01$ | $32.07 + 0.04$              | $50.76 \pm 0.02$ |
| HaBa [Liu <i>et al.</i> , 2022a] | $48.01 \pm 0.04$               | $58.73 \pm 0.01$ | $54.60 \pm 0.05$              | $63.53 \pm 0.02$ | $34.28 \pm 0.03$            | $51.50 \pm 0.02$ |
| FTD [Du et al., 2023a]           | $52.52 \pm 0.02$               | $59.38 \pm 0.01$ | $60.13 \pm 0.04$              | $67.73 \pm 0.01$ | $33.92 \pm 0.04$            | $52.49 \pm 0.01$ |
| DATM [Guo et al., 2023]          | $54.84 \pm 0.03$               | $61.14 \pm 0.04$ | $60.87 \pm 0.05$              | $68.07 \pm 0.01$ | $33.97 \pm 0.04$            | $52.93 \pm 0.02$ |
| SeqMatch [Du et al., 2023b]      | $48.72 \pm 0.03$               | $61.39 \pm 0.04$ | $55.67 \pm 0.04$              | $65.13 \pm 0.02$ | $24.88 \pm 0.04$            | $51.81 \pm 0.01$ |
| <b>OUR</b>                       | $66.18 + 0.02$                 | $69.65 + 0.01$   | $65.37 + 0.02$                | $68.90 + 0.01$   | $51.19 + 0.02$              | $53.85 + 0.01$   |

Table 3: Performances of ConvNet using various distillation methods on the **High-Resolution** medical datasets.

<span id="page-5-2"></span>Image /page/5/Figure/2 description: The image displays two plots side-by-side. Plot (a), titled "Time complexity", shows "Evaluation Accuracy" on the y-axis and "Distillation Time (min)" on the x-axis. It features two lines: a red line labeled "OUR" and a blue line labeled "FTD", both with shaded areas indicating variability. A dashed black line is also present. Plot (b), titled "Memory complexity", shows "GPU Memory (Bytes)" on the y-axis and "Distillation Time (hour)" on the x-axis. It also shows two lines, one red labeled "OUR" and one blue labeled "FTD", with the blue line appearing as a horizontal line at the top and the red line showing an increasing curve.

Figure 3: Comparison of the time and memory complexity of our method with a dynamic expert time-step range to other trajectory matching method in the PATHMNIST.

based on trajectory matching are generally superior to the methods based on distribution matching, especially when the ipc is low. (2) due to the diversity of medical datasets, multi-step trajectory matching methods are not always better than single-step matching methods. *e.g.,* Seqmatch [\[Du](#page-7-6) *et al.*[, 2023b\]](#page-7-6) is not suitable for situations with low ipc, and HaBa [Liu *et al.*[, 2022a\]](#page-7-15) stability is relatively poor. (3) When the resolution is high, our method can achieve extremely high performance with a low ipc, and this is because the highresolution synthetic images allow our method to attach more diverse information on them. (4) our method has limited improvement when the ipc is high, and this is because a certain number of synthetic images are initialized from the original dataset, which inherently possess considerable diversity and the ability to alleviate overlap. *i.e.,* the additional information value gained by our method is much lower than the information value added when ipc is low.

## 5 Analysis

### 5.1 Quantitative Analysis

### Cross-Architecture Generalization.

For dataset distillation methods that need to be deployed in practical application scenarios, a satisfactory distilled dataset should have *similar training effects to the original one on downstream models with arbitrary architectures*. Therefore, <span id="page-5-1"></span>Table 4: Generalization performances of synthetic datasets on the unseen evaluation models in the PATHMNIST.

|            | <b>Evaluation Models</b> |              |              |              |
|------------|--------------------------|--------------|--------------|--------------|
| Method     | ConvNet                  | LeNet        | ResNet18     | AlexNet      |
| DC         | 57.63 ± 0.01             | 44.35 ± 0.03 | 34.22 ± 0.02 | 40.72 ± 0.05 |
| DM         | 50.74 ± 0.02             | 41.59 ± 0.04 | 42.84 ± 0.03 | 41.25 ± 0.04 |
| <b>DSA</b> | 58.56 ± 0.02             | 51.26 ± 0.05 | 43.63 ± 0.02 | 53.85 ± 0.04 |
| <b>MTT</b> | 50.86 ± 0.02             | 44.95 ± 0.03 | 44.71 ± 0.04 | 45.11 ± 0.05 |
| FTD        | 53.47 ± 0.02             | 44.21 ± 0.02 | 45.87 ± 0.04 | 46.27 ± 0.02 |
| <b>OUR</b> | 67.01 ± 0.01             | 51.64 ± 0.03 | 52.46 ± 0.03 | 53.72 ± 0.03 |

cross-architecture generalization performance is an important metric, and we report the generalization performances of our method and competitors in the PATHMNIST on Table [4.](#page-5-1) The results indicate that compared to existing dataset distillation methods, our proposed method still maintains the best performance when faced with network architectures different from those used in buffer and distillation stages.

<span id="page-5-3"></span>Image /page/5/Figure/12 description: The image contains two plots. The plot on the left is a line graph showing "Trajectory Matching Loss" on the y-axis and "Distillation Steps (K)" on the x-axis, ranging from 0 to 6. Two lines are plotted: one labeled "OUR" in red, which decreases from approximately 0.99 to 0.975, and another labeled "FTD" in blue, which fluctuates significantly between approximately 0.94 and 1.09. The plot on the right is a line graph showing "Evaluation Variance" on the y-axis, ranging from 0.012 to 0.022, and "Distillation Methods" on the x-axis, with categories "OUR", "MTT", "FTD", "DATM", and "OUR (w/ overlap)". The "OUR" method has a variance of approximately 0.0135, "MTT" has a variance of approximately 0.0125, "FTD" has a variance of approximately 0.015, "DATM" has a variance of approximately 0.0155, and "OUR (w/ overlap)" has a variance of approximately 0.0215.

(a) Trajectory matching loss (b) Evaluation stability variance

Figure 4: Comparison of the stability in the PATHMNIST during distillation and evaluation stages.

### Time and Memory Complexity Evaluation

We evaluate the running time and GPU memory of mainstream trajectory matching methods to estimate the training cost brought by distillation. Specifically, we first analyze the evaluation accuracy of our and FTD methods over the distillation duration, and the expert time-step range of the FTD is set to 50. Since our method uses a dynamic expert time-step range that gradually increases from 1, we can achieve per-

<span id="page-6-1"></span>Table 5: Comparison of the superior performances of our method with a fixed fewer IPC to other dataset distillation methods in the PATHMNIST.

| IPC      | Evaluation Methods |                  |                  |                  |
|----------|--------------------|------------------|------------------|------------------|
|          | DC                 | DM               | MTT              | FTD              |
| 2        | $57.63 \pm 0.01$   | $50.74 \pm 0.02$ | $50.86 \pm 0.02$ | $53.47 \pm 0.02$ |
| 4        | $63.23 \pm 0.01$   | $60.42 \pm 0.02$ | $61.97 \pm 0.02$ | $66.33 \pm 0.01$ |
| 6        | $67.61 \pm 0.01$   | $68.42 \pm 0.01$ | $66.54 \pm 0.04$ | $68.38 \pm 0.01$ |
| OURIPC=2 | $67.01 \pm 0.01$   |                  |                  |                  |

formance comparable to FTD in very little time. As shown in Figure [3a,](#page-5-2) we achieved 100% performance of FTD using approximately one-sixth of the duration. Similarly, we also analyzed the GPU memory usage of our method and FTD in Figure [3b,](#page-5-2) and our method has consistently consumed less graphics memory than FTD, especially in the early stages of training. Note that we evaluate these two methods in the same computation environment and fairly.

### 5.2 Ablation Study

In this section, we conduct a series of ablation studies for our method to investigate the effectiveness of the proposed two main contributions: progressive stable distillation, dynamic retraining and overlap mitigation.

Progressive Stable Distillation. To verify the stability of our method, we evaluated the trajectory-matching loss in the distillation stage and the stability variance in the evaluation stage. As shown in Figure [4a,](#page-5-3) it can be observed that our method achieves more stable loss compared to FTD due to the progressive training strategy. As shown in Figure [4b,](#page-5-3) we evaluate the stability of the synthetic datasets distilled by different trajectory matching methods using 10 randomly initialized models. It can be observed that the synthetic dataset generated by our method has good evaluation stability after retraining and overlap mitigation.

<span id="page-6-0"></span>Image /page/6/Figure/6 description: The image displays three plots related to model performance. Plot (a) is a line graph titled 'MMD' showing MMD values on the y-axis against Distillation Steps (K) on the x-axis. It plots three lines: 'OUR' (red), 'Base (w/o overlap)' (blue), and 'Base (w/ overlap)' (light blue). The 'OUR' line starts at approximately 0.15 and rises to about 0.5 at 4K steps. The 'Base (w/o overlap)' line starts at 0.1 and increases to about 0.75 at 4K steps. The 'Base (w/ overlap)' line is flat at 0.1. Plots (b) and (c) are stacked bar charts showing 'Evaluation Accuracy' on the y-axis for different medical datasets on the x-axis. Plot (b) is for 'IPC=2' and plot (c) is for 'IPC=10'. Both plots show bars for datasets like COVID, BREAST, SKIN, PATH, OCT, and ORGAN3D. The bars are stacked with 'FTD' (light blue) at the bottom, 'OUR (w/ overlap)' (yellow) in the middle, and 'OUR' (red) at the top. In plot (b), accuracies range from around 50% to 65% for FTD, with additional contributions from the other two categories. In plot (c), accuracies are generally higher, with FTD values ranging from about 50% to 65%, and the total accuracies reaching up to around 70%.

Figure 5: (a) MMD plots using different overlap mitigation losses in the PATHMNIST. (b) (c) visualization of the performance improvements in the ablation study.

Dynamic Retraining and Overlap Mitigation. We use MMD to demonstrate the existence of overlap in the distillation stage and the necessity of overlap mitigation. The orange line in Figure [5a](#page-6-0) shows that as the distillation progresses, the MMD among images of the same class continues to decrease, indicating that they become increasingly similar. The blue line indicates that the MMD continues to increase and does not converge when we only add the overlap mitigation loss, as this loss will dominate at the end of distillation. The red line shows that after adding the dynamic retraining components, the MMD can maintain increased and gradually converged. It increases the diversity of synthetic images while maintaining the stability. In addition, the improvements of different components in our method are in Figure [5](#page-6-0) (b)(c), showing their effectiveness.

<span id="page-6-2"></span>Image /page/6/Figure/10 description: The image displays a comparison of three different trajectories and their corresponding surfaces, labeled (a) Buffer trajectory, (b) FTD trajectory, and (c) OUR trajectory, along with (d) Buffer surface, (e) FTD surface, and (f) OUR surface. The top row shows contour plots of parameter spaces with trajectories plotted on them. The x-axis for all three plots is labeled with values ranging from -1.0 to 2.5, and the y-axis is labeled 'Parameter Space' with values from -1.00 to 1.00. Each plot shows a curved trajectory moving through the contour lines. The bottom row presents 3D surface plots. The 'Buffer surface' shows a deep, symmetrical bowl shape. The 'FTD surface' is a relatively flat, elongated saddle shape. The 'OUR surface' is also a saddle shape, but it appears more rounded and less elongated than the FTD surface.

Figure 6: Visualization of 2D/3D evaluation trajectory using loss landscape in the PATHMNIST.

<span id="page-6-3"></span>Image /page/6/Figure/12 description: The image displays three t-SNE plots, labeled (a) Buffer, (b) FTD, and (c) OUR. Each plot visualizes data points colored according to class labels 0 through 8, as indicated by legends on the right side of each plot. The x and y axes range from -100 to 100. Plot (a) shows distinct clusters of points for each class. Plot (b) shows a more dispersed distribution with some overlap between classes. Plot (c) also shows a relatively dispersed distribution, with some classes appearing more separated than others.

Figure 7: Feature visualization using t-SNE in the PATHM-NIST during buffer and evaluation stages.

### 5.3 Qualitative Analysis

In this section, we perform loss landscape [Li *et al.*[, 2018\]](#page-7-21) and t-SNE [\[Van der Maaten and Hinton, 2008\]](#page-8-10) respectively to visualize the performance of trajectory matching and features. As shown in Figure [6,](#page-6-2) we present the evaluation trajectories of different methods on dynamic 2D contour loss curves and static 3D loss landscapes respectively. We can observe that our method has denser contour lines, a higher and narrower landscape compared to FTD, which verifies that our trajectory converges and matches better to the buffer [\[Garipov](#page-7-22) *et al.*[, 2018; Garipov](#page-7-22) *et al.*, 2018]. As shown in Figure [7,](#page-6-3) we demonstrate that our method achieved fewer inter-class confusion issues than FTD. More analysis results are in the supplementary materials.

## 6 Conclusions

This paper established a new and comprehensive medical image dataset distillation benchmark. Through evaluation, the proposed progressive trajectory matching strategy and the overlap elimination of synthetic images achieve SOTA performances.

## References

- <span id="page-7-14"></span>[Al-Dhabyani *et al.*, 2020] Walid Al-Dhabyani, Mohammed Gomaa, Hussien Khaled, and Aly Fahmy. Dataset of breast ultrasound images. *Data in brief*, 28:104863, 2020.
- <span id="page-7-17"></span>[Alom *et al.*, 2018] Md Zahangir Alom, Tarek M Taha, Christopher Yakopcic, Stefan Westberg, Paheding Sidike, Mst Shamima Nasrin, Brian C Van Esesn, Abdul A S Awwal, and Vijayan K Asari. The history began from alexnet: A comprehensive survey on deep learning approaches. *arXiv preprint arXiv:1803.01164*, 2018.
- <span id="page-7-12"></span>[Bilic *et al.*, 2023] Patrick Bilic, Patrick Christ, Hongwei Bran Li, Eugene Vorontsov, Avi Ben-Cohen, Georgios Kaissis, Adi Szeskin, Colin Jacobs, Gabriel Efrain Humpire Mamani, Gabriel Chartrand, et al. The liver tumor segmentation benchmark (lits). *Medical Image Analysis*, 84:102680, 2023.
- <span id="page-7-3"></span>[Cazenavette *et al.*, 2022] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022.
- <span id="page-7-20"></span>[Cui *et al.*, 2022] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. *Advances in Neural Information Processing Systems*, 35:810–822, 2022.
- <span id="page-7-4"></span>[Du *et al.*, 2023a] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3749–3758, 2023.
- <span id="page-7-6"></span>[Du *et al.*, 2023b] Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. *arXiv preprint arXiv:2311.01570*, 2023.
- <span id="page-7-22"></span>[Garipov *et al.*, 2018] Timur Garipov, Pavel Izmailov, Dmitrii Podoprikhin, Dmitry P Vetrov, and Andrew G Wilson. Loss surfaces, mode connectivity, and fast ensembling of dnns. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-7-0"></span>[Geng *et al.*, 2023] Jiahui Geng, Zongxiong Chen, Yuandou Wang, Herbert Woisetschlaeger, Sonja Schimmler, Ruben Mayer, Zhiming Zhao, and Chunming Rong. A survey on dataset distillation: Approaches, applications and future directions. *arXiv preprint arXiv:2305.01975*, 2023.
- <span id="page-7-9"></span>[Gretton *et al.*, 2012] Arthur Gretton, Karsten M Borgwardt, Malte J Rasch, Bernhard Schölkopf, and Alexander Smola. A kernel two-sample test. *The Journal of Machine Learning Research*, 13(1):723–773, 2012.
- <span id="page-7-5"></span>[Guo *et al.*, 2023] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023.
- <span id="page-7-19"></span>[He *et al.*, 2016] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recog-

nition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016.

- <span id="page-7-10"></span>[Kather *et al.*, 2019] Jakob Nikolas Kather, Johannes Krisam, Pornpimol Charoentong, Tom Luedde, Esther Herpel, Cleo-Aron Weis, Timo Gaiser, Alexander Marx, Nektarios A Valous, Dyke Ferber, et al. Predicting survival from colorectal cancer histology slides using deep learning: A retrospective multicenter study. *PLoS medicine*, 16(1):e1002730, 2019.
- <span id="page-7-11"></span>[Kermany *et al.*, 2018] Daniel S Kermany, Michael Goldbaum, Wenjia Cai, Carolina CS Valentim, Huiying Liang, Sally L Baxter, Alex McKeown, Ge Yang, Xiaokang Wu, Fangbing Yan, et al. Identifying medical diagnoses and treatable diseases by image-based deep learning. *cell*, 172(5):1122–1131, 2018.
- <span id="page-7-18"></span>[LeCun and others, 2015] Yann LeCun et al. Lenet-5, convolutional neural networks. *URL: http://yann. lecun. com/exdb/lenet*, 20(5):14, 2015.
- <span id="page-7-1"></span>[Lei and Tao, 2023] Shiye Lei and Dacheng Tao. A comprehensive survey to dataset distillation. *arXiv preprint arXiv:2301.05603*, 2023.
- <span id="page-7-21"></span>[Li *et al.*, 2018] Hao Li, Zheng Xu, Gavin Taylor, Christoph Studer, and Tom Goldstein. Visualizing the loss landscape of neural nets. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-7-7"></span>[Li *et al.*, 2020] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Soft-label anonymous gastric x-ray image distillation. In *2020 IEEE International Conference on Image Processing (ICIP)*, pages 305–309. IEEE, 2020.
- <span id="page-7-8"></span>[Li *et al.*, 2022a] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Compressed gastric image generation based on soft-label dataset distillation for medical data sharing. *Computer Methods and Programs in Biomedicine*, 227:107189, 2022.
- <span id="page-7-2"></span>[Li *et al.*, 2022b] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Dataset distillation for medical dataset sharing. *arXiv preprint arXiv:2209.14603*, 2022.
- <span id="page-7-15"></span>[Liu *et al.*, 2022a] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *Advances in Neural Information Processing Systems*, 35:1100–1113, 2022.
- <span id="page-7-16"></span>[Liu *et al.*, 2022b] Zhuang Liu, Hanzi Mao, Chao-Yuan Wu, Christoph Feichtenhofer, Trevor Darrell, and Saining Xie. A convnet for the 2020s. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 11976–11986, 2022.
- <span id="page-7-13"></span>[Rahman *et al.*, 2021] Tawsifur Rahman, Amith Khandakar, Yazan Qiblawey, Anas Tahir, Serkan Kiranyaz, Saad Bin Abul Kashem, Mohammad Tariqul Islam, Somaya Al Maadeed, Susu M Zughaier, Muhammad Salman Khan, et al. Exploring the effect of image enhancement techniques on covid-19 detection using chest x-ray images. *Computers in biology and medicine*, 132:104319, 2021.

- <span id="page-8-1"></span>[Sachdeva and McAuley, 2023] Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *arXiv preprint arXiv:2301.04272*, 2023.
- <span id="page-8-9"></span>[Smith *et al.*, 2023] Samuel L Smith, Andrew Brock, Leonard Berrada, and Soham De. Convnets match vision transformers at scale. *arXiv preprint arXiv:2310.16764*, 2023.
- <span id="page-8-8"></span>[Tschandl *et al.*, 2018] Philipp Tschandl, Cliff Rosendahl, and Harald Kittler. The ham10000 dataset, a large collection of multi-source dermatoscopic images of common pigmented skin lesions. *Scientific data*, 5(1):1–9, 2018.
- <span id="page-8-10"></span>[Van der Maaten and Hinton, 2008] Laurens Van der Maaten and Geoffrey Hinton. Visualizing data using t-sne. *Journal of machine learning research*, 9(11), 2008.
- <span id="page-8-2"></span>[Wang *et al.*, 2018] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-8-4"></span>[Wang *et al.*, 2022] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196–12205, 2022.
- <span id="page-8-7"></span>[Yang *et al.*, 2023] Jiancheng Yang, Rui Shi, Donglai Wei, Zequan Liu, Lin Zhao, Bilian Ke, Hanspeter Pfister, and Bingbing Ni. Medmnist v2-a large-scale lightweight benchmark for 2d and 3d biomedical image classification. *Scientific Data*, 10(1):41, 2023.
- <span id="page-8-0"></span>[Yu *et al.*, 2023] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023.
- <span id="page-8-6"></span>[Zhao and Bilen, 2021] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021.
- <span id="page-8-3"></span>[Zhao and Bilen, 2023] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023.
- <span id="page-8-5"></span>[Zhao *et al.*, 2020] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.
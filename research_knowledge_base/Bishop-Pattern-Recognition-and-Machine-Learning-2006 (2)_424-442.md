**Figure 8.46** A fragment of a factor graph illustrating the evaluation of the marginal  $p(x)$ .

Image /page/0/Figure/2 description: This is a diagram illustrating a message passing process in a graphical model. On the left, a light blue cloud shape encloses a factor node labeled 'Fs(x, Xs)' which is connected to multiple variable nodes represented by red circles. A red square node labeled 'fs' is connected to the 'Fs(x, Xs)' node and also to a variable node labeled 'x' (represented by a red circle). An arrow labeled 'µfs→x(x)' points from the 'fs' node to the 'x' node, indicating a message being passed. The 'x' node is further connected to multiple variable nodes represented by red squares, with a dotted line indicating more connections.

 $f_s$ , and  $F_s(x, X_s)$  represents the product of all the factors in the group associated with factor  $f_s$ .

Substituting (8.62) into (8.61) and interchanging the sums and products, we obtain

$$
p(x) = \prod_{s \in ne(x)} \left[ \sum_{X_s} F_s(x, X_s) \right]
$$
  
= 
$$
\prod_{s \in ne(x)} \mu_{f_s \to x}(x).
$$
 (8.63)

Here we have introduced a set of functions  $\mu_{f, \to x}(x)$ , defined by

$$
\mu_{f_s \to x}(x) \equiv \sum_{X_s} F_s(x, X_s) \tag{8.64}
$$

which can be viewed as *messages* from the factor nodes  $f_s$  to the variable node x. We see that the required marginal  $p(x)$  is given by the product of all the incoming messages arriving at node  $x$ .

In order to evaluate these messages, we again turn to Figure 8.46 and note that each factor  $F_s(x, X_s)$  is described by a factor (sub-)graph and so can itself be factorized. In particular, we can write

$$
F_s(x, X_s) = f_s(x, x_1, \dots, x_M) G_1(x_1, X_{s1}) \dots G_M(x_M, X_{sM}) \tag{8.65}
$$

where, for convenience, we have denoted the variables associated with factor  $f_x$ , in addition to x, by  $x_1, \ldots, x_M$ . This factorization is illustrated in Figure 8.47. Note that the set of variables  $\{x, x_1, \ldots, x_M\}$  is the set of variables on which the factor  $f_s$  depends, and so it can also be denoted  $\mathbf{x}_s$ , using the notation of (8.59).

Substituting (8.65) into (8.64) we obtain

$$
\mu_{f_s \to x}(x) = \sum_{x_1} \dots \sum_{x_M} f_s(x, x_1, \dots, x_M) \prod_{m \in ne(f_s) \setminus x} \left[ \sum_{X_{xm}} G_m(x_m, X_{sm}) \right]
$$
$$
= \sum_{x_1} \dots \sum_{x_M} f_s(x, x_1, \dots, x_M) \prod_{m \in ne(f_s) \setminus x} \mu_{x_m \to f_s}(x_m) \qquad (8.66)
$$

# **8.4. Inference in Graphical Models 405**

Image /page/1/Figure/1 description: Figure 8.47 Illustration of the factorization of the subgraph associated with factor node fs.

Image /page/1/Figure/2 description: This is a factor graph illustrating a message passing algorithm. A central red square labeled 'fs' represents a factor node. It is connected by red lines to three circular nodes. Two of these nodes, labeled 'xM' and 'xm', are grouped within a light blue cloud shape labeled 'Gm(xm, Xsm)', indicating a sub-graph. A dotted line connects 'xM' to 'xm', suggesting a sequence or multiple variables. A third circular node, labeled 'x', is connected to 'fs' on the right. Blue arrows indicate messages being passed: 'µxM→fs (xM)' flows from 'xM' to 'fs', and 'µfs→x(x)' flows from 'fs' to 'x'.

where  $ne(f_s)$  denotes the set of variable nodes that are neighbours of the factor node  $f_s$ , and ne $(f_s) \setminus x$  denotes the same set but with node x removed. Here we have defined the following messages from variable nodes to factor nodes

$$
\mu_{x_m \to f_s}(x_m) \equiv \sum_{X_{sm}} G_m(x_m, X_{sm}). \tag{8.67}
$$

We have therefore introduced two distinct kinds of message, those that go from factor nodes to variable nodes denoted  $\mu_{f\to x}(x)$ , and those that go from variable nodes to factor nodes denoted  $\mu_{x\to f}(x)$ . In each case, we see that messages passed along a link are always a function of the variable associated with the variable node that link connects to.

The result (8.66) says that to evaluate the message sent by a factor node to a variable node along the link connecting them, take the product of the incoming messages along all other links coming into the factor node, multiply by the factor associated with that node, and then marginalize over all of the variables associated with the incoming messages. This is illustrated in Figure 8.47. It is important to note that a factor node can send a message to a variable node once it has received incoming messages from all other neighbouring variable nodes.

Finally, we derive an expression for evaluating the messages from variable nodes to factor nodes, again by making use of the (sub-)graph factorization. From Figure 8.48, we see that term  $G_m(x_m, X_{sm})$  associated with node  $x_m$  is given by a product of terms  $F_l(x_m, X_{ml})$  each associated with one of the factor nodes  $f_l$  that is linked to node  $x_m$  (excluding node  $f_s$ ), so that

$$
G_m(x_m, X_{sm}) = \prod_{l \in ne(x_m) \backslash f_s} F_l(x_m, X_{ml})
$$
\n(8.68)

where the product is taken over all neighbours of node  $x_m$  except for node  $f_s$ . Note that each of the factors  $F_l(x_m, X_{ml})$  represents a subtree of the original graph of precisely the same kind as introduced in (8.62). Substituting (8.68) into (8.67), we

**Figure 8.48** Illustration of the evaluation of the message sent by a variable node to an adjacent factor node.

Image /page/2/Figure/2 description: A diagram shows a circle labeled xm connected to three squares labeled fL, fs, and fl. The square labeled fL is connected to the circle by a solid red line. The square labeled fs is connected to the circle by a solid red line. The square labeled fl is connected to the circle by a solid red line. The square labeled fL is also connected to the square labeled fl by a dotted red line. A light blue cloud-like shape is shown below the circle and the square labeled fl. The cloud-like shape is labeled Fl(xm, Xml).

then obtain

$$
\mu_{x_m \to f_s}(x_m) = \prod_{l \in ne(x_m) \backslash f_s} \left[ \sum_{X_{ml}} F_l(x_m, X_{ml}) \right]
$$
$$
= \prod_{l \in ne(x_m) \backslash f_s} \mu_{f_l \to x_m}(x_m) \tag{8.69}
$$

where we have used the definition (8.64) of the messages passed from factor nodes to variable nodes. Thus to evaluate the message sent by a variable node to an adjacent factor node along the connecting link, we simply take the product of the incoming messages along all of the other links. Note that any variable node that has only two neighbours performs no computation but simply passes messages through unchanged. Also, we note that a variable node can send a message to a factor node once it has received incoming messages from all other neighbouring factor nodes.

Recall that our goal is to calculate the marginal for variable node  $x$ , and that this marginal is given by the product of incoming messages along all of the links arriving at that node. Each of these messages can be computed recursively in terms of other messages. In order to start this recursion, we can view the node  $x$  as the root of the tree and begin at the leaf nodes. From the definition (8.69), we see that if a leaf node is a variable node, then the message that it sends along its one and only link is given by

$$
\mu_{x \to f}(x) = 1 \tag{8.70}
$$

as illustrated in Figure 8.49(a). Similarly, if the leaf node is a factor node, we see from (8.66) that the message sent should take the form

$$
\mu_{f \to x}(x) = f(x) \tag{8.71}
$$

**Figure 8.49** The sum-product algorithm begins with messages sent by the leaf nodes, which depend on whether the leaf node is (a) a variable node, or (b) a factor node.

Image /page/2/Figure/11 description: The image displays two diagrams, labeled (a) and (b), illustrating a concept related to functions and mappings. Diagram (a) shows a red circle labeled 'x' connected by a red line to a red square labeled 'f'. A blue arrow points from the circle to the square, with the text "µx→f(x) = 1" above it. Diagram (b) shows a red square labeled 'f' connected by a red line to a red circle labeled 'x'. A blue arrow points from the square to the circle, with the text "µf→x(x) = f(x)" above it.

as illustrated in Figure 8.49(b).

At this point, it is worth pausing to summarize the particular version of the sumproduct algorithm obtained so far for evaluating the marginal  $p(x)$ . We start by viewing the variable node  $x$  as the root of the factor graph and initiating messages at the leaves of the graph using (8.70) and (8.71). The message passing steps (8.66) and (8.69) are then applied recursively until messages have been propagated along every link, and the root node has received messages from all of its neighbours. Each node can send a message towards the root once it has received messages from all of its other neighbours. Once the root node has received messages from all of its neighbours, the required marginal can be evaluated using (8.63). We shall illustrate this process shortly.

To see that each node will always receive enough messages to be able to send out a message, we can use a simple inductive argument as follows. Clearly, for a graph comprising a variable root node connected directly to several factor leaf nodes, the algorithm trivially involves sending messages of the form (8.71) directly from the leaves to the root. Now imagine building up a general graph by adding nodes one at a time, and suppose that for some particular graph we have a valid algorithm. When one more (variable or factor) node is added, it can be connected only by a single link because the overall graph must remain a tree, and so the new node will be a leaf node. It therefore sends a message to the node to which it is linked, which in turn will therefore receive all the messages it requires in order to send its own message towards the root, and so again we have a valid algorithm, thereby completing the proof.

Now suppose we wish to find the marginals for every variable node in the graph. This could be done by simply running the above algorithm afresh for each such node. However, this would be very wasteful as many of the required computations would be repeated. We can obtain a much more efficient procedure by 'overlaying' these multiple message passing algorithms to obtain the general sum-product algorithm as follows. Arbitrarily pick any (variable or factor) node and designate it as the root. Propagate messages from the leaves to the root as before. At this point, the root node will have received messages from all of its neighbours. It can therefore send out messages to all of its neighbours. These in turn will then have received messages from all of their neighbours and so can send out messages along the links going away from the root, and so on. In this way, messages are passed outwards from the root all the way to the leaves. By now, a message will have passed in both directions across every link in the graph, and every node will have received a message from all of its neighbours. Again a simple inductive argument can be *Exercise 8.20* used to verify the validity of this message passing protocol. Because every variable node will have received messages from all of its neighbours, we can readily calculate the marginal distribution for every variable in the graph. The number of messages that have to be computed is given by twice the number of links in the graph and so involves only twice the computation involved in finding a single marginal. By comparison, if we had run the sum-product algorithm separately for each node, the amount of computation would grow quadratically with the size of the graph. Note that this algorithm is in fact independent of which node was designated as the root,

*Exercise 8.20*

**Figure 8.50** The sum-product algorithm can be viewed purely in terms of messages sent out by factor nodes to other factor nodes. In this example, the outgoing message shown by the blue arrow is obtained by taking the product of all the incoming messages shown by green arrows, multiplying by the factor  $f_s$ , and marginalizing over the variables  $x_1$  and  $x_2$ .

Image /page/4/Figure/2 description: A diagram shows a network of nodes and edges. There are three circular nodes labeled x1, x2, and x3. There are also square nodes, some of which are labeled fs. Red lines connect the nodes, representing edges. Green arrows point from square nodes towards the circular nodes x1 and x2, indicating incoming signals. A blue arrow points from a square node labeled fs towards the circular node x3, indicating a signal transmission. The overall structure resembles a factor graph or a message-passing algorithm visualization.

and indeed the notion of one node having a special status was introduced only as a convenient way to explain the message passing protocol.

Next suppose we wish to find the marginal distributions  $p(\mathbf{x}_s)$  associated with the sets of variables belonging to each of the factors. By a similar argument to that *Exercise 8.21* used above, it is easy to see that the marginal associated with a factor is given by the product of messages arriving at the factor node and the local factor at that node

$$
p(\mathbf{x}_s) = f_s(\mathbf{x}_s) \prod_{i \in \text{ne}(f_s)} \mu_{x_i \to f_s}(x_i)
$$
\n(8.72)

in complete analogy with the marginals at the variable nodes. If the factors are parameterized functions and we wish to learn the values of the parameters using the EM algorithm, then these marginals are precisely the quantities we will need to calculate in the E step, as we shall see in detail when we discuss the hidden Markov model in Chapter 13.

The message sent by a variable node to a factor node, as we have seen, is simply the product of the incoming messages on other links. We can if we wish view the sum-product algorithm in a slightly different form by eliminating messages from variable nodes to factor nodes and simply considering messages that are sent out by factor nodes. This is most easily seen by considering the example in Figure 8.50.

So far, we have rather neglected the issue of normalization. If the factor graph was derived from a directed graph, then the joint distribution is already correctly normalized, and so the marginals obtained by the sum-product algorithm will similarly be normalized correctly. However, if we started from an undirected graph, then in general there will be an unknown normalization coefficient  $1/Z$ . As with the simple chain example of Figure 8.38, this is easily handled by working with an unnormalized version  $\tilde{p}(\mathbf{x})$  of the joint distribution, where  $p(\mathbf{x}) = \tilde{p}(\mathbf{x})/Z$ . We first run the sum-product algorithm to find the corresponding unnormalized marginals  $\tilde{p}(x_i)$ . The coefficient  $1/Z$  is then easily obtained by normalizing any one of these marginals, and this is computationally efficient because the normalization is done over a single variable rather than over the entire set of variables as would be required to normalize  $\widetilde{p}(\mathbf{x})$  directly.

At this point, it may be helpful to consider a simple example to illustrate the operation of the sum-product algorithm. Figure 8.51 shows a simple 4-node factor

*Exercise 8.21*

# **8.4. Inference in Graphical Models 409**

Image /page/5/Figure/1 description: Figure 8.51 shows a simple factor graph used to illustrate the sum-product algorithm. The graph includes a variable node labeled x1, connected to a factor node represented by a red circle, which is then connected to another factor node represented by a red square.

Image /page/5/Figure/2 description: This is a graphical representation of a factor graph. It features four nodes labeled x1, x2, x3, and x4, depicted as red circles. There are three factor nodes, labeled fa, fb, and fc, represented as red squares. The connections between the nodes are shown as red lines. Specifically, x1 is connected to fa, fa is connected to x2, x2 is connected to fb, fb is connected to x3, and x2 is also connected to fc, which in turn is connected to x4. The overall structure resembles a 'T' shape with x1 and x3 extending horizontally from x2, and x4 extending vertically downwards from x2.

graph whose unnormalized joint distribution is given by

$$
\widetilde{p}(\mathbf{x}) = f_a(x_1, x_2) f_b(x_2, x_3) f_c(x_2, x_4).
$$
\n(8.73)

In order to apply the sum-product algorithm to this graph, let us designate node  $x_3$ as the root, in which case there are two leaf nodes  $x_1$  and  $x_4$ . Starting with the leaf nodes, we then have the following sequence of six messages

$$
\mu_{x_1 \to f_a}(x_1) = 1 \tag{8.74}
$$

$$
\mu_{f_a \to x_2}(x_2) = \sum_{x_1} f_a(x_1, x_2) \tag{8.75}
$$

$$
\mu_{x_4 \to f_c}(x_4) = 1 \tag{8.76}
$$

$$
\mu_{f_c \to x_2}(x_2) = \sum_{x_4} f_c(x_2, x_4) \tag{8.77}
$$

$$
\mu_{x_2 \to f_b}(x_2) = \mu_{f_a \to x_2}(x_2) \mu_{f_c \to x_2}(x_2) \tag{8.78}
$$

$$
\mu_{f_b \to x_3}(x_3) = \sum_{x_2} f_b(x_2, x_3) \mu_{x_2 \to f_b}.
$$
\n(8.79)

The direction of flow of these messages is illustrated in Figure 8.52. Once this message propagation is complete, we can then propagate messages from the root node out to the leaf nodes, and these are given by

$$
\mu_{x_3 \to f_b}(x_3) = 1 \tag{8.80}
$$

$$
\mu_{f_b \to x_2}(x_2) = \sum_{x_3} f_b(x_2, x_3) \tag{8.81}
$$

$$
\mu_{x_2 \to f_a}(x_2) = \mu_{f_b \to x_2}(x_2) \mu_{f_c \to x_2}(x_2) \tag{8.82}
$$

$$
\mu_{f_a \to x_1}(x_1) = \sum_{x_2} f_a(x_1, x_2) \mu_{x_2 \to f_a}(x_2)
$$
\n(8.83)

$$
\mu_{x_2 \to f_c}(x_2) = \mu_{f_a \to x_2}(x_2) \mu_{f_b \to x_2}(x_2) \tag{8.84}
$$

$$
\mu_{f_c \to x_4}(x_4) = \sum_{x_2} f_c(x_2, x_4) \mu_{x_2 \to f_c}(x_2). \tag{8.85}
$$

Image /page/6/Figure/1 description: The image displays two factor graphs, labeled (a) and (b). Both graphs consist of nodes represented by red circles and edges represented by red lines. Some edges also have blue arrows indicating the direction of message passing. In graph (a), there are four variable nodes labeled x1, x2, x3, and x4. Node x2 is connected to x1 and x3 via two factor nodes (red squares). Node x2 is also connected to x4 via two factor nodes. Blue arrows in graph (a) show messages passing from x1 to x2, from x2 to x3, from x2 to the first factor node below it, and from that factor node to x4. Graph (b) is similar to graph (a), but the blue arrows indicate message passing in the opposite direction: from x2 to x1, from x3 to x2, from x4 to the factor node below x2, and from that factor node to x2.

**Figure 8.52** Flow of messages for the sum-product algorithm applied to the example graph in Figure 8.51. (a) From the leaf nodes  $x_1$  and  $x_4$  towards the root node  $x_3$ . (b) From the root node towards the leaf nodes.

One message has now passed in each direction across each link, and we can now evaluate the marginals. As a simple check, let us verify that the marginal  $p(x<sub>2</sub>)$  is given by the correct expression. Using (8.63) and substituting for the messages using the above results, we have

$$
\widetilde{p}(x_2) = \mu_{f_a \to x_2}(x_2) \mu_{f_b \to x_2}(x_2) \mu_{f_c \to x_2}(x_2)
$$
\n
$$
= \left[ \sum_{x_1} f_a(x_1, x_2) \right] \left[ \sum_{x_3} f_b(x_2, x_3) \right] \left[ \sum_{x_4} f_c(x_2, x_4) \right]
$$
\n
$$
= \sum_{x_1} \sum_{x_2} \sum_{x_4} f_a(x_1, x_2) f_b(x_2, x_3) f_c(x_2, x_4)
$$
\n
$$
= \sum_{x_1} \sum_{x_3} \sum_{x_4} \widetilde{p}(\mathbf{x}) \tag{8.86}
$$

as required.

So far, we have assumed that all of the variables in the graph are hidden. In most practical applications, a subset of the variables will be observed, and we wish to calculate posterior distributions conditioned on these observations. Observed nodes are easily handled within the sum-product algorithm as follows. Suppose we partition **x** into hidden variables **h** and observed variables **v**, and that the observed value of **v** is denoted  $\hat{\mathbf{v}}$ . Then we simply multiply the joint distribution  $p(\mathbf{x})$  by  $\prod_i I(v_i, \hat{v}_i)$ ,<br>where  $I(v_i, \hat{v}_i) = 1$  if  $v_i = \hat{v}_i$  and  $I(v_i, \hat{v}_i) = 0$  otherwise. This product corresponds where  $I(v,\hat{v})=1$  if  $v = \hat{v}$  and  $I(v,\hat{v})=0$  otherwise. This product corresponds to  $p(\mathbf{h}, \mathbf{v} = \hat{\mathbf{v}})$  and hence is an unnormalized version of  $p(\mathbf{h}|\mathbf{v} = \hat{\mathbf{v}})$ . By running the sum-product algorithm, we can efficiently calculate the posterior marginals  $p(h_i|\mathbf{v} = \hat{\mathbf{v}})$  up to a normalization coefficient whose value can be found efficiently using a local computation. Any summations over variables in **v** then collapse into a single term.

We have assumed throughout this section that we are dealing with discrete variables. However, there is nothing specific to discrete variables either in the graphical framework or in the probabilistic construction of the sum-product algorithm. For **Table 8.1** Example of a joint distribution over two binary variables for which the maximum of the joint distribution occurs for different variable values compared to the maxima of the two marginals.

|       | $x=0$ | $x=1$ |
|-------|-------|-------|
| $y=0$ | 0.3   | 0.4   |
| $y=1$ | 0.3   | 0.0   |

continuous variables the summations are simply replaced by integrations. We shall give an example of the sum-product algorithm applied to a graph of linear-Gaussian *Section 13.3* variables when we consider linear dynamical systems.

## **8.4.5 The max-sum algorithm**

The sum-product algorithm allows us to take a joint distribution  $p(x)$  expressed as a factor graph and efficiently find marginals over the component variables. Two other common tasks are to find a setting of the variables that has the largest probability and to find the value of that probability. These can be addressed through a closely related algorithm called *max-sum*, which can be viewed as an application of *dynamic programming* in the context of graphical models (Cormen *et al.*, 2001).

A simple approach to finding latent variable values having high probability would be to run the sum-product algorithm to obtain the marginals  $p(x_i)$  for every variable, and then, for each marginal in turn, to find the value  $x_i^*$  that maximizes that marginal. However, this would give the set of values that are *individually* the most probable. In practice, we typically wish to find the set of values that *jointly* have the largest probability, in other words the vector **x**max that maximizes the joint distribution, so that

$$
\mathbf{x}^{\max} = \arg\max_{\mathbf{x}} p(\mathbf{x})
$$
 (8.87)

for which the corresponding value of the joint probability will be given by

$$
p(\mathbf{x}^{\max}) = \max_{\mathbf{x}} p(\mathbf{x}).
$$
\n(8.88)

In general,  $\mathbf{x}^{\text{max}}$  is not the same as the set of  $x_i^*$  values, as we can easily show using a simple example. Consider the joint distribution  $p(x, y)$  over two binary variables a simple example. Consider the joint distribution  $p(x, y)$  over two binary variables  $x, y \in \{0, 1\}$  given in Table 8.1. The joint distribution is maximized by setting  $x =$ 1 and  $y = 0$ , corresponding the value 0.4. However, the marginal for  $p(x)$ , obtained by summing over both values of y, is given by  $p(x = 0) = 0.6$  and  $p(x = 1) = 0.4$ , and similarly the marginal for y is given by  $p(y = 0) = 0.7$  and  $p(y = 1) = 0.3$ , and so the marginals are maximized by  $x = 0$  and  $y = 0$ , which corresponds to a value of 0.3 for the joint distribution. In fact, it is not difficult to construct examples for which the set of individually most probable values has probability zero under the *Exercise 8.27* joint distribution.

> We therefore seek an efficient algorithm for finding the value of **x** that maximizes the joint distribution  $p(x)$  and that will allow us to obtain the value of the joint distribution at its maximum. To address the second of these problems, we shall simply write out the max operator in terms of its components

$$
\max_{\mathbf{x}} p(\mathbf{x}) = \max_{x_1} \dots \max_{x_M} p(\mathbf{x})
$$
\n(8.89)

*Section 13.3*

where M is the total number of variables, and then substitute for  $p(x)$  using its expansion in terms of a product of factors. In deriving the sum-product algorithm, we made use of the distributive law (8.53) for multiplication. Here we make use of the analogous law for the max operator

$$
\max(ab, ac) = a \max(b, c) \tag{8.90}
$$

which holds if  $a \ge 0$  (as will always be the case for the factors in a graphical model). This allows us to exchange products with maximizations.

Consider first the simple example of a chain of nodes described by (8.49). The evaluation of the probability maximum can be written as

$$
\max_{\mathbf{x}} p(\mathbf{x}) = \frac{1}{Z} \max_{x_1} \cdots \max_{x_N} [\psi_{1,2}(x_1, x_2) \cdots \psi_{N-1,N}(x_{N-1}, x_N)]
$$
  
= 
$$
\frac{1}{Z} \max_{x_1} [\psi_{1,2}(x_1, x_2) [ \cdots \max_{x_N} \psi_{N-1,N}(x_{N-1}, x_N) ]] .
$$

As with the calculation of marginals, we see that exchanging the max and product operators results in a much more efficient computation, and one that is easily interpreted in terms of messages passed from node  $x_N$  backwards along the chain to node  $x_1$ .

We can readily generalize this result to arbitrary tree-structured factor graphs by substituting the expression (8.59) for the factor graph expansion into (8.89) and again exchanging maximizations with products. The structure of this calculation is identical to that of the sum-product algorithm, and so we can simply translate those results into the present context. In particular, suppose that we designate a particular variable node as the 'root' of the graph. Then we start a set of messages propagating inwards from the leaves of the tree towards the root, with each node sending its message towards the root once it has received all incoming messages from its other neighbours. The final maximization is performed over the product of all messages arriving at the root node, and gives the maximum value for  $p(x)$ . This could be called the *max-product* algorithm and is identical to the sum-product algorithm except that summations are replaced by maximizations. Note that at this stage, messages have been sent from leaves to the root, but not in the other direction.

In practice, products of many small probabilities can lead to numerical underflow problems, and so it is convenient to work with the logarithm of the joint distribution. The logarithm is a monotonic function, so that if  $a > b$  then  $\ln a > \ln b$ , and hence the max operator and the logarithm function can be interchanged, so that

$$
\ln\left(\max_{\mathbf{x}} p(\mathbf{x})\right) = \max_{\mathbf{x}} \ln p(\mathbf{x}).
$$
\n(8.91)

The distributive property is preserved because

$$
\max(a+b, a+c) = a + \max(b, c). \tag{8.92}
$$

Thus taking the logarithm simply has the effect of replacing the products in the max-product algorithm with sums, and so we obtain the *max-sum* algorithm. From

# **8.4. Inference in Graphical Models 413**

the results (8.66) and (8.69) derived earlier for the sum-product algorithm, we can readily write down the max-sum algorithm in terms of message passing simply by replacing 'sum' with 'max' and replacing products with sums of logarithms to give

$$
\mu_{f \to x}(x) = \max_{x_1, ..., x_M} \left[ \ln f(x, x_1, ..., x_M) + \sum_{m \in ne(f_s) \setminus x} \mu_{x_m \to f}(x_m) \right] (8.93)
$$

$$
\mu_{x \to f}(x) = \sum_{l \in ne(x) \setminus f} \mu_{f_l \to x}(x). (8.94)
$$

The initial messages sent by the leaf nodes are obtained by analogy with (8.70) and (8.71) and are given by

$$
\mu_{x \to f}(x) = 0 \tag{8.95}
$$

$$
\mu_{f \to x}(x) = \ln f(x) \tag{8.96}
$$

while at the root node the maximum probability can then be computed, by analogy with  $(8.63)$ , using

$$
p^{\max} = \max_{x} \left[ \sum_{s \in \text{ne}(x)} \mu_{f_s \to x}(x) \right]. \tag{8.97}
$$

So far, we have seen how to find the maximum of the joint distribution by propagating messages from the leaves to an arbitrarily chosen root node. The result will be the same irrespective of which node is chosen as the root. Now we turn to the second problem of finding the configuration of the variables for which the joint distribution attains this maximum value. So far, we have sent messages from the leaves to the root. The process of evaluating (8.97) will also give the value  $x^{\max}$  for the most probable value of the root node variable, defined by

$$
x^{\max} = \arg\max_{x} \left[ \sum_{s \in \text{ne}(x)} \mu_{f_s \to x}(x) \right]. \tag{8.98}
$$

At this point, we might be tempted simply to continue with the message passing algorithm and send messages from the root back out to the leaves, using (8.93) and (8.94), then apply (8.98) to all of the remaining variable nodes. However, because we are now maximizing rather than summing, it is possible that there may be multiple configurations of **x** all of which give rise to the maximum value for  $p(\mathbf{x})$ . In such cases, this strategy can fail because it is possible for the individual variable values obtained by maximizing the product of messages at each node to belong to different maximizing configurations, giving an overall configuration that no longer corresponds to a maximum.

The problem can be resolved by adopting a rather different kind of message passing from the root node to the leaves. To see how this works, let us return once again to the simple chain example of N variables  $x_1, \ldots, x_N$  each having K states,

**Figure 8.53** A lattice, or trellis, diagram showing explicitly the  $K$  possible states (one per row of the diagram) for each of the variables  $x_n$  in the chain model. In this illustration  $K = 3$ . The arrow shows the direction of message passing in the max-product algorithm. For every state  $k$  of each variable  $x_n$  (corresponding to column n of the diagram) the function  $\phi(x_n)$  defines a unique state at the previous variable, indicated by the black lines. The two paths through the lattice correspond to configurations that give the global maximum of the joint probability distribution, and either of these can be found by tracing back along the black lines in the opposite direction to the arrow.

Image /page/10/Figure/2 description: The image displays a diagram with three rows labeled k=1, k=2, and k=3. The first row contains red squares, the second row contains green squares, and the third row contains blue squares. The squares in each row are arranged horizontally, with some connections indicated by black lines. The third row also has labels n-2, n-1, n, and n+1 below the squares. An arrow at the top indicates a direction from left to right.

corresponding to the graph shown in Figure 8.38. Suppose we take node  $x_N$  to be the root node. Then in the first phase, we propagate messages from the leaf node  $x_1$ to the root node using

$$
\mu_{x_n \to f_{n,n+1}}(x_n) = \mu_{f_{n-1,n} \to x_n}(x_n)
$$
  
$$
\mu_{f_{n-1,n} \to x_n}(x_n) = \max_{x_{n-1}} [\ln f_{n-1,n}(x_{n-1}, x_n) + \mu_{x_{n-1} \to f_{n-1,n}}(x_n)]
$$

which follow from applying  $(8.94)$  and  $(8.93)$  to this particular graph. The initial message sent from the leaf node is simply

$$
\mu_{x_1 \to f_{1,2}}(x_1) = 0. \tag{8.99}
$$

The most probable value for  $x_N$  is then given by

$$
x_N^{\max} = \underset{x_N}{\arg \max} \left[ \mu_{f_{N-1,N} \to x_N}(x_N) \right]. \tag{8.100}
$$

Now we need to determine the states of the previous variables that correspond to the same maximizing configuration. This can be done by keeping track of which values of the variables gave rise to the maximum state of each variable, in other words by storing quantities given by

$$
\phi(x_n) = \underset{x_{n-1}}{\text{arg}\max} \left[ \ln f_{n-1,n}(x_{n-1}, x_n) + \mu_{x_{n-1} \to f_{n-1,n}}(x_n) \right]. \tag{8.101}
$$

To understand better what is happening, it is helpful to represent the chain of variables in terms of a *lattice* or *trellis* diagram as shown in Figure 8.53. Note that this is not a probabilistic graphical model because the nodes represent individual states of variables, while each variable corresponds to a column of such states in the diagram. For each state of a given variable, there is a unique state of the previous variable that maximizes the probability (ties are broken either systematically or at random), corresponding to the function  $\phi(x_n)$  given by (8.101), and this is indicated

by the lines connecting the nodes. Once we know the most probable value of the final node  $x_N$ , we can then simply follow the link back to find the most probable state of node  $x_{N-1}$  and so on back to the initial node  $x_1$ . This corresponds to propagating a message back down the chain using

$$
x_{n-1}^{\max} = \phi(x_n^{\max})
$$
\n
$$
(8.102)
$$

and is known as *back-tracking*. Note that there could be several values of  $x_{n-1}$  all of which give the maximum value in (8.101). Provided we chose one of these values when we do the back-tracking, we are assured of a globally consistent maximizing configuration.

In Figure 8.53, we have indicated two paths, each of which we shall suppose corresponds to a global maximum of the joint probability distribution. If  $k = 2$ and  $k = 3$  each represent possible values of  $x_N^{\max}$ , then starting from either state and tracing back along the black lines, which corresponds to iterating (8.102), we obtain a valid global maximum configuration. Note that if we had run a forward pass of max-sum message passing followed by a backward pass and then applied (8.98) at each node separately, we could end up selecting some states from one path and some from the other path, giving an overall configuration that is not a global maximizer. We see that it is necessary instead to keep track of the maximizing states during the forward pass using the functions  $\phi(x_n)$  and then use back-tracking to find a consistent solution.

The extension to a general tree-structured factor graph should now be clear. If a message is sent from a factor node f to a variable node  $x$ , a maximization is performed over all other variable nodes  $x_1, \ldots, x_M$  that are neighbours of that factor node, using (8.93). When we perform this maximization, we keep a record of which values of the variables  $x_1, \ldots, x_M$  gave rise to the maximum. Then in the back-tracking step, having found  $x^{\text{max}}$ , we can then use these stored values to assign consistent maximizing states  $x_1^{\max}, \ldots, x_M^{\max}$ . The max-sum algorithm, with back-tracking, gives an exact maximizing configuration for the variables provided the factor graph is a tree. An important application of this technique is for finding the most probable sequence of hidden states in a hidden Markov model, in which *Section 13.2* case it is known as the *Viterbi* algorithm.

*Section 13.2*

As with the sum-product algorithm, the inclusion of evidence in the form of observed variables is straightforward. The observed variables are clamped to their observed values, and the maximization is performed over the remaining hidden variables. This can be shown formally by including identity functions for the observed variables into the factor functions, as we did for the sum-product algorithm.

It is interesting to compare max-sum with the iterated conditional modes (ICM) algorithm described on page 389. Each step in ICM is computationally simpler because the 'messages' that are passed from one node to the next comprise a single value consisting of the new state of the node for which the conditional distribution is maximized. The max-sum algorithm is more complex because the messages are functions of node variables  $x$  and hence comprise a set of  $K$  values for each possible state of x. Unlike max-sum, however, ICM is not guaranteed to find a global maximum even for tree-structured graphs.

# **8.4.6 Exact inference in general graphs**

The sum-product and max-sum algorithms provide efficient and exact solutions to inference problems in tree-structured graphs. For many practical applications, however, we have to deal with graphs having loops.

The message passing framework can be generalized to arbitrary graph topologies, giving an exact inference procedure known as the *junction tree algorithm* (Lauritzen and Spiegelhalter, 1988; Jordan, 2007). Here we give a brief outline of the key steps involved. This is not intended to convey a detailed understanding of the algorithm, but rather to give a flavour of the various stages involved. If the starting point is a directed graph, it is first converted to an undirected graph by moralization, whereas if starting from an undirected graph this step is not required. Next the graph is *triangulated*, which involves finding chord-less cycles containing four or more nodes and adding extra links to eliminate such chord-less cycles. For instance, in the graph in Figure 8.36, the cycle  $A-C-B-D-A$  is chord-less a link could be added between A and B or alternatively between C and D. Note that the joint distribution for the resulting triangulated graph is still defined by a product of the same potential functions, but these are now considered to be functions over expanded sets of variables. Next the triangulated graph is used to construct a new tree-structured undirected graph called a *join tree*, whose nodes correspond to the maximal cliques of the triangulated graph, and whose links connect pairs of cliques that have variables in common. The selection of which pairs of cliques to connect in this way is important and is done so as to give a *maximal spanning tree* defined as follows. Of all possible trees that link up the cliques, the one that is chosen is one for which the *weight* of the tree is largest, where the weight for a link is the number of nodes shared by the two cliques it connects, and the weight for the tree is the sum of the weights for the links. If the tree is condensed, so that any clique that is a subset of another clique is absorbed into the larger clique, this gives a *junction tree*. As a consequence of the triangulation step, the resulting tree satisfies the *running intersection property*, which means that if a variable is contained in two cliques, then it must also be contained in every clique on the path that connects them. This ensures that inference about variables will be consistent across the graph. Finally, a two-stage message passing algorithm, essentially equivalent to the sum-product algorithm, can now be applied to this junction tree in order to find marginals and conditionals. Although the junction tree algorithm sounds complicated, at its heart is the simple idea that we have used already of exploiting the factorization properties of the distribution to allow sums and products to be interchanged so that partial summations can be performed, thereby avoiding having to work directly with the joint distribution. The role of the junction tree is to provide a precise and efficient way to organize these computations. It is worth emphasizing that this is achieved using purely graphical operations!

The junction tree is exact for arbitrary graphs and is efficient in the sense that for a given graph there does not in general exist a computationally cheaper approach. Unfortunately, the algorithm must work with the joint distributions within each node (each of which corresponds to a clique of the triangulated graph) and so the computational cost of the algorithm is determined by the number of variables in the largest

clique and will grow exponentially with this number in the case of discrete variables. An important concept is the *treewidth* of a graph (Bodlaender, 1993), which is defined in terms of the number of variables in the largest clique. In fact, it is defined to be as one less than the size of the largest clique, to ensure that a tree has a treewidth of 1. Because there in general there can be multiple different junction trees that can be constructed from a given starting graph, the treewidth is defined by the junction tree for which the largest clique has the fewest variables. If the treewidth of the original graph is high, the junction tree algorithm becomes impractical.

## **8.4.7 Loopy belief propagation**

For many problems of practical interest, it will not be feasible to use exact inference, and so we need to exploit effective approximation methods. An important class of such approximations, that can broadly be called *variational* methods, will be discussed in detail in Chapter 10. Complementing these deterministic approaches is a wide range of *sampling* methods, also called *Monte Carlo* methods, that are based on stochastic numerical sampling from distributions and that will be discussed at length in Chapter 11.

Here we consider one simple approach to approximate inference in graphs with loops, which builds directly on the previous discussion of exact inference in trees. The idea is simply to apply the sum-product algorithm even though there is no guarantee that it will yield good results. This approach is known as *loopy belief propagation* (Frey and MacKay, 1998) and is possible because the message passing rules (8.66) and (8.69) for the sum-product algorithm are purely local. However, because the graph now has cycles, information can flow many times around the graph. For some models, the algorithm will converge, whereas for others it will not.

In order to apply this approach, we need to define a *message passing schedule*. Let us assume that one message is passed at a time on any given link and in any given direction. Each message sent from a node replaces any previous message sent in the same direction across the same link and will itself be a function only of the most recent messages received by that node at previous steps of the algorithm.

We have seen that a message can only be sent across a link from a node when all other messages have been received by that node across its other links. Because there are loops in the graph, this raises the problem of how to initiate the message passing algorithm. To resolve this, we suppose that an initial message given by the unit function has been passed across every link in each direction. Every node is then in a position to send a message.

There are now many possible ways to organize the message passing schedule. For example, the *flooding schedule* simultaneously passes a message across every link in both directions at each time step, whereas schedules that pass one message at a time are called *serial schedules*.

Following Kschischnang *et al.* (2001), we will say that a (variable or factor) node a has a message *pending* on its link to a node b if node a has received any message on any of its other links since the last time it send a message to b. Thus, when a node receives a message on one of its links, this creates pending messages on all of its other links. Only pending messages need to be transmitted because

other messages would simply duplicate the previous message on the same link. For graphs that have a tree structure, any schedule that sends only pending messages will eventually terminate once a message has passed in each direction across every *Exercise 8.29* link. At this point, there are no pending messages, and the product of the received messages at every variable give the exact marginal. In graphs having loops, however, the algorithm may never terminate because there might always be pending messages, although in practice it is generally found to converge within a reasonable time for most applications. Once the algorithm has converged, or once it has been stopped if convergence is not observed, the (approximate) local marginals can be computed using the product of the most recently received incoming messages to each variable node or factor node on every link.

> In some applications, the loopy belief propagation algorithm can give poor results, whereas in other applications it has proven to be very effective. In particular, state-of-the-art algorithms for decoding certain kinds of error-correcting codes are equivalent to loopy belief propagation (Gallager, 1963; Berrou *et al.*, 1993; McEliece *et al.*, 1998; MacKay and Neal, 1999; Frey, 1998).

## **8.4.8 Learning the graph structure**

In our discussion of inference in graphical models, we have assumed that the structure of the graph is known and fixed. However, there is also interest in going beyond the inference problem and learning the graph structure itself from data (Friedman and Koller, 2003). This requires that we define a space of possible structures as well as a measure that can be used to score each structure.

From a Bayesian viewpoint, we would ideally like to compute a posterior distribution over graph structures and to make predictions by averaging with respect to this distribution. If we have a prior  $p(m)$  over graphs indexed by m, then the posterior distribution is given by

$$
p(m|\mathcal{D}) \propto p(m)p(\mathcal{D}|m)
$$
\n(8.103)

where D is the observed data set. The model evidence  $p(\mathcal{D}|m)$  then provides the score for each model. However, evaluation of the evidence involves marginalization over the latent variables and presents a challenging computational problem for many models.

Exploring the space of structures can also be problematic. Because the number of different graph structures grows exponentially with the number of nodes, it is often necessary to resort to heuristics to find good candidates.

# **Exercises**

- **8.1**  $(\star)$  **www** By marginalizing out the variables in order, show that the representation (8.5) for the joint distribution of a directed graph is correctly normalized, provided each of the conditional distributions is normalized.
- **8.2** (\*) **WWW** Show that the property of there being no directed cycles in a directed graph follows from the statement that there exists an ordered numbering of the nodes such that for each node there are no links going to a lower-numbered node.

**Exercises 419**

| <b>Table 8.2</b> The joint distribution over three binary variables. |   |   |            |
|----------------------------------------------------------------------|---|---|------------|
| a                                                                    | b | c | $p(a,b,c)$ |

| $\alpha$ | h            | $\overline{c}$ | p(a, b, c)         |
|----------|--------------|----------------|--------------------|
| 0        | 0            | 0              | $0.\overline{192}$ |
| 0        | $\Omega$     | 1              | 0.144              |
| 0        | 1            | 0              | 0.048              |
| 0        | $\mathbf{1}$ | 1              | 0.216              |
| 1        | $\Omega$     | 0              | 0.192              |
| 1        | 0            | 1              | 0.064              |
| 1        | 1            | 0              | 0.048              |
| 1        |              |                | 0.096              |

- **8.3**  $(\star \star)$  Consider three binary variables  $a, b, c \in \{0, 1\}$  having the joint distribution given in Table 8.2. Show by direct evaluation that this distribution has the property that a and b are marginally dependent, so that  $p(a, b) \neq p(a)p(b)$ , but that they become independent when conditioned on c, so that  $p(a, b|c) = p(a|c)p(b|c)$  for both  $c = 0$  and  $c = 1$ .
- **8.4**  $(\star \star)$  Evaluate the distributions  $p(a)$ ,  $p(b|c)$ , and  $p(c|a)$  corresponding to the joint distribution given in Table 8.2. Hence show by direct evaluation that  $p(a, b, c)$  =  $p(a)p(c|a)p(b|c)$ . Draw the corresponding directed graph.
- **8.5 ( ) www** Draw a directed probabilistic graphical model corresponding to the relevance vector machine described by (7.79) and (7.80).
- **8.6** ( $\star$ ) For the model shown in Figure 8.13, we have seen that the number of parameters required to specify the conditional distribution  $p(y|x_1,\ldots,x_M)$ , where  $x_i \in \{0,1\}$ , could be reduced from  $2^M$  to  $M + 1$  by making use of the logistic sigmoid representation (8.10). An alternative representation (Pearl, 1988) is given by

$$
p(y = 1|x_1, ..., x_M) = 1 - (1 - \mu_0) \prod_{i=1}^{M} (1 - \mu_i)^{x_i}
$$
 (8.104)

where the parameters  $\mu_i$  represent the probabilities  $p(x_i = 1)$ , and  $\mu_0$  is an additional parameters satisfying  $0 \le \mu_0 \le 1$ . The conditional distribution (8.104) is known as the *noisy-OR*. Show that this can be interpreted as a 'soft' (probabilistic) form of the logical OR function (i.e., the function that gives  $y = 1$  whenever at least one of the  $x_i = 1$ ). Discuss the interpretation of  $\mu_0$ .

- **8.7**  $(\star \star)$  Using the recursion relations (8.15) and (8.16), show that the mean and covariance of the joint distribution for the graph shown in Figure 8.14 are given by (8.17) and (8.18), respectively.
- **8.8 (** $\star$ ) **www** Show that  $a \perp b$ ,  $c \mid d$  implies  $a \perp b \mid d$ .
- **8.9** ( $\star$ ) **www** Using the d-separation criterion, show that the conditional distribution for a node **x** in a directed graph, conditioned on all of the nodes in the Markov blanket, is independent of the remaining variables in the graph.

**Figure 8.54** Example of a graphical model used to explore the conditional independence properties of the head-to-head path  $a-c-b$  when a descendant of c, namely the node  $d$ , is observed.

- **8.10**  $(\star)$  Consider the directed graph shown in Figure 8.54 in which none of the variables is observed. Show that  $a \perp b \mid \emptyset$ . Suppose we now observe the variable d. Show that in general  $a \not\perp b \mid d$ .
- **8.11**  $(\star \star)$  Consider the example of the car fuel system shown in Figure 8.21, and suppose that instead of observing the state of the fuel gauge  $G$  directly, the gauge is seen by the driver  $D$  who reports to us the reading on the gauge. This report is either that the gauge shows full  $D = 1$  or that it shows empty  $D = 0$ . Our driver is a bit unreliable, as expressed through the following probabilities

$$
p(D = 1|G = 1) = 0.9 \tag{8.105}
$$

c

 $a \bigcap b$ 

d

$$
p(D = 0|G = 0) = 0.9. \tag{8.106}
$$

Suppose that the driver tells us that the fuel gauge shows empty, in other words that we observe  $D = 0$ . Evaluate the probability that the tank is empty given only this observation. Similarly, evaluate the corresponding probability given also the observation that the battery is flat, and note that this second probability is lower. Discuss the intuition behind this result, and relate the result to Figure 8.54.

- **8.12** ( $\star$ ) **www** Show that there are  $2^{M(M-1)/2}$  distinct undirected graphs over a set of M distinct random variables. Draw the 8 possibilities for the case of  $M = 3$ .
- **8.13**  $(\star)$  Consider the use of iterated conditional modes (ICM) to minimize the energy function given by (8.42). Write down an expression for the difference in the values of the energy associated with the two states of a particular variable  $x_j$ , with all other variables held fixed, and show that it depends only on quantities that are local to  $x_i$ in the graph.
- **8.14** ( $\star$ ) Consider a particular case of the energy function given by (8.42) in which the coefficients  $\beta = h = 0$ . Show that the most probable configuration of the latent variables is given by  $x_i = y_i$  for all *i*.
- **8.15**  $(\star \star)$  **www** Show that the joint distribution  $p(x_{n-1}, x_n)$  for two neighbouring nodes in the graph shown in Figure 8.38 is given by an expression of the form (8.58).

- **8.16** ( $\star\star$ ) Consider the inference problem of evaluating  $p(\mathbf{x}_n|\mathbf{x}_N)$  for the graph shown in Figure 8.38, for all nodes  $n \in \{1, \ldots, N-1\}$ . Show that the message passing algorithm discussed in Section 8.4.1 can be used to solve this efficiently, and discuss which messages are modified and in what way.
- **8.17**  $(\star \star)$  Consider a graph of the form shown in Figure 8.38 having  $N = 5$  nodes, in which nodes  $x_3$  and  $x_5$  are observed. Use d-separation to show that  $x_2 \perp x_5 \mid x_3$ . Show that if the message passing algorithm of Section 8.4.1 is applied to the evaluation of  $p(x_2|x_3, x_5)$ , the result will be independent of the value of  $x_5$ .
- **8.18**  $(\star \star)$  **www** Show that a distribution represented by a directed tree can trivially be written as an equivalent distribution over the corresponding undirected tree. Also show that a distribution expressed as an undirected tree can, by suitable normalization of the clique potentials, be written as a directed tree. Calculate the number of distinct directed trees that can be constructed from a given undirected tree.
- **8.19**  $(\star \star)$  Apply the sum-product algorithm derived in Section 8.4.4 to the chain-ofnodes model discussed in Section 8.4.1 and show that the results (8.54), (8.55), and (8.57) are recovered as a special case.
- **8.20** (\*) **www** Consider the message passing protocol for the sum-product algorithm on a tree-structured factor graph in which messages are first propagated from the leaves to an arbitrarily chosen root node and then from the root node out to the leaves. Use proof by induction to show that the messages can be passed in such an order that at every step, each node that must send a message has received all of the incoming messages necessary to construct its outgoing messages.
- **8.21**  $(\star \star)$  **www** Show that the marginal distributions  $p(\mathbf{x}_s)$  over the sets of variables  $\mathbf{x}_s$  associated with each of the factors  $f_x(\mathbf{x}_s)$  in a factor graph can be found by first running the sum-product message passing algorithm and then evaluating the required marginals using (8.72).
- **8.22**  $(\star)$  Consider a tree-structured factor graph, in which a given subset of the variable nodes form a connected subgraph (i.e., any variable node of the subset is connected to at least one of the other variable nodes via a single factor node). Show how the sum-product algorithm can be used to compute the marginal distribution over that subset.
- **8.23**  $(\star \star)$  **www** In Section 8.4.4, we showed that the marginal distribution  $p(x_i)$  for a variable node  $x_i$  in a factor graph is given by the product of the messages arriving at this node from neighbouring factor nodes in the form (8.63). Show that the marginal  $p(x_i)$  can also be written as the product of the incoming message along any one of the links with the outgoing message along the same link.
- **8.24** ( $\star \star$ ) Show that the marginal distribution for the variables  $x_s$  in a factor  $f_s(x_s)$  in a tree-structured factor graph, after running the sum-product message passing algorithm, can be written as the product of the message arriving at the factor node along all its links, times the local factor  $f(\mathbf{x}_s)$ , in the form (8.72).

- **8.25**  $(\star \star)$  In (8.86), we verified that the sum-product algorithm run on the graph in Figure 8.51 with node  $x_3$  designated as the root node gives the correct marginal for  $x_2$ . Show that the correct marginals are obtained also for  $x_1$  and  $x_3$ . Similarly, show that the use of the result (8.72) after running the sum-product algorithm on this graph gives the correct joint distribution for  $x_1, x_2$ .
- **8.26**  $(\star)$  Consider a tree-structured factor graph over discrete variables, and suppose we wish to evaluate the joint distribution  $p(x_a, x_b)$  associated with two variables  $x_a$  and  $x<sub>b</sub>$  that do not belong to a common factor. Define a procedure for using the sumproduct algorithm to evaluate this joint distribution in which one of the variables is successively clamped to each of its allowed values.
- **8.27**  $(\star \star)$  Consider two discrete variables x and y each having three possible states, for example  $x, y \in \{0, 1, 2\}$ . Construct a joint distribution  $p(x, y)$  over these variables having the property that the value  $\hat{x}$  that maximizes the marginal  $p(x)$ , along with the value  $\hat{y}$  that maximizes the marginal  $p(y)$ , together have probability zero under the joint distribution, so that  $p(\hat{x},\hat{y})=0$ .
- **8.28**  $(\star \star)$  **WWW** The concept of a *pending* message in the sum-product algorithm for a factor graph was defined in Section 8.4.7. Show that if the graph has one or more cycles, there will always be at least one pending message irrespective of how long the algorithm runs.
- **8.29**  $(\star \star)$  **www** Show that if the sum-product algorithm is run on a factor graph with a tree structure (no loops), then after a finite number of messages have been sent, there will be no pending messages.
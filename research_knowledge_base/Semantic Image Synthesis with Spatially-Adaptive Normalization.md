# <span id="page-0-1"></span>Semantic Image Synthesis with Spatially-Adaptive Normalization

Taesung Park<sup>1,2∗</sup> <PERSON><PERSON><PERSON><sup>2</sup> <PERSON><PERSON><PERSON><PERSON><sup>2</sup> <PERSON><PERSON><PERSON><sup>2,3</sup>

<sup>1</sup>UC Berkeley <sup>2</sup>NVIDIA <sup>2,3</sup>MIT CSAIL

Image /page/0/Picture/3 description: The image displays a grid of images demonstrating semantic manipulation and style transfer. The top row shows five segmentation maps, illustrating changes in elements like sky, clouds, sea, grass, and mountains. The left column presents three original style images: a serene seascape at sunset, a vibrant sunset over water, and a painting of a water scene with trees. The remaining grid cells show the results of applying the styles from the left column to the semantic maps from the top row. The overall presentation highlights how different styles can be applied to a base scene defined by a segmentation map.

Figure 1: Our model allows user control over both semantic and style as synthesizing an image. The semantic (e.g., the existence of a tree) is controlled via a label map (the top row), while the style is controlled via the reference style image (the leftmost column). Please visit our [website](https://github.com/NVlabs/SPADE) for interactive image synthesis demos.

## <span id="page-0-0"></span>Abstract

*We propose spatially-adaptive normalization, a simple but effective layer for synthesizing photorealistic images given an input semantic layout. Previous methods directly feed the semantic layout as input to the deep network, which is then processed through stacks of convolution, normalization, and nonlinearity layers. We show that this is suboptimal as the normalization layers tend to "wash away" semantic information. To address the issue, we propose using the input layout for modulating the activations in normalization layers through a spatially-adaptive, learned transformation. Experiments on several challenging datasets demonstrate the advantage of the proposed method over existing approaches, regarding both visual fidelity and alignment with input layouts. Finally, our model allows user control over both semantic and style. Code is available at*

## <https://github.com/NVlabs/SPADE>*.*

## 1. Introduction

Conditional image synthesis refers to the task of generating photorealistic images conditioning on certain input data. Seminal work computes the output image by stitching pieces from a single image (e.g., Image Analogies  $[16]$ ) or using an image collection  $[7, 14, 23, 30, 35]$  $[7, 14, 23, 30, 35]$  $[7, 14, 23, 30, 35]$  $[7, 14, 23, 30, 35]$  $[7, 14, 23, 30, 35]$  $[7, 14, 23, 30, 35]$  $[7, 14, 23, 30, 35]$  $[7, 14, 23, 30, 35]$  $[7, 14, 23, 30, 35]$ . Recent methods directly learn the mapping using neural networks [\[3,](#page-8-5) [6,](#page-8-6) [22,](#page-8-7) [47,](#page-9-1) [48,](#page-9-2) [54,](#page-9-3) [55,](#page-9-4) [56\]](#page-9-5). The latter methods are faster and require no external database of images.

We are interested in a specific form of conditional image synthesis, which is converting a semantic segmentation mask to a photorealistic image. This form has a wide range of applications such as content generation and image editing  $[6, 22, 48]$  $[6, 22, 48]$  $[6, 22, 48]$  $[6, 22, 48]$  $[6, 22, 48]$ . We refer to this form as semantic image synthesis. In this paper, we show that the conventional network architecture  $[22, 48]$  $[22, 48]$  $[22, 48]$ , which is built by stacking convolutional, normalization, and nonlinearity layers, is at best

<sup>∗</sup>Taesung Park contributed to the work during his NVIDIA internship.

<span id="page-1-1"></span>suboptimal because their normalization layers tend to "wash away" information contained in the input semantic masks. To address the issue, we propose *spatially-adaptive normalization*, a conditional normalization layer that modulates the activations using input semantic layouts through a spatiallyadaptive, learned transformation and can effectively propagate the semantic information throughout the network.

We conduct experiments on several challenging datasets including the COCO-Stuff  $[4, 32]$  $[4, 32]$  $[4, 32]$ , the ADE20K  $[58]$ , and the Cityscapes [\[9\]](#page-8-10). We show that with the help of our spatially-adaptive normalization layer, a compact network can synthesize significantly better results compared to several state-of-the-art methods. Additionally, an extensive ablation study demonstrates the effectiveness of the proposed normalization layer against several variants for the semantic image synthesis task. Finally, our method supports multimodal and style-guided image synthesis, enabling controllable, diverse outputs, as shown in Figure [1.](#page-0-0) Also, please see our SIGGRAPH 2019 Real-Time Live [demo](https://www.youtube.com/watch?v=Gz9weuemhDA&feature=youtu.be&t=2949) and try our online [demo](https://github.com/NVlabs/SPADE) by yourself.

## 2. Related Work

Deep generative models can learn to synthesize images. Recent methods include generative adversarial networks (GANs) [\[13\]](#page-8-11) and variational autoencoder (VAE) [\[28\]](#page-8-12). Our work is built on GANs but aims for the conditional image synthesis task. The GANs consist of a generator and a discriminator where the goal of the generator is to produce realistic images so that the discriminator cannot tell the synthesized images apart from the real ones.

Conditional image synthesis exists in many forms that differ in the type of input data. For example, class-conditional models [\[3,](#page-8-5) [36,](#page-9-7) [37,](#page-9-8) [39,](#page-9-9) [41\]](#page-9-10) learn to synthesize images given category labels. Researchers have explored various models for generating images based on text [\[18](#page-8-13)[,44](#page-9-11)[,52,](#page-9-12)[55\]](#page-9-4). Another widely-used form is image-to-image translation based on a type of conditional GANs [\[20,](#page-8-14) [22,](#page-8-7) [24,](#page-8-15) [25,](#page-8-16) [33,](#page-8-17) [57,](#page-9-13) [59,](#page-9-14) [60\]](#page-9-15), where both input and output are images. Compared to earlier non-parametric methods [\[7,](#page-8-1) [16,](#page-8-0) [23\]](#page-8-3), learning-based methods typically run faster during test time and produce more realistic results. In this work, we focus on converting segmentation masks to photorealistic images. We assume the training dataset contains registered segmentation masks and images. With the proposed spatially-adaptive normalization, our compact network achieves better results compared to leading methods.

Unconditional normalization layers have been an important component in modern deep networks and can be found in various classifiers, including the Local Response Normalization in the AlexNet [\[29\]](#page-8-18) and the Batch Normalization (BatchNorm) in the Inception-v2 network [\[21\]](#page-8-19). Other popular normalization layers include the Instance Normalization (InstanceNorm) [\[46\]](#page-9-16), the Layer Normalization [\[2\]](#page-8-20), the Group Normalization [\[50\]](#page-9-17), and the Weight Normalization [\[45\]](#page-9-18). We label these normalization layers as unconditional as they do not depend on external data in contrast to the conditional normalization layers discussed below.

Conditional normalization layers include the Conditional Batch Normalization (Conditional BatchNorm) [\[11\]](#page-8-21) and Adaptive Instance Normalization (AdaIN) [\[19\]](#page-8-22). Both were first used in the style transfer task and later adopted in various vision tasks [\[3,](#page-8-5) [8,](#page-8-23) [10,](#page-8-24) [20,](#page-8-14) [26,](#page-8-25) [36,](#page-9-7) [39,](#page-9-9) [42,](#page-9-19) [49,](#page-9-20) [54\]](#page-9-3). Different from the earlier normalization techniques, conditional normalization layers require external data and generally operate as follows. First, layer activations are normalized to zero mean and unit deviation. Then the normalized activations are *denormalized* by modulating the activation using a learned affine transformation whose parameters are inferred from external data. For style transfer tasks [\[11,](#page-8-21) [19\]](#page-8-22), the affine parameters are used to control the global style of the output, and hence are uniform across spatial coordinates. In contrast, our proposed normalization layer applies a spatially-varying affine transformation, making it suitable for image synthesis from semantic masks. Wang *et al*. proposed a closely related method for image super-resolution [\[49\]](#page-9-20). Both methods are built on spatiallyadaptive modulation layers that condition on semantic inputs. While they aim to incorporate semantic information into super-resolution, our goal is to design a generator for style and semantics disentanglement. We focus on providing the semantic information in the context of modulating normalized activations. We use semantic maps in different scales, which enables coarse-to-fine generation. The reader is encouraged to review their work for more details.

### 3. Semantic Image Synthesis

Let  $m \in L^{H \times W}$  be a semantic segmentation mask where  $\mathbb L$  is a set of integers denoting the semantic labels, and  $H$  and  $W$  are the image height and width. Each entry in m denotes the semantic label of a pixel. We aim to learn a mapping function that can convert an input segmentation mask m to a photorealistic image.

Spatially-adaptive denormalization. Let  $\mathbf{h}^i$  denote the activations of the  $i$ -th layer of a deep convolutional network for a batch of N samples. Let  $C<sup>i</sup>$  be the number of channels in the layer. Let  $H^i$  and  $W^i$  be the height and width of the activation map in the layer. We propose a new conditional normalization method called the SPatially-Adaptive (DE)normalization<sup>[1](#page-1-0)</sup> (SPADE). Similar to the Batch Normalization  $[21]$ , the activation is normalized in the channelwise manner and then modulated with learned scale and bias. Figure [2](#page-2-0) illustrates the SPADE design. The activation

<span id="page-1-0"></span><sup>&</sup>lt;sup>1</sup>Conditional normalization  $[11, 19]$  $[11, 19]$  $[11, 19]$  uses external data to denormalize the normalized activations; i.e., the denormalization part is conditional.

<span id="page-2-3"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: This is a diagram illustrating a neural network architecture. An input image, depicted as a flat plane with colored regions representing a landscape with a tree, undergoes a 'conv' operation. This leads to two parallel paths. One path involves a 'conv' operation followed by another 'conv' operation, resulting in a block labeled with 'beta'. The second path starts with 'Batch Norm' enclosed in a dashed box, followed by a 'conv' operation, and then a block labeled with 'gamma'. The outputs of these two paths are combined through element-wise multiplication (indicated by a circle with an 'x') and then element-wise addition (indicated by a circle with a '+').

Figure 2: In the SPADE, the mask is first projected onto an embedding space and then convolved to produce the modulation parameters  $\gamma$  and  $\beta$ . Unlike prior conditional normalization methods,  $\gamma$  and  $\beta$  are not vectors, but tensors with spatial dimensions. The produced  $\gamma$  and  $\beta$  are multiplied and added to the normalized activation element-wise.

value at site  $(n \in N, c \in C^i, y \in H^i, x \in W^i)$  is

<span id="page-2-1"></span>
$$
\gamma_{c,y,x}^i(\mathbf{m}) \frac{h_{n,c,y,x}^i - \mu_c^i}{\sigma_c^i} + \beta_{c,y,x}^i(\mathbf{m}) \tag{1}
$$

where  $h_{n,c,y,x}^{i}$  is the activation at the site before normalization and  $\mu_c^i$  and  $\sigma_c^i$  are the mean and standard deviation of the activations in channel  $c$ :

$$
\mu_c^i = \frac{1}{NH^iW^i} \sum_{n,y,x} h_{n,c,y,x}^i \tag{2}
$$

$$
\sigma_c^i = \sqrt{\frac{1}{NH^i W^i} \sum_{n,y,x} \left( (h_{n,c,y,x}^i)^2 - (\mu_c^i)^2 \right)}.
$$
 (3)

The variables  $\gamma_{c,y,x}^i(\mathbf{m})$  and  $\beta_{c,y,x}^i(\mathbf{m})$  in [\(1\)](#page-2-1) are the learned modulation parameters of the normalization layer. In contrast to the BatchNorm [\[21\]](#page-8-19), they depend on the input segmentation mask and vary with respect to the location  $(y, x)$ . We use the symbol  $\gamma_{c, y, x}^{i}$  and  $\beta_{c, y, x}^{i}$  to denote the functions that convert m to the scaling and bias values at the site  $(c, y, x)$  in the *i*-th activation map. We implement the functions  $\gamma_{c,y,x}^i$  and  $\beta_{c,y,x}^i$  using a simple two-layer convolutional network, whose design is in the appendix.

In fact, SPADE is related to, and is a generalization of several existing normalization layers. First, replacing the segmentation mask m with the image class label and making the modulation parameters spatially-invariant (i.e.,  $\gamma^i_{c,y_1,x_1}\equiv\gamma^i_{c,y_2,x_2}$  and  $\beta^i_{c,y_1,x_1}\equiv\beta^i_{c,y_2,x_2}$  for any  $y_1,y_2\in$  $\{1, 2, ..., H^i\}$  and  $x_1, x_2 \in \{1, 2, ..., W^i\}$ , we arrive at the form of the Conditional BatchNorm [\[11\]](#page-8-21). Indeed, for any spatially-invariant conditional data, our method reduces to the Conditional BatchNorm. Similarly, we can arrive at the AdaIN [\[19\]](#page-8-22) by replacing m with a real image, making the modulation parameters spatially-invariant, and setting  $N = 1$ . As the modulation parameters are adaptive to the input segmentation mask, the proposed SPADE is better suited for semantic image synthesis.

<span id="page-2-2"></span>Image /page/2/Figure/9 description: The image displays a comparison of image generation results for a scene with sky and grass. The top row shows the input segmentation map for the sky, labeled 'sky' in light blue, next to a gray placeholder for the 'pix2pixHD' output, and finally the 'SPADE' generated image of a clear blue sky. The bottom row shows the input segmentation map for the grass, labeled 'grass' in green, next to another gray placeholder for 'pix2pixHD', and the 'SPADE' generated image of green grass.

Figure 3: Comparing results given uniform segmentation maps: while the SPADE generator produces plausible textures, the pix2pixHD generator  $[48]$  produces two identical outputs due to the loss of the semantic information after the normalization layer.

SPADE generator. With the SPADE, there is no need to feed the segmentation map to the first layer of the generator, since the learned modulation parameters have encoded enough information about the label layout. Therefore, we discard encoder part of the generator, which is commonly used in recent architectures [\[22,](#page-8-7) [48\]](#page-9-2). This simplification results in a more lightweight network. Furthermore, similarly to existing class-conditional generators  $[36,39,54]$  $[36,39,54]$  $[36,39,54]$ , the new generator can take a random vector as input, enabling a simple and natural way for multi-modal synthesis [\[20,](#page-8-14) [60\]](#page-9-15).

Figure [4](#page-3-0) illustrates our generator architecture, which employs several ResNet blocks [\[15\]](#page-8-26) with upsampling layers. The modulation parameters of all the normalization layers are learned using the SPADE. Since each residual block operates at a different scale, we downsample the semantic mask to match the spatial resolution.

We train the generator with the same multi-scale discriminator and loss function used in pix2pixHD [\[48\]](#page-9-2) except that we replace the least squared loss term [\[34\]](#page-9-21) with the hinge loss term [\[31,](#page-8-27)[38,](#page-9-22)[54\]](#page-9-3). We test several ResNet-based discriminators used in recent unconditional GANs [\[1,](#page-8-28) [36,](#page-9-7) [39\]](#page-9-9) but observe similar results at the cost of a higher GPU memory requirement. Adding the SPADE to the discriminator also yields a similar performance. For the loss function, we observe that removing any loss term in the pix2pixHD loss function lead to degraded generation results.

Why does the SPADE work better? A short answer is that it can better preserve semantic information against common normalization layers. Specifically, while normalization layers such as the InstanceNorm [\[46\]](#page-9-16) are essential pieces in almost all the state-of-the-art conditional image synthesis models [\[48\]](#page-9-2), they tend to wash away semantic information when applied to uniform or flat segmentation masks.

Let us consider a simple module that first applies convolution to a segmentation mask and then normalization. Furthermore, let us assume that a segmentation mask with a single label is given as input to the module (e.g., all the

<span id="page-3-1"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: This figure illustrates a neural network architecture. On the left, a "SPADE ResBlk" block is shown, which takes two image inputs and processes them through a series of SPADE layers, ReLU activations, and 3x3 convolutions, with a residual connection indicated by a plus sign. To the right, a pipeline begins with a "pix2pixHD" component, followed by a series of "SPADE ResBlk" blocks. These blocks are depicted as progressively taller rectangular prisms, suggesting an increase in feature dimensionality or resolution. The process starts with a 2D Gaussian-like distribution plot, which is then transformed into a 3D representation, and subsequently processed through multiple "SPADE ResBlk" stages, each associated with an input image. The final output is a tall rectangular prism.

Figure 4: In the SPADE generator, each normalization layer uses the segmentation mask to modulate the layer activations. *(left)* Structure of one residual block with the SPADE. *(right)* The generator contains a series of the SPADE residual blocks with upsampling layers. Our architecture achieves better performance with a smaller number of parameters by removing the downsampling layers of leading image-to-image translation networks such as the pix2pixHD model [\[48\]](#page-9-2).

pixels have the same label such as sky or grass). Under this setting, the convolution outputs are again uniform, with different labels having different uniform values. Now, after we apply InstanceNorm to the output, the normalized activation will become all zeros no matter what the input semantic label is given. Therefore, semantic information is totally lost. This limitation applies to a wide range of generator architectures, including pix2pixHD and its variant that concatenates the semantic mask at all intermediate layers, as long as a network applies convolution and then normalization to the semantic mask. In Figure  $3$ , we empirically show this is precisely the case for pix2pixHD. Because a segmentation mask consists of a few uniform regions in general, the issue of information loss emerges when applying normalization.

In contrast, the segmentation mask in the SPADE Generator is fed through spatially adaptive modulation *without* normalization. Only activations from the previous layer are normalized. Hence, the SPADE generator can better preserve semantic information. It enjoys the benefit of normalization without losing the semantic input information.

Multi-modal synthesis. By using a random vector as the input of the generator, our architecture provides a simple way for multi-modal synthesis [\[20,](#page-8-14) [60\]](#page-9-15). Namely, one can attach an encoder that processes a real image into a random vector, which will be then fed to the generator. The encoder and generator form a VAE [\[28\]](#page-8-12), in which the encoder tries to capture the style of the image, while the generator combines the encoded style and the segmentation mask information via the SPADEs to reconstruct the original image. The encoder also serves as a style guidance network at test time to capture the style of target images, as used in Figure [1.](#page-0-0) For training, we add a KL-Divergence loss term [\[28\]](#page-8-12).

## 4. Experiments

**Implementation details.** We apply the Spectral Norm [\[38\]](#page-9-22) to all the layers in both generator and discriminator. The learning rates for the generator and discriminator are 0.0001 and 0.0004, respectively [\[17\]](#page-8-29). We use the ADAM solver [\[27\]](#page-8-30) with  $\beta_1 = 0$  and  $\beta_2 = 0.999$ . All the experiments are conducted on an NVIDIA DGX1 with 8 32GB V100 GPUs. We use synchronized BatchNorm, i.e., these statistics are collected from all the GPUs.

Datasets. We conduct experiments on several datasets.

- *COCO-Stuff* [\[4\]](#page-8-8) is derived from the COCO dataset [\[32\]](#page-8-9). It has 118, 000 training images and 5, 000 validation images captured from diverse scenes. It has 182 semantic classes. Due to its vast diversity, existing image synthesis models perform poorly on this dataset.
- *ADE20K* [\[58\]](#page-9-6) consists of 20, 210 training and 2,000 validation images. Similarly to the COCO, the dataset contains challenging scenes with 150 semantic classes.
- *ADE20K-outdoor* is a subset of the ADE20K dataset that only contains outdoor scenes, used in Qi *et al*. [\[43\]](#page-9-23).
- *Cityscapes* dataset [\[9\]](#page-8-10) contains street scene images in German cities. The training and validation set sizes are 3, 000 and 500, respectively. Recent work has achieved photorealistic semantic image synthesis results [\[43,](#page-9-23) [47\]](#page-9-1) on the Cityscapes dataset.
- *Flickr Landscapes.* We collect 41, 000 photos from Flickr and use 1, 000 samples for the validation set. To avoid expensive manual annotation, we use a well-trained DeepLabV2 [\[5\]](#page-8-31) to compute input segmentation masks.

We train the competing semantic image synthesis methods on the same training set and report their results on the same validation set for each dataset.

Performance metrics. We adopt the evaluation protocol from previous work  $[6, 48]$  $[6, 48]$  $[6, 48]$ . Specifically, we run a semantic segmentation model on the synthesized images and compare how well the predicted segmentation mask matches the ground truth input. Intuitively, if the output images are realistic, a well-trained semantic segmentation model should be able to predict the ground truth label. For measuring the segmentation accuracy, we use both the mean Intersection-

<span id="page-4-3"></span><span id="page-4-1"></span>Image /page/4/Figure/0 description: This image displays a comparison of different image generation models. The top row shows a surfer on a wave, with the first column being a segmentation label, followed by the ground truth image, and then the outputs from CRN, pix2pixHD, and the 'Ours' model. The bottom row shows a baseball game, with a batter, catcher, and umpire, also presented with a segmentation label, ground truth, and outputs from CRN, pix2pixHD, and 'Ours'.

Figure 5: Visual comparison of semantic image synthesis results on the COCO-Stuff dataset. Our method successfully synthesizes realistic details from semantic labels.

<span id="page-4-2"></span>Image /page/4/Figure/2 description: This image is a comparison of different methods for synthesizing realistic details from semantic labels. It displays a grid of images, with the first column showing semantic labels and the subsequent columns showing the results from various methods: Ground Truth, CRN [6], SIMS [43], pix2pixHD [48], and Ours. The grid is organized into four rows, each presenting a different scene. The first row shows a street scene with buildings and cars. The second row depicts a poolside area with palm trees and a clear blue sky. The third and fourth rows both show street scenes from a first-person perspective, with cars, buildings, and trees lining the roads.

<span id="page-4-0"></span>Figure 6: Visual comparison of semantic image synthesis results on the ADE20K outdoor and Cityscapes datasets. Our method produces realistic images while respecting the spatial semantic layout at the same time.

| Method           | COCO-Stuff  |             |             | ADE20K      |             |             | ADE20K-outdoor |             |             | Cityscapes  |             |             |
|------------------|-------------|-------------|-------------|-------------|-------------|-------------|----------------|-------------|-------------|-------------|-------------|-------------|
|                  | mIoU        | accu        | FID         | mIoU        | accu        | FID         | mIoU           | accu        | FID         | mIoU        | accu        | FID         |
| <b>CRN [6]</b>   | 23.7        | 40.4        | 70.4        | 22.4        | 68.8        | 73.3        | 16.5           | 68.6        | 99.0        | 52.4        | 77.1        | 104.7       |
| <b>SIMS [43]</b> | N/A         | N/A         | N/A         | N/A         | N/A         | N/A         | 13.1           | 74.7        | 67.7        | 47.2        | 75.5        | 49.7        |
| pix2pixHD [48]   | 14.6        | 45.8        | 111.5       | 20.3        | 69.2        | 81.8        | 17.4           | 71.6        | 97.8        | 58.3        | 81.4        | 95.0        |
| <b>Ours</b>      | <b>37.4</b> | <b>67.9</b> | <b>22.6</b> | <b>38.5</b> | <b>79.9</b> | <b>33.9</b> | <b>30.8</b>    | <b>82.9</b> | <b>63.3</b> | <b>62.3</b> | <b>81.9</b> | <b>71.8</b> |

Table 1: Our method outperforms the current leading methods in semantic segmentation (mIoU and accu) and FID [\[17\]](#page-8-29) scores on all the benchmark datasets. For the mIoU and accu, higher is better. For the FID, lower is better.

over-Union (mIoU) and the pixel accuracy (accu). We use the state-of-the-art segmentation networks for each dataset: DeepLabV2 [\[5,](#page-8-31) [40\]](#page-9-24) for COCO-Stuff, UperNet101 [\[51\]](#page-9-25) for ADE20K, and DRN-D-105 [\[53\]](#page-9-26) for Cityscapes. In addition to the mIoU and the accu segmentation performance metrics, we use the Fréchet Inception Distance (FID) [[17\]](#page-8-29) to measure the distance between the distribution of synthesized results and the distribution of real images.

Baselines. We compare our method with 3 leading semantic image synthesis models: the pix2pixHD model [\[48\]](#page-9-2), the cascaded refinement network (CRN) [\[6\]](#page-8-6), and the semiparametric image synthesis method (SIMS) [\[43\]](#page-9-23). The pix2pixHD is the current state-of-the-art GAN-based conditional image synthesis framework. The CRN uses a deep network that repeatedly refines the output from low to high resolution, while the SIMS takes a semi-parametric approach that composites real segments from a training set and refines the boundaries. Both the CRN and SIMS are mainly trained using image reconstruction loss. For a fair comparison, we train the CRN and pix2pixHD models using the implementations provided by the authors. As image synthesis using the SIMS requires many queries to the training

<span id="page-5-0"></span>Image /page/5/Picture/0 description: The image is a grid of 12 landscape photographs. The top row shows a rocky outcrop with green grass and a cloudy sky, a mountain range with autumn trees and a body of water, a rocky riverbed with flowing water, and a serene lake reflecting trees and sky. The middle row features a lake with a reflection of a mountain range and trees, an island in a calm body of water with a reflection, a lone tree in a grassy field under a dramatic cloudy sky, and a solitary tree in a snowy landscape. The bottom row displays a rocky beach with waves, a cliffside overlooking the ocean under a cloudy sky, a lake reflecting mountains and trees, and a body of water with reflections of trees and mountains.

Figure 7: Semantic image synthesis results on the Flickr Landscapes dataset. The images were generated from semantic layout of photographs on the Flickr website.

dataset, it is computationally prohibitive for a large dataset such as the COCO-stuff and the full ADE20K. Therefore, we use the results provided by the authors when available.

Quantitative comparisons. As shown in Table [1,](#page-4-0) our method outperforms the current state-of-the-art methods by a large margin in all the datasets. For the COCO-Stuff, our method achieves an mIoU score of 35.2, which is about 1.5 times better than the previous leading method. Our FID is also 2.2 times better than the previous leading method. We note that the SIMS model produces a lower FID score but has poor segmentation performances on the Cityscapes dataset. This is because the SIMS synthesizes an image by first stitching image patches from the training dataset. As using the real image patches, the resulting image distribution can better match the distribution of real images. However, because there is no guarantee that a perfect query (e.g., a person in a particular pose) exists in the dataset, it tends to copy objects that do not match the input segments.

Qualitative results. In Figures [5](#page-4-1) and [6,](#page-4-2) we provide qualitative comparisons of the competing methods. We find that our method produces results with much better visual quality and fewer visible artifacts, especially for diverse scenes in the COCO-Stuff and ADE20K dataset. When the training dataset size is small, the SIMS model also renders images with good visual quality. However, the depicted content often deviates from the input segmentation mask (e.g., the shape of the swimming pool in the second row of Figure [6\)](#page-4-2).

<span id="page-5-1"></span>

| Dataset        | Ours vs. CRN | Ours vs. pix2pixHD | Ours vs. SIMS |
|----------------|--------------|--------------------|---------------|
| COCO-Stuff     | 79.76        | 86.64              | N/A           |
| ADE20K         | 76.66        | 83.74              | N/A           |
| ADE20K-outdoor | 66.04        | 79.34              | 85.70         |
| Cityscapes     | 63.60        | 53.64              | 51.52         |

Table 2: User preference study. The numbers indicate the percentage of users who favor the results of the proposed method over those of the competing method.

In Figures [7](#page-5-0) and [8,](#page-6-0) we show more example results from the Flickr Landscape and COCO-Stuff datasets. The proposed method can generate diverse scenes with high image fidelity. More results are included in the appendix.

Human evaluation. We use the Amazon Mechanical Turk (AMT) to compare the perceived visual fidelity of our method against existing approaches. Specifically, we give the AMT workers an input segmentation mask and two synthesis outputs from different methods and ask them to choose the output image that looks more like a corresponding image of the segmentation mask. The workers are given unlimited time to make the selection. For each comparison, we randomly generate 500 questions for each dataset, and each question is answered by 5 different workers. For quality control, only workers with a lifetime task approval rate greater than 98% can participate in our study.

Table [2](#page-5-1) shows the evaluation results. We find that users

<span id="page-6-3"></span><span id="page-6-0"></span>Image /page/6/Figure/0 description: This image is a grid of 12 smaller images, arranged in three rows and four columns. The top row shows outdoor activities: people flying kites in a grassy field, a kite in the sky, a surfer riding a wave, and a snowboarder on a snowy mountain. The middle row features animals: two sheep in a grassy field, a herd of elephants near rocks, two swans in water, a zebra in a grassy field, and sailboats on blue water. The bottom row displays indoor scenes and food: a living room with furniture, a bathroom with a sink and toilet, a table with pizzas and drinks, a plate of food with broccoli and bread, and a train at a station.

Figure 8: Semantic image synthesis results on COCO-Stuff. Our method successfully generates realistic images in diverse scenes ranging from animals to sports activities.

<span id="page-6-1"></span>

| Method                   | #param | COCO.       | ADE.        | City.       | Method         | COCO | ADE20K | Cityscapes |
|--------------------------|--------|-------------|-------------|-------------|----------------|------|--------|------------|
| decoder w/ SPADE (Ours)  | 96M    | <b>35.2</b> | 38.5        | 62.3        | segmap input   | 35.2 | 38.5   | 62.3       |
| compact decoder w/ SPADE | 61M    | <b>35.2</b> | 38.0        | <b>62.5</b> | random input   | 35.3 | 38.3   | 61.6       |
| decoder w/ Concat        | 79M    | 31.9        | 33.6        | 61.1        | kernelsize 5x5 | 35.0 | 39.3   | 61.8       |
| pix2pixHD++ w/ SPADE     | 237M   | 34.4        | <b>39.0</b> | 62.2        | kernelsize 3x3 | 35.2 | 38.5   | 62.3       |
| pix2pixHD++ w/ Concat    | 195M   | 32.9        | 38.9        | 57.1        | kernelsize 1x1 | 32.7 | 35.9   | 59.9       |
| pix2pixHD++              | 183M   | 32.7        | 38.3        | 58.8        | #params 141M   | 35.3 | 38.3   | 62.5       |
| compact pix2pixHD++      | 103M   | 31.6        | 37.3        | 57.6        | #params 96M    | 35.2 | 38.5   | 62.3       |
| pix2pixHD[48]            | 183M   | 14.6        | 20.3        | 58.3        | #params 61M    | 35.2 | 38.0   | 62.5       |
| <b>Sync BatchNorm</b>    | 35.0   | 39.3        | 61.8        |             |                |      |        |            |
| <b>BatchNorm</b>         | 33.7   | 37.9        | 61.8        |             |                |      |        |            |
| <b>InstanceNorm</b>      | 33.9   | 37.4        | 58.7        |             |                |      |        |            |

Table 3: The mIoU scores are boosted when the SPADE is used, for both the decoder architecture (Figure [4\)](#page-3-0) and encoder-decoder architecture of pix2pixHD++ (our improved baseline over pix2pixHD [\[48\]](#page-9-2)). On the other hand, simply concatenating semantic input at every layer fails to do so. Moreover, our compact model with smaller depth at all layers outperforms all the baselines.

strongly favor our results on all the datasets, especially on the challenging COCO-Stuff and ADE20K datasets. For the Cityscapes, even when all the competing methods achieve high image fidelity, users still prefer our results.

Effectiveness of the SPADE. For quantifying importance of the SPADE, we introduce a strong baseline called pix2pixHD++, which combines all the techniques we find useful for enhancing the performance of pix2pixHD except the SPADE. We also train models that receive the segmentation mask input at all the intermediate layers via feature concatenation in the channel direction, which is termed as pix2pixHD++ w/ Concat. Finally, the model that com-

<span id="page-6-2"></span>

Table 4: The SPADE generator works with different configurations. We change the input of the generator, the convolutional kernel size acting on the segmentation map, the capacity of the network, and the parameter-free normalization method. The settings used in the paper are boldfaced.

bines the strong baseline with the SPADE is denoted as pix2pixHD++ w/ SPADE.

As shown in Table [3,](#page-6-1) the architectures with the proposed SPADE consistently outperforms its counterparts, in both the decoder-style architecture described in Figure [4](#page-3-0) and more traditional encoder-decoder architecture used in the pix2pixHD. We also find that concatenating segmentation masks at all intermediate layers, a reasonable alternative to the SPADE, does not achieve the same performance as SPADE. Furthermore, the decoder-style SPADE generator works better than the strong baselines even with a smaller number of parameters.

<span id="page-7-0"></span>Image /page/7/Picture/0 description: This image displays a grid of landscape images, likely generated by an AI model. The grid is organized into four rows and five columns. The first column contains segmentation masks with small inset images, showing the original scene that was segmented. The remaining columns showcase variations of synthesized landscape images, possibly exploring different styles, lighting conditions, or artistic interpretations. The landscapes depicted include snowy mountains, trees against dramatic skies, country roads, and beaches. The overall presentation suggests a demonstration of image synthesis capabilities, where different visual outputs are generated based on input segmentation or prompts.

Figure 9: Our model attains multimodal synthesis capability when trained with the image encoder. During deployment, by using different random noise, our model synthesizes outputs with diverse appearances but all having the same semantic layouts depicted in the input mask. For reference, the ground truth image is shown inside the input segmentation mask.

Variations of SPADE generator. Table [4](#page-6-2) reports the performance of several variations of our generator. First, we compare two types of input to the generator where one is the random noise while the other is the downsampled segmentation map. We find that both of the variants render similar performance and conclude that the modulation by SPADE alone provides sufficient signal about the input mask. Second, we vary the type of parameter-free normalization layers before applying the modulation parameters. We observe that the SPADE works reliably across different normalization methods. Next, we vary the convolutional kernel size acting on the label map, and find that kernel size of 1x1 hurts performance, likely because it prohibits utilizing the context of the label. Lastly, we modify the capacity of the generator by changing the number of convolutional filters. We present more variations and ablations in the appendix.

Multi-modal synthesis. In Figure [9,](#page-7-0) we show the multimodal image synthesis results on the Flickr Landscape dataset. For the same input segmentation mask, we sample different noise inputs to achieve different outputs. More results are included in the appendix.

Semantic manipulation and guided image synthesis. In Figure [1,](#page-0-0) we show an application where a user draws different segmentation masks, and our model renders the corresponding landscape images. Moreover, our model allows users to choose an external style image to control the global appearances of the output image. We achieve it by replacing the input noise with the embedding vector of the style image computed by the image encoder.

## 5. Conclusion

We have proposed the spatially-adaptive normalization, which utilizes the input semantic layout while performing the affine transformation in the normalization layers. The proposed normalization leads to the first semantic image synthesis model that can produce photorealistic outputs for diverse scenes including indoor, outdoor, landscape, and street scenes. We further demonstrate its application for multi-modal synthesis and guided image synthesis.

Acknowledgments. We thank Alexei A. Efros, Bryan Catanzaro, Andrew Tao, and Jan Kautz for insightful advice. We thank Chris Hebert, Gavriil Klimov, and Brad Nemire for their help in constructing the demo apps. Taesung Park contributed to the work during his internship at NVIDIA. His Ph.D. is supported by a Samsung Scholarship.

## References

- <span id="page-8-28"></span>[1] M. Arjovsky, S. Chintala, and L. Bottou. Wasserstein generative adversarial networks. In *International Conference on Machine Learning (ICML)*, 2017. [3](#page-2-3)
- <span id="page-8-20"></span>[2] J. L. Ba, J. R. Kiros, and G. E. Hinton. Layer normalization. *arXiv preprint arXiv:1607.06450*, 2016. [2](#page-1-1)
- <span id="page-8-5"></span>[3] A. Brock, J. Donahue, and K. Simonyan. Large scale gan training for high fidelity natural image synthesis. In *International Conference on Learning Representations (ICLR)*, 2019. [1,](#page-0-1) [2](#page-1-1)
- <span id="page-8-8"></span>[4] H. Caesar, J. Uijlings, and V. Ferrari. Coco-stuff: Thing and stuff classes in context. In *IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2018. [2,](#page-1-1) [4](#page-3-1)
- <span id="page-8-31"></span>[5] L.-C. Chen, G. Papandreou, I. Kokkinos, K. Murphy, and A. L. Yuille. Deeplab: Semantic image segmentation with deep convolutional nets, atrous convolution, and fully connected crfs. *IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)*, 40(4):834–848, 2018. [4,](#page-3-1) [5](#page-4-3)
- <span id="page-8-6"></span>[6] Q. Chen and V. Koltun. Photographic image synthesis with cascaded refinement networks. In *IEEE International Conference on Computer Vision (ICCV)*, 2017. [1,](#page-0-1) [4,](#page-3-1) [5,](#page-4-3) [13,](#page-12-0) [14,](#page-13-0) [15,](#page-14-0) [16,](#page-15-0) [17,](#page-16-0) [18](#page-17-0)
- <span id="page-8-1"></span>[7] T. Chen, M.-M. Cheng, P. Tan, A. Shamir, and S.-M. Hu. Sketch2photo: internet image montage. *ACM Transactions on Graphics (TOG)*, 28(5):124, 2009. [1,](#page-0-1) [2](#page-1-1)
- <span id="page-8-23"></span>[8] T. Chen, M. Lucic, N. Houlsby, and S. Gelly. On self modulation for generative adversarial networks. In *International Conference on Learning Representations*, 2019. [2](#page-1-1)
- <span id="page-8-10"></span>[9] M. Cordts, M. Omran, S. Ramos, T. Rehfeld, M. Enzweiler, R. Benenson, U. Franke, S. Roth, and B. Schiele. The cityscapes dataset for semantic urban scene understanding. In *IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2016. [2,](#page-1-1) [4](#page-3-1)
- <span id="page-8-24"></span>[10] H. De Vries, F. Strub, J. Mary, H. Larochelle, O. Pietquin, and A. C. Courville. Modulating early visual processing by language. In *Advances in Neural Information Processing Systems*, 2017. [2](#page-1-1)
- <span id="page-8-21"></span>[11] V. Dumoulin, J. Shlens, and M. Kudlur. A learned representation for artistic style. In *International Conference on Learning Representations (ICLR)*, 2016. [2,](#page-1-1) [3](#page-2-3)
- <span id="page-8-32"></span>[12] X. Glorot and Y. Bengio. Understanding the difficulty of training deep feedforward neural networks. In *Proceedings of the thirteenth international conference on artificial intelligence and statistics*, pages 249–256, 2010. [12,](#page-11-0) [13](#page-12-0)
- <span id="page-8-11"></span>[13] I. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. Warde-Farley, S. Ozair, A. Courville, and Y. Bengio. Generative adversarial nets. In *Advances in Neural Information Processing Systems*, 2014. [2](#page-1-1)
- <span id="page-8-2"></span>[14] J. Hays and A. A. Efros. Scene completion using millions of photographs. In *ACM SIGGRAPH*, 2007. [1](#page-0-1)
- <span id="page-8-26"></span>[15] K. He, X. Zhang, S. Ren, and J. Sun. Deep residual learning for image recognition. In *IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2016. [3](#page-2-3)
- <span id="page-8-0"></span>[16] A. Hertzmann, C. E. Jacobs, N. Oliver, B. Curless, and D. H. Salesin. Image analogies. 2001. [1,](#page-0-1) [2](#page-1-1)
- <span id="page-8-29"></span>[17] M. Heusel, H. Ramsauer, T. Unterthiner, B. Nessler, and S. Hochreiter. GANs trained by a two time-scale update rule

converge to a local Nash equilibrium. In *Advances in Neural Information Processing Systems*, 2017. [4,](#page-3-1) [5,](#page-4-3) [13](#page-12-0)

- <span id="page-8-13"></span>[18] S. Hong, D. Yang, J. Choi, and H. Lee. Inferring semantic layout for hierarchical text-to-image synthesis. In *IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2018. [2](#page-1-1)
- <span id="page-8-22"></span>[19] X. Huang and S. Belongie. Arbitrary style transfer in realtime with adaptive instance normalization. In *IEEE International Conference on Computer Vision (ICCV)*, 2017. [2,](#page-1-1) [3](#page-2-3)
- <span id="page-8-14"></span>[20] X. Huang, M.-Y. Liu, S. Belongie, and J. Kautz. Multimodal unsupervised image-to-image translation. *European Conference on Computer Vision (ECCV)*, 2018. [2,](#page-1-1) [3,](#page-2-3) [4](#page-3-1)
- <span id="page-8-19"></span>[21] S. Ioffe and C. Szegedy. Batch normalization: Accelerating deep network training by reducing internal covariate shift. In *International Conference on Machine Learning (ICML)*, 2015. [2,](#page-1-1) [3](#page-2-3)
- <span id="page-8-7"></span>[22] P. Isola, J.-Y. Zhu, T. Zhou, and A. A. Efros. Image-toimage translation with conditional adversarial networks. In *IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2017. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-3) [11,](#page-10-0) [12](#page-11-0)
- <span id="page-8-3"></span>[23] M. Johnson, G. J. Brostow, J. Shotton, O. Arandjelovic, V. Kwatra, and R. Cipolla. Semantic photo synthesis. In *Computer Graphics Forum*, volume 25, pages 407–413, 2006. [1,](#page-0-1) [2](#page-1-1)
- <span id="page-8-15"></span>[24] L. Karacan, Z. Akata, A. Erdem, and E. Erdem. Learning to generate images of outdoor scenes from attributes and semantic layouts. *arXiv preprint arXiv:1612.00215*, 2016. [2](#page-1-1)
- <span id="page-8-16"></span>[25] L. Karacan, Z. Akata, A. Erdem, and E. Erdem. Manipulating attributes of natural scenes via hallucination. *arXiv preprint arXiv:1808.07413*, 2018. [2](#page-1-1)
- <span id="page-8-25"></span>[26] T. Karras, S. Laine, and T. Aila. A style-based generator architecture for generative adversarial networks. In *IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2019. [2](#page-1-1)
- <span id="page-8-30"></span>[27] D. P. Kingma and J. Ba. Adam: A method for stochastic optimization. In *International Conference on Learning Representations (ICLR)*, 2015. [4](#page-3-1)
- <span id="page-8-12"></span>[28] D. P. Kingma and M. Welling. Auto-encoding variational bayes. In *International Conference on Learning Representations (ICLR)*, 2014. [2,](#page-1-1) [4,](#page-3-1) [11,](#page-10-0) [12](#page-11-0)
- <span id="page-8-18"></span>[29] A. Krizhevsky, I. Sutskever, and G. E. Hinton. Imagenet classification with deep convolutional neural networks. In *Advances in Neural Information Processing Systems*, 2012.  $\overline{2}$  $\overline{2}$  $\overline{2}$
- <span id="page-8-4"></span>[30] J.-F. Lalonde, D. Hoiem, A. A. Efros, C. Rother, J. Winn, and A. Criminisi. Photo clip art. In *ACM transactions on graphics (TOG)*, volume 26, page 3. ACM, 2007. [1](#page-0-1)
- <span id="page-8-27"></span>[31] J. H. Lim and J. C. Ye. Geometric gan. *arXiv preprint arXiv:1705.02894*, 2017. [3,](#page-2-3) [11](#page-10-0)
- <span id="page-8-9"></span>[32] T.-Y. Lin, M. Maire, S. Belongie, J. Hays, P. Perona, D. Ramanan, P. Dollár, and C. L. Zitnick. Microsoft coco: Common objects in context. In *European Conference on Computer Vision (ECCV)*, 2014. [2,](#page-1-1) [4](#page-3-1)
- <span id="page-8-17"></span>[33] M.-Y. Liu, T. Breuel, and J. Kautz. Unsupervised image-toimage translation networks. In *Advances in Neural Information Processing Systems*, 2017. [2](#page-1-1)

- <span id="page-9-21"></span>[34] X. Mao, O. Li, H. Xie, Y. R. Lau, Z. Wang, and S. P. Smolley. Least squares generative adversarial networks. In *IEEE International Conference on Computer Vision (ICCV)*, 2017. [3,](#page-2-3) [11](#page-10-0)
- <span id="page-9-0"></span>[35] T. B. Mathias Eitz, Kristian Hildebrand and M. Alexa. Photosketch: A sketch based image query and compositing system. In *ACM SIGGRAPH 2009 Talk Program*, 2009. [1](#page-0-1)
- <span id="page-9-7"></span>[36] L. Mescheder, A. Geiger, and S. Nowozin. Which training methods for gans do actually converge? In *International Conference on Machine Learning (ICML)*, 2018. [2,](#page-1-1) [3,](#page-2-3) [11](#page-10-0)
- <span id="page-9-8"></span>[37] M. Mirza and S. Osindero. Conditional generative adversarial nets. *arXiv preprint arXiv:1411.1784*, 2014. [2](#page-1-1)
- <span id="page-9-22"></span>[38] T. Miyato, T. Kataoka, M. Koyama, and Y. Yoshida. Spectral normalization for generative adversarial networks. In *International Conference on Learning Representations (ICLR)*, 2018. [3,](#page-2-3) [4,](#page-3-1) [11](#page-10-0)
- <span id="page-9-9"></span>[39] T. Miyato and M. Koyama. cGANs with projection discriminator. In *International Conference on Learning Representations (ICLR)*, 2018. [2,](#page-1-1) [3,](#page-2-3) [11](#page-10-0)
- <span id="page-9-24"></span>[40] K. Nakashima. Deeplab-pytorch. [https://github.](https://github.com/kazuto1011/deeplab-pytorch) [com/kazuto1011/deeplab-pytorch](https://github.com/kazuto1011/deeplab-pytorch), 2018. [5](#page-4-3)
- <span id="page-9-10"></span>[41] A. Odena, C. Olah, and J. Shlens. Conditional image synthesis with auxiliary classifier GANs. In *International Conference on Machine Learning (ICML)*, 2017. [2](#page-1-1)
- <span id="page-9-19"></span>[42] E. Perez, H. De Vries, F. Strub, V. Dumoulin, and A. Courville. Learning visual reasoning without strong priors. In *International Conference on Machine Learning (ICML)*, 2017. [2](#page-1-1)
- <span id="page-9-23"></span>[43] X. Qi, Q. Chen, J. Jia, and V. Koltun. Semi-parametric image synthesis. In *IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2018. [4,](#page-3-1) [5,](#page-4-3) [13,](#page-12-0) [17,](#page-16-0) [18](#page-17-0)
- <span id="page-9-11"></span>[44] S. Reed, Z. Akata, X. Yan, L. Logeswaran, B. Schiele, and H. Lee. Generative adversarial text to image synthesis. In *International Conference on Machine Learning (ICML)*, 2016. [2](#page-1-1)
- <span id="page-9-18"></span>[45] T. Salimans and D. P. Kingma. Weight normalization: A simple reparameterization to accelerate training of deep neural networks. In *Advances in Neural Information Processing Systems*, 2016. [2](#page-1-1)
- <span id="page-9-16"></span>[46] D. Ulyanov, A. Vedaldi, and V. Lempitsky. Instance normalization: The missing ingredient for fast stylization. arxiv 2016. *arXiv preprint arXiv:1607.08022*, 2016. [2,](#page-1-1) [3](#page-2-3)
- <span id="page-9-1"></span>[47] T.-C. Wang, M.-Y. Liu, J.-Y. Zhu, G. Liu, A. Tao, J. Kautz, and B. Catanzaro. Video-to-video synthesis. In *Advances in Neural Information Processing Systems*, 2018. [1,](#page-0-1) [4](#page-3-1)
- <span id="page-9-2"></span>[48] T.-C. Wang, M.-Y. Liu, J.-Y. Zhu, A. Tao, J. Kautz, and B. Catanzaro. High-resolution image synthesis and semantic manipulation with conditional gans. In *IEEE Conference on*

*Computer Vision and Pattern Recognition (CVPR)*, 2018. [1,](#page-0-1) [3,](#page-2-3) [4,](#page-3-1) [5,](#page-4-3) [7,](#page-6-3) [11,](#page-10-0) [12,](#page-11-0) [13,](#page-12-0) [14,](#page-13-0) [15,](#page-14-0) [16,](#page-15-0) [17,](#page-16-0) [18](#page-17-0)

- <span id="page-9-20"></span>[49] X. Wang, K. Yu, C. Dong, and C. Change Loy. Recovering realistic texture in image super-resolution by deep spatial feature transform. In *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition*, pages 606–615, 2018. [2](#page-1-1)
- <span id="page-9-17"></span>[50] Y. Wu and K. He. Group normalization. In *European Conference on Computer Vision (ECCV)*, 2018. [2](#page-1-1)
- <span id="page-9-25"></span>[51] T. Xiao, Y. Liu, B. Zhou, Y. Jiang, and J. Sun. Unified perceptual parsing for scene understanding. In *European Conference on Computer Vision (ECCV)*, 2018. [5](#page-4-3)
- <span id="page-9-12"></span>[52] T. Xu, P. Zhang, Q. Huang, H. Zhang, Z. Gan, X. Huang, and X. He. Attngan: Fine-grained text to image generation with attentional generative adversarial networks. In *IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2018. [2](#page-1-1)
- <span id="page-9-26"></span>[53] F. Yu, V. Koltun, and T. Funkhouser. Dilated residual networks. In *IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2017. [5](#page-4-3)
- <span id="page-9-3"></span>[54] H. Zhang, I. Goodfellow, D. Metaxas, and A. Odena. Selfattention generative adversarial networks. In *International Conference on Machine Learning (ICML)*, 2019. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-3) [11](#page-10-0)
- <span id="page-9-4"></span>[55] H. Zhang, T. Xu, H. Li, S. Zhang, X. Huang, X. Wang, and D. Metaxas. Stackgan: Text to photo-realistic image synthesis with stacked generative adversarial networks. In *IEEE International Conference on Computer Vision (ICCV)*, 2017. [1,](#page-0-1) [2](#page-1-1)
- <span id="page-9-5"></span>[56] H. Zhang, T. Xu, H. Li, S. Zhang, X. Wang, X. Huang, and D. Metaxas. Stackgan++: Realistic image synthesis with stacked generative adversarial networks. *IEEE Transactions on Pattern Analysis and Machine Intelligence (TPAMI)*, 2018. [1](#page-0-1)
- <span id="page-9-13"></span>[57] B. Zhao, L. Meng, W. Yin, and L. Sigal. Image generation from layout. In *IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2019. [2](#page-1-1)
- <span id="page-9-6"></span>[58] B. Zhou, H. Zhao, X. Puig, S. Fidler, A. Barriuso, and A. Torralba. Scene parsing through ade20k dataset. In *IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, 2017. [2,](#page-1-1) [4](#page-3-1)
- <span id="page-9-14"></span>[59] J.-Y. Zhu, T. Park, P. Isola, and A. A. Efros. Unpaired imageto-image translation using cycle-consistent adversarial networks. In *IEEE International Conference on Computer Vision (ICCV)*, 2017. [2](#page-1-1)
- <span id="page-9-15"></span>[60] J.-Y. Zhu, R. Zhang, D. Pathak, T. Darrell, A. A. Efros, O. Wang, and E. Shechtman. Toward multimodal image-toimage translation. In *Advances in Neural Information Processing Systems*, 2017. [2,](#page-1-1) [3,](#page-2-3) [4](#page-3-1)

## <span id="page-10-0"></span>A. Additional Implementation Details

Generator. The architecture of the generator consists of a series of the proposed SPADE ResBlks with nearest neighbor upsampling. We train our network using 8 GPUs simultaneously and use the synchronized version of the Batch-Norm. We apply the Spectral Norm [\[38\]](#page-9-22) to all the convolutional layers in the generator. The architectures of the proposed SPADE and SPADE ResBlk are given in Figure [10](#page-10-1) and Figure [11,](#page-10-1) respectively. The architecture of the generator is shown in Figure [12.](#page-10-2)

Discriminator. The architecture of the discriminator follows the one used in the pix2pixHD method [\[48\]](#page-9-2), which uses a multi-scale design with the InstanceNorm (IN). The only difference is that we apply the Spectral Norm to all the

<span id="page-10-1"></span>Image /page/10/Figure/3 description: This is a diagram of the SPADE module. The module takes an input from a Sync Batch Norm layer and an input from a Resize layer with order 0. The Resize layer output is fed into a 3x3-Conv-128, ReLU layer. The output of this layer is then fed into two 3x3-Conv-k layers. One of these layers is multiplied with the output of the Sync Batch Norm layer. The other 3x3-Conv-k layer is added to the result of the multiplication. The final output of the SPADE module is then passed on.

Figure 10: SPADE Design. The term 3x3-Conv-k denotes a 3-by-3 convolutional layer with  $k$  convolutional filters. The segmentation map is resized to match the resolution of the corresponding feature map using nearest-neighbor downsampling.

Image /page/10/Figure/5 description: The image displays a block diagram of a SPADE ResBlk(k) module. The module takes an input from the top and two conditional inputs from the left. Inside the main block, there are sequential operations: SPADE, ReLU, 3x3-Conv-k, SPADE, ReLU, and 3x3-Conv-k. A residual connection is shown, where the output of the first 3x3-Conv-k is added to the output of a sub-block. This sub-block, enclosed in a dashed rectangle, consists of SPADE, ReLU, and 3x3-Conv-k. The final output is obtained after an addition operation (indicated by a circle with a plus sign) and an arrow pointing downwards. The conditional inputs on the left are visually represented as segmented images, with distinct regions colored blue, dark gray, light gray, and green, and a yellow element on top.

Figure 11: SPADE ResBlk. The residual block design largely follows that in Mescheder *et al*. [\[36\]](#page-9-7) and Miyato *et al*. [\[39\]](#page-9-9). We note that for the case that the number of channels before and after the residual block is different, the skip connection is also learned (dashed box in the figure). convolutional layers of the discriminator. The details of the discriminator architecture is shown in Figure [13.](#page-11-1)

<span id="page-10-2"></span>Image /page/10/Figure/7 description: The image displays a neural network architecture diagram for a SPADE Generator. The diagram starts with an input image on the left and a 3D Gaussian distribution plot at the top. The network processes the input through a Linear layer (256, 16384), followed by a Reshape layer (1024, 4, 4). It then proceeds through a series of SPADE Residual Blocks with upsampling operations: SPADE ResBlk(1024) Upsample(2) repeated three times, followed by SPADE ResBlk(512) Upsample(2), SPADE ResBlk(256) Upsample(2), SPADE ResBlk(128) Upsample(2), and finally SPADE ResBlk(64) Upsample(2). The output layer consists of a 3x3 Convolution with 3 channels and a Tanh activation function.

Figure 12: SPADE Generator. Different from prior image generators [\[22,](#page-8-7) [48\]](#page-9-2), the semantic segmentation mask is passed to the generator through the proposed SPADE Res-Blks in Figure [11.](#page-10-1)

Image Encoder. The image encoder consists of 6 stride-2 convolutional layers followed by two linear layers to produce the mean and variance of the output distribution as shown in Figure [14.](#page-11-2)

Learning objective. We use the learning objective function in the pix2pixHD work  $[48]$  except that we replace its LS-GAN loss [\[34\]](#page-9-21) term with the Hinge loss term [\[31,](#page-8-27) [38,](#page-9-22) [54\]](#page-9-3). We use the same weighting among the loss terms in the objective function as that in the pix2pixHD work.

When training the proposed framework with the image encoder for multi-modal synthesis and style-guided image synthesis, we include a KL Divergence loss:

$$
\mathcal{L}_{\text{KLD}} = \mathcal{D}_{\text{KL}}(q(\mathbf{z}|\mathbf{x})||p(\mathbf{z}))
$$

where the prior distribution  $p(z)$  is a standard Gaussian distribution and the variational distribution  $q$  is fully determined by a mean vector and a variance vector [\[28\]](#page-8-12). We use the reparamterization trick [\[28\]](#page-8-12) for back-propagating the gradient from the generator to the image encoder. The weight for the KL Divergence loss is 0.05.

In Figure [15,](#page-11-3) we overview the training data flow. The image encoder encodes a real image to a mean vector and a variance vector. They are used to compute the noise input to the generator via the reparameterization trick [\[28\]](#page-8-12). The generator also takes the segmentation mask of the input image as input with the proposed SPADE ResBlks. The

<span id="page-11-1"></span><span id="page-11-0"></span>

| Image: Left input image    | Image: Right input image |
|----------------------------|--------------------------|
| Concat                     |                          |
| 4x4-↓2-Conv-64, LReLU      |                          |
| 4x4-↓2-Conv-128, IN, LReLU |                          |
| 4x4-↓2-Conv-256, IN, LReLU |                          |
| 4x4-Conv-512, IN, LReLU    |                          |
| 4x4-Conv-1                 |                          |

Figure 13: Our discriminator design largely follows that in the pix2pixHD [\[48\]](#page-9-2). It takes the concatenation the segmentation map and the image as input. It is based on the Patch-GAN [\[22\]](#page-8-7). Hence, the last layer of the discriminator is a convolutional layer.

<span id="page-11-2"></span>Image /page/11/Figure/2 description: The image displays a neural network architecture diagram. It starts with an input image of a sunset landscape with a silhouetted tree. The architecture consists of a series of convolutional layers with downsampling (indicated by ↓2), followed by Instance Normalization (IN) and Leaky Rectified Linear Unit (LReLU) activation functions. Specifically, there are six such layers: 3x3-↓2-Conv-64, 3x3-↓2-Conv-128, 3x3-↓2-Conv-256, 3x3-↓2-Conv-512, 3x3-↓2-Conv-512, and 3x3-↓2-Conv-512. After these convolutional layers, there is a Reshape layer with dimensions (8192, 1, 1). Finally, the architecture branches into two parallel Linear layers, each with 256 units. One linear layer outputs a value labeled 'μ' (mu), and the other outputs a value labeled 'σ²' (sigma squared).

Figure 14: The image encoder consists a series of convolutional layers with stride 2 followed by two linear layers that output a mean vector  $\mu$  and a variance vector  $\sigma$ .

discriminator takes concatenation of the segmentation mask

and the output image from the generator as input and aims to classify that as fake.

Training details. We perform 200 epochs of training on the Cityscapes and ADE20K datasets, 100 epochs of training on the COCO-Stuff dataset, and 50 epochs of training on the Flickr Landscapes dataset. The image sizes are  $256 \times 256$ , except the Cityscapes at  $512 \times 256$ . We linearly decay the learning rate to 0 from epoch 100 to 200 for the Cityscapes and ADE20K datasets. The batch size is 32. We initialize the network weights using thes Glorot initialization [\[12\]](#page-8-32).

<span id="page-11-3"></span>Image /page/11/Figure/7 description: This is a diagram illustrating a generative adversarial network (GAN) architecture. The diagram shows a sequence of components: an Image Encoder, a Generator, a Concat layer, and a Discriminator. An input image of a snowy landscape with a tree is fed into the Image Encoder. The output of the Image Encoder is then fed into the Generator. Additionally, another input image, depicting a stylized landscape with a tree, is fed into the Generator via multiple arrows. A 3D plot of a Gaussian distribution is also shown next to the Generator, suggesting its use as input. The Generator's output is passed to the Concat layer, which also receives input from the stylized landscape image. Finally, the output of the Concat layer is fed into the Discriminator. The overall flow suggests a process where an image is encoded, then used by a generator to create new data, possibly conditioned on other inputs, and finally evaluated by a discriminator.

Figure 15: The image encoder encodes a real image to a latent representation for generating a mean vector and a variance vector. They are used to compute the noise input to the generator via the reparameterization trick [\[28\]](#page-8-12). The generator also takes the segmentation mask of the input image as input via the proposed SPADE ResBlks. The discriminator takes concatenation of the segmentation mask and the output image from the generator as input and aims to classify that as fake.

## <span id="page-12-0"></span>B. Additional Ablation Study

<span id="page-12-1"></span>

| Method                                                   | COCO. | ADE. | City. |
|----------------------------------------------------------|-------|------|-------|
| Ours                                                     | 35.2  | 38.5 | 62.3  |
| Ours w/o Perceptual loss                                 | 24.7  | 30.1 | 57.4  |
| Ours w/o GAN feature matching loss                       | 33.2  | 38.0 | 62.2  |
| Ours w/ a deeper discriminator                           | 34.9  | 38.3 | 60.9  |
| pix2pixHD++ w/ SPADE                                     | 34.4  | 39.0 | 62.2  |
| pix2pixHD++                                              | 32.7  | 38.3 | 58.8  |
| pix2pixHD++ w/o Sync BatchNorm                           | 27.4  | 31.8 | 51.1  |
| pix2pixHD++ w/o Sync BatchNorm,<br>and w/o Spectral Norm | 26.0  | 31.9 | 52.3  |
| pix2pixHD [48]                                           | 14.6  | 20.3 | 58.3  |

Table 5: Additional ablation study results using the mIoU metric: the table shows that both the perceptual loss and GAN feature matching loss terms are important. Making the discriminator deeper does not lead to a performance boost. The table also shows that all the components (Synchronized BatchNorm, Spectral Norm, TTUR, the Hinge loss objective, and the SPADE) used in the proposed method helps our strong baseline, pix2pixHD++.

Table [5](#page-12-1) provides additional ablation study results analyzing the contribution of individual components in the proposed method. We first find that both of the perceptual loss and GAN feature matching loss inherited from the learning objective function of the pix2pixHD [\[48\]](#page-9-2) are important. Removing any of them leads to a performance drop. We also find that increasing the depth of the discriminator by inserting one more convolutional layer to the top of the pix2pixHD discriminator does not improve the results.

In Table [5,](#page-12-1) we also analyze the effectiveness of each component used in our strong baseline, the pix2pixHD++ method, derived from the pix2pixHD method. We found that the Spectral Norm, synchronized BatchNorm, TTUR [\[17\]](#page-8-29), and the hinge loss objective all contribute to the performance boost. Adding the SPADE to the strong baseline further improves the performance. Note that the pix2pixHD++ w/o Sync BatchNorm and w/o Spectral Norm still differs from the pix2pixHD in that it uses the hinge loss objective, TTUR, a large batch size, and the Glorot initialization [\[12\]](#page-8-32).

## C. Additional Results

In Figure [16,](#page-13-1) [17,](#page-14-1) and [18,](#page-15-1) we show additional synthesis results from the proposed method on the COCO-Stuff and ADE20K datasets with comparisons to those from the CRN  $[6]$  and pix2pixHD  $[48]$  methods.

In Figure [19](#page-16-1) and [20,](#page-17-1) we show additional synthesis results from the proposed method on the ADE20K-outdoor and Cityscapes datasets with comparison to those from the CRN  $[6]$ , SIMS  $[43]$ , and pix2pixHD  $[48]$  methods.

In Figure [21,](#page-18-0) we show additional multi-modal synthesis results from the proposed method. As sampling different z from a standard multivariate Gaussian distribution, we synthesize images of diverse appearances.

In the accompanying [video,](https://github.com/NVlabs/SPADE) we demonstrate our semantic image synthesis interface. We show how a user can create photorealistic landscape images by painting semantic labels on a canvas. We also show how a user can synthesize images of diverse appearances for the same semantic segmentation mask as well as transfer the appearance of a provided style image to the synthesized one.

<span id="page-13-1"></span><span id="page-13-0"></span>

| Label                                   | Ground Truth               | CRN                        | pix2pixHD                  | Ours                       |
|-----------------------------------------|----------------------------|----------------------------|----------------------------|----------------------------|
| Image: Giraffe segmentation             | Image: Giraffe             | Image: Giraffe             | Image: Giraffe             | Image: Giraffe             |
| Image: People flying kites segmentation | Image: People flying kites | Image: People flying kites | Image: People flying kites | Image: People flying kites |
| Image: Kite segmentation                | Image: Kite                | Image: Kite                | Image: Kite                | Image: Kite                |
| Image: Surfer segmentation              | Image: Surfer              | Image: Surfer              | Image: Surfer              | Image: Surfer              |
| Image: Skiers segmentation              | Image: Skiers              | Image: Skiers              | Image: Skiers              | Image: Skiers              |
| Image: Skier segmentation               | Image: Skier               | Image: Skier               | Image: Skier               | Image: Skier               |
| Image: Swans segmentation               | Image: Swans               | Image: Swans               | Image: Swans               | Image: Swans               |
| Image: Swans segmentation               | Image: Swans               | Image: Swans               | Image: Swans               | Image: Swans               |
| Image: Elephants segmentation           | Image: Elephants           | Image: Elephants           | Image: Elephants           | Image: Elephants           |
| Image: Elephants segmentation           | Image: Elephants           | Image: Elephants           | Image: Elephants           | Image: Elephants           |

Figure 16: Additional results with comparison to those from the CRN [\[6\]](#page-8-6) and pix2pixHD [\[48\]](#page-9-2) methods on the COCO-Stuff dataset.

<span id="page-14-1"></span><span id="page-14-0"></span>

| Label                                        | Ground Truth                                            | CRN                                                      | pix2pixHD                                                              | Ours                                                    |
|----------------------------------------------|---------------------------------------------------------|----------------------------------------------------------|------------------------------------------------------------------------|---------------------------------------------------------|
| Image: Segmentation map of a zebra           | Image: Zebra standing in a field                        | Image: Zebra in a grassy field                           | Image: Zebra in a grassy field with a hazy effect                      | Image: Zebra standing in a field                        |
| Image: Segmentation map of a harbor scene    | Image: Boats in a harbor with calm water and a blue sky | Image: Boats in a harbor with calm water and a hazy sky  | Image: Boats in a harbor with calm water and a hazy sky                | Image: Boats in a harbor with calm water and a blue sky |
| Image: Segmentation map of an interior scene | Image: Living room with furniture                       | Image: Living room with furniture and a television       | Image: Living room with furniture and a television, with a hazy effect | Image: Living room with furniture                       |
| Image: Segmentation map of an interior scene | Image: Bathroom with sink and mirror                    | Image: Bathroom with sink and mirror, with a hazy effect | Image: Bathroom with sink and mirror, with a hazy effect               | Image: Bathroom with sink and mirror                    |
| Image: Segmentation map of a food scene      | Image: Platter of food and beers                        | Image: Waffles and other food items on a plate           | Image: Waffles and other food items on a plate, with a hazy effect     | Image: Pizza and drinks                                 |
| Image: Segmentation map of a food scene      | Image: Plate with avocado toast and salad               | Image: Plate with crackers and salad                     | Image: Plate with crackers and salad, with a hazy effect               | Image: Plate with toast, potatoes, and salad            |
| Image: Segmentation map of a train scene     | Image: Train on tracks                                  | Image: Train on tracks with a hazy effect                | Image: Train on tracks with a hazy effect                              | Image: Train on tracks                                  |
| Image: Segmentation map of a canal scene     | Image: Yellow boat on a canal                           | Image: White boat on a canal with a hazy effect          | Image: White boat on a canal with a hazy effect                        | Image: Red boat on a canal                              |

Figure 17: Additional results with comparison to those from the CRN [\[6\]](#page-8-6) and pix2pixHD [\[48\]](#page-9-2) methods on the COCO-Stuff dataset.

<span id="page-15-1"></span><span id="page-15-0"></span>Image /page/15/Picture/0 description: This image displays a grid of images comparing different methods for image generation. The columns are labeled 'Label', 'Ground Truth', 'CRN', 'pix2pixHD', and 'Ours'. Each row shows a different scene, starting with a bedroom, followed by a conference room, another conference room, a dining room, a living room, a home office, and finally another bedroom. The 'Label' column shows semantic segmentation masks for each scene, while the other columns show the generated images from different methods, with 'Ground Truth' representing the original image.

Figure 18: Additional results with comparison to those from the CRN [\[6\]](#page-8-6) and pix2pixHD [\[48\]](#page-9-2) methods on the ADE20K dataset.

<span id="page-16-1"></span><span id="page-16-0"></span>

| Label                   | Ground Truth              | CRN                       | SIMS                      | pix2pixHD                 | Ours                      |
|-------------------------|---------------------------|---------------------------|---------------------------|---------------------------|---------------------------|
| Image: Segmentation map | Image: Coastal landscape  | Image: Coastal landscape  | Image: Coastal landscape  | Image: Coastal landscape  | Image: Coastal landscape  |
| Image: Segmentation map | Image: Forest stream      | Image: Forest stream      | Image: Rocky landscape    | Image: Forest stream      | Image: Forest stream      |
| Image: Segmentation map | Image: City street        | Image: City street        | Image: City street        | Image: City street        | Image: City street        |
| Image: Segmentation map | Image: Golf course        | Image: Golf course        | Image: Golf course        | Image: Golf course        | Image: Golf course        |
| Image: Segmentation map | Image: Mountain landscape | Image: Mountain landscape | Image: Mountain landscape | Image: Mountain landscape | Image: Mountain landscape |
| Image: Segmentation map | Image: Rock archway       | Image: Rock archway       | Image: Rock archway       | Image: Rock archway       | Image: Rock archway       |
| Image: Segmentation map | Image: Tropical beach     | Image: Tropical beach     | Image: Tropical beach     | Image: Tropical beach     | Image: Tropical beach     |
| Image: Segmentation map | Image: Golf players       | Image: Golf players       | Image: Golf players       | Image: Golf players       | Image: Golf players       |
| Image: Segmentation map | Image: Rural scene        | Image: Rural scene        | Image: Rural scene        | Image: Rural scene        | Image: Rural scene        |
| Image: Segmentation map | Image: City street        | Image: City street        | Image: City street        | Image: City street        | Image: City street        |

Figure 19: Additional results with comparison to those from the CRN [\[6\]](#page-8-6), SIMS [\[43\]](#page-9-23), and pix2pixHD [\[48\]](#page-9-2) methods on the ADE20K-outdoor dataset. 17

<span id="page-17-1"></span><span id="page-17-0"></span>Image /page/17/Picture/0 description: This image displays a comparison of different methods for semantic segmentation of road scenes. The top row shows the 'Label' (ground truth segmentation), 'Ground Truth' (original image), and 'Ours' (results from the proposed method). The bottom row shows results from three other methods: 'CRN', 'SIMS', and 'pix2pixHD'. The 'Label' image is a colorful segmentation map with different colors representing different objects like roads, cars, buildings, and vegetation. The 'Ground Truth' and the results from the other methods are photographic images of a road scene, likely taken from a vehicle's perspective, showing cars, buildings, trees, and the road itself under an overcast sky. The comparison highlights the performance of the 'Ours' method against established techniques.

The following is a list of items:

- Item 1
- Item 2
- Item 3

This is a numbered list:

1. First item
2. Second item
3. Third item

This is a table:

| <strong>Header 1</strong> | <strong>Header 2</strong> |
|---------------------------|---------------------------|
| Row 1, Col 1              | Row 1, Col 2              |
| Row 2, Col 1              | Row 2, Col 2              |

This is an example of bold text: **bold text**.

This is an example of italic text: *italic text*.

This is an example of inline code: `inline code`.

This is an example of a code block:

`console.log("Hello, world!");`

This is an example of a link: [Google](https://www.google.com).

This is an example of a header:

# Header 1

## Header 2

### Header 3

This is an example of inline math:  $E = mc^2$ .

This is an example of display math:

$$
\int_0^\infty e^{-x^2} dx = \frac{\sqrt{\pi}}{2}
$$
This is a form:

**User Information**

| Labels | Values   |
|--------|----------|
| Name   | John Doe |
| Age    | 30       |
| City   | New York |

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong>                  |
|-------------------------|------------------------------------------|
| Experiment ID           | 12345                                    |
| Date                    | 2023-10-27                               |
| Result                  | Success                                  |
| Notes                   | All parameters within acceptable ranges. |

Image /page/17/Picture/4 description: This image displays a comparison of different image generation models. The top row shows a semantic segmentation label on the left, followed by the ground truth image and the output from the 'Ours' model in the center and right columns, respectively. The bottom row presents the results from three other models: 'CRN' on the left, 'SIMS' in the center, and 'pix2pixHD' on the right. All images depict a street scene with buildings, traffic lights, and pedestrians, with a focus on the accuracy and realism of the generated images compared to the ground truth.

CRN

SIMS

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment Name         | Alpha                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

This experiment was a significant step forward in our research.

Image /page/17/Figure/8 description: This image displays a comparison of different image generation models for autonomous driving scenarios. The top row shows the 'Label' (likely a semantic segmentation map), 'Ground Truth' (the original image), and 'Ours' (the output from the model being presented). The bottom row shows the results from three other models: 'CRN', 'SIMS', and 'pix2pixHD'. All images depict a street view from a car's perspective, with parked cars lining the road and buildings on either side. The 'Label' image uses distinct colors to represent different semantic classes like road, sidewalk, cars, and buildings.

Figure 20: Additional results with comparison to those from the CRN [\[6\]](#page-8-6), SIMS [\[43\]](#page-9-23), and pix2pixHD [\[48\]](#page-9-2) methods on the Cityscapes dataset.

<span id="page-18-0"></span>Image /page/18/Picture/0 description: This image displays a grid of landscape images, organized into three columns: 'Label', 'Ground Truth', and 'Multi-modal results'. Each row presents a different landscape scene, with the 'Label' column showing a segmentation map of the scene, the 'Ground Truth' column showing the original photograph, and the 'Multi-modal results' column showing various synthesized versions of the landscape. The scenes vary widely, including coastal cliffs, mountainous valleys, open fields with trees, and snowy plains, often captured at different times of day or with varying atmospheric conditions.

Figure 21: Additional multi-modal synthesis results on the Flickr Landscapes Dataset. By sampling latent vectors from a standard Gaussian distribution, we synthesize images of diverse appearances.
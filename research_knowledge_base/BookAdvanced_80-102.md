### 3.3.4 Prostate Cancer Data Example (Continued)

Table 3.3 shows the coefficients from a number of different selection and shrinkage methods. They are *best-subset selection* using an all-subsets search, ridge regression, the lasso, principal components regression and partial least squares. Each method has a complexity parameter, and this was chosen to minimize an estimate of prediction error based on tenfold cross-validation; full details are given in Section 7.10. Briefly, cross-validation works by dividing the training data randomly into ten equal parts. The learning method is fit—for a range of values of the complexity parameter—to nine-tenths of the data, and the prediction error is computed on the remaining one-tenth. This is done in turn for each one-tenth of the data, and the ten prediction error estimates are averaged. From this we obtain an estimated prediction error curve as a function of the complexity parameter.

Note that we have already divided these data into a training set of size 67 and a test set of size 30. Cross-validation is applied to the training set, since selecting the shrinkage parameter is part of the training process. The test set is there to judge the performance of the selected model.

The estimated prediction error curves are shown in Figure 3.7. Many of the curves are very flat over large ranges near their minimum. Included are estimated standard error bands for each estimated error rate, based on the ten error estimates computed by cross-validation. We have used the "one-standard-error" rule—we pick the most parsimonious model within one standard error of the minimum (Section 7.10, page 244). Such a rule acknowledges the fact that the tradeoff curve is estimated with error, and hence takes a conservative approach.

Best-subset selection chose to use the two predictors lcvol and lweight. The last two lines of the table give the average prediction error (and its estimated standard error) over the test set.

# 3.4 Shrinkage Methods

By retaining a subset of the predictors and discarding the rest, subset selection produces a model that is interpretable and has possibly lower prediction error than the full model. However, because it is a discrete process variables are either retained or discarded—it often exhibits high variance, and so doesn't reduce the prediction error of the full model. Shrinkage methods are more continuous, and don't suffer as much from high variability.

## 3.4.1 Ridge Regression

Ridge regression shrinks the regression coefficients by imposing a penalty on their size. The ridge coefficients minimize a penalized residual sum of

Image /page/1/Figure/1 description: This image displays five plots, each illustrating the relationship between a model complexity parameter and cross-validation (CV) error, along with standard error bars. The plots are titled 'All Subsets', 'Ridge Regression', 'Lasso', 'Principal Components Regression', and 'Partial Least Squares'. The y-axis for all plots represents 'CV Error' and ranges from 0.6 to 1.8. The x-axis varies for each plot: 'Subset Size' for 'All Subsets' (0 to 8), 'Degrees of Freedom' for 'Ridge Regression' (0 to 8), 'Shrinkage Factor s' for 'Lasso' (0.0 to 1.0), and 'Number of Directions' for both 'Principal Components Regression' and 'Partial Least Squares' (0 to 8). Each plot shows a yellow line representing the estimated CV error, with blue error bars indicating the standard error. A dashed purple line is present in each plot, generally indicating an optimal or selected value for the complexity parameter.

FIGURE 3.7. Estimated prediction error curves and their standard errors for the various selection and shrinkage methods. Each curve is plotted as a function of the corresponding complexity parameter for that method. The horizontal axis has been chosen so that the model complexity increases as we move from left to right. The estimates of prediction error and their standard errors were obtained by tenfold cross-validation; full details are given in Section 7.10. The least complex model within one standard error of the best is chosen, indicated by the purple vertical broken lines.

TABLE 3.3. Estimated coefficients and test error results, for different subset and shrinkage methods applied to the prostate data. The blank entries correspond to variables omitted.

| Term       | LS       | Best Subset | Ridge    | Lasso | PCR      | PLS      |
|------------|----------|-------------|----------|-------|----------|----------|
| Intercept  | 2.465    | 2.477       | 2.452    | 2.468 | 2.497    | 2.452    |
| lcavol     | 0.680    | 0.740       | 0.420    | 0.533 | 0.543    | 0.419    |
| lweight    | 0.263    | 0.316       | 0.238    | 0.169 | 0.289    | 0.344    |
| age        | $-0.141$ |             | $-0.046$ |       | $-0.152$ | $-0.026$ |
| lbph       | 0.210    |             | 0.162    | 0.002 | 0.214    | 0.220    |
| svi        | 0.305    |             | 0.227    | 0.094 | 0.315    | 0.243    |
| lcp        | $-0.288$ |             | 0.000    |       | $-0.051$ | 0.079    |
| gleason    | $-0.021$ |             | 0.040    |       | 0.232    | 0.011    |
| pgg45      | 0.267    |             | 0.133    |       | $-0.056$ | 0.084    |
| Test Error | 0.521    | 0.492       | 0.492    | 0.479 | 0.449    | 0.528    |
| Std Error  | 0.179    | 0.143       | 0.165    | 0.164 | 0.105    | 0.152    |

squares,

$$
\hat{\beta}^{\text{ridge}} = \underset{\beta}{\text{argmin}} \left\{ \sum_{i=1}^{N} (y_i - \beta_0 - \sum_{j=1}^{p} x_{ij} \beta_j)^2 + \lambda \sum_{j=1}^{p} \beta_j^2 \right\}.
$$
 (3.41)

Here  $\lambda \geq 0$  is a complexity parameter that controls the amount of shrinkage: the larger the value of  $\lambda$ , the greater the amount of shrinkage. The coefficients are shrunk toward zero (and each other). The idea of penalizing by the sum-of-squares of the parameters is also used in neural networks, where it is known as *weight decay* (Chapter 11).

An equivalent way to write the ridge problem is

$$
$\hat{\beta}^{\text{ridge}} = \underset{\beta}{\text{argmin}} \sum_{i=1}^{N} \left( y_i - \beta_0 - \sum_{j=1}^{p} x_{ij} \beta_j \right)^2,$ 
 $(3.42)$
$$

$$
\text{subject to } \sum_{j=1}^{p} \beta_j^2 \le t
$$

which makes explicit the size constraint on the parameters. There is a oneto-one correspondence between the parameters  $\lambda$  in (3.41) and t in (3.42). When there are many correlated variables in a linear regression model, their coefficients can become poorly determined and exhibit high variance. A wildly large positive coefficient on one variable can be canceled by a similarly large negative coefficient on its correlated cousin. By imposing a size constraint on the coefficients, as in (3.42), this problem is alleviated.

The ridge solutions are not equivariant under scaling of the inputs, and so one normally standardizes the inputs before solving (3.41). In addition,

notice that the intercept  $\beta_0$  has been left out of the penalty term. Penalization of the intercept would make the procedure depend on the origin chosen for Y; that is, adding a constant c to each of the targets  $y_i$  would not simply result in a shift of the predictions by the same amount  $c$ . It can be shown (Exercise 3.5) that the solution to (3.41) can be separated into two parts, after reparametrization using *centered* inputs: each  $x_{ij}$  gets replaced by  $x_{ij} - \bar{x}_j$ . We estimate  $\beta_0$  by  $\bar{y} = \frac{1}{N} \sum_{i=1}^{N} y_i$ . The remaining coefficients get estimated by a ridge regression without intercept, using the centered  $x_{ij}$ . Henceforth we assume that this centering has been done, so that the input matrix **X** has p (rather than  $p + 1$ ) columns.

Writing the criterion in (3.41) in matrix form,

$$
RSS(\lambda) = (\mathbf{y} - \mathbf{X}\beta)^{T}(\mathbf{y} - \mathbf{X}\beta) + \lambda\beta^{T}\beta,
$$
\n(3.43)

the ridge regression solutions are easily seen to be

$$
\hat{\beta}^{\text{ridge}} = (\mathbf{X}^T \mathbf{X} + \lambda \mathbf{I})^{-1} \mathbf{X}^T \mathbf{y},\tag{3.44}
$$

where **I** is the  $p \times p$  identity matrix. Notice that with the choice of quadratic penalty  $\beta^T \beta$ , the ridge regression solution is again a linear function of y. The solution adds a positive constant to the diagonal of  $X^T X$  before inversion. This makes the problem nonsingular, even if  $X^T X$  is not of full rank, and was the main motivation for ridge regression when it was first introduced in statistics (Hoerl and Kennard, 1970). Traditional descriptions of ridge regression start with definition (3.44). We choose to motivate it via (3.41) and (3.42), as these provide insight into how it works.

Figure 3.8 shows the ridge coefficient estimates for the prostate cancer example, plotted as functions of  $df(\lambda)$ , the *effective degrees of freedom* implied by the penalty  $\lambda$  (defined in (3.50) on page 68). In the case of orthonormal inputs, the ridge estimates are just a scaled version of the least squares estimates, that is,  $\hat{\beta}^{\text{ridge}} = \hat{\beta}/(1+\lambda)$ .

Ridge regression can also be derived as the mean or mode of a posterior distribution, with a suitably chosen prior distribution. In detail, suppose  $y_i \sim N(\beta_0 + x_i^T \beta, \sigma^2)$ , and the parameters  $\beta_j$  are each distributed as  $N(0, \tau^2)$ , independently of one another. Then the (negative) log-posterior density of  $\beta$ , with  $\tau^2$  and  $\sigma^2$  assumed known, is equal to the expression in curly braces in (3.41), with  $\lambda = \sigma^2/\tau^2$  (Exercise 3.6). Thus the ridge estimate is the mode of the posterior distribution; since the distribution is Gaussian, it is also the posterior mean.

The *singular value decomposition* (SVD) of the centered input matrix  $\bf{X}$ gives us some additional insight into the nature of ridge regression. This decomposition is extremely useful in the analysis of many statistical methods. The SVD of the  $N \times p$  matrix **X** has the form

$$
\mathbf{X} = \mathbf{U}\mathbf{D}\mathbf{V}^T. \tag{3.45}
$$

Image /page/4/Figure/1 description: This is a plot showing the coefficients of different variables as a function of df(lambda). The x-axis is labeled df(lambda) and ranges from 0 to 8. The y-axis is labeled Coefficients and ranges from -0.2 to 0.6. Several variables are plotted, including lcavol, lweight, svi, pgg45, lbph, gleason, age, and lcp. A dashed red line is shown at approximately df(lambda) = 5. The coefficients for lcavol increase linearly with df(lambda). The coefficients for lweight, svi, pgg45, and lbph generally increase with df(lambda) but at a slower rate than lcavol. The coefficients for gleason, age, and lcp generally decrease with df(lambda), with lcp showing a significant decrease.

FIGURE 3.8. Profiles of ridge coefficients for the prostate cancer example, as the tuning parameter  $\lambda$  is varied. Coefficients are plotted versus  $df(\lambda)$ , the effective degrees of freedom. A vertical line is drawn at  $df = 5.0$ , the value chosen by cross-validation.

Here U and V are  $N \times p$  and  $p \times p$  orthogonal matrices, with the columns of  $U$  spanning the column space of  $X$ , and the columns of  $V$  spanning the row space. D is a  $p \times p$  diagonal matrix, with diagonal entries  $d_1 \geq d_2 \geq$  $\cdots \ge d_p \ge 0$  called the singular values of **X**. If one or more values  $d_j = 0$ , X is singular.

Using the singular value decomposition we can write the least squares fitted vector as

$$
\mathbf{X}\hat{\beta}^{ls} = \mathbf{X}(\mathbf{X}^T\mathbf{X})^{-1}\mathbf{X}^T\mathbf{y}
$$
  
=  $\mathbf{U}\mathbf{U}^T\mathbf{y},$  (3.46)

after some simplification. Note that  $U^T y$  are the coordinates of y with respect to the orthonormal basis U. Note also the similarity with  $(3.33)$ ; Q and U are generally different orthogonal bases for the column space of  $X$  (Exercise 3.8).

Now the ridge solutions are

$$
\mathbf{X}\hat{\beta}^{\text{ridge}} = \mathbf{X}(\mathbf{X}^T\mathbf{X} + \lambda \mathbf{I})^{-1}\mathbf{X}^T\mathbf{y}
$$
  

$$
= \mathbf{U} \mathbf{D}(\mathbf{D}^2 + \lambda \mathbf{I})^{-1}\mathbf{D} \mathbf{U}^T\mathbf{y}
$$
  

$$
= \sum_{j=1}^p \mathbf{u}_j \frac{d_j^2}{d_j^2 + \lambda} \mathbf{u}_j^T\mathbf{y},
$$
 (3.47)

where the  $\mathbf{u}_j$  are the columns of U. Note that since  $\lambda \geq 0$ , we have  $d_j^2/(d_j^2 + \lambda^2)$  $\lambda$ )  $\leq$  1. Like linear regression, ridge regression computes the coordinates of y with respect to the orthonormal basis U. It then shrinks these coordinates by the factors  $d_j^2/(d_j^2 + \lambda)$ . This means that a greater amount of shrinkage is applied to the coordinates of basis vectors with smaller  $d_j^2$ .

What does a small value of  $d_j^2$  mean? The SVD of the centered matrix X is another way of expressing the principal components of the variables in **X**. The sample covariance matrix is given by  $S = X^T X/N$ , and from  $(3.45)$  we have

$$
\mathbf{X}^T \mathbf{X} = \mathbf{V} \mathbf{D}^2 \mathbf{V}^T,\tag{3.48}
$$

which is the *eigen decomposition* of  $X^T X$  (and of S, up to a factor N). The eigenvectors  $v_j$  (columns of V) are also called the *principal compo*nents (or Karhunen–Loeve) directions of **X**. The first principal component direction  $v_1$  has the property that  $z_1 = Xv_1$  has the largest sample variance amongst all normalized linear combinations of the columns of X. This sample variance is easily seen to be

$$
Var(\mathbf{z}_1) = Var(\mathbf{X}v_1) = \frac{d_1^2}{N},
$$
\n(3.49)

and in fact  $\mathbf{z}_1 = \mathbf{X} v_1 = \mathbf{u}_1 d_1$ . The derived variable  $\mathbf{z}_1$  is called the first principal component of  $X$ , and hence  $u_1$  is the normalized first principal

Image /page/6/Figure/1 description: This is a scatter plot showing the relationship between two variables, X1 and X2. The plot displays a cloud of green circular data points, indicating a positive correlation between X1 and X2. Two purple lines are overlaid on the data. One line, labeled "Largest Principal Component," runs diagonally from the bottom left to the top right, passing through the center of the data cloud. The second line, labeled "Smallest Principal Component," runs diagonally from the top left to the bottom right, perpendicular to the first line and also passing through the center. The x-axis is labeled "X1" and ranges from -4 to 4, with tick marks at -4, -2, 0, 2, and 4. The y-axis is labeled "X2" and ranges from -4 to 4, with tick marks at -4, -2, 0, 2, and 4.

FIGURE 3.9. Principal components of some input data points. The largest principal component is the direction that maximizes the variance of the projected data, and the smallest principal component minimizes that variance. Ridge regression projects y onto these components, and then shrinks the coefficients of the low– variance components more than the high-variance components.

component. Subsequent principal components  $z_j$  have maximum variance  $d_j^2/N$ , subject to being orthogonal to the earlier ones. Conversely the last principal component has minimum variance. Hence the small singular values  $d_i$  correspond to directions in the column space of **X** having small variance, and ridge regression shrinks these directions the most.

Figure 3.9 illustrates the principal components of some data points in two dimensions. If we consider fitting a linear surface over this domain (the Y -axis is sticking out of the page), the configuration of the data allow us to determine its gradient more accurately in the long direction than the short. Ridge regression protects against the potentially high variance of gradients estimated in the short directions. The implicit assumption is that the response will tend to vary most in the directions of high variance of the inputs. This is often a reasonable assumption, since predictors are often chosen for study because they vary with the response variable, but need not hold in general.

In Figure 3.7 we have plotted the estimated prediction error versus the quantity

$$
df(\lambda) = tr[\mathbf{X}(\mathbf{X}^T \mathbf{X} + \lambda \mathbf{I})^{-1} \mathbf{X}^T],
$$
  

$$
= tr(\mathbf{H}_{\lambda})
$$
  

$$
= \sum_{j=1}^p \frac{d_j^2}{d_j^2 + \lambda}. (3.50)
$$

This monotone decreasing function of  $\lambda$  is the *effective degrees of freedom* of the ridge regression fit. Usually in a linear-regression fit with  $p$  variables, the degrees-of-freedom of the fit is  $p$ , the number of free parameters. The idea is that although all  $p$  coefficients in a ridge fit will be non-zero, they are fit in a restricted fashion controlled by  $\lambda$ . Note that  $df(\lambda) = p$  when  $\lambda = 0$  (no regularization) and df( $\lambda$ )  $\rightarrow$  0 as  $\lambda \rightarrow \infty$ . Of course there is always an additional one degree of freedom for the intercept, which was removed apriori. This definition is motivated in more detail in Section 3.4.4 and Sections 7.4–7.6. In Figure 3.7 the minimum occurs at  $df(\lambda) = 5.0$ . Table 3.3 shows that ridge regression reduces the test error of the full least squares estimates by a small amount.

## 3.4.2 The Lasso

The lasso is a shrinkage method like ridge, with subtle but important differences. The lasso estimate is defined by

$$
\hat{\beta}^{\text{lasso}} = \underset{\beta}{\text{argmin}} \sum_{i=1}^{N} \left( y_i - \beta_0 - \sum_{j=1}^{p} x_{ij} \beta_j \right)^2
$$

$$
\text{subject to } \sum_{j=1}^{p} |\beta_j| \le t. \tag{3.51}
$$

Just as in ridge regression, we can re-parametrize the constant  $\beta_0$  by standardizing the predictors; the solution for  $\hat{\beta}_0$  is  $\bar{y}$ , and thereafter we fit a model without an intercept (Exercise 3.5). In the signal processing literature, the lasso is also known as basis pursuit (Chen et al., 1998).

We can also write the lasso problem in the equivalent *Lagrangian form* 

$$
\hat{\beta}^{\text{lasso}} = \underset{\beta}{\text{argmin}} \bigg\{ \frac{1}{2} \sum_{i=1}^{N} (y_i - \beta_0 - \sum_{j=1}^{p} x_{ij} \beta_j)^2 + \lambda \sum_{j=1}^{p} |\beta_j| \bigg\}. \tag{3.52}
$$

Notice the similarity to the ridge regression problem (3.42) or (3.41): the  $L_2$  ridge penalty  $\sum_{i=1}^{p} \beta_i^2$  is replaced by the  $L_1$  lasso penalty  $\sum_{i=1}^{p} |\beta_i|$ . This latter constraint makes the solutions nonlinear in the  $y_i$ , and there is no closed form expression as in ridge regression. Computing the lasso solution is a quadratic programming problem, although we see in Section 3.4.4 that efficient algorithms are available for computing the entire path of solutions as  $\lambda$  is varied, with the same computational cost as for ridge regression. Because of the nature of the constraint, making  $t$  sufficiently small will cause some of the coefficients to be exactly zero. Thus the lasso does a kind of continuous subset selection. If t is chosen larger than  $t_0 = \sum_{i=1}^{p} |\hat{\beta}_j|$  (where  $\hat{\beta}_j = \hat{\beta}_j^{\text{ls}}$ , the least squares estimates), then the lasso estimates are the  $\hat{\beta}_j$ 's. On the other hand, for  $t = t_0/2$  say, then the least squares coefficients are shrunk by about 50% on average. However, the nature of the shrinkage is not obvious, and we investigate it further in Section 3.4.4 below. Like the subset size in variable subset selection, or the penalty parameter in ridge regression, t should be adaptively chosen to minimize an estimate of expected prediction error.

In Figure 3.7, for ease of interpretation, we have plotted the lasso prediction error estimates versus the standardized parameter  $s = t/\sum_{i=1}^{p} |\hat{\beta}_j|$ . A value  $\hat{s} \approx 0.36$  was chosen by 10-fold cross-validation; this caused four coefficients to be set to zero (fifth column of Table 3.3). The resulting model has the second lowest test error, slightly lower than the full least squares model, but the standard errors of the test error estimates (last line of Table 3.3) are fairly large.

Figure 3.10 shows the lasso coefficients as the standardized tuning parameter  $s = t/\sum_{1}^{p}|\hat{\beta}_j|$  is varied. At  $s = 1.0$  these are the least squares estimates; they decrease to 0 as  $s \to 0$ . This decrease is not always strictly monotonic, although it is in this example. A vertical line is drawn at  $s = 0.36$ , the value chosen by cross-validation.

## 3.4.3 Discussion: Subset Selection, Ridge Regression and the Lasso

In this section we discuss and compare the three approaches discussed so far for restricting the linear regression model: subset selection, ridge regression and the lasso.

In the case of an orthonormal input matrix  $X$  the three procedures have explicit solutions. Each method applies a simple transformation to the least squares estimate  $\hat{\beta}_j$ , as detailed in Table 3.4.

Ridge regression does a proportional shrinkage. Lasso translates each coefficient by a constant factor  $\lambda$ , truncating at zero. This is called "soft thresholding," and is used in the context of wavelet-based smoothing in Section 5.9. Best-subset selection drops all variables with coefficients smaller than the Mth largest; this is a form of "hard-thresholding."

Back to the nonorthogonal case; some pictures help understand their relationship. Figure 3.11 depicts the lasso (left) and ridge regression (right) when there are only two parameters. The residual sum of squares has elliptical contours, centered at the full least squares estimate. The constraint

Image /page/9/Figure/1 description: This is a plot showing the coefficients of several variables as a function of the shrinkage factor s. The y-axis represents the coefficients, ranging from -0.2 to 0.8. The x-axis represents the shrinkage factor s, ranging from 0.0 to 1.0. Several lines, each representing a different variable, are plotted. The variables are labeled as lcavol, svi, lweight, pgg45, lbph, gleason, age, and lcp. The line for lcavol starts at a coefficient of approximately 0.0 at s=0.0 and increases to a coefficient of approximately 0.7 at s=1.0. The lines for svi, lweight, pgg45, and lbph generally increase with s, with some fluctuations. The lines for gleason, age, and lcp generally decrease with s, with some fluctuations. A vertical dashed red line is present at s=0.4.

FIGURE 3.10. Profiles of lasso coefficients, as the tuning parameter t is varied. Coefficients are plotted versus  $s = t/\sum_{1}^{p} |\hat{\beta}_{j}|$ . A vertical line is drawn at  $s = 0.36$ , the value chosen by cross-validation. Compare Figure 3.8 on page 65; the lasso profiles hit zero, while those for ridge do not. The profiles are piece-wise linear, and so are computed only at the points displayed; see Section 3.4.4 for details.

**TABLE 3.4.** Estimators of  $\beta_j$  in the case of orthonormal columns of **X**. M and  $\lambda$ are constants chosen by the corresponding techniques; sign denotes the sign of its argument  $(\pm 1)$ , and  $x_+$  denotes "positive part" of x. Below the table, estimators are shown by broken red lines. The 45° line in gray shows the unrestricted estimate for reference.

Image /page/10/Figure/2 description: The image displays a table comparing three estimators: Best subset (size M), Ridge, and Lasso, along with their respective formulas. Below the table are three plots illustrating the effect of these estimators. The first plot, labeled 'Best Subset', shows a piecewise linear relationship with a threshold at |βˆ(M)|. The second plot, labeled 'Ridge', shows a linear shrinkage of the coefficient towards zero. The third plot, labeled 'Lasso', also shows a linear shrinkage but with a sharp corner at λ, indicating potential sparsity. Further down, two contour plots are presented, each showing elliptical contours representing the distribution of estimated coefficients. The left plot, associated with the Best Subset or Lasso, shows the contours intersecting a diamond-shaped region (L1 norm constraint), indicating a solution at a corner. The right plot, associated with Ridge regression, shows the contours intersecting a circular region (L2 norm constraint), indicating a solution on the circle. Both contour plots show the estimated coefficient vector βˆ within the feasible region.

FIGURE 3.11. Estimation picture for the lasso (left) and ridge regression (right). Shown are contours of the error and constraint functions. The solid blue areas are the constraint regions  $|\beta_1| + |\beta_2| \leq t$  and  $\beta_1^2 + \beta_2^2 \leq t^2$ , respectively, while the red ellipses are the contours of the least squares error function.

region for ridge regression is the disk  $\beta_1^2 + \beta_2^2 \leq t$ , while that for lasso is the diamond  $|\beta_1| + |\beta_2| \leq t$ . Both methods find the first point where the elliptical contours hit the constraint region. Unlike the disk, the diamond has corners; if the solution occurs at a corner, then it has one parameter  $\beta_i$  equal to zero. When  $p > 2$ , the diamond becomes a rhomboid, and has many corners, flat edges and faces; there are many more opportunities for the estimated parameters to be zero.

We can generalize ridge regression and the lasso, and view them as Bayes estimates. Consider the criterion

$$
\tilde{\beta} = \underset{\beta}{\text{argmin}} \left\{ \sum_{i=1}^{N} (y_i - \beta_0 - \sum_{j=1}^{p} x_{ij} \beta_j)^2 + \lambda \sum_{j=1}^{p} |\beta_j|^q \right\}
$$
(3.53)

for  $q \geq 0$ . The contours of constant value of  $\sum_j |\beta_j|^q$  are shown in Figure 3.12, for the case of two inputs.

Thinking of  $|\beta_j|^q$  as the log-prior density for  $\beta_j$ , these are also the equicontours of the prior distribution of the parameters. The value  $q = 0$  corresponds to variable subset selection, as the penalty simply counts the number of nonzero parameters;  $q = 1$  corresponds to the lasso, while  $q = 2$  to ridge regression. Notice that for  $q \leq 1$ , the prior is not uniform in direction, but concentrates more mass in the coordinate directions. The prior corresponding to the  $q = 1$  case is an independent double exponential (or Laplace) distribution for each input, with density  $(1/2\tau) \exp(-|\beta|/\tau)$  and  $\tau = 1/\lambda$ . The case  $q = 1$  (lasso) is the smallest q such that the constraint region is convex; non-convex constraint regions make the optimization problem more difficult.

In this view, the lasso, ridge regression and best subset selection are Bayes estimates with different priors. Note, however, that they are derived as posterior modes, that is, maximizers of the posterior. It is more common to use the mean of the posterior as the Bayes estimate. Ridge regression is also the posterior mean, but the lasso and best subset selection are not.

Looking again at the criterion (3.53), we might try using other values of q besides 0, 1, or 2. Although one might consider estimating q from the data, our experience is that it is not worth the effort for the extra variance incurred. Values of  $q \in (1, 2)$  suggest a compromise between the lasso and ridge regression. Although this is the case, with  $q > 1$ ,  $|\beta_j|^q$  is differentiable at 0, and so does not share the ability of lasso  $(q = 1)$  for

Image /page/11/Figure/8 description: The image displays five plots arranged horizontally, each showing a curve on a Cartesian coordinate system with x and y axes. Each plot is labeled with a value of 'q'. The first plot, labeled 'q = 4', shows a rounded square shape. The second plot, labeled 'q = 2', shows a circle. The third plot, labeled 'q = 1', shows a diamond shape. The fourth plot, labeled 'q = 0.5', shows a shape resembling a four-pointed star with sharp cusps. The fifth plot, labeled 'q = 0.1', shows a shape that is essentially a cross, with four straight line segments extending from the origin along the axes.

**FIGURE 3.12.** Contours of constant value of  $\sum_j |\beta_j|^q$  for given values of q.

Image /page/12/Figure/1 description: Two graphs are shown side-by-side. The graph on the left is labeled "q = 1.2" and "Lq". It is a diamond shape with curved sides, colored cyan. The graph on the right is labeled "α = 0.2" and "Elastic Net". It is also a diamond shape with curved sides, colored red. Both graphs are centered on a black crosshair.

**FIGURE 3.13.** Contours of constant value of  $\sum_j |\beta_j|^q$  for  $q = 1.2$  (left plot), and the elastic-net penalty  $\sum_j (\alpha \beta_j^2 + (1-\alpha)|\beta_j|)$  for  $\alpha = 0.2$  (right plot). Although visually very similar, the elastic-net has sharp (non-differentiable) corners, while the  $q = 1.2$  penalty does not.

setting coefficients exactly to zero. Partly for this reason as well as for computational tractability, Zou and Hastie (2005) introduced the elasticnet penalty

$$
\lambda \sum_{j=1}^{p} \left( \alpha \beta_j^2 + (1 - \alpha)|\beta_j| \right),\tag{3.54}
$$

a different compromise between ridge and lasso. Figure 3.13 compares the  $L_q$  penalty with  $q = 1.2$  and the elastic-net penalty with  $\alpha = 0.2$ ; it is hard to detect the difference by eye. The elastic-net selects variables like the lasso, and shrinks together the coefficients of correlated predictors like ridge. It also has considerable computational advantages over the  $L_q$  penalties. We discuss the elastic-net further in Section 18.4.

## 3.4.4 Least Angle Regression

Least angle regression (LAR) is a relative newcomer (Efron et al., 2004), and can be viewed as a kind of "democratic" version of forward stepwise regression (Section 3.3.2). As we will see, LAR is intimately connected with the lasso, and in fact provides an extremely efficient algorithm for computing the entire lasso path as in Figure 3.10.

Forward stepwise regression builds a model sequentially, adding one variable at a time. At each step, it identifies the best variable to include in the active set, and then updates the least squares fit to include all the active variables.

Least angle regression uses a similar strategy, but only enters "as much" of a predictor as it deserves. At the first step it identifies the variable most correlated with the response. Rather than fit this variable completely, LAR moves the coefficient of this variable continuously toward its leastsquares value (causing its correlation with the evolving residual to decrease in absolute value). As soon as another variable "catches up" in terms of correlation with the residual, the process is paused. The second variable then joins the active set, and their coefficients are moved together in a way that keeps their correlations tied and decreasing. This process is continued

until all the variables are in the model, and ends at the full least-squares fit. Algorithm 3.2 provides the details. The termination condition in step 5 requires some explanation. If  $p > N - 1$ , the LAR algorithm reaches a zero residual solution after  $N-1$  steps (the  $-1$  is because we have centered the data).

### Algorithm 3.2 Least Angle Regression.

- 1. Standardize the predictors to have mean zero and unit norm. Start with the residual  $\mathbf{r} = \mathbf{y} - \bar{\mathbf{y}}, \beta_1, \beta_2, \dots, \beta_p = 0.$
- 2. Find the predictor  $\mathbf{x}_j$  most correlated with r.
- 3. Move  $\beta_j$  from 0 towards its least-squares coefficient  $\langle \mathbf{x}_j , \mathbf{r} \rangle$ , until some other competitor  $x_k$  has as much correlation with the current residual as does  $x_i$ .
- 4. Move  $\beta_i$  and  $\beta_k$  in the direction defined by their joint least squares coefficient of the current residual on  $(\mathbf{x}_i, \mathbf{x}_k)$ , until some other competitor  $x_l$  has as much correlation with the current residual.
- 5. Continue in this way until all  $p$  predictors have been entered. After  $\min(N-1, p)$  steps, we arrive at the full least-squares solution.

Suppose  $A_k$  is the active set of variables at the beginning of the kth step, and let  $\beta_{\mathcal{A}_k}$  be the coefficient vector for these variables at this step; there will be  $k - 1$  nonzero values, and the one just entered will be zero. If  $\mathbf{r}_k = \mathbf{y} - \mathbf{X}_{\mathcal{A}_k} \beta_{\mathcal{A}_k}$  is the current residual, then the direction for this step is

$$
\delta_k = (\mathbf{X}_{\mathcal{A}_k}^T \mathbf{X}_{\mathcal{A}_k})^{-1} \mathbf{X}_{\mathcal{A}_k}^T \mathbf{r}_k. \tag{3.55}
$$

The coefficient profile then evolves as  $\beta_{A_k}(\alpha) = \beta_{A_k} + \alpha \cdot \delta_k$ . Exercise 3.23 verifies that the directions chosen in this fashion do what is claimed: keep the correlations tied and decreasing. If the fit vector at the beginning of this step is  $\hat{\mathbf{f}}_k$ , then it evolves as  $\hat{\mathbf{f}}_k(\alpha) = \hat{\mathbf{f}}_k + \alpha \cdot \mathbf{u}_k$ , where  $\mathbf{u}_k = \mathbf{X}_{\mathcal{A}_k} \delta_k$ is the new fit direction. The name "least angle" arises from a geometrical interpretation of this process;  $\mathbf{u}_k$  makes the smallest (and equal) angle with each of the predictors in  $\mathcal{A}_k$  (Exercise 3.24). Figure 3.14 shows the absolute correlations decreasing and joining ranks with each step of the LAR algorithm, using simulated data.

By construction the coefficients in LAR change in a piecewise linear fashion. Figure 3.15 [left panel] shows the LAR coefficient profile evolving as a function of their  $L_1$  arc length <sup>2</sup>. Note that we do not need to take small

<sup>&</sup>lt;sup>2</sup>The  $L_1$  arc-length of a differentiable curve  $\beta(s)$  for  $s \in [0, S]$  is given by  $\text{TV}(\beta, S)$  =  $\int_0^S ||\dot{\beta}(s)||_1 ds$ , where  $\dot{\beta}(s) = \partial \beta(s)/\partial s$ . For the piecewise-linear LAR coefficient profile, this amounts to summing the  $L_1$  norms of the changes in coefficients from step to step.

Image /page/14/Figure/1 description: The image is a line graph showing absolute correlations on the y-axis against L1 Arc Length on the x-axis. The y-axis ranges from 0.0 to 0.4, with tick marks at 0.0, 0.1, 0.2, 0.3, and 0.4. The x-axis ranges from 0 to 15, with tick marks at 0, 5, 10, and 15. Several lines of different colors are plotted on the graph. Vertical dashed lines are present at x-values corresponding to labels v2, v6, v4, v5, v3, and v1. The blue line starts at approximately 0.4 at x=0, decreases sharply to approximately 0.15 at x=9, and then decreases to approximately 0.02 at x=17. The cyan line starts at approximately 0.4 at x=0 and ends at x=1. The orange line starts at approximately 0.16 at x=0, increases slightly to approximately 0.17 at x=2, then decreases to approximately 0.08 at x=10, and ends at approximately 0.04 at x=17. The dark red line starts at approximately 0.14 at x=0, decreases to approximately 0.12 at x=5, then increases to approximately 0.14 at x=10, and decreases to approximately 0.05 at x=17. The gray line starts at approximately 0.12 at x=0, increases to approximately 0.15 at x=8, and then decreases to approximately 0.03 at x=17. The light green line starts at approximately 0.05 at x=0, decreases to approximately 0.005 at x=8, and then increases to approximately 0.04 at x=17.

FIGURE 3.14. Progression of the absolute correlations during each step of the LAR procedure, using a simulated data set with six predictors. The labels at the top of the plot indicate which variables enter the active set at each step. The step length are measured in units of  $L_1$  arc length.

Image /page/14/Figure/3 description: The image displays two plots side-by-side, both titled with the x-axis labeled "L1 Arc Length" and the y-axis labeled "Coefficients". The left plot is titled "Least Angle Regression" and the right plot is titled "Lasso". Both plots show multiple colored lines representing coefficients changing with L1 Arc Length. The y-axis ranges from -1.5 to 0.5, and the x-axis ranges from 0 to 15. Several vertical dotted lines are present on both plots, indicating specific points along the L1 Arc Length. The lines in both plots generally start near zero and change their values as the L1 Arc Length increases, with some lines increasing and others decreasing. Some lines appear to be identical in both plots, while others show slight variations in their paths.

FIGURE 3.15. Left panel shows the LAR coefficient profiles on the simulated data, as a function of the  $L_1$  arc length. The right panel shows the Lasso profile. They are identical until the dark-blue coefficient crosses zero at an arc length of about 18.

steps and recheck the correlations in step 3; using knowledge of the covariance of the predictors and the piecewise linearity of the algorithm, we can work out the exact step length at the beginning of each step (Exercise 3.25).

The right panel of Figure 3.15 shows the lasso coefficient profiles on the same data. They are almost identical to those in the left panel, and differ for the first time when the blue coefficient passes back through zero. For the prostate data, the LAR coefficient profile turns out to be identical to the lasso profile in Figure 3.10, which never crosses zero. These observations lead to a simple modification of the LAR algorithm that gives the entire lasso path, which is also piecewise-linear.

### Algorithm 3.2a Least Angle Regression: Lasso Modification.

4a. If a non-zero coefficient hits zero, drop its variable from the active set of variables and recompute the current joint least squares direction.

The LAR(lasso) algorithm is extremely efficient, requiring the same order of computation as that of a single least squares fit using the  $p$  predictors. Least angle regression always takes  $p$  steps to get to the full least squares estimates. The lasso path can have more than  $p$  steps, although the two are often quite similar. Algorithm 3.2 with the lasso modification 3.2a is an efficient way of computing the solution to any lasso problem, especially when  $p \gg N$ . Osborne et al. (2000a) also discovered a piecewise-linear path for computing the lasso, which they called a homotopy algorithm.

We now give a heuristic argument for why these procedures are so similar. Although the LAR algorithm is stated in terms of correlations, if the input features are standardized, it is equivalent and easier to work with innerproducts. Suppose  $A$  is the active set of variables at some stage in the algorithm, tied in their absolute inner-product with the current residuals  $y - X\beta$ . We can express this as

$$
\mathbf{x}_j^T(\mathbf{y} - \mathbf{X}\beta) = \gamma \cdot s_j, \ \forall j \in \mathcal{A} \tag{3.56}
$$

where  $s_j \in \{-1,1\}$  indicates the sign of the inner-product, and  $\gamma$  is the common value. Also  $|\mathbf{x}_k^T(\mathbf{y} - \mathbf{X}\beta)| \leq \gamma \ \forall k \notin \mathcal{A}$ . Now consider the lasso criterion (3.52), which we write in vector form

$$
R(\beta) = \frac{1}{2} ||\mathbf{y} - \mathbf{X}\beta||_2^2 + \lambda ||\beta||_1.
$$
 (3.57)

Let B be the active set of variables in the solution for a given value of  $\lambda$ . For these variables  $R(\beta)$  is differentiable, and the stationarity conditions give

$$
\mathbf{x}_j^T(\mathbf{y} - \mathbf{X}\beta) = \lambda \cdot \text{sign}(\beta_j), \ \forall j \in \mathcal{B}
$$
 (3.58)

Comparing (3.58) with (3.56), we see that they are identical only if the sign of  $\beta_j$  matches the sign of the inner product. That is why the LAR

algorithm and lasso start to differ when an active coefficient passes through zero; condition (3.58) is violated for that variable, and it is kicked out of the active set  $\beta$ . Exercise 3.23 shows that these equations imply a piecewiselinear coefficient profile as  $\lambda$  decreases. The stationarity conditions for the non-active variables require that

$$
|\mathbf{x}_k^T(\mathbf{y} - \mathbf{X}\beta)| \le \lambda, \ \forall k \notin \mathcal{B},\tag{3.59}
$$

which again agrees with the LAR algorithm.

Figure 3.16 compares LAR and lasso to forward stepwise and stagewise regression. The setup is the same as in Figure 3.6 on page 59, except here  $N = 100$  here rather than 300, so the problem is more difficult. We see that the more aggressive forward stepwise starts to overfit quite early (well before the 10 true variables can enter the model), and ultimately performs worse than the slower forward stagewise regression. The behavior of LAR and lasso is similar to that of forward stagewise regression. Incremental forward stagewise is similar to LAR and lasso, and is described in Section 3.8.1.

### Degrees-of-Freedom Formula for LAR and Lasso

Suppose that we fit a linear model via the least angle regression procedure, stopping at some number of steps  $k < p$ , or equivalently using a lasso bound t that produces a constrained version of the full least squares fit. How many parameters, or "degrees of freedom" have we used?

Consider first a linear regression using a subset of  $k$  features. If this subset is prespecified in advance without reference to the training data, then the degrees of freedom used in the fitted model is defined to be k. Indeed, in classical statistics, the number of linearly independent parameters is what is meant by "degrees of freedom." Alternatively, suppose that we carry out a best subset selection to determine the "optimal" set of k predictors. Then the resulting model has  $k$  parameters, but in some sense we have used up more than  $k$  degrees of freedom.

We need a more general definition for the effective degrees of freedom of an adaptively fitted model. We define the degrees of freedom of the fitted vector  $\hat{\mathbf{y}} = (\hat{y}_1, \hat{y}_2, \dots, \hat{y}_N)$  as

$$
df(\hat{\mathbf{y}}) = \frac{1}{\sigma^2} \sum_{i=1}^{N} Cov(\hat{y}_i, y_i).
$$
\n(3.60)

Here  $Cov(\hat{y}_i, y_i)$  refers to the sampling covariance between the predicted value  $\hat{y}_i$  and its corresponding outcome value  $y_i$ . This makes intuitive sense: the harder that we fit to the data, the larger this covariance and hence  $df(\hat{y})$ . Expression (3.60) is a useful notion of degrees of freedom, one that can be applied to any model prediction  $\hat{y}$ . This includes models that are

Image /page/17/Figure/1 description: A line graph displays the relationship between the fraction of L1 arc-length on the x-axis and E||\hat{\beta}(k) - \beta||^2 on the y-axis. Five different methods are plotted: Forward Stepwise (black dots connected by a dashed line), LAR (light blue dots connected by a dashed line), Lasso (orange dots connected by a dashed line), Forward Stagewise (yellow dots connected by a dashed line), and Incremental Forward Stagewise (green dots connected by a dashed line). The y-axis ranges from approximately 0.55 to 0.67, with tick marks at 0.55, 0.60, and 0.65. The x-axis ranges from 0.0 to 1.0, with tick marks at 0.0, 0.2, 0.4, 0.6, 0.8, and 1.0. The Forward Stepwise line starts at approximately 0.58, decreases to a minimum around 0.4 fraction, and then increases. The other four lines (LAR, Lasso, Forward Stagewise, and Incremental Forward Stagewise) all start at a higher value around 0.65 at 0.0 fraction, decrease to a minimum around 0.4-0.5 fraction, and then increase again, with the LAR line generally showing the lowest values in the increasing portion.

FIGURE 3.16. Comparison of LAR and lasso with forward stepwise, forward stagewise  $(FS)$  and incremental forward stagewise  $(FS_0)$  regression. The setup is the same as in Figure 3.6, except  $N = 100$  here rather than 300. Here the slower FS regression ultimately outperforms forward stepwise. LAR and lasso show similar behavior to  $FS$  and  $FS_0$ . Since the procedures take different numbers of steps (across simulation replicates and methods), we plot the MSE as a function of the fraction of total  $L_1$  arc-length toward the least-squares fit.

adaptively fitted to the training data. This definition is motivated and discussed further in Sections 7.4–7.6.

Now for a linear regression with  $k$  fixed predictors, it is easy to show that  $df(\hat{y}) = k$ . Likewise for ridge regression, this definition leads to the closed-form expression (3.50) on page 68:  $df(\hat{y}) = tr(S_{\lambda})$ . In both these cases, (3.60) is simple to evaluate because the fit  $\hat{y} = H_{\lambda}y$  is linear in y. If we think about definition (3.60) in the context of a best subset selection of size k, it seems clear that  $df(\hat{y})$  will be larger than k, and this can be verified by estimating  $Cov(\hat{y}_i, y_i)/\sigma^2$  directly by simulation. However there is no closed form method for estimating  $df(\hat{y})$  for best subset selection.

For LAR and lasso, something magical happens. These techniques are adaptive in a smoother way than best subset selection, and hence estimation of degrees of freedom is more tractable. Specifically it can be shown that after the kth step of the LAR procedure, the effective degrees of freedom of the fit vector is exactly  $k$ . Now for the lasso, the (modified) LAR procedure often takes more than  $p$  steps, since predictors can drop out. Hence the definition is a little different; for the lasso, at any stage  $df(\hat{y})$  approximately equals the number of predictors in the model. While this approximation works reasonably well anywhere in the lasso path, for each  $k$  it works best at the *last* model in the sequence that contains  $k$  predictors. A detailed study of the degrees of freedom for the lasso may be found in Zou et al.  $(2007).$ 

# 3.5 Methods Using Derived Input Directions

In many situations we have a large number of inputs, often very correlated. The methods in this section produce a small number of linear combinations  $Z_m$ ,  $m = 1, \ldots, M$  of the original inputs  $X_j$ , and the  $Z_m$  are then used in place of the  $X_i$  as inputs in the regression. The methods differ in how the linear combinations are constructed.

## 3.5.1 Principal Components Regression

In this approach the linear combinations  $Z_m$  used are the principal components as defined in Section 3.4.1 above.

Principal component regression forms the derived input columns  $z_m =$  $\mathbf{X}v_m$ , and then regresses y on  $\mathbf{z}_1, \mathbf{z}_2, \ldots, \mathbf{z}_M$  for some  $M \leq p$ . Since the  $\mathbf{z}_m$ are orthogonal, this regression is just a sum of univariate regressions:

$$
\hat{\mathbf{y}}_{(M)}^{\text{per}} = \bar{y}\mathbf{1} + \sum_{m=1}^{M} \hat{\theta}_m \mathbf{z}_m, \tag{3.61}
$$

where  $\hat{\theta}_m = \langle \mathbf{z}_m, \mathbf{y} \rangle / \langle \mathbf{z}_m, \mathbf{z}_m \rangle$ . Since the  $\mathbf{z}_m$  are each linear combinations of the original  $x_j$ , we can express the solution (3.61) in terms of coefficients of the  $x_i$  (Exercise 3.13):

$$
\hat{\beta}^{\text{pcr}}(M) = \sum_{m=1}^{M} \hat{\theta}_m v_m.
$$
\n(3.62)

As with ridge regression, principal components depend on the scaling of the inputs, so typically we first standardize them. Note that if  $M = p$ , we would just get back the usual least squares estimates, since the columns of  $\mathbf{Z} = \mathbf{U} \mathbf{D}$  span the column space of **X**. For  $M < p$  we get a reduced regression. We see that principal components regression is very similar to ridge regression: both operate via the principal components of the input matrix. Ridge regression shrinks the coefficients of the principal components (Figure 3.17), shrinking more depending on the size of the corresponding eigenvalue; principal components regression discards the  $p - M$  smallest eigenvalue components. Figure 3.17 illustrates this.

Image /page/19/Figure/1 description: The image is a line graph showing the shrinkage factor on the y-axis and the index on the x-axis. There are two lines plotted: one in blue representing 'ridge' and one in orange representing 'pcr'. The 'ridge' line starts at approximately 0.9 and decreases linearly to about 0.28 at index 8. The 'pcr' line starts at 1.0 and remains at 1.0 until index 7, where it drops sharply to 0.0 and stays at 0.0 for index 8. The legend indicates that the blue line is 'ridge' and the orange line is 'pcr'.

FIGURE 3.17. Ridge regression shrinks the regression coefficients of the principal components, using shrinkage factors  $d_j^2/(d_j^2 + \lambda)$  as in (3.47). Principal component regression truncates them. Shown are the shrinkage and truncation patterns corresponding to Figure 3.7, as a function of the principal component index.

In Figure 3.7 we see that cross-validation suggests seven terms; the resulting model has the lowest test error in Table 3.3.

## 3.5.2 Partial Least Squares

This technique also constructs a set of linear combinations of the inputs for regression, but unlike principal components regression it uses y (in addition to  $X$ ) for this construction. Like principal component regression, partial least squares (PLS) is not scale invariant, so we assume that each  $x_i$  is standardized to have mean 0 and variance 1. PLS begins by computing  $\hat{\varphi}_{1j} = \langle \mathbf{x}_j, \mathbf{y} \rangle$  for each j. From this we construct the derived input  $\mathbf{z}_1 = \sum_j \hat{\varphi}_{1j} \mathbf{x}_j$ , which is the first partial least squares direction. Hence in the construction of each  $z_m$ , the inputs are weighted by the strength of their univariate effect on  $y^3$ . The outcome y is regressed on  $z_1$  giving coefficient  $\hat{\theta}_1$ , and then we orthogonalize  $x_1, \ldots, x_p$  with respect to  $z_1$ . We continue this process, until  $M \leq p$  directions have been obtained. In this manner, partial least squares produces a sequence of derived, orthogonal inputs or directions  $z_1, z_2, \ldots, z_M$ . As with principal-component regression, if we were to construct all  $M = p$  directions, we would get back a solution equivalent to the usual least squares estimates; using  $M < p$  directions produces a reduced regression. The procedure is described fully in Algorithm 3.3.

<sup>&</sup>lt;sup>3</sup>Since the  $\mathbf{x}_j$  are standardized, the first directions  $\hat{\varphi}_{1j}$  are the univariate regression coefficients (up to an irrelevant constant); this is not the case for subsequent directions.

Algorithm 3.3 Partial Least Squares.

- 1. Standardize each  $\mathbf{x}_j$  to have mean zero and variance one. Set  $\hat{\mathbf{y}}^{(0)} =$  $\bar{y}$ **1**, and  $\mathbf{x}_{j}^{(0)} = \mathbf{x}_{j}, \ j = 1, \ldots, p.$
- 2. For  $m = 1, 2, ..., p$ 
  - (a)  $\mathbf{z}_m = \sum_{j=1}^p \hat{\varphi}_{mj} \mathbf{x}_j^{(m-1)}$ , where  $\hat{\varphi}_{mj} = \langle \mathbf{x}_j^{(m-1)}, \mathbf{y} \rangle$ .
  - (b)  $\hat{\theta}_m = \langle \mathbf{z}_m, \mathbf{y} \rangle / \langle \mathbf{z}_m, \mathbf{z}_m \rangle.$
  - (c)  $\hat{\mathbf{y}}^{(m)} = \hat{\mathbf{y}}^{(m-1)} + \hat{\theta}_m \mathbf{z}_m.$
  - (d) Orthogonalize each  $\mathbf{x}_j^{(m-1)}$  with respect to  $\mathbf{z}_m: \mathbf{x}_j^{(m)} = \mathbf{x}_j^{(m-1)}$  $\langle \langle \mathbf{z}_m, \mathbf{x}_j^{(m-1)} \rangle / \langle \mathbf{z}_m, \mathbf{z}_m \rangle | \mathbf{z}_m, j = 1, 2, \ldots, p.$
- 3. Output the sequence of fitted vectors  $\{\hat{\mathbf{y}}_1^{(m)}\}_{1}^{p}$ . Since the  $\{\mathbf{z}_{\ell}\}_{1}^{m}$  are linear in the original  $\mathbf{x}_j$ , so is  $\hat{\mathbf{y}}^{(m)} = \mathbf{X}\hat{\beta}^{\text{pls}}(m)$ . These linear coefficients can be recovered from the sequence of PLS transformations.

In the prostate cancer example, cross-validation chose  $M = 2$  PLS directions in Figure 3.7. This produced the model given in the rightmost column of Table 3.3.

What optimization problem is partial least squares solving? Since it uses the response y to construct its directions, its solution path is a nonlinear function of y. It can be shown (Exercise 3.15) that partial least squares seeks directions that have high variance and have high correlation with the response, in contrast to principal components regression which keys only on high variance (Stone and Brooks, 1990; Frank and Friedman, 1993). In particular, the mth principal component direction  $v_m$  solves:

$$
\max_{\alpha} \text{Var}(\mathbf{X}\alpha)
$$
 (3.63)  
subject to  $||\alpha|| = 1$ ,  $\alpha^T \mathbf{S} v_\ell = 0$ ,  $\ell = 1, ..., m - 1$ ,

where **S** is the sample covariance matrix of the  $x_j$ . The conditions  $\alpha^T S v_\ell =$ 0 ensures that  $z_m = X\alpha$  is uncorrelated with all the previous linear combinations  $\mathbf{z}_{\ell} = \mathbf{X} v_{\ell}$ . The mth PLS direction  $\hat{\varphi}_m$  solves:

$$
\max_{\alpha} \text{Corr}^{2}(\mathbf{y}, \mathbf{X}\alpha) \text{Var}(\mathbf{X}\alpha)
$$
\nsubject to  $||\alpha|| = 1$ ,  $\alpha^{T} \mathbf{S} \hat{\varphi}_{\ell} = 0$ ,  $\ell = 1, \ldots, m - 1$ .

\n(3.64)

Further analysis reveals that the variance aspect tends to dominate, and so partial least squares behaves much like ridge regression and principal components regression. We discuss this further in the next section.

If the input matrix  $X$  is orthogonal, then partial least squares finds the least squares estimates after  $m = 1$  steps. Subsequent steps have no effect

since the  $\hat{\varphi}_{mj}$  are zero for  $m > 1$  (Exercise 3.14). It can also be shown that the sequence of PLS coefficients for  $m = 1, 2, \ldots, p$  represents the conjugate gradient sequence for computing the least squares solutions (Exercise 3.18).

# 3.6 Discussion: A Comparison of the Selection and Shrinkage Methods

There are some simple settings where we can understand better the relationship between the different methods described above. Consider an example with two correlated inputs  $X_1$  and  $X_2$ , with correlation  $\rho$ . We assume that the true regression coefficients are  $\beta_1 = 4$  and  $\beta_2 = 2$ . Figure 3.18 shows the coefficient profiles for the different methods, as their tuning parameters are varied. The top panel has  $\rho = 0.5$ , the bottom panel  $\rho = -0.5$ . The tuning parameters for ridge and lasso vary over a continuous range, while best subset, PLS and PCR take just two discrete steps to the least squares solution. In the top panel, starting at the origin, ridge regression shrinks the coefficients together until it finally converges to least squares. PLS and PCR show similar behavior to ridge, although are discrete and more extreme. Best subset overshoots the solution and then backtracks. The behavior of the lasso is intermediate to the other methods. When the correlation is negative (lower panel), again PLS and PCR roughly track the ridge path, while all of the methods are more similar to one another.

It is interesting to compare the shrinkage behavior of these different methods. Recall that ridge regression shrinks all directions, but shrinks low-variance directions more. Principal components regression leaves M high-variance directions alone, and discards the rest. Interestingly, it can be shown that partial least squares also tends to shrink the low-variance directions, but can actually inflate some of the higher variance directions. This can make PLS a little unstable, and cause it to have slightly higher prediction error compared to ridge regression. A full study is given in Frank and Friedman (1993). These authors conclude that for minimizing prediction error, ridge regression is generally preferable to variable subset selection, principal components regression and partial least squares. However the improvement over the latter two methods was only slight.

To summarize, PLS, PCR and ridge regression tend to behave similarly. Ridge regression may be preferred because it shrinks smoothly, rather than in discrete steps. Lasso falls somewhere between ridge regression and best subset regression, and enjoys some of the properties of each.

Image /page/22/Figure/1 description: The image displays two plots, each illustrating the relationship between beta\_1 and beta\_2 coefficients for different regression methods. The top plot, labeled "rho = 0.5", shows the 'Least Squares' solution as a black dot at approximately (4, 2). The 'Best Subset' method is represented by a blue line forming a triangle with vertices at (0,0), (4,2), and (5,0). The 'Lasso' method is shown as a green line segment from (0,0) to approximately (3.5, 1.8). The 'Ridge' method is a red curve from (0,0) to (4,2). The 'PLS' method is an orange line segment from (0,0) to approximately (3, 2.5). The 'PCR' method is a purple line segment from (0,0) to approximately (2.5, 2.8). The bottom plot, labeled "rho = -0.5", shows a similar configuration but with different shapes and positions for the methods. The 'Least Squares' solution is again a black dot at approximately (4, 2). The 'Best Subset' is a blue line forming a triangle with vertices at (0,0), (4,2), and (5,0). The 'Lasso' is a green line segment from (0,0) to approximately (3.5, 1.8). The 'Ridge' is a red curve from (0,0) to (4,2). The 'PLS' is an orange line segment from (0,0) to approximately (3, 2.5). The 'PCR' is a purple line segment from (0,0) to approximately (2.5, 2.8). Both plots have beta\_1 on the x-axis and beta\_2 on the y-axis, with dashed vertical lines at x=0.

Image /page/22/Figure/2 description: The image shows the Greek letter rho followed by an equals sign and the number 0.5.

Image /page/22/Figure/3 description: The image shows the Greek letter rho followed by an equals sign and the number -0.5.

Image /page/22/Figure/4 description: This is a scatter plot showing the relationship between beta 1 on the x-axis and beta 2 on the y-axis. The plot displays several regression methods: Least Squares, Ridge, Lasso, Best Subset, PLS, and PCR. The Least Squares method is represented by a black dot at coordinates (4, 2). The Ridge regression is shown as a curved red line starting from (0,0) and extending towards the Least Squares point. The Lasso regression is depicted as a green line, also originating from (0,0) and moving towards the Least Squares point, but with a sharper angle. The Best Subset method is illustrated by a blue line segment connecting (0,0) to (3,0) and then to (4,2). The PLS method is shown as an orange line segment from (0,0) to (2.5, -0.5) and then to (4,2). The PCR method is represented by a purple line segment from (0,0) to (0.5, -1) and then to (4,2). The plot includes dashed lines at x=0 and y=0, indicating the axes, and tick marks for values from 0 to 6 on the x-axis and from -1 to 3 on the y-axis.

FIGURE 3.18. Coefficient profiles from different methods for a simple problem: two inputs with correlation  $\pm 0.5$ , and the true regression coefficients  $\beta = (4, 2)$ .
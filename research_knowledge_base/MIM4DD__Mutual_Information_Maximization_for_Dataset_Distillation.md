# MIM4DD: Mutual Information Maximization for Dataset Distillation

Yu<PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><PERSON><sup>2</sup>, <PERSON><sup>1</sup>\* <sup>1</sup>Department of Computer Science, Illinois Institute of Technology <sup>2</sup><NAME_EMAIL>, <EMAIL>, <EMAIL>

## Abstract

Dataset distillation (DD) aims to synthesize a small dataset whose test performance is comparable to a full dataset using the same model. State-of-the-art (SoTA) methods optimize synthetic datasets primarily by matching heuristic indicators extracted from two networks: one from real data and one from synthetic data (see Fig. [1,](#page-0-0) Left), such as gradients and training trajectories. DD is essentially a compression problem that emphasizes maximizing the preservation of information contained in the data. We argue that well-defined metrics which measure the amount of shared information between variables in information theory are necessary for success measurement but are never considered by previous works. Thus, we introduce mutual information (MI) as the metric to quantify the shared information between the synthetic and the real datasets, and devise MIM4DD numerically maximizing the MI via a newly designed optimizable objective within a contrastive learning framework to update the synthetic dataset. Specifically, we designate the samples in different datasets that share the same labels as positive pairs and *vice versa* negative pairs. Then we respectively pull and push those samples in positive and negative pairs into contrastive space via minimizing NCE loss. As a result, the targeted MI can be transformed into a lower bound represented by feature maps of samples, which is numerically feasible. Experiment results show that MIM4DD can be implemented as an add-on module to existing SoTA DD methods.

# 1 Introduction

Deep learning has remarkably successful perfor-mance in computer vision tasks [\[25,](#page-10-0) [6\]](#page-9-0), but most deep-learning-based methods require enormous amounts of data followed by extensive training resources. For example, in order to train a CLIP model [\[31\]](#page-10-1) in a self-supervised manner [\[8\]](#page-9-1), a highresolution dataset with more than 10 million images are collected for training, which consumes tens of hundreds of GPU hours.

A straightforward solution to eliminate the reliance on training DNNs on data is to construct small training sets. Before the deep learning era, coreset or subset selection is the most prevalent paradigm, in which one can obtain a subset of salient data points to represent the original dataset of interest  $[1, 12, 34]$  $[1, 12, 34]$  $[1, 12, 34]$  $[1, 12, 34]$  $[1, 12, 34]$ . In the era of deep learning, in contrast to

<span id="page-0-0"></span>Image /page/0/Figure/7 description: This image displays two distinct diagrams illustrating different approaches to optimizing datasets. The left diagram shows a network processing a 'Real Dataset' and a 'Synthetic Dataset' through multiple layers, calculating a 'Matching Loss' and a final 'Loss'. Both forward and backward propagation are indicated. The right diagram focuses on optimizing a 'Synthetic Dataset' by comparing its distribution to the distribution of a 'Real Dataset' using 'Mutual Information'. Both diagrams show an 'Optimize' step influencing the loss or mutual information calculation, with dashed red arrows indicating backward propagation.

Figure 1: (Left) General framework of previous SoTA DD methods: matching heuristic indicators extracted by networks from real and synthetic datasets; (Right) the motivation of MIM4DD: maximizing the mutual information between two datasets for optimizing the synthetic dataset.

<sup>∗</sup>Corresponding author

<sup>37</sup>th Conference on Neural Information Processing Systems (NeurIPS 2023).

previously mentioned selection-based methods, Dataset Distillation (DD), also known as Dataset Condensation [\[41,](#page-10-3) [46,](#page-10-4) [40,](#page-10-5) [5\]](#page-9-4), offers a revolutionary paradigm to synthesize a small dataset using gradient descent algorithms [\[33\]](#page-10-6), as shown in Fig[.1](#page-0-0) (Left). In DD, the key question is how to define metrics to measure the distance between synthetic and real datasets. Only by optimizing a well designed distance metric can gradient descent be properly applied to update synthetic data. To answer this question, researchers design several distance algorithms. For example, Zhao *et al.* [\[46\]](#page-10-4) measure distance between the batch gradients of the synthetic samples and original ones; Wang *et al.* [\[40\]](#page-10-5) use the similarity between the feature maps extracted by networks on different datasets; Cazenavette *et al.* [\[5\]](#page-9-4) resort to the MSE between the training trajectories of networks on real and synthetic datasets.

*Dataset distillation task is essentially a compression problem with a strong emphasis on maximizing the preservation of information contained in the data* [\[41,](#page-10-3) [19,](#page-9-5) [11\]](#page-9-6). Admittedly, previous heuristicdesigned distance metrics have achieved promising performance. However, we argue that previous works neglect the well-defined distributional metric in information theory, which is necessary for success measurement. Specifically, if we define a dataset's samples as variables, it is imperative that the high-level distributional properties of these variables from synthetic and real datasets (*e.g.*, correlations and dependencies between two datasets) should be captured and utilized to guide the update of synthetic datasets. Motivated by this notion, we introduce Mutual Information (MI), a well-formulated metric in information theory for dataset distillation. In detail, MI quantifies the information amount shared by the real and synthetic datasets. In contrast to the aforementioned works focusing on aligning the indicators extracted by different neural networks, MI can naturally capture non-linear statistical dependencies between variables and be used as a measure of true dependence, which is important information in data compression [\[4,](#page-9-7) [39,](#page-10-7) [36\]](#page-10-8).

Based on MI metric, we propose a novel method, termed Mutual Information Maximization for Dataset Distillation (MIM4DD). In particular, we first formulate DD as a problem of MI maximization between two datasets. Then, we derive a numerically feasible lower bound and maximize this lower bound via contrastive learning [\[15,](#page-9-8) [18,](#page-9-9) [8\]](#page-9-1). Finally, we design a highly effective optimization strategy for the dataset distillation task using contrastive estimation for MI maximization. In this way, contrastive learning theoretically leads to the targeted MI maximization and also contributes to the inter-class decorrelation of synthetic samples. In other words, MIM4DD is built upon a contrastive learning framework to synthesize the small dataset, where samples from the synthetic dataset are pulled closer to the counterparts with the identical label in the real dataset, and pulled further away from the ones with different labels in the contrastive space. To the best of our knowledge, it is the first work aiming at MIM of the datasets over the DD task within a contrastive learning framework.

Overall, the contributions of this paper are three-fold: (i) To distill information from a large real dataset to a small synthetic dataset under a well-defined metric, we formulate the DD task as an MI maximization problem. To the best of our knowledge, this is the first work to introduce MI into the literature of DD. (ii) To maximize the targeted MI, we derive a numerically feasible lower bound and maximize it via contrastive learning. In this way, the heterogeneity in the generated images gets enhanced, which further improves the performance of the DD method. (iii) Experimental results show that our method outperforms existing SoTA methods. Importantly, our method can be implemented as a plug-and-play module for existing methods.

# 2 Methodology

In this section, we demonstrate the methodology of Mutual Information Maximization for Dataset Distillation (MIM4DD). Firstly, we briefly revisit the general framework in previous SoTA DD methods, and we illustrate the MI background. Secondly, we model the DD problem within the literature on mutual information maximization (MIM). Then, we convert the targeted but numerical inaccessible MIM goal into a learnable object optimization problem, and solve the problem via a newly designed MIM4DD loss. Finally, we discuss the potential insights of MIM4DD. Note that we only elaborate on the key derivations in this section due to the space limitation; detailed discussions and technical theorems can be found in the supplemental material.

## 2.1 Preliminaries

Dataset Distillation. In short, the goal of dataset distillation (also dubbed dataset condensation) is to synthesize a small training set,  $\bar{\mathcal{D}}_{syn} = {\{x_{syn}^j, y^j\}}_{j=1}^M$  such that models trained on this synthetic

dataset can have comparable performance as models (with the same architecture) trained on the large real set,  $\mathcal{D}_{real} = {\mathbf{x}_{real}^i, \mathbf{y}^i\}_{i=1}^N$ , in which  $M \ll N$ . In this way, after obtaining the targeted synthetic data, the training process of DNNs can be largely accelerated. In this premise, we review some representative DD methods [\[43,](#page-10-9) [5,](#page-9-4) [40\]](#page-10-5), which generally propose to enforce the behaviors of models trained on real and synthetic datasets to be similar. The core idea can be formalized in a form of an optimization problem:

$$
\mathcal{D}_{syn}^{\star} = \arg\min_{\mathcal{D}_{syn}} \mathbb{E}_{\mathbf{x}_{syn} \sim \mathcal{D}_{syn}} \mathbb{E}_{\mathbf{x}_r \sim \mathcal{D}_{real}} \text{Dist}(f(\mathbf{x}_{real}; \theta^{\star}), f(\mathbf{x}_{syn}; \gamma^{\star})), \tag{2.1}
$$

in which  $\theta^*$  and  $\gamma^*$  are the learned parameters of networks trained on the real dataset  $\mathcal{D}_{real}$  and the synthetic dataset  $\mathcal{D}_{syn}$ , respectively; and the distance function  $Dist(\cdot, \cdot)$  is specifically developed to measure the similarity of two networks across datasets. With aligning the networks, one can optimize the synthetic data. Note that designing the distance functions aligning the networks trained on different datasets to optimize the synthetic dataset is the core of previous DD methods, *e.g.*, CAFE [\[40\]](#page-10-5) uses the MSE distance of feature maps, and MTT [\[5\]](#page-9-4) calculates the difference of training trajectories as distance functions.

Although those heuristically designed distance functions lead to promising results, we argue that well-defined metrics in information theory for measuring the amount of shared information between variables have never been considered, as DD is essentially a compression problem with a different emphasis on the information contained in the data.

Mutual Information and Contrastive Learning. Mutual information quantifies the amount of information obtained about one random variable by observing the other random variable. It is a dimensionless quantity with (generally) units of bits, and can be considered as the reduction in uncertainty about one random variable given knowledge of another. High mutual information indicates a large reduction in uncertainty and *vice versa* [\[23\]](#page-9-10). Strictly, for two discrete variables X and  $Y$ , their mutual information (MI) can be defined as [\[23\]](#page-9-10):

<span id="page-2-0"></span>
$$
I(\mathbf{X}, \mathbf{Y}) = \sum_{x, y} P_{\mathbf{X}\mathbf{Y}}(x, y) \log \frac{P_{\mathbf{X}\mathbf{Y}}(x, y)}{P_{\mathbf{X}}(x) P_{\mathbf{Y}}(y)},
$$
(2.2)

where  $P_{XY}(x, y)$  is the joint distribution,  $P_X(x) = \sum_y P_{XY}(x, y)$  and  $P_Y(y) = \sum_x P_{XY}(x, y)$ are the marginals of  $X$  and  $Y$ , respectively.

In the content of DD, we would like them to share as much information as possible. Theoretically, considering the samples in real and synthetic datasets as two random variables, the MI between those two variables should be maximized. Recently, contrastive learning has been proven an effective approach to maximize MI. Many methods based on contrastive loss for self-supervised learning are proposed, such as [\[18\]](#page-9-9), [\[29\]](#page-10-10), [\[42\]](#page-10-11). These methods are theoretically based on NCE [\[15\]](#page-9-8) and InfoNCE [\[18\]](#page-9-9). Essentially, the key concept of contrastive learning is to pull representations in positive pairs close and push representations in negative pairs apart in a contrastive space. Thus the major obstacle for modeling problems in a contrastive way is to define the negative and positive pairs. In this work, we also resort to a contrastive learning framework for maximizing our targeted MI. Meanwhile, we illustrate how we formulate DD as a MI maximization problem, and how we solve this targeted problem within the contrastive learning framework.

<span id="page-2-2"></span>

### 2.2 MIM4DD: Mutual Information Maximization for Dataset Distillation

In this section, we first formalize the idea of maximizing the MI between the distributions of real and synthesized data, via constructing a contrastive task based on Noise-Contrastive Estimation (NCE). Specifically, we derive a novel MIM4DD loss to distill task-specific information from real data  $\mathcal{D}_{real}$ to synthetic data  $\mathcal{D}_{syn}$ , where NCE is introduced to avoid the direct MI computation by estimating it with its lower bound in Eq[.2.14.](#page-5-0) From the perspective of NCE, straight-forwardly, the real and synthesized samples from the same class can be pulled close, and samples from different classes can be pushed away, which corresponds to the core idea of contrastive learning.

**Problem Formulation:** Ideally, for variable  $X_{real}$  representing the samples in real data and  $X_{syn}$  in the synthetic data, we desire to maximize the MI between  $\mathbf{X}_{real}$  and  $\mathbf{X}_{syn}$  in terms of  $\mathbf{X}_{syn}$ , *i.e.*,

<span id="page-2-1"></span>Targeted MI: 
$$
\mathbf{X}_{syn}^{*} = \arg \max_{\mathbf{X}_{syn}} I(\mathbf{X}_{real}, \mathbf{X}_{syn}).
$$
 (2.3)

In this way, synthetic data  $\mathcal{D}_{syn}^{\star}$  and fixed real data  $\mathcal{D}_{real}^{\star}$  can share maximal information. However, directly approaching this goal is unattainable since the distributions of datasets themselves are absurd to estimate [\[15,](#page-9-8) [18\]](#page-9-9). To encounter this obstacle, let us include the networks trained on real and synthetic datasets. We define those networks in the form of K-layer Multi-Layer Perceptrons (MLPs). For simplification, we discard the bias term of those MLPs. Then the network  $f(x)$  can be denoted as:

<span id="page-3-2"></span>
$$
f(\mathbf{W}^1, \cdots, \mathbf{W}^K; \mathbf{x}) = (\mathbf{W}^K \cdot \boldsymbol{\sigma} \cdot \mathbf{W}^{K-1} \cdot \cdots \cdot \boldsymbol{\sigma} \cdot \mathbf{W}^1)(\mathbf{x}),
$$
(2.4)

where x is the input sample and  $\mathbf{W}^k : \mathbb{R}^{d_{k-1}} \longmapsto \mathbb{R}^{d_k} (k = 1, ..., K)$  stands for the weight matrix connecting the  $(k-1)$ -th and the k-th layer, with  $d_{k-1}$  and  $d_k$  representing the sizes of the input and output of the k-th network layer, respectively. The  $\sigma(\cdot)$  function performs element-wise activation operation on the input feature maps. Based on those predefined notions, the sectional MLP  $f^k(\mathbf{x})$ with the front k layers of the  $f(\mathbf{x})$  can be represented as:

<span id="page-3-3"></span>
$$
f^{k}(\mathbf{W}^{1},\cdots,\mathbf{W}^{k};\mathbf{x})=(\mathbf{W}^{k}\cdot\boldsymbol{\sigma}\cdots\boldsymbol{\sigma}\cdot\mathbf{W}^{1})(\mathbf{x}).
$$
\n(2.5)

The k-th layer's feature maps are  $\mathbf{A}^k_{syn}$  and  $\mathbf{A}^k_{real}$ ,  $(k \in \{1, \cdots, K\})$ , where  $\mathbf{A}^k_{syn}$  =  $(\mathbf{a}_{syn}^{k,1},\cdots,\mathbf{a}_{syn}^{k,M})$  and  $\mathbf{A}_{real}^k = (\mathbf{a}_{real}^{k,1},\cdots,\mathbf{a}_{real}^{k,N})$  can be considered as a series of variables. Specifically, the feature map can be obtained by:

$$
\mathbf{a}_{syn}^{k,j_{c_r}} = f^k(\mathbf{x}_{syn}^j), \ j \in \{1, \cdots, M\}, \quad \mathbf{a}_{real}^{k,i_{c_s}} = f^k(\mathbf{x}_{real}^i), \ i \in \{1, \cdots, N\}.
$$
 (2.6)

Here, we show the relationship of MI in data X level and in feature maps A level, *i.e.*, the relationship between  $I(\mathbf{X}_{real}, \mathbf{X}_{syn})$  and  $I(\mathbf{A}_{real}, \mathbf{A}_{syn})$ . We utilize the in-variance of MI to understand it. The property can be illustrated as follows:

Theorem 1 (In-variance of Mutual Information): Mutual information is invariant under the reparametrization of the marginal variables. If  $X' = F(X)$  and  $Y' = G(Y)$  are homeomorphisms  $(i.e., F(\cdot)$  and  $G(\cdot)$  are smooth uniquely invertible maps), then

$$
I(X,Y) = I(X',Y').
$$
\n
$$
(2.7)
$$

Since each layer's mapping  $\mathbf{W}^k : \mathbb{R}^{d_{k-1}} \longmapsto \mathbb{R}^{d_k}$   $(k = 1, ..., K)$  can be considered as the smooth uniquely invertible maps **Theorem 1.** Combining this theorem with the definition of MI in Eq[.2.2,](#page-2-0) we observe that the MI in the targeted data level is equivalent to MI in the feature maps level, *i.e.*,

<span id="page-3-0"></span>
$$
I(\mathbf{X}_{real}, \mathbf{X}_{syn}) = I(\mathbf{A}_{real}^k, \mathbf{A}_{syn}^k), (k = 1, ..., K).
$$
 (2.8)

More details and the proof of this theorem are in Supplemental Materials and [\[21\]](#page-9-11).

Facilitated by this property, we are able to access the calculation of MI between real and synthetic datasets via the feature maps of networks trained on those datasets. In other words, Theorem 1 helps us transfer the targeted MI maximization in Eq[.2.3](#page-2-1) to a reachable MI in Eq[.2.8](#page-3-0) in the dataset distillation literature, *i.e.*,

<span id="page-3-1"></span>Accessible MI: 
$$
\arg \max_{\mathbf{X}_{syn}} \sum_{k=1}^{K} I(\mathbf{A}_{real}^k, \mathbf{A}_{syn}^k),
$$
 (2.9)

in which  $\mathbf{A}^k_{syn} = f^k(\mathbf{X}_{syn})$  and  $\mathbf{A}^k_{real} = f^k(\mathbf{X}_{real})$ .

Apart from the theoretical derivation, intuitively, the corresponding variables  $(a_{syn}^k, a_{real}^k)$  should share more information, *i.e.*, MI of the same layer's output feature maps  $I(\mathbf{a}_{syn}^k, \mathbf{a}_{real}^k)$  ( $k \in$  $\{1, \dots, K\}$  should be maximized to enforce them mutually dependent. This motivation has also been testified from the perspective of KD [\[17,](#page-9-12) [13\]](#page-9-13) by CRD [\[39\]](#page-10-7) and WCoRD [\[7\]](#page-9-14). In those methods, the penultimate layer's feature maps of teacher and student are aligned in a contrastive learning manner to enhance the heterogeneity of representations, which can also be explained via MI maximization. However, MIM4DD is different from those methods (details is in Related Work).

To optimize the accessible Mutual Information (MI) as defined in Eq[.2.9,](#page-3-1) we incorporate a contrastive learning framework into our targeted Dataset Distillation (DD) task. The fundamental principle of contrastive learning involves comparing varying perspectives of the data, typically under different data augmentations, to compute similarity scores [\[29,](#page-10-10) [18,](#page-9-9) [2,](#page-9-15) [16,](#page-9-16) [8\]](#page-9-1). This framework is suitable for our case, since the activations from real and synthetic datasets can be seen as two different views.

<span id="page-4-1"></span>Image /page/4/Figure/0 description: This diagram illustrates a machine learning process involving synthetic and real data. On the left, synthetic data, represented by a blurry image of a bird and a dog, is fed into a network. This network processes the data, leading to an update. The processed data then enters a central sphere labeled 'Contrastive Space', where 'Push' and 'Pull' operations are indicated by arrows and text, showing interactions between data points represented by colored shapes (blue triangle, green star, orange star, purple triangle). The 'Forward Pass' and 'Backward Pass' are indicated by arrows. On the right, real data, consisting of grids of bird images and dog images, is processed by another network. The output of this network is shown as 'Representations', depicted by colored vertical bars. The overall diagram suggests a method for learning representations by contrasting synthetic and real data within a contrastive space.

Figure 2: Feeding images from two datasets into two corresponding neural networks, and we obtain the three pairs of representations for each layer. We embed the representations into a contrastive space, then learn from the pair correlation with the contrastive learning task in Eq.  $2.16$ . In this way, the heterogeneity in the generated images can be enhanced. Theoretically, we can realize the formulated mutual information maximization (MIM) in Eq. [2.9,](#page-3-1) which is equivalent to the targeted MIM in Eq. [2.3.](#page-2-1)

(Definition: Positive and Negative Samples.) Let's denote each sample from the real and synthetic datasets as  $\{ \mathbf x_{real}^{j_{c_r}} \}$   $(j_{c_r} \in \{1, \cdots, N\})$  and  $\{ \mathbf x_{syn}^{i_{c_s}} \}$   $(i_{c_s} \in \{1, \cdots, M\})$ , respectively. Here,  $c_r$  and  $c_s \in \{1, \dots, C\}$  represent the class labels of  $\mathbf{x}_{real}^{j_{c_r}}$  and  $\mathbf{x}_{syn}^{i_{c_s}}$ , respectively. We feed two batches of samples from two datasets to two different neural networks and obtain  $N \cdot M$  pairs of k-th layer's activations  $(a_{syn}^{k,i_{cs}}, a_{real}^{k,j_{cr}})$ , which can further be utilized for the contrastive learning task. We define a pair containing two activations corresponding to the same labels as the positive pair, *i.e.*, if  $c_r = c_s$ ,  $(\mathbf{a}_{syn}^{k,i_{c}} , \mathbf{a}_{real}^{k,j_{c}})$  + and *vice versa*. Consequently, there is  $\frac{M}{C} \cdot \frac{M}{C} \cdot C = \frac{1}{C} \cdot N \cdot M$  positive pairs, and thus  $(1 - \frac{1}{C}) \cdot N \cdot M$  negative pairs. The key concept of contrastive learning is to discriminate whether a given pair of activation  $(\mathbf{a}_{syn}^{k,i_{c_s}}, \mathbf{a}_{real}^{k,j_{c_r}})$  is positive or negative. In other words, it involves estimate the distribution  $P(D \mid \mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}})$ , in which D is a scalar variable indicating whether  $c_s = c_r$  or  $c_s \neq c_r$ . Specifically,  $D = 1$  when  $c_s = c_r$ , and  $D = 0$  when  $c_s \neq c_r$ . However, we can not directly reach the distribution  $P(D \mid \mathbf{a}_{syn}^{k,i_{c_s}}, \mathbf{a}_{real}^{k,j_{c_r}})$  [\[15\]](#page-9-8), and thus we introduce its corresponding variational form:

<span id="page-4-2"></span>
$$
q(D \mid \mathbf{a}_{real}^{k,j_{cr}}, \mathbf{a}_{syn}^{k,i_{cs}}). \tag{2.10}
$$

Intuitively,  $q(D \mid \mathbf{a}_{real}^{k,j_{c_r}}, \mathbf{a}_{syn}^{k,i_{c_s}})$  can be treated as a binary classifier, which can classify a given pair  $(a_{syn}^{k,i_{c_s}}, a_{real}^{k,j_{c_r}})$  into positive or negative. Importantly,  $q(D \mid a_{real}^{k,i_{c_s}}, a_{syn}^{k,j_{c_r}})$  can be estimated by some mature statistical methods, such as NCE [\[15\]](#page-9-8) and InfoNCE [\[18\]](#page-9-9).

Using the Bayes rule, the posterior probability of two activations from the positive pair can be formalized as:

$$
q(D = 1 \mid \mathbf{a}_{syn}^{k, i_{c}} , \mathbf{a}_{real}^{k, j_{c}}) = \frac{q(\mathbf{a}_{syn}^{k, i_{c}} , \mathbf{a}_{real}^{k, j_{c}} \mid D = 1) \frac{1}{C}}{q(\mathbf{a}_{syn}^{k, i_{c}} , \mathbf{a}_{real}^{k, j_{c}} \mid D = 1) \frac{1}{C} + q(\mathbf{a}_{syn}^{k, i_{c}} , \mathbf{a}_{real}^{k, j_{c}} \mid D = 0) \frac{C-1}{C}}.
$$
 (2.11)

The probability of activations from negative pair is  $q(D = 0 \mid \mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,i_{cr}}) = 1 - q(D = 1 \mid \mathbf{a}_{syn}^{k,i_{cs}})$  $\mathbf{a}_{syn}^{k,i_{c_s}}, \mathbf{a}_{real}^{k,j_{c_r}}$ ). To simplify the NCE derivative, several works [\[15,](#page-9-8) [42,](#page-10-11) [39\]](#page-10-7) build assumption about the dependence of the variables, we also use the assumption that the activations from positive pairs are dependent and the ones from negative pairs are independent, *i.e.*  $q(\mathbf{a}_{syn}^{k,i_{c_s}}, \mathbf{a}_{real}^{k,j_{c_r}} \mid D = 1)$  $P(\mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}})$  and  $q(\mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}} \mid D = 0) = P(\mathbf{a}_{syn}^{k,i_{cs}})P(\mathbf{a}_{real}^{k,j_{cr}})$ . Hence, the above equation can be simplified as:

<span id="page-4-0"></span>
$$
q(D = 1 | \mathbf{a}_{syn}^{k, i_{c_s}}, \mathbf{a}_{real}^{k, j_{c_r}}) = \frac{P(\mathbf{a}_{syn}^{k, i_{c_s}}, \mathbf{a}_{real}^{k, j_{c_r}})}{P(\mathbf{a}_{syn}^{k, i_{c_s}}, \mathbf{a}_{real}^{k, j_{c_r}}) + P(\mathbf{a}_{syn}^{k, i_{c_s}})P(\mathbf{a}_{real}^{k, j_{c_r}})(C - 1)}.
$$
(2.12)

Performing logarithm to Eq[.2.12](#page-4-0) and arranging the terms, we can achieve

<span id="page-4-3"></span>
$$
\log q(D = 1 \mid \mathbf{a}_{syn}^{k, i_{c_s}}, \mathbf{a}_{real}^{k, j_{c_r}}) \le \log \frac{P(\mathbf{a}_{syn}^{k, i_{c_s}}, \mathbf{a}_{real}^{k, j_{c_r}})}{P(\mathbf{a}_{syn}^{k, i_{c_s}}) P(\mathbf{a}_{real}^{k, j_{c_r}})} - \log(C - 1). \tag{2.13}
$$

Taking expectation of  $P(\mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}})$  on both sides, and combining Eq[.2.2,](#page-2-0) we can transfer the MI into:

<span id="page-5-0"></span>
$$
\overbrace{\int (\mathbf{a}_{syn}^k, \mathbf{a}_{real}^k)}^{\text{ofmissible MI in Eq. 2.9}} \geq \log(C-1) + \mathbb{E}_{P(\mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}} | D=1)} \left[ \log q(D=1 \mid \mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}}) \right], \quad (2.14)
$$

where  $I(\mathbf{a}_{syn}^k, \mathbf{a}_{real}^k)$  is the MI between the real and synthetic data distributions. Instead of directly maximizing the MI, maximizing the lower bound in the Eq[.2.14](#page-5-0) is a practical solution.

However, even  $q(D=1 \mid \mathbf{a}^{k,i_{c_s}}, \mathbf{a}^{k,j_{c_r}})$  is still hard to estimate. Thus, as tackled by many contrastive learning works [\[29,](#page-10-10) [18,](#page-9-9) [2,](#page-9-15) [39,](#page-10-7) [36,](#page-10-8) [37\]](#page-10-12), we introduce a discriminator network  $d(\cdot, \cdot)$  with parameter  $\phi$  $(i.e., d(\mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}}; \phi))$ . Basically, the discriminator d can map  $\mathbf{a}_{syn}^k, \mathbf{a}_{real}^k$  to  $[0, 1]$  (*i.e.*, distinguish given two samples  $\mathbf{a}_{syn}^k$ ,  $\mathbf{a}_{real}^k$  belonging to positive or negative pair). Specifically, the discriminator function is designed as follows:

<span id="page-5-4"></span>
$$
d(\mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}}) = \exp(\frac{< g_{\phi}(\mathbf{a}_{syn}^{k,i_{cs}}), g_{\phi}(\mathbf{a}_{real}^{k,j_{cr}}) >}{\tau})/C,\tag{2.15}
$$

in which  $g_{\phi}(\cdot)$  is the embedding function for mapping the activations into the contrastive space and  $C = \exp(\frac{\langle g(\mathbf{a}_{syn}^{k, i_{cs}}), g(\mathbf{a}_{real}^{k, j_{cs}}) \rangle}{\pi} + 1, \text{ and } \tau \text{ is a temperature parameter that controls the concentration}$ level of the distribution [\[17,](#page-9-12) [39\]](#page-10-7).

**Loss Function.** We define the contrastive loss function  $\mathcal{L}_{NCE}^{k}$  between the k-th layer's activations  $\mathbf{a}^k_{syn}$  and  $\mathbf{a}^k_{real}$  as:  $\mathcal{L}^k_{NCE}$  =

<span id="page-5-1"></span>
$$
\mathbb{E}_{q(\mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}}|D=1)} \left[ \log h(\mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}}) \right] + (C-1) \cdot \mathbb{E}_{q(\mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}}|D=0)} \left[ \log(1 - h(\mathbf{a}_{syn}^{k,i_{cs}}, \mathbf{a}_{real}^{k,j_{cr}})) \right].
$$
\n(2.16)

In the view of contrastive learning, the first term in the above loss function about positive pairs is optimized for capturing more intra-class correlations and the second term of negative pairs is for interclass decorrelation. Because we construct the pairs instance-wisely, the number of negative pairs can be the size of the entire real dataset, *e.g.*, 50K for CIFAR [\[22\]](#page-9-17). By incorporating hand-crafted, additional contrastive pairs for the proxy optimization problem in Eq[.2.16,](#page-5-1) the representational quality of generated images can be further enhanced as demonstrated by numerous contrastive learning methods[\[8,](#page-9-1) [29,](#page-10-10) [18,](#page-9-9) [2\]](#page-9-15).

<span id="page-5-3"></span>Image /page/5/Figure/9 description: The image displays two line graphs side-by-side, each plotting accuracy against a parameter lambda in the overall loss function (Eq. 17). The left graph, labeled "Accuracy on CIFAR10", shows accuracy values ranging from approximately 64.6 to 66.4. The data points are approximately at lambda values of 0.0, 0.1, 0.2, 0.5, 1.0, 1.5, and 3.0. The accuracy generally increases from lambda 0.0 to 0.5, peaking around 66.3, and then decreases to about 65.3 at lambda 3.0. Error bars are present for each data point. The right graph, labeled "Accuracy on CIFAR100", shows accuracy values ranging from approximately 39.6 to 41.2. The data points are at similar lambda values. The accuracy increases from lambda 0.0 to 0.5, peaking around 40.9, then slightly decreases and increases again, reaching a peak around 41.1 at lambda 1.5, and then decreases to about 40.5 at lambda 3.0. Error bars are also present for these data points. The number (2.16) is displayed in the top right corner of the image.

Figure 3: Ablation Studies. Effect of  $\lambda$  in  $\mathcal{L}_{\text{MIM4DD}}$ (Eq[.2.17\)](#page-5-2) on CIFAR10 (Left) and CIFAR100 (Right).  $\lambda = 0$  is the baseline, MTT [\[5\]](#page-9-4).

Finally, by incorporating the set of NCE loss for various layers  $\{\mathcal{L}_{NCE}^k\}$  ,  $(k = 1, \cdots, K)$ , we can then formulate MIM4DD loss  $\mathcal{L}_{\text{MIM4DD}}$  as:

<span id="page-5-2"></span>
$$
\mathcal{L}_{\text{MIM4DD}} = \lambda \sum_{k=1}^{K} \frac{\mathcal{L}_{NCE}^{k}}{\beta^{K-1-k}} + \mathcal{L}_{DD},\tag{2.17}
$$

in which  $\mathcal{L}_{DD}$  can be any loss functions in previous DD methods [\[40,](#page-10-5) [43,](#page-10-9) [5\]](#page-9-4),  $\lambda$  is used to control the NCE loss, and  $\beta$  is a scalar greater than 1. In practice, we use MTT [\[5\]](#page-9-4) or BPTT [\[11\]](#page-9-6) as default  $\mathcal{L}_{DD}$ .

### 2.3 Discussion on MIM4DD

In addition to the theoretical formulation, we are going to provide a more straightforward manifestation of MIM4DD. As illustrated in Fig[.2,](#page-4-1) by embedding the activations to the contrastive space and constructing proper negative-and-positive embedding pairs, we can heterogeneously distill the relevant information from the real dataset to learnable synthetic dataset, and enhance the heterogeneity of the synthetic dataset within the contrastive learning framework as shown in Fig[.5.](#page-8-0) Specifically, networks on different datasets first learn self-supervised information benefited from a large number of the designed pairs [\[18,](#page-9-9) [39\]](#page-10-7). Then, via the backward pass in Fig[.2,](#page-4-1) the synthetic data are updated

w.r.t. this contrastive loss. While the actual number of negative samples in practice can be huge, *e.g.*, 50K for synthesizing 10 pictures per-class to replace CIFAR10 [\[22\]](#page-9-17) despite only drawing two samples in Fig[.2.](#page-4-1)

Furthermore, we give a direct explanation of *why optimizing*  $\mathcal{L}_{MIMADD}$  *in* Eq[.2.17](#page-5-2) *can lead to the targeted MIM* between synthetic dataset and real dataset in Eq[.2.3.](#page-2-1) Firstly, optimizing the formulated contrastive loss in Eq[.2.16](#page-5-1) is equivalent to maximizing the derived accessible MI between activations in Eq[.2.9,](#page-3-1) as the inequality in Eq[.2.14](#page-5-0) (Eq[.2.10](#page-4-2)[-2.13](#page-4-3) can derive Eq[.2.14\)](#page-5-0). Secondly, based on Theorem 1 (Eq[.A.1\)](#page-11-0) and the property of the networks itself (Eq[.2.4](#page-3-2) and Eq[.2.5\)](#page-3-3), maximizing the accessible MI in Eq[.2.9](#page-3-1) equals to maximizing the targeted MI in Eq[.2.8,](#page-3-0) which is our goal, to minimize the MI between synthetic dataset and real dataset. Since real data are fixed and synthetic data are learnable, the optimized synthetic dataset can contain as much information as the real dataset under the metric of MI.

## 3 Experiments

In this section, we conduct comprehensive experiments to evaluate our proposed method MIM4DD on four different datasets for DD task. We first describe the implementation details of MIM4DD, and then compare our method with several SoTA DD methods to demonstrate superiority of our proposed method. Finally, we validate the effectiveness of MI module (connected with Eq[.2.16](#page-5-1) and Eq[.2.17\)](#page-5-2) by a series of ablation studies.

### 3.1 Datasets and Implementation Details

Datasets. We use MNIST [\[24\]](#page-9-18), SVHN [\[35\]](#page-10-13), and CIFAR10/100 datasets to conduct our experiments. **MNIST** [\[24\]](#page-9-18) is a dataset for handwritten digits recognition that is widely used for validating image recognition models. It contains 60,000 training images and 10,000 testing images with the size of  $28 \times 28$ . CIFAR10/100 [\[22\]](#page-9-17) are two datasets consist of tiny colored natural images with the size of  $32 \times 32$  from 10 and 100 categories, respectively. In each dataset, 50,000 images are used for training and 10,000 images for testing. More details of the datasets can be found in *Supplemental Materials*.

Implementation Details. In the experiments, we optimize synthetic sets with 1/10/50 Images Per Class (IPC) across all three datasets, using a three-layer Convolutional Network (ConvNet) identical to those used in [\[46,](#page-10-4) [40,](#page-10-5) [5\]](#page-9-4). The ConvNet comprises three consecutive blocks of 'Conv-InstNorm-ReLU-AvgPool.' Each convolutional layer has 128 channels, and AvgPool represents a  $2 \times 2$  average pooling operation with stride 2. The synthetic images' initial learning rate is 0.1, which is halved at the 1,800th and 2,800th iterations. The training is stopped after 5,000 iterations. To test the ConvNet's performance on the synthetic dataset, we train the network on synthetic sets for 300 epochs and assess the performance using five randomly initialized networks. The network's initial learning rate is 0.01. As per [\[5\]](#page-9-4), we conduct five experiments and report the mean and standard deviation across the five networks. The default batch size is 256, and  $\lambda$  in Eq[.2.17](#page-5-2) is 0.8. The effect of  $\lambda$  is explored in Sec[.3.3.](#page-6-0)

### 3.2 Comparison with SoTA

We compare MIM4DD with a series of state-of-the-art (SoTA) dataset distillation methods, including Dataset Distillation (DD) [\[41\]](#page-10-3), LD [\[3\]](#page-9-19), Dataset Condensation (DC) [\[46\]](#page-10-4), DC with differentiable siamese augmentation (DSA) [\[43\]](#page-10-9), DC with distribution matching (DM) [\[45\]](#page-10-14), CAFE [\[40\]](#page-10-5), FRePo [\[47\]](#page-10-15), TESLA [\[10\]](#page-9-20), BPTT [\[11\]](#page-9-6), and MTT [\[5\]](#page-9-4). We report the performances of our method and comparisons on three datasets in Table [1.](#page-7-0) Taking into account the overall performance of MIM4DD across these mainstream dataset distillation benchmarks, it's apparent that our method consistently outperforms existing SoTAs. For instance, in the setting of generating 10 images per class, our method delivers top-tier results across all datasets. Additionally, when we synthesize 10 images per class using CIFAR100 as a real-world dataset, our method surpasses MTT by a margin of 1.4%. Importantly, our method can serve as an effective plug-and-play module for existing state-of-the-art DD methods.

<span id="page-6-0"></span>

### 3.3 Ablation Studies

|                   | <b>MNIST</b>        |                     | <b>CIFAR10</b>      |                     |                     | <b>CIFAR100</b>     |                     |                     |
|-------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|
|                   | $IPC-1$             | $IPC-10$            | $IPC-50$            | $IPC-1$             | $IPC-10$            | $IPC-50$            | $IPC-1$             | $IPC-10$            |
| Full Set          | $99.6 \pm 0.0$      |                     | $84.8 \pm 0.1$      |                     |                     | $56.2 \pm 0.3$      |                     |                     |
| DD[41]            |                     | $79.5 \pm 8.1$      | $\sim$              |                     | $36.8 \pm 1.2$      |                     |                     |                     |
| LD[3]             | $60.9 \pm 3.2$      | $87.3 \pm 0.7$      | $93.3 \pm 0.3$      | $25.7 \pm 0.7$      | $38.3 \pm 0.4$      | $42.5 \pm 0.4$      | $11.5 \pm 0.4$      |                     |
| <b>CAFE</b> [40]  | $93.1 \pm 0.3$      | $97.2 \pm 0.2$      | $98.6 \pm 0.2$      | $30.3 \pm 1.1$      | $46.3 \pm 0.6$      | $55.5 \pm 0.6$      | $14.0 \pm 0.3$      | $31.5 \pm 0.2$      |
| DM [44]           | $89.7 \pm 0.6$      | $97.5 \pm 0.1$      | $98.6 \pm 0.1$      | $26.0 \pm 0.8$      | $48.9 \pm 0.6$      | $63.0 \pm 0.4$      | $11.4 \pm 0.3$      | $29.7 \pm 0.3$      |
| <b>DSA</b> [43]   | $88.7 \pm 0.6$      | $97.8 \pm 0.1$      | $99.2 \pm 0.1$      | $28.8 \pm 0.7$      | $52.1 \pm 0.5$      | $60.6 \pm 0.5$      | $16.8 \pm 0.2$      | $32.3 \pm 0.3$      |
| DC[46]            | $91.7 \pm 0.5$      | $97.4 \pm 0.2$      | $98.9 \pm 0.2$      | $28.3 \pm 0.5$      | $44.9 \pm 0.5$      | $53.9 \pm 0.5$      | $12.8 \pm 0.3$      | $25.2 \pm 0.3$      |
| <b>DCC</b> [26]   |                     |                     |                     | $32.9 \pm 0.8$      | $49.4 \pm 0.5$      | $61.6 \pm 0.4$      | $13.3 \pm 0.3$      | $30.6 \pm 0.4$      |
| <b>DSAC</b> [26]  |                     |                     |                     | $34.0 \pm 0.7$      | $54.5 \pm 0.5$      | $64.2 \pm 0.4$      | $14.6 \pm 0.3$      | $33.5 \pm 0.3$      |
| <b>FRePo</b> [47] | $92.4 \pm 0.5$      | $98.4 \pm 0.1$      | $98.8 \pm 0.1$      | $41.3 \pm 0.5$      | $59.6 \pm 0.3$      | $63.6 \pm 0.2$      | $24.8 \pm 0.2$      | $31.2 \pm 0.2$      |
| $FRePo-w$ [47]    | $93.0 \pm 0.4$      | $98.6 \pm 0.1$      | $99.2 \pm 0.0$      | $46.8 \pm 0.7$      | $65.5 \pm 0.4$      | $71.7 \pm 0.2$      | $28.7 \pm 0.1$      | $42.5 \pm 0.2$      |
| MTT[5]            | $91.4 \pm 0.9$      | $97.3 \pm 0.1$      | $98.5 \pm 0.1$      | $46.3 \pm 0.8$      | $65.3 \pm 0.7$      | $71.6 \pm 0.2$      | $24.3 \pm 0.3$      | $40.1 \pm 0.4$      |
| TESLA $[10]$      |                     |                     |                     | $48.5 \pm 0.8$      | $66.4 \pm 0.8$      | $72.6 \pm 0.7$      | $24.8 \pm 0.4$      | $41.7 \pm 0.3$      |
| <b>MTT</b> [5]    | $91.4 \pm 0.9$      | $97.3 \pm 0.1$      | $98.5 \pm 0.1$      | $46.3 \pm 0.8$      | $65.3 \pm 0.7$      | $71.6 \pm 0.2$      | $24.3 \pm 0.3$      | $40.1 \pm 0.4$      |
| + MIM4DD          | $92.0 \pm 0.6$      | $98.1 \pm 0.2$      | $98.9 \pm 0.2$      | $47.6 \pm 0.2$      | $66.4 \pm 0.2$      | $71.4 \pm 0.3$      | $25.1 \pm 0.3$      | $41.5 \pm 0.2$      |
| Δ                 | (0.6 <sup>†</sup> ) | (0.8 <sup>†</sup> ) | (0.4 <sup>†</sup> ) | (1.3 <sup>†</sup> ) | (1.1 <sup>†</sup> ) | $(0.2\downarrow)$   | (0.8 <sup>†</sup> ) | (1.4 <sup>†</sup> ) |
| <b>BPTT</b> [11]  | $94.7 \pm 0.2$      | $98.9 \pm 0.1$      | $99.2 \pm 0.0$      | $49.1 \pm 0.6$      | $62.4 \pm 0.4$      | $70.5 \pm 0.4$      | $21.3 \pm 0.6$      | $34.7 \pm 0.5$      |
| + MIM4DD          | $95.8 \pm 0.3$      | $98.9 \pm 0.1$      | $99.2 \pm 0.1$      | $51.8 \pm 0.3$      | $66.4 \pm 0.8$      | $72.9 \pm 0.5$      | $25.0 \pm 0.4$      | $38.5 \pm 0.6$      |
| Δ                 | (1.1 <sup>†</sup> ) | $(0.0-)$            | $(0.0-)$            | (2.7 <sup>†</sup> ) | (4.0 <sup>†</sup> ) | (2.4 <sup>†</sup> ) | (3.7 <sup>†</sup> ) | (3.8 <sup>†</sup> ) |
| <b>DREAM</b> [27] |                     |                     | ٠                   | $51.1 \pm 0.3$      | $69.4 \pm 0.4$      | $74.8 \pm 0.1$      | $29.5 \pm 0.3$      | $46.8 \pm 0.7$      |
| $+$ MIM4DD        |                     |                     |                     | $51.9 \pm 0.3$      | $70.8 \pm 0.1$      | $74.7 \pm 0.2$      | $31.1 \pm 0.4$      | $47.4 \pm 0.3$      |
| Δ                 |                     |                     |                     | (0.8 <sup>†</sup> ) | (1.4 <sup>†</sup> ) | $(0.1\downarrow)$   | (0.6 <sup>†</sup> ) | (0.6 <sup>†</sup> ) |

<span id="page-7-0"></span>Table 1: Dataset distillation methods comparisons. The settings are the same as previous SoTAs, BPTT [\[11\]](#page-9-6), MTT [\[5\]](#page-9-4), and DREAM [\[27\]](#page-10-16). Importantly, MIM4DD can work as an add-on module for SoTA methods.

We conduct a series of ablation studies of MIM4DD in Table 2: Hyperparameters selection w. 10 Imgs/Cls CIFAR-10 and CIFAR100. By adjusting the coefficient  $\lambda$  in the loss function  $\mathcal{L}_{\text{MIMADD}}$  (Eq[.2.16](#page-5-1) and Eq[.2.17\)](#page-5-2), we investigate the effect of MIM4DD loss in synthesizing distilled datasets. The results are shown in Fig[.3.](#page-5-3) We observe the trend that with  $\lambda$  increasing, the performance improves, which validates the effectiveness of our designed method. However, when the ratio of  $\mathcal{L}_{NCE}$  in overall loss is greater than a threshold, the performance of student networks drops, which means the main task, dataset distillation is overlooked in the optimization.

<span id="page-7-1"></span>on CIFAR10.

| Hyper-parameter                                           | Accuracy                         |  |  |
|-----------------------------------------------------------|----------------------------------|--|--|
| critic function w.o network                               | $64.9 \pm 0.2$                   |  |  |
| critic function w. 1 fc-layer                             | $65.9 \pm 0.4$                   |  |  |
| critic function w. 2 fc-layer                             | $65.3 \pm 0.4$                   |  |  |
| $\overline{\beta} = 1.0$ in $\mathcal{L}_{\text{MIM4DD}}$ | $63.8 \pm 0.6$                   |  |  |
| $\beta = 2.0$ in $\mathcal{L}_{\text{MIM4DD}}$            | $\mathbf{66.0} \pm \mathbf{0.5}$ |  |  |
| $\beta = 0.5$ in $\mathcal{L}_{\text{MIMADD}}$            | $62.8 \pm 0.6$                   |  |  |

MIM4DD as an Add-on Module. We apply the MIM4DD framework to state-of-the-art Dataset Distillation (DD) techniques, including MTT [\[5\]](#page-9-4) and BPTT [\[11\]](#page-9-6). The results are presented in Tabl[e1.](#page-7-0) It is observed that MIM4DD effectively enhances the performance of all the tested methods, providing substantial evidence that our approach can be utilized as an add-on module to existing techniques.

Hyper-parameters and Relative Module Selection. In addition to ablation studies, we conduct experiments to select the important hyper-parameters and modules. We investigate one hyperparameter, the co-efficient to adjust the weight of layer in overall loss in Eq[.2.17,](#page-5-2) and one module, the architecture of embedding network in Eq[.2.15.](#page-5-4) The results are in Table [2.](#page-7-1)

### 3.4 Regularization Propriety

Here, we analyze the trends of dataset distillation loss,  $\mathcal{L}_{DD}$  in Eq[.2.17](#page-5-2) during training. As we presented in Sec. [2.2,](#page-2-2) the term  $\mathcal{L}_{DD}$  in Eq. 2.17 can be any dataset distillation loss. By controling the co-efficient  $\lambda$  in Eq[.2.17,](#page-5-2) we can separately study the DD loss  $\mathcal{L}_{DD}$ . Their training curves are presented in Fig[.4.](#page-7-2) When turning on NCE loss (assigning  $\lambda = 0.8$ ), we observe that the  $\mathcal{L}_{DD}$  drops using

<span id="page-7-2"></span>Image /page/7/Figure/9 description: The image displays two line graphs side-by-side, each plotting a metric against 'Step' on the x-axis. The y-axis for the left graph ranges from 0 to 2, while the y-axis for the right graph ranges from 0 to 1.5. Both graphs show a blue line representing a fluctuating metric over approximately 4000 steps, with a shaded area indicating variability. The left graph's metric generally stays between 1.2 and 1.7, while the right graph's metric starts around 1.5 and gradually decreases to approximately 0.9 by the end of the 4000 steps.

Figure 4:  $\mathcal{L}_{DD}$  curves while training *w.o.* (left) and *w*. (right)  $\mathcal{L}_{NCE}$ .

our designed NCE loss (Left), while the corresponding testing performance improves (see Table [1\)](#page-7-0).

Combining this phenomenon and analysis on the effect of  $\lambda$  in Fig. [3,](#page-5-3) we conclude that our designed MIM4DD module can act as a regularization term.

### 3.5 CKA analysis

We formulate DD in a view of variable distribution, and thus we analyze distributional information flow within layers of neural networks. Centered kernel alignment (CKA) [\[9,](#page-9-21) [20,](#page-9-22) [32\]](#page-10-19) is able to compare activations within or across networks quantitatively. Specifically, for a network fed by m samples, CKA algorithm takes  $X \in \mathbb{R}^{m \times p_1}$  and  $Y \in \mathbb{R}^{m \times p_2}$  as inputs which are output activations of two layers (with  $p_1$  and  $p_2$  neurons respectively). Letting  $\mathbf{K} \triangleq \mathbf{XX}^\top$  and  $\mathbf{L} \triangleq \mathbf{YY}^\top$ <br>denote the Gram matrices for the two layers  $\mathbf{G}\mathbf{K}$  a computes:  $\mathbf{G}\mathbf{K}$  at  $\mathbf{K}$ , denote the Gram matrices for the two layers CKA computes:  $\text{CKA}(\mathbf{K}, \mathbf{L}) = \frac{\text{HSIC}(\mathbf{K}, \mathbf{L})}{\sqrt{\text{HSIC}(\mathbf{K}, \mathbf{K}) \text{HSIC}(\mathbf{L}, \mathbf{L})}},$ 

where HSIC is the Hilbert-Schmidt independence criterion [\[14\]](#page-9-23). Given the centering matrix  $H =$ 

 $\mathbf{I}_n - \frac{1}{n} \mathbf{1} \mathbf{1}^\top$  and the centered Gram matrices  $\mathbf{K}' = \mathbf{HKH}$  and  $\mathbf{L}' = \mathbf{HLH}$ ,  $\text{HSIC} = \frac{\text{vec}(\mathbf{K}')\text{vec}(\mathbf{L}')}{(m-1)^2}$ , the similarity between these centered Gram matrices. Therefore, we employ Centered Kernel Alignment (CKA) to delve into the information interplay between real and synthetic data. Specifically, we input two datasets into two correspondingly trained networks and examine the similarity between the networks' feature maps using CKA. The findings are depicted in Fig[.5.](#page-8-0) The CKA heatmap reveals that the dataset distilled via MIM4DD shares more information with the real dataset compared to the MTT [\[5\]](#page-9-4). This is because, in our DD formulation, the output similarity is supposed to be highly related to the data similarity (Theorem 1, Eq. $A.1$ ).

<span id="page-8-0"></span>Image /page/8/Figure/5 description: The image displays two heatmaps side-by-side, labeled "MTT" on the left and "MIM4DD" on the right. Both heatmaps have the x-axis labeled "Layers ConvNet on Real Dataset" and the y-axis labeled "Layers ConvNet on Synthetic Dataset". The color bar on the right ranges from -0.003 to 0.003, with intermediate values of -0.002, -0.001, 0.000, 0.001, and 0.002. The MTT heatmap shows a pattern where the color intensity varies across the layers, with some horizontal bands of similar color. The MIM4DD heatmap also shows variations in color intensity across layers, with a more pronounced horizontal banding pattern, particularly around the center.

Figure 5: CKA analyzes the information shared within different datasets (Real v.s. Synthetic with 10 Img/Cls on CIFAR100). The lighter the dot, the more similar of the two corresponding layers learned from different datasets. Higher similar score between two layers' output represents those two layers share more information.

## 4 Related Work

Dataset Distillation is essentially a compression problem that emphasizes maximizing the preservation of information contained in the data. We argue that well-defined metrics which measure the amount of shared information between variables in information theory are necessary for success measurement but are never considered by previous works. Therefore, we propose introducing a well-defined metric in information theory, mutual information (MI), to guide the optimization of synthetic datasets.

The formulation of our method for DD, MIM4DD absorbs the core idea of contrastive learning (*i.e.*, constructing the informative positive and negative pairs for contrastive loss) of the existing contrastive learning methods, especially the contrastive KD methods, CRD [\[39\]](#page-10-7) and WCoRD [\[7\]](#page-9-14). However, our approach has several differences from those methods: (i) our targeted MI and formulated numerical problem are totally different; (ii) our method can naturally avoid the cost of MemoryBank [\[42\]](#page-10-11) for the exponential number of negative pairs in CRD and WCoRD, thanks to the small size of the synthetic dataset in our task. (Further details can be found in the Appendix.)

# 5 Conclusion

In this paper, we explore that well-defined metrics which measures the amount of shared information between variables in information theory for dataset distillation. Specifically, we introduce MI as the metric to quantify the shared information between the synthetic and the real datasets, and devise MIM4DD numerically maximizing the MI via a newly designed optimizable objective within a contrastive learning framework to update the synthetic dataset.

## Acknowledgements

This work is supported by NSF IIS-2309073 and NSF SCH-2123521. This article solely reflects the opinions and conclusions of its authors and not the funding agency.

## References

- <span id="page-9-2"></span>[1] Pankaj K Agarwal, Sariel Har-Peled, and Kasturi R Varadarajan. Approximating extent measures of points. *Journal of the ACM*, 2004.
- <span id="page-9-15"></span>[2] Philip Bachman, R Devon Hjelm, and William Buchwalter. Learning representations by maximizing mutual information across views. In *NeurIPS*, 2019.
- <span id="page-9-19"></span>[3] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020.
- <span id="page-9-7"></span>[4] Leon Brillouin. *Science and information theory*. Courier Corporation, 2013.
- <span id="page-9-4"></span>[5] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022.
- <span id="page-9-0"></span>[6] Liang-Chieh Chen, George Papandreou, Iasonas Kokkinos, Kevin Murphy, and Alan L Yuille. Deeplab: Semantic image segmentation with deep convolutional nets, atrous convolution, and fully connected crfs. *TPAMI*, 2017.
- <span id="page-9-14"></span>[7] Liqun Chen, Dong Wang, Zhe Gan, Jingjing Liu, Ricardo Henao, and Lawrence Carin. Wasserstein contrastive representation distillation. In *CVPR*, 2021.
- <span id="page-9-1"></span>[8] Xinlei Chen and Kaiming He. Exploring simple siamese representation learning. In *CVPR*, 2021.
- <span id="page-9-21"></span>[9] Corinna Cortes, Mehryar Mohri, and Afshin Rostamizadeh. Algorithms for learning kernels based on centered alignment. *JMLR*, 2012.
- <span id="page-9-20"></span>[10] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. *arXiv preprint arXiv:2211.10586*, 2022.
- <span id="page-9-6"></span>[11] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In *NeurIPS*, 2022.
- <span id="page-9-3"></span>[12] Dan Feldman. Introduction to core-sets: an updated survey. *arXiv preprint arXiv:2011.09384*, 2020.
- <span id="page-9-13"></span>[13] Jianping Gou, Baosheng Yu, Stephen J Maybank, and Dacheng Tao. Knowledge distillation: A survey. *IJCV*, 2021.
- <span id="page-9-23"></span>[14] Arthur Gretton, Kenji Fukumizu, Choon Teo, Le Song, Bernhard Scholkopf, and Alex Smola. A kernel ¨ statistical test of independence. In *NeurIPS*, 2007.
- <span id="page-9-8"></span>[15] Michael Gutmann and Aapo Hyvärinen. Noise-contrastive estimation: A new estimation principle for unnormalized statistical models. In *AISTATS*, 2010.
- <span id="page-9-16"></span>[16] Kaiming He, Haoqi Fan, Yuxin Wu, Saining Xie, and Ross Girshick. Momentum contrast for unsupervised visual representation learning. In *CVPR*, 2020.
- <span id="page-9-12"></span>[17] Geoffrey Hinton, Oriol Vinyals, and Jeff Dean. Distilling the knowledge in a neural network. In *NeurIPS*, 2014.
- <span id="page-9-9"></span>[18] R Devon Hjelm, Alex Fedorov, Samuel Lavoie-Marchildon, Karan Grewal, Phil Bachman, Adam Trischler, and Yoshua Bengio. Learning deep representations by mutual information estimation and maximization. *arXiv preprint arXiv:1808.06670*, 2018.
- <span id="page-9-5"></span>[19] Jeremias Knoblauch, Hisham Husain, and Tom Diethe. Optimal continual learning has perfect memory and is np-hard. In *ICML*, 2020.
- <span id="page-9-22"></span>[20] Simon Kornblith, Mohammad Norouzi, Honglak Lee, and Geoffrey Hinton. Similarity of neural network representations revisited. In *ICML*, 2019.
- <span id="page-9-11"></span>[21] Alexander Kraskov, Harald Stögbauer, and Peter Grassberger. Estimating mutual information. Physical *review E*, 2004.
- <span id="page-9-17"></span>[22] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-9-10"></span>[23] Solomon Kullback. *Information theory and statistics*. Courier Corporation, 1997.
- <span id="page-9-18"></span>[24] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 1998.

- <span id="page-10-0"></span>[25] Yann LeCun, Yoshua Bengio, and Geoffrey Hinton. Deep learning. *Nature*, 2015.
- <span id="page-10-18"></span>[26] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *ICML*, 2022.
- <span id="page-10-16"></span>[27] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. In *ICCV*, 2023.
- <span id="page-10-20"></span>[28] Dougal Maclaurin, David Duvenaud, and Ryan Adams. Gradient-based hyperparameter optimization through reversible learning. In *ICML*, 2015.
- <span id="page-10-10"></span>[29] Aaron van den Oord, Yazhe Li, and Oriol Vinyals. Representation learning with contrastive predictive coding. *arXiv preprint arXiv:1807.03748*, 2018.
- <span id="page-10-22"></span>[30] Ben Poole, Sherjil Ozair, Aaron Van Den Oord, Alex Alemi, and George Tucker. On variational bounds of mutual information. In *ICML*, 2019.
- <span id="page-10-1"></span>[31] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, et al. Learning transferable visual models from natural language supervision. In *ICML*, 2021.
- <span id="page-10-19"></span>[32] Maithra Raghu, Thomas Unterthiner, Simon Kornblith, Chiyuan Zhang, and Alexey Dosovitskiy. Do vision transformers see like convolutional neural networks? In *NeurIPS*, 2021.
- <span id="page-10-6"></span>[33] Sebastian Ruder. An overview of gradient descent optimization algorithms. *arXiv preprint arXiv:1609.04747*, 2016.
- <span id="page-10-2"></span>[34] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018.
- <span id="page-10-13"></span>[35] Pierre Sermanet, Soumith Chintala, and Yann LeCun. Convolutional neural networks applied to house numbers digit classification. In *ICPR*, 2012.
- <span id="page-10-8"></span>[36] Yuzhang Shang, Dan Xu, Ziliang Zong, and Yan Yan. Network binarization via contrastive learning. In *ECCV*, 2022.
- <span id="page-10-12"></span>[37] Yuzhang Shang, Bingxin Xu, Gaowen Liu, Ramana Rao Kompella, and Yan Yan. Causal-dfq: Causality guided data-free network quantization. In *ICCV*, 2023.
- <span id="page-10-21"></span>[38] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *IJCNN*, 2021.
- <span id="page-10-7"></span>[39] Yonglong Tian, Dilip Krishnan, and Phillip Isola. Contrastive representation distillation. In *ICLR*, 2021.
- <span id="page-10-5"></span>[40] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022.
- <span id="page-10-3"></span>[41] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-10-11"></span>[42] Zhirong Wu, Yuanjun Xiong, Stella X Yu, and Dahua Lin. Unsupervised feature learning via non-parametric instance discrimination. In *CVPR*, 2018.
- <span id="page-10-9"></span>[43] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021.
- <span id="page-10-17"></span>[44] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023.
- <span id="page-10-14"></span>[45] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021.
- <span id="page-10-4"></span>[46] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 2021.
- <span id="page-10-15"></span>[47] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *arXiv preprint arXiv:2206.00719*, 2022.

## A Appendix

### A.1 In-variance of Mutual Information

Theorem 1 (In-variance of Mutual Information): Mutual information is invariant under reparametrization of the marginal variables. If  $X' = F(X)$  and  $Y' = G(Y)$  are homeomorphisms  $(i.e., F(\cdot)$  and  $G(\cdot)$  are smooth uniquely invertible maps), then

<span id="page-11-0"></span>
$$
I(X,Y) = I(X',Y').
$$
\n(A.1)

**Proof.** If  $X' = F(X)$  and  $Y' = G(Y)$  are homeomorphisms (smooth and uniquely invertible maps), and  $J_X = \|\frac{\partial X}{\partial X'}\|$  and  $J_Y = \|\frac{\partial Y}{\partial Y'}\|$  are the Jacobi determinants, then

$$
\mu'(x', y') = J_X(x')J_Y(y')\mu(x, y) \tag{A.2}
$$

and similarly for the marginal densities, which gives

$$
I(X', Y') = \iint dx'dy'\mu'(x', y') \log \frac{\mu'(x', y')}{\mu'_x(x')\mu'_y(y')}
$$

$$
= \iint dx dy \mu(x, y) \log \frac{\mu(x, y)}{\mu_x(x)\mu_y(y)}
$$
 (A.3)
$$
= I(X, Y).
$$

More details can be found in [\[21\]](#page-9-11).

## Discussion on Theorem 1.

Our objective is to maximize the Mutual Information (MI) between the synthetic dataset and the real dataset (Eq. 3), a task that is numerically unfeasible. To overcome this challenge, we present this theorem. It allows us to transform the target problem at the data level (Eq. 3) into a more manageable problem at the feature map level (Eq. 9). Given that each layer's mapping  $\mathbf{W}^k : \mathbb{R}^{d_{k-1}} \longmapsto \mathbb{R}^{d_k}$  ( $k =$  $1, ..., K$ ) in the network (as per Eq. 4, 5, and 6) can be treated as smooth, uniquely invertible maps, we can achieve the goal of maximizing the mutual information between the two datasets. This is done by maximizing the mutual information between two sets of down-sampled feature maps.

### A.2 Datasets and Implementation Details

#### A.2.1 Datasets

MNIST [\[24\]](#page-9-18) is a dataset for handwritten digits recognition that is widely used for validating image recognition models. It contains 60,000 training images and 10,000 testing images with the size of  $28 \times 28$ .

**CIFAR10/100** [\[22\]](#page-9-17) are two datasets consist of tiny colored natural images with the size of  $32 \times 32$ from 10 and 100 categories, respectively. In each dataset, 50,000 images are used for training and 10,000 images for testing.

#### A.2.2 Implementation Details.

In the experiments, we optimize synthetic sets with 1/10/50 Images Per Class (IPC) across all three datasets, using a three-layer Convolutional Network (ConvNet) identical to those used in [\[46,](#page-10-4) [40,](#page-10-5) [5\]](#page-9-4). The ConvNet comprises three consecutive blocks of 'Conv-InstNorm-ReLU-AvgPool.' Each convolutional layer has 128 channels, and AvgPool represents a  $2 \times 2$  average pooling operation with stride 2. The synthetic images' initial learning rate is 0.1, which is halved at the 1,800th and 2,800th iterations. The training is stopped after 5,000 iterations. To test the ConvNet's performance on the synthetic dataset, we train the network on synthetic sets for 300 epochs and assess the performance using five randomly initialized networks. The network's initial learning rate is 0.01. As per [\[5\]](#page-9-4), we conduct five experiments and report the mean and standard deviation across the five networks. The default batch size is 256, and  $\lambda$  in Eq.17 is 0.8. The effect of  $\lambda$  is explored in Sec.3.3.

Image /page/12/Picture/0 description: The image displays a grid of 60 images, divided into two columns by a vertical red line. The left column contains 30 real images, and the right column contains 30 synthetic images. The real images are organized into 6 rows and 5 columns, showcasing various objects and animals such as airplanes, cars, birds, cats, deer, dogs, frogs, horses, ships, and trucks. The synthetic images on the right appear to be generated or stylized versions of the real images, with a more abstract or artistic quality, often resembling dreamlike or impressionistic interpretations of the original subjects.

Figure 6: (Left) Samples from CIFAR10; (Right) Samples from Synthetic dataset based on CIFAR10. We observe that the heterogeneity in the generated images enhanced, benefited from the contrastive learning loss (Loss  $\mathcal{L}_{\text{MIMADD}}$  in Eq.17).

## A.3 Synthetic Samples Visualization.

## B Related Work

Dataset Distillation (DD) is firstly introduced by Wang *et al.* [\[41\]](#page-10-3), in which they optimize the distilled images using gradient-based hyperparameter optimization [\[28\]](#page-10-20). The key problem is to optimize the specific-designed metrics of networks on real and synthetic datasets to update the optimizable images. Subsequently, several works significantly improve the results by designing different metrics. For example, Bohdal *et al.*and Sucholutsky *et al.* [\[3,](#page-9-19) [38\]](#page-10-21) use distance between networks' soft labels; Zhao *et al.* [\[46\]](#page-10-4) define the gradients of networks as metric; Zhao *et al.* [\[43\]](#page-10-9) further adopts augmentations to enhance the alignment ability; Wang *et al.* [\[40\]](#page-10-5) utilize distance of network feature maps as metric; and Cazenavette [\[5\]](#page-9-4) propose long-range trajectory to construct the metric function. Lee *et al.* [\[26\]](#page-10-18) propose Dataset Condensation with Contrastive Signals (DCC) by modifying the loss function to enable the DC methods to effectively capture the differences between classes. On the other hand, researchers take DD as a bi-level optimization problem. For example, Zhou *et al.* [\[47\]](#page-10-15) employ a closed-form approximation for the unrolled inner optimization; Deng *et al.* [\[11\]](#page-9-6) revisits the optimization framework in [\[41\]](#page-10-3) and observe that the inclusion of a momentum term in inner optimization can significantly enhance performance, leading to state-of-the-art results in certain settings.

DD is essentially a compression problem that emphasizes on maximizing the preservation of information contained in the data. We argue that well-defined metrics which measure the amount of shared information between variables in information theory are necessary for success measurement, but are

never considered by previous works. Therefore, we propose to introduce a well-defined metric in information theory, mutual information (MI), to guide the optimization of synthetic datasets.

Contrastive Learning and Mutual Information. The fundamental idea of all contrastive learning methods is to draw the representations of positive pairs closer and push those of negative pairs farther apart within a contrastive space. Several self-supervised learning methods are rooted in well-established ideas of MI maximization, such as Deep InfoMax [\[18\]](#page-9-9), Contrastive Predictive Coding [\[29\]](#page-10-10), MemoryBank [\[42\]](#page-10-11), Augmented Multiscale DIM [\[2\]](#page-9-15), MoCo [\[16\]](#page-9-16) and SimSaim [\[8\]](#page-9-1). These are based on NCE [\[15\]](#page-9-8) and InfoNCE [\[18\]](#page-9-9) which can be seen as a lower bound on MI [\[30\]](#page-10-22). Meanwhile, Tian *et al.* [\[39\]](#page-10-7) and Chen *et al.* [\[7\]](#page-9-14) extend the contrastive concept into the realm of Knowledge Distillation (KD), pulling and pushing the representations of teacher and student.

The formulation of our method for DD, MIM4DD also absorbs the core idea (*i.e.*, constructing the informative positive and negative pairs for contrastive loss) of the existing contrastive learning methods, especially the contrastive KD methods, CRD [\[39\]](#page-10-7) and WCoRD [\[7\]](#page-9-14). However, our approach has several differences from those methods: (i) our targeted MI and formulated numerical problem are totally different; (ii) our method can naturally avoid the cost of MemoryBank [\[42\]](#page-10-11) for the exponential number of negative pairs in CRD and WCoRD, thanks to the small size of the synthetic dataset in our task. Given that the size of the synthetic dataset M typically ranges from  $0.1 - 1\%$  of the size of the real dataset N, the product  $M \cdot N$  is significantly smaller than  $N \cdot N$  (*i.e.*,  $M \cdot N \ll N \cdot N$ ).

Difference with DCC [\[26\]](#page-10-18). Recently, Lee et al. [\[26\]](#page-10-18) introduced Dataset Condensation with Contrastive Signals (DCC), modifying the loss function to allow Dataset Condensation methods to effectively discern differences between classes. However, several distinctions exist between DCC and our method: (i) They are motivated differently. Our approach is predicated on information degradation, while DCC hinges on class diversity. (ii) From the perspective of contrastive learning, the view, positive and negative samples differ considerably. Our approach can be implemented at the feature map level, thanks to the introduced Theorem 1, while DCC can only be deployed at the gradient level. (iii) The performance of our method significantly surpasses that of DCC.
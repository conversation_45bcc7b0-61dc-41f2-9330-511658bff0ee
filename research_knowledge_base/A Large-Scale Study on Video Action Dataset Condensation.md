# <span id="page-0-1"></span>A Large-Scale Study on Video Action Dataset Condensation

Yang <PERSON><sup>1</sup> <PERSON><PERSON><sup>2</sup> <PERSON><PERSON><sup>1, 3,  $\boxtimes$ </sup> <sup>1</sup>State Key Laboratory for Novel Software Technology, Nanjing University  $2^2$ Ant Group  $3$  Shanghai AI Lab

## Abstract

*Dataset condensation has made significant progress in the image domain. Unlike images, videos possess an additional temporal dimension, which harbors considerable redundant information, making condensation even more crucial. However, video dataset condensation still remains an underexplored area. We aim to bridge this gap by providing a large-scale empirical study with systematic design and fair comparison. Specifically, our work delves into three key aspects to provide valuable empirical insights: (1) temporal processing of video data, (2) establishing a comprehensive evaluation protocol for video dataset condensation, and (3) adaptation of condensation methods to the space-time domain and fair comparisons among them. From this study, we derive several intriguing observations: (i) sample diversity appears to be more crucial than temporal diversity for video dataset condensation, (ii) simple slide-window sampling proves to be effective, and (iii) sample selection currently outperforms dataset distillation in most cases. Furthermore, we conduct experiments on three prominent action recognition datasets (HMDB51, UCF101 and Kinetics-400) and achieve state-of-the-art results on all of them. Our code is available at* [https://github.com/MCG-](https://github.com/MCG-NJU/Video-DC)[NJU/Video-DC](https://github.com/MCG-NJU/Video-DC)*.*

## 1. Introduction

Dataset condensation, crucial for data-efficient learning, focuses on reducing the size of an original dataset while maintaining the performance of models trained on the condensed version [\[35\]](#page-11-0). This task addresses challenges associated with training neural networks on large datasets, including high computational costs and memory consumption. Dataset condensation has numerous applications, such as neural architecture search  $[27]$ , privacy protection  $[18]$ , and more.

Previous research on dataset condensation [\[3,](#page-10-2) [6,](#page-10-3) [7,](#page-10-4) [10,](#page-10-5) [14,](#page-10-6) [16,](#page-10-7) [17,](#page-10-8) [20,](#page-10-9) [21,](#page-10-10) [24,](#page-10-11) [28,](#page-10-12) [33,](#page-11-1) [35,](#page-11-0) [41](#page-11-2)[–44\]](#page-11-3) has primarily focused on image datasets. While some methods have been

<span id="page-0-0"></span>Image /page/0/Figure/8 description: This is a flowchart illustrating a two-stage process: (1) Condensation and (2) Evaluation. In the Condensation stage, a 'Synthetic Video' is processed by an 'Interpolator' which receives feedback from a 'Condensation Network'. The 'Condensation Network' also receives input from 'Real Video' and a 'Dataset Distillation' module. A 'Sample Selection' module determines whether to proceed based on a condition 'G > 0', with a 'Loss Backward' arrow indicating gradient flow. The 'Synthetic Video' is combined with the output of the 'Sample Selection'. In the Evaluation stage, the 'Synthetic Video' is again processed by an 'Interpolator', followed by an 'Evaluation Network', and finally tested on a 'Val Set'. The diagram also includes a legend indicating 'Slide-window', 'Loss Backward', 'G: Require Grads', and 'Condensation Network'.

Figure 1. Pipeline of Our Study. Our study includes three core elements: temporal processing, condensation methods and evaluation settings. Temporal processing is the special design for video data, including slide-window sampling and interpolation. Real videos comes across a condensation method (categorized into sample selection and dataset distillation) to form the synthetic dataset (left). Then, the synthetic dataset trains an evaluation network to test on val set as the evaluation metric (right). Both of the process relies on a neural network.

applied to other data modalities, such as graph data [\[38,](#page-11-4) [40\]](#page-11-5) and natural language [\[37\]](#page-11-6), video dataset condensation remains largely unexplored. As an extension of images, video plays a crucial role in conveying visual information. However, the additional data in videos increases storage and computational demands, while also introducing redundant scene and texture information. These factors underscore the heightened need for and significant potential of video dataset condensation. In response, our work adapts existing algorithms for video data and conducts a large-scale empirical study on video dataset condensation.

Algorithms for dataset condensation can be broadly classified into two main categories: sample selection and dataset distillation. The former focuses on selecting samples from the original dataset, making the condensed dataset a subset of the original data (or data after combination). For example, RDED [\[28\]](#page-10-12) selects samples based on observer scores and concatenates the selected samples into

B : Corresponding author (<EMAIL>).

<span id="page-1-1"></span>condensed data. While, the latter synthesizes data by optimizing proxy objectives, such as matching training trajectories or distributions. Both methods have demonstrated effectiveness on image datasets.

The motivation for our work is to address the growing significance of video dataset condensation. We adapt existing algorithms for video data to explore different designs of temporal processing and conduct fair comparisons between various methods. Previous work [\[36\]](#page-11-7) shares a similar motivation, conducting ablations on the parameterization of temporal expressions and extending classic dataset distillation algorithms to the space-time domain. By contrast, our approach incorporates dataset selection algorithms and introduces a more comprehensive evaluation protocol. Moreover, our method demonstrates superior performance compared to theirs [\[36\]](#page-11-7).

We follow the pipeline of Fig. [1](#page-0-0) to conduct our study. Firstly, the real dataset is condensed by different condensation methods into the synthesized dataset. Then, the synthetic one is used to train downstream neural networks, the performance of which on the real validation set serves as the metric for evaluating the condensation algorithms. We adapt various dataset condensation methods from images to videos. Similar to image dataset condensation, we select action recognition as the evaluation task for video dataset condensation. Besides, we propose a co-optimized slidewindow sampling method to effectively restore temporal information considering the importance of temporal processing in video dataset condensation.

Specifically, our large-scale study follows these three main steps: Firstly, we adapt existing dataset condensation methods (RDED [\[28\]](#page-10-12), DATM [\[10\]](#page-10-5), EDC [\[24\]](#page-10-11)) to video data. Based on an analysis of different evaluation settings, we establish a unified evaluation protocol for fair comparison. Secondly, to investigate different designs of temporal processing, we propose a paradigm of sampling with interpolation. Based on this, we propose a co-optimized sliding window sampling method. Additionally, we conducted ablation studies on both temporal and sample compression. Finally, we do fair comparison between classic dataset condensation methods and do ablations about details of algorithms. Based on the experiments, we draw intriguing observations: sample selection methods generally outperform dataset distillation methods in most situations while the trend reverses when the condensation ratio is extremely low. This suggests an sub-optimized problem of dataset distillation especially for video data. Once resolved, it holds immense potential for efficient machine learning.

In summary, our key contributions are as follows: (1) the establishment of an evaluation protocol for video dataset condensation, (2) the development of a temporal processing method, (3) comprehensive experimental results and in-depth analysis of condensation methods, and (4) stateof-art performance on three popular action video datasets: HMDB51 [\[15\]](#page-10-13), UCF101 [\[26\]](#page-10-14) and Kinetics-400 [\[2\]](#page-10-15).

## <span id="page-1-0"></span>2. Related Work

Dataset Condensation contains two main approaches: sample selection and dataset distillation.

Sample Selection. In sample selection, there are two main kinds of approaches: optimization-based and score-based selection. Optimization-based selection focuses on finding a small coreset that effectively captures the diverse characteristics of the entire dataset. Methods such as Herding [\[4\]](#page-10-16) and K-center [\[23\]](#page-10-17) aim to create a coreset that mirrors the full dataset distribution. Techniques like Craig [\[19\]](#page-10-18) and GradMatch [\[12\]](#page-10-19) look for a coreset that minimizes the average gradient discrepancy from the full dataset during neural network training. While these methods perform well at small to medium instances-per-class (IPCs), they often encounter scalability challenges in terms of computation and performance as IPCs increase, particularly when compared to score-based selection approaches.

Score-based selection [\[11,](#page-10-20) [13\]](#page-10-21), on the other hand, assigns a value to each instance based on its difficulty or impact on neural network training. For instance, the Forgetting [\[29\]](#page-10-22) method assesses how challenging an instance is by counting the number of times it is misclassified after having been correctly classified in earlier epochs. Recently, RDED [\[28\]](#page-10-12) has utilized pretrained models to evaluate the realism and diversity of samples. Based on these metrics, it selects samples and combines them to form the final condensed dataset. These techniques prioritize challenging samples that capture rare and intricate features. They often outperform optimization-based selection methods, particularly at larger IPC levels where maintaining sample diversity becomes increasingly critical [\[22,](#page-10-23) [29\]](#page-10-22).

RDED achieves state-of-the-art performance across various image datasets, making it our preferred representative for sample selection methods. For completeness, we also report the results of several other sample selection methods. Dataset Distillation. Dataset distillation algorithms can be classified into the following categories: (1) Performance Matching: a wide range of techniques directly optimizes the performance of downstream network following the initial work of DD[\[35\]](#page-11-0). However, this process involves a bi-level optimization. To mitigate the problem, some approaches [\[17,](#page-10-8) [20,](#page-10-9) [21,](#page-10-10) [44\]](#page-11-3) incorporates kernel ridge regression (KRR) to get the closed-form solution of the inter loop, which largely reduce the computational complexity. For example, KIP [\[20,](#page-10-9) [21\]](#page-10-10) employs the Neural Tangents Kernel (NTK). Besides, FRePo [\[44\]](#page-11-3) further separates a neural network into a feature extractor and a linear classifier for optimization. (2) Trajectory Matching: DC [\[43\]](#page-11-8) proposes optimizing the condensed data by aligning the parameter updates of a network when trained on both origi<span id="page-2-1"></span>nal and synthetic data. DSA [\[41\]](#page-11-2) builds on this idea and demonstrates that applying symmetric data augmentation can effectively enhance performance. To further enhance the alignment of optimization trajectories, MTT [\[3\]](#page-10-2) introduces multi-step parameter matching. TESLA [\[6\]](#page-10-3) refines its algorithm to effectively reduce memory and computational costs. DATM [\[10\]](#page-10-5) achieves improved performance by selectively aligning specific segments of the optimization trajectory. (3) Distribution Matching: DM [\[42\]](#page-11-9) aligns the features of real and synthetic data after image backbone, while CAFE [\[33\]](#page-11-1) utilizes features from different layers of models. A series of recent works have leveraged statistical matching to reduce the computational and storage costs of algorithms. SRe2L [\[39\]](#page-11-10) was the first to introduce statistical matching, extending distribution matching algorithms to large-scale datasets like ImageNet. G-VBSM [\[25\]](#page-10-24) enhances performance by making full use of various teacher networks and statistical measures, while EDC [\[24\]](#page-10-11) refines numerous details of the entire pipeline for statistic matching.

Action Recognition. The development of video backbones for action recognition has progressed significantly to better capture spatial and temporal features. C3D [\[31\]](#page-11-11) introduced 3D convolutions to process both aspects simultaneously but was computationally heavy. I3D [\[2\]](#page-10-15) improved efficiency by inflating 2D convolutional filters pre-trained on image data into 3D, benefiting from transfer learning. R(2+1)D [\[32\]](#page-11-12) refined this by separating 3D convolutions into 2D spatial and 1D temporal components, enhancing flexibility and reducing parameters. The SlowFast [\[8\]](#page-10-25) network further advanced the field with a dual-pathway design that processes video at different frame rates, capturing both detailed spatial information and fast motion changes. Some transformer-based backbone [\[1,](#page-10-26) [30,](#page-10-27) [34\]](#page-11-13) have also demonstrated great effectiveness. In this work, we use these video backbones for video dataset condensation.

### 3. Method

The objective of this work is to investigate recent dataset condensation methods for condensing video datasets. For these methods were initially designed for image datasets, we extend them to the space-time domain, examining the implementation details and comparing their performance under consistent conditions to evaluate their efficacy in dataset condensation. Our study primarily focuses on one sample selection method: RDED [\[28\]](#page-10-12), and two dataset distillation methods: DATM  $[10]$  and EDC  $[24]$ .

As in Fig. [1,](#page-0-0) the process of the dataset condensation can be divided into two main stages. First, the original dataset is condensed into a smaller one using either distillation or selection methods. Next, the condensed dataset is applied to a downstream task to assess the effectiveness of the condensation algorithm. Adapting condensation algorithms from images to videos, we propose slide-window sampling and interpolation for temporal processing. In this section, we will discuss about the temporal processing and then introduce two parts of dataset condensation separately.

<span id="page-2-0"></span>

### 3.1. Temporal Processing

From images to videos, the processing of the temporal dimension is the most crucial topic. Like in action recognition, the clip input needs to be sampled and augmented before being passed into the network. To enhance temporal compression, an interpolation module can be added after sampling to adjust the compressed video to the required number of frames [\[36\]](#page-11-7). Wang *et al*. [\[36\]](#page-11-7) proposed splitting the compressed video into separate segments, with each segment serving as a training video clip. By contrast, we propose using a slide-window sampling across the synthetic video, enabling more efficient use of the condensed data.

Previous dataset condensation methods mainly focus on condensing between instances. However, in the case of videos, there is often much redundant information along the temporal dimension, making it crucial to explore temporal compression. Maintaining the same spatial resolution, we condensed the dataset to  $N_c$  samples, each with  $T_c$ frames. Then, the total condensation ratio can be expressed as  $\frac{N_c \times T_c}{N \times T_m}$ , where N is the number of videos in the original dataset, and  $T_m$  is the average number of frames per original video. We further define  $\frac{N_c}{N}$  as the instance compression ratio and  $\frac{T_c}{T_m}$  separately as temporal compression ratio. These two factors are ablated in Tab. [4b,](#page-5-0) and Tab. [4a](#page-5-0) explores the effectiveness of pure temporal compression.

### 3.2. Condensation Algorithm

For a given large, real dataset  $\mathcal{D}_{real} = (X, Y)$ , dataset condensation aims to synthesize a smaller dataset  $\mathcal{D}_{syn} =$  $(\hat{X}, \hat{Y})$  such that model trained on  $\mathcal{D}_{syn}$  achieve comparable test performance as those trained on  $\mathcal{D}_{real}$ . We decompose the synthesis of videos and labels, discussing each aspect separately. As summarized in Sec. [2,](#page-1-0) condensation algorithms can be classified into sample selection and dataset distillation. Additionally, we categorize the relabel strategies they use into three main types: hard label, soft label and multiple soft label. In this subsection, we will discuss about these methods carefully. Figure [2](#page-3-0) visualizes the three types of condensation algorithms.

DATM [\[10\]](#page-10-5) (Fig. [2a](#page-3-0)) is a representative of matching training trajectories. It optimizes the synthetic data by matching the training trajectories of condensation models trained on  $\mathcal{D}_{real}$  and  $\mathcal{D}_{syn}$ . Specifically, let  $\{\theta_t^R\}_{0}^n$  denote the training trajectory obtained by  $\mathcal{D}_{real}$ , where  $\theta_t^R$  is the parameters of the condensation network at training step t. Similarly,  $\theta_t^S$ denotes the parameters trained on  $\mathcal{D}_{sun}$  at time step t.

In each iteration of the distillation, sampling time step t

<span id="page-3-1"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: The figure illustrates a method for condensing datasets, featuring three main components: (a) Trajectory Matching, (b) Distribution Matching, and (c) Score Selection. The overall process involves a 'Condensed Dataset' and a 'Real Dataset' feeding into a 'Condensation Module'. This module is updated via a 'Proxy Loss' or 'Selection Index'. Component (a) shows a trajectory matching process with model parameters (θ) at different time steps (t-1, t, t+1) for both reference (R) and source (S) data, leading to a 'Loss' calculation. Component (b) details a multi-stage distribution matching process (Stage I, Stage II, Stage III), where each stage involves matching and culminates in a 'CE Loss + Matching Loss'. Component (c) describes a 'Score Selection' mechanism involving 'N' scores, an 'Observer', and 'IPC' selections, resulting in 'C' outputs. A legend indicates that 'θ0' represents 'Model Param', a sequence of boxes represents 'Video Clip', and a green rectangle represents 'Model Layer'.

Figure 2. Conceptual Visualization of Three Dataset Condensation Frameworks Applied to Video. Trajectory Matching (a) and Distribution Matching (b) in the blue boxes belong to dataset distillation methods, and Score Selection (c) in the green box belongs to sample selection methods. Dataset distillation typically defines a proxy task to help condense the dataset and uses the output proxy loss (left) to update the condensed dataset, while sample selection directly draws samples from the real dataset to form the condensed one. Trajectory Matching (a) uses a normed L2 loss to describe distance between parameters, and (b) matches the distribution between model layers. (c) scores each sample from the real dataset and reconstructs them to the condensed dataset.

from 0 to n, the proxy loss is:

$$
\mathcal{L} = \frac{\|\theta_{t+N}^S - \theta_{t+M}^R\|_2^2}{\|\theta_t^R - \theta_{t+M}^R\|_2^2},\tag{1}
$$

where M and N is the hyper-parameter of optimization step on  $\mathcal{D}_{real}$  and  $\mathcal{D}_{sun}$  for the condensation network.

Specifically, to better align the difficulty of distillation as the distillation process advances, DATM gradually increases the sampled time steps, ensuring a smoother and more effective optimization.

EDC [\[24\]](#page-10-11) (Fig. [2b](#page-3-0)) optimizes condensed video clips through distribution matching. The core concept of distribution matching is to align data distribution of  $\mathcal{D}_{real}$  and  $\mathcal{D}_{sun}$ in different feature space of condensation models, ensuring that synthetic data achieves optimization effects comparable to real data. For scalability, EDC and its predecessors [\[25,](#page-10-24) [39\]](#page-11-10) simplify the distribution matching process by focusing on matching the mean and variance of features. By pre-computing the mean and variance of features across different modules from the condensation model, the distillation process becomes extremely efficient and computationally lightweight. Due to its efficiency, we select EDC as the representative distribution matching algorithm. More details about EDC can be found in the Appendix.

RDED [\[28\]](#page-10-12) ( Fig. [2c](#page-3-0)) selects the most realistic clip from a set of samples and combine them to form the final condensed video. The realism of a clip  $\xi_{i,k}$  is defined as  $-\ell(\phi_{\theta_{\mathcal{T}}}(\xi_{i,k}), y_i)$ , where  $\phi_{\theta_{\mathcal{T}}}$  represents the pretrained model and  $y_i$  is the human-annotated label.

Previous studies [\[9\]](#page-10-28) have shown that many carefully designed sample selection methods do not have obvious advantages over random selection. However, RDED achieves remarkable results with this simple design. Thus, we choose RDED as the representative sample selection algorithm.

Relabel Strategies have a significant impact on the performance of condensed datasets (Fig. [4\)](#page-6-0). Besides, the storage and computation consumption for three different strategies differs a lot. Thus, we need to distinguish different relabel strategies for fair comparison.

Hard labeling assigns each video a one-hot encoded label that remains unchanged during the dataset condensation process. This method is the most straightforward and intuitive, serving as the fundamental strategy.

Soft labeling is inspired by the field of knowledge distillation and uses logits as labels. Using logits as labels can better capture the semantic similarity between samples and different classes, as well as the relationships between various categories. This representation is particularly beneficial for learning in classification tasks. It can be be derived from a pre-trained teacher model or set as learnable vectors that are optimized together with the condensed videos.

Finally, multi soft labeling (Multi-SL) involves assign-

<span id="page-4-3"></span><span id="page-4-0"></span>

| Dataset      | #videos | $n_{mean}$ | $T_{mean}$ | $T_{median}$ |
|--------------|---------|------------|------------|--------------|
| HMDB51       | 3,570   | 70         | 97         | 79           |
| UCF101       | 9,537   | 94         | 187        | 168          |
| Kinetics-400 | 240,000 | 600        | 286        | 300          |

Table 1. Statistics of Action Recognition Video Datasets.  $n_{mean}$  represents the average number of videos per class.  $T_{mean}$ and  $T_{median}$  are the mean and median of frames per video.

ing multiple soft labels to a single sample. This method leverages data augmentation to assign different logits as labels during different epochs of training. By doing so, it captures diverse aspects of the sample under various augmented views, enhancing the model's generalization ability.

### 3.3. Evaluation Protocol

This work primarily focuses on video dataset condensation for action recognition. The evaluation process involves training a new neural network specifically for action recognition. It is more complex than other tasks, with different training settings significantly impacting the final metric. Dataset condensation not only aims to save storage consumption but also the training time when utilizing condensed datasets. To achieve this, we limit the number of iterations to approximately 20% of that used for the original dataset. Under these conditions, the neural network may not fully converge, but the metric remains fair and can better reflect performance in real employment situation.

Additionally, data augmentation and loss functions significantly impact the results. We conduct ablation studies on them with different algorithms and relabeling strategies to broadly demonstrate that: (1) comparisons between methods are consistent across different evaluation settings, (2) evaluation settings substantially affect the final metrics, and (3) employing different evaluation settings when comparing algorithms may lead to incorrect conclusions. Therefore, we propose a unified evaluation protocol based on our experimental results.

### 4. Experiment

Our work builds upon numerous existing algorithms for image dataset condensation, so we retain their original names for simplicity. In this section, unless otherwise specified, all algorithms refer to their video-version implementations.

Datasets. Unless otherwise noted, we perform dataset condensation on UCF101 [\[26\]](#page-10-14), which contains approximately 10,000 training videos across 101 human action categories. To verify the methods' effectiveness on a broader scope, we also provide results on HMDB51 [\[15\]](#page-10-13) and Kinetics-400 [\[2\]](#page-10-15). HMDB51 includes 51 categories, each with 70 training videos. In contrast, Kinetics-400 has significantly more videos than the former two datasets. With around

<span id="page-4-1"></span>Image /page/4/Figure/9 description: The image is a line graph titled "Convergence of evaluation model on condensed UCF101". The x-axis represents the Epoch, ranging from 0 to 600. The y-axis represents the MSE-GT Loss, ranging from 2.5 to 22.5. There are four lines plotted on the graph, each representing a different model convergence: DATM-600ep (dashed green line), DATM-300ep (dashed orange line), RDED-600ep (dashed blue line), and RDED-300ep (dashed pink line). The DATM-300ep line starts at approximately 10 MSE-GT Loss and converges to around 4.5 MSE-GT Loss by epoch 600. The DATM-600ep line starts at approximately 4.5 MSE-GT Loss and stays around 4.5 MSE-GT Loss throughout the epochs. The RDED-300ep line starts at approximately 22 MSE-GT Loss and converges to around 9 MSE-GT Loss by epoch 600. The RDED-600ep line starts at approximately 12 MSE-GT Loss and converges to around 7 MSE-GT Loss by epoch 600.

Figure 3. Training Loss of Evaluation Models. DATM and RDED represents dataset distillation and sample selection respectively. Cosine annealing scheduler is used for different epoch (ep) number. The loss is not converged after 300 eps of training. The loss curves of DATM are under those of RDED.

<span id="page-4-2"></span>

|              | Hard Label |     | Soft Label |      | Multi-SL |      |
|--------------|------------|-----|------------|------|----------|------|
|              | CutMix     | w/  | w/o        | w/   | w/o      | w/   |
| <b>EDC</b> + | 4.5        | 4.6 | 3.0        | 6.0  | 5.7      | 7.0  |
| <b>DATM</b>  | 6.7        | 6.8 | 9.7        | 12.1 | 14.9     | 15.4 |
| <b>RDED</b>  | 10.2       | 9.7 | 11.0       | 14.3 | 16.1     | 17.7 |

Table 2. Augmentation Ablation: CutMix. Data: UCF101. Not using CutMix consistently improve the accuracy with only one exception (RDED hard label).  $EDC^{\dagger}$  represents a simple version of EDC without category-wise matching.

240,000 training videos in 400 human action categories, it allows for results at extreme condensation ratios and enables exploration of the methods' scalability.

Table [1](#page-4-0) shows the statistics of the three action recognition datasets. The average frames of videos in HMDB51 and UCF101 are 97 and 187, respectively. Most of Kinetics videos are of 300 frames in duration. For comparison, the input of video backbone typically spans around 60 frames in videos. Thus, it is more suitable to do temporal exploration on Kinetics-400 and UCF101. Moreover, with a proper scale on video duration and number, we select UCF101 for ablation.

Implementation Details. Following previous work [\[36\]](#page-11-7), we use MiniC3D as the condensation model. The pretrained teacher is trained by mmaction code base with 8 frames as inputs. In EDC, we incorporate auxiliary teacher models: I3D, R(2+1)D, and SlowOnly, all of which use ResNet-18 as their backbone. For HMDB51 and UCF101, we crop and resize the frame to  $112 \times 112$ . For Kinetics-400, the frames are cropped and resized to  $56 \times 56$ , considering memory and computation consumption. The default data augmentation we use is resized crop and horizontal flip.

### 4.1. Evaluation Protocol

In this section, we examine the different settings of the evaluation process, including the training schedule, data augmentation, and loss function. Through experiments, we em-

<span id="page-5-3"></span><span id="page-5-1"></span>

| Loss | Soft Label |        | Multi-SL    |        |
|------|------------|--------|-------------|--------|
|      | KL         | MSE-GT | KL          | MSE-GT |
| EDC† | 4.8        | 6.0    | 5.5         | 7.9    |
| DATM | 11.3       | 13.7   | 14.7        | 15.4   |
| RDED | 15.0       | 14.3   | <b>18.2</b> | 17.7   |

Table 3. Ablation of Loss Function. Data: UCF101. MSE-GT loss generally outperforms KL loss, except RDED. For not affecting the comparison between method, we choose MSE-GT loss for simplification.  $EDC^{\dagger}$  is a simple version of EDC without category-wise matching.

<span id="page-5-0"></span>

| $T_c$                                                                                                      | 4    | 8           | 16   | 24   | All  |
|------------------------------------------------------------------------------------------------------------|------|-------------|------|------|------|
| Acc %                                                                                                      | 33.5 | <b>43.7</b> | 42.8 | 42.3 | 52.6 |
| (a) <b>Pure Temporal Compression</b> ( $N_c = N$ ), Dataset:<br>UCF101. All is the result on full dataset. |      |             |      |      |      |

(b) More Samples (IPC) vs More Frames  $(T_c)$ 

Table 4. Temporal Study. Tab. [4a](#page-5-0) maintains the number of instances and ablates the condensed frame number  $(T_c)$ . Tab. [4b](#page-5-0) shows that instance diversity is important than temporal diversity.

phasize the importance of using a consistent evaluation setting and propose the evaluation protocol.

Training Schedule. Figure [3](#page-4-1) shows the loss curve of the evaluation model. We select DATM and RDED as representatives for dataset distillation and sample selection, respectively, and try two training schedules spanning 300 and 600 epochs. It is evident that at 300 epochs, the evaluation model do not converge on either compressed dataset. Moreover, the trend of the curves suggests that the model has not fully converged even at 600 epochs. However, the purpose of dataset condensation requires short training schedule of the evaluation network. Thus, we set 300 epochs as the default setting and make the number of iterations around 20% of the original one by adjusting the batch size.

Furthermore, the loss curves of the evaluation networks show that the loss value for DATM is lower than that for RDED. This suggests that the condensed dataset from DATM may be overfitting to the network architecture, which could partially explain its worse performance.

Augmentation and Loss Function. Table [2](#page-4-2) presents an ablation study on the use of CutMix, a strong data augmentation applied in G-VBSM [\[25\]](#page-10-24). Table [3](#page-5-1) ablates two loss functions widely used in previous dataset condensation works [\[25\]](#page-10-24): KL-Divergence and MSE-GT. All of the experiments are done in the setting of IPC=1 in UCF101. We show the results of three condensation algorithms with different label strategies and make the following observations:

<span id="page-5-2"></span>

| Sampling     | Interpolation | <b>RDED</b> | EDC         |
|--------------|---------------|-------------|-------------|
| None         | X             | 11.2        | 10.0        |
| Segment      | X             | 10.6        | 10.9        |
| Slide-window | X             | <b>11.5</b> | <b>12.0</b> |
| Slide-window | ✓             | 9.0         | 11.8        |

Table 5. Sampling and Interpolation. Metric: top5 accuracy in K400. Slide-window sampling outperforms segment sampling and brings larger improvements on synthetic data (EDC). Linear interpolation does not work.

(1) Using MSE-GT loss without CutMix augmentation generally leads to better performance. Through all the comparison groups, label strategies contribute up to an 8.6% improvement, followed by 3.3% from augmentation and 2.4% from loss functions, in that order.

(2) Whatever the evaluation setting, RDED performs better than DATM, and  $EDC^{\dagger}$  performs worst. That is to say the comparison between methods is consistent through different evaluation settings.

(3) Although RDED generally performs best under the same setting, DATM without CutMix provides 12.1% accuracy surpassing the performance of RDED with CutMix (11.0%) under soft labels. There remains such examples. This shows that evaluation settings can affect the conclusion of comparison.

To conclude, evaluation settings for dataset condensation makes a great influence to the final metric, sometimes even larger than the algorithm itself. Thus, it is urgent to establish an evaluation protocol for video dataset condensation. Following our experiments, we adopted a training regimen of 300 epochs, excluded CutMix, and used MES-GT loss for our evaluation protocol. To reduce training time, we adjust the batch size to ensure the number of iterations is around 20% of that required when training on the original dataset.

### 4.2. Temporal Processing

All the experiments so far save 8 frames for each video of condensed datasets which is consistent with the input clip of the video backbone we used. Thus, the sampling strategy and interpolation module are negligible. In this subsection, we conduct a seriese of experiments focused on temporal processing, including strategies for temporal condensation and the acquisition of clips from videos.

Table [4a](#page-5-0) shows the condensation results on UCF101 of RDED when maintaining the number of videos. We can observe that videos of 8 frames achieve the best results 43.7%. The performance decreases as we add the number of  $T_c$ . When  $T_c$  is set to 4, we add a linear interpolation to restore it to the length of input clip. This may lead to the failure of its result. It will be further analyzed in Tab. [5.](#page-5-2)

Table [4b](#page-5-0) shows the ablation between IPC and  $T_c$ . They

<span id="page-6-2"></span><span id="page-6-1"></span>

| Dataset              | IPC         | Ratio %     | HMDB51      |             |             | UCF101      |             |             | Kinetics-400 |             |    |
|----------------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|--------------|-------------|----|
|                      |             |             | 1           | 5           | 10          | 1           | 5           | 10          | 1            | 5           | 10 |
|                      |             | 1.2         | 5.9         | 11.8        | 0.4         | 0.9         | 4.6         | 0.05        | 0.23         | 0.47        |    |
| Full Dataset         |             | 26.08       |             |             | 52.56       |             |             | 44.6        |              |             |    |
| Sample Selection     | Random      | 8.3         | 15.6        | 17.0        | 14.7        | 26.9        | 31.4        | 7.1         | 11.1         | 14.1        |    |
|                      | Herding     | 10.6        | 12.2        | 15.9        | 13.0        | 22.8        | 27.7        | 6.1         | 11.3         | 13.8        |    |
|                      | <b>RDED</b> | <b>12.0</b> | <b>15.7</b> | <b>18.0</b> | <b>17.7</b> | <b>29.4</b> | <b>33.5</b> | <b>11.9</b> | <b>16.5</b>  | <b>18.9</b> |    |
| Dataset Distillation | DM† [36]    | 6.0         | 8.2         | -           | -           | -           | -           | 6.3         | 7.0          | -           |    |
|                      | MTT† [36]   | 6.5         | 8.9         | -           | -           | -           | -           | 6.3         | 11.5         | -           |    |
|                      | FRePo† [36] | 8.6         | 10.3        | -           | -           | -           | -           | -           | -            | -           |    |
|                      | <b>EDC</b>  | 6.9         | 8.9         | 12.5        | 12.0        | 22.2        | 24.3        | 10.9        | 11.2         | 12.6        |    |
|                      | <b>DATM</b> | 9.0         | 12.9        | 14.2        | 15.4        | 27.2        | 31.7        | 12.1        | 16.7         | 18.5        |    |

Table 6. Results of Previous SOTA and Our Methods on HMDB51, UCF101 and K400. Top-5 accuracy for K400 and Top-1 accuracy for the others are reported. The upper and lower sections of the table shows results for sample selection and dataset distillation, respectively. Underlined values indicate the best results within its category (selection or distillation), while bold values represent the overall best result. IPC: Instance(s) Per Class, Ratio: total condensation ratio defined in Sec. [3.1.](#page-2-0) † indicates the methods come from previous work [\[36\]](#page-11-7).

<span id="page-6-0"></span>Image /page/6/Figure/2 description: The image is a line graph titled "Dataset Condensation on UCF101". The x-axis is labeled "Instance Per Class (IPC)" and ranges from 1 to 20. The y-axis is labeled "Accuracy(%)" and ranges from 0 to 35. There are four lines plotted: EDC (blue circles, dashed line), DATM (green squares, solid line), RDED (orange diamonds, solid line), and a line representing "Multi Soft label" (black solid line) and "Hard label" (black dashed line). The EDC line shows accuracies of approximately 12, 22, 24, and 28 at IPC values of 1, 5, 10, and 20 respectively. The DATM line shows accuracies of approximately 15, 27, 31, and 36 at IPC values of 1, 5, 10, and 20 respectively. The RDED line shows accuracies of approximately 18, 19, 34, and 38 at IPC values of 1, 5, 10, and 20 respectively. The "Hard label" line shows accuracies of approximately 7, 18, 21, and 29 at IPC values of 1, 5, 10, and 20 respectively. The "Multi Soft label" line is not clearly distinguishable from the other lines in the provided image, but it appears to be a solid black line.

Figure 4. Performance curve of various condensation methods with different relabeling strategies on UCF101, illustrating the comparison among condensation methods and highlighting the significant impact of relabeling strategies.

separately measure the condensation ratio of instance and time. We maintain the iteration number for evaluation model in the ablation. For longer temporal duration, experiments on K400 is reported here. On both datasets, we can find instance diversity is more important than temporal diversity for video dataset condensation.

Table [5](#page-5-2) show different temporal formation for RDED and EDC on K400. The first row is the baseline setting that we used so far. Segment is the video formation proposed in the previous work [\[36\]](#page-11-7). It segments video into multiple segments and matches the condensed segment with the original respectively. Slide-window sampling is proposed in Sec. [3.1.](#page-2-0) We also try interpolation in Tab. [5.](#page-5-2) These two operations are consistent in the condensation and evaluation process. We make the following observations:

(1) Our proposed slide-window brings improvements on both methods. Especially for EDC, it ourperforms the baseline by 2%. This may bring by the coherent optimization of different windows from the condensed video.

(2) Adding Interpolation brings performance drop, especially for sample selection method RDED. Considering the naive linear interpolation method used here, maybe it does not accord with the distribution of real data. The situation is much better for EDC, where the 0.2% drop for top5 accuracy is almost within random fluctuation.

(3) Performance for EDC is better than RDED in the latter three rows. It implies that special processing of temporal dimension needs co-optimization in the process of condensation. This indicates that there is still significant room for improvement in video dataset distillation.

#### 4.3. Main Results and Analysis

One of our main motivations is to compare previous image dataset condensation algorithms on video datasets. Figure [4](#page-6-0) intuitively shows their performance on UCF101 with increasing IPCs. From the figure, we can observe that RDED with multi soft label generally outperforms the other two methods. Additionally, the relabel strategies significantly impact the results, bringing huge improvements even more notable than methods.

We further show the results on three datasets in Tab. [6](#page-6-1) in this section. The full dataset indicate the accuracy of the network trained on the full dataset. Following previous work [\[36\]](#page-11-7), we report the top1 accuracy for HMDB51 and UCF101, and top5 accuracy for K400.

As noted in Deepcore [\[9\]](#page-10-28), random sampling remains a strong baseline when compared to traditional sample selection methods such as Herding. RDED and DATM achieve the best performance within their respective categories (selection and distillation). While RDED generally outperforms DATM across most situations, DATM shows superior performance when the condensation ratio is extremely

<span id="page-7-2"></span><span id="page-7-0"></span>

|             | <b>Evaluation Network</b> |             |             |             | Candidates (Backbone) |         |     |          | Evaluation Model |
|-------------|---------------------------|-------------|-------------|-------------|-----------------------|---------|-----|----------|------------------|
|             | MiniC3D                   | R(2+1)D     | I3D         | SlowOnly    | MiniC3D               | R(2+1)D | I3D | SlowOnly |                  |
| Random      | 14.7                      | 8.8         | 9.1         | 13.7        | ✓                     |         |     |          | 10.1             |
| Herding     | 13.0                      | 5.7         | 8.7         | 12.3        | ✓                     | ✓       |     |          | 8.7              |
| <b>RDED</b> | <b>17.7</b>               | <b>11.7</b> | <b>14.1</b> | <b>21.1</b> | ✓                     |         | ✓   |          | 11.7             |
| <b>EDC</b>  | 12.0                      | 3.7         | 8.7         | 7.4         | ✓                     |         |     | ✓        | 12.1             |
| <b>DATM</b> | 15.4                      | 7.0         | 9.3         | 15.0        | ✓                     |         | ✓   | ✓        | <b>12.4</b>      |

Table 7. Cross-architecture generalization on UCF101 with IPC=1. MiniC3D is used as the condensation network and all the four networks are used for evaluation.

low. On one hand, this indicates that current dataset distillation methods face optimization challenges, achieving superior results over non-optimized methods only when the number of learnable parameters is relatively small. On the other hand, in the era of big data, the continuous expansion of both model and dataset sizes has led to an increasing demand for higher condensation ratios. Dataset distillation offers a more efficient representation of data, which can significantly accelerate model training. This capability holds great potential for the future, as it can address the growing computational and storage challenges associated with large-scale data and models. Last but not least, our methods achieve state-of-art results on all the three datasets, outperforming all methods proposed in the previous work [\[36\]](#page-11-7).

Cross-Architecture Generalization. A key attribute of a condensed dataset is its capacity to generalize effectively to various unseen architectures [\[5\]](#page-10-29). Previous experiments performed dataset condensation using MiniC3D and evaluated it on the same architecture. In this subsection, we evaluate the cross-architecture generalization by considering additional networks architectures for evaluation. As presented in Table [7,](#page-7-0) RDED outperforms other methods on whatever evaluation network. The rankings of different methods are consistent through different evaluation networks, and all of them shows good generalization ability.

### 4.4. Ablation of Condensation Models

In the dataset condensation pipeline, neural networks serve two key roles: aiding the condensation process and being trained on the condensed dataset to evaluate the effectiveness of the condensation algorithm. Previous experiments use MiniC3D for both roles. However, for dataset distillation, utilizing multiple condensation models can typically boost the performance. This subsection ablates the choice of condensation models for dataset distillation on UCF101.

Table [8](#page-7-1) tries MiniC3D, R(2+1)D, I3D and SlowOnly as the condensation models. MiniC3D is same as that used in [\[36\]](#page-11-7), and the latter three model uses the ResNet-18 backbone. It can be seen that I3D and SlowOnly can boost the performance with MiniC3D, but R(2+1)D has a negative influence. This may be because  $R(2+1)D$  explicitly separates

<span id="page-7-1"></span>

Table 8. Choice of Condensation model. Results of EDC on UCF101 about candidate backbones in the recover process.

spatial and temporal modeling, creating a significant structural difference from MiniC3D. As a result, it is challenging to jointly optimize condensed data using both architectures simultaneously. Furthermore, the final row demonstrates that the improvements achieved through the integration of I3D and SlowOnly can be combined.

## 5. Limitations

One limitation of this study on video dataset condensation is that while it effectively adapts image-based condensation methods to video data, it may not fully capture the complexities inherent in diverse video datasets, such as variations in motion dynamics and temporal dependencies. Although DATM demonstrates impressive results, its resource consumption remains significantly high. Future research could focus on reducing the storage and time requirements associated with this method. Moreover, experiments indicate that the optimization challenges of dataset distillation remain unresolved, particularly for video data where the additional temporal dimension complicates the process. Addressing these optimization issues is essential to fully realize the potential of dataset distillation.

## 6. Conclusion

In this work, we extend three significant dataset condensation methods from images to video data, each representing a distinct category of method. Given the complexity of evaluating dataset condensation methods, our experiments revealed that the evaluation setup significantly impacts the final metrics of these algorithms. Therefore, we propose a unified evaluation protocol to ensure consistent and reliable assessments. Building upon this evaluation protocol, we further investigated the methods for processing temporal sequences in videos. Our experiments reveal that sample diversity is more critical than temporal diversity. Consequently, we introduced a co-optimized slide-window sampling method to effectively compress temporal information. Finally, we conducted a comprehensive comparison of various dataset condensation algorithms. Our experiments reveal that relabeling strategies significantly impact the performance of condensed data. While sample selection meth<span id="page-8-1"></span>ods generally outperform distillation methods, distillation methods still maintain an advantage at extremely low compression ratios. We hope our study will foster future research on video dataset condensation.

## Appendix

### A. Details about EDC

EDC simplifies distribution matching to fine-grained statistical matching, significantly reducing the computational overhead of the algorithm. From the definition of EDC [\[24\]](#page-10-11), there are two specific approaches to Statistical Matching:

**Definition A.1** Given N random samples  $\{x_i\}_{i=1}^N$  with an unknown distribution  $p_{mix}(x)$ , we define two forms to statis*tical matching. Form (1): involves synthesizing* M *distilled*  $samples \{y_i\}_{i=1}^M$ , where  $M \ll N$ , ensuring that the variances and means of both  $\{x_i\}_{i=1}^N$  and  $\{y_i\}_{i=1}^M$  are consis*tent. Form (2): treats*  $p_{mix}(x)$  *as a GMM with* **C** *components. For random samples*  $\{x_i^j\}_{i=1}^{N_j}$   $(\sum_j N_j = N)$  within *each component*  $c_j$ , we synthesize  $M_j$  ( $\sum_j M_j = M$ ) dis*tilled samples*  $\{y_i^j\}_{i=1}^{M_j}$ , where  $M_j \ll N_j$ , to maintain the *consistency of variances and means between*  $\{x_i^j\}_{i=1}^{N_j}$  and  ${y_i^j}_{i=1}^{M_j}$ .

For dataset condensation, Form (1) can be viewed as matching the statistics of the whole datasets, while Form(2) divides data into several clusters according to the label. Combining both forms to match statistics, the matching loss of EDC is defined as followed:

$$
\mathcal{L}_{syn} = \sum_{k} (\alpha(||p(\mu|\phi_k(\mathcal{X}^S)) - p(\mu|\phi_k(\mathcal{X}^T))||_2
$$

$$
+||p(\sigma^2|\phi_k(\mathcal{X}^S)) - p(\sigma^2|\phi_k(\mathcal{X}^T))||_2) \quad \text{#Form (1)}
$$

$$
+ (1 - \alpha) \sum_{i} p(c_i)(||p(\mu|\phi_k(\mathcal{X}^S), c_i) - p(\mu|\phi_k(\mathcal{X}^T), c_i)||_2
$$

$$
+||p(\sigma^2|\phi_k(\mathcal{X}^S), c_i) - p(\sigma^2|\phi_k(\mathcal{X}^T, c_i))||_2)), \quad \text{#Form (2)}
$$
(2)

where  $C$  represents the total number of components,  $c_i$  indicates the *i*-th component within a GMM,  $\alpha$  is a coefficient for adjusting the balance and  $\phi_k$  is the map from input data space to k-th layer space from the condensation model pool. The total loss function  $\mathcal{L}_{syn}$  is designed to effectively regulate the information density of  $\mathcal{X}^{\mathcal{S}}$  and to align the distribution of  $\mathcal{X}^{\mathcal{S}}$  with that of  $\mathcal{X}^{\mathcal{T}}$ . Operationally, each category in the original dataset is mapped to a distinct component in the GMM framework.

### B. Evaluation Protocol

For fair comparison among different types of methods, we propose a unified evaluation protocol. We will release our

<span id="page-8-0"></span>

| Sampling     | Interpolation | RDED        | EDC         | DATM       |
|--------------|---------------|-------------|-------------|------------|
| None         | x             | 11.2        | 10.0        | 7.2        |
| Segment      | x             | 10.6        | 10.9        | 7.3        |
| Slide-window | x             | <b>11.5</b> | <b>12.0</b> | <b>8.5</b> |
| Slide-window | ✓             | 9.0         | 11.8        | 5.5        |

Table 9. Sampling and Interpolation†. Metric: top5 accuracy in K400. Complete version for Table [5](#page-5-2)

evaluation code soon, and details are introduced in this section for quick review.

**Optimizer:** we use Adam optimizer with base learning rate 0.001, betas [0.9, 0.999] and weight decay 0.01.

Scheduler: we train the evaluation for 300 epochs with a cosine learning rate scheduler.

Batch size: we adjust batch size according to dataset and IPC to make sure the iteration number is around 20% of the original one , following the equation:

$$
BS = base \times IPC,
$$
 (3)

where BS is the training batch size, and base is the batch size when IPC is equal to one. The base for HMDB51 and UCF101 is set to 10, while that for K400 is set to 40.

Augmentation: we apply resized crop and horizontal flip when training evaluation models.

Loss Function: we use MSE-GT as the loss function.

### C. More Results

Table [5](#page-5-2) does not include results from DATM for time restrictions. We have completed results of DATM in Tab. [9.](#page-8-0) It can be seen that slide-window brings an improvement of 1.3 accuracy, which is much more obvious than segment sampling, and simple linear interpolation brings negative effects to the results. The results of DATM verify the effectiveness of slide-window sampling and the limitation of simple linear interpolation just as former analysis.

### D. Visualization

We show the visualization of RDED and EDC in Fig. [5](#page-9-0) and Fig. [6,](#page-9-1) representing selection and distillation method respectively. Compared with RDED (real data), EDC captures some crucial patterns through distillation. While there is some redundant information in the temporal dimension.

<span id="page-9-0"></span>Image /page/9/Picture/0 description: The image displays five rows of video frames. The top row shows a woman applying makeup to her eyes. The second row shows a woman applying lipstick. The third row shows a person walking in a wooded area. The fourth row shows a baby crawling. The bottom row shows a gymnast performing on a balance beam.

Figure 5. Visualization of RDED for UCF101 IPC=1

<span id="page-9-1"></span>Figure 6. Visualization of EDC for UCF101 IPC=1

## References

- <span id="page-10-26"></span>[1] Gedas Bertasius, Heng Wang, and Lorenzo Torresani. Is space-time attention all you need for video understanding? In *ICML*, page 4, 2021. [3](#page-2-1)
- <span id="page-10-15"></span>[2] João Carreira and Andrew Zisserman. Quo vadis, action recognition? a new model and the kinetics dataset. In *CVPR*, pages 4724–4733, 2017. [2,](#page-1-1) [3,](#page-2-1) [5](#page-4-3)
- <span id="page-10-2"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022. [1,](#page-0-1) [3](#page-2-1)
- <span id="page-10-16"></span>[4] Yutian Chen, Max Welling, and Alex Smola. Super-samples from kernel herding. In *Proceedings of the Twenty-Sixth Conference on Uncertainty in Artificial Intelligence*, pages 109–116, 2010. [2](#page-1-1)
- <span id="page-10-29"></span>[5] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. DC-BENCH: Dataset condensation benchmark. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022. [8](#page-7-2)
- <span id="page-10-3"></span>[6] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *ICML*, pages 6565–6590, 2023. [1,](#page-0-1) [3](#page-2-1)
- <span id="page-10-4"></span>[7] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In *NeurIPS*, 2022. [1](#page-0-1)
- <span id="page-10-25"></span>[8] Christoph Feichtenhofer, Haoqi Fan, Jitendra Malik, and Kaiming He. Slowfast networks for video recognition. In *ICCV*, pages 6202–6211, 2019. [3](#page-2-1)
- <span id="page-10-28"></span>[9] Chengcheng Guo, Bo Zhao, and Yanbing Bai. Deepcore: A comprehensive library for coreset selection in deep learning, 2022. [4,](#page-3-1) [7](#page-6-2)
- <span id="page-10-5"></span>[10] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *The Twelfth International Conference on Learning Representations*, 2024. [1,](#page-0-1) [2,](#page-1-1) [3](#page-2-1)
- <span id="page-10-20"></span>[11] Ziheng Jiang, Chiyuan Zhang, Kunal Talwar, and Michael C Mozer. Characterizing structural regularities of labeled data in overparameterized models. In *International Conference on Machine Learning*, 2021. [2](#page-1-1)
- <span id="page-10-19"></span>[12] Krishnateja Killamsetty, Sivasubramanian Durga, Ganesh Ramakrishnan, Abir De, and Rishabh Iyer. Grad-match: Gradient matching based data subset selection for efficient deep model training. In *International Conference on Machine Learning*, 2021. [2](#page-1-1)
- <span id="page-10-21"></span>[13] Krishnateja Killamsetty, Durga Sivasubramanian, Ganesh Ramakrishnan, and Rishabh Iyer. Glister: Generalization based data subset selection for efficient and robust learning. In *Association for the Advancement of Artificial Intelligence*, 2021. [2](#page-1-1)
- <span id="page-10-6"></span>[14] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *ICML*, 2022. [1](#page-0-1)
- <span id="page-10-13"></span>[15] H. Kuehne, H. Jhuang, E. Garrote, T. Poggio, and T. Serre. Hmdb: A large video database for human motion recognition. In *ICCV*, pages 2556–2563, 2011. [2,](#page-1-1) [5](#page-4-3)

- <span id="page-10-7"></span>[16] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *NeurIPS*, 2022. [1](#page-0-1)
- <span id="page-10-8"></span>[17] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, 2022. [1,](#page-0-1) [2](#page-1-1)
- <span id="page-10-1"></span>[18] Noel Loo, Ramin Hasani, Mathias Lechner, Alexander Amini, and Daniela Rus. Dataset distillation fixes dataset reconstruction attacks. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2024. [1](#page-0-1)
- <span id="page-10-18"></span>[19] Baharan Mirzasoleiman, Jeff Bilmes, and Jure Leskovec. Coresets for data-efficient training of machine learning models. In *International Conference on Machine Learning*, 2020.  $\mathcal{D}$
- <span id="page-10-9"></span>[20] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [1,](#page-0-1) [2](#page-1-1)
- <span id="page-10-10"></span>[21] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021. [1,](#page-0-1) [2](#page-1-1)
- <span id="page-10-23"></span>[22] Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training. In *Advances in Neural Information Processing Systems*, 2021. [2](#page-1-1)
- <span id="page-10-17"></span>[23] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *International Conference on Learning Representations*, 2018. [2](#page-1-1)
- <span id="page-10-11"></span>[24] Shitong Shao, Zikai Zhou, Huanran Chen, and Zhiqiang Shen. Elucidating the design space of dataset condensation. *arXiv preprint arXiv:2404.13733*, 2024. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-1) [9](#page-8-1)
- <span id="page-10-24"></span>[25] Xindong Zhang Shitong Shao, Zeyuan Yin and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. *arXiv preprint arXiv:2311.17950*, 2023. [3,](#page-2-1) [4,](#page-3-1) [6](#page-5-3)
- <span id="page-10-14"></span>[26] Khurram Soomro, Amir Roshan Zamir, and Mubarak Shah. UCF101: A dataset of 101 human actions classes from videos in the wild. *CoRR*, abs/1212.0402, 2012. [2,](#page-1-1) [5](#page-4-3)
- <span id="page-10-0"></span>[27] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *Proceedings of the International Conference on Machine Learning (ICML)*, pages 9206–9216, 2020. [1](#page-0-1)
- <span id="page-10-12"></span>[28] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2024. [1,](#page-0-1) [2,](#page-1-1) [3,](#page-2-1) [4](#page-3-1)
- <span id="page-10-22"></span>[29] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018. [2](#page-1-1)
- <span id="page-10-27"></span>[30] Zhan Tong, Yibing Song, Jue Wang, and Limin Wang. Videomae: Masked autoencoders are data-efficient learners for self-supervised video pre-training. In *NeurIPS*, pages 10078–10093. Curran Associates, Inc., 2022. [3](#page-2-1)

- <span id="page-11-11"></span>[31] Du Tran, Lubomir Bourdev, Rob Fergus, Lorenzo Torresani, and Manohar Paluri. Learning spatiotemporal features with 3d convolutional networks. In *ICCV*, pages 4489–4497, 2015. [3](#page-2-1)
- <span id="page-11-12"></span>[32] Du Tran, Heng Wang, Lorenzo Torresani, Jamie Ray, Yann LeCun, and Manohar Paluri. A closer look at spatiotemporal convolutions for action recognition. In *CVPR*, pages 6450– 6459, 2018. [3](#page-2-1)
- <span id="page-11-1"></span>[33] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022. [1,](#page-0-1) [3](#page-2-1)
- <span id="page-11-13"></span>[34] Mengmeng Wang, Jiazheng Xing, and Yong Liu. Actionclip: A new paradigm for video action recognition. *CoRR*, abs/2109.08472, 2021. [3](#page-2-1)
- <span id="page-11-0"></span>[35] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2](#page-1-1)
- <span id="page-11-7"></span>[36] Ziyu Wang, Yue Xu, Cewu Lu, and Yong-Lu Li. Dancing with still images: Video distillation via static-dynamic disentanglement, 2024. [2,](#page-1-1) [3,](#page-2-1) [5,](#page-4-3) [7,](#page-6-2) [8](#page-7-2)
- <span id="page-11-6"></span>[37] Xindi Wu, Byron Zhang, Zhiwei Deng, and Olga Russakovsky. Vision-language dataset distillation. *Transactions on Machine Learning Research*, 2024. [1](#page-0-1)
- <span id="page-11-4"></span>[38] Beining Yang, Kai Wang, Qingyun Sun, Cheng Ji, Xingcheng Fu, Hao Tang, Yang You, and Jianxin Li. Does graph distillation see like vision dataset counterpart? In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023. [1](#page-0-1)
- <span id="page-11-10"></span>[39] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *Thirty-seventh Conference on Neural Information Processing Systems*, 2023. [3,](#page-2-1) [4](#page-3-1)
- <span id="page-11-5"></span>[40] Tianle Zhang, Yuchen Zhang, Kun Wang, Kai Wang, Beining Yang, Kaipeng Zhang, Wenqi Shao, Ping Liu, Joey Tianyi Zhou, and Yang You. Two trades is not baffled: Condensing graph via crafting rational gradient matching. *arXiv preprint arXiv:2402.04924*, 2024. [1](#page-0-1)
- <span id="page-11-2"></span>[41] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021. [1,](#page-0-1) [3](#page-2-1)
- <span id="page-11-9"></span>[42] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023. [3](#page-2-1)
- <span id="page-11-8"></span>[43] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020. [2](#page-1-1)
- <span id="page-11-3"></span>[44] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *arXiv preprint arXiv:2206.00719*, 2022. [1,](#page-0-1) [2](#page-1-1)
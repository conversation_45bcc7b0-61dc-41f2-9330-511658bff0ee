# CaT: Balanced Continual Graph Learning with Graph Condensation

<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>

*School of Electrical Engineering and Computer Science, The University of Queensland,*

{yilun.liu, r.qiu, helen.huang}@uq.edu.au

*Abstract*—Continual graph learning (CGL) is purposed to continuously update a graph model with graph data being fed in a streaming manner. Since the model easily forgets previously learned knowledge when training with new-coming data, the catastrophic forgetting problem has been the major focus in CGL. Recent replay-based methods intend to solve this problem by updating the model using both (1) the entire new-coming data and (2) a sampling-based memory bank that stores replayed graphs to approximate the distribution of historical data. After updating the model, a new replayed graph sampled from the incoming graph will be added to the existing memory bank. Despite these methods are intuitive and effective for the CGL, two issues are identified in this paper. Firstly, most samplingbased methods struggle to fully capture the historical distribution when the storage budget is tight. Secondly, a significant data imbalance exists in terms of the scales of the complex newcoming graph data and the lightweight memory bank, resulting in unbalanced training. To solve these issues, a *Condense and Train (CaT)* framework is proposed in this paper. Prior to each model update, the new-coming graph is condensed to a small yet informative synthesised replayed graph, which is then stored in a *Condensed Graph Memory* with historical replay graphs. In the continual learning phase, a *Training in Memory* scheme is used to update the model directly with the *Condensed Graph Memory* rather than the whole new-coming graph, which alleviates the data imbalance problem. Extensive experiments conducted on four benchmark datasets successfully demonstrate superior performances of the proposed CaT framework in terms of effectiveness and efficiency. The code has been released on [https://github.com/superallen13/CaT-CGL.](https://github.com/superallen13/CaT-CGL)

*Index Terms*—graph condensation, continual graph learning

## I. INTRODUCTION

Compared to traditional graph representation learning that treats graphs as static data and trains a model with the data as a whole, continual graph learning (CGL) deals with a more practical scenario where the graph data is emerged continually and is fed into the model in a streaming manner [\[4\]](#page-8-0), [\[21\]](#page-9-0), [\[35\]](#page-9-1).

For the CGL problem, the most significant challenge is to address a catastrophic forgetting problem that the model easily forgets the knowledge learned from the historical graph data while overemphasising the incoming data [\[16\]](#page-9-2), [\[37\]](#page-9-3), [\[44\]](#page-9-4). Due to hardware limitations in storage and computation, the catastrophic forgetting problem happens to CGL models when it is impractical to access the entire historical graph during training. If conventional Graph Neural Networks (GNNs) [\[11\]](#page-9-5), [\[27\]](#page-9-6), [\[33\]](#page-9-7) are directly employed to continually learn from incoming graphs, the model performance for data from the historical distribution tends to deteriorate due to the distribution shift between the historical and the current graphs. Recently, a few

<span id="page-0-0"></span>Image /page/0/Figure/10 description: The image displays two distinct sections. The left section is labeled "Memory Bank" and contains a rounded rectangle filled with "Historical Replayed Graphs." Inside this rectangle, there are two small graphs composed of colored nodes and dashed lines representing connections. The first graph has three green nodes connected to a central green node. The second graph has three red nodes connected to a central red node, with another red node connected to one of the outer red nodes. A dotted line indicates that there are more graphs in the memory bank. The right section is labeled "Incoming Graph" and shows a larger graph enclosed by a dashed, irregular outline. This graph consists of white, orange, and blue nodes connected by solid lines. The blue nodes appear to be centrally located and highly connected, with orange nodes surrounding them, and white nodes on the periphery.

Image /page/0/Figure/11 description: The image displays two line graphs side-by-side, illustrating data imbalance in replay-based CGL methods. The top title is "(a) Data imbalance in replay-based CGL methods." The left graph, labeled "(b) Imbalance in Arxiv," shows "accuracy (%)" on the y-axis ranging from 10 to 90, and "tasks" on the x-axis ranging from 6 to 10. It plots "accuracy of task 0" (orange dashed line), "accuracy of task 5" (yellow dashed line), and "accuracy of task 6" (green dashed line), along with "incoming graph size" (dark blue solid line) on a secondary y-axis ranging from 0 to 20000. The right graph, labeled "(c) Imbalance in Reddit," shows "accuracy (%)" on the y-axis ranging from 0 to 100, and "tasks" on the x-axis ranging from 4 to 8. It plots "accuracy of task 2" (orange dashed line), "accuracy of task 3" (yellow dashed line), and "accuracy of task 6" (green dashed line), along with "incoming graph size" (dark blue solid line) on a secondary y-axis ranging from 0 to 20000.

Fig. 1: Imbalanced learning problem in the replay-based CGL methods. (a) In the update phase of replay-based methods, both the replayed graphs in the memory bank and the incoming graph are used for training. Generally, the replayed graphs are significantly smaller than the incoming graph. In (b) and (c), the prediction accuracy of the CGL model on Arxiv and Reddit datasets is shown. When the size of the incoming graph is much larger than those of replayed graphs, such as Task 8 in Arxiv and Task 7 in Reddit, the model performance over data drawn from previous tasks will drastically decrease. This is due to the data imbalance issue in the continual learning.

attempts have been made to tackle this catastrophic forgetting problem, leveraging regularisation penalty [\[16\]](#page-9-2), architecture redesign [\[38\]](#page-9-8), and replayed graph [\[37\]](#page-9-3), [\[44\]](#page-9-4). As the champion of the three methods, replay-based CGL models store replayed graphs in a memory bank by sampling methods to maintain the historical distribution to tackle the catastrophic forgetting problem, resulting in improved performance and plasticity. For example, ER-GNN [\[16\]](#page-9-2) stores informative nodes from historical graphs in the memory bank, and SSM [\[37\]](#page-9-3) sparsifies the incoming graphs as replayed graphs.

Although replay-based methods are intuitive and effective, two major issues are observed during our study of these approaches. Firstly, to achieve a competitive performance, existing replay-based CGL methods usually require large spaces to store the historical information as much as possible. When the storage budget is limited, these memory banks would hardly present the complete picture of the distribution of historical data. Secondly, it is difficult to balance the model update training over incoming and replayed graphs since incoming graphs are generally much larger than replayed graphs in scale. Figure (Fig.) [1\(](#page-0-0)a) demonstrates the situation of imbalanced learning where the incoming graph is significantly larger than replayed graphs. Fig. [1\(](#page-0-0)b) and Fig. [1\(](#page-0-0)c) show that the model performance on the historical data drops when the incoming graph is significantly larger than replayed graphs.

In light of the discussion above, it is motivated to design a novel framework that can simultaneously improve the effectiveness and efficiency of the memory bank and balance the continual training for replay-based GCL methods. To generate small but informative replayed graphs, recent graph condensation techniques [\[9\]](#page-9-9), [\[10\]](#page-9-10), [\[17\]](#page-9-11) have demonstrated great potential, which can condense a graph into a smaller synthetic graph using differentiable methods. Compared to the sampling-based replayed graphs, the graph condensation has the merits of generating smaller and learnable replayed graphs without compromising the performance. Regarding the imbalanced training, it is difficult to maintain a balance when replayed graphs and the entire incoming graph are directly combined to train the model due to the nature of the imbalanced data scale. Instead, if the synthetic replayed graph derived from condensation methods is able to support the model training without sacrificing the performance, it is possible to bypass the usage of the entire incoming graph for a model update but rather purely rely on the synthetic graphs. To fulfil these two objectives, we propose a replaybased *Condense and Train (CaT)* framework for CGL. In the continual learning process, it maintains a small yet effective *Condensed Graph Memory (CGM)* that expands with the synthetic replayed graph condensed from the incoming graph before the model update. In the model update phase, the training strategy used for the proposed framework is *Training in Memory (TiM)* where the model only updates with the memory bank. The TiM ensures that the condensed synthetic graph has a similar size to replayed graphs, alleviating the imbalance issue. The contributions are as follows:

- For CGL problem, a novel framework CaT is proposed with a CGM module to reduce the size of replayed graphs and a TiM scheme to balance the continual training.
- CGM is derived from performing a graph condensation for the large incoming graphs using distribution matching.
- TiM is developed to balance the training using the large incoming graph and the small memory bank.
- Extensive experiments conducted on four benchmark datasets verify the state-of-the-art performance of CaT.

## II. RELATED WORK

### *A. Graph Neural Networks*

Graph Neural Networks (GNNs) are effective tools for graph-based tasks [\[14\]](#page-9-12), [\[34\]](#page-9-13). GCN [\[11\]](#page-9-5) employs Laplacian normalisation for message propagation. GAT [\[27\]](#page-9-6) is proposed to use the attention mechanism [\[26\]](#page-9-14) for message passing. SGC [\[33\]](#page-9-7) simplifies GCN by removing the non-linear activation layer. GraphSAGE [\[7\]](#page-9-15) uses node sampling to deal with large-scale graph representation learning.

### *B. Graph Continual Learning*

Graph continual learning is a task for handling streaming graph data. Continual learning has been studied in computer vision [\[1\]](#page-8-1), [\[12\]](#page-9-16), [\[15\]](#page-9-17), [\[18\]](#page-9-18). In graph area, CGL methods can be categorised into three branches: regularisation [\[16\]](#page-9-2), replay- [\[37\]](#page-9-3), [\[44\]](#page-9-4), and architecture-based [\[38\]](#page-9-8) methods. TWP [\[16\]](#page-9-2) preserves the topological information of historical graphs by adding regularisation. HPNs [\[38\]](#page-9-8) redesigns the architecture to 3-layer prototypes for representation learning. ER-GNN [\[44\]](#page-9-4) integrates memory-replay by storing representative nodes. SSM [\[37\]](#page-9-3), [\[39\]](#page-9-19) stores the sparisified subgraphs in the memory bank to preserve the structural information. Two recent benchmarks [\[13\]](#page-9-20), [\[36\]](#page-9-21) have been developed for CGL.

### *C. Graph Condensation*

Dataset condensation generates a small and synthetic dataset to replace the original dataset and to train a model with similar performance. Dataset condensation has been applied in computer vision [\[2\]](#page-8-2), [\[28\]](#page-9-22), [\[30\]](#page-9-23), [\[40\]](#page-9-24)–[\[42\]](#page-9-25). Recently, gradient matching has been applied to graph condensation, such as GCond [\[10\]](#page-9-10), DosCond [\[9\]](#page-9-9) and MCond [\[5\]](#page-9-26). DM [\[41\]](#page-9-27) aims to learn synthetic samples which have similar distribution with the original dataset to mimic sampling methods [\[3\]](#page-8-3), [\[24\]](#page-9-28), [\[31\]](#page-9-29). GCDM [\[17\]](#page-9-11) uses the distribution matching for graph condensation.

Recent attempts on computer vision have directly applied dataset condensation to continual learning [\[6\]](#page-9-30), [\[19\]](#page-9-31), [\[22\]](#page-9-32), [\[23\]](#page-9-33), [\[32\]](#page-9-34), although these methods follow the typical training scheme, which will fall into imbalanced training.

## III. PRELIMINARY

In the following, a bold lowercase letter denotes a vector, a bold uppercase letter denotes a matrix, a general letter denotes a scalar, and a scripted uppercase letter denotes a set.

### *A. Graph*

For a node classification problem, a graph is denoted as  $G = \{A, X, Y\}$ , where  $X \in \mathbb{R}^{n \times d}$  is the d-dimensional feature matrix for *n* nodes, and the adjacency matrix  $A \in$  $\mathbb{R}^{n \times n}$  denotes the graph structure. In this paper, the graph is undirected and unweighted.  $Y \in \mathbb{R}^{n \times 1}$  includes node labels from a class set C.

### *B. Graph Neural Networks*

Graph Neural Networks (GNNs) are tools for representation learning in node classification problems. The node representation in GNN is calculated by aggregating messages from neighbouring nodes. GNN can be represented as a function:

<span id="page-1-0"></span>
$$
E = \text{GNN}_{\theta}(A, X), \tag{1}
$$

where  $\theta$  is the model parameter and  $\mathbf{E} \in \mathbb{R}^{n \times d'}$  denotes  $d'$ dimensional node embeddings.

<span id="page-2-1"></span>Image /page/2/Figure/0 description: This figure illustrates two continual graph learning frameworks: (a) a typical replay-based CGL framework and (b) the CaT framework. Both frameworks process historical tasks and future tasks. The replay-based framework involves sampling from an incoming graph Gk-1 to create a memory bank Mk-2, which is then used for continual training of GNNk-1. The CaT framework utilizes a memory bank Mk-1, which is condensed from previous graphs, and then undergoes continual training with GNNk. Both frameworks involve graph representations (G, G-tilde), sampling, continual training, and graph neural network (GNN) models. The figure also shows the flow of information between historical tasks and future tasks, with specific notations like TiM (Task-independent Memory) and Condense.

Fig. 2: Typical replay-based CGL and CaT in class-IL. (a) A typical replay-based CGL framework trains the model GNN $_{k-1}$ directly using the memory bank  $\mathcal{M}_{k-2}$  and the incoming graph  $\mathcal{G}_{k-1}$ , and the sampling  $\tilde{\mathcal{G}}_{k-1}$  is added to  $\mathcal{M}_{k-1}$ . (b) CaT condenses the incoming graph  $\mathcal{G}_{k-1}$  first to have an updated memory bank  $\mathcal{M}_{k-1}$  and trains the model GNN $_{k-1}$  with  $\mathcal{M}_{k-1}$ .

## *C. Graph Condensation*

Graph condensation aims to synthesis a small graph  $G =$  $\{\tilde{A}, \tilde{X}, \tilde{Y}\}\$  for a large graph  $\mathcal{G} = \{A, X, Y\}$ . The model trained with the synthetic graph is expected to have a similar performance as with the original graph. This objective is:

<span id="page-2-2"></span>
$$
\min_{\tilde{\mathcal{G}}} \mathcal{L}(\mathcal{G}; \tilde{\theta}), \quad \text{s.t. } \tilde{\theta} = \argmin_{\theta} \mathcal{L}(\tilde{\mathcal{G}}; \theta), \tag{2}
$$

where  $\mathcal L$  is a task-related loss function, e.g., cross-entropy, and  $\theta$  is the parameter of GNN.

## *D. Node Classification in CGL*

In node classification of CGL, a model is required to handle K tasks  $\{\mathcal{T}_1, \mathcal{T}_2, \dots \mathcal{T}_K\}$ . For the  $k_{th}$  task  $\mathcal{T}_k$ , an incoming graph  $\mathcal{G}_k$  arrives and the model needs to be updated with  $\mathcal{G}_k$ while be tested on all previous graphs and the incoming graph. Following [\[16\]](#page-9-2), [\[37\]](#page-9-3), [\[44\]](#page-9-4), the setting is transductive learning and can be easily extended to inductive learning.

CGL problem has two different continual settings, task incremental learning (task-IL) and class incremental learning (class-IL). In task-IL, the model is only required to distinguish nodes in the same task. While in class-IL, the model is required to classify nodes from all tasks together. Class-IL is more challenging, and this paper focuses on this setting while also reporting the overall performance under task-IL.

## *E. Imbalanced learning in replay-based CGL methods*

Normally, for the replay-based CGL methods, when storing replayed graphs in the memory bank, there is a budget  $b$ limiting the maximum node number for every replayed graph. In the training phase, replayed graphs and the incoming graph are used to train the model together. However, when the size

of the incoming graph is significantly larger than the budget, the model will overemphasis on the incoming graph.

Current replay-based CGL methods use weighted loss to tackle the imbalance issue, which combines the current task  $\mathcal{T}_k$  loss  $\mathcal{L}(\mathcal{G}_k; \theta_k)$  and the replayed graph loss  $\mathcal{L}(\mathcal{M}_{k-1}; \theta_k)$ . For example, ER-GNN [\[16\]](#page-9-2) calculates weight by graph size:

$$
\ell_{\text{ER-GNN}} = \frac{n_{1:k-1}}{n_k + n_{1:k-1}} \mathcal{L}(\mathcal{G}_k; \theta_k) + \frac{n_k}{n_k + n_{1:k-1}} \mathcal{L}(\mathcal{M}_{k-1}; \theta_k),
$$
(3)

<span id="page-2-3"></span>where  $n_k$  and  $n_{1:k-1}$  are the node number of the incoming graph  $\mathcal{G}_k$  and of graphs in memory bank respectively. When  $n_{1:k-1} < n_k$ , the  $\mathcal{L}(\mathcal{M}_{k-1}; \theta_k)$  are assigned with a larger scale factor. The learning focus will be on the memory bank. SSM [\[37\]](#page-9-3) balances the size of each class:

<span id="page-2-0"></span>
$$
\ell_{\text{SSM}} = \sum_{c \in \mathcal{C}_k} \frac{1}{n_c} \mathcal{L}_c(\mathcal{G}_k; \theta_k) + \sum_{c \in \mathcal{C}_{0:k-1}} \frac{1}{n_c} \mathcal{L}_c(\mathcal{M}_{k-1}; \theta_k), \tag{4}
$$

where  $n_c$  is the number of nodes belonging to the class c and the learning will focus on graphs with less nodes.

Both balancing methods are faced with training problems during the continual learning process. In the class-IL setting, the imbalanced learning problem is mainly caused by the different sample sizes between different classes. ER-GNN will restrict the model from learning on the memory bank once the size of the entire memory bank exceeds the size of the incoming graph significantly. For example, in the Arxiv dataset, SSM will compromise the performance of the current task when the task is sensitive for directly scaling down the training loss. To keep the best performance of SSM, the balancing method using Equation (Eq.) [4](#page-2-0) will only be effective on extremely imbalanced datasets (e.g., Reddit and Products).

## IV. METHODOLOGY

This section describes the details of the proposed Condense and Train (CaT) framework. The comparisons between CaT and existing replay-based CGL methods are shown in Fig. [2.](#page-2-1) Existing replay-based CGL methods directly leverage the incoming graph for updating and storing the sampling of the incoming graph in the memory bank. CaT first condenses the incoming graph and updates the model with the condensed graphs instead of the whole incoming graph.

### *A. Condensed Graph Memory*

Condensed graph memory (CGM) is a memory bank that stores condensed synthetic graphs to approximate the historical data distribution. In this section, we develop graph condensation with distribution matching, aiming to maintain a similar data distribution for the synthetic data as the original data. This approach serves as a replayed graph generation method.

For Task  $\mathcal{T}_k$ , the incoming graph  $\mathcal{G}_k = \{A_k, X_k, Y_k\}$ , a condensed graph  $\tilde{\mathcal{G}}_k = \{ \tilde{\mathbf{A}}_k, \tilde{\mathbf{X}}_k, \tilde{\mathbf{Y}}_k \}$  is generated by graph condensation. Compared with Eq. [2,](#page-2-2) under the distribution matching scheme, the objective function of graph condensation here can be reformulated as follows:

$$
\tilde{\mathcal{G}}_k^* = \underset{\tilde{\mathcal{G}}_k}{\arg\min} \text{Dist}(\mathcal{G}_k, \tilde{\mathcal{G}}_k),\tag{5}
$$

where  $Dist(\cdot, \cdot)$  function calculates the distance between two graphs. Using distribution matching, the distance between two graphs is measured in the embedding space, where both graphs are encoded by the same graph encoder  $GNN_{\theta}$ :

$$
\tilde{\mathcal{G}}_k^* = \underset{\tilde{\mathcal{G}}_k}{\arg \min} \text{Dist}(\text{GNN}_{{\theta}_k}(\boldsymbol{A}_k, \boldsymbol{X}_k), \text{GNN}_{{\theta}_k}(\tilde{\boldsymbol{A}}_k, \tilde{\boldsymbol{X}}_k))
$$
(6)  
= 
$$
\underset{\tilde{\mathcal{G}}_k}{\arg \min} \text{Dist}(\boldsymbol{E}_k, \tilde{\boldsymbol{E}}_k),
$$
(7)

where  $\tilde{\mathcal{G}}^{*}_{k} = \{\tilde{\boldsymbol{A}}^{*}_{k}$  $_{k}^{*},\tilde{\boldsymbol{X}}_{k}^{*}$  $\{\boldsymbol{\tilde{Y}}_k^*\}$  is the optimal replayed graph with distribution close to the distribution of the incoming graph. Maximum mean discrepancy (MMD) is used to empirically calculate the distribution distance between two graphs. The objective is to find an optimal  $\tilde{\mathcal{G}}_k$  for MMD:

<span id="page-3-0"></span>
$$
\ell_{\text{MMD}} = \sum_{c \in \mathcal{C}_k} r_c \cdot ||\text{Mean}(\boldsymbol{E}_{k,c}) - \text{Mean}(\tilde{\boldsymbol{E}}_{k,c})||^2, \quad (8)
$$

where  $\mathcal{C}_k$  is the set of classes of nodes in  $\mathcal{G}_k$ ,  $\mathbf{E}_{c,k}$  and  $\mathbf{E}_{c,k}$  are the embedding matrix of the incoming graph and condensed graph, respectively, where all nodes' labels are  $c_k$ , and  $r_{c,k}$  =  $|\bm{E}_{c,k}|$  $\frac{E_{c,k}}{|E_k|}$  is the class ratio for class  $c_k$ .  $|\cdot|$  is the number of rows in a matrix. Mean $(\cdot)$  is the mean vector of the node embeddings.

To efficiently operate the condensation procedure, the random GNN encoders are employed here without training the GNNs. The objective of the distribution matching is to minimise the embedding distance in different embedding spaces given by GNNs with random parameters  $\theta_p$ :

$$
\min_{\tilde{\mathcal{G}}_k} \sum_{\theta_p \sim \Theta} \ell_{\text{MMD}, \theta_p},\tag{9}
$$

**Algorithm 1:** Condensed Graph Memory of  $\mathcal{T}_k$ 

<span id="page-3-1"></span>**Input:** Incoming graph  $\mathcal{G}_k = \{A_k, X_k, Y_k\}$ , budget  $b_k$  for the replayed graph  $\tilde{G}_k$ Output:  $\mathcal{\tilde{G}}_k = \{\tilde{\boldsymbol{A}}_k, \tilde{\boldsymbol{X}}_k, \tilde{\boldsymbol{Y}}_k\}$ 

- 1 Initialise labels of condensed graph  $\tilde{Y}_k$  by sampling from the label distribution of  $\mathcal{G}_k$ ;
- 2 Initialise node features of condensed graph  $\bar{X}_k$  by randomly sampling  $b_k$  node features from  $X_k$ ;
- 3 Initialise the adjacency matrix  $A_k$  with only self-loops; 4 for  $p \leftarrow 1$  to P do
- 5 | Initialise parameter of graph encoder  $\theta_p$ ;
- 6 **for**  $c_k \leftarrow 0$  to  $|\mathcal{C}_k| 1$  do  $7 \quad | \quad \left| \quad E_{c,k} = {\rm GNN}_{\theta_p}({\boldsymbol A}_k,{\boldsymbol X}_k) \; ; \quad \quad \ \ \star \; {\rm Eq.} \; \; 1 \; \; \star \, / \right.$  $7 \quad | \quad \left| \quad E_{c,k} = {\rm GNN}_{\theta_p}({\boldsymbol A}_k,{\boldsymbol X}_k) \; ; \quad \quad \ \ \star \; {\rm Eq.} \; \; 1 \; \; \star \, / \right.$  $7 \quad | \quad \left| \quad E_{c,k} = {\rm GNN}_{\theta_p}({\boldsymbol A}_k,{\boldsymbol X}_k) \; ; \quad \quad \ \ \star \; {\rm Eq.} \; \; 1 \; \; \star \, / \right.$  $\hat{\bm{s}} \quad | \quad \mid \quad \tilde{E}_{c,k} = \text{GNN}_{\theta_p}(\tilde{\bm{A}}_k,\tilde{\bm{X}}_k) \ ; \qquad \text{/*} \ \ \text{Eq.} \ \ 1 \ \ \text{*/}$  $\hat{\bm{s}} \quad | \quad \mid \quad \tilde{E}_{c,k} = \text{GNN}_{\theta_p}(\tilde{\bm{A}}_k,\tilde{\bm{X}}_k) \ ; \qquad \text{/*} \ \ \text{Eq.} \ \ 1 \ \ \text{*/}$  $\hat{\bm{s}} \quad | \quad \mid \quad \tilde{E}_{c,k} = \text{GNN}_{\theta_p}(\tilde{\bm{A}}_k,\tilde{\bm{X}}_k) \ ; \qquad \text{/*} \ \ \text{Eq.} \ \ 1 \ \ \text{*/}$ 9 Calculate  $\ell_{\text{MMD},\theta_p}$  according to Eq. [8](#page-3-0)  $\begin{array}{cc} \textbf{10} & \end{array} \left| \begin{array}{c} \end{array} \!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\!\$  $/\star$   $\eta$  is learning rate  $*/$

where  $\Theta$  indicates the whole parameter space. The overall procedure of CGM is shown in Algorithm [1.](#page-3-1)

With the limit of a budget as b, node labels  $\tilde{Y} \in \mathcal{C}_k^b$  for the condensed graph is initialised and kept as the same class ratio as the original graph (i.e., for any class  $c_k$ ,  $r_{k,c} \approx \tilde{r}_{k,c}$ ). Random sampling from the incoming graph is used to initialise the condensed node features  $\tilde{X}_k \in \mathbb{R}^{\tilde{b} \times d}$  at the beginning based on the assigned label. The initialisation can also be implemented as random noise.

### *B. Train in Memory*

 $11$  end <sup>12</sup> end

In continual learning, the vanilla replay-based CGL methods are faced with an imbalanced learning problem. When the size of the incoming graph is significantly larger than that of replayed graphs, the model is hard to balance the learning of knowledge from the historical graphs and the incoming graph. The previous attempts for balance are based on the loss scaling. A general form of Eq. [3](#page-2-3) and [4](#page-2-0) can be represented as:

$$
\ell_{\text{replay}} = \alpha \mathcal{L}(\mathcal{G}_k; \theta_k) + \beta \mathcal{L}(\mathcal{M}_{k-1}; \theta_k), \tag{10}
$$

where most effort is dedicated to  $\alpha$  and  $\beta$  according to the imbalance scale, which inevitably compromises the performance.

In CaT, since CGM has the ability to condense a graph without compromising the performance, it is reasonable to tackle the imbalance problem by using the condensed incoming graph instead of the whole incoming graph. To incorporate this beneficial characteristic of condensed graphs into the continual learning for balanced training, when the incoming graph  $\mathcal{G}_k$ arrived, the condensed graph  $\tilde{\mathcal{G}}_k$  is firstly generated, which is then used to update the previous memory  $\mathcal{M}_{k-1}$ :

<span id="page-3-2"></span>
$$
\mathcal{M}_k = \mathcal{M}_{k-1} \cup \tilde{\mathcal{G}}_k. \tag{11}
$$

## Algorithm 2: Overall procedure of CaT

<span id="page-4-1"></span>**Input:** A streaming of tasks  $\{\mathcal{T}_1, \mathcal{T}_2, ..., \mathcal{T}_K\}$ **Output:**  $GNN_K$ 1 Initialise a CGL model  $GNN_0$ ; 2 Initialise an empty memory bank  $\mathcal{M}_0$ ; 3 for  $k \leftarrow 1$  to K do 4 | Extract incoming graph  $\mathcal{G}_k$  from  $\mathcal{T}_k$ ; 5 Obtain  $\tilde{G}_k$  by CGM;  $/*$  Algorithm [1](#page-3-1)  $*/$ 6  $\big| \mathcal{M}_k = \mathcal{M}_{k-1} \cup \tilde{\mathcal{G}}_k;$  $/\star$  Eq. [11](#page-3-2)  $\star$ /<br> $/\star$  Eq. 12  $\star$ / 7 Update GNN<sub>k−1</sub> to GNN<sub>k</sub>; 8 end

TABLE I: Dataset statistics.

<span id="page-4-2"></span>

| <b>Dataset</b> | <b>Nodes</b> | <b>Edges</b> | <b>Features</b> | <b>Classes</b> | <b>Tasks</b> |
|----------------|--------------|--------------|-----------------|----------------|--------------|
| CoraFull       | 19,793       | 130,622      | 8,710           | 70             | 35           |
| Arxiv          | 169,343      | 1,166,243    | 128             | 40             | 20           |
| Reddit         | 227,853      | 114,615,892  | 602             | 40             | 20           |
| Products       | 2,449,028    | 61,859,036   | 100             | 46             | 23           |

Instead of training with  $\mathcal{M}_{k-1}$  and  $\mathcal{G}_k$  to deal with the imbalanced issue, CaT will update the model based on  $\mathcal{M}_k$ :

$$
\ell_{\text{CaT}} = \mathcal{L}(\mathcal{M}_k; \theta_k)
$$
  
=  $\mathcal{L}(\tilde{\mathcal{G}}_k; \theta_k) + \mathcal{L}(\mathcal{M}_{k-1}; \theta_k).$  (12)

<span id="page-4-0"></span>This process is named Train in Memory (TiM) since the model only trains with replayed graphs in the memory bank.

In summary, the proposed CaT framework uses graph condensation to generate small and effective replayed graphs and applies the TiM scheme to solve the imbalanced learning in CGL. The overall procedure of CaT is shown in Algorithm [2.](#page-4-1)

## V. EXPERIMENTS

### *A. Setup*

*a) Datasets:* Following the previous work [\[36\]](#page-9-21)–[\[38\]](#page-9-8), four datasets for node classification tasks are used in experiments, CoraFull [\[20\]](#page-9-35), Arxiv [\[8\]](#page-9-36), Reddit [\[7\]](#page-9-15) and Products [\[8\]](#page-9-36). CoraFull and Arxiv are both citation networks. Reddit is a post-to-post graph. The Products dataset is the co-purchase network. Table [I](#page-4-2) shows the statistics of these datasets.

Each dataset is split into a series of tasks focusing on the node classification problem. Each task includes nodes of two unique classes as an incoming graph. In each task, 60% nodes are chosen as training nodes, 20% nodes are for validation, and 20% are for testing. Class-IL is the main focus of the experiment since it is more challenging than task-IL, although overall performance in the task-IL setting will also be reported. In the continual update phase, the model can only access the newly incoming graph and the memory bank. In the testing phase, the model is required to be evaluated with test graphs from all previous tasks. There are no inter-task edges between any two tasks. In the task-IL setting, the output dimension of the model is set to two at all times. In the class-IL setting, since the total class number is not given, the output dimension is incremental as new tasks are coming.

*b) Baselines:* The following baselines are compared:

- Finetuning is the lower bound baseline by updating the model only with newly incoming graphs.
- Joint is the ideal upper bound situation where the memory bank contains all historical incoming graphs.
- **EWC** [\[12\]](#page-9-16) applies quadratic penalties to the model weights that are important to the previous tasks.
- MAS [\[1\]](#page-8-1) utilises a regularisation term for parameters sensitive to the model performance of historical tasks.
- **GEM** [\[18\]](#page-9-18) modifies the gradients using the informative data stored in memory.
- TWP [\[16\]](#page-9-2) preserves the topological information for previous tasks by a regularisation term.
- LwF [\[15\]](#page-9-17) distils the knowledge from the old model to the new model to keep the previous knowledge.
- HPNs [\[38\]](#page-9-8) redesign the conventional graph embedding generation for the task-IL setting by maintaining threelevel prototypes. Although HPNs are recently published, this baseline is only reported in task-IL experiments.
- **ER-GNN** [\[44\]](#page-9-4) samples the informative nodes from incoming graphs into the memory bank.
- **SSM** [\[37\]](#page-9-3) stores the sparsified incoming graph in the memory bank for future replay.

*c) Evaluation Metrics:* When the model is updated after Task  $\mathcal{T}_k$ , all previous tasks from  $\mathcal{T}_1$  to  $\mathcal{T}_k$  are evaluated. A lower triangular performance matrix  $M \in \mathbb{R}^{K \times K}$  is maintained, where  $m_{i,j}$  denotes the classification accuracy of Task  $\mathcal{T}_j$  after learning from Task  $\mathcal{T}_i$  ( $i \leq j$ ). Additionally, the following metrics are used to compare different methods comprehensively.

Average performance (AP) measures the average model performance after learning from Task  $\mathcal{T}_k$ :

$$
AP_k = \frac{1}{k} \sum_{i=1}^{k} m_{k,i}.
$$
 (13)

**Mean of average performance**  $(\overline{AP})$  [\[43\]](#page-9-37) denotes the average performance of model snapshots in the continual leanring process:

$$
\overline{AP} = \frac{1}{k} \sum_{i=1}^{k} AP_i.
$$
 (14)

Backward transfer (BWT) [\[29\]](#page-9-38) (also known as the average forgetting (AF)) indicates how the training process of the current task affects the previous tasks. The larger number implies that training the current task will have a greater impact on historical tasks. A negative or a positive number implies a negative or a positive impact, respectively:

$$
BWT_k = \frac{1}{k-1} \sum_{i=1}^{k-1} (m_{k,i} - m_{i,i}).
$$
 (15)

*d) Implementation:* The budget ratio represents the proportion of the memory bank to the total number of nodes in the entire training set, and the budget for every task is evenly assigned. By default, the budget ratio for the Joint baseline

<span id="page-5-0"></span>TABLE II: Overall results for class-IL setting without inter-task edges. All replay-based CGL methods have a budget ratio of 0.01. BWT is also called average forgetting (AF). The bold results are the best performance excluding Joint, and the underlined results are the best baselines excluding Joint. ↑ denotes the greater value represents greater performance.

| Category            | Methods    | CoraFull                         |                                  | Arxiv                            |                                   | Reddit                           |                                  | Products                         |                                  |
|---------------------|------------|----------------------------------|----------------------------------|----------------------------------|-----------------------------------|----------------------------------|----------------------------------|----------------------------------|----------------------------------|
|                     |            | AP (%) $\uparrow$                | BWT (%) $\uparrow$               | AP (%) $\uparrow$                | BWT (%) $\uparrow$                | AP (%) $\uparrow$                | BWT (%) $\uparrow$               | AP (%) $\uparrow$                | BWT (%) $\uparrow$               |
| Lower bound         | Finetuning | $2.2 \pm 0.0$                    | $-96.6 \pm 0.1$                  | $5.0 \pm 0.0$                    | $-96.7 \pm 0.1$                   | $5.0 \pm 0.0$                    | $-99.6 \pm 0.0$                  | $4.3 \pm 0.0$                    | $-97.2 \pm 0.1$                  |
| Regularisation      | EWC        | $2.9 \pm 0.2$                    | $-96.1 \pm 0.3$                  | $5.0 \pm 0.0$                    | $-96.8 \pm 0.1$                   | $5.3 \pm 0.6$                    | $-99.2 \pm 0.7$                  | $7.6 \pm 1.1$                    | $-91.7 \pm 1.4$                  |
|                     | MAS        | $2.2 \pm 0.0$                    | $-94.1 \pm 0.6$                  | $4.9 \pm 0.0$                    | $-95.0 \pm 0.7$                   | $10.7 \pm 1.4$                   | $-92.7 \pm 1.5$                  | $10.1 \pm 0.6$                   | $-89.0 \pm 0.5$                  |
|                     | GEM        | $2.5 \pm 0.1$                    | $-96.6 \pm 0.1$                  | $5.0 \pm 0.0$                    | $-96.8 \pm 0.1$                   | $5.3 \pm 0.5$                    | $-99.3 \pm 0.5$                  | $4.3 \pm 0.1$                    | $-96.8 \pm 0.1$                  |
|                     | TWP        | $21.2 \pm 3.2$                   | $-67.4 \pm 1.6$                  | $4.3 \pm 1.1$                    | $-93.0 \pm 8.3$                   | $9.5 \pm 2.0$                    | $-35.5 \pm 5.5$                  | $6.8 \pm 3.5$                    | $-64.3 \pm 12.8$                 |
| <b>Distillation</b> | LWF        | $2.2 \pm 0.0$                    | $-96.6 \pm 0.1$                  | $5.0 \pm 0.0$                    | $-96.8 \pm 0.1$                   | $5.0 \pm 0.0$                    | $-99.5 \pm 0.0$                  | $4.3 \pm 0.0$                    | $-96.8 \pm 0.2$                  |
| <b>Replay</b>       | ER-GNN     | $4.0 \pm 0.7$                    | $-94.3 \pm 0.9$                  | $30.8 \pm 0.6$                   | $-68.3 \pm 0.7$                   | $31.8 \pm 4.0$                   | $-71.2 \pm 4.2$                  | $39.5 \pm 1.3$                   | $-48.2 \pm 1.4$                  |
|                     | SSM        | $16.2 \pm 2.8$                   | $-82.1 \pm 2.9$                  | $35.1 \pm 1.8$                   | $-63.7 \pm 1.9$                   | $51.6 \pm 6.4$                   | $-50.3 \pm 6.7$                  | $62.7 \pm 0.5$                   | $-22.1 \pm 0.5$                  |
| <b>Full dataset</b> | Joint      | $85.3 \pm 0.1$                   | $-2.7 \pm 0.0$                   | $63.5 \pm 0.3$                   | $-15.7 \pm 0.4$                   | $98.2 \pm 0.0$                   | $-0.5 \pm 0.0$                   | $72.2 \pm 0.4$                   | $-5.3 \pm 0.5$                   |
| <b>Ours</b>         | <b>CaT</b> | <b><math>64.5 \pm 1.4</math></b> | <b><math>-3.3 \pm 2.6</math></b> | <b><math>66.0 \pm 1.1</math></b> | <b><math>-13.1 \pm 1.0</math></b> | <b><math>97.6 \pm 0.1</math></b> | <b><math>-0.2 \pm 0.2</math></b> | <b><math>71.0 \pm 0.2</math></b> | <b><math>-4.8 \pm 0.4</math></b> |

<span id="page-5-1"></span>TABLE III: Overall results in task-IL setting without inter-task edges. <sup>∗</sup>The results of HPNs are from the original paper, and only Arxiv and Products are provided here.

| Category       | Methods    | CoraFull                   |                            | Arxiv                   |                    | Reddit                  |                        | Products                   |                    |
|----------------|------------|----------------------------|----------------------------|-------------------------|--------------------|-------------------------|------------------------|----------------------------|--------------------|
|                |            | AP (%) $\uparrow$          | BWT (%) $\uparrow$         | AP (%) $\uparrow$       | BWT (%) $\uparrow$ | AP (%) $\uparrow$       | BWT (%) $\uparrow$     | AP (%) $\uparrow$          | BWT (%) $\uparrow$ |
| Lower bound    | Finetuning | $51.0 \pm 3.4$             | $-46.2 \pm 3.5$            | $67.1 \pm 5.2$          | $-31.3 \pm 5.6$    | $57.1 \pm 7.4$          | $-44.6 \pm 7.8$        | $56.4 \pm 3.8$             | $-42.4 \pm 4.0$    |
| Regularisation | EWC        | $87.4 \pm 2.2$             | $-9.1 \pm 2.2$             | $85.6 \pm 7.7$          | $-11.9 \pm 8.1$    | $85.5 \pm 3.3$          | $-14.8 \pm 3.5$        | $90.3 \pm 1.8$             | $-6.8 \pm 1.9$     |
|                | MAS        | $93.0 \pm 0.3$             | $-0.7 \pm 0.5$             | $83.8 \pm 6.9$          | $-12.0 \pm 7.8$    | $99.0 \pm 0.1$          | $0.0 \pm 0.0$          | $\underline{95.9 \pm 0.1}$ | $0.0 \pm 0.0$      |
|                | GEM        | $\underline{94.3 \pm 0.6}$ | $\underline{-2.1 \pm 0.5}$ | $94.7 \pm 0.1$          | $-2.3 \pm 0.2$     | $99.3 \pm 0.1$          | $-0.3 \pm 0.1$         | $86.9 \pm 0.9$             | $-10.6 \pm 0.9$    |
|                | TWP        | $87.9 \pm 1.9$             | $-4.9 \pm 0.6$             | $77.1 \pm 7.3$          | $-3.5 \pm 5.4$     | $74.1 \pm 5.5$          | $-1.5 \pm 0.5$         | $75.5 \pm 4.4$             | $-4.9 \pm 6.4$     |
| Distillation   | LwF        | $64.7 \pm 1.1$             | $-32.3 \pm 1.2$            | $60.2 \pm 5.8$          | $-38.6 \pm 6.2$    | $62.4 \pm 3.5$          | $-39.1 \pm 3.7$        | $50.1 \pm 0.7$             | $-49.3 \pm 0.8$    |
| Architecture   | $HPNs^*$   |                            | $-$                        | $85.8 \pm 0.7$          | $0.6 \pm 0.9$      |                         | $-$                    | $80.1 \pm 0.8$             | $2.9 \pm 1.0$      |
| Replay         | ER-GNN     | $54.2 \pm 1.0$             | $-43.1 \pm 1.1$            | $92.2 \pm 0.3$          | $-4.9 \pm 0.3$     | $94.3 \pm 0.5$          | $-5.6 \pm 0.5$         | $83.5 \pm 0.4$             | $-14.3 \pm 0.5$    |
|                | SSM        | $78.7 \pm 1.1$             | $-17.9 \pm 1.2$            | $93.3 \pm 0.4$          | $-3.6 \pm 0.4$     | $99.2 \pm 0.2$          | $-0.5 \pm 0.2$         | $94.6 \pm 0.5$             | $-2.7 \pm 0.4$     |
| Full dataset   | Joint      | $97.2 \pm 0.0$             | $0.2 \pm 0.1$              | $96.7 \pm 0.0$          | $-0.1 \pm 0.1$     | $99.7 \pm 0.0$          | $0.0 \pm 0.0$          | $95.7 \pm 0.7$             | $-0.2 \pm 0.7$     |
| Ours           | CGM        | $\textbf{95.3} \pm 0.3$    | $\textbf{-0.3} \pm 0.3$    | $\textbf{95.8} \pm 0.2$ | $0.2 \pm 0.1$      | $\textbf{99.4} \pm 0.0$ | $\textbf{0.1} \pm 0.1$ | $95.6 \pm 0.3$             | $0.1 \pm 0.3$      |

is 1 because it stores every training data in its memory. For example, 0.01 is the budget ratio in most following experiments, and the size of the memory bank becomes 1% of the size of the entire training data. Although the budget is set to a real number instead of a ratio of the entire training set in more piratical scenarios, the budget ratio is used in the experiments for keeping fairness and comparing the efficiency of different memory banks. Unless otherwise specified, for the replay-based method, the default budget ratio is 0.01.

GCN is the default backbone model. For CGM, a 2-layer GCN with a 512-dimensional hidden layer is used to encode all four datasets, and other graph encoders are evaluated as well. The learning rate for the condensed feature matrix is 0.0001 for CoraFull and 0.01 for other datasets. For the node classification problem, a 2-layer GCN with a 256-dimensional hidden layer and a class number-dependent output layer is used as the node classifier in all datasets. Unless otherwise specified, all results are obtained by running three times and reported with the average value and the standard error. All experiments are conducted on one NVIDIA RTX 2080 Ti

## GPU.

### *B. Overall Results*

The CaT is compared with all baselines in both class-IL and task-IL settings. AP is used to evaluate the average model performance of all learned tasks at the end of the task streaming, and BWT (also known as average forgetting (AF)) implies the forgetting problem of the model during continual learning. Table [II](#page-5-0) shows the overall performance of all baselines and the CaT in the class-IL CGL setting. CaT achieves the state-of-theart performance compared with all other CGL baselines and can match the ideal Joint performance in the Arxiv, Reddit and Products by only maintaining a synthetic memory bank whose budget ratio is only 0.01. Besides, the results show that CaT has a smaller BWT, which means CaT cannot only preserve the historical knowledge of the model but reduce the negative effects on the previous tasks while training the current task to alleviate the catastrophic forgetting problem. Although CaT outperforms other baselines in CoraFull, CaT does not reach the Joint performance with two potential reasons: (1) the 0.01 budget ratio for CoraFull limits the replayed graph

<span id="page-6-0"></span>TABLE IV: Ablation study of the CaT framework.

| <b>CGM</b> | <b>TiM</b> |                                                                     | CoraFull                                                              | Arxiv                                                                |                                                                        |  |
|------------|------------|---------------------------------------------------------------------|-----------------------------------------------------------------------|----------------------------------------------------------------------|------------------------------------------------------------------------|--|
|            |            | AP $(\%)$ $\uparrow$                                                | BWT $(\%)$ $\uparrow$                                                 | AP $(\%)$ $\uparrow$                                                 | BWT $(\%)$ $\uparrow$                                                  |  |
| Х          | Х<br>Х     | $16.2 \pm 2.8$<br>$54.8 \pm 1.3$                                    | $-82.1 \pm 2.9$<br>$-41.7 \pm 1.1$                                    | $35.1 \pm 1.8$<br>$34.2 \pm 7.0$                                     | $-63.7 \pm 1.9$<br>$-64.3 \pm 7.5$                                     |  |
| Х          | ✓          | $16.8 \pm 5.0$<br>$64.5 \pm 1.4$                                    | $-29.0\pm 6.1$<br>$-3.3 \pm 2.6$                                      | $53.5 \pm 1.4$<br>$66.0 \pm 1.1$                                     | $-16.2 \pm 1.4$<br>$-13.1 \pm 1.0$                                     |  |
| <b>CGM</b> | <b>TiM</b> |                                                                     | Reddit                                                                |                                                                      | Products                                                               |  |
|            |            | AP $(\%)$ $\uparrow$                                                | BWT $(\%)$ $\uparrow$                                                 | AP $(\%)$ $\uparrow$                                                 | BWT $(\%)$ $\uparrow$                                                  |  |
| Х<br>Х     | Х<br>X     | $51.6\pm 6.4$<br>$60.2 \pm 3.7$<br>$92.1 \pm 1.2$<br>$97.6 \pm 0.1$ | $-50.3\pm 6.7$<br>$-41.3 \pm 3.9$<br>$-4.3 \pm 1.6$<br>$-0.2 \pm 0.2$ | $62.7 \pm 0.5$<br>$71.3 \pm 0.2$<br>$63.2 \pm 0.2$<br>$71.0 \pm 0.2$ | $-22.1 \pm 0.5$<br>$-13.5 \pm 0.6$<br>$-9.7 \pm 0.6$<br>$-4.8 \pm 0.4$ |  |

to four nodes, which is extremely small to contain sufficient information; (2) CoraFull has 35 tasks, which is more than other datasets and difficult to retain historical knowledge.

Other baselines can hardly match the performance of CaT. Finetuning is easy to forget the previous knowledge since it only uses the newly incoming graph to update the model. Regularisation-based methods (e.g., EWC, MAS, GEM, TWP) also have unsatisfactory performance since adding overhead restrictions to the model will lead to bad model plasticity during the long streaming tasks. As a distillation method, LwF hardly handles the class-IL setting in the CGL. ER-GNN does not have reasonable results in all benchmarks for the sampling-based replay methods since there is a severe imbalanced training problem. SSM stores sparsified subgraphs in the memory bank, which can preserve the topological information for the historical graph data. Although SSM has a good performance, it still has a gap to Joint or CaT.

It is worth noting that even though all historical data can be used for training, Joint also has a negative BWT. The reason is that the class-IL setting requires the model to increase the output layer dimension as the new classes emerge, where the model cannot perfectly remember all previous knowledge. The results of HPNs for the class-IL are not provided since it is not designed for the class-IL setting.

Table [III](#page-5-1) shows the overall performance under the task-IL setting. Compared to the class-IL setting, task-IL is much easier, and all baseline methods get reasonable results. The CaT achieves the state-of-the-art performance and can match the Joint method with only a 0.01 budget ratio.

## *C. Ablation Study*

The CaT framework has two key components, CGM and TiM. To study their effectiveness, different CaT variants are evaluated, and the AP and BWT of these variants are reported in Table [IV.](#page-6-0) The variant without CGM indicates using Random Choice for the memory bank, and the variant without TiM indicates the typical replay-based scheme using the whole incoming graph for training. According to Table [IV,](#page-6-0) compared to the variant without both components, the variant using CGM improves both AP and BWT in the CoraFull and Arxiv datasets but dropped in the Reddit and Products datasets. The main

<span id="page-6-1"></span>Image /page/6/Figure/8 description: This image contains four bar charts, labeled (a) CoraFull, (b) Arxiv, (c) Reddit, and (d) Products. Each chart displays the Average Precision (AP) in percentage on the y-axis against the Budget Ratio in percentage on the x-axis. The budget ratios shown are 0.005, 0.01, 0.05, and 0.1. For each budget ratio, there are four bars representing different methods: Joint (indicated by a dashed purple line), EaT (red), SaT (blue), and CaT (green). In chart (a) CoraFull, AP values range from approximately 10% to 78%. In chart (b) Arxiv, AP values range from approximately 40% to 65%. In chart (c) Reddit, AP values range from approximately 45% to 100%. In chart (d) Products, AP values range from approximately 30% to 72%. A caption below the charts reads "Fig. 2: AP of methods with different budget ratios. All methods".

Fig. 3: AP of methods with different budget ratios. All methods use the TiM to avoid imbalanced training for fairness. CGM is more effective and efficient than ER-GNN and SSM.

reason is that CoraFull and Arxiv are small datasets where CGM can easily capture the data distribution. CGM hardly benefits the model for large datasets, especially in the Products dataset, where the scale imbalance problem is dominant. Compared to the variant without TiM, the variant with TiM significantly improves the overall performance, especially on large datasets (e.g., Reddit and Products). This also reflects the effectiveness of TiM for imbalanced graphs.

### *D. Effectiveness and Efficiency of CGM*

To analyse the effectiveness and efficiency of CGM, different memory banks are evaluated with four budget ratios, i.e., 0.005, 0.01, 0.05, and 0.1. Specifically, 0.005 is an extremely limited budget ratio, under which CGM only contains 2-node replayed graphs on the CoraFull dataset. While 0.1 is a large budget ratio, which represents the size of the memory bank equals to 10% of the size of the entire training set. For a fair comparison with CaT, the TiM scheme is applied for ER-GNN and SSM. EaT and SaT are used to denote the ER-GNN with TiM and SSM with TiM for short. The AP is used here.

*a) Different Memory Banks:* Fig. [3](#page-6-1) demonstrates that the CGM is more effective than existing sampling-based memory banks. CGM converges to optimal performance much quicker. CGM almost gets the best performance in all evaluated cases. CGM significantly outperforms other sampling-based memory banks when the budget ratio is relatively small (e.g., 0.005, 0.01). Although in the Arxiv dataset, SSM can outperform the CGM when the budget ratio is as large as 0.1, which is impractical for the memory bank. Besides, CGM has a small standard error, demonstrating that CGM is more robust for continual training. In the CoraFull dataset, the AP of 0.01

<span id="page-7-0"></span>Image /page/7/Figure/0 description: This image displays four scatter plots, each representing a different method for classifying data points. The plots are arranged in a 2x2 grid and labeled (a) ER-GNN, (b) SSM, (c) Random Choice, and (d) CGM. Each plot shows two classes of data points, class 30 and class 31, distinguished by color (blue for class 30, pink for class 31) and marker shape (circles for original data points, stars for selected data points). The legends in each plot specify the meaning of the colors and markers for each class and method. For example, in plot (a), blue circles and pink circles represent the original class 30 and class 31 data points, respectively, while blue stars and red stars represent selected class 30 and class 31 data points using the ER-GNN method. The distribution of points in each plot suggests a clustering of the two classes, with some overlap. The selected points (stars) appear to be concentrated in the region where the two classes are most intermingled.

Fig. 4: The visualisation of node embeddings from the original graph and the replayed graph of different methods (ER-GNN, SSM, Random Choice and CGM) in Task 15 of Reddit.

budget is slightly lower than 0.005. The size of replayed graphs with 0.01 budget (4-node replayed graph) and 0.005 budget (2-node replayed graph) is similar. The only difference is that the 2-node replayed graph has a uniform class distribution, but the 4-node replayed graph keeps the same class distribution as the original graph. The potential reason is the class imbalance problem raised in the small replayed graphs. We leave the balancing between classes as a future research problem.

*b) Budget Efficiency:* The advantage of the condensed graph is to keep the information of the original graph while reducing the graph size significantly. Fig. [3](#page-6-1) shows that the CGM method outperforms the sampling-based methods in achieving higher performance within a more limited budget. In all datasets, the sampling-based methods have a huge performance gap to CGM. 0.005-budget ratio CGM can outperform or match the 0.1-budget ratio sampling-based methods.

On the one hand, CGM uses less memory space to accurately approximate the historical data distribution. On the other hand, in the training phase, the model needs to propagate messages in the memory bank. Therefore, a small memory bank can improve both storage and computation efficiency.

*c) Visualisation:* To further explore the effectiveness of CGM. The t-SNE [\[25\]](#page-9-39) visualises the node embedding of different memory banks in Fig. [4,](#page-7-0) which shows the embedding distribution of ER-GNN, SSM, CGM initialisation (Random Choice) and CGM. To keep the same embedding space for reasonable comparisons, all node embeddings are generated with the same graph encoder. The node embeddings in samplingbased memory banks are close to each other, which cannot effectively approximate the complete graph distribution. On the contrary, the embedding of CGM is more diverse and can cover the whole distribution, enabling the classification model to learn a more accurate decision boundary.

<span id="page-7-1"></span>TABLE V:  $\overline{AP}$  (%) of different methods using TiM scheme.

|               | TiM | CoraFull                         | Arxiv                            | Reddit                           | Products                         |
|---------------|-----|----------------------------------|----------------------------------|----------------------------------|----------------------------------|
| <b>ER-GNN</b> |     | $11.5 \pm 0.1$<br>$15.7 \pm 0.5$ | $36.2 \pm 0.6$<br>$57.1 \pm 0.6$ | $48.5 \pm 1.4$<br>$80.0 \pm 1.8$ | $35.1 \pm 0.2$<br>$42.8 \pm 0.5$ |
| <b>SSM</b>    |     | $12.0 \pm 0.1$<br>$36.7 \pm 2.5$ | $39.4 \pm 0.9$<br>$62.4 \pm 1.2$ | $71.0 \pm 0.9$<br>$92.8 \pm 1.3$ | $57.5 \pm 0.2$<br>$74.6 \pm 0.3$ |
| <b>CGM</b>    | х   | $12.0 \pm 0.1$<br>$36.7 \pm 2.5$ | $39.4 \pm 0.9$<br>$62.4 \pm 1.2$ | $71.0 \pm 0.9$<br>$92.8 \pm 1.3$ | $65.6 \pm 0.1$<br>$81.7 \pm 0.1$ |

TABLE VI: AP (%) of different graph encoders.

<span id="page-7-2"></span>

|                   |                                  | CoraFull                         |                                  |                                  | Arxiv                            |                                  |
|-------------------|----------------------------------|----------------------------------|----------------------------------|----------------------------------|----------------------------------|----------------------------------|
|                   | 0.01                             | 0.05                             | 0.1                              | 0.01                             | 0.05                             | 0.1                              |
| SGC<br><b>GCN</b> | $56.7 \pm 1.7$<br>$64.5 \pm 1.4$ | $74.9 \pm 1.0$<br>$75.4 \pm 0.5$ | $78.5 \pm 0.5$<br>$77.3 \pm 0.6$ | $64.2 \pm 0.4$<br>$66.0 \pm 1.1$ | $66.6 \pm 0.9$<br>$70.0 \pm 1.9$ | $64.3 \pm 0.3$<br>$67.8 \pm 0.8$ |
|                   |                                  |                                  |                                  |                                  |                                  |                                  |
|                   |                                  | Reddit                           |                                  |                                  | Products                         |                                  |
|                   | 0.01                             | 0.05                             | 0.1                              | 0.01                             | 0.05                             | 0.1                              |

### *E. Balanced Learning with TiM*

*a) Different Methods with TiM:* TiM is a plug-and-play training scheme for all existing replay-based CGL methods. Table [V](#page-7-1) shows the mean of AP for different replay-based CGL methods with and without TiM. It clearly shows that the TiM scheme can improve the overall average performance with all memory bank generation methods. The reason is that the TiM can ensure training graphs for the CGL models have a similar size to deal with the imbalanced issue, which can solve the catastrophic forgetting problem.

*b) Visualisation:* The performance matrices of ER-GNN, SSM, CGM, and those memory banks training with TiM (i.e., EaT, SaT can CaT) on the CoraFull, Arxiv, Reddit and Products datasets under 0.01 budget ratio are visualised in Fig. [5.](#page-8-4) All memory banks without TiM struggle with remembering the previous knowledge since the scale gap between the newly incoming graph and replayed graphs in the memory bank. After using the TiM scheme, the performance matrices show the forgetting process slows down (i.e., the colour of each column is not changed a lot), which indicates the catastrophic forgetting problem is alleviated as the imbalanced training issue is tackled.

### *F. Parameter Sensitivity*

There are several hyperparameters in CGM, including the budget for the replayed graph, which is already evaluated in Fig. [3.](#page-6-1) This section will discuss the choice of graph encoders.

*a) Different Graph Encoders:* This experiment will compare GCN [\[11\]](#page-9-5) and SGC [\[33\]](#page-9-7) as encoders for CGM, while GCN will still be applied in the node classification for CGL. AP is used to measure the effectiveness. All CGM encoders have a 256-dimensional hidden layer and a 128-dimensional output layer. Table [VI](#page-7-2) shows that for different budget ratios, SGC and GCN can serve as competitive encoders for CGM. One exception is that in CoraFull with the budget ratio of 0.01,

<span id="page-8-4"></span>Image /page/8/Figure/0 description: The image displays a collection of heatmaps organized into four main sections labeled (a) CoraFull, (b) Arxiv, (c) Reddit, and (d) Products. Each section contains six heatmaps arranged in a row, representing different models: ER-GNN, EaT, SSM, SaT, CGM, and CaT. The heatmaps visualize performance matrices, with the x and y axes labeled 'Tasks'. The color scale, shown on the right side of each row, ranges from 0 to 100, indicating performance values. The heatmaps generally show a triangular pattern, with colors varying across the tasks. For CoraFull, the ER-GNN heatmap shows a gradient from dark purple to yellow, indicating increasing performance. EaT and SSM show more varied patterns with blocks of different colors. SaT, CGM, and CaT show a general trend of increasing performance from bottom left to top right, with CaT exhibiting the most consistent yellow color. The Arxiv and Reddit sections show similar patterns to CoraFull, with variations in the intensity and distribution of colors. The Products section also follows a similar structure, with the ER-GNN heatmap showing a distinct pattern of dark and light blocks, while the other models display gradients or more uniform color distributions.

Fig. 5: Performance matrix visualisation of ER-GNN, SSM, CGM and their combination with TiM scheme in CoraFull, Arxiv, Reddit and Products datasets. The coloured square located at the  $i_{th}$  row and the  $j_{th}$  column denotes the classification accuracy of Task  $\mathcal{T}_j$  after model training on Task  $\mathcal{T}_i$ . Light colour means high accuracy, and dark colour means low accuracy. The  $i_{th}$ column from top to bottom can represent the accuracy changes during the model's continual training of Task  $\mathcal{T}_i$ .

the performance of CGM with SGC is much lower than that with GCN. This is possible because 0.01 is a very strict budget ratio for the CoraFull dataset that SGC does not have the sufficient representation ability with such less data. A similar situation happens in the Products dataset, which is a huge and challenging dataset for GNNs.

## VI. CONCLUSION

This paper identifies the inefficient sampling-based memory bank and unbalanced continual learning issues in the replaybased CGL methods. To solve these issues, a novel CaT framework is proposed, which includes two key components, CGM and TiM. CGM is a small yet effective memory bank based on the graph condensation. TiM scheme updates the memory bank with the newly incoming graph and continuously trains the model with this memory bank to balance the update. Extensive experiments demonstrate that this framework achieves state-ofthe-art performance in task-IL and class-IL settings.

### ACKNOWLEDGMENT

This work is supported by Australian Research Council CE200100025 and DP230101196.

### **REFERENCES**

- <span id="page-8-1"></span>[1] R. Aljundi, F. Babiloni, M. Elhoseiny, M. Rohrbach, and T. Tuytelaars, "Memory aware synapses: Learning what (not) to forget," in *ECCV*, 2018.
- <span id="page-8-2"></span>[2] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J. Zhu, "Generalizing dataset distillation via deep generative prior," in *CVPR*, 2023.
- <span id="page-8-3"></span>[3] Y. Chen, M. Welling, and A. J. Smola, "Super-samples from kernel herding," in *UAI*, 2010.
- <span id="page-8-0"></span>[4] A. A. Daruna, M. Gupta, M. Sridharan, and S. Chernova, "Continual learning of knowledge graph embeddings," *IEEE Robotics Autom. Lett.*, 2021.

- <span id="page-9-26"></span>[5] X. Gao, T. Chen, Y. Zang, W. Zhang, Q. V. H. Nguyen, K. Zheng, and H. Yin, "Graph condensation for inductive node representation learning," *CoRR*, vol. abs/2307.15967, 2023.
- <span id="page-9-30"></span>[6] J. Gu, K. Wang, W. Jiang, and Y. You, "Summarizing stream data for memory-restricted online continual learning," *CoRR*, vol. abs/2305.16645, 2023.
- <span id="page-9-15"></span>[7] W. L. Hamilton, Z. Ying, and J. Leskovec, "Inductive representation learning on large graphs," in *NeurIPS*, 2017.
- <span id="page-9-36"></span>[8] W. Hu, M. Fey, M. Zitnik, Y. Dong, H. Ren, B. Liu, M. Catasta, and J. Leskovec, "Open graph benchmark: Datasets for machine learning on graphs," in *NeurIPS*, 2020.
- <span id="page-9-9"></span>[9] W. Jin, X. Tang, H. Jiang, Z. Li, D. Zhang, J. Tang, and B. Yin, "Condensing graphs via one-step gradient matching," in *KDD*, 2022.
- <span id="page-9-10"></span>[10] W. Jin, L. Zhao, S. Zhang, Y. Liu, J. Tang, and N. Shah, "Graph condensation for graph neural networks," in *ICLR*, 2022.
- <span id="page-9-5"></span>[11] T. N. Kipf and M. Welling, "Semi-supervised classification with graph convolutional networks," in *ICLR*, 2017.
- <span id="page-9-16"></span>[12] J. Kirkpatrick, R. Pascanu, N. C. Rabinowitz, J. Veness, G. Desjardins, A. A. Rusu, K. Milan, J. Quan, T. Ramalho, A. Grabska-Barwinska, D. Hassabis, C. Clopath, D. Kumaran, and R. Hadsell, "Overcoming catastrophic forgetting in neural networks," *CoRR*, vol. abs/1612.00796, 2016.
- <span id="page-9-20"></span>[13] J. Ko, S. Kang, and K. Shin, "Begin: Extensive benchmark scenarios and an easy-to-use framework for graph continual learning," *CoRR*, vol. abs/2211.14568, 2022.
- <span id="page-9-12"></span>[14] Y. Li, D. Tarlow, M. Brockschmidt, and R. S. Zemel, "Gated graph sequence neural networks," in *ICLR*, 2016.
- <span id="page-9-17"></span>[15] Z. Li and D. Hoiem, "Learning without forgetting," *TPAMI*, 2018.
- <span id="page-9-2"></span>[16] H. Liu, Y. Yang, and X. Wang, "Overcoming catastrophic forgetting in graph neural networks," in *AAAI*, 2021.
- <span id="page-9-11"></span>[17] M. Liu, S. Li, X. Chen, and L. Song, "Graph condensation via receptive field distribution matching," *CoRR*, vol. abs/2206.13697, 2022.
- <span id="page-9-18"></span>[18] D. Lopez-Paz and M. Ranzato, "Gradient episodic memory for continual learning," in *NeurIPS*, 2017.
- <span id="page-9-31"></span>[19] W. Masarczyk and I. Tautkute, "Reducing catastrophic forgetting with learning on synthetic data," in *CVPR Workshop*, 2020.
- <span id="page-9-35"></span>[20] A. McCallum, K. Nigam, J. Rennie, and K. Seymore, "Automating the construction of internet portals with machine learning," *Inf. Retr.*, 2000.
- <span id="page-9-0"></span>[21] R. Qiu, H. Yin, Z. Huang, and T. Chen, "GAG: global attributed graph neural network for streaming session-based recommendation," in *SIGIR*, 2020.
- <span id="page-9-32"></span>[22] A. Rosasco, A. Carta, A. Cossu, V. Lomonaco, and D. Bacciu, "Distilled replay: Overcoming forgetting through synthetic samples," in *Continual Semi-Supervised Learning - First International Workshop, CSSL*, 2021.
- <span id="page-9-33"></span>[23] M. Sangermano, A. Carta, A. Cossu, and D. Bacciu, "Sample condensation in online continual learning," in *IJCNN*, 2022.
- <span id="page-9-28"></span>[24] O. Sener and S. Savarese, "Active learning for convolutional neural networks: A core-set approach," in *ICLR*, 2018.
- <span id="page-9-39"></span>[25] L. Van der Maaten and G. Hinton, "Visualizing data using t-sne." *JMLR*, 2008.
- <span id="page-9-14"></span>[26] A. Vaswani, N. Shazeer, N. Parmar, J. Uszkoreit, L. Jones, A. N. Gomez, L. Kaiser, and I. Polosukhin, "Attention is all you need," in *NeurIPS*, 2017.
- <span id="page-9-6"></span>[27] P. Velickovic, G. Cucurull, A. Casanova, A. Romero, P. Liò, and Y. Bengio, "Graph attention networks," in *ICLR*, 2018.
- <span id="page-9-22"></span>[28] K. Wang, B. Zhao, X. Peng, Z. Zhu, S. Yang, S. Wang, G. Huang, H. Bilen, X. Wang, and Y. You, "CAFE: learning to condense dataset by aligning features," in *CVPR*, 2022.
- <span id="page-9-38"></span>[29] L. Wang, X. Zhang, H. Su, and J. Zhu, "A comprehensive survey of continual learning: Theory, method and application," *CoRR*, vol. abs/2302.00487, 2023.
- <span id="page-9-23"></span>[30] T. Wang, J. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *CoRR*, vol. abs/1811.10959, 2018.
- <span id="page-9-29"></span>[31] Z. Wang and J. Ye, "Querying discriminative and representative samples for batch mode active learning," in *KDD*, 2013.
- <span id="page-9-34"></span>[32] F. Wiewel and B. Yang, "Condensed composite memory continual learning," in *IJCNN*, 2021.
- <span id="page-9-7"></span>[33] F. Wu, A. H. S. Jr., T. Zhang, C. Fifty, T. Yu, and K. Q. Weinberger, "Simplifying graph convolutional networks," in *ICML*, 2019.
- <span id="page-9-13"></span>[34] K. Xu, W. Hu, J. Leskovec, and S. Jegelka, "How powerful are graph neural networks?" in *ICLR*, 2019.
- <span id="page-9-1"></span>[35] Y. Xu, Y. Zhang, W. Guo, H. Guo, R. Tang, and M. Coates, "Graphsail: Graph structure aware incremental learning for recommender systems," in *CIKM*, 2020.

- <span id="page-9-21"></span>[36] X. Zhang, D. Song, and D. Tao, "Cglb: Benchmark tasks for continual graph learning," in *NeurIPS Systems Datasets and Benchmarks Track*, 2022.
- <span id="page-9-3"></span>[37] -, "Sparsified subgraph memory for continual graph representation learning," in *ICDM*, 2022.
- <span id="page-9-8"></span>[38] ——, "Hierarchical prototype networks for continual graph representation learning," *TPAMI*, 2023.
- <span id="page-9-19"></span>[39] -, "Sufficient subgraph embedding memory for continual graph representation learning," 2023. [Online]. Available: [https://openreview.](https://openreview.net/forum?id=SJjvXfape5U) [net/forum?id=SJjvXfape5U](https://openreview.net/forum?id=SJjvXfape5U)
- <span id="page-9-24"></span>[40] B. Zhao and H. Bilen, "Dataset condensation with differentiable siamese augmentation," in *ICML*, 2021.
- <span id="page-9-27"></span>[41] ——, "Dataset condensation with distribution matching," in *WACV*, 2023.
- <span id="page-9-25"></span>[42] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," in *ICLR*, 2021.
- <span id="page-9-37"></span>[43] D. Zhou, Q. Wang, Z. Qi, H. Ye, D. Zhan, and Z. Liu, "Deep classincremental learning: A survey," *CoRR*, vol. abs/2302.03648, 2023.
- <span id="page-9-4"></span>[44] F. Zhou and C. Cao, "Overcoming catastrophic forgetting in graph neural networks with experience replay," in *AAAI*, 2021.
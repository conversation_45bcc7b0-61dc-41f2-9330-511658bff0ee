tion of probability. Consider the example of polynomial curve fitting discussed in Section 1.1. It seems reasonable to apply the frequentist notion of probability to the random values of the observed variables  $t_n$ . However, we would like to address and quantify the uncertainty that surrounds the appropriate choice for the model parameters **w**. We shall see that, from a Bayesian perspective, we can use the machinery of probability theory to describe the uncertainty in model parameters such as **w**, or indeed in the choice of model itself.

<PERSON><PERSON>' theorem now acquires a new significance. Recall that in the boxes of fruit example, the observation of the identity of the fruit provided relevant information that altered the probability that the chosen box was the red one. In that example, <PERSON><PERSON>' theorem was used to convert a prior probability into a posterior probability by incorporating the evidence provided by the observed data. As we shall see in detail later, we can adopt a similar approach when making inferences about quantities such as the parameters **w** in the polynomial curve fitting example. We capture our assumptions about **w**, before observing the data, in the form of a prior probability distribution  $p(\mathbf{w})$ . The effect of the observed data  $\mathcal{D} = \{t_1, \ldots, t_N\}$  is expressed through the conditional probability  $p(\mathcal{D}|\mathbf{w})$ , and we shall see later, in Section 1.2.5, how this can be represented explicitly. <PERSON><PERSON>' theorem, which takes the form

$$
p(\mathbf{w}|\mathcal{D}) = \frac{p(\mathcal{D}|\mathbf{w})p(\mathbf{w})}{p(\mathcal{D})}
$$
(1.43)

then allows us to evaluate the uncertainty in  $w$  *after* we have observed  $D$  in the form of the posterior probability  $p(\mathbf{w}|\mathcal{D})$ .

The quantity  $p(\mathcal{D}|\mathbf{w})$  on the right-hand side of Bayes' theorem is evaluated for the observed data set  $D$  and can be viewed as a function of the parameter vector **w**, in which case it is called the *likelihood function*. It expresses how probable the observed data set is for different settings of the parameter vector **w**. Note that the likelihood is not a probability distribution over **w**, and its integral with respect to **w** does not (necessarily) equal one.

Given this definition of likelihood, we can state Bayes' theorem in words

$$
posterior \propto likelihood \times prior
$$
 (1.44)

where all of these quantities are viewed as functions of **w**. The denominator in (1.43) is the normalization constant, which ensures that the posterior distribution on the left-hand side is a valid probability density and integrates to one. Indeed, integrating both sides of (1.43) with respect to **w**, we can express the denominator in Bayes' theorem in terms of the prior distribution and the likelihood function

$$
p(\mathcal{D}) = \int p(\mathcal{D}|\mathbf{w})p(\mathbf{w}) \, \mathrm{d}\mathbf{w}.
$$
 (1.45)

In both the Bayesian and frequentist paradigms, the likelihood function  $p(\mathcal{D}|\mathbf{w})$ plays a central role. However, the manner in which it is used is fundamentally different in the two approaches. In a frequentist setting, **w** is considered to be a fixed parameter, whose value is determined by some form of 'estimator', and error bars on this estimate are obtained by considering the distribution of possible data sets  $D$ . By contrast, from the Bayesian viewpoint there is only a single data set  $D$  (namely the one that is actually observed), and the uncertainty in the parameters is expressed through a probability distribution over **w**.

A widely used frequentist estimator is *maximum likelihood*, in which **w** is set to the value that maximizes the likelihood function  $p(\mathcal{D}|\mathbf{w})$ . This corresponds to choosing the value of **w** for which the probability of the observed data set is maximized. In the machine learning literature, the negative log of the likelihood function is called an *error function*. Because the negative logarithm is a monotonically decreasing function, maximizing the likelihood is equivalent to minimizing the error.

One approach to determining frequentist error bars is the *bootstrap* (Efron, 1979; Hastie *et al.*, 2001), in which multiple data sets are created as follows. Suppose our original data set consists of N data points  $X = \{x_1, \ldots, x_N\}$ . We can create a new data set  $X_B$  by drawing N points at random from X, with replacement, so that some points in **X** may be replicated in  $X_B$ , whereas other points in **X** may be absent from  $\mathbf{X}_{\text{B}}$ . This process can be repeated L times to generate L data sets each of size N and each obtained by sampling from the original data set **X**. The statistical accuracy of parameter estimates can then be evaluated by looking at the variability of predictions between the different bootstrap data sets.

One advantage of the Bayesian viewpoint is that the inclusion of prior knowledge arises naturally. Suppose, for instance, that a fair-looking coin is tossed three times and lands heads each time. A classical maximum likelihood estimate of the *Section 2.1* probability of landing heads would give 1, implying that all future tosses will land heads! By contrast, a Bayesian approach with any reasonable prior will lead to a much less extreme conclusion.

There has been much controversy and debate associated with the relative merits of the frequentist and Bayesian paradigms, which have not been helped by the fact that there is no unique frequentist, or even Bayesian, viewpoint. For instance, one common criticism of the Bayesian approach is that the prior distribution is often selected on the basis of mathematical convenience rather than as a reflection of any prior beliefs. Even the subjective nature of the conclusions through their dependence on the choice of prior is seen by some as a source of difficulty. Reducing *Section 2.4.3* the dependence on the prior is one motivation for so-called *noninformative* priors. However, these lead to difficulties when comparing different models, and indeed Bayesian methods based on poor choices of prior can give poor results with high confidence. Frequentist evaluation methods offer some protection from such prob-*Section 1.3* lems, and techniques such as cross-validation remain useful in areas such as model comparison.

> This book places a strong emphasis on the Bayesian viewpoint, reflecting the huge growth in the practical importance of Bayesian methods in the past few years, while also discussing useful frequentist concepts as required.

> Although the Bayesian framework has its origins in the  $18<sup>th</sup>$  century, the practical application of Bayesian methods was for a long time severely limited by the difficulties in carrying through the full Bayesian procedure, particularly the need to marginalize (sum or integrate) over the whole of parameter space, which, as we shall

*Section 2.1*

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.

*Section 1.3*

see, is required in order to make predictions or to compare different models. The development of sampling methods, such as Markov chain Monte Carlo (discussed in Chapter 11) along with dramatic improvements in the speed and memory capacity of computers, opened the door to the practical use of Bayesian techniques in an impressive range of problem domains. Monte Carlo methods are very flexible and can be applied to a wide range of models. However, they are computationally intensive and have mainly been used for small-scale problems.

More recently, highly efficient deterministic approximation schemes such as variational Bayes and expectation propagation (discussed in Chapter 10) have been developed. These offer a complementary alternative to sampling methods and have allowed Bayesian techniques to be used in large-scale applications (Blei *et al.*, 2003).

### **1.2.4 The Gaussian distribution**

We shall devote the whole of Chapter 2 to a study of various probability distributions and their key properties. It is convenient, however, to introduce here one of the most important probability distributions for continuous variables, called the *normal* or *Gaussian* distribution. We shall make extensive use of this distribution in the remainder of this chapter and indeed throughout much of the book.

For the case of a single real-valued variable  $x$ , the Gaussian distribution is defined by

$$
\mathcal{N}(x|\mu, \sigma^2) = \frac{1}{(2\pi\sigma^2)^{1/2}} \exp\left\{-\frac{1}{2\sigma^2}(x-\mu)^2\right\}
$$
(1.46)

which is governed by two parameters:  $\mu$ , called the *mean*, and  $\sigma^2$ , called the *variance*. The square root of the variance, given by  $\sigma$ , is called the *standard deviation*, and the reciprocal of the variance, written as  $\beta = 1/\sigma^2$ , is called the *precision*. We shall see the motivation for these terms shortly. Figure 1.13 shows a plot of the Gaussian distribution.

From the form of (1.46) we see that the Gaussian distribution satisfies

$$
\mathcal{N}(x|\mu, \sigma^2) > 0. \tag{1.47}
$$

*Exercise 1.7* Also it is straightforward to show that the Gaussian is normalized, so that

Image /page/2/Picture/11 description: A portrait of a man with white hair pulled back in a queue, wearing a dark blue military uniform with gold trim and epaulets. He has a white cravat and a medal pinned to his chest. The background is dark and indistinct.

# Pierre-Simon Laplace 1749–1827

It is said that Laplace was seriously lacking in modesty and at one point declared himself to be the best mathematician in France at the time, a claim that was arguably true. As well as being prolific in mathe-

matics, he also made numerous contributions to astronomy, including the nebular hypothesis by which the

earth is thought to have formed from the condensation and cooling of a large rotating disk of gas and dust. In 1812 he published the first edition of Théorie Analytique des Probabilités, in which Laplace states that "probability theory is nothing but common sense reduced to calculation". This work included a discussion of the inverse probability calculation (later termed Bayes' theorem by Poincaré), which he used to solve problems in life expectancy, jurisprudence, planetary masses, triangulation, and error estimation.

**Figure 1.13** Plot of the univariate Gaussian showing the mean  $\mu$  and the  $\mathcal{N}(x|\mu, \sigma^2)$ <br>standard deviation  $\sigma$ .

Image /page/3/Figure/2 description: The image shows a bell curve, which represents a normal distribution, denoted as N(x|μ, σ^2). The x-axis is labeled 'x' and has a mark at 'μ', representing the mean. The y-axis is not explicitly labeled with a variable but represents the probability density function. A horizontal double-headed arrow spans from approximately one standard deviation to the left of the mean to one standard deviation to the right of the mean, indicating a range of 2σ. The curve is symmetrical around the mean 'μ' and tapers off towards the x-axis on both sides.

$$
\int_{-\infty}^{\infty} \mathcal{N}(x|\mu, \sigma^2) \, \mathrm{d}x = 1. \tag{1.48}
$$

Thus (1.46) satisfies the two requirements for a valid probability density.

We can readily find expectations of functions of  $x$  under the Gaussian distribu-*Exercise 1.8* tion. In particular, the average value of x is given by

$$
\mathbb{E}[x] = \int_{-\infty}^{\infty} \mathcal{N}(x|\mu, \sigma^2) x \, dx = \mu.
$$
 (1.49)

Because the parameter  $\mu$  represents the average value of x under the distribution, it is referred to as the mean. Similarly, for the second order moment

$$
\mathbb{E}[x^2] = \int_{-\infty}^{\infty} \mathcal{N}(x|\mu, \sigma^2) x^2 dx = \mu^2 + \sigma^2.
$$
 (1.50)

From (1.49) and (1.50), it follows that the variance of x is given by

$$
\text{var}[x] = \mathbb{E}[x^2] - \mathbb{E}[x]^2 = \sigma^2 \tag{1.51}
$$

and hence  $\sigma^2$  is referred to as the variance parameter. The maximum of a distribution *Exercise 1.9* is known as its mode. For a Gaussian, the mode coincides with the mean.

We are also interested in the Gaussian distribution defined over a D-dimensional vector **x** of continuous variables, which is given by

$$
\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma}) = \frac{1}{(2\pi)^{D/2}} \frac{1}{|\boldsymbol{\Sigma}|^{1/2}} \exp\left\{-\frac{1}{2}(\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} \boldsymbol{\Sigma}^{-1}(\mathbf{x} - \boldsymbol{\mu})\right\} \tag{1.52}
$$

where the D-dimensional vector  $\mu$  is called the mean, the  $D \times D$  matrix  $\Sigma$  is called the covariance, and <sup>|</sup>**Σ**<sup>|</sup> denotes the determinant of **Σ**. We shall make use of the multivariate Gaussian distribution briefly in this chapter, although its properties will be studied in detail in Section 2.3.

*Exercise 1.8*

**Figure 1.14** Illustration of the likelihood function for a Gaussian distribution, shown by the red curve. Here the black points de-  $p(x)$ note a data set of values  $\{x_n\}$ , and the likelihood function given by (1.53) corresponds to the product of the blue values. Maximizing the likelihood involves adjusting the mean and variance of the Gaussian so as to maximize this product.

Image /page/4/Figure/2 description: The image displays a graph of a normal distribution curve, denoted as N(x|µ, σ²). The x-axis is labeled 'x' and the y-axis is labeled 'p(x)'. Several points are marked on the x-axis with black dots. Above these points, blue dots are placed on the red curve, connected to the x-axis by vertical green lines. One specific point on the x-axis is labeled 'xn'.

Now suppose that we have a data set of observations  $\mathbf{x} = (x_1, \dots, x_N)^T$ , representing N observations of the scalar variable x. Note that we are using the typeface **x** to distinguish this from a single observation of the vector-valued variable  $(x_1, \ldots, x_D)^T$ , which we denote by **x**. We shall suppose that the observations are drawn independently from a Gaussian distribution whose mean  $\mu$  and variance  $\sigma^2$ are unknown, and we would like to determine these parameters from the data set. Data points that are drawn independently from the same distribution are said to be *independent and identically distributed*, which is often abbreviated to i.i.d. We have seen that the joint probability of two independent events is given by the product of the marginal probabilities for each event separately. Because our data set **x** is i.i.d., we can therefore write the probability of the data set, given  $\mu$  and  $\sigma^2$ , in the form

$$
p(\mathbf{x}|\mu, \sigma^2) = \prod_{n=1}^{N} \mathcal{N}\left(x_n|\mu, \sigma^2\right).
$$
 (1.53)

When viewed as a function of  $\mu$  and  $\sigma^2$ , this is the likelihood function for the Gaussian and is interpreted diagrammatically in Figure 1.14.

One common criterion for determining the parameters in a probability distribution using an observed data set is to find the parameter values that maximize the likelihood function. This might seem like a strange criterion because, from our foregoing discussion of probability theory, it would seem more natural to maximize the probability of the parameters given the data, not the probability of the data given the parameters. In fact, these two criteria are related, as we shall discuss in the context *Section 1.2.5* of curve fitting.

> For the moment, however, we shall determine values for the unknown parameters  $\mu$  and  $\sigma^2$  in the Gaussian by maximizing the likelihood function (1.53). In practice, it is more convenient to maximize the log of the likelihood function. Because the logarithm is a monotonically increasing function of its argument, maximization of the log of a function is equivalent to maximization of the function itself. Taking the log not only simplifies the subsequent mathematical analysis, but it also helps numerically because the product of a large number of small probabilities can easily underflow the numerical precision of the computer, and this is resolved by computing instead the sum of the log probabilities. From  $(1.46)$  and  $(1.53)$ , the log likelihood

function can be written in the form

$$
\ln p\left(\mathbf{x}|\mu,\sigma^2\right) = -\frac{1}{2\sigma^2} \sum_{n=1}^{N} (x_n - \mu)^2 - \frac{N}{2} \ln \sigma^2 - \frac{N}{2} \ln(2\pi). \tag{1.54}
$$

Maximizing (1.54) with respect to  $\mu$ , we obtain the maximum likelihood solution

$$
\mu_{\rm ML} = \frac{1}{N} \sum_{n=1}^{N} x_n \tag{1.55}
$$

which is the *sample mean*, i.e., the mean of the observed values  $\{x_n\}$ . Similarly, maximizing (1.54) with respect to  $\sigma^2$ , we obtain the maximum likelihood solution for the variance in the form

$$
\sigma_{\rm ML}^2 = \frac{1}{N} \sum_{n=1}^{N} (x_n - \mu_{\rm ML})^2
$$
 (1.56)

which is the *sample variance* measured with respect to the sample mean  $\mu_{ML}$ . Note that we are performing a joint maximization of (1.54) with respect to  $\mu$  and  $\sigma^2$ , but in the case of the Gaussian distribution the solution for  $\mu$  decouples from that for  $\sigma^2$ so that we can first evaluate (1.55) and then subsequently use this result to evaluate (1.56).

Later in this chapter, and also in subsequent chapters, we shall highlight the significant limitations of the maximum likelihood approach. Here we give an indication of the problem in the context of our solutions for the maximum likelihood parameter settings for the univariate Gaussian distribution. In particular, we shall show that the maximum likelihood approach systematically underestimates the variance of the distribution. This is an example of a phenomenon called *bias* and is related *Section 1.1* to the problem of over-fitting encountered in the context of polynomial curve fitting. We first note that the maximum likelihood solutions  $\mu_{ML}$  and  $\sigma_{ML}^2$  are functions of the data set values  $x_1, \ldots, x_N$ . Consider the expectations of these quantities with respect to the data set values, which themselves come from a Gaussian distribution *Exercise 1.12* with parameters  $\mu$  and  $\sigma^2$ . It is straightforward to show that

$$
\mathbb{E}[\mu_{\mathrm{ML}}] = \mu \tag{1.57}
$$

$$
\mathbb{E}[\sigma_{\text{ML}}^2] = \left(\frac{N-1}{N}\right)\sigma^2 \tag{1.58}
$$

so that on average the maximum likelihood estimate will obtain the correct mean but will underestimate the true variance by a factor  $(N - 1)/N$ . The intuition behind this result is given by Figure 1.15.

From (1.58) it follows that the following estimate for the variance parameter is unbiased

$$
\tilde{\sigma}^2 = \frac{N}{N-1} \sigma_{\text{ML}}^2 = \frac{1}{N-1} \sum_{n=1}^{N} (x_n - \mu_{\text{ML}})^2.
$$
 (1.59)

*Exercise 1.11* given by

*Section 1.1*

**Figure 1.15** Illustration of how bias arises in using maximum likelihood to determine the variance of a Gaussian. The green curve shows the true Gaussian distribution from which data is generated, and the three red curves show the Gaussian distributions obtained by fitting to three data sets, each consisting of two data points shown in blue, using the maximum likelihood results (1.55) and (1.56). Averaged across the three data sets, the mean is correct, but the variance is systematically under-estimated because it is measured relative to the sample mean and not relative to the true mean.

Image /page/6/Figure/2 description: The image displays three subplots, labeled (a), (b), and (c) from top to bottom. Each subplot contains a horizontal black line representing an axis. Two bell-shaped curves, one red and one green, are plotted on this axis. A blue dot is also present on the axis in each subplot. In subplot (a), the red curve is peaked to the left of the blue dot, and the green curve is broader and peaked to the left of the red curve. In subplot (b), both curves are shifted to the right compared to subplot (a), with the red curve still peaked to the left of the blue dot, and the green curve peaked to the left of the red curve. In subplot (c), the red curve is peaked to the right of the blue dot, and the green curve is broader and peaked to the left of the red curve.

In Section 10.1.3, we shall see how this result arises automatically when we adopt a Bayesian approach.

Note that the bias of the maximum likelihood solution becomes less significant as the number N of data points increases, and in the limit  $N \to \infty$  the maximum likelihood solution for the variance equals the true variance of the distribution that generated the data. In practice, for anything other than small  $N$ , this bias will not prove to be a serious problem. However, throughout this book we shall be interested in more complex models with many parameters, for which the bias problems associated with maximum likelihood will be much more severe. In fact, as we shall see, the issue of bias in maximum likelihood lies at the root of the over-fitting problem that we encountered earlier in the context of polynomial curve fitting.

### **1.2.5 Curve fitting re-visited**

We have seen how the problem of polynomial curve fitting can be expressed in *Section 1.1* terms of error minimization. Here we return to the curve fitting example and view it from a probabilistic perspective, thereby gaining some insights into error functions and regularization, as well as taking us towards a full Bayesian treatment.

> The goal in the curve fitting problem is to be able to make predictions for the target variable  $t$  given some new value of the input variable  $x$  on the basis of a set of training data comprising N input values  $\mathbf{x} = (x_1, \dots, x_N)^T$  and their corresponding target values  $\mathbf{t} = (t_1, \dots, t_N)^T$ . We can express our uncertainty over the value of the target variable using a probability distribution. For this purpose, we shall assume that, given the value of x, the corresponding value of t has a Gaussian distribution with a mean equal to the value  $y(x, w)$  of the polynomial curve given by (1.1). Thus we have

$$
p(t|x, \mathbf{w}, \beta) = \mathcal{N}\left(t|y(x, \mathbf{w}), \beta^{-1}\right)
$$
\n(1.60)

where, for consistency with the notation in later chapters, we have defined a precision parameter  $\beta$  corresponding to the inverse variance of the distribution. This is illustrated schematically in Figure 1.16.

**Figure 1.16** Schematic illustration of a Gaussian conditional distribution for  $t$  given  $x$  given by (1.60), in which the mean is given by the polynomial function  $y(x, \mathbf{w})$ , and the precision is given by the parameter  $\beta$ , which is related to the variance by  $\beta^{-1} = \sigma^2$ .

Image /page/7/Figure/2 description: The image displays a graph with an x-axis labeled 'x' and a y-axis labeled 't'. A red curve, representing the function y(x, w), is plotted, showing an increasing trend. At a specific point x0 on the x-axis, a dashed green line extends horizontally to the y-axis, indicating the value y(x0, w). A vertical blue line is drawn at x0. Overlapping the red curve and the vertical line at x0, a blue curve represents a probability density function, labeled p(t|x0, w, β). This curve is centered around the value y(x0, w) and shows a typical bell shape of a normal distribution. To the right of the graph, a double-headed arrow indicates a range of 2σ, suggesting the spread or standard deviation of the probability distribution.

We now use the training data  $\{x, t\}$  to determine the values of the unknown parameters **w** and  $\beta$  by maximum likelihood. If the data are assumed to be drawn independently from the distribution (1.60), then the likelihood function is given by

$$
p(\mathbf{t}|\mathbf{x}, \mathbf{w}, \beta) = \prod_{n=1}^{N} \mathcal{N}\left(t_n | y(x_n, \mathbf{w}), \beta^{-1}\right).
$$
 (1.61)

As we did in the case of the simple Gaussian distribution earlier, it is convenient to maximize the logarithm of the likelihood function. Substituting for the form of the Gaussian distribution, given by (1.46), we obtain the log likelihood function in the form

$$
\ln p(\mathbf{t}|\mathbf{x}, \mathbf{w}, \beta) = -\frac{\beta}{2} \sum_{n=1}^{N} \left\{ y(x_n, \mathbf{w}) - t_n \right\}^2 + \frac{N}{2} \ln \beta - \frac{N}{2} \ln(2\pi).
$$
 (1.62)

Consider first the determination of the maximum likelihood solution for the polynomial coefficients, which will be denoted by  $w_{\text{ML}}$ . These are determined by maximizing (1.62) with respect to **w**. For this purpose, we can omit the last two terms on the right-hand side of (1.62) because they do not depend on **w**. Also, we note that scaling the log likelihood by a positive constant coefficient does not alter the location of the maximum with respect to **w**, and so we can replace the coefficient  $\beta/2$  with 1/2. Finally, instead of maximizing the log likelihood, we can equivalently minimize the negative log likelihood. We therefore see that maximizing likelihood is equivalent, so far as determining **w** is concerned, to minimizing the *sum-of-squares error function* defined by (1.2). Thus the sum-of-squares error function has arisen as a consequence of maximizing likelihood under the assumption of a Gaussian noise distribution.

We can also use maximum likelihood to determine the precision parameter  $\beta$  of the Gaussian conditional distribution. Maximizing (1.62) with respect to  $\beta$  gives

$$
\frac{1}{\beta_{\rm ML}} = \frac{1}{N} \sum_{n=1}^{N} \left\{ y(x_n, \mathbf{w}_{\rm ML}) - t_n \right\}^2.
$$
 (1.63)

Again we can first determine the parameter vector  $w_{ML}$  governing the mean and subsequently use this to find the precision  $\beta_{ML}$  as was the case for the simple Gaussian *Section 1.2.4* distribution.

> Having determined the parameters **w** and  $\beta$ , we can now make predictions for new values of x. Because we now have a probabilistic model, these are expressed in terms of the *predictive distribution* that gives the probability distribution over t, rather than simply a point estimate, and is obtained by substituting the maximum likelihood parameters into (1.60) to give

$$
p(t|x, \mathbf{w}_{\mathrm{ML}}, \beta_{\mathrm{ML}}) = \mathcal{N}\left(t|y(x, \mathbf{w}_{\mathrm{ML}}), \beta_{\mathrm{ML}}^{-1}\right).
$$
 (1.64)

Now let us take a step towards a more Bayesian approach and introduce a prior distribution over the polynomial coefficients **w**. For simplicity, let us consider a Gaussian distribution of the form

$$
p(\mathbf{w}|\alpha) = \mathcal{N}(\mathbf{w}|\mathbf{0}, \alpha^{-1}\mathbf{I}) = \left(\frac{\alpha}{2\pi}\right)^{(M+1)/2} \exp\left\{-\frac{\alpha}{2}\mathbf{w}^{\mathrm{T}}\mathbf{w}\right\} \tag{1.65}
$$

where  $\alpha$  is the precision of the distribution, and  $M+1$  is the total number of elements in the vector **w** for an  $M<sup>th</sup>$  order polynomial. Variables such as  $\alpha$ , which control the distribution of model parameters, are called *hyperparameters*. Using Bayes' theorem, the posterior distribution for **w** is proportional to the product of the prior distribution and the likelihood function

$$
p(\mathbf{w}|\mathbf{x}, \mathbf{t}, \alpha, \beta) \propto p(\mathbf{t}|\mathbf{x}, \mathbf{w}, \beta) p(\mathbf{w}|\alpha).
$$
 (1.66)

We can now determine **w** by finding the most probable value of **w** given the data, in other words by maximizing the posterior distribution. This technique is called *maximum posterior*, or simply *MAP*. Taking the negative logarithm of (1.66) and combining with  $(1.62)$  and  $(1.65)$ , we find that the maximum of the posterior is given by the minimum of

$$
\frac{\beta}{2} \sum_{n=1}^{N} \{y(x_n, \mathbf{w}) - t_n\}^2 + \frac{\alpha}{2} \mathbf{w}^{\mathrm{T}} \mathbf{w}.
$$
 (1.67)

Thus we see that maximizing the posterior distribution is equivalent to minimizing the regularized sum-of-squares error function encountered earlier in the form (1.4), with a regularization parameter given by  $\lambda = \alpha/\beta$ .

### **1.2.6 Bayesian curve fitting**

Although we have included a prior distribution  $p(\mathbf{w}|\alpha)$ , we are so far still making a point estimate of **w** and so this does not yet amount to a Bayesian treatment. In a fully Bayesian approach, we should consistently apply the sum and product rules of probability, which requires, as we shall see shortly, that we integrate over all values of **w**. Such marginalizations lie at the heart of Bayesian methods for pattern recognition.

*Section 1.2.4*

In the curve fitting problem, we are given the training data **x** and **t**, along with a new test point  $x$ , and our goal is to predict the value of  $t$ . We therefore wish to evaluate the predictive distribution  $p(t|x, \mathbf{x}, \mathbf{t})$ . Here we shall assume that the parameters  $\alpha$  and  $\beta$  are fixed and known in advance (in later chapters we shall discuss how such parameters can be inferred from data in a Bayesian setting).

A Bayesian treatment simply corresponds to a consistent application of the sum and product rules of probability, which allow the predictive distribution to be written in the form

$$
p(t|x, \mathbf{x}, \mathbf{t}) = \int p(t|x, \mathbf{w}) p(\mathbf{w}|\mathbf{x}, \mathbf{t}) \, d\mathbf{w}.
$$
 (1.68)

Here  $p(t|x, \mathbf{w})$  is given by (1.60), and we have omitted the dependence on  $\alpha$  and β to simplify the notation. Here  $p(\mathbf{w}|\mathbf{x}, \mathbf{t})$  is the posterior distribution over parameters, and can be found by normalizing the right-hand side of (1.66). We shall see in Section 3.3 that, for problems such as the curve-fitting example, this posterior distribution is a Gaussian and can be evaluated analytically. Similarly, the integration in (1.68) can also be performed analytically with the result that the predictive distribution is given by a Gaussian of the form

$$
p(t|x, \mathbf{x}, \mathbf{t}) = \mathcal{N}\left(t|m(x), s^2(x)\right) \tag{1.69}
$$

where the mean and variance are given by

$$
m(x) = \beta \phi(x)^{\mathrm{T}} \mathbf{S} \sum_{n=1}^{N} \phi(x_n) t_n \qquad (1.70)
$$

$$
s^{2}(x) = \beta^{-1} + \phi(x)^{T} \mathbf{S} \phi(x).
$$
 (1.71)

Here the matrix **S** is given by

$$
\mathbf{S}^{-1} = \alpha \mathbf{I} + \beta \sum_{n=1}^{N} \phi(x_n) \phi(x)^{\mathrm{T}}
$$
 (1.72)

where **I** is the unit matrix, and we have defined the vector  $\phi(x)$  with elements  $\phi_i(x) = x^i$  for  $i = 0, \ldots, M$ .

We see that the variance, as well as the mean, of the predictive distribution in  $(1.69)$  is dependent on x. The first term in  $(1.71)$  represents the uncertainty in the predicted value of t due to the noise on the target variables and was expressed already in the maximum likelihood predictive distribution (1.64) through  $\beta_{ML}^{-1}$ . However, the second term arises from the uncertainty in the parameters **w** and is a consequence of the Bayesian treatment. The predictive distribution for the synthetic sinusoidal regression problem is illustrated in Figure 1.17.

**Figure 1.17** The predictive distribution resulting from a Bayesian treatment of polynomial curve fitting using an  $M = 9$  polynomial, with the fixed parameters  $\alpha = 5 \times 10^{-3}$  and  $\beta =$ 11.1 (corresponding to the known noise variance), in which the red curve denotes the mean of the predictive distribution and the red region corresponds to  $\pm 1$  standard deviation around the mean.

Image /page/10/Figure/2 description: The image is a plot showing a sinusoidal curve with data points. The x-axis is labeled 'x' and ranges from 0 to 1. The y-axis is labeled 't' and ranges from -1 to 1. There are seven blue circles representing data points scattered along the curve. A red curve is drawn through these points, and a pink shaded region surrounds the red curve, indicating uncertainty. A green curve is also plotted, which appears to be a smoother version of the red curve or a different model fit. The data points are located at approximately (0, 0.5), (0.2, 0.8), (0.3, 1), (0.5, 0.2), (0.7, -0.5), (0.85, -0.6), and (1, 0.2).

## **1.3. Model Selection**

In our example of polynomial curve fitting using least squares, we saw that there was an optimal order of polynomial that gave the best generalization. The order of the polynomial controls the number of free parameters in the model and thereby governs the model complexity. With regularized least squares, the regularization coefficient  $\lambda$  also controls the effective complexity of the model, whereas for more complex models, such as mixture distributions or neural networks there may be multiple parameters governing complexity. In a practical application, we need to determine the values of such parameters, and the principal objective in doing so is usually to achieve the best predictive performance on new data. Furthermore, as well as finding the appropriate values for complexity parameters within a given model, we may wish to consider a range of different types of model in order to find the best one for our particular application.

We have already seen that, in the maximum likelihood approach, the performance on the training set is not a good indicator of predictive performance on unseen data due to the problem of over-fitting. If data is plentiful, then one approach is simply to use some of the available data to train a range of models, or a given model with a range of values for its complexity parameters, and then to compare them on independent data, sometimes called a *validation set*, and select the one having the best predictive performance. If the model design is iterated many times using a limited size data set, then some over-fitting to the validation data can occur and so it may be necessary to keep aside a third *test set* on which the performance of the selected model is finally evaluated.

In many applications, however, the supply of data for training and testing will be limited, and in order to build good models, we wish to use as much of the available data as possible for training. However, if the validation set is small, it will give a relatively noisy estimate of predictive performance. One solution to this dilemma is to use *cross-validation*, which is illustrated in Figure 1.18. This allows a proportion  $(S-1)/S$  of the available data to be used for training while making use of all of the

# **1.4. The Curse of Dimensionality 33**

**Figure 1.18** The technique of S-fold cross-validation, illustrated here for the case of  $S = 4$ , involves taking the available data and partitioning it into  $S$ groups (in the simplest case these are of equal size). Then  $S - 1$  of the groups are used to train a set of models that are then evaluated on the remaining group. This procedure is then repeated for all  $S$  possible choices for the held-out group, indicated here by the red blocks, and the performance scores from the  $S$  runs are then averaged.

Image /page/11/Figure/2 description: The image displays four rows labeled "run 1" through "run 4". Each row contains four rectangular cells. In "run 1", the first cell is shaded pink. In "run 2", the second cell is shaded pink. In "run 3", the third cell is shaded pink. In "run 4", the fourth cell is shaded pink. There is also some text to the left of the rows, but it is cut off and unreadable.

data to assess performance. When data is particularly scarce, it may be appropriate to consider the case  $S = N$ , where N is the total number of data points, which gives the *leave-one-out* technique.

One major drawback of cross-validation is that the number of training runs that must be performed is increased by a factor of S, and this can prove problematic for models in which the training is itself computationally expensive. A further problem with techniques such as cross-validation that use separate data to assess performance is that we might have multiple complexity parameters for a single model (for instance, there might be several regularization parameters). Exploring combinations of settings for such parameters could, in the worst case, require a number of training runs that is exponential in the number of parameters. Clearly, we need a better approach. Ideally, this should rely only on the training data and should allow multiple hyperparameters and model types to be compared in a single training run. We therefore need to find a measure of performance which depends only on the training data and which does not suffer from bias due to over-fitting.

Historically various 'information criteria' have been proposed that attempt to correct for the bias of maximum likelihood by the addition of a penalty term to compensate for the over-fitting of more complex models. For example, the *Akaike information criterion*, or AIC (Akaike, 1974), chooses the model for which the quantity

$$
\ln p(\mathcal{D}|\mathbf{w}_{\mathrm{ML}}) - M \tag{1.73}
$$

is largest. Here  $p(\mathcal{D}|\mathbf{w}_{\text{MI}})$  is the best-fit log likelihood, and M is the number of adjustable parameters in the model. A variant of this quantity, called the *Bayesian information criterion*, or *BIC*, will be discussed in Section 4.4.1. Such criteria do not take account of the uncertainty in the model parameters, however, and in practice they tend to favour overly simple models. We therefore turn in Section 3.4 to a fully Bayesian approach where we shall see how complexity penalties arise in a natural and principled way.

#### **1.4. The Curse of Dimensionality**

In the polynomial curve fitting example we had just one input variable  $x$ . For practical applications of pattern recognition, however, we will have to deal with spaces

**Figure 1.19** Scatter plot of the oil flow data for input variables  $x_6$  and  $x_7$ , in which red denotes the 'homogenous' class, green denotes the 'annular' class, and blue denotes the 'laminar' class. Our goal is to classify the new test point denoted by  $' \times'$ .

Image /page/12/Figure/2 description: This is a scatter plot with three clusters of data points. The x-axis is labeled "x6" and ranges from 0 to 1. The y-axis is labeled "x7" and ranges from 0 to 2. The data points are colored blue, green, and red. The blue points are clustered at the top of the plot, roughly between y-values of 1.5 and 2. The green and red points are more spread out in the lower half of the plot. There is a black cross marker located around x6=0.35 and x7=1.35. There are also a few blue points scattered near the bottom of the plot, around y=0.1.

of high dimensionality comprising many input variables. As we now discuss, this poses some serious challenges and is an important factor influencing the design of pattern recognition techniques.

In order to illustrate the problem we consider a synthetically generated data set representing measurements taken from a pipeline containing a mixture of oil, water, and gas (Bishop and James, 1993). These three materials can be present in one of three different geometrical configurations known as 'homogenous', 'annular', and 'laminar', and the fractions of the three materials can also vary. Each data point comprises a 12-dimensional input vector consisting of measurements taken with gamma ray densitometers that measure the attenuation of gamma rays passing along narrow beams through the pipe. This data set is described in detail in Appendix A. Figure 1.19 shows 100 points from this data set on a plot showing two of the measurements  $x_6$  and  $x_7$  (the remaining ten input values are ignored for the purposes of this illustration). Each data point is labelled according to which of the three geometrical classes it belongs to, and our goal is to use this data as a training set in order to be able to classify a new observation  $(x_6, x_7)$ , such as the one denoted by the cross in Figure 1.19. We observe that the cross is surrounded by numerous red points, and so we might suppose that it belongs to the red class. However, there are also plenty of green points nearby, so we might think that it could instead belong to the green class. It seems unlikely that it belongs to the blue class. The intuition here is that the identity of the cross should be determined more strongly by nearby points from the training set and less strongly by more distant points. In fact, this intuition turns out to be reasonable and will be discussed more fully in later chapters.

How can we turn this intuition into a learning algorithm? One very simple approach would be to divide the input space into regular cells, as indicated in Figure 1.20. When we are given a test point and we wish to predict its class, we first decide which cell it belongs to, and we then find all of the training data points that

**Figure 1.20** Illustration of a simple approach to the solution of a classification problem in which the input space is divided into cells and any new test point is assigned to the class that has a majority number of representatives in the same cell as the test point. As we shall see shortly, this simplistic approach has some severe shortcomings.

Image /page/13/Figure/2 description: This is a scatter plot with a black cross marker in the center. The plot has an x-axis labeled "x6" ranging from 0 to 1, and a y-axis labeled "x7" ranging from 0 to 2. The plot is divided into several colored regions: blue in the top section (y > 1.5), red in the middle section (1 < y < 1.5), and green in the bottom section (y < 1). Within these regions, there are numerous circular markers of different colors: blue circles are concentrated in the top blue region, red circles are scattered across the red and green regions, and green circles are primarily in the lower green and red regions. There are also some blue circles in the bottom green region. The plot is further divided by black grid lines, creating distinct rectangular cells.

fall in the same cell. The identity of the test point is predicted as being the same as the class having the largest number of training points in the same cell as the test point (with ties being broken at random).

There are numerous problems with this naive approach, but one of the most severe becomes apparent when we consider its extension to problems having larger numbers of input variables, corresponding to input spaces of higher dimensionality. The origin of the problem is illustrated in Figure 1.21, which shows that, if we divide a region of a space into regular cells, then the number of such cells grows exponentially with the dimensionality of the space. The problem with an exponentially large number of cells is that we would need an exponentially large quantity of training data in order to ensure that the cells are not empty. Clearly, we have no hope of applying such a technique in a space of more than a few variables, and so we need to find a more sophisticated approach.

We can gain further insight into the problems of high-dimensional spaces by *Section 1.1* returning to the example of polynomial curve fitting and considering how we would

Image /page/13/Figure/6 description: Figure 1.21 is an illustration of the curse of dimensionality. It shows how the number of regions in a regular grid grows exponentially with the dimensionality D of the space. For clarity, only a subset of the cubical regions are shown for D = 3.

Image /page/13/Figure/7 description: The image displays three diagrams illustrating dimensions D=1, D=2, and D=3. The D=1 diagram shows a single axis labeled x1 with four tick marks. The D=2 diagram shows a 2D grid in the x1-x2 plane, forming a 3x3 square. The D=3 diagram depicts a 3D structure composed of stacked cubes, resembling a staircase or pyramid shape, with axes labeled x1, x2, and x3.

extend this approach to deal with input spaces having several variables. If we have D input variables, then a general polynomial with coefficients up to order 3 would take the form

$$
y(\mathbf{x}, \mathbf{w}) = w_0 + \sum_{i=1}^{D} w_i x_i + \sum_{i=1}^{D} \sum_{j=1}^{D} w_{ij} x_i x_j + \sum_{i=1}^{D} \sum_{j=1}^{D} \sum_{k=1}^{D} w_{ijk} x_i x_j x_k.
$$
 (1.74)

As D increases, so the number of independent coefficients (not all of the coefficients are independent due to interchange symmetries amongst the  $x$  variables) grows proportionally to  $D^3$ . In practice, to capture complex dependencies in the data, we may need to use a higher-order polynomial. For a polynomial of order  $M$ , the growth in *Exercise 1.16* the number of coefficients is like  $D^M$ . Although this is now a power law growth, rather than an exponential growth, it still points to the method becoming rapidly unwieldy and of limited practical utility.

Our geometrical intuitions, formed through a life spent in a space of three dimensions, can fail badly when we consider spaces of higher dimensionality. As a simple example, consider a sphere of radius  $r = 1$  in a space of D dimensions, and ask what is the fraction of the volume of the sphere that lies between radius  $r = 1 - \epsilon$ and  $r = 1$ . We can evaluate this fraction by noting that the volume of a sphere of radius r in D dimensions must scale as  $r^D$ , and so we write

$$
V_D(r) = K_D r^D \tag{1.75}
$$

*Exercise 1.18* where the constant  $K_D$  depends only on D. Thus the required fraction is given by

$$
\frac{V_D(1) - V_D(1 - \epsilon)}{V_D(1)} = 1 - (1 - \epsilon)^D
$$
\n(1.76)

which is plotted as a function of  $\epsilon$  for various values of D in Figure 1.22. We see that, for large D, this fraction tends to 1 even for small values of  $\epsilon$ . Thus, in spaces of high dimensionality, most of the volume of a sphere is concentrated in a thin shell near the surface!

As a further example, of direct relevance to pattern recognition, consider the behaviour of a Gaussian distribution in a high-dimensional space. If we transform from Cartesian to polar coordinates, and then integrate out the directional variables, *Exercise 1.20* we obtain an expression for the density  $p(r)$  as a function of radius r from the origin. Thus  $p(r)\delta r$  is the probability mass inside a thin shell of thickness  $\delta r$  located at radius r. This distribution is plotted, for various values of  $D$ , in Figure 1.23, and we see that for large  $D$  the probability mass of the Gaussian is concentrated in a thin shell.

> The severe difficulty that can arise in spaces of many dimensions is sometimes called the *curse of dimensionality* (Bellman, 1961). In this book, we shall make extensive use of illustrative examples involving input spaces of one or two dimensions, because this makes it particularly easy to illustrate the techniques graphically. The reader should be warned, however, that not all intuitions developed in spaces of low dimensionality will generalize to spaces of many dimensions.

**Figure 1.22** Plot of the fraction of the volume of a sphere lying in the range  $r = 1-\epsilon$ to  $r = 1$  for various values of the dimensionality D.

Image /page/15/Figure/2 description: The image is a line graph showing the relationship between epsilon on the x-axis and volume fraction on the y-axis. There are four distinct blue lines, each representing a different value of D: D=20, D=5, D=2, and D=1. The lines start at the origin (0,0) and generally increase as epsilon increases. The line for D=20 rises most steeply, reaching a volume fraction of 1 at an epsilon value of approximately 0.1. The line for D=5 reaches a volume fraction of 1 at an epsilon value of approximately 0.3. The line for D=2 reaches a volume fraction of 1 at an epsilon value of approximately 0.6. The line for D=1 is a straight line from (0,0) to (1,1), indicating a linear relationship.

Although the curse of dimensionality certainly raises important issues for pattern recognition applications, it does not prevent us from finding effective techniques applicable to high-dimensional spaces. The reasons for this are twofold. First, real data will often be confined to a region of the space having lower effective dimensionality, and in particular the directions over which important variations in the target variables occur may be so confined. Second, real data will typically exhibit some smoothness properties (at least locally) so that for the most part small changes in the input variables will produce small changes in the target variables, and so we can exploit local interpolation-like techniques to allow us to make predictions of the target variables for new values of the input variables. Successful pattern recognition techniques exploit one or both of these properties. Consider, for example, an application in manufacturing in which images are captured of identical planar objects on a conveyor belt, in which the goal is to determine their orientation. Each image is a point

**Figure 1.23** Plot of the probability density with respect to radius  $r$  of a Gaussian distribution for various values of the dimensionality  $D$ . In a high-dimensional space, most of the probability mass of a Gaussian is located within a thin shell at a specific radius.

Image /page/15/Figure/5 description: This is a line graph showing the probability distribution p(r) as a function of r. There are three curves, labeled D=1 (red), D=2 (green), and D=20 (blue). The red curve for D=1 starts at a high value at r=0 and decreases rapidly, crossing the x-axis around r=1.5. The green curve for D=2 starts at a lower value at r=0, peaks at approximately r=0.7 with a value of about 1.2, and then decreases, crossing the x-axis around r=2.5. The blue curve for D=20 starts at r=0 with a value of 0, peaks at approximately r=2 with a value of about 1.2, and then decreases, crossing the x-axis around r=3.5. The x-axis is labeled 'r' and ranges from 0 to 4. The y-axis is labeled 'p(r)' and ranges from 0 to 2.

in a high-dimensional space whose dimensionality is determined by the number of pixels. Because the objects can occur at different positions within the image and in different orientations, there are three degrees of freedom of variability between images, and a set of images will live on a three dimensional *manifold* embedded within the high-dimensional space. Due to the complex relationships between the object position or orientation and the pixel intensities, this manifold will be highly nonlinear. If the goal is to learn a model that can take an input image and output the orientation of the object irrespective of its position, then there is only one degree of freedom of variability within the manifold that is significant.

## **1.5. Decision Theory**

We have seen in Section 1.2 how probability theory provides us with a consistent mathematical framework for quantifying and manipulating uncertainty. Here we turn to a discussion of decision theory that, when combined with probability theory, allows us to make optimal decisions in situations involving uncertainty such as those encountered in pattern recognition.

Suppose we have an input vector **x** together with a corresponding vector **t** of target variables, and our goal is to predict **t** given a new value for **x**. For regression problems, **t** will comprise continuous variables, whereas for classification problems **t** will represent class labels. The joint probability distribution  $p(x, t)$  provides a complete summary of the uncertainty associated with these variables. Determination of  $p(x, t)$  from a set of training data is an example of *inference* and is typically a very difficult problem whose solution forms the subject of much of this book. In a practical application, however, we must often make a specific prediction for the value of **t**, or more generally take a specific action based on our understanding of the values **t** is likely to take, and this aspect is the subject of decision theory.

Consider, for example, a medical diagnosis problem in which we have taken an X-ray image of a patient, and we wish to determine whether the patient has cancer or not. In this case, the input vector **x** is the set of pixel intensities in the image, and output variable  $t$  will represent the presence of cancer, which we denote by the class  $C_1$ , or the absence of cancer, which we denote by the class  $C_2$ . We might, for instance, choose t to be a binary variable such that  $t = 0$  corresponds to class  $C_1$  and  $t = 1$  corresponds to class  $C_2$ . We shall see later that this choice of label values is particularly convenient for probabilistic models. The general inference problem then involves determining the joint distribution  $p(\mathbf{x}, \mathcal{C}_k)$ , or equivalently  $p(\mathbf{x}, t)$ , which gives us the most complete probabilistic description of the situation. Although this can be a very useful and informative quantity, in the end we must decide either to give treatment to the patient or not, and we would like this choice to be optimal in some appropriate sense (Duda and Hart, 1973). This is the *decision* step, and it is the subject of decision theory to tell us how to make optimal decisions given the appropriate probabilities. We shall see that the decision stage is generally very simple, even trivial, once we have solved the inference problem.

Here we give an introduction to the key ideas of decision theory as required for

the rest of the book. Further background, as well as more detailed accounts, can be found in Berger (1985) and Bather (2000).

Before giving a more detailed analysis, let us first consider informally how we might expect probabilities to play a role in making decisions. When we obtain the X-ray image **x** for a new patient, our goal is to decide which of the two classes to assign to the image. We are interested in the probabilities of the two classes given the image, which are given by  $p(\mathcal{C}_k|\mathbf{x})$ . Using Bayes' theorem, these probabilities can be expressed in the form

$$
p(C_k|\mathbf{x}) = \frac{p(\mathbf{x}|C_k)p(C_k)}{p(\mathbf{x})}.
$$
\n(1.77)

Note that any of the quantities appearing in Bayes' theorem can be obtained from the joint distribution  $p(\mathbf{x}, \mathcal{C}_k)$  by either marginalizing or conditioning with respect to the appropriate variables. We can now interpret  $p(C_k)$  as the prior probability for the class  $\mathcal{C}_k$ , and  $p(\mathcal{C}_k|\mathbf{x})$  as the corresponding posterior probability. Thus  $p(\mathcal{C}_1)$  represents the probability that a person has cancer, before we take the X-ray measurement. Similarly,  $p(C_1|\mathbf{x})$  is the corresponding probability, revised using Bayes' theorem in light of the information contained in the X-ray. If our aim is to minimize the chance of assigning **x** to the wrong class, then intuitively we would choose the class having the higher posterior probability. We now show that this intuition is correct, and we also discuss more general criteria for making decisions.

#### **1.5.1 Minimizing the misclassification rate**

Suppose that our goal is simply to make as few misclassifications as possible. We need a rule that assigns each value of **x** to one of the available classes. Such a rule will divide the input space into regions  $\mathcal{R}_k$  called *decision regions*, one for each class, such that all points in  $\mathcal{R}_k$  are assigned to class  $\mathcal{C}_k$ . The boundaries between decision regions are called *decision boundaries* or *decision surfaces*. Note that each decision region need not be contiguous but could comprise some number of disjoint regions. We shall encounter examples of decision boundaries and decision regions in later chapters. In order to find the optimal decision rule, consider first of all the case of two classes, as in the cancer problem for instance. A mistake occurs when an input vector belonging to class  $C_1$  is assigned to class  $C_2$  or vice versa. The probability of this occurring is given by

$$
p(mistake) = p(\mathbf{x} \in \mathcal{R}_1, C_2) + p(\mathbf{x} \in \mathcal{R}_2, C_1)
$$
  

$$
= \int_{\mathcal{R}_1} p(\mathbf{x}, C_2) \, d\mathbf{x} + \int_{\mathcal{R}_2} p(\mathbf{x}, C_1) \, d\mathbf{x}.
$$
 (1.78)

We are free to choose the decision rule that assigns each point **x** to one of the two classes. Clearly to minimize  $p(mistake)$  we should arrange that each **x** is assigned to whichever class has the smaller value of the integrand in (1.78). Thus, if  $p(\mathbf{x}, C_1)$  $p(\mathbf{x}, \mathcal{C}_2)$  for a given value of **x**, then we should assign that **x** to class  $\mathcal{C}_1$ . From the product rule of probability we have  $p(\mathbf{x}, \mathcal{C}_k) = p(\mathcal{C}_k|\mathbf{x})p(\mathbf{x})$ . Because the factor  $p(x)$  is common to both terms, we can restate this result as saying that the minimum

Image /page/18/Figure/1 description: This image displays two probability density functions, labeled p(x, C1) and p(x, C2), plotted against the variable x. The first function, p(x, C1), is a bell-shaped curve peaking at a lower x-value, while p(x, C2) is another bell-shaped curve peaking at a higher x-value. A vertical dashed line is drawn at x0, and a vertical solid line is drawn at x-hat. The area under p(x, C1) to the left of x0 is shaded green, and the area under p(x, C2) between x0 and x-hat is shaded red. The area under p(x, C2) to the right of x-hat is shaded blue. The x-axis is divided into two regions, R1 to the left of x0 and R2 to the right of x-hat, with an overlapping region between x0 and x-hat.

**Figure 1.24** Schematic illustration of the joint probabilities  $p(x, C_k)$  for each of two classes plotted against x, together with the decision boundary  $x = \widehat{x}$ . Values of  $x \geqslant \widehat{x}$  are classified as class  $C_2$  and hence belong to decision region  $\mathcal{R}_2$ , whereas points  $x < \hat{x}$  are classified as  $C_1$  and belong to  $\mathcal{R}_1$ . Errors arise from the blue, green, and red regions, so that for  $x < \hat{x}$  the errors are due to points from class  $\mathcal{C}_2$  being misclassified as  $\mathcal{C}_1$  (represented by the sum of the red and green regions), and conversely for points in the region  $x \geqslant \hat{x}$  the errors are due to points from class  $C_1$  being misclassified as  $C_2$  (represented by the blue region). As we vary the location  $\hat{x}$  of the decision boundary, the combined areas of the blue and green regions remains constant, whereas the size of the red region varies. The optimal choice for  $\hat{x}$  is where the curves for  $p(x, C_1)$  and  $p(x, C_2)$  cross, corresponding to  $\widehat{x} = x_0$ , because in this case the red region disappears. This is equivalent to the minimum misclassification rate decision rule, which assigns each value of  $x$  to the class having the higher posterior probability  $p(\mathcal{C}_k|x)$ .

> probability of making a mistake is obtained if each value of **x** is assigned to the class for which the posterior probability  $p(C_k|\mathbf{x})$  is largest. This result is illustrated for two classes, and a single input variable  $x$ , in Figure 1.24.

> For the more general case of  $K$  classes, it is slightly easier to maximize the probability of being correct, which is given by

$$
p(\text{correct}) = \sum_{k=1}^{K} p(\mathbf{x} \in \mathcal{R}_k, C_k)
$$
$$
= \sum_{k=1}^{K} \int_{\mathcal{R}_k} p(\mathbf{x}, C_k) d\mathbf{x}
$$
(1.79)

which is maximized when the regions  $\mathcal{R}_k$  are chosen such that each **x** is assigned to the class for which  $p(\mathbf{x}, \mathcal{C}_k)$  is largest. Again, using the product rule  $p(\mathbf{x}, \mathcal{C}_k)$  =  $p(\mathcal{C}_k|\mathbf{x})p(\mathbf{x})$ , and noting that the factor of  $p(\mathbf{x})$  is common to all terms, we see that each **x** should be assigned to the class having the largest posterior probability  $p(\mathcal{C}_k|\mathbf{x}).$ 

**Figure 1.25** An example of a loss matrix with elements  $L_{kj}$  for the cancer treatment problem. The rows correspond to the true class, whereas the columns correspond to the assignment of class made by our decision criterion.

|                   | cancer            | normal |
|-------------------|-------------------|--------|
| cancer $\sqrt{ }$ | $\mathbf{\Omega}$ | 1000   |
| normal \          |                   |        |

#### **1.5.2 Minimizing the expected loss**

For many applications, our objective will be more complex than simply minimizing the number of misclassifications. Let us consider again the medical diagnosis problem. We note that, if a patient who does not have cancer is incorrectly diagnosed as having cancer, the consequences may be some patient distress plus the need for further investigations. Conversely, if a patient with cancer is diagnosed as healthy, the result may be premature death due to lack of treatment. Thus the consequences of these two types of mistake can be dramatically different. It would clearly be better to make fewer mistakes of the second kind, even if this was at the expense of making more mistakes of the first kind.

We can formalize such issues through the introduction of a *loss function*, also called a *cost function*, which is a single, overall measure of loss incurred in taking any of the available decisions or actions. Our goal is then to minimize the total loss incurred. Note that some authors consider instead a *utility function*, whose value they aim to maximize. These are equivalent concepts if we take the utility to be simply the negative of the loss, and throughout this text we shall use the loss function convention. Suppose that, for a new value of **x**, the true class is  $\mathcal{C}_k$  and that we assign **x** to class  $C_i$  (where j may or may not be equal to k). In so doing, we incur some level of loss that we denote by  $L_{ki}$ , which we can view as the  $k, j$  element of a *loss matrix*. For instance, in our cancer example, we might have a loss matrix of the form shown in Figure 1.25. This particular loss matrix says that there is no loss incurred if the correct decision is made, there is a loss of 1 if a healthy patient is diagnosed as having cancer, whereas there is a loss of 1000 if a patient having cancer is diagnosed as healthy.

The optimal solution is the one which minimizes the loss function. However, the loss function depends on the true class, which is unknown. For a given input vector **x**, our uncertainty in the true class is expressed through the joint probability distribution  $p(\mathbf{x}, \mathcal{C}_k)$  and so we seek instead to minimize the average loss, where the average is computed with respect to this distribution, which is given by

$$
\mathbb{E}[L] = \sum_{k} \sum_{j} \int_{\mathcal{R}_{j}} L_{kj} p(\mathbf{x}, C_{k}) \, \mathrm{d}\mathbf{x}.
$$
 (1.80)

Each **x** can be assigned independently to one of the decision regions  $\mathcal{R}_i$ . Our goal is to choose the regions  $\mathcal{R}_j$  in order to minimize the expected loss (1.80), which implies that for each **x** we should minimize  $\sum_{k} L_{kj} p(\mathbf{x}, \hat{C}_k)$ . As before, we can use the product rule  $p(\mathbf{x}, \hat{C}_k) = p(\hat{C}_k | \mathbf{x}) p(\mathbf{x})$  to eliminate the common factor of  $p(\mathbf{x})$ the product rule  $p(\mathbf{x}, \mathcal{C}_k) = p(\mathcal{C}_k|\mathbf{x})p(\mathbf{x})$  to eliminate the common factor of  $p(\mathbf{x})$ . Thus the decision rule that minimizes the expected loss is the one that assigns each

**Figure 1.26** Illustration of the reject option. Inputs  $x$  such that the larger of the two posterior probabilities is less than or equal to some threshold  $\theta$  will be rejected.

Image /page/20/Figure/2 description: The image is a plot showing two curves, labeled p(C1|x) in blue and p(C2|x) in red, against a variable x on the horizontal axis. The vertical axis ranges from 0.0 to 1.0. A dashed green line is drawn at a level labeled \u03b8. Two vertical green lines are shown, indicating a region labeled "reject region" on the x-axis. The blue curve starts at 1.0, decreases, and then flattens out at 0.0. The red curve starts at 0.0, increases, and then flattens out at 1.0. The curves intersect at the level of the dashed green line, which is also intersected by the vertical green lines.

new  $x$  to the class  $j$  for which the quantity

$$
\sum_{k} L_{kj} p(\mathcal{C}_k | \mathbf{x}) \tag{1.81}
$$

is a minimum. This is clearly trivial to do, once we know the posterior class probabilities  $p(\mathcal{C}_k|\mathbf{x})$ .

### **1.5.3 The reject option**

We have seen that classification errors arise from the regions of input space where the largest of the posterior probabilities  $p(C_k|\mathbf{x})$  is significantly less than unity, or equivalently where the joint distributions  $p(x, C_k)$  have comparable values. These are the regions where we are relatively uncertain about class membership. In some applications, it will be appropriate to avoid making decisions on the difficult cases in anticipation of a lower error rate on those examples for which a classification decision is made. This is known as the *reject option*. For example, in our hypothetical medical illustration, it may be appropriate to use an automatic system to classify those X-ray images for which there is little doubt as to the correct class, while leaving a human expert to classify the more ambiguous cases. We can achieve this by introducing a threshold  $\theta$  and rejecting those inputs **x** for which the largest of the posterior probabilities  $p(C_k|\mathbf{x})$  is less than or equal to  $\theta$ . This is illustrated for the case of two classes, and a single continuous input variable  $x$ , in Figure 1.26. Note that setting  $\theta = 1$  will ensure that all examples are rejected, whereas if there are K classes then setting  $\theta < 1/K$  will ensure that no examples are rejected. Thus the fraction of examples that get rejected is controlled by the value of  $\theta$ .

We can easily extend the reject criterion to minimize the expected loss, when a loss matrix is given, taking account of the loss incurred when a reject decision is

### **1.5.4 Inference and decision**

We have broken the classification problem down into two separate stages, the *inference stage* in which we use training data to learn a model for  $p(\mathcal{C}_k|\mathbf{x})$ , and the

*Exercise 1.24* made.

subsequent *decision* stage in which we use these posterior probabilities to make optimal class assignments. An alternative possibility would be to solve both problems together and simply learn a function that maps inputs **x** directly into decisions. Such a function is called a *discriminant function*.

In fact, we can identify three distinct approaches to solving decision problems, all of which have been used in practical applications. These are given, in decreasing order of complexity, by:

**(a)** First solve the inference problem of determining the class-conditional densities  $p(\mathbf{x}|\mathcal{C}_k)$  for each class  $\mathcal{C}_k$  individually. Also separately infer the prior class probabilities  $p(\mathcal{C}_k)$ . Then use Bayes' theorem in the form

$$
p(C_k|\mathbf{x}) = \frac{p(\mathbf{x}|C_k)p(C_k)}{p(\mathbf{x})}
$$
\n(1.82)

to find the posterior class probabilities  $p(\mathcal{C}_k|\mathbf{x})$ . As usual, the denominator in Bayes' theorem can be found in terms of the quantities appearing in the numerator, because

$$
p(\mathbf{x}) = \sum_{k} p(\mathbf{x}|\mathcal{C}_k) p(\mathcal{C}_k).
$$
 (1.83)

Equivalently, we can model the joint distribution  $p(\mathbf{x}, \mathcal{C}_k)$  directly and then normalize to obtain the posterior probabilities. Having found the posterior probabilities, we use decision theory to determine class membership for each new input **x**. Approaches that explicitly or implicitly model the distribution of inputs as well as outputs are known as *generative models*, because by sampling from them it is possible to generate synthetic data points in the input space.

- **(b)** First solve the inference problem of determining the posterior class probabilities  $p(\mathcal{C}_k|\mathbf{x})$ , and then subsequently use decision theory to assign each new **x** to one of the classes. Approaches that model the posterior probabilities directly are called *discriminative models*.
- (c) Find a function  $f(x)$ , called a discriminant function, which maps each input **x** directly onto a class label. For instance, in the case of two-class problems,  $f(\cdot)$  might be binary valued and such that  $f = 0$  represents class  $C_1$  and  $f = 1$ represents class  $C_2$ . In this case, probabilities play no role.

Let us consider the relative merits of these three alternatives. Approach (a) is the most demanding because it involves finding the joint distribution over both **x** and  $\mathcal{C}_k$ . For many applications, x will have high dimensionality, and consequently we may need a large training set in order to be able to determine the class-conditional densities to reasonable accuracy. Note that the class priors  $p(\mathcal{C}_k)$  can often be estimated simply from the fractions of the training set data points in each of the classes. One advantage of approach (a), however, is that it also allows the marginal density of data  $p(x)$  to be determined from (1.83). This can be useful for detecting new data points that have low probability under the model and for which the predictions may

Image /page/22/Figure/1 description: The image displays two plots side-by-side. The left plot, titled 'class densities', shows two probability density functions, labeled p(x|C1) in blue and p(x|C2) in red, plotted against the variable 'x' on the x-axis, ranging from 0 to 1. The y-axis ranges from 0 to 5. The blue curve, p(x|C1), has two peaks, one around x=0.2 and another smaller one around x=0.5. The red curve, p(x|C2), has a single large peak around x=0.65. The right plot shows two curves, labeled p(C1|x) in blue and p(C2|x) in red, plotted against 'x' on the x-axis, also ranging from 0 to 1. The y-axis ranges from 0 to 1.2. The blue curve, p(C1|x), starts at 1 and decreases as x increases, crossing the red curve around x=0.55. The red curve, p(C2|x), starts at 0 and increases as x increases, crossing the blue curve at the same point. A vertical green line is drawn at approximately x=0.55, indicating the decision boundary where the probabilities of the two classes are equal.

**Figure 1.27** Example of the class-conditional densities for two classes having a single input variable  $x$  (left plot) together with the corresponding posterior probabilities (right plot). Note that the left-hand mode of the class-conditional density  $p(x|C_1)$ , shown in blue on the left plot, has no effect on the posterior probabilities. The vertical green line in the right plot shows the decision boundary in  $x$  that gives the minimum misclassification rate.

be of low accuracy, which is known as *outlier detection* or *novelty detection* (Bishop, 1994; Tarassenko, 1995).

However, if we only wish to make classification decisions, then it can be wasteful of computational resources, and excessively demanding of data, to find the joint distribution  $p(\mathbf{x}, \mathcal{C}_k)$  when in fact we only really need the posterior probabilities  $p(\mathcal{C}_k|\mathbf{x})$ , which can be obtained directly through approach (b). Indeed, the classconditional densities may contain a lot of structure that has little effect on the posterior probabilities, as illustrated in Figure 1.27. There has been much interest in exploring the relative merits of generative and discriminative approaches to machine learning, and in finding ways to combine them (Jebara, 2004; Lasserre *et al.*, 2006).

An even simpler approach is (c) in which we use the training data to find a discriminant function  $f(\mathbf{x})$  that maps each  $\mathbf{x}$  directly onto a class label, thereby combining the inference and decision stages into a single learning problem. In the example of Figure 1.27, this would correspond to finding the value of x shown by the vertical green line, because this is the decision boundary giving the minimum probability of misclassification.

With option (c), however, we no longer have access to the posterior probabilities  $p(\mathcal{C}_k|\mathbf{x})$ . There are many powerful reasons for wanting to compute the posterior probabilities, even if we subsequently use them to make decisions. These include:

**Minimizing risk.** Consider a problem in which the elements of the loss matrix are subjected to revision from time to time (such as might occur in a financial

application). If we know the posterior probabilities, we can trivially revise the minimum risk decision criterion by modifying (1.81) appropriately. If we have only a discriminant function, then any change to the loss matrix would require that we return to the training data and solve the classification problem afresh.

- **Reject option.** Posterior probabilities allow us to determine a rejection criterion that will minimize the misclassification rate, or more generally the expected loss, for a given fraction of rejected data points.
- **Compensating for class priors.** Consider our medical X-ray problem again, and suppose that we have collected a large number of X-ray images from the general population for use as training data in order to build an automated screening system. Because cancer is rare amongst the general population, we might find that, say, only 1 in every 1,000 examples corresponds to the presence of cancer. If we used such a data set to train an adaptive model, we could run into severe difficulties due to the small proportion of the cancer class. For instance, a classifier that assigned every point to the normal class would already achieve 99.9% accuracy and it would be difficult to avoid this trivial solution. Also, even a large data set will contain very few examples of X-ray images corresponding to cancer, and so the learning algorithm will not be exposed to a broad range of examples of such images and hence is not likely to generalize well. A balanced data set in which we have selected equal numbers of examples from each of the classes would allow us to find a more accurate model. However, we then have to compensate for the effects of our modifications to the training data. Suppose we have used such a modified data set and found models for the posterior probabilities. From Bayes' theorem (1.82), we see that the posterior probabilities are proportional to the prior probabilities, which we can interpret as the fractions of points in each class. We can therefore simply take the posterior probabilities obtained from our artificially balanced data set and first divide by the class fractions in that data set and then multiply by the class fractions in the population to which we wish to apply the model. Finally, we need to normalize to ensure that the new posterior probabilities sum to one. Note that this procedure cannot be applied if we have learned a discriminant function directly instead of determining posterior probabilities.
- **Combining models.** For complex applications, we may wish to break the problem into a number of smaller subproblems each of which can be tackled by a separate module. For example, in our hypothetical medical diagnosis problem, we may have information available from, say, blood tests as well as X-ray images. Rather than combine all of this heterogeneous information into one huge input space, it may be more effective to build one system to interpret the Xray images and a different one to interpret the blood data. As long as each of the two models gives posterior probabilities for the classes, we can combine the outputs systematically using the rules of probability. One simple way to do this is to assume that, for each class separately, the distributions of inputs for the X-ray images, denoted by  $x_I$ , and the blood data, denoted by  $x_B$ , are
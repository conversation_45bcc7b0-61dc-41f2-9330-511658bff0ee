# Remember the Past: Distilling Datasets into Addressable Memories for Neural Networks

Zhiwei Deng Olga <PERSON>akovsky Department of Computer Science Princeton University {zhiweid, olgarus}@cs.princeton.edu

## Abstract

We propose an algorithm that compresses the critical information of a large dataset into compact addressable memories. These memories can then be recalled to quickly re-train a neural network and recover the performance (instead of storing and re-training on the full original dataset). Building upon the dataset distillation framework, we make a key observation that a *shared common representation* allows for more efficient and effective distillation. Concretely, we learn a set of bases (aka "memories") which are shared between classes and combined through learned flexible addressing functions to generate a diverse set of training examples. This leads to several benefits: 1) the size of compressed data does not necessarily grow linearly with the number of classes; 2) an overall higher compression rate with more effective distillation is achieved; and 3) more generalized queries are allowed beyond recalling the original classes. We demonstrate state-of-the-art results on the dataset distillation task across six benchmarks, including up to 16.5% and 9.7% in retained accuracy improvement when distilling CIFAR10 and CIFAR100 respectively. We then leverage our framework to perform continual learning, achieving state-of-the-art results on four benchmarks, with 23.2% accuracy improvement on MANY. The code is released on our project webpage<sup>[1](#page-0-0)</sup>.

## 1 Introduction

Compressing a large amount of information into a small memory storage space is one of the key components of human intelligence  $[1-3]$  $[1-3]$  – a person can retrieve memories from the past and quickly recover the corresponding skills. Deep learning methods have made large strides in building taskspecific models, but are shown to easily forget past knowledge when learning new tasks [\[4,](#page-10-2) [5\]](#page-10-3).

To equip neural network learners with memorizing ability, dataset distillation [\[6\]](#page-10-4) is proposed as a potential solution. Concretely, a compressed set of examples (memories) is learned to summarize the key information in a dataset that affects model training; these examples can then be used to quickly retrain models and recover the corresponding skills. This differs from the standard reconstructionbased compression algorithms [\[7](#page-10-5)[–9\]](#page-10-6) and shows strong performance [\[10–](#page-10-7)[13\]](#page-10-8).

A critical question in building powerful compressed memories is: what structures and representations should we use to build the memories? An effective structure and organization of memories can lead to different fundamental assumptions about data and affect the compression and learning behaviours. Existing works [\[10,](#page-10-7) [11,](#page-10-9) [14,](#page-10-10) [12,](#page-10-11) [15,](#page-10-12) [13,](#page-10-8) [6\]](#page-10-4) follow a simple representation, where a set of learnable examples is assigned for each class. However, under this assumption, the size of the memories can linearly grow with the number of classes, making the distillation of datasets with a large number of classes challenging. Naturally, this can potentially lead to redundancies in the learned memories,

<span id="page-0-0"></span><sup>1</sup> <https://github.com/princetonvisualai/RememberThePast-DatasetDistillation>

<sup>36</sup>th Conference on Neural Information Processing Systems (NeurIPS 2022).

due to the separation of data among classes. Furthermore, this representation is less generalizable to continuous label space, where infinite number of label values exists.

In our paper, we make the observation that there is information shared between classes, and hypothesize that a common and compact representation exists for all classes. Following this hypothesis, we propose to formulate the problem as a memory addressing process, where the memories store a common set of bases shared by all classes, and the recombination of bases is performed through an addressing function. This decomposition between memories and addressing functions enables the possibility that all common information is stored in one part of the representation, and the accessing of the common information depends on the specific labels and is handled through an extra function. We find that this formulation can significantly improve both the compression rate and the performance.

We adopt the back-propagation through time learning framework to train the memories and addressing functions, and identify several critical factors that can improve the performance. Specifically, we find that adopting the momentum term, and performing long unrolls in the inner optimization loop are both critical. This differs from the common usage of bi-level optimization algorithm on this task [\[6,](#page-10-4) [16\]](#page-10-13), and leads to strong performance outperforming single-step gradient matching methods [\[10,](#page-10-7) [11\]](#page-10-9) even with the simple data representation.

In the experiments, we extensively evaluate our algorithm on six benchmarks of the Dataset Distillation task, and show that it consistently outperforms previous state-of-the-art by a significant margin. For example, we achieve 66.4% accuracy on CIFAR10 with the storage space of 1 image per class, improving over the previous state-of-the-art KIP method [\[12,](#page-10-11) [13\]](#page-10-8) by 16.5%. We further demonstrate our method on the continual learning tasks, and show that a simple "compress-then-recall" method using our framework leads to state-of-the-art results on four datasets. For example, we outperform all prior methods by 23.2% in retained accuracy on the challenging MANY [\[17\]](#page-10-14) benchmark. Finally, we demonstrate the generality of our approach by extending to image-based (rather than label-based) memory recall, and synthesizing new classifiers (unseen during training) from our distilled memories.

# 2 Related works

Dataset Distillation. The task of dataset distillation is fundamentally a compression problem, with a different prioritization on the information contained in data. There have been several lines of methods, developed with different criteria to prioritize information. *Generalization loss* with bi-level optimization framework [\[18–](#page-10-15)[20\]](#page-11-0) has been widely studied and is used in the early works of dataset distillation [\[6,](#page-10-4) [16\]](#page-10-13). It emphasizes on the loss at the final optimization state. *Gradient-matching or score-matching* methods [\[10,](#page-10-7) [11,](#page-10-9) [15\]](#page-10-12) are adopted to directly match the induced gradients from synthetic data. If ideally matched over the gradient field, the compressed dataset can naturally lead to the same model parameters with gradient descent. *Kernel method* [\[13,](#page-10-8) [12\]](#page-10-11) shows that with the connection to Gaussian processes, a kernel inducing points method can be used to achieve strong performance, but with large computation costs. These are also connected with the recent progress on pragmatic compression methods [\[21,](#page-11-1) [22\]](#page-11-2), which compress or match distributions based on a decision process (in dataset distillation's case, the gradient descent search process).

Continual learning. Broadening the learning paradigms, continual learning problem aims to build agents that learn through a stream of tasks and accrue knowledge along the process. "Catastrophic forgetting" [\[5,](#page-10-3) [23,](#page-11-3) [4\]](#page-10-2) is a well-known phenomenon in this setting, where the neural network forgets previous skills when learning new ones. Various methods [\[24–](#page-11-4)[27,](#page-11-5) [17,](#page-10-14) [28](#page-11-6)[–35\]](#page-11-7) on regularization, replay, or dynamic model, have been proposed to alleviate the issue and address the "stabilityplasticity dilemma" [\[36,](#page-11-8) [37\]](#page-11-9). Memory buffer has been a critical component in the past methods [\[24–](#page-11-4) [27,](#page-11-5) [17,](#page-10-14) [28,](#page-11-6) [38,](#page-12-0) [39\]](#page-12-1), but mainly relies on a random selection of real samples with different strategies. Recently, several works extend the usage of memory to storing random basis [\[39\]](#page-12-1) (online setting) or SVD bases [\[38\]](#page-12-0) (offline setting).

# 3 Background: dataset distillation

The task of Dataset Distillation [\[6\]](#page-10-4) is proposed to compress the key information of a large-scale training dataset into a small amount of learned data, which can be stored using limited memory space and retrieved through label indices or task information to recover the performance of a model.

**Problem Setting.** Formally, given a large dataset  $\mathcal{D}_{tr} = \{(\bm{x}_i, \bm{y}_i)\}_{i=1}^N$  containing  $N$  pairs of training data  $(x_i, y_i)$ , where  $x_i$  is an image and  $y_i$  is the corresponding label in C classes, a small dataset  $\mathcal{D}_s = \{(\boldsymbol{x}'_j, \boldsymbol{y}_j)\}_{j=1}^{N'}$ ,  $N' \ll N$ , can be synthesized or distilled, such that a model trained on  $\mathcal{D}_s$  can have the same generalization ability as ones trained on  $\mathcal{D}_{tr}$ :

<span id="page-2-1"></span>
$$
\mathbb{E}_{(\boldsymbol{x},\boldsymbol{y})\sim\mathcal{D}_{te}}\big[\mathbf{m}(f(\boldsymbol{x};\boldsymbol{\theta}^{(*)}),\boldsymbol{y})\big] \simeq \mathbb{E}_{(\boldsymbol{x},\boldsymbol{y})\sim\mathcal{D}_{te}}\big[\mathbf{m}(f(\boldsymbol{x};\boldsymbol{\theta}'^{(*)}),\boldsymbol{y})\big] \tag{1}
$$

where  $\theta^{(*)}$  and  $\theta'^{(*)}$  are the optimized parameters using  $\mathcal{D}_{tr}$  and  $\mathcal{D}_s$  respectively, m is a metric, e.g., accuracy, and  $\mathcal{D}_{te}$  is the test dataset. The model is often a neural network classifier  $f(\cdot;\boldsymbol{\theta})$ parameterized by  $\theta$  and trained with a loss function  $\ell(f(\mathbf{x}; \theta), \mathbf{y})$ .

Synthetic dataset representations. The synthetic dataset contains the core information that needs to be learned. The representation of the synthetic data affects the compactness and effectiveness of the distillation process. In existing methods [\[6,](#page-10-4) [10,](#page-10-7) [11,](#page-10-9) [14,](#page-10-10) [12,](#page-10-11) [15\]](#page-10-12), the dataset  $\mathcal{D}_s$  is defined as a collection of learnable data samples  $(x', y)$ , and the number of samples is separately and equally distributed across classes. This representation has several disadvantages: first, the number of synthetic data samples needed for a dataset grows linearly with the number of classes, leading to limited applicability when the number of classes is large or undefined (e.g., language or other continuous labels); second, the potentially shared and common information across classes is ignored – this results in a less compact representation of the distilled information and lower compression rate; lastly, the representation is not able to generalize to new classes or tasks, due to the lack of common representation learned across classes.

## 4 Model

Overview. In this section, first, we present a new perspective of the problem, where the Dataset Distillation problem is formulated as a *memory addressing process*: instead of learning synthetic images separately for each class, we construct and learn a common memory representation that can be accessed through addressing matrices to construct synthetic datasets. Under this formulation, the number of synthetic images does not need to grow linearly with the number of classes, the shared information among classes can be exploited to reduce redundancies and improve compression rate, and datasets can be distilled with respect to more generic queries. Second, we further show several critical empirical facets of back-propagation through time framework, which lead to drastic improvements on the performance and outperform the single-step gradient matching methods. This is in contrast with the current common observation that gradient matching outperforms back-propagation through time framework on dataset distillation tasks.

In the following, we present the two core components of our method, (1) the new formulation of dataset distillation, with memories and addressing matrices in Sec. [4.1,](#page-2-0) and (2) the learning framework under back-propagation through time in Sec. [4.2.](#page-4-0)

<span id="page-2-0"></span>

### 4.1 Dataset Distillation as memory addressing

**Problem formulation.** Given a task-specific dataset  $\mathcal{D}_{tr} = \{x_i, y_i\}_{i=1}^N, x \in \mathcal{X}, y \in \mathcal{Y}$ , we aim to learn a single compact and compressed representation  $M$ , referred to as memories, that can be accessed through a learned addressing function  $A(\cdot)$ .  $A(\cdot)$  takes all possible values of y as input and recalls the corresponding synthetic data. With a set of  $\{y_i\}$ , a synthetic dataset recalled using the above process can train a model  $f_{\theta}: \mathcal{X} \to \mathcal{Y}$  from scratch and obtain the same generalization ability as trained on  $\mathcal{D}_{tr}$ .

Ideally, the memories  $M$  and the addressing function  $A$  can jointly capture the critical information that defines the task mapping from X to Y, such that the recalled synthetic data given a query  $y_i$ contains distinctive information that defines  $y_i$  and the synthetic dataset recalled with  $\{y_i\}$  can satisfy eqn. [1](#page-2-1) when used for re-training. For example, in the standard classification tasks, we can enumerate all possible values in the label space (discrete) and address the memories, to construct the synthetic dataset that contains critical information. Under this formulation, since we are learning a single, shared and accessible representation for all  $\mathbf{y}_s$ , the size of memories can be defined flexibly regardless of the number of classes, removing the linear growth limitation in the standard distillation settings. There is also no constraint on the form of ys, which can be either discrete or continuous. The *storage budget* or *compression rate* is calculated by considering the storage space of parameters in both memories and addressing functions, which should be as compact as possible.

<span id="page-3-0"></span>Image /page/3/Figure/0 description: The image displays a diagram illustrating a method for addressing and retrieving compressed images (memories). On the left, a 'Label' column shows a binary vector with a '1' in the first row and a '1' in the second row, indicating two classes: 'Class 0' and 'Class 1'. Next to it is a 'Hard-coded addressing matrix' with values of 0 and 1. Green arrows connect the '1's in the label vector to specific cells in the addressing matrix, which then point to 'Compressed images (memories)' on the right. These memories are depicted as a stack of small images. On the right side of the diagram, a 'Query (label, etc.)' vector labeled 'y' is shown. This is followed by a series of 'Learnable addressing matrices' arranged in a K-dimensional structure, with each matrix containing numerical values like 0.1, -0.4, 9.1, 5.1, etc. Finally, a stack of 'Shared common bases (memories)' is presented, which are also depicted as small images. A bracket indicates that both the learnable addressing matrices and the shared common bases are considered within the 'storage budget'.

Figure 1: Distilling a large-scale dataset into compressed memories. *Left:* the standard dataset distillation task under the formulation of memory addressing. The addressing matrices are hard-coded with 1s and 0s to fetch the corresponding compressed image in memories. The memory size grows linearly with number of classes. *Right:* learnable addressing matrix with shared common bases (number of bases can be flexibly defined). The information sharing between classes is captured in this representation. The queries can be generalized to any vector representation, besides one-hot labels, i.e. for a general dataset from  $\mathcal X$  to  $\mathcal Y$ , it can be distilled into memories for recall and model re-training.

**Memory representation.** We use a set of bases to store in the memories  $\mathcal{M} = \{\boldsymbol{b}_1, ..., \boldsymbol{b}_K\}$ , where each vector  $\mathbf{b}_k \in \mathbb{R}^d$  has the same dimension as  $\mathbf{x} \in \mathbb{R}^d$ , and all vectors collectively define the intrinsic components in a dataset that characterizes the task mapping from  $X$  to  $Y$ . Through re-using the bases, we can produce a desired synthetic dataset for model re-training. *Spatial redundancies in images:* note that, as a special case, images can contain redundancies spatially and be stored in a downsampled version to improve the compression rate. The downsampled image bases can be passed via a deterministic upsampling process (e.g., bilinear interpolation) to recover the original resolution.

**Memory addressing.** For each query y, we use a parameterized function  $A(y)$  to re-combine the bases in the memories  $M$ . Similar to previous methods on accessing memories, we use  $A$  to produce a set of coefficients, and linearly combine the bases to produce synthetic data. Formally, to retrieve  $r$ synthetic examples for each y, we define a set of matrices  $\{A_1, ..., A_r\}, A_i \in \mathbb{R}^{d_y \times K}$ , where  $d_y$  is the dimension size of  $y$  and  $r$  is the number of data samples that can be retrieved. With the memories  $\mathcal{M} = \{\boldsymbol{b}_1, ..., \boldsymbol{b}_K\}$ , we define:

<span id="page-3-1"></span>
$$
\boldsymbol{x}'_i^T = \boldsymbol{y}^T \boldsymbol{A}_i [\boldsymbol{b}_1; \dots; \boldsymbol{b}_K]^T, \boldsymbol{x}' \in \mathbb{R}^{d \times 1}
$$
 (2)

where  $y\in\mathbb{R}^{d_y\times 1}$  is in a vectorized form, such one-hot encoding of categorical labels, and  $v=y^TA_i$ corresponds to a coefficient vector  $v$  that combines the bases. The produced synthetic data  $x'$  is paired with  $y$  as the corresponding label. Our model is shown on the right of figure [1.](#page-3-0)

**Constructing a dataset.** To construct a synthetic dataset  $\mathcal{D}_s$  for model re-training, we are often given a set of samples  $\{y_i\}$ , or can enumerate all possible values of y (if discrete). With the set  $\{y_i\}$ , we use eqn[.2](#page-3-1) to address and retrieve the synthetic dataset  $\mathcal{D}_s^{\mathbf{y}=\mathbf{y}_i} = \{(\mathbf{x}'_j, \mathbf{y}_i)\}_{j=1}^r$  for each  $\mathbf{y}_i$ . The final dataset is the union of all  $\mathcal{D}_s = \bigcup \mathcal{D}_s^{y=y_i}$ . The dataset  $\mathcal{D}_s$  can be used in either a minibatch form for stochastic gradient descent, or as a whole for batch gradient descent.

Generalized possibilities of queries. Another advantage of our formulation, besides a compact and shared representation, is the various possibilities of queries y. In principle, under this formulation, the label  $y$  can be flexibly defined as other forms, such as language or audio, where the representation resides in a continuous space or follows a distribution  $p(h(\mathbf{y}))$  defined by a feature extractor  $h(\cdot)$ . The set of  $\{y_i\}$  then can be sampled from the distribution p instead of having to enumerate all possible values. This provides a general way of compressing or distilling a large dataset without constraint on the forms of labels.

Connection with standard Dataset Distillation. In the standard setting, dataset distillation is defined for classification tasks with discrete labels and each label owns its unique set of synthetic data. We can show that this is a special case of our formulation: if the bases are constructed as the collection of those label-specific synthetic data  $(K = N', N'$  is the total size of the synthetic dataset), the addressing matrices  $A_i$  are defined in space  $\{0,1\}^{C \times N'}$  where  $A_i[m,n] = 1$  if n equals

<span id="page-4-1"></span>

## Algorithm 1

- 1: **hyperparameters:** Momentum rate  $\beta_0$ ,  $\beta_1$ , learning rate  $\alpha_0$ ,  $\alpha_1$  for  $\theta$  and  $\phi$  respectively.
- 2: **input:** Dataset  $\mathcal{D}_{tr}$ , memories  $\mathcal{M}$ , addressing function  $\mathcal{A}$ , loss function  $\ell(\cdot, \cdot)$
- 3: repeat
- 4: Sample a subset of labels  $\mathcal{Y}'$
- 5: Address the memories M and obtain synthetic dataset  $\mathcal{D}_s^{\mathcal{Y}'}$  with eqn. [2](#page-3-1)
- 6: Randomly initialize model parameters  $\theta_0$
- 7: Initialize momentum  $m_0 = 0$
- 8: for  $t = 1$  to  $T$  do
- 9: Sample a minibatch  $B_s = \{ (x'_i, y_i) \}$  from  $\mathcal{D}_s^{\mathcal{Y}'}$
- 10: Compute  $\mathcal{L} = \frac{1}{|\mathbf{B}_s|} \sum_{i=1}^{|\mathbf{B}_s|} \ell(f_{\theta_{t-1}}(\mathbf{x}'_i), y_i)$
- 11: Update momentum  $m_t = \beta_0 m_{t-1} + \frac{d\mathcal{L}}{d\theta_{t-1}}$
- 12: Update  $\theta_t = \theta_{t-1} \alpha_0 m_t$ <br>13: **end for**
- end for
- 14: Sample a minibatch  $B = \{(\boldsymbol{x}_i, \boldsymbol{y}_i)\}\$ from  $\mathcal{D}_{tr}$  with labels in  $\mathcal{Y}'$
- 15: Compute  $J(\phi) = \frac{1}{|B|} \sum_{i=1}^{|B|} \ell(f_{\theta_T}(x_i), y_i)$
- 16: Update  $\phi \leftarrow \text{OPT-STEP}(\phi, J(\phi), \alpha_1, \beta_1)$
- 17: until Converge

to  $m(N'/C) + i$  (i<sup>th</sup> item of  $m<sup>th</sup>$  class) and  $A<sub>i</sub>[m,n] = 0$  at other positions, then the "retrieval" process can also be defined as eqn. [2.](#page-3-1)

<span id="page-4-0"></span>

### 4.2 Learning framework: back-propagation through time

In this section, we build upon the back-propagation through time algorithm and discuss in detail the learning framework that performs the distillation process from a dataset to memories and addressing functions.

Starting from notations, we define the parameters contained in both memories and addressing functions as  $\phi$ , which are collectively optimized. A loss function is  $\ell(\cdot, \cdot)$  is defined on a task-specific dataset. We denote an optimization algorithm as  $\text{OPT}(\cdot, \cdot; \alpha, \beta, \ell, T)$ , where  $\alpha$  and  $\beta$  are the learning rate and the momentum rate, respectively, and  $T$  is the number of optimization steps. For a single step optimization, we denote it as OPT-STEP $(\cdot, \cdot; \alpha, \beta)$ .

To learn the parameters  $\phi = \{M, \mathcal{A}\}\$ , we follow a standard bi-level optimization framework with back-propagation through time (BPTT), where the inner-loop uses the synthetic dataset  $\mathcal{D}_s$  to train a randomly initialized model starting from scratch, and a generalization loss is computed using a minibatch  $B = \{ (x_i, y_i) \}$  sampled from  $\mathcal{D}_{tr}$ . The parameters  $\phi$  are implicitly contained in the synthetic dataset  $\mathcal{D}_s$  and optimized when minimizing the generalization loss. The bi-level optimization defines:

$$
\min J(\boldsymbol{\phi}) = \frac{1}{|\boldsymbol{B}|} \sum_{i=1}^{|\boldsymbol{B}|} \ell(f(\boldsymbol{x}_i; \boldsymbol{\theta}^*), \boldsymbol{y}_i),
$$

$$
\text{subject to} \quad \boldsymbol{\theta}^* = \text{OPT}(\boldsymbol{\theta}_0, \mathcal{D}_s; \alpha_0, \beta_0, \ell, T) \tag{3}
$$

where  $\theta_0$  represents the initializing parameters,  $\theta^*$  is the optimized model parameters in the inner optimization loop,  $\alpha_0$  and  $\beta_0$  are the learning rate and momentum rate for opt( $\cdot$ , $\cdot$ ), and  $J(\phi)$  is the generalization loss on minibatch  $B$ . In practice, for each inner loop training, we randomly sample a subset  $\mathcal{Y}'$  from ys and retrieve the corresponding subset  $\mathcal{D}_s^{\mathcal{Y}'}$ . This reduces the computation cost in inner loops. We empirically observe that equivalent results can be achieved with faster runtime. The algorithm is summarized in Alg. [1,](#page-4-1) where lines 8-13 define the inner loop optimization process  $OPT(\cdot, \cdot; \alpha, \beta, \ell, T)$ , and the gradients of generalization loss (line 15) is back-propagated through the inner loop to update  $\phi$ . Note that, in principle, the inner loop optimization can be performed using any optimizer. In this paper, we mainly rely on the standard stochastic gradient descent with momentum to train the distilled data.

Critical factors in BPTT. Although being a natural choice in performing dataset distillation and adopted in the original work [\[6\]](#page-10-4), the BPTT framework has been shown to underperform other algorithms, such as single-step gradient matching methods [\[10,](#page-10-7) [11\]](#page-10-9), on various benchmarks. The underlying causes that hinder the performance of the algorithm are still underexplored. In our work, we investigate and identify the factors that can unleash the potential of back-propagation through time framework on dataset distillation and lead to significant performance boosts.

*Momentum term.* In previous dataset distillation works [\[6,](#page-10-4) [16\]](#page-10-13), the usage of back-propagation through time framework omits the momentum term in the inner loop optimization. Indeed, this has been a common practice in meta-learning tasks [\[18,](#page-10-15) [40\]](#page-12-2). Adding momentum terms in meta-learning can potentially even hurt the performance and lead to less gradient diversity [\[40\]](#page-12-2). However, we observe that, in dataset distillation tasks, the momentum term is crucial for making BPTT excel, even in the relatively short inner loop optimization settings (e.g. 10 or 20 steps). We provide results and analysis in the experiments section.

*Long unrolled trajectories.* Another aspect in using BPTT in dataset distillation is the length of unrolled optimization trajectories in the inner loops. The previous usage of BPTT on this task [\[6,](#page-10-4) [16\]](#page-10-13) adopts relatively short inner loop optimization trajectories (e.g. 10-30 steps). Instead, we show that unrolling the trajectories long enough (e.g. 200 steps) with momentum terms can potentially produce  $\theta^*$  that better summarizes the information contained in memories and addressing matrices, generating more effective gradients to learn the compressed representation.

# 5 Experiments

We thoroughly evaluate our model and demonstrate the benefits over previous methods. In section [5.1,](#page-5-0) we show that using a shared representation is critical to improving the distillation performance and compression rates. Specifically, we observe that there is strong evidence that there is information re-using across classes. We further show the benefits of our model on standard continual learning tasks in section [5.2.](#page-7-0) For example, we observe that a simple "compress-then-recall" method can achieve performance outperforming state-of-the-art continual learning models with complex designs. Finally, in section [5.3,](#page-8-0) we show that storing the compressed data enables synthesizing new classifiers (section [5.3.1\)](#page-8-1) and the shared representation formulation allows more general queries (section [5.3.2\)](#page-9-0), which can be continuous (e.g. image features).

<span id="page-5-0"></span>

## 5.1 Dataset Distillation

In this section, we follow the standard setting of dataset distillation, and perform dataset compression that can be recalled with discrete class labels.

Datasets. We test our models on six standard dataset distillation benchmarks: MNIST [\[41\]](#page-12-3), FashionMNIST [\[42\]](#page-12-4), SVHN [\[43\]](#page-12-5), CIFAR10 [\[44\]](#page-12-6), CIFAR100 [\[44\]](#page-12-6), and TinyImageNet [\[45\]](#page-12-7). MNIST contains 10 classes with 60,000 writing digit images as training and 10,000 as test set. The images are gray-scale with a shape of  $28 \times 28$ . FashionMNIST is a dataset with clothing and shoe images and consists of a training with size 60,000 and a test set with size 10,000. Each image is  $28 \times 28$  in gray scale, and has a label from 10 classes. SVHN contains street digit images where each image has shape  $32 \times 32 \times 3$ . The dataset contains 73,257 images for training and 26,032 images for testing. CIFAR10 and CIFAR100 are color image datasets, with 50,000 training images and 10,000 testing images on each. CIFAR10 has 10 classes with 5,000 images per class, and CIFAR100 has 100 classes with 500 images per class. TinyImageNet [\[45\]](#page-12-7) contains 200 categories with images of resolution 64x64. The training and testing sets have 100,000 and 10,000 images respectively.

Experiment settings. We evaluate our distillation models under three different memory budgets for each dataset: 1/10/50 images per class. We focus on high compression rate scenarios and consider the 1 and 10 settings for CIFAR100. Following previous works [\[14,](#page-10-10) [11,](#page-10-9) [12,](#page-10-11) [15\]](#page-10-12), the main network architecture used in experiments is a simple convolutional network (ConvNet) with  $3 \times 3$  filters, InstanceNorm, ReLU and  $2 \times 2$  average pooling. For the evaluation protocol, each model is evaluated on 20 randomly initialized models, trained for 300 epochs on a synthetic dataset, and tested on a held-out testing dataset. We use one GPU per experiment run.

Memory budget calculation. Since our model uses memories and addressing matrices to store the compressed information, we treat the total number of images as a memory storage budget. When

<span id="page-6-2"></span>

|                     | I/C | <b>DC</b> [10] | DSA [11]       | $KIP(NN)$ [12] | CAFE* [46]     | TM [15]        | DM [14]        | Ours           |
|---------------------|-----|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
|                     |     | $91.7 + 0.5$   | $88.7 \pm 0.6$ | $90.1 + 0.1$   | $93.1 \pm 0.3$ |                | $89.7 \pm 0.6$ | $98.7 \pm 0.7$ |
| <b>MNIST</b> [41]   | 10  | $97.4 \pm 0.2$ | $97.8 \pm 0.1$ | $97.5 \pm 0.0$ | $97.5 \pm 0.1$ | ٠              | $97.5 \pm 0.1$ | $99.3 + 0.5$   |
|                     | 50  | $98.8 \pm 0.2$ | $99.2 \pm 0.1$ | $98.3 \pm 0.1$ | $98.9 \pm 0.2$ | ۰              | $98.6 \pm 0.1$ | $99.4 \pm 0.4$ |
|                     |     | $70.5 \pm 0.6$ | $70.6 \pm 0.6$ | $73.5 \pm 0.5$ | $77.1 \pm 0.9$ | -              |                | $88.5 \pm 0.1$ |
| <b>F-MNIST [42]</b> | 10  | $82.3 + 0.4$   | $84.6 \pm 0.3$ | $86.8 + 0.1$   | $83.0 \pm 0.3$ |                | -              | $90.0 + 0.7$   |
|                     | 50  | $83.6 \pm 0.4$ | $88.7 \pm 0.2$ | $88.0 \pm 0.1$ | $88.2 \pm 0.3$ |                | -              | $91.2 \pm 0.3$ |
|                     |     | $31.2 \pm 1.4$ | $27.5 + 1.4$   | $57.3 \pm 0.1$ | $42.9 \pm 3.0$ |                | ۰              | $87.3 \pm 0.1$ |
| <b>SVHN</b> [43]    | 10  | $76.1 \pm 0.6$ | $79.2 + 0.5$   | $75.0 \pm 0.1$ | $77.9 \pm 0.6$ |                | -              | $89.1 \pm 0.2$ |
|                     | 50  | $82.3 \pm 0.3$ | $84.4 \pm 0.4$ | $80.5 \pm 0.1$ | $82.3 \pm 0.4$ | ۰              | -              | $89.5 \pm 0.2$ |
|                     |     | $28.3 + 0.5$   | $28.8 \pm 0.7$ | $49.9 + 0.2$   | $31.6 \pm 0.8$ | $46.3 + 0.8$   | $26.0 \pm 0.8$ | $66.4 \pm 0.4$ |
| <b>CIFAR10 [44]</b> | 10  | $44.9 \pm 0.5$ | $52.1 \pm 0.5$ | $62.7 \pm 0.3$ | $50.9 \pm 0.5$ | $65.3 \pm 0.7$ | $48.9 \pm 0.6$ | $71.2 + 0.4$   |
|                     | 50  | $53.9 \pm 0.5$ | $60.6 \pm 0.5$ | $68.6 \pm 0.2$ | $62.3 \pm 0.4$ | $71.6 \pm 0.2$ | $63.0 \pm 0.4$ | $73.6 \pm 0.5$ |
| CIFAR100 [44]       |     | $12.8 + 0.3$   | $13.9 + 0.3$   | $15.7 + 0.2$   | $14.0 \pm 0.3$ | $24.3 \pm 0.3$ | $11.4 + 0.3$   | $34.0 + 0.4$   |
|                     | 10  | $25.2 \pm 0.3$ | $32.3 + 0.3$   | $28.3 \pm 0.1$ | $31.5 \pm 0.2$ | $40.1 \pm 0.4$ | $29.7 \pm 0.3$ | $42.9 \pm 0.7$ |
| TinyImageNet [45]   |     |                |                |                |                | $8.8 \pm 0.3$  | $3.9 \pm 0.2$  | $16.0 \pm 0.7$ |

Table 1: We compare our method with previous works on ConvNet recovered accuracy. Our algorithm consistently outperforms all previous methods and achieves state-of-the-art. <sup>∗</sup>Note that we selected the best results from baseline model variants. I/C is images per class (storage budget eqn. [4\)](#page-6-0).

comparing with  $N$  images for  $C$  classes, we ensure

$$
size(base) + size(addressing matrices) \approx NCsize (image)
$$
\n(4)

where  $size(\cdot)$  is the total size of a tensor, assuming the numbers are stored as floats. For a given number of bases, we calculate the corresponding maximum number of addressing matrices allowed using eqn. [4](#page-6-0) and use the integer lowerbound as the final value.

Model details. We use bases with spatial resolution downsampled by a factor of 2 in both height and width from the standard image size based on datasets. All models are trained for 50k iterations with SGD optimizer. For the inner loop optimization, we set the momentum rate as 0.9, and use 150 steps for small memory budgets 1 and 10, and 200 steps for budget 50. The number of bases is selected on the held-out validation set (10% of training set), an example is shown in figure [2,](#page-6-1) where different numbers of bases and addressing

<span id="page-6-1"></span><span id="page-6-0"></span>Image /page/6/Figure/6 description: This is a line graph showing the accuracy of two datasets, CIFAR10 and SVHN, based on the number of bases. The x-axis is labeled "#bases (#addr-mat) (1 I/C)" and shows the values 8(307), 16(115), 20(76), 24(51), and 32(19). The y-axis is labeled "Accuracy" and ranges from 60 to 90. The blue line represents CIFAR10, with accuracy values peaking around 67 at 16 and 20 bases, and then decreasing to around 62 at 32 bases. The red line represents SVHN, with accuracy values consistently higher, peaking around 86 at 16 and 20 bases, and then decreasing slightly to around 84 at 32 bases. The figure is titled "Figure 2: Validation set".

matrices (calculated with eqn. [4\)](#page-6-0) have impacts on accuracies; more details in the appendix.

Result 1: state-of-the-art accuracy. We compare our model with previous methods: Dataset Condensation (DC) [\[10\]](#page-10-7), Differentiable Siamese Augmentation (DSA) [\[11\]](#page-10-9), Kernel Inducing Points (KIP) [\[12\]](#page-10-11), Distribution Matching (DM) [\[14\]](#page-10-10), Aligning Features (CAFE) [\[46\]](#page-12-8) and Trajectory Matching (TM) [\[15\]](#page-10-12). The results are summarized in table [1.](#page-6-2) Following previous methods [\[11,](#page-10-9) [12,](#page-10-11) [15\]](#page-10-12), we adopt simple data augmentations and preprocessing: flip and rotation on CIFAR10 and CIFAR100 datasets, and ZCA on SVHN, CIFAR10 and CIFAR100. As shown in table [1,](#page-6-2) our model consistently outperforms previous methods, especially under high compression rate cases where only 1 image is allowed per class. For example, we achieve 87.3% and 66.4% on SVHN and CIFAR10 with 1 image per class, outperforming prior arts by 30% and 16.5% under the same storage budget, respectively, and even beat the performance of previous methods using 10 images per class.

Analysis: information sharing across classes. The core observation in our method is that a common representation can enable information sharing across classes and reduce redundancies. To verify this, we calculate the average coefficients  $\bar{v} = \frac{1}{r} \sum_{i=0}^{r-1} y^T A_i$  for each class y and visualize the cosine similarities of  $\bar{v}$  from two classes. The visualizations are shown in fig. [3.](#page-7-1) Higher cosine similarity scores indicate that two classes are utilizing similar bases components in the memories to produce synthetic images. For example, in CIFAR100 (right one of figure [3\)](#page-7-1), classes maple, oak, palm, pine and willow trees have strong sharing, while lawn mower and rocket are distinct from each other. Similar patterns can be found in CIFAR10 dataset, shown in the left one of figure [3.](#page-7-1)

Result 2: back-propagation through time is a strong baseline. In figure [4](#page-7-1) and table [2,](#page-7-2) we show that a vanilla BPTT variant is already a strong baseline which outperforms previous single-step gradient methods [\[10\]](#page-10-7) by 40.4% on SVHN and 20.3% on SVHN and CIFAR10 under 1 image per class. Note that the performance on SVHN has doubled the accuracy 31.2% obtained using single-step gradient matching methods [\[10\]](#page-10-7). In the vanilla BPTT variant, no downsampling (ds) or

<span id="page-7-1"></span>Image /page/7/Figure/0 description: The image contains three plots. The first two plots are heatmaps showing class similarity. The first heatmap has labels for airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck on both axes. The second heatmap has labels for maple tree, oak tree, palm tree, pine tree, willow tree, bicycle, bus, motorcycle, pickup truck, train, lawn mower, rocket, streetcar, tank, and tractor on both axes. Both heatmaps have a color bar indicating values from -0.6 to 1.0. The third plot is a line graph showing accuracy versus the number of inner optimization steps. It displays four lines representing CIFAR10-BPTT-w/ m (solid blue), CIFAR10-BPTT-w/o m (dashed blue), SVHN-BPTT-w/ m (solid red), and SVHN-BPTT-w/o m (dashed red). The x-axis is labeled 'Number of inner opt steps' with values 1, 10, 20, 50, 100, 150, and 200. The y-axis is labeled 'Accuracy' with values ranging from 30 to 70.

Figure 3: Similarity matrices of learned addressing coefficients for the CIFAR10 dataset (left) and a subset of CIFAR100 classes (right).

Image /page/7/Figure/2 description: This is a line graph showing the accuracy of different models based on the number of inner optimization steps. The x-axis is labeled "Number of inner opt steps" and ranges from 1 to 200, with specific points marked at 1, 10, 20, 50, 100, 150, and 200. The y-axis is labeled "Accuracy" and ranges from approximately 25 to 55. There are four lines plotted: a solid red line, a dashed red line, a solid blue line, and a dashed blue line. Each line has a shaded area around it, indicating a confidence interval. The solid red line shows a rapid increase in accuracy from around 28 at 1 step to over 50 at 10 steps, and then plateaus. The dashed red line starts at around 28, increases to about 50 at 10 steps, and then gradually increases to around 54 at 200 steps. The solid blue line starts at around 28, increases to about 45 at 20 steps, and then plateaus around 47. The dashed blue line starts at around 28, increases to about 38 at 100 steps, and then increases to about 41 at 200 steps.

Figure 4: Analysis on BPTT steps and momentums.

<span id="page-7-2"></span>

|                      | I/C | Single-step GM | Ours <sup>BPTT</sup> | $Ours^{\text{BPTT}+ds}$ | Ours <sup>Full w/o Aug.</sup> | $Ours$ <sup>Full</sup> |
|----------------------|-----|----------------|----------------------|-------------------------|-------------------------------|------------------------|
|                      |     | $28.8 + 0.7$   | $49.1 + 0.6$         | $55.2 + 0.5$            | $64.2 + 0.6$                  | $66.4 + 0.4$           |
| CIFAR <sub>10</sub>  | 10  | $52.1 + 0.5$   | $62.4 + 0.4$         | $65.9 + 0.4$            | $70.9 + 0.4$                  | $71.2 + 0.4$           |
|                      | 50  | $60.6 + 0.5$   | $70.5 + 0.4$         | $71.1 + 0.5$            | $72.1 + 0.5$                  | $73.8 + 0.4$           |
| CIFAR <sub>100</sub> |     | $13.9 + 0.3$   | $21.3 + 0.6$         | $25.9 + 0.4$            | $33.5 + 0.2$                  | $34.0 + 0.4$           |
|                      | 10  | $32.3 + 0.3$   | $34.7 + 0.5$         | $36.5 + 0.4$            | $40.6 + 0.3$                  | $42.9 + 0.7$           |

Table 2: Ablation studies of every component and comparison with single-step gradient matching [\[10\]](#page-10-7). ds: downsampling. Aug.: data augmentation.

memory addressing formulation is used. We also analyze the effects of *long unrolls* and *momentum terms* on vanilla BPTT in figure [4.](#page-7-1) It is observed that on both short inner loops (10 steps) and long ones (100 steps), adding momentum terms can consistently lead to a strong performance boost, e.g. 7.0% and 9.2% on CIFAR10. Using longer inner loop trajectories can also increase the recovered accuracy, e.g. 18.2% and 42.3% on CIFAR10 and SVHN, respectively, compared to 1 step cases.

Ablation studies. To further analyze the effects of different components in our algorithm, we perform ablation studies on CIFAR10 and CIFAR100. Besides the vanilla BPTT, the ablation results of components (downsampling, augmentation and memory addressing formulation) are summarized in table [2.](#page-7-2) We show that downsampling can indeed reduce spatial redundancies (e.g. improve results from 49.1% to 55.2% on CIFAR10 with 1 image per class), and memory addressing formulation can further increase the recovered accuracy (from 55.2% to 64.2% on CIFAR10 with 1 image per class). It is also shown that our model is quite robust to the ablation of data augmentation, which has a small effect (1-2%) on the results. The resulting algorithm is a *simple and effective* framework that uses memory addressing formulation and BPTT with long unrolls to distill datasets.

Cross-architecture generalization. Our memories and addressing matrices are also generalizable across various architectures. We test our algorithm on ConvNet, ResNet12 and AlexNet for training and testing. The results are summarized in the appendix, section [B.1,](#page-14-0) table [6.](#page-14-1)

<span id="page-7-0"></span>

### 5.2 Continual learning

One of the key usages of memories is to prevent forgetting when a model continually learns through tasks. In this section, we evaluate our algorithm on the standard continual learning benchmarks and show that, due to the strong performance, a simple "compress-then-recall" method with our model can already rival with previous state-of-the-arts with complex designs.

Efficient lifelong learning. Following [\[47\]](#page-12-9), we work with the problem where all tasks are streamed in mini-batches and learned in a *single pass*. A learner is allowed to be equipped with a small memory buffer. The data samples after seen will not be available unless stored in the buffer. We use a mini-batch size of 10 to stream the data, following previous works [\[28,](#page-11-6) [24\]](#page-11-4).

Evaluation. The learner's performance after learning on the task stream is commonly evaluated under two metrics: retained accuracy (RA) and backward-transfer and interference (BTI). RA is the average accuracy of the final trained model on all tasks, and BTI measures the performance difference between after it was learned and after the full training process. Note that our algorithm does not perform actual learner training on the data streams and BTI is not applicable.

<span id="page-8-2"></span>

|                   |                    | <b>Rotations</b> |                    | <b>Permutations</b> |                    | <b>MANY</b>      |                    | <b>CIFAR-100</b> |
|-------------------|--------------------|------------------|--------------------|---------------------|--------------------|------------------|--------------------|------------------|
|                   | RA $\uparrow$      | BTI $\downarrow$ | RA $\uparrow$      | BTI $\downarrow$    | RA $\uparrow$      | BTI $\downarrow$ | RA $\uparrow$      | BTI $\downarrow$ |
| <b>ONLINE</b>     | $53.38^{\pm 1.53}$ | $-5.44$          | $55.42^{\pm 0.65}$ | $-13.76$            | $32.62^{\pm 0.43}$ | $-19.06$         | $32.62^{\pm 0.43}$ | $-19.06$         |
| <b>EWC</b> [48]   | $57.96^{\pm 1.33}$ | $-20.42$         | $62.32^{\pm 1.34}$ | $-13.32$            | $33.10^{\pm0.14}$  | $-18.50$         | -                  | -                |
| <b>GEM [24]</b>   | $67.38^{\pm 1.75}$ | $-18.02$         | $55.42^{\pm 1.10}$ | $-24.42$            | $39.50^{\pm0.62}$  | $-17.50$         | $48.27^{\pm 1.10}$ | $-13.7$          |
| <b>MER [17]</b>   | $77.42^{\pm0.78}$  | $-5.60$          | $73.46^{\pm 0.45}$ | $-9.96$             | $51.00^{\pm0.54}$  | $-13.57$         | $51.38^{\pm 1.05}$ | $-12.83$         |
| <b>La-M [28]</b>  | $77.42^{\pm0.65}$  | $-8.64$          | $74.34^{\pm0.67}$  | $-7.60$             | $50.43^{\pm 0.21}$ | $-10.00$         | $61.18^{\pm 1.44}$ | $-9.00$          |
| <b>sp-La [30]</b> | $77.77^{\pm 0.58}$ | $-8.16$          | $76.88^{\pm0.72}$  | $-8.39$             | $50.81^{\pm0.79}$  | $-13.73$         | -                  | -                |
| <b>Ours</b>       | $80.32^{\pm0.28}$  | N/A              | $78.48^{\pm0.76}$  | N/A                 | $74.07^{\pm0.51}$  | N/A              | $62.58^{\pm 1.1}$  | N/A              |

Table 3: We show that "compress-then-recall" is a strong baseline that outperforms previous methods on four continual learning benchmarks. Baseline numbers are from [\[28\]](#page-11-6) or obtained from public official repos.

Benchmarks. We evaluate our method on three tasks widely used in previous Continual Learning works. MNIST Rotations [\[24\]](#page-11-4) contains 20 tasks with 1,000 samples in each. Every task consists of images rotated by a fixed angle from 0 to 180 degrees. MNIST Permutations [\[48\]](#page-12-10) has 20 tasks, and each task contains 1,000 images generated through shuffling the image pixels by a fixed permutation. MANY Permutations [\[17\]](#page-10-14) is a longer variant with 100 tasks in total and 200 samples in each. Incremental CIFAR-100 [\[29,](#page-11-11) [24\]](#page-11-4) splits the CIFAR100 dataset into 20 5-way classification tasks as the task stream for learning.

Our model. Based on our distillation method, we adopt a simple framework to perform continual learning: "compress then recall". During the training phase, we do not perform learning on neural networks, instead, the dataset of each task is distilled to memories and the paired addressing matrices. During test phase, we simply fetch the corresponding memories and addressing matrices for each task, and train a new model from scratch to perform classification. *Memory buffer designs.* When a new task starts, we use the full remaining memory buffer to store the samples and perform distillation with both buffer samples and streamed samples. After a task ends, the distilled memories and addressing matrices are stored in the buffer, taking  $1/T$  of the space, where T is the total number of tasks. Namely, the buffer size keeps shrinking when more compressed representation of tasks is stored. Note that we compare our model with previous methods under the *exact same memory sizes for fair comparisons*. See the appendix for more details on model and memory designs.

Results. We show that this simple method is already a strong baseline that outperforms prior arts on four benchmarks, summarized in table [3.](#page-8-2) Our method is compared with: Online, EWC [\[48\]](#page-12-10), GEM [\[24\]](#page-11-4), MER [\[17\]](#page-10-14), C-MAML [\[28\]](#page-11-6), La-MAML [\[28\]](#page-11-6) and Sparse-LaMAML [\[30\]](#page-11-10). For example, we can obtain a 23% boost on MNIST MANY benchmark: from 50.81% to 74.07%.

We further compare our model with previous works Kernel Continual Learning [\[39\]](#page-12-1) and Stable SGD [\[37\]](#page-11-9) following their settings, where each task in MNIST Rotation and MNIST Permutation contains 60,000 samples instead of 1,000 samples. Our model achieves  $87.3^{\pm 0.92}$  and  $88.3^{\pm 0.58}$  on Permutated MNIST and Rotated MNIST under their setting, outperforming both KCL (85.5 $\pm$ 0.78 and  $81.8^{\pm0.60}$ ) and Stable SGD (80.1<sup> $\pm0.51$ </sup> and 70.8<sup> $\pm0.78$ </sup>). Interestingly, our results also are higher than the multitask upperbound  $(86.5^{\pm 0.21}$  and  $87.3^{\pm 0.47})$ , potentially due to that there is task interference in joint training, which can be naturally avoided in our method.

<span id="page-8-0"></span>

### 5.3 Synthesizing new classifiers after learning

If we want to memorize the past, what is the benefit of storing the compressed representation rather than a trained model? In this section, we show that our compressed representation can enable flexible synthesis of new classifiers after the learning. Specifically, we demonstrate extrapolating between tasks to train new models, and performing memory recall with images instead of labels, showing the generalizability of our framework on other query forms.

<span id="page-8-1"></span>

#### 5.3.1 Extrapolating between tasks

In the real world, tasks often do not come together and a learner, therefore, cannot observe all tasks at once. In current machine learning paradigms, when models are separately trained for disjoint tasks, it has difficulty extrapolating between tasks to build new classifiers. This is different from human learning. We show that storing our compressed representation enables a learner to extrapolate and synthesize new classifiers after learning separately on each task. Specifically, we separate CIFAR100

into 20 disjoint 5-way classification tasks as training tasks. For testing, we select classes that are not seen together during training by randomly choosing k tasks and picking 1 class from each selected task. We use  $k = 2$  and  $k = 5$  to construct 2-way and 5-way classification tasks, and sample 1,000 tasks each for evaluation. To train our models, we independently distill the datasets for 20 training tasks into corresponding memories. For each testing task, the class labels are used as queries to recall the synthetic data from the corresponding memories. The recalled data for each label, although not seen together during training, are used for re-training a k-way classifier from scratch. We find that the compressed data can indeed train classifiers on new combinations, for example, we can achieve 72.53% $\pm$ 8.74 on 2-way classification, and 46.54% $\pm$ 6.42 on 5-way classification, with 1 image per class storage budget. The upperbound with the full real dataset is  $92.23\% \pm 4.76$  and  $82.72\% \pm 4.29$ .

<span id="page-9-0"></span>

### 5.3.2 Dataset Distillation extension – recall the past with images

We extend the standard setting to recall the past with images: when the label information and task scopes are missing, but a few visual observations can be made, we would like to build classifiers based on the visual data. For example, when we see a bear image and a deer image, but cannot recall the exact word or category, can we recall the memories with images and build a classifier? This is possible with our problem formulation, where the forms of queries are not constrained to labels and we can *distill a dataset to memories addressable by images*.

We formulate the problem as follows. Formally, after observing a training dataset  $\mathcal{D}_{tr}$  with  $\mathcal{Y} = \{0, ..., C\text{-}1\}$ , we would like to flexibly build classifiers for a subtask  $\mathcal{Y}_g \subset \mathcal{Y}$  based on visual observations  $\mathcal{X}_q$  from  $\mathcal{Y}_q$ , when the actual information of  $\mathcal{Y}_q$  is unknown. We work with 1-shot and 5-shot observation cases. As a baseline, we build a nearest neighbor classifier, which is pretrained on  $\mathcal{D}_{tr}$  and takes features of few-shot data

<span id="page-9-1"></span>

| Methods              | 1 shot       | 5 shot       |
|----------------------|--------------|--------------|
| Nearest neighbor     | 48.55        | 61.72        |
| classify-then-recall | 50.58        | 58.46        |
| image addressing     | <b>55.74</b> | <b>71.20</b> |

Table 4: Few-shot perf. recovery.

to classify test images. As a model variant, we could also "classify-then-recall", using a classifier trained on  $\mathcal{D}_{tr}$  to map the image shots into labels and turning into the standard setup. Benefiting from the general design of our formulation, we show that a model can directly perform "image addressing", where a feature network can provide query vectors  $y$  in fig[.1.](#page-3-0) The feature network, memories and addressing matrices can be jointly trained on  $\mathcal{D}_{tr}$ . We evaluate the above models and baselines on CIFAR100 and summarize the results in table [4.](#page-9-1) As shown in the table, our model is not only able to successfully perform the *continuous query addressing* with image feature vectors, but also outperforms two strong baselines on constructing new classifiers. More analysis is in the appendix.

## 6 Conclusion and limitations

In this paper, we propose a framework that distills a large dataset into compact addressable memories. This framework introduces several benefits, including removing the linear growth contraints on the compressed data size, allowing more general queries besides categorical labels, and most importantly, achieving high compression rate with strong re-training performance, outperforming previous stateof-the-arts in dataset distillation. We also demonstrate a "compress-then-recall" method using our framework, leading to new state-of-the-arts in continual learning on four datasets. Our full model has potential limitations on the costly inner optimization loop, which might be time-consuming on larger models or datasets. This limitation might be solved by combining the memory formulation with a different learning framework. One potential societal concern with dataset distillation in general is that the distilled dataset may not contain the full diversity of the original data distribution, causing the retrained classifier to perform especially poorly on minority populations; our method arguably takes a step towards mitigating that concern through improving the retrained accuracy.

# 7 Acknowledgements

This material is based upon work supported by the National Science Foundation under Grants No. 2107048 and 2112562. Any opinions, findings, and conclusions or recommendations expressed in this material are those of the author(s) and do not necessarily reflect the views of the National Science Foundation. We would also like to thank Vishvak Murahari, Sunny Cui, Ruth Fong, Vikram Ramaswamy, and Zeyu Wang for discussions.

## References

- <span id="page-10-0"></span>[1] Timothy F Brady, Talia Konkle, and George A Alvarez. Compression in visual working memory: using statistical regularities to form more efficient memory representations. *Journal of Experimental Psychology: General*, 138(4):487, 2009.
- [2] Geoffrey R Loftus and Elizabeth F Loftus. *Human memory: The processing of information*. Psychology Press, 2019.
- <span id="page-10-1"></span>[3] John R Anderson and Gordon H Bower. *Human associative memory*. Psychology press, 2014.
- <span id="page-10-2"></span>[4] Ian J Goodfellow, Mehdi Mirza, Da Xiao, Aaron Courville, and Yoshua Bengio. An empirical investigation of catastrophic forgetting in gradient-based neural networks. *arXiv preprint arXiv:1312.6211*, 2013.
- <span id="page-10-3"></span>[5] Michael McCloskey and Neal J Cohen. Catastrophic interference in connectionist networks: The sequential learning problem. In *Psychology of learning and motivation*, volume 24, pages 109–165. Elsevier, 1989.
- <span id="page-10-4"></span>[6] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-10-5"></span>[7] Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. *Advances in neural information processing systems*, 27, 2014.
- [8] Ian Gemp, Brian McWilliams, Claire Vernade, and Thore Graepel. Eigengame: Pca as a nash equilibrium. In *International Conference on Learning Representations*, 2020.
- <span id="page-10-6"></span>[9] Diederik P Kingma and Max Welling. Auto-encoding variational bayes. *arXiv preprint arXiv:1312.6114*, 2013.
- <span id="page-10-7"></span>[10] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021.
- <span id="page-10-9"></span>[11] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, 2021.
- <span id="page-10-11"></span>[12] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34, 2021.
- <span id="page-10-8"></span>[13] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *International Conference on Learning Representations*, 2021.
- <span id="page-10-10"></span>[14] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021.
- <span id="page-10-12"></span>[15] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022.
- <span id="page-10-13"></span>[16] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. *arXiv preprint arXiv:1910.02551*, 2019.
- <span id="page-10-14"></span>[17] Matthew Riemer, Ignacio Cases, Robert Ajemian, Miao Liu, Irina Rish, Yuhai Tu, and Gerald Tesauro. Learning to learn without forgetting by maximizing transfer and minimizing interference. *arXiv preprint arXiv:1810.11910*, 2018.
- <span id="page-10-15"></span>[18] Chelsea Finn, Pieter Abbeel, and Sergey Levine. Model-agnostic meta-learning for fast adaptation of deep networks. In *International Conference on Machine Learning*, pages 1126–1135. PMLR, 2017.
- [19] Luca Franceschi, Michele Donini, Paolo Frasconi, and Massimiliano Pontil. Forward and reverse gradient-based hyperparameter optimization. In *International Conference on Machine Learning*, pages 1165–1173. PMLR, 2017.

- <span id="page-11-0"></span>[20] Aniruddh Raghu, Maithra Raghu, Simon Kornblith, David Duvenaud, and Geoffrey Hinton. Teaching with commentaries. In *International Conference on Learning Representations*, 2020.
- <span id="page-11-1"></span>[21] Sid Reddy, Anca Dragan, and Sergey Levine. Pragmatic image compression for human-in-theloop decision-making. *Advances in Neural Information Processing Systems*, 34, 2021.
- <span id="page-11-2"></span>[22] Shengjia Zhao, Abhishek Sinha, Yutong He, Aidan Perreault, Jiaming Song, and Stefano Ermon. Comparing distributions by measuring differences that affect decision making. In *International Conference on Learning Representations*, 2021.
- <span id="page-11-3"></span>[23] Roger Ratcliff. Connectionist models of recognition memory: constraints imposed by learning and forgetting functions. *Psychological review*, 97(2):285, 1990.
- <span id="page-11-4"></span>[24] David Lopez-Paz and Marc'Aurelio Ranzato. Gradient episodic memory for continual learning. *Advances in neural information processing systems*, 30:6467–6476, 2017.
- [25] Arslan Chaudhry, Marcus Rohrbach, Mohamed Elhoseiny, Thalaiyasingam Ajanthan, Puneet K Dokania, Philip HS Torr, and Marc'Aurelio Ranzato. On tiny episodic memories in continual learning. *arXiv preprint arXiv:1902.10486*, 2019.
- [26] Tom Veniat, Ludovic Denoyer, and Marc'Aurelio Ranzato. Efficient continual learning with modular networks and task-driven priors. *arXiv preprint arXiv:2012.12631*, 2020.
- <span id="page-11-5"></span>[27] Cuong V Nguyen, Yingzhen Li, Thang D Bui, and Richard E Turner. Variational continual learning. *arXiv preprint arXiv:1710.10628*, 2017.
- <span id="page-11-6"></span>[28] Gunshi Gupta, Karmesh Yadav, and Liam Paull. La-maml: Look-ahead meta learning for continual learning. *arXiv preprint arXiv:2007.13904*, 2020.
- <span id="page-11-11"></span>[29] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H Lampert. icarl: Incremental classifier and representation learning. In *Proceedings of the IEEE conference on Computer Vision and Pattern Recognition*, pages 2001–2010, 2017.
- <span id="page-11-10"></span>[30] Johannes Von Oswald, Dominic Zhao, Seijin Kobayashi, Simon Schug, Massimo Caccia, Nicolas Zucchet, and João Sacramento. Learning where to learn: Gradient sparsity in meta and continual learning. *Advances in Neural Information Processing Systems*, 34, 2021.
- [31] Ameya Prabhu, Philip HS Torr, and Puneet K Dokania. Gdumb: A simple approach that questions our progress in continual learning. In *European conference on computer vision*, pages 524–540. Springer, 2020.
- [32] Matthias De Lange, Rahaf Aljundi, Marc Masana, Sarah Parisot, Xu Jia, Ales Leonardis, Gregory Slabaugh, and Tinne Tuytelaars. Continual learning: A comparative study on how to defy forgetting in classification tasks. *arXiv preprint arXiv:1909.08383*, 2(6), 2019.
- [33] Raia Hadsell, Dushyant Rao, Andrei A Rusu, and Razvan Pascanu. Embracing change: Continual learning in deep neural networks. *Trends in cognitive sciences*, 24(12):1028–1040, 2020.
- [34] Andrei A Rusu, Neil C Rabinowitz, Guillaume Desjardins, Hubert Soyer, James Kirkpatrick, Koray Kavukcuoglu, Razvan Pascanu, and Raia Hadsell. Progressive neural networks. *arXiv preprint arXiv:1606.04671*, 2016.
- <span id="page-11-7"></span>[35] Jaehong Yoon, Eunho Yang, Jeongtae Lee, and Sung Ju Hwang. Lifelong learning with dynamically expandable networks. In *International Conference on Learning Representations*, 2018.
- <span id="page-11-8"></span>[36] Martial Mermillod, Aurélia Bugaiska, and Patrick Bonin. The stability-plasticity dilemma: Investigating the continuum from catastrophic forgetting to age-limited learning effects. *Frontiers in psychology*, 4:504, 2013.
- <span id="page-11-9"></span>[37] Seyed Iman Mirzadeh, Mehrdad Farajtabar, Razvan Pascanu, and Hassan Ghasemzadeh. Understanding the role of training regimes in continual learning. *Advances in Neural Information Processing Systems*, 33:7308–7320, 2020.

- <span id="page-12-0"></span>[38] Gobinda Saha, Isha Garg, and Kaushik Roy. Gradient projection memory for continual learning. In *International Conference on Learning Representations*, 2020.
- <span id="page-12-1"></span>[39] Mohammad Mahdi Derakhshani, Xiantong Zhen, Ling Shao, and Cees Snoek. Kernel continual learning. In *International Conference on Machine Learning*, pages 2621–2631. PMLR, 2021.
- <span id="page-12-2"></span>[40] Alex Nichol and John Schulman. Reptile: a scalable metalearning algorithm. *arXiv preprint arXiv:1803.02999*, 2(3):4, 2018.
- <span id="page-12-3"></span>[41] Li Deng. The mnist database of handwritten digit images for machine learning research. *IEEE Signal Processing Magazine*, 29(6):141–142, 2012.
- <span id="page-12-4"></span>[42] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-12-5"></span>[43] Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Bo Wu, and Andrew Y Ng. Reading digits in natural images with unsupervised feature learning. 2011.
- <span id="page-12-6"></span>[44] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-12-7"></span>[45] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-12-8"></span>[46] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe learning to condense dataset by aligning features. In *IEEE/CVF Conference on Computer Vision and Pattern Recognition 2022*, 2022.
- <span id="page-12-9"></span>[47] Arslan Chaudhry, Marc'Aurelio Ranzato, Marcus Rohrbach, and Mohamed Elhoseiny. Efficient lifelong learning with a-gem. *arXiv preprint arXiv:1812.00420*, 2018.
- <span id="page-12-10"></span>[48] James Kirkpatrick, Razvan Pascanu, Neil Rabinowitz, Joel Veness, Guillaume Desjardins, Andrei A Rusu, Kieran Milan, John Quan, Tiago Ramalho, Agnieszka Grabska-Barwinska, et al. Overcoming catastrophic forgetting in neural networks. *Proceedings of the national academy of sciences*, 114(13):3521–3526, 2017.

# A Experiment setups

In this section, we provide detailed experimental setups for all the tasks discussed in the main paper. Specifically, we will explain the datasets, architectures and implementation details for all tasks.

<span id="page-13-0"></span>

## A.1 Dataset Distillation

Datasets. Our models are tested on six standard dataset distillation benchmarks:

- MNIST contains 10 classes with 60,000 writing digit images as the training set and 10,000 images as the test set. The images are gray-scale with a shape of  $28 \times 28$  and associated with a label from 10 classes (digit 0-9).
- FashionMNIST is a dataset with clothing and shoe images and consists of a training set with size 60,000 and a test set with size 10,000. Each image is  $28 \times 28$  in gray scale, and has a label from 10 classes.
- SVHN street digit images where each image has a shape of  $32 \times 32 \times 3$ . The dataset contains 73257 images for training and 26032 images for testing. We use the cropped SVHN where the center of the image indicates the number and the rest is background. Each image is categorized into 10 classes (digits 0-9).
- CIFAR10 is a dataset consisting of  $32 \times 32$  RGB images and has 10 classes in total: airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck. Each class contains 5,000 images for training and 1,000 images for testing, leading to 50,000 images for training and 10,000 images for testing in total.
- CIFAR100 contains 60,000 images in total from 100 classes. For every class, 500 images are used for training and 100 images are used in testing. The 100 classes are associated with 20 superclasses, where each superclass contains 5 classes at a finer level.
- TinyImageNet is a downscaled subset of ImageNet, with 200 classes. The dataset contains images of shape 64x64, a training set with 100,000 images and a testing set with 10,000 images.

Architectures. We mainly work with a three-layer convolutional neural network, denoted as "ConvNet", which contains convolutional layers with  $3 \times 3$  filters, followed by ReLU activation function and InstanceNorm. The network has 128 hidden dimensions and uses an average pooling layer with  $2 \times 2$  kernel size after every Instancenorm operation. We also test our models on ResNet-12 with 64, 128, 256, 512 hidden dimensions in each block. The ResNet-12 architecture is slightly modified by replacing BatchNorm with InstanceNorm, and removing the final average pooling layer. We find using the full spatial information in the final layer is important for distillation. Both ConvNet and ResNet-12 are standard architectures for few-shot learning benchmarks.

Implementation details. We use one 24-GB GPU for each experiment run. For all our models, we use a SGD optimizer with learning rate 0.1 and momentum rate 0.5. Every model is trained for 50,000 iterations. For both the inner loop optimization and evaluation, we use learning rate 0.01 and momentum rate 0.9. For random initialization of addressing matrices and bases, we use Kaiming uniform initialization. To select the number of bases for each setting, we randomly sample 10% of training set as the validation set. Data augmentations with rotation and flip are applied on CIFAR10 and CIFAR100 datasets. ZCA preprocessing is used on CIFAR10, CIFAR100 and SVHN datasets. No ZCA preprocessing or data augmentations are used on MNIST and FashionMNIST datasets.

# A.2 Continual learning

Datasets. We use six datasets to evaluate our models. The details are summarized in table [5.](#page-14-2)

Architectures and implementation details. For all MNIST-based datasets, we use a multi-layer perceptron (MLP) with 256 hidden units. Following La-MAML, we use the ConvNet architecture with 160 hidden dimensions. All experiments are run on a 24-GB GPU, using a SGD optimizer with 0.1 learning rate and 0.5 momentum rate. The inner loop optimization learning rate is set as 0.01 with momentum rate 0.9. During the testing phase, the re-training phase uses the same setups as the inner loop optimization. We use data samples stored in the memory buffer for minibatch replay to perform compressing, summarized in table [5.](#page-14-2)

<span id="page-14-2"></span>

| Dataset       | #Tasks | Batch size | #Samples/task | Total mem size | #Bases | #Replay |
|---------------|--------|------------|---------------|----------------|--------|---------|
| Rotations     | 20     | 10         | 1000          | 200            | 24(ds) | 4       |
| Permutations  | 20     | 10         | 1000          | 200            | 8      | 20      |
| MANY          | 100    | 10         | 1000          | 1000           | 8      | 20      |
| CIFAR100      | 20     | 10         | 2250          | 200            | 24(ds) | 4       |
| Rotations*    | 20     | 10         | 60000         | 200            | 24(ds) | 2       |
| Permutations* | 20     | 10         | 60000         | 200            | 8      | 2       |

Table 5: The details on six benchmarks used in the experiments: MNIST Rotations (Rotations), MNIST Permutations (Permutations), MANY Permutations (MANY), Incremental CIFAR100 (CI-FAR100), MNIST Rotations with 60,000 data samples (Rotations<sup>∗</sup> ), MNIST Permutations with 60,000 data samples (Permutations<sup>∗</sup> ). Note that works compare under different benchmarks, we follow the settings and compare our model with La-MAML on Rotations, Permutations, MANY, and CIFAR100, and compare with Kernel Continual Learning on Rotations<sup>∗</sup> and Permutations<sup>∗</sup> . #Samples per task is specified for training. (ds) indicates using downsampled bases.

### A.3 New classifier synthesis

Datasets and setups. For experiments on both extrapolating between tasks and recall with images, we use CIFAR100 as the dataset. *For task extrapolation experiments*, we split CIFAR100 into 20 5-way classification tasks for training, and use 2-way and 5-way classification for testing. The 2-way and 5-way tasks during testing are obtained through randomly selecting 2 or 5 training tasks and then randomly sampling 1 class from each selected task. This ensures that every pairs of classes in a testing task have not been used together for training. *For recall with images experiments*, we use all classes in CIFAR100 for training, and use 20 5-way classification tasks in testing. During evaluation on a 5-way classification task, we sample 1 or 5 images per class (depends on 1-shot or 5-shot), and use the sampled images for recall. The sampled images are from test set, i.e. we would like to use testing images to perform recall and build a new classifier.

Architectures and implementation details. In the task extrapolation experiments, since our models are performing dataset distillation, we use the exact same hyperparameters and architectures as dataset distillation tasks in Sec. [A.1.](#page-13-0) For recall with images, we use 64 bases and 16 addressing matrices (i.e. each query can generate 16 synthetic images) in our model. For baselines, we pretrain the feature backbone for nearest neighbor classifier and the classifier in "classify-then-recall" for 100 epochs on CIFAR100, using SGD optimizer with 0.01 learning rate and 0.9 momentum rate. The visual observations (image shots) we used are from the test set.

# B Additional results and discussion

<span id="page-14-0"></span>

## B.1 Dataset Distillation

Back-propagation through time as a strong baseline. Besides the main ablation study results on CIFAR10 and CIFAR100, table [7](#page-15-0) provides the results for the benchmarks. As shown in the table, back-propagation through time is indeed a strong baseline that consistently outperforms the singlestep gradient matching method, and downsampling can reduce spatial redundancies and improve the compression rate, leading to a higher recovery performance.

<span id="page-14-1"></span>

|          | 1 image/class  |                |                | 10 images/class |                |                |
|----------|----------------|----------------|----------------|-----------------|----------------|----------------|
|          | AlexNet        | ResNet-12      | ConvNet        | AlexNet         | ResNet-12      | ConvNet        |
| AlexNet  | $58.5 \pm 0.5$ | $53.6 \pm 0.6$ | $57.3 \pm 0.6$ | $65.6 \pm 0.5$  | $60.2 \pm 0.6$ | $63.7 \pm 0.6$ |
| ResNet12 | $53.2 \pm 0.8$ | $58.5 \pm 0.5$ | $57.0 \pm 0.3$ | $62.3 \pm 0.9$  | $67.8 \pm 0.3$ | $65.2 \pm 0.6$ |
| ConvNet  | $50.5 \pm 1.3$ | $55.9 \pm 0.6$ | $66.4 \pm 0.4$ | $63.8 \pm 0.8$  | $67.5 \pm 0.4$ | $71.2 \pm 0.4$ |

Table 6: Cross architecture generalization under various pixel/image storage budgets.

<span id="page-15-0"></span>

|                | $_{\rm I/C}$ | Single-step GM | $\mathrm{Ours}^\mathrm{BPTT}$ | $\mathrm{Ours}^{\mathrm{BPTT}+\mathrm{ds}}$ | Ours Full w/o Aug. | $\mathrm{Ours}^\mathrm{Full}$ |
|----------------|--------------|----------------|-------------------------------|---------------------------------------------|--------------------|-------------------------------|
|                |              | $91.7 + 0.5$   | $95.2 + 0.3$                  | $98.2 + 0.1$                                |                    | $98.7 \pm 0.7$                |
| <b>MNIST</b>   | 10           | $97.4 + 0.2$   | $98.8 + 0.1$                  | $98.9 + 0.1$                                |                    | $99.3 + 0.5$                  |
|                | 50           | $98.8 + 0.2$   | $99.2 + 0.1$                  | $99.4 \pm 0.1$                              |                    | $99.4 \pm 0.4$                |
|                |              | $70.5 + 0.6$   | $83.9 + 0.4$                  | $86.7 + 0.3$                                |                    | $88.5 + 0.1$                  |
| <b>F-MNIST</b> | 10           | $82.3 + 0.4$   | $89.1 + 0.2$                  | $89.1 \pm 0.1$                              |                    | $90.0 + 0.7$                  |
|                | 50           | $83.6 + 0.4$   | $90.4 + 0.1$                  | $90.7 + 0.1$                                |                    | $91.2 + 0.3$                  |
|                |              | $31.2 + 1.4$   | $71.6 + 0.8$                  | $80.1 + 0.5$                                |                    | $87.3 + 0.1$                  |
| <b>SVHN</b>    | 10           | $76.1 + 0.6$   | $83.1 + 0.3$                  | $86.2 + 0.2$                                |                    | $89.1 + 0.2$                  |
|                | 50           | $82.3 \pm 0.3$ | $86.5 + 0.2$                  | $88.8 \pm 0.2$                              |                    | $89.5 + 0.2$                  |
|                |              | $28.3 + 0.5$   | $49.1 + 0.6$                  | $55.2 + 0.5$                                | $64.2 + 0.6$       | $66.4 + 0.4$                  |
| CIFAR10        | 10           | $44.9 + 0.5$   | $62.4 + 0.4$                  | $65.9 + 0.4$                                | $70.9 + 0.4$       | $71.2 + 0.4$                  |
|                | 50           | $53.9 \pm 0.5$ | $70.5 + 0.4$                  | $71.1 \pm 0.5$                              | $72.1 \pm 0.5$     | $73.6 \pm 0.5$                |
| CIFAR100       |              | $12.8 + 0.3$   | $21.3 + 0.6$                  | $25.9 + 0.4$                                | $33.5 + 0.2$       | $34.0 + 0.4$                  |
|                | 10           | $25.2 + 0.3$   | $34.7 \pm 0.5$                | $36.5 \pm 0.4$                              | $40.6 \pm 0.3$     | $42.9 \pm 0.7$                |

Table 7: Full ablation studies on model variants and comparison with single-step gradient matching baseline. No augmentations are used on MNIST, FashionMNIST and SVHN.

Transfer across architectures. To show that our compressed memories are generalizable across architectures, we also test the training on ResNet-12. Specifically, we learn the memories and addressing matrices on ConvNet and ResNet-12, and test them on ResNet-12 and ConvNet, respectively. Results are summarized in table [6.](#page-14-1) We use 10 images per class as the storage budget on CIFAR10. Each row is the architecture that our method trains on, and each column is the generalization performance. The learned compressed representation is quite robust across ConvNet and ResNet-12.

Choice of # bases. To select the number of bases for each experiment, we evaluate the performance on a separate validation set, which is 10% random samples of the training set. The results on the validation set are shown in fig. [5.](#page-15-1) We select the number of bases that leads to the highest performance on the validation set for the full training set distillation.

<span id="page-15-1"></span>Image /page/15/Figure/4 description: The image displays a grid of eight line graphs, arranged in two rows and four columns. Each graph plots accuracy on the y-axis against the number of bases on the x-axis. The top row graphs compare the accuracy of CIFAR10 and SVHN datasets across different numbers of bases (1 I/C, 10 I/C, 50 I/C, and 1 I/C respectively). The bottom row graphs compare the accuracy of MNIST and F-MNIST datasets across similar ranges of bases (1 I/C, 10 I/C, 50 I/C, and 10 I/C respectively). The first graph in the top row shows CIFAR10 accuracy ranging from approximately 60 to 85, and SVHN accuracy ranging from approximately 80 to 85, with the x-axis labeled from 8 to 32. The second graph in the top row shows CIFAR10 accuracy around 70-72 and SVHN accuracy around 85-90, with the x-axis labeled from 32 to 256. The third graph in the top row shows CIFAR10 accuracy around 70-73 and SVHN accuracy around 88-90, with the x-axis labeled from 128 to 768. The fourth graph in the top row shows CIFAR100 accuracy decreasing from approximately 35 to 25 as the number of bases increases from 16 to 256. The first graph in the bottom row shows MNIST accuracy around 95-97 and F-MNIST accuracy around 90-92, with the x-axis labeled from 8 to 32. The second graph in the bottom row shows MNIST accuracy around 97-98 and F-MNIST accuracy around 90-92, with the x-axis labeled from 32 to 256. The third graph in the bottom row shows MNIST accuracy around 97-98 and F-MNIST accuracy around 90-92, with the x-axis labeled from 128 to 768. The fourth graph in the bottom row shows CIFAR100 accuracy decreasing from approximately 40 to 25 as the number of bases increases from 64 to 768.

Figure 5: Number of bases v.s. retrain accuracy on validation set. I/C: images per class.

Further ablations on momentum terms. How is the momentum term exactly affecting the backpropagation through time process? We analyze the performance of baseline BPTT algorithms on three cases: no-momentum, forward-only momentum, and full momentum. No-momentum uses BPTT without momentum terms. Forward-only momentum uses the momentum term only in the forward BPTT, but blocks the gradients on the momentum term in the backward pass (except for the gradients on the current time step weights) to remove the "bridging effect" of momentum term across multiple steps. Full momentum is our full model. All the experiments are performed on CIFAR10 with 200 inner optimization steps.

| I/C | no-momentum    | forward-only momentum | full momentum  |
|-----|----------------|-----------------------|----------------|
| 1   | $40.5 \pm 0.8$ | $45.6 \pm 0.7$        | $49.1 \pm 0.6$ |
| 10  | $50.0 \pm 0.5$ | $57.4 \pm 0.3$        | $62.4 \pm 0.4$ |

Table 8: Further analysis of momentum terms of BPTT on CIFAR10 dataset.

Adam optimizer for inner loop. We also experimented with using Adam optimizer to optimize the synthetic data, instead of using stochastic gradient descent with momentum. Empirically, we found that Adam optimizer leads to certain instability of gradients (e.g., magnitude) on the inner optimization steps when using the same learning rate magnitude and perfers smaller ones such as 1e-4. The end results are similar to the SGD algorithms.

## B.2 Continual learning

Memory designs in "compress-then-recall". We follow the Reservoir sampling strategy to store samples in the memory buffer. When learning through the tasks, our algorithm utilizes all the currently available memory buffer storage space to store samples. After the learning on one task is finished, the algorithm saves the compressed representation to the memory buffer, taking  $1/T$  the buffer where  $T$ is the total number of tasks, and clear the storage space which stores the real samples for the current task. This strategy makes sure that the compression algorithm has enough samples to replay, resulting in  $1 - (t - 1)/T$  of the storage to use, where  $t \in \{1, ..., T\}$  is the current task index.

## B.3 New classifier synthesis

Designs of "image addressing" model. Since our formulation allows flexible query forms, we use an extra ConvNet to take the visual observations (images) as input and treat the output feature vectors as queries. The feature vector queries are used for vector matrix product with addressing matrices to compute coefficients for combining bases. To train the addressing model: For every training iteration, we randomly subsample a subset of classes from all classes and pick 1 or 5 images (depends on 1-shot or 5-shot), and use the recalled synthetic datasets with the feature vectors of the images to perform inner loop optimizations. The generalization loss is computed using other image-label pairs from the subset classes (the same as standard dataset distillation training). The ConvNet (feature extractor), bases and image matrices are jointly trained.

Discussion of "image addressing" results. We compare the "image addressing" model with two strong baselines: nearest neighbor classifiers and "classify-then-recall" method. It's interesting to see that, having the ability to access the dataset-level information (even compressed) can often lead to better performance when building a new classifier, while nearest neighbor classifiers can only utilize image shots to serve as limited information for classification. Note that the "classify-then-recall" method is also a strong baseline, but can suffer from the classification errors on test images, leading to less robust recall. The direct usage of feature vectors from image shots can provide a continuous space and potentially lead to more robust behaviours in the addressing and recall processes.

# C Visualization and analysis

Coefficients similarity map. We show the full matrix of cosine similarities on the coefficients that combine the bases from all 100 classes in CIFAR100, as shown in fig. [6.](#page-17-0) The order of classes on x and y axis is organized by superclasses. Every 5 classes is under a common superclass on the axis. As shown by the matrix, we can clearly see that the classes under the same superclass often have significant similarities, indicating strong sharings when combining bases. For example, categories bridge, castle and house share similar bases; baby, girl, man and woman also share similar bases, while crab and tulips use very different coefficients to perform addressing.

Visualization on bases. In figure [7,](#page-18-0) we visualize the learned 64 bases on CIFAR100. The bases contain various colors, shapes and textures, and are used to be combined with coefficients generated from queries and addressing matrices.

# D More visualization comparisons

In this section, we further compare the visualization of our methods under various settings.

<span id="page-16-0"></span>

## D.1 Same amount of generated images

We visualize the synthetic images from the baseline method BPTT and from our proposed memory addressing parameterization. For BPTT, we use 100 image per class as the budgets, and for our

<span id="page-17-0"></span>Image /page/17/Figure/0 description: A heatmap displays the correlation between different image classes. The classes are listed vertically on the left side and horizontally on the top. The color scale on the right ranges from -0.6 (yellow) to 1.0 (dark blue), indicating the strength and direction of the correlation. Dark blue squares represent strong positive correlations, while yellow squares represent strong negative correlations. The diagonal line of dark blue squares indicates a perfect positive correlation of 1.0 for each class with itself. Some clusters of dark blue squares suggest strong positive correlations between certain pairs of classes, such as 'bear', 'leopard', 'lion', and 'tiger', and another cluster including 'maple tree', 'oak tree', 'palm tree', 'pine tree', and 'willow tree'.

֡֟֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡֡ aquarium fish sweet peppers lawn mower

Figure 6: Full coefficient cosine similarity matrix on CIFAR100. Zoom in to view the details. Classes are ordered with superclasses. On the x and y axis, in order, every 5 classes belongs to a common superclass.

method, we use 10 images per class and 43 bases to generate approximately the same amount of recalled images (99). The synthetic images are visualized in figures below. To easily compare with the vanilla version of BPTT, we do not use downsampling in either BPTT or the memory addressing formulation.

<span id="page-18-0"></span>Image /page/18/Picture/0 description: A grid of 8x8 small images, each displaying abstract, colorful patterns. The images are arranged in a square formation with black borders separating them. The overall impression is a mosaic of diverse visual elements, possibly representing features learned by a neural network.

Figure 7: CIFAR100 learned 64 bases.

| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
|-----------------------|-----------------------|-----------------------|-----------------------|
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |
| Image: abstract image | Image: abstract image | Image: abstract image | Image: abstract image |

Method: BPTT with standard parameterization Method: BPTT with memories and addressing matrices

Figure 8: Recalled synthetic images for class apple.

## D.2 Various image per class budgets

We compare the visualizations of synthetic images under various image per class (I/C) budgets. Similar to previous section [D.1,](#page-16-0) we use bases with the same shape, and compare the results under 2, 10 and 50 I/Cs. The corresponding number of bases are 8, 43 and 215. The visualization results are summarized in figure [12,](#page-21-0) figure [13](#page-22-0) and figure [14,](#page-23-0)

Image /page/19/Picture/0 description: The image displays a grid of 9x10 small, abstract, colorful images, resembling neural network visualizations or feature maps. The grid is mostly filled, with the top right corner having a gap where only the first row of images is present. Each small image contains swirling patterns of blues, yellows, reds, and grays, suggesting complex data representations. The overall impression is a systematic arrangement of visual data points.

| m |             |      |             |  |
|---|-------------|------|-------------|--|
|   |             |      | ×           |  |
|   |             | œ    |             |  |
|   |             |      |             |  |
|   | <b>PERS</b> | an a | <b>STAR</b> |  |
|   |             |      | - 14        |  |
|   |             |      | s.          |  |
|   |             |      |             |  |
|   |             |      |             |  |
| k |             |      |             |  |
|   |             |      |             |  |
|   |             |      |             |  |
|   |             |      |             |  |

Method: BPTT with standard parameterization Method: BPTT with memories and addressing matrices

Figure 9: Recalled synthetic images for class aquarium fish.

Image /page/19/Figure/5 description: A grid of 70 small images, each depicting a stylized representation of a mouth with teeth. The images are arranged in 10 rows and 7 columns, with a small section missing from the top left corner, leaving a 9x7 grid in that area. The mouths vary slightly in color and expression, with some appearing more open or closed than others. The overall impression is a mosaic of facial features, possibly generated by an artificial intelligence model.

Image /page/19/Picture/7 description: The image displays a grid of 9x10 small, abstract images, each resembling a stylized face or mask. The overall color palette is muted, with dominant tones of pink, purple, blue, and gray. The images are arranged in neat rows and columns, with a black border around each individual image and a thicker black border around the entire grid. The bottom left corner of the grid is incomplete, with only one image in the last row and no images in the last column of that row, suggesting a partial grid or a specific arrangement. The individual images themselves are somewhat blurry and abstract, with variations in color and subtle differences in shape, possibly representing different features or expressions.

Method: BPTT with standard parameterization Method: BPTT with memories and addressing matrices

Figure 10: Recalled synthetic images for class bed.

|  |   | ٠ |  |
|--|---|---|--|
|  |   |   |  |
|  |   |   |  |
|  |   |   |  |
|  |   |   |  |
|  | ı |   |  |
|  |   |   |  |
|  |   |   |  |
|  |   |   |  |
|  |   |   |  |
|  |   |   |  |
|  |   |   |  |

Method: BPTT with standard parameterization Method: BPTT with memories and addressing matrices

Figure 11: Recalled synthetic images for class bottle.

<span id="page-21-0"></span>

Figure 12: Recalled synthetic images for classes apple, aquarium fish, and baby under 2 I/C with 8 bases.

<span id="page-22-0"></span>Image /page/22/Figure/0 description: This is a grid of images, likely representing features or activations from a neural network. The grid is arranged in columns and rows. The top section of the grid contains images that appear to be apples, with variations in color and shape. Below the apple images, there is a section with images that resemble fish, displaying various colors and patterns. The bottom section of the grid contains images that look like faces or people, with different expressions and features. A number '23' is visible in the bottom right corner of the grid.

Figure 13: Recalled synthetic images for classes apple, aquarium fish, and baby under 10 I/C with 43 bases.

<span id="page-23-0"></span>

| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
|-----------------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|-----------------------------|
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |
| Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images | Image: grid of small images |

Figure 14: Recalled synthetic images for classes apple, aquarium fish, and baby under 50 I/C with 215 bases.
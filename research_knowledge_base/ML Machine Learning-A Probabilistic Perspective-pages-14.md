### **Algorithm 11.1:** K-means algorithm

## **1** *initialize* $\mathbf{m}_k$ ;

## **<sup>2</sup> repeat**

- **3** Assign each data point to its closest cluster center:  $z_i = \arg \min_k ||\mathbf{x}_i \boldsymbol{\mu}_k||_2^2$ ;
- **<sup>4</sup>** Update each cluster center by computing the mean of all points assigned to it:

$$
\Big| \quad \boldsymbol{\mu}_k = \tfrac{1}{N_k} \sum_{i: z_i = k} \mathbf{x}_i;
$$

**<sup>5</sup> until** *converged*;

Image /page/0/Figure/8 description: The image displays two figures, labeled (a) and (b), each showing a grayscale image of a clown's face. Figure (a) is labeled "K=2" and appears to be a binary image with only black and white pixels, representing a segmentation of the clown's face into two clusters. Figure (b) is labeled "K=4" and shows a more detailed segmentation of the clown's face into four grayscale levels, with shades of black, dark gray, light gray, and white. Both figures have axes indicating pixel coordinates, with the x-axis ranging from 50 to 300 and the y-axis from 20 to 200.

**Figure 11.12** An image compressed using vector quantization with a codebook of size K. (a)  $K = 2$ . (b)  $K = 4$ . Figure generated by vqDemo.

## ********** Vector quantization**

Since K-means is not a proper EM algorithm, it is not maximizing likelihood. Instead, it can be interpreted as a greedy algorithm for approximately minimizing a loss function related to data compression, as we now explain.

Suppose we want to perform lossy compression of some real-valued vectors,  $\mathbf{x}_i \in \mathbb{R}^D$ . A very simple approach to this is to use **vector quantization** or **VQ**. The basic idea is to replace each real-valued vector  $\mathbf{x}_i \in \mathbb{R}^D$  with a discrete symbol  $z_i \in \{1, ..., K\}$ , which is an index into a **codebook** of K prototypes,  $\mu_k \in \mathbb{R}^D$ . Each data vector is encoded by using the index of the most similar prototype, where similarity is measured in terms of Euclidean distance:

$$
\text{encode}(\mathbf{x}_i) = \arg \min_k ||\mathbf{x}_i - \boldsymbol{\mu}_k||^2 \tag{11.37}
$$

We can define a cost function that measures the quality of a codebook by computing the **reconstruction error** or **distortion** it induces:

$$
J(\boldsymbol{\mu}, \mathbf{z}|K, \mathbf{X}) \triangleq \frac{1}{N} \sum_{i=1}^{N} ||\mathbf{x}_i - \text{decode}(\text{encode}(\mathbf{x}_i))||^2 = \frac{1}{N} \sum_{i=1}^{N} ||\mathbf{x}_i - \boldsymbol{\mu}_{z_i}||^2
$$
(11.38)

where  $\text{decode}(k) = \mu_k$ . The K-means algorithm can be thought of as a simple iterative scheme for minimizing this objective.

Of course, we can achieve zero distortion if we assign one prototype to every data vector, but that takes  $O(NDC)$  space, where N is the number of real-valued data vectors, each of length  $D$ , and  $C$  is the number of bits needed to represent a real-valued scalar (the quantization accuracy). However, in many data sets, we see similar vectors repeatedly, so rather than storing them many times, we can store them once and then create pointers to them. Hence we can reduce the space requirement to  $O(N \log_2 K + KDC)$ : the  $O(N \log_2 K)$  term arises because each of the  $N$  data vectors needs to specify which of the  $K$  codewords it is using (the pointers); and the  $O(KDC)$  term arises because we have to store each codebook entry, each of which is a D-dimensional vector. Typically the first term dominates the second, so we can approximate the **rate** of the encoding scheme (number of bits needed per object) as  $O(log_2 K)$ , which is typically much less than  $O(DC)$ .

One application of VQ is to image compression. Consider the  $N = 200 \times 320 = 64,000$  pixel image in Figure 11.12; this is gray-scale, so  $D = 1$ . If we use one byte to represent each pixel (a gray-scale intensity of 0 to 255), then  $C = 8$ , so we need  $NC = 512,000$  bits to represent the image. For the compressed image, we need  $N \log_2 K + KC$  bits. For  $K = 4$ , this is about 128kb, a factor of 4 compression. For  $K = 8$ , this is about 192kb, a factor of 2.6 compression, at negligible perceptual loss (see Figure 11.12(b)). Greater compression could be achieved if we modelled spatial correlation between the pixels, e.g., if we encoded 5x5 blocks (as used by JPEG). This is because the residual errors (differences from the model's predictions) would be smaller, and would take fewer bits to encode.

### **11.4.2.7 Initialization and avoiding local minima**

Both K-means and EM need to be initialized. It is common to pick  $K$  data points at random, and to make these be the initial cluster centers. Or we can pick the centers sequentially so as to try to "cover" the data. That is, we pick the initial point uniformly at random. Then each subsequent point is picked from the remaining points with probability proportional to its squared distance to the points's closest cluster center. This is known as **farthest point clustering** (Gonzales 1985), or **k-means++** (Arthur and Vassilvitskii 2007; Bahmani et al. 2012). Surprisingly, this simple trick can be shown to guarantee that the distortion is never more than  $O(\log K)$  worse than optimal (Arthur and Vassilvitskii 2007).

An heuristic that is commonly used in the speech recognition community is to incrementally "grow" GMMs: we initially give each cluster a score based on its mixture weight; after each round of training, we consider splitting the cluster with the highest score into two, with the new centroids being random perturbations of the original centroid, and the new scores being half of the old scores. If a new cluster has too small a score, or too narrow a variance, it is removed. We continue in this way until the desired number of clusters is reached. See (Figueiredo and Jain 2002) for a similar incremental approach.

### **11.4.2.8 MAP estimation**

As usual, the MLE may overfit. The overfitting problem is particularly severe in the case of GMMs. To understand the problem, suppose for simplicity that  $\Sigma_k = \sigma_k^2 I$ , and that  $K = 2$ . It is possible to get an infinite likelihood by assigning one of the centers, say  $\mu_2$ , to a single data point, say  $x_1$ , since then the 1st term makes the following contribution to the likelihood:

$$
\mathcal{N}(\mathbf{x}_1|\boldsymbol{\mu}_2, \sigma_2^2 I) = \frac{1}{\sqrt{2\pi\sigma_2^2}} e^0
$$
\n(11.39)

Image /page/2/Figure/1 description: The image contains two plots. Plot (a) shows a probability density function p(x) with a sharp peak and a long tail, with several data points represented by black dots on the x-axis and blue dots above them connected by green lines to the curve. Plot (b) is a line graph showing the fraction of times EM for GMM fails versus dimensionality. The x-axis ranges from 10 to 100, and the y-axis ranges from 0 to 1. Two lines are plotted: a red line with circles labeled 'MLE' and a black dotted line with squares labeled 'MAP'. The MLE line starts at approximately 0.05 at dimensionality 10, rises sharply to 0.8 at dimensionality 20, and then stays at 1 from dimensionality 30 to 100. The MAP line remains at 0 from dimensionality 10 to 60, then rises to approximately 0.1 at dimensionality 70, and then drops back to near 0 for higher dimensionalities.

**Figure 11.13** (a) Illustration of how singularities can arise in the likelihood function of GMMs. Based on (Bishop 2006a) Figure 9.7. Figure generated by mixGaussSingularity. (b) Illustration of the benefit of MAP estimation vs ML estimation when fitting a Gaussian mixture model. We plot the fraction of times (out of 5 random trials) each method encounters numerical problems vs the dimensionality of the problem, for  $N = 100$  samples. Solid red (upper curve): MLE. Dotted black (lower curve): MAP. Figure generated by mixGaussMLvsMAP.

Hence we can drive this term to infinity by letting  $\sigma_2 \rightarrow 0$ , as shown in Figure 11.13(a). We will call this the "collapsing variance problem".

An easy solution to this is to perform MAP estimation. The new auxiliary function is the expected complete data log-likelihood plus the log prior:

$$
Q'(\boldsymbol{\theta}, \boldsymbol{\theta}^{old}) = \left[ \sum_{i} \sum_{k} r_{ik} \log \pi_{ik} + \sum_{i} \sum_{k} r_{ik} \log p(\mathbf{x}_i | \boldsymbol{\theta}_k) \right] + \log p(\boldsymbol{\pi}) + \sum_{k} \log p(\boldsymbol{\theta}_k)
$$
11.40)

Note that the E step remains unchanged, but the M step needs to be modified, as we now explain.

For the prior on the mixture weights, it is natural to use a Dirichlet prior,  $\pi \sim \text{Dir}(\alpha)$ , since this is conjugate to the categorical distribution. The MAP estimate is given by

$$
\pi_k = \frac{r_k + \alpha_k - 1}{N + \sum_k \alpha_k - K} \tag{11.41}
$$

If we use a uniform prior,  $\alpha_k = 1$ , this reduces to Equation 11.28.

The prior on the parameters of the class conditional densities,  $p(\theta_k)$ , depends on the form of the class conditional densities. We discuss the case of GMMs below, and leave MAP estimation for mixtures of Bernoullis to Exercise 11.3.

For simplicity, let us consider a conjugate prior of the form

$$
p(\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k) = \text{NIW}(\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k | \mathbf{m}_0, \kappa_0, \nu_0, \mathbf{S}_0)
$$
\n(11.42)

From Section 4.6.3, the MAP estimate is given by

$$
\hat{\boldsymbol{\mu}}_k = \frac{r_k \overline{\mathbf{x}}_k + \kappa_0 \mathbf{m}_0}{r_k + \kappa_0} \tag{11.43}
$$

$$
\Delta \sum_{i} r_{ik} \mathbf{x}_i \tag{11.44}
$$

$$
\overline{\mathbf{x}}_k \quad \triangleq \quad \frac{\sum_i r_i \kappa \mathbf{x}_i}{r_k} \tag{11.45}
$$

$$
\hat{\Sigma}_k = \frac{\mathbf{S}_0 + \mathbf{S}_k + \frac{\kappa_0 r_k}{\kappa_0 + r_k} (\overline{\mathbf{x}}_k - \mathbf{m}_0)(\overline{\mathbf{x}}_k - \mathbf{m}_0)^T}{\nu_0 + r_k + D + 2}
$$
(11.46)

$$
\mathbf{S}_k \triangleq \sum_i r_{ik} (\mathbf{x}_i - \overline{\mathbf{x}}_k)(\mathbf{x}_i - \overline{\mathbf{x}}_k)^T
$$
 (11.47)

We now illustrate the benefits of using MAP estimation instead of ML estimation in the context of GMMs. We apply EM to some synthetic data in  $D$  dimensions, using either ML or MAP estimation. We count the trial as a "failure" if there are numerical issues involving singular matrices. For each dimensionality, we conduct 5 random trials. The results are illustrated in Figure 11.13(b) using  $N = 100$ . We see that as soon as D becomes even moderately large, ML estimation crashes and burns, whereas MAP estimation never encounters numerical problems.

When using MAP estimation, we need to specify the hyper-parameters. Here we mention some simple heuristics for setting them (Fraley and Raftery 2007, p163). We can set  $\kappa_0 = 0$ , so that the  $\mu_k$  are unregularized, since the numerical problems only arise from  $\Sigma_k$ . In this case, the MAP estimates simplify to  $\hat{\mu}_k = \overline{\mathbf{x}}_k$  and  $\hat{\Sigma}_k = \frac{\mathbf{S}_0 + \mathbf{S}_k}{\nu_0 + r_k + D + 2}$ , which is not quite so scary-looking.

Now we discuss how to set  $S_0$ . One possibility is to use

$$
\mathbf{S}_0 = \frac{1}{K^{1/D}} \text{diag}(s_1^2, \dots, s_D^2) \tag{11.48}
$$

where  $s_j = (1/N) \sum_{i=1}^N (x_{ij} - \overline{x}_j)^2$  is the pooled variance for dimension j. (The reason for the  $\frac{1}{K^{1/D}}$  term is that the resulting volume of each ellipsoid is then given by  $|\mathbf{S}_0| =$ <br> $\frac{1}{K^{1/D}}$   $\frac{e^2}{\sqrt{2}}$   $\frac{e^2}{\sqrt{2}}$   $\frac{1}{\sqrt{2}}$  The parameter  $\mu$  controls bow strongly we believe this prior.  $\frac{1}{K}$  diag( $s_1^2, \ldots, s_D^2$ )|.) The parameter  $\nu_0$  controls how strongly we believe this prior. The weakest prior we can use, while still being proper, is to set  $\nu_0 = D + 2$ , so this is a common choice.

## **11.4.3 EM for mixture of experts**

We can fit a mixture of experts model using EM in a straightforward manner. The expected complete data log likelihood is given by

$$
Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{old}) = \sum_{i=1}^{N} \sum_{k=1}^{K} r_{ik} \log[\pi_{ik} \mathcal{N}(y_i | \mathbf{w}_k^T \mathbf{x}_i, \sigma_k^2)] \qquad (11.49)
$$

$$
\pi_{i,k} \triangleq \mathcal{S}(\mathbf{V}^T \mathbf{x}_i)_k \tag{11.50}
$$

$$
r_{ik} \propto \pi_{ik}^{old} \mathcal{N}(y_i | \mathbf{x}_i^T \mathbf{w}_k^{old}, (\sigma_k^{old})^2)
$$
 (ll.5l)

So the E step is the same as in a standard mixture model, except we have to replace  $\pi_k$  with  $\pi_{i,k}$  when computing  $r_{ik}$ .

In the M step, we need to maximize  $Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{old})$  wrt  $\mathbf{w}_k$ ,  $\sigma_k^2$  and  $\mathbf{V}$ . For the regression parameters for model  $k$ , the objective has the form

$$
Q(\boldsymbol{\theta}_k, \boldsymbol{\theta}^{old}) = \sum_{i=1}^{N} r_{ik} \left\{ -\frac{1}{\sigma_k^2} (y_i - \mathbf{w}_k^T \mathbf{x}_i) \right\}
$$
(11.52)

We recognize this as a weighted least squares problem, which makes intuitive sense: if  $r_{ik}$  is small, then data point  $i$  will be downweighted when estimating model  $k$ 's parameters. From Section 8.3.4 we can immediately write down the MLE as

$$
\mathbf{w}_k = (\mathbf{X}^T \mathbf{R}_k \mathbf{X})^{-1} \mathbf{X}^T \mathbf{R}_k \mathbf{y}
$$
 (11.53)

where  $\mathbf{R}_k = \text{diag}(r_{k,k})$ . The MLE for the variance is given by

$$
\sigma_k^2 = \frac{\sum_{i=1}^N r_{ik} (y_i - \mathbf{w}_k^T \mathbf{x}_i)^2}{\sum_{i=1}^N r_{ik}}
$$
(11.54)

We replace the estimate of the unconditional mixing weights  $\pi$  with the estimate of the gating parameters, **V**. The objective has the form

$$
\ell(\mathbf{V}) = \sum_{i} \sum_{k} r_{ik} \log \pi_{i,k} \tag{11.55}
$$

We recognize this as equivalent to the log-likelihood for multinomial logistic regression in Equation 8.34, except we replace the "hard" 1-of-C encoding  $\mathbf{y}_i$  with the "soft" 1-of-K encoding **r**i. Thus we can estimate **V** by fitting a logistic regression model to soft target labels.

### **11.4.4 EM for DGMs with hidden variables**

We can generalize the ideas behind EM for mixtures of experts to compute the MLE or MAP estimate for an arbitrary DGM. We could use gradient-based methods (Binder et al. 1997), but it is much simpler to use EM (Lauritzen 1995): in the E step, we just estimate the hidden variables, and in the M step, we will compute the MLE using these filled-in values. We give the details below.

For simplicity of presentation, we will assume all CPDs are tabular. Based on Section 10.4.2, let us write each CPT as follows:

$$
p(x_{it}|\mathbf{x}_{i,\text{pa}(t)},\boldsymbol{\theta}_{t}) = \prod_{c=1}^{K_{\text{pa}(t)}} \prod_{k=1}^{K_t} \theta_{tck}^{\mathbb{I}(x_{it}=i,\mathbf{x}_{i,\text{pa}(t)}=c)}
$$
(11.56)

The log-likelihood of the complete data is given by

$$
\log p(\mathcal{D}|\boldsymbol{\theta}) = \sum_{t=1}^{V} \sum_{c=1}^{K_{\text{pa}(t)}} \sum_{k=1}^{K_t} N_{tck} \log \theta_{tck}
$$
\n(11.57)

where  $N_{tck} = \sum_{i=1}^{N} \mathbb{I}(x_{it} = i, \mathbf{x}_{i, \text{pa}(t)} = c)$  are the empirical counts. Hence the expected complete data log-likelihood has the form

$$
\mathbb{E}\left[\log p(\mathcal{D}|\boldsymbol{\theta})\right] = \sum_{t} \sum_{c} \sum_{k} \overline{N}_{tck} \log \theta_{tck} \tag{11.58}
$$

where

$$
\overline{N}_{tck} = \sum_{i=1}^{N} \mathbb{E} \left[ \mathbb{I}(x_{it} = i, \mathbf{x}_{i, \text{pa}(t)} = c) \right] = \sum_{i} p(x_{it} = k, \mathbf{x}_{i, \text{pa}(t)} = c | \mathcal{D}_i)
$$
\n(11.59)

where  $\mathcal{D}_i$  are all the visible variables in case *i*.

The quantity  $p(x_{it}, \mathbf{x}_{i, \text{pa}(t)} | \mathcal{D}_i, \theta)$  is known as a **family marginal**, and can be computed using any GM inference algorithm. The  $\overline{N}_{tjk}$  are the expected sufficient statistics, and constitute the output of the E step.

Given these ESS, the M step has the simple form

$$
\hat{\theta}_{tck} = \frac{\overline{N}_{tck}}{\sum_{k'} \overline{N}_{tjk'}}
$$
\n(11.60)

This can be proved by adding Lagrange multipliers (to enforce the constraint  $\sum_k \theta_{tjk} = 1$ ) to the expected complete data log likelihood, and then optimizing each parameter vector  $\theta_{tc}$ separately. We can modify this to perform MAP estimation with a Dirichlet prior by simply adding pseudo counts to the expected counts.

## **11.4.5 EM for the Student distribution \***

One problem with the Gaussian distribution is that it is sensitive to outliers, since the logprobability only decays quadratically with distance from the center. A more robust alternative is the Student t distribution, as discussed in Section **??**.

Unlike the case of a Gaussian, there is no closed form formula for the MLE of a Student, even if we have no missing data, so we must resort to iterative optimization methods. The easiest one to use is EM, since it automatically enforces the constraints that  $\nu$  is positive and that  $\Sigma$ is symmetric positive definite. In addition, the resulting algorithm turns out to have a simple intuitive form, as we see below.

At first blush, it might not be apparent why EM can be used, since there is no missing data. The key idea is to introduce an "artificial" hidden or auxiliary variable in order to simplify the algorithm. In particular, we will exploit the fact that a Student distribution can be written as a **Gaussian scale mixture**:

$$
\mathcal{T}(\mathbf{x}_i|\boldsymbol{\mu}, \boldsymbol{\Sigma}, \nu) = \int \mathcal{N}(\mathbf{x}_i|\boldsymbol{\mu}, \boldsymbol{\Sigma}/z_i) \text{Ga}(z_i | \frac{\nu}{2}, \frac{\nu}{2}) dz_i
$$
\n(II.61)

(See Exercise 11.1 for a proof of this in the 1d case.) This can be thought of as an "infinite" mixture of Gaussians, each one with a slightly different covariance matrix.

Treating the  $z_i$  as missing data, we can write the complete data log likelihood as

$$
\ell_c(\boldsymbol{\theta}) = \sum_{i=1}^N \left[ \log \mathcal{N}(\mathbf{x}_i | \boldsymbol{\mu}, \boldsymbol{\Sigma}/z_i) + \log \mathrm{Ga}(z_i | \nu/2, \nu/2) \right]
$$
(11.62)

$$
= \sum_{i=1}^{N} \left[ -\frac{D}{2} \log(2\pi) - \frac{1}{2} \log |\Sigma| - \frac{z_i}{2} \delta_i + \frac{\nu}{2} \log \frac{\nu}{2} - \log \Gamma(\frac{\nu}{2}) \right]
$$
(11.63)

$$
+\frac{\nu}{2}(\log z_i - z_i) + (\frac{D}{2} - 1)\log z_i\bigg]
$$
\n(11.64)

where we have defined the Mahalanobis distance to be

$$
\delta_i = (\mathbf{x}_i - \boldsymbol{\mu})^T \boldsymbol{\Sigma}^{-1} (\mathbf{x}_i - \boldsymbol{\mu})
$$
\n(11.65)

We can partition this into two terms, one involving  $\mu$  and  $\Sigma$ , and the other involving  $\nu$ . We have, dropping irrelevant constants,

$$
\ell_c(\boldsymbol{\theta}) = L_N(\boldsymbol{\mu}, \boldsymbol{\Sigma}) + L_G(\nu) \tag{11.66}
$$

$$
L_N(\boldsymbol{\mu}, \boldsymbol{\Sigma}) \quad \triangleq \quad -\frac{1}{2} N \log |\boldsymbol{\Sigma}| - \frac{1}{2} \sum_{i=1}^N z_i \delta_i \tag{11.67}
$$

$$
L_G(\nu) \triangleq -N \log \Gamma(\nu/2) + \frac{1}{2} N \nu \log(\nu/2) + \frac{1}{2} \nu \sum_{i=1}^N (\log z_i - z_i)
$$
 (11.68)

## **11.4.5.1 EM with** *ν* **known**

Let us first derive the algorithm with  $\nu$  assumed known, for simplicity. In this case, we can ignore the  $L_G$  term, so we only need to figure out how to compute  $\mathbb{E}[z_i]$  wrt the old parameters. From Section 4.6.2.2 we have

$$
p(z_i|\mathbf{x}_i,\boldsymbol{\theta}) = \text{Ga}(z_i|\frac{\nu+D}{2},\frac{\nu+\delta_i}{2})
$$
\n(11.69)

Now if  $z_i \sim \text{Ga}(a, b)$ , then  $\mathbb{E}[z_i] = a/b$ . Hence the E step at iteration t is

$$
\overline{z}_{i}^{(t)} \triangleq \mathbb{E}\left[z_{i}|\mathbf{x}_{i}, \boldsymbol{\theta}^{(t)}\right] = \frac{\nu^{(t)} + D}{\nu^{(t)} + \delta_{i}^{(t)}}\tag{11.70}
$$

The M step is obtained by maximizing  $\mathbb{E}[L_N(\mu, \Sigma)]$  to yield

$$
\hat{\boldsymbol{\mu}}^{(t+1)} = \frac{\sum_{i} \overline{z}_{i}^{(t)} \mathbf{x}_{i}}{\sum_{i} \overline{z}_{i}^{(t)}}
$$
\n(11.71)

$$
\hat{\Sigma}^{(t+1)} = \frac{1}{N} \sum_{i} \overline{z}_{i}^{(t)} (\mathbf{x}_{i} - \hat{\boldsymbol{\mu}}^{(t+1)}) (\mathbf{x}_{i} - \hat{\boldsymbol{\mu}}^{(t+1)})^{T}
$$
\n(11.72)

$$
= \frac{1}{N} \left[ \sum_{i} \overline{z}_{i}^{(t)} \mathbf{x}_{i} \mathbf{x}_{i}^{T} - \left( \sum_{i=1}^{N} \overline{z}_{i}^{(t)} \right) \hat{\boldsymbol{\mu}}^{(t+1)} (\hat{\boldsymbol{\mu}}^{(t+1)})^{T} \right]
$$
(11.73)

These results are quite intuitive: the quantity  $\overline{z}_i$  is the precision of measurement i, so if it is small, the corresponding data point is down-weighted when estimating the mean and covariance. This is how the Student achieves robustness to outliers.

## ********** EM with** *ν* **unknown**

To compute the MLE for the degrees of freedom, we first need to compute the expectation of  $L_G(\nu)$ , which involves  $z_i$  and log  $z_i$ . Now if  $z_i \sim Ga(a, b)$ , then one can show that

$$
\overline{\ell}_i^{(t)} \triangleq \mathbb{E}\left[\log z_i|\boldsymbol{\theta}^{(t)}\right] = \Psi(a) - \log b \tag{11.74}
$$

Image /page/7/Figure/1 description: The image displays two scatter plots, labeled (a) and (b), comparing the classification of 'Bankrupt' (represented by blue circles) and 'Solvent' (represented by blue triangles) using two different methods. Plot (a), titled '14 errors using gauss (red=error)', shows data points with two red ellipses and a red 'x' marking an error. Plot (b), titled '4 errors using student (red=error)', also shows data points with two red ellipses and a red 'x' marking an error. Both plots have x and y axes ranging from approximately -5 to 2 and -7 to 3, respectively. A legend in the bottom right indicates that circles represent 'Bankrupt' and triangles represent 'Solvent'.

**Figure 11.14** Mixture modeling on the bankruptcy data set. Left: Gaussian class conditional densities. Right: Student class conditional densities. Points that belong to class 1 are shown as triangles, points that belong to class 2 are shown as circles The estimated labels, based on the posterior probability of belonging to each mixture component, are computed. If these are incorrect, the point is colored red, otherwise it is colored blue. (Training data is in black.) Figure generated by mixStudentBankruptcyDemo.

where  $\Psi(x) = \frac{d}{dx} \log \Gamma(x)$  is the digamma function. Hence, from Equation 11.69, we have

$$
\bar{\ell}_i^{(t)} = \Psi(\frac{\nu^{(t)} + D}{2}) - \log(\frac{\nu^{(t)} + \delta_i^{(t)}}{2})
$$
\n(11.75)

$$
= \log(\overline{z}_i^{(t)}) + \Psi(\frac{\nu^{(t)} + D}{2}) - \log(\frac{\nu^{(t)} + D}{2}) \tag{11.76}
$$

Substituting into Equation 11.68, we have

$$
\mathbb{E}\left[L_G(\nu)\right] = -N \log \Gamma(\nu/2) + \frac{N\nu}{2} \log(\nu/2) + \frac{\nu}{2} \sum_i (\bar{\ell}_i^{(t)} - \bar{z}_i^{(t)}) \tag{11.77}
$$

The gradient of this expression is equal to

$$
\frac{d}{d\nu}\mathbb{E}\left[L_G(\nu)\right] = -\frac{N}{2}\Psi(\nu/2) + \frac{N}{2}\log(\nu/2) + \frac{N}{2} + \frac{1}{2}\sum_i(\bar{\ell}_i^{(t)} - \bar{z}_i^{(t)})\tag{11.78}
$$

This has a unique solution in the interval  $(0, +\infty]$  which can be found using a 1d constrained optimizer.

Performing a gradient-based optimization in the M step, rather than a closed-form update, is an example of what is known as the **generalized EM** algorithm. One can show that EM will still converge to a local optimum even if we only perform a "partial" improvement to the parameters in the M step.

### **11.4.5.3 Mixtures of Student distributions**

It is easy to extend the above methods to fit a mixture of Student distributions. See Exercise 11.4 for the details.

Let us consider a small example from (Lo 2009, ch3). We have a  $N = 66$ ,  $D = 2$  data set regarding the bankrupty patterns of certain companies. The first feature specifies the ratio

of retained earnings (RE) to total assets, and the second feature specifies the ratio of earnings before interests and taxes (EBIT) to total assets. We fit two models to this data, ignoring the class labels: a mixture of 2 Gaussians, and a mixture of 2 Students. We then use each fitted model to classify the data. We compute the most probable cluster membership and treat this as  $\hat{y}_i$ . We then compare  $\hat{y}_i$  to the true labels  $y_i$  and compute an error rate. If this is more than 50%, we permute the latent labels (i.e., we consider cluster 1 to represent class 2 and vice versa), and then recompute the error rate. Points which are misclassified are then shown in red. The result is shown in Figure 11.14. We see that the Student model made 4 errors, the Gaussian model made 21. This is because the class-conditional densities contain some extreme values, causing the Gaussian to be a poor choice.

### **11.4.6 EM for probit regression \***

In Section 9.4.2, we described the latent variable interpretation of probit regression. Recall that this has the form  $p(y_i = 1|z_i) = \mathbb{I}(z_i > 0)$ , where  $z_i \sim \mathcal{N}(\mathbf{w}^T \mathbf{x}_i, 1)$  is latent. We now show how to fit this model using EM. (Although it is possible to fit probit regression models using gradient based methods, as shown in Section 9.4.1, this EM-based approach has the advantage that it generalized to many other kinds of models, as we will see later on.)

The complete data log likelihood has the following form, assuming a  $\mathcal{N}(\mathbf{0}, \mathbf{V}_0)$  prior on w:

$$
\ell(\mathbf{z}, \mathbf{w} | \mathbf{V}_0) = \log p(\mathbf{y} | \mathbf{z}) + \log \mathcal{N}(\mathbf{z} | \mathbf{X} \mathbf{w}, \mathbf{I}) + \log \mathcal{N}(\mathbf{w} | \mathbf{0}, \mathbf{V}_0)
$$
(11.79)

$$
= \sum_{i} \log p(y_i|z_i) - \frac{1}{2}(\mathbf{z} - \mathbf{X}\mathbf{w})^T(\mathbf{z} - \mathbf{X}\mathbf{w}) - \frac{1}{2}\mathbf{w}^T\mathbf{V}_0^{-1}\mathbf{w} + \text{const (ll.80)}
$$

The posterior in the E step is a **truncated Gaussian**:

$$
p(z_i|y_i, \mathbf{x}_i, \mathbf{w}) = \begin{cases} \mathcal{N}(z_i|\mathbf{w}^T\mathbf{x}_i, 1)\mathbb{I}(z_i > 0) & \text{if } y_i = 1\\ \mathcal{N}(z_i|\mathbf{w}^T\mathbf{x}_i, 1)\mathbb{I}(z_i < 0) & \text{if } y_i = 0 \end{cases}
$$
(II.81)

In Equation 11.80, we see that **w** only depends linearly on **z**, so we just need to compute  $\mathbb{E}[z_i|y_i, \mathbf{x}_i, \mathbf{w}]$ . Exercise 11.15 asks you to show that the posterior mean is given by

$$
\mathbb{E}\left[z_{i}|\mathbf{w},\mathbf{x}_{i}\right] = \begin{cases} \mu_{i} + \frac{\phi(\mu_{i})}{1-\Phi(-\mu_{i})} = \mu_{i} + \frac{\phi(\mu_{i})}{\Phi(\mu_{i})} & \text{if } y_{i} = 1\\ \mu_{i} - \frac{\phi(\mu_{i})}{\Phi(-\mu_{i})} = \mu_{i} - \frac{\phi(\mu_{i})}{1-\Phi(\mu_{i})} & \text{if } y_{i} = 0 \end{cases}
$$
(ll.82)

where  $\mu_i = \mathbf{w}^T \mathbf{x}_i$ .

In the M step, we estimate **w** using ridge regression, where  $\mu = \mathbb{E}[\mathbf{z}]$  is the output we are trying to predict. Specifically, we have

$$
\hat{\mathbf{w}} = (\mathbf{V}_0^{-1} + \mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T \boldsymbol{\mu}
$$
\n(11.83)

The EM algorithm is simple, but can be much slower than direct gradient methods, as illustrated in Figure 11.15. This is because the posterior entropy in the E step is quite high, since we only observe that  $z$  is positive or negative, but are given no information from the likelihood about its magnitude. Using a stronger regularizer can help speed convergence, because it constrains the range of plausible  $z$  values. In addition, one can use various speedup tricks, such as data augmentation (van Dyk and Meng 2001), but we do not discuss that here.

Image /page/9/Figure/1 description: This is a line graph titled "probit regression with L2 regularizer of 0.100". The y-axis is labeled "penalized NLL" and ranges from 0 to 70. The x-axis is labeled "iter" and ranges from 0 to 120. There are two lines plotted: one red line with circular markers labeled "em", and one black dashed line with square markers labeled "minfunc". Both lines show a decreasing trend, with the "minfunc" line starting higher and decreasing more rapidly initially, while the "em" line starts lower and decreases more gradually, eventually leveling off at a penalized NLL of approximately 10.

**Figure 11.15** Fitting a probit regression model in 2d using a quasi-Newton method or EM. Figure generated by probitRegDemo.

# **11.4.7 Theoretical basis for EM \***

In this section, we show that EM monotonically increases the observed data log likelihood until it reaches a local maximum (or saddle point, although such points are usually unstable). Our derivation will also serve as the basis for various generalizations of EM that we will discuss later.

### **11.4.7.1 Expected complete data log likelihood is a lower bound**

Consider an arbitrary distribution  $q(\mathbf{z}_i)$  over the hidden variables. The observed data log likelihood can be written as follows:

$$
\ell(\boldsymbol{\theta}) \triangleq \sum_{i=1}^{N} \log \left[ \sum_{\mathbf{z}_i} p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta}) \right] = \sum_{i=1}^{N} \log \left[ \sum_{\mathbf{z}_i} q(\mathbf{z}_i) \frac{p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta})}{q(\mathbf{z}_i)} \right]
$$
(11.84)

Now  $log(u)$  is a *concave* function, so from Jensen's inequality (Equation 2.113) we have the following *lower bound*:

$$
\ell(\boldsymbol{\theta}) \ge \sum_{i} \sum_{\mathbf{z}_i} q_i(\mathbf{z}_i) \log \frac{p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta})}{q_i(\mathbf{z}_i)}
$$
(11.85)

Let us denote this lower bound as follows:

$$
Q(\boldsymbol{\theta}, q) \triangleq \sum_{i} \mathbb{E}_{q_i} \left[ \log p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta}) \right] + \mathbb{H} \left( q_i \right) \tag{11.86}
$$

where  $\mathbb{H}(q_i)$  is the entropy of  $q_i$ .

The above argument holds for any positive distribution  $q$ . Which one should we choose? Intuitively we should pick the q that yields the tightest lower bound. The lower bound is a sum over  $i$  of terms of the following form:

$$
L(\boldsymbol{\theta}, q_i) = \sum_{\mathbf{z}_i} q_i(\mathbf{z}_i) \log \frac{p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta})}{q_i(\mathbf{z}_i)}
$$
(11.87)

$$
= \sum_{\mathbf{z}_i} q_i(\mathbf{z}_i) \log \frac{p(\mathbf{z}_i | \mathbf{x}_i, \boldsymbol{\theta}) p(\mathbf{x}_i | \boldsymbol{\theta})}{q_i(\mathbf{z}_i)}
$$
(11.88)

$$
= \sum_{\mathbf{z}_i} q_i(\mathbf{z}_i) \log \frac{p(\mathbf{z}_i | \mathbf{x}_i, \boldsymbol{\theta})}{q_i(\mathbf{z}_i)} + \sum_{\mathbf{z}_i} q_i(\mathbf{z}_i) \log p(\mathbf{x}_i | \boldsymbol{\theta}) \qquad (11.89)
$$

$$
= -KL (q_i(\mathbf{z}_i)||p(\mathbf{z}_i|\mathbf{x}_i,\boldsymbol{\theta})) + log p(\mathbf{x}_i|\boldsymbol{\theta})
$$
\n(11.90)

The  $p(\mathbf{x}_i|\boldsymbol{\theta})$  term is independent of  $q_i$ , so we can maximize the lower bound by setting  $q_i(\mathbf{z}_i)$  =  $p(\mathbf{z}_i|\mathbf{x}_i, \theta)$ . Of course,  $\theta$  is unknown, so instead we use  $q_i^t(\mathbf{z}_i) = p(\mathbf{z}_i|\mathbf{x}_i, \theta^t)$ , where  $\theta^t$  is our estimate of the parameters at iteration  $t$ . This is the output of the E step.

Plugging this in to the lower bound we get

$$
Q(\boldsymbol{\theta}, q^t) = \sum_{i} \mathbb{E}_{q_i^t} \left[ \log p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta}) \right] + \mathbb{H} \left( q_i^t \right)
$$
\n(11.91)

We recognize the first term as the expected complete data log likelihood. The second term is a constant wrt *θ*. So the M step becomes

$$
\boldsymbol{\theta}^{t+1} = \arg \max_{\boldsymbol{\theta}} Q(\boldsymbol{\theta}, \boldsymbol{\theta}^t) = \arg \max_{\boldsymbol{\theta}} \sum_{i} \mathbb{E}_{q_i^t} \left[ \log p(\mathbf{x}_i, \mathbf{z}_i | \boldsymbol{\theta}) \right]
$$
(11.92)

as usual.

Now comes the punchline. Since we used  $q_i^t(\mathbf{z}_i) = p(\mathbf{z}_i | \mathbf{x}_i, \boldsymbol{\theta}^t)$ , the KL divergence becomes zero, so  $L(\boldsymbol{\theta}^t, q_i) = \log p(\mathbf{x}_i | \boldsymbol{\theta}^t)$ , and hence

$$
Q(\boldsymbol{\theta}^t, \boldsymbol{\theta}^t) = \sum_i \log p(\mathbf{x}_i | \boldsymbol{\theta}^t) = \ell(\boldsymbol{\theta}^t)
$$
\n(11.93)

We see that the lower bound is tight after the E step. Since the lower bound "touches" the function, maximizing the lower bound will also "push up" on the function itself. That is, the M step is guaranteed to modify the parameters so as to increase the likelihood of the observed data (unless it is already at a local maximum).

This process is sketched in Figure 11.16. The dashed red curve is the original function (the observed data log-likelihood). The solid blue curve is the lower bound, evaluated at  $\theta^t$ ; this touches the objective function at  $\theta^t$ . We then set  $\theta^{t+1}$  to the maximum of the lower bound (blue curve), and fit a new bound at that point (dotted green curve). The maximum of this new bound becomes  $\theta^{t+2}$ , etc. (Compare this to Newton's method in Figure 8.4(a), which repeatedly fits and then optimizes a quadratic approximation.)

## ********** EM monotonically increases the observed data log likelihood**

We now prove that EM monotonically increases the observed data log likelihood until it reaches a local optimum. We have

$$
\ell(\boldsymbol{\theta}^{t+1}) \ge Q(\boldsymbol{\theta}^{t+1}, \boldsymbol{\theta}^t) \ge Q(\boldsymbol{\theta}^t, \boldsymbol{\theta}^t) = \ell(\boldsymbol{\theta}^t)
$$
\n(11.94)

Image /page/11/Figure/1 description: This is a line graph showing three curves: Q(θ,θt) in blue, Q(θ,θt+1) in green, and I(θ) in red. The x-axis is labeled with three points: θt, θt+1, and θt+2. The blue curve is a downward-opening parabola that peaks at θt+1. The green curve starts at zero at θt, rises to a peak between θt+1 and θt+2, and then falls. The red curve starts at a positive value at θt, rises to a peak at θt+2, and then falls.

**Figure 11.16** Illustration of EM as a bound optimization algorithm. Based on Figure 9.14 of (Bishop 2006a). Figure generated by emLogLikelihoodMax.

where the first inequality follows since  $Q(\theta, \cdot)$  is a lower bound on  $\ell(\theta)$ ; the second inequality follows since, by definition,  $Q(\theta^{t+1}, \theta^t) = \max_{\theta} Q(\theta, \theta^t) \ge Q(\theta^t, \theta^t)$ ; and the final equality follows Equation 11.93.

As a consequence of this result, if you do not observe monotonic increase of the observed data log likelihood, you must have an error in your math and/or code. (If you are performing MAP estimation, you must add on the log prior term to the objective.) This is a surprisingly powerful debugging tool.

### **11.4.8 Online EM**

When dealing with large or streaming datasets, it is important to be able to learn online, as we discussed in Section 8.5. There are two main approaches to **online EM** in the literature. The first approach, known as **incremental EM** (Neal and Hinton 1998), optimizes the lower bound  $Q(\theta, q_1, \ldots, q_N)$  one  $q_i$  at a time; however, this requires storing the expected sufficient statistics for each data case. The second approach, known as **stepwise EM** (Sato and Ishii 2000; Cappe and Mouline 2009; Cappe 2010), is based on stochastic approximation theory, and only requires constant memory use. We explain both approaches in more detail below, following the presentation of (Liang and Klein Liang and Klein).

### **11.4.8.1 Batch EM review**

Before explaining online EM, we review batch EM in a more abstract setting. Let  $\phi(\mathbf{x}, \mathbf{z})$  be a vector of sufficient statistics for a single data case. (For example, for a mixture of multinoullis, this would be the count vector  $a(j)$ , which is the number of cluster j was used in **z**, plus the matrix  $B(j, v)$ , which is of the number of times the hidden state was j and the observed letter was v.) Let  $\mathbf{s}_i = \sum_{\mathbf{z}} p(\mathbf{z}|\mathbf{x}_i, \boldsymbol{\theta}) \phi(\mathbf{x}_i, \mathbf{z})$  be the expected sufficient statistics for case i, and  $\mu = \sum_{i=1}^{N} s_i$  be the sum of the ESS. Given  $\mu$ , we can derive an ML or MAP estimate of the parameters in the M step; we will denote this operation by  $\theta(\mu)$ . (For example, in the case of mixtures of multinoullis, we just need to normalize **a** and each row of **B**.) With this notation under our belt, the pseudo code for batch EM is as shown in Algorithm 8.

**Algorithm 11.2:** Batch EM algorithm

**<sup>1</sup>** *initialize μ*; **<sup>2</sup> repeat**  $\mu^{new} = 0$ ; **4 for** *each example*  $i = 1 : N$  **do**  $\mathbf{s}_i := \sum_{\mathbf{z}} p(\mathbf{z}|\mathbf{x}_i, \boldsymbol{\theta}(\boldsymbol{\mu})) \boldsymbol{\phi}(\mathbf{x}_i, \mathbf{z})$  ;  $\mu^{new} := \mu^{new} + s_i;$ **7**  $\mu := \mu^{new}$ ; **<sup>8</sup> until** *converged*;

## **11.4.8.2 Incremental EM**

In incremental EM (Neal and Hinton 1998), we keep track of  $\mu$  as well as the  $s_i$ . When we come to a data case, we swap out the old  $s_i$  and replace it with the new  $s_i^{new}$ , as shown in the code in Algorithm 8. Note that we can exploit the sparsity of  $\mathbf{s}_i^{new}$  to speedup the computation of  $\theta$ , since most components of  $\mu$  will not have changed.

**Algorithm 11.3:** Incremental EM algorithm

**1** *initialize*  $s_i$  for  $i = 1 : N$ ; 2  $\mu = \sum_i \mathbf{s}_i;$ **3 repeat for** each example  $i = 1 : N$  in a random order **do**  $\mathbf{s}_i^{new} := \sum_{\mathbf{z}} p(\mathbf{z}|\mathbf{x}_i, \boldsymbol{\theta}(\boldsymbol{\mu})) \boldsymbol{\phi}(\mathbf{x}_i, \mathbf{z})$  ;  $\mu := \mu + s_i^{new} - s_i;$  $\mathbf{z}^{\phantom{\dag}}\mathbf{z}^{\phantom{\dag}}\mathbf{z}^{\phantom{\dag}}_{i}:=\mathbf{s}^{new}_{i};$ **<sup>8</sup> until** *converged*;

This can be viewed as maximizing the lower bound  $Q(\theta, q_1, \ldots, q_N)$  by optimizing  $q_1$ , then *θ*, then  $q_2$ , then *θ*, etc. As such, this method is guaranteed to monotonically converge to a local maximum of the lower bound and to the log likelihood itself.

### **11.4.8.3 Stepwise EM**

In stepwise EM, whenever we compute a new  $s_i$ , we move  $\mu$  towards it, as shown in Algorithm 7.<sup>2</sup> At iteration k, the stepsize has value  $\eta_k$ , which must satisfy the Robbins-Monro conditions in Equation 8.82. For example, (Liang and Klein Liang and Klein) use  $\eta_k = (2 + k)^{-\kappa}$  for  $0.5 < \kappa < 1$ . We can get somewhat better behavior by using a minibatch of size m before each update. It is possible to optimize m and  $\kappa$  to maximize the training set likelihood, by

<sup>2.</sup> A detail: As written the update for *μ* does not exploit the sparsity of  $\mathbf{s}_i$ . We can fix this by storing  $\mathbf{m} = \frac{\mu}{\prod_{j < k} (1 - \eta_j)}$ instead of *μ*, and then using the sparse update  $m := m + \frac{\eta_k}{\prod_{j < k} (1 - \eta_j)} s_i$ . This will not affect the results (i.e.,  $\theta(\mu) = \theta(m)$ , since scaling the counts by a global constant has no effect.

Image /page/13/Figure/1 description: The image displays three dashed curves labeled 1, 2, and 3, arranged vertically. Each curve represents a different level or stage. Green dashed lines with black arrows point downwards to specific points on each curve, indicating a progression or search for a minimum. Curve 1 is relatively smooth and concave up. Curve 2 has a more complex shape with a local minimum. Curve 3 exhibits even more fluctuations and a deeper minimum. The ellipsis at the bottom suggests that this pattern continues for further levels.

**Figure 11.17** Illustration of deterministic annealing. Based on http://en.wikipedia.org/wiki/Grad uated\_optimization.

trying different values in parallel for an initial trial period; this can significantly speed up the algorithm.

**Algorithm 11.4:** Stepwise EM algorithm *initialize*  $\mu$ ;  $k = 0$ ; **2 repeat for** *each example*  $i = 1 : N$  *in a random order* **do**  $\mathbf{s}_i := \sum_{i} p(\mathbf{z}|\mathbf{x}_i, \boldsymbol{\theta}(\boldsymbol{\mu})) \boldsymbol{\phi}(\mathbf{x}_i, \mathbf{z})$ ;  $\mu := (1 - \eta_k)\mu + \eta_k \mathbf{s}_i;$   $k := k + 1$ **until** *converged*;

(Liang and Klein Liang and Klein) compare batch EM, incremental EM, and stepwise EM on four different unsupervised language modeling tasks. They found that stepwise EM (using  $\kappa \approx 0.7$  and  $m \approx 1000$ ) was faster than incremental EM, and both were much faster than batch EM. In terms of accuracy, stepwise EM was usually as good or sometimes even better than batch EM; incremental EM was often worse than either of the other methods.

## **11.4.9 Other EM variants \***

EM is one of the most widely used algorithms in statistics and machine learning. Not surprisingly, many variations have been proposed. We briefly mention a few below, some of which we will use in later chapters. See (McLachlan and Krishnan 1997) for more information.

• **Annealed EM** In general, EM will only converge to a local maximum. To increase the chance of finding the global maximum, we can use a variety of methods. One approach is to use a method known as **deterministic annealing** (Rose 1998). The basic idea is to "smooth" the posterior "landscape" by raising it to a temperature, and then gradually cooling it, all the while slowly tracking the global maximum. See Figure 11.17. for a sketch. (A stochastic version

Image /page/14/Figure/1 description: The image displays two plots, labeled (a) and (b), both illustrating the relationship between training time on the x-axis and a metric on the y-axis. In plot (a), a solid blue line labeled "true log-likelihood" and a dotted red line labeled "lower bound" both increase with training time, with the "true log-likelihood" consistently above the "lower bound" and both appearing to plateau. Plot (b) shows a similar trend initially, with both lines increasing with training time. However, the "true log-likelihood" line peaks and then slightly decreases before plateauing, while the "lower bound" line continues to increase and appears to approach the plateaued value of the "true log-likelihood" from below.

**Figure 11.18** Illustration of possible behaviors of variational EM. (a) The lower bound increases at each iteration, and so does the likelihood. (b) The lower bound increases but the likelihood decreases. In this case, the algorithm is closing the gap between the approximate and true posterior. This can have a regularizing effect. Based on Figure 6 of (Saul et al. 1996). Figure generated by varEMbound.

of this algorithm is described in Section 24.6.1.) An annealed version of EM is described in (Ueda and Nakano 1998).

- **Variational EM** In Section 11.4.7, we showed that the optimal thing to do in the E step is to make  $q_i$  be the exact posterior over the latent variables,  $q_i^t(\mathbf{z}_i) = p(\mathbf{z}_i | \mathbf{x}_i, \boldsymbol{\theta}^t)$ . In this case, the lower bound on the log likelihood will be tight, so the M step will "push up" on the log-likelihood itself. However, sometimes it is computationally intractable to perform exact inference in the E step, but we may be able to perform approximate inference. If we can ensure that the E step is performing inference based on a a lower bound to the likelihood, then the M step can be seen as monotonically increasing this lower bound (see Figure 11.18). This is called **variational EM** (Neal and Hinton 1998). See Chapter 21 for some variational inference methods that can be used in the E step.
- **Monte Carlo EM** Another approach to handling an intractable E step is to use a Monte Carlo approximation to the expected sufficient statistics. That is, we draw samples from the posterior,  $\mathbf{z}_i^s \sim p(\mathbf{z}_i | \mathbf{x}_i, \boldsymbol{\theta}^t)$ , and then compute the sufficient statistics for each completed vector,  $(\mathbf{x}_i, \mathbf{z}_i^s)$ , and then average the results. This is called **Monte Carlo EM** or **MCEM** (Wei and Tanner 1990). (If we only draw a single sample, it is called **stochastic EM** (Celeux and Diebolt 1985).) One way to draw samples is to use MCMC (see Chapter 24). However, if we have to wait for MCMC to converge inside each E step, the method becomes very slow. An alternative is to use stochastic approximation, and only perform "brief" sampling in the E step, followed by a partial parameter update. This is called **stochastic approximation EM** (Delyon et al. 1999) and tends to work better than MCEM. Another alternative is to apply MCMC to infer the parameters as well as the latent variables (a fully Bayesian approach), thus eliminating the distinction between E and M steps. See Chapter 24 for details.
- **Generalized EM** Sometimes we can perform the E step exactly, but we cannot perform the M step exactly. However, we can still monotonically increase the log likelihood by performing a "partial" M step, in which we merely increase the expected complete data log likelihood, rather than maximizing it. For example, we might follow a few gradient steps. This is called

Image /page/15/Figure/1 description: The image contains two plots, labeled (a) and (b). Both plots show the relationship between iterations on the x-axis and loglik on the y-axis. The title for both plots is K=5, D=15, N=5000. Plot (a) shows five lines representing different algorithms: EM (1.080) with blue circles, OR(1) (1.358) with red crosses, OR(1.25) (1.141) with black asterisks, OR(2) (1.219) with green triangles, and OR(5) (1.433) with light blue triangles. The y-axis ranges from -42 to -38.5. Plot (b) also shows five lines representing different algorithms with similar markers and colors, but with different values in parentheses: EM (1.315), OR(1) (1.368), OR(1.25) (1.381), OR(2) (1.540), and OR(5) (1.474). The y-axis in plot (b) ranges from -42 to -36.

**Figure 11.19** Illustration of adaptive over-relaxed EM applied to a mixture of 5 Gaussians in 15 dimensions. We show the algorithm applied to two different datasets, randomly sampled from a mixture of 10 Gaussians. We plot the convergence for different update rates  $\eta$ . Using  $\eta = 1$  gives the same results as regular EM. The actual running time is printed in the legend. Figure generated by mixGaussOverRelaxedEmDemo.

the **generalized EM** or **GEM** algorithm. (This is an unfortunate term, since there are many ways to generalize EM....)

- **ECM(E) algorithm** The **ECM** algorithm stands for "expectation conditional maximization", and refers to optimizing the parameters in the M step sequentially, if they turn out to be dependent. The **ECME** algorithm, which stands for "ECM either" (Liu and Rubin 1995), is a variant of ECM in which we maximize the expected complete data log likelihood (the Q function) as usual, or the observed data log likelihood, during one or more of the conditional maximization steps. The latter can be much faster, since it ignores the results of the E step, and directly optimizes the objective of interest. A standard example of this is when fitting the Student T distribution. For fixed  $\nu$ , we can update  $\Sigma$  as usual, but then to update  $\nu$ , we replace the standard update of the form  $\nu^{t+1} = \arg \max_{\nu} Q((\mu^{t+1}, \Sigma^{t+1}, \nu), \theta^t)$  with  $\nu^{t+1}$  = arg max<sub> $\nu$ </sub> log  $p(\mathcal{D}|\boldsymbol{\mu}^{t+1}, \boldsymbol{\Sigma}^{t+1}, \nu)$ . See (McLachlan and Krishnan 1997) for more information.
- **Over-relaxed EM** Vanilla EM can be quite slow, especially if there is lots of missing data. The adaptive **overrelaxed EM algorithm** (Salakhutdinov and Roweis 2003) performs an update of the form  $\theta^{t+1} = \theta^t + \eta(M(\theta^t) - \theta^t)$ , where  $\eta$  is a step-size parameter, and  $M(\theta^t)$  is the usual update computed during the M step. Obviously this reduces to standard EM if  $\eta = 1$ , but using larger values of  $\eta$  can result in faster convergence. See Figure 11.19 for an illustration. Unfortunately, using too large a value of  $\eta$  can cause the algorithm to fail to converge.

Finally, note that EM is in fact just a special case of a larger class of algorithms known as **bound optimization** or **MM** algorithms (MM stands for **minorize-maximize**). See (Hunter and Lange 2004) for further discussion.

# **11.5 Model selection for latent variable models**

When using LVMs, we must specify the number of latent variables, which controls the model complexity. In particuarl, in the case of mixture models, we must specify  $K$ , the number of clusters. Choosing these parameters is an example of model selection. We discuss some approaches below.

### **11.5.1 Model selection for probabilistic models**

The optimal Bayesian approach, discussed in Section 5.3, is to pick the model with the largest marginal likelihood,  $K^* = \operatorname{argmax}_{k} p(\mathcal{D}|K)$ .

There are two problems with this. First, evaluating the marginal likelihood for LVMs is quite difficult. In practice, simple approximations, such as BIC, can be used (see e.g., (Fraley and Raftery 2002)). Alternatively, we can use the cross-validated likelihood as a performance measure, although this can be slow, since it requires fitting each model  $F$  times, where  $F$  is the number of CV folds.

The second issue is the need to search over a potentially large number of models. The usual approach is to perform exhaustive search over all candidate values of  $K$ . However, sometimes we can set the model to its maximal size, and then rely on the power of the Bayesian Occam's razor to "kill off" unwanted components. An example of this will be shown in Section 21.6.1.6, when we discuss variational Bayes.

An alternative approach is to perform stochastic sampling in the space of models. Traditional approaches, such as (Green 1998, 2003; Lunn et al. 2009), are based on reversible jump MCMC, and use birth moves to propose new centers, and death moves to kill off old centers. However, this can be slow and difficult to implement. A simpler approach is to use a Dirichlet process mixture model, which can be fit using Gibbs sampling, but still allows for an unbounded number of mixture components; see Section 25.2 for details.

Perhaps surprisingly, these sampling-based methods can be faster than the simple approach of evaluating the quality of each  $K$  separately. The reason is that fitting the model for each  $K$  is often slow. By contrast, the sampling methods can often quickly determine that a certain value of  $K$  is poor, and thus they need not waste time in that part of the posterior.

## **11.5.2 Model selection for non-probabilistic methods**

What if we are not using a probabilistic model? For example, how do we choose  $K$  for the  $K$ means algorithm? Since this does not correspond to a probability model, there is no likelihood, so none of the methods described above can be used.

An obvious proxy for the likelihood is the reconstruction error. Define the squared reconstruction error of a data set  $D$ , using model complexity  $K$ , as follows:

$$
E(\mathcal{D}, K) = \frac{1}{|\mathcal{D}|} \sum_{i \in \mathcal{D}} ||\mathbf{x}_i - \hat{\mathbf{x}}_i||^2
$$
\n(11.95)

In the case of K-means, the reconstruction is given by  $\hat{\mathbf{x}}_i = \boldsymbol{\mu}_{z_i}$ , where  $z_i = \text{argmin}_k ||\mathbf{x}_i - \mathbf{x}_i||$  $\mu_k$ ||<sup>2</sup>, as explained in Section ********.

Figure 11.20(a) plots the reconstruction error on the *test set* for K-means. We notice that the error decreases with increasing model complexity! The reason for this behavior is as follows:

Image /page/17/Figure/1 description: This image contains two plots. Plot (a) is titled "MSE on test vs K for K-means" and shows a line graph with points representing the Mean Squared Error (MSE) on the y-axis, ranging from 0 to 0.25, plotted against the number of clusters (K) on the x-axis, ranging from 2 to 16. The MSE decreases as K increases. Plot (b) is titled "NLL on test set vs K for GMM" and shows a line graph with points representing the Negative Log-Likelihood (NLL) on the y-axis, ranging from 1195 to 1245, plotted against the number of components (K) on the x-axis, ranging from 2 to 16. The NLL initially decreases sharply from K=2 to K=4, then fluctuates slightly before generally increasing for larger values of K.

**Figure 11.20** Test set performance vs K for data generated from a mixture of 3 Gaussians in 1d (data is shown in Figure 11.21(a)). (a) MSE on test set for K-means. (b) Negative log likelihood on test set for GMM. Figure generated by kmeansModelSel1d.

Image /page/17/Figure/3 description: The image displays three plots labeled (a), (b), and (c). Plot (a) is a histogram titled 'Xtrain' with the x-axis ranging from -3 to 3 and the y-axis ranging from 0 to 60. Plot (b) contains six smaller plots, each showing vertical red bars. These plots are labeled with 'K=' followed by a number (2, 3, 4, 5, 6, 10) and 'mse=' followed by a decimal value. The x-axis for these plots ranges from -2 to 2, and the y-axis ranges from 0 to 1. Plot (c) contains six smaller plots, each showing a blue curve representing a probability distribution. These plots are labeled with 'K=' followed by a number (2, 3, 4, 5, 6, 10) and 'nll=' followed by a decimal value. The x-axis for these plots ranges from -2 to 2, and the y-axis ranges from 0 to 0.5.

**Figure 11.21** Synthetic data generated from a mixture of 3 Gaussians in 1d. (a) Histogram of training data. (Test data looks essentially the same.) (b) Centroids estimated by K-means for  $K \in \{2,3,4,5,6,10\}$ . (c) GMM density model estimated by EM for for the same values of  $K$ . Figure generated by kmeansModelSel1d.

when we add more and more centroids to  $K$ -means, we can "tile" the space more densely, as shown in Figure 11.21(b). Hence any given test vector is more likely to find a close prototype to accurately represent it as  $K$  increases, thus decreasing reconstruction error. However, if we use a probabilistic model, such as the GMM, and plot the negative log-likelihood, we get the usual U-shaped curve on the test set, as shown in Figure 11.20(b).

In supervised learning, we can always use cross validation to select between non-probabilistic models of different complexity, but this is not the case with unsupervised learning. Although this is not a novel observation (e.g., it is mentioned in passing in (Hastie et al. 2009, p519), one of the standard references in this field), it is perhaps not as widely appreciated as it should be. In fact, it is one of the more compelling arguments in favor of probabilistic models.

Given that cross validation doesn't work, and supposing one is unwilling to use probabilistic models (for some bizarre reason...), how can one choose  $K$ ? The most common approach is to plot the reconstruction error on the training set versus K, and to try to identify a **knee** or **kink** in the curve. The idea is that for  $K < K^*$ , where  $K^*$  is the "true" number of clusters, the rate of decrease in the error function will be high, since we are splitting apart things that should not be grouped together. However, for  $K > K^*$ , we are splitting apart "natural" clusters, which does not reduce the error by as much.

This kink-finding process can be automated by use of the **gap statistic** (Tibshirani et al. 2001). Nevertheless, identifying such kinks can be hard, as shown in Figure 11.20(a), since the loss function usually drops off gradually. A different approach to "kink finding" is described in Section 12.3.2.1.

# **11.6 Fitting models with missing data**

Suppose we want to fit a joint density model by maximum likelihood, but we have "holes" in our data matrix, due to missing data (usually represented by NaNs). More formally, let  $O_{ij} = 1$  if component j of data case i is observed, and let  $O_{ij} = 0$  otherwise. Let  $\mathbf{X}_v = \{x_{ij} : O_{ij} = 1\}$ be the visible data, and  $\mathbf{X}_h = \{x_{ij} : O_{ij} = 0\}$  be the missing or hidden data. Our goal is to compute

$$
\hat{\boldsymbol{\theta}} = \underset{\boldsymbol{\theta}}{\operatorname{argmax}} p(\mathbf{X}_v | \boldsymbol{\theta}, \mathbf{O})
$$
\n(11.96)

Under the missing at random assumption (see Section 8.6.2), we have

$$
p(\mathbf{X}_v|\boldsymbol{\theta}, \mathbf{O}) = \prod_{i=1}^N p(\mathbf{x}_{iv}|\boldsymbol{\theta})
$$
\n(11.97)

where  $\mathbf{x}_{iv}$  is a vector created from row i and the columns indexed by the set  $\{j : O_{ij} = 1\}$ . Hence the log-likelihood has the form

$$
\log p(\mathbf{X}_v|\boldsymbol{\theta}) = \sum_i \log p(\mathbf{x}_{iv}|\boldsymbol{\theta})
$$
\n(11.98)

where

$$
p(\mathbf{x}_{iv}|\boldsymbol{\theta}) = \sum_{\mathbf{x}_{ih}} p(\mathbf{x}_{iv}, \mathbf{x}_{ih}|\boldsymbol{\theta})
$$
\n(11.99)

and  $\mathbf{x}_{ih}$  is the vector of hidden variables for case i (assumed discrete for notational simplicity). Substituting in, we get

$$
\log p(\mathbf{X}_v|\boldsymbol{\theta}) = \sum_i \log \left[ \sum_{\mathbf{x}_{ih}} p(\mathbf{x}_{iv}, \mathbf{x}_{ih}|\boldsymbol{\theta}) \right]
$$
(11.100)

Unfortunately, this objective is hard to maximize. since we cannot push the log inside the sum. However, we can use the EM algorithm to compute a local optimum. We give an example of this below.

# **11.6.1 EM for the MLE of an MVN with missing data**

Suppose we want to fit an MVN by maximum likelihood, but we have missing data. We can use EM to find a local maximum of the objective, as we explain below.

## **11.6.1.1 Getting started**

To get the algorithm started, we can compute the MLE based on those rows of the data matrix that are fully observed. If there are no such rows, we can use some ad-hoc imputation procedures, and then compute an initial MLE.

## **11.6.1.2 E step**

Once we have  $θ^{t-1}$ , we can compute the expected complete data log likelihood at iteration t as follows:

$$
Q(\boldsymbol{\theta}, \boldsymbol{\theta}^{t-1}) = \mathbb{E}\left[\sum_{i=1}^{N} \log \mathcal{N}(\mathbf{x}_i | \boldsymbol{\mu}, \boldsymbol{\Sigma}) | \mathcal{D}, \boldsymbol{\theta}^{t-1}\right]
$$
(11.101)

$$
= -\frac{N}{2}\log|2\pi\Sigma| - \frac{1}{2}\sum_{i} \mathbb{E}\left[ (\mathbf{x}_{i} - \boldsymbol{\mu})^{T} \boldsymbol{\Sigma}^{-1} (\mathbf{x}_{i} - \boldsymbol{\mu}) \right]
$$
(11.102)

$$
= -\frac{N}{2}\log|2\pi\Sigma| - \frac{1}{2}\mathrm{tr}(\Sigma^{-1}\sum_{i}\mathbb{E}\left[ (\mathbf{x}_{i} - \boldsymbol{\mu})(\mathbf{x}_{i} - \boldsymbol{\mu})^{T} \right]
$$
(11.103)

$$
= -\frac{N}{2}\log|\mathbf{\Sigma}| - \frac{ND}{2}\log(2\pi) - \frac{1}{2}\mathrm{tr}(\mathbf{\Sigma}^{-1}\mathbb{E}[\mathbf{S}(\boldsymbol{\mu})]) \tag{11.104}
$$

where

$$
\mathbb{E}\left[\mathbf{S}(\boldsymbol{\mu})\right] \triangleq \sum_{i} \left(\mathbb{E}\left[\mathbf{x}_{i} \mathbf{x}_{i}^{T}\right] + \boldsymbol{\mu} \boldsymbol{\mu}^{T} - 2 \boldsymbol{\mu} \mathbb{E}\left[\mathbf{x}_{i}\right]^{T}\right)
$$
\n(11.105)

(We drop the conditioning of the expectation on  $D$  and  $\theta^{t-1}$  for brevity.) We see that we need to compute  $\sum_i \mathbb{E} [\mathbf{x}_i]$  and  $\sum_i \mathbb{E} [\mathbf{x}_i \mathbf{x}_i^T]$ ; these are the expected sufficient statistics.

To compute these quantities, we use the results from Section 4.3.1. Specifically, consider case i, where components v are observed and components h are unobserved. We have

$$
\mathbf{x}_{ih}|\mathbf{x}_{iv},\boldsymbol{\theta} \sim \mathcal{N}(\mathbf{m}_i,\mathbf{V}_i) \tag{11.106}
$$

$$
\mathbf{m}_i \triangleq \boldsymbol{\mu}_h + \boldsymbol{\Sigma}_{hv} \boldsymbol{\Sigma}_{vv}^{-1} (\mathbf{x}_{iv} - \boldsymbol{\mu}_v) \tag{11.107}
$$

$$
\mathbf{V}_i \triangleq \mathbf{\Sigma}_{hh} - \mathbf{\Sigma}_{hv} \mathbf{\Sigma}_{vv}^{-1} \mathbf{\Sigma}_{vh} \tag{11.108}
$$

Hence the expected sufficient statistics are

$$
\mathbb{E}\left[\mathbf{x}_{i}\right] = \left(\mathbb{E}\left[\mathbf{x}_{ih}\right]; \mathbf{x}_{iv}\right) = \left(\mathbf{m}_{i}; \mathbf{x}_{iv}\right) \tag{11.109}
$$

where we have assumed (without loss of generality) that the unobserved variables come before the observed variables in the node ordering.

To compute  $\mathbb{E} \left[ \mathbf{x}_i \mathbf{x}_i^T \right]$ , we use the result that  $\text{cov} \left[ \mathbf{x} \right] = \mathbb{E} \left[ \mathbf{x} \mathbf{x}^T \right] - \mathbb{E} \left[ \mathbf{x} \right] \mathbb{E} \left[ \mathbf{x}^T \right]$ . Hence

$$
\mathbb{E}\left[\mathbf{x}_{i}\mathbf{x}_{i}^{T}\right] = \mathbb{E}\left[\begin{pmatrix} \mathbf{x}_{ih} \\ \mathbf{x}_{iv} \end{pmatrix}\begin{pmatrix} \mathbf{x}_{ih}^{T} & \mathbf{x}_{iv}^{T} \end{pmatrix}\right] = \begin{pmatrix} \mathbb{E}\left[\mathbf{x}_{ih}\mathbf{x}_{ih}^{T}\right] & \mathbb{E}\left[\mathbf{x}_{ih}\right]\mathbf{x}_{iv}^{T} \\ \mathbf{x}_{iv}\mathbb{E}\left[\mathbf{x}_{ih}\right]^{T} & \mathbf{x}_{iv}\mathbf{x}_{iv}^{T} \end{pmatrix}
$$
(11.110)

$$
\mathbb{E}\left[\mathbf{x}_{ih}\mathbf{x}_{ih}^T\right] = \mathbb{E}\left[\mathbf{x}_{ih}\right]\mathbb{E}\left[\mathbf{x}_{ih}\right]^T + \mathbf{V}_i \tag{11.11}
$$

### **11.6.1.3 M step**

By solving  $\nabla Q(\theta, \theta^{(t-1)}) = 0$ , we can show that the M step is equivalent to plugging these ESS into the usual MLE equations to get

$$
\mu^t = \frac{1}{N} \sum_{i} \mathbb{E} \left[ \mathbf{x}_i \right] \tag{11.112}
$$

$$
\Sigma^{t} = \frac{1}{N} \sum_{i} \mathbb{E} \left[ \mathbf{x}_{i} \mathbf{x}_{i}^{T} \right] - \boldsymbol{\mu}^{t} (\boldsymbol{\mu}^{t})^{T}
$$
\n(11.113)

Thus we see that EM is *not* equivalent to simply replacing variables by their expectations and applying the standard MLE formula; that would ignore the posterior variance and would result in an incorrect estimate. Instead we must compute the expectation of the sufficient statistics, and plug that into the usual equation for the MLE. We can easily modify the algorithm to perform MAP estimation, by plugging in the ESS into the equation for the MAP estimate. For an implementation, see gaussMissingFitEm.

### **11.6.1.4 Example**

As an example of this procedure in action, let us reconsider the imputation problem from Section 4.3.2.3, which had  $N = 100$  10-dimensional data cases, with 50% missing data. Let us fit the parameters using EM. Call the resulting parameters  $\hat{\theta}$ . We can use our model for predictions by computing  $\mathbb{E}\left[\mathbf{x}_{ih}|\mathbf{x}_{iv},\hat{\boldsymbol{\theta}}\right]$ . Figure 11.22(a-b) indicates that the results obtained using the learned parameters are almost as good as with the true parameters. Not surprisingly, performance improves with more data, or as the fraction of missing data is reduced.

## ********** Extension to the GMM case**

It is straightforward to fit a mixture of Gaussians in the presence of partially observed data vectors  $x_i$ . We leave the details as an exercise.

### **Exercises**

**Exercise 11.1** Student T as infinite mixture of Gaussians Derive Equation 11.61. For simplicity, assume a one-dimensional distribution.

Image /page/21/Figure/1 description: The image displays a 2x2 grid of scatter plots, divided into two main sections labeled (a) and (b). Section (a) is titled "imputation with true params" and contains two scatter plots. The top-left plot shows 'imputed' on the y-axis and 'truth' on the x-axis, with R-squared value of 0.260. The x-axis ranges from -10 to 10, and the y-axis ranges from -10 to 10. The top-right plot also shows 'imputed' on the y-axis and 'truth' on the x-axis, with an R-squared value of 0.685. The x-axis ranges from -20 to 20, and the y-axis ranges from -10 to 20. Below these, the bottom-left plot shows 'imputed' on the y-axis and 'truth' on the x-axis, with an R-squared value of 0.399. The x-axis ranges from -10 to 10, and the y-axis ranges from -10 to 10. The bottom-right plot shows 'imputed' on the y-axis and 'truth' on the x-axis, with an R-squared value of 0.531. The x-axis ranges from -10 to 10, and the y-axis ranges from -10 to 10. Section (b) is titled "imputation with em" and also contains two scatter plots. The top-left plot shows 'imputed' on the y-axis and 'truth' on the x-axis, with an R-squared value of 0.220. The x-axis ranges from -10 to 10, and the y-axis ranges from -10 to 10. The top-right plot shows 'imputed' on the y-axis and 'truth' on the x-axis, with an R-squared value of 0.609. The x-axis ranges from -20 to 20, and the y-axis ranges from -10 to 20. Below these, the bottom-left plot shows 'imputed' on the y-axis and 'truth' on the x-axis, with an R-squared value of 0.113. The x-axis ranges from -10 to 10, and the y-axis ranges from -10 to 10. The bottom-right plot shows 'imputed' on the y-axis and 'truth' on the x-axis, with an R-squared value of 0.532. The x-axis ranges from -10 to 10, and the y-axis ranges from -10 to 10.

**Figure 11.22** Illustration of data imputation. (a) Scatter plot of true values vs imputed values using true parameters. (b) Same as (b), but using parameters estimated with EM. Figure generated by gaussImputationDemo.

### **Exercise 11.2** EM for mixtures of Gaussians

Show that the M step for ML estimation of a mixture of Gaussians is given by

$$
\mu_k = \frac{\sum_i r_{ik} \mathbf{x}_i}{r_k} \tag{11.114}
$$

$$
\Sigma_k = \frac{\sum_i r_{ik} (\mathbf{x}_i - \boldsymbol{\mu}_k) (\mathbf{x}_i - \boldsymbol{\mu}_k)^T}{r_k} = \frac{\sum_i r_{ik} \mathbf{x}_i \mathbf{x}_i^T - r_k \boldsymbol{\mu}_k \boldsymbol{\mu}_k^T}{r_k}
$$
(11.115)

**Exercise 11.3** EM for mixtures of Bernoullis

• Show that the M step for ML estimation of a mixture of Bernoullis is given by

$$
\mu_{kj} = \frac{\sum_{i} r_{ik} x_{ij}}{\sum_{i} r_{ik}} \tag{11.116}
$$

• Show that the M step for MAP estimation of a mixture of Bernoullis with a  $\beta(\alpha, \beta)$  prior is given by

$$
\mu_{kj} = \frac{\left(\sum_{i} r_{ik} x_{ij}\right) + \alpha - 1}{\left(\sum_{i} r_{ik}\right) + \alpha + \beta - 2} \tag{11.117}
$$

**Exercise 11.4** EM for mixture of Student distributions

Derive the EM algorithm for ML estimation of a mixture of multivariate Student T distributions.

**Exercise 11.5** Gradient descent for fitting GMM

Consider the Gaussian mixture model

$$
p(\mathbf{x}|\boldsymbol{\theta}) = \sum_{k} \pi_k \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)
$$
\n(11.118)

Define the log likelihood as

$$
\ell(\boldsymbol{\theta}) = \sum_{n=1}^{N} \log p(\mathbf{x}_n | \boldsymbol{\theta})
$$
\n(11.119)

Image /page/22/Figure/1 description: This is a graphical model diagram. It shows a plate labeled 'N' containing a shaded circle labeled 'xn' and two unshaded circles labeled 'Jn' and 'Kn'. Arrows point from 'Jn' and 'Kn' to 'xn'. Above the plate, there are two unshaded circles labeled 'p' and 'q', with arrows pointing to 'Jn' and 'Kn' respectively. Below the plate, there are two separate boxes. The left box is labeled 'm' and contains an unshaded circle labeled 'μj'. The right box is labeled 'l' and contains an unshaded circle labeled 'σk'. Arrows point from 'μj' to 'xn' and from 'σk' to 'xn'.

**Figure 11.23** A mixture of Gaussians with two discrete latent indicators.  $J_n$  specifies which mean to use, and  $K_n$  specifies which variance to use.

Define the posterior responsibility that cluster k has for datapoint n as follows:

$$
r_{nk} \triangleq p(z_n = k | \mathbf{x}_n, \boldsymbol{\theta}) = \frac{\pi_k \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)}{\sum_{k'=1}^K \pi_{k'} \mathcal{N}(\mathbf{x}_n | \boldsymbol{\mu}_{k'}, \boldsymbol{\Sigma}_{k'})}
$$
(11.120)

a. Show that the gradient of the log-likelihood wrt  $\mu_k$  is

$$
\frac{d}{d\mu_k}\ell(\boldsymbol{\theta}) = \sum_n r_{nk} \Sigma_k^{-1} (\mathbf{x}_n - \mu_k)
$$
\n(11.121)

b. Derive the gradient of the log-likelihood wrt  $\pi_k$ . (For now, ignore any constraints on  $\pi_k$ .)

c. One way to handle the constraint that  $\sum_{k=1}^{K} \pi_k = 1$  is to reparameterize using the softmax function:

$$
\pi_k \triangleq \frac{e^{w_k}}{\sum_{k'=1}^K e^{w_{k'}}}
$$
\n(11.122)

Here  $w_k \in \mathbb{R}$  are unconstrained parameters. Show that

$$
\frac{d}{dw_k}\ell(\boldsymbol{\theta}) = \sum_n r_{nk} - \pi_k \tag{11.123}
$$

(There may be a constant factor missing in the above expression...) Hint: use the chain rule and the fact that

$$
\frac{d\pi_j}{dw_k} = \begin{cases} \pi_j(1-\pi_j) & \text{if } j=k\\ -\pi_j\pi_k & \text{if } j\neq k \end{cases}
$$
\n(11.124)

which follows from Exercise 8.4(1).

- d. Derive the gradient of the log-likelihood wrt **Σ**k. (For now, ignore any constraints on **Σ**k.)
- e. One way to handle the constraint that **Σ**<sup>k</sup> be a symmetric positive definite matrix is to reparameterize using a Cholesky decomposition,  $\Sigma_k = \mathbf{R}_k^T \mathbf{R}$ , where **R** is an upper-triangular, but otherwise unconstrained matrix. Derive the gradient of the log-likelihood wrt  $\mathbf{R}_k$ . unconstrained matrix. Derive the gradient of the log-likelihood wrt  $\mathbf{R}_k$ .

#### **Exercise 11.6** EM for a finite scale mixture of Gaussians

(Source: Jaakkola..) Consider the graphical model in Figure 11.23 which defines the following:

$$
p(x_n|\theta) = \sum_{j=1}^{m} p_j \left[ \sum_{k=1}^{l} q_k N(x_n | \mu_j, \sigma_k^2) \right]
$$
 (11.125)

where  $\theta = \{p_1, \ldots, p_m, \mu_1, \ldots, \mu_m, q_1, \ldots, q_l, \sigma_1^2, \ldots, \sigma_l^2\}$  are all the parameters. Here  $p_j \triangleq P(J_n = i)$  and  $q_i \triangleq P(K_i = k)$  are the conjugator of mixture weights. We can think of this as a mixture j) and  $q_k \triangleq P(K_n = k)$  are the equivalent of mixture weights. We can think of this as a mixture of m non-Gaussian components, where each component distribution is a scale mixture,  $p(x|i;\theta)$ of m non-Gaussian components, where each component distribution is a scale mixture,  $p(x|j; \theta) = \sum_{k=1}^{l} p_k N(x, y_k, x^2)$  combining Caussians with different variances (cooles)  $\sum_{k=1}^{l} q_k N(x; \mu_j, \sigma_k^2)$ , combining Gaussians with different variances (scales).

We will now derive a generalized EM algorithm for this model. (Recall that in generalized EM, we do a partial update in the M step, rather than finding the exact maximum.)

- a. Derive an expression for the responsibilities,  $P(J_n = j, K_n = k | x_n, \theta)$ , needed for the E step.
- b. Write out a full expression for the expected complete log-likelihood

$$
Q(\theta^{new}, \theta^{old}) = E_{\theta^{old}} \sum_{n=1}^{N} \log P(J_n, K_n, x_n | \theta^{new})
$$
\n(11.126)

c. Solving the M-step would require us to jointly optimize the means  $\mu_1, \ldots, \mu_m$  and the variances  $\sigma_1^2,\ldots,\sigma_l^2.$  It will turn out to be simpler to first solve for the  $\mu_j$ 's given fixed  $\sigma_j^2$ 's, and subsequently solve for  $\sigma_j^2$ 's given the new values of  $\mu_j$ 's. For brevity, we will just do the first part. Derive an expression for the maximizing  $\mu_j$ 's given fixed  $\sigma_{1:l}^2$ , i.e., solve  $\frac{\partial Q}{\partial \mu^{new}} = 0$ .

#### **Exercise 11.7** Manual calculation of the M step for a GMM

(Source: de Freitas.) In this question we consider clustering 1D data with a mixture of 2 Gaussians using the EM algorithm. You are given the 1-D data points  $x = \begin{bmatrix} 1 \\ 1 \end{bmatrix}$  20. Suppose the output of the E step is the following matrix:

$$
\mathbf{R} = \left[ \begin{array}{cc} 1 & 0 \\ 0.4 & 0.6 \\ 0 & 1 \end{array} \right] \tag{11.127}
$$

where entry  $r_{i,c}$  is the probability of obervation  $x_i$  belonging to cluster c (the responsibility of cluster c for data point *i*). You just have to compute the M step. You may state the equations for maximum likelihood estimates of these quantities (which you should know) without proof; you just have to apply the equations to this data set. You may leave your answer in fractional form. Show your work.

- a. Write down the likelihood function you are trying to optimize.
- b. After performing the M step for the mixing weights  $\pi_1, \pi_2$ , what are the new values?
- c. After performing the M step for the means  $\mu_1$  and  $\mu_2$ , what are the new values?

#### **Exercise 11.8** Moments of a mixture of Gaussians

Consider a mixture of  $K$  Gaussians

$$
p(\mathbf{x}) = \sum_{k=1}^{K} \pi_k \mathcal{N}(\mathbf{x} | \boldsymbol{\mu}_k, \boldsymbol{\Sigma}_k)
$$
\n(11.128)

a. Show that

$$
\mathbb{E}\left[\mathbf{x}\right] = \sum_{k} \pi_{k} \boldsymbol{\mu}_{k} \tag{11.129}
$$

Image /page/24/Figure/1 description: A scatter plot with a grid background. The x-axis ranges from -2 to 18, and the y-axis ranges from -1 to 5. There are two rows of asterisks. The bottom row of asterisks are located at y=0 and x values of 0, 2, 4, 6, 8, 10, 12, 14, 16, and 18. The top row of asterisks are located at y=3 and x values of 1, 3, 5, 7, 9, 11, 13, 15, and 17. Two of the asterisks in the top row, at x=9 and x=11, are circled.

**Figure 11.24** Some data points in 2d. Circles represent the initial guesses for  $m_1$  and  $m_2$ .

b. Show that

$$
cov\left[\mathbf{x}\right] = \sum_{k} \pi_{k} \left[\mathbf{\Sigma}_{k} + \boldsymbol{\mu}_{k} \boldsymbol{\mu}_{k}^{T}\right] - \mathbb{E}\left[\mathbf{x}\right] \mathbb{E}\left[\mathbf{x}\right]^{T}
$$
\n(11.130)

Hint: use the fact that  $cov[\mathbf{x}] = \mathbb{E} [\mathbf{x} \mathbf{x}^T] - \mathbb{E} [\mathbf{x}] \mathbb{E} [\mathbf{x}]^T$ .

**Exercise 11.9** K-means clustering by hand

(Source: Jaakkola.)

In Figure 11.24, we show some data points which lie on the integer grid. (Note that the x-axis has been compressed; distances should be measured using the actual grid coordinates.) Suppose we apply the Kmeans algorithm to this data, using  $K = 2$  and with the centers initialized at the two circled data points. Draw the final clusters obtained after K-means converges (show the approximate location of the new centers and group together all the points assigned to each center). Hint: think about shortest Euclidean distance.

**Exercise 11.10** Deriving the K-means cost function

Show that

$$
J_W(\mathbf{z}) = \frac{1}{2} \sum_{k=1}^{K} \sum_{i: z_i = k} \sum_{i': z_{i'} = k} (x_i - x_{i'})^2 = \sum_{k=1}^{K} n_k \sum_{i: z_i = k} (x_i - \overline{x}_k)^2
$$
(11.131)

Hint: note that, for any  $\mu$ ,

$$
\sum_{i} (x_i - \mu)^2 = \sum_{i} [(x_i - \overline{x}) - (\mu - \overline{x})]^2
$$
 (11.132)

$$
= \sum_{i} (x_i - \overline{x})^2 + \sum_{i} (\overline{x} - \mu)^2 - 2 \sum_{i} (x_i - \overline{x}) (\mu - \overline{x}) \tag{11.133}
$$

$$
= ns2 + n(\overline{x} - \mu)2
$$
 (11.134)

where  $s^2 = \frac{1}{n} \sum_{i=1}^n (x_i - \overline{x})^2$ , since

$$
\sum_{i} (x_i - \overline{x})(\mu - \overline{x}) = (\mu - \overline{x}) \left( (\sum_{i} x_i) - n\overline{x} \right) = (\mu - \overline{x})(n\overline{x} - n\overline{x}) = 0 \tag{11.135}
$$

**Exercise 11.11** Visible mixtures of Gaussians are in the exponential family

Show that the joint distribution  $p(x, z|\theta)$  for a 1d GMM can be represented in exponential family form.

Image /page/25/Figure/1 description: This is a scatter plot titled "regression with censored data". The x-axis represents "inverse temperature" and ranges from 2 to 2.4. The y-axis represents "survival time" and ranges from 2.6 to 4.6. There are two regression lines plotted: one labeled "EM" (dashed red line) and another labeled "OLS" (solid blue line). The data points are represented by black circles. Red crosses indicate censored data, and green asterisks indicate predicted values. The plot shows a positive correlation between inverse temperature and survival time, with both regression lines generally following this trend. The EM line appears to be slightly above the OLS line for most of the data points.

**Figure 11.25** Example of censored linear regression. Black circles are observed training points, red crosses are observed but censored training points. Green stars are predicted values of the censored training points. We also show the lines fit by least squares (ignoring censoring) and by EM. Based on Figure 5.6 of (Tanner 1996). Figure generated by linregCensoredSchmeeHahnDemo, written by Hannes Bretschneider.

**Exercise 11.12** EM for robust linear regression with a Student t likelihood

Consider a model of the form

$$
p(y_i|\mathbf{x}_i, \mathbf{w}, \sigma^2, \nu) = \mathcal{T}(y_i|\mathbf{w}^T \mathbf{x}_i, \sigma^2, \nu)
$$
\n(11.136)

Derive an EM algorithm to compute the MLE for **w**. You may assume  $\nu$  and  $\sigma^2$  are fixed, for simplicity. Hint: see Section 11.4.5.

#### **Exercise 11.13** EM for EB estimation of Gaussian shrinkage model

Extend the results of Section 5.6.2.2 to the case where the  $\sigma_j^2$  are not equal (but are known). Hint: treat the  $\theta_j$  as hidden variables, and then to integrate them out in the E step, and maximize  $\eta = (\mu, \tau^2)$  in the M step.

### **Exercise 11.14** EM for censored linear regression

**Censored regression** refers to the case where one knows the outcome is at least (or at most) a certain value, but the precise value is unknown. This arises in many different settings. For example, suppose one is trying to learn a model that can predict how long a program will take to run, for different settings of its parameters. One may abort certain runs if they seem to be taking too long; the resulting run times are said to be **right censored**. For such runs, all we know is that  $y_i \geq c_i$ , where  $c_i$  is the censoring time, that is,  $y_i = \min(z_i, c_i)$ , where  $z_i$  is the true running time and  $y_i$  is the observed running time. We can also define **left censored** and **interval censored** models.<sup>3</sup> Derive an EM algorithm for fitting a linear regression model to right-censored data. Hint: use the results from Exercise 11.15. See Figure 11.25 for an example, based on the data from (Schmee and Hahn 1979). We notice that the EM line is tilted upwards more, since the model takes into account the fact that the truncated values are actually higher than the observed values.

<sup>3.</sup> There is a closely related model in econometrics called the **Tobit model**, in which  $y_i = \max(z_i, 0)$ , so we only get to observe positive outcomes. An example of this is when  $z_i$  represents "desired investment", and  $y_i$  is actual investment. Probit regression (Section 9.4) is another example.

### **Exercise 11.15** Posterior mean and variance of a truncated Gaussian

Let  $z_i = \mu_i + \sigma \epsilon_i$ , where  $\epsilon_i \sim \mathcal{N}(0, 1)$ . Sometimes, such as in probit regression or censored regression, we do not observe  $z_i$ , but we observe the fact that it is above some threshold, namely we observe the event  $E = \mathbb{I}(z_i \ge c_i) = \mathbb{I}(\epsilon_i \ge \frac{c_i - \mu_i}{\sigma})$ . (See Exercise 11.14 for details on censored regression, and Section 11.4.6 for probit regression.) Show that

$$
\mathbb{E}\left[z_i\middle|z_i\geq c_i\right] = \mu_i + \sigma H\left(\frac{c_i - \mu_i}{\sigma}\right) \tag{11.137}
$$

and

$$
\mathbb{E}\left[z_i^2|z_i\geq c_i\right] = \mu_i^2 + \sigma^2 + \sigma(c_i + \mu_i)H\left(\frac{c_i - \mu_i}{\sigma}\right)
$$
\n(11.138)

where we have defined

$$
H(u) \triangleq \frac{\phi(u)}{1 - \Phi(u)}\tag{11.139}
$$

and where  $\phi(u)$  is the pdf of a standard Gaussian, and  $\Phi(u)$  is its cdf.

Hint 1: we have  $p(\epsilon_i|E) = \frac{p(\epsilon_i,E)}{p(E)}$ , where E is some event of interest. Hint 2: It can be shown that

$$
\frac{d}{dw}\mathcal{N}(w|0,1) = -w\mathcal{N}(w|0,1)
$$
\n(11.140)

and hence

$$
\int_{b}^{c} w \mathcal{N}(w|0,1) = \mathcal{N}(b|0,1) - \mathcal{N}(c|0,1)
$$
\n(11.141)
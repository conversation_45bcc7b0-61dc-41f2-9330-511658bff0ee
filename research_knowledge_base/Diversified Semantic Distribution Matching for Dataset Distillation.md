Image /page/0/Picture/0 description: A button with a circular icon and text that says "Check for updates". The icon is a circle with a red bookmark in the center, and segments of yellow and blue on either side of the bookmark.

# Diversified Semantic Distribution Matching for Dataset Distillation

[Hongcheng Li](https://orcid.org/0009-0005-7847-3806)

Institute of Information Engineering, Chinese Academy of Sciences School of Cyber Security, University of Chinese Academy of Sciences Key Laboratory of Cyberspace Security Defense Beijing, China <EMAIL>

[<PERSON><PERSON>](https://orcid.org/0000-0002-1316-9118)<sup>∗</sup> Institute of Information Engineering, Chinese Academy of Sciences Key Laboratory of Cyberspace Security Defense Beijing, China <EMAIL>

## <PERSON><PERSON>u

Institute of Information Engineering, Chinese Academy of Sciences School of Cyber Security, University of Chinese Academy of Sciences Key Laboratory of Cyberspace Security Defense Beijing, China <EMAIL>

[<PERSON>](https://orcid.org/0000-0001-6709-0942)

Institute of Information Engineering, Chinese Academy of Sciences Key Laboratory of Cyberspace Security Defense Beijing, China <EMAIL>

## <PERSON><PERSON> Wang

Institute of Information Engineering, Chinese Academy of Sciences Beijing, China <EMAIL>

# ABSTRACT

Dataset distillation, also known as dataset condensation, offers a possibility for compressing a large-scale dataset into a small-scale one (i.e., distilled dataset) while achieving similar performance during model training. This method effectively tackles the challenges of training efficiency and storage cost posed by the large-scale dataset. Existing dataset distillation methods can be categorized into Optimization-Oriented (OO)-based and Distribution-Matching (DM)-based methods. Since OO-based methods require bi-level optimization to alternately optimize the model and the distilled data, they face challenges due to high computational overhead in practical applications. Thus, DM-based methods have emerged as an alternative by aligning the prototypes of the distilled data to those of the original data. Although efficient, these methods overlook the diversity of the distilled data, which will limit the performance of evaluation tasks. In this paper, we propose a novel Diversified Semantic Distribution Matching (DSDM) approach for dataset distillation. To accurately capture semantic features, we first pre-train models for dataset distillation. Subsequently, we estimate the distribution of each category by calculating its prototype and covariance matrix, where the covariance matrix indicates the direction of semantic feature transformations for each category. Then, in addition to the prototypes, the covariance matrices are also matched to obtain more diversity for the distilled data. However, since the distilled

<sup>∗</sup> indicates corresponding author. † indicates corresponding author.

Image /page/0/Picture/14 description: The image is a graphic representing a Creative Commons license. It features a gray rectangle with a black border. On the left side of the rectangle, there is a white circle with the letters "cc" inside, also in white. To the right of the "cc" symbol, there is another white circle containing a black silhouette of a person. Below these symbols, a black bar runs across the bottom of the gray rectangle, with the white letters "BY" centered within it.

[This work is licensed under a Creative Commons Attribution](https://creativecommons.org/licenses/by/4.0/) [International 4.0 License.](https://creativecommons.org/licenses/by/4.0/)

MM '24, October 28-November 1, 2024, Melbourne, VIC, Australia © 2024 Copyright held by the owner/author(s). ACM ISBN 979-8-4007-0686-8/24/10 <https://doi.org/10.1145/3664647.3680900>

data are optimized by multiple pre-trained models, the training process will fluctuate severely. Therefore, we match the distilled data of the current pre-trained model with the historical integrated prototypes. Experimental results demonstrate that our DSDM achieves state-of-the-art results on both image and speech datasets. Code is available at https://github.com/Li-Hongcheng/DSDM.

## CCS CONCEPTS

• Computing methodologies  $\rightarrow$  Artificial intelligence.

## KEYWORDS

Dataset Distillation, Prototype Learning, Semantic Feature

## ACM Reference Format:

Hongcheng Li, Yucan Zhou, Xiaoyan Gu, Bo Li, and Weiping Wang. 2024. Diversified Semantic Distribution Matching for Dataset Distillation. In Proceedings of the 32nd ACM International Conference on Multimedia (MM '24), October 28-November 1, 2024, Melbourne, VIC, Australia ACM, New York, NY, USA, [9](#page-8-0) pages.<https://doi.org/10.1145/3664647.3680900>

# 1 INTRODUCTION

In the era of big data, the volume of multimedia data grows exponentially, which brings great challenges to storage and training efficiency [\[13,](#page-8-1) [33\]](#page-8-2). For instance, the multi-modal CLIP was trained on an impressive 400 million data, consuming thousands of GPU days [\[18,](#page-8-3) [23,](#page-8-4) [31\]](#page-8-5). This poses an interesting issue: how to summarize the large-scale multimedia data into a compact but insightful subset to facilitate the training procedure while maintaining similar performance. To achieve this purpose, an intuitive approach is to select representative instances from the original dataset, known as coreset selection [\[4,](#page-8-6) [9,](#page-8-7) [24,](#page-8-8) [26,](#page-8-9) [32\]](#page-8-10). However, sample selection involves directly discarding a significant portion of instances from the original dataset, leading to the loss of crucial underlying information essential for model training. Fortunately, Dataset Distillation (DD)

MM '24, October 28-November 1, 2024, Melbourne, VIC, Australia Hongcheng Li, Yucan Zhou, Xiaoyan Gu, Bo Li, and Weiping Wang

<span id="page-1-0"></span>Image /page/1/Figure/2 description: The image illustrates a process involving deep networks and semantic transformation for data analysis. The top section shows original data being processed by deep networks, resulting in a feature distribution. This distribution is then subjected to prototype matching, where the prototype of the target data (μT) is matched with the prototype of the source data (μS). The bottom section begins with a covariance matrix, which is then transformed semantically. This transformation involves a starting image (likely a horse) with a red star representing the prototype, and arrows labeled 'Color', 'Posture', and 'Perspective' pointing to different images of horses, indicating a change in these attributes. Below this, a legend clarifies symbols: a circle for feature space, a red star for the prototype of original data, plus signs for features of original data, and red circles for features of distilled data. The process concludes with diversity matching, where the distribution of target data (N(μT, CovT)) is matched with the distribution of source data (N(μS, CovS)), shown as a distribution of blue plus signs with three red circles representing distilled data features.

Figure 1: An illustration of diversified semantic dataset distillation. Unlike previous methods match prototypes of the original and distilled dataset ( $\mu_{\mathcal{T}} \sim \mu_{\mathcal{S}}$ ), our method matches Gaussian distributions ( $N(\mu_T, Cov_T) \sim N(\mu_S, Cov_S)$ ).

has been proposed as a solution to condense the original large-scale dataset into a smaller one (i.e., distilled dataset), while ensuring that models trained on the distilled dataset exhibit comparable performance to those trained on the original dataset [\[6,](#page-8-11) [17,](#page-8-12) [30\]](#page-8-13).

Existing methods for dataset distillation can be generally categorized into two main groups: Optimization-Oriented (OO)-based methods and Distribution-Matching (DM)-based methods [\[36,](#page-8-14) [42\]](#page-8-15). OO-based methods employ a bi-level optimization process [\[2,](#page-8-16) [8,](#page-8-17) [10,](#page-8-18) [41\]](#page-8-19), where the model is first updated with the original dataset, and then, the distilled dataset is optimized by minimizing the distance between the original and distilled data. Although OO-based methods have demonstrated good performance, they are often criticized for their significant time consumption during training. In contrast, DM-based methods are proposed to improve the training efficiency by eliminating model updates [\[29,](#page-8-20) [38\]](#page-8-21). Specifically, DM [\[40\]](#page-8-22) optimizes the the distilled data by matching corresponding prototypes calculated with a random initialized model on the distilled and original datasets. However, the embeddings generated by a randomly initialized model tend to be inaccurate, which can impact the performance of the distilled dataset. To tackle this issue, IDM [\[42\]](#page-8-15) utilizes a sequence of pre-trained models to obtain accurate prototypes. Although these prototypes are more accurate, they ignore the diversity of the distilled dataset, which will reduce the generalization of the models trained on it.

As illustrated in Figure [1,](#page-1-0) when only prototypes of the distilled data and the original data are aligned to optimize the distilled data, the algorithm may lazily minimize the alignment loss by distilling instances near the center of each class into the distilled data. As a result, all the distilled instances contain similar information, which overlooks the diverse and meaningful semantic information in the original data. Besides the prototype, the covariance matrix is another important statistic to depict the variations of a category, where each item in the covariance matrix reflects the transformation from one-dimensional semantic feature to another. As shown in Figure [1,](#page-1-0) suppose the "purple, orange, blue" in the covariance

matrix represent the transformation from standing to "color transformation, posture transformation, and perspective transformation", applying these transformations to the a white, frontal and standing horse can obtain horses of "brown, lying, and running". Therefore, the covariance matrix carries rich semantic diversity information. Inspired by this, we can improve the diversity of the distilled data by aligning their covariance matrix to that of the original data to make the distilled and original data distribute similarly.

In this paper, we propose a simple yet efficient Diversified Semantic Distribution Matching (DSDM) method for dataset distillation, aiming to obtain semantic diversified distilled data. To acquire accurate semantic information from the original data, we first pre-train a set of models on the original dataset. Then, each category in the original dataset is modeled into a Gaussian distribution with the semantic embedding extracted by a pre-trained model, where the prototype represents the intrinsic feature of a category, and the covariance matrix represents the semantic variations of instances in this category. Subsequently, we align the prototype and covariance matrix of the corresponding distilled data to make the distribution of the distilled data similar to that of the original data. With the additional alignment of the covariance matrix, our distilled data will be explicitly required to be distributed dispersedly in the feature space of the original data, ensuring the performance of the distilled data in the evaluation tasks. In addition, since the distilled data are optimized by multiple pre-trained models to make them generalize well to different models, the training process will fluctuate severely. Therefore, we introduce a memory bank to store the integrated prototypes of distilled data optimized by previous pre-trained models. Finally, the distilled data of the current pre-trained model are aligned with the historical integrated prototypes to ensure that the distilled data generalize well across various models. Empirical evaluations conducted on five image datasets and a speech dataset demonstrate that our method not only preserves the efficiency of DM-based approaches but also exhibits significant performance improvements.

The contributions of this paper are summarized as follows:

- We introduce a novel approach called Diversified Semantic Distribution Matching (DSDM) for dataset distillation. To the best of our knowledge, this is the first work to align features from a semantic distribution perspective, thereby enhancing the semantic diversity of distilled dataset.
- We analyze the disparities in feature space distributions among different models. To achieve stable optimization of distillation, we align the prototype of the current distilled data with the historically optimized distilled data.
- We conduct a series of experiments to thoroughly validate the effectiveness of DSDM on both image and speech datasets. Through rigorous experimentation, our DSDM has demonstrated state-of-the-art performance with robust generalization capabilities across various scenarios.

# 2 RELATED WORK

## 2.1 Coreset Selection

The classical approach to compressing the original dataset is coreset selection or instance selection. In addition to random sampling from the original dataset, most existing strategies progressively select important data points based on some heuristic selection criteria. For example, Herding [\[5,](#page-8-23) [32\]](#page-8-10) minimizes the distance between the feature space's center of the chosen subset and that of the original dataset by greedily incorporating one instance at a time during each iteration. K-Center [\[9,](#page-8-7) [26\]](#page-8-9) selects data points that minimize the maximum distance between a data point and its nearest center. Gradient-Greedy [\[1\]](#page-8-24) employs gradients as features to maximize the diversity of instances in the replay. However, these heuristic selection criteria do not guarantee that the selected subset is optimal for training the model, particularly in the deep neural networks.

# 2.2 Dataset Distillation

Existing Dataset Distillation methods can be classified into two types: Optimization-Oriented (OO)-based and Distribution Matching (DM)-based methods [\[36,](#page-8-14) [42\]](#page-8-15).

Optimization-Oriented (OO)-based methods integrate information learned from the dataset into the synthesized dataset during model training, necessitating iterative bi-level optimization of both the model and distilled dataset. Wang et al. are the first to introduce the concept of dataset distillation from an optimization perspective, updating synthesized instances through meta-learning [\[30\]](#page-8-13). DC utilizes this bi-level meta-learning optimization approach to match gradients of the distilled dataset and the original dataset to simulate the training process [\[41\]](#page-8-19). DSA further improves data efficiency by introducing differentiable Siamese augmentation, enabling effective training of neural networks with augmented data [\[39\]](#page-8-25). IDC injects a differentiable multi-formation function into synthesized instances within the constraint of fixed distilled dataset storage [\[14\]](#page-8-26). Along these lines, various approaches have been proposed to generate distilled instances, including matching the training trajectories [\[2\]](#page-8-16), using data hallucination networks to construct sample features [\[27\]](#page-8-27), and selecting original sample features via clustering [\[19\]](#page-8-28). Although these methods have improved performance of the distilled data, they still follow the bi-level optimization paradigm to update the model and the distilled data alternately, which is difficult to be optimized and time-consuming.

Distribution-Matching (DM)-based methods directly align the distribution of distilled dataset with that of the real dataset in the feature space [\[29,](#page-8-20) [40\]](#page-8-22). They avoid the bi-level optimization by leaving out the updating of the model. DM [\[40\]](#page-8-22) is proposed to match the average feature representations of the original dataset with those of the distilled dataset. However, this approach ignores the discriminative nature of the classes. To address this limitation, CAFE [\[29\]](#page-8-20) employs a discriminative loss that utilizes the prototypes of the distilled instances as classifiers for the real instances. However, the embeddings to calculate the prototypes are extracted with a randomly initialized model, which are usually inaccurate and will affect the performance of distillation. To address this issue, IDM [\[42\]](#page-8-15) introduces a queue of pre-trained models to find more accurate class centers. In addition, M3D [\[36\]](#page-8-14) embeds the class centers into the Reproducing Kernel Hilbert space, which aligns the distributions more efficiently. We recognize that only aligning class centers while overlooking the semantic diversity in the original dataset will produce oversimplified distilled data, which are similar to the class centers. Therefore, our method considers semantic information to enhance the diversity of distilled data.

## 3 PRELIMINARY

Dataset distillation is to condense a large-scale training dataset  $\mathcal{T} =$  $\{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  into a small-scale distilled dataset  $\mathcal{S} = \big\{ (s_j, y_j) \big\}_{j=1}^{|\mathcal{S}|}$ where models trained on S resemble those trained on  $\mathcal T$ . Typically, the distilled dataset  $S$  is initialized with randomly selected original instances from  $T$  or random noise. Then,  $S$  is optimized by minimizing the information loss between the distilled and the original examples, which can be formulated as:

$$
S = \arg\min_{S} D(\phi(\mathcal{T}), \phi(S)), \tag{1}
$$

where  $D$  represents a distance metric such as Mean Square Error (MSE), and  $\phi$  denotes the matching objective. As mentioned above, based on whether to perform a costly bi-level optimization, existing methods can be mainly divided into Optimization-Oriented (OO) based and Distribution-Matching (DM)-based methods [\[35,](#page-8-29) [36\]](#page-8-14).

In OO-based methods,  $\phi$  typically represents the gradients. In each iteration, to simulate different backbones, the parameters of a model are initialized to  $\boldsymbol{\theta}^{(0)}$  . Then, in the outer loop,  $\boldsymbol{\theta}^{(0)}$  is updated for  $\hat{N}$  steps using  $\mathcal T$  to generate gradients through a training procedure. Next, in the inner loop,  $S$  is optimized by minimizing the gradient matching losses between the original and distilled data. The loss function can be defined as follows:

$$
S = \arg\min_{S} \mathbb{E}_{\theta^{(0)} \sim P_{\theta}} \left[ \sum_{i=0}^{\hat{N}} D\left(\nabla l\left(S; \theta^{(i)}\right), \nabla l\left(\mathcal{T}; \theta^{(i)}\right)\right) \right],
$$
\n(2)

\nwith

\n
$$
\theta^{(i)} = \theta^{(i-1)} - \eta \nabla l\left(\mathcal{T}; \theta^{(i-1)}\right), \quad i \geq 1,
$$

where  $P_{\theta}$  is the distribution for the initialization of network parameters,  $l$  is the empirical cross-entropy loss and  $\eta$  is the learning rate for updating the network.

Although OO-based methods achieve superior performance, these methods incur significant time overhead due to their bi-level loops. To mitigate this challenge, DM-based methods aim to generate distilled data to approximate the original data by directly matching the class centers of the distilled dataset with those of the original dataset [\[40\]](#page-8-22). This objective can be formulated as:

$$
S = \arg\min_{S} E_{\theta \sim P_{\theta}} [D(g(\theta_F; S), g(\theta_F; \mathcal{T}))], \tag{3}
$$

where  $\theta_F$  denotes the feature sub-network parameterized by  $\theta$ , instantiated by the model without the output layer and the function g represents the embedding function parameterized with  $\theta_F$ .

## 4 METHOD

## 4.1 Overview

We illustrate the framework of our proposed Diversified Semantic Distribution Matching (DSDM) in Figure [2.](#page-3-0) To obtain accurate instance embeddings, we train multiple models using the original dataset. For the  $t$ -th iteration, we initialize a feature sub-network  $\theta_F^{(t)}$  instantiated by randomly selecting one of these pre-trained models. Then, we derive instance embeddings from both  $\mathcal T$  and S. The extracted features are then used to compute prototypes  $\mu_S$ and  $\mu_{\mathcal{T}}$ , as well as covariance matries  $Cov_{S}$  and  $Cov_{T}$  for each category in both  $\mathcal T$  and  $\mathcal S$ . The prototypes encapsulate inherent

<span id="page-3-0"></span>Image /page/3/Figure/2 description: This is a flowchart illustrating a method for Diversified Semantic Distribution Matching. The process begins with Initialization, where a Selected Model is chosen from Pre-trained Models. The core of the method involves a Feature Extractor that processes Original Data (T) and Distilled Data (S). This leads to the Diversified Semantic Distribution Matching stage, which compares prototypes and embeddings of both original and distilled data, calculating losses like Lproto, Lh, and LSD. The process also includes Memory Historical Integrated Prototypes, where optimized data is processed to generate historical prototypes (h(t)) which are stored and updated in a Memory Bank. The flowchart includes a legend explaining symbols for decision boundaries, matching, backpropagation, data streams, prototypes, embeddings, and feature spaces.

Figure 2: The illustration of our proposed DSDM. First, DSDM instantiates a feature extractor  $\theta_F^{(t)}$  by randomly selecting a model from the pre-trained models. Then, DSDM utilizes three key components to optimize the distilled data: prototype alignment ( $\mathcal{L}_{proto}$ ), semantic diversity alignment ( $\mathcal{L}_{SD}$ ), and historical model alignment ( $\mathcal{L}_h$ ). After the t-th iteration of optimization,  $\hat{\mathbf{D}}$ DSDM updates and stores the historically optimized prototypes  $\bm{h}^{(t)}$  in the memory bank.

features, while the covariance matrices capture the semantic diversity of all features within each category. We then match the prototypes and covariance matries for each category. However, the feature distributions of  $S$  from different pre-trained models can exhibit significant discrepancies during the iteration process, as elaborated in Section [4.3.](#page-4-0) To mitigate the instability stemming from these differences, we align the historical prototypes of distilled data  $h^{(t-1)}$  with the current distilled data  $\mu_{\mathcal{S}}$ . To preserve the historical prototypes for each pre-trained model, we establish a memory bank to memory historical integrated prototypes. Finally, we summarize the total loss to optimize distilled instances.

## 4.2 Diversified Semantic Distribution Matching

In the deep neural networks, disparate instances belonging to the same category often undergo nuanced semantic transformations within their deeper feature representations [\[3,](#page-8-30) [28,](#page-8-31) [37\]](#page-8-32). Given that covariance matrices encapsulate both the interrelationships and variances among features, they provide a rich understanding of semantic characteristics. We argue that leveraging category-specific covariance matrices enables the representation of features with distinctive semantic transformations. Thus, we derive semantic directions from estimated covariance matrices, assuming a class-centered normal distribution. Our objective simplifies to matching covariance matrices of features generated for each category  $S$  to closely approximate those of  $\mathcal{T}$  (i.e.,  $\mathcal{N}(\mu_S, Cov_S) \sim \mathcal{N}(\mu_T, Cov_T)$ ).

Previous DM [\[40\]](#page-8-22) utilizes initialized neural networks to measure the Maximum Mean Discrepancy (MMD) between the original dataset  $\mathcal T$  and the distilled dataset  $\mathcal S$ . However, aligning the embeddings with randomly initialized neural networks will lead to inaccurate embeddings, as neural networks are ignorant to the current task. Only accurate embeddings can capture effective semantic features. To mitigate this issue, we adopt a strategy of pre-training several models using  $\mathcal T$ . At each iteration t, we randomly select one of these pre-trained models and utilize its last layer (excluding the output layer) as the feature extractor  $\theta_F^{(t)}$ .

For a large-scale original dataset  $\mathcal{T} = \{x_i, y_i\}_{i=1}^{|\mathcal{T}|}$ , where  $y_i \in L$  $\{l_1, l_2, ..., l_C\}$  and C represents the number of categories, we compress IPC (Instances Per Class) instances for each category  $l_c$  (i.e., for the distilled synthetic dataset  $\mathcal{S} = \big\{ (s_j, y_j) \big\}^{\vert \mathcal{S} \vert}_{j=1}$  ,  $\vert \mathcal{S} \vert = \textit{IPC} \times \textit{C}$ ).  $\mathcal{S}$  is usually initialized with instances randomly selected instances from the original dataset. To effectively capture the semantic features of the original dataset  $\mathcal{T}$ , we use  $\theta_F^{(t)}$  to get instance embeddings. We employ the embedding function  $g$  to generate embeddings for each instance in both the distilled and original datasets, denoted by  $f_i = g(\theta_F^{(t)}; x_i)$  and  $\hat{f}_j = g(\theta_F^{(t)}; s_j)$ , where  $f_i, \hat{f}_j \in R^d$ .

Given the inherent differences in the direction of semantic features across classes, we should first align the inherent semantic features of each class between  $\mathcal T$  and  $\mathcal S$ . Thus, we utilize the euclidean distance to align the prototype of  $\mathcal{T}$ , denoted as  $\mu_{\mathcal{T},c}$ , with the prototype of S, denoted as  $\mu_{S,c}$ . The loss function for this

<span id="page-4-1"></span>Image /page/4/Figure/1 description: The image displays two plots side-by-side. Plot (a), titled "Feature Distribution", is a scatter plot showing the distribution of features for six different models. Each model's data points are clustered together and colored differently: Model 1 is light blue, Model 2 is red, Model 3 is orange, Model 4 is dark purple, Model 5 is gray, and Model 6 is dark green. Plot (b), titled "Feature Matching Loss", is a bar chart illustrating the feature matching loss for the same six models. The x-axis is labeled "Model" and ranges from 1 to 6. The y-axis represents the loss, ranging from 5.0 to 8.0. The bar heights and corresponding loss values are: Model 1 (blue) - 6.22, Model 2 (red) - 7.34, Model 3 (orange) - 6.7, Model 4 (dark purple) - 6.31, Model 5 (gray) - 7.12, and Model 6 (dark green) - 5.97.

#### Figure 3: Illustration of feature distributions and matching loss on the same distilled data under six pre-trained models.

prototype alignment is defined as follows:

<span id="page-4-2"></span>
$$
Lproto=∑c=1C‖μT,c-μS,c‖2s.t.μT,c=1|BcT|∑i=1|BcT|fi,μS,c=1|BcS|∑j=1|BcS|f^j(4)
$$

where  $B_c^{\mathcal{T}},$   $B_c^{\mathcal{S}}$  represent a batch of data sampled from the same category  $l_c$  in  $\mathcal T$  and  $\mathcal S$ .

When aligning the prototypes, we let the covariance matrices align to capture semantic diversity. The objective of semantic diversity alignment can be expressed as follows:

<span id="page-4-3"></span>
$$
$\mathcal{L}_{SD} = \sum_{c=1}^{C} \frac{1}{d} \left\| \mathbf{Cov}_{{\mathcal{T}},c} - \mathbf{Cov}_{S,c} \right\|^2$
$$
  
s.t. 
$$
$\mathbf{Cov}_{{\mathcal{T}},c}(m,n) = \frac{\sum_{i=1}^{|B_c^{\mathcal{T}}|} \left( f_i^m - \mu_{{\mathcal{T}},c}^m \right) \left( f_i^n - \mu_{{\mathcal{T}},c}^n \right)}{|B_c^{\mathcal{T}}|} , (5)$
$$
$$
$\mathbf{Cov}_{S,c}(m,n) = \frac{\sum_{j=1}^{|B_c^S|} \left( f_j^m - \mu_{S,c}^m \right) \left( f_j^n - \mu_{S,c}^n \right)}{|B_c^S|}$
$$

where  $(m, n)$  denotes the position of the covariance matrix.

<span id="page-4-0"></span>

## 4.3 Align Historical Prototypes

To enhance model generalization of distilled data across diverse evaluation tasks, we adopt multiple pre-trained models to capture various semantic features. However, notable differences emerge in the feature distributions of distilled data for the same class across different pre-trained models. In Figure [3](#page-4-1) (a), we visualize the feature distributions of the distilled data from a category across six pretraining models. It is obvious that data distilled by different pretrained models are distributed differently. Figure [3](#page-4-1) (b) demonstrates the matching loss between the distilled data and the original data under these six pre-trained models. We can see that the distance between the same distilled data and the same original data varies greatly among different pre-trained models, resulting that training loss for data distillation fluctuations severely when the model for feature extraction changes.

To enhance the stability of distillation performance over multiple pre-trained models, we introduce a memory bank to store

<span id="page-4-5"></span>

| <b>Input:</b> $\mathcal{T}$ : original training dataset; $\mathcal{S}$ : distilled synthetic dataset; $T$ : number of training iterations; $C$ : number of class; $\eta_{DSDM}$ : learning rate of optimizing distilled data; $M$ : number of pre-trained networks. |                                                                                                                                          |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------|
| 1                                                                                                                                                                                                                                                                   | Randomly train $M$ models $\{\theta_m\}_{m=1}^M$ with $\mathcal{T};$                                                                     |
| 2                                                                                                                                                                                                                                                                   | Initialize distilled dataset $S$ ;                                                                                                       |
| 3                                                                                                                                                                                                                                                                   | for $t = 1 \leftarrow$ to $T$ do                                                                                                         |
| 4                                                                                                                                                                                                                                                                   | Randomly load a checkpoint from the pre-trained models to instantiate a feature extractor $\theta_{\rm F}^{(t)}$ .                       |
| 5                                                                                                                                                                                                                                                                   | Sample an intra-class mini-batch $B_c^{\mathcal{T}} \sim \mathcal{T}, B_c^{\mathcal{S}} \sim \mathcal{S}$ for each category $c$ in $C$ ; |

- 6 Compute prototype matching loss  $\mathcal{L}_{proto}$  with Eq.[\(4\)](#page-4-2).
- 7 Compute semantic diversity matching loss  $\mathcal{L}_{SD}$  with  $Eq.(5)$  $Eq.(5)$ .
- 8 Compute historical prototype alignment loss  $\mathcal{L}_h$  with  $Eq.(6)$  $Eq.(6)$ .
- 9 Compute the total loss  $\mathcal{L}_{total}$  with Eq.[\(9\)](#page-5-0).
- 10 Update distilled data  $S: S \leftarrow S \eta_{DSDM} \nabla_S \mathcal{L}_{total}$ 11 Compute and update the historical prototype of the optimized data  $h_c^{(t)}$  for each category  $c$ .
- 12 Store  $h^{(t)} = {h_c^{(t)}}_{c=1}^C$  into the memory bank. Output: Distilled synthetic dataset S.

the integrated prototypes of distilled data optimized by previous pre-trained models. Then, we align the distilled data of the current model with the corresponding integrated prototype  $h_c^{(t-1)}$ . Thus, we compute the historical prototype alignment loss as:

<span id="page-4-4"></span>
$$
\mathcal{L}_h = \sum_{c=1}^C \|\mu_{S,c} - \bm{h}_c^{(t-1)}\|^2.
$$
 (6)

To store and update the prototypes used for historical prototype alignment, we first compute prototypes  $h_c^{(t)}$  for each class after the *t*-th optimization with the corresponding feature extractor  $\theta_F^{(t)}$  on the optimized distilled data  $\tilde{\mathcal{S}}$ :

$$
\boldsymbol{h}_c^{(t)} = \frac{1}{|\tilde{\mathcal{S}}_c|} \sum_{\tilde{s}_k \in \tilde{\mathcal{S}}_c} g(\boldsymbol{\theta}_F^{(t)}; \tilde{s}_k),\tag{7}
$$

where  $\tilde{\mathcal{S}_c}$  represents the distilled data of category  $l_c$  after the  $t$ th optimization. Next, we update the integrated prototypes in the memory bank:

$$
\boldsymbol{h}_c^{(t)} = \begin{cases} \alpha \boldsymbol{h}_c^{(t-1)} + (1-\alpha) \boldsymbol{h}_c^{(t)}, & t > 1 \\ 0, & t = 1, \end{cases} \tag{8}
$$

where  $\alpha$  is a smoothing factor, typically set to be 0.99.

## 4.4 Overall Loss and Training Algorithm

In a nutshell, we minimize the sum of the above losses, including the prototype alignment loss  $\mathcal{L}_{proto}$ , semantic diversity alignment loss  $\mathcal{L}_{SD}$ , and the historical prototype alignment loss  $\mathcal{L}_h$ . Combining these loss terms together, our diversified semantic distribution

MM '24, October 28-November 1, 2024, Melbourne, VIC, Australia

MM '24, October 28-November 1, 2024, Melbourne, VIC, Australia Hongcheng Li, Yucan Zhou, Xiaoyan Gu, Bo Li, and Weiping Wang

matching loss can be formulated as:

<span id="page-5-0"></span>
$$
\mathcal{L}_{total} = \mathcal{L}_{proto} + \lambda_1 \mathcal{L}_{SD} + \lambda_2 \mathcal{L}_h, \tag{9}
$$

where  $\lambda_1$  and  $\lambda_2$  are hyperparameters for balancing these terms.

The pseudocode for DSDM is provided in Algorithm [1.](#page-4-5) In addition, we adopt partitioning and expansion augmentation for the initialization of  $S$ , as proposed in IDM [\[42\]](#page-8-15), aims to increase the number of representations extracted from  $S$  without incurring additional storage costs. Specifically, employing a factor parameter  $l$ , each distilled instance is factorized into  $l \times l$  mini-examples, which are then up-sampled to their original size during training. This approach effectively maximizes the utilization of storage space in S. We integrate the same augmentation technique into our work, thereby further leveraging its advantages in aligning distributions for enhanced diversity.

# 5 EXPERIMENTS

## 5.1 Experimental Setup

Datasets. To evaluate the effectiveness of our dataset distillation method, we conduct experiments on five image datasets and one speech dataset. The five image datasets are MNIST [\[16\]](#page-8-33), Fashion-MNIST (FMNIST) [\[34\]](#page-8-34), SVHN [\[20\]](#page-8-35), CIFAR-10 [\[15\]](#page-8-36), and CIFAR-100 [\[15\]](#page-8-36). Specifically, MNIST is a black and white dataset consisting of 60,000 training images and 10,000 test images from 10 different classes. SVHN is a color dataset and consists of 73,257 digits and 26,032 digits for training and testing respectively. CIFAR-10/100 consists of 50,000 training images and 10,000 test images from 10 and 100 different classes respectively. For the speech dataset, we use the Mini-Speech-Commands dataset [\[7\]](#page-8-37), consisting of 8000 one-second audio clips representing eight command categories.

Implementation Details. We follow the configuration of DM [\[40\]](#page-8-22) to implement our method. Specifically, we use a 3-layer convolutional network (ConvNet-3) and a 4-layer convolutional network (ConvNet-4) [\[25\]](#page-8-38) for the image and speech datasets, respectively. We initialize  $M = 10$  pre-trained models, each trained for  $E = 20$ epochs. We execute  $T = 10K$  matching optimization iterations. For the weight parameters, we set  $\lambda_1 = 50$  and  $\lambda_2 = 0.2$  (see section [5.5](#page-5-1)) for further analysis). Moreover, we set the optimization learning rate for distilled instances  $\eta_{DSDM} = 0.1$ . Following IDM [\[42\]](#page-8-15), we fix the factor parameter  $l$  to 2 across all datasets. We use Top-1 test accuracy on the test set of the original dataset as the evaluation metric to assess the performance of a network trained on the distilled dataset. The evaluation task is trained for 1500 epochs. We perform 5 experiments and report the mean and standard deviation of the results.

Compared Methods. We compare our DSDM with several state-of-the-art baselines across three categories: Coreset Selection, OO-based methods, and DM-based methods. For coreset selection methods, we consider the following: Random [\[4,](#page-8-6) [24\]](#page-8-8), Herding [\[32\]](#page-8-10) and K-Center [\[9,](#page-8-7) [26\]](#page-8-9). For OO-based methods, we consider the following: Dataset Distillation (DD) [\[30\]](#page-8-13), Dataset Condensation (DC) [\[41\]](#page-8-19), DSA [\[39\]](#page-8-25) and MTT [\[2\]](#page-8-16). Although recent OO-based methods [\[19,](#page-8-28) [21,](#page-8-39) [22\]](#page-8-40) achieve better performance, we do not compare them due to the remarkable difference in training scheme and cost. For DM-based methods, we consider the following: CAFE [\[29\]](#page-8-20), its variant CAFE+DSA [\[29\]](#page-8-20), DM [\[40\]](#page-8-22), IDM [\[42\]](#page-8-15), and M3D [\[36\]](#page-8-14).

## 5.2 Results on Image Datasets

The performance of our DSDM and that of our competitors on the five image datasets is shown in Table [1.](#page-6-0) From these results we can draw the following conclusions: Firstly, our DSDM consistently outperforms current state-of-the-art techniques, especially when the IPC is set to 10 and 50. In particular, our method shows significant improvements over the suboptimal DM-based method, M3D, by 3.0% and 5.9% on CIFAR-10, respectively. Secondly, using the original dataset (Whole) for training represents the upper bound performance for dataset distillation. Encouragingly, we observe a marginal degradation of only 4.1% at a condensation ratio of 0.7% on SVHN, and a slight decrease of 2.2% at a condensation ratio of 10% on CIFAR-100. These results suggest that our method has the potential to achieve lossless compression by maintaining semantic diversity. Thirdly, the improvements of Our DSDM are limited when IPC=1. This is because the covariance matrix of only one distilled instance cannot be estimated. However, our DSDM still outperforms most of the other methods, because our DSDM aligns features from multiple models to stabilize the training.

# 5.3 Results on Speech Dataset

Following the setting in previous work [\[14\]](#page-8-26), each speech data is preprocessed to be a magnitude spectrogram with the size of 64×64. For the factor parameter  $l$ , each spectrogram is factorized into  $2$  miniexamples along the time axis. Table [2](#page-6-1) shows the test accuracy for mini-speech commands. It can be seen that our DSDM also greatly outperforms the baseline method, confirming its effectiveness in the speech domain. This means that our approach can be useful in both the image and the speech domain.

## 5.4 Cross-Architecture Evaluation

A more important issue for distilled data is whether they can generalize well to different networks in the evaluation tasks. In Table [3](#page-6-2) we show the performance of our distilled dataset (optimized on ConvNet-3) on the evaluation task trained with ConvNet-3, ResNet-10 [\[11\]](#page-8-41) and DenseNet-121 [\[12\]](#page-8-42). Notably, our DSDM not only outperforms other methods on specific distillation model (ConvNet-3) but also shows significant performance gains on other unseen network architectures (ResNet-10 and DenseNet-121). This robust crossarchitecture generalization suggests that our distilled dataset with enhanced semantic diversity and integrated information from different pre-models can generalize well to different backbones in the evaluation tasks.

<span id="page-5-1"></span>

## 5.5 Analysis

Ablation Study. To evaluate the effectiveness of each component in our DSDM, we conduct ablation experiments on CIFAR-10, including the prototype alignment loss ( $\mathcal{L}_{proto}$ ), pre-trained models (PM), semantic diversity alignment loss  $(\mathcal{L}_{SD})$ , and historical prototype alignment loss  $(\mathcal{L}_h)$ . The results are presented in Table [4,](#page-6-3) we can draw the following conclusions: (1) Prototype alignment utilizing pre-trained models exhibits enhancements of 2.9% and 1.4% for IPC=10 and IPC=50, respectively. This suggests that leveraging pre-trained models enables the acquisition of accurate instance embeddings, thereby generating precise intrinsic semantic features for each class. (2) The inclusion of our proposed semantic

<span id="page-6-0"></span>Table 1: Top-1 accuracy of test models trained on distilled synthetic images on multiple datasets. We evaluate our method on five different datasets with different numbers of synthetic images per class. Ratio(%): the ratio of distilled images to the whole training set. Whole: the accuracy of the model trained on the whole original training set. Note:  $DD^{\dagger}$  uses different architectures i.e. LeNet for MNIST and AlexNet for CIFAR10. The other methods use ConvNet-3.

| <b>Datasets</b>     |              | <b>MNIST</b>     |                                                                                                                                                                                                                                                                                      |       | FashionMNIST     |                          |                          | <b>SVHN</b>                                                                                                                                              |        |                          | <b>CIFAR-10</b>  |            |                                                                                                                               | <b>CIFAR-100</b>                                  |                            |
|---------------------|--------------|------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|------------------|--------------------------|--------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------|--------|--------------------------|------------------|------------|-------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------|----------------------------|
| $_{\rm IPC}$        | $\mathbf{1}$ | 10               | 50                                                                                                                                                                                                                                                                                   |       | 10               | 50                       |                          | 10                                                                                                                                                       | 50     | 1                        | 10               | 50         | $\mathbf{1}$                                                                                                                  | 10                                                | 50                         |
| Ratio $(\%)$        | 0.017        | 0.17             | 0.83                                                                                                                                                                                                                                                                                 | 0.017 | 0.17             | 0.83                     | 0.014                    | 0.14                                                                                                                                                     | 0.7    | 0.02                     | 0.2              | 1          | 0.2                                                                                                                           | 2                                                 | 10                         |
| Whole               |              | $99.6_{\pm 0.0}$ |                                                                                                                                                                                                                                                                                      |       | $93.5_{\pm 0.1}$ |                          |                          | $95.4_{\pm 0.1}$                                                                                                                                         |        |                          | $84.8_{\pm 0.1}$ |            |                                                                                                                               | $56.2_{\pm 0.3}$                                  |                            |
| Random              |              |                  | $64.9_{\pm 3.5}$ $95.1_{\pm 0.9}$ $97.9_{\pm 0.5}$ $51.4_{\pm 3.8}$ $73.8_{\pm 0.7}$ $82.5_{\pm 0.7}$ $14.6_{\pm 1.6}$ $35.1_{\pm 4.1}$ $70.9_{\pm 0.9}$ $14.4_{\pm 2.0}$ $26.0_{\pm 1.2}$ $43.4_{\pm 1.0}$ $1.4_{\pm 0.1}$                                                          |       |                  |                          |                          |                                                                                                                                                          |        |                          |                  |            |                                                                                                                               |                                                   | $5.0_{+0.2}$ $15.0_{+0.4}$ |
| Herding             |              |                  | $89.2_{\pm 1.6}$ $93.7_{\pm 0.3}$ $94.8_{\pm 0.3}$                                                                                                                                                                                                                                   |       |                  |                          |                          | $67.0_{\pm 1.9}$ $71.1_{\pm 0.7}$ $71.9_{\pm 0.8}$ $20.9_{\pm 1.3}$ $50.5_{\pm 3.3}$ $72.6_{\pm 0.8}$ $21.5_{\pm 1.2}$ $31.6_{\pm 0.7}$ $40.4_{\pm 0.6}$ |        |                          |                  |            |                                                                                                                               | $8.4_{\pm 0.3}$ $17.3_{\pm 0.3}$ $33.7_{\pm 0.5}$ |                            |
| K-center            |              |                  | $89.3_{\pm 1.5}$ $84.4_{\pm 1.7}$ $97.4_{\pm 0.3}$ $66.9_{\pm 1.8}$ $54.7_{\pm 1.5}$ $68.3_{\pm 0.8}$ $21.0_{\pm 1.5}$ $14.0_{\pm 1.3}$ $20.1_{\pm 1.4}$ $21.5_{\pm 1.3}$ $14.7_{\pm 0.7}$ $27.0_{\pm 1.4}$ $8.3_{\pm 0.3}$ $7.1_{\pm 0.2}$ $30.5_{\pm 0.3}$                         |       |                  |                          |                          |                                                                                                                                                          |        |                          |                  |            |                                                                                                                               |                                                   |                            |
| $DD^{\dagger}$ [30] | $\sim$       | $79.5_{\pm 8.1}$ |                                                                                                                                                                                                                                                                                      |       |                  |                          |                          |                                                                                                                                                          |        | $\overline{\phantom{a}}$ | $36.8_{\pm 1.2}$ | $\sim$ $-$ |                                                                                                                               |                                                   |                            |
| DC[41]              |              |                  | 91.7 $\pm$ 05 97.4 $\pm$ 0.3 98.8 $\pm$ 0.2                                                                                                                                                                                                                                          |       |                  |                          |                          | $70.5_{\pm 0.6}$ $82.3_{\pm 0.4}$ $83.6_{\pm 0.4}$ $31.2_{\pm 1.4}$ $76.1_{\pm 0.6}$ $82.3_{\pm 0.3}$                                                    |        |                          |                  |            | $28.3_{\pm 0.5}$ $44.9_{\pm 0.5}$ $53.9_{\pm 0.5}$ $12.8_{\pm 0.3}$ $25.2_{\pm 0.3}$                                          |                                                   |                            |
| DSA [39]            |              |                  | 88.7 $\pm$ 0.6 97.8 $\pm$ 0.1 99.2 $\pm$ 0.1                                                                                                                                                                                                                                         |       |                  |                          |                          | $70.6_{\pm 0.6}$ 84.6 $_{\pm 0.3}$ 88.7 $_{\pm 0.2}$ 27.5 $_{\pm 1.4}$ 79.2 $_{\pm 0.5}$ 84.4 $_{\pm 0.4}$                                               |        |                          |                  |            | $28.8_{\pm 0.7}$ 52.1 <sub>±0.5</sub> 60.6 <sub>±0.5</sub> 13.9 <sub>±0.3</sub> 32.3 <sub>±0.3</sub> 42.8 <sub>±0.4</sub>     |                                                   |                            |
| MTT[2]              |              |                  |                                                                                                                                                                                                                                                                                      |       |                  |                          |                          |                                                                                                                                                          | $\sim$ |                          |                  |            | 46.3 <sub>±0.8</sub> 65.3 <sub>±0.7</sub> 71.6 <sub>±0.2</sub> 24.3 <sub>±0.3</sub> 40.1 <sub>±0.4</sub> 47.7 <sub>±0.2</sub> |                                                   |                            |
| DM [40]             |              |                  | $89.7_{+0.6}$ $97.5\pm0.1$ $98.6_{+0.1}$                                                                                                                                                                                                                                             |       |                  |                          |                          | $70.7_{\pm 0.6}$ $83.5_{\pm 0.3}$ $88.1_{\pm 0.6}$ $30.3_{\pm 0.1}$ $73.5_{\pm 0.5}$ $82.0_{\pm 0.8}$                                                    |        |                          |                  |            | $26.0_{\pm 0.8}$ $48.9_{\pm 0.6}$ $63.0_{\pm 0.4}$ $11.4_{\pm 0.3}$ $29.7_{\pm 0.3}$ $43.6_{\pm 0.4}$                         |                                                   |                            |
| <b>CAFE</b> [29]    |              |                  | 93.1 <sub>±0.3</sub> 97.2 <sub>±0.2</sub> 98.6 <sub>±0.2</sub>                                                                                                                                                                                                                       |       |                  |                          |                          | 77.1 <sub>±0.9</sub> 83.0 <sub>±0.4</sub> 84.8 <sub>±0.4</sub> 42.6 <sub>±3.3</sub> 75.9 <sub>±0.6</sub> 81.3 <sub>±0.3</sub>                            |        |                          |                  |            | $30.3_{\pm 1.1}$ $46.3_{\pm 0.6}$ $55.5_{\pm 0.6}$ $12.9_{\pm 0.3}$ $27.8_{\pm 0.3}$ $37.9_{\pm 0.3}$                         |                                                   |                            |
| CAFE+DSA [29]       |              |                  | $90.8_{\pm 0.5}$ $97.5_{\pm 0.1}$ $98.9_{\pm 0.2}$                                                                                                                                                                                                                                   |       |                  |                          |                          | $73.7_{\pm 0.7}$ $83.0_{\pm 0.3}$ $88.2_{\pm 0.3}$ $42.9_{\pm 3.0}$ $77.9_{\pm 0.6}$ $82.3_{\pm 0.4}$                                                    |        |                          |                  |            | $31.6_{\pm 0.8}$ $50.9_{\pm 0.5}$ $62.3_{\pm 0.4}$ $14.0_{\pm 0.3}$ $31.5_{\pm 0.2}$ $42.9_{\pm 0.2}$                         |                                                   |                            |
| IDM $[42]$          |              |                  |                                                                                                                                                                                                                                                                                      |       |                  | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$ | $\overline{\phantom{a}}$                                                                                                                                 | $\sim$ |                          |                  |            | $45.6_{\pm 0.7}$ $58.6_{\pm 0.1}$ $67.5_{\pm 0.1}$ $20.1_{\pm 0.3}$ $45.1_{\pm 0.1}$ $50.0_{\pm 0.2}$                         |                                                   |                            |
| M3D [36]            |              |                  | $94.4_{\pm 0.2}$ $97.6_{\pm 0.1}$ $98.2_{\pm 0.2}$ $80.7_{\pm 0.3}$ $85.0_{\pm 0.1}$ $86.2_{\pm 0.3}$ $62.8_{\pm 0.5}$ $83.3_{\pm 0.7}$ $89.0_{\pm 0.2}$ $45.3_{\pm 0.3}$ $63.5_{\pm 0.2}$ $69.9_{\pm 0.5}$ $26.2_{\pm 0.3}$ $42.4_{\pm 0.2}$ $50.9_{\pm 0.$                         |       |                  |                          |                          |                                                                                                                                                          |        |                          |                  |            |                                                                                                                               |                                                   |                            |
| <b>DSDM</b>         |              |                  | $\begin{bmatrix} 94.8_{\pm 0.2} & 98.5_{\pm 0.2} & 99.2_{\pm 0.1} & 80.8_{\pm 0.4} & 87.3_{\pm 0.3} & 88.9_{\pm 0.3} & 60.2_{\pm 0.2} & 85.4_{\pm 0.3} & 91.3_{\pm 0.2} & 45.0_{\pm 0.4} & 66.5_{\pm 0.3} & 75.8_{\pm 0.3} & 19.5_{\pm 0.2} & 46.2_{\pm 0.3} & 54.0_{\pm 0.2} & 19.$ |       |                  |                          |                          |                                                                                                                                                          |        |                          |                  |            |                                                                                                                               |                                                   |                            |

<span id="page-6-1"></span>Table 2: Top-1 accuracy of ConvNet-4 trained on distilled spectrograms.

| IPC | Method |         |      |      |      |
|-----|--------|---------|------|------|------|
|     | Random | Herding | DM   | DSA  | DSDM |
| 10  | 42.6   | 56.2    | 69.1 | 65.0 | 71.1 |
| 20  | 57.0   | 72.9    | 77.0 | 74.0 | 78.9 |

<span id="page-6-2"></span>Table 3: Cross-architecture generalization performance (%) on CIFAR-10. The distilled synthetic images is condensed using ConvNet-3 and evaluated using other architectures.

| IPC | Evaluation   | Method     |      |      |      |
|-----|--------------|------------|------|------|------|
|     |              | <b>DSA</b> | DM   | M3D  | Ours |
|     | ConvNet-3    | 52.1       | 48.9 | 63.5 | 66.5 |
| 10  | ResNet-10    | 32.9       | 42.3 | 56.7 | 63.7 |
|     | DenseNet-121 | 34.5       | 39.0 | 54.6 | 63.8 |
| 50  | ConvNet-3    | 60.6       | 63.0 | 69.9 | 75.8 |
|     | ResNet-10    | 49.7       | 58.6 | 66.6 | 70.8 |
|     | DenseNet-121 | 49.1       | 57.4 | 66.1 | 68.7 |

diversity alignment yields improvements of 2.4% and 4.9% respectively. This underscores the effectiveness of our covariance matrix in capturing semantic diversity. Notably, with the increased IPC (i.e., IPC=50), more diversified distilled instances are obtained, resulting in a greater improvement. (3) Historical prototype alignment emphasizes the capacity to maintain the semantic diversity of feature distributions across various models, it can further boost

<span id="page-6-3"></span>Table 4: Ablation study of different components in DSDM on CIFAR10 with IPC=10/50.

| $\mathcal{L}_{proto}$ | PM | $\mathcal{L}_{SD}$ | $\mathcal{L}_{h}$ | IPC = 10 | IPC = 50 |
|-----------------------|----|--------------------|-------------------|----------|----------|
| ✓                     |    |                    |                   | 60.6     | 68.8     |
| ✓                     | ✓  |                    |                   | 63.5     | 70.2     |
| ✓                     | ✓  | ✓                  |                   | 66.2     | 75.1     |
| ✓                     | ✓  |                    | ✓                 | 64.6     | 72.7     |
| ✓                     | ✓  | ✓                  | ✓                 | 66.5     | 75.8     |

performance. In summary, the three designed loss functions are complementary and together improve the overall performance.

**Effect of Pre-trained Epochs.** The epochs  $E$  for pre-training directly affect the accuracy of instance embeddings (further affect the prototypes and semantic diversity for distillation) extracted by the pre-trained models. To show their effects, we pre-train ConvNets on CIFAR-10 by  $E$  in  $\{1, 5, 10, 15, 20, 25, 30\}$  and utilize these pre-trained models to perform our DSDM for IPC=50. As shown in the left of Figure [4,](#page-7-0) there is a gradual improvement in performance with increasing epochs until reaching optimal performance at  $E = 20$ . After that, increasing epochs do not significantly affect the performance, indicating that our DSDM only needs a few additional pre-training epochs to acquire better performance.

Effect of Number of Pre-trained Models. More pre-trained models mean the distilled data generalize better for the evaluation task. However, more pre-trained models also occupy more training time. Therefore, we perform our DSDM with different numbers of models  $M \in \{1, 5, 10, 20\}$  with the pre-training epochs  $E = 20$ . As illustrated in the right of Figure [4,](#page-7-0) we show the distillation performance. When M is set to 1 and 5, the performance is slightly worse.

MM '24, October 28-November 1, 2024, Melbourne, VIC, Australia

<span id="page-7-0"></span>Image /page/7/Figure/1 description: The image contains two bar charts side-by-side. The left chart is titled "Epoch (E)" on the x-axis and "Test Accuracy (%)" on the y-axis, with values ranging from 70 to 78. The bars represent test accuracy at different epochs: 1 (72.9%), 5 (74.3%), 10 (74.9%), 15 (75.4%), 20 (75.8%), 25 (75.6%), and 30 (75.7%). The right chart is titled "Number of Pre-trained Models (M)" on the x-axis and "Test Accuracy (%)" on the y-axis, also with values ranging from 70 to 78. The bars represent test accuracy with different numbers of pre-trained models: 1 (71.0%), 5 (73.2%), 10 (75.8%), 15 (75.2%), 20 (75.7%), 25 (75.4%), and 30 (75.6%).

Figure 4: The distillation performance for different pre-train epochs (Left) and different number of pre-trained models (Right). The evaluation is on CIFAR-10 with IPC = 50.

<span id="page-7-1"></span>Image /page/7/Figure/3 description: The image contains two line graphs side-by-side. Both graphs plot "Test Accuracy (%)" on the y-axis against a parameter on the x-axis. The left graph shows the parameter \u00b11 on the x-axis, ranging from 10 to 100. The right graph shows the parameter \u00b12 on the x-axis, ranging from 0.05 to 0.50. Both graphs display two lines: a red line with circular markers labeled "DSDM" and a blue dashed line labeled "M3D". The red line in the left graph starts at approximately 72% accuracy at \u00b11=10, increases to a peak of about 76% at \u00b11=50, and then slightly decreases to about 75.5% at \u00b11=100. The blue dashed line remains constant at approximately 70% accuracy. The red line in the right graph starts at approximately 74.5% accuracy at \u00b12=0.05, increases to a peak of about 75.5% at \u00b12=0.15, and then decreases to about 72% at \u00b12=0.50. The blue dashed line again remains constant at approximately 70%.

Figure 5: Effect of  $\lambda_1$  and  $\lambda_2$  on CIFAR-10 with IPC=50.

This indicates that the number of models has a significant effect on the distillation performance. Despite this, our DSDM achieves optimal performance with only 10 pre-trained models with little storage and computation consumption.

Effect of  $\lambda_1$ . The parameter  $\lambda_1$  is a weighting parameter for the degree of improved semantic diversity. Figure [5](#page-7-1) illustrates the effect of  $\lambda_1$  on the distillation performance for IPC=50 on CIFAR-10. We find that performance improves as the range of  $\lambda_1$  goes from 10 to 50, while the effect of increasing the range of  $\lambda_1$  from 50 to 100 is almost negligible. So we set  $\lambda_1$  to 50. Furthermore, we observe that our DSDM improves by 2.3% over the suboptimal M3D result despite  $\lambda_1 = 10$ , which further illustrates the importance of semantic diversity matching in dataset distillation.

Effect of  $\lambda_2$ . Similarly, we observe the effect of the weighting parameter  $\lambda_2$  on the performance of the historical prototype. When  $\lambda_2$  = 0.2, our DSDM performance reaches the optimal value. As  $\lambda_2$ continues to increase, the performance decreases, which may be because the integration of the historical model is quite different from the current model, causing the current model to deviate from the class center of the original data.

## 5.6 Visualization

Distribution Visualization. To qualitatively illustrate the effectiveness of our DSDM in modeling the distribution of the original dataset, we use t-SNE to visualize the distilled data of the same category on CIFAR-10 for IPC=10/50 obtained by DM and DSDM, respectively. The results are depicted in Figure [6,](#page-7-2) where the "green dots" and the "red stars" stand for the real data and distilled data. It is obvious that the distilled data obtained by DSDM distribute more dispersedly than those of DM, especially for the distilled instances in the blue circles.

Distilled Images. To evaluate whether our method captures the semantic diversity of the original dataset, we visualize the distilled

MM '24, October 28-November 1, 2024, Melbourne, VIC, Australia Hongcheng Li, Yucan Zhou, Xiaoyan Gu, Bo Li, and Weiping Wang

<span id="page-7-2"></span>Image /page/7/Figure/12 description: The image displays four scatter plots, labeled (a), (b), (c), and (d). Each plot shows a distribution of light green points, with several red star-shaped markers scattered throughout. Plots (a) and (b) are labeled "DM (IPC=10)" and "DSDM (IPC=10)" respectively, and both have a blue oval highlighting a small cluster of red stars in the upper left quadrant. Plots (c) and (d) are labeled "DM (IPC=50)" and "DSDM (IPC=50)" respectively. Plot (c) shows a denser distribution of red stars compared to (a), with a blue oval highlighting a cluster of red stars in the upper left. Plot (d) shows an even greater density of red stars than (c), with a similar blue oval highlighting a cluster in the upper left. The overall impression is a comparison of data distributions under different parameters (IPC=10 and IPC=50) and methods (DM and DSDM).

Figure 6: The data distributions of original images and distilled images through DM and DSDM for the same category on CIFAR-10, with IPC=10/50. "Green dots" denote the real data, while "red stars" represent the distilled data.

<span id="page-7-3"></span>Image /page/7/Picture/14 description: The image displays two grids of small, abstract images, likely generated by a machine learning model. The left grid contains 100 images arranged in a 10x10 formation, and the right grid also contains 100 images in a 10x10 formation. The images themselves are colorful and somewhat noisy, with many appearing to be abstract representations of objects or scenes. Some images in the right grid show recognizable shapes, such as horses and boats, while the images in the left grid are more abstract and less clearly defined. The overall impression is a collection of generated visual samples.

(a) The distilled images of DM. (b) The distilled images of DSDM.

Figure 7: Visualizations of distilled images generated by DM and DSDM on CIFAR-10 with IPC=10.

data of DM and our DSDM on CIFAR-10 in Figure [7.](#page-7-3) Each class contains 10 images. Our observations lead to two key conclusions: (1) The distilled data of our DSDM are more different compared to those of DM, proving that our covariance matrix alignment captures the semantic diversity of the original dataset. (2) The distilled data of our DSDM is more discriminative between each class, indicating that the class center of the pre-trained model effectively preserves the inherent features of the classes.

## 6 CONCLUSION

In this work, we propose a novel approach called Diversified Semantic Distribution Matching (DSDM) for Dataset Distillation, which explicitly aims to capture the semantic diversity features of the original dataset. DSDM consists of three carefully designed losses: prototype alignment, semantic diversity alignment, and historical prototype alignment. The prototype alignment and semantic diversity alignment modules are employed to capture the distribution of the original dataset. The historical prototype alignment enables DSDM to perform more stably during the training process. Experimental results across five image datasets and one speech dataset demonstrate that our DSDM outperforms existing techniques and generalizes well across different model architectures. In the future, we will explore how to better capture semantic diversity in more challenging scenarios of a low condensation ratio (e.g., IPC=1).

# ACKNOWLEDGMENTS

This work was supported by the Chinese Academy of Sciences under grant No. XDB0690302 and the National Natural Science Foundation of China under grant No. 62006221.

<span id="page-8-0"></span>Diversified Semantic Distribution Matching for Dataset Distillation MM '24, October 28-November 1, 2024, Melbourne, VIC, Australia

MM '24, October 28-November 1, 2024, Melbourne, VIC, Australia

# REFERENCES

- <span id="page-8-24"></span>[1] Rahaf Aljundi, Min Lin, Baptiste Goujaud, and Yoshua Bengio. 2019. Gradient based sample selection for online continual learning. Advances in neural information processing systems 32 (2019).
- <span id="page-8-16"></span>[2] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. 2022. Dataset Distillation by Matching Training Trajectories. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR) Workshops. 4750–4759.
- <span id="page-8-30"></span>[3] Xiaohua Chen, Yucan Zhou, Dayan Wu, Wanqian Zhang, Yu Zhou, Bo Li, and Weiping Wang. 2022. Imagine by reasoning: A reasoning-based implicit semantic data augmentation for long-tailed classification. In Proceedings of the AAAI Conference on Artificial Intelligence, Vol. 36. 356–364.
- <span id="page-8-6"></span>[4] Yutian Chen, Max Welling, and Alex Smola. 2010. Super-samples from kernel herding. In Proceedings of the Twenty-Sixth Conference on Uncertainty in Artificial Intelligence (Catalina Island, CA) (UAI'10). AUAI Press, Arlington, Virginia, USA,  $109 - 116$ .
- <span id="page-8-23"></span>[5] Yutian Chen, Max Welling, and Alex Smola. 2012. Super-samples from kernel herding. arXiv preprint arXiv:1203.3472 (2012).
- <span id="page-8-11"></span>[6] Zongxiong Chen, Jiahui Geng, Herbert Woisetschlaeger, Sonja Schimmler, Ruben Mayer, and Chunming Rong. 2023. A Comprehensive Study on Dataset Distillation: Performance, Privacy, Robustness and Fairness. arXiv preprint arXiv:2305.03355 (2023).
- <span id="page-8-37"></span>[7] Speech Commands. 1804. A Dataset for Limited-Vocabulary Speech Recognition. URL: https://arxiv. org/abs/1804.03209 ( : 28.12. 2020) (1804).
- <span id="page-8-17"></span>[8] Jiawei Du, Qin Shi, and Joey Tianyi Zhou. 2024. Sequential Subset Matching for Dataset Distillation. Advances in Neural Information Processing Systems 36 (2024).
- <span id="page-8-7"></span>[9] Reza Zanjirani Farahani and Masoud Hekmatfar. 2009. Facility location: concepts, models, algorithms and case studies. Springer Science & Business Media.
- <span id="page-8-18"></span>[10] Yunzhen Feng, Shanmukha Ramakrishna Vedantam, and Julia Kempe. 2023. Embarrassingly Simple Dataset Distillation. In The Twelfth International Conference on Learning Representations.
- <span id="page-8-41"></span>[11] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. 2016. Deep residual learning for image recognition. In Proceedings of the IEEE conference on computer vision and pattern recognition. 770–778.
- <span id="page-8-42"></span>[12] Gao Huang, Zhuang Liu, Laurens van der Maaten, and Kilian Q Weinberger. 2017. Densely connected convolutional networks. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition.
- <span id="page-8-1"></span>[13] Zi Huang, Heng Tao Shen, Jie Shao, Stefan Rüger, and Xiaofang Zhou. 2008. Locality condensation: a new dimensionality reduction method for image retrieval. In Proceedings of the 16th ACM International Conference on Multimedia (Vancouver, British Columbia, Canada) (MM '08). Association for Computing Machinery, New York, NY, USA, 219–228.<https://doi.org/10.1145/1459359.1459389>
- <span id="page-8-26"></span>[14] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. 2022. Dataset Condensation via Efficient Synthetic-Data Parameterization. In International Conference on Machine Learning (ICML).
- <span id="page-8-36"></span>[15] Alex Krizhevsky, Geoffrey Hinton, et al. 2009. Learning multiple layers of features from tiny images. (2009).
- <span id="page-8-33"></span>[16] Yann LeCun and Corinna Cortes. 2005. The mnist database of handwritten digits. <https://api.semanticscholar.org/CorpusID:60282629>
- <span id="page-8-12"></span>[17] Shiye Lei and Dacheng Tao. 2023. A comprehensive survey of dataset distillation. IEEE Transactions on Pattern Analysis and Machine Intelligence (2023).
- <span id="page-8-3"></span>[18] Fan Liu, Huilin Chen, Zhiyong Cheng, Liqiang Nie, and Mohan Kankanhalli. 2023. Semantic-Guided Feature Distillation for Multimodal Recommendation. In Proceedings of the 31st ACM International Conference on Multimedia (MM '23). Association for Computing Machinery, New York, NY, USA, 6567–6575. <https://doi.org/10.1145/3581783.3611886>
- <span id="page-8-28"></span>[19] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. 2023. Dream: Efficient dataset distillation by representative matching. In Proceedings of the IEEE/CVF International Conference on Computer Vision. 17314–17324.
- <span id="page-8-35"></span>[20] Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Bo Wu, and Andrew Y Ng. 2011. Reading digits in natural images with unsupervised feature learning. (2011).
- <span id="page-8-39"></span>[21] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. 2020. Dataset meta-learning from kernel ridge-regression. arXiv preprint arXiv:2011.00050 (2020).
- <span id="page-8-40"></span>[22] TimothyK. Nguyen, Roman Novak, Lechao Xiao, and Jae-Hoon Lee. 2021. Dataset Distillation with Infinitely Wide Convolutional Networks. Neural Information Processing Systems,Neural Information Processing Systems (Dec 2021).
- <span id="page-8-4"></span>[23] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, et al. 2021. Learning transferable visual models from natural language supervision. In International conference on machine learning. PMLR, 8748–8763.
- <span id="page-8-8"></span>[24] Sylvestre-Alvise Rebuffi, Alexander Kolesnikov, Georg Sperl, and Christoph H. Lampert. 2017. iCaRL: Incremental Classifier and Representation Learning. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR).

- <span id="page-8-38"></span>[25] Levent Sagun, Utku Evci, V Ugur Guney, Yann Dauphin, and Leon Bottou. 2017. Empirical analysis of the hessian of over-parametrized neural networks. arXiv preprint arXiv:1706.04454 (2017).
- <span id="page-8-9"></span>[26] Ozan Sener and Silvio Savarese. 2017. Active Learning for Convolutional Neural Networks: A Core-Set Approach. arXiv: Machine Learning, arXiv: Machine Learning (Aug 2017).
- <span id="page-8-27"></span>[27] Xingyi Yang Jingwen Ye Xinchao Wang Songhua Liu, Kai Wang. 2022. Dataset Distillation via Factorization. NeurIPS (2022).
- <span id="page-8-31"></span>[28] Paul Upchurch, Jacob Gardner, Geoff Pleiss, Robert Pless, Noah Snavely, Kavita Bala, and Kilian Weinberger. 2017. Deep feature interpolation for image content changes. In Proceedings of the IEEE conference on computer vision and pattern recognition. 7064–7073.
- <span id="page-8-20"></span>[29] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. 2022. CAFE: Learning To Condense Dataset by Aligning Features. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). 12196–12205.
- <span id="page-8-13"></span>[30] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. 2018. Dataset distillation. arXiv preprint arXiv:1811.10959 (2018).
- <span id="page-8-5"></span>[31] Zixiao Wang, Hongtao Xie, Yuxin Wang, Jianjun Xu, Boqiang Zhang, and Yongdong Zhang. 2023. Symmetrical Linguistic Feature Distillation with CLIP for Scene Text Recognition. In Proceedings of the 31st ACM International Conference on Multimedia (MM '23). Association for Computing Machinery, New York, NY, USA, 509–518.<https://doi.org/10.1145/3581783.3611769>
- <span id="page-8-10"></span>[32] Max Welling. 2009. Herding dynamical weights to learn. In Proceedings of the 26th Annual International Conference on Machine Learning. [https://doi.org/10.](https://doi.org/10.1145/1553374.1553517) [1145/1553374.1553517](https://doi.org/10.1145/1553374.1553517)
- <span id="page-8-2"></span>[33] Yongkang Wong, Shaojing Fan, Yangyang Guo, Ziwei Xu, Karen Stephen, Rishabh Sheoran, Anusha Bhamidipati, Vivek Barsopia, Jianquan Liu, and Mohan Kankanhalli. 2022. Compute to Tell the Tale: Goal-Driven Narrative Generation. In Proceedings of the 30th ACM International Conference on Multimedia (MM '22). Association for Computing Machinery, New York, NY, USA, 6875–6882. <https://doi.org/10.1145/3503161.3549202>
- <span id="page-8-34"></span>[34] Han Xiao, Kashif Rasul, and Roland Vollgraf. 2017. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. arXiv preprint arXiv:1708.07747 (2017).
- <span id="page-8-29"></span>[35] Ruonan Yu, Songhua Liu, and Xinchao Wang. 2023. Dataset distillation: A comprehensive review. IEEE Transactions on Pattern Analysis and Machine Intelligence (2023).
- <span id="page-8-14"></span>[36] Hansong Zhang, Shikun Li, Pengju Wang, Dan Zeng, and Shiming Ge. 2024. M3D: Dataset Condensation by Minimizing Maximum Mean Discrepancy. In The 38th Annual AAAI Conference on Artificial Intelligence (AAAI).
- <span id="page-8-32"></span>[37] Hanwang Zhang, Zheng-Jun Zha, Yang Yang, Shuicheng Yan, Yue Gao, and Tat-Seng Chua. 2013. Attribute-augmented semantic hierarchy: towards bridging semantic gap and intention gap in image retrieval. In Proceedings of the 21st ACM International Conference on Multimedia (Barcelona, Spain) (MM '13). Association for Computing Machinery, New York, NY, USA, 33–42. [https://doi.org/10.1145/](https://doi.org/10.1145/2502081.2502093) [2502081.2502093](https://doi.org/10.1145/2502081.2502093)
- <span id="page-8-21"></span>[38] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. 2023. Accelerating dataset distillation via model augmentation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 11950–11959.
- <span id="page-8-25"></span>[39] Bo Zhao and Hakan Bilen. 2021. Dataset condensation with differentiable siamese augmentation. In International Conference on Machine Learning. PMLR, 12674– 12685.
- <span id="page-8-22"></span>[40] Bo Zhao and Hakan Bilen. 2023. Dataset condensation with distribution matching. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision. 6514–6523.
- <span id="page-8-19"></span>[41] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. 2021. Dataset condensation with gradient matching. International Conference on Learning Representations (2021).
- <span id="page-8-15"></span>[42] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. 2023. Improved distribution matching for dataset condensation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. 7856–7865.
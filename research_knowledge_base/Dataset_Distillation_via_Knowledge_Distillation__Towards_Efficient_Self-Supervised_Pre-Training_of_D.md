# DATASET DISTILLATION VIA KNOWLEDGE DISTILLA-TION: TOWARDS EFFICIENT SELF-SUPERVISED PRE-TRAINING OF DEEP NETWORKS

<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> Department of Computer Science, University of California, <NAME_EMAIL>, <EMAIL>, <EMAIL>

## ABSTRACT

Dataset distillation (DD) generates small synthetic datasets that can efficiently train deep networks with a limited amount of memory and compute. Despite the success of DD methods for supervised learning, DD for self-supervised pre-training of deep models has remained unaddressed. Pre-training on unlabeled data is crucial for efficiently generalizing to downstream tasks with limited labeled data. In this work, we propose the first effective DD method for SSL pre-training. First, we show, theoretically and empirically, that naïve application of supervised DD methods to SSL fails, due to the high variance of the SSL gradient. Then, we address this issue by relying on insights from knowledge distillation (KD) literature. Specifically, we train a small student model to match the representations of a larger teacher model trained with SSL. Then, we generate a small synthetic dataset by matching the training trajectories of the student models. As the KD objective has considerably lower variance than SSL, our approach can generate synthetic datasets that can successfully pre-train high-quality encoders. Through extensive experiments, we show that our distilled sets lead to up to 13% higher accuracy than prior work, on a variety of downstream tasks, in the presence of limited labeled data. Code at <https://github.com/BigML-CS-UCLA/MKDT>

## 1 INTRODUCTION

Dataset distillation (DD) aims to generate a very small set of synthetic images that can simulate training on a large image dataset, with extremely limited memory and compute [\(Wang et al., 2018\)](#page-12-0). This facilitates training models on the edge, speeds up continual learning, and provides strong privacy guarantees [\(Kim et al., 2022;](#page-10-0) [Cazenavette et al., 2022;](#page-10-1) [Sajedi et al., 2023;](#page-11-0) [Dong et al., 2022\)](#page-10-2). As a result, there has been a surge of interest in developing better DD methods for training neural networks in a supervised manner. However, in many applications, very few labeled example are available. In this case, supervised models often fail to generalize well. Instead, models are pre-trained, using self-supervised learning (SSL), on a large amount of *unlabeled* data and then adapted to the downstream task using the limited labeled data by training a linear classifier using the labeled examples of each downstream task (linear probe). Remarkably, [Chen et al.](#page-10-3) [\(2020\)](#page-10-3) showed that SSL pre-training, followed by linear probe, can outperform Supervised Learning (SL) by nearly 30% on ImageNet [\(Deng et al., 2009\)](#page-10-4) when only 1% of labels are available. More impressively, by only training the linear layer (linear probe), SSL pretraining is able to generalize to a variety of downstream tasks nearly as effectively as full fine-tuning on the downstream task, for a fraction of the cost. Thus, SSL pre-training's benefits are invaluable in today's modern ML ecosystem, where unlabeled data is plentiful and it is essential to generalize to a plethora of downstream tasks, effectively and efficiently. With the datasets for SSL being far larger than those for SL, the computational and privacy benefits of dataset distillation for SSL are even more important than they are for SL. Nevertheless, DD for SSL pre-training has remained an open problem.

DD for SSL is, however, very challenging. One needs to ensure that pre-training on the synthetic dataset, distilled from unlabeled data, results in a encoder that yields high-quality representations for a variety of downstream tasks. Existing DD methods for SL generate synthetic data by matching gradients [\(Zhao et al., 2021;](#page-12-1) [Zhao & Bilen, 2021;](#page-12-2) [Lee et al., 2022\)](#page-11-1) or trajectory [\(Cazenavette et al.,](#page-10-1) [2022;](#page-10-1) [Du et al., 2023;](#page-10-5) [Cui et al., 2022\)](#page-10-6) of training on distribution of real data [\(Zhao & Bilen, 2023;](#page-12-3) [Sajedi et al., 2023;](#page-11-0) [Wang et al., 2022a\)](#page-11-2), or meta-model matching by generating synthetic data such that training on it achieves low loss on real data [\(Wang et al., 2018;](#page-12-0) [Nguyen et al., 2021;](#page-11-3) [Loo et al.,](#page-11-4) [2022;](#page-11-4) [Zhou et al., 2022\)](#page-12-4). Among them, gradient and distribution matching methods heavily rely on labels and will suffer from representation collapse otherwise. Hence, they are not applicable to SSL DD. Very recently, [Lee et al.](#page-11-5) [\(2023\)](#page-11-5) applied meta-model matching to generate synthetic examples for SSL pre-training, and evaluated its performance by fine-tuning the entire pre-trained model on the large labeled downstream datasets. However, we show that SSL pre-training on these distilled sets does not provide any advantage over SSL pre-training on random real examples.

In this work, we address distilling small synthetic datasets for SSL pre-training via trajectory matching. First, we show, theoretically and empirically, that naïve application of trajectory matching to SSL fails, due to the high variance of the gradient of the SSL loss. Then, we rely on insights from *knowledge distillation* (KD) to considerably reduce the variance of SSL trajectories. KD trains a smaller student network to match the predictions of a larger teacher network trained with supervised learning [\(Hinton](#page-10-7) [et al., 2015\)](#page-10-7). In doing so, the student network can match the performance of the larger teacher model.

Here, we apply KD for SSL by training a student encoder to match the *representations* of a larger teacher encoder trained with SSL. Then, we propose generating synthetic data for SSL by Matching Knowledge Distillation Trajectories (MKDT). Crucially, as the KD objective for training the student model has considerably lower variance, it enables generating higher-quality synthetic data by matching the lower-variance trajectories of the *student* model. As a result, the encoder can learn high-quality representations from the synthetic data. We also provide theoretical and empirical evidence showing that KD trajectories are indeed lower variance than SSL trajectories and that this lower variance enables effective dataset distillation for SSL.

Finally, we conduct extensive experiments to validate the effectiveness of our proposal MKDT for SSL pre-training. In particular, we distill both low resolution (CIFAR10, CIFAR100) and larger, high resolution datasets (TinyImageNet) down to 2% and 5% of original dataset size and show that, across various downstream tasks, MKDT distilled sets outperform all baselines by up to 13% in the presence of limited labeled data. Moreover, we confirm that the datasets distilled with smaller ConvNets can transfer to architectures as large as ResNet-18. Finally, we demonstrate that MKDT is effective across SSL algorithms (BarlowTwins [\(Zbontar et al., 2021\)](#page-12-5) and SimCLR [\(Chen et al., 2020\)](#page-10-3)).

<span id="page-1-0"></span>

# 2 RELATED WORK

## 2.1 DATASET DISTILLATION

There has been a large body of recent work on dataset distillation for supervised learning. These techniques can be broadly characterized into meta-model matching, gradient matching, distribution matching and trajectory matching [\(Sachdeva & McAuley, 2023\)](#page-11-6).

Meta-model Matching Meta-model matching generates synthetic data such that a model trained on the synthetic dataset achieves low training loss on the real dataset [\(Wang et al., 2018\)](#page-12-0). The traditional meta-model matching approach is computation and memory inefficient as it requires solving a bi-level optimization problem. Thus, several methods [\(Nguyen et al., 2021;](#page-11-3) [Loo et al., 2022;](#page-11-4) [Zhou et al.,](#page-12-4) [2022\)](#page-12-4) have been proposed to solve the inner-optimization problem in closed form with kernel ridge regression.

Gradient Matching Gradient matching generates synthetic data by matching the gradient of a network trained on the original dataset with the gradient of the network trained on the synthetic dataset [\(Zhao et al., 2021;](#page-12-1) [Zhao & Bilen, 2021;](#page-12-2) [Lee et al., 2022\)](#page-11-1). Gradient-matching is done for each class separately, otherwise optimizing the synthetic images to match gradients is not possible [\(Zhao et al., 2021\)](#page-12-1). As a result, these methods require labels to be applicable.

Matching Training Trajectories (MTT) MTT, first proposed by [Cazenavette et al.](#page-10-1) [\(2022\)](#page-10-1), generates synthetic data by matching the training trajectories of models trained on the real dataset with that of the synthetic dataset. [Cui et al.](#page-10-6) [\(2022\)](#page-10-6) reduced the memory footprint of MTT and [Du et al.](#page-10-5) [\(2023\)](#page-10-5) minimized the accumulated error in matching trajectories by distilling flatter trajectories.

Distribution Matching Distribution matching generates synthetic data by directly matching the distribution of synthetic dataset and original dataset. One line of work does so by minimizing the

maximum mean discrepancy (MMD) between the representations of the synthetic and real data using a large pool of feature extractors [\(Zhao & Bilen, 2023;](#page-12-3) [Sajedi et al., 2023;](#page-11-0) [Wang et al., 2022b\)](#page-12-6). For these methods, distilling examples **per class** is essential, as without labels, the models trained on the synthetic data suffer from representation collapse and cannot learn any discriminative features [\(Zhao](#page-12-3) [& Bilen, 2023\)](#page-12-3). More recently, [\(Yin et al., 2024;](#page-12-7) [Zhou et al., 2024;](#page-12-8) [Shao et al., 2024\)](#page-11-7) apply ideas from *data-free knowledge distillation*[Lopes et al.](#page-11-8) [\(2017\)](#page-11-8) to match the distributions of synthetic and real images using the batch norm statistics of models trained on the full data. While these methods do not distill per class, the distillation loss relies on the labels of the data and is essential to distill data preserving class-discriminative features.

Dataset Distillation for SSL Very recently, KRR-ST [\(Lee et al., 2023\)](#page-11-5) applied the kernel-based meta-model matching to distillation for SSL. However, kernel ridge regression, with a relatively unchanging encoder as the kernel function, prevents distilling synthetic data that is useful for training the encoder effectively. We empirically confirm that the encoder learnt by pre-training on these generated examples cannot outperform encoder learnt directly using SSL pre-training on random real images. While KRR-ST also uses a MSE loss to representations instead of directly performing SSL, they claim to do so to mitigate the bias of bi-level optimization in meta-model based matching for SSL. In MKDT, we instead, use the knowledge distillation loss of MSE to representations of a larger teacher model to reduce the high variance of SSL gradients, and thus lower variance trajectories enable trajectory matching.

MTT is another dataset distillation method that is agnostic to the labels, and hence can be potentially applied to SSL. Nevertheless, application of MTT to SSL has not been explored before. In our work, we show that that naïve application of MTT to SSL yields poor performance. Then, we propose a method that leverages knowledge distillation to enable effective dataset distillation for SSL.

### 2.2 DATA-EFFICIENT LEARNING VIA SUBSET SELECTION

Another line of work that enables data-efficient learning is selecting subsets of training data that generalize on par with the full data. This has been extensively studied for supervised learning [\(Coleman](#page-10-8) [et al., 2020;](#page-10-8) [Toneva et al., 2019;](#page-11-9) [Paul et al., 2021;](#page-11-10) [Mirzasoleiman et al., 2020;](#page-11-11) [Yang et al., 2023\)](#page-12-9). At a high level, these works show that difficult-to-learn examples with a higher loss or gradient norm or uncertainty benefit SL the most. More recently, SAS [\(Joshi & Mirzasoleiman, 2023\)](#page-10-9) has been proposed for selecting subsets of data for self-supervised contrastive learning (CL). Interestingly, the most beneficial subsets for SL are shown to be least beneficial for self-supervised CL. We use SAS as a baseline and show that the synthetic data distilled by our method can outperform training on these subsets.

## 2.3 KNOWLEDGE DISTILLATION

Knowledge distillation (KD) is a technique used to transfer knowledge from a large teacher model to a smaller student model, with the aim of retaining high performance with reduced complexity [\(Hinton et al., 2015\)](#page-10-7). For supervised learning, some techniques align the student's outputs with those of the teacher [\(Hinton et al., 2015\)](#page-10-7), while others concentrate on matching intermediate features [\(Romero et al., 2015\)](#page-11-12), attention maps [\(Zagoruyko & Komodakis, 2017\)](#page-12-10) or pairwise distances between examples [\(Park et al., 2019\)](#page-11-13). Recent works have adapted KD for SSL models [\(Passalis](#page-11-14) [& Tefas, 2018;](#page-11-14) [Chen et al., 2017;](#page-10-10) [Koohpayegani et al., 2020;](#page-11-15) [Yu et al., 2019\)](#page-12-11). DarkRank [\(Chen et al.,](#page-10-10) [2017\)](#page-10-10) approaches KD for SSL as a rank matching problem between teacher and student encoders. PKT [\(Passalis & Tefas, 2018\)](#page-11-14) and Compress [\(Koohpayegani et al., 2020\)](#page-11-15) model the similarities in data samples within the representation space as a probability distribution, aiming to align these distributions between the teacher and student encoders. [Yu et al.](#page-12-11) [\(2019\)](#page-12-11) introduced the concept of minimizing the Mean Squared Error (MSE) between the representations of student and teacher encoders. In this work, we rely on KD to enable effective dataset distillation for SSL.

# 3 PROBLEM FORMULATION

Consider a dataset  $\mathcal{D}_{real} = \{x_i\}_{i \in [n]}$  of n unlabeled training examples drawn i.i.d. from an unknown distribution. Contrastive SSL methods [\(Zbontar et al., 2021;](#page-12-5) [Chen et al., 2020\)](#page-10-3) learn an encoder f that produces semantically meaningful representations by training on  $\mathcal{D}_{\text{real}}$ . BarlowTwins, in particular, learns these representations using the cross-correlation matrix of the outputs of different augmented

views of a given batch of training data:

<span id="page-3-1"></span>
$$
\mathcal{L}_{\text{BT}} := \sum_{i=1}^{d} (1 - F_{ii})^2 + \lambda \sum_{i=1}^{d} \sum_{j=1, j \neq i}^{d} F_{ij}^2,
$$
\n(1)

where F is the cross-correlation of *outputs within in a batch B* s.t.  $F_{ij}$  =  $\mathbb{E}_{x\in B}\mathbb{E}_{x_1,x_2\in\mathcal{A}(x)}[f_i(x_1)f_j(x_2)]$  with  $\mathcal{A}(x)$  being the set of augmented views of x, d is the dimension of the encoder f and  $\lambda$  is a hyperparameter. After pre-training, a linear classifier is trained on representations and labels of downstream task(s).

Our goal is to generate a synthetic dataset  $\mathcal{D}_{syn}$  from  $\mathcal{D}_{real}$ , such that  $|\mathcal{D}_{syn}| \ll |\mathcal{D}_{real}|$  and the representations of  $\mathcal{D}_{real}$  using the encoder  $f_{\theta_{syn}}$ , trained on the the synthetic data  $\mathcal{D}_{syn}$ , are similar to those obtained from encoder  $f_{\theta_{\text{real}}}$ , trained on the real data  $\mathcal{D}_{\text{real}}$ . Formally,

$$
\mathcal{D}_{\text{syn}}^* := \underset{\mathcal{D}_{\text{syn}}}{\arg \min} \mathbb{E}_{x \sim \mathcal{D}_{\text{real}}} D(f_{\theta_{\text{syn}}}(x), f_{\theta_{\text{real}}}(x)), \tag{2}
$$

where  $D(\cdot, \cdot)$  is a distance function.

Evaluation To evaluate the encoder trained on the synthetic data, for every downstream task  $\mathcal{D}_d$ , we train a linear classifier  $g_{D_d}$  on the representations of  $f_{\theta_{syn}}$ , and evaluate the generalization error of the linear classifier on  $\mathcal{D}_d$ :

<span id="page-3-3"></span>
$$
Err[f_{\theta_{syn}}(\mathcal{D}_d)] := \mathbb{E}_{(x,y)\sim\mathcal{D}_d} \mathbf{1}\big[g_{\mathcal{D}_d}(f_{\theta_{syn}}(x)) \neq y\big]
$$
(3)

An effective encoder achieves small  $\text{Err}[f_{\theta_{syn}}(\mathcal{D}_d)]$  across all downstream tasks.

## 4 MATCHING TRAINING TRAJECTORIES FOR SSL

As discussed in Sec. [2,](#page-1-0) distribution matching and gradient matching methods cannot work without labels, and meta-model matching cannot effectively update the encoder. Therefore, in our work, we focus on application of MTT to SSL distillation. First, we discuss the challenges of applying MTT in the SSL setting and show that its naïve application does not work. Then, we present our method, MKDT, that relies on recent results in knowledge distillation (KD) to enable trajectory matching for SSL.

<span id="page-3-2"></span>

### 4.1 CHALLENGES OF MATCHING SSL TRAINING TRAJECTORIES

In this section, we first introduce trajectory matching (MTT) for supervised learning (SL). Then, we discuss why naively applying MTT to the SSL setting does not work.

Matching Training Trajectories for SL MTT [Cazenavette et al.](#page-10-1) [\(2022\)](#page-10-1) generates a synthetic dataset by matching the trajectory of parameters  $\hat{\theta}$  of a model trained on the synthetic data with trajectory of parameters  $\theta^*$  of the model trained on real data (*expert trajectory*). This matching is guided by the following loss function:

<span id="page-3-0"></span>
$$
\mathcal{L}_{DD}(\mathcal{D}_{syn}) = \frac{\|\hat{\theta}_{t+N} - \theta_{t+M}^*\|^2}{\|\theta_t^* - \theta_{t+M}^*\|^2}
$$
(4)

In equation [4,](#page-3-0)  $\theta^*_t$  denotes the model parameters after training on real data up to step  $t.$  The term  $\hat{\theta}_{t+N}$ represents the model parameters after training on the synthetic dataset for N steps, starting from  $\theta_t^*$ . Similarly,  $\theta_{t+M}^*$  refers to the model parameters after M steps of training on the real dataset. The primary goal of MTT is to ensure that the encoder's weights after training on the synthetic dataset for N steps closely match the encoder's weights after training on real data for a significantly larger number of steps M, usually with  $N \ll M$ . MTT is agnostic to the training algorithm and doesn't rely on labels; thus, can be applied to dataset distillation for SSL. However, naïve application of MTT cannot effectively distill synthetic data for SSL pre-training, as we will discuss next.

High Variance Gradients Prevent Effective Trajectory Matching for SSL SSL losses rely on interaction between all examples in a batch and consequently have high variance over choices of random batches (c.f., the Barlow-Twins loss in equation [1\)](#page-3-1). As a result, the contribution of examples to the loss and hence their gradients varies significantly based on the rest of examples in the batch [Robinson et al.](#page-11-16) [\(2021\)](#page-11-16), unlike SL where each example's contribution to the loss is independent of

<span id="page-4-1"></span>Image /page/4/Figure/1 description: The image contains three plots. Plot (a) is titled "Variance in Weights at End of Trajectory v/s Trajectory Length". The y-axis is labeled "Variance of End of Trajectory Weights" and ranges from 0.00000 to 0.00175. The x-axis is labeled "Length of Trajectory" and ranges from 0 to 5. Three lines are plotted: MTT-SSL (blue), MTT-SSL (4x batch size) (green), and MTT-SL (orange). The MTT-SSL and MTT-SSL (4x batch size) lines rise sharply from 0 to approximately 0.00175 between trajectory lengths 0 and 1, and then remain flat. The MTT-SL line rises sharply from 0 to approximately 0.0011 between trajectory lengths 0 and 1, and then remains flat. Plot (b) is titled "Distillation Loss (Error in Matching Trajectories) v/s Distillation Iterations". The y-axis is labeled "Distillation Loss" and ranges from 0.850 to 1.000. The x-axis is labeled "Distillation Iteration" and ranges from 0 to 100. Three lines are plotted: MTT-SSL (blue), MTT-SL (orange), and MTT-SSL (4x batch size) (green). The MTT-SSL line is flat at 1.000. The MTT-SL line starts at approximately 0.950 and decreases linearly to approximately 0.850 at iteration 100. The MTT-SSL (4x batch size) line starts at approximately 0.980, fluctuates between 0.975 and 0.990, and ends at approximately 0.975. Plot (c) is titled "Average Absolute Change in Pixel v/s # Distillation Iterations". The y-axis is labeled "Mean Change in Synthetic Image" and ranges from 0.00 to 0.06. The x-axis is labeled "Distillation Iteration" and ranges from 0 to 100. Three lines are plotted: MTT-SSL (blue), MTT-SL (orange), and MTT-SSL (4x batch size) (green). The MTT-SSL line is flat at 0.00. The MTT-SL line increases linearly from 0.00 to approximately 0.06 at iteration 100. The MTT-SSL (4x batch size) line increases linearly from 0.00 to approximately 0.02 at iteration 100.

Figure 1: Challenges of MTT for SSL (Dataset: CIFAR100 (1%); Arch: 3-layer ConvNet)

other examples. The high variance in gradient over mini-batches in each iteration results in high variance of the trajectories of SSL.

Theoretical Evidence for Higher Variance Gradients in SSL. We now present, in Theorem [4.1,](#page-4-0) theoretical evidence, in a simplified setting, demonstrating that the variance of the gradient of SSL over mini-batches is indeed greater than that of SL, i.e.,  $\text{Var}(\nabla_W L_{SSL}(B)) > \text{Var}(\nabla_W L_{SL}(B)).$ Proof appears in Appendix [C.](#page-16-0) Appendix [D](#page-22-0) presents a more general version of this analysis, when optimizing with synchronous parallel SGD.

<span id="page-4-0"></span>**Theorem 4.1.** Let  $D = \{(x_i, y_i)\}_{i=1}^n$  be a dataset with n examples, where  $x_i$  is the *i*-th input and  $y_i \in \{0,1\}$  *is its corresponding class label. Assume the data*  $x_i$  *are generated using the sparse coding model [Xue et al.](#page-12-12)* [\(2023\)](#page-12-12); *Joshi et al.* [\(2024\)](#page-10-11): for class 0,  $x_i = e_0 + \epsilon_i$ , and for class 1,  $x_i = e_1 + \epsilon_i$ , where  $e_0$  and  $e_1$  are basis vectors and  $\epsilon_i \sim \mathcal{N}(0, \sigma_N I)$  is noise. Note that using mean *class vectors*  $e_0$ ,  $e_1$  *w.l.o.g. models the setting of arbitrary mean class vectors that are orthogonal* to each other. Each class has  $\frac{n}{2}$  examples.

*Consider a linear model*  $f_{\theta}(x) = Wx$ , with W *initialized as I (the identity matrix). The supervised mean squared error (MSE) loss is given by:*

$$
L_{SL}(B) = \frac{1}{|B|} \sum_{i \in B} ||f_{\theta}(x_i) - e_{y_i}||^2,
$$

where  $e_{y_i}$  is the one-hot encoded vector for class  $y_i$ , and  $B$  is a mini-batch.

*The SSL Loss (spectral contrastive loss used here for simplicity of analysis) is given by:*

$$
L_{SSL} = -2\mathbb{E}_{x_1, x_2 \sim \mathcal{A}(x_i)} \left[ f_{\theta}(x_1)^T f_{\theta}(x_2) \right] + \mathbb{E}_{x_i, x_j \in B} \left[ f_{\theta}(x_i)^T f_{\theta}(x_j) \right]^2,
$$

*where*  $A(x_i) = x_i + \epsilon_{aug}$ *, with*  $\epsilon_{aug} \sim \mathcal{N}(0, I)$  *representing augmentation noise.* 

*Under stochastic gradient descent (SGD) with a mini-batch size* B *of 2:*

$$
Var(\nabla_W L_{SL}(B)) < Var(\nabla_W L_{SSL}(B)).
$$

where for a matrix M,  $\text{Var}(M) := \mathbb{E}[\|M - \mathbb{E}[M]\|^2]$  as in [Gower et al.](#page-10-12) [\(2020\)](#page-10-12).

Empirical Evidence for Challenges of Matching SSL Trajectories Due to the high variance in the gradient of SSL objectives, the naive application of MTT to SSL does not succeed. Firstly, the slower convergence caused by high variance gradients necessitates much longer trajectories for both training on real and synthetic data. Secondly, the higher variance of gradients results in greater variance in the weights at the end of trajectories starting from the same initialization (henceforth referred to as *variance of trajectories*), as illustrated theoretically in the simplified setting above. Attempting to match SSL's longer, higher variance trajectories is extremely challenging, as matching such trajectories results in chaotic updates to the synthetic images. Thus, the synthetic images cannot move away from their initialization meaningfully. Fig. [1a](#page-4-1) shows empirically that the variance of SSL trajectories is larger than that of SL trajectories, across different trajectory lengths. Additionally, the

variance of trajectories grows faster, with length of trajectory, for SSL than for SL, exacerbating the problem for longer trajectory matching. Fig. [1b](#page-4-1) compares a simplified distillation using MTT with a single expert trajectory for SSL and SL. Despite extensive hyper-parameter tuning, matching even a single expert trajectory is challenging for SSL, confirmed by the slow decrease of distillation loss. This indicates that the training trajectory on the distilled set is unable to match the training trajectory on the real data for SSL. Fig. [1c](#page-4-1) shows that the difficulty in aligning trajectories is due to the chaotic updates of the synthetic image, as evidenced by the synthetic images being unable to move away from their initialization. To further confirm that the inability to distill effectively is indeed due to the variance of trajectories, we also include a comparison to MTT SSL with 4x larger batch size, which leads to slightly lower variance. Fig. [1a](#page-4-1) confirms that indeed the larger batch size reduces the variance of the trajectories slightly. However, Fig. [1b](#page-4-1) and [1c](#page-4-1) show that reducing the variance of SSL trajectories via larger batch size is insufficient to help distillation since an infeasibly large batch size will likely be required to achieve the necessary low variance trajectories.

Next, we will present our method, MKDT, designed to address the above challenges.

### 4.2 MATCHING KNOWLEDGE DISTILLATION TRAJECTORIES

To reduce the length and variance of SSL trajectories, our key idea is to leverage the recent results in knowledge distillation (KD) [Kim et al.](#page-10-13) [\(2021\)](#page-10-13). We first introduce KD, and then discuss our method, MKDT, Matching Knowledge Distillation Trajectories, that leverages KD to reduce the length and variance of SSL trajectories.

Knowledge Distillation (KD) KD refers to *distilling* the *knowledge* of a larger model (teacher) into a smaller model (student) to achieve similar generalization as the larger model, but with reduced complexity and faster inference times. Here, we rely on the knowledge distillation objective for SSL models, introduced in [Yu et al.](#page-12-11) [\(2019\)](#page-12-11):

<span id="page-5-0"></span>
$$
\mathcal{L}_{\mathcal{KD}} = \mathbb{E}_{x \sim \mathcal{D}_{\text{real}}} \left[ \text{MSE}(f_S(x), f_T(x)) \right],\tag{5}
$$

where  $f_S$  and  $f_T$  represent the student and teacher encoders respectively. [\(Yu et al., 2019\)](#page-12-11) trains student models with the aforementioned KD objective and the original SSL Loss. However, we only minimize the MSE between student and teacher representations to avoid the issues with matching SSL training trajectories (discussed in Sec. [4.1\)](#page-3-2).

Converting SSL to SL trajectories via KD We use the objective presented in equation [5](#page-5-0) i.e. minimizing the MSE between the *representations* of a student and a teacher model trained with SSL. In doing so, we train the student model to match the performance of the teacher trained with SSL. Note that training the student model by minimizing the MSE loss in equation [5](#page-5-0) is a *supervised* objective. Therefore, while the trained student model will produce similar representations to that of the teacher, training with MSE loss is much faster than SSL, as its gradients have a much smaller variance (*c.f.* Fig. [1a\)](#page-4-1). Thus, we can get shorter and lower variance *expert* trajectories from the *student* models trained with KD using the MSE loss, instead of the teacher model trained with SSL. Then, we can generate synthetic examples by matching these shorter and lower variance trajectories, without relying on labels.

Matching KD Trajectories (MKDT) We now describe our method MKDT: Matching Knowledge Distillation Trajectories. MKDT has the following three steps: (1) training a teacher model with SSL, (2) getting expert trajectories by training student models to match representations of the teacher using KD objective, (3) generating synthetic examples by matching the expert trajectories. Below, we discuss each of these steps in more details.

(1) Training the Teacher Model with SSL First, we train the teacher encoder  $f_{\theta_T}$  with  $\mathcal{L}_{\text{SSL}}$  on  $\mathcal{D}_{\text{real}}$ :

$$
\theta_T = \arg\min_{\theta} \left[ \mathcal{L}_{\text{SSL}}(f_{\theta}, \mathcal{D}_{\text{real}}) \right],\tag{6}
$$

In our experiments,  $\mathcal{L}_{\text{SSL}}$  is the BarlowTwins loss function shown in equation [1,](#page-3-1) but our method is agnostic to the choice of SSL algorithm. Since KD with larger models leads to better downstream performance [\(Huang et al., 2022\)](#page-10-14), we use a teacher model that is much larger than the student encoder used for creating the expert trajectories for distillation. For example, in our experiments we use a ResNet-18 as the teacher encoder and a 3 or 4-layer ConvNet as the student encoder.

(2) Getting Expert Trajectories with KD For training the expert trajectories, we encode the full real data with the teacher model and train the student model to minimize the MSE between its <span id="page-6-0"></span>Algorithm 1 MKDT: Matching Knowledge Distillation Trajectories **Require:** *K*: Number of expert trajectories Require: S: Number of distillation steps **Require:**  $M: \#$  of updates between starting and target expert params. **Require:**  $N: \#$  of updates to student network per distillation step. **Require:**  $T^+ < T$ : Maximum start epoch. 1: Train model  $f_{\theta_T}$  using  $\mathcal{L}_{\text{SSL}}$  on  $\mathcal{D}_{\text{real}}$  using augmentations  $\mathcal{A}$ 2:  $\{\tau_i^*\}\leftarrow$  Train K expert trajectories to minimize  $\mathcal{L}_{\mathcal{KD}}(f_{s_i}, f_{\theta_T})$ 3: Initialize distilled data  $\mathcal{D}_{syn}$ ,  $\mathcal{Z}_{syn}$  ∼  $\mathcal{D}_{real}$ ,  $\mathcal{Z}_{real}$ 4: Initialize trainable learning rate  $\alpha_{syn} \coloneqq \alpha_0$  for  $\mathcal{D}_{syn}$ 5: for  $S$  steps do 6:  $\triangleright$  Sample expert trajectory:  $\tau^* \sim {\{\tau_i^*\}}$  with  $\tau^* = {\{\theta_t^*\}}_0^T$ 7:  $\triangleright$  Choose random start epoch,  $t \leq T^+$ 8:  $\triangleright$  Initialize student network with expert params: 9:  $\hat{\theta}_t \coloneqq \theta_t^*$ 10: for  $n = 0 \rightarrow N - 1$  do 11: ▷ Sample a mini-batch of distilled images: 12:  $b_{t+n} \sim \mathcal{D}_{syn}$ <br>13:  $\triangleright$  Update student i 13: ▷ Update student network w.r.t. MSE loss to reference representations: 14:  $\hat{\theta}_{t+n+1} = \hat{\theta}_{t+n} - \alpha_{\text{syn}} \nabla \mathcal{L}_{\text{MSE}} \hat{\theta}_{t+n} (b_{t+n}, \mathcal{Z}_{\text{syn}})$ 15: end for 16: ▷ Compute loss between ending student and expert params: 17:  $\mathcal{L}_{DD}(\mathcal{D}_{syn}) = \|\hat{\theta}_{t+N} - \theta_{t+M}^*\|_2^2 / \|\theta_t^* - \theta_{t+M}^*\|_2^2$ 18:  $\triangleright$  Update  $\mathcal{D}_{syn}$  and  $\alpha_{syn}$  with respect to  $\mathcal{L}_{DD}(\mathcal{D}_{syn})$ 19: end for **Ensure:** distilled data  $\mathcal{D}_{syn}$ ,  $\mathcal{Z}_{syn}$  and learning rate  $\alpha_{syn}$ 

representations and that of the teacher model. We refer to the representations of the real data from the teacher model as *teacher representations* denoted by  $\mathcal{Z}_T = [\cdots f_{\theta_T}(x_i) \cdots], \forall x_i \in \mathcal{D}_{\text{real}}.$  Formally,

$$
\min_{\theta^*} \mathbb{E}_{x_i \in \mathcal{D}_{\text{real}}} \mathcal{L}_{\text{MSE}}(f_{\theta^*}(x_i), [\mathcal{Z}_T]_i). \tag{7}
$$

We train  $K$  such student encoders and save the weights after each epoch of training to generate the *expert* trajectories that we will match in the distillation phase.

(3) Data Distillation by Matching KD Trajectories We now optimize the synthetic data such that training on it results in trajectories that match the aforementioned expert trajectories. First, we initialize  $\mathcal{D}_{syn}$  as a subset of  $\mathcal{D}_{real}$  and  $\mathcal{Z}_{syn}$  as the corresponding teacher representations from  $\mathcal{Z}_T$ . Then, in every distillation iteration, we sample an expert trajectory starting at epoch t, where  $t \leq T^{+}$ , of length  $M$ . We then train on the synthetic data for  $N$  steps by minimizing the MSE between representations of synthetic data from  $f_{\theta_{syn}}$  and  $\mathcal{Z}_{syn}$   $f_{\theta_T}$ . Formally,  $\forall n \in [N]$ ,

$$
\hat{\theta}_{t+n+1} = \hat{\theta}_{t+n} - \alpha_{\text{syn}} \nabla \mathcal{L}_{\text{MSE}}(f_{\hat{\theta}_{t+n}}(\mathcal{D}_{\text{syn}}), \mathcal{Z}_{\text{syn}})
$$
(8)

Now, we compute our distillation loss  $\mathcal{L}_{DD}(\mathcal{D}_{syn})$  (shown in equation [4\)](#page-3-0) using the parameters of the encoder trained on the synthetic data and the encoder trained on the full data, and update the synthetic data and learning rate,  $\mathcal{D}_{syn}$  and  $\alpha_{syn}$ , respectively, to minimize this. Note that  $\mathcal{Z}_{syn}$  remains unchanged. We repeat this distillation for  $S$  iterations. Pseudo-code of MKDT is provided in Alg. [1.](#page-6-0)

**Initializing Synthetic Data** Empirically, we find that initializing  $\mathcal{D}_{syn}$  from the subset of examples from Dreal that have *high loss* across the expert trajectories, leads to better downstream performance than initializing with random subsets. In particular, for all expert trajectories, we use the encoders after 1 epoch of training and use it to compute the MSE loss for all examples  $x_i \in \mathcal{D}_{real}$  i.e.  $\mathcal{L}_{\text{MSE}}(f_{\theta_1^*}(x_i), [\mathcal{Z}_T]_i)$ . We then average the loss for examples across encoders from all expert trajectories and choose the examples with highest loss to initialize our synthetic data. Sec. [5](#page-7-0) compares initializing MKDT with random subsets and such *high loss* subsets.

**Evaluating the Distilled Dataset** For evaluation, we first pre-train the encoder  $f_{\theta_{syn}}$  on the distilled data by minimizing the MSE between the representations of the synthetic data  $\mathcal{D}_{syn}$  and  $\mathcal{Z}_{syn}$  using the distilled learning rate  $\alpha_{syn}$ .

$$
\theta_{\text{syn}} = \underset{\theta}{\arg\min} \mathbb{E}_{x_i \in \mathcal{D}_{\text{syn}}} \mathcal{L}_{\text{MSE}}(f_{\theta}(x), [\mathcal{Z}_{\text{syn}}]_i) \text{ with l.r. } \alpha_{\text{syn}} \tag{9}
$$

<span id="page-7-1"></span>

| Size of Downstream  | Method            | Pre-Training                  |                  |                  | Downstream      |                 |                 |                 |
|---------------------|-------------------|-------------------------------|------------------|------------------|-----------------|-----------------|-----------------|-----------------|
| Labeled Data $(\%)$ |                   | CIFAR10                       | Tiny ImageNet    | CIFAR100         | Aircraft        | <b>CUB2011</b>  | Dogs            | Flowers         |
|                     | No Pre-Training   | $35.84 \pm 1.39$              | $2.52 \pm 0.09$  | $8.01 \pm 0.19$  | $2.43 \pm 0.17$ | $1.27 \pm 0.10$ | $1.92 \pm 0.18$ | $2.02 \pm 0.76$ |
|                     | Random Subset     | $36.35 \pm 0.67$              | $2.41 \pm 0.08$  | $7.42 \pm 0.31$  | $2.41 \pm 0.30$ | $1.16 \pm 0.08$ | $1.90 \pm 0.22$ | $1.99 \pm 0.19$ |
| $1\%$               | <b>SAS</b> Subset | $36.02{\scriptstyle \pm1.52}$ | $2.69 \pm 0.31$  | $7.77 \pm 0.35$  | $2.29 \pm 0.26$ | $1.14 \pm 0.01$ | $1.78 \pm 0.32$ | $2.22 \pm 0.23$ |
|                     | <b>KRR-ST</b>     | $37.19 + 0.49$                | $2.84 \pm 0.17$  | $8.67 \pm 0.41$  | $2.53 + 0.05$   | $1.25 \pm 0.02$ | $1.88 + 0.32$   | $2.42 \pm 0.22$ |
|                     | <b>MKDT</b>       | $44.36 \pm 1.61$              | $3.58 + 0.09$    | $10.58 \pm 0.24$ | $2.58 \pm 0.18$ | $1.37 \pm 0.09$ | $2.11 \pm 0.28$ | $2.52 \pm 0.07$ |
|                     | <b>Full Data</b>  | $58.21 \pm 0.28$              | $4.94 \pm 0.38$  | $14.53 \pm 0.40$ | $2.92 \pm 0.25$ | $1.69 + 0.13$   | $2.40 \pm 0.25$ | $3.23 \pm 0.69$ |
|                     | No Pre-Training   | $46.23 \pm 0.07$              | $5.37 \pm 0.39$  | $16.12 \pm 0.13$ | $5.61 \pm 0.68$ | $1.97 \pm 0.13$ | $2.90 \pm 0.18$ | $5.22 \pm 0.52$ |
|                     | Random Subset     | $46.62{\pm}1.02$              | $5.49 \pm 0.12$  | $15.28 \pm 0.66$ | $5.35 \pm 0.96$ | $1.51 \pm 0.08$ | $2.52 \pm 0.22$ | $3.64 \pm 0.46$ |
| 5%                  | <b>SAS</b> Subset | $46.52 \pm 0.61$              | $5.41 \pm 0.42$  | $15.90 \pm 0.28$ | $5.63 \pm 0.76$ | $1.48 + 0.16$   | $2.69 + 0.21$   | $3.75 \pm 0.10$ |
|                     | <b>KRR-ST</b>     | $46.75 \pm 0.45$              | $6.85 \pm 0.20$  | $16.65 \pm 0.31$ | $5.41 \pm 0.45$ | $1.88 + 0.10$   | $2.76 \pm 0.40$ | $4.52 \pm 0.14$ |
|                     | <b>MKDT</b>       | $53.08 \pm 0.13$              | $7.25 \pm 0.17$  | $19.57 \pm 0.29$ | $5.97 \pm 0.79$ | $2.06 \pm 0.10$ | $3.06 \pm 0.46$ | $4.97 \pm 0.54$ |
|                     | <b>Full Data</b>  | $67.16 \pm 0.43$              | $10.85 \pm 0.16$ | $26.38 \pm 0.52$ | $6.92 \pm 0.61$ | $2.51 \pm 0.08$ | $3.88 + 0.25$   | $6.37 \pm 0.67$ |

#### Table 1: Pre-training on CIFAR10 (2% of Full Data)

We then evaluate the encoder  $f_{\theta_{syn}}$  using  $Err_{f_{\theta_{syn}}}(\mathcal{D}_d)$ , defined in equation [3,](#page-3-3) i.e. the generalization error of linear classifier  $g_{\mathcal{D}_d}$  trained on the representations obtained from encoder  $f_{\theta_{syn}}$  and corresponding labels of downstream task  $\mathcal{D}_d$ .

<span id="page-7-0"></span>

# 5 EXPERIMENTS

<span id="page-7-2"></span>

| Size of Downstream  | Method                                                                                                                                    | Pre-Training                                                                                                                             |                                                                                                                                   |                                                                                                                                          | Downstream                                                                                                                      |                                                                                                                                 |                                                                                                                                 |                                                                                                                                              |
|---------------------|-------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------|
| Labeled Data $(\%)$ |                                                                                                                                           | CIFAR100                                                                                                                                 | Tiny ImageNet                                                                                                                     | CIFAR <sub>10</sub>                                                                                                                      | Aircraft                                                                                                                        | <b>CUB2011</b>                                                                                                                  | Dogs                                                                                                                            | Flowers                                                                                                                                      |
| $1\%$               | No Pre-Training<br>Random Subset<br><b>SAS Subset</b><br><b>High Loss Subset</b><br><b>KRR-ST</b><br><b>MKDT</b> (Rnd Sub)<br><b>MKDT</b> | $8.01 \pm 0.19$<br>$9.20 \pm 0.15$<br>$9.39 \pm 0.18$<br>$10.03 \pm 0.12$<br>$8.31 \pm 0.30$<br>$11.44 \pm 0.36$<br>$12.36 \pm 0.67$     | $2.52 \pm 0.09$<br>$3.16 \pm 0.21$<br>$3.23 \pm 0.19$<br>$3.33 \pm 0.10$<br>$2.73 \pm 0.08$<br>$3.90 \pm 0.20$<br>$4.13 \pm 0.29$ | $35.84 \pm 1.39$<br>$38.03 \pm 1.22$<br>$38.73 \pm 1.48$<br>$39.78 \pm 1.61$<br>$37.19 \pm 0.83$<br>$43.35 \pm 1.08$<br>$44.90 \pm 1.18$ | $2.43 \pm 0.17$<br>$2.41 \pm 0.15$<br>$2.53 \pm 0.04$<br>$2.47 + 0.29$<br>$2.56 + 0.20$<br>$2.53 + 0.22$<br>$2.74 \pm 0.30$     | $1.27 \pm 0.10$<br>$1.43 \pm 0.12$<br>$1.42 \pm 0.04$<br>$1.56 \pm 0.14$<br>$1.29 \pm 0.04$<br>$1.66 \pm 0.13$<br>$1.61 + 0.14$ | $1.92 \pm 0.18$<br>$1.99{\pm0.10}$<br>$2.07 \pm 0.14$<br>$2.13 \pm 0.24$<br>$1.92 \pm 0.11$<br>$2.22 + 0.20$<br>$2.15 \pm 0.39$ | $2.02 \pm 0.76$<br>$2.81 \pm 0.43$<br>$2.95 + 0.37$<br>$2.63 + 0.51$<br>$2.58 + 0.14$<br>$2.63 \pm 1.02$<br>$3.24 \pm 0.44$                  |
|                     | <b>Full Data</b>                                                                                                                          | $21.44 \pm 0.86$                                                                                                                         | $6.80 \pm 0.37$                                                                                                                   | $58.21 \pm 0.81$                                                                                                                         | $3.20 \pm 0.22$                                                                                                                 | $1.79 \pm 0.08$                                                                                                                 | $2.50 \pm 0.27$                                                                                                                 | $3.09 \pm 1.14$                                                                                                                              |
| 5%                  | No Pre-Training<br>Random Subset<br><b>SAS Subset</b><br><b>High Loss Subset</b><br><b>KRR-ST</b><br><b>MKDT</b> (Rnd Sub)<br><b>MKDT</b> | $16.12 \pm 0.13$<br>$17.75 \pm 0.42$<br>$17.94 \pm 0.54$<br>$18.72{\pm}0.21$<br>$16.40 \pm 0.63$<br>$21.71 \pm 0.28$<br>$22.64 \pm 0.42$ | $5.37 \pm 0.39$<br>$6.79 \pm 0.06$<br>$6.71 \pm 0.52$<br>$6.94 \pm 0.34$<br>$6.16 \pm 0.45$<br>$8.01 \pm 0.08$<br>$8.07 \pm 0.16$ | $46.23 \pm 0.07$<br>$48.59 \pm 0.26$<br>$48.69 \pm 0.26$<br>$49.59 \pm 0.34$<br>$47.96 \pm 0.32$<br>$53.08 + 0.19$<br>$54.12 \pm 0.29$   | $5.61 \pm 0.68$<br>$5.66 \pm 0.71$<br>$5.95 \pm 0.88$<br>$5.63 \pm 0.52$<br>$5.54 + 0.97$<br>$6.24 \pm 0.79$<br>$6.68 \pm 0.83$ | $1.97 \pm 0.13$<br>$2.12 \pm 0.23$<br>$2.15 \pm 0.27$<br>$2.58 + 0.13$<br>$2.00 \pm 0.08$<br>$2.53 \pm 0.03$<br>$2.50 \pm 0.16$ | $2.90 \pm 0.18$<br>$3.02 \pm 0.31$<br>$3.22 \pm 0.47$<br>$3.18 + 0.20$<br>$2.95 + 0.25$<br>$3.38 \pm 0.23$<br>$3.25 \pm 0.36$   | $5.22{\scriptstyle \pm0.52}$<br>$5.44 + 0.22$<br>$5.56 \pm 0.43$<br>$6.14 \pm 0.54$<br>$4.69 \pm 0.15$<br>$6.26 \pm 0.22$<br>$6.37 \pm 0.46$ |
|                     | <b>Full Data</b>                                                                                                                          | $35.78 \pm 0.54$                                                                                                                         | $14.11 \pm 0.55$                                                                                                                  | $67.25 \pm 0.49$                                                                                                                         | $7.46 \pm 0.53$                                                                                                                 | $3.00 \pm 0.05$                                                                                                                 | $4.23 \pm 0.21$                                                                                                                 | $8.41 \pm 0.60$                                                                                                                              |

Table 2: Pre-training on CIFAR100 (2% of Full Data)

Table 3: Pre-training on TinyImageNet (2% of Full Data)

<span id="page-7-3"></span>

| Size of Downstream  | Method                                                           | Pre-Training                                                             |                                                                          |                                                                          | Downstream                                                         |                                                                        |                                                                      |                                                                        |
|---------------------|------------------------------------------------------------------|--------------------------------------------------------------------------|--------------------------------------------------------------------------|--------------------------------------------------------------------------|--------------------------------------------------------------------|------------------------------------------------------------------------|----------------------------------------------------------------------|------------------------------------------------------------------------|
| Labeled Data $(\%)$ |                                                                  | Tiny ImageNet                                                            | CIFAR <sub>10</sub>                                                      | CIFAR100                                                                 | Aircraft                                                           | <b>CUB2011</b>                                                         | Dogs                                                                 | <b>Flowers</b>                                                         |
| $1\%$               | No Pre-Training<br>Random Subset<br><b>KRR-ST</b><br><b>MKDT</b> | $2.63 \pm 0.22$<br>$3.03 \pm 0.20$<br>$3.32 \pm 0.22$<br>$3.87 \pm 0.05$ | $30.40 + 1.02$<br>$34.46 + 1.13$<br>$34.24 \pm 0.94$<br>$37.25 \pm 0.47$ | $7.14 \pm 0.35$<br>$7.66 \pm 0.67$<br>$7.84 \pm 0.98$<br>$8.95 \pm 0.34$ | $2.25 + 0.11$<br>$2.27 + 0.22$<br>$2.14 \pm 0.30$<br>$2.30 + 0.19$ | $1.28 + 0.16$<br>$1.24 \pm 0.05$<br>$1.30 \pm 0.08$<br>$1.36 \pm 0.22$ | $1.76 + 0.13$<br>$1.92 + 0.26$<br>$1.95 \pm 0.22$<br>$1.99 + 0.20$   | $2.84 + 0.20$<br>$2.16 + 0.47$<br>$2.21 + 0.33$<br>$2.86 \pm 0.25$     |
|                     | <b>Full Data</b>                                                 | $9.42 \pm 0.36$                                                          | $50.52 \pm 0.87$                                                         | $15.18 \pm 0.37$                                                         | $2.57 + 0.29$                                                      | $1.59 + 0.31$                                                          | $2.20 \pm 0.37$                                                      | $3.29 \pm 0.28$                                                        |
| $5\%$               | No Pre-Training<br>Random Subset<br><b>KRR-ST</b><br><b>MKDT</b> | $5.69{\pm}0.45$<br>$6.76 \pm 0.16$<br>$7.13 + 0.22$<br>$7.99 \pm 0.32$   | $39.91 \pm 0.36$<br>$43.74 + 0.82$<br>$42.44 + 1.85$<br>$45.97 + 0.27$   | $13.32{\pm}0.30$<br>$13.83 + 0.13$<br>$13.85 + 0.72$<br>$16.50 + 0.35$   | $4.46 + 0.81$<br>$4.49 + 0.91$<br>$3.99 + 0.57$<br>$4.66 + 0.70$   | $1.71 \pm 0.06$<br>$1.66 \pm 0.14$<br>$1.77{\pm}0.07$<br>$2.07 + 0.11$ | $2.51 \pm 0.18$<br>$2.67 + 0.31$<br>$2.47 + 0.21$<br>$2.91 \pm 0.10$ | $4.83 \pm 0.32$<br>$4.23 + 0.70$<br>$4.14 \pm 0.71$<br>$5.49 \pm 0.51$ |
|                     | <b>Full Data</b>                                                 | $18.93 \pm 0.34$                                                         | $58.90 + 0.43$                                                           | $26.47{\pm}0.78$                                                         | $5.07 + 0.71$                                                      | $2.47{\pm}0.06$                                                        | $3.85 + 0.19$                                                        | $7.09 \pm 1.01$                                                        |

In this section, we evaluate the downstream generalization of models trained using the synthetic sets distilled by MKDT that are 2% and 5% of the original dataset's size for CIFAR10, CIFAR100 [\(Krizhevsky & Hinton, 2009\)](#page-11-17), TinyImageNet [\(Le & Yang, 2015\)](#page-11-18). We also conduct ablation studies over initialization of the distilled set and the SSL algorithm. Finally, we also consider the generalization of the distilled sets to larger architectures.

<span id="page-8-0"></span>

| Pre-Training | Size of Downstream | Method                                               |                                                                        |                                                          | Downstream Task Accuracy                               |                                                     |                                                     |                                                       |                                                     |
|--------------|--------------------|------------------------------------------------------|------------------------------------------------------------------------|----------------------------------------------------------|--------------------------------------------------------|-----------------------------------------------------|-----------------------------------------------------|-------------------------------------------------------|-----------------------------------------------------|
| Dataset      | Labeled Data (%)   |                                                      | CIFAR10                                                                | CIFAR100                                                 | Tiny ImageNet                                          | Aircraft                                            | <b>CUB2011</b>                                      | Dogs                                                  | <b>Flowers</b>                                      |
| CIFAR10      | 1%                 | Random Subset<br><b>KRR-ST</b><br><b>MKDT</b>        | $37.63 \pm 2.28$<br>$36.69{\pm}0.88$<br>$50.23 \scriptstyle{\pm 1.48}$ | $7.63 \pm 0.13$<br>$8.69 \pm 0.32$<br>$12.33 \pm 0.31$   | $2.63 \pm 0.07$<br>$3.20 \pm 0.23$<br>$4.27 \pm 0.36$  | $2.28 + 0.22$<br>$2.26 \pm 0.13$<br>$3.11 \pm 0.15$ | $1.13 + 0.11$<br>$1.33 + 0.09$<br>$1.59 + 0.07$     | $1.81 \pm 0.18$<br>$1.91 \pm 0.34$<br>$2.26 \pm 0.29$ | $2.05 \pm 0.23$<br>$2.39 + 0.18$<br>$2.42 \pm 0.62$ |
|              | 5%                 | <b>Random Subset</b><br><b>KRR-ST</b><br><b>MKDT</b> | $48.13 \pm 0.35$<br>$47.40 \pm 0.34$<br>$58.37 \pm 0.17$               | $16.06 \pm 0.15$<br>$16.95 \pm 0.53$<br>$23.15 \pm 0.71$ | $5.21 \pm 0.56$<br>$7.10 \pm 0.27$<br>$8.84 \pm 0.20$  | $5.03 \pm 0.88$<br>$5.56 + 0.77$<br>$6.79 \pm 0.88$ | $1.71 \pm 0.12$<br>$1.98 + 0.07$<br>$2.30 \pm 0.14$ | $2.61 + 0.20$<br>$2.78 + 0.16$<br>$3.34 \pm 0.35$     | $3.30 \pm 0.10$<br>$4.38 + 0.04$<br>$5.94 \pm 0.24$ |
| CIFAR100     | 1%                 | Random Subset<br><b>KRR-ST</b><br><b>MKDT</b>        | $42.45 \pm 0.76$<br>$37.86 \pm 1.14$<br>$47.77 + 1.12$                 | $11.55 \pm 0.37$<br>$9.02 \pm 0.24$<br>$13.40 \pm 0.31$  | $4.17 + 0.15$<br>$2.94 \pm 0.13$<br>$4.45 \pm 0.33$    | $2.47 + 0.17$<br>$2.42 + 0.35$<br>$2.93 \pm 0.42$   | $1.51 + 0.12$<br>$1.50 + 0.07$<br>$1.61 \pm 0.02$   | $2.12 \pm 0.18$<br>$1.99 + 0.19$<br>$2.22 + 0.27$     | $2.61 + 0.61$<br>$3.04 \pm 0.36$<br>$2.59 + 1.07$   |
|              | 5%                 | Random Subset<br><b>KRR-ST</b><br><b>MKDT</b>        | $51.92 \pm 0.33$<br>$47.53 \pm 0.11$<br>$56.61 \scriptstyle{\pm 0.58}$ | $20.34 \pm 0.20$<br>$17.24 \pm 0.47$<br>$25.18 \pm 0.67$ | $7.83 \pm 0.24$<br>$6.60 \pm 0.32$<br>$9.12 \pm 0.40$  | $6.06 \pm 0.79$<br>$5.37 + 0.85$<br>$6.66 \pm 0.62$ | $2.27 \pm 0.18$<br>$2.31 + 0.33$<br>$2.66 \pm 0.23$ | $3.22 \pm 0.39$<br>$2.87 \pm 0.27$<br>$3.66 \pm 0.44$ | $5.78 + 0.34$<br>$5.23 + 0.14$<br>$6.93 \pm 0.60$   |
| TinyImageNet | 1%                 | <b>Random Subset</b><br><b>KRR-ST</b><br><b>MKDT</b> | $40.33 + 1.16$<br>$34.27 \pm 1.36$<br>$41.44 \pm 0.85$                 | $9.41 \pm 0.29$<br>$7.54 \pm 0.35$<br>$10.29 \pm 0.38$   | $4.19 \pm 0.19$<br>$3.19 \pm 0.22$<br>$5.09 \pm 0.45$  | $2.23 \pm 0.38$<br>$2.11 + 0.23$<br>$2.16 \pm 0.28$ | $1.44 \pm 0.10$<br>$1.30 + 0.12$<br>$1.29 \pm 0.06$ | $2.06 \pm 0.12$<br>$1.68 + 0.20$<br>$2.02 \pm 0.28$   | $2.77 + 0.24$<br>$2.65 + 0.64$<br>$2.92 \pm 0.49$   |
|              | 5%                 | <b>Random Subset</b><br><b>KRR-ST</b><br><b>MKDT</b> | $48.46 \pm 0.40$<br>$42.82{\pm}0.46$<br>$50.79 \scriptstyle{\pm 0.47}$ | $15.63 \pm 0.62$<br>$13.71 \pm 0.30$<br>$19.25 \pm 0.23$ | $8.99 \pm 0.61$<br>$6.50 \pm 0.23$<br>$10.63 \pm 0.23$ | $4.55 + 0.80$<br>$4.36 + 0.49$<br>$4.88 + 0.65$     | $1.98 + 0.18$<br>$1.97 \pm 0.06$<br>$2.08 + 0.03$   | $2.91 + 0.47$<br>$2.75 \pm 0.37$<br>$2.89 \pm 0.41$   | $5.06 + 0.84$<br>$3.97 \pm 0.14$<br>$5.58 \pm 0.43$ |

#### Table 4: Pre-training with Larger Distilled Set Size (5% of Full Data)

<span id="page-8-1"></span>Table 5: Transfer to Larger Architectures (Pre-Training on CIFAR100 5%, 5% Downsteam Labels)

| Method    | Pre-Training    |                  | Downstream       |                  |                 |                 |                 |                 |
|-----------|-----------------|------------------|------------------|------------------|-----------------|-----------------|-----------------|-----------------|
|           | CIFAR100        | CIFAR10          | TinyImageNet     | Aircraft         | <b>CUB2011</b>  | Dogs            | <b>Flowers</b>  |                 |
| ResNet-10 | No Pre-Training | $1.36 \pm 0.31$  | $13.18 \pm 1.74$ | $1.03 \pm 0.07$  | $1.00 \pm 0.01$ | $0.43 \pm 0.13$ | $0.60 \pm 0.01$ | $0.75 \pm 0.14$ |
|           | Random          | $18.80 \pm 0.58$ | $44.24 \pm 0.85$ | $10.33 \pm 0.16$ | $2.15 \pm 0.34$ | $1.81 \pm 0.16$ | $2.52 \pm 0.29$ | $5.41 \pm 0.61$ |
|           | <b>KRR-ST</b>   | $13.84 \pm 0.78$ | $39.21 \pm 0.55$ | $8.04 \pm 0.52$  | $2.12 \pm 0.15$ | $1.16 \pm 0.05$ | $1.77 \pm 0.14$ | $4.56 \pm 0.42$ |
|           | <b>MKDT</b>     | $23.23 \pm 0.79$ | $49.13 \pm 0.69$ | $13.35 \pm 0.24$ | $1.68 \pm 0.10$ | $1.67 \pm 0.09$ | $2.64 \pm 0.16$ | $6.15 \pm 0.47$ |
| ResNet-18 | No Pre-Training | $1.01 \pm 0.01$  | $10.00 \pm 0.00$ | $0.91 \pm 0.10$  | $1.01 \pm 0.01$ | $0.56 \pm 0.07$ | $0.67 \pm 0.09$ | $0.93 \pm 0.38$ |
|           | Random          | $16.82 \pm 0.69$ | $40.11 \pm 1.16$ | $8.95 \pm 0.23$  | $1.84 \pm 0.25$ | $1.62 \pm 0.06$ | $2.40 \pm 0.25$ | $5.16 \pm 0.59$ |
|           | <b>KRR-ST</b>   | $12.30 \pm 0.83$ | $35.73 \pm 1.07$ | $7.21 \pm 0.35$  | $2.32 \pm 0.39$ | $1.18 \pm 0.16$ | $1.81 \pm 0.14$ | $2.45 \pm 0.12$ |
|           | <b>MKDT</b>     | $21.51 \pm 0.17$ | $46.10 \pm 0.60$ | $11.57 \pm 0.17$ | $2.05 \pm 0.43$ | $1.86 \pm 0.05$ | $2.36 \pm 0.29$ | $5.17 \pm 0.93$ |

Distillation Setup We use ResNet-18 trained with Barlow Twins [\(Zbontar et al., 2021\)](#page-12-5) as the teacher encoder and train  $K = 100$  student encoders (using ConvNets) to generate the expert trajectories. As in previous work [\(Cazenavette et al., 2022;](#page-10-1) [Zhao & Bilen, 2021;](#page-12-2) [Zhao et al., 2021;](#page-12-1) [Chen et al., 2023;](#page-10-15) [Du et al., 2023;](#page-10-5) [Cui et al., 2022\)](#page-10-6), we use a 3-layer ConvNet for the lower resolution CIFAR datasets and a 4-layer ConvNet for the higher resolution TinyImageNet. Exact hyperparamters in Appendix [A.](#page-13-0)

Evaluation Setup To test generalization in presence of limited labeled data, we evaluate encoders pre-trained on the distilled data using linear probe on 1% and 5% of downstream labeled data.

Baselines We compare pre-training with MKDT distilled sets to pre-training with random subsets, SAS subsets [\(Joshi & Mirzasoleiman, 2023\)](#page-10-9), KRR-ST distilled sets [\(Lee et al., 2023\)](#page-11-5) as well as pre-training on the full data. For KRR-ST, we use the provided distilled sets for CIFAR100 and Tiny ImageNet, and distill using the provided code for CIFAR10. We omit SAS for TinyImageNet as this subset was not provided. For distilled sets of size 5%, we consider only Random and MKDT, since other baselines did not provide distilled sets.

Downstream Generalization Performance Table [1](#page-7-1) demonstrates that pre-training on CIFAR10 using MKDT with a 2% distilled set improves performance by 8% on CIFAR10 and 5% on downstream tasks over the KRR-ST baseline. Gains on CIFAR10 are consistent across 1% and 5% labeled data, but improvements on downstream tasks are more pronounced with 5%, indicating MKDT scales well with more labeled data. On CIFAR100, MKDT 2% distilled set improves performance by 6% and 8% on CIFAR100 and downstream tasks, respectively. Additionally, MKDT shows up to 3% improvement on downstream tasks for larger, higher-resolution datasets like TinyImageNet (200K examples, 64x64 resolution), highlighting MKDT's scalability. KRR-ST consistently fails to outperform SSL pre-training on random subsets across all settings. In Appendix [B,](#page-15-0) we verify that this holds for fine-tuning experiments from KRR-ST [\(Lee et al., 2023\)](#page-11-5), affirming MKDT as the only effective DD method for SSL pre-training. Table [4](#page-8-0) shows that pre-training with larger distilled sets (5% of full data) further enhances performance by up to 13%, confirming MKDT scales effectively with distilled set size as well. Table [7](#page-9-0) shows that MKDT outperforms the strongest baseline (random subsets) by 5% on pre-training and 7% on downstream tasks when using 10% and 50% labeled data.

<span id="page-9-1"></span>

| Pre-Training  | Size of Downstream | Method                                        |                                                          |                                                        | Downstream Task Accuracy                              |                                                       |                                                       |                                                     |                                                       |
|---------------|--------------------|-----------------------------------------------|----------------------------------------------------------|--------------------------------------------------------|-------------------------------------------------------|-------------------------------------------------------|-------------------------------------------------------|-----------------------------------------------------|-------------------------------------------------------|
| Dataset       | Labeled Data (%)   |                                               | CIFAR10                                                  | CIFAR100                                               | Tiny ImageNet                                         | Aircraft                                              | <b>CUB2011</b>                                        | Dogs                                                | Flowers                                               |
| 1%<br>CIFAR10 |                    | Random Subset<br><b>KRR-ST</b><br><b>MKDT</b> | $35.20 \pm 1.12$<br>$36.90 \pm 1.30$<br>$40.77 \pm 1.05$ | $7.35 \pm 0.28$<br>$8.38 + 0.17$<br>$9.17 \pm 0.13$    | $2.29 \pm 0.14$<br>$2.95 \pm 0.12$<br>$3.06 \pm 0.16$ | $2.21 \pm 0.09$<br>$2.45 \pm 0.13$<br>$2.69 \pm 0.21$ | $1.19 + 0.06$<br>$1.19 + 0.09$<br>$1.35 \pm 0.06$     | $1.83 + 0.16$<br>$1.87 + 0.18$<br>$2.02 \pm 0.23$   | $1.83 \pm 0.24$<br>$2.35 + 0.06$<br>$1.88 + 0.22$     |
|               | 5%                 | Random Subset<br><b>KRR-ST</b><br><b>MKDT</b> | $45.69{\pm}0.43$<br>$46.87{\pm}0.52$<br>$51.77 \pm 0.25$ | $15.09 + 0.39$<br>$16.29{\pm}0.37$<br>$18.07 \pm 0.52$ | $5.71 \pm 0.15$<br>$6.31 \pm 0.43$<br>$6.55 \pm 0.23$ | $5.21 + 1.04$<br>$5.31 + 0.63$<br>$5.90 \pm 0.76$     | $1.52 \pm 0.18$<br>$1.89 \pm 0.14$<br>$1.89 \pm 0.15$ | $2.48 + 0.16$<br>$2.66 \pm 0.18$<br>$2.98 + 0.36$   | $3.36 + 0.20$<br>$4.36 \pm 0.16$<br>$4.09 \pm 0.28$   |
| CIFAR100      | 1%                 | Random Subset<br><b>KRR-ST</b><br><b>MKDT</b> | $34.67{\pm}0.89$<br>$36.57 \pm 1.02$<br>$39.59 \pm 1.19$ | $7.35 \pm 0.54$<br>$8.38 + 0.36$<br>$9.44 \pm 0.37$    | $2.29 \pm 0.16$<br>$3.01 + 0.22$<br>$3.07 \pm 0.08$   | $2.23 \pm 0.21$<br>$2.41 + 0.15$<br>$2.60 \pm 0.23$   | $1.10 + 0.06$<br>$1.28 + 0.02$<br>$1.33 + 0.11$       | $1.78 + 0.26$<br>$1.71 \pm 0.30$<br>$1.93 \pm 0.27$ | $1.77{\pm}0.13$<br>$1.98 \pm 0.24$<br>$2.49 \pm 0.06$ |
|               | 5%                 | Random Subset<br><b>KRR-ST</b><br><b>MKDT</b> | $45.67 \pm 0.69$<br>$46.76 \pm 0.50$<br>$49.87 \pm 0.75$ | $15.11 \pm 0.44$<br>$15.75 + 0.46$<br>$18.47 \pm 0.21$ | $5.21 \pm 0.29$<br>$6.17 \pm 0.20$<br>$6.65 \pm 0.21$ | $5.21 \pm 0.67$<br>$5.43 + 0.65$<br>$5.56 \pm 0.86$   | $1.51 + 0.14$<br>$1.93 + 0.06$<br>$1.93 \pm 0.13$     | $2.54 \pm 0.19$<br>$2.61 + 0.25$<br>$2.98 + 0.32$   | $3.37{\pm}0.47$<br>$3.55 + 0.29$<br>$4.83 \pm 0.27$   |

| Table 6: Ablation over SSL Algorithm (SimCLR), Distilled Set Size 2% |  |  |  |
|----------------------------------------------------------------------|--|--|--|
|                                                                      |  |  |  |

Table 7: Larger Labeled Data Fractions (10%, 50%), Distilled Set Size 2%

<span id="page-9-0"></span>

| Pre-Training | Size of Downstream | Method                                               |                                                          |                                                          | Downstream Task Accuracy                                 |                                                          |                                                       |                                                       |                                                          |
|--------------|--------------------|------------------------------------------------------|----------------------------------------------------------|----------------------------------------------------------|----------------------------------------------------------|----------------------------------------------------------|-------------------------------------------------------|-------------------------------------------------------|----------------------------------------------------------|
| Dataset      | Labeled Data (%)   |                                                      | CIFAR <sub>10</sub>                                      | CIFAR100                                                 | Tiny ImageNet                                            | Aircraft                                                 | <b>CUB2011</b>                                        | Dogs                                                  | <b>Flowers</b>                                           |
| CIFAR10      | 10%                | <b>Random Subset</b><br><b>KRR-ST</b><br><b>MKDT</b> | $51.21 \pm 0.38$<br>$51.02 \pm 0.53$<br>$56.88 \pm 0.85$ | $19.75 \pm 0.36$<br>$21.01 \pm 0.14$<br>$24.61 \pm 0.42$ | $8.04 \pm 0.45$<br>$8.95 \pm 0.26$<br>$9.76 \pm 0.28$    | $7.42 \pm 0.43$<br>$7.91 \pm 0.76$<br>$8.96 \pm 0.71$    | $2.15 \pm 0.16$<br>$2.44 \pm 0.12$<br>$2.78 \pm 0.22$ | $3.10 \pm 0.30$<br>$3.54 \pm 0.29$<br>4.37 $\pm$ 0.37 | $5.22 \pm 0.96$<br>$6.82 \pm 0.64$<br>$7.38 \pm 1.00$    |
|              | 50%                | <b>Random Subset</b><br><b>KRR-ST</b><br><b>MKDT</b> | $57.18 \pm 0.63$<br>$58.09 \pm 0.07$<br>$63.63 \pm 0.17$ | $26.86 \pm 0.36$<br>$29.01 \pm 0.30$<br>34.06 $\pm$ 0.39 | $15.16 \pm 0.03$<br>$15.94 \pm 0.31$<br>$17.57 \pm 0.37$ | $16.11 \pm 0.90$<br>$17.60 \pm 1.04$<br>$19.43 \pm 0.93$ | $4.18 \pm 0.05$<br>$5.01 \pm 0.34$<br>$5.43 \pm 0.22$ | $6.17 \pm 0.08$<br>$6.81 \pm 0.35$<br>$7.63 \pm 0.17$ | $12.98 \pm 0.35$<br>$15.92 \pm 0.64$<br>$16.89 \pm 0.37$ |
| CIFAR100     | 10%                | Random Subset<br><b>KRR-ST</b><br><b>MKDT</b>        | $52.23 \pm 0.38$<br>$52.40 \pm 0.73$<br>57.67 $\pm$ 0.33 | $21.56 \pm 0.62$<br>$21.39 \pm 0.16$<br>$27.28 \pm 0.18$ | $8.57 \pm 0.43$<br>$8.21 \pm 0.07$<br>$11.05 \pm 0.50$   | $8.01 \pm 0.61$<br>$7.64 \pm 0.36$<br>$9.25 \pm 0.70$    | $2.73 \pm 0.17$<br>$2.34 \pm 0.12$<br>$3.42 \pm 0.16$ | $3.94 \pm 0.28$<br>$3.76 \pm 0.25$<br>$4.43 \pm 0.38$ | $7.50 \pm 1.33$<br>$6.52 \pm 1.28$<br>$9.35 \pm 1.28$    |
|              | 50%                | <b>Random Subset</b><br><b>KRR-ST</b><br><b>MKDT</b> | $60.39 \pm 0.17$<br>$58.57 \pm 0.78$<br>$65.85 \pm 0.33$ | $31.62 \pm 0.32$<br>$29.46 \pm 0.81$<br>38.09 $\pm$ 0.35 | $16.02 \pm 0.14$<br>$15.70 \pm 0.07$<br>$17.46 \pm 0.24$ | $18.10 \pm 0.12$<br>$15.89 \pm 0.54$<br>$20.73 \pm 0.24$ | $5.65 \pm 0.18$<br>$4.82 \pm 0.41$<br>$6.67 \pm 0.05$ | $7.37 \pm 0.23$<br>$7.00 \pm 0.18$<br>$8.48 \pm 0.21$ | $16.18 \pm 0.31$<br>$15.07 \pm 0.46$<br>$21.15 \pm 0.50$ |
| TinyImageNet | 10%                | <b>Random Subset</b><br><b>KRR-ST</b><br><b>MKDT</b> | $44.08 \pm 1.91$<br>$45.48 \pm 0.84$<br>49.33 $\pm$ 0.49 | $16.43 \pm 0.12$<br>$17.02 \pm 0.26$<br>$20.19 \pm 0.45$ | $8.84 \pm 0.85$<br>$8.88 \pm 0.41$<br>$10.89 \pm 0.63$   | $5.57 \pm 0.61$<br>$5.29 \pm 0.16$<br>$6.61 \pm 0.28$    | $2.12 \pm 0.30$<br>$2.08 \pm 0.21$<br>$2.47 \pm 0.18$ | $3.07 \pm 0.17$<br>$3.21 \pm 0.15$<br>$3.86 \pm 0.36$ | $6.51 \pm 1.27$<br>$6.10 \pm 1.44$<br>$7.33 \pm 1.34$    |
|              | 50%                | <b>Random Subset</b><br><b>KRR-ST</b><br><b>MKDT</b> | $47.35 \pm 0.92$<br>$48.16 \pm 1.18$<br>$51.98 \pm 0.44$ | $19.28 \pm 0.61$<br>$20.01 \pm 0.40$<br>$24.41 \pm 0.39$ | $14.01 \pm 0.38$<br>$13.59 \pm 0.47$<br>$16.99 \pm 0.25$ | $8.74 \pm 0.78$<br>$8.66 \pm 1.29$<br>$10.84 \pm 0.25$   | $3.61 \pm 0.31$<br>$3.46 \pm 0.22$<br>4.44 $\pm$ 0.28 | $5.18 \pm 0.29$<br>$4.98 \pm 0.31$<br>$6.22 \pm 0.15$ | $13.60 \pm 1.13$<br>$15.12 \pm 0.37$<br>$16.47 \pm 0.46$ |

Ablations We perform ablations over two factors: 1) initialization and 2) SSL algorithm. Table [2](#page-7-2) presents results for pre-training with MKDT using random subset initialization, as well as results for pre-training directly on the high-loss subset initialization used by MKDT. Interestingly, the KD objective for SSL pre-training benefits slightly more from the high-loss subset than from random subsets. Consequently, MKDT initialized from the high-loss subset performs better than when initialized from a random subset. Table [6](#page-9-1) shows results for MKDT using a teacher model trained with SimCLR [\(Chen et al., 2020\)](#page-10-3) instead of BarlowTwins. Specifically, we train a ResNet-18 for 400 epochs using SimCLR. Here too, MKDT achieves approximately 6% higher performance compared to random subsets across downstream datasets. This confirms that MKDT generalizes across different SSL training algorithms.

Generalization to Larger Architectures Table [5](#page-8-1) compares CIFAR100 5% size MKDT distilled set to 5% size random subsets, using the larger ResNet-10 and ResNet-18 architectures. Across all downstream tasks, we confirm that MKDT distilled sets outperform baselines even when using larger architectures. Surprisingly, the larger ResNet-18 slightly under-performs the smaller ResNet-10. This trend is observed across all baselines, including no pre-training. We conjecture this is due to larger models requiring a lot more data to be able to use their extra capacity to surpass their smaller counterparts.

## 6 CONCLUSION

To conclude, we propose MKDT, the first effective approach for dataset distillation for SSL pretraining. We demonstrated the challenges of naïvely adapting previous supervised distillation method and showed how knowledge distillation with trajectory matching can remedy these problems. Empirically, we showed up to 13% improvement in downstream accuracy when pre-training with MKDT distilled sets over the next best baseline. Thus, we enable highly data-efficient SSL pre-training.

Acknowledgments This research was partially supported by the National Science Foundation CA-REER Award 2146492, and an Amazon PhD fellowship.

#### REFERENCES

- <span id="page-10-1"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 4750–4759, 2022.
- <span id="page-10-3"></span>Ting Chen, Simon Kornblith, Mohammad Norouzi, and Geoffrey Hinton. A simple framework for contrastive learning of visual representations, 2020.
- <span id="page-10-15"></span>Xuxi Chen, Yu Yang, Zhangyang Wang, and Baharan Mirzasoleiman. Data distillation can be like vodka: Distilling more times for better quality. *International Conference on Representation Learning (ICLR)*, 2023.
- <span id="page-10-10"></span>Yuntao Chen, Naiyan Wang, and Zhaoxiang Zhang. Darkrank: Accelerating deep metric learning via cross sample similarities transfer, 2017.
- <span id="page-10-8"></span>C Coleman, C Yeh, S Mussmann, B Mirzasoleiman, P Bailis, P Liang, J Leskovec, and M Zaharia. Selection via proxy: Efficient data selection for deep learning. In *International Conference on Learning Representations (ICLR)*, 2020.
- <span id="page-10-6"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. *arXiv preprint arXiv:2211.10586*, 2022.
- <span id="page-10-4"></span>Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE Conference on Computer Vision and Pattern Recognition*, pp. 248–255, 2009. doi: 10.1109/CVPR.2009.5206848.
- <span id="page-10-2"></span>Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning*, pp. 5378–5396. PMLR, 2022.
- <span id="page-10-5"></span>Jiawei Du, Yidi Jiang, Vincent Y. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation, 2023.
- <span id="page-10-16"></span>Wele Gedara Chaminda Bandara, Celso M De Melo, and Vishal M Patel. Guarding barlow twins against overfitting with mixed samples. *arXiv e-prints*, pp. arXiv–2312, 2023.
- <span id="page-10-12"></span>Robert M. Gower, Mark Schmidt, Francis Bach, and Peter Richtarik. Variance-reduced methods for machine learning, 2020. URL <https://arxiv.org/abs/2010.00892>.
- <span id="page-10-7"></span>Geoffrey Hinton, Oriol Vinyals, and Jeff Dean. Distilling the knowledge in a neural network, 2015.
- <span id="page-10-14"></span>Tao Huang, Shan You, Fei Wang, Chen Qian, and Chang Xu. Knowledge distillation from a stronger teacher, 2022.
- <span id="page-10-9"></span>Siddharth Joshi and Baharan Mirzasoleiman. Data-efficient contrastive self-supervised learning: Most beneficial examples for supervised learning contribute the least. In Andreas Krause, Emma Brunskill, Kyunghyun Cho, Barbara Engelhardt, Sivan Sabato, and Jonathan Scarlett (eds.), *Proceedings of the 40th International Conference on Machine Learning*, volume 202 of *Proceedings of Machine Learning Research*, pp. 15356–15370. PMLR, 23–29 Jul 2023. URL <https://proceedings.mlr.press/v202/joshi23b.html>.
- <span id="page-10-11"></span>Siddharth Joshi, Arnav Jain, Ali Payani, and Baharan Mirzasoleiman. Data-efficient contrastive language-image pretraining: Prioritizing data quality over quantity. In Sanjoy Dasgupta, Stephan Mandt, and Yingzhen Li (eds.), *Proceedings of The 27th International Conference on Artificial Intelligence and Statistics*, volume 238 of *Proceedings of Machine Learning Research*, pp. 1000–1008. PMLR, 02–04 May 2024. URL [https://proceedings.mlr.press/v238/](https://proceedings.mlr.press/v238/joshi24a.html) [joshi24a.html](https://proceedings.mlr.press/v238/joshi24a.html).
- <span id="page-10-0"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *Proceedings of the 39th International Conference on Machine Learning*, 2022.
- <span id="page-10-13"></span>Taehyeon Kim, Jaehoon Oh, NakYil Kim, Sangwook Cho, and Se-Young Yun. Comparing kullbackleibler divergence and mean squared error loss in knowledge distillation, 2021.

- <span id="page-11-15"></span>Soroush Abbasi Koohpayegani, Ajinkya Tejankar, and Hamed Pirsiavash. Compress: Self-supervised learning by compressing representations, 2020.
- <span id="page-11-17"></span>Alex Krizhevsky and Geoffrey Hinton. Learning multiple layers of features from tiny images. Technical Report 0, University of Toronto, Toronto, Ontario, 2009.
- <span id="page-11-18"></span>Ya Le and Xuan S. Yang. Tiny imagenet visual recognition challenge. 2015. URL [https:](https://api.semanticscholar.org/CorpusID:16664790) [//api.semanticscholar.org/CorpusID:16664790](https://api.semanticscholar.org/CorpusID:16664790).
- <span id="page-11-5"></span>Dong Bok Lee, Seanie Lee, Joonho Ko, Kenji Kawaguchi, Juho Lee, and Sung Ju Hwang. Selfsupervised dataset distillation for transfer learning, 2023.
- <span id="page-11-1"></span>Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *Proceedings of the 39th International Conference on Machine Learning*, pp. 12352–12364, 2022.
- <span id="page-11-4"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *Advances in Neural Information Processing Systems*, 2022.
- <span id="page-11-8"></span>Raphael Gontijo Lopes, Stefano Fenu, and Thad Starner. Data-free knowledge distillation for deep neural networks, 2017. URL <https://arxiv.org/abs/1710.07535>.
- <span id="page-11-11"></span>Baharan Mirzasoleiman, Jeff Bilmes, and Jure Leskovec. Coresets for data-efficient training of machine learning models. In *International Conference on Machine Learning*, pp. 6950–6960. PMLR, 2020.
- <span id="page-11-3"></span>Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridgeregression. In *International Conference on Learning Representations*, 2021. URL [https:](https://openreview.net/forum?id=l-PrrQrK0QR) [//openreview.net/forum?id=l-PrrQrK0QR](https://openreview.net/forum?id=l-PrrQrK0QR).
- <span id="page-11-13"></span>Wonpyo Park, Dongju Kim, Yan Lu, and Minsu Cho. Relational knowledge distillation, 2019.
- <span id="page-11-14"></span>Nikolaos Passalis and Anastasios Tefas. Learning deep representations with probabilistic knowledge transfer. In *Proceedings of the European Conference on Computer Vision (ECCV)*, September 2018.
- <span id="page-11-10"></span>Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training. *Advances in Neural Information Processing Systems*, 34: 20596–20607, 2021.
- <span id="page-11-16"></span>Joshua Robinson, Ching-Yao Chuang, Suvrit Sra, and Stefanie Jegelka. Contrastive learning with hard negative samples, 2021.
- <span id="page-11-12"></span>Adriana Romero, Nicolas Ballas, Samira Ebrahimi Kahou, Antoine Chassang, Carlo Gatta, and Yoshua Bengio. Fitnets: Hints for thin deep nets, 2015.
- <span id="page-11-6"></span>Noveen Sachdeva and Julian McAuley. Data distillation: A survey, 2023.
- <span id="page-11-0"></span>Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pp. 17097–17107, 2023.
- <span id="page-11-7"></span>Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized largescale data condensation via various backbone and statistical matching, 2024. URL [https:](https://arxiv.org/abs/2311.17950) [//arxiv.org/abs/2311.17950](https://arxiv.org/abs/2311.17950).
- <span id="page-11-9"></span>Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J. Gordon. An Empirical Study of Example Forgetting During Deep Neural Network Learning, November 2019. URL <http://arxiv.org/abs/1812.05159>. arXiv:1812.05159 [cs, stat].
- <span id="page-11-2"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 12196–12205, 2022a.

- <span id="page-12-6"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features, 2022b.
- <span id="page-12-0"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-12-12"></span>Yihao Xue, Siddharth Joshi, Eric Gan, Pin-Yu Chen, and Baharan Mirzasoleiman. Which features are learnt by contrastive learning? on the role of simplicity bias in class collapse and feature suppression, 2023.
- <span id="page-12-9"></span>Yu Yang, Hao Kang, and Baharan Mirzasoleiman. Towards sustainable learning: Coresets for data-efficient deep learning. *International Conference on Machine Learning (ICML)*, 2023.
- <span id="page-12-7"></span>Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-12-11"></span>Lu Yu, Vacit Oguz Yazici, Xialei Liu, Joost van de Weijer, Yongmei Cheng, and Arnau Ramisa. Learning metrics from teachers: Compact networks for image embedding, 2019.
- <span id="page-12-10"></span>Sergey Zagoruyko and Nikos Komodakis. Paying more attention to attention: Improving the performance of convolutional neural networks via attention transfer, 2017.
- <span id="page-12-5"></span>Jure Zbontar, Li Jing, Ishan Misra, Yann LeCun, and Stéphane Deny. Barlow twins: Self-supervised learning via redundancy reduction, 2021.
- <span id="page-12-2"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pp. 12674–12685. PMLR, 2021.
- <span id="page-12-3"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision (WACV)*, 2023.
- <span id="page-12-1"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021.
- <span id="page-12-8"></span>Muxin Zhou, Zeyuan Yin, Shitong Shao, and Zhiqiang Shen. Self-supervised dataset distillation: A good compression is all you need, 2024.
- <span id="page-12-4"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *Advances in Neural Information Processing Systems*, 2022.

<span id="page-13-0"></span>

# A EXPERIMENT DETAILS

<span id="page-13-2"></span>

## A.1 ADDITIONAL DETAILS FOR EXPERIMENTS IN TABLES 1, 2, 3, 4, 5, 6, 7

Training the Teacher Model Using SSL We trained the teacher model using BarlowTwins [\(Zbontar](#page-12-5) [et al., 2021\)](#page-12-5) using the training setup ResNet18 specified in [\(Gedara Chaminda Bandara et al., 2023\)](#page-10-16). We used the Adam optimizer with batch size 256, learning rate 0.01, cosine annealing learning rate schedule, and weight decay 10<sup>-6</sup>. The feature dimension is 1024. Finally, we use the pre-projection head representation of the trained model for teacher representation, and its dimension is of size 512.

Training *Expert* Trajectories Using KD We trained 100 expert trajectories for each dataset with random initialization of the network for 20 epochs, using Stochastic Gradient Descent with learning rate 0.1, momentum 0.9, and weight decay 1e-4. Similar to other DD works [\(Cazenavette et al., 2022;](#page-10-1) [Lee et al., 2023\)](#page-11-5), we used depth 4 ConvNet for Tiny ImageNet and depth 3 ConvNet for both CIFAR 10 and CIFAR 100. We did not apply any augmentation except normalization, and did not apply the ZCA-whitening.

<span id="page-13-1"></span>Distillation Hyperparameters We distilled 2% of CIFAR 10, CIFAR 100, and Tiny ImageNet. We used SGD for optimizing the synthetic images with batch size 256, momentum 0.5. We distilled CIFAR 10 and CIFAR 100 with depth 3 ConvNet and Tiny ImageNet with depth 4 ConvNet. We initialize the synthetic learning rate as 0.1 and used SGD with learning rate  $10^{-4}$  and momentum 0.5 to update it. We distilled the datasets for 5000 iterations and evaluated their performance for all the experiments except those in Table [5,](#page-8-1) where we use the distilled dataset after 1000 iterations. The other hyper-parameters are recorded in Table [8.](#page-13-1)

Table 8: MKDT Hyperparameters on 2% Distilled Set

|                         | CIFAR10   | CIFAR100  | TinyImageNet |
|-------------------------|-----------|-----------|--------------|
| Percentage Distilled    | 2%        | 2%        | 2%           |
| Model                   | ConvNetD3 | ConvNetD3 | ConvNetD4    |
| Synthetic Steps $(N)$   | 40        | 40        | 10           |
| Expert Epochs $(M)$     |           |           |              |
| Max Start Epoch $(T^+)$ |           |           |              |
| Learning Rate (Pixels)  | 103       | 103       | 105          |

Pre-training on Synthetic Data For the synthetic data, we pre-train them using the MSE loss with their learned representation for 20 epochs using SGD with batch size 256, their distilled learning rate, momentum 0.9, and weight decay  $10^{-4}$ . We use a depth 3 ConvNet for CIFAR 10 and CIFAR 100, and a depth 4 ConvNet for Tiny ImageNet. For distilled datasets, we use the synthetic learning rate  $\alpha_{syn}$ . For other datasets (e.g., random subset), we use the same setting except a learning rate of 0.1.

Evaluation We use the models' penultimate layer's representations (as is standard in contrastive learning [\(Zbontar et al., 2021;](#page-12-5) [Chen et al., 2020\)](#page-10-3)) of the downstream task's training set and train a linear classifier, using LBFGS with 100 iterations and regularization weight  $10^{-3}$ . We then use the pre-projection head representations of the test set of the downstream task and evaluate using the aforementioned linear classifer to report the downstream test accuracy.

Using SimCLR for Obtaining Teacher Representation. We conducted an ablation study using a teacher model trained with SimCLR [\(Chen et al., 2020\)](#page-10-3) instead of Barlow Twins [\(Zbontar et al.,](#page-12-5) [2021\)](#page-12-5) for CIFAR 10 and CIFAR 100. The experiment steps are similar to the ones in [A.1.](#page-13-2) During the "Training the Teacher Model Using SSL" step , we used the Adam optimizer with batch size 512, learning rate 0.001, and weight decay 10<sup>-6</sup> to train a ResNet18 along with a 2-layer linear projection head for 400 epochs. The projection head included Batch Normalization and ReLU after the first layer, and Batch Normalization after the second layer, projecting to 128 dimensions. Then, we used the pre-projection head representation of the trained model for getting the teacher representation of size 512. The other steps are the same as the one in [A.1.](#page-13-2) Table [6](#page-9-1) shows that MKDT consistently outperforms the random subset across all downstream datasets and various sizes of labeled data, highlighting the method's generalizability to other contrastive learning methods.

Scaling the Method to Larger Distillation Sets. In addition to distilling 2% subsets, we also conducted experiments distilling 5% subsets of CIFAR10, CIFAR100, and TinyImageNet to evaluate the generalizability of the method to larger distillation sets. Table [4](#page-8-0) shows the scalability of the method to larger distilled set sizes. The experiments procedure are the same as the ones in [A.1](#page-13-2) except

|                         | CIFAR10   | CIFAR100  | TinyImageNet |
|-------------------------|-----------|-----------|--------------|
| Percentage Distilled    | 5%        | 5%        | 5%           |
| Model                   | ConvNetD3 | ConvNetD3 | ConvNetD4    |
| Synthetic Steps $(N)$   | 40        | 40        | 10           |
| Expert Epochs $(M)$     | 2         | 2         | 2            |
| Max Start Epoch $(T^+)$ | 5         | 5         | 2            |
| Learning Rate (Pixels)  | 104       | 104       | 105          |

<span id="page-14-0"></span>Table 9: MKDT Hyperparameters on 5% Distilled Set

that we use different distillation hyperparameters for the 5% distilled set. The hyperparameters are summarized in Table [9.](#page-14-0)

Scaling the Method to Larger Downstream Labeled Dataset Sizes. We evaluated the performance for CIFAR 10, CIFAR 100, and TinyImageNet on larger downstream labeled data sizes, specifically 10% and 50% labeled data sizes, using the 2% distilled set obtained with the method illustrated in [A.1.](#page-13-2) As shown in Table [7,](#page-9-0) MKDT continues to outperform random subset across all downstream datasets and data sizes, demonstrating its scalability with larger labeled data sizes.

Details for KRR-ST [Lee et al.](#page-11-5) [\(2023\)](#page-11-5) We use the code and hyper-parameters provided in [\(Lee et al.,](#page-11-5) [2023\)](#page-11-5). As the original paper did not provide the results and the hyperparameters for CIFAR 10, we use the same hyperparameters as CIFAR 100 to distill CIFAR 10. In particular, this invovles using BarlowTwins ResNet-18 as the teacher model as well.

## A.2 ADDITIONAL DETAILS ON EXPERIMENTS IN FIG. 1

For the experiment in Figure [1a,](#page-4-1) we train 5 trajectories of each of MTT SSL and MTT SL for CIFAR 100 using the same random initialization of the network, respectively. For MTT SSL, we train the models with the Adam optimizer with batch size 1024, learning rate 0.001, and weight decay  $10^{-6}$ . For MTT SL, we train the model with SGD with batch size 256, learning rate 0.01, momentum 0, and no weight decay.

For both of the experiments in Figure [1b](#page-4-1) and [1c,](#page-4-1) we distill the dataset using MTT SL with image learning rate 1000, max start epoch 0, synthetic steps 20, and expert epochs 4. We distill using MTT SLL with image learning rate 1000, max start epoch 0, synthetic steps 10, and expert epochs 2. We distilled them for 100 iterations and record the change in the loss function and the average absolute change in pixels.

<span id="page-15-0"></span>

# B ADDITIONAL COMPARISON OF RANDOM SUBSET SSL PRE-TRAINING TO KRR-ST

This experiment is conducted in the setting of [Lee et al.](#page-11-5) [\(2023\)](#page-11-5) for pre-training on CIFAR100. This experiment pre-trains on a distilled set / subset of 2% the size of CIFAR100 and then evaluates on downstream tasks by finetuning the entire network with the entire labeled dataset for the downstream task. KRR-ST compares with the random subset baseline by pre-training using SL. However, we use SSL on the random subset as a baseline instead. Here, we show that, in fact, even in the finetuning setting reported by [Lee et al.](#page-11-5) [\(2023\)](#page-11-5), KRR-ST cannot outperform SSL pre-training on random real images. The baseline reported as 'Random' in [Lee et al.](#page-11-5) [\(2023\)](#page-11-5) refers to SL pre-training as opposed to SSL pre-training.

| Method                                               | Pre-Training                  |                              | Downstream                     |                                       |                                       |                    |                               |
|------------------------------------------------------|-------------------------------|------------------------------|--------------------------------|---------------------------------------|---------------------------------------|--------------------|-------------------------------|
|                                                      | CIFAR100                      | CIFAR <sub>10</sub>          | Aircraft                       | Cars                                  | <b>CUB2011</b>                        | Dogs               | <b>Flowers</b>                |
| No pre-training (from Lee et al. (2023))             | $64.95 + 0.21$                | $87.34_{\pm0.13}$            | $34.66{\scriptstyle \pm0.39}$  | $19.43_{\pm0.14}$ $18.46_{\pm0.11}$   |                                       | $22.31_{\pm 0.22}$ | $58.75 + 0.41$                |
| Random (Supervised Learning (from Lee et al. (2023)) | $65.23 + 0.12$                | $87.55 + 0.19$               | $33.99 + 0.45$                 | $19.77_{\pm0.21}$                     | $18.18_{\pm 0.21}$                    | $21.69_{\pm 0.18}$ | $59.31 + 0.27$                |
| $KRR-ST$ (from Lee et al. $(2023)$ )                 | $66.81 + 0.11$                | $88.72 + 0.11$               | $41.54 \scriptstyle{\pm 0.37}$ |                                       | $28.68_{\pm 0.32}$ $25.30_{\pm 0.37}$ | $26.39_{\pm 0.08}$ | $67.88 + 0.18$                |
| Random (SSL Pre-training)                            | $66.44{\scriptstyle \pm0.14}$ | $88.74{\scriptstyle\pm0.20}$ | $42.02 \pm 0.06$               | $28.75_{\pm 0.23}$ $25.12_{\pm 0.19}$ |                                       | $26.57_{\pm0.22}$  | $68.21{\scriptstyle \pm0.40}$ |

|  | Table 10: Pre-training on CIFAR100 |  |
|--|------------------------------------|--|
|--|------------------------------------|--|

<span id="page-16-0"></span>

## C PROOF FOR THEOREM 4.1

*Proof.* To analyze the variance of the gradient for both SL and SSL, we will conduct a case analysis. There are 3 possible unique cases for constructing the mini-batch. Assuming  $n$  is extremely large s.t.  $\frac{n/2}{n} \approx \frac{n/2-1}{n}$  $\frac{2-1}{n}$ , we have:

- 1. Both examples are from class 0 with probability =  $\frac{1}{4}$
- 2. Both examples are from class 1 with probability =  $\frac{1}{4}$
- 3. 1 example from each class with probability =  $\frac{1}{2}$

Let  $x_1, y_1$  refer to example 1 and its corresponding label vector; similarly, let  $x_2, y_2$  refer to example 2 and its corresponding label vector.

To show the effect of each term, we refer to the class mean vectors for class 0 and class 1 as  $\mu_0$  and  $\mu_1$ , respectively; and use  $e_0$  and  $e_1$ , the basis vectors to represent the labels for class 0 and class 1.

### Analyzing cases for SL

$$
\nabla_W(L_{SL}(B)) = \frac{1}{2} \sum_{i=1}^{2} 2(Wx_i - y_i)x_i^{\top}
$$
\n(10)

$$
= (Wx_1 - y_1)(x_1)^{\top} + (Wx_2 - y_2)(x_2)^{\top}
$$
\n(11)

*Case 1: Both examples from class 0*

$$
\nabla_W(L_{SL}(B)) = (Wx_1 - e_0)(x_1)^{\top} + (Wx_2 - e_0)(x_2)^{\top}
$$
\n(12)

$$
\mathbb{E}[\nabla_W(L_{SL}(B))] = \mathbb{E}[(Wx_1 - e_0)(x_1)^{\top} + (Wx_2 - e_0)(x_2)^{\top}]
$$
\n
$$
\mathbb{E}[(Wx_1 - e_0)(x_1)^{\top}] = \mathbb{E}[(Wx_2 - e_0)(x_2)^{\top}]
$$
\n(13)

$$
= \mathbb{E}[(Wx_1 - e_0)(x_1)^{\top}] + \mathbb{E}[Wx_2 - e_0)(x_2)^{\top}]
$$
\n
$$
= \mathbb{E}[(W\mu_0 + W\epsilon_{N_1} - e_0)(e_0 + \epsilon_{N_1})^{\top}] + \mathbb{E}[(W\mu_0 + W\epsilon_{N_2} - e_0)(e_0 + \epsilon_{N_2})^{\top}]
$$
\n(15)

$$
= \mathbb{E}[(\mu_0 + \epsilon_{N_1} - e_0)(\mu_0 + \epsilon_{N_1})^{\top}] + \mathbb{E}[(\mu_0 + \epsilon_{N_2} - e_0)(\mu_0 + \epsilon_{N_2})^{\top}] \text{by substituting } W = I
$$
\n(16)

$$
= \mathbb{E}[\mu_0 \mu_0^{\top} + \epsilon_{N_1} \mu_0^{\top} - e_0 \mu_0^{\top} + \mu_0 \epsilon_{N_1}^{\top} + \epsilon_{N_1} \epsilon_{N_1}^{\top} - e_0 \epsilon_{N_1}^{\top}] + \mathbb{E}[\mu_0 \mu_0^{\top} + \epsilon_{N_2} \mu_0^{\top} - e_0 \mu_0^{\top} + \mu_0 \epsilon_{N_2}^{\top} + \epsilon_{N_2} \epsilon_{N_2}^{\top} - e_0 \epsilon_{N_2}^{\top}]
$$
(17)

$$
=2(\mu_0\mu_0^{\top} - e_0\mu_0^T) \tag{18}
$$

$$
\begin{align*}
\nabla_W(L_{SL}(B)) &= \mu_0 \mu_0^{\top} + \epsilon_{N_1} \mu_0^{\top} - e_0 \mu_0^{\top} + \mu_0 \epsilon_{N_1}^{\top} + \epsilon_{N_1} \epsilon_{N_1}^{\top} - e_0 \epsilon_{N_1}^{\top} \\
& \quad + \mu_0 \mu_0^{\top} + \epsilon_{N_2} \mu_0^{\top} - e_0 \mu_0^{\top} + \mu_0 \epsilon_{N_2}^{\top} + \epsilon_{N_2} \epsilon_{N_2}^{\top} - e_0 \epsilon_{N_2}^{\top} \\
&= 2\mu_0 \mu_0^{\top} + (\epsilon_{N_1} + \epsilon_{N_2}) \mu_0^{\top} - 2e_0 \mu_0^{\top} + \mu_0 (\epsilon_{N_1}^{\top} + \epsilon_{N_2}^{\top}) \\
& \quad + \epsilon_{N_1} \epsilon_{N_1}^{\top} + \epsilon_{N_2} \epsilon_{N_2}^{\top} - e_0 (\epsilon_{N_1}^{\top} + \epsilon_{N_2}^{\top}) \quad (19)
\end{align*}
$$

*Case 2: Both examples from class 1*

By symmetry, we have:

$$
\mathbb{E}[\nabla_W(L_{SL}(B))] = 2(\mu_1 \mu_1^\top - e_1 \mu_1^\top) \tag{20}
$$

and

$$
\nabla_W L_{SL}(B) = 2\mu_1 \mu_1^\top + (\epsilon_{N_1} + \epsilon_{N_2})\mu_1^\top - 2e_1 \mu_1^\top + \mu_1(\epsilon_{N_1}^\top + \epsilon_{N_2}^\top) + \epsilon_{N_1} \epsilon_{N_1}^\top + \epsilon_{N_2} \epsilon_{N_2}^\top - e_1(\epsilon_{N_1}^\top + \epsilon_{N_2}^\top)
$$
\n(21)

*Case 3: 1 example from each class*

$$
\mathbb{E}[\nabla_W(L_{SL}(B))] = \mu_0 \mu_0^\top - e_0 \mu_0^\top + \mu_1 \mu_1^\top - e_1 \mu_1^\top \tag{22}
$$

and

$$
\nabla_{W}L_{SL}(B) = \mu_{0}\mu_{0}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} - e_{0}\mu_{0}^{\top} + \mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top} - e_{0}\epsilon_{N_{1}}^{\top} + \mu_{1}\mu_{1}^{\top} + \epsilon_{N_{2}}\mu_{1}^{\top} - e_{1}\mu_{1}^{\top} + \mu_{1}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top} - e_{1}\epsilon_{N_{2}}^{\top}
$$
\n(23)

*Putting it together*

$$
\mathbb{E}[\nabla_W(L_{SL}(B))] = \frac{1}{4} \cdot 2(\mu_0 \mu_0^\top - e_0 \mu_0^\top) \tag{24}
$$

$$
+\frac{1}{4} \cdot 2(\mu_1 \mu_1^\top - e_1 \mu_1^\top) \tag{25}
$$

$$
+\frac{1}{2}\left(\mu_0\mu_0^{\top} - e_0\mu_0^{\top} + \mu_1\mu_1^{\top} - e_1\mu_1^{\top}\right) \tag{26}
$$

$$
= \mu_0 \mu_0^{\top} - e_0 \mu_0^{\top} + \mu_1 \mu_1^{\top} - e_1 \mu_1^{\top}
$$
 (27)

Finally,

$$
Var(\nabla_W(L_{SL}(B))) = \mathbb{E}[\|\nabla_W(L_{SL}(B)) - \mathbb{E}[\nabla_W(L_{SL}(B))]\|^2]
$$
(28)  
$$
= \frac{1}{4} \mathbb{E}[(\|\nabla_W(L_{SL}(B)) - \mathbb{E}[\nabla_W(L_{SL}(B))]\|^2] | \text{case 1}]
$$
$$
+ \frac{1}{4} \mathbb{E}[(\|\nabla_W(L_{SL}(B)) - \mathbb{E}[\nabla_W(L_{SL}(B))]\|^2] | \text{case 2}]
$$
$$
+ \frac{1}{2} \mathbb{E}[(\|\nabla_W(L_{SL}(B)) - \mathbb{E}[\nabla_W(L_{SL}(B))]\|^2] | \text{case 3}]
$$
(29)

 $Simplifying term I: \mathbb{E}[(\|\nabla_W(L_{SL}(B)) - \mathbb{E}[\nabla_W(L_{SL}(B))]\|^2)|$ case 1]

$$
\nabla_{W}(L_{SL}(B)) - \mathbb{E}[\nabla_{W}(L_{SL}(B))] = 2\mu_{0}\mu_{0}^{\top} + (\epsilon_{N_{1}} + \epsilon_{N_{2}})\mu_{0}^{\top} - 2e_{0}\mu_{0}^{\top} + \mu_{0}(\epsilon_{N_{1}}^{\top} + \epsilon_{N_{2}}^{\top}) \\+ \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top} - e_{0}(\epsilon_{N_{1}}^{\top} + \epsilon_{N_{2}}^{\top}) \\- (\mu_{0}\mu_{0}^{\top} - e_{0}\mu_{0}^{\top} + \mu_{1}\mu_{1}^{\top} - e_{1}\mu_{1}^{\top}) \\= \mu_{0}\mu_{0}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} - e_{0}\mu_{0}^{\top} + \mu_{0}\epsilon_{N_{1}}^{\top} + \mu_{0}\epsilon_{N_{2}}^{\top} \\+ \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top} - e_{0}\epsilon_{N_{1}}^{\top} - e_{0}\epsilon_{N_{2}}^{\top} \\- \mu_{1}\mu_{1}^{\top} + e_{1}\mu_{1}^{\top}
$$
\n(30)

*By symmetry, term 2* i.e.  $\mathbb{E}[(\|\nabla_W(L_{SL}(B)) - \mathbb{E}[\nabla_W(L_{SL}(B))]\|^2)|$  case 2 can be simplified as follows:

$$
\nabla_W(L_{SL}(B)) - \mathbb{E}[\nabla_W(L_{SL}(B))] = \mu_1 \mu_1^{\top} + \epsilon_{N_1} \mu_1^{\top} + \epsilon_{N_2} \mu_1^{\top} - e_1 \mu_1^{\top} + \mu_1 \epsilon_{N_1}^{\top} + \mu_1 \epsilon_{N_2}^{\top}
$$

$$
+\epsilon_{N_1}\epsilon_{N_1}^{\top}+\epsilon_{N_2}\epsilon_{N_2}^{\top}-e_1\epsilon_{N_1}^{\top}-e_1\epsilon_{N_2}^{\top}-\mu_0\mu_0^{\top}+e_0\mu_0^{\top}
$$
\n(31)

*Simplifying term 3*:

$$
\mathbb{E}[(\|\nabla_W(L_{SL}(B)) - \mathbb{E}[\nabla_W(L_{SL}(B))]\|^2)|\text{case 3}]
$$
\n(32)

$$
\nabla_W(L_{SL}(B)) - \mathbb{E}[\nabla_W(L_{SL}(B))] = \mu_0 \mu_0^{\top} + \epsilon_{N_1} \mu_0^{\top} - e_0 \mu_0^{\top} + \mu_0 \epsilon_{N_1}^{\top} + \epsilon_{N_1} \epsilon_{N_1}^{\top} - e_0 \epsilon_{N_1}^{\top} + \mu_1 \mu_1^{\top} + \epsilon_{N_2} \mu_1^{\top} - e_1 \mu_1^{\top} + \mu_1 \epsilon_{N_2}^{\top} + \epsilon_{N_2} \epsilon_{N_2}^{\top} - e_1 \epsilon_{N_2}^{\top} - (\mu_0 \mu_0^{\top} - e_0 \mu_0^{\top} + \mu_1 \mu_1^{\top} - e_1 \mu_1^{\top}) = \epsilon_{N_1} \mu_0^{\top} + \mu_0 \epsilon_{N_1}^{\top} + \epsilon_{N_1} \epsilon_{N_1}^{\top} - e_0 \epsilon_{N_1}^{\top} + \epsilon_{N_2} \mu_1^{\top} + \mu_1 \epsilon_{N_2}^{\top} + \epsilon_{N_2} \epsilon_{N_2}^{\top} - e_1 \epsilon_{N_2}^{\top}
$$
(33)

### Analyzing cases for SSL

We will now analyze the same for SSL and show by comparing each of the 3 terms, element-wise, above to their counterparts that  $Var(L_{SSL}(B)) > Var(L_{SL}(B))$ 

From [Xue et al.](#page-12-12) [\(2023\)](#page-12-12), we have that  $L_{SSL}$  can be re-written as

$$
\nabla_W L_{SSL}(W) = -\text{Tr}(2\tilde{M}WW^{\top}) + \text{Tr}(MW^{\top}WMW^{\top}W)
$$

where

$$
M = \frac{1}{2m} \sum_{i=1}^{2m} x_i x_i^{\top}
$$

with  $m$  being the number of augmentations, and  $M$  represents the covariance of the training data. The matrix  $\tilde{M}$  is defined as:

$$
\tilde{M} = \frac{1}{2} \sum_{i=1}^{n} \left( \frac{1}{m} \sum_{x \in \mathcal{A}(x_i)} x \right) \left( \frac{1}{m} \sum_{x \in \mathcal{A}(x_i)} x^{\top} \right)
$$

where  $A(x_i)$  denotes the set of augmentations for the sample  $x_i$ . **The Contract of the Contract of the Contract of the Contract of the Contract of the Contract of the Contract o** 

As  $m o 
e ext{inf}$ , 
$$
M = ilde{M} = rac{1}{2}(x_1 x_1^T + x_2 x_2^T)
$$
.

Hence, 
$$
abla_W(L_{SSL}(B)) = -4WM + 4WMW^{ op}WM
$$

Substituting  $W = I$ , we get 
$$
abla_W(L_{SSL}(B)) = -4M + 4M^2
$$

Case 1: Both examples from class 0

$$
M = \frac{1}{2} (x_1 x_1^\top + x_2 x_2^\top) \tag{34}
$$

$$
= \frac{1}{2} \big( (\mu_0 + \epsilon_{N_1}) (\mu_0 + \epsilon_{N_1})^\top + (\mu_0 + \epsilon_{N_2}) (\mu_0 + \epsilon_{N_2})^\top \big) \tag{35}
$$

$$
= \frac{1}{2} \left( \mu_0 \mu_0^\top + \mu_0 \epsilon_{N_1}^\top + \epsilon_{N_1} \mu_0^\top + \epsilon_{N_1} \epsilon_{N_1}^\top + \epsilon_{N_2} \epsilon_{N_2}^\top + \mu_0 \mu_0^\top + \mu_0 \epsilon_{N_2}^\top + \epsilon_{N_2} \mu_0^\top + \epsilon_{N_2} \epsilon_{N_2}^\top \right) \tag{36}
$$

$$
= \mu_0 \mu_0^\top + \frac{1}{2} \left( \mu_0 \epsilon_{N_1}^\top + \epsilon_{N_1} \mu_0^\top + \epsilon_{N_1} \epsilon_{N_1}^\top + \mu_0 \epsilon_{N_2}^\top + \epsilon_{N_2} \mu_0^\top + \epsilon_{N_2} \epsilon_{N_2}^\top \right)
$$
\n(37)

$$
M^{2} = \left(\mu_{0}\mu_{0}^{\top} + \frac{1}{2}\left(\mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top}\right)\right)
$$
(38)

$$
+\mu_{0}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top})^{2}
$$
 (39)
$$
=\mu_{0}\mu_{0}^{\top}\mu_{0}\mu_{0}^{\top}
$$
$$
+\mu_{0}\mu_{0}^{\top} \cdot \frac{1}{2} \left(\mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top}\right)
$$
$$
+\mu_{0}\mu_{0}^{\top} \cdot \frac{1}{2} \left(\mu_{0}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top}\right)
$$
$$
+\frac{1}{2} \left(\mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top}\right)\mu_{0}\mu_{0}^{\top}
$$
$$
+\frac{1}{2} \left(\mu_{0}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top}\right)\mu_{0}\mu_{0}^{\top}
$$
$$
+\frac{1}{4} \left(\mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top}\right)^{2}
$$
$$
+\frac{1}{4} \left(\mu_{0}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top}\right)^{2}
$$
$$
+\frac{1}{4} \left(\mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top}\right) \left(\mu_{0}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top}\right)
$$
$$
+\frac{1}{4} \left(\mu_{0}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top}\right) \left(\mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top}\right)
$$
 (40)

$$
\mathbb{E}[-4M + 4MM] = -4\mu_0\mu_0^{\top} + 4\mu_0\mu_0^{\top}\mu_0\mu_0^{\top}
$$
\n(41)

*Case 2: Both examples from class 1*

By symmetry,

$$
M = \mu_1 \mu_1^{\top} + \frac{1}{2} \left( \mu_1 \epsilon_{N_1}^{\top} + \epsilon_{N_1} \mu_1^{\top} + \epsilon_{N_1} \epsilon_{N_1}^{\top} + \mu_1 \epsilon_{N_2}^{\top} + \epsilon_{N_2} \mu_1^{\top} + \epsilon_{N_2} \epsilon_{N_2}^{\top} \right)
$$
\n(42)

$$
\mathbb{E}[-4M + 4MM] = -4\mu_1\mu_1^\top + 4\mu_1\mu_1^\top\mu_1\mu_1^\top
$$
\n(43)

*Case 3: 1 example from each class*

$$
M = \frac{1}{2} (x_1 x_1^\top + x_2 x_2^\top) \tag{44}
$$

$$
= \frac{1}{2} \big( (\mu_0 + \epsilon_{N_1}) (\mu_0 + \epsilon_{N_1})^\top + (\mu_1 + \epsilon_{N_2}) (\mu_1 + \epsilon_{N_2})^\top \big) \tag{45}
$$

$$
= \frac{1}{2} \left( \mu_0 \mu_0^\top + \mu_0 \epsilon_{N_1}^\top + \epsilon_{N_1} \mu_0^\top + \epsilon_{N_1} \epsilon_{N_1}^\top + \mu_1 \mu_1^\top + \mu_1 \epsilon_{N_2}^\top + \epsilon_{N_2} \mu_1^\top + \epsilon_{N_2} \epsilon_{N_2}^\top \right) \tag{46}
$$

$$
\mathbb{E}[-4M + 4MM] = -2\mu_0\mu_0^{\top} - 2\mu_1\mu_1^{\top} + 2\mu_0\mu_0^{\top}\mu_0\mu_0^{\top} + 2\mu_1\mu_1^{\top}\mu_1\mu_1^{\top}
$$
(47)

*Putting it together*

Hence.

$$
\mathbb{E}[-4M + 4M^2] = \frac{1}{4} \left( -4\mu_0 \mu_0^{\top} + 4\mu_0 \mu_0^{\top} \mu_0 \mu_0^{\top} \right) + \frac{1}{4} \left( -4\mu_1 \mu_1^{\top} + 4\mu_1 \mu_1^{\top} \mu_1 \mu_1^{\top} \right)
$$

$$
+\frac{1}{2}\left(-2\mu_0\mu_0^{\top}-2\mu_1\mu_1^{\top}+2\mu_0\mu_0^{\top}\mu_0\mu_0^{\top}+2\mu_1\mu_1^{\top}\mu_1\mu_1^{\top}\right) \tag{48}
$$

$$
= -2\mu_0\mu_0^{\top} - 2\mu_1\mu_1^{\top} + 2\mu_0\mu_0^{\top}\mu_0\mu_0^{\top} + 2\mu_1\mu_1^{\top}\mu_1\mu_1^{\top}
$$
(49)

### Comparing each term, element-wise

*Comparing Term 1*

$$
\nabla_W(L_{SL}(B)) - \mathbb{E}[\nabla_W(L_{SL}(B))] = \mu_0 \mu_0^{\top} + \epsilon_{N_1} \mu_0^{\top} + \epsilon_{N_2} \mu_0^{\top} - e_0 \mu_0^{\top} + \mu_0 \epsilon_{N_1}^{\top} + \mu_0 \epsilon_{N_2}^{\top} + \epsilon_{N_1} \epsilon_{N_1}^{\top} + \epsilon_{N_2} \epsilon_{N_2}^{\top} - e_0 \epsilon_{N_1}^{\top} - e_0 \epsilon_{N_2}^{\top}
$$
  
-  $\mu_1 \mu_1^{\top} + e_1 \mu_1^{\top}$  (50)

$$
\nabla_{W}(L_{SSL}(B)) - \mathbb{E}[\nabla_{W}(L_{SSL}(B))] = -2\mu_{0}\mu_{0}^{\top} - 2\mu_{0}\epsilon_{N_{1}}^{\top} - 2\epsilon_{N_{1}}\mu_{0}^{\top} - 2\epsilon_{N_{1}}\epsilon_{N_{1}}^{\top} - 2\mu_{0}\epsilon_{N_{2}}^{\top} - 2\epsilon_{N_{2}}\mu_{0}^{\top} - 2\epsilon_{N_{2}}\epsilon_{N_{2}}^{\top}
$$

$$
= 2\mu_{0}\mu_{0}^{\top} \mu_{0}\mu_{0}^{\top} \\
+ \mu_{0}\mu_{0}^{\top} \cdot 2 (\mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top}) \\
+ \mu_{0}\mu_{0}^{\top} \cdot 2 (\mu_{0}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top}) \\
+ 2 (\mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top}) \mu_{0}\mu_{0}^{\top} \\
+ 2 (\mu_{0}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top}) \mu_{0}\mu_{0}^{\top} \\
+ 1 (\mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top})^{2} \\
+ 1 (\mu_{0}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top})^{2} \\
+ 1 (\mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top}) (\mu_{0}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top}) \\
+ 1 (\mu_{0}\epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}}\mu_{0}^{\top} + \epsilon_{N_{2}}\epsilon_{N_{2}}^{\top}) (\mu_{0}\epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}}\mu_{0}^{\top} + \epsilon_{N_{1}}\epsilon_{N_{1}}^{\top}) (51)
$$

<span id="page-20-0"></span>Recall that  $\mu_0 = e_0$ .

Use  $\epsilon_1$  and  $\epsilon_2$  to denote the value of an abtrirary element of  $\epsilon_{N_1}$  and  $\epsilon_{N_2}$ , respectively. *Comparing the expectation of the square of element*  $(0, 0)$  *of*  $\nabla_W(L(B)) - \mathbb{E}[\nabla_W(L(B))]$ : For SL:  $\mathbb{E}[(1 + \epsilon_1 + \epsilon_2 - 1 + \epsilon_1 + \epsilon_2 + \epsilon_1^2 + \epsilon_2^2 - \epsilon_1 - \epsilon_2 - 1 + 1)^2] = \mathbb{E}[(\epsilon_1 + \epsilon_2 + \epsilon_1^2 + \epsilon_2^2)]$ From Eq. [51,](#page-20-0) it is easy to see that the expectation of the square of element  $(0, 0)$  is far larger due to far more  $\epsilon_1$ ,  $\epsilon_2$ ,  $\epsilon_1^2$  and  $\epsilon_2^2$  terms.

Similar argument holds for diagonal terms that are not  $(0, 0)$ , due to the greater number of terms that only have  $\epsilon_1^2$  and  $\epsilon_2^2$  (all terms having only  $\epsilon_1$  and  $\epsilon_2$  are only present in element  $(0,0)$  since they are always multiplied with  $e_0$ ).

An analogous argument holds for off-diagonal elements. Here, the contribution is only due to terms containing only  $\epsilon_{N_1}$  and/or  $\epsilon_{N_2}$ .

*Comparing Term 2*

By symmetry, it must be that the same holds for term 2.

*Comparing Term 3*

$$
\nabla_W(L_{SL}(B)) - \mathbb{E}[\nabla_W(L_{SL}(B))] = \epsilon_{N_1}\mu_0^\top + \mu_0 \epsilon_{N_1}^\top + \epsilon_{N_1} \epsilon_{N_1}^\top - e_0 \epsilon_{N_1}^\top + \epsilon_{N_2}\mu_1^\top + \mu_1 \epsilon_{N_2}^\top + \epsilon_{N_2} \epsilon_{N_2}^\top - e_1 \epsilon_{N_2}^\top
$$
(52)

$$
\nabla_W(L_{SSL}(B)) - \mathbb{E}[\nabla_W(L_{SSL}(B))] = -2(\mu_0\mu_0^{\top} + \mu_0\epsilon_{N_1}^{\top} + \epsilon_{N_1}\mu_0^{\top} + \epsilon_{N_1}\epsilon_{N_1}^{\top})
$$

+

$$
\mu_{1} \mu_{1}^{\top} + \mu_{1} \epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}} \mu_{1}^{\top} + \epsilon_{N_{2}} \epsilon_{N_{2}}^{\top}
$$
+

$$
4(\mu_{0} \mu_{0}^{\top} + \mu_{0} \epsilon_{N_{1}}^{\top} + \epsilon_{N_{1}} \mu_{0}^{\top} + \epsilon_{N_{1}} \epsilon_{N_{1}}^{\top}
$$
+

$$
\mu_{1} \mu_{1}^{\top} + \mu_{1} \epsilon_{N_{2}}^{\top} + \epsilon_{N_{2}} \mu_{1}^{\top} + \epsilon_{N_{2}} \epsilon_{N_{2}}^{\top})^{2} (53)
$$

Again, from analogous arugments from *Term 1*, we can see that the expression for  $L_{SSL}$  has larger expected square element-wise than that of  $L_{SL}$ .

## Conclusion

Since, for each term (corresponding to the 3 cases of mini-batch), we have that

$$
\mathbb{E}[\|\nabla_{W}(L_{SSL}(B)) - \mathbb{E}[\nabla_{W}(L_{SSL}(B))] \|^{2}] > \mathbb{E}[\|\nabla_{W}(L_{SL}(B)) - \mathbb{E}[\nabla_{W}(L_{SL}(B))] \|^{2}], \quad (54)
$$
  
Thus, we can conclude  
$$
\text{Var}(\nabla_{W}L_{SL}(B)) < \text{Var}(\nabla_{W}L_{SSL}(B)).
$$

<span id="page-22-0"></span>

# D PROPOSITION D.1: GENERALIZED ANALYSIS OF VARIANCE OF TRAJECTORY UNDER SYNCHRONOUS PARALLEL SGD

<span id="page-22-1"></span>**Proposition D.1.** Let  $D = \{(x_i, y_i)\}_{i=1}^N$  be a dataset with N examples, where  $x_i$  is the *i*-th input and  $y_i \in \{1, 2, \ldots, K\}$  is its corresponding class label from K classes. We assume the data  $x_i$  is *generated from a distribution where each example from class* k *can be modeled as a d-dimensional*  $(k \ll d)$  1-hot vector  $e_k$  plus some noise  $\epsilon_i$ , i.e.  $x_i = e_{y_i} + \epsilon_i$ , where  $\mu_{y_i}$  is the vector corresponding *to class*  $y_i$  *and*  $\epsilon_i \sim \mathcal{N}(0,I)$ *. Moreover, we consider a linear model*  $f_\theta(x) = \theta x$  where  $\theta \in \mathbb{R}^{K \times d}$ *.* 

*The supervised loss function*  $L_{SL}$  *is defined as a loss function that depends solely on each individual example:*

$$
L_{SL}(B) = \frac{1}{|B|} \sum_{i \in B} \ell_{SL}(f_{\theta}(x_i), y_i),
$$
\n(55)

*where*  $\ell_{SL}$  *is an arbitrary supervised loss function (e.g., MSE, Cross-Entropy, etc.) and B is a mini-batch of examples.*

*For SSL, we consider the spectral contrastive loss:*

$$
L_{SSL} = -2\mathbb{E}_{x_1, x_2 \sim \mathcal{A}(x_i)} [f(x_1)^T f(x_2)] + \mathbb{E}_{x_i, x_j \sim \mathcal{A}(B)} [f(x_i)^T f(x_j)]^2
$$
(56)

*where* A denotes the augmentation that are modeled as:  $A(x_i) \sim x_i + \epsilon_{aug}$ , where  $\epsilon_{aug} \sim \mathcal{N}(0, I)$ .

*We analyze this setting under synchronous parallel SGD, where the number of mini-batches equals the number of parallel threads. Let* P *represent a partition of the dataset* D *into a set of disjoint mini-batches, each of size* |B|*. The synchronous parallel SGD update for an arbitrary loss function*  $L(B)$  *on mini-batch*  $B$  *at time step*  $t$  *is*  $\theta_{t+1} := \theta_t - \alpha \sum_{B \in P} \nabla_{\theta_t} L(B)$ *.* 

*Let*  $\theta$  *be the initial parameters and*  $\theta_{SL}$ ,  $\theta_{SSL}$  *be the model parameters after a single epoch of training with synchronous parallel using the supervised loss*  $L_{SL}$  *and the self-supervised loss*  $L_{SSL}$ *, respectively, with a learning rate* α*. Then, for sufficiently large batch size* |B|*,*

$$
Var_P(\theta_{SL}) < Var_P(\theta_{SSL}),\tag{57}
$$

*where* Var<sub>P</sub> represents the variance over different partitions P, and for a vector random variable X, *we define the scalar variance as*  $Var(X) = \mathbb{E}[\|X - \mathbb{E}[X]\|^2]$ 

*Proof.* For synchronous parallel SGD, we have:

$$
\theta_{t+1} = \theta_t - \alpha \sum_{B \in P} \nabla_{\theta_t} L(B),
$$

where  $L$  is an arbitrary loss function.

### Variance of Trajectory in Supervised Learning

Consider two arbitrary partitions  $P_1$  and  $P_2$ . Let  $\theta$  represent the initial parameters, and let  $\theta_{SL_1}$ and  $\theta_{SL_2}$  represent the parameters after a single epoch of synchronous parallel SGD for supervised learning using  $L_{SL}$  on the partitions  $P_1$  and  $P_2$ , respectively.

We can express the updates as:

$$
\theta_{SL_1} = \theta + \alpha \sum_{B \in P_1} \nabla_{\theta} L_{SL}(B) \tag{58}
$$

$$
= \theta + \alpha \sum_{B \in P_1} \sum_{(x_i, y_i) \in B} \frac{1}{|B|} \nabla_{\theta} L_{SL}(\{x_i, y_i\}) \text{ (due to independence of loss for each example)}
$$
\n
$$
\tag{59}
$$

$$
= \theta + \alpha \sum_{(x_i, y_i) \in D} \frac{1}{|B|} \nabla_{\theta} L_{SL}(\{x_i, y_i\})
$$
\n
$$
(60)
$$

$$
= \theta + \alpha \sum_{B \in P_2} \sum_{(x_i, y_i) \in B} \frac{1}{|B|} \nabla_{\theta} L_{SL}(\{x_i, y_i\})
$$
\n
$$
\tag{61}
$$

$$
=\theta_{SL_2}.\tag{62}
$$

Since  $\theta_{SL_1} = \theta_{SL_2}$  for arbitrary partitions  $P_1$  and  $P_2$ , we can conclude that:

$$
\text{Var}_P(\theta_{SL}) = 0.
$$

### Variance of Trajectory in Self-Supervised Learning

Next, we need to show that  $Var_P(\theta_{SSL}) > 0$ . Let  $\theta$  represent the initial parameters, and let  $\theta_{SSL_1}$ and  $\theta_{SSL_2}$  represent the parameters after a single epoch of synchronous parallel SGD using the self-supervised loss  $L_{SSL}$  with partitions  $P_1$  and  $P_2$ , respectively.

To show Var $_P(\theta_{SSL}) > 0$ , it suffices to show that there exist partitions  $P_1$  and  $P_2$  such that  $\theta_{SSL_1} \neq \theta_{SSL_2}.$ 

Consider the following two partitions:

- Let  $P_1$  be a partition where each mini-batch is constructed by iterating over classes, until |B| examples are selected, and picking a single example from the remaining examples in each class
- Let  $P_2$  be a partition where each mini-batch is constructed by picking examples from the remaining examples each class until  $|B|$  examples

It is easy to see, from prior work in hard negative mining for contrastive SSL such as [Robinson](#page-11-16) [et al.](#page-11-16) [\(2021\)](#page-11-16), that  $P_2$  will have a different gradient across all batches from  $P_1$  as  $P_2$  only contrasts examples within the same class, whereas  $P_1$  contrasts examples across different classes. Hence,  $\theta_{SSL_1} \neq \theta_{SSL_2}.$ 

We will now prove this for our setting, considering two random partitions  $P_1$  and  $P_2$ .

[Xue et al.](#page-12-12) [\(2023\)](#page-12-12) shows the  $L_{SSL}$  can be rewritten as  $L_{SSL} = -Tr(2M\theta\theta^T) + Tr(M'\theta^T\theta M'\theta^T\theta)$ 

where  $M = \frac{1}{m|B|} \sum_{i=1}^{m|B|} x_i x_i^T$ ,  $M' = \frac{1}{|B|} \sum_{i=1}^{|B|} \left(\frac{1}{m} \sum_{x \in A(x_i)} x\right) \left(\frac{1}{m} \sum_{x \in A(x_i)} x\right)^T$  where m is the number of augmentations sampled.

From [Xue et al.](#page-12-12) [\(2023\)](#page-12-12), we also have  $\nabla_{\theta}(L_{SSL}) = -4\theta M + 4\theta M'\theta^T\theta M'$ 

With m i.e. number of augmentations large enough,  $M = M'$ .

We now refer to M for  $P_1$  as  $M_1$  and M for  $P_2$  as  $M_2$ .

From the construction of  $P_1$ , we have  $M_1 = I$  and from the construction of  $P_2$ , we have  $[M_2]_{i,j} =$  $\delta_{i,k}\delta_{j,k}$  where  $\delta_{i,j} = 1[i = j]$  for batch where examples from class k are selected.

From this, we can see that, the  $\nabla_{\theta}(L_{SSL})$  is not equal for  $P_1$  and  $P_2$ .

Hence, we can conclude that  $\theta_{SSL_1} \neq \theta_{SSL_2}$ 

 $\Box$ 

# E EXAMPLES FROM MKDTAND KRR-ST (LEE ET AL., 2023)

Here, we show examples from CIFAR10, CIFAR100 and TinyImageNet for the data distilled using MKDTand KRR-ST [\(Lee et al., 2023\)](#page-11-5).

Image /page/24/Picture/3 description: The image displays two grids of images. The left grid contains 30 images, arranged in 5 rows and 6 columns. Many of these images are distorted with colorful noise, but some recognizable objects include cats, dogs, horses, and cars. The right grid also contains 30 images, arranged in 5 rows and 6 columns. These images are significantly more faded and distorted, with only faint outlines of objects visible, appearing to be similar categories of animals and vehicles as in the left grid.

(a) MKDT

(a) MKDT (b) KRR-ST

Figure 2: Examples of Distilled Images for CIFAR10

Image /page/24/Picture/7 description: The image displays two grids of images. The left grid contains 30 clear images of various objects and animals, including a raccoon, a wolf, a rose, a bicycle, an elephant, a lion, a tractor, and a train. The right grid contains 30 blurry and noisy images that appear to be generated or corrupted versions of the images in the left grid. The overall presentation suggests a comparison between original and processed images.

(a) MKDT

(a) MKDT (b) KRR-ST

Figure 3: Examples of Distilled Images for CIFAR100

Image /page/24/Picture/11 description: The image is a collage of 100 smaller images, arranged in a 10x10 grid. The images are diverse, featuring a variety of subjects including nature, objects, and people. Some of the discernible images include a butterfly, a compass, a car with the word "TOYOTA" visible, a person in a suit, a dog, and various textures and patterns.

(a) MKDT

(a) MKDT (b) KRR-ST

Figure 4: Examples of Distilled Images for TinyImageNet

# F DM AND MTT-SSL EXPERIMENTS

We also compared our methods with two DD methods adapted to SSL. Specifically, we focused on distilling 5% of CIFAR 100. The results all show that those methods do not outperform MKDT.

To adapt DM for SSL, we used the same codebase and hyperparameters as DM [\(Zhao & Bilen, 2023\)](#page-12-3). However, during distillation, instead of DM's original approach of sampling random images from a specific class, we modified the process to sample random images from the entire dataset. This adjustment allows us to approximate the behavior of running DM in a label-free setting. The results are shown in [11.](#page-25-0)

To adapt MTT to SSL, we modified the supervised learning algorithm in getting the trajectory and training the network in the inner loop of the distillation to be the SimCLR SSL algorithm. Due to the

<span id="page-25-0"></span>

| Dataset              | CIFAR100       | CIFAR10        | TinyImageNet  | Aircraft      | CUB2011       | Dogs          | Flowers       |
|----------------------|----------------|----------------|---------------|---------------|---------------|---------------|---------------|
| CIFAR10 (1% Labels)  | 7.09 $ ±0.16$  | 35.98 $ ±0.98$ | 2.43 $ ±0.19$ | 2.15 $ ±0.21$ | 1.19 $ ±0.13$ | 1.71 $ ±0.18$ | 1.60 $ ±0.17$ |
| CIFAR10 (5% Labels)  | 14.77 $ ±0.10$ | 46.41 $ ±0.35$ | 5.11 $ ±0.32$ | 4.94 $ ±0.88$ | 1.57 $ ±0.31$ | 2.53 $ ±0.13$ | 3.10 $ ±0.33$ |
| CIFAR100 (1% Labels) | 8.74 $ ±0.35$  | 37.75 $ ±0.87$ | 2.75 $ ±0.22$ | 2.51 $ ±0.08$ | 1.20 $ ±0.11$ | 2.07 $ ±0.20$ | 2.53 $ ±0.27$ |
| CIFAR100 (5% Labels) | 17.01 $ ±0.37$ | 47.32 $ ±0.38$ | 6.40 $ ±0.21$ | 5.08 $ ±0.82$ | 1.99 $ ±0.13$ | 2.77 $ ±0.28$ | 4.62 $ ±0.28$ |

high variance of SSL as analyzed in [4.1,](#page-3-2) distillation fails to converge, and the poor results further shows that MTT with SSL does not work. The results are shown in [12.](#page-25-1)

|  |  |  | Table 11: Results for Distilling with DM with SSL. |  |  |  |  |
|--|--|--|----------------------------------------------------|--|--|--|--|
|--|--|--|----------------------------------------------------|--|--|--|--|

<span id="page-25-1"></span>

| <b>Dataset</b>       | <b>CIFAR100</b> | <b>CIFAR10</b>  | <b>TinyImageNet</b> | <b>Aircraft</b> | <b>CUB2011</b> | <b>Dogs</b>    | <b>Flowers</b> |
|----------------------|-----------------|-----------------|---------------------|-----------------|----------------|----------------|----------------|
| CIFAR10 (1% Labels)  | 4.63 $ ± 0.30$  | 22.06 $ ± 1.86$ | 1.11 $ ± 0.16$      | 1.92 $ ± 0.26$  | 0.77 $ ± 0.11$ | 1.38 $ ± 0.28$ | 1.26 $ ± 0.37$ |
| CIFAR10 (5% Labels)  | 7.10 $ ± 0.43$  | 29.04 $ ± 0.58$ | 1.90 $ ± 0.17$      | 2.81 $ ± 0.60$  | 0.88 $ ± 0.08$ | 1.82 $ ± 0.19$ | 1.42 $ ± 0.53$ |
| CIFAR100 (1% Labels) | 4.26 $ ± 0.34$  | 26.06 $ ± 1.58$ | 1.20 $ ± 0.14$      | 1.49 $ ± 0.28$  | 0.87 $ ± 0.26$ | 1.24 $ ± 0.07$ | 1.41 $ ± 0.27$ |
| CIFAR100 (5% Labels) | 9.66 $ ± 0.24$  | 38.16 $ ± 0.57$ | 2.11 $ ± 0.19$      | 2.83 $ ± 0.41$  | 1.06 $ ± 0.04$ | 1.74 $ ± 0.15$ | 2.38 $ ± 0.19$ |

Table 12: Results for Distilling with MTT using High Variance SSL Trajectories.

# G SOCIETAL IMPACT

Our work democratizes the development of models trained with SSL pre-training, by reducing the volume of data needed to train such models, and thus the costs of training these models. However, the risk with dataset distillation is augmenting the biases of the original dataset.
application of these ideas to a real bio-sequence problem.

# **5.5 Hierarchical Bayes**

A key requirement for computing the posterior  $p(\theta|\mathcal{D})$  is the specification of a prior  $p(\theta|\eta)$ , where  $\eta$  are the hyper-parameters. What if we don't know how to set  $\eta$ ? In some cases, we can use uninformative priors, we we discussed above. A more Bayesian approach is to put a prior on our priors! In terms of graphical models (Chapter 10), we can represent the situation as follows:

$$
\eta \to \theta \to \mathcal{D} \tag{5.76}
$$

This is an example of a **hierarchical Bayesian model**, also called a **multi-level model**, since there are multiple levels of unknown quantities. We give a simple example below, and we will see many others later in the book.

## **5.5.1 Example: modeling related cancer rates**

Consider the problem of predicting cancer rates in various cities (this example is from (Johnson and <PERSON> 1999, p24)). In particular, suppose we measure the number of people in various cities,  $N_i$ , and the number of people who died of cancer in these cities,  $x_i$ . We assume  $x_i \sim Bin(N_i, \theta_i)$ , and we want to estimate the cancer rates  $\theta_i$ . One approach is to estimate them all separately, but this will suffer from the sparse data problem (underestimation of the rate of cancer due to small  $N_i$ ). Another approach is to assume all the  $\theta_i$  are the same; this is called **parameter tying**. The resulting pooled MLE is just  $\hat{\theta} = \sum_{\lambda}$  $\frac{\sum_{i} x_i}{\sum_{i} N_i}$  $\frac{i}{i} \frac{x_i}{N_i}$ . But the assumption that all the cities have the same rate is a rather strong one. A compromise approach is to assume that the  $\theta_i$  are similar, but that there may be city-specific variations. This can be modeled by assuming the  $\theta_i$  are drawn from some common distribution, say  $\theta_i \sim \text{Beta}(a, b)$ . The full joint distribution can be written as

$$
p(\mathcal{D}, \boldsymbol{\theta}, \boldsymbol{\eta} | \mathbf{N}) = p(\boldsymbol{\eta}) \prod_{i=1}^{N} \text{Bin}(x_i | N_i, \theta_i) \text{Beta}(\theta_i | \boldsymbol{\eta})
$$
\n(5.77)

where  $\eta = (a, b)$ .

Note that it is crucial that we infer  $\eta = (a, b)$  from the data; if we just clamp it to a constant, the  $\theta_i$  will be conditionally independent, and there will be no information flow between them. By contrast, by treating *η* as an unknown (hidden variable), we allow the data-poor cities to **borrow statistical strength** from data-rich ones.

Suppose we compute the joint posterior  $p(\eta, \theta | \mathcal{D})$ . From this we can get the posterior marginals  $p(\theta_i|\mathcal{D})$ . In Figure 5.11(a), we plot the posterior means,  $\mathbb{E}[\theta_i|\mathcal{D}]$ , as blue bars, as well as the population level mean,  $\mathbb{E} [a/(a+b)|\mathcal{D}]$ , shown as a red line (this represents the average of the  $\theta_i$ 's). We see that the posterior mean is shrunk towards the pooled estimate more strongly for cities with small sample sizes  $N_i$ . For example, city 1 and city 20 both have a 0 observed cancer incidence rate, but city 20 has a smaller population, so its rate is shrunk more towards the population-level estimate (i.e., it is closer to the horizontal red line) than city 1.

Figure 5.11(b) shows the 95% posterior credible intervals for  $\theta_i$ . We see that city 15, which has a very large population (53,637 people), has small posterior uncertainty. Consequently this city

Image /page/1/Figure/1 description: The image displays two figures, labeled (a) and (b). Figure (a) contains four bar charts stacked vertically. The top chart shows the 'number of people with cancer (truncated at 5)' on the y-axis and city index from 0 to 25 on the x-axis. The second chart shows 'pop of city (truncated at 2000)' on the y-axis and city index from 0 to 25 on the x-axis. The third chart shows 'MLE\*1000 (red line=pooled MLE)' on the y-axis and city index from 0 to 25 on the x-axis, with a red horizontal line indicating the pooled MLE. The bottom chart shows 'posterior mean\*1000 (red line=pop mean)' on the y-axis and city index from 0 to 25 on the x-axis, with a red horizontal line indicating the population mean. Figure (b) is a horizontal bar chart showing '95% credible interval on theta, \*=median'. The y-axis ranges from 0 to 20, representing different cities or groups. The x-axis represents values from 0 to 8 x 10^-3. Each row displays a horizontal blue line representing the credible interval, with an asterisk (\*) marking the median for each corresponding y-axis value.

**Figure 5.11** (a) Results of fitting the model using the data from (Johnson and Albert 1999, p24). First row: Number of cancer incidents  $x_i$  in 20 cities in Missouri. Second row: population size  $N_i$ . The largest city (number 15) has a population of  $N_{15} = 53637$  and  $x_{15} = 54$  incidents, but we truncate the vertical axes of the first two rows so that the differences between the other cities are visible. Third row: MLE  $\theta_i$ . The red line is the pooled MLE. Fourth row: posterior mean  $\mathbb{E}[\theta_i|\mathcal{D}]$ . The red line is  $\mathbb{E}[\alpha/(a+b)|\mathcal{D}]$ , the population-level mean. (b) Posterior 95% credible intervals on the cancer rates. Figure generated by cancerRatesEb

has the largest impact on the posterior estimate of *η*, which in turn will impact the estimate of the cancer rates for other cities. Cities 10 and 19, which have the highest MLE, also have the highest posterior uncertainty, reflecting the fact that such a high estimate is in conflict with the prior (which is estimated from all the other cities).

In the above example, we have one parameter per city, modeling the probability the response is on. By making the Bernoulli rate parameter be a function of covariates,  $\theta_i = \text{sigm}(\mathbf{w}_i^T \mathbf{x})$ , we can model multiple correlated logistic regression tasks. This is called **multi-task learning** and can model multiple correlated logistic regression tasks. This is called **multi-task learning**, and will be discussed in more detail in Section 9.5.

# **5.6 Empirical Bayes**

In hierarchical Bayesian models, we need to compute the posterior on multiple levels of latent variables. For example, in a two-level model, we need to compute

$$
p(\eta, \theta | \mathcal{D}) \propto p(\mathcal{D} | \theta) p(\theta | \eta) p(\eta)
$$
\n(5.78)

In some cases, we can analytically marginalize out  $\theta$ ; this leaves is with the simpler problem of just computing  $p(\eta|\mathcal{D})$ .

As a computational shortcut, we can approximate the posterior on the hyper-parameters with a point-estimate,  $p(\eta|\mathcal{D}) \approx \delta_{\hat{\eta}}(\eta)$ , where  $\hat{\eta} = \argmax p(\eta|\mathcal{D})$ . Since  $\eta$  is typically much smaller than  $\theta$  in dimensionality, it is less prone to overfitting, so we can safely use a uniform prior on *η*. Then the estimate becomes

$$
\hat{\boldsymbol{\eta}} = \operatorname{argmax} p(\mathcal{D}|\boldsymbol{\eta}) = \operatorname{argmax} \left[ \int p(\mathcal{D}|\boldsymbol{\theta}) p(\boldsymbol{\theta}|\boldsymbol{\eta}) d\boldsymbol{\theta} \right]
$$
\n(5.79)

where the quantity inside the brackets is the marginal or integrated likelihood, sometimes called the evidence. This overall approach is called **empirical Bayes** (**EB**) or **type-II maximum likelihood**. In machine learning, it is sometimes called the **evidence procedure**.

Empirical Bayes violates the principle that the prior should be chosen independently of the data. However, we can just view it as a computationally cheap approximation to inference in a hierarchical Bayesian model, just as we viewed MAP estimation as an approximation to inference in the one level model  $\theta \to \mathcal{D}$ . In fact, we can construct a hierarchy in which the more integrals one performs, the "more Bayesian" one becomes:

| Method                  | Definition                                                                                                                           |
|-------------------------|--------------------------------------------------------------------------------------------------------------------------------------|
| Maximum likelihood      | $\hat{\theta} = \arg\max_{\theta} p(\mathcal{D} \theta)$                                                                             |
| MAP estimation          | $\hat{\theta} = \arg!\max_{\theta} p(\mathcal{D} \theta)p(\theta \eta)$                                                              |
| ML-II (Empirical Bayes) | $\hat{\eta} = \arg \max_{\eta} \int p(\mathcal{D} \theta)p(\theta \eta) d\theta = \arg \max_{\eta} p(\mathcal{D} \eta)$              |
| MAP-II                  | $\hat{\eta} = \arg!\max_{\eta} \int p(\mathcal{D} \theta)p(\theta \eta)p(\eta)d\theta = \arg!\max_{\eta} p(\mathcal{D} \eta)p(\eta)$ |
| <b>Full Bayes</b>       | $p(\theta, \eta   \mathcal{D}) \propto p(\mathcal{D}   \theta) p(\theta   \eta) p(\eta)$                                             |

Note that EB can be shown to have good frequentist properties (see e.g., (Carlin and Louis 1996; Efron 2010)), so it is widely used by non-Bayesians. For example, the popular James-Stein estimator, discussed in Section 6.3.3.2, can be derived using EB.

## **5.6.1 Example: beta-binomial model**

Let us return to the cancer rates model. We can analytically integrate out the  $\theta_i$ 's, and write down the marginal likelihood directly, as follows:

$$
p(\mathcal{D}|a,b) = \prod_{i} \int \text{Bin}(x_i|N_i, \theta_i) \text{Beta}(\theta_i|a, b) d\theta_i
$$
\n(5.80)

$$
= \prod_{i} \frac{B(a+x_i, b+N_i-x_i)}{B(a,b)}
$$
(5.81)

Various ways of maximizing this wrt  $a$  and  $b$  are discussed in (Minka 2000e).

Having estimated  $\alpha$  and  $\beta$ , we can plug in the hyper-parameters to compute the posterior  $p(\theta_i|\hat{a}, b, \mathcal{D})$  in the usual way, using conjugate analysis. The net result is that the posterior mean of each  $\theta_i$  is a weighted average of its local MLE and the prior means, which depends on  $\eta = (a, b)$ ; but since  $\eta$  is estimated based on all the data, each  $\theta_i$  is influenced by all the data.

## **5.6.2 Example: Gaussian-Gaussian model**

We now study another example that is analogous to the cancer rates example, except the data is real-valued. We will use a Gaussian likelihood and a Gaussian prior. This will allow us to write down the solution analytically.

In particular, suppose we have data from multiple related groups. For example,  $x_{ij}$  could be the test score for student i in school j, for  $j = 1 : D$  and  $i = 1 : N_j$ . We want to estimate the mean score for each school,  $\theta_j$ . However, since the sample size,  $N_j$ , may be small for some schools, we can regularize the problem by using a hierarchical Bayesian model, where we assume  $\theta_i$  come from a common prior,  $\mathcal{N}(\mu, \tau^2)$ .

The joint distribution has the following form:

$$
p(\theta, \mathcal{D}|\boldsymbol{\eta}, \sigma^2) = \prod_{j=1}^{D} \mathcal{N}(\theta_j | \mu, \tau^2) \prod_{i=1}^{N_j} \mathcal{N}(x_{ij} | \theta_j, \sigma^2)
$$
\n(5.82)

where we assume  $\sigma^2$  is known for simplicity. (We relax this assumption in Exercise 24.4.) We explain how to estimate *η* below. Once we have estimated  $\eta = (\mu, \tau)$ , we can compute the posteriors over the  $\theta_i$ 's. To do that, it simplifies matters to rewrite the joint distribution in the following form, exploiting the fact that  $N_i$  Gaussian measurements with values  $x_{ij}$  and variance  $\sigma^2$  are equivalent to one measurement of value  $\overline{x}_j \triangleq \frac{1}{N_j} \sum_{i=1}^{N_j} x_{ij}$  with variance  $\sigma_j^2 \triangleq \sigma^2/N_j$ . This yields

$$
p(\theta, \mathcal{D}|\hat{\boldsymbol{\eta}}, \sigma^2) = \prod_{j=1}^{D} \mathcal{N}(\theta_j|\hat{\mu}, \hat{\tau}^2) \mathcal{N}(\overline{x}_j|\theta_j, \sigma_j^2)
$$
(5.83)

From this, it follows from the results of Section 4.4.1 that the posteriors are given by

$$
p(\theta_j | \mathcal{D}, \hat{\mu}, \hat{\tau}^2) = \mathcal{N}(\theta_j | \hat{B}_j \hat{\mu} + (1 - \hat{B}_j) \overline{x}_j, (1 - \hat{B}_j) \sigma_j^2)
$$
(5.84)

$$
\hat{B}_j \triangleq \frac{\sigma_j^2}{\sigma_j^2 + \hat{\tau}^2} \tag{5.85}
$$

where  $\hat{\mu} = \overline{x}$  and  $\hat{\tau}^2$  will be defined below.

The quantity  $0 \leq \hat{B}_i \leq 1$  controls the degree of **shrinkage** towards the overall mean,  $\mu$ . If the data is reliable for group j (e.g., because the sample size  $N_j$  is large), then  $\sigma_j^2$  will be small relative to  $\tau^2$ ; hence  $\hat{B}_j$  will be small, and we will put more weight on  $\bar{x}_j$  when we estimate  $\theta_j$ . However, groups with small sample sizes will get regularized (shrunk towards the overall mean  $\mu$ ) more heavily. We will see an example of this below.

If  $\sigma_j = \sigma$  for all groups j, the posterior mean becomes

$$
\hat{\theta}_j = \hat{B}\overline{x} + (1-\hat{B})\overline{x}_j = \overline{x} + (1-\hat{B})(\overline{x}_j - \overline{x})
$$
\n(5.86)

This has exactly the same form as the James Stein estimator discussed in Section 6.3.3.2.

### **5.6.2.1 Example: predicting baseball scores**

We now give an example of shrinkage applied to baseball batting averages, from (Efron and Morris 1975). We observe the number of hits for  $D = 18$  players during the first  $T = 45$  games. Call the number of hits  $b_i$ . We assume  $b_j \sim Bin(T, \theta_j)$ , where  $\theta_j$  is the "true" batting average for player j. The goal is to estimate the  $\theta_i$ . The MLE is of course  $\hat{\theta}_i = x_i$ , where  $x_i = b_i/T$  is the empirical batting average. However, we can use an EB approach to do better.

To apply the Gaussian shrinkage approach described above, we require that the likelihood be Gaussian,  $x_j \sim \mathcal{N}(\theta_j, \sigma^2)$  for known  $\sigma^2$ . (We drop the *i* subscript since we assume  $N_j = 1$ ,

Image /page/4/Figure/1 description: The image contains two plots, labeled (a) and (b). Plot (a) is a line graph showing multiple blue lines originating from a single point on the x-axis around 0.25 and extending upwards to points on the y-axis ranging from 0 to 1. The x-axis is labeled with values from 0.2 to 0.4. The title of plot (a) is "MLE (top) and shrinkage estimates (bottom)". Plot (b) is a bar chart comparing three different estimates: "true" (blue), "shrunk" (green), and "MLE" (red) for "player number" 1 through 5. The y-axis of plot (b) is labeled "MSE" and ranges from 0 to 0.4. The title of plot (b) indicates "MSE MLE = 0.0042, MSE shrunk = 0.0013". The legend for plot (b) shows the colors corresponding to "true", "shrunk", and "MLE".

**Figure 5.12** (a) MLE parameters (top) and corresponding shrunken estimates (bottom). (b) We plot the true parameters (blue), the posterior mean estimate (green), and the MLEs (red) for 5 of the players. Figure generated by shrinkageDemoBaseball.

since  $x_j$  already represents the average for player j.) However, in this example we have a binomial likelihood. While this has the right mean,  $\mathbb{E}[x_j] = \theta_j$ , the variance is not constant:

$$
\text{var}\left[x_j\right] = \frac{1}{T^2} \text{ var}\left[b_j\right] = \frac{T\theta_j(1-\theta_j)}{T^2} \tag{5.87}
$$

So let us apply a **variance stabilizing transform**<sup>5</sup> to  $x_j$  to better match the Gaussian assumption:

$$
y_j = f(y_j) = \sqrt{T} \arcsin(2y_j - 1)
$$
\n(5.88)

Now we have approximately  $y_j \sim \mathcal{N}(f(\theta_j), 1) = \mathcal{N}(\mu_j, 1)$ . We use Gaussian shrinkage to estimate the  $\mu_i$  using Equation 5.86 with  $\sigma^2 = 1$ , and we then transform back to get

$$
\hat{\theta}_j = 0.5(\sin(\hat{\mu}_j/\sqrt{T}) + 1) \tag{5.89}
$$

The results are shown in Figure 5.12(a-b). In (a), we plot the MLE  $\hat{\theta}_i$  and the posterior mean  $\overline{\theta}_i$ . We see that all the estimates have shrunk towards the global mean, 0.265. In (b), we plot the true value  $\theta_j$ , the MLE  $\hat{\theta}_j$  and the posterior mean  $\overline{\theta}_j$ . (The "true" values of  $\theta_j$  are estimated from a large number of independent games.) We see that, on average, the shrunken estimate is much closer to the true parameters than the MLE is. Specifically, the mean squared error, defined by MSE =  $\frac{1}{N} \sum_{j=1}^{D} (\theta_j - \overline{\theta}_j)^2$ , is over three times smaller using the shrinkage estimates  $\overline{\theta}_i$  than using the MLEs  $\hat{\theta}_i$ .

## **5.6.2.2 Estimating the hyper-parameters**

In this section, we give an algorithm for estimating *η*. Suppose initially that  $\sigma_j^2 = \sigma^2$  is the same for all groups. In this case, we can derive the EB estimate in closed form as we now show. same for all groups. In this case, we can derive the EB estimate in closed form, as we now show. From Equation 4.126, we have

$$
p(\overline{x}_j|\mu, \tau^2, \sigma^2) = \int \mathcal{N}(\overline{x}_j|\theta_j, \sigma^2) \mathcal{N}(\theta_j|\mu, \tau^2) d\theta_j = \mathcal{N}(\overline{x}_j|\mu, \tau^2 + \sigma^2)
$$
(5.90)

<sup>5.</sup> Suppose  $\mathbb{E}[X] = \mu$  and  $\text{var}[X] = \sigma^2(\mu)$ . Let  $Y = f(X)$ . Then a Taylor series expansions gives  $Y \approx$  $f(\mu) + (X - \mu)f'(\mu)$ . Hence var  $[Y] \approx f'(\mu)^2 \text{var}[X - \mu] = f'(\mu)^2 \sigma^2(\mu)$ . A variance stabilizing transformation is a function f such that  $f'(\mu)^2 \sigma^2(\mu)$  is independent of u is a function f such that  $f'(\mu)^2 \sigma^2(\mu)$  is independent of  $\mu$ .

Hence the marginal likelihood is

$$
p(\mathcal{D}|\mu,\tau^2,\sigma^2) = \prod_{j=1}^{D} \mathcal{N}(\overline{x}_j|\mu,\tau^2+\sigma^2)
$$
\n(5.91)

Thus we can estimate the hyper-parameters using the usual MLEs for a Gaussian. For  $\mu$ , we have

$$
\hat{\mu} = \frac{1}{D} \sum_{j=1}^{D} \overline{x}_j = \overline{x}
$$
\n(5.92)

which is the overall mean.

For the variance, we can use moment matching (which is equivalent to the MLE for a Gaussian): we simply equate the model variance to the empirical variance:

$$
\hat{\tau}^2 + \sigma^2 = \frac{1}{D} \sum_{j=1}^{D} (\overline{x}_j - \overline{x})^2 \triangleq s^2
$$
\n(5.93)

so  $\hat{\tau}^2 = s^2 - \sigma^2$ . Since we know  $\tau^2$  must be positive, it is common to use the following revised estimate:

$$
\hat{\tau}^2 = \max\{0, s^2 - \sigma^2\} = (s^2 - \sigma^2)_+
$$
\n(5.94)

Hence the shrinkage factor is

$$
\hat{B} = \frac{\sigma^2}{\sigma^2 + \hat{\tau}^2} = \frac{\sigma^2}{\sigma^2 + (s^2 - \sigma^2)_+}
$$
\n(5.95)

In the case where the  $\sigma_i^2$ 's are different, we can no longer derive a solution in closed form. Exercise 11.13 discusses how to use the EM algorithm to derive an EB estimate, and Exercise 24.4 discusses how to perform full Bayesian inference in this hierarchical model.

## **5.7 Bayesian decision theory**

We have seen how probability theory can be used to represent and updates our beliefs about the state of the world. However, ultimately our goal is to convert our beliefs into actions. In this section, we discuss the optimal way to do this.

We can formalize any given statistical decision problem as a game against nature (as opposed to a game against other strategic players, which is the topic of game theory, see e.g., (Shoham and Leyton-Brown 2009) for details). In this game, nature picks a state or parameter or label,  $y \in \mathcal{Y}$ , unknown to us, and then generates an observation,  $\mathbf{x} \in \mathcal{X}$ , which we get to see. We then have to make a decision, that is, we have to choose an action a from some **action space** A. Finally we incur some **loss**,  $L(y, a)$ , which measures how compatible our action a is with nature's hidden state y. For example, we might use misclassification loss,  $L(y, a) = \mathbb{I}(y \neq a)$ , or squared loss,  $L(y, a) = (y - a)^2$ . We will see some other examples below.

Our goal is to devise a **decision procedure** or **policy**,  $\delta : \mathcal{X} \to \mathcal{A}$ , which specifies the optimal action for each possible input. By optimal, we mean the action that minimizes the expected loss:

$$
\delta(\mathbf{x}) = \underset{a \in \mathcal{A}}{\operatorname{argmin}} \mathbb{E}\left[L(y, a)\right] \tag{5.96}
$$

In economics, it is more common to talk of a **utility function**; this is just negative loss,  $U(y, a) = -L(y, a)$ . Thus the above rule becomes

$$
\delta(\mathbf{x}) = \underset{a \in \mathcal{A}}{\operatorname{argmax}} \mathbb{E}\left[U(y, a)\right] \tag{5.97}
$$

This is called the **maximum expected utility principle**, and is the essence of what we mean by **rational behavior**.

Note that there are two different interpretations of what we mean by "expected". In the Bayesian version, which we discuss below, we mean the expected value of  $y$  given the data we have seen so far. In the frequentist version, which we discuss in Section 6.3, we mean the expected value of y and **x** that we expect to see in the future.

In the Bayesian approach to decision theory, the optimal action, having observed **x**, is defined as the action a that minimizes the **posterior expected loss**:

$$
\rho(a|\mathbf{x}) \triangleq \mathbb{E}_{p(y|\mathbf{x})} \left[ L(y, a) \right] = \sum_{y} L(y, a) p(y|\mathbf{x}) \tag{5.98}
$$

(If y is continuous (e.g., when we want to estimate a parameter vector), we should replace the sum with an integral.) Hence the **Bayes estimator**, also called the **Bayes decision rule**, is given by

$$
\delta(\mathbf{x}) = \arg\min_{a \in \mathcal{A}} \rho(\mathbf{a}|\mathbf{x})
$$
\n(5.99)

## **5.7.1 Bayes estimators for common loss functions**

In this section we show how to construct Bayes estimators for the loss functions most commonly arising in machine learning.

##### **5.7.1.1 MAP estimate minimizes 0-1 loss**

The **0-1 loss** is defined by

$$
L(y, a) = \mathbb{I}(y \neq a) = \begin{cases} 0 & \text{if } a = y \\ 1 & \text{if } a \neq y \end{cases}
$$
\n(5.100)

This is commonly used in classification problems where y is the true class label and  $a = \hat{y}$  is the estimate.

For example, in the two class case, we can write the loss matrix as follows:

|       | $\hat{y} = 1$ | $\hat{y} = 0$ |
|-------|---------------|---------------|
| $y=1$ | 0             | 1             |
| $y=0$ | 1             | 0             |

Image /page/7/Figure/1 description: A graph shows two curves, one blue and one red, plotted against an x-axis labeled 'x'. The y-axis is labeled from 0.0 to 1.0, with a dotted green line indicating a 'threshold'. The blue curve, labeled 'p(y=1 | x)', starts at 1.0 on the left and decreases as x increases, crossing the threshold and approaching 0.0. The red curve, labeled 'p(y=2 | x)', starts at 0.0 on the left and increases as x increases, crossing the threshold and leveling off at 1.0. Two vertical green lines are drawn, marking the boundaries of a region labeled 'Reject Region' on the x-axis. This region is located where the blue curve is below the threshold and the red curve is above the threshold.

**Figure 5.13** For some regions of input space, where the class posteriors are uncertain, we may prefer not to choose class 1 or 2; instead we may prefer the reject option. Based on Figure 1.26 of (Bishop 2006a).

(In Section 5.7.2, we generalize this loss function so it penalizes the two kinds of errors on the off-diagonal differently.)

The posterior expected loss is

$$
\rho(a|\mathbf{x}) = p(a \neq y|\mathbf{x}) = 1 - p(y|\mathbf{x})
$$
\n(5.10)

Hence the action that minimizes the expected loss is the posterior mode or MAP estimate

$$
y^*(\mathbf{x}) = \arg\max_{y \in \mathcal{Y}} p(y|\mathbf{x})
$$
\n(5.102)

## **5.7.1.2 Reject option**

In classification problems where  $p(y|\mathbf{x})$  is very uncertain, we may prefer to choose a **reject action**, in which we refuse to classify the example as any of the specified classes, and instead say "don't know". Such ambiguous cases can be handled by e.g., a human expert. See Figure 5.13 for an illustration. This is useful in **risk averse** domains such as medicine and finance.

We can formalize the reject option as follows. Let choosing  $a = C + 1$  correspond to picking the reject action, and choosing  $a \in \{1, \ldots, C\}$  correspond to picking one of the classes. Suppose we define the loss function as

$$
L(y=j, a=i) = \begin{cases} 0 & \text{if } i=j \text{ and } i, j \in \{1, ..., C\} \\ \lambda_r & \text{if } i = C+1 \\ \lambda_s & \text{otherwise} \end{cases}
$$
(5.103)

where  $\lambda_r$  is the cost of the reject action, and  $\lambda_s$  is the cost of a substitution error. In Exercise 5.3, you will show that the optimal action is to pick the reject action if the most probable class has a probability below  $1 - \frac{\lambda_r}{\lambda_s}$ ; otherwise you should just pick the most probable class.

Image /page/8/Figure/1 description: The image displays three plots labeled (a), (b), and (c). Plot (a) shows the function |x|^0.2, which has a sharp dip at x=0 and increases symmetrically as x moves away from zero. Plot (b) shows the function |x|^1.0, which is a V-shaped graph with its vertex at the origin, representing the absolute value function. Plot (c) shows the function |x|^2.0, which is a parabola opening upwards with its vertex at the origin, representing x squared. All plots have the x-axis ranging from -2 to 2 and the y-axis ranging from 0 to 2.

**Figure 5.14** (a-c). Plots of the  $L(y, a) = |y - a|^q$  vs  $|y - a|$  for  $q = 0.2$ ,  $q = 1$  and  $q = 2$ . Figure generated by loss Eunction Eig. generated by lossFunctionFig.

#### *********** Posterior mean minimizes $\ell_2$ (quadratic) loss

For continuous parameters, a more appropriate loss function is **squared error**,  $\ell_2$  **loss**, or **quadratic loss**, defined as

$$
L(y, a) = (y - a)^2
$$
\n(5.104)

The posterior expected loss is given by

$$
\rho(a|\mathbf{x}) = \mathbb{E}\left[ (y-a)^2|\mathbf{x} \right] = \mathbb{E}\left[ y^2|\mathbf{x} \right] - 2a\mathbb{E}\left[ y|\mathbf{x} \right] + a^2 \tag{5.105}
$$

Hence the optimal estimate is the posterior mean:

$$
\frac{\partial}{\partial a}\rho(a|\mathbf{x}) = -2\mathbb{E}[y|\mathbf{x}] + 2a = 0 \Rightarrow \hat{y} = \mathbb{E}[y|\mathbf{x}] = \int yp(y|\mathbf{x})dy \tag{5.106}
$$

This is often called the **minimum mean squared error** estimate or **MMSE** estimate.

In a linear regression problem, we have

$$
p(y|\mathbf{x}, \boldsymbol{\theta}) = \mathcal{N}(y|\mathbf{x}^T \mathbf{w}, \sigma^2)
$$
\n(5.107)

In this case, the optimal estimate given some training data  $D$  is given by

$$
\mathbb{E}\left[y|\mathbf{x},\mathcal{D}\right] = \mathbf{x}^T \mathbb{E}\left[\mathbf{w}|\mathcal{D}\right]
$$
\n(5.108)

That is, we just plug-in the posterior mean parameter estimate. Note that this is the optimal thing to do no matter what prior we use for **w**.

## **5.7.1.4** Posterior median minimizes $\ell_1$ (absolute) loss

The  $\ell_2$  loss penalizes deviations from the truth quadratically, and thus is sensitive to outliers. A more robust alternative is the absolute or  $\ell_1$  **loss**,  $L(y, a) = |y - a|$  (see Figure 5.14). The optimal estimate is the posterior median, i.e., a value a such that  $P(y < a|\mathbf{x}) = P(y \ge a|\mathbf{x}) = 0.5$ . See Exercise 5.9 for a proof.

#### **5.7.1.5 Supervised learning**

Consider a prediction function  $\delta : \mathcal{X} \to \mathcal{Y}$ , and suppose we have some cost function  $\ell(y, y')$  which gives the cost of predicting  $y'$  when the truth is  $y \in \mathbb{R}^n$ . We can define the loss incurred by which gives the cost of predicting  $y'$  when the truth is y. We can define the loss incurred by taking action  $\delta$  (i.e., using this predictor) when the unknown state of nature is  $\theta$  (the parameters of the data generating mechanism) as follows:

$$
L(\boldsymbol{\theta}, \delta) \triangleq \mathbb{E}_{(\mathbf{x}, y) \sim p(\mathbf{x}, y | \boldsymbol{\theta})} [\ell(y, \delta(\mathbf{x}))] = \sum_{\mathbf{x}} \sum_{y} L(y, \delta(\mathbf{x})) p(\mathbf{x}, y | \boldsymbol{\theta})
$$
(5.109)

This is known as the **generalization error**. Our goal is to minimize the posterior expected loss, given by

$$
\rho(\delta|\mathcal{D}) = \int p(\boldsymbol{\theta}|\mathcal{D}) L(\boldsymbol{\theta}, \delta) d\boldsymbol{\theta}
$$
\n(5.110)

This should be contrasted with the frequentist risk which is defined in Equation 6.47.

### **5.7.2 The false positive vs false negative tradeoff**

In this section, we focus on binary decision problems, such as hypothesis testing, two-class classification, object/ event detection, etc. There are two types of error we can make: a **false positive** (aka **false alarm**), which arises when we estimate  $\hat{y} = 1$  but the truth is  $y = 0$ ; or a **false negative** (aka **missed detection**), which arises when we estimate  $\hat{y} = 0$  but the truth is  $y = 1$ . The 0-1 loss treats these two kinds of errors equivalently. However, we can consider the following more general loss matrix:

$$
\begin{array}{c|cc}\n & \hat{y} = 1 & \hat{y} = 0 \\
\hline\ny = 1 & 0 & L_{FN} \\
y = 0 & L_{FP} & 0\n\end{array}
$$

 $y = 0$  |  $L_{FP}$  0<br>where  $L_{FN}$  is the cost of a false negative, and  $L_{FP}$  is the cost of a false positive. The posterior expected loss for the two possible actions is given by

$$
\rho(\hat{y} = 0 | \mathbf{x}) = L_{FN} p(y = 1 | \mathbf{x}) \tag{5.11}
$$

$$
\rho(\hat{y} = 1|\mathbf{x}) = L_{FP} p(y = 0|\mathbf{x}) \tag{5.112}
$$

Hence we should pick class  $\hat{y} = 1$  iff

$$
\rho(\hat{y} = 0|\mathbf{x}) \quad > \quad \rho(\hat{y} = 1|\mathbf{x}) \tag{5.113}
$$

$$
\frac{p(y=1|\mathbf{x})}{p(y=0|\mathbf{x})} > \frac{L_{FP}}{L_{FN}}
$$
\n(5.114)

If  $L_{FN} = cL_{FP}$ , it is easy to show (Exercise 5.10) that we should pick  $\hat{y} = 1$  iff  $p(y = 1)$  $1|\mathbf{x}|/p(y=0|\mathbf{x}) > \tau$ , where  $\tau = c/(1+c)$  (see also (Muller et al. 2004)). For example, if a false negative costs twice as much as false positive, so  $c = 2$ , then we use a decision threshold of 2/3 before declaring a positive.

Below we discuss ROC curves, which provide a way to study the FP-FN tradeoff without having to choose a specific threshold.

## **5.7.2.1 ROC curves and all that**

Suppose we are solving a binary decision problem, such as classification, hypothesis testing, object detection, etc. Also, assume we have a labeled data set,  $\mathcal{D} = \{(\mathbf{x}_i, y_i)\}\$ . Let  $\delta(\mathbf{x}) =$ 

| Estimate | Truth           |                 | Σ                       |
|----------|-----------------|-----------------|-------------------------|
|          | 1               | 0               |                         |
| 1        | TP              | FP              | $\hat{N}_+ = TP + FP$   |
| 0        | FN              | TN              | $\hat{N}_- = FN + TN$   |
| Σ        | $N_+ = TP + FN$ | $N_- = FP + TN$ | $N = TP + FP + FN + TN$ |

**Table 5.2** Quantities derivable from a confusion matrix.  $N_+$  is the true number of positives,  $\hat{N}_+$  is the "called" number of positives,  $N_-\,$  is the true number of negatives,  $N_-\,$  is the "called" number of negatives.

| $y=1$                                                                     | $y=0$               |
|---------------------------------------------------------------------------|---------------------|
| $\hat{y} = 1$   $TP/N_{+}$ =TPR=sensitivity=recall                        | $FP/N$ = FPR=type I |
| $\hat{y} = 0$   $FN/N_+$ =FNR=miss rate=type II   $TN/N_-$ =TNR=specifity |                     |
|                                                                           |                     |

**Table 5.3** Estimating  $p(\hat{y}|y)$  from a confusion matrix. Abbreviations: FNR = false negative rate, FPR = false positive rate,  $TNR = true$  negative rate,  $TPR = true$  positive rate.

 $\mathbb{I}(f(\mathbf{x}) > \tau)$  be our decision rule, where  $f(\mathbf{x})$  is a measure of confidence that  $y = 1$  (this should be monotonically related to  $p(y = 1|\mathbf{x})$ , but does not need to be a probability), and  $\tau$  is some threshold parameter. For each given value of  $\tau$ , we can apply our decision rule and count the number of true positives, false positives, true negatives, and false negatives that occur, as shown in Table 5.2. This table of errors is called a **confusion matrix**.

From this table, we can compute the **true positive rate** (TPR), also known as the **sensitivity**, **recall** or **hit rate**, by using  $TPR = TP/N_+ \approx p(\hat{y} = 1|y = 1)$ . We can also compute the **false positive rate** (FPR), also called the **false alarm rate**, or the **type I error rate**, by using  $FPR = FP/N_-\approx p(\hat{y}=1|y=0)$ . These and other definitions are summarized in Tables 5.3 and 5.4. We can combine these errors in any way we choose to compute a loss function.

However, rather than than computing the TPR and FPR for a fixed threshold  $\tau$ , we can run our detector for a set of thresholds, and then plot the TPR vs FPR as an implicit function of  $\tau$ . This is called a **receiver operating characteristic** or **ROC** curve. See Figure 5.15(a) for an example. Any system can achieve the point on the bottom left,  $(FPR = 0, TPR = 0)$ , by setting  $\tau = 1$  and thus classifying everything as negative; similarly any system can achieve the point on the top right,  $(FPR = 1, TPR = 1)$ , by setting  $\tau = 0$  and thus classifying everything as positive. If a system is performing at chance level, then we can achieve any point on the diagonal line  $TPR = FPR$  by choosing an appropriate threshold. A system that perfectly separates the positives from negatives has a threshold that can achieve the top left corner,  $(FPR = 0, TPR = 1)$ ; by varying the threshold such a system will "hug" the left axis and then the top axis, as shown in Figure 5.15(a).

The quality of a ROC curve is often summarized as a single number using the **area under the curve** or **AUC**. Higher AUC scores are better; the maximum is obviously 1. Another summary statistic that is used is the **equal error rate** or **EER**, also called the **cross over rate**, defined as the value which satisfies  $FPR = FNR$ . Since  $FNR = 1 - TPR$ , we can compute the EER by drawing a line from the top left to the bottom right and seeing where it intersects the ROC curve (see points A and B in Figure 5.15(a)). Lower EER scores are better; the minimum is obviously 0.

Image /page/11/Figure/1 description: The image displays two plots, labeled (a) and (b). Plot (a) is a Receiver Operating Characteristic (ROC) curve, with 'fpr' on the x-axis and 'tpr' on the y-axis, both ranging from 0 to 1. It shows two curves, labeled 'A' (red) and 'B' (blue), both starting at (0,0) and ending at (1,1). A diagonal black line from (0,1) to (1,0) is also present. The area under curve 'B' is shaded with blue vertical lines. Plot (b) is a precision-recall curve, with 'recall' on the x-axis and 'precision' on the y-axis, both ranging from 0 to 1. It also shows two curves, labeled 'A' (red) and 'B' (blue), both starting at (0,1) and ending at (1,0). Curve 'A' is above curve 'B' for most of the plot.

**Figure 5.15** (a) ROC curves for two hypothetical classification systems. A is better than B. We plot the true positive rate (TPR) vs the false positive rate (FPR) as we vary the threshold  $\tau$ . We also indicate the equal error rate (EER) with the red and blue dots, and the area under the curve (AUC) for classifier B. (b) A precision-recall curve for two hypothetical classification systems. A is better than B. Figure generated by PRhand.

|             | $y=1$                                  | $y=0$                 |
|-------------|----------------------------------------|-----------------------|
| $\hat{y}=1$ | $TP/N_{+}=\text{precision}=\text{PPV}$ | $FP/N_{+}=\text{FDP}$ |
| $\hat{y}=0$ | $FN/N_{-}$                             | $TN/N_{-}=\text{NPV}$ |

**Table 5.4** Estimating  $p(y|\hat{y})$  from a confusion matrix. Abbreviations: FDP = false discovery probability,  $NPV$  = negative predictive value,  $PPV$  = positive predictive value,

## **5.7.2.2 Precision recall curves**

When trying to detect a rare event (such as retrieving a relevant document or finding a face in an image), the number of negatives is very large. Hence comparing  $TPR = TP/N_{+}$  to  $FPR = FP/N_$  is not very informative, since the FPR will be very small. Hence all the "action" in the ROC curve will occur on the extreme left. In such cases, it is common to plot the TPR versus the number of false positives, rather than vs the false positive rate.

However, in some cases, the very notion of "negative" is not well-defined. For example, when detecting objects in images (see Section 1.2.1.3), if the detector works by classifying patches, then the number of patches examined — and hence the number of true negatives — is a parameter of the algorithm, not part of the problem definition. So we would like to use a measure that only talks about positives.

The **precision** is defined as  $TP/\hat{N}_+ = p(y = 1|\hat{y} = 1)$  and the **recall** is defined as  $TP/N_{+} = p(\hat{y} = 1|y = 1)$ . Precision measures what fraction of our detections are actually positive, and recall measures what fraction of the positives we actually detected. If  $\hat{y}_i \in \{0, 1\}$ is the predicted label, and  $y_i \in \{0,1\}$  is the true label, we can estimate precision and recall using

$$
P = \frac{\sum_{i} y_i \hat{y}_i}{\sum_{i} \hat{y}_i}, \ R = \frac{\sum_{i} y_i \hat{y}_i}{\sum_{i} y_i}
$$
\n
$$
(5.115)
$$

A **precision recall curve** is a plot of precision vs recall as we vary the threshold  $\tau$ . See Figure 5.15(b). Hugging the top right is the best one can do.

This curve can be summarized as a single number using the mean precision (averaging over

|                | Class 1          |                 |                | Class 2             |     |                | Pooled      |      |
|----------------|------------------|-----------------|----------------|---------------------|-----|----------------|-------------|------|
|                | $y=1$ $y=0$      |                 |                | $y=1$ $y=0$         |     |                | $y=1$ $y=0$ |      |
|                | $\hat{v} = 1$ 10 | $\overline{10}$ |                | $\hat{y} = 1$ 90 10 |     | $\hat{u}=1$    | 100         | 20   |
| $\hat{u}=0$ 10 |                  | 970             | $\hat{y}=0$ 10 |                     | 890 | $\hat{y}=0$ 20 |             | 1860 |
|                |                  |                 |                |                     |     |                |             |      |

**Table 5.5** Illustration of the difference between macro- and micro-averaging. y is the true label, and  $\hat{y}$ is the called label. In this example, the macro-averaged precision is  $\left[\frac{10}{10} + \frac{10}{90}\right] + \frac{90}{10} + \frac{90}{2} =$  $(0.5+0.9)/2=0.7$ . The micro-averaged precision is  $100/(100+20) \approx 0.83$ . Based on Table 13.7 of (Manning et al. 2008).

recall values), which approximates the area under the curve. Alternatively, one can quote the precision for a fixed recall level, such as the precision of the first  $K = 10$  entities recalled. This is called the **average precision at K** score. This measure is widely used when evaluating information retrieval systems.

## **5.7.2.3 F-scores \***

For a fixed threshold, one can compute a single precision and recall value. These are often combined into a single statistic called the **F score**, or **F1 score**, which is the harmonic mean of precision and recall:

$$
F_1 \triangleq \frac{2}{1/P + 1/R} = \frac{2PR}{R+P}
$$
\n
$$
(5.116)
$$

Using Equation 5.115, we can write this as

$$
F_1 = \frac{2\sum_{i=1}^{N} y_i \hat{y}_i}{\sum_{i=1}^{N} y_i + \sum_{i=1}^{N} \hat{y}_i}
$$
\n(5.117)

This is a widely used measure in information retrieval systems.

To understand why we use the harmonic mean instead of the arithmetic mean,  $(P + R)/2$ , consider the following scenario. Suppose we recall all entries, so  $R = 1$ . The precision will be given by the **prevalence**,  $p(y = 1)$ . Suppose the prevalence is low, say  $p(y = 1) = 10^{-4}$ . The arithmetic mean of P and R is given by  $(P + R)/2 = (10^{-4} + 1)/2 \approx 50\%$ . By contrast, the harmonic mean of this strategy is only  $\frac{2 \times 10^{-4} \times 1}{1+10^{-4}} \approx 0.2\%$ .<br>In the multi-class case (e.g. for document classification

In the multi-class case (e.g., for document classification problems), there are two ways to generalize  $F_1$  scores. The first is called **macro-averaged F1**, and is defined as  $\sum_{c=1}^{C} F_1(c)/C$ , where  $F_c(c)$  is the  $F_c$  score obtained on the task of distinguishing class c from all the others where  $F_1(c)$  is the  $F_1$  score obtained on the task of distinguishing class c from all the others. The other is called **micro-averaged F1**, and is defined as the  $F_1$  score where we pool all the counts from each class's contingency table.

Table 5.5 gives a worked example that illustrates the difference. We see that the precision of class 1 is 0.5, and of class 2 is 0.9. The macro-averaged precision is therefore 0.7, whereas the micro-averaged precision is 0.83. The latter is much closer to the precision of class 2 than to the precision of class 1, since class 2 is five times larger than class 1. To give equal weight to each class, use macro-averaging.

#### ********* False discovery rates \***

Suppose we are trying to discover a rare phenomenon using some kind of high throughput measurement device, such as a gene expression micro array, or a radio telescope. We will need to make many binary decisions of the form  $p(y_i = 1|D) > \tau$ , where  $D = {\mathbf{x}_i}_{i=1}^N$  and N may<br>be large. This is called **multiple hypothesis testing**. Note that the difference from standard be large. This is called **multiple hypothesis testing**. Note that the difference from standard binary classification is that we are classifying  $y_i$  based on all the data, not just based on  $x_i$ . So this is a simultaneous classification problem, where we might hope to do better than a series of individual classification problems.

How should we set the threshold  $\tau$ ? A natural approach is to try to minimize the expected number of false positives. In the Bayesian approach, this can be computed as follows:

$$
FD(\tau, \mathcal{D}) \triangleq \sum_{i} \underbrace{(1 - p_i)}_{\text{pr. error discovery}} \underbrace{\mathbb{I}(p_i > \tau)}_{\text{(5.118)}}
$$

where  $p_i \triangleq p(y_i = 1 | \mathcal{D})$  is your belief that this object exhibits the phenomenon in question.<br>We then define the posterior expected **false discovery rate** as follows: We then define the posterior expected **false discovery rate** as follows:

$$
FDR(\tau, \mathcal{D}) \triangleq FD(\tau, \mathcal{D})/N(\tau, \mathcal{D})
$$
\n(5.119)

where  $N(\tau, \mathcal{D}) = \sum_i \mathbb{I}(p_i > \tau)$  is the number of discovered items. Given a desired FDR tolerance say  $\alpha = 0.05$  one can then adapt  $\tau$  to achieve this is called the **direct noterior** tolerance, say  $\alpha = 0.05$ , one can then adapt  $\tau$  to achieve this; this is called the **direct posterior probability approach** to controlling the FDR (Newton et al. 2004; Muller et al. 2004).

In order to control the FDR it is very helpful to estimate the  $p_i$ 's jointly (e.g., using a hierarchical Bayesian model, as in Section 5.5), rather than independently. This allows the pooling of statistical strength, and thus lower FDR. See e.g., (Berry and Hochberg 1999) for more information.

## **5.7.3 Other topics \***

In this section, we briefly mention a few other topics related to Bayesian decision theory. We do not have space to go into detail, but we include pointers to the relevant literature.

#### **5.7.3.1 Contextual bandits**

A **one-armed bandit** is a colloquial term for a slot machine, found in casinos around the world. The game is this: you insert some money, pull an arm, and wait for the machine to stop; if you're lucky, you win some money. Now imagine there is a bank of  $K$  such machines to choose from. Which one should you use? This is called a **multi-armed bandit**, and can be modeled using Bayesian decision theory: there are  $K$  possible actions, and each action has an unknown reward (payoff function)  $r_k$ . By maintaining a belief state,  $p(r_{1:K}|\mathcal{D}) = \prod_k p(r_k|\mathcal{D})$ , one can<br>devise an optimal policy; this can be compiled into a series of **Gittins Indices** (Gittins 1989) devise an optimal policy; this can be compiled into a series of **Gittins Indices** (Gittins 1989). This optimally solves the **exploration-exploitation** tradeoff, which specifies how many times one should try each action before deciding to go with the winner.

Now consider an extension where each arm, and the player, has an associated feature vector; call all these features **x**. This is called a **contextual bandit** (see e.g., (Sarkar 1991; Scott 2010; Li et al. 2011)). For example, the "arms" could represent ads or news articles which we want to show to the user, and the features could represent properties of these ads or articles, such

#### *5.7. Bayesian decision theory* 185

as a bag of words, as well as properties of the user, such as demographics. If we assume a linear model for reward,  $r_k = \theta_k^T \mathbf{x}$ , we can maintain a distribution over the parameters of each  $r_k = \theta_k^T \mathbf{x}$ , we can maintain a distribution over the parameters of each arm,  $p(\theta_k|\mathcal{D})$ , where  $\mathcal D$  is a series of tuples of the form  $(a, \mathbf x, r)$ , which specifies which arm was pulled, what its features were, and what the resulting outcome was (e.g.,  $r = 1$  if the user clicked on the ad, and  $r = 0$  otherwise). We discuss ways to compute  $p(\theta_k|\mathcal{D})$  from linear and logistic regression models in later chapters.

Given the posterior, we must decide what action to take. One common heuristic, known as **UCB** (which stands for "upper confidence bound") is to take the action which maximizes

$$
k^* = \underset{k=1}{\text{argmax}} \,\mu_k + \lambda \sigma_k \tag{5.120}
$$

where  $\mu_k = \mathbb{E}[r_k|\mathcal{D}]$ ,  $\sigma_k^2 = \text{var}[r_k|\mathcal{D}]$  and  $\lambda$  is a tuning parameter that trades off exploration<br>and exploitation. The intuition is that we should nick actions about which we believe are good and exploitation. The intuition is that we should pick actions about which we believe are good ( $\mu_k$  is large), and/ or actions about which we are uncertain ( $\sigma_k$  is large).

An even simpler method, known as **Thompson sampling**, is as follows. At each step, we pick action  $k$  with a probability that is equal to its probability of being the optimal action:

$$
p_k = \int \mathbb{I}(\mathbb{E}[r|a, \mathbf{x}, \boldsymbol{\theta}] = \max_{a'} \mathbb{E}[r|a', \mathbf{x}, \boldsymbol{\theta}]) p(\boldsymbol{\theta}|\mathcal{D}) d\boldsymbol{\theta}
$$
(5.121)

We can approximate this by drawing a single sample from the posterior,  $\theta^t \sim p(\theta|\mathcal{D})$ , and then choosing  $k^* = \arg \max_k \mathbb{E} \left[ r | \mathbf{x}, k, \boldsymbol{\theta}^t \right]$ . Despite its simplicity, this has been shown to work quite well (Chapelle and Li 2011).

### **5.7.3.2 Utility theory**

Suppose we are a doctor trying to decide whether to operate on a patient or not. We imagine there are 3 states of nature: the patient has no cancer, the patient has lung cancer, or the patient has breast cancer. Since the action and state space is discrete, we can represent the loss function  $L(\theta, a)$  as a **loss matrix**, such as the following:

|               | Surgery | No surgery |
|---------------|---------|------------|
| No cancer     | 20      | 0          |
| Lung cancer   | 10      | 50         |
| Breast cancer | 10      | 60         |

These numbers reflects the fact that not performing surgery when the patient has cancer is very bad (loss of 50 or 60, depending on the type of cancer), since the patient might die; not performing surgery when the patient does not have cancer incurs no loss (0); performing surgery when the patient does not have cancer is wasteful (loss of 20); and performing surgery when the patient does have cancer is painful but necessary (10).

It is natural to ask where these numbers come from. Ultimately they represent the personal **preferences** or values of a fictitious doctor, and are somewhat arbitrary: just as some people prefer chocolate ice cream and others prefer vanilla, there is no such thing as the "right" loss/ utility function. However, it can be shown (see e.g., (DeGroot 1970)) that any set of consistent preferences can be converted to a scalar loss/ utility function. Note that utility can be measured on an arbitrary scale, such as dollars, since it is only relative values that matter.<sup>6</sup>

<sup>6.</sup> People are often squeamish about talking about human lives in monetary terms, but all decision making requires

#### **5.7.3.3 Sequential decision theory**

So far, we have concentrated on **one-shot decision problem**s, where we only have to make one decision and then the game ends. In Setion 10.6, we will generalize this to multi-stage or sequential decision problems. Such problems frequently arise in many business and engineering settings. This is closely related to the problem of reinforcement learning. However, further discussion of this point is beyond the scope of this book.

### **Exercises**

**Exercise 5.1** Proof that a mixture of conjugate priors is indeed conjugate

Derive Equation 5.69.

**Exercise 5.2** Optimal threshold on classification probability

Consider a case where we have learned a conditional probability distribution  $P(y|\mathbf{x})$ . Suppose there are only two classes, and let  $p_0 = P(Y = 0|\mathbf{x})$  and  $p_1 = P(Y = 1|\mathbf{x})$ . Consider the loss matrix below:

| predicted<br>label $\hat{y}$ | true label $y$ |                |
|------------------------------|----------------|----------------|
|                              | 0              | 1              |
| 0                            | 0              | $\lambda_{01}$ |
| 1                            | $\lambda_{10}$ | 0              |

- a. Show that the decision  $\hat{y}$  that minimizes the expected loss is equivalent to setting a probability threshold θ and predicting  $\hat{y} = 0$  if  $p_1 < θ$  and  $\hat{y} = 1$  if  $p_1 \ge θ$ . What is θ as a function of  $\lambda_{01}$  and  $\lambda_{10}$ ? (Show your work.)
- b. Show a loss matrix where the threshold is 0.1. (Show your work.)

**Exercise 5.3** Reject option in classifiers

(Source: (Duda et al. 2001, Q2.13).)

In many classification problems one has the option either of assigning  $x$  to class j or, if you are too uncertain, of choosing the **reject option**. If the cost for rejects is less than the cost of falsely classifying the object, it may be the optimal action. Let  $\alpha_i$  mean you choose action i, for  $i = 1 : C + 1$ , where C is the number of classes and  $C + 1$  is the reject action. Let  $Y = i$  be the true (but unknown) **state of nature**. Define the loss function as follows

$$
\lambda(\alpha_i|Y=j) = \begin{cases}\n0 & \text{if } i=j \text{ and } i, j \in \{1, \dots, C\} \\
\lambda_r & \text{if } i = C+1 \\
\lambda_s & \text{otherwise}\n\end{cases}
$$
\n(5.122)

In otherwords, you incur 0 loss if you correctly classify, you incur  $\lambda_r$  loss (cost) if you choose the reject option, and you incur  $\lambda_s$  loss (cost) if you make a substitution error (misclassification).

tradeoffs, and one needs to use some kind of "currency" to compare different courses of action. Insurance companies do this all the time. Ross Schachter, a decision theorist at Stanford University, likes to tell a story of a school board who rejected a study on absestos removal from schools because it performed a **cost-benefit analysis**, which was considered "inhumane" because they put a dollar value on children's health; the result of rejecting the report was that the absestos was not removed, which is surely more "inhumane". In medical domains, one often measures utility in terms of **QALY**, or quality-adjusted life-years, instead of dollars, but it's the same idea. Of course, even if you do not explicitly specify how much you value different people's lives, your *behavior* will reveal your implicit values/ preferences, and these preferences can then be converted to a real-valued scale, such as dollars or QALY. Inferring a utility function from behavior is called **inverse reinforcement learning**.

| Decision  | true label $y$ |    |
|-----------|----------------|----|
|           | $\hat{y}$      | 0  |
| predict 0 | 0              | 10 |
| predict 1 | 10             | 0  |
| reject    | 3              | 3  |

- a. Show that the minimum risk is obtained if we decide  $Y = j$  if  $p(Y = j|\mathbf{x}) \ge p(Y = k|\mathbf{x})$  for all k (i.e., *j* is the most probable class) *and* if  $p(Y = j | \mathbf{x}) \ge 1 - \frac{\lambda_r}{\lambda_s}$ ; otherwise we decide to reject.
- b. Describe qualitatively what happens as  $\lambda_r/\lambda_s$  is increased from 0 to 1 (i.e., the relative cost of rejection increases).

##### **Exercise 5.4** More reject options

In many applications, the classifier is allowed to "reject" a test example rather than classifying it into one of the classes. Consider, for example, a case in which the cost of a misclassification is \$10 but the cost of having a human manually make the decison is only \$3. We can formulate this as the following loss matrix:

- a. Suppose  $P(y = 1|\mathbf{x})$  is predicted to be 0.2. Which decision minimizes the expected loss?
- b. Now suppose  $P(y = 1|\mathbf{x})=0.4$ . Now which decision minimizes the expected loss?
- c. Show that in general, for this loss matrix, but for any posterior distribution, there will be two thresholds  $\theta_0$  and  $\theta_1$  such that the optimal decisionn is to predict 0 if  $p_1 < \theta_0$ , reject if  $\theta_0 \leq p_1 \leq \theta_1$ , and predict 1 if  $p_1 > \theta_1$  (where  $p_1 = p(y = 1|\mathbf{x})$ ). What are these thresholds?

#### **Exercise 5.5** Newsvendor problem

Consider the following classic problem in decision theory/ economics. Suppose you are trying to decide how much quantity Q of some product (e.g., newspapers) to buy to maximize your profits. The optimal amount will depend on how much demand  $D$  you think there is for your product, as well as its cost to you C and its selling price P. Suppose D is unknown but has pdf  $f(D)$  and cdf  $F(D)$ . We can evaluate the expected profit by considering two cases: if  $D > Q$ , then we sell all Q items, and make profit  $\pi = (P - C)Q$ ; but if  $D < Q$ , we only sell D items, at profit  $(P - C)D$ , but have wasted  $C(Q - D)$ on the unsold items. So the expected profit if we buy quantity  $Q$  is

$$
E\pi(Q) = \int_{Q}^{\infty} (P - C)Qf(D)dD + \int_{0}^{Q} (P - C)Df(D) - \int_{0}^{Q} C(Q - D)f(D)dD
$$
 (5.123)

Simplify this expression, and then take derivatives wrt  $Q$  to show that the optimal quantity  $Q^*$  (which maximizes the expected profit) satisfies

$$
F(Q^*) = \frac{P - C}{P} \tag{5.124}
$$

#### **Exercise 5.6** Bayes factors and ROC curves

Let  $B = p(D|H_1)/p(D|H_0)$  be the bayes factor in favor of model 1. Suppose we plot two ROC curves, one computed by thresholding B, and the other computed by thresholding  $p(H_1|D)$ . Will they be the same or different? Explain why.

#### **Exercise 5.7** Bayes model averaging helps predictive accuracy

Let  $\Delta$  be a quantity that we want to predict, let  $\mathcal D$  be the observed data and  $\mathcal M$  be a finite set of models. Suppose our action is to provide a probabilistic prediction  $p()$ , and the loss function is  $L(\Delta, p() ) =$   $-\log p(\Delta)$ . We can either perform Bayes model averaging and predict using

$$
p^{BMA}(\Delta) = \sum_{m \in \mathcal{M}} p(\Delta|m, \mathcal{D}) p(m|\mathcal{D})
$$
\n(5.125)

or we could predict using any single model (a plugin approximation)

$$
p^{m}(\Delta) = p(\Delta|m, \mathcal{D}) \tag{5.126}
$$

Show that, for all models  $m \in \mathcal{M}$ , the posterior expected loss using BMA is lower, i.e.,

$$
\mathbb{E}\left[L(\Delta, p^{BMA})\right] \leq \mathbb{E}\left[L(\Delta, p^{m})\right]
$$
\n(5.127)

where the expectation over  $\Delta$  is with respect to

$$
p(\Delta|\mathcal{D}) = \sum_{m \in \mathcal{M}} p(\Delta|m, \mathcal{D}) p(m|\mathcal{D})
$$
\n(5.128)

Hint: use the non-negativity of the KL divergence.

**Exercise 5.8** MLE and model selection for a 2d discrete distribution

#### (Source: Jaakkola.)

Let  $x \in \{0, 1\}$  denote the result of a coin toss ( $x = 0$  for tails,  $x = 1$  for heads). The coin is potentially biased, so that heads occurs with probability  $\theta_1$ . Suppose that someone else observes the coin flip and reports to you the outcome, y. But this person is unreliable and only reports the result correctly with probability  $\theta_2$ ; i.e.,  $p(y|x, \theta_2)$  is given by

$$
\begin{array}{c|cc}\n & y = 0 & y = 1 \\
\hline\nx = 0 & \theta_2 & 1 - \theta_2 \\
x = 1 & 1 - \theta_2 & \theta_2\n\end{array}
$$

Assume that  $\theta_2$  is independent of x and  $\theta_1$ .

- a. Write down the joint probability distribution  $p(x, y|\theta)$  as a 2 × 2 table, in terms of  $\theta = (\theta_1, \theta_2)$ .
- b. Suppose have the following dataset:  $x = (1, 1, 0, 1, 1, 0, 0)$ ,  $y = (1, 0, 0, 0, 1, 0, 1)$ . What are the MLEs for  $\theta_1$  and  $\theta_2$ ? Justify your answer. Hint: note that the likelihood function factorizes,

$$
p(x, y|\boldsymbol{\theta}) = p(y|x, \theta_2)p(x|\theta_1)
$$
\n(5.129)

What is  $p(\mathcal{D}|\boldsymbol{\theta}, M_2)$  where  $M_2$  denotes this 2-parameter model? (You may leave your answer in fractional form if you wish.)

- c. Now consider a model with 4 parameters,  $\boldsymbol{\theta} = (\theta_{0,0}, \theta_{0,1}, \theta_{1,0}, \theta_{1,1})$ , representing  $p(x, y | \boldsymbol{\theta}) = \theta_{x,y}$ . (Only 3 of these parameters are free to vary, since they must sum to one.) What is the MLE of *θ*? What is  $p(\mathcal{D}|\boldsymbol{\theta}, M_4)$  where  $M_4$  denotes this 4-parameter model?
- d. Suppose we are not sure which model is correct. We compute the leave-one-out cross validated log likelihood of the 2-parameter model and the 4-parameter model as follows:

$$
L(m) = \sum_{i=1}^{n} \log p(x_i, y_i | m, \hat{\theta}(\mathcal{D}_{-i}))
$$
\n(5.130)

and  $\theta(\mathcal{D}_{-i})$ ) denotes the MLE computed on  $\mathcal D$  excluding row i. Which model will CV pick and why? Hint: notice how the table of counts changes when you omit each training case one at a time.

e. Recall that an alternative to CV is to use the BIC score, defined as

$$
\text{BIC}(M,\mathcal{D}) \triangleq \log p(\mathcal{D}|\hat{\boldsymbol{\theta}}_{MLE}) - \frac{\text{dof}(M)}{2}\log N
$$
\n(5.131)

where  $\text{dof}(M)$  is the number of free parameters in the model, Compute the BIC scores for both models (use log base  $e$ ). Which model does BIC prefer? (use log base e). Which model does BIC prefer?

**Exercise 5.9** Posterior median is optimal estimate under L1 loss

Prove that the posterior median is optimal estimate under L1 loss.

**Exercise 5.10** Decision rule for trading off FPs and FNs

If  $L_{FN} = cL_{FP}$ , show that we should pick  $\hat{y} = 1$  iff  $p(y = 1|\mathbf{x})/p(y = 0|\mathbf{x}) > \tau$ , where  $\tau = c/(1+c)$ 

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
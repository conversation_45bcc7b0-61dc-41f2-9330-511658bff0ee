# GENERATIVE TEACHING NETWORKS: ACCELERATING NEURAL ARCHITECTURE SEARCH BY LEARNING TO GENERATE SYNTHETIC TRAINING DATA

<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>& <PERSON><sup>∗</sup> Uber AI Labs

# ABSTRACT

This paper investigates the intriguing question of whether we can create learning algorithms that automatically generate training data, learning environments, and curricula in order to help AI agents rapidly learn. We show that such algorithms are possible via Generative Teaching Networks (GTNs), a general approach that is, in theory, applicable to supervised, unsupervised, and reinforcement learning, although our experiments only focus on the supervised case. GTNs are deep neural networks that generate data and/or training environments that a learner (e.g. a freshly initialized neural network) trains on for a few SGD steps before being tested on a target task. We then differentiate *through the entire learning process* via meta-gradients to update the GTN parameters to improve performance on the target task. GTNs have the beneficial property that they can theoretically generate any type of data or training environment, making their potential impact large. This paper introduces GTNs, discusses their potential, and showcases that they can substantially accelerate learning. We also demonstrate a practical and exciting application of GTNs: accelerating the evaluation of candidate architectures for neural architecture search (NAS), which is rate-limited by such evaluations, enabling massive speed-ups in NAS. GTN-NAS improves the NAS state of the art, finding higher performing architectures when controlling for the search proposal mechanism. GTN-NAS also is competitive with the overall state of the art approaches, which achieve top performance while using orders of magnitude less computation than typical NAS methods. Speculating forward, GTNs may represent a first step toward the ambitious goal of algorithms that generate their own training data and, in doing so, open a variety of interesting new research questions and directions.

# 1 INTRODUCTION AND RELATED WORK

Access to vast training data is now common in machine learning. However, to effectively train neural networks (NNs) does not require using *all available* data. For example, recent work in curriculum learning [\(Graves et al., 2017\)](#page-8-0), active learning [\(Konyushkova et al., 2017;](#page-9-0) [Settles, 2010\)](#page-10-0) and core-set selection [\(Sener & Savarese, 2018;](#page-10-1) [Tsang et al., 2005\)](#page-10-2) demonstrates that a surrogate dataset can be created by intelligently sampling a subset of training data, and that such surrogates enable competitive test performance with less training effort. Being able to more rapidly determine the performance of an architecture in this way could particularly benefit architecture search, where training thousands or millions of candidate NN architectures on full datasets can become prohibitively expensive. From this lens, related work in learning-to-teach has shown promise. For example, the learning to teach (L2T) [\(Fan et al., 2018\)](#page-8-1) method accelerates learning for a NN learner (hereafter, just *learner*) through reinforcement learning, by learning how to subsample mini-batches of data.

A key insight in this paper is that the surrogate data need not be drawn from the original data distribution (i.e. they may not need to resemble the original data). For example, humans can learn new skills from reading a book or can prepare for a team game like soccer by practicing skills, such as passing, dribbling, juggling, and shooting. This paper investigates the question of whether we can train a data-generating network that can produce *synthetic* data that effectively and efficiently

<sup>∗</sup> co-senior authors. Corresponding authors: {felipe.such,kstanley,jeffclune}@uber.com

teaches a target task to a learner. Related to the idea of generating data, Generative Adversarial Networks (GANs) can produce impressive high-resolution images [\(Goodfellow et al., 2014;](#page-8-2) [Brock](#page-8-3) [et al., 2018\)](#page-8-3), but they are incentivized to mimic real data [\(Goodfellow et al., 2014\)](#page-8-2), instead of being optimized to teach learners *more* efficiently than real data.

Another approach for creating surrogate training data is to treat the training data itself as a hyperparameter of the training process and learn it directly. Such learning can be done through metagradients (also called hyper-gradients), i.e. differentiating through the training process to optimize a meta-objective. This approach was described in [Maclaurin et al.](#page-9-1) [\(2015\)](#page-9-1), where 10 synthetic training images were learned using meta-gradients such that when a network is trained on these images, the network's performance on the MNIST validation dataset is maximized. In recent work concurrent with our own, [Wang et al.](#page-10-3) [\(2019b\)](#page-10-3) scaled this idea to learn 100 synthetic training examples. While the 100 synthetic examples were more effective for training than 100 original (real) MNIST training examples, we show that it is difficult to scale this approach much further without the regularity across samples provided by a generative architecture (Figure [2b,](#page-5-0) green line).

Being able to very quickly train learners is particularly valuable for neural architecture search (NAS), which is exciting for its potential to automatically discover high-performing architectures, which otherwise must be undertaken through time-consuming manual experimentation for new domains. Many advances in NAS involve accelerating the evaluation of candidate architectures by training a predictor of how well a trained learner would perform, by extrapolating from previously trained architectures [\(Luo et al., 2018;](#page-9-2) [Liu et al., 2018a;](#page-9-3) [Baker et al., 2017\)](#page-8-4). This approach is still expensive because it requires many architectures to be trained and evaluated to train the predictor. Other approaches accelerate training by sharing training across architectures, either through shared weights (e.g. as in ENAS; [Pham et al.](#page-9-4) [\(2018\)](#page-9-4)), or Graph HyperNetworks [\(Zhang et al., 2018\)](#page-10-4).

We propose a scalable, novel, meta-learning approach for creating synthetic data called Generative Teaching Networks (GTNs). GTN training has two nested training loops: an inner loop to train a learner network, and an outer-loop to train a generator network that produces synthetic training data for the learner network. Experiments presented in Section [3](#page-3-0) demonstrate that the GTN approach produces synthetic data that enables much faster learning, speeding up the training of a NN by a factor of 9. Importantly, the synthetic data in GTNs is not only agnostic to the weight initialization of the learner network (as in [Wang et al.](#page-10-3) [\(2019b\)](#page-10-3)), but is also agnostic to the learner's *architecture*. As a result, GTNs are a viable method for *accelerating evaluation* of candidate architectures in NAS. Indeed, controlling for the search algorithm (i.e. using GTN-produced synthetic data as a drop-in replacement for real data when evaluating a candidate architecture's performance), GTN-NAS improves the NAS state of the art by finding higher-performing architectures than comparable methods like weight sharing [\(Pham et al., 2018\)](#page-9-4) and Graph HyperNetworks [\(Zhang et al., 2018\)](#page-10-4); it also is competitive with methods using more sophisticated search algorithms and orders of magnitude more computation. It could also be combined with those methods to provide further gains.

One promising aspect of GTNs is that they make very few assumptions about the learner. In contrast, NAS techniques based on shared training are viable only if the parameterizations of the learners are similar. For example, it is unclear how weight-sharing or HyperNetworks could be applied to architectural search spaces wherein layers could be either convolutional or fully-connected, as there is no obvious way for weights learned for one layer type to inform those of the other. In contrast, GTNs are able to create training data that can generalize between such diverse types of architectures.

GTNs also open up interesting new research questions and applications to be explored by future work. Because they can rapidly train new architectures, GTNs could be used to create NNs *ondemand* that meet specific design constraints (e.g. a given balance of performance, speed, and energy usage) and/or have a specific subset of skills (e.g. perhaps one needs to rapidly create a compact network capable of three particular skills). Because GTNs can generate virtually any learning environment, they also one day could be a key to creating AI-generating algorithms, which seek to bootstrap themselves from simple initial conditions to powerful forms of AI by creating an openended stream of challenges (learning opportunities) while learning to solve them [\(Clune, 2019\)](#page-8-5).

# 2 METHODS

The main idea in GTNs is to train a data-generating network such that a learner network trained on data it *rapidly* produces high accuracy in a target task. Unlike a GAN, here the two networks

cooperate (rather than compete) because their interests are aligned towards having the learner perform well on the target task when trained on data produced by the GTN. The generator and the learner networks are trained with meta-learning via nested optimization that consists of inner and outer training loops (Figure [1a\)](#page-2-0). In the inner-loop, the generator  $G(z, y)$  takes Gaussian noise  $(z)$ and a label  $(y)$  as input and outputs synthetic data  $(x)$ . Optionally, the generator could take only noise as input and produce both data and labels as output (Appendix [F\)](#page-13-0). The learner is then trained on this synthetic data for a fixed number of inner-loop training steps with any optimizer, such as SGD or Adam [\(Kingma & Ba, 2014\)](#page-9-5): we use SGD with momentum in this paper. SI Equation [1](#page-11-0) defines the inner-loop SGD with momentum update for the learner parameters  $\theta_t$ . We sample  $z_t$ (noise vectors input to the generator) from a unit-variance Gaussian and  $y_t$  labels for each generated sample) uniformly from all available class labels. Note that both  $z_t$  and  $y_t$  are batches of samples. We can also learn a curriculum directly by additionally optimizing  $z_t$  directly (instead of sampling it randomly) and keeping  $y_t$  fixed throughout all of training.

The inner-loop loss function  $\ell_{\text{inner}}$  can be cross-entropy for classification problems or mean squared error for regression problems. Note that the inner-loop objective does not depend on the outerloop objective and could even be parameterized and learned through meta-gradients with the rest of the system [\(Houthooft et al., 2018\)](#page-9-6). In the outer-loop, the learner  $\theta_T$  (i.e. the learner parameters trained on *synthetic* data after the T inner-loop steps) is evaluated on the real *training* data, which is used to compute the outer-loop loss (aka meta-training loss). The gradient of the meta-training loss with respect to the generator is computed by backpropagating through the entire inner-loop learning process. While computing the gradients for the generator we also compute the gradients of hyperparameters of the inner-loop SGD update rule (its learning rate and momentum), which are updated after each outer-loop at no additional cost. To reduce memory requirements, we leverage gradientcheckpointing [\(Griewank & Walther, 2000\)](#page-8-6) when computing meta-gradients. The computation and memory complexity of our approach can be found in Appendix [D.](#page-12-0)

<span id="page-2-0"></span>Image /page/2/Figure/2 description: The image displays a three-part figure. The first part is a diagram illustrating a meta-learning process with an outer loop and an inner loop. The inner loop involves a Generator and a Learner, taking (1) Noise and (2) Data as input, with a (3) SGD Step updating the Learner. The outer loop uses (4) Meta-loss calculated from Real Data and feeds the (5) Gradient of Meta-loss w.r.t. Generator back to the Generator. The second part is a box plot comparing Validation Loss for models 'Without WN' and 'With WN'. The 'Without WN' box plot shows a wide range of validation loss, with a median around 0.2 and an outlier at 1.2. The 'With WN' box plot shows a very low validation loss, with a median close to 0.0. The third part is a line graph showing Test Accuracy over Outer-loop Iterations (from 0 to 2000). Four lines represent different curriculum strategies: 'No Curriculum' (red), 'All Shuffled' (blue), 'Shuffled Batch' (green), and 'Full Curriculum' (purple). The 'Full Curriculum' line shows the highest test accuracy, consistently above 0.975, while 'No Curriculum' shows the lowest, starting around 0.850 and reaching about 0.950.

(a) Overview of Generative Teaching Networks (b) GTN stability with WN (c) GTN curricula comparison

Figure 1: (a) Generative Teaching Network (GTN) Method. The numbers in the figure reflect the order in which a GTN is executed. Noise is fed as an input to the Generator (1), which uses it to generate new data (2). The learner is trained (e.g. using SGD or Adam) to perform well on the generated data (3). The trained learner is then evaluated on the real training data in the outer-loop to compute the outer-loop meta-loss (4). The gradients of the generator parameters are computed w.r.t. to the meta-loss to update the generator (5). Both a learned curriculum and weight normalization substantially improve GTN performance. (b) Weight normalization improves meta-gradient training of GTNs, and makes the method much more robust to different hyperparameter settings. Each boxplot reports the final loss of 20 runs obtained *during* hyperparameter optimization with Bayesian Optimization (lower is better). (c) shows a comparison between GTNs with different types of curricula. The GTN method with the most control over how samples are presented performs the best.

A key motivation for this work is to generate synthetic data that is learner agnostic, i.e. that generalizes across different potential learner architectures and initializations. To achieve this objective, at the beginning of each new outer-loop training, we choose a new learner architecture according to a predefined set and randomly initialize it (details in Appendix [A\)](#page-11-1).

Meta-learning with Weight Normalization. Optimization through meta-gradients is often unstable [\(Maclaurin et al., 2015\)](#page-9-1). We observed that this instability greatly complicates training because of its hyperparameter sensitivity, and training quickly diverges if they are not well-set. Combining the gradients from Evolution Strategies [\(Salimans et al., 2017\)](#page-10-5) and backpropagation using inverse variance weighting [\(Fleiss, 1993;](#page-8-7) [Metz et al., 2019\)](#page-9-7) improved stability in our experiments, but optimization still consistently diverged whenever we increased the number of inner-loop optimization

steps. To mitigate this issue, we introduce applying weight normalization [\(Salimans & Kingma,](#page-10-6) [2016\)](#page-10-6) to stabilize meta-gradient training by normalizing the generator and learner weights. Instead of updating the weights (W) directly, we parameterize them as  $W = g \cdot V / ||V||$  and instead update the scalar g and vector V. Weight normalization eliminates the need for (and cost of) calculating ES gradients and combining them with backprop gradients, simplifying and speeding up the algorithm.

We hypothesize that weight normalization will help stabilize meta-gradient training more broadly, although future work is required to test this hypothesis in meta-learning contexts besides GTNs. The idea is that applying weight normalization to meta-learning techniques is analogous to batch normalization for deep networks [\(Ioffe & Szegedy, 2015\)](#page-9-8). Batch normalization normalizes the forward propagation of activations in a long sequence of parameterized operations (a deep NN). In meta-gradient training both the activations and weights result from a long sequence of parameterized operations and thus both should be normalized. Results in section [3.1](#page-4-0) support this hypothesis.

Learning a Curriculum with Generative Teaching Networks. Previous work has shown that a learned curriculum can be more effective than training from uniformly sampled data [\(Graves et al.,](#page-8-0) [2017\)](#page-8-0). A curriculum is usually encoded with indexes to samples from a given dataset, rendering it non-differentiable and thereby complicating the curriculum's optimization. With GTNs however, a curriculum can be encoded as a series of input vectors to the generator (i.e. instead of sampling the  $z_t$  inputs to the generator from a Gaussian distribution, a sequence of  $z_t$  inputs can be learned). A curriculum can thus be learned by differentiating through the generator to optimize this sequence (in addition to the generator's parameters). Experiments confirm that GTNs more effectively teach learners when optimizing such a curriculum (Section [3.2\)](#page-4-1).

Accelerating NAS with Generative Teaching Networks. Since GTNs can accelerate learner training, we propose harnessing GTNs to accelerate NAS. Rather than evaluating each architecture in a target task with a standard training procedure, we propose evaluating architectures with a metaoptimized training process (that generates synthetic data in addition to optimizing inner-loop hyperparameters). We show that doing so significantly reduces the cost of running NAS (Section [3.4\)](#page-5-1).

The goal of these experiments is to find a high-performing CNN architecture for the CIFAR10 image-classification task [\(Krizhevsky et al., 2009\)](#page-9-9) with limited compute costs. We use the same architecture search-space, training procedure, hyperparameters, and code from Neural Architecture Optimization [\(Luo et al., 2018\)](#page-9-2), a state-of-the-art NAS method. The search space consists of the topology of two cells: a reduction cell and a convolutional cell. Multiple copies of such cells are stacked according to a predefined blueprint to form a full CNN architecture (see [Luo et al.](#page-9-2) [\(2018\)](#page-9-2) for more details). The blueprint has two hyperparameters N and F that control how many times the convolutional cell is repeated (depth) and the width of each layer, respectively. Each cell contains  $B = 5$  nodes. For each node within a cell, the search algorithm has to choose two inputs as well as two operations to apply to those inputs. The inputs to a node can be previous nodes or the outputs of the last two layers. There are 11 operations to choose from (Appendix [C\)](#page-12-1).

Following [Luo et al.](#page-9-2) [\(2018\)](#page-9-2), we report the performance of our best cell instantiated with  $N =$  $6, F = 36$  after the resulting architecture is trained for a significant amount of time (600 epochs). Since evaluating each architecture in those settings (named *final evaluation* from now on) is time consuming, [Luo et al.](#page-9-2) [\(2018\)](#page-9-2) uses a surrogate evaluation (named *search evaluation*) to estimate the performance of a given cell wherein a smaller version of the architecture ( $N = 3, F = 32$ ) is trained for less epochs (100) on real data. We further reduce the evaluation time of each cell by replacing the training data in the search evaluation with GTN synthetic data, thus reducing the training time per evaluation by 300x (which we call *GTN evaluation*). While we were able to train GTNs directly on the complex architectures from the NAS search space, training was prohibitively slow. Instead, for these experiments, we optimize our GTN ahead of time using proxy learners described in Appendix [A.2,](#page-11-2) which are smaller fully-convolutional networks (this meta-training took 8h on one p6000 GPU). Interestingly, although we never train our GTN on any NAS architectures, because of generalization, synthetic data from GTNs were still effective for training them.

# <span id="page-3-0"></span>3 RESULTS

We first demonstrate that weight normalization significantly improves the stability of meta-learning, an independent contribution of this paper (Section [3.1\)](#page-4-0). We then show that training with synthetic data is more effective when learning such data jointly with a curriculum that orders its presentation

to the learner (Section [3.2\)](#page-4-1). We next show that GTNs can generate a synthetic training set that enables more rapid learning in a few SGD steps than real training data in two supervised learning domains (MNIST and CIFAR10) and in a reinforcement learning domain (cart-pole, Appendix [H\)](#page-15-0). We then apply GTN-synthetic training data for neural architecture search to find high performing architectures for CIFAR10 with limited compute, outperforming comparable methods like weight sharing [\(Pham et al., 2018\)](#page-9-4) and Graph HyperNetworks [\(Zhang et al., 2018\)](#page-10-4) (Section [3.4\)](#page-5-1).

We uniformly split the usual MNIST *training* set into training (50k) and validation sets (10k). The training set was used for inner-loop training (for the baseline) and to compute meta-gradients for all the treatments. We used the validation set for hyperparameter tuning and report accuracy on the usual MNIST test set (10k images). We followed the same procedure for CIFAR10, resulting in training, validation, and test sets with 45k, 5k, and 10k examples, respectively. Unless otherwise specified, we ran each experiment 5 times and plot the mean and its 95% confidence intervals from (n=1,000) bootstrapping. Appendix [A](#page-11-1) describes additional experimental details.

### <span id="page-4-0"></span>3.1 IMPROVING STABILITY WITH WEIGHT NORMALIZATION

To demonstrate the effectiveness of weight normalization for stabilizing and robustifying metaoptimization, we compare the results of running hyperparameter optimization for GTNs with and without weight normalization on MNIST. Figure [1b](#page-2-0) shows the distribution of the final performance obtained for 20 runs *during* hyperparameter tuning, which reflects how sensitive the algorithms are to hyperparameter settings. Overall, weight normalization substantially improved robustness to hyperparameters and final learner performance, supporting the initial hypothesis.

## <span id="page-4-1"></span>3.2 IMPROVING GTNS WITH A CURRICULUM

We experimentally evaluate four different variants of GTNs, each with increasing control over the ordering of the  $z$  codes input to the generator, and thus the order of the inputs provided to the learner. The first variant (called *GTN - No Curriculum*), trains a generator to output synthetic training data by sampling the noise vector  $z$  for each sample independently from a Gaussian distribution. In the next three GTN variants, the generator is provided with a fixed set of input samples (instead of a noise vector). These input samples are learned along with the generator parameters during GTN training. The second GTN variant (called *GTN - All Shuffled*) learns a fixed set of 4,096 input samples that are presented in a random order without replacement (thus learning controls the data, but not the order in which they are presented). The third variant (called *GTN - Shuffled Batch*) learns 32 batches of 128 samples each (so learning controls which samples coexist within a batch), but the order in which the batches are presented is randomized (without replacement). Finally, the fourth variant (called *GTN - Full Curriculum*) learns a deterministic sequence of 32 batches of 128 samples, giving learning full control. Learning such a curriculum incurs no additional computational expense, as learning the  $z_t$  tensor is computationally negligible and avoids the cost of repeatedly sampling new Gaussian  $z$  codes. We plot the test accuracy of a learner (with random initial weights and architecture) as a function of outer-loop iterations for all four variants in Figure [1c.](#page-2-0) Although *GTNs - No curriculum* can seemingly generate endless data (see Appendix [G\)](#page-14-0), it performs worse than the other three variants with a fixed set of generator inputs. Overall, training the GTN with exact ordering of input samples (*GTN - Full Curriculum*) outperforms all other variants.

While curriculum learning usually refers to training on easy tasks first and increasing their difficulty over time, our curriculum goes beyond presenting tasks in a certain order. Specifically, *GTN - Full Curriculum* learns both the order in which to present samples and the specific group of samples to present at the same time. The ability to learn a full curriculum improves GTN performance. For that reason, we adopt that approach for all GTN experiments.

#### 3.3 GTNS FOR SUPERVISED LEARNING

To explore whether GTNs can generate training data that helps networks learn rapidly, we compare to 3 treatments for MNIST classification. 1) *Real Data* - Training learners with random mini-batches of real data, as is ubiquitous in SGD. 2) *Dataset Distillation* - Training learners with synthetic data, where training examples are directly encoded as tensors optimized by the meta-objective, as in [Wang](#page-10-3) [et al.](#page-10-3) [\(2019b\)](#page-10-3). 3) *GTN* - Our method where the training data presented to the learner is generated by a neural network. Note that all three methods meta-optimize the inner-loop hyperparameters (i.e. the learning rate and momentum of SGD) as part of the meta-optimization.

We emphasize that producing state-of-the-art (SOTA) performance (e.g. on MNIST or CIFAR) when training with GTN-generated data is *not* important for GTNs. Because the ultimate aim for GTNs is to accelerate NAS (Section [3.4\)](#page-5-1), what matters is *how well and inexpensively we can identify* architectures that achieve high *asymptotic accuracy* when later trained on the full (real) training set. A means to that end is being able to train architectures rapidly, i.e. with very few SGD steps, because doing so allows NAS to rapidly identify promising architectures. We are thus interested in "few-step accuracy (i.e. accuracy after a few–e.g. 32 or 128–SGD steps). Besides, there are many reasons not to expect SOTA performance with GTNs (Appendix [B\)](#page-12-2).

Figure [2a](#page-5-0) shows that the GTN treatment significantly outperforms the other ones ( $p < 0.01$ ) and trains a learner to be much more accurate when *in the few-step performance regime*. Specifically, for each treatment the figure shows the test performance of a learner following 32 inner-loop training steps with a batch size of 128. We would not expect training on synthetic data to produce higher accuracy than unlimited SGD steps on real data, but here the performance gain comes because GTNs can *compress* the real training data by producing synthetic data that enables learners to learn more quickly than on real data. For example, the original dataset might contain many similar images, where only a few of them would be sufficient for training (and GTN can produce just these few). GTN could also combine many different things that need to be learned about images into one image.

Figure [2b](#page-5-0) shows the few-step performance of a learner from each treatment after 2000 total outerloop iterations (∼1 hour on a p6000 GPU). For reference, Dataset Distillation [\(Wang et al., 2019b\)](#page-10-3) reported 79.5% accuracy for a randomly initialized network (using 100 synthetic images vs. our 4,096) and L2T [\(Fan et al., 2018\)](#page-8-1) reported needing 300x more training iterations to achieve  $> 98\%$ MNIST accuracy. Surprisingly, although recognizable as digits and effective for training, GTNgenerated images (Figure [2c\)](#page-5-0) were not visually realistic (see Discussion).

<span id="page-5-0"></span>Image /page/5/Figure/3 description: The image displays three plots. Plot (a) is titled "Meta-training curves" and shows test accuracy on the y-axis against outer-loop iterations on the x-axis. The plot includes three lines: GTN (red), Real Data (blue), and Dataset Distillation (green), with shaded regions indicating variance. The x-axis ranges from 0 to 2000, and the y-axis ranges from 0.90 to 1.00. Plot (b) is titled "Training curves" and shows test accuracy on the y-axis against inner-loop iterations on the x-axis. It also features three lines: GTN (red), Real Data (blue), and Dataset Distillation (green), with shaded regions. The x-axis ranges from 0 to 30, and the y-axis ranges from 0.90 to 1.00. Plot (c) is titled "GTN-generated samples" and presents a grid of grayscale images, each appearing to be a handwritten digit, with some digits being clearer than others.

Figure 2: Teaching MNIST with GTN-generated images. (a) MNIST test set few-step accuracy across outer-loop iterations for different sources of inner-loop training data. The inner-loop consists of 32 SGD steps and the outer-loop optimizes MNIST validation accuracy. Our method (GTN) outperforms the two controls (dataset distillation and samples from real data). (b) For the final meta-training iteration, across inner-loop training, accuracy on the MNIST test set when inner-loop training on different data sources. (c) 100 random samples from the trained GTN. Samples are often recognizable as digits, but are not realistic (see Discussion). Each column contains samples from a different digit class, and each row is taken from different inner-loop iterations (evenly spaced from the 32 total iterations, with early iterations at the top).

#### <span id="page-5-1"></span>3.4 ARCHITECTURE SEARCH WITH GTNS

We next test the benefits of GTN for NAS (GTN-NAS) in CIFAR10, a domain where NAS has previously shown significant improvements over the best architectures produced by armies of human scientists. Figure [3a](#page-6-0) shows the few-step training accuracy of a learner trained with either GTN-synthetic data or real (CIFAR10) data over meta-training iterations. After 8h of meta-training, training with GTN-generated data was significantly faster than with real data, as in MNIST.

To explore the potential for GTN-NAS to accelerate CIFAR10 architecture search, we investigated the Spearman rank correlation (across architectures sampled from the NAS search space) between accelerated GTN-trained network performance (*GTN evaluation*) and the usual more expensive performance metric used during NAS (*search evaluation*). A correlation plot is shown in Figure [3c;](#page-6-0) note that a strong correlation implies we can train architectures using GTN evaluation as an inexpensive surrogate. We find that GTN evaluation enables predicting the performance of an architecture efficiently. The rank-correlation between 128 *steps* of training with GTN-synthetic data vs. 100 *epochs* of real data is 0.3606. The correlation improves to 0.5582 when considering the top 50% of architectures recommended by GTN evaluation scores, which is important because those are the ones that search would select. This improved correlation is slightly stronger than that from *3 epochs* of training with real data (0.5235), a  $\sim$  9× cost-reduction per trained model.

<span id="page-6-0"></span>Image /page/6/Figure/1 description: The image displays three plots related to CIFAR10. Plot (a) is a line graph titled 'CIFAR10 inner-loop training' showing 'Training Accuracy' on the y-axis and 'Inner-loop Iterations' on the x-axis, with two lines representing 'GTN' (red) and 'Real Data' (blue). Plot (b) is a grid of CIFAR10 GTN samples, showing a collection of colorful images. Plot (c) is a scatter plot titled 'CIFAR10 correlation' with 'GTN Predicted Performance' on the x-axis and 'Real Data Predicted Perf.' on the y-axis, featuring black 'x' markers and blue square markers.

Figure 3: Teaching CIFAR10 with GTN-generated images. (a) CIFAR10 training set performance of the final learner (after 1,700 meta-optimization steps) across inner-loop learning iterations. (b) Samples generated by GTN to teach CIFAR10 are unrecognizable, despite being effective for training. Each column contains a different class, and each row is taken from the same inner-loop iteration (evenly spaced from all 128 iterations, early iterations at the top). (c) Correlation between performance prediction using GTN-data vs. Real Data. When considering the top half of architectures (as ranked by GTN evaluation), correlation between GTN evaluation and search evaluation is strong (0.5582 rank-correlation), suggesting that GTN-NAS has potential to uncover high performing architectures at a significantly lower cost. Architectures shown are uniformly sampled from the NAS search space. The top 10% of architectures according to the GTN evaluation (blue squares)– those likely to be selected by GTN-NAS–have high true asymptotic accuracy.

Architecture search methods are composed of several semi-independent components, such as the choice of search space, search algorithm, and proxy evaluation of candidate architectures. GTNs are proposed as an improvement to this last component, i.e. as a new way to quickly evaluate a new architecture. Thus we test our method under the standard search space for CIFAR10, using a simple form of search (random search) for which there are previous benchmark results. In particular, we ran an architecture search experiment where we evaluated 800 randomly generated architectures trained with GTN-synthetic data. We present the performance after *final evaluation* of the best architecture found in Table [1.](#page-7-0) This experimental setting is similar to that of [Zhang et al.](#page-10-4) [\(2018\)](#page-10-4). Highlighting the potential of GTNs as an improved proxy evaluation for architectures, we achieve state-of-the-art results when controlling for search algorithm (the choice of which is orthogonal to our contribution). While it is an apples-to-oranges comparison, GTN-NAS is competitive even with methods that use more advanced search techniques than random search to propose architectures (Appendix [E\)](#page-13-1). GTN is compatible with such techniques, and would likely improve their performance, an interesting area of future work. Furthermore, because of the NAS search space, the modules GTN found can be used to create even larger networks. A further test of whether GTNs predictions generalize is if such larger networks would continue performing better than architectures generated by the realdata control, similarly scaled. We tried F=128 and show it indeed does perform better (Table [1\)](#page-7-0), suggesting additional gains can be had by searching post-hoc for the correct F and N settings.

# 4 DISCUSSION, FUTURE WORK, AND CONCLUSION

The results presented here suggest potential future applications and extensions of GTNs. Given the ability of GTNs to rapidly train new models, they are particularly useful when training many independent models is required (as we showed for NAS). Another such application would be to teach networks on demand to realize particular trade-offs between e.g. accuracy, inference time, and memory requirements. While to address a range of such trade-offs would ordinarily require training many models ahead of time and selecting amongst them [\(Elsken et al., 2019\)](#page-8-8), GTNs could instead rapidly train a new network only when a particular trade-off is needed. Similarly, agents with unique combinations of skills could be created on demand when needed.

<span id="page-7-0"></span>Table 1: Performance of different architecture search methods. Our results report mean  $\pm$  SD of 5 evaluations of the same architecture with different initializations. It is common to report scores with and without Cutout [\(DeVries & Taylor, 2017\)](#page-8-9), a data augmentation technique used during training. We found better architectures compared to other methods that reduce architecture evaluation speed and were tested with random search (Random Search+WS and Random Search+GHN). Increasing the width of the architecture found (F=128) further improves performance. Because each NAS method finds a different architecture, the number of parameters differs. Each method ran once.

| Model                                                 | Error(%)        | #params | GPU Days |
|-------------------------------------------------------|-----------------|---------|----------|
| Random Search + GHN (Zhang et al., 2018)              | $4.3 \pm 0.1$   | 5.1M    | 0.42     |
| Random Search + Weight Sharing (Luo et al., 2018)     | 3.92            | 3.9M    | 0.25     |
| Random Search + Real Data (baseline)                  | $3.88 \pm 0.08$ | 12.4M   | 10       |
| Random Search + GTN (ours)                            | $3.84 \pm 0.06$ | 8.2M    | 0.67     |
| Random Search + Real Data + Cutout (baseline)         | $3.02 \pm 0.03$ | 12.4M   | 10       |
| Random Search + GTN + Cutout (ours)                   | $2.92 \pm 0.06$ | 8.2M    | 0.67     |
| Random Search + Real Data + Cutout (F=128) (baseline) | $2.51 \pm 0.13$ | 151.7M  | 10       |
| Random Search + GTN + Cutout (F=128) (ours)           | $2.42 \pm 0.03$ | 97.9M   | 0.67     |

Interesting questions are raised by the lack of similarity between the synthetic GTN data and real MNIST and CIFAR10 data. That unrealistic and/or unrecognizable images can meaningfully affect NNs is reminiscent of the finding that deep neural networks are easily fooled by unrecognizable images [\(Nguyen et al., 2015\)](#page-9-10). It is possible that if neural network architectures were functionally more similar to human brains, GTNs' synthetic data might more resemble real data. However, an alternate (speculative) hypothesis is that the human brain might also be able to rapidly learn an arbitrary skill by being shown unnatural, unrecognizable data (recalling the novel Snow Crash).

The improved stability of training GTNs from weight normalization naturally suggests the hypothesis that weight normalization might similarly stabilize, and thus meaningfully improve, any techniques based on meta-gradients (e.g. MAML [\(Finn et al., 2017\)](#page-8-10), learned optimizers [\(Metz et al.,](#page-9-7) [2019\)](#page-9-7), and learned update rules [\(Metz et al., 2018\)](#page-9-11)). In future work, we will more deeply investigate how consistently, and to what degree, this hypothesis holds.

Both weight sharing and GHNs can be combined with GTNs by using the shared weights or Hyper-Network for initialization of proposed learners and then fine-tuning on GTN-produced data. GTNs could also be combined with more intelligent ways to propose which architecture to sample next such as NAO [\(Luo et al., 2018\)](#page-9-2). Many other extensions would also be interesting to consider. GTNs could be trained for unsupervised learning, for example by training a useful embedding function. Additionally, they could be used to stabilize GAN training and prevent mode collapse (Appendix [I](#page-16-0) shows encouraging initial results). One particularly promising extension is to introduce a closedloop curriculum (i.e. one that responds dynamically to the performance of the learner throughout training), which we believe could significantly improve performance. For example, a recurrent GTN that is conditioned on previous learner outputs could adapt its samples to be appropriately easier or more difficult depending on an agent's learning progress, similar in spirit to the approach of a human tutor. Such closed-loop teaching can improve learning [\(Fan et al., 2018\)](#page-8-1).

An additional interesting direction is having GTNs generate training environments for RL agents. Appendix [H](#page-15-0) shows this works for the simple RL task of CartPole. That could be either for a predefined target task, or could be combined with more open-ended algorithms that attempt to continuously generate new, different, interesting tasks that foster learning [\(Clune, 2019;](#page-8-5) [Wang et al.,](#page-10-7) [2019a\)](#page-10-7). Because GTNs can encode any possible environment, they (or something similar) may be necessary to have truly unconstrained, open-ended algorithms [\(Stanley et al., 2017\)](#page-10-8). If techniques could be invented to coax GTNs to produce recognizable, human-meaningful training environments, the technique could also produce interesting virtual worlds for us to learn in, play in, or explore.

This paper introduces a new method called Generative Teaching Networks, wherein data generators are trained to produce effective training data through meta-learning. We have shown that such an approach can produce supervised datasets that yield better few-step accuracy than an equivalent amount of real training data, and generalize across architectures and random initializations. We leverage such efficient training data to create a fast NAS method that generates state-of-the-art architectures (controlling for the search algorithm). While GTNs may be of particular interest to the

field of architecture search (where the computational cost to evaluate candidate architectures often limits the scope of its application), we believe that GTNs open up an intriguing and challenging line of research into a variety of algorithms that learn to generate their own training data.

# 5 ACKNOWLEDGEMENTS

For insightful discussions and suggestions, we thank the members of Uber AI Labs, especially Theofanis Karaletsos, Martin Jankowiak, Thomas Miconi, Joost Huizinga, and Lawrence Murray.

# REFERENCES

- <span id="page-8-12"></span>Marcin Andrychowicz, Bowen Baker, Maciek Chociej, Rafal Jozefowicz, Bob McGrew, Jakub Pachocki, Arthur Petron, Matthias Plappert, Glenn Powell, Alex Ray, et al. Learning dexterous in-hand manipulation. *arXiv preprint arXiv:1808.00177*, 2018.
- <span id="page-8-4"></span>Bowen Baker, Otkrist Gupta, Ramesh Raskar, and Nikhil Naik. Accelerating neural architecture search using performance prediction. *arXiv preprint arXiv:1705.10823*, 2017.
- <span id="page-8-3"></span>Andrew Brock, Jeff Donahue, and Karen Simonyan. Large scale gan training for high fidelity natural image synthesis. *arXiv preprint arXiv:1809.11096*, 2018.
- <span id="page-8-11"></span>Greg Brockman, Vicki Cheung, Ludwig Pettersson, Jonas Schneider, John Schulman, Jie Tang, and Wojciech Zaremba. Openai gym. *arXiv preprint arXiv:1606.01540*, 2016.
- <span id="page-8-5"></span>Jeff Clune. Ai-gas: Ai-generating algorithms, an alternate paradigm for producing general artificial intelligence. *arXiv preprint arXiv:1905.10985*, 2019.
- <span id="page-8-9"></span>Terrance DeVries and Graham W Taylor. Improved regularization of convolutional neural networks with cutout. *arXiv preprint arXiv:1708.04552*, 2017.
- <span id="page-8-13"></span>Gamaleldin F. Elsayed, Ian J. Goodfellow, and Jascha Sohl-Dickstein. Adversarial reprogramming of neural networks. *CoRR*, abs/1806.11146, 2018. URL [http://arxiv.org/abs/1806.](http://arxiv.org/abs/1806.11146) [11146](http://arxiv.org/abs/1806.11146).
- <span id="page-8-8"></span>Thomas Elsken, Jan Hendrik Metzen, and Frank Hutter. Efficient multi-objective neural architecture search via lamarckian evolution. In *International Conference on Learning Representations*, 2019.
- <span id="page-8-1"></span>Yang Fan, Fei Tian, Tao Qin, Xiang-Yang Li, and Tie-Yan Liu. Learning to teach. *arXiv preprint arXiv:1805.03643*, 2018.
- <span id="page-8-14"></span>Chelsea Finn and Sergey Levine. Meta-learning and universality: Deep representations and gradient descent can approximate any learning algorithm. *arXiv preprint arXiv:1710.11622*, 2017.
- <span id="page-8-10"></span>Chelsea Finn, Pieter Abbeel, and Sergey Levine. Model-agnostic meta-learning for fast adaptation of deep networks. In *Proceedings of the 34th International Conference on Machine Learning-Volume 70*, pp. 1126–1135. JMLR. org, 2017.
- <span id="page-8-7"></span>JL Fleiss. Review papers: The statistical basis of meta-analysis. *Statistical methods in medical research*, 2(2):121–145, 1993.
- <span id="page-8-2"></span>Ian Goodfellow, Jean Pouget-Abadie, Mehdi Mirza, Bing Xu, David Warde-Farley, Sherjil Ozair, Aaron Courville, and Yoshua Bengio. Generative adversarial nets. In *Advances in neural information processing systems*, pp. 2672–2680, 2014.
- <span id="page-8-0"></span>Alex Graves, Marc G. Bellemare, Jacob Menick, Remi Munos, and Koray Kavukcuoglu. Automated ´ curriculum learning for neural networks. In *Proceedings of the 34th International Conference on Machine Learning, ICML 2017, Sydney, NSW, Australia, 6-11 August 2017*, pp. 1311–1320, 2017.
- <span id="page-8-6"></span>Andreas Griewank and Andrea Walther. Algorithm 799: revolve: an implementation of checkpointing for the reverse or adjoint mode of computational differentiation. *ACM Transactions on Mathematical Software (TOMS)*, 26(1):19–45, 2000.

- <span id="page-9-12"></span>Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Delving deep into rectifiers: Surpassing human-level performance on imagenet classification. In *Proceedings of the IEEE international conference on computer vision*, pp. 1026–1034, 2015.
- <span id="page-9-16"></span>Geoffrey E. Hinton, Oriol Vinyals, and Jeffrey Dean. Distilling the knowledge in a neural network. *CoRR*, abs/1503.02531, 2015.
- <span id="page-9-6"></span>Rein Houthooft, Yuhua Chen, Phillip Isola, Bradly Stadie, Filip Wolski, OpenAI Jonathan Ho, and Pieter Abbeel. Evolved policy gradients. In S. Bengio, H. Wallach, H. Larochelle, K. Grauman, N. Cesa-Bianchi, and R. Garnett (eds.), *Advances in Neural Information Processing Systems 31*, pp. 5405–5414. Curran Associates, Inc., 2018.
- <span id="page-9-8"></span>Sergey Ioffe and Christian Szegedy. Batch normalization: Accelerating deep network training by reducing internal covariate shift. *arXiv preprint arXiv:1502.03167*, 2015.
- <span id="page-9-5"></span>Diederik P Kingma and Jimmy Ba. Adam: A method for stochastic optimization. *arXiv preprint arXiv:1412.6980*, 2014.
- <span id="page-9-0"></span>Ksenia Konyushkova, Raphael Sznitman, and Pascal Fua. Learning active learning from data. In *NIPS*, 2017.
- <span id="page-9-9"></span>Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. Technical report, Citeseer, 2009.
- <span id="page-9-3"></span>Chenxi Liu, Barret Zoph, Maxim Neumann, Jonathon Shlens, Wei Hua, Li-Jia Li, Li Fei-Fei, Alan Yuille, Jonathan Huang, and Kevin Murphy. Progressive neural architecture search. In *Proceedings of the European Conference on Computer Vision (ECCV)*, pp. 19–34, 2018a.
- <span id="page-9-15"></span>Hanxiao Liu, Karen Simonyan, and Yiming Yang. Darts: Differentiable architecture search. *arXiv preprint arXiv:1806.09055*, 2018b.
- <span id="page-9-2"></span>Renqian Luo, Fei Tian, Tao Qin, Enhong Chen, and Tie-Yan Liu. Neural architecture optimization. In *Advances in neural information processing systems*, pp. 7816–7827, 2018.
- <span id="page-9-13"></span>Andrew L Maas, Awni Y Hannun, and Andrew Y Ng. Rectifier nonlinearities improve neural network acoustic models. In *Proc. icml*, volume 30, pp. 3, 2013.
- <span id="page-9-1"></span>Dougal Maclaurin, David Duvenaud, and Ryan P. Adams. Gradient-based hyperparameter optimization through reversible learning. In *Proceedings of the 32Nd International Conference on International Conference on Machine Learning - Volume 37*, ICML'15, pp. 2113–2122. JMLR.org, 2015.
- <span id="page-9-11"></span>Luke Metz, Niru Maheswaranathan, Brian Cheung, and Jascha Sohl-Dickstein. Meta-learning update rules for unsupervised representation learning. *arXiv preprint arXiv:1804.00222*, 2018.
- <span id="page-9-7"></span>Luke Metz, Niru Maheswaranathan, Jeremy Nixon, Daniel Freeman, and Jascha Sohl-dickstein. Learned optimizers that outperform on wall-clock and validation loss, 2019.
- <span id="page-9-17"></span>Volodymyr Mnih, Adria Puigdomenech Badia, Mehdi Mirza, Alex Graves, Timothy Lillicrap, Tim Harley, David Silver, and Koray Kavukcuoglu. Asynchronous methods for deep reinforcement learning. In *International conference on machine learning*, pp. 1928–1937, 2016.
- <span id="page-9-10"></span>Anh Nguyen, Jason Yosinski, and Jeff Clune. Deep neural networks are easily fooled: High confidence predictions for unrecognizable images. In *In Computer Vision and Pattern Recognition (CVPR '15)*, 2015.
- <span id="page-9-4"></span>Hieu Pham, Melody Guan, Barret Zoph, Quoc Le, and Jeff Dean. Efficient neural architecture search via parameters sharing. In Jennifer Dy and Andreas Krause (eds.), *Proceedings of the 35th International Conference on Machine Learning*, volume 80 of *Proceedings of Machine Learning Research*, pp. 4095–4104, Stockholmsmssan, Stockholm Sweden, 10–15 Jul 2018. PMLR.
- <span id="page-9-14"></span>Esteban Real, Alok Aggarwal, Yanping Huang, and Quoc V Le. Regularized evolution for image classifier architecture search. In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 33, pp. 4780–4789, 2019.

- <span id="page-10-6"></span>Tim Salimans and Durk P Kingma. Weight normalization: A simple reparameterization to accelerate training of deep neural networks. In *Advances in Neural Information Processing Systems*, pp. 901–909, 2016.
- <span id="page-10-11"></span>Tim Salimans, Ian J. Goodfellow, Wojciech Zaremba, Vicki Cheung, Alec Radford, and Xi Chen. Improved techniques for training gans. In *NIPS*, 2016.
- <span id="page-10-5"></span>Tim Salimans, Jonathan Ho, Xi Chen, Szymon Sidor, and Ilya Sutskever. Evolution strategies as a scalable alternative to reinforcement learning. *arXiv preprint arXiv:1703.03864*, 2017.
- <span id="page-10-1"></span>Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *International Conference on Learning Representations*, 2018.
- <span id="page-10-0"></span>Burr Settles. Active learning literature survey. Technical report, 2010.
- <span id="page-10-12"></span>Hava T Siegelmann and Eduardo D Sontag. On the computational power of neural nets. *Journal of computer and system sciences*, 50(1):132–150, 1995.
- <span id="page-10-10"></span>Akash Srivastava, Lazar Valkov, Chris Russell, Michael U. Gutmann, and Charles Sutton. Veegan: Reducing mode collapse in gans using implicit variational learning. In I. Guyon, U. V. Luxburg, S. Bengio, H. Wallach, R. Fergus, S. Vishwanathan, and R. Garnett (eds.), *Advances in Neural Information Processing Systems 30*, pp. 3308–3318. Curran Associates, Inc., 2017.
- <span id="page-10-8"></span>Kenneth O. Stanley, Joel Lehman, and Lisa Soros. Open-endedness: The last grand challenge youve never heard of. *O'Reilly Online*, 2017. URL [https://www.oreilly.com/ideas/](https://www.oreilly.com/ideas/ open-endedness-the-last-grand-challenge-youve-never-heard-of) [open-endedness-the-last-grand-challenge-youve-never-heard-of](https://www.oreilly.com/ideas/ open-endedness-the-last-grand-challenge-youve-never-heard-of).
- <span id="page-10-14"></span>Christian Szegedy, Wojciech Zaremba, Ilya Sutskever, Joan Bruna, Dumitru Erhan, Ian Goodfellow, and Rob Fergus. Intriguing properties of neural networks. *arXiv preprint arXiv:1312.6199*, 2013.
- <span id="page-10-13"></span>Josh Tobin, Rachel Fong, Alex Ray, Jonas Schneider, Wojciech Zaremba, and Pieter Abbeel. Domain randomization for transferring deep neural networks from simulation to the real world. In *2017 IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS)*, pp. 23–30. IEEE, 2017.
- <span id="page-10-15"></span>Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018.
- <span id="page-10-2"></span>Ivor Tsang, James Kwok, and Pak-Ming Cheung. Core vector machines: Fast svm training on very large data sets. *Journal of Machine Learning Research*, 6:363–392, 04 2005.
- <span id="page-10-7"></span>Rui Wang, Joel Lehman, Jeff Clune, and Kenneth O Stanley. Paired open-ended trailblazer (poet): Endlessly generating increasingly complex and diverse learning environments and their solutions. *arXiv preprint arXiv:1901.01753*, 2019a.
- <span id="page-10-3"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation, 2019b.
- <span id="page-10-4"></span>Chris Zhang, Mengye Ren, and Raquel Urtasun. Graph hypernetworks for neural architecture search. *arXiv preprint arXiv:1810.05749*, 2018.
- <span id="page-10-9"></span>Barret Zoph and Quoc V. Le. Neural architecture search with reinforcement learning. 2017. URL <https://arxiv.org/abs/1611.01578>.

<span id="page-11-1"></span>

# APPENDIX A ADDITIONAL EXPERIMENTAL DETAILS

The outer loop loss function is domain specific. In the supervised experiments on MNIST and CIFAR, the outer loop loss was cross-entropy for logistic regression on real MNIST or CIFAR data. The inner-loop loss matches the outer-loop loss, but with synthetic data instead of real data. Appendix [H](#page-15-0) describes the losses for the RL experiments.

The following equation defines the inner-loop SGD with momentum update for the learner parameters  $\theta_t$ .

<span id="page-11-0"></span>
$$
\theta_{t+1} = \theta_t - \alpha \sum_{0 \le t' \le t} \beta^{t-t'} \nabla \ell_{\text{inner}}(G(\mathbf{z}_{t'}, \mathbf{y}_{t'}), \mathbf{y}_{t'}, \theta_{t'}),
$$
\n(1)

where  $\alpha$  and  $\beta$  are the learning rate and momentum hyperparameters, respectively.  $z_t$  is a batch of noise vectors that are input to the generator and are sampled from a unit-variance Gaussian.  $y_t$  are a batch of labels for each generated sample/input and are sampled uniformly from all available class labels. Instead of randomly sampling  $z_t$ , we can also learn a curriculum by additionally optimizing  $z_t$ directly and keeping  $y_t$  fixed throughout all of training. Results for both approaches (and additional curriculum ablations) are reported in Section [3.2.](#page-4-1)

#### A.1 MNIST EXPERIMENTS:

For the GTN training for MNIST we sampled architectures from a distribution that produces architectures with convolutional (conv) and fully-connectd (FC) layers. All architectures had 2 conv layers, but the number of filters for each layer was sampled uniformly from the ranges  $U([32, 128])$ and  $U([64, 256])$ , respectively. After each conv layer there is a max pooling layer for dimensionality reduction. After the last conv layer, there is a fully-connected layer with number of filters sampled uniformly from the range  $U([64, 256])$ . We used Kaiming Normal initialization [\(He et al., 2015\)](#page-9-12) and LeakyReLUs [\(Maas et al., 2013\)](#page-9-13) (with  $\alpha = 0.1$ ). We use BatchNorm [\(Ioffe & Szegedy, 2015\)](#page-9-8) for both the generator and the learners. The BatchNorm momentum for the learner was set to 0 (meta-training consistently converged to small values and we saw no significant gain from learning the value).

<span id="page-11-3"></span>The generator consisted of 2 FC layers (1024 and  $128 * H/4 * H/4$  filters, respectively, where H is the final width of the synthetic image). After the last FC layer there are 2 conv layers. The first conv has 64 filters. The second conv has 1 filter followed by a Tanh. We found it particularly important to normalize (mean of zero and variance of one) all datasets. Hyperparameters are shown in Table [2.](#page-11-3)

| Hyperparameter               | Value |
|------------------------------|-------|
| <b>Learning Rate</b>         | 0.01  |
| <b>Initial LR</b>            | 0.02  |
| Initial Momentum             | 0.5   |
| Adam Beta_1                  | 0.9   |
| Adam Beta_2                  | 0.999 |
| Size of latent variable      | 128   |
| Inner-Loop Batch Size        | 128   |
| <b>Outer-Loop Batch Size</b> | 128   |

Table 2: Hyperparameters for MNIST experiments

### <span id="page-11-2"></span>A.2 CIFAR10 EXPERIMENTS:

For GTN training for CIFAR-10, the template architecture is a small learner with 5 convolutional layers followed by a global average pooling and an FC layer. The second and fourth convolution had stride=2 for dimensionality reduction. The number of filters of the first conv layer was sampled uniformly from the range  $U([32, 128])$  while all others were sampled uniformly from the range  $U([64, 256])$ . Other details including the generator architecture were the same as the MNIST experiments, except the CIFAR generator's second conv layer had 3 filters instead of 1. Hyperparameters

<span id="page-12-3"></span>used can be found in Table [3.](#page-12-3) For CIFAR10 we augmented the real training set when training GTNs with random crops and horizontal flips. We do not add weight normalization to the final architectures found during architecture search, but we do so when we train architectures with GTN-generated data during architecture search to provide an estimate of their asymptotic performance.

| Hyperparameter               | Value  |
|------------------------------|--------|
| <b>Learning Rate</b>         | 0.002  |
| Initial LR                   | 0.02   |
| Initial Momentum             | 0.5    |
| Adam Beta_1                  | 0.9    |
| Adam Beta_2                  | 0.9    |
| Adam $\epsilon$              | $1e-5$ |
| Size of latent variable      | 128    |
| <b>Inner-loop Batch Size</b> | 128    |
| <b>Outer-loop Batch Size</b> | 256    |

Table 3: Hyperparameters for CIFAR10 experiments

<span id="page-12-2"></span>

# APPENDIX B REASONS GTNS ARE NOT EXPECTED TO PRODUCE SOTA ACCURACY VS. ASYMPTOTIC PERFORMANCE WHEN TRAINING ON REAL DATA

There are three reasons not to expect SOTA accuracy levels for the learners trained on synthetic data: (1) we train for very few SGD steps (32 or 128 vs. tens of thousands), (2) SOTA performance results from architectures explicitly designed (with much human effort) to achieve record accuracy, whereas GTN produces compressed training data optimized to generalize across diverse architectures with the aim of quickly evaluating a new architecture's potential, and (3) SOTA methods often use data outside of the benchmark dataset and complex data-augmentation schemes.

<span id="page-12-1"></span>

# APPENDIX C CELL SEARCH SPACE

When searching for the operations in a CNN cell, the 11 possible operations are listed below.

- identity
- $\bullet$  1  $\times$  1 convolution
- $3 \times 3$  convolution
- $1 \times 3 + 3 \times 1$  convolution
- $1 \times 7 + 7 \times 1$  convolution
- $2 \times 2$  max pooling
- $3 \times 3$  max pooling
- $5 \times 5$  max pooling
- $2 \times 2$  average pooling
- $3 \times 3$  average pooling
- $5 \times 5$  average pooling

<span id="page-12-0"></span>

# APPENDIX D COMPUTATION AND MEMORY COMPLEXITY

With the traditional training of DNNs with back-propagation, the memory requirements are proportional to the size of the network because activations during the forward propagation have to be stored for the backward propagation step. With meta-gradients, the memory requirement also grows with the number of inner-loop steps because all activations and weights have to be stored for the 2nd order gradient to be computed. This becomes impractical for large networks and/or many inner-loop steps. To reduce the memory requirements, we utilize gradient-checkpointing [\(Griewank &](#page-8-6) [Walther, 2000\)](#page-8-6) by only storing the computed weights of learner after each inner-loop step and recomputing the activations during the backward pass. This trick allows us to compute meta-gradients for networks with 10s of millions of parameters over hundreds of inner-loop steps in a single GPU. While in theory the computational cost of computing meta-gradients with gradient-checkpointing is 4x larger than computing gradients (and 12x larger than forward propagation), in our experiments it is about 2.5x slower than gradients through backpropagation due to parallelism. We could further reduce the memory requirements by utilizing reversable hypergradients [\(Maclaurin et al., 2015\)](#page-9-1), but, in our case, we were not constrained by the number of inner-loop steps we can store in memory.

<span id="page-13-1"></span>

# APPENDIX E EXTENDED NAS RESULTS

In the limited computation regime (less than 1 day of computation), the best methods were, in order, GHN, ENAS, GTN, and NAONet with a mean error of 2.84%, 2.89%, 2.92%, and 2.93%, respectively. A 0.08% difference on CIFAR10 represents 8 out of the 10k test samples. For that reason, we consider all of these methods as state of the art. Note that out of the four, GTN is the only one relying on Random Search for architecture proposal.

Table 4: Performance of different architecture search methods. Search with our method required 16h total, of which 8h were spent training the GTN and 8h were spent evaluating 800 architectures with GTN-produced synthetic data. Our results report mean  $\pm$  SD of 5 evaluations of the same architecture with different initializations. It is common to report scores with and without Cutout [\(DeVries](#page-8-9) [& Taylor, 2017\)](#page-8-9), a data augmentation technique used during training.We found better architectures compared to other methods using random search (Random-WS and GHN-Top) and are competitive with algorithms that benefit from more advanced search methods (e.g. NAONet and ENAS employ non-random architecture proposals for performance gains; GTNs could be combined with such nonrandom proposals, which would likely further improve performance). Increasing the width of the architecture found (F=128) further improves performance.

| Model                                      | Error(%)           | #params | Random | GPU Days |
|--------------------------------------------|--------------------|---------|--------|----------|
| NASNet-A (Zoph & Le, 2017)                 | 3.41               | 3.3M    | Х      | 2000     |
| AmoebaNet-B + Cutout (Real et al., 2019)   | 2.13               | 34.9M   | Х      | 3150     |
| DARTS + Cutout (Liu et al., 2018b)         | 2.83               | 4.6M    | Х      | 4        |
| NAONet + Cutout (Luo et al., 2018)         | 2.48               | 10.6M   | Х      | 200      |
| NAONet-WS (Luo et al., 2018)               | 3.53               | 2.5M    | Х      | 0.3      |
| NAONet-WS + Cutout (Luo et al., 2018)      | 2.93               | 2.5M    | Х      | 0.3      |
| ENAS (Pham et al., 2018)                   | 3.54               | 4.6M    | Х      | 0.45     |
| ENAS + Cutout (Pham et al., 2018)          | 2.89               | 4.6M    | Х      | 0.45     |
| GHN Top-Best + Cutout (Zhang et al., 2018) | 2.84 $	extpm$ 0.07 | 5.7M    | Х      | 0.84     |
| GHN Top (Zhang et al., 2018)               | 4.3 $	extpm$ 0.1   | 5.1M    | ✓      | 0.42     |
| Random-WS (Luo et al., 2018)               | 3.92               | 3.9M    | ✓      | 0.25     |
| Random Search + Real Data (baseline)       | 3.88 $	extpm$ 0.08 | 12.4M   | ✓      | 10       |
| RS + Real Data + Cutout (baseline)         | 3.02 $	extpm$ 0.03 | 12.4M   | ✓      | 10       |
| RS + Real Data + Cutout (F=128) (baseline) | 2.51 $	extpm$ 0.13 | 151.7M  | ✓      | 10       |
| Random Search + GTN (ours)                 | 3.84 $	extpm$ 0.06 | 8.2M    | ✓      | 0.67     |
| Random Search + GTN + Cutout (ours)        | 2.92 $	extpm$ 0.06 | 8.2M    | ✓      | 0.67     |
| RS + GTN + Cutout (F=128) (ours)           | 2.42 $	extpm$ 0.03 | 97.9M   | ✓      | 0.67     |

<span id="page-13-0"></span>

# APPENDIX F CONDITIONED GENERATOR VS. XY-GENERATOR

Our experiments in the main paper conditioned the generator to create data with given labels, by concatenating a one-hot encoded label to the input vector. We also explored an alternative approach where the generator itself produced a target probability distribution to label the data it generates. Because more information is encoded into a soft label than a one-hot encoded one, we expected an improved training set to be generated by this variant. Indeed, such a "dark knowledge" distillation setup has been shown to perform better than learning from labels [\(Hinton et al., 2015\)](#page-9-16).

<span id="page-14-1"></span>However, the results in Figure [4](#page-14-1) indicate that jointly generating both images and their soft labels under-performs generating only images, although the result could change with different hyperparameter values and/or innovations that improve the stability of training.

Image /page/14/Figure/1 description: This is a line graph showing the validation accuracy over outer-loop steps. The x-axis represents outer-loop steps, ranging from 0 to 3500. The y-axis represents validation accuracy, ranging from 0.800 to 1.000. There are three lines plotted: 'Real Data' in red, 'GTN' in blue, and 'DK' in green. All three lines show an increasing trend in validation accuracy as the outer-loop steps increase. The 'GTN' line generally shows the highest accuracy, followed by 'Real Data', and then 'DK'. Shaded regions around each line indicate variability or confidence intervals.

Figure 4: Comparison between a conditional generator and a generator that outputs an image/label pair. We expected the latter "dark knowledge" approach to outperform the conditional generator, but that does not seem to be the case. Because initialization and training of the dark knowledge variant were more sensitive, we believe a more rigorous tuning of the process could lead to a different result.

<span id="page-14-0"></span>

# APPENDIX G GTN GENERATES (SEEMINGLY) ENDLESS DATA

While optimizing images directly (i.e. optimizing a fixed tensor of images) would result in a fixed number of samples, optimizing a generator can potentially result in an unlimited amount of new samples. We tested this generative capability by generating more data during evaluation (i.e. with no change to the meta-optimization procedure) in two ways. In the first experiment, we increase the amount of data in each inner-loop optimization step by increasing the batch size (which results in lower variance gradients). In the second experiment, we keep the number of samples per batch fixed, but increase the number of inner-loop optimization steps for which a new network is trained. Both cases result in an increased amount of training data. If the GTN generator has overfit to the number of inner-loop optimization steps during meta-training and/or the batch size, then we would not expect performance to improve when we have the generator produce more data. However, an alternate hypothesis is that the GTN is producing a healthy distribution of training data, irrespective of exactly how it is being used. Such a hypothesis would be supported by performance increase in these experiments.

Figure [5a](#page-15-1) shows performance as a function of increasing batch size (beyond the batch size used during meta-optimization, i.e. 128). The increase in performance of GTN means that we can sample larger training sets from our generator (with diminishing returns) and that we are not limited by the choice of batch size during training (which is constrained due to both memory and computation requirements).

Figure [5b](#page-15-1) shows the results of generating more data by increasing the number of inner-loop optimization steps. Generalization to more inner-loop optimization steps is important when the number of inner-loop optimization steps used during meta-optimization is not enough to achieve maximum performance. This experiment also tests the generalization of the optimizer hyperparameters because they were optimized to maximize learner performance after a fixed number of steps. There is an increase in performance of the learner trained on GTN-generated data as the number of innerloop optimization steps is increased, demonstrating that the GTN is producing generally useful data instead of overfitting to the number of inner-loop optimization steps during training (Figure [5b\)](#page-15-1). Extending the conclusion from Figure [2b,](#page-5-0) in the very low data regime, GTN is significantly better than training on real data ( $p < 0.05$ ). However, as more inner-loop optimization steps are taken and thus more unique data is available to the learner, training on the real data becomes more effective than learning from synthetic data ( $p < 0.05$ ) (see Figure [5b\)](#page-15-1).

<span id="page-15-1"></span>Image /page/15/Figure/0 description: The image contains two plots. The plot on the left shows validation accuracy versus inner loop batch size, with batch sizes ranging from 150 to 500. The plot on the right shows validation accuracy versus inner-loop steps, with steps ranging from 20 to 120. Both plots display two lines: one for 'Real Data' (red) and one for 'GTN' (blue), with shaded areas indicating confidence intervals. In the left plot, the GTN line starts at approximately 0.938 and increases to about 0.952, while the Real Data line starts at approximately 0.916 and increases to about 0.948. In the right plot, the Real Data line starts at approximately 0.918 and increases to about 0.972, while the GTN line starts at approximately 0.938 and increases to about 0.954.

(a) Increasing inner-loop batch size

(b) Increasing inner-loop optimization steps

<span id="page-15-2"></span>Figure 5: (a) The left figure shows that even though GTN was meta-trained to generate synthetic data of batch size 128, sampling increasingly larger batches results in improved learner performance (the inner-loop optimization steps are fixed to 16). (b) The right figure shows that increasing the number of inner-loop optimization steps (beyond the 16 steps used during meta-training) improves learner performance. The performance gain with real data is larger in this setting. This improvement shows that GTNs do not overfit to a specific number of inner-loop optimization steps.

Image /page/15/Picture/4 description: A grid of blurry, black and white images of handwritten digits. The digits are arranged in rows and columns, with each digit appearing to be a variation of a number from 0 to 9. The overall impression is a mosaic of distorted numerical characters.

Figure 6: GTN samples w/o curriculum.

Another interesting test for our generative model is to test the distribution of learners after they have trained on the synthetic data. We want to know, for instance, if training on synthetic samples from one GTN results in a functionally similar set of learner weights regardless of learner initialization (this phenomena can be called learner mode collapse). Learner mode collapse would prevent the performance gains that can be achieved through ensembling diverse learners. We tested for learner mode collapse by evaluating the performance (on held-out data and held-out architecture) of an ensemble of 32 randomly initialized learners that are trained on independent batches from the *same GTN*. To construct the ensemble, we average the predicted probability distributions across the learners to compute a combined prediction and accuracy. The results of this experiment can be seen in Figure [7,](#page-16-1) which shows that the combined performance of an ensemble is better (on average) than an individual learner, providing additional evidence that the distribution of synthetic data is healthy and allows ensembles to be harnessed to improve performance, as is standard with networks trained on real data.

<span id="page-15-0"></span>

# APPENDIX H GTN FOR RL

To demonstrate the potential of GTNs for RL, we tested our approach with a small experiment on the classic CartPole test problem (see [Brockman et al.](#page-8-11) [\(2016\)](#page-8-11) for details on the domain. We conducted this experiment before the discovery that weight normalization improves GTN training, so these experiments do not feature it; it might further improve performance. For this experiment, the meta-objective the GTN is trained with is the advantage actor-critic formulation:  $\log \pi (a|\theta_\pi)(R V(s; \theta_{\eta})$ ) [\(Mnih et al., 2016\)](#page-9-17). The state-value V is provided by a separate neural network trained to estimate the average state-value for the learners produced so far during meta-training. The learners train on synthetic data via a single-step of SGD with a batch size of 512 and a mean squared error

<span id="page-16-1"></span>Image /page/16/Figure/0 description: This is a line graph showing the validation accuracy over outer-loop steps. The x-axis represents outer-loop steps, ranging from 0 to 3500. The y-axis represents validation accuracy, ranging from 0.86 to 1.00. There are four lines plotted: 'Real Data - single' (red), 'Real Data - ensemble' (blue), 'GTN - single' (green), and 'GTN - ensemble' (purple). The 'Real Data - ensemble' line stays consistently around 0.95. The 'Real Data - single' line starts around 0.90 and fluctuates slightly around 0.91. The 'GTN - single' line starts around 0.85 and increases steadily to about 0.94. The 'GTN - ensemble' line starts around 0.89 and increases steadily to about 0.97. Shaded areas around each line indicate variability.

Figure 7: Performance of an ensemble of GTN learners vs. individual GTN learners. Ensembling a set of neural networks that each had different weight initializations, but were trained on data from the *same GTN* substantially improves performance. This result provides more evidence that GTNs generate a healthy distribution of training data and are not somehow forcing the learners to all learn a functionally equivalent solution.

regression loss, meaning the inner loop is supervised learning. The outer-loop is reinforced because the simulator is non-differentiable. We could have also used an RL algorithm in the inner loop. In that scenario the GTN would have to learn to produce an entire synthetic world an RL agent would learn in. Thus, it would create the initial state and then iteratively receive actions and generate the next state and optionally a reward. For example, a GTN could learn to produce an entire MDP that an agent trains on, with the meta-objective being that the trained agent then performs well on a target task. We consider such synthetic (PO)MDPs an exciting direction for future research.

The score on CartPole is the number of frames out of 200 for which the pole is elevated. Both GTN and an A2C [\(Mnih et al., 2016\)](#page-9-17) control effectively solve the problem (Figure [8\)](#page-17-0). Interestingly, training GTNs takes the same number of simulator steps as training a single learner with policygradients (Figure [8\)](#page-17-0). Incredibly, however, once trained, the synthetic data from a GTN can be used to train a learner to maximum performance in a single SGD step! While that is unlikely to be true for harder target RL tasks, these results suggest that the speed-up for architecture search from using GTNs in the RL domain can be even greater than in supervised domain.

The CartPole experiments feature a single-layer neural network with 64 hidden units and a tanh activation function for both the policy and the value network. The inner-loop batch size was 512 and the number of inner-loop training iterations was 1. The observation space of this environment consists of a real-valued vector of size 4 (Cart position, Cart velocity, Pole position, Pole velocity). The action space consists of 2 discrete actions (move left or move right). The outer loop loss is the reward function for the target domain (here, pole-balancing). The inner loop loss is mean squared error (i.e. the network is doing supervised learning on the state-action mapping pairs provided by the GTN).

<span id="page-16-0"></span>

# APPENDIX I SOLVING MODE COLLAPSE IN GANS WITH GTNS

We created an implementation of generative adversarial networks (GANs) [\(Goodfellow et al., 2014\)](#page-8-2) and found they tend to generate the same class of images (e.g. only 1s, Figure [9\)](#page-17-1), which is a common training pathology in GANs known as mode collapse [\(Srivastava et al., 2017\)](#page-10-10). While there are techniques to prevent mode collapse (e.g. minibatch discrimination and historical averaging [\(Salimans](#page-10-11) [et al., 2016\)](#page-10-11)), we hypothesized that combining the ideas behind GTNs and GANs might provide a different, additional technique to help combat mode collapse. The idea is to add a discriminator to the GTN forcing the data it generates to both be realistic and help a learner perform well on the meta-objective of classifying MNIST. The reason this approach should help prevent mode collapse is that if the generator only produces one class of images, a learner trained on that data will not be able to classify all classes of images. This algorithm (GTN-GAN) was able to produce realistic images with no identifiable mode collapse (Figure [10\)](#page-18-0). GTNs offer a different type of solution to the issue of mode collapse than the many that have been proposed, adding a new tool to our toolbox

<span id="page-17-0"></span>Image /page/17/Figure/0 description: The image is a line graph showing the performance of two agents, A2C Agent and A2C + GTN Agent, over 100,000 environment steps. The y-axis represents the reward, ranging from 0 to 200, and the x-axis represents the environment steps, from 0 to 100,000. The A2C Agent is represented by a red line, and the A2C + GTN Agent is represented by a blue line. Both agents show an increasing trend in reward as the environment steps increase. Initially, the A2C Agent performs slightly better, but the A2C + GTN Agent catches up and surpasses the A2C Agent around 40,000 steps. By 100,000 steps, both agents achieve a reward of approximately 190. The shaded areas around the lines indicate the variance or confidence interval for each agent's performance.

Figure 8: An A2C Agent control trains a single policy throughout all of training, while the GTN method starts with a new, randomly initialized network *at each iteration* and produces the plotted performance *after a single step of SGD*. This plot is difficult to parse because of that difference: it compares the accumulated performance of A2C across all environment steps up to that point vs. the performance achieved with GTN data *in a single step of SGD from a single batch of synthetic data*. Thus, at the 100,000<sup>th</sup> step of training, GTNs enable training a newly initialized network to the given performance (of around 190) 100,000 times faster with GTN synthetic data than with A2C from scratch. With GTNs, we can therefore train many new, high-performing agents quickly. That would be useful in many ways, such as greatly accelerating architecture search algorithms for RL. Of course, these results are on a simple problem, and (unlike our supervised learning experiments) have not yet shown that the GTN data works with different architectures, but these results demonstrate the intriguing potential of GTNs for RL. One reason we might expect even larger speedups for RL vs. supervised learning is because a major reason RL is sample inefficient is because it requires exploration to figure out how to solve the problem. However, once that exploration has been done, the GTN can produce data to efficiently teach that solution to a new architecture. RL thus represents an exciting area of future research for GTNs. Performing that research is beyond the scope of this paper, but we highlight the intriguing potential here to inspire such future work.

<span id="page-17-1"></span>for solving that problem. Note we do not claim this approach is better than other techniques to prevent mode collapse, only that it is an interesting new type of option, perhaps one that could be productively combined with other techniques.

| 60107333 | 85110316 | 45434237 | 86174515 | 41089482 | 70150759 | 76609017 | 72289607 |
|----------|----------|----------|----------|----------|----------|----------|----------|
| 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 |
| 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 |
| 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 |
| 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 |
| 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 |
| 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 |
| 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 | 11111111 |

Figure 9: Images generated by a basic GAN on MNIST before and after mode collapse. The left image shows GAN-produced images early in GAN training and the right image shows GAN samples later in training after mode collapse has occurred due to training instabilities.

# APPENDIX J ADDITIONAL MOTIVATION

There is an additional motivation for GTNs that involves long-term, ambitious research goals: GTN is a step towards algorithms that generate their own training environments, such that agents trained in them eventually solve tasks we otherwise do not know how to train agents to solve [\(Clune, 2019\)](#page-8-5). It is important to pursue such algorithms because our capacity to conceive of effective training environments on our own as humans is limited, yet for our learning algorithms to achieve their full potential they will ultimately need to consume vast and complex curricula of learning challenges

<span id="page-18-0"></span>Image /page/18/Picture/0 description: The image displays two grids of handwritten digits on a black background. The left grid contains 8 rows and 8 columns of digits, while the right grid also contains 8 rows and 8 columns of digits. The digits appear to be from the MNIST dataset, a collection of handwritten digits.

Figure 10: Images generated by a GTN with an auxiliary GAN loss. Combining GTNs with GANs produces far more realistic images than GTNs alone (which produced alien, unrecognizable images, Figure [6\)](#page-15-2). The combination also stabilizes GAN training, preventing mode collapse.

and data. Algorithms for generating curricula, such as the the paired open-ended trailblazer (POET) algorithm [\(Wang et al., 2019a\)](#page-10-7), have proven effective for achieving behaviors that would otherwise be out of reach, but no algorithm yet can generate completely unconstrained training conditions. For example, POET searches for training environments within a highly restricted preconceived space of problems. GTNs are exciting because they can encode a rich set of possible environments with minimal assumptions, ranging from labeled data for supervised learning to (in theory) entire complex virtual RL domains (with their own learned internal physics). Because RNNs are Turing-complete [\(Siegelmann & Sontag, 1995\)](#page-10-12), GTNs should be able to theoretically encode all possible learning environments. Of course, while what is theoretically possible is different from what is achievable in practice, GTNs give us an expressive environmental encoding to begin exploring what potential is unlocked when we can learn to generate sophisticated learning environments. The initial results presented here show that GTNs can be trained end-to-end with gradient descent through the entire learning process; such end-to-end learning has proven highly scalable before, and may similarly in the future enable learning expressive GTNs that encode complex learning environments.

# APPENDIX K ON THE REALISM OF IMAGES

There are two phenomenon related to the recognizability of the GTN-generated images that are interesting. (1) Many of the images generated by GTNs are unrecognizable (e.g. as digits), yet a network trained on them still performs well on a real, target task (e.g. MNIST). (2) Some conditions increase the realism (recognizability) of the images. We will focus on the MNIST experiments because that is where we have conducted experiments to investigate this phenomenon.

Figure [12](#page-22-0) shows all of the images generated by a GTN with a curriculum. Most of the images do not resemble real MNIST digits, and many are alien and unrecognizable. Interestingly, there is a qualitative change in the recognizability of the images at the very end of the curriculum (the last 4-5 rows, which show the last two training batches). Both phenomena are interesting, and we do not have satisfactory explanations for either. Here we present many hypothesis we have generated that could explain these phenomenon. We also present a few experiments we have done to shed light on these issues. A more detailed investigation is an interesting area for future research.

Importantly, the recognizable images at the end of the curriculum are not *required* to obtain high performance on MNIST. The evidence for that fact is in Figure [2,](#page-5-0) which shows that the performance of a learner trained on GTN-data is already high after around 23 inner-loop iterations, before the network has seen the recognizable images in the last 4-5 rows (which are shown in the last two training batches, i.e. training iterations 31 and 32). Thus, a network can learn to get over 98% accuracy on MNIST training only on unrecognizable images.

At a high level, there are three possible camps of explanation for these phenomenon.

Camp 1. Performance would be higher with *higher* realism, but optimization difficulties (e.g. vanishing/exploding gradients) prevent learning a generator that produces such higherperforming, more realistic images. Evidence in favor of this camp of hypotheses is that the realistic images come at the end of the curriculum, where the gradient flow is easiest (as gradients do not have to flow back through multiple inner-loop steps of learning). A prediction of this hypothesis is that as we improve our ability to train GTNs, the images will become more realistic.

Camp 2. Performance is higher with lower realism (at least when not late in the curriculum), which is why unrealistic images are produced. There are at least two reasons why unrealistic images could generate higher performance. (A) Compression enables *faster* learning (i.e. learning with fewer samples). Being able to produce unrealistic images allows much more information to be packed into a single training example. For example, imagine a single image that could teach a network about many different styles of the digit 7 all at the same time (and/or different translations, rotations, and scales of a 7). It is well known that data augmentation improves performance because it teaches a network, for example, that the same image at different locations in the image is of the same class. It is conceivable that a single image could do something similar by showing multiple 7s at different locations. (B) Unrealistic images allow better *generalization*. When trying to produce high performance with very few samples, the risk of performance loss due to overfitting is high. A small set of realistic images may not have enough variation in non-essential aspects of the image (e.g. the background color) that allow a network to reliably learn the class of interest in a way that will generalize to instances of that class not in the training set (e.g. images of that class with a background color not in the training set). With the ability to produce unrealistic images (e.g. 7s against many different artificial backdrops, such as by adding seemingly random noise to the background color), GTNs could prevent the network from overfitting to spurious correlations in the training set (e.g. background color). In other words, GTNs could *learn* to produce something similar to domain randomization [\(Tobin et al., 2017;](#page-10-13) [Andrychowicz et al., 2018\)](#page-8-12) to improve generalization, an exciting prospect.

Camp 3. It makes no difference on performance whether the images are realistic, but there are more unrealistic images that are effective than realistic ones, explaining why they tend to be produced. This hypothesis is in line with the fact that deep neural networks are easily fooled [\(Nguyen et al., 2015\)](#page-9-10) and susceptible to adversarial examples [\(Szegedy et al., 2013\)](#page-10-14). The idea is that images that are unrecognizeable to us are surprisingly meaningful to (i.e. impactful on) DNNs. This hypothesis is also in line with the fact that images can be generated that hack a trained DNN to cause it to perform other functions it was not trained to do (e.g. to perform a different function entirely, such as hacking an ImageNet classification network to perfom a counting task like counting the number of occurences of Zebras in an image) [\(Elsayed et al., 2018\)](#page-8-13). This hypothesis is also in line with recent research into meta-learning, showing that an initial weight vector can be carefully chosen such that it will produce a desired outcome (including implementing any learning algorithm) once subjected to data and SGD [\(Finn et al., 2017;](#page-8-10) [Finn & Levine, 2017\)](#page-8-14). One thing not explained by this hypothesis is why images at the end of the curriculum are more recognizable.

Within this third camp of hypotheses is the possibility that the key features required to recognize a type of image (e.g. a 7) could be broken up across images. For example, one image could teach a network about the bottom half of a 7 and another about the top half. Recognizing either on its own is evidence for a seven, and if across a batch or training dataset the network learned to associate both features with the class 7, there is no reason that both the top half and bottom half ever have to co-occur. That could lead to unrealistic images with partial features. One prediction of this hypothesis (although one not exclusive to this hypothesis), is that averaging all of the images for each class across the entire GTN-produced training set should reveal recognizable digits. The idea is that no individual image contains a full seven, but on average the images combine to produce sevens (and the other digits). Figure [11](#page-21-0) shows the results of this experiment. On average the digits are recognizable. This result is also consistent with Camp 1 of hypotheses: perhaps performance would increase further if the images were individually more recognizable. It is also consistent with Camp 2: perhaps the network is forced to combine many sevens into each image, making them individually unrecognizeable, but recognizable as 7s on average. Additionally, in line with Camp 2, if the network has learned to produce something like domain randomization, it could add variation across the dataset in the background (making each individual image less recognizable), but Hypothesis 2 would predict that, on average, the aspects of the image that do not matter (e.g. the background) average out to a neutral value or the true dataset mean (for MNIST, black), whereas the true class information (e.g. the digit itself) would be recognizable on average, exactly as we see in Figure [11.](#page-21-0) Thus, the average images shed light on the overall subject, but do not provide conclusive results regarding which camp of hypotheses is correct.

An additional experiment we performed was to see if the alien images somehow represent the edges of the decision boundaries between images. The hypothesis is that images in the center of a cluster (e.g. a Platonic, archetypal 7) are not that helpful to establish neural network decision boundaries

between classes, and thus GTN does not need to produce many of them. Instead, it might benefit by generating mostly edge cases to establish the decision boundaries, which is why the digits are mostly difficult to recognize. To rephrase this hypothesis in the language of support vector machines, the GTN could be mostly producing the support vectors of each class, instead of more recognizable images well inside of each class (i.e. instead of producing many Platonic images with a high margin from the decision boundary). A prediction of this hypothesis is that the unrecognizable GTN-generated images should be closer to the decision boundaries than the recognizable GTN-generated images.

To test this hypothesis, we borrow an idea and technique from [Toneva et al.](#page-10-15) [\(2018\)](#page-10-15), which argues that one way to identify images near (or far) from a decision boundary is to count the number of times that, during the training of a neural network, images in the training set have their classification labels change. The intuition is that Platonic images in the center of a class will not have their labels change often across training, whereas images near the boundaries between classes will change labels often as the decision boundaries are updated repeatedly during training. We trained a randomly initialized network on real images (the results are qualitatively the same if the network is trained on the GTN-produced images). After each training step we classify the images in Figure [12](#page-22-0) with the network being trained. We then rank the synthetic images from Figure [12](#page-22-0) on the frequency that their classification changed between adjacent SGD steps.

Figure [15](#page-25-0) presents these images reordered (in row-major order) according to the number of times the output label for that image changed during training. The recognizable images are all tightly clustered in this analysis, showing that there is a strong relationship between how recognizable an image is and how often its label changes during training. Interestingly, the images are not all the way at one end of the spectrum. However, keep in mind that many images in this sorted list are tied with respect to the number of changes (with ties broken randomly), and the number of flips does not go up linearly with each row of the image. Figure [14](#page-24-0) shows the number of label flips vs. the order in this ranked list. The recognizable images on average have 2.0 label flips (Figure [14,](#page-24-0) orange horizontal line), meaning that they are towards the extreme of images whose labels do not change often. This result is in line with the hypothesis that these are Platonic images well inside the class boundary. However, there are also many unrecognizable images whose labels do not flip often, which is not explained by this hypothesis. Overall, this analysis suggests the discovery of something interesting, although much future work needs to be done to probe this question further.

Why are images only realistic at the end of the curriculum? Separate from, but related to, the question of why most images are unrecognizable, is why the recognizable images are only produced at the end of the curriculum. We have come up with a few different hypotheses, but we do not know which is correct. (1) The gradients flow best to those samples, and thus they become the most realistic (in line with Camp 1 of hypotheses above). (2) It helps performance for some reason to have realistic images right at the end of training, but realism does not help (Camp 3) or even hurts (Camp 2) earlier in the curriculum. For example, perhaps the Platonic images are the least likely to change the decision boundaries, allowing them to be used for final fine-tuning of the decision boundaries (akin to an annealed learning rate). In line with this hypothesis is that, when optimization cannot create a deterministic curriculum, realism seems to be higher on average (Figure [13\)](#page-23-0). (3) The effect is produced by the decision to take the batch normalization [\(Ioffe & Szegedy, 2015\)](#page-9-8) statistics from the final batch of training. Batch normalization is a common technique to improve training. Following normal batch norm procedures, during inner-loop training the batch norm statistics (mean and variance) are computed per batch. However, during inner-loop testing/inference, the statistics are instead computed from the training set. In our experiments, we calculate these statistics from the *last* batch in the curriculum. Thus, if it helps performance on the meta-training test set (the inner loop test set performance the GTN is being optimized for) to have the statistics of that batch match the statistics of the target data set (which contains real images), there could be a pressure for those images to be more realistic. Contrary to this hypothesis, however, is the fact that realism increases in the last two batches of the curriculum, not just the last batch (most visible in Figure [2,](#page-5-0) which shows sample from each batch in a separate row).

Another hypothesis (consistent with Camp 1 and Camp 3), is that producing first unrealistic then realistic images might reflect how neural networks learn (e.g. first learning low-level filters before moving to more complex examples). However, that hypothesis would presumably predict a gradual increase in realism across the curriculum, instead of realism only sharply increasing in the last few batches. Finally, we did not observe this phenomenon in the CIFAR experiments with a full curriculum: the last few batches are not realistic in that experiment (Figure [3b\)](#page-6-0). We do not know why the results on this front are different between MNIST and CIFAR experiments.

In short, we do not have a good understanding for why realism increases towards the end of the curriculum. Shedding more light on this issue is an interesting area for future research.

<span id="page-21-0"></span>Image /page/21/Picture/2 description: A grayscale image displays a sequence of ten digits, from 0 to 9, arranged horizontally. Each digit is rendered in a pixelated, somewhat blurry style, with varying shades of gray against a black background. The digits appear to be generated or reconstructed, possibly from a machine learning model, as they are not perfectly sharp but recognizable as the numbers 0, 1, 2, 3, 4, 5, 6, 7, 8, and 9 in order from left to right.

Figure 11: Pixel-wise mean per class of all GTN-generated images from the full curriculum treatment.

<span id="page-22-0"></span>Image /page/22/Figure/0 description: The image is a grid of many small, black and white images of handwritten digits. The digits appear to be generated by a machine learning model, as they are not perfectly formed and some are difficult to discern. The overall impression is a dense, textured pattern of numbers.

Figure 12: All images generated by the full-curriculum GTN. The images are shown in the order they are presented to the network, with the first bagsh of images in the curriculum in the top row and the last batch of data in the last row. The batch size does not correspond to the number of samples per row, so batches wrap from the right side of one row to the left side of the row below.

<span id="page-23-0"></span>Image /page/23/Picture/0 description: The image is a black background filled with a dense pattern of white numbers and symbols. The numbers appear to be randomly distributed and are of various sizes and orientations. Some of the symbols are difficult to discern due to the density and noise in the image, but they appear to include digits from 0 to 9, as well as some punctuation marks like commas and periods. The overall impression is of a chaotic or noisy data visualization. The caption below the image reads "Figure 13: A sample of images generated by the no-curriculum GTN."

Figure 13: A sample of images generated by the no-curriculum GTN.

<span id="page-24-0"></span>Image /page/24/Figure/0 description: The image is a line graph showing the label switching frequency for different image indices. The x-axis is labeled "Image Index" and ranges from 0 to 4000. The y-axis is labeled "Label Switching Frequency" and ranges from 0.0 to 20.0. A horizontal orange line is present at a frequency of 2.0. A blue line graph starts at a frequency of approximately 0.5 at image index 0, then increases in steps to approximately 2.0 at image index 1000. Between image indices 1000 and 2000, the frequency remains at 2.0. After image index 2000, the frequency increases in steps, reaching approximately 2.5 at image index 2500, then 3.5 at image index 2750, 5.0 at image index 3000, 6.0 at image index 3250, 7.5 at image index 3500, 12.5 at image index 3750, and finally rises sharply to approximately 20.0 at image index 4250.

Figure 14: The number of times a class label changes across training for each GTN-generated sample (Y-axis) vs. the rank of that sample when ordered by that same statistic (X-axis). A relatively small fraction of the samples flip labels many times (in line with the idea that they are near the class decision boundaries), whereas most samples change labels only a few times (i.e. once the are learned, they stay learned, in line with them being more canonical examples). The orange line shows the average number of class changes for recognizable images (those in the red box in Figure [15\)](#page-25-0). While not the images with the least number of flips, these recognizable images are towards the end of the spectrum of images whose labels do not change often, in line with the hypothesis that they are more canonical class exemplars.

<span id="page-25-0"></span>Image /page/25/Figure/0 description: The image displays a grid of numerous handwritten digits, likely from the MNIST dataset, arranged in rows and columns. A prominent red rectangle highlights a specific section of this grid, encompassing several rows of these digits. The overall impression is a visual representation of generated or sampled digits, possibly illustrating the output of a machine learning model.

Figure 15: All images generated by the full-curriculum GTN ordered by the frequency that their labels change during training. Highlighted is a dense region of realistic images that we manually identified.
<span id="page-0-0"></span>

# Curriculum Coarse-to-Fine Selection for High-IPC Dataset Distillation

Yanda Chen\* <PERSON><PERSON>\* <PERSON><PERSON>† <PERSON><PERSON>e School of Computer Science and Technology, Harbin Institute of Technology, Shenzhen

<EMAIL>

{chengongwei,zhangmiao,guanweili,nieliqiang}@hit.edu.cn

## Abstract

*Dataset distillation (DD) excels in synthesizing a small number of images per class (IPC) but struggles to maintain its effectiveness in high-IPC settings. Recent works on dataset distillation demonstrate that combining distilled and real data can mitigate the effectiveness decay. However, our analysis of the combination paradigm reveals that the current one-shot and independent selection mechanism induces an incompatibility issue between distilled and real images. To address this issue, we introduce a novel curriculum coarse-to-fine selection (CCFS) method for efficient high-IPC dataset distillation. CCFS employs a curriculum selection framework for real data selection, where we leverage a coarse-to-fine strategy to select appropriate real data based on the current synthetic dataset in each curriculum. Extensive experiments validate CCFS, surpassing the state-of-the-art by +6.6% on CIFAR-10, +5.8% on CIFAR-100, and +3.4% on Tiny-ImageNet under high-IPC settings. Notably, CCFS achieves 60.2% test accuracy on ResNet-18 with a 20% compression ratio of Tiny-ImageNet, closely matching full-dataset training with only 0.3% degradation. Code:* <https://github.com/CYDaaa30/CCFS>*.*

## 1. Introduction

Dataset distillation [\[32,](#page-9-0) [38\]](#page-9-1) aims to condense the original training dataset into a small but powerful synthetic dataset, which can then be used to train competitive models. Current Dataset Distillation (DD) methods [\[3,](#page-8-0) [34,](#page-9-2) [43\]](#page-9-3) have shown impressive performance at extremely small scales, such as 1 or 5 IPC (images-per-class). Unfortunately, these methods become less effective as IPC increases [\[4,](#page-8-1) [8,](#page-8-2) [46\]](#page-9-4), sometimes even underperforming random sample selection.

Recent studies [\[8,](#page-8-2) [19\]](#page-8-3) investigated this phenomenon and attributed it to a key issue in dataset distillation: current approaches tend to distill simple and general features into synthetic images while ignoring rare and complex features. These works attempt to address this issue from perspectives

Image /page/0/Figure/11 description: This figure illustrates a combination-based dataset distillation process. The top section, labeled "Combination-based Dataset Distillation," shows a "Matching Loss" that takes input from an "Original dataset" and a "Synthetic dataset." The synthetic dataset is composed of a combination of "D\_real" and "D\_distill." The bottom section, titled "The Selection of D\_real," presents two methods for selecting real data. Method (a), "SelMatch," involves a "One-shot Selection" from the "Original dataset," categorized by difficulty from "hard" to "easy," to obtain "D\_real." Method (b), "Ours," uses the "Original dataset" and "D\_distill" as input to a "Filter" and a "Curriculum Selection" process, which iteratively outputs a set of selected real datasets: {D\_real^1, D\_real^2, ..., D\_real^J}.

Figure 1. Comparison of combination-based dataset distillation. *Top:* General paradigm. *Bottom:* (a) SelMatch conducts an independent and one-shot selection of  $\mathcal{D}_{real}$ . (b) Our method applies curriculum selection, making  $\mathcal{D}_{\text{real}}$  dependent on  $\mathcal{D}_{\text{distill}}$ .

of optimization and dataset construction. The former work, DATM [\[8\]](#page-8-2), attempts to leverage the trajectories from different training stages to generate synthetic images with diverse patterns. It still fails to adequately incorporate the rare features of hard samples [\[19\]](#page-8-3). The latter work, SelMatch [\[19\]](#page-8-3), advocates a combination-based paradigm which merges a distilled image set  $\mathcal{D}_{distill}$  and a real image set  $\mathcal{D}_{real}$  to construct the synthetic dataset. By complementing rare and complex features of real data, SelMatch achieves state-ofthe-art performance in high-IPC situations.

A vital advantage of the combination-based paradigm is the introduction of a real image set  $\mathcal{D}_{real}$ . Although Sel-Match shows impressive performance, we argue that its selection approach of  $\mathcal{D}_{\text{real}}$  still has two shortcomings. 1) The fixed and one-shot selection of  $\mathcal{D}_{\text{real}}$  is sub-optimal and may produce inappropriate real images. 2) The independence between  $\mathcal{D}_{\text{real}}$  and  $\mathcal{D}_{\text{distill}}$  reduces the complementary effect

<sup>\*</sup>Equal contribution

<sup>†</sup>Corresponding authors

<span id="page-1-0"></span>of  $\mathcal{D}_{\text{real}}$ . To verify our point, we compare SelMatch with two naive variants in Section [3.2,](#page-2-0) "SelMatch w/ two-shot selection" and "SelMatch w/ reverse selection" which only consider a two-shot paradigm and reverse order of selection and distillation. The superiority of proposed variants reveals the underlying incompatibility issue between  $\mathcal{D}_{\text{real}}$ and  $\mathcal{D}_{\text{distill}}$  in SelMatch. This issue reduces the information richness of the generated synthetic dataset, distinctly impacting SelMatch's performance in high-IPC situations.

To address the incompatibility issue, we propose a novel Curriculum Coarse-to-Fine Selection (CCFS) method for high-IPC dataset distillation. CCFS aims to progressively select suitable real data based on the distilled set. We cast the selection of the real images as a curriculum learning problem. This allows us to merge the real images from easy to difficult through a series of curriculum phases, ensuring comprehensive coverage of the essential patterns. To enhance the connection between the real images and the distilled images, we devise a coarse-to-fine selection strategy that takes into account both the global sample difficulty and the current synthetic dataset. Our selection strategy coarsely filters out correctly-classified samples, then finely chooses a subset from misclassified samples according to their difficulty scores. The coarse stage ensures that the selected samples contain the unlearned patterns of the current synthetic set. The fine stage maximizes the complementary effect of selected samples by avoiding overly complex ones.

Extensive experiments on CIFAR-10/100 [\[16\]](#page-8-4) and Tiny-ImageNet [\[18\]](#page-8-5) demonstrate that our approach consistently outperforms current state-of-the-art methods across compression ratios range of 5%-30%. Specifically, we achieved top-1 test accuracies of  $\{92.5\%, 71.5\%, 60.2\%\}$ on {CIFAR-10, CIFAR-100, Tiny-ImageNet} with compression ratios of {10%, 10%, 20%}, surpassing the current SOTA by  $\{6.6\%, 5.8\%, 3.4\%\}$ , separately. We record the state in each curriculum phase, including filter performance, difficulty distribution and visualization of real samples. The analyses of these states effectively illustrate the incremental growth in both performance and difficulty introduction of the synthetic dataset as the curriculum progresses, which aligns with our design principles. Our contributions can be summarized as follows:

- 1. We advocate the combination-based paradigm for high-IPC dataset distillation, and propose a novel curriculum coarse-to-fine selection method to address the existing incompatibility issue.
- 2. In each curriculum, we devise a coarse-to-fine selection strategy to yield the optimal real data by inspecting the limitations of the current synthetic dataset.
- 3. Our method achieves only 0.3% performance loss with a 20% compression ratio on Tiny-ImageNet. The analyses indicate the selected images become more complex and difficult as the curriculum progresses.

# 2. Related Works

Dataset Distillation. Dataset distillation aims to condense the original training dataset  $T$  into a small but powerful synthetic set  $S$ , which allows models to be trained efficiently and achieve performance comparable to full dataset training. Wang et al. [\[32\]](#page-9-0) first proposed the concept of dataset distillation with a bi-level framework. Subsequently, a few works have sought to optimize dataset distillation from different perspectives [\[2,](#page-8-6) [10,](#page-8-7) [35,](#page-9-5) [41,](#page-9-6) [42\]](#page-9-7). Meanwhile, various surrogate objectives gradually formed several significant branches, which can be summarized as kernelbased approaches [\[23,](#page-8-8) [25,](#page-8-9) [47\]](#page-9-8), gradient/trajectory-based methods [\[3,](#page-8-0) [6,](#page-8-10) [8,](#page-8-2) [21,](#page-8-11) [44\]](#page-9-9), distribution-based techniques [\[31,](#page-9-10) [40,](#page-9-11) [43,](#page-9-3) [45\]](#page-9-12), distilled dataset parameterization [\[5,](#page-8-12) [14,](#page-8-13) [22,](#page-8-14) [33\]](#page-9-13) and decoupled-optimization [\[24,](#page-8-15) [29,](#page-9-14) [36,](#page-9-15) [37\]](#page-9-16).

Recent studies [\[8,](#page-8-2) [19\]](#page-8-3) have noticed the issue of dataset distillation failing when synthesizing large number of images per class (IPC). Their consensus is introducing more complex features into the synthetic dataset to alleviate its homogeneous and simplistic nature. DATM [\[8\]](#page-8-2) employs a flexible trajectory matching to align expert trajectories from later training stages, aligning with the theory that models learn more complex patterns as training progresses. On the other hand, SelMatch [\[19\]](#page-8-3) emphasizes the initialization of the synthetic dataset by using a sliding window to incorporate real images of appropriate difficulty levels. It divides the synthetic dataset into two subsets,  $\mathcal{D}_{distill}$  and  $\mathcal{D}_{real}$ , allowing for updates to  $\mathcal{D}_{\text{distill}}$  while keeping  $\mathcal{D}_{\text{real}}$  fixed during MTT [\[3\]](#page-8-0) distillation. This approach serves as the SOTA approach for high-IPC dataset distillation.

Our method CCFS is based on the concept of combining distilled data with real data to solve the failing problem in high-IPC cases. Unlike previous approaches, we design a novel curriculum framework to progressively select suitable real data for the distilled data, and conduct a more targeted selection strategy within each curriculum phase.

Curriculum Learning in Dataset Distillation Curriculum learning [\[1,](#page-8-16) [17,](#page-8-17) [20,](#page-8-18) [28,](#page-9-17) [39\]](#page-9-18) is originally defined as a method for progressively training models by strategically arranging the inputting sequence of training data. Some dataset distillation methods leverage the concept of curriculum learning. SeqMatch [\[7\]](#page-8-19) divides synthetic data into multiple subsets and sequentially optimizes them to learn high-level features. CDA [\[36\]](#page-9-15) implements a progressive difficulty data augmentation on the synthetic images. CUDD [\[24\]](#page-8-15) employs curriculum evaluation to gradually expand the distilled dataset. In CCFS, we design a curriculum framework that gradually expands the synthetic dataset by incorporating suitable real samples. This process takes into account both the prior knowledge of sample difficulty and the limitations of the current synthetic dataset. Our curriculum framework effectively enriches the diversity of the synthetic dataset and enhances its performance.

<span id="page-2-5"></span><span id="page-2-4"></span>Image /page/2/Figure/0 description: This figure contains a bar chart and four line charts. The bar chart on the left shows the test accuracy (%) for different IPC values (25, 50, 100, 150) for three methods: 1) SelMatch, 2) SelMatch w/ two-shot selection, and 3) SelMatch w/ reverse selection. The test accuracy increases with IPC for all three methods. The line charts on the right show the test accuracy (%) against beta for different IPC values (25, 50, 100, 150). For IPC=25, SelMatch w/ reverse selection has higher accuracy than SelMatch. For IPC=50, SelMatch w/ reverse selection has higher accuracy than SelMatch. For IPC=100, SelMatch w/ reverse selection has higher accuracy than SelMatch. For IPC=150, SelMatch w/ reverse selection has higher accuracy than SelMatch.

(a) Results of 3 setting

(a) Results of 3 settings (b) Detailed comparison of setting 1) and 3)

Figure 2. Results of the analysis experiments on CIFAR-100. (a) Top-1 accuracy of the 3 settings with IPC=25, 50, 100, 150. In each IPC, setting [2\),](#page-2-1) which modifies only the selection strategy of  $\mathcal{D}_{real}$ , outperforms setting [1\)](#page-2-2) with the original SelMatch setup. Setting [3\)](#page-2-3) reverses setting [2\)'](#page-2-1)s process by first distilling  $\mathcal{D}_{\text{distill}}$  and then conducting a two-shot selection to obtain  $\mathcal{D}_{\text{real}}$ , resulting in the best performance among the 3 groups. (b) A detailed comparison between setting [1\)](#page-2-2) and [3\)](#page-2-3) at various window starting point  $\beta$ . In all cases of  $\beta$ , setting 3) outperforms setting [1\)](#page-2-2) and shows more stable performance fluctuations across different  $β$ .

### 3. Preliminary

#### 3.1. Combination-Based Dataset Distillation

Recent studies [\[4,](#page-8-1) [8,](#page-8-2) [46\]](#page-9-4) reveal that traditional dataset distillation methods tend to synthesize simple features of the original dataset. This limits its effectiveness especially when the synthetic dataset contains more images per class (IPC). To address this problem, SelMatch [\[19\]](#page-8-3) introduces a combination-based framework consisting of selectionbased initialization and partial optimization.

SelMatch introduces modifications to the traditional optimization-based method in both the initialization and the updating phase. It begins by arranging the training samples of the original dataset in descending order of difficulty based on pre-calculated difficulty scores. Then it uses a sliding window of size  $IPC$  (images-per-class) to select subsets in each class with a window starting point hyperparameter  $\beta \in [0, 1]$ . It collects these selected subsets of each class as  $\mathcal{D}_{initial}$  to initialize  $\mathcal{D}_{syn}$ .

Once the window starting point is determined, SelMatch further partitions samples within the window according to a distillation portion hyperparameter  $\alpha \in [0, 1]$ . The subset  $\mathcal{D}_{\text{real}}$  contains the harder samples of the first  $(1-\alpha) \times |\mathcal{D}_{\text{syn}}|$ portion of the window and keeps unchanged during distillation. The remaining  $\alpha \times |\mathcal{D}_{syn}|$  easier samples serve as the initialization set  $\mathcal{D}_{\text{pre-distill}}$  for distillation to produce the subset  $\mathcal{D}_{distill}$ . Both  $\beta$  and  $\alpha$  are optimal hyperparameters determined through a search process.

During subsequent MTT [\[3\]](#page-8-0) distillation, the update aims to minimize the matching loss between the entire  $\mathcal{D}_{syn} =$  $\mathcal{D}_{\text{real}} \cup \mathcal{D}_{\text{distill}}$  and the original dataset  $\mathcal{T}$ , i.e.,

$$
\mathcal{L}\left(\mathcal{D}_{\text{real}}\cup\mathcal{D}_{\text{distill}},\mathcal{T}\right). \tag{1}
$$

<span id="page-2-0"></span>

#### 3.2. Limitations of SelMatch

The combination-based paradigm in SelMatch represents the SOTA approach for addressing the less effective problem of high-IPC dataset distillation. However, we argue that the sliding window selection, as the core of SelMatch, may have shortcomings due to its rigid fixed and one-shot mechanism. To verify the existence of the shortcomings, we design two variants which break the mechanism. Details of the settings are as follows:

- <span id="page-2-2"></span>1) SelMatch: Sort the original dataset in descending order by difficulty score. Determine  $\mathcal{D}_{syn}$  with a sliding window. Partition  $\mathcal{D}_{syn}$  into  $\mathcal{D}_{real}$  and  $\mathcal{D}_{distill}$ . Update  $\mathcal{D}_{distill}$ by MTT's approach and keep  $\mathcal{D}_{\text{real}}$  unchanged.
- <span id="page-2-1"></span>2) SelMatch w/ two-shot selection: Change the one-shot window selection of  $\mathcal{D}_{\text{real}}$  into a two-shot selection. Train a model on the initialization set  $\mathcal{D}_{pre-distill}$  and evaluate it on the full training set. Select the simplestmisclassified samples and add them into  $\mathcal{D}_{\text{real}}$ . Repeat the process twice, then merge  $\mathcal{D}_{\text{real}}$  with  $\mathcal{D}_{\text{distill}}$ .
- <span id="page-2-3"></span>3) SelMatch w/ reverse selection: Reverse the process in [2\)](#page-2-1) to first conduct distillation and then make the twoshot selection. At first, conduct dataset distillation on  $\mathcal{D}_{pre-distill}$  to generate  $\mathcal{D}_{distill}$ . Then implement the twoshot selection of  $\mathcal{D}_{\text{real}}$  based on the distilled set  $\mathcal{D}_{\text{distill}}$ . Finally merge them to produce the synthetic dataset.

We conduct our analytical experiments on CIFAR-100 and evaluate the final  $\mathcal{D}_{syn} = \mathcal{D}_{real} \cup \mathcal{D}_{distill}$  on the test dataset. The results are shown in Figure [2.](#page-2-4)

Figure  $2(a)$  $2(a)$  presents top-1 test accuracy of the 3 settings with the best hyperparameters (such as  $\alpha$  and  $\beta$ ). Across all IPC cases, setting [3\)](#page-2-3) performs the best, followed by setting [2\),](#page-2-1) with setting [1\)](#page-2-2) falling behind. Specifically, setting [3\)](#page-2-3) achieves an average performance improvement of

<span id="page-3-0"></span>Image /page/3/Figure/0 description: The figure illustrates a "Curriculum Selection Framework" and a "Coarse-to-Fine Selection Strategy" for machine learning. The framework begins with "Dataset Distillation" producing "Distilled Data" and "Coarse-to-Fine Selection" processing "Real Data" from previous curricula (Curriculum 1: j-1) to create a "Synthetic Dataset Sj-1". This dataset is then used in the "Coarse Filtering" stage, where a neural network "Filter φj\*" is trained and evaluated on an "Original Dataset T \ Djreal1:j-1". Misclassified data is identified. The "Fine Selection" stage then categorizes data based on a "Difficulty score" from "Easy" to "Hard", separating it into categories like "Van" and "Cat". Finally, the process generates a new "Synthetic Dataset Sj" containing updated "Distilled Data" and "Real Data" for the next curriculum (Curriculum j).

Figure 3. Architecture of our curriculum coarse-to-fine selection method for high-IPC dataset distillation, CCFS. CCFS adopts a combination of distilled and real data to construct the final synthetic dataset. We apply a curriculum framework and select the optimal real data for the current synthetic dataset in each curriculum. (a) Curriculum selection framework: CCFS begins the curriculum with the already distilled data as the initial synthetic dataset. Then continuously incorporates real data into the current synthetic dataset through the coarse-to-fine selection within each curriculum phase. (b) Coarse-to-fine selection strategy: In the coarse stage, CCFS trains a filter model on the current synthetic dataset and evaluates it on the original dataset excluding already selected data to filter out all correctly classified samples. In the fine stage, CCFS selects the simplest misclassified samples and incorporates them into the current synthetic dataset for the next curriculum.

1.7% over setting [1\)](#page-2-2) across the four IPC settings (max: +2.8%), while [2\)](#page-2-1) achieves an average improvement of 0.9% over  $1$ )(max: +1.7%). This improvement becomes more obvious as IPC increases. Figure [2\(](#page-2-4)b) presents a detailed comparison between setting [1\)](#page-2-2) and [3\)](#page-2-3) at various window starting point  $\beta$ . In all cases of  $\beta$ , setting [3\)](#page-2-3) outperforms setting [1\)](#page-2-2) and shows more stable performance fluctuations across different β.

In the first comparison between setting  $1$ ) and  $2$ ), setting [2\)](#page-2-1) obtains a better  $\mathcal{D}_{real}$  through more targeted, multiple-times selections. This reflects the rigid limitations of the fixed and one-shot sliding window initialization. While in the second comparison between setting [2\)](#page-2-1) and [3\),](#page-2-3) the only difference lies in whether the selection is made based on the initialization set  $\mathcal{D}_{pre-distill}$  or the distilled set  $\mathcal{D}_{\text{distill}}$ . It proves that selecting based on the distilled set is better. In both [1\)](#page-2-2) and [2\),](#page-2-1) although  $\mathcal{D}_{\text{real}}$  is suitable for  $\mathcal{D}_{\text{pre-distill}}$  at initialization, the updated  $\mathcal{D}_{\text{distill}}$  by the distillation prevents the assurance of this compatibility. When we reverse the process as setting [3\),](#page-2-3) the selection process becomes more targeted, further enhancing the connection between  $\mathcal{D}_{real}$  and  $\mathcal{D}_{distill}$ . The independence between  $\mathcal{D}_{real}$ and  $\mathcal{D}_{\text{distill}}$  reduces the complementary effect of  $\mathcal{D}_{\text{real}}$ .

These two factors collectively lead to the incompatibility issue between  $\mathcal{D}_{\text{real}}$  and  $\mathcal{D}_{\text{distill}}$  in the current combination paradigm, resulting in a consistent performance gap compared to full datasets. This incompatibility becomes more evident under suboptimal settings shown in Figure [2\(](#page-2-4)b). Once deviating from the optimal window position, the performance of SelMatch drops rapidly. This incompatibility issue inspired us to explore more effective strategies to select suitable real data based on the distilled data.

# 4. Method

In this section, we introduce our Curriculum Coarse-to-Fine Selection method (CCFS) for high-IPC dataset distillation. Following the idea of combining distilled and real data to construct the synthetic dataset, CCFS aims to progressively select suitable real data based on the distilled data. We first get distilled data through dataset distillation methods. Then employ a curriculum selection framework for real data selection beginning with distilled data. In each curriculum, we conduct our coarse-to-fine selection strategy to obtain the optimal real data and integrate it with the current synthetic dataset. Figure [3](#page-3-0) illustrates the architecture of CCFS.

Curriculum Selection Framework Our analysis experiments reveal that the fixed and one-shot selection of  $\mathcal{D}_{\text{real}}$ hinders obtaining suitable real samples for the synthetic dataset. This motivated us to structure the selection process of  $\mathcal{D}_{\text{real}}$  as a curriculum framework for more comprehensive coverage of essential patterns.

To make  $\mathcal{D}_{real}$  a more effective complement to  $\mathcal{D}_{distill}$ . We choose to conduct selection after distillation is finished. We begin the curriculum with the already distilled dataset  $\mathcal{D}_{distill}$ as the initial synthetic dataset  $S_0$ . In each curriculum phase

<span id="page-4-1"></span>j, we expect to obtain the optimal  $\mathcal{D}_{\text{real}}^j$  from the original dataset for the current synthetic dataset  $S_{j-1}$ :

$$
\mathcal{D}_{\text{real}}^j = \text{Select}(\mathcal{S}_{j-1}, \mathcal{T} \backslash \mathcal{D}_{\text{real}}^{1:j-1})
$$
  
s.t.  $\mathcal{S}_0 = \mathcal{D}_{\text{distill}}$  and  $j \in \{1, 2, ..., J\},$  (2)

where  $J$  is the number of curricula and Select denotes the selection strategy used in each curriculum. Note that previously selected samples are excluded in each curriculum.

Then we incorporate  $\mathcal{D}_{\text{real}}^j$  into  $\mathcal{S}_{j-1}$  as  $\mathcal{S}_j$  for the next curriculum phase. After the last curriculum, we obtain the final synthetic dataset  $S$  with the target size:

$$
S_j = S_{j-1} \cup \mathcal{D}_{real}^j,\tag{3}
$$

$$
S = S_J. \tag{4}
$$

We expect to gradually incorporate suitable features through this curriculum selection framework. Next, we introduce our strategy of selecting the optimal  $\mathcal{D}_{\text{real}}$  in each curriculum phase and explain how this strategy functions within the curriculum framework.

Coarse-to-Fine Selection Strategy The weak connection between  $\mathcal{D}_{\text{real}}$  and  $\mathcal{D}_{\text{distill}}$  in the incompatibility issue calls for a more targeted selection strategy. We design a two-step strategy, coarse-to-fine selection, to get the optimal  $\mathcal{D}_{real}$  in each curriculum phase. Given a synthetic dataset  $S$ , we first use a filter  $\phi$  trained on S to evaluate on the original training dataset  $\mathcal T$  and obtain the misclassified samples  $\mathcal D_{\text{mis}}$ :

$$
\mathcal{D}_{\text{mis}} = \left\{ (x_i, y_i) \in \mathcal{T} \mid \phi_{\boldsymbol{\theta}_{\mathcal{S}}}^*(x_i) \neq y_i \right\},\tag{5}
$$

where  $\theta_{\mathcal{S}}$  denotes the parameters of  $\phi$  optimized on  $\mathcal{S}$ .

This evaluation can coarsely reflect the limitations of current S. These limitations are primarily concentrated in the misclassified samples  $\mathcal{D}_{\text{mis}}$  from the original training set. Considering  $\mathcal{D}_{\text{mis}}$  may include samples with beneficial, harmful, or negligible influence on training [\[15,](#page-8-20) [26\]](#page-8-21), we conduct a finer selection next. We arrange the samples of  $\mathcal{D}_{\text{mis}}$  in ascending order based on pre-computed difficulty scores [\[12,](#page-8-22) [30\]](#page-9-19) and then select the easiest samples up to the target size as the optimal complement to current  $S$ :

$$
\mathcal{D}_{\text{real}} = \bigcup_{c \in \mathcal{C}} \left\{ (x_i, y_i) \in \mathcal{D}_{\text{mis}}^{(c)} \, \middle| \, \text{rank}_{\mathcal{D}_{\text{mis}}^{(c)}}(x_i) \le k \right\}, \quad (6)
$$

where C represents the total number of classes,  $\mathcal{D}_{\text{mis}}^{(c)}$  includes samples of class  $c$  in  $\mathcal{D}_{\text{mis}}$ , rank denotes the ascending order arrangement of sample difficulty, and  $k$  is the target complement amount for each class. To keep balance, we select an equal number of complement samples per class.

We figure that simple features that haven't been learned are essential for  $S$ . The nature of dataset distillation often leads to synthesizing mainly easy and representative features from the original dataset [\[8,](#page-8-2) [19,](#page-8-3) [24\]](#page-8-15), which provides

the filter with fundamental classification capabilities as evidenced by correctly classified samples. In the first step, we coarsely filter out correctly classified samples to avoid reintroducing these already learned simple features.

Now we have  $\mathcal{D}_{\text{mis}}$  that reflects the limitations in S. Among these limitations, simpler features provide greater benefit to model training compared to more difficult and complex features, as they are easier to learn. Pre-calculated difficulty scores effectively measure the relative difficulty of sample features from a global perspective, guiding our fine selection in the next step. By selecting the simplest ones from misclassified samples, we obtain the optimal  $\mathcal{D}_{\text{real}}$ while avoiding the introduction of overly complex features that could hinder the performance of  $S$ . Section  $5.4$  further demonstrates the effectiveness of our selection strategy.

Algorithm [1](#page-4-0) describes the entire process of the CCFS algorithm. We employ this coarse-to-fine selection in our curriculum framework. Although we repeatedly select the simplest-misclassified samples across curriculum phases, the continuous enrichment of the synthetic dataset leads to an improvement in the filter's capacity, which in turn raises the lower bound of the difficulty for misclassified samples. Meanwhile, the strategy of selecting the simplest samples maintains a manageable difficulty progression between curriculum phases. We provide more details on the progressive nature of CCFS in Further Analysis [5.5.](#page-6-1)

<span id="page-4-0"></span>Algorithm 1 CCFS: A curriculum coarse-to-fine selection framework for high-IPC dataset distillation

**Input:** Original full dataset  $\mathcal{T}$ , number of classes  $C$ , target images per class IPC, distillation portion  $\alpha \in [0, 1]$ , dataset distillation algorithm  $A$ , number of curricula  $J$ , pre-calculated difficulty score score.

$$
\mathcal{D}_{\text{distill}} = \mathcal{A}(\mathcal{T}), \text{ s.t. } |\mathcal{D}_{\text{distill}}| = \lceil \alpha \times \text{IPC} \times C \rceil
$$
\n
$$
\mathcal{S}_0 \leftarrow \mathcal{D}_{\text{distill}}
$$
\n**for**  $j = 1$  **to**  $J$  **do**\n
$$
k_j = \lfloor \frac{\text{IPC} \times (1-\alpha)}{J} \rfloor
$$
\nTrain the filter model on  $\mathcal{S}_{j-1}$  to get  $\phi_j^*$ 

\n
$$
\triangleright \text{Coarse Filtering}
$$
\n
$$
\mathcal{D}_{\text{mis}}^j = \left\{ (x_i, y_i) \in \mathcal{T} \setminus \mathcal{D}_{\text{real}}^{1:j-1} \mid \phi_j^*(x_i) \neq y_i \right\}
$$
\n
$$
\triangleright \text{ Fine Selection}
$$
\n
$$
\text{rank}_{\mathcal{D}_{\text{mis}}^{j,c}} \leftarrow \text{sort}_{\text{asc}}(\mathcal{D}_{\text{mis}}^{j,c}, \text{score})
$$
\n
$$
\mathcal{D}_{\text{real}}^j = \bigcup_{c \in \mathcal{C}} \left\{ (x_i, y_i) \in \mathcal{D}_{\text{mis}}^{j,c} \mid \text{rank}_{\mathcal{D}_{\text{mis}}^{j,c}}(x_i) \leq k_j \right\}
$$
\n
$$
\mathcal{S}_j \leftarrow \mathcal{S}_{j-1} \cup \mathcal{D}_{\text{real}}^j
$$
\n**end for**\n**Output:** The final synthetic dataset  $\mathcal{S} \leftarrow \mathcal{S}_J$ 

<span id="page-5-1"></span><span id="page-5-0"></span>Table 1. Performance of CCFS compared to the SOTA dataset distillation and coreset selection baselines. We report the results of all listed methods with the identical validation model ResNet-18. CCFS achieves state-of-the-art performance across high-IPC settings ranging from 5% to 30% compression ratio. Additionally, the selection-only version of our method, self-evolved selection, beats other coreset selection baselines and exhibits comparable performance to SOTA dataset distillation methods. <sup>∗</sup> denotes results obtained using the official code due to the incomplete results shown in original papers. *IPC*: images per class, *Ratio*: the compression ratio of the synthetic dataset compared to the original dataset.

| Dataset                |                |                | $CIFAR-10$     |                |                |                | CIFAR-100      |                    |                | Tiny-ImageNet  |
|------------------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|--------------------|----------------|----------------|
| <b>IPC</b>             | 250            | 500            | 1000           | 1500           | 25             | 50             | 100            | 150                | 50             | 100            |
| Ratio                  | $5\%$          | 10%            | 20%            | $30\%$         | $5\%$          | $10\%$         | 20%            | $30\%$             | $10\%$         | 20%            |
| Random                 | $73.4 \pm 1.5$ | $79.3 \pm 0.3$ | $85.6 \pm 0.4$ | $88.3 \pm 0.2$ | $35.8 \pm 0.6$ | $40.7 \pm 1.0$ | $53.2 \pm 0.9$ | $60.3 + 1.3$       | $30.1 \pm 0.6$ | $40.1 \pm 0.4$ |
| Forgetting [30]        | $30.7 \pm 0.3$ | $41.5 \pm 0.7$ | $68.4 \pm 1.6$ | $83.5 \pm 1.8$ | $9.5 \pm 0.3$  | $13.2 \pm 0.6$ | $27.0 + 1.1$   | $42.3 + 1.0$       | $5.7 \pm 0.1$  | $12.4 \pm 0.2$ |
| Glister $[13]$         | $46.6 \pm 1.3$ | $56.6 \pm 0.5$ | $79.0 \pm 0.7$ | $85.0 \pm 0.9$ | $21.7 \pm 0.8$ | $26.7 \pm 1.3$ | $39.9 \pm 1.4$ | $52.1 \pm 1.3$     | $22.6 \pm 0.5$ | $34.0 \pm 0.3$ |
| Oracle window $[19]$   | $79.3 \pm 0.7$ | $85.2 \pm 0.1$ | $89.9 \pm 0.5$ | $90.6 \pm 0.3$ | $43.2 \pm 1.8$ | $50.0 \pm 0.8$ | $59.2 \pm 0.8$ | $64.7 \pm 0.5$     | $42.5 \pm 0.3$ | $49.2 \pm 0.3$ |
| Self-evolved selection | $81.6 \pm 0.5$ | $86.4 \pm 0.3$ | $90.3 \pm 0.5$ | $91.6 \pm 0.4$ | $45.6 \pm 0.5$ | $50.7 \pm 0.7$ | $62.6 \pm 0.8$ | $66.5 \pm 0.2$     | $43.9 \pm 0.6$ | $50.2 \pm 0.4$ |
| <b>DSA</b> [42]        | $74.7 \pm 1.5$ | $78.7 \pm 0.7$ | $84.8 \pm 0.5$ | $\sim$         | $38.4 \pm 0.4$ | $43.6 \pm 0.7$ | ٠              | $\sim$             | $27.8 \pm 1.4$ |                |
| DM [43]                | $75.3 \pm 1.4$ | $79.1 \pm 0.6$ | $85.6 \pm 0.5$ | $\sim$         | $37.5 \pm 0.6$ | $42.6 \pm 0.5$ |                | ۰                  | $31.0 \pm 0.6$ |                |
| <b>MTT</b> [3]         | $80.7 \pm 0.4$ | $82.2 \pm 0.4$ | $86.1 \pm 0.3$ | $88.6 \pm 0.2$ | $49.9 \pm 0.7$ | $51.3 \pm 0.4$ | $58.7 \pm 0.6$ | $63.1 \pm 0.3$     | $40.3 \pm 0.3$ | $44.2 \pm 0.5$ |
| $SRe^{2}L^{*}$ [37]    | $77.5 \pm 0.7$ | $85.1 \pm 0.2$ | $86.8 \pm 0.3$ | $87.8 \pm 0.4$ | $49.7 \pm 0.4$ | $51.4 \pm 0.4$ | $58.8 \pm 0.2$ | $61.9 \pm 0.3$     | $41.1 \pm 0.4$ | $49.7 \pm 0.3$ |
| <b>DATM</b> [8]        | $\sim$         | $84.8 \pm 0.3$ | $87.6 \pm 0.3$ | ٠              | ٠              | $51.0 \pm 0.5$ | $61.5 \pm 0.3$ | ÷.                 | $42.2 \pm 0.2$ |                |
| SelMatch [19]          | $82.8 \pm 0.2$ | $85.9 \pm 0.2$ | $90.4 \pm 0.2$ | $91.3 \pm 0.2$ | $50.9 \pm 0.3$ | $54.5 \pm 0.6$ | $62.4 \pm 0.5$ | $67.4 + 0.2$       | $44.7 \pm 0.2$ | $50.4 \pm 0.2$ |
| $CDA*$ [36]            | $78.0 \pm 0.4$ | $84.4 \pm 0.4$ | $86.4 \pm 0.2$ | $87.5 \pm 0.4$ | $50.6 \pm 0.3$ | $59.7 \pm 0.2$ | $61.1 \pm 0.1$ | $63.4 \pm 0.2$     | $45.6 \pm 0.2$ | $52.4 \pm 0.1$ |
| CUDD [24]              |                |                |                |                | $63.5 \pm 0.3$ | $65.7 \pm 0.2$ |                |                    | $55.6 \pm 0.2$ | $56.8 \pm 0.2$ |
| CCFS (Ours)            | $87.9 \pm 0.4$ | $92.5 \pm 0.2$ | $93.2 \pm 0.1$ | $93.8 \pm 0.1$ | $65.3 \pm 0.2$ | $71.5 \pm 0.3$ | $73.0 \pm 0.2$ | $74.8 \!\pm\! 0.2$ | $55.8 \pm 0.3$ | $60.2 \pm 0.2$ |
| <b>Full Dataset</b>    | $95.5 \pm 0.2$ |                |                |                |                |                | $78.8 \pm 0.3$ |                    |                | $60.5 \pm 0.2$ |

## 5. Experimental Results

### 5.1. Experiment Setup

We evaluate the performance of our method CCFS on various datasets including CIFAR-10, CIFAR-100, and Tiny-ImageNet. We compare our method with SOTA dataset distillation and coreset selection methods. For coreset selection baselines, we include Glister [\[13\]](#page-8-23), Forgetting [\[30\]](#page-9-19), and the oracle-window selection proposed in SelMatch. For comparison, we also report a selection-only version of CCFS, referred to as self-evolved selection. For dataset distillation baselines, we incorporate DSA [\[42\]](#page-9-7), DM [\[43\]](#page-9-3), MTT [\[3\]](#page-8-0), DATM [\[8\]](#page-8-2), SelMatch [\[19\]](#page-8-3), SRe<sup>2</sup>L [\[37\]](#page-9-16), CDA [\[36\]](#page-9-15) and CUDD [\[24\]](#page-8-15). We also report the full dataset training performance with 200 training epochs.

### Datasets Details.

- CIFAR-10 [\[16\]](#page-8-4): 10 classes with 5000 low-resolution (32  $\times$  32) training images per class, 10000 images for testing.
- CIFAR-100 [\[16\]](#page-8-4): 100 classes with 500 low-resolution (32)  $\times$  32) training images per class, 10000 images for testing.
- Tiny-ImageNet [\[18\]](#page-8-5): 200 classes with 500 highresolution (64  $\times$  64) training images per class, 10000 images for validation.

Evaluation Networks. We choose ResNet-18 [\[9\]](#page-8-24) as the uniform evaluation network for the main comparison. For cross-architecture generalization, we use ResNet-50/101, DenseNet-121 [\[11\]](#page-8-25), and RegNet-Y-8GF [\[27\]](#page-9-20) as the evaluation backbones.

**Implement Details of CCFS.** We begin with  $\mathcal{D}_{distill}$  syn-thesized by CDA [\[36\]](#page-9-15) method, which belongs to  $SRe^2L$  [\[37\]](#page-9-16) series and implements a progressive difficulty data augmentation on the synthetic images during distillation. In the next curriculum selection, we set the default number of curriculum phases to 3 and evenly distribute the samples to be selected among them. In each curriculum, we train ResNet-18 from scratch on the current synthetic dataset as the filter, using equal training epochs as those in the final evaluation. We use pre-calculated Forgetting [\[30\]](#page-9-19) scores and apply our selection strategy on the training set, excluding previously selected samples. We report the results of the optimal distillation portion  $\alpha$  at each IPC setting. Our method has excellent scalability and can be adapted to various dataset distillation methods. We present the results of combining CCFS with MTT [\[3\]](#page-8-0) dataset distillation in the Appendix.

## 5.2. Main Results

We compare our method with the state-of-the-art dataset distillation and coreset selection methods on CIFAR-10, CIFAR-100, and Tiny-ImageNet under high-IPC settings ranging from 5% to 30% compression ratios. As shown in Table [1,](#page-5-0) previous distillation methods gradually lose effectiveness as IPC increases, even falling behind random selection. By progressively introducing suitable real samples into the synthetic dataset, CCFS establishes new stateof-the-art performance in high-IPC settings. Notably, our method achieves a performance gain of {6.6%, 5.8%} on {CIFAR-10, CIFAR-100} with compression ratio of 10%.

<span id="page-6-6"></span><span id="page-6-2"></span>Table 2. Cross-architecture experiment results on Tiny-ImageNet with IPC=100.

| Method             | Validation Model |              |              |              |              |
|--------------------|------------------|--------------|--------------|--------------|--------------|
|                    | R18              | R50          | R101         | DenseNet-121 | RegNet-Y-8GF |
| SRe2L              | 48.00            | 51.02        | 51.92        | 50.66        | 54.78        |
| CDA                | 51.12            | 54.00        | 55.04        | 52.47        | 57.13        |
| <b>CCFS (Ours)</b> | <b>60.20</b>     | <b>60.67</b> | <b>61.17</b> | <b>60.52</b> | <b>62.94</b> |

<span id="page-6-3"></span>Table 3. Ablation study on the select strategy on CIFAR-100 with  $IPC=50$ .

| Coarse Stage  | Fine Stage |      |        |
|---------------|------------|------|--------|
|               | Simple     | Hard | Random |
| Classified    | 66.8       | 63.5 | 66.8   |
| Misclassified | 71.5       | 65.0 | 70.1   |

For Tiny-ImageNet with IPC=100 (20% compression ratio), we achieve 60.2% top-1 test accuracy, representing a 3.4% improvement over the current state-of-the-art method. This performance comes remarkably close to the 60.5% test accuracy of full dataset training.

Additionally, we report a selection-only version of CCFS, referred to as self-evolved selection. We select real samples of appropriate difficulty with a sliding window as the initial coreset and expand it following the CCFS strategy. Self-evolved selection significantly outperforms other coreset selection methods in high IPC and also exhibits comparable performance to advanced dataset distillation approaches. It indicates that progressively selecting suitable real images is crucial for producing a coreset with the largest coverage of essential patterns in the original dataset.

### 5.3. Cross-architecture Generalization

To further evaluate CCFS's effectiveness, we conduct crossarchitecture generalization experiments using additional validation models beyond the ResNet-18 in the main table, including ResNet-50/101, DenseNet-121, and RegNet-Y-8GF. We set ResNet-18 as the filter model for curriculum selection and generate the final synthetic dataset, which is then used to train other validation models from scratch. As shown in Table [2,](#page-6-2) our method demonstrates robust generalization performance.

### 5.4. Ablation Study

<span id="page-6-0"></span>The selection strategy. In our coarse-to-fine selection strategy, we choose the misclassified subset in the coarse stage and select the simplest ones in the fine stage next. This combination has demonstrated excellent performance. Here, we explore other combinations. Specifically, for each curriculum selection, we select the simplest/hardest or just randomly select samples from correctly-classified/misclassified subset by current trained filter model. We conducted experiments on CIFAR-100 with IPC=50. Among all results shown in Table [3,](#page-6-3) the simplest-misclassified selection strategy outperforms all other combinations, further demonstrating its effectiveness.

<span id="page-6-4"></span>Table 4. Ablation study on the difficulty score used in selection strategy with 10% compression ratio on CIFAR-10, CIFAR-100 and Tiny-ImageNet.

| Score             | CIFAR-10    | CIFAR-100   | Tiny-ImageNet |
|-------------------|-------------|-------------|---------------|
| Logits            | 91.8        | 68.7        | 52.5          |
| C-score           | 92.2        | 71.0        | -             |
| <b>Forgetting</b> | <b>92.5</b> | <b>71.5</b> | <b>55.8</b>   |

<span id="page-6-5"></span>Table 5. Ablation study on the number of curricula with 10% compression ratio on CIFAR-10, CIFAR-100 and Tiny-ImageNet.

| Number of curricula | CIFAR-10 | CIFAR-100 | Tiny-ImageNet |
|---------------------|----------|-----------|---------------|
| 1                   | 91.6     | 67.9      | 54.4          |
| 2                   | 91.8     | 70.4      | 55.3          |
| 3                   | 92.5     | 71.5      | 55.8          |
| 4                   | 92.4     | 71.6      | 55.7          |

The difficulty scores. Our method requires difficulty scores to measure the complexity of samples. We explored the impact of using different difficulty scores. We include pre-calculated C-score [\[12\]](#page-8-22) and Forgetting scores [\[30\]](#page-9-19). Additionally, we measure sample difficulty using the predicted values (logits) by the current trained filter model for the actual class of each training sample, with smaller values indicating greater difficulty. We conducted experiments on CIFAR-10, CIFAR-100, and Tiny-ImageNet with a 10% compression ratio using these three scores. Results in Table [4](#page-6-4) indicate that the Forgetting score outperformed the others across all three datasets, while the logits approach leads to performance degradation due to its coarse reflection of sample difficulty. The Forgetting score is leveraged for the main results (Table [1\)](#page-5-0).

The number of curricula. Table [5](#page-6-5) illustrates the impact of varying the number of curricula. It indicates that moderate increases improve performance, further demonstrating the effectiveness of the curriculum selection framework. However, continuing to increase number of curricula results in only marginal performance gains. Balancing performance and efficiency, we employ 3 curriculum phases in our main results (Table [1\)](#page-5-0).

<span id="page-6-1"></span>

### 5.5. Further Analysis

The curriculum framework in CCFS aims to progressively incorporate suitable samples into the synthetic dataset, thereby enhancing its performance incrementally. We expect to observe a continuous improvement in the classification capability of the filter model, along with a gradual increase in the overall difficulty of the selected samples as the curriculum progresses. To verify that CCFS achieves these intended effects, we conduct extensive experiments on Tiny-ImageNet with 3 curriculum phases and record the state in each curriculum phase. Figure [4](#page-7-0) illustrates the effectiveness of the curriculum framework in CCFS from both the filter performance and sample difficulty.

In Figure  $4(a)$  $4(a)$ , we present the performance of the filter model trained on the synthetic dataset in each curriculum

<span id="page-7-0"></span>Image /page/7/Figure/0 description: This image contains three plots. Plot (a) is a line graph titled "Filter performance" showing "Top-1 Accuracy(%)" on the y-axis and "Curriculum Stage" on the x-axis. There are two lines: a blue line representing "on val set" and a red line representing "on train set". The accuracy increases with the curriculum stage for both sets, with the "on train set" consistently showing higher accuracy. Plot (b) is a violin plot titled "Difficulty Distribution" showing "Difficulty Score" on the y-axis and "Curriculum Stage" on the x-axis. It displays the distribution of difficulty scores for two categories, "IPC=50" (teal) and "IPC=100" (coral), across three curriculum stages. The distributions show an increase in difficulty score as the curriculum stage progresses. Plot (c) is a grid of images titled "Visualization of selected samples". It shows examples of "Albatross", "School Bus", and "Banana" across three curriculum stages (1, 2, and 3). The images for Albatross and School Bus appear to become more complex or varied with each stage, while the Banana images show different stages of peeling and a creatively decorated banana in the last stage.

Figure 4. Further analysis on the curriculum framework. (a) Performance of the filter model trained on the synthetic dataset in each curriculum phase with IPC=50: The filter's classification accuracy steadily improves on both the original training set and the validation set. (b) The difficulty distribution of real samples selected in each curriculum phase: As the curriculum progresses, both the average difficulty as well as the upper and lower difficulty bounds of selected samples increase significantly. Moreover, higher IPC tend to include more difficult samples than lower IPC within the same curriculum phase. CCFS effectively guides the synthetic dataset to incorporate more challenging samples. (c) Visualization of the samples selected in each curriculum phase. We present images of median difficulty across several categories in Tiny-ImageNet: Albatross, School Bus and Banana. The visualization effectively illustrates the gradual increase in difficulty (diverse poses, complex backgrounds, other distractions...) facilitated by CCFS.

phase with IPC=50. As the curriculum progresses, the filter's classification accuracy steadily improves on both the original training set and the validation set. This indirectly reflects the growing representational capacity of the synthetic dataset.

Figure [4\(](#page-7-0)b) illustrates the difficulty distribution of real samples selected in each curriculum phase. We can observe that the selected samples' average difficulty increases significantly across 3 curriculum phases as intended. Moreover, as the curriculum progresses, both the upper and lower bounds of the difficulty in selected samples are rising. This trend indicates that CCFS effectively enhances the overall difficulty of the synthetic dataset instead of struggling within a similar range. Since we continuously select the simplest-misclassified samples in each curriculum phase, the increase in lower bounds also indirectly reflects the rising threshold for the filter model's errors, indicating a more concrete improvement in its filtering capability, rather than just a simple performance boost. Consequently, CCFS effectively guides the synthetic dataset to incorporate more challenging and training-valuable samples. Additionally, within the same curriculum phase, synthetic datasets with higher IPC tend to include more difficult samples. This also aligns with our expectations: larger synthetic datasets are supposed to encapsulate more complex information. As IPC increases, CCFS successfully introduces harder and rarer features into the synthetic dataset.

In Figure  $4(c)$  $4(c)$ , we visualize the samples selected in different curriculum phases, showcasing samples of median difficulty across several categories. In the early stages, CCFS tends to select classic samples that capture the general features of the category. These images have simple backgrounds and fully visible objects. As the curriculum progresses, more challenging samples are incorporated into the synthetic dataset, featuring diverse poses (e.g., bird in flight, peeled banana), partial views (e.g., the lower half of bird, the front of school bus), complex backgrounds, and other distractions. This visualization effectively illustrates the gradual increase in difficulty facilitated by CCFS.

# 6. Conclusion

In this paper, we reveal the incompatibility issue between distilled and real data in the current combination-based dataset distillation method through a series of analysis experiments. We propose CCFS, a novel combination-based framework for high-IPC dataset distillation. We apply a curriculum selection framework for real data and begin the curriculum with distilled data. This ensures suitable features are progressively introduced into the synthetic dataset across curriculum phases. In each curriculum phase, we employ our coarse-to-fine selection strategy to obtain the optimal real data for the current synthetic dataset. This effectively enhances the connection between distilled and real data. CCFS significantly narrows the performance gap between synthetic datasets and full datasets under high-IPC conditions. We achieve state-of-the-art performance in various high-IPC settings on CIFAR-10, CIFAR-100, and Tiny-ImageNet. Further analyses demonstrate the effectiveness of our selection strategy and the expected progressive effect of the curriculum framework. CCFS also exhibits robust cross-architecture generalization and excellent scalability to other distillation approaches.

## Acknowledgement

This study is supported by the National Natural Science Foundation of China (Grant No. 62306084, U23B2051, 62476071, and U24A20328), and Shenzhen Science and Technology Program (Grant No. GXWD20231128102243003, KJZD20230923115113026, and ZDSYS20230626091203008), and China Postdoctoral Science Foundation (Grant No. 2024M764192).

## References

- <span id="page-8-16"></span>[1] Yoshua Bengio, Jérôme Louradour, Ronan Collobert, and Jason Weston. Curriculum learning. In *Proceedings of the 26th annual international conference on machine learning*, pages 41–48, 2009. [2](#page-1-0)
- <span id="page-8-6"></span>[2] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020. [2](#page-1-0)
- <span id="page-8-0"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [1,](#page-0-0) [2,](#page-1-0) [3,](#page-2-5) [6](#page-5-1)
- <span id="page-8-1"></span>[4] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. DC-BENCH: Dataset condensation benchmark. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022. [1,](#page-0-0) [3](#page-2-5)
- <span id="page-8-12"></span>[5] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. *Advances in Neural Information Processing Systems*, 35:34391–34404, 2022. [2](#page-1-0)
- <span id="page-8-10"></span>[6] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 3749–3758, 2023. [2](#page-1-0)
- <span id="page-8-19"></span>[7] Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023. [2](#page-1-0)
- <span id="page-8-2"></span>[8] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *The Twelfth International Conference on Learning Representations*, 2024. [1,](#page-0-0) [2,](#page-1-0) [3,](#page-2-5) [5,](#page-4-1) [6](#page-5-1)
- <span id="page-8-24"></span>[9] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [6](#page-5-1)
- <span id="page-8-7"></span>[10] Yang He, Lingao Xiao, Joey Tianyi Zhou, and Ivor Tsang. Multisize dataset condensation. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2024. [2](#page-1-0)
- <span id="page-8-25"></span>[11] Gao Huang, Zhuang Liu, Laurens Van Der Maaten, and Kilian Q Weinberger. Densely connected convolutional networks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4700–4708, 2017. [6](#page-5-1)

- <span id="page-8-22"></span>[12] Ziheng Jiang, Chiyuan Zhang, Kunal Talwar, and Michael C Mozer. Characterizing structural regularities of labeled data in overparameterized models. *arXiv preprint arXiv:2002.03206*, 2020. [5,](#page-4-1) [7](#page-6-6)
- <span id="page-8-23"></span>[13] Krishnateja Killamsetty, Durga Sivasubramanian, Ganesh Ramakrishnan, and Rishabh Iyer. Glister: Generalization based data subset selection for efficient and robust learning. In *Proceedings of the AAAI Conference on Artificial Intelligence*, pages 8110–8118, 2021. [6](#page-5-1)
- <span id="page-8-13"></span>[14] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022. [2](#page-1-0)
- <span id="page-8-20"></span>[15] Pang Wei Koh and Percy Liang. Understanding black-box predictions via influence functions. In *International conference on machine learning*, pages 1885–1894. PMLR, 2017. [5](#page-4-1)
- <span id="page-8-4"></span>[16] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. *Technical Report, University of Toronto, Department of Computer Science, 2009*, 2009. [2,](#page-1-0) [6](#page-5-1)
- <span id="page-8-17"></span>[17] M Kumar, Benjamin Packer, and Daphne Koller. Self-paced learning for latent variable models. *Advances in neural information processing systems*, 23, 2010. [2](#page-1-0)
- <span id="page-8-5"></span>[18] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015. [2,](#page-1-0) [6](#page-5-1)
- <span id="page-8-3"></span>[19] Yongmin Lee and Hye Won Chung. Selmatch: Effectively scaling up dataset distillation via selection-based initialization and partial updates by trajectory matching. In *Forty-first International Conference on Machine Learning*, 2024. [1,](#page-0-0) [2,](#page-1-0) [3,](#page-2-5) [5,](#page-4-1) [6](#page-5-1)
- <span id="page-8-18"></span>[20] Yong Jae Lee and Kristen Grauman. Learning the easy things first: Self-paced visual category discovery. In *CVPR 2011*, pages 1721–1728. IEEE, 2011. [2](#page-1-0)
- <span id="page-8-11"></span>[21] Dai Liu, Jindong Gu, Hu Cao, Carsten Trinitis, and Martin Schulz. Dataset distillation by automatic training trajectories. *arXiv preprint arXiv:2407.14245*, 2024. [2](#page-1-0)
- <span id="page-8-14"></span>[22] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *Advances in neural information processing systems*, 35:1100– 1113, 2022. [2](#page-1-0)
- <span id="page-8-8"></span>[23] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2022. [2](#page-1-0)
- <span id="page-8-15"></span>[24] Zhiheng Ma, Anjia Cao, Funing Yang, and Xing Wei. Curriculum dataset distillation. *arXiv preprint arXiv:2405.09150*, 2024. [2,](#page-1-0) [5,](#page-4-1) [6](#page-5-1)
- <span id="page-8-9"></span>[25] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2021. [2](#page-1-0)
- <span id="page-8-21"></span>[26] Garima Pruthi, Frederick Liu, Satyen Kale, and Mukund Sundararajan. Estimating training data influence by tracing gradient descent. *Advances in Neural Information Processing Systems*, 33:19920–19930, 2020. [5](#page-4-1)

- <span id="page-9-20"></span>[27] Ilija Radosavovic, Raj Prateek Kosaraju, Ross Girshick, Kaiming He, and Piotr Dollár. Designing network design spaces. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 10428–10436, 2020. [6](#page-5-1)
- <span id="page-9-17"></span>[28] Petru Soviany. Curriculum learning with diversity for supervised computer vision tasks. *arXiv preprint arXiv:2009.10625*, 2020. [2](#page-1-0)
- <span id="page-9-14"></span>[29] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 9390–9399, 2024. [2](#page-1-0)
- <span id="page-9-19"></span>[30] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J Gordon. An empirical study of example forgetting during deep neural network learning. *arXiv preprint arXiv:1812.05159*, 2018. [5,](#page-4-1) [6,](#page-5-1) [7](#page-6-6)
- <span id="page-9-10"></span>[31] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196– 12205, 2022. [2](#page-1-0)
- <span id="page-9-0"></span>[32] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-0) [2](#page-1-0)
- <span id="page-9-13"></span>[33] Xing Wei, Anjia Cao, Funing Yang, and Zhiheng Ma. Sparse parameterization for epitomic dataset distillation. In *NeurIPS*, 2023. [2](#page-1-0)
- <span id="page-9-2"></span>[34] Yifan Wu, Jiawei Du, Ping Liu, Yuewei Lin, Wenqing Cheng, and Wei Xu. Towards adversarially robust dataset distillation by curvature regularization. *arXiv preprint arXiv:2403.13322*, 2024. [1](#page-0-0)
- <span id="page-9-5"></span>[35] Yue Xu, Yong-Lu Li, Kaitong Cui, Ziyu Wang, Cewu Lu, Yu-Wing Tai, and Chi-Keung Tang. Distill gold from massive ores: Bi-level data pruning towards efficient dataset distillation. In *Proceedings of the European Conference on Computer Vision (ECCV)*, 2024. [2](#page-1-0)
- <span id="page-9-15"></span>[36] Zeyuan Yin and Zhiqiang Shen. Dataset distillation in large data era. *arXiv preprint arXiv:2311.18838*, 2023. [2,](#page-1-0) [6](#page-5-1)
- <span id="page-9-16"></span>[37] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2023. [2,](#page-1-0) [6](#page-5-1)
- <span id="page-9-1"></span>[38] Ruonan Yu, Songhua Liu, and Xinchao Wang. A comprehensive survey to dataset distillation. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 46(1):150–170, 2023. [1](#page-0-0)
- <span id="page-9-18"></span>[39] Dingwen Zhang, Deyu Meng, Chao Li, Lu Jiang, Qian Zhao, and Junwei Han. A self-paced multiple-instance learning framework for co-saliency detection. In *Proceedings of the IEEE international conference on computer vision*, pages 594–602, 2015. [2](#page-1-0)
- <span id="page-9-11"></span>[40] Hansong Zhang, Shikun Li, Fanzhao Lin, Weiping Wang, Zhenxing Qian, and Shiming Ge. DANCE: Dual-view distribution alignment for dataset condensation. In *Proceedings of*

*the International Joint Conference on Artificial Intelligence (IJCAI)*, 2024. [2](#page-1-0)

- <span id="page-9-6"></span>[41] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Xu Dongkuan. Accelerating dataset distillation via model augmentation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 11950–11959, 2023. [2](#page-1-0)
- <span id="page-9-7"></span>[42] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, 2021. [2,](#page-1-0) [6](#page-5-1)
- <span id="page-9-3"></span>[43] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023. [1,](#page-0-0) [2,](#page-1-0) [6](#page-5-1)
- <span id="page-9-9"></span>[44] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021. [2](#page-1-0)
- <span id="page-9-12"></span>[45] Ganlong Zhao, Guanbin Li, Yipeng Oin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7856–7865, 2023. [2](#page-1-0)
- <span id="page-9-4"></span>[46] Daquan Zhou, Kai Wang, Jianyang Gu, Xiangyu Peng, Dongze Lian, Yifan Zhang, Yang You, and Jiashi Feng. Dataset quantization. In *Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV)*, 2023. [1,](#page-0-0) [3](#page-2-5)
- <span id="page-9-8"></span>[47] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *Advances in Neural Information Processing Systems*, 35:9813–9827, 2022. [2](#page-1-0)

# A. Implement Details

In this section, we introduce more details about the implementation of CCFS. In the main results, we choose CDA as the base distillation method. We utilize its official code to synthesize  $\mathcal{D}_{distill}$  for CIFAR-10/100 and Tiny-ImageNet. We also leverage the pre-generated soft label approach for the final synthetic data as CDA. Here, we don't elaborate on details of the dataset distillation. We provide implementation details of the subsequent curriculum selection and the final evaluation below.

## A.1. CIFAR-10/100

Hyper-parameter Setting. In curriculum selection, we set the default number of curriculum phases to 3 and evenly distribute the samples to be selected among them. In each curriculum, we train a modified ResNet-18 model from scratch on the current synthetic dataset as the filter, using equal training epochs as those in the final evaluation. We use pre-calculated Forgetting scores and apply our coarseto-fine selection strategy to the training set, excluding previously selected samples. For evaluation, we train the identical ResNet-18 on the final synthetic dataset and follow the same training settings as the filter. The hyperparameter settings are shown in Table [6.](#page-10-0)

<span id="page-10-0"></span>Table 6. Hyperparameter settings on CIFAR-10/100.

| config                 | value             |
|------------------------|-------------------|
| difficulty score       | Forgetting        |
| number of curricula    | 3                 |
| optimizer              | SGD               |
| base learning rate     | 0.1               |
| momentum               | 0.9               |
| weight decay           | 5e-4              |
| learning rate schedule | cosine decay      |
| augmentation           | RandomResizedCrop |

For the hyperparameter—training epochs, we set the same training epochs for both the filter and the final evaluation model. The number of training epochs varies based on the target IPC. We assign more training epochs to smaller IPC settings, following the settings in other dataset distillation methods. Table [7](#page-10-1) shows the specific settings of the training epochs.

For the hyperparameter—batch size, We configure it based on the current size of the synthetic dataset considering its progressive growth across curriculum phases. As the size of the synthetic dataset grows, we appropriately increase the batch size in the filter training. For evaluation, we similarly set the evaluation model's training batch size based on the size of the final synthetic dataset. Table [8](#page-10-2) presents the detailed settings for the batch size.

<span id="page-10-1"></span>Table 7. Training epochs configuration on CIFAR-10/100.

| Compression Ratio | 5%  | 10% | 20% | 30% |
|-------------------|-----|-----|-----|-----|
| Training Epochs   | 500 | 500 | 250 | 200 |

<span id="page-10-2"></span>Table 8. Batch size configuration for both the filter and the evaluation training according to the size of the current on CIFAR-10/100.

| Compression Ratio | ≤ 5% | 5% - 20% | > 20% |
|-------------------|------|----------|-------|
| Batch Size        | 32   | 64       | 128   |

## A.2. Tiny-ImageNet

Hyper-parameter Setting. In curriculum selection, we set the default number of curriculum phases to 3 and evenly distribute the samples to be selected among them. In each curriculum, we train a modified ResNet-18 model from scratch on the current synthetic dataset as the filter, using equal training epochs as those in the final evaluation. We use pre-calculated Forgetting scores and apply our coarseto-fine selection strategy to the training set, excluding previously selected samples. For evaluation, we train the identical ResNet-18 on the final synthetic dataset and follow the same training settings as the filter. We uniformly set the training epochs to 100 and the batch size to 64 for both the

<span id="page-10-3"></span>filter training across curriculum phases and the final evaluation. The hyperparameter settings are shown in Table [9.](#page-10-3)

Table 9. Parameter setting on CIFAR-10/100.

| config                 | value             |
|------------------------|-------------------|
| difficulty score       | Forgetting        |
| number of curricula    | 3                 |
| optimizer              | SGD               |
| base learning rate     | 0.2               |
| momentum               | 0.9               |
| weight decay           | 1e-4              |
| learning rate schedule | cosine decay      |
| augmentation           | RandomResizedCrop |
| training epochs        | 100               |
| batch size             | 64                |

## B. Distillation Portion

The portion  $\alpha$  of  $\mathcal{D}_{distill}$  in the final synthetic dataset is another key hyperparameter. In the main table, we report the results of the best distillation portion  $\alpha$  in each setting. Here, we provide results of other  $\alpha$  settings. As shown in Figure [5,](#page-11-0) in high-IPC settings, the optimal distillation portion  $\alpha$  is typically between 0.2 and 0.4. We recommend a small distillation portion  $\alpha$  in high-IPC settings.

## C. CCFS with MTT

In the main results, we use CDA to get  $\mathcal{D}_{distill}$ . However, our curriculum selection framework is independent of the base dataset distillation method and can be applied to other dataset distillation methods. To verify the scalability of CCFS, we also provide results using MTT as the dataset distillation method. We compare them with SelMatch, which is also based on the MTT approach. We follow the same experimental setup as SelMatch to evaluate the synthetic datasets on ResNet-18. The results in Table [10](#page-11-1) demonstrate that CCFS with MTT still outperforms SelMatch across all high-IPC settings, showcasing its excellent scalability.

# D. More Experimental Results

In the ablation study, we present the results of other combinations in the selection strategy on CIFAR-100 with IPC=50 and demonstrate that the simplest-misclassified strategy is the optimal combination. Here, we provide experimental results of more datasets and more IPC settings to further validate the effectiveness of the simplestmisclassified combination. As shown in Table [11,](#page-11-2) [12](#page-11-3) and [13,](#page-11-4) the simplest-misclassified combination consistently outperforms others in all settings. This further validates the effectiveness of our coarse-to-fine selection strategy.

<span id="page-11-0"></span>Image /page/11/Figure/0 description: This figure displays three line graphs illustrating the impact of distillation portion alpha on Top-1 accuracy for different IPC (Inter-Process Communication) values across CIFAR-10, CIFAR-100, and Tiny-ImageNet datasets. The x-axis in all graphs represents the Distillation Portion alpha, ranging from 0.1 to 0.9. The y-axis represents Top-1 Accuracy in percentage. Graph (a) on CIFAR-10 shows four lines for IPC values of 250, 500, 1000, and 1500. The accuracy generally increases with alpha up to 0.4 for IPC=250, then decreases. For higher IPC values, the accuracy remains relatively high and shows a slight decrease as alpha increases. Graph (b) on CIFAR-100 shows four lines for IPC values of 25, 50, 100, and 150. Similar to graph (a), accuracy tends to increase with alpha initially and then decrease, with higher IPC values yielding better accuracy. Graph (c) on Tiny-ImageNet shows two lines for IPC values of 50 and 100. The accuracy for IPC=100 is consistently higher than for IPC=50 across all alpha values, with both showing a general downward trend as alpha increases.

<span id="page-11-1"></span>Figure 5. Impact of different distillation portion  $\alpha$  on CIFAR-10/100 and Tiny-ImageNet. We recommend a small distillation portion  $\alpha$  in high-IPC settings.

| Dataset<br>IPC<br>Ratio | CIFAR-10    |             |             |             | CIFAR-100   |             |             |             |
|-------------------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|
|                         | 25<br>5%    | 50<br>10%   | 100<br>20%  | 150<br>30%  | 25<br>5%    | 50<br>10%   | 100<br>20%  | 150<br>30%  |
| SelMatch                | 82.8        | 85.9        | 90.4        | 91.3        | 50.9        | 54.5        | 62.4        | 67.4        |
| CCFS w/ MTT             | <b>83.2</b> | <b>86.3</b> | <b>91.0</b> | <b>92.1</b> | <b>51.6</b> | <b>56.0</b> | <b>65.2</b> | <b>69.2</b> |

Table 10. Results of CCFS with MTT as the base dataset distillation method. CCFS with MTT still outperforms SelMatch across all high-IPC settings, showcasing its excellent scalability.

<span id="page-11-2"></span>

| Table 11. CIFAR-10 |            |      |        |               |      |             |
|--------------------|------------|------|--------|---------------|------|-------------|
| IPC                | classified |      |        | misclassified |      |             |
|                    | random     | hard | simple | random        | hard | simple      |
| 250                | 84.2       | 86.0 | 85.4   | 87.0          | 86.4 | <b>87.9</b> |
| 500                | 89.8       | 90.5 | 90.8   | 91.8          | 91.6 | <b>92.5</b> |
| 1000               | 91.5       | 91.8 | 91.9   | 92.6          | 92.2 | <b>93.2</b> |
| 1500               | 92.4       | 93.0 | 92.9   | 93.2          | 92.9 | <b>93.8</b> |

<span id="page-11-3"></span>

| IPC | classified |      |        | misclassified |      |        |
|-----|------------|------|--------|---------------|------|--------|
|     | random     | hard | simple | random        | hard | simple |
| 25  | 59.2       | 52.5 | 60.5   | 62.9          | 51.6 | 65.3   |
| 50  | 66.8       | 63.5 | 66.8   | 70.1          | 65.0 | 71.5   |
| 100 | 70.7       | 69.1 | 70.4   | 72.0          | 71.0 | 73.0   |
| 150 | 72.1       | 71.6 | 71.2   | 73.3          | 72.7 | 74.8   |

<span id="page-11-4"></span>

| IPC | classified |      |        | misclassified |      |        |
|-----|------------|------|--------|---------------|------|--------|
|     | random     | hard | simple | random        | hard | simple |
| 50  | 52.1       | 48.4 | 52.5   | 52.9          | 46.5 | 55.8   |
| 100 | 58.1       | 56.4 | 57.7   | 58.2          | 54.9 | 60.2   |

# E. Visualization

We present more visualizations of the synthetic datasets, including CIFAR-10 with IPC=250 (ratio=5%) and 1500 (ratio=30%) in Figure [6](#page-12-0) and [7,](#page-13-0) resp., CIFAR-100 with IPC=25 (ratio=5%) and 150 (ratio=30%) in Figure [8](#page-14-0) and [9,](#page-15-0) resp., and Tiny-ImageNet with IPC=50 (ratio=10%) and 100 (ratio=20%) in Figure [10](#page-16-0) and [11,](#page-17-0) respectively. In each visualization, we show partial images from 10 classes in the

dataset (corresponding to 10 columns). The first six rows denote the selected real images  $\mathcal{D}_{\text{real}}$ , while the last four rows correspond to the distilled images  $\mathcal{D}_{distill}$ . For  $\mathcal{D}_{real}$ , we display two samples of median difficulty per class selected at each curriculum phase. The visualizations demonstrate the progressive difficulty of selected samples across curriculum phases and show that higher IPC settings tend to select more challenging samples than lower IPC settings within the same phase.

<span id="page-12-0"></span>Image /page/12/Picture/0 description: The image displays a grid of images, organized into two main sections labeled "D\_real" and "D\_distill". The "D\_real" section is further divided into three horizontal rows, labeled "Curriculum 1", "Curriculum 2", and "Curriculum 3". Each of these rows contains a grid of 10 images, showcasing various real-world objects and animals such as airplanes, cars, cats, dogs, deer, horses, birds, and boats. The "D\_distill" section, located below "D\_real", also contains a grid of images, but these appear to be generated or synthesized, with abstract patterns and less distinct objects. This section is also organized into rows, though not explicitly labeled with curricula. The overall presentation suggests a comparison between real images and generated images, possibly in the context of machine learning or artificial intelligence model training.

Figure 6. Visualization of the synthetic dataset (CIFAR-10, IPC=250)

<span id="page-13-0"></span>Image /page/13/Picture/0 description: The image displays a grid of images organized into three rows labeled "Curriculum 1", "Curriculum 2", and "Curriculum 3". Each row contains eight columns of images. To the left of these rows, there is a bracket encompassing all three rows, labeled "D\_real". Below the "D\_real" bracket, there is another bracket encompassing the bottom four rows of images, labeled "D\_distill". The images in the top three rows (under "D\_real") appear to be clear photographs of various subjects, including airplanes, cars, animals (such as dogs, cats, deer, horses, and birds), and boats. The images in the bottom section (under "D\_distill") are abstract and distorted, appearing to be generated or processed images, with blurry shapes and colors that vaguely resemble the subjects in the top rows.

Figure 7. Visualization of the synthetic dataset (CIFAR-10, IPC=1500)

<span id="page-14-0"></span>Image /page/14/Picture/0 description: This image displays a grid of images, organized into three rows labeled "Curriculum 1", "Curriculum 2", and "Curriculum 3" on the left side. Below these rows, there is a bracket encompassing the entire grid and labeled "D\_real". Below this, another bracket encompasses the bottom half of the grid and is labeled "D\_distill". The grid itself contains numerous smaller images, predominantly featuring animals such as sunflowers, caterpillars, turtles, tigers, lions, skunks, rabbits, and hamsters. The top three rows (D\_real) appear to contain clear, distinct images of these subjects. The bottom three rows (D\_distill) show images that are significantly more abstract and distorted, with blurred colors and shapes, suggesting a generative or synthetic output.

Figure 8. Visualization of the synthetic dataset (CIFAR-100, IPC=25)

<span id="page-15-0"></span>Image /page/15/Picture/0 description: The image displays a grid of images organized into two main sections, labeled D\_real and D\_distill. The D\_real section is further divided into three curricula: Curriculum 1, Curriculum 2, and Curriculum 3. Each curriculum contains a row of images. Curriculum 1 shows a bird, a hamster, a mountain landscape, a camel, a cockroach, and a monkey. Curriculum 2 shows butterflies, a hamster eating, mushrooms, a wolf, a chair, a camel, a cockroach, and a monkey. Curriculum 3 shows butterflies, a hamster, a mushroom, a mountain landscape, a dolphin, a chair, a camel, a cockroach, and a monkey. The D\_distill section, located below D\_real, contains a grid of images that appear to be generated or modified, showing abstract patterns and distorted versions of objects like butterflies, chairs, kangaroos, and mushrooms.

Figure 9. Visualization of the synthetic dataset (CIFAR-100, IPC=150)

<span id="page-16-0"></span>Image /page/16/Picture/0 description: The image displays a grid of 80 smaller images, arranged in 8 columns and 10 rows. The grid is divided into two main sections, labeled "D\_real" and "D\_distill". The "D\_real" section occupies the top 6 rows and is further subdivided into three curricula: "Curriculum 1" (top 2 rows), "Curriculum 2" (rows 3-4), and "Curriculum 3" (rows 5-6). The "D\_distill" section comprises the bottom 4 rows. The images within the grid depict a variety of subjects, including animals (monkeys, camels, birds), food items (peppers, pretzels, pancakes, meals), objects (bottles, brooms, boats), and natural scenes. The images in the "D\_distill" section appear to be lower resolution or more abstract representations compared to those in the "D\_real" section.

Figure 10. Visualization of the synthetic dataset (Tiny-ImageNet, IPC=50)

<span id="page-17-0"></span>Image /page/17/Picture/0 description: The image displays a grid of 80 images, organized into four rows and ten columns. The left side of the grid is labeled with "D\_real" and "D\_distill". "D\_real" is further divided into three sections labeled "Curriculum 1", "Curriculum 2", and "Curriculum 3". Each section contains two rows of images. "Curriculum 1" shows images of oranges, a dog, a mushroom, people running, a spider, a hedgehog, mashed potatoes, a cougar, and a person in religious attire. "Curriculum 2" features oranges, a golden retriever, a mushroom, people in a crowd, a spider, mashed potatoes, a cougar, and a person in religious attire. "Curriculum 3" includes an orange, a dog, a mushroom, a person playing a musical instrument, a spider, mashed potatoes, a cougar, and a person in religious attire. The "D\_distill" section, located below "D\_real", contains the remaining six rows of images, which appear to be distorted or abstract versions of various subjects, including animals, food, and natural scenes.

Figure 11. Visualization of the synthetic dataset (Tiny-ImageNet, IPC=100)
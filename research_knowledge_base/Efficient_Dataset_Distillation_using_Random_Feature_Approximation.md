# Efficient Dataset Distillation using Random Feature Approximation

<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> Computer Science and Artificial Intelligence Lab (CSAIL) Massachusetts Institute of Technology (MIT) {loo, rhasani, amini, rus} @mit.edu

### Abstract

Dataset distillation compresses large datasets into smaller synthetic coresets which retain performance with the aim of reducing the storage and computational burden of processing the entire dataset. Today's best-performing algorithm, *Kernel Inducing Points* (KIP), which makes use of the correspondence between infinite-width neural networks and kernel-ridge regression, is prohibitively slow due to the exact computation of the neural tangent kernel matrix, scaling  $\tilde{O}(|S|^2)$ , with  $|S|$  being the coreset size. To improve this, we propose a novel algorithm that uses a random feature approximation (RFA) of the Neural Network Gaussian Process (NNGP) kernel, which reduces the kernel matrix computation to  $O(|S|)$ . Our algorithm provides at least a 100-fold speedup over KIP and can run on a single GPU. Our new method, termed an RFA Distillation (RFAD), performs competitively with KIP and other dataset condensation algorithms in accuracy over a range of large-scale datasets, both in kernel regression and finite-width network training. We demonstrate the effectiveness of our approach on tasks involving model interpretability and privacy preservation.<sup>[1](#page-0-0)</sup>

# 1 Introduction

Coreset algorithms aim to summarize large datasets into significantly smaller datasets that still accurately represent the full dataset on downstream tasks [\[Jubran et al.,](#page-11-0) [2019\]](#page-11-0). There are myriad applications of these smaller datasets including speeding up model training [\[Mirza](#page-12-1)[soleiman et al.,](#page-12-1) [2020\]](#page-12-1), reducing catastrophic forgetting [\[Aljundi et al.,](#page-10-0) [2019,](#page-10-0) [Rebuffi et al.,](#page-12-2) [2017,](#page-12-2) [Borsos et al.,](#page-10-1) [2020\]](#page-10-1), and enhancing interpretability [\[Kim et al.,](#page-11-1) [2016,](#page-11-1) [Bien and Tibshirani,](#page-10-2) [2011\]](#page-10-2). While most coreset selection techniques aim to select representative data points from the dataset, recent work has looked at generating synthetic data points instead, a process known as dataset distillation [\[Wang et al.,](#page-13-0) [2018,](#page-13-0) [Bohdal et al.,](#page-10-3)

Image /page/0/Figure/6 description: The image displays a comparison between two methods, KIP and RFAD (ours), across two metrics: Time per iteration and Accuracy. For Time per iteration, KIP takes 460s, while RFAD takes 2.4s, indicating RFAD is 191.6 times faster. For Accuracy, KIP achieves 67%, and RFAD achieves 73.1%, representing a +6.1% gain. The right side of the image shows an example distilled dataset from CelebA, featuring images labeled 'Class Male' and 'Class Female'. Below these images, it states that training on these 2 images yields 92% classification accuracy.

Image /page/0/Figure/7 description: Figure 1 shows that RFAD provides over 100-fold speedup compared to the state-of-the-art algorithm Kernel-Inducing Points (KIP) [Nguyen et al., 2021a], while also exceeding its performance on CIFAR-10. The figure also includes an example of distilled synthetic sets, with one image per class.

[2020,](#page-10-3) [Sucholutsky and Schonlau,](#page-13-1) [2019,](#page-13-1) [Zhao et al.,](#page-13-2) [2021,](#page-13-2) [Zhao and Bilen,](#page-13-3) [2021b,](#page-13-3) [Nguyen et al.,](#page-12-3) [2021b\]](#page-12-3). These synthetic datasets have the benefit of using continuous gradient-based optimization techniques rather than combinatorial methods and are not limited to the set of images and labels given by the dataset, providing added flexibility and performance.

<span id="page-0-0"></span> $^1$ Code is available at https://github.com/yolky/RFAD

<sup>36</sup>th Conference on Neural Information Processing Systems (NeurIPS 2022).

A large variety of applications benefit from obtaining an efficient dataset distillation algorithm. For instance, Kernel methods [\[Vinyals et al.,](#page-13-4) [2016,](#page-13-4) [Kaya and Bilge,](#page-11-2) [2019,](#page-11-2) [Snell et al.,](#page-13-5) [2017,](#page-13-5) [Ghorbani](#page-10-4) [et al.,](#page-10-4) [2020,](#page-10-4) [Refinetti et al.,](#page-12-4) [2021\]](#page-12-4) usually demand a large support set in order to generate good prediction performance at inference. This can be facilitated by an efficient dataset distillation pipeline. Moreover, distilling a synthetic version of sensitive data helps preserve privacy; a support set can be provided to an end-user for the downstream applications without disclosure of data. Lastly, for resource-hungry applications such as continual learning [\[Borsos et al.,](#page-10-1) [2020\]](#page-10-1), neural architecture search [\[Shleifer and Prokop,](#page-13-6) [2019\]](#page-13-6) and automated machine learning [\[Hutter et al.,](#page-11-3) [2019\]](#page-11-3), generation of a support-set on which we can fit models efficiently is very helpful.

Recently, a dataset distillation method called Kernel-Inducing Points (KIP) [\[Nguyen et al.,](#page-12-0) [2021a](#page-12-0)[,b\]](#page-12-3) showed great performance in neural network classification tasks. KIP uses Neural Tangent Kernel (NTK) ridge-regression to *exactly* compute the output states of an infinite-width neural network trained on the support set. Although the method established the state-of-the-art for dataset distillation in terms of accuracy, the computational complexity of KIP is very high due to the exact calculation of the NTK. The algorithm, therefore, has limited applicability.

In this paper, we build on the prior work on KIP and develop a new algorithm for dataset distillation called RFAD, which has similar accuracy and significantly better performance than KIP. The key insight is to introduce a new kernel inducing point method that improves complexity from  $O(|S|^2)$ (where |S| is the support-set size) to  $O(|S|)$ . To this end, we make three **major contributions**:

I. We develop RFAD, a fast, accurate, and scalable algorithm for dataset distillation in neural network classification tasks.

II. We improve the time performance of KIP [\[Nguyen et al.,](#page-12-0) [2021a,](#page-12-0)[b\]](#page-12-3) by over two orders of magnitude while retaining or improving its accuracy. This speedup comes from leveraging a random-feature approximation of the Neural Network Gaussian Process (NNGP) kernel by instantiating random neural networks.

III. We show the effectiveness of RFAD in efficient dataset distillation tasks, enhancing model interpretability and privacy preservation.

# 2 Background and Related Work

Coresets and Dataset Distillation. Coresets are a subset of data that ensure models trained on them show competitive performance compared to models trained directly on data. Standard coreset selection algorithms use importance sampling to find coresets [\[Har-Peled and Mazumdar,](#page-10-5) [2004,](#page-10-5) [Lucic et al.,](#page-11-4) [2017,](#page-11-4) [Cohen et al.,](#page-10-6) [2017\]](#page-10-6). Besides random selection methods, inspired by catastrophic forgetting [\[Toneva et al.,](#page-13-7) [2019\]](#page-13-7) and mean-matching (adding samples to the coreset to match the mean of the original dataset) [\[Chen et al.,](#page-10-7) [2010,](#page-10-7) [Rebuffi et al.,](#page-12-2) [2017,](#page-12-2) [Castro et al.,](#page-10-8) [2018,](#page-10-8) [Belouadah and](#page-10-9) [Popescu,](#page-10-9) [2020,](#page-10-9) [Scholkopf et al.,](#page-13-8) [1999\]](#page-13-8), new algorithms have been introduced. An overview of how coresets work on point approximation is provided in [Phillips](#page-12-5) [\[2016\]](#page-12-5)

More recently, aligned with coreset selection methods, new algorithms have been developed to distill a synthetic dataset from a given dataset, such that fitting to this synthetic set provides performance on par with training on the original dataset [\[Wang et al.,](#page-13-0) [2018\]](#page-13-0). To this end, these dataset condensation (or distillation) algorithms use gradient matching [\[Zhao et al.,](#page-13-2) [2021,](#page-13-2) [Maclaurin et al.,](#page-12-6) [2015,](#page-12-6) [Lorraine](#page-11-5) [et al.,](#page-11-5) [2020\]](#page-11-5), utilize differentiable siamese augmentation [\[Zhao and Bilen,](#page-13-3) [2021b\]](#page-13-3), and matching distributions [\[Zhao and Bilen,](#page-13-9) [2021a\]](#page-13-9). Dataset distillation has also been applied to the labels rather than images [\[Bohdal et al.,](#page-10-3) [2020\]](#page-10-3). Recently, a novel algorithm called Kernel Inducing Points (KIP) [\[Nguyen et al.,](#page-12-0) [2021a,](#page-12-0)[b\]](#page-12-3) has been introduced that performs very well on distilling synthetic sets by using neural tangent kernel ridge-regression (KRR). KIP, similar to other algorithms, is computationally expensive. Here, we propose a new method to significantly improve its complexity.

Infinite Width Neural Networks. Single-layer infinite-width randomly initialized neural networks correspond to Gaussian Processes [\[Neal,](#page-12-7) [1996\]](#page-12-7), allowing for closed-form exact training of Bayesian neural networks for regression. Recently, this has been extended to deep fully-connected networks [\[Lee et al.,](#page-11-6) [2018,](#page-11-6) [de G. Matthews et al.,](#page-10-10) [2018\]](#page-10-10), convolutional networks [\[Novak et al.,](#page-12-8) [2019,](#page-12-8) [Garriga-](#page-10-11)[Alonso et al.,](#page-10-11) [2019\]](#page-10-11), attention-based networks [\[Hron et al.,](#page-11-7) [2020\]](#page-11-7), and even to arbitrary neural architectures [\[Yang,](#page-13-10) [2019\]](#page-13-10), with the corresponding GP kernel being the NNGP Kernel. Likewise, for infinite-width neural networks trained with gradient descent, the training process simplifies dramati-

cally, corresponding to kernel ridge regression when trained with MSE loss with the corresponding kernel being the Neural Tangent Kernel (NTK) [\[Jacot et al.,](#page-11-8) [2018,](#page-11-8) [Arora et al.,](#page-10-12) [2019,](#page-10-12) [Loo et al.,](#page-11-9) [2022\]](#page-11-9). These two kernels are closely related, as the NNGP kernel forms the leading term of the NTK kernel, representing the effect of the final layer weights. Calculation of kernel entries typically scales with  $O(HWD)$  for conv nets, with H, W being the image height and width, D the network depth, and  $O(H^2W^2D)$  for architectures with global average pooling [\[Arora et al.,](#page-10-12) [2019\]](#page-10-12). This, combined with the necessity of computing and inverting the  $N \times N$  kernel matrix for kernel ridge regression, typically make these methods intractable for large datasets [\[Snelson and Ghahramani,](#page-13-11) [2006,](#page-13-11) [Titsias,](#page-13-12) [2009\]](#page-13-12).

Random Feature methods. Every kernel corresponds to a dot product for some feature map:  $k(x, x') = \phi(x)^T \phi(x')$ . Random feature methods aim to approximate the feature vector with a finite-dimensional random feature vector, the most notable example being Random Fourier Features [\[Rahimi and Recht,](#page-12-9) [2007\]](#page-12-9). Typically, this limits the rank of the kernel matrix, enabling faster matrix inversion and allowing for scaling kernel methods to large datasets. Recently, these random feature methods have been used to speed up NTKs and NNGPs [\[Zandieh et al.,](#page-13-13) [2021,](#page-13-13) [Novak et al.,](#page-12-10) [2022,](#page-12-10) [2019\]](#page-12-8) at inference or for neural architecture search [\[Peng et al.,](#page-12-11) [2020\]](#page-12-11). In this work, we focus on the NNGP approximation described in [Novak et al.](#page-12-8) [\[2019\]](#page-12-8), as it only requires network forward passes and is model agnostic, allowing for flexible usage across different architectures without more complex machinery needed to calculate the approximation, unlike those found in [Zandieh et al.](#page-13-13) [\[2021\]](#page-13-13), [Novak](#page-12-10) [et al.](#page-12-10) [\[2022\]](#page-12-10).

## 3 Algorithm Setup and Design

In this section, we first provide a high-level background on the KIP algorithm. We then sequentially outline our modifications leading to the RFAD algorithm.

### 3.1 KIP Revisit

The Kernel-Inducing Point algorithm [\[Nguyen et al.,](#page-12-0) [2021a,](#page-12-0)[b\]](#page-12-3), or KIP, is a dataset distillation technique that uses the NTK kernel ridge-regression correspondence to compute exactly the outputs of an infinite-width neural network trained on the support set, bypassing the need to ever compute gradients or back-propagate on any finite network. Let  $X_T, y_T$  correspond to the images and one-hot vector labels on the training dataset and let  $X_S, y_S$  be the corresponding images and labels for the support set, which we aim to optimize. We have the outputs of a trained neural network as  $f(X_T) = K_{TS}(K_{SS} + \lambda I)^{-1}y_S$ , with K being the kernel matrices calculated using the NTK kernel, with  $T \times S$  or  $S \times S$  entries, for  $K_{TS}$  and  $K_{SS}$ , respectively.  $\lambda$  is a small regularization parameter. KIP then optimizes  $L_{MSE} = ||y_T - f(X_T)||_2^2$  directly. The key bottleneck is the computation of these kernel matrices, requiring  $O(TS \cdot HWD)$  time and memory, necessitating the use of hundreds of GPUs working in parallel. Additionally, the use of the MSE loss is suboptimal.

### 3.2 Replacing the NTK Kernel with an NNGP Kernel

We first replace the NTK used in the kernel regression of KIP with an NNGP kernel. While this change alone would yield a speed up, as the NNGP kernel is less computationally intensive to compute [\[Novak et al.,](#page-12-12) [2020\]](#page-12-12), we primarily do this because the NNGP kernel admits a simple random feature approximation, with advantages described later in this section. We first justify the appropriateness of this modification.

Firstly, we denote that in the computation of NTK  $(\Theta)$  and NNGP  $(K)$  forms the leading term, as shown in Table 1 in Appendix D of [\[Novak et al.,](#page-12-12) [2020\]](#page-12-12) which outlines the NTK and NNGP kernel computation rules for various layers of a neural network. For fully connected (FC) layers, which is the typical final layer in neural network architectures, the remaining terms are suppressed by a matrix of expected derivatives with respect to activations,  $\dot{K}$ , as observed by the recursion yielded from the computation of the NTK for an FC network [\[Novak et al.,](#page-12-12) [2020\]](#page-12-12):  $\Theta^l = K^l + \dot{K}^l \odot \Theta^{l-1}$ . For ReLU activations, the entries in this derivative matrix are upper bounded by 1, so the remaining terms must have a decaying contribution. We verify that our algorithms still provide good performance under the NTK and for finite networks trained with gradient descent, justifying this approximation.

#### Algorithm 1 Dataset distillation with NNGP random features

<span id="page-3-0"></span>**Require:** Training set and labels  $X_T, y_T$ , Randomly initialized coreset and labels  $X_S, y_S$ , Random network count N, Random network output dimension M, Batch size  $|B|$ , Random network initialization distribution,  $p(\theta)$ , Regularization coefficient,  $\lambda$ , Learning rate  $\eta$ , while loss not converged do Sample batch from the training set  $X_B, y_B \sim p(X_T, y_T)$ Sample N random networks each with output dimension M from  $p(\theta)$ :  $\theta_1, \dots, \theta_N \sim p(\theta)$ Compute random features for batch with random nets:  $\hat{\Phi}(X_B) \leftarrow \frac{1}{\sqrt{NM}}[f_{\theta_1}(X_B),...,f_{\theta_N}(X_B)]^T \in \mathbb{R}^{|NM| \times |B|}$ Compute random features for support set with random nets:  $\hat{\Phi}(X_S) \leftarrow \frac{1}{\sqrt{NM}}[f_{\theta_1}(X_S),...,f_{\theta_N}(X_S)]^T \in \mathbb{R}^{|NM| \times |S|}$ Compute kernel matrices:  $\hat{K}_{BS} \leftarrow \hat{\Phi}(X_B)^T \hat{\Phi}(X_S)$  $\hat{K}_{SS} \leftarrow \hat{\Phi}(X_S)^T \hat{\Phi}(X_S)$ Calculate trained network output on batch:  $\hat{y}_B \leftarrow \hat{K}_{BS}(\hat{K}_{SS} + \lambda I_{|S|})^{-1}y_S$ Calculate loss:  $\mathcal{L} = \mathcal{L}(y_B, \hat{y}_B)$ Update coreset:  $X_S \leftarrow X_S - \eta \frac{\partial \mathcal{L}}{\partial X_S}, y_S \leftarrow y_S - \eta \frac{\partial \mathcal{L}}{\partial y_S}$ end while

### 3.3 Replacing NNGP with an Empirical NNGP

When we sample from a Gaussian process  $f \sim \mathcal{GP}(0, K)$ , it suggests a natural finite feature map corresponding to scaled draws from the GP:  $\hat{\phi}(x) = \frac{1}{\sqrt{x}}$  $\frac{1}{N}[f_1(x),...,f_N(x)]^T$ . For most GPs, this insight is not relevant, as sampling from a GP typically requires a Cholesky decomposition of the kernel matrix, requiring its computation in the first place [\[Rasmussen and Williams,](#page-12-13) [2006\]](#page-12-13). However, for NNGP we can generate approximate samples of  $f$  by instantiating random neural networks,  $f_i(x) = f_{\theta_i}(x), \theta_i \sim p(\theta)$ , for some initialization distribution  $p(\theta)$ . Moreover, with a given neural network, we can define  $f_i$  to be a vector of dimension M by having a network with multiple output heads, meaning that with N networks, we have  $N$  M features. For our purposes, we typically have  $N = 8$ ,  $M = 4096$ , giving 32768 total features. For the convolutional architectures we consider, this corresponds to  $C = 256$  convolutional channels per layer. Even with this relatively large number of features, we still see a significant computation speedup over exact calculation.

To sample f ∼ GP(0, K), we would have to instantiate random *infinite* width neural nets, whereas, in practice, we can only sample finite ones. This discrepancy incurs an  $O(1/C)$  bias to our kernel matrix entries, with C being the width-relevant parameter (i.e., convolutional channels) [\[Yaida,](#page-13-14) [2020\]](#page-13-14). However, we have a  $O(1/(NC))$  variance of the mean of the random features [\[Daniely et al.,](#page-10-13) [2016\]](#page-10-13), meaning that in practice, the variance dominates the computation over bias. This has been noted empirically in [Novak et al.](#page-12-8) [\[2019\]](#page-12-8), and we verify that the finite-width bias does not significantly affect performance in appendix [I,](#page-18-0) showing that we can achieve reasonable performance with as little as *one* convolution channel.

The time cost of computing these random features is linear in the training set and coreset size,  $|T|, |S|$ . With the relatively low cost of matrix multiplication, this results in the construction of the kernel matrices  $K_{TS}$  and  $K_{SS}$  having  $O(|T| + |S|)$  and  $O(|S|)$ , time complexity, respectively, as opposed to  $O(|T||S|)$  and  $O(|S|^2)$  with KIP. Noting that the cost of matrix inversion is relatively small compared to random feature construction, our total runtime is reduced to linear in the coreset size. We empirically verify this linear time complexity in section 4.1 and additionally provide a more detailed discussion in appendix [C.](#page-15-0)

### 3.4 Loss Function in dataset distillation

We denoted earlier that  $L_{MSE}$  is not well suited for dataset distillation settings. In particular, there are two key problems:

Over-influence of already correctly classified data points. Consider two-way classification, with the label 1 corresponding to the positive class and  $-1$  corresponding to the negative class. Let  $x_1$ and  $x_2$  be items in the training set whose labels are both 1. Let  $f_{KRR}(x) = K_{x,S}(K_{SS} + \lambda I)^{-1}y_S$ be the KRR output on x given our support set  $X_S$ . If  $f_{KRR}(x_1) = 5$  and  $f_{KRR}(x_2) = -1$ , then the

|               |               | Fixed Labels                                       |                                                    |                                                    | Learned Labels                                     |                                                    |                                                    |
|---------------|---------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|
|               | Img/Cls       | DC                                                 | <b>DSA</b>                                         | <b>KIP</b>                                         | RFAD (ours)                                        | <b>KIP</b>                                         | RFAD (ours)                                        |
| <b>MNIST</b>  | 1<br>10<br>50 | $91.7 \pm 0.5$<br>$97.4 + 0.2$<br>$98.8 \pm 0.1$   | $88.7 \pm 0.6$<br>$97.8 \pm 0.1$<br>$99.2 + 0.1$   | $95.2 + 0.2$<br>$98.4 \pm 0.0$<br>$99.1 \pm 0.0$   | $96.7 + 0.2$<br>$99.0 + 0.1$<br>$99.1 \pm 0.0$     | $97.3 \pm 0.1$<br>$99.1 + 0.1$<br>$99.4 \pm 0.1$   | $97.2 \pm 0.2$<br>$99.1 \pm 0.0$<br>$99.1 \pm 0.0$ |
| Fashion-MNIST | 10<br>50      | $70.5 \pm 0.6$<br>$82.3 \pm 0.4$<br>$83.6 \pm 0.4$ | $70.6 \pm 0.6$<br>$84.6 \pm 0.3$<br>$88.7 \pm 0.2$ | $78.9 \pm 0.2$<br>$87.6 \pm 0.1$<br>$90.0 \pm 0.1$ | $81.6 \pm 0.6$<br>$90.0 \pm 0.1$<br>$91.3 \pm 0.1$ | $82.9 \pm 0.2$<br>$91.0 \pm 0.1$<br>$92.4 \pm 0.1$ | $84.6 \pm 0.2$<br>$90.3 \pm 0.2$<br>$91.4 \pm 0.1$ |
| <b>SVHN</b>   | 1<br>10<br>50 | $31.2 + 1.4$<br>$76.1 \pm 0.6$<br>$82.3 \pm 0.3$   | $27.5 \pm 1.4$<br>$79.2 + 0.5$<br>$84.4 + 0.4$     | $48.1 \pm 0.7$<br>$75.8 \pm 0.1$<br>$81.3 \pm 0.2$ | $51.4 + 1.3$<br>$77.2 + 0.3$<br>$81.8 \pm 0.2$     | $64.3 \pm 0.4$<br>$81.1 \pm 0.5$<br>$84.3 + 0.1$   | $57.4 \pm .8$<br>$78.2 \pm 0.5$<br>$82.4 \pm 0.1$  |
| CIFAR-10      | 1<br>10<br>50 | $28.3 \pm 0.5$<br>$44.9 \pm 0.5$<br>$53.9 \pm 0.5$ | $28.8 \pm 0.7$<br>$52.1 \pm 0.5$<br>$60.6 \pm 0.5$ | $59.1 \pm 0.4$<br>$67.0 \pm 0.4$<br>$71.7 \pm 0.2$ | $61.1 \pm 0.7$<br>$73.1 \pm 0.1$<br>$76.1 \pm 0.3$ | $64.7 \pm 0.2$<br>$75.6 \pm 0.2$<br>$80.6 \pm 0.1$ | $61.4 \pm 0.8$<br>$73.7 \pm 0.2$<br>$76.6 \pm 0.3$ |
| CIFAR-100     | 10            | $12.8 \pm 0.3$<br>$25.2 \pm 0.3$                   | $13.9 \pm 0.3$<br>$32.3 \pm 0.3$                   | $31.8 \pm 0.3$<br>$46.0\pm0.2$                     | $36.0 \pm 0.4$<br>$44.9 \pm 0.2$                   | $34.9 \pm 0.1$<br>$49.5 \pm 0.3$                   | $44.1 \pm 0.1$<br>$46.8 \pm 0.2$                   |

<span id="page-4-0"></span>Table 1: Kernel distillation results on five datasets with varying support set sizes. Bolded numbers indicate the best performance with fixed labels, and underlined numbers indicate the best performance with learned labels. Note that DC and DSA use fixed labels.  $(n = 4)$ 

resulting MSE error on  $x_1$  and  $x_2$  would be 16 and 4, respectively. Notably,  $x_1$  incurs a larger loss and results in a larger gradient on  $X<sub>S</sub>$  than  $x<sub>2</sub>$ , despite being correctly classified and  $x<sub>2</sub>$  being incorrectly classified. In the heavily constrained dataset distillation setting, fitting both data points simultaneously is not possible, leading to underfitting of the data in terms of classification in order to better fit already-correctly labeled data points in terms of regression.

Unclear probabilistic interpretation of MSE for classification. This prevents regression from being used directly in calibration-sensitive environment, necessitating the use of transformation functions in tasks such as GP classification [\[Williams and Barber,](#page-13-15) [1998,](#page-13-15) [Milios et al.,](#page-12-14) [2018\]](#page-12-14).

Based on these two issues, we adopt : Platt scaling [\[Platt,](#page-12-15) [2000\]](#page-12-15), by applying a cross entropy loss to the labels instead of an MSE one:  $\mathcal{L}_{\text{plat}} = x$ -entropy $(y_T, f(X_T)/\tau)$ , where  $\tau$  is a positive learned temperature scaling parameter. Unlike typical Platt scaling, we learn  $\tau$  jointly with our support set instead of post-hoc tuning on a separate validation set.  $f(X_T)$  is still calculated using the same KRR formula. Accordingly, this corresponds to training a network using MSE loss, but at inference, scaling the outputs by  $\tau^{-1}$  and applying a softmax to get a categorical distribution. Unlike typical GP classification, we ignore the variance of our predictions, taking only the mean instead.

The combination of these three changes, namely, using the NNGP kernel instead of NTK, applying a random-feature approximation of NNGP, and Platt-scaling result in our RFAD algorithm, which is given in algorithm [1.](#page-3-0)

# 4 Experiments with RFAD

Here, we perform experiments to evaluate the performance of RFAD in dataset distillation tasks.

Benchmarks. We applied our algorithm to five datasets: MNIST, FashionMNIST, SVHN, CIFAR-10 and CIFAR-100 [\[LeCun et al.,](#page-11-10) [2010,](#page-11-10) [Xiao et al.,](#page-13-16) [2017,](#page-13-16) [Netzer et al.,](#page-12-16) [2011,](#page-12-16) [Krizhevsky et al.,](#page-11-11) [2009\]](#page-11-11), distilling the datasets to coresets with 1, 10 or 50 images per class.

Network Structure and Training Setup. Similar to previous work on dataset distillation, we used standard ConvNet architectures with three convolutional layers with average pooling and ReLU activations [\[Zhao et al.,](#page-13-2) [2021,](#page-13-2) [2020,](#page-14-0) [Nguyen et al.,](#page-12-3) [2021b\]](#page-12-3). Similar to KIP [\[Nguyen et al.,](#page-12-3) [2021b\]](#page-12-3), we do not use instancenorm layers because of the lack of an infinite-width analog. During training, we used  $N = 8$  random models, each with  $C = 256$  convolutional channels per layer, and during testtime, we evaluated the datasets using the exact NNGP kernel using the neural-tangents library [\[Novak](#page-12-12) [et al.,](#page-12-12) [2020\]](#page-12-12). We consider both the fixed and learned label configurations, with Platt scaling applied and no data augmentation. We used the regularized Zero Component Analysis (ZCA) preprocessing

|              | Img/Cls | DC/DSA         | KIP to NN        | <b>RFAD</b> to NN |
|--------------|---------|----------------|------------------|-------------------|
|              | 1       | $91.7 \pm 0.5$ | $90.1 + 0.1$     | $94.4 \pm 1.5^*$  |
| <b>MNIST</b> | 10      | $97.8 \pm 0.1$ | $97.5 + 0.0$     | $98.5 + 0.1^*$    |
|              | 50      | $99.2 + 0.1$   | $98.3 + 0.1$     | $98.8 + 0.1$      |
| Fashion-     | 1       | $70.6 + 0.6$   | $73.5 \pm 0.5^*$ | $78.6 + 1.3^*$    |
| <b>MNIST</b> | 10      | $84.6 + 0.3$   | $86.8 + 0.1$     | $87.0 + 0.5$      |
|              | 50      | $88.7 + 0.2$   | $88.0 + 0.1^*$   | $88.8 + 0.4$      |
|              | 1       | $31.2 \pm 1.4$ | $57.3\pm0.1^{*}$ | $52.2 + 2.2^*$    |
| <b>SVHN</b>  | 10      | $79.2 + 0.5$   | $75.0 + 0.1$     | $74.9 \pm 0.4$    |
|              | 50      | $84.4 + 0.4$   | $80.5 + 0.1$     | $80.9 \pm 0.3^*$  |
|              | 1       | $28.8 \pm 0.7$ | $49.9 + 0.2$     | $53.6 + 1.2^*$    |
| $CIFAR-10$   | 10      | $52.1 + 0.5$   | $62.7 + 0.3$     | $66.3 + 0.5^*$    |
|              | 50      | $60.6 + 0.5$   | $68.6 + 0.2$     | $71.1 + 0.4$      |
| $CIFAR-100$  | 1       | $13.9 \pm 0.3$ | $15.7 \pm 0.2$   | $26.3 + 1.1^*$    |
|              | 10      | $32.3 \pm 0.3$ | $28.3 \pm 0.1$   | $33.0 \pm 0.3^*$  |
|              |         |                |                  |                   |

<span id="page-5-1"></span>Table 2: Performance of finite networks trained with gradient descent on DC/DSA, KIP, and RFAD distilled images.  $*$  denotes the result was obtained using learned labels. (n = 12)

for SVHN, CIFAR-10, and CIFAR-100, to improve KRR performance for color image datasets [\[Shankar et al.,](#page-13-17) [2020,](#page-13-17) [Nguyen et al.,](#page-12-3) [2021b\]](#page-12-3). More details are available in appendix [D.](#page-16-0)

Baselines. We compare RFAD to recently developed advanced dataset distillation algorithms such as: KIP [\[Nguyen et al.,](#page-12-0) [2021a](#page-12-0)[,b\]](#page-12-3), Dataset Condensation with gradient matching (DC) [\[Zhao et al.,](#page-13-2) [2021\]](#page-13-2), and differentiable Siamese augmentation (DSA) [\[Zhao and Bilen,](#page-13-3) [2021b\]](#page-13-3).

Table [1](#page-4-0) summarizes the results. We observe that in the fixed label configuration, our method outperforms other models in almost every dataset. In particular, it outperforms KIP by up to  $6.1\%$  in the CIFAR-10 10 img/cls setting. We attribute this gain primarily to the use of Platt scaling. RFAD falls slightly behind KIP with learned labels. While this could partially be explained because we did not apply data augmentation, which marginally elevated performance for KIP on some datasets [\[Nguyen et al.,](#page-12-3) [2021b\]](#page-12-3), we hypothesize that the performance difference is caused by the increased gradient variance associated with the random feature method. Nevertheless, in all experiments, RFAD is at least two orders of magnitude faster than KIP (Figure [2\)](#page-5-0).

### 4.1 Time Savings during training

Next, we evaluated the time efficiency of RFAD. fig. [2](#page-5-0) shows the time taken per training iteration on CIFAR-10 over coreset sizes and the number of models, N used to evaluate the empirical NNGP kernel during training. Each training iteration contains 5120 examples from the training set. fig. [2](#page-5-0) depicts that the time taken by RFAD is linear in both the number of models used during training and in the coreset size, validating the time complexity described above. We expect that for larger coreset sizes, the matrix inversion will begin to dominate due to its cubic complexity, but for small coreset sizes, the computation of the kernel matrix dominates the computation time.

In the right-hand side plot in fig. [2](#page-5-0) we show the same plot in log-scale, compared to KIP. For KIP, we used a batch size of 5000, and rather than measuring the time taken, we use the calculation provided in appendix B of [\[Nguyen et al.,](#page-12-3) [2021b\]](#page-12-3), which describes the running time of the algorithm. We observe evidently that even for the modest coreset sizes, the quadratic time complexity of computing the exact kernel matrix in KIP results in it being multiple orders of magnitude slower than our RFAD. Both KIP and RFAD converge in between 3000-15000 training iterations, resulting in times between 1-14hrs for RFAD and several hundred GPU hours for KIP, depending on the coreset size dataset, and when the early stopping condition is triggered.

Image /page/5/Figure/8 description: The image contains two plots side-by-side, both titled "Training time per iteration for RFAD vs KIP at varying coreset sizes on CIFAR-10". The x-axis for both plots is labeled "Coreset Size (Img/Cls)" and ranges from 0 to 100. The left plot has a y-axis labeled "Time per iteration (s)" and ranges from 0 to 4.0. It shows four lines representing RFAD with N=1 (blue), N=2 (orange), N=4 (green), and N=8 (red). The right plot has a y-axis labeled "Log-time per iteration (s)" and uses a logarithmic scale, ranging from 10^0 to 10^3. It shows the same four lines for RFAD, plus a purple line representing KIP. The legend in the right plot clarifies the colors for each line: blue for RFAD N=1, orange for RFAD N=2, green for RFAD N=4, red for RFAD N=8, and purple for KIP.

<span id="page-5-0"></span>Figure 2: Time per training iteration for RFAD and KIP with varying number of models, N. Left: Linear plot of time. Right: Logarithmic time for training iteration. RFAD achieves over two-ordersof-magnitude speedup compared KIP per training iteration while converging with a similar number of iterations.

Image /page/6/Figure/0 description: The image displays five line graphs, each representing the accuracy of two methods, NNGP and NTK, across different datasets (MNIST, Fashion-MNIST, SVHN, CIFAR-10, and CIFAR-100) as a function of coreset size (Img/Cls). The x-axis for all graphs represents the coreset size, with values of 1, 10, and 50. The y-axis represents accuracy in percentage. In the MNIST graph, NNGP achieves accuracies of approximately 96.7%, 99%, and 99.2% at coreset sizes 1, 10, and 50 respectively, while NTK achieves 94%, 98.5%, and 99% at the same sizes. For Fashion-MNIST, NNGP shows accuracies of 81.5%, 90.5%, and 91.5%, and NTK shows 75.5%, 86%, and 86.5%. The SVHN graph shows NNGP at 50.5%, 76%, and 78.5%, and NTK at 50%, 72%, and 77%. CIFAR-10 data shows NNGP at 61%, 70%, and 77%, and NTK at 60.5%, 67%, and 75%. Finally, CIFAR-100 data indicates NNGP at 35.5%, 38.5%, and 45%, and NTK at 31.5%, 35.5%, and 45%. Shaded regions around the lines indicate variability or confidence intervals.

Figure 3: NNGP to NTK transfer performance on RFAD distilled images. The blue line indicates the performance of RFAD distilled images evaluated on NNGP. The orange line shows the same images evaluated using NTK. Despite being trained using the empirical NNGP kernel, these images still perform well on the NTK kernel, losing at most a few percentage points.  $(n = 4)$ 

Image /page/6/Figure/2 description: The image displays three line graphs side-by-side, illustrating empirical NNGP inference accuracy for CIFAR-10 with 10 images per class and fixed labels. The y-axis represents accuracy in percentage, ranging from approximately 40% to 75%. The x-axis appears to represent some measure of model complexity or training progress, though it is not explicitly labeled. Each graph shows four colored lines representing different values of 'C': blue for C=128, orange for C=256, green for C=512, and red for C=1024. A dotted black line labeled 'Exact' is present in each graph, indicating a baseline or theoretical maximum accuracy, which is around 74%. Black dots are plotted on the colored lines in the first and third graphs, likely indicating specific points of interest. All lines generally show an increasing trend in accuracy, with higher values of 'C' tending to achieve higher accuracies, especially at the beginning of the curves. The shaded areas around the colored lines suggest some measure of uncertainty or variance.

<span id="page-6-1"></span>Figure 4: Empirical NNGP kernel performance at test-time with a varying number of models used to compute the empirical NNGP kernel and number of convolutional channels per model.  $(n = 5)$ 

Number of features

<span id="page-6-0"></span> $10<sup>3</sup>$ 

 $10<sup>4</sup>$ 

 $10<sup>1</sup>$ 

Time taken (s)

 $10<sup>2</sup>$ 

 $10<sup>0</sup>$ 

### 4.2 NTK Kernel and Finite Network Transfer

 $\circ$ 

 $10$ 

20

Number of models

30

 $10<sup>2</sup>$ 

One of the key elements of the RFAD algorithm is the replacement of NTK with the empirical NNGP kernel. While we argued earlier that the two should exhibit a similar performance given their similar formalism, in this section, we verify this claim experimentally. We evaluated our distilled coresets obtained from our RFAD algorithm in two different transfer scenarios. In the first setting, at test time, we used an NTK kernel instead of the NNGP kernel. In the second setting, we trained a finite-width network with gradient descent on the distilled datasets obtained via RFAD. Similar to [\[Nguyen et al.,](#page-12-3) [2021b\]](#page-12-3), we used a 1024-width finite network for our finite-transfer results since it better mimics the infinite width setting that corresponds to the NTK.

Remarkably, as shown in fig. [3,](#page-6-0) in most datasets, these coresets suffer little to no performance drop when evaluated using NTK compared to the exact NNGP kernel, despite being trained using the empirical NNGP kernel. The largest performance gap is 8% on SVHN with 10 images per class, and in some datasets, notably CIFAR-100, 10 img/cls evaluating using the NTK kernel outperforms NNGP. This suggests that either the exact NNGP kernel or the random feature NNGP kernel could potentially be used as a cheaper approximation for the exact NTK kernel.

table [2](#page-5-1) shows the resulting finite network transfer when training with gradient descent on our coresets. Our images appear to have the best performance in finite-network transfer, outperforming KIP in almost all benchmarks and the DC/DSA algorithms in many, despite DC/DCA being designed specifically for finite-width networks. We attribute this performance gain over KIP primarily to two tricks we used during training. Firstly, we applied centering, which, rather than training a typical network  $f_{\theta}(x)$ , we instead train a network with its output at initialization subtracted:  $f_{\theta}(x) - f_{\theta_0}(x)$ .

This has been shown empirically to speed up the convergence of finite-width networks by reducing the bias caused by the finite-width initialization while still preserving the NTK [\[Lee et al.,](#page-11-12) [2020,](#page-11-12) [Hu et al.,](#page-11-13) [2020\]](#page-11-13). We find that for these small datasets, this modification significantly improves performance. The second trick is label scaling; we scale the target labels by a factor  $\alpha > 1$ :  $\mathcal{L}_{\alpha} = ||f_{\theta}(x) - \alpha y||_2^2 / \alpha^2$ , and at inference divide the model's outputs by  $\alpha$ . Note that this does not

Image /page/7/Figure/0 description: This image contains two line graphs side-by-side, both plotting test accuracy (%) against the number of models used during training (N). The left graph is titled "Fashion-MNIST" and the right graph is titled "CIFAR-10". Both graphs display six lines, each representing a different combination of class count (1/cls, 10/cls, 50/cls) and calibration method (platt, mse). The legend indicates that solid lines with circular markers represent the "platt" method, and dashed lines with circular markers represent the "mse" method. For "Fashion-MNIST", the "platt" lines show accuracies around 80%, 89%, and 90.5% for 1, 10, and 50 classes respectively, with slight increases as N increases. The "mse" lines are generally lower, around 74%, 85%, and 87.5% for 1, 10, and 50 classes respectively. For "CIFAR-10", the "platt" lines show accuracies around 56%, 68%, and 73.5% for 1, 10, and 50 classes respectively, with slight increases as N increases. The "mse" lines are generally lower, around 54%, 66%, and 70% for 1, 10, and 50 classes respectively. Shaded regions around the lines indicate confidence intervals.

<span id="page-7-0"></span>Figure 5: Objective function sensitivity.  $(n = 4)$  to use Platt scaling and the number of models during training (N) for Fashion-MNIST and CIFAR-10

affect the infinite-width setting, as in KRR, the output is linear w.r.t. the support set labels. Ablations of these changes are in appendix [F.](#page-18-1)

### 4.3 Empirical NNGP Performance at Inference

To validate the efficacy of our method, we evaluated our distilled coresets using features from random networks as opposed to the exact kernel. We varied the width of individual networks between 128 and 1024 channels and the number of models between 1 and 32. fig. [4](#page-6-1) shows the resulting classification accuracy on the CIFAR-10 dataset with 10 images/class. The black dot represents the configuration we used during training: 8 models, each with width 256 (More ablations are provided in appendix [I\)](#page-18-0). We conclude that the random feature method, for all network widths, is able to reach close to the exact NNGP kernel performance (dotted line) if a sufficient number of models are used. Interestingly, the performance is almost entirely dependent on the total number of features (proportional to  $C \times N$ , with  $C$  being the number of convolutional channels) and not the width of individual networks, suggesting that the finite-width bias associated with random finite networks is minimal.

In appendix [I,](#page-18-0) we show that this can be taken to the extreme, with 70% accuracy achieved with a network with a *single* convolutional channel. These results corroborate the findings of [\[Novak et al.,](#page-12-8) [2019\]](#page-12-8), which first proposed this random feature method, where they found, like us, that performance was almost entirely determined by the total feature count.

Platt scaling. We performed ablations on the use of the cross-entropy loss and the number of models used during training. We reran our algorithm on CIFAR-10 and Fashion-MNIST, using either 1, 2, 4, or 8 models during training, using MSE loss or cross-entropy loss. fig. [5](#page-7-0) shows the resulting performance of these configurations. Evidently, using a cross-entropy loss results in substantial performance gains, even as much as 8% as with Fashion-MNIST with one img/cls.

## <span id="page-7-1"></span>5 RFAD Application I: Interpretability

Large datasets contribute to the difficulty of understanding deep learning models. In this paper, we consider interpretability in the sense of the influence of individual training examples on network predictions [\[Hasani et al.,](#page-11-14) [2019,](#page-11-14) [Lechner et al.,](#page-11-15) [2020,](#page-11-15) [Wang et al.,](#page-13-18) [2022\]](#page-13-18). One method of understanding this effect is the use of influence functions, which seek to answer the following counterfactual question: which item in the training set, if left out, would change the model's prediction the most [\[Hampel,](#page-10-14) [1974,](#page-10-14) [Koh and Liang,](#page-11-16) [2017,](#page-11-16) [Kabra et al.,](#page-11-17) [2015\]](#page-11-17)? For deep networks, this can only be answered approximately. This is because retraining a network on copies of the training set with individual items left out is computationally intractable. One solution is to use kernel ridge regression on a small support set. We can recompute the KRR on the kernel matrices with the ith individual coreset element removed, with  $K_{x,S\setminus i} K_{S\setminus i,S\setminus i}$  being the resulting kernel matrices with the *i*th row/column corresponding to the ith coreset entry removed.

In particular, let  $p(y_{\text{test}} = c|S)$  be the probability prediction (computed by applying Platt scaling) of an example belonging to class c computed on the entire coreset, S. Let  $p(y_{\text{test}} = c | S \setminus i)$  be the same prediction calculated with the *i*th coreset element removed. We define the influence score,  $I_i$  of coreset element *i* on  $x_{\text{test}}$  as  $\sum_{c \le C} |p(y_{\text{test}} = c|S) - p(y_{\text{test}} = c|S \setminus i)|$ . Taking the top values of  $I_i$ yields the most relevant examples.

Image /page/8/Figure/0 description: The image displays a comparison of a machine learning model's predictions on a test image of a deer, which was incorrectly predicted as a bird, and a test image of a dog, which was correctly predicted as a dog. For the incorrect deer prediction, the model's most relevant images in the core set are a bird, followed by four deer images, and then two more deer images. For the correct dog prediction, the model's most relevant images in the core set are two dog images, followed by two cat images. The image also shows the most relevant images in the training set for both cases. For the deer test image, the most relevant training images are a deer, a bird, a deer, a bird, and another bird. For the dog test image, the most relevant training images are a dog, a dog, a dog, a dog, and another dog.

<span id="page-8-0"></span>Figure 6: The most relevant images for an incorrect (top row) and correct (bottom row) prediction on CIFAR-10. The most relevant coreset images are picked based on the coreset influence score  $I$ , and for their training set, the training influence score J. These metrics are fast to compute and result in semantically meaningful explanations for these two predictions.

While this method provides a simple way of gaining insights into how a prediction depends on the coreset, it does not provide insight into how this prediction comes from the original training set which produced the coreset. The method can be extended to accommodate this. Heuristically, we conjecture that two elements are similar if their predictions depend on the same elements in the coreset. We compute  $p(y_j = c | S)$  and  $p(y_j = c | S \setminus i)$  for every element j in the training set and i in the coreset. Then, we define its influence embedding as  $z_{i,c}^j = p(y_j = c|S) - p(y_j = c|S \setminus i), z^j \in R^{|S| \times |C|}$ . This way,  $z^{j}$  defines the sensitivity of a training datapoint prediction on the coreset. We compute the same embedding for a test datapoint  $z<sup>test</sup>$ , and to compare data points we use cosine similarity,  $J_{\text{test},j} = \cos(z^{\text{test}}, z^j)$ . Values of  $z^j$  can be precomputed for the training set, typically in a few minutes for CIFAR-10, allowing for relatively fast queries, in contrast to the more expensive Hessian-inverse vector product required in [Koh and Liang](#page-11-16) [\[2017\]](#page-11-16), which is costly to compute and difficult to store.

fig. [6](#page-8-0) shows the results of this algorithm applied to CIFAR-10 with 50 img/cls for an incorrectly and correctly predicted image. In both cases, the resulting queries are visually similar to the test data point. One could use this information to not only explain a single incorrect prediction but to understand harmful items in their test set, or where more data needs to be collected.

As a second application of RFAD, we create coresets that contain no human-understandable information from the source dataset yet still retain high test performance. [\[Nguyen et al.,](#page-12-0) [2021a\]](#page-12-0) proposed the concept of a  $\rho$ -corrupted coreset: A fraction  $\rho$  of the coresets elements are completely independent of the source dataset. Practically, for our algorithm, this means initializing the coreset with random noise and keeping a random  $\rho$  fraction of the pixels kept at their initialization. We term this algorithm  $RFAD<sub>o</sub>$ .

Image /page/8/Figure/5 description: The image displays a grid of images showing classification accuracy for male and female subjects under varying "Inversion Correlation Ratios" (ρ). The top row is labeled "Male" and the bottom row is labeled "Female". Each row has four columns, corresponding to ρ = 0, ρ = 0.4, ρ = 0.8, and ρ = 0.95. Above each image in the top row are classification accuracies: 92.0 ± 0.2%, 91.0 ± 0.8%, 89.1 ± 0.4%, and 86.9 ± 1.4%. The images themselves appear to be stylized faces, with the clarity decreasing as ρ increases. At ρ = 0, the faces are relatively clear. At ρ = 0.4, the faces are still discernible but more distorted and noisy. At ρ = 0.8 and ρ = 0.95, the images are highly pixelated and resemble random noise, with any facial features becoming almost entirely obscured.

<span id="page-8-1"></span>Increasing Corruption Ratio,  $\rho$ 

Figure 7: CelebA distilled datasets for male/female classification with 1 image per class at varying corruption ratios. At  $\rho = 0$ , the distilled images are very interpretable, but at  $\rho = 0.95$ , the images look like white noise, despite achieving 86.9% accuracy on the classification task.

Adding noise to gradient updates of the inputs of a network can be shown to give differentially private guarantees [\[Abadi et al.,](#page-10-15) [2016\]](#page-10-15). While our scheme does not provide the same guarantees, we note the following two privacy-preserving properties of RFADρ: firstly, the distillation process is irreversible: there are many datasets for which a distilled dataset provides zero loss. Secondly, if the true data distribution assigns a low probability to images of white noise, then for high values of  $ρ$ , this guarantees that the distilled dataset stays far away in  $L_2$  norm from real data points, since  $ρ$ fraction of the pixels are stuck at their initialization. This means that a distilled  $\text{RFAD}_{\rho}$  dataset will not recreate any real points in the training set.

## 6 RFAD Application II: Privacy

We applied  $RFAD<sub>o</sub>$  on CIFAR-10, and CelebA faces datasets. For CIFAR-10 we distilled the standard 10-class classification task, with corruption ratios taking values of [0, 0.2, 0.4, 0.8, 0.9], with 1, 10 or 50 images per class. For CelebA, we performed male/female binary classification with corruption ratios between 0 and 0.95 with 1, 10, or 50 samples per class. fig. [8](#page-9-0) show the resulting performance.

For CIFAR-10, even at corruption ratios of 0.9, we are able to achieve 40.6% accuracy with one sample per class, far above the natural baseline of 16.1% ([\[Nguyen et al.,](#page-12-3) [2021b\]](#page-12-3) table A1). For CelebA, we achieve 81% accuracy with only two images, one male and one female, with 95% of the pixels in the image being random noise.

Image /page/9/Figure/3 description: The image displays two line graphs side-by-side, both plotting accuracy (%) against corruption ratio (ρ). The left graph is titled "CIFAR-10" and shows three lines representing different class ratios: "1/cls" (blue), "10/cls" (orange), and "50/cls" (green). The accuracy for all classes decreases as the corruption ratio increases, with the "50/cls" line generally showing the highest accuracy and the "1/cls" line showing the lowest. The right graph is titled "CelebA" and shows two lines, "1/cls" (blue) and "10/cls" (orange). The accuracy for both lines remains relatively high and stable for lower corruption ratios, then drops sharply as the corruption ratio approaches 1.0. The "10/cls" line consistently shows higher accuracy than the "1/cls" line in this graph.

<span id="page-9-0"></span>Figure 8: Performance of RFAD on CIFAR-10 and CelebA classification with varying support set sizes and corruption ratios. Performance degrades very gradually as noise is increased, still achieving high performance with 90% corruption.  $(n = 3)$ 

We additionally visualize the distilled images for the male and female classes in the CelebA distillation task with one image per class in fig. [7](#page-8-1) at varying corruption ratios. While the image initially contains visually interpretable features with  $\rho = 0$ , they quickly devolve into pure noise at  $\rho = 0.95$ .

# 7 Conclusions

We proposed RFAD, a dataset distillation algorithm that provides a 100-fold speedup over the current state-of-the-art KIP while retaining accuracy. The speedup is primarily due to the use of the approximate NNGP kernel as opposed to the exact NTK one, reducing the time complexity from  $\overline{O(|S|^2})$  to  $O(|S|)$ . The success of the approximation here, combined with the similarity between the NTK and NNGP kernels, suggests the random network NNGP approximation as an efficient method for algorithms where the exact computation of the NNGP or NTK kernel is infeasible. We analyzed our method comprehensively and showed its effectiveness, and proposed two applications in model interpretability and privacy preservation. With this new tool, we hope that future work could begin to use Neural Tangent Kernel as an algorithmic design tool in addition to its current theoretical use for neural network analysis. Lastly, RFAD has the following limitations:

Use of instancenorm. In practice, we found that our datasets distilled without instancenorm do not transfer well to finite networks with it. Conversely, if we use random networks with instancenorm in RFAD, these transfer to finite networks with instancenorm but not to ones without or the NNGP kernel. This suggests that the features used by networks with/without instancenorm differ, making it difficult to distill datasets that perform well on both. We discuss this further in appendix [G.](#page-18-2)

Overfitting in dataset distillation. On Platt scaling, we argued that the heavily constrained nature of dataset distillation leads to underfitting of the training set when using an MSE-style loss in KIP, and we verified the efficacy of using a Platt loss instead. However, we observed that in simple datasets, such as MNIST, or with large coresets relative to the data, such as CIFAR-100 with 10 images per class, we could overfit to the dataset. We found that these distilled datasets were able to achieve near 100% classification accuracy on the training set, meaning that it was distilled perfectly in terms of the Platt-loss. This implies that adding more images would not improve performance. Thus we hypothesize that using a Platt loss would be detrimental if the compression ratio is low.

## Acknowledgements

This research has been funded in part by the Office of Naval Research Grant Number Grant N00014- 18-1-2830 and the J. P. Morgan AI Research program. We are very grateful.

# **References**

- <span id="page-10-15"></span>M. Abadi, A. Chu, I. Goodfellow, H. B. McMahan, I. Mironov, K. Talwar, and L. Zhang. Deep learning with differential privacy. In *Proceedings of the 2016 ACM SIGSAC Conference on Computer and Communications Security*, CCS '16, page 308–318, New York, NY, USA, 2016. Association for Computing Machinery. ISBN 9781450341394. doi: 10.1145/2976749.2978318. URL <https://doi.org/10.1145/2976749.2978318>.
- <span id="page-10-0"></span>R. Aljundi, M. Lin, B. Goujaud, and Y. Bengio. Gradient based sample selection for online continual learning. In *NeurIPS*, 2019.
- <span id="page-10-12"></span>S. Arora, S. S. Du, W. Hu, Z. Li, R. Salakhutdinov, and R. Wang. On exact computation with an infinitely wide neural net. In *NeurIPS*, 2019.
- <span id="page-10-9"></span>E. Belouadah and A. Popescu. Scail: Classifier weights scaling for class incremental learning. In *The IEEE Winter Conference on Applications of Computer Vision*, 2020.
- <span id="page-10-2"></span>J. Bien and R. Tibshirani. Prototype selection for interpretable classification. *The Annals of Applied Statistics*, 5(4):2403–2424, 2011.
- <span id="page-10-3"></span>O. Bohdal, Y. Yang, and T. Hospedales. Flexible dataset distillation: Learn labels instead of images. *Neural Information Processing Systems Workshop*, 2020.
- <span id="page-10-1"></span>Z. Borsos, M. Mutnỳ, and A. Krause. Coresets via bilevel optimization for continual learning and streaming. *NeurIPS*, 2020.
- <span id="page-10-8"></span>F. M. Castro, M. J. Marín-Jiménez, N. Guil, C. Schmid, and K. Alahari. End-to-end incremental learning. In *Proceedings of the European Conference on Computer Vision (ECCV)*, pages 233–248, 2018.
- <span id="page-10-7"></span>Y. Chen, M. Welling, and A. Smola. Super-samples from kernel herding. *The Twenty-Sixth Conference Annual Conference on Uncertainty in Artificial Intelligence*, 2010.
- <span id="page-10-16"></span>L. Chizat, E. Oyallon, and F. R. Bach. On lazy training in differentiable programming. In *NeurIPS*, 2019.
- <span id="page-10-6"></span>M. B. Cohen, C. Musco, and C. Musco. Input sparsity time low-rank approximation via ridge leverage score sampling. In *Proceedings of the Twenty-Eighth Annual ACM-SIAM Symposium on Discrete Algorithms*, pages 1758–1777. SIAM, 2017.
- <span id="page-10-13"></span>A. Daniely, R. Frostig, and Y. Singer. Toward deeper understanding of neural networks: The power of initialization and a dual view on expressivity. In D. Lee, M. Sugiyama, U. Luxburg, I. Guyon, and R. Garnett, editors, *Advances in Neural Information Processing Systems*, volume 29. Curran Associates, Inc., 2016. URL [https://proceedings.neurips.cc/paper/2016/file/](https://proceedings.neurips.cc/paper/2016/file/abea47ba24142ed16b7d8fbf2c740e0d-Paper.pdf) [abea47ba24142ed16b7d8fbf2c740e0d-Paper.pdf](https://proceedings.neurips.cc/paper/2016/file/abea47ba24142ed16b7d8fbf2c740e0d-Paper.pdf).
- <span id="page-10-10"></span>A. G. de G. Matthews, J. Hron, M. Rowland, R. E. Turner, and Z. Ghahramani. Gaussian process behaviour in wide deep neural networks. In *International Conference on Learning Representations*, 2018. URL <https://openreview.net/forum?id=H1-nGgWC->.
- <span id="page-10-11"></span>A. Garriga-Alonso, C. E. Rasmussen, and L. Aitchison. Deep convolutional networks as shallow gaussian processes. In *International Conference on Learning Representations*, 2019. URL <https://openreview.net/forum?id=Bklfsi0cKm>.
- <span id="page-10-4"></span>B. Ghorbani, S. Mei, T. Misiakiewicz, and A. Montanari. When do neural networks outperform kernel methods? *arXiv preprint arXiv:2006.13409*, 2020.
- <span id="page-10-14"></span>F. R. Hampel. The influence curve and its role in robust estimation. *Journal of the American Statistical Association*, 69(346):383–393, 1974. ISSN 01621459. URL [http://www.jstor.](http://www.jstor.org/stable/2285666) [org/stable/2285666](http://www.jstor.org/stable/2285666).
- <span id="page-10-5"></span>S. Har-Peled and S. Mazumdar. On coresets for k-means and k-median clustering. In *Proceedings of the thirty-sixth annual ACM symposium on Theory of computing*, pages 291–300, 2004.

- <span id="page-11-14"></span>R. Hasani, A. Amini, M. Lechner, F. Naser, R. Grosu, and D. Rus. Response characterization for auditing cell dynamics in long short-term memory networks. In *2019 International Joint Conference on Neural Networks (IJCNN)*, pages 1–8. IEEE, 2019.
- <span id="page-11-7"></span>J. Hron, Y. Bahri, J. Sohl-Dickstein, and R. Novak. Infinite attention: Nngp and ntk for deep attention networks. In *ICML*, 2020.
- <span id="page-11-13"></span>W. Hu, Z. Li, and D. Yu. Simple and effective regularization methods for training on noisily labeled data with generalization guarantee. In *International Conference on Learning Representations*, 2020. URL <https://openreview.net/forum?id=Hke3gyHYwH>.
- <span id="page-11-3"></span>F. Hutter, L. Kotthoff, and J. Vanschoren. *Automated machine learning: methods, systems, challenges*. Springer Nature, 2019.
- <span id="page-11-8"></span>A. Jacot, F. Gabriel, and C. Hongler. Neural tangent kernel: Convergence and generalization in neural networks. In *Advances in Neural Information Processing Systems*, 2018.
- <span id="page-11-0"></span>I. Jubran, A. Maalouf, and D. Feldman. Introduction to coresets: Accurate coresets. *CoRR*, abs/1910.08707, 2019. URL <http://arxiv.org/abs/1910.08707>.
- <span id="page-11-17"></span>M. Kabra, A. Robie, and K. Branson. Understanding classifier errors by examining influential neighbors. *2015 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 3917–3925, 2015.
- <span id="page-11-2"></span>M. Kaya and H. Bilge. Deep metric learning: A survey. *Symmetry*, 11:1066, 08 2019. doi: 10.3390/sym11091066.
- <span id="page-11-1"></span>B. Kim, R. Khanna, and O. Koyejo. Examples are not enough, learn to criticize! criticism for interpretability. In *Proceedings of the 30th International Conference on Neural Information Processing Systems*, NIPS'16, page 2288–2296, Red Hook, NY, USA, 2016. Curran Associates Inc. ISBN 9781510838819.
- <span id="page-11-16"></span>P. W. Koh and P. Liang. Understanding black-box predictions via influence functions. In *Proceedings of the 34th International Conference on Machine Learning - Volume 70*, ICML'17, page 1885–1894. JMLR.org, 2017.
- <span id="page-11-11"></span>A. Krizhevsky, G. Hinton, et al. Learning multiple layers of features from tiny images. Technical report, Citeseer, 2009.
- <span id="page-11-15"></span>M. Lechner, R. Hasani, A. Amini, T. A. Henzinger, D. Rus, and R. Grosu. Neural circuit policies enabling auditable autonomy. *Nature Machine Intelligence*, 2(10):642–652, 2020.
- <span id="page-11-10"></span>Y. LeCun, C. Cortes, and C. Burges. Mnist handwritten digit database. *ATT Labs [Online]. Available: http://yann.lecun.com/exdb/mnist*, 2, 2010.
- <span id="page-11-6"></span>J. Lee, J. Sohl-dickstein, J. Pennington, R. Novak, S. Schoenholz, and Y. Bahri. Deep neural networks as gaussian processes. In *International Conference on Learning Representations*, 2018. URL <https://openreview.net/forum?id=B1EA-M-0Z>.
- <span id="page-11-12"></span>J. Lee, S. Schoenholz, J. Pennington, B. Adlam, L. Xiao, R. Novak, and J. Sohl-Dickstein. Finite versus infinite neural networks: an empirical study. In H. Larochelle, M. Ranzato, R. Hadsell, M. F. Balcan, and H. Lin, editors, *Advances in Neural Information Processing Systems*, volume 33, pages 15156–15172. Curran Associates, Inc., 2020. URL [https://proceedings.neurips.cc/](https://proceedings.neurips.cc/paper/2020/file/ad086f59924fffe0773f8d0ca22ea712-Paper.pdf) [paper/2020/file/ad086f59924fffe0773f8d0ca22ea712-Paper.pdf](https://proceedings.neurips.cc/paper/2020/file/ad086f59924fffe0773f8d0ca22ea712-Paper.pdf).
- <span id="page-11-9"></span>N. Loo, R. Hasani, A. Amini, and D. Rus. Evolution of neural tangent kernels under benign and adversarial training. In *Advances in Neural Information Processing Systems*, 2022.
- <span id="page-11-5"></span>J. Lorraine, P. Vicol, and D. Duvenaud. Optimizing millions of hyperparameters by implicit differentiation. In *International Conference on Artificial Intelligence and Statistics*, pages 1540–1552. PMLR, 2020.
- <span id="page-11-4"></span>M. Lucic, M. Faulkner, A. Krause, and D. Feldman. Training gaussian mixture models at scale via coresets. *The Journal of Machine Learning Research*, 18(1):5885–5909, 2017.

- <span id="page-12-6"></span>D. Maclaurin, D. Duvenaud, and R. Adams. Gradient-based hyperparameter optimization through reversible learning. In *International Conference on Machine Learning*, pages 2113–2122, 2015.
- <span id="page-12-14"></span>D. Milios, R. Camoriano, P. Michiardi, L. Rosasco, and M. Filippone. Dirichlet-based gaussian processes for large-scale calibrated classification. In *Proceedings of the 32nd International Conference on Neural Information Processing Systems*, pages 6008–6018, 2018.
- <span id="page-12-1"></span>B. Mirzasoleiman, J. A. Bilmes, and J. Leskovec. Coresets for data-efficient training of machine learning models. In *Proceedings of the 37th International Conference on Machine Learning, ICML 2020, 13-18 July 2020, Virtual Event*, volume 119 of *Proceedings of Machine Learning Research*, pages 6950–6960. PMLR, 2020.
- <span id="page-12-7"></span>R. M. Neal. *Bayesian Learning for Neural Networks*. Springer-Verlag, Berlin, Heidelberg, 1996. ISBN 0387947248.
- <span id="page-12-16"></span>Y. Netzer, T. Wang, A. Coates, A. Bissacco, B. Wu, and A. Y. Ng. Reading digits in natural images with unsupervised feature learning. *Google Research*, 2011.
- <span id="page-12-0"></span>T. Nguyen, Z. Chen, and J. Lee. Dataset meta-learning from kernel ridge-regression. In *International Conference on Learning Representations*, 2021a. URL [https://openreview.net/forum?id=](https://openreview.net/forum?id=l-PrrQrK0QR) [l-PrrQrK0QR](https://openreview.net/forum?id=l-PrrQrK0QR).
- <span id="page-12-3"></span>T. Nguyen, R. Novak, L. Xiao, and J. Lee. Dataset distillation with infinitely wide convolutional networks. In *Thirty-Fifth Conference on Neural Information Processing Systems*, 2021b. URL <https://openreview.net/forum?id=hXWPpJedrVP>.
- <span id="page-12-8"></span>R. Novak, L. Xiao, Y. Bahri, J. Lee, G. Yang, D. A. Abolafia, J. Pennington, and J. Sohl-dickstein. Bayesian deep convolutional networks with many channels are gaussian processes. In *International Conference on Learning Representations*, 2019. URL [https://openreview.net/forum?id=](https://openreview.net/forum?id=B1g30j0qF7) [B1g30j0qF7](https://openreview.net/forum?id=B1g30j0qF7).
- <span id="page-12-12"></span>R. Novak, L. Xiao, J. Hron, J. Lee, A. A. Alemi, J. Sohl-Dickstein, and S. S. Schoenholz. Neural tangents: Fast and easy infinite neural networks in python. In *International Conference on Learning Representations*, 2020. URL <https://openreview.net/forum?id=SklD9yrFPS>.
- <span id="page-12-10"></span>R. Novak, J. Sohl-Dickstein, and S. S. Schoenholz. Fast finite width neural tangent kernel. In K. Chaudhuri, S. Jegelka, L. Song, C. Szepesvári, G. Niu, and S. Sabato, editors, *International Conference on Machine Learning, ICML 2022, 17-23 July 2022, Baltimore, Maryland, USA*, volume 162 of *Proceedings of Machine Learning Research*, pages 17018–17044. PMLR, 2022. URL <https://proceedings.mlr.press/v162/novak22a.html>.
- <span id="page-12-11"></span>D. Peng, D. S. Park, J. Lee, J. Sohl-dickstein, and Y. Cao. Towards nngp-guided neural architecture search. In *ArXiv*, 2020.
- <span id="page-12-5"></span>J. M. Phillips. Coresets and sketches. *arXiv preprint arXiv:1601.00617*, 2016.
- <span id="page-12-15"></span>J. Platt. Probabilistic outputs for support vector machines and comparisons to regularized likelihood methods. *Adv. Large Margin Classif.*, 10, 06 2000.
- <span id="page-12-9"></span>A. Rahimi and B. Recht. Random features for large-scale kernel machines. In *Proceedings of the 20th International Conference on Neural Information Processing Systems*, pages 1177–1184, 2007.
- <span id="page-12-13"></span>C. E. Rasmussen and C. K. I. Williams. *Gaussian processes for machine learning.* Adaptive computation and machine learning. MIT Press, 2006. ISBN 026218253X.
- <span id="page-12-2"></span>S.-A. Rebuffi, A. Kolesnikov, G. Sperl, and C. H. Lampert. icarl: Incremental classifier and representation learning. *2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 5533–5542, 2017.
- <span id="page-12-4"></span>M. Refinetti, S. Goldt, F. Krzakala, and L. Zdeborová. Classifying high-dimensional gaussian mixtures: Where kernel methods fail and neural networks succeed. *arXiv preprint arXiv:2102.11742*, 2021.

- <span id="page-13-8"></span>B. Scholkopf, S. Mika, C. Burges, P. Knirsch, K.-R. Muller, G. Ratsch, and A. Smola. Input space versus feature space in kernel-based methods. *IEEE Transactions on Neural Networks*, 10(5): 1000–1017, 1999. doi: 10.1109/72.788641.
- <span id="page-13-17"></span>V. Shankar, A. Fang, W. Guo, S. Fridovich-Keil, J. Ragan-Kelley, L. Schmidt, and B. Recht. Neural kernels without tangents. In *ICML*, pages 8614–8623, 2020. URL [http://proceedings.mlr.](http://proceedings.mlr.press/v119/shankar20a.html) [press/v119/shankar20a.html](http://proceedings.mlr.press/v119/shankar20a.html).
- <span id="page-13-6"></span>S. Shleifer and E. Prokop. Using small proxy datasets to accelerate hyperparameter search. *arXiv preprint arXiv:1906.04887*, 2019.
- <span id="page-13-5"></span>J. Snell, K. Swersky, and R. Zemel. Prototypical networks for few-shot learning. In *Advances in neural information processing systems*, pages 4077–4087, 2017.
- <span id="page-13-11"></span>E. Snelson and Z. Ghahramani. Sparse gaussian processes using pseudo-inputs. In *Advances in neural information processing systems*, pages 1257–1264, 2006.
- <span id="page-13-1"></span>I. Sucholutsky and M. Schonlau. Soft-label dataset distillation and text dataset distillation. *arXiv preprint arXiv:1910.02551*, 2019.
- <span id="page-13-12"></span>M. Titsias. Variational learning of inducing variables in sparse gaussian processes. In *Artificial Intelligence and Statistics*, pages 567–574, 2009.
- <span id="page-13-7"></span>M. Toneva, A. Sordoni, R. T. d. Combes, A. Trischler, Y. Bengio, and G. J. Gordon. An empirical study of example forgetting during deep neural network learning. *ICLR*, 2019.
- <span id="page-13-4"></span>O. Vinyals, C. Blundell, T. Lillicrap, D. Wierstra, et al. Matching networks for one shot learning. In *Advances in neural information processing systems*, 2016.
- <span id="page-13-0"></span>T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-13-18"></span>T.-H. Wang, W. Xiao, T. Seyde, R. Hasani, and D. Rus. Interpreting neural policies with disentangled tree representations. *arXiv preprint arXiv:2210.06650*, 2022.
- <span id="page-13-15"></span>C. K. I. Williams and D. Barber. Bayesian classification with gaussian processes. *IEEE Trans. Pattern Anal. Mach. Intell.*, 20(12):1342–1351, dec 1998. ISSN 0162-8828. doi: 10.1109/34.735807.
- <span id="page-13-16"></span>H. Xiao, K. Rasul, and R. Vollgraf. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-13-14"></span>S. Yaida. Non-Gaussian processes and neural networks at finite widths. In J. Lu and R. Ward, editors, *Proceedings of The First Mathematical and Scientific Machine Learning Conference*, volume 107 of *Proceedings of Machine Learning Research*, pages 165–192. PMLR, 20–24 Jul 2020. URL <https://proceedings.mlr.press/v107/yaida20a.html>.
- <span id="page-13-10"></span>G. Yang. Tensor programs i: Wide feedforward or recurrent neural networks of any architecture are gaussian processes. In *NeurIPS 2019*, December 2019.
- <span id="page-13-13"></span>A. Zandieh, I. Han, H. Avron, N. Shoham, C. Kim, and J. Shin. Scaling neural tangent kernels via sketching and random features. In A. Beygelzimer, Y. Dauphin, P. Liang, and J. W. Vaughan, editors, *Advances in Neural Information Processing Systems*, 2021. URL [https://openreview.](https://openreview.net/forum?id=vIRFiA658rh) [net/forum?id=vIRFiA658rh](https://openreview.net/forum?id=vIRFiA658rh).
- <span id="page-13-9"></span>B. Zhao and H. Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021a.
- <span id="page-13-3"></span>B. Zhao and H. Bilen. Dataset condensation with differentiable siamese augmentation. *arXiv preprint arXiv:2102.08259*, 2021b.
- <span id="page-13-2"></span>B. Zhao, K. R. Mopuri, and H. Bilen. Dataset condensation with gradient matching. In *International Conference on Learning Representations*, 2021. URL [https://openreview.net/forum?id=](https://openreview.net/forum?id=mSAKhLYLSsl) [mSAKhLYLSsl](https://openreview.net/forum?id=mSAKhLYLSsl).

- <span id="page-14-0"></span>S. Zhao, Z. Liu, J. Lin, J.-Y. Zhu, and S. Han. Differentiable augmentation for data-efficient gan training. *Neural Information Processing Systems*, 2020.
- <span id="page-14-1"></span>J. Zhuang, T. Tang, Y. Ding, S. Tatikonda, N. Dvornek, X. Papademetris, and J. S. Duncan. Adabelief optimizer: Adapting stepsizes by the belief in observed gradients. *arXiv preprint arXiv:2010.07468*, 2020.

## Checklist

1. For all authors...

- (a) Do the main claims made in the abstract and introduction accurately reflect the paper's contributions and scope? [Yes]
- (b) Did you describe the limitations of your work? [Yes]
- (c) Did you discuss any potential negative societal impacts of your work? [N/A]
- (d) Have you read the ethics review guidelines and ensured that your paper conforms to them? [Yes]
- 2. If you are including theoretical results...
  - (a) Did you state the full set of assumptions of all theoretical results? [N/A]
  - (b) Did you include complete proofs of all theoretical results? [N/A]
- 3. If you ran experiments...
  - (a) Did you include the code, data, and instructions needed to reproduce the main experimental results (either in the supplemental material or as a URL)? [Yes]
  - (b) Did you specify all the training details (e.g., data splits, hyperparameters, how they were chosen)? [Yes]
  - (c) Did you report error bars (e.g., with respect to the random seed after running experiments multiple times)? [Yes]
  - (d) Did you include the total amount of compute and the type of resources used (e.g., type of GPUs, internal cluster, or cloud provider)? [Yes]
- 4. If you are using existing assets (e.g., code, data, models) or curating/releasing new assets...
  - (a) If your work uses existing assets, did you cite the creators? [Yes]
  - (b) Did you mention the license of the assets? [N/A]
  - (c) Did you include any new assets either in the supplemental material or as a URL? [N/A]
  - (d) Did you discuss whether and how consent was obtained from people whose data you're using/curating? [N/A]
  - (e) Did you discuss whether the data you are using/curating contains personally identifiable information or offensive content? [N/A]
- 5. If you used crowdsourcing or conducted research with human subjects...
  - (a) Did you include the full text of instructions given to participants and screenshots, if applicable? [N/A]
  - (b) Did you describe any potential participant risks, with links to Institutional Review Board (IRB) approvals, if applicable? [N/A]
  - (c) Did you include the estimated hourly wage paid to participants and the total amount spent on participant compensation? [N/A]

# Appendix

## A Code

The code is available at https://github.com/yolky/RFAD

## B Notations

| Symbol | Meaning                                          |
|--------|--------------------------------------------------|
| H      | Input image height in pixels                     |
| W      | Input image width in pixels                      |
| D      | Network Depth                                    |
| C      | Number of convolutional channels (network width) |
| N      | Number of models used during training            |
| M      | Number of network features, proportional to C    |
| T      | Training set size                                |
| B      | Training batch size                              |
| S      | Support set/coreset size                         |

Table 3: Notation for variables used throughout the paper. In some cases, C also refers to number of classes in the dataset (section [5\)](#page-7-1), however it is clear from context.

<span id="page-15-0"></span>

## C Time Complexity Discussion

Computing the approximate kernel matrices for RFAD consists of three main steps: random feature calculation, random feature matrix multiplication, and kernel matrix inversion. KIP, in comparison consists of two main steps: kernel matrix computation, and kernel matrix inversion. In practice, random feature matrix multiplication and kernel matrix inversion require negligible amounts of time for our coreset sizes, so we instead focus on the slowest part for RFAD and KIP: random feature calculation and kernel matrix computation, respectively. We discuss the runtime per training iteration of our algorithm for RFAD and KIP, assuming training batch size  $|B|$  and coreset size |S|. Additionally, we split the discussion into the  $|B| \leq |S|$  and  $|B| > |S|$  regimes, as the exact complexity differs between these settings.

**Case 1: RFAD with**  $|B| \leq |S|$ :  $O(|S|)$  random feature computation dominates, while the matrix computation (multiplying feature vectors together) is cheap. The resulting complexity is effectively  $O(|S|)$ .

**Case 2: KIP with**  $|B| < |S|$ :  $O(|S|^2)$  computation of  $K_{SS}$  is the dominant term, in which case the resulting time complexity is effectively  $O(|S|^2)$ .

**Case 3: RFAD with**  $|B| > |S|$ :  $O(|S|)$  and  $O(|B|)$  and random feature computation for the coreset and training batch are both are relatively expensive. Again, multiplying the random feature matrices together is cheap. The resulting complexity is thus  $O(\lambda_1|S| + \lambda_2|B|)$  Training batch random features can be computed more quickly than coreset random features as the computation graph does not need to be retained for backpropagation, so  $\lambda_1 >> \lambda_2$ .

**Case 4: KIP with**  $|B| > |S|$ :  $O(|B||S|)$  computation of  $K_{BS}$  is the dominant term, in which case the resulting time complexity is  $O(|B||S|)$ , meaning linear complexity in the coreset size. However, since  $|B| > |S|$ , this is effectively worse than  $O(|S|^2)$  scaling. Therefore, KIP is only linear in a regime where linear scaling is worse than quadratic scaling, due to a high time coefficient associated with the linear term. Note that unlike RFAD, we cannot compute  $K_{SS}$  entries any quicker than  $K_{BS}$ entries, as we cannot factorize it into coreset and training batch features, meaning we must retain the entire computation graph.

# <span id="page-16-0"></span>D Implementation details

### D.1 Preprocessing

For black/white datasets, i.e., MNIST and Fashion-MNIST, we use standard preprocessing, where we subtract the mean and divide by the standard deviation.

For color dataset SVHN, CIFAR-10, CIFAR-100, we use regularized Zero Component Analysis (ZCA) preprocessing with a regularization parameter of  $\lambda = 0.1$  for all datasets. A description of this preprocessing method is available in the appendix of [\[Nguyen et al.,](#page-12-3) [2021b\]](#page-12-3), or in [Shankar et al.](#page-13-17) [\[2020\]](#page-13-17). For CIFAR-10 and CIFAR-100, we did not include the unit normalization step in regularized ZCA as we found it reduced performance by around 1-2%.

### D.2 Architectures

For all architectures, we use the same ConvNet architecture used in [Zhao et al.](#page-13-2) [\[2021\]](#page-13-2), [Zhao and Bilen](#page-13-3) [\[2021b\]](#page-12-3), [Nguyen et al.](#page-12-3) [2021b] which consists of three layers of  $3 \times 3$  convolutions,  $2 \times 2$  average pool, and ReLU activations, followed by a fully connected layer. We do not use any normalization layers in any experiments unless otherwise stated.

For weight initialization, we use standard parameterization with Gaussian weight and bias initializations with variances  $\sigma_w^2 = 2$  and  $\sigma_b^2 = 0.1$ , following [\[Nguyen et al.,](#page-12-3) [2021b\]](#page-12-3). Note that by default, PyTorch uses Kaiming uniform initializations, which means we had to write custom convolutional layers to have Gaussian initializations. While we did not collect data on this, we found this difference in initialization to be negligible - for all intents and purposes, the default initialization works just as well but corresponds to a slightly different NNGP process.

For RFAD training, we used neural networks with 256 convolutional channels per layer. Additionally, we removed the final fully-connected layer and used the representations after the final ReLU layer to calculate the NNGP kernel instead. This can be done by noting that for fully-connected layers  $K^l = \sigma_w^2 K^{l-1} + \sigma_b^2$ . This theoretically removes some variance associated with the final layer and saves on some memory. In practice, we found this did not affect performance.

<span id="page-16-1"></span>

### D.3 Training

We used Adabelief optimizer [\[Zhuang et al.,](#page-14-1) [2020\]](#page-14-1) with a learning rate of  $1e - 3$  for all experiments and parameters, and  $\varepsilon = 1e - 16$ .

Additionally, we found it useful to split up the representation of  $X<sub>S</sub>$  into two pieces: one parameter  $\hat{X}_S$  with the same shape as  $X_S$ :  $\mathbb{R}^{|S| \times C \times H \times W}$  and another matrix  $T \in \mathbb{R}^{(C \times H \times W) \times (C \times H \times W)}$ . For example, for CIFAR-10, T would be a  $3072 \times 3072$  matrix ( $3072 = 3 \times 32 \times 32$ ). Then, we compute  $X_S$  as  $X_S$  = reshape(Tflatten( $\hat{X}_S$ ). Note this is like ZCA preprocessing where the transformation matrix is learned from  $T$ . In the code we refer to this as the  $transform\_mat$ .

Note that this does not add any extra variables for the coreset, but in practice, we found that this trick resulted in slightly faster convergence, particularly at initialization. The  $T$  matrix is initialized at the identity and learned with a small learning rate (5e-5). While we do not have a theoretical justification for why this speeds up optimization, we believe it may allow the optimizer to learn dependencies between parameters, perhaps allowing it to behave more like a second-order optimizer.

The coreset is initialized with a class-balanced subset of the data. For the labels, we use one-hot vectors with  $1/C$  subtracted so that the vector is zero-mean. E.g. for 10 samples per class the label would be  $[0.9, -0.1, -0.1, \dots -0.1].$ 

In experiments that use Platt scaling, we learn the logarithm of  $\tau$  with a learning rate of 1e-2.

For each gradient step/training iteration, we use 5120 training examples. We compute features for these in 4 batches of 1280. We save memory in this step by calculating these features with the torch.no\_grad() flag, as we do not backpropagate through these values. For the matrix inversion, we found it helpful to use double-precision since the process is sensitive to rounding errors.

Like with KIP, we used a adaptive kernel regularizer when computing the inverse in  $(K_{SS} + \lambda I)^{-1}$ . We parameterized this as  $\lambda = \lambda_0 \bar{tr}(K_{SS})$ , where  $\bar{tr}$  is the average value along the diagonal. We used  $\lambda_0 = 5e - 3.$ 

We performed early stopping with the patience of 1000 iterations. Every 40 epochs, the loss on a validation set of size 1000 is measured. This validation set is a subset of the training set (we are training on the validation set); however, we used a fixed set of 16 random neural networks when calculating the validation loss.

For results in table [1,](#page-4-0) we ran the algorithm four separate times for each configuration.

<span id="page-17-0"></span>

### D.4 Runtime experiments

The time taken was calculated by running RFAD on CIFAR-10 with images per class in  $[1, 2, 5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]$ , averaging over 200 training iterations with  $N =$ 1, 2, 4, 8. These experiments are run on a single RTX 3090. To make the results comparable to a V100, we add a conservative 40% extra time taken.

### D.5 Finite Network Transfer

We use finite networks with 1024 convolutional channels with the same weight and bias-variance as we trained on, again with standard parameterization. We used SGD with a learning rate of either  $1e-1$ ,  $1e-2$ ,  $1e-3$ ,  $1e-4$ , momentum 0.9, weight decay of either 0,  $1e-3$  and label scaling coefficients in 1, 2, 8, 16. When weight decay was used, we used the modified weight decay  $||\theta - \theta_0||_2^2$ given in [Hu et al.](#page-11-13) [\[2020\]](#page-11-13), standard weight decay would not result in zero-output when centering is used. The best hyperparameters were determined by the best validation set accuracy (taking from the training set) on the first run of the algorithm. Unless otherwise stated, we use the centering trick. Batch sizes are up to 500, meaning that we performed full-batch gradient descent for all experiments except CIFAR-100 with 10 images per class.

The results reported in table [2](#page-5-1) are the average of 12 training runs: 3 finite network training runs for each of the 4 repeat runs of RFAD.

### D.6 Privacy experiments

For CIFAR-10 corruption experiments, we use the same training protocol as in appendix [D.3.](#page-16-1) Rather than initializing the coreset with real images, we initialize with white noise in image space. The corruption constraint is applied in *pixel* space rather than the ZCA space. Note that the corruption mask (whether a pixel can be optimized) is done per-pixel and per-channel, meaning that a pixel can have its red and green channels fixed but blue channel free.

We downsized the CelebA dataset to  $64 \times 64$  images, applying standard preprocessing for this experiment. We used a training batch size of 1280 for CelebA. The full dataset achieves an accuracy of 97.6%.

For the privacy experiments, we ran only a single run for each corruption ratio. To calculate the means and standard deviations, we took the best iteration, based on the early stopping condition, and iterations at 400, 200 iterations before, and 200 and 400 after.

# E Time taken additional results

As discussed in section 4.1 and appendix [D.4,](#page-17-0) we added an extra 40% to iteration times to make or times on a RTX 3090 comparable to a Tesla V100. In this section, we report the original times. Additionally, we report the total number of training iterations and total runtimes for all our distillation results. Note that because of the early stopping condition, the epochs used during evaluation are 1000 iterations before the iteration counts we report here.

Image /page/18/Figure/0 description: The image contains text that reads "Training time per iteration for RFAD vs KIP at varying coreset sizes on CIFAR-10".

Image /page/18/Figure/1 description: The image contains two plots side-by-side. Both plots have 'Coreset Size (Img/Cls)' on the x-axis, ranging from 0 to 100. The left plot's y-axis is labeled 'Time per iteration (s)' and ranges from 0 to 2.5. It shows four lines representing RFAD with N=1 (blue), N=2 (orange), N=4 (green), and N=8 (red). The right plot's y-axis is labeled 'Log-time per iteration (s)' and uses a logarithmic scale, ranging from 10^0 to 10^3. It shows the same four RFAD lines plus a purple line representing KIP. The RFAD lines show a general upward trend with increasing coreset size, with RFAD N=8 being the highest. The KIP line starts at approximately 10^2 and increases sharply to over 10^3 as the coreset size increases.

Figure 9: Time per training iteration for CIFAR-10 with varying number of models used during and and support sizes run on an RTX 3090 compared to KIP

Table 4: Total runtime and iteration for all RFAD distilled datasets. All experiments converge in under 10h on a single RTX 3090, with label learning usually taking longer.

|               | Img/Cls  | Number of iterations                                    | Fixed Labels<br>Total elapsed<br>time(h)        | Average time<br>per iteration (s) | Number of iterations                                  | Learned Labels<br>Total elapsed<br>time(h)      | Average time<br>per iteration (s) |
|---------------|----------|---------------------------------------------------------|-------------------------------------------------|-----------------------------------|-------------------------------------------------------|-------------------------------------------------|-----------------------------------|
|               |          | $6650 \pm 859$                                          | $1.6 \pm 0.2$                                   | 0.86                              | $7390 \pm 1907$                                       | $1.7 \pm 0.5$                                   | 0.85                              |
| <b>MNIST</b>  | 10<br>50 | $11070 + 3049$<br>$8330 \pm 1225$                       | $2.8 + 0.8$<br>$2.7 \pm 0.4$                    | 0.90<br>1.18                      | $12770 \pm 2609$<br>$7420 \pm 1047$                   | $3.2 + 0.6$<br>$2.4 \pm 0.3$                    | 0.90<br>1.19                      |
| Fashion-MNIST | 10<br>50 | $9580 \pm 1740$<br>$14780 \pm 2025$<br>$11190 \pm 2056$ | $2.3 \pm 0.4$<br>$3.7 \pm 0.5$<br>$3.6 \pm 0.7$ | 0.85<br>0.89<br>1.17              | $9390 + 1924$<br>$13010 \pm 2893$<br>$12230 \pm 2206$ | $2.2 \pm 0.5$<br>$3.2 \pm 0.7$<br>$3.9 \pm 0.7$ | 0.85<br>0.89<br>1.15              |
| <b>SVHN</b>   | 10<br>50 | $7540 \pm 1521$<br>$9060 + 2155$<br>$8270 \pm 2717$     | $3.4 \pm 0.7$<br>$4.2 + 1.0$<br>$4.8 \pm 1.5$   | 1.60<br>1.67<br>2.10              | $5700 + 1267$<br>$7330 \pm 1954$<br>$10370 \pm 758$   | $2.5 \pm 0.6$<br>$3.4 \pm 0.9$<br>$6.0 \pm 0.5$ | 1.59<br>1.66<br>2.08              |
| $CIFAR-10$    | 10<br>50 | $4610 \pm 778$<br>$8310 \pm 2096$<br>$8370 \pm 2335$    | $2.0 \pm 0.3$<br>$3.8 + 1.0$<br>$4.9 \pm 1.3$   | 1.56<br>1.64<br>2.13              | $4300 \pm 1457$<br>$9210 + 923$<br>$14140 \pm 4901$   | $1.9 \pm 0.6$<br>$4.3 + 0.4$<br>$8.4 \pm 3.0$   | 1.60<br>1.66<br>2.13              |
| $CIFAR-100$   | 10       | $9820 \pm 2067$<br>$8410 \pm 2069$                      | $4.7 \pm 1.0$<br>$6.3 \pm 1.5$                  | 1.71<br>2.72                      | $13650 \pm 4161$<br>$13610 \pm 1463$                  | $6.1 \pm 2.3$<br>$9.6 \pm 0.7$                  | 1.62<br>2.54                      |

# <span id="page-18-1"></span>F Centering and label scaling for finite networks ablations

table [5](#page-19-0) shows the full table finite network transfer results. We see that label scaling and centering provide performance benefits, particularly for small support sets. We do not have a theoretical explanation for why label scaling improves performance. [\[Chizat et al.,](#page-10-16) [2019\]](#page-10-16) suggests that label scaling for values  $\alpha < 1$  should result in the network staying in the lazy regime, but in our case, we found values of  $\alpha > 1$  to improve performance.

# <span id="page-18-2"></span>G Effect of InstanceNorm

In ?? we discussed the observation that if we attempt to use instancenorm for finite network training for KIP distilled images, we do not see good performance. Conversely, if we use random networks with instancenorm in RFAD, these distilled datasets do not perform well on networks without instancenorm, either in the KRR case or finite network case. table [6](#page-19-1) shows the exact results. For NNGP KRR with instancenorm, we used the empirical NNGP kernel, with 32 networks with 1024 channels, as there is no exact implementation of the instancenorm NNGP in the neural-tangents PyTorch library [\[Novak et al.,](#page-12-12) [2020\]](#page-12-12).

# H Interpretability additional examples

# <span id="page-18-0"></span>I Empirical NNGP at Inference additional results

This section contains additional plots showing the effectiveness of using the Empirical NNGP at inference for all of our RFAD distilled datasets. In all cases, we are able to achieve close to the performance of the exact NNGP kernel for convolutional architectures with  $C \geq 128$ . We

|                          | Img/Cls       | Fixed labels<br>no label scale                      | Fixed labels<br>no centering                       | Fixed labels                                       | Learned labels                                     |
|--------------------------|---------------|-----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|
| <b>MNIST</b>             | 1             | $84.1 \pm 5.5$                                      | $90.1 \pm 3.3$                                     | $92.2 \pm 2.1$                                     | $94.4 \pm 1.5$                                     |
|                          | 10            | $96.7 \pm 0.3$                                      | $98.3 \pm 0.2$                                     | $98.4 \pm 0.2$                                     | $98.5 \pm 0.1$                                     |
|                          | 50            | $98.5 \pm 0.2$                                      | $98.8 \pm 0.1$                                     | $98.8 \pm 0.1$                                     | $98.8 \pm 0.1$                                     |
| Fashion-<br><b>MNIST</b> | 1<br>10<br>50 | $51.0 \pm 19.2$<br>$84.5 \pm 0.9$<br>$87.7 \pm 0.4$ | $69.8 \pm 3.4$<br>$85.0 \pm 0.7$<br>$87.4 \pm 0.4$ | $76.7 \pm 1.7$<br>$87.0 \pm 0.5$<br>$88.8 \pm 0.4$ | $78.6 \pm 1.3$<br>$85.9 \pm 0.7$<br>$88.5 \pm 0.4$ |
| <b>SVHN</b>              | 1             | $32.6 \pm 3.0$                                      | $40.2 \pm 2.9$                                     | $43.1 \pm 2.4$                                     | $52.2 \pm 2.2$                                     |
|                          | 10            | $65.7 \pm 1.0$                                      | $72.9 \pm 0.8$                                     | $73.6 \pm 1.0$                                     | $74.9 \pm 0.4$                                     |
|                          | 50            | $78.0 \pm 0.3$                                      | $78.5 \pm 0.3$                                     | $80.1 \pm 0.4$                                     | $80.9 \pm 0.3$                                     |
| $CIFAR-10$               | 1             | $48.7 \pm 1.6$                                      | $48.0 \pm 1.7$                                     | $53.2 \pm 1.2$                                     | $53.6 \pm 0.9$                                     |
|                          | 10            | $63.4 \pm 0.6$                                      | $56.9 \pm 1.0$                                     | $66.1 \pm 0.5$                                     | $66.3 \pm 0.5$                                     |
|                          | 50            | $69.5 \pm 0.4$                                      | $68.0 \pm 0.6$                                     | $71.1 \pm 0.4$                                     | $70.3 \pm 0.5$                                     |
| $CIFAR-100$              | 1             | $19.6 \pm 0.6$                                      | $21.2 \pm 0.3$                                     | $24.2 \pm 0.4$                                     | $26.3 \pm 1.1$                                     |
|                          | 10            | $30.5 \pm 0.3$                                      | $18.5 \pm 0.6$                                     | $30.3 \pm 0.3$                                     | $33.0 \pm 0.3$                                     |

<span id="page-19-0"></span>Table 5: Finite network transfer performance of KIP distilled images. We additional report performance if either label scaling or centering is not used. Label scaling and centering provide benefits particularly for smaller support set sizes.

<span id="page-19-1"></span>Table 6: Accuracy of RFAD distilled datasets run with or without networks with instancenorm during training evaluated on NNGP KRR and finite networks with SGD with or without instancenorm. We see that transferring from instancenorm to no instancenorm or vice versa incurs a large performance penalty. \* indicates thats the emperical NNGP kernel was used, as there is no exact implementation of instancenorm in the neural-tangents library [\[Novak et al.,](#page-12-12) [2020\]](#page-12-12)

|                                        |          |                                                    | Without InstanceNorm                               | With InstanceNorm                                  |                                                    |  |
|----------------------------------------|----------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|----------------------------------------------------|--|
|                                        | Img/Cls  |                                                    | NNGP KRR Finite Network NNGP KRR*                  |                                                    | <b>Finite Network</b>                              |  |
| Trained without<br><b>InstanceNorm</b> | 10<br>50 | $61.1 \pm 0.7$<br>$73.1 \pm 0.1$<br>$76.1 \pm 0.3$ | $53.2 \pm 1.2$<br>$66.1 \pm 0.5$<br>$71.1 \pm 0.4$ | $35.3 \pm 0.9$<br>$45.9 \pm 1.8$<br>$59.1 \pm 0.4$ | $37.4 \pm 1.1$<br>$45.1 \pm 1.1$<br>$50.3 \pm 0.8$ |  |
| Trained with<br><b>InstanceNorm</b>    | 10<br>50 | $18.1 \pm 3.7$<br>$25.6 \pm 5.3$<br>$52.5 \pm 0.5$ | $40.6 \pm 3.7$<br>$36.3 \pm 1.5$<br>$55.0 \pm 0.6$ | $57.8 \pm 0.7$<br>$71.1 \pm 0.2$<br>$74.4 \pm 0.2$ | $52.8 \pm 0.7$<br>$63.5 \pm 0.5$<br>$62.2 \pm 0.4$ |  |

additionally show an experiment where we achieve 70% accuracy on CIFAR 10, using our 10 img/cls fixed label distilled coreset using the empirical NNGP kernel from random neural networks with *one* convolutional channel in fig. [12](#page-21-0)

Image /page/20/Picture/0 description: This image displays a grid of images categorized by animal type: Car, Dog, Frog, and Horse. Each row shows a 'Test Image' followed by 'Most Relevant in Coreset' images and 'Most Relevant in Training Set' images. For the 'Car' row, the test image is a red car, and the relevant images are mostly trucks and cars. For the 'Dog' row, the test image is a white puppy, and the relevant images are mostly cats and dogs. For the 'Frog' row, the test image is a frog, and the relevant images are mostly cats and dogs. For the 'Horse' row, the test image is a white horse, and the relevant images are mostly dogs and horses. Each test image is accompanied by a red 'X' and its predicted class label, which is incorrect in all cases shown.

Figure 10: Incorrectly predicted test images and the most relevant images in the coreset and training set for CIFAR-10, 50 images/cls

Image /page/20/Picture/2 description: The image displays a comparison of image retrieval results for different categories: dogs, ships, birds, and planes. On the left, under the heading 'Test Image', there is a clear image of a dog, a ship, a bird, and a plane, each with a green checkmark and its corresponding label. To the right of each test image, there are two columns showing 'Most Relevant in Coreset' and 'Most Relevant in Training Set'. Each of these columns contains five smaller, often blurry or abstract, images that are related to the test image category. For the dog test image, the coreset and training set images are mostly dogs, with one cat in the coreset. For the ship test image, the relevant images are all ships. For the bird test image, the relevant images are all birds. For the plane test image, the relevant images are all planes. The overall layout is a grid comparing the effectiveness of retrieving similar images from a coreset versus a larger training set.

Figure 11: Correctly predicted test images and the most relevant images in the coreset and training set for CIFAR-10, 50 images/cls

Image /page/21/Figure/0 description: The image displays two plots showing the empirical NNGP inference accuracy for CIFAR-10 with 10 images per class and fixed labels, using one convolutional channel. The left plot has a linear x-axis representing the number of models, ranging from 0 to 300,000. The y-axis represents accuracy in percentage, ranging from 20% to 70%. A dotted horizontal line indicates an 'Exact' accuracy of approximately 72%. A solid blue line labeled 'C = 1' shows the accuracy increasing rapidly from around 10% at 0 models to about 70% at 25,000 models, and then plateauing around 70% for the rest of the range. The right plot has a logarithmic x-axis, also representing the number of models, from 10^0 to 10^5. The y-axis is the same, representing accuracy in percentage from 20% to 70%. The dotted horizontal line for 'Exact' accuracy is also present. The solid blue line labeled 'C = 1' shows a similar trend, starting at around 10% accuracy for 10^0 models, gradually increasing to about 70% accuracy by 10^4 models, and reaching approximately 70% by 10^5 models. The legend in the right plot clarifies that the dotted line represents 'Exact' and the solid blue line represents 'C = 1'.

<span id="page-21-0"></span>Figure 12: Empirical NNGP performance at inference for CIFAR-10, 10 images per class, fixed labels, using convolutional networks with one convolutional channels. We can achieve reasonable performance, 70%, albeit requiring over  $10^5$  random models.

Image /page/21/Figure/2 description: This image displays three rows of plots, each row representing a different number of images per class (1 img/cls, 10 img/cls, and 50 img/cls) for MNIST. Each row contains five plots that show the empirical NNGP inference accuracy. The x-axes of the plots represent different metrics: number of models, number of features, time taken (s), memory (approx Mb), and memory \* time taken (sMb). The y-axis for all plots is accuracy (%). Within each plot, there are four colored lines (blue, orange, green, and red) representing different values of C (128, 256, 512, and 1024, respectively), and a dotted black line labeled 'Exact'. The shaded regions around the colored lines indicate the variance. Black dots are present on some of the lines, indicating specific points of interest.

Figure 13: Empirical NNGP inference accuracy for MNIST with fixed labels

Image /page/22/Figure/0 description: This image displays three rows of plots, each row representing a different number of images per class (1, 10, and 50) for Fashion-MNIST with fixed labels. Each row contains five plots that show empirical NNGP inference accuracy. The x-axes of these plots represent 'Number of models', 'Number of features', 'Time taken (s)', 'Memory (approx Mb)', and 'Memory \* Time taken (sMb)'. The y-axis for all plots is 'Accuracy (%)'. Each plot shows multiple colored lines (blue for C=128, orange for C=256, green for C=512, and red for C=1024) representing different values of C, with shaded areas indicating the variance. A dotted black line labeled 'Exact' indicates the exact inference accuracy. In each row, the accuracy generally increases with the number of models, number of features, time taken, memory, and memory\*time taken, with the different C values showing varying performance characteristics. The plots for 10 and 50 images per class show higher overall accuracies compared to the plot for 1 image per class.

Figure 14: Empirical NNGP inference accuracy for Fashion-MNIST with fixed labels

Image /page/23/Figure/0 description: This image displays a collection of line graphs illustrating the empirical NNGP inference accuracy for SVHN dataset with varying image-per-class counts (1, 10, and 50) and fixed labels. Each row corresponds to a different image-per-class count. Within each row, there are five subplots. The first subplot shows accuracy versus the number of models, with the x-axis ranging from 0 to 30 and the y-axis from 30% to 55%. The second subplot displays accuracy against the number of features, using a logarithmic scale for the x-axis (10^2 to 10^4) and a y-axis from 30% to 55%. The third subplot plots accuracy against time taken in seconds, with a logarithmic x-axis (10^0 to 10^2) and a y-axis from 30% to 55%. The fourth subplot shows accuracy versus memory usage in approximate megabytes, with a logarithmic x-axis (10^1 to 10^3) and a y-axis from 30% to 55%. The fifth subplot illustrates accuracy against memory multiplied by time taken, using a logarithmic x-axis (10^1 to 10^3) and a y-axis from 30% to 55%. Each subplot contains multiple colored lines representing different values of C (128, 256, 512, 1024) and a dotted line labeled 'Exact'. The shaded regions around the lines indicate confidence intervals. The second row, corresponding to 10 img/cls, has y-axis ranges from 40% to 75%, and the third row, for 50 img/cls, has y-axis ranges from 50% to 80%.

Figure 15: Empirical NNGP inference accuracy for SVHN with fixed labels

Image /page/24/Figure/0 description: The image displays three rows of plots, each row representing a different configuration for empirical NNGP inference accuracy on CIFAR-10. The configurations are 1 img/cls, 10 img/cls, and 50 img/cls, all with fixed labels. Each row contains five plots. The first plot in each row shows accuracy (%) versus the number of models. The second plot shows accuracy (%) versus the number of features on a logarithmic scale. The third plot shows accuracy (%) versus time taken (s) on a logarithmic scale. The fourth plot shows accuracy (%) versus memory (approx Mb) on a logarithmic scale. The fifth plot shows accuracy (%) versus memory \* time taken (sMb) on a logarithmic scale. Each plot displays multiple colored lines representing different values of C (128, 256, 512, 1024), with a dotted line indicating 'Exact' accuracy. Shaded regions around the colored lines indicate uncertainty or variance.

Figure 16: Empirical NNGP inference accuracy for CIFAR-10 with fixed labels

Image /page/24/Figure/2 description: The image displays two rows of plots, each row representing empirical NNGP inference accuracy for CIFAR-100 with different image per class settings. The top row shows results for 1 img/cls, and the bottom row for 10 img/cls, both with fixed labels. Each row contains five plots. The first plot in each row shows accuracy (%) versus the number of models. The second plot shows accuracy (%) versus the number of features on a logarithmic scale. The third plot shows accuracy (%) versus time taken (s) on a logarithmic scale. The fourth plot shows accuracy (%) versus memory (approx Mb) on a logarithmic scale. The fifth plot shows accuracy (%) versus memory \* time taken (sMb) on a logarithmic scale. All plots include lines for different values of C: 128 (blue), 256 (orange), 512 (green), and 1024 (red), as well as a dotted line representing 'Exact' accuracy. Black dots are marked on some of the lines in each plot. The y-axis for the top row ranges from 15% to 35%, while the y-axis for the bottom row ranges from 20% to 45%.

Figure 17: Emperical NNGP inference accuracy for CIFAR-100 with fixed labels

Image /page/25/Figure/0 description: This image displays three rows of plots, each row representing a different number of images per class for MNIST: 1, 10, and 50. Each row contains five plots that show the empirical NNGP inference accuracy. The x-axes of the plots represent 'Number of models', 'Number of features', 'Time taken (s)', 'Memory (approx Mb)', and 'Memory \* Time taken (sMb)'. The y-axis for all plots is 'Accuracy (%)'. Each plot shows multiple colored lines (blue, orange, green, red) representing different values of C (128, 256, 512, 1024), along with a dotted black line labeled 'Exact'. Shaded areas around the colored lines indicate uncertainty. Black dots are present on some of the lines, marking specific points.

Figure 18: Empirical NNGP inference accuracy for MNIST with learned labels

Image /page/26/Figure/0 description: This image contains three rows of plots, each row representing a different number of images per class (1, 10, and 50) for Fashion-MNIST with learned labels. Each row has five plots that show empirical NNGP inference accuracy. The x-axes of the plots represent: number of models, number of features, time taken (in seconds), memory (in approximate Mb), and memory times time taken (in sMB). The y-axis for all plots is accuracy in percentage. Each plot displays multiple colored lines representing different values of C (128, 256, 512, 1024) and a dotted black line labeled 'Exact'. The plots generally show that accuracy increases with the number of models, number of features, time taken, memory, and memory\*time taken, with higher values of C generally leading to higher accuracy, approaching the 'Exact' accuracy.

Figure 19: Empirical NNGP inference accuracy for Fashion-MNIST with learned labels

Image /page/27/Figure/0 description: This image contains three rows of plots, each row representing a different dataset: SVHN with 1 img/cls, SVHN with 10 img/cls, and SVHN with 50 img/cls. Each row has five plots. The first plot in each row shows accuracy (%) versus the number of models, with x-axis ranging from 0 to 30. The second plot shows accuracy (%) versus the number of features, with x-axis on a logarithmic scale from 10^2 to 10^4. The third plot shows accuracy (%) versus time taken (s), with x-axis on a logarithmic scale from 10^0 to 10^2. The fourth plot shows accuracy (%) versus memory (approx Mb), with x-axis on a logarithmic scale from 10^1 to 10^3. The fifth plot shows accuracy (%) versus memory \* time taken (sMb), with x-axis on a logarithmic scale from 10^1 to 10^3. Each plot displays multiple colored lines representing different values of C (128, 256, 512, 1024) and a dotted line labeled 'Exact'. The accuracy generally increases with the number of models, number of features, time taken, memory, and memory \* time taken, approaching a maximum value. The plots for 10 img/cls and 50 img/cls show higher maximum accuracies compared to the 1 img/cls dataset.

Figure 20: Empirical NNGP inference accuracy for SVHN with learned labels

Image /page/28/Figure/0 description: The image displays three rows of plots, each row representing a different number of images per class (1, 10, and 50) for CIFAR-10 with learned labels. Each row contains five plots that show the empirical NNGP inference accuracy. The x-axes of these plots represent: number of models, number of features, time taken (s), memory (approx Mb), and memory \* time taken (sMb). The y-axis for all plots is accuracy (%). Each plot shows multiple colored lines (blue, orange, green, red) corresponding to different values of C (128, 256, 512, 1024), and a dotted black line labeled 'Exact'. The plots generally show that accuracy increases with the number of models, features, time, and memory, with higher values of C generally achieving higher accuracy faster or with less resources. A black dot is present on each curve, likely indicating a specific point of interest or a threshold.

Figure 21: Empirical NNGP inference accuracy for CIFAR-10 with learned labels

Image /page/28/Figure/2 description: The image displays two rows of plots, each containing five subplots. The top row is titled "Empirical NNGP inference accuracy for CIFAR-100, 1 img/cls, learned labels", and the bottom row is titled "Empirical NNGP inference accuracy for CIFAR-100, 10 img/cls, learned labels". Each subplot plots accuracy (%) on the y-axis against a different metric on the x-axis: "Number of models", "Number of features", "Time taken (s)", "Memory (approx Mb)", and "Memory \* Time taken (sMb)". Within each subplot, there are four colored lines representing different values of C: 128 (blue), 256 (orange), 512 (green), and 1024 (red). A dotted black line labeled "Exact" is also present in each plot, indicating the exact inference accuracy. Black dots are placed on some of the colored lines in each subplot.

Figure 22: Emperical NNGP inference accuracy for CIFAR-100 with learned labels
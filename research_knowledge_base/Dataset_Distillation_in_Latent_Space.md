# Dataset Distillation in Latent Space

<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>\*

<span id="page-0-1"></span>MoE Key Lab of Artificial Intelligence, Shanghai Jiao Tong University, Shanghai, China

<EMAIL>, <EMAIL>, <EMAIL>

## Abstract

*Dataset distillation (DD) is a newly emerging research area aiming at alleviating the heavy computational load in training models on large datasets. It tries to distill a large dataset into a small and condensed one so that models trained on the distilled dataset can perform comparably with those trained on the full dataset when performing downstream tasks. Among the previous works in this area, there are three key problems that hinder the performance and availability of the existing DD methods: high time complexity, high space complexity, and low info-compactness. In this work, we simultaneously attempt to settle these three problems by moving the DD processes from conventionally used pixel space to latent space. Encoded by a pretrained generic autoencoder, latent codes in the latent space are naturally info-compact representations of the original images in much smaller sizes. After transferring three mainstream DD algorithms to latent space, we significantly reduce time and space consumption while achieving similar performance, allowing us to distill high-resolution datasets or target at greater data ratio that previous methods have failed. Besides, within the same storage budget, we can also quantitatively deliver more latent codes than pixel-level images, which further boosts the performance of our methods.*

# <span id="page-0-0"></span>1. Introduction

Due to the rapidly progressing computation capability of modern devices, people are building unprecedentedly large and data-hungry models. For instance, the state-of-theart text-to-image generative models, Stable Diffusions [\[30\]](#page-8-0), were pretrained on LAION-5B [\[33\]](#page-8-1), which contains 5.85 billion text-image pairs. Although large models trained on large datasets have achieved fascinating performance in many applications, they are still known for the high demands on training time, computing devices, storage budgets, and electricity consumption.

Methods following the prototype of DD [\[39\]](#page-9-0) have usually focused on one or two of the three problems. For example, the three mainstream DD algorithms DC [\[44\]](#page-9-1), DM [\[46\]](#page-9-2) and MTT [\[6\]](#page-8-6) respectively use gradient matching, feature matching and parameter matching to efficiently approximate the bi-level optimization of the prototype, improving P1–P2 yet omitting P3. Some other works factorize the distilled dataset into more info-compact *components* [\[7,](#page-8-7) [19,](#page-8-8) [25\]](#page-8-9) thus alleviate P3. Nevertheless, since these methods still operate on pixel space using the DD algorithms above, they even induce extra time and space consumption when transforming the components to images and inversely back-propagating gradients from images to components, thus fail in P1–P2. To the best of our knowledge, no previous work simultaneously settles all the three problems.

Within the field of image generation, another do-

In recent years, dataset distillation (DD) has become a newly emerging research topic which tries to lower the aforementioned demands. Inspired by knowledge distillation [\[3,](#page-8-2) [5,](#page-8-3) [16,](#page-8-4) [31\]](#page-8-5), DD aims to distill a full dataset into a much smaller synthetic set, so that in specific tasks the models trained on this distilled dataset are expected to perform comparably to those trained on the full dataset. Wang et al. [\[39\]](#page-9-0) proposed a pioneering prototype method of DD by solving a bi-level optimization problem, which has inspired many following works. By analyzing these works, we summarize the common issues in DD into three problems. Problem 1: Most DD methods involve solving a computationally intensive optimization problem, which has high time complexity. Problem 2: Most methods have to store the computation graph recording the optimization process of the network before updating the distilled dataset, making its space complexity high too, especially for those bi-level optimization methods with nested loops. Problem 3: As we expect to remain a small data ratio between the size of distilled dataset and full dataset, the distilled dataset should be highly info-compact to cover as much information as possible. However, distilling dataset in the original space (*e.g*. pixel space for images) will inevitably condense high-frequency details into limited storage budget, which are usually less necessary for downstream tasks.

<sup>\*</sup>Corresponding author.

<span id="page-1-1"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: This figure illustrates two methods of dataset distillation: (a) Dataset Distillation and (b) Latent Dataset Distillation, followed by (c) Evaluation. In (a), a 'Train Image' set is fed into a 'DD Algorithm (DC/DM/MTT)' which generates a 'Syn Image' set, with a 'Match Loss' and gradient '∇S' involved. In (b), 'Train Image' is first processed by an encoder 'E' to produce latent representations 'LT'. These are then fed into a 'DD Algorithm (LatentDC/DM/MTT)' which generates latent representations 'LS'. A decoder 'D' then produces the 'Syn Image' set. This process also involves a 'Match Loss' and gradient '∇LS'. Finally, in (c) Evaluation, a 'Net' is trained on the original 'Train Image' and another 'Net' is trained on the distilled 'Syn Image', with a 'Performance Comparison' between them.

Figure 1. An overview of dataset distillation (DD) and LatentDD. (a) Procedure of DD in pixel space, where DD algorithms produce gradients to update synthetic images  $S$ . (b) Procedure of DD in latent space, where DD algorithms directly operate on latent codes and produce gradients to update synthetic latent codes in  $L<sub>S</sub>$ , without decoding latent codes into images before feeding them into DD algorithms. (c) After distillation, networks trained on real training images  $\tau$  and synthetic images  $S$  will be compared.

main fraught with computational complexity, recent breakthroughs in diffusion models [\[11,](#page-8-10) [17,](#page-8-11) [35\]](#page-9-3) have pushed both the performance and the time  $\&$  space consumption to a new level. Later on, Rombach et al. [\[30\]](#page-8-0) have proposed latent diffusion models transferring the diffusing/denoising procedures from pixel space to latent space with the help of a pretrained autoencoder, which largely accelerates the training process while keeping the performance. Inspired by such design, we propose Latent Dataset Distillation (LatentDD) which transfers the three mainstream DD algorithms DC, DM and MTT to fully operate on latent codes encoded by the pretrained generic autoencoder provided in latent diffusion models rather than on images, namely LatentDC, LatentDM and LatentMTT. An overview of La-tentDD is shown in Figure [1](#page-1-0) (b) and (c). Since the latent codes have much smaller size than pixel-level images, LatentDD takes significantly less time and space (both main memory and GPU memory) to run DD algorithms, alleviating P1–P2. Such acceleration and space reduction is only at the cost of marginal performance degradation, as the pretrained autoencoder can roughly keep the distribution of the original images into the latent codes and thus solving DD in latent space is approximately equivalent to that in pixel space (see Sec. [3.2\)](#page-2-0). As for P3, the latent codes are naturally info-compact representations of the original images since the autoencoder can reconstruct the images from latent codes losing just the subtlest details. Besides, with a fixed data ratio (storage budget) in DD tasks, we can quantitatively store much more latent codes than pixel-level images, which boosts the performance of LatentDD.

In summary, our work is the first to settle all the three problems in DD at the same time. Additionally, it is noteworthy that its fast and space-saving designs enable LatentDD to distill high-resolution datasets. While most of the recent works have been dealing with toy datasets like CI-FAR10/100 [\[20\]](#page-8-12) while only a few latest ones trying higher resolution like 64 or 128, we roll out our experiments on high-resolution settings starting from 256, and beyond. These challenging experiments in Sec. [4](#page-5-0) have manifested the superiority of LatentDD over previous works.

# 2. Related Work

When selecting representatives from large-scale datasets, coreset selection [\[1,](#page-8-13) [2,](#page-8-14) [13,](#page-8-15) [18,](#page-8-16) [34,](#page-9-4) [37,](#page-9-5) [40,](#page-9-6) [48\]](#page-9-7) was once the primary solution. Since Wang et al. [\[39\]](#page-9-0), follow-up works started to focus on dataset distillation, aiming at solving the three problems mentioned in Sec. [1.](#page-0-0) Besides the brief introductions below, some surveys [\[14,](#page-8-17) [23,](#page-8-18) [32,](#page-8-19) [41\]](#page-9-8) are also recommended for more details.

Gradient Matching Zhao and Bilen [\[44\]](#page-9-1) proposed Dataset Condensation (DC), the first practically plausible DD algorithm which simplified the clumsy bi-level optimization of the prototype. DC used single-step gradient matching as a surrogate objective to bridge the parameter gap when trained on real/synthetic datasets. Following DC, DSA [\[45\]](#page-9-9) attached Differentiable Siamese Augmentation to DC framework. DCC/DSAC [\[22\]](#page-8-20) enhanced DC/DSA with contrastive signal, matching gradient in an all-class manner instead of a class-wise one.

Feature Matching Zhao and Bilen [\[46\]](#page-9-2) proposed Distribution Matching (DM), which extracted features from both <span id="page-2-2"></span>real/synthetic images via randomly initialized networks and matched their mean values class-wisely. As another mainstream DD algorithm, DM largely accelerated DD process by completely avoiding bi-level optimization. IDM [\[47\]](#page-9-10) improved DM with image partitioning [\[19\]](#page-8-8) and trained feature extractors. Similar to DM, CAFE [\[38\]](#page-9-11) also compared the mean values of multi-layer features, yet equipped with an extra discrimination loss.

Parameter Matching Cazenavette et al. [\[6\]](#page-8-6) proposed the third DD algorithm Matching Training Trajectories (MTT). It reduced the accumulated parameter error in gradient matching methods by matching model parameters after relatively long-term training trajectories. After MTT, Li et al. [\[24\]](#page-8-21) pruned hard-to-match parameters, FTD [\[12\]](#page-8-22) regularized flat trajectories during the buffer phase, and TESLA [\[8\]](#page-8-23) improved MTT by lowering its space complexity.

Optimization Besides designing DD algorithms, some other works attempted to enhance these algorithms with optimization techniques, including kernel method [\[27](#page-8-24)[–29,](#page-8-25) [49\]](#page-9-12), label learning [\[4,](#page-8-26) [8\]](#page-8-23), model augmentation [\[43\]](#page-9-13), clustering [\[26\]](#page-8-27) and calibration [\[50\]](#page-9-14).

Factorization Factorization is another research direction orthogonal to designing DD algorithms. It aims at factorizing distilled images into more info-compact components, so that these components can be transformed into quantitatively more images for downstream tasks than directly storing pixel-level images within the same storage budget. Specific strategies include image partitioning [\[19,](#page-8-8) [47\]](#page-9-10) and factorizing images into latent codes + decoders [\[7,](#page-8-7) [10,](#page-8-28) [21,](#page-8-29) [25\]](#page-8-9). However, all these methods had to repeatedly restore the pixel-level images before sending them into DD algorithms, and back-propagate the gradients from the images to the components, resulting in heavy time & space overhead. On the contrary, as a factorization method, our LatentDD instead directly operates in latent space, which largely reduces time & space consumption.

# 3. Latent Dataset Distillation

# 3.1. Problem Definition

Suppose we have a large dataset  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  to be distilled, which consists of real pairs of datum  $x_i \in \mathbb{R}^d$  and class label  $y_i \in \{0, \ldots, C-1\}$  where d is the dimension of the data and  $C$  is the number of classes. The goal is to seek a distilled dataset  $\mathcal{S} = \{(\tilde{x}_i, \tilde{y}_i)\}_{i=1}^{|\mathcal{S}|}$  including synthetic pairs of datum  $\tilde{x}_i$  and label  $\tilde{y}_i$ , and  $|\mathcal{S}| \ll |\mathcal{T}|$ . Conventionally the synthetic data follow the form of real data (*i.e.*  $\tilde{x}_i \in \mathbb{R}^d$  and  $\tilde{y}_i \in \{0, \ldots, C-1\}$ . However, there are also some works exploring more effective forms of data via factorization, or

labels via label learning, as long as  $S$  does not exceed a predefined storage budget.

With the distilled  $S$ , we expect that models trained on it will achieve comparable performance with those trained on the real dataset  $T$  in downstream tasks. Formally,

$$
\mathcal{S}^* = \underset{\mathcal{S}}{\arg \min} \|\mathcal{L}^{\mathcal{T}}(\theta^{\mathcal{S}}) - \mathcal{L}^{\mathcal{T}}(\theta^{\mathcal{T}})\| \text{ subject to}
$$
$$
\theta^{\mathcal{S}} = \underset{\theta}{\arg \min} \mathcal{L}^{\mathcal{S}}(\theta), \quad \theta^{\mathcal{T}} = \underset{\theta}{\arg \min} \mathcal{L}^{\mathcal{T}}(\theta), \tag{1}
$$

where  $\theta^{\mathcal{S}}$  and  $\theta^{\mathcal{T}}$  are models trained on  $\mathcal{S}$  and  $\mathcal{T}$ , and  $\mathcal{L}^{\mathcal{S}}$ ,  $\mathcal{L}^{\mathcal{T}}$  are respectively a certain objective (loss function) when evaluated on the two datasets. Since models trained on S are unlikely to outperform those trained on  $\mathcal T$ , we usually seek  $S^*$  that performs the best:

<span id="page-2-1"></span>
$$
S^* = \underset{S}{\arg\min} \mathcal{L}^{\mathcal{T}}(\theta^S) \quad \text{subject to} \quad \theta^S = \underset{\theta}{\arg\min} \mathcal{L}^S(\theta). \tag{2}
$$

## <span id="page-2-0"></span>3.2. From Pixel to Latent Space

The prototype of DD straightforwardly solves Eq. [\(2\)](#page-2-1) in a bi-level optimization manner, and later three mainstream DD algorithms DC, DM and MTT have been proposed to efficiently solve Eq. [\(2\)](#page-2-1) by optimizing surrogate objectives. Primarily focusing on image classification, all the previous works have distilled datasets in pixel space. When solv-ing Eq. [\(2\)](#page-2-1) with DD algorithms, data in both  $\mathcal T$  and  $\mathcal S$  are in the form of the original pixel-level images (*i.e.*  $x_i, \tilde{x}_i \in$  $\mathbb{R}^{3 \times H \times W}$ , as three-channel images with size  $(H, W)$ ). Although some previous works have proposed factorization methods which distill more info-compact components instead of images, they are still based on DD algorithms in pixel space. As pixel-level images usually consist of lowfrequency information, which includes the contents that are truly necessary to downstream tasks like image classification, *and* high-frequency information including fine and subtle details and even noises. Taking both parts of information into account when running DD algorithms has brought considerable overhead in time and space consumption, especially when distilling high-resolution datasets or aiming at a higher data ratio. Also, with the useless high-frequency information occupying some of the storage budget, the less info-compact synthetic images fail to reach better scores in classification tasks.

In this work, we attempt to transfer the DD algorithms to directly operate in latent space rather than pixel space. Suppose we have an autoencoder  $\{\mathcal{E}(\cdot), \mathcal{D}(\cdot)\}\$  pretrained on a large dataset to ensure its generalization ability to encode and reconstruct any image with its encoder  $\mathcal{E}(\cdot)$  and decoder  $\mathcal{D}(\cdot)$ . Specifically, from a real image  $x_i \in \mathbb{R}^{3 \times H \times W}$  in T, the encoder can encode it into a latent code  $l_i \in \mathbb{R}^{C \times \frac{H}{f} \times \frac{W}{f}}$ as a C-channel feature map with a downsampling factor f, and the settings of C and f ensure that the size of  $l_i$ is smaller than  $x_i$  (*i.e.*  $C < 3 \cdot f^2$ ). Then, from a latent

<span id="page-3-1"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: Two heatmaps are displayed side-by-side. Both heatmaps are square matrices with axes labeled from 0 to 15. The diagonal elements of both matrices are dark purple, indicating a value of 0. The color scale for the left heatmap ranges from 0 to 500, with purple representing low values and yellow representing high values. The color scale for the right heatmap ranges from 0 to 100, with a similar color gradient. The heatmaps show a distribution of values across the matrix, with some areas appearing brighter yellow, indicating higher values.

Figure 2. The Euclidean distance matrices of 20 randomly sampled images (left) from subset *Bird* of ImageNet and their corresponding latent codes (right).

code  $l_i$ , the decoder can decode it into a reconstructed image  $x'_i \in \mathbb{R}^{3 \times H \times W}$  back to the original size. If the autoencoder is well trained, the reconstructed image  $x_i'$  should be roughly the same as the original image  $x_i$ , with acceptable minor loss of details and noises. In this way, the latent code  $l_i$  can naturally serve as an info-compact representation of the original image  $x_i$  since most of the necessary information needed to reconstruct  $x_i$  has been encoded into  $l_i$ . Therefore, if we can use latent codes instead of images in DD algorithms, it will improve DD processes by simultaneously alleviate the three problems mentioned in Sec. [1.](#page-0-0)

Stable Diffusion, the most widely used instance of latent diffusion models [\[30\]](#page-8-0), is equipped with such a pretrained generic autoencoder, which successfully transfer the timeconsuming denoising processes to latent space. In our LatentDD, we utilize this off-the-shelf autoencoder as a converter between images in pixel space and latent codes in latent space. By default, this autoencoder is capable of encoding any image with any resolution into a latent code with  $C = 4$  channels and a downsampling factor  $f = 8$ . For instance, an RGB image of resolution  $512 \times 512$  will be encoded into a latent code of size  $4 \times 64 \times 64$ . By these settings, a latent code is only 1/48 of the original image w.r.t. the number of parameters, which is highly info-compact.

Nevertheless, before transferring DD algorithms to latent space, we still need to verify that this autoencoder will not break the original distribution of the images, as keeping this distribution is critical to image classification tasks. In a preliminary experiment, we randomly select 20 images from *Bird*, a subset of ImageNet (see Sec. [4.1](#page-5-1) for details), encode them into latent codes, and respectively show the Euclidean distance matrices of these images and latent codes in Figure [2.](#page-3-0) From the two heatmaps, we may conclude that the latent codes approximately remain the distribution of the original images, as the relative distances among these latent codes strongly correlate with those among the images. As a result, the learned classification hyperplanes in latent space can correspond to the hyperplanes in pixel space, making DD in latent space and pixel space approximately equivalent. Such equivalence is also empirically validated by ablation study in Sec. [4.3,](#page-6-0) where training classifiers on the full sets of images/latent codes (Tab. [3\)](#page-7-0) and distilling the datasets into same number of images/latent codes (Tab. [4\)](#page-7-1) both render similar performance.

Based on Eq. [\(2\)](#page-2-1), DD in latent space can be written as

$$
L_{\mathcal{S}}^{*} = \underset{L_{\mathcal{S}}}{\arg\min} \mathcal{L}^{L_{\mathcal{T}}}(\theta^{L_{\mathcal{S}}}) \quad \text{subject to} \quad \theta^{L_{\mathcal{S}}} = \underset{\theta}{\arg\min} \mathcal{L}^{L_{\mathcal{S}}}(\theta),
$$
\n(3)

where  $L_{\mathcal{T}} = \{ (\mathcal{E}(x_i), y_i) \}_{i=1}^{|\mathcal{T}|}$  and  $L_{\mathcal{S}} = \{ (\tilde{l}_i, \tilde{y}_i) \}_{i=1}^{|\mathcal{S}|}$  are respectively the set of real and synthetic pairs of latent code and its label, and we remain  $\tilde{y}_i \in \{0, \ldots, C-1\}$  as class labels. Before running LatentDD algorithms,  $L_{\tau}$  is precomputed and stored in the main memory for fast retrieval, and  $L<sub>S</sub>$  is initialized with randomly selected real latent codes. After obtaining  $L_{\mathcal{S}}^*$  via LatentDD, we can reconstruct the synthetic image dataset as  $S^* = \{(\mathcal{D}(\tilde{l}_i), \tilde{y}_i)\}_{i=1}^{|S|}$  for downstream tasks, as depicted in Figure [1.](#page-1-0)

As previous works [\[10,](#page-8-28) [19,](#page-8-8) [25\]](#page-8-9) focusing on factorization have proved, the fixed data ratio will severely confine the performance on downstream image classification tasks if we stick to delivering distilled datasets as pixel-level images within a limited storage budget. These works instead deliver info-compact components such as low-resolution thumbnails or a combination of latent codes and decoders within the storage budget, which can be resized or decoded into more images than the same budget can directly store. Our LatentDD has followed this idea, as we deliver  $n \cdot 3f^2/C$  latent codes rather than  $n$  pixel-level images. It is also worth mentioning that, previous works of latent codes + decoders also train their decoders along with the latent codes during DD processes, thus their decoders can only be exclusively used on specific datasets (or even specific classes of these datasets) and specific resolutions. So they have to store the decoders as a part of the distilled datasets to be delivered. On the contrary, since our pretrained autoencoder is generic for any image and any resolution, and is also publicly available online, it only takes  $O(1)$  space to store the decoder when we distill N datasets instead of  $O(N)$  as in previous works. Hence our decoder will averagely take negligible space if we distill many datasets and may be excluded from the storage budget like an unparameterized resizing operation that can be applied to any image.

#### 3.3. Latent Dataset Distillation Algorithms

Dataset Condensation (DC), Distribution Matching (DM) and Matching Training Trajectories (MTT) are three mainstream DD algorithms, which solve the DD problem in Eq. [\(2\)](#page-2-1) with surrogate objectives of gradient matching, feature matching and parameter matching respectively. We

<span id="page-4-3"></span>show how to seamlessly transfer these algorithms to latent space as below. Pseudocodes providing detailed procedures are available in Appendix.

### 3.3.1 LatentDC

DC [\[44\]](#page-9-1) is designed based on the observation that, if the model  $\theta^S$  trained on distilled dataset S has similar parameters with  $\theta^{\mathcal{T}}$  trained on real dataset  $\mathcal{T}$ , it will be certain to perform comparably when evaluated on test set. Such resembling parameters can be achieved by matching the gradients of network parameters  $\nabla_{\theta}$  induced by training the same model  $\theta_t$  (at timestep t) on S and T. Formally,

<span id="page-4-2"></span>
$$
S^* = \underset{S}{\arg\min} \mathbb{E}_{\theta_0 \sim P_{\theta_0}} \left[ \sum_{t=0}^{T-1} D(\nabla_{\theta} \mathcal{L}^S(\theta_t), \nabla_{\theta} \mathcal{L}^T(\theta_t)) \right]
$$
\n(4)

\nsubject to

\n
$$
\theta_{t+1} \leftarrow \theta_t - \eta \nabla_{\theta} \mathcal{L}^S(\theta_t),
$$

where DC randomly initializes network parameters  $\theta_0$  and repeatedly matches the two groups of gradients with a cosine-like gradient matching loss  $D(\cdot, \cdot)$  along the T-step training process of  $\theta_t$  on synthetic dataset S and classification criterion  $\mathcal L$  (usually a cross-entropy loss).

To run DC in latent space instead of pixel space, the most essential modification is that we have to replace the network  $\theta$  operating on pixel-level images with a new network  $\tilde{\theta}$  operating on latent codes. Since the latent codes  $l \in \mathbb{R}^{C \times \frac{H}{f} \times \frac{W}{f}}$  still remain a 2D spatial structure just as images, commonly used convolutional architectures are also applicable to them if only we accordingly change the channel numbers and feature map sizes. Besides, as the size of  $l$  is much smaller than images, we can use lighter networks with fewer layers as  $\theta$ . For instance, while distilling image datasets of resolution 256 conventionally uses ConvNetD6 with depth 6, we apply ConvNetD3 as  $\theta$  when  $f = 8$  and ConvNetD4 when  $f = 4$ , just as dealing with images of the corresponding resolution (refer to Sec. [4.1](#page-5-1) for details). Being able to reduce the size of networks used in DD is one of the main reasons that our latent version of DD algorithms takes much less time and space to run. With  $\theta$ , our LatentDC can be formulated as

<span id="page-4-1"></span>
$$
L_{\mathcal{S}}^{*} = \underset{L_{\mathcal{S}}}{\arg\min} \mathbb{E}_{\tilde{\theta}_{0} \sim P_{\tilde{\theta}_{0}}} \left[ \sum_{t=0}^{T-1} D(\nabla_{\tilde{\theta}} \mathcal{L}^{L_{\mathcal{S}}}(\tilde{\theta}_{t}), \nabla_{\tilde{\theta}} \mathcal{L}^{L_{\mathcal{T}}}(\tilde{\theta}_{t})) \right]
$$
\n(5)

\nsubject to

\n
$$
\tilde{\theta}_{t+1} \leftarrow \tilde{\theta}_{t} - \eta \nabla_{\tilde{\theta}} \mathcal{L}^{L_{\mathcal{T}}}(\tilde{\theta}_{t}),
$$

where we follow Kim et al. [\[19\]](#page-8-8) to (1) update  $\hat{\theta}$  using real latent codes in  $L_{\tau}$  instead of synthetic ones in  $L_S$  as the gradients will quickly vanish if trained on the latter and (2) use an MSE-like gradient matching loss for  $D(\cdot, \cdot)$  which better fits training on real datasets. In Figure [3,](#page-4-0) we illustrate the gradient matching losses of both DC and LatentDC in the first 300 iterations when distilling *Bird* into one image/latent code per class (see Sec. [4.3\)](#page-6-0), where the loss of

<span id="page-4-0"></span>Image /page/4/Figure/8 description: A line graph shows the gradient matching loss over 300 iterations for two methods: DC (blue line) and LatentDC (orange line). The y-axis represents the gradient matching loss, ranging from 150 to 400, and the x-axis represents the iteration number, from 0 to 300. Both lines show a sharp decrease in loss in the initial iterations, followed by a gradual decrease and stabilization. The LatentDC line generally stays below the DC line, indicating a lower gradient matching loss.

Figure 3. MSE gradient matching loss of DC and LatentDC in the first 300 iterations when distilling subset *Bird* of ImageNet.

LatentDC decreases slightly faster, and more steadily than its counterpart in pixel space in the beginning. After that, DC and LatentDD both converge to a similar level of loss, which matches the resembling performance on downstream classfication tasks reported in Tab. [4.](#page-7-1)

Based on DC, Zhao and Bilen [\[45\]](#page-9-9) add Differentiable Siamese Augmentation (DSA) during both training and evaluation stages, which has become a standard technique in following works. However, according to our preliminary experiments, only two transformations (crop, cutout) used in DSA can also be applied to latent codes, while the others (color, scale, rotate, flip) on latent codes will unexpectedly affect the quality of the decoded images. Hence, during the training stage in Eq. [\(5\)](#page-4-1) we do not apply DSA, but still augmenting the pixel-level images decoded from latent codes in the evaluation stage (see Appendix). Such strategy is also adopted by LatentDM and LatentMTT. We suppose that designing a set of feasible transformations for augmenting latent codes is another topic worth researching on, yet we leave this part for future work.

#### 3.3.2 LatentDM

Unlike the other two algorithms DC and MTT, DM [\[46\]](#page-9-2) is designed aiming at totally eliminate the computationally intensive bi-level optimization. It updates the distilled dataset  $S$  so that the empirical estimate of maximum mean discrepancy (MMD) is minimized between each class of S and  $\mathcal{T}$ :

$$
S^* = \underset{S}{\arg\min} \mathbb{E}_{{\theta} \sim P_{{\theta}}}[\sum_{c=0}^{C-1} || \frac{1}{|\mathcal{T}_c|} \sum_{(x_i, y_i) \in \mathcal{T}_c} \phi_{{\theta}}(x_i) - \frac{1}{|\mathcal{S}_c|} \sum_{(\tilde{x}_i, \tilde{y}_i) \in \mathcal{S}_c} \phi_{{\theta}}(\tilde{x}_i) ||^2] \quad (6)
$$

where  $\mathcal{T}_c$ ,  $\mathcal{S}_c$  are the subsets of class c, and  $\phi_\theta$  is an embedding function based on randomly initialized networks  $\theta$ . Similar to LatentDC, we can also transfer DM to LatentDM <span id="page-5-4"></span>by using embedding networks  $\tilde{\theta}$  of proper architecture:

$$
L_S^* = \underset{L_S}{\arg\min} \mathbb{E}_{{\tilde{\theta}} \sim P_{{\tilde{\theta}}}} \left[ \sum_{c=0}^{C-1} \left\| \frac{1}{|L\tau_c|} \sum_{(l_i,y_i) \in L\tau_c} \phi_{{\tilde{\theta}}}(l_i) - \frac{1}{|L_{S_c}|} \sum_{(\tilde{l}_i,\tilde{y}_i) \in L_{S_c}} \phi_{{\tilde{\theta}}}(\tilde{l}_i) \right\|^2 \right],
$$
(7)

where the embedding function  $\phi_{\tilde{\theta}}$  now extracts embeddings from latent codes rather than images.

## 3.3.3 LatentMTT

MTT [\[6\]](#page-8-6) is designed to alleviate the issue of the accumulated parameter error caused by the difference between gradients  $\nabla_{\theta} \mathcal{L}^{S}(\theta_t)$  and  $\nabla_{\theta} \mathcal{L}^{T}(\theta_t)$  in Eq. [\(4\)](#page-4-2) of DC. Starting from the same network  $\theta_t^{\mathcal{T}}$  that has been trained on the real dataset  $T$  for  $t$  steps, it matches the parameters of two networks  $\theta_{t+N}^{\mathcal{S}}$  and  $\theta_{t+M}^{\mathcal{T}}$  respectively after a student trajectory which trains on  $S$  for  $N$  steps and an expert trajectory which trains on  $T$  for  $M$  steps:

$$
S^* = \underset{S}{\arg\min} \mathbb{E}_{t \in \{0, ..., T^+\}} \left[ \frac{\|\theta_{t+N}^S - \theta_{t+M}^T\|_2^2}{\|\theta_t^T - \theta_{t+M}^T\|_2^2} \right], \quad (8)
$$

where the starting step  $t$  is sampled within the limit of a maximum starting step  $T^+$  since the later part of the a training trajectory is less informative. By matching parameters through multi-step trajectories instead of single-step gradients, MTT generally outperforms DC at the expense of greater time and space consumption. Just as in LatentDC and LatentDM, we can modify MTT into LatentMTT by moving both the expert and the student trajectories into the latent space:

$$
L_{\mathcal{S}}^{*} = \underset{L_{\mathcal{S}}}{\arg\min} \mathbb{E}_{t \in \{0, ..., T^{+}\}} \left[ \frac{\|\tilde{\theta}_{t \cdot \mathcal{S}}^{L_{\mathcal{S}}}-\tilde{\theta}_{t \cdot \mathcal{A}}^{L_{\mathcal{T}}}\|_{2}^{2}}{\|\tilde{\theta}_{t}^{L_{\mathcal{T}}}-\tilde{\theta}_{t \cdot \mathcal{A}}^{L_{\mathcal{T}}}\|_{2}^{2}} \right], \quad (9)
$$

where we also pre-buffer the expert trajectories in latent space for faster run-time matching, similar to MTT in pixel space.

# <span id="page-5-0"></span>4. Experiments

#### <span id="page-5-1"></span>4.1. Experimental Settings

Datasets To fully illustrate the capability of LatentDD, we conduct experiments on high-resolution datasets starting from 256, which is higher than the maximum resolution of almost all the previous works. We omit the less challenging low-resolution datasets such as MNIST and CI-FAR since these datasets can be managed by most previous methods. Specifically, we take five subsets of ImageNet [\[9\]](#page-8-30), namely *Bird* (ImageSquawk), *Fruit* (ImageFruit), *Woof* (ImageWoof), *Cat* (ImageMeow) and *Nette* (ImageNette), where Woof and Nette are online resources<sup>[1](#page-5-2)</sup> and the other

three comes from Cazenavette et al. [\[6\]](#page-8-6). Our experiments cover different settings of DD aiming at image classification as the downstream task, including resolution 256 or 512, and image per class (IPC) 1 or 10. See Appendix for the categories included in these ImageNet subsets and the procedures of preprocessing the images.

Baselines We select previous state-of-the-art methods based on each of the three mainstream DD algorithms DC, DM and MTT into our baseline list. For DC, we include DSA [\[45\]](#page-9-9) with augmentation, IDC [\[19\]](#page-8-8) which partitions images and GLaD DC [\[7\]](#page-8-7) based on GAN prior. For DM, we include the original DM [\[46\]](#page-9-2) and GLaD DM [\[7\]](#page-8-7). Finally for MTT, we include MTT  $[6]$ , GLaD MTT  $[7]$  and FTD  $[12]$ with flattened expert trajectories. Since no previous work has reported performance under these challenging settings, the results are based on our evaluation using their public implementations with as little modification as possible to adapt to high-resolution scenarios.

Choice of Autoencoder Although we may use any autoencoder that remains the distribution of the original images among the latent codes, we practically choose the one as a part of Stable Diffusion  $v1.4^2$  $v1.4^2$  as the pretrained generic autoencoder. This autoencoder is capable of encoding any image with a downsampling factor  $f = 8$ . Though this autoencoder can encode images of any resolution, it was originally trained on resolution 512 and we have observed that more details will be lost in the reconstructed images if used on lower resolution. Therefore, we adopt a preprocessing procedure, in which we first upsample the original pixel-level dataset  $T$  by a factor of two, and then encode the resized dataset into latent codes  $L_{\tau}$ , resulting in a downsampling factor  $f = 4$ . Before evaluation, we also follow a postprocessing procedure where we downsample the decoded images by a factor of two, restoring the synthetic dataset  $S$  at the original resolution. By default, we apply these procedures of  $f = 4$  in the experiments below unless otherwise specified. See Appendix for further information and analysis of autoencoder.

Implementation Details Please refer to Appendix for hyperparameter settings, training/evaluation protocols and pseudocodes for the three LatentDD algorithms.

## 4.2. LatentDD vs. Baselines

The quantitative results of the comparisons between LatentDD algorithms and baseline methods are shown in Tab. [1,](#page-6-1) where the distilled datasets are evaluated on ConvNets [\[15\]](#page-8-31) whose depths correspond to image resolutions

<span id="page-5-2"></span><sup>1</sup><https://github.com/fastai/imagenette>

<span id="page-5-3"></span><sup>2</sup>[https : / / huggingface . co / CompVis / stable](https://huggingface.co/CompVis/stable-diffusion-v-1-4-original)  [diffusion-v-1-4-original](https://huggingface.co/CompVis/stable-diffusion-v-1-4-original)

<span id="page-6-3"></span><span id="page-6-1"></span>

|            |                  | Res. 256         |       |       |       |              |             |       | Res. 512         |       |
|------------|------------------|------------------|-------|-------|-------|--------------|-------------|-------|------------------|-------|
| Algo.      | Method           | IPC <sub>1</sub> |       |       |       |              | IPC 10      |       | IPC <sub>1</sub> |       |
|            |                  | Bird             | Fruit | Woof  | Cat   | <b>Nette</b> | <b>Bird</b> | Fruit | <b>Bird</b>      | Fruit |
|            | <b>DSA</b>       | 30.52            | 20.28 | 22.12 | 22.20 | 34.40        | 45.52       | 30.48 | 25.44            | 16.40 |
| DC         | <b>IDC</b>       | 36.28            | 24.60 | 25.64 | 27.68 | 48.16        | 64.28       | 39.68 | 28.68            | 20.12 |
|            | <b>GLaD DC</b>   | 30.32            | 19.56 | 21.84 | 22.24 | 34.44        | 46.04       | 32.60 | 25.80            | 16.72 |
|            | LatentDC         | 46.72            | 30.12 | 28.96 | 38.08 | 55.92        | 80.44       | 51.60 | 47.52            | 29.68 |
|            | DM               | 27.64            | 19.48 | 20.04 | 21.16 | 32.08        | 41.56       | 28.12 | 28.28            | 19.72 |
| DM         | <b>GLaD DM</b>   | 28.84            | 21.28 | 21.28 | 20.52 | 32.40        |             |       | 29.32            | 20.68 |
|            | LatentDM         | 47.08            | 30.68 | 28.00 | 36.28 | 56.08        | 77.20       | 47.76 | 46.20            | 30.60 |
|            | <b>MTT</b>       | 35.80            | 21.08 | 24.92 | 24.16 | 40.52        |             |       |                  |       |
| <b>MTT</b> | <b>GLaD MTT</b>  | 32.20            | 20.48 | 23.00 | 21.44 | 31.84        |             |       |                  |       |
|            | <b>FTD</b>       | 35.96            | 21.76 | 26.32 | 26.60 | 40.96        |             |       |                  |       |
|            | <b>LatentMTT</b> | 52.86            | 37.82 | 39.84 | 41.42 | 62.86        | 78.44       | 52.46 | 52.44            | 36.20 |

Table 1. Quantitative results of dataset distillation experiments on ImageNet subsets. LatentDD algorithms follow the setting of  $f = 4$ , where LPC =  $12 \times$  IPC. The mean classification accuracy among five times of evaluations is reported. The results marked as a hyphen indicate that the methods have run out of 24GB GPU memory during the experiments.

<span id="page-6-2"></span>

| Method               | Bird 256           |               | Bird 512           |
|----------------------|--------------------|---------------|--------------------|
|                      | IPC 1              | IPC 10        | IPC 1              |
| <b>Build</b>         | <b>2m / 26.1GB</b> |               | <b>2m / 94.6GB</b> |
| <b>Build Latent</b>  | <b>10m / 5.0GB</b> |               | <b>52m / 9.9GB</b> |
| DSA                  | 6h 34m             | 30h 27m       | 25h 17m            |
| <b>IDC</b>           | 32h 36m            | 40h 26m       | 46h 50m            |
| <b>GLaD DC</b>       | 15h 15m            | 119h 20m      | 30h 51m            |
| LatentDC             | <b>1h 59m</b>      | <b>3h 18m</b> | <b>3h 21m</b>      |
| DM                   | 10m                | 14m           | 51m                |
| <b>GLaD DM</b>       | 55m                | -             | 2h 15m             |
| LatentDM             | <b>1m</b>          | <b>2m</b>     | <b>1m</b>          |
| <b>MTT Buffer</b>    |                    | 16h 20m       | 48h 50m            |
| FTD Buffer           |                    | 24h 00m       | 92h 30m            |
| <b>Latent Buffer</b> |                    | <b>50m</b>    | <b>3h 10m</b>      |
| <b>MTT</b>           | 7h 03m             | -             | -                  |
| <b>GLaD MTT</b>      | 11h 48m            | -             | -                  |
| <b>FTD</b>           | 7h 17m             | -             | -                  |
| <b>LatentMTT</b>     | <b>2h 14m</b>      | <b>4h 16m</b> | <b>2h 38m</b>      |

Table 2. Time and space (main memory) consumption during the dataset building, expert trajectory buffering and training processes of LatentDD and the baselines, evaluated on a single NVIDIA RTX 4090. LatentDD algorithms follow the setting of  $f = 4$ , where  $LPC = 12 \times IPC$ . A hyphen - indicates that the method has run out of 24GB GPU memory during the experiment.

(see Appendix). In these experiments, our LatentDD algorithms follow the previous factorization-based methods which deliver info-compact components instead of images [\[10,](#page-8-28) [19,](#page-8-8) [21,](#page-8-29) [25,](#page-8-9) [47\]](#page-9-10), delivering 12 latents per class (LPC) within the same storage of 1 IPC, and accordingly 120 LPC within 10 IPC under the setting of  $f = 4$ . As our LatentDD methods deliver highly info-compact latent codes, they are able to significantly outperform previous works which deliver less info-compact low-resolution thumbnails (IDC) or full-size images (other baselines). In this way, our LatentDD has successfully settled the info-compactness problem (P3) mentioned in Sec. [1.](#page-0-0) Additionally, we will show cross-architecture results and depict some distilled samples with three LatentDD algorithms in Appendix.

Besides, we also list the running time and main memory consumption of the above experiments in Tab. [2.](#page-6-2) Though LatentDD methods spend more time on building the datasets into main memory since they pre-encode the real datasets into latent codes, the buffering processes of LatentMTT and the training processes of all latent methods have been largely accelerated due to the smaller sizes of both the latent codes and the networks. The time spent on decoding latent codes into images is negligible since it only takes a few seconds once before evaluation. Also, unlike the previous factorization-based methods (*e.g*. IDC, GLaD) which still run pixel-level DD algorithms, LatentDD completely avoids computational overhead caused by repeatedly transforming components (*e.g*. thumbnails, latent codes) into images and backpropagating gradient from images to the components, as the only method operating DD algorithms in latent space. As for the space consumption, it is much more efficient to store latent codes into main memory than images when building datasets, and the low runtime GPU memory costs also allow LatentDD methods to run on higher resolution or greater data ratio. In conclusion, our LatentDD methods have settled the problems of high time  $\&$  space complexity (P[1](#page-0-0)–P2) in Sec. 1 as well.

#### <span id="page-6-0"></span>4.3. Ablation Studies

Pixel Space vs. Latent Space We transfer the distillation processes from pixel space to latent space based on

<span id="page-7-0"></span>

| Full set                       | Model     | Res. 256    |              |             |            |              | Model     | Res. 512    |              |
|--------------------------------|-----------|-------------|--------------|-------------|------------|--------------|-----------|-------------|--------------|
|                                |           | <b>Bird</b> | <b>Fruit</b> | <b>Woof</b> | <b>Cat</b> | <b>Nette</b> |           | <b>Bird</b> | <b>Fruit</b> |
| Pixel $(\	ext{T})$             | ConvNetD6 | 96.40       | 69.40        | 80.00       | 73.60      | 90.40        | ConvNetD7 | 92.20       | 68.80        |
| Latent $(L^{\	ext{T}}, f = 4)$ | ConvNetD4 | 92.60       | 71.60        | 77.20       | 73.00      | 91.60        | ConvNetD5 | 95.20       | 75.60        |
| Latent $(L^{\	ext{T}}, f = 8)$ | ConvNetD3 | 89.00       | 68.80        | 74.40       | 67.20      | 89.40        | ConvNetD4 | 92.80       | 72.40        |

<span id="page-7-1"></span>Table 3. Classification accuracy of the classifiers trained in pixel space and latent spaces respectively on the full sets without distillation.

| Method     | <b>Bird 256</b> |            |
|------------|-----------------|------------|
|            | IPC/LPC 1       | IPC/LPC 10 |
| <b>DSA</b> | 30.52           | 45.52      |
| LatentDC   | 29.68           | 45.56      |
| DM         | 27.64           | 41.56      |
| LatentDM   | 26.64           | 45.00      |
| <b>MTT</b> | 35.80           | -          |
| LatentMTT  | 33.60           | 46.88      |

Table 4. Quantitative results of dataset distillation where both IPC and LPC are set to 1 or 10, and  $f = 4$  for LatentDD methods.

the fact that classification in latent space is approximately equivalent to that in pixel space. Along with the theoretical analysis in Sec. [3.2,](#page-2-0) we also empirically verify such equivalence by training classifiers directly on full datasets in pixel space  $\mathcal T$  and latent spaces  $L_{\mathcal T}$  without distillation, whose performance also represents the upper bound for DD methods. We may conclude from the results in Tab. [3](#page-7-0) that (1) the performance between the two spaces is generally comparable without significant disparity; (2) when the resolution is lower (256), the latent space may be slightly inferior due to certain information loss; (3) however when the resolution is high (512), the latent space outperforms the pixel space probably because there is too much redundant highfrequency information in pixel-level images and the information loss in latent codes decreases. Therefore, transferring classification tasks from pixel space to latent space is not only feasible, but may also bring some benefits by removing the useless information irrelevant for classification.

We also compare LatentDD methods under the same IPC/LPC rather than the same storage budget. As the results shown in Tab. [4,](#page-7-1) LatentDD still achieves comparable performance with the DD algorithms in pixel space. These results also match Figure [3](#page-4-0) where LatentDC can converge to a similar level of gradient matching loss to DC.

Downsampling Factor  $f$  and Latents Per Class (LPC) In Tab. [5,](#page-7-2) we additionally provide quantitative results of LatentDD methods with  $f = 8$ , where LPC = 48  $\times$  IPC. As the number of latent codes has further increased, LatentDD methods reach even higher scores. However, as we have mentioned in Sec. [4.1](#page-5-1) that the autoencoder is originally pretrained on resolution 512, encoding and reconstructing im-

<span id="page-7-2"></span>

| Method    | f | Res. 256 IPC 1 |       | Res. 512 IPC 1 |       |
|-----------|---|----------------|-------|----------------|-------|
|           |   | Bird           | Fruit | Bird           | Fruit |
| LatentDC  | 4 | 46.72          | 30.12 | 47.52          | 29.68 |
|           | 8 | 67.28          | 38.08 | 69.46          | 37.22 |
| LatentDM  | 4 | 47.08          | 30.68 | 46.20          | 30.60 |
|           | 8 | 67.40          | 37.60 | 68.22          | 37.20 |
| LatentMTT | 4 | 52.86          | 37.82 | 52.44          | 36.20 |
|           | 8 | 66.42          | 45.28 | 69.20          | 42.84 |

Table 5. Quantitative results of dataset distillation where LatentDD methods are compared between  $f = 4$  (LPC =  $12 \times$  IPC) and  $f = 8$  (LPC = 48  $\times$  IPC).

ages with overly low resolution and a large downsampling factor f may induce too much loss of information that they may alter the distribution of the original images and finally counterweight the benefits brought by the quantity of latent codes (see examples and further analysis in Appendix). For instance, while reaching a mean accuracy of 44.38 when running LatentDC with  $f = 4$  on *Bird* at low resolution 64 and IPC 1, which is a comparable result at resolution 256 (46.72) or 512 (47.52) in Tab. [5,](#page-7-2) we have observed a remarkable drop from 67.28/69.46 to 48.68 with  $f = 8$ . In practical applications, it is essential to emphasize the need for maintaining this balance according to the characteristics of specific datasets.

To verify that our LatentDD methods can indeed achieve performance gain by distilling datasets instead of merely delivering higher LPC than IPC within the same storage budget, latent codes initialized from randomly selected dataset images without distillation are also evaluated. See Appendix for results and analysis.

# 5. Conclusions

In this work, we propose a new idea of transferring the dataset distillation (DD) processes from conventionally used pixel space to a more efficient latent space. Such transfer aims at three main problems commonly seen in the area of DD: P1: high time complexity; P2: high space complexity; and P3: low info-compactness, which have not been simultaneously settled in previous works. Practically, we propose LatentDD methods based on the three mainstream DD algorithms in pixel space by utilizing the latent codes encoded with a pretrained generic autoencoder as natural info-compact representations of pixel-level images.

Experimental results have validated that LatentDD methods can not only largely reduce time and space consumption thus enable these methods on higher resolution or data ratio, but also significantly boost the performance in DD tasks by delivering quantitatively more info-compact latent codes than pixel-level images within the same storage budget.

# References

- <span id="page-8-13"></span>[1] Sharat Agarwal, Himanshu Arora, Saket Anand, and Chetan Arora. Contextual diversity for active learning. In *ECCV*, 2020. [2](#page-1-1)
- <span id="page-8-14"></span>[2] Rahaf Aljundi, Min Lin, Baptiste Goujaud, and Yoshua Bengio. Gradient based sample selection for online continual learning. In *ICLR*, 2019. [2](#page-1-1)
- <span id="page-8-2"></span>[3] Lei Jimmy Ba and Rich Caruana. Do deep nets really need to be deep? In *NeurIPS*, 2014. [1](#page-0-1)
- <span id="page-8-26"></span>[4] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. In *NeurIPS Workshop*, 2020. [3](#page-2-2)
- <span id="page-8-3"></span>[5] Cristian Bucila, Rich Caruana, and Alexandru Niculescu-Mizil. Model compression. In *KDD*, 2006. [1](#page-0-1)
- <span id="page-8-6"></span>[6] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022. [1,](#page-0-1) [3,](#page-2-2) [6,](#page-5-4) [11](#page-10-0)
- <span id="page-8-7"></span>[7] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *CVPR*, 2023. [1,](#page-0-1) [3,](#page-2-2) [6,](#page-5-4) [11](#page-10-0)
- <span id="page-8-23"></span>[8] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *ICML*, 2023. [3](#page-2-2)
- <span id="page-8-30"></span>[9] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, K. Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *CVPR*, 2009. [6,](#page-5-4) [11](#page-10-0)
- <span id="page-8-28"></span>[10] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In *NeurIPS*, 2022. [3,](#page-2-2) [4,](#page-3-1) [7](#page-6-3)
- <span id="page-8-10"></span>[11] Prafulla Dhariwal and Alex Nichol. Diffusion models beat gans on image synthesis. In *NeurIPS*, 2021. [2](#page-1-1)
- <span id="page-8-22"></span>[12] Jiawei Du, Yidi Jiang, Vincent T. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *CVPR*, 2023. [3,](#page-2-2) [6](#page-5-4)
- <span id="page-8-15"></span>[13] Dan Feldman, Melanie Schmidt, and Christian Sohler. Turning big data into tiny data: Constant-size coresets for kmeans, pca and projective clustering. In *SODA*, 2013. [2](#page-1-1)
- <span id="page-8-17"></span>[14] Zongxion Geng, Jiahui andg Chen, Yuandou Wang, Herbert Woisetschlaeger, Sonja Schimmler, Ruben Mayer, Zhiming Zhao, and Chunming Rong. A survey on dataset distillation: Approaches, applications and future directions. In *IJCAI*, 2023. [2](#page-1-1)
- <span id="page-8-31"></span>[15] Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *CVPR*, 2018. [6,](#page-5-4) [11](#page-10-0)
- <span id="page-8-4"></span>[16] Geoffrey Hinton, Oriol Vinyals, and Jeff Dean. Distilling the knowledge in a neural network. In *NeurIPS Workshop*, 2014. [1](#page-0-1)

- <span id="page-8-11"></span>[17] Jonathan Ho, Ajay Jain, and Pieter Abbeel. Denoising diffusion probabilistic models. In *NeurIPS*, 2020. [2](#page-1-1)
- <span id="page-8-16"></span>[18] Rishabh Iyer, Ninad Khargonkar, Jeff Bilmes, and Himanshu Asnani. Submodular combinatorial information measures with applications in machine learning. In *ALT*, 2021. [2](#page-1-1)
- <span id="page-8-8"></span>[19] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient syntheticdata parameterization. In *ICML*, 2022. [1,](#page-0-1) [3,](#page-2-2) [4,](#page-3-1) [5,](#page-4-3) [6,](#page-5-4) [7,](#page-6-3) [11](#page-10-0)
- <span id="page-8-12"></span>[20] Alex Krizhevsky. Learning multiple layers of features from tiny images, 2009. [2](#page-1-1)
- <span id="page-8-29"></span>[21] Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.00719*, 2022. [3,](#page-2-2) [7](#page-6-3)
- <span id="page-8-20"></span>[22] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *ICML*, 2022. [2](#page-1-1)
- <span id="page-8-18"></span>[23] Shiye Lei and Dacheng Tao. A comprehensive survey to dataset distillation. *arXiv preprint arXiv:2301.05603*, 2023. [2](#page-1-1)
- <span id="page-8-21"></span>[24] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Dataset distillation using parameter pruning. *IEICE Transactions on Fundamentals of Electronics, Communications and Computer Sciences*, advpub:2023EAL2053, 2023. [3](#page-2-2)
- <span id="page-8-9"></span>[25] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *NeurIPS*, 2022. [1,](#page-0-1) [3,](#page-2-2) [4,](#page-3-1) [7](#page-6-3)
- <span id="page-8-27"></span>[26] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. DREAM: Efficient dataset distillation by representative matching. In *ICCV*, 2023. [3](#page-2-2)
- <span id="page-8-24"></span>[27] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, 2022. [3](#page-2-2)
- [28] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *ICLR*, 2021.
- <span id="page-8-25"></span>[29] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021. [3](#page-2-2)
- <span id="page-8-0"></span>[30] Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Björn Ommer. High-resolution image synthesis with latent diffusion models. In *CVPR*, 2022. [1,](#page-0-1) [2,](#page-1-1) [4](#page-3-1)
- <span id="page-8-5"></span>[31] Adriana Romero, Nicolas Ballas, Samira Ebrahimi Kahou, Antoine Chassang, Carlo Gatta, and Yoshua Bengio. Fitnets: Hints for thin deep nets. *arXiv preprint arXiv:1412.6550*, 2015. [1](#page-0-1)
- <span id="page-8-19"></span>[32] Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *Transactions on Machine Learning Research*, 2023. [2](#page-1-1)
- <span id="page-8-1"></span>[33] Christoph Schuhmann, Romain Beaumont, Richard Vencu, Cade Gordon, Ross Wightman, Mehdi Cherti, Theo Coombes, Aarush Katta, Clayton Mullis, Mitchell Wortsman, Patrick Schramowski, Srivatsa Kundurthy, Katherine Crowson, Ludwig Schmidt, Robert Kaczmarczyk, and Jenia Jitsev. Laion-5b: An open large-scale dataset for training next generation image-text models. In *NeurIPS*, 2022. [1](#page-0-1)

- <span id="page-9-4"></span>[34] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. In *ICLR*, 2018. [2](#page-1-1)
- <span id="page-9-3"></span>[35] Jiaming Song, Chenlin Meng, and Stefano Ermon. Denoising diffusion implicit models. In *ICLR*, 2021. [2](#page-1-1)
- <span id="page-9-16"></span>[36] Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth O. Stanley, and Jeff Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *ICML*, 2020. [13](#page-12-0)
- <span id="page-9-5"></span>[37] Mariya Toneva, Alessandro Sordoni, Remi Tachet des Combes, Adam Trischler, Yoshua Bengio, and Geoffrey J. Gordon. An empirical study of example forgetting during deep neural network learning. In *ICLR*, 2019. [2](#page-1-1)
- <span id="page-9-11"></span>[38] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. CAFE: Learning to condense dataset by aligning features. In *CVPR*, 2022. [3](#page-2-2)
- <span id="page-9-0"></span>[39] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-1) [2](#page-1-1)
- <span id="page-9-6"></span>[40] Max Welling. Herding dynamical weights to learn. In *ICML*, 2009. [2](#page-1-1)
- <span id="page-9-8"></span>[41] Ruonan Yu, Songhua Liu, and Xinchao Wang. A comprehensive survey to dataset distillation. *arXiv preprint arXiv:2301.07014*, 2023. [2](#page-1-1)
- <span id="page-9-15"></span>[42] Sangdoo Yun, Dongyoon Han, Seong Joon Oh, Sanghyuk Chun, Junsuk Choe, and Youngjoon Yoo. Cutmix: Regularization strategy to train strong classifiers with localizable features. In *ICCV*, 2019. [11](#page-10-0)
- <span id="page-9-13"></span>[43] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Xu Dongkuan. Accelerating dataset distillation via model augmentation. In *CVPR*, 2023. [3](#page-2-2)
- <span id="page-9-1"></span>[44] Bo Zhao and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021. [1,](#page-0-1) [2,](#page-1-1) [5](#page-4-3)
- <span id="page-9-9"></span>[45] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021. [2,](#page-1-1) [5,](#page-4-3) [6,](#page-5-4) [11](#page-10-0)
- <span id="page-9-2"></span>[46] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023. [1,](#page-0-1) [2,](#page-1-1) [5,](#page-4-3) [6](#page-5-4)
- <span id="page-9-10"></span>[47] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *CVPR*, 2023. [3,](#page-2-2) [7](#page-6-3)
- <span id="page-9-7"></span>[48] Daquan Zhou, Kai Wang, Jianyang Gu, Xiangyu Peng, Dongze Lian, Yifan Zhang, Yang You, and Jiashi Feng. Dataset quantization. In *ICCV*, 2023. [2](#page-1-1)
- <span id="page-9-12"></span>[49] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *NeurIPS*, 2022. [3](#page-2-2)
- <span id="page-9-14"></span>[50] Dongyao Zhu, Bowen Lei, Jie Zhang, Yanbo Fang, Ruqi Zhang, Yiqun Xie, and Dongkuan Xu. Rethinking data distillation: Do not overlook calibration. In *ICCV*, 2023. [3](#page-2-2)

# <span id="page-10-0"></span>Appendix

# A. Implementation Detail

When running our LatentDD algorithms LatentDC, LatentDM and LatentMTT, we generally follow the default settings of the original DC, DM and MTT respectively. Besides, we fix some settings across the baselines and our methods to ensure that the comparisons are as fair as possible. In this section, we will introduce basic settings and implementation details of LatentDD, including

- hyperparameters in Appendix [A.1,](#page-10-1)
- training and evaluation protocol in Appendix [A.2,](#page-10-2)
- analysis of autoencoder Appendix [A.3,](#page-10-3)
- dataset selection and preprocessig in Appendix [A.4,](#page-10-4)
- pesudocodes of the three LatentDD methods in Appendix [A.5.](#page-12-1)

# <span id="page-10-1"></span>A.1. Hyperparameters

The hyperparameters under different experimental settings are listed in Tab. [6,](#page-11-0) except for other hyperparameters that have been introduced in our main paper or by previous works. Base learning rate  $\eta_{\text{base}}$  can be seen as the learning rate *per latent code*, as the cross-entropy loss  $\mathcal L$  will be averaged among the latent codes. Hence the real learning rate  $\eta_{L_{\mathcal{S}}}$  updating the latent codes is set to

$$
\eta_{L_{\mathcal{S}}} = \eta_{\text{base}} \times \text{LPC}.\tag{10}
$$

Note that these settings are just used to produce the results reported in this work, they are not guaranteed to be the optimized settings to render the best performance.

# <span id="page-10-2"></span>A.2. Training/Evaluation Protocol

Following previous works, we adopt ConvNet [\[15\]](#page-8-31) as the network architecture primarily used in the experiments. By default, ConvNet of depth  $d$  (denoted by ConvNetD $d$ ) consists of d blocks followed by a single linear layer, where each block is a sequence of convolution with kernel size 3 and padding 1, instance normalization, ReLU activation and average pooling by factor 2. We set the depth  $d$  of the ConvNet according to the resolution of the latent codes (during training stage) or pixel-level images (during evaluation stage), refer to Tab. [6](#page-11-0) for details. The learning rate updating the network parameters during evaluation stage is set to 0.01.

As explained in the main paper, we do not apply Differentiable Siamese Augmentation (DSA) [\[45\]](#page-9-9) during the training processes because many augmenting transformations designed for pixel-level images do not fit for latent codes. However, we use DSA in the evaluation processes in pixel space. Following Kim et al. [\[19\]](#page-8-8), we additionally replace cutout with CutMix [\[42\]](#page-9-15), which is a calibration technique to alleviate the over-confident issue of the models trained on limited data.

<span id="page-10-3"></span>

### A.3. Analysis of Autoencoder

In Figure [4](#page-11-1) we show some examples of reconstructing original images at different resolutions with the autoencoder used in LatentDD, with  $f = 8$  and without pre-upsampling and post-downsampling. Although the autoencoder can be applied to a variety of resolutions, we observe that the lower the resolution is, the more details will be lost in the reconstruction because the resolution of the latent codes becomes too small to encode spatial information. While the reconstructed image of resolution 512 only loses the finest details that are hardly perceptible, the image of resolution 32 loses too much information to even tell that it depicts a bird. Therefore, the experiments in our paper are mainly rolled out with  $f = 4$  where we adopt the (1) upsampling; (2) encoding; (3) LatentDD; (4) decoding; and (5) downsampling procedure for a generalized applicability on various resolutions, though in high-resolution scenarios  $f = 8$  may produce better results due to the greater quantity of latent codes. In extreme cases where the dataset resolution is too low, we suggest trying  $f = 2$  with pre-upsampling and post-downsampling by factor of 4 to avoid excessive loss of information, or replace with another pretrained generic autoencoder which suits low-resolution images. For other data modality (*e.g*., text, audio, video) where autoencoders may not be available, the idea of LatentDD is still applicable if there is a way to compress the data into smaller size without altering the dataset distribution and reconstruct them back to the original form, depending on the specific modality.

<span id="page-10-4"></span>

#### A.4. Dataset Selection and Preprocessing

We list the categories (and their indices) that are included in the ImageNet [\[9\]](#page-8-30) subsets below.

- Bird: peacock (84), flamingo (130), macaw (88), pelican (144), king penguin (145), bald eagle (22), toucan (96), ostrich (9), black swan (100), sulphur-crested cockatoo (89).
- Fruit: pineapple (953), banana (954), strawberry (949), orange (950), lemon (951), pomegranate (957), fig (952), bell pepper (945), cucumber (943), granny smith (948).
- Woof: australian terrier (193), border terrier (182), samoyed (258), beagle (162), shih-tzu (155), english foxhound (167), rhodesian ridgeback (159), dingo (273), golden retriever (207), old english sheepdog (229).
- Cat: tabby (281), tiger cat (282), persian cat (283), siamese cat (284), egyptian cat (285), lion (291), tiger (292), jaguar (290), snow leopard (289), lynx (287).
- Nette: tench (0), english springer (217), cassette player (482), chain saw (491), church (497), french horn (566), garbage truck (569), gas pump (571), golf ball (574), parachute (701).

For preprocessing the images of ImageNet, we follow the previous works  $[6, 7]$  $[6, 7]$  $[6, 7]$  to apply a sequence of  $(1)$  nor-

<span id="page-11-0"></span>

|      |              |                | Hyperparameter                                  | LatentDC        | LatentDM | LatentMTT        |
|------|--------------|----------------|-------------------------------------------------|-----------------|----------|------------------|
|      |              |                | iteration $T$                                   | 1000            | 1000     | 5000             |
| Res. | <b>IPC</b>   | $\overline{f}$ | max starting epoch $T^+$                        |                 |          | 5                |
|      |              |                | model learning rate $\eta_{\tilde{\theta}}$     | 0.01            |          | $0.01$ (initial) |
|      |              |                | $\eta_{\tilde{\theta}}$ learning rate $\lambda$ |                 |          | $10^{-6}$        |
|      |              |                | outer loop / expert epoch M                     | $\overline{10}$ |          | 1                |
|      |              |                | inner loop / student step $N$                   | 50              |          | 40               |
|      |              | $\overline{4}$ | base learning rate $\eta_{\text{base}}$         | 0.05            | 0.5      | 50               |
|      |              |                | batch size                                      | 64              | 64       | 64               |
|      | $\mathbf{1}$ |                | ConvNet depth (train / eval)                    | 4/6             | 4/6      | 4/6              |
|      |              |                | outer loop / expert epoch $M$                   | 10              |          | 1                |
|      |              |                | inner loop / student step $N$                   | 50              |          | 60               |
| 256  |              | 8              | base learning rate $\eta_{\text{base}}$         | 0.05            | 0.1      | 1                |
|      |              |                | batch size                                      | 64              | 64       | 64               |
|      |              |                | ConvNet depth (train / eval)                    | 3/6             | 3/6      | 3/6              |
|      |              |                | outer loop / expert epoch M                     | 10              |          | 1                |
|      |              |                | inner loop / student step $N$                   | 50              |          | 80               |
|      | 10           | 4              | base learning rate $\eta_{\text{base}}$         | 0.05            | 0.5      | 10               |
|      |              |                | batch size                                      | 64              | 64       | 64               |
|      |              |                | ConvNet depth (train / eval)                    | 4/6             | 4/6      | 4/6              |
|      |              |                | outer loop / expert epoch $M$                   | 10              |          | 1                |
|      |              |                | inner loop / student step $N$                   | 50              |          | 40               |
|      |              | $\overline{4}$ | base learning rate $\eta_{\text{base}}$         | 0.25            | 0.5      | 500              |
|      |              |                | batch size                                      | 32              | 32       | 32               |
| 512  | 1            |                | ConvNet depth (train / eval)                    | 5/7             | 5/7      | 5/7              |
|      |              |                | outer loop / expert epoch M                     | 10              |          | 1                |
|      |              |                | inner loop / student step $N$                   | 50              |          | 60               |
|      |              | 8              | base learning rate $\eta_{\mathrm{base}}$       | 0.05            | 0.1      | 20               |
|      |              |                | batch size                                      | 16              | 16       | 16               |
|      |              |                | ConvNet depth (train / eval)                    | 4/7             | 4/7      | 4/7              |

Table 6. Hyperparameter settings of the experiments on LatentDD algorithms. A hyphen - indicates that the hyperparameter is not applicable to this algorithm.

<span id="page-11-1"></span>Image /page/11/Figure/2 description: This image displays a comparison between original and reconstructed images of a robin perched on a branch. The top row shows the original images, progressively becoming clearer from left to right, with resolutions labeled as 32, 64, 128, 256, and 512. The bottom row shows the reconstructed images corresponding to these resolutions. The first reconstructed image (32) is very blurry. The subsequent reconstructed images show increasing levels of detail, with the images at 256 and 512 resolutions appearing sharp and clear, closely resembling the original images.

Figure 4. Comparison between the original images and the reconstructed images (encoded from the original images and decoded back by the autoencoder) at different resolutions from 32 to 512. Zoom in to see details of the high-resolution images.

<span id="page-12-5"></span><span id="page-12-0"></span>

| Method    | Res. 256 |       |       |       |       |        |       | Res. 512 |       |
|-----------|----------|-------|-------|-------|-------|--------|-------|----------|-------|
|           | IPC 1    |       |       |       |       | IPC 10 |       | IPC 1    |       |
|           | Bird     | Fruit | Woof  | Cat   | Nette | Bird   | Fruit | Bird     | Fruit |
| Initial   | 32.36    | 23.24 | 20.20 | 29.04 | 43.72 | 75.28  | 45.80 | 32.56    | 22.96 |
| LatentDC  | 46.72    | 30.12 | 28.96 | 38.08 | 55.92 | 80.44  | 51.60 | 47.52    | 29.68 |
| LatentDM  | 47.08    | 30.68 | 28.00 | 36.28 | 56.08 | 77.20  | 47.76 | 46.20    | 30.60 |
| LatentMTT | 52.86    | 37.82 | 39.84 | 41.42 | 62.86 | 78.44  | 52.46 | 52.44    | 36.20 |

Table 7. Comparison between the latent codes before distillation (Initial), and after running LatentDD algorithms on ImageNet subsets, following the setting of  $f = 4$  and LPC =  $12 \times$  IPC. The mean classification accuracy among five times of evaluations is reported.

<span id="page-12-6"></span>

| Method           | Bird 256 IPC 1 |          |       |         |
|------------------|----------------|----------|-------|---------|
|                  | ConvNet        | ResNet18 | VGG11 | AlexNet |
| <b>DSA</b>       | 30.52          | 14.84    | 20.68 | 24.24   |
| <b>IDC</b>       | 36.28          | 40.24    | 30.20 | 32.24   |
| <b>GLaD DC</b>   | 30.32          | 23.20    | 22.08 | 19.44   |
| <b>LatentDC</b>  | 46.72          | 56.00    | 49.32 | 37.56   |
| <b>DM</b>        | 27.64          | 13.44    | 17.40 | 23.64   |
| <b>GLaD DM</b>   | 28.84          | 26.24    | 18.24 | 19.08   |
| <b>LatentDM</b>  | 47.08          | 56.00    | 47.56 | 37.12   |
| <b>MTT</b>       | 35.80          | 18.72    | 22.36 | 20.84   |
| <b>GLaD MTT</b>  | 32.20          | 39.56    | 23.28 | 19.80   |
| <b>FTD</b>       | 35.96          | 19.28    | 21.96 | 23.92   |
| <b>LatentMTT</b> | 52.86          | 57.76    | 52.96 | 39.64   |

Table 8. Quantitative results of dataset distillation experiments on ImageNet subset *Bird*, evaluated on cross-architecture networks (ResNet18, VGG11 and AlexNet). LatentDD algorithms follow the setting of  $f = 4$ , where LPC =  $12 \times$  IPC. The mean classification accuracy among five times of evaluations is reported.

malization; (2) resizing to target resolution; and (3) center cropping.

# <span id="page-12-1"></span>A.5. Pseudocodes of LatentDD Algorithms

The pseudocodes of the three LatentDD methods, LatentDC, LatentDM and LatentMTT are respectively shown in Algorithm [1,](#page-13-0) [2](#page-13-1) and [3.](#page-13-2)

# B. Additional Results

In this section, we illustrate some additional experimental results that have not been shown in the main paper due to page limitation. These results include

- the evaluation on the initial latent codes encoded from randomly selected dataset images before distillation in Appendix [B.1,](#page-12-2)
- the performance of LatentDD methods when evaluated on cross-architecture networks in Appendix [B.2,](#page-12-3)
- some image samples decoded from the distilled latent codes in Appendix [B.3.](#page-12-4)

### <span id="page-12-2"></span>B.1. Latent Codes Before vs. After Distillation

In Tab. [7](#page-12-5) (row *Initial*) we list the classification accuracy of the latent codes initialized from random dataset images before distillation. Compared with the results of LatentDD algorithms, we may conclude that our LatentDD methods do not achieve the best performance only because they deliver more latent codes than pixel-level images within the same storage budget. Instead they still improve the latent codes during the distillation processes, albeit the performance gain is relatively small when  $IPC = 10$  probably because the potential for further improvement is limited when we already have 120 latent codes per class.

## <span id="page-12-3"></span>B.2. Cross-architecture Performance

One of the goals of dataset distillation is that we expect the distilled datasets can achieve good performance on any network architecture, not limited to the one adopted during distillation, so that these distilled datasets can be utilized on downstream tasks such as neural architecture search [\[36\]](#page-9-16). In Tab. [8,](#page-12-6) we demonstrate the uniformly good performance of LatentDD algorithms on some cross-architecture networks. As all the baselines are trained on the same architecture used for evaluation (*e.g*. ConvNetD6 for resolution 256), they are more or less overfitted to the specific architecture as a trend of performance drop can be observed when evaluated on other architectures. On the contrary, our LatentDD methods are relatively robust to architecture changes. It is also worth mentioning that the results of LatentDD methods on ConvNet in Tab. [8](#page-12-6) (and also the tables in the main paper which include performance of LatentDD) are already cross-architecture to some extent, since the depths of the ConvNets used for training and evaluation are different.

#### <span id="page-12-4"></span>B.3. Qualitative Results

For a more comprehensive illustration of the experimental results, we depict some image samples decoded from the distilled latent codes on the ImageNet subset *Bird* (with categories listed in Appendix [A.4\)](#page-10-4) by LatentDC, LatentDM and LatentMTT in Figures [5](#page-14-0) to [7](#page-16-0) respectively.

Algorithm 1: LatentDC

**Input:** Real dataset in latent space  $L_{\mathcal{T}} = \{(l_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  where  $l_i = \mathcal{E}(x_i)$ ; iteration T; outer loop M; inner loop N; number of classes C; classification loss CrossEntropy( $\cdot, \cdot$ ); gradient matching loss  $D(\cdot, \cdot)$ ; learning rate of synthetic dataset  $\eta_{L_S}$  and model parameters  $\eta_{\tilde{\theta}}$ .

1 Initialize synthetic dataset in latent space  $L_{\mathcal{S}} = \{(\tilde{l}_i, \tilde{y}_i)\}_{i=1}^{|\mathcal{S}|}$  with randomly sampled  $(l_i, y_i) \in L_{\mathcal{T}}$ .

2 for iter =  $0, \ldots, T-1$  do

3 Initialize model  $\phi_{\tilde{\theta}}$  with random parameters  $\theta$ . 4  $\vert$  for  $m = 0, ..., M - 1$  do  $\mathfrak{s}$  |  $\mathcal{L}_{\text{match}} = 0$ 6 **for**  $c = 0, ..., C - 1$  do  $7 \mid \cdot \mid$  Randomly sample a real batch  $B_{L_{\mathcal{T}}} \subseteq L_{\mathcal{T}_c}$  and a synthetic batch  $B_{L_{\mathcal{S}}} \subseteq L_{\mathcal{S}_c}$  of class c.  $\mathbf{B} \quad \begin{array}{|c|c|} \hline \end{array} \quad \mathcal{L}^{L\, \mathcal{T}}_c(\tilde{\theta}) = \frac{1}{|B_{L\, \mathcal{T}}|} \sum_{(l,y)\in B_{L\, \mathcal{T}}} \mathrm{CrossEntropy}(\phi_{\tilde{\theta}}(l), y)$  $\mathcal{L}_{c}^{Ls}(\tilde{\theta})=\frac{1}{|B_{LS}|}\sum_{(\tilde{l},\tilde{y})\in B_{L_{\mathcal{S}}}}\mathrm{CrossEntropy}(\phi_{\tilde{\theta}}(\tilde{l}),\tilde{y})$  $10 \quad \left[ \quad \right] \quad \left[ \quad \mathcal{L}_{\text{match}} \leftarrow \mathcal{L}_{\text{match}} + D(\nabla_{\tilde{\theta}} \mathcal{L}_{c}^{L_{\mathcal{S}}}(\tilde{\theta}), \nabla_{\tilde{\theta}} \mathcal{L}_{c}^{L_{\mathcal{T}}}(\tilde{\theta})) \right]$ 11  $\Big|$   $L_{\mathcal{S}} \leftarrow L_{\mathcal{S}} - \eta_{L_{\mathcal{S}}} \nabla_{L_{\mathcal{S}}} \mathcal{L}_{\text{match}}$  // update synthetic latent codes with gradient matching 12 **for**  $n = 0, ..., N - 1$  do  $13$   $\Big|\quad\Big|\quad\Big|\quad \tilde\theta\leftarrow \tilde\theta - \eta_{\tilde\theta}\nabla_{\tilde\theta}\mathcal{L}^{L\tau}(\tilde\theta)\ /\ /\ \text{train the model on real latent codes}\Big|$ Output:  $L_{\mathcal{S}}$ 

<span id="page-13-0"></span>

## Algorithm 2: LatentDM

**Input:** Real dataset in latent space  $L_{\mathcal{T}} = \{(l_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  where  $l_i = \mathcal{E}(x_i)$ ; iteration T; learning rate of synthetic dataset  $\eta_{L_{\mathcal{S}}}$ . 1 Initialize synthetic dataset in latent space  $L_{\mathcal{S}} = \{(\tilde{l}_i, \tilde{y}_i)\}_{i=1}^{|\mathcal{S}|}$  with randomly sampled  $(l_i, y_i) \in L_{\mathcal{T}}$ . 2 for iter =  $0, \ldots, T-1$  do

3 Initialize model  $\phi_{\tilde{\theta}}$  with random parameters  $\theta$ . 4  $\mathcal{L}_{\text{match}} = 0$ 5  $\vert$  for  $c = 0, ..., C - 1$  do 6 Randomly sample a real batch  $B_{L_{\mathcal{T}}} \subseteq L_{\mathcal{T}_c}$  and a synthetic batch  $B_{L_{\mathcal{S}}} \subseteq L_{\mathcal{S}_c}$  of class c.  $\mathcal{T} \quad \left| \quad \right| \quad \mathcal{L}_{\text{match}} \leftarrow \mathcal{L}_{\text{match}} + \| \frac{1}{|B_{L_{\mathcal{T}}}|} \sum_{(l,y) \in B_{L_{\mathcal{T}}}} \phi_{\tilde{\theta}}(l) - \frac{1}{|B_{L_{\mathcal{S}}}|} \sum_{(\tilde{l},\tilde{y}) \in B_{L_{\mathcal{S}}}} \phi_{\tilde{\theta}}(\tilde{l}) \|^2$  $L_S \leftarrow L_S - \eta_{L_S} \nabla_{L_S} \mathcal{L}_{match}$  // update synthetic latent codes with distribution matching Output: L<sub>S</sub>

<span id="page-13-1"></span>

### Algorithm 3: LatentMTT

**Input:** Real dataset in latent space  $L_{\mathcal{T}} = \{(l_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  where  $l_i = \mathcal{E}(x_i)$ ; iteration  $T$ ; maximum starting epoch  $T^+$ ; expert epoch M; student step N; number of classes C; classification loss CrossEntropy( $\cdot, \cdot$ ); learning rate of synthetic dataset  $\eta_{L_S}$ ; initial learning rate of model parameters  $\eta_{\tilde{\theta}}^0$ ; learning rate of updating the the learning rate of model parameters  $\lambda$ .

1 Initialize synthetic dataset in latent space  $L_S = \{(\tilde{l}_i, \tilde{y}_i)\}_{i=1}^{|S|}$  with randomly sampled  $(l_i, y_i) \in L_{\mathcal{T}}$ .

- 2 Initialize learning rate of model parameters  $\eta_{\tilde{\theta}} = \eta_{\tilde{\theta}}^0$ .
- 3 for iter =  $0, \ldots, T-1$  do
- 4 Randomly sample a starting epoch  $t \sim \mathcal{U}(\{0, \ldots, T^+\})$ .
- 5 Randomly choose an expert trajectory from the buffer:  $\tilde{\theta}_t^{L\tau}$ , ...,  $\tilde{\theta}_{t+M}^{L\tau}$ .
- 6 Initialize student network  $\phi$  with parameters  $\tilde{\theta} = \tilde{\theta}_t^L \tau$ .
- 7  $\vert$  for  $n = 0, ..., N 1$  do
- 8 Randomly sample a synthetic batch  $B_{L,s} \subseteq L_{\mathcal{S}}$ .
- 9  $\mathcal{L}^{L_S}(\tilde{\theta}) = \frac{1}{|B_{L_S}|} \sum_{(\tilde{l},\tilde{y}) \in B_{L_S}} \text{CrossEntropy}(\phi_{\tilde{\theta}}(\tilde{l}), \tilde{y})$
- 10  $\left[\begin{array}{c} \tilde{\theta}\leftarrow\tilde{\theta}-\eta_{\tilde{\theta}}\nabla_{\tilde{\theta}}\mathcal{L}^{L_{\mathcal{S}}}(\tilde{\theta})\end{array}\right]/$  student step
- $11 \quad \Big| \quad \mathcal{L}_{\text{match}} = \|\tilde{\theta} \tilde{\theta}^{L\tau}_{t+M}\|_2^2 / \|\tilde{\theta}^{L\tau}_{t} \tilde{\theta}^{L\tau}_{t+M}\|_2^2$
- $12 \left| \right.$   $L_S \leftarrow L_S \eta_{L_S} \nabla_{L_S} L_{\text{match}}$  // update synthetic latent codes with trajectory matching
- 13  $\eta_{\tilde{\theta}} \leftarrow \eta_{\tilde{\theta}} \lambda \nabla_{\eta_{\tilde{\theta}}} \mathcal{L}_{\text{match}}$

<span id="page-13-2"></span>Output:  $L_{\mathcal{S}}$ 

<span id="page-14-0"></span>Image /page/14/Picture/0 description: The image displays a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each smaller image appears to be a generated or stylized depiction of a bird, with varying colors, poses, and backgrounds. Some images are clearer than others, showing recognizable bird features like beaks, feathers, and wings, while many are more abstract or impressionistic. The overall impression is a diverse collection of bird imagery, possibly generated by an AI model, with a focus on vibrant colors and artistic interpretations.

Figure 5. The images decoded from the distilled latent codes by LatentDC with  $f = 4$  on *Bird*, resolution 256, IPC 1 (LPC 12). Each row respectively presents the category of peacock, flamingo, macaw, pelican, king penguin, bald eagle, toucan, ostrich, black swan, sulphurcrested cockatoo.

Image /page/15/Picture/0 description: A grid of 100 images, each depicting a bird or a group of birds, is presented. The images are arranged in 10 rows and 10 columns. The birds featured include flamingos, parrots, eagles, penguins, toucans, swans, and cockatoos, among others. The images appear to be generated or processed, with a somewhat abstract or painterly quality in many of them. The overall impression is a diverse collection of avian subjects presented in a structured, grid format.

Figure 6. The images decoded from the distilled latent codes by LatentDM with  $f = 4$  on *Bird*, resolution 256, IPC 1 (LPC 12). Each row respectively presents the category of peacock, flamingo, macaw, pelican, king penguin, bald eagle, toucan, ostrich, black swan, sulphurcrested cockatoo.

<span id="page-16-0"></span>Image /page/16/Picture/0 description: The image is a grid of 80 smaller images, each depicting a bird. The birds are varied and include peacocks, flamingos, parrots, penguins, eagles, toucans, ostriches, swans, and cockatoos. The images are arranged in 10 rows and 8 columns. Some images are clear and detailed, while others are more abstract or impressionistic, suggesting they may be generated or processed images. The overall impression is a diverse collection of avian subjects presented in a grid format.

Figure 7. The images decoded from the distilled latent codes by LatentMTT with  $f = 4$  on *Bird*, resolution 256, IPC 1 (LPC 12). Each row respectively presents the category of peacock, flamingo, macaw, pelican, king penguin, bald eagle, toucan, ostrich, black swan, sulphur-crested cockatoo.
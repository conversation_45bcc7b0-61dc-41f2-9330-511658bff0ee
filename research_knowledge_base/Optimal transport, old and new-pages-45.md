Synthetic treatment of Ricci curvature

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.

The last part of these notes is devoted to a direction of research which was mainly explored by <PERSON><PERSON>, <PERSON> and myself from 2004 on.

In Chapter 17 it was proven that lower Ricci curvature bounds influence displacement convexity properties of certain classes of functionals; but also that these properties characterize lower Ricci curvature bounds. So we may "transform the theorem into a definition" and express the property "Ricci curvature is bounded below by  $K$ " in terms of certain displacement convexity properties. This approach is synthetic, in the sense that it does not rely on analytic computations (of the Ricci  $tensor...$ , but rather on the properties of certain objects which play an important role in some geometric arguments.

This point of view has the advantage of applying to nonsmooth spaces, just as lower (or upper) sectional curvature bounds can be defined in nonsmooth metric spaces by <PERSON><PERSON><PERSON>'s method. An important difference however is that the notion of generalized Ricci curvature will be defined not only in terms of distances, but also in terms of reference measures. So the basic object will not be a metric space, but a metric-measure space, that is, a metric space equipped with a reference measure.

Chapters 26 and 27 are preparatory. In Chapter 26 I shall try to convey in some detail the meaning of the word "synthetic", with a simple illustration about convex functions; then Chapter 27 will be devoted to some reminders about the convergence of metric-measure spaces.

The next two chapters constitute the core of this part. In Chapter 28 I will consider optimal transport in possibly nonsmooth spaces, and establish various properties of stability of optimal transport under convergence of metric-measure spaces. Then in Chapter 29 I shall present a synthetic definition of the curvature-dimension condition  $CD(K, N)$ in a nonsmooth context, and prove that it too is stable. Here is a geometric consequence of these results that can be stated without any reference to optimal transport: If a Riemannian manifold is the limit of a sequence of  $CD(K, N)$  Riemannian manifolds, then it, too, satisfies  $CD(K,N).$ 

The last chapter will present the state of the art concerning the qualitative geometric and analytic properties enjoyed by metric-measure spaces satisfying curvature-dimension conditions, with complete proofs.

The issues discussed in this part are concisely reviewed in my seminar proceedings [821] (in French), or the survey paper by Lott [573], whose presentation is probably more geometer-friendly.

Convention: Throughout Part III, geodesics are constant-speed, minimizing geodesics.

## Analytic and synthetic points of view

The present chapter is devoted to a simple pedagogical illustration of the opposition between the "analytic" and "synthetic" points of view. Consider the following two definitions for convexity on  $\mathbb{R}^n$ :

(i) A convex function is a function  $\varphi$  which is twice continuously differentiable, and whose Hessian  $\nabla_x^2 \varphi$  is nonnegative at each  $x \in \mathbb{R}^n$ ;

(ii) A convex function is a function  $\varphi$  such that for all  $x, y \in \mathbb{R}^n$ , and  $\lambda \in [0, 1],$ 

$$
\varphi((1 - \lambda)x + \lambda y) \le (1 - \lambda)\varphi(x) + \lambda\varphi(y).
$$

How can we compare these two definitions?

1) When applied to  $C<sup>2</sup>$  functions, both definitions coincide, but the second one is obviously *more general*. Not only is it expressed without any reference to second differentiability, but there are examples, such as  $\varphi(x) = |x|$ , which satisfy (ii) but not (i). So Definition (ii) really is an extension of Definition (i).

2) Definition (ii) is more stable than Definition (i). Here is what I mean by that: Take a sequence  $(\varphi_k)_{k\in\mathbb{N}}$  of convex functions, converging to some other function  $\varphi$ ; how do I know that  $\varphi$  is convex? To pass to the limit in Definition (i), I would need the convergence to be very strong, say in  $C^2(\mathbb{R}^n)$ . (Let's forget here about the notion of distributional convergence, which would solve the problem but is much less elementary.) On the other hand, I can pass to the limit in Definition (ii) assuming only, say, pointwise convergence. So Definition (ii) is much easier to "pass to the limit in" — even if the limit is known to be smooth.

3) Definition (ii) is also a better *starting point* for studying properties of convex functions. In this set of notes, most of the time, when I used some convexity, it was via (ii), not (i).

4) On the other hand, if I give you a particular function (by its explicit analytic expression, say), and ask you whether it is convex, it will probably be a nightmare to check Definition (ii) directly, while Definition (i) might be workable: You just need to compute the second derivative and check its sign. Probably this is the method that will work most easily for the huge majority of candidate convex functions that you will meet in your life, if you don't have any extra information on them (like they are the limit of some family of functions.. .).

5) Definition (i) is naturally local, while Definition (ii) is global (and probably this is related to the fact that it is so difficult to check). In particular, Definition (i) involves an object (the second derivative) which can be used to quantify the "strength of convexity" at each point. Of course, one may define a convex function as a function satisfying (ii) locally, i.e. when x and y stay in the neighborhood of any given point; but then locality does not enter in such a simple way as in (i), and the issue immediately arises whether a function which satisfies (ii) locally, also satisfies (ii) globally.

In the above discussion, Definition (i) can be thought of as **analytic** (it is based on the computation of certain objects), while Definition (ii) is synthetic (it is based on certain qualitative properties which are the basis for proofs). Observations 1–5 above are in some sense typical: synthetic definitions ought to be more general and more stable, and they should be usable directly to prove interesting results; on the other hand, they may be difficult to check in practice, and they are usually less precise (and less "local") than analytic definitions.

In classical Euclidean geometry, the analytic approach consists in introducing Cartesian coordinates and making computations with equations of lines and circles, sines and cosines, etc. The synthetic approach, on the other hand, is more or less the one that was used already by ancient Greeks (and which is still taught, or at least should be taught, to our kids, for developing the skill of proof-making): It is not based on computations, but on axioms  $\dot{a}$  la Euclid, qualitative properties of lines, angles, circles and triangles, construction of auxiliary points, etc. The analytic approach is conceptually simple, but sometimes leads to very horrible computations; the synthetic approach is often lighter, but requires better intuition and clever elementary arguments. It is also usually (but this is of course a matter of taste) more elegant.

In "Riemannian" geometry, curvature is traditionally defined by a purely analytic approach: From the Riemannian scalar product one can compute several functions which are called sectional curvature, Ricci curvature, scalar curvature, etc. For instance, for any  $x \in M$ , the sectional curvature at point  $x$  is a *function* which associates to each 2-dimensional plane  $P \subset T_xM$  a number  $\sigma_x(P)$ , for which there is an explicit expression in terms of a basis of  $P$ , and a certain combination of derivatives of the metric at x. Intuitively,  $\sigma_x(P)$  measures the speed of convergence of geodesics that start at  $x$ , with velocities spanning the plane P. A lot of geometric information can be retrieved from the bounds on the sectional curvature. Then a space is said to have nonnegative sectional curvature if  $\sigma_x(P)$  is nonnegative, for all P and for all  $x$ .

However, there is also a famous synthetic point of view on sectional curvature, due to Alexandrov. In Alexandrov's approach one does not try to define what the curvature is, but what it means to have nonnegative curvature: By definition, a geodesic space  $(\mathcal{X}, d)$  is said to have Alexandrov curvature bounded below by  $K$ , or to be an **Alexandrov** space with curvature bounded below by  $K$ , if triangles in  $\mathcal X$  are no more "skinny" than reference triangles drawn on the model space  $\mathbb{R}^2$ . More precisely: If  $xyz$  is a triangle in  $\mathcal{X}, x_0y_0z_0$  is a triangle drawn on  $\mathbb{R}^2$  with  $d(x_0, y_0) = d(x, y), d(y_0, z_0) = d(y, z), d(z_0, x_0) = d(z, x), x'$ is a midpoint between y and z, and  $x'_0$  a midpoint between  $y_0$  and  $z_0$ , then one should have  $d(x_0, x'_0) \leq d(x, x')$ . (See Figure 26.1.)

There is an excellent analogy with the previous discussion for convex functions. The Alexandrov definition is equivalent to the analytic one in case it is applied to a smooth Riemannian manifold; but it is more general, since it applies for instance to a cone (say, the two-dimensional cone embedded in  $\mathbb{R}^3$ , constructed over a circular basis). It is also more stable; in particular, it passes to the limit under Gromov–Hausdorff convergence, a notion that will be described in the sequel. It can still be used as the starting point for many properties involving sectional curvature. On the other hand, it is in general difficult to check directly, and there is no associated notion of curvature (when one says "Alexandrov space of nonnegative curvature", the words "nonnegative" and "curvature" do not make sense independently of each other).

754 26 Analytic and synthetic points of view

Image /page/7/Figure/1 description: The image displays two geometric figures. The figure on the left is a curved triangle with vertices labeled x, y, and z. A line segment connects vertex x to a point on the base between y and z. The figure on the right is a straight-sided triangle with vertices labeled x0, y0, and z0. A line segment connects vertex x0 to a point on the base between y0 and z0.

Fig. 26.1. The triangle on the left is drawn in  $\mathcal{X}$ , the triangle on the right is drawn on the model space  $\mathbb{R}^2$ ; the lengths of their edges are the same. The thin geodesic lines go through the apex to the middle of the basis; the one on the left is longer than the one on the right. In that sense the triangle on the left is fatter than the triangle on the right. If all triangles in  $\mathcal X$  look like this, then  $\mathcal X$  has nonnegative curvature. (Think of a triangle as the belly of some individual, the belt being the basis, and the neck being the apex; of course the line going from the apex to the middle of the basis is the tie. The fatter the individual, the longer his tie should be.)

Image /page/7/Figure/3 description: The image displays two geometric shapes. The shape on the left is a sector of a circle with vertices labeled x, y, and z. A shaded region, resembling a narrow parallelogram, is depicted within this sector, extending from vertex x towards the base connecting y and z. The shape on the right is a triangle with vertices labeled x0, y0, and z0. A similar shaded parallelogram is shown within this triangle, originating from vertex x0 and extending towards the base connecting y0 and z0.

Still there is a generalization of what it means to have curvature bounded below by  $K \in \mathbb{R}$ , where K is an arbitrary real number, not necessarily 0. It is obtained by replacing the model space  $\mathbb{R}^2$  by the model space with constant curvature  $K$ , that is:

- the sphere  $S^2(1/\sqrt{K})$  with radius  $R = 1/\sqrt{K}$ , if  $K > 0$ ;
- the plane  $\mathbb{R}^2$ , if  $K = 0$ ;
- the hyperbolic space  $\mathbb{H}(1/\sqrt{|K|})$  with "hyperbolic radius"  $R =$  $1/\sqrt{|K|}$ , if  $K < 0$ ; this can be realized as the half-plane  $\mathbb{R} \times (0, +\infty)$ , equipped with the metric  $g_{(x,y)}(dx\,dy) = (dx^2 + dy^2)/(|K|y^2)$ .

Geodesic spaces satisfying these inequalities are called Alexandrov spaces with curvature bounded below by  $K$ ; all the remarks which were made above in the case  $K = 0$  apply in this more general case. There is also a symmetric notion of Alexandrov spaces with curvature bounded above, obtained by just reversing the inequalities.

This generalized notion of sectional curvature bounds has been explored by many authors, and quite strong results have been obtained concerning the geometric and analytic implications of such bounds. But until recently the synthetic treatment of lower Ricci curvature bounds stood as an open problem. The thesis developed in the rest of these notes is that optimal transport provides a solution to this problem (maybe not the only one, but so far the only one which seems to be acceptable).

## Bibliographical notes

In close relation to the topics discussed in this chapter, there is an illuminating course by Gromov [437], which I strongly recommend to the reader who wants to learn about the meaning of curvature.

Alexandrov spaces are also called **CAT** spaces, in honor of Cartan, Alexandrov and Toponogov. But the terminology of CAT space is often restricted to Alexandrov spaces with upper sectional bounds. So a  $CAT(K)$  space typically means an Alexandrov space with "sectional curvature bounded above by  $K$ ". In the sequel, I shall only consider lower curvature bounds.

There are several good sources for Alexandrov spaces, in particular the book by Burago, Burago and Ivanov [174] and the synthesis paper by Burago, Gromov and Perelman [175]. Analysis on Alexandrov spaces has been an active research topic in the past decade [536, 537, 539, 583, 584, 665, 676, 677, 678, 681, 751].

There is also a notion of "approximate" Alexandrov spaces, called  $CAT_{\delta}(K)$  spaces, in which a fixed "resolution error"  $\delta$  is allowed in the defining inequalities [439]. (In the case of upper curvature bounds, this notion has applications to the theory of hyperbolic groups.) Such spaces are not necessarily geodesic spaces, not even length spaces, they can be discrete; a pair of points  $(x_0, x_1)$  will not necessarily admit a midpoint, but there will be a  $\delta$ -approximate midpoint (that is, m such that  $|d(x_0,m) - d(x_0,x_1)/2| \leq \delta, |d(x_1,m) - d(x_0,x_1)/2| \leq \delta$ .

The open problem of developing a satisfactory synthetic treatment of Ricci curvature bounds was discussed in the above-mentioned book by Gromov [437, pp. 84–85], and more recently in a research paper by Cheeger and Colding [228, Appendix 2]. References about recent developments related to optimal transport will be given in the sequel.

Although this is not really the point of this chapter, I shall take this opportunity to briefly discuss the structure of the Wasserstein space over Alexandrov spaces. In Chapter 8 I already mentioned that Alexandrov spaces with lower curvature bounds might be the natural setting for certain regularity issues associated with optimal transport; recall indeed Open Problem 8.21 and the discussion before it. Another issue is how sectional (or Alexandrov) curvature bounds influence the geometry of the Wasserstein space.

It was shown by Lott and myself [577, Appendix A] that if M is a compact Riemannian manifold then M has nonnegative sectional curvature if and only if  $P_2(M)$  is an Alexandrov space with nonnegative curvature. Independently, Sturm [762, Proposition 2.10(iv)] proved the more general result according to which  $\mathcal X$  is an Alexandrov space with nonnegative curvature if and only if  $P_2(\mathcal{X})$  is an Alexandrov space with nonnegative curvature. A study of tangent cones was performed in [577, Appendix A]; it is shown in particular that tangent cones at absolutely continuous measures are Hilbert spaces.

All this suggested that the notion of Alexandrov curvature matched well with optimal transport. However, at the same time, Sturm showed that if X is not nonnegatively curved, then  $P_2(\mathcal{X})$  cannot be an Alexandrov space (morally, the curvature takes all values in  $(-\infty, +\infty)$ ) at Dirac masses); this negative result was recently developed in [575]. To circumvent this obstacle, Ohta [655, Section 3] suggested replacing the Alexandrov property by a weaker condition known as 2-uniform convexity and used in Banach space theory (see e.g.  $[62]$ ). Savaré [735, 736] came up independently with a similar idea. By definition, a geodesic space  $(\mathcal{X}, d)$  is 2-uniform with a constant  $S \geq 1$  if, given any three points  $x, y, z \in \mathcal{X}$ , and a minimizing geoesic  $\gamma$  joining y to z, one has

$$
\forall t \in [0,1], \qquad d(x,\gamma_t)^2 \ge (1-t) d(x,y)^2 + t d(x,z)^2 - S^2 t (1-t) d(y,z)^2.
$$

(When  $S = 1$  this is exactly the inequality defining nonnegative Alexandrov curvature.) Ohta shows that (a) any Alexandrov space with curvature bounded below is locally 2-uniform; (b)  $\mathcal X$  is 2-uniformly smooth with constant S if and only if  $P_2(\mathcal{X})$  is 2-uniformly smooth with the same constant S. He further uses the 2-uniform smoothness to study the structure of tangent cones in  $P_2(\mathcal{X})$ . Both Ohta and Savaré use these inequalities to construct gradient flows in the Wasserstein space over Alexandrov spaces.

Even when  $\mathcal X$  is a smooth manifold with nonnegative curvature, many technical issues remain open about the structure of  $P_2(M)$  as an Alexandrov space. For instance, the notion of the tangent cone used in the above-mentioned works is the one involving the space of directions, but does it coincide with the notion derived from rescaling and Gromov–Hausdorff convergence? (See Example 27.16. In finite dimension there are theorems of equivalence of the two definitions, but now we are in a genuinely infinite-dimensional setting.) How should one define a regular point of  $P_2(M)$ : as an absolutely continuous measure, or an absolutely continuous measure with positive density? And in the latter case, do these measures form a totally convex set? Do singular measures form a very small set in some sense? Can one define and use quasi-geodesics in  $P_2(M)$ ? And so on.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
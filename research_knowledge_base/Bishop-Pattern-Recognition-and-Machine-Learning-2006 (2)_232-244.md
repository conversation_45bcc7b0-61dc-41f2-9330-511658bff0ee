however, find another use for the probit model when we discuss Bayesian treatments of logistic regression in Section 4.5.

One issue that can occur in practical applications is that of *outliers*, which can arise for instance through errors in measuring the input vector **x** or through mislabelling of the target value  $t$ . Because such points can lie a long way to the wrong side of the ideal decision boundary, they can seriously distort the classifier. Note that the logistic and probit regression models behave differently in this respect because the tails of the logistic sigmoid decay asymptotically like  $\exp(-x)$  for  $x \to \infty$ , whereas for the probit activation function they decay like  $\exp(-x^2)$ , and so the probit model can be significantly more sensitive to outliers.

However, both the logistic and the probit models assume the data is correctly labelled. The effect of mislabelling is easily incorporated into a probabilistic model by introducing a probability  $\epsilon$  that the target value t has been flipped to the wrong value (<PERSON><PERSON> and <PERSON><PERSON>, 2000a), leading to a target value distribution for data point **x** of the form

$$
p(t|\mathbf{x}) = (1 - \epsilon)\sigma(\mathbf{x}) + \epsilon(1 - \sigma(\mathbf{x}))
$$
  
=  $\epsilon + (1 - 2\epsilon)\sigma(\mathbf{x})$  (4.117)

where  $\sigma(\mathbf{x})$  is the activation function with input vector **x**. Here  $\epsilon$  may be set in advance, or it may be treated as a hyperparameter whose value is inferred from the data.

### **4.3.6 Canonical link functions**

For the linear regression model with a Gaussian noise distribution, the error function, corresponding to the negative log likelihood, is given by (3.12). If we take the derivative with respect to the parameter vector **w** of the contribution to the error function from a data point n, this takes the form of the 'error'  $y_n - t_n$  times the feature vector  $\phi_n$ , where  $y_n = \mathbf{w}^T \phi_n$ . Similarly, for the combination of the logistic sigmoid activation function and the cross-entropy error function (4.90), and for the softmax activation function with the multiclass cross-entropy error function (4.108), we again obtain this same simple form. We now show that this is a general result of assuming a conditional distribution for the target variable from the exponential family, along with a corresponding choice for the activation function known as the *canonical link function*.

We again make use of the restricted form (4.84) of exponential family distributions. Note that here we are applying the assumption of exponential family distribution to the target variable  $t$ , in contrast to Section 4.2.4 where we applied it to the input vector **x**. We therefore consider conditional distributions of the target variable of the form

$$
p(t|\eta, s) = \frac{1}{s} h\left(\frac{t}{s}\right) g(\eta) \exp\left\{\frac{\eta t}{s}\right\}.
$$
 (4.118)

Using the same line of argument as led to the derivation of the result (2.226), we see that the conditional mean of  $t$ , which we denote by  $y$ , is given by

$$
y \equiv \mathbb{E}[t|\eta] = -s\frac{d}{d\eta}\ln g(\eta). \tag{4.119}
$$

Thus y and  $\eta$  must related, and we denote this relation through  $\eta = \psi(y)$ .

Following Nelder and Wedderburn (1972), we define a *generalized linear model* to be one for which  $y$  is a nonlinear function of a linear combination of the input (or feature) variables so that

$$
y = f(\mathbf{w}^{\mathrm{T}}\boldsymbol{\phi})\tag{4.120}
$$

where  $f(\cdot)$  is known as the *activation function* in the machine learning literature, and  $f^{-1}(\cdot)$  is known as the *link function* in statistics.

Now consider the log likelihood function for this model, which, as a function of  $\eta$ , is given by

$$
\ln p(\mathbf{t}|\eta, s) = \sum_{n=1}^{N} \ln p(t_n|\eta, s) = \sum_{n=1}^{N} \left\{ \ln g(\eta_n) + \frac{\eta_n t_n}{s} \right\} + \text{const} \qquad (4.121)
$$

where we are assuming that all observations share a common scale parameter (which corresponds to the noise variance for a Gaussian distribution for instance) and so s is independent of n. The derivative of the log likelihood with respect to the model parameters **w** is then given by

$$
\nabla_{\mathbf{w}} \ln p(\mathbf{t}|\eta, s) = \sum_{n=1}^{N} \left\{ \frac{d}{d\eta_n} \ln g(\eta_n) + \frac{t_n}{s} \right\} \frac{d\eta_n}{dy_n} \frac{dy_n}{da_n} \nabla a_n
$$
$$
= \sum_{n=1}^{N} \frac{1}{s} \left\{ t_n - y_n \right\} \psi'(y_n) f'(a_n) \phi_n \tag{4.122}
$$

where  $a_n = \mathbf{w}^T \boldsymbol{\phi}_n$ , and we have used  $y_n = f(a_n)$  together with the result (4.119) for  $\mathbb{E}[t|\eta]$ . We now see that there is a considerable simplification if we choose a particular form for the link function  $f^{-1}(y)$  given by

$$
f^{-1}(y) = \psi(y) \tag{4.123}
$$

which gives  $f(\psi(y)) = y$  and hence  $f'(\psi)\psi'(y) = 1$ . Also, because  $a = f^{-1}(y)$ , we have  $a = \psi$  and hence  $f'(a)\psi'(y) = 1$ . In this case, the gradient of the error function reduces to

$$
\nabla \ln E(\mathbf{w}) = \frac{1}{s} \sum_{n=1}^{N} \{y_n - t_n\} \phi_n.
$$
 (4.124)

For the Gaussian  $s = \beta^{-1}$ , whereas for the logistic model  $s = 1$ .

## **4.4. The Laplace Approximation**

In Section 4.5 we shall discuss the Bayesian treatment of logistic regression. As we shall see, this is more complex than the Bayesian treatment of linear regression models, discussed in Sections 3.3 and 3.5. In particular, we cannot integrate exactly

over the parameter vector **w** since the posterior distribution is no longer Gaussian. It is therefore necessary to introduce some form of approximation. Later in the *Chapter 10* book we shall consider a range of techniques based on analytical approximations *Chapter 11* and numerical sampling.

Here we introduce a simple, but widely used, framework called the Laplace approximation, that aims to find a Gaussian approximation to a probability density defined over a set of continuous variables. Consider first the case of a single continuous variable z, and suppose the distribution  $p(z)$  is defined by

$$
p(z) = \frac{1}{Z}f(z)
$$
 (4.125)

where  $Z = \int f(z) dz$  is the normalization coefficient. We shall suppose that the value of  $Z$  is unknown. In the Laplace method the goal is to find a Gaussian approximation  $q(z)$  which is centred on a mode of the distribution  $p(z)$ . The first step is to find a mode of  $p(z)$ , in other words a point  $z_0$  such that  $p'(z_0)=0$ , or equivalently

$$
\left. \frac{df(z)}{dz} \right|_{z=z_0} = 0. \tag{4.126}
$$

A Gaussian distribution has the property that its logarithm is a quadratic function of the variables. We therefore consider a Taylor expansion of  $\ln f(z)$  centred on the mode  $z_0$  so that

$$
\ln f(z) \simeq \ln f(z_0) - \frac{1}{2}A(z - z_0)^2 \tag{4.127}
$$

where

$$
A = -\frac{d^2}{dz^2} \ln f(z) \Big|_{z=z_0} . \tag{4.128}
$$

Note that the first-order term in the Taylor expansion does not appear since  $z_0$  is a local maximum of the distribution. Taking the exponential we obtain

$$
f(z) \simeq f(z_0) \exp\left\{-\frac{A}{2}(z-z_0)^2\right\}.
$$
 (4.129)

We can then obtain a normalized distribution  $q(z)$  by making use of the standard result for the normalization of a Gaussian, so that

$$
q(z) = \left(\frac{A}{2\pi}\right)^{1/2} \exp\left\{-\frac{A}{2}(z-z_0)^2\right\}.
$$
 (4.130)

The Laplace approximation is illustrated in Figure 4.14. Note that the Gaussian approximation will only be well defined if its precision  $A > 0$ , in other words the stationary point  $z_0$  must be a local maximum, so that the second derivative of  $f(z)$ at the point  $z_0$  is negative.

*Chapter 10*
*Chapter 11*

Image /page/3/Figure/1 description: The image contains two plots. The left plot shows two curves, one red and one yellow, on a graph with x-axis values from -2 to 4 and y-axis values from 0 to 0.8. The yellow curve is a shaded area representing a distribution, peaking around 0.7 at x=0. The red curve is a narrower distribution, peaking at approximately 0.75 at x=-0.5. The right plot shows two curves, one red and one yellow, on a graph with x-axis values from -2 to 4 and y-axis values from 0 to 40. The yellow curve is a V-shaped line, decreasing from 40 at x=-2 to 0 at x=0, and then increasing to 30 at x=4. The red curve is a U-shaped parabola, decreasing from approximately 8 at x=-2 to 0 at x=0, and then increasing to approximately 25 at x=4.

**Figure 4.14** Illustration of the Laplace approximation applied to the distribution  $p(z) \propto \exp(-z^2/2)\sigma(20z + 4)$ where  $\sigma(z)$  is the logistic sigmoid function defined by  $\sigma(z) = (1 + e^{-z})^{-1}$ . The left plot shows the normalized distribution  $p(z)$  in yellow, together with the Laplace approximation centred on the mode  $z_0$  of  $p(z)$  in red. The right plot shows the negative logarithms of the corresponding curves.

We can extend the Laplace method to approximate a distribution  $p(\mathbf{z}) = f(\mathbf{z})/Z$ defined over an M-dimensional space **z**. At a stationary point **z**<sub>0</sub> the gradient  $\nabla f(\mathbf{z})$ will vanish. Expanding around this stationary point we have

$$
\ln f(\mathbf{z}) \simeq \ln f(\mathbf{z}_0) - \frac{1}{2} (\mathbf{z} - \mathbf{z}_0)^{\mathrm{T}} \mathbf{A} (\mathbf{z} - \mathbf{z}_0)
$$
(4.131)

where the  $M \times M$  Hessian matrix **A** is defined by

$$
\mathbf{A} = -\nabla \nabla \ln f(\mathbf{z})\big|_{\mathbf{z} = \mathbf{z}_0} \tag{4.132}
$$

and  $\nabla$  is the gradient operator. Taking the exponential of both sides we obtain

$$
f(\mathbf{z}) \simeq f(\mathbf{z}_0) \exp\left\{-\frac{1}{2}(\mathbf{z}-\mathbf{z}_0)^{\mathrm{T}} \mathbf{A}(\mathbf{z}-\mathbf{z}_0)\right\}.
$$
 (4.133)

The distribution  $q(\mathbf{z})$  is proportional to  $f(\mathbf{z})$  and the appropriate normalization coefficient can be found by inspection, using the standard result (2.43) for a normalized multivariate Gaussian, giving

$$
q(\mathbf{z}) = \frac{|\mathbf{A}|^{1/2}}{(2\pi)^{M/2}} \exp\left\{-\frac{1}{2}(\mathbf{z}-\mathbf{z}_0)^{\mathrm{T}} \mathbf{A}(\mathbf{z}-\mathbf{z}_0)\right\} = \mathcal{N}(\mathbf{z}|\mathbf{z}_0, \mathbf{A}^{-1}) \qquad (4.134)
$$

where <sup>|</sup>**A**<sup>|</sup> denotes the determinant of **A**. This Gaussian distribution will be well defined provided its precision matrix, given by **A**, is positive definite, which implies that the stationary point  $z_0$  must be a local maximum, not a minimum or a saddle point.

In order to apply the Laplace approximation we first need to find the mode  $z_0$ , and then evaluate the Hessian matrix at that mode. In practice a mode will typically be found by running some form of numerical optimization algorithm (Bishop

and Nabney, 2008). Many of the distributions encountered in practice will be multimodal and so there will be different Laplace approximations according to which mode is being considered. Note that the normalization constant  $Z$  of the true distribution does not need to be known in order to apply the Laplace method. As a result of the central limit theorem, the posterior distribution for a model is expected to become increasingly better approximated by a Gaussian as the number of observed data points is increased, and so we would expect the Laplace approximation to be most useful in situations where the number of data points is relatively large.

One major weakness of the Laplace approximation is that, since it is based on a Gaussian distribution, it is only directly applicable to real variables. In other cases it may be possible to apply the Laplace approximation to a transformation of the variable. For instance if  $0 \leq \tau < \infty$  then we can consider a Laplace approximation of  $\ln \tau$ . The most serious limitation of the Laplace framework, however, is that it is based purely on the aspects of the true distribution at a specific value of the variable, and so can fail to capture important global properties. In Chapter 10 we shall consider alternative approaches which adopt a more global perspective.

#### **4.4.1 Model comparison and BIC**

As well as approximating the distribution  $p(z)$  we can also obtain an approximation to the normalization constant  $Z$ . Using the approximation (4.133) we have

$$
Z = \int f(\mathbf{z}) d\mathbf{z}
$$

$$
\approx f(\mathbf{z}_0) \int \exp \left\{-\frac{1}{2} (\mathbf{z} - \mathbf{z}_0)^T \mathbf{A} (\mathbf{z} - \mathbf{z}_0) \right\} d\mathbf{z}
$$

$$
= f(\mathbf{z}_0) \frac{(2\pi)^{M/2}}{|\mathbf{A}|^{1/2}}
$$
 $(4.135)$ 

where we have noted that the integrand is Gaussian and made use of the standard result (2.43) for a normalized Gaussian distribution. We can use the result (4.135) to obtain an approximation to the model evidence which, as discussed in Section 3.4, plays a central role in Bayesian model comparison.

Consider a data set D and a set of models  $\{\mathcal{M}_i\}$  having parameters  $\{\theta_i\}$ . For each model we define a likelihood function  $p(\mathcal{D}|\theta_i,\mathcal{M}_i)$ . If we introduce a prior  $p(\theta_i|M_i)$  over the parameters, then we are interested in computing the model evidence  $p(\mathcal{D}|\mathcal{M}_i)$  for the various models. From now on we omit the conditioning on  $\mathcal{M}_i$  to keep the notation uncluttered. From Bayes' theorem the model evidence is given by

$$
p(\mathcal{D}) = \int p(\mathcal{D}|\boldsymbol{\theta})p(\boldsymbol{\theta}) d\boldsymbol{\theta}.
$$
 (4.136)

Identifying  $f(\theta) = p(\mathcal{D}|\theta)p(\theta)$  and  $Z = p(\mathcal{D})$ , and applying the result (4.135), we obtain

$$
\ln p(\mathcal{D}) \simeq \ln p(\mathcal{D}|\boldsymbol{\theta}_{\text{MAP}}) + \ln p(\boldsymbol{\theta}_{\text{MAP}}) + \frac{M}{2}\ln(2\pi) - \frac{1}{2}\ln|\mathbf{A}| \quad (4.137)
$$

*Exercise 4.22* 

where  $\theta_{\text{MAP}}$  is the value of  $\theta$  at the mode of the posterior distribution, and **A** is the *Hessian* matrix of second derivatives of the negative log posterior

$$
\mathbf{A} = -\nabla \nabla \ln p(\mathcal{D}|\boldsymbol{\theta}_{\text{MAP}}) p(\boldsymbol{\theta}_{\text{MAP}}) = -\nabla \nabla \ln p(\boldsymbol{\theta}_{\text{MAP}}|\mathcal{D}). \tag{4.138}
$$

The first term on the right hand side of (4.137) represents the log likelihood evaluated using the optimized parameters, while the remaining three terms comprise the 'Occam factor' which penalizes model complexity.

If we assume that the Gaussian prior distribution over parameters is broad, and *Exercise 4.23* that the Hessian has full rank, then we can approximate (4.137) very roughly using

$$
\ln p(\mathcal{D}) \simeq \ln p(\mathcal{D}|\boldsymbol{\theta}_{\text{MAP}}) - \frac{1}{2}M\ln N \tag{4.139}
$$

where N is the number of data points, M is the number of parameters in  $\theta$  and we have omitted additive constants. This is known as the *Bayesian Information Criterion* (BIC) or the *Schwarz criterion* (Schwarz, 1978). Note that, compared to AIC given by (1.73), this penalizes model complexity more heavily.

Complexity measures such as AIC and BIC have the virtue of being easy to evaluate, but can also give misleading results. In particular, the assumption that the Hessian matrix has full rank is often not valid since many of the parameters are not Section 3.5.3 'well-determined'. We can use the result (4.137) to obtain a more accurate estimate of the model evidence starting from the Laplace approximation, as we illustrate in the context of neural networks in Section 5.7.

## **4.5. Bayesian Logistic Regression**

We now turn to a Bayesian treatment of logistic regression. Exact Bayesian inference for logistic regression is intractable. In particular, evaluation of the posterior distribution would require normalization of the product of a prior distribution and a likelihood function that itself comprises a product of logistic sigmoid functions, one for every data point. Evaluation of the predictive distribution is similarly intractable. Here we consider the application of the Laplace approximation to the problem of Bayesian logistic regression (Spiegelhalter and Lauritzen, 1990; MacKay, 1992b).

#### **4.5.1 Laplace approximation**

Recall from Section 4.4 that the Laplace approximation is obtained by finding the mode of the posterior distribution and then fitting a Gaussian centred at that mode. This requires evaluation of the second derivatives of the log posterior, which is equivalent to finding the Hessian matrix.

Because we seek a Gaussian representation for the posterior distribution, it is natural to begin with a Gaussian prior, which we write in the general form

$$
p(\mathbf{w}) = \mathcal{N}(\mathbf{w}|\mathbf{m}_0, \mathbf{S}_0)
$$
\n(4.140)

*Exercise 4.23*

*Section 3.5.3*

where  $m_0$  and  $S_0$  are fixed hyperparameters. The posterior distribution over **w** is given by

$$
p(\mathbf{w}|\mathbf{t}) \propto p(\mathbf{w})p(\mathbf{t}|\mathbf{w})
$$
\n(4.141)

where  $\mathbf{t} = (t_1, \dots, t_N)^T$ . Taking the log of both sides, and substituting for the prior distribution using (4.140), and for the likelihood function using (4.89), we obtain

$$
\ln p(\mathbf{w}|\mathbf{t}) = -\frac{1}{2}(\mathbf{w} - \mathbf{m}_0)^{\mathrm{T}} \mathbf{S}_0^{-1}(\mathbf{w} - \mathbf{m}_0) + \sum_{n=1}^{N} \{t_n \ln y_n + (1 - t_n) \ln(1 - y_n)\} + \text{const} \quad (4.142)
$$

where  $y_n = \sigma(\mathbf{w}^T \boldsymbol{\phi}_n)$ . To obtain a Gaussian approximation to the posterior distribution, we first maximize the posterior distribution to give the MAP (maximum posterior) solution  $w_{\text{MAP}}$ , which defines the mean of the Gaussian. The covariance is then given by the inverse of the matrix of second derivatives of the negative log likelihood, which takes the form

$$
\mathbf{S}_N = -\nabla \nabla \ln p(\mathbf{w}|\mathbf{t}) = \mathbf{S}_0^{-1} + \sum_{n=1}^N y_n (1 - y_n) \boldsymbol{\phi}_n \boldsymbol{\phi}_n^{\mathrm{T}}.
$$
 (4.143)

The Gaussian approximation to the posterior distribution therefore takes the form

$$
q(\mathbf{w}) = \mathcal{N}(\mathbf{w}|\mathbf{w}_{\text{MAP}}, \mathbf{S}_N). \tag{4.144}
$$

Having obtained a Gaussian approximation to the posterior distribution, there remains the task of marginalizing with respect to this distribution in order to make predictions.

#### **4.5.2 Predictive distribution**

The predictive distribution for class  $C_1$ , given a new feature vector  $\phi(\mathbf{x})$ , is obtained by marginalizing with respect to the posterior distribution  $p(\mathbf{w}|\mathbf{t})$ , which is itself approximated by a Gaussian distribution  $q(\mathbf{w})$  so that

$$
p(C_1|\boldsymbol{\phi}, \mathbf{t}) = \int p(C_1|\boldsymbol{\phi}, \mathbf{w}) p(\mathbf{w}|\mathbf{t}) \, d\mathbf{w} \simeq \int \sigma(\mathbf{w}^T \boldsymbol{\phi}) q(\mathbf{w}) \, d\mathbf{w} \tag{4.145}
$$

with the corresponding probability for class  $C_2$  given by  $p(C_2|\phi, \mathbf{t})=1-p(C_1|\phi, \mathbf{t})$ . To evaluate the predictive distribution, we first note that the function  $\sigma(\mathbf{w}^T\boldsymbol{\phi})$  depends on **w** only through its projection onto  $\phi$ . Denoting  $a = \mathbf{w}^T \phi$ , we have

$$
\sigma(\mathbf{w}^{\mathrm{T}}\boldsymbol{\phi}) = \int \delta(a - \mathbf{w}^{\mathrm{T}}\boldsymbol{\phi})\sigma(a) da \qquad (4.146)
$$

where  $\delta(\cdot)$  is the Dirac delta function. From this we obtain

$$
\int \sigma(\mathbf{w}^{\mathrm{T}}\boldsymbol{\phi})q(\mathbf{w}) \, \mathrm{d}\mathbf{w} = \int \sigma(a)p(a) \, \mathrm{d}a \tag{4.147}
$$

where

$$
p(a) = \int \delta(a - \mathbf{w}^{\mathrm{T}} \phi) q(\mathbf{w}) \, \mathrm{d}\mathbf{w}.
$$
 (4.148)

We can evaluate  $p(a)$  by noting that the delta function imposes a linear constraint on **w** and so forms a marginal distribution from the joint distribution  $q(\mathbf{w})$  by integrating out all directions orthogonal to  $\phi$ . Because  $q(\mathbf{w})$  is Gaussian, we know from Section 2.3.2 that the marginal distribution will also be Gaussian. We can evaluate the mean and covariance of this distribution by taking moments, and interchanging the order of integration over <sup>a</sup> and **w**, so that

$$
\mu_a = \mathbb{E}[a] = \int p(a) a \, da = \int q(\mathbf{w}) \mathbf{w}^{\mathrm{T}} \phi \, d\mathbf{w} = \mathbf{w}_{\mathrm{MAP}}^{\mathrm{T}} \phi \tag{4.149}
$$

where we have used the result (4.144) for the variational posterior distribution  $q(\mathbf{w})$ . Similarly

$$
\sigma_a^2 = \text{var}[a] = \int p(a) \{a^2 - \mathbb{E}[a]^2\} da
$$

$$
= \int q(\mathbf{w}) \{(\mathbf{w}^T \boldsymbol{\phi})^2 - (\mathbf{m}_N^T \boldsymbol{\phi})^2\} d\mathbf{w} = \boldsymbol{\phi}^T \mathbf{S}_N \boldsymbol{\phi}. \quad (4.150)
$$

Note that the distribution of  $\alpha$  takes the same form as the predictive distribution (3.58) for the linear regression model, with the noise variance set to zero. Thus our variational approximation to the predictive distribution becomes

$$
p(\mathcal{C}_1|\mathbf{t}) = \int \sigma(a)p(a) da = \int \sigma(a)\mathcal{N}(a|\mu_a, \sigma_a^2) da.
$$
 (4.151)

This result can also be derived directly by making use of the results for the marginal *Exercise 4.24* of a Gaussian distribution given in Section 2.3.2.

The integral over  $\alpha$  represents the convolution of a Gaussian with a logistic sigmoid, and cannot be evaluated analytically. We can, however, obtain a good approximation (Spiegelhalter and Lauritzen, 1990; MacKay, 1992b; Barber and Bishop, 1998a) by making use of the close similarity between the logistic sigmoid function  $\sigma(a)$  defined by (4.59) and the probit function  $\Phi(a)$  defined by (4.114). In order to obtain the best approximation to the logistic function we need to re-scale the horizontal axis, so that we approximate  $\sigma(a)$  by  $\Phi(\lambda a)$ . We can find a suitable value of  $\lambda$  by requiring that the two functions have the same slope at the origin, which gives *Exercise 4.25*  $\lambda^2 = \pi/8$ . The similarity of the logistic sigmoid and the probit function, for this choice of  $\lambda$ , is illustrated in Figure 4.9.

The advantage of using a probit function is that its convolution with a Gaussian can be expressed analytically in terms of another probit function. Specifically we *Exercise 4.26* can show that

$$
\int \Phi(\lambda a) \mathcal{N}(a|\mu, \sigma^2) da = \Phi\left(\frac{\mu}{(\lambda^{-2} + \sigma^2)^{1/2}}\right).
$$
 (4.152)

*Exercise 4.24*

We now apply the approximation  $\sigma(a) \simeq \Phi(\lambda a)$  to the probit functions appearing on both sides of this equation, leading to the following approximation for the convolution of a logistic sigmoid with a Gaussian

$$
\int \sigma(a) \mathcal{N}(a|\mu, \sigma^2) da \simeq \sigma\left(\kappa(\sigma^2)\mu\right)
$$
\n(4.153)

where we have defined

$$
\kappa(\sigma^2) = (1 + \pi \sigma^2 / 8)^{-1/2}.
$$
\n(4.154)

Applying this result to (4.151) we obtain the approximate predictive distribution in the form

$$
p(C_1|\phi,\mathbf{t}) = \sigma\left(\kappa(\sigma_a^2)\mu_a\right)
$$
 (4.155)

where  $\mu_a$  and  $\sigma_a^2$  are defined by (4.149) and (4.150), respectively, and  $\kappa(\sigma_a^2)$  is defined by (4.154).

Note that the decision boundary corresponding to  $p(C_1|\phi, \mathbf{t})=0.5$  is given by  $\mu_a = 0$ , which is the same as the decision boundary obtained by using the MAP value for **w**. Thus if the decision criterion is based on minimizing misclassification rate, with equal prior probabilities, then the marginalization over **w** has no effect. However, for more complex decision criteria it will play an important role. Marginalization of the logistic sigmoid model under a Gaussian approximation to the posterior distribution will be illustrated in the context of variational inference in Figure 10.13.

# **Exercises**

**4.1**  $(\star \star)$  Given a set of data points  $\{x_n\}$ , we can define the *convex hull* to be the set of all points **x** given by

$$
\mathbf{x} = \sum_{n} \alpha_n \mathbf{x}_n \tag{4.156}
$$

where  $\alpha_n \geq 0$  and  $\sum_n \alpha_n = 1$ . Consider a second set of points  $\{y_n\}$  together with their corresponding convex bull. By definition, the two sets of points will be linearly their corresponding convex hull. By definition, the two sets of points will be linearly separable if there exists a vector  $\hat{\mathbf{w}}$  and a scalar  $w_0$  such that  $\hat{\mathbf{w}}^T\mathbf{x}_n + w_0 > 0$  for all  $\mathbf{x}_n$ , and  $\hat{\mathbf{w}}^T \mathbf{y}_n + w_0 < 0$  for all  $\mathbf{y}_n$ . Show that if their convex hulls intersect, the two sets of points cannot be linearly separable, and conversely that if they are linearly separable, their convex hulls do not intersect.

**4.2**  $(\star \star)$  **www** Consider the minimization of a sum-of-squares error function (4.15), and suppose that all of the target vectors in the training set satisfy a linear constraint

$$
\mathbf{a}^{\mathrm{T}}\mathbf{t}_n + b = 0 \tag{4.157}
$$

where  $t_n$  corresponds to the  $n^{\text{th}}$  row of the matrix **T** in (4.15). Show that as a consequence of this constraint, the elements of the model prediction  $y(x)$  given by the least-squares solution (4.17) also satisfy this constraint, so that

$$
\mathbf{a}^{\mathrm{T}}\mathbf{y}(\mathbf{x}) + b = 0. \tag{4.158}
$$

To do so, assume that one of the basis functions  $\phi_0(\mathbf{x})=1$  so that the corresponding parameter  $w_0$  plays the role of a bias.

- **4.3**  $(\star \star)$  Extend the result of Exercise 4.2 to show that if multiple linear constraints are satisfied simultaneously by the target vectors, then the same constraints will also be satisfied by the least-squares prediction of a linear model.
- **4.4 ( ) www** Show that maximization of the class separation criterion given by (4.23) with respect to **w**, using a Lagrange multiplier to enforce the constraint  $\mathbf{w}^T \mathbf{w} = 1$ , leads to the result that  $\mathbf{w} \propto (\mathbf{m}_2 - \mathbf{m}_1)$ .
- **4.5**  $(\star)$  By making use of (4.20), (4.23), and (4.24), show that the Fisher criterion (4.25) can be written in the form (4.26).
- **4.6** ( $\star$ ) Using the definitions of the between-class and within-class covariance matrices given by  $(4.27)$  and  $(4.28)$ , respectively, together with  $(4.34)$  and  $(4.36)$  and the choice of target values described in Section 4.1.5, show that the expression (4.33) that minimizes the sum-of-squares error function can be written in the form (4.37).
- **4.7** ( $\star$ ) **www** Show that the logistic sigmoid function (4.59) satisfies the property  $\sigma(-a)=1-\sigma(a)$  and that its inverse is given by  $\sigma^{-1}(y)=\ln\{y/(1-y)\}.$
- **4.8** ( $\star$ ) Using (4.57) and (4.58), derive the result (4.65) for the posterior class probability in the two-class generative model with Gaussian densities, and verify the results  $(4.66)$  and  $(4.67)$  for the parameters **w** and  $w_0$ .
- **4.9 ( ) www** Consider a generative classification model for K classes defined by prior class probabilities  $p(C_k) = \pi_k$  and general class-conditional densities  $p(\phi | C_k)$ where  $\phi$  is the input feature vector. Suppose we are given a training data set  $\{\phi_n, \mathbf{t}_n\}$ where  $n = 1, \ldots, N$ , and  $\mathbf{t}_n$  is a binary target vector of length K that uses the 1-of-K coding scheme, so that it has components  $t_{nj} = I_{jk}$  if pattern n is from class  $\mathcal{C}_k$ . Assuming that the data points are drawn independently from this model, show that the maximum-likelihood solution for the prior probabilities is given by

$$
\pi_k = \frac{N_k}{N} \tag{4.159}
$$

where  $N_k$  is the number of data points assigned to class  $\mathcal{C}_k$ .

**4.10**  $(\star \star)$  Consider the classification model of Exercise 4.9 and now suppose that the class-conditional densities are given by Gaussian distributions with a shared covariance matrix, so that

$$
p(\phi|\mathcal{C}_k) = \mathcal{N}(\phi|\boldsymbol{\mu}_k, \boldsymbol{\Sigma}).
$$
\n(4.160)

Show that the maximum likelihood solution for the mean of the Gaussian distribution for class  $\mathcal{C}_k$  is given by

$$
\mu_k = \frac{1}{N_k} \sum_{n=1}^{N} t_{nk} \phi_n
$$
\n(4.161)

which represents the mean of those feature vectors assigned to class  $\mathcal{C}_k$ . Similarly, show that the maximum likelihood solution for the shared covariance matrix is given by

$$
\Sigma = \sum_{k=1}^{K} \frac{N_k}{N} \mathbf{S}_k
$$
\n(4.162)

where

$$
\mathbf{S}_{k} = \frac{1}{N_{k}} \sum_{n=1}^{N} t_{nk} (\boldsymbol{\phi}_{n} - \boldsymbol{\mu}_{k}) (\boldsymbol{\phi}_{n} - \boldsymbol{\mu}_{k})^{\mathrm{T}}.
$$
 (4.163)

Thus  $\Sigma$  is given by a weighted average of the covariances of the data associated with each class, in which the weighting coefficients are given by the prior probabilities of the classes.

N

- **4.11**  $(\star \star)$  Consider a classification problem with K classes for which the feature vector *φ* has M components each of which can take L discrete states. Let the values of the components be represented by a 1-of-L binary coding scheme. Further suppose that, conditioned on the class  $\mathcal{C}_k$ , the M components of  $\phi$  are independent, so that the class-conditional density factorizes with respect to the feature vector components. Show that the quantities  $a_k$  given by (4.63), which appear in the argument to the softmax function describing the posterior class probabilities, are linear functions of the components of *φ*. Note that this represents an example of the naive Bayes model which is discussed in Section 8.2.2.
- **4.12** ( $\star$ ) **www** Verify the relation (4.88) for the derivative of the logistic sigmoid function defined by (4.59).
- **4.13** ( $\star$ ) **www** By making use of the result (4.88) for the derivative of the logistic sigmoid, show that the derivative of the error function (4.90) for the logistic regression model is given by (4.91).
- **4.14**  $(\star)$  Show that for a linearly separable data set, the maximum likelihood solution for the logistic regression model is obtained by finding a vector **w** whose decision boundary  $\mathbf{w}^T \phi(\mathbf{x}) = 0$  separates the classes and then taking the magnitude of **w** to infinity.
- **4.15**  $(\star \star)$  Show that the Hessian matrix **H** for the logistic regression model, given by (4.97), is positive definite. Here **R** is a diagonal matrix with elements  $y_n(1 - y_n)$ , and  $y_n$  is the output of the logistic regression model for input vector  $\mathbf{x}_n$ . Hence show that the error function is a concave function of **w** and that it has a unique minimum.
- **4.16** ( $\star$ ) Consider a binary classification problem in which each observation  $\mathbf{x}_n$  is known to belong to one of two classes, corresponding to  $t = 0$  and  $t = 1$ , and suppose that the procedure for collecting training data is imperfect, so that training points are sometimes mislabelled. For every data point  $x_n$ , instead of having a value t for the class label, we have instead a value  $\pi_n$  representing the probability that  $t_n = 1$ . Given a probabilistic model  $p(t = 1 | \phi)$ , write down the log likelihood function appropriate to such a data set.

- **4.17**  $(\star)$  **www** Show that the derivatives of the softmax activation function (4.104), where the  $a_k$  are defined by (4.105), are given by (4.106).
- **4.18**  $(\star)$  Using the result (4.91) for the derivatives of the softmax activation function, show that the gradients of the cross-entropy error  $(4.108)$  are given by  $(4.109)$ .
- **4.19**  $(\star)$  **www** Write down expressions for the gradient of the log likelihood, as well as the corresponding Hessian matrix, for the probit regression model defined in Section 4.3.5. These are the quantities that would be required to train such a model using IRLS.
- **4.20**  $(\star \star)$  Show that the Hessian matrix for the multiclass logistic regression problem, defined by (4.110), is positive semidefinite. Note that the full Hessian matrix for this problem is of size  $MK \times MK$ , where M is the number of parameters and K is the number of classes. To prove the positive semidefinite property, consider the product  $\mathbf{u}^{\mathrm{T}}\mathbf{H}\mathbf{u}$  where  $\mathbf{u}$  is an arbitrary vector of length  $MK$ , and then apply Jensen's inequality.
- **4.21** ( $\star$ ) Show that the probit function (4.114) and the erf function (4.115) are related by (4.116).
- **4.22** ( $\star$ ) Using the result (4.135), derive the expression (4.137) for the log model evidence under the Laplace approximation.
- **4.23**  $(\star \star)$  **www** In this exercise, we derive the BIC result (4.139) starting from the Laplace approximation to the model evidence given by (4.137). Show that if the prior over parameters is Gaussian of the form  $p(\theta) = \mathcal{N}(\theta | \mathbf{m}, \mathbf{V}_0)$ , the log model evidence under the Laplace approximation takes the form

$$
\ln p(\mathcal{D}) \simeq \ln p(\mathcal{D}|\boldsymbol{\theta}_{\text{MAP}}) - \frac{1}{2}(\boldsymbol{\theta}_{\text{MAP}} - \mathbf{m})^{\text{T}} \mathbf{V}_0^{-1}(\boldsymbol{\theta}_{\text{MAP}} - \mathbf{m}) - \frac{1}{2}\ln|\mathbf{H}| + \text{const}
$$

where **H** is the matrix of second derivatives of the log likelihood  $\ln p(\mathcal{D}|\theta)$  evaluated at  $\theta_{\text{MAP}}$ . Now assume that the prior is broad so that  $V_0^{-1}$  is small and the second term on the right-hand side above can be neglected. Furthermore, consider the case term on the right-hand side above can be neglected. Furthermore, consider the case of independent, identically distributed data so that **H** is the sum of terms one for each data point. Show that the log model evidence can then be written approximately in the form of the BIC expression (4.139).

- **4.24**  $(\star \star)$  Use the results from Section 2.3.2 to derive the result (4.151) for the marginalization of the logistic regression model with respect to a Gaussian posterior distribution over the parameters **w**.
- **4.25** ( $\star\star$ ) Suppose we wish to approximate the logistic sigmoid  $\sigma(a)$  defined by (4.59) by a scaled probit function  $\Phi(\lambda a)$ , where  $\Phi(a)$  is defined by (4.114). Show that if  $\lambda$  is chosen so that the derivatives of the two functions are equal at  $a = 0$ , then  $\lambda^2 = \pi/8.$

**4.26**  $(\star \star)$  In this exercise, we prove the relation (4.152) for the convolution of a probit function with a Gaussian distribution. To do this, show that the derivative of the lefthand side with respect to  $\mu$  is equal to the derivative of the right-hand side, and then integrate both sides with respect to  $\mu$  and then show that the constant of integration vanishes. Note that before differentiating the left-hand side, it is convenient first to introduce a change of variable given by  $a = \mu + \sigma z$  so that the integral over a is replaced by an integral over  $z$ . When we differentiate the left-hand side of the relation (4.152), we will then obtain a Gaussian integral over  $z$  that can be evaluated analytically.
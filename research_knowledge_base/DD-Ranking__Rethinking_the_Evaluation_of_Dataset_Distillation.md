# <span id="page-0-0"></span>DD-Ranking: Rethinking the Evaluation of Dataset **Distillation**

## DD-Ranking Team

§ <https://github.com/NUS-HPC-AI-Lab/DD-Ranking.git> [ <https://nus-hpc-ai-lab.github.io/DD-Ranking/> 5 <https://huggingface.co/spaces/logits/DD-Ranking>

## Abstract

In recent years, dataset distillation has provided a reliable solution for data compression, where models trained on the resulting smaller synthetic datasets achieve performance comparable to those trained on the original datasets. To further improve the performance of synthetic datasets, various training pipelines and optimization objectives have been proposed, greatly advancing the field of dataset distillation. Recent decoupled dataset distillation methods introduce soft labels and stronger data augmentation during the post-evaluation phase and scale dataset distillation up to larger datasets (*e.g.*, ImageNet-1K). However, this raises a question: *Is accuracy still a reliable metric to fairly evaluate dataset distillation methods?* Our empirical findings suggest that the performance improvements of these methods often stem from additional techniques rather than the inherent quality of the images themselves, with even randomly sampled images achieving superior results. Such misaligned evaluation settings severely hinder the development of DD. Therefore, we propose DD-Ranking, a unified evaluation framework, along with new general evaluation metrics to uncover the true performance improvements achieved by different methods. By refocusing on the actual information enhancement of distilled datasets, DD-Ranking provides a more comprehensive and fair evaluation standard for future research advancements.

## 1 Introduction

With the rapid advancement of deep learning, training increasingly complex and more complex models on large scale datasets has become a standard paradigm, achieving remarkable performance in various fields, such as computer vision  $[20, 9]$  $[20, 9]$  $[20, 9]$  and natural language processing  $[8, 1]$  $[8, 1]$  $[8, 1]$ . However, this process often incurs substantial computational and storage demands, significantly hindering deployment across diverse scenarios. Dataset distillation (DD) [\[54\]](#page-13-0), as a recent promising solution for dataset compression, offers novel insights to address these challenges. In recent years, diverse training pipelines  $[21, 7, 64, 15, 59]$  $[21, 7, 64, 15, 59]$  $[21, 7, 64, 15, 59]$  $[21, 7, 64, 15, 59]$  $[21, 7, 64, 15, 59]$  $[21, 7, 64, 15, 59]$  $[21, 7, 64, 15, 59]$  $[21, 7, 64, 15, 59]$  $[21, 7, 64, 15, 59]$  and optimization objectives  $[67, 63, 2]$  $[67, 63, 2]$  $[67, 63, 2]$  $[67, 63, 2]$  $[67, 63, 2]$  have been proposed, driving rapid advancement in the field of dataset distillation.

To further enhance the testing accuracy of models trained on synthetic datasets during the postevaluation phase, recent studies have incorporated general performance boosting techniques (e.g., soft labels) into the evaluation process. Some methods jointly optimize the generated images and their corresponding unique soft labels  $[18, 31]$  $[18, 31]$  $[18, 31]$ , while decoupled dataset distillation methods  $[59, 41, 46]$  $[59, 41, 46]$  $[59, 41, 46]$  $[59, 41, 46]$ , [48,](#page-12-2) [37\]](#page-12-3) utilize epoch-wise soft labels provided by pre-trained teacher models during post-evaluation phase. Although these works successfully demonstrate that soft labels significantly improve testing accuracy of the validation models, their soft label implementation strategies differ substantially, and performance comparisons with prior methods often fail to account for gains attributable to soft labels.

<sup>∗</sup> See all members in Appendix [A.](#page-14-0) Zhiwei Deng (Google DeepMind) served as an external advisor only.

<span id="page-1-2"></span><span id="page-1-1"></span>

| DC             | <b>DSA</b>               | DM                           | MTT            | DataDAM                      | <b>DATM</b>    | SRe2L          | RDED           | <b>CDA</b>     | <b>DWA</b>     | D4M            | <b>EDC</b>     | G-VBSM         |
|----------------|--------------------------|------------------------------|----------------|------------------------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
| 1K             | 1 <sub>K</sub>           | 1K                           | 1 <sub>K</sub> | 1 <sub>K</sub>               | 1 <sub>K</sub> | 300            | 300            | 300            | 300            | 300            | 300            | 300            |
| 256            | 256                      | 256                          | 256            | 256                          | 256            | 1024           | 100            | 128            | 128            | 1024           | 100            | 1024           |
| <b>SGD</b>     | <b>SGD</b>               | <b>SGD</b>                   | <b>SGD</b>     | <b>SGD</b>                   | <b>SGD</b>     | AdamW          | AdamW          | AdamW          | AdamW          | AdamW          | AdamW          | AdamW          |
| step           | step                     | step                         | step           | step                         | step           | cosine         | cosine         | cosine         | cosine         | cosine         | cosine         | cosine         |
| hard           | hard                     | hard                         | hard           | hard                         | soft           | soft           | soft           | soft           | soft           | soft           | soft           | soft           |
|                |                          |                              |                | $\qquad \qquad \blacksquare$ | single         | multiple       | multiple       | multiple       | multiple       | multiple       | multiple       | multiple       |
| CE             | CE                       | CE                           | CE             | CE                           | <b>SCE</b>     | <b>KL</b>      | <b>KL</b>      | <b>KL</b>      | <b>KL</b>      | <b>KL</b>      | <b>MSE</b>     | <b>MSE</b>     |
|                |                          |                              |                | $\qquad \qquad \blacksquare$ | single         | single         | single         | single         | single         | single         | ensemble       | ensemble       |
| N <sub>o</sub> | Yes                      | Yes                          | <b>Yes</b>     | Yes                          | <b>Yes</b>     | N <sub>o</sub> | N <sub>o</sub> | N <sub>o</sub> | N <sub>o</sub> | N <sub>o</sub> | N <sub>o</sub> | N <sub>o</sub> |
| N <sub>o</sub> | N <sub>o</sub>           | N <sub>o</sub>               | Yes            | N <sub>o</sub>               | <b>Yes</b>     | N <sub>o</sub> | N <sub>o</sub> | N <sub>o</sub> | N <sub>o</sub> | N <sub>o</sub> | N <sub>o</sub> | N <sub>o</sub> |
| N <sub>o</sub> | N <sub>o</sub>           | N <sub>o</sub>               | N <sub>o</sub> | N <sub>o</sub>               | N <sub>o</sub> | Yes            | <b>Yes</b>     | Yes            | Yes            | Yes            | Yes            | <b>Yes</b>     |
|                | $\overline{\phantom{a}}$ | $\qquad \qquad \blacksquare$ |                | $\overline{\phantom{a}}$     | $\sim$         | 0.08, 1.0      | 0.5, 1.0       | 0.08, 1.0      | 0.08, 1.0      | 0.08, 1.0      | 0.5, 1.0       | 0.08, 1.0      |
| N <sub>o</sub> | N <sub>o</sub>           | N <sub>o</sub>               | N <sub>o</sub> | N <sub>o</sub>               | N <sub>o</sub> | N <sub>o</sub> | <b>Yes</b>     | N <sub>o</sub> | N <sub>o</sub> | N <sub>o</sub> | Yes            | N <sub>o</sub> |
| N <sub>o</sub> | N <sub>o</sub>           | N <sub>o</sub>               | N <sub>o</sub> | N <sub>o</sub>               | N <sub>o</sub> | Yes            | <b>Yes</b>     | Yes            | Yes            | Yes            | Yes            | <b>Yes</b>     |
|                |                          |                              |                |                              |                |                |                |                |                |                |                |                |

Table 1: Evaluation configurations of various dataset distillation methods. We separate agent model training hyperparameters (top) and data augmentation (bottom). For each row, different colors highlight the differences in the evaluation setting.

Furthermore, Subsequent studies frequently employ more intensive data augmentation, superior optimizers, and refined training hyper-parameters [\[43,](#page-12-4) [4\]](#page-10-6) during evaluation to maximize model performance, with even randomly sampled images achieving superior results under better postevaluation settings. This practice conflates genuine improvements in dataset quality with performance variations caused by inconsistent evaluation settings, severely impeding progress in dataset distillation and directing subsequent improvements toward suboptimal directions. Based on the aforementioned discussion, we must emphasize that in the growing field of dataset distillation, relying solely on the testing accuracy of validation model as the exclusive criterion for assessing the quality of synthetic datasets exhibits significant unreliability and unfairness when applied across varying settings.

To address these issues, we propose DD-Ranking, a unified evaluation framework, and introduce a new fair and generalizable metric to realign with the original objectives of dataset distillation. Specifically, we first test evaluation models using randomly sampled images under the evaluation settings of various distillation methods to establish baseline performance for different settings. The performance of generated images is then calibrated by calculating the difference from this baseline. On the other hand, we compute the difference between the performance of synthetic datasets under the hard label settings and the maximum achievable performance using the full original dataset. By jointly applying these two adaptive metrics to evaluate existing distillation methods, we derive a new performance indicator that reveals the true differences in distillation capabilities among methods. Building upon this, we also propose a novel metric for evaluating data augmentations. We further examine the robustness of the introduced metrics across diverse application scenarios.

DD-Ranking addresses the inconsistencies present in existing dataset distillation evaluation protocols and unifies various methods under a fair and standardized evaluation framework, thereby establishing a solid baseline and offering valuable insights for future research. The contributions of our benchmark are threefold. First, we standardize evaluation metrics for dataset distillation, resolving the persistent issue of unfair comparisons in test accuracy across different methods. Second, experimental observations from DD-Ranking demonstrate that previous performance improvements commonly originate from the enhanced model training techniques instead of the distilled dataset. Thus, DD-Ranking encourages the community to direct future efforts toward enhancing the informativeness of synthetic data. Third, building upon the era of dataset distillation, we introduce a general and robust metric that serves as a novel evaluation criterion, with broader applicability across data-centric AI tasks.

## 2 Motivation

### 2.1 Overview of Unfairness

The conventional approach to evaluating dataset distillation methods relies on measuring the test accuracy of an agent model trained on the distilled dataset<sup>[2](#page-1-0)</sup>. However, we have identified substantial unfairness in this evaluation paradigm stemming from highly inconsistent training configurations for

<span id="page-1-0"></span> $2$ Our discussion focuses exclusively on image classification datasets, as these are most frequently used.

<span id="page-2-2"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: This image contains two line graphs side-by-side, both plotting Test Accuracy (%) against different methods. The left graph, labeled (a) TinyImageNet IPC50, shows results for 'Hard Label' and 'Soft Label' conditions. The x-axis lists methods: DC, DSA, DM, MTT, DATM, EDF, SRe2L, D4M, RDED. The y-axis ranges from 10 to 60. Two lines are plotted: a blue line with square markers representing 'DD Method' and an orange line with triangle markers representing 'Random w/ DD Method'. The right graph, labeled (b) ImageNet1K IPC10, shows results only for 'Soft Label' conditions. The x-axis lists methods: SRe2L, D4M, G-VBSM, CDA, DWA, RDED, EDC. The y-axis ranges from 20 to 50. Similar to the left graph, a blue line with square markers represents 'DD Method' and an orange line with triangle markers represents 'Random w/ DD Method'.

Figure 1: Test accuracies of the agent model trained on synthetic data distilled by various DD methods and on randomly selected data. Despite soft labels being able to significantly improve the test accuracy, DD methods may fail to outperform random selection under the same training setting.

the agent model. Table [1](#page-1-1) presents a comparative analysis of training parameters and data augmentation employed by various dataset distillation methods on the same target dataset. We use different colors to highlight the differences in the current dataset distillation evaluation settings. We believe that the performance evaluated without a unified and standardized benchmark is not reliable for a fair comparison. Among these inconsistencies, two critical factors significantly undermine the fairness of current evaluation protocols: label representation (including the corresponding loss function) and data augmentation techniques.

### 2.2 Soft Labels

Soft labels significantly improve the test accuracy. Soft labels have been extensively employed in various domains, particularly in knowledge distillation tasks. Unlike hard labels, which assign discrete categorical values, soft labels represent probability distributions across class categories. These distributions are typically derived from the output logits of pretrained models. Recently, applying soft labels has emerged as a popular approach in evaluating dataset distillation methods. In this framework, each distilled image is associated with one or multiple soft labels generated by a pretrained teacher model. For instance, DATM [\[18\]](#page-11-2) concurrently optimizes synthetic data and corresponding soft labels during bi-level optimization procedures. SRe2L [\[59\]](#page-13-2) employs a teacher model to generate multiple soft labels per data sample at test time. Consequently, the training objective for an agent model on the distilled dataset becomes minimizing the loss (e.g., Kullback–Leibler divergence) between its output logits and these soft labels. Due to this knowledge distillation paradigm, evaluation metrics with soft labels consistently demonstrate substantially higher performance, as illustrated in Figure [1.](#page-2-0)

Improvements originate from knowledge distillation [\[37\]](#page-12-3), instead of synthetic data. We argue that the observed enhancement in test accuracy is predominantly attributable to knowledge distillation from soft labels, rather than any inherent improvement in the informativeness of the distilled data. To substantiate this claim, we conducted a comparative analysis examining test accuracies across random noises annotated with soft labels, randomly selected samples annotated with soft labels, and several baselines using soft labels. Throughout this experiment, we control

<span id="page-2-1"></span>

| dataset  | EDC. |      | RDED |      |  |
|----------|------|------|------|------|--|
| ipc      | 10   | 50   | 10   | 50   |  |
| w/ aug.  | 48.6 | 58.0 | 42.0 | 56.5 |  |
| w/o aug. | 12.5 | 39.7 | 15.3 | 27.9 |  |

Table 2: Ablation on ImageNet1K. Data augmentation largely contributes to the high accuracy, especially on high-resolution datasets.

all other training parameters the same across each baseline comparison, such as the identical teacher model, learning rate, and optimizer.

As demonstrated in Figure [1,](#page-2-0) data randomly selected from the original dataset but annotated with soft labels consistently outperforms baseline-distilled data in most cases. Moreover, even random noise patterns labeled with soft labels achieve non-negligible test accuracy, substantially exceeding random guessing. These findings conclude that while soft labeling techniques certainly elevate test accuracy metrics, they also obscure meaningful assessment of the intrinsic quality and representational capacity of the distilled data itself.

<span id="page-3-0"></span>

### 2.3 Data Augmentation

Data augmentation is a widely used technique to enhance model training performance. Current dataset distillation methods also apply various augmentation techniques during their evaluation process. As shown in Table [1,](#page-1-1) there is significant diversity in the augmentation strategies used by existing dataset distillation methods, with different approaches typically adopting different sets of transformations. However, this variation makes it difficult to fairly evaluate and compare different dataset distillation methods because improvements in test accuracy brought about by data augmentation do not necessarily reflect the inherent quality of the distilled data itself.

To better demonstrate this claim, we conducted a comparative analysis of two established baseline methods, measuring their performance both with and without their respective data augmentation. As depicted in Table [2,](#page-2-1) a substantial portion of the reported performance gains can be directly attributed to augmentation rather than to the intrinsic quality of the distilled datasets. Therefore, similar to soft labels, these results highlight the need for new evaluation metrics that more accurately capture the true informational value of distilled data, instead of relying solely on raw test accuracy that can be inflated by augmentation techniques.

### 3 DD-Ranking

### 3.1 Overview

Motivated by the unfairness above, we introduce DD-Ranking. DD-Ranking is an integrated and easyto-use evaluation benchmark for dataset distillation (DD). It aims to provide a fair evaluation scheme for DD methods that can decouple the impacts from knowledge distillation and data augmentation to reflect the real informativeness of the distilled data. Under the finding that the test accuracy no longer fits the need for fair and comprehensive evaluation, we design new metrics for both the label representation and data augmentation.

### 3.2 Label-Robust Score

Hard label recovery. The initial goal of dataset distillation is to synthesize a small number of data points that do not need to come from the correct data distribution, but will, when given to the learning algorithm as training data, approximate the model trained on the original data [\[54\]](#page-13-0). Given that almost all existing classification datasets use hard label annotation, we think it is crucial for DD methods to maintain good performance with hard labels. To this end, we propose the **hard label recovery** (HLR). Specifically, for both hard-label-based and soft-label-based methods, we evaluate the test accuracy of the distilled data and that of the original dataset with hard labeling, denoted as acc<sub>syn-hard</sub> and acc<sub>real-hard</sub>, respectively. The hard label recovery is computed by taking the difference:

$$
HLR = acc_{real-hard} - acc_{syn-hard}
$$
 (1)

A smaller HLR indicates that the distilled data enables the agent model to recover more of the performance of the same model trained on the full dataset.

Improvement over random. Despite the popularity of applying soft labels to evaluate DD methods, it's not fair to directly compare methods with soft labels against methods with hard labels. Also, there isn't a unified recipe for soft-label-based training, and differences such as how many soft labels per sample, loss function, and temperature could significantly impact the results. This makes it difficult to compare different soft-label-based methods. Thus, to make different methods comparable under mixed label types, we propose **improvement over random (IOR)**. This metric is based on the common sense that any DD method should at least outperform random selection under the same training recipe, and we use the relative performance improvements over random selection to compare any pair of DD methods. Specifically, denote the test accuracy of the model trained on synthetic data with any label type and that on a randomly selected subset (the capacity (e.g., image per class) is kept the same as the synthetic data) with that label type as  $acc_{syn-any}$  and  $acc_{rdm-any}$ , respectively. For each DD method, we keep all of its evaluation settings (such as data augmentation, loss function, learning rate, etc.) unchanged when training the agent model on random data. Then, the IOR is computed by:

$$
IOR = acc_{syn-any} - acc_{rdm-any}
$$
 (2)

<span id="page-4-1"></span>IOR is positively related to the performance of DD methods. By doing so, we can effectively disentangle the improvement brought solely by knowledge distillation and reflect the true informativeness of the distilled data.

Label-robust score Combining hard label recovery (HLR) and improvement over random (IOR), we present the label-robust score (LRS). LRS first takes a weighted sum  $\alpha$  of HLR and IOR via a weight parameter  $\lambda$  as follows:

$$
\alpha = \lambda \text{IOR} - (1 - \lambda) \text{HLR} \tag{3}
$$

We assign a negative mark to HLR so that both parts of the sum are positively correlated with the performance. The raw range of  $\alpha$  is between  $[-1, 1]$ , so we normalize LRS to the range  $[0, 1]$  by letting LRS =  $100\% \times (e^{\alpha} - e^{-1})/(e - e^{-1})$ . A higher LRS indicates that the distilled dataset of the corresponding method is more robust to the label representation and has richer information.

<span id="page-4-0"></span>

### 3.3 Augmentation-Robust Score

Data augmentation, as a trick to enhance model training, doesn't reveal the quality of the dataset itself. Thus, the improvement in test accuracy brought merely by data augmentation at test time should not be attributed to the effectiveness of the dataset distillation method. To disentangle data augmentation's impact, we introduce the **augmentation-robust score (ARS)** which continues to leverage the relative improvement over randomly selected data. Specifically, we first evaluate synthetic data and a randomly selected subset under the same setting to obtain  $acc_{sym-aug}$  and  $acc_{rdm-aug}$  (same as IOR). Next, we evaluate both synthetic data and random data again without the data augmentation, and results are denoted as  $acc_{syn\text{-}naug}$  and  $acc_{rdm\text{-}naug}$ .

We claim that an informative subset via distillation should surpass any randomly selected subset of the same size, regardless of the use of data augmentation. Thus, both differences, acc<sub>syn-aug</sub> − acc<sub>rdm-aug</sub> and acc<sub>syn-naug</sub> – acc<sub>rdm-naug</sub>, are positively correlated to the real informativeness of the distilled dataset. We take a weighted sum of the two differences

$$
\beta = \gamma (acc_{syn-aug} - acc_{rdm-aug}) + (1 - \gamma) (acc_{syn-aug} - acc_{rdm-naug})
$$
\n(4)

and use a similar normalization method to compute ARS. A higher ARS indicates that the distilled dataset of the corresponding method is more robust to data augmentation.

### 4 Results

### 4.1 Evaluation Settings

Baseline. We evaluate a wide range of representative works in dataset distillation. For hard-label methods, we evaluate DC [\[67\]](#page-13-3), DSA [\[62\]](#page-13-5), MTT [\[2\]](#page-10-5), DM [\[63\]](#page-13-4), and DataDAM [\[39\]](#page-12-5). For soft-label methods, we evaluate SRe2L [\[59\]](#page-13-2), DATM [\[18\]](#page-11-2), EDF [\[50\]](#page-12-6), DWA [\[13\]](#page-10-7), RDED [\[48\]](#page-12-2), CDA [\[58\]](#page-13-6), EDC [\[43\]](#page-12-4), and G-VBSM [\[42\]](#page-12-7). In the case where the method provides its distilled data, we adopt it directly. In the case where the distilled data is absent, we strictly follow their implementation provided in both the paper and code repo to replicate their results.

Dataset. We report DD-Ranking benchmarking results on the four existing datasets: CIFAR-10 [\[22\]](#page-11-4), CIFAR-100 [\[22\]](#page-11-4), TinyImageNet [\[24\]](#page-11-5), and ImageNet1K [\[38\]](#page-12-8). The resolution of images in CIFAR-10 and CIFAR-100 is  $32 \times 32$ . The resolution of images in TinyImageNet is  $64 \times 64$ . The resolution of images in ImageNet1K is  $224 \times 224$ . We only report ARS results on ImageNet1K due to space limit. More results can be found in our leaderboard.

Model. For each baseline method, we use the model architecture reported in the paper for evaluation. This includes ConvNet of depth 3 and 4 with instance normalization, ConvNet of depth 3 and 4 with batch normalization, and ResNet-18 [\[19\]](#page-11-6). Additionally, to validate the robustness of DD-Ranking on different model architectures, we incorporate AlexNet [\[23\]](#page-11-7), ResNet-50, VGG-11 [\[45\]](#page-12-9), Swin-Transformer-tiny [\[30\]](#page-11-8), and Vision-Transformer-base [\[10\]](#page-10-8).

DD-Ranking evaluation. The evaluation is performed 5 times with different random seeds. We report **the mean value** in the following tables. When computing the accuracy under hard labels, we perform the hyperparameter search for the learning rate and report the best one. When computing the accuracy under soft labels, we regard the learning provided by each method as the **optimal learning** rate by default, and the learning rate search is performed for random selection.

<span id="page-5-0"></span>

| ipc<br>metric | 1           |             |             | 10          |             |             | 50          |             |             |
|---------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|
|               | $HLR↓$      | $IOR↑$      | $LRS↑$      | $HLR↓$      | $IOR↑$      | $LRS↑$      | $HLR↓$      | $IOR↑$      | $LRS↑$      |
| DC            | 52.7        | 12.4        | 19.1        | 36.7        | 18.5        | 23.2        | 26.3        | 12.3        | 24.0        |
| DSA           | 58.9        | 13.2        | 18.2        | 35.1        | 19.6        | 23.7        | 27.4        | 11.0        | 23.5        |
| MTT           | 42.2        | 27.6        | 23.9        | <b>23.7</b> | 30.9        | 28.4        | <b>16.5</b> | 20.5        | 27.8        |
| DM            | 61.4        | 8.7         | 17.0        | 39.4        | 16.1        | 22.2        | 25.1        | 12.7        | 24.3        |
| DATADAM       | 49.9        | 15.6        | 20.0        | 34.8        | 19.9        | 23.8        | 21.9        | 15.8        | 25.6        |
| DATM          | <b>41.9</b> | <b>30.8</b> | <b>24.6</b> | 26.8        | <b>35.1</b> | <b>28.7</b> | 18.9        | <b>23.9</b> | <b>28.0</b> |
| SRe2L         | 69.9        | $-0.3$      | 14.3        | 67.8        | $-5.7$      | 13.8        | 62.9        | $-6.5$      | 14.4        |
| RDED          | 60.6        | 2.4         | 16.2        | 50.7        | 1.1         | 17.6        | 36.0        | $-1.6$      | 19.6        |
| D4M           | 51.1        | 6.7         | 18.4        | 39.9        | 9.1         | 20.8        | 27.0        | 6.6         | 22.8        |

Table 3: Label-robust score evaluation results on CIFAR-10. We also report the hard-label recovery and improvement over random for a more comprehensive comparison. The color scheme corresponds to that of Figure [1.](#page-2-0) The  $\lambda$  is set to 0.5 for this and the following results. On CIFAR-10, hard-labelbased methods perform generally better.

<span id="page-5-1"></span>

| ipc<br>metric  | 1    |      |      | 10          |             |             | 50   |       |      |
|----------------|------|------|------|-------------|-------------|-------------|------|-------|------|
|                | HLR↓ | IOR↑ | LRS↑ | HLR↓        | IOR↑        | LRS↑        | HLR↓ | IOR↑  | LRS↑ |
| DC             | 39.4 | 8.4  | 20.8 | 25.5        | 12.7        | 24.2        | 21.8 | 1.1   | 22.7 |
| <b>DSA</b>     | 46.0 | 8.5  | 19.6 | 26.1        | 13.5        | 24.3        | 21.2 | 2.0   | 23.0 |
| <b>MTT</b>     | 35.2 | 16.7 | 23.1 | <b>18.0</b> | <b>20.7</b> | <b>27.5</b> | 12.1 | 11.6  | 26.8 |
| <b>DM</b>      | 48.0 | 6.1  | 18.9 | 30.1        | 10.7        | 23.0        | 16.6 | 7.2   | 24.9 |
| <b>DATADAM</b> | 45.2 | 9.1  | 19.9 | 25.9        | 14.8        | 24.6        | 12.4 | 11.8  | 26.8 |
| <b>DATM</b>    | 24.1 | 18.5 | 25.7 | 18.9        | 18.4        | 26.8        | 10.3 | 26.1  | 30.4 |
| SRe2L          | 52.7 | -1.9 | 16.7 | 50.5        | -14.8       | 15.0        | 46.2 | -11.5 | 16.2 |
| <b>RDED</b>    | 45.6 | -0.5 | 18.1 | 37.5        | -1.2        | 19.4        | 27.3 | -1.5  | 21.2 |
| D4M            | 30.9 | 10.0 | 22.7 | 40.1        | 9.7         | 20.9        | 26.7 | 13.5  | 24.2 |

Table 4: LRS, HLR, and IOR evaluation results on CIFAR-100. DATM constantly performs the best and outperforms random selection to a large extent. This implies that soft labels are effective in improving synthetic data when used properly.

### 4.2 Label-Robust Score

Results on CIFAR-10, CIFAR-100, and TinyImageNet. Tables [3,](#page-5-0) [4,](#page-5-1) and [5](#page-6-0) present LRS evaluation results on CIFAR-10, CIFAR-100, and TinyImageNet, respectively. Among hard-label-based methods, trajectory matching (MTT) achieves the best performance, outperforming both gradient matching approaches (DC and DSA) and distribution matching methods (DM and DataDAM). As IPC increases, the distribution matching methods perform better than the gradient matching methods. Within the soft-label-based category, methods that optimize one-to-one soft labels jointly with synthetic data (DATM) demonstrate superior performance compared to approaches that directly utilize multiple soft labels from teacher models (D4M, SRe2L, and RDED). D4M, which employs a generative modeling approach, outperforms decoupled methods, especially when IPC increases. Across all methods, DATM emerges as the strongest baseline. Notably, hard-label-based methods yield results closer to full-dataset performance with hard labels and exhibit greater improvement over random data selection compared to their soft-label counterparts.

Results on ImageNet1K. Table [6](#page-6-1) presents LRS results of various methods on ImageNet1K. All existing methods capable of efficiently scaling to ImageNet1K employ soft labeling techniques. Remarkably, current DD methods consistently underperform random selection across most IPC settings when soft labeling is also applied to randomly selected data. This performance gap widens as IPC increases. While these methods achieve notably high accuracy when using soft labels, their performance under hard labels deteriorates significantly, revealing a substantial gap compared to the real dataset.

<span id="page-6-0"></span>

| ipc | metric      | 1                                              |                                                  |                                                  | 10                                             |                                                  |                                                  | 50                                             |                                                  |                                                  |
|-----|-------------|------------------------------------------------|--------------------------------------------------|--------------------------------------------------|------------------------------------------------|--------------------------------------------------|--------------------------------------------------|------------------------------------------------|--------------------------------------------------|--------------------------------------------------|
|     |             | HLR <span style="vertical-align:sub;">↓</span> | IOR <span style="vertical-align:super;">↑</span> | LRS <span style="vertical-align:super;">↑</span> | HLR <span style="vertical-align:sub;">↓</span> | IOR <span style="vertical-align:super;">↑</span> | LRS <span style="vertical-align:super;">↑</span> | HLR <span style="vertical-align:sub;">↓</span> | IOR <span style="vertical-align:super;">↑</span> | LRS <span style="vertical-align:super;">↑</span> |
|     | DC          | 28.6                                           | 3.9                                              | 22.0                                             | 21.5                                           | 7.1                                              | 23.9                                             | 21.3                                           | -2.1                                             | 22.2                                             |
|     | <b>DSA</b>  | 30.3                                           | 3.7                                              | 21.6                                             | 20.3                                           | 6.8                                              | 24.1                                             | 17.6                                           | 7.6                                              | 25.8                                             |
|     | <b>MTT</b>  | 30.7                                           | 5.8                                              | 21.9                                             | <b>15.6</b>                                    | <b>14.6</b>                                      | <b>26.7</b>                                      | 15.6                                           | 10.2                                             | 26.4                                             |
|     | DM          | 36.7                                           | 2.3                                              | 20.2                                             | 26.2                                           | 7.5                                              | 23.1                                             | 18.9                                           | 5.3                                              | 24.1                                             |
|     | <b>DATM</b> | <b>25.4</b>                                    | <b>8.6</b>                                       | <b>23.5</b>                                      | 18.3                                           | 14.2                                             | 26.0                                             | 13.5                                           | 15.1                                             | 27.2                                             |
|     | <b>EDF</b>  | 25.8                                           | <b>9.2</b>                                       | <b>23.5</b>                                      | 18.5                                           | <b>15.4</b>                                      | 26.2                                             | 13.8                                           | 15.9                                             | 27.3                                             |
|     | SRe2L       | 45.6                                           | -1.8                                             | 15.4                                             | 43.6                                           | -8.5                                             | 17.1                                             | 33.6                                           | -9.6                                             | 18.6                                             |
|     | <b>RDED</b> | 34.0                                           | 3.9                                              | 21.0                                             | 25.6                                           | 1.8                                              | 23.7                                             | 15.2                                           | -0.6                                             | 23.7                                             |
|     | D4M         | 40.6                                           | -3.0                                             | 18.6                                             | 35.6                                           | -5.8                                             | 18.9                                             | 27.7                                           | 12.8                                             | 23.8                                             |

Table 5: LRS, HLR, and IOR evaluation results on TinyImageNet. For decoupled methods, D4M appears to be more effective when IPC is large, and RDED performs better at smaller IPCs.

<span id="page-6-1"></span>

| ipc<br>metric | 1           |             |             | 10          |             |             | 50          |             |             |
|---------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|-------------|
|               | HLR↓        | IOR↑        | LRS↑        | HLR↓        | IOR↑        | LRS↑        | HLR↓        | IOR↑        | LRS↑        |
| SRe2L         | 56.3        | -1.5        | 16.2        | 55.0        | -15.6       | 14.2        | 53.4        | -13.2       | 14.8        |
| <b>RDED</b>   | <b>55.7</b> | <b>1.6</b>  | <b>16.8</b> | <b>50.2</b> | <b>-0.6</b> | <b>17.4</b> | <b>39.8</b> | <b>-3.6</b> | <b>22.9</b> |
| D4M           | 55.9        | -0.6        | 15.6        | 53.0        | -7.7        | 15.8        | 43.7        | -5.8        | 17.6        |
| <b>DWA</b>    | <b>56.1</b> | <b>-1.2</b> | <b>16.3</b> | <b>54.4</b> | <b>-4.1</b> | <b>16.1</b> | <b>49.7</b> | <b>-7.8</b> | <b>16.3</b> |
| <b>CDA</b>    | <b>56.2</b> | <b>-2.5</b> | <b>16.1</b> | <b>54.9</b> | <b>-8.6</b> | <b>15.3</b> | <b>52.0</b> | <b>-6.7</b> | <b>16.1</b> |
| <b>EDC</b>    | <b>55.7</b> | <b>-0.8</b> | <b>16.4</b> | <b>52.0</b> | <b>-0.4</b> | <b>17.1</b> | <b>41.3</b> | <b>-0.1</b> | <b>18.9</b> |
| G-VBSM        | 56.3        | -1.2        | 16.3        | 55.0        | -7.3        | 15.5        | 44.9        | -5.9        | 17.4        |

Table 6: LRS, HLR, and IOR evaluation results on ImageNet1K. Notably, existing DD methods (mainly decoupled) hardly outperform random selection and perform, and fail to perform well when switched to hard labels.

### Findings. Based on these results, we identify three key insights.

*i) Test accuracy is not a reliable metric when soft labels are employed.* Soft labels demonstrate even higher effectiveness on random data. Notably, on TinyImageNet and ImageNet1K, classifiers trained on random data with soft labels consistently **outperform** those trained on DD-synthesized data. While DATM maintains an advantage over random selection on TinyImageNet, this improvement diminishes substantially when soft labels are applied to random data. This observation reinforces our claim that accuracy improvements with soft labels primarily stem from knowledge distillation rather than the intrinsic informativeness of synthetic data.

*ii) Soft labels enhance synthetic dataset informativeness when jointly optimized.* Among soft-labelbased methods, DATM and EDF employ a distinct approach by assigning unique soft labels to each sample and jointly optimizing both samples and labels during distillation. Unlike generative and decoupled methods that generate soft labels at test time, these optimized soft labels improve synthetic data quality, as evidenced by superior LRS scores. This demonstrates that integrating soft labels into the training process can meaningfully enhance synthetic data quality.

*iii) Matching-based methods remain the strongest baselines.* Despite computational limitations that restrict their scalability to large-scale datasets like ImageNet1K, matching-based methods (encompassing gradient, trajectory, and feature matching) consistently produce more effective distilled datasets. Besides, RDED and D4M appear to be more effective among decoupled methods, implying the importance of the realism of synthetic data.

### 4.3 Augmentation-Robust Score

Table [7](#page-7-0) presents ARS performance metrics for various DD methods applied to ImageNet1K, including IOR results with and without data augmentation as introduced in Section [3.3.](#page-4-0) Most existing decoupled

<span id="page-7-2"></span><span id="page-7-0"></span>

| 1 <sub>DC</sub> |        |        |      |                                                                                           | 10      | 50   |         |         |      |
|-----------------|--------|--------|------|-------------------------------------------------------------------------------------------|---------|------|---------|---------|------|
| metric          |        |        |      | IOR w/o aug† IOR w/ aug† ARS† IOR w/o aug† IOR w/ aug† ARS† IOR w/o aug† IOR w/ aug† ARS† |         |      |         |         |      |
| SRe2L           | $-1.2$ | $-1.5$ | 26.3 | $-4.4$                                                                                    | $-15.6$ | 22.9 | $-21.0$ | $-13.2$ | 20.2 |
| <b>RDED</b>     | 0.8    | 1.6    | 27.4 | 5.6                                                                                       | $-0.6$  | 28.1 | 2.0     | $-3.6$  | 26.7 |
| D4M             | $-0.3$ | $-0.6$ | 26.7 | $-0.5$                                                                                    | $-7.7$  | 25.2 | $-2.0$  | $-5.8$  | 25.3 |
| <b>DWA</b>      | $-1.2$ | $-1.2$ | 26.4 | $-4.0$                                                                                    | $-4.1$  | 25.2 | $-13.0$ | $-7.8$  | 22.7 |
| <b>CDA</b>      | $-1.1$ | $-2.5$ | 26.1 | $-4.9$                                                                                    | $-8.6$  | 24.1 | $-14.1$ | $-6.7$  | 22.7 |
| <b>EDC</b>      | $-0.5$ | $-0.8$ | 26.6 | $-0.3$                                                                                    | $-0.4$  | 26.8 | $-3.2$  | $-0.1$  | 26.2 |
| G-VBSM          | $-1.2$ | $-1.2$ | 26.4 | $-7.9$                                                                                    | $-7.3$  | 23.8 | $-18.0$ | $-5.9$  | 22.1 |

Table 7: Augmentation-robust score (ARS) evaluation results on ImageNet1K. We report both IOR w/ aug ( $\mathrm{acc}_{syn\text{-}aug} - \mathrm{acc}_{rdm\text{-}aug}$ ) and IOR w/o aug ( $\mathrm{acc}_{syn\text{-}naug} - \mathrm{acc}_{rdm\text{-}naug}$ ).  $\gamma$  is 0.5 by default.

<span id="page-7-1"></span>Image /page/7/Figure/2 description: A bar chart displays the "LRS (%)" on the y-axis, ranging from 10 to 25. The x-axis is divided into five categories: "DM CIFAR-10 IPC50", "RDED CIFAR-100 IPC50", "MTT TinyImageNet IPC10", "D4M TinyImageNet IPC10", "SRe2L ImageNet1K IPC10", and "DWA ImageNet1K IPC10". Each category has multiple bars representing different models: Conv-3, AlexNet, ResNet18, VGG11, Conv-4, ResNet50, Swin-T, and ViT-B/16. For "DM CIFAR-10 IPC50", the LRS values are approximately 24.3, 24.5, 23.9, and 24.2. For "RDED CIFAR-100 IPC50", the values are around 21.2, 21.3, 21.1, and 21.4. For "MTT TinyImageNet IPC10", the values are approximately 26.7, 26.5, 26.8, and 26.6. For "D4M TinyImageNet IPC10", the values are about 18.9, 19.1, 18.8, and 18.7. For "SRe2L ImageNet1K IPC10", the values are approximately 14.2, 14.3, 14.1, and 14.4. Finally, for "DWA ImageNet1K IPC10", the values are around 16.1, 16.2, 15.9, and 16.0.

Figure 2: Label-robust scores of various methods with four different agent model architectures. The LRS fluctuation is minimal for each method, indicating that DD-Ranking is robust to different model architectures.

and generative DD methods fail to surpass random selection regardless of augmentation status. Without data augmentation, the performance disparity between DD methods and random selection widens as IPC increases. These findings demonstrate that contemporary DD approaches, despite their heavy reliance on data augmentation strategies, frequently underperform when these same augmentation techniques are applied to simple random selection. Notably, when augmentation is excluded from evaluation, the performance gap between certain DD methodologies and random selection becomes more pronounced, further supporting our assertion that conventional test accuracy metrics no longer serve as an equitable evaluation criterion in this domain.

### 4.4 Analysis

**Robust to model architecture.** Various dataset distillation methods employ different agent models for distillation architecture or evaluation of synthetic data. Despite variations in raw test accuracy across model architectures, the metric used to evaluate dataset distillation performance should remain consistent, with minimal fluctuation in metric values. As shown in Figure [2,](#page-7-1) the Learning Rate Schedule (LRS) results for six methods across different settings, each tested with four distinct model architectures, demonstrate high consistency. This consistency validates the robustness of our benchmark across different model architectures.

Robust to soft labels. In decoupled dataset distillation [\[59,](#page-13-2) [41,](#page-12-0) [46,](#page-12-1) [48\]](#page-12-2), epoch-wise soft labels constitute a crucial component of the synthetic dataset. Recent studies [\[41,](#page-12-0) [43,](#page-12-4) [4\]](#page-10-6) have explored improving test accuracy by leveraging stronger teacher models to provide soft labels without altering the synthetic data itself. However, the validity of this technique remains insufficiently investigated. As shown in Figure [3,](#page-8-0) whether through the use of different teacher models or advanced hybrid soft label strategies by fusing soft labels generated by multiple teachers, our proposed LRS consistently exhibits strong robustness, thereby validating its reliability across diverse soft label settings.

<span id="page-8-1"></span><span id="page-8-0"></span>Image /page/8/Figure/0 description: A bar chart displays the LRS (%) for different models across four datasets: SRe2L, G-VBSM, D4M, and RDED. The models are ResNet-18 (blue), MobileNet-V2 (pink), EfficientNet-B0 (light blue), AlexNet (red), Swin-T-V2 (yellow), and Hybrid (purple). For SRe2L, the LRS values are 14.2, 14.1, 14.2, 14.1, 14.6, and 14.2. For G-VBSM, the values are 15.5, 15.5, 15.3, 15.3, 15.9, and 15.5. For D4M, the values are 15.8, 15.8, 15.6, 15.7, 15.9, and 15.8. For RDED, the values are 17.4, 17.2, 17.2, 17.0, 17.2, and 16.9. The y-axis ranges from 4 to 16, labeled as LRS (%).

Figure 3: Label-robust scores of decoupled distillation methods with different teacher model architectures. The LRS fluctuation is minimal for each method, indicating that DD-Ranking is robust to soft labels generated by different models.

## 5 Related Works

Hard-label-based dataset distillation methods. Hard-label-based DD methods assign categorical labels to synthetic samples, the same as the labels of the real dataset. Matching-based methods are known as representative hard-label-based methods. *i) Gradient matching*: Synthetic data is optimized such that the gradients they induce on a neural network closely match those from real data. Following the pioneering work of Dataset Condensation (DC) [\[67\]](#page-13-3), various works have improved gradient matching, such as DSA [\[62\]](#page-13-5), DCC [\[25\]](#page-11-9), and LCMat [\[44\]](#page-12-10). *ii) Trajectory matching*: Synthetic data are optimized by aligning the training dynamics of models trained on synthetic data with those trained on real data. MTT [\[2\]](#page-10-5) first introduced this approach, where synthetic data is optimized by aligning the training dynamics of models trained on synthetic data with those trained on real data. Building on this, various methods such as TESLA [\[6\]](#page-10-9), FTD [\[11\]](#page-10-10), and ATT [\[28\]](#page-11-10) further enhance trajectory matching by improving memory efficiency and reducing trajectory errors. *iii) Feature matching* is an alternative to gradient or trajectory-based distillation, where synthetic data is optimized to induce similar internal representations as real data. Represented by CAFE [\[51\]](#page-12-11), DM [\[66\]](#page-13-7), and DataDAM [\[39\]](#page-12-5), this approach offers a lightweight framework with comparable performances, especially on large IPC settings.

Soft-label-based dataset distillation methods. DD methods using soft labels employ knowledge distillation during evaluation. Each synthetic sample is assigned to one or multiple soft labels generated by a pretrained teacher model. Among matching-based methods, DATM [\[18\]](#page-11-2), PAD [\[27\]](#page-11-11), and EDF [\[50\]](#page-12-6) optimize the soft labels jointly with synthetic data during trajectory matching. Recently, decoupled methods have demonstrated strong scalability on large datasets such as ImageNet1K by decoupling the bi-level optimization. SRe2L [\[59\]](#page-13-2) first proposed a three-stage "squeeze, recover, and relabel" paradigm. During the relabel stage, soft labels are generated and saved for each synthetic sample. Following this approach, CDA [\[58\]](#page-13-6), DWA [\[48\]](#page-12-2), EDC [\[43\]](#page-12-4), and G-VBSM [\[42\]](#page-12-7) further improve the performance from both data and soft label perspectives. RDED synthesizes condensed data by concatenating core image patches. D4M employs diffusion models to generate high-quality synthetic images.

Dataset distillation benchmark. A notable challenge for dataset distillation is the lack of comprehensive benchmarks. DC-Bench [\[5\]](#page-10-11) is the first large-scale standardized benchmark for dataset condensation methods in general. It provides a comprehensive evaluation for several dataset distillation methods and coreset selection methods. Comp-DD is proposed in EDF [\[50\]](#page-12-6) targeting dataset distillation in complex scenarios. It extracts new subsets from ImageNet1K based on the complexity metric. However, both benchmarks no longer satisfy the need for fair evaluation of dataset distillation methods under the soft label trend. Therefore, DD-Ranking is proposed to solve this problem.

## 6 Conclusion and Future Work

We propose DD-Ranking, a new benchmark that provides a fair and comprehensive evaluation for dataset distillation. DD-Ranking is well motivated by the unfairness originated from inconsistent training settings of existing DD evaluation, especially the use of soft labels and data augmentation. To <span id="page-9-0"></span>this end, DD-Ranking introduces both label robust score and augmentation robust score to disentangle the effect of knowledge distillation via soft labeling and data augmentation, and ultimately reveal the true informativeness of distilled datasets. Hopefully, DD-Ranking can facilitate the development of dataset distillation towards improving data quality instead of accuracy. DD-Ranking is already open-source as a PyPI package with detailed documentation. One potential limitation of the current DD-Ranking is that we only support methods for image classification dataset distillation. We are aware that several works [\[72,](#page-13-8) [55\]](#page-13-9) have extended dataset distillation to other tasks and modalities. In the future, we will constantly integrate more baseline methods into our benchmark and extend DD-Ranking to other modalities.

# References

- <span id="page-10-2"></span>[1] Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. *Advances in neural information processing systems*, 33:1877–1901, 2020. [1](#page-0-0)
- <span id="page-10-5"></span>[2] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [1,](#page-0-0) [5,](#page-4-1) [9,](#page-8-1) [20](#page-19-0)
- <span id="page-10-13"></span>[3] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. *arXiv preprint arXiv:2305.01649*, 2023. [20](#page-19-0)
- <span id="page-10-6"></span>[4] Jiacheng Cui, Zhaoyi Li, Xiaochen Ma, Xinyue Bi, Yaxin Luo, and Zhiqiang Shen. Dataset distillation via committee voting. *arXiv preprint arXiv:2501.07575*, 2025. [2,](#page-1-2) [8](#page-7-2)
- <span id="page-10-11"></span>[5] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. *arXiv preprint arXiv:2207.09639*, 2022. [9](#page-8-1)
- <span id="page-10-9"></span>[6] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023. [9,](#page-8-1) [20](#page-19-0)
- <span id="page-10-3"></span>[7] Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. In S. Koyejo, S. Mohamed, A. Agarwal, D. Belgrave, K. Cho, and A. Oh, editors, *Advances in Neural Information Processing Systems*, volume 35, pages 34391–34404. Curran Associates, Inc., 2022. [1](#page-0-0)
- <span id="page-10-1"></span>[8] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. Bert: Pre-training of deep bidirectional transformers for language understanding. *arXiv preprint arXiv:1810.04805*, 2018. [1](#page-0-0)
- <span id="page-10-0"></span>[9] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020. [1](#page-0-0)
- <span id="page-10-8"></span>[10] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, Jakob Uszkoreit, and Neil Houlsby. An image is worth 16x16 words: Transformers for image recognition at scale, 2021. [5](#page-4-1)
- <span id="page-10-10"></span>[11] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pages 3749–3758, 2023. [9,](#page-8-1) [20](#page-19-0)
- <span id="page-10-12"></span>[12] Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. *ArXiv*, abs/2311.01570, 2023. [20](#page-19-0)
- <span id="page-10-7"></span>[13] Jiawei Du, Xin Zhang, Juncheng Hu, Wenxin Huang, and Joey Tianyi Zhou. Diversity-driven synthesis: Enhancing dataset distillation through directed weight adjustment. In *The Thirtyeighth Annual Conference on Neural Information Processing Systems*, 2024. [5](#page-4-1)
- <span id="page-10-15"></span>[14] Jiawei Du, Xin Zhang, Juncheng Hu, Wenxin Huang, and Joey Tianyi Zhou. Diversity-driven synthesis: Enhancing dataset distillation through directed weight adjustment. In *NeurIPS*, 2024. [20](#page-19-0)
- <span id="page-10-4"></span>[15] Yunzhen Feng, Shanmukha Ramakrishna Vedantam, and Julia Kempe. Embarrassingly simple dataset distillation. In *The Twelfth International Conference on Learning Representations*, 2024. [1](#page-0-0)
- <span id="page-10-14"></span>[16] Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. *ArXiv*, abs/2311.15529, 2023. [20](#page-19-0)

- <span id="page-11-15"></span>[17] Chengcheng Guo, Bo Zhao, and Yanbing Bai. Deepcore: A comprehensive library for coreset selection in deep learning. *arXiv preprint arXiv:2204.08499*, 2022. [20](#page-19-0)
- <span id="page-11-2"></span>[18] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023. [1,](#page-0-0) [3,](#page-2-2) [5,](#page-4-1) [9](#page-8-1)
- <span id="page-11-6"></span>[19] Kaiming He, X. Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. *2016 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, pages 770–778, 2015. [5](#page-4-1)
- <span id="page-11-0"></span>[20] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [1](#page-0-0)
- <span id="page-11-1"></span>[21] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022. [1](#page-0-0)
- <span id="page-11-4"></span>[22] Alex Krizhevsky. Learning multiple layers of features from tiny images. 2009. [5](#page-4-1)
- <span id="page-11-7"></span>[23] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. In F. Pereira, C.J. Burges, L. Bottou, and K.Q. Weinberger, editors, *Advances in Neural Information Processing Systems*, volume 25. Curran Associates, Inc., 2012. [5](#page-4-1)
- <span id="page-11-5"></span>[24] Ya Le and Xuan S. Yang. Tiny imagenet visual recognition challenge. 2015. [5](#page-4-1)
- <span id="page-11-9"></span>[25] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sung-Hoon Yoon. Dataset condensation with contrastive signals. In *ICML*, 2022. [9,](#page-8-1) [20](#page-19-0)
- <span id="page-11-16"></span>[26] Yongmin Lee and Hye Won Chung. Selmatch: Effectively scaling up dataset distillation via selection-based initialization and partial updates by trajectory matching. In *ICML*, 2024. [20](#page-19-0)
- <span id="page-11-11"></span>[27] Zekai Li, Ziyao Guo, Wangbo Zhao, Tianle Zhang, Zhi-Qi Cheng, Samir Khaki, Kaipeng Zhang, Ahmad Sajedi, Konstantinos N Plataniotis, Kai Wang, and Yang You. Prioritize alignment in dataset distillation, 2024. [9,](#page-8-1) [20](#page-19-0)
- <span id="page-11-10"></span>[28] Dai Liu, Jindong Gu, Hu Cao, Carsten Trinitis, and Martin Schulz. Dataset distillation by automatic training trajectories. In *ECCV*, 2024. [9,](#page-8-1) [20](#page-19-0)
- <span id="page-11-17"></span>[29] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Hua Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *ICCV*, pages 17268–17278, 2023. [20](#page-19-0)
- <span id="page-11-8"></span>[30] Ze Liu, Yutong Lin, Yue Cao, Han Hu, Yixuan Wei, Zheng Zhang, Stephen Lin, and Baining Guo. Swin transformer: Hierarchical vision transformer using shifted windows, 2021. [5](#page-4-1)
- <span id="page-11-3"></span>[31] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *arXiv preprint arXiv:2210.12067*, 2022. [1](#page-0-0)
- <span id="page-11-13"></span>[32] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, 2022. [20](#page-19-0)
- <span id="page-11-14"></span>[33] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. In *ICML*, 2023. [20](#page-19-0)
- <span id="page-11-18"></span>[34] Brian B. Moser, Federico Raue, Sebastián M. Palacio, Stanislav Frolov, and Andreas Dengel. Latent dataset distillation with diffusion models. *ArXiv*, abs/2403.03881, 2024. [20](#page-19-0)
- <span id="page-11-12"></span>[35] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *ICLR*, 2021. [20](#page-19-0)

- <span id="page-12-12"></span>[36] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In A. Beygelzimer, Y. Dauphin, P. Liang, and J. Wortman Vaughan, editors, *NeurIPS*, 2021. [20](#page-19-0)
- <span id="page-12-3"></span>[37] Tian Qin, Zhiwei Deng, and David Alvarez-Melis. A label is worth a thousand images in dataset distillation. *arXiv preprint arXiv:2406.10485*, 2024. [1,](#page-0-0) [3](#page-2-2)
- <span id="page-12-8"></span>[38] Olga Russakovsky, Jia Deng, Hao Su, Jonathan Krause, Sanjeev Satheesh, Sean Ma, Zhiheng Huang, Andrej Karpathy, Aditya Khosla, Michael Bernstein, Alexander C. Berg, and Li Fei-Fei. Imagenet large scale visual recognition challenge, 2015. [5](#page-4-1)
- <span id="page-12-5"></span>[39] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 17097–17107, 2023. [5,](#page-4-1) [9](#page-8-1)
- <span id="page-12-16"></span>[40] Yuzhang Shang, Zhihang Yuan, and Yan Yan. Mim4dd: Mutual information maximization for dataset distillation. In *NeurIPS*, 2023. [20](#page-19-0)
- <span id="page-12-0"></span>[41] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 16709–16718, 2024. [1,](#page-0-0) [8,](#page-7-2) [20](#page-19-0)
- <span id="page-12-7"></span>[42] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2024. [5,](#page-4-1) [9](#page-8-1)
- <span id="page-12-4"></span>[43] Shitong Shao, Zikai Zhou, Huanran Chen, and Zhiqiang Shen. Elucidating the design space of dataset condensation. *arXiv preprint arXiv:2404.13733*, 2024. [2,](#page-1-2) [5,](#page-4-1) [8,](#page-7-2) [9,](#page-8-1) [20](#page-19-0)
- <span id="page-12-10"></span>[44] Seung-Jae Shin, Heesun Bae, DongHyeok Shin, Weonyoung Joo, and Il-Chul Moon. Losscurvature matching for dataset selection and condensation. In *AISTATS*, 2023. [9,](#page-8-1) [20](#page-19-0)
- <span id="page-12-9"></span>[45] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition, 2015. [5](#page-4-1)
- <span id="page-12-1"></span>[46] Duo Su, Junjie Hou, Weizhi Gao, Yingjie Tian, and Bowen Tang. Dˆ 4: Dataset distillation via disentangled diffusion model. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 5809–5818, 2024. [1,](#page-0-0) [8](#page-7-2)
- <span id="page-12-15"></span>[47] Duo Su, Junjie Hou, Weizhi Gao, Yingjie Tian, and Bowen Tang. D4m: Dataset distillation via disentangled diffusion model. In *CVPR*, 2024. [20](#page-19-0)
- <span id="page-12-2"></span>[48] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 9390–9399, 2024. [1,](#page-0-0) [5,](#page-4-1) [8,](#page-7-2) [9,](#page-8-1) [20](#page-19-0)
- <span id="page-12-14"></span>[49] Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Hua Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *ArXiv*, abs/2303.04707, 2023. [20](#page-19-0)
- <span id="page-12-6"></span>[50] Kai Wang, Zekai Li, Zhi-Qi Cheng, Samir Khaki, Ahmad Sajedi, Ramakrishna Vedantam, Konstantinos N Plataniotis, Alexander Hauptmann, and Yang You. Emphasizing discriminative features for dataset distillation in complex scenarios. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2025. [5,](#page-4-1) [9,](#page-8-1) [20](#page-19-0)
- <span id="page-12-11"></span>[51] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196–12205, 2022. [9](#page-8-1)
- <span id="page-12-13"></span>[52] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Hua Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe learning to condense dataset by aligning features. In *CVPR*, 2022. [20](#page-19-0)

- <span id="page-13-14"></span>[53] Shaobo Wang, Yicun Yang, Zhiyuan Liu, Chenghao Sun, Xuming Hu, Conghui He, and Linfeng Zhang. Dataset distillation with neural characteristic function: A minmax perspective. In *CVPR*, 2025. [20](#page-19-0)
- <span id="page-13-0"></span>[54] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-0) [4](#page-3-0)
- <span id="page-13-9"></span>[55] Xindi Wu, Byron Zhang, Zhiwei Deng, and Olga Russakovsky. Vision-language dataset distillation. In *TMLR*, 2024. [10](#page-9-0)
- <span id="page-13-11"></span>[56] Shaolei Yang, Shen Cheng, Mingbo Hong, Haoqiang Fan, Xing Wei, and Shuaicheng Liu. Neural spectral decomposition for dataset distillation. In *ECCV*, 2024. [20](#page-19-0)
- <span id="page-13-18"></span>[57] Zeyuan Yin and Zhiqiang Shen. Dataset distillation in large data era. *ArXiv*, 2023. [20](#page-19-0)
- <span id="page-13-6"></span>[58] Zeyuan Yin and Zhiqiang Shen. Dataset distillation via curriculum data synthesis in large data era. *Transactions on Machine Learning Research*, 2024. [5,](#page-4-1) [9](#page-8-1)
- <span id="page-13-2"></span>[59] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *Advances in Neural Information Processing Systems*, 36, 2024. [1,](#page-0-0) [3,](#page-2-2) [5,](#page-4-1) [8,](#page-7-2) [9,](#page-8-1) [20](#page-19-0)
- <span id="page-13-17"></span>[60] Ruonan Yu, Songhua Liu, Zigeng Chen, Jingwen Ye, and Xinchao Wang. Heavy labels out! dataset distillation with label space lightening. *ArXiv*, 2024. [20](#page-19-0)
- <span id="page-13-13"></span>[61] Hansong Zhang, Shikun Li, Pengju Wang, Dan Zeng, and Shiming Ge. M3d: Dataset condensation by minimizing maximum mean discrepancy. In *AAAI*, 2023. [20](#page-19-0)
- <span id="page-13-5"></span>[62] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [5,](#page-4-1) [9,](#page-8-1) [20](#page-19-0)
- <span id="page-13-4"></span>[63] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *arXiv preprint arXiv:2110.04181*, 2021. [1,](#page-0-0) [5,](#page-4-1) [20](#page-19-0)
- <span id="page-13-1"></span>[64] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. *arXiv preprint arXiv:2204.07513*, 2022. [1](#page-0-0)
- <span id="page-13-16"></span>[65] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. *ArXiv*, 2022. [20](#page-19-0)
- <span id="page-13-7"></span>[66] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023. [9](#page-8-1)
- <span id="page-13-3"></span>[67] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021. [1,](#page-0-0) [5,](#page-4-1) [9,](#page-8-1) [20](#page-19-0)
- <span id="page-13-12"></span>[68] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7856–7865, 2023. [20](#page-19-0)
- <span id="page-13-19"></span>[69] Zhenghao Zhao, Yuzhang Shang, Junyi Wu, and Yan Yan. Dataset quantization with active learning based adaptive sampling. In *ECCV*, 2024. [20](#page-19-0)
- <span id="page-13-20"></span>[70] Zhenghao Zhao, Haoxuan Wang, Yuzhang Shang, Kai Wang, and Yan Yan. Distilling long-tailed datasets. In *CVPR*, 2025. [20](#page-19-0)
- <span id="page-13-15"></span>[71] Xinhao Zhong, Hao Fang, Bin Chen, Xulin Gu, Tao Dai, Meikang Qiu, and Shu-Tao Xia. Hierarchical features matter: A deep exploration of gan priors for improved dataset distillation. *ArXiv*, abs/2406.05704, 2024. [20](#page-19-0)
- <span id="page-13-8"></span>[72] Daquan Zhou, Kai Wang, Jianyang Gu, Xiangyu Peng, Dongze Lian, Yifan Zhang, Yang You, and Jiashi Feng. Dataset quantization, 2023. [10](#page-9-0)
- <span id="page-13-10"></span>[73] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *NeurIPS*, 2022. [20](#page-19-0)

<span id="page-14-0"></span>

# A DD-Ranking Team

We provide the full list of DD-Ranking team members as follows (\* denotes equal contribution):

- Zekai Li\* (National University of Singapore)
- Xinhao Zhong\* (National University of Singapore)
- Samir Khaki (University of Toronto)
- Zhiyuan Liang (National University of Singapore)
- Yuhao Zhou (National University of Singapore)
- Mingjia Shi (National University of Singapore)
- Dongwen Tang (National University of Singapore)
- Ziqiao Wang (National University of Singapore)
- Wangbo Zhao (National University of Singapore)
- Xuanlei Zhao (National University of Singapore)
- Mengxuan Wu (National University of Singapore)
- Haonan Wang (National University of Singapore)
- Ziheng Qin (National University of Singapore)
- Dai Liu (Technical University of Munich)
- Kaipeng Zhang (Shanghai AI Lab)
- Tianyi Zhou (A\*STAR)
- Zheng Zhu (Tsinghua University)
- Kun Wang (University of Science and Technology of China)
- Shaobo Wang (Shanghai Jiao Tong University)
- Guang Li (Hokkaido University)
- Junhao Zhang (National University of Singapore)
- Jiawei Liu (National University of Singapore)
- Zhiheng Ma (SUAT)
- Linfeng Zhang (Shanghai Jiao Tong University)
- Yiran Huang (Technical University of Munich)

- Lingjuan Lyu (Sony)
- Jiancheng Lv (Sichuan University)
- Yaochu Jin (Westlake University)
- Zeynep Akata (Technical University of Munich)
- Jindong Gu (Oxford University)
- Peihao Wang (University of Texas at Austin)
- Mike Shou (National University of Singapore)
- Zhiwei Deng (Google DeepMind)
- Qian Zheng (Zhejiang University)
- Hao Ye (Xiaomi)
- Shuo Wang (Baidu)
- Xiaobo Wang (Chinese Academy of Science)
- Yan Yan (University of Illinois at Chicago)
- Yuzhang Shang (University of Illinois at Chicago)
- George Cazenavette (Massachusetts Institute of Technology)
- Xindi Wu (Princeton University)
- Justin Cui (University of California, Los Angeles)
- Tianlong Chen (University of North Carolina at Chapel Hill)
- Angela Yao (National University of Singapore)
- Baharan Mirzasoleiman (University of California, Los Angeles)
- Hakan Bilen (University of Edinburgh)
- Manolis Kellis (Massachusetts Institute of Technology)
- Konstantinos N. Plataniotis (University of Toronto)
- Bo Zhao (Shanghai Jiao Tong University)
- Zhangyang Wang (University of Texas at Austin)
- Yang You (National University of Singapore)
- Kai Wang (National University of Singapore)

Zekai and Xinhao *contribute equally* to this work. Zekai serves as the *project lead*, and Kai Wang is the *corresponding author*.

# B Additional Experiment Results

<span id="page-15-0"></span>Results from Table [3](#page-5-0) to Table [6](#page-6-1) are computed by letting  $\lambda = 0.5$ . By default, we treat the hard label recovery and improvement over random equally important. In Table [8](#page-15-0) to Table [19,](#page-18-0) we report the LRS results under different  $\lambda$ . A larger  $\lambda$  gives higher priority to IOR, and a smaller  $\lambda$  focuses more on HLR. We encourage future newly proposed DD methods to enhance both HLR and IOR.

| λ       | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|---------|------|------|------|------|------|
| DC      | 11.2 | 14.9 | 19.1 | 24.0 | 29.5 |
| DSA     | 9.7  | 13.7 | 18.2 | 23.5 | 29.5 |
| MTT     | 14.3 | 18.7 | 23.9 | 29.8 | 36.6 |
| DM      | 9.0  | 12.8 | 17.0 | 22.0 | 27.6 |
| DATADAM | 11.9 | 15.8 | 20.2 | 25.2 | 30.9 |
| DATM    | 14.4 | 19.2 | 24.6 | 30.9 | 38.2 |
| SRe2L   | 7.0  | 10.4 | 14.3 | 18.8 | 23.9 |
| RDED    | 9.1  | 12.4 | 16.2 | 20.4 | 25.3 |
| D4M     | 11.4 | 14.7 | 18.4 | 22.6 | 27.3 |

Table 8: LRS evaluation results on CIFAR-10 IPC1 under different  $\lambda$ .

| λ       | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|---------|------|------|------|------|------|
| DC      | 15.5 | 19.1 | 23.2 | 27.7 | 32.8 |
| DSA     | 16.0 | 19.6 | 23.7 | 28.3 | 33.4 |
| MTT     | 19.8 | 23.9 | 28.5 | 33.5 | 39.2 |
| DM      | 14.7 | 18.2 | 22.2 | 26.7 | 31.6 |
| DATADAM | 16.1 | 19.7 | 23.8 | 28.4 | 33.5 |
| DATM    | 19.0 | 23.5 | 28.7 | 34.5 | 41.2 |
| SRe2L   | 7.3  | 10.4 | 13.8 | 17.7 | 22.1 |
| RDED    | 11.3 | 14.3 | 17.5 | 21.2 | 25.2 |
| D4M     | 14.3 | 17.4 | 20.8 | 24.6 | 28.7 |

Table 9: LRS evaluation results on CIFAR-10 IPC10 under different  $\lambda$ .

| λ       | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|---------|------|------|------|------|------|
| DC      | 18.3 | 21.1 | 24.0 | 27.2 | 30.6 |
| DSA     | 18.0 | 20.6 | 23.5 | 26.7 | 30.1 |
| MTT     | 21.8 | 24.7 | 27.8 | 31.1 | 34.7 |
| DM      | 18.7 | 21.4 | 24.3 | 27.5 | 30.9 |
| DATADAM | 19.8 | 22.6 | 25.6 | 28.8 | 32.3 |
| DATM    | 21.1 | 24.4 | 28.0 | 31.9 | 36.1 |
| SRe2L   | 8.3  | 11.2 | 14.4 | 18.0 | 22.0 |
| RDED    | 15.1 | 17.3 | 19.6 | 22.1 | 24.8 |
| D4M     | 17.9 | 20.3 | 22.8 | 25.4 | 28.3 |

Table 10: LRS evaluation results on CIFAR-10 IPC50 under different  $\lambda$ .

| λ              | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|----------------|------|------|------|------|------|
| DC             | 14.4 | 17.5 | 20.8 | 24.4 | 28.5 |
| <b>DSA</b>     | 12.7 | 16.0 | 19.6 | 23.7 | 28.2 |
| <b>MTT</b>     | 15.9 | 19.3 | 23.1 | 27.4 | 32.1 |
| DM             | 12.1 | 15.3 | 18.9 | 22.8 | 27.2 |
| <b>DATADAM</b> | 12.9 | 16.2 | 19.9 | 23.9 | 28.5 |
| <b>DATM</b>    | 19.2 | 22.3 | 25.7 | 29.4 | 33.4 |
| SRe2L          | 10.8 | 13.6 | 16.7 | 20.2 | 24.0 |
| <b>RDED</b>    | 12.6 | 15.2 | 18.1 | 21.3 | 24.8 |
| D4M            | 16.9 | 19.7 | 22.7 | 25.9 | 29.5 |

Table 11: LRS evaluation results on CIFAR-100 IPC1 under different  $\lambda$ .

| λ       | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|---------|------|------|------|------|------|
| DC      | 18.6 | 21.3 | 24.3 | 27.4 | 30.8 |
| DSA     | 18.4 | 21.3 | 24.3 | 27.6 | 31.2 |
| MTT     | 21.3 | 24.3 | 27.5 | 30.9 | 34.7 |
| DM      | 17.1 | 19.9 | 23.0 | 26.2 | 29.8 |
| DATADAM | 18.6 | 21.5 | 24.6 | 28.0 | 31.7 |
| DATM    | 20.9 | 23.7 | 26.8 | 30.1 | 33.6 |
| SRe2L   | 11.0 | 12.9 | 15.0 | 17.3 | 19.8 |
| RDED    | 14.7 | 17.0 | 19.4 | 22.0 | 24.9 |
| D4M     | 14.3 | 17.4 | 20.9 | 24.7 | 29.0 |

Table 12: LRS evaluation results on CIFAR-100 IPC10 under different  $\lambda$ .

# C Additional Related Work

We acknowledge that DD-Ranking has not included enough dataset distillation methods. We discuss them here. In the near future, we will continue to extend our benchmark.

| λ       | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|---------|------|------|------|------|------|
| DC      | 19.4 | 21.0 | 22.7 | 24.5 | 26.4 |
| DSA     | 19.6 | 21.2 | 23.0 | 24.8 | 26.8 |
| MTT     | 22.9 | 24.8 | 26.8 | 28.8 | 31.0 |
| DM      | 21.3 | 23.1 | 24.9 | 26.9 | 29.0 |
| DATADAM | 22.9 | 24.8 | 26.8 | 28.9 | 31.1 |
| DATM    | 24.2 | 27.2 | 30.4 | 33.9 | 37.6 |
| SRe2L   | 12.1 | 14.1 | 16.2 | 18.5 | 21.0 |
| RDED    | 17.6 | 19.3 | 21.2 | 23.1 | 25.2 |
| D4M     | 18.3 | 21.1 | 24.2 | 27.5 | 31.1 |

Table 13: LRS evaluation results on CIFAR-100 IPC50 under different  $\lambda$ .

| λ     | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|-------|------|------|------|------|------|
| DC    | 17.4 | 19.6 | 22.0 | 24.5 | 27.2 |
| DSA   | 16.9 | 19.1 | 21.6 | 24.2 | 27.0 |
| MTT   | 16.8 | 19.3 | 21.9 | 24.8 | 27.8 |
| DM    | 15.0 | 17.5 | 20.2 | 23.1 | 26.2 |
| DATM  | 18.5 | 20.9 | 23.5 | 26.2 | 29.2 |
| EDF   | 18.4 | 20.9 | 23.5 | 26.3 | 29.4 |
| SRe2L | 12.5 | 15.1 | 17.9 | 21.0 | 24.3 |
| RDED  | 15.8 | 18.3 | 20.9 | 23.8 | 26.9 |
| D4M   | 13.8 | 16.1 | 18.6 | 21.2 | 24.1 |

Table 14: LRS evaluation results on TinyImageNet IPC1 under different  $\lambda$ .

| λ     | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|-------|------|------|------|------|------|
| DC    | 19.7 | 21.7 | 23.9 | 26.3 | 28.7 |
| DSA   | 20.0 | 22.0 | 24.1 | 26.3 | 28.7 |
| MTT   | 21.9 | 24.2 | 26.7 | 29.3 | 32.1 |
| DM    | 18.2 | 20.6 | 23.1 | 25.8 | 28.7 |
| DATM  | 20.9 | 23.4 | 26.0 | 28.8 | 31.8 |
| EDF   | 20.9 | 23.5 | 26.2 | 29.2 | 32.3 |
| SRe2L | 12.8 | 14.9 | 17.1 | 19.5 | 22.1 |
| RDED  | 18.2 | 20.1 | 22.1 | 24.2 | 26.5 |
| D4M   | 15.1 | 16.9 | 18.9 | 21.1 | 23.3 |

Table 15: LRS evaluation results on TinyImageNet IPC10 under different  $\lambda$ .

| λ     | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|-------|------|------|------|------|------|
| DC    | 19.4 | 20.8 | 22.2 | 23.7 | 25.2 |
| DSA   | 20.9 | 22.8 | 24.8 | 26.9 | 29.1 |
| MTT   | 21.7 | 23.7 | 25.8 | 28.0 | 30.3 |
| DM    | 20.4 | 22.2 | 24.1 | 26.1 | 28.1 |
| DATM  | 22.6 | 24.9 | 27.2 | 29.8 | 32.4 |
| EDF   | 22.5 | 24.9 | 27.3 | 30.0 | 32.8 |
| SRe2L | 15.5 | 17.0 | 18.6 | 20.3 | 22.1 |
| RDED  | 21.4 | 22.5 | 23.7 | 24.8 | 26.0 |
| D4M   | 17.9 | 20.8 | 23.8 | 27.2 | 30.8 |

Table 16: LRS evaluation results on TinyImageNet IPC50 under different  $\lambda$ .

| λ      | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|--------|------|------|------|------|------|
| SRe2L  | 9.9  | 12.9 | 16.2 | 19.9 | 24.0 |
| RDED   | 10.2 | 13.3 | 16.8 | 20.8 | 25.2 |
| D4M    | 10.1 | 13.1 | 16.4 | 20.2 | 24.4 |
| DWA    | 10.0 | 13.0 | 16.3 | 20.0 | 24.1 |
| CDA    | 9.9  | 12.8 | 16.1 | 19.7 | 23.7 |
| EDC    | 10.1 | 13.1 | 16.4 | 20.1 | 24.3 |
| G-VBSM | 10.0 | 12.9 | 16.3 | 20.0 | 24.1 |

Table 17: LRS evaluation results on ImageNet1K IPC1 under different  $\lambda$ .

| λ      | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|--------|------|------|------|------|------|
| SRe2L  | 9.9  | 12.0 | 14.2 | 16.7 | 19.3 |
| RDED   | 11.4 | 14.2 | 17.4 | 20.8 | 24.6 |
| D4M    | 10.6 | 13.0 | 15.8 | 18.7 | 22.0 |
| DWA    | 10.3 | 13.1 | 16.1 | 19.5 | 23.2 |
| CDA    | 10.1 | 12.6 | 15.3 | 18.3 | 21.6 |
| EDC    | 11.0 | 13.9 | 17.1 | 20.6 | 24.6 |
| G-VBSM | 10.1 | 12.7 | 15.5 | 18.6 | 22.1 |

Table 18: LRS evaluation results on ImageNet1K IPC10 under different  $\lambda$ .

<span id="page-18-0"></span>

| λ      | 0.1  | 0.3  | 0.5  | 0.7  | 0.9  |
|--------|------|------|------|------|------|
| SRe2L  | 10.3 | 12.5 | 14.8 | 17.4 | 20.2 |
| RDED   | 14.0 | 16.2 | 18.6 | 21.2 | 23.9 |
| D4M    | 12.9 | 15.1 | 17.6 | 20.2 | 23.0 |
| DWA    | 11.3 | 13.7 | 16.3 | 19.1 | 22.1 |
| CDA    | 10.8 | 13.3 | 16.1 | 19.1 | 22.4 |
| EDC    | 13.7 | 16.2 | 18.9 | 21.9 | 25.1 |
| G-VBSM | 12.6 | 14.9 | 17.4 | 20.0 | 22.9 |

Table 19: LRS evaluation results on ImageNet1K IPC50 under different  $\lambda$ .

<span id="page-19-0"></span>

| Category              | Method                  |
|-----------------------|-------------------------|
| Kernel-based          | <b>KIP-FC</b> [35]      |
|                       | <b>KIP-ConvNet</b> [36] |
|                       | <b>FRePo</b> [73]       |
|                       | <b>RFAD</b> [32]        |
|                       | <b>RCIG</b> [33]        |
| Gradient-matching     | DC [67]                 |
|                       | DSA [62]                |
|                       | DCC [25]                |
|                       | LCMat[44]               |
| Trajectory-matching   | MTT [2]                 |
|                       | TESLA [6]               |
|                       | <b>FTD</b> [11]         |
|                       | SeqMatch [12]           |
|                       | <b>DATM</b> [17]        |
|                       | <b>ATT</b> [28]         |
|                       | <b>NSD</b> [56]         |
|                       | PAD [27]                |
|                       | <b>EDF</b> [50]         |
|                       | SelMatch [26]           |
| Distribution-matching | DM [63]                 |
|                       | <b>CAFE</b> [52]        |
|                       | IDM [68]                |
|                       | <b>DREAM</b> [29]       |
|                       | M3D [61]                |
|                       | <b>NCFD</b> [53]        |
| Generative model      | <b>DiM</b> [49]         |
|                       | <b>GLaD</b> [3]         |
|                       | <b>H-PD</b> [71]        |
|                       | <b>LD3M</b> [34]        |
|                       | <b>IT-GAN</b> [65]      |
|                       | <b>D4M</b> [47]         |
|                       | Minimax Diffusion [16]  |
| Decoupled             | <b>SRe2L</b> [59]       |
|                       | <b>RDED</b> [48]        |
|                       | HeLIO [60]              |
|                       | <b>DWA</b> [14]         |
|                       | <b>CDA</b> [57]         |
|                       | <b>EDC</b> [43]         |
|                       | G-VBSM [41]             |
| Others                | <b>MIM4DD</b> [40]      |
|                       | <b>DQAS</b> [69]        |
|                       | LDD [70]                |

Table 20: Summary of previous works on dataset distillation
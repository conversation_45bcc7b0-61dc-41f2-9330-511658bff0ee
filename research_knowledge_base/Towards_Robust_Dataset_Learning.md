# Towards Robust Dataset Learning

<span id="page-0-0"></span><PERSON><PERSON> of Pittsburgh

Xinda Li University of Waterloo

F<PERSON>ian <PERSON> University of Waterloo

Heng Huang University of Pittsburgh

Hongyang Zhang University of Waterloo

## Abstract

*Adversarial training has been actively studied in recent computer vision research to improve the robustness of models. However, due to the huge computational cost of generating adversarial samples, adversarial training methods are often slow. In this paper, we study the problem of learning a robust dataset such that any classifier naturally trained on the dataset is adversarially robust. Such a dataset benefits the downstream tasks as the natural training is much faster than adversarial training, and demonstrates that the desired property of robustness is transferable between models and data. In this work, we propose a principled tri-level optimization to formulate the robust dataset learning problem. We show that, under an abstraction model that characterizes robust vs. non-robust features, the proposed method provably learns a robust dataset. Extensive experiments on benchmark datasets demonstrate the effectiveness of our new algorithm with different network initializations and architectures.*

## 1. Introduction

Deep learning models are vulnerable to adversarial examples [\[2,](#page-8-0) [30\]](#page-9-0): an adversary can arbitrarily manipulate the prediction results of deep neural networks with slight perturbations to the data. Many defense approaches, including heuristic defenses [\[5,](#page-8-1) [11,](#page-8-2) [14,](#page-8-3) [20,](#page-8-4) [22,](#page-8-5) [25,](#page-8-6) [31,](#page-9-1) [36,](#page-9-2) [38,](#page-9-3) [43,](#page-9-4) [44\]](#page-9-5) and certified defenses [\[1,](#page-8-7)[7,](#page-8-8)[8,](#page-8-9)[12,](#page-8-10)[17,](#page-8-11)[18,](#page-8-12)[26,](#page-9-6)[27,](#page-9-7)[34,](#page-9-8)[35,](#page-9-9)[37,](#page-9-10)[39–](#page-9-11)[42\]](#page-9-12), have been developed to protect deep learning models from these threats. The focus of this paper is on integrating the property of adversarial robustness into a dataset, such that a robust model (against small perturbation to the original test data) can be obtained through *natural training* on the learned dataset.

There are several reasons of studying this problem. 1) Discovered by [\[13\]](#page-8-13) in their seminal work, the desirable property of adversarial robustness is transferable between models and data. We propose a *principled* approach of robust feature extraction with theoretical guarantees and improved empirical performance. 2) Expensive computational cost of most existing defenses hinders their applicability to the scenarios of limited computational resources. Although the task of learning robust dataset might itself be timeconsuming, once the *one-time* task has been outsourced to Machine Learning as a Service (MLaaS), one can benefit from the robust dataset for fast training of their own customized robust models, as natural training only requires light computational cost. 3) Distributing a robust dataset is more flexible than distributing a robust model. This is because loading a robust model requires extensive compatibility among deep learning framework (e.g., PyTorch, TensorFlow, MXNet, Keras, etc.), network architecture, and checkpoint. On the other hand, distributing a robust dataset allows everyone to train a network with their preferred architecture and deep learning framework for downstream tasks. Moreover, a robust dataset can be small, e.g., of size only 10% of the original dataset, making it easy to transmit.

However, there are only few works on robust dataset learning. A related but orthogonal research topic is dataset distillation  $[6, 33]$  $[6, 33]$  $[6, 33]$ , which aims at reducing the scale of dataset by distilling knowledge from a large dataset to a small dataset. Despite dataset distillation has become a popular research topic in machine learning with various applications [\[3,](#page-8-15)[6,](#page-8-14)[23,](#page-8-16)[24,](#page-8-17)[29,](#page-9-14)[45,](#page-9-15)[46\]](#page-9-16), how to learn a *robust* dataset is less explored. To our best knowledge, the only attempt on building a robust dataset is by  $[13]$  on robust feature extraction. Thus, more theoretical and empirical results are desired for an in-depth understanding of robust dataset learning.

The idea behind our robust dataset learning is to represent a classifier as a function of a dataset, so that one can treat the dataset as a learnable parameter of the classifier. Throughout the paper, we name such a classifier the

<span id="page-1-1"></span>*data-parameterized classifier*. We formulate robust dataset learning as a min-max, tri-level optimization problem. We theoretically show the efficiency of our algorithm, and empirically verify our robust dataset learning algorithm on MNIST, CIFAR10, and TinyImageNet datasets.

Summary of contributions. Our work tackles the problem of robust dataset learning and advances the area in the following ways.

- Algorithmically, we formulate robust dataset learning via a tri-level optimization problem, where we parameterize the model weights by data, find adversarial examples of the model, and optimize adversarial loss over the data. This learning objective encourages the algorithm to maximize both clean and robust accuracy of the data-parameterized classifier.
- Theoretically, we investigate this tri-level optimization problem under an abstraction model that characterizes robust vs. non-robust features [\[32\]](#page-9-17), where the objective is to find a dataset that minimizes robust error on the data-parameterized classifier. We show that while the classifier naturally trained on clean dataset is nonrobust, our data-parameterized classifier (trained on the robust dataset) is provably robust.
- Experimentally, we evaluate the clean and robust accuracy of our algorithm on MNIST, CIFAR10, and Tiny-ImageNet. We consider baselines for robust dataset learning, which use datasets generated through adver-sarial attacks or robust feature extraction [\[13\]](#page-8-13). We show that our algorithm outperforms the baselines by a large margin. For example, on the CIFAR10 dataset with 0.25  $\ell_2$  threat model, our method achieves robust accuracy as high as 59.52% under AutoAttack, beating the state-of-the-art 48.20% in the same setting by a large margin.

## 2. Related Works

Dataset distillation. The ultimate goal of dataset distillation is to reduce training dataset size by distilling knowledge from the data.  $[33]$  proposed the first dataset distillation algorithm, which expressed the model parameters with the distilled images and optimized the images using gradient descent method. The subsequent works significantly improved the results by various strategies, such as learning soft labels [\[29\]](#page-9-14), strengthening learning signal through gradient matching [\[46\]](#page-9-16), adopting differentiable Siamese augmentation [\[45\]](#page-9-15), optimizing with the neural tangent kernel under infinite-width limit [\[24\]](#page-8-17), and matching training trajectories [\[6\]](#page-8-14). Dataset distillation has been applied to many machine learning fields, including federated learning [\[28,](#page-9-18) [47\]](#page-9-19), privacy-preserving ML [\[19\]](#page-8-18), and neural architecture search [\[45\]](#page-9-15).

Robust feature extraction. [\[13\]](#page-8-13) studied the existence and pervasiveness of adversarial examples. They theoretically demonstrated that adversarial examples are related to the presence of non-robust features, which are in fact highly predictive to neural networks, but brittle and imperceptible to humans. They proposed an empirical algorithm to separate the robust and non-robust features in the data to verify their theoretical results. A by-product of their algorithm is to obtain a robust model through natural training on the robust features. However, they did not provide a principled approach for robust dataset learning.

# 3. Learning Robust Dataset: A Principled Approach

In this section, we propose a principled optimization formulation for robust dataset learning.

Problem settings. There are two essential settings for the robust dataset learning problem: 1) construct a dataset; 2) obtain a robust model with only natural training on the constructed dataset.

Goal. Given a original dataset  $X<sup>nat</sup>$ , The optimization objective is to find an optimal robust dataset  $X^{\text{rob}}$  such that the neural network that is naturally trained on  $X^{\text{rob}}$  is robust against adversarial perturbation to the clean test data, where the test data follows the same distribution as  $X<sup>nat</sup>$ .

Optimization. The idea behind our method is to represent a classifier as a function of a dataset and to find the optimal dataset such that the classifier is robust against adversarial perturbations. With this idea, we formulate robust dataset learning as a tri-level optimization problem. Denote by  $X^{\text{nat}} := \{(x_i, y_i)\}_{i=1}^n$  the original training data pairs, and by  $X^{\text{rob}} := \{(x_i^{\text{rob}}, y_i)\}_{i=1}^n$  the robust dataset to be learned. Notice we only optimize the data points  $\{x_i^{\text{rob}}\}_{i=1}^n$ and keep the labels  $\{y_i\}_{i=1}^n$  unchanged. Step 1). For a given loss  $\mathcal{L}$ , in the first level we create a data-parameterized classifier  $f_{\theta(X^{rob})}$  through minimizing the loss on  $X^{rob}$ , which is initialized by  $X<sup>nat</sup>$  and updated by gradient descent; Step 2). In the second level, we calculate the adversarial samples  $X^{\text{adv}} := \{(x_i^{\text{adv}}, y_i)\}_{i=1}^n$  of  $X^{\text{nat}}$  by attacking  $f_{\theta(X^{\text{rob}})}$ ; Step 3). In the third level, we search for the optimal  $X^{\text{rob}}$ which minimizes the loss of  $f_{\theta(X^{rob})}$  on  $X^{adv}$ . With the above steps, our optimization problem is given by

<span id="page-1-0"></span>
$$
\min_{X^{\text{rob}}} \frac{1}{n} \sum_{i=1}^{n} \max_{x_i^{\text{adv}}} \in \mathcal{B}(x_i, \epsilon) \mathcal{L}(f_{\theta(X^{\text{rob}})}(x_i^{\text{adv}}), y_i),
$$
s.t.

$$
\theta(X^{\text{rob}}) = \operatorname*{argmin}_{\theta} \frac{1}{n} \sum_{i=1}^{n} \mathcal{L}(f_{\theta}(x_i^{\text{rob}}), y_i),
$$
 $(1)$ 

where  $f_{\theta}$  stands for a neural network parameterized by  $\theta$ ,  $\mathcal{B}(x_i, \epsilon)$  stands for a ball centered at  $x_i^{\text{nat}}$  with radius  $\epsilon$ , and

<span id="page-2-1"></span>Image /page/2/Picture/0 description: The image displays a comparison of original and robust models on MNIST and CIFAR10 datasets under adversarial attacks. The top section shows original MNIST digits (0-9) and robust MNIST digits (0-9), with the robust model achieving 52.36% accuracy under 0.2 L-infinity Autoattack, compared to the original model's 8.28%. The bottom section shows original CIFAR10 images (frog, truck, deer, car, bird, horse, boat) and robust CIFAR10 images, with the robust model achieving 54.74% accuracy under 2/255 L-infinity Autoattack, compared to the original model's 6.28%.

Figure 1. Illustration of original and robust images. For MNIST and CIFAR10, the first row represents the original images, while the second row represents the robust dataset generated by our algorithm. The rightmost column shows the robust accuracy.

 $\mathcal L$  is a loss function, e.g., the cross entropy loss or hinge loss.

Efficient algorithm. However, [Eq. 1](#page-1-0) is hard to be solved as it is a tri-level optimization problem. Denote by  $t$  the current learning epoch. The main difficulty is to find the closed form of the parameterized weight  $\theta(X^{\text{rob}})$  which minimizes the loss w.r.t.  $\{(x_i^{\text{rob}}, y_i)\}_{i=1}^n$ . Because the non-linearity of neural network, the closed form of  $\theta(X^{\text{rob}})$  is always intractable, empirically we use tens of thousands of gradient descent to approximate  $\theta(X^{\text{rob}})$ . However, a memory issue will occur if we store all the gradient information during training. Thus, we use one-step gradient update to estimate  $\theta(X^{\text{rob}})$ 

<span id="page-2-0"></span>
$$
\theta(X^{\text{rob}}) = \underset{\theta}{\text{argmin}} \frac{1}{n} \sum_{i=1}^{n} \mathcal{L}(f_{\theta}(x_i^{\text{rob}}), y_i)
$$
  
$$
\approx \theta_{t-1} - \gamma \frac{1}{n} \sum_{i=1}^{n} \nabla_{\theta} \mathcal{L}(f_{\theta}(x_i^{\text{rob}}), y_i)|_{\theta = \theta_{t-1}}
$$
 $(2)$ 

where  $\theta_{t-1}$  is the network weight from the previous epoch and  $\gamma$  is the learning rate. To solve the inner maximization problem, we apply PGD-attack [\[21\]](#page-8-19) via repeatedly using

$$
x_i^{\text{adv}} \leftarrow \text{Clip}_{\mathcal{B}(x_i^{\text{nat}}, \epsilon)}(x_i^{\text{adv}} + \alpha \text{sign}(\nabla_{x_i^{\text{adv}}}\mathcal{L}(f_{\theta}(x_i^{\text{adv}}), y_i))),
$$

where  $\epsilon$  is the attack budget,  $\mathcal{B}(x_i^{\text{nat}}, \epsilon)$  stands for a ball centered at  $x_i^{\mathsf{nat}}$  with radius  $\epsilon, \mathsf{Clip}_{\mathcal{B}(x_i^{\mathsf{nat}}, \epsilon)}$  is a clip function that restricts adversarial samples to  $\mathcal{B}(x_i^{\text{nat}}, \epsilon)$ , and  $\alpha$  is the step size of the PGD-attack. We use gradient descent to optimize the robust dataset:

$$
x_i^{\text{rob}} \leftarrow x_i^{\text{rob}} - \beta \text{sign}(\frac{1}{n}\sum_{i=1}^n \nabla_{x_i^{\text{rob}}} \mathcal{L}(f_{\theta(\{x_i^{\text{rob}}\}_{i=1}^n)}(x_i^{\text{adv}}), y_i)),
$$

where  $\beta$  is the learning rate for the robust data. Notice that since we focus on the image classification tasks, we apply fast sign gradient method to modify the data. This method can be replaced by other gradient descent methods for a specific task. The details of our robust learning algorithm is shown in [Algorithm 1.](#page-3-0)

Comparing to dataset distillation. The dataset distillation problem is usually formulated by a bi-level optimization problem. For example, the optimization problem of [\[33\]](#page-9-13) is given by

$$
\min_{X^*} \frac{1}{n} \sum_{i=1}^n \mathcal{L}(f_{\theta(X^*)}(x_i), y_i),
$$
 (3)

s.t. 
$$
\theta(X^*) = \theta_0 - \gamma \frac{1}{n} \sum_{i=1}^k \nabla_{\theta} \mathcal{L}(f_{\theta}(x_i^*), y_i^*)|_{\theta = \theta_0},
$$

where  $X^{nat} = \{(x_i, y_i\}_{i=1}^n$  is the natural dataset, and  $X^* = \{(x_i^*, y_i^*\}_{i=1}^k \text{ is the distilled dataset which has significant.}$ icant small volume, i.e.,  $k \ll n$ . There are also different formulations of the dataset distillation problem, e.g., matching training trajectory/gradient, but they still share the similar bi-level optimization framework. Our optimization problem is tri-level with large distilled (robust) dataset  $(k = n)$ , which is harder to solve and requires more carefully algorithm design.

Difference between our work and  $[13]$ .  $[13]$  formulated the robust feature extraction problem by finding  $\{(x_i^{\text{rob}}, y_i)\}_{i=1}^n$  that minimizes  $||g(x_i^{\text{rob}}) - g(x_i)||_2$ , where g is a robust pre-trained representation model trained by adversarial training, and a proper initialization of  $x_i^{\text{rob}}$  is involved to avoid converging to a trivial solution  $x_i$ . As we will see in Sections [4](#page-3-1) and [5,](#page-5-0) our new formulation [Eq. 1](#page-1-0) not only provably extracts the robust feature under a simple abstraction model, but also enjoys an improved empirical performance on many experiment settings compared with [\[13\]](#page-8-13). <span id="page-3-7"></span>Algorithm 1: Robust dataset learning.

**Input:** original training set  $X<sup>nat</sup>$ ; number of training epochs T; classifier f (with weight  $\theta$ ), initialized by  $\theta_0$ ; learning rate  $\gamma$  of classifier; learning rate  $\beta$  of robust dataset; PGD steps for generating adversarial example s; PGD steps size  $\alpha$ ; PGD attack budgets  $\epsilon$ .

initialize classifier weights  $\theta$  with  $\theta_0$ , initialize  $X^{\text{rob}}$ with  $X<sup>nat</sup>$ ;

for *1:T* do

for  $min\text{-}batches\ (b^\text{nat}, y^\text{batch})\subseteq X^\text{nat}$  and  $(b^{rob}, y^{batch}) \subseteq X^{rob}$  do step 1. update classifier with robust data via [Eq. 2:](#page-2-0)  $\theta(X^{\text{rob}}) \leftarrow \theta(X^{\text{rob}}) \gamma \frac{1}{|b^{\mathsf{rob}}|} \sum_{(x,y) \in (b^{\mathsf{rob}},y^{\mathsf{batch}})} \nabla_{\theta} \mathcal{L}(f_{\theta}(x),y);$ step 2. calculate adversarial examples from original training set via PGD attack: for each  $(x^{nat}, y) \in (b^{nat}, y^{batch})$ , initialize  $x^{\text{adv}}$  with  $x^{\text{nat}}$ ; for  $1 : s$  do generate perturbation through FSGM and clip it within a ball centered at  $x^{\text{nat}}$ with radius  $\epsilon$ :  $x^{\mathsf{adv}} \leftarrow \text{Clip}_{\mathcal{B}(x^{\mathsf{nat}}, \epsilon)}(x^{\mathsf{adv}} +$  $\alpha \text{sign}(\nabla_{x^{\text{adv}}} \mathcal{L}(f_{\theta}(x^{\text{adv}}), y))),$ end step 3. for each  $x^{\text{rob}} \in b^{\text{rob}}$ , update robust data by minimizing robust error:  $x^{\mathsf{rob}} \leftarrow x^{\mathsf{rob}} \beta \mathsf{sign}(\frac{1}{|b^{\mathsf{rob}}|}\sum_{(x^{\mathsf{adv}},y)} \nabla_{x^{\mathsf{rob}}} \mathcal{L}(f_\theta(x^{\mathsf{adv}}), y));$ end end **return** robust dataset  $X^{\text{rob}}$ .

<span id="page-3-1"></span><span id="page-3-0"></span>

## 4. Theoretical Analysis

In this section, we present a fairly simple theoretical model to analyze the above-mentioned robust dataset learning problem. Our analysis is structured as follows: In [Sec. 4.1,](#page-3-2) we introduce the problem settings of the data distribution, classifier, and the optimization objective of the robust dataset learning problem; In [Sec. 4.2,](#page-4-0) we prove that the optimal classifier trained on clean dataset can be nonrobust; In [Sec. 4.3,](#page-4-1) we demonstrate that our optimization objective leads to a robust dataset. All proof details can be found in [Appendix A.](#page-10-0)

## <span id="page-3-2"></span>4.1. Problem settings

Setting. We consider the data distribution commonly used in prior works  $[13, 32]$  $[13, 32]$  $[13, 32]$ , where the instance  $x =$   $(x_1, ..., x_{d+1}) \sim \mathcal{D}$  and the label y follow:

<span id="page-3-5"></span>
$$
y \sim \text{Uniform}\{-1, 1\}, \quad x_1 \sim \begin{cases} y, & \text{with prob. } p; \ -y, & \text{with prob. } 1 - p \end{cases} \tag{4}
$$

$$
x_i \sim \mathcal{N}(\mu y, 1), \quad i = 2, 3, \dots, d + 1,
$$

where  $\mathcal{N}(\mu, \sigma^2)$  is a Gaussian distribution with mean  $\mu =$  $o(1)$  and variance  $\sigma^2$ . It consists of strongly-correlated (to the label) feature  $x_1$  and weakly-correlated features  $x_2, ..., x_{d+1}$  (as  $\mu$  is selected small enough). The stronglycorrelated feature is robust against  $\Theta(1)$  perturbations but the weakly-correlated features are not. Thus, a naturally trained classifier on this distribution is non-robust as it puts heavy weights on non-robust features. Besides, it is easy to achieve high natural accuracy on this data distribution. For example, we can set  $\mu = \Theta(1/\sqrt{d})$  such that a simple classifier, e.g., sign( $x_2$ ), can achieve at least 99% natural accuracy. The probability  $p$  quantifies the correlation between the feature  $x_1$  and the label y. We can set p to be moderately large, i.e.,  $p = 0.97$ . We note that the same data distribution was used to demonstrate the trade-off between robustness and accuracy [\[32\]](#page-9-17) and to provide a clean abstraction of robust and non-robust features [\[13\]](#page-8-13). In this paper, we use the same distribution to show a separation between natural training on the clean dataset and on the robust dataset returned by our framework.

We model the natural training by a soft SVM loss (a.k.a. the hinge loss) of a linear classifier:

<span id="page-3-4"></span>
$$
\min_{\boldsymbol{w}} \mathcal{L}(\boldsymbol{w}; \mathcal{D}) := \mathbb{E}_{(\boldsymbol{x} \sim \mathcal{D}, y)}[\max\{0, 1 - y\boldsymbol{w}^T \boldsymbol{x}\}] + \lambda ||\boldsymbol{w}||_2^2,
$$
\n(5)

where  $w = (w_1, ..., w_{d+1})$  is the weight vector and  $\lambda > 0$ is a regularization parameter. For a given distribution  $\mathcal{D}',$ the data-parameterized classifier (w.r.t.  $\mathcal{D}'$ ) is given by  $sign(\boldsymbol{w}_{\mathcal{D}'}^T\boldsymbol{x})$ , where  $\boldsymbol{w}_{\mathcal{D}'} := argmin_{\boldsymbol{w}} \mathcal{L}(\boldsymbol{w}; \mathcal{D}')$  is the optimal weight of the SVM w.r.t.  $\mathcal{D}'$ .

**Goal.** Our goal is to create a robust data distribution  $\mathcal{D}'$ , such that  $w_{\mathcal{D}}$  is robust. We formulate this problem via our proposed tri-level optimization framework [Eq. 1,](#page-1-0) where the algorithm is supposed to find  $\mathcal{D}'$  that minimizes the adversarial loss w.r.t. the weight  $w_{\mathcal{D}'}$  and the worst-case perturbation  $\delta$ . In particular, we define our robust dataset learning problem as

<span id="page-3-6"></span>
$$
\min_{\mathcal{D}\'} \mathbb{E}_{(\boldsymbol{x},y)}[\max_{\|\boldsymbol{\delta}\|_{\infty}\leq\epsilon} \max\{0, 1-y\boldsymbol{w}_{\mathcal{D}\'}^T(\boldsymbol{x}+\boldsymbol{\delta})\}] + \lambda \|\boldsymbol{w}_{\mathcal{D}\'}\|_2^2,
$$
  
s.t.  $\boldsymbol{w}_{\mathcal{D}\'} := \operatorname*{argmin}_{\boldsymbol{w}} \mathcal{L}(\boldsymbol{w}; \mathcal{D}\''),$  (6)

<span id="page-3-3"></span><sup>&</sup>lt;sup>1</sup>We study the popular error in this section, where the problem of robust dataset learning reduces to the problem of robust data distribution learning.

<span id="page-4-5"></span>where the inner maximization

$$
\mathbb{E}_{(\boldsymbol{x},y)}[\max_{||\boldsymbol{\delta}||_{\infty}\leq\epsilon} \max\{0,1-y\boldsymbol{w}_{\mathcal{D}'}^T(\boldsymbol{x}+\boldsymbol{\delta})\}]+\lambda||\boldsymbol{w}_{\mathcal{D}'}||_2^2
$$

is the adversarial loss that applies  $\ell_{\infty}$  perturbation with budget  $\epsilon$  to attack  $w_{\mathcal{D}}$ , and the outer minimization optimizes the adversarial loss w.r.t.  $\mathcal{D}'$ . Intuitively, the optimal solution of this min-max problem implies a robust data distribution.

## <span id="page-4-0"></span>4.2. Natural training on the clean dataset is nonrobust

As a comparison, we begin by showing that the optimal classifier of [Eq. 5](#page-3-4) is non-robust.

We consider the robustness of classifiers under  $\ell_{\infty}$  adversarial perturbations with attack budget  $\epsilon$ , which means that an adversary can modify each feature by at most a value of  $\pm \epsilon$ . Note that the first feature of x [\(Eq. 4\)](#page-3-5) is strongly correlated with the label, and the rest  $d$  features are only weakly correlated with the label. We prove that both strongly and weakly-correlated features contribute to the prediction in the optimal classifier, while the effect of the weaklycorrelated features dominates the strongly-correlated one, i.e., the weight of SVM on the weakly-correlated features is larger than the weight on the strongly-correlated feature. Under  $\ell_{\infty}$ -perturbations with  $\epsilon = \Theta(1/\sqrt{d})$ , the positive effect of weakly-correlated features will be overridden by the perturbation, i.e., the weakly-correlated features hurt the prediction under attacks. For example, if  $\epsilon = 2\mu$ , the weakly-correlated features will be shifted to be anti-correlated features by the adversary, i.e.,  $(\mathcal{N}(\mu y, 1) \rightarrow$  $\mathcal{N}(-\mu y, 1)$ . As the weakly-correlated features dominate the prediction of the optimal classifier, the classifier will predict opposite labels under such perturbations. In the following lemma, we formally state the above discussion.

<span id="page-4-6"></span>**Lemma 1.** *If*  $\mu \geq \frac{4}{\sqrt{2}}$  $\frac{d}{dt}$  and  $p \leq 0.975$ , the optimal classi $f$ ier  $\boldsymbol{w}^* = (w_1^*,...,w_{d+1}^*)$  *of [Eq. 5](#page-3-4) achieves more than 99% natural accuracy but less than 0.2% robust accuracy with*  $\ell_{\infty}$ *-perturbation of size*  $\epsilon > 2\mu$ *.* 

This lemma is adapted from Theorem 2 of [\[32\]](#page-9-17), which states that the optimal classifier naturally trained on  $x$  has low accuracy under  $\Theta(1/\sqrt{d})$   $\ell_{\infty}$ -attacks, which indicates achieving robustness on this dataset is non-trivial.

<span id="page-4-1"></span>

## 4.3. Natural training on the robust dataset of our framework is robust

In this part, we will show that the optimal dataset of our min-max optimization [Eq. 6](#page-3-6) can lead to a robust classifier against  $\Theta(1) \ell_{\infty}$ -perturbations.

<span id="page-4-2"></span>We start with calculating the strongest  $\ell_{\infty}$  adversarial perturbations of the SVM classifier.

Lemma 2. *For arbitrary* w*, the optimal* δ *of the maximization problem*

$$
\max_{||\boldsymbol{\delta}||_{\infty}\leq\epsilon} \max\{0, 1 - y\boldsymbol{w}^T(\boldsymbol{x}+\boldsymbol{\delta})\}
$$

*is given by*  $\delta = -\epsilon sign(yw)$ *.* 

This lemma provides a closed form of strongest  $\ell_{\infty}$  adversarial perturbations of the SVM classifier. It's easy to verify this lemma via Holder's inequality  $-yw^T\delta \leq$  $||\boldsymbol{w}||_1 ||\boldsymbol{\delta}||_{\infty} = \epsilon ||\boldsymbol{w}||_1.$  Thus  $\mathbb{E}_{(\boldsymbol{x},y)}[\max\{0,1-y\boldsymbol{w}^T(\boldsymbol{x}+\boldsymbol{w})\}]$  $\{\delta\}(\delta)\} \supseteq \mathbb{E}_{(\bm{x},y)}[\max\{0,1-y\bm{w}^T\bm{x}+\epsilon||\bm{w}||_1\}].$  Taking  $\delta = -\epsilon \text{sign}(y\omega)$ , we can reach this maximum.

According to [Lemma 2,](#page-4-2) the inner maximization problem has a closed form solution

$$
\mathbb{E}_{(\boldsymbol{x},y)}[\max_{\|\boldsymbol{\delta}\|_{\infty}\leq\epsilon}\max\{0,1-y\boldsymbol{w}_{\mathcal{D}'}^T(\boldsymbol{x}+\boldsymbol{\delta})\}]
$$
  
=
$$
\mathbb{E}_{(\boldsymbol{x},y)}[\max\{0,1-y\boldsymbol{w}_{\mathcal{D}'}^T\boldsymbol{x}+\epsilon||\boldsymbol{w}_{\mathcal{D}'}||_1\}].
$$

Thus, we only need to solve the minimization problem

<span id="page-4-3"></span>
$$
\min_{\mathcal{D}'} \mathbb{E}_{(\boldsymbol{x}, y)}[\max\{0, 1 - y\boldsymbol{w}_{\mathcal{D}}^T.\boldsymbol{x} + \epsilon || \boldsymbol{w}_{\mathcal{D}'} ||_1\}] + \lambda || \boldsymbol{w}_{\mathcal{D}'} ||_2^2.
$$
\n(7)

However, it is computationally intractable to find the optimal distribution  $\mathcal{D}'$  directly, as we cannot represent  $w_{\mathcal{D}'}$ with  $\mathcal{D}'$  explicitly. Instead, we try to find the necessary and sufficient conditions of  $w_{\mathcal{D}}$  that minimizes [Eq. 7.](#page-4-3) We show the existence of distributions such that the related  $w_{\mathcal{D}}$  satisfies the conditions.

<span id="page-4-4"></span>Theorem 1. *Let*

$$
\mathcal{D}^* = \underset{\mathcal{D}'}{\operatorname{argmin}} \mathbb{E}_{(\boldsymbol{x},y)}[\max\{0, 1 - y\boldsymbol{w}_{\mathcal{D}'}^T\boldsymbol{x} + \epsilon ||\boldsymbol{w}_{\mathcal{D}'}||_1\}]
$$
  
+  $\lambda ||\boldsymbol{w}_{\mathcal{D}'}||_2^2$ .

If  $1 > \epsilon \geq \mu$  , then  $\bm{w}_{\mathcal{D}^*} := (w_{\mathcal{D}^*}^{(1)}, ..., w_{\mathcal{D}^*}^{(d+1)})$  must satisfy  $w_{\mathcal{D}^*}^{(1)} > 0$  and  $w_{\mathcal{D}^*}^{(2)} = w_{\mathcal{D}^*}^{(3)} = ... = w_{\mathcal{D}^*}^{(d+1)} = 0.$ 

This theorem states a necessary condition of the optimal dataset from [Eq. 6.](#page-3-6) The robust dataset should suffice that the data-parameterized classifier (naturally trained on the dataset) is independent of the weak-correlated features  $x_2, ..., x_{d+1}$ . Thus, the data-parameterized classifier should be more robust than vanilla classifier trained on original dataset.

<span id="page-4-7"></span>Theorem 2. *The optimal SVM w.r.t. the robust dataset* D<sup>∗</sup> *has clean and robust accuracy*  $\geq p$  *under*  $\ell_{\infty}$ *-perturbation (with budget less than 1) on the original dataset. An optimal solution of* D<sup>∗</sup> *is given by*

$$
y \sim Uniform\{-1, 1\}, \quad x_1 \sim \begin{cases} y, & with prob. p; \\ -y, & with prob. 1 - p, \\ x_i = 1, i = 2, 3, ..., d + 1. \end{cases}
$$

<span id="page-5-4"></span>According to [Theorem 1,](#page-4-4) the weight of the optimal SVM learned from  $\mathcal{D}^*$  should satisfy  $w_{\mathcal{D}^*}^{(2)} = w_{\mathcal{D}^*}^{(3)} = ... =$  $w_{\mathcal{D}^*}^{(d+1)} = 0$ . Thus the clean and robust accuracy are only related to the first feature of  $x$ . It is easy to see the natural accuracy is equal to the probability that  $sign(x_1) = y$ , which is p. Besides, when the perturbation budget  $\epsilon < 1$ , the adversary does not change the sign of  $x_1$ . Thus the robust accuracy is also p.

Compared to the original distribution  $\mathcal{D}$  [\(Eq. 4\)](#page-3-5), the robust distribution  $\mathcal{D}^*$  keeps the strongly-correlated feature  $x_1$  unchanged and modifies the weakly-correlated features  $x_2, ..., x_{d+1}$  to uncorrelated features (a constant). In this way the optimal SVM trained on the robust distribution will not assign weights on the uncorrelated features, because they do not contribute to the predictions. Thus, the resulting classifier is relatively robust, as it depends only on the strongly-correlated feature.

### 4.4. Extension to general data distributions.

We now show that our theorems in [Sec. 4.3](#page-4-1) hold for a more general distribution. Consider the case where the instance  $x$  and the label  $y$  follow the distribution below:

$$
y \sim \text{Uniform}\{-1, 1\}, \quad x_1 \sim \begin{cases} y, & \text{with prob. } p; \\ -y, & \text{with prob. } 1 - p, \end{cases}
$$
\n
$$
x_i \sim \mathcal{D}_i, \quad i = 2, 3, \dots, d + 1,
$$

where  $\mathcal{D}_i$  are symmetric distributions with mean  $\mu_i \leq 1$ . We prove that the parameterized SVM with the optimal robust dataset  $\mathcal{D}^*$  achieves at least  $p$  clean and robust accuracy under  $\ell_{\infty}$ -perturbation (with budget less than 1). The details can be found in [Sec. A.4.](#page-13-0)

## <span id="page-5-0"></span>5. Experiments

In this section, we conduct comprehensive experiments to demonstrate the effectiveness of our algorithm on MNIST  $[16]$ , CIFAR10  $[15]$ , and TinyImageNet  $[10]$ .

<span id="page-5-3"></span>

### 5.1. Robustness

In this part, we compare the performance of our (robust) data-parameterized model with models obtained from several baseline methods under  $\ell_2$  and  $\ell_{\infty}$  attacks. We use the state-of-the-art attack method—Autoattack [\[9\]](#page-8-23) for evaluating the adversarial robustness of models.

**Baseline.** [\[13\]](#page-8-13) is the only work related to robust dataset learning. We include this work as one of the baseline for  $CIFAR10<sup>2</sup>$  $CIFAR10<sup>2</sup>$  $CIFAR10<sup>2</sup>$  Besides, motivated by adversarial training, we create two other baselines. In adversarial training, we utilize adversarial examples to improve robustness, so we take the adversarial data generated from both natural (see Adv. data of natural classifier in [Table 1\)](#page-6-0) and robust pre-trained classifiers (see Adv. data of robust classifier in [Table 1\)](#page-6-0) as two baseline robust datasets. In order to make a fair comparison, we require all robust datasets to have the same size.

General settings. In data pre-procession phase, we randomly cropped the image to  $28\times28$  for MNIST,  $32\times32$ for CIFAR10, and  $64\times64$  for TinyImageNet with 4 pixes padding. Then we apply random horizontal flip to the images and normalize them with mean 0.1307 and variance 0.3081. During training, we use SGD [\[4\]](#page-8-24) with learning rate 0.01, momentum 0.9, weight decay 5e-4, and cosine learning rate decay to fine tune the models. For the robust pre-train model we train TRADES [\[43\]](#page-9-4) with 0.2  $\ell_{\infty}$  perturbations for MNIST and 4/255  $\ell_{\infty}$  perturbations for CIFAR 10.

Evaluation. During the evaluation, we fine-tune a natural classifier with the given (robust) dataset and evaluate the model with adversarial attacks. For MNIST and CIFAR-10, we use Autoattack [\[9\]](#page-8-23) with various budgets to evaluate the robustness of the models on the test set. Since Autoattack is computationally expensive on TinyImageNet, we evaluate our algorithm with the same set of budgets using PGD-10 attack instead.<sup>[3](#page-5-2)</sup>

Experiment setup. We use a CNN which has two convolutional layers, followed by two fully-connected layers for MNIST. We apply ResNet-18 for CIFAR10 and Tiny-ImageNet. The output size of the last layer is the number of classes of each dataset. In our robust dataset learn-ing algorithm [\(Algorithm 1\)](#page-3-0), we set  $\theta_0$  to be the weights of the classifier. During training, we use PGD-20 to generate adversarial samples for MNIST and PGD-10 for the other two datasets. The step size of PGD attack is selected as  $\epsilon/10$ . Besides, we set the learning rate of the classifier to  $\gamma = 0.01$  and the learning rate of the robust dataset to  $\beta = 0.5/255$ . For the baseline methods, the datasets are generated using PGD attacks on natural and robust pretrain models (See Adv. data of natural and robust classifier in [Table 1\)](#page-6-0). To generate robust pre-train models, we train TRADES [\[43\]](#page-9-4) using the corresponding budgets. During evaluation phase, we use SGD with learning rate 0.01, momentum 0.9, and weight decay 5e-4, to fine tune the same model on all datasets.

Result analysis. [Table 1](#page-6-0) illustrates the experimental results on the three datasets. Compared to the baseline methods, the classifier naturally trained on our robust dataset achieves nearly 10% increase on the robust accuracy on all tasks and

<span id="page-5-1"></span><sup>&</sup>lt;sup>2</sup> [\[13\]](#page-8-13) released a robust dataset on CIFAR10 but did not release the code for generating these images. See [https://github.com/](https://github.com/MadryLab/constructed-datasets) [MadryLab/constructed-datasets](https://github.com/MadryLab/constructed-datasets). In this work, we use the  $\ell_2$ robustness reported in their work for comparison, and evaluate  $\ell_{\infty}$  robustness on the released CIFAR10 dataset.

<span id="page-5-2"></span><sup>3</sup>Additional results of TinyImageNet using Autoattack can be found in [Appendix B.](#page-15-0)

<span id="page-6-2"></span><span id="page-6-0"></span>Table 1. Experimental results of robust dataset learning on MNIST, CIFAR10 and TinyImageNet with Autoattack, where we naturally train classifiers on the datasets created by different methods. Numbers with ∗ refer to the experimental results with 1000-PGD attack reported by the original work.

| <b>MNIST</b>                       | Robust acc $(\%)$ / Natural acc $(\%)$                                   | $0.1~(\ell_{\infty})$              | $0.2~(\ell_{\infty})$       | $1.0(\ell_2)$                                         | $2.0(\ell_2)$     |
|------------------------------------|--------------------------------------------------------------------------|------------------------------------|-----------------------------|-------------------------------------------------------|-------------------|
|                                    | Natural dataset                                                          | 71.73/98.10                        | 8.28/98.10                  | 79.14/98.10                                           | 21.28/98.10       |
|                                    | Adv. data of natural classifier                                          | 84.17/97.41                        | 19.40/94.71                 | 86.81/97.85                                           | 26.58/95.80       |
|                                    | Adv. data of robust classifier                                           | 76.70/97.93                        | 11.79/97.50                 | 79.67/97.58                                           | 24.21/97.83       |
|                                    | Robust dataset (ours)                                                    | 93.53/98.69                        | 52.36/97.29                 | 91.60/98.76                                           | 48.40/98.25       |
|                                    |                                                                          |                                    |                             |                                                       |                   |
|                                    | Robust acc $(\%)$ / Natural acc $(\%)$                                   | 2/255 $(\ell_{\infty})$            | 4/255 $(\ell_{\infty})$     | $0.25(\ell_2)$                                        | $0.5(\ell_2)$     |
|                                    | Natural dataset                                                          | 6.28/93.23                         | 0.02/93.23                  | 9.85/93.23                                            | 0.04/93.23        |
| CIFAR <sub>10</sub>                | Adv. data of natural classifier                                          | 48.21/84.66                        | 17.86/80.48                 | 47.05/81.83                                           | 21.33/81.14       |
|                                    | Adv. data of robust classifier                                           | 10.51/86.06                        | 0.21/85.83                  | 11.68/88.70                                           | 0.39/86.64        |
|                                    | Ilyas et al. $[13]$                                                      | 36.36/77.53                        | 14.56/78.61                 | 48.20*/85.40*                                         | 21.85*/85.40*     |
|                                    | Robust dataset (ours)                                                    | 54.74/87.19                        | 26.79/85.55                 | 59.52/86.59                                           | 27.35/85.10       |
|                                    |                                                                          |                                    |                             |                                                       |                   |
| TinyImageNet                       | Robust acc $(\%)$ / Natural acc $(\%)$                                   | $2/255 \, (\ell_{\infty})$         | 4/255 $(\ell_{\infty})$     | $0.25(\ell_2)$                                        | $0.5(\ell_2)$     |
|                                    | Natural dataset                                                          | 0.34/70.94                         | 0.16/70.94                  | 4.55/70.94                                            | 0.52/70.94        |
|                                    | Adv. data of natural classifier                                          | 12.96/65.22                        | 5.10/65.13                  | 29.97/65.98                                           | 9.36/64.14        |
|                                    | Robust dataset (ours)                                                    | 25.43/60.02                        | 18.42/60.36                 | 39.55/61.13                                           | 25.48/60.92       |
|                                    |                                                                          |                                    |                             |                                                       |                   |
| 1.00                               | 1.00                                                                     | 1.00<br>Adv X1                     |                             | Nat X1                                                |                   |
| $0.75 -$<br>0.50                   | 0.75<br>0.50                                                             | Adv X2<br>0.75<br>0.50             | Nat X1,X2<br>new classifier | Nat X2<br>0.75<br>Adv X1,X2<br>0.50<br>new classifier |                   |
| 0.25                               | 0.25                                                                     | 0.25                               |                             | 0.25                                                  |                   |
| 0.00                               | 0.00                                                                     | 0.00                               |                             | 0.00                                                  |                   |
| $-0.25 -$                          | $-0.25$                                                                  | $-0.25$                            |                             | $-0.25$                                               |                   |
| $-0.50$                            | Adv X1<br>$-0.50$<br>Nat X1<br>Adv X2                                    | $-0.50$                            |                             | $-0.50$                                               |                   |
| $-0.75 -$                          | Nat X2<br>Nat X1.X2<br>$-0.75$<br>robust classifier<br>robust classifier | $-0.75$                            |                             | $-0.75$                                               |                   |
| $-1.00$<br>$-1.0$<br>$-0.5$<br>0.0 | $-1.00$<br>0.5<br>$-1.0$<br>$-0.5$<br>0.5<br>1.0<br>0.0                  | $-1.00$<br>1.0<br>$-1.0$<br>$-0.5$ | 0.0<br>0.5                  | $-1.00$<br>$-1.0$<br>$-0.5$<br>1.0                    | 0.0<br>0.5<br>1.0 |

<span id="page-6-1"></span>Figure 2. The process (from left to right) on how natural training on adversarial examples of a robust classifier leads to a non-robust model. The green line is the robust classifier used to generate adversarial examples, and the red line is the new classifier naturally trained on the adversarial examples of a robust classifier. "Nat X1" and "Nat X2" stand for natural data of class 1 and 2, respectively, and "Adv X1" and "Adv X2" are their adversarial counterparts. From the 4th plot we can see that the new classifier is non-robust.

attacks. We also notice that the classifier trained on the adversarial examples of robust classifier suffers from poor robust accuracy. We provide a simple example [\(Fig. 2\)](#page-6-1) to show that natural training on adversarial examples of a robust classifier may lead to a non-robust model.

Why do we not compare with adversarial training? There are two reasons: 1) while the output of adversarial training is a classifier, in the robust dataset learning task, the input of our evaluation benchmark is a dataset on which we would natually train a classifier. Therefore, adversarial training does not fit our evaluation benchmark. Instead, we modify adversarial training as another baseline "adver-sarial data of robust classifier" in Table [1.](#page-6-0) 2) We remark that our work is not aiming to set a new SOTA benchmark for adversarial defense, but rather to design a time-efficient method that benefits scenarios with limited computational resources. For example, learning a robust CIFAR10 dataset takes around 2 hours on a NVIDIA RTX A5000 GPU; fine tuning a classifier on our robust dataset takes at most 10 minutes. However, adversarial training, e.g., PGD Adversarial Training  $[21]$ , TRADES  $[43]$ , takes more than one day on the same GPU.

## 5.2. Ablation study

In this part, we conduct ablation experiments to study the effect of dataset size and the transferability of our robust dataset to different initialization and architectures. The robust dataset learning settings and evaluation methods are the same as in [Sec. 5.1.](#page-5-3)

Dataset size. We study the effect of different robust dataset

| Robust acc (%) / Natural acc (%) | Threat model            | Natural dataset | Robust dataset (ours) |             |             |
|----------------------------------|-------------------------|-----------------|-----------------------|-------------|-------------|
|                                  |                         |                 | 10% size              | 20% size    | 100% size   |
| <b>MNIST</b>                     | $0.2~(\ell_{\infty})$   | 8.28/98.10      | 25.65/96.11           | 41.87/96.83 | 52.36/97.29 |
|                                  | $2.0~(\ell_2)$          | 21.28/98.10     | 34.47/96.75           | 40.21/97.59 | 48.40/98.25 |
| CIFAR10                          | 2/255 $(\ell_{\infty})$ | 6.28/93.23      | 37.69/83.26           | 43.87/86.10 | 54.74/87.19 |
|                                  | $0.25~(\ell_2)$         | 9.85/93.23      | 42.61/81.50           | 45.54/82.80 | 59.52/86.59 |
| TinyImageNet                     | 2/255 $(\ell_{\infty})$ | 0.34/70.94      | 15.64/60.58           | 17.75/60.35 | 25.43/60.02 |
|                                  | $0.25~(\ell_2)$         | 4.55/70.94      | 24.38/63.42           | 26.71/62.71 | 39.55/61.13 |

<span id="page-7-0"></span>Table 2. Experiments with different size of robust dataset on MNIST, CIFAR10, and TinyImageNet, where we naturally train classifiers on the datasets created by different methods.

<span id="page-7-1"></span>Image /page/7/Figure/2 description: The image displays a grid of eight bar charts, each comparing the accuracy of a "Nat acc" (natural accuracy) and an "Adv acc" (adversarial accuracy) on both a "Natural dataset" and a "Robust dataset" under different initialization and autoattack conditions. The y-axis for all charts represents "Accuracy" ranging from 0 to 100. The top row shows "Initialization 1, 0.2 Autoattack" and "Initialization 1, 2 2 Autoattack" on the left and right respectively, followed by "Initialization 2, 0.2 Autoattack" and "Initialization 2, 2 2 Autoattack" on the left and right. The bottom row shows "ResNet-34, 2/255 Autoattack" and "ResNet-34, 0.25 2 Autoattack" on the left and right, followed by "ResNet-50, 2/255 Autoattack" and "ResNet-50, 0.25 2 Autoattack" on the left and right. In most charts, the "Nat acc" is significantly higher than the "Adv acc" on both datasets. Specifically, for "Initialization 1, 0.2 Autoattack", "Nat acc" is around 98% on both datasets, while "Adv acc" is around 10% on the natural dataset and 50% on the robust dataset. For "Initialization 1, 2 2 Autoattack", "Nat acc" is around 98% on both datasets, while "Adv acc" is around 10% on the natural dataset and 20% on the robust dataset. For "Initialization 2, 0.2 Autoattack", "Nat acc" is around 98% on both datasets, while "Adv acc" is around 10% on the natural dataset and 50% on the robust dataset. For "Initialization 2, 2 2 Autoattack", "Nat acc" is around 98% on both datasets, while "Adv acc" is around 10% on the natural dataset and 20% on the robust dataset. For "ResNet-34, 2/255 Autoattack", "Nat acc" is around 98% on the natural dataset and 80% on the robust dataset, while "Adv acc" is around 5% on the natural dataset and 35% on the robust dataset. For "ResNet-34, 0.25 2 Autoattack", "Nat acc" is around 98% on the natural dataset and 80% on the robust dataset, while "Adv acc" is around 5% on the natural dataset and 35% on the robust dataset. For "ResNet-50, 2/255 Autoattack", "Nat acc" is around 98% on the natural dataset and 80% on the robust dataset, while "Adv acc" is around 5% on the natural dataset and 35% on the robust dataset. For "ResNet-50, 0.25 2 Autoattack", "Nat acc" is around 98% on the natural dataset and 80% on the robust dataset, while "Adv acc" is around 5% on the natural dataset and 35% on the robust dataset.

Figure 3. Experiments of transferability of our robust dataset. Top: Different initialization of the same CNN model on MNIST. We fine tune two CNN models (initialization 1 and initialization 2) on the robust MNIST dataset generated by a third initialization. Bottom: Different architectures on CIFAR10. We fine tune one ResNet-34 and one ResNet-50 models on the robust CIFAR10 generated by ResNet-18.

size, where the robust dataset size is constrained to only 10% and 20% of the original dataset. We evaluate the robustness of classifiers under several adversarial budges for MNIST, CIFAR10 and TinyImageNet. The results are shown in [Table 2.](#page-7-0) We find that even if trained with 10% of the size of the original dataset, the resulting classifier can still achieve 42.61% robust accuracy on CIFAR10 and 24.38% robust accuracy on TinyImageNet.

Different network initializations. We evaluate the transferability of our robust dataset by using different network initializations. In our experiments, we apply the same CNN with different initializations to evaluate the classifier trained with MNIST robust dataset under 0.2  $\ell_{\infty}$  and 2.0  $\ell_2$  Autoattacks. In [Fig. 3](#page-7-1) Top, we see that our robust dataset effectively improves the robustness of the classifier with different network initializations.

Different network architectures. We also investigate the case where the naturally trained network during testing has a different architecture from that we use to learn the robust dataset. In the experiments, we use our robust dataset to fine-tune ResNet-34 and ResNet-50 models. We evaluate the adversarial robustness under 2/255  $\ell_{\infty}$  and 0.25  $\ell_2$  Au<span id="page-7-2"></span>Table 3. Experiments with different random seeds on CIFAR10 and MNIST.

| Robust dataset (ours)                              | <b>MNIST</b>                      |  |  |
|----------------------------------------------------|-----------------------------------|--|--|
| Robust acc / Natural acc<br>$0.2~(\ell_{\infty})$  | $52.51 \pm 0.59 / 98.14 \pm 0.41$ |  |  |
|                                                    | CIFAR <sub>10</sub>               |  |  |
| Robust acc / Natural acc<br>$2/255(\ell_{\infty})$ | $54.37 \pm 0.71/97.29 \pm 0.55$   |  |  |

toattack. In [Fig. 3](#page-7-1) Bottom, we see that our robust dataset enjoys descent transferability across different network architectures.

Different random seeds. Due to the expensive cost of the Autoattack, we are not able to report the confidence score in [Table 1.](#page-6-0) We repeat the experiments in [Table 1](#page-6-0) with 10 different random seeds on 0.2  $\ell_{\infty}$  Autoattack on MNIST and 2/255  $\ell_{\infty}$  Autoattack on CIFAR10, the results are reported in [Table 3.](#page-7-2) From the table we see that our model share similar performance on different random seeds.

## 6. Conclusion

In this work, we propose a principle, tri-level optimization algorithm to solve the robust dataset learning problem. We theoretically prove the guarantee of our algorithm on an abstraction model, and empirically verify its effectiveness and efficiency on three popular image classification datasets. Our proposed algorithm provides a principled way to integrate the property of adversarial robustness into a dataset. The evaluation results of the method imply various real-world applications under scenarios with limited computational resources.

## References

- <span id="page-8-7"></span>[1] Mislav Balunović and Martin Vechev. Adversarial training and provable defenses: Bridging the gap. In *8th International Conference on Learning Representations (ICLR 2020)(virtual)*. International Conference on Learning Representations, 2020. [1](#page-0-0)
- <span id="page-8-0"></span>[2] Battista Biggio, Igino Corona, Davide Maiorca, Blaine Nelson, Nedim Nedim Srndic, Pavel Laskov, Giorgio Giacinto, and Fabio Roli. Evasion attacks against machine learning at test time. In *Joint European conference on machine learning and knowledge discovery in databases*, pages 387–402, 2013. [1](#page-0-0)
- <span id="page-8-15"></span>[3] Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020. [1](#page-0-0)
- <span id="page-8-24"></span>[4] Léon Bottou. Large-scale machine learning with stochastic gradient descent. In *Proceedings of COMPSTAT'2010*, pages 177–186. Springer, 2010. [6](#page-5-4)
- <span id="page-8-1"></span>[5] Yair Carmon, Aditi Raghunathan, Ludwig Schmidt, John C Duchi, and Percy S Liang. Unlabeled data improves adversarial robustness. *Advances in Neural Information Processing Systems*, 32, 2019. [1](#page-0-0)
- <span id="page-8-14"></span>[6] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-8-8"></span>[7] Jeremy M Cohen, Elan Rosenfeld, and J Zico Kolter. Certified adversarial robustness via randomized smoothing. *ICML*, 2019. [1](#page-0-0)
- <span id="page-8-9"></span>[8] Francesco Croce, Maksym Andriushchenko, and Matthias Hein. Provable robustness of relu networks via maximization of linear regions. In *the 22nd International Conference on Artificial Intelligence and Statistics*, pages 2057–2066. PMLR, 2019. [1](#page-0-0)
- <span id="page-8-23"></span>[9] Francesco Croce and Matthias Hein. Reliable evaluation of adversarial robustness with an ensemble of diverse parameter-free attacks. In *International conference on machine learning*, pages 2206–2216. PMLR, 2020. [6](#page-5-4)
- <span id="page-8-22"></span>[10] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009. [6](#page-5-4)

- <span id="page-8-2"></span>[11] Yinpeng Dong, Zhijie Deng, Tianyu Pang, Jun Zhu, and Hang Su. Adversarial distributional training for robust deep learning. *Advances in Neural Information Processing Systems*, 33:8270–8283, 2020. [1](#page-0-0)
- <span id="page-8-10"></span>[12] Sven Gowal, Krishnamurthy Dvijotham, Robert Stanforth, Rudy Bunel, Chongli Qin, Jonathan Uesato, Relja Arandjelovic, Timothy Mann, and Pushmeet Kohli. On the effectiveness of interval bound propagation for training verifiably robust models. *arXiv preprint arXiv:1810.12715*, 2018. [1](#page-0-0)
- <span id="page-8-13"></span>[13] Andrew Ilyas, Shibani Santurkar, Dimitris Tsipras, Logan Engstrom, Brandon Tran, and Aleksander Madry. Adversarial examples are not bugs, they are features. *Advances in neural information processing systems*, 32, 2019. [1,](#page-0-0) [2,](#page-1-1) [3,](#page-2-1) [4,](#page-3-7) [6,](#page-5-4) [7](#page-6-2)
- <span id="page-8-3"></span>[14] Harini Kannan, Alexey Kurakin, and Ian Goodfellow. Adversarial Logit Pairing. In *arXiv:1803.06373*, 2018. [1](#page-0-0)
- <span id="page-8-21"></span>[15] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009. [6](#page-5-4)
- <span id="page-8-20"></span>[16] Yann LeCun. The mnist database of handwritten digits. *http://yann. lecun. com/exdb/mnist/*, 1998. [6](#page-5-4)
- <span id="page-8-11"></span>[17] Mathias Lecuyer, Vaggelis Atlidakis, Roxana Geambasu, Daniel Hsu, and Suman Jana. Certified robustness to adversarial examples with differential privacy. In *2019 IEEE Symposium on Security and Privacy (SP)*, pages 656–672. IEEE, 2019. [1](#page-0-0)
- <span id="page-8-12"></span>[18] Bai Li, Changyou Chen, Wenlin Wang, and Lawrence Carin. Certified adversarial robustness with additive noise. In *Advances in Neural Information Processing Systems*, pages 9464–9474, 2019. [1](#page-0-0)
- <span id="page-8-18"></span>[19] Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Soft-label anonymous gastric x-ray image distillation. In *2020 IEEE International Conference on Image Processing (ICIP)*, pages 305–309. IEEE, 2020. [2](#page-1-1)
- <span id="page-8-4"></span>[20] Fangzhou Liao, Ming Liang, Yinpeng Dong, Tianyu Pang, Xiaolin Hu, and Jun Zhu. Defense against adversarial attacks using high-level representation guided denoiser. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 1778–1787, 2018. [1](#page-0-0)
- <span id="page-8-19"></span>[21] Aleksander Madry, Aleksandar Makelov, Ludwig Schmidt, Dimitris Tsipras, and Adrian Vladu. Towards deep learning models resistant to adversarial attacks. *arXiv preprint arXiv:1706.06083*, 2017. [3,](#page-2-1) [7](#page-6-2)
- <span id="page-8-5"></span>[22] Aamir Mustafa, Salman Khan, Munawar Hayat, Roland Goecke, Jianbing Shen, and Ling Shao. Adversarial defense by restricting the hidden space of deep neural networks. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 3385–3394, 2019. [1](#page-0-0)
- <span id="page-8-16"></span>[23] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020. [1](#page-0-0)
- <span id="page-8-17"></span>[24] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-8-6"></span>[25] Nicolas Papernot, Patrick McDaniel, Xi Wu, Somesh Jha, and Ananthram Swami. Distillation as a Defense to Adversarial Perturbations against Deep Neural Networks. In *arXiv:1511.04508*, 2016. [1](#page-0-0)

- <span id="page-9-6"></span>[26] Aditi Raghunathan, Jacob Steinhardt, and Percy Liang. Certified defenses against adversarial examples. *arXiv preprint arXiv:1801.09344*, 2018. [1](#page-0-0)
- <span id="page-9-7"></span>[27] Gagandeep Singh, Timon Gehr, Matthew Mirman, Markus Püschel, and Martin Vechev. Fast and effective robustness certification. *Advances in neural information processing systems*, 31, 2018. [1](#page-0-0)
- <span id="page-9-18"></span>[28] Ilia Sucholutsky and Matthias Schonlau. Secdd: Efficient and secure method for remotely training neural networks. *arXiv preprint arXiv:2009.09155*, 2020. [2](#page-1-1)
- <span id="page-9-14"></span>[29] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *International Joint Conference on Neural Networks*, pages 1–8, 2021. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-9-0"></span>[30] Christian Szegedy, Wojciech Zaremba, Ilya Sutskever, Joan Bruna, Dumitru Erhan, Ian Goodfellow, and Rob Fergus. Intriguing properties of neural networks. *ICML*, 2014. [1](#page-0-0)
- <span id="page-9-1"></span>[31] Florian Tramer, Nicholas Carlini, Wieland Brendel, and Aleksander Madry. On adaptive attacks to adversarial example defenses. *arXiv preprint arXiv:2002.08347*, 2020. [1](#page-0-0)
- <span id="page-9-17"></span>[32] Dimitris Tsipras, Shibani Santurkar, Logan Engstrom, Alexander Turner, and Aleksander Madry. Robustness may be at odds with accuracy. In *International Conference on Learning Representations*, 2019. [2,](#page-1-1) [4,](#page-3-7) [5,](#page-4-5) [11](#page-10-1)
- <span id="page-9-13"></span>[33] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018. [1,](#page-0-0) [2,](#page-1-1) [3](#page-2-1)
- <span id="page-9-8"></span>[34] Eric Wong and Zico Kolter. Provable defenses against adversarial examples via the convex outer adversarial polytope. In *International Conference on Machine Learning*, pages 5286–5295. PMLR, 2018. [1](#page-0-0)
- <span id="page-9-9"></span>[35] Eric Wong, Frank Schmidt, Jan Hendrik Metzen, and J Zico Kolter. Scaling provable adversarial defenses. *Advances in Neural Information Processing Systems*, 31, 2018. [1](#page-0-0)
- <span id="page-9-2"></span>[36] Dongxian Wu, Shu-Tao Xia, and Yisen Wang. Adversarial weight perturbation helps robust generalization. In *Advances in Neural Information Processing Systems*, pages 2958–2969, 2020. [1](#page-0-0)
- <span id="page-9-10"></span>[37] Kai Y Xiao, Vincent Tjeng, Nur Muhammad Shafiullah, and Aleksander Madry. Training for faster adversarial robustness verification via inducing relu stability. *arXiv preprint arXiv:1809.03008*, 2018. [1](#page-0-0)
- <span id="page-9-3"></span>[38] Cihang Xie, Jianyu Wang, Zhishuai Zhang, Zhou Ren, and Alan Yuille. Mitigating adversarial effects through randomization. *arXiv preprint arXiv:1711.01991*, 2017. [1](#page-0-0)
- <span id="page-9-11"></span>[39] Kaidi Xu, Zhouxing Shi, Huan Zhang, Yihan Wang, Kai-Wei Chang, Minlie Huang, Bhavya Kailkhura, Xue Lin, and Cho-Jui Hsieh. Automatic perturbation analysis for scalable certified robustness and beyond. *Advances in Neural Information Processing Systems*, 33:1129–1141, 2020. [1](#page-0-0)
- [40] Runtian Zhai, Chen Dan, Di He, Huan Zhang, Boqing Gong, Pradeep Ravikumar, Cho-Jui Hsieh, and Liwei Wang. Macer: Attack-free and scalable robust training via maximizing certified radius. *arXiv preprint arXiv:2001.02378*, 2020. [1](#page-0-0)
- [41] Bohang Zhang, Tianle Cai, Zhou Lu, Di He, and Liwei Wang. Towards certifying l-infinity robustness using neural networks with l-inf-dist neurons. In *International Con-*

*ference on Machine Learning*, pages 12368–12379. PMLR, 2021. [1](#page-0-0)

- <span id="page-9-12"></span>[42] Dinghuai Zhang, Mao Ye, Chengyue Gong, Zhanxing Zhu, and Qiang Liu. Black-box certification with randomized smoothing: A functional optimization based framework. *Advances in Neural Information Processing Systems*, 33:2316– 2326, 2020. [1](#page-0-0)
- <span id="page-9-4"></span>[43] Hongyang Zhang, Yaodong Yu, Jiantao Jiao, Eric Xing, Laurent El Ghaoui, and Michael Jordan. Theoretically principled trade-off between robustness and accuracy. In *International conference on machine learning*, pages 7472–7482. PMLR, 2019. [1,](#page-0-0) [6,](#page-5-4) [7](#page-6-2)
- <span id="page-9-5"></span>[44] Jingfeng Zhang, Xilie Xu, Bo Han, Gang Niu, Li zhen Cui, Masashi Sugiyama, and Mohan S. Kankanhalli. Attacks which do not kill training make adversarial learning stronger. In *International Conference on Machine Learning*, 2020. [1](#page-0-0)
- <span id="page-9-15"></span>[45] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-9-16"></span>[46] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021. [1,](#page-0-0) [2](#page-1-1)
- <span id="page-9-19"></span>[47] Yanlin Zhou, George Pu, Xiyao Ma, Xiaolin Li, and Dapeng Wu. Distilled one-shot federated learning. *arXiv preprint arXiv:2009.07999*, 2020. [2](#page-1-1)

<span id="page-10-1"></span><span id="page-10-0"></span>

### A. Additional proofs

<span id="page-10-2"></span>**Lemma 3.** *(Lemma D.1 in [\[32\]](#page-9-17)) The optimal solution*  $w^* = (w_1^*,...,w_{d+1}^*)$  *of our optimization problem [Eq. 5](#page-3-4) must satisfy*  $w_2 = ... = w_{d+1}$  *and sign* $(w_i) \geq 0, i \in [d+1]$ .

*Proof.* We prove this lemma by contradiction, assume w.l.o.g. the optimal solution  $w^* = (w_1^*, ..., w_{d+1}^*)$  satisfying  $w_2^* \neq w_3^*$ , we can let  $w' = (w_1^*, w_3^*, w_2^*, w_4^*, ..., w_{d+1}^*)$ . In this case, we have  $y w^{*T} x = y w'^T x$  because both  $w_2^* x_2 + w_3^* x_3$  and  $w_2^*x_3 + w_3^*x_2$  follow  $\mathcal{N}((w_2^* + w_3^*)\mu y, w_2^*z_2^2 + w_3^*z_2^2)$ . So we have  $\mathcal{L}(w';\mathcal{D}) = \mathcal{L}(w^*;\mathcal{D})$ . Moreover, since the margin term  $\mathbb{E}_{(\bm{x},y)}[\max\{0,1-y\bm{w}^T\bm{x}\}]$  is convex in w, by Jensen's inequality, averaging  $\bm{w}^*$  and  $\bm{w}'$  will not increase the value of that margin term. On the other hand,  $||\frac{w^*+w^*}{2}||_2^2 < ||w^*||_2^2$  as  $2(\frac{w_2^*+w_3^*}{2})^2 < w_2^*^2 + w_3^*^2$  when  $w_2^* \neq w_3^*$ . Thus  $\mathcal{L}(\frac{\mathbf{w}^*+\mathbf{w}'}{2};\mathcal{D}) < \mathcal{L}(\mathbf{w}^*;\mathcal{D})$ , which yields contradiction. Analogously, if there exists i, such that sign $(w_i^*) < 0$ , let  $\mathbf{w}' =$  $(w_1^*, ..., -w_i^*, ..., w_{d+1}^*)$ , we have  $||w'||_2^2 = ||w^*||_2^2$  and  $\mathbb{E}_{(x,y)}[\max\{0, 1 - yw^{T}x\}] \leq \mathbb{E}_{(x,y)}[\max\{0, 1 - yw^{*T}x\}]$ , which yields another contradiction. □

<span id="page-10-3"></span>**Lemma 4.** *(Lemma D.2 in [\[32\]](#page-9-17))* If  $\mu \geq \frac{4}{\sqrt{2}}$ *If*  $\mu \ge \frac{4}{\sqrt{d}}$  and  $p \le 0.975$ , the optimal solution  $\mathbf{w}^* = (w_1^*, ..., w_{d+1}^*)$  of our optimization *problem [Eq. 5](#page-3-4) must satisfy*  $w_1^* < \sqrt{d}w_2^*$ .

*Proof.* Assume for the sake of contradiction that  $w_1^* \geq$  $\sqrt{d}w_2^*$ . By [Lemma 3](#page-10-2) we have  $w_2^* = ... = w_{d+1}^*$ . Assume w.l.o.g.  $||w^*||_2 = 1$ , we have  $w_2^* \leq \frac{1}{\sqrt{2}}$  $\frac{1}{2d}$ . Then, with probability at least  $1 - p$ , the first feature predicts the wrong label and without enough weight, the remaining features cannot compensate for it. Concretely,

$$
\mathbb{E}[\max(0, 1 - y\mathbf{w}^{*T}\mathbf{x})] \ge (1 - p)\mathbb{E}[\max(0, 1 + w_1^* - w_2^* \sum_{i=2}^{d+1} \mathcal{N}(\mu, 1))]
$$
  
\n
$$
\ge (1 - p)\mathbb{E}[\max(0, 1 + \sqrt{d}w_2^* - w_2^* \mathcal{N}(d\mu, d))]
$$
  
\n
$$
\ge (1 - p)\mathbb{E}[\max(0, 1 + 1/\sqrt{2} - \mathcal{N}(\sqrt{\frac{d}{2}}\mu, \frac{1}{2}))].
$$
\n(8)

We will now show that a solution  $w' = (w'_1, ..., w'_{d+1})$  that assigns zero weight on the first feature  $(w'_2 = ... = w'_{d+1} = \frac{1}{\sqrt{d}})$ d and  $w'_1 = 0$ ), achieves a better loss.

$$
\mathbb{E}[\max(0, 1 - y\boldsymbol{w}^{\prime T}\boldsymbol{x})] = \mathbb{E}[\max(0, 1 - \mathcal{N}(\sqrt{d}\mu, 1))]. \tag{9}
$$

By the optimality of  $w^*$  we must have

$$
\mathbb{E}[\max(0, 1 - \mathcal{N}(\sqrt{d}\mu, 1))] \ge (1 - p)\mathbb{E}[\max(0, 1 + 1/\sqrt{2} - \mathcal{N}(\sqrt{\frac{d}{2}}\mu, \frac{1}{2}))],
$$

which yields  $p \geq 1 - \frac{\mathbb{E}[\max(0, 1 - \mathcal{N}(\sqrt{d}\mu, 1))]}{\mathbb{E}[\max(0, 1 - \mathcal{N}(\sqrt{d}\mu, 1))]}$  $\frac{\mathbb{E}[\max(0,1-\mathcal{N}(\sqrt{d}\mu,1))]}{\mathbb{E}[\max(0,1+1/\sqrt{2}-\mathcal{N}(\sqrt{\frac{d}{2}}\mu,\frac{1}{2}))]} \geq 1 - \frac{\mathbb{E}[\max(0,1-\mathcal{N}(4,1))]}{\mathbb{E}[\max(0,1+1/\sqrt{2}-\mathcal{N}(2\sqrt{2})))]}$  $\frac{\mathbb{E}[\max(0,1-N(4,1))]}{\mathbb{E}[\max(0,1+1/\sqrt{2}-\mathcal{N}(2\sqrt{2},\frac{1}{2}))]} > 0.975$ , which contradicts to the condition that  $p \leq 0.975$ .  $\Box$ 

### A.1. Proof of [Lemma 1](#page-4-6)

*Proof.* Part 1. We show that the optimal classifier can achieve high natural accuracy. By [Lemma 3](#page-10-2) and [Lemma 4](#page-10-3) we have  $w_2^* = ... = w_{d+1}^*$ ,  $sign(w_i^*) \ge 0, i \in [d+1]$ , and  $w_1^* \le \sqrt{d}w_2^*$ . Consider  $w^{*T}x = w_1^*x_1 + w_2^* \sum_{i=2}^{d+1} \mathcal{N}(\mu y, 1) =$   $w_2^* \mathcal{N}(\frac{w_1^*}{w_2^*}x_1 + d\mu y, d)$ , because  $\epsilon \geq 2\mu$  and  $\mu \geq \frac{4}{\sqrt{3}}$  $\frac{d}{dt}$ , the probability that x is correctly classified is

$$
\Pr(\text{sign}(\boldsymbol{w}^{*T}\boldsymbol{x}) = \text{sign}(y)) = p \Pr(\mathcal{N}(\frac{w_1^*}{w_2^*} + d\mu, d) > 0) + (1-p) \Pr(\mathcal{N}(-\frac{w_1^*}{w_2^*} + d\mu, d) > 0)
$$

$$
\geq p \Pr(\mathcal{N}(\mu d, d) > 0) + (1-p) \Pr(\mathcal{N}(\mu - \sqrt{d}, d) > 0)
$$

$$
\geq p \Pr(\mathcal{N}(4\sqrt{d}, d) > 0) + (1-p) \Pr(\mathcal{N}(3\sqrt{d}, d) > 0)
$$

$$
= p \Pr(\mathcal{N}(4, 1) > 0) + (1-p) \Pr(\mathcal{N}(3, 1) > 0)
$$

$$
\geq \Pr(\mathcal{N}(3, 1) > 0)
$$

$$
\geq 0.9986.
$$
 (10)

Thus the natural accuracy of the optimal classifier  $w^*$  is greater than 99%.

Part 2. We show that the optimal classifier achieve low robust accuracy. Firstly, according to [Lemma 2,](#page-4-2) the perturbed distribution  $x + \delta$  is given by

$$
y \sim \{-1, 1\}, \quad x_1 \sim \begin{cases} y(1 - \epsilon), & \text{with probability } p; \\ -y(1 + \epsilon), & \text{with probability } 1 - p, \end{cases} \quad x_i \sim \mathcal{N}((\mu - \epsilon)y, 1), \ i \ge 2. \tag{11}
$$

By [Lemma 3](#page-10-2) and [Lemma 4,](#page-10-3) we have  $w_2^* = ... = w_{d+1}^*$ ,  $sign(w_i^*) \ge 0, i \in [d+1]$ , and  $w_1^* \le$  $\sqrt{d}w_2^*$ . Consider  $\mathbf{w}^{*T}(\mathbf{x}+\boldsymbol{\delta})=$  $w_1^*x_1 + w_2^* \sum_{i=2}^{d+1} \mathcal{N}((\mu - \epsilon)y, 1) = w_2^* \mathcal{N}(\frac{w_1^*}{w_2^*}x_1 + d(\mu - \epsilon)y, d)$ . Because  $\epsilon \ge 2\mu$  and  $\mu \ge \frac{4}{\sqrt{3}}$  $\frac{d}{d}$ , the probability that  $x + \delta$ is correctly classified is

$$
\Pr(\text{sign}(\boldsymbol{w}^{*T}\boldsymbol{x}) = \text{sign}(y)) = p \Pr(\mathcal{N}(\frac{w_1^*}{w_2^*} + d(\mu - \epsilon), d) > 0)
$$

$$
+ (1 - p) \Pr(\mathcal{N}(-\frac{w_1^*}{w_2^*} + d(\mu - \epsilon), d) > 0)
$$

$$
\leq p \Pr(\mathcal{N}(\sqrt{d} - \mu d, d) > 0) + (1 - p) \Pr(\mathcal{N}(-\mu d, d) > 0)
$$

$$
\leq p \Pr(\mathcal{N}(-3\sqrt{d}, d) > 0) + (1 - p) \Pr(\mathcal{N}(-4\sqrt{d}, d) > 0)
$$

$$
= p \Pr(\mathcal{N}(-3, 1) > 0) + (1 - p) \Pr(\mathcal{N}(-4, 1) > 0)
$$

$$
\leq \Pr(\mathcal{N}(-3, 1) > 0)
$$

$$
\leq 0.00135. (12)
$$

Thus the robust accuracy of the optimal classifier  $w^*$  is less than 0.2%.

### A.2. Proof of [Theorem 1](#page-4-4)

*Proof.* The optimal  $\mathcal{D}^*$  should make  $1 - yw_{\mathcal{D}^*}^T x + \epsilon ||w_{\mathcal{D}^*}||_1$  and  $||w_{\mathcal{D}^*}||_2^2$  as small as possible.

$$
1 - yw_{\mathcal{D}^*}^T \mathbf{x} + \epsilon ||w_{\mathcal{D}^*}||_1 = 1 - yw_{\mathcal{D}^*}^{(1)} x_1 + \epsilon |w_{\mathcal{D}^*}^{(1)}| + \sum_{i=2}^{d+1} (\epsilon |w_{\mathcal{D}^*}^{(i)}| - yw_{\mathcal{D}^*}^{(i)} x_i), \tag{13}
$$

as  $x_i \sim \mathcal{N}(\mu y, 1), i \ge 2$ , we have  $\sum_{i=2}^{d+1} (\epsilon |w_{\mathcal{D}^*}^{(i)}| - yw_{\mathcal{D}^*}^{(i)} x_i) \sim \mathcal{N}(\sum_{i=2}^{d+1} (\epsilon |w_{\mathcal{D}^*}^{(i)}| - \mu yw_{\mathcal{D}^*}^{(i)}), \sum_{i=2}^{d+1} w_{\mathcal{D}^*}^{(i)2}).$ 

Denote by  $\mathcal{L}_{\mathcal{D}'} := \mathbb{E}_{(\bm{x},y)}[\max\{0, 1 - y\bm{w}_{\mathcal{D}'}^T\bm{x} + \epsilon ||\bm{w}_{\mathcal{D}'}||_1\}$ . Assume there exist  $w_{\mathcal{D}^*}^{(i)} \neq 0, i \geq 2$ , we will show there exists  $\mathcal{D}_0$  such that  $\bm{w}_{\mathcal{D}_0} := (w_{\mathcal{D}^*}^{(1)}, 0, ..., 0)$  and

$$
\mathcal{L}_{\mathcal{D}_0} + \lambda ||\bm{w}_{\mathcal{D}_0}||_2^2 < \mathcal{L}_{\mathcal{D}^*} + \lambda ||\bm{w}_{\mathcal{D}^*}||_2^2. \tag{14}
$$

 $\Box$ 

.

**Step 1:** We will show if  $w_{\mathcal{D}_0} := (w_{\mathcal{D}^*}^{(1)}, 0, ..., 0),$ 

$$
\mathcal{L}_{\mathcal{D}_0} + \lambda ||\boldsymbol{w}_{\mathcal{D}_0}||_2^2 < \mathcal{L}_{\mathcal{D}^*} + \lambda ||\boldsymbol{w}_{\mathcal{D}^*}||_2^2
$$

Firstly, it is easy to observe that  $\lambda ||w_{\mathcal{D}_0}||_2^2 < \lambda ||w_{\mathcal{D}^*}||_2^2$ . Then we focus on the term  $\mathcal{L}_{\mathcal{D}_0}$  and  $\mathcal{L}_{\mathcal{D}'}$ .

Denote by  $A = 1 - yw_{\mathcal{D}^*}^{(1)}x_1 + \epsilon |w_{\mathcal{D}^*}^{(1)}|$ ,  $\mu' = \sum_{i=2}^{d+1} (\epsilon |w_{\mathcal{D}^*}^{(i)}| - \mu y w_{\mathcal{D}^*}^{(i)})$ ,  $\sigma'^2 = \sum_{i=2}^{d+1} w_{\mathcal{D}^*}^{(i)2}$ , and  $z = \sum_{i=2}^{d+1} (\epsilon |w_{\mathcal{D}^*}^{(i)}| - \mu y w_{\mathcal{D}^*}^{(i)})$  $yw_{\mathcal{D}^*}^{(i)}x_i) \sim \mathcal{N}(\mu', \sigma'^2)$ . Then by [Eq. 24](#page-13-1) we have

$$
1 - yw_{\mathcal{D}^*}^T \mathbf{x} + \epsilon ||\mathbf{w}_{\mathcal{D}^*}||_1 = A + z,
$$

and thus we can simplify  $\mathcal{L}_{\mathcal{D}_0}, \mathcal{L}_{\mathcal{D}^*}$  as below:

$$
\mathcal{L}_{\mathcal{D}_0} = \mathbb{E}_{(\bm{x},y)}[\max\{0, 1 - y\bm{w}_{\mathcal{D}_0}^T\bm{x} + \epsilon ||\bm{w}_{\mathcal{D}_0}||_1\}] = \mathbb{E}_{x_1, y}[\max\{0, 1 - y\bm{w}_{\mathcal{D}^*}^{(1)}x_1 + \epsilon |w_{\mathcal{D}^*}^{(1)}|\}] = \mathbb{E}_{x_1, y}[A\mathbb{I}_{A\geq 0}] \tag{15}
$$

$$
\mathcal{L}_{\mathcal{D}^*} = \mathbb{E}_{(\boldsymbol{x},y)}[\max\{0, 1 - y\boldsymbol{w}_{\mathcal{D}^*}^T \boldsymbol{x} + \epsilon || \boldsymbol{w}_{\mathcal{D}^*} ||_1 \}] = \mathbb{E}_{\boldsymbol{x},y}[(A+z)\mathbb{I}_{A+z\geq 0}]
$$
(16)

<span id="page-12-1"></span>Consider  $\mathcal{L}_{\mathcal{D}^*} = \mathbb{E}_{\boldsymbol{x},y}[(A+z)\mathbb{I}_{A+z>0}],$ 

$$
\mathcal{L}_{\mathcal{D}^*} = \mathbb{E}_{\mathbf{x},y}[(A+z)\mathbb{I}_{A+z\geq 0}] \geq \mathbb{E}_{z,x_1,y}[(A+z)\mathbb{I}_{A+z\geq 0}\mathbb{I}_{A\geq 0}] \n= \mathbb{E}_{x_1,y}[\mathbb{I}_{A\geq 0}\mathbb{E}_z[(A+z)\mathbb{I}_{z\geq -A}]] \n= \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}\mathbb{E}_z[\mathbb{I}_{z\geq -A}] + \mathbb{I}_{A\geq 0}\mathbb{E}_z[z\mathbb{I}_{z\geq -A}]] \n= \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}] - \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}\mathbb{E}_z[\mathbb{I}_{z<-A}]] + \mathbb{E}_{x_1,y}[\mathbb{I}_{A\geq 0}\mathbb{E}_z[z\mathbb{I}_{z\geq -A}]]
$$
\n(17)

Now we consider  $\mathbb{E}_z[z\mathbb{I}_{z\geq -A}]$ , as  $z \sim \mathcal{N}(\mu', \sigma'^2)$ , we have  $\frac{z-\mu'}{\sigma'} \sim \mathcal{N}(0, 1)$  and

$$
\mathbb{E}_{z}[z\mathbb{I}_{z\geq -A}] = \mathbb{E}_{z}[z\mathbb{I}_{\mu'\geq z\geq -A}] + \mathbb{E}_{z}[z\mathbb{I}_{2\mu'+A\geq z\geq \mu'}] + \mathbb{E}_{z}[z\mathbb{I}_{z\geq 2\mu'+A}] \\ = \mathbb{E}_{s\sim \mathcal{N}(0,1)}[(\sigma' s + \mu')\mathbb{I}_{0\geq s\geq -\frac{A+\mu'}{\sigma'}}] + \mathbb{E}_{s\sim \mathcal{N}(0,1)}[(\sigma' s + \mu')\mathbb{I}_{\frac{A+\mu'}{\sigma'}\geq s\geq 0}] + \mathbb{E}_{z}[z\mathbb{I}_{z\geq 2\mu'+A}] \\ = 2\mu' \mathbb{E}_{s\sim \mathcal{N}(0,1)}[\mathbb{I}_{0\geq s\geq -\frac{A+\mu'}{\sigma'}}] + \mathbb{E}_{z}[z\mathbb{I}_{z\geq 2\mu'+A}]
$$
 (18)

since  $\epsilon > \mu$ , we have

$$
\mu' = \sum_{i=2}^{d+1} (\epsilon |w_{\mathcal{D}^*}^{(i)}| - \mu y w_{\mathcal{D}^*}^{(i)}) \ge \sum_{i=2}^{d+1} (\epsilon - \mu) |w_{\mathcal{D}^*}^{(i)}| > 0
$$

<span id="page-12-0"></span>Thus

$$
\mathbb{E}_{z}[z\mathbb{I}_{z\geq -A}] = 2\mu' \mathbb{E}_{s\sim \mathcal{N}(0,1)}[\mathbb{I}_{0\geq s\geq -\frac{A+\mu'}{\sigma'}}] + \mathbb{E}_{z}[z\mathbb{I}_{z\geq 2\mu'+A}]
$$
>  $\mathbb{E}_{z}[z\mathbb{I}_{z\geq 2\mu'+A}]$ 

>  $(2\mu' + A)\mathbb{E}_{z}[\mathbb{I}_{z\geq 2\mu'+A}]$ 

>  $A\mathbb{E}_{z}[\mathbb{I}_{z\geq 2\mu'+A}]$ 

=  $A\mathbb{E}_{z}[\mathbb{I}_{z\leq -A}]$  (19)

Plug [Eq. 19](#page-12-0) into [Eq. 17](#page-12-1) we have

$$
\mathcal{L}_{\mathcal{D}^*} = \mathbb{E}_{\mathbf{x},y}[(A+z)\mathbb{I}_{A+z\geq 0}] \geq \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}] - \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}\mathbb{E}_z[\mathbb{I}_{z<-A}]] + \mathbb{E}_{x_1,y}[\mathbb{I}_{A\geq 0}\mathbb{E}_z[z\mathbb{I}_{z\geq -A}]]
$$
  
\n
$$
> \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}] - \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}\mathbb{E}_z[\mathbb{I}_{z<-A}]] + \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}\mathbb{E}_z[\mathbb{I}_{z\leq -A}]]
$$
  
\n
$$
= \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}]
$$
  
\n
$$
= \mathcal{L}_{\mathcal{D}_0}.
$$
 (20)

**Step 2:** We will show the existence of distribution  $\mathcal{D}_0$  such that  $\mathbf{w}_{\mathcal{D}_0} := (w_{\mathcal{D}^*}^{(1)}, 0, ..., 0)$ , we can set  $x_2 = x_3 = ... =$  $x_{d+1} = 1$  in  $\mathcal{D}_0$  and  $x_1 = \frac{1}{\sqrt{2}}$  $\frac{1}{w_{\mathcal{D}^*}^{(1)}} y$  such that  $w_{\mathcal{D}_0}^{(1)}$  $\mathcal{D}_0^{(1)} = w_{\mathcal{D}^*}^{(1)}$  and  $w_{\mathcal{D}_0}^{(i)}$  $\mathcal{D}_0^{(i)}=0, i\geq 2.$ Combining Step 1 and 2 yields contradiction.

### A.3. Proof of [Theorem 2](#page-4-7)

*Proof.* According to [Theorem 1,](#page-4-4) the weight of the optimal SVM learned from  $\mathcal{D}^*$  should satisfy  $w_{\mathcal{D}^*}^{(2)} = w_{\mathcal{D}^*}^{(3)} = ...$  $w_{\mathcal{D}^*}^{(d+1)}=0$ . Thus the clean and robust accuracy are only related to the first feature of x. It is easy to see the natural accuracy is equal to the probability that  $sign(x_1) = y$ , which is p. Besides, when the perturbation budget  $\epsilon < 1$ , the adversary does not change the sign of  $x_1$ . Thus the robust accuracy is also  $p$ .  $\Box$ 

<span id="page-13-0"></span>

### A.4. Advanced Theoretical Analysis on a General Dataset

Consider dataset distribution  $(x, y) \in \mathbb{R}^{(d+1)\times 1}$  follow the distribution below:

$$
y \sim \{-1, 1\}, \quad x_1 \sim \begin{cases} y, & \text{with prob } p; \\ -y, & \text{with prob } 1 - p, \end{cases} \quad x_i \sim \mathcal{D}_i, \ i \ge 2,
$$
 (21)

where  $\mathcal{D}_i$  are symmetric distributions with mean  $\mu_i \leq 1$ .

<span id="page-13-2"></span>

### Lemma 5. *The sum of independent symmetric distributions is also symmetric.*

*Proof.* Based on the fact (which is easy to prove) that a random variable is symmetric if and only if its characteristic function is real-valued. The characteristic function of the sum of independent symmetric distributions is given by the multiplication of the characteristic function of independent symmetric distributions, which is also real-valued. Thus the sum of independent symmetric distributions is also symmetric.  $\Box$ 

Following the settings in the above section we have the lemma below

<span id="page-13-4"></span>Lemma 6. *Our minmax optimization problem is*

$$
\min_{\mathcal{D}'} \max_{||\boldsymbol{\delta}||_{\infty} \leq \epsilon} \mathbb{E}_{(\boldsymbol{x},y)}[\max\{0, 1 - y\boldsymbol{w}_{\mathcal{D}'}^T(\boldsymbol{x} + \boldsymbol{\delta})\}] = \min_{\mathcal{D}'} \mathbb{E}_{(\boldsymbol{x},y)}[\max\{0, 1 - y\boldsymbol{w}_{\mathcal{D}'}^T\boldsymbol{x} + \epsilon ||\boldsymbol{w}_{\mathcal{D}'}||_1\}]
$$
(22)

*Let*

$$
\mathcal{D}^* = \underset{\mathcal{D}'}{\operatorname{argmin}} \mathbb{E}_{(\boldsymbol{x}, y)}[\max\{0, 1 - y\boldsymbol{w}_{\mathcal{D}'}^T\boldsymbol{x} + \epsilon || \boldsymbol{w}_{\mathcal{D}'} ||_1\}], \tag{23}
$$

*if* 1 >  $\epsilon$  ≥ max<sub>i≥2</sub>  $\mu$ <sub>i</sub>, then  $\bm{w}_{\mathcal{D}^*} := (w_{\mathcal{D}^*}^{(1)}, ..., w_{\mathcal{D}^*}^{(d+1)})$  must satisfy  $w_{\mathcal{D}^*}^{(2)} = w_{\mathcal{D}^*}^{(3)} = ... = w_{\mathcal{D}^*}^{(d+1)} = 0$ *Proof.* The optimal  $\mathcal{D}^*$  should make  $1 - y\mathbf{w}_{\mathcal{D}^*}^T\mathbf{x} + \epsilon ||\mathbf{w}_{\mathcal{D}^*}||_1$  as small as possible.

<span id="page-13-1"></span>
$$
1 - yw_{\mathcal{D}^*}^T \mathbf{x} + \epsilon ||w_{\mathcal{D}^*}||_1 = 1 - yw_{\mathcal{D}^*}^{(1)}x_1 + \epsilon |w_{\mathcal{D}^*}^{(1)}| + \sum_{i=2}^{d+1} (\epsilon |w_{\mathcal{D}^*}^{(i)}| - yw_{\mathcal{D}^*}^{(i)}x_i), \tag{24}
$$

as  $x_i \sim \mathcal{N}(\mu y, 1), i \geq 2$ , we have  $\sum_{i=2}^{d+1} (\epsilon |w_{\mathcal{D}^*}^{(i)}| - yw_{\mathcal{D}^*}^{(i)} x_i) \sim \mathcal{N}(\sum_{i=2}^{d+1} (\epsilon |w_{\mathcal{D}^*}^{(i)}| - \mu yw_{\mathcal{D}^*}^{(i)}), \sum_{i=2}^{d+1} w_{\mathcal{D}^*}^{(i)2}).$ Assume there exist  $w_{\mathcal{D}^*}^{(i)} \neq 0, i \geq 2$ , we will show there exists  $\mathcal{D}_0$  such that  $w_{\mathcal{D}_0} := (w_{\mathcal{D}^*}^{(1)}, 0, ..., 0)$  and

 $\mathcal{L}_{\mathcal{D}_0} := \mathbb{E}_{(\bm{x},y)}[\max\{0, 1 - y\bm{w}_{\mathcal{D}_0}^T\bm{x} + \epsilon||\bm{w}_{\mathcal{D}_0}||_1\} < \mathbb{E}_{(\bm{x},y)}[\max\{0, 1 - y\bm{w}_{\mathcal{D}^*}^T\bm{x} + \epsilon||\bm{w}_{\mathcal{D}^*}||_1\}] =: \mathcal{L}_{\mathcal{D}^*}$ 

**Step 1:** We will show if  $w_{\mathcal{D}_0} := (w_{\mathcal{D}^*}^{(1)}, 0, ..., 0),$ 

$$
\mathcal{L}_{\mathcal{D}_0} < \mathcal{L}_{\mathcal{D}^*}.
$$

Denote by  $A=1-yw_{\mathcal{D}^*}^{(1)}x_1+\epsilon|w_{\mathcal{D}^*}^{(1)}|$ ,  $\mu'=\sum_{i=2}^{d+1}(\epsilon|w_{\mathcal{D}^*}^{(i)}|-\mu yw_{\mathcal{D}^*}^{(i)}),$  and  $z=\sum_{i=2}^{d+1}(\epsilon|w_{\mathcal{D}^*}^{(i)}|-\mu yw_{\mathcal{D}^*}^{(i)}x_i)\sim\mathcal{S}$ , by [Lemma 5](#page-13-2) we know S is symmetric with mean  $\mu'$ . Then by [Eq. 24](#page-13-1) we have

$$
1 - yw_{\mathcal{D}^*}^T \mathbf{x} + \epsilon ||\mathbf{w}_{\mathcal{D}^*}||_1 = A + z,
$$

and thus we can simplify  $\mathcal{L}_{\mathcal{D}_0}, \mathcal{L}_{\mathcal{D}^*}$  as below:

$$
\mathcal{L}_{\mathcal{D}_0} = \mathbb{E}_{(\bm{x},y)}[\max\{0, 1 - y\bm{w}_{\mathcal{D}_0}^T\bm{x} + \epsilon ||\bm{w}_{\mathcal{D}_0}||_1\}] = \mathbb{E}_{x_1, y}[\max\{0, 1 - y\bm{w}_{\mathcal{D}^*}^{(1)}x_1 + \epsilon |w_{\mathcal{D}^*}^{(1)}|\}] = \mathbb{E}_{x_1, y}[A\mathbb{I}_{A \ge 0}],\tag{25}
$$

$$
\mathcal{L}_{\mathcal{D}^*} = \mathbb{E}_{(\boldsymbol{x},y)}[\max\{0, 1 - y\boldsymbol{w}_{\mathcal{D}^*}^T \boldsymbol{x} + \epsilon ||\boldsymbol{w}_{\mathcal{D}^*}||_1\}] = \mathbb{E}_{\boldsymbol{x},y}[(A+z)\mathbb{I}_{A+z\geq 0}].
$$
 (26)

<span id="page-13-3"></span>Consider  $\mathcal{L}_{\mathcal{D}^*} = \mathbb{E}_{\boldsymbol{x},y}[(A+z)\mathbb{I}_{A+z>0}],$ 

$$
\mathcal{L}_{\mathcal{D}^*} = \mathbb{E}_{\mathbf{x},y}[(A+z)\mathbb{I}_{A+z\geq 0}] \geq \mathbb{E}_{z,x_1,y}[(A+z)\mathbb{I}_{A+z\geq 0}\mathbb{I}_{A\geq 0}]
$$

$$
= \mathbb{E}_{x_1,y}[\mathbb{I}_{A\geq 0}\mathbb{E}_z[(A+z)\mathbb{I}_{z\geq -A}]]
$$

$$
= \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}\mathbb{E}_z[\mathbb{I}_{z\geq -A}] + \mathbb{I}_{A\geq 0}\mathbb{E}_z[z\mathbb{I}_{z\geq -A}]]
$$

$$
= \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}] - \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}\mathbb{E}_z[\mathbb{I}_{z\leq -A}]] + \mathbb{E}_{x_1,y}[\mathbb{I}_{A\geq 0}\mathbb{E}_z[z\mathbb{I}_{z\geq -A}]] \hspace*{\fill} (27)
$$

Now we consider  $\mathbb{E}_z[z\mathbb{I}_{z\geq -A}]$ , as  $z \sim S$  and S is symmetric with  $\mu'$ . We have  $S - \mu'$  is symmetric with 0 and

$$
$\mathbb{E}_{z}[z\mathbb{I}_{z\geq-A}] = \mathbb{E}_{z}[z\mathbb{I}_{\mu'\geq z\geq-A}] + \mathbb{E}_{z}[z\mathbb{I}_{2\mu'+A\geq z\geq\mu'}] + \mathbb{E}_{z}[z\mathbb{I}_{z\geq 2\mu'+A}]$
$$

$$
$= \mathbb{E}_{s\sim S-\mu'}[(s+\mu')\mathbb{I}_{0\geq s\geq-A-\mu'}] + \mathbb{E}_{s\sim S-\mu'}[(s+\mu')\mathbb{I}_{A+\mu'\geq s\geq 0}] + \mathbb{E}_{z}[z\mathbb{I}_{z\geq 2\mu'+A}]$
$$

$$
$= 2\mu'\mathbb{E}_{s\sim S-\mu'} [\mathbb{I}_{0\geq s\geq-A-\mu'}] + \mathbb{E}_{z}[z\mathbb{I}_{z\geq 2\mu'+A}]$
$$

$$
$= 2\mu'\mathbb{E}_{z}[\mathbb{I}_{\mu'\geq z\geq-A}] + \mathbb{E}_{z}[z\mathbb{I}_{z\geq 2\mu'+A}].$
$$
(28)

Since  $\epsilon > \mu$ , we have

$$
\mu' = \sum_{i=2}^{d+1} (\epsilon |w_{\mathcal{D}^*}^{(i)}| - \mu y w_{\mathcal{D}^*}^{(i)}) \ge \sum_{i=2}^{d+1} (\epsilon - \mu) |w_{\mathcal{D}^*}^{(i)}| > 0.
$$

<span id="page-14-0"></span>Thus

$$
\mathbb{E}_{z}[z\mathbb{I}_{z\geq -A}] = 2\mu' \mathbb{E}_{z}[\mathbb{I}_{\mu'\geq z\geq -A}] + \mathbb{E}_{z}[z\mathbb{I}_{z\geq 2\mu'+A}]
$$

$$
> \mathbb{E}_{z}[z\mathbb{I}_{z\geq 2\mu'+A}]
$$

$$
> (2\mu' + A)\mathbb{E}_{z}[\mathbb{I}_{z\geq 2\mu'+A}]
$$

$$
> A\mathbb{E}_{z}[\mathbb{I}_{z\geq 2\mu'+A}]
$$

$$
= A\mathbb{E}_{z}[\mathbb{I}_{z\leq -A}]. \quad (29)
$$

Plugging [Eq. 29](#page-14-0) into [Eq. 27,](#page-13-3) we have

$$
\mathcal{L}_{\mathcal{D}^*} = \mathbb{E}_{\mathbf{x},y}[(A+z)\mathbb{I}_{A+z\geq 0}] \geq \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}] - \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}\mathbb{E}_z[\mathbb{I}_{z<-A}]] + \mathbb{E}_{x_1,y}[\mathbb{I}_{A\geq 0}\mathbb{E}_z[z\mathbb{I}_{z\geq -A}]]
$$
  

$$
> \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}] - \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}\mathbb{E}_z[\mathbb{I}_{z<-A}]] + \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}\mathbb{E}_z[\mathbb{I}_{z\leq -A}]]
$$
  

$$
= \mathbb{E}_{x_1,y}[A\mathbb{I}_{A\geq 0}]
$$
  

$$
= \mathcal{L}_{\mathcal{D}_0}. \quad (30)
$$

**Step 2:** We will show the existence of distribution  $\mathcal{D}_0$  such that  $\mathbf{w}_{\mathcal{D}_0} := (w_{\mathcal{D}^*}^{(1)}, 0, ..., 0)$ , we can set  $x_2 = x_3 = ... =$  $\frac{1}{w_{\mathcal{D}^*}^{(1)}} y$  such that  $w_{\mathcal{D}_0}^{(1)}$  $\mathcal{D}_0^{(1)} = w_{\mathcal{D}^*}^{(1)}$  and  $w_{\mathcal{D}_0}^{(i)}$  $x_{d+1} = 1$  in  $\mathcal{D}_0$  and  $x_1 = \frac{1}{\sqrt{2}}$  $\mathcal{D}_0^{(i)}=0, i\geq 2.$  $\Box$ 

Combining Step 1 and 2 yields contradiction.

Lemma 7. *The optimal SVM learned from*  $D^*$  *have at least* p *clean and robust accuracy (with*  $\ell_{\infty}$  *budget less than 1) on the original dataset.*

*Proof.* According to [Lemma 6,](#page-13-4) the weight of the optimal SVM learned from  $\mathcal{D}^*$  should satisfy  $w_{\mathcal{D}^*}^{(2)} = w_{\mathcal{D}^*}^{(3)} = ...$  $w_{\mathcal{D}^*}^{(d+1)}=0$ . Thus the clean and robust accuracy are only related to the first feature of x. It is easy to see the natural accuracy is equal to the probability that  $sign(x_1) = y$ , which is p. Besides, when the perturbation budget  $\epsilon < 1$ , the adversary does not change the sign of  $x_1$ . Thus the robust accuracy is also  $p$ .  $\Box$ 

# <span id="page-15-0"></span>B. Additional Experiment Results

| Robust acc / Natural acc        | 2/255 $(\ell_{\infty})$ | 0.25 $(\ell_2)$    |
|---------------------------------|-------------------------|--------------------|
| Natural dataset                 | 0.01/70.94              | 1.6/70.94          |
| Adv. data of natural classifier | 7.35/61.66              | 25.06/65.29        |
| Robust dataset (ours)           | <b>15.71/61.60</b>      | <b>34.72/62.91</b> |

Table 4. TinyImagenet results (Under AutoAttack)
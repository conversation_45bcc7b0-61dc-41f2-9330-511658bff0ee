<PERSON> has asymptotically nonnegative curvature  $\implies$ 

$$
\forall y \in K, \quad \nabla_x^2 \left( \frac{d(x, y)^2}{2} \right) \le C(K) \operatorname{Id}_{T_xM},
$$

where K is any compact subset of M. Again, at points where  $d(x, y)^2/2$ is not twice differentiable, the conclusion should be reinterpreted as

$$
x \to \frac{d(x, y)^2}{2}
$$
 is semiconcave with a modulus  $\omega(r) = C(K) \frac{r^2}{2}$ .

Examples 10.53. The previous result applies to any compact manifold, or any manifold which has been obtained from  $\mathbb{R}^n$  by modification on a compact set. But it does not apply to the hyperbolic space  $\mathbb{H}^n$ ; in fact, if y is any given point in  $\mathbb{H}^n$ , then the function  $x \to d(y, x)^2$  is not uniformly semiconcave as  $x \to \infty$ . (Take for instance the unit disk in  $\mathbb{R}^2$ , with polar coordinates  $(r, \theta)$  as a model of  $\mathbb{H}^2$ , then the distance from the origin is  $d(r, \theta) = \log((1+r)/(1-r))$ ; an explicit computation shows that the first (and only nonzero) coefficient of the matrix of the Hessian of  $d^2/2$  is  $1 + r d(r)$ , which diverges logarithmically as  $r \to 1$ .)

Remark 10.54. The exponent 2 appearing in the definition of "asymptotic nonnegative curvature" above is optimal in the sense that for any  $p < 2$  it is possible to construct manifolds satisfying  $\sigma_x \geq -C/d(x_0, x)^p$ and on which  $d(x_0, \cdot)^2$  is not uniformly semiconcave.

## Bibliographical notes

The key ideas in this chapter were first used in the case of the quadratic cost function in Euclidean space [154, 156, 722].

The existence of solutions to the Monge problem and the differentiability of c-convex functions, for strictly superlinear convex cost functions in  $\mathbb{R}^n$  (other than quadratic) was investigated by several authors, including in particular Rüschendorf [717] (formula (10.4) seems to appear there for the first time), Smith and Knott [754], Gangbo and McCann [398, 399]. In the latter reference, the authors get rid of all moment assumptions by avoiding the explicit use of Kantorovich duality. These results are reviewed in [814, Chapter 2]. Gangbo and McCann impose some assumptions of growth and superlinearity, such as the one described in Example 10.19. Ekeland [323] applies similar tools to the optimal matching problem (in which both measures are transported to another topological space).

It is interesting to notice that the structure of the optimal map can be guessed heuristically from the old-fashioned method of Lagrange multipliers; see [328, Section 2.2] where this is done for the case of the quadratic cost function.

The terminology of the twist condition comes from dynamical systems, in particular the study of certain classes of diffeomorphisms in dimension 2 called twist diffeomorphisms [66]. Moser [641] showed that under certain conditions, these diffeomorphisms can be represented as the time-1 map of a strictly convex Lagrangian system. In this setting, the twist condition can be informally recast by saying that the dynamics "twists" vertical lines (different velocities, common position) into oblique curves (different positions).

There are cases of interest where the twist condition is satisfied even though the cost function does not have a Lagrangian structure. Examples are the so-called symmetrized Bregman cost function  $c(x,y) = \langle \nabla \phi(x) - \nabla \phi(y), x - y \rangle$  where  $\phi$  is strictly convex [206], or the cost  $c(x,y) = |x-y|^2 + |f(x) - g(y)|^2$ , where f and g are convex and k-Lipschitz,  $k < 1$  [794]. For applications in meteorology, Cullen and Maroofi [268] considered cost functions of the form  $c(x,y) = [(x_1 - y_1)^2 + (x_2 - y_2)^2 + \varphi(x_3)]/y_3$  in a bounded region of  $\mathbb{R}^3$ .

Conversely, a good example of an interesting smooth cost function which does not satisfy the twist condition is provided by the restriction of the square Euclidean distance to the sphere, or to a product of convex boundaries [6, 400].

Gangbo and McCann [399] considered not only strictly convex, but also strictly concave cost functions in  $\mathbb{R}^n$  (more precisely, strictly concave functions of the distance), which are probably more realistic from an economic perspective, as explained in the introduction of their paper. The main results from [399] are briefly reviewed in [814, Section 2.4]. Further numerical and theoretical analysis for nonconvex cost functions in dimension 1 have been considered by McCann [615], Rüschendorf and Uckelmann [724, 796], and Plakhov [683]. Hsu and Sturm [484] worked on a very nice application of an optimal transport problem with a concave cost to a problem of maximal coupling of Brownian paths.

McCann [616] proved Theorem 10.41 when  $M$  is a compact Riemannian manifold and  $\mu$  is absolutely continuous. This was the first optimal transport theorem on a Riemannian manifold (save for the very particular case of the n-dimensional torus, which was treated before by Cordero-Erausquin [240]). In his paper McCann also mentioned the possibility of covering more general cost functions expressed in terms of the distance.

Later Bernard and Buffoni [105] extended McCann's results to more general Lagrangian cost functions, and imported tools and techniques from the theory of Lagrangian systems (related in particular to Mather's minimization problem). The proof of the basic result of this chapter (Theorem 10.28) is in some sense an extension of the Bernard– Buffoni theorem to its "natural generality". It is clear from the proof that the Riemannian structure plays hardly any role, so it extends for instance to Finsler geometries, as was done in the work of Ohta [657].

Before the explicit link realized by Bernard and Buffoni, several researchers, in particular Evans, Fathi and Gangbo, had become gradually aware of the strong similarities between Monge's theory on the one hand, and Mather's theory on the other. De Pascale, Gelli and Granieri [278] contributed to this story; see also [839].

Fang and Shao rewrote McCann's theorem in the formalism of Lie groups [339]. They used this reformulation as a starting point to derive theorems of unique existence of the optimal transport on the path space over a Lie group. Shao's PhD Thesis [748] contains a synthetic view on these issues, and reminders about differential calculus in Lie groups.

Feyel and Ustünel  $[358, 359, 360, 362]$  derived theorems of unique solvability of the Monge problem in the Wiener space, when the cost is the square of the Cameron–Martin distance (or rather pseudo-distance, since it takes the value  $+\infty$ ). Their tricky analysis goes via finitedimensional approximations.

Ambrosio and Rigot [33] adapted the proof of Theorem 10.41 to cover degenerate (subriemannian) situations such as the Heisenberg group, equipped with either the squared Carnot–Carathéodory metric or the squared Korányi norm. The proofs required a delicate analysis of minimizing geodesics, differentiability properties of the squared distance, and fine properties of BV functions on the Heisenberg group. Then Rigot [702] generalized these results to certain classes of groups. Further work in this area (including the absolute continuity of Wasserstein geodesics at intermediate times, the differentiability of the optimal maps, and the derivation of an equation of Monge–Ampère type) was achieved by Agrachev and Lee [3], Figalli and Juillet [366], and Figalli and Rifford [370].

Another nonsmooth generalization of Theorem 10.41 was obtained by Bertrand [114], who adapted McCann's argument to the case of an Alexandrov space with finite (Hausdorff) dimension and (sectional) curvature bounded below. His analysis makes crucial use of fine regularity results on the structure of Alexandrov spaces, derived by Perelman, Otsu and Shioya [174, 175, 665].

Remark 10.30 about the uniqueness of the potential  $\psi$  is due to Loeper [570, Appendix]; it still holds if  $\mu$  is any singular measure, provided that  $d\mu/d\text{vol} > 0$  almost everywhere (even though the transport map might not be unique then).

The use of approximate differentials as in Theorem 10.38 was initiated by Ambrosio and collaborators [30, Chapter 6], for strictly convex cost functions in  $\mathbb{R}^n$ . The adaptation to Riemannian manifolds is due to Fathi and Figalli [348], with a slightly more complicated (but slightly more general) approach than the one used in this chapter.

The tricky proof of Theorem 10.42 takes its roots in Alexandrov's uniqueness theorem for graphs of prescribed Gauss curvature [16]. (The method can be found in [53, Theorem 10.2].) McCann [613] understood that Alexandrov's strategy could be revisited to yield the uniqueness of a cyclically monotone transport in  $\mathbb{R}^n$  without the assumption of finite total cost (Corollary 10.44 in the case when  $M = \mathbb{R}^n$ ). The tricky extension to more general cost functions on Riemannian manifolds was performed later by Figalli [363]. The current proof of Theorem 10.42 is so complicated that the reader might prefer to have a look at [814, Section 2.3.3], where the core of McCann's proof is explained in simpler terms in the particular case  $c(x, y) = |x - y|^2$ .

The case when the cost function is the distance  $(c(x, y) = d(x, y))$ is not covered by Theorem 10.28, nor by any of the theorems appearing in the present chapter. This case is quite more tricky, be it in Euclidean space or on a manifold. The interested reader can consult [814, Section 2.4.6] for a brief review, as well as the research papers [20, 31, 32, 104, 190, 279, 280, 281, 354, 364, 380, 686, 765, 791]. The treatment by Bernard and Buffoni [104] is rather appealing, for its simplicity and links to dynamical system tools. An extreme case (maybe purely academic) is when the cost is the Cameron–Martin distance on the Wiener space; then usual strategies seem to fail, in the first place because of (non)measurability issues [23].

The optimal transport problem with a distance cost function is also related to the irrigation problem studied recently by various authors  $[109, 110, 111, 112, 152]$ , the Bouchitté–Buttazzo variational problem [147, 148], and other problems as well. In this connection, see also Pratelli [689].

The partial optimal transport problem, where only a fixed fraction of the mass is transferred, was studied in [192, 365]. Under adequate assumptions on the cost function, one has the following results: whenever the transferred mass is at least equal to the shared mass between the measures  $\mu$  and  $\nu$ , then (a) there is uniqueness of the partial transport map; (b) all the shared mass is at the same time both source and target; (c) the "active" region depends monotonically on the mass transferred, and is the union of the intersection of the supports and a semiconvex set.

To conclude, here are some remarks about the technical ingredients used in this chapter.

Rademacher [697] proved his theorem of almost everywhere differentiability in 1918, for Lipschitz functions of two variables; this was later generalized to an arbitrary number of variables. The simple argument presented in this section seems to be due to Christensen [233]; it can also be found, up to minor variants, in modern textbooks about real analysis such as the one by Evans and Gariepy [331, pp. 81–84]. Ambrosio showed me another simple argument which uses Lebesgue's density theorem and the identification of a Lipschitz function with a function whose distributional derivative is essentially bounded.

The book by Cannarsa and Sinestrari [199] is an excellent reference for semiconvexity and subdifferentiability in  $\mathbb{R}^n$ , as well as the links with the theory of Hamilton–Jacobi equations. It is centered on semiconcavity rather than semiconvexity (and superdifferentiability rather than subdifferentiability), but this is just a question of convention. Many regularity results in this chapter have been adapted from that source (see in particular Theorem 2.1.7 and Corollary 4.1.13 there). Also the proof of Theorem 10.48(i) is adapted from [199, Theorem 4.1.6 and Corollary 4.1.9]. The core results in this circle of ideas and tools can be traced back to a pioneering paper by Alberti, Ambrosio and Cannarsa [12]. Following Ambrosio's advice, I used the same methods to establish Theorem 10.48(ii) in the present notes.

## 284 10 Solution of the Monge problem II: Local approach

One often says that  $S \subset \mathbb{R}^n$  is d-rectifiable if it can be written as a countable union of  $C^1$  manifolds (submanifolds of  $\mathbb{R}^n$ ), apart from a set of zero  $\mathcal{H}^d$  measure. This property seems stronger, but is actually equivalent to Definition 10.47 (see [753, Lemma 11.1]). Stronger notions are obtained by changing  $C^1$  into  $C^r$  for some  $r \geq 2$ . For instance Alberti [9] shows that the  $(n-1)$ -rectifiability of the nondifferentiability set of a convex function is achieved with  $C^2$  manifolds, which is optimal.

Apart from plain subdifferentiability and Clarke subdifferentiability, other notions of differentiability for nonsmooth functions are discussed in [199], such as Dini derivatives or reachable gradients.

The theory of approximate differentiability (in Euclidean space) is developed in Federer [352, Section 3.1.8]; see also Ambrosio, Gigli and Savaré  $[30, Section 5.5]$ . A central result is the fact that any approximately differentiable function coincides, up to a set of arbitrarily small measure, with a Lipschitz function.

The proof of Besicovich's density theorem [331, p. 43] is based on Besicovich's covering lemma. This theorem is an alternative to the more classical Lebesgue density theorem (based on Vitali's covering lemma), which requires the doubling property. The price to pay for Besicovich's theorem is that it only works in  $\mathbb{R}^n$  (or a Riemannian manifold, by localization) rather than on a general metric space.

The nonsmooth implicit function theorem in the second Appendix (Theorem 10.50) seems to be folklore in nonsmooth real analysis; the core of its proof was explained to me by Fathi. Corollary 10.52 was discovered or rediscovered by McCann [613, Appendix], in the case where  $\psi$  and  $\widetilde{\psi}$  are convex functions in  $\mathbb{R}^n$ .

Everything in the Third Appendix, in particular the key differential inequality (10.46), was explained to me by Gallot. The lower bound assumption on the sectional curvatures  $\sigma_x \geq -C/d(x_0, x)^2$  is sufficient to get upper bounds on  $\nabla_x^2 d(x, y)^2$  as y stays in a compact set, but it is not sufficient to get upper bounds that are uniform in both  $x$  and  $y$ . A counterexample is developed in [393, pp. 213–214].

The exact computation about the hyperbolic space in Example 10.53 is the extremal situation for a comparison theorem about the Hessian of the squared distance [246, Lemma 3.12]: If  $M$  is a Riemannian manifold with sectional curvature bounded below by  $\kappa < 0$ , then

$$
\nabla_x^2 \left( \frac{d(x,y)^2}{2} \right) \le \frac{\sqrt{|\kappa|} d(x,y)}{\tanh(\sqrt{|\kappa|} d(x,y))}.
$$

As pointed out to me by Ghys, the problem of finding a sufficient condition for the Hessian of  $d(x, y)^2$  to be bounded above is related to the problem of whether large spheres  $S_r(y)$  centered at y look flat at infinity, in the sense that their second fundamental form is bounded like  $O(1/r)$ .

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
## Dataset Distillation Meets Provable Subset Selection

[<PERSON><PERSON>](https://scholar.google.com/citations?user=721xaz0AAAAJ&hl=en) <sup>∗</sup> <NAME_EMAIL>

[<PERSON><PERSON>](https://scholar.google.com/citations?user=6r72e-MAAAAJ&hl=en) \* CSAIL, MIT Cambridge, Massachusetts <EMAIL>

[<PERSON><PERSON><PERSON>](https://scholar.google.com/citations?user=nZEtlZoAAAAJ&hl=en) The University of Haifa Haifa, Israel <EMAIL>

### Abstract

Deep learning has grown tremendously over recent years, yielding state-of-the-art results in various fields. However, training such models requires huge amounts of data, increasing the computational time and cost. To address this, dataset distillation was proposed to compress a large training dataset into a smaller synthetic one that retains its performance – this is usually done by (1) uniformly initializing a synthetic set and (2) iteratively updating/learning this set according to a predefined loss by uniformly sampling instances from the full data. In this paper, we improve both phases of dataset distillation: (1) we present a provable, sampling-based approach for initializing the distilled set by identifying important and removing redundant points in the data, and (2) we further merge the idea of data subset selection with dataset distillation, by training the distilled set on "important" sampled points during the training procedure instead of randomly sampling the next batch. To do so, we define the notion of importance based on the relative contribution of instances with respect to two different loss functions, i.e., one for the initialization phase (a kernel fitting function for kernel ridge regression and K-means based loss function for any other distillation method), and the relative cross-entropy loss (or any other predefined loss) function for the training phase. Finally, we provide experimental results showing how our method can latch on to existing dataset distillation techniques and improve their performance.

## 1 Introduction

Deep learning has advanced at an extraordinary rate in the previous decade, becoming the first option in many fields. This advancement is attributed to constantly increasing computational resources that stimulate cutting-edge algorithms to cope with large amounts of data. However, dealing with the infinite expansion of data under a constraint of limited computing power has been increasingly difficult [\[36\]](#page-11-0), especially during the training phase, where training a single deep learning model could last for weeks or even months. To that end, subset selection [\[32,](#page-11-1) [21\]](#page-10-0) and dataset distillation [\[25,](#page-10-1) [36\]](#page-11-0) methods have been proposed to speed up the training process.

Subset selection. The idea is to select a representative subset from the entire data [\[22,](#page-10-2) [50\]](#page-12-0), before each training epoch, followed by training the model on that subset. However, these methods fall short when such a subset is computed once for the entire training procedure.

Dataset distillation. More recent approaches focused on creating a small synthetic dataset (not necessarily a subset) such that training the model on this small data will approximate the training process on the entire big data. This technique is known as dataset distillation [\[25,](#page-10-1) [52\]](#page-12-1) and it has been gaining great interest in recent years. Notably, these synthetic datasets offer the advantage of employing continuous gradient-based optimization procedures rather than geometrical and combina-

<sup>∗</sup>Equal Contribution

<span id="page-1-2"></span>Image /page/1/Figure/0 description: The image illustrates a process of distilling knowledge from a full training dataset to a distilled dataset. At the beginning of an epoch (i), a full training dataset with images and their labels (y) is presented alongside a current distilled dataset with images and their labels (ỹ). A formula is shown for calculating a score 's' for every input training data (e.g., a dog image). This score is the ratio of DistillationLoss between the current distilled set and the input training data, divided by the DistillationLoss between the full training set and the input training data. The process then iterates for k batches. In each iteration, the next batch is sampled to learn from based on the calculated score 's'. This sampled batch is then used to update the current distilled set, indicated by a blue arrow pointing from the sampled batch to the 'Update current distilled set' label and then to the 'Current distilled data' section. Another blue arrow shows the output of the distillation loss calculation feeding back into the process.

Figure 1: **Smart Learning.** At the beginning of a specific epoch  $i$ , we first compute the importance of every input data from the training data. The importance of an input data item aims to measure how well this specific example has been learned/represented using the current distilled data. We then sample the next batches according to this measure. y corresponds to the true label and  $\tilde{y}$  denotes the prediction of the model, trained on the distilled dataset; see Section [3.4](#page-6-0) for more details.

torial approaches and are not restricted to the collection of inputs and labels provided by the dataset, resulting in increased flexibility and better performance in practice.

Usually, dataset distillation methods start by **initializing at random** a synthetic dataset or by sampling uniformly a subset from the input data. Then, learning the small synthetic data is done iteratively by sampling batches **uniformly at random** from the entire data and minimizing a distillation objective, which depends on the algorithm, for example, matching gradients with respect to the data and the loss function used by the model [\[21\]](#page-10-0), using the neural tangent kernel of the model [\[35\]](#page-11-2), using a teacher/student model mechanism [\[10\]](#page-9-0), or using neural network Gaussian process techniques [\[25\]](#page-10-1). Hence, dataset distillation, irrespective of the method used, includes the initialization step and the learning step.

Then, the learning step itself can be viewed as a process of two functions: The first function squeezes the (current) information to be learned from the whole data set (in previous approaches, this corresponds to the random sampling at each iteration). The second function **injects** the extracted information into the synthetic samples (this is the learning process). Thus, most data distillation methods rely heavily on uniform sampling in both the initialization step and in learning, where squeezing is performed by sampling random batches of real data. To this end, we raise the following questions regarding the effectiveness of the uniform sampling and provide the intuition behind them:

<span id="page-1-0"></span>(1) Is uniform sampling sufficient for dataset distillation initialization? In general, initialization has a crucial role in determining convergence of the training process of deep learning model [\[15\]](#page-10-3). We argue that the initialization of distillation is as important as model initializing for training, and has a similar effect on the convergence and final solution. While each instance is equally important from the point of view of the uniform sampler, the reality of such a matter is far from this, namely, some subsets are more informative than others. Using subset selection with theoretical guarantees for initializing the distilled set offers both practical and theoretical benefits: a) the learning starts from a better solution that is closer to a robust local minimum; b) the training process can only further improve the guarantees associated with the initial subset.

<span id="page-1-1"></span>(2) Should we apply uniform sampling in the squeezing part at each iteration? As the learning process proceeds, more and more information from different data points is captured in the distilled set. Thus, the loss of the learning process with respect to these points gets lower and lower, and consequently, the gradients with respect to these points (specifically toward the end of the learning process) get lower as well. Eventually, uniform sampling creates a batch full of "small norm" gradients with very few samples affecting the gradient mean. This causes lower diversity of high gradient samples. To resolve this issue we suggest replacing uniform samples with "important" examples – those that the distillation process has not yet encapsulated. In summary, we can obtain

<span id="page-2-0"></span>Image /page/2/Figure/0 description: The image depicts a process flow for data distillation. It begins with a grid of 49 diverse images, including animals, vehicles, and landscapes. An arrow points to a circular diagram with concentric rings and scattered blue dots, representing a distribution. Another arrow leads to a similar but slightly different circular diagram. Below these diagrams, a mathematical expression is shown: "∀x ∈ R^d: ∑ K(p,x) ~ ∑ K(p,x)". Following this, a grid of 9 distinct images is displayed, featuring a horse, a white dog, a cat, a pink dog, a tabby cat, a red car, a boat, a blurred image of people, and a blurred image of a dog's face. Finally, a blue arrow points downwards from this grid with the text "Use as initialization for the data distillation algorithm".

Figure 2: Initialization: Given an input dataset, e.g., CIFAR10, treat the data as a set P of n points in  $\mathbb{R}^d$ , then, apply a coreset construction algorithm (Algorithm [1\)](#page-6-1) on P to obtain the coreset S satisfying Theorem [3.](#page-5-0) Use corresponding input data (of  $S$ ) as an initialization for the distillation algorithm. Note that the kernel  $K$  we wish to estimate here is the corresponding kernel of the desired neural network we wish to distill data with.

better distillation results by sampling a meaningful subset of points at each iteration and learning the synthetic data with respect to them.

#### 2 Key ideas

Following the motivation described in the previous section, we introduce the two main ideas.

#### 2.1 Provable initialization

In this work, we distinguish between two main panels of distillation techniques: neural tangent kernel (NTK) based methods (e.g., [\[35\]](#page-11-2)), and neural network-related loss minimization techniques (e.g., [\[55\]](#page-12-2)). For each of the above categories of distillation techniques, we provide a robust initializer with provable theoretical guarantees.

Initialization for NTK-based distillation methods. Recent work [\[24,](#page-10-4) [17\]](#page-10-5) has shown that the learning procedure of neural networks replicates the behavior of Gaussian kernel as the width of the neural network tends to infinity. In other words, one can regard the neural network as a Gaussian process. This eases the inference and analysis of the learning process and it can be used for dataset distillation [\[35\]](#page-11-2). For formal recitation, the following is given.

<span id="page-2-1"></span>Claim 1 (ANNs represent Gaussian kernels [\[17\]](#page-10-5)). *In the infinite-width limit, artificial neural networks have a Gaussian distribution described by a kernel.*

In other words, every neural network tends to behave like a Gaussian process from the perspective of learning – the wider the width of the neural network, the more similar the network is to a Gaussian process. This means that the corresponding kernel (referred to in the literature by neural tangent kernel [\[17\]](#page-10-5)) of the neural network (describes the learning process) describes a Gaussian distribution; for full details on how to compute this kernel, we refer the reader to [\[17\]](#page-10-5). With this in mind, Question [\(1\)](#page-1-0) for NTK-based methods can be restated as follows, "*Can we find a subset of the data such that fitting the infinite-width neural network corresponding kernel on this subset will approximate fitting the same kernel on the entire data?*". Note that such a subset is attainable (as we will see later) for the case of infinite-width models, where the associated kernel is Gaussian in nature [\[17\]](#page-10-5). In addition, using such a set in dataset distillation is beneficial, as the subset encapsulates the capabilities and caveats of the learning process of the model on the entire data.

Initialization for neural network-related loss minimization distillation technique (*NNLMDT*). Recently [\[11\]](#page-9-1) questioned the effect of initialization of the dataset distillation techniques [\[57,](#page-12-3) [55,](#page-12-2) [56\]](#page-12-4), and observed that using K-center initialization [\[42\]](#page-11-3) of the synthetic set improves the accuracy of the learned model trained on the distilled data. With this in mind, Question [\(1\)](#page-1-0) in the case of *NNLMDTs* can be restated as follows, "*Can we improve upon* K*-center initialization in the case of NNLMDT?*".

##### 2.2 Data distillation meets subset selection

For small sizes of distilled datasets, the performance of data distillation techniques is far superior to that of subset selection techniques [\[54\]](#page-12-5). On the other hand, subset selection techniques identify the relevant subset of the training data by either finding "hard" examples with respect to the model's success in classification [\[38\]](#page-11-4) or by selecting a subset such that the model's gradient with respect to the entire data is approximated by the model's gradient with respect to the subset [\[21,](#page-10-0) [50\]](#page-12-0), etc. Our second idea is to take the best from both worlds by merging both methodologies.

We propose to replace the uniform sampling used in the distillation methods (e.g., [\[25\]](#page-10-1)) with randomized greedy subset selection to form batches in model training. This modification aims to draw model's attention to the harder instances (associated with the higher loss) to learn the distilled set. With this goal in mind, Question [\(2\)](#page-1-1) becomes "How to determine which instances of the data should be selected for the next update of the distilled data that yields more effective learning?"

#### 2.3 Our contribution

<span id="page-3-0"></span>In this paper, we answer the questions raised in the previous section by suggesting the following:

- (i) NTK Provable-Init: A provable data subset selection for better initialization of the NTKbased learning dataset distillation algorithms. Our algorithm guarantees that fitting any Gaussian kernel on the initially selected subset (to be learned) approximates the fitting cost of using the same kernel on the entire data; see Theorem [3,](#page-5-0) and Algorithm [1.](#page-6-1) This implies that the learning procedure of models with equivalence to Gaussian kernels will behave the same on our subset (to some degree  $\varepsilon$ ) and on the entire data. To do so, we provide the first multiplicative factor approximation coreset for kernel ridge regression. For full details see Theorem [3](#page-5-0) and Remark [12,](#page-16-0) and for an illustrative depiction of this result see Figure [2.](#page-2-0) While our application hinges upon approximating Gaussian kernels, our theoretical guarantees hold for a family of kernel functions, namely, positive-definite shift-invariant kernel functions.
- <span id="page-3-1"></span>(ii) *NNLMDT* Provable-Init: We propose the usage of K-means coresets (specifically lightweight) as an initialization technique for non-NTK-based distillation techniques. We believe that this initialization technique better encapsulates the underlying structure of the data than the K-center problem; see Section [3.2,](#page-5-1) and Figure [3](#page-8-0) at Section [4.](#page-7-0)
- <span id="page-3-2"></span>(iii) Better sampling during training: A provable data subset selection policy to replace the uniform sampling which is used for updating the distilled dataset at every iteration of the distillation process. The algorithm enjoys some provable guarantees with respect to the classification capabilities associated with the distilled dataset. Using this policy, we direct the focus of the distillation process toward instances that are not encapsulated in the scope of the distilled data; see Algorithm [2,](#page-7-1) Theorem [5](#page-7-2) and Figure [1.](#page-1-2)
- (iv) Distil-Boost: Combining [\(i\),](#page-3-0) [\(ii\),](#page-3-1) and [\(iii\),](#page-3-2) yields our proposed boosting mechanism for any sampling-based distillation techniques.
- (v) Extensive experimental study and open source code of distillation algorithms on various datasets to show how our method improves the quality of the distilled dataset.

## 3 Method

We now present our notations and definitions and restate theorems from related work to fit our context.

**Notations.** For an integer  $n > 2$ , we use  $[n]$  to denote the set  $\{1, 2, \ldots, n\}$ . A weighted set of points is a pair  $(P, w)$ , where  $P \subset \mathbb{R}^d$  and  $w: P \to [0, \infty)$  is a weight function. We use  $\mathbf{1}_n \in \mathbb{R}^n$  to denote a vector of *n* entries each equal to 1. For any set  $A \subseteq \mathbb{R}^d$ , |A| denotes the cardinality of A.

As stated in Contributions [\(i\)](#page-3-0) and [\(ii\),](#page-3-1) we provide an initialization policy towards better learning the distilled set. To suggest such initializations, we turn to coresets. A coreset is a small weighted subset  $(S, v)$  of the full dataset  $P \subset \mathbb{R}^d$  that provably approximates the given loss function f for every query in a given set of queries X. Coresets  $[1-3, 28, 4, 30, 9, 12]$  $[1-3, 28, 4, 30, 9, 12]$  $[1-3, 28, 4, 30, 9, 12]$  $[1-3, 28, 4, 30, 9, 12]$  $[1-3, 28, 4, 30, 9, 12]$  $[1-3, 28, 4, 30, 9, 12]$  $[1-3, 28, 4, 30, 9, 12]$  $[1-3, 28, 4, 30, 9, 12]$  $[1-3, 28, 4, 30, 9, 12]$  $[1-3, 28, 4, 30, 9, 12]$  $[1-3, 28, 4, 30, 9, 12]$  have been suggested for various machine learning problems as they were found to be advantageous in many aspects; e.g., for clustering [\[19,](#page-10-8) [29,](#page-10-9) [7,](#page-9-7) [49\]](#page-12-6), for regression and classification problems [\[46,](#page-11-5) [8,](#page-9-8) [27\]](#page-10-10), sine wave fitting and k-segmentation problems [\[41,](#page-11-6) [31\]](#page-11-7), coresets for pruning neural networks [\[48,](#page-12-7) [33,](#page-11-8) [26\]](#page-10-11), etc; see

surveys [\[18,](#page-10-12) [14\]](#page-10-13). In addition, coresets were utilized for marine application [\[45\]](#page-11-9) and in the field of Robotics[\[47\]](#page-11-10), specifically, for boosting the quality of sampling based path planners, e.g., RRT<sup>∗</sup> [\[20\]](#page-10-14). For formality, the following definition is given.

<span id="page-4-0"></span>**Definition 2** ( $\varepsilon$ -coreset). Let  $(P, w)$  be a weighted set of n points in  $\mathbb{R}^d$ , X be a (probably infinite) set *of queries,*  $\varepsilon \in (0,1)$ *, and let*  $f : P \times X \to [0,\infty)$  *be a loss function. An*  $\varepsilon$ -coreset for  $(P, w, X, f)$ *is a pair*  $(S, v)$  *where*  $S \subseteq P$ *,*  $v : S \to [0, \infty)$  *is a weight function, such that for every*  $x \in X$ *,*  $\left|1-\frac{\sum_{q\in S}v(q)f(q,x)}{\sum_{p\in P}w(p)f(p,x)}\right|$  $\sum_{p\in P} w(p) f(p,x)$  $\Big|\leq\varepsilon.$ 

##### 3.1 Smart initialization via Gaussian kernel coresets for NTK-based distillation methods

Claim [1](#page-2-1) states that as neural networks tend to be wider, their NTK (the kernel which approximates their learning behavior) tends to be Gaussian. If we were to find a subset of the data that approximates a Gaussian kernel fitting problem, this subset would help improve the distillation process. To this end, we define the kernel function  $k : \mathbb{R}^d \times \mathbb{R}^d$ , such that for every  $x, y \in \mathbb{R}^d$ ,  $k(x, y) = e^{-0.5||x-y||^2}$ , where the fitting function that our coreset aims to approximate, is the kernel density loss, i.e, for every  $x \in \mathbb{R}^d$ :  $\sum$ p∈P  $\frac{1}{n}k(p, x)$ . The motivation behind this loss function is simple as it aims to encapsulate

the probability distribution function of the data. Thus, finding a coreset for the Gaussian kernel density estimation gives a coreset that enables the preservation (to some extent) of the properties associated with the neural network tangent kernel concerning the training data. For the initialization of NTK-based methods, we aim at finding the best subset that ensures that the ratio between the kernel fitting cost of the entire data and that of the chosen subset is close to 1. Hence, we aim to compute an  $\varepsilon$ -coreset  $(S, v)$  for the tuple  $(P, w, \mathbb{R}^d, k)$ . In the context of distillation initialization, P here is the input training data (e.g., CIFAR10 [\[23\]](#page-10-15)), and for every input  $p \in P$ ,  $w(p) = 1/n$ .

**Sensitivity sampling.** To compute an  $\varepsilon$ -coreset, we utilize the sensitivity sampling framework [\[6\]](#page-9-9). In short, the sensitivity of a point  $p \in P$  is a numbers $(p) \ge \sup_{x \in X} \frac{w(p)f(p,x)}{\sum_{q \in P} w(q)f(q,x)}$ , (where the sup is over every  $x \in X$  such that the denominator is non-zero) that reflects the "importance" of this point with respect to the whole input set  $P$  and the loss function at hand. Once we bound the sensitivities for every  $p \in P$ , we can sample points from P according to their corresponding sensitivity bounds and re-weight the sampled points to obtain an  $\varepsilon$ -coreset as in Definition [2.](#page-4-0) The size of the sample (coreset) is proportional to the sum of these bounds – the tighter (smaller) these bounds, the smaller the coreset size. Formally speaking, Theorem [8](#page-13-0) is a simplified restatement of the generic Theorem 5.5 in [\[6\]](#page-9-9) which explains how to compute an  $\varepsilon$ -coreset for the input data with respect to a general loss function (query space)  $f : P \times \mathbb{R}^d \to [0, \infty)$  (in our case  $f := k$ ).

Sensitivity bounding. To generate our coresets, we first need to upper-bound the sensitivities. Recall that a kernel corresponds to an inner product between mappings of a pair of points in some feature space [\[44\]](#page-11-11). Assume for now, that such mapping z is explicitly given, i.e.,  $z : \mathbb{R}^d \to \mathbb{R}^c$ such that for any pair  $x, y \in \mathbb{R}^d$ ,  $k(x, y) = z(x)^T \overline{z(y)}$  (this assumption will be handled/removed later). Thus, the loss function we wish to approximate becomes  $\sum_{p \in P} z(p)^T z(x)$  for every  $x \in \mathbb{R}^d$ . We note that for the Gaussian kernel (and many other kernels),  $z(x)^T z(y) = |z(x)^T z(y)| \ge 0$ for any  $x, y \in \mathbb{R}^d$ . Hence to bound the sensitivity, we define  $\tilde{P} = \{z(p) | p \in P\}$  and bound  $s(p) = \sup_{x \in \mathbb{R}^d} \frac{z(p)^T z(x)}{\sum_{q \in P} z(q)^T z(x)}$  using Lemma [11](#page-14-0) in Section [C.1](#page-14-1) in the supplementary material.

On constructing z. The problem with this approach is that the feature space ( $\mathbb{R}^c$ ) is rather intangible or hard to map to, while, the sensitivity bounding tool in Lemma [11](#page-14-0) requires the representation of these points in the feature space. To resolve this problem, we use a randomized approximated feature map – the dot product in this approximated feature map aims to approximate the Gaussian kernel function. Specifically, we use Theorem [10](#page-13-1) (see Section [B](#page-13-2) in the supplementary material) which states that there exists a feature space of some dimension  $D \ll c$  such that the dot product of mapped features of two points in this space approximates the original Gaussian kernel which the model with the infinite width's kernel mimics. Hence, we first aim at computing a coreset for the dot product loss function in such spaces. This coreset will be used to compute the initial starting subset for any NTK-based sampling-based distillation techniques.

In summary, Algorithm [1](#page-6-1) and Theorem [3](#page-5-0) describe the pseudo-code and theoretical guarantees associated with our kernel fitting problem for the NTK-based methods initialization.

<span id="page-5-1"></span>

##### 3.2 Smart initialization via $K$ -means coresets for NNLMDT distillation methods

First observe that the  $K$ -center problem which inspired the use of  $K$ -center initializer [\[42\]](#page-11-3) involves the optimization problem of  $\min_{C \subset \mathbb{R}^d} \max_{p \in P} \min_{c \in C} ||p - c||_2^2$ . While, such a problem is an  $|\boldsymbol{C}|$   $\!\!=$   $\!\!K$ 

NP-hard problem, the K-center initialization is deterministic and easy to compute and is a greedy algorithm that admits a multiplicative factor of 2 to the optimal K-center problem [\[42\]](#page-11-3). We believe that while such an optimization problem encapsulates the internal structure of the data to some degree, the  $K$ -means problem can be exploited to gain higher accuracy than that of the  $K$ -center problem, and we believe that this boils down to the optimization problem that K-means involves, which is,  $\min_{C \subset \mathbb{R}^d}$  $|C|=K$ P  $\sum_{p \in P}$  min<sub>c∈C</sub>  $||p - c||_2^2$ . Thus, we propose using (lightweight) K-means coreset [\[1\]](#page-9-2), the

lightweight coreset, that aims to approximate the  $K$ -means problem that involves the input data while admitting  $(1 + \varepsilon)$ -multiplicative approximation to any given K-points determining the clustering of the input data. Formally, for a given set  $P \subset \mathbb{R}^d$ , a K-means coreset is a pair  $(S, u)$ , where  $S \subset P$ 

and  $u : S \to \mathbb{R}$  such that for a any set C of K points in  $\mathbb{R}^d$  $1 - \frac{\sum_{p \in S} \min_{c \in C} u(p) ||p - c||_2^2}{\sum_{p \in P} \min_{c \in C} ||p - c||_2^2}$  $\begin{array}{c} \hline \end{array}$  $\leq \varepsilon$ . Note

that the  $K$ -means problem differs from the  $K$ -center problem by the fact that instead of using the max operator over the distance between each point and its closest center (native to the  $K$ -center problem), the problem revolves around averaging the distance of points to their closest center. We stress that by doing so, we better encapsulate the underlying structure of the data than the  $K$ -center problem. Next, we provide our first main result: Algorithm [1](#page-6-1) which includes initialization methods for both NTK-based distillation methods and *NNLMDT*s. Theorem [3](#page-5-0) provides the theoretical guarantees for Algorithm [1.](#page-6-1)

#### 3.3 Provable initializer: Overview of Algorithm [1](#page-6-1)

Given a set P of n points in  $\mathbb{R}^d$ , a weight function  $w : P \to [0, \infty)$ , a sample size  $m > 0$ , and a kernel function k, for a sufficiently large sample size m, Algorithm [1](#page-6-1) outputs a pair  $(S, v)$  that is an  $\varepsilon$ -coreset with respect to either the kernel fitting problem involving k, or the K-means clustering problem involving P for any  $K > 1$ . Note that for *NNLMDT*s distillation techniques, the function  $k$  is set to an empty function. In the case of NTK-based: The heart of our algorithm lies in approximating the feature map function associated with the kernel function  $k$  using [\[40\]](#page-11-12). We aim to approximate the structural properties associated with such space using coresets for the  $\ell_1$ -regression problem, specifically that of [\[46\]](#page-11-5). This is done by ensuring that the dot product of the feature map is non-negative as a result of carefully choosing the approximation used by Theorem [10;](#page-13-1) we refer the reader to the proof of Theorem [3.](#page-5-0) The conversion from kernel fitting to dot product of points in the feature space requires manipulation of the input data as presented at Line [3.](#page-6-2) We then compute the  $f$ -SVD of the new input data with respect to the  $\ell_1$ -regression problem followed by bounding the sensitivity of such points (Lines [4–](#page-6-3)[8\)](#page-6-4). As for *NNLMDT*: we first compute the weighted average  $\mu$  of  $P$  as in Line [6,](#page-6-5) followed by setting the sensitivity to be the average between (i) the relative squared distance from  $\mu$  of any point  $p \in P$  with respect to the summation of squared distances from the points of P to  $\mu$  and (ii) the relative weight of a point with respect to the total weight of the points of P. We adopted this sensitivity bound from [\[1\]](#page-9-2) in order to be able to construct lightweight coresets which mainly aim towards the  $K$ -means clustering problem. **Finally,** we have all the required ingredients to use Theorem [8](#page-13-0) (see Section [A](#page-13-3) at the supplementary material) in order to obtain an  $\varepsilon$ -coreset, i.e., we sample i.i.d m points from P based on their sensitivity bounds (see Line [10\)](#page-6-6), followed by assigning a new weight for every sampled point at Line [11.](#page-6-7)

We now present the theoretical guarantees of Algorithm [1.](#page-6-1)

<span id="page-5-0"></span>**Theorem 3** (Guarantees of our smart initialization). Let  $P \subseteq \mathbb{R}^d$  be a set of n points,  $K > 0$  be an *integer,* R be the diameter of  $P^2$  $P^2$ , X be a set of queries of diameter 3R containing P, i.e.,  $P \subseteq X$ . *Let*  $k : P \times X \to (0, \infty)$  *be a Gaussian kernel such that*  $\min_{p \in P, x \in X} k(x, p) = \alpha(R)$ *, where*  $\alpha(R) > 0$ ,  $\delta \in (0, 1)$ , and let  $\varepsilon \in (0, \alpha(R)]$ . Finally, let D be defined as in Theorem [10](#page-13-1) with *plugging*  $X := X$  *and*  $k := k$  *and*  $\varepsilon := \varepsilon$  *into Theorem [10.](#page-13-1) Let*  $m \in O\left(\frac{Dd}{\varepsilon^2} \left(d \log D + \log \frac{1}{\delta}\right)\right)$  for *NTK based distillation techniques, and*  $m \in O\left(dK\left(\log(K) + \log\left(\frac{1}{\delta}\right)\right)\right)$  *for the <code>NNLMDT</code>s. Let* 

<span id="page-5-2"></span><sup>&</sup>lt;sup>2</sup> For *NNLMDT*s, R can be as large as possible since it will not be used, whereas for NTK-based, it will be present in D.

Algorithm 1: SMART-INITIALIZATION $(P, w, m, k)$ 

<span id="page-6-1"></span>**Input:** A set  $P \subseteq \mathbb{R}^d$  of n points, a weight function  $w : P \to [0, \infty)$ , a sample size  $m \ge 1$ , and a kernel function k.

**Output:** A pair  $(S, v)$  satisfying Theorem [3.](#page-5-0)

1 if  $\mathbb{1}$  (*Is-NTK-based-?*) then

<span id="page-6-2"></span>2 Construct z via Theorem [10](#page-13-1) with respect to  $k$  // See Algorithm [3](#page-14-2) at the Appendix 3 Set  $P' = \{z(p)|p \in P\}$ 

<span id="page-6-3"></span> $4 \mid \; \; \mathsf{Set}\,(U,\Sigma,V) \; \text{to be the $\ell_1$-SVD of $(P',w,|\cdot|)$}\; \text{/} \text{/} \; \; \mathsf{See} \; \; \text{\textsf{Lemma} } \; 11 \; \; \text{\textsf{at} the Appendix}$  $4 \mid \; \; \mathsf{Set}\,(U,\Sigma,V) \; \text{to be the $\ell_1$-SVD of $(P',w,|\cdot|)$}\; \text{/} \text{/} \; \; \mathsf{See} \; \; \text{\textsf{Lemma} } \; 11 \; \; \text{\textsf{at} the Appendix}$  $4 \mid \; \; \mathsf{Set}\,(U,\Sigma,V) \; \text{to be the $\ell_1$-SVD of $(P',w,|\cdot|)$}\; \text{/} \text{/} \; \; \mathsf{See} \; \; \text{\textsf{Lemma} } \; 11 \; \; \text{\textsf{at} the Appendix}$ 5 else

<span id="page-6-5"></span>6 Set  $\mu \in \mathbb{R}^d$  to be  $\sum_{p \in P} \frac{w(p)}{\sum_{q \in P} w(q)} p$ 

7 for *every*  $p \in P$  do

<span id="page-6-4"></span> $\mathbf{s}$  | Set  $s(p) :=$  $\sqrt{ }$  $\int$  $\mathcal{L}$  $w(p) \| U(z(p)) \|_1$  NTK-based distillation techniques<br>  $w(p)$   $w(p)$   $w(p)$   $w(p)$  $rac{w(p)}{2\sum\limits_{q\in P}w(q)} + \frac{w(p)\|p-\mu\|_2^2}{\sum_{q\in P}2w(q)\|q-\mu\|_2^2}$  Otherwise

 $\mathsf{s} \ \mathsf{s}$  Set  $t := \sum_{p \in P} s(p)$ 

<span id="page-6-6"></span>10 Pick an i.i.d sample S of m points from P, where each  $p \in P$  is sampled with probability  $\frac{s(p)}{t}$ .

<span id="page-6-7"></span>11 Set  $v : \mathbb{R}^d \to [0, \infty]$  to be a weight function such that for every  $q \in S$ ,  $v(q) = \frac{tw(q)}{s(q) \cdot m}$ . 12 return  $(S, v)$ 

 $(S, v)$  *be the output of a call to* SMART-INITIALIZATION( $P$ ,  $\frac{1}{n}$ **1**<sub>n</sub>,  $m$ ,  $k$ )*. Then with probability at least*  $1 - \delta$ , for NTK-based distillation techniques, it holds that for every  $x \in X$ ,

$$
\left|\sum_{q\in S} v(q)k(q,x)-\sum_{p\in P} \frac{1}{n}k(p,x)\right| \leq \varepsilon \left(1+\sum_{p\in P} \frac{1}{n}k(p,x)\right),
$$

while for NNLMDTs, let  $\mu = \sum_{p \in P} p/n$ , it holds that for every set  $C \subseteq \mathbb{R}^d$  of  $K$  points

$$
\left|\sum_{p\in P} \min_{c\in C} \|p-c\|_2^2 - \sum_{p\in S} \min_{c\in C} v(p)\|p-c\|_2^2\right| \le \frac{\varepsilon}{2} \sum_{p\in P} \min_{c\in C} \|p-c\|_2^2 + \frac{\varepsilon}{2} \sum_{p\in P} \|p-\mu\|_2^2.
$$

We refer the reader to Section [D](#page-16-1) for further extensions of this result.

<span id="page-6-0"></span>

#### 3.4 Subset selection policy for better distillation

In the learning stage, batches of real data are sampled from the full dataset to compute the distillation loss. We propose sampling subsets of the training data that are either not in the scope of the distilled dataset or not fully covered in it. This will enhance the quality of the distilled dataset. To sample the next batch wisely, we define the notion of **importance-based sampling strategy**. Let  $P \subseteq \mathbb{R}^{d}$  be the full training data, and  $y : P \to \mathcal{L}$  be it corresponding label function, P be the current distilled dataset, and its corresponding labels  $\tilde{y}$ . Let  $\phi$  be the loss function used by the dataset distillation technique for prediction during distillation. For each sample  $p$  in the training set  $P$ , we assign a probability  $\mathbb{P}(p):=$  $\phi(p,y(p),\tilde{P},\tilde{y})$  $\frac{\phi(p,y(p),P,y)}{q\in P}\phi(q,y(q),\tilde{P},\tilde{y})$  (a.k.a the importance) equal to the ratio between the current value (this value changes during the training process) of the loss function used by the distillation method on this example and the total loss – sum of losses across the whole training set. For *KIP* [\[35\]](#page-11-2), the loss function is the squared difference loss function on the predicted labels using kernel ridge regression model and their ground truth label, for *RFAD* [\[25\]](#page-10-1), an x-entropy loss function is used to measure the distance between the predicted label and true label, and finally, for *DC* [\[57\]](#page-12-3), the loss function is the cross-entropy loss; see Figure [1](#page-1-2) for illustration.

Remark 4 (Weak coreset). *Our importance sampling can also be regarded as a weak coreset where* f *would be the loss function used by the distillation technique, the query set* X *in this context contains at each moment of the training procedure of the distillation technique, the current distilled data's* *properties, e.g., the kernel matrix in* KIP *and* RFAD*, and the network in* DC*. Using the above parts, our importance sampling is parallel to generating a weak coreset that enjoys provable guarantees for a single query. For more details, see Theorem [5](#page-7-2) and its proof.*

We now present our subset selection for better distillation, referred to as Algorithm [2.](#page-7-1)

**Overview of Algorithm [2.](#page-7-1)** Algorithm [2](#page-7-1) receives as input, a set P of n points in  $\mathbb{R}^d$ , a labeling function  $y : P \to \mathcal{L}$  where  $\mathcal L$  denotes the set of unique labels, a sample size  $m > 0$ , the current distilled dataset  $\tilde{P}$  associated with its labels  $\tilde{y}$  at some iteration  $i \geq 1$  and a loss function  $\phi$  used by some data distillation technique for prediction of training instances during the distillation process. First, at Line [2,](#page-7-3) we calculate for every point  $p \in P$  the ratio between its loss and the sum of losses across all of the training data with respect to the predicted labels of the distillation process. Then at Line [3,](#page-7-4) we sample  $m$  points according to these computed ratios and assign each sampled point a weight (Line [4\)](#page-7-5). These points form a batch for updating the distilled set (via the corresponding dataset distillation algorithm). Algorithm [2](#page-7-1) guarantees a weak coreset with respect to the loss function used by the distillation method at hand. Figure [1](#page-1-2) provides an intuitive illustration for the suggested subset selection sapling policy.

| <b>Algorithm 2:</b> SMART-PICK $(P, y, m, \phi, \tilde{P}, \tilde{y})$ |  |  |
|------------------------------------------------------------------------|--|--|
|------------------------------------------------------------------------|--|--|

<span id="page-7-1"></span>**Input:** A set  $P \subseteq \mathbb{R}^d$  of n points, a label function  $y : P \to \mathcal{L}$ , a sample size  $m \ge 1$ , current distilled dataset  $\tilde{P}$  and its corresponding labels  $\tilde{y}$  and a loss function  $\phi$ used by the dataset distillation technique for prediction during distillation. **Output:** A subset  $S \subset P$  and  $v : S \to [0, \infty)$ .

```
1 for every p in P do
```

<span id="page-7-3"></span>2  $\mathbb{P}(p) := \frac{\phi(p, y(p), \tilde{P}, \tilde{y})}{\sum \phi(q, y(q), \tilde{P}, \tilde{y})}$  $\frac{\varphi(p,y(p),P,y)}{\varphi\in P}$  // Setting the sampling probability of p<br> $\frac{\sum\limits_{q\in P}\phi(q,y(q),\tilde{P},\tilde{y})}{P}$ 3 S := Sample m points from P with via importance sampling involving  $\mathbb{P}(\cdot)$ 

<span id="page-7-5"></span><span id="page-7-4"></span>4 Let  $v : S \to [0, \infty)$  such that for every  $p \in S$ ,  $v(p) = \frac{1}{\mathbb{P}(p) \cdot m}$ 

5 return  $(S, v)$ 

We now show the provable guarantees of Algorithm [2](#page-7-1) and their implications.

<span id="page-7-2"></span>**Theorem 5.** Let P be a set of n points, y be a labeling function,  $\varepsilon, \delta \in (0, 1)$ ,  $\phi$  be a loss function used by the distillation technique,  $m \in O(\log(1/\delta)/\varepsilon^2)$  be a sample size, and let  $(\tilde{P}, \tilde{y})$  be the *current distilled set (with its labels) at some iteration*  $i \geq 0$ . Let  $(S, v)$  be the output of a call to **SMART-PICK**  $(P, y, m, \phi, \tilde{P}, \tilde{y})$ *. Then, with probability*  $1 - \delta$ *,*  $\Big|$  $1 - \frac{\sum_{q \in S} v(q) \phi(q, y(q), \tilde{P}, \tilde{y})}{\sum_{q \in S} \phi(q, y(q), \tilde{P}, \tilde{y})}$  $\sum_{q\in P}\phi(q,\hat{y}(q),\tilde{P},\tilde{y})$  $\begin{array}{|c|c|} \hline \hline \hline \hline \hline \hline \hline \hline \hline \hline \hline \hline \hline \$ ≤ ε.

**The logic behind Algorithm [2.](#page-7-1)** Notice that we could have decided to make  $S$  (output of Algorithm [2\)](#page-7-1) contain only the hardest points to classify from the point of view of the distilled data. However, we wouldn't have encapsulated the classification capabilities of the model properly, as some of the easier examples can be harder for classification in the next iteration of the data distillation technique. Hence, by taking a coreset, we can (i) better encapsulate the distributional properties of the classification capabilities associated with the distilled data from a probabilistic point of view, and (ii) increase the chances of an item that is not that hard in this iteration being hard in the next iteration to be part of S.

<span id="page-7-0"></span>

## 4 Experiments

System details. Our algorithms were implemented in Python 3.9 [\[51\]](#page-12-8) using Numpy [\[16\]](#page-10-16), Scikit-Learn [\[39\]](#page-11-13), and Pytorch [\[37\]](#page-11-14). Tests were performed on NVIDIA DGX A100 servers with 8 NVIDIA A100 GPUs each, fast InfiniBand interconnect, and supporting infrastructure.

Neural network and Datasets. Throughout our experiments, we used the ConvNet network [\[43\]](#page-11-15) for both training and evaluating synthetic dataset. This network is comprised of three  $3 \times 3$  convolution layers, each followed by  $2 \times 2$  average pooling and instance normalization. The hidden embedding size is set to 128. As for the datasets, we have used MNIST [\[13\]](#page-10-17), Fashion-MNIST [\[53\]](#page-12-9), SVHN [\[34\]](#page-11-16), CIFAR10 [\[23\]](#page-10-15) and CIFAR100 [\[23\]](#page-10-15).

<span id="page-8-0"></span>Image /page/8/Figure/0 description: This image contains three plots labeled (a), (b), and (c), each showing test accuracy versus the number of training iterations. Plot (a) is for 1 image per class, plot (b) is for 10 images per class, and plot (c) is for 50 images per class. All plots compare two methods: 'k-center' (represented by blue lines with 'x' markers and shaded blue areas) and 'lightweight coreset' (represented by red lines with circle markers and shaded red areas). In plot (a), the 'lightweight coreset' generally shows higher accuracy than 'k-center', with accuracies ranging from approximately 26.5 to 27.8. In plot (b), both methods show increasing accuracy with training iterations, with 'lightweight coreset' consistently outperforming 'k-center', reaching accuracies between approximately 47.0 and 48.5. Plot (c) also shows 'lightweight coreset' performing better than 'k-center', with accuracies ranging from about 56.2 to 58.5, and both methods show a general upward trend in accuracy as training iterations increase.

Figure 3: Comparison between  $K$ -center initialization and a lightweight coreset for  $K$ -means

Kernel ridge regression In what follows, we deploy both Algorithm [1](#page-6-1) and Algorithm [2](#page-7-1) in the context of boosting the results of KRRs for RFAD [\[25\]](#page-10-1). To ensure a fair comparison, we used the same setting of [\[25\]](#page-10-1), i.e., we used the regularized Zero Component Analysis (ZCA) preprocessing. As seen from Table [1,](#page-8-1) our algorithms boosted the performance of RFAD in terms of KRR test accuracy where in this experiment, across the whole table, our algorithms ensured a higher accuracy, furthermore, in some cases our boosting methodology achieves almost a 5% accuracy improvement (see SVHN).

<span id="page-8-1"></span>Table 1: Distillation of five datasets. Bold numbers indicate the best performance in the context of KRR, while underlined numbers indicate the best performances in the context of neural network training. <sup>∗</sup> at the end of a methods name, indicates adapting our techniques with given method.

|               | NTK-based |                |                | <i>NNLMDTs</i>  |                       |                  |
|---------------|-----------|----------------|----------------|-----------------|-----------------------|------------------|
|               | Img/Cls   | <b>KIP</b>     | <b>RFAD</b>    | $RFAD^*$        | DC                    | $DC^*$           |
| <b>MNIST</b>  |           | $95.2 + 0.2$   | $96.7 + 0.2$   | $97.21 + 0.2$   | $91.7 + 0.5$          | $92.01 \pm 0.4$  |
|               | 10        | $98.4 \pm 0.0$ | $99.0 + 0.1$   | $99.14 + 0.0$   | $97.4 + 0.2$          | $97.83 \pm 0.15$ |
|               | 50        | $99.1 \pm 0.0$ | $99.1 \pm 0.0$ | $99.26 + 0.0$   | $98.8 \pm 0.1$        | $98.89 \pm 0.1$  |
| Fashion-MNIST |           | $78.9 + 0.2$   | $81.6 + 0.6$   | $83.51 + 0.4$   | $\sqrt{70.5} \pm 0.6$ | $70.43 + 0.64$   |
|               | 10        | $87.6 \pm 0.1$ | $90.0 \pm 0.1$ | $90.64 \pm 0.1$ | $82.3 \pm 0.4$        | $82.23 + 0.48$   |
|               | 50        | $90.0 + 0.1$   | $91.3 + 0.1$   | $91.7 + 0.1$    | $83.6 \pm 0.4$        | $85.65 \pm 0.25$ |
| <b>SVHN</b>   |           | $48.1 + 0.7$   | $51.4 + 1.3$   | $56.96 + 0.9$   | $31.2 + 1.4$          | $28.81 \pm 1.31$ |
|               | 10        | $75.8 + 0.1$   | $77.2 \pm 0.3$ | $78.59\pm0.2$   | $76.1 \pm 0.6$        | $77.14 \pm 0.72$ |
|               | 50        | $81.3 \pm 0.2$ | $81.8 \pm 0.2$ | $82.48 \pm 0.2$ | $82.3 \pm 0.3$        | $81.15 \pm 0.3$  |
| CIFAR10       |           | $59.1 + 0.4$   | $61.1 + 0.7$   | $64.42 + 0.4$   | $28.3 \pm 0.5$        | $28.13 + 0.54$   |
|               | 10        | $67.0 + 0.4$   | $73.1 + 0.1$   | $74.29 + 0.2$   | $44.9 + 0.5$          | $46.41 \pm 0.69$ |
|               | 50        | $71.7 \pm 0.2$ | $76.1 \pm 0.3$ | $77.01 \pm 0.2$ | $53.9 \pm 0.5$        | $53.4 \pm 0.5$   |
| CIFAR100      |           | $31.8 \pm 0.3$ | $36.0 \pm 0.4$ | $38.49 + 0.2$   | $12.8 + 0.3$          | $12.81 \pm 0.34$ |
|               | 10        | $46.0 \pm 0.2$ | $44.9 \pm 0.2$ | $45.82 \pm 0.1$ | $25.2 + 0.3$          | $26.5 \pm 0.34$  |

Neural network training. We have adopted Algorithms [1](#page-6-1) and [2](#page-7-1) to boost the performance of a distillation technique that belongs to the *NNLMDT*s family, namely (DC) [\[57\]](#page-12-3). Similarly to before, to ensure fairness across the board, we have adopted here the same settings of [\[57\]](#page-12-3) where we modified the implementation [\[57\]](#page-12-3) to include our smart initialization (Algorithm [1\)](#page-6-1) and our greedy sampling (Algorithm [2\)](#page-7-1), and we report the average results over 5 trials. In addition, in this experiment, we used lightweight coresets for our initialization technique, while  $\phi$  function was set to be the cross-entropy loss of the network that is being trained during the distillation process of the DC method. For most of the datasets, we were able to boost the performance of the DC technique using our algorithms, while in the rest, we got comparable results.

Boosting the results of DM. In this experiment, we have tested the quality of our initialization technique against the  $K$ -center initialization technique, where  $K$  denotes the number of images taken from each class. We evaluated the quality of such techniques on the DM method [\[56\]](#page-12-4). To this end, we ran the DM method 10 times, each with a different number of iterations from the set  $\{2000i\}_{i=1}^{10}$ . Figure [3](#page-8-0) shows that our initialization technique is comparable to the K-center initialization technique for the case of having  $K = 1$ , while for the case of  $K = 10$ , the advantage of choosing our initialization technique is more clear. As for the case of  $K = 50$ , our method is far superior to the K-center initialization techniques.

## 5 Conclusions and future work

This paper provides a mechanism for enhancing the quality of various distillation techniques by leveraging key observations from data subset selection into the world of dataset distillation, both by (i) providing provable initialization techniques, and (ii) introducing smarter sampling methodology to better grasp the underlying structure of the data that the distillation method aims to achieve. Our experiments confirm that the choice of sampling methodology and initial set that the distillation uses is crucial as it leads to better test accuracy using sophisticated sampling methodologies. we hope that our work will be regarded as a foundation stone in the pursuit of sophisticated sampling techniques and initialization methods for enhancing the quality of distillation methods or learning in general.

### References

- <span id="page-9-2"></span>[1] Olivier Bachem, Mario Lucic, and Andreas Krause. Scalable k-means clustering via lightweight coresets. In *Proceedings of the 24th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining*, pages 1119–1127, 2018.
- [2] Olivier Bachem, Mario Lucic, and Silvio Lattanzi. One-shot coresets: The case of k-clustering. In Amos Storkey and Fernando Perez-Cruz, editors, *Proceedings of the Twenty-First International Conference on Artificial Intelligence and Statistics*, volume 84 of *Proceedings of Machine Learning Research*, pages 784–792, Playa Blanca, Lanzarote, Canary Islands, 09–11 Apr 2018. PMLR.
- <span id="page-9-3"></span>[3] Mihai Badoiu and Kenneth L Clarkson. Optimal core-sets for balls. ˘ *Computational Geometry*, 40(1):14–22, 2008.
- <span id="page-9-4"></span>[4] Maria-Florina F Balcan, Steven Ehrlich, and Yingyu Liang. Distributed k-means and k-median clustering on general topologies. In *Advances in Neural Information Processing Systems*, pages 1995–2003, 2013.
- <span id="page-9-10"></span>[5] Salomon Bochner. Monotone funktionen, stieltjessche integrale und harmonische analyse. *Mathematische Annalen*, 108(1):378–410, 1933.
- <span id="page-9-9"></span>[6] Vladimir Braverman, Dan Feldman, Harry Lang, Adiel Statman, and Samson Zhou. New frameworks for offline and streaming coreset constructions. *arXiv preprint arXiv:1612.00889*, 2016.
- <span id="page-9-7"></span>[7] Vladimir Braverman, Shaofeng Jiang, Robert Krauthgamer, and Xuan Wu. Coresets for clustering with missing values. *Advances in Neural Information Processing Systems*, 34:17360– 17372, 2021.
- <span id="page-9-8"></span>[8] Vladimir Braverman, Shaofeng Jiang, Robert Krauthgamer, and Xuan Wu. Coresets for clustering with missing values. In M. Ranzato, A. Beygelzimer, Y. Dauphin, P.S. Liang, and J. Wortman Vaughan, editors, *Advances in Neural Information Processing Systems*, volume 34, pages 17360–17372. Curran Associates, Inc., 2021.
- <span id="page-9-5"></span>[9] Vladimir Braverman, Shaofeng H.-C. Jiang, Robert Krauthgamer, and Xuan Wu. Coresets for ordered weighted clustering. In Kamalika Chaudhuri and Ruslan Salakhutdinov, editors, *Proceedings of the 36th International Conference on Machine Learning*, volume 97 of *Proceedings of Machine Learning Research*, pages 744–753, Long Beach, California, USA, 09–15 Jun 2019. PMLR.
- <span id="page-9-0"></span>[10] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022.
- <span id="page-9-1"></span>[11] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. *arXiv preprint arXiv:2207.09639*, 2022.
- <span id="page-9-6"></span>[12] Ryan Curtain, Sungjin Im, Ben Moseley, Kirk Pruhs, and Alireza Samadian. On coresets for regularized loss minimization. *arXiv preprint arXiv:1905.10845*, 2019.

- <span id="page-10-17"></span>[13] Li Deng. The mnist database of handwritten digit images for machine learning research. *IEEE Signal Processing Magazine*, 29(6):141–142, 2012.
- <span id="page-10-13"></span>[14] Dan Feldman. Introduction to core-sets: an updated survey. *arXiv preprint arXiv:2011.09384*, 2020.
- <span id="page-10-3"></span>[15] Ian Goodfellow, Yoshua Bengio, and Aaron Courville. *Deep learning*. MIT press, 2016.
- <span id="page-10-16"></span>[16] Charles R. Harris, K. Jarrod Millman, Stéfan J. van der Walt, Ralf Gommers, Pauli Virtanen, David Cournapeau, Eric Wieser, Julian Taylor, Sebastian Berg, Nathaniel J. Smith, Robert Kern, Matti Picus, Stephan Hoyer, Marten H. van Kerkwijk, Matthew Brett, Allan Haldane, Jaime Fernández del Río, Mark Wiebe, Pearu Peterson, Pierre Gérard-Marchant, Kevin Sheppard, Tyler Reddy, Warren Weckesser, Hameer Abbasi, Christoph Gohlke, and Travis E. Oliphant. Array programming with NumPy. *Nature*, 585(7825):357–362, September 2020.
- <span id="page-10-5"></span>[17] Arthur Jacot, Franck Gabriel, and Clément Hongler. Neural tangent kernel: Convergence and generalization in neural networks. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-10-12"></span>[18] Ibrahim Jubran, Alaa Maalouf, and Dan Feldman. Introduction to coresets: Accurate coresets. *arXiv preprint arXiv:1910.08707*, 2019.
- <span id="page-10-8"></span>[19] Ibrahim Jubran, Murad Tukan, Alaa Maalouf, and Dan Feldman. Sets clustering. In *International Conference on Machine Learning*, pages 4994–5005. PMLR, 2020.
- <span id="page-10-14"></span>[20] Sertac Karaman, Matthew R. Walter, Alejandro Perez, Emilio Frazzoli, and Seth Teller. Anytime motion planning using the rrt\*. In *2011 IEEE International Conference on Robotics and Automation*, pages 1478–1483, 2011.
- <span id="page-10-0"></span>[21] Krishnateja Killamsetty, Sivasubramanian Durga, Ganesh Ramakrishnan, Abir De, and Rishabh Iyer. Grad-match: Gradient matching based data subset selection for efficient deep model training. In *International Conference on Machine Learning*, pages 5464–5474. PMLR, 2021.
- <span id="page-10-2"></span>[22] KrishnaTeja Killamsetty, Durga Sivasubramanian, Ganesh Ramakrishnan, and Rishabh K. Iyer. GLISTER: generalization based data subset selection for efficient and robust learning. In *Thirty-Fifth AAAI Conference on Artificial Intelligence, AAAI*, 2021.
- <span id="page-10-15"></span>[23] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-10-4"></span>[24] Jaehoon Lee, Yasaman Bahri, Roman Novak, Samuel S Schoenholz, Jeffrey Pennington, and Jascha Sohl-Dickstein. Deep neural networks as gaussian processes. *arXiv preprint arXiv:1711.00165*, 2017.
- <span id="page-10-1"></span>[25] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *arXiv preprint arXiv:2210.12067*, 2022.
- <span id="page-10-11"></span>[26] Alaa Maalouf, Gilad Eini, Ben Mussay, Dan Feldman, and Margarita Osadchy. A unified approach to coreset learning. *IEEE Transactions on Neural Networks and Learning Systems*, 2022.
- <span id="page-10-10"></span>[27] Alaa Maalouf, Ibrahim Jubran, and Dan Feldman. Fast and accurate least-mean-squares solvers. *Advances in Neural Information Processing Systems*, 32, 2019.
- <span id="page-10-6"></span>[28] Alaa Maalouf, Ibrahim Jubran, and Dan Feldman. Fast and accurate least-mean-squares solvers for high dimensional data. *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 44(12):9977–9994, 2022.
- <span id="page-10-9"></span>[29] Alaa Maalouf, Ibrahim Jubran, Murad Tukan, and Dan Feldman. Coresets for the average case error for finite query sets. *Sensors*, 21(19):6689, 2021.
- <span id="page-10-7"></span>[30] Alaa Maalouf, Murad Tukan, Vladimir Braverman, and Daniela Rus. Autocoreset: An automatic practical coreset construction framework. *arXiv preprint arXiv:2305.11980*, 2023.

- <span id="page-11-7"></span>[31] Alaa Maalouf, Murad Tukan, Eric Price, Daniel M Kane, and Dan Feldman. Coresets for data discretization and sine wave fitting. In *International Conference on Artificial Intelligence and Statistics*, pages 10622–10639. PMLR, 2022.
- <span id="page-11-1"></span>[32] Baharan Mirzasoleiman, Jeff A. Bilmes, and Jure Leskovec. Coresets for data-efficient training of machine learning models. In *Proceedings of the 37th International Conference on Machine Learning, ICML*, pages 6950–6960, 2020.
- <span id="page-11-8"></span>[33] Ben Mussay, Dan Feldman, Samson Zhou, Vladimir Braverman, and Margarita Osadchy. Dataindependent structured pruning of neural networks via coresets. *IEEE Transactions on Neural Networks and Learning Systems*, 33(12):7829–7841, 2021.
- <span id="page-11-16"></span>[34] Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Bo Wu, and Andrew Y Ng. Reading digits in natural images with unsupervised feature learning. 2011.
- <span id="page-11-2"></span>[35] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. *arXiv preprint arXiv:2011.00050*, 2020.
- <span id="page-11-0"></span>[36] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021.
- <span id="page-11-14"></span>[37] Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, Alban Desmaison, Andreas Kopf, Edward Yang, Zachary DeVito, Martin Raison, Alykhan Tejani, Sasank Chilamkurthy, Benoit Steiner, Lu Fang, Junjie Bai, and Soumith Chintala. Pytorch: An imperative style, high-performance deep learning library. In *Advances in Neural Information Processing Systems 32*, pages 8024–8035. Curran Associates, Inc., 2019.
- <span id="page-11-4"></span>[38] Mansheej Paul, Surya Ganguli, and Gintare Karolina Dziugaite. Deep learning on a data diet: Finding important examples early in training. *Advances in Neural Information Processing Systems*, 34:20596–20607, 2021.
- <span id="page-11-13"></span>[39] F. Pedregosa, G. Varoquaux, A. Gramfort, V. Michel, B. Thirion, O. Grisel, M. Blondel, P. Prettenhofer, R. Weiss, V. Dubourg, J. Vanderplas, A. Passos, D. Cournapeau, M. Brucher, M. Perrot, and E. Duchesnay. Scikit-learn: Machine learning in Python. *Journal of Machine Learning Research*, 12:2825–2830, 2011.
- <span id="page-11-12"></span>[40] Ali Rahimi and Benjamin Recht. Random features for large-scale kernel machines. *Advances in neural information processing systems*, 20, 2007.
- <span id="page-11-6"></span>[41] Guy Rosman, Mikhail Volkov, Dan Feldman, John W Fisher III, and Daniela Rus. Coresets for k-segmentation of streaming data. In Z. Ghahramani, M. Welling, C. Cortes, N. Lawrence, and K.Q. Weinberger, editors, *Advances in Neural Information Processing Systems*, volume 27. Curran Associates, Inc., 2014.
- <span id="page-11-3"></span>[42] Ozan Sener and Silvio Savarese. Active learning for convolutional neural networks: A core-set approach. *arXiv preprint arXiv:1708.00489*, 2017.
- <span id="page-11-15"></span>[43] Christian Szegedy, Wei Liu, Yangqing Jia, Pierre Sermanet, Scott Reed, Dragomir Anguelov, Dumitru Erhan, Vincent Vanhoucke, and Andrew Rabinovich. Going deeper with convolutions. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 1–9, 2015.
- <span id="page-11-11"></span>[44] Sergios Theodoridis and Konstantinos Koutroumbas. *Pattern recognition*. Elsevier, 2006.
- <span id="page-11-9"></span>[45] Murad Tukan, Eli Biton, and Roee Diamant. An efficient drifters deployment strategy to evaluate water current velocity fields. *CoRR*, abs/2301.04216, 2023.
- <span id="page-11-5"></span>[46] Murad Tukan, Alaa Maalouf, and Dan Feldman. Coresets for near-convex functions. *Advances in Neural Information Processing Systems*, 33:997–1009, 2020.
- <span id="page-11-10"></span>[47] Murad Tukan, Alaa Maalouf, Dan Feldman, and Roi Poranne. Obstacle aware sampling for path planning. In *2022 IEEE/RSJ International Conference on Intelligent Robots and Systems (IROS)*, pages 13676–13683, 2022.

- <span id="page-12-7"></span>[48] Murad Tukan, Loay Mualem, and Alaa Maalouf. Pruning neural networks via coresets and convex geometry: Towards no assumptions. *arXiv preprint arXiv:2209.08554*, 2022.
- <span id="page-12-6"></span>[49] Murad Tukan, Xuan Wu, Samson Zhou, Vladimir Braverman, and Dan Feldman. New coresets for projective clustering and applications. In *International Conference on Artificial Intelligence and Statistics*, pages 5391–5415. PMLR, 2022.
- <span id="page-12-0"></span>[50] Murad Tukan, Samson Zhou, Alaa Maalouf, Daniela Rus, Vladimir Braverman, and Dan Feldman. Provable data subset selection for efficient neural network training. *arXiv preprint arXiv:2303.05151*, 2023.
- <span id="page-12-8"></span>[51] Guido Van Rossum and Fred L. Drake. *Python 3 Reference Manual*. CreateSpace, Scotts Valley, CA, 2009.
- <span id="page-12-1"></span>[52] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-12-9"></span>[53] Han Xiao, Kashif Rasul, and Roland Vollgraf. Fashion-mnist: a novel image dataset for benchmarking machine learning algorithms. *arXiv preprint arXiv:1708.07747*, 2017.
- <span id="page-12-5"></span>[54] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023.
- <span id="page-12-2"></span>[55] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pages 12674–12685. PMLR, 2021.
- <span id="page-12-4"></span>[56] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023.
- <span id="page-12-3"></span>[57] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.

<span id="page-13-3"></span>

## A Coreset tools

We first define the term *query space* which will aid us in simplifying the proofs as well as the corresponding theorems.

**Definition 6** (Query space). Let P be a set of  $n \ge 1$  points in  $\mathbb{R}^d$ ,  $w : P \to [0, \infty)$  be a non-negative *weight function, and let*  $f: P \times X \to [0, \infty)$  *denote a loss function. The tuple*  $(P, w, X, f)$  *is called a query space.*

<span id="page-13-4"></span>**Definition 7** (VC-dimension [\[6\]](#page-9-9)). *For a query space*  $(P, w, X, f)$  *and*  $r \in [0, \infty)$ *, we define* 

$$
ranges(x, r) = \{ p \in P \mid w(p)f(p, x) \le r \},
$$

*for every*  $x \in X$  *and*  $r \ge 0$ *. The dimension of*  $(P, w, X, f)$  *is the size* |S| *of the largest subset*  $S \subset P$ *such that*

$$
|\{S \cap \text{ranges}(x, r) \mid x \in X, r \ge 0\}| = 2^{|S|},
$$

where  $|A|$  denotes the number of points in A for every  $A \subseteq \mathbb{R}^d$ .

<span id="page-13-0"></span>**Theorem 8** (Restatement of Theorem 5.5 in [\[6\]](#page-9-9)). Let  $(P, w)$  be a weighted set of n points in  $\mathbb{R}^d$ ,  $\epsilon, \delta \in (0, 1)$ ,  $X \subseteq \mathbb{R}^d$ , and let  $f : P \times X \to [0, \infty)$ . For every  $p \in P$  define the sensitivity of p as  $\sup_{x \in X} \frac{w(p)f(p,x)}{\sum_{q \in P} w(q)f(q,x)}$ , where the sup is over every  $x \in X$  such that the denominator is *non-zero. Let*  $s : P \to [0, 1]$  *be a function such that*  $s(p)$  *is an upper bound on the sensitivity of p. Let*  $t = \sum_{p \in P} s(p)$  and d' be the VC dimension of quadruple  $(P, w, X, f)$ ; see Definition [7.](#page-13-4) Let  $c \ge 1$ *be a sufficiently large constant, and let* S *be a random sample of*  $|S| \ge \frac{ct}{\varepsilon^2}$   $(d' \log(t) + \log(\frac{1}{\delta}))$  *i.i.d points from P, such that every*  $p \in P$  *is sampled with probability*  $s(p)/t$ *. Let*  $v(p) = \frac{w(p)t}{s(p)|S|}$  for *every*  $p \in S$ *. Then, with probability at least*  $1 - \delta$ *,*  $(S, v)$  *is an*  $\varepsilon$ *-coreset for*  $(P, w, X, f)$ *.* 

<span id="page-13-2"></span>

## B Random Fourier features

<span id="page-13-5"></span>**Theorem 9** (Bochner's theorem [\[5\]](#page-9-10)). *A complex-valued function*  $g : \mathbb{R}^d \to \mathbb{C}$  *is positive-definite if* and only if it is the Fourier Transform of a finite non-negative Borel measure  $\mu$  on  $\mathbb{R}^d$ , i.e.,

$$
g(x) = \int_{\mathbb{R}^d} \rho(\omega) e^{-ix^T \omega} d\mu(w), \quad \forall x \in \mathbb{R}^d
$$

With the above in mind, if the Gaussian kernel is adequately scaled, Theorem [9](#page-13-5) guarantees that its Fourier transforms  $\rho(\omega)$  is a proper probability distribution [\[40\]](#page-11-12). This observation gave birth to the following celebrated result of [\[40\]](#page-11-12).

We now present the main tool which is used in this paper for approximating the feature map that our Gaussian kernel aims to represent.

<span id="page-13-1"></span>Theorem 10 (Special case of Claim 1 from [\[40\]](#page-11-12)). *Let* R > 0 *be a positive real number, and let*  $X\subseteq \mathbb{R}^d$  be a set of queries with diameter R. Let  $\hat{k}:X\times X\to \mathbb{R}$  be the Gaussian kernel,  $\sigma_\rho$  be the second moment of the Fourier transform of  $k$ , and let  $\varepsilon\in(0,1)$ . Then for  $D\in\Omega\left(\frac{d}{\varepsilon^2}\log\left(\frac{\sigma_\rho R}{\varepsilon}\right)\right)$ , there exists  $z: M \to \mathbb{R}^D$  such that  $\sup_{x,y \in X} |z(x)^T z(y) - k(x,y)| \leq \varepsilon$ , with constant probability.

We now present for completeness the process needed to construct such a feature map.

**Overview of Algorithm [3.](#page-14-2)** Given a positive-definite shift-invariant kernel function k, Algorithm [3](#page-14-2) outputs a feature map that aims to approximate the feature map that the kernel function  $k$  aims to represent. To construct such a feature map, first, Fourier transform is constructed (see Line [1\)](#page-14-3), followed by drawing D samples i.i.d from  $\mathbb{R}^d$  using the Fourier transform of k. We then draw D i.i.d. samples from a uniform distribution  $[0, 2\pi]$ ; see Lines [2–](#page-14-4)[3.](#page-14-5) Finally, at Line [4,](#page-14-6) the feature map z is constructed.

Algorithm 3: RANDOM-FOURIER-FEATURES $(k)$ 

<span id="page-14-2"></span>**Input:** A positive-definite shift-invariant kernel  $k$ . **Output:** A randomized feature map  $z : \mathbb{R}^d \to \mathbb{R}^D$ satisfying Theorem [10.](#page-13-1)

<span id="page-14-3"></span>1 Compute the Fourier transform  $\rho$  of the kernel k

<span id="page-14-6"></span><span id="page-14-5"></span><span id="page-14-4"></span>2 Draw D i.i.d. samples  $\{\omega_i \in \mathbb{R}^d\}_{i=1}^D$  from  $\rho$ 3 Draw D i.i.d. samples  $\{b_i \in \mathbb{R}\}_{i=1}^D$  from the uniform distribution  $[0, 2\pi]$ 4 return  $z(x) := \sqrt{\frac{2}{D}}$  $\lceil$  $\begin{array}{|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c$  $\cos\left(\omega_1^T x + b_1\right)$  $\cos\left(\omega_2^Tx+b_2\right)$ . . .  $\cos\left(\omega_D^Tx+b_D\right)$ 1  $\begin{array}{|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c$ 

#### C Proof of our technical results

<span id="page-14-1"></span>

##### C.1 Proof of Theorem [3](#page-5-0)

First, we present the following tool which we will use to bound our sensitivities with respect to the kernel fitting problem.

<span id="page-14-0"></span>**Lemma 11** (Special case of Lemma 35, [\[46\]](#page-11-5)). Let  $(\tilde{P}, w)$  be a weighted set of points in  $\mathbb{R}^c$ . There *exists a diagonal matrix of*  $\Sigma \in [0,\infty)^{c \times c}$  *of full rank and an orthogonal matrix*  $V \in \mathbb{R}^{c \times c}$  such *that every*  $x \in \mathbb{R}^c$ ,  $\left\| \Sigma V^T x \right\|_2 \leq \sum_i$  $p{\in}\tilde{P}$  $\mathbb{E}[w(p) | p^T x] \leq \sqrt{c} \| \Sigma V^T x \|_2$ . Define  $U : \tilde{P} \to \mathbb{R}^c$  such that

$$
U(p) = p \left(\Sigma V^T\right)^{-1} \text{ for every } p \in \tilde{P}. \text{ Then, (i) for every } p \in \tilde{P}, \sup_{x \in \mathbb{R}^c} \frac{w(p)|p^T x|}{\sum_{p \in \tilde{P}} w(q)|q^T x|} \le ||U(p)||_1,
$$

$$
(ii) \text{ and } \sum_{p \in \tilde{P}} \sup_{x \in \mathbb{R}^c} \frac{w(p)|p^T x|}{\sum_{p \in \tilde{P}} w(q)|q^T x|} \le c^{1.5}. \text{ The tuple } (U, \Sigma, V) \text{ is the } \ell_1\text{-SVD of } \tilde{P} \text{ [46].}
$$

With the above in mind, we proceed to prove Theorem [3.](#page-5-0)

**Theorem 3** (Guarantees of our smart initialization). Let  $P \subseteq \mathbb{R}^d$  be a set of n points,  $K > 0$  be an *integer,* R be the diameter of  $P^3$  $P^3$ , X be a set of queries of diameter 3R containing P, i.e.,  $P \subseteq X$ . Let  $k: P \times X \to (0, \infty)$  be a Gaussian kernel such that  $\min_{p \in P, x \in X} k(x, p) = \alpha(R)$ , where  $\alpha(R) > 0, \delta \in (0,1)$ , and let  $\varepsilon \in (0, \alpha(R)]$ . Finally, let D be defined as in Theorem [10](#page-13-1) with *plugging*  $X := X$  *and*  $k := k$  *and*  $\varepsilon := \varepsilon$  *into Theorem [10.](#page-13-1) Let*  $m \in O\left(\frac{Dd}{\varepsilon^2} \left(d \log D + \log \frac{1}{\delta}\right)\right)$  for *NTK based distillation techniques, and*  $m \in O\left(dK\left(\log(K) + \log\left(\frac{1}{\delta}\right)\right)\right)$  *for the <code>NNLMDT</code>s. Let*  $(S, v)$  *be the output of a call to* SMART-INITIALIZATION( $P$ ,  $\frac{1}{n}$ **1**<sub>n</sub>,  $m$ ,  $k$ )*. Then with probability at least*  $1 - \delta$ *, for NTK-based distillation techniques, it holds that for every*  $x \in X$ *,* 

$$
\left|\sum_{q\in S} v(q)k(q,x)-\sum_{p\in P} \frac{1}{n}k(p,x)\right| \leq \varepsilon \left(1+\sum_{p\in P} \frac{1}{n}k(p,x)\right),
$$

 $\mathbf{L}$ 

while for NNLMDTs, let  $\mu = \sum_{p \in P} p/n$ , it holds that for every set  $C \subseteq \mathbb{R}^d$  of  $K$  points

$$
\left|\sum_{p\in P} \min_{c\in C} \|p-c\|_2^2 - \sum_{p\in S} \min_{c\in C} v(p)\|p-c\|_2^2\right| \le \frac{\varepsilon}{2} \sum_{p\in P} \min_{c\in C} \|p-c\|_2^2 + \frac{\varepsilon}{2} \sum_{p\in P} \|p-\mu\|_2^2.
$$

*Proof.* **Handling NTK-based distillation techniques.** First, let  $z : X \to \mathbb{R}^D$  be the result of plugging  $X := X$ ,  $k := k$  and  $\varepsilon := \varepsilon$  into Theorem [10.](#page-13-1) Observe that for every  $x, y \in X$ ,

$$
k(x, y) - \varepsilon \le z(x)^T z(y) \le k(x, y) + \varepsilon,
$$

<span id="page-14-7"></span><sup>3</sup> For *NNLMDT*s, R can be as large as possible since it will not be used, whereas for NTK-based, it will be present in D.

where both inequalities holds by Theorem [10.](#page-13-1)

Thus, it holds that for every  $x \in X$ 

<span id="page-15-0"></span>
$$
\frac{1}{n}\sum_{p\in P}k(p,x)-\varepsilon \leq \frac{1}{n}\sum_{p\in P}z(p)^T z(x) \leq \frac{1}{n}\sum_{p\in P}k(p,x)+\varepsilon,
$$
\n(1)

where the inequalities follows since  $P \subseteq X$ , and  $\Sigma$ p∈P  $\frac{\varepsilon}{n} = \varepsilon.$ 

Notice that for every  $x, y \in X$ ,  $z(x)^T z(y) \ge 0$  holds only for  $\varepsilon \le \alpha(R)$ . With this in mind, using Lemma [11](#page-14-0) results in

$$
s(z(p)) = \sup_{x \in X} \frac{z(p)^T z(x)}{\sum_{q \in P} z(q)^T z(x)} \leq ||U(z(p))||_1.
$$

By plugging the bound on sensitivities,  $\varepsilon := \varepsilon$ , and  $\delta := \delta$  into Theorem [8,](#page-13-0) we obtain a subset  $S \subseteq P$ and a weight function  $v : S \to [0, \infty)$  such that for every  $x \in X$ ,

$$
(1 - \varepsilon) \frac{1}{n} \sum_{p \in P} z(p)^T z(x) \le \sum_{p \in S} v(p) z(p)^T z(x)
$$
  
 
$$
\le (1 + \varepsilon) \frac{1}{n} \sum_{p \in P} z(p)^T z(x).
$$
 (2)

<span id="page-15-1"></span>By combining [\(1\)](#page-15-0) and [\(2\)](#page-15-1), we obtain that

$$
(1 - \varepsilon) \frac{1}{n} k(p, x) - 2\varepsilon \le \sum_{q \in S} v(q) z(q)^T z(x),
$$

and

$$
\sum_{q \in S} v(q)z(q)^T z(x) \le (1+\varepsilon) \frac{1}{n} \sum_{p \in P} k(p,x) + 2\varepsilon
$$

The theorem for NTK-based distillation techniques then follows by combining the above two inequalities with the assumption that  $\varepsilon \in (0, \alpha(R))$ .

Handling *NNLMDT*s. The coreset guarantees hold by [\[1\]](#page-9-2); see Theorem 2 of [1].

 $\Box$ 

###### C.2 Proof of Theorem [5](#page-7-2)

**Theorem 5.** Let P be a set of *n* points, y be a labeling function,  $\varepsilon, \delta \in (0, 1)$ ,  $\phi$  be a loss function used by the distillation technique,  $m \in O(\log(1/\delta)/\varepsilon^2)$  be a sample size, and let  $(\tilde{P}, \tilde{y})$  be the *current distilled set (with its labels) at some iteration*  $i \geq 0$ . Let  $(S, v)$  be the output of a call to **SMART-PICK**  $(P, y, m, \phi, \tilde{P}, \tilde{y})$ *. Then, with probability*  $1 - \delta$ *,*  $\Big|$  $1 - \frac{\sum_{q \in S} v(q) \phi(q, y(q), \tilde{P}, \tilde{y})}{\sum_{q \in S} \phi(q, y(q), \tilde{P}, \tilde{y})}$  $\sum_{q \in P} \phi(q, y(q), \tilde{P}, \tilde{y})$  $\begin{array}{|c|c|} \hline \hline \hline \hline \hline \hline \hline \hline \hline \hline \hline \hline \hline \$ ≤ ε.

*Proof.* The sensitivity of each item in P with respect to some distillation loss at some iteration of the distillation procedure is heavily correlated with the accuracy of prediction. The model accuracy is directly influenced by the distilled dataset that is used to update the model at the current iteration. So in a way, the optimization involved in the definition of the sensitivity (see Theorem [8\)](#page-13-0) is easily solvable since X contains a single element related to the chosen distillation technique.

Hence, setting the sensitivity of any instance of p becomes  $\frac{\phi(p,y(p), \tilde{P}, \tilde{y})}{\sum\limits_{q \in P} \phi(q,y(q), \tilde{P}, \tilde{y})}$ . Note that this function

is also used in Line [2](#page-7-3) of Algorithm [2.](#page-7-1)

With the above sensitivities, we can compute a weak coreset using Theorem [8,](#page-13-0) i.e., the coreset's provable guarantees only hold with respect to the distillation loss at the specific time (iteration). Observe that since we have only one query which is the current state of the model (that is being used in the distillation technique), then the total sensitivity is 1, where also the VC dimension is also 1 (see Definition [7\)](#page-13-4).

Thus by Theorem [8,](#page-13-0) it suffices to sample only  $O\left(\frac{1}{\varepsilon^2} \left(\log \frac{1}{\delta}\right)\right)$ , and with this, the proof of Theorem [5](#page-7-2) is concluded.

 $\Box$ 

<span id="page-16-1"></span>

## D Extensions

From additive to multiplicative approximation. We show that by tuning  $\varepsilon$  in Theorem [10,](#page-13-1) our coreset can be associated with only a multiplicative-factor approximation.

<span id="page-16-0"></span>Remark 12. *Notice that Theorem [3](#page-5-0) holds when the approximation error used to produce the random feature map is upper bounded by the lowest value the kernel function* k *can get over the Cartesian product of* P *and* X*. This indicates that our coreset size is inversely proportional to the square of such a term. However, such a term is usually pessimistic as our results indicate that using such coreset with small coreset sizes, yields more informative distilled datasets. In addition, for* ε *satisfying the above, our coreset gives a multiplicative approximation.*

Shift-invariant kernels – Supporting a larger family of kernels. Our theory holds for a large family of kernels including the Gaussian kernel, Laplacian kernel, and the Cauchy kernel as shown in the following table.

| Table 2. Example of shift-invariant kern |                                            |
|------------------------------------------|--------------------------------------------|
| Kernel name                              | k(x, y)                                    |
| Gaussian                                 | $e^{-\ x-y\ _2^2}$                         |
| Laplacian                                | $e^{-\ x-y\ _1}$                           |
| Cauchy                                   | $\prod_{i=1}^d \frac{2}{1+ e_i^T(x-y) ^2}$ |

Table 2: Example of shift-invariant kernels

#### E Limitations

**Bound on**  $\varepsilon$  **– Theorem [3.](#page-5-0)** Theorem [3](#page-5-0) states that for certain values of  $\varepsilon$ , the provable guarantees of the coreset holds. This is indeed essential for ensuring that the kernel fitting problem can be reformulated as a  $\ell_1$ -regression problem. Without such an assumption, it is required to split the data P into two sets which are defined by the sign of  $z(p)^T z(x)$ , i.e.,  $P_+(x) = \{p \mid |p \in \tilde{P}, z(p)^T z(x) \ge 0\},$ and  $P_-(x) = P \setminus P_+(x)$ . The problem with such an approach is the fact that for every  $x \in X$ , the content of these sets would change. Consequently, without our assumption, a bound on the sensitivity becomes as hard as solving the optimization problem that the definition of the sensitivity of each point  $p \in P$  entails.

The bound with respect to the diameter of  $X$ . While such an assumption seems to limit, it is essential for the existence of a coreset for the kernel fitting problem regardless of the sensitivity bounding technique. Without such an assumption, the coreset size would be bounded from below by  $\Omega n$  using a similar derivation to that of Theorem 3.1 [\[50\]](#page-12-0).
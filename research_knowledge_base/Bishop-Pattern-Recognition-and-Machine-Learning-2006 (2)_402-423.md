Image /page/0/Figure/1 description: The image depicts a process flow. On the left, a blue cloud shape contains the text "p(x)". An arrow points from this cloud to a blue rectangle. Inside the rectangle is a directed acyclic graph (DAG) with five nodes. Four nodes are represented by red circles with white interiors, and one node is a red circle with a blue interior. Red arrows indicate the direction of the relationships between the nodes. An arrow points from the blue rectangle to a blue cloud shape on the right, which contains the text "DF".

**Figure 8.25** We can view a graphical model (in this case a directed graph) as a filter in which a probability distribution  $p(x)$  is allowed through the filter if, and only if, it satisfies the directed factorization property (8.5). The set of all possible probability distributions  $p(x)$  that pass through the filter is denoted  $\mathcal{DF}$ . We can alternatively use the graph to filter distributions according to whether they respect all of the conditional independencies implied by the d-separation properties of the graph. The d-separation theorem says that it is the same set of distributions  $D\mathcal{F}$  that will be allowed through this second kind of filter.

tions  $p(x)$ . At the other extreme, we have the fully disconnected graph, i.e., one having no links at all. This corresponds to joint distributions which factorize into the product of the marginal distributions over the variables comprising the nodes of the graph.

Note that for any given graph, the set of distributions  $D\mathcal{F}$  will include any distributions that have additional independence properties beyond those described by the graph. For instance, a fully factorized distribution will always be passed through the filter implied by any graph over the corresponding set of variables.

We end our discussion of conditional independence properties by exploring the concept of a *Markov blanket* or *Markov boundary*. Consider a joint distribution  $p(\mathbf{x}_1,..., \mathbf{x}_D)$  represented by a directed graph having D nodes, and consider the conditional distribution of a particular node with variables  $x_i$  conditioned on all of the remaining variables  $\mathbf{x}_{i\neq i}$ . Using the factorization property (8.5), we can express this conditional distribution in the form

$$
p(\mathbf{x}_i|\mathbf{x}_{\{j\neq i\}}) = \frac{p(\mathbf{x}_1,\dots,\mathbf{x}_D)}{\int p(\mathbf{x}_1,\dots,\mathbf{x}_D) d\mathbf{x}_i}
$$
$$
= \frac{\prod_k p(\mathbf{x}_k|pa_k)}{\int \prod_k p(\mathbf{x}_k|pa_k) d\mathbf{x}_i}
$$

in which the integral is replaced by a summation in the case of discrete variables. We now observe that any factor  $p(\mathbf{x}_k|pa_k)$  that does not have any functional dependence on  $x_i$  can be taken outside the integral over  $x_i$ , and will therefore cancel between numerator and denominator. The only factors that remain will be the conditional distribution  $p(\mathbf{x}_i|pa_i)$  for node  $\mathbf{x}_i$  itself, together with the conditional distributions for any nodes  $x_k$  such that node  $x_i$  is in the conditioning set of  $p(x_k|pa_k)$ , in other words for which  $\mathbf{x}_i$  is a parent of  $\mathbf{x}_k$ . The conditional  $p(\mathbf{x}_i|pa_i)$  will depend on the parents of node  $\mathbf{x}_i$ , whereas the conditionals  $p(\mathbf{x}_k|pa_k)$  will depend on the children

**Figure 8.26** The Markov blanket of a node  $x_i$  comprises the set of parents, children and co-parents of the node. It has the property that the conditional distribution of **x**i, conditioned on all the remaining variables in the graph, is dependent only on the variables in the  $\bigcap_{x_i} x_i$ 

of **<sup>x</sup>**<sup>i</sup> as well as on the *co-parents*, in other words variables corresponding to parents of node  $\mathbf{x}_k$  other than node  $\mathbf{x}_i$ . The set of nodes comprising the parents, the children and the co-parents is called the Markov blanket and is illustrated in Figure 8.26. We can think of the Markov blanket of a node  $x_i$  as being the minimal set of nodes that isolates  $x_i$  from the rest of the graph. Note that it is not sufficient to include only the parents and children of node  $x_i$  because the phenomenon of explaining away means that observations of the child nodes will not block paths to the co-parents. We must therefore observe the co-parent nodes also.

# **8.3. Markov Random Fields**

We have seen that directed graphical models specify a factorization of the joint distribution over a set of variables into a product of local conditional distributions. They also define a set of conditional independence properties that must be satisfied by any distribution that factorizes according to the graph. We turn now to the second major class of graphical models that are described by undirected graphs and that again specify both a factorization and a set of conditional independence relations.

A *Markov random field*, also known as a *Markov network* or an *undirected graphical model* (Kindermann and Snell, 1980), has a set of nodes each of which corresponds to a variable or group of variables, as well as a set of links each of which connects a pair of nodes. The links are undirected, that is they do not carry arrows. In the case of undirected graphs, it is convenient to begin with a discussion of conditional independence properties.

## **8.3.1 Conditional independence properties**

*Section 8.2* In the case of directed graphs, we saw that it was possible to test whether a particular conditional independence property holds by applying a graphical test called d-separation. This involved testing whether or not the paths connecting two sets of nodes were 'blocked'. The definition of blocked, however, was somewhat subtle due to the presence of paths having head-to-head nodes. We might ask whether it is possible to define an alternative graphical semantics for probability distributions such that conditional independence is determined by simple graph separation. This is indeed the case and corresponds to undirected graphical models. By removing the

Image /page/1/Picture/9 description: A diagram shows a central white circle labeled "xi" with a red outline. Two red arrows point from two blue circles at the top to the central white circle. Two red lines extend downwards from the central white circle. To the right of the central white circle, there is another blue circle with a red outline, connected by a single red line extending upwards.

**Figure 8.27** An example of an undirected graph in which every path from any node in set  $A$  to any node in set  $B$  passes through at least one node in set  $C$ . Consequently the conditional independence property  $A \perp\!\!\!\perp B \mid C$  holds for any probability distribution described by this graph.

Image /page/2/Figure/2 description: A network graph is depicted with nodes represented by circles and edges by red lines. Some nodes are filled with blue, while others are empty white circles. Three distinct groups of nodes are highlighted with dashed green ellipses, labeled A, C, and B from left to right. Group A encloses two white nodes connected by a red line, and another white node above them connected to one of the enclosed nodes. Group C encloses two blue nodes connected by a red line, and two white nodes, one above and one to the right, connected to the blue nodes. Group B encloses two white nodes connected by a red line, and another white node to the right connected to one of the enclosed nodes.

directionality from the links of the graph, the asymmetry between parent and child nodes is removed, and so the subtleties associated with head-to-head nodes no longer arise.

Suppose that in an undirected graph we identify three sets of nodes, denoted A, B, and C, and that we consider the conditional independence property

$$
A \perp \!\!\!\perp B \mid C. \tag{8.37}
$$

To test whether this property is satisfied by a probability distribution defined by a graph we consider all possible paths that connect nodes in set  $A$  to nodes in set B. If all such paths pass through one or more nodes in set  $C$ , then all such paths are 'blocked' and so the conditional independence property holds. However, if there is at least one such path that is not blocked, then the property does not necessarily hold, or more precisely there will exist at least some distributions corresponding to the graph that do not satisfy this conditional independence relation. This is illustrated with an example in Figure 8.27. Note that this is exactly the same as the d-separation criterion except that there is no 'explaining away' phenomenon. Testing for conditional independence in undirected graphs is therefore simpler than in directed graphs.

An alternative way to view the conditional independence test is to imagine removing all nodes in set  $C$  from the graph together with any links that connect to those nodes. We then ask if there exists a path that connects any node in  $A$  to any node in B. If there are no such paths, then the conditional independence property must hold.

The Markov blanket for an undirected graph takes a particularly simple form, because a node will be conditionally independent of all other nodes conditioned only on the neighbouring nodes, as illustrated in Figure 8.28.

## **8.3.2 Factorization properties**

We now seek a factorization rule for undirected graphs that will correspond to the above conditional independence test. Again, this will involve expressing the joint distribution  $p(x)$  as a product of functions defined over sets of variables that are local to the graph. We therefore need to decide what is the appropriate notion of locality in this case.

**Figure 8.28** For an undirected graph, the Markov blanket of a node  $x_i$  consists of the set of neighbouring nodes. It has the property that the conditional distribution of  $x_i$ , conditioned on all the remaining variables in the graph, is dependent only on the variables in the Markov blanket.

Image /page/3/Picture/2 description: A diagram shows a central white circle connected by red lines to four blue circles. The blue circles are arranged in a cross shape around the central white circle.

If we consider two nodes  $x_i$  and  $x_j$  that are not connected by a link, then these variables must be conditionally independent given all other nodes in the graph. This follows from the fact that there is no direct path between the two nodes, and all other paths pass through nodes that are observed, and hence those paths are blocked. This conditional independence property can be expressed as

$$
p(x_i, x_j | \mathbf{x}_{\setminus \{i,j\}}) = p(x_i | \mathbf{x}_{\setminus \{i,j\}}) p(x_j | \mathbf{x}_{\setminus \{i,j\}})
$$
(8.38)

where  $\mathbf{x}_{\setminus \{i,j\}}$  denotes the set **x** of all variables with  $x_i$  and  $x_j$  removed. The factorization of the joint distribution must therefore be such that  $x_i$  and  $x_j$  do not appear in the same factor in order for the conditional independence property to hold for all possible distributions belonging to the graph.

This leads us to consider a graphical concept called a *clique*, which is defined as a subset of the nodes in a graph such that there exists a link between all pairs of nodes in the subset. In other words, the set of nodes in a clique is fully connected. Furthermore, a *maximal clique* is a clique such that it is not possible to include any other nodes from the graph in the set without it ceasing to be a clique. These concepts are illustrated by the undirected graph over four variables shown in Figure 8.29. This graph has five cliques of two nodes given by  $\{x_1, x_2\}$ ,  $\{x_2, x_3\}$ ,  $\{x_3, x_4\}$ ,  $\{x_4, x_2\}$ , and  $\{x_1, x_3\}$ , as well as two maximal cliques given by  $\{x_1, x_2, x_3\}$  and  $\{x_2, x_3, x_4\}$ . The set  $\{x_1, x_2, x_3, x_4\}$  is not a clique because of the missing link from  $x_1$  to  $x_4$ .

We can therefore define the factors in the decomposition of the joint distribution to be functions of the variables in the cliques. In fact, we can consider functions of the maximal cliques, without loss of generality, because other cliques must be subsets of maximal cliques. Thus, if  ${x_1, x_2, x_3}$  is a maximal clique and we define an arbitrary function over this clique, then including another factor defined over a subset of these variables would be redundant.

Let us denote a clique by C and the set of variables in that clique by  $\mathbf{x}_C$ . Then

**Figure 8.29** A four-node undirected graph showing a clique (outlined in green) and a maximal clique (outlined in blue).  $\sqrt{2^{11}}$ 

Image /page/3/Figure/10 description: A diagram of a graph with four nodes arranged in a square. The nodes are connected by red lines, forming the edges of the graph. Some edges are labeled with variables: x1, x2, x3, and x4. Two of the nodes are enclosed in a green oval, and all four nodes are enclosed in a blue oval. The green oval encompasses the top two nodes and the edge between them labeled x1. The blue oval encompasses all four nodes and all the red edges, except for the edge labeled x1. The edge labeled x2 connects the top right node to the bottom left node. The edge labeled x3 connects the top left node to the bottom right node. The edge labeled x4 connects the bottom left node to the bottom right node.

the joint distribution is written as a product of *potential functions*  $\psi_C(\mathbf{x}_C)$  over the maximal cliques of the graph

$$
p(\mathbf{x}) = \frac{1}{Z} \prod_{C} \psi_C(\mathbf{x}_C).
$$
 (8.39)

Here the quantity Z, sometimes called the *partition function*, is a normalization constant and is given by

$$
Z = \sum_{\mathbf{x}} \prod_{C} \psi_C(\mathbf{x}_C) \tag{8.40}
$$

which ensures that the distribution  $p(x)$  given by (8.39) is correctly normalized. By considering only potential functions which satisfy  $\psi_C(\mathbf{x}_C) \ge 0$  we ensure that  $n(\mathbf{x}) \ge 0$ . In (8.40) we have assumed that x comprises discrete variables, but the  $p(\mathbf{x}) \geq 0$ . In (8.40) we have assumed that **x** comprises discrete variables, but the framework is equally applicable to continuous variables or a combination of the two framework is equally applicable to continuous variables, or a combination of the two, in which the summation is replaced by the appropriate combination of summation and integration.

Note that we do not restrict the choice of potential functions to those that have a specific probabilistic interpretation as marginal or conditional distributions. This is in contrast to directed graphs in which each factor represents the conditional distribution of the corresponding variable, conditioned on the state of its parents. However, in special cases, for instance where the undirected graph is constructed by starting with a directed graph, the potential functions may indeed have such an interpretation, as we shall see shortly.

One consequence of the generality of the potential functions  $\psi_C(\mathbf{x}_C)$  is that their product will in general not be correctly normalized. We therefore have to introduce an explicit normalization factor given by (8.40). Recall that for directed graphs, the joint distribution was automatically normalized as a consequence of the normalization of each of the conditional distributions in the factorization.

The presence of this normalization constant is one of the major limitations of undirected graphs. If we have a model with  $M$  discrete nodes each having  $K$  states, then the evaluation of the normalization term involves summing over  $K^M$  states and so (in the worst case) is exponential in the size of the model. The partition function is needed for parameter learning because it will be a function of any parameters that govern the potential functions  $\psi_C(\mathbf{x}_C)$ . However, for evaluation of local conditional distributions, the partition function is not needed because a conditional is the ratio of two marginals, and the partition function cancels between numerator and denominator when evaluating this ratio. Similarly, for evaluating local marginal probabilities we can work with the unnormalized joint distribution and then normalize the marginals explicitly at the end. Provided the marginals only involves a small number of variables, the evaluation of their normalization coefficient will be feasible.

So far, we have discussed the notion of conditional independence based on simple graph separation and we have proposed a factorization of the joint distribution that is intended to correspond to this conditional independence structure. However, we have not made any formal connection between conditional independence and factorization for undirected graphs. To do so we need to restrict attention to potential functions  $\psi_C(\mathbf{x}_C)$  that are strictly positive (i.e., never zero or negative for any choice of  $\mathbf{x}_C$ ). Given this restriction, we can make a precise relationship between factorization and conditional independence.

To do this we again return to the concept of a graphical model as a filter, corresponding to Figure 8.25. Consider the set of all possible distributions defined over a fixed set of variables corresponding to the nodes of a particular undirected graph. We can define  $U\mathcal{I}$  to be the set of such distributions that are consistent with the set of conditional independence statements that can be read from the graph using graph separation. Similarly, we can define  $U\mathcal{F}$  to be the set of such distributions that can be expressed as a factorization of the form (8.39) with respect to the maximal cliques of the graph. The *Hammersley-Clifford* theorem (Clifford, 1990) states that the sets  $UT$  and  $UF$  are identical.

Because we are restricted to potential functions which are strictly positive it is convenient to express them as exponentials, so that

$$
\psi_C(\mathbf{x}_C) = \exp\{-E(\mathbf{x}_C)\}\tag{8.41}
$$

where  $E(\mathbf{x}_C)$  is called an *energy function*, and the exponential representation is called the *Boltzmann distribution*. The joint distribution is defined as the product of potentials, and so the total energy is obtained by adding the energies of each of the maximal cliques.

In contrast to the factors in the joint distribution for a directed graph, the potentials in an undirected graph do not have a specific probabilistic interpretation. Although this gives greater flexibility in choosing the potential functions, because there is no normalization constraint, it does raise the question of how to motivate a choice of potential function for a particular application. This can be done by viewing the potential function as expressing which configurations of the local variables are preferred to others. Global configurations that have a relatively high probability are those that find a good balance in satisfying the (possibly conflicting) influences of the clique potentials. We turn now to a specific example to illustrate the use of undirected graphs.

## **8.3.3 Illustration: Image de-noising**

We can illustrate the application of undirected graphs using an example of noise removal from a binary image (Besag, 1974; Geman and Geman, 1984; Besag, 1986). Although a very simple example, this is typical of more sophisticated applications. Let the observed noisy image be described by an array of binary pixel values  $y_i \in$  $\{-1,+1\}$ , where the index  $i = 1,\ldots,D$  runs over all pixels. We shall suppose that the image is obtained by taking an unknown noise-free image, described by binary pixel values  $x_i \in \{-1, +1\}$  and randomly flipping the sign of pixels with some small probability. An example binary image, together with a noise corrupted image obtained by flipping the sign of the pixels with probability 10%, is shown in Figure 8.30. Given the noisy image, our goal is to recover the original noise-free image.

Because the noise level is small, we know that there will be a strong correlation between  $x_i$  and  $y_i$ . We also know that neighbouring pixels  $x_i$  and  $x_j$  in an image are strongly correlated. This prior knowledge can be captured using the Markov

Image /page/6/Picture/1 description: The image displays four panels, each containing the words "Bayes' Theorem" written in blue, handwritten-style text within a blue border on a yellow background. The top left panel shows the text clearly. The other three panels have varying degrees of blue speckle noise superimposed on the text and background, with the top right panel exhibiting the most noise, the bottom left panel showing a moderate amount of noise, and the bottom right panel also showing a moderate amount of noise, similar to the bottom left panel.

**Figure 8.30** Illustration of image de-noising using a Markov random field. The top row shows the original binary image on the left and the corrupted image after randomly changing 10% of the pixels on the right. The bottom row shows the restored images obtained using iterated conditional models (ICM) on the left and using the graph-cut algorithm on the right. ICM produces an image where 96% of the pixels agree with the original image, whereas the corresponding number for graph-cut is 99%.

random field model whose undirected graph is shown in Figure 8.31. This graph has two types of cliques, each of which contains two variables. The cliques of the form  ${x_i, y_i}$  have an associated energy function that expresses the correlation between these variables. We choose a very simple energy function for these cliques of the form  $-\eta x_i y_i$  where  $\eta$  is a positive constant. This has the desired effect of giving a lower energy (thus encouraging a higher probability) when  $x_i$  and  $y_i$  have the same sign and a higher energy when they have the opposite sign.

The remaining cliques comprise pairs of variables  $\{x_i, x_j\}$  where i and j are indices of neighbouring pixels. Again, we want the energy to be lower when the pixels have the same sign than when they have the opposite sign, and so we choose an energy given by  $-\beta x_i x_j$  where  $\beta$  is a positive constant.

Because a potential function is an arbitrary, nonnegative function over a maximal clique, we can multiply it by any nonnegative functions of subsets of the clique, or

**Figure 8.31** An undirected graphical model representing a Markov random field for image de-noising, in which  $x_i$  is a binary variable denoting the state of pixel  $i$  in the unknown noise-free image, and  $y_i$ denotes the corresponding value of pixel  $i$  in the observed noisy image.

equivalently we can add the corresponding energies. In this example, this allows us to add an extra term  $hx_i$  for each pixel i in the noise-free image. Such a term has the effect of biasing the model towards pixel values that have one particular sign in preference to the other.

The complete energy function for the model then takes the form

$$
E(\mathbf{x}, \mathbf{y}) = h \sum_{i} x_i - \beta \sum_{\{i, j\}} x_i x_j - \eta \sum_{i} x_i y_i \tag{8.42}
$$

which defines a joint distribution over **x** and **y** given by

$$
p(\mathbf{x}, \mathbf{y}) = \frac{1}{Z} \exp\{-E(\mathbf{x}, \mathbf{y})\}.
$$
 (8.43)

We now fix the elements of **y** to the observed values given by the pixels of the noisy image, which implicitly defines a conditional distribution  $p(\mathbf{x}|\mathbf{y})$  over noisefree images. This is an example of the *Ising model*, which has been widely studied in statistical physics. For the purposes of image restoration, we wish to find an image **x** having a high probability (ideally the maximum probability). To do this we shall use a simple iterative technique called *iterated conditional modes*, or *ICM* (Kittler and Föglein, 1984), which is simply an application of coordinate-wise gradient ascent. The idea is first to initialize the variables  $\{x_i\}$ , which we do by simply setting  $x_i =$  $y_i$  for all i. Then we take one node  $x_j$  at a time and we evaluate the total energy for the two possible states  $x_j = +1$  and  $x_j = -1$ , keeping all other node variables fixed, and set  $x_j$  to whichever state has the lower energy. This will either leave the probability unchanged, if  $x_j$  is unchanged, or will increase it. Because only *Exercise 8.13* one variable is changed, this is a simple local computation that can be performed efficiently. We then repeat the update for another site, and so on, until some suitable stopping criterion is satisfied. The nodes may be updated in a systematic way, for instance by repeatedly raster scanning through the image, or by choosing nodes at random.

> If we have a sequence of updates in which every site is visited at least once, and in which no changes to the variables are made, then by definition the algorithm

Image /page/7/Picture/10 description: A graphical representation of a probabilistic model is shown. There are two rows of nodes, with the bottom row labeled with 'xi' and the top row labeled with 'yi'. The nodes are connected by red lines, indicating dependencies or relationships. The nodes in the bottom row are white circles, and there are three of them visible. The nodes in the top row are also white circles, but some of them have blue circles above them, connected by a red line. Specifically, the first and third nodes in the top row have blue circles above them, and the second node in the top row also has a blue circle above it. The connections suggest a graphical model structure, possibly a factor graph or a Markov random field, with observed variables (blue circles) and latent variables (white circles).

**Figure 8.32** (a) Example of a directed graph. (b) The equivalent undirected graph.

Image /page/8/Figure/2 description: The image displays two diagrams, labeled (a) and (b), illustrating sequences of variables. Diagram (a) shows a directed sequence of variables x1, x2, ..., xN-1, xN, where each variable is represented by a circle and connected to the next by a red arrow. Diagram (b) shows an undirected sequence of variables x1, x2, ..., xN, xN-1, where each variable is represented by a circle and connected to the next by a red line. The sequence in (b) appears to be reversed at the end compared to (a).

will have converged to a local maximum of the probability. This need not, however, correspond to the global maximum.

For the purposes of this simple illustration, we have fixed the parameters to be  $\beta = 1.0$ ,  $\eta = 2.1$  and  $h = 0$ . Note that leaving  $h = 0$  simply means that the prior probabilities of the two states of  $x_i$  are equal. Starting with the observed noisy image as the initial configuration, we run ICM until convergence, leading to the de-noised image shown in the lower left panel of Figure 8.30. Note that if we set  $\beta = 0$ , which effectively removes the links between neighbouring pixels, then the global most probable solution is given by  $x_i = y_i$  for all i, corresponding to the observed *Exercise 8.14* noisy image.

Later we shall discuss a more effective algorithm for finding high probability so-*Section 8.4* lutions called the max-product algorithm, which typically leads to better solutions, although this is still not guaranteed to find the global maximum of the posterior distribution. However, for certain classes of model, including the one given by (8.42), there exist efficient algorithms based on *graph cuts* that are guaranteed to find the global maximum (Greig *et al.*, 1989; Boykov *et al.*, 2001; Kolmogorov and Zabih, 2004). The lower right panel of Figure 8.30 shows the result of applying a graph-cut algorithm to the de-noising problem.

## **8.3.4 Relation to directed graphs**

We have introduced two graphical frameworks for representing probability distributions, corresponding to directed and undirected graphs, and it is instructive to discuss the relation between these. Consider first the problem of taking a model that is specified using a directed graph and trying to convert it to an undirected graph. In some cases this is straightforward, as in the simple example in Figure 8.32. Here the joint distribution for the directed graph is given as a product of conditionals in the form

$$
p(\mathbf{x}) = p(x_1)p(x_2|x_1)p(x_3|x_2)\cdots p(x_N|x_{N-1}).
$$
\n(8.44)

Now let us convert this to an undirected graph representation, as shown in Figure 8.32. In the undirected graph, the maximal cliques are simply the pairs of neighbouring nodes, and so from (8.39) we wish to write the joint distribution in the form

$$
p(\mathbf{x}) = \frac{1}{Z} \psi_{1,2}(x_1, x_2) \psi_{2,3}(x_2, x_3) \cdots \psi_{N-1,N}(x_{N-1}, x_N).
$$
 (8.45)

*Section 8.4*

**8.3. Markov Random Fields 391**

**Figure 8.33** Example of a simple directed graph (a) and the corresponding moral graph (b).

Image /page/9/Figure/2 description: The image displays two diagrams, labeled (a) and (b), illustrating causal relationships between variables represented by nodes labeled x1, x2, x3, and x4. Diagram (a) shows a directed acyclic graph (DAG) where x1 and x3 are independent causes, and x2 is a cause for x4. Both x2 and x3 also appear to be causes for x4, with arrows pointing from x1 to x4, x2 to x4, and x3 to x4. Diagram (b) presents an undirected graph, possibly representing a Markov random field, where x1, x2, x3, and x4 are all interconnected. Specifically, x1 is connected to x2 and x4, x2 is connected to x1, x3, and x4, x3 is connected to x2 and x4, and x4 is connected to x1, x2, and x3, forming a complete graph or a triangle with an additional node connected to all vertices.

This is easily done by identifying

$$
\psi_{1,2}(x_1, x_2) = p(x_1)p(x_2|x_1)
$$

$$
\psi_{2,3}(x_2, x_3) = p(x_3|x_2)
$$

$$
\vdots
$$

$$
\psi_{N-1,N}(x_{N-1}, x_N) = p(x_N|x_{N-1})
$$

where we have absorbed the marginal  $p(x_1)$  for the first node into the first potential function. Note that in this case, the partition function  $Z = 1$ .

Let us consider how to generalize this construction, so that we can convert any distribution specified by a factorization over a directed graph into one specified by a factorization over an undirected graph. This can be achieved if the clique potentials of the undirected graph are given by the conditional distributions of the directed graph. In order for this to be valid, we must ensure that the set of variables that appears in each of the conditional distributions is a member of at least one clique of the undirected graph. For nodes on the directed graph having just one parent, this is achieved simply by replacing the directed link with an undirected link. However, for nodes in the directed graph having more than one parent, this is not sufficient. These are nodes that have 'head-to-head' paths encountered in our discussion of conditional independence. Consider a simple directed graph over 4 nodes shown in Figure 8.33. The joint distribution for the directed graph takes the form

$$
p(\mathbf{x}) = p(x_1)p(x_2)p(x_3)p(x_4|x_1, x_2, x_3). \tag{8.46}
$$

We see that the factor  $p(x_4|x_1, x_2, x_3)$  involves the four variables  $x_1, x_2, x_3$ , and  $x_4$ , and so these must all belong to a single clique if this conditional distribution is to be absorbed into a clique potential. To ensure this, we add extra links between all pairs of parents of the node  $x_4$ . Anachronistically, this process of 'marrying the parents' has become known as *moralization*, and the resulting undirected graph, after dropping the arrows, is called the *moral graph*. It is important to observe that the moral graph in this example is fully connected and so exhibits no conditional independence properties, in contrast to the original directed graph.

Thus in general to convert a directed graph into an undirected graph, we first add additional undirected links between all pairs of parents for each node in the graph and

then drop the arrows on the original links to give the moral graph. Then we initialize all of the clique potentials of the moral graph to 1. We then take each conditional distribution factor in the original directed graph and multiply it into one of the clique potentials. There will always exist at least one maximal clique that contains all of the variables in the factor as a result of the moralization step. Note that in all cases the partition function is given by  $Z = 1$ .

The process of converting a directed graph into an undirected graph plays an *Section 8.4* important role in exact inference techniques such as the *junction tree algorithm*. Converting from an undirected to a directed representation is much less common and in general presents problems due to the normalization constraints.

> We saw that in going from a directed to an undirected representation we had to discard some conditional independence properties from the graph. Of course, we could always trivially convert any distribution over a directed graph into one over an undirected graph by simply using a fully connected undirected graph. This would, however, discard all conditional independence properties and so would be vacuous. The process of moralization adds the fewest extra links and so retains the maximum number of independence properties.

We have seen that the procedure for determining the conditional independence properties is different between directed and undirected graphs. It turns out that the two types of graph can express different conditional independence properties, and it is worth exploring this issue in more detail. To do so, we return to the view of Section 8.2 **a** specific (directed or undirected) graph as a filter, so that the set of all possible distributions over the given variables could be reduced to a subset that respects the conditional independencies implied by the graph. A graph is said to be a *D map* (for 'dependency map') of a distribution if every conditional independence statement satisfied by the distribution is reflected in the graph. Thus a completely disconnected graph (no links) will be a trivial D map for any distribution.

> Alternatively, we can consider a specific distribution and ask which graphs have the appropriate conditional independence properties. If every conditional independence statement implied by a graph is satisfied by a specific distribution, then the graph is said to be an *I map* (for 'independence map') of that distribution. Clearly a fully connected graph will be a trivial I map for any distribution.

> If it is the case that every conditional independence property of the distribution is reflected in the graph, and vice versa, then the graph is said to be a *perfect map* for

**Figure 8.34** Venn diagram illustrating the set of all distributions P over a given set of variables, together with the set of distributions D that can be represented as a perfect map using a directed graph, and the set U that can be represented as a perfect map using an undirected graph.

Image /page/10/Figure/8 description: A Venn diagram shows three overlapping ellipses. The largest ellipse is red and encompasses the other two. Inside the red ellipse, there is a green ellipse and a blue ellipse that overlap each other. The letter 'D' is placed below the green ellipse, the letter 'U' is placed below the overlapping region of the green and blue ellipses, and the letter 'P' is placed below the red ellipse.

*Section 8.4*

*Section 8.2*

 $\mathcal{C}_{0}^{(n)}$ 

 $A \qquad \qquad B$ 

Figure 8.35 A directed graph whose conditional independence properties cannot be expressed using an undirected graph over the same three variables.

that distribution. A perfect map is therefore both an I map and a D map.

Consider the set of distributions such that for each distribution there exists a directed graph that is a perfect map. This set is distinct from the set of distributions such that for each distribution there exists an undirected graph that is a perfect map. In addition there are distributions for which neither directed nor undirected graphs offer a perfect map. This is illustrated as a Venn diagram in Figure 8.34.

Figure 8.35 shows an example of a directed graph that is a perfect map for a distribution satisfying the conditional independence properties  $A \perp\!\!\!\perp B \mid \emptyset$  and  $A \not\perp B \mid C$ . There is no corresponding undirected graph over the same three variables that is a perfect map.

Conversely, consider the undirected graph over four variables shown in Figure 8.36. This graph exhibits the properties  $A \not\perp B \mid \emptyset$ ,  $C \perp D \mid A \cup B$  and  $A \perp\!\!\!\perp B \mid C \cup D$ . There is no directed graph over four variables that implies the same set of conditional independence properties.

The graphical framework can be extended in a consistent way to graphs that include both directed and undirected links. These are called *chain graphs* (Lauritzen and Wermuth, 1989; Frydenberg, 1990), and contain the directed and undirected graphs considered so far as special cases. Although such graphs can represent a broader class of distributions than either directed or undirected alone, there remain distributions for which even a chain graph cannot provide a perfect map. Chain graphs are not discussed further in this book.

**Figure 8.36** An undirected graph whose conditional independence properties cannot be expressed in terms of a directed graph over the same variables.

Image /page/11/Picture/8 description: A diagram shows four circles labeled A, B, C, and D. The circles are arranged in a diamond shape. Circle A is to the left, circle B is to the right, circle C is at the top, and circle D is at the bottom. Lines connect circle A to circle C, circle A to circle D, circle B to circle C, and circle B to circle D. The diagram is outlined in red.

# **8.4. Inference in Graphical Models**

We turn now to the problem of inference in graphical models, in which some of the nodes in a graph are clamped to observed values, and we wish to compute the posterior distributions of one or more subsets of other nodes. As we shall see, we can exploit the graphical structure both to find efficient algorithms for inference, and

**Figure 8.37** A graphical representation of Bayes' theorem. See the text for details.

Image /page/12/Figure/2 description: The image displays three diagrams labeled (a), (b), and (c). Each diagram consists of two circles, one labeled 'x' above the other labeled 'y'. A red arrow connects the 'x' circle to the 'y' circle. In diagram (a), both circles are white. In diagram (b), the 'x' circle is white, and the 'y' circle is filled with blue. In diagram (c), the 'x' circle is white, and the 'y' circle is filled with blue, with the arrow pointing upwards from 'y' to 'x'.

to make the structure of those algorithms transparent. Specifically, we shall see that many algorithms can be expressed in terms of the propagation of local *messages* around the graph. In this section, we shall focus primarily on techniques for exact inference, and in Chapter 10 we shall consider a number of approximate inference algorithms.

To start with, let us consider the graphical interpretation of Bayes' theorem. Suppose we decompose the joint distribution  $p(x, y)$  over two variables x and y into a product of factors in the form  $p(x, y) = p(x)p(y|x)$ . This can be represented by the directed graph shown in Figure 8.37(a). Now suppose we observe the value of  $y$ , as indicated by the shaded node in Figure 8.37(b). We can view the marginal distribution  $p(x)$  as a prior over the latent variable x, and our goal is to infer the corresponding posterior distribution over  $x$ . Using the sum and product rules of probability we can evaluate

$$
p(y) = \sum_{x'} p(y|x')p(x')
$$
 (8.47)

which can then be used in Bayes' theorem to calculate

$$
p(x|y) = \frac{p(y|x)p(x)}{p(y)}.
$$
 (8.48)

Thus the joint distribution is now expressed in terms of  $p(y)$  and  $p(x|y)$ . From a graphical perspective, the joint distribution  $p(x, y)$  is now represented by the graph shown in Figure 8.37(c), in which the direction of the arrow is reversed. This is the simplest example of an inference problem for a graphical model.

### **8.4.1 Inference on a chain**

Now consider a more complex problem involving the chain of nodes of the form shown in Figure 8.32. This example will lay the foundation for a discussion of exact inference in more general graphs later in this section.

Specifically, we shall consider the undirected graph in Figure 8.32(b). We have already seen that the directed chain can be transformed into an equivalent undirected chain. Because the directed graph does not have any nodes with more than one parent, this does not require the addition of any extra links, and the directed and undirected versions of this graph express exactly the same set of conditional independence statements.

The joint distribution for this graph takes the form

$$
p(\mathbf{x}) = \frac{1}{Z} \psi_{1,2}(x_1, x_2) \psi_{2,3}(x_2, x_3) \cdots \psi_{N-1,N}(x_{N-1}, x_N).
$$
 (8.49)

We shall consider the specific case in which the  $N$  nodes represent discrete variables each having K states, in which case each potential function  $\psi_{n-1,n}(x_{n-1}, x_n)$ comprises an K × K table, and so the joint distribution has  $(N-1)K^2$  parameters.

Let us consider the inference problem of finding the marginal distribution  $p(x_n)$ for a specific node  $x_n$  that is part way along the chain. Note that, for the moment, there are no observed nodes. By definition, the required marginal is obtained by summing the joint distribution over all variables except  $x_n$ , so that

$$
p(x_n) = \sum_{x_1} \cdots \sum_{x_{n-1}} \sum_{x_{n+1}} \cdots \sum_{x_N} p(\mathbf{x}).
$$
 (8.50)

In a naive implementation, we would first evaluate the joint distribution and then perform the summations explicitly. The joint distribution can be represented as a set of numbers, one for each possible value for **x**. Because there are <sup>N</sup> variables each with K states, there are  $K^N$  values for **x** and so evaluation and storage of the joint distribution, as well as marginalization to obtain  $p(x_n)$ , all involve storage and computation that scale exponentially with the length  $N$  of the chain.

We can, however, obtain a much more efficient algorithm by exploiting the conditional independence properties of the graphical model. If we substitute the factorized expression (8.49) for the joint distribution into (8.50), then we can rearrange the order of the summations and the multiplications to allow the required marginal to be evaluated much more efficiently. Consider for instance the summation over  $x_N$ . The potential  $\psi_{N-1,N}(x_{N-1},x_N)$  is the only one that depends on  $x_N$ , and so we can perform the summation

$$
\sum_{x_N} \psi_{N-1,N}(x_{N-1}, x_N) \tag{8.51}
$$

first to give a function of  $x_{N-1}$ . We can then use this to perform the summation over  $x_{N-1}$ , which will involve only this new function together with the potential  $\psi_{N-2,N-1}(x_{N-2}, x_{N-1})$ , because this is the only other place that  $x_{N-1}$  appears. Similarly, the summation over  $x_1$  involves only the potential  $\psi_{1,2}(x_1, x_2)$  and so can be performed separately to give a function of  $x_2$ , and so on. Because each summation effectively removes a variable from the distribution, this can be viewed as the removal of a node from the graph.

If we group the potentials and summations together in this way, we can express

the desired marginal in the form

$$
p(x_n) = \frac{1}{Z}
$$

$$
\underbrace{\left[\sum_{x_{n-1}} \psi_{n-1,n}(x_{n-1}, x_n) \cdots \left[\sum_{x_2} \psi_{2,3}(x_2, x_3) \left[\sum_{x_1} \psi_{1,2}(x_1, x_2)\right]\right] \cdots \right]}_{\mu_\alpha(x_n)}
$$

$$
\underbrace{\left[\sum_{x_{n+1}} \psi_{n,n+1}(x_n, x_{n+1}) \cdots \left[\sum_{x_N} \psi_{N-1,N}(x_{N-1}, x_N)\right] \cdots \right]}_{\mu_\beta(x_n)}
$$
. (8.52)

The reader is encouraged to study this re-ordering carefully as the underlying idea forms the basis for the later discussion of the general sum-product algorithm. Here the key concept that we are exploiting is that multiplication is distributive over addition, so that

$$
ab + ac = a(b + c) \tag{8.53}
$$

in which the left-hand side involves three arithmetic operations whereas the righthand side reduces this to two operations.

Let us work out the computational cost of evaluating the required marginal using this re-ordered expression. We have to perform  $N-1$  summations each of which is over  $K$  states and each of which involves a function of two variables. For instance, the summation over  $x_1$  involves only the function  $\psi_{1,2}(x_1, x_2)$ , which is a table of  $K \times K$  numbers. We have to sum this table over  $x_1$  for each value of  $x_2$  and so this has  $O(K^2)$  cost. The resulting vector of K numbers is multiplied by the matrix of numbers  $\psi_{2,3}(x_2, x_3)$  and so is again  $O(K^2)$ . Because there are  $N-1$  summations and multiplications of this kind, the total cost of evaluating the marginal  $p(x_n)$  is  $O(NK^2)$ . This is linear in the length of the chain, in contrast to the exponential cost of a naive approach. We have therefore been able to exploit the many conditional independence properties of this simple graph in order to obtain an efficient calculation. If the graph had been fully connected, there would have been no conditional independence properties, and we would have been forced to work directly with the full joint distribution.

We now give a powerful interpretation of this calculation in terms of the passing of local *messages* around on the graph. From (8.52) we see that the expression for the marginal  $p(x_n)$  decomposes into the product of two factors times the normalization constant

$$
p(x_n) = \frac{1}{Z} \mu_\alpha(x_n) \mu_\beta(x_n).
$$
 (8.54)

We shall interpret  $\mu_{\alpha}(x_n)$  as a message passed forwards along the chain from node  $x_{n-1}$  to node  $x_n$ . Similarly,  $\mu_\beta(x_n)$  can be viewed as a message passed backwards

**Figure 8.38** The marginal distribution  $p(x_n)$  for a node  $x_n$  along the chain is obtained by multiplying the two messages  $\mu_\alpha(x_n)$  and  $\mu_\beta(x_n),$  and then normalizing. These messages can themselves be evaluated recursively by passing messages from both ends of the chain towards node  $x_n$ .

Image /page/15/Figure/2 description: The image displays a linear chain of nodes, represented by red circles, labeled from x1 to xN. The nodes are connected by red lines, with dotted lines indicating intermediate nodes between x1 and xn-1, and between xn+1 and xN. Blue arrows indicate fluxes between adjacent nodes. Specifically, there is a flux labeled \mu\_\alpha(x\_{n-1}) pointing from x\_{n-1} to the left, and a flux labeled \mu\_\alpha(x\_n) pointing from x\_n to the left. Additionally, there is a flux labeled \mu\_\beta(x\_n) pointing from x\_n to the right, and a flux labeled \mu\_\beta(x\_{n+1}) pointing from x\_{n+1} to the right.

along the chain to node  $x_n$  from node  $x_{n+1}$ . Note that each of the messages comprises a set of K values, one for each choice of  $x_n$ , and so the product of two messages should be interpreted as the point-wise multiplication of the elements of the two messages to give another set of  $K$  values.

The message  $\mu_{\alpha}(x_n)$  can be evaluated recursively because

$$
\mu_{\alpha}(x_n) = \sum_{x_{n-1}} \psi_{n-1,n}(x_{n-1}, x_n) \left[ \sum_{x_{n-2}} \cdots \right]
$$
$$
= \sum_{x_{n-1}} \psi_{n-1,n}(x_{n-1}, x_n) \mu_{\alpha}(x_{n-1}). \tag{8.55}
$$

We therefore first evaluate

$$
\mu_{\alpha}(x_2) = \sum_{x_1} \psi_{1,2}(x_1, x_2) \tag{8.56}
$$

and then apply (8.55) repeatedly until we reach the desired node. Note carefully the structure of the message passing equation. The outgoing message  $\mu_{\alpha}(x_n)$  in (8.55) is obtained by multiplying the incoming message  $\mu_{\alpha}(x_{n-1})$  by the local potential involving the node variable and the outgoing variable and then summing over the node variable.

Similarly, the message  $\mu_{\beta}(x_n)$  can be evaluated recursively by starting with node  $x_N$  and using

$$
\mu_{\beta}(x_n) = \sum_{x_{n+1}} \psi_{n+1,n}(x_{n+1}, x_n) \left[ \sum_{x_{n+2}} \cdots \right]
$$
$$
= \sum_{x_{n+1}} \psi_{n+1,n}(x_{n+1}, x_n) \mu_{\beta}(x_{n+1}). \tag{8.57}
$$

This recursive message passing is illustrated in Figure 8.38. The normalization constant  $Z$  is easily evaluated by summing the right-hand side of  $(8.54)$  over all states of  $x_n$ , an operation that requires only  $O(K)$  computation.

Graphs of the form shown in Figure 8.38 are called *Markov chains*, and the corresponding message passing equations represent an example of the *Chapman-Kolmogorov* equations for Markov processes (Papoulis, 1984).

Now suppose we wish to evaluate the marginals  $p(x_n)$  for every node  $n \in$  $\{1,\ldots,N\}$  in the chain. Simply applying the above procedure separately for each node will have computational cost that is  $O(N^2M^2)$ . However, such an approach would be very wasteful of computation. For instance, to find  $p(x_1)$  we need to propagate a message  $\mu_{\beta}(\cdot)$  from node  $x_N$  back to node  $x_2$ . Similarly, to evaluate  $p(x_2)$ we need to propagate a messages  $\mu_{\beta}(\cdot)$  from node  $x_N$  back to node  $x_3$ . This will involve much duplicated computation because most of the messages will be identical in the two cases.

Suppose instead we first launch a message  $\mu_{\beta}(x_{N-1})$  starting from node  $x_N$ and propagate corresponding messages all the way back to node  $x_1$ , and suppose we similarly launch a message  $\mu_{\alpha}(x_2)$  starting from node  $x_1$  and propagate the corresponding messages all the way forward to node  $x_N$ . Provided we store all of the intermediate messages along the way, then any node can evaluate its marginal simply by applying (8.54). The computational cost is only twice that for finding the marginal of a single node, rather than  $N$  times as much. Observe that a message has passed once in each direction across each link in the graph. Note also that the normalization constant Z need be evaluated only once, using any convenient node.

If some of the nodes in the graph are observed, then the corresponding variables are simply clamped to their observed values and there is no summation. To see this, note that the effect of clamping a variable  $x_n$  to an observed value  $\hat{x}_n$  can be expressed by multiplying the joint distribution by (one or more copies of) an additional function  $I(x_n, \hat{x}_n)$ , which takes the value 1 when  $x_n = \hat{x}_n$  and the value 0 otherwise. One such function can then be absorbed into each of the potentials that contain  $x_n$ . Summations over  $x_n$  then contain only one term in which  $x_n = \hat{x}_n$ .

Now suppose we wish to calculate the joint distribution  $p(x_{n-1}, x_n)$  for two neighbouring nodes on the chain. This is similar to the evaluation of the marginal for a single node, except that there are now two variables that are not summed out. *Exercise 8.15* A few moments thought will show that the required joint distribution can be written in the form

$$
p(x_{n-1}, x_n) = \frac{1}{Z} \mu_\alpha(x_{n-1}) \psi_{n-1,n}(x_{n-1}, x_n) \mu_\beta(x_n).
$$
 (8.58)

Thus we can obtain the joint distributions over all of the sets of variables in each of the potentials directly once we have completed the message passing required to obtain the marginals.

This is a useful result because in practice we may wish to use parametric forms for the clique potentials, or equivalently for the conditional distributions if we started from a directed graph. In order to learn the parameters of these potentials in situa-*Chapter 9* tions where not all of the variables are observed, we can employ the *EM algorithm*, and it turns out that the local joint distributions of the cliques, conditioned on any observed data, is precisely what is needed in the E step. We shall consider some examples of this in detail in Chapter 13.

### **8.4.2 Trees**

We have seen that exact inference on a graph comprising a chain of nodes can be performed efficiently in time that is linear in the number of nodes, using an algorithm

*Exercise 8.15*

Image /page/17/Figure/1 description: Figure 8.39 shows examples of tree-structured graphs, specifically illustrating (a) an undirected tree, (b) a directed tree, and (c) a directed polytree.

Image /page/17/Figure/2 description: The image displays three diagrams labeled (a), (b), and (c). Diagram (a) shows a central circle connected to four other circles arranged in a cross shape, with lines indicating connections but no arrows. Diagram (b) depicts a tree-like structure with a top circle connected to two circles below it, each of which is connected to two more circles, forming a binary tree with arrows indicating the direction of the connections. Diagram (c) shows a central circle with four circles arranged around it in a cross shape, with arrows pointing from the outer circles towards the central circle.

that can be interpreted in terms of messages passed along the chain. More generally, inference can be performed efficiently using local message passing on a broader class of graphs called *trees*. In particular, we shall shortly generalize the message passing formalism derived above for chains to give the *sum-product* algorithm, which provides an efficient framework for exact inference in tree-structured graphs.

In the case of an undirected graph, a tree is defined as a graph in which there is one, and only one, path between any pair of nodes. Such graphs therefore do not have loops. In the case of directed graphs, a tree is defined such that there is a single node, called the *root*, which has no parents, and all other nodes have one parent. If we convert a directed tree into an undirected graph, we see that the moralization step will not add any links as all nodes have at most one parent, and as a consequence the corresponding moralized graph will be an undirected tree. Examples of undirected and directed trees are shown in Figure 8.39(a) and 8.39(b). Note that a distribution represented as a directed tree can easily be converted into one represented by an *Exercise 8.18* undirected tree, and vice versa.

> If there are nodes in a directed graph that have more than one parent, but there is still only one path (ignoring the direction of the arrows) between any two nodes, then the graph is a called a *polytree*, as illustrated in Figure 8.39(c). Such a graph will have more than one node with the property of having no parents, and furthermore, the corresponding moralized undirected graph will have loops.

### **8.4.3 Factor graphs**

The sum-product algorithm that we derive in the next section is applicable to undirected and directed trees and to polytrees. It can be cast in a particularly simple and general form if we first introduce a new graphical construction called a *factor graph* (Frey, 1998; Kschischnang *et al.*, 2001).

Both directed and undirected graphs allow a global function of several variables to be expressed as a product of factors over subsets of those variables. Factor graphs make this decomposition explicit by introducing additional nodes for the factors themselves in addition to the nodes representing the variables. They also allow us to be more explicit about the details of the factorization, as we shall see.

Let us write the joint distribution over a set of variables in the form of a product of factors

$$
p(\mathbf{x}) = \prod_{s} f_s(\mathbf{x}_s)
$$
\n(8.59)

where  $x<sub>s</sub>$  denotes a subset of the variables. For convenience, we shall denote the

*Exercise 8.18*

**Figure 8.40** Example of a factor graph, which corresponds to the factorization (8.60).

Image /page/18/Figure/2 description: A bipartite graph is depicted with three circular nodes labeled x1, x2, and x3 at the top, and four square nodes labeled fa, fb, fc, and fd at the bottom. Red lines connect the nodes, indicating edges. Specifically, x1 is connected to fa and fb. x2 is connected to fa, fb, and fc. x3 is connected to fc and fd.

individual variables by  $x_i$ , however, as in earlier discussions, these can comprise groups of variables (such as vectors or matrices). Each factor  $f_s$  is a function of a corresponding set of variables  $x_s$ .

Directed graphs, whose factorization is defined by (8.5), represent special cases of (8.59) in which the factors  $f_s(\mathbf{x}_s)$  are local conditional distributions. Similarly, undirected graphs, given by (8.39), are a special case in which the factors are potential functions over the maximal cliques (the normalizing coefficient  $1/Z$  can be viewed as a factor defined over the empty set of variables).

In a factor graph, there is a node (depicted as usual by a circle) for every variable in the distribution, as was the case for directed and undirected graphs. There are also additional nodes (depicted by small squares) for each factor  $f_s(\mathbf{x}_s)$  in the joint distribution. Finally, there are undirected links connecting each factor node to all of the variables nodes on which that factor depends. Consider, for example, a distribution that is expressed in terms of the factorization

$$
p(\mathbf{x}) = f_a(x_1, x_2) f_b(x_1, x_2) f_c(x_2, x_3) f_d(x_3).
$$
 (8.60)

This can be expressed by the factor graph shown in Figure 8.40. Note that there are two factors  $f_a(x_1, x_2)$  and  $f_b(x_1, x_2)$  that are defined over the same set of variables. In an undirected graph, the product of two such factors would simply be lumped together into the same clique potential. Similarly,  $f_c(x_2, x_3)$  and  $f_d(x_3)$  could be combined into a single potential over  $x_2$  and  $x_3$ . The factor graph, however, keeps such factors explicit and so is able to convey more detailed information about the underlying factorization.

Image /page/18/Figure/8 description: The image displays three factor graphs labeled (a), (b), and (c). Graph (a) is a triangle with nodes labeled x1, x2, and x3, connected by edges. Graph (b) shows a central square node labeled 'f' connected to three circular nodes labeled x1, x2, and x3. Graph (c) features two square nodes labeled 'fa' and 'fb'. Node 'fa' is connected to circular nodes x1 and x2. Node 'fb' is connected to circular nodes x2 and x3. Additionally, 'fa' and 'fb' are connected to each other, and 'fa' is also connected to x3.

**Figure 8.41** (a) An undirected graph with a single clique potential  $\psi(x_1, x_2, x_3)$ . (b) A factor graph with factor  $f(x_1, x_2, x_3) = \psi(x_1, x_2, x_3)$  representing the same distribution as the undirected graph. (c) A different factor graph representing the same distribution, whose factors satisfy  $f_a(x_1, x_2, x_3)f_b(x_1, x_2) = \psi(x_1, x_2, x_3)$ .

Image /page/19/Figure/1 description: The image displays three diagrams labeled (a), (b), and (c), illustrating different graphical models. Diagram (a) shows a directed acyclic graph with two nodes labeled x1 and x2 pointing to a third node labeled x3. Diagram (b) presents a factor graph where a square node labeled 'f' connects to three circular nodes labeled x1, x2, and x3. Diagram (c) depicts another factor graph with two square nodes labeled 'fa' and 'fb', and a central square node labeled 'fc'. The node 'fa' connects to x1, 'fc' connects to x1, x2, and x3, and 'fb' connects to x2. All nodes and edges are colored red.

**Figure 8.42** (a) A directed graph with the factorization  $p(x_1)p(x_2)p(x_3|x_1, x_2)$ . (b) A factor graph representing the same distribution as the directed graph, whose factor satisfies  $f(x_1, x_2, x_3) = p(x_1)p(x_2)p(x_3|x_1, x_2)$ . (c) A different factor graph representing the same distribution with factors  $f_a(x_1) = p(x_1)$ ,  $f_b(x_2) = p(x_2)$  and  $f_c(x_1, x_2, x_3) = p(x_3|x_1, x_2).$ 

Factor graphs are said to be *bipartite* because they consist of two distinct kinds of nodes, and all links go between nodes of opposite type. In general, factor graphs can therefore always be drawn as two rows of nodes (variable nodes at the top and factor nodes at the bottom) with links between the rows, as shown in the example in Figure 8.40. In some situations, however, other ways of laying out the graph may be more intuitive, for example when the factor graph is derived from a directed or undirected graph, as we shall see.

If we are given a distribution that is expressed in terms of an undirected graph, then we can readily convert it to a factor graph. To do this, we create variable nodes corresponding to the nodes in the original undirected graph, and then create additional factor nodes corresponding to the maximal cliques  $\mathbf{x}_s$ . The factors  $f_s(\mathbf{x}_s)$  are then set equal to the clique potentials. Note that there may be several different factor graphs that correspond to the same undirected graph. These concepts are illustrated in Figure 8.41.

Similarly, to convert a directed graph to a factor graph, we simply create variable nodes in the factor graph corresponding to the nodes of the directed graph, and then create factor nodes corresponding to the conditional distributions, and then finally add the appropriate links. Again, there can be multiple factor graphs all of which correspond to the same directed graph. The conversion of a directed graph to a factor graph is illustrated in Figure 8.42.

We have already noted the importance of tree-structured graphs for performing efficient inference. If we take a directed or undirected tree and convert it into a factor graph, then the result will again be a tree (in other words, the factor graph will have no loops, and there will be one and only one path connecting any two nodes). In the case of a directed polytree, conversion to an undirected graph results in loops due to the moralization step, whereas conversion to a factor graph again results in a tree, as illustrated in Figure 8.43. In fact, local cycles in a directed graph due to links connecting parents of a node can be removed on conversion to a factor graph by defining the appropriate factor function, as shown in Figure 8.44.

We have seen that multiple different factor graphs can represent the same directed or undirected graph. This allows factor graphs to be more specific about the

Image /page/20/Figure/1 description: The image displays three diagrams labeled (a), (b), and (c). Diagram (a) shows a directed acyclic graph with five nodes represented by circles and directed edges connecting them. Diagram (b) shows an undirected graph with five nodes represented by circles and edges connecting them, forming two triangles and a line segment. Diagram (c) shows a graph with seven nodes, five represented by circles and two represented by red squares. The nodes are connected by edges, with the red squares positioned between some of the circular nodes, suggesting a different type of relationship or structure compared to the previous diagrams.

**Figure 8.43** (a) A directed polytree. (b) The result of converting the polytree into an undirected graph showing the creation of loops. (c) The result of converting the polytree into a factor graph, which retains the tree structure.

precise form of the factorization. Figure 8.45 shows an example of a fully connected undirected graph along with two different factor graphs. In (b), the joint distribution is given by a general form  $p(x) = f(x_1, x_2, x_3)$ , whereas in (c), it is given by the more specific factorization  $p(\mathbf{x}) = f_a(x_1, x_2) f_b(x_1, x_3) f_c(x_2, x_3)$ . It should be emphasized that the factorization in (c) does not correspond to any conditional independence properties.

### **8.4.4 The sum-product algorithm**

We shall now make use of the factor graph framework to derive a powerful class of efficient, exact inference algorithms that are applicable to tree-structured graphs. Here we shall focus on the problem of evaluating local marginals over nodes or subsets of nodes, which will lead us to the *sum-product* algorithm. Later we shall modify the technique to allow the most probable state to be found, giving rise to the *max-sum* algorithm.

Also we shall suppose that all of the variables in the model are discrete, and so marginalization corresponds to performing sums. The framework, however, is equally applicable to linear-Gaussian models in which case marginalization involves integration, and we shall consider an example of this in detail when we discuss linear *Section 13.3* dynamical systems.

**Figure 8.44** (a) A fragment of a directed graph having a local cycle. (b) Conversion to a fragment of a factor graph having a tree structure, in which  $f(x_1, x_2, x_3) =$  $p(x_1)p(x_2|x_1)p(x_3|x_1, x_2).$ 

Image /page/20/Figure/9 description: The image displays two diagrams, labeled (a) and (b), illustrating graphical models. Diagram (a) shows a directed acyclic graph (DAG) with three nodes labeled x1, x2, and x3. There are directed edges from x1 to x2, x1 to x3, and x2 to x3, forming a triangular structure with arrows indicating the direction of influence. Diagram (b) presents a factor graph. It features three circular nodes labeled x1, x2, and x3, connected to a central square node. The square node is labeled with the function f(x1, x2, x3), indicating a relationship or dependency among the three variables.

Image /page/21/Figure/1 description: The image displays three factor graphs labeled (a), (b), and (c). Graph (a) shows a triangle with nodes labeled x1, x2, and x3, connected by edges. Graph (b) also has nodes x1, x2, and x3, but a central square node labeled f(x1, x2, x3) connects to all three variable nodes. Graph (c) is a triangle with nodes x1, x2, and x3, and square nodes labeled fa, fb, and fc are placed on the edges connecting the variable nodes.

**Figure 8.45** (a) A fully connected undirected graph. (b) and (c) Two factor graphs each of which corresponds to the undirected graph in (a).

There is an algorithm for exact inference on directed graphs without loops known as *belief propagation* (Pearl, 1988; Lauritzen and Spiegelhalter, 1988), and is equivalent to a special case of the sum-product algorithm. Here we shall consider only the sum-product algorithm because it is simpler to derive and to apply, as well as being more general.

We shall assume that the original graph is an undirected tree or a directed tree or polytree, so that the corresponding factor graph has a tree structure. We first convert the original graph into a factor graph so that we can deal with both directed and undirected models using the same framework. Our goal is to exploit the structure of the graph to achieve two things: (i) to obtain an efficient, exact inference algorithm for finding marginals; (ii) in situations where several marginals are required to allow computations to be shared efficiently.

We begin by considering the problem of finding the marginal  $p(x)$  for particular variable node  $x$ . For the moment, we shall suppose that all of the variables are hidden. Later we shall see how to modify the algorithm to incorporate evidence corresponding to observed variables. By definition, the marginal is obtained by summing the joint distribution over all variables except  $x$  so that

$$
p(x) = \sum_{\mathbf{x} \setminus x} p(\mathbf{x})
$$
\n(8.61)

where  $x \setminus x$  denotes the set of variables in x with variable x omitted. The idea is to substitute for  $p(x)$  using the factor graph expression (8.59) and then interchange summations and products in order to obtain an efficient algorithm. Consider the fragment of graph shown in Figure 8.46 in which we see that the tree structure of the graph allows us to partition the factors in the joint distribution into groups, with one group associated with each of the factor nodes that is a neighbour of the variable node  $x$ . We see that the joint distribution can be written as a product of the form

$$
p(\mathbf{x}) = \prod_{s \in ne(x)} F_s(x, X_s)
$$
\n(8.62)

 $ne(x)$  denotes the set of factor nodes that are neighbours of x, and  $X_s$  denotes the set of all variables in the subtree connected to the variable node  $x$  via the factor node
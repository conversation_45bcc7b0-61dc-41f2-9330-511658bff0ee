# Neural Tangent Kernel: Convergence and Generalization in Neural Networks

Arthur <PERSON>acot École Polytechnique Fédérale <NAME_EMAIL>

Franck Gabriel Imperial College London and École Polytechnique Fédérale <NAME_EMAIL>

> Clément Hongler École Polytechnique Fédérale <NAME_EMAIL>

## Abstract

At initialization, artificial neural networks (ANNs) are equivalent to Gaussian processes in the infinite-width limit [\(16;](#page-9-0) [4;](#page-9-1) [7;](#page-9-2) [13;](#page-9-3) [6\)](#page-9-4), thus connecting them to kernel methods. We prove that the evolution of an ANN during training can also be described by a kernel: during gradient descent on the parameters of an ANN, the network function  $f_\theta$  (which maps input vectors to output vectors) follows the kernel gradient of the functional cost (which is convex, in contrast to the parameter cost) w.r.t. a new kernel: the Neural Tangent Kernel (NTK). This kernel is central to describe the generalization features of ANNs. While the NTK is random at initialization and varies during training, in the infinite-width limit it converges to an explicit limiting kernel and it stays constant during training. This makes it possible to study the training of ANNs in function space instead of parameter space. Convergence of the training can then be related to the positive-definiteness of the limiting NTK. We prove the positive-definiteness of the limiting NTK when the data is supported on the sphere and the non-linearity is non-polynomial.

We then focus on the setting of least-squares regression and show that in the infinitewidth limit, the network function  $f_\theta$  follows a linear differential equation during training. The convergence is fastest along the largest kernel principal components of the input data with respect to the NTK, hence suggesting a theoretical motivation for early stopping.

Finally we study the NTK numerically, observe its behavior for wide networks, and compare it to the infinite-width limit.

# 1 Introduction

Artificial neural networks (ANNs) have achieved impressive results in numerous areas of machine learning. While it has long been known that ANNs can approximate any function with sufficiently many hidden neurons [\(11;](#page-9-5) [14\)](#page-9-6), it is not known what the optimization of ANNs converges to. Indeed the loss surface of neural networks optimization problems is highly non-convex: it has a high number of saddle points which may slow down the convergence [\(5\)](#page-9-7). A number of results [\(3;](#page-9-8) [17;](#page-10-0) [18\)](#page-10-1) suggest that for wide enough networks, there are very few "bad" local minima, i.e. local minima with much higher cost than the global minimum. More recently, the investigation of the geometry of the loss landscape at initialization has been the subject of a precise study [\(12\)](#page-9-9). The analysis of the dynamics of training in the large-width limit for shallow networks has seen recent progress as well [\(15\)](#page-9-10). To the best of the authors knowledge, the dynamics of deep networks has however remained an open problem until the present paper: see the contributions section below.

A particularly mysterious feature of ANNs is their good generalization properties in spite of their usual over-parametrization [\(20\)](#page-10-2). It seems paradoxical that a reasonably large neural network can fit random labels, while still obtaining good test accuracy when trained on real data [\(23\)](#page-10-3). It can be noted that in this case, kernel methods have the same properties [\(1\)](#page-9-11).

In the infinite-width limit, ANNs have a Gaussian distribution described by a kernel [\(16;](#page-9-0) [4;](#page-9-1) [7;](#page-9-2) [13;](#page-9-3) [6\)](#page-9-4). These kernels are used in Bayesian inference or Support Vector Machines, yielding results comparable to ANNs trained with gradient descent [\(2;](#page-9-12) [13\)](#page-9-3). We will see that in the same limit, the behavior of ANNs during training is described by a related kernel, which we call the neural tangent network (NTK).

### 1.1 Contribution

We study the network function  $f_\theta$  of an ANN, which maps an input vector to an output vector, where  $\theta$  is the vector of the parameters of the ANN. In the limit as the widths of the hidden layers tend to infinity, the network function at initialization,  $f_\theta$  converges to a Gaussian distribution [\(16;](#page-9-0) [4;](#page-9-1) [7;](#page-9-2) [13;](#page-9-3) [6\)](#page-9-4).

In this paper, we investigate fully connected networks in this infinite-width limit, and describe the dynamics of the network function  $f_{\theta}$  during training:

- During gradient descent, we show that the dynamics of  $f_\theta$  follows that of the so-called *kernel gradient descent* in function space with respect to a limiting kernel, which only depends on the depth of the network, the choice of nonlinearity and the initialization variance.
- The convergence properties of ANNs during training can then be related to the positivedefiniteness of the infinite-width limit NTK. In the case when the dataset is supported on a sphere, we prove this positive-definiteness using recent results on dual activation functions [\(4\)](#page-9-1). The values of the network function  $f_\theta$  outside the training set is described by the NTK, which is crucial to understand how ANN generalize.
- For a least-squares regression loss, the network function  $f_\theta$  follows a linear differential equation in the infinite-width limit, and the eigenfunctions of the Jacobian are the kernel principal components of the input data. This shows a direct connection to kernel methods and motivates the use of early stopping to reduce overfitting in the training of ANNs.
- Finally we investigate these theoretical results numerically for an artificial dataset (of points on the unit circle) and for the MNIST dataset. In particular we observe that the behavior of wide ANNs is close to the theoretical limit.

## 2 Neural networks

In this article, we consider fully-connected ANNs with layers numbered from  $0$  (input) to  $L$  (output), each containing  $n_0, \ldots, n_L$  neurons, and with a Lipschitz, twice differentiable nonlinearity function  $\sigma : \mathbb{R} \to \mathbb{R}$ , with bounded second derivative <sup>[1](#page-1-0)</sup>.

This paper focuses on the ANN *realization function*  $F^{(L)} : \mathbb{R}^P \to \mathcal{F}$ , mapping parameters  $\theta$  to functions  $f_{\theta}$  in a space F. The dimension of the parameter space is  $P = \sum_{\ell=0}^{L-1} (n_{\ell} + 1)n_{\ell+1}$ : the parameters consist of the connection matrices  $W^{(\ell)} \in \mathbb{R}^{n_{\ell} \times n_{\ell+1}}$  and bias vectors  $b^{(\ell)} \in \mathbb{R}^{n_{\ell+1}}$  for  $\ell = 0, ..., L - 1$ . In our setup, the parameters are initialized as iid Gaussians  $\mathcal{N}(0, 1)$ .

For a fixed distribution  $p^{in}$  on the input space  $\mathbb{R}^{n_0}$ , the function space F is defined as  ${f: \mathbb{R}^{n_0} \to \mathbb{R}^{n_L}}$ . On this space, we consider the seminorm  $|| \cdot ||_{p^{in}}$ , defined in terms of the bilinear form

$$
\langle f, g \rangle_{p^{in}} = \mathbb{E}_{x \sim p^{in}} \left[ f(x)^T g(x) \right].
$$

<span id="page-1-0"></span><sup>&</sup>lt;sup>1</sup>While these smoothness assumptions greatly simplify the proofs of our results, they do not seem to be strictly needed for the results to hold true.

In this paper, we assume that the input distribution  $p^{in}$  is the empirical distribution on a finite dataset  $x_1, ..., x_N$ , i.e the sum of Dirac measures  $\frac{1}{N} \sum_{i=0}^{N} \delta_{x_i}$ .

We define the network function by  $f_{\theta}(x) := \tilde{\alpha}^{(L)}(x;\theta)$ , where the functions  $\tilde{\alpha}^{(\ell)}(\cdot;\theta) : \mathbb{R}^{n_0} \to \mathbb{R}^{n_\ell}$ (called *preactivations*) and  $\alpha^{(\ell)}(\cdot;\theta): \mathbb{R}^{n_0} \to \mathbb{R}^{n_\ell}$  (called *activations*) are defined from the 0-th to the  $L$ -th layer by:

$$
\alpha^{(0)}(x;\theta) = x
$$
  

$$
\tilde{\alpha}^{(\ell+1)}(x;\theta) = \frac{1}{\sqrt{n_{\ell}}} W^{(\ell)} \alpha^{(\ell)}(x;\theta) + \beta b^{(\ell)}
$$
  

$$
\alpha^{(\ell)}(x;\theta) = \sigma(\tilde{\alpha}^{(\ell)}(x;\theta)),
$$

where the nonlinearity  $\sigma$  is applied entrywise. The scalar  $\beta > 0$  is a parameter which allows us to tune the influence of the bias on the training.

<span id="page-2-1"></span>**Remark 1.** Our definition of the realization function  $F^{(L)}$  slightly differs from the classical one. Usually, the factors  $\frac{1}{\sqrt{n_\ell}}$  and the parameter  $\beta$  are absent and the parameters are initialized using *what is sometimes called LeCun initialization, taking*  $W_{ij}^{(\ell)} \sim \mathcal{N}(0, \frac{1}{n_\ell})$  and  $b_j^{(\ell)} \sim \mathcal{N}(0, 1)$  *(or* sometimes  $b_j^{(\ell)} = 0$ ) to compensate. While the set of representable functions  $F^{(L)}(\mathbb{R}^P)$  is the same for both parametrizations (with or without the factors  $\frac{1}{\sqrt{n_\ell}}$  and  $\beta$ ), the derivatives of the realization *function with respect to the connections*  $\partial_{W_{ij}^{(\ell)}} F^{(L)}$  and bias  $\partial_{b_j^{(\ell)}} F^{(L)}$  are scaled by  $\frac{1}{\sqrt{n_\ell}}$  and  $\beta$ *respectively in comparison to the classical parametrization.*

The factors  $\frac{1}{\sqrt{n_\ell}}$  are key to obtaining a consistent asymptotic behavior of neural networks as the *widths of the hidden layers*  $n_1, ..., n_{L-1}$  *grow to infinity. However a side-effect of these factors is that they reduce greatly the influence of the connection weights during training when*  $n_\ell$  *is large: the factor* β *is introduced to balance the influence of the bias and connection weights. In our numerical experiments, we take*  $\beta = 0.1$  *and use a learning rate of* 1.0*, which is larger than usual, see Section* [6.](#page-6-0) *This gives a behaviour similar to that of a classical network of width* 100 *with a learning rate of* 0.01*.*

<span id="page-2-0"></span>

## 3 Kernel gradient

The training of an ANN consists in optimizing  $f_\theta$  in the function space  $\mathcal F$  with respect to a functional cost  $C : \mathcal{F} \to \mathbb{R}$ , such as a regression or cross-entropy cost. Even for a convex functional cost C, the composite cost  $C \circ F^{(L)} : \mathbb{R}^P \to \mathbb{R}$  is in general highly non-convex [\(3\)](#page-9-8). We will show that during training, the network function  $f_\theta$  follows a descent along the kernel gradient with respect to the Neural Tangent Kernel (NTK) which we introduce in Section [4.](#page-4-0) This makes it possible to study the training of ANNs in the function space  $\mathcal F$ , on which the cost  $C$  is convex.

A *multi-dimensional kernel* K is a function  $\mathbb{R}^{n_0} \times \mathbb{R}^{n_0} \to \mathbb{R}^{n_L \times n_L}$ , which maps any pair  $(x, x')$  to an  $n_L \times n_L$ -matrix such that  $K(x, x') = K(x', x)^T$  (equivalently K is a symmetric tensor in  $\mathcal{F} \otimes \mathcal{F}$ ). Such a kernel defines a bilinear map on F, taking the expectation over independent  $x, x' \sim p^{in}$ .

$$
\langle f, g \rangle_K := \mathbb{E}_{x, x' \sim p^{in}} \left[ f(x)^T K(x, x') g(x') \right].
$$

The kernel K is *positive definite with respect to*  $|| \cdot ||_{p^{in}}$  if  $||f||_{p^{in}} > 0 \implies ||f||_K > 0$ .

We denote by  $\mathcal{F}^*$  the dual of  $\mathcal F$  with respect to  $p^{in}$ , i.e. the set of linear forms  $\mu : \mathcal F \to \mathbb R$  of the form  $\mu = \langle d, \cdot \rangle_{p^{in}}$  for some  $d \in \mathcal{F}$ . Two elements of F define the same linear form if and only if they are equal on the data. The constructions in the paper do not depend on the element  $d \in \mathcal{F}$  chosen in order to represent  $\mu$  as  $\langle d, \cdot \rangle_{p^{in}}$ . Using the fact that the partial application of the kernel  $K_{i, \cdot}(x, \cdot)$  is a function in F, we can define a map  $\Phi_K : \mathcal{F}^* \to \mathcal{F}$  mapping a dual element  $\mu = \langle d, \cdot \rangle_{p^{in}}$  to the function  $f_{\mu} = \Phi_K(\mu)$  with values:

$$
f_{\mu,i}(x) = \mu K_{i,\cdot}(x,\cdot) = \langle d, K_{i,\cdot}(x,\cdot) \rangle_{p^{in}}.
$$

For our setup, which is that of a finite dataset  $x_1, \ldots, x_n \in \mathbb{R}^{n_0}$ , the cost functional C only depends on the values of  $f \in \mathcal{F}$  at the data points. As a result, the (functional) derivative of the cost C at a point  $f_0 \in \mathcal{F}$  can be viewed as an element of  $\mathcal{F}^*$ , which we write  $\partial_f^{in} C|_{f_0}$ . We denote by  $d|_{f_0} \in \mathcal{F}$ , a corresponding dual element, such that  $\partial_f^{in} C|_{f_0} = \langle d|_{f_0}, \cdot \rangle_{p^{in}}$ .

The *kernel gradient*  $\nabla_K C|_{f_0} \in \mathcal{F}$  is defined as  $\Phi_K(\partial_f^{in} C|_{f_0})$ . In contrast to  $\partial_f^{in} C$  which is only defined on the dataset, the kernel gradient generalizes to values  $x$  outside the dataset thanks to the kernel K:

$$
\nabla_K C|_{f_0}(x) = \frac{1}{N} \sum_{j=1}^N K(x, x_j) d|_{f_0}(x_j).
$$

A time-dependent function  $f(t)$  follows the *kernel gradient descent with respect to* K if it satisfies the differential equation

 $\partial_t f(t) = -\nabla_K C|_{f(t)}.$ 

During kernel gradient descent, the cost  $C(f(t))$  evolves as

$$
\partial_t C|_{f(t)} = -\langle d|_{f(t)}, \nabla_K C|_{f(t)} \rangle_{p^{in}} = - ||d|_{f(t)}||_K^2.
$$

Convergence to a critical point of  $C$  is hence guaranteed if the kernel  $K$  is positive definite with respect to  $|| \cdot ||_{p^{in}}$ : the cost is then strictly decreasing except at points such that  $||d|_{f(t)}||_{p^{in}} = 0$ . If the cost is convex and bounded from below, the function  $f(t)$  therefore converges to a global minimum as  $t \to \infty$ .

<span id="page-3-0"></span>

### 3.1 Random functions approximation

As a starting point to understand the convergence of ANN gradient descent to kernel gradient descent in the infinite-width limit, we introduce a simple model, inspired by the approach of [\(19\)](#page-10-4).

A kernel K can be approximated by a choice of P random functions  $f^{(p)}$  sampled independently from any distribution on  $\mathcal F$  whose (non-centered) covariance is given by the kernel  $K$ :

$$
\mathbb{E}[f_k^{(p)}(x)f_{k'}^{(p)}(x')] = K_{kk'}(x, x').
$$

These functions define a random linear parametrization  $F^{lin}: \mathbb{R}^P \to \mathcal{F}$ 

$$
\theta \mapsto f_{\theta}^{lin} = \frac{1}{\sqrt{P}} \sum_{p=1}^{P} \theta_p f^{(p)}.
$$

The partial derivatives of the parametrization are given by

$$
\partial_{\theta_p} F^{lin}(\theta) = \frac{1}{\sqrt{P}} f^{(p)}.
$$

Optimizing the cost  $C \circ F^{lin}$  through gradient descent, the parameters follow the ODE:

$$
\partial_t \theta_p(t) = -\partial_{\theta_p}(C \circ F^{lin})(\theta(t)) = -\frac{1}{\sqrt{P}} \partial_f^{in} C|_{f_{\theta(t)}^{lin}} f^{(p)} = -\frac{1}{\sqrt{P}} \left\langle d|_{f_{\theta(t)}^{lin}}, f^{(p)} \right\rangle_{p^{in}}.
$$

As a result the function  $f_{\theta(t)}^{lin}$  evolves according to

$$
\partial_t f_{\theta(t)}^{lin} = \frac{1}{\sqrt{P}} \sum_{p=1}^P \partial_t \theta_p(t) f^{(p)} = -\frac{1}{P} \sum_{p=1}^P \left\langle d \big|_{f_{\theta(t)}^{lin}} f^{(p)} \right\rangle_{p^{in}} f^{(p)},
$$

where the right-hand side is equal to the kernel gradient  $-\nabla_{\tilde{K}} C$  with respect to the *tangent kernel* 

$$
\tilde{K} = \sum_{p=1}^{P} \partial_{\theta_p} F^{lin}(\theta) \otimes \partial_{\theta_p} F^{lin}(\theta) = \frac{1}{P} \sum_{p=1}^{P} f^{(p)} \otimes f^{(p)}.
$$

This is a random  $n_L$ -dimensional kernel with values  $\tilde{K}_{ii'}(x, x') = \frac{1}{P} \sum_{p=1}^{P} f_i^{(p)}(x) f_{i'}^{(p)}$  $i^{(p)}(x').$ 

Performing gradient descent on the cost  $C \circ F^{lin}$  is therefore equivalent to performing kernel gradient descent with the tangent kernel  $\tilde{K}$  in the function space. In the limit as  $P \to \infty$ , by the law of large numbers, the (random) tangent kernel  $\tilde{K}$  tends to the fixed kernel K, which makes this method an approximation of kernel gradient descent with respect to the limiting kernel K.

<span id="page-4-0"></span>

## 4 Neural tangent kernel

For ANNs trained using gradient descent on the composition  $C \circ F^{(L)}$ , the situation is very similar to that studied in the Section [3.1.](#page-3-0) During training, the network function  $f_\theta$  evolves along the (negative) kernel gradient

$$
\partial_t f_{\theta(t)} = -\nabla_{\Theta^{(L)}} C|_{f_{\theta(t)}}
$$

with respect to the *neural tangent kernel* (NTK)

$$
\Theta^{(L)}(\theta) = \sum_{p=1}^P \partial_{\theta_p} F^{(L)}(\theta) \otimes \partial_{\theta_p} F^{(L)}(\theta).
$$

However, in contrast to  $F^{lin}$ , the realization function  $F^{(L)}$  of ANNs is not linear. As a consequence, the derivatives  $\partial_{\theta_p} F^{(L)}(\theta)$  and the neural tangent kernel depend on the parameters  $\theta$ . The NTK is therefore random at initialization and varies during training, which makes the analysis of the convergence of  $f_\theta$  more delicate.

In the next subsections, we show that, in the infinite-width limit, the NTK becomes deterministic at initialization and stays constant during training. Since  $f_\theta$  at initialization is Gaussian in the limit, the asymptotic behavior of  $f_\theta$  during training can be explicited in the function space  $\mathcal{F}$ .

### 4.1 Initialization

As observed in [\(16;](#page-9-0) [4;](#page-9-1) [7;](#page-9-2) [13;](#page-9-3) [6\)](#page-9-4), the output functions  $f_{\theta,i}$  for  $i = 1, ..., n_L$  tend to iid Gaussian processes in the infinite-width limit (a proof in our setup is given in the appendix):

<span id="page-4-1"></span>Proposition 1. *For a network of depth* L *at initialization, with a Lipschitz nonlinearity* σ*, and in the limit as*  $n_1, ..., n_{L-1} \to \infty$ , the output functions  $f_{\theta,k}$ , for  $k = 1, ..., n_L$ , tend (in law) to iid centered *Gaussian processes of covariance*  $\Sigma^{(L)}$ , where  $\Sigma^{(L)}$  is defined recursively by:

$$
\Sigma^{(1)}(x, x') = \frac{1}{n_0} x^T x' + \beta^2
$$
  
$$
\Sigma^{(L+1)}(x, x') = \mathbb{E}_{f \sim \mathcal{N}(0, \Sigma^{(L)})} [\sigma(f(x))\sigma(f(x'))] + \beta^2,
$$

taking the expectation with respect to a centered Gaussian process  $f$  of covariance  $\Sigma^{(L)}.$ 

**Remark 2.** Strictly speaking, the existence of a suitable Gaussian measure with covariance  $\Sigma^{(L)}$  is not needed: we only deal with the values of  $f$  at  $x, x'$  (the joint measure on  $f(x), f(x')$  is simply a *Gaussian vector in 2D). For the same reasons, in the proof of Proposition [1](#page-4-1) and Theorem [1,](#page-4-2) we will freely speak of Gaussian processes without discussing their existence.*

The first key result of our paper (proven in the appendix) is the following: in the same limit, the Neural Tangent Kernel (NTK) converges in probability to an explicit deterministic limit.

<span id="page-4-2"></span>Theorem 1. *For a network of depth* L *at initialization, with a Lipschitz nonlinearity* σ*, and in the*  $l$ imit as the layers width  $n_1,...,n_{L-1}\to\infty$ , the NTK  $\Theta^{(L)}$  converges in probability to a deterministic *limiting kernel:*

$$
\Theta^{(L)} \to \Theta^{(L)}_{\infty} \otimes Id_{n_L}.
$$

The scalar kernel  $\Theta^{(L)}_\infty : \mathbb{R}^{n_0} \times \mathbb{R}^{n_0} \to \mathbb{R}$  is defined recursively by

$$
\Theta_{\infty}^{(1)}(x, x') = \Sigma^{(1)}(x, x')
$$
  
\n
$$
\Theta_{\infty}^{(L+1)}(x, x') = \Theta_{\infty}^{(L)}(x, x')\dot{\Sigma}^{(L+1)}(x, x') + \Sigma^{(L+1)}(x, x'),
$$

*where*

$$
\dot{\Sigma}^{(L+1)}(x, x') = \mathbb{E}_{f \sim \mathcal{N}(0, \Sigma^{(L)})} [\dot{\sigma}(f(x)) \dot{\sigma}(f(x'))],
$$

taking the expectation with respect to a centered Gaussian process  $f$  of covariance  $\Sigma^{(L)}$ , and where σ˙ *denotes the derivative of* σ*.*

Remark 3. *By Rademacher's theorem,* σ˙ *is defined everywhere, except perhaps on a set of zero Lebesgue measure.*

Note that the limiting  $\Theta_{\infty}^{(L)}$  only depends on the choice of  $\sigma$ , the depth of the network and the variance of the parameters at initialization (which is equal to 1 in our setting).

### 4.2 Training

Our second key result is that the NTK stays asymptotically constant during training. This applies for a slightly more general definition of training: the parameters are updated according to a training direction  $d_t \in \mathcal{F}$ :

$$
\partial_t \theta_p(t) = \left\langle \partial_{\theta_p} F^{(L)}(\theta(t)), d_t \right\rangle_{p^{in}}.
$$

In the case of gradient descent,  $d_t = -d|_{f_{\theta(t)}}$  (see Section [3\)](#page-2-0), but the direction may depend on another network, as is the case for e.g. Generative Adversarial Networks [\(10\)](#page-9-13). We only assume that the integral  $\int_0^T ||d_t||_{p^{in}} dt$  stays stochastically bounded as the width tends to infinity, which is verified for e.g. least-squares regression, see Section [5.](#page-5-0)

<span id="page-5-1"></span>Theorem 2. *Assume that* σ *is a Lipschitz, twice differentiable nonlinearity function, with bounded* second derivative. For any  $T$  such that the integral  $\int_0^T \| d_t \|_{p^{in}} dt$  stays stochastically bounded, as  $n_1, ..., n_{L-1}$  → ∞*, we have, uniformly for*  $t \in [0, T]$ *,* 

$$
\Theta^{(L)}(t) \to \Theta^{(L)}_{\infty} \otimes Id_{n_L}.
$$

*As a consequence, in this limit, the dynamics of*  $f_\theta$  *is described by the differential equation* 

$$
\partial_t f_{\theta(t)} = \Phi_{\Theta_{\infty}^{(L)} \otimes Id_{n_L}} \left( \langle d_t, \cdot \rangle_{p^{in}} \right).
$$

Remark 4. *As the proof of the theorem (in the appendix) shows, the variation during training of the individual activations in the hidden layers shrinks as their width grows. However their collective variation is significant, which allows the parameters of the lower layers to learn: in the formula of* the limiting NTK  $\Theta_{\infty}^{(L+1)}(x,x')$  in Theorem [1,](#page-4-2) the second summand  $\Sigma^{(L+1)}$  represents the learning *due to the last layer, while the first summand represents the learning performed by the lower layers.*

As discussed in Section [3,](#page-2-0) the convergence of kernel gradient descent to a critical point of the cost  $C$  is guaranteed for positive definite kernels. The limiting NTK is positive definite if the span of the derivatives  $\partial_{\theta_p} F^{(L)}$ ,  $p = 1, ..., P$  becomes dense in F w.r.t. the  $p^{in}$ -norm as the width grows to infinity. It seems natural to postulate that the span of the preactivations of the last layer (which themselves appear in  $\partial_{\theta_p} F^{(L)}$ , corresponding to the connection weights of the last layer) becomes dense in F, for a large family of measures  $p^{in}$  and nonlinearities (see e.g. [\(11;](#page-9-5) [14\)](#page-9-6) for classical theorems about ANNs and approximation). In the case when the dataset is supported on a sphere, the positive-definiteness of the limiting NTK can be shown using Gaussian integration techniques and existing positive-definiteness criteria, as given by the following proposition, proven in Appendix [A.4:](#page-16-0)

<span id="page-5-2"></span>**Proposition 2.** *For a non-polynomial Lipschitz nonlinearity*  $\sigma$ , *for any input dimension*  $n_0$ , *the restriction of the limiting NTK*  $\Theta_{\infty}^{(L)}$  *to the unit sphere*  $\mathbb{S}^{n_0-1} = \{x \in \mathbb{R}^{n_0} : x^T x = 1\}$  *is positivedefinite if*  $L > 2$ *.* 

## <span id="page-5-0"></span>5 Least-squares regression

Given a goal function  $f^*$  and input distribution  $p^{in}$ , the least-squares regression cost is

$$
C(f) = \frac{1}{2} ||f - f^*||_{p^{in}}^2 = \frac{1}{2} \mathbb{E}_{x \sim p^{in}} [||f(x) - f^*(x)||^2].
$$

Theorems [1](#page-4-2) and [2](#page-5-1) apply to an ANN trained on such a cost. Indeed the norm of the training direction  $||d(f)||_{p^{in}} = ||f^* - f||_{p^{in}}$  is strictly decreasing during training, bounding the integral. We are therefore interested in the behavior of a function  $f_t$  during kernel gradient descent with a kernel K (we are of course especially interested in the case  $K = \Theta_{\infty}^{(L)} \otimes Id_{n_L}$ ):

$$
\partial_t f_t = \Phi_K \left( \langle f^* - f, \cdot \rangle_{p^{in}} \right).
$$

The solution of this differential equation can be expressed in terms of the map  $\Pi : f \mapsto$  $\Phi_K\left(\left\langle f, \cdot \right\rangle_{p^{in}}\right)$ :

$$
f_t = f^* + e^{-t\Pi}(f_0 - f^*)
$$

where  $e^{-t\Pi} = \sum_{k=0}^{\infty} \frac{(-t)^k}{k!} \Pi^k$  is the exponential of  $-t\Pi$ . If  $\Pi$  can be diagonalized by eigenfunctions  $f^{(i)}$  with eigenvalues  $\lambda_i$ , the exponential  $e^{-t\Pi}$  has the same eigenfunctions with eigenvalues  $e^{-t\lambda_i}$ .

For a finite dataset  $x_1, ..., x_N$  of size N, the map  $\Pi$  takes the form

$$
\Pi(f)_k(x) = \frac{1}{N} \sum_{i=1}^N \sum_{k'=1}^{n_L} f_{k'}(x_i) K_{kk'}(x_i, x).
$$

The map  $\Pi$  has at most  $Nn<sub>L</sub>$  positive eigenfunctions, and they are the kernel principal components  $f^{(1)},..., f^{(Nn_L)}$  of the data with respect to to the kernel  $K$  [\(21;](#page-10-5) [22\)](#page-10-6). The corresponding eigenvalues  $\lambda_i$  is the variance captured by the component.

Decomposing the difference  $(f^* - f_0) = \Delta_f^0 + \Delta_f^1 + ... + \Delta_f^{N_{n_L}}$  along the eigenspaces of  $\Pi$ , the trajectory of the function  $f_t$  reads

$$
f_t = f^* + \Delta_f^0 + \sum_{i=1}^{Nn_L} e^{-t\lambda_i} \Delta_f^i,
$$

where  $\Delta_f^0$  is in the kernel (null-space) of  $\Pi$  and  $\Delta_f^i \propto f^{(i)}$ .

The above decomposition can be seen as a motivation for the use of early stopping. The convergence is indeed faster along the eigenspaces corresponding to larger eigenvalues  $\lambda_i$ . Early stopping hence focuses the convergence on the most relevant kernel principal components, while avoiding to fit the ones in eigenspaces with lower eigenvalues (such directions are typically the 'noisier' ones: for instance, in the case of the RBF kernel, lower eigenvalues correspond to high frequency functions).

Note that by the linearity of the map  $e^{-t\Pi}$ , if  $f_0$  is initialized with a Gaussian distribution (as is the case for ANNs in the infinite-width limit), then  $f_t$  is Gaussian for all times t. Assuming that the kernel is positive definite on the data (implying that the  $Nn_L \times Nn_L$  Gram marix  $\tilde{K} = (K_{kk'}(x_i, x_j))_{ik,jk'}$ is invertible), as  $t \to \infty$  limit, we get that  $f_{\infty} = f^* + \Delta_f^0 = f_0 - \sum_i \Delta_f^i$  takes the form

$$
f_{\infty,k}(x) = \kappa_{x,k}^T \tilde{K}^{-1} y^* + \left( f_0(x) - \kappa_{x,k}^T \tilde{K}^{-1} y_0 \right),
$$

with the  $Nn_l$ -vectors  $\kappa_{x,k}$ ,  $y^*$  and  $y_0$  given by

$$
\kappa_{x,k} = (K_{kk'}(x, x_i))_{i,k'}
$$
  
$$
y^* = (f_k^*(x_i))_{i,k}
$$
  
$$
y_0 = (f_{0,k}(x_i))_{i,k}.
$$

The first term, the mean, has an important statistical interpretation: it is the maximum-a-posteriori (MAP) estimate given a Gaussian prior on functions  $f_k \sim \mathcal{N}(0, \Theta_{\infty}^{(L)})$  and the conditions  $f_k(x_i)$  =  $f_k^*(x_i)$ . Equivalently, it is equal to the kernel ridge regression [\(22\)](#page-10-6) as the regularization goes to zero ( $\lambda \to 0$ ). The second term is a centered Gaussian whose variance vanishes on the points of the dataset.

<span id="page-6-0"></span>

## 6 Numerical experiments

In the following numerical experiments, fully connected ANNs of various widths are compared to the theoretical infinite-width limit. We choose the size of the hidden layers to all be equal to the same value  $n := n_1 = ... = n_{L-1}$  and we take the ReLU nonlinearity  $\sigma(x) = \max(0, x)$ .

In the first two experiments, we consider the case  $n_0 = 2$ . Moreover, the input elements are taken on the unit circle. This can be motivated by the structure of high-dimensional data, where the centered data points often have roughly the same norm<sup>[2](#page-6-1)</sup>.

In all experiments, we took  $n<sub>L</sub> = 1$  (note that by our results, a network with  $n<sub>L</sub>$  outputs behaves asymptotically like  $n<sub>L</sub>$  networks with scalar outputs trained independently). Finally, the value of the parameter  $\beta$  is chosen as 0.1, see Remark [1.](#page-2-1)

<span id="page-6-1"></span><sup>&</sup>lt;sup>2</sup>The classical example is for data following a Gaussian distribution  $\mathcal{N}(0, Id_{n_0})$ : as the dimension  $n_0$  grows, The classical example is for data following a Gaussian all data points have approximately the same norm  $\sqrt{n_0}$ .

<span id="page-7-0"></span>Image /page/7/Figure/0 description: The image displays two plots side-by-side. The left plot shows several curves representing data points with 'y' on the x-axis ranging from -3 to 3, and values on the y-axis ranging from 0.05 to 0.40. The curves are colored green and red, with some solid and some dotted lines, labeled with different values of 'n' and 't'. The right plot also shows several curves with 'y' on the x-axis ranging from -3 to 3, and values on the y-axis ranging from -0.4 to 0.4. The y-axis is labeled as 'f(sin(y), cos(y))'. The curves are colored green, red, and blue, with dotted, solid, and dashed lines, labeled with different values of 'n' and percentiles.

for two widths  $n$  and two times  $t$ .

Figure 1: Convergence of the NTK to a fixed limit Figure 2: Networks function  $f_{\theta}$  near convergence for two widths  $n$  and 10th, 50th and 90th percentiles of the asymptotic Gaussian distribution.

### 6.1 Convergence of the NTK

The first experiment illustrates the convergence of the NTK  $\Theta^{(L)}$  of a network of depth  $L = 4$  for two different widths  $n = 500, 10000$ . The function  $\Theta^{(4)}(x_0, x)$  is plotted for a fixed  $x_0 = (1, 0)$ and  $x = (\cos(\gamma), \sin(\gamma))$  on the unit circle in Figure [1.](#page-7-0) To observe the distribution of the NTK, 10 independent initializations are performed for both widths. The kernels are plotted at initialization  $t = 0$  and then after 200 steps of gradient descent with learning rate 1.0 (i.e. at  $t = 200$ ). We approximate the function  $f^*(x) = x_1x_2$  with a least-squares cost on random  $\mathcal{N}(0, 1)$  inputs.

For the wider network, the NTK shows less variance and is smoother. It is interesting to note that the expectation of the NTK is very close for both networks widths. After 200 steps of training, we observe that the NTK tends to "inflate". As expected, this effect is much less apparent for the wider network ( $n = 10000$ ) where the NTK stays almost fixed, than for the smaller network ( $n = 500$ ).

### 6.2 Kernel regression

For a regression cost, the infinite-width limit network function  $f_{\theta(t)}$  has a Gaussian distribution for all times t and in particular at convergence  $t \to \infty$  (see Section [5\)](#page-5-0). We compared the theoretical Gaussian distribution at  $t \to \infty$  to the distribution of the network function  $f_{\theta(T)}$  of a finite-width network for a large time  $T = 1000$ . For two different widths  $n = 50,1000$  and for 10 random initializations each, a network is trained on a least-squares cost on 4 points of the unit circle for 1000 steps with learning rate 1.0 and then plotted in Figure [2.](#page-7-0)

We also approximated the kernels  $\Theta_{\infty}^{(4)}$  and  $\Sigma^{(4)}$  using a large-width network ( $n = 10000$ ) and used them to calculate and plot the 10th, 50th and 90-th percentiles of the  $t \to \infty$  limiting Gaussian distribution.

The distributions of the network functions are very similar for both widths: their mean and variance appear to be close to those of the limiting distribution  $t \to \infty$ . Even for relatively small widths  $(n = 50)$ , the NTK gives a good indication of the distribution of  $f_{\theta(t)}$  as  $t \to \infty$ .

### 6.3 Convergence along a principal component

We now illustrate our result on the MNIST dataset of handwritten digits made up of grayscale images of dimension  $28 \times 28$ , yielding a dimension of  $n_0 = 784$ .

We computed the first 3 principal components of a batch of  $N = 512$  digits with respect to the NTK of a high-width network  $n = 10000$  (giving an approximation of the limiting kernel) using a power iteration method. The respective eigenvalues are  $\lambda_1 = 0.0457$ ,  $\lambda_2 = 0.00108$  and  $\lambda_3 = 0.00078$ . The kernel PCA is non-centered, the first component is therefore almost equal to the constant function,

<span id="page-8-1"></span>Image /page/8/Figure/0 description: The image displays three plots. The leftmost plot is a scatter plot with f^(2)(x) on the x-axis ranging from -3 to 2 and f^(3)(x) on the y-axis ranging from -2 to 2. The scatter plot contains numerous data points, many of which are labeled with numbers such as 0, 2, 3, 4, 5, 6, 7, 8, and 9. The middle plot is a line graph with 't' on the x-axis ranging from 0 to 4000 and ||ht||p^in on the y-axis ranging from 0.00 to 0.14. This plot shows three dotted lines in green, red, and blue, representing n=100, n=1000, and n=10000 respectively. All three lines initially increase, peak, and then decrease. The rightmost plot is also a line graph with 't' on the x-axis ranging from 0 to 4000 and ||gt||p^in on the y-axis ranging from 0.0 to 0.5. This plot shows four lines: three dotted lines in green, red, and blue representing n=100, n=1000, and n=10000, and a solid blue line representing n=infinity. All lines show a decreasing trend over time, with the n=infinity line decreasing the slowest and the n=100 line decreasing the fastest.

(a) The 2nd and 3rd principal (b) Deviation of the network function (c) Convergence of  $f_\theta$  along the 2nd components of MNIST.  $f_{\theta}$  from the straight line. principal component.

Figure 3

which explains the large gap between the first and second eigenvalues<sup>[3](#page-8-0)</sup>. The next two components are much more interesting as can be seen in Figure [3a,](#page-8-1) where the batch is plotted with x and y coordinates corresponding to the 2nd and 3rd components.

We have seen in Section [5](#page-5-0) how the convergence of kernel gradient descent follows the kernel principal components. If the difference at initialization  $f_0 - f^*$  is equal (or proportional) to one of the principal components  $f^{(i)}$ , then the function will converge along a straight line (in the function space) to  $f^*$  at an exponential rate  $e^{-\lambda_i t}$ .

We tested whether ANNs of various widths  $n = 100, 1000, 10000$  behave in a similar manner. We set the goal of the regression cost to  $f^* = f_{\theta(0)} + 0.5f^{(2)}$  and let the network converge. At each time step t, we decomposed the difference  $f_{\theta(t)} - f^*$  into a component  $g_t$  proportional to  $f^{(2)}$  and another one  $h_t$  orthogonal to  $f^{(2)}$ . In the infinite-width limit, the first component decays exponentially fast  $||g_t||_{p^{in}} = 0.5e^{-\lambda_2 t}$  while the second is null  $(h_t = 0)$ , as the function converges along a straight line.

As expected, we see in Figure [3b](#page-8-1) that the wider the network, the less it deviates from the straight line (for each width  $n$  we performed two independent trials). As the width grows, the trajectory along the 2nd principal component (shown in Figure [3c\)](#page-8-1) converges to the theoretical limit shown in blue.

A surprising observation is that smaller networks appear to converge faster than wider ones. This may be explained by the inflation of the NTK observed in our first experiment. Indeed, multiplying the NTK by a factor  $\alpha$  is equivalent to multiplying the learning rate by the same factor. However, note that since the NTK of large-width network is more stable during training, larger learning rates can in principle be taken. One must hence be careful when comparing the convergence speed in terms of the number of steps (rather than in terms of the time  $t$ ): both the inflation effect and the learning rate must be taken into account.

## 7 Conclusion

This paper introduces a new tool to study ANNs, the Neural Tangent Kernel (NTK), which describes the local dynamics of an ANN during gradient descent. This leads to a new connection between ANN training and kernel methods: in the infinite-width limit, an ANN can be described in the function space directly by the limit of the NTK, an explicit constant kernel  $\Theta_{\infty}^{(L)}$ , which only depends on its depth, nonlinearity and parameter initialization variance. More precisely, in this limit, ANN gradient descent is shown to be equivalent to a kernel gradient descent with respect to  $\Theta_{\infty}^{(L)}$ . The limit of the NTK is hence a powerful tool to understand the generalization properties of ANNs, and it allows one to study the influence of the depth and nonlinearity on the learning abilities of the network. The analysis of training using NTK allows one to relate convergence of ANN training with the positive-definiteness of the limiting NTK and leads to a characterization of the directions favored by early stopping methods.

<span id="page-8-0"></span><sup>&</sup>lt;sup>3</sup>It can be observed numerically, that if we choose  $\beta = 1.0$  instead of our recommended 0.1, the gap between the first and the second principal component is about ten times bigger, which makes training more difficult.

# Acknowledgements

The authors thank K. Kytölä for many interesting discussions. The second author was supported by the ERC CG CRITICAL. The last author acknowledges support from the ERC SG Constamis, the NCCR SwissMAP, the Blavatnik Family Foundation and the Latsis Foundation.

# **References**

- <span id="page-9-11"></span>[1] M. Belkin, S. Ma, and S. Mandal. To understand deep learning we need to understand kernel learning. *arXiv preprint*, Feb 2018.
- <span id="page-9-12"></span>[2] Y. Cho and L. K. Saul. Kernel methods for deep learning. In *Advances in Neural Information Processing Systems 22*, pages 342–350. Curran Associates, Inc., 2009.
- <span id="page-9-8"></span>[3] A. Choromanska, M. Henaff, M. Mathieu, G. B. Arous, and Y. LeCun. The Loss Surfaces of Multilayer Networks. *Journal of Machine Learning Research*, 38:192–204, nov 2015.
- <span id="page-9-1"></span>[4] A. Daniely, R. Frostig, and Y. Singer. Toward deeper understanding of neural networks: The power of initialization and a dual view on expressivity. In D. D. Lee, M. Sugiyama, U. V. Luxburg, I. Guyon, and R. Garnett, editors, *Advances in Neural Information Processing Systems 29*, pages 2253–2261. Curran Associates, Inc., 2016.
- <span id="page-9-7"></span>[5] Y. N. Dauphin, R. Pascanu, C. Gulcehre, K. Cho, S. Ganguli, and Y. Bengio. Identifying and attacking the saddle point problem in high-dimensional non-convex optimization. In *Proceedings of the 27th International Conference on Neural Information Processing Systems - Volume 2*, NIPS'14, pages 2933–2941, Cambridge, MA, USA, 2014. MIT Press.
- <span id="page-9-4"></span>[6] A. G. de G. Matthews, J. Hron, M. Rowland, R. E. Turner, and Z. Ghahramani. Gaussian process behaviour in wide deep neural networks. In *International Conference on Learning Representations*, 2018.
- <span id="page-9-2"></span>[7] A. G. de G. Matthews, J. Hron, R. E. Turner, and Z. Ghahramani. Sample-then-optimize posterior sampling for bayesian linear models. In *NIPS workshop on Advances in Approximate Bayesian Inference*, 2017.
- <span id="page-9-14"></span>[8] S. S. Dragomir. *Some Gronwall Type Inequalities and Applications*. Nova Science Publishers, 2003.
- <span id="page-9-15"></span>[9] T. Gneiting. Strictly and non-strictly positive definite functions on spheres. *Bernoulli*, 19(4):1327–1349, 2013.
- <span id="page-9-13"></span>[10] I. J. Goodfellow, J. Pouget-Abadie, M. Mirza, B. Xu, D. Warde-Farley, S. Ozair, A. Courville, and Y. Bengio. Generative Adversarial Networks. *NIPS'14 Proceedings of the 27th International Conference on Neural Information Processing Systems - Volume 2*, pages 2672–2680, jun 2014.
- <span id="page-9-5"></span>[11] K. Hornik, M. Stinchcombe, and H. White. Multilayer feedforward networks are universal approximators. *Neural Networks*, 2(5):359 – 366, 1989.
- <span id="page-9-9"></span>[12] R. Karakida, S. Akaho, and S.-i. Amari. Universal Statistics of Fisher Information in Deep Neural Networks: Mean Field Approach. jun 2018.
- <span id="page-9-3"></span>[13] J. H. Lee, Y. Bahri, R. Novak, S. S. Schoenholz, J. Pennington, and J. Sohl-Dickstein. Deep neural networks as gaussian processes. *ICLR*, 2018.
- <span id="page-9-6"></span>[14] M. Leshno, V. Lin, A. Pinkus, and S. Schocken. Multilayer feedforward networks with a nonpolynomial activation function can approximate any function. *Neural Networks*, 6(6):861–867, 1993.
- <span id="page-9-10"></span>[15] S. Mei, A. Montanari, and P.-M. Nguyen. A mean field view of the landscape of two-layer neural networks. *Proceedings of the National Academy of Sciences*, 115(33):E7665–E7671, 2018.
- <span id="page-9-0"></span>[16] R. M. Neal. *Bayesian Learning for Neural Networks*. Springer-Verlag New York, Inc., Secaucus, NJ, USA, 1996.

- <span id="page-10-0"></span>[17] R. Pascanu, Y. N. Dauphin, S. Ganguli, and Y. Bengio. On the saddle point problem for non-convex optimization. *arXiv preprint*, 2014.
- <span id="page-10-1"></span>[18] J. Pennington and Y. Bahri. Geometry of neural network loss surfaces via random matrix theory. In *Proceedings of the 34th International Conference on Machine Learning*, volume 70 of *Proceedings of Machine Learning Research*, pages 2798–2806, International Convention Centre, Sydney, Australia, 06–11 Aug 2017. PMLR.
- <span id="page-10-4"></span>[19] A. Rahimi and B. Recht. Random features for large-scale kernel machines. In *Advances in Neural Information Processing Systems 20*, pages 1177–1184. Curran Associates, Inc., 2008.
- <span id="page-10-2"></span>[20] L. Sagun, U. Evci, V. U. Güney, Y. Dauphin, and L. Bottou. Empirical analysis of the hessian of over-parametrized neural networks. *CoRR*, abs/1706.04454, 2017.
- <span id="page-10-5"></span>[21] B. Schölkopf, A. Smola, and K.-R. Müller. Nonlinear component analysis as a kernel eigenvalue problem. *Neural Computation*, 10(5):1299–1319, 1998.
- <span id="page-10-6"></span>[22] J. Shawe-Taylor and N. Cristianini. *Kernel Methods for Pattern Analysis*. Cambridge University Press, New York, NY, USA, 2004.
- <span id="page-10-3"></span>[23] C. Zhang, S. Bengio, M. Hardt, B. Recht, and O. Vinyals. Understanding deep learning requires rethinking generalization. *ICLR 2017 proceedings*, Feb 2017.

<span id="page-10-7"></span>

## A Appendix

This appendix is dedicated to proving the key results of this paper, namely Proposition [1](#page-4-1) and Theorems [1](#page-4-2) and [2,](#page-5-1) which describe the asymptotics of neural networks at initialization and during training.

We study the limit of the NTK as  $n_1, ..., n_{L-1} \to \infty$  sequentially, i.e. we first take  $n_1 \to \infty$ , then  $n_2 \to \infty$ , etc. This leads to much simpler proofs, but our results could in principle be strengthened to the more general setting when  $\min(n_1, ..., n_{L-1}) \to \infty$ .

A natural choice of convergence to study the NTK is with respect to the operator norm on kernels:

$$
||K||_{op} = \max_{||f||_{p^{in}} \le 1} ||f||_{K} = \max_{||f||_{p^{in}} \le 1} \sqrt{\mathbb{E}_{x,x'}[f(x)^T K(x,x')f(x')]}.
$$

where the expectation is taken over two independent  $x, x' \sim p^{in}$ . This norm depends on the input distribution  $p^{in}$ . In our setting,  $p^{in}$  is taken to be the empirical measure of a finite dataset of distinct samples  $x_1, ..., x_N$ . As a result, the operator norm of K is equal to the leading eigenvalue of the  $Nn_L \times Nn_L$  Gram matrix  $(K_{kk'}(x_i, x_j))_{k,k' < n_L, i,j < N}$ . In our setting, convergence in operator norm is hence equivalent to pointwise convergence of  $K$  on the dataset.

### A.1 Asymptotics at Initialization

It has already been observed [\(16;](#page-9-0) [13\)](#page-9-3) that the output functions  $f_{\theta,i}$  for  $i = 1, ..., n_L$  tend to iid Gaussian processes in the infinite-width limit.

Proposition 1. *For a network of depth* L *at initialization, with a Lipschitz nonlinearity* σ*, and in the limit as*  $n_1, ..., n_{L-1} \to \infty$  *sequentially, the output functions*  $f_{\theta,k}$ *, for*  $k = 1, ..., n_L$ *, tend (in law) to* iid centered Gaussian processes of covariance  $\Sigma^{(L)}$ , where  $\Sigma^{(L)}$  is defined recursively by:

$$
\Sigma^{(1)}(x, x') = \frac{1}{n_0} x^T x' + \beta^2
$$
  
$$
\Sigma^{(L+1)}(x, x') = \mathbb{E}_f[\sigma(f(x))\sigma(f(x'))] + \beta^2,
$$

taking the expectation with respect to a centered Gaussian process  $f$  of covariance  $\Sigma^{(L)}.$ 

*Proof.* We prove the result by induction. When  $L = 1$ , there are no hidden layers and  $f_\theta$  is a random affine function of the form:

$$
f_{\theta}(x) = \frac{1}{\sqrt{n_0}} W^{(0)} x + \beta b^{(0)}.
$$

All output functions  $f_{\theta,k}$  are hence independent and have covariance  $\Sigma^{(1)}$  as needed.

The key to the induction step is to consider an  $(L + 1)$ -network as the following composition: an L-network  $\mathbb{R}^{n_0} \to \mathbb{R}^{n_L}$  mapping the input to the pre-activations  $\tilde{\alpha}_i^{(L)}$ , followed by an elementwise application of the nonlinearity  $\sigma$  and then a random affine map  $\mathbb{R}^{n_L} \to \mathbb{R}^{n_L+1}$ . The induction hypothesis gives that in the limit as sequentially  $n_1, ..., n_{L-1} \to \infty$  the preactivations  $\tilde{\alpha}_i^{(L)}$  tend to iid Gaussian processes with covariance  $\Sigma^{(L)}$ . The outputs

$$
f_{\theta,i} = \frac{1}{\sqrt{n_L}} W_i^{(L)} \alpha^{(L)} + \beta b_i^{(L)}
$$

conditioned on the values of  $\alpha^{(L)}$  are iid centered Gaussians with covariance

$$
\tilde{\Sigma}^{(L+1)}(x, x') = \frac{1}{n_L} \alpha^{(L)}(x; \theta)^T \alpha^{(L)}(x'; \theta) + \beta^2.
$$

By the law of large numbers, as  $n_L \rightarrow \infty$ , this covariance tends in probability to the expectation

$$
\tilde{\Sigma}^{(L+1)}(x,x') \to \Sigma^{(L+1)}(x,x') = \mathbb{E}_{f \sim \mathcal{N}(0,\Sigma^{(L)})}[\sigma(f(x))\sigma(f(x'))] + \beta^2.
$$

In particular the covariance is deterministic and hence independent of  $\alpha^{(L)}$ . As a consequence, the conditioned and unconditioned distributions of  $f_{\theta,i}$  are equal in the limit: they are iid centered Gaussian of covariance  $\Sigma^{(L+1)}$ .  $\Box$ 

In the infinite-width limit, the neural tangent kernel, which is random at initialization, converges in probability to a deterministic limit.

Theorem 1. *For a network of depth* L *at initialization, with a Lipschitz nonlinearity* σ*, and in the limit as the layers width*  $n_1,...,n_{L-1}\to\infty$  sequentially, the NTK  $\Theta^{(L)}$  converges in probability to a *deterministic limiting kernel:*

$$
\Theta^{(L)} \to \Theta^{(L)}_{\infty} \otimes Id_{n_L}.
$$

The scalar kernel  $\Theta^{(L)}_\infty : \mathbb{R}^{n_0} \times \mathbb{R}^{n_0} \to \mathbb{R}$  is defined recursively by

$$
\Theta_{\infty}^{(1)}(x, x') = \Sigma^{(1)}(x, x')
$$
  
\n
$$
\Theta_{\infty}^{(L+1)}(x, x') = \Theta_{\infty}^{(L)}(x, x')\dot{\Sigma}^{(L+1)}(x, x') + \Sigma^{(L+1)}(x, x'),
$$

*where*

$$
\dot{\Sigma}^{(L+1)}(x, x') = \mathbb{E}_{f \sim \mathcal{N}(0, \Sigma^{(L)})} [\dot{\sigma}(f(x)) \dot{\sigma}(f(x'))],
$$

taking the expectation with respect to a centered Gaussian process  $f$  of covariance  $\Sigma^{(L)}$ , and where σ˙ *denotes the derivative of* σ*.*

*Proof.* The proof is again by induction. When  $L = 1$ , there is no hidden layer and therefore no limit to be taken. The neural tangent kernel is a sum over the entries of  $W^{(0)}$  and those of  $b^{(0)}$ :

$$
\Theta_{kk'}(x, x') = \frac{1}{n_0} \sum_{i=1}^{n_0} \sum_{j=1}^{n_1} x_i x'_j \delta_{jk} \delta_{jk'} + \beta^2 \sum_{j=1}^{n_1} \delta_{jk} \delta_{jk'}
$$
$$
= \frac{1}{n_0} x^T x' \delta_{kk'} + \beta^2 \delta_{kk'} = \Sigma^{(1)}(x, x') \delta_{kk'}.
$$

Here again, the key to prove the induction step is the observation that a network of depth  $L + 1$  is an L-network mapping the inputs x to the preactivations of the L-th layer  $\tilde{\alpha}^{(L)}(x)$  followed by a nonlinearity and a random affine function. For a network of depth  $L + 1$ , let us therefore split the parameters into the parameters  $\tilde{\theta}$  of the first L layers and those of the last layer  $(W^{(L)}, b^{(L)})$ .

By Proposition [1](#page-4-1) and the induction hypothesis, as  $n_1, ..., n_{L-1} \to \infty$  the pre-activations  $\tilde{\alpha}_i^{(L)}$  are iid centered Gaussian with covariance  $\Sigma^{(L)}$  and the neural tangent kernel  $\Theta_{ii'}^{(L)}(x, x')$  of the smaller network converges to a deterministic limit:

$$
\left(\partial_{\tilde{\theta}}\tilde{\alpha}_i^{(L)}(x;\theta)\right)^T\partial_{\tilde{\theta}}\tilde{\alpha}_{i'}^{(L)}(x';\theta)\to\Theta_{\infty}^{(L)}(x,x')\delta_{ii'}.
$$

We can split the neural tangent network into a sum over the parameters  $\hat{\theta}$  of the first L layers and the remaining parameters  $W^{(L)}$  and  $b^{(L)}$ .

For the first sum let us observe that by the chain rule:

$$
\partial_{\tilde{\theta}_p} f_{\theta,k}(x) = \frac{1}{\sqrt{n_L}} \sum_{i=1}^{n_L} \partial_{\tilde{\theta}_p} \tilde{\alpha}_i^{(L)}(x; \theta) \dot{\sigma}(\tilde{\alpha}_i^{(L)}(x; \theta)) W_{ik}^{(L)}.
$$

By the induction hypothesis, the contribution of the parameters  $\tilde{\theta}$  to the neural tangent kernel  $\Theta_{kk'}^{(L+1)}(x, x')$  therefore converges as  $n_1, ..., n_{L-1} \to \infty$ :

$$
\frac{1}{n_L}\sum_{i,i'=1}^{n_L}\Theta^{(L)}_{ii'}(x,x')\sigma(\tilde{\alpha}_i^{(L)}(x;\theta))\sigma(\tilde{\alpha}_{i'}^{(L)}(x';\theta))W^{(L)}_{ik}W^{(L)}_{i'k'}
$$

$$
\to \frac{1}{n_L}\sum_{i=1}^{n_L}\Theta^{(L)}_\infty(x,x')\sigma(\tilde{\alpha}_i^{(L)}(x;\theta))\sigma(\tilde{\alpha}_i^{(L)}(x';\theta))W^{(L)}_{ik}W^{(L)}_{ik'}
$$

By the law of large numbers, as  $n_L \rightarrow \infty$ , this tends to its expectation which is equal to

$$
\Theta_{\infty}^{(L)}(x, x') \dot{\Sigma}^{(L+1)}(x, x') \delta_{kk'}.
$$

It is then easy to see that the second part of the neural tangent kernel, the sum over  $W^{(L)}$  and  $b^{(L)}$ converges to  $\Sigma^{(L+1)} \delta_{kk'}$  as  $n_1, ..., n_L \to \infty$ .  $\Box$ 

### A.2 Asymptotics during Training

Given a training direction  $t \mapsto d_t \in \mathcal{F}$ , a neural network is trained in the following manner: the parameters  $\theta_p$  are initialized as iid  $\mathcal{N}(0, 1)$  and follow the differential equation:

$$
\partial_t \theta_p(t) = \left\langle \partial_{\theta_p} F^{(L)}, d_t \right\rangle_{p^{in}}
$$

.

In this context, in the infinite-width limit, the NTK stays constant during training:

Theorem 2. *Assume that* σ *is a Lipschitz, twice differentiable nonlinearity function, with bounded* second derivative. For any  $T$  such that the integral  $\int_0^T \| d_t \|_{p^{in}} dt$  stays stochastically bounded, as  $n_1, ..., n_{L-1}$  →  $\infty$  *sequentially, we have, uniformly for*  $t \in [0, T]$ *,* 

$$
\Theta^{(L)}(t) \to \Theta^{(L)}_{\infty} \otimes Id_{n_L}.
$$

As a consequence, in this limit, the dynamics of  $f_\theta$  is described by the differential equation

$$
\partial_t f_{\theta(t)} = \Phi_{\Theta_{\infty}^{(L)} \otimes Id_{n_L}} \left( \langle d_t, \cdot \rangle_{p^{in}} \right).
$$

*Proof.* As in the previous theorem, the proof is by induction on the depth of the network. When  $L = 1$ , the neural tangent kernel does not depend on the parameters, it is therefore constant during training.

For the induction step, we again split an  $L + 1$  network into a network of depth L with parameters  $\tilde{\theta}$  and top layer connection weights  $W^{(L)}$  and bias  $b^{(L)}$ . The smaller network follows the training direction

$$
d'_{t} = \dot{\sigma} \left( \tilde{\alpha}^{(L)}(t) \right) \left( \frac{1}{\sqrt{n_L}} W^{(L)}(t) \right)^T d_t
$$

for  $i = 1, ..., n_L$ , where the function  $\tilde{\alpha}_i^{(L)}(t)$  is defined as  $\tilde{\alpha}_i^{(L)}(\cdot; \theta(t))$ . We now want to apply the induction hypothesis to the smaller network. For this, we need to show that  $\int_0^T ||d'_t||_{p^{in}} dt$  is stochastically bounded as  $n_1, \ldots, n_L \to \infty$ . Since  $\sigma$  is a c-Lipschitz function, we have that

$$
||d'_t||_{p^{in}} \leq c||\frac{1}{\sqrt{n_L}}W^{(L)}(t)||_{op}||d_t||_{p^{in}}.
$$

To apply the induction hypothesis, we now need to bound  $\|\frac{1}{\sqrt{n_L}}W^{(L)}(t)\|_{op}$ . For this, we use the following lemma, which is proven in Appendix [A.3](#page-14-0) below:

<span id="page-13-0"></span>**Lemma 1.** With the setting of Theorem [2,](#page-5-1) for a network of depth  $L + 1$ , for any  $\ell = 1, \ldots, L$ , we *have the convergence in probability:*

$$
\lim_{n_L \to \infty} \cdots \lim_{n_1 \to \infty} \sup_{t \in [0,T]} \left\| \frac{1}{\sqrt{n_\ell}} \left( W^{(\ell)}(t) - W^{(\ell)}(0) \right) \right\|_{op} = 0
$$

From this lemma, to bound  $\|\frac{1}{\sqrt{n_L}}W^{(L)}(t)\|_{op}$ , it is hence enough to bound  $\|\frac{1}{\sqrt{n_L}}W^{(L)}(0)\|_{op}$ . From the law of large numbers, we obtain that the norm of each of the  $n_{L+1}$  rows of  $W^{(L)}(0)$  is bounded, and hence that  $\|\frac{1}{\sqrt{n_L}}W^{(L)}(0)\|_{op}$  is bounded (keep in mind that  $n_{L+1}$  is fixed, while  $n_1, \ldots, n_L$ grow).

From the above considerations, we can apply the induction hypothesis to the smaller network, yielding, in the limit as  $n_1, \ldots, n_L \to \infty$  (sequentially), that the dynamics is governed by the constant kernel  $\Theta^{(L)}_\infty$ :

$$
\partial_t \tilde{\alpha}_i^{(L)}(t) = \frac{1}{\sqrt{n_L}} \Phi_{\Theta_{\infty}^{(L)}} \left( \left\langle \dot{\sigma} \left( \tilde{\alpha}_i^{(L)}(t) \right) \left( W_i^{(L)}(t) \right)^T d_t, \cdot \right\rangle_{p^{in}} \right).
$$

At the same time, the parameters of the last layer evolve according to

$$
\partial_t W_{ij}^{(L)}(t) = \frac{1}{\sqrt{n_L}} \left\langle \alpha_i^{(L)}(t), d_{t,j} \right\rangle_{p^{in}}.
$$

We want to give an upper bound on the variation of the weights columns  $W_i^{(L)}(t)$  and of the activations  $\tilde{\alpha}_i^{(L)}(t)$  during training in terms of  $L^2$ -norm and  $p^{in}$ -norm respectively. Applying the Cauchy-Schwarz inequality for each j, summing and using  $\partial_t || \cdot || \le ||\partial_t \cdot ||$ ), we have

$$
\partial_t \left\|W_i^{(L)}(t)-W_i^{(L)}(0)\right\|_2 \leq \frac{1}{\sqrt{n_L}}||\alpha_i^{(L)}(t)||_{p^{in}}||d_t||_{p^{in}}.
$$

Now, observing that the operator norm of  $\Phi_{\Theta_{\infty}^{(L)}}$  is equal to  $||\Theta_{\infty}^{(L)}||_{op}$ , defined in the introduction of Appendix [A,](#page-10-7) and using the Cauchy-Schwarz inequality, we get

$$
\partial_t \left\| \tilde{\alpha}_i^{(L)}(t) - \tilde{\alpha}_i^{(L)}(0) \right\|_{p^{in}} \leq \frac{1}{\sqrt{n_L}} \left\| \Theta_{\infty}^{(L)} \right\|_{op} \left\| \dot{\sigma} \left( \tilde{\alpha}_i^{(L)}(t) \right) \right\|_{\infty} \left\| W_i^{(L)}(t) \right\|_2 \left\| d_t \right\|_{p^{in}},
$$

where the sup norm  $\lVert \cdot \rVert_{\infty}$  is defined by  $\lVert f \rVert_{\infty} = \sup_x |f(x)|$ .

To bound both quantities simultaneously, study the derivative of the quantity

$$
A(t) = ||\alpha_i^{(L)}(0)||_{p^{in}} + c \left\| \tilde{\alpha}_i^{(L)}(t) - \tilde{\alpha}_i^{(L)}(0) \right\|_{p^{in}} + ||W_i^{(L)}(0)||_2 + \left\| W_i^{(L)}(t) - W_i^{(L)}(0) \right\|_2.
$$

We have

$$
\partial_t A(t) \leq \frac{1}{\sqrt{n_L}} \left( c^2 \left\| \Theta_{\infty}^{(L)} \right\|_{op} \left\| W_i^{(L)}(t) \right\|_2 + ||\alpha_i^{(L)}(t)||_{p^{in}} \right) ||d_t||_{p^{in}}
$$

$$
\leq \frac{\max\{c^2 \|\Theta_{\infty}^{(L)}\|_{op}, 1\}}{\sqrt{n_L}} ||d_t||_{p^{in}} A(t),
$$

where, in the first inequality, we have used that  $|\dot{\sigma}| \leq c$  and, in the second inequality, that the sum  $||W_i^{(L)}(t)||_2 + ||\alpha_i^{(L)}(t)||_{p^{in}}$  is bounded by  $A(t)$ . Applying Grönwall's Lemma, we now get

$$
A(t) \le A(0) \exp \left( \frac{\max\{c^2 \| \Theta_{\infty}^{(L)} \|_{op}, 1\}}{\sqrt{n_L}} \int_0^t \| d_s \|_{p^{in}} ds \right).
$$

Note that  $\|\Theta_{\infty}^{(L)}\|_{op}$  is constant during training. Clearly the value inside of the exponential converges to zero in probability as  $n_L \to \infty$  given that the integral  $\int_0^t ||d_t||_{p^{in}} ds$  stays stochastically bounded. The variations of the activations  $\left\|\tilde{\alpha}_i^{(L)}(t) - \tilde{\alpha}_i^{(L)}(0)\right\|_{p^{in}}$  and weights  $\left\|W_i^{(L)}(t) - W_i^{(L)}(0)\right\|_2$  are bounded by  $c^{-1}(A(t) - A(0))$  and  $A(t) - A(0)$  respectively, which converge to zero at rate  $O\left(\frac{1}{\sqrt{n_L}}\right)$ .

We can now use these bounds to control the variation of the NTK and to prove the theorem. To understand how the NTK evolves, we study the evolution of the derivatives with respect to the parameters. The derivatives with respect to the bias parameters of the top layer  $\partial_{b_j^{(L)}} f_{\theta,j'}$  are always equal to  $\delta_{jj'}$ . The derivatives with respect to the connection weights of the top layer are given by

$$
\partial_{W_{ij}^{(L)}} f_{\theta,j'}(x) = \frac{1}{\sqrt{n_L}} \alpha_i^{(L)}(x;\theta) \delta_{jj'}.
$$

The pre-activations  $\tilde{\alpha}_i^{(L)}$  evolve at a rate of  $\frac{1}{\sqrt{n_L}}$  and so do the activations  $\alpha_i^{(L)}$ . The summands  $\partial_{W_{i,j}^{(L)}} f_{\theta,j'}(x) \otimes \partial_{W_{i,j}^{(L)}} f_{\theta,j''}(x')$  of the NTK hence vary at rate of  $n_L^{-3/2}$  which induces a variation of the NTK of rate  $\frac{1}{\sqrt{n_L}}$ .

Finally let us study the derivatives with respect to the parameters of the lower layers

$$
\partial_{\tilde{\theta}_k} f_{\theta,j}(x) = \frac{1}{\sqrt{n_L}} \sum_{i=1}^{n_L} \partial_{\tilde{\theta}_k} \tilde{\alpha}_i^{(L)}(x; \theta) \dot{\sigma} \left( \tilde{\alpha}_i^{(L)}(x; \theta) \right) W_{ij}^{(L)}.
$$

Their contribution to the NTK  $\Theta_{jj'}^{(L+1)}(x, x')$  is

$$
\frac{1}{n_L} \sum_{i,i'=1}^{n_L} \Theta_{ii'}^{(L)}(x, x') \dot{\sigma} \left(\tilde{\alpha}_i^{(L)}(x; \theta)\right) \dot{\sigma} \left(\tilde{\alpha}_{i'}^{(L)}(x'; \theta)\right) W_{ij}^{(L)} W_{i'j'}^{(L)}.
$$

By the induction hypothesis, the NTK of the smaller network  $\Theta^{(L)}$  tends to  $\Theta^{(L)}_{\infty} \delta_{ii'}$  as  $n_1, ..., n_{L-1} \rightarrow \infty$ . The contribution therefore becomes

$$
\frac{1}{n_L}\sum_{i=1}^{n_L}\Theta_{\infty}^{(L)}(x,x')\dot{\sigma}\left(\tilde{\alpha}_i^{(L)}(x;\theta)\right)\dot{\sigma}\left(\tilde{\alpha}_i^{(L)}(x';\theta)\right)W_{ij}^{(L)}W_{ij'}^{(L)}.
$$

The connection weights  $W_{ij}^{(L)}$  vary at rate  $\frac{1}{\sqrt{n_L}}$ , inducing a change of the same rate to the whole sum. We simply have to prove that the values  $\dot{\sigma}(\tilde{\alpha}_i^{(L)}(x;\theta))$  also change at rate  $\frac{1}{\sqrt{n_L}}$ . Since the second derivative of  $\sigma$  is bounded, we have that

$$
\partial_t \left( \dot{\sigma} \left( \tilde{\alpha}_i^{(L)}(x; \theta(t)) \right) \right) = O \left( \partial_t \tilde{\alpha}_i^{(L)}(x; \theta(t)) \right).
$$

Since  $\partial_t \tilde{\alpha}_i^{(L)}(x;\theta(t))$  goes to zero at a rate  $\frac{1}{\sqrt{n_L}}$  by the bound on  $A(t)$  above, this concludes the proof.

 $\Box$ 

It is somewhat counterintuitive that the variation of the activations of the hidden layers  $\alpha_i^{(\ell)}$  during training goes to zero as the width becomes large<sup>[4](#page-14-1)</sup>. It is generally assumed that the purpose of the activations of the hidden layers is to learn "good" representations of the data during training. However note that even though the variation of each individual activation shrinks, the number of neurons grows, resulting in a significant collective effect. This explains why the training of the parameters of each layer  $\ell$  has an influence on the network function  $f_\theta$  even though it has asymptotically no influence on the individual activations of the layers  $\ell'$  for  $\ell < \ell' < L$ .

<span id="page-14-0"></span>

### A.3 A Priori Control during Training

The goal of this section is to prove Lemma [1,](#page-13-0) which is a key ingredient in the proof of Theorem [2.](#page-5-1) Let us first recall it:

**Lemma 1.** With the setting of Theorem [2,](#page-5-1) for a network of depth  $L + 1$ , for any  $\ell = 1, \ldots, L$ , we *have the convergence in probability:*

$$
\lim_{n_L \to \infty} \cdots \lim_{n_1 \to \infty} \sup_{t \in [0,T]} \left\| \frac{1}{\sqrt{n_{\ell}}} \left( W^{(\ell)}(t) - W^{(\ell)}(0) \right) \right\|_{op} = 0
$$

<span id="page-14-1"></span><sup>&</sup>lt;sup>4</sup>As a consequence, the pre-activations stay Gaussian during training as well, with the same covariance  $\Sigma^{(\ell)}$ .

*Proof.* We prove the lemma for all  $\ell = 1, \ldots, L$  simultaneously, by expressing the variation of the weights  $\frac{1}{\sqrt{n_\ell}} W^{(\ell)}$  and activations  $\frac{1}{\sqrt{n_\ell}} \tilde{\alpha}^{(\ell)}$  in terms of 'back-propagated' training directions  $d^{(1)}, \ldots, d^{(L)}$  associated with the lower layers and the NTKs of the corresponding subnetworks:

1. At all times, the evolution of the preactivations and weights is given by:

$$
\partial_t \tilde{\alpha}^{(\ell)} = \Phi_{\Theta^{(\ell)}} \left( < d_t^{(\ell)}, \cdot >_{p^{in}} \right)
$$
\n
$$
\partial_t W^{(\ell)} = \frac{1}{\sqrt{n_\ell}} < \alpha^{(\ell)}, d_t^{(\ell+1)} >_{p^{in}},
$$

where the layer-wise training directions  $d^{(1)}, \ldots, d^{(L)}$  are defined recursively by

$$
d_t^{(\ell)} = \begin{cases} d_t & \text{if } \ell = L + 1 \\ \dot{\sigma} \left( \tilde{\alpha}^{(\ell)} \right) \left( \frac{1}{\sqrt{n_\ell}} W^{(\ell)} \right)^T d_t^{(\ell+1)} & \text{if } \ell \le L, \end{cases}
$$

and where the sub-network NTKs  $\Theta^{(\ell)}$  satisfy

$$
\Theta^{(1)} = \left[ \left[ \frac{1}{\sqrt{n_0}} \alpha^{(0)} \right]^T \left[ \frac{1}{\sqrt{n_0}} \alpha^{(0)} \right] \right] \otimes Id_{n_{\ell}} + \beta^2 \otimes Id_{n_{\ell}}
$$
$$
\Theta^{(\ell+1)} = \frac{1}{\sqrt{n_{\ell}}} W^{(\ell)} \dot{\sigma}(\tilde{\alpha}^{(\ell)}) \Theta^{(\ell)} \dot{\sigma}(\tilde{\alpha}^{(\ell)}) \frac{1}{\sqrt{n_{\ell}}} W^{(\ell)} + \left[ \left[ \frac{1}{\sqrt{n_{\ell}}} \alpha^{(\ell)} \right]^T \left[ \frac{1}{\sqrt{n_{\ell}}} \alpha^{(\ell)} \right] \right] \otimes Id_{n_{\ell}} + \beta^2 \otimes Id_{n_{\ell}}.
$$

2. Set  $w^{(k)}(t) := \left\|\frac{1}{\sqrt{n_k}}W^{(k)}(t)\right\|_{op}$  and  $a^{(k)}(t) := \left\|\frac{1}{\sqrt{n_k}}\alpha^{(k)}(t)\right\|_{p^{in}}$ . The identities of the previous step yield the following recursive bounds:

$$
\left\| d_t^{(\ell)} \right\|_{p^{in}} \leq c w^{(\ell)}(t) \left\| d_t^{(\ell+1)} \right\|_{p^{in}},
$$

where c is the Lipschitz constant of  $\sigma$ . These bounds lead to

$$
\left\|d_t^{(\ell)}\right\|_{p^{in}} \leq c^{L+1-\ell} \prod_{k=\ell}^{L} w^{(k)}(t) \left\|d_t\right\|_{p^{in}}.
$$

For the subnetworks NTKs we have the recursive bounds

$$
\|\Theta^{(1)}\|_{op} \le (a^{(0)}(t))^2 + \beta^2.
$$
  
$$
\|\Theta^{(\ell+1)}\|_{op} \le c^2 (w^{(\ell)}(t))^2 \|\Theta^{(\ell)}\|_{op} + (a^{(\ell)}(t))^2 + \beta^2,
$$

which lead to

$$
\|\Theta^{(\ell+1)}\|_{op} \leq \mathcal{P}\left(a^{(1)}, \ldots, a^{(\ell)}, w^{(1)}, \ldots, w^{(\ell)}\right),
$$

where  $P$  is a polynomial which only depends on  $\ell, c, \beta$  and  $p^{in}$ .

3. Set

$$
\tilde{a}^{(k)}(t) := \left\| \frac{1}{\sqrt{n_k}} \left( \tilde{\alpha}^{(k)}(t) - \tilde{\alpha}^{(k)}(0) \right) \right\|_{p^{in}}
$$
$$
\tilde{w}^{(k)}(t) := \left\| \frac{1}{\sqrt{n_k}} \left( W^{(k)}(t) - W^{(k)}(0) \right) \right\|_{op}
$$

and define

$$
A(t) = \sum_{k=1}^{L} a^{(k)}(0) + c\tilde{a}^{(k)}(t) + w^{(k)}(0) + \tilde{w}^{(k)}(t).
$$

Since  $a^{(k)}(t) \le a^{(k)}(0) + c\tilde{a}^{(k)}(t)$  and  $w^{(k)}(t) \le w^{(k)}(0) + \tilde{w}^{(k)}(t)$ , controlling  $A(t)$ will enable us to control the  $a^{(k)}(t)$  and  $w^{(k)}(t)$ . Using the formula at the beginning of the first step, we obtain

$$
\partial_t \tilde{a}^{(\ell)}(t) \leq \frac{1}{\sqrt{n_{\ell}}} \|\Theta^{(\ell)}(t)\|_{op} \|d_t^{(\ell)}\|_{p^{in}}
$$
  
$$
\partial_t \tilde{w}^{(\ell)}(t) \leq \frac{1}{\sqrt{n_{\ell}}} a^{(\ell)}(t) \|d_t^{(\ell+1)}\|_{p^{in}}.
$$

This allows one to bound the derivative of  $A(t)$  as follows:

$$
\partial_t A(t) \leq \sum_{\ell=1}^L \frac{c}{\sqrt{n_\ell}} \|\Theta^{(\ell)}(t)\|_{op} \|d_t^{(\ell)}\|_{p^{in}} + \frac{1}{\sqrt{n_\ell}} a^{(\ell)}(t) \|d_t^{(\ell+1)}\|_{p^{in}}.
$$

Using the polynomial bounds on  $\|\Theta^{(\ell)}(t)\|_{op}$  and  $\|d_t^{(\ell+1)}\|_{p^{in}}$  in terms of the  $a^{(k)}$  and  $w^{(k)}$ for  $k = 1, \dots \ell$  obtained in the previous step, we get that

$$
\partial_t A(t) \leq \frac{1}{\sqrt{\min\left\{n_1,\ldots,n_L\right\}}}\mathcal{Q}\left(w^{(1)}\left(t\right),\ldots,w^{(L)}\left(t\right),a^{(1)}\left(t\right),\ldots,a^{(L)}\left(t\right)\right)\|d_t\|_{p^{in}},
$$

where the polynomial Q only depends on  $L, c, \beta$  and  $p^{in}$  and has positive coefficients. As a result, we can use  $a^{(k)}(t) \le a^{(k)}(0) + c\tilde{a}^{(k)}(t)$  and  $w^{(k)}(t) \le w^{(k)}(0) + \tilde{w}^{(k)}(t)$  to get the polynomial bound

$$
\partial_t A(t) \leq \frac{1}{\sqrt{\min\left\{n_1,\ldots,n_L\right\}}}\tilde{\mathcal{Q}}\left(A\left(t\right)\right) \|d_t\|_{p^{in}}.
$$

4. Let us now observe that  $A(0)$  is stochastically bounded as we take the sequential limit  $\lim_{n_L\to\infty}\cdots\lim_{n_1\to\infty}$  as in the statement of the lemma. In this limit, we indeed have that  $w^{(\ell)}$  and  $a^{(\ell)}$  are convergent: we have  $w^{(\ell)} \to 0$ , while  $a^{(\ell)}$  converges by Proposition [1.](#page-4-1)

The polynomial control we obtained on the derivative of  $A(t)$  now allows one to use (a nonlinear form of, see e.g. [\(8\)](#page-9-14)) Grönwall's Lemma: we obtain that  $A(t)$  stays uniformly bounded on  $[0, \tau]$  for some  $\tau = \tau(n_1, \ldots, n_L) > 0$ , and that  $\tau \to T$  as  $\min(n_1,\ldots,n_L) \to \infty$ , owing to the  $\frac{1}{\sqrt{\min(L)}}$  $\frac{1}{\min\{1,\ldots,n_L\}}$  in front of the polynomial. Since A (t) is bounded, the differential bound on A (t) gives that the derivative  $\partial_t A(t)$  converges uniformly to 0 on [0,  $\tau$ ] for any  $\tau < T$ , and hence  $A(t) \to A(0)$ . This concludes the proof of the lemma.

$$
\qquad \qquad \Box
$$

<span id="page-16-0"></span>

## A.4 Positive-Definiteness of $\Theta_{\infty}^{(L)}$

This subsection is devoted to the proof of Proposition [2,](#page-5-2) which we now recall:

**Proposition 2.** *For a non-polynomial Lipschitz nonlinearity*  $\sigma$ *, for any input dimension*  $n_0$ *, the restriction of the limiting NTK*  $\Theta_{\infty}^{(L)}$  *to the unit sphere*  $\mathbb{S}^{n_0-1} = \{x \in \mathbb{R}^{n_0} : x^T x = 1\}$  *is positivedefinite if*  $L > 2$ *.* 

A key ingredient for the proof of Proposition [2](#page-5-2) is the following Lemma, which comes from [\(4\)](#page-9-1).

<span id="page-16-1"></span>**Lemma 2** (Lemma 12(a) in suppl. mat. of [\(4\)](#page-9-1)). Let  $\hat{\mu}$  :  $[-1, 1] \rightarrow \mathbb{R}$  denote the dual of a Lipschitz  $f$ unction  $\mu:\mathbb{R}\to\mathbb{R}$ , defined by  $\hat{\mu}(\rho)=\mathbb{E}_{(X,Y)}\left[\mu\left(X\right)\mu\left(Y\right)\right]$  where  $(X,Y)$  is a centered Gaussian *vector of covariance* Σ*, with*

$$
\Sigma = \begin{pmatrix} 1 & \rho \\ \rho & 1 \end{pmatrix}.
$$

*If the expansion of*  $\mu$  *in Hermite polynomials*  $(h_i)_{i>0}$  *is given by*  $\mu = \sum_{i=0}^{\infty} a_i h_i$ *, we have* 

$$
\hat{\mu}(\rho) = \sum_{i=0}^{\infty} a_i^2 \rho^i.
$$

The other key ingredient for proving Proposition [2](#page-5-2) is the following theorem, which is a slight reformulation of Theorem 1(b) in  $(9)$ , which itself is a generalization of a classical result of Schönberg:

<span id="page-17-0"></span>**Theorem 3.** For a function  $f: [-1,1] \to \mathbb{R}$  with  $f(\rho) = \sum_{n=0}^{\infty} b_n \rho^n$ , the kernel  $K_f^{(n_0)}: \mathbb{S}^{n_0-1} \times$  $\mathbb{S}^{n_0-1}\to \mathbb{R}$  *defined by* 

$$
K_f^{(n_0)}(x, x') = f\left(x^T x'\right)
$$

*is positive-definite for any*  $n_0 \geq 1$  *if and only if the coefficients*  $b_n$  *are strictly positive for infinitely many even and infinitely many odd integers* n*.*

With Lemma [2](#page-16-1) and Theorem [3](#page-17-0) above, we are now ready to prove Proposition [2.](#page-5-2)

*Proof of Proposition* [2.](#page-5-2) We first decompose the limiting NTK  $\Theta^{(L)}$  recursively, relate its positivedefiniteness to that of the activation kernels, then show that the positive-definiteness of the activation kernels at level 2 implies that of the higher levels, and finally show the positive-definiteness at level 2 using Lemma [2](#page-16-1) and Theorem [3:](#page-17-0)

1. Observe that for any  $L \geq 1$ , using the notation of Theorem [1,](#page-4-2) we have

$$
\Theta^{(L+1)} = \dot{\Sigma}^{(L)}\Theta^{(L)} + \Sigma^{(L+1)}
$$

.

Note that the kernel  $\dot{\Sigma}^{(L)}\Theta^{(L)}$  is positive semi-definite, being the product of two positive semi-definite kernels. Hence, if we show that  $\Sigma^{(L+1)}$  is positive-definite, this implies that  $\Theta^{(L+1)}$  is positive-definite.

2. By definition, with the notation of Proposition [1](#page-4-1) we have

$$
\Sigma^{(L+1)}(x, x') = \mathbb{E}_{f \sim \mathcal{N}(0, \Sigma^{(L)})} \left[ \sigma \left( f \left( x \right) \right) \sigma \left( f \left( x' \right) \right) \right] + \beta^2.
$$

This gives, for any collection of coefficients  $c_1, \ldots, c_d \in \mathbb{R}$  and any pairwise distinct  $x_1, \ldots, x_d \in \mathbb{R}^{n_0}$ , that

$$
\sum_{i,j=1}^d c_i c_j \Sigma^{(L+1)}(x_i, x_j) = \mathbb{E}\left[\left(\sum_i c_i \sigma(f(x_i))\right)^2\right] + \left(\beta \sum_i c_i\right)^2.
$$

Hence the left-hand side only vanishes if  $\sum c_i \sigma(f(x_i))$  is almost surely zero. If  $\Sigma^{(L)}$  is positive-definite, the Gaussian  $(f(x_i))_{i=1,\dots,d}$  is non-degenerate, so this only occurs when  $c_1 = \cdots = c_d = 0$  since  $\sigma$  is assumed to be non-constant. This shows that the positivedefiniteness of  $\Sigma^{(L+1)}$  is implied by that of  $\Sigma^{(L)}$ . By induction, if  $\Sigma^{(2)}$  is positive-definite, we obtain that all  $\Sigma^{(L)}$  with  $L \geq 2$  are positive-definite as well. By the first step this hence implies that  $\Theta^{(L)}$  is positive-definite as well.

3. By the previous steps, to prove the proposition, it suffices to show the positive-definitess of  $\Sigma^{(2)}$  on the unit sphere  $\mathbb{S}^{n_0-1}$ . We have

$$
\Sigma^{(2)}(x, x') = \mathbb{E}_{(X, Y) \sim \mathcal{N}(0, \tilde{\Sigma})} [\sigma(X) \sigma(Y)] + \beta^2
$$

where

$$
\tilde{\Sigma} = \begin{pmatrix} \frac{1}{n_0} + \beta^2 & \frac{1}{n_0} x^T x' + \beta^2 \\ \frac{1}{n_0} x^T x + \beta^2 & \frac{1}{n_0} + \beta^2 \end{pmatrix}.
$$

A change of variables then yields

<span id="page-17-1"></span>
$$
\mathbb{E}_{(X,Y)\sim\mathcal{N}(0,\tilde{\Sigma})}\left[\sigma\left(X\right)\sigma\left(Y\right)\right]+\beta^2=\hat{\mu}\left(\frac{n_0\beta^2+x^Tx'}{n_0\beta^2+1}\right)+\beta^2,\tag{1}
$$

where  $\hat{\mu} : [-1, 1] \to \mathbb{R}$  is the dual in the sense of Lemma [2](#page-16-1) of the function  $\mu : \mathbb{R} \to \mathbb{R}$ defined by  $\mu(x) = \sigma\left(x\sqrt{\frac{1}{n_0} + \beta^2}\right)$ .

4. Writing the expansion of  $\mu$  in Hermite polynomials  $(h_i)_{i \geq 0}$ 

$$
\mu = \sum_{i=0}^{\infty} a_i h_i,
$$

we obtain that  $\hat{\mu}$  is given by the power series

$$
\hat{\mu}(\rho) = \sum_{i=0}^{\infty} a_i^2 \rho^i,
$$

Since  $\sigma$  is non-polynomial, so is  $\mu$ , and as a result, there is an infinite number of nonzero  $a_i$ 's in the above sum.

5. Using [\(1\)](#page-17-1) above, we obtain that

$$
\Sigma^{(2)}(x, x') = \nu(x^T x'),
$$

where  $\nu : \mathbb{R} \to \mathbb{R}$  is defined by

$$
\nu(\rho) = \beta^2 + \sum_{i=0}^{\infty} a_i \left( \frac{n_0 \beta^2 + \rho}{n_0 \beta^2 + 1} \right)^i,
$$

where the  $a_i$ 's are the coefficients of the Hermite expansion of  $\mu$ . Now, observe that by the previous step, the power series expansion of  $\nu$  contains both an infinite number of nonzero even terms and an infinite number of nonzero odd terms. This enables one to apply Theorem [3](#page-17-0) to obtain that  $\Sigma^{(2)}$  is indeed positive-definite, thereby concluding the proof.

 $\Box$ 

Remark 5. *Using similar techniques to the one applied in the proof above, one can show a converse to Proposition [2:](#page-5-2) if the nonlinearity*  $\sigma$  *is a polynomial, the corresponding NTK*  $\Theta^{(2)}$  *is not positive*definite S<sup>n<sub>0</sub>−1</sub> for certain input dimensions n<sub>0</sub>.</sup>
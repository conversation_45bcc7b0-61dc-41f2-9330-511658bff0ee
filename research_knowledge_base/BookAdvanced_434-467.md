## Bibliographic Notes

Projection pursuit was proposed by <PERSON> and <PERSON> (1974), and specialized to regression by <PERSON> and <PERSON> (1981). <PERSON><PERSON> (1985) gives a scholarly overview, and <PERSON><PERSON><PERSON> and <PERSON><PERSON> (1994) present a formulation using smoothing splines. The motivation for neural networks dates back to <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON> (1943), <PERSON><PERSON><PERSON> and <PERSON> (1960) (reprinted in <PERSON> and <PERSON> (1988)) and <PERSON> (1962). <PERSON><PERSON> (1949) heavily influenced the development of learning algorithms. The resurgence of neural networks in the mid 1980s was due to <PERSON><PERSON><PERSON> (1974), <PERSON> (1985) and <PERSON><PERSON><PERSON><PERSON> et al. (1986), who proposed the back-propagation algorithm. Today there are many books written on the topic, for a broad range of audiences. For readers of this book, <PERSON><PERSON> et al. (1991), <PERSON> (1995) and <PERSON><PERSON><PERSON> (1996) may be the most informative. Bayesian learning for neural networks is described in <PERSON> (1996). The ZIP code example was taken from <PERSON> (1989); see also <PERSON> et al. (1990) and <PERSON> et al. (1998).

We do not discuss theoretical topics such as approximation properties of neural networks, such as the work of <PERSON> (1993), <PERSON><PERSON> et al. (1995) and <PERSON> (1992). Some of these results are summarized by <PERSON><PERSON><PERSON> (1996).

## **Exercises**

Ex. 11.1 Establish the exact correspondence between the projection pursuit regression model (11.1) and the neural network (11.5). In particular, show that the single-layer regression network is equivalent to a PPR model with  $g_m(\omega_m^T x) = \beta_m \sigma(\alpha_{0m} + s_m(\omega_m^T x))$ , where  $\omega_m$  is the mth unit vector. Establish a similar equivalence for a classification network.

Ex. 11.2 Consider a neural network for a quantitative outcome as in (11.5), using squared-error loss and identity output function  $q_k(t) = t$ . Suppose that the weights  $\alpha_m$  from the input to hidden layer are nearly zero. Show that the resulting model is nearly linear in the inputs.

Ex. 11.3 Derive the forward and backward propagation equations for the cross-entropy loss function.

Ex. 11.4 Consider a neural network for a K class outcome that uses crossentropy loss. If the network has no hidden layer, show that the model is equivalent to the multinomial logistic model described in Chapter 4.

#### Ex. 11.5

(a) Write a program to fit a single hidden layer neural network (ten hidden units) via back-propagation and weight decay.

#### 416 Neural Networks

(b) Apply it to 100 observations from the model

$$
Y = \sigma(a_1^T X) + (a_2^T X)^2 + 0.30 \cdot Z,
$$

where  $\sigma$  is the sigmoid function, Z is standard normal,  $X^T = (X_1, X_2)$ , each  $X_j$  being independent standard normal, and  $a_1 = (3, 3), a_2 =$ (3, −3). Generate a test sample of size 1000, and plot the training and test error curves as a function of the number of training epochs, for different values of the weight decay parameter. Discuss the overfitting behavior in each case.

(c) Vary the number of hidden units in the network, from 1 up to 10, and determine the minimum number needed to perform well for this task.

Ex. 11.6 Write a program to carry out projection pursuit regression, using cubic smoothing splines with fixed degrees of freedom. Fit it to the data from the previous exercise, for various values of the smoothing parameter and number of model terms. Find the minimum number of model terms necessary for the model to perform well and compare this to the number of hidden units from the previous exercise.

Ex. 11.7 Fit a neural network to the spam data of Section 9.1.2, and compare the results to those for the additive model given in that chapter. Compare both the classification performance and interpretability of the final model.

This is page 417 Printer: Opaque this

# 12

## Support Vector Machines and Flexible Discriminants

## 12.1 Introduction

In this chapter we describe generalizations of linear decision boundaries for classification. Optimal separating hyperplanes are introduced in Chapter 4 for the case when two classes are linearly separable. Here we cover extensions to the nonseparable case, where the classes overlap. These techniques are then generalized to what is known as the support vector machine, which produces nonlinear boundaries by constructing a linear boundary in a large, transformed version of the feature space. The second set of methods generalize Fisher's linear discriminant analysis (LDA). The generalizations include flexible discriminant analysis which facilitates construction of nonlinear boundaries in a manner very similar to the support vector machines, penalized discriminant analysis for problems such as signal and image classification where the large number of features are highly correlated, and mixture discriminant analysis for irregularly shaped classes.

## 12.2 The Support Vector Classifier

In Chapter 4 we discussed a technique for constructing an optimal separating hyperplane between two perfectly separated classes. We review this and generalize to the nonseparable case, where the classes may not be separable by a linear boundary.

Image /page/3/Figure/1 description: The image displays two side-by-side scatter plots illustrating the concept of Support Vector Machines (SVMs). Both plots show red and green data points, representing two different classes. A diagonal line, labeled "x^T \beta + \beta\_0 = 0", represents the decision boundary. A yellow shaded region indicates the margin, with dashed lines marking the boundaries of this margin. The width of the margin is indicated by arrows and labeled as "margin" and "M = 1/||\beta||". The left plot shows a clear separation with the margin, while the right plot demonstrates a soft margin scenario where some data points (marked with \xi^\* and a number) fall within or on the wrong side of the margin, indicating misclassifications or slack variables.

FIGURE 12.1. Support vector classifiers. The left panel shows the separable case. The decision boundary is the solid line, while broken lines bound the shaded maximal margin of width  $2M = 2/||\beta||$ . The right panel shows the nonseparable (overlap) case. The points labeled  $\xi_j^*$  are on the wrong side of their margin by an amount  $\xi_j^* = M\xi_j$ ; points on the correct side have  $\xi_j^* = 0$ . The margin is maximized subject to a total budget  $\sum \xi_i \leq constant$ . Hence  $\sum \xi_j^*$  is the total distance of points on the wrong side of their margin.

Our training data consists of N pairs  $(x_1, y_1), (x_2, y_2), \ldots, (x_N, y_N)$ , with  $x_i \in \mathbb{R}^p$  and  $y_i \in \{-1, 1\}$ . Define a hyperplane by

$$
\{x:\ f(x) = x^T \beta + \beta_0 = 0\},\tag{12.1}
$$

where  $\beta$  is a unit vector:  $\|\beta\| = 1$ . A classification rule induced by  $f(x)$  is

$$
G(x) = \text{sign}[x^T \beta + \beta_0].
$$
\n(12.2)

The geometry of hyperplanes is reviewed in Section 4.5, where we show that  $f(x)$  in (12.1) gives the signed distance from a point x to the hyperplane  $f(x) = x^T \beta + \beta_0 = 0$ . Since the classes are separable, we can find a function  $f(x) = x^T \beta + \beta_0$  with  $y_i f(x_i) > 0$   $\forall i$ . Hence we are able to find the hyperplane that creates the biggest margin between the training points for class 1 and −1 (see Figure 12.1). The optimization problem

$$
\max_{\beta,\beta_0,\|\beta\|=1} M
$$
  
subject to  $y_i(x_i^T\beta + \beta_0) \ge M, i = 1,..., N,$  (12.3)

captures this concept. The band in the figure is  $M$  units away from the hyperplane on either side, and hence  $2M$  units wide. It is called the *margin*.

We showed that this problem can be more conveniently rephrased as

 $k = 0.80$ 

$$
\min_{\beta,\beta_0} \|\beta\|
$$
  
subject to  $y_i(x_i^T \beta + \beta_0) \ge 1, i = 1,..., N,$  (12.4)

where we have dropped the norm constraint on β. Note that  $M = 1/||\beta||$ . Expression (12.4) is the usual way of writing the support vector criterion for separated data. This is a convex optimization problem (quadratic criterion, linear inequality constraints), and the solution is characterized in Section 4.5.2.

Suppose now that the classes overlap in feature space. One way to deal with the overlap is to still maximize  $M$ , but allow for some points to be on the wrong side of the margin. Define the slack variables  $\xi = (\xi_1, \xi_2, \ldots, \xi_N)$ . There are two natural ways to modify the constraint in (12.3):

$$
y_i(x_i^T \beta + \beta_0) \geq M - \xi_i,
$$
\nor

\n
$$
(12.5)
$$

$$
y_i(x_i^T \beta + \beta_0) \geq M(1 - \xi_i), \tag{12.6}
$$

 $\forall i, \xi_i \geq 0, \sum_{i=1}^N \xi_i \leq \text{constant}$ . The two choices lead to different solutions. The first choice seems more natural, since it measures overlap in actual distance from the margin; the second choice measures the overlap in relative distance, which changes with the width of the margin  $M$ . However, the first choice results in a nonconvex optimization problem, while the second is convex; thus (12.6) leads to the "standard" support vector classifier, which we use from here on.

Here is the idea of the formulation. The value  $\xi_i$  in the constraint  $y_i(x_i^T \beta + \xi_i^T \beta)$  $\beta_0$ )  $\geq M(1 - \xi_i)$  is the proportional amount by which the prediction  $f(x_i) = x_i^T \beta + \beta_0$  is on the wrong side of its margin. Hence by bounding the sum  $\sum \xi_i$ , we bound the total proportional amount by which predictions fall on the wrong side of their margin. Misclassifications occur when  $\xi_i > 1$ , so bounding  $\sum \xi_i$  at a value K say, bounds the total number of training misclassifications at K.

As in (4.48) in Section 4.5.2, we can drop the norm constraint on  $\beta$ , define  $M = 1/||\beta||$ , and write (12.4) in the equivalent form

$$
\min \|\beta\| \quad \text{subject to} \quad \begin{cases} \quad y_i(x_i^T \beta + \beta_0) \ge 1 - \xi_i \,\forall i, \\ \quad \xi_i \ge 0, \ \sum \xi_i \le \text{constant.} \end{cases} \tag{12.7}
$$

This is the usual way the support vector classifier is defined for the nonseparable case. However we find confusing the presence of the fixed scale "1" in the constraint  $y_i(x_i^T \beta + \beta_0) \ge 1 - \xi_i$ , and prefer to start with (12.6). The right panel of Figure 12.1 illustrates this overlapping case.

By the nature of the criterion (12.7), we see that points well inside their class boundary do not play a big role in shaping the boundary. This seems like an attractive property, and one that differentiates it from linear discriminant analysis (Section 4.3). In LDA, the decision boundary is determined by the covariance of the class distributions and the positions of the class centroids. We will see in Section 12.3.3 that logistic regression is more similar to the support vector classifier in this regard.

## 12.2.1 Computing the Support Vector Classifier

Image /page/5/Picture/2 description: A yellow, cartoonish face with wide, staring eyes and an open mouth is depicted. The figure has its hands pressed against its temples, as if in shock or distress. The background is black.

The problem (12.7) is quadratic with linear inequality constraints, hence it is a convex optimization problem. We describe a quadratic programming solution using Lagrange multipliers. Computationally it is convenient to re-express (12.7) in the equivalent form

$$
\min_{\beta,\beta_0} \frac{1}{2} ||\beta||^2 + C \sum_{i=1}^N \xi_i
$$
\nsubject to 
$$
\xi_i \ge 0, y_i(x_i^T \beta + \beta_0) \ge 1 - \xi_i \,\forall i,
$$
\n(12.8)

where the "cost" parameter  $C$  replaces the constant in  $(12.7)$ ; the separable case corresponds to  $C = \infty$ .

The Lagrange (primal) function is

$$
L_P = \frac{1}{2} ||\beta||^2 + C \sum_{i=1}^{N} \xi_i - \sum_{i=1}^{N} \alpha_i [y_i (x_i^T \beta + \beta_0) - (1 - \xi_i)] - \sum_{i=1}^{N} \mu_i \xi_i, \tag{12.9}
$$

which we minimize w.r.t  $\beta$ ,  $\beta_0$  and  $\xi_i$ . Setting the respective derivatives to zero, we get

$$
\beta = \sum_{i=1}^{N} \alpha_i y_i x_i, \qquad (12.10)
$$

$$
0 = \sum_{i=1}^{N} \alpha_i y_i, \qquad (12.11)
$$

$$
\alpha_i = C - \mu_i, \forall i,
$$
\n(12.12)

as well as the positivity constraints  $\alpha_i$ ,  $\mu_i$ ,  $\xi_i \geq 0$   $\forall i$ . By substituting  $(12.10)$ – $(12.12)$  into  $(12.9)$ , we obtain the Lagrangian (Wolfe) dual objective function

$$
L_D = \sum_{i=1}^{N} \alpha_i - \frac{1}{2} \sum_{i=1}^{N} \sum_{i'=1}^{N} \alpha_i \alpha_{i'} y_i y_{i'} x_i^T x_{i'},
$$
 (12.13)

which gives a lower bound on the objective function  $(12.8)$  for any feasible point. We maximize  $L_D$  subject to  $0 \leq \alpha_i \leq C$  and  $\sum_{i=1}^{N} \alpha_i y_i = 0$ . In addition to  $(12.10)$ – $(12.12)$ , the Karush–Kuhn–Tucker conditions include the constraints

$$
\alpha_i[y_i(x_i^T \beta + \beta_0) - (1 - \xi_i)] = 0, \qquad (12.14)
$$

$$
\mu_i \xi_i = 0, \qquad (12.15)
$$

$$
y_i(x_i^T \beta + \beta_0) - (1 - \xi_i) \geq 0, \qquad (12.16)
$$

for  $i = 1, \ldots, N$ . Together these equations  $(12.10)$ – $(12.16)$  uniquely characterize the solution to the primal and dual problem.

From (12.10) we see that the solution for  $\beta$  has the form

$$
\hat{\beta} = \sum_{i=1}^{N} \hat{\alpha}_i y_i x_i,
$$
\n(12.17)

with nonzero coefficients  $\hat{\alpha}_i$  only for those observations i for which the constraints in (12.16) are exactly met (due to (12.14)). These observations are called the *support vectors*, since  $\hat{\beta}$  is represented in terms of them alone. Among these support points, some will lie on the edge of the margin  $(\hat{\xi}_i = 0)$ , and hence from (12.15) and (12.12) will be characterized by  $0 < \hat{\alpha}_i < C$ ; the remainder  $(\hat{\xi}_i > 0)$  have  $\hat{\alpha}_i = C$ . From (12.14) we can see that any of these margin points  $(0 < \hat{\alpha}_i, \hat{\xi}_i = 0)$  can be used to solve for  $\beta_0$ , and we typically use an average of all the solutions for numerical stability.

Maximizing the dual (12.13) is a simpler convex quadratic programming problem than the primal (12.9), and can be solved with standard techniques (Murray et al., 1981, for example).

Given the solutions  $\hat{\beta}_0$  and  $\hat{\beta}$ , the decision function can be written as

$$
\hat{G}(x) = \text{sign}[\hat{f}(x)]
$$
  
=  $\text{sign}[x^T \hat{\beta} + \hat{\beta}_0].$  (12.18)

The tuning parameter of this procedure is the cost parameter C.

## 12.2.2 Mixture Example (Continued)

Figure 12.2 shows the support vector boundary for the mixture example of Figure 2.5 on page 21, with two overlapping classes, for two different values of the cost parameter C. The classifiers are rather similar in their performance. Points on the wrong side of the boundary are support vectors. In addition, points on the correct side of the boundary but close to it (in the margin), are also support vectors. The margin is larger for  $C = 0.01$ than it is for  $C = 10,000$ . Hence larger values of C focus attention more on (correctly classified) points near the decision boundary, while smaller values involve data further away. Either way, misclassified points are given weight, no matter how far away. In this example the procedure is not very sensitive to choices of C, because of the rigidity of a linear boundary.

The optimal value for  $C$  can be estimated by cross-validation, as discussed in Chapter 7. Interestingly, the leave-one-out cross-validation error can be bounded above by the proportion of support points in the data. The reason is that leaving out an observation that is not a support vector will not change the solution. Hence these observations, being classified correctly by the original boundary, will be classified correctly in the cross-validation process. However this bound tends to be too high, and not generally useful for choosing  $C$  (62\% and 85\%, respectively, in our examples).

Image /page/7/Figure/1 description: The image displays two plots, each illustrating a classification boundary for a Support Vector Machine (SVM) with different values of the regularization parameter C. The top plot shows a classification boundary for C = 10000, with a training error of 0.270, a test error of 0.288, and a Bayes error of 0.210. The bottom plot shows a classification boundary for C = 0.01, with a training error of 0.26, a test error of 0.30, and a Bayes error of 0.21. Both plots feature two classes of data points, represented by orange and blue circles, with a background color gradient indicating the predicted class. Solid black lines represent the decision boundary and margins, while dashed purple curves indicate the non-linear decision boundary. Black dots highlight support vectors.

Image /page/7/Figure/2 description: The image contains the text "C = 0.01".

FIGURE 12.2. The linear support vector boundary for the mixture data example with two overlapping classes, for two different values of  $C$ . The broken lines indicate the margins, where  $f(x) = \pm 1$ . The support points  $(\alpha_i > 0)$  are all the points on the wrong side of their margin. The black solid dots are those support points falling exactly on the margin ( $\xi_i = 0$ ,  $\alpha_i > 0$ ). In the upper panel 62% of the observations are support points, while in the lower panel 85% are. The broken purple curve in the background is the Bayes decision boundary.

## 12.3 Support Vector Machines and Kernels

The support vector classifier described so far finds linear boundaries in the input feature space. As with other linear methods, we can make the procedure more flexible by enlarging the feature space using basis expansions such as polynomials or splines (Chapter 5). Generally linear boundaries in the enlarged space achieve better training-class separation, and translate to nonlinear boundaries in the original space. Once the basis functions  $h_m(x)$ ,  $m = 1, \ldots, M$  are selected, the procedure is the same as before. We fit the SV classifier using input features  $h(x_i) = (h_1(x_i), h_2(x_i), \ldots, h_M(x_i)),$  $i = 1, ..., N$ , and produce the (nonlinear) function  $\hat{f}(x) = h(x)^T \hat{\beta} + \hat{\beta}_0$ . The classifier is  $\hat{G}(x) = \text{sign}(\hat{f}(x))$  as before.

The *support vector machine* classifier is an extension of this idea, where the dimension of the enlarged space is allowed to get very large, infinite in some cases. It might seem that the computations would become prohibitive. It would also seem that with sufficient basis functions, the data would be separable, and overfitting would occur. We first show how the SVM technology deals with these issues. We then see that in fact the SVM classifier is solving a function-fitting problem using a particular criterion and form of regularization, and is part of a much bigger class of problems that includes the smoothing splines of Chapter 5. The reader may wish to consult Section 5.8, which provides background material and overlaps somewhat with the next two sections.

## 12.3.1 Computing the SVM for Classification

We can represent the optimization problem (12.9) and its solution in a special way that only involves the input features via inner products. We do this directly for the transformed feature vectors  $h(x_i)$ . We then see that for particular choices of h, these inner products can be computed very cheaply.

The Lagrange dual function (12.13) has the form

$$
L_D = \sum_{i=1}^{N} \alpha_i - \frac{1}{2} \sum_{i=1}^{N} \sum_{i'=1}^{N} \alpha_i \alpha_{i'} y_i y_{i'} \langle h(x_i), h(x_{i'}) \rangle.
$$
 (12.19)

From (12.10) we see that the solution function  $f(x)$  can be written

$$
f(x) = h(x)^{T} \beta + \beta_0
$$
  
= 
$$
\sum_{i=1}^{N} \alpha_i y_i \langle h(x), h(x_i) \rangle + \beta_0.
$$
 (12.20)

As before, given  $\alpha_i$ ,  $\beta_0$  can be determined by solving  $y_i f(x_i) = 1$  in (12.20) for any (or all)  $x_i$  for which  $0 < \alpha_i < C$ .

So both  $(12.19)$  and  $(12.20)$  involve  $h(x)$  only through inner products. In fact, we need not specify the transformation  $h(x)$  at all, but require only knowledge of the kernel function

$$
K(x, x') = \langle h(x), h(x') \rangle \tag{12.21}
$$

that computes inner products in the transformed space. K should be a symmetric positive (semi-) definite function; see Section 5.8.1.

Three popular choices for  $K$  in the SVM literature are

*d*th-Degree polynomial: 
$$
K(x, x') = (1 + \langle x, x' \rangle)^d
$$
,  
Radial basis:  $K(x, x') = \exp(-\gamma ||x - x'||^2)$ ,  
Neural network:  $K(x, x') = \tanh(\kappa_1 \langle x, x' \rangle + \kappa_2)$ .  
(12.22)

Consider for example a feature space with two inputs  $X_1$  and  $X_2$ , and a polynomial kernel of degree 2. Then

$$
K(X, X') = (1 + \langle X, X' \rangle)^2
$$
  

$$
= (1 + X_1X'_1 + X_2X'_2)^2
$$
  

$$
= 1 + 2X_1X'_1 + 2X_2X'_2 + (X_1X'_1)^2 + (X_2X'_2)^2 + 2X_1X'_1X_2X'_2. \quad (12.23)
$$

Then  $M = 6$ , and if we choose  $h_1(X) = 1$ ,  $h_2(X) = \sqrt{2}X_1$ ,  $h_3(X) =$  $\sqrt{2}X_2$ ,  $h_4(X) = X_1^2$ ,  $h_5(X) = X_2^2$ , and  $h_6(X) = \sqrt{2}X_1X_2$ , then  $K(X, X') =$  $\langle h(X), h(X') \rangle$ . From (12.20) we see that the solution can be written

$$
\hat{f}(x) = \sum_{i=1}^{N} \hat{\alpha}_i y_i K(x, x_i) + \hat{\beta}_0.
$$
\n(12.24)

The role of the parameter  $C$  is clearer in an enlarged feature space, since perfect separation is often achievable there. A large value of  $C$  will discourage any positive  $\xi_i$ , and lead to an overfit wiggly boundary in the original feature space; a small value of C will encourage a small value of  $\|\beta\|$ , which in turn causes  $f(x)$  and hence the boundary to be smoother. Figure 12.3 show two nonlinear support vector machines applied to the mixture example of Chapter 2. The regularization parameter was chosen in both cases to achieve good test error. The radial basis kernel produces a boundary quite similar to the Bayes optimal boundary for this example; compare Figure 2.5.

In the early literature on support vectors, there were claims that the kernel property of the support vector machine is unique to it and allows one to finesse the curse of dimensionality. Neither of these claims is true, and we go into both of these issues in the next three subsections.

Image /page/10/Figure/1 description: This image displays a scatter plot with a decision boundary, likely representing a classification task. There are three classes of data points, indicated by black circles, orange circles, and blue circles. The background is colored with dotted patterns, with a light orange/yellowish hue in the top left and a light blue hue in the bottom right, suggesting two distinct regions. Solid black curves and dashed black curves represent decision boundaries, separating these regions. Additionally, dashed purple curves are present, possibly indicating other boundaries or confidence intervals. In the bottom left corner, text indicates performance metrics: 'Training Error: 0.180', 'Test Error: 0.245', and 'Bayes Error: 0.210'.

SVM - Degree-4 Polynomial in Feature Space

SVM - Radial Kernel in Feature Space

Image /page/10/Figure/4 description: This is a scatter plot showing a classification boundary with two classes of data points, represented by orange circles and blue circles. Black dots are also present, likely representing support vectors or boundary points. The background is filled with a dotted pattern, colored orange and blue, indicating the predicted class for each region. Several contour lines, including solid black, dashed black, and dashed purple lines, are drawn to delineate decision boundaries. In the bottom left corner, text indicates the performance metrics: Training Error: 0.160, Test Error: 0.218, and Bayes Error: 0.210.

FIGURE 12.3. Two nonlinear SVMs for the mixture data. The upper plot uses a 4th degree polynomial kernel, the lower a radial basis kernel (with  $\gamma = 1$ ). In each case  $C$  was tuned to approximately achieve the best test error performance, and  $C = 1$  worked well in both cases. The radial basis kernel performs the best (close to Bayes optimal), as might be expected given the data arise from mixtures of Gaussians. The broken purple curve in the background is the Bayes decision  $boundary.$ 

Image /page/11/Figure/1 description: This is a line graph comparing four different loss functions: Hinge Loss, Binomial Deviance, Squared Error, and Class Huber. The x-axis is labeled 'yf' and ranges from -3 to 3. The y-axis is labeled 'Loss' and ranges from 0.0 to 3.0. A dashed vertical line is present at x=0, and a dashed horizontal line is present at y=0.0. The Hinge Loss is a red line that decreases linearly from approximately 3.0 at x=-3 to 0.0 at x=1. The Binomial Deviance is a green line that decreases from approximately 3.0 at x=-3, curves downwards, and reaches 0.0 at x=1, then increases slightly. The Squared Error is an orange line that decreases from approximately 3.0 at x=-3, curves downwards, and reaches a minimum of 0.0 at x=1.5, then increases. The Class Huber is a blue line that decreases sharply from approximately 3.0 at x=-3, curves downwards, and reaches 0.0 at x=0.5, then stays at 0.0 until approximately x=1.5, after which it increases slightly.

FIGURE 12.4. The support vector loss function (hinge loss), compared to the negative log-likelihood loss (binomial deviance) for logistic regression, squared-error loss, and a "Huberized" version of the squared hinge loss. All are shown as a function of yf rather than f, because of the symmetry between the  $y = +1$  and  $y = -1$  case. The deviance and Huber have the same asymptotes as the SVM loss, but are rounded in the interior. All are scaled to have the limiting left-tail slope of  $-1$ .

## 12.3.2 The SVM as a Penalization Method

With  $f(x) = h(x)^T \beta + \beta_0$ , consider the optimization problem

$$
\min_{\beta_0, \beta} \sum_{i=1}^{N} [1 - y_i f(x_i)]_+ + \frac{\lambda}{2} ||\beta||^2
$$
\n(12.25)

where the subscript " $+$ " indicates positive part. This has the form  $loss +$ penalty, which is a familiar paradigm in function estimation. It is easy to show (Exercise 12.1) that the solution to (12.25), with  $\lambda = 1/C$ , is the same as that for  $(12.8)$ .

Examination of the "hinge" loss function  $L(y, f) = [1 - yf]_+$  shows that it is reasonable for two-class classification, when compared to other more traditional loss functions. Figure 12.4 compares it to the log-likelihood loss for logistic regression, as well as squared-error loss and a variant thereof. The (negative) log-likelihood or binomial deviance has similar tails as the SVM loss, giving zero penalty to points well inside their margin, and a

TABLE 12.1. The population minimizers for the different loss functions in Figure 12.4. Logistic regression uses the binomial log-likelihood or deviance. Linear discriminant analysis (Exercise 4.2) uses squared-error loss. The SVM hinge loss estimates the mode of the posterior class probabilities, whereas the others estimate a linear transformation of these probabilities.

| Loss Function                       | L[y, f(x)]                                             | Minimizing Function                               |
|-------------------------------------|--------------------------------------------------------|---------------------------------------------------|
| Binomial<br>Deviance                | $\log[1 + e^{-yf(x)}]$                                 | $f(x) = \log \frac{\Pr(Y = +1 x)}{\Pr(Y = -1 x)}$ |
| SVM Hinge<br>Loss                   | $[1 - y f(x)]_+$                                       | $f(x) = sign[\Pr(Y = +1 x) - \frac{1}{2}]$        |
| Squared<br>Error                    | $[y - f(x)]^2 = [1 - yf(x)]^2$                         | $f(x) = 2\Pr(Y = +1 x) - 1$                       |
| "Huberised"<br>Square<br>Hinge Loss | $-4yf(x), yf(x) < -1$<br>$[1-yf(x)]_{+}^{2}$ otherwise | $f(x) = 2\Pr(Y = +1 x) - 1$                       |

linear penalty to points on the wrong side and far away. Squared-error, on the other hand gives a quadratic penalty, and points well inside their own margin have a strong influence on the model as well. The squared hinge loss  $L(y, f) = [1 - yf]_+^2$  is like the quadratic, except it is zero for points inside their margin. It still rises quadratically in the left tail, and will be less robust than hinge or deviance to misclassified observations. Recently Rosset and Zhu (2007) proposed a "Huberized" version of the squared hinge loss, which converts smoothly to a linear loss at  $y f = -1$ .

We can characterize these loss functions in terms of what they are estimating at the population level. We consider minimizing  $EL(Y, f(X))$ . Table 12.1 summarizes the results. Whereas the hinge loss estimates the classifier  $G(x)$  itself, all the others estimate a transformation of the class posterior probabilities. The "Huberized" square hinge loss shares attractive properties of logistic regression (smooth loss function, estimates probabilities), as well as the SVM hinge loss (support points).

Formulation (12.25) casts the SVM as a regularized function estimation problem, where the coefficients of the linear expansion  $f(x) = \beta_0 + h(x)^T \beta$ are shrunk toward zero (excluding the constant). If  $h(x)$  represents a hierarchical basis having some ordered structure (such as ordered in roughness),

then the uniform shrinkage makes more sense if the rougher elements  $h_j$  in the vector  $h$  have smaller norm.

All the loss-functions in Table 12.1 except squared-error are so called "margin maximizing loss-functions" (Rosset et al., 2004b). This means that if the data are separable, then the limit of  $\hat{\beta}_{\lambda}$  in (12.25) as  $\lambda \to 0$  defines the optimal separating hyperplane<sup>1</sup>.

## 12.3.3 Function Estimation and Reproducing Kernels

Image /page/13/Picture/4 description: A yellow, cartoonish face with wide eyes and an open mouth is depicted in the style of Edvard Munch's 'The Scream'. The figure has its hands pressed to its temples, and its body is a dark color, possibly black or dark gray. The background is white.

Here we describe SVMs in terms of function estimation in reproducing kernel Hilbert spaces, where the kernel property abounds. This material is discussed in some detail in Section 5.8. This provides another view of the support vector classifier, and helps to clarify how it works.

Suppose the basis  $h$  arises from the (possibly finite) eigen-expansion of a positive definite kernel K,

$$
K(x, x') = \sum_{m=1}^{\infty} \phi_m(x)\phi_m(x')\delta_m
$$
 (12.26)

and  $h_m(x) = \sqrt{\delta_m} \phi_m(x)$ . Then with  $\theta_m = \sqrt{\delta_m} \beta_m$ , we can write (12.25) as

$$
\min_{\beta_0, \theta} \sum_{i=1}^N \left[ 1 - y_i (\beta_0 + \sum_{m=1}^\infty \theta_m \phi_m(x_i)) \right]_+ + \frac{\lambda}{2} \sum_{m=1}^\infty \frac{\theta_m^2}{\delta_m}.
$$
 (12.27)

Now (12.27) is identical in form to (5.49) on page 169 in Section 5.8, and the theory of reproducing kernel Hilbert spaces described there guarantees a finite-dimensional solution of the form

$$
f(x) = \beta_0 + \sum_{i=1}^{N} \alpha_i K(x, x_i).
$$
 (12.28)

In particular we see there an equivalent version of the optimization criterion (12.19) [Equation (5.67) in Section 5.8.2; see also Wahba et al. (2000)],

$$
\min_{\beta_0, \alpha} \sum_{i=1}^N (1 - y_i f(x_i))_+ + \frac{\lambda}{2} \alpha^T \mathbf{K} \alpha, \tag{12.29}
$$

where **K** is the  $N \times N$  matrix of kernel evaluations for all pairs of training features (Exercise 12.2).

These models are quite general, and include, for example, the entire family of smoothing splines, additive and interaction spline models discussed

<sup>&</sup>lt;sup>1</sup>For logistic regression with separable data,  $\hat{\beta}_{\lambda}$  diverges, but  $\hat{\beta}_{\lambda}/||\hat{\beta}_{\lambda}$  converges to the optimal separating direction.

in Chapters 5 and 9, and in more detail in Wahba (1990) and Hastie and Tibshirani (1990). They can be expressed more generally as

$$
\min_{f \in \mathcal{H}} \sum_{i=1}^{N} [1 - y_i f(x_i)]_+ + \lambda J(f),
$$
\n(12.30)

where  $H$  is the structured space of functions, and  $J(f)$  an appropriate regularizer on that space. For example, suppose  $H$  is the space of additive functions  $f(x) = \sum_{j=1}^{p} f_j(x_j)$ , and  $J(f) = \sum_j \int {\{f''_j(x_j)\}}^2 dx_j$ . Then the solution to  $(12.30)$  is an additive cubic spline, and has a kernel representation (12.28) with  $K(x, x') = \sum_{j=1}^{p} K_j(x_j, x'_j)$ . Each of the  $K_j$  is the kernel appropriate for the univariate smoothing spline in  $x_i$  (Wahba, 1990).

Conversely this discussion also shows that, for example, any of the kernels described in (12.22) above can be used with any convex loss function, and will also lead to a finite-dimensional representation of the form  $(12.28)$ . Figure 12.5 uses the same kernel functions as in Figure 12.3, except using the binomial log-likelihood as a loss function<sup>2</sup>. The fitted function is hence an estimate of the log-odds,

$$
\hat{f}(x) = \log \frac{\hat{Pr}(Y = +1|x)}{\hat{Pr}(Y = -1|x)}
$$

$$
= \hat{\beta}_0 + \sum_{i=1}^{N} \hat{\alpha}_i K(x, x_i)
$$
 (12.31)

or conversely we get an estimate of the class probabilities

$$
\hat{\Pr}(Y = +1|x) = \frac{1}{1 + e^{-\hat{\beta}_0 - \sum_{i=1}^{N} \hat{\alpha}_i K(x, x_i)}}.
$$
\n(12.32)

The fitted models are quite similar in shape and performance. Examples and more details are given in Section 5.8.

It does happen that for SVMs, a sizable fraction of the N values of  $\alpha_i$ can be zero (the nonsupport points). In the two examples in Figure 12.3, these fractions are 42% and 45%, respectively. This is a consequence of the piecewise linear nature of the first part of the criterion (12.25). The lower the class overlap (on the training data), the greater this fraction will be. Reducing  $\lambda$  will generally reduce the overlap (allowing a more flexible f). A small number of support points means that  $\hat{f}(x)$  can be evaluated more quickly, which is important at lookup time. Of course, reducing the overlap too much can lead to poor generalization.

 $2$ Ji Zhu assisted in the preparation of these examples.

Image /page/15/Figure/1 description: This image displays a scatter plot with two classes of data points, represented by orange circles and blue circles. The plot is divided into regions by decision boundaries, indicated by solid black lines and dashed black lines. A purple dashed line also appears to be a boundary or separator. The background is a grid of dots, colored light orange in some areas and light blue in others, suggesting a classification based on the regions. In the bottom left corner, there is text indicating performance metrics: 'Training Error: 0.190', 'Test Error: 0.263', and 'Bayes Error: 0.210'. The overall impression is a visualization of a machine learning classification model, likely a Support Vector Machine (SVM) with a non-linear kernel, showing its decision regions and error rates.

LR - Degree-4 Polynomial in Feature Space

LR - Radial Kernel in Feature Space

Image /page/15/Figure/4 description: This is a scatter plot showing two classes of data points, one in orange and one in light blue. The plot displays decision boundaries for a classification model, with solid black and dashed black lines representing different boundaries, and a dashed purple line also indicating a boundary. The background is filled with a dotted pattern, colored light orange in the upper left and light blue in the lower right, indicating the predicted class for different regions of the feature space. In the bottom left corner, there is text indicating performance metrics: "Training Error: 0.150", "Test Error: 0.221", and "Bayes Error: 0.210". The title of the plot is "ER - Radial Kernels in Feature Space".

FIGURE 12.5. The logistic regression versions of the SVM models in Figure 12.3, using the identical kernels and hence penalties, but the log-likelihood loss instead of the SVM loss function. The two broken contours correspond to posterior probabilities of 0.75 and 0.25 for the  $+1$  class (or vice versa). The broken purple curve in the background is the Bayes decision boundary.

| Method          | Test Error (SE)   |                    |
|-----------------|-------------------|--------------------|
|                 | No Noise Features | Six Noise Features |
| 1 SV Classifier | 0.450 (0.003)     | 0.472 (0.003)      |
| 2 SVM/poly 2    | 0.078 (0.003)     | 0.152 (0.004)      |
| 3 SVM/poly 5    | 0.180 (0.004)     | 0.370 (0.004)      |
| 4 SVM/poly 10   | 0.230 (0.003)     | 0.434 (0.002)      |
| 5 BRUTO         | 0.084 (0.003)     | 0.090 (0.003)      |
| 6 MARS          | 0.156 (0.004)     | 0.173 (0.005)      |
| Bayes           | 0.029             | 0.029              |

TABLE 12.2. Skin of the orange: Shown are mean (standard error of the mean) of the test error over 50 simulations. BRUTO fits an additive spline model adaptively, while MARS fits a low-order interaction model adaptively.

## 12.3.4 SVMs and the Curse of Dimensionality

In this section, we address the question of whether SVMs have some edge on the curse of dimensionality. Notice that in expression (12.23) we are not allowed a fully general inner product in the space of powers and products. For example, all terms of the form  $2X_jX'_j$  are given equal weight, and the kernel cannot adapt itself to concentrate on subspaces. If the number of features p were large, but the class separation occurred only in the linear subspace spanned by say  $X_1$  and  $X_2$ , this kernel would not easily find the structure and would suffer from having many dimensions to search over. One would have to build knowledge about the subspace into the kernel; that is, tell it to ignore all but the first two inputs. If such knowledge were available a priori, much of statistical learning would be made much easier. A major goal of adaptive methods is to discover such structure.

We support these statements with an illustrative example. We generated 100 observations in each of two classes. The first class has four standard normal independent features  $X_1, X_2, X_3, X_4$ . The second class also has four standard normal independent features, but conditioned on  $9 \le \sum X_j^2 \le 16$ . This is a relatively easy problem. As a second harder problem, we augmented the features with an additional six standard Gaussian noise features. Hence the second class almost completely surrounds the first, like the skin surrounding the orange, in a four-dimensional subspace. The Bayes error rate for this problem is 0.029 (irrespective of dimension). We generated 1000 test observations to compare different procedures. The average test errors over 50 simulations, with and without noise features, are shown in Table 12.2.

Line 1 uses the support vector classifier in the original feature space. Lines 2–4 refer to the support vector machine with a 2-, 5- and 10-dimensional polynomial kernel. For all support vector procedures, we chose the cost parameter  $C$  to minimize the test error, to be as fair as possible to the

Image /page/17/Figure/1 description: This figure displays four plots, each representing the test error of an SVM with a radial kernel for different gamma values: 5, 1, 0.5, and 0.1. The x-axis for all plots is labeled 'C' and uses a logarithmic scale, ranging from 1e-01 to 1e+03. The y-axis for all plots is labeled 'Test Error' and ranges from 0.20 to 0.35. Each plot shows the test error as a function of C for a specific gamma value. A dashed purple line is present across all plots at a test error of approximately 0.21.

#### Test Error Curves − SVM with Radial Kernel

**FIGURE 12.6.** Test-error curves as a function of the cost parameter  $C$  for the radial-kernel SVM classifier on the mixture data. At the top of each plot is the scale parameter  $\gamma$  for the radial kernel:  $K_{\gamma}(x, y) = \exp{-\gamma ||x - y||^2}$ . The optimal value for C depends quite strongly on the scale of the kernel. The Bayes error rate is indicated by the broken horizontal lines.

method. Line 5 fits an additive spline model to the  $(-1, +1)$  response by least squares, using the BRUTO algorithm for additive models, described in Hastie and Tibshirani (1990). Line 6 uses MARS (multivariate adaptive regression splines) allowing interaction of all orders, as described in Chapter 9; as such it is comparable with the SVM/poly 10. Both BRUTO and MARS have the ability to ignore redundant variables. Test error was not used to choose the smoothing parameters in either of lines 5 or 6.

In the original feature space, a hyperplane cannot separate the classes, and the support vector classifier (line 1) does poorly. The polynomial support vector machine makes a substantial improvement in test error rate, but is adversely affected by the six noise features. It is also very sensitive to the choice of kernel: the second degree polynomial kernel (line 2) does best, since the true decision boundary is a second-degree polynomial. However, higher-degree polynomial kernels (lines 3 and 4) do much worse. BRUTO performs well, since the boundary is additive. BRUTO and MARS adapt well: their performance does not deteriorate much in the presence of noise.

## 12.3.5 A Path Algorithm for the SVM Classifier

Image /page/17/Picture/7 description: A yellow, cartoonish depiction of Edvard Munch's "The Scream" painting. The figure has wide, staring eyes, an open mouth, and hands pressed to its ears. The background is black.

The regularization parameter for the SVM classifier is the cost parameter C, or its inverse  $\lambda$  in (12.25). Common usage is to set C high, leading often to somewhat overfit classifiers.

Figure 12.6 shows the test error on the mixture data as a function of C, using different radial-kernel parameters  $\gamma$ . When  $\gamma = 5$  (narrow peaked kernels), the heaviest regularization (small C) is called for. With  $\gamma = 1$ 

Image /page/18/Figure/1 description: The image displays two plots related to a support vector machine. The left plot is a scatter plot showing data points classified into two groups, orange circles and blue squares, with labels 1 through 12. It illustrates the decision boundary f(x) = 0, and the margin boundaries f(x) = +1 and f(x) = -1, with an indication of the margin width as 1/||β||. The right plot is a line graph showing the relationship between lambda (λ) on the y-axis and alpha\_i(λ) on the x-axis, with different colored lines representing different data points, and the numbers 1 through 12 labeling specific points on these lines.

FIGURE 12.7. A simple example illustrates the SVM path algorithm. (left panel:) This plot illustrates the state of the model at  $\lambda = 0.5$ . The " + 1" points are orange, the "−1" blue.  $\lambda = 1/2$ , and the width of the soft margin is  $2/||\beta|| = 2 \times 0.587$ . Two blue points  $\{3, 5\}$  are misclassified, while the two orange points  $\{10, 12\}$  are correctly classified, but on the wrong side of their margin  $f(x) = +1$ ; each of these has  $y_i f(x_i) < 1$ . The three square shaped points {2,6,7} are exactly on their margins. (right panel:) This plot shows the piecewise linear profiles  $\alpha_i(\lambda)$ . The horizontal broken line at  $\lambda = 1/2$  indicates the state of the  $\alpha_i$ for the model in the left plot.

(the value used in Figure 12.3), an intermediate value of  $C$  is required. Clearly in situations such as these, we need to determine a good choice for C, perhaps by cross-validation. Here we describe a path algorithm (in the spirit of Section 3.8) for efficiently fitting the entire sequence of SVM models obtained by varying C.

It is convenient to use the loss+penalty formulation (12.25), along with Figure 12.4. This leads to a solution for  $\beta$  at a given value of  $\lambda$ :

$$
\beta_{\lambda} = \frac{1}{\lambda} \sum_{i=1}^{N} \alpha_i y_i x_i.
$$
\n(12.33)

The  $\alpha_i$  are again Lagrange multipliers, but in this case they all lie in [0, 1].

Figure 12.7 illustrates the setup. It can be shown that the KKT optimality conditions imply that the labeled points  $(x_i, y_i)$  fall into three distinct groups:

- Observations correctly classified and outside their margins. They have  $y_i f(x_i) > 1$ , and Lagrange multipliers  $\alpha_i = 0$ . Examples are the orange points 8, 9 and 11, and the blue points 1 and 4.
- Observations sitting on their margins with  $y_i f(x_i) = 1$ , with Lagrange multipliers  $\alpha_i \in [0, 1]$ . Examples are the orange 7 and the blue 2 and 8.
- Observations inside their margins have  $y_i f(x_i) < 1$ , with  $\alpha_i = 1$ . Examples are the blue 3 and 5, and the orange 10 and 12.

The idea for the path algorithm is as follows. Initially  $\lambda$  is large, the margin  $1/||\beta_{\lambda}||$  is wide, and all points are inside their margin and have  $\alpha_i = 1$ . As  $\lambda$  decreases,  $1/||\beta_{\lambda}||$  decreases, and the margin gets narrower. Some points will move from inside their margins to outside their margins, and their  $\alpha_i$  will change from 1 to 0. By continuity of the  $\alpha_i(\lambda)$ , these points will *linger* on the margin during this transition. From  $(12.33)$  we see that the points with  $\alpha_i = 1$  make fixed contributions to  $\beta(\lambda)$ , and those with  $\alpha_i = 0$  make no contribution. So all that changes as  $\lambda$  decreases are the  $\alpha_i \in [0, 1]$  of those (small number) of points on the margin. Since all these points have  $y_i f(x_i) = 1$ , this results in a small set of linear equations that prescribe how  $\alpha_i(\lambda)$  and hence  $\beta_{\lambda}$  changes during these transitions. This results in piecewise linear paths for each of the  $\alpha_i(\lambda)$ . The breaks occur when points cross the margin. Figure 12.7 (right panel) shows the  $\alpha_i(\lambda)$ profiles for the small example in the left panel.

Although we have described this for linear SVMs, exactly the same idea works for nonlinear models, in which (12.33) is replaced by

$$
f_{\lambda}(x) = \frac{1}{\lambda} \sum_{i=1}^{N} \alpha_i y_i K(x, x_i).
$$
 (12.34)

Details can be found in Hastie et al. (2004). An R package svmpath is available on CRAN for fitting these models.

## 12.3.6 Support Vector Machines for Regression

In this section we show how SVMs can be adapted for regression with a quantitative response, in ways that inherit some of the properties of the SVM classifier. We first discuss the linear regression model

$$
f(x) = x^T \beta + \beta_0,\tag{12.35}
$$

and then handle nonlinear generalizations. To estimate  $\beta$ , we consider minimization of

$$
H(\beta, \beta_0) = \sum_{i=1}^{N} V(y_i - f(x_i)) + \frac{\lambda}{2} ||\beta||^2,
$$
\n(12.36)

Image /page/20/Figure/1 description: The image displays two plots side-by-side. The left plot, titled "Vǫ(r)", shows a piecewise linear function. The x-axis is labeled "r" and ranges from -4 to 4. The y-axis ranges from -1 to 4. The function is zero between -ε and ε, decreases linearly with a slope of -1 for r < -ε, and increases linearly with a slope of 1 for r > ε. The right plot, titled "VH(r)", shows a quadratic function, resembling a parabola opening upwards. The x-axis is also labeled "r" and ranges from -4 to 4. The y-axis ranges from 0 to 12. The function has a minimum value of 0 at r=0. It decreases for r < 0 and increases for r > 0, with dashed lines indicating tangent lines at -c and c, suggesting a quadratic form like r^2.

**FIGURE 12.8.** The left panel shows the  $\epsilon$ -insensitive error function used by the support vector regression machine. The right panel shows the error function used in Huber's robust regression (blue curve). Beyond |c|, the function changes from quadratic to linear.

where

$$
V_{\epsilon}(r) = \begin{cases} 0 & \text{if } |r| < \epsilon, \\ |r| - \epsilon, & \text{otherwise.} \end{cases}
$$
 (12.37)

This is an " $\epsilon$ -insensitive" error measure, ignoring errors of size less than  $\epsilon$  (left panel of Figure 12.8). There is a rough analogy with the support vector classification setup, where points on the correct side of the decision boundary and far away from it, are ignored in the optimization. In regression, these "low error" points are the ones with small residuals.

It is interesting to contrast this with error measures used in robust regression in statistics. The most popular, due to Huber (1964), has the form

$$
V_H(r) = \begin{cases} r^2/2 & \text{if } |r| \le c, \\ c|r| - c^2/2, & |r| > c, \end{cases}
$$
(12.38)

shown in the right panel of Figure 12.8. This function reduces from quadratic to linear the contributions of observations with absolute residual greater than a prechosen constant c. This makes the fitting less sensitive to outliers. The support vector error measure (12.37) also has linear tails (beyond  $\epsilon$ ), but in addition it flattens the contributions of those cases with small residuals.

If  $\hat{\beta}$ ,  $\hat{\beta}_0$  are the minimizers of H, the solution function can be shown to have the form

$$
\hat{\beta} = \sum_{i=1}^{N} (\hat{\alpha}_i^* - \hat{\alpha}_i) x_i,
$$
\n(12.39)

$$
\hat{f}(x) = \sum_{i=1}^{N} (\hat{\alpha}_i^* - \hat{\alpha}_i) \langle x, x_i \rangle + \beta_0, \qquad (12.40)
$$

where  $\hat{\alpha}_i, \hat{\alpha}_i^*$  are positive and solve the quadratic programming problem

$$
\min_{\alpha_i, \alpha_i^*} \epsilon \sum_{i=1}^N (\alpha_i^* + \alpha_i) - \sum_{i=1}^N y_i (\alpha_i^* - \alpha_i) + \frac{1}{2} \sum_{i,i'=1}^N (\alpha_i^* - \alpha_i) (\alpha_{i'}^* - \alpha_{i'}) \langle x_i, x_{i'} \rangle
$$

subject to the constraints

$$
0 
\leq 
\alpha_i, 
\alpha_i^* 
\leq 1/\lambda,
$$
  

$$
\sum_{i=1}^N (
\alpha_i^* 
-\alpha_i) = 0,
$$
 (12.41)  

$$
\alpha_i 
\alpha_i^* = 0.
$$

Due to the nature of these constraints, typically only a subset of the solution values  $(\hat{\alpha}_i^* - \hat{\alpha}_i)$  are nonzero, and the associated data values are called the support vectors. As was the case in the classification setting, the solution depends on the input values only through the inner products  $\langle x_i, x_{i'} \rangle$ . Thus we can generalize the methods to richer spaces by defining an appropriate inner product, for example, one of those defined in (12.22).

Note that there are parameters,  $\epsilon$  and  $\lambda$ , associated with the criterion (12.36). These seem to play different roles.  $\epsilon$  is a parameter of the loss function  $V_{\epsilon}$ , just like c is for  $V_H$ . Note that both  $V_{\epsilon}$  and  $V_H$  depend on the scale of y and hence r. If we scale our response (and hence use  $V_H(r/\sigma)$  and  $V_{\epsilon}(r/\sigma)$  instead), then we might consider using preset values for c and  $\epsilon$  (the value  $c = 1.345$  achieves 95% efficiency for the Gaussian). The quantity  $\lambda$ is a more traditional regularization parameter, and can be estimated for example by cross-validation.

## 12.3.7 Regression and Kernels

As discussed in Section 12.3.3, this kernel property is not unique to support vector machines. Suppose we consider approximation of the regression function in terms of a set of basis functions  $\{h_m(x)\}\$ ,  $m = 1, 2, \ldots, M$ :

$$
f(x) = \sum_{m=1}^{M} \beta_m h_m(x) + \beta_0.
$$
 (12.42)

To estimate  $\beta$  and  $\beta_0$  we minimize

$$
H(\beta, \beta_0) = \sum_{i=1}^{N} V(y_i - f(x_i)) + \frac{\lambda}{2} \sum \beta_m^2
$$
 (12.43)

for some general error measure  $V(r)$ . For any choice of  $V(r)$ , the solution  $\hat{f}(x) = \sum \hat{\beta}_m h_m(x) + \hat{\beta}_0$  has the form

$$
\hat{f}(x) = \sum_{i=1}^{N} \hat{a}_i K(x, x_i)
$$
\n(12.44)

with  $K(x, y) = \sum_{m=1}^{M} h_m(x)h_m(y)$ . Notice that this has the same form as both the radial basis function expansion and a regularization estimate, discussed in Chapters 5 and 6.

For concreteness, let's work out the case  $V(r) = r^2$ . Let **H** be the  $N \times M$ basis matrix with *imth* element  $h_m(x_i)$ , and suppose that  $M > N$  is large. For simplicity we assume that  $\beta_0 = 0$ , or that the constant is absorbed in h; see Exercise 12.3 for an alternative.

We estimate  $\beta$  by minimizing the penalized least squares criterion

$$
H(\beta) = (\mathbf{y} - \mathbf{H}\beta)^{T}(\mathbf{y} - \mathbf{H}\beta) + \lambda \|\beta\|^{2}.
$$
 (12.45)

The solution is

$$
\hat{\mathbf{y}} = \mathbf{H}\hat{\beta} \tag{12.46}
$$

with  $\hat{\beta}$  determined by

$$
-\mathbf{H}^{T}(\mathbf{y} - \mathbf{H}\hat{\beta}) + \lambda\hat{\beta} = 0.
$$
 (12.47)

From this it appears that we need to evaluate the  $M \times M$  matrix of inner products in the transformed space. However, we can premultiply by H to give

$$
\mathbf{H}\hat{\beta} = (\mathbf{H}\mathbf{H}^T + \lambda \mathbf{I})^{-1} \mathbf{H}\mathbf{H}^T \mathbf{y}.
$$
 (12.48)

The  $N \times N$  matrix  $\mathbf{H}\mathbf{H}^T$  consists of inner products between pairs of observations  $i, i'$ ; that is, the evaluation of an inner product kernel  $\{\mathbf{H}\mathbf{H}^T\}_{i,i'} =$  $K(x_i, x_{i'})$ . It is easy to show (12.44) directly in this case, that the predicted values at an arbitrary  $x$  satisfy

$$
\hat{f}(x) = h(x)^{T} \hat{\beta}
$$

$$
= \sum_{i=1}^{N} \hat{\alpha}_{i} K(x, x_{i}), \qquad (12.49)
$$

where  $\hat{\alpha} = (\mathbf{H}\mathbf{H}^T + \lambda \mathbf{I})^{-1}\mathbf{y}$ . As in the support vector machine, we need not specify or evaluate the large set of functions  $h_1(x), h_2(x), \ldots, h_M(x)$ . Only the inner product kernel  $K(x_i, x_{i'})$  need be evaluated, at the N training points for each  $i, i'$  and at points x for predictions there. Careful choice of  $h_m$  (such as the eigenfunctions of particular, easy-to-evaluate kernels K) means, for example, that  $HH<sup>T</sup>$  can be computed at a cost of  $N^2/2$ evaluations of K, rather than the direct cost  $N^2M$ .

Note, however, that this property depends on the choice of squared norm  $\|\beta\|^2$  in the penalty. It does not hold, for example, for the  $L_1$  norm  $|\beta|$ , which may lead to a superior model.

#### 12.3.8 Discussion

The support vector machine can be extended to multiclass problems, essentially by solving many two-class problems. A classifier is built for each pair of classes, and the final classifier is the one that dominates the most (Kressel, 1999; Friedman, 1996; Hastie and Tibshirani, 1998). Alternatively, one could use the multinomial loss function along with a suitable kernel, as in Section 12.3.3. SVMs have applications in many other supervised and unsupervised learning problems. At the time of this writing, empirical evidence suggests that it performs well in many real learning problems.

Finally, we mention the connection of the support vector machine and structural risk minimization (7.9). Suppose the training points (or their basis expansion) are contained in a sphere of radius R, and let  $G(x)$  =  $sign[f(x)] = sign[\beta^{T}x + \beta_0]$  as in (12.2). Then one can show that the class of functions  $\{G(x), ||\beta|| \leq A\}$  has VC-dimension h satisfying

$$
h \le R^2 A^2. \tag{12.50}
$$

If  $f(x)$  separates the training data, optimally for  $\|\beta\| \leq A$ , then with probability at least  $1 - \eta$  over training sets (Vapnik, 1996, page 139):

$$
Error_{Test} \le 4 \frac{h[\log(2N/h) + 1] - \log(\eta/4)}{N}.
$$
 (12.51)

The support vector classifier was one of the first practical learning procedures for which useful bounds on the VC dimension could be obtained, and hence the SRM program could be carried out. However in the derivation, balls are put around the data points—a process that depends on the observed values of the features. Hence in a strict sense, the VC complexity of the class is not fixed a priori, before seeing the features.

The regularization parameter C controls an upper bound on the VC dimension of the classifier. Following the SRM paradigm, we could choose C by minimizing the upper bound on the test error, given in  $(12.51)$ . However, it is not clear that this has any advantage over the use of cross-validation for choice of C.

## 12.4 Generalizing Linear Discriminant Analysis

In Section 4.3 we discussed linear discriminant analysis (LDA), a fundamental tool for classification. For the remainder of this chapter we discuss a class of techniques that produce better classifiers than LDA by directly generalizing LDA.

Some of the virtues of LDA are as follows:

• It is a simple prototype classifier. A new observation is classified to the class with closest centroid. A slight twist is that distance is measured in the Mahalanobis metric, using a pooled covariance estimate.

- LDA is the estimated Bayes classifier if the observations are multivariate Gaussian in each class, with a common covariance matrix. Since this assumption is unlikely to be true, this might not seem to be much of a virtue.
- The decision boundaries created by LDA are linear, leading to decision rules that are simple to describe and implement.
- LDA provides natural low-dimensional views of the data. For example, Figure 12.12 is an informative two-dimensional view of data in 256 dimensions with ten classes.
- Often LDA produces the best classification results, because of its simplicity and low variance. LDA was among the top three classifiers for 7 of the 22 datasets studied in the STATLOG project (Michie et al.,  $1994)^3$ .

Unfortunately the simplicity of LDA causes it to fail in a number of situations as well:

- Often linear decision boundaries do not adequately separate the classes. When  $N$  is large, it is possible to estimate more complex decision boundaries. Quadratic discriminant analysis (QDA) is often useful here, and allows for quadratic decision boundaries. More generally we would like to be able to model irregular decision boundaries.
- The aforementioned shortcoming of LDA can often be paraphrased by saying that a single prototype per class is insufficient. LDA uses a single prototype (class centroid) plus a common covariance matrix to describe the spread of the data in each class. In many situations, several prototypes are more appropriate.
- At the other end of the spectrum, we may have way too many (correlated) predictors, for example, in the case of digitized analogue signals and images. In this case LDA uses too many parameters, which are estimated with high variance, and its performance suffers. In cases such as this we need to restrict or regularize LDA even further.

In the remainder of this chapter we describe a class of techniques that attend to all these issues by generalizing the LDA model. This is achieved largely by three different ideas.

The first idea is to recast the LDA problem as a linear regression problem. Many techniques exist for generalizing linear regression to more flexible, nonparametric forms of regression. This in turn leads to more flexible forms of discriminant analysis, which we call FDA. In most cases of interest, the

<sup>3</sup>This study predated the emergence of SVMs.

regression procedures can be seen to identify an enlarged set of predictors via basis expansions. FDA amounts to LDA in this enlarged space, the same paradigm used in SVMs.

In the case of too many predictors, such as the pixels of a digitized image, we do not want to expand the set: it is already too large. The second idea is to fit an LDA model, but penalize its coefficients to be smooth or otherwise coherent in the spatial domain, that is, as an image. We call this procedure penalized discriminant analysis or PDA. With FDA itself, the expanded basis set is often so large that regularization is also required (again as in SVMs). Both of these can be achieved via a suitably regularized regression in the context of the FDA model.

The third idea is to model each class by a mixture of two or more Gaussians with different centroids, but with every component Gaussian, both within and between classes, sharing the same covariance matrix. This allows for more complex decision boundaries, and allows for subspace reduction as in LDA. We call this extension mixture discriminant analysis or MDA.

All three of these generalizations use a common framework by exploiting their connection with LDA.

## 12.5 Flexible Discriminant Analysis

In this section we describe a method for performing LDA using linear regression on derived responses. This in turn leads to nonparametric and flexible alternatives to LDA. As in Chapter 4, we assume we have observations with a quantitative response G falling into one of K classes  $\mathcal{G} = \{1, \ldots, K\},\$ each having measured features X. Suppose  $\theta : \mathcal{G} \mapsto \mathbb{R}^1$  is a function that assigns scores to the classes, such that the transformed class labels are optimally predicted by linear regression on  $X$ : If our training sample has the form  $(g_i, x_i)$ ,  $i = 1, 2, ..., N$ , then we solve

$$
\min_{\beta,\theta} \sum_{i=1}^{N} \left( \theta(g_i) - x_i^T \beta \right)^2, \tag{12.52}
$$

with restrictions on  $\theta$  to avoid a trivial solution (mean zero and unit variance over the training data). This produces a one-dimensional separation between the classes.

More generally, we can find up to  $L \leq K - 1$  sets of independent scorings for the class labels,  $\theta_1, \theta_2, \ldots, \theta_L$ , and L corresponding linear maps  $\eta_\ell(X) =$  $X^T \beta_{\ell}, \ell = 1, \ldots, L$ , chosen to be optimal for multiple regression in  $\mathbb{R}^p$ . The scores  $\theta_{\ell}(g)$  and the maps  $\beta_{\ell}$  are chosen to minimize the average squared residual,

$$
ASR = \frac{1}{N} \sum_{\ell=1}^{L} \left[ \sum_{i=1}^{N} \left( \theta_{\ell}(g_i) - x_i^T \beta_{\ell} \right)^2 \right].
$$
 (12.53)

The set of scores are assumed to be mutually orthogonal and normalized with respect to an appropriate inner product to prevent trivial zero solutions.

Why are we going down this road? It can be shown that the sequence of discriminant (canonical) vectors  $\nu_{\ell}$  derived in Section 4.3.3 are identical to the sequence  $\beta_{\ell}$  up to a constant (Mardia et al., 1979; Hastie et al., 1995). Moreover, the Mahalanobis distance of a test point  $x$  to the  $k$ th class centroid  $\hat{\mu}_k$  is given by

$$
\delta_J(x, \hat{\mu}_k) = \sum_{\ell=1}^{K-1} w_\ell (\hat{\eta}_\ell(x) - \bar{\eta}_\ell^k)^2 + D(x), \qquad (12.54)
$$

where  $\bar{\eta}_{\ell}^{k}$  is the mean of the  $\hat{\eta}_{\ell}(x_i)$  in the kth class, and  $D(x)$  does not depend on k. Here  $w_{\ell}$  are coordinate weights that are defined in terms of the mean squared residual  $r_{\ell}^2$  of the  $\ell$ th optimally scored fit

$$
w_{\ell} = \frac{1}{r_{\ell}^2 (1 - r_{\ell}^2)}.\tag{12.55}
$$

In Section 4.3.2 we saw that these canonical distances are all that is needed for classification in the Gaussian setup, with equal covariances in each class. To summarize:

LDA can be performed by a sequence of linear regressions, followed by classification to the closest class centroid in the space of fits. The analogy applies both to the reduced rank version, or the full rank case when  $L = K - 1$ .

The real power of this result is in the generalizations that it invites. We can replace the linear regression fits  $\eta_{\ell}(x) = x^T \beta_{\ell}$  by far more flexible, nonparametric fits, and by analogy achieve a more flexible classifier than LDA. We have in mind generalized additive fits, spline functions, MARS models and the like. In this more general form the regression problems are defined via the criterion

$$
ASR(\{\theta_{\ell}, \eta_{\ell}\}_{\ell=1}^{L}) = \frac{1}{N} \sum_{\ell=1}^{L} \left[ \sum_{i=1}^{N} \left( \theta_{\ell}(g_i) - \eta_{\ell}(x_i) \right)^2 + \lambda J(\eta_{\ell}) \right], \quad (12.56)
$$

where  $J$  is a regularizer appropriate for some forms of nonparametric regression, such as smoothing splines, additive splines and lower-order ANOVA spline models. Also included are the classes of functions and associated penalties generated by kernels, as in Section 12.3.3.

Before we describe the computations involved in this generalization, let us consider a very simple example. Suppose we use degree-2 polynomial regression for each  $\eta_{\ell}$ . The decision boundaries implied by the (12.54) will be quadratic surfaces, since each of the fitted functions is quadratic, and as

Image /page/27/Figure/1 description: A scatter plot displays two sets of data points, one in light blue and the other in orange. Both sets of points are clustered around the origin. An ellipse, drawn in black, encloses a majority of the orange points and some of the blue points. Inside the black ellipse, a dashed purple circle is also centered around the origin, enclosing a denser cluster of orange points. The x and y axes are labeled with tick marks at -2, 0, and 2.

**FIGURE 12.9.** The data consist of 50 points generated from each of  $N(0, I)$  and  $N(0, \frac{9}{4}I)$ . The solid black ellipse is the decision boundary found by FDA using degree-two polynomial regression. The dashed purple circle is the Bayes decision boundary.

in LDA their squares cancel out when comparing distances. We could have achieved identical quadratic boundaries in a more conventional way, by augmenting our original predictors with their squares and cross-products. In the enlarged space one performs an LDA, and the linear boundaries in the enlarged space map down to quadratic boundaries in the original space. A classic example is a pair of multivariate Gaussians centered at the origin, one having covariance matrix I, and the other  $cI$  for  $c > 1$ ; Figure 12.9 illustrates. The Bayes decision boundary is the sphere  $||x|| = \frac{pc \log c}{2(c-1)}$ , which is a linear boundary in the enlarged space.

Many nonparametric regression procedures operate by generating a basis expansion of derived variables, and then performing a linear regression in the enlarged space. The MARS procedure (Chapter 9) is exactly of this form. Smoothing splines and additive spline models generate an extremely large basis set  $(N \times p)$  basis functions for additive splines), but then perform a penalized regression fit in the enlarged space. SVMs do as well; see also the kernel-based regression example in Section 12.3.7. FDA in this case can be shown to perform a penalized linear discriminant analysis in the enlarged space. We elaborate in Section 12.6. Linear boundaries in the enlarged space map down to nonlinear boundaries in the reduced space. This is exactly the same paradigm that is used with support vector machines (Section 12.3).

We illustrate FDA on the speech recognition example used in Chapter 4.), with  $K = 11$  classes and  $p = 10$  predictors. The classes correspond to

Image /page/28/Figure/1 description: The image displays two scatter plots side-by-side, both titled with "Coordinate 2 for Training Data" on the y-axis and "Coordinate 1 for Training Data" on the x-axis. The left plot is titled "Linear Discriminant Analysis" and shows data points scattered across the graph with various colors, indicating different clusters. The right plot is titled "Flexible Discriminant Analysis -- Bruto" and also shows scattered data points in different colors, but these points appear more distinctly clustered and separated compared to the left plot. The points in both plots are represented by circles, some filled and some hollow.

FIGURE 12.10. The left plot shows the first two LDA canonical variates for the vowel training data. The right plot shows the corresponding projection when FDA/BRUTO is used to fit the model; plotted are the fitted regression functions  $\hat{\eta}_1(x_i)$  and  $\hat{\eta}_2(x_i)$ . Notice the improved separation. The colors represent the eleven different vowel sounds.

11 vowel sounds, each contained in 11 different words. Here are the words, preceded by the symbols that represent them:

|          |                 | Vowel Word   Vowel Word   Vowel Word   Vowel Word |                    |     |            |       |
|----------|-----------------|---------------------------------------------------|--------------------|-----|------------|-------|
| $\vdots$ | $heed$   O      |                                                   | hod                | hid | $\perp$ C: | hoard |
| E        | $head \cup U$   |                                                   | hood $\parallel$ A | had | 11:        | who'd |
| a:       | hard $\vert$ 3: |                                                   | heard $\mid Y$     | hud |            |       |

Each of eight speakers spoke each word six times in the training set, and likewise seven speakers in the test set. The ten predictors are derived from the digitized speech in a rather complicated way, but standard in the speech recognition world. There are thus 528 training observations, and 462 test observations. Figure 12.10 shows two-dimensional projections produced by LDA and FDA. The FDA model used adaptive additive-spline regression functions to model the  $\eta_{\ell}(x)$ , and the points plotted in the right plot have coordinates  $\hat{\eta}_1(x_i)$  and  $\hat{\eta}_2(x_i)$ . The routine used in S-PLUS is called bruto, hence the heading on the plot and in Table 12.3. We see that flexible modeling has helped to separate the classes in this case. Table 12.3 shows training and test error rates for a number of classification techniques. FDA/MARS refers to Friedman's multivariate adaptive regression splines; degree  $= 2$ means pairwise products are permitted. Notice that for FDA/MARS, the best classification results are obtained in a reduced-rank subspace.

TABLE 12.3. Vowel recognition data performance results. The results for neural networks are the best among a much larger set, taken from a neural network archive. The notation FDA/BRUTO refers to the regression method used with FDA.

| Technique |                                          | Error Rates |      |
|-----------|------------------------------------------|-------------|------|
|           |                                          | Training    | Test |
| (1)       | LDA                                      | 0.32        | 0.56 |
|           | Softmax                                  | 0.48        | 0.67 |
| (2)       | QDA                                      | 0.01        | 0.53 |
| (3)       | CART                                     | 0.05        | 0.56 |
| (4)       | CART (linear combination splits)         | 0.05        | 0.54 |
| (5)       | Single-layer perceptron                  |             | 0.67 |
| (6)       | Multi-layer perceptron (88 hidden units) |             | 0.49 |
| (7)       | Gaussian node network (528 hidden units) |             | 0.45 |
| (8)       | Nearest neighbor                         |             | 0.44 |
| (9)       | FDA/BRUTO                                | 0.06        | 0.44 |
|           | Softmax                                  | 0.11        | 0.50 |
| (10)      | FDA/MARS (degree = 1)                    | 0.09        | 0.45 |
|           | Best reduced dimension (=2)              | 0.18        | 0.42 |
|           | Softmax                                  | 0.14        | 0.48 |
| (11)      | FDA/MARS (degree = 2)                    | 0.02        | 0.42 |
|           | Best reduced dimension (=6)              | 0.13        | 0.39 |
|           | Softmax                                  | 0.10        | 0.50 |

## 12.5.1 Computing the FDA Estimates

The computations for the FDA coordinates can be simplified in many important cases, in particular when the nonparametric regression procedure can be represented as a linear operator. We will denote this operator by  $\mathbf{S}_{\lambda}$ ; that is,  $\hat{\mathbf{y}} = \mathbf{S}_{\lambda} \mathbf{y}$ , where y is the vector of responses and  $\hat{\mathbf{y}}$  the vector of fits. Additive splines have this property, if the smoothing parameters are fixed, as does MARS once the basis functions are selected. The subscript  $\lambda$ denotes the entire set of smoothing parameters. In this case optimal scoring is equivalent to a canonical correlation problem, and the solution can be computed by a single eigen-decomposition. This is pursued in Exercise 12.6, and the resulting algorithm is presented here.

We create an  $N \times K$  indicator response matrix Y from the responses  $g_i$ , such that  $y_{ik} = 1$  if  $g_i = k$ , otherwise  $y_{ik} = 0$ . For a five-class problem Y might look like the following:

|           | $C_1$ | $C_2$ | $C_3$ | $C_4$ | $C_5$ |
|-----------|-------|-------|-------|-------|-------|
| $g_1 = 2$ | 0     | 1     | 0     | 0     | 0     |
| $g_2 = 1$ | 1     | 0     | 0     | 0     | 0     |
| $g_3 = 1$ | 1     | 0     | 0     | 0     | 0     |
| $g_4 = 5$ | 0     | 0     | 0     | 0     | 1     |
| $g_5 = 4$ | 0     | 0     | 0     | 1     | 0     |
| ...       |       |       | ...   |       |       |
| $g_N = 3$ | 0     | 0     | 1     | 0     | 0     |

Here are the computational steps:

- 1. Multivariate nonparametric regression. Fit a multiresponse, adaptive nonparametric regression of Y on X, giving fitted values Y. Let  $S_{\lambda}$ be the linear operator that fits the final chosen model, and  $\eta^*(x)$  be the vector of fitted regression functions.
- 2. Optimal scores. Compute the eigen-decomposition of  $\mathbf{Y}^T \hat{\mathbf{Y}} = \mathbf{Y}^T \mathbf{S}_{\lambda} \mathbf{Y}$ , where the eigenvectors  $\Theta$  are normalized:  $\Theta^T \mathbf{D}_{\pi} \Theta = \mathbf{I}$ . Here  $\mathbf{D}_{\pi} =$  $\mathbf{Y}^T\mathbf{Y}/N$  is a diagonal matrix of the estimated class prior probabilities.
- 3. Update the model from step 1 using the optimal scores:  $\eta(x) = \mathbf{\Theta}^T \eta^*(x)$ .

The first of the K functions in  $\eta(x)$  is the constant function— a trivial solution; the remaining  $K-1$  functions are the discriminant functions. The constant function, along with the normalization, causes all the remaining functions to be centered.

Again  $\mathbf{S}_{\lambda}$  can correspond to any regression method. When  $\mathbf{S}_{\lambda} = \mathbf{H}_X$ , the linear regression projection operator, then FDA is linear discriminant analysis. The software that we reference in the Computational Considerations section on page 455 makes good use of this modularity; the fda function has a method- argument that allows one to supply *any* regression function, as long as it follows some natural conventions. The regression functions we provide allow for polynomial regression, adaptive additive models and MARS. They all efficiently handle multiple responses, so step (1) is a single call to a regression routine. The eigen-decomposition in step (2) simultaneously computes all the optimal scoring functions.

In Section 4.2 we discussed the pitfalls of using linear regression on an indicator response matrix as a method for classification. In particular, severe masking can occur with three or more classes. FDA uses the fits from such a regression in step (1), but then transforms them further to produce useful discriminant functions that are devoid of these pitfalls. Exercise 12.9 takes another view of this phenomenon.

## 12.6 Penalized Discriminant Analysis

Although FDA is motivated by generalizing optimal scoring, it can also be viewed directly as a form of regularized discriminant analysis. Suppose the regression procedure used in FDA amounts to a linear regression onto a basis expansion  $h(X)$ , with a quadratic penalty on the coefficients:

$$
ASR(\{\theta_{\ell}, \beta_{\ell}\}_{\ell=1}^{L}) = \frac{1}{N} \sum_{\ell=1}^{L} \left[ \sum_{i=1}^{N} (\theta_{\ell}(g_i) - h^{T}(x_i)\beta_{\ell})^2 + \lambda \beta_{\ell}^{T} \Omega \beta_{\ell} \right].
$$
 (12.57)

The choice of  $\Omega$  depends on the problem. If  $\eta_{\ell}(x) = h(x)\beta_{\ell}$  is an expansion on spline basis functions,  $\Omega$  might constrain  $\eta_{\ell}$  to be smooth over  $\mathbb{R}^{p}$ . In the case of additive splines, there are  $N$  spline basis functions for each coordinate, resulting in a total of  $Np$  basis functions in  $h(x)$ ;  $\Omega$  in this case is  $Np \times Np$  and block diagonal.

The steps in FDA can then be viewed as a generalized form of LDA, which we call *penalized discriminant analysis*, or PDA:

- Enlarge the set of predictors X via a basis expansion  $h(X)$ .
- Use (penalized) LDA in the enlarged space, where the penalized Mahalanobis distance is given by

$$
D(x, \mu) = (h(x) - h(\mu))^T (\Sigma_W + \lambda \Omega)^{-1} (h(x) - h(\mu)), \quad (12.58)
$$

where  $\Sigma_W$  is the within-class covariance matrix of the derived variables  $h(x_i)$ .

• Decompose the classification subspace using a penalized metric:

max  $u^T \Sigma_{\text{Bet}} u$  subject to  $u^T (\Sigma_W + \lambda \Omega) u = 1$ .

Loosely speaking, the penalized Mahalanobis distance tends to give less weight to "rough" coordinates, and more weight to "smooth" ones; since the penalty is not diagonal, the same applies to linear combinations that are rough or smooth.

For some classes of problems, the first step, involving the basis expansion, is not needed; we already have far too many (correlated) predictors. A leading example is when the objects to be classified are digitized analog signals:

- the log-periodogram of a fragment of spoken speech, sampled at a set of 256 frequencies; see Figure 5.5 on page 149.
- the grayscale pixel values in a digitized image of a handwritten digit.

Image /page/32/Picture/1 description: The image displays a grid of nine pairs of square images, each pair labeled with "LDA: Coefficient X" and "PDA: Coefficient X", where X ranges from 1 to 9. Each pair consists of a left image and a right image. The left images in each pair are generally noisy, with a speckled appearance of red and green pixels. The right images in each pair show more defined patterns of red and green areas, with smoother transitions between colors compared to the left images. The overall arrangement suggests a comparison between two types of coefficients (LDA and PDA) across nine different coefficients, visualized as pixelated patterns.

FIGURE 12.11. The images appear in pairs, and represent the nine discriminant coefficient functions for the digit recognition problem. The left member of each pair is the LDA coefficient, while the right member is the PDA coefficient, regularized to enforce spatial smoothness.

It is also intuitively clear in these cases why regularization is needed. Take the digitized image as an example. Neighboring pixel values will tend to be correlated, being often almost the same. This implies that the pair of corresponding LDA coefficients for these pixels can be wildly different and opposite in sign, and thus cancel when applied to similar pixel values. Positively correlated predictors lead to noisy, negatively correlated coefficient estimates, and this noise results in unwanted sampling variance. A reasonable strategy is to regularize the coefficients to be smooth over the spatial domain, as with images. This is what PDA does. The computations proceed just as for FDA, except that an appropriate penalized regression method is used. Here  $h^T(X)\beta_\ell = X\beta_\ell$ , and  $\Omega$  is chosen so that  $\beta_\ell^T\Omega\beta_\ell$ penalizes roughness in  $\beta_{\ell}$  when viewed as an image. Figure 1.2 on page 4 shows some examples of handwritten digits. Figure 12.11 shows the discriminant variates using LDA and PDA. Those produced by LDA appear as salt-and-pepper images, while those produced by PDA are smooth images. The first smooth image can be seen as the coefficients of a linear contrast functional for separating images with a dark central vertical strip (ones, possibly sevens) from images that are hollow in the middle (zeros, some fours). Figure 12.12 supports this interpretation, and with more difficulty allows an interpretation of the second coordinate. This and other

Image /page/33/Figure/1 description: A scatter plot displays data points colored and labeled with numbers from 0 to 9, representing different categories. The x-axis is labeled "PDA: Discriminant Coordinate 1" and ranges from -6 to 6. The y-axis is labeled "PDA: Discriminant Coordinate 2" and ranges from -6 to 6. Several clusters of data points are visible, with some points circled and labeled with numbers in black circles, indicating centroids or specific groups. For example, a cluster of blue '0's is on the left, a cluster of red '1's is on the right, and green '6's are in the upper left. Other numbers and colors are interspersed throughout the plot, showing varying degrees of separation and overlap between categories.

FIGURE 12.12. The first two penalized canonical variates, evaluated for the test data. The circles indicate the class centroids. The first coordinate contrasts mainly 0's and 1's, while the second contrasts 6's and 7/9's.
Remark 22.26. The equivalence between (i) and (ii) remains true when the Riemannian manifold  $M$  is replaced by a general metric space. On the other hand, the equivalence with (iii) uses at least a little bit of the Riemannian structure (say, a local Poincaré inequality, a local doubling property and a length property).

Remark 22.27. The equivalence between (i), (ii) and (iii) can be made more precise. As the proof will show, if  $\nu$  satisfies a Poincaré inequality with constant  $\lambda$ , then for any  $c < 2\sqrt{\lambda}$  there is an explicit constant  $K = K(c) > 0$  such that (22.48) holds true; and the  $K(c)$  converges to  $\lambda$  as  $c \to 0$ . Conversely, if for each  $c > 0$  we call  $K(c)$  the best constant in (22.48), then  $\nu$  satisfies a Poincaré inequality with constant  $\lambda =$  $\lim_{c\to 0} K(c)$ . Also, in (ii)  $\Rightarrow$  (iii) one can choose  $C = \max(4/K, 2/c)$ , while in (iii)  $\Rightarrow$  (i) the Poincar<PERSON> constant can be taken equal to  $C^{-1}$ .

Theorem 22.25 will be obtained by two ingredients: The first one is the Hamilton–Jacobi semigroup with a nonquadratic Lagrangian; the second one is a generalization of Theorem 22.17, stated below. The notation  $L^*$  will stand for the Legendre transform of the convex function  $L : \mathbb{R}_+ \to \mathbb{R}_+$ , and  $L''$  for the distributional derivative of  $L$ (well-defined on  $\mathbb{R}_+$  once L has been extended by 0 on  $\mathbb{R}_-$ ).

Theorem 22.28 (From generalized log Sobolev to transport to **generalized Poincaré).** Let  $M$  be a Riemannian manifold equipped with its geodesic distance d and with a reference probability measure  $\nu = e^{-V}$ vol  $\in P_2(M)$ . Let L be a strictly increasing convex function  $\mathbb{R}_+ \to \mathbb{R}_+$  such that  $L(0) = 0$  and  $L''$  is bounded above; let  $c_L(x,y) =$  $L(d(x,y))$  and let  $C<sub>L</sub>$  be the optimal transport cost associated with the cost function  $c_L$ . Further, assume that  $L(r) \leq C(1+r)^p$  for some  $p \in [1, 2]$  and some  $C > 0$ . Then:

(i) Further, assume that  $L^*(ts) \le t^2 L^*(s)$  for all  $t \in [0,1], s \ge 0$ . If there is  $\lambda \in (0,1]$  such that v satisfies the generalized logarithmic Sobolev inequality with constant  $\lambda$ :

For any  $\mu = \rho \nu \in P(M)$  such that  $\log \rho \in \text{Lip}(M)$ ,

$$
H_{\nu}(\mu) \le \frac{1}{\lambda} \int L^*(|\nabla \log \rho|) d\mu; \quad (22.50)
$$

then  $\nu$  also satisfies the following transport inequality:

$$
\forall \mu \in P_p(M), \qquad C_L(\mu, \nu) \le \frac{H_\nu(\mu)}{\lambda}.
$$
 (22.51)

(ii) If  $\nu$  satisfies (22.51), then it also satisfies the generalized Poincaré inequality with constant  $\lambda$ :

$$
\forall f \in \text{Lip}(M), \ \|f\|_{\text{Lip}} \le L'(\infty),
$$
$$
\int f \, d\nu = 0 \Longrightarrow \int f^2 \, d\nu \le \frac{2}{\lambda} \int L^*(|\nabla f|) \, d\nu.
$$

Proof of Theorem 22.28. The proof is the same as the proof of Theorem 22.17. After picking up  $g \in C_b(M)$ , one introduces the function

$$
\phi(t) = \frac{1}{\lambda t} \log \int e^{\lambda t H_t g} d\nu, \qquad H_t g(x) = \inf_{y \in M} \left[ g(y) + t L \left( \frac{d(x, y)}{t} \right) \right].
$$

The properties of  $H_t g$  are investigated in Theorem 22.46 in the Appendix.

It is not always true that  $\phi$  is continuous at  $t = 0$ , but at least the monotonicity of  $H_t g$  implies

$$
\lim_{t \to 0^+} \phi(t) \le \phi(0).
$$

Theorem 22.46 makes it possible to compute the right derivative of  $\phi$ as in the proof of Theorem  $22.17(i)$ :

$$
\frac{d^+ \phi(t)}{dt} := \lim_{s \to 0^+} \left[ \frac{\phi(t+s) - \phi(t)}{s} \right]
$$

$$
= \frac{1}{\lambda t^2 \int_M e^{\lambda t H_t g} d\nu} \left[ - \left( \int_M e^{\lambda t H_t g} d\nu \right) \log \left( \int_M e^{\lambda t H_t g} d\nu \right) + \int_M (\lambda t H_t g) e^{\lambda t H_t g} d\nu - \frac{1}{2\lambda} \int_M (\lambda^2 t^2 L^* (\nabla^{-} H_t g)) e^{\lambda t H_t g} d\nu \right]
$$

$$
\leq \frac{1}{\lambda t^2 \int_M e^{\lambda t H_t g} d\nu} \left[ - \left( \int_M e^{\lambda t H_t g} d\nu \right) \log \left( \int_M e^{\lambda t H_t g} d\nu \right) + \int_M (\lambda t H_t g) e^{\lambda t H_t g} d\nu - \frac{1}{2\lambda} \int_M L^* (\lambda t \nabla^{-} H_t g) e^{\lambda t H_t g} d\nu \right],
$$
(22.52)

where the inequality  $L^*(\lambda ts) \leq \lambda^2 t^2 L^*(s)$  was used. By assumption, the quantity inside square brackets is nonpositive, so  $\phi$  is nonincreasing on  $(0, 1]$ , and therefore on [0, 1]. The inequality  $\phi(1) \leq \phi(0)$  can be recast as

$$
\frac{1}{\lambda} \log \int_M e^{\lambda \inf_{y \in M} \left[ g(y) + L(d(x, y)) \right]} \nu(dx) \le \int_M g \, d\nu,
$$

which by Theorem 5.26 is the dual formulation of  $(22.51)$ .

As for part (ii) of the theorem, it is similar to part (ii) of Theorem 22.17.  $□$ 

Now we have enough tools at our disposal to carry on the proof of Theorem 22.25.

*Proof of Theorem 22.25.* We shall start with the proof of (i)  $\Rightarrow$  (ii). Let  $f = \log \rho - \int (\log \rho) d\nu$ ; so  $\int f d\nu = 0$  and the assumption in (ii) reads  $|\nabla f| \leq c$ . Moreover, with  $a = \int (\log \rho) d\nu$  and  $X = \int e^f d\nu$ ,

$$
I_{\nu}(\mu) = e^a \int |\nabla f|^2 e^f d\nu;
$$

$$
H_{\nu}(\mu) = \int (f+a)e^{f+a} d\nu - \left(\int e^{f+a} d\nu\right) \log \left(\int e^{f+a} d\nu\right)
$$
  
=  $e^a \left(\int f e^f d\nu - \int e^f d\nu + 1\right) - e^a (X \log X - X + 1)$   
$$
\leq e^a \left(\int f e^f d\nu - \int e^f d\nu + 1\right).
$$

So it is sufficient to prove

$$
|\nabla f| \le c \Longrightarrow \qquad \int \left( f e^f - e^f + 1 \right) d\nu \le \frac{1}{K} \int |\nabla f|^2 e^f d\nu. \tag{22.53}
$$

In the sequel, c is any constant satisfying  $0 < c < 2\sqrt{\lambda}$ . Inequality (22.53) will be proven by two auxiliary inequalities:

$$
\int f^2 \, d\nu \le e^{c\sqrt{5/\lambda}} \int f^2 e^{-|f|} \, d\nu; \tag{22.54}
$$

$$
\int f^2 e^f \, d\nu \le \frac{1}{\lambda} \left( \frac{2\sqrt{\lambda} + c}{2\sqrt{\lambda} - c} \right)^2 \int |\nabla f|^2 e^f \, d\nu. \tag{22.55}
$$

Note that the upper bound on  $|\nabla f|$  is crucial in both inequalities.

Once (22.54) and (22.55) are established, the result is immediately obtained. Indeed, the right-hand side of (22.54) is obviously bounded by the left-hand side of (22.55), so both expressions are bounded above by

a constant multiple of  $\int |\nabla f|^2 e^f d\nu$ . On the other hand, an elementary study shows that

$$
\forall f \in \mathbb{R}, \qquad f e^f - e^f + 1 \le \max\left(f^2, f^2 e^f\right),
$$

so  $(22.53)$  holds true for some explicit constant  $K(c)$ .

To obtain (22.54), we proceed as follows. The elementary inequality  $2|f|^3 \leq \delta f^2 + \delta^{-1} f^4$  ( $\delta > 0$ ) integrates up to

$$
2\int |f|^3 \, d\nu \le \delta \int f^2 \, d\nu + \delta^{-1} \int f^4 \, d\nu
$$

$$
= \delta \int f^2 \, d\nu + \delta^{-1} \left( \int f^2 \, d\nu \right)^2 + \delta^{-1} \left[ \int (f^2)^2 \, d\nu - \left( \int f^2 \, d\nu \right)^2 \right]. \tag{22.56}
$$

By the Poincaré inequality,  $\int f^2 d\nu \le (1/\lambda) \int |\nabla f|^2 d\nu \le c^2/\lambda$ , which implies  $(\int f^2 d\nu)^2 \le (c^2/\lambda) \int f^2 d\nu$ . Also by the Poincaré inequality,

$$
\int (f^2)^2 d\nu - \left(\int f^2 d\nu\right)^2 \le (1/\lambda) \int |\nabla(f^2)|^2 d\nu
$$

$$
= (4/\lambda) \int f^2 |\nabla f|^2 d\nu \le (4c^2/\lambda) \int f^2 d\nu.
$$

Plugging this information back into (22.56), we obtain

$$
2\int |f|^3\,d\nu\leq \left(\delta+\frac{5c^2}{\delta\lambda}\right)\int f^2\,d\nu.
$$

The choice  $\delta = \sqrt{5c^2/\lambda}$  yields

$$
\int |f|^3 \, d\nu \le c \sqrt{\frac{5}{\lambda}} \int f^2 \, d\nu. \tag{22.57}
$$

By Jensen's inequality, applied with the convex function  $x \to e^{-|x|}$ and the probability measure  $\sigma = f^2 \nu / (\int f^2 d\nu)$ ,

$$
\int f^2 e^{-|f|} \, d\nu = \left( \int e^{-|f|} \, d\sigma \right) \left( \int f^2 \, d\nu \right) \geq e^{-\int |f| \, d\sigma} \left( \int f^2 \, d\nu \right);
$$

in other words

$$
\int f^2 \, d\nu \le \exp\left(\frac{\int |f|^3 \, d\nu}{\int f^2 \, d\nu}\right) \int f^2 e^{-|f|} \, d\nu.
$$

Combining this inequality with (22.57) finishes the proof of (22.54).

To establish (22.55), we first use the condition  $\int f d\nu = 0$  and the Poincaré inequality to write

$$
\left(\int f e^{f/2} \, d\nu\right)^2
$$

$$
= \frac{1}{4} \left(\int [f(x) - f(y)] \left[e^{f(x)/2} - e^{f(y)/2}\right] d\nu(x) \, d\nu(y)\right)^2
$$

$$
\leq \frac{1}{4} \left(\int |f(x) - f(y)|^2 \, d\nu(x) \, d\nu(y)\right) \left(\int \left[e^{f(x)/2} - e^{f(y)/2}\right]^2 \, d\nu(x) \, d\nu(y)\right)
$$

$$
= \left(\int f^2 \, d\nu - \left(\int f \, d\nu\right)^2\right) \left(\int e^f \, d\nu - \left(\int e^{f/2} \, d\nu\right)^2\right)
$$

$$
\leq \frac{1}{\lambda^2} \left(\int |\nabla f|^2 \, d\nu\right) \left(\int |\nabla (e^{f/2})|^2 \, d\nu\right)
$$

$$
\leq \frac{c^2}{4\lambda^2} \int |\nabla f|^2 e^f \, d\nu. \tag{22.58}
$$
Next, also by the Poincaré inequality and the chain-rule

Next, also by the Poincaré inequality and the chain-rule,

$$
\int f^2 e^f d\nu - \left(\int f e^{f/2} d\nu\right)^2
$$

$$
\leq \frac{1}{\lambda} \int |\nabla (f e^{f/2})|^2 d\nu
$$

$$
= \frac{1}{\lambda} \int |\nabla f|^2 \left(1 + \frac{f}{2}\right)^2 e^f d\nu
$$

$$
= \frac{1}{\lambda} \left(\int |\nabla f|^2 e^f d\nu + \int |\nabla f|^2 f e^f d\nu + \frac{1}{4} \int |\nabla f|^2 f e^f d\nu\right)
$$

$$
\leq \frac{1}{\lambda} \left(\int |\nabla f|^2 e^f d\nu + c \sqrt{\int |\nabla f|^2 e^f d\nu} \sqrt{\int f^2 e^f d\nu} + \frac{c^2}{4} \int f^2 e^f d\nu\right).
$$
(22.59)

By adding up  $(22.58)$  and  $(22.59)$ , we obtain

$$
\int f^2 e^f d\nu \le \left(\frac{1}{\lambda} + \frac{c^2}{4\lambda^2}\right) \int |\nabla f|^2 e^f d\nu + \frac{c}{\lambda} \sqrt{\int |\nabla f|^2 e^f d\nu} \sqrt{\int f^2 e^f d\nu} + \frac{c^2}{4\lambda} \int f^2 e^f d\nu.
$$

This inequality of degree 2 involving the two quantities  $\sqrt{\int f^2 e^f d\nu}$ and  $\sqrt{\int |\nabla f|^2 e^f d\nu}$  can be transformed into (22.55). (Here the fact that  $c^2/(4\lambda) < 1$  is crucial.) This completes the proof of (i)  $\Rightarrow$  (ii).

Now we shall see that (ii)  $\Rightarrow$  (iii). Let  $\nu$  satisfy a modified logarithmic Sobolev inequality as in (22.48). Then let  $L(s) = cs^2/2$  for  $0 \leq s \leq 1$ ,  $L(s) = c(s - 1/2)$  for  $s > 1$ . The function L so defined is convex, strictly increasing and  $L'' \leq c$ . Its Legendre transform  $L^*$  is quadratic on [0, c] and identically  $+\infty$  on  $(c, +\infty)$ . So (22.48) can be rewritten

$$
H_{\nu}(\mu) \leq \frac{2c}{K} \int L^*(|\nabla \log \rho|) d\mu.
$$

Since  $L^*(tr) \leq t^2 L^*(r)$  for all  $t \in [0,1], r \geq 0$ , we can apply Theorem 22.28(i) to deduce the modified transport inequality

$$
C_L(\mu,\nu) \le \max\left(\frac{2c}{K}, 1\right) H_{\nu}(\mu),\tag{22.60}
$$

which implies (iii) since  $C_{q\ell} \leq (2/c) C_L$ .

It remains to check (iii)  $\Rightarrow$  (i). If  $\nu$  satisfies (iii), then it also satisfies  $C_L(\mu,\nu) \leq C H_{\nu}(\mu)$ , where L is as before (with  $c = 1$ ); as a consequence, it satisfies the generalized Poincaré inequality of Theorem 22.28(ii). Pick up any Lipschitz function  $f$  and apply this inequality to  $\varepsilon f$ , where  $\varepsilon$  is small enough that  $\varepsilon ||f||_{\text{Lip}} < 1$ ; the result is

$$
\int f \, d\nu = 0 \Longrightarrow \quad \varepsilon^2 \int f^2 \, d\nu \le (2C) \int L^*(\varepsilon |\nabla^- f|) \, d\nu.
$$

Since  $L^*$  is quadratic on [0, 1], factors  $\varepsilon^2$  cancel out on both sides, and we are back with the usual Poincaré inequality. □

**Exercise 22.29.** Prove directly the implication (ii)  $\Rightarrow$  (i).

Let us now see the implications of Theorem 22.25 in terms of concentration of measure.

Theorem 22.30 (Measure concentration from Poincaré inequality). Let M be a Riemannian manifold equipped with its geodesic distance, and with a reference probability measure  $\nu = e^{-V}$  vol. Assume that  $\nu$  satisfies a Poincaré inequality with constant  $\lambda$ . Then there is a constant  $C = C(\lambda) > 0$  such that for any Borel set A,

$$
\forall r \ge 0,
$$
  $\nu[A^r] \ge 1 - \frac{e^{-C \min(r, r^2)}}{\nu[A]}.$  (22.61)

Moreover, for any  $f \in \text{Lip}(M)$  (resp.  $\text{Lip}(M) \cap L^1(\nu)$ ),

$$
\nu\Big[\big\{x;\ f(x)\geq m+r\big\}\Big]\leq e^{-C\min\left(\frac{r}{\|f\|_{\mathrm{Lip}}},\frac{r^2}{\|f\|_{\mathrm{Lip}}^2}\right)},\tag{22.62}
$$

where m is a median (resp. the mean value) of f with respect to  $\nu$ .

Proof of Theorem 22.30. The proof of (22.61) is similar to the implication (i)  $\Rightarrow$  (iii) in Theorem 22.10. Define  $B = M \setminus A^r$ , and let  $\nu_A =$  $(1_A)\nu/\nu[A], \nu_B = (1_B)\nu/\nu[B].$  Obviously,  $C_{q\ell}(\nu_A, \nu_B) \ge \min(r, r^2)$ . The inequality  $\min(a + b, (a + b)^2) \le 4 \left[ \min(a, a^2) + \min(b, b^2) \right]$  makes it possible to adapt the proof of the triangle inequality for  $W_1$  (given in Chapter 6) and get  $C_{q\ell}(\nu_A, \nu_B) \leq 4[C_{q\ell}(\nu_A, \nu) + C_{q\ell}(\nu_B, \nu)]$ . Thus

$$
\min(r, r^2) \le 4[C_{q\ell}(\nu_A, \nu) + C_{q\ell}(\nu_B, \nu)].
$$

By Theorem 22.28,  $\nu$  satisfies (22.49), so there is  $C > 0$  such that

$$
\min(r, r^2) \le C\big(H_\nu(\nu_A) + H_\nu(\nu_B)\big) \n= C\Big(\log \frac{1}{\nu[A]} + \log \frac{1}{1 - \nu[A^r]}\Big),
$$

and (22.61) follows immediately. Then (22.62) is obtained by arguments similar to those used before in the proof of Theorem 22.10. □

**Example 22.31.** The exponential measure  $\nu(dx) = (1/2)e^{-|x|} dx$  does not admit Gaussian tails, so it fails to satisfy properties of Gaussian concentration expressed in Theorem 22.10. However, it does satisfy a Poincaré inequality. So  $(22.61)$ ,  $(22.62)$  hold true for this measure.

Now, consider the problem of concentration of measure in a product space, say  $(M^N, \nu^{\otimes N})$ , where  $\nu$  satisfies a Poincaré inequality. We may equip  $M^N$  with the metric

$$
d_2(x,y) = \sqrt{\sum_i d(x_i, y_i)^2};
$$

then  $\mu^{\otimes N}$  will satisfy a Poincaré inequality with the same constant as  $\nu$ , and we may apply Theorem 22.30 to study concentration in  $(M^N, d_2, \nu^{\otimes N}).$ 

However, there is a more interesting approach, due to Talagrand, in which one uses both the distance  $d_2$  and the distance

$$
d_1(x, y) = \sum_i d(x_i, y_i).
$$

Here is the procedure: Given a Borel set  $A \subset M^N$ , first enlarge it by r in distance  $d_2$  (that is, consider all points which lie at a distance less than r from A); then enlarge the result by  $r^2$  in distance  $d_1$ . This is explained in the next theorem, where  $A^{r,d}$  stands for the enlargement of A by r in distance d, and  $||f||_{\text{Lip}(\mathcal{X},d)}$  stands for the Lipschitz norm of f on  $\mathcal X$  with respect to the distance d.

Theorem 22.32 (Product measure concentration from Poincaré inequality). Let M be a Riemannian manifold equipped with its geodesic distance d and a reference probability measure  $\nu = e^{-V}$ vol. Assume that  $\nu$  satisfies a Poincaré inequality with constant  $\lambda$ . Then there is a constant  $C = C(\lambda)$  such that for any  $N \in \mathbb{N}$ , and for any Borel set  $A \subset M^N$ ,

$$
\nu^{\otimes N}[A] \ge \frac{1}{2} \implies \nu^{\otimes N}\Big[ (A^{r;d_2})^{r^2;d_1} \Big] \ge 1 - e^{-Cr^2}.
$$
 (22.63)

Moreover, for any  $f \in \text{Lip}(M^N, d_1) \cap \text{Lip}(M^N, d_2)$  (resp.  $\text{Lip}(M^N, d_1) \cap$ Lip $(M^N, d_2) \cap L^1(\nu^{\otimes N}),$ 

$$
\nu^{\otimes N} \Big[ \{ x; \ f(x) \ge m + r \} \Big] \le e^{-C \min \Big( \frac{r}{\|f\|_{\mathrm{Lip}(M^N, d_1)}} , \frac{r^2}{\|f\|_{\mathrm{Lip}(M^N, d_2)}^2} \Big)} , \tag{22.64}
$$

where  $m$  is a median of  $f$  (resp. the mean value of  $f$ ) with respect to the measure  $\nu^{\otimes N}$ .

Conversely, if  $(22.63)$  or  $(22.64)$  holds true, then  $\nu$  satisfies a Poincaré inequality.

*Proof of Theorem 22.32.* Once again, the equivalence  $(22.63) \Leftrightarrow (22.64)$ is based on arguments similar to those used in the proof of Theorem  $22.10$ ; in the sequel I shall use  $(22.63)$ .

Let us assume that  $\nu$  satisfies a Poincaré inequality, and prove (22.63). By Theorem 22.25,  $\nu$  satisfies a transport-cost inequality of the form

$$
\forall \mu \in P_1(M), \quad C_{q\ell}(\mu, \nu) \le C H_{\nu}(\mu).
$$

On  ${\cal M}^N$  define the cost

$$
c(x,y) = \sum c_{q\ell}(x_i, y_i),
$$

and let  $\overline{C}$  be the associated optimal cost functional.

By Remark 22.7,  $\nu^{\otimes N}$  satisfies an inequality of the form

$$
\forall \mu \in P_1(M^N), \quad \overline{C}(\mu, \nu^{\otimes N}) \le C H_{\nu^{\otimes N}}(\mu). \tag{22.65}
$$

Let A be a Borel set of  $M^N$  with  $\nu^{\otimes N}[A] \geq 1/2$ , and let  $r > 0$  be given. Let  $B = M^N \setminus (A^{r,d_2})^{r^2; d_1}$ . Let  $\nu_B$  be obtained by conditioning  $\nu$ on B (that is,  $\nu_B = (1_B)\nu/\nu[B]$ ). Consider the problem of transporting  $\nu_B$  to  $\nu$  optimally, with the cost c. At least a portion  $\nu^{\otimes N}[A] \geq 1/2$  of the mass has to go to from  $B$  to  $A$ , so

$$
\overline{C}(\nu_B, \nu^{\otimes N}) \ge \frac{1}{2} \inf_{x \in A, y \in B} c(x, y) =: \frac{1}{2} c(A, B).
$$

On the other hand, by (22.65),

$$
\overline{C}(\mu, \nu^{\otimes N}) \le C H_{\nu^{\otimes N}}(\mu) = C \log \frac{1}{\nu[B]}.
$$

By combining these two inequalities, we get

$$
\nu\Big[\big(A^{r;d_2}\big)^{r^2;d_1}\Big] = 1 - \nu[B] \ge 1 - e^{-1/(2C)} c(A,B).
$$

To prove (22.63), it only remains to check that

$$
c(A, B) \ge r^2.
$$

So let  $x = (x_1, \ldots, x_N) \in A$ , and let  $y \in M^N$  such that  $c(x, y) < r^2$ ; the goal is to show that  $y \in (A^{r,d_2})^{r^2; d_1}$ . For each  $i \in \{1, \ldots, N\}$ , define  $z_i = x_i$  if  $d(x_i, y_i) > 1$ ,  $z_i = y_i$  otherwise. Then

$$
d_2(x, z)^2 = \sum_{d(x_i, y_i) \le 1} d(x_i, y_i)^2 \le \sum_i c_{q\ell}(x_i, y_i) = c(x, y) < r^2;
$$

so  $z \in A^{r; d_2}$ . Similarly,

$$
d_1(z, y) = \sum_{d(x_i, y_i) > 1} d(x_i, y_i) \le \sum_i c_{q\ell}(x_i, y_i) = c(x, y) < r^2;
$$

so y lies at a distance at most  $r^2$  from z, in distance  $d_1$ . This concludes the proof.

Let us now assume that  $\nu$  satisfies (22.63), and prove the modified Talagrand inequality appearing in Theorem 22.25(iii), which will in turn imply the Poincaré inequality. The first step is to note that

$$
(A^{r;q_2})^{r^2;d_1} \subset A^{c;4r^2} := \left\{ x \in \mathcal{X}^N; \ \exists a \in A; \ c(a,x) \le 4r^2 \right\}. \tag{22.66}
$$

Indeed, separating the four cases (a)  $d(a_i, z_i) \leq 1$ ,  $d(y_i, z_i) \leq 1$ ; (b)  $d(a_i, z_i) \leq 1$ ,  $d(y_i, z_i) \geq 1$ ; (c)  $d(a_i, z_i) \geq 1$ ,  $d(a_i, y_i) \geq 1/2$ ; (d)  $d(a_i, z_i) \geq 1, d(a_i, y_i) \leq 1/2, d(y_i, z_i) \geq d(a_i, z_i)/2$ , one obtains the elementary inequality

$$
c_{q\ell}(a_i, z_i) \le 2 [d(a_i, y_i)^2 + d(y_i, z_i)],
$$

and  $(22.66)$  follows from the definitions and summation over i.

As in the proof of Theorem 22.22, let  $\hat{\mu}_x^N = (1/N) \sum_i \delta_{x_i}$ , let  $f_N(x) = C_{q\ell}(\widehat{\mu}_x^N, \nu)$  let  $m_N$  be a median of  $f_N$  (with respect to the probability measure  $\nu^{\otimes N}$ ) and let  $A = \{x; f_N(x) \leq m_N\}$ , so that A has probability at least  $1/2$ . If  $x \in (A^{r,d_2})^{r^2,d_1}$  then by  $(22.66)$  there is  $a \in A$  such that  $c(a, x) \le 4r^2$ , and by Theorem 4.8,

$$
C_{q\ell}(\widehat{\mu}_{x}^N, \widehat{\mu}_{a}^N) \le \frac{4r^2}{N}.
$$

On the other hand, as in the proof of Theorem 22.30, one has

$$
C_{q\ell}(\widehat{\mu}_{x}^{N},\nu) \leq 4 \left[ C_{q\ell}(\widehat{\mu}_{x}^{N},\widehat{\mu}_{a}^{N}) + C_{q\ell}(\widehat{\mu}_{a}^{N},\nu) \right].
$$

It follows by means of Property (22.63) that

$$
\nu^{\otimes N} \Big[ C_{q\ell}(\widehat{\mu}_x^N, \nu) > 4 \, m_N + 16 \, \frac{r^2}{N} \Big] \leq \nu^{\otimes N} \big[ \mathcal{X}^N \setminus A^{c; 4r^2} \big] \leq \nu^{\otimes N} \big[ \mathcal{X}^N \setminus (A^{r; d_2})^{r^2; d_1} \big] \leq e^{-Cr^2}.
$$

The rest of the argument is as in Theorem 22.22. □

**Example 22.33.** Let  $\nu(dx)$  be the exponential measure  $e^{-|x|}dx/2$  on R, then  $\nu^{\otimes N}(dx) = (1/2^N)e^{-\sum |x_i|} \prod_{i=1}^n dx_i$  on  $\mathbb{R}^N$ . Theorem 22.32 shows that for every Borel set  $A \subset \mathbb{R}^N$  with  $\nu^{\otimes N}[A] \ge 1/2$  and any  $\delta > 0$ ,

$$
\nu^{\otimes N} \left[ A + B_r^{d_2} + B_{r^2}^{d_1} \right] \ge 1 - e^{-cr^2} \tag{22.67}
$$

where  $B_r^d$  stands for the ball of center 0 and radius r in  $\mathbb{R}^N$  for the distance d.

Remark 22.34. Strange as this may seem, inequality (22.67) contains (up to numerical constants) the Gaussian concentration of the Gaussian measure! Indeed, let  $T : \mathbb{R} \to \mathbb{R}$  be the increasing rearrangement of the exponential measure  $\nu$  onto the one-dimensional Gaussian measure  $\gamma$ (so  $T_{\#}\nu = \gamma$ ,  $(T^{-1})_{\#}\gamma = \nu$ ). An explicit computation shows that

$$
|T(x) - T(y)| \le C \min(|x - y|, \sqrt{|x - y|})
$$
 (22.68)

for some numeric constant C. Let  $T_N(x_1,\ldots,x_N) = (T(x_1),\ldots,T(x_N));$ obviously  $(T_N)_\#(\nu^{\otimes N}) = \gamma^{\otimes N}, (T_N)_\#^{-1}(\gamma^{\otimes N}) = \nu^{\otimes N}$ . Further, let A be any Borel set in  $\mathbb{R}^N$ , and let  $y \in T_N^{-1}(A) + B_r^{d_2} + B_{r^2}^{d_1}$  $r_1^{a_1}$ . This means that there are w and x such that  $T_N(w) \in A$ ,  $|x - w|_2 \le r$ ,  $|y - x|_1 \le r^2$ . Then by (22.68),

$$
|T_N(w) - T_N(y)|_2^2 = \sum |\nT(w_i) - T(y_i)|^2
$$
  
\n
$$
\leq C^2 \sum_i \min(|w_i - y_i|, |w_i - y_i|^2)
$$
  
\n
$$
\leq C^2 \Big( \sum_{|w_i - x_i| \geq |x_i - y_i|} 2|w_i - x_i| + \sum_{|w_i - x_i| < |x_i - y_i|} 4|x_i - y_i|^2 \Big)
$$
  
\n
$$
\leq 4C^2 \Big( \sum |x_i - w_i| + \sum |x_i - y_i|^2 \Big)
$$
  
\n
$$
\leq 8C^2 r^2;
$$

so  $T_N(y) \in A + B_{\sqrt{8}Cr}^{d_2}$ . In summary, if  $C' = \sqrt{8}C$ , then

$$
T_N\big(T_N^{-1}(A) + B_r^{d_2} + B_{r^2}^{d_1}\big) \subset A + B_{C'r}^{d_2}.
$$

As a consequence, if  $A \subset \mathbb{R}^N$  is any Borel set satisfying  $\gamma^{\otimes N}[A] \ge 1/2$ , then  $\nu^{\otimes N}[T_N^{-1}(A)] = \gamma^{\otimes N}[A] \ge 1/2$ , and

$$
\gamma^{\otimes N}[A^{C'r}] \ge \gamma^{\otimes N} \Big[ T_N \big( T_N^{-1}(A) + B_r^{d_2} + B_{r^2}^{d_1} \big) \Big]
$$
  
=  $\nu^{\otimes N} \Big[ T_N^{-1}(A) + B_r^{d_2} + B_{r^2}^{d_1} \Big]$   
$$
\ge 1 - e^{-cr^2}
$$

for some numeric constant  $c > 0$ . This is precisely the Gaussian concentration property as it appears in Theorem  $22.10(iii)$  — in a dimensionfree form.

Remark 22.35. In certain situations, (22.67) provides sharper concentration properties for the Gaussian measure, than the usual Gaussian concentration bounds. This might look paradoxical, but can be explained by the fact that Gaussian concentration considers arbitrary sets A, while in many problems one is led to study the concentration of measure around certain very particular sets, for instance with a "cubic" structure; then inequality  $(22.67)$  might be very efficient.

**Example 22.36.** Let  $A = \{x \in \mathbb{R}^N; \max |x_i| \leq m\}$  be the centered cube of side 2m, where  $m = m(N) \rightarrow \infty$  is chosen in such a way that  $\gamma^{\otimes N}[A] \geq 1/2$ . (It is a classical fact that  $m = O(\sqrt{\log N})$  will do, but we don't need that information.) If  $r \geq 1$  is small with respect to m, then the enlargement of the cube is dominated by the behavior of T close to  $T^{-1}(m)$ . Since  $T(x)$  behaves approximately like  $\sqrt{x}$  for large values of x,  $T^{-1}(m)$  is of the order  $m^2$ ; and close to  $m^2$  the Lipschitz norm of T is  $O(1/m)$ . Then the computation before can be sharpened into

$$
T_N\left(T_N^{-1}(A) + B_r^{d_2} + B_{r^2}^{d_1}\right) \subset A + B_{C'r^2/m}^{d_2};
$$

so the concentration of measure can be felt with enlargements by a distance of the order of  $r^2/m \ll r$ .

## Dimension-dependent inequalities

There is no well-identified analog of Talagrand inequalities that would take advantage of the finiteness of the dimension to provide sharper concentration inequalities. In this section I shall suggest some natural possibilities, focusing on positive curvature for simplicity; so M will be compact. This compactness assumption is not a serious restriction: If  $(M, e^{-V}$  vol) is any Riemannian manifold satisfying a CD $(K, N)$  inequality for some  $K \in \mathbb{R}$ ,  $N < \infty$ , and  $e^{-V}$  vol satisfies a Talagrand inequality, then it can be shown that M is compact.

Theorem 22.37 (Finite-dimensional transport-energy inequalities). Let M be a Riemannian manifold equipped with a probability measure  $\nu = e^{-V}$ vol,  $V \in C^2(M)$ , satisfying a curvaturedimension bound  $CD(K, N)$  for some  $K > 0$ ,  $N \in (1, \infty)$ . Then, for any  $\mu = \rho \nu \in P_2(M)$ ,

$$
\int_{M} \left[ N \left( \frac{\alpha}{\sin \alpha} \right)^{1 - \frac{1}{N}} \rho(x_0)^{-\frac{1}{N}} - (N - 1) \frac{\alpha}{\tan \alpha} \right] \pi(dx_0 dx_1) \le 1,
$$
\n(22.69)

where  $\alpha(x_0, x_1) = \sqrt{K/(N-1)} d(x_0, x_1)$ , and  $\pi$  is the unique optimal coupling between  $\mu$  and  $\nu$ . Equivalently,

$$
\int_{M} \left[ N \left( \frac{\alpha}{\sin \alpha} \right)^{1 - \frac{1}{N}} - (N - 1) \frac{\alpha}{\tan \alpha} - 1 \right] \pi(dx_0 dx_1)
$$

$$
\leq \int \left( \frac{\alpha}{\sin \alpha} \right)^{1 - \frac{1}{N}} \left[ (N - 1)\rho - N\rho^{1 - \frac{1}{N}} + 1 \right] d\nu. \quad (22.70)
$$

**Remark 22.38.** The function  $(N-1)r - Nr^{1-\frac{1}{N}} + 1$  is nonnegative, and so is the integrand in the right-hand side of (22.70). If the coefficient  $\alpha$ / sin  $\alpha$  above would be replaced by 1, then the right-hand side of (22.70) would be just  $\int [(N - 1)\rho - N\rho^{1 - \frac{1}{N}} + 1] d\nu = H_{N,\nu}(\rho)$ .

Corollary 22.39 (Further finite-dimensional transport-energy inequalities). With the same assumptions and notation as in Theorem 22.37, the following inequalities hold true:

$$
\forall p \in (1, \infty) \qquad H_{Np,\nu}(\mu) \ge \int \left[ (Np - 1) - (N - 1) \frac{\alpha}{\tan \alpha} - N(p - 1) \left( \frac{\sin \alpha}{\alpha} \right)^{\frac{1}{p - 1} \left( 1 - \frac{1}{N} \right)} \right] d\pi;
$$

$$
(22.71)
$$

$$
2H_{N,\nu}(\mu) - \int \rho^{1-\frac{1}{N}} \log \rho \, d\nu \ge \int \left[ (2N-1) - (N-1) \frac{\alpha}{\tan \alpha} - N \exp\left( 1 - \left( \frac{\alpha}{\sin \alpha} \right)^{1-\frac{1}{N}} \right) \right] d\pi; \tag{22.72}
$$

$$
H_{\infty,\nu}(\mu) \ge (N-1) \int \left(1 - \frac{\alpha}{\tan \alpha} + \log \frac{\alpha}{\sin \alpha}\right) d\pi.
$$
 (22.73)

Proof of Theorem 22.37. Apply Theorem 20.10 with  $U(r) = -N r^{1-1/N}$ ,  $\rho_0 = 1, \rho_1 = \rho$ : This yields the inequality

$$
-N \leq -\int N \rho^{1-\frac{1}{N}} \left(\frac{\alpha}{\sin \alpha}\right)^{1-\frac{1}{N}} d\pi(\cdot|\cdot) d\nu - \int (N-1) \left(1-\frac{\alpha}{\tan \alpha}\right) d\pi(\cdot|\cdot) d\nu.
$$

Since  $\pi$  has marginals  $\rho \nu$  and  $\nu$ , this is the same as (22.69).

To derive (22.70) from (22.69), it is sufficient to check that

$$
\int NQ d\pi = \int Q[(N-1)\rho + 1] d\nu,
$$

where  $Q = (\alpha / \sin \alpha)^{1-\frac{1}{N}}$ . But this is immediate because Q is a symmetric function of  $x_0$  and  $x_1$ , and  $\pi$  has marginals  $\mu = \rho \nu$  and  $\nu$ , so

$$
\int Q(x_0, x_1) d\nu(x_0) = \int Q(x_0, x_1) d\nu(x_1) = \int Q(x_0, x_1) d\pi(x_0, x_1)
$$

$$
= \int Q(x_0, x_1) \rho(x_0) d\nu(x_0).
$$

*Proof of Corollary 22.39.* Write again  $Q = (\alpha / \sin \alpha)^{1-\frac{1}{N}}$ . The classical Young inequality can be written  $ab \le a^p/p + b^{p'}/p'$ , where  $p' = p/(p-1)$ is the conjugate exponent to  $p$ ; so

$$
N p \rho^{1-\frac{1}{Np}} = (N p \rho) \left[ \rho^{-\frac{1}{N}} Q \right]^{\frac{1}{p}} Q^{-\frac{1}{p}} \le (N p \rho) \left[ \frac{\rho^{-\frac{1}{N}} Q}{p} + \frac{Q^{-\frac{p'}{p'}}}{p'} \right].
$$

By integration of this inequality and (22.69),

$$
H_{N,\nu}(\mu) + Np = -Np \int \rho^{1-\frac{1}{Np}} \n\ge - \int N \rho^{-\frac{1}{N}} Q d\pi - N(p-1) \int Q^{-\frac{1}{p-1}} d\pi \n\ge -1 - \int (N-1) \frac{\alpha}{\tan \alpha} d\pi - N(p-1) \int Q^{-\frac{1}{p-1}} d\pi,
$$

which is the same as  $(22.71)$ .

Then (22.72) and (22.73) are obtained by taking the limits  $p \to 1$ and  $p \to \infty$ , respectively. Equivalently, one can apply the inequalities  $ab \le a \log a - 2a + e^{b+1}$  and  $ab \le a \log a - a + e^b$  instead of Young's inequality; more precisely, to get (22.72) from (22.69), one can write

$$
N\rho^{1-\frac{1}{N}}\log\rho^{\frac{1}{N}} = (N\rho^{1-\frac{1}{N}}e^{-Q})(e^{Q}\log\rho^{\frac{1}{N}})
$$
  
 
$$
\leq (N\rho^{1-\frac{1}{N}}e^{-Q})(e^{Q}Q - 2e^{Q} + e\rho^{\frac{1}{N}});
$$

and to get (22.73) from (22.69),

$$
N\rho \log Q = (N\rho^{1-\frac{1}{N}})\rho^{\frac{1}{N}} \log Q \le (N\rho^{1-\frac{1}{N}}) \left(\rho^{\frac{1}{N}} \log \rho^{\frac{1}{N}} - \rho^{\frac{1}{N}} + Q\right).
$$

All the inequalities appearing in Corollary 22.39 can be seen as refinements of the Talagrand inequality appearing in Theorem 22.14; concentration inequalities derived from them take into account, for instance, the fact that the distance between any two points can never exceed  $\pi \sqrt{(N-1)/K}$ .

Exercise 22.40. Give a more direct derivation of inequality (22.73), based on the fact that  $U(r) = r \log r$  lies in  $\mathcal{D}C_N$ .

Exercise 22.41. Use the inequalities proven in this section, and the result of Exercise 22.20, to recover, at least formally, the inequality

$$
\left[ \int h \, d\nu = 0 \right] \Longrightarrow \qquad \|h\|_{H^{-1}(\nu)}^2 \le \frac{KN}{N-1} \, \|h\|_{L^2(\nu)}^2
$$

under an assumption of curvature-dimension bound  $CD(K, N)$ . Now turn this into a rigorous proof, assuming as much smoothness on  $h$ and on the density of  $\nu$  as you wish. (*Hint:* When  $\varepsilon \to 0$ , the optimal transport between  $(1+\varepsilon h)\nu$  and  $\nu$  converges in measure to the identity map; this enables one to pass to the limit in the distortion coefficients.)

Remark 22.42. If one applies the same procedure to (22.71), one recovers a constant  $K(Np)/(Np-1)$ , which reduces to the correct constant only in the limit  $p \to 1$ . As for inequality (22.73), it leads to just K (which would be the limit  $p \to \infty$ ).

Remark 22.43. Since the Talagrand inequality implies a Poincaré inequality without any loss in the constants, and the optimal constant in the Poincaré inequality is  $KN/(N-1)$ , it is natural to ask whether this is also the optimal constant in the Talagrand inequality. The answer is affirmative, in view of Theorem 22.17, since the logarithmic Sobolev inequality also holds true with the same constant. But I don't know of any transport proof of this!

Open Problem 22.44. Find a direct transport argument to prove that the curvature-dimension  $CD(K, N)$  with  $K > 0$  and  $N < \infty$  implies  $T_2(K)$  with  $K = KN/(N-1)$ , rather than just  $T_2(K)$ .

Note that inequality (22.73) does not solve this problem, since by Remark 22.42 it only implies the Poincaré inequality with constant  $K$ .

I shall conclude with a very loosely formulated open problem, which might be nonsense:

Open Problem 22.45. In the Euclidean case, is there a particular variant of the Talagrand inequality which takes advantage of the homogeneity under dilations, just as the usual Sobolev inequality in  $\mathbb{R}^n$ ? Is it useful?

## Recap

In the end, the main results of this chapter can be summarized by just a few diagrams:

• Relations between functional inequalities: By combining Theorems 21.2, 22.17, 22.10 and elementary inequalities, one has

$$
CD(K, \infty) \implies (LS) \implies (T_2) \implies (P) \implies (\exp_1)
$$
  
$$
\downarrow \qquad (T_1) \iff (\exp_2) \implies (\exp_1)
$$

All these symbols designate properties of the reference measure  $\nu$ : (LS) stands for logarithmic Sobolev inequality,  $(P)$  for Poincaré inequality,  $\exp_2$  means that  $\nu$  has a finite square-exponential moment, and  $\exp_1$ that it has a finite exponential moment.

• Reformulations of Poincaré inequality: Theorem 22.25 can be visualized as

$$
(P) \iff (LSLL) \iff (T_{q\ell})
$$

where (LSLL) means logarithmic Sobolev for log-Lipschitz functions, and  $(T_{q\ell})$  designates the transportation-cost inequality involving the quadratic-linear cost.

• Concentration properties via functional inequalities: The three main such results proven in this chapter are

 $(T_1) \iff Gaussian\ concentration\ (Theorem 22.10)$ 

 $(T_2) \iff dimension \text{ free Gaussian concentration (Theorem 22.22)}$ 

 $(P) \iff$  dimension free *exponential* concentration (Theorem 22.32)

### Appendix: Properties of the Hamilton –Jacobi semigroup

This Appendix is devoted to the proof of Theorem 22.46 below, which was used in the proof of Theorem 22.28 (and also in the proof of Theorem 22.17 via Proposition 22.16). It says that if a nice convex Lagrangian  $L(|v|)$  is given on a Riemannian manifold, then the solution  $f(t, x)$  of the associated Hamilton–Jacobi semigroup satisfies (a) certain regularity properties which go beyond differentiability; (b) the *point*wise differential equation

$$
\frac{\partial f}{\partial t} + L^* \big( |\nabla^- f(x)| \big) = 0,
$$

where  $|\nabla^- f(x)|$  is defined by (20.2).

Recall the notion of semiconcavity from Definition 10.10; throughout this Appendix I shall say "semiconcave" for "semiconcave with a quadratic modulus". To say that  $L$  is locally semiconcave is equivalent to saying that the (distributional) second derivative  $L''$  is locally bounded above on  $\mathbb{R}$ , once L has been extended by 0 on  $\mathbb{R}_-$ .

Theorem 22.46 (Some properties of the Hamilton–Jacobi semigroup on a manifold). Let  $L : \mathbb{R}_+ \to \mathbb{R}_+$  be a strictly increasing, locally semiconcave, convex continuous function with  $L(0) = 0$ . Let M be a Riemannian manifold equipped with its geodesic distance d. For any  $f \in C_b(M)$ , define the evolution  $(H_t f)_{t>0}$  by

$$
\begin{cases} H_0 f = f \\ (H_t f)(x) = \inf_{y \in M} \left[ f(y) + t L\left(\frac{d(x, y)}{t}\right) \right] \qquad (t > 0, \ x \in M). \end{cases}
$$
 $(22.74)$ 

Then:

(i) For any  $s,t \geq 0$ ,  $H_s H_t f = H_{t+s} f$ .

(ii) For any  $x \in M$ , inf  $f \leq (H_t f)(x) \leq f(x)$ ; moreover, for any  $t > 0$  the infimum over M in (22.74) can be restricted to the closed ball  $B[x,R(f,t)],$  where

$$
R(f,t) = t L^{-1} \left( \frac{\sup f - \inf f}{t} \right).
$$

(iii) For any  $t > 0$ ,  $H_t f$  is Lipschitz and locally semiconcave on M; moreover  $||H_t f||_{\text{Lip}} \leq L'(\infty)$ .

(iv) For any  $t > 0$ ,  $H_{t+s}f$  is nonincreasing in s, and converges monotonically and locally uniformly to  $H_t f$  as  $s \to 0$ ; this conclusion extends to  $t = 0$  if  $||f||_{\text{Lip}} \leq L'(\infty)$ .

(v) For any  $t \geq 0$ ,  $s > 0$ ,  $x \in M$ ,

$$
\frac{|H_{t+s}f(x)-H_tf(x)|}{s}\leq L^*\Big(\|H_tf\|_{\mathrm{Lip}(B[x,R(f,s)])}\Big).
$$

(vi) For any  $x \in M$  and  $t > 0$ ,

$$
\liminf_{s \downarrow 0} \frac{(H_{t+s}f)(x) - (H_tf)(x)}{s} \ge -L^*(|\nabla^- H_t f(x)|);
$$

this conclusion extends to  $t = 0$  if  $||f||_{\text{Lip}} \leq L'(\infty)$ .

(vii) For any  $x \in M$  and  $t > 0$ ,

$$
\lim_{s \downarrow 0} \frac{(H_{t+s}f)(x) - (H_t f)(x)}{s} = -L^*(|\nabla^{\dagger} H_t f|);
$$

this conclusion extends to  $t = 0$  if  $||f||_{\text{Lip}} \le L'(\infty)$  and f is locally semiconcave.

**Remark 22.47.** If  $L'(\infty) < +\infty$  then in general  $H_t f$  is not continuous as a function of t at  $t = 0$ . This can be seen by the fact that  $||H_t f||_{\text{Lin}} \le$  $L'(\infty)$  for all  $t > 0$ .

Remark 22.48. There is no measure theory in Theorem 22.46, and conclusions hold for all (not just almost all)  $x \in M$ .

*Proof of Theorem 22.46.* First, note that the inverse  $L^{-1}$  of L is welldefined  $\mathbb{R}_+ \to \mathbb{R}_+$  since L is strictly increasing and goes to  $+\infty$  at infinity. Also  $L'(\infty) = \lim_{r \to \infty} (L(r)/r)$  is well-defined in  $(0, +\infty]$ . Further, note that

$$
L^*(p) = \sup_{r \ge 0} [pr - L(r)]
$$

is a convex nondecreasing function of p, satisfying  $L^*(0) = 0$ .

Let  $x, y, z \in M$  and  $t, s > 0$ . Since L is increasing and convex,

$$
L\left(\frac{d(x,y)}{t+s}\right) \le L\left(\frac{d(x,z)+d(z,y)}{t+s}\right)
$$
  
$$
\le \frac{t}{t+s}L\left(\frac{d(x,z)}{t}\right) + \frac{s}{t+s}L\left(\frac{d(z,y)}{s}\right),
$$

with equality if  $d(x, z)/t = d(z, y)/s$ , i.e. if z is an  $s/(t + s)$ -barycenter of  $x$  and  $y$ . (There always exists such a  $z$ .) So

$$
(t+s) L\left(\frac{d(x+y)}{t+s}\right) = \inf_{z \in M} \left[ t L\left(\frac{d(x,z)}{t}\right) + s L\left(\frac{d(x,y)}{s}\right) \right].
$$

This implies (i).

The lower bound in (ii) is obvious since  $L \geq 0$ , and the upper bound follows from the choice  $y = x$  in (22.74). Moreover, if  $d(x, y) > R(f, t)$ , then

$$
f(x) + t L\left(\frac{d(x, y)}{t}\right) > (\inf f) + t L\left(\frac{R(f, t)}{t}\right)
$$
  
$$
= (\inf f) + (\sup f - \inf f) = \sup f;
$$

so the infimum in (22.74) may be restricted to those  $y \in M$  such that  $d(x,y) \leq R(f,t)$ . Note that  $R(f,t)$  is finite for all  $t > 0$ .

When y varies in  $B[x, R(f,t)]$ , the function  $t L(d(x,y)/t)$  remains *C*-Lipschitz, where  $C = L'(R(f,t)/t) < +\infty$ . So  $H_t f$  is an infimum of uniformly Lipschitz functions, and is therefore Lipschitz. It is obvious that  $C \leq L'(\infty)$ .

To prove (iii) it remains to show that  $H_t f$  is locally semiconcave for  $t > 0$ . Let  $(\gamma_t)_{0 \leq t \leq 1}$  be a minimizing geodesic in M, then for  $\lambda \in [0, 1]$ ,

$$
H_t f(\gamma_\lambda) - (1 - \lambda) H_t f(\gamma_0) - \lambda H_t f(\gamma_1) = \inf_{z_\lambda} \inf_{z_0} \sup_{z_1} \left\{ f(z_\lambda) - (1 - \lambda) f(z_0) - \lambda f(z_1) + t \left[ L \left( \frac{d(z_\lambda, \gamma_\lambda)}{t} \right) - (1 - \lambda) L \left( \frac{d(z_0, \gamma_0)}{t} \right) - \lambda L \left( \frac{d(z_1, \gamma_1)}{t} \right) \right] \right\}
$$

$$
\geq t \inf_{z} \left[ L \left( \frac{d(z, \gamma_\lambda)}{t} \right) - (1 - \lambda) L \left( \frac{d(z, \gamma_0)}{t} \right) - \lambda L \left( \frac{d(z, \gamma_1)}{t} \right) \right],
$$
where the latter inequality has been obtained by choosing z = z<sub>0</sub> =

where the latter inequality has been obtained by choosing  $z = z_0 =$  $z_1 = z_\lambda$ . The infimum may be restricted to a large ball containing the balls of radius  $R(f, t)$  centered at  $\gamma_0$ ,  $\gamma_\lambda$  and  $\gamma_1$ . When the image of  $\gamma$ is contained in a compact set  $K$  we may therefore find a large ball  $B$ (depending on  $K$ ) such that

$$
H_t f(\gamma_\lambda) - (1 - \lambda) H_t f(\gamma_0) - \lambda H_t f(\gamma_1)
$$
  
\n
$$
\geq t \inf_{z \in B} \left[ L \left( \frac{d(z, \gamma_\lambda)}{t} \right) - (1 - \lambda) L \left( \frac{d(z, \gamma_0)}{t} \right) - \lambda L \left( \frac{d(z, \gamma_1)}{t} \right) \right].
$$
\n(22.75)

When z varies in B, the distance function  $d(z, \cdot)$  is uniformly semiconcave (with a quadratic modulus) on the compact set  $K$ ; recall indeed the computations in the Third Appendix of Chapter 10. Let  $F = L(\cdot / t)$ , restricted to a large interval where  $d(z, \cdot)$  takes values; and let  $\varphi = d(z, \cdot)$ , restricted to K. Since F is semiconcave increasing Lipschitz and  $\varphi$  is semiconcave Lipschitz, their composition  $F \circ \varphi$ is semiconcave, and the modulus of semiconcavity is uniform in z. So there is  $C = C(K)$  such that

$$
\inf_{z \in B} \left[ L\left(\frac{d(z, \gamma_{\lambda})}{t}\right) - (1 - \lambda) L\left(\frac{d(z, \gamma_{0})}{t}\right) - \lambda L\left(\frac{d(z, \gamma_{1})}{t}\right) \right] \geq -C\lambda(1 - \lambda) d(\gamma_{0}, \gamma_{1})^{2}.
$$

This and  $(22.75)$  show that  $H_t f$  is locally semiconcave and conclude the proof of (iii).

To prove (iv), let  $g = H_t f$ . It is clear that  $H_s g$  is a nonincreasing function of s since  $s L(d(x,y)/s)$  is itself a nonincreasing function of s. I shall now distinguish two cases.

**Case 1:**  $L'(\infty) = +\infty$ ; then  $\lim_{s\to 0} R(g, s) = (\sup g - \inf g)/L'(\infty) = 0$ . For any  $x \in M$ ,

$$
g(x) \ge H_s g(x) = \inf_{d(x,y)\le R(g,s)} \left[ g(y) + s L\left(\frac{d(x,y)}{s}\right) \right]
$$

$$
\ge \inf_{d(x,y)\le R(g,s)} g(y),
$$

and this converges to  $g(x)$  as  $s \to 0$ , locally uniformly in x.

**Case 2:**  $L'(\infty) < +\infty$ ; then  $\lim_{s\to 0} R(g, s) > 0$  (except if g is constant, a case which I omit since it is trivial). Since  $||g||_{\text{Lip}} \leq L'(\infty)$ ,

$$
g(y) \ge g(x) - L'(\infty) d(x, y),
$$

so

$$
g(x) \ge H_s g(x) \ge g(x) + \inf_{d(x,y) \le R(g,s)} \left[ s L\left(\frac{d(x,y)}{s}\right) - L'(\infty) d(x,y) \right]
$$
  
 
$$
\ge g(x) + \left[ s L\left(\frac{R(g,s)}{s}\right) - L'(\infty) R(g,s) \right], \qquad (22.76)
$$

where I used the fact that  $s L(d/s) - L'(\infty) d$  is a nonincreasing function of d (to see this, note that  $L'(d/s) - L'(\infty) \leq 0$ , where  $L'(r)$  is the right-derivative of L at r). By definition,  $s L(R(g, s)/s) = \sup g - \inf g$ , so (22.76) becomes

$$
H_s g(x) \ge g(x) + \left[ (\sup g - \inf g) - L'(\infty) R(g, s) \right].
$$

As  $s \to 0$ , the expression inside square brackets goes to 0, and  $H_s g(x)$ converges uniformly to  $q(x)$ . So (iv) is established.

To prove (v), again let  $g = H_t f$ , then

$$
0 \le g(x) - H_s g(x)
$$
  
= 
$$
\sup_{d(x,y)\le R(g,s)} \left[ g(x) - g(y) - s L\left(\frac{d(x,y)}{s}\right) \right]
$$
  
$$
\le s \left\{ \sup_{d(x,y)\le R(g,s)} \left( \frac{[g(y) - g(x)]_ -}{d(x,y)} \right) \frac{d(x,y)}{s} - L\left(\frac{d(x,y)}{s}\right) \right\}
$$
  
$$
\le s L^* \left( \sup_{d(x,y)\le R(g,s)} \frac{[g(y) - g(x)]_ -}{d(x,y)} \right), \qquad (22.77)
$$

where I have used the inequality  $p r \leq L(r) + L^{*}(p)$ . Statement (v) follows at once from (22.77). Moreover, if  $L'(\infty) = +\infty$ , then  $L^*$  is continuous on  $\mathbb{R}_+$ , so by the definition of  $|\nabla^-g|$  and the fact that  $R(g,s) \to 0$ ,

$$
\limsup_{s \downarrow 0} \frac{g(x) - H_s g(x)}{s} \le L^* \left( \lim_{s \downarrow 0} \sup_{d(x,y) \le R(g,s)} \frac{[g(y) - g(x)]-}{d(x,y)} \right)
$$
$$
= L^* \left( |\nabla^{\{-1\}} g(x)| \right),
$$

which proves (vi) in the case  $L'(\infty) = +\infty$ .

If  $L'(\infty) < +\infty$ , things are a bit more intricate. If  $||g||_{\text{Lip}} \leq L'(\infty)$ , then of course  $|\nabla^- g(x)| \leq L'(\infty)$ . I shall distinguish two situations:

• If  $|\nabla^- g(x)| = L'(\infty)$ , the same argument as before shows

$$
\frac{g(x) - H_s g(x)}{s} \le L^*(||g||_{\text{Lip}}) \le L^*(L'(\infty)) = L^*(|\nabla^- g(x)|).
$$

• If  $|\nabla^- g(x)| < L'(\infty)$ , I claim that there is a function  $\alpha = \alpha(s)$ , depending on x, such that  $\alpha(s) \longrightarrow 0$  as  $s \longrightarrow 0$ , and

$$
H_s g(x) = \inf_{d(x,y)\le\alpha(s)} \left[ g(y) + s L\left(\frac{d(x,y)}{s}\right) \right].
$$
 (22.78)

If this is true then the same argument as in the case  $L'(\infty) = +\infty$ will work.

So let us prove (22.78). By Lemma 22.49 below, there exists  $\delta > 0$ such that for all  $y \in B[x, R(g, s)],$ 

$$
g(x) - g(y) \le (L'(\infty) - \delta) d(x, y).
$$
 (22.79)

For any  $\alpha_0 > 0$  we can find  $s_0$  such that

$$
\alpha \ge \alpha_0, \ s \le s_0 \Longrightarrow \frac{s}{\alpha} L\left(\frac{\alpha}{s}\right) \ge L'(\infty) - \frac{\delta}{2}.
$$

So we may define a function  $\alpha(s) \to 0$  such that

$$
\alpha \ge \alpha(s) \Longrightarrow \qquad s \, L\left(\frac{\alpha}{s}\right) \ge \left(L'(\infty) - \frac{\delta}{2}\right) \, \alpha. \tag{22.80}
$$

If  $d(x,y) \ge \alpha(s)$ , (22.79) and (22.80) imply

$$
g(y) + s L\left(\frac{d(x,y)}{s}\right) \ge g(x) + s L\left(\frac{d(x,y)}{s}\right) - \left(L'(\infty) - \delta\right) d(x,y)
$$
$$
\ge g(x) + \frac{\delta}{2} d(x,y) > g(x).
$$

So the infimum of  $[g(y) + s L(d(x, y)/s)]$  may be restricted to those  $y \in M$  such that  $d(x,y) \leq \alpha(s)$ , and  $(22.78)$  is true. Thus (vi) holds true in all cases.

It only remains to prove (vii). Let  $g = H_t f$ ; as we already know,  $||g||_{\text{Lip}} \leq L'(\infty)$  and g is locally semiconcave. The problem is to show

$$
\liminf_{s \downarrow 0} \left[ \frac{g(x) - H_s g(x)}{s} \right] \ge L^*(|\nabla^- g(x)|).
$$

This is obvious if  $|\nabla^- g(x)| = 0$ , so let us assume  $|\nabla^- g(x)| > 0$ . (Note that  $|\nabla^- g(x)| < +\infty$  since g is Lipschitz.)

By the same computation as before,

$$
\frac{g(x) - H_s g(x)}{s} = \frac{1}{s} \sup_{d(x,y) \le R(g,s)} \left[ g(x) - g(y) - s L\left(\frac{d(x,y)}{s}\right) \right].
$$

First, assume  $L'(\infty) = +\infty$ , so  $L^*$  is defined on the whole of  $\mathbb{R}_+$ . Let  $q \in \partial L(|\nabla^- g(x)|)$ . As  $s \to 0$ ,

$$
\frac{R(g,s)}{s} \longrightarrow L^{-1}(\infty) = +\infty.
$$

So for s small enough,  $R(g, s) > s q$ . This implies

$$
\frac{g(x) - H_s g(x)}{s} \ge \frac{1}{s} \sup_{d(x,y)=s} \left[ g(x) - g(y) - s L\left(\frac{d(x,y)}{s}\right) \right]
$$
$$
= \sup_{d(x,y)=s q} \left[ \left(\frac{g(x) - g(y)}{d(x,y)}\right) q - L(q) \right]. \tag{22.81}
$$

Let

$$
\psi(r) = \sup_{d(x,y)=r} \left[ \frac{g(x) - g(y)}{d(x,y)} \right].
$$

If it can be shown that

$$
\psi(r) \xrightarrow[r \to 0]{} |\nabla^- g(x)|,\tag{22.82}
$$

then we can pass to the limit in (22.81) and recover

$$
\liminf_{s \downarrow 0} \frac{g(x) - H_s g(x)}{s} \ge |\nabla^- g(x)| q - L(q) = L^*(|\nabla^- g(x)|).
$$

If  $L'(\infty) < +\infty$  and  $|\nabla^- g(x)| = L'(\infty)$ , the above reasoning fails because  $\partial L^*(|\nabla^- g(x)|)$  might be empty. However, for any  $\theta < |\nabla^- g(x)|$ we may find  $q \in \partial L^*(\theta)$ , then the previous argument shows that

$$
\liminf_{s \downarrow 0} \frac{g(x) - H_s g(x)}{s} \ge L^*(\theta);
$$

the conclusion is obtained by letting  $\theta \to |\nabla^- g(x)|$  and using the lower semicontinuity of  $L^*$ .

So it all boils down to checking (22.82). This is where the semiconcavity of  $g$  will be useful. (Indeed  $(22.82)$  might fail for an arbitrary Lipschitz function.) The problem can be rewritten

$$
\lim_{r \to 0} \psi(r) = \lim_{r \to 0} \sup_{s \le r} \psi(s);
$$

so it is enough to show that  $\psi$  does have a limit at 0.

Let  $S_r$  denote the sphere of center x and radius r. If r is small enough, for any  $z \in S_r$  there is a unique geodesic joining x to z, and the exponential map induces a bijection between  $S_{r'}$  and  $S_r$ , for any  $r' \in (0, r]$ . Let  $\lambda = r'/r \in (0, 1]$ ; for any  $y \in S_{r'}$  we can find a unique geodesic  $\gamma$  such that  $\gamma_0 = x$ ,  $\gamma_\lambda = y$ ,  $\gamma_1 \in S_r$ . By semiconcavity, there is a constant  $C = C(x, r)$  such that

$$
g(\gamma_{\lambda}) - (1 - \lambda) g(\gamma_0) - \lambda g(\gamma_1) \geq -C \lambda (1 - \lambda) d(\gamma_0, \gamma_1)^2.
$$

This can be rewritten

$$
\frac{g(\gamma_0) - g(\gamma_1)}{d(\gamma_0, \gamma_1)} - \frac{g(\gamma_0) - g(\gamma_\lambda)}{\lambda d(\gamma_0, \gamma_1)} \ge -C \lambda (1 - \lambda) d(\gamma_0, \gamma_1);
$$

or equivalently,

$$
\frac{g(x) - g(\gamma_1)}{d(x, \gamma_1)} - \frac{g(x) - g(y)}{d(x, y)} \ge -C (r - r').
$$

So

$$
d(x,y) = r' \Longrightarrow \qquad \psi(r) - \frac{g(x) - g(y)}{d(x,y)} \ge -C(r - r').
$$

By taking the supremum over  $y$ , we conclude that

$$
\psi(r) - \psi(r') \geq -C(r - r').
$$

In particular  $\psi(r) + Cr$  is a nondecreasing function of r, so  $\psi$  has a limit as  $r \to 0$ . This concludes the proof. □

The following lemma was used in the proof of Theorem 22.46:

Lemma 22.49. Let M be a Riemannian manifold (or more generally, a geodesic space), and let L, R be positive numbers. If  $g : M \to \mathbb{R}$  is L-Lipschitz and  $|\nabla^- g(x)| < L$  for some  $x \in M$  then there is  $\delta > 0$  such that for any  $y \in B[x, R]$ ,

$$
g(x) - g(y) \le (L - \delta) d(x, y).
$$

Proof of Lemma 22.49. By assumption

$$
\limsup_{y \to x} \frac{[g(y) - g(x)]_-}{d(x, y)} < L.
$$

So there are  $r > 0$ ,  $\eta > 0$  such that if  $d(x, z) \leq r$  then

$$
g(x) \le g(z) + (L - \eta) d(x, z).
$$
 (22.83)

Let  $y \in B[z, R]$  and let  $\gamma$  be a geodesic joining  $\gamma(0) = x$  to  $\gamma(1) = y$ ; let  $z = \gamma(r/R)$ . Then  $d(x, z) = (r/R) d(x, y) \le r$ , so (22.83) holds true. As a consequence,
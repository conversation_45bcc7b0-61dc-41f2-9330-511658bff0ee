# <span id="page-0-2"></span>Emphasizing Discriminative Features for Dataset Distillation in Complex Scenarios

Kai <PERSON><sup>1,2∗</sup>, <PERSON><PERSON><PERSON><sup>1,2∗</sup>, <PERSON><PERSON><PERSON><PERSON><sup>2†</sup>, <PERSON><PERSON><sup>3</sup>, <PERSON><sup>3</sup>, <PERSON><PERSON><PERSON><PERSON><PERSON><sup>4</sup>, <PERSON><PERSON><sup>3</sup>, <PERSON><sup>2</sup>, <PERSON><sup>1†</sup> <sup>1</sup>NUS, <sup>2</sup>CMU, <sup>3</sup>University of Toronto, <sup>4</sup>Independent Researcher

# Abstract

*Dataset distillation has demonstrated strong performance on simple datasets like CIFAR, MNIST, and TinyImageNet but struggles to achieve similar results in more complex scenarios. In this paper, we propose EDF (emphasizes the discriminative features), a dataset distillation method that enhances key discriminative regions in synthetic images using Grad-CAM activation maps. Our approach is inspired by a key observation: in simple datasets, high-activation areas typically occupy most of the image, whereas in complex scenarios, the size of these areas is much smaller. Unlike previous methods that treat all pixels equally when synthesizing images, EDF uses Grad-CAM activation maps to enhance high-activation areas. From a supervision perspective, we downplay supervision signals produced by lower trajectorymatching losses, as they contain common patterns. Additionally, to help the DD community better explore complex scenarios, we build the Complex Dataset Distillation (Comp-DD) benchmark by meticulously selecting sixteen subsets, eight easy and eight hard, from ImageNet-1K. In particular, EDF consistently outperforms SOTA results in complex scenarios, such as ImageNet-1K subsets. Hopefully, more researchers will be inspired and encouraged to improve the practicality and efficacy of DD. Our code and benchmark have been made public at [NUS-HPC-AI-Lab/EDF.](https://github.com/NUS-HPC-AI-Lab/EDF)*

## 1. Introduction

Dataset Distillation (DD) has been making remarkable progress since it was first proposed by [\[54\]](#page-9-0). Currently, the mainstream of DD is matching-based methods [\[1,](#page-8-0) [68,](#page-10-0) [69,](#page-10-1) [71\]](#page-10-2), which first extract patterns from the real dataset, then define different types of supervision to inject extracted patterns into the synthetic data. On several simple benchmarks, such as CIFAR [\[19\]](#page-8-1) and TinyImageNet [\[21\]](#page-8-2), existing DD methods can achieve lossless performance [\[13,](#page-8-3) [25\]](#page-8-4).

<span id="page-0-0"></span>Image /page/0/Figure/7 description: The image displays a diagram illustrating a dataset distillation process and its performance evaluation. The top left shows a flow chart where CIFAR-10 is processed to select 10 similar classes from ImageNet-1K (IN1K), resulting in IN1K-CIFAR-10, and then undergoes Dataset Distillation (Trajectory-Matching). To the right of the flowchart is a bar chart comparing the 'Recovery Ratio (%)' for CIFAR-10 and IN1K-CIFAR-10 across different 'Compressing Ratio (%)' values of 0.1, 1, and 5. For CIFAR-10, the recovery ratios are 57.9%, 82%, and 93.1% at these ratios, respectively. For IN1K-CIFAR-10, the ratios are 53.8%, 75.5%, and 86.1%. Below the bar chart, a section labeled '(a) The performance of' is partially visible. To the right of the bar chart, there are two rows of images with superimposed heatmaps, indicating activation areas. The top row shows examples of 'CIFAR-10 Bird' and 'CIFAR-10 Dog', with activation means of 0.49 and 0.47, and high activation ratios of 43.2% and 42.5%, respectively. The bottom row shows examples of 'IN1K-CIFAR-10 Bird' and 'IN1K-CIFAR-10 Dog', with activation means of 0.25 and 0.28, and high activation ratios of 25.4% and 30.0%, respectively. A color bar on the right indicates activation values from 0.0 to 1.0.

dataset distillation drops (b) Images from IN1K-CIFAR-10 have much remarkably in complex lower activation means and smaller highly actiscenarios. vated areas.

Figure 1. (a) DD recovery ratio (distilled data accuracy over full data accuracy) comparison between CIFAR-10 and IN1K-CIFAR-10. We use trajectory matching for demonstration. (b) Comparison between Grad-CAM activation map statistics of CIFAR-10 and IN1K-CIFAR-10. The ratio refers to the percentage of pixels whose activation values are higher than 0.5.

However, DD still struggles to be practically used in realworld applications, *i.e.*, images in complex scenarios are characterized by significant variations in object sizes and the presence of a large amount of class-irrelevant information. To show that current DD methods fail to achieve satisfying performance in complex scenarios, we apply trajectory matching [\[13\]](#page-8-3) on a 10-class subset from ImageNet-1K extracted by selecting similar classes of CIFAR-10, called IN1K-CIFAR-10. As depicted in Figure [1a,](#page-0-0) under three compressing ratios, DD's performance[s](#page-0-1) on IN1K-CIFAR-10 are consistently worse than those on CIFAR-10.

To figure out the reason, we take a closer look at the ImageNet-1K and CIFAR-10 from the data perspective. One key observation is that the percentage of discriminative features in the complex scenario, which can be visualized by Grad-CAM [\[42\]](#page-9-1), is much lower. From Figure [1b,](#page-0-0) CIFAR-10 images are sticker-like, and activation maps have higher means and larger highly activated areas. By contrast, activation maps of the IN1K-CIFAR-10 subset exhibit much lower activation means and smaller highly activated areas. Previous methods [\[1,](#page-8-0) [13\]](#page-8-3) treat all pixels of synthetic images

<sup>∗</sup>equal contribution (<EMAIL>, <EMAIL>) † advising contribution. Part of this work was completed at CMU.

<span id="page-0-1"></span>We compare recovery ratios because datasets are different.

<span id="page-1-1"></span><span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays three 3D surface plots comparing activation levels across width and height for different image types: 'High loss distilled image', 'Initialized image', and 'Low loss distilled image'. Each plot shows activation on the z-axis, ranging from 0.0 to 1.0, with a color bar indicating the activation scale from blue (low) to red (high). The 'High loss distilled image' plot shows a mean activation of 0.53, with the text indicating 'Mean increased; Area expanded'. The 'Initialized image' plot has a mean activation of 0.45. The 'Low loss distilled image' plot shows a mean activation of 0.4, with text indicating 'Mean decreased; Area shrinked' and arrows pointing downwards labeled 'Shifted', suggesting a downward shift in activation.

(a) High-loss supervision increases activation means and expands the highactivation area, while low-loss supervision reduces the activation mean and shifts to the wrong discriminative area.

Image /page/1/Figure/2 description: The image displays three scatter plots side-by-side, labeled Iter 1000, Iter 4000, and Iter 8000. Each plot shows data points represented by circles and triangles in various colors (blue, purple, yellow, pink, and brown). Insets in the top right corner of each plot contain a 2x2 grid with colored circles and numerical values. The first inset shows values 3.7, 3.6, 3.7, and 4.4. The second inset shows 2.8, 3.5, 2.8, and 3.8. The third inset shows 2.5, 2.3, 2.5, and 2.9. The plots illustrate the progression of data clustering or distribution over iterations.

(b) Triangles and circles represent real and synthetic image features, respectively. As the distillation with low-loss only proceeds, more and more common patterns are introduced to synthetic images.

Figure 2. (a) Grad-CAM activation maps of the image with initialization, high-loss supervision distillation, and low-loss supervision distillation. (b) t-SNE visualization of image features with only low-loss supervision. Different colors represent different classes. The top right is inter-class distance computed by the average of point-wise distances.

equally. Therefore, when applying these methods to more complex scenarios, the large ratio of low-activation areas leads to non-discriminative features dominating the learning process, resulting in a performance drop.

As for supervision, taking trajectory matching as an example, we investigate the impact on synthetic images of supervision signals brought by different trajectory-matching losses. Specifically, we compare the performance between i) using only supervision from low trajectory-matching losses to update synthetic images and ii) using only supervision from high trajectory-matching losses to update synthetic images. The effect on a single image is shown in Figure [2a.](#page-1-0) Low-loss supervision reduces the mean of Grad-CAM activation maps and shrinks the high-activation area (also shifted). By contrast, high-loss supervision increases the activation mean and expands the high-activation region.

Additionally, we visualize the inter-class feature distribution for a broader view. In Figure [2b,](#page-1-0) we show the t-SNE of features of synthetic images distilled by only low trajectory-matching losses. As the distillation proceeds, synthetic image features of different classes continuously come closer, and the confusion among classes becomes more severe, which is likely caused by common patterns. The above two phenomenons confirm that low-loss supervision primarily reduces the representation of discriminative features and embeds more common patterns into synthetic images, harming DD's performance.

Based on the above observations, we propose to Emphasize Discriminative Features (EDF), built on trajectory matching. To synthesize more discriminative features in the distilled data, we enable discriminative areas to receive more updates compared with non-discriminative ones. This is achieved by guiding the optimization of synthetic images with gradient weights computed from Grad-CAM activation maps. Highly activated pixels are assigned higher gradients for enhancement. To mitigate the negative impact of common patterns, the EDF distinguishes between different supervision signals by dropping those with a low trajectory matching loss according to a drop ratio.

To help the community explore DD in complex scenarios, we extract new subsets from ImageNet-1K with various levels of complexity and build the Complex DD benchmark (Comp-DD). The complexity levels of these new subsets are determined by the average ratios of high-activation areas (Grad-CAM activation value > predefined threshold). We run EDF and several typical DD methods on partial Comp-DD and will release the full benchmark for future studies to further improve performance.

In summary, EDF consistently achieves state-of-the-art (SOTA) performance across various datasets, underscoring its effectiveness. On some ImageNet-1K subsets, EDF achieves lossless performance. To the best of our knowledge, we are the first to achieve lossless performance on ImageNet-1K subsets. We build the Complex Dataset Distillation benchmark based on complexity, providing convenience for future research to continue improving DD's performance in complex scenarios.

## 2. Method

Our approach, Emphasize Discriminative Features (EDF), enhances discriminative features of synthetic images. As shown in Figure [3,](#page-2-0) EDF first trains trajectories on real images  $T$  and synthetic images  $S$  and computes the trajectory matching loss. Then, *Common Pattern Dropout* filters out low-loss supervision signals, retaining high-loss ones for backpropagation. After obtaining gradients for the synthetic images, *Discriminative Area Enhancement* uses dynamically extracted Grad-CAM activation maps to rescale pixel gradients, focusing updates on discriminative regions. Although EDF is built on the trajectory-matching backbone, the two modules can be easily applied to other matching-based methods with minor modifications.

### 2.1. Common Pattern Dropout

This module reduces common patterns in supervision by matching expert and student trajectories on real and synthetic data, then removing low-loss elements. This ensures only meaningful supervision enhances the model's ability to capture discriminative features.

<span id="page-2-2"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: This is a flowchart illustrating a machine learning model. The left side, under the heading "Common Pattern Dropout", shows "Expert Trajectory" and "Student Trajectory" feeding into a "Trajectory Matching Loss". This loss is then used to update parameters, with a graph showing "loss" on the y-axis and "parameter" on the x-axis, indicating "drop low-loss supervision" with scissors symbols. The middle section shows a "DAE" module connected to "Pixel Gradients" via element-wise multiplication (⊗), which then updates "Syn. Images". The right side, under "Discriminative Area Enhancement", shows input images leading to "Grad-CAM" and an "Activation map". The activation map is processed by a function "F" (described as "function that transforms activation map to gradient weight") which takes values greater than or equal to the mean and adds beta, and values less than the mean and sets them to 1, resulting in a "Gradient weight" map. The function F is illustrated with a 3D surface plot showing values from 0.0 to 1.0 on the z-axis.

Figure 3. Workflow of Emphasize Discriminative Features (EDF). EDF comprises two modules: (1) *Common Pattern Dropout*, which filters out low-loss signals, and the (2) *Discriminative Area Enhancement*, which amplifies gradients in critical regions. β denotes the enhancement factor. "mean" denotes the mean activation value of the activation map.

Trajectory Generation and Loss Computation. To generate expert and student trajectories, we first train agent models on real data for  $E$  epochs, saving the resulting parameters as expert trajectories, denoted by  $\{\theta_t\}_0^E$ . At each distillation iteration, we randomly select an initial point  $\theta_t$  and a target point  $\theta_{t+M}$  from these expert trajectories. Similarly, student trajectories are produced by initializing an agent model at  $\theta_t$  and training it on the synthetic dataset, yielding the parameters  $\{\hat{\theta}_t\}_{0}^{\bar{N}}$ . The trajectory matching loss is the distance between final student parameters  $\hat{\theta}_{t+N}$  and expert's target parameters  $\theta_{t+M}$ , normalized by the initial difference:

$$
L = \frac{||\hat{\theta}_{t+N} - \theta_{t+M}||^2}{||\theta_{t+M} - \theta_t||^2}.
$$
 (1)

Instead of directly summing this loss, we decompose it into an array of individual losses between corresponding parameters in the expert and student trajectories, represented as  $L = \{l_1, l_2, \ldots, l_P\}$ , where P is the number of parameters, and  $l_i$  is the loss associated with the *i*-th parameter.

Low-loss Element Dropping. Our analyses of Figure [2a](#page-1-0) and [2b](#page-1-0) show that signals of low-loss trajectories typically introduce common patterns, which hinder the learning of key discriminative features, particularly in complex scenarios. To address this, we sort the array of losses computed from the previous step in ascending order. Using a predefined dropout ratio  $\alpha$ , we discard the smallest  $|\alpha \cdot P|$  losses ( $| \cdot |$ denotes the floor function), which are assumed to capture common, non-discriminative features. The remaining losses are summed and normalized to form the final supervision:

$$
L \stackrel{\text{sort}}{\longrightarrow} L' = \{ \underbrace{l_1, l_2, \cdots, l_{\lfloor \alpha \cdot P \rfloor}}, \underbrace{l_{\lfloor \alpha \cdot P \rfloor + 1}, \cdots, l_P}_{\text{sum&normalize}} \}, (2)
$$

where  $L'$  represents the updated loss array after dropping the lowest  $|\alpha \cdot P|$  elements. The remaining losses,  $l_{\alpha P \mid +1}, \ldots, l_P$ , are then summed and normalized to form the final supervision signal.

### 2.2. Discriminative Area Enhancement

After the pruned loss from *Common Pattern Dropout* is backpropagated, this module amplifies the importance of discriminative regions in synthetic images. Grad-CAM activation maps are dynamically extracted from the synthetic data to highlight areas most relevant for classification. These activation maps are then used to rescale the pixel gradients, applying a weighted update that prioritizes highly activated regions, thereby focusing the learning process on key discriminative features.

Activation Map Extraction. Grad-CAM generates classspecific activation maps by leveraging the gradients that flow into the final convolutional layer, highlighting key areas relevant to predicting a target class. To compute these maps, we first train a convolutional model  $G$  on the real dataset. Following the Grad-CAM formulation (Equation [3\)](#page-2-1), we calculate the activation map for each class  $c: M^c \in \mathbb{R}^{IPC \times H \times W}$ on the synthetic images (IPC is the number of images per class). The activation map  $M<sup>c</sup>$  is a gradient-weighted sum of feature maps across all convolutional layers:

<span id="page-2-1"></span>
$$
\alpha^{c} = \frac{1}{Z} \sum_{h} \sum_{w} \frac{\partial y^{c}}{\partial A^{l}_{h,w}}, \quad M^{c} = ReLU(\sum_{l} \alpha_{l}^{c} A^{l}). \tag{3}
$$

 $\alpha^c$  represents the weight of activation corresponding to the *l*-th feature map,  $A<sup>l</sup>$ , computed by gradients. Finally, we concatenate  $M^c$  of all images in class c and obtain  $M \in$  $\mathbb{R}^{|S| \times H \times W}$ . Note that the capability of the model used to extract activation maps does not affect the performance. We provide ablation in the Appendix [11.5.](#page-13-0)

Discriminative Area Biased Update. A major limitation of previous DD algorithms [\[1,](#page-8-0) [8,](#page-8-5) [13\]](#page-8-3) on the complex scenario is that they treat each pixel equally and provide no guidance for the distillation process on which area of synthetic images should be emphasized. Therefore, we propose to update synthetic images in a biased manner. Instead of treating each pixel equally, we enhance the significance of discriminative

<span id="page-3-1"></span><span id="page-3-0"></span>Image /page/3/Figure/0 description: The image displays two charts side-by-side. The left chart is a circular diagram divided into segments, each representing a category like 'Dog', 'Car', 'Bird', 'Music', 'Insect Round', 'Fish', 'Snake', and 'Dog'. Each segment is further divided into two parts, labeled '#easy' and '#hard', with associated numbers. For example, 'Dog' has '#easy 12108' and '#hard 12958'. The right chart is a bar graph comparing 'Easy' and 'Hard' categories across different items such as 'Bird', 'Car', 'Snake', 'Dog', 'Fish', 'Insect', 'Round', and 'Music'. The y-axis is labeled 'Complexity (%)' and ranges from 50 to 75. Each bar has a numerical value displayed above it, and red arrows indicate the difference in complexity between the 'Easy' and 'Hard' bars for each item. For instance, 'Bird' has 'Easy' at 66 and 'Hard' at 74.5 with a difference of 8.5. 'Car' has 'Easy' at 57.4 and 'Hard' at 64.4 with a difference of 7.0. 'Snake' has 'Easy' at 64.3 and 'Hard' at 70.8 with a difference of 6.5. 'Dog' has 'Easy' at 64.8 and 'Hard' at 70.8 with a difference of 6.0. 'Fish' has 'Easy' at 60.4 and 'Hard' at 66.4 with a difference of 6.0. 'Insect' has 'Easy' at 67.9 and 'Hard' at 73 with a difference of 5.1. 'Round' has 'Easy' at 64.6 and 'Hard' at 69.3 with a difference of 4.7. 'Music' has 'Easy' at 64.1 and 'Hard' at 68.7 with a difference of 4.6. The text 'xx diff. of complexity' is also present near the top right of the bar chart.

(a) Number of images in each (b) Complexity of Easy vs. Hard subsets in subset each category

Figure 4. (a) Statistics of the training set in the Comp-DD benchmark. Each subset contains 500 images in the validation set. (b) Comparison of subset-level complexity between easy and hard subsets across all categories. The complexity of hard subsets is higher than that of easy subsets.

areas by guiding the optimization with activation maps extracted in the previous step. We define the discriminative area of a synthetic image as the percentage of pixels with activation values above the mean since synthetic images are dynamically changing (see Section [4.3](#page-4-0) for discussion). Specifically, we process activation maps from the previous step with a function  $\mathcal{F}(M, \beta)$  to create weights for pixel gradients as follows:

<span id="page-3-2"></span>
$$
\mathcal{F}(M_{h,w}^i, \beta) = \begin{cases} 1 & \text{if } M_{h,w}^i < \bar{M}^i, \\ \beta + M_{h,w}^i & \text{if } M_{h,w}^i \ge \bar{M}^i. \end{cases}
$$
 (4)

 $M_{h,w}^i$  denotes the activation value of the *i* the image at coordinate  $(h, w)$ , and  $\overline{M}^i$  denotes the mean activation of  $M^i$ .  $\beta \geq 1$  is called the *enhancement factor*. Then, we rescale the gradient matrix of synthetic images by multiplying it with the weight matrix element-wise:

$$
(\nabla D_{syn})_{edf} = \nabla D_{syn} \odot \mathcal{F}(M, \beta). \tag{5}
$$

We drag gradients of discriminative areas to a higher range so that they receive more updates.

Dynamic Update of Activation Maps. As synthetic images are optimized, high-activation regions shift over time. To capture these changes, we recompute the activation maps every K iterations, focusing updates on the most relevant areas. The frequency  $K$  is a tunable hyperparameter, adjusted based on the learning rate of the synthetic images (see Section [4.3](#page-4-0) for details). This ensures evolving discriminative areas are accurately captured. The complete algorithm is provided in the Appendix [9.](#page-11-0)

## 3. Complex Dataset Distillation Benchmark

We introduce the Complex Dataset Distillation (Comp-DD) benchmark, which is constructed by selecting subsets from ImageNet-1K based on their complexity. This benchmark represents an early and pioneering effort to address dataset distillation in complex scenarios. Although there are numerous benchmarks [\[7,](#page-8-6) [19,](#page-8-1) [21\]](#page-8-2) for simpler tasks, there is a notable absence of benchmarks designed specifically for complex scenarios. This gap presents a significant challenge to advancing research in this area and limits the practical application of dataset distillation. To bridge this gap, we propose the first dataset distillation benchmark explicitly built around scenario complexity, aiming to promote further exploration within the DD community.

Complexity Metrics. We evaluate the complexity of an image by measuring the average size of high-activation regions of the Grad-CAM activation map. Using a pre-trained ResNet model, we first generate Grad-CAM activation maps for all images, class by class. For each image, we calculate the percentage of pixels with activation values above a predefined threshold  $\alpha$  (set to 0.5 in our case), with higher percentages indicating lower complexity (more clarifications can be found in Appendix [12.2\)](#page-15-0). Formally, the complexity of the *i*-th image is computed as  $1 - \frac{\sum_h \sum_w 1[M_{h,w}^i \ge \alpha]}{H \cdot W}$  where 1 is the indicator function. The complexity of each class is then determined by averaging the complexity scores across all images within that class.

Subset Selection. To reduce the influence of class differences, we select subsets from each *category*, where a category consists of classes representing visually similar objects or animals of the same species. This approach allows us to focus on complexity while controlling for inter-class variability. Specifically, we first manually identify representative categories in ImageNet-1K with sufficient numbers of classes  $(> 20)$ . For each category, we rank the classes by complexity in descending order. Following established practice, we construct two ten-class subsets for each category: the *easy* subset, comprising the ten least complex classes, and the *hard* subset, comprising the ten most complex classes. The subset-level complexity is determined by averaging the complexity scores across all classes in each subset.

Statistics. We carefully selected eight categories from ImageNet-1K: Bird, Car, Dog, Fish, Snake, Insect, Round, and Music. Each category contains two ten-class subsets: one *easy* and one *hard*, with difficulty determined by the complexity metrics outlined above. Figure [4a](#page-3-0) summarizes the number of training images in each subset, while all subsets contain 500 images in the validation set. To illustrate the difference between easy and hard subsets, Figure [4b](#page-3-0) compares the subset-level complexity for each category. As expected, the hard subsets display significantly higher complexity than the easy subsets. For a detailed breakdown of the classes in each subset, please refer to Appendix [12.1.](#page-13-1)

<span id="page-4-1"></span>

## 4. Experiments

## 4.1. Experimental Setup

Datasets and Architecture. We conduct a comprehensive evaluation of EDF on six ten-class subsets [\[16\]](#page-8-7) of ImageNet-1K (ImageNette, ImageWoof, ImageMeow, ImageYellow, ImageFruit, and ImageSquawk) and a one-hundred-class subset (ImageNet100). Each subset contains ten classes, with approximately 13,000 images in the training set and 500 images in the validation set. On the Comp-DD benchmark, we report the results of the Bird, Car, and Dog categories. For all experiments, we use a 5-layer ConvNet (ConvNetD5) as both the distillation and the evaluation architecture. For cross-architecture evaluation (see results in Appendix  $11.1$ ), we validate synthetic data accuracy on Alexnet [\[20\]](#page-8-8), VGG11 [\[45\]](#page-9-2), ResNet18 [\[14\]](#page-8-9).

Baselines. We compare two baselines: dataset distillation (DD) methods and methods utilizing knowledge distillation (Eval. w/ Knowledge Distillation) [\[15\]](#page-8-10). For DD methods, we compare trajectory-matching-based methods such as MTT  $[1]$ , FTD  $[8]$ , and DATM  $[13]$ . In the knowledge distillation group, we compare against SRe2L [\[64\]](#page-9-3) and RDED [\[48\]](#page-9-4). The results for subsets not covered in these papers are obtained through replication using the official open-source recipe.

## 4.2. Main Results

ImageNet-1K Subsets. We compare our approach with previous works on various ImageNet-1K subsets. As shown in Table [1,](#page-5-0) EDF consistently achieves state-of-the-art (SOTA) results across all settings. On larger IPCs, *i.e.*, 200 or 300, the performance of EDF significantly outperforms that observed with smaller IPCs. We achieve lossless performances on ImageMeow and ImageYellow under IPC300, 23% of real data, as shown in Table [2.](#page-5-1) When evaluated against Eval. w/ Knowledge Distillation methods, our distilled datasets outperform SRe2L and RDED in most settings. It is important to note that applying knowledge distillation (KD) for evaluation tends to reduce EDF's pure dataset distillation performance, particularly in low IPC (images per class) settings such as IPC1 and IPC10. This occurs because smaller IPCs lack the capacity to effectively incorporate the knowledge from a well-trained teacher model. We also provide results without knowledge distillation in Appendix [11.2.](#page-12-1)

CIFAR-10 and Tiny-ImageNet. We also evaluate our approach on simple datasets, *i.e.*, CIFAR-10 and Tiny-ImageNet. As illustrated in Table [3,](#page-5-2) EDF can still outperform the SOTAs of the DD and Eval. w/ KD approaches in most settings. The results confirm the good generalization of our approach in simple scenarios.

Comp-DD Benchmark. The results for EDF on the Bird, Car, and Dog categories from the Comp-DD Benchmark are

shown in Table [4.](#page-6-0) EDF demonstrates superior test accuracy. As expected, the recovery ratios for easy subsets are consistently higher than those for hard subsets, confirming that the hard subsets present a greater challenge for DD methods. These results validate our complexity metrics, which effectively distinguish the varying levels of difficulty between easy and hard subsets.

<span id="page-4-0"></span>

### 4.3. Ablation Study

In this section, we perform ablation studies to analyze key components of our approach. Unless otherwise specified, the following results are all based on ConvNetD5.

Effect of Modules. We validate the contribution of Discriminative Area Enhancement (DAE) and Common Pattern Dropout (CPD), respectively. As illustrated in Table [5,](#page-6-1) both DAE and CPD significantly improve the baseline performance. DAE's biased updates toward high-activation areas effectively enhance the discriminative features in synthetic images. CPD, on the other hand, mitigates the negative influence of common patterns by filtering out low-loss supervision, ensuring that the synthetic images retain their discriminative properties.

Supervision Dropout Ratio. The dropout ratio in CPD is critical for balancing the removal of common patterns and dataset capacity (IPC). As shown in Table [6a,](#page-7-0) smaller IPCs benefit most from moderate dropout ratios (12.5-25%), which filter low-loss signals while preserving important information. For larger IPCs, higher dropout ratios (37.5-50%) improve performance, as these datasets can tolerate more aggressive filtering. However, an excessively high ratio (e.g., 75%) reduces performance across all IPCs by discarding too much information, weakening the ability to learn.

Frequency of Activation Map Update. To accurately capture the evolving discriminative features in synthetic images, EDF dynamically updates the Grad-CAM activation maps at a predefined frequency. The choice of update frequency should be adjusted based on the IPC to achieve optimal per-formance. As shown in Table [6b,](#page-7-0) larger IPCs benefit from a lower update frequency, as the pixel learning rate is set lower for more stable distillation. In contrast, smaller IPCs require a higher update frequency to effectively adapt to the faster changes in the synthetic images during training.

This trend is influenced by the pixel learning rate: larger IPCs can use lower rates to ensure smooth convergence, making frequent updates unnecessary. Smaller IPCs, with limited data capacity, require higher learning rates and more frequent updates to quickly adapt to changes in discriminative areas. Thus, selecting the appropriate update frequency is essential for balancing stability and adaptability in the distillation process, depending on dataset size and complexity.

Strategies for Discriminative Area Enhancement. The Discriminative Area Enhancement (DAE) component in-

<span id="page-5-3"></span><span id="page-5-0"></span>

|              | <b>IPC</b>   |                              |                             | DD                            |                              |                                      |                              | Eval. w/ Knowledge Distillation |                             | Full            |
|--------------|--------------|------------------------------|-----------------------------|-------------------------------|------------------------------|--------------------------------------|------------------------------|---------------------------------|-----------------------------|-----------------|
| Dataset      |              | Random                       | <b>MTT</b>                  | <b>FTD</b>                    | <b>DATM</b>                  | <b>EDF</b>                           | SRe2L                        | <b>RDED</b>                     | <b>EDF</b>                  |                 |
|              | $\mathbf{1}$ | $12.6 \pm 1.5$               | $47.7_{\pm 0.9}$            | $52.2{\scriptstyle~ \pm 1.0}$ | $52.5{\scriptstyle \pm1.0}$  | $\overline{52.6}_{\pm 0.5}$          | $20.8{\rm \pm 0.2}$          | $28.9 \pm 0.1$                  | $25.7_{\pm 0.4}$            |                 |
| ImageNette   | 10           | $44.8_{\pm1.3}$              | $63.0_{\pm1.3}$             | $67.7_{\pm0.7}$               | $68.9_{\pm0.8}$              | $\textbf{71.0}_{\pm 0.8}$            | $50.6_{\pm0.8}$              | $59.0_{\pm1.0}$                 | $\mathbf{64.5}_{\pm0.6}$    | $87.8_{\pm1.0}$ |
|              | 50           | $60.4_{\pm1.4}$              | $\sqrt{}$                   | $\overline{\wedge}$           | $75.4_{\pm0.9}$              | $\textbf{77.8}_{\pm 0.5}$            | $73.8_{\pm0.6}$              | $83.1_{\pm0.6}$                 | $\textbf{84.8}_{\pm 0.5}$   |                 |
|              | 1            | $\overline{11.4}_{\pm 1.3}$  | $28.6{\pm}0.8$              | $30.1_{\pm 1.0}$              | $30.4_{\pm 0.7}$             | $30.8_{\pm 1.0}$                     | $15.8 + 0.8$                 | $18.0_{\pm 0.3}$                | $19.2_{\pm 0.2}$            |                 |
| ImageWoof    | 10           | $20.2_{\pm1.2}$              | $35.8 + 1.8$                | $38.8{\pm}1.4$                | $40.5_{\pm0.6}$              | $\textbf{41.8}_{\pm 0.2}$            | $38.4_{\pm0.4}$              | $40.1_{\pm0.2}$                 | $42.3_{\pm 0.3}$            | $66.5_{\pm1.3}$ |
|              | 50           | $28.2_{\pm0.9}$              | $\sqrt{}$                   | $\sqrt{}$                     | $47.1_{\pm 1.1}$             | $\textbf{48.4}_{\pm 0.5}$            | $49.2_{\pm 0.4}$             | $60.8_{\pm 0.5}$                | $\mathbf{61.6}_{\pm0.8}$    |                 |
|              | 1            | $11.2_{\pm 1.2}$             | $30.7_{\pm 1.6}$            | $33.8 + 1.5$                  | $34.0_{\pm 0.5}$             | $34.5{\scriptstyle \pm0.2}$          | $22.2_{\pm 0.6}$             | $19.2_{\pm 0.8}$                | $20.8 + 0.5$                |                 |
| ImageMeow    | 10           | $22.4_{\pm0.8}$              | $40.4_{\pm2.2}$             | $43.3 \pm 0.6$                | $48.9_{\pm1.1}$              | $\mathbf{52.6}_{\pm0.4}$             | $27.4_{\pm0.5}$              | $44.2_{\pm0.6}$                 | $\textbf{48.4}_{\pm 0.7}$   | $65.2_{\pm0.8}$ |
|              | 50           | $38.0_{\pm 0.5}$             | $\overline{\phantom{a}}$    | $\overline{\phantom{a}}$      | $56.8_{\pm0.9}$              | $58.2_{\pm 0.6}$                     | $35.8 \pm 0.7$               | $55.0_{\pm 0.6}$                | $\mathbf{58.2}_{\pm0.9}$    |                 |
|              | $\mathbf{1}$ | $14.8_{\pm 1.0}$             | $45.2_{\pm 0.8}$            | $47.7_{\pm 1.1}$              | $48.5{\scriptstyle \pm 0.4}$ | $49.4_{\pm 0.5}$                     | $31.8{\scriptstyle \pm 0.7}$ | $30.6{\scriptstyle \pm0.2}$     | $33.5_{\pm 0.6}$            |                 |
| Image Yellow | 10           | $41.8_{\pm1.1}$              | $60.0_{\pm1.5}$             | $62.8_{\pm1.4}$               | $65.1_{\pm0.7}$              | $\mathbf{68.2}_{\pm 0.4}$            | $48.2_{\pm0.5}$              | $59.2_{\pm0.5}$                 | $\textbf{60.8}_{\pm 0.5}$   | $83.2_{\pm0.9}$ |
|              | 50           | $54.6_{\pm0.5}$              | $\overline{\wedge}$         | $\overline{\mathcal{M}}$      | $70.2_{\pm 0.8}$             | $73.2_{\pm 0.8}$                     | $57.6_{\pm0.9}$              | $75.8 + 0.7$                    | $\textbf{76.2}_{\pm 0.3}$   |                 |
|              | 1            | $12.4_{\pm 0.9}$             | $26.6{\scriptstyle \pm0.8}$ | $\overline{29.1}_{\pm 0.9}$   | $30.9_{\pm 1.0}$             | $32.8_{\pm 0.6}$                     | $23.4_{\pm 0.5}$             | $33.8{\scriptstyle \pm 0.4}$    | $29.6{\scriptstyle \pm0.4}$ |                 |
| ImageFruit   | 10           | $20.0_{\pm 0.6}$             | $40.3_{\pm0.5}$             | $44.9_{\pm1.5}$               | $45.5_{\pm0.9}$              | $\textbf{46.2}_{\pm 0.6}$            | $39.2_{\pm0.7}$              | $45.4_{\pm 0.6}$                | $\textbf{48.4}_{\pm 0.8}$   | $64.4_{\pm0.8}$ |
|              | 50           | $33.6{\scriptstyle \pm 0.9}$ | $\overline{\wedge}$         | $\sqrt{}$                     | $50.2{\scriptstyle \pm 0.5}$ | $\mathbf{53.2}_{\pm0.5}$             | $44.2{\scriptstyle \pm 0.8}$ | $54.8 + 0.9$                    | $\textbf{56.4}_{\pm 0.6}$   |                 |
|              | 1            | $13.2_{\pm 1.1}$             | $39.4_{\pm 1.5}$            | $40.5{\scriptstyle \pm0.9}$   | $41.1_{\pm 0.6}$             | $\overline{\textbf{41.8}}_{\pm 0.5}$ | $\overline{21.2}_{\pm 1.0}$  | $33.8_{\pm 0.6}$                | $30.5_{\pm 0.5}$            |                 |
| ImageSquawk  | 10           | $29.6{\scriptstyle \pm1.5}$  | $52.3_{\pm1.0}$             | $58.4_{\pm1.5}$               | $61.8_{\pm1.3}$              | $\mathbf{65.4}_{\pm0.8}$             | $39.2_{\pm0.3}$              | $59.0_{\pm 0.5}$                | $\textbf{59.4}_{\pm 0.6}$   | $86.4_{\pm0.8}$ |
|              | 50           | $52.8 \pm 0.4$               | $\overline{\wedge}$         | $\overline{\phantom{a}}$      | $71.0_{\pm1.2}$              | $74.8_{\pm 1.2}$                     | $56.8 \pm 0.4$               | $77.2_{\pm 1.2}$                | $77.8_{\pm0.5}$             |                 |
|              | 1            | $2.4_{\pm 0.3}$              |                             |                               | $9.8{\pm}1.1$                | $\overline{11.5}_{\pm 1.0}$          |                              | $\textbf{9.4}_{\pm0.2}$         | $8.1 \scriptstyle{\pm 0.6}$ |                 |
| ImageNet100  | 10           | $15.2_{\pm0.5}$              |                             |                               | $20.9_{\pm 0.8}$             | $\textbf{21.9}_{\pm 0.8}$            |                              | $28.2{\pm}0.8$                  | $\mathbf{32.0}_{\pm0.5}$    | $56.4_{\pm0.4}$ |
|              | 50           | $29.7_{\pm0.2}$              |                             |                               | $42.7_{\pm0.7}$              | $\textbf{44.2}_{\pm 0.6}$            |                              | $42.8_{\pm0.2}$                 | $\textbf{45.6}_{\pm 0.5}$   |                 |

Table 1. Results of depth-5 ConvNet on ImageNet-1K subsets. indicates worse performance than DATM. EDF achieves SOTAs on all settings compared with DD methods. Compared with SRe2L and RDED, we achieve SOTAs on 16 out of 21 settings.

<span id="page-5-1"></span>

| Subset | ImageMeow        |                                    | ImageYellow      |                                    |
|--------|------------------|------------------------------------|------------------|------------------------------------|
|        | IPC              | 200                                | 300              | 200                                |
| Random | $52.8_{\pm 0.4}$ | $55.3_{\pm 0.3}$                   | $70.5_{\pm 0.5}$ | $72.8_{\pm 0.8}$                   |
| DATM   | $60.7_{\pm 0.6}$ | $64.1_{\pm 0.7}$                   | $79.3_{\pm 1.0}$ | $82.1_{\pm 1.1}$                   |
| EDF    | $62.5_{\pm 0.7}$ | <b><math>65.9_{\pm 0.6}</math></b> | $81.0_{\pm 0.9}$ | <b><math>84.2_{\pm 0.7}</math></b> |
| Full   | $65.2_{\pm 1.3}$ |                                    | $83.2_{\pm 0.9}$ |                                    |

Table 2. Bolded entries are lossless performances under IPC300.

<span id="page-5-2"></span>

| Dataset       | <b>IPC</b> | DD               |                             | Eval. w/KD                  |                   |  |  |
|---------------|------------|------------------|-----------------------------|-----------------------------|-------------------|--|--|
|               |            | <b>DATM</b>      | <b>EDF</b>                  | <b>RDED</b>                 | EDF               |  |  |
|               |            | $46.9_{\pm 0.5}$ | 47.8 $\scriptstyle \pm 0.3$ | $23.5_{\pm 0.3}$            | $28.1_{\pm 0.5}$  |  |  |
| CIFAR-10      | 50         | $76.1_{\pm 0.3}$ | $77.3_{\pm0.5}$             | $68.4_{\pm 0.1}$            | $69.5_{\pm 0.3}$  |  |  |
|               | 500        | $83.5 + 0.2$     | $84.8_{\pm 0.5}$            |                             | $86.1{\pm}0.4$    |  |  |
|               |            | $17.1_{\pm 0.3}$ | $17.8_{\pm 0.2}$            | $12.0_{\pm 0.1}$            | $10.3_{\pm 0.4}$  |  |  |
| Tiny-ImageNet | 10         | $31.1_{\pm 0.3}$ | $32.5 + 0.5$                | $39.6{\scriptstyle \pm0.1}$ | $40.5 + 0.7$      |  |  |
|               | 50         | $39.7_{\pm 0.3}$ | 41.1 $\pm$ 0.6              | 47.6 $\pm$ 0.2              | 48.9 $_{\pm 0.3}$ |  |  |

Table 3. EDF also outperforms baselines of two approaches on datasets in simple scenarios, such as CIFAR-10 and Tiny-ImageNet.

volves two key factors: the enhancement factor  $\beta$  and the threshold for activation maps. Ablation studies (Table [7a\)](#page-7-1) show that the best performance is achieved when  $\beta$  is between 1 and 2. When  $\beta$  < 1, some discriminative areas are diminished rather than enhanced, as their gradient weights become < 1. Conversely, excessively large  $\beta$  values ( $\geq 10$ ) lead to overemphasis on certain areas, distorting the overall learning process (see Appendix [11.3](#page-12-2) for examples of this distortion). Therefore,  $\beta$  should be reasonably controlled to

balance the emphasis on discriminative regions.

Dynamic thresholding using mean activation values outperforms fixed thresholds (Table [7b\)](#page-7-1). This is because the mean adapts to the evolving activation maps during training, whereas a fixed threshold risks either emphasizing lowactivation areas if set too low or omitting key discriminative features if set too high.

## 5. Analysis and Discussion

Disitlled Images of Different Supervision. As pointed out earlier, low-loss supervision tends to introduce common patterns, such as backgrounds and general colors, while high-loss supervision contains discriminative, class-specific features. To visualize this effect, we select two images with similar backgrounds and colors, but distinct objects. Two images are then distilled by high-loss and low-loss supervision, respectively. As shown in Figure [5a,](#page-6-2) common patterns are indeed widely present in low-loss supervision distilled images, making two images hard to distinguish. In contrast, high-loss supervision preserves more discriminative details, enabling the model to distinguish between two classes. This confirms the validity of dropping low-loss supervision and underscores the effectiveness of the *Common Pattern Dropout* (CPD) in mitigating the negative impact of common features.

Enhancement of Discriminative Areas. Our Discriminative Area Enhancement (DAE) module aims to amplify updates in high-activation areas of synthetic images, as identified by Grad-CAM. To show how DAE enhances discrimi-

<span id="page-6-0"></span>

| Method      |                       | Bird-Easy                  |                  | Bird-Hard                                 |                  | Dog-Easy         |                             | Dog-Hard                                                                         |                  | Car-Easy         |                  | Car-Hard                   |
|-------------|-----------------------|----------------------------|------------------|-------------------------------------------|------------------|------------------|-----------------------------|----------------------------------------------------------------------------------|------------------|------------------|------------------|----------------------------|
| <b>IPC</b>  | 10                    | 50                         | 10               | 50                                        | 10               | 50               | 10                          | 50                                                                               | 10               | 50               | 10               | 50                         |
| Random      | $32.4_{\pm 0.5}$      | $53.8 + 0.6$               | $22.6_{\pm 0.7}$ | $41.8_{\pm 0.5}$                          | $26.0_{\pm 0.4}$ | $30.8_{\pm 0.8}$ | $14.5_{\pm 0.2}$            | 27.6 $\pm$ 0.7                                                                   | $18.2_{\pm 0.4}$ | $34.4_{\pm 0.3}$ | $25.6 + 0.5$     | $40.4_{\pm 0.5}$           |
| <b>FTD</b>  | $60.0_{\pm 1.1}$      | 63.4 $\pm$ 0.6             | $54.4_{\pm 0.8}$ | 59.6 $\pm$ 1.2                            | $41.1_{\pm 1.3}$ | $45.9_{\pm 0.9}$ | $36.5{\scriptstyle \pm1.1}$ | 43.5 $\pm$ 0.9                                                                   | $44.4_{\pm 1.1}$ | 49.6 $\pm$ 0.5   | $52.1_{\pm 0.5}$ | $55.6 + 0.9$               |
| <b>DATM</b> | $62.2_{\pm 0.4}$      | 67.1 $\pm$ 0.3             | $56.0_{\pm0.5}$  | 62.9 $\pm$ 0.8                            | $42.8_{\pm 0.7}$ | $48.2_{\pm 0.5}$ | 38.6 $\pm$ 0.7              | $47.4_{\pm 0.5}$                                                                 | $46.4_{\pm 0.5}$ | $53.8 + 0.6$     | $53.2_{\pm 0.6}$ | $58.7_{+0.8}$              |
| <b>EDF</b>  | 63.4 $\pm$ 0.5        | 69.0 $_{\pm \text{0.8}}$ ' |                  | 57.1 <sub>+0.4</sub> 64.8 <sub>+0.6</sub> | 43.2 $\pm$ 0.5   | 49.4 $\pm$ 0.8   | $39.6_{\pm0.9}$             | 49.2 $\pm$ 0.3                                                                   | 47.6 $\pm$ 0.7   | 54.6 $\pm$ 0.2   | , 55.4 $\pm$ 0.8 | 61.0 $\scriptstyle\pm 0.5$ |
| Full        |                       | $81.6_{\pm 1.0}$           |                  | $82.4_{\pm 0.8}$                          |                  | $57.3_{\pm 0.3}$ |                             | $58.4_{\pm 0.5}$                                                                 |                  | $63.5_{\pm 0.2}$ |                  | $72.8_{\pm 1.1}$           |
|             |                       |                            |                  |                                           |                  |                  |                             | (a) EDF achieves SOTAs on Bird, Dog, and Car categories of the Comp-DD benchmark |                  |                  |                  |                            |
|             | Category              |                            |                  | <b>Bird</b>                               |                  |                  | Dog                         |                                                                                  |                  |                  | Car              |                            |
|             | <b>IPC</b>            |                            | 10               | 50                                        |                  | 10               |                             | 50                                                                               |                  | 10               |                  | 50                         |
|             | Complexity            | Easy                       | Hard             | Easy                                      | Hard             | Easy             | Hard                        | Hard<br>Easy                                                                     | Easy             | Hard             | Easy             | Hard                       |
|             | Recovery ratio $(\%)$ | 77.7                       | 69.3             | 84.6                                      | 76.9             | 75.4             | 67.8                        | 86.2<br>84.2                                                                     | 76.2             | 75.6             | 87.4             | 83.7                       |
|             |                       |                            |                  |                                           |                  |                  |                             |                                                                                  |                  |                  |                  |                            |

(b) Recovery ratios of easy subsets are higher than that of hard subsets, aligning with the complexity metrics.

Table 4. (a) Partial results on Bird, Dog, and Car categories of the Complex DD Benchmark under IPC 10 and 50. (b) Recovery ratios of EDF on the partial Complex DD Benchmark.

<span id="page-6-2"></span>Image /page/6/Figure/3 description: The image displays a comparison of image distillation techniques, specifically DATM and EDF, against initialized images. The left side of the figure (a) illustrates how low-loss supervision embeds common patterns (background, colors) and discriminative features, showing examples of high loss, real images, and low loss. The right side of the figure (b) presents a grid of images. The top row shows initialized images with percentages below them, ranging from 32.6% to 41.3%. The middle row shows DATM distilled images, with percentages ranging from 40.6% to 45.3%. The bottom row shows EDF distilled images, with percentages ranging from 38.4% to 57.1%, and also includes percentage increases below each image, such as 24.5%, 8.2%, 14.3%, 8.1%, 1.9%, 2.1%, 6.7%, and 5.6%. The caption states that EDF largely increases the percentage of discriminative areas, achieving the highest percentage with an average of 9%, and that the distilled images contain more discriminative features.

(a) Low-loss supervision mainly embeds com-(b) EDF largely increases the percentage of discriminative areas (bottom left figure of each image) with an average of 9%, achieving the highest. Our distilled images contain more discriminative features.

Figure 5. (a) Comparison between high-loss and low-loss supervision distilled images. (b) Comparison of discriminative areas in images produced by initialization, DATM, and EDF. Figures at the bottom are increments made by EDF over the initial image.

<span id="page-6-1"></span>

| <b>DAE</b> | <b>CPD</b> | ImageWoof   | ImageMeow   | ImageYellow |
|------------|------------|-------------|-------------|-------------|
|            |            | 39.2        | 48.9        | 65.1        |
|            |            | 40.3        | 49.5        | 66.2        |
| ✓          |            | 41.1        | 51.2        | 67.5        |
| ✓          | ✓          | <b>41.8</b> | <b>52.6</b> | <b>68.2</b> |

Table 5. Ablation results of two modules, DAE and CPD, on three ImageNet-1K subsets. Both modules bring improvements to the performance, underscoring individual efficacy.

native areas, we visualize the same group of images under initialization, DATM distillation, and EDF distillation in Figure [5b](#page-6-2) We also report discriminative area statistics, computed by the percentage of pixels whose activation values are higher than the mean, on each image at the bottom left. As can be discovered, DATM is capable of increasing discriminative regions, while EDF can achieve a more significant enhancement. Visually, the enhancement manifests through an increased number of core objects and enlarged areas of class-specific features. Moreover, EDF's enhancement is more pronounced, especially when the image has smaller

discriminative areas initially, e.g., discriminative features of the first column image increase by 24.5%. These phenomena demonstrate that EDF is effective in capturing and emphasizing discriminative features.

Supervision Dropout Criteria. We investigate the impact of the following dropout strategies: (i) dynamic dropout, which includes random selection from all layers, and (ii) static dropout, which includes uniform selection across layers and fixed selection from the first, middle, or last layers. As shown in Table [8,](#page-7-2) all strategies except EDF's loss-based dropout lead to performance degradation, with uniform selection and last-layer dropout causing the most significant degradation.

The reasons for this are twofold. First, supervision of low losses—primarily located in the shallow layers of the model—is the main source of common patterns. Discarding supervision from deep layers, where loss values are higher (random selection, uniform selection, or last-layer dropout), reduces the presence of discriminative features. Second, static dropout fails to account for the dynamic nature of lowloss supervision, as trajectory-matching losses vary across layers as the distillation process evolves.

<span id="page-7-3"></span><span id="page-7-0"></span>

|             |    | Ratio (%)   |      |             |             |      |      |
|-------------|----|-------------|------|-------------|-------------|------|------|
|             |    | 0           | 12.5 | 25          | 37.5        | 50   | 75   |
| ImageFruit  | 1  | <b>32.8</b> | 32.4 | 32.3        | 31.8        | 30.6 | 29.1 |
|             | 10 | 45.4        | 45.9 | <b>46.5</b> | 46.2        | 45.8 | 44.3 |
|             | 50 | 49.5        | 50.1 | 50.7        | <b>50.9</b> | 50.6 | 49.2 |
| ImageSquawk | 1  | <b>41.8</b> | 41.3 | 41.2        | 41.0        | 39.6 | 38.1 |
|             | 10 | 64.8        | 65.0 | <b>65.4</b> | 65.2        | 64.9 | 63.2 |
|             | 50 | 73.9        | 74.2 | 74.6        | <b>74.8</b> | 74.5 | 72.8 |

(a) Within a reasonable range, the target supervision dropout ratio increases as the IPC becomes larger. Dropping too much supervision could result in losing too much information.

Table 6. (a) Results of different supervision dropout ratios across various IPCs. (b) Results of different activation map update frequencies across various IPCs.

<span id="page-7-1"></span>

| IPC | Enhancement Factor ( $\beta$ ) |             |      |      |      | IPC  | Enhancement Factor ( $\beta$ ) |      |      |      |      | IPC  | Activation Threshold |             |  |  |
|-----|--------------------------------|-------------|------|------|------|------|--------------------------------|------|------|------|------|------|----------------------|-------------|--|--|
|     | 0.5                            | 1           | 2    | 5    | 10   | 0.5  | 1                              | 2    | 5    | 10   | 0.2  | 0.5  | 0.8                  | mean        |  |  |
| 1   | 33.4                           | <b>34.5</b> | 34.3 | 33.2 | 31.8 | 29.1 | <b>30.8</b>                    | 30.5 | 30.2 | 28.8 | 34.2 | 34.0 | 33.8                 | <b>34.5</b> |  |  |
| 10  | 50.1                           | <b>52.6</b> | 52.1 | 49.4 | 49.0 | 40.9 | <b>41.8</b>                    | 41.0 | 40.4 | 40.4 | 51.2 | 52.3 | 51.5                 | <b>52.6</b> |  |  |
| 50  | 57.8                           | <b>59.5</b> | 59.2 | 58.1 | 57.6 | 47.5 | <b>48.2</b>                    | 48.4 | 48.1 | 47.2 | 58.0 | 59.0 | 58.4                 | <b>59.5</b> |  |  |

(a) Results on ImageMeow (left) and ImageWoof (right). ImageWoof has a higher complexity. Enhancement factor should be set within a reasonable range ( $\geq 1$  and  $\leq 5$  in general).

(b) Using "mean" as a dynamic threshold gives the best performance on three IPCs.

(b) Within a reasonable range, a higher frequency performs better on small IPCs, while larger IPCs prefer a lower frequency.

Table 7. (a) Ablation of the enhancement factor on ImageMeow and ImageWoof. (b) Ablation of the activation threshold on ImageMeow.

<span id="page-7-2"></span>Image /page/7/Figure/7 description: The image displays a bar chart illustrating the trajectory-matching loss per layer in ConvNetD5. The x-axis represents the layers, numbered 1 through 11. The y-axis indicates the matching loss, ranging from 0.0 to 0.3. Four sets of bars are shown for each layer, corresponding to different iterations: Iter 0 (blue), Iter 1000 (yellow), Iter 2000 (green), and Iter 3000 (red). Below the bar chart, a table categorizes strategies: Random, Uniform, First, Middle, Last, and EDF. For each strategy, it lists the parameters assigned to layers and the corresponding accuracy (Acc. (%)). The accuracies are: Random (71.3%), Uniform (68.7%), First (71.9%), Middle (73.1%), Last (65.4%), and EDF (74.8%). The EDF strategy shows the highest accuracy.

Table 8. EDF's loss-wise dropout performs the best. The dropping ratio of all criteria is fixed at 25%. "Param. to layer" refers to layers that contain dropped trajectory parameters.

## 6. Related Work

Approaches. DD aims to create compact datasets that maintain performance levels comparable to full-scale datasets. Approaches in DD can be categorized into two primary approaches: matching-based and knowledge-distillation-based.

1) *Matching-based approaches* are foundational in DD research, focusing on aligning synthetic data with real datasets by capturing essential patterns. Landmark works like gradient matching (DC) [\[71\]](#page-10-2), distribution matching (DM) [\[68\]](#page-10-0), and trajectory matching (MTT) [\[1\]](#page-8-0) extract critical metrics from real datasets, then replicate these metrics in synthetic data. Subsequent research has refined these methods, improving the fidelity of distilled datasets  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$  $[2, 18, 23, 28, 30, 41, 51,$ [69,](#page-10-1) [72\]](#page-10-3). Data selection techniques have been integrated to synthesize more representative samples [\[24,](#page-8-15) [49,](#page-9-8) [59\]](#page-9-9). Recent advancements optimize distillation for different image-perclass (IPC) settings, balancing dataset size and information retention [\[3,](#page-8-16) [9,](#page-8-17) [13,](#page-8-3) [24,](#page-8-15) [25\]](#page-8-4). Moreover, soft labels have been widely applied to improve the performance [\[6,](#page-8-18) [40,](#page-9-10) [47,](#page-9-11) [65\]](#page-9-12). Current matching-based methods treat pixels uniformly, overlooking discriminative features and supervision signals, limiting their effectiveness on complex datasets.

2) *Knowledge-distillation-based approaches* take an alternative route by aligning teacher-student model outputs when evaluating distilled datasets. Notable examples include SRe2L [\[64\]](#page-9-3) and RDED [\[48\]](#page-9-4). In our work, we adopt knowledge distillation as a validation strategy for fair comparisons.

Benchmarks. Some benchmarks for dataset distillation, such as DC-BENCH [\[7\]](#page-8-6) and DD-Ranking [\[26\]](#page-8-19), have been proposed in light of the unfairness issue in previous DD evaluation and provide a more fair comparison scheme. In this work, we propose the Comp-DD benchmark for DD in complex scenarios and offer a new angle to evaluate existing methods. Comp-DD systematically explores dataset distillation complexity by curating subsets from ImageNet-1K with varying degrees of difficulty. This benchmark provides a more rigorous evaluation framework, facilitating deeper exploration of DD in complex, real-world settings and encouraging further advances in the field.

## 7. Conclusion

We introduced Emphasize Discriminative Features (EDF), a dataset distillation method that enhances class-specific regions in synthetic images. EDF addresses two key limitations of prior methods: i) enhancing discriminative regions in synthetic images using Grad-CAM activation maps, and ii) filtering out low-loss signals that embed common patterns through *Common Pattern Dropout (CPD)* and *Discriminative Area Enhancement (DAE)*. EDF achieves state-of-the-art results across ImageNet-1K subsets, including lossless performance on several of them. We also proposed the Comp-DD benchmark, designed to evaluate dataset distillation in both simple and complex settings.

## 8. Acknowledgement

We thank Bo Zhao, Guang Li, Dai Liu, Zhenghao Zhao, Songhua Liu, Ruonan Yu, Zhiyuan Liang, and Gongfan Fang for valuable discussions and feedback. This work also was supported by the National Research Foundation, Singapore, under its AI Singapore Programme (AISG Award No: AISG2-PhD-2021-08-008). Yang You's group is being sponsored by NUS startup grant (Presidential Young Professorship), Singapore MOE Tier-1 grant, ByteDance grant, ARCTIC grant, SMI grant (WBS number: A8001104- 00-00), Alibaba grant, and Google grant for TPU usage.

## References

- <span id="page-8-0"></span>[1] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, pages 10708–10717, 2022. [1,](#page-0-2) [3,](#page-2-2) [5,](#page-4-1) [8,](#page-7-3) [2](#page-1-1)
- <span id="page-8-11"></span>[2] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A. Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *CVPR*, 2023. [8](#page-7-3)
- <span id="page-8-16"></span>[3] Xuxi Chen, Yu Yang, Zhangyang Wang, and Baharan Mirzasoleiman. Data distillation can be like vodka: Distilling more times for better quality. In *ICLR*, 2023. [8](#page-7-3)
- <span id="page-8-21"></span>[4] Yilan Chen, Wei Huang, and Tsui-Wei Weng. Provable and efficient dataset distillation for kernel ridge regression. In *NeurIPS*, 2024. [6](#page-5-3)
- <span id="page-8-22"></span>[5] Zongxiong Chen, Jiahui Geng, Derui Zhu, Herbert Woisetschlaeger, Qing Li, Sonja Schimmler, Ruben Mayer, and Chunming Rong. A comprehensive study on dataset distillation: Performance, privacy, robustness and fairness. *ArXiv*, 2023. [6](#page-5-3)
- <span id="page-8-18"></span>[6] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *ICML*, 2022. [8,](#page-7-3) [2](#page-1-1)
- <span id="page-8-6"></span>[7] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dcbench: Dataset condensation benchmark. In *NeurIPS*, 2022. [4,](#page-3-1) [8,](#page-7-3) [6](#page-5-3)
- <span id="page-8-5"></span>[8] Jiawei Du, Yiding Jiang, Vincent Y. F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *CVPR*, 2022. [3,](#page-2-2) [5,](#page-4-1) [1,](#page-0-2) [8](#page-7-3)
- <span id="page-8-17"></span>[9] Jiawei Du, Qin Shi, and Joey Tianyi Zhou. Sequential subset matching for dataset distillation. *ArXiv*, abs/2311.01570, 2023. [8](#page-7-3)
- <span id="page-8-23"></span>[10] Jiawei Du, Xin Zhang, Juncheng Hu, Wenxin Huang, and Joey Tianyi Zhou. Diversity-driven synthesis: Enhancing dataset distillation through directed weight adjustment. In *NeurIPS*, 2024. [6](#page-5-3)
- <span id="page-8-20"></span>[11] François-Guillaume Fernandez. Torchcam: class activation explorer. [https://github.com/frgfm/torch](https://github.com/frgfm/torch-cam)[cam](https://github.com/frgfm/torch-cam), 2020. [1,](#page-0-2) [3](#page-2-2)
- <span id="page-8-28"></span>[12] Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. *ArXiv*, abs/2311.15529, 2023. [8](#page-7-3)

- <span id="page-8-3"></span>[13] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. In *ICLR*, 2024. [1,](#page-0-2) [3,](#page-2-2) [5,](#page-4-1) [8](#page-7-3)
- <span id="page-8-9"></span>[14] Kaiming He, X. Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, 2015. [5,](#page-4-1) [2](#page-1-1)
- <span id="page-8-10"></span>[15] Geoffrey E. Hinton, Oriol Vinyals, and Jeffrey Dean. Distilling the knowledge in a neural network. *ArXiv*, abs/1503.02531, 2015. [5](#page-4-1)
- <span id="page-8-7"></span>[16] Jeremy Howard. Imagenette and imagewoof: Imagenet subsets for classification. [https://github.com/fastai/](https://github.com/fastai/imagenette) [imagenette](https://github.com/fastai/imagenette), 2019. [5](#page-4-1)
- <span id="page-8-24"></span>[17] Zachary Izzo and James Zou. A theoretical study of dataset distillation. In *NeurIPS*, 2023. [6](#page-5-3)
- <span id="page-8-12"></span>[18] Samir Khaki, Ahmad Sajedi, Kai Wang, Lucy Z. Liu, Yuri A. Lawryshyn, and Konstantinos N. Plataniotis. Atom: Attention mixer for efficient dataset distillation. *ArXiv*, 2024. [8](#page-7-3)
- <span id="page-8-1"></span>[19] Alex Krizhevsky. Learning multiple layers of features from tiny images. 2009. [1,](#page-0-2) [4](#page-3-1)
- <span id="page-8-8"></span>[20] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E. Hinton. Imagenet classification with deep convolutional neural networks. *Communications of the ACM*, 60:84 – 90, 2012. [5,](#page-4-1) [2](#page-1-1)
- <span id="page-8-2"></span>[21] Ya Le and Xuan S. Yang. Tiny imagenet visual recognition challenge. 2015. [1,](#page-0-2) [4](#page-3-1)
- <span id="page-8-26"></span>[22] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sung-Hoon Yoon. Dataset condensation with contrastive signals. In *ICML*, 2022. [8](#page-7-3)
- <span id="page-8-13"></span>[23] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sung-Hoon Yoon. Dataset condensation with contrastive signals. In *ICML*, 2022. [8](#page-7-3)
- <span id="page-8-15"></span>[24] Yongmin Lee and Hye Won Chung. Selmatch: Effectively scaling up dataset distillation via selection-based initialization and partial updates by trajectory matching. In *ICML*, 2024. [8](#page-7-3)
- <span id="page-8-4"></span>[25] Zekai Li, Ziyao Guo, Wangbo Zhao, Tianle Zhang, Zhi-Qi Cheng, Samir Khaki, Kaipeng Zhang, Ahmad Sajedi, Konstantinos N Plataniotis, Kai Wang, and Yang You. Prioritize alignment in dataset distillation. *ArXiv*, 2024. [1,](#page-0-2) [8](#page-7-3)
- <span id="page-8-19"></span>[26] Zekai Li, Xinhao Zhong, Zhiyuan Liang, Yuhao Zhou, Mingjia Shi, Ziqiao Wang, Wangbo Zhao, Xuanlei Zhao, Haonan Wang, Ziheng Qin, Dai Liu, Kaipeng Zhang, Tianyi Zhou, Zheng Zhu, Kun Wang, Guang Li, Junhao Zhang, Jiawei Liu, Yiran Huang, Lingjuan Lyu, Jiancheng Lv, Yaochu Jin, Zeynep Akata, Jindong Gu, Rama Vedantam, Mike Shou, Zhiwei Deng, Yan Yan, Yuzhang Shang, George Cazenavette, Xindi Wu, Justin Cui, Tianlong Chen, Angela Yao, Manolis Kellis, Konstantinos N. Plataniotis, Bo Zhao, Zhangyang Wang, Yang You, and Kai Wang. Dd-ranking: Rethinking the evaluation of dataset distillation. GitHub repository, 2024. [8](#page-7-3)
- <span id="page-8-27"></span>[27] Dai Liu, Jindong Gu, Hu Cao, Carsten Trinitis, and Martin Schulz. Dataset distillation by automatic training trajectories. In *ECCV*, 2024. [8](#page-7-3)
- <span id="page-8-14"></span>[28] Haoyang Liu, Tiancheng Xing, Luwei Li, Vibhu Dalal, Jingrui He, and Haohan Wang. Dataset distillation via the wasserstein metric. *ArXiv*, abs/2311.18531, 2023. [8](#page-7-3)
- <span id="page-8-25"></span>[29] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *NeurIPS*, 2022. [6](#page-5-3)

- <span id="page-9-5"></span>[30] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Hua Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *ICCV*, pages 17268–17278, 2023. [8](#page-7-3)
- <span id="page-9-23"></span>[31] Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. In *NeurIPS*, 2022. [8](#page-7-3)
- <span id="page-9-24"></span>[32] Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. In *ICML*, 2023. [8](#page-7-3)
- <span id="page-9-13"></span>[33] Noel Loo, Alaa Maalouf, Ramin Hasani, Mathias Lechner, Alexander Amini, and Daniela Rus. Large scale dataset distillation with domain shift. In *ICML*, 2024. [6](#page-5-3)
- [34] Jonathan Lorraine, Paul Vicol, and David Duvenaud. Optimizing millions of hyperparameters by implicit differentiation. In *PMLR*, 2019.
- [35] Alaa Maalouf, Murad Tukan, Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. On the size and approximation error of distilled sets. In *NeurIPS*, 2023.
- <span id="page-9-14"></span>[36] Hao Miao, Ziqiao Liu, Yan Zhao, Chenjuan Guo, Bin Yang, Kai Zheng, and Christian S. Jensen. Less is more: Efficient time series dataset condensation via two-fold modal matching– extended version. *arXiv*, 2024. [6](#page-5-3)
- <span id="page-9-29"></span>[37] Brian B. Moser, Federico Raue, Sebastián M. Palacio, Stanislav Frolov, and Andreas Dengel. Latent dataset distillation with diffusion models. *ArXiv*, abs/2403.03881, 2024. [8](#page-7-3)
- <span id="page-9-21"></span>[38] Timothy Nguyen, Zhourong Chen, and Jaehoon Lee. Dataset meta-learning from kernel ridge-regression. In *ICLR*, 2021. [8](#page-7-3)
- <span id="page-9-22"></span>[39] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *NeurIPS*, 2021. [8](#page-7-3)
- <span id="page-9-10"></span>[40] Tian Qin, Zhiwei Deng, and David Alvarez-Melis. A label is worth a thousand images in dataset distillation. In *NeurIPS*, 2024. [8,](#page-7-3) [6](#page-5-3)
- <span id="page-9-6"></span>[41] Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z. Liu, Yuri A. Lawryshyn, and Konstantinos N. Plataniotis. Datadam: Efficient dataset distillation with attention matching. In *ICCV*, pages 17097–17107, 2023. [8](#page-7-3)
- <span id="page-9-1"></span>[42] Ramprasaath R. Selvaraju, Abhishek Das, Ramakrishna Vedantam, Michael Cogswell, Devi Parikh, and Dhruv Batra. Grad-cam: Visual explanations from deep networks via gradient-based localization. *International Journal of Computer Vision*, 128:336 – 359, 2016. [1](#page-0-2)
- <span id="page-9-31"></span>[43] Yuzhang Shang, Zhihang Yuan, and Yan Yan. Mim4dd: Mutual information maximization for dataset distillation. In *NeurIPS*, 2023. [8](#page-7-3)
- <span id="page-9-25"></span>[44] Seung-Jae Shin, Heesun Bae, DongHyeok Shin, Weonyoung Joo, and Il-Chul Moon. Loss-curvature matching for dataset selection and condensation. In *AISTATS*, 2023. [8](#page-7-3)
- <span id="page-9-2"></span>[45] Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *CoRR*, abs/1409.1556, 2014. [5,](#page-4-1) [2](#page-1-1)
- <span id="page-9-30"></span>[46] Duo Su, Junjie Hou, Weizhi Gao, Yingjie Tian, and Bowen Tang. D4m: Dataset distillation via disentangled diffusion model. In *CVPR*, 2024. [8](#page-7-3)
- <span id="page-9-11"></span>[47] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *2021 International*

*Joint Conference on Neural Networks (IJCNN)*, pages 1–8, 2021. [8](#page-7-3)

- <span id="page-9-4"></span>[48] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *CVPR*, 2024. [5,](#page-4-1) [8,](#page-7-3) [2](#page-1-1)
- <span id="page-9-8"></span>[49] Anirudh S. Sundar, Gökçe Keskin, Chander Chandak, I-Fan Chen, Pegah Ghahremani, and Shalini Ghosh. Prune then distill: Dataset distillation with importance sampling. In *ICASSP*, 2023. [8](#page-7-3)
- <span id="page-9-15"></span>[50] Paul Vicol, Jonathan P Lorraine, Fabian Pedregosa, David Duvenaud, and Roger B Grosse. On implicit bias in overparameterized bilevel optimization. In *PMLR*, 2022. [6](#page-5-3)
- <span id="page-9-7"></span>[51] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Hua Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe learning to condense dataset by aligning features. In *CVPR*, 2022. [8,](#page-7-3) [2](#page-1-1)
- <span id="page-9-28"></span>[52] Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Hua Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *ArXiv*, abs/2303.04707, 2023. [8](#page-7-3)
- <span id="page-9-27"></span>[53] Shaobo Wang, Yicun Yang, Zhiyuan Liu, Chenghao Sun, Xuming Hu, Conghui He, and Linfeng Zhang. Dataset distillation with neural characteristic function: A minmax perspective. In *CVPR*, 2025. [8](#page-7-3)
- <span id="page-9-0"></span>[54] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros. Dataset distillation. *ArXiv*, 2020. [1,](#page-0-2) [2](#page-1-1)
- <span id="page-9-16"></span>[55] Xing Wei, Anjia Cao, Funing Yang, and Zhiheng Ma. Sparse parameterization for epitomic dataset distillation. In *NeurIPS*, 2023. [6](#page-5-3)
- [56] Xindi Wu, Byron Zhang, Zhiwei Deng, and Olga Russakovsky. Vision-language dataset distillation. In *TMLR*, 2024.
- [57] Yifan Wu, Jiawei Du, Ping Liu, Yuewei Lin, Wei Xu, and Wenqing Cheng. Dd-robustbench: An adversarial robustness benchmark for dataset distillation. *ArXiv*, 2024.
- <span id="page-9-17"></span>[58] Lingao Xiao and Yang He. Are large-scale soft labels necessary for large-scale dataset distillation? In *NeurIPS*, 2024. [6](#page-5-3)
- <span id="page-9-9"></span>[59] Yue Xu, Yong-Lu Li, Kaitong Cui, Ziyu Wang, Cewu Lu, Yu-Wing Tai, and Chi-Keung Tang. Distill gold from massive ores: Efficient dataset distillation via critical samples selection. *ArXiv*, abs/2305.18381, 2023. [8](#page-7-3)
- <span id="page-9-18"></span>[60] Yue Xu, Zhilin Lin, Yusong Qiu, Cewu Lu, and Yong-Lu Li. Low-rank similarity mining for multimodal dataset distillation. In *ICML*, 2024. [6](#page-5-3)
- <span id="page-9-26"></span>[61] Shaolei Yang, Shen Cheng, Mingbo Hong, Haoqiang Fan, Xing Wei, and Shuaicheng Liu. Neural spectral decomposition for dataset distillation. In *ECCV*, 2024. [8](#page-7-3)
- <span id="page-9-19"></span>[62] William Yang, Ye Zhu, Zhiwei Deng, and Olga Russakovsky. What is dataset distillation learning? In *ICML*, 2024. [6](#page-5-3)
- <span id="page-9-20"></span>[63] Zeyuan Yin and Zhiqiang Shen. Dataset distillation in large data era. *ArXiv*, 2023. [6](#page-5-3)
- <span id="page-9-3"></span>[64] Zeyuan Yin, Eric P. Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *NeurIPS*, 2023. [5,](#page-4-1) [8,](#page-7-3) [2](#page-1-1)
- <span id="page-9-12"></span>[65] Ruonan Yu, Songhua Liu, Zigeng Chen, Jingwen Ye, and Xinchao Wang. Heavy labels out! dataset distillation with label space lightening. *ArXiv*, 2024. [8,](#page-7-3) [5](#page-4-1)

- <span id="page-10-6"></span>[66] Bowen Yuan, Zijian Wang, Mahsa Baktashmotlagh, Yadan Luo, and Zi Huang. Color-oriented redundancy reduction in dataset distillation. In *NeurIPS*, 2024. [6](#page-5-3)
- <span id="page-10-8"></span>[67] Hansong Zhang, Shikun Li, Pengju Wang, Dan Zeng, and Shiming Ge. M3d: Dataset condensation by minimizing maximum mean discrepancy. In *AAAI*, 2023. [8](#page-7-3)
- <span id="page-10-0"></span>[68] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. *WACV*, pages 6503–6512, 2021. [1,](#page-0-2) [8,](#page-7-3) [2](#page-1-1)
- <span id="page-10-1"></span>[69] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, 2021. [1,](#page-0-2) [8,](#page-7-3) [2](#page-1-1)
- <span id="page-10-10"></span>[70] Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan, 2022. [8](#page-7-3)
- <span id="page-10-2"></span>[71] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICML*, 2021. [1,](#page-0-2) [8,](#page-7-3) [2](#page-1-1)
- <span id="page-10-3"></span>[72] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. *CVPR*, pages 7856–7865, 2023. [8](#page-7-3)
- <span id="page-10-4"></span>[73] Wangbo Zhao, Yizeng Han, Jiasheng Tang, Zhikai Li, Yibing Song, Kai Wang, Zhangyang Wang, and Yang You. A stitch in time saves nine: Small vlm is a precise guidance for accelerating large vlms. *ArXiv*, 2024. [2](#page-1-1)
- <span id="page-10-5"></span>[74] Wangbo Zhao, Jiasheng Tang, Yizeng Han, Yibing Song, Kai Wang, Gao Huang, Fan Wang, and Yang You. Dynamic tuning towards parameter and inference efficiency for vit adaptation. In *NeurIPS*, 2024. [2](#page-1-1)
- <span id="page-10-11"></span>[75] Zhenghao Zhao, Yuzhang Shang, Junyi Wu, and Yan Yan. Dataset quantization with active learning based adaptive sampling. In *ECCV*, 2024. [8](#page-7-3)
- <span id="page-10-12"></span>[76] Zhenghao Zhao, Haoxuan Wang, Yuzhang Shang, Kai Wang, and Yan Yan. Distilling long-tailed datasets. In *CVPR*, 2025. [8](#page-7-3)
- <span id="page-10-9"></span>[77] Xinhao Zhong, Hao Fang, Bin Chen, Xulin Gu, Tao Dai, Meikang Qiu, and Shu-Tao Xia. Hierarchical features matter: A deep exploration of gan priors for improved dataset distillation. *ArXiv*, abs/2406.05704, 2024. [8](#page-7-3)
- <span id="page-10-7"></span>[78] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. In *NeurIPS*, 2022. [8](#page-7-3)

## Emphasizing Discriminative Features for Dataset Distillation in Complex Scenarios

## Supplementary Material

We organize our supplementary material as follows:

## Algorithm of EDF:

• Appendix [9:](#page-11-0) Pseudo code of EDF with detailed explanation.

## Experimental Settings:

- Appendix [10.1:](#page-11-1) Training recipe.
- Appendix [10.2:](#page-12-3) Evaluation recipe.
- Appendix [10.3:](#page-12-4) Computing resources required for different settings.

### Additional Experimental Results and Findings:

- Appendix [11.1:](#page-12-0) Cross-architecture evaluation.
- Appendix [11.2:](#page-12-1) Results of distilled datasets without knowledge-distillation-based evaluation.
- Appendix [11.3:](#page-12-2) Distorted synthetic images under excessive enhancement factors.
- Appendix [11.4:](#page-13-2) The changing trend of discriminative areas in EDF distillation.
- Appendix [11.5:](#page-13-0) The impact of using different models to extract activation maps.

### Comp-DD Benchmark

- Appendix [12.1:](#page-13-1) Subset details of the Comp-DD benchmark.
- Appendix [12.3:](#page-15-1) Hyper-parameters of the Comp-DD benchmark.
- Appendix [12.2:](#page-15-0) More clarifications on the complexity metrics.

### Visualization

• Appendix [13:](#page-15-2) Visualization of EDF distilled images.

### Related Work

• Appendix [14:](#page-15-3) More related work of dataset distillation.

<span id="page-11-0"></span>

## 9. Algorithm of EDF

Algorithm [1](#page-11-2) provides a pseudo-code of EDF. Lines 1-7 specify inputs of the EDF, including a trajectory-matching algorithm  $A$ , the model for Grad-CAM  $G$ , the frequency of activation map update  $K$ , the supervision dropout ratio  $\alpha$ , the enhancement factor  $\beta$ , the activation map processing function  $F$ , and the number of distillation iterations  $T$ .

Lines 12-14 describe the Common Pattern Dropout module. After we obtain the trajectory matching losses from  $A$ , we sort them in ascending order to get ordered losses. Then, the smallest  $\alpha |L|$  elements are dropped as they introduce non-discriminative common patterns.

Lines 15-19 describe the Discriminative Area Enhancement module. For every  $K$  iterations, we update activation

<span id="page-11-2"></span>

### Algorithm 1 Emphasizing Discriminative Features

## Input: $D_{real}$ : The real dataset

- **Input:**  $D_{syn}$ : The synthetic dataset
- Input: A: A trajectory-matching based algorithm
- Input:  $G$ : Grad-CAM model

**Input:**  $K$ : Activation maps update frequency

- **Input:**  $\alpha$ : Threshold of supervision dropout
- **Input:**  $T$ : Total distillation steps
- **Input:**  $\beta$ : Enhancement factor

**Input:**  $F$ : Activation map processing function

Input: r: Learning rate of synthetic dataset

| 1: for t in $0 \dots T - 1$ do                                                       |                            |
|--------------------------------------------------------------------------------------|----------------------------|
| $L \leftarrow \mathcal{A}(D_{syn}, D_{real})$<br>2:                                  | $\triangleright$ Compute   |
| the array of trajectory matching losses                                              |                            |
| $L' \leftarrow Sort(L)$<br>3:                                                        | $\triangleright$ Sort L to |
| get ordered losses                                                                   |                            |
| $L_{edf} \leftarrow \sum_{i=\alpha L }^{ L } L'_i$<br>4:                             | $\triangleright$ Dropout   |
| low-loss supervision                                                                 |                            |
| <b>if</b> t mod $K = 0$ then<br>5:                                                   |                            |
| $M \leftarrow \mathcal{G}(D_{sun})$<br>6:                                            | $\triangleright$ Update    |
| activation maps of current S                                                         |                            |
| end if<br>7:                                                                         |                            |
| $(\nabla D_{sun})_{EDF} \leftarrow \nabla D_{sun} \circ \mathcal{F}(M, \beta)$<br>8: | $\triangleright$ Process   |
| synthetic image gradients                                                            |                            |
| $D_{syn} \leftarrow D_{syn} - r \cdot (\nabla D_{syn})_{EDF}$<br>9:                  | ⊳ Biased                   |
| update towards discriminative areas                                                  |                            |
| 10: end for                                                                          |                            |
| 11: Return $D_{sum}$                                                                 |                            |

maps of synthetic images. The gradients of synthetic images are then processed by the function  $F$  (see Equation [4](#page-3-2) for the computation). Finally, synthetic images are updated biasedly towards discriminative areas.

## 10. Experimental Settings

<span id="page-11-1"></span>

### 10.1. Training Details

We follow previous trajectory matching works  $[8, 13, 25]$  $[8, 13, 25]$  $[8, 13, 25]$  $[8, 13, 25]$  $[8, 13, 25]$ to train expert trajectories for one hundred epochs. Hyperparameters are directly adopted without modification. For distillation, we implement EDF based on DATM [\[13\]](#page-8-3) and PAD [\[25\]](#page-8-4), which simultaneously distills soft labels along with images.

We use torch-cam [\[11\]](#page-8-20) for Grad-CAM implementation. Hyper-parameters are listed in Table [9.](#page-13-3)

<span id="page-12-3"></span>

## 10.2. Evaluation Details

To achieve a fair comparison, when comparing EDF with DD methods, we only adopt the set of differentiable augmentations commonly used in previous studies [\[1,](#page-8-0) [68,](#page-10-0) [69\]](#page-10-1) to train a surrogate model on distilled data and labels.

When comparing EDF with DD+KD methods, we follow their evaluation methods, which we detail the steps as follows:

- 1. Train a teacher model on the real dataset and freeze it afterward.
- 2. Train a student model on the distilled dataset by minimizing the KL-Divergence loss between the output of the student model and the output of the teacher model on the same batch from distilled data.
- 3. Validate the student model on the test set and obtain test accuracy.

For implementation, please refer to the official repo of SRe2[L](#page-12-5) and RDE[D.](#page-12-6)

<span id="page-12-4"></span>

## 10.3. Computing Resources

Experiments on IPC 1/10 can be run with 4x Nvidia-A100 80GB GPUs, and experiments on IPC 50 can be run with 8x Nvidia-A100 80GB GPUs. The GPU memory demand is primarily dictated by the volume of synthetic data per batch and the total training iterations the augmentation model undergoes with that data. When IPC becomes large, GPU usage can be optimized by either adopting techniques like TESLA [\[6\]](#page-8-18) or by scaling down the number of training iterations ("syn steps") or shrinking the synthetic data batch size ("batch\_syn").

### 11. Additional experiment results and findings

<span id="page-12-0"></span>

## 11.1. Cross-architecture Evaluation

Generalizability on different model architectures is one key property of a well-distilled dataset. To show that EDF can generalize well on different models, we evaluate synthetic images under IPC 10 and 50 of the ImageSquawk subset, on three other standard models, AlexNet [\[20\]](#page-8-8), VGG11 [\[45\]](#page-9-2), and ResNet18 [\[14\]](#page-8-9). As shown in Table [14,](#page-16-0) our distilled datasets outperform random selection and two baseline methods on both IPC10 and IPC50. Compared with IPC10, distilled images under IPC50 can achieve better performance on unseen neural networks. This suggests that EDF's distillation results have decent generalizability across different architectures, especially when the compressing ratio is smaller which allows distilled datasets to accommodate more discriminative information.

<span id="page-12-6"></span><https://github.com/LINs-lab/RDED>

<span id="page-12-1"></span>

### 11.2. Eval. without Knowledge Distillation

Starting from [\[54\]](#page-9-0), representative dataset distillation (DD) methods [\[1,](#page-8-0) [51,](#page-9-7) [69,](#page-10-1) [71\]](#page-10-2) establish a general workflow as follows: 1) *Distillation*: At this stage, information from the real dataset is fully accessible to the DD algorithm to train synthetic data. 2) *Evaluation*: After the distilled dataset is obtained, the evaluation is performed by training a randomly initialized model on the distilled data. Specifically, in the context of classification, the objective is to minimize crossentropy loss. Recently, some new methods [\[48,](#page-9-4) [64,](#page-9-3) [73,](#page-10-4) [74\]](#page-10-5) introduced teacher knowledge into the student model by applying knowledge distillation. Although it helps improve performances to a large extent, it may not be able to reflect the effectiveness of dataset distillation accurately.

To this end, we remove the knowledge distillation from Eval. w/ Knowledge Distillation (SRe2L and RDED) methods but keep soft labels to ensure a fair comparison, Specifically, we train a classification model on the synthetic images by only minimizing the cross-entropy loss between student output and soft labels. As shown in Table [11,](#page-14-0) without knowledge distillation, EDF outperforms SRe2L and RDED in 8 out of 9 settings. Our advantage is more pronounced, especially when IPC is smaller, underscoring the superior efficacy of EDF on smaller compressing ratios.

<span id="page-12-2"></span>

## 11.3. Distorted Images of Large Enhancement Factor

In Figure [6,](#page-12-7) we show results of using excessively large enhancement factors as mentioned in Section [4.3.](#page-4-0) The distributions of these distilled images are distorted, with many pixels containing only blurred information. This occurs because excessively increasing the gradients in discriminative areas can lead to large updates between iterations, resulting in the divergence of the pixel distribution. Therefore, the enhancement of discrimination areas is not the stronger the better. It is important to maintain the enhancement factor within a reasonable range.

<span id="page-12-7"></span>Image /page/12/Figure/19 description: This image displays a grid of generated images, organized into three rows labeled with beta values: \u03b2 = 1, \u03b2 = 5, and \u03b2 = 10. Each row contains five distinct images. The images appear to be generated by a machine learning model, possibly a generative adversarial network (GAN), and show variations of subjects like cats and birds. The quality and clarity of the generated images seem to change with the beta values, with lower beta values potentially producing more recognizable or detailed images, while higher beta values might result in more abstract or noisy outputs.

Figure 6. Distorted image distributions due to excessively large enhancement factors  $(= 10)$ 

<span id="page-12-5"></span>[https://github.com/VILA- Lab/SRe2L/tree/main/](https://github.com/VILA-Lab/SRe2L/tree/main/SRe2L) [SRe2L](https://github.com/VILA-Lab/SRe2L/tree/main/SRe2L)

<span id="page-13-3"></span>

| Modules            |              | <b>CPD</b> |                | <b>DAE</b> |       |           | <b>TM</b>   |           |
|--------------------|--------------|------------|----------------|------------|-------|-----------|-------------|-----------|
| Hyper-parameters   |              | $\alpha$   | β              | K          | T     | batch_syn | $lr$ -pixel | syn_steps |
|                    |              | $\Omega$   |                | 50         |       | 1000      | 10000       | 40        |
| ImageNette         | 10           | 0.25       |                | 100        | 10000 | 250       | 1000        | 40        |
|                    | 50           | 0.375      | 1              | 200        |       | 100       | 100         | 80        |
|                    | 1            | $\Omega$   | 1              | 50         |       | 1000      | 10000       | 40        |
| ImageWoof          | 10           | 0.25       |                | 100        | 10000 | 250       | 1000        | 40        |
|                    | 50           | 0.375      | $\mathfrak{2}$ | 200        |       | 100       | 100         | 80        |
|                    | 1            | $\Omega$   |                | 50         |       | 1000      | 10000       | 40        |
| ImageMeow          | 10           | 0.25       |                | 100        | 10000 | 250       | 1000        | 40        |
|                    | 50           | 0.375      | $\overline{c}$ | 200        |       | 200       | 100         | 40        |
|                    |              | $\Omega$   |                | 50         |       | 1000      | 10000       | 40        |
| <b>ImageYellow</b> | 10           | 0.25       |                | 100        | 10000 | 250       | 1000        | 40        |
|                    | 50           | 0.375      |                | 200        |       | 200       | 100         | 40        |
|                    |              | $\Omega$   |                | 50         |       | 1000      | 10000       | 40        |
| ImageFruit         | 10           | 0.25       |                | 100        | 10000 | 250       | 1000        | 40        |
|                    | 50           | 0.375      |                | 200        |       | 200       | 100         | 40        |
|                    | $\mathbf{1}$ | $\Omega$   |                | 50         |       | 1000      | 10000       | 40        |
| ImageSquawk        | 10           | 0.25       |                | 100        | 10000 | 250       | 1000        | 40        |
|                    | 50           | 0.375      | $\overline{c}$ | 200        |       | 100       | 100         | 80        |

Table 9. Hyper-parameters of experiments on ImageNet nette, woof, meow, fruit, yellow, squawk subsets.

| Method      | ConvNetD5 | ResNet18                | VGG11 | AlexNet | Method      | ConvNetD5 | ResNet18               | VGG11 | AlexNet |
|-------------|-----------|-------------------------|-------|---------|-------------|-----------|------------------------|-------|---------|
| Random      | 41.8      | 40.9                    | 43.2  | 35.7    | Random      | 29.6      | 31.4                   | 30.8  | 25.7    |
| <b>FTD</b>  | 62.8      | 49.8                    | 50.5  | 47.6    | <b>FTD</b>  | 58.4      | 55.6                   | 57.6  | 52.3    |
| <b>DATM</b> | 65.1      | 52.4                    | 51.2  | 49.6    | <b>DATM</b> | 61.8      | 62.8                   | 65.6  | 63.5    |
| <b>EDF</b>  | 68.2      | 50.8                    | 53.2  | 48.2    | <b>EDF</b>  | 65.4      | 63.6                   | 64.8  | 69.2    |
|             |           | (a) Image Yellow, IPC10 |       |         |             |           | (b) ImageSquawk, IPC50 |       |         |

Table 10. Cross-architecture evaluation on ResNet18, VGG11, and AlexNet. ConvNetD5 is the distillation architecture. Distilled datasets under IPC10 and IPC50 outperform random selection, FTD, and DATM, showing good generalizability.

<span id="page-13-2"></span>

## 11.4. Changing Trends of Discriminative Areas

Figure [5b](#page-6-2) demonstrates that EDF effectively expands the discriminative areas (high-activation regions) on several image samples at a fixed distillation iteration. In Figure [7a,](#page-14-1) we show the changing trend of discriminative areas of 5 different classes across 10000 iterations (sampled every 500 iterations). Despite the fluctuation, these areas expand as the distillation proceeds. This further confirms that one key factor in the success of EDF is that it successfully increases discriminative features in synthetic images and turns them into more informative data samples.

<span id="page-13-0"></span>

## 11.5. Impact of different Grad-CAM Models

In our experiments, we use ResNe18 as the Grad-CAM model to extract activation maps. However, the choice of Grad-CAM model does not have a significant impact on the performance, as long as it has been trained on the target dataset. As shown in Table [12,](#page-14-2) differences between performances of different Grad-CAM models are within 0.5, demonstrating that our discriminative area enhancement module doesn't depend on the choice of Grad-CAM model.

## 11.6. Latency of Grad-CAM

We use torchcam [\[11\]](#page-8-20) implementation of various Grad-CAM methods to extract activation maps. In Table [13,](#page-14-3) we show the latencies of extracting activation maps of various IPCs. Notably, these latencies are all below one second, demonstrating a high inference speed. In our experiments, the maximum number of extractions for one distillation is 200 (on IPC1). Thus, the total time used for activation map extraction is at most two minutes, which is neglectable compared with the latency of the full distillation (several hours). In conclusion, our use of Grad-CAM activation maps to provide guidance doesn't reduce the efficiency of the backbone.

## 12. Comp-DD Benchmark

<span id="page-13-1"></span>

## 12.1. Subset Details

The corresponding class labels for each subset are listed as follows:

• Bird-Hard: n01537544, n01592084, n01824575, n01558993, n01534433, n01843065, n01530575, n01560419, n01601694, n01532829

<span id="page-14-0"></span>

| Dataset     | ImageNette     |                |                |                | ImageWoof      |                | ImageSquawk    |                |                |  |
|-------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|--|
| <b>IPC</b>  |                | 10             | 50             |                | 10             | 50             |                | 10             | 50             |  |
| Random      | $12.6 \pm 1.5$ | $44.8 \pm 1.3$ | $60.4 \pm 1.4$ | $11.4 \pm 1.3$ | $20.2 \pm 1.2$ | $28.2 \pm 0.9$ | $13.2 \pm 1.1$ | $29.6 \pm 1.5$ | $52.8 \pm 0.4$ |  |
| DМ          | $28.2 \pm 1.5$ | $58.1 \pm 1.1$ | $65.8 \pm 1.1$ | $19.6 \pm 1.4$ | $30.4 \pm 1.3$ | $36.3 \pm 1.4$ | $19.7 \pm 1.3$ | $30.0 \pm 1.0$ | $61.5 \pm 1.2$ |  |
| <b>MTT</b>  | $47.7 \pm 0.9$ | $63.0 \pm 1.3$ | $69.2 \pm 1.0$ | $28.6 \pm 0.8$ | $35.8 \pm 1.8$ | $42.3 \pm 0.9$ | $39.4 \pm 1.5$ | $52.3 \pm 1.0$ | $65.4 \pm 1.2$ |  |
| SRe2L       | $18.4 \pm 0.8$ | $41.0 \pm 0.3$ | $55.6 \pm 0.2$ | $16.0 \pm 0.2$ | $32.2 \pm 0.3$ | $35.8 \pm 0.2$ | $22.5 \pm 0.5$ | $35.6 \pm 0.4$ | $42.2 \pm 0.3$ |  |
| <b>RDED</b> | $28.0 \pm 0.5$ | $53.6 \pm 0.8$ | $72.8 \pm 0.3$ | $19.0 \pm 0.3$ | $32.6 \pm 0.5$ | $52.6 \pm 0.6$ | $33.8 \pm 0.5$ | $52.2 \pm 0.5$ | $71.6 \pm 0.8$ |  |
| <b>EDF</b>  | $52.6 \pm 0.5$ | $71.0 \pm 0.8$ | $77.8 \pm 0.5$ | $30.8 \pm 1.0$ | $41.8 \pm 0.2$ | $48.4 \pm 0.5$ | $41.8 \pm 0.5$ | $65.4 \pm 0.8$ | $74.8 \pm 1.2$ |  |

Table 11. Performances of SRe2L and RDED without using knowledge distillation during evaluation. EDF outperforms the other two methods in most of settings, and our advantage is more pronounced as IPC gets smaller.

<span id="page-14-2"></span>

| IPC | Grad-CAM Model |             |          |       |
|-----|----------------|-------------|----------|-------|
|     | ConvNetD5      | ResNet18    | ResNet50 | VGG11 |
| 1   | 52.3           | <b>52.6</b> | 52.5     | 52.5  |
| 10  | <b>71.2</b>    | 71.0        | 70.8     | 70.7  |
| 50  | 77.4           | <b>77.8</b> | 77.6     | 77.6  |

Table 12. Results of using different Grad-CAM models on ImageNette. The choice of model only has minor influence on the performance.

<span id="page-14-3"></span>

| IPC         | 1    | 10   | 50   | 200  | 300  |
|-------------|------|------|------|------|------|
| Latency (s) | 0.63 | 0.52 | 0.74 | 0.68 | 0.94 |

 $\overline{\phantom{a}}$ 

Table 13. Latency of extracting Grad-CAM activation maps using ResNet18. For each IPC in our experiments, the latency is less than one second.

- Bird-Easy: n02007558, n02027492, n01798484, n02033041, n02012849, n02025239, n01818515, n01820546, n02051845, n01608432
- Dog-Hard: n02107683, n02107574, n02109525, n02096585, n02085620, n02113712, n02086910, n02093647, n02086079, n02102040
- Dog-Easy: n02096294, n02093428, n02105412, n02089973, n02109047, n02109961, n02105056, n02092002, n02114367, n02110627
- Car-Hard: n04252077, n03776460, n04335435, n03670208, n03594945, n03445924, n03444034, n04467665, n03977966, n02704792
- Car-Easy: n03459775, n03208938, n03930630, n04285008, n03100240, n02814533, n03770679, n04065272, n03777568, n04037443
- Snake-Hard: n01693334, n01687978, n01685808, n01682714, n01688243, n01737021, n01751748, n01739381, n01728920, n01728572
- Snake-Easy: n01749939, n01735189, n01729977, n01734418, n01742172, n01744401, n01756291, n01755581, n01729322, n01740131
- Insect-Hard:  $n02165456$ ,  $n02281787$ ,  $n02280649$ , n02172182, n02281406, n02165105, n02264363, n02268853, n01770081, n02277742

<span id="page-14-1"></span>Image /page/14/Figure/14 description: A line graph displays the discriminative area (%) on the y-axis against iteration on the x-axis. Five classes are represented by different colored lines and markers: Class 1 (blue circles), Class 2 (orange triangles), Class 3 (green stars), Class 4 (red squares), and Class 5 (purple diamonds). The y-axis ranges from 47% to 51%, and the x-axis ranges from 0 to 8000 iterations. The graph shows fluctuations in the discriminative area for each class over the iterations, with Class 5 generally maintaining the highest discriminative area.

(a) In general, discriminative areas show a trend of increase as the distillation proceeds.

Image /page/14/Figure/16 description: This is a histogram showing the distribution of activation values. The x-axis is labeled "Activation" and ranges from 0.0 to 1.0. The y-axis is labeled "# Pixels" and ranges from 0 to 600. The histogram displays a series of blue bars representing the number of pixels at different activation levels. There are several prominent peaks, with a large peak around 0.6, and smaller peaks at lower activation values, including a very tall, narrow peak near 0.0.

(b) Most of the pixels have activation around 0.25 to 0.75.

Figure 7. (a) The trend of discriminative area change across various distillation iterations. (b) Distribution of the activation map of a random image from ImageNet-1K.

- Insect-Easy:  $n02279972$ ,  $n02233338$ ,  $n02219486$ , n02206856, n02174001, n02190166, n02167151, n02231487, n02168699, n02236044
- Fish-Hard: n01440764, n02536864, n02514041, n02641379, n01494475, n02643566, n01484850, n02640242, n01698640, n01873310
- Fish-Easy: n01496331, n01443537, n01498041,

n02655020, n02526121, n01491361, n02606052, n02607072, n02071294, n02066245

- **Round-Hard:** n04409515, n04254680, n03982430, n04548280, n02799071, n03445777, n03942813, n03134739, n04039381, n09229709
- Round-Easy: n02782093, n03379051, n07753275, n04328186, n02794156, n09835506, n02802426, n04540053, n04019541, n04118538
- Music-Hard: n02787622, n03495258, n02787622, n03452741, n02676566, n04141076, n02992211, n02672831, n03272010, n03372029
- Music-Easy: n03250847, n03854065, n03017168, n03394916, n03721384, n03110669, n04487394, n03838899, n04536866, n04515003

<span id="page-15-0"></span>

### 12.2. Complexity Metrics

We use the percentage of pixels whose Grad-CAM activation values exceed a predefined fixed threshold to evaluate the complexity of an image. In our settings, the fixed threshold is 0.5. The reasons for fixing the threshold at 0.5 are twofold. Firstly, when selecting subsets, images are static and won't be updated in any form (this is different from EDF's DAE module, which updates synthetic images). Thus, using a fixed threshold is sufficient for determining the highactivation areas.

Secondly, values of a Grad-CAM activation map range from 0 to 1, with higher values corresponding to higher activation. We present the distribution of the activation map of a random image from ImageNet-1K in Figure [13b,](#page-18-0) where the majority of pixels have activation values between 0.25 and 0.75. Subsequently, if the threshold is too small or too large, the complexity scores of all classes will be close (standard deviation is small), as shown in Figure [12](#page-17-0) and [13.](#page-18-0) This results in no clear distinguishment between easy and hard subsets. Finally, we set 0.5 as the threshold, which is the middle point of the range. Complexity distribution under this threshold is shown in Figure [10.](#page-15-4)

Our complexity metrics are an early effort to define how complex an image is in the context of dataset distillation. We acknowledge potential biases or disadvantages and encourage future studies to continue the refinement of complex metrics.

## <span id="page-15-1"></span>12.3. Benchmark Hyper-parameters

For the trajectory training, experiment settings are the same as those used for ImageNet-1K and its subsets. For distillation, we provide hyper-parameters of EDF on the Complex DD Benchmark in Table [14.](#page-16-0) These hyper-parameters can serve as a reference for future works to extend to other subsets of the benchmark.

Image /page/15/Figure/11 description: A bar chart displays the complexity of different classes, with the y-axis labeled "Complexity (%)" ranging from 0 to 100. The x-axis is labeled "Classes". The bars represent the complexity values for each class, with most bars falling between 10% and 30%. The chart indicates a standard deviation of 4.3%.

Figure 8. Complexity distribution of all classes from ImageNet-1K under threshold being 0.1. An excessively small threshold will cause the complexity of all classes to become low and difficult to distinguish.

Image /page/15/Figure/13 description: A bar chart displays the complexity percentage for classes. The y-axis is labeled 'Complexity (%)' and ranges from 0 to 100. The x-axis is labeled 'Classes'. A single blue bar spans across the chart, reaching close to the 100% mark on the y-axis. The text 'std: 0.9%' is displayed above the bar, indicating the standard deviation.

Figure 9. Complexity distribution of all classes from ImageNet-1K under threshold being 0.9. An excessively large threshold will cause the complexity of all classes to become high and difficult to distinguish.

<span id="page-15-4"></span>Image /page/15/Figure/15 description: This is a bar chart showing the complexity of different classes. The x-axis is labeled "Classes" and the y-axis is labeled "Complexity (%)". The bars range from approximately 60% to 70% in complexity. The standard deviation is indicated as 6.3%.

Figure 10. Complexity distribution of all classes from ImageNet-1K under threshold being 0.5. A moderate threshold makes the complexity differences between classes more distinct.

<span id="page-15-2"></span>

## 13. Visualization of Distilled Images on ImageNet-1K

In Figure [11](#page-17-1) to [13,](#page-18-0) we present a visualization of distilled images of all ImageNet-1K subsets in Table [1.](#page-5-0)

<span id="page-15-3"></span>

## 14. More Related Work

In Table [15,](#page-18-1) we present a comprehensive summary of previous dataset distillation methods, categorized by different approaches. There are four main categories of dataset distillation: gradient matching, trajectory matching, distribution matching, and generative model-based methods. Recently, some works [\[48,](#page-9-4) [64,](#page-9-3) [65\]](#page-9-12) add knowledge distil-

<span id="page-16-0"></span>

| Modules          |              | <b>CPD</b> |                  | <b>DAE</b> |       |           | TM          |           |
|------------------|--------------|------------|------------------|------------|-------|-----------|-------------|-----------|
| Hyper-parameters |              | $\alpha$   | $\beta$          | К          | T     | batch_syn | $lr$ -pixel | syn_steps |
|                  |              | $\Omega$   | 1                | 50         |       | 1000      | 10000       |           |
| CDD-Bird-Easy    | 10           | 0.25       | $\mathbf{1}$     | 100        | 10000 | 400       | 1000        | 40        |
|                  | 50           | 0.375      | $\overline{2}$   | 200        |       | 200       | 100         |           |
|                  | 1            | $\Omega$   | $\mathbf{1}$     | 50         |       | 1000      | 10000       |           |
| CDD-Bird-Hard    | 10           | 0.25       | $\mathbf{1}$     | 100        | 10000 | 400       | 1000        | 40        |
|                  | 50           | 0.375      | $\overline{2}$   | 200        |       | 200       | 100         |           |
|                  | 1            | $\Omega$   | 1                | 50         |       | 1000      | 10000       |           |
| CDD-Dog-Easy     | 10           | 0.25       | 1                | 100        | 10000 | 400       | 1000        | 40        |
|                  | 50           | 0.375      | $\overline{2}$   | 200        |       | 200       | 100         |           |
|                  | $\mathbf{1}$ | $\Omega$   | $\mathbf{1}$     | 50         |       | 1000      | 10000       |           |
| CDD-Dog-Hard     | 10           | 0.25       | $\mathbf{1}$     | 100        | 10000 | 400       | 1000        | 40        |
|                  | 50           | 0.375      | $\boldsymbol{2}$ | 200        |       | 200       | 100         |           |
|                  | 1            | $\Omega$   | $\mathbf{1}$     | 50         |       | 1000      | 10000       |           |
| CDD-Car-Easy     | 10           | 0.25       | $\mathbf{1}$     | 100        | 10000 | 400       | 1000        | 40        |
|                  | 50           | 0.375      | $\overline{2}$   | 200        |       | 200       | 100         |           |
|                  |              | $\Omega$   | 1                | 50         |       | 1000      | 10000       |           |
| CDD-Car-Hard     | 10           | 0.25       | 1                | 100        | 10000 | 400       | 1000        | 40        |
|                  | 50           | 0.375      | $\overline{2}$   | 200        |       | 200       | 100         |           |

Table 14. Hyper-parameters of EDF on the Complex DD Benchmark.

lation during the evaluation stage of dataset distillation. [\[4,](#page-8-21) [5,](#page-8-22) [7,](#page-8-6) [10,](#page-8-23) [17,](#page-8-24) [29,](#page-8-25) [33](#page-9-13)[–36,](#page-9-14) [40,](#page-9-10) [50,](#page-9-15) [55](#page-9-16)[–58,](#page-9-17) [60,](#page-9-18) [62,](#page-9-19) [63,](#page-9-20) [66\]](#page-10-6).

<span id="page-17-1"></span>Image /page/17/Picture/0 description: This is a grid of 8x8 images, each image is a small square. The images are abstract and appear to be generated by an AI. The images are arranged in rows and columns. The first row shows images that are mostly green and brown, with some blurry shapes that could be animals or plants. The second row shows images that are mostly brown and white, with some blurry shapes that could be dogs. The third row shows images that are mostly gray and black, with some blurry shapes that could be cars or metal objects. The fourth row shows images that are mostly red and orange, with some blurry shapes that could be vehicles or machinery. The fifth row shows images that are mostly gray and brown, with some blurry shapes that could be buildings or churches. The sixth row shows images that are mostly gray and black, with some blurry shapes that could be abstract patterns or spirals. The seventh row shows images that are mostly green and blue, with some blurry shapes that could be trucks or vehicles. The eighth row shows images that are mostly white and blue, with some blurry shapes that could be clouds or abstract patterns. The overall impression is a collection of abstract, dreamlike images.

Image /page/17/Picture/1 description: The image contains the text "(a) ImageNette".

Image /page/17/Picture/2 description: The image displays a grid of 100 images, arranged in 10 rows and 10 columns. Each individual image within the grid appears to be a generated image of a dog, with varying breeds, poses, and backgrounds. The overall quality of the generated images varies, with some appearing more realistic than others, and some exhibiting artifacts or distortions typical of AI-generated content. The grid is divided into two sections, labeled (a) ImageNet-1k and (b) ImageWoof, suggesting a comparison between image generation models or datasets. The images in section (a) are generally more varied in terms of dog breeds and settings, while section (b) seems to focus more on specific breeds or types of dogs, with a higher degree of visual coherence in some instances.

(b) ImageWoof

Figure 11

<span id="page-17-0"></span>Image /page/17/Picture/5 description: A grid of 100 images, each 10x10, displays various animals, primarily cats and big cats like lions and tigers. The images are somewhat abstract and stylized, with a focus on the animals' faces and fur textures. The top rows predominantly feature domestic cats in different poses and settings, some appearing to be sleeping or playing. Moving down the grid, the images transition to larger felines such as lions, tigers, and leopards, often depicted in profile or with their heads facing forward. The overall impression is a collection of generated animal portraits, likely from a machine learning model, with a consistent aesthetic of soft focus and blended colors.

(a) ImageMeow

Image /page/17/Picture/7 description: This is a grid of 100 images, each depicting a different fruit or a collection of fruits. The fruits shown include pineapples, bananas, strawberries, oranges, lemons, pomegranates, figs, bell peppers, and apples. The images are arranged in a 10x10 grid. Some images are clear and detailed, while others are more abstract or blurry, suggesting they may be generated images or artistic representations.

(b) ImageFruit

Figure 12

<span id="page-18-0"></span>Image /page/18/Picture/0 description: This is a grid of 80 images, arranged in 8 rows and 10 columns. The images appear to be generated by an AI model, as they are somewhat abstract and stylized. The top rows feature images of flowers, bees, and fruits, including sunflowers, purple flowers, bees on flowers, lemons, bananas, and various berries. The middle rows show more fruits, such as apples, oranges, and grapes, as well as images of corn on the cob and pumpkins. The lower rows display images of yellow school buses in various settings, honeycomb structures, lions, spiders, and small yellow birds.

(a) ImageYellow

Image /page/18/Picture/2 description: The image displays a grid of 80 smaller images, arranged in 8 rows and 10 columns. The overall title of the figure is "(a) Image renow". The smaller images predominantly feature various types of birds, including flamingos, parrots, penguins, eagles, toucans, ostriches, swans, and cockatoos. Many of the images appear to be artistic renditions or stylized representations of these birds, with some exhibiting abstract or painterly qualities. The second part of the figure is titled "(b) ImageSquawk" and also shows a grid of 80 smaller images, again arranged in 8 rows and 10 columns, featuring similar bird subjects with comparable artistic styles.

(b) ImageSquawk

<span id="page-18-1"></span>

| Category                                | Method                 |
|-----------------------------------------|------------------------|
| Kernel-based                            | KIP-FC [38]            |
|                                         | KIP-ConvNet [39]       |
|                                         | FRePo [78]             |
|                                         | RFAD [31]              |
|                                         | RCIG [32]              |
| Gradient-matching                       | DC [71]                |
|                                         | DSA [69]               |
|                                         | DCC [22]               |
|                                         | LCMat [44]             |
| Trajectory-matching                     | MTT [1]                |
|                                         | Tesla [6]              |
|                                         | FTD [8]                |
|                                         | SeqMatch [9]           |
|                                         | DATM [13]              |
|                                         | ATT [27]               |
|                                         | NSD [61]               |
|                                         | PAD [25]               |
|                                         | SelMatch [24]          |
| Distribution-matching                   | DM [68]                |
|                                         | CAFE [51]              |
|                                         | IDM [72]               |
|                                         | DREAM [30]             |
|                                         | M3D [67]               |
|                                         | NCFD [53]              |
| Generative model                        | DiM [52]               |
|                                         | GLaD [2]               |
|                                         | H-GLaD [77]            |
|                                         | LD3M [37]              |
|                                         | IT-GAN [70]            |
|                                         | D4M [46]               |
|                                         | Minimax Diffusion [12] |
| + Knowledge distillation for evaluation | SRe2L [64]             |
|                                         | RDED [48]              |
|                                         | HeLIO [65]             |
| Others                                  | MIM4DD [43]            |
|                                         | DQAS [75]              |
|                                         | LDD [76]               |

Table 15. Summary of previous works on dataset distillation

Figure 13
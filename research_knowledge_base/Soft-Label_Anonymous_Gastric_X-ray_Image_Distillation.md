# SOFT-LABEL ANONYMOUS GASTRIC X-RAY IMAGE DISTILLATION

*<PERSON>uang Li* † *Ren Togo* †† *<PERSON><PERSON><PERSON>* ††† *<PERSON><PERSON>*†††

† Graduate School of Information Science and Technology, Hokkaido University, Japan †† Education and Research Center for Mathematical and Data Science, Hokkaido University, Japan ††† Faculty of Information Science and Technology, Hokkaido University, Japan E-mail: {guang, togo, ogawa}@lmd.ist.hokudai.ac.jp, <EMAIL>

## ABSTRACT

This paper presents a soft-label anonymous gastric X-ray image distillation method based on a gradient descent approach. The sharing of medical data is demanded to construct high-accuracy computeraided diagnosis (CAD) systems. However, the large size of the medical dataset and privacy protection are remaining problems in medical data sharing, which hindered the research of CAD systems. The idea of our distillation method is to extract the valid information of the medical dataset and generate a tiny distilled dataset that has a different data distribution. Different from model distillation, our method aims to find the optimal distilled images, distilled labels and the optimized learning rate. Experimental results show that the proposed method can not only effectively compress the medical dataset but also anonymize medical images to protect the patient's private information. The proposed approach can improve the efficiency and security of medical data sharing.

*Index Terms*— Medical image distillation, medical data sharing, anonymization, gastric X-ray images.

### 1. INTRODUCTION

Deep learning, in particular, deep convolutional neural networks (DCNNs), have been popular with many areas in computer vision [\[1\]](#page-4-0). Especially, in the field of medical image analysis, DCNNbased CAD systems are often used as an auxiliary diagnosis of diseases [\[2,](#page-4-1) [3\]](#page-4-2). The sharing of medical data is a primary method for building high-accuracy CAD systems [\[4\]](#page-4-3). However, there are still two main problems in medical data sharing. Firstly, with the increase in the number of medical equipment, the amount of medical image data grows exponentially, resulting in large size of the medical dataset [\[5\]](#page-4-4). Therefore, the sharing of medical image data is inefficient. It is necessary to extract valid data to reduce the size of the medical dataset. Secondly, because the medical images contain the patient's private information, there is still an enormous controversy over the use of these medical data [\[6\]](#page-4-5). It is difficult to share the medical image data of many diseases, which hindered the research and development of CAD systems.

To solve the problem of reducing the size of the dataset, many researchers have proposed unique solutions. For example, many instance selection and dataset pruning methods aim to select a subset of the entire training dataset and achieve performance comparable to the original full dataset, which can reduce the size of the dataset [\[7,](#page-4-6) [8\]](#page-4-7). Similarly, Campbell et al. proposed a Bayesian coreset construction method that can find valid data in the original dataset via the greedy iterative geodesic ascent [\[9\]](#page-4-8). Also, Sener et al. proposed a core-set construction approach based on active learning [\[10\]](#page-4-9). Active learning reduces the amount of data that need to be labeled by only labeling data that are difficult to classify [\[11,](#page-4-10) [12\]](#page-4-11). Although these previous approaches have made some progresses in solving the problem of reducing the size of standard image dataset, due to the high complexity of medical images and the need for professional knowledge of labeling, they still cannot meet the requirements for medical dataset.

Privacy protection problem has always been the main obstacle in medical data sharing [\[13,](#page-4-12) [14\]](#page-4-13). Researchers have also tackled the problem of hiding personal information. For example, some methods for removal of identifiers have been proposed to protect the privacy of patients [\[15\]](#page-4-14). These methods contain stripping identifier and one-way hashing algorithm. The stripping identifier approach is an anonymization algorithm which can remove all patient identifiers in a record. And the one-way hashing algorithm transforms a patient record string into another string, such that operations on the hash value cannot calculate the original patient record. In recent years, with the rise of big data, some ways to use cloud computing platforms to share medical data securely have been proposed. For example, Yang et al. proposed a hybrid solution method of privacypreserving medical data sharing based on cloud computing [\[16\]](#page-4-15). Also, Fabian et al. proposed a method to realize the safe sharing of medical data with semi-trust cloud computing environments [\[17\]](#page-4-16). Since it is challenging to ensure the validity of anonymized medical images, all of these methods do not pay attention to the anonymization of medical image data itself.

In this paper, we propose a novel method that can not only effectively compress the medical dataset but also anonymize medical images to protect the patient's private information [\[18,](#page-4-17) [19\]](#page-4-18). Since the researches of gastric X-ray images have both of the above problems, and we have previously proposed methods for anonymous gastritis image generation and automatic detection of gastritis [\[20,](#page-4-19) [21\]](#page-4-20), we also focus on gastric X-ray images in this research. Considering the gastric X-ray images with high resolutions can lead to expensive computing cost, we divide them into patches that have three categories. To maximize the compression of the dataset and distillation of the valid information, we distill each class into one image for training. Then we use the distilled images to estimate the labels of full gastric X-ray images. Experimental results show that our distillation method achieved competitive classification accuracy using a tiny distilled dataset. Furthermore, the sharing of medical image

In our work, the medical data were provided by The University of Tokyo Hospital in Japan. We express our thanks to Nobutake Yamamichi of the Graduate School of Medicine, The University of Tokyo, and Katsuhiro Mabe of the Junpukai Health Maintenance Center. This study was supported in part by the Photo-Excitonix Project of Hokkaido University, and in part by the JSPS KAKENHI under Grant JP17H01744 and Grant JP20K19857.

data can become more efficient and safer because the distilled images do not contain private information.

Our contributions are summarized as follows:

- We propose a novel method for anonymous medical image distillation, which can improve the efficiency and security of medical image data sharing.
- We realize high classification performance by distilling each class into one image for training.

## 2. ANONYMOUS GASTRIC X-RAY IMAGE DISTILLATION

This section shows the details of the soft-label anonymous gastric X-ray image distillation method. In subsection 2.1, we preprocess the training dataset by dividing the gastric images into patches. In subsection 2.2, we demonstrate the entire flow of the anonymous gastric image distillation algorithm. In subsection 2.3, we explain how to estimate the labels of full gastric X-ray images.

### 2.1. Patch-based Gastric X-ray Image Labeling

This subsection shows how we preprocess the training dataset. The gastric X-ray images in our dataset have high resolutions, e.g., 2,048  $\times$  2,048 pixels. In practical applications, the high-resolution images can lead to expensive computing cost. Therefore, we divide each gastric X-ray image into  $H \times W$  patches (H and W respectively denote the number of patches in the vertical direction and the horizontal direction) and manually label these patch-based gastric X-ray images into the following three categories:

- $I:$  patches outside of the stomach (irrelevant),
- $\mathcal{N}$ : patches extracted from non-gastritis X-ray images (negative) inside of the stomach,
- $P$ : patches extracted from gastritis X-ray images (positive) inside of the stomach.

Figures [1](#page-1-0) and [2](#page-1-1) show the full gastric X-ray images and patch-based gastric X-ray images, respectively.

#### 2.2. Anonymous Gastric Image Distillation

In this subsection, we explain our anonymous gastric image distillation algorithm. When we have the patch-based gastric training dataset  $(\mathbf{x}, \mathbf{y}) = \{x_g, y_g\}_{g=1}^G$ , where G denotes the number of training images,  $x_g$  and  $y_g$  denote the image and the corresponding label, respectively. We parameterize a DCNN model as  $\theta$ , and let the twice-differentiable loss function  $\ell(x, y, \theta)$  denotes the loss of this network on the entire training dataset  $(x, y)$ . Considering the gastritis patches and non-gastritis patches may have common features, we let the distilled images  $\tilde{\mathbf{x}}$  have soft labels  $\tilde{\mathbf{y}}$ , which contain  $\mathcal{I}, \mathcal{N}$  and  $P$  [\[22\]](#page-4-21). In our gastric image distillation method, we distill the valid information of the entire training dataset to a tiny distilled dataset  $(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}) = {\tilde{x}_m, \tilde{y}_m}_{m=1}^M$  with  $M \ll G$ . For each distilling step *i*, the weights are updated as follows:

<span id="page-1-2"></span>
$$
\theta_{i+1} \leftarrow \theta_i - \tilde{\alpha} \nabla_{\theta_i} \ell \left( \tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta_i \right), \tag{1}
$$

where  $(\tilde{\mathbf{x}}, \tilde{\mathbf{y}})$  denotes the distilled dataset, and  $\tilde{\alpha}$  denotes the optimized learning rate.

With the derived new weights  $\theta_{i+1}$  of the DCNN model, we evaluate it on the entire training dataset  $(x, y)$ . Our goal is to find

Image /page/1/Picture/17 description: Two radiographic images labeled (a) and (b) show a contrast study of the upper gastrointestinal tract. Image (a) displays the stomach and duodenum filled with contrast material, with the stomach appearing distended and the duodenum showing normal segmentation. Image (b) also shows the stomach and duodenum with contrast, but the mucosal pattern of the stomach is more clearly delineated, revealing a textured appearance, and there are some small filling defects or irregularities noted within the gastric lumen.

Fig. 1. Examples of full gastric X-ray images: (a) a sample of nongastritis image, (b) a sample of gastritis image.

<span id="page-1-0"></span>Image /page/1/Figure/19 description: The image displays three rows of medical images, labeled (a), (b), and (c). Row (a) contains five images that appear to be X-rays of a medical procedure, possibly involving the esophagus or a similar anatomical structure. Row (b) contains five images that show detailed textures, resembling mammograms or other soft tissue imaging, with a dark background and lighter, fibrous patterns. Row (c) also contains five images with similar textures to row (b), displaying intricate patterns of light and dark areas, suggestive of biological tissue.

<span id="page-1-1"></span>Fig. 2. Examples of patch-based gastric X-ray images: (a) irrelevant patches in  $\mathcal{I}$ , (b) non-gastritis patches in  $\mathcal{N}$ , (c) gastritis patches in  $\mathcal{P}$ .

the optimal distilled images  $\tilde{\mathbf{x}}^*$ , distilled labels  $\tilde{\mathbf{y}}^*$  and the optimized learning rate  $\tilde{\alpha}^*$ . The objective function is defined as:

<span id="page-1-3"></span>
$$
\tilde{\mathbf{x}}^*, \tilde{\mathbf{y}}^*, \tilde{\alpha}^* = \arg \min \mathcal{L} (\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \tilde{\alpha}; \theta_i),
$$
  
= 
$$
\arg \min \ell (\mathbf{x}, \mathbf{y}, \theta_{i+1}),
$$
  
= 
$$
\arg \min \ell (\mathbf{x}, \mathbf{y}, \theta_i - \tilde{\alpha} \nabla_{\theta_i} \ell (\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta_i)),
$$
 (2)

where  $\ell$   $(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta_i)$  is twice-differentiable, and  $\mathcal{L}$   $(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \tilde{\alpha}; \theta_i)$  is differentiable.

To obtain the optimal distilled images, distilled labels and the optimized learning rate, we update the distilled images  $\tilde{\mathbf{x}}$ , distilled labels  $\tilde{v}$  and the optimized learning rate  $\tilde{\alpha}$  at each distilling step with gradient descent as follows:

$$
\tilde{\mathbf{x}} \leftarrow \tilde{\mathbf{x}} - \alpha \nabla_{\tilde{\mathbf{x}}} \mathcal{L}, \n\tilde{\mathbf{y}} \leftarrow \tilde{\mathbf{y}} - \alpha \nabla_{\tilde{\mathbf{y}}} \mathcal{L}, \n\tilde{\alpha} \leftarrow \tilde{\alpha} - \alpha \nabla_{\tilde{\alpha}} \mathcal{L},
$$
\n(3)

<span id="page-1-4"></span>where  $\nabla_{\tilde{\mathbf{x}}}\mathcal{L}, \nabla_{\tilde{\mathbf{y}}}\mathcal{L}$  and  $\nabla_{\tilde{\alpha}}\mathcal{L}$  respectively denote the gradient of  $\mathcal{L}$ based on  $\tilde{\mathbf{x}}, \tilde{\mathbf{y}}$  and  $\tilde{\alpha}$ ,  $\alpha$  denotes the learning rate.

<span id="page-2-0"></span>

##### Algorithm 1 Anonymous Gastric Image Distillation

**Input:**  $\theta_0$ : the random initial weights; M: the number of distilled images; E: distill epochs; I: distill steps;

α: learning rate; K: batch size; T: training steps;  $\tilde{y}_0$ : initial value for  $\tilde{y}$ ;  $\tilde{\alpha}_0$ : initial value for  $\tilde{\alpha}$ 

Output: x̃: distilled images; ȳ: distilled labels;  $\tilde{\alpha}$ : optimized learning rate

1: Initialize  $\tilde{\mathbf{x}} = {\{\tilde{x}_m\}}_{m=1}^M$  randomly,  $\tilde{\mathbf{y}} = {\{\tilde{y}_m\}}_{m=1}^M \leftarrow \tilde{\mathbf{y}}_0, \tilde{\alpha} \leftarrow \tilde{\alpha}_0$ 

2: for each training step  $t = 1$  to T do

3: Get a minibatch of training data  $(\mathbf{x}_t, \mathbf{y}_t) = \{x_{t,k}, y_{t,k}\}_{k=1}^K$ 

- 4: Get the random initial weights  $\theta_0$
- 5: **for** each distilling epoch  $e = 1$  to E do
- 6: for each distilling step  $i = 0$  to  $I 1$  do
- 7: Compute updated weights with gradient descent:  $\theta_{i+1} \leftarrow \theta_i \tilde{\alpha} \nabla_{\theta_i} \ell(\tilde{\mathbf{x}}, \tilde{\mathbf{y}}, \theta_i)$
- 8: Evaluate the objective function on the minibatch of training data:  $\mathcal{L} = \ell(\mathbf{x}_t, \mathbf{y}_t, \theta_{i+1})$
- 9: Update distilled data:  $\tilde{\mathbf{x}} \leftarrow \tilde{\mathbf{x}} \alpha \nabla_{\tilde{\mathbf{x}}} \mathcal{L}, \tilde{\mathbf{y}} \leftarrow \tilde{\mathbf{y}} \alpha \nabla_{\tilde{\mathbf{y}}} \mathcal{L}, \text{ and } \tilde{\alpha} \leftarrow \tilde{\alpha} \alpha \nabla_{\tilde{\alpha}} \mathcal{L}$
- 10: end for

11: end for

12: end for

Then we demonstrate the details of the anonymous gastric image distillation algorithm. Firstly, we show our input and output settings. Let  $\theta_0$  denotes the random initial weights of the DCNN model, M,  $E$  and  $I$  respectively denote the number of distilled images, distill epochs and distill steps. Also, let  $\alpha$ , K and T are respectively denote learning rate, batch size and training steps. In addition,  $\tilde{y}_0$  is the initial value of  $\tilde{y}$ , and  $\tilde{\alpha}_0$  is the initial value of  $\tilde{\alpha}$ . And we obtain the distilled images  $\tilde{x}$ , distilled labels  $\tilde{y}$  and the optimized learning rate  $\tilde{\alpha}$  on the output.

Next, we explain the training process of the algorithm. First, we randomly initialize the distilled images  $\tilde{x}$ . Then we initialize the distilled labels  $\tilde{y}$  and the optimized learning rate  $\tilde{\alpha}$  with  $\tilde{y}_0$  and  $\tilde{\alpha}_0$ , respectively. At each training step  $t$ , we get a minibatch of training data  $(\mathbf{x}_t, \mathbf{y}_t)$  whose size is K. Then we get the random initial weights  $\theta_0$  and begin the distilling process. We repeat the distilling process for  $E$  times. At each distilling step  $i$ , we compute the updated weights with Eq. [\(1\)](#page-1-2). Then we evaluate the objective function on the minibatch of training data with Eq. [\(2\)](#page-1-3). Finally, we update the distilled data  $\tilde{\mathbf{x}}$ ,  $\tilde{\mathbf{y}}$ , and  $\tilde{\alpha}$  with Eq. [\(3\)](#page-1-4) based on gradient descent [\[23,](#page-4-22) [24\]](#page-4-23). Algorithm [1](#page-2-0) shows the entire flow of our proposed approach.

#### 2.3. Full Gastric X-ray Image Classification

In this subsection, we explain how to estimate the label of a full gastric X-ray image based on patches. First, when we have a test gastric image, we divide it into  $H \times W$  patches. Then we put the divided patches into the trained DCNN model, and we can obtain the predicted labels of these patches. Next, we respectively calculate the number of patches whose predicted labels are  $N$  and  $P$ . The  $I$ patches that extracted from outside of the stomach are not related to the gastritis/non-gastritis prediction, and hence we do not take into account them to the probability calculation. Finally, we estimate the label of a full gastric X-ray image as follows:

$$
y^{\text{test}} = \begin{cases} 1 & \text{if } \frac{\text{Num}(\mathcal{P})}{\text{Num}(\mathcal{P}) + \text{Num}(\mathcal{N})} \ge \epsilon \\ 0 & \text{otherwise} \end{cases}, \tag{4}
$$

where  $\epsilon$  is a threshold, Num( $\mathcal{P}$ ) denotes the number of gastritis patches, and  $Num(\mathcal{N})$  denotes the number of non-gastritis patches. Note that if  $y^{\text{test}} = 1$ , the estimation result of a full gastric X-ray image is gastritis, and the estimation result is non-gastritis otherwise.

### 3. EXPERIMENTAL RESULTS

In this section, we verify the effectiveness of our distillation method with experimental results. In subsection 3.1, we show the experimental settings of our method. In subsection 3.2, we evaluate the performance of our method by classifying the full gastric X-ray images.

### 3.1. Experimental Settings

This subsection shows the experimental settings of our research. The dataset used in our study contains 815 patients' (240 gastritis and 575 non-gastritis) gastric X-ray images. Each image has a ground truth (gastritis/non-gastritis), which was determined by patient diagnosis results of endoscopic examination and X-ray inspection. All of the gastric X-ray images are gray-scale and high-resolution. The training dataset contains 200 patients' (100 gastritis and 100 nongastritis) images. Also, the rest of the patients' (140 gastritis and 475 non-gastritis) images are included in the test dataset. In the data preprocessing stage, we divided the images into patches (299  $\times$  299 pixels), *i.e.*,  $H = W = 35$ , where the sliding interval was set to 50 pixels. Besides, the patches extracted from the training dataset were labeled as  $I, N$  and  $P$  by a radiological technologist. Note that if the regions inside of the stomach were less than  $1\%$  in a patch, it was labeled as  $I$ . In addition, if the regions inside of the stomach were more than 85% in a patch, it was labeled as  $\mathcal N$  or  $\mathcal P$ . And we discarded the rest of the patches in the training dataset. As a result, we obtained  $\mathcal{I}, \mathcal{N}$  and  $\mathcal{P}$  whose number of patches were 48,385, 42,785 and 45,127, respectively.

In the training phase, we loaded the patch-based training dataset  $(\mathcal{I}, \mathcal{N})$  and  $(\mathcal{P})$  into a DCNN model. Since the distilling process involves the calculation of a quadratic gradient, a complicated DCNN model can lead to inefficiency. Hence, we constructed the ResNet18 [\[25\]](#page-4-24) model with PyTorch framework [\[26,](#page-4-25) [27\]](#page-4-26), and the loss  $\ell$  was cross entropy loss. In our experiments, we set the number of distilled images of each category to 1, *i.e.*,  $M = 3$ . Besides, the distill epochs and steps were set to 3, *i.e.*,  $E = I = 3$  (total 9) distill steps). And we initialized the soft labels with one-hot values of the original labels, which tends to have higher performance compared to the random initialization. The hard-label distillation method had the same settings as soft-label distillation, except for the labels being fixed [\[28\]](#page-4-27). As a result, the training process distilled each class into one image and saved the distilled images, distilled

<span id="page-3-0"></span>

| Method                 | Sen   | Spe          | HM           |
|------------------------|-------|--------------|--------------|
| <b>Proposed Method</b> | 0.886 | <b>0.869</b> | <b>0.877</b> |
| ResNet18 (3000)        | 0.814 | 0.832        | 0.823        |

ResNet18 (2000) 0.907 0.760 0.827 ResNet18 (1000) 0.914 0.669 0.773

Table 1. Results of proposed method compared with ResNet18 trained on the random subsets in Ex. I.

<span id="page-3-1"></span>Table 2. Results of proposed method compared with hard-label distillation in Ex. II.

| Method                  | Sen   | Spe   | HM    |
|-------------------------|-------|-------|-------|
| <b>Proposed Method</b>  | 0.886 | 0.869 | 0.877 |
| Hard-Label Distillation | 0.829 | 0.884 | 0.856 |

labels and the optimized learning rate. We performed 400 epochs in both of soft-label distillation and hard-label distillation and saved the results after every ten epochs for testing and evaluation. It is difficult for a DCNN model to learn from only several images, so we respectively selected 1,000, 2,000 and 3,000 images per category from the patch-based training dataset and trained three ResNet18 models until convergence.

In the test phase, we performed two experiments (Ex. I and Ex. II) to show the validity of our proposed method quantitatively. Firstly, in Ex. I, we selected the distilled soft-label images that have the best classification performance on the patch-based training dataset and evaluated the performance on the full X-ray images of the test dataset. Specifically, we evaluated the full gastric X-ray images classification performance of the ResNet18 models trained on the three random subsets. Secondly, in Ex. II, we used the hard-label distillation as a comparative method. As in Ex. I, we selected the best distilled hard-label images, which have the highest classification accuracy on the patch-based training dataset. And we utilized the distilled hard-label images to evaluate the classification performance on the full gastric X-ray images. Note that we set the threshold  $\epsilon$  to 0.4 in both of Exs. I and II, which tends to have an excellent classification performance. We utilized the following sensitivity (Sen), specificity (Spe) and harmonic mean (HM) of Sen and Spe as evaluation indexes:

$$
Sen = \frac{TP}{TP + FN},
$$
\n(5)

$$
Spe = \frac{TN}{TN + FP},
$$
\n(6)

$$
HM = \frac{2 \times \text{Sen} \times \text{Spe}}{\text{Sen} + \text{Spe}},\tag{7}
$$

where TP, FN, TN and FP denote the number of true positive, false negative, true negative and false positive, respectively.

#### 3.2. Results and Discussion

The experimental results are shown in Tables [1](#page-3-0) and [2.](#page-3-1) Table [1](#page-3-0) shows the full gastric X-ray images classification performance of our proposed method and ResNet18 trained on the random subsets. The ResNet18 model trained with 3,000 images per category (total 9,000 images) has an HM score of 0.823. On the other hand, our soft-

Image /page/3/Figure/13 description: The image displays two rows of three images each, all appearing as static noise. The top row is labeled "(a) distilled hard-label images at distill step 9" with individual labels "I", "N", and "P" above each image. The bottom row is preceded by the text "Dataset: Gastritis, Arch: ResNet18, Step: 9, LR: 0.0073" and has individual labels "I (base)", "N (base)", and "P (base)" above each image. The bottom row is also labeled "(b) distilled soft-label images at distill step 9".

(b) distilled soft-label images at distill step 9

<span id="page-3-2"></span>Fig. 3. Examples of distilled images. I: the distilled image of label  $I$ , N: the distilled image of label  $N$ , P: the distilled image of label  $P$ , LR: the optimized learning rate.

label distillation method that distilled each class into only one image for training has a higher HM score of 0.877. We can see that the proposed method realizes competitive classification accuracy with a tiny distilled dataset. Table [2](#page-3-1) shows the full gastric X-ray images classification performance of our soft-label distillation and the hard-label distillation method. The HM scores of both the two approaches exceed 0.85, but our soft-label distillation outperforms the hard-label distillation with a higher score. Furthermore, we can see that the classification performance of our method becomes more stable because of the balance of Sen score and Spe score. It means that the distilled images with soft-label can lead to better distillation results. Experimental results clearly showed the validity of our purposed soft-label distillation method.

Figure [3](#page-3-2) shows examples of distilled hard-label images and softlabel images used in our experiments. With the distillation methods, the information of gastritis/non-gastritis patch images was extracted and merged into only one image. Note that I (base), N (base) and P (base) respectively denote the image belongs to label  $\mathcal{I}, \mathcal{N}$  and  $\mathcal{P}$ with the highest probability. From Fig. [3,](#page-3-2) we can see that the features of gastritis/non-gastritis patches cannot be distinguished, in other words, the gastric images were anonymized completely. Hence, the distilled images have no private information of patients. Figure [3](#page-3-2) clearly showed that our proposed method can effectively compress and anonymize the medical image data.

### 4. CONCLUSION

In this paper, we have proposed a soft-label anonymous gastric image distillation method. The proposed method realizes high classification performance by distilling each class into one image for training. Furthermore, the sharing of medical image data with our approach can become more efficient and safer because the distilled images are anonymous.

### 5. REFERENCES

- <span id="page-4-0"></span>[1] Yann LeCun, Yoshua Bengio, and Geoffrey Hinton, "Deep learning," *Nature*, vol. 521, pp. 436–444, 2015.
- <span id="page-4-1"></span>[2] Geert Litjens, Thijs Kooi, Babak Ehteshami Bejnordi, Arnaud Arindra Adiyoso Setio, Francesco Ciompi, Mohsen Ghafoorian, Jeroen Awm Van Der Laak, Bram Van Ginneken, and Clara I Sánchez, "A survey on deep learning in medical image analysis," *Medical Image Analysis*, vol. 42, pp. 60–88, 2017.
- <span id="page-4-2"></span>[3] Oliver Faust, Yuki Hagiwara, Tan Jen Hong, Oh Shu Lih, and U Rajendra Acharya, "Deep learning for healthcare applications based on physiological signals: A review," *Computer Methods and Programs in Biomedicine*, vol. 161, pp. 1–13, 2018.
- <span id="page-4-3"></span>[4] Elissa R Weitzman, Liljana Kaci, and Kenneth D Mandl, "Sharing medical data for health research: The early personal health record experience," *Journal of Medical Internet Research*, vol. 12, no. 2, pp. e14, 2010.
- <span id="page-4-4"></span>[5] Sandra Goldbeck-Wood, Alexander Dorozynski, Leiv Gunnar Lie, Masaya Yamauchi, Christopher Zinn, Deborah Josefson, and Miranda Ingram, "Complementary medicine is booming worldwide," *British Medical Journal*, vol. 313, no. 7050, pp. 131–134, 1996.
- <span id="page-4-5"></span>[6] Pareek Narendra K. and Patidar Vinod, "Medical image protection using genetic algorithm operations," *Soft Computing*, vol. 20, no. 2, pp. 763–772, 2016.
- <span id="page-4-6"></span>[7] J Arturo Olvera-Lopez, J Ariel Carrasco-Ochoa, J Francisco ´ Martínez-Trinidad, and Josef Kittler, "A review of instance selection methods," *Artificial Intelligence Review*, vol. 34, no. 2, pp. 133–143, 2010.
- <span id="page-4-7"></span>[8] Olivier Bachem, Mario Lucic, and Andreas Krause, "Practical coreset constructions for machine learning," *arXiv preprint arXiv:1703.06476*, 2017.
- <span id="page-4-8"></span>[9] Trevor Campbell and Tamara Broderick, "Bayesian coreset construction via greedy iterative geodesic ascent," in *Proceedings of the International Conference on Machine Learning (ICML)*, 2018, pp. 698–706.
- <span id="page-4-9"></span>[10] Ozan Sener and Silvio Savarese, "Active learning for convolutional neural networks: A core-set approach," in *Proceedings of the International Conference on Learning Representations (ICLR)*, 2018.
- <span id="page-4-10"></span>[11] David A Cohn, Zoubin Ghahramani, and Michael I Jordan, "Active learning with statistical models," *Journal of Artificial Intelligence Research*, vol. 4, pp. 129–145, 1996.
- <span id="page-4-11"></span>[12] Simon Tong and Edward Chang, "Support vector machine active learning for image retrieval," in *Proceedings of the ACM International Conference on Multimedia (ACM MM)*, 2001, pp. 107–118.
- <span id="page-4-12"></span>[13] Deven McGraw, James X Dempsey, Leslie Harris, and Janlori Goldman, "Privacy as an enabler, not an impediment: building trust into health information exchange," *Health Affairs*, vol. 28, no. 2, pp. 416–427, 2009.
- <span id="page-4-13"></span>[14] Bradley Malin, David Karp, and Richard H Scheuermann, "Technical and policy approaches to balancing patient privacy and data sharing in clinical and translational research," *Journal of Investigative Medicine*, vol. 58, no. 1, pp. 11–18, 2010.

- <span id="page-4-14"></span>[15] Jules J Berman, "Confidentiality issues for medical data miners," *Artificial Intelligence in Medicine*, vol. 26, no. 1-2, pp. 25–36, 2002.
- <span id="page-4-15"></span>[16] Ji-Jiang Yang, Jian-Qiang Li, and Yu Niu, "A hybrid solution for privacy preserving medical data sharing in the cloud environment," *Future Generation Computer Systems*, vol. 43-44, pp. 74–86, 2015.
- <span id="page-4-16"></span>[17] Benjamin Fabian, Tatiana Ermakova, and Philipp Junghanns, "Collaborative and secure sharing of healthcare data in multiclouds," *Information Systems*, vol. 48, pp. 132–150, 2015.
- <span id="page-4-17"></span>[18] Yoshua Bengio, "Gradient-based optimization of hyperparameters," *Neural Computation*, vol. 12, no. 8, pp. 1889–1900, 2000.
- <span id="page-4-18"></span>[19] Dougal Maclaurin, David Duvenaud, and Ryan Adams, "Gradient-based hyperparameter optimization through reversible learning," in *Proceedings of the International Conference on Machine Learning (ICML)*, 2015, pp. 2113–2122.
- <span id="page-4-19"></span>[20] Ren Togo, Kenta Ishihara, Takahiro Ogawa, and Miki Haseyama, "Anonymous gastritis image generation via adversarial learning from gastric x-ray images," in *Proceedings of the International Conference on Image Processing (ICIP)*, 2018, pp. 2082–2086.
- <span id="page-4-20"></span>[21] Misaki Kanai, Ren Togo, Takahiro Ogawa, and Miki Haseyama, "Gastritis detection from gastric x-ray images via fine-tuning of patch-based deep convolutional neural network," in *Proceedings of the International Conference on Image Processing (ICIP)*, 2019, pp. 1371–1375.
- <span id="page-4-21"></span>[22] Ilia Sucholutsky and Matthias Schonlau, "Soft-label dataset distillation and text dataset distillation," *arXiv preprint arXiv:1910.02551*, 2019.
- <span id="page-4-22"></span>[23] James S Bergstra, Rémi Bardenet, Yoshua Bengio, and Balázs Kégl, "Algorithms for hyper-parameter optimization," in Pro*ceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2011, pp. 2546–2554.
- <span id="page-4-23"></span>[24] Chelsea Finn, Pieter Abbeel, and Sergey Levine, "Modelagnostic meta-learning for fast adaptation of deep networks," in *Proceedings of the International Conference on Machine Learning (ICML)*, 2017, pp. 1126–1135.
- <span id="page-4-24"></span>[25] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun, "Deep residual learning for image recognition," in *Proceedings of the Conference on Computer Vision and Pattern Recognition (CVPR)*, 2016, pp. 770–778.
- <span id="page-4-25"></span>[26] Adam Paszke, Sam Gross, Soumith Chintala, Gregory Chanan, Edward Yang, Zachary DeVito, Zeming Lin, Alban Desmaison, Luca Antiga, and Adam Lerer, "Automatic differentiation in pytorch," in *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS), Workshop*, 2017.
- <span id="page-4-26"></span>[27] Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, et al., "Pytorch: An imperative style, high-performance deep learning library," in *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, 2019, pp. 8024–8035.
- <span id="page-4-27"></span>[28] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A. Efros, "Dataset distillation," *arXiv preprint arXiv:1811.10959*, 2018.
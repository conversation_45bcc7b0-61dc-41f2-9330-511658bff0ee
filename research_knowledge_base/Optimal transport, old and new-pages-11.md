# Solution of the Monge problem II: Local approach

In the previous chapter, we tried to establish the almost sure singlevaluedness of the c-subdifferential by an argument involving "global" topological properties, such as connectedness. Since this strategy worked out only in certain particular cases, we shall now explore a different method, based on *local* properties of *c*-convex functions. The idea is that the global question "Is the c-subdifferential of  $\psi$  at x single-valued or not?" might be much more subtle to attack than the local question "Is the function  $\psi$  differentiable at x or not?" For a large class of cost functions, these questions are in fact equivalent; but these different formulations suggest different strategies. So in this chapter, the emphasis will be on tangent vectors and gradients, rather than points in the c-subdifferential.

This approach takes its source from the works by <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON> on the quadratic cost in  $\mathbb{R}^n$ , around the end of the eighties. It has since then been improved by many authors, a key step being the extension to Riemannian manifolds, first addressed by <PERSON><PERSON><PERSON><PERSON><PERSON> in 2000.

The main results in this chapter are Theorems 10.28, 10.38 and (to a lesser extent) 10.42, which solve the Monge problem with increasing generality. For Parts II and III of this course, only the particular case considered in Theorem 10.41 is needed.

## A heuristic argument

Let  $\psi$  be a c-convex function on a Riemannian manifold M, and  $\phi = \psi^c$ . Assume that  $y \in \partial_c \psi(x)$ ; then, from the definition of c-subdifferential, 228 10 Solution of the Monge problem II: Local approach

one has, for all  $\widetilde{x} \in M$ ,

$$
\begin{cases}\n\phi(y) - \psi(x) = c(x, y) \\
\phi(y) - \psi(\tilde{x}) \le c(\tilde{x}, y).\n\end{cases}
$$
\n(10.1)

It follows that

$$
\psi(x) - \psi(\tilde{x}) \le c(\tilde{x}, y) - c(x, y). \tag{10.2}
$$

Now the idea is to see what happens when  $\tilde{x} \to x$ , along a given direction. Let w be a tangent vector at x, and consider a path  $\varepsilon \to \tilde{x}(\varepsilon)$ , defined for  $\varepsilon \in [0, \varepsilon_0)$ , with initial position x and initial velocity w. (For instance,  $\widetilde{x}(\varepsilon) = \exp_x(\varepsilon w)$ ; or in  $\mathbb{R}^n$ , just consider  $\widetilde{x}(\varepsilon) = x + \varepsilon w$ .) Assume that  $\psi$  and  $c(\cdot, y)$  are differentiable at x, divide both sides of (10.2) by  $\varepsilon > 0$  and pass to the limit:

$$
-\nabla\psi(x)\cdot w \le \nabla_x c(x,y)\cdot w. \tag{10.3}
$$

If then one changes w to  $-w$ , the inequality will be reversed. So necessarily

$$
\nabla \psi(x) + \nabla_x c(x, y) = 0. \tag{10.4}
$$

If x is given, this is an equation for y. Since our goal is to show that y is determined by  $x$ , then it will surely help if  $(10.4)$  admits at most one solution, and this will obviously be the case if  $\nabla_x c(x, \cdot)$  is *injective*. This property (injectivity of  $\nabla_x c(x, \cdot)$ ) is in fact a classical condition in the theory of dynamical system, where it is sometimes referred to as a twist condition.

Three objections might immediately be raised. First,  $\psi$  is an unknown of the problem, defined by an infimum, so why would it be differentiable? Second, the injectivity of  $\nabla_x c$  as a function of y seems quite hard to check on concrete examples. Third, even if  $c$  is given in the problem and a priori quite nice, why should it be differentiable at  $(x, y)$ ? As a very simple example, consider the square distance function  $d(x, y)^2$  on the 1-dimensional circle  $S^1 = \mathbb{R}/(2\pi\mathbb{Z})$ , identified with  $[0, 2\pi)$ :

$$
d(x, y) = \min(|x - y|, 2\pi - |x - y|).
$$

Then  $d(x, y)$  is not differentiable as a function of x when  $|y - x| = \pi$ , and of course  $d(x, y)^2$  is not differentiable either (see Figure 10.1).

Similar problems would occur on, say, a compact Riemannian manifold, as soon as there is no uniqueness of the geodesic joining x to  $y$ .

Image /page/2/Figure/1 description: The image contains two plots. The left plot shows a triangular function on the x-axis ranging from 0 to 2π, with the y-axis labeled d(x, 0). The function starts at 0, increases linearly to a peak at x=π, and then decreases linearly back to 0 at x=2π. The right plot shows a function on the x-axis ranging from 0 to 2π, with the y-axis labeled d(x, 0)^2. This function starts at 0, increases sharply to a peak at x=π, and then decreases rapidly towards 0 as x approaches 2π. The shape of this function resembles a squared version of the triangular function, with a sharper peak and faster decay.

Fig. 10.1. The distance function  $d(\cdot, y)$  on  $S^1$ , and its square. The upper-pointing singularity is typical. The square distance is not differentiable when  $|x-y| = \pi$ ; still it is superdifferentiable, in a sense that is explained later.

For instance, if  $N$  and  $S$  respectively stand for the north and south poles on  $S^2$ , then  $d(x, S)$  fails to be differentiable at  $x = N$ .

Of course, for any x this happens only for a negligible set of  $y$ 's; and the cost function is differentiable everywhere else, so we might think that this is not a serious problem. But who can tell us that the optimal transport will not try to take each  $x$  (or a lot of them) to a place  $y$ such that  $c(x, y)$  is not differentiable?

To solve these problems, it will be useful to use some concepts from nonsmooth analysis: subdifferentiability, superdifferentiability, approximate differentiability. The short answers to the above problems are that (a) under adequate assumptions on the cost function,  $\psi$  will be differentiable out of a very small set (of codimension at least 1); (b)  $c$  will be superdifferentiable because it derives from a Lagrangian, and subdifferentiable wherever  $\psi$  itself is differentiable; (c) where it exists,  $\nabla_x c$ will be injective because  $c$  derives from a *strictly convex* Lagrangian.

The next three sections will be devoted to some basic reminders about differentiability and regularity in a nonsmooth context. For the convenience of the non-expert reader, I shall provide complete proofs of the most basic results about these issues. Conversely, readers who feel very comfortable with these notions can skip these sections.

## Differentiability and approximate differentiability

Let us start with the classical definition of differentiability:

**Definition 10.1 (Differentiability).** Let  $U \subset \mathbb{R}^n$  be an open set. A function  $f: U \to \mathbb{R}$  is said to be differentiable at  $x \in U$  if there exists a vector  $p \in \mathbb{R}^n$  such that

$$
f(z) = f(x) + \langle p, z - x \rangle + o(|z - x|) \quad \text{as } z \to x.
$$

Then the vector p is uniquely determined; it is called the gradient of f at x, and is denoted by  $\nabla f(x)$ ; the map  $w \to \langle p, w \rangle$  is the differential of  $f$  at  $x$ .

If U is an open set of a smooth Riemannian manifold M,  $f: U \to \mathbb{R}$ is said to be differentiable at x if it is so when expressed in a local chart around x; or equivalently if there is a tangent vector  $p \in T_xM$  such that

$$
f(\exp_w x) = f(x) + \langle p, w \rangle + o(|w|) \quad \text{as } w \to 0.
$$

The vector p is again denoted by  $\nabla f(x)$ .

Differentiability is a pointwise concept, which is not invariant under, say, change of Lebesgue equivalence class: If f is differentiable or even  $C^{\infty}$  everywhere, by changing it on a dense countable set we may obtain a function which is discontinuous everywhere, and a fortiori not differentiable. The next notion is more flexible in this respect, since it allows for modification on a negligible set. It relies on the useful concept of **density**. Recall that a measurable set  $A$  is said to have density  $\rho$  at x if

$$
\lim_{r \to 0} \frac{\text{vol}\left[A \cap B_r(x)\right]}{\text{vol}\left[B_r(x)\right]} = \rho.
$$

It is a basic result of measure theory that a measurable set in  $\mathbb{R}^n$ , or in a Riemannian manifold, has density 1 at almost all of its points.

Definition 10.2 (Approximate differentiability). Let U be an open set of a Riemannian manifold M, and let  $f: U \to \mathbb{R} \cup \{\pm \infty\}$  be a measurable function. Then f is said to be approximately differentiable at  $x \in U$  if there is a measurable function  $f: U \to \mathbb{R}$ , differentiable at x, such that the set  $\{f = f\}$  has density 1 at x; in other words,

$$
\lim_{r \to 0} \frac{\text{vol}\left[\left\{z \in B_r(x); \ f(z) = \tilde{f}(z)\right\}\right]}{\text{vol}\left[B_r(x)\right]} = 1.
$$

Then one defines the approximate gradient of  $f$  at  $x$  by the formula

$$
\widetilde{\nabla}f(x) = \nabla \widetilde{f}(x).
$$

*Proof that*  $\tilde{\nabla}f(x)$  *is well-defined.* Since this concept is local and invariant by diffeomorphism, it is sufficient to treat the case when  $U$  is a subset of  $\mathbb{R}^n$ .

Let  $f_1$  and  $f_2$  be two measurable functions on U which are both differentiable at  $x$  and coincide with  $f$  on a set of density 1. The problem is to show that  $\nabla f_1(x) = \nabla f_2(x)$ .

For each  $r > 0$ , let  $Z_r$  be the set of points in  $B_r(x)$  where either  $f(x) \neq f_1(x)$  or  $f(x) \neq f_2(x)$ . It is clear that vol  $[Z_r] = o(\text{vol }[B_r(x)])$ . Since  $f_1$  and  $f_2$  are continuous at x, one can write

$$
\widetilde{f}_1(x) = \lim_{r \to 0} \frac{1}{\text{vol}[B_r(x)]} \int \widetilde{f}_1(z) dz
$$

$$
= \lim_{r \to 0} \frac{1}{\text{vol}[B_r(x) \setminus Z_r]} \int \widetilde{f}_1(z) dz = \lim_{r \to 0} \frac{1}{\text{vol}[B_r(x) \setminus Z_r]} \int \widetilde{f}_2(z) dz
$$

$$
= \lim_{r \to 0} \frac{1}{\text{vol}[B_r(x)]} \int \widetilde{f}_2(z) dz = \widetilde{f}_2(x).
$$

So let  $f(x)$  be the common value of  $f_1$  and  $f_2$  at x. Next, for any  $z \in B_r(x) \setminus Z_r$ , one has

$$
\begin{cases}\ \widetilde{f}_1(z) = \widetilde{f}(x) + \langle \nabla \widetilde{f}_1(x), z - x \rangle + o(r), \\ \ \widetilde{f}_2(z) = \widetilde{f}(x) + \langle \nabla \widetilde{f}_2(x), z - x \rangle + o(r), \end{cases}
$$

so

$$
\left\langle \nabla \widetilde{f}_1(x) - \nabla \widetilde{f}_2(x), z - x \right\rangle = o(r).
$$

Let  $w := \nabla f_1(x) - \nabla f_2(x)$ ; the previous estimate reads

$$
x \notin Z_r \Longrightarrow \langle w, z - x \rangle = o(r). \tag{10.5}
$$

If  $w \neq 0$ , then the set of  $z \in B_r(x)$  such that  $\langle w, z - x \rangle \geq r/2$  has measure at least K vol  $[B_r(x)]$ , for some  $K > 0$ . If r is small enough, then vol  $[Z_r] \leq (K/4)$  vol  $[B_r(x)] \leq (K/2)$  vol  $[B_r(x) \setminus Z_r]$ , so

$$
\text{vol}\left[\left\{z\in B_r(x)\setminus Z_r;\ \langle w,z-x\rangle\geq \frac{r}{2}\right\}\right]\geq \frac{K}{2}\text{ vol}\left[B_r(x)\setminus Z_r\right].
$$

Then (still for  $r$  small enough),

$$
\frac{\int_{B_r(x)\setminus Z_r} |\langle w, z - x \rangle| dy}{\text{vol}\left[B_r(x)\setminus Z_r\right]} \ge \frac{Kr}{4},
$$

in contradiction with (10.5). The conclusion is that  $w = 0$ , which was the goal.  $□$ 

## Regularity in a nonsmooth world

Regularity is a loose concept about the control of "how fast" a function varies. In the present section I shall review some notions of regularity which apply in a nonsmooth context, and act as a replacement for, say,  $C^1$  or  $C^2$  regularity bounds.

Definition 10.3 (Lipschitz continuity). Let  $U \subset \mathbb{R}^n$  be open, and let  $f: U \to \mathbb{R}$  be given. Then:

(i) f is said to be Lipschitz if there exists  $L < \infty$  such that

$$
\forall x, z \in U, \qquad |f(z) - f(x)| \le L|z - x|.
$$

(ii) f is said to be locally Lipschitz if, for any  $x_0 \in U$ , there is a neighborhood O of  $x_0$  in which f is Lipschitz.

If U is an open subset of a Riemannian manifold M, then  $f: U \to \mathbb{R}$ is said to be locally Lipschitz if it is so when expressed in local charts; or equivalently if f is Lipschitz on any compact subset of U, equipped with the geodesic distance on M.

**Example 10.4.** Obviously, a  $C<sup>1</sup>$  function is locally Lipschitz, but the converse is not true (think of  $f(x) = |x|$ ).

Definition 10.5 (Subdifferentiability, superdifferentiability). Let U be an open set of  $\mathbb{R}^n$ , and  $f: U \to \mathbb{R}$  a function. Then:

(i) f is said to be subdifferentiable at x, with subgradient p, if

$$
f(z) \ge f(x) + \langle p, z - x \rangle + o(|z - x|).
$$

The convex set of all subgradients p at x will be denoted by  $\nabla^- f(x)$ .

(ii)  $f$  is said to be uniformly subdifferentiable in  $U$  if there is a continuous function  $\omega : \mathbb{R}_+ \to \mathbb{R}_+$ , such that  $\omega(r) = o(r)$  as  $r \to 0$ , and

$$
\forall x \in U \qquad \exists p \in \mathbb{R}^n; \qquad f(z) \ge f(x) + \langle p, z - x \rangle - \omega(|z - x|).
$$

 $(iii)$  f is said to be locally subdifferentiable (or locally uniformly subdifferentiable) in U if each  $x_0 \in U$  admits a neighborhood on which f is uniformly subdifferentiable.

If U is an open set of a smooth manifold M, a function  $f: U \to \mathbb{R}$ is said to be subdifferentiable at some point  $x$  (resp. locally subdifferentiable in U) if it is so when expressed in local charts.

Corresponding notions of superdifferentiability and supergradients are obtained in an obvious way by just reversing the signs of the inequalities. The convex set of supergradients for  $f$  at  $x$  is denoted by  $\nabla^+ f(x)$ .

**Examples 10.6.** If f has a minimum at  $x_0 \in U$ , then 0 is a subgradient of f at  $x_0$ , whatever the regularity of f. If f has a subgradient p at x and g is smooth, then  $f + g$  has a subgradient  $p + \nabla g(x)$  at x. If f is convex in  $U$ , then it is (uniformly) subdifferentiable at every point in  $U$ , by the well-known inequality

$$
f(z) \ge f(x) + \langle p, z - x \rangle,
$$

which holds true as soon as  $p \in \partial f(x)$  and  $[x, y] \subset U$ . If f is the sum of a convex function and a smooth function, then it is also uniformly subdifferentiable.

It is obvious that differentiability implies both subdifferentiability and superdifferentiability. The converse is true, as shown by the next statement.

Proposition 10.7 (Sub- and superdifferentiability imply differentiability). Let  $U$  be an open set of a smooth Riemannian manifold M, and let  $f: U \to \mathbb{R}$  be a function. Then f is differentiable at x if and only if it is both subdifferentiable and superdifferentiable there; and then

$$
\nabla^- f(x) = \nabla^+ f(x) = \{\nabla f(x)\}.
$$

Proof of Proposition 10.7. The only nontrivial implication is that if f is both subdifferentiable and superdifferentiable, then it is differentiable. Since this statement is local and invariant by diffeomorphism, let us pretend that  $U \subset \mathbb{R}^n$ . So let  $p \in \nabla^- f(x)$  and  $q \in \nabla^+ f(x)$ ; then

$$
f(z) - f(x) \ge \langle p, z - x \rangle - o(|z - x|);
$$

234 10 Solution of the Monge problem II: Local approach

$$
f(z) - f(x) \le \langle q, z - x \rangle + o(|z - x|).
$$

This implies  $\langle p - q, z - x \rangle \le o(|z - x|)$ , which means

$$
\lim_{z \to x; \ z \neq x} \left\langle p - q, \frac{z - x}{|z - x|} \right\rangle = 0.
$$

Since the unit vector  $(z - x)/|z - x|$  can take arbitrary fixed values in the unit sphere as  $z \to x$ , it follows that  $p = q$ . Then

$$
f(z) - f(x) = \langle p, z - x \rangle + o(|z - x|),
$$

which means that  $f$  is indeed differentiable at  $x$ . This also shows that  $p = q = \nabla f(x)$ , and the proof is complete. □

The next proposition summarizes some of the most important results about the links between regularity and differentiability:

Theorem 10.8 (Regularity and differentiability almost everywhere). Let U be an open subset of a smooth Riemannian manifold M, and let  $f: U \to \mathbb{R}$  be a function. Let n be the dimension of M. Then:

(i) If f is continuous, then it is subdifferentiable on a dense subset of U, and also superdifferentiable on a dense subset of U.

(ii) If  $f$  is locally Lipschitz, then it is differentiable almost everywhere (with respect to the volume measure).

(iii) If  $f$  is locally subdifferentiable (resp. locally superdifferentiable), then it is locally Lipschitz and differentiable out of a countably  $(n-1)$ rectifiable set. Moreover, the set of differentiability points coincides with the set of points where there is a unique subgradient (resp. supergradient). Finally,  $\nabla f$  is continuous on its domain of definition.

Remark 10.9. Statement (ii) is known as Rademacher's theorem. The conclusion in statement (iii) is stronger than differentiability almost everywhere, since an  $(n-1)$ -rectifiable set has dimension  $n-1$ , and is therefore negligible. In fact, as we shall see very soon, the local subdifferentiability property is stronger than the local Lipschitz property. Reminders about the notion of countable rectifiability are provided in the Appendix.

Proof of Theorem 10.8. First we can cover U by a countable collection of small open sets  $U_k$ , each of which is diffeomorphic to an open subset

 $O_k$  of  $\mathbb{R}^n$ . Then, since all the concepts involved are local and invariant under diffeomorphism, we may work in  $O_k$ . So in the sequel, I shall pretend that U is a subset of  $\mathbb{R}^n$ .

Let us start with the proof of (i). Let  $f$  be continuous on  $U$ , and let V be an open subset of U; the problem is to show that  $f$  admits at least one point of subdifferentiability in V. So let  $x_0 \in V$ , and let  $r > 0$  be so small that  $\overline{B(x_0, r)} \subset V$ . Let  $B = B(x_0, r)$ , let  $\varepsilon > 0$ and let g be defined on  $\overline{B}$  by  $g(x) := f(x) + |x - x_0|^2 / \varepsilon$ . Since f is continuous, g attains its minimum on  $\overline{B}$ . But g on  $\partial B$  is bounded below by  $r^2/\varepsilon - M$ , where M is an upper bound for  $|f|$  on  $\overline{B}$ . If  $\varepsilon < r^2/(2M)$ , then  $g(x_0) = f(x_0) \leq M < r^2/\varepsilon - M \leq \inf_{\partial B} g$ ; so g cannot achieve its minimum on  $\partial B$ , and has to achieve it at some point  $x_1 \in B$ . Then g is subdifferentiable at  $x_1$ , and therefore f also. This establishes (i).

The other two statements are more tricky. Let  $f: U \to \mathbb{R}$  be a Lipschitz function. For  $v \in \mathbb{R}^n$  and  $x \in U$ , define

$$
D_v f(x) := \lim_{t \to 0} \left[ \frac{f(x + tv) - f(x)}{t} \right],
$$
 (10.6)

provided that this limit exists. The problem is to show that for almost any x, there is a vector  $p(x)$  such that  $D_v f(x) = \langle p(x), v \rangle$  and the limit in (10.6) is uniform in, say,  $v \in S^{n-1}$ . Since the functions  $[f(x + tv)$  $f(x)/t$  are uniformly Lipschitz in v, it is enough to prove the pointwise convergence (that is, the mere existence of  $D_v f(x)$ ), and then the limit will automatically be uniform by Ascoli's theorem. So the goal is to show that for almost any x, the limit  $D_v f(x)$  exists for any v, and is linear in v.

It is easily checked that:

(a)  $D_v f(x)$  is homogeneous in v:  $D_{tv} f(x) = t D_v f(x);$ 

(b)  $D_v f(x)$  is a Lipschitz function of v on its domain: in fact,  $|D_v f(x) - D_w f(x)| \leq L |v - w|$ , where  $L = ||f||_{\text{Lip}}$ ;

(c) If  $D_w f(x) \to \ell$  as  $w \to v$ , then  $D_v f(x) = \ell$ ; this comes from the estimate

$$
\sup_{t} \left| \left( \frac{f(x+tv) - f(x)}{t} \right) - \left( \frac{f(x+tv_k) - f(x)}{t} \right) \right| \le ||f||_{\text{Lip}} |v - v_k|.
$$

For each  $v \in \mathbb{R}^n$ , let  $A_v$  be the set of  $x \in \mathbb{R}^n$  such that  $D_v f(x)$ does not exist. The first claim is that each  $A_v$  has zero Lebesgue measure. This is obvious if  $v = 0$ . Otherwise, let  $H = v^{\perp}$  be the hyperplane orthogonal to v, passing through the origin. For each  $x_0 \in H$ , let  $L_{x_0} = x_0 + \mathbb{R}v$  be the line parallel to v, passing through  $x_0$ . The nonexistence of  $D_v f(x)$  at  $x = x_0 + t_0 v$  is equivalent to the nondifferentiability of  $t \mapsto f(x + tv)$  at  $t = t_0$ . Since  $t \mapsto f(x + tv)$  is Lipschitz  $\mathbb{R} \to \mathbb{R}$ , it follows from a well-known result of real analysis that it is differentiable for  $\lambda_1$ -almost all  $t \in \mathbb{R}$ , where  $\lambda_1$  stands for the one-dimensional Lebesgue measure. So  $\lambda_1[A_v \cap L_{x_0}] = 0$ . Then by Fubini's theorem,  $\lambda_n[A_v] = \int_H \lambda_1[A_v \cap L_{x_0}] dx_0 = 0$ , where  $\lambda_n$  is the n-dimensional Lebesgue measure, and this proves the claim.

The problem consists in extending the function  $D_v f$  into a *linear* (not just homogeneous) function of  $v$ . Let  $v \in \mathbb{R}^n$ , and let  $\zeta$  be a smooth compactly supported function. Then, by the dominated convergence theorem,

$$
(\zeta * D_v f)(x) = \int \zeta(x - y) \lim_{t \to 0} \left[ \frac{f(y + tv) - f(y)}{t} \right] dy
$$
  
$$
= \lim_{t \to 0} \frac{1}{t} \int \zeta(x - y) [f(y + tv) - f(y)] dy
$$
  
$$
= \lim_{t \to 0} \frac{1}{t} \int \left[ \zeta(x - y + tv) - \zeta(x - y) \right] f(y) dy
$$
  
$$
= \int \langle \nabla \zeta(x - y), v \rangle f(y) dy.
$$

(Note that  $\zeta * D_v f$  is well-defined for any x, even if  $D_v f$  is defined only for almost all x.) So  $\zeta * D_v f$  depends linearly on v. In particular, if v and w are any two vectors in  $\mathbb{R}^n$ , then

$$
\zeta * [D_{v+w}f - D_vf - D_wf] = 0.
$$

Since  $\zeta$  is arbitrary, it follows that

$$
D_v f(x) + D_w f(x) = D_{v+w} f(x)
$$
\n(10.7)

for almost all  $x \in \mathbb{R}^n \setminus (A_v \cap A_w \cap A_{v+w})$ , that is, for almost all  $x \in \mathbb{R}^n$ .

Now it is easy to conclude. Let  $B_{v,w}$  be the set of all  $x \in \mathbb{R}^n$  such that  $D_v f(x)$ ,  $D_w f(x)$  or  $D_{v+w} f(x)$  is not well-defined, or (10.7) does not hold true. Let  $(v_k)_{k \in \mathbb{N}}$  be a dense sequence in  $\mathbb{R}^n$ , and let  $B :=$  $\bigcup_{j,k\in\mathbb{N}} B_{v_j,v_k}$ . Then B is still Lebesgue-negligible, and for each  $x \notin B$ we have

$$
D_{v_j+v_k}f(x) = D_{v_j}f(x) + D_{v_k}f(x).
$$
\n(10.8)

Since  $D_{\nu}f(x)$  is a Lipschitz continuous function of v, it can be extended uniquely into a Lipschitz continuous function, defined for all  $x \notin B$ 

and  $v \in \mathbb{R}^n$ , which turns out to be  $D_v f(x)$  in view of Property (c). By passing to the limit in (10.8), we see that  $D_v f(x)$  is an additive function of  $v$ . We already know that it is a homogeneous function of  $v$ , so it is in fact linear. This concludes the proof of (ii).

Next let us turn to the proof of (iii). Before going on, I shall first explain in an informal way the main idea of the proof of statement (iii). Suppose for simplicity that we are dealing with a convex function in  $\mathbb{R}^n$ . If p lies in the subdifferential  $\partial \psi(x)$  of  $\psi$  at x, then for all  $z \in \mathbb{R}^n$ ,

$$
\psi(z) \ge \psi(x) + \langle p, z - x \rangle.
$$

In particular, if  $p \in \partial \psi(x)$  and  $p' \in \partial \psi(x')$ , then

$$
\left\langle p-p',\; x-x'\right\rangle\geq 0.
$$

If  $\psi$  is not differentiable at x, this means that the convex set  $\partial \psi(x)$ is not a single point, so it should contain a line segment  $[p, p'] \subset \mathbb{R}^n$ . For these heuristic explanations, let us  $\hat{p}$  and  $p'$ , and let  $\Sigma$  be the set of all  $x \in \mathbb{R}^n$  such that  $[p, p'] \subset \partial \psi(x)$ . Then  $\langle p - p', x - x' \rangle \ge 0$ for all  $x, x' \in \Sigma$ . By exchanging the roles of p and p', we see that actually  $\langle p - p', x - x' \rangle = 0$ . This implies that  $\Sigma$  is included in a single hyperplane, orthogonal to  $p - p'$ ; in particular its dimension is at most  $n-1$ .

The rigorous argument can be decomposed into six steps. In the sequel,  $\psi$  will stand for a locally subdifferentiable function.

**Step 1:**  $\psi$  is locally semiconvex. Without loss of generality, we may assume that  $\omega(r)/r$  is nondecreasing continuous (otherwise replace  $\omega(r)$ ) by  $\overline{\omega}(r) = r \sup \{\omega(s)/s; s \leq r\}$  which is a nondecreasing continuous function of r); then  $\omega(tr) \leq t \omega(r)$ .

Let  $x_0 \in U$ , let V be a convex neighborhood of  $x_0$  in U. Let  $x, y \in V$ ,  $t \in [0,1]$  and  $p \in \nabla^-\psi((1-t)x + ty)$ . Then

$$
\psi(x) \ge \psi((1-t)x + ty) + \langle t(x-y), p \rangle - t \, \omega(|x-y|); \tag{10.9}
$$

$$
\psi(y) \ge \psi((1-t)x + ty) + \langle (1-t)(y-x), p \rangle - (1-t)\,\omega(|x-y|). \tag{10.10}
$$

Take the linear combination of (10.9) and (10.10) with respective coefficients  $(1-t)$  and t: the result is

$$
\psi((1-t)x + ty) \le (1-t)\psi(x) + t\psi(y) + 2t(1-t)\omega(|x-y|). \tag{10.11}
$$

**Step 2:**  $\psi$  is locally bounded above. Let  $x_0 \in U$ , let  $x_1, \ldots, x_N \in U$ be such that the convex hull C of  $(x_1,...,x_N)$  is a neighborhood of  $x_0$  ( $N = 2n$  will do). Any point of C can be written as  $\sum \alpha_i x_i$  where  $0 \leq \alpha_i \leq i$ ,  $\sum \alpha_i = 1$ . By (10.11) and finite induction,

$$
\psi\left(\sum \alpha_i x_i\right) \leq \sum \alpha_i \psi(x_i) + 2^N \max_{ij} \omega(|x_i - x_j|);
$$

so  $\psi$  is bounded above on C, and therefore in a neighborhood of  $x_0$ .

**Step 3:**  $\psi$  is locally bounded below. Let  $x_0 \in U$ , let V be a neighborhood of  $x_0$  on which  $\psi$  is bounded above, and let  $B = B_r(x_0)$ , where r is such that  $B_r(x_0) \subset V$ . For any  $x \in B$ , let  $y = 2x_0 - x$ ; then  $|x_0 - y| = |x_0 - x| < r$ , so  $y \in B$ , and

$$
\psi(x_0) = \psi\left(\frac{x+y}{2}\right) \le \frac{1}{2} \big[\psi(x) + \psi(y)\big] + \frac{\omega(|x-y|)}{2}.
$$

Since  $\psi(x_0)$  is fixed and  $\psi(y)$  is bounded above, it follows that  $\psi(x)$  is bounded below for  $x \in B$ .

**Step 4:**  $\psi$  is locally Lipschitz. Let  $x_0 \in U$ , let V be a neighborhood of  $x_0$  on which  $|\psi| \leq M < +\infty$ , and let  $r > 0$  be such that  $B_r(x_0) \subset V$ . For any  $y, y' \in B_{r/2}(x_0)$ , we can write  $y' = (1-t)y + tz$ , where  $t = |y - y'|/r$ , so  $z = (y - y')/t + y \in B_r(x_0)$  and  $|y - z| = r$ . Then  $\psi(y') \le (1-t)\,\psi(y) + t\,\psi(z) + 2\,t(1-t)\,\omega(|y-z|),$  so

$$
\frac{\psi(y') - \psi(y)}{|y - y'|} = \frac{\psi(y') - \psi(y)}{t|y - z|} \le \frac{\psi(y) - \psi(z)}{|y - z|} + \frac{2\omega(|y - z|)}{|y - z|}
$$
$$
\le \frac{2M}{r} + \frac{2\omega(r)}{r}.
$$

Thus the ratio  $[\psi(y') - \psi(y)]/|y' - y|$  is uniformly bounded above in  $B_{r/2}(x_0)$ . By symmetry (exchange y and y'), it is also uniformly bounded below, and  $\psi$  is Lipschitz on  $B_{r/2}(x_0)$ .

Step 5:  $\nabla^- \psi$  is continuous. This means that if  $p_k \in \nabla^- \psi(x_k)$  and  $(x_k, p_k) \to (x, p)$  then  $p \in \nabla^- \psi(x)$ . To prove this, it suffices to pass to the limit in the inequality

$$
\psi(z) \geq \psi(x_k) + \langle p_k, z - x_k \rangle - \omega(|z - x_k|).
$$

Step 6:  $\psi$  is differentiable out of an  $(n-1)$ -dimensional set. Indeed, let  $\Sigma$  be the set of points x such that  $\nabla^-\psi(x)$  is not reduced to a single element. Since  $\nabla^- \psi(x)$  is a convex set, for each  $x \in \Sigma$  there is a nontrivial segment  $[p, p'] \subset \nabla^- \psi(x)$ . So

$$
\Sigma = \bigcup_{\ell \in \mathbb{N}} \Sigma^{(\ell)},
$$

where  $\Sigma^{(\ell)}$  is the set of points x such that  $\nabla^-\psi(x)$  contains a segment  $[p, p']$  of length  $1/\ell$  and  $|p| \leq \ell$ . To conclude, it is sufficient to show that each  $\Sigma^{(\ell)}$  is countably  $(n-1)$ -rectifiable, and for that it is sufficient to show that for each  $x \in \Sigma^{(\ell)}$  the dimension of the tangent cone  $T_x \Sigma^{(\ell)}$ is at most  $n - 1$  (Theorem 10.48(i) in the First Appendix).

So let  $x \in \Sigma^{(\ell)}$ , and let  $q \in T_x\Sigma^{(\ell)}$ ,  $q \neq 0$ . By assumption, there is a sequence  $x_k \in \Sigma^{(\ell)}$  such that

$$
\frac{x_k-x}{t_k}\longrightarrow q.
$$

In particular  $|x - x_k|/t_k$  converges to the finite, nonzero limit |q|.

For any  $k \in \mathbb{N}$ , there is a segment  $[p_k, p'_k]$ , of length  $\ell^{-1}$ , that is contained in  $\nabla^- \psi(x_k)$ ; and  $|p_k| \leq \ell, |p'_k| \leq \ell + \ell^{-1}$ . By compactness, up to extraction of a subsequence one has  $x_k \to x$ ,  $p_k \to p$ ,  $p'_k \to p'$ ,  $|p - p'| = \ell^{-1}$ . By continuity of  $\nabla^- \psi$ , both p and p' belong to  $\nabla^- \psi(x)$ . Then the two inequalities

$$
\begin{cases} \psi(x) \ge \psi(x_k) + \langle p'_k, x - x_k \rangle - \omega(|x - x_k|) \\ \psi(x_k) \ge \psi(x) + \langle p, x_k - x \rangle - \omega(|x - x_k|) \end{cases}
$$

combine to yield

$$
\langle p - p'_k, x - x_k \rangle \ge -2\omega(|x - x_k|).
$$

So

$$
\left\langle p-p'_k, \frac{x-x_k}{t_k} \right\rangle \ge -2 \frac{\omega(|x-x_k|)}{|x-x_k|} \frac{|x-x_k|}{t_k}.
$$

Passing to the limit, we find

$$
\langle p-p',q\rangle\geq 0.
$$

But the roles of  $p$  and  $p'$  can be exchanged, so actually

$$
\langle p-p',q\rangle=0.
$$
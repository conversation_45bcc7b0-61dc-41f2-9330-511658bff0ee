# <span id="page-0-0"></span>TD3: <PERSON>position Based Dataset Distillation Method for Sequential Recommendation

[<PERSON><PERSON><PERSON>](https://orcid.org/0009-0001-1039-9735)

<EMAIL> University of Science and Technology of China & State Key Laboratory of Cognitive Intelligence, Hefei, China

[<PERSON><PERSON>](https://orcid.org/0000-0003-2662-3444) <EMAIL> Beijing University of Posts and Telecommunications, Beijing, China [<PERSON><PERSON><PERSON>](https://orcid.org/0009-0005-0853-1089)

<EMAIL> University of Science and Technology of China & State Key Laboratory of Cognitive Intelligence, Hefei, China

> [<PERSON><PERSON>](https://orcid.org/0000-0002-1513-7814) <EMAIL> Rutgers Business School, Newark, NJ, USA

# [Junping Du](https://orcid.org/0000-0002-9402-3806) <EMAIL> Beijing University of Posts and Telecommunications, Beijing, China

# [<PERSON><PERSON>](https://orcid.org/0000-0001-9921-2078)<sup>∗</sup>

<EMAIL> University of Science and Technology of China & State Key Laboratory of Cognitive Intelligence, Hefei, China

> [Xingyu Lou](https://orcid.org/0009-0003-3180-0668) <EMAIL> Sun Yat-sen University, Guangzhou, China

[Enhong Chen](https://orcid.org/0000-0002-4835-4102) <EMAIL> University of Science and Technology of China & State Key Laboratory of Cognitive Intelligence, Hefei, China

# Abstract

In the era of data-centric AI, the focus of recommender systems has shifted from model-centric innovations to data-centric approaches. The success of modern AI models is built on large-scale datasets, but this also results in significant training costs. Dataset distillation has emerged as a key solution, condensing large datasets to accelerate model training while preserving model performance. However, condensing discrete and sequentially correlated user-item interactions, particularly with extensive item sets, presents considerable challenges. This paper introduces TD3, a novel Tucker Decomposition based Dataset Distillation method within a meta-learning framework, designed for sequential recommendation. TD3 distills a fully expressive synthetic sequence summary from original data. To efficiently reduce computational complexity and extract refined latent patterns, Tucker decomposition decouples the summary into four factors: synthetic user latent factor, temporal dynamics latent factor, shared item latent factor, and a relation core that models their interconnections. Additionally, a surrogate objective in bi-level optimization is proposed to align feature spaces extracted from models trained on both original data and synthetic sequence summary beyond the naïve performance matching approach. In the inner-loop, an augmentation technique allows the learner to closely fit the synthetic summary, ensuring an accurate update of it in the outer-loop.

WWW '25, Sydney, NSW, Australia

© 2025 Copyright held by the owner/author(s). Publication rights licensed to ACM. ACM ISBN 979-8-4007-1274-6/25/04 <https://doi.org/10.1145/3696410.3714613>

To accelerate the optimization process and address long dependencies, RaT-BPTT is employed for bi-level optimization. Experiments and analyses on multiple public datasets have confirmed the superiority and cross-architecture generalizability of the proposed designs. Codes are released at [https://github.com/USTC-StarTeam/TD3.](https://github.com/USTC-StarTeam/TD3)

# CCS Concepts

# • Information systems $\rightarrow$ Data mining.

# Keywords

Recommender Systems; Dataset Distillation; Bi-level Optimization

### ACM Reference Format:

Jiaqing Zhang, Mingjia Yin, Hao Wang, Yawen Li, Yuyang Ye, Xingyu Lou, Junping Du, and Enhong Chen. 2025. TD3: Tucker Decomposition Based Dataset Distillation Method for Sequential Recommendation. In Proceedings of the ACM Web Conference 2025 (WWW '25), April 28-May 2, 2025, Sydney, NSW, Australia. ACM, New York, NY, USA, [10](#page-9-0) pages. [https://doi.org/10.1145/](https://doi.org/10.1145/3696410.3714613) [3696410.3714613](https://doi.org/10.1145/3696410.3714613)

## 1 Introduction

To address the persistent challenge of information overload from the Internet, Recommendation Systems (RS) have become essential tools by suggesting personalized content to users [\[13,](#page-8-0) [51,](#page-9-1) [59,](#page-9-2) [66,](#page-9-3) [72,](#page-9-4) [75\]](#page-9-5). Among them, Sequential Recommendation Systems (SRS) capture user preferences in the evolving form through chronological interaction sequences [\[12,](#page-8-1) [19,](#page-8-2) [44,](#page-8-3) [52,](#page-9-6) [63,](#page-9-7) [82\]](#page-9-8). However, as recommendation models become increasingly complex, the primary constraint affecting recommendation performance gradually shifts towards the quantity and quality of recommendation data [\[20\]](#page-8-4), leading to the emergence of data-centric recommendations, as shown in fig. [1.](#page-1-0) Moreover, several emerging AI companies have prioritized data for its numerous benefits, including improved accuracy, faster

<sup>∗</sup>Corresponding author.

Permission to make digital or hard copies of all or part of this work for personal or classroom use is granted without fee provided that copies are not made or distributed for profit or commercial advantage and that copies bear this notice and the full citation on the first page. Copyrights for components of this work owned by others than the author(s) must be honored. Abstracting with credit is permitted. To copy otherwise, or republish, to post on servers or to redistribute to lists, requires prior specific permission and/or a fee. Request <NAME_EMAIL>.

WWW '25, April 28-May 2, 2025, Sydney, NSW, Australia Jiaqing Zhang et al.

<span id="page-1-0"></span>Image /page/1/Figure/1 description: The image displays two distinct processes related to recommender systems. The top process, titled 'Data-Centric Recommender System: Optimizing Recommendation Data', shows a flow from 'Tremendous Datasets' to a 'Settled Model and Distillation Algorithm', and finally to an 'Informative Data Summary'. The bottom process illustrates a different approach, starting with 'Settled Datasets', moving to 'Model Designs and Evaluation Metrics', and concluding with a 'Best Designed Model'.

**Model-Centric Recommender System: Optimizing Recommendation Model**

# Figure 1: Comparison of the data-centric recommender system through the lens of dataset distillation approach with the traditional model-centric recommender system. The key difference lies in their distinct optimization objectives.

deployment, and standardized workflows. These collective initiatives in academia and industry highlight the growing recognition of data-centric approaches as essential for innovation [\[26,](#page-8-5) [36,](#page-8-6) [39,](#page-8-7) [49\]](#page-8-8).

Several initiatives have already been dedicated to the data-centric movement. A notable work launched by [\[71\]](#page-9-9) aims to acquire an informative and generalizable training dataset for sequential recommender systems and asks the participants to iterate on the sequential recommendation dataset regeneration mostly focusing on improving the data quality. Another separate line is dataset distillation (DD) [\[53\]](#page-9-10), which focuses on both data quality and data quantity. Unlike heuristic data pruning methods that directly select data points from original datasets, DD methods are designed to generate novel data points and have emerged as a solution for creating high-quality and informative data summaries. The utility of DD approaches has been witnessed in several fields, including federated learning [\[18,](#page-8-9) [28,](#page-8-10) [54,](#page-9-11) [64\]](#page-9-12), continual learning [\[9,](#page-8-11) [34,](#page-8-12) [69,](#page-9-13) [76\]](#page-9-14), graph neural network [\[5,](#page-8-13) [11,](#page-8-14) [68,](#page-9-15) [77\]](#page-9-16) and recommender systems [\[41,](#page-8-15) [42,](#page-8-16) [50\]](#page-8-17).

Significant progress has been made in DD for non-sequential recommender systems [\[41,](#page-8-15) [50,](#page-8-17) [57,](#page-9-17) [58\]](#page-9-18). Methods like ∞-AE [\[41\]](#page-8-15) and DConRec [\[57\]](#page-9-17) distill user-item interaction matrices, while CGM [\[50\]](#page-8-17) condenses categorical recommendation data in clickthrough rate (CTR) scenarios. Additionally, TF-DCon [\[58\]](#page-9-18) employs large language models (LLMs) to condense user and item content for content-based recommendations. However, applying DD to sequential recommendation systems presents challenges due to several inherent complexities. (1) Maintaining sequential correlations: User-item interactions are sequentially correlated, reflecting the dynamic evolution of user preferences. Existing DD methods generate multiple synthetic interactions independently. Although it is possible to trivially organize these interactions into a sequence, this fails to capture the sequential correlations essential for modeling user behavior over time. (2) Optimization dilemma: In DD, the distilled dataset is typically parameterized as a learnable matrix, enabling fully differentiable distillation through a bi-level optimization process [\[2\]](#page-8-18). In sequential settings, this optimization becomes more difficult because the parameterized dataset size increases with sequence length. The enlarged parameter space further exacerbates convergence issues in the bi-level optimization process.

To address these challenges, we introduce TD3 to efficiently reduce computational complexity and extract streamlined latent patterns by decomposing the summary into four components: (1) Synthetic User Latent Factor (U), which represents synthetic user representations; (2) Temporal Dynamics Latent Factor (T), which captures temporal contextual information; (3) Shared Item Latent Factor (V), which characterizes items within the set and aligns with the item embedding table; and (4) Relation Core (G), which models the interrelationships among the factors. After decomposition, each factor is represented as a two-dimensional tensor, with its size determined by the sequence number, maximum sequence length, and item set size. Additionally, component V shared with the item embedding table does not require learning during distillation, making TD3 suitable for large item sets and long sequences. To address the final challenge, we propose an enhanced bi-level optimization objective to align feature spaces from models trained on both original and synthetic data. During inner-loop training, an augmentation technique allows the learner model to deeply fit the synthetic summary, ensuring accurate updates of it in the outerloop. This approach accelerates convergence and, in conjunction with RaT-BPTT [\[6\]](#page-8-19), minimizes computational costs while ensuring effective distillation. The contributions are concretely summarized:

- We study a novel problem in sequential recommendation: distilling a compressed yet informative synthetic sequence summary that retains essential information from the original dataset.
- We introduce TD3, which employs Tucker decomposition to separate the factors influencing the size of the synthetic summary, thereby reducing computational and storage complexity.
- Augmented learner training in inner-loop ensures precise synthetic data updates and feature space alignment loss is proposed beyond the naïve bi-level optimization objective for a better loss landscape to optimize while minimizing computational costs and preserving long dependencies through RaT-BPTT.
- Empirical studies on public datasets have confirmed the superiority and cross-architecture generalizability of TD3's designs.

# <span id="page-1-1"></span>2 Related Work

Sequential Recommendation (SR) aims to leverage historical sequences to better capture current user intent [\[38\]](#page-8-20). In recent years, there has been rapid advancement in model-centric SR approaches, focusing from Markov chains [\[15\]](#page-8-21) and factorization [\[40\]](#page-8-22) to RNNs [\[16,](#page-8-23) [24\]](#page-8-24), CNNs [\[46,](#page-8-25) [67\]](#page-9-19), and GNNs [\[55,](#page-9-20) [60,](#page-9-21) [65\]](#page-9-22). Models like SASRec [\[19\]](#page-8-2) and BERT4Rec [\[45\]](#page-8-26) leverage self-attention mechanisms to learn the influence of each interaction on target behaviors. Recently, the emergence of LLMs has further enriched SR model design, for instance, [\[14,](#page-8-27) [70\]](#page-9-23) leverage LLMs to uncover latent relationships [\[10\]](#page-8-28), while RecFormer [\[23\]](#page-8-29) represents items as item "sentences", and SAID [\[17\]](#page-8-30) employs LLMs to learn semantically aligned item ID embeddings. With the emergence of the concept of data-centric recommendation, more works shift the focus to recommendation data enhancement. DR4SR [\[71\]](#page-9-9) proposes to regenerate an informative and generalizable training dataset for sequential recommendations. FMLP-Rec [\[83\]](#page-9-24) and HSD [\[74\]](#page-9-25) adopt learnable filters for data denoising. DiffuASR [\[29\]](#page-8-31) proposes a diffusion augmentation for higher quality data generation. ASReP [\[30\]](#page-8-32) focused on generating fabricated data for long-tailed sequences.

<span id="page-2-2"></span>Image /page/2/Figure/1 description: The image illustrates a tensor decomposition process. On the left, a 3D tensor is depicted with dimensions labeled 'Synthetic Users', 'Time Steps', and 'Total Items'. An arrow labeled 'Decomposition' points to the right, where the original tensor is broken down into several components. These components include a 'Core Tensor' in the center, a 'User Tensor' above it, a 'Temporal Tensor' to the left, and an 'Item Embedding' to the right. The 'User Tensor', 'Temporal Tensor', and 'Item Embedding' are represented as 2D grids, suggesting they capture different aspects of the data.

Figure 2: An illustration of Tucker decomposition. The left part shows a three-dimensional synthetic sequence summary, with the third dimension representing the probability distribution over the entire item set. The right part illustrates the tucker decomposition, composed of a core tensor and factor matrices. The user tensor, temporal tensor, and core tensor are the parameters to be learned, while the item tensor shares values with the trained item embedding table.

Dataset Distillation (DD) compresses large training datasets into smaller ones while preserving similar performance [\[8,](#page-8-33) [33\]](#page-8-34). There have been several lines of methods, that prioritize different aspects of information. Performance matching based methods focus on optimizing loss at the final training stage. For example, Farzi [\[42\]](#page-8-16) distills auto-regressive data in latent space to produce a latent data summary and a decoder, although its parameters scale linearly with the vocabulary size and sequence length. ∞-AE [\[41\]](#page-8-15) uses neural tangent kernels (NTKs) to approximate an infinitely wide autoencoder and synthesizes fake users through sampling-based reconstruction, while DConRec [\[57\]](#page-9-17) distills synthetic datasets by sampling useritem pairs from a learnable probabilistic matrix, both tailored for collaborative filtering data in the form of user-item-rating triples. Another line of research focuses on data matching, encouraging synthetic data to replicate the behavior of target data. CGM [\[50\]](#page-8-17), following the gradient matching paradigm [\[79\]](#page-9-26) that mimics the influence on model parameters by matching the gradients of the target and synthetic data in each iteration, optimizes a new form of synthetic data rather than condensing discrete one- or multi-hot data in CTR scenarios. Furthermore, TF-DCon [\[58\]](#page-9-18) utilizes large language models (LLMs) to condense item and user content for content-based recommendations, although this approach is hardly applicable to the contexts of ID-based sequential recommendation.

Tucker Decomposition (TD) decomposes a tensor into a set of factor matrices and a smaller core tensor [\[47\]](#page-8-35). It can be viewed as a kind of principal component analysis approach for high-order tensors. In particular, when the super-diagonal elements in the core tensor of tucker equal 1 and other elements equal 0, tucker decomposition degrades into canonical decomposition [\[62\]](#page-9-27). In three-mode case, A tucker decomposition of a tensor  $X \in \mathbb{R}^{I_1 \times I_2 \times I_3}$  is:

$$
X = G \times_1 \mathbf{A}^{(1)} \times_2 \mathbf{A}^{(2)} \times_3 \mathbf{A}^{(3)} =: [[G; \mathbf{A}^{(1)}, \mathbf{A}^{(2)}, \mathbf{A}^{(3)}]], \quad (1)
$$

where  $\times_n$  indicating the tensor product along the n-th mode, each  $A^{(n)} \in \mathbb{R}^{I_n \times R_n}$  is called the *factor matrix*, and  $\mathcal{G} \in \mathbb{R}^{R_1 \times R_2 \times R_3}$  is the core tensor, show the level of interaction between all factors.

# 3 Methodology

This section introduces TD3, which distills discrete and complex sequential recommendation datasets into fully expressive synthetic sequence summaries in latent space.

# 3.1 Overview

**Notation.** Suppose we are given a large training dataset  $\mathcal{T} \triangleq$  $\{x_i\}_{i=1}^{\vert\mathcal{T}\vert}$ , where  $x_i\triangleq [x_{ij}\in\mathcal{V}] \frac{|x_i|}{j=1}$  is an ordered sequence of items, | with each item  $x_{ij}$  belonging to the set of all possible items  $\mathcal V$ . We denote the user set of the training dataset as  $\mathcal{U}$ , where  $|\mathcal{U}| = |\mathcal{T}|$ . Our goal is to learn a differentiable function  $\Phi_{\theta}$  (i.e. SASRec) with parameters  $\theta$ , which predicts the next item  $x_{i+1}$  given the previous sequence  $x_{1:i}$ . The parameters of this function are optimized by minimizing an empirical loss over the training set :

<span id="page-2-0"></span>
$$
\theta^{\mathcal{T}} = \arg \min_{\theta} \mathcal{L}^{\mathcal{T}}(\theta),
$$
  
$$
\mathcal{L}^{\mathcal{T}}(\theta) \triangleq \mathbb{E}_{\mathbf{x} \sim \mathcal{T}, x_i \sim \mathbf{x}} \left[ \ell^{\mathcal{T}}(\Phi_{\theta}(\mathbf{x}_{1:i}), x_{i+1}) \right],
$$
 (2)

where function  $\ell^{\mathcal{T}}(\cdot, \cdot)$  represents the next item prediction loss, and  $\theta^{\mathcal{T}}$  is the minimizer of  $\mathcal{L}^{\mathcal{T}}$ , which reflects the generalization performance of the model  $\Phi_{\theta} \tau$ . Our objective is to generate a small set of condensed synthetic sequence summary  $S \in \mathbb{R}^{\mu \times \zeta \times |\mathcal{V}|}$ consisting of  $\mu$  fake sequences of maximum length  $\zeta$  and  $|S| \ll |\mathcal{T}|$ . Similar to eq. [\(2\)](#page-2-0), once the condensed set is learned, the parameters  $\theta$  can be trained on this set as follows :

<span id="page-2-1"></span>
$$
\theta^{S} = \arg \min_{\theta} \mathcal{L}^{S}(\theta),
$$
  
$$
\mathcal{L}^{S}(\theta) \triangleq \mathbb{E}_{\tilde{\mathbf{x}} \sim S, \tilde{x}_{i} \sim \tilde{\mathbf{x}}} \left[ t^{S} (\Phi_{\theta}(\psi(\tilde{\mathbf{x}}_{1:i}, \mathbf{E})), \tilde{x}_{i+1}) \right],
$$
 (3)

where  $\tilde{\mathbf{x}}_i \triangleq [$   $\mathcal{S}[i, j, :]$   $\big\rfloor_{j=1}^{\zeta},$   $\ell^{\mathcal{S}}(\cdot, \cdot)$  measures the distance between probability distributions,  $\psi(\cdot, \cdot)$  is matrix product, E is the item embedding table and  $\mathcal{L}^{\mathcal{S}}$  is the generalization performance of  $\Phi_{\theta^{\mathcal{S}}}$ . We wish the generalization performance of  $\Phi_{\theta}$  to be close to  $\Phi_{\theta}$ .

$$
\mathbb{E}_{\mathbf{x}\sim\mathcal{T}, x_i\sim\mathbf{x}} \left[ \ell^{\mathcal{T}}(\Phi_{\theta}(\mathbf{x}_{1:i}), x_{i+1}) \right] \n\approx \mathbb{E}_{\tilde{\mathbf{x}}\sim\mathcal{S}, \tilde{x}_i\sim\tilde{\mathbf{x}}} \left[ \ell^{\mathcal{S}}(\Phi_{\theta}(\psi(\tilde{\mathbf{x}}_{1:i}, \mathbf{E})), \tilde{x}_{i+1}) \right].
$$
\n(4)

As the synthetic set  $S$  is significantly smaller, we expect the optimization in eq. [\(3\)](#page-2-1) to be significantly faster than in eq. [\(2\)](#page-2-0) .

Problem. The objective of achieving comparable generalization performance by training on synthetic data can be formulated in an alternative way. As proposed in [\[53\]](#page-9-10), this can be framed as a meta-learning problem using bi-level optimization. In this approach, the inner-loop trains the learner models on synthetic data, while the *outer-loop* evaluates its quality using  $\ell^{\mathcal{T}}(\cdot, \cdot)$  on the original dataset, updating the synthetic summary via gradient descent. More formally, the bi-level optimization problem can be expressed as :

$$
S^* = \underset{\theta_0 \sim \Theta}{\mathbb{E}} \left[ \mathcal{L}^{\mathcal{T}}(\theta^*) \right],
$$
  
t. 
$$
\theta^* = \underset{\theta}{\text{arg min}} \mathcal{L}^S(\theta | \theta_0).
$$
 (5)

The primary approach for addressing bi-level optimization problems is truncated backpropagation through time (T-BPTT) [\[37,](#page-8-36) [56\]](#page-9-28) in reverse mode. When the inner-loop learner updated uses gradient

s.

#### WWW '25, April 28-May 2, 2025, Sydney, NSW, Australia Jiaqing Zhang et al.

Image /page/3/Figure/2 description: This image illustrates a three-step process for model training. Step 1, 'Model Pretraining,' shows training data being used to create a 'Trained-Model Item-Embedding' and an 'Early Epoch Pretrained Learner Model Pool.' Step 2, 'Synthetic Data Distilling,' takes training data and a 'Synthetic Summary' (composed of 'User Tensor,' 'Core Tensor,' 'Temporal Tensor,' and 'Item Embedding') to train with synthetic data. This step involves 'forward' and 'RaT-BPTT' processes, resulting in 'Test Loss' and 'Feature Space Alignment Loss.' Step 3, 'Model Training,' uses the 'Synthetic Summary' and 'Training Data' to train a model, which is depicted as a neural network. This final step is noted to achieve 'Significantly less training time and memory usage' with 'Comparable Performance' compared to another model shown.

Figure 3: Illustration of TD3. In step 1, the learner is trained to get the best checkpoint for feature space alignment and item embedding for the shared item latent factor, and checkpoints from early epochs are saved to form a pool for initialization in each outer-loop. Step 2 visualizes a single outer-loop step, where the meta-gradient is computed from both the test loss and the feature space alignment loss. Step 3 demonstrates that the distilled summary enables training of other networks with similar performance to models trained on real data, while significantly reducing training time and memory usage.

descent with a learning rate  $\eta$ , the meta-gradient with respect to the distilled sequence summary is obtained as follows :

$$
G = -\eta \frac{\partial \mathcal{L}^{S}(\theta_{T})}{\partial \theta} \sum_{i=T-M}^{T-1} \Pi_{j=i+1}^{T-1} \left[ 1 - \eta \frac{\partial^{2} \mathcal{L}^{S}(\theta_{j})}{\partial \theta^{2}} \right] \frac{\partial^{2} \mathcal{L}^{S}(\theta_{i})}{\partial \theta \partial S}, \tag{6}
$$

where  $T$  represents the total optimization and unrolling steps that we perform in *inner-step* with loss  $\mathcal{L}^{S}(\theta)$ , but T-BPTT only propagates backward through a smaller window of  $M$  steps.

# 3.2 Synthetic Summary Decomposition

The discrete nature of user-item interaction records in sequential recommendation data complicates the direct use of gradient methods to distill an informative summary in the same format as the original data. Inspired by prior research [\[27,](#page-8-37) [32,](#page-8-38) [42\]](#page-8-16), we choose to distill in the latent space. Consequently, we define the synthetic sequence summary as  $S \in \mathbb{R}^{\mu \times \zeta \times |\mathcal{V}|}$ , a three-dimensional probability tensor that contains  $\mu$  synthetic users, each with up to  $\zeta$ interaction records. The third dimension of  $S$  represents the size of the entire item set  $|\mathcal{V}|$ , where  $\mathcal{S}_{ij}$  captures the interaction information of synthetic user  $i$  at position  $j$  by synthesizing the total original item information, with each item weighted differently, ensuring that the summary preserves the critical points.

Considering that the size of S is  $\mu \times \zeta \times |\mathcal{V}|$ , an increase in any of its dimensions leads to substantial growth in the overall tensor size, thereby escalating computational and storage requirements, especially when the original item set is large. Inspired by Tucker decomposition,  $S$  can be decomposed into the products of several smaller factor tensors and a core tensor, where the dimension of each sub-tensor is determined by only a single dimension, as visualized in fig. [2](#page-2-2) and formalized as follows:

<span id="page-3-0"></span>
$$
S = G \times_1 U \times_2 T \times_3 V =: [[G; U, T, V]], \tag{7}
$$

where  $\mathbf{U} \in \mathbb{R}^{\mu \times d_1}$ ,  $\mathbf{T} \in \mathbb{R}^{\zeta \times d_2}$ ,  $\mathbf{V} \in \mathbb{R}^{|\mathcal{V}| \times d_3}$ ,  $\mathbf{G} \in \mathbb{R}^{d_1 \times d_2 \times d_3}$ , with  $\times_n$  indicating the tensor product along the n-th mode and  $d_n \ll |\mathcal{V}|$ . Empirically,  $d_1 = d_2$ . More importantly, **V** is shared with the trained item embedding table, with no parameters needed to be trained.

The advantage of this decomposition lies in its ability to decouple the factors influencing the size of  $S$ , thereby reducing data dimensionality as well as computational and storage complexity while preserving key feature information. This approach enables more efficient processing of high-dimensional data and enhances the understanding of its structure and characteristics.

### 3.3 Enhanced Bi-Level Optimization

Existing methods are computationally expensive for generating synthetic datasets with satisfactory generalizability, as optimizing synthetic data requires differently initialized networks [\[73\]](#page-9-29). To accelerate dataset distillation, we propose 1) augmented learner training, which enables the learner model to effectively fit the synthetic summary, supporting precise and comprehensive updates of synthetic data. Additionally, as mentioned in [\[35,](#page-8-39) [61\]](#page-9-30), bias and poorly conditioned loss landscapes arise from truncated unrolling in TBPTT, we further propose 2) feature space alignment which aligns feature spaces from models trained on both original and synthetic data, combined with 3) random truncated backpropagation through time (RaT-BPTT) proposed by [\[6\]](#page-8-19) to reduce bias in TBPTT and create a more favorable loss landscape for optimization.

3.3.1 Augmented Learner Training. As shown in eq. [\(7\)](#page-3-0), we define the synthetic sequences summary as a three-dimensional probabilistic tensor obtained from the factors of the tucker decomposition and a core matrix via the mode product operation:  $S \in \mathbb{R}^{\mu \times \zeta \times |\mathcal{V}|}$ . Under this settings, we use Kullback-Leibler (KL) Divergence as the loss function in eq. [\(3\)](#page-2-1), and the inner objective is defined as:

$$
\mathbf{x} = S[:, : \zeta, :],
$$

$$
\mathbf{y} = S[:, \zeta, :],
$$

$$
\hat{\mathbf{y}} = \text{Softmax}(\Phi_{\theta_j^S}(\psi(\mathbf{x}, \mathbf{E}))) \quad (8)
$$

$$
\ell^S(\mathbf{y}, \hat{\mathbf{y}}) = \mathcal{D}_{KL}(\mathbf{y} \| \hat{\mathbf{y}}) = \sum_{i=1}^{|V|} y_i \log \frac{y_i}{\hat{y}_i},
$$

where **x** is the input, **y** is the target,  $\Phi_{\theta_i^S}(\cdot)$  is the learner model trained on synthetic data in  $j$ -th step,  $\hat{y}$  is output of the learner model and  $\mathcal{D}_{KL}(\cdot || \cdot)$  is the discrete pointwise KL-divergence.

However, this approach only uses the previous 1 to  $\zeta$  – 1 interactions to predict the  $\zeta$ -th interaction probability. We propose further enhancing the prediction by randomly sampling middle positions, which can significantly improve the diversity of training, strengthen contextual understanding, enhance the model's generalization ability, and reduce reliance on specific sequence patterns. This strategy helps the model capture sequence information more comprehensively, improving its practical application performance.

3.3.2 Feature Space Alignment. We propose an enhancement to the current outer-loop test accuracy objective by integrating a metric that ensures the alignment of feature spaces between models trained on the original dataset and those trained on a synthetic sequence summary. As highlighted in [\[21\]](#page-8-40), the conventional metalearning framework is predominantly concerned with equating the performance of models trained on original data with those trained on synthetic data. However, from the perspective of loss surfaces, this strategy primarily attempts to replicate the local minima of the target data through distilled data [\[22\]](#page-8-41). Despite its initial efficacy, this method faces significant challenges due to the presence of poorly conditioned loss landscapes [\[43\]](#page-8-42). In light of these limitations, our goal is to optimize the synthetic data  $S$  such that the learner  $\Phi_{\theta S}$  trained on them achieves not only comparable generalization performance to  $\Phi_{\theta^{\mathcal{T}}}$  but also converges to a similar solution in the feature space. The enhanced objective can be formulated as:

$$
S^* = \arg \min_{S} \mathbb{E}_{{\theta_0 \sim \Theta}} [\mathcal{L}^{\mathcal{T}}(\theta^*) + \mathcal{L}^{\mathcal{F}}(\theta^*)],
$$
  
s.t.  $\mathcal{L}^{\mathcal{F}}(S, \mathcal{T}; \theta^*) \triangleq \frac{1}{2} \| \Phi_{\theta} \mathcal{T}(\mathcal{T}) - \Phi_{\theta^*}(\mathcal{T}) \|_F^2,$   
 $\mathcal{L}^{\mathcal{T}}(\theta) \triangleq \mathbb{E}_{\mathbf{x} \sim \mathcal{T}, x_i \sim \mathbf{x}} [\ell^{\mathcal{T}}(\Phi_{\theta^*}(\mathbf{x}_{1:i}), x_{i+1})],$   
 $\theta^* = \arg \min_{\theta} \mathcal{L}^S(\theta | \theta_0),$   $(9)$ 

where  $\mathcal{L}^{\mathcal{F}}(\theta^*)$  is the feature space alignment loss with a mean squared error (MSE) by optimizing synthetic summary  $S$  directly. Unlike the most basic meta-learning framework, by ensuring that the feature representations of the synthetic data trained model are closely aligned with those of the original data trained model, we aim to foster a more robust and reliable learning process. This approach not only enhances the model's ability to generalize but also improves its stability across diverse datasets, thereby addressing the inherent challenges posed by poorly conditioned loss landscapes. Through this enhancement, we seek to advance the field of metalearning, paving the way for more effective and efficient training paradigms, going beyond simply matching performance metrics.

3.3.3 Random Truncated Backpropagation Through Time. Training neural networks on distilled data is challenging largely due to the pronounced non-convexity of the optimization process. One common approach to capture long-term dependencies in this context is Backpropagation Through Time (BPTT), although it suffers from slow optimization and excessive memory demands. TBPTT, which limits unrolled steps, is a more efficient alternative. Yet, TBPTT introduces its drawbacks, such as bias from the truncation [\[61\]](#page-9-30) and poorly conditioned loss landscapes, especially with long unrolls [\[48\]](#page-8-43). To address these issues, [\[6\]](#page-8-19) propose the Random Truncated Backpropagation Through Time (RaT-BPTT) method, which

Algorithm 1: Optimization for TD3

| Algorithm 1: Optimization for TBC                                                     |                                                                                                                                                                                             |
|---------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>Input</b> : $\mathcal{T}$ : original dataset; [G, U, T, V]: core tensor and tucker |                                                                                                                                                                                             |
| factors for generating synthetic summary; $\Theta$ : pretrained                       |                                                                                                                                                                                             |
| model parameters; N: total unrolling steps for BPTT; W:                               |                                                                                                                                                                                             |
| truncated window size; $\alpha$ : learning rate for the synthetic                     |                                                                                                                                                                                             |
| data; $\eta$ : learning rate for the learner model;                                   |                                                                                                                                                                                             |
| 1                                                                                     | // Outer loop: update synthetic sequences summary                                                                                                                                           |
| 2                                                                                     | while not converged do                                                                                                                                                                      |
| 3                                                                                     | $\triangleright$ Initialize learner's parameter $\theta_0 \sim \Theta$                                                                                                                      |
| 4                                                                                     | $\triangleright$ Sample a mini-batch of original data $\mathcal{B}^T \sim \mathcal{T}$                                                                                                      |
| 5                                                                                     | $\triangleright$ Uniformly sample the ending unrolling step $M \sim U(W, N)$                                                                                                                |
| 6                                                                                     | // Inner loop: update learner model parameters                                                                                                                                              |
| 7                                                                                     | for $n \leftarrow 1, \dots, M$ do                                                                                                                                                           |
| 8                                                                                     | $\triangleright$ Sample a mini-batch of synthetic user :                                                                                                                                    |
| 9                                                                                     | $\mathcal{B}^U \sim U$                                                                                                                                                                      |
| 10                                                                                    | $\triangleright$ Generate a mini-batch of synthetic summary :                                                                                                                               |
| 11                                                                                    | $\mathcal{B}^S = G \times_1 \mathcal{B}^U \times_2 T \times_3 V$                                                                                                                            |
| 12                                                                                    | // Start Random Truncked Backpropagation Through time                                                                                                                                       |
| 13                                                                                    | if $n = M - W - 1$ then                                                                                                                                                                     |
| 14                                                                                    | $\triangleright$ start accumulating gradients                                                                                                                                               |
| 15                                                                                    | end if                                                                                                                                                                                      |
| 16                                                                                    | $\triangleright$ Update learner's parameter by gradient descent :                                                                                                                           |
| 17                                                                                    | $\theta_n = \theta_{n-1} - \eta \nabla \mathcal{L}^S(\mathcal{B}^S; \theta_{n-1})$                                                                                                          |
| 18                                                                                    | end for                                                                                                                                                                                     |
| 19                                                                                    | $\triangleright$ Compute test loss and feature space alignment loss :                                                                                                                       |
| 20                                                                                    | $\mathcal{L}(\theta_M) = \mathcal{L}^{\mathcal{T}}(\theta_M) + \frac{1}{2} \  \Phi_{\theta^{\mathcal{T}}}(\mathcal{B}^{\mathcal{T}}) - \Phi_{\theta^S_M}(\mathcal{B}^{\mathcal{T}}) \ _F^2$ |
| 21                                                                                    | $\triangleright$ Update synthetic data summary $S = S - \alpha \nabla_S \mathcal{L}(\theta_M)$                                                                                              |
| 22                                                                                    | end while                                                                                                                                                                                   |
| Output: synthetic sequence summary $S$ .                                              |                                                                                                                                                                                             |

<span id="page-4-0"></span>combines randomization with truncation in BPTT. This approach unrolls within a randomly anchored and fixed-size window along the training trajectory and aggregates gradients within this window. The random window ensures that the RaT-BPTT gradient serves as a random subsample of the full BPTT gradient, covering the entire trajectory, while the truncated window improves gradient stability and reduces memory usage. As a result, RaT-BPTT enables faster training and better performance. It can be formulated as follows:

$$
G = -\eta \frac{\partial \mathcal{L}^{S}(\theta_{T})}{\partial \theta} \sum_{i=M-W}^{M-1} \Pi_{j=i+1}^{M-1} \left[ 1 - \eta \frac{\partial^{2} \mathcal{L}^{S}(\theta_{j})}{\partial \theta^{2}} \right] \frac{\partial^{2} \mathcal{L}^{S}(\theta_{i})}{\partial \theta \partial S},
$$
\n(10)

where  $M$  is the random number of total unrolled steps in the inner $loop$ , and  $W$  represents the number of steps included in the backward, with only the final  $W$  steps being used for backpropagation.

Previous studies have demonstrated that diverse models in inner optimization improve robustness of the synthetic data [\[1,](#page-8-44) [78\]](#page-9-31). Moreover, pretrained models significantly enhance dataset distillation by providing better initialization, faster convergence, and higher-quality synthetic data [\[31,](#page-8-45) [42\]](#page-8-16). Building on these insights, we maintain a pool of pretrained learner models from early training epochs, capturing various stages of learning. At each outer step of the distillation process, we randomly select models from this pool to ensure that the training signal remains diverse and representative for optimizing the synthetic dataset. The comprehensive training procedure for our proposed method is detailed in Algorithm [1.](#page-4-0)

<span id="page-5-0"></span>Table 1: Statistical information of experimental datasets.

| Dataset  | # user<br>( \mathcal{U} ) | # item<br>( \mathcal{V} ) | # inter<br>( $\sum_{\mathbf{x}} N_{\mathbf{x}}$ ) | avg.<br>length | sparsity |
|----------|---------------------------|---------------------------|---------------------------------------------------|----------------|----------|
| Magazine | 408                       | 758                       | 2.7k                                              | 6.6            | 99.13%   |
| Epinions | 4739                      | 7998                      | 24.7k                                             | 5.2            | 99.99%   |
| ML-100k  | 944                       | 1683                      | 100k                                              | 106.0          | 93.71%   |
| ML-1M    | 6041                      | 3707                      | 1M                                                | 165.6          | 95.53%   |

# 4 Experiments

In this section, we present and analyze the experiments on four public datasets, aiming to

### 4.1 Settings

Training Datasets. To evaluate the distillation method proposed in this paper, we conduct experiments on four commonly used and publicly available datasets with variable statistics in table [1.](#page-5-0)

- [1](#page-0-0)) Amazon<sup>1</sup> includes Amazon product reviews and metadata infomation. For our empirical study, we mainly focus on the categories of "Magazine\_Subscriptions".
- [2](#page-0-0)) MovieLens<sup>2</sup> is maintained by GroupLens and it contains movie ratings from the MovieLens recommendation service. We use the "ml-100k" and "ml-1m" versions for our experiments.
- [3](#page-0-0))  $Epinions<sup>3</sup>$  was collected by [\[80\]](#page-9-32) from Epinions.com, a popular online consumer review website. It describes consumer reviews and also contains trust relationships amongst users and spans more than a decade, from January 2001 to November 2013.

Evaluation Metrics. We adopt the leave-one-out strategy for evaluation, following prior research [\[3,](#page-8-46) [71](#page-9-9)? ]. For each sequence, the most recent interaction is used for testing, the second for validation, and the rest for training. To expedite evaluation, as in previous studies [\[19\]](#page-8-2), we randomly sample 100 negative items to rank with the ground-truth item, which closely approximates full-ranking results while significantly reducing the computational cost. We assess performance using HR, NDCG, and MRR. HR@k checks if the target item appears within the top-k recommendations, NDCG@k considers the item's rank, and MRR@k computes the average reciprocal rank of the first relevant item, with  $k \in \{5, 10, 20\}$ .

Implementation Details. We implement TD3 using PyTorch and develop recommendation models based on the library of Recbole [\[81\]](#page-9-33). Throughout the distillation process, we use SASRec [\[19\]](#page-8-2) serves as the learner across all datasets, utilizing attention heads  $\in \{1, 2\}$ , layers ∈ {1, 2}, hidden size ∈ {64, 128}, inner size ∈ {64, 128, 256}, and attention dropout probability of 0.5 and hidden dropout probability of 0.2. For magazine and epinions dataset, we set  $d1, d2 \in \{8, 16\},$ while for ml-100k and ml-1m dataset, we set  $d1, d2 \in \{16, 32, 64\}$ . To evaluate the cross-architecture generalization of the proposed TD3, we employ GRU4Rec [\[16\]](#page-8-23), BERT4Rec [\[45\]](#page-8-26), and NARM [\[24\]](#page-8-24) for performance assessment. For all distilled datasets, we apply the Adam optimizer [\[4\]](#page-8-47) for both the inner-loop and outer-loop optimization.

<sup>2</sup><https://grouplens.org/datasets/movielens/>

Jiaqing Zhang et al.

<span id="page-5-1"></span>Table 2: Comparison of TD3 with existing dataset distillation techniques and heuristic sampling based methods.

| Dataset          | Metric             | Sampling              |                      | Distillation         | Full-Data            |       |
|------------------|--------------------|-----------------------|----------------------|----------------------|----------------------|-------|
|                  |                    | Random.               | Longest.             | Farzi                | TD <sub>3</sub>      |       |
|                  | $HR@10$ ↑          | $15.44 \ (\pm 2.68)$  | 19.62 $(\pm 0.31)$   | $41.46 \ (\pm 1.27)$ | $47.87 \ (\pm 1.54)$ | 45.40 |
| Magazine         | $HR@20$ $\uparrow$ | $27.50$ (±1.03)       | $33.66 \ (\pm 2.73)$ | 58.70 $(\pm 0.13)$   | $61.17 \ (\pm 1.83)$ | 56.98 |
| $[30 \times 20]$ | NDCG@10 $\uparrow$ | $7.75 \ (\pm 1.16)$   | $9.58 \ (\pm 0.58)$  | $24.27$ (±0.20)      | $27.90 \ (\pm 0.88)$ | 25.63 |
|                  | NDCG@20 $\uparrow$ | $10.75 \ (\pm 0.77)$  | $13.10 \ (\pm 0.30)$ | $28.65 \ (\pm 0.13)$ | $31.25$ (±0.98)      | 28.57 |
|                  | $HR@10$ ↑          | $10.67$ (±0.90)       | $10.24$ (±0.21)      | $18.99 \ (\pm 0.30)$ | 19.86 $(\pm 0.10)$   | 19.13 |
| Epinions         | $HR@20$ $\uparrow$ | $20.25$ (±1.25)       | $20.01$ (±0.41)      | $29.82 \ (\pm 0.39)$ | $31.06 \ (\pm 0.19)$ | 30.09 |
| $[15 \times 30]$ | NDCG@10 $\uparrow$ | $4.93 \ (\pm 0.36)$   | $4.79 \ (\pm 0.06)$  | $10.38 \ (\pm 0.17)$ | $10.67$ (±0.05)      | 10.25 |
|                  | NDCG@20 $\uparrow$ | $7.31 \ (\pm 0.45)$   | $7.22 \ (\pm 0.11)$  | $13.09 \ (\pm 0.07)$ | $13.49 \ (\pm 0.08)$ | 13.00 |
|                  | $HR@10$ ↑          | $10.85$ (±2.30)       | $13.36 \ (\pm 0.45)$ | $62.92 \ (\pm 1.39)$ | $66.14 \ (\pm 1.16)$ | 68.93 |
| $MI - 100k$      | $HR@20$ $\uparrow$ | $21.49$ (±3.98)       | $25.59$ (±0.64)      | $77.84 \ (\pm 0.30)$ | $81.37 \ (\pm 0.43)$ | 83.78 |
| $[30\times50]$   | NDCG@10 $\uparrow$ | $4.81$ (±1.10)        | $5.97 \ (\pm 0.32)$  | $34.92 \ (\pm 0.71)$ | $38.56 \ (\pm 0.86)$ | 40.97 |
|                  | NDCG@20 $\uparrow$ | $7.48 \ (\pm 1.28)$   | $9.02 \ (\pm 0.36)$  | $38.72 \ (\pm 0.40)$ | 42.44 $(+0.72)$      | 44.76 |
|                  | $HR@10$ ↑          | $15.88 \ (\pm 0.22)$  | $16.60 \ (\pm 0.51)$ | $38.01 \ (\pm 0.98)$ | $70.52~(\pm 0.36)$   | 79.32 |
| $MI - 1M$        | $HR@20$ $\uparrow$ | $28.09$ ( $\pm$ 0.31) | $30.93$ (±0.45)      | $56.10(+1.24)$       | $82.52 \ (\pm 0.25)$ | 87.60 |
| $[200\times50]$  | NDCG@10 $\uparrow$ | $7.40~(\pm 0.11)$     | $7.77 \ (\pm 0.16)$  | $19.85 \ (\pm 0.49)$ | $45.93 \ (\pm 0.37)$ | 58.82 |
|                  | NDCG@20 $\uparrow$ | $10.47~(\pm 0.10)$    | $11.36 \ (\pm 0.14)$ | $24.41 \ (\pm 0.55)$ | $48.97 \ (\pm 0.30)$ | 60.93 |

In the outer-loop, the synthetic sequence summary optimizer is configured with a learning rate  $\alpha \in \{0.01, 0.03\}$  and a weight decay of 0.0001, using a cosine scheduler to adjust the learning rate throughout the process. In the inner-loop, the learner optimizer employs a learning rate  $\eta \in \{0.003, 0.005, 0.01\}$ , with a weight decay of 0.00005. The inner steps are set to 200, using a random truncated window of 40 for backpropagation through time in RaT-BPTT, implemented via the Higher [\[7\]](#page-8-48) package across all datasets.

# 4.2 Overall Performance

We evaluated TD3's performance across various synthetic sequence summary sizes and diverse datasets. In table [3,](#page-6-0) we compare models trained on full original datasets with those using various-sized synthetic sequence summaries. Additionally, table [2](#page-5-1) and fig. [4](#page-7-0) compare TD3's performance with Farzi and heuristic sampling methods: random sampling, which selects sequences uniformly, and longest sampling, which selects sequences in descending order of length. Our findings are as follows: 1) TD3 achieves comparable training performance, even with substantial data compression. This shows that small-batch synthetic summaries distilled from the original dataset effectively capture essential information, preserving data integrity for model training. 2) In datasets such as Magazine and Epinions, models trained on TD3-distilled summaries outperform those trained on the original datasets. This highlights the value of high-quality, smaller datasets over larger, noisier ones, underscoring the importance of data quality in model training. 3) As illustrated in table [2](#page-5-1) and fig. [4,](#page-7-0) TD3 is more sample-efficient than Farzi and heuristic methods, showing superior data utilization.

These results illustrate the transformative potential of data distillation in improving sequential recommendation systems. This approach represents a shift towards a data-centric paradigm in recommender systems, where prioritizing data quantity and quality and strategic compression can create more robust and efficient algorithms, reducing computational costs and storage demands. This evolution paves the way for the next generation of recommendation algorithms, focusing on maximizing value from the minimal data.

 $^1$ <http://snap.stanford.edu/data/amazon/productGraph/categoryFiles/>  $\,$ 

<sup>3</sup>[https://cseweb.ucsd.edu/~jmcauley/datasets.html#social\\_data](https://cseweb.ucsd.edu/~jmcauley/datasets.html#social_data)

| Dataset &<br>Model | Data<br>size                    | HR@5 1                       | HR@10 $\uparrow$             | HR@20 $\uparrow$             | NDCG@5 $\uparrow$             | NDCG@10 $\uparrow$            | NDCG@20 $\uparrow$            | $MRR@5$ ↑                                     | MRR@10↑                       | MRR@20 $\uparrow$             |
|--------------------|---------------------------------|------------------------------|------------------------------|------------------------------|-------------------------------|-------------------------------|-------------------------------|-----------------------------------------------|-------------------------------|-------------------------------|
|                    | $[10 \times 10] \equiv 2.5\%$   | $34.24$ (±0.73)              | $46.72 \ (\pm 0.71)$         | 56.65 $(\pm 0.80)$           | $21.82 \ (\pm 0.18)$          | $25.81 \ (\pm 0.26)$          | $28.33 \ (\pm 0.30)$          | $17.74 \ (\pm 0.21)$                          | $19.36 \ (\pm 0.28)$          | $20.05$ (±0.28)               |
| Magazine           | $[15 \times 15] \equiv 3.7\%$   | $37.11$ (±0.23) <sup>*</sup> | $48.60$ (±0.90) <sup>*</sup> | $60.51$ (±0.76) <sup>*</sup> | $24.14$ (±0.21) <sup>**</sup> | $27.82~(±0.34)$ <sup>**</sup> | 30.82 $(\pm 0.35)^{**}$       | $19.88~(±0.35)$ <sup>**</sup>                 | $21.38$ (±0.34) <sup>**</sup> | $22.20$ (±0.36) <sup>**</sup> |
| &                  | $[25 \times 20] \equiv 6.1\%$   | $34.15 \ (\pm 1.21)$         | $47.45$ $(\pm 0.31)^*$       | $61.00 \ (\pm 1.03)$         | $23.04$ (±0.46) <sup>*</sup>  | $27.36~(±0.35)$ <sup>**</sup> | $30.78 \ (\pm 0.07)$          | $19.39 \left( \pm 0.39 \right)$ <sup>**</sup> | $21.19~(±0.44)$ **            | 22.12 $(\pm 0.36)^{**}$       |
| SASRec             | [30 x 20] $\equiv 7.4\%$        | 34.81 $(\pm 0.47)$           | $47.87 \ (\pm 1.54)$         | 61.17 $(+1.83)$ <sup>*</sup> | $23.62$ (±0.28) <sup>*</sup>  | 27.90~(±0.88)                 | $31.25$ (±0.98) <sup>**</sup> | 19.96 $(\pm 0.53)^{++}$                       | $21.75$ $(+0.77)^*$           | 22.67~(±0.80)                 |
|                    | Full-Data                       | $34.15 \ (\pm 0.65)$         | 45.40 $(\pm 0.76)$           | 56.98 $(\pm 0.23)$           | $21.92 \ (\pm 0.18)$          | $25.63 \text{ } (\pm 0.28)$   | $28.57 \ (\pm 0.14)$          | $17.90 \ (\pm 0.12)$                          | 19.47 $(\pm 0.22)$            | $20.29$ (±0.20)               |
|                    | $[10 \times 50] \equiv 0.2\%$   | $12.07 \ (\pm 0.10)$         | 19.69 $(\pm 0.09)^*$         | $30.51$ (±0.19)              | $8.15 \ (\pm 0.07)$           | $10.59 \ (\pm 0.10)$          | 13.31 $(\pm 0.09)^*$          | $6.86 \ (\pm 0.11)$                           | $7.85 \ (\pm 0.13)$           | $8.60~(\pm 0.12)$             |
| Epinions           | $[15 \times 30] \equiv 0.3\%$   | $12.35 \text{ } (\pm 0.10)$  | 19.86 $(\pm 0.10)^{**}$      | 31.06 $(\pm 0.19)^*$         | $8.27 \ (\pm 0.04)$           | 10.67 $(\pm 0.05)^*$          | $13.49~(±0.08)$ <sup>**</sup> | 6.93 $(\pm 0.09)$                             | $7.91 \ (\pm 0.09)$           | $8.67 \ (\pm 0.10)$           |
| &                  | $[20 \times 20] \equiv 0.4\%$   | $12.26 \ (\pm 0.30)$         | $19.37 \ (\pm 0.26)$         | 30.87 $(\pm 0.19)^*$         | $8.20 \ (\pm 0.19)$           | $10.47 \ (\pm 0.20)$          | 13.38 $(\pm 0.12)^{n}$        | $6.88 \ (\pm 0.19)$                           | $7.80 \ (\pm 0.21)$           | $8.59 \ (\pm 0.19)$           |
| SASRec             | Full-Data                       | $11.83 \text{ (+0.42)}$      | $19.13 \ (\pm 0.16)$         | $30.09$ (±0.27)              | $7.92 \ (\pm 0.29)$           | $10.25$ (±0.19)               | $13.00 \ (\pm 0.08)$          | $6.64 \ (\pm 0.25)$                           | $7.58 \ (\pm 0.21)$           | $8.33 \ (\pm 0.17)$           |
| $ML-100k$          | $[25 \times 150] \equiv 2.6\%$  | $48.57 \ (\pm 0.23)$         | $66.45 \ (\pm 1.27)$         | $81.30 \ (\pm 0.28)$         | $32.64$ (±0.30)               | $38.44 \ (\pm 0.69)$          | 42.20 $(\pm 0.35)$            | $27.39$ (±0.42)                               | $29.80 \ (\pm 0.56)$          | $30.83$ (±0.47)               |
|                    | $[30 \times 50] \equiv 3.2\%$   | 49.84 $(\pm 1.02)$           | $66.14 \ (\pm 1.16)$         | $81.37 \ (\pm 0.44)$         | $33.30$ (±0.31)               | $38.56 \ (\pm 0.86)$          | 42.44 $(\pm 0.72)$            | $27.88 \ (\pm 0.63)$                          | $30.04 \ (\pm 0.85)$          | $31.12 \ (\pm 0.82)$          |
| &<br>SASRec        | $[50 \times 50] \equiv 5.3\%$   | 51.86 $(\pm 0.13)$           | 68.79 $(\pm 0.49)$           | $82.96 \ (\pm 0.36)$         | $35.13 \ (\pm 0.59)$          | 40.62 $(\pm 0.16)$            | 44.21 $(\pm 0.20)$            | $29.63 \ (\pm 0.38)$                          | $31.90 \ (\pm 0.19)$          | 32.89 $(\pm 0.22)$            |
|                    | Full-Data                       | $51.75$ (±1.17)              | 69.46 $(\pm 0.26)$           | $84.37 \ (\pm 0.22)$         | $35.57 \ (\pm 0.60)$          | 41.34 $(\pm 0.49)$            | $45.12 \ (\pm 0.54)$          | $30.24~(\pm 0.70)$                            | $32.65 \ (\pm 0.70)$          | 33.69 $(\pm 0.72)$            |
|                    | $[50 \times 50] \equiv 0.8\%$   | 56.33 $(\pm 0.43)$           | 69.71 $(\pm 0.37)$           | $81.88 \ (\pm 0.34)$         | 41.69 $(+0.24)$               | 46.02 $(\pm 0.17)$            | 49.10 $(\pm 0.14)$            | $36.83$ (±0.20)                               | $38.63 \ (\pm 0.16)$          | 39.47 $(\pm 0.15)$            |
| $ML-1m$            | $[100 \times 50] \equiv 1.7\%$  | 56.68 $(\pm 0.13)$           | $70.42~(\pm 0.20)$           | $82.82 \ (\pm 0.27)$         | 41.42 $(\pm 0.18)$            | 45.87 $(\pm 0.11)$            | 49.02 $(\pm 0.20)$            | $36.37 \ (\pm 0.21)$                          | $38.22 \ (\pm 0.18)$          | $39.08 \ (\pm 0.20)$          |
| &<br>SASRec        | $[200 \times 100] \equiv 3.3\%$ | 62.80 $(\pm 0.31)$           | $74.45$ (±0.17)              | $84.83$ (±0.08)              | 47.82 $(\pm 0.24)$            | $51.61$ (±0.20)               | $54.24$ (±0.18)               | 42.84 $(\pm 0.23)$                            | 44.42 $(\pm 0.21)$            | $45.14 \ (\pm 0.21)$          |
|                    | Full-Data                       | 67.92 $(\pm 0.36)$           | $78.22 \ (\pm 0.28)$         | $86.91 \ (\pm 0.09)$         | 54.02 $(\pm 0.14)$            | 57.37 $(\pm 0.11)$            | 59.57 $(+0.07)$               | 49.38 $(\pm 0.09)$                            | 50.78 $(\pm 0.07)$            | $51.38 \ (\pm 0.07)$          |

<span id="page-6-0"></span>Table 3: Comparison of TD3's performance (%) across various size synthetic data and the full dataset. Bold numbers denote the best-performing distilled summary. Underlined numbers denote that the distilled summary outperforms the full dataset. Superscript \* means improvements are statistically significant with p<0.05, while \*\* means p<0.01.

<span id="page-6-1"></span>Table 4: Wall-clock runtime and storage costs are detailed for each operation. Distillation covers 30 epochs but usually needs less computation. Original data training uses early stopping, while synthetic data training is fixed at 200 or 500 epochs.

| Dataset  | TD3 distillation process |        | Training on original data |        | Training on synthetic data |        |
|----------|--------------------------|--------|---------------------------|--------|----------------------------|--------|
|          | Compute                  | Memory | Compute                   | Memory | Compute                    | Memory |
| Magazine | 21m 28s                  | 742MB  | 1m 13s                    | 666MB  | 25s                        | 534MB  |
| Epinions | 55m 46s                  | 2156MB | 1m 27s                    | 932MB  | 18s                        | 624MB  |
| ML-100k  | 1h 58m 24s               | 3460MB | 5m 47s                    | 1910MB | 14s                        | 604MB  |
| ML-1m    | 2h 26m 59s               | 9832MB | 57m 31s                   | 3482MB | 22s                        | 924MB  |

# 4.3 Time and Memory Analysis

4.3.1 Theoretical Memory Complexity. As discussed in Section [2,](#page-1-1) Farzi [\[42\]](#page-8-16) decomposes synthetic data  $S \in \mathbb{R}^{\mu \times \zeta \times |\mathcal{V}|}$  to a latent summary  $D \in \mathbb{R}^{\mu \times \zeta \times d}$  and a decoder  $M \in \mathbb{R}^{d \times |\mathcal{V}|}$ , where  $d \ll |\mathcal{V}|$ . To highlight the advantages of employing Tucker decomposition for three-dimensional data, we perform a comparative analysis of our proposed approach with that of Farzi. In particular, we investigate the computational footprint associated with the bi-level optimization framework by evaluating memory usage during a single outer-loop step, providing insights into the efficiency gains achieved through our method as follows:

Farzi : 
$$
O(|\Phi| + |\mathcal{B}^{\mathcal{T}}| \cdot \zeta \cdot d_3 + |\mathcal{B}^{\mathcal{S}}| \cdot \zeta \cdot |\mathcal{V}| + \mu \cdot \zeta \cdot d + d \cdot |\mathcal{V}|)
$$
  
TD3 :  $O(|\Phi| + |\mathcal{B}^{\mathcal{T}}| \cdot \zeta \cdot d_3 + |\mathcal{B}^{\mathcal{S}}| \cdot \zeta \cdot |\mathcal{V}| + (\mu + \zeta) \cdot d_1 + d_1^2 \cdot d_3)$ 

where  $|\mathcal{B}^{\mathcal{T}}|$  and  $|\mathcal{B}^{\mathcal{S}}|$  denote the batch size of real and synthetic data, respectively, while  $d_3$  represents the item embeddings' hidden dimension. Additionally,  $d$ ,  $d_1$ , and  $d_3$  are of the same order of magnitude and much smaller than  $|V|$ . When the item set is very large, or the distilled sequence summary requires larger values of  $\mu$ 

and  $\zeta$ , the inequality  $(\mu + \zeta) \cdot d_1 + d_1^2 \cdot d_3 \ll \mu \cdot \zeta \cdot d + d \cdot |\mathcal{V}|$  holds, the method proposed in our work will offer a spatial advantage.

4.3.2 Empirical Computational Complexity. The data distillation process consumes considerable wall-clock time and GPU memory, so we conducted a detailed quantitative analysis of these requirements for both distillation and model training on the original and distilled datasets, as shown in table [4.](#page-6-1) Wall-clock time is reported in single A100 (80GB) GPU hours. While distillation generally takes longer and uses more memory than training on the original data, its cost is often amortizable in real-world scenarios where multiple models must be trained on the same dataset. The amortization, based on the ratios in the table, varies by dataset and distillation scale. Notably, for large datasets, where training time is typically lengthy, training on significantly reduced distilled data can shorten this process by several orders of magnitude. This trade-off substantially decreases future training time, making distillation a one-time cost that yields long-term benefits for various downstream tasks, such as hyperparameter tuning and architecture exploration. Hence, the distillation process and its amortization will be well justified.

<span id="page-7-0"></span>Image /page/7/Figure/1 description: The image contains two plots, one titled "Magazine" and the other titled "ML-100k". Both plots have "Synthetic Summary Size" on the x-axis and "MRR@10" on the y-axis. The "Magazine" plot shows five lines representing different methods: TD3 Distillation (yellow circles), Farzi Distillation (teal triangles), Random Sampling (red inverted triangles), Longest Sampling (blue squares), and Full-Data (orange diamonds). The y-axis ranges from 0 to 25. The "ML-100k" plot also shows the same five lines with the same color and marker scheme. The y-axis for this plot ranges from 0 to 40. In both plots, the x-axis is on a logarithmic scale, with values 0.1 and 1 shown. The Full-Data line is consistently at the top of both plots, around 20 for "Magazine" and 32 for "ML-100k". The other lines show varying performance based on the synthetic summary size.

Figure 4: Illustration of the performance comparison of the TD3 against: Farzi, random sampling and longest sampling.

# 4.4 Cross-Architecture Generalization

Since the synthetic sequence summary is carefully tailored for optimizing a specific learner model, we assess its generalizability across various unseen architectures, as shown in table [5.](#page-7-1) We first distill the Epinions dataset using SASRec [\[19\]](#page-8-2), resulting in a condensed synthetic summary of size  $[50 \times 20]$ . This summary is then used to train several alternative architectures, including GRU4Rec [\[16\]](#page-8-23), which models sequential behavior using Gated Recurrent Units (GRU); NARM [\[25\]](#page-8-49), which enhances GRU4Rec with an attention mechanism to emphasize user intent; and BERT4Rec [\[45\]](#page-8-26), which uses bidirectional self-attention to learn sequence representations. The models trained on the synthetic summary demonstrate strong generalization across diverse architectures, maintaining high predictive performance. In some cases, they even outperform models trained on the original dataset, highlighting the effectiveness of our proposed TD3 method in enabling cross-architecture transferability.

# 4.5 Ablation Studies

To analyze our method's components, we conducted ablation studies on ML-100k. Results for Feature Space Alignment (FSA) and Augmented Learner Training (ALT) are in table [6.](#page-7-2) FSA and ALT complement each other. ALT significantly boosts TD3's performance by enhancing contextual understanding and reducing dependence on specific sequence patterns, improving sequence information capture and data updates in the outer-loop. FSA alone also enhances performance across metrics by strengthening the objective function, aiding convergence to a similar solution in the feature space, and

<span id="page-7-1"></span>Table 5: Evaluation of generalization performance on unseen architectures using a synthetic summary of size  $[50 \times 20]$ distilled from the Epinions dataset via SASRec.

|         | Architecture  |                                |               |               |  |  |
|---------|---------------|--------------------------------|---------------|---------------|--|--|
| Metric  |               | Synthetic Data / Original Data |               |               |  |  |
|         | SASRec        | <b>NARM</b>                    | GRU4Rec       | BERT4Rec      |  |  |
| HR@10   | 19.75 / 19.61 | 19.60 / 19.43                  | 19.11 / 20.25 | 18.98 / 17.14 |  |  |
| HR@20   | 31.30 / 30.90 | 30.55 / 31.10                  | 29.49 / 31.49 | 29.08 / 27.62 |  |  |
| NDCG@10 | 10.61 / 10.45 | 10.67 / 10.25                  | 10.55 / 10.93 | 10.31 / 9.29  |  |  |
| NDCG@20 | 13.51 / 13.28 | 13.43 / 13.18                  | 13.15 / 13.75 | 12.83 / 11.91 |  |  |
| MRR@10  | 7.85 / 7.69   | 7.98 / 7.48                    | 7.96 / 8.13   | 7.70 / 6.93   |  |  |
| MRR@20  | 8.64 / 8.46   | 8.73 / 8.28                    | 8.66 / 8.90   | 8.38 / 7.63   |  |  |

<span id="page-7-2"></span>Table 6: Ablation performance of a model trained on a [50 × 50] synthetic summary distilled from the ML-100k. ✗ indicates a module was not used, while ✓ indicates the opposite.

| Dataset | FSA | ALT | NDCG@5               | NDCG@10              | MRR@5                | MRR@10               |
|---------|-----|-----|----------------------|----------------------|----------------------|----------------------|
| ML-100k | ✕   | ✕   | 33.27 (±0.65)        | 38.80 (±0.51)        | 27.90 (±0.65)        | 30.19 (±0.59)        |
|         | ✕   | ✕   | 34.27 (±0.57)        | 40.06 (±0.33)        | 29.03 (±0.61)        | 31.44 (±0.51)        |
|         | ✕   | ✓   | 34.45 (±0.79)        | 39.75 (±0.27)        | 28.84 (±0.62)        | 31.03 (±0.39)        |
|         | ✓   | ✓   | <b>35.13</b> (±0.59) | <b>40.62</b> (±0.16) | <b>29.63</b> (±0.38) | <b>31.90</b> (±0.19) |

maintaining performance on original and synthetic data. Utilizing both in the inner-loop and outer-loop maximizes distillation benefits.

# 5 Conclusion

In this paper, we propose TD3 which distills a large discrete sequential recommendation dataset into an informative synthetic summary, which is decomposed into four factors inspired by Tucker decomposition in latent space. TD3 offers several advantages, including the decoupling of factors that influence the size of  $S$ , thereby reducing data dimensionality, computational costs, and storage complexity while preserving essential feature information. Additionally, we introduce an enhanced bi-level optimization approach featuring an augmented learner training strategy in the inner-loop, ensuring the learner deeply fits the summary and a feature-space alignment surrogate objective in the outer-loop, ensuring optimal learning of synthetic data parameters. Experiments and analyses confirm the effectiveness and necessity of the proposed designs.

While our work offers significant advantages, it does have certain limitations, notably the computational demands of the bi-level optimization process, which still can be challenging for scaling with larger models and datasets. However, this cost is often offset in scenarios where multiple models need training on the same dataset, substantially reducing training time in the future. Distillation becomes a one-time cost with long-term benefits for various tasks. In future work, we aim to develop a more time-efficient dataset distillation method that scales to larger datasets without sacrificing performance. Additionally, We also intend to use dataset distillation for cross-domain knowledge transfer, allowing the information from one domain to be reused in other domains, thereby enhancing the framework's versatility in different recommendation contexts.

### References

- <span id="page-8-44"></span>[1] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 4750–4759, 2022.
- <span id="page-8-18"></span>[2] Stephan Dempe. Bilevel optimization: theory, algorithms, applications and a bibliography. Bilevel optimization: advances and next challenges, pages 581–672, 2020.
- <span id="page-8-46"></span>[3] Jacob Devlin. Bert: Pre-training of deep bidirectional transformers for language understanding. arXiv preprint arXiv:1810.04805, 2018.
- <span id="page-8-47"></span>[4] P Kingma Diederik. Adam: A method for stochastic optimization. (No Title), 2014.
- <span id="page-8-13"></span>[5] Qizhang Feng, Zhimeng Stephen Jiang, Ruiquan Li, Yicheng Wang, Na Zou, Jiang Bian, and Xia Hu. Fair graph distillation. Advances in Neural Information Processing Systems, 36:80644–80660, 2023.
- <span id="page-8-19"></span>[6] Yunzhen Feng, Shanmukha Ramakrishna Vedantam, and Julia Kempe. Embarrassingly simple dataset distillation. In The Twelfth International Conference on Learning Representations, 2023.
- <span id="page-8-48"></span>[7] Edward Grefenstette, Brandon Amos, Denis Yarats, Phu Mon Htut, Artem Molchanov, Franziska Meier, Douwe Kiela, Kyunghyun Cho, and Soumith Chintala. Generalized inner loop meta-learning. arXiv preprint arXiv:1910.01727, 2019.
- <span id="page-8-33"></span>[8] Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 15793–15803, 2024.
- <span id="page-8-11"></span>[9] Jianyang Gu, Kai Wang, Wei Jiang, and Yang You. Summarizing stream data for memory-restricted online continual learning. arXiv preprint arXiv:2305.16645, 2, 2023.
- <span id="page-8-28"></span>[10] Wei Guo, Hao Wang, Luankang Zhang, Jin Yao Chin, Zhongzhou Liu, Kai Cheng, Qiushi Pan, Yi Quan Lee, Wanqi Xue, Tingjia Shen, et al. Scaling new frontiers: Insights into large recommendation models. arXiv preprint arXiv:2412.00714, 2024.
- <span id="page-8-14"></span>[11] Mridul Gupta, Sahil Manchanda, Sayan Ranu, and Hariprasad Kodamana. Mirage: Model-agnostic graph distillation for graph classification. arXiv preprint arXiv:2310.09486, 2023.
- <span id="page-8-1"></span>[12] Yongqiang Han, Hao Wang, Kefan Wang, Likang Wu, Zhi Li, Wei Guo, Yong Liu, Defu Lian, and Enhong Chen. End4rec: Efficient noise-decoupling for multibehavior sequential recommendation. arXiv preprint arXiv:2403.17603, 2024.
- <span id="page-8-0"></span>[13] Yongqiang Han, Likang Wu, Hao Wang, Guifeng Wang, Mengdi Zhang, Zhi Li, Defu Lian, and Enhong Chen. Guesr: A global unsupervised data-enhancement with bucket-cluster sampling for sequential recommendation. In International Conference on Database Systems for Advanced Applications, pages 286–296. Springer, 2023.
- <span id="page-8-27"></span>[14] Jesse Harte, Wouter Zorgdrager, Panos Louridas, Asterios Katsifodimos, Dietmar Jannach, and Marios Fragkoulis. Leveraging large language models for sequential recommendation. In Proceedings of the 17th ACM Conference on Recommender Systems, pages 1096–1102, 2023.
- <span id="page-8-21"></span>[15] Ruining He and Julian McAuley. Fusing similarity models with markov chains for sparse sequential recommendation. In 2016 IEEE 16th international conference on data mining (ICDM), pages 191–200. IEEE, 2016.
- <span id="page-8-23"></span>[16] Balázs Hidasi, Alexandros Karatzoglou, Linas Baltrunas, and Domonkos Tikk. Session-based recommendations with recurrent neural networks. arXiv preprint arXiv:1511.06939, 2015.
- <span id="page-8-30"></span>[17] Jun Hu, Wenwen Xia, Xiaolu Zhang, Chilin Fu, Weichang Wu, Zhaoxin Huan, Ang Li, Zuoli Tang, and Jun Zhou. Enhancing sequential recommendation via llm-based semantic embedding learning. In Companion Proceedings of the ACM on Web Conference 2024, pages 103–111, 2024.
- <span id="page-8-9"></span>[18] Chun-Yin Huang, Kartik Srinivas, Xin Zhang, and Xiaoxiao Li. Overcoming data and model heterogeneities in decentralized federated learning via synthetic anchors. arXiv preprint arXiv:2405.11525, 2024.
- <span id="page-8-2"></span>[19] Wang-Cheng Kang and Julian McAuley. Self-attentive sequential recommendation. In 2018 IEEE international conference on data mining (ICDM), pages 197-206. IEEE, 2018.
- <span id="page-8-4"></span>[20] Riwei Lai, Li Chen, Rui Chen, and Chi Zhang. A survey on data-centric recommender systems. arXiv preprint arXiv:2401.17878, 2024.
- <span id="page-8-40"></span>[21] Shiye Lei and Dacheng Tao. A comprehensive survey of dataset distillation. IEEE Transactions on Pattern Analysis and Machine Intelligence, 2023.
- <span id="page-8-41"></span>[22] Hao Li, Zheng Xu, Gavin Taylor, Christoph Studer, and Tom Goldstein. Visualizing the loss landscape of neural nets. Advances in neural information processing systems, 31, 2018.
- <span id="page-8-29"></span>[23] Jiacheng Li, Ming Wang, Jin Li, Jinmiao Fu, Xin Shen, Jingbo Shang, and Julian McAuley. Text is all you need: Learning language representations for sequential recommendation. In Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, pages 1258–1267, 2023.
- <span id="page-8-24"></span>[24] Jing Li, Pengjie Ren, Zhumin Chen, Zhaochun Ren, Tao Lian, and Jun Ma. Neural attentive session-based recommendation. In Proceedings of the 2017 ACM on

<span id="page-8-49"></span>Conference on Information and Knowledge Management, pages 1419–1428, 2017. [25] Jing Li, Pengjie Ren, Zhumin Chen, Zhaochun Ren, Tao Lian, and Jun Ma. Neural attentive session-based recommendation. In Proceedings of the 2017 ACM on

- <span id="page-8-5"></span>Conference on Information and Knowledge Management, pages 1419–1428, 2017. [26] Li Li, Jiawei Peng, Huiyi Chen, Chongyang Gao, and Xu Yang. How to configure good in-context sequence for visual question answering. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 26710– 26720, 2024.
- <span id="page-8-37"></span>[27] Yongqi Li and Wenjie Li. Data distillation for text classification. arXiv preprint arXiv:2104.08448, 2021.
- <span id="page-8-10"></span>[28] Ping Liu, Xin Yu, and Joey Tianyi Zhou. Meta knowledge condensation for federated learning. arXiv preprint arXiv:2209.14851, 2022.
- <span id="page-8-31"></span>[29] Qidong Liu, Fan Yan, Xiangyu Zhao, Zhaocheng Du, Huifeng Guo, Ruiming Tang, and Feng Tian. Diffusion augmentation for sequential recommendation. In Proceedings of the 32nd ACM International Conference on Information and Knowledge Management, pages 1576–1586, 2023.
- <span id="page-8-32"></span>[30] Zhiwei Liu, Ziwei Fan, Yu Wang, and Philip S Yu. Augmenting sequential recommendation with pseudo-prior items via reversely pre-training transformer. In Proceedings of the 44th international ACM SIGIR conference on Research and development in information retrieval, pages 1608–1612, 2021.
- <span id="page-8-45"></span>[31] Yao Lu, Xuguang Chen, Yuchen Zhang, Jianyang Gu, Tianle Zhang, Yifan Zhang, Xiaoniu Yang, Qi Xuan, Kai Wang, and Yang You. Can pre-trained models assist in dataset distillation? arXiv preprint arXiv:2310.03295, 2023.
- <span id="page-8-38"></span>[32] Aru Maekawa, Naoki Kobayashi, Kotaro Funakoshi, and Manabu Okumura. Dataset distillation with attention labels for fine-tuning bert. In Proceedings of the 61st Annual Meeting of the Association for Computational Linguistics (Volume 2: Short Papers), pages 119–127, 2023.
- <span id="page-8-34"></span>[33] Aru Maekawa, Satoshi Kosugi, Kotaro Funakoshi, and Manabu Okumura. Dilm: Distilling dataset into language model for text-level dataset distillation. arXiv preprint arXiv:2404.00264, 2024.
- <span id="page-8-12"></span>[34] Wojciech Masarczyk and Ivona Tautkute. Reducing catastrophic forgetting with learning on synthetic data. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops, pages 252–253, 2020.
- <span id="page-8-39"></span>[35] Luke Metz, Niru Maheswaranathan, Jeremy Nixon, Daniel Freeman, and Jascha Sohl-Dickstein. Understanding and correcting pathologies in the training of learned optimizers. In International Conference on Machine Learning, pages 4556–4565. PMLR, 2019.
- <span id="page-8-6"></span>[36] Andrew Ng. Landing ai. Landing AI. Available online: https://landing.ai/(accessed on 8 February 2023), 2023.
- <span id="page-8-36"></span>[37] GV Puskorius and LA Feldkamp. Truncated backpropagation through time and kalman filter training for neurocontrol. In Proceedings of 1994 IEEE International Conference on Neural Networks (ICNN'94), volume 4, pages 2488–2493. IEEE, 1994.
- <span id="page-8-20"></span>[38] Massimo Quadrana, Paolo Cremonesi, and Dietmar Jannach. Sequence-aware recommender systems. ACM computing surveys (CSUR), 51(4):1–36, 2018. [39] Alexander Ratner. Scale ai. Snorkel AI. Available online: https://snorkel.ai/(accessed
- <span id="page-8-7"></span>on 8 February 2023), 2023.
- <span id="page-8-22"></span>[40] Steffen Rendle, Christoph Freudenthaler, and Lars Schmidt-Thieme. Factorizing personalized markov chains for next-basket recommendation. In Proceedings of the 19th international conference on World wide web, pages 811–820, 2010.
- <span id="page-8-15"></span>[41] Noveen Sachdeva, Mehak Dhaliwal, Carole-Jean Wu, and Julian McAuley. Infinite recommendation networks: A data-centric approach. Advances in Neural Information Processing Systems, 35:31292–31305, 2022.
- <span id="page-8-16"></span>[42] Noveen Sachdeva, Zexue He, Wang-Cheng Kang, Jianmo Ni, Derek Zhiyuan Cheng, and Julian McAuley. Farzi data: Autoregressive data distillation. arXiv preprint arXiv:2310.09983, 2023.
- <span id="page-8-42"></span>[43] Noveen Sachdeva and Julian McAuley. Data distillation: A survey. arXiv preprint arXiv:2301.04272, 2023.
- <span id="page-8-3"></span>[44] Tingjia Shen, Hao Wang, Chuhan Wu, Jin Yao Chin, Wei Guo, Yong Liu, Huifeng Guo, Defu Lian, Ruiming Tang, and Enhong Chen. Predictive models in sequential recommendations: Bridging performance laws with data quality insights. arXiv preprint arXiv:2412.00430, 2024.
- <span id="page-8-26"></span>[45] Fei Sun, Jun Liu, Jian Wu, Changhua Pei, Xiao Lin, Wenwu Ou, and Peng Jiang. Bert4rec: Sequential recommendation with bidirectional encoder representations from transformer. In Proceedings of the 28th ACM international conference on information and knowledge management, pages 1441–1450, 2019.
- <span id="page-8-25"></span>[46] Jiaxi Tang and Ke Wang. Personalized top-n sequential recommendation via convolutional sequence embedding. In Proceedings of the eleventh ACM international conference on web search and data mining, pages 565–573, 2018.
- <span id="page-8-35"></span>[47] Ledyard R Tucker et al. The extension of factor analysis to three-dimensional matrices. Contributions to mathematical psychology, 110119:110–182, 1964.
- <span id="page-8-43"></span>[48] Paul Vicol, Luke Metz, and Jascha Sohl-Dickstein. Unbiased gradient estimation in unrolled computation graphs with persistent evolution strategies. In International Conference on Machine Learning, pages 10553–10563. PMLR, 2021.
- <span id="page-8-8"></span>[49] Alexandr Wang. Scale ai. Scale AI. Available online: https://scale.com/(accessed on 8 February 2023), 2023.
- <span id="page-8-17"></span>[50] Cheng Wang, Jiacheng Sun, Zhenhua Dong, Ruixuan Li, and Rui Zhang. Gradient matching for categorical data distillation in ctr prediction. In Proceedings of the 17th ACM Conference on Recommender Systems, pages 161–170, 2023.

- <span id="page-9-1"></span><span id="page-9-0"></span>[51] Hao Wang, Defu Lian, Hanghang Tong, Qi Liu, Zhenya Huang, and Enhong Chen. Hypersorec: Exploiting hyperbolic user and item representations with multiple aspects for social-aware recommendation. ACM Transactions on Information Systems (TOIS), 40(2):1–28, 2021.
- <span id="page-9-6"></span>[52] Shoujin Wang, Qi Zhang, Liang Hu, Xiuzhen Zhang, Yan Wang, and Charu Aggarwal. Sequential/session-based recommendations: Challenges, approaches, applications and opportunities. In Proceedings of the 45th International ACM SIGIR Conference on Research and Development in Information Retrieval, pages 3425–3428, 2022.
- <span id="page-9-10"></span>[53] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. arXiv preprint arXiv:1811.10959, 2018.
- <span id="page-9-11"></span>[54] Yuan Wang, Huazhu Fu, Renuga Kanagavelu, Qingsong Wei, Yong Liu, and Rick Siow Mong Goh. An aggregation-free federated learning for tackling data heterogeneity. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 26233–26242, 2024.
- <span id="page-9-20"></span>[55] Ziyang Wang, Wei Wei, Gao Cong, Xiao-Li Li, Xian-Ling Mao, and Minghui Qiu. Global context enhanced graph neural networks for session-based recommendation. In Proceedings of the 43rd international ACM SIGIR conference on research and development in information retrieval, pages 169–178, 2020.
- <span id="page-9-28"></span>[56] Ronald J Williams and Jing Peng. An efficient gradient-based algorithm for online training of recurrent network trajectories. Neural computation, 2(4):490–501, 1990.
- <span id="page-9-17"></span>[57] Jiahao Wu, Wenqi Fan, Shengcai Liu, Qijiong Liu, Rui He, Qing Li, and Ke Tang. Dataset condensation for recommendation. arXiv preprint arXiv:2310.01038, 2023.
- <span id="page-9-18"></span>[58] Jiahao Wu, Qijiong Liu, Hengchang Hu, Wenqi Fan, Shengcai Liu, Qing Li, Xiao-Ming Wu, and Ke Tang. Leveraging large language models (llms) to empower training-free dataset condensation for content-based recommendation. arXiv preprint arXiv:2310.09874, 2023.
- <span id="page-9-2"></span>[59] Likang Wu, Zhi Zheng, Zhaopeng Qiu, Hao Wang, Hongchao Gu, Tingjia Shen, Chuan Qin, Chen Zhu, Hengshu Zhu, Qi Liu, et al. A survey on large language models for recommendation (2023). URL: https://arxiv. org/abs/2305.19860, 2023.
- <span id="page-9-21"></span>[60] Shu Wu, Yuyuan Tang, Yanqiao Zhu, Liang Wang, Xing Xie, and Tieniu Tan. Session-based recommendation with graph neural networks. In Proceedings of the AAAI conference on artificial intelligence, volume 33, pages 346–353, 2019.
- <span id="page-9-30"></span>[61] Yuhuai Wu, Mengye Ren, Renjie Liao, and Roger Grosse. Understanding shorthorizon bias in stochastic meta-optimization. arXiv preprint arXiv:1803.02021, 2018.
- <span id="page-9-27"></span>[62] Likang Xiao, Richong Zhang, Zijie Chen, and Junfan Chen. Tucker decomposition with frequency attention for temporal knowledge graph completion. In Findings of the Association for Computational Linguistics: ACL 2023, pages 7286–7300, 2023.
- <span id="page-9-7"></span>[63] Wenjia Xie, Hao Wang, Luankang Zhang, Rui Zhou, Defu Lian, and Enhong Chen. Breaking determinism: Fuzzy modeling of sequential recommendation using discrete state space diffusion model. arXiv preprint arXiv:2410.23994, 2024.
- <span id="page-9-12"></span>[64] Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. Feddm: Iterative distribution matching for communication-efficient federated learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pages 16323–16332, 2023.
- <span id="page-9-22"></span>[65] Chengfeng Xu, Pengpeng Zhao, Yanchi Liu, Victor S Sheng, Jiajie Xu, Fuzhen Zhuang, Junhua Fang, and Xiaofang Zhou. Graph contextualized self-attention network for session-based recommendation. In IJCAI, volume 19, pages 3940– 3946, 2019.
- <span id="page-9-3"></span>[66] Xiang Xu, Hao Wang, Wei Guo, Luankang Zhang, Wanshan Yang, Runlong Yu, Yong Liu, Defu Lian, and Enhong Chen. Multi-granularity interest retrieval and refinement network for long-term user behavior modeling in ctr prediction. arXiv preprint arXiv:2411.15005, 2024.
- <span id="page-9-19"></span>[67] An Yan, Shuo Cheng, Wang-Cheng Kang, Mengting Wan, and Julian McAuley. Cosrec: 2d convolutional neural networks for sequential recommendation. In Proceedings of the 28th ACM international conference on information and knowledge

management, pages 2173–2176, 2019.

- <span id="page-9-15"></span>[68] Beining Yang, Kai Wang, Qingyun Sun, Cheng Ji, Xingcheng Fu, Hao Tang, Yang You, and Jianxin Li. Does graph distillation see like vision dataset counterpart? Advances in Neural Information Processing Systems, 36, 2024.
- <span id="page-9-13"></span>[69] Enneng Yang, Li Shen, Zhenyi Wang, Tongliang Liu, and Guibing Guo. An efficient dataset condensation plugin and its application to continual learning. Advances in Neural Information Processing Systems, 36, 2023.
- <span id="page-9-23"></span>[70] Shenghao Yang, Weizhi Ma, Peijie Sun, Qingyao Ai, Yiqun Liu, Mingchen Cai, and Min Zhang. Sequential recommendation with latent relations based on large language model. In Proceedings of the 47th International ACM SIGIR Conference on Research and Development in Information Retrieval, pages 335–344, 2024.
- <span id="page-9-9"></span>[71] Mingjia Yin, Hao Wang, Wei Guo, Yong Liu, Suojuan Zhang, Sirui Zhao, Defu Lian, and Enhong Chen. Dataset regeneration for sequential recommendation. In Proceedings of the 30th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, pages 3954–3965, 2024.
- <span id="page-9-4"></span>[72] Mingjia Yin, Hao Wang, Xiang Xu, Likang Wu, Sirui Zhao, Wei Guo, Yong Liu, Ruiming Tang, Defu Lian, and Enhong Chen. Apgl4sr: A generic framework with adaptive and personalized global collaborative information in sequential recommendation. In Proceedings of the 32nd ACM International Conference on Information and Knowledge Management, pages 3009–3019, 2023. [73] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A compre-
- <span id="page-9-29"></span>hensive review. IEEE Transactions on Pattern Analysis and Machine Intelligence, 2023.
- <span id="page-9-25"></span>[74] Chi Zhang, Yantong Du, Xiangyu Zhao, Qilong Han, Rui Chen, and Li Li. Hierarchical item inconsistency signal learning for sequence denoising in sequential recommendation. In Proceedings of the 31st ACM International Conference on Information & Knowledge Management, pages 2508–2518, 2022.
- <span id="page-9-5"></span>[75] Luankang Zhang, Hao Wang, Suojuan Zhang, Mingjia Yin, Yongqiang Han, Jiaqing Zhang, Defu Lian, and Enhong Chen. A unified framework for adaptive representation enhancement and inversed learning in cross-domain recommendation. In International Conference on Database Systems for Advanced Applications, pages 115–130. Springer, 2024.
- <span id="page-9-14"></span>[76] Xikun Zhang, Dongjin Song, Yushan Jiang, Yixin Chen, and Dacheng Tao. Learning system dynamics without forgetting. arXiv preprint arXiv:2407.00717, 2024.
- <span id="page-9-16"></span>[77] Yuchen Zhang, Tianle Zhang, Kai Wang, Ziyao Guo, Yuxuan Liang, Xavier Bresson, Wei Jin, and Yang You. Navigating complexity: Toward lossless graph condensation via expanding window matching. arXiv preprint arXiv:2402.05011, 2024.
- <span id="page-9-31"></span>[78] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision, pages 6514–6523, 2023.
- <span id="page-9-26"></span>[79] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. arXiv preprint arXiv:2006.05929, 2020.
- <span id="page-9-32"></span>[80] Tong Zhao, Julian McAuley, and Irwin King. Leveraging social connections to improve personalized ranking for collaborative filtering. In Proceedings of the 23rd ACM international conference on conference on information and knowledge management, pages 261–270, 2014.
- <span id="page-9-33"></span>[81] Wayne Xin Zhao, Yupeng Hou, Xingyu Pan, Chen Yang, Zeyu Zhang, Zihan Lin, Jingsen Zhang, Shuqing Bian, Jiakai Tang, Wenqi Sun, Yushuo Chen, Lanling Xu, Gaowei Zhang, Zhen Tian, Changxin Tian, Shanlei Mu, Xinyan Fan, Xu Chen, and Ji-Rong Wen. Recbole 2.0: Towards a more up-to-date recommendation library. In CIKM, pages 4722–4726. ACM, 2022.
- <span id="page-9-8"></span>[82] Xiaoyao Zheng, Xingwang Li, Zhenghua Chen, Liping Sun, Qingying Yu, Liangmin Guo, and Yonglong Luo. Enhanced self-attention mechanism for long and short term sequential recommendation models. IEEE Transactions on Emerging Topics in Computational Intelligence, 2024.
- <span id="page-9-24"></span>[83] Kun Zhou, Hui Yu, Wayne Xin Zhao, and Ji-Rong Wen. Filter-enhanced mlp is all you need for sequential recommendation. In Proceedings of the ACM web conference 2022, pages 2388–2399, 2022.
any subsequent observations of data. In many cases, however, we may have little idea of what form the distribution should take. We may then seek a form of prior distribution, called a *noninformative prior*, which is intended to have as little influence on the posterior distribution as possible (<PERSON><PERSON>, 1946; <PERSON> and <PERSON>, 1973; <PERSON> and <PERSON>, 1994). This is sometimes referred to as 'letting the data speak for themselves'.

If we have a distribution  $p(x|\lambda)$  governed by a parameter  $\lambda$ , we might be tempted to propose a prior distribution  $p(\lambda)$  = const as a suitable prior. If  $\lambda$  is a discrete variable with  $K$  states, this simply amounts to setting the prior probability of each state to  $1/K$ . In the case of continuous parameters, however, there are two potential difficulties with this approach. The first is that, if the domain of  $\lambda$  is unbounded, this prior distribution cannot be correctly normalized because the integral over  $\lambda$ diverges. Such priors are called *improper*. In practice, improper priors can often be used provided the corresponding posterior distribution is *proper*, i.e., that it can be correctly normalized. For instance, if we put a uniform prior distribution over the mean of a Gaussian, then the posterior distribution for the mean, once we have observed at least one data point, will be proper.

A second difficulty arises from the transformation behaviour of a probability density under a nonlinear change of variables, given by (1.27). If a function  $h(\lambda)$ is constant, and we change variables to  $\lambda = \eta^2$ , then  $\hat{h}(\eta) = h(\eta^2)$  will also be constant. However, if we choose the density  $p_{\lambda}(\lambda)$  to be constant, then the density of  $\eta$  will be given, from (1.27), by

$$
p_{\eta}(\eta) = p_{\lambda}(\lambda) \left| \frac{\mathrm{d}\lambda}{\mathrm{d}\eta} \right| = p_{\lambda}(\eta^2) 2\eta \propto \eta \tag{2.231}
$$

and so the density over  $\eta$  will not be constant. This issue does not arise when we use maximum likelihood, because the likelihood function  $p(x|\lambda)$  is a simple function of  $\lambda$  and so we are free to use any convenient parameterization. If, however, we are to choose a prior distribution that is constant, we must take care to use an appropriate representation for the parameters.

Here we consider two simple examples of noninformative priors (Berger, 1985). First of all, if a density takes the form

$$
p(x|\mu) = f(x - \mu)
$$
 (2.232)

then the parameter  $\mu$  is known as a *location parameter*. This family of densities exhibits *translation invariance* because if we shift x by a constant to give  $\hat{x} = x + c$ , then

$$
p(\hat{x}|\hat{\mu}) = f(\hat{x} - \hat{\mu})
$$
\n(2.233)

where we have defined  $\hat{\mu} = \mu + c$ . Thus the density takes the same form in the new variable as in the original one, and so the density is independent of the choice of origin. We would like to choose a prior distribution that reflects this translation invariance property, and so we choose a prior that assigns equal probability mass to

#### **2.4. The Exponential Family**

an interval  $A \le \mu \le B$  as to the shifted interval  $A - c \le \mu \le B - c$ . This implies

$$
\int_{A}^{B} p(\mu) d\mu = \int_{A-c}^{B-c} p(\mu) d\mu = \int_{A}^{B} p(\mu - c) d\mu
$$
 (2.234)

and because this must hold for all choices of A and B, we have

$$
p(\mu - c) = p(\mu) \tag{2.235}
$$

which implies that  $p(\mu)$  is constant. An example of a location parameter would be the mean  $\mu$  of a Gaussian distribution. As we have seen, the conjugate prior distribution for  $\mu$  in this case is a Gaussian  $p(\mu|\mu_0, \sigma_0^2) = \mathcal{N}(\mu|\mu_0, \sigma_0^2)$ , and we obtain a noninformative prior by taking the limit  $\sigma_0^2 \rightarrow \infty$ . Indeed, from (2.141) and (2.142) we see that this gives a posterior distribution over  $\mu$  in which the contributions from the prior vanish.

As a second example, consider a density of the form

$$
p(x|\sigma) = \frac{1}{\sigma} f\left(\frac{x}{\sigma}\right)
$$
 (2.236)

where  $\sigma > 0$ . Note that this will be a normalized density provided  $f(x)$  is correctly *Exercise* 2.59 normalized. The parameter  $\sigma$  is known as a *scale parameter*, and the density exhibits *scale invariance* because if we scale x by a constant to give  $\hat{x} = cx$ , then

$$
p(\hat{x}|\hat{\sigma}) = \frac{1}{\hat{\sigma}} f\left(\frac{\hat{x}}{\hat{\sigma}}\right)
$$
 (2.237)

where we have defined  $\hat{\sigma} = c\sigma$ . This transformation corresponds to a change of scale, for example from meters to kilometers if  $x$  is a length, and we would like to choose a prior distribution that reflects this scale invariance. If we consider an interval  $A \leq \sigma \leq B$ , and a scaled interval  $A/c \leq \sigma \leq B/c$ , then the prior should assign equal probability mass to these two intervals. Thus we have

$$
\int_{A}^{B} p(\sigma) d\sigma = \int_{A/c}^{B/c} p(\sigma) d\sigma = \int_{A}^{B} p\left(\frac{1}{c}\sigma\right) \frac{1}{c} d\sigma \qquad (2.238)
$$

and because this must hold for choices of  $A$  and  $B$ , we have

$$
p(\sigma) = p\left(\frac{1}{c}\sigma\right)\frac{1}{c} \tag{2.239}
$$

and hence  $p(\sigma) \propto 1/\sigma$ . Note that again this is an improper prior because the integral of the distribution over  $0 \le \sigma \le \infty$  is divergent. It is sometimes also convenient to think of the prior distribution for a scale parameter in terms of the density of the log of the parameter. Using the transformation rule (1.27) for densities we see that  $p(\ln \sigma) = \text{const.}$  Thus, for this prior there is the same probability mass in the range  $1 \leq \sigma \leq 10$  as in the range  $10 \leq \sigma \leq 100$  and in  $100 \leq \sigma \leq 1000$ .

An example of a scale parameter would be the standard deviation  $\sigma$  of a Gaussian distribution, after we have taken account of the location parameter  $\mu$ , because

$$
\mathcal{N}(x|\mu, \sigma^2) \propto \sigma^{-1} \exp\left\{- (\tilde{x}/\sigma)^2\right\}
$$
 (2.240)

where  $\tilde{x} = x - \mu$ . As discussed earlier, it is often more convenient to work in terms of the precision  $\lambda = 1/\sigma^2$  rather than  $\sigma$  itself. Using the transformation rule for densities, we see that a distribution  $p(\sigma) \propto 1/\sigma$  corresponds to a distribution over  $\lambda$ of the form  $p(\lambda) \propto 1/\lambda$ . We have seen that the conjugate prior for  $\lambda$  was the gamma *Section 2.3* distribution  $Gam(\lambda|a_0, b_0)$  given by (2.146). The noninformative prior is obtained as the special case  $a_0 = b_0 = 0$ . Again, if we examine the results (2.150) and (2.151) for the posterior distribution of  $\lambda$ , we see that for  $a_0 = b_0 = 0$ , the posterior depends only on terms arising from the data and not from the prior.

# **2.5. Nonparametric Methods**

Throughout this chapter, we have focussed on the use of probability distributions having specific functional forms governed by a small number of parameters whose values are to be determined from a data set. This is called the *parametric* approach to density modelling. An important limitation of this approach is that the chosen density might be a poor model of the distribution that generates the data, which can result in poor predictive performance. For instance, if the process that generates the data is multimodal, then this aspect of the distribution can never be captured by a Gaussian, which is necessarily unimodal.

In this final section, we consider some *nonparametric* approaches to density estimation that make few assumptions about the form of the distribution. Here we shall focus mainly on simple frequentist methods. The reader should be aware, however, that nonparametric Bayesian methods are attracting increasing interest (Walker *et al.*, 1999; Neal, 2000; Müller and Quintana, 2004; Teh et al., 2006).

Let us start with a discussion of histogram methods for density estimation, which we have already encountered in the context of marginal and conditional distributions in Figure 1.11 and in the context of the central limit theorem in Figure 2.6. Here we explore the properties of histogram density models in more detail, focussing on the case of a single continuous variable x. Standard histograms simply partition x into distinct bins of width  $\Delta_i$  and then count the number  $n_i$  of observations of x falling in bin  $i$ . In order to turn this count into a normalized probability density, we simply divide by the total number N of observations and by the width  $\Delta_i$  of the bins to obtain probability values for each bin given by

$$
p_i = \frac{n_i}{N\Delta_i} \tag{2.241}
$$

for which it is easily seen that  $\int p(x) dx = 1$ . This gives a model for the density  $p(x)$  that is constant over the width of each bin, and often the bins are chosen to have the same width  $\Delta_i = \Delta$ .

*Section 2.3*

**Figure 2.24** An illustration of the histogram approach to density estimation, in which a data set of 50 data points is generated from the distribution shown by the green curve. Histogram density estimates, based on (2.241), with a common bin width  $\Delta$  are shown for various values of ∆.

Image /page/3/Figure/2 description: This image displays three histograms, each with a corresponding green curve representing a probability density function. The x-axis for all histograms ranges from 0 to 1, with tick marks at 0, 0.5, and 1. The y-axis for all histograms ranges from 0 to 5. The top histogram is labeled "∆ = 0.04" and shows a bimodal distribution with peaks around 0.2 and 0.8. The middle histogram is labeled "∆ = 0.08" and also shows a bimodal distribution, but the peaks are less pronounced and closer together, with a peak around 0.2 and another broader peak around 0.8. The bottom histogram is labeled "∆ = 0.25" and displays a unimodal distribution with a single broad peak centered around 0.5.

In Figure 2.24, we show an example of histogram density estimation. Here the data is drawn from the distribution, corresponding to the green curve, which is formed from a mixture of two Gaussians. Also shown are three examples of histogram density estimates corresponding to three different choices for the bin width  $\Delta$ . We see that when  $\Delta$  is very small (top figure), the resulting density model is very spiky, with a lot of structure that is not present in the underlying distribution that generated the data set. Conversely, if  $\Delta$  is too large (bottom figure) then the result is a model that is too smooth and that consequently fails to capture the bimodal property of the green curve. The best results are obtained for some intermediate value of ∆ (middle figure). In principle, a histogram density model is also dependent on the choice of edge location for the bins, though this is typically much less significant than the value of  $\Delta$ .

Note that the histogram method has the property (unlike the methods to be discussed shortly) that, once the histogram has been computed, the data set itself can be discarded, which can be advantageous if the data set is large. Also, the histogram approach is easily applied if the data points are arriving sequentially.

In practice, the histogram technique can be useful for obtaining a quick visualization of data in one or two dimensions but is unsuited to most density estimation applications. One obvious problem is that the estimated density has discontinuities that are due to the bin edges rather than any property of the underlying distribution that generated the data. Another major limitation of the histogram approach is its scaling with dimensionality. If we divide each variable in a D-dimensional space into M bins, then the total number of bins will be  $M<sup>D</sup>$ . This exponential scaling *Section 1.4* with *D* is an example of the curse of dimensionality. In a space of high dimensionality, the quantity of data needed to provide meaningful estimates of local probability density would be prohibitive.

> The histogram approach to density estimation does, however, teach us two important lessons. First, to estimate the probability density at a particular location, we should consider the data points that lie within some local neighbourhood of that point. Note that the concept of locality requires that we assume some form of distance measure, and here we have been assuming Euclidean distance. For histograms,

*Section 1.4*

this neighbourhood property was defined by the bins, and there is a natural 'smoothing' parameter describing the spatial extent of the local region, in this case the bin width. Second, the value of the smoothing parameter should be neither too large nor too small in order to obtain good results. This is reminiscent of the choice of model complexity in polynomial curve fitting discussed in Chapter 1 where the degree  $M$ of the polynomial, or alternatively the value  $\alpha$  of the regularization parameter, was optimal for some intermediate value, neither too large nor too small. Armed with these insights, we turn now to a discussion of two widely used nonparametric techniques for density estimation, kernel estimators and nearest neighbours, which have better scaling with dimensionality than the simple histogram model.

### **2.5.1 Kernel density estimators**

Let us suppose that observations are being drawn from some unknown probability density  $p(x)$  in some D-dimensional space, which we shall take to be Euclidean, and we wish to estimate the value of  $p(x)$ . From our earlier discussion of locality, let us consider some small region <sup>R</sup> containing **x**. The probability mass associated with this region is given by

$$
P = \int_{\mathcal{R}} p(\mathbf{x}) \, \mathrm{d}\mathbf{x}.\tag{2.242}
$$

Now suppose that we have collected a data set comprising  $N$  observations drawn from  $p(x)$ . Because each data point has a probability P of falling within R, the total number K of points that lie inside  $\mathcal R$  will be distributed according to the binomial *Section 2.1* distribution

$$
Bin(K|N, P) = \frac{N!}{K!(N-K)!} P^K (1 - P)^{1-K}.
$$
 (2.243)

Using  $(2.11)$ , we see that the mean fraction of points falling inside the region is  $\mathbb{E}[K/N] = P$ , and similarly using (2.12) we see that the variance around this mean is var $[K/N] = P(1 - P)/N$ . For large N, this distribution will be sharply peaked around the mean and so

$$
K \simeq NP. \tag{2.244}
$$

If, however, we also assume that the region  $\mathcal R$  is sufficiently small that the probability density  $p(x)$  is roughly constant over the region, then we have

$$
P \simeq p(\mathbf{x})V \tag{2.245}
$$

where V is the volume of R. Combining  $(2.244)$  and  $(2.245)$ , we obtain our density estimate in the form

$$
p(\mathbf{x}) = \frac{K}{NV}.\tag{2.246}
$$

Note that the validity of (2.246) depends on two contradictory assumptions, namely that the region  $R$  be sufficiently small that the density is approximately constant over the region and yet sufficiently large (in relation to the value of that density) that the number  $K$  of points falling inside the region is sufficient for the binomial distribution to be sharply peaked.

*Section 2.1*

#### **2.5. Nonparametric Methods**

We can exploit the result (2.246) in two different ways. Either we can fix  $K$  and determine the value of  $V$  from the data, which gives rise to the  $K$ -nearest-neighbour technique discussed shortly, or we can fix  $V$  and determine  $K$  from the data, giving rise to the kernel approach. It can be shown that both the  $K$ -nearest-neighbour density estimator and the kernel density estimator converge to the true probability density in the limit  $N \to \infty$  provided V shrinks suitably with N, and K grows with N (Duda and Hart, 1973).

We begin by discussing the kernel method in detail, and to start with we take the region  $\mathcal R$  to be a small hypercube centred on the point **x** at which we wish to determine the probability density. In order to count the number  $K$  of points falling within this region, it is convenient to define the following function

$$
k(\mathbf{u}) = \begin{cases} 1, & |u_i| \leq 1/2, & i = 1, \dots, D, \\ 0, & \text{otherwise} \end{cases}
$$
 (2.247)

which represents a unit cube centred on the origin. The function  $k(\mathbf{u})$  is an example of a *kernel function*, and in this context is also called a *Parzen window*. From (2.247), the quantity  $k((\mathbf{x} - \mathbf{x}_n)/h)$  will be one if the data point  $\mathbf{x}_n$  lies inside a cube of side <sup>h</sup> centred on **x**, and zero otherwise. The total number of data points lying inside this cube will therefore be

$$
K = \sum_{n=1}^{N} k\left(\frac{\mathbf{x} - \mathbf{x}_n}{h}\right).
$$
 (2.248)

Substituting this expression into (2.246) then gives the following result for the estimated density at **x**

$$
p(\mathbf{x}) = \frac{1}{N} \sum_{n=1}^{N} \frac{1}{h^D} k\left(\frac{\mathbf{x} - \mathbf{x}_n}{h}\right)
$$
 (2.249)

where we have used  $V = h^D$  for the volume of a hypercube of side h in D dimensions. Using the symmetry of the function  $k(\mathbf{u})$ , we can now re-interpret this equation, not as a single cube centred on  $x$  but as the sum over  $N$  cubes centred on the N data points  $x_n$ .

As it stands, the kernel density estimator (2.249) will suffer from one of the same problems that the histogram method suffered from, namely the presence of artificial discontinuities, in this case at the boundaries of the cubes. We can obtain a smoother density model if we choose a smoother kernel function, and a common choice is the Gaussian, which gives rise to the following kernel density model

$$
p(\mathbf{x}) = \frac{1}{N} \sum_{n=1}^{N} \frac{1}{(2\pi h^2)^{1/2}} \exp\left\{-\frac{\|\mathbf{x} - \mathbf{x}_n\|^2}{2h^2}\right\}
$$
(2.250)

where h represents the standard deviation of the Gaussian components. Thus our density model is obtained by placing a Gaussian over each data point and then adding up the contributions over the whole data set, and then dividing by  $N$  so that the density is correctly normalized. In Figure 2.25, we apply the model (2.250) to the data

**Figure 2.25** Illustration of the kernel density model (2.250) applied to the same data set used to demonstrate the histogram approach in Figure 2.24. We see that  $h$  acts as a smoothing parameter and that if it is set too small (top panel), the result is a very noisy density model, whereas if it is set too large (bottom panel), then the bimodal nature of the underlying distribution from which the data is generated (shown by the green curve) is washed out. The best density model is obtained for some intermediate value of  $h$  (middle panel).

Image /page/6/Figure/2 description: The image displays three plots stacked vertically, each showing two curves: one blue and one green. The x-axis for all plots ranges from 0 to 1, with tick marks at 0, 0.5, and 1. The y-axis for all plots ranges from 0 to 5. Each plot is labeled with a value of 'h'. The top plot, labeled 'h = 0.005', shows the blue curve as a series of sharp, narrow peaks concentrated around certain values between 0 and 1, while the green curve is a smoother, broader distribution. The middle plot, labeled 'h = 0.07', shows both the blue and green curves as smoother, bell-shaped distributions, with the green curve peaking slightly before the blue curve. The bottom plot, labeled 'h = 0.2', also shows both curves as smooth, bell-shaped distributions, but with a wider spread than the middle plot, and the green curve peaks at a lower value on the x-axis than the blue curve.

set used earlier to demonstrate the histogram technique. We see that, as expected, the parameter  $h$  plays the role of a smoothing parameter, and there is a trade-off between sensitivity to noise at small  $h$  and over-smoothing at large  $h$ . Again, the optimization of  $h$  is a problem in model complexity, analogous to the choice of bin width in histogram density estimation, or the degree of the polynomial used in curve fitting.

We can choose any other kernel function  $k(\mathbf{u})$  in (2.249) subject to the conditions

$$
k(\mathbf{u}) \geqslant 0, \tag{2.251}
$$

$$
\int k(\mathbf{u}) \, \mathrm{d}\mathbf{u} = 1 \tag{2.252}
$$

which ensure that the resulting probability distribution is nonnegative everywhere and integrates to one. The class of density model given by (2.249) is called a kernel density estimator, or *Parzen* estimator. It has a great merit that there is no computation involved in the 'training' phase because this simply requires storage of the training set. However, this is also one of its great weaknesses because the computational cost of evaluating the density grows linearly with the size of the data set.

## **2.5.2 Nearest-neighbour methods**

One of the difficulties with the kernel approach to density estimation is that the parameter  $h$  governing the kernel width is fixed for all kernels. In regions of high data density, a large value of h may lead to over-smoothing and a washing out of structure that might otherwise be extracted from the data. However, reducing  $h$  may lead to noisy estimates elsewhere in data space where the density is smaller. Thus the optimal choice for  $h$  may be dependent on location within the data space. This issue is addressed by nearest-neighbour methods for density estimation.

We therefore return to our general result  $(2.246)$  for local density estimation, and instead of fixing  $V$  and determining the value of  $K$  from the data, we consider a fixed value of  $K$  and use the data to find an appropriate value for  $V$ . To do this, we consider a small sphere centred on the point **x** at which we wish to estimate the

**Figure 2.26** Illustration of K-nearest-neighbour density estimation using the same data set as in Figures 2.25 and 2.24. We see that the parameter  $K$  governs the degree of smoothing, so that a small value of  $K$  leads to a very noisy density model (top panel), whereas a large value (bottom panel) smoothes out the bimodal nature of the true distribution (shown by the green curve) from which the data set was generated.

Image /page/7/Figure/2 description: This image contains three plots stacked vertically. Each plot has a y-axis ranging from 0 to 5 and an x-axis ranging from 0 to 1. The top plot is labeled "K = 1" and shows a blue line with many sharp peaks and valleys, and a smooth green line that peaks around x=0.5. The middle plot is labeled "K = 5" and shows a blue line with fewer, broader peaks and valleys than the top plot, and a smooth green line that peaks around x=0.5. The bottom plot is labeled "K = 30" and shows a blue line that is smoother and has a single broad peak around x=0.7, and a smooth green line that peaks around x=0.5. The y-axis labels are repeated for each plot, showing values 0 and 5.

density  $p(x)$ , and we allow the radius of the sphere to grow until it contains precisely K data points. The estimate of the density  $p(x)$  is then given by (2.246) with V set to the volume of the resulting sphere. This technique is known as K *nearest neighbours* and is illustrated in Figure 2.26, for various choices of the parameter  $K$ , using the same data set as used in Figure 2.24 and Figure 2.25. We see that the value of  $K$ now governs the degree of smoothing and that again there is an optimum choice for K that is neither too large nor too small. Note that the model produced by  $K$  nearest *Exercise 2.61* neighbours is not a true density model because the integral over all space diverges.

> We close this chapter by showing how the  $K$ -nearest-neighbour technique for density estimation can be extended to the problem of classification. To do this, we apply the K-nearest-neighbour density estimation technique to each class separately and then make use of Bayes' theorem. Let us suppose that we have a data set comprising  $N_k$  points in class  $\mathcal{C}_k$  with N points in total, so that  $\sum_k N_k = N$ . If we wish to classify a new point **x**, we draw a sphere centred on **x** containing precisely  $K$  points irrespective of their class. Suppose this sphere has volume  $V$  and contains  $K_k$  points from class  $\mathcal{C}_k$ . Then (2.246) provides an estimate of the density associated with each class

$$
p(\mathbf{x}|\mathcal{C}_k) = \frac{K_k}{N_k V}.\tag{2.253}
$$

Similarly, the unconditional density is given by

$$
p(\mathbf{x}) = \frac{K}{NV} \tag{2.254}
$$

while the class priors are given by

$$
p(\mathcal{C}_k) = \frac{N_k}{N}.\tag{2.255}
$$

We can now combine  $(2.253)$ ,  $(2.254)$ , and  $(2.255)$  using Bayes' theorem to obtain the posterior probability of class membership

$$
p(C_k|\mathbf{x}) = \frac{p(\mathbf{x}|C_k)p(C_k)}{p(\mathbf{x})} = \frac{K_k}{K}.
$$
\n(2.256)

*Exercise 2.61*

**Figure 2.27** (a) In the  $K$ -nearestneighbour classifier, a new point, shown by the black diamond, is classified according to the majority class membership of the  $K$  closest training data points, in this case  $K =$ 3. (b) In the nearest-neighbour  $(K = 1)$  approach to classification, the resulting decision boundary is composed of hyperplanes that form perpendicular bisectors of pairs of points from different classes.

Image /page/8/Figure/2 description: The image displays two plots, labeled (a) and (b), on a white background. Both plots have an x1 and x2 axis, representing a 2D coordinate system. In plot (a), there is a red dot and a blue dot. A green line segment connects the red dot to a black diamond shape. In plot (b), there is also a red dot and a blue dot. A jagged green line starts near the bottom left of the plot and moves upwards and to the right, passing near the red dot. The blue dots in both plots are positioned at the origin of the x1 axis.

If we wish to minimize the probability of misclassification, this is done by assigning the test point **x** to the class having the largest posterior probability, corresponding to the largest value of  $K_k/K$ . Thus to classify a new point, we identify the K nearest points from the training data set and then assign the new point to the class having the largest number of representatives amongst this set. Ties can be broken at random. The particular case of  $K = 1$  is called the *nearest-neighbour* rule, because a test point is simply assigned to the same class as the nearest point from the training set. These concepts are illustrated in Figure 2.27.

In Figure 2.28, we show the results of applying the  $K$ -nearest-neighbour algorithm to the oil flow data, introduced in Chapter 1, for various values of  $K$ . As expected, we see that  $K$  controls the degree of smoothing, so that small  $K$  produces many small regions of each class, whereas large  $K$  leads to fewer larger regions.

Image /page/8/Figure/5 description: The image displays three scatter plots side-by-side, each illustrating a decision boundary for a K-Nearest Neighbors classifier with different values of K. The x-axis for all plots is labeled 'x6' and ranges from 0 to 2. The y-axis for all plots is labeled 'x7' and ranges from 0 to 2. Each plot shows data points colored blue, red, and green, representing different classes. The background of each plot is colored to indicate the predicted class for different regions of the feature space. The first plot is for K=1, the second for K=3, and the third for K=31. The plots show how the decision boundaries become smoother as K increases.

**Figure 2.28** Plot of 200 data points from the oil data set showing values of  $x_6$  plotted against  $x_7$ , where the red, green, and blue points correspond to the 'laminar', 'annular', and 'homogeneous' classes, respectively. Also shown are the classifications of the input space given by the  $K$ -nearest-neighbour algorithm for various values of  $K$ .

An interesting property of the nearest-neighbour  $(K = 1)$  classifier is that, in the limit  $N \to \infty$ , the error rate is never more than twice the minimum achievable error rate of an optimal classifier, i.e., one that uses the true class distributions (Cover and Hart, 1967) .

As discussed so far, both the K-nearest-neighbour method, and the kernel density estimator, require the entire training data set to be stored, leading to expensive computation if the data set is large. This effect can be offset, at the expense of some additional one-off computation, by constructing tree-based search structures to allow (approximate) near neighbours to be found efficiently without doing an exhaustive search of the data set. Nevertheless, these nonparametric methods are still severely limited. On the other hand, we have seen that simple parametric models are very restricted in terms of the forms of distribution that they can represent. We therefore need to find density models that are very flexible and yet for which the complexity of the models can be controlled independently of the size of the training set, and we shall see in subsequent chapters how to achieve this.

# **Exercises**

**2.1** ( $\star$ ) **www** Verify that the Bernoulli distribution (2.2) satisfies the following properties

$$
\sum_{x=0}^{1} p(x|\mu) = 1 \tag{2.257}
$$

$$
\mathbb{E}[x] = \mu \tag{2.258}
$$

$$
var[x] = \mu(1 - \mu). \tag{2.259}
$$

Show that the entropy  $H[x]$  of a Bernoulli distributed random binary variable x is given by

$$
H[x] = -\mu \ln \mu - (1 - \mu) \ln(1 - \mu).
$$
 (2.260)

**2.2**  $(\star \star)$  The form of the Bernoulli distribution given by (2.2) is not symmetric between the two values of  $x$ . In some situations, it will be more convenient to use an equivalent formulation for which  $x \in \{-1, 1\}$ , in which case the distribution can be written

$$
p(x|\mu) = \left(\frac{1-\mu}{2}\right)^{(1-x)/2} \left(\frac{1+\mu}{2}\right)^{(1+x)/2}
$$
 (2.261)

where  $\mu \in [-1, 1]$ . Show that the distribution (2.261) is normalized, and evaluate its mean, variance, and entropy.

**2.3**  $(\star \star)$  **www** In this exercise, we prove that the binomial distribution (2.9) is normalized. First use the definition  $(2.10)$  of the number of combinations of m identical objects chosen from a total of  $N$  to show that

$$
\binom{N}{m} + \binom{N}{m-1} = \binom{N+1}{m}.
$$
\n(2.262)

Use this result to prove by induction the following result

$$
(1+x)^N = \sum_{m=0}^{N} \binom{N}{m} x^m
$$
\n(2.263)

which is known as the *binomial theorem*, and which is valid for all real values of x. Finally, show that the binomial distribution is normalized, so that

$$
\sum_{m=0}^{N} \binom{N}{m} \mu^m (1-\mu)^{N-m} = 1
$$
\n(2.264)

which can be done by first pulling out a factor  $(1 - \mu)^N$  out of the summation and then making use of the binomial theorem.

- **2.4**  $(\star \star)$  Show that the mean of the binomial distribution is given by (2.11). To do this, differentiate both sides of the normalization condition (2.264) with respect to  $\mu$  and then rearrange to obtain an expression for the mean of  $n$ . Similarly, by differentiating (2.264) twice with respect to  $\mu$  and making use of the result (2.11) for the mean of the binomial distribution prove the result (2.12) for the variance of the binomial.
- **2.5**  $(\star \star)$  **www** In this exercise, we prove that the beta distribution, given by (2.13), is correctly normalized, so that (2.14) holds. This is equivalent to showing that

$$
\int_0^1 \mu^{a-1} (1 - \mu)^{b-1} d\mu = \frac{\Gamma(a)\Gamma(b)}{\Gamma(a+b)}.
$$
 (2.265)

From the definition (1.141) of the gamma function, we have

$$
\Gamma(a)\Gamma(b) = \int_0^\infty \exp(-x)x^{a-1} dx \int_0^\infty \exp(-y)y^{b-1} dy.
$$
 (2.266)

Use this expression to prove (2.265) as follows. First bring the integral over  $\gamma$  inside the integrand of the integral over x, next make the change of variable  $t = y + x$ where x is fixed, then interchange the order of the x and t integrations, and finally make the change of variable  $x = t\mu$  where t is fixed.

**2.6**  $(\star)$  Make use of the result (2.265) to show that the mean, variance, and mode of the beta distribution (2.13) are given respectively by

$$
\mathbb{E}[\mu] = \frac{a}{a+b} \tag{2.267}
$$

$$
\text{var}[\mu] = \frac{ab}{(a+b)^2(a+b+1)} \tag{2.268}
$$

$$
mode[\mu] = \frac{a-1}{a+b-2}.
$$
 (2.269)

- **2.7**  $(\star \star)$  Consider a binomial random variable x given by (2.9), with prior distribution for  $\mu$  given by the beta distribution (2.13), and suppose we have observed m occurrences of  $x = 1$  and l occurrences of  $x = 0$ . Show that the posterior mean value of x lies between the prior mean and the maximum likelihood estimate for  $\mu$ . To do this, show that the posterior mean can be written as  $\lambda$  times the prior mean plus  $(1 - \lambda)$ times the maximum likelihood estimate, where  $0 \le \lambda \le 1$ . This illustrates the concept of the posterior distribution being a compromise between the prior distribution and the maximum likelihood solution.
- **2.8** ( $\star$ ) Consider two variables x and y with joint distribution  $p(x, y)$ . Prove the following two results

$$
\mathbb{E}[x] = \mathbb{E}_y [\mathbb{E}_x [x|y]] \qquad (2.270)
$$

$$
\text{var}[x] = \mathbb{E}_y \left[ \text{var}_x[x|y] \right] + \text{var}_y \left[ \mathbb{E}_x[x|y] \right]. \tag{2.271}
$$

Here  $\mathbb{E}_x[x|y]$  denotes the expectation of x under the conditional distribution  $p(x|y)$ , with a similar notation for the conditional variance.

**2.9**  $(\star \star \star)$  **www** . In this exercise, we prove the normalization of the Dirichlet distribution (2.38) using induction. We have already shown in Exercise 2.5 that the beta distribution, which is a special case of the Dirichlet for  $M = 2$ , is normalized. We now assume that the Dirichlet distribution is normalized for  $M - 1$  variables and prove that it is normalized for  $M$  variables. To do this, consider the Dirichlet distribution over M variables, and take account of the constraint  $\sum_{k=1}^{M} \mu_k = 1$  by eliminating  $\mu_M$ , so that the Dirichlet is written

$$
p_M(\mu_1, \dots, \mu_{M-1}) = C_M \prod_{k=1}^{M-1} \mu_k^{\alpha_k - 1} \left( 1 - \sum_{j=1}^{M-1} \mu_j \right)^{\alpha_M - 1}
$$
 (2.272)

and our goal is to find an expression for  $C_M$ . To do this, integrate over  $\mu_{M-1}$ , taking care over the limits of integration, and then make a change of variable so that this integral has limits 0 and 1. By assuming the correct result for  $C_{M-1}$  and making use of (2.265), derive the expression for  $C_M$ .

**2.10** ( $\star\star$ ) Using the property  $\Gamma(x+1) = x\Gamma(x)$  of the gamma function, derive the following results for the mean, variance, and covariance of the Dirichlet distribution given by  $(2.38)$ 

$$
\mathbb{E}[\mu_j] = \frac{\alpha_j}{\alpha_0} \tag{2.273}
$$

$$
\text{var}[\mu_j] = \frac{\alpha_j(\alpha_0 - \alpha_j)}{\alpha_0^2(\alpha_0 + 1)} \tag{2.274}
$$

$$
cov[\mu_j \mu_l] = -\frac{\alpha_j \alpha_l}{\alpha_0^2 (\alpha_0 + 1)}, \qquad j \neq l \tag{2.275}
$$

where  $\alpha_0$  is defined by (2.39).

**2.11** ( $\star$ ) **www** By expressing the expectation of  $\ln \mu_j$  under the Dirichlet distribution (2.38) as a derivative with respect to  $\alpha_j$ , show that

$$
\mathbb{E}[\ln \mu_j] = \psi(\alpha_j) - \psi(\alpha_0)
$$
 (2.276)

where  $\alpha_0$  is given by (2.39) and

$$
\psi(a) \equiv \frac{d}{da} \ln \Gamma(a) \tag{2.277}
$$

is the *digamma* function.

**2.12** ( $\star$ ) The uniform distribution for a continuous variable x is defined by

$$
U(x|a,b) = \frac{1}{b-a}, \qquad a \leq x \leq b. \tag{2.278}
$$

Verify that this distribution is normalized, and find expressions for its mean and variance.

- **2.13**  $(\star \star)$  Evaluate the Kullback-Leibler divergence (1.113) between two Gaussians  $p(\mathbf{x}) = \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma})$  and  $q(\mathbf{x}) = \mathcal{N}(\mathbf{x}|\mathbf{m}, \mathbf{L})$ .
- **2.14**  $(\star \star)$  **www** This exercise demonstrates that the multivariate distribution with maximum entropy, for a given covariance, is a Gaussian. The entropy of a distribution  $p(\mathbf{x})$  is given by

$$
H[\mathbf{x}] = -\int p(\mathbf{x}) \ln p(\mathbf{x}) \, \mathrm{d}\mathbf{x}.\tag{2.279}
$$

We wish to maximize  $H[x]$  over all distributions  $p(x)$  subject to the constraints that  $p(x)$  be normalized and that it have a specific mean and covariance, so that

$$
\int p(\mathbf{x}) \, \mathrm{d}\mathbf{x} = 1 \tag{2.280}
$$

$$
\int p(\mathbf{x})\mathbf{x} \, \mathrm{d}\mathbf{x} = \boldsymbol{\mu} \tag{2.281}
$$

$$
\int p(\mathbf{x})(\mathbf{x} - \boldsymbol{\mu})(\mathbf{x} - \boldsymbol{\mu})^{\mathrm{T}} d\mathbf{x} = \boldsymbol{\Sigma}.
$$
 (2.282)

By performing a variational maximization of (2.279) and using Lagrange multipliers to enforce the constraints (2.280), (2.281), and (2.282), show that the maximum likelihood distribution is given by the Gaussian (2.43).

**2.15** ( $\star\star$ ) Show that the entropy of the multivariate Gaussian  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma})$  is given by

$$
H[\mathbf{x}] = \frac{1}{2} \ln |\mathbf{\Sigma}| + \frac{D}{2} (1 + \ln(2\pi))
$$
 (2.283)

where <sup>D</sup> is the dimensionality of **x**.

**2.16**  $(\star \star \star)$  **www** Consider two random variables  $x_1$  and  $x_2$  having Gaussian distributions with means  $\mu_1, \mu_2$  and precisions  $\tau_1, \tau_2$  respectively. Derive an expression for the differential entropy of the variable  $x = x_1 + x_2$ . To do this, first find the distribution of  $x$  by using the relation

$$
p(x) = \int_{-\infty}^{\infty} p(x|x_2)p(x_2) dx_2
$$
 (2.284)

and completing the square in the exponent. Then observe that this represents the convolution of two Gaussian distributions, which itself will be Gaussian, and finally make use of the result (1.110) for the entropy of the univariate Gaussian.

- **2.17**  $(\star)$  **www** Consider the multivariate Gaussian distribution given by (2.43). By writing the precision matrix (inverse covariance matrix)  $\Sigma^{-1}$  as the sum of a symmetric and an anti-symmetric matrix, show that the anti-symmetric term does not appear in the exponent of the Gaussian, and hence that the precision matrix may be taken to be symmetric without loss of generality. Because the inverse of a symmetric matrix is also symmetric (see Exercise 2.22), it follows that the covariance matrix may also be chosen to be symmetric without loss of generality.
- **2.18**  $(\star \star \star)$  Consider a real, symmetric matrix  $\Sigma$  whose eigenvalue equation is given by (2.45). By taking the complex conjugate of this equation and subtracting the original equation, and then forming the inner product with eigenvector  $\mathbf{u}_i$ , show that the eigenvalues  $\lambda_i$  are real. Similarly, use the symmetry property of  $\Sigma$  to show that two eigenvectors  $\mathbf{u}_i$  and  $\mathbf{u}_j$  will be orthogonal provided  $\lambda_j \neq \lambda_j$ . Finally, show that without loss of generality, the set of eigenvectors can be chosen to be orthonormal, so that they satisfy (2.46), even if some of the eigenvalues are zero.
- **2.19** ( $\star\star$ ) Show that a real, symmetric matrix  $\Sigma$  having the eigenvector equation (2.45) can be expressed as an expansion in the eigenvectors, with coefficients given by the eigenvalues, of the form (2.48). Similarly, show that the inverse matrix  $\Sigma^{-1}$  has a representation of the form (2.49).
- **2.20**  $(\star \star)$  **www** A positive definite matrix  $\Sigma$  can be defined as one for which the quadratic form

$$
\mathbf{a}^{\mathrm{T}}\boldsymbol{\Sigma}\mathbf{a} \tag{2.285}
$$

is positive for any real value of the vector **a**. Show that a necessary and sufficient condition for  $\Sigma$  to be positive definite is that all of the eigenvalues  $\lambda_i$  of  $\Sigma$ , defined by (2.45), are positive.

- **2.21** ( $\star$ ) Show that a real, symmetric matrix of size  $D \times D$  has  $D(D+1)/2$  independent parameters.
- **2.22**  $(\star)$  **www** Show that the inverse of a symmetric matrix is itself symmetric.
- **2.23**  $(\star \star)$  By diagonalizing the coordinate system using the eigenvector expansion (2.45), show that the volume contained within the hyperellipsoid corresponding to a constant

Mahalanobis distance  $\Delta$  is given by

$$
V_D|\Sigma|^{1/2}\Delta^D\tag{2.286}
$$

where  $V_D$  is the volume of the unit sphere in D dimensions, and the Mahalanobis distance is defined by (2.44).

**2.24**  $(\star \star)$  **www** Prove the identity (2.76) by multiplying both sides by the matrix

$$
\begin{pmatrix} A & B \\ C & D \end{pmatrix} \tag{2.287}
$$

and making use of the definition (2.77).

**2.25**  $(\star \star)$  In Sections 2.3.1 and 2.3.2, we considered the conditional and marginal distributions for a multivariate Gaussian. More generally, we can consider a partitioning of the components of **x** into three groups  $\mathbf{x}_a$ ,  $\mathbf{x}_b$ , and  $\mathbf{x}_c$ , with a corresponding partitioning of the mean vector  $\mu$  and of the covariance matrix  $\Sigma$  in the form

$$
\mu = \begin{pmatrix} \mu_a \\ \mu_b \\ \mu_c \end{pmatrix}, \qquad \Sigma = \begin{pmatrix} \Sigma_{aa} & \Sigma_{ab} & \Sigma_{ac} \\ \Sigma_{ba} & \Sigma_{bb} & \Sigma_{bc} \\ \Sigma_{ca} & \Sigma_{cb} & \Sigma_{cc} \end{pmatrix}. (2.288)
$$

By making use of the results of Section 2.3, find an expression for the conditional distribution  $p(\mathbf{x}_a|\mathbf{x}_b)$  in which  $\mathbf{x}_c$  has been marginalized out.

**2.26**  $(\star \star)$  A very useful result from linear algebra is the *Woodbury* matrix inversion formula given by

$$
(\mathbf{A} + \mathbf{BCD})^{-1} = \mathbf{A}^{-1} - \mathbf{A}^{-1} \mathbf{B} (\mathbf{C}^{-1} + \mathbf{DA}^{-1} \mathbf{B})^{-1} \mathbf{DA}^{-1}.
$$
 (2.289)

By multiplying both sides by  $(A + BCD)$  prove the correctness of this result.

- **2.27** ( $\star$ ) Let **x** and **z** be two independent random vectors, so that  $p(\mathbf{x}, \mathbf{z}) = p(\mathbf{x})p(\mathbf{z})$ . Show that the mean of their sum  $y = x + z$  is given by the sum of the means of each of the variable separately. Similarly, show that the covariance matrix of **y** is given by the sum of the covariance matrices of **x** and **z**. Confirm that this result agrees with that of Exercise 1.10.
- **2.28**  $(\star \star \star)$  **www** Consider a joint distribution over the variable

$$
z = \begin{pmatrix} x \\ y \end{pmatrix} \tag{2.290}
$$

whose mean and covariance are given by (2.108) and (2.105) respectively. By making use of the results (2.92) and (2.93) show that the marginal distribution  $p(x)$  is given (2.99). Similarly, by making use of the results (2.81) and (2.82) show that the conditional distribution  $p(y|x)$  is given by (2.100).

- **2.29**  $(\star \star)$  Using the partitioned matrix inversion formula (2.76), show that the inverse of the precision matrix (2.104) is given by the covariance matrix (2.105).
- **2.30** ( $\star$ ) By starting from (2.107) and making use of the result (2.105), verify the result (2.108).
- **2.31**  $(\star \star)$  Consider two multidimensional random vectors **x** and **z** having Gaussian distributions  $p(\mathbf{x}) = \mathcal{N}(\mathbf{x}|\boldsymbol{\mu}_{\mathbf{x}}, \boldsymbol{\Sigma}_{\mathbf{x}})$  and  $p(\mathbf{z}) = \mathcal{N}(\mathbf{z}|\boldsymbol{\mu}_{\mathbf{z}}, \boldsymbol{\Sigma}_{\mathbf{z}})$  respectively, together with their sum  $y = x+z$ . Use the results (2.109) and (2.110) to find an expression for the marginal distribution  $p(y)$  by considering the linear-Gaussian model comprising the product of the marginal distribution  $p(\mathbf{x})$  and the conditional distribution  $p(\mathbf{y}|\mathbf{x})$ .
- **2.32**  $(\star \star \star)$  **www** This exercise and the next provide practice at manipulating the quadratic forms that arise in linear-Gaussian models, as well as giving an independent check of results derived in the main text. Consider a joint distribution  $p(x, y)$ defined by the marginal and conditional distributions given by (2.99) and (2.100). By examining the quadratic form in the exponent of the joint distribution, and using the technique of 'completing the square' discussed in Section 2.3, find expressions for the mean and covariance of the marginal distribution  $p(\mathbf{y})$  in which the variable **x** has been integrated out. To do this, make use of the Woodbury matrix inversion formula (2.289). Verify that these results agree with (2.109) and (2.110) obtained using the results of Chapter 2.
- **2.33**  $(\star \star \star)$  Consider the same joint distribution as in Exercise 2.32, but now use the technique of completing the square to find expressions for the mean and covariance of the conditional distribution  $p(x|y)$ . Again, verify that these agree with the corresponding expressions  $(2.111)$  and  $(2.112)$ .
- **2.34**  $(\star \star)$  **www** To find the maximum likelihood solution for the covariance matrix of a multivariate Gaussian, we need to maximize the log likelihood function (2.118) with respect to  $\Sigma$ , noting that the covariance matrix must be symmetric and positive definite. Here we proceed by ignoring these constraints and doing a straightforward maximization. Using the results  $(C.21)$ ,  $(C.26)$ , and  $(C.28)$  from Appendix C, show that the covariance matrix  $\Sigma$  that maximizes the log likelihood function (2.118) is given by the sample covariance (2.122). We note that the final result is necessarily symmetric and positive definite (provided the sample covariance is nonsingular).
- **2.35**  $(\star \star)$  Use the result (2.59) to prove (2.62). Now, using the results (2.59), and (2.62), show that

$$
\mathbb{E}[\mathbf{x}_n \mathbf{x}_m] = \boldsymbol{\mu} \boldsymbol{\mu}^{\mathrm{T}} + I_{nm} \boldsymbol{\Sigma}
$$
 (2.291)

where  $x_n$  denotes a data point sampled from a Gaussian distribution with mean  $\mu$ and covariance  $\Sigma$ , and  $I_{nm}$  denotes the  $(n, m)$  element of the identity matrix. Hence prove the result (2.124).

**2.36**  $(\star \star)$  **www** Using an analogous procedure to that used to obtain (2.126), derive an expression for the sequential estimation of the variance of a univariate Gaussian

distribution, by starting with the maximum likelihood expression

$$
\sigma_{\rm ML}^2 = \frac{1}{N} \sum_{n=1}^{N} (x_n - \mu)^2.
$$
 (2.292)

Verify that substituting the expression for a Gaussian distribution into the Robbins-Monro sequential estimation formula (2.135) gives a result of the same form, and hence obtain an expression for the corresponding coefficients  $a_N$ .

- **2.37**  $(\star \star)$  Using an analogous procedure to that used to obtain (2.126), derive an expression for the sequential estimation of the covariance of a multivariate Gaussian distribution, by starting with the maximum likelihood expression (2.122). Verify that substituting the expression for a Gaussian distribution into the Robbins-Monro sequential estimation formula (2.135) gives a result of the same form, and hence obtain an expression for the corresponding coefficients  $a_N$ .
- **2.38**  $(\star)$  Use the technique of completing the square for the quadratic form in the exponent to derive the results (2.141) and (2.142).
- **2.39**  $(\star \star)$  Starting from the results (2.141) and (2.142) for the posterior distribution of the mean of a Gaussian random variable, dissect out the contributions from the first  $N - 1$  data points and hence obtain expressions for the sequential update of  $\mu_N$  and  $\sigma_N^2$ . Now derive the same results starting from the posterior distribution  $p(\mu|x_1,...,x_{N-1}) = \mathcal{N}(\mu|\mu_{N-1}, \sigma_{N-1}^2)$  and multiplying by the likelihood function  $p(x_N | \mu) = \mathcal{N}(x_N | \mu, \sigma^2)$  and then completing the square and normalizing to obtain the posterior distribution after  $N$  observations.
- **2.40**  $(\star \star)$  **www** Consider a D-dimensional Gaussian random variable **x** with distribution  $\mathcal{N}(\mathbf{x}|\boldsymbol{\mu}, \boldsymbol{\Sigma})$  in which the covariance  $\boldsymbol{\Sigma}$  is known and for which we wish to infer the mean  $\mu$  from a set of observations  $\mathbf{X} = {\mathbf{x}_1, \dots, \mathbf{x}_N}$ . Given a prior distribution  $p(\mu) = \mathcal{N}(\mu|\mu_0, \Sigma_0)$ , find the corresponding posterior distribution  $p(\mu|\mathbf{X})$ .
- **2.41**  $(\star)$  Use the definition of the gamma function (1.141) to show that the gamma distribution (2.146) is normalized.
- **2.42**  $(\star \star)$  Evaluate the mean, variance, and mode of the gamma distribution (2.146).
- **2.43 ( )** The following distribution

$$
p(x|\sigma^2, q) = \frac{q}{2(2\sigma^2)^{1/q} \Gamma(1/q)} \exp\left(-\frac{|x|^q}{2\sigma^2}\right)
$$
 (2.293)

is a generalization of the univariate Gaussian distribution. Show that this distribution is normalized so that

$$
\int_{-\infty}^{\infty} p(x|\sigma^2, q) dx = 1
$$
\n(2.294)

and that it reduces to the Gaussian when  $q = 2$ . Consider a regression model in which the target variable is given by  $t = y(\mathbf{x}, \mathbf{w}) + \epsilon$  and  $\epsilon$  is a random noise variable drawn from the distribution (2.293). Show that the log likelihood function over **w** and  $\sigma^2$ , for an observed data set of input vectors  $\mathbf{X} = {\mathbf{x}_1, ..., \mathbf{x}_N}$  and corresponding target variables  $\mathbf{t} = (t_1, ..., t_N)^T$ , is given by

$$
\ln p(\mathbf{t}|\mathbf{X}, \mathbf{w}, \sigma^2) = -\frac{1}{2\sigma^2} \sum_{n=1}^{N} |y(\mathbf{x}_n, \mathbf{w}) - t_n|^q - \frac{N}{q} \ln(2\sigma^2) + \text{const} \quad (2.295)
$$

where 'const' denotes terms independent of both **w** and  $\sigma^2$ . Note that, as a function of **w**, this is the  $L_q$  error function considered in Section 1.5.5.

- **2.44** ( $\star\star$ ) Consider a univariate Gaussian distribution  $\mathcal{N}(x|\mu, \tau^{-1})$  having conjugate Gaussian-gamma prior given by (2.154), and a data set  $\mathbf{x} = \{x_1, \dots, x_N\}$  of i.i.d. observations. Show that the posterior distribution is also a Gaussian-gamma distribution of the same functional form as the prior, and write down expressions for the parameters of this posterior distribution.
- **2.45**  $(\star)$  Verify that the Wishart distribution defined by (2.155) is indeed a conjugate prior for the precision matrix of a multivariate Gaussian.
- **2.46**  $(\star)$  **www** Verify that evaluating the integral in (2.158) leads to the result (2.159).
- **2.47** ( $\star$ ) **www** Show that in the limit  $\nu \to \infty$ , the t-distribution (2.159) becomes a Gaussian. Hint: ignore the normalization coefficient, and simply look at the dependence on  $x$ .
- **2.48**  $(\star)$  By following analogous steps to those used to derive the univariate Student's t-distribution (2.159), verify the result (2.162) for the multivariate form of the Student's t-distribution, by marginalizing over the variable  $\eta$  in (2.161). Using the definition (2.161), show by exchanging integration variables that the multivariate t-distribution is correctly normalized.
- **2.49**  $(\star \star)$  By using the definition (2.161) of the multivariate Student's t-distribution as a convolution of a Gaussian with a gamma distribution, verify the properties (2.164), (2.165), and (2.166) for the multivariate t-distribution defined by (2.162).
- **2.50** ( $\star$ ) Show that in the limit  $\nu \rightarrow \infty$ , the multivariate Student's t-distribution (2.162) reduces to a Gaussian with mean *<sup>µ</sup>* and precision **Λ**.
- **2.51** ( $\star$ ) **www** The various trigonometric identities used in the discussion of periodic variables in this chapter can be proven easily from the relation

$$
\exp(iA) = \cos A + i \sin A \tag{2.296}
$$

in which  $i$  is the square root of minus one. By considering the identity

$$
\exp(iA)\exp(-iA) = 1\tag{2.297}
$$

prove the result (2.177). Similarly, using the identity

$$
\cos(A - B) = \Re \exp\{i(A - B)\}\tag{2.298}
$$

where  $\Re$  denotes the real part, prove (2.178). Finally, by using  $sin(A - B)$  =  $\Im \exp{i(A - B)}$ , where  $\Im$  denotes the imaginary part, prove the result (2.183).

**2.52**  $(\star \star)$  For large m, the von Mises distribution (2.179) becomes sharply peaked around the mode  $\theta_0$ . By defining  $\xi = m^{1/2}(\theta - \theta_0)$  and making the Taylor expansion of the cosine function given by

$$
\cos \alpha = 1 - \frac{\alpha^2}{2} + O(\alpha^4)
$$
\n(2.299)

show that as  $m \to \infty$ , the von Mises distribution tends to a Gaussian.

- **2.53** ( $\star$ ) Using the trigonometric identity (2.183), show that solution of (2.182) for  $\theta_0$  is given by (2.184).
- **2.54** ( $\star$ ) By computing first and second derivatives of the von Mises distribution (2.179), and using  $I_0(m) > 0$  for  $m > 0$ , show that the maximum of the distribution occurs when  $\theta = \theta_0$  and that the minimum occurs when  $\theta = \theta_0 + \pi \pmod{2\pi}$ .
- **2.55** ( $\star$ ) By making use of the result (2.168), together with (2.184) and the trigonometric identity (2.178), show that the maximum likelihood solution  $m_{ML}$  for the concentration of the von Mises distribution satisfies  $A(m_{ML}) = \overline{r}$  where  $\overline{r}$  is the radius of the mean of the observations viewed as unit vectors in the two-dimensional Euclidean plane, as illustrated in Figure 2.17.
- **2.56**  $(\star \star)$  **www** Express the beta distribution (2.13), the gamma distribution (2.146), and the von Mises distribution (2.179) as members of the exponential family (2.194) and thereby identify their natural parameters.
- **2.57**  $(\star)$  Verify that the multivariate Gaussian distribution can be cast in exponential family form (2.194) and derive expressions for  $\eta$ ,  $\mathbf{u}(\mathbf{x})$ ,  $h(\mathbf{x})$  and  $q(\eta)$  analogous to  $(2.220)$ – $(2.223)$ .
- **2.58** ( $\star$ ) The result (2.226) showed that the negative gradient of  $\ln q(\eta)$  for the exponential family is given by the expectation of  $u(x)$ . By taking the second derivatives of (2.195), show that

$$
-\nabla \nabla \ln g(\boldsymbol{\eta}) = \mathbb{E}[\mathbf{u}(\mathbf{x})\mathbf{u}(\mathbf{x})^{\mathrm{T}}] - \mathbb{E}[\mathbf{u}(\mathbf{x})]\mathbb{E}[\mathbf{u}(\mathbf{x})^{\mathrm{T}}] = \mathrm{cov}[\mathbf{u}(\mathbf{x})]. \quad (2.300)
$$

- **2.59** ( $\star$ ) By changing variables using  $y = x/\sigma$ , show that the density (2.236) will be correctly normalized, provided  $f(x)$  is correctly normalized.
- **2.60**  $(\star \star)$  **www** Consider a histogram-like density model in which the space **x** is divided into fixed regions for which the density  $p(x)$  takes the constant value  $h_i$  over the *i*<sup>th</sup> region, and that the volume of region *i* is denoted  $\Delta_i$ . Suppose we have a set of N observations of **x** such that  $n_i$  of these observations fall in region i. Using a Lagrange multiplier to enforce the normalization constraint on the density, derive an expression for the maximum likelihood estimator for the  $\{h_i\}$ .
- **2.61**  $(\star)$  Show that the K-nearest-neighbour density model defines an improper distribution whose integral over all space is divergent.
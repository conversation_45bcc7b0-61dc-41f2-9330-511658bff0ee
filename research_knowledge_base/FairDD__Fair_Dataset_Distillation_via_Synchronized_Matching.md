# FAIRDD: FAIR DATASET DISTILLATION VIA SYNCHRONIZED MATCHING

<PERSON><PERSON> $^1,$  <PERSON><PERSON> $^1,$  <PERSON>bo He $^{1*},$  <PERSON><PERSON><PERSON>g $^1,$  <PERSON><PERSON> $^1$ <sup>1</sup>Zhejiang University

## ABSTRACT

Condensing large datasets into smaller synthetic counterparts has demonstrated its promise for image classification. However, previous research has overlooked a crucial concern in image recognition: ensuring that models trained on condensed datasets are unbiased towards protected attributes (PA), such as gender and race. Our investigation reveals that dataset distillation (DD) fails to alleviate the unfairness towards minority groups within original datasets. Moreover, this bias typically worsens in the condensed datasets due to their smaller size. To bridge the research gap, we propose a novel fair dataset distillation (FDD) framework, namely FairDD, which can be seamlessly applied to diverse matching-based DD approaches, requiring no modifications to their original architectures. The key innovation of FairDD lies in synchronously matching synthetic datasets to PA-wise groups of original datasets, rather than indiscriminate alignment to the whole distributions in vanilla DDs, dominated by majority groups. This synchronized matching allows synthetic datasets to avoid collapsing into majority groups and bootstrap their balanced generation to all PA groups. Consequently, FairDD could effectively regularize vanilla DDs to favor biased generation toward minority groups while maintaining the accuracy of target attributes. Theoretical analyses and extensive experimental evaluations demonstrate that FairDD significantly improves fairness compared to vanilla DD methods, without sacrificing classification accuracy. Its consistent superiority across diverse DDs, spanning Distribution and Gradient Matching, establishes it as a versatile FDD approach.

## 1 INTRODUCTION

Deep learning has witnessed remarkable success in computer vision, particularly with recent breakthroughs in vision models [\(Oquab et al., 2023;](#page-12-0) [Kirillov et al., 2023;](#page-11-0) [Radford et al., 2021;](#page-12-1) [Li et al.,](#page-11-1) [2022;](#page-11-1) [Zhou et al., 2023\)](#page-13-0). Their vision backbones, such as ResNet [\(He et al., 2015\)](#page-10-0) and ViT [\(Dosovit](#page-10-1)[skiy et al., 2020\)](#page-10-1), are data-hungry models that require extensive amounts of data for optimization. Dataset Distillation (DD) [\(Wang et al., 2018;](#page-13-1) [Zhao & Bilen, 2021;](#page-13-2) [2023;](#page-13-3) [Cazenavette et al., 2022;](#page-10-2) [Wang et al., 2022;](#page-12-2) [Lee et al., 2022b;](#page-11-2) [Cui et al., 2022;](#page-10-3) [Loo et al., 2023;](#page-12-3) [Guo et al., 2023;](#page-10-4) [He &](#page-11-3) [Zhou, 2024;](#page-11-3) [Zhao et al., 2024\)](#page-13-4) provides a promising solution to alleviate this data requirement by condensing the original large dataset into more informative and smaller counterparts [\(Mehrabi et al.,](#page-12-4) [2021;](#page-12-4) [Chung et al., 2023\)](#page-10-5). Despite its appeal, existing DD researches focus on ensuring that models trained on condensed datasets perform comparable accuracy to those trained on the original dataset in terms of target attributes (TA) [\(Cui et al., 2024;](#page-10-6) [Lu et al., 2024;](#page-12-5) [Vahidian et al., 2024\)](#page-12-6). However, they have overlooked enabling the fairness of trained models with respect to protected attributes (PA).

Unfairness typically arises from imbalanced sample distributions among PA in the empirical training datasets. When the original datasets suffer from the PA imbalance, the corresponding datasets condensed by vanilla DDs inherit and amplify this bias in Fig. [1\(a\).](#page-1-0) Since vanilla DDs tend to cover TA distribution for image classification, and as a result, it naturally leads to more synthetic samples located in majority groups compared to minority groups w.r.t. PA, as shown in Fig. [1\(b\).](#page-1-1) In this case, these condensed datasets retain the imbalance between protected attributes, thereby rendering the model trained on them unfair. Moreover, the reduced size of the condensed datasets typically amplifies the bias present in the original datasets, especially when there is a significant gap in size between the original and condensed datasets, such as image per class (IPC) 1000 vs. 10. Therefore, it is worthwhile to broaden the scope of DD to encompass both TA accuracy and PA fairness. Recent

<sup>∗</sup>Corresponding authors.

<span id="page-1-0"></span>Image /page/1/Figure/0 description: The image displays a bar chart comparing fairness metrics across three datasets: Colored-MNIST, CIFAR10-S, and CelebA. The y-axis represents 'Fairness at IPC = 50 (DEOM↓)' ranging from 0 to 100. The x-axis labels the datasets. For each dataset, there are five bars representing different methods: 'Whole dataset' (red), 'DM' (blue), 'DM+FairDD' (green), 'IDC' (purple), and 'IDC+FairDD' (orange). The specific fairness values are: Colored-MNIST - Whole dataset: 10.10, DM: 100.00, DM+FairDD: 10.05, IDC: 65.34, IDC+FairDD: 9.18. CIFAR10-S - Whole dataset: 49.72, DM: 75.13, DM+FairDD: 18.28, IDC: 92.00, IDC+FairDD: 29.00. CelebA - Whole dataset: 24.85, DM: 40.26, DM+FairDD: 14.08, IDC: 56.74, IDC+FairDD: 22.57. To the right of the bar chart are two grids of colored digits, likely illustrating the datasets used in the experiment. The left grid shows digits 0 through 9, each repeated multiple times with varying colors. The right grid shows a similar arrangement of colored digits.

(a) Fairness comparison on original and distilled datasets using different DDs.

<span id="page-1-1"></span>(b) Condensed datasets on C-MNIST (FG) using DM.

<span id="page-1-2"></span>(c) Condensed datasets using FairDD+DM.

Figure 1: Condensed datasets fail to mitigate and amplify the bias present in original datasets. (a) depicts the bias against minority groups of the model trained on condensed datasets, showing that vanilla DDs exacerbate the unfairness (IPC=50). However, incorporating FairDD into vanilla DDs could effectively mitigate these biases. (b) and (c) distill C-MNIST (FG) (IPC=10), where each class contains 10 colors and individual colors dominate different categories as PA. (b) shows that each class (row) is biased to the majority color in the condensed dataset via vanilla DDs. (c) preserves PA of minority groups and mitigates color imbalance in the condensed dataset via DM+FairDD.

works attempt to address the class/TA-level long-tailed phenomenon [Zhao et al.](#page-13-4) [\(2024\)](#page-13-4) and spurious correlations [\(Cui et al., 2024\)](#page-10-6) to improve the classification performance [\(Vahidian et al., 2024;](#page-12-6) [Wang](#page-13-5) [et al., 2024\)](#page-13-5), but the exploration on Fair Dataset Distillation (FDD) is still blank.

To the best of our knowledge, we are the first to propose a novel FDD framework, FairDD, which enables PA fairness in the model trained on the condensed datasets, regardless of PA imbalance in the original data. However, simultaneously maintaining TA accuracy and improving PA fairness is challenging, as the algorithm must properly balance emphasis across all groups, i.e., majority groups primarily for TA distributional coverage and minority groups primarily for PA bias mitigation.

FairDD tackles this challenge by (1) partitioning the empirical training distribution into different groups according to PA and decomposing the single alignment target of vanilla DDs into PA-wise sub-targets. (2) synchronously matching synthetic samples to these PA groups, which equally bootstraps synthetic datasets to each PA group without involving the specific group size. In doing so, we reformulated the optimization objectives of vanilla DDs into fairDD-style versions. This allows FairDD to mitigate the effect of the imbalanced PA to the generation of  $S$  and to prevent  $S$ from collapsing into the majority group. Meanwhile, FairDD could also achieve the comprehensive coverage of the entire distribution for TA accuracy as shown in Fig. [1\(c\).](#page-1-2) In addition, FairDD requires no modification to existing DDs or additional modules. We provide a theoretical guarantee that FairDD could improve PA fairness while maintaining TA accuracy.

Extensive experiments demonstrate that our proposed framework effectively mitigates the unfairness in datasets of highly diverse bias. FairDD substantially improves model fairness on condensed datasets compared to various vanilla DDs. FairDD demonstrates its versatility across diverse DD paradigms, including Distribution Matching (DM) and Gradient Matching (GM). Note that we do not apply FairDD to Trajectory Matching (TM) as doing so would require extra model trajectories trained on minority groups, in which the corresponding models would suffer from overfitting due to their limited samples. Our paper makes the following main contributions:

- To the best of our knowledge, our research is the first attempt to incorporate fairness issues into DD explicitly. We reveal that vanilla DDs fail to mitigate the bias in original datasets and may exacerbate it due to the limited synthetic samples, leading to severe PA bias in the model trained by the resulting condensed dataset.
- We introduce a novel FDD framework called FairDD, which proposes synchronized matching to align synthetic samples to all PA groups partitioned from the original data distribution. This allows the generated synthetic samples to be agnostic to PA imbalance of original datasets and maintain the overall distributional coverage of TA.
- FairDD requires no alternations to the original architectures of vanilla DDs, in which it only needs to revise the vanilla optimization objectives as a group-level formulation. We also provide theoretical analyses to guarantee the fairness and accuracy of synthetic samples.

• Extensive empirical experiments demonstrate that FairDD mitigates the unfairness of vanilla DDs by a large margin. The consistent superiority across diverse DDs, including DM and GM, illustrates that FairDD is a generalist for FDD.

## 2 PRELIMINARIES

**Dataset Distillation.** Given a vast dataset  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^N$ , the goal of DD is to condense the original dataset  $\mathcal{T}$  into a smaller dataset  $\mathcal{S} = \{(x_i, y_i)\}_{i=1}^M$  via distillation algorithm Alg with a nerual network, parameterized by  $\theta$ . Randomly initialized classification network  $g_\psi$  should maintain the same empirical risk whether it is trained on  $S$  or  $T$ .

$$
S^* = \underset{S}{\text{arg min}} \text{Alg}(S, \mathcal{T}, \theta),\tag{1}
$$

$$
\mathbb{E}_{\psi \sim \Psi}[\ell(g_{\psi}; \mathcal{S})] \simeq \mathbb{E}_{\psi \sim \Psi}[\ell(g_{\psi}; \mathcal{T})], \tag{2}
$$

where  $\Psi$  and  $\ell(\cdot)$  represent the parameter space and loss function, respectively. The pioneering work [\(Wang et al., 2018\)](#page-13-1) formulates Alg as a bi-level optimization problem. However, such an optimization process is time-consuming and unsTable Recent works circumvent it and propose surrogate matching objectives to achieve comparable and even better performance. This research line is collectively referred to as the DMF, and our paper primarily studies one-stage GM [\(Zhao et al.,](#page-13-6) [2020;](#page-13-6) [Zhao & Bilen, 2021\)](#page-13-2) and DM [\(Zhao & Bilen, 2023;](#page-13-3) [Wang et al., 2022;](#page-12-2) [Zhao & Bilen, 2022\)](#page-13-7). We leave two-stage trajectory-matching (TM) for future exploration.

Visual Fairness Visual fairness is an important field to mitigate discrimination against minority groups. Group fairness requires no statistical disparity to different groups in terms of PA, such as race and gender. This means that an ideal fair model should make independent predictions between TA and PA. One of the common fairness criteria is equalized odds (EO), which computes the prediction accuracy of PA conditioned on TA, to evaluate the level of conditional independence between PA and TA. We use two types of difference of equalized odds  $DEO<sub>M</sub>$  and  $DEO<sub>A</sub>$  from the worst and averaged levels. Formally, given the PA set  $A = \{a_1, a_2, ..., a_p\}$ , DEO<sub>M</sub> and DEO<sub>A</sub> [\(Jung et al., 2021\)](#page-11-4) can be formulated mathematically as follows:

$$
DEO_M = \max_{y \in \mathcal{Y}} \max_{a_i, a_j \in A \& a_i \neq a_j} |P(\hat{Y} = y | Y = y, A = a_i) - P(\hat{Y} = y | Y = y, A = a_j)|,
$$
  
\n
$$
DEO_A = \max_{y \in \mathcal{Y}} \max_{a_i, a_j \in A \& a_i \neq a_j} |P(\hat{Y} = y | Y = y, A = a_i) - P(\hat{Y} = y | Y = y, A = a_j)|.
$$

<span id="page-2-2"></span>

## 3 A CLOSE LOOK AT DATASET DISTILLATION FROM FAIRNESS

A unified perspective for Data Match Framework. The essence of the DMF lies in choosing the target signs of original samples that effectively represent their characteristics for image recognition, and then aligning these signals as a proxy task to optimize the condensed dataset. The target signal  $\phi(x;\theta)$  is typically the key information from feature extraction or optimization process using a randomly initialized network parameterized by  $\theta$ . For example, GM aligns the gradient information produced by  $\mathcal T$  with that of the condensed S. Instead, DM matches the embedding distributions of  $\mathcal T$ and S. As for these approaches in DMF. we can unify the optimization objective as  $\mathcal{L}(S; \theta, \mathcal{T})$ :

<span id="page-2-0"></span>
$$
\mathcal{L}(\mathcal{S}; \theta, \mathcal{T}) := \sum_{y \in \mathcal{Y}} \mathcal{D}\big(\mathbb{E}[\phi_{x \sim \mathcal{T}_y}(x; \theta)], \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)]\big),\tag{3}
$$

where  $\mathbb{E}[\phi_{x \sim \mathcal{T}_y}(x;\theta)] \in \mathbb{R}^C$  and  $\mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x;\theta)] \in \mathbb{R}^C$  are represented expectation vectors of the target signs on  $\mathcal T$  and  $\mathcal S$ , respectively.  $\mathcal D(\cdot, \cdot)$  is a distance function. In DMF, MSE is adopted in DM and DREAM, and MAE is used in IDC. Also, cosine distance is involved in DC.

Why does vanilla DD fail to mitigate PA imbalance? Given the PA set  $\mathcal A$  in  $\mathcal T$ , let us define the class-level sample ratio  $\mathcal{R}_y = \{r_y^{a_1}, r_y^{a_2}, ..., r_y^{a_p}\}$ , where  $r_y^{a_i} = |\mathcal{T}_y^{a_i}|/|\mathcal{T}_y|$ , and  $|\cdot|$  represents the cardinal number of a set. Current DD paradigms focus on preserving TA representativeness for image recognition. Here, we decompose the whole expectation into the expectation of PA-wise groups, i.e,  $\mathbb{E}[\phi_{x \sim \mathcal{T}_y}(x; \theta)] = \sum_{a_i \in \mathcal{A}} r_y^{a_i} \mathbb{E}[\phi_{x \sim \mathcal{T}_y^{a_i}}(x; \theta)]$ , and thus Eq. [3](#page-2-0) can be rewritten as follows:

<span id="page-2-1"></span>
$$
\mathcal{L}(\mathcal{S}; \theta, \mathcal{T}) := \sum_{y \in \mathcal{Y}} \mathcal{D}(\sum_{a_i \in \mathcal{A}} r_y^{a_i} \mathbb{E}[\phi_{x \sim \mathcal{T}_y^{a_i}}(x; \theta)], \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)]). \tag{4}
$$

From Eq. [4,](#page-2-1) the optimization objective of class y is weighted by the sample ratio  $r_y^{a_i}$  from different groups. When T suffers from PA imbalance, e.g.,  $r_y^{a_j} \gg \sum_{i \neq j} r_y^{a_i}$ , the majority group indexed by  $i$  contributes more to the alignment compared to minority groups, as present in Fig. [2\(a\).](#page-4-0) In

other words,  $S$  tends to produce more samples belonging to group  $i$  for the total loss minimization. Therefore, the objective of vanilla DDs suffers from the PA imbalance within  $T$ .

Next, we further study how the resulting S is affected by sample ratio  $r_y^{a_i}$  of different groups. To this end, we assume that the optimization process could reach the optimal solution for each class, and as a result, the final resulting S satisfies the condition that the derivative of the objective function with respect to  $\mathbb{E}[\phi_{x\sim S_y}(x;\theta)]$  equals 0. Formally. we have the following mathematical equation:

<span id="page-3-0"></span>
$$
\frac{\partial \mathcal{L}(\mathcal{S}_y; \theta, \mathcal{T}_y)}{\partial \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)]} = 0 \implies \frac{\partial \mathcal{D}(\sum_{a_i \in \mathcal{A}} r_y^{a_i} \mathbb{E}[\phi_{x \sim \mathcal{T}_y^{a_i}}(x; \theta)], \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)])}{\partial \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)]} = 0 \tag{5}
$$

Now, let's delve into the specific distance metrics used in vanilla DDs, where the most commonly used metrics are MAE, MSE, and cosine distance. We respectively analyze that the optimal point of  $\mathbb{E}[\phi_{x\sim S_y}(x;\theta)]$  could reach under each of these metrics.

$$
\frac{\partial \mathcal{L}(\mathcal{S}_{y};\theta,\mathcal{T}_{y})}{\partial \mathbb{E}[\phi_{x\sim\mathcal{S}_{y}}(x;\theta)]} = 0 \implies \mathbb{E}[\phi_{x\sim\mathcal{S}_{y}}(x;\theta)] = \begin{cases} \sum_{a_{i}\in\mathcal{A}} r_{y}^{a_{i}}\mathbb{E}[\phi_{x\sim\mathcal{T}_{y}^{a_{i}}}(x;\theta)], & \text{For MAE} \\ \sum_{a_{i}\in\mathcal{A}} r_{y}^{a_{i}}\mathbb{E}[\phi_{x\sim\mathcal{T}_{y}^{a_{i}}}(x;\theta)], & \text{For MSE} \\ \lambda \sum_{a_{i}\in\mathcal{A}} r_{y}^{a_{i}}\mathbb{E}[\phi_{x\sim\mathcal{T}_{y}^{a_{i}}}(x;\theta)], & \text{For cosine distance} \end{cases}
$$
(6)

Where  $\lambda$  is a scalar, equaling  $\frac{\mathbb{E}[\phi_{x\sim S_y}(x;\theta)]\|_2}{\|\nabla e\|_x^{\alpha_i}\mathbb{E}[\phi_{x\sim S_y}(x;\theta)]}$  $\frac{\|\mathbb{E}[{\varphi_x \sim S_y(x, \theta)}\|_2]}{\|\sum_{a_i \in A} r_y^{a_i} \mathbb{E}[\phi_{x \sim T_y^{a_i}}(x; \theta)]\|_2}$ . Eq. [6](#page-3-0) presents that the expectation of synthetic samples  $\mathbb{E}[\phi_{x\sim S_y}(x;\theta)]$  ultimately converges to an average on expectations of all PA groups, weighted by their respective sample ratios  $r_y^{a_i}$ . This indicates that vanilla DDs naturally favor majority groups, causing  $S$  to shift towards them and inherit their biases.

Therefore, when original datasets suffer from PA imbalance, e.g.,  $r_y^{a_j} \gg \sum_{i \neq j} r_y^{a_i}$ , the unfairness of the synthetic dataset stems from two different aspects: 1) The majority group renders synthetic samples to locate its region from Eq. [6.](#page-3-0) 2) According to Eq. [4,](#page-2-1) the large sample quantities of the majority group contribute more to the total loss. As a result, minority groups experience higher loss during testing, which limits the model to represent them accurately. These factors prompt us to reduce the impact of PA imbalance on the generation of  $S$ .

## 4 FAIRDD

### 4.1 OVERVIEW

In this paper, we propose a novel FDD framework that achieves both PA fairness and TA accuracy for the model trained on its generation  $S$ , regardless of whether the original datasets exhibit PA fairness. As illustrated in Fig. [2\(b\),](#page-4-1) FairDD first partitions the dataset into different groups w.r.t. PA and then introduces an effective synchronized matching to equally align S with each group within  $\mathcal{T}$ . Compared to vanilla DDs, which simply pull the synthetic dataset toward the whole dataset center that is biased toward the majority group in the synthetic dataset, FairDD proposes a group-level synchronized alignment, in which each group attracts the synthetic data toward itself, thus forcing it to move farther from other groups. This "pull-and-push" process prevents the generation from collapsing into majority groups (fairness) and ensures class-level distributional coverage (accuracy).

### 4.2 METHODS

As mentioned in Sec. [3,](#page-2-2) vanilla DDs fail to mitigate PA imbalance and even amplify the discrimination. The relation behind the failure is that the majority group dominates the generation direction of  $\mathcal S$ and leads to the resulting  $S$  inheriting the PA imbalance, i.e., preference to fitting to the majority group. To avoid the synthetic samples collapsing into the majority group, we decompose the single target (dominated by the majority group) into PA-wise sub-targets, and simultaneously align  $S$  with these sub-targets, without incorporating the specific sample ratio of each group into the optimization objective. In this way, we obtain the unified objective function of FairDD:

$$
\mathcal{L}_{FairDD}(\mathcal{S}; \theta, \mathcal{T}) := \sum_{y \in \mathcal{Y}} \sum_{a_i \in \mathcal{A}} \mathcal{D}(\mathbb{E}[\phi_{x \sim \mathcal{T}_y^{a_i}}(x; \theta)], \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)]). \tag{7}
$$

The reformulation forms synchronized matching, where different sub-targets attempt to pull  $S$  into their corresponding PA regions. Each PA group holds equal importance in generating  $S$ , ultimately converging to a balanced (fair) status. Subsequently, we present a theoretical analysis illustrating how FairDD effectively mitigates PA imbalance and guarantees coverage across the entire TA distribution.

<span id="page-4-0"></span>Image /page/4/Figure/0 description: The image displays two pipelines for domain adaptation, labeled (a) The pipeline of vanilla DDs and (b) The pipeline of FairDD. Both pipelines start with an original dataset T, which contains images of digits with varying colors. The target attribute is the digital number, and the protect attribute is color imbalance. The pipelines then involve randomly initializing a dataset S0, which is then processed to create either a biased dataset S (in pipeline a) or an unbiased dataset S (in pipeline b). Both pipelines utilize a backbone of diverse DDs of DMF. Pipeline (a) shows a 'Vanilla match' process where class-wise signal distributions in Ty and Sy are pulled towards each other. This leads to 'Distributional undercoverage & Bias inheritance (amplification)' in the class-wise signal space in Sy. Pipeline (b) shows an 'Imbalance-agnostic synchronized match' process, where majority and minority group centers in Ty and Sy are pulled. This results in 'Distributional coverage & Bias mitigation' in the class-wise signal space in Sy. The key difference highlighted is the bias mitigation strategy employed in pipeline (b) compared to the bias inheritance in pipeline (a).

(a) The pipeline of vanilla DDs.

<span id="page-4-2"></span><span id="page-4-1"></span>(b) The pipeline of FairDD.

Figure 2: Comparison between (a) vanilla DDs and (b) FairDD. Taking C-MNIST (FG) for example  $(IPC = 10)$ , vanilla DDs directly align S (random initialization) with the whole distribution regardless of majority or minority groups. This causes  $S$  to suffer from distributional coverage and inherit (even amplify) the bias of  $\mathcal T$ . Instead, FairDD first groups target signals of  $\mathcal T$  and then proposes to align S (random initialization) with respective group centers. With this synchronized matching, S is simultaneously pulled by all group centers in a batch. This prevents the condensed dataset  $S$  from being biased towards the majority group, allowing it to better cover the distribution of the original dataset T. We can observe that each class in the resulting S incorporates multiple colors from the minority color groups and mitigates the bias of  $\mathcal T$ , originally dominated by majority colors.

<span id="page-4-4"></span>**Theorem 4.1.** *For any PA set* A, network parameters  $\theta$ , and target signs  $\phi(\cdot)$ ,  $\mathcal{L}_{FairDD}(S; \theta, \mathcal{T})$ *could mitigate the influence of PA imbalance of original datasets on generating synthetic samples. Especially when* D(·) *is MAE or MSE, synchronized matching ensures that the signal expectation of* S *is situated at the center of the expectation across all PA groups within* T *.*

*Proof.* We assume that  $\mathbb{E}[\phi_{x \sim S_y}(x; \theta)]$  could reach the optimal solution for each class. Consequently, we have  $\partial \mathcal{L}_{FairDD}(\mathcal{S}_y; \hat{\theta}, \mathcal{T}_y) / \partial \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)] = 0$ , and derive the respective optimal solution:

$$
\frac{\partial \mathcal{L}_{FairDD}(\mathcal{S}_y; \theta, \mathcal{T}_y)}{\partial \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)]} = 0 \implies \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)] = \begin{cases} \frac{1}{|\mathcal{A}|} \sum_{a_i \in \mathcal{A}} \mathbb{E}[\phi_{x \sim \mathcal{T}_y^{a_i}}(x; \theta)], & \text{For MAE} \\ \frac{1}{|\mathcal{A}|} \sum_{a_i \in \mathcal{A}} \mathbb{E}[\phi_{x \sim \mathcal{T}_y^{a_i}}(x; \theta)], & \text{For MSE} \\ \frac{\lambda}{|\mathcal{A}|} \sum_{a_i \in \mathcal{A}} \mathbb{E}[\phi_{x \sim \mathcal{T}_y^{a_i}}(x; \theta)], & \text{For cosine distance} \end{cases}(8)
$$

According to Eq. [8,](#page-4-2) the resulting  $\mathbb{E}[\phi_{x\sim S_y}(x;\theta)]$  are independent on the sample ratio  $\mathcal{R}_y$ , indicating the corresponding S unaffected by  $\mathcal{R}_y$ . As a result, the condensed S will not be dominated by majority groups that happened in vanilla DDs. All PA centers contribute equally to the generation of S, which succeeds in mitigating the PA imbalance of T. Especially when  $\mathcal{D}(\cdot)$  is MAE or MSE, the expectation of target signs of  $S$  is equal to the arithmetic mean of centers of all PA groups. This shows that  $S$  generated by FairDD is not biased towards any groups.

Although we mitigate the bias inheritance in vanilla DDs by synchronously aligning  $S$  to fine-grained PA-wise groups, it is also crucial to investigate whether  $\mathcal{L}_{FairDD}(S; \theta, \mathcal{T})$  (synchronized matching) ensures that the resulting S achieves comprehensive distributional coverage for  $\mathcal T$ . As mentioned above,  $\mathcal{L}(S; \theta, \mathcal{T})$  matches S and T in a global view to fully cover T's distribution. Below, we provide a theoretical guarantee that  $\mathcal{L}_{FairDD}(\mathcal{S}; \theta, \mathcal{T})$  could maintain comprehensive coverage compared to  $\mathcal{L}(\mathcal{S}; \theta, \mathcal{T})$  when  $\mathcal{D}(\cdot, \cdot)$  is a convex distance function, which is commonly used in diverse  $DDs<sup>1</sup>$  $DDs<sup>1</sup>$  $DDs<sup>1</sup>$ .

<span id="page-4-3"></span><sup>&</sup>lt;sup>1</sup>Emperical experiments show FairDD also can cover the TA distributions when  $\mathcal{D}(\cdot, \cdot)$  is not convex.

**Theorem 4.2.** *For any PA set* A *and target signs*  $\phi_{\theta}(\cdot)$ *,*  $\mathcal{L}_{FairDD}(\mathcal{S}; \theta, \mathcal{T})$  *is the upper bound of vanilla unified objective*  $\mathcal{L}(S; \theta, \mathcal{T})$ *, i.e.,*  $\mathcal{L}_{FairDD}(S; \theta, \mathcal{T}) \geq \mathcal{L}(S; \theta, \mathcal{T})$ *, when*  $\mathcal{D}(\cdot, \cdot)$  *is convex. Optimizing*  $\mathcal{L}_{FairDD}(S; \theta, \mathcal{T})$  *can guarantee the comprehensive distribution coverage for*  $\mathcal{T}$ *. Proof.*

$$
\mathcal{L}(\mathcal{S}; \theta, \mathcal{T}) = \sum_{y \in \mathcal{Y}} \mathcal{D}(\mathbb{E}[\phi_{x \sim \mathcal{T}_y}(x; \theta)], \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)])
$$

$$
= \sum_{y \in \mathcal{Y}} \mathcal{D}\left(\sum_{a_i \in \mathcal{A}} r_y^{a_i} \mathbb{E}[\phi_{x \sim \mathcal{T}_y^{a_i}}(x; \theta)], \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)]\right)
$$

$$
\leq \sum_{y \in \mathcal{Y}} \sum_{a_i \in \mathcal{A}} r_y^{a_i} \mathcal{D}(\mathbb{E}[\phi_{x \sim \mathcal{T}_y^{a_i}}(x; \theta)], \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)])
$$
(9)
$$
\leq \sum_{y \in \mathcal{Y}} \sum_{a_i \in \mathcal{A}} \mathcal{D}(\mathbb{E}[\phi_{x \sim \mathcal{T}_y^{a_i}}(x; \theta)], \mathbb{E}[\phi_{x \sim \mathcal{S}_y}(x; \theta)])
$$
(10)

<span id="page-5-1"></span><span id="page-5-0"></span>
$$
= {\mathcal{L}}_{FairDD}({\mathcal{S}}; \theta, {\mathcal{T}})
$$

Eq. [9](#page-5-0) is obtained according to the Jensen Inequality, and Eq. [10](#page-5-1) is given since group ratios are smaller than one.  $\mathcal{L}_{FairDD}(S; \theta, \mathcal{T})$  serves as the upper bound of  $\mathcal{L}(S; \theta, \mathcal{T})$ , meaning that minimizing  $\mathcal{L}_{FairDD}(S;\theta, \mathcal{T})$  ensures the minimization of  $\mathcal{L}(S;\theta, \mathcal{T})$ . Hence, optimizing S with FairDD can guarantee the comprehensive distributional coverage by bounding  $\mathcal{L}(S; \theta, \mathcal{T})$  tailored for accuracy.

## 5 EXPERIMENT

### 5.1 EXPERIMENT SETUP

Datasets Comprehensive experiments have been conducted on publicly available datasets of diverse biases, including foreground bias (FG), background bias (BG), BG & FG bias, and real-world bias. C-MNIST (FG) is a variant of MNIST [\(LeCun et al., 2010\)](#page-11-5) used to evaluate model fairness, where the handwriting numbers in each class are painted with ten different colors. To correlate the TA (digital number) and PA (color) within the training dataset, each training class is predominantly associated with one color according to the same biased ratio (BR), while the remaining samples are evenly painted with the other nine colors. BR is the ratio of the majority group samples to the total samples across all groups. For the test dataset, we evenly paint the numbers for each class with ten colors to test the model bias trained on  $S$ . C-MNIST (BG) adopts the same operation on the background and keeps the foreground unchanged. Colored-FMNIST (FG) is the modified version of Fashion-MNIST, originally aiming to classify object semantics. Like C-MNIST (FG), we color the objects for the training and test datasets. Colored-FMNIST (BG) paints the background similarly to C-MNIST (BG). CIFAR10-S (BG & FG) introduces a PA by applying grayscale or not to CIFAR10 samples. Following [Wang et al.](#page-13-8) [\(2020\)](#page-13-8), we grayscale a portion of the training images, correlating TA and PA among different classes. For fairness evaluation, we duplicate the test images, apply grayscale to the copies, and add them to the test dataset. We also test FairDD on the real-world facial dataset CelebA, a widely used fairness dataset. We follow the common practice of treating attractive attribute as TA and gender as PA (evaluations on other attributes refer to Appendix [B\)](#page-14-0).

**Baselines & Evaluation metrics** FairDD is a general fairness framework applicable to diverse DDs in DMF. We apply FairDD to diverse DMF approaches including DM method DM [\(Zhao & Bilen,](#page-13-3) [2023\)](#page-13-3) and GM methods DC [\(Zhao et al., 2020\)](#page-13-6), IDC (DC version) [\(Kim et al., 2022\)](#page-11-6), and DREAM (DC version) [\(Liu et al., 2023\)](#page-11-7). To provide an overall evaluation for model bias toward PA, we use  $DEO_M(\downarrow) \in [0, 100]$  and  $DEO_A(\downarrow) \in [0, 100]$  to measure the worst and average fairness levels. Also, we report accuracy(↑) to assess the model's prediction of TA. We also provide a comparison with MTT in Appendix [L.](#page-18-0) Sometimes, we will abuse DM+FairDD and FairDD for clarification.

Implementation details We default to BR of 0.9 for all synthetic original datasets to induce significant PA skew. In Table [24,](#page-29-0) we conduct the ablation study on BR. All baselines are reproduced using official implementations. FairDD doesn't introduce extra hyperparameters or learnable parameters. Experiments are conducted on PyTorch 2.0.0 with a single NVIDIA RTX 3090 24GB GPU.

### 5.2 MAIN RESULTS

We use distilled datasets  $S$  from different DDs to train and evaluate ConvNet with the same parameters, and then report the corresponding fairness and accuracy. *Random* refers to sampling defined IPC from the original dataset to create smaller datasets. Besides, *Whole* means we train the model using the entire training dataset without distillation or sampling.

|                                                                                                                                 | Table 1: Fairness comparison on diverse IPCs. The best results are highlighted in <b>bold</b> .                                                                                                                                                                                                                                                                                                                                                                                                                                                          |  |    |                        |  |  |  |     |            |       |         |           |  |
|---------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|----|------------------------|--|--|--|-----|------------|-------|---------|-----------|--|
| Dataset                                                                                                                         | Methods $_{\text{IDC}}$ Random<br>$\overline{\text{DEO}_{M}}\overline{\text{DEO}}_{A}\overline{\text{DEO}_{M}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{M}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{M}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{M}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{M}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{A}}\overline{\text{DEO}_{A}}$ |  | DM | DM+FairDD DC DC+FairDD |  |  |  | IDC | IDC+FairDD | DREAM | +FairDD | Whole     |  |
| C-MNIST<br>(FG)                                                                                                                 | 10 100.0 98.72 100.0 99.96 17.04 7.95 99.85 65.61 26.75 11.96 100.0 91.45 12.24 6.64 98.99 78.71 11.88 7.21<br>50 100.0 99.58 100.0 91.68 10.05 5.46 46.99 20.55 18.42 8.86 65.34 34.91 9.18 5.94 52.03 26.63 18.37 7.50 10.10 5.89<br>$ 100 $ $ 100.0$ 88.64 $ 99.36$ 66.38 8.17 4.86 $ 45.27$ 17.45 22.32 9.49 $ 64.36$ 35.82 11.88 6.21 $ 69.30$ 33.30 11.88 6.88                                                                                                                                                                                     |  |    |                        |  |  |  |     |            |       |         |           |  |
| C-MNIST<br>(BG)                                                                                                                 | 10   100.0 99.11   100.0 99.97 13.42 6.77   100.0 73.60 20.66 9.94   100.0 88.30 18.61 7.50   100.0 52.06 15.31 6.83<br>$50 100.099.77 100.097.85$ 8.98 5.25 60.66 26.38 20.29 9.90 93.05 42.23 19.66 8.05 64.15 23.30 20.41 9.04<br>$\left 100\right 100.0$ 89.07 $\left 100.0$ 52.23 6.60 4.31 62.63 20.87 32.58 10.40 63.24 27.79 12.24 6.32 44.88 22.86 16.33 7.80                                                                                                                                                                                   |  |    |                        |  |  |  |     |            |       |         | 9.70 5.78 |  |
| C-FMNIST 10 100.0 99.18 100.0 99.05 26.87 16.38 99.40 78.96 46.80 24.01 100.0 97.27 32.33 16.80 100.0 95.17 42.00 20.87<br>(FG) | 50 100.0 94.61 100.0 96.46 24.92 13.74 99.33 67.02 46.67 21.48 100.0 81.93 40.00 17.37 99.67 83.27 47.67 22.33 79.20 41.72<br>$ 100 $ $ 100.0$ $ 94.85 $ $ 100.0$ $ 85.11$ $ 23.83$ $ 12.75 $ $ 99.58$ $ 66.45$ $ 56.68$ $ 23.07 $ $ 100.0$ $ 79.10$ $ 48.33$ $ 17.43 $ $ 97.33$ $ 70.10$ $ 74.00$ $ 40.40$                                                                                                                                                                                                                                              |  |    |                        |  |  |  |     |            |       |         |           |  |
| C-FMNIST<br>(BG)                                                                                                                | 10   100.0 99.40   100.0 99.68 33.05 19.72   100.0 92.91 61.75 34.88   100.0 99.40 42.00 23.80   100.0 94.70 36.00 23.50  <br>50 100.0 98.52 100.0 99.71 24.50 14.47 100.0 75.41 44.60 25.25 100.0 95.60 78.00 34.50 100.0 88.40 34.00 23.70 91.40 51.68<br>$ 100 $ $ 100.0$ $ 96.05 $ $ 100.0$ $ 93.88$ $ 21.95$ $ 13.33 $ $ 99.70$ $ 73.38$ $ 52.75$ $ 23.48 $ $ 100.0$ $ 90.70$ $ 77.00$ $ 36.00 $ $ 100.0$ $ 83.90$ $ 40.00$ $ 23.20$                                                                                                                |  |    |                        |  |  |  |     |            |       |         |           |  |
|                                                                                                                                 | 10 25.04 8.29 59.20 39.31 31.75 8.73 42.23 27.35 22.08 8.22 80.70 48.38 19.90 5.28 51.80 31.43 20.80 7.77<br>CIFAR10-S 50 57.11 28.89 75.13 55.70 18.28 7.35 71.46 45.81 34.39 11.21 92.00 60.56 29.00 9.10 56.80 36.19 14.70 6.53 49.72 33.17<br>100 66.49 43.16 73.81 55.10 14.77 5.89 68.69 48.64 32.70 11.26 92.70 60.93 62.80 25.18 82.30 48.12 12.10 6.06                                                                                                                                                                                          |  |    |                        |  |  |  |     |            |       |         |           |  |
| CelebA                                                                                                                          | $10 10.48$ 9.20 30.01 28.85 9.37 5.71 15.48 14.16 6.64 5.29 34.85 34.48 8.36 4.49 40.75 36.70 9.20 5.36<br>50 22.88 20.32 40.26 38.81 14.08 9.87 24.89 23.83 14.33 12.92 56.74 46.50 22.57 15.15 43.57 38.53 23.62 14.29 24.85 24.16<br>100 18.67 18.01 42.63 41.12 10.93 6.65 29.00 27.52 18.16 17.04 50.99 42.66 28.27 17.63 52.51 39.34 24.87 15.36                                                                                                                                                                                                   |  |    |                        |  |  |  |     |            |       |         |           |  |

Table 1: Fairness comparison on diverse IPCs. The best results are highlighted in **bold**.

<span id="page-6-0"></span>Image /page/6/Figure/2 description: The image displays a grid of generated images, divided into two rows and five columns. The top row is labeled "DM" and the bottom row is labeled "FairDD+DM". Each column presents a different category of generated images: the first two columns show generated digits, the next two columns show generated clothing items, and the last column shows generated faces. The "DM" row shows the results of a standard diffusion model, while the "FairDD+DM" row shows the results of a fair diffusion model. The generated digits in the "DM" row are colorful and varied, while those in the "FairDD+DM" row appear more uniform in color. The clothing items in both rows are diverse in style and color. The faces in the "DM" row are somewhat blurry and varied, while those in the "FairDD+DM" row are also blurry but appear to have a more consistent tone.

(a) C-MNIST (FG). (b) C-MNIST (BG). (c) C-FMNIST (FG). (d) C-FMNIST (BG). (e) CIFAR10-S.

Figure 3: Visualization comparison on S at IPC = 10 for diverse datasets. FairDD successfully mitigates the bias from original datasets in (a) (foreground digital color), (b) background color, (c) foreground object color, (d) background color, and (e) foreground and background grayscale.

FairDD significantly improves the fairness of vanilla DD approaches We provide comprehensive fairness comparisons across various DD paradigms, including DM and DC. As illustrated in Table [21,](#page-27-0) vanilla DDs fail to mitigate the bias present in the original datasets and even exacerbate unfairness towards biased groups. In C-MNIST (FG), the distilled datasets from DM suffer from severe unfairness at IPC=10 compared to *Whole*, with  $DEO<sub>M</sub>$  and  $DEO<sub>A</sub>$  reaching 100.0 and 99.96 vs. 10.10 and 5.89. In some cases, *Random* presents better fairness than vanilla DDs, particularly when dealing with complex objects like CelebA. This suggests that while vanilla DDs effectively condense information into smaller samples, their inductive bias, which favors the majority group, worsens the fairness to the minority group. However, when FairDD is applied to vanilla DDs, there is a significant improvement in fairness performance, with  $DEO<sub>M</sub>$  dropping substantially from 100.0 to 17.04, and  $DEO<sub>A</sub>$  decreasing from 99.96 to 7.95 in C-MNIST (FG). This indicates that FairDD's synchronized matching ensures the equal treatment of each group, effectively mitigating the bias that vanilla DDs exacerbate. FairDD further reduces the bias originally present in the original datasets. For example, DC + FairDD outperforms *Whole* in C-FMNIST (FG) and CIFAR10-S, as well as in the real-world dataset CelebA, achieving the overall improvement on  $DEO<sub>M</sub>$  and  $DEO<sub>A</sub>$  metrics. Similar performance gains are also observed in other baselines. These results underscore the effectiveness and versatility of FairDD. We provide the visualization comparison of resulting  $S$  in Fig. [3.](#page-6-0)

FairDD maintains the comparable and even higher accuracy than vanilla DD approaches A fairness framework must maintain TA accuracy in addition to improving fairness across PA groups. We report the TA accuracy of FairDD in comparison to other baselines in Table [22.](#page-28-0) Compared to

|  | Table 2: Accuracy comparison on diverse IPCs. |  |  |
|--|-----------------------------------------------|--|--|
|  |                                               |  |  |

Table 3: Cross-arch. comparison.

| Methods  <br>Datasets  |                 | Acc.                    | Acc.                      | Acc.                                                         | Acc.                              | Acc.                                 | Acc.                              | Acc.                    | Acc.                    | IPC Random   DM + FairDD   DC + FairDD   IDC + FairDD   DREAM + FairDD   Whole<br>Acc. | Acc.  | Method          | <b>Cross</b><br>arch.                                                                               | $DEO_M$ $\overline{DEO_A}$ Acc. $\overline{DEO_M}$ $\overline{DEO_A}$ Acc. | DM                                                                           |                                     | DM+FairDD   |                                                      |
|------------------------|-----------------|-------------------------|---------------------------|--------------------------------------------------------------|-----------------------------------|--------------------------------------|-----------------------------------|-------------------------|-------------------------|----------------------------------------------------------------------------------------|-------|-----------------|-----------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------|------------------------------------------------------------------------------|-------------------------------------|-------------|------------------------------------------------------|
| C-MNIST<br>(FG)        | 10<br>50<br>100 | 30.75<br>47.38<br>67.41 | 25.01<br>56.84<br>78.04   | 94.61<br>96.58<br>96.79                                      | 71.41<br>90.54                    | 90.62<br>92.68<br>$ 91.64 \t 93.23 $ | 53.06<br>88.55<br>$ 90.39\rangle$ | 95.67<br>96.77<br>97.11 | 75.04<br>91.02<br>88.87 | 94.04<br>94.59<br>95.16                                                                | 97.71 | C-MNIST<br>(FG) | ConvNet<br>AlexNet<br>VGG11<br>ResNet18 100.0 96.00 52.05 8.40                                      |                                                                            | 100.0 91.68 56.84 10.05<br>100.0 98.82 44.02 10.35<br>99.70 70.73 75.22 9.55 |                                     |             | 5.46 96.58<br>6.16 96.12<br>5.39 96.80<br>4.63 97.13 |
| C-MNIST<br>(BG)        | 10<br>50<br>100 | 27.95<br>45.52<br>67.28 | 123.40<br>47.74<br>79.87  | 94.88<br>96.86<br>97.33                                      | 65.91<br>88.53<br>$ 90.20\rangle$ | 90.84<br>92.20<br>92.73              | 162.09<br>86.14<br>89.66          | 94.84<br>95.29<br>95.84 | 79.81<br>89.24<br>90.70 | 93.54<br>93.20<br>94.06                                                                | 97.80 | C-FMNIST        | Mean   99.93 89.31 57.03 9.59<br>ConvNet   100.0 99.71 36.27 24.50 14.47 79.07<br>AlexNet           |                                                                            | 100.0 99.75 22.72 20.60                                                      |                                     | 14.11 76.14 | 5.41 96.66                                           |
| C-FMNIST<br>(FG)       | 10<br>50<br>100 | 32.80<br>42.48<br>55.31 | 133.35<br>49.94           | 77.09<br>82.11<br>57.99 83.25                                | 160.77<br>69.08                   | 76.01<br>75.83<br>$ 68.84$ 74.91     | 44.08<br>64.45<br> 66.37          | 79.66<br>80.80<br>80.28 | 49.72<br>65.69<br>68.25 | 77.24<br>78.79<br>78.51                                                                | 82.94 | (BG)            | VGG11<br>ResNet18 100.0 99.78 23.37 22.50 14.96 75.21<br>Mean   100.0 99.25 31.37 22.30 14.73 77.25 |                                                                            |                                                                              | 100.0 97.77 43.11 21.60 14.36 78.57 |             |                                                      |
| C-FMNIST<br>(BG)       | 10<br>50<br>100 | 24.96<br>34.92<br>44.87 | 22.26<br>36.27<br>49.30   | 71.10<br>79.07<br>80.63                                      | 47.32<br>60.58<br> 62.70          | 68.51<br>75.80<br>71.76              | 37.59<br>46.20<br>48.61           | 72.67<br>73.72<br>73.18 | 45.30<br>53.62<br>53.32 | 71.56<br>72.80<br>73.00                                                                | 77.97 | CIFAR10-S       | ConvNet 75.13 55.70 45.02 18.28<br>AlexNet<br>VGG11<br>ResNet18 76.23 54.35 38.03 16.44             |                                                                            | 75.30 52.57 36.09 15.84<br>61.48 44.05 43.23 11.51                           |                                     |             | 7.35 58.84<br>5.12 49.16<br>4.16 52.65<br>5.14 50.93 |
| CIFAR <sub>10</sub> -S | 10<br>50<br>100 | 23.60<br>36.46<br>39.34 | 37.88<br>45.02            | 45.17<br>58.84<br>$ 48.11 \t\t 61.33 \t\t  42.73 \t\t 51.74$ | 37.88 <br>41.28                   | 41.82<br>49.26                       | 48.30<br>47.26<br>47.27           | 56.40<br>57.84<br>56.98 | 55.09<br>57.59<br>57.14 | 58.40<br>61.85<br>62.70                                                                | 69.78 |                 | Mean<br>ConvNet<br>AlexNet                                                                          | 72.04 51.67 40.59 15.27<br>32.51                                           | 40.26 38.81 64.61 14.08<br>31.62 63.10 9.38                                  |                                     | 9.87        | 5.44 52.90<br>68.50<br>5.75 64.24                    |
| CelebA                 | 10<br>50<br>100 | 54.51<br>55.99<br>60.62 | 61.79<br> 64.61<br> 65.13 | 64.37<br>68.50<br>68.84                                      | 57.19<br>60.16<br>62.53           | 57.63<br>59.89<br>61.89              | 61.49<br>60.75<br> 64.04          | 63.54<br>66.89<br>67.24 | 64.38<br>64.62<br>62.58 | 66.26<br>68.26<br>64.12                                                                | 74.09 | CelebA          | VGG11<br>ResNet18 25.60 24.93 60.32 6.72<br>Mean 31.10 30.25 62.40 9.78                             |                                                                            | 26.03 24.63 61.57 8.95                                                       |                                     |             | 6.32 62.05<br>4.29 61.80<br>6.58 64.15               |

*Random*, training the model by vanilla DDs yields better performance. This shows that vanilla DDs capture the informative patterns of majority groups, improving their TA accuracy. However, by focusing on dominant patterns in majority groups, they neglect the important patterns in minority groups within the training datasets. Thus, their representation coverage is limited. In contrast, FairDD proposes synchronized matching to push the S to cover each group, and as a result, the generated S retains key patterns of all groups and achieves comprehensive coverage. For example, DM obtains 25.01 at IPC = 10 on C-MNIST (FG), and its accuracy boosts to 94.61 when applying FairDD. In real-world CelebA, FairDD obtains comparable performance for DC and presents superiority over vanilla DDs. These demonstrate that FairDD could mitigate the bias without compromising accuracy.

Generalization to diverse architectures Here, we investigate the cross-model generalization of FairDD, where ConvNet is used to condense datasets, and we evaluate  $S$  on other architectures, including AlexNet, VGG11, and ResNet18. We compare DM and FairDD across four datasets at  $IPC = 50$ , evaluating performance against BG, FG, BG & FG, and real-world biases. As shown in Table [23,](#page-29-1) among these architectures, FairDD achieves DEO<sub>M</sub> of 10.05, 10.35, 9.55, and 8.40 on C-MNIST (FG), DEO<sup>A</sup> of 14.47, 14.11, 14.36, and 14.96 on C-FMNIST (BG), and accuracy of 58.84, 49.16, 52.65, and 50.93 on CIFAR10-S. These steady results suggest that  $S$  generated by FairDD are not restricted to the model used for distillation but generalize well across diverse architectures. Additionally, with the model capacity increasing, the model generally tends to be more fair to all groups. However, the accuracy sometimes decreases, such as when it drops from 58.84 (ConvNet) to 50.93 (ResNet18) in CIFAR10-S and from 68.50 (ConvNet) to 61.80 (ResNet18) in CelebA. We assume that while increased attention from larger models can lead to accuracy gains for minority groups, it may limit the representations for majority groups at certain levels. The accuracy gains for minority groups may be smaller than the accuracy losses for majority groups, particularly in larger models that have limited potential improvement in recognizing minority groups. As a result, overall accuracy may decrease despite fairness improvement.

### 5.3 RESULT ANALYSIS

Visualization analysis on representation coverage We investigate whether the FairDD effectively covers the whole distribution of original datasets. For this purpose, we first feed the original training set into the randomly initialized network used in the distillation, to extract the corresponding features. Subsequently, we use the same network to extract features of distilled dataset  $S$  from DM and FairDD. As shown in Fig. [4\(a\),](#page-7-0) the synthetic samples in vanilla DDs almost locate the majority group for

Image /page/7/Figure/7 description: Two t-SNE plots are shown side-by-side. Each plot displays clusters of points colored in various shades of blue, light blue, pink, purple, green, yellow, and orange. Several dark blue star shapes are overlaid on the clusters in both plots, indicating specific points of interest. The overall arrangement of the clusters and the distribution of the star markers are similar between the two plots, suggesting a comparison or a slight variation in the data representation.

<span id="page-7-0"></span>

<span id="page-7-1"></span>(a) The  $S$  distribution of generated by DM. (b) The  $S$  distribution of generated by FairDD.

Figure 4: Feature coverage comparison on TA between DM and DM+FairDD. The training and synthetic dataset features are extracted by  $\phi_{\theta}$ . We visualize one class and make the remaining classes transparent for presentation. The  $S$  generated by DM and FairDD are marked by stars in (a) and (b).

optimizing the original alignment objective. In this case, vanilla DDs neglect to condense the key

<span id="page-8-0"></span>Image /page/8/Figure/0 description: The image displays four t-SNE plots, each labeled with a letter from (a) to (d). Plot (a) is titled "PA t-SNE of DM." Plot (b) is titled "PA t-SNE of FairDD." Plot (c) is titled "TA t-SNE of DM." Plot (d) is titled "TA t-SNE of FairDD." Each plot shows a scatter of colored points, with different colors representing different clusters or categories. Plots (a) and (c) show more distinct clusters of points, while plots (b) and (d) show a more mixed distribution of colors within the clusters.

<span id="page-8-1"></span>Figure 5: T-SNE visualization comparison on PA and TA between DM and DM+FairDD. Each color represents distinct PA groups in (a) and (b). (c) and (d) use different colors to represent different PA. In (a), DM shows obvious distinctiveness towards different PA, whereas in (b), DM+FairDD eliminates the recognition of PA. As illustrated in (c) and (d), DM+FairDD optimizes the model to produce compact TA representations, but DM fails to cluster TA from the same class.

<span id="page-8-3"></span><span id="page-8-2"></span>

| Table 4: Ablation on BR at IPC = $50$ . |                            |       |                                                                                                              |                                        |            | Table 5: Ablation on initialization at IPC = $50$ . |                                  |       |                                                  |       |                                                                                                    |                            |                          |  |
|-----------------------------------------|----------------------------|-------|--------------------------------------------------------------------------------------------------------------|----------------------------------------|------------|-----------------------------------------------------|----------------------------------|-------|--------------------------------------------------|-------|----------------------------------------------------------------------------------------------------|----------------------------|--------------------------|--|
| Methods<br>Dataset                      | <b>BR</b>                  | DM    | $\overline{DEO_M}$ $\overline{DEO_A}$ Acc. $\overline{DEO_M}$ $\overline{DEO_A}$ Acc.                        | DM+FairDD                              |            | Methods<br>Dataset                                  | Init.                            |       | DM                                               |       | $DEOM$ $\overline{DEOA}$ $\overline{Acc}$ . $\overline{DEOM}$ $\overline{DEOA}$ $\overline{Acc}$ . | DM+FairDD                  |                          |  |
| C-MNIST<br>(FG)                         | 0.85 99.54<br>0.90         |       | 70.13 76.24 10.13<br>100.0 91.68 56.84 10.05<br>$\begin{bmatrix} 0.95 \end{bmatrix}$ 100.0 100.0 33.73 10.30 | 5.20 96.62<br>5.46 96.58<br>5.84 96.05 |            | C-MNIST<br>(FG)                                     | Random<br><b>Noise</b><br>Hybrid |       | 100.0 99.64 41.45 9.33<br>100.0 99.06 39.97 9.03 |       | 100.0 91.68 56.84 10.05                                                                            | 5.33 96.27                 | 5.46 96.58<br>5.28 96.06 |  |
| <b>C-FMNIST</b><br>(BG)                 | $0.85$ 100.0<br>0.90 100.0 |       | 95.54 46.14 23.75 13.85 79.61<br>99.71 36.27 24.50 14.47 79.07<br>0.95 100.0 99.79 26.30 29.15 17.72 78.46   |                                        |            | <b>C-FMNIST</b><br>(BG)                             | Random<br><b>Noise</b><br>Hybrid | 100.0 | 99.71                                            |       | 36.27 24.50<br>100.0 99.67 22.92 23.00<br>100.0 99.68 26.38 21.45 14.34 79.19                      | 14.47 79.07<br>14.40 78.84 |                          |  |
| CIFAR10-S 0.90 75.13 55.70 45.02 18.28  | 0.85 71.75<br>0.95         | 50.11 | 46.99 16.44<br>75.43 58.58 43.56 17.49 7.10 58.18                                                            | 7.35 58.84                             | 6.58 59.12 | CIFAR <sub>10-S</sub>                               | Randoml<br>Noise<br>Hybrid       | 75.13 | 55.70                                            | 45.02 | 18.28<br>55.28 37.26 46.97 16.15<br>65.59 46.18 45.16 17.30                                        | 6.13 56.41<br>6.71 56.78   | 7.35 58.84               |  |

patterns of minority groups. This leads to the information loss of minority groups in  $S$ . FairDD achieves overall coverage for both majority and minority groups in Fig. [4\(b\).](#page-7-1) This is because FairDD introduces synchronized matching to reformulate the distillation objective for aligning the PA-wise groups rather than being dominated by the majority group like vanilla DDs. In doing so, FairDD avoids  $S$  collapsing into the majority group and retains informative patterns from all groups.

Visualization analysis on fairness and accuracy To intuitively present the effectiveness of FairDD, we train  $g_{\psi}$  using S of C-MNIST (FG) distilled by DM and FairDD, and then extract the features from the test dataset. Different colors paint these resulting features according to PA and TA, respectively. As shown in Figs. [5\(a\)](#page-8-0) and [5\(b\),](#page-8-1) features with the same PA tend to form a cluster, indicating that the model trained on DM is sensitive to PA and thus failing to guarantee fairness among all PA. In contrast with DM, the feature distributions in Fig. [5\(b\)](#page-8-1) exhibit nearly complete overlaps across all PA. It shows that the model trained on FairDD is agnostic to PA and does not exhibit bias towards these PA. Besides the PA fairness, we also study the feature distribution from the TA perspective. Fig. [5\(c\)](#page-8-2) shows that features belonging to one TA scatter and fail to provide compact representations for one class. The failure of DM can be attributed to model bias toward PA. Combined with Fig. [5\(a\),](#page-8-0) it can be observed that PA has a stronger influence on the feature distribution compared to TA. As a result, PA-wise representations are tightly clustered, but representations from the same TA are divided into PA-wise parts. In contrast, FairDD proposes synchronized matching effectively mitigates this by treating each PA group equally within one TA. The equal treatment allows different PA groups within the same TA to cluster more easily, leading to more compact representations that benefit capturing class semantics in Fig. [5\(d\).](#page-8-3) These results highlight the superiority of FairDD in improving PA fairness and TA accuracy. We also provide visualization analysis on  $S$  generation in Appendix [M.](#page-19-0) Additional computation overhead is provided in Appendix [C.](#page-14-1)

## 5.4 ABLATION STUDY

Ablation on biased ratio of original datasets BR reflects the extent of unfairness in the original datasets and indicates the level of PA skew that the distillation process of  $S$  will encounter. We investigate the impact of BR values on fairness performance by setting BR to  $\{0.85, 0.90, 0.95\}$  on C-MNIST (FG), C-FMNIST (BG), and CIFAR10-S. The results at IPC = 50 in Table [24](#page-29-0) show that DM is sensitive to the BR of original datasets, with its  $DEO<sub>M</sub>$  decreasing from 70.13 to 100.0 as BR increases from 0.85 to 0.95. A similar trend is observed in other datasets. Compared to DM, FairDD maintains consistent fairness and accuracy levels across different biases. This is attributed to the synchronized matching, which explicitly aligns each PA-wise subtarget, reducing sensitivity to group-specific sample numbers. This shows FairDD's robustness to PA skew in the original datasets.

Ablation on initialization of synthetic images The initialization of  $S$  determines the prior information obtained by DDs. We examine the effect of different initialization using three strategies: *random*: randomly drawing samples from the original datasets to initialize  $S$ ; *Noise*: using noise obeying the standard normal distribution for initialization; and *hybrid*: selecting each initialization sample with equal probability to follow either the *random* or *Noise* strategy. In Table [25,](#page-30-0) the DEO<sup>M</sup> and DEO<sub>A</sub> metrics of DM have largely fluctuate under these initialization strategies. This suggests that DM fails to incorporate sufficient distilled information into  $S$ , making it overly dependent on the prior information of S. In contrast, FairDD achieves robust performance in both fairness and accuracy, indicating that the informative patterns during distillation dominate the generation of  $S$ .

# 6 RELATED WORK

Dataset distillation Dataset distillation has been broadly applied to many important fields [\(Lee](#page-11-8) [et al., 2023;](#page-11-8) [He et al., 2024;](#page-11-9) [Feng et al., 2023;](#page-10-7) [Chen et al., 2023\)](#page-10-8). The first work [\(Wang et al.,](#page-13-1) [2018\)](#page-13-1) attempts to formulate dataset distillation as a bi-level optimization problem. However, the two folds of the optimization process are time-consuming. Neural tangent kernel [\(Jacot et al., 2018\)](#page-11-10) are utilized to obtain the close form of the inner loop [\(Nguyen et al., 2021;](#page-12-7) [Loo et al., 2022;](#page-11-11) [Zhou et al.,](#page-13-9) [2022\)](#page-13-9). Some works propose surrogate objectives to achieve comparable even better performance, including matching-based methods like GM [\(Zhao et al., 2020;](#page-13-6) [Zhao & Bilen, 2021;](#page-13-2) [Lee et al.,](#page-11-12) [2022a\)](#page-11-12), DM [\(Zhao & Bilen, 2023;](#page-13-3) [Wang et al., 2022;](#page-12-2) [Zhao & Bilen, 2022\)](#page-13-7), TM [\(Cazenavette](#page-10-2) [et al., 2022;](#page-10-2) [Cui et al., 2022\)](#page-10-3), soft label learning [\(Bohdal et al., 2020;](#page-10-9) [Sucholutsky & Schonlau,](#page-12-8) [2021\)](#page-12-8) and factorization [\(Kim et al., 2022;](#page-11-6) [Deng & Russakovsky, 2022;](#page-10-10) [Liu et al., 2022;](#page-11-13) [Lee et al.,](#page-11-12) [2022a\)](#page-11-12). [Cui et al.](#page-10-6) [\(2024\)](#page-10-6) and [Zhao et al.](#page-13-4) [\(2024\)](#page-13-4) focus on reducing the bias to improve the classification performance [\(Vahidian et al., 2024;](#page-12-6) [Wang et al., 2024\)](#page-13-5). In summary, current DD approaches only pursue classification accuracy that models trained on synthetic datasets, while they neglect fairness concerning PA. Therefore, we propose FairDD to improve fairness without sacrificing TA accuracy.

Visual fairness With the advancement of computer vision, fair predictions without discrimination towards minority groups have become a crucial topic (Caton  $\&$  Haas, 2024). According to the stage of bias mitigation, the research field of fairness algorithm [\(Bellamy et al., 2019\)](#page-10-12) can be classified into three branches: Pre-processing [\(Creager et al., 2019;](#page-10-13) [Louizos et al., 2015;](#page-12-9) [Quadrianto et al., 2019;](#page-12-10) [Sattigeri et al., 2019\)](#page-12-11), In-processing [\(Agarwal et al., 2018;](#page-10-14) [Jiang & Nachum, 2020;](#page-11-14) [Zafar et al., 2017;](#page-13-10) [Zhang et al., 2018;](#page-13-11) [Jung et al., 2021;](#page-11-4) [Wang et al., 2020;](#page-13-8) [Jung et al., 2022;](#page-11-15) [Zemel et al., 2013\)](#page-13-12), and Post-processing [\(Alghamdi et al., 2020;](#page-10-15) [Hardt et al., 2016\)](#page-10-16). Our research falls within Pre-processing branch. Pre-processing aims to generate a fair version of datasets for downstream tasks. Several related works frame this issue as a data-to-data translation problem, utilizing generative models to produce fairer datasets concerning protected groups [\(Sattigeri et al., 2019;](#page-12-11) [Quadrianto et al., 2019\)](#page-12-10). However, unlike the traditional fairness approach that primarily focuses on reducing bias in original datasets [Subramanian et al.](#page-12-12) [\(2021\)](#page-12-12); [Tarzanagh et al.](#page-12-13) [\(2023\)](#page-12-13); [Vogel et al.](#page-12-14) [\(2020\)](#page-12-14); [Rangwani et al.](#page-12-15) [\(2022\)](#page-12-15), our work aims to integrate fairness into DD. Our objective is to mitigate the bias of original datasets while simultaneously condensing them into smaller and more informative counterparts.

# 7 CONCLUSION

This paper reveals for the first time that vanilla DDs fail to mitigate the bias of original datasets and even exacerbate the bias. To address the problem, we propose a unified fair dataset distillation framework called FairDD, broadly applicable to various DDs in DMF. FairDD requires no modifications to the architectures of vanilla DDs and introduces an easy-to-implement yet effective attribute-wise matching. This method mitigates the dominance of the majority group and ensures that synthetic datasets equally incorporate representative patterns with all protected attributes from both majority groups and minority groups. By doing so, FairDD guarantees the fairness of synthetic datasets while maintaining their representativeness for image recognition. We provide extensive theoretical analysis and empirical results to demonstrate the superiority of FairDD.

Limitation Since FairDD relies on PA's prior information to conduct attribute-wise matching, it is valuable to explore the scenario where PA is unavailable [Liu et al.](#page-11-16) [\(2021\)](#page-11-16). A potential solution is to generate pseudo-labels to guide FairDD through self-supervised learning or unsupervised learning.

**Board impact** This paper aims to improve data efficiency and enhance model fairness in modern machine learning, fully compliant with legal regulations. Since training a fair model from scripts with extensive data is time-consuming, our work in providing a fair condensed dataset for effective model training can have significant societal impacts. We hope our research raises attention to achieve both fairness and accuracy for dataset distillation in academia and industry.

## REFERENCES

- <span id="page-10-14"></span>Alekh Agarwal, Alina Beygelzimer, Miroslav Dudík, John Langford, and Hanna Wallach. A reductions approach to fair classification. In *International Conference on Machine Learning*, pp. 60–69. PMLR, 2018.
- <span id="page-10-15"></span>Wael Alghamdi, Shahab Asoodeh, Hao Wang, Flavio P Calmon, Dennis Wei, and Karthikeyan Natesan Ramamurthy. Model projection: Theory and applications to fair machine learning. In *2020 IEEE International Symposium on Information Theory (ISIT)*, pp. 2711–2716. IEEE, 2020.
- <span id="page-10-12"></span>Rachel KE Bellamy, Kuntal Dey, Michael Hind, Samuel C Hoffman, Stephanie Houde, Kalapriya Kannan, Pranay Lohia, Jacquelyn Martino, Sameep Mehta, Aleksandra Mojsilovic, et al. Ai ´ fairness 360: An extensible toolkit for detecting and mitigating algorithmic bias. *IBM Journal of Research and Development*, 63(4/5):4–1, 2019.
- <span id="page-10-9"></span>Ondrej Bohdal, Yongxin Yang, and Timothy Hospedales. Flexible dataset distillation: Learn labels instead of images. *arXiv preprint arXiv:2006.08572*, 2020.
- <span id="page-10-11"></span>Simon Caton and Christian Haas. Fairness in machine learning: A survey. *ACM Computing Surveys*, 56(7):1–38, 2024.
- <span id="page-10-2"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 4750–4759, 2022.
- <span id="page-10-8"></span>Xuxi Chen, Yu Yang, Zhangyang Wang, and Baharan Mirzasoleiman. Data distillation can be like vodka: Distilling more times for better quality. *arXiv preprint arXiv:2310.06982*, 2023.
- <span id="page-10-5"></span>Ming-Yu Chung, Sheng-Yen Chou, Chia-Mu Yu, Pin-Yu Chen, Sy-Yen Kuo, and Tsung-Yi Ho. Rethinking backdoor attacks on dataset distillation: A kernel method perspective. *arXiv preprint arXiv:2311.16646*, 2023.
- <span id="page-10-13"></span>Elliot Creager, David Madras, Jörn-Henrik Jacobsen, Marissa Weis, Kevin Swersky, Toniann Pitassi, and Richard Zemel. Flexibly fair representation learning by disentanglement. In *International conference on machine learning*, pp. 1436–1445. PMLR, 2019.
- <span id="page-10-3"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. *arXiv preprint arXiv:2211.10586*, 2022.
- <span id="page-10-6"></span>Justin Cui, Ruochen Wang, Yuanhao Xiong, and Cho-Jui Hsieh. Ameliorate spurious correlations in dataset condensation. *arXiv preprint arXiv:2406.06609*, 2024.
- <span id="page-10-10"></span>Zhiwei Deng and Olga Russakovsky. Remember the past: Distilling datasets into addressable memories for neural networks. *arXiv preprint arXiv:2206.02916*, 2022.
- <span id="page-10-1"></span>Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, Jakob Uszkoreit, and Neil Houlsby. An image is worth 16x16 words: Transformers for image recognition at scale. *ArXiv*, abs/2010.11929, 2020. URL [https://api.semanticscholar.org/CorpusID:](https://api.semanticscholar.org/CorpusID:225039882) [225039882](https://api.semanticscholar.org/CorpusID:225039882).
- <span id="page-10-7"></span>Yunzhen Feng, Shanmukha Ramakrishna Vedantam, and Julia Kempe. Embarrassingly simple dataset distillation. In *The Twelfth International Conference on Learning Representations*, 2023.
- <span id="page-10-4"></span>Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023.
- <span id="page-10-16"></span>Moritz Hardt, Eric Price, and Nati Srebro. Equality of opportunity in supervised learning. *Advances in neural information processing systems*, 29, 2016.
- <span id="page-10-0"></span>Kaiming He, X. Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. *2016 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, pp. 770–778, 2015. URL <https://api.semanticscholar.org/CorpusID:206594692>.

- <span id="page-11-3"></span>Yang He and Joey Tianyi Zhou. Data-independent module-aware pruning for hierarchical vision transformers. In *The Twelfth International Conference on Learning Representations*, 2024. URL <https://openreview.net/forum?id=7Ol6foUi1G>.
- <span id="page-11-9"></span>Yang He, Lingao Xiao, Joey Tianyi Zhou, and Ivor Tsang. Multisize dataset condensation. In *The Twelfth International Conference on Learning Representations*, 2024. URL [https://](https://openreview.net/forum?id=FVhmnvqnsI) [openreview.net/forum?id=FVhmnvqnsI](https://openreview.net/forum?id=FVhmnvqnsI).
- <span id="page-11-10"></span>Arthur Jacot, Franck Gabriel, and Clément Hongler. Neural tangent kernel: Convergence and generalization in neural networks. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-11-14"></span>Heinrich Jiang and Ofir Nachum. Identifying and correcting label bias in machine learning. In *International Conference on Artificial Intelligence and Statistics*, pp. 702–712. PMLR, 2020.
- <span id="page-11-4"></span>Sangwon Jung, Donggyu Lee, Taeeon Park, and Taesup Moon. Fair feature distillation for visual recognition. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 12115–12124, 2021.
- <span id="page-11-15"></span>Sangwon Jung, Sanghyuk Chun, and Taesup Moon. Learning fair classifiers with partially annotated group labels. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 10348–10357, 2022.
- <span id="page-11-6"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pp. 11102–11118. PMLR, 2022.
- <span id="page-11-0"></span>Alexander Kirillov, Eric Mintun, Nikhila Ravi, Hanzi Mao, Chloe Rolland, Laura Gustafson, Tete Xiao, Spencer Whitehead, Alexander C Berg, Wan-Yen Lo, et al. Segment anything. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pp. 4015–4026, 2023.
- <span id="page-11-5"></span>Yann LeCun, Corinna Cortes, Chris Burges, et al. Mnist handwritten digit database, 2010.
- <span id="page-11-8"></span>Dong Bok Lee, Seanie Lee, Joonho Ko, Kenji Kawaguchi, Juho Lee, and Sung Ju Hwang. Selfsupervised dataset distillation for transfer learning. In *The Twelfth International Conference on Learning Representations*, 2023.
- <span id="page-11-12"></span>Hae Beom Lee, Dong Bok Lee, and Sung Ju Hwang. Dataset condensation with latent space knowledge factorization and sharing. *arXiv preprint arXiv:2208.10494*, 2022a.
- <span id="page-11-2"></span>Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pp. 12352–12364. PMLR, 2022b.
- <span id="page-11-1"></span>Junnan Li, Dongxu Li, Caiming Xiong, and Steven Hoi. Blip: Bootstrapping language-image pretraining for unified vision-language understanding and generation. In *International conference on machine learning*, pp. 12888–12900. PMLR, 2022.
- <span id="page-11-16"></span>Evan Zheran Liu, Behzad Haghgoo, Annie S. Chen, Aditi Raghunathan, Pang Wei Koh, Shiori Sagawa, Percy Liang, and Chelsea Finn. Just train twice: Improving group robustness without training group information. *ArXiv*, abs/2107.09044, 2021. URL [https://api.semanticscholar.](https://api.semanticscholar.org/CorpusID:235825419) [org/CorpusID:235825419](https://api.semanticscholar.org/CorpusID:235825419).
- <span id="page-11-13"></span>Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *arXiv preprint arXiv:2210.16774*, 2022.
- <span id="page-11-7"></span>Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Hua Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *2023 IEEE/CVF International Conference on Computer Vision (ICCV)*, pp. 17268–17278, 2023. URL [https://api.semanticscholar.](https://api.semanticscholar.org/CorpusID:257232785) [org/CorpusID:257232785](https://api.semanticscholar.org/CorpusID:257232785).
- <span id="page-11-11"></span>Noel Loo, Ramin Hasani, Alexander Amini, and Daniela Rus. Efficient dataset distillation using random feature approximation. *arXiv preprint arXiv:2210.12067*, 2022.

- <span id="page-12-3"></span>Noel Loo, Ramin Hasani, Mathias Lechner, Alexander Amini, and Daniela Rus. Understanding reconstruction attacks with the neural tangent kernel and dataset distillation. *arXiv preprint arXiv:2302.01428*, 2023.
- <span id="page-12-9"></span>Christos Louizos, Kevin Swersky, Yujia Li, Max Welling, and Richard Zemel. The variational fair autoencoder. *arXiv preprint arXiv:1511.00830*, 2015.
- <span id="page-12-5"></span>Yao Lu, Jianyang Gu, Xuguang Chen, Saeed Vahidian, and Qi Xuan. Exploring the impact of dataset bias on dataset distillation. *ArXiv*, abs/2403.16028, 2024. URL [https://api.](https://api.semanticscholar.org/CorpusID:268680434) [semanticscholar.org/CorpusID:268680434](https://api.semanticscholar.org/CorpusID:268680434).
- <span id="page-12-4"></span>Ninareh Mehrabi, Fred Morstatter, Nripsuta Saxena, Kristina Lerman, and Aram Galstyan. A survey on bias and fairness in machine learning. *ACM computing surveys (CSUR)*, 54(6):1–35, 2021.
- <span id="page-12-7"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021.
- <span id="page-12-0"></span>Maxime Oquab, Timothée Darcet, Théo Moutakanni, Huy Vo, Marc Szafraniec, Vasil Khalidov, Pierre Fernandez, Daniel Haziza, Francisco Massa, Alaaeldin El-Nouby, et al. Dinov2: Learning robust visual features without supervision. *arXiv preprint arXiv:2304.07193*, 2023.
- <span id="page-12-10"></span>Novi Quadrianto, Viktoriia Sharmanska, and Oliver Thomas. Discovering fair representations in the data domain. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 8227–8236, 2019.
- <span id="page-12-1"></span>Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, et al. Learning transferable visual models from natural language supervision. In *International conference on machine learning*, pp. 8748–8763. PMLR, 2021.
- <span id="page-12-15"></span>Harsh Rangwani, Sumukh K Aithal, Mayank Mishra, and R. Venkatesh Babu. Escaping saddle points for effective generalization on class-imbalanced data. *ArXiv*, abs/2212.13827, 2022. URL <https://api.semanticscholar.org/CorpusID:255186001>.
- <span id="page-12-11"></span>Prasanna Sattigeri, Samuel C Hoffman, Vijil Chenthamarakshan, and Kush R Varshney. Fairness gan: Generating datasets with fairness properties using a generative adversarial network. *IBM Journal of Research and Development*, 63(4/5):3–1, 2019.
- <span id="page-12-12"></span>Shivashankar Subramanian, Afshin Rahimi, Timothy Baldwin, Trevor Cohn, and Lea Frermann. Fairness-aware class imbalanced learning. In *Conference on Empirical Methods in Natural Language Processing*, 2021. URL [https://api.semanticscholar.org/CorpusID:](https://api.semanticscholar.org/CorpusID:237593022) [237593022](https://api.semanticscholar.org/CorpusID:237593022).
- <span id="page-12-8"></span>Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *2021 International Joint Conference on Neural Networks (IJCNN)*, pp. 1–8. IEEE, 2021.
- <span id="page-12-13"></span>Davoud Ataee Tarzanagh, Bojian Hou, Boning Tong, Qi Long, and Li Shen. Fairness-aware class imbalanced learning on multiple subgroups. *Proceedings of machine learning research*, 216:2123– 2133, 2023. URL <https://api.semanticscholar.org/CorpusID:260843665>.
- <span id="page-12-6"></span>Saeed Vahidian, Mingyu Wang, Jianyang Gu, Vyacheslav Kungurtsev, Wei Jiang, and Yiran Chen. Group distributionally robust dataset distillation with risk minimization. *arXiv preprint arXiv:2402.04676*, 2024.
- <span id="page-12-14"></span>Robin Vogel, Mastane Achab, Stéphan Clémençon, and Charles Tillier. Weighted empirical risk minimization: Sample selection bias correction based on importance sampling. *ArXiv*, abs/2002.05145, 2020. URL <https://api.semanticscholar.org/CorpusID:211082802>.
- <span id="page-12-2"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 12196–12205, 2022.

- <span id="page-13-5"></span>Shaobo Wang, Yantai Yang, Qilong Wang, Kaixin Li, Linfeng Zhang, and Junchi Yan. Not all samples should be utilized equally: Towards understanding and improving dataset distillation, 2024. URL <https://arxiv.org/abs/2408.12483>.
- <span id="page-13-1"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-13-8"></span>Zeyu Wang, Klint Qinami, Ioannis Christos Karakozis, Kyle Genova, Prem Nair, Kenji Hata, and Olga Russakovsky. Towards fairness in visual recognition: Effective strategies for bias mitigation. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 8919–8928, 2020.
- <span id="page-13-10"></span>Muhammad Bilal Zafar, Isabel Valera, Manuel Gomez Rogriguez, and Krishna P Gummadi. Fairness constraints: Mechanisms for fair classification. In *Artificial intelligence and statistics*, pp. 962–970. PMLR, 2017.
- <span id="page-13-12"></span>Rich Zemel, Yu Wu, Kevin Swersky, Toni Pitassi, and Cynthia Dwork. Learning fair representations. In *International conference on machine learning*, pp. 325–333. PMLR, 2013.
- <span id="page-13-11"></span>Brian Hu Zhang, Blake Lemoine, and Margaret Mitchell. Mitigating unwanted biases with adversarial learning. In *Proceedings of the 2018 AAAI/ACM Conference on AI, Ethics, and Society*, pp. 335– 340, 2018.
- <span id="page-13-2"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pp. 12674–12685. PMLR, 2021.
- <span id="page-13-7"></span>Bo Zhao and Hakan Bilen. Synthesizing informative training samples with gan. In *NeurIPS 2022 Workshop on Synthetic Data for Empowering ML Research*, 2022.
- <span id="page-13-3"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pp. 6514–6523, 2023.
- <span id="page-13-6"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.
- <span id="page-13-4"></span>Zhenghao Zhao, Haoxuan Wang, Yuzhang Shang, Kai Wang, and Yan Yan. Distilling long-tailed datasets, 2024. URL <https://arxiv.org/abs/2408.14506>.
- <span id="page-13-0"></span>Qihang Zhou, Guansong Pang, Yu Tian, Shibo He, and Jiming Chen. Anomalyclip: Object-agnostic prompt learning for zero-shot anomaly detection. In *The Twelfth International Conference on Learning Representations*, 2023.
- <span id="page-13-9"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *arXiv preprint arXiv:2206.00719*, 2022.

<span id="page-14-2"></span>

| Datasets      | TA              | PA               | TA number | PA number | Training set size | Test set size | BR in Training set           | BR in Test set | Condensed ratio |        |       |
|---------------|-----------------|------------------|-----------|-----------|-------------------|---------------|------------------------------|----------------|-----------------|--------|-------|
|               |                 |                  |           |           |                   |               |                              |                | 10              | 50     | 100   |
| C-MNIST (FG)  | Digital number  | Digital color    | 10        | 10        | 60000             | 10000         | 0.90                         | balance        | 0.17%           | 0.83%  | 1.67% |
| C-MNIST (BG)  | Digital number  | Background color | 10        | 10        | 60000             | 10000         | 0.90                         | balance        | 0.17%           | 0.83%  | 1.67% |
| C-FMNIST (FG) | Object category | Object color     | 10        | 10        | 60000             | 10000         | 0.90                         | balance        | 0.17%           | 0.83%  | 1.67% |
| C-FMNIST (BG) | Object category | Background color | 10        | 10        | 60000             | 10000         | 0.90                         | balance        | 0.17%           | 0.83%  | 1.67% |
| CIFAR10-S     | Object category | Grayscale or not | 10        | 2         | 50000             | 20000         | 0.90                         | balance        | 0.20%           | 1.00%  | 2.00% |
| CelebA        | Attractive      | Gender           | 2         | 2         | 162770            | 7656          | class0: 0.62<br>class1: 0.77 | balance        | 0.012%          | 0.061% | 0.12% |

Table 6: Statistics for all datasets used in our paper.

# A DATASEST STATISTICS

In this section, we provide detailed statistics for all datasets used in the manuscript for reproduction. As shown in Table [6,](#page-14-2) we present the target attribute (TA), protected attribute (PA), the sample number of the training set, the sample number of the test set, and the BR in the training set. Additionally, all test sets are balanced, with equal sample sizes across groups. We also report the condensed ratio at IPC 10, 50, and 100, which is computed by the ratio of the condensed dataset size to the training set size.

<span id="page-14-0"></span>

# B MORE ATTRIBUTES ANALYSIS ON CELEBA

We explore additional facial attributes in CelebA to further demonstrate the robustness of FairDD. To this end, we regard gender as the PA, and young, big\_nose, and blond\_hair as the TA, which results in CelebA<sub>t</sub>, CelebA<sub>b</sub>, CelebA<sub>h</sub> and respectively. We also replace the target attribute from attribute to blond\_hair, resulting in CelebA $^h.$  The performance is reported in Table [7.](#page-14-3) FairDD exhibits fairer behavior compared to vanilla DDs across these attributes while maintaining comparable accuracy, as seen in Table [8.](#page-14-3)

<span id="page-14-3"></span>

| Table 7: Fairness comparison on different attributes.                                                                                                                                                                                                                                                                                                                                                                                                                            | Table 8: Accuracy comparison.                                                                                                                                                                                                                         |
|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Methods $\left \text{IPC}\right _{\overline{\text{DEO}_M}} \frac{\text{DM}}{\text{DEO}_M} \frac{\text{DM} + \text{FairDD}}{\text{DEO}_A} \frac{\text{DC}}{\text{DEO}_A} \frac{\text{DC}}{\text{DEO}_M} \frac{\text{DC} + \text{FairDD}}{\text{DEO}_A} \frac{\text{Whole}}{\text{DEO}_M} \frac{\text{Whole}}{\text{DEO}_A} \frac{\text{Whole}}{\text{DEO}_A} \frac{\text{Whole}}{\text{DEO}_A} \frac{\text{Whole}}{\text{DEO}_A} \frac{\text{Whole}}{\text{DEO}_A} \frac{\text{W$ | $\frac{\text{Methods}}{\text{Dataset}} \left  \text{IPC} \right  \frac{\text{DM}}{\text{Acc.}} + \frac{\text{FairDD}}{\text{Acc.}} \left  \frac{\text{DC}}{\text{Acc.}} + \frac{\text{FairDD}}{\text{Acc.}} \right  \frac{\text{Whole}}{\text{Acc.}}$ |
| $\text{CelebA}_y \begin{bmatrix} 10 & 34.18 & 31.49 & \textbf{13.30} & \textbf{10.38} & 20.58 & \textbf{19.26} & \textbf{10.86} & \textbf{8.55} \\ 100 & 46.90 & 41.13 & \textbf{12.90} & \textbf{8.21} & 27.98 & 25.18 & \textbf{14.69} & \textbf{11.26} & 25.40 & 16.02 \\ 100 & 44.96 & 37.84 & \textbf{9.17} & \textbf{5.1$                                                                                                                                                  | CelebA <sub>y</sub> $\begin{vmatrix} 10 & 62.34 & 63.79 & 55.91 & 56.99 \\ 50 & 63.59 & 67.33 & 59.87 & 59.42 \\ 100 & 66.68 & 69.90 & 63.53 & 61.59 \end{vmatrix}$ 75.99                                                                             |
| $\text{CelebA}_b \begin{array}{ rrrrrrrrrrrrrrrrrr} 10 & 45.57 & 45.13 & \textbf{15.63} & \textbf{13.47} & \textbf{18.17} & \textbf{16.81} & \textbf{7.54} & \textbf{6.34} \\ 50 & 51.91 & 51.13 & \textbf{14.44} & \textbf{12.01} & \textbf{23.85} & \textbf{22.34} & \textbf{20.58} & \textbf{16.87} & \textbf{34.48} & \textbf{25.50} \\ 100 & 5$                                                                                                                             | CelebA <sub>b</sub> $\begin{vmatrix} 10 & 57.46 & 59.50 & 52.91 & 54.67 \\ 50 & 58.71 & 62.39 & 56.55 & 55.46 \\ 100 & 60.30 & 64.34 & 57.65 & 57.15 \end{vmatrix}$ 66.80                                                                             |
| CelebA <sub>h</sub>   10   17.01 9.56 7.76 6.02   12.44 8.01 9.25 7.31   15.53 11.56                                                                                                                                                                                                                                                                                                                                                                                             | CelebA <sub>h</sub>   10   63.64 <b>64.86</b>   <b>58.04 57.55</b>   75.33                                                                                                                                                                            |
| CelebA <sup>h</sup>   10   30.28 20.76 12.70 8.28   25.94 15.11 16.78 9.88   46.67 26.11                                                                                                                                                                                                                                                                                                                                                                                         | CelebA <sup>h</sup>   10   77.66 <b>79.71</b>   72.07 <b>75.03</b>   79.44                                                                                                                                                                            |

To further study the generalization of FairDD, we regard blond hair as the protected attribute and attractive as the target attribute, resulting in CelebA<sub>b</sub>. As illustrated in Table, FairDD+DM obtains 7.76% DEO<sub>M</sub> and 6.02% DEO<sub>A</sub>, outperforming DM by 9.25% and 3.54%. Accuracy has also been improved.

<span id="page-14-1"></span>

# C COMPUTATION OVERHEAD

In this section, we investigate the computational efficiency of FairDD. Since FairDD performs finegrained alignment at the group level, we evaluate the impact of the number of groups on training time (min) and peak GPU memory consumption (MB). As shown in Table [9,](#page-15-0) FairDD requires more time than vanilla DDs on C-MNIST (FG), and the time increases as the number of groups (PA) grows. This phenomenon is particularly noticeable in DC because the gradient must be computed with respect to the number of groups. In contrast, DM avoids computing gradients independently for each group and directly computes all embeddings once in a single pass, reducing the additional overhead caused by FairDD's group-level alignment. Regarding GPU memory usage, FairDD incurs no obvious additional overhead compared to vanilla DDs.

Here, we further supplement the overhead analysis with respect to image resolutions. We conduct experiments on CMNIST, CelebA (32), CelebA (64), and CelebA (96) on DM and DC at IPC=10.

Table 9: Comparison of computation overhead on FairDD and vanilla DDs.

<span id="page-15-0"></span>

| Group<br>number | 0 (vanilla DD) |        | 2 (FairDD) |        | 4 (FairDD) |        | 6 (FairDD) |        | 8 (FairDD) |        | 10 (FairDD) |        |
|-----------------|----------------|--------|------------|--------|------------|--------|------------|--------|------------|--------|-------------|--------|
|                 | T (min)        | G (MB) | T (min)    | G (MB) | T (min)    | G (MB) | T (min)    | G (MB) | T (min)    | G (MB) | T (min)     | G (MB) |
| DC              | 70             | 2143   | 94         | 2345   | 128        | 2369   | 152        | 2393   | 181.8      | 2419   | 210         | 2443   |
| DM              | 26.2           | 1579   | 31.75      | 1579   | 33.2       | 1579   | 35.2       | 1579   | 36.5       | 1579   | 36.9        | 1579   |

DM and DC align different signals, which would bring different effects. As illustrated in Table [10,](#page-15-1) it can be observed that FairDD + DM does not require additional GPU memory consumption but does necessitate more time. The time gap increases from 0.42 minutes to 1.79 minutes as input resolution varies (e.g., CelebA  $32 \times 32$ , CelebA  $64 \times 64$ , and CelebA  $96 \times 96$ ); however, the gap remains small. This can be attributed to FairDD performing group-level alignment on features, which is less influenced by input resolution. Notably, although CMNIST and CelebA  $(32 \times 32)$  share the same resolution, the time gap is more pronounced for CMNIST (e.g., 3 minutes). This is attributed to CMNIST having 10 attributes, whereas CelebA  $(32 \times 32)$  has only 2 attributes. These indicate that FairDD + DM requires no additional GPU memory consumption. Its additional time depends on both input resolution and the number of groups, but the number of groups more significantly influences it. As for DC, FairDD requires additional GPU memory and time. Since FairDD + DC explicitly computes group-level gradients, the resulting gradient caches cause FairDD + DC to consume more memory. The additional consumption is acceptable compared to the performance gain on fairness. Additionally, the time gap is relatively larger than that observed between DM and FairDD + DM. Similar to DM, the group number is the primary factor contributing to additional time consumption compared to input resolution.

Table 10: Comparison of computation overhead for IPC = 10.

<span id="page-15-1"></span>

| Methods<br>Dataset | Group<br>number | DM    |        | DM+FairDD |        | DC    |        | DC+FairDD |        |
|--------------------|-----------------|-------|--------|-----------|--------|-------|--------|-----------|--------|
|                    |                 | Time  | Memory | Time      | Memory | Time  | Memory | Time      | Memory |
| CMNIST             | 10              | 15.55 | 1227   | 18.55     | 1227   | 58.75 | 1767   | 83.13     | 1893   |
| CelebA32 × 32      | 2               | 10.93 | 2293   | 11.35     | 2293   | 32.98 | 2413   | 34.65     | 2479   |
| CelebA64 × 64      | 2               | 11.18 | 8179   | 12.20     | 8177   | 43.67 | 8525   | 47.07     | 8841   |
| CelebA96 × 96      | 2               | 12.83 | 17975  | 14.62     | 17975  | 82.37 | 18855  | 86.88     | 19427  |

### D ADDITIONAL EXPERIMENT ON UTKFACE

<span id="page-15-2"></span>We also conduct another real image dataset namely UTKFace, commonly used for fairness. It consists of 20,000 face images including three attributes, age, gender, and race. We follow a common setting and treat age as the target attribute and gender as the protected attribute. We test DM and FairDD + DM with the same parameters, the results in Table [11](#page-15-2) show that our method outperforms the vanilla dataset distillation by 16.1% and 8.92% on the DEO<sub>M</sub> and DEO<sub>A</sub>. Similar results are observed at  $IPC = 50$ .

| Table 11: UTKFace performance on DM. |  |  |
|--------------------------------------|--|--|
|--------------------------------------|--|--|

| $\begin{tabular}{l c c} Methods & \text{IPC} & \text{DM} & \text{DM}+\text{FairDD} \\ \text{Dataset} & \text{IPC.} & \overline{\text{DEO}_M} & \overline{\text{DEO}_A} & \overline{\text{Acc.} & \overline{\text{DEO}_M} & \overline{\text{DEO}_A}} \\ \end{tabular}$ |  |  |  |  |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|
| UTKFace 10 66.67 32.97 16.31 67.72 16.87 7.39<br>5.09 5.09 5.15 28.58 14.03 74.66 10.59                                                                                                                                                                               |  |  |  |  |

# E ABLATION STUDY ON WEIGHTING MECHANISM

We denote the model with inversely proportional weighting as FairDD<sub>inverse</sub>. Our experiments on C-FMNIST and CIFAR10-S at IPC=10 reveal that FairDDinverse suffers significant performance degradation, with  $DEO_M$  increasing from 33.05% to 56.60% and  $DEO_A$  rising from 19.72% to 35.13% in terms of fairness performance metrics. Additionally, there is a decline in accuracy for TA.

| <b>Methods</b>                                                                                                                              | <b>IPC</b> | DM |  | $DM + FairDDinverse$ |  | DM+FairDD |                                                                                                                                                                                                                                                                                                                |
|---------------------------------------------------------------------------------------------------------------------------------------------|------------|----|--|----------------------|--|-----------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| <b>Dataset</b>                                                                                                                              |            |    |  |                      |  |           | $\frac{1}{\sqrt{\text{Acc. }}}$ $\frac{1.2 \text{N}}{\text{DEO}_{\text{M}}}$ $\frac{1.2 \text{N}}{\text{DecO}_{\text{M}}}$ $\frac{1.2 \text{N}}{\text{DEO}_{\text{M}}}$ $\frac{1.2 \text{N}}{\text{DEO}_{\text{A}}}$ $\frac{1.2 \text{N}}{\text{DEO}_{\text{M}}}$ $\frac{1.2 \text{N}}{\text{DEO}_{\text{A}}}$ |
| C-FMNIST (BG) 10 22.26 100.0 99.05 70.55 56.60 35.13 71.10 33.05 19.72<br>CIFAR10-S 10 37.88 59.20 39.31 38.14 48.27 37.41 45.17 31.75 8.73 |            |    |  |                      |  |           |                                                                                                                                                                                                                                                                                                                |
|                                                                                                                                             |            |    |  |                      |  |           |                                                                                                                                                                                                                                                                                                                |

Table 12: Performance between FairDD and FairDD<sub>inverse</sub>.

We attribute this degradation to the excessive penalization of groups with larger sample sizes. The success of FairDD lies in grouping all samples with the same PA into a single group and performing the group-level alignment. Each group contributes equally to the total alignment, inherently mitigating the effects of imbalanced sample sizes across different groups. However, penalizing groups based on sample cardinality reintroduces an unexpected bias related to group size in the information condensation process. This results in large groups receiving smaller weights during alignment, placing them in a weaker position and causing synthetic samples to deviate excessively from large (majority) groups. Consequently, majority patterns become underrepresented, ultimately hindering overall performance.

# F ABLATION STUDY ON GROUP LABEL NOISE

Here, we evaluate the robustness of spurious group labels could provide more insights. We randomly sample the entire dataset according to a predefined ratio. These samples are randomly assigned to group labels to simulate noise. To ensure a thorough evaluation, we set sample ratios at  $10\%$ ,  $15\%$ , 20%, and 50%. As shown in the table, when the ratio increases from 10% to 20%, the DEO<sub>M</sub> results range from 14.93% to 18.31% with no significant performance variations observed. These results indicate that FairDD is robust to noisy group labels. However, as the ratio increases further to 50%, relatively significant performance variations become apparent. It can be understood that under a high noise ratio, the excessive true samples of majority attributes are assigned to minority labels. This causes the minority group center to shift far from its true center and thus be underrepresented.

| Table 13: Ablation study on group label noise. |
|------------------------------------------------|
|------------------------------------------------|

| Methods $\left \text{IPC}\right _{\overline{\text{Acc}}} \frac{\text{DM}}{\text{Dec}_\Lambda} \frac{\text{DM} + \text{FairDD}}{\text{DCO}_\Lambda} \frac{\text{DM} + \text{FairDD}}{\text{ACC}} \frac{\text{DM} + \text{FairDD} (10\%)}{\text{DEO}_\Lambda} \frac{\text{DM} + \text{FairDD} (15\%)}{\text{ACC}} \frac{\text{DM} + \text{FairDD} (15\%)}{\text{DEO}_\Lambda} \frac{\text{DM} + \text{FairDD} (20\%)}{\text{ACC}} \frac{\text{DM} + \text{BarDD} (20\%)}{\text{DEO}_$ |  |  |  |  |  |  |  |  |  |  |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|--|--|--|--|--|--|
| CMNIST (BG) 10 27.95 100.0 99.11 94.88 13.42 6.77 94.34 16.54 7.81 94.44 17.90 8.61 94.32 18.31 9.20 89.56 66.19 25.97                                                                                                                                                                                                                                                                                                                                                              |  |  |  |  |  |  |  |  |  |  |

### G ABLATION STUDY ON BALANCED ORIGINAL DATASET.

We synthesized a fair version of CelebA, referred to as CelebA $_{Fair}$ . The target attribute is attractive (attractive and unattractive), and the protected attribute is gender (female and male). In the original dataset, the sample numbers for female-attractive, female-unattractive, male-attractive, and maleunattractive groups are imbalanced. To create a fair version, Celeb $A_{Fair}$  samples the number of instances based on the smallest group, ensuring equal representation across all four groups. We tested the fairness performance of FairDD and DM at IPC = 10, as well as the performance of models trained on the full dataset. As shown in Table [14,](#page-17-0) vanilla DD achieves  $14.33\%$  DEO<sub>A</sub> and  $8.77\%$  DEO<sub>M</sub>. In comparison, the full dataset achieves  $3.66\%$  DEO<sub>A</sub> and  $2.77\%$  DEO<sub>M</sub>. While DM still exacerbates bias with a relatively small margin, this is primarily due to partial information loss introduced during the distillation process. FairDD produces fairer results, achieving  $11.11\%$  DEO<sub>A</sub> and 6.68% DEO<sub>M</sub>.

| Table 14: Performance on balanced original dataset. |  |  |  |
|-----------------------------------------------------|--|--|--|
|-----------------------------------------------------|--|--|--|

<span id="page-17-0"></span>

| Methods $\left \text{IPC}\right $ $\frac{\text{Whole}}{\text{Acc.}}$ $\frac{\text{Whole}}{\text{DEO}_{\text{M}}}\frac{\text{DM}}{\text{DecO}_{\text{A}}}\frac{\text{DM}}{\text{DEO}_{\text{A}}}\frac{\text{DM} + \text{FairDD}}{\text{DEO}_{\text{A}}\text{Acc.}}$ |  |  |  |  |  |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|--|
| Celeb $A_{Fair}$   10   76.33 3.66 2.77   63.31 14.33 8.77   63.17 11.11 6.68                                                                                                                                                                                      |  |  |  |  |  |

#### H ABLATION STUDY ON NUANCED PA GROUPS

We perform a fine-grained PA division. For example, we consider gender and wearing-necktie as two correlated attributes and divide them into four groups: males with a necktie, males without a necktie, females with a necktie, and females without a necktie (Celeb $A_{q\&n}$ ). Similarly, we consider gender and paleskin and divide them into four groups (Celeb $A_{q\&p}$ ). Their target attribute is attractive. As shown in the Table [15,](#page-17-1) FairDD outperforms vanilla DD in the accuracy and fairness performance on these two experiments. The performance for necktie and gender is improved from 57.50% to 25.00% on DEO<sub>M</sub> and 52.79% to 21.73% on DEO<sub>A</sub>. Accuracy is also improved from 63.25% to 67.98%. Similar results can be observed for gender and paleskin. Hence, FairDD can mitigate more fine-grained attribute bias, even when there is an intersection between attributes.

Table 15: Performance on nuanced groups.

<span id="page-17-1"></span>

| Methods $\left \text{IPC}\right $ $\frac{\text{DM}}{\text{Acc.}} \frac{\text{DM}}{\text{DEO}_{\text{A}}} \frac{\text{DM} + \text{FairDD}}{\text{Acc.}}$ $\frac{\text{DM} + \text{FairDD}}{\text{DEO}_{\text{A}}}$ |  |  |  |  |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|
| CelebA <sub>g&amp;n</sub>   10   63.25 57.50 52.79   67.98 25.00 21.73<br>CelebA <sub>g&amp;p</sub>   10   62.48 44.81 41.60   64.37 26.92 19.33                                                                  |  |  |  |  |

### I ABLATION STUDY ON IMBALANCED PA GROUPS

To further study FairDD robustness under more biased scenarios, we keep the sample number of the majority group in each class invariant and allocate the sample size to the remaining 9 minority groups with increasing ratios, i.e., 1:2:3:4:5:6:7:8:9. We denote this variant CMNIST $_{unbalance}$  This could help create varying extents of underrepresented samples for different minority groups. Notably, the least-represented PA groups account for only about 1/500 of the entire dataset, which equates to just 12 samples out of 6000 in CMNIST $u_{nbalance}$ . As shown in Table [16,](#page-17-2) FairDD achieves a robust performance of 16.33% DEO<sub>M</sub> and 9.01% DEO<sub>A</sub> compared to 17.04% and 7.95% in the balanced PA groups. A similar steady behavior is observed in accuracy, which changes from 94.45% to 94.61%. This illustrates the robustness of FairDD under different levels of dataset imbalance.

<span id="page-17-2"></span>

| Methods<br>Dataset                                                                                                                                                                                     |  | DM<br>$\left \text{IPC}\right _{\overline{\text{Acc.}}} \frac{\sum_{i=1}^{N+1}}{\text{DEO}_M} \frac{\sum_{i=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}^{N} \sum_{j=1}$ |  | DM+FairDD |  |
|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|-----------|--|
| $\text{CMNIST} \begin{array}{c ccc} \text{CMNIST} & 10 & 25.01 & 100.0 & 99.96 & 94.61 & 17.04 & 7.95 \\ \text{CMNIST}_{unbalance} & 10 & 23.38 & 100.0 & 99.89 & 94.45 & 16.33 & 9.01 \\ \end{array}$ |  |                                                                                                                                                                                                                                                                                                                                                                                |  |           |  |

Table 16: Performance on imbalanced PA.

# J EXPLORATION ON VISION TRANSFORMER AS BACKBONE

Although the Vision Transformer (ViT) is a powerful backbone network, to the best of my knowledge, current DDs, such as DM and DC, have not yet utilized ViT as the extraction network. We conducted experiments using 1-layer, 2-layer, and 3-layer ViTs. As shown in Table [17,](#page-18-1) vanilla DM at IPC=10 suffers performance degradation in classification, dropping from 25.01% to 18.63%. Moreover, as the number of layers increases, the performance deteriorates more severely. This suggests that current DDs are not directly compatible with ViTs. While FairDD still outperforms DM in both accuracy

<span id="page-18-1"></span>and fairness metrics, the observed improvement gain is smaller compared to results obtained on convolutional networks. Further research into leveraging ViTs for DD and FairDD is a promising direction worth exploring.

| $\left. \frac{\text{Methods}}{\text{Database}} \right  \text{IPC} \right  \overbrace{\text{Acc.}} \frac{\text{DM}}{\text{DEO}_M} \frac{\text{DM} + \text{FairDD}}{\text{Acc.} \text{DEO}_M} \frac{\text{DM}}{\text{DEO}_M}$ |  |  |  |  |
|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|
| ViT1   10   18.63 100.0 98.48   56.15 82.10 56.72<br>ViT2   10   18.28 100.0 98.99   33.89 72.85 40.97<br>ViT3   10   16.15 100.0 95.75   26.70 65.71 29.46                                                                 |  |  |  |  |
|                                                                                                                                                                                                                             |  |  |  |  |
|                                                                                                                                                                                                                             |  |  |  |  |

Table 17: Exploration on ViT architecture.

### K EXPLORATION ON CHALLENGING DATASET

<span id="page-18-2"></span>We created CIFAR100-S following the same operation as CIFAR10-S, where the grayscale or not is regarded as PA. Due to the time limit, we supplemented CIFAR100-S on DM at IPC=10. As shown in Table [18,](#page-18-2) DM achieves the classification accuracy of 22.84%, and the fairness of 69.9% DEO<sub>M</sub> and  $25.37\%$  DEO<sub>A</sub>. Compared to vanilla DM, FairDD obtains more accurate classification performance and mitigates the bias to the minority groups, with 27.30% DEO $_M$  and 15.54% DEO $_A$  improvement.

|  |  | Table 18: Exploration on challenging dataset. |  |
|--|--|-----------------------------------------------|--|
|--|--|-----------------------------------------------|--|

| Methods $\left \text{IPC}\right $ $\frac{\text{DM}}{\text{Acc.}} \frac{\text{DM}}{\text{DEO}_{M}} \frac{\text{DM} + \text{Far} \text{DD}}{\text{Acc.}} \frac{\text{DM}}{\text{DEO}_{M}} \frac{\text{DA}}{\text{DEO}_{A}}$ |  |  |  |  |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|--|--|
| CIFAR100-S  10   19.69 69.90 25.37   22.84 42.60 9.83                                                                                                                                                                     |  |  |  |  |

<span id="page-18-0"></span>

#### L COMPARISON WITH MTT

Unlike DMF, MTT uses a two-stage method to condense the dataset. First, it stores the model trajectories, and then it uses these trajectories to guide the generation of the synthetic dataset. To provide a comprehensive comparison, we compare FairDD with MTT, as shown in Tables [19](#page-18-3) and [20.](#page-18-3)

<span id="page-18-3"></span>Table 19: Fairness comparison on diverse IPCs. The best Table 20: Accuracy comparison on diresults are highlighted in bold. verse IPCs.

| Methods $\left  \text{IPC} \right _{\overline{\text{DEO}_M}} \frac{\text{Random}}{\overline{\text{DEO}_A}} \frac{\text{MTT}}{\text{DEO}_A} \frac{\text{DM}}{\text{DEO}_A} \frac{\text{DM}}{\text{DEO}_A} \frac{\text{DM} + \text{FairDD}}{\text{DEO}_A} \frac{\text{Whole}}{\text{DEO}_A} \frac{\text{Whole}}{\text{DEO}_A}$ | Methods $\left  \text{IPC} \right  \frac{\text{Random}}{\text{Acc.}} \left  \frac{\text{MTT}}{\text{Acc.}} \right  \frac{\text{DM}}{\text{Acc.}} + \frac{\text{FairDD}}{\text{Acc.}} \left  \frac{\text{Whole}}{\text{Acc.}} \right $                                                                                                                  |
|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| $\begin{array}{ c cccc c c c c c c c c c c c c c c c c c $<br>C-MNIST<br>(FG) $\begin{vmatrix} 50 \\ 100 \end{vmatrix}$ 100.0 88.64 26.81 13.02 99.36 66.38 <b>8.17 4.86</b>                                                                                                                                                 | C-MNIST $\begin{array}{ c c c c c c c c } \hline \text{C-MNIST} & \text{10} & \text{30.75} & \text{92.00} & \text{25.01} & \text{94.61} \\ \hline \text{GCD} & \text{50} & \text{47.38} & \text{94.08} & \text{56.84} & \text{96.58} & \text{97.71} \\\hline \end{array}$<br>$(FG)$  100   67.41   94.29   78.04 96.79                                 |
| $\begin{array}{ l c c c c c c c c c c c c c c c c c c $<br>C-FMNIST<br>(BG)<br>$\vert 100 \vert 100.0$ 96.05 97.20 63.66 100.0 93.88 21.95 13.33                                                                                                                                                                             | C-FMNIST $\begin{vmatrix} 10 \\ 50 \end{vmatrix}$ 24.96 $\begin{vmatrix} 67.92 \\ 22.26 \end{vmatrix}$ 21.10 $\begin{vmatrix} 70.92 \\ 70.32 \end{vmatrix}$ 34.92 $\begin{vmatrix} 67.92 \\ 70.32 \end{vmatrix}$ 36.27 <b>79.07</b> 77.97<br>(BG) $\begin{vmatrix} 50 \\ 100 \end{vmatrix}$ 44.87 $\begin{vmatrix} 70.74 \\ 49.30 \end{vmatrix}$ 80.63 |

From the results, FairDD outperforms MTT in both fairness and accuracy. Notably, MTT surpasses DM by a large margin, which we attribute to two factors: 1) Unlike DMF, which is directly influenced by biased data, MTT aligns the model parameters to optimize the synthetic dataset, and this indirect alignment reduces the impact of bias in the data. 2) An accurate model typically conceals its inherent unfairness, as it can better classify each class despite underlying biases. For example, when *Whole* model achieves high accuracy on the C-MNIST (FG) dataset, MTT inherits this accuracy and conceals its biases. However, when the model's accuracy declines on the C-FMNIST (BG) dataset, MTT reveals its underlying unfairness in Fig. [6\(a\).](#page-19-1) In contrast, FairDD directly addresses unfairness rather than relying on high accuracy to obscure biased behavior in Fig. [6\(b\).](#page-19-2)

<span id="page-19-1"></span>Image /page/19/Picture/0 description: The image displays two grids of generated clothing images, labeled (a) MTT visualization and (b) FairDD visualization. Both grids contain multiple rows and columns of small, colorful images, each depicting a piece of clothing such as a shirt, pants, or shoes. The backgrounds of the images in the MTT visualization grid are varied and often blend with the clothing, while the FairDD visualization grid features distinct, solid color backgrounds for each image, creating a more organized and contrasting appearance.

Figure 6: Visualization comparison on C-FMNIST (BG) between MTT and FairDD + DM.

<span id="page-19-3"></span><span id="page-19-2"></span>Image /page/19/Figure/2 description: The image illustrates a process involving synthetic and original datasets of handwritten digits. On the left, a synthetic dataset S is shown, containing images of digits 0 through 9, each with a different color. An arrow labeled "FairDD" points from the original dataset T to the synthetic dataset S. The original dataset T, in the center, displays a variety of digits, also in different colors, with a placeholder "..." indicating more digits. An arrow labeled "Vanilla DDs" points from the original dataset T to another synthetic dataset S on the right. This second synthetic dataset S also contains images of digits 0 through 9, each with a distinct color.

Figure 7: Visualization on  $S$  at IPC=1 for FairDD and vanilla DDs. Left is the condensed dataset using FairDD, which incorporates different PA, i.e., foreground colors. Right is the condensed dataset using vanilla DDs, where each class presents the same color as the corresponding majority group.

<span id="page-19-0"></span>

# M ADDITIONAL VISUALIZATION ANALYSIS

Visualization analysis on  $S$  generation We aim to investigate whether FaiDD renders the expecta-tion of S locate the center among all groups, as clarified in Eq. [8.](#page-4-2) If the clarification holds, S should contain all PA at IPC = 1 because the expectation of S is equal to S when IPC = 1. We visualize S at IPC=1 on C-MNIST (FG), where each class (digital number) is dominated by one color, and the rest is colored by the rest nine colors. As shown in Fig. [7,](#page-19-3) the  $S$  generated by FairDD combines all colors from PA groups. This suggests that FairDD can effectively incorporate all PA into resulting  $S$ , indirectly validating the Theorem [4.1.](#page-4-4) Meanwhile, we observe that the majority groups dominate vanilla DDs according to Eq. [6,](#page-3-0) where the resulting  $S$  contains the colors from the corresponding majority groups.

# N MORE VISUALIZATIONS

We provide more visualizations at IPC = 50 on different datasets in Figures [8,](#page-20-0) [9,](#page-21-0) [10,](#page-22-0) [11,](#page-23-0) and [13.](#page-25-0)

<span id="page-20-0"></span>Image /page/20/Figure/0 description: The image displays a grid of handwritten digits from 0 to 9, with each row dedicated to a specific digit. The digits are presented in various colors against a black background. The first row shows multiple instances of the digit '0' in shades of red and orange. The second row features the digit '1' in colors like yellow, orange, and purple. The third row contains the digit '2' in yellow, green, and blue. The fourth row displays the digit '3' in yellow, green, and blue. The fifth row shows the digit '4' in green and blue. The sixth row has the digit '5' in cyan, blue, and purple. The seventh row displays the digit '6' in blue and purple. The eighth row features the digit '7' in purple and pink. The ninth row shows the digit '8' in pink and red. The final row contains the digit '9' in yellow, pink, and purple. Each row has approximately 15 to 20 repetitions of the digit.

(a) Visualization of the initialized dataset at IPC = 50 in C-MNIST (FG). The foreground of each class is dominated by one color.

|  |  |  |  |  |  |  |  |  |  |  |                                         |  |  |  |  |  |  |  |  |  |  |  | ${\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\circ} {\scriptstyle\$ |  |  |
|--|--|--|--|--|--|--|--|--|--|--|-----------------------------------------|--|--|--|--|--|--|--|--|--|--|--|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--|--|
|  |  |  |  |  |  |  |  |  |  |  |                                         |  |  |  |  |  |  |  |  |  |  |  | --------------------------------------                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           |  |  |
|  |  |  |  |  |  |  |  |  |  |  |                                         |  |  |  |  |  |  |  |  |  |  |  | 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |  |  |
|  |  |  |  |  |  |  |  |  |  |  |                                         |  |  |  |  |  |  |  |  |  |  |  | 333333333333333333333333333333333333333                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |  |  |
|  |  |  |  |  |  |  |  |  |  |  |                                         |  |  |  |  |  |  |  |  |  |  |  | <i>4</i> 4 4 <i>4 4 4 4</i> 4 4 4 4 4 5 4 4 4 4 4 4 4 4 4 4                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |  |  |
|  |  |  |  |  |  |  |  |  |  |  |                                         |  |  |  |  |  |  |  |  |  |  |  | 555555555555555555555555555555555555555                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |  |  |
|  |  |  |  |  |  |  |  |  |  |  |                                         |  |  |  |  |  |  |  |  |  |  |  | 6 6 6 6 6 6 6 6 6 6 6 6 6 6 6 6 6 6 6 6                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                          |  |  |
|  |  |  |  |  |  |  |  |  |  |  |                                         |  |  |  |  |  |  |  |  |  |  |  | 1 7 7 7 7 7 8 9 9 9 9 7 7 7 7 7 7 7 7 9 9 7 7 7 9 7 9 7 7 7 7 7 7 7 7 7 7 7 7 7 7 7 7 7 7 7 7                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    |  |  |
|  |  |  |  |  |  |  |  |  |  |  |                                         |  |  |  |  |  |  |  |  |  |  |  | $\textit{688835885888888888888888888888888888888$                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |  |  |
|  |  |  |  |  |  |  |  |  |  |  | 999999999999991999999999999999999999999 |  |  |  |  |  |  |  |  |  |  |  |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |  |  |

(b) Visualization of the condensed dataset at IPC = 50 in C-MNIST (FG) using Vanilla DM. The foreground of each class inherits the bias.

Image /page/20/Figure/4 description: The image displays a grid of handwritten digits from 0 to 9, with each digit appearing multiple times in various colors against a black background. The digits are arranged in rows, with each row dedicated to a specific digit. The first row shows multiple instances of the digit '0' in colors like white, red, blue, green, yellow, and purple. The second row contains the digit '1' in similar colorful variations. This pattern continues for digits '2' through '9', with each row showcasing the respective digit in a spectrum of colors. The overall impression is a colorful and organized collection of handwritten numbers.

(c) Visualization of the condensed dataset at IPC = 50 in C-MNIST (FG) using FairDD + DM. The foreground of each class mitigates such bias.

Figure 8: Visualization comparison on C-MNIST (FG) between vanilla DM and FairDD + DM.

<span id="page-21-0"></span>Image /page/21/Figure/0 description: The image is a grid of handwritten digits, organized by digit value and color. The top row displays the digit '0' in various shades of red and orange. The second row shows the digit '1' in orange, yellow, and blue. The third row features the digit '2' in yellow, green, and pink. The fourth row contains the digit '3' in green and yellow. The fifth row displays the digit '4' in green and blue. The sixth row shows the digit '5' in blue and cyan. The seventh row features the digit '6' in blue and purple. The eighth row contains the digit '7' in purple and pink. The ninth row displays the digit '8' in pink and red. The bottom row shows the digit '9' in purple, pink, and yellow. Each row has approximately 20 cells, and there are 10 rows in total, representing digits 0 through 9.

(a) Visualization of the initialized dataset at IPC = 50 in C-MNIST (BG). The background of each class is dominated by one color.

Image /page/21/Figure/2 description: The image displays a grid of handwritten digits from 0 to 9, arranged in rows and columns. Each row is dedicated to a specific digit, starting with 0 at the top and ending with 9 at the bottom. The background of each cell is colored with a gradient of colors, transitioning from red at the top to purple at the bottom. Within each cell, a single handwritten digit is present. The digits are varied in style and clarity, with some being very clear and others more smudged or stylized. The grid appears to be a visualization of a dataset, possibly for machine learning or pattern recognition, showcasing different examples of each digit.

(b) Visualization of the condensed dataset at IPC = 50 in C-MNIST (BG) using Vanilla DM. The background of each class inherits the bias.

Image /page/21/Figure/4 description: The image is a grid of 10 rows and 20 columns, with each cell containing a handwritten digit. The digits are organized by row, with the first row containing only 0s, the second row containing only 1s, and so on, up to the tenth row containing only 9s. Each digit is displayed in a different colored background, with the colors cycling through blue, green, yellow, red, purple, and cyan for each digit within a row. For example, the first row has 0s on blue, green, yellow, red, purple, cyan, blue, green, yellow, red, purple, cyan, blue, green, yellow, red, purple, cyan, blue, and green backgrounds. The digits themselves are handwritten in black ink.

(c) Visualization of the condensed dataset at IPC = 50 in C-MNIST (BG) using FairDD + DM. The background of each class mitigates such bias.

Figure 9: Visualization comparison on C-MNIST (BG) between vanilla DM and FairDD + DM.

<span id="page-22-0"></span>Image /page/22/Figure/0 description: The image displays a grid of fashion items, organized by color and type. The top rows feature tops like t-shirts and sweaters in a rainbow of colors, from red and orange to yellow, green, blue, and purple. Below these are dresses and skirts, also in various colors. The middle section showcases outerwear such as jackets and coats, predominantly in shades of green, blue, and purple. The lower rows display footwear, including sandals, heels, sneakers, and boots, again in a spectrum of colors. The bottom rows are filled with handbags and bags in vibrant pinks, purples, and oranges, as well as some neutral-colored boots. The items are arranged in neat rows and columns against a black background, creating a visually organized and colorful display of clothing and accessories.

(a) Visualization of the initialized dataset at IPC = 50 in C-FMNIST (FG). The foreground of each class is dominated by one color.

Image /page/22/Figure/2 description: The image displays a grid of fashion items, organized by type and color. The top row features t-shirts in various colors, arranged from red to yellow. Below that are pants, also in a rainbow of colors. The third row showcases sweaters and long-sleeved shirts, predominantly in yellow and green hues. The fourth row presents dresses and skirts in a spectrum of colors, including yellow, green, and blue. The fifth row displays shoes, such as sandals and heels, in shades of blue and teal. The sixth row contains sneakers in various colors, primarily blue and purple. The seventh row features handbags in vibrant pink and purple tones. The bottom row consists of boots and ankle boots in white and gray.

(b) Visualization of the condensed dataset at IPC = 50 in C-FMNIST (FG) using Vanilla DM. The foreground of each class inherits the bias.

Image /page/22/Figure/4 description: The image displays a grid of fashion items, each rendered in a different vibrant color against a black background. The items are organized into rows and columns, showcasing a variety of clothing and footwear. The top rows feature t-shirts and pants, followed by dresses and jackets. The lower rows include shoes, sneakers, and handbags. The diverse color palette applied to each item creates a visually striking and organized presentation of different fashion categories.

(c) Visualization of the condensed dataset at IPC = 50 in C-FMNIST (FG) using FairDD + DM. The foreground of each class mitigates such bias.

Figure 10: Visualization comparison on C-FMNIST (FG) between vanilla DM and FairDD + DM.

<span id="page-23-0"></span>Image /page/23/Figure/0 description: The image displays a grid of fashion items, organized by color and type. The items are arranged in rows and columns, with each cell containing a single fashion item. The colors of the cells follow a rainbow spectrum, starting with red at the top left and progressing through orange, yellow, green, blue, indigo, and violet. The fashion items include tops, bottoms, dresses, outerwear, shoes, and bags. The top rows primarily feature tops and bottoms, while the middle rows showcase dresses and outerwear. The lower rows display shoes and bags. The overall arrangement suggests a catalog or a visual representation of a fashion dataset, possibly for machine learning or design purposes.

(a) Visualization of the initialized dataset at IPC = 50 in C-FMNIST (BG). The background of each class is dominated by one color.

Image /page/23/Figure/2 description: The image displays a grid of fashion items, organized by color and type. The top rows feature tops like t-shirts and sweaters, followed by pants and jackets. The middle rows showcase dresses and skirts, while the lower rows display shoes and bags. The items are arranged in a rainbow color spectrum, with each row generally corresponding to a different color or a mix of colors. The overall impression is a catalog or collection of clothing and accessories presented in a visually organized manner.

(b) Visualization of the condensed dataset at IPC = 50 in C-FMNIST (BG) using Vanilla DM. The background of each class inherits the bias.

Image /page/23/Figure/4 description: The image is a grid of 10 rows and 20 columns, with each cell containing a colorful, abstract representation of clothing items. The background colors of the cells vary, including bright pink, yellow, blue, green, red, and purple. The clothing items depicted are diverse and include t-shirts, pants, jackets, dresses, shoes, and bags. The style of the images is somewhat pixelated and artistic, with bold outlines and vibrant color palettes. The overall impression is a visually stimulating collage of fashion items presented in a pop-art style.

(c) Visualization of the condensed dataset at IPC = 50 in C-FMNIST (BG) using FairDD + DM. The background of each class mitigates such bias.

Figure 11: Visualization comparison on C-FMNIST (BG) between vanilla DM and FairDD + DM.

Image /page/24/Picture/0 description: The image is a grid of 100 small images, arranged in 10 rows and 10 columns. The images are a mix of black and white and color photographs. The subjects of the images include airplanes, cars, various animals such as dogs, cats, birds, horses, deer, and sheep, as well as ships and trucks. The grid appears to be a dataset or a collection of sample images.

(a) Visualization of the initialized dataset at IPC = 50 in CIFAR10-S. The top five classes (rows) are dominated by the grayscale images, and color ones dominate the bottle five.

Image /page/24/Picture/2 description: The image is a grid of many small images, arranged in rows and columns. The grid appears to be a collection of diverse images, possibly from a dataset or a visual experiment. The top rows predominantly feature black and white images of vehicles, such as cars and trucks, and some aerial views. The middle rows transition to color images, showcasing a variety of animals like dogs, cats, horses, and birds, as well as some natural scenes with greenery. The bottom rows continue with color images, featuring more vehicles, boats, and some outdoor scenes. The overall impression is a mosaic of different subjects, with a mix of monochrome and color photographs.

(b) Visualization of the condensed dataset at IPC = 50 in CIFAR10-S using Vanilla DM. The foreground and background of each class inherit the bias.

Image /page/24/Picture/4 description: The image is a grid of 100 smaller images, arranged in 10 rows and 10 columns. Each smaller image appears to be a photograph of an object or scene. The overall impression is a collage of diverse subjects, with many images featuring animals such as dogs, cats, and birds, as well as vehicles like cars and airplanes, and some scenes of nature or landscapes. The quality of the smaller images varies, with some appearing clearer than others, and some showing signs of digital noise or artifacting, suggesting they might be generated or processed images.

(c) Visualization of the condensed dataset at IPC = 50 in CIFAR10-S using FairDD + DM. The foreground and background of each class mitigate such bias.

Figure 12: Visualization comparison on CIFAR10-S between vanilla DM and FairDD + DM.

<span id="page-25-0"></span>Image /page/25/Picture/0 description: The image displays a grid of celebrity headshots, arranged in two rows of eight images each. Several of the images are highlighted with a red border. The top row features individuals like an Asian woman blowing a kiss, a blonde man, a woman on a tennis court, a man in sunglasses, a man in a fedora, an Asian woman in profile, an Asian woman with dark hair, and a man with curly hair. The bottom row includes a blonde woman, a woman with voluminous hair, a man with short brown hair, a woman with dark hair, a woman in traditional Indian attire, a woman with long brown hair, a woman with short brown hair, and a woman in Indian bridal attire. The red borders highlight specific pairs of images: the man with short brown hair and the woman with dark hair in the bottom row, and the woman with short brown hair and the man with dark hair in the bottom row.

(a) Visualization of the initialized dataset at IPC = 10 in CelebA. The top row is dominated by the male, and the female dominates the bottom row.

Image /page/25/Picture/2 description: The image displays a grid of 16 generated faces, arranged in two rows of eight. The faces are somewhat pixelated and appear to be computer-generated. Several faces are highlighted with red borders: the two faces in the second column of the bottom row, and the two faces in the fifth column of the bottom row.

(b) Visualization of the condensed dataset at IPC = 10 in CelebA using Vanilla DM. The synthetic dataset inherits the gender bias.

Image /page/25/Picture/4 description: The image displays a grid of 12 generated faces, arranged in two rows of six. The faces are somewhat abstract and pixelated, with varying skin tones, hair colors, and expressions. Some faces are clearly defined, while others are more blurred. Four of the faces are highlighted with a red border: the first two faces in the second row and the last two faces in the second row.

(c) Visualization of the condensed dataset at IPC = 10 in CelebA using FairDD + DM. The synthetic dataset mitigates the gender bias.

Figure 13: Visualization comparison on CelebA between vanilla DM and FairDD + DM.

O COMPLETE RESULTS IN THE FORMAT OF MEAN  $\pm$  STANDARD DEVIATION.

<span id="page-27-0"></span>

|                        |                                                                                                                                                                                                                                                                                                                                                                                                 | Table 21: Fairness comparison on diverse IPCs. The results are reported in the format of mean $\pm$ standard deviation. |           |                                                                                                                                       |           |                 |            |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |         |                |                                   |  |
|------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------|-----------|---------------------------------------------------------------------------------------------------------------------------------------|-----------|-----------------|------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------|----------------|-----------------------------------|--|
|                        | Methods $\ln c$ Random $\ln M$ DM Dataset $\ln c \ln M$ DEO <sub>M</sub> DEO <sub>M</sub> DEO <sub>M</sub>                                                                                                                                                                                                                                                                                      |                                                                                                                         | DM+FairDD | $\sum_{i=1}^{n}$                                                                                                                      | DC+FairDD | $\overline{D}C$ | IDC+FairDD | DEO   DEO   DEO   DEO   DEO   DEO   DEO   DEO   DEO   DEO   DEO   DEO   DEO   DEO   DEO   DEO<br>DREAM                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               | +FairDD |                | Whole                             |  |
| $\widehat{E}$          | $C-MNIST$ $\left[ \frac{10}{2} \right]$ $\left[ \frac{100.0 \pm 0.009872 \pm 0.40}{90.00 \pm 0.00996 \pm 0.05}$ $17.04 \pm 1.13$<br>$50  100.01 + 0.09958 + 0.12 100.0 + 0.009168 + 1.67 10.05 + 0.75$<br>$1000100000088.64\pm 2.6799.36\pm 0.2766.38\pm 1.80$ 8.17 $\pm$ 0.45                                                                                                                  |                                                                                                                         |           | $4.86 \pm 0.21$ $45.27 \pm 1.53$ $17.45 \pm 0.99$ $22.32 \pm 1.85$ 9.49 $\pm$ 0.94 $64.36 \pm 1.63$ $35.82 \pm 1.59$ $11.88 \pm 1.65$ |           |                 |            | 8700768'S 070760101   8107612 83175181781781782081782077831201817831783178207818201818182018318312182183179101010101010102831<br>$\frac{1.95+0.87}{2.95+0.79} \approx 2.7826.75+2.7826.75+2.14 \times 11.96+0.196 \times 0.00 +0.0091 +45 +1.101224+1.18 \times 6.64+0.44 \times 99 +1.9678.71+2.981148 +0.791 +2.191348 +0.791148 +0.791148 +0.791148 +0.791148 +0.791148 +0.791148 +0.791148 +0.791148 +0$<br>$6.21 \pm 0.30$ 69.30 $\pm$ 1.87 33.30 $\pm$ 1.55 11.88 $\pm$ 1.24                                                                                                                                                                                                                                                                   |         | $6.88 + 0.34$  |                                   |  |
| $\widehat{\mathbf{B}}$ | $100$  100.0 $\pm$ 0.06 89.07 $\pm$ 2.30 100.0 $\pm$ 0.06 52.23 $\pm$ 2.46 6.60 $\pm$ 0.45<br>$C-MNIST$ $\left[ \frac{10}{2} \right]$ $\left[ \frac{100.0 \pm 0.009911 \pm 0.26}{99.000} \right]$ $\left[ \frac{100.0 \pm 0.09997 \pm 0.04}{99.97 \pm 0.04} \right]$ $\left[ 13.42 \pm 1.61 \right]$<br>$50$   $100.0 \pm 0.00977$ + 0.00   0.00 0 + 0.00 0 $\pm 0.88$ + 0.74                   |                                                                                                                         |           |                                                                                                                                       |           |                 |            | $5.25\pm0.22\left[90.66\pm3.0526.38\pm1.592029\pm2.009\pm2.009\pm0.54\right]93.05\pm3.35\pm2.35\pm1.8719566\pm2.48\left[8.05\pm0.31\right]\left[4.15\pm1.5023.30\pm2.9420.41\pm1.22\right]\left[9.45\pm0.21\right]$<br>$6.77\pm0.14$ $ 100.0_{10} 73.60_{12.2,31} 20.66_{11.58}$ 9.94 $\pm$ 0.57 $ 100.0_{10} 0.00$ 88.30 $\pm$ 2.54 $r$ 18.61 $\pm$ 1.52 $7.50_{-0.02}$ $ 100.0_{+2.2} 52.06_{-2.2,16}$ 15.31 $\pm$ 0.68 6.83 $\pm$ 0.63<br>$4.31 \pm 0.19$ $62.63 \pm 1.85$ $20.87 \pm 1.28$<br>$32.58 \pm 1.97$<br>$10.40 \pm 0.64$ $63.24 \pm 1.37$<br>$27.79 \pm 1.42$<br>$12.24 \pm 1.38$<br>$6.32 \pm 0.58$<br>$84.88 \pm 1.34$<br>$22.86 \pm 2.22$<br>$16.33 \pm 1.12$                                                                       |         | $7.80\pm$ 0.25 | $9.70 \pm 0.74$ 5.78 $\pm$ 0.11   |  |
| ίą.                    | $100 100.01009485\pm0.74 100.01000\pm0.0085.11\pm1.092$<br>33.83 $\pm$ 1.92<br>$50$  100.0 $\pm$ 0.00 $94.61$ $\pm$ 1.08  100.0 $\pm$ 0.00 $96.46$ $\pm$ 0.33 24.92 $\pm$ 1.89<br>$\text{C-FMMIST}\left(\text{100.0}\scriptstyle\pm 0.00\scriptstyle\,99118}\scriptstyle\pm 0.11\right 100.0\scriptstyle\pm 0.00\scriptstyle\,9905}\scriptstyle\pm 0.14\scriptstyle\,26.87\scriptstyle\pm 1.69$ |                                                                                                                         |           |                                                                                                                                       |           |                 |            | 06107211 8707070707105107148711488 2014840010004000040100040101517140.87081800100000000000000000000000000000<br>$16.38 + 0.02 + 0.02 + 0.05 + 0.02 + 0.009 = 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.00114 + 0.$<br>$12.75\pm0.40\pm0.3766.45\pm2.065686\pm1.9623.07\pm1.92\left[100.02\pm0.00\right]$                                                                                                                                                                                                                                                                                       |         |                |                                   |  |
| $\frac{1}{2}$          | $50$  100.0 $\pm$ 0.0098.52 $\pm$ 0.63  100.0 $\pm$ 0.0099.71 $\pm$ 0.1024.50 $\pm$ 1.19<br>$C$ -FMNIST $\left  \begin{array}{l} 10^{100.0 \pm 0.0099.40 \pm 0.14} \end{array} \right $ 100.0 $\pm$ 0.00 $^{99.68 \pm 0.05}$ 33.05 $\pm$ 2.35<br>$10001_{00.009605 \pm 0.67}$  100.0 $\pm$ 0.00 $93.88_{\pm 0.66}$ 21.95 $\pm$ 1.11                                                             |                                                                                                                         |           |                                                                                                                                       |           |                 |            | $14.47\pm0.66\left[100.0_{\pm0.00} \times 25.14\pm1.20_{\pm0.04} \times 25.25\pm0.93\right] \times 100.06\pm0.000\left[100.0_{\pm0.04} \times 25.0_{\pm0.04} \times 25.0_{\pm0.04} \times 25.0_{\pm0.04} \times 25.0_{\pm0.04} \times 25.0_{\pm0.04} \times 25.0_{\pm0.04} \times 25.0_{\pm0.04} \times 25.0_{\pm0.04} \times$<br>13.33+10.80 99.70+0.3738+1.80 52.75±3.89 23.48+1.60   100.0+0.00 90.70+1.82 77.09+2.88 36.00+2.62   100.0+0.00+0.00+39.90+1.56 40.00+0.02+6.21.20.94<br>$197.72\pm1.18$ $ 100.06\,00.00\pm0.091\,20.23$ $ 100.06\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm1.58$ $ 100.06\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\pm0.00\$ |         |                | 91.40 $\pm$ 1.85 51.68 $\pm$ 1.34 |  |
|                        | $10^{25.04}_{20.93}$ $829_{\pm 0.65}$ $ 59.20_{\pm 1.27}$ $39.31_{\pm 1.28}$ $31.75_{\pm 1.52}$<br>$CIFAR 10-50$ 50 $ 57.11+1.42 28.89+1.83 75.13+1.24 55.70+1.11 18.28+0.78$<br>$100 66.49 \pm 1.8843.16 \pm 1.45 73.81 \pm 2.1155.10 \pm 1.45 14.77 \pm 0.50$                                                                                                                                 |                                                                                                                         |           |                                                                                                                                       |           |                 |            | $7.35\pm0.89 \begin{bmatrix} 11.46\pm0.045.81\pm2.2234-32\pm2.22 & 11.21\pm0.84 \end{bmatrix} 22.06\pm0.61 \begin{bmatrix} 60.86\pm0.7829.00\pm1.75 & 9.10\pm0.164 \end{bmatrix} 8.04\pm1.28 \begin{bmatrix} 16.80\pm1.2334.02\pm0.861 \end{bmatrix} 4.70\pm1.29 \begin{bmatrix} 6.53\pm0.861 \end{bmatrix} 4.97\pm0.8$<br>$8.73\pm0.71\frac{42.23}{1.723}\pm1.83.27.35\pm1.52.22.08\pm1.16$ $8.22\pm1.16$ $8.23\pm1.26$ $8.24\pm1.26$ $8.24\pm1.26$<br>$5.89\pm0.29\begin{bmatrix} 68.69\pm1.5348.64\pm1.1732.70\pm1.58 & 11.26\pm0.97 & 92.70\pm0.593\pm0.51 & 62.80\pm2.97 & 25.18\pm1.61 & 82.30\pm2.16 & 48.12\pm0.91 & 12.10\pm2.10 & 12.10\pm2.71 & 12.10\pm2.71 & 12.10\pm2.71 & 12.10\pm2.71 & 12.10\pm2.71 & 12.10$                        |         | $6.06\pm0.80$  |                                   |  |
| CelebA                 | $10^{10.48}$ ±1.97 920±2.00 30.01±1.34 28.85±1.33 9.37±0.82<br>$1000$  18.67 $\pm$ 1.19 18.01 $\pm$ 1.16 $ 42.63\pm$ 1.18 41.12 $\pm$ 0.96 10.93 $\pm$ 0.46<br>$50   22.88 \pm 1.41 20.32 \pm 1.02   40.26 \pm 0.94 38.81 \pm 0.70 14.08 \pm 0.32$                                                                                                                                              |                                                                                                                         |           |                                                                                                                                       |           |                 |            | PS OF 01-N 016 14.88 98.16 14.88 14.84 14.84 14.84 14.87 14.87 14.87 14.87 15.27 15.11 14.87 16.94 16.89 14.85 14.85 14.85 14.85 14.94.0 14.184 14.89 14.184 14.89 14.184 14.89 14.184 14.89 14.184 14.89 14.184 14.89 14.184<br>$5.71\pm0.85$ $ 15.48\pm2.62$ $ 4.16\pm2.59$ $6.64\pm1.69$ $5.29\pm1.60$ $5.29\pm1.62$ $ 34.85\pm1.21$ $34.48\pm1.45$ $8.36\pm2.87$ $4.49\pm1.75$ $ 40.75\pm1.26$ $5.670\pm1.21$ $20.21\pm1.20$ $5.36\pm0.89$<br>$6.65\pm0.56\, 29.00\pm2.42\,Z7.52\pm2.21\,18.16\pm2.80\,17.04\pm2.93\, 80.99\pm1.0242.66\pm0.34\,28.27\pm1.65\,17.63\pm1.61\, 52.51\pm0.84\,39.34\pm0.50\,24.87\pm1.82\,15.36\pm1.26$                                                                                                             |         |                |                                   |  |
|                        |                                                                                                                                                                                                                                                                                                                                                                                                 |                                                                                                                         |           |                                                                                                                                       |           |                 |            |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |         |                |                                   |  |

Table 21: Fairness comparison on diverse IPCs. The results are reported in the format of mean  $\pm$  standard deviation.

| Methods                 | $ _{\text{IPC}}$ Random | DM   | +FairDD                                                                                                                                                                                                                                                                                                                                                                                                                                                   | DC   | +FairDD | <b>IDC</b> |      | +FairDD   DREAM +FairDD |                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Whole               |
|-------------------------|-------------------------|------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------|---------|------------|------|-------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------|
| Datasets                | Acc.                    | Acc. | Acc.                                                                                                                                                                                                                                                                                                                                                                                                                                                      | Acc. | Acc.    | Acc.       | Acc. | Acc.                    | Acc.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | Acc.                |
| C-MNIST<br>(FG)         |                         |      | $10\begin{array}{l l}30.75_{\pm0.96} \end{array} 25.01_{\pm0.94} 94.61_{\pm0.21} 71.41_{\pm1.27} 90.62_{\pm0.28} 53.06_{\pm1.13} 95.67_{\pm0.20} 75.04_{\pm1.86} 94.04_{\pm0.14}$                                                                                                                                                                                                                                                                         |      |         |            |      |                         | 50 47.38 $\pm$ 0.98 56.84 $\pm$ 1.92 96.58 $\pm$ 0.08 90.54 $\pm$ 0.43 92.68 $\pm$ 0.18 88.55 $\pm$ 0.38 96.77 $\pm$ 0.07 91.02 $\pm$ 0.68 94.59 $\pm$ 0.23                                                                                                                                                                                                                                                                                                                                                                                                                                                                            | $97.71_{\pm0.09}$   |
|                         |                         |      |                                                                                                                                                                                                                                                                                                                                                                                                                                                           |      |         |            |      |                         | $100\, \,67.41_{\pm1.08}\, \,78.04_{\pm1.24}\, \,96.79_{\pm0.14}\, \,91.64_{\pm0.33}\, \,93.23_{\pm0.19}\, \,90.39_{\pm0.48}\, \,97.11_{\pm0.08}\, \,88.87_{\pm1.04}\, \,95.16_{\pm0.12}\, \,95.01_{\pm0.08}\, \,96.01_{\pm0.07}\, \,97.01_{\pm0.08}\, \,98.01_{\pm0.07}\, \,99.01$                                                                                                                                                                                                                                                                                                                                                    |                     |
| C-MNIST<br>(BG)         |                         |      | $10\ \left 27.95_{\pm 0.75}\right 23.40_{\pm 0.57}\ \left 23.40_{\pm 0.57}\ \left 94.88_{\pm 0.13}\right \right.\nonumber\\ 65.91_{\pm 1.91}\ \left 90.84_{\pm 0.19}\right \left 62.09_{\pm 1.06}\ \left 94.84_{\pm 0.21}\right \right.\nonumber\\ \left 79.81_{\pm 1.37}\ \left 93.54_{\pm 0.26}\ \left 10.95\right \right \right)\nonumber\\$                                                                                                           |      |         |            |      |                         | 50 $ 45.52_{\pm 0.98} 47.74_{\pm 1.35} 96.86_{\pm 0.09} 88.53_{\pm 0.61} 92.20_{\pm 0.19} 86.14_{\pm 0.74} 95.29_{\pm 0.13} 89.24_{\pm 0.82} 93.20_{\pm 0.50} 89.24_{\pm 0.82} 89.20_{\pm 0.50} 89.20_{\pm 0.61} 89.20_{\pm 0.61} 89.20_{\pm 0.61} 89.20_{\pm 0.61} 89.20_{$<br>$100\begin{pmatrix} 67.28_{\pm 1.31} & 79.87_{\pm 0.77} & 97.33_{\pm 0.09} & 90.20_{\pm 0.57} & 92.73_{\pm 0.17} & 89.66_{\pm 0.66} & 95.84_{\pm 0.06} & 90.70_{\pm 0.70} & 94.06_{\pm 0.24} \end{pmatrix}$                                                                                                                                            | $^{97.80}\pm 0.07$  |
| <b>C-FMNIST</b><br>(FG) |                         |      | $10\left[32.80_{\pm 1.44}\right]33.35_{\pm 1.27}\right.77.09_{\pm 0.33}\left[60.77_{\pm 0.88}\right.76.01_{\pm 0.19}\left[44.08_{\pm 1.64}\right.79.66_{\pm 0.21}\left[49.72_{\pm 1.07}\right.77.24_{\pm 0.15}\right]$                                                                                                                                                                                                                                    |      |         |            |      |                         | $50$ $\left  42.48_{\pm 1.05} \right $ $49.94_{\pm 0.75}$ $82.11_{\pm 0.20}$ $\left  69.08_{\pm 0.54}$ $75.83_{\pm 0.33} \right $ $64.45_{\pm 0.69}$ $80.80_{\pm 0.33}$ $\left  65.69_{\pm 1.04}$ $78.79_{\pm 1.07}$<br>$100\, \textcolor{red}{\big }\, 55.31\, \textcolor{red}{\pm 0.67} \,\textcolor{red}{\big }\, 57.99\, \textcolor{red}{\pm 0.84}\, \textcolor{red}{\big }\, 83.25\, \textcolor{red}{\pm 0.19}\, \textcolor{red}{\big }\, 68.84\, \textcolor{red}{\pm 0.61}\, \textcolor{red}{\big }\, 74.91\, \textcolor{red}{\pm 0.40}\, \textcolor{red}{\big }\, 66.37\, \textcolor{red}{\pm 0.26}\, \textcolor{red}{\big }\,$ | $82.94_{\pm0.14}$   |
| <b>C-FMNIST</b><br>(BG) |                         |      | $10\left.\left\lbrack 24.96_{\pm 0.63} \right\rbrack 22.26_{\pm 0.77}\right.71.10_{\pm 0.43}\left\lbrack 47.32_{\pm 0.96}\right.\\ \left. 68.51_{\pm 0.38}\left\lbrack 37.59_{\pm 0.76}\right\rbrack 72.67_{\pm 0.20}\left\lbrack 45.30_{\pm 0.78}\right\rbrack 71.56_{\pm 0.34}\right.\\$                                                                                                                                                                |      |         |            |      |                         | 50 34.92 10.57 36.27 10.72 $79.07_{\pm 0.72}$ $79.07_{\pm 0.27}$ $60.58_{\pm 0.72}$ $75.80_{\pm 0.28}$ $46.20_{\pm 1.10}$ $73.72_{\pm 0.43}$ $53.62_{\pm 0.50}$ $72.80_{\pm 0.11}$<br>$100\,44.87_{\pm0.65}\,49.30_{\pm0.58}\,80.63_{\pm0.16}\,  \text{62.70}_{\pm0.66}\,71.76_{\pm0.28}\,  \text{48.61}_{\pm1.00}\,73.18_{\pm0.57}\,  \text{53.32}_{\pm0.78}\,73.00_{\pm0.39}$                                                                                                                                                                                                                                                        | ${77.97}_{\pm0.44}$ |
| CIFAR10-S               |                         |      | $10\ \left 23.60_{\pm 0.32}\right 37.88_{\pm 0.27}\ \left 45.17_{\pm 0.46}\right 37.88_{\pm 0.76}\ \left 41.82_{\pm 0.70}\right 48.30_{\pm 0.79}\ \left 56.40_{\pm 0.37}\right \ 55.09_{\pm 0.43}\ \left 58.40_{\pm 0.24}\right 48.30_{\pm 0.37}\ \left 21.33_{\pm 0.37}\right 48.30_{\pm 0.37}\ \left 25.33_{\pm 0.37}\right$<br>50 36.46 + 0.49 45.02 + 0.44 58.84 + 0.23 41.28 + 0.80 49.26 + 0.42 47.26 + 0.73 57.84 + 0.24 57.59 + 0.93 61.85 + 0.33 |      |         |            |      |                         | $100\Big 39.34_{\pm0.56}\Big 48.11_{\pm0.63} \ 61.33_{\pm0.37}\Big 42.73_{\pm0.73} \ 51.74_{\pm0.52}\Big 47.27_{\pm1.66} \ 56.98_{\pm0.85}\Big 57.14_{\pm0.26} \ 62.70_{\pm0.37}\Big $                                                                                                                                                                                                                                                                                                                                                                                                                                                 | $69.78_{\pm0.18}$   |
| CelebA                  |                         |      | $10\left[54.51_{\pm 1.63}\right]61.79_{\pm 0.82} \right.\left. 64.37_{\pm 0.31}\right[57.19_{\pm 2.31} \right.\left. 57.63_{\pm 1.49}\right]61.49_{\pm 0.57} \right.\left. 63.54_{\pm 0.73}\right]64.38_{\pm 0.33} \right.\left. 66.26_{\pm 0.18}\right]$                                                                                                                                                                                                 |      |         |            |      |                         | $50$ $55.99_{\pm 1.23}$ 64.61 $_{\pm 0.25}$ 68.50 $_{\pm 0.58}$ 60.16 $_{\pm 1.57}$ 59.89 $_{\pm 1.11}$ 60.75 $_{\pm 0.37}$ 66.89 $_{\pm 0.28}$ 64.62 $_{\pm 0.44}$ 68.26 $_{\pm 0.37}$<br>$100\, 60.62_{\pm 0.71}\, 65.13_{\pm 0.30}\, \,68.84_{\pm 0.32}\, 62.53_{\pm 2.42}\, \,61.89_{\pm 1.91}\, 64.04_{\pm 0.68}\, \,67.24_{\pm 0.58}\, 62.58_{\pm 0.45}\, \,64.12_{\pm 0.28}\, 63.53_{\pm 0.45}\, 65.53_{\pm 0.46}\, 66.53_{\pm 0.47}\, 67.53_{\pm 0.47}\, 68$                                                                                                                                                                   | $^{74.09} \pm 0.27$ |

<span id="page-28-0"></span>Table 22: Accuracy comparison on diverse IPCs. The results are reported in the format of mean  $\pm$ standard deviation.

| Method                  | Cross arch. | DM                                                                       |                                                                          |                                                                          | DM+FairDD                                                                |                                                                          |                                                                          |
|-------------------------|-------------|--------------------------------------------------------------------------|--------------------------------------------------------------------------|--------------------------------------------------------------------------|--------------------------------------------------------------------------|--------------------------------------------------------------------------|--------------------------------------------------------------------------|
|                         |             | $DEO_M$                                                                  | $DEO_A$                                                                  | Acc.                                                                     | $DEO_M$                                                                  | $DEO_A$                                                                  | Acc.                                                                     |
| <b>C-MNIST</b><br>(FG)  | ConvNet     | 100.0 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.00$ | 91.68 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.67$ | 56.84 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.92$ | 10.05 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.75$ | 5.46 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.40$  | 96.58 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.80$ |
|                         | AlexNet     | 100.0 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.00$ | 98.82 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.63$ | 44.02 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 3.34$ | 10.35 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.60$ | 6.16 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.39$  | 96.12 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.19$ |
|                         | VGG11       | 99.70 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.02$ | 70.73 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 3.44$ | 75.22 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.41$ | 9.55 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.66$  | 5.39 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.36$  | 96.80 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.16$ |
|                         | ResNet18    | 100.0 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.00$ | 96.00 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.07$ | 52.05 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.93$ | 8.40 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.50$  | 4.63 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.27$  | 97.13 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.18$ |
|                         | Mean        | 99.93                                                                    | 89.31                                                                    | 57.03                                                                    | 9.59                                                                     | 5.41                                                                     | 96.66                                                                    |
| <b>C-FMNIST</b><br>(BG) | ConvNet     | 100.0 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.00$ | 99.71 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.10$ | 36.27 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.72$ | 24.50 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.19$ | 14.47 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.66$ | 79.07 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.27$ |
|                         | AlexNet     | 100.0 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.00$ | 99.75 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.12$ | 22.72 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.60$ | 20.60 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.03$ | 14.11 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.85$ | 76.14 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.71$ |
|                         | VGG11       | 100.0 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.00$ | 97.77 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.98$ | 43.11 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.79$ | 21.60 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.76$ | 14.36 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.91$ | 78.57 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.24$ |
|                         | ResNet18    | 100.0 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.00$ | 99.78 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.09$ | 23.37 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.11$ | 22.50 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.09$ | 14.96 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.81$ | 75.21 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.53$ |
|                         | Mean        | 100.0                                                                    | 99.25                                                                    | 31.37                                                                    | 22.30                                                                    | 14.73                                                                    | 77.25                                                                    |
| <b>CIFAR10-S</b>        | ConvNet     | 75.13 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.24$ | 55.70 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.11$ | 45.02 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.44$ | 18.28 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.78$ | 7.35 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.59$  | 58.84 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.23$ |
|                         | AlexNet     | 75.30 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.90$ | 52.57 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.29$ | 36.09 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.51$ | 15.84 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.11$ | 5.12 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.28$  | 49.16 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.66$ |
|                         | VGG11       | 61.48 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.91$ | 44.05 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.40$ | 43.23 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.55$ | 11.51 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.77$ | 4.16 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.32$  | 52.65 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.76$ |
|                         | ResNet18    | 76.23 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.72$ | 54.35 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.99$ | 38.03 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.51$ | 16.44 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.98$ | 5.14 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.50$  | 50.93 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.77$ |
|                         | Mean        | 72.04                                                                    | 51.67                                                                    | 40.59                                                                    | 15.27                                                                    | 5.44                                                                     | 52.90                                                                    |
| <b>CelebA</b>           | ConvNet     | 40.26 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.94$ | 38.81 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.70$ | 64.61 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.25$ | 14.08 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.32$ | 9.87 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.00$  | 68.50 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.58$ |
|                         | AlexNet     | 32.51 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.85$ | 31.62 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.03$ | 63.10 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.55$ | 9.38 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.86$  | 5.75 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.76$  | 64.24 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.80$ |
|                         | VGG11       | 26.03 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 2.10$ | 24.63 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.97$ | 61.57 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.79$ | 8.95 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.97$  | 6.32 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.10$  | 62.05 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.61$ |
|                         | ResNet18    | 25.60 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.87$ | 24.93 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.75$ | 60.32 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.82$ | 6.72 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.81$  | 4.29 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 0.67$  | 61.80 $	hinspace
aisebox{0.3ex}{$	extstyle	ext{ 	extpm}$}	hinspace 1.07$ |
|                         | Mean        | 31.10                                                                    | 30.25                                                                    | 62.40                                                                    | 9.78                                                                     | 6.58                                                                     | 64.15                                                                    |

<span id="page-29-1"></span>Table 23: Cross-arch. comparison. The results are reported in the format of mean  $\pm$  standard deviation.

<span id="page-29-0"></span>Table 24: Ablation on BR at IPC = 50. The results are reported in the format of mean  $\pm$  standard deviation.

| Methods<br>Dataset | BR   | DM                                                   |                                                      |                                                      | DM+FairDD                                            |                                                      |                                                      |
|--------------------|------|------------------------------------------------------|------------------------------------------------------|------------------------------------------------------|------------------------------------------------------|------------------------------------------------------|------------------------------------------------------|
|                    |      | $\[\overline{\text{DEO}}_\text{M}$ ]                 | $\[\overline{DEO_A}$ ]                               | Acc.                                                 | $\[\overline{DEO}}_M$ ]                              | $\[\overline{DEO_A}$ ]                               | Acc.                                                 |
| C-MNIST<br>(FG)    | 0.85 | 99.54 <span style="vertical-align:sub;">±0.37</span> | 70.13 <span style="vertical-align:sub;">±1.69</span> | 76.24 <span style="vertical-align:sub;">±1.68</span> | 10.13 <span style="vertical-align:sub;">±0.75</span> | 5.20 <span style="vertical-align:sub;">±0.33</span>  | 96.62 <span style="vertical-align:sub;">±0.11</span> |
|                    | 0.90 | 100.0 <span style="vertical-align:sub;">±0.00</span> | 91.68 <span style="vertical-align:sub;">±1.67</span> | 56.84 <span style="vertical-align:sub;">±1.92</span> | 10.05 <span style="vertical-align:sub;">±0.75</span> | 5.46 <span style="vertical-align:sub;">±0.40</span>  | 96.58 <span style="vertical-align:sub;">±0.08</span> |
|                    | 0.95 | 100.0 <span style="vertical-align:sub;">±0.00</span> | 100.0 <span style="vertical-align:sub;">±0.00</span> | 33.73 <span style="vertical-align:sub;">±1.08</span> | 10.30 <span style="vertical-align:sub;">±0.74</span> | 5.84 <span style="vertical-align:sub;">±0.39</span>  | 96.05 <span style="vertical-align:sub;">±0.17</span> |
| C-FMNIST<br>(BG)   | 0.85 | 100.0 <span style="vertical-align:sub;">±0.00</span> | 95.54 <span style="vertical-align:sub;">±0.79</span> | 46.14 <span style="vertical-align:sub;">±0.93</span> | 23.75 <span style="vertical-align:sub;">±1.58</span> | 13.85 <span style="vertical-align:sub;">±0.82</span> | 79.61 <span style="vertical-align:sub;">±0.17</span> |
|                    | 0.90 | 100.0 <span style="vertical-align:sub;">±0.00</span> | 99.71 <span style="vertical-align:sub;">±0.10</span> | 36.27 <span style="vertical-align:sub;">±0.72</span> | 24.50 <span style="vertical-align:sub;">±1.19</span> | 14.47 <span style="vertical-align:sub;">±0.66</span> | 79.07 <span style="vertical-align:sub;">±0.27</span> |
|                    | 0.95 | 100.0 <span style="vertical-align:sub;">±0.00</span> | 99.79 <span style="vertical-align:sub;">±0.08</span> | 26.30 <span style="vertical-align:sub;">±0.44</span> | 29.15 <span style="vertical-align:sub;">±1.30</span> | 17.72 <span style="vertical-align:sub;">±0.90</span> | 78.46 <span style="vertical-align:sub;">±0.25</span> |
| CIFAR10-S          | 0.85 | 71.75 <span style="vertical-align:sub;">±0.90</span> | 50.11 <span style="vertical-align:sub;">±0.70</span> | 46.99 <span style="vertical-align:sub;">±0.43</span> | 16.44 <span style="vertical-align:sub;">±0.88</span> | 6.58 <span style="vertical-align:sub;">±0.76</span>  | 59.12 <span style="vertical-align:sub;">±0.32</span> |
|                    | 0.90 | 75.13 <span style="vertical-align:sub;">±1.24</span> | 55.70 <span style="vertical-align:sub;">±1.11</span> | 45.02 <span style="vertical-align:sub;">±0.44</span> | 18.28 <span style="vertical-align:sub;">±0.78</span> | 7.35 <span style="vertical-align:sub;">±0.59</span>  | 58.84 <span style="vertical-align:sub;">±0.23</span> |
|                    | 0.95 | 75.43 <span style="vertical-align:sub;">±1.28</span> | 58.58 <span style="vertical-align:sub;">±0.82</span> | 43.56 <span style="vertical-align:sub;">±0.38</span> | 17.49 <span style="vertical-align:sub;">±1.26</span> | 7.10 <span style="vertical-align:sub;">±0.91</span>  | 58.18 <span style="vertical-align:sub;">±0.30</span> |

<span id="page-30-0"></span>Table 25: Ablation on initialization at IPC = 50. The results are reported in the format of mean  $\pm$ standard deviation.

| Methods<br>Dataset | Init.  | DM                  |                     |                     | DM+FairDD           |                     |                     |
|--------------------|--------|---------------------|---------------------|---------------------|---------------------|---------------------|---------------------|
|                    |        | $DEO_M$             | $DEO_A$             | Acc.                | $DEO_M$             | $DEO_A$             | Acc.                |
| C-MNIST<br>(FG)    | Random | 100.0 $_{\pm 0.00}$ | 91.68 $_{\pm 1.67}$ | 56.84 $_{\pm 1.92}$ | 10.05 $_{\pm 0.75}$ | 5.46 $_{\pm 0.40}$  | 96.58 $_{\pm 0.08}$ |
|                    | Noise  | 100.0 $_{\pm 0.00}$ | 99.64 $_{\pm 0.39}$ | 41.45 $_{\pm 1.46}$ | 9.33 $_{\pm 0.63}$  | 5.28 $_{\pm 0.29}$  | 96.06 $_{\pm 0.10}$ |
|                    | Hybrid | 100.0 $_{\pm 0.00}$ | 99.06 $_{\pm 0.74}$ | 39.97 $_{\pm 1.77}$ | 9.03 $_{\pm 0.62}$  | 5.33 $_{\pm 0.25}$  | 96.27 $_{\pm 0.09}$ |
| C-FMNIST<br>(BG)   | Random | 100.0 $_{\pm 0.00}$ | 99.71 $_{\pm 0.10}$ | 36.27 $_{\pm 0.72}$ | 24.50 $_{\pm 1.19}$ | 14.47 $_{\pm 0.66}$ | 79.07 $_{\pm 0.27}$ |
|                    | Noise  | 100.0 $_{\pm 0.00}$ | 99.67 $_{\pm 0.07}$ | 22.92 $_{\pm 0.78}$ | 23.00 $_{\pm 1.93}$ | 14.40 $_{\pm 0.63}$ | 78.84 $_{\pm 0.20}$ |
|                    | Hybrid | 100.0 $_{\pm 0.00}$ | 99.68 $_{\pm 0.07}$ | 26.38 $_{\pm 0.80}$ | 21.45 $_{\pm 1.32}$ | 14.34 $_{\pm 0.89}$ | 79.19 $_{\pm 0.16}$ |
| CIFAR10-S          | Random | 75.13 $_{\pm 1.24}$ | 55.70 $_{\pm 1.11}$ | 45.02 $_{\pm 0.44}$ | 18.28 $_{\pm 0.78}$ | 7.35 $_{\pm 0.59}$  | 58.84 $_{\pm 0.23}$ |
|                    | Noise  | 55.28 $_{\pm 1.57}$ | 37.26 $_{\pm 0.63}$ | 46.97 $_{\pm 0.29}$ | 16.15 $_{\pm 0.62}$ | 6.13 $_{\pm 0.50}$  | 56.41 $_{\pm 0.30}$ |
|                    | Hybrid | 65.59 $_{\pm 1.38}$ | 46.18 $_{\pm 0.84}$ | 45.16 $_{\pm 0.40}$ | 17.30 $_{\pm 1.26}$ | 6.71 $_{\pm 0.33}$  | 56.78 $_{\pm 0.24}$ |
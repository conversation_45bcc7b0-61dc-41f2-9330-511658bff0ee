From <PERSON><PERSON>' theorem, the posterior distribution for  $\alpha$  and  $\beta$  is given by

$$
p(\alpha, \beta | \mathbf{t}) \propto p(\mathbf{t} | \alpha, \beta) p(\alpha, \beta). \tag{3.76}
$$

If the prior is relatively flat, then in the evidence framework the values of  $\hat{\alpha}$  and β are obtained by maximizing the marginal likelihood function  $p(\mathbf{t} | \alpha, \beta)$ . We shall proceed by evaluating the marginal likelihood for the linear basis function model and then finding its maxima. This will allow us to determine values for these hyperparameters from the training data alone, without recourse to cross-validation. Recall that the ratio  $\alpha/\beta$  is analogous to a regularization parameter.

As an aside it is worth noting that, if we define conjugate (Gamma) prior distributions over  $\alpha$  and  $\beta$ , then the marginalization over these hyperparameters in (3.74) can be performed analytically to give a Student's t-distribution over **w** (see Section 2.3.7). Although the resulting integral over **w** is no longer analytically tractable, it might be thought that approximating this integral, for example using the Laplace approximation discussed (Section 4.4) which is based on a local Gaussian approximation centred on the mode of the posterior distribution, might provide a practical alternative to the evidence framework (<PERSON><PERSON><PERSON> and <PERSON>, 1991). However, the integrand as a function of **w** typically has a strongly skewed mode so that the Laplace approximation fails to capture the bulk of the probability mass, leading to poorer results than those obtained by maximizing the evidence (MacKay, 1999).

Returning to the evidence framework, we note that there are two approaches that we can take to the maximization of the log evidence. We can evaluate the evidence function analytically and then set its derivative equal to zero to obtain re-estimation equations for  $\alpha$  and  $\beta$ , which we shall do in Section 3.5.2. Alternatively we use a technique called the expectation maximization (EM) algorithm, which will be discussed in Section 9.3.4 where we shall also show that these two approaches converge to the same solution.

# **3.5.1 Evaluation of the evidence function**

The marginal likelihood function  $p(\mathbf{t}|\alpha, \beta)$  is obtained by integrating over the weight parameters **w**, so that

$$
p(\mathbf{t}|\alpha,\beta) = \int p(\mathbf{t}|\mathbf{w},\beta)p(\mathbf{w}|\alpha) \, \mathrm{d}\mathbf{w}.
$$
 (3.77)

One way to evaluate this integral is to make use once again of the result  $(2.115)$ *Exercise 3.16* for the conditional distribution in a linear-Gaussian model. Here we shall evaluate the integral instead by completing the square in the exponent and making use of the standard form for the normalization coefficient of a Gaussian.

*Exercise 3.17*

*Exercise 3.17* From (3.11), (3.12), and (3.52), we can write the evidence function in the form

$$
p(\mathbf{t}|\alpha,\beta) = \left(\frac{\beta}{2\pi}\right)^{N/2} \left(\frac{\alpha}{2\pi}\right)^{M/2} \int \exp\left\{-E(\mathbf{w})\right\} d\mathbf{w} \tag{3.78}
$$

### **3.5. The Evidence Approximation 167**

where <sup>M</sup> is the dimensionality of **w**, and we have defined

$$
E(\mathbf{w}) = \beta E_D(\mathbf{w}) + \alpha E_W(\mathbf{w})
$$
  
=  $\frac{\beta}{2} ||\mathbf{t} - \mathbf{\Phi} \mathbf{w}||^2 + \frac{\alpha}{2} \mathbf{w}^T \mathbf{w}.$  (3.79)

We recognize  $(3.79)$  as being equal, up to a constant of proportionality, to the reg-*Exercise 3.18* ularized sum-of-squares error function (3.27). We now complete the square over **w** giving

$$
E(\mathbf{w}) = E(\mathbf{m}_N) + \frac{1}{2}(\mathbf{w} - \mathbf{m}_N)^{\mathrm{T}} \mathbf{A}(\mathbf{w} - \mathbf{m}_N)
$$
(3.80)

where we have introduced

$$
\mathbf{A} = \alpha \mathbf{I} + \beta \mathbf{\Phi}^{\mathrm{T}} \mathbf{\Phi}
$$
 (3.81)

together with

$$
E(\mathbf{m}_N) = \frac{\beta}{2} \left\| \mathbf{t} - \mathbf{\Phi} \mathbf{m}_N \right\|^2 + \frac{\alpha}{2} \mathbf{m}_N^{\mathrm{T}} \mathbf{m}_N. \tag{3.82}
$$

Note that **A** corresponds to the matrix of second derivatives of the error function

$$
\mathbf{A} = \nabla \nabla E(\mathbf{w}) \tag{3.83}
$$

and is known as the *Hessian matrix*. Here we have also defined  $\mathbf{m}_N$  given by

$$
\mathbf{m}_N = \beta \mathbf{A}^{-1} \mathbf{\Phi}^{\mathrm{T}} \mathbf{t}.\tag{3.84}
$$

Using (3.54), we see that  $\mathbf{A} = \mathbf{S}_N^{-1}$ , and hence (3.84) is equivalent to the previous definition (3.53) and therefore represents the mean of the posterior distribution definition (3.53), and therefore represents the mean of the posterior distribution.

The integral over **w** can now be evaluated simply by appealing to the standard *Exercise 3.19* result for the normalization coefficient of a multivariate Gaussian, giving

$$
\int \exp\{-E(\mathbf{w})\} d\mathbf{w}
$$

$$
= \exp\{-E(\mathbf{m}_N)\} \int \exp\{-\frac{1}{2}(\mathbf{w} - \mathbf{m}_N)^T \mathbf{A}(\mathbf{w} - \mathbf{m}_N)\} d\mathbf{w}
$$

$$
= \exp\{-E(\mathbf{m}_N)\} (2\pi)^{M/2} |\mathbf{A}|^{-1/2}. \quad (3.85)
$$

Using (3.78) we can then write the log of the marginal likelihood in the form

$$
\ln p(\mathbf{t}|\alpha,\beta) = \frac{M}{2}\ln \alpha + \frac{N}{2}\ln \beta - E(\mathbf{m}_N) - \frac{1}{2}\ln |\mathbf{A}| - \frac{N}{2}\ln(2\pi) \tag{3.86}
$$

which is the required expression for the evidence function.

Returning to the polynomial regression problem, we can plot the model evidence against the order of the polynomial, as shown in Figure 3.14. Here we have assumed a prior of the form (1.65) with the parameter  $\alpha$  fixed at  $\alpha = 5 \times 10^{-3}$ . The form of this plot is very instructive. Referring back to Figure 1.4, we see that the  $M = 0$ polynomial has very poor fit to the data and consequently gives a relatively low value

*Exercise 3.70*

*Exercise 3.19*

**Figure 3.14** Plot of the model evidence versus the order  $M$ , for the polynomial regression model, showing that the evidence favours the model with  $M = 3$ .

Image /page/2/Figure/2 description: A line graph shows the relationship between M on the x-axis and values ranging from -18 to -26 on the y-axis. The line starts at approximately M=0 with a y-value of -24, rises to -18 at M=3, drops to -22 at M=2, rises again to -18 at M=3, then decreases linearly to approximately -25.5 at M=9.

for the evidence. Going to the  $M = 1$  polynomial greatly improves the data fit, and hence the evidence is significantly higher. However, in going to  $M = 2$ , the data fit is improved only very marginally, due to the fact that the underlying sinusoidal function from which the data is generated is an odd function and so has no even terms in a polynomial expansion. Indeed, Figure 1.5 shows that the residual data error is reduced only slightly in going from  $M = 1$  to  $M = 2$ . Because this richer model suffers a greater complexity penalty, the evidence actually falls in going from  $M = 1$ to  $M = 2$ . When we go to  $M = 3$  we obtain a significant further improvement in data fit, as seen in Figure 1.4, and so the evidence is increased again, giving the highest overall evidence for any of the polynomials. Further increases in the value of M produce only small improvements in the fit to the data but suffer increasing complexity penalty, leading overall to a decrease in the evidence values. Looking again at Figure 1.5, we see that the generalization error is roughly constant between  $M = 3$  and  $M = 8$ , and it would be difficult to choose between these models on the basis of this plot alone. The evidence values, however, show a clear preference for  $M = 3$ , since this is the simplest model which gives a good explanation for the observed data.

# **3.5.2 Maximizing the evidence function**

Let us first consider the maximization of  $p(\mathbf{t}|\alpha, \beta)$  with respect to  $\alpha$ . This can be done by first defining the following eigenvector equation

$$
\left(\beta\mathbf{\Phi}^{\mathrm{T}}\mathbf{\Phi}\right)\mathbf{u}_{i}=\lambda_{i}\mathbf{u}_{i}.
$$
 (3.87)

From (3.81), it then follows that **A** has eigenvalues  $\alpha + \lambda_i$ . Now consider the derivative of the term involving  $\ln |A|$  in (3.86) with respect to  $\alpha$ . We have

$$
\frac{d}{d\alpha}\ln|\mathbf{A}| = \frac{d}{d\alpha}\ln\prod_{i}(\lambda_i + \alpha) = \frac{d}{d\alpha}\sum_{i}\ln(\lambda_i + \alpha) = \sum_{i}\frac{1}{\lambda_i + \alpha}.\tag{3.88}
$$

Thus the stationary points of (3.86) with respect to  $\alpha$  satisfy

$$
0 = \frac{M}{2\alpha} - \frac{1}{2}\mathbf{m}_N^{\mathrm{T}}\mathbf{m}_N - \frac{1}{2}\sum_i \frac{1}{\lambda_i + \alpha}.
$$
 (3.89)

### **3.5. The Evidence Approximation 169**

Multiplying through by  $2\alpha$  and rearranging, we obtain

$$
\alpha \mathbf{m}_N^{\mathrm{T}} \mathbf{m}_N = M - \alpha \sum_i \frac{1}{\lambda_i + \alpha} = \gamma.
$$
 (3.90)

Since there are M terms in the sum over i, the quantity  $\gamma$  can be written

$$
\gamma = \sum_{i} \frac{\lambda_i}{\alpha + \lambda_i}.
$$
\n(3.91)

The interpretation of the quantity  $\gamma$  will be discussed shortly. From (3.90) we see *Exercise* 3.20 that the value of  $\alpha$  that maximizes the marginal likelihood satisfies

$$
\alpha = \frac{\gamma}{\mathbf{m}_N^{\mathrm{T}} \mathbf{m}_N}.
$$
\n(3.92)

Note that this is an implicit solution for  $\alpha$  not only because  $\gamma$  depends on  $\alpha$ , but also because the mode  $\mathbf{m}_{N}$  of the posterior distribution itself depends on the choice of  $\alpha$ . We therefore adopt an iterative procedure in which we make an initial choice for  $\alpha$  and use this to find  $\mathbf{m}_N$ , which is given by (3.53), and also to evaluate  $\gamma$ , which is given by (3.91). These values are then used to re-estimate  $\alpha$  using (3.92), and the process repeated until convergence. Note that because the matrix **Φ**<sup>T</sup>**<sup>Φ</sup>** is fixed, we can compute its eigenvalues once at the start and then simply multiply these by  $\beta$  to obtain the  $\lambda_i$ .

It should be emphasized that the value of  $\alpha$  has been determined purely by looking at the training data. In contrast to maximum likelihood methods, no independent data set is required in order to optimize the model complexity.

We can similarly maximize the log marginal likelihood (3.86) with respect to  $\beta$ . To do this, we note that the eigenvalues  $\lambda_i$  defined by (3.87) are proportional to  $\beta$ , and hence  $d\lambda_i/d\beta = \lambda_i/\beta$  giving

$$
\frac{d}{d\beta}\ln|\mathbf{A}| = \frac{d}{d\beta}\sum_{i}\ln(\lambda_i + \alpha) = \frac{1}{\beta}\sum_{i}\frac{\lambda_i}{\lambda_i + \alpha} = \frac{\gamma}{\beta}.
$$
 (3.93)

The stationary point of the marginal likelihood therefore satisfies

$$
0 = \frac{N}{2\beta} - \frac{1}{2} \sum_{n=1}^{N} \left\{ t_n - \mathbf{m}_N^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}_n) \right\}^2 - \frac{\gamma}{2\beta}
$$
(3.94)

*Exercise* 3.22 and rearranging we obtain

$$
\frac{1}{\beta} = \frac{1}{N - \gamma} \sum_{n=1}^{N} \left\{ t_n - \mathbf{m}_N^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}_n) \right\}^2.
$$
 (3.95)

Again, this is an implicit solution for  $\beta$  and can be solved by choosing an initial value for  $\beta$  and then using this to calculate **m**<sub>N</sub> and  $\gamma$  and then re-estimate  $\beta$  using (3.95), repeating until convergence. If both  $\alpha$  and  $\beta$  are to be determined from the data, then their values can be re-estimated together after each update of  $\gamma$ .

*Exercise 3.20*

**Figure 3.15** Contours of the likelihood function (red) and the prior (green) in which the axes in parameter space have been rotated to align with the eigenvectors  $u_i$  of the Hessian. For  $\alpha = 0$ , the mode of the posterior is given by the maximum likelihood solution  $w_{ML}$ , whereas for nonzero  $\alpha$  the mode is at  $w_{\text{MAP}} = m_N$ . In the direction  $w_1$  the eigenvalue  $\lambda_1$ , defined by (3.87), is small compared with  $\alpha$  and so the quantity  $\lambda_1/(\lambda_1 + \alpha)$ is close to zero, and the corresponding MAP value of  $w_1$  is also close to zero. By contrast, in the direction  $w_2$ the eigenvalue  $\lambda_2$  is large compared with  $\alpha$  and so the quantity  $\lambda_2/(\lambda_2+\alpha)$  is close to unity, and the MAP value of  $w_2$  is close to its maximum likelihood value.

Image /page/4/Figure/2 description: The image displays a 2D plot with two axes labeled w1 and w2. A red ellipse is centered at a point labeled wML, with vectors u1 and u2 originating from wML and pointing along the major and minor axes of the ellipse, respectively. A blue dot labeled wMAP is located to the left of the ellipse and slightly above the w1 axis. A green circle is centered at the origin of the plot.

## **3.5.3 Effective number of parameters**

The result (3.92) has an elegant interpretation (MacKay, 1992a), which provides insight into the Bayesian solution for  $\alpha$ . To see this, consider the contours of the likelihood function and the prior as illustrated in Figure 3.15. Here we have implicitly transformed to a rotated set of axes in parameter space aligned with the eigenvectors  $\mathbf{u}_i$  defined in (3.87). Contours of the likelihood function are then axis-aligned ellipses. The eigenvalues  $\lambda_i$  measure the curvature of the likelihood function, and so in Figure 3.15 the eigenvalue  $\lambda_1$  is small compared with  $\lambda_2$  (because a smaller curvature corresponds to a greater elongation of the contours of the likelihood function). Because  $\beta \mathbf{\Phi}^T \mathbf{\Phi}$  is a positive definite matrix, it will have positive eigenvalues, and so the ratio  $\lambda_i/(\lambda_i + \alpha)$  will lie between 0 and 1. Consequently, the quantity  $\gamma$ defined by (3.91) will lie in the range  $0 \leq \gamma \leq M$ . For directions in which  $\lambda_i \gg \alpha$ , the corresponding parameter  $w_i$  will be close to its maximum likelihood value, and the ratio  $\lambda_i/(\lambda_i + \alpha)$  will be close to 1. Such parameters are called *well determined* because their values are tightly constrained by the data. Conversely, for directions in which  $\lambda_i \ll \alpha$ , the corresponding parameters  $w_i$  will be close to zero, as will the ratios  $\lambda_i/(\lambda_i + \alpha)$ . These are directions in which the likelihood function is relatively insensitive to the parameter value and so the parameter has been set to a small value by the prior. The quantity  $\gamma$  defined by (3.91) therefore measures the effective total number of well determined parameters.

We can obtain some insight into the result (3.95) for re-estimating  $\beta$  by comparing it with the corresponding maximum likelihood result given by (3.21). Both of these formulae express the variance (the inverse precision) as an average of the squared differences between the targets and the model predictions. However, they differ in that the number of data points  $N$  in the denominator of the maximum likelihood result is replaced by  $N - \gamma$  in the Bayesian result. We recall from (1.56) that the maximum likelihood estimate of the variance for a Gaussian distribution over a

single variable  $x$  is given by

$$
\sigma_{\rm ML}^2 = \frac{1}{N} \sum_{n=1}^{N} (x_n - \mu_{\rm ML})^2
$$
\n(3.96)

and that this estimate is biased because the maximum likelihood solution  $\mu_{\text{ML}}$  for the mean has fitted some of the noise on the data. In effect, this has used up one degree of freedom in the model. The corresponding unbiased estimate is given by (1.59) and takes the form

$$
\sigma_{\text{MAP}}^2 = \frac{1}{N-1} \sum_{n=1}^{N} (x_n - \mu_{\text{ML}})^2.
$$
 (3.97)

We shall see in Section 10.1.3 that this result can be obtained from a Bayesian treatment in which we marginalize over the unknown mean. The factor of  $N - 1$  in the denominator of the Bayesian result takes account of the fact that one degree of freedom has been used in fitting the mean and removes the bias of maximum likelihood. Now consider the corresponding results for the linear regression model. The mean of the target distribution is now given by the function  $\mathbf{w}^T \phi(\mathbf{x})$ , which contains M parameters. However, not all of these parameters are tuned to the data. The effective number of parameters that are determined by the data is  $\gamma$ , with the remaining  $M - \gamma$ parameters set to small values by the prior. This is reflected in the Bayesian result for the variance that has a factor  $N - \gamma$  in the denominator, thereby correcting for the bias of the maximum likelihood result.

We can illustrate the evidence framework for setting hyperparameters using the sinusoidal synthetic data set from Section 1.1, together with the Gaussian basis function model comprising 9 basis functions, so that the total number of parameters in the model is given by  $M = 10$  including the bias. Here, for simplicity of illustration, we have set  $\beta$  to its true value of 11.1 and then used the evidence framework to determine  $\alpha$ , as shown in Figure 3.16.

We can also see how the parameter  $\alpha$  controls the magnitude of the parameters  $\{w_i\}$ , by plotting the individual parameters versus the effective number  $\gamma$  of parameters, as shown in Figure 3.17.

If we consider the limit  $N \gg M$  in which the number of data points is large in relation to the number of parameters, then from (3.87) all of the parameters will be well determined by the data because **Φ**<sup>T</sup>**<sup>Φ</sup>** involves an implicit sum over data points, and so the eigenvalues  $\lambda_i$  increase with the size of the data set. In this case,  $\gamma = M$ , and the re-estimation equations for  $\alpha$  and  $\beta$  become

$$
\alpha = \frac{M}{2E_W(\mathbf{m}_N)}\tag{3.98}
$$

$$
\beta = \frac{N}{2E_D(\mathbf{m}_N)}\tag{3.99}
$$

where  $E_W$  and  $E_D$  are defined by (3.25) and (3.26), respectively. These results can be used as an easy-to-compute approximation to the full evidence re-estimation

Image /page/6/Figure/1 description: The image displays two plots side-by-side, both with the x-axis labeled "ln α" and ranging from -5 to 5. The left plot shows a red curve that starts high on the left and decreases as it moves to the right, and a blue curve that starts low on the left and increases sharply as it moves to the right. The two curves intersect around ln α = 0. The right plot shows a red curve that starts low on the left, rises to a peak around ln α = 1, and then decreases sharply to the right. It also shows a blue curve that starts moderately high on the left, decreases to a minimum around ln α = 2, and then increases sharply to the right. The two curves intersect around ln α = -2 and again around ln α = 3.5.

**Figure 3.16** The left plot shows  $\gamma$  (red curve) and  $2\alpha E_W(\mathbf{m}_N)$  (blue curve) versus  $\ln \alpha$  for the sinusoidal synthetic data set. It is the intersection of these two curves that defines the optimum value for  $\alpha$  given by the evidence procedure. The right plot shows the corresponding graph of log evidence  $\ln p(\mathbf{t}|\alpha, \beta)$  versus  $\ln \alpha$  (red curve) showing that the peak coincides with the crossing point of the curves in the left plot. Also shown is the test set error (blue curve) showing that the evidence maximum occurs close to the point of best generalization.

formulae, because they do not require evaluation of the eigenvalue spectrum of the Hessian.

**Figure 3.17** Plot of the 10 parameters  $w_i$ from the Gaussian basis function model versus the effective number of parameters  $\gamma,$  in which the  $\ket{w_i}$ hyperparameter  $\alpha$  is varied in the range  $0 \le \alpha \le \infty$  causing  $\gamma$  to vary in the range  $0 \leq \gamma \leq M$ . 0 1 2

Image /page/6/Figure/5 description: The image is a plot with the x-axis labeled 'γ' ranging from 0 to 10, and the y-axis labeled 'vᵢ' ranging from -2 to 2. There are ten lines plotted, each representing a different value from 0 to 9, indicated by colored numbers to the right of the plot. All lines originate from the point (0,0). The lines show varying trends as γ increases. For example, the line labeled '0' (red) increases sharply after γ=8, reaching above 2. The line labeled '9' (green) decreases steadily, reaching below -2 at γ=10. Other lines show more complex behavior, with some increasing, some decreasing, and some oscillating.

# **3.6. Limitations of Fixed Basis Functions**

Throughout this chapter, we have focussed on models comprising a linear combination of fixed, nonlinear basis functions. We have seen that the assumption of linearity in the parameters led to a range of useful properties including closed-form solutions to the least-squares problem, as well as a tractable Bayesian treatment. Furthermore, for a suitable choice of basis functions, we can model arbitrary nonlinearities in the mapping from input variables to targets. In the next chapter, we shall study an analogous class of models for classification.

It might appear, therefore, that such linear models constitute a general purpose framework for solving problems in pattern recognition. Unfortunately, there are some significant shortcomings with linear models, which will cause us to turn in later chapters to more complex models such as support vector machines and neural networks.

The difficulty stems from the assumption that the basis functions  $\phi_i(\mathbf{x})$  are fixed before the training data set is observed and is a manifestation of the curse of dimensionality discussed in Section 1.4. As a consequence, the number of basis functions needs to grow rapidly, often exponentially, with the dimensionality D of the input space.

Fortunately, there are two properties of real data sets that we can exploit to help alleviate this problem. First of all, the data vectors  $\{x_n\}$  typically lie close to a nonlinear manifold whose intrinsic dimensionality is smaller than that of the input space as a result of strong correlations between the input variables. We will see an example of this when we consider images of handwritten digits in Chapter 12. If we are using localized basis functions, we can arrange that they are scattered in input space only in regions containing data. This approach is used in radial basis function networks and also in support vector and relevance vector machines. Neural network models, which use adaptive basis functions having sigmoidal nonlinearities, can adapt the parameters so that the regions of input space over which the basis functions vary corresponds to the data manifold. The second property is that target variables may have significant dependence on only a small number of possible directions within the data manifold. Neural networks can exploit this property by choosing the directions in input space to which the basis functions respond.

# **Exercises**

**3.1**  $(\star)$  **www** Show that the 'tanh' function and the logistic sigmoid function (3.6) are related by

$$
\tanh(a) = 2\sigma(2a) - 1.
$$
 (3.100)

Hence show that a general linear combination of logistic sigmoid functions of the form  $\overline{M}$ 

$$
y(x, \mathbf{w}) = w_0 + \sum_{j=1}^{M} w_j \sigma\left(\frac{x - \mu_j}{s}\right)
$$
(3.101)

is equivalent to a linear combination of 'tanh' functions of the form

$$
y(x, \mathbf{u}) = u_0 + \sum_{j=1}^{M} u_j \tanh\left(\frac{x - \mu_j}{s}\right)
$$
 (3.102)

and find expressions to relate the new parameters  $\{u_1, \ldots, u_M\}$  to the original parameters  $\{w_1, \ldots, w_M\}.$ 

**3.2**  $(\star \star)$  Show that the matrix

$$
\mathbf{\Phi}(\mathbf{\Phi}^{\mathrm{T}}\mathbf{\Phi})^{-1}\mathbf{\Phi}^{\mathrm{T}} \tag{3.103}
$$

takes any vector **v** and projects it onto the space spanned by the columns of **Φ**. Use this result to show that the least-squares solution (3.15) corresponds to an orthogonal projection of the vector **t** onto the manifold  $S$  as shown in Figure 3.2.

**3.3** ( $\star$ ) Consider a data set in which each data point  $t_n$  is associated with a weighting factor  $r_n > 0$ , so that the sum-of-squares error function becomes

$$
E_D(\mathbf{w}) = \frac{1}{2} \sum_{n=1}^{N} r_n \left\{ t_n - \mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}_n) \right\}^2.
$$
 (3.104)

Find an expression for the solution  $w^*$  that minimizes this error function. Give two alternative interpretations of the weighted sum-of-squares error function in terms of (i) data dependent noise variance and (ii) replicated data points.

**3.4 ( ) www** Consider a linear model of the form

$$
y(x, \mathbf{w}) = w_0 + \sum_{i=1}^{D} w_i x_i
$$
 (3.105)

together with a sum-of-squares error function of the form

$$
E_D(\mathbf{w}) = \frac{1}{2} \sum_{n=1}^{N} \left\{ y(x_n, \mathbf{w}) - t_n \right\}^2.
$$
 (3.106)

Now suppose that Gaussian noise  $\epsilon_i$  with zero mean and variance  $\sigma^2$  is added independently to each of the input variables  $x_i$ . By making use of  $\mathbb{E}[\epsilon_i]=0$  and  $\mathbb{E}[\epsilon_i \epsilon_j] = \delta_{ij} \sigma^2$ , show that minimizing  $E_D$  averaged over the noise distribution is equivalent to minimizing the sum-of-squares error for noise-free input variables with the addition of a weight-decay regularization term, in which the bias parameter  $w_0$ is omitted from the regularizer.

- **3.5** ( $\star$ ) **www** Using the technique of Lagrange multipliers, discussed in Appendix E, show that minimization of the regularized error function (3.29) is equivalent to minimizing the unregularized sum-of-squares error (3.12) subject to the constraint (3.30). Discuss the relationship between the parameters  $\eta$  and  $\lambda$ .
- **3.6** ( $\star$ ) **www** Consider a linear basis function regression model for a multivariate target variable **t** having a Gaussian distribution of the form

$$
p(\mathbf{t}|\mathbf{W}, \Sigma) = \mathcal{N}(\mathbf{t}|\mathbf{y}(\mathbf{x}, \mathbf{W}), \Sigma)
$$
 (3.107)

where

$$
\mathbf{y}(\mathbf{x}, \mathbf{W}) = \mathbf{W}^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}) \tag{3.108}
$$

together with a training data set comprising input basis vectors  $\phi(\mathbf{x}_n)$  and corresponding target vectors  $\mathbf{t}_n$ , with  $n = 1, \ldots, N$ . Show that the maximum likelihood solution  $W_{ML}$  for the parameter matrix  $W$  has the property that each column is given by an expression of the form (3.15), which was the solution for an isotropic noise distribution. Note that this is independent of the covariance matrix  $\Sigma$ . Show that the maximum likelihood solution for  $\Sigma$  is given by

$$
\Sigma = \frac{1}{N} \sum_{n=1}^{N} \left( \mathbf{t}_n - \mathbf{W}_{\mathrm{ML}}^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}_n) \right) \left( \mathbf{t}_n - \mathbf{W}_{\mathrm{ML}}^{\mathrm{T}} \boldsymbol{\phi}(\mathbf{x}_n) \right)^{\mathrm{T}}.
$$
 (3.109)

- **3.7** ( $\star$ ) By using the technique of completing the square, verify the result (3.49) for the posterior distribution of the parameters **w** in the linear basis function model in which  $\mathbf{m}_N$  and  $\mathbf{S}_N$  are defined by (3.50) and (3.51) respectively.
- **3.8**  $(\star \star)$  **www** Consider the linear basis function model in Section 3.1, and suppose that we have already observed  $N$  data points, so that the posterior distribution over w is given by  $(3.49)$ . This posterior can be regarded as the prior for the next observation. By considering an additional data point  $(\mathbf{x}_{N+1}, t_{N+1})$ , and by completing the square in the exponential, show that the resulting posterior distribution is again given by (3.49) but with  $\mathbf{S}_N$  replaced by  $\mathbf{S}_{N+1}$  and  $\mathbf{m}_N$  replaced by  $\mathbf{m}_{N+1}$ .
- **3.9**  $(\star \star)$  Repeat the previous exercise but instead of completing the square by hand, make use of the general result for linear-Gaussian models given by  $(2.116)$ .
- **3.10**  $(\star \star)$  **www** By making use of the result (2.115) to evaluate the integral in (3.57), verify that the predictive distribution for the Bayesian linear regression model is given by (3.58) in which the input-dependent variance is given by (3.59).
- **3.11**  $(\star \star)$  We have seen that, as the size of a data set increases, the uncertainty associated with the posterior distribution over model parameters decreases. Make use of the matrix identity (Appendix C)

$$
\left(\mathbf{M} + \mathbf{v}\mathbf{v}^{\mathrm{T}}\right)^{-1} = \mathbf{M}^{-1} - \frac{\left(\mathbf{M}^{-1}\mathbf{v}\right)\left(\mathbf{v}^{\mathrm{T}}\mathbf{M}^{-1}\right)}{1 + \mathbf{v}^{\mathrm{T}}\mathbf{M}^{-1}\mathbf{v}}
$$
(3.110)

to show that the uncertainty  $\sigma_N^2(\mathbf{x})$  associated with the linear regression function given by (3.59) satisfies given by (3.59) satisfies

$$
\sigma_{N+1}^2(\mathbf{x}) \leq \sigma_N^2(\mathbf{x}).\tag{3.111}
$$

**3.12**  $(\star \star)$  We saw in Section 2.3.6 that the conjugate prior for a Gaussian distribution with unknown mean and unknown precision (inverse variance) is a normal-gamma distribution. This property also holds for the case of the conditional Gaussian distribution  $p(t|\mathbf{x}, \mathbf{w}, \beta)$  of the linear regression model. If we consider the likelihood function (3.10), then the conjugate prior for **w** and  $\beta$  is given by

$$
p(\mathbf{w}, \beta) = \mathcal{N}(\mathbf{w}|\mathbf{m}_0, \beta^{-1}\mathbf{S}_0) \text{Gam}(\beta|a_0, b_0).
$$
 (3.112)

Show that the corresponding posterior distribution takes the same functional form, so that

$$
p(\mathbf{w}, \beta | \mathbf{t}) = \mathcal{N}(\mathbf{w} | \mathbf{m}_N, \beta^{-1} \mathbf{S}_N) \text{Gam}(\beta | a_N, b_N)
$$
(3.113)

and find expressions for the posterior parameters  $\mathbf{m}_N$ ,  $\mathbf{S}_N$ ,  $a_N$ , and  $b_N$ .

**3.13**  $(\star \star)$  Show that the predictive distribution  $p(t|\mathbf{x}, \mathbf{t})$  for the model discussed in Exercise 3.12 is given by a Student's t-distribution of the form

$$
p(t|\mathbf{x}, \mathbf{t}) = \text{St}(t|\mu, \lambda, \nu)
$$
\n(3.114)

and obtain expressions for  $\mu$ ,  $\lambda$  and  $\nu$ .

**3.14**  $(\star \star)$  In this exercise, we explore in more detail the properties of the equivalent kernel defined by (3.62), where  $S_N$  is defined by (3.54). Suppose that the basis functions  $\phi_i(\mathbf{x})$  are linearly independent and that the number N of data points is greater than the number  $M$  of basis functions. Furthermore, let one of the basis functions be constant, say  $\phi_0(\mathbf{x})=1$ . By taking suitable linear combinations of these basis functions, we can construct a new basis set  $\psi_i(\mathbf{x})$  spanning the same space but that are orthonormal, so that

$$
\sum_{n=1}^{N} \psi_j(\mathbf{x}_n) \psi_k(\mathbf{x}_n) = I_{jk}
$$
\n(3.115)

where  $I_{jk}$  is defined to be 1 if  $j = k$  and 0 otherwise, and we take  $\psi_0(\mathbf{x}) = 1$ . Show that for  $\alpha = 0$ , the equivalent kernel can be written as  $k(\mathbf{x}, \mathbf{x}') = \psi(\mathbf{x})^T \psi(\mathbf{x}')$ <br>where  $\psi = (\psi_1, \psi_2)^T$ . Use this result to show that the kernel satisfies the where  $\psi = (\psi_1, \dots, \psi_M)^T$ . Use this result to show that the kernel satisfies the summation constraint

$$
\sum_{n=1}^{N} k(\mathbf{x}, \mathbf{x}_n) = 1.
$$
 (3.116)

- **3.15** ( $\star$ ) **www** Consider a linear basis function model for regression in which the parameters  $\alpha$  and  $\beta$  are set using the evidence framework. Show that the function  $E(\mathbf{m}_N)$  defined by (3.82) satisfies the relation  $2E(\mathbf{m}_N) = N$ .
- **3.16** ( $\star\star$ ) Derive the result (3.86) for the log evidence function  $p(\mathbf{t}|\alpha, \beta)$  of the linear regression model by making use of (2.115) to evaluate the integral (3.77) directly.
- **3.17**  $(\star)$  Show that the evidence function for the Bayesian linear regression model can be written in the form (3.78) in which  $E(w)$  is defined by (3.79).
- **3.18**  $(\star \star)$  **www** By completing the square over **w**, show that the error function (3.79) in Bayesian linear regression can be written in the form (3.80).
- **3.19**  $(\star \star)$  Show that the integration over **w** in the Bayesian linear regression model gives the result  $(3.85)$ . Hence show that the log marginal likelihood is given by  $(3.86)$ .

- **3.20**  $(\star \star)$  **www** Starting from (3.86) verify all of the steps needed to show that maximization of the log marginal likelihood function (3.86) with respect to  $\alpha$  leads to the re-estimation equation (3.92).
- **3.21** ( $\star \star$ ) An alternative way to derive the result (3.92) for the optimal value of  $\alpha$  in the evidence framework is to make use of the identity

$$
\frac{d}{d\alpha}\ln|\mathbf{A}| = \text{Tr}\left(\mathbf{A}^{-1}\frac{d}{d\alpha}\mathbf{A}\right).
$$
 (3.117)

Prove this identity by considering the eigenvalue expansion of a real, symmetric matrix **A**, and making use of the standard results for the determinant and trace of **A** expressed in terms of its eigenvalues (Appendix C). Then make use of (3.117) to derive (3.92) starting from (3.86).

- **3.22**  $(\star \star)$  Starting from (3.86) verify all of the steps needed to show that maximization of the log marginal likelihood function (3.86) with respect to  $\beta$  leads to the re-estimation equation (3.95).
- **3.23**  $(\star \star)$  **www** Show that the marginal probability of the data, in other words the model evidence, for the model described in Exercise 3.12 is given by

$$
p(\mathbf{t}) = \frac{1}{(2\pi)^{N/2}} \frac{b_0^{a_0}}{b_N^{a_N}} \frac{\Gamma(a_N)}{\Gamma(a_0)} \frac{|\mathbf{S}_N|^{1/2}}{|\mathbf{S}_0|^{1/2}}
$$
(3.118)

by first marginalizing with respect to **w** and then with respect to  $\beta$ .

**3.24**  $(\star \star)$  Repeat the previous exercise but now use Bayes' theorem in the form

$$
p(\mathbf{t}) = \frac{p(\mathbf{t}|\mathbf{w}, \beta)p(\mathbf{w}, \beta)}{p(\mathbf{w}, \beta|\mathbf{t})}
$$
(3.119)

and then substitute for the prior and posterior distributions and the likelihood function in order to derive the result (3.118).

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
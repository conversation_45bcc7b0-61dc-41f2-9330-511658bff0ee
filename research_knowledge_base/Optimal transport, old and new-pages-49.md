The remaining part of the present chapter is devoted to a proof of stability for the weak  $CD(K, N)$  property.

## Continuity properties of the functionals $U_{\nu}$ and $U^{\beta}_{\pi,\nu}$

In this section, I shall explain some of the remarkable properties of the integral functionals appearing in Definition 29.1. For the moment it will be sufficient to restrict to the case of a *compact* space  $\mathcal{X}$ , and it will be convenient to consider that  $U_{\nu}$  and  $U_{\pi,\nu}^{\beta}$  are defined on the set of all (nonnegative) finite signed Borel measures, not necessarily probability measures. (Actually, in Definition 29.1 it was not assumed that  $\mu$  is a probability measure.) One may even think of these functionals as defined on the whole vector space  $M(\mathcal{X})$  of finite Borel measures on  $\mathcal{X},$ with the convention that their value is  $+\infty$  if  $\mu$  is not nonnegative; then  $U_{\nu}$  and  $U_{\pi,\nu}^{\beta}$  are true convex functional on  $M(\mathcal{X})$ .

It will be convenient to study the functionals  $U_{\nu}$  by means of their Legendre representation. Generally speaking, the Legendre representation of a convex functional  $\Phi$  defined on a vector space E is an identity of the form

$$
\varPhi(x)=\sup\Big\{\langle A,x\rangle-\varPsi(A)\Big\},
$$

where  $\Lambda$  varies over a certain subset of  $E^*$ , and  $\Psi$  is a convex functional of A. Usually, A varies over the whole set  $E^*$ , and  $\Psi(\Lambda)$  =  $\sup_{x\in E}[\langle \Lambda, x\rangle-\Phi(x)]$  is the Legendre transform of  $\Phi$ ; but here we don't really want to do so, because nobody knows what the huge space  $M(\mathcal{X})^*$ looks like. So it is better to restrict to subspaces of  $M(\mathcal{X})^*$ . There are several natural possible choices, resulting in various Legendre representations; which one is most convenient depends on the context. Here are the ones that will be useful in the sequel.

Definition 29.18 (Legendre transform of a real-valued convex **function).** Let  $U : \mathbb{R}_+ \to \mathbb{R}$  be a continuous convex function with  $U(0) = 0$ ; its Legendre transform is defined on R by

$$
U^*(p) = \sup_{r \in \mathbb{R}_+} \left[ pr - U(r) \right].
$$

It is easy to check that  $U^*$  is a convex function, taking the value  $-U(0) = 0$  on  $(-\infty, U'(0)]$  and  $+\infty$  on  $(U'(\infty), +\infty)$ .

Proposition 29.19 (Legendre representation of  $U_{\nu}$ ). Let  $U$ :  $\mathbb{R}_+ \to \mathbb{R}$  be a continuous convex function with  $U(0) = 0$ , let X be a compact metric space, equipped with a finite reference measure  $\nu$ . Then, whenever  $\mu$  is a finite measure on  $\mathcal{X},$ 

(i) 
$$
U_{\nu}(\mu) = \sup \left\{ \int_{\mathcal{X}} \varphi \, d\mu - \int_{\mathcal{X}} U^*(\varphi) \, d\nu; \ \varphi \in L^{\infty}(\mathcal{X}); \ \varphi \leq U'(\infty) \right\}
$$

(ii)  $U_{\nu}(\mu) = \sup \left\{ \int_{\mathcal{X}} \varphi \, d\mu - \int_{\mathcal{X}} U^*(\varphi) \, d\nu; \quad \varphi \in C(\mathcal{X}), \right\}$ 

  
 $U'\left(\frac{1}{M}\right) \leq \varphi \leq U'(M); \quad M \in \mathbb{N} \right\}.$ 

The deceiving simplicity of these formulas hides some subtleties: For instance, it is in general impossible to drop the restriction  $\varphi \leq U'(\infty)$ in (i), so the supremum is not taken over the whole vector space  $L^{\infty}(\mathcal{X})$ but only on a subspace thereof. Proposition 29.19 can be proven by elementary tools of measure theory; see the bibliographical notes for references and comments.

The next statement gathers the three important properties on which the main results of this chapter rest: (i)  $U_{\nu}(\mu)$  is lower semicontinuous in  $(\mu, \nu)$ ; (ii)  $U_{\nu}(\mu)$  is never increased by push-forward; (iii)  $\mu$  can be regularized in such a way that  $U^{\beta}_{\pi,\nu}(\mu)$  is upper semicontinuous in  $(\pi,\mu)$ along the approximation. In the next statement,  $M_+(\mathcal{X})$  will stand for the set of finite (nonnegative) Borel measures on  $\mathcal{X}$ , and  $L_+^1(\nu)$  for the set of nonnegative  $\nu$ -integrable measurable functions on  $\mathcal{X}$ .

Theorem 29.20 (Continuity and contraction properties of  $U_{\nu}$ and  $U_{\pi,\nu}^{\beta}$ . Let  $(\mathcal{X},d)$  be a compact metric space, equipped with a finite measure  $\nu$ . Let  $U : \mathbb{R}_+ \to \mathbb{R}_+$  be a convex continuous function, with  $U(0) = 0$ . Further, let  $\beta(x, y)$  be a continuous positive function on  $\mathcal{X} \times \mathcal{X}$ . Then, with the notation of Definition 29.1:

(i)  $U_{\nu}(\mu)$  is a weakly lower semicontinuous function of both  $\mu$  and  $\nu$ in  $M_+(\mathcal{X})$ . More explicitly, if  $\mu_k \to \mu$  and  $\nu_k \to \nu$  in the weak topology of convergence against bounded continuous functions, then

$$
U_{\nu}(\mu) \leq \liminf_{k \to \infty} U_{\nu_k}(\mu_k).
$$

(ii)  $U_{\nu}$  satisfies a contraction principle in both  $\mu$  and  $\nu$ ; that is, if  $\mathcal{Y}$ is another compact space, and  $f: \mathcal{X} \to \mathcal{Y}$  is any measurable function, then

$$
U_{f\#\nu}(f\#\mu)\leq U_{\nu}(\mu).
$$

(iii) If  $U$  "grows at most polynomially", in the sense that

$$
\forall r > 0, \qquad r \, U'(r) \le C \, (U(r)_{+} + r), \tag{29.16}
$$

then for any probability measure  $\mu \in P(\mathcal{X})$ , with  $\text{Spt } \mu \subset \text{Spt } \nu$ , there is a sequence  $(\mu_k)_{k\in\mathbb{N}}$  of probability measures converging weakly to  $\mu$ , such that each  $\mu_k$  has a continuous density, and for any sequence  $(\pi_k)_{k\in\mathbb{N}}$ converging weakly to  $\pi$  in  $P(X \times X)$ , such that  $\pi_k$  admits  $\mu_k$  as first marginal and  $\text{Spt } \pi_k \subset (\text{Spt } \nu) \times (\text{Spt } \nu),$ 

$$
\limsup_{k \to \infty} U_{\pi_k, \nu}^{\beta}(\mu_k) \le U_{\pi, \nu}^{\beta}(\mu). \tag{29.17}
$$

Remark 29.21. The assumption of polynomial growth in (iii) is obviously true if U is Lipschitz; or if  $U(r)$  behaves at infinity like  $ar \log r + br$  (or like a polynomial).

**Exercise 29.22.** Use Property (ii) in the case  $U(r) = r \log r$  to recover the Csiszár–Kullback–Pinsker inequality  $(22.25)$ . Hint: Take f to be valued in  $\{0, 1\}$ , apply Csiszár's two-point inequality

$$
\forall x, y \in [0, 1],
$$
  $x \log \frac{x}{y} + (1 - x) \log \left( \frac{1 - x}{1 - y} \right) \ge 2 (x - y)^2$ 

and optimize the choice of f.

*Proof of Theorem 29.20.* To prove (i), note that  $U^*$  is continuous on  $[U'(1/M), U'(M));$  so if  $\varphi$  is continuous with values in  $[U'(1/M), U'(M)],$ then  $U^*(\varphi)$  is also continuous. Then Proposition 29.19(ii) can be rewritten as

$$
U_{\nu}(\mu) = \sup_{(\varphi,\psi)\in\mathcal{U}}\left\{\int\varphi\,d\mu + \int\psi\,d\nu\right\},\,
$$

where U is a certain subset of  $C(\mathcal{X}) \times C(\mathcal{X})$ . In particular,  $U_{\nu}(\mu)$  is a supremum of continuous functions of  $(\mu, \nu)$ ; it follows that  $U_{\nu}$  is lower semicontinuous.

To prove (ii), pick up any  $\varphi \in L^{\infty}(\mathcal{X})$  with  $\varphi \leq U'(\infty)$ . Then

Continuity properties of the functionals  $U_{\nu}$  and  $U_{\pi}^{\beta}$ 827

$$
\int_{\mathcal{X}} (\varphi \circ f) d\mu - \int_{\mathcal{X}} U^*(\varphi \circ f) d\nu = \int_{\mathcal{Y}} \varphi d(f_{\#}\mu) - \int_{\mathcal{Y}} U^*(\varphi) d(f_{\#}\nu).
$$

If  $\varphi \leq U'(\infty)$ , then also  $\varphi \circ f \leq U'(\infty)$ ; similarly, if  $\varphi$  is bounded, then also  $\varphi \circ f$  is bounded. So

$$
\sup_{\psi \in L^{\infty}; \ \psi \leq U'(\infty)} \left\{ \int_{\mathcal{X}} \psi \, d\mu - \int_{\mathcal{X}} U^*(\psi) \, d\nu \right\} \leq \sup_{\varphi \in L^{\infty}; \ \varphi \leq U'(\infty)} \left\{ \int_{\mathcal{Y}} \varphi \, d(f_{\#}\mu) - \int_{\mathcal{Y}} U^*(\varphi) \, d(f_{\#}\nu) \right\}.
$$

By Proposition 29.19(i), the left-hand side coincides with  $U_{\nu}(\mu)$ , and the right-hand side with  $U_{f\mu\mu}(f\mu\nu)$ . This concludes the proof of the contraction property (ii).

Now let us consider the proof of (iii), which is a bit tricky. For pedagogical reasons I shall first treat a simpler case.

**Particular case:**  $\beta \equiv 1$ . (Then there is no need for any restriction on the growth of  $U$ .)

Let  $\varepsilon = \varepsilon_k$  be a sequence in  $(0, 1)$ ,  $\varepsilon_k \to 0$ , and let  $K_{\varepsilon}(x, y)$  be a sequence of symmetric continuous nonnegative functions on  $\mathcal{X} \times \mathcal{X}$ satisfying

$$
\begin{cases} \forall x \in \text{Spt } \nu, & \int K_{\varepsilon}(x, y) \, \nu(dy) = 1; \\ \forall x, y \in \mathcal{X}, & d(x, y) \ge \varepsilon \Longrightarrow K_{\varepsilon}(x, y) = 0. \end{cases}
$$

Such kernels induce a regularization of probability measures, as recalled in the First Appendix. On  $S = \text{Spt } \nu$ , define

$$
\rho_{\varepsilon}(x) = \int_{\operatorname{Spt}\nu} K_{\varepsilon}(x, y) \, \mu(dy);
$$

this is a (uniformly) continuous function on a compact set. By the Tietze–Urysohn extension theorem,  $\rho_{\varepsilon}$  can be extended into a continuous function  $\tilde{\rho}_{\varepsilon}$  on the whole of X. Of course,  $\rho_{\varepsilon}$  and  $\tilde{\rho}_{\varepsilon}$  coincide *ν*-almost everywhere. We shall see that  $\mu_{\varepsilon} = \rho_{\varepsilon} \nu$  (or, more explicitly,  $\mu_k = \rho_{\varepsilon_k} \nu$  does the job for statement (iii).

Let us first assume that  $\mu$  is absolutely continuous, and let  $\rho$  be its density. Since  $\text{Spt}\,\mu$  and  $\text{Spt}\,\mu_{\varepsilon}$  are included in  $S = \text{Spt}\,\nu$ ,

$$
U_{\nu}(\mu) = \int_{\mathcal{X}} U(\rho) d\nu = \int_{S} U(\rho) d\nu; \qquad U_{\nu}(\mu_{\varepsilon}) = \int_{S} U(\rho_{\varepsilon}) d\nu.
$$

So up to changing X for S, we might just assume that  $Spt(\nu) = X$ . Then for each  $x \in \mathcal{X}, K_{\varepsilon}(x, y) \nu(dy)$  is a probability measure on  $\mathcal{X},$ and by Jensen's inequality,

$$
U(\rho_{\varepsilon}(x)) = U\left(\int_{\mathcal{X}} K_{\varepsilon}(x, y) \,\rho(y)\,\nu(dy)\right) \leq \int_{\mathcal{X}} K_{\varepsilon}(x, y) \, U(\rho(y)) \,\nu(dy). \tag{29.18}
$$

Now integrate both sides of the latter inequality against  $\nu(dx)$ ; this is allowed because  $U(r) \geq -C(r+1)$  for some finite constant C, so the left-hand side of (29.18) is bounded below by the integrable function  $-C(\rho_{\varepsilon}+1)$ . After integration, one has

$$
\int_{\mathcal{X}} U(\rho_{\varepsilon}) d\nu \leq \int_{\mathcal{X}\times\mathcal{X}} K_{\varepsilon}(x, y) U(\rho(y)) \nu(dy) \nu(dx).
$$

But  $K_{\varepsilon}(x,y) \nu(dx)$  is a probability measure for any  $y \in \mathcal{X}$ , so

$$
\int_{\mathcal{X}\times\mathcal{X}} K_{\varepsilon}(x,y) U(\rho(y)) \nu(dy) \nu(dx) = \int_{\mathcal{X}} U(\rho(y)) \nu(dy) = \int_{\mathcal{X}} U(\rho) d\nu.
$$

To summarize:  $U_{\nu}(\mu_{\varepsilon}) \leq U_{\nu}(\mu)$  for all  $\varepsilon > 0$ , and then the conclusion follows.

If  $\mu$  is not absolutely continuous, define

$$
\rho_{a,\varepsilon}(x) = \int_{\text{Spt}\,\nu} K_{\varepsilon}(x,y) \,\rho(y) \,\nu(dy); \qquad \rho_{s,\varepsilon}(x) = \int_{\text{Spt}\,\nu} K_{\varepsilon}(x,y) \,\mu_s(dy),
$$

where  $\mu = \rho \nu + \mu_s$  is the Lebesgue decomposition of  $\mu$  with respect to  $\nu$ . Then  $\rho_{\varepsilon} = \rho_{a,\varepsilon} + \rho_{s,\varepsilon}$ . By convexity of U, for any  $\theta \in (0,1)$ ,

$$
\int U(\rho_{\varepsilon}) d\nu \le (1 - \theta) \int U\left(\frac{\rho_{a,\varepsilon}}{1 - \theta}\right) d\nu + \theta \int U\left(\frac{\rho_{s,\varepsilon}}{\theta}\right) d\nu
$$
  
$$
\le (1 - \theta) \int U\left(\frac{\rho_{a,\varepsilon}}{1 - \theta}\right) d\nu + U'(\infty) \int \rho_{s,\varepsilon} d\nu
$$
  
$$
= (1 - \theta) \int U\left(\frac{\rho_{a,\varepsilon}}{1 - \theta}\right) d\nu + U'(\infty) \mu_{s}[\mathcal{X}].
$$

It is easy to pass to the limit as  $\theta \to 0$  (use the monotone convergence theorem for the positive part of  $U$ , and the dominated convergence theorem for the negative part). Thus

Continuity properties of the functionals  $U_{\nu}$  and  $U_{\pi,\nu}^{\beta}$  829

$$
\int U(\rho_{\varepsilon})\,d\nu\leq \int U(\rho_{a,\varepsilon})\,d\nu+U'(\infty)\,\mu_s[\mathcal{X}].
$$

The first part of the proof shows that  $\int U(\rho_{a,\varepsilon}) d\nu \leq \int U(\rho) d\nu$ , so

$$
\int U(\rho_{\varepsilon})\,d\nu\leq \int U(\rho)\,d\nu+U'(\infty)\,\mu_s[\mathcal{X}]\ =U_{\nu}(\mu),
$$

and this again implies the conclusion.

 $\mathcal{L}$ 

General case with  $\beta$  variable: This is much, much more tricky and I urge the reader to skip this case at first encounter.

Before starting the proof, here are a few remarks. The assumption of polynomial growth implies the following estimate: For each  $B > 0$ there is a constant  $C$  such that

$$
\sup_{B^{-1} \le a \le B} \left( \frac{U_+(ar)}{a} \right) \le C \left( U_+(r) + r \right). \tag{29.19}
$$

Let us check (29.19) briefly. If  $U \leq 0$ , there is nothing to prove. If  $U \geq 0$ , the polynomial growth assumption amounts to  $r U'(r) \leq C(U(r) + r)$ , so  $r(U'(r)+1) \le 2C(U(r)+r)$ ; then  $(d/dt) \log[U(tr)+t] \le 2C/t$ , so

$$
U(ar) + ar \le (U(r) + r) a^{2C}, \tag{29.20}
$$

whence the conclusion. Finally, if  $U$  does not have a constant sign, this means that there is  $r_0 > 0$  such that  $U(r) \leq 0$  for  $r \leq r_0$ , and  $U(r) > 0$ for  $r > r_0$ . Then:

- if  $r < B^{-1}r_0$ , then  $U_+(ar) = 0$  for all  $a \leq B$  and (29.19) is obviously true;
- if  $r > B r_0$ , then  $U(ar) > 0$  for all  $a \in [B^{-1}, B]$  and one establishes (29.20) as before;
- if  $B^{-1}r_0 \leq r \leq Br_0$ , then  $U_+(ar)$  is bounded above for  $a \leq B$ , while r is bounded below, so obviously  $(29.19)$  is satisfied for some well-chosen constant C.

Next, we may dismiss the case when the right-hand side of the inequality in (29.17) is  $+\infty$  as trivial; so we might assume that

$$
\int \beta(x, y) U_{+} \left( \frac{\rho(x)}{\beta(x, y)} \right) \pi(dy|x) \nu(dx) < +\infty; \qquad U'(\infty) \mu_{s}[\mathcal{X}] < +\infty.
$$
\n(29.21)

Since  $B^{-1} \leq \beta(x, y) \leq B$  for some  $B > 0$ , (29.19) implies the existence of a constant C such that

$$
C^{-1} \int U_{+}(\rho(x)) \nu(dx) = C^{-1} \int U_{+}(\rho(x)) \pi(dy|x) \nu(dx)
$$
  

$$
\leq \int \beta(x, y) U_{+} \left(\frac{\rho(x)}{\beta(x, y)}\right) \pi(dy|x) \nu(dx)
$$
  

$$
\leq C \int U_{+}(\rho(x)) \pi(dy|x) \nu(dx) = C \int U_{+}(\rho(x)) \nu(dx).
$$

So (29.21) implies the integrability of  $U_{+}(\rho)$ .

After these preliminaries, we can go on with the proof. In the sequel, the symbol  $C$  will stand for constants that may vary from place to place but only depend on the nonlinearity  $U$  and the distortion coefficients  $\beta$ . I shall write  $\mu_{\varepsilon}$  for  $\mu_k = \mu_{\varepsilon_k}$ , to emphasize the role of  $\varepsilon$  as a regularization parameter. For consistency, I shall also write  $\pi_{\varepsilon}$  instead of  $\pi_k$ .

Step 1: Reduction to the case  $\text{Spt } \nu = \mathcal{X}$ . This step is essentially trivial. Let  $S = \text{Spt } \nu$ . By assumption  $\pi_{\varepsilon}[(\mathcal{X} \times \mathcal{X}) \setminus (S \times S)] = 0$ , so  $\pi_{\varepsilon}[y \notin S|x] = 0$ ,  $\rho_{\varepsilon} \nu(dx)$ -almost surely; equivalently,  $\pi_{\varepsilon}[y \notin S|x] = 0$ ,  $\nu(dx)$ -almost surely on  $\{\rho_{\varepsilon} > 0\}$ . Since  $U(0) = 0$ , values of x such that  $\rho_{\varepsilon}(x) = 0$  do not affect the integral in the left-hand side of (29.17), so we might restrict this integral to  $y \in S$ . Then the  $\nu(dx)$  integration allows us to further restrict the integral to  $x \in S$ .

Since each  $\pi_{\varepsilon}$  is concentrated on the closed set  $S \times S$ , the same is true for the weak limit  $\pi$ ; then the same reasoning as above applies for the right-hand side of (29.17), and that integral can also be restricted to  $S \times S$ .

It only remains to check that the assumption of weak convergence  $\pi_{\varepsilon} \to \pi$  is preserved under restriction to  $S \times S$ . Let  $\varphi$  be a continuous function on  $S \times S$ . Since  $S \times S$  is compact,  $\varphi$  is uniformly continuous, so by Tietze–Urysohn's theorem it can be extended into a continuous function on the whole of  $\mathcal{X} \times \mathcal{X}$ , still denoted  $\varphi$ . Then

$$
\int_{S\times S} \varphi \, d\pi_{\varepsilon} = \int_{\mathcal{X}\times \mathcal{X}} \varphi \, d\pi_{\varepsilon} \xrightarrow[\varepsilon \to 0]{} \int_{\mathcal{X}\times \mathcal{X}} \varphi \, d\pi = \int_{S\times S} \varphi \, d\pi,
$$

so  $\pi$  is indeed the weak limit of  $\pi_{\varepsilon}$ , when viewed as a probability measure on  $S \times S$ .

In the sequel all the discussion will be restricted to  $S$ , so I shall assume  $\text{Spt } \nu = \mathcal{X}$ .

Step 2: Reduction to the case  $U'(0) > -\infty$ . The problem now is to get rid of possibly very large negative values of  $U'$  close to 0. For any  $\delta > 0$ , define  $U'_{\delta}(r) := \max (U'(\delta), U'(r))$  and

$$
U_{\delta}(r) = \int_0^r U'_{\delta}(s) \, ds.
$$

Since U' is nondecreasing,  $U'_{\delta}$  converges monotonically to  $U' \in L^1(0,r)$ as  $\delta \to 0$ . It follows that  $U_{\delta}(r)$  decreases to  $U(r)$ , for all  $r > 0$ . Let us check that all the assumptions which we imposed on  $U$ , still hold true for  $U_{\delta}$ . First,  $U_{\delta}(0) = 0$ . Also, since  $U'_{\delta}$  is nondecreasing,  $U_{\delta}$  is convex. Finally,  $U_{\delta}$  has polynomial growth; indeed:

- if  $r \leq \delta$ , then  $r(U_{\delta})'(r) = r U'(\delta)$  is bounded above by a constant multiple of  $r$ ;
- if  $r > \delta$ , then  $r(U_{\delta})'(r) = r U'(r)$ , which is bounded (by assumption) by  $C(U(r)_+ + r)$ , and this obviously is bounded by  $C(U_\delta(r)_+ + r)$ , for  $U \leq U_{\delta}$ .

The next claim is that the integral

$$
\int \beta(x, y) U_{\delta}\left(\frac{\rho(x)}{\beta(x, y)}\right) \pi(dy|x) \nu(dx)
$$

makes sense and is not  $+\infty$ . Indeed, as we have just seen, there is a constant C such that  $(U_{\delta})_+ \leq C(U_+(r) + r)$ . Then the contribution of the linear part  $Cr$  is finite, since

$$
\int \beta(x,y) \left( \frac{\rho(x)}{\beta(x,y)} \right) \pi(dy|x) \nu(dx) = \int \rho(x) \nu(dx) \le 1;
$$

and the contribution of  $CU_+(r)$  is also finite in view of (29.21). So

$$
\int \beta(x,y) (U_{\delta})_+ \left( \frac{\rho(x)}{\beta(x,y)} \right) \pi(dy|x) \nu(dx) < +\infty,
$$

which proves the claim.

Now assume that Theorem 29.20(iii) has been proved with  $U_{\delta}$  in place of U. Then, for any  $\delta > 0$ ,

$$
\limsup_{\varepsilon \downarrow 0} \int \beta(x, y) U\left(\frac{\rho_{\varepsilon}(x)}{\beta(x, y)}\right) \pi_{\varepsilon}(dy|x) \nu(dx)
$$

$$
\leq \limsup_{\varepsilon \downarrow 0} \int \beta(x, y) U_{\delta}\left(\frac{\rho_{\varepsilon}(x)}{\beta(x, y)}\right) \pi_{\varepsilon}(dy|x) \nu(dx)
$$

$$
\leq \int \beta(x, y) U_{\delta}\left(\frac{\rho(x)}{\beta(x, y)}\right) \pi(dy|x) \nu(dx).
$$

But by monotone convergence,

$$
\int \beta(x, y) U_{\delta}\left(\frac{\rho(x)}{\beta(x, y)}\right) \pi(dy|x) \nu(dx) \\ = \int \beta(x, y) U\left(\frac{\rho(x)}{\beta(x, y)}\right) \pi(dy|x) \nu(dx),
$$

and inequality (29.17) follows.

To summarize: It is sufficient to establish  $(29.17)$  with U replaced by  $U_{\delta}$ , and thus we may assume that U is bounded below by a linear function  $r \rightarrow -Kr$ .

Step 3: Reduction to the case when  $U \geq 0$ . As we have already seen, adding a linear function  $Kr$  to  $U$  does not alter the assumptions on  $U$ ; it does not change the conclusion either, because this only adds the constant  $K$  to both sides of the inequality  $(29.17)$ . For the righthand side, this is a consequence of

$$
\int \beta(x, y) K \frac{\rho_{\varepsilon}(x, y)}{\beta(x, y)} \pi_{\varepsilon}(dy|x) \nu(dx) = K \int \pi_{\varepsilon}(dy|x) (\rho_{\varepsilon} \nu)(dx)
$$
$$
= K \int \pi_{\varepsilon}(dx dy) = K;
$$

and for the right-hand side the computation is similar, once one has noticed that the first marginal of  $\pi$  is the weak limit of the first marginal  $\mu_{\varepsilon}$  of  $\pi_{\varepsilon}$ , i.e.  $\mu$  (as recalled in the First Appendix).

So in the sequel I shall assume that  $U \geq 0$ .

Step 4: Treatment of the singular part. To take care of the singular part, the reasoning is similar to the one already used in the particular case  $\beta = 1$ : Write  $\mu = \rho \nu + \mu_s$ , and

$$
\rho_{a,\varepsilon}(x) = \int_{\mathcal{X}} K_{\varepsilon}(x,y) \, \rho(y) \, \nu(dy); \qquad \rho_{s,\varepsilon}(x) = \int_{\mathcal{X}} K_{\varepsilon}(x,y) \, \mu_s(dy).
$$

Then by convexity of U, for any  $\theta \in (0,1)$ ,

$$
U_{\pi,\nu}^{\beta}(\mu_{\varepsilon}) \le (1-\theta) U_{\pi,\nu}^{\beta} \left( \frac{\rho_{a,\varepsilon} \nu}{1-\theta} \right) + \theta U_{\pi,\nu}^{\beta} \left( \frac{\rho_{s,\varepsilon} \nu}{\theta} \right)
$$
  
$$
\le (1-\theta) U_{\pi,\nu}^{\beta} \left( \frac{\rho_{a,\varepsilon} \nu}{1-\theta} \right) + U'(\infty) \mu_{s}[\mathcal{X}],
$$

and the limit  $\theta \rightarrow 0$  yields

$$
U^{\beta}_{\pi,\nu}(\mu_{\varepsilon}) \leq U^{\beta}_{\pi,\nu}(\rho_{a,\varepsilon} \nu) + U'(\infty) \mu_{s}[\mathcal{X}].
$$

In the next two steps I shall focus on the first term  $U^{\beta}_{\pi,\nu}(\rho_{a,\varepsilon}\nu);$ I shall write  $\rho_{\varepsilon}$  for  $\rho_{a,\varepsilon}$ .

Step 5: Approximation of  $\beta$ . For any two points x, y in X, define

$$
\beta_{\varepsilon}(x,y) = \int_{\mathcal{X}\times\mathcal{X}} K_{\varepsilon}(\overline{x},x) K_{\varepsilon}(\overline{y},y) \,\beta(\overline{x},\overline{y}) \,\nu(d\overline{x}) \,\nu(d\overline{y}).
$$

The measure  $K_{\varepsilon}(x,\overline{x})K_{\varepsilon}(y,\overline{y})\nu(d\overline{x})\nu(d\overline{y})$  is a probability measure on  $\mathcal{X} \times \mathcal{X}$ , supported in  $\{d(x,\overline{x}) \leq \varepsilon, d(y,\overline{y}) \leq \varepsilon\}$ , so

$$
\left| \beta_{\varepsilon}(x, y) - \beta(x, y) \right| = \left| \int K_{\varepsilon}(x, \overline{x}) K_{\varepsilon}(y, \overline{y}) \left[ \beta(\overline{x}, \overline{y}) - \beta(x, y) \right] \nu(d\overline{x}) \nu(d\overline{y}) \right|
$$
  
$$
\leq \sup \left\{ \left| \beta(\overline{x}, \overline{y}) - \beta(x, y) \right|; \ d(x, \overline{x}) \leq \varepsilon, \ d(y, \overline{y}) \leq \varepsilon \right\}.
$$

The latter quantity goes to 0 uniformly in  $x$  and  $y$  by uniform continuity of  $\beta$ ; so  $\beta_{\varepsilon}$  converges uniformly to  $\beta$ . The goal now is to replace  $\beta$  by  $\beta_{\varepsilon}$  in the left-hand side of the desired inequality (29.17).

The map  $w : b \longrightarrow bU(r/b)$  is continuously differentiable and (since  $U \geq 0$ ),

$$
|w'(b)| = p(r/b) \le (r/b) U'(r/b) \le C(U(r/b) + r/b).
$$

So if,  $\beta \leq \tilde{\beta}$  are two positive real numbers, then

$$
\left|\widetilde{\beta} U\left(\frac{\rho}{\widetilde{\beta}}\right) - \beta U\left(\frac{\rho}{\beta}\right)\right| = \int_{\beta}^{\beta} p\left(\frac{\rho}{b}\right) db \le C \left|\widetilde{\beta} - \beta\right| \sup_{\frac{\rho}{\beta} \le r \le \frac{\rho}{\beta}} (U(r) + r).
$$

Now assume that  $\beta$  and  $\tilde{\beta}$  are bounded from above and below by positive constants, say  $B^{-1} \leq \beta$ ,  $\tilde{\beta} \leq B$ , then by (29.19) there is  $C > 0$ , depending only on  $B$ , such that

$$
\left|\widetilde{\beta}\,U\left(\frac{\rho}{\widetilde{\beta}}\right)-\beta\,U\left(\frac{\rho}{\beta}\right)\right|\leq C\left|\widetilde{\beta}-\beta\right|\left(U(\rho)+\rho\right).
$$

Apply this estimate with  $\rho = \rho_{\varepsilon}(x), \ \beta = \beta(x, y), \text{ and } {\beta, \tilde{\beta}}$  =  $\{\beta(x,y),\beta_{\varepsilon}(x,y)\}\;$ ; this is allowed since  $\beta$  is bounded from above and below by positive constants, and the same is true of  $\beta_{\varepsilon}$  since it has been obtained by averaging  $\beta$ . So there is a constant C such that for all  $x, y \in \mathcal{X}$ ,

$$
\left| \beta_{\varepsilon}(x, y) U\left(\frac{\rho_{\varepsilon}(x)}{\beta_{\varepsilon}(x, y)}\right) - \beta(x, y) U\left(\frac{\rho_{\varepsilon}(x)}{\beta(x, y)}\right) \right| \\ \leq C \left| \beta_{\varepsilon}(x, y) - \beta(x, y) \right| \left( U(\rho_{\varepsilon}(x)) + \rho_{\varepsilon}(x) \right). \quad (29.22)
$$

Then

$$
\left| \int \beta_{\varepsilon}(x, y) U \left( \frac{\rho_{\varepsilon}(x)}{\beta_{\varepsilon}(x, y)} \right) \pi_{\varepsilon}(dy|x) \nu(dx) - \int \beta(x, y) U \left( \frac{\rho_{\varepsilon}(x)}{\beta(x, y)} \right) \pi_{\varepsilon}(dy|x) \nu(dx) \right|
$$
  
\n
$$
\leq \int \left| \beta_{\varepsilon}(x, y) U \left( \frac{\rho_{\varepsilon}(x)}{\beta_{\varepsilon}(x, y)} \right) - \beta(x, y) U \left( \frac{\rho_{\varepsilon}(x)}{\beta(x, y)} \right) \right| \pi_{\varepsilon}(dy|x) \nu(dx)
$$
  
\n
$$
\leq C \int \left| \beta_{\varepsilon}(x, y) - \beta(x, y) \right| \left( U(\rho_{\varepsilon}(x)) + \rho_{\varepsilon}(x) \right) \pi_{\varepsilon}(dy|x) \nu(dx)
$$
  
\n
$$
\leq C \left( \sup_{x, y \in \mathcal{X}} \left| \beta_{\varepsilon}(x, y) - \beta(x, y) \right| \right) \int \left[ U(\rho_{\varepsilon}(x)) + \rho_{\varepsilon}(x) \right] \nu(dx)
$$
  
\n
$$
\leq C \left( \sup_{x, y \in \mathcal{X}} \left| \beta_{\varepsilon}(x, y) - \beta(x, y) \right| \right) \int \left[ U(\rho) + \rho \right] d\nu,
$$

where the last inequality follows from Jensen's inequality as in the proof of the particular case  $\beta \equiv 1$ . To summarize:

$$
\limsup_{\varepsilon \downarrow 0} \left| \int \beta_{\varepsilon}(x, y) U\left(\frac{\rho_{\varepsilon}(x)}{\beta_{\varepsilon}(x, y)}\right) \pi_{\varepsilon}(dy | x) \nu(dx) - \int \beta(x, y) U\left(\frac{\rho_{\varepsilon}(x)}{\beta(x, y)}\right) \pi_{\varepsilon}(dy | x) \nu(dx) \right| = 0.
$$

So the desired conclusion is equivalent to

$$
\limsup_{\varepsilon \downarrow 0} \int \beta_{\varepsilon}(x, y) U\left(\frac{\rho_{\varepsilon}(x)}{\beta_{\varepsilon}(x, y)}\right) \pi_{\varepsilon}(dy|x) \nu(dx) + U'(\infty) \mu_{s}[\mathcal{X}]
$$
  
$$
\leq \int \beta(x, y) U\left(\frac{\rho(x)}{\beta(x, y)}\right) \pi(dy|x) \nu(dx) + U'(\infty) \mu_{s}[\mathcal{X}].
$$

Step 6: Convexity inequality. This is a key step. By Legendre representation,

Continuity properties of the functionals  $U_{\nu}$  and  $U_{\pi,\nu}^{\beta}$  835

$$
\beta U\left(\frac{\rho}{\beta}\right) = \beta \sup_{p \in \mathbb{R}} \left( p \frac{\rho}{\beta} - U^*(p) \right)
$$
$$
= \sup_{p \in \mathbb{R}} \left[ p\rho - \beta U^*(p) \right],
$$

so  $\beta U(\rho/\beta)$  is a (jointly) convex function of  $(\beta, \rho) \in (0, +\infty) \times \mathbb{R}_+$ .

On the other hand,  $\beta_{\varepsilon}$  and  $\rho_{\varepsilon}$  are averaged values of  $\beta(\overline{x}, \overline{y})$  and  $\rho(\overline{x})$ , respectively, over the probability measure  $K_{\varepsilon}(x,\overline{x})K_{\varepsilon}(y,\overline{y})\,\nu(d\overline{x})\,\nu(d\overline{y}).$ So by Jensen's inequality,

$$
\beta_{\varepsilon}(x, y) U\left(\frac{\rho_{\varepsilon}(x)}{\beta_{\varepsilon}(x, y)}\right) \leq \int K_{\varepsilon}(x, \overline{x}) K_{\varepsilon}(y, \overline{y}) \beta(\overline{x}, \overline{y}) U\left(\frac{\rho(\overline{x})}{\beta(\overline{x}, \overline{y})}\right) \nu(d\overline{x}) \nu(d\overline{y}).
$$

To conclude the proof of (29.17) it suffices to show

$$
\varepsilon o 0 \implies \int K_{\varepsilon}(x,\overline{x}) K_{\varepsilon}(y,\overline{y}) \beta(\overline{x},\overline{y}) U\left(\frac{\rho(\overline{x})}{\beta(\overline{x},\overline{y})}\right) \nu(d\overline{x}) \nu(d\overline{y}) \pi_{\varepsilon}(dy|x) \nu(dx) + U'(\infty) \mu_{s}[\mathcal{X}] \longrightarrow \int \beta(x,y) U\left(\frac{\rho(x)}{\beta(x,y)}\right) \pi(dy|x) \nu(dx) + U'(\infty) \mu_{s}[\mathcal{X}]. (29.23)
$$

This will be the object of the final three steps.

Step 7: Approximation by a continuous function. Let us start with some explanations. Forget about the singular part for simplicity, and define

$$
f(x, y) = \beta(x, y) U\left(\frac{\rho(x)}{\beta(x, y)}\right),
$$
  
$$
\omega_{\varepsilon}(dx dy) = \left(\int K_{\varepsilon}(\overline{x}, x) K_{\varepsilon}(\overline{y}, y) \pi_{\varepsilon}(d\overline{y}|\overline{x}) \nu(d\overline{x})\right) \nu(dy) \nu(dx),
$$
  
$$
\omega(dx dy) = \pi(dy|x) \nu(dx).
$$

With this notation, the goal (29.23) can be rewritten as

$$
\int f \, d\omega_{\varepsilon} \longrightarrow \int f \, d\omega. \tag{29.24}
$$

This is not trivial, in particular because  $f$  is a priori not continuous. The obvious solution is to try to replace  $f$  by a continuous approximation, but then we run into another problem: the conditional measure  $\pi(dy|x)$  is completely arbitrary if  $\rho(x) = 0$ . This is not serious as long as we multiply it by  $f(x,y)$ , since  $f(x,y)$  vanishes when  $\rho(x)$  does, but this might become annoying when  $f(x, y)$  is replaced by a continuous approximation.

Instead, let us rather define

$$
g(x,y) = \frac{f(x,y)}{\rho(x)} = \frac{\beta(x,y)}{\rho(x)} U\left(\frac{\rho(x)}{\beta(x,y)}\right),
$$

with the convention  $U(0)/0 = U'(0)$ ,  $U(\infty)/\infty = U'(\infty)$ , and we impose that  $\rho(x)$  is finite outside of Spt  $\mu_s$  and takes the value + $\infty$ on Spt  $\mu_s$ . If  $U'(\infty) = +\infty$ , then the finiteness of  $U^{\beta}_{\pi,\nu}(\mu)$  imposes  $\mu_s[\mathcal{X}] = 0$ , so  $\text{Spt}\,\mu_s = \emptyset$ ; in particular, g takes values in  $\mathbb{R}$ .

We further define

$$
\widetilde{\pi}_{\varepsilon}(dx\,dy)=\left(\int K_{\varepsilon}(\overline{x},x)\,K_{\varepsilon}(\overline{y},y)\,\pi_{\varepsilon}(d\overline{y}|\overline{x})\,\nu(d\overline{x})\right)\nu(dy)\,\mu(dx).
$$

Note that the x-marginal of  $\widetilde{\pi}_{\varepsilon}$  is

$$
\left(\int K_{\varepsilon}(\overline{x},x) K_{\varepsilon}(\overline{y},y) \pi_{\varepsilon}(d\overline{y}|\overline{x}) \nu(d\overline{x}) \nu(dy)\right) \mu(dx)
$$
$$
= \left(\int K_{\varepsilon}(\overline{x},x) \pi_{\varepsilon}(d\overline{y}|\overline{x}) \nu(d\overline{x})\right) \mu(dx)
$$
$$
= \left(\int K_{\varepsilon}(\overline{x},x) \nu(d\overline{x})\right) \mu(dx) = \mu(dx).
$$

In particular,

$$
\int_{\text{Spt}\,\mu_s} g(x,y)\,\widetilde{\pi}_{\varepsilon}(dx\,dy) = U'(\infty)\,\mu_s(dx).
$$

Then the goal (29.23) becomes

$$
\int g(x, y) \, \widetilde{\pi}_{\varepsilon}(dx \, dy) \xrightarrow[\varepsilon \to 0]{} \int g(x, y) \, \pi(dx \, dy). \tag{29.25}
$$

• If  $U'(\infty) = +\infty$ , then  $\text{Spt}\,\mu_s = \emptyset$  and for each  $x, g(x, y)$  is a continuous function of y; moreover, since  $\beta$  is bounded from above and below by positive constants, (29.19) implies

$$
\sup_{y} \left[ \beta(x, y) U\left(\frac{\rho(x)}{\beta(x, y)}\right) \right] \leq C\Big( U(\rho(x)) + \rho(x) \Big).
$$

In particular,

$$
\int_{\mathcal{X}} \sup_{y} g(x, y) d\mu(x) = \int_{\mathcal{X}} \sup_{y} \left[ \beta(x, y) U \left( \frac{\rho(x)}{\beta(x, y)} \right) \right] d\nu(x) < +\infty;
$$

in other words, g belongs to the vector space  $L^1((X,\mu); C(X))$  of  $\mu$ -integrable functions valued in the normed space  $C(\mathcal{X})$  (equipped with the norm of uniform convergence).

• If  $U'(\infty) < +\infty$  then  $g(x, \cdot)$  is also continuous for all x (it is identically equal to  $U'(\infty)$  if  $x \in \text{Spt}\,\mu_s$ , and  $\sup_y g(x, y) \leq U'(\infty)$  is obviously  $\mu(dx)$ -integrable; so the conclusion  $g \in L^1((\mathcal{X}, \mu); C(\mathcal{X}))$ still holds true.

By Lemma 29.36 in the Second Appendix, there is a sequence  $(\Psi_k)_{k \in \mathbb{N}}$  in  $C(\mathcal{X} \times \mathcal{X})^{\mathbb{N}}$  such that

$$
\int_{\mathcal{X}} \sup_{y} |g(x, y) - \Psi_k(x, y)| d\mu(x) \xrightarrow[k \to \infty]{} 0,
$$

and each function  $\Psi_k(x, \cdot)$  is identically equal to  $U'(\infty)$  when x varies in Spt  $\mu_s$ .

Since the x-marginal of  $\pi$  is  $\mu$ ,

$$
\left| \int \left( g(x, y) - \Psi_k(x, y) \right) \pi(dx \, dy) \right|
$$
  
$$
\leq \int \sup_y |f(x, y) - \Psi_k(x, y)| \mu(dx) \xrightarrow[k \to \infty]{} 0.
$$

A similar computation applies with  $\widetilde{\pi}_{\varepsilon}$  in place of  $\pi$ . So it is also true that

$$
\limsup_{\varepsilon \downarrow 0} \left| \int \left( g(x, y) - \Psi_k(x, y) \right) \widetilde{\pi}_{\varepsilon}(dx \, dy) \right| \xrightarrow[k \to \infty]{} 0.
$$

After these estimates, to conclude the proof it is sufficient to show that for any fixed  $k$ ,

$$
\int \Psi_k(x, y) \, \widetilde{\pi}_{\varepsilon}(dx \, dy) \xrightarrow[\varepsilon \downarrow 0]{} \int \Psi_k(x, y) \, \pi(dx \, dy). \tag{29.26}
$$

In the sequel I shall drop the index k and write just  $\Psi$  for  $\Psi_k$ . It will be useful in the sequel to know that  $\Psi(x, y) = U'(\infty)$  when  $x \in \text{Spt } \mu_s$ ; apart from that,  $\Psi$  might be any continuous function.

Step 8: Variations on a regularization theme. Let  $\widetilde{\pi}_{\varepsilon}^{(a)}$  be the contribution of  $\rho \nu$  to  $\tilde{\pi}_{\varepsilon}$ . Explicitly,

$$
\widetilde{\pi}_{\varepsilon}^{(a)}(dx\,dy) = \left(\int K_{\varepsilon}(\overline{x},x)\,K_{\varepsilon}(\overline{y},y)\,\pi_{\varepsilon}(d\overline{y}|\overline{x})\,\rho(x)\,\nu(d\overline{x})\right)\nu(dy)\,\nu(dx).
$$

Then let

$$
\overline{\pi}_{\varepsilon}^{(a)}(dx\,dy) = \left(\int K_{\varepsilon}(\overline{x},x)\,K_{\varepsilon}(\overline{y},y)\,\rho(\overline{x})\,\nu(d\overline{x})\,\pi_{\varepsilon}(d\overline{y}|\overline{x})\right)\,\nu(dy)\,\nu(dx);
$$
$$
\widehat{\pi}_{\varepsilon}^{(a)}(dx\,dy) = \left(\int K_{\varepsilon}(\overline{x},x)\,K_{\varepsilon}(\overline{y},y)\,\rho_{\varepsilon}(\overline{x})\,\nu(d\overline{x})\,\pi_{\varepsilon}(d\overline{y}|\overline{x})\right)\,\nu(dy)\,\nu(dx).
$$

We shall check that  $\widetilde{\pi}_{\varepsilon}^{(a)}$  is well approximated by  $\overline{\pi}_{\varepsilon}^{(a)}$  and  $\widehat{\pi}_{\varepsilon}^{(a)}$ . First of all,

$$
\|\overline{\pi}_{\varepsilon}^{(a)} - \widetilde{\pi}_{\varepsilon}^{(a)}\|_{TV}
$$

$$
\leq \int K_{\varepsilon}(\overline{x}, x) K_{\varepsilon}(\overline{y}, y) |\rho(\overline{x}) - \rho(x) | \pi_{\varepsilon}(d\overline{y}|\overline{x}) \nu(d\overline{x}) \nu(dy) \nu(dx)
$$

$$
= \int K_{\varepsilon}(\overline{x}, x) |\rho(\overline{x}) - \rho(x) | \pi_{\varepsilon}(d\overline{y}|\overline{x}) \nu(d\overline{x}) \nu(dx)
$$

$$
= \int K_{\varepsilon}(\overline{x}, x) |\rho(\overline{x}) - \rho(x) | \nu(d\overline{x}) \nu(dx). \tag{29.27}
$$

Let us show that the integral in (29.27) converges to 0. Since  $C(\mathcal{X})$  is dense in  $L^1(\mathcal{X}, \nu)$ , there is a sequence of continuous functions  $(\psi_j)_{j\in\mathbb{N}}$ converging to  $\rho$  in  $L^1(\mathcal{X}, \nu)$ . Then

$$
\int K_{\varepsilon}(\overline{x},x) \left| \rho(\overline{x}) - \psi_j(\overline{x}) \right| \nu(d\overline{x}) \nu(d\overline{x}) = \int \left| \rho(\overline{x}) - \psi_j(\overline{x}) \right| \nu(d\overline{x}) \xrightarrow[j \to \infty]{j \to \infty} 0,
$$
\n(29.28)

and the convergence is uniform in  $\varepsilon$ . Symetrically,

$$
\int K_{\varepsilon}(\overline{x},x) \left| \rho(x) - \psi_j(x) \right| \nu(d\overline{x}) \nu(dx) \xrightarrow[j \to \infty]{} 0 \qquad \text{uniformly in } \varepsilon. \tag{29.29}
$$

On the other hand, for each j, the uniform continuity of  $\psi_j$  guarantees that

$$
\int K_{\varepsilon}(\overline{x}, x) |\psi_j(x) - \psi_j(\overline{x})| \nu(d\overline{x}) \nu(dx)
$$

$$
\leq \nu[\mathcal{X}] \sup_{d(x, \overline{x}) \leq \varepsilon} |\psi_j(x) - \psi_j(\overline{x})| \xrightarrow[\varepsilon \to 0]{} 0. \quad (29.30)
$$

The combination of (29.27), (29.28), (29.29) and (29.30) shows that

$$
\|\overline{\pi}_{\varepsilon}^{(a)} - \widetilde{\pi}_{\varepsilon}^{(a)}\|_{TV} \longrightarrow 0. \tag{29.31}
$$

Next,

$$
\begin{split}
\|\overline{\pi}_{\varepsilon}^{(a)} - \widehat{\pi}_{\varepsilon}^{(a)}\|_{TV} \\ &= \int K_{\varepsilon}(\overline{x}, x) K_{\varepsilon}(\overline{y}, y) \left|\rho_{\varepsilon}(\overline{x}) - \rho(\overline{x})\right| \pi_{\varepsilon}(d\overline{y}|\overline{x}) \nu(d\overline{x}) \nu(dy) \nu(dx) \\ &= \int K_{\varepsilon}(\overline{x}, x) \left|\rho_{\varepsilon}(\overline{x}) - \rho(\overline{x})\right| \pi_{\varepsilon}(d\overline{y}|\overline{x}) \nu(d\overline{x}) \nu(dx) \\ &= \int K_{\varepsilon}(\overline{x}, x) \left|\rho_{\varepsilon}(\overline{x}) - \rho(\overline{x})\right| \nu(d\overline{x}) \nu(dx) \\ &= \int \left|\rho_{\varepsilon}(\overline{x}) - \rho(\overline{x})\right| \nu(d\overline{x}) \\ &= \int \left|\int K_{\varepsilon}(\overline{x}, \overline{y}) \left[\rho(\overline{y}) - \rho(\overline{x})\right] \nu(d\overline{y}) \right| \nu(d\overline{x}) \\ &\leq \int K_{\varepsilon}(\overline{x}, \overline{y}) \left|\rho(\overline{y}) - \rho(\overline{x})\right| \nu(d\overline{y}) \nu(d\overline{x}),
\end{split}
$$

and this goes to 0 as we already saw before; so

$$
\|\hat{\pi}_{\varepsilon}^{(a)} - \overline{\pi}_{\varepsilon}^{(a)}\|_{TV} \longrightarrow 0, \tag{29.32}
$$

By (29.31) and (29.32),  $\|\hat{\pi}_{\varepsilon}^{(a)} - \tilde{\pi}_{\varepsilon}^{(a)}\|_{TV} \longrightarrow 0$  as  $\varepsilon \to 0$ . In particular,

$$
\int \Psi \, d\widehat{\pi}_{\varepsilon}^{(a)} - \int \Psi \, d\widetilde{\pi}_{\varepsilon}^{(a)} \xrightarrow[\varepsilon \downarrow 0]{} 0. \tag{29.33}
$$

Now let

$$
\widetilde{\pi}_{\varepsilon}^{(s)}(dx\,dy) = \left(\int K_{\varepsilon}(\overline{x},x)\,K_{\varepsilon}(\overline{y},y)\,\pi_{\varepsilon}(d\overline{y}|\overline{x}\,)\,\nu(d\overline{x})\right)\,\nu(dy)\,\mu_{s}(dx);
$$

$$
\widehat{\pi}_{\varepsilon}^{(s)}(dx\,dy) = \left(\int K_{\varepsilon}(\overline{x},x)\,K_{\varepsilon}(\overline{y},y)\,\mu_{s,\varepsilon}(d\overline{x}\,)\,\pi_{\varepsilon}(d\overline{y}|\overline{x})\right)\,\nu(dy)\,\nu(dx);
$$

so that  $\widetilde{\pi}_{\varepsilon}(dx\,dy)=\widetilde{\pi}_{\varepsilon}^{(a)}(dx\,dy)+\widetilde{\pi}_{\varepsilon}^{(s)}(dx\,dy).$  Further, define

$$
\widehat{\pi}_{\varepsilon}(dx\,dy) = \widehat{\pi}_{\varepsilon}^{(a)}(dx\,dy) + \widehat{\pi}_{\varepsilon}^{(s)}(dx\,dy)
$$

$$
= \left( \int K_{\varepsilon}(\overline{x}, x) K_{\varepsilon}(\overline{y}, y) \, \pi_{\varepsilon}(d\overline{x}\,d\overline{y}) \right) \, \nu(dy) \, \nu(dx).
$$

Since  $\Psi(x, y) = U'(\infty)$  when  $x \in \text{Spt } \mu_s$ , we have

$$
\int \Psi \, d\widetilde{\pi}_{\varepsilon}^{(s)} = \int_{\text{Spt }\mu_s} \Psi \, d\widetilde{\pi}_{\varepsilon}^{(s)} = U'(\infty) \, \mu_s[\mathcal{X}],
$$

while

$$
\int \Psi \, d\widehat{\pi}_{\varepsilon}^{(s)} = U'(\infty) \, \mu_{s,\varepsilon}[\mathcal{X}] = U'(\infty) \, \mu_s[\mathcal{X}].
$$

Combining this with (29.33), we can conclude that

$$
\int \Psi \, d\widehat{\pi}_{\varepsilon} - \int \Psi \, d\widetilde{\pi}_{\varepsilon} \xrightarrow[\varepsilon \downarrow 0]{} 0. \tag{29.34}
$$

At this point, to finish the proof of the theorem it suffices to establish  $\int \Psi \, d\hat{\pi}_{\varepsilon} \longrightarrow \int \Psi \, d\pi$ ; which is true if  $\hat{\pi}_{\varepsilon}$  converges weakly to  $\pi$ .

**Step 9: Duality.** Proving the convergence of  $\hat{\pi}_{\varepsilon}$  to  $\pi$  will be easy because  $\hat{\pi}_{\varepsilon}$  is a kind of regularization of  $\pi_{\varepsilon}$ , and it will be possible to "transfer the regularization to the test function" by duality. Indeed:

$$
\int \Psi(x, y) \,\widehat{\pi}_{\varepsilon}(dx\,dy) = \int \Psi(x, y) \, K_{\varepsilon}(\overline{x}, x) \, K_{\varepsilon}(\overline{y}, y) \, \pi_{\varepsilon}(d\overline{y}\,d\overline{x}) \, \nu(dy) \, \nu(dx)
$$

$$
= \int \Psi_{\varepsilon}(\overline{x}, \overline{y}) \, \pi_{\varepsilon}(d\overline{y}\,d\overline{x}),
$$

where

$$
\Psi_{\varepsilon}(\overline{x},\overline{y}) = \int \Psi(x,y) K_{\varepsilon}(\overline{x},x) K_{\varepsilon}(\overline{y},y) \nu(dy) \nu(dx).
$$

By the same classical argument that we already used several times,  $\Psi_{\varepsilon}$  converges uniformly to  $\Psi$ :

$$

$$

Since on the other hand  $\pi_{\varepsilon}$  converges weakly to  $\pi$ , we conclude that

$$
\int \Psi_{\varepsilon} d\pi_{\varepsilon} \longrightarrow \int \Psi d\pi,
$$

and the proof is complete. □

I shall conclude this section with a corollary of Theorem 29.20:

Corollary 29.23 (Another sufficient condition to be a weak  $CD(K, N)$  space). In Definition 29.8 it is equivalent to require inequality (29.11) for all probability measures  $\mu_0, \mu_1$ ; or only when  $\mu_0, \mu_1$ are absolutely continuous with continuous densities.

*Proof of Corollary 29.23.* Assume that  $(\mathcal{X}, d, \nu)$  satisfies the assumptions of Definition 29.8, except that  $\mu_0, \mu_1$  are required to be absolutely continuous. The goal is to show that the absolute continuity condition can be relaxed.

By Proposition 29.12, we may assume that  $U$  has polynomial growth, in the sense of Theorem 29.20(iii). Let us also assume that  $\beta_t^{(K,N)}$  $t^{(K,N)}$  is continuous.

Let  $\mu_0, \mu_1$  be two possibly singular probability measures such that Spt  $\mu_0$ , Spt  $\mu_1 \subset \nu$ . By Theorem 29.20(iii), there are sequences of probability measures  $\mu_{k,0} \to \mu_0$  and  $\mu_{k,1} \to \mu_1$ , all absolutely continuous and with continuous densities, such that for any  $\pi_k \in \Pi(\mu_{k,0}, \mu_{k,1}),$ converging weakly to  $\pi$ ,

$$
\left\{
\begin{array}{l}
\limsup_{k \to \infty} U_{\pi_k, \nu}^{\beta_{1-t}^{(K, N)}}(\mu_{k, 0}) \leq U_{\pi, \nu}^{\beta_{1-t}^{(K, N)}}(\mu_0) \\
\limsup_{k \to \infty} U_{\tilde{\pi}_k, \nu}^{\beta_{t}^{(K, N)}}(\mu_{k, 1}) \leq U_{\tilde{\pi}, \nu}^{\beta_{t}^{(K, N)}}(\mu_{k, 0})
\end{array}
\right. (29.35)
$$

For each  $k \in \mathbb{N}$ , there is a displacement interpolation  $(\mu_{k,t})_{0 \leq t \leq 1}$  and an associated optimal transference plan  $\pi_k \in P(\mathcal{X} \times \mathcal{X})$  such that

$$
U_{\nu}(\mu_{k,t}) \le (1-t) U_{\pi_k, \nu}^{\beta_{1-t}^{(K,N)}}(\mu_{k,0}) + t U_{\check{\pi}_k, \nu}^{\beta_t^{(K,N)}}(\mu_{k,1}). \tag{29.36}
$$

By Theorem 28.9 (in the very simple case when  $\mathcal{X}_k = \mathcal{X}$  for all k), we may extract a subsequence such that  $\mu_{k,t} \longrightarrow \mu_t$  in  $P_2(\mathcal{X})$ , for each  $t \in [0, 1]$ , and  $\pi_k \longrightarrow \pi$  in  $P(\mathcal{X} \times \mathcal{X})$ , where  $(\mu_t)_{0 \leq t \leq 1}$  is a displacement interpolation and  $\pi \in \Pi(\mu_0, \mu_1)$  is an associated optimal transference plan. Then by Theorem 29.20(i),

$$
U_{\nu}(\mu_t) \le \liminf_{k \to \infty} U_{\nu}(\mu_{k,t}).
$$

Combining this with (29.35) and (29.36), we deduce that

$$
U_{\nu}(\mu_t) \le (1-t) U_{\pi,\nu}^{\beta_{1-t}^{(K,N)}}(\mu_0) + t U_{\tilde{\pi},\nu}^{\beta_t^{(K,N)}}(\mu_1),
$$
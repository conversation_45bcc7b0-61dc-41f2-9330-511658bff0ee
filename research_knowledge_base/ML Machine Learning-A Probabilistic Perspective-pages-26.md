# **22** *More variational inference*

## **22.1 Introduction**

In Chapter 21, we discussed mean field inference, which approximates the posterior by a product of marginal distributions. This allows us to use different parametric forms for each variable, which is particularly useful when performing Bayesian inference for the parameters of statistical models (such as the mean and variance of a Gaussian or GMM, or the regression weights in a GLM), as we saw when we discussed variational Bayes and VB-EM.

In this chapter, we discuss a slightly different kind of variational inference. The basic idea is to minimize  $J(q) = \mathbb{KL}(q||\tilde{p})$ , where  $\tilde{p}$  is the exact but unnormalized posterior as before, but where we no longer require q to be factorized. In fact, we do not even require q to be a globally valid joint distribution. Instead, we only require that q is locally consistent, meaning that the joint distribution of two adjacent nodes agrees with the corresponding marginals (we will define this more precisely below).

In addition to this new kind of inference, we will discuss approximate methods for MAP state estimation in discrete graphical models. It turns out that algorithms for solving the MAP problem are very similar to some approximate methods for computing marginals, as we will see.

# **22.2 Loopy belief propagation: algorithmic issues**

There is a very simple approximate inference algorithm for discrete (or Gaussian) graphical models known as **loopy belief propagation** or **LBP**. The basic idea is extremely simple: we apply the belief propagation algorithm of Section 20.2 to the graph, even if it has loops (i.e., even if it is not a tree). This method is simple and efficient, and often works well in practice, outperforming mean field (Weiss 2001). In this section, we discuss the algorithm in more detail. In the next section, we analyse this algorithm in terms of variational inference.

# **22.2.1 A brief history**

When applied to loopy graphs, BP is not guaranteed to give correct results, and may not even converge. Indeed, Judea Pearl, who invented belief propagation for trees, wrote the following about loopy BP in 1988:

When loops are present, the network is no longer singly connected and local propagation

schemes will invariably run into trouble ... If we ignore the existence of loops and permit the nodes to continue communicating with each other as if the network were singly connected, messages may circulate indefinitely around the loops and the process may not converge to a stable equilibrium ... Such oscillations do not normally occur in probabilistic networks ... which tend to bring all messages to some stable equilibrium as time goes on. However, this asymptotic equilibrium is not coherent, in the sense that it does not represent the posterior probabilities of all nodes of the network — (Pearl 1988, p.195)

Despite these reservations, Pearl advocated the use of belief propagation in loopy networks as an approximation scheme (J. Pearl, personal communication) and exercise 4.7 in (Pearl 1988) investigates the quality of the approximation when it is applied to a particular loopy belief network.

However, the main impetus behind the interest in BP arose when McEliece et al. (1998) showed that a popular algorithm for error correcting codes known as turbo codes (Berrou et al. 1993) could be viewed as an instance of BP applied to a certain kind of graph. This was an important observation since turbo codes have gotten very close to the theoretical lower bound on coding efficiency proved by Shannon. (Another approach, known as low density parity check or LDPC codes, has achieved comparable performance; it also uses LBP for decoding — see Figure 22.1 for an example.) In (Murphy et al. 1999), LBP was experimentally shown to also work well for inference in other kinds of graphical models beyond the error-correcting code context, and since then, the method has been widely used in many different applications.

## **22.2.2 LBP on pairwise models**

We now discuss how to apply LBP to an undirected graphical model with pairwise factors (we discuss the directed case, which can involve higher order factors, in the next section). The method is simple: just continually apply Equations 20.11 and 20.10 until convergence. See Algorithm 8 for the pseudocode, and beliefPropagation for some Matlab code. We will discuss issues such as convergence and accuracy of this method shortly.

| <b>Algorithm 22.1:</b> Loopy belief propagation for a pairwise MRF               |  |
|----------------------------------------------------------------------------------|--|
| 1 Input: node potentials $\psi_s(x_s)$ , edge potentials $\psi_{st}(x_s, x_t)$ ; |  |
| 2 Initialize messages $m_{s\rightarrow t}(x_t) = 1$ for all edges $s - t$ ;      |  |
| 3 Initialize beliefs $bel_s(x_s) = 1$ for all nodes s;                           |  |
| 4 repeat                                                                         |  |
| 5 Send message on each edge                                                      |  |

$$
m_{s \to t}(x_t) = \sum_{x_s} \left( \psi_s(x_s) \psi_{st}(x_s, x_t) \prod_{u \in \text{nbr}_s \backslash t} m_{u \to s}(x_s) \right);
$$

**6** Update belief of each node  $\text{bel}_s(x_s) \propto \psi_s(x_s) \prod_{t \in \text{nbr}_s} m_{t \to s}(x_s)$ ;

**<sup>7</sup> until** *beliefs don't change significantly*;

**8** Return marginal beliefs bel<sub>s</sub> $(x_s)$ ;

Image /page/2/Figure/1 description: The image displays two bipartite graphs, labeled (a) and (b). Graph (a) is a small bipartite graph with six nodes on the left, labeled x1 through x6, and four nodes on the right, labeled psi 134, psi 256, psi 135, and psi 246. Lines connect nodes from the left set to the right set, indicating relationships. Graph (b) is a much larger, more complex bipartite graph, resembling a tree structure with some cyclical connections. It consists of many small circles and squares connected by lines. The overall structure of graph (b) is radial, with a central cluster of nodes expanding outwards into a more spread-out arrangement.

**Figure 22.1** (a) A simple factor graph representation of a (2,3) low-density parity check code (factor graphs are defined in Section ********). Each message bit (hollow round circle) is connected to two parity factors (solid black squares), and each parity factor is connected to three bits. Each parity factor has the form  $\psi_{stu}(x_s, x_t, x_u) = \mathbb{I}(x_s \otimes x_t \otimes x_u = 1)$ , where  $\otimes$  is the xor operator. The local evidence factors for each hidden node are not shown. (b) A larger example of a random LDPC code. We see that this graph is "locally tree-like", meaning there are no short cycles; rather, each cycle has length  $\sim \log m$ , where m is the number of nodes. This gives us a hint as to why loopy BP works so well on such graphs. (Note, however, that some error correcting code graphs have short loops, so this is not the full explanation.) Source: Figure 2.9 from (Wainwright and Jordan 2008b). Used with kind permission of Martin Wainwright.

## **22.2.3 LBP on a factor graph**

To handle models with higher-order clique potentials (which includes directed models where some nodes have more than one parent), it is useful to use a representation known as a factor graph. We explain this representation below, and then describe how to apply LBP to such models.

## ********** Factor graphs**

A **factor graph** (Kschischang et al. 2001; Frey 2003) is a graphical representation that unifies directed and undirected models, and which simplifies certain message passing algorithms. More precisely, a factor graph is an undirected bipartite graph with two kinds of nodes. Round nodes represent variables, square nodes represent factors, and there is an edge from each variable to every factor that mentions it. For example, consider the MRF in Figure 22.2(a). If we assume one potential per maximal clique, we get the factor graph in Figure 22.2(b), which represents the function

$$
f(x_1, x_2, x_3, x_4) = f_{124}(x_1, x_2, x_4) f_{234}(x_2, x_3, x_4)
$$
\n(22.1)

If we assume one potential per edge, we get the factor graph in Figure 22.2(c), which represents the function

$$
f(x_1, x_2, x_3, x_4) = f_{14}(x_1, x_4) f_{12}(x_1, x_2) f_{34}(x_3, x_4) f_{23}(x_2, x_3) f_{24}(x_2, x_4)
$$
(22.2)

Image /page/3/Figure/1 description: The image displays three different graph structures labeled (a), (b), and (c). Graph (a) is a diamond shape with nodes labeled 1, 2, 3, and 4. Nodes 1 and 3 are at the top and bottom respectively, and nodes 4 and 2 are on the left and right. There are edges connecting 1 to 4, 1 to 2, 4 to 3, and 2 to 3. Additionally, there is a horizontal edge connecting 4 and 2. Graph (b) also has nodes labeled 1, 2, 3, and 4, arranged in a similar diamond-like structure but with square nodes inserted in the middle of the edges. Node 1 is at the top, node 3 is at the bottom, and nodes 4 and 2 are on the sides. There are square nodes connecting 1 to 4, 1 to 2, 4 to 3, and 2 to 3. Graph (c) is similar to graph (b) but with an additional square node in the middle of the horizontal edge between nodes 4 and 2. Specifically, there are square nodes connecting 1 to 4, 1 to 2, 4 to 3, and 2 to 3, and also a square node connecting 4 to 2.

**Figure 22.2** (a) A simple UGM. (b) A factor graph representation assuming one potential per maximal clique. (c) A factor graph representation assuming one potential per edge.

Image /page/3/Figure/3 description: The image displays two diagrams, labeled (a) and (b), illustrating probabilistic graphical models. Diagram (a) is a directed acyclic graph (DAG) showing five nodes labeled x1, x2, x3, x4, and x5. Arrows indicate dependencies: x1 and x2 both point to x3, and x3 points to x4 and x5. Diagram (b) represents the same probabilistic model in a factor graph format. It includes variable nodes (circles labeled x1, x2, x3, x4, x5) and factor nodes (rectangles representing probability distributions: p(x1), p(x2), p(x3|x1, x2), p(x4|x3), and p(x5|x3)). Lines connect variable nodes to the factor nodes that involve them.

Figure 22.3 (a) A simple DGM. (b) Its corresponding factor graph. Based on Figure 5 of (Yedidia et al. 2001)..

We can also convert a DGM to a factor graph: just create one factor per CPD, and connect that factor to all the variables that use that CPD. For example, Figure 22.3 represents the following factorization:

$$
f(x_1, x_2, x_3, x_4, x_5) = f_1(x_1) f_2(x_2) f_{123}(x_1, x_2, x_3) f_{34}(x_3, x_4) f_{35}(x_3, x_5)
$$
(22.3)

where we define  $f_{123}(x_1, x_2, x_3) = p(x_3|x_1, x_2)$ , etc. If each node has at most one parent (and hence the graph is a chain or simple tree), then there will be one factor per edge (root nodes can have their prior CPDs absorvbed into their children's factors). Such models are equivalent to pairwise MRFs.

Image /page/4/Figure/1 description: This is a diagram illustrating a factor graph, likely used in probabilistic graphical models or signal processing. On the left, a variable node 'x' (represented by a circle) is connected to several factor nodes 'h1', 'h2', and subsequent factors indicated by dots, all enclosed within a dashed oval labeled 'n(x) \ {f}'. Arrows labeled 'μh1→x(x)' and similar indicate messages being passed from the factor nodes to the variable node 'x'. In the center, there are bidirectional arrows between 'x' and a factor node 'f' (represented by a square), labeled 'μx→f(x)' and 'μf→x(x)', signifying message passing between these nodes. On the right, the factor node 'f' is connected to variable nodes 'y1', 'y2', and subsequent variables indicated by dots, all enclosed within a dashed oval labeled 'n(f) \ {x}'. Arrows labeled 'μy1→f(y1)' and similar indicate messages being passed from the variable nodes to the factor node 'f'.

**Figure 22.4** Message passing on a bipartite factor graph. Square nodes represent factors, and circles represent variables. Source: Figure 6 of (Kschischang et al. 2001). Used with kind permission of Brendan Frey.

## ********** BP on a factor graph**

We now derive a version of BP that sends messages on a factor graph, as proposed in (Kschischang et al. 2001). Specifically, we now have two kinds of messages: variables to factors

$$
m_{x \to f}(x) = \prod_{h \in \text{nbr}(x) \setminus \{f\}} m_{h \to x}(x)
$$
\n(22.4)

and factors to variables:

$$
m_{f \to x}(x) = \sum_{\mathbf{y}} f(x, \mathbf{y}) \prod_{y \in \text{nbr}(f) \setminus \{x\}} m_{y \to f}(y)
$$
\n(22.5)

Here nbr(x) are all the factors that are connected to variable x, and  $\text{nbr}(f)$  are all the variables that are connected to factor  $f$ . These messages are illustrated in Figure 22.4. At convergence, we can compute the final beliefs as a product of incoming messages:

$$
bel(x) \propto \prod_{f \in \text{nbr}(x)} m_{f \to x}(x) \tag{22.6}
$$

In the following sections, we will focus on LBP for pairwise models, rather than for factor graphs, but this is just for notational simplicity.

## **22.2.4 Convergence**

LBP does not always converge, and even when it does, it may converge to the wrong answers. This raises several questions: how can we predict when convergence will occur? what can we do to increase the probability of convergence? what can we do to increase the rate of convergence? We briefly discuss these issues below. We then discuss the issue of accuracy of the results at convergence.

Image /page/5/Figure/1 description: This figure contains six plots labeled (a) through (f). Plot (a) shows the percentage of messages converged over time in seconds, with three lines representing Synchronous, Asynchronous, and No smoothing. The Synchronous line is dotted and shows a gradual increase from 0% to about 60% over 100 seconds. The Asynchronous line is solid and shows a rapid increase from 0% to about 60% in 40 seconds, then fluctuates around 60%. The No smoothing line is dashed and stays around 20% for the first 40 seconds, then increases to about 50% and fluctuates. Plots (b) through (f) show the probability P(Xn = 0) over time in seconds, where n is a specific value (115, 10, 61, 7, and 17 respectively). Each of these plots has three lines: Synchronous (dotted), Asynchronous (solid), and True (horizontal solid line). In plots (b) and (c), the Synchronous and Asynchronous lines converge to approximately 0.2. In plot (d), the Asynchronous line fluctuates around 0.55, while the Synchronous line fluctuates around 0.65. In plot (e), both Synchronous and Asynchronous lines oscillate significantly, with the Synchronous line oscillating between approximately 0.4 and 0.9, and the Asynchronous line oscillating between approximately 0.4 and 0.8. In plot (f), the Asynchronous line oscillates between approximately 0.2 and 0.6, while the Synchronous line oscillates between approximately 0.1 and 0.9. The True line in plots (b) through (f) is a horizontal line at 0.2 in (b) and (c), at 0.5 in (d) and (e), and at 0.2 in (f).

**Figure 22.5** Illustration of the behavior of loopy belief propagation on an  $11 \times 11$  Ising grid with random potentials,  $w_{ij} \sim \text{Unif}(-C, C)$ , where  $C = 11$ . For larger C, inference becomes harder. (a) Percentage of messasges that have converged vs time for 3 different update schedules: Dotted = damped sychronous (few nodes converge), dashed = undamped asychnronous (half the nodes converge), solid = damped asychnronous (all nodes converge). (b-f) Marginal beliefs of certain nodes vs time. Solid straight line = truth, dashed = sychronous, solid = damped asychronous. Source: Figure 11.C.1 of (Koller and Friedman 2009). Used with kind permission of Daphne Koller.

## **22.2.4.1 When will LBP converge?**

The details of the analysis of when LBP will converge are beyond the scope of this chapter, but we briefly sketch the basic idea. The key analysis tool is the **computation tree**, which visualizes the messages that are passed as the algorithm proceeds. Figure 22.6 gives a simple example. In the first iteration, node 1 receives messages from nodes 2 and 3. In the second iteration, it receives one message from node 3 (via node 2), one from node 2 (via node 3), and two messages from node 4 (via nodes 2 and 3). And so on.

The key insight is that  $T$  iterations of LBP is equivalent to exact computation in a computation tree of height  $T + 1$ . If the strengths of the connections on the edges is sufficiently weak, then the influence of the leaves on the root will diminish over time, and convergence will occur. See (Wainwright and Jordan 2008b) and references therein for more information.

Image /page/6/Figure/1 description: The image displays two diagrams, labeled (a) and (b). Diagram (a) is a graph with four nodes, two of which are shaded gray and labeled 2 and 3, and two are white and labeled 1 and 4. The nodes are arranged in a diamond shape, with edges connecting 1 to 2, 1 to 3, 2 to 4, and 3 to 4. Additionally, there is an edge connecting nodes 2 and 3. Diagram (b) shows a rooted tree structure. The root node is labeled 1 and is white. Its children are a gray node labeled 2 and a gray node labeled 3. The tree branches out with nodes colored white and gray, and each node is labeled with a number. The leaves of the tree are also labeled with numbers.

Figure 22.6 (a) A simple loopy graph. (b) The computation tree, rooted at node 1, after 4 rounds of message passing. Nodes 2 and 3 occur more often in the tree because they have higher degree than nodes 1 and 2. Source: Figure 8.2 of (Wainwright and Jordan 2008b). Used with kind permission of Martin Wainwright.

## ********** Making LBP converge**

Although the theoretical convergence analysis is very interesting, in practice, when faced with a model where LBP is not converging, what should we do?

One simple way to reduce the chance of oscillation is to use **damping**. That is, instead of sending the message  $M_{ts}^k$ , we send a damped message of the form

$$
\tilde{M}_{ts}^k(x_s) = \lambda M_{ts}(x_s) + (1 - \lambda)\tilde{M}_{ts}^{k-1}(x_s)
$$
\n(22.7)

where  $0 \leq \lambda \leq 1$  is the damping factor Clearly if  $\lambda = 1$  this reduces to the standard scheme, but for  $\lambda$  < 1, this partial updating scheme can help improve convergence. Using a value such as  $\lambda \sim 0.5$  is standard practice. The benefits of this approach are shown in Figure 22.5, where we see that damped updating results in convergence much more often than undamped updating.

It is possible to devise methods, known as **double loop algorithms**, which are guaranteed to converge to a local minimum of the same objective that LBP is minimizing (Yuille 2001; Welling and Teh 2001). Unfortunately, these methods are rather slow and complicated, and the accuracy of the resulting marginals is usually not much greater than with standard LBP. (Indeed, oscillating marginals is sometimes a sign that the LBP approximation itself is a poor one.) Consequently, these techniques are not very widely used. In Section 22.4.2, we will see a different convergent version of BP that is widely used.

## **22.2.4.3 Increasing the convergence rate: message scheduling**

Even if LBP converges, it may take a long time. The standard approach when implementing LBP is to perform **synchronous updates**, where all nodes absorb messages in parallel, and then send out messages in parallel. That is, the new messages at iteration  $k + 1$  are computed in parallel using

$$
\mathbf{m}^{k+1} = (f_1(\mathbf{m}^k), \dots, f_E(\mathbf{m}^k))
$$
\n(22.8)

where E is the number of edges, and  $f_{st}(\mathbf{m})$  is the function that computes the message for edge  $s \to t$  given all the old messages. This is analogous to the Jacobi method for solving linear systems of equations. It is well known (Bertsekas 1997) that the Gauss-Seidel method, which performs **asynchronous updates** in a fixed round-robin fashion, converges faster when solving linear systems of equations. We can apply the same idea to LBP, using updates of the form

$$
\mathbf{m}_{i}^{k+1} = f_{i} \left( \{ \mathbf{m}_{j}^{k+1} : j < i \}, \{ \mathbf{m}_{j}^{k} : j > i \} \right)
$$
 (22.9)

where the message for edge i is computed using new messages (iteration  $k + 1$ ) from edges earlier in the ordering, and using old messages (iteration  $k$ ) from edges later in the ordering.

This raises the question of what order to update the messages in. One simple idea is to use a fixed or random order. The benefits of this approach are shown in Figure 22.5, where we see that (damped) asynchronous updating results in convergence much more often than synchronous updating.

A smarter approach is to pick a set of spanning trees, and then to perform an up-down sweep on one tree at a time, keeping all the other messages fixed. This is known as **tree reparameterization** (TRP) (Wainwright et al. 2001), which should not be confused with the more sophisticated tree-reweighted BP (often abbreviated to TRW) to be discussed in Section 22.4.2.1.

However, we can do even better by using an adaptive ordering. The intuition is that we should focus our computational efforts on those variables that are most uncertain. (Elidan et al. 2006) proposed a technique known as **residual belief propagation**, in which messages are scheduled to be sent according to the norm of the difference from their previous value. That is, we define the residual of new message  $m_{st}$  at iteration  $k$  to be

$$
r(s, t, k) = ||\log m_{st} - \log m_{st}^k||_{\infty} = \max_{i} |\log \frac{m_{st}(i)}{m_{st}^k(i)}|
$$
\n(22.10)

We can store messages in a priority queue, and always send the one with highest residual. When a message is sent from s to t, all of the other messages that depend on  $m_{st}$  (i.e., messages of the form  $m_{tu}$  where  $u \in \text{nbr}(t) \setminus s$  need to be recomputed; their residual is recomputed, and they are added back to the queue. In (Elidan et al. 2006), they showed (experimentally) that this method converges more often, and much faster, than using sychronous updating, asynchronous updating with a fixed order, and the TRP approach.

A refinement of residual BP was presented in (Sutton and McCallum 2007). In this paper, they use an upper bound on the residual of a message instead of the actual residual. This means that messages are only computed if they are going to be sent; they are not just computed for the purposes of evaluating the residual. This was observed to be about five times faster than residual BP, although the quality of the final results is similar.

## **22.2.5 Accuracy of LBP**

For a graph with a single loop, one can show that the max-product version of LBP will find the correct MAP estimate, if it converges (Weiss 2000). For more general graphs, one can bound the error in the approximate marginals computed by LBP, as shown in (Wainwright et al. 2003; Vinyals et al. 2010). Much stronger results are available in the case of Gaussian models (Weiss and Freeman 2001a; Johnson et al. 2006; Bickson 2009). In particular, in the Gaussian case, if the method converges, the means are exact, although the variances are not (typically the beliefs are over confident).

## **22.2.6 Other speedup tricks for LBP \***

There are several tricks one can use to make BP run faster. We discuss some of them below.

## **22.2.6.1 Fast message computation for large state spaces**

The cost of computing each message in BP (whether in a tree or a loopy graph) is  $O(K^f)$ , where K is the number of states, and f is the size of the largest factor ( $f = 2$  for pairwise UGMs). In many vision problems (e.g., image denoising),  $K$  is quite large (say 256), because it represents the discretization of some underlying continuous space, so  $O(K^2)$  per message is too expensive. Fortunately, for certain kinds of pairwise potential functions of the form  $\psi_{st}(x_s, x_t) = \psi(x_s - x_t)$ , one can compute the sum-product messages in  $O(K \log K)$  time using the fast Fourier transform or FFT, as explained in (Felzenszwalb and Huttenlocher 2006). The key insight is that message computation is just convolution:

$$
M_{st}^{k}(x_t) = \sum_{x_s} \psi(x_s - x_t) h(x_s)
$$
\n(22.11)

where  $h(x_s) = \psi_s(x_s) \prod_{v \in \text{nbr}(s) \setminus t} M_{vs}^{k-1}(x_s)$ . If the potential function  $\psi(z)$  is a Gaussian-like potential, we can compute the convolution in  $O(K)$  time by sequentially convolving with a small number of box filters (Felzenszwalb and Huttenlocher 2006).

For the max-product case, a technique called the **distance transform** can be used to compute messages in  $O(K)$  time. However, this only works if  $\psi(z) = \exp(-E(z))$  and where  $E(z)$ has one the following forms: quadratic,  $E(z) = z^2$ ; truncated linear,  $E(z) = \min(c_1|z|, c_2)$ ; or Potts model,  $E(z) = c \mathbb{I}(z \neq 0)$ . See (Felzenszwalb and Huttenlocher 2006) for details.

## **22.2.6.2 Multi-scale methods**

A method which is specific to 2d lattice structures, which commonly arise in computer vision, is based on multi-grid techniques. Such methods are widely used in numerical linear algebra, where one of the core problems is the fast solution of linear systems of equations; this is equivalent to MAP estimation in a Gaussian MRF. In the computer vision context, (Felzenszwalb and Huttenlocher 2006) suggested using the following heuristic to significantly speedup BP: construct a coarse-to-fine grid, compute messages at the coarse level, and use this to initialize messages at the level below; when we reach the bottom level, just a few iterations of standard BP are required, since long-range communication has already been achieved via the initialization process.

The beliefs at the coarse level are computed over a small number of large blocks. The local evidence is computed from the average log-probability each possible block label assigns to all the pixels in the block. The pairwise potential is based on the discrepancy between labels of neighboring blocks, taking into account their size. We can then run LBP at the coarse level, and then use this to initialize the messages one level down. Note that the *model* is still a flat grid; however, the *initialization process* exploits the multi-scale nature of the problem. See (Felzenszwalb and Huttenlocher 2006) for details.

## **22.2.6.3 Cascades**

Another trick for handling high-dimensional state-spaces, that can also be used with exact inference (e.g., for chain-structured CRFs), is to prune out improbable states based on a computationally cheap filtering step. In fact, one can create a hierarchy of models which tradeoff speed and accuracy. This is called a computational **cascade**. In the case of chains, one can guarantee that the cascade will never filter out the true MAP solution (Weiss et al. 2010).

# **22.3 Loopy belief propagation: theoretical issues \***

We now attempt to understand the LBP algorithm from a variational point of view. Our presentation is closely based on an excellent 300-page review article (Wainwright and Jordan 2008a). This paper is sometimes called "the monster" (by its own authors!) in view of its length and technical difficulty. This section just sketches some of the main results.

To simplify the presentation, we focus on the special case of pairwise UGMs with discrete variables and tabular potentials. Many of the results generalize to UGMs with higher-order clique potentials (which includes DGMs), but this makes the notation more complex (see (Koller and Friedman 2009) for details of the general case).

## **22.3.1 UGMs represented in exponential family form**

We assume the distribution has the following form:

$$
p(\mathbf{x}|\boldsymbol{\theta}, G) = \frac{1}{Z(\boldsymbol{\theta})} \exp \left\{ \sum_{s \in \mathcal{V}} \theta_s(x_s) + \sum_{(s,t) \in \mathcal{E}} \theta_{st}(x_s, x_t) \right\}
$$
(22.12)

where graph G has nodes  $\mathcal V$  and edges  $\mathcal E$ . (Henceforth we will drop the explicit conditioning on  $\theta$  and G for brevity, since we assume both are known and fixed.) We can rewrite this in exponential family form as follows:

$$
p(\mathbf{x}|\boldsymbol{\theta}) = \frac{1}{Z(\boldsymbol{\theta})} \exp(-E(\mathbf{x}))
$$
\n(22.13)

$$
E(\mathbf{x}) \triangleq -\boldsymbol{\theta}^T \boldsymbol{\phi}(\mathbf{x}) \tag{22.14}
$$

where  $\boldsymbol{\theta} = (\{\theta_{s,i}\}, \{\theta_{s,t,i,k}\})$  are all the node and edge parameters (the canonical parameters), and  $\phi(\mathbf{x}) = (\{\mathbb{I}(x_s = j)\}, \{\mathbb{I}(x_s = j, x_t = k)\})$  are all the node and edge indicator functions (the sufficient statistics). Note: we use  $s, t \in V$  to index nodes and  $j, k \in \mathcal{X}$  to index states.

The mean of the sufficient statistics are known as the mean parameters of the model, and are given by

$$
\boldsymbol{\mu} = \mathbb{E} \left[ \phi(\mathbf{x}) \right] = (\{ p(x_s = j) \}_s, \{ p(x_s = j, x_t = k) \}_s \neq t) = (\{ \mu_{s;j} \}_s, \{ \mu_{st;jk} \}_{s \neq t}) \tag{22.15}
$$

This is a vector of length  $d = |\mathcal{X}||V| + |\mathcal{X}|^2|E|$ , containing the node and edge marginals. It completely characterizes the distribution  $p(x|\theta)$ , so we sometimes treat  $\mu$  as a distribution itself.

Equation 22.12 is called the **standard overcomplete representation**. It is called "overcomplete" because it ignores the sum-to-one constraints. In some cases, it is convenient to remove this redundancy. For example, consider an Ising model where  $X_s \in \{0, 1\}$ . The model can be written as

$$
p(\mathbf{x}) = \frac{1}{Z(\theta)} \exp \left\{ \sum_{s \in \mathcal{V}} \theta_s x_s + \sum_{(s,t) \in \mathcal{E}} \theta_{st} x_s x_t \right\}
$$
(22.16)

Hence we can use the following minimal parameterization

$$
\phi(\mathbf{x}) = (x_s, s \in V; x_s x_t, (s, t) \in E) \in \mathbb{R}^d
$$
\n(22.17)

where  $d = |V| + |E|$ . The corresponding mean parameters are  $\mu_s = p(x_s = 1)$  and  $\mu_{st} =$  $p(x_s = 1, x_t = 1).$ 

## **22.3.2 The marginal polytope**

The space of allowable  $\mu$  vectors is called the **marginal polytope**, and is denoted  $\mathbb{M}(G)$ , where G is the structure of the graph defining the UGM. This is defined to be the set of all mean parameters for the given model that can be generated from a valid probability distribution:

$$
\mathbb{M}(G) \triangleq \{ \mu \in \mathbb{R}^d : \exists p \quad \text{s.t.} \quad \mu = \sum_{\mathbf{x}} \phi(\mathbf{x}) p(\mathbf{x}) \text{ for some } p(\mathbf{x}) \ge 0, \sum_{\mathbf{x}} p(\mathbf{x}) = 1 \} (22.18)
$$

For example, consider an Ising model. If we have just two nodes connected as  $X_1 - X_2$ , one can show that we have the following minimal set of constraints:  $0 \leq \mu_{12}$ ,  $0 \leq \mu_{12} \leq \mu_1$ ,  $0 \leq \mu_{12} \leq \mu_2$ , and  $1 + \mu_{12} - \mu_1 - \mu_2 \geq 0$ . We can write these in matrix-vector form as

$$
\begin{pmatrix} 0 & 0 & 1 \\ 1 & 0 & -1 \\ 0 & 1 & -1 \\ -1 & -1 & 1 \end{pmatrix} \begin{pmatrix} \mu_1 \\ \mu_2 \\ \mu_{12} \end{pmatrix} \ge \begin{pmatrix} 0 \\ 0 \\ 0 \\ -1 \end{pmatrix}
$$
(22.19)

These four constraints define a series of half-planes, whose intersection defines a polytope, as shown in Figure 22.7(a).

Since  $\mathbb{M}(G)$  is obtained by taking a convex combination of the  $\phi(\mathbf{x})$  vectors, it can also be written as the **convex hull** of the feature set:

$$
\mathbb{M}(G) = \text{conv}\{\phi_1(\mathbf{x}), \dots, \phi_d(\mathbf{x})\}\tag{22.20}
$$

For example, for a 2 node MRF  $X_1 - X_2$  with binary states, we have

$$
\mathbb{M}(G) = \text{conv}\{(0,0,0), (1,0,0), (0,1,0), (1,1,1)\}\
$$
\n(22.21)

These are the four black dots in Figure 22.7(a). We see that the convex hull defines the same volume as the intersection of half-spaces.

The marginal polytope will play a crucial role in the approximate inference algorithms we discuss in the rest of this chapter.

Image /page/11/Figure/1 description: The image displays three diagrams labeled (a), (b), and (c). Diagram (a) is a 3D coordinate system with axes labeled \(\mu\_1\), \(\mu\_2\), and \(\mu\_{12}\). It shows a tetrahedron with vertices at (0,0,0), (1,0,0), (0,1,0), and (1,1,1), with the tetrahedron shaded in gray. Diagram (b) shows a closed loop of connected points, forming a polygon with curved lines connecting adjacent vertices. This shape is labeled \(M\_F(G)\) and \(M(G)\), with an arrow pointing to one of the vertices labeled \(\mu\_e\). Diagram (c) shows a shaded polygon with vertices labeled with black and gray dots. The polygon is labeled \(M(G)\) and \(L(G)\). Arrows point to the vertices and edges, with labels \(\mu\_x\) and \(\tau\).

**Figure 22.7** (a) Illustration of the marginal polytope for an Ising model with two variables. (b) Cartoon illustration of the set  $\mathbb{M}_F(G)$ , which is a nonconvex inner bound on the marginal polytope  $\mathbb{M}(G)$ .  $\mathbb{M}_F(G)$ is used by mean field. (c) Cartoon illustration of the relationship between  $\mathbb{M}(G)$  and  $\mathbb{L}(G)$ , which is used by loopy BP. The set  $\mathbb{L}(G)$  is always an outer bound on  $\mathbb{M}(G)$ , and the inclusion  $\mathbb{M}(G) \subset \mathbb{L}(G)$  is strict whenever  $G$  has loops. Both sets are polytopes, which can be defined as an intersection of half-planes (defined by facets), or as the convex hull of the vertices.  $\mathbb{L}(G)$  actually has fewer facets than  $\mathbb{M}(G)$ , despite the picture. In fact,  $\mathbb{L}(G)$  has  $O(|\mathcal{X}||V| + |\mathcal{X}|^2|E|)$  facets, where  $|\mathcal{X}|$  is the number of states per variable, |V| is the number of variables, and |E| is the number of edges. By contrast,  $\mathbb{M}(G)$  has  $O(|\mathcal{X}|^{|V|})$  facets.<br>On the other hand  $\mathbb{I}(G)$  has more vertices than  $\mathbb{M}(G)$  despite the picture since  $\mathbb{I}(G)$  conta On the other hand,  $\mathbb{L}(G)$  has more vertices than  $\mathbb{M}(G)$ , despite the picture, since  $\mathbb{L}(G)$  contains all the binary vector extreme points  $\mu \in M(G)$ , plus additional fractional extreme points. Source: Figures 3.6, 5.4 and 4.2 of (Wainwright and Jordan 2008a). Used with kind permission of Martin Wainwright.

## **22.3.3 Exact inference as a variational optimization problem**

Recall from Section 21.2 that the goal of variational inference is to find the distribution q that maximizes the **energy functional**

$$
L(q) = -\mathbb{KL}\left(q||p\right) + \log Z = \mathbb{E}_q\left[\log \tilde{p}(\mathbf{x})\right] + \mathbb{H}\left(q\right) \le \log Z \tag{22.22}
$$

where  $\tilde{p}(\mathbf{x}) = Zp(\mathbf{x})$  is the unnormalized posterior. If we write  $\log \tilde{p}(\mathbf{x}) = \theta^T \phi(\mathbf{x})$ , and we let  $q = p$ , then the exact energy functional becomes

$$
\max_{\boldsymbol{\mu} \in \mathbb{M}(G)} \boldsymbol{\theta}^T \boldsymbol{\mu} + \mathbb{H}(\boldsymbol{\mu})
$$
\n(22.23)

where  $\mu = \mathbb{E}_p [\phi(\mathbf{x})]$  is a joint distribution over all state configurations **x** (so it is valid to write  $\mathbb{H}(\mu)$ ). Since the KL divergence is zero when  $p = q$ , we know that

$$
\max_{\boldsymbol{\mu} \in \mathbb{M}(G)} \boldsymbol{\theta}^T \boldsymbol{\mu} + \mathbb{H}(\boldsymbol{\mu}) = \log Z(\boldsymbol{\theta})
$$
\n(22.24)

This is a way to cast exact inference as a variational optimization problem.

Equation 22.24 seems easy to optimize: the objective is concave, since it is the sum of a linear function and a concave function (see Figure 2.21 to see why entropy is concave); furthermore, we are maximizing this over a convex set. However, the marginal polytope  $\mathbb{M}(G)$  has exponentially many facets. In some cases, there is structure to this polytope that can be exploited by dynamic programming (as we saw in Chapter 20), but in general, exact inference takes exponential time. Most of the existing deterministic approximate inference schemes that have been proposed in the literature can be seen as different approximations to the marginal polytope, as we explain below.

## **22.3.4 Mean field as a variational optimization problem**

We discussed mean field at length in Chapter 21. Let us re-interpret mean field inference in our new more abstract framework. This will help us compare it to other approximate methods which we discuss below.

First, let F be an edge subgraph of the original graph G, and let  $\mathcal{I}(F) \subseteq \mathcal{I}$  be the subset of sufficient statistics associated with the cliques of F. Let  $\Omega$  be the set of canonical parameters for the full model, and define the canonical parameter space for the submodel as follows:

$$
\Omega(F) \triangleq \{ \boldsymbol{\theta} \in \Omega : \boldsymbol{\theta}_{\alpha} = 0 \,\forall \alpha \in \mathcal{I} \setminus \mathcal{I}(F) \}
$$
\n(22.25)

In other words, we require that the natural parameters associated with the sufficient statistics  $\alpha$  outside of our chosen class to be zero. For example, in the case of a fully factorized approximation,  $F_0$ , we remove all edges from the graph, giving

$$
\Omega(F_0) \triangleq \{ \boldsymbol{\theta} \in \Omega : \boldsymbol{\theta}_{st} = 0 \ \forall (s, t) \in E \}
$$
\n(22.26)

In the case of structured mean field (Section 21.4), we set  $\theta_{st} = 0$  for edges which are not in our tractable subgraph.

Next, we define the mean parameter space of the restricted model as follows:

$$
\mathbb{M}_F(G) \triangleq \{ \boldsymbol{\mu} \in \mathbb{R}^d : \boldsymbol{\mu} = \mathbb{E}_{\boldsymbol{\theta}} \left[ \boldsymbol{\phi}(\mathbf{x}) \right] \text{ for some } \boldsymbol{\theta} \in \Omega(F) \}
$$
\n(22.27)

This is called an **inner approximation** to the marginal polytope, since  $\mathbb{M}_F(G) \subset \mathbb{M}(G)$ . See Figure 22.7(b) for a sketch. Note that  $\mathbb{M}_F(G)$  is a non-convex polytope, which results in multiple local optima. By contrast, some of the approximations we will consider later will be convex.

We define the entropy of our approximation  $\mathbb{H}(\mu(F))$  as the entropy of the distribution  $\mu$  defined on submodel F. Then we define the **mean field energy functional** optimization problem as follows:

$$
\max_{\boldsymbol{\mu} \in \mathbb{M}_F(G)} \boldsymbol{\theta}^T \boldsymbol{\mu} + \mathbb{H}(\boldsymbol{\mu}) \le \log Z(\boldsymbol{\theta})
$$
\n(22.28)

In the case of the fully factorized mean field approximation for pairwise UGMs, we can write this objective as follows:

$$
\max_{\mu \in \mathcal{P}^d} \sum_{s \in V} \sum_{x_s} \theta_s(x_s) \mu_s(x_s) + \sum_{(s,t) \in E} \sum_{x_s, x_t} \theta_{st}(x_s, x_t) \mu_s(x_s) \mu_t(x_t) + \sum_{s \in V} \mathbb{H}(\mu_s) \tag{22.29}
$$

where  $\mu_s \in \mathcal{P}$ , and  $\mathcal P$  is the probability simplex over  $\mathcal X$ .

Mean field involves a concave objective being maximized over a non-convex set. It is typically optimized using coordinate ascent, since it is easy to optimize a scalar concave function over  $P$ for each  $\mu_s$ . For example, for a pairwise UGM we get

$$
\mu_s(x_s) \propto \exp(\theta_s(x_s)) \exp\left(\sum_{t \in \text{nbr}(s)} \sum_{x_t} \mu_t(x_t) \theta_{st}(x_s, x_t)\right) \tag{22.30}
$$

## **22.3.5 LBP as a variational optimization problem**

In this section, we explain how LBP can be viewed as a variational inference problem.

Image /page/13/Figure/1 description: The image displays two figures, labeled (a) and (b). Figure (a) is a graph representing a network with three white nodes labeled 1, 2, and 3, forming a triangle. Each node has associated matrices of numbers. Node 1 has matrices [[0.4, 0.1], [0.1, 0.4]] and [[0.1, 0.4], [0.4, 0.1]]. Node 2 has matrices [[0.4, 0.1], [0.1, 0.4]] and [[0.5], [0.5]]. Node 3 has matrices [[0.4, 0.1], [0.1, 0.4]] and [[0.5], [0.5]]. There are also three black nodes, each connected to one of the white nodes. The black nodes connected to nodes 1 and 3 have matrices [[0.5], [0.5]]. The black node connected to node 2 also has matrices [[0.5], [0.5]]. Figure (b) is a 3D coordinate system with axes labeled \( ext{μ}\_{12}\), \( ext{μ}\_{13}\), and \( ext{μ}\_{23}\). It shows a shaded polyhedron with black and gray dots representing vertices. Key vertices are labeled with coordinates: (0, 1/2, 0), (0, 0, 1/2), and (1/2, 1/2, 1/2). Gray dots are also present at other positions within the coordinate system.

**Figure 22.8** (a) Illustration of pairwise UGM on binary nodes, together with a set of pseudo marginals that are not globally consistent. (b) A slice of the marginal polytope illustrating the set of feasible edge marginals, assuming the node marginals are clamped at  $\mu_1 = \mu_2 = \mu_3 = 0.5$ . Source: Figure 4.1 of (Wainwright and Jordan 2008a). Used with kind permission of Martin Wainwright.

#### **22.3.5.1 An outer approximation to the marginal polytope**

If we want to consider all possible probability distributions which are Markov wrt our model, we need to consider all vectors  $\mu \in M(G)$ . Since the set  $M(G)$  is exponentially large, it is usually infeasible to optimize over. A standard strategy in combinatorial optimization is to relax the constraints. In this case, instead of requiring probability vector  $\mu$  to live in M(G), we consider a vector  $\tau$  that only satisfies the following **local consistency** constraints:

$$
\sum_{x_s} \tau_s(x_s) = 1 \tag{22.31}
$$

$$
\sum_{x_t} \tau_{st}(x_s, x_t) = \tau_s(x_s) \tag{22.32}
$$

The first constraint is called the normalization constraint, and the second is called the marginalization constraint. We then define the set

$$
\mathbb{L}(G) \triangleq \{ \tau \ge 0 : (22.31) \text{ holds } \forall s \in V \text{ and } (22.32) \text{ holds } \forall (s, t) \in E \}
$$
\n
$$
(22.33)
$$

The set  $\mathbb{L}(G)$  is also a polytope, but it only has  $O(|V| + |E|)$  constraints. It is a convex **outer approximation** on  $M(G)$ , as shown in Figure 22.7(c).

We call the terms  $\tau_s, \tau_{st} \in L(G)$  pseudo marginals, since they may not correspond to marginals of any valid probability distribution. As an example of this, consider Figure 22.8(a). The picture shows a set of pseudo node and edge marginals, which satisfy the local consistency requirements. However, they are not globally consistent. To see why, note that  $\tau_{12}$  implies  $p(X_1 = X_2) = 0.8$ ,  $\tau_{23}$  implies  $p(X_2 = X_3) = 0.8$ , but  $\tau_{13}$  implies  $p(X_1 = X_3) = 0.2$ , which is not possible (see (Wainwright and Jordan 2008b, p81) for a formal proof). Indeed, Figure 22.8(b) shows that  $\mathbb{L}(G)$  contains points that are not in  $\mathbb{M}(G)$ .

We claim that  $\mathbb{M}(G) \subseteq \mathbb{L}(G)$ , with equality iff G is a tree. To see this, first consider

an element  $\mu \in M(G)$ . Any such vector must satisfy the normalization and marginalization constraints, hence  $\mathbb{M}(G) \subseteq \mathbb{L}(G)$ .

Now consider the converse. Suppose T is a tree, and let  $\mu \in L(T)$ . By definition, this satisfies the normalization and marginalization constraints. However, any tree can be represented in the form

$$
p_{\mu}(\mathbf{x}) = \prod_{s \in V} \mu_s(x_s) \prod_{(s,t) \in E} \frac{\mu_{st}(x_s, x_t)}{\mu_s(x_s)\mu_t(x_t)}
$$
(22.34)

Hence satsifying normalization and local consistency is enough to define a valid distribution for any tree. Hence  $\mu \in M(T)$  as well.

In contrast, if the graph has loops, we have that  $\mathbb{M}(G) \neq \mathbb{L}(G)$ . See Figure 22.8(b) for an example of this fact.

## **22.3.5.2 The entropy approximation**

From Equation 22.34, we can write the exact entropy of any tree structured distribution  $\mu \in$  $\mathbb{M}(T)$  as follows:

$$
\mathbb{H}(\boldsymbol{\mu}) = \sum_{s \in V} H_s(\mu_s) - \sum_{(s,t) \in E} I_{st}(\mu_{st}) \tag{22.35}
$$

$$
H_s(\mu_s) = -\sum_{x_s \in \mathcal{X}_s} \mu_s(x_s) \log \mu_s(x_s)
$$
\n(22.36)

$$
I_{st}(\mu_{st}) = \sum_{(x_s, x_t) \in \mathcal{X}_s \times \mathcal{X}_t} \mu_{st}(x_s, x_t) \log \frac{\mu_{st}(x_s, x_t)}{\mu_s(x_s)\mu_t(x_t)}
$$
(22.37)

Note that we can rewrite the mutual information term in the form  $I_{st}(\mu_{st}) = H_s(\mu_s) + H_t(\mu_t) H_{st}(\mu_{st})$ , and hence we get the following alternative but equivalent expression:

$$
\mathbb{H}(\mu) = -\sum_{s \in V} (d_s - 1) H_s(\mu_s) + \sum_{(s,t) \in E} H_{st}(\mu_{st})
$$
\n(22.38)

where  $d_s$  is the degree (number of neighbors) for node s.

The **Bethe**<sup>1</sup> approximation to the entropy is simply the use of Equation 22.35 even when we don't have a tree:

$$
\mathbb{H}_{\text{Bethe}}(\tau) = \sum_{s \in V} H_s(\tau_s) - \sum_{(s,t) \in E} I_{st}(\tau_{st}) \tag{22.39}
$$

We define the **Bethe free energy** as

$$
F_{\text{Bethe}}(\tau) \triangleq -\left[\boldsymbol{\theta}^T \boldsymbol{\tau} + \mathbb{H}_{\text{Bethe}}(\tau)\right]
$$
\n(22.40)

We define the **Bethe energy functional** as the negative of the Bethe free energy.

<sup>1.</sup> Hans Bethe was a German-American physicist, 1906–2005.

#### **22.3.5.3 The LBP objective**

Combining the outer approximation  $\mathbb{L}(G)$  with the Bethe approximation to the entropy, we get the following Bethe variational problem (BVP):

$$
\min_{\boldsymbol{\tau} \in \mathbb{L}(G)} F_{\text{Bethe}}(\boldsymbol{\tau}) = \max_{\boldsymbol{\tau} \in \mathbb{L}(G)} \boldsymbol{\theta}^T \boldsymbol{\tau} + \mathbb{H}_{\text{Bethe}}(\boldsymbol{\tau}) \tag{22.41}
$$

The space we are optimizing over is a convex set, but the objective itself is not concave (since  $\mathbb{H}_{\text{Bethe}}$  is not concave). Thus there can be multiple local optima of the BVP.

The value obtained by the BVP is an approximation to  $\log Z(\theta)$ . In the case of trees, the approximation is exact, and in the case of models with attractive potentials, the approximation turns out to be an upper bound (Sudderth et al. 2008).

#### ********** Message passing and Lagrange multipliers**

In this subsection, we will show that any fixed point of the LBP algorithm defines a stationary point of the above constrained objective. Let us define the normalization constraint at  $C_{ss}(\bm{\tau})\triangleq$  $1 - \sum_{x_s} \tau_s(x_s)$ , and the marginalization constraint as  $C_{ts}(x_s; \tau) \triangleq \tau_s(x_s) - \sum_{x_t} \tau_{st}(x_s, x_t)$ for each edge  $t \rightarrow s$ . We can now write the Lagrangian as

$$
\mathcal{L}(\tau, \lambda; \theta) \triangleq \theta^T \tau + \mathbb{H}_{\text{Bethe}}(\tau) + \sum_s \lambda_{ss} C_{ss}(\tau) + \sum_{s,t} \left[ \sum_{x_s} \lambda_{ts}(x_s) C_{ts}(x_s; \tau) + \sum_{x_t} \lambda_{st}(x_t) C_{st}(x_t; \tau) \right]
$$
(22.42)

(The constraint that  $\tau \geq 0$  is not explicitly enforced, but one can show that it will hold at the optimum since  $\theta > 0$ .) Some simple algebra then shows that  $\nabla_{\tau} \mathcal{L} = 0$  yields

$$
\log \tau_s(x_s) = \lambda_{ss} + \theta_s(x_s) + \sum_{t \in \text{nbr}(s)} \lambda_{ts}(x_s)
$$
\n(22.43)

$$
\log \frac{\tau_{st}(x_s, x_t)}{\tilde{\tau}_s(x_s)\tilde{\tau}_t(x_t)} = \theta_{st}(x_s, x_t) - \lambda_{ts}(x_s) - \lambda_{st}(x_t)
$$
\n(22.44)

where we have defined  $\tilde{\tau}_s(x_s) \triangleq \sum_{x_t} \tau(x_s, x_t)$ . Using the fact that the marginalization constraint implies  $\tilde{\tau}_s(x_s) = \tau_s(x_s)$ , we get

$$
\log \tau_{st}(x_s, x_t) = \lambda_{ss} + \lambda_{tt} + \theta_{st}(x_s, x_t) + \theta_s(x_s) + \theta_t(x_t) + \sum_{u \in \text{nbr}(s) \setminus t} \lambda_{us}(x_s) + \sum_{u \in \text{nbr}(t) \setminus s} \lambda_{ut}(x_t)
$$
\n(22.45)

To make the connection to message passing, define  $M_{ts}(x_s) = \exp(\lambda_{ts}(x_s))$ . With this notation, we can rewrite the above equations (after taking exponents of both sides) as follows:

$$
\tau_s(x_s) \propto \exp(\theta_s(x_s)) \prod_{t \in \text{nbr}(s)} M_{ts}(x_s) \tag{22.46}
$$

$$
\tau_{st}(x_s, x_t) \propto \exp(\theta_{st}(x_s, x_t) + \theta_s(x_s) + \theta_t(x_t))
$$

$$
\times \prod_{u \in \text{nbr}(s) \backslash t} M_{us}(x_s) \prod_{u \in \text{nbr}(t) \backslash s} M_{ut}(x_t) \tag{22.47}
$$

where the  $\lambda$  terms are absorbed into the constant of proportionality. We see that this is equivalent to the usual expression for the node and edge marginals in LBP.

To derive an equation for the messages in terms of other messages (rather than in terms of  $\lambda_{ts}$ ), we enforce the marginalization condition  $\sum_{x_t} \tau_{st}(x_s, x_t) = \tau_s(x_s)$ . Then one can show that

$$
M_{ts}(x_s) \propto \sum_{x_t} \left[ \exp \left\{ \theta_{st}(x_s, x_t) + \theta_t(x_t) \right\} \prod_{u \in \text{nbr}(t) \backslash s} M_{ut}(x_t) \right]
$$
(22.48)

We see that this is equivalent to the usual expression for the messages in LBP.

## **22.3.6 Loopy BP vs mean field**

It is interesting to compare the naive mean field (MF) and LBP approximations. There are several obvious differences. First, LBP is exact for trees whereas MF is not, suggesting LBP will in general be more accurate (see (Wainwright et al. 2003) for an analysis). Second, LBP optimizes over node and edge marginals, whereas MF only optimizes over node marginals, again suggesting LBP will be more accurate. Third, in the case that the true edge marginals factorize, so  $\mu_{st} = \mu_s \mu_t$ , the free energy approximations will be the same in both cases.

What is less obvious, but which nevertheless seems to be true, is that the MF objective has many more local optima than the LBP objective, so optimizing the MF objective seems to be harder. In particular, (Weiss 2001), shows empirically that optimizing MF starting from uniform or random initial conditions often leads to poor results, whereas optimizing BP from uniform initial messages often leads to good results. Furthermore, initializing MF with the BP marginals also leads to good results (although MF tends to be more overconfident than BP), indicating that the problem is caused not by the inaccuracy of the MF approximation, but rather by the severe non-convexity of the MF objective, and by the weakness of the standard coordinate descent optimization method used by  $MF<sup>2</sup>$  However, the advantage of MF is that it gives a lower bound on the partition function, unlike BP, which is useful when using it as a subroutine inside a learning algorithm. Also, MF is easier to extend to other distributions besides discrete and Gaussian, as we saw in Chapter 21. Intuitively, this is because MF only works with marginal distributions, which have a single type, rather than needing to define pairwise distributions, which may need to have two different types.

# **22.4 Extensions of belief propagation \***

In this section, we discuss various extensions of LBP.

## **22.4.1 Generalized belief propagation**

We can improve the accuracy of loopy BP by clustering together nodes that form a tight loop. This is known as the **cluster variational method**. The result is a hyper-graph, which is a graph

<sup>2. (</sup>Honkela et al. 2003) discusses the use of the pattern search algorithm to speedup mean field inference in the case of continuous random variables. It is possible that similar ideas could be adapted to the discrete case, although there may be no reason to do this, given that LBP already works well in the discrete case.

Image /page/17/Figure/1 description: The image displays two diagrams, labeled (a) and (b). Diagram (a) shows a 3x3 grid of nodes, numbered 1 through 9, with solid lines connecting adjacent nodes horizontally and vertically. Dashed circles are drawn around nodes 1, 2, 3, 4, 5, 6, 7, 8, and 9, with overlapping circles indicating connections or proximity. Diagram (b) illustrates a directed graph. A central node labeled '5' has incoming arrows from nodes labeled '2 5', '4 5', '5 6', and '5 8'. Node '2 5' has an incoming arrow from '1 2 4 5'. Node '4 5' has an incoming arrow from '1 2 4 5' and an outgoing arrow to '5'. Node '5 6' has an incoming arrow from '2 3 5 6' and an outgoing arrow to '5'. Node '5 8' has an incoming arrow from '5 8' and an outgoing arrow to '5'. There are also nodes labeled '4 5 7 8' with an incoming arrow to '4 5' and '5 8', and '5 6 8 9' with an incoming arrow to '5 6' and '5 8'.

**Figure 22.9** (a) Kikuchi clusters superimposed on a  $3 \times 3$  lattice graph. (b) Corresponding hyper-graph. Source: Figure 4.5 of (Wainwright and Jordan 2008b). Used with kind permission of Martin Wainwright.

where there are hyper-edges between sets of vertices instead of between single vertices. Note that a junction tree (Section 20.4.1) is a kind of hyper-graph. We can represent hyper-graph using a poset (partially ordered set) diagram, where each node represents a hyper-edge, and there is an arrow  $e_1 \rightarrow e_2$  if  $e_2 \subset e_1$ . See Figure 22.9 for an example.

Let t be the size of the largest hyper-edge in the hyper-graph. If we allow t to be as large as the treewidth of the graph, then we can represent the hyper-graph as a tree, and the method will be exact, just as LBP is exact on regular trees (with treewidth 1). In this way, we can define a continuum of approximations, from LBP all the way to exact inference.

Define  $\mathbb{L}_t(G)$  to be the set of all pseudo-marginals such that normalization and marginalization constraints hold on a hyper-graph whose largest hyper-edge is of size  $t + 1$ . For example, in Figure 22.9, we impose constraints of the form

$$
\sum_{x_1, x_2} \tau_{1245}(x_1, x_2, x_4, x_5) = \tau_{45}(x_4, x_5), \quad \sum_{x_6} \tau_{56}(x_5, x_6) = \tau_5(x_5), \dots
$$
 (22.49)

Furthermore, we approximate the entropy as follows:

$$
\mathbb{H}_{\text{Kikuchi}}(\tau) \triangleq \sum_{g \in E} c(g) H_g(\tau_g)
$$
\n(22.50)

where  $H_q(\tau_q)$  is the entropy of the joint (pseudo) distribution on the vertices in set g, and  $c(g)$ is called the **overcounting number** of set g. These are related to **Mobious numbers** in set theory. Rather than giving a precise definition, we just give a simple example. For the graph in Figure 22.9, we have

$$
\mathbb{H}_{\text{Kikuchi}}(\tau) = [H_{1245} + H_{2356} + H_{4578} + H_{5689}] - [H_{25} + H_{45} + H_{56} + H_{58}] + H_5
$$
\n(22.51)

Putting these two approximations together, we can define the **Kikuchi free energy**<sup>3</sup> as follows:

$$
F_{\text{Kikuchi}}(\tau) \triangleq -\left[\boldsymbol{\theta}^T \boldsymbol{\tau} + \mathbb{H}_{\text{Kikuchi}}(\tau)\right]
$$
\n(22.52)

<sup>3.</sup> Ryoichi Kikuchi is a Japanese physicist.

Our variational problem becomes

$$
\min_{\boldsymbol{\tau} \in \mathbb{L}_t(G)} F_{\text{Kikuchi}}(\boldsymbol{\tau}) = \max_{\boldsymbol{\tau} \in \mathbb{L}_t(G)} \boldsymbol{\theta}^T \boldsymbol{\tau} + \mathbb{H}_{\text{Kikuchi}}(\boldsymbol{\tau})
$$
\n(22.53)

Just as with the Bethe free energy, this is not a concave objective. There are several possible algorithms for finding a local optimum of this objective, including a message passing algorithm known as **generalized belief propagation**. However, the details are beyond the scope of this chapter. See e.g., (Wainwright and Jordan 2008b, Sec 4.2) or (Koller and Friedman 2009, Sec 11.3.2) for more information. Suffice it to say that the method gives more accurate results than LBP, but at increased computational cost (because of the need to handle clusters of nodes). This cost, plus the complexity of the approach, have precluded it from widespread use.

## **22.4.2 Convex belief propagation**

The mean field energy functional is concave, but it is maximized over a non-convex inner approximation to the marginal polytope. The Bethe and Kikuchi energy functionals are not concave, but they are maximized over a convex outer approximation to the marginal polytope. Consequently, for both MF and LBP, the optimization problem has multiple optima, so the methods are sensitive to the initial conditions. Given that the exact formulation (Equation 22.24) a concave objective maximized over a convex set, it is natural to try to come up with an appproximation which involves a concave objective being maximized over a convex set.

We now describe one method, known as **convex belief propagation**. This involves working with a set of tractable submodels, F, such as trees or planar graphs. For each model  $F \subset G$ , the entropy is higher,  $\mathbb{H}(\mu(F)) \geq \mathbb{H}(\mu(G))$ , since F has fewer constraints. Consequently, any convex combination of such subgraphs will have higher entropy, too:

$$
\mathbb{H}\left(\mu(G)\right) \leq \sum_{F \in \mathcal{F}} \rho(F) \mathbb{H}\left(\mu(F)\right) \triangleq \mathbb{H}(\mu, \rho) \tag{22.54}
$$

where  $\rho(F) \ge 0$  and  $\sum_F \rho(F)=1$ . Furthermore,  $\mathbb{H}(\mu, \rho)$  is a concave function of  $\mu$ . We now define the convex free energy as

$$
F_{\text{Convex}}(\boldsymbol{\mu}, \rho) \triangleq -\left[\boldsymbol{\mu}^T \boldsymbol{\theta} + \mathbb{H}(\boldsymbol{\mu}, \rho)\right]
$$
(22.55)

We define the concave energy functional as the negative of the convex free energy. We discuss how to optimize  $\rho$  below.

Having defined an upper bound on the entropy, we now consider a convex outerbound on the marginal polytope of mean parameters. We want to ensure we can evaluate the entropy of any vector  $\tau$  in this set, so we restrict it so that the projection of  $\tau$  onto the subgraph G lives in the projection of  $M$  onto  $F$ :

$$
\mathbb{L}(G;\mathcal{F}) \triangleq \{ \boldsymbol{\tau} \in \mathbb{R}^d : \boldsymbol{\tau}(F) \in \mathbb{M}(F) \,\forall F \in \mathcal{F} \}
$$
\n(22.56)

This is a convex set since each  $\mathbb{M}(F)$  is a projection of a convex set. Hence we define our problem as

$$
\min_{\boldsymbol{\tau} \in \mathbb{L}(G; \mathcal{F})} F_{\text{Convex}}(\boldsymbol{\tau}, \rho) = \max_{\boldsymbol{\tau} \in \mathbb{L}(G; \mathcal{F})} \boldsymbol{\tau}^T \boldsymbol{\theta} + \mathbb{H}(\boldsymbol{\tau}, \rho) \tag{22.57}
$$

This is a concave objective being maximized over a convex set, and hence has a unique maximum. We give a specific example below.

Image /page/19/Figure/1 description: The image displays four distinct graphs labeled (a), (b), (c), and (d). Each graph consists of nodes represented by circles and edges represented by lines connecting these nodes. The graphs are variations of a tree-like structure with some branching. Specific nodes are labeled with letters: 'f' appears at the top of the structures, and 'b' and 'e' appear at different positions within the branching patterns. Graph (a) shows a square-like structure at the top connected to a central node labeled 'b', which then branches into a triangle-like structure with a node labeled 'e'. Graph (b) shows a linear arrangement of nodes with 'f' at the top, a node labeled 'b' in the middle, and a node labeled 'e' at the bottom, with some side branches. Graph (c) is similar to (b) but with a different branching pattern below the 'b' node, leading to the 'e' node. Graph (d) also resembles (b) and (c) but has a distinct branching configuration near the 'e' node.

**Figure 22.10** (a) A graph. (b-d) Some of its spanning trees. Source: Figure 7.1 of (Wainwright and Jordan 2008b). Used with kind permission of Martin Wainwright.

## **22.4.2.1 Tree-reweighted belief propagation**

Consider the specific case where  $\mathcal F$  is all spanning trees of a graph. For any given tree, the entropy is given by Equation 22.35. To compute the upper bound, obtained by averaging over all trees, note that the terms  $\sum_F \rho(F) H(\mu(F)_s)$  for single nodes will just be  $H_s$ , since node s appears in every tree, and  $\sum_F \rho(F)=1$ . But the mutual information term  $I_{st}$  receives weight  $\rho_{st} = \mathbb{E}_{\rho} [\mathbb{I}((s,t) \in E(T))]$ , known as the **edge appearance probability**. Hence we have the following upper bound on the entropy:

$$
\mathbb{H}\left(\boldsymbol{\mu}\right) \leq \sum_{s \in V} H_s(\mu_s) - \sum_{(s,t) \in E} \rho_{st} I_{st}(\mu_{st})
$$
\n(22.58)

The edge appearance probabilities live in a space called the **spanning tree polytope**. This is because they are constrained to arise from a distribution over trees. Figure 22.10 gives an example of a graph and three of its spanning trees. Suppose each tree has equal weight under ρ. The edge f occurs in 1 of the 3 trees, so  $ρ<sub>f</sub> = 1/3$ . The edge e occurs in 2 of the 3 trees, so  $\rho_e = 2/3$ . The edge b appears in all of the trees, so  $\rho_b = 1$ . And so on. Ideally we can find a distribution  $\rho$ , or equivalently edge probabilities in the spanning tree polytope, that make the above bound as tight as possible. An algorithm to do this is described in (Wainwright et al. 2005). (A simpler approach is to generate spanning trees of  $G$  at random until all edges are covered, or use all single edges with weight  $\rho_e = 1/E$ .)

What about the set we are optimizing over? We require  $\mu(T) \in M(T)$  for each tree T, which means enforcing normalization and local consistency. Since we have to do this for every tree, we are enforcing normalization and local consistency on every edge. Hence  $\mathbb{L}(G; \mathcal{F}) = \mathbb{L}(G)$ . So our final optimization problem is as follows:

$$
\max_{\tau \in \mathbb{L}(G)} \left\{ \tau^T \theta + \sum_{s \in V} H_s(\tau_s) - \sum_{(s,t) \in E(G)} \rho_{st} I_{st}(\tau_{st}) \right\}
$$
(22.59)

which is the same as the LBP objective except for the crucial  $\rho_{st}$  weights. So long as  $\rho_{st} > 0$ for all edges  $(s, t)$ , this problem is strictly concave with a unique maximum.

How can we find this global optimum? As for LBP, there are several algorithms, but perhaps the simplest is a modification of belief propagation known as **tree reweighted belief propagation**, also called **TRW** or **TRBP** for short. The message from t to s is now a function of all messages sent from other neighbors v to t, as before, but now it is also a function of the message sent from  $s$  to  $t$ . Specifically

$$
M_{ts}(x_s) \propto \sum_{x_t} \exp\left(\frac{1}{\rho_{st}} \theta_{st}(x_s, x_t) + \theta_t(x_t)\right) \frac{\prod_{v \in \text{nbr}(t) \setminus s} [M_{vt}(x_t)]^{\rho_{vt}}}{[M_{st}(x_t)]^{1-\rho_{ts}}}
$$
(22.60)

At convergence, the node and edge pseudo marginals are given by

$$
\tau_s(x_s) \propto \exp(\theta_s(x_s)) \prod_{v \in \text{nbr}(s)} [M_{vs}(x_s)]^{\rho_{vs}} \tag{22.61}
$$

$$
\tau_{st}(x_s, x_t) \propto \varphi_{st}(x_s, x_t) \frac{\prod_{v \in \text{nbr}(s) \setminus t} [M_{vs}(x_s)]^{\rho_{vs}}}{[M_{ts}(x_s)]^{1-\rho_{st}}} \frac{\prod_{v \in \text{nbr}(t) \setminus s} [M_{vt}(x_t)]^{\rho_{vt}}}{[M_{st}(x_t)]^{1-\rho_{ts}}}
$$
(22.62)

$$
\varphi_{st}(x_s, x_t) \triangleq \exp\left(\frac{1}{\rho_{st}} \theta_{st}(x_s, x_t) + \theta_s(x_s) + \theta_t(x_t)\right) \tag{22.63}
$$

This algorithm can be derived using a method similar to that described in Section ********.

If  $\rho_{st} = 1$  for all edges  $(s, t) \in E$ , the algorithm reduces to the standard LBP algorithm. However, the condition  $\rho_{st} = 1$  implies every edge is present in every spanning tree with probability 1, which is only possible if the original graph is a tree. Hence the method is only equivalent to standard LBP on trees, when the method is of course exact.

In general, this message passing scheme is not guaranteed to converge to the unique global optimum. One can devise double-loop methods that are guaranteed to converge (Hazan and Shashua 2008), but in practice, using damped updates as in Equation 22.7 is often sufficient to ensure convergence.

It is also possible to produce a convex version of the Kikuchi free energy, which one can optimize with a modified version of generalized belief propagation. See (Wainwright and Jordan 2008b, Sec 7.2.2) for details.

From Equation 22.59, and using the fact that the TRBP entropy approximation is an upper bound on the true entropy, wee see that the TRBP objective is an upper bound on  $\log Z$ . Using the fact that  $I_{st} = H_s + H_t - H_{st}$ , we can rewrite the upper bound as follows:

$$
\log \hat{Z}(\boldsymbol{\theta}) \triangleq \boldsymbol{\tau}^T \boldsymbol{\theta} + \sum_{st} \rho_{st} H_{st}(\tau_{st}) + \sum_s c_s H_s(\tau_s) \le \log Z(\boldsymbol{\theta}) \tag{22.64}
$$

where  $c_s \triangleq 1 - \sum_t \rho_{st}$ .

## **22.5 Expectation propagation**

**Expectation propagation** (EP) (Minka 2001c) is a form of belief propagation where the messages are approximated. It is a generalization of the assumed density filtering (ADF) algorithm, discussed in Section 18.5.3. In that method, we approximated the posterior at each step using an assumed functional form, such as a Gaussian. This posterior can be computed using moment matching, which locally optimizes  $\mathbb{KL}(p||q)$  for a single term. From this, we derived the message to send to the next time step.

ADF works well for sequential Bayesian updating, but the answer it gives depends on the order in which the data is seen. EP essentially corrects this flaw by making multiple passes over the data (thus EP is an offline or batch inference algorithm).

## **22.5.1 EP as a variational inference problem**

We now explain how to view EP in terms of variational inference. We follow the presentation of (Wainwright and Jordan 2008b, Sec 4.3), which should be consulted for further details.

Suppose the joint distribution can be written in exponential family form as follows:

$$
p(\mathbf{x}|\boldsymbol{\theta},\tilde{\boldsymbol{\theta}}) \propto f_0(\mathbf{x}) \exp(\boldsymbol{\theta}^T \boldsymbol{\phi}(\mathbf{x})) \prod_{i=1}^{d_I} \exp(\tilde{\boldsymbol{\theta}}_i^T \boldsymbol{\Phi}_i(\mathbf{x}))
$$
\n(22.65)

where we have partitioned the parameters and the sufficient statistics into a tractable term *θ* of size  $d_T$  and  $d_I$  intractable terms  $\theta_i$ , each of size b.

For example, consider the problem of inferring an unknown vector **x**, when the observation model is a mixture of two Gaussians, one centered at **x** and one centered at **0**. (This can be used to represent outliers, for example.) Minka (who invented EP) calls this the **clutter problem**. More formally, we assume an observation model of the form

$$
p(\mathbf{y}|\mathbf{x}) = (1 - w)\mathcal{N}(\mathbf{y}|\mathbf{x}, \mathbf{I}) + w\mathcal{N}(\mathbf{y}|\mathbf{0}, a\mathbf{I})
$$
\n(22.66)

where  $0 < w < 1$  is the known mixing weight (fraction of outliers), and  $a > 0$  is the variance of the background distribution. Assuming a fixed prior of the form  $p(x) = \mathcal{N}(x|0, \Sigma)$ , we can write our model in the required form as follows:

$$
p(\mathbf{x}|\mathbf{y}_{1:N}) \propto \mathcal{N}(\mathbf{x}|\mathbf{0}, \boldsymbol{\Sigma}) \prod_{i=1}^{N} p(\mathbf{y}_i|\mathbf{x})
$$
\n(22.67)

$$
= \exp\left(-\frac{1}{2}\mathbf{x}^T\mathbf{\Sigma}^{-1}\mathbf{x}\right)\exp\left(\sum_{i=1}^N\log p(\mathbf{y}_i|\mathbf{x})\right) \tag{22.68}
$$

This matches our canonical form where  $f_0(\mathbf{x}) \exp(\boldsymbol{\theta}^T \boldsymbol{\phi}(\mathbf{x}))$  corresponds to  $\exp\left(-\frac{1}{2}\mathbf{x}^T \boldsymbol{\Sigma}^{-1} \mathbf{x}\right)$ , using  $\phi(\mathbf{x})=(\mathbf{x}, \mathbf{x}\mathbf{x}^T)$ , and we set  $\mathbf{\Phi}_i(\mathbf{x}) = \log p(\mathbf{v}_i|\mathbf{x})$ ,  $\tilde{\theta}_i = 1$ , and  $d_I = N$ . The exact inference problem corresponds to

The exact inference problem corresponds to

$$
\max_{(\tau,\tilde{\tau}) \in \mathcal{M}(\phi,\Phi)} \tau^T \theta + \tilde{\tau}^T \tilde{\theta} + \mathbb{H}\left((\tau,\tilde{\tau})\right)
$$
\n(22.69)

where  $\mathcal{M}(\phi, \Phi)$  is the set of mean parameters realizable by any probability distribution as seen through the eyes of the sufficient statistics:

$$
\mathcal{M}(\boldsymbol{\phi},\boldsymbol{\Phi}) = \{(\boldsymbol{\mu},\tilde{\boldsymbol{\mu}}) \in \mathbb{R}^{d_T} \times \mathbb{R}^{d_I b} : (\boldsymbol{\mu},\tilde{\boldsymbol{\mu}}) = \mathbb{E}\left[ (\boldsymbol{\phi}(\mathbf{X}),\boldsymbol{\Phi}_1(\mathbf{X}),\ldots,\boldsymbol{\Phi}_{d_I}(\mathbf{X})) \right]\} \tag{22.70}
$$

As it stands, it is intractable to perform inference in this distribution. For example, in our clutter example, the posterior contains  $2^N$  modes. But suppose we incorporate just one of the intractable terms, say the i'th one; we will call this the  $\Phi_i$ -augmented distribution:

$$
p(\mathbf{x}|\boldsymbol{\theta},\tilde{\boldsymbol{\theta}}_i) \propto f_0(\mathbf{x}) \exp(\boldsymbol{\theta}^T \boldsymbol{\phi}(\mathbf{x})) \exp(\tilde{\boldsymbol{\theta}}_i^T \boldsymbol{\Phi}_i(\mathbf{x}))
$$
\n(22.71)

In our clutter example, this becomes

$$
p(\mathbf{x}|\boldsymbol{\theta},\tilde{\boldsymbol{\theta}}_i) = \exp\left(-\frac{1}{2}\mathbf{x}^T\mathbf{\Sigma}^{-1}\mathbf{x}\right)[w\mathcal{N}(\mathbf{y}_i|\mathbf{0},a\mathbf{I}) + (1-w)\mathcal{N}(\mathbf{y}_i|\mathbf{x},\mathbf{I})] \tag{22.72}
$$

This *is* tractable to compute, since it is just a mixture of 2 Gaussians.

The key idea behind EP is to work with these the  $\Phi_i$ -augmented distributions in an iterative fashion. First, we approximate the convex set  $\mathcal{M}(\phi, \Phi)$  with another, larger convex set:

$$
\mathcal{L}(\phi, \Phi) \triangleq \{(\tau, \tilde{\tau}) : \tau \in \mathcal{M}(\phi), (\tau, \tilde{\tau}_i) \in \mathcal{M}(\phi, \Phi_i)\}\
$$
\n(22.73)

where  $\mathcal{M}(\phi) = {\mu \in \mathbb{R}^{d_T} : \mu = \mathbb{E}[\phi(\mathbf{X})]}$  and  $\mathcal{M}(\phi, \Phi_i) = {\mu, \tilde{\mu}_i} \in \mathbb{R}^{d_T} \times \mathbb{R}^b$ .  $(\mu, \tilde{\mu}_i) = \mathbb{E}[(\phi(\mathbf{X}), \Phi_i(\mathbf{X}))]$ . Next we approximate the entropy by the following term-by-term approximation:

$$
\mathbb{H}_{ep}(\tau,\tilde{\tau}) \triangleq \mathbb{H}(\tau) + \sum_{i=1}^{d_{I}} \left[ \mathbb{H}(\tau,\tilde{\tau}_{i}) - \mathbb{H}(\tau) \right]
$$
\n(22.74)

Then the EP problem becomes

$$
\max_{(\tau,\tilde{\tau})\in\mathcal{L}(\phi,\Phi)} \tau^T \theta + \tilde{\tau}^T \tilde{\theta} + \mathbb{H}_{\text{ep}}(\tau,\tilde{\tau})
$$
\n(22.75)

## **22.5.2 Optimizing the EP objective using moment matching**

We now discuss how to maximize the EP objective in Equation 22.75. Let us duplicate  $\tau d_I$ times to yield  $\eta_i = \tau$ . The augmented set of parameters we need to optimize is now

$$
(\boldsymbol{\tau},(\boldsymbol{\eta}_i,\tilde{\boldsymbol{\tau}}_i)_{i=1}^{d_I}) \in \mathbb{R}^{d_T} \times (\mathbb{R}^{d_T} \times \mathbb{R}^b)^{d_I}
$$
\n(22.76)

subject to the constraints that  $\eta_i = \tau$  and  $(\eta_i, \tilde{\tau}_i) \in \mathcal{M}(\phi; \Phi_i)$ . Let us associate a vector of Lagrange multipliers  $\lambda_i \in \mathbb{R}^{d_T}$  with the first set of constraints. Then the partial Lagrangian becomes

$$
L(\boldsymbol{\tau}; \boldsymbol{\lambda}) = \boldsymbol{\tau}^T \boldsymbol{\theta} + \mathbb{H}(\boldsymbol{\tau}) + \sum_{i=1}^{d_i} \left[ \tilde{\boldsymbol{\tau}}_i^T \tilde{\boldsymbol{\theta}}_i + \mathbb{H}((\boldsymbol{\eta}_i, \tilde{\boldsymbol{\tau}}_i)) - \mathbb{H}(\boldsymbol{\eta}_i) + \boldsymbol{\lambda}_i^T(\boldsymbol{\tau} - \boldsymbol{\eta}_i) \right]
$$
(22.77)

By solving  $\nabla_{\tau}L(\tau;\lambda) = 0$ , we can show that the corresponding distribution in  $\mathcal{M}(\phi)$  has the form

$$
q(\mathbf{x}|\boldsymbol{\theta}, \boldsymbol{\lambda}) \propto f_0(\mathbf{x}) \exp\{(\boldsymbol{\theta} + \sum_{i=1}^{d_I} \boldsymbol{\lambda}_i)^T \boldsymbol{\phi}(\mathbf{x})\}
$$
 (22.78)

The  $\lambda_i^T \phi(\mathbf{x})$  terms represents an approximation to the *i*'th intractable term using the sufficient statistics from the base distribution, as we will see below. Similarly, by solving  $\nabla_{(\eta_i,\tilde{\tau}_i)}L(\tau;\lambda)$ **0**, we find that the corresponding distribution in  $\mathcal{M}(\phi, \Phi_i)$  has the form

$$
q_i(\mathbf{x}|\boldsymbol{\theta}, \tilde{\boldsymbol{\theta}}_i, \boldsymbol{\lambda}) \propto f_0(\mathbf{x}) \exp\{(\boldsymbol{\theta} + \sum_{j \neq i} \boldsymbol{\lambda}_j)^T \boldsymbol{\phi}(\mathbf{x}) + \tilde{\boldsymbol{\theta}}_i^T \boldsymbol{\Phi}_i(\mathbf{x})\}
$$
(22.79)

This corresponds to removing the approximation to the *i*'th term,  $\lambda_i$ , from the base distribution, and adding in the correct *i*'th term,  $\Phi_i$ . Finally,  $\nabla_{\lambda} L(\tau; \lambda) = 0$  just enforces the constraints that  $\tau = \mathbb{E}_q [\phi(\mathbf{X})]$  and  $\eta_i = \mathbb{E}_{q_i} [\phi(\mathbf{X})]$  are equal. In other words, we get the following moment matching constraints:

$$
\int q(\mathbf{x}|\boldsymbol{\theta}, \boldsymbol{\lambda}) \phi(\mathbf{x}) d\mathbf{x} = \int q_i(\mathbf{x}|\boldsymbol{\theta}, \tilde{\boldsymbol{\theta}}_i, \boldsymbol{\lambda}) \phi(\mathbf{x}) d\mathbf{x}
$$
\n(22.80)

Thus the overall algorithm is as follows. First we initialize the  $\lambda_i$ . Then we iterate the following to convergence: pick a term i; compute  $q_i$  (corresponding to removing the old approximation to  $\Phi_i$  and adding in the new one); then update the  $\lambda_i$  term in q by solving the moment matching equation  $\mathbb{E}_{q_i} [\phi(\mathbf{X})] = \mathbb{E}_q [\phi(\mathbf{X})]$ . (Note that this particular optimization scheme is not guaranteed to converge to a fixed point.)

An equivalent way of stating the algorithm is as follows. Let us assume the true distribution is given by

$$
p(\mathbf{x}|\mathcal{D}) = \frac{1}{Z} \prod_i f_i(\mathbf{x})
$$
\n(22.81)

We approximate each  $f_i$  by  $\tilde{f}_i$  and set

$$
q(\mathbf{x}) = \frac{1}{Z} \prod_{i} \tilde{f}_i(\mathbf{x})
$$
\n(22.82)

Now we repeat the following until convergence:

- 1. Choose a factor  $\tilde{f}_i$  to refine.
- 2. Remove  $\tilde{f}_i$  from the posterior by dividing it out:

$$
q_{-i}(\mathbf{x}) = \frac{q(\mathbf{x})}{\tilde{f}_i(\mathbf{x})}
$$
(22.83)

This can be implemented by substracting off the natural parameters of  $\tilde{f}_i$  from q.

3. Compute the new posterior  $q^{new}(\mathbf{x})$  by solving

$$
\min_{q^{new}(\mathbf{x})} \mathbb{KL}\left(\frac{1}{Z_i} f_i(\mathbf{x}) q_{-i}(\mathbf{x}) || q^{new}(\mathbf{x})\right) \tag{22.84}
$$

This can be done by equating the moments of  $q^{new}(\mathbf{x})$  with those of  $q_i(\mathbf{x}) \propto q_{-i}(\mathbf{x})f_i(\mathbf{x})$ . The corresponding normalization constant has the form

$$
Z_i = \int q_{-i}(\mathbf{x}) f_i(\mathbf{x}) d\mathbf{x}
$$
\n(22.85)

4. Compute the new factor (message) that was implicitly used (so it can be later removed):

$$
\tilde{f}_i(\mathbf{x}) = Z_i \frac{q^{new}(\mathbf{x})}{q_{-i}(\mathbf{x})}
$$
\n(22.86)

After convergence, we can approximate the marginal likelihood using

$$
p(\mathcal{D}) \approx \int \prod_i \tilde{f}_i(\mathbf{x}) d\mathbf{x}
$$
 (22.87)

We will give some examples of this below which will make things clearer.

## **22.5.3 EP for the clutter problem**

Let us return to considering the clutter problem. Our presentation is based on (Bishop 2006b).<sup>4</sup> For simplicity, we will assume that the prior is a spherical Gaussian,  $p(\mathbf{x}) = \mathcal{N}(\mathbf{0}, b\mathbf{I})$ . Also, we choose to approximate the posterior by a spherical Gaussian,  $q(\mathbf{x}) = \mathcal{N}(\mathbf{m}, v\mathbf{I})$ . We set  $f_0(\mathbf{x})$ to be the prior; this can be held fixed. The factor approximations will be "Gaussian like" terms of the form

$$
\tilde{f}_i(\mathbf{x}) = s_i \mathcal{N}(\mathbf{x}|\mathbf{m}_i, v_i \mathbf{I})
$$
\n(22.88)

Note, however, that in the EP updates, the variances may be negative! Thus these terms should be interpreted as functions, but not necessarily probability distributions. (If the variance is negative, it means the that  $f_i$  curves upwards instead of downwards.)

First we remove  $\hat{f}_i(\mathbf{x})$  from  $q(\mathbf{x})$  by division, which yields  $q_{-i}(\mathbf{x}) = \mathcal{N}(\mathbf{m}_{-i}, v_{-i})$ , where

$$
v_{-i}^{-1} = v^{-1} - v_i^{-1} \tag{22.89}
$$

$$
\mathbf{m}_{-i} = \mathbf{m} + v_{-i} v_i^{-1} (\mathbf{m} - \mathbf{m}_i)
$$
 (22.90)

The normalization constant is given by

$$
Z_i = (1 - w)\mathcal{N}(\mathbf{y}_i|\mathbf{m}_{-i}, (v_{-i} + 1)\mathbf{I}) + w\mathcal{N}(\mathbf{y}_i|\mathbf{0}, a\mathbf{I})
$$
\n(22.91)

Next we compute  $q^{new}(\mathbf{x})$  by computing the mean and variance of  $q-i(\mathbf{x})f_i(\mathbf{x})$  as follows:

$$
\mathbf{m} = \mathbf{m}_{-i} + \rho_i \frac{v_{-i}}{v_{-i} + 1} (\mathbf{y}_i - \mathbf{m}_{-i})
$$
 (22.92)

$$
v = v_{-i} - \rho_i \frac{v_{-i}^2}{v_{-i} + 1} + \rho_i (1 - \rho_i) \frac{v_{-i}^2 ||\mathbf{y}_i - \mathbf{m}_i||^2}{D(v_{-i} + 1)^2}
$$
(22.93)

$$
\rho_i = 1 - \frac{w}{Z_i} \mathcal{N}(\mathbf{y}_i | \mathbf{0}, a\mathbf{I}) \tag{22.94}
$$

where D is the dimensionality of **x** and  $\rho_i$  can be interpreted as the probability that  $\mathbf{y}_i$  is not clutter.

Finally, we compute the new factor  $\tilde{f}_i$  whose parameters are given by

$$
v_i^{-1} = v^{-1} - v_{-i}^{-1} \tag{22.95}
$$

$$
\mathbf{m}_{i} = \mathbf{m}_{-i} + (v_{i} + v_{-i})v_{-i}^{-1}(\mathbf{m} - \mathbf{m}_{-i})
$$
\n(22.96)

$$
s_i = \frac{Z_i}{(2\pi v_i)^{D/2} \mathcal{N}(\mathbf{m}_i | \mathbf{m}_{-i}, (v_i + v_{-i}) \mathbf{I})}
$$
(22.97)

<sup>4.</sup> For a handy "crib sheet", containing many of the standard equations needed for deriving Gaussian EP algorithms, see http://research.microsoft.com/en-us/um/people/minka/papers/ep/minka-ep-quickref.pdf.

At convergence, we can approximate the marginal likelihood as follows:

$$
p(\mathcal{D}) \approx (2\pi v)^{D/2} \exp(c/2) \prod_{i=1}^{N} s_i (2\pi v_i)^{-D/2}
$$
\n(22.98)

$$
c \triangleq \frac{\mathbf{m}^T \mathbf{m}}{v} - \sum_{i=1}^N \frac{\mathbf{m}_i^T \mathbf{m}_i}{v_i}
$$
 (22.99)

In (Minka 2001d), it is shown that, at least on this example, EP gives better accuracy per unit of CPU time than VB and MCMC.

## **22.5.4 LBP is a special case of EP**

We now show that loopy belief propagation is a special case of EP, where the base distribution contains the node marginals and the "intractable" terms correspond to the edge potentials. We assume the model has the pairwise form shown in Equation 22.12. If there are  $m$  nodes, the base distribution takes the form

$$
p(\mathbf{x}|\boldsymbol{\theta}_1,\ldots,\boldsymbol{\theta}_m,\mathbf{0}) \propto \prod_{s \in V} \exp(\theta_s(x_s))
$$
\n(22.100)

The entropy of this distribution is simply

$$
\mathbb{H}\left(\boldsymbol{\tau}_{1:m}\right) = \sum_{s} \mathbb{H}\left(\boldsymbol{\tau}_{s}\right) \tag{22.101}
$$

If we add in the  $u - v$  edge, the  $\Phi_{uv}$  augmented distribution has the form

$$
p(\mathbf{x}|\boldsymbol{\theta}_{1:m},\boldsymbol{\theta}_{uv}) \propto \left[\prod_{s \in V} \exp(\theta_s(x_s))\right] \exp(\theta_{uv}(x_u,x_v)) \tag{22.102}
$$

Since this graph is a tree, the exact entropy of this distribution is given by

$$
\mathbb{H}(\tau_{1:m}, \tilde{\tau}_{uv}) = \sum_{s} \mathbb{H}(\tau_s) - I(\tilde{\tau}_{uv})
$$
\n(22.103)

where  $I(\tau_{uv}) = \mathbb{H}(\tau_u) + \mathbb{H}(\tau_v) - \mathbb{H}(\tau_{uv})$  is the mutual information. Thus the EP approximation to the entropy of the full distribution is given by

$$
\mathbb{H}_{\text{ep}}(\tau,\tilde{\tau}) = \mathbb{H}(\tau) + \sum_{(u,v)\in E} [\mathbb{H}(\tau_{1:m},\tilde{\tau}_{uv}) - \mathbb{H}(\tau)] \qquad (22.104)
$$

$$
= \sum_{s} \mathbb{H}(\boldsymbol{\tau}_{s}) + \sum_{(u,v)\in E} \left[ \sum_{s} \mathbb{H}(\boldsymbol{\tau}_{s}) - I(\tilde{\boldsymbol{\tau}}_{uv}) - \sum_{s} \mathbb{H}(\boldsymbol{\tau}_{s}) \right]
$$
(22.105)

$$
= \sum_{s} \mathbb{H}(\boldsymbol{\tau}_{s}) - \sum_{(u,v) \in E} I(\tilde{\boldsymbol{\tau}}_{uv}) \tag{22.106}
$$

which is precisely the Bethe approximation to the entropy.

We now show that the convex set that EP is optimizing over,  $\mathcal{L}(\phi, \Phi)$  given by Equation 22.73, is the same as the one that LBP is optimizing over,  $\mathbb{L}(G)$  given in Equation 22.33. First, let us consider the set  $\mathcal{M}(\phi)$ . This consists of all marginal distributions ( $\tau_s$ ,  $s \in V$ ), realizable by a factored distribution. This is therefore equivalent to the set of all distributions which satisfy non-negativity  $\tau_s(x_s) \geq 0$  and the local normalization constraint  $\sum_{x_s} \tau(x_s) = 1$ . Now consider the set  $\mathcal{M}(\phi, \Phi_{uv})$  for a single  $u-v$  edge. This is equivalent to the marginal polytope  $\mathbb{M}(G_{uv})$ , where  $G_{uv}$  is the graph with the single  $u - v$  edge added. Since this graph corresponds to a tree, this set also satisfies the marginalization conditions

$$
\sum_{x_v} \tau_{uv}(x_u, x_v) = \tau_u(x_u), \ \sum_{x_u} \tau_{uv}(x_u, x_v) = \tau_v(x_v)
$$
\n(22.107)

Since  $\mathcal{L}(\phi, \Phi)$  is the union of such sets, as we sweep over all edges in the graph, we recover the same set as  $L(G)$ .

We have shown that the Bethe approximation is equivalent to the EP approximation. We now show how the EP algorithm reduces to LBP. Associated with each intractable term  $i = (u, v)$ will be a pair of Lagrange multipliers,  $(\lambda_{uv}(x_v), \lambda_{vu}(x_u))$ . Recalling that  $\theta^T \phi(\mathbf{x}) = [\theta_s(x_s)]_s$ , the base distribution in Equation 22.78 has the form

$$
q(\mathbf{x}|\boldsymbol{\theta}, \boldsymbol{\lambda}) \propto \prod_{s} \exp(\theta_s(x_s)) \prod_{(u,v) \in E} \exp(\lambda_{uv}(x_v) + \lambda_{vu}(x_u)) \tag{22.108}
$$

$$
= \prod_{s} \exp\left(\theta_{s}(x_{s}) + \sum_{t \in \mathcal{N}(s)} \lambda_{ts}(x_{s})\right) \tag{22.109}
$$

Similarly, the augmented distribution in Equation 22.79 has the form

$$
q_{uv}(\mathbf{x}|\boldsymbol{\theta},\boldsymbol{\lambda}) \propto q(\mathbf{x}|\boldsymbol{\theta},\boldsymbol{\lambda}) \exp (\theta_{uv}(x_u,x_v) - \lambda_{uv}(x_v) - \lambda_{vu}(x_u)) \tag{22.110}
$$

We now need to update  $\tau_u(x_u)$  and  $\tau_v(x_v)$  to enforce the moment matching constraints:

$$
\left(\mathbb{E}_{q}\left[x_{s}\right],\mathbb{E}_{q}\left[x_{t}\right]\right) = \left(\mathbb{E}_{q_{uv}}\left[x_{s}\right],\mathbb{E}_{q_{uv}}\left[x_{t}\right]\right) \tag{22.11}
$$

It can be shown that this can be done by performing the usual sum-product message passing step along the  $u - v$  edge (in both directions), where the messages are given by  $M_{uv}(x_v) =$  $\exp(\lambda_{uv}(x_v))$ , and  $M_{vu}(x_u) = \exp(\lambda_{vu}(x_u))$ . Once we have updated q, we can derive the corresponding messages  $\lambda_{uv}$  and  $\lambda_{vu}$ .

The above analysis suggests a natural extension, where we make the base distribution be a tree structure instead of a fully factored distribution. We then add in one edge at a time, absorb its effect, and approximate the resulting distribution by a new tree. This is known as **tree EP** (Minka and Qi 2003), and is more accurate than LBP, and sometimes faster. By considering other kinds of structured base distributions, we can derive algorothms that outperform generalization belief propagation (Welling et al. 2005).

## **22.5.5 Ranking players using TrueSkill**

We now present an interesting application of EP to the problem of ranking players who compete in games. Microsoft uses this method — known as **TrueSkill** (Herbrich et al. 2007) — to rank

Image /page/27/Figure/1 description: The image displays two diagrams, labeled (a) and (b), illustrating the TrueSkill model. Diagram (a) shows a Bayesian network with nodes representing skills (S1-S4), player abilities (P1-P4), intermediate variables (t1-t3, d1-d2), and observed outcomes (y1, y2). Diagram (b) presents a more detailed graphical model with nodes representing factors (F1-F3), skills (S1-S3), intermediate variables (h1-h2, d1-d2, k1-k2), and observed outcomes (y1, y2). Arrows indicate dependencies between nodes, and numbered blue circles in diagram (b) suggest a sequence of updates or calculations within the model.

**Figure 22.11** (a) A DGM representing the TrueSkill model for 4 players and 3 teams, where team 1 is player 1, team 2 is players 2 and 3, and team 3 is player 4. We assume there are two games, team 1 vs team 2, and team 2 vs team 3. Nodes with double circles are deterministic. (b) A factor graph representation of the model where we assume there are 3 players (and no teams). There are 2 games, player 1 vs player 2, and player 2 vs player 3. The numbers inside circles represent steps in the message passing algorithm.

players who use the Xbox 360 Live online gaming system; this system process over  $10^5$  games per day, making this one of the largest application of Bayesian statistics to date.<sup>5</sup> The same method can also be applied to other games, such as tennis or chess.<sup>6</sup>

The basic idea is shown in Figure 22.11(a). We assume each player i has a latent or true underlying skill level  $s_i \in \mathbb{R}$ . These skill levels can evolve over time according to a simple dynamical model,  $p(s_i^t | s_i^{t-1}) = \mathcal{N}(s_i^t | s_i^{t-1}, \gamma^2)$ . In any given game, we define the performance of player i to be  $p_i$ , which has the conditional distribution  $p(p_i|s_i) = \mathcal{N}(p_i|s_i, \beta^2)$ . We then define the performance of a team to be the sum of the performance of its constituent players. For example, in Figure 22.11(a), we assume team 2 is composed of players 2 and 3, so we define  $t_2 = p_2 + p_3$ . Finally, we assume that the outcome of a game depends on the difference in performance levels of the two teams. For example, in Figure 22.11(a), we assume  $y_1 = \text{sign}(d_1)$ , where  $d_1 = t_1 - t_2$ , and where  $y_1 = +1$  means team 1 won, and  $y_1 = -1$  means team 2 won. Thus the prior probability that team 1 wins is

$$
p(y_1 = +1|\mathbf{s}) = \int p(d_1 > 0|t_1, t_2)p(t_1|s_1)p(t_2|s_2)dt_1dt_2
$$
\n(22.112)

where  $t_1 \sim \mathcal{N}(s_1, \beta^2)$  and  $t_2 \sim \mathcal{N}(s_2 + s_3, \beta^2).$ <sup>7</sup>

To simplify the presentation of the algorithm, we will ignore the dynamical model and assume a common static factored Gaussian prior,  $\mathcal{N}(\mu_0, \sigma_0^2)$ , on the skills. Also, we will assume that each team consists of 1 player, so  $t_i = p_i$ , and that there can be no ties. Finally, we will integrate out the performance variables  $p_i$ , and assume  $\beta^2 = 1$ , leading to a final model of the form

$$
p(\mathbf{s}) = \prod_{i} \mathcal{N}(s_i | \mu_0, \sigma^2)
$$
\n(22.113)

$$
p(d_g|\mathbf{s}) = \mathcal{N}(d_g|s_{i_g} - s_{j_g}, 1) \tag{22.114}
$$

$$
p(y_g|d_g) = \mathbb{I}(y_g = \text{sign}(d_g)) \tag{22.115}
$$

where  $i_g$  is the first player of game g, and  $j_g$  is the second player. This is represented in factor graph form in in Figure 22.11(b). We have 3 kinds of factors: the prior factor,  $f_i(s_i)$  =  $\mathcal{N}(s_i|\mu_0, \sigma_0^2)$ , the game factor,  $h_g(s_{i_g}, s_{j_g}, d_g) = \mathcal{N}(d_g|s_{i_g} - s_{j_g}, 1)$ , and the outcome factor,  $k_q(d_q, y_q) = \mathbb{I}(y_q = sign(d_q)).$ 

Since the likelihood term  $(y_q|d_q)$  is not conjugate to the Gaussian priors, we will have to perform approximate inference. Thus even when the graph is a tree, we will need to iterate. (If there were an additional game, say between player 1 and player 3, then the graph would no longer be a tree.) We will represent all messages and marginal beliefs by 1d Gaussians. We will use the notation  $\mu$  and v for the mean and variance (the moment parameters), and  $\lambda = 1/v$ and  $\eta = \lambda \mu$  for the precision and precision-adjusted mean (the natural parameters).

<sup>5.</sup> Naive Bayes classifiers, which are widely used in spam filters, are often described as the most common application of Bayesian methods. However, the parameters of such models are usually fit using non-Bayesian methods, such as penalized maximum likelihood.

<sup>6.</sup> Our presentation of this algorithm is based in part on lecture notes by Carl Rasmussen Joaquin Quinonero-Candela, available at http://mlg.eng.cam.ac.uk/teaching/4f13/1112/lect13.pdf.

<sup>7.</sup> Note that this is very similar to probit regression, discussed in Section 9.4, except the inputs are (the differences of) latent 1 dimensional factors. If we assume a logistic noise model instead of a Gaussian noise model, we recover the Bradley Terry model of ranking.

We initialize by assuming that at iteration 0, the initial upward messages from factors  $h_q$  to variables  $s_i$  are uniform, i.e.,

$$
m_{h_g \to s_{i_g}}^0(s_{i_g}) = 1, \ \lambda_{h_g \to s_{i_g}}^0 = 0, \ \eta_{h_g \to s_{i_g}}^0 = 0 \tag{22.116}
$$

and similarly  $m_{h_g\to s_{j_g}}^0(s_{j_g})=1$ . The messages passing algorithm consists of 6 steps per game, as illustrated in Figure 22.11(b). We give the details of these steps below.

1. Compute the posterior over the skills variables:

$$
q^t(s_i) = f(s_i) \prod_q m_{h_g \to s_i}^{t-1}(s_i) = \mathcal{N}_c(s_i | \eta_i^t, \lambda_i^t)
$$
\n(22.117)

$$
\lambda_i^t = \lambda_0 + \sum_g \lambda_{h_g \to s_i}^{t-1}, \quad \eta_i^t = \eta_0 + \sum_g \eta_{h_g \to s_i}^{t-1}
$$
\n(22.118)

2. Compute the message from the skills variables down to the game factor  $h_q$ :

$$
m_{s_{ig}\to h_g}^t(s_{i_g}) = \frac{q^t(s_{i_g})}{m_{h_g \to s_{i_g}}^t(s_{i_g})}, \quad m_{s_{j_g} \to h_g}^t(s_{j_g}) = \frac{q^t(s_{j_g})}{m_{h_g \to s_{j_g}}^t(s_{j_g})}
$$
(22.119)

where the division is implemented by subtracting the natural parameters as follows:

$$
\lambda_{s_{ig}\to h_g}^t = \lambda_{s_{ig}}^t - \lambda_{h_g \to s_{ig}}^t, \ \ \eta_{s_{ig}\to h_g}^t = \eta_{s_{ig}}^t - \eta_{h_g \to s_{ig}}^t \tag{22.120}
$$

and similarly for  $s_{j_q}$ .

3. Compute the message from the game factor  $h_g$  down to the difference variable  $d_g$ :

$$
m_{h_g \to d_g}^t(d_g) = \int \int h_g(d_g, s_{i_g}, s_{j_g}) m_{s_{i_g} \to h_g}^t(s_{i_g}) m_{s_{j_g} \to h_g}^t(s_{j_g}) ds_{i_g} ds_{j_g}
$$
 (22.121)

$$
= \int \int \mathcal{N}(d_g | s_{i_g} - s_{j_g}, 1) \mathcal{N}(s_{i_g} | \mu^t_{s_{i_g} \to h_g}, v^t_{s_{i_g} \to h_g}) \tag{22.122}
$$

$$
\mathcal{N}(s_{j_g}|\mu^t_{s_{j_g}\to h_g}, v^t_{s_{j_g}\to h_g}) ds_{i_g} ds_{j_g} \tag{22.123}
$$

$$
= \mathcal{N}(d_g | \mu^t_{h_g \to d_g}, v^t_{h_g \to d_g}) \tag{22.124}
$$

$$
v_{h_g \to d_g}^t = 1 + v_{s_{i_g} \to h_g}^t + v_{s_{j_g} \to h_g}^t \tag{22.125}
$$

$$
\mu_{h_g \to d_g}^t = \mu_{s_{i_g} \to h_g}^t - \mu_{s_{j_g} \to h_g}^t \tag{22.126}
$$

4. Compute the posterior over the difference variables:

$$
q^t(d_g) \propto m^t_{h_g \to d_g}(d_g) m_{k_g \to d_g}(d_g) \tag{22.127}
$$

$$
= \mathcal{N}(d_g | \mu_{h_g \to d_g}^t, v_{h_g \to d_g}^t) \mathbb{I}(y_g = \text{sign}(d_g)) \tag{22.128}
$$

$$
\approx \mathcal{N}(d_g|\mu_g^t, v_g^t) \tag{22.129}
$$

Image /page/30/Figure/1 description: The image displays two plots side-by-side. Plot (a), titled "Ψ function", shows a curve that starts at approximately y=6.2 at x=-6 and decreases linearly to approximately y=0.2 at x=2, then flattens out to y=0 for x>2. The x-axis ranges from -6 to 6, and the y-axis ranges from 0 to 7. Plot (b), titled "Λ function", shows a curve that starts at y=1 at x=-6 and decreases non-linearly, becoming steeper around x=-2 and flattening out to y=0 for x>3. The x-axis ranges from -6 to 6, and the y-axis ranges from 0 to 1.

**Figure 22.12** (a)  $\Psi$  function. (b)  $\Lambda$  function. Based on Figure 2 of (Herbrich et al. 2007). Figure generated by trueskillPlot.

(Note that the upward message from the  $k_q$  factor is constant.) We can find these parameters by moment matching as follows:

$$
\mu_g^t = y_g \mu_{h_g \to d_g}^t + \sigma_{h_g \to d_g}^t \Psi \left( \frac{y_g \mu_{h_g \to d_g}^t}{\sigma_{h_g \to d_g}^t} \right) \tag{22.130}
$$

$$
v_g^t = v_{h_g \to d_g}^t \left[ 1 - \Lambda \left( \frac{y_g \mu_{h_g \to d_g}^t}{\sigma_{h_g \to d_g}^t} \right) \right]
$$
 (22.131)

$$
\Psi(x) \triangleq \frac{\mathcal{N}(x|0,1)}{\Phi(x)} \tag{22.132}
$$

$$
\Lambda(x) \triangleq \Psi(x)(\Psi(x) + x) \tag{22.133}
$$

(The derivation of these equations is left as a modification to Exercise 11.15.) These functions are plotted in Figure 22.12. Let us try to understand these equations. Suppose  $\mu_{h,g\to dg}^t$  is a large positive number. That means we expect, based on the current estimate of the skills, that  $d_g$  will be large and positive. Consequently, if we observe  $y_g = +1$ , we will not be surprised that  $i_g$  is the winner, which is reflected in the fact that the update factor for the mean is small,  $\Psi(y_g\mu_{h_g\to d_g}^t) \approx 0$ . Similarly, the update factor for the variance is small,  $\Lambda(y_g\mu_{h_g\to d_g}^t) \approx 0$ . However, if we observe  $y_g = -1$ , then the update factor for the mean and variance becomes quite large.

5. Compute the upward message from the difference variable to the game factor  $h_g$ :

$$
m_{d_g \to h_g}^t(d_g) = \frac{q^t(d_g)}{m_{d_g \to h_g}^t(d_g)} \tag{22.134}
$$

$$
\lambda_{d_g \to h_h}^t = \lambda_g^t - \lambda_{h_g \to d_g}^t, \quad \eta_{d_g \to h_h}^t = \eta_g^t - \eta_{h_g \to d_g}^t \tag{22.135}
$$

6. Compute the upward messages from the game factor to the skill variables. Let us assume

Image /page/31/Figure/1 description: The image displays two figures, labeled (a) and (b). Figure (a) is a directed acyclic graph with six nodes labeled 1 through 6. Node 1 has directed edges pointing to nodes 2 and 3. Node 2 has a directed edge pointing to node 4. Node 3 has a directed edge pointing to node 4. Node 4 has directed edges pointing to nodes 5 and 6. Figure (b) is a line graph with error bars. The x-axis ranges from 0 to 7, and the y-axis ranges from -2 to 2. The line graph shows a decreasing trend. The data points and their corresponding error bars are approximately at (1, 1.7), (2, 0.5), (3, 0.3), (4, -0.3), (5, -1.2), and (6, -0.7). The error bars extend approximately 0.3 units above and below each data point.

**Figure 22.13** (a) A DAG representing a partial ordering of players. (b) Posterior mean plus/minus 1 standard deviation for the latent skills of each player based on 26 games. Figure generated by trueskillDemo.

that  $i_g$  is the winner, and  $j_g$  is the loser. Then we have

$$
m_{h_g \to s_{i_g}}^t(s_{i_g}) = \int \int h_g(d_g, s_{i_g}, s_{j_g}) m_{d_g \to h_g}^t(d_g) m_{s_{j_g} \to h_g}^t(s_{j_g}) d d_g ds_{j_g}
$$
 (22.136)

$$
= \mathcal{N}(s_{i_g}|\mu^t_{h_g \to s_{i_g}}, v^t_{h_g \to s_{i_g}}) \tag{22.137}
$$

$$
v_{h_g \to s_{ig}}^t = 1 + v_{d_g \to h_g}^t + v_{s_{j_g} \to h_g}^t
$$
\n(22.138)

$$
\mu_{h_g \to s_{i_g}}^t = \mu_{d_g \to h_g}^t + \mu_{s_{j_g} \to h_g}^t \tag{22.139}
$$

And similarly

$$
m_{h_g \to s_{j_g}}^t(s_{j_g}) = \int \int h_g(d_g, s_{i_g}, s_{j_g}) m_{d_g \to h_g}^t(d_g) m_{s_{i_g} \to h_g}^t(s_{i_g}) d d_g ds_{i_g}
$$
 (22.140)

$$
= \mathcal{N}(s_{j_g}|\mu^t_{h_g \to s_{j_g}}, v^t_{h_g \to s_{j_g}}) \tag{22.141}
$$

$$
v_{h_g \to s_{j_g}}^t = 1 + v_{d_g \to h_g}^t + v_{s_{i_g} \to h_g}^t \tag{22.142}
$$

$$
\mu_{h_g \to s_{j_g}}^t = \mu_{d_g \to h_g}^t - \mu_{s_{i_g} \to h_g}^t \tag{22.143}
$$

When we compute  $q^{t+1}(s_{i_g})$  at the next iteration, by combining  $m^t_{h_g\rightarrow s_{i_g}}(s_{i_g})$  with the prior factor, we will see that the posterior mean of  $s_{i_g}$  goes up. Similarly, the posterior mean of  $s_{j_q}$  goes down.

It is straightforward to combine EP with ADF to perform online inference, which is necessary for most practical applications.

Let us consider a simple example of this method. We create a partial ordering of 5 players as shown in Figure 22.13(a). We then sample some game outcomes from this graph, where a parent always beats a child. We pass this data into (5 iterations of) the EP algorithm and infer the posterior mean and variance for each player's skill level. The results are shown in Figure 22.13(b). We see that the method has correctly inferred the rank ordering of the players.

## **22.5.6 Other applications of EP**

The TrueSkill model was developed by researchers at Microsoft. They and others have extended the model to a variety of other interesting applications, including personalized ad recommendation (Stern et al. 2009), predicting click-through-rate on ads in the Bing search engine (Graepel et al. 2010), etc. They have also developed a general purpose Bayesian inference toolbox based on EP called **infer.net** (Minka et al. 2010).

EP has also been used for a variety of other models, such as Gaussian process classification (Nickisch and Rasmussen 2008). See http://research.microsoft.com/en-us/um/people/ minka/papers/ep/roadmap.html for a list of other EP applications.

# **22.6 MAP state estimation**

In this section, we consider the problem of finding the most probable configuration of variables in a discrete-state graphical model, i.e., our goal is to find a MAP assignment of the following form:

$$
\mathbf{x}^* = \arg\max_{\mathbf{x} \in \mathcal{X}^m} p(\mathbf{x}|\boldsymbol{\theta}) = \arg\max_{\mathbf{x} \in \mathcal{X}^m} \sum_{i \in V} \theta_i(x_i) + \sum_{f \in F} \theta_f(\mathbf{x}_f) = \arg\max_{\mathbf{x} \in \mathcal{X}^m} \boldsymbol{\theta}^T \boldsymbol{\phi}(\mathbf{x})
$$
 (22.144)

where  $\theta_i$  are the singleton node potentials, and  $\theta_f$  are the factor potentials. (In this section, we follow the notation of (Sontag et al. 2011), which considers the case of general potentials, not just pairwise ones.) Note that the partition function  $Z(\theta)$  plays no role in MAP estimation.

If the treewidth is low, we can solve this problem with the junction tree algorithm (Section 20.4), but in general this problem is intractable. In this section, we discuss various approximations, building on the material from Section 22.3.

### **22.6.1 Linear programming relaxation**

We can rewrite the objective in terms of the variational parameters as follows:

$$
\arg\max_{\mathbf{x}\in\mathcal{X}^m} \boldsymbol{\theta}^T \boldsymbol{\phi}(\mathbf{x}) = \arg\max_{\boldsymbol{\mu}\in\mathbb{M}(G)} \boldsymbol{\theta}^T \boldsymbol{\mu}
$$
\n(22.145)

where  $\phi(\mathbf{x})=[\{\mathbb{I}(x_{s} = j)\}, \{\mathbb{I}(x_{f} = k)\})$  and  $\mu$  is a probability vector in the marginal polytope. To see why this equation is true, note that we can just set  $\mu$  to be a degenerate distribution with  $\mu(x_s) = \mathbb{I}(x_s = x_s^*)$ , where  $x_s^*$  is the optimal assigment of node s. So instead of optimizing over discrete assignments, we now optimize over probability distributions  $\mu$ .

It seems like we have an easy problem to solve, since the objective in Equation 22.145 is linear in  $\mu$ , and the constraint set  $\mathbb{M}(G)$  is convex. The trouble is,  $\mathbb{M}(G)$  in general has a number of facets that is exponential in the number of nodes.

A standard strategy in combinatorial optimization is to relax the constraints. In this case, instead of requiring probability vector  $\mu$  to live in the marginal polytope  $\mathbb{M}(G)$ , we allow it to

live inside a convex outer bound  $\mathbb{L}(G)$ . Having defined this relaxed constraint set, we have

$$
\max_{\mathbf{x} \in \mathcal{X}^m} \boldsymbol{\theta}^T \boldsymbol{\phi}(\mathbf{x}) = \max_{\boldsymbol{\mu} \in \mathbb{M}(G)} \boldsymbol{\theta}^T \boldsymbol{\mu} \le \max_{\boldsymbol{\tau} \in \mathbb{L}(G)} \boldsymbol{\theta}^T \boldsymbol{\tau}
$$
\n(22.146)

If the solution is integral, it is exact; if it is fractional, it is an approximation. This is called a (first order) **linear programming relaxtion**. The reason it is called first-order is that the constraints that are enforced are those that correspond to consistency on a tree, which is a graph of treewidth 1. It is possible to enforce higher-order consistency, using graphs with larger treewidth (see (Wainwright and Jordan 2008b, sec 8.5) for details).

How should we actually perform the optimization? We can use a generic linear programming package, but this is often very slow. Fortunately, in the case of graphical models, it is possible to devise specialised distributed message passing algorithms for solving this optimization problem, as we explain below.

## **22.6.2 Max-product belief propagation**

The MAP objective in Equation 22.145,  $\max_{\mu \in \mathbb{M}(G)} \theta^T \mu$ , is almost identical to the inference objective in Equation 22.23,  $\max_{\mu \in M(G)} \theta^T \mu + \mathbb{H}(\mu)$ , apart from the entropy term. One heuristic way to proceed would be to consider the **zero temperature limit** of the probability distribution  $\mu$ , where the probability distribution has all its mass centered on its mode (see Section 4.2.2). In such a setting, the entropy term becomes zero. We can then modify the message passing methods used to solve the inference problem so that they solve the MAP estimation problem instead. In particular, in the zero temperature limit, the sum operator becomes the max operator, which results in a method called **max-product belief propagation**.

In more detail, let

$$
A(\theta) \triangleq \max_{\mu \in \mathbb{M}(G)} \theta^T \mu + \mathbb{H}(\mu)
$$
\n(22.147)

Now consider an inverse temperature  $\beta$  going to infinity. We have

$$
\lim_{\beta \to +\infty} \frac{A(\beta \theta)}{\beta} = \lim_{\beta \to +\infty} \frac{1}{\beta} \max_{\mu \in \mathbb{M}(G)} \left\{ (\beta \theta)^T \mu + \mathbb{H}(\mu) \right\}
$$
(22.148)

$$
= \max_{\boldsymbol{\mu} \in \mathbb{M}(G)} \left\{ \boldsymbol{\theta}^T \boldsymbol{\mu} + \lim_{\beta \to +\infty} \frac{1}{\beta} \mathbb{H}\left(\boldsymbol{\mu}\right) \right\} \tag{22.149}
$$

$$
= \max_{\mu \in \mathbb{M}(G)} \theta^T \mu \tag{22.150}
$$

It is the concavity of the objective function that allows us to interchange the lim and max operators (see (Wainwright and Jordan 2008b, p274) for details).

Now consider the Bethe approximation, which has the form  $\max_{\tau \in \mathbb{L}(G)} \theta^T \tau + \mathbb{H}_{\text{Bethe}}(\tau)$ . We showed that loopy BP finds a local optimum of this objective. In the zero temperature limit, this objective is equivalent to the LP relaxation of the MAP problem. Unfortunately, max-product loopy BP does not solve this LP relaxation unless the graph is a tree (Wainwright and Jordan 2008b, p211). The reason is that Bethe energy functional is not concave (except on trees), so we are not licensed to swap the limit and max operators in the above zero-temperature derivation. However, if we use tree-reweighted BP, or TRBP/ TRW, we have a concave objective. In this case, one can show (Kolmogorov and Wainwright 2005) that the max-product version of TRBP does solve the above LP relaxation.

A certain scheduling of this algorithm, known as **sequential TRBP**, **TRBP-S**, or **TRW-S**, can be shown to always converge (Kolmogorov 2006), and furthermore, it typically does so faster than the standard parallel updates. The idea is to pick an arbitrary node ordering  $X_1, \ldots, X_N$ . We then consider a set of trees which is a subsequence of this ordering. At each iteration, we perform max-product BP from  $X_1$  towards  $X_N$  and back along one of these trees. It can be shown that this monotonically minimizes a lower bound on the energy, and thus is guaranteed to converge to the global optimum of the LP relaxation.

## **22.6.3 Graphcuts**

In this section, we show how to find MAP state estimates, or equivalently, minimum energy configurations, by using the **max flow/min cut** algorithm for graphs.<sup>8</sup> This class of methods is known as **graphcuts** and is very widely used, especially in computer vision applications.

We will start by considering the case of MRFs with binary nodes and a restricted class of potentials; in this case, graphcuts will find the exact global optimum. We then consider the case of multiple states per node, which are assumed to have some underlying ordering; we can approximately solve this case by solving a series of binary subproblems, as we will see.

## ********** Graphcuts for the generalized Ising model**

Let us start by considering a binary MRF where the edge energies have the following form:

$$
E_{uv}(x_u, x_v) = \begin{cases} 0 & \text{if } x_u = x_v \\ \lambda_{st} & \text{if } x_u \neq x_v \end{cases}
$$
 (22.151)

where  $\lambda_{st} \geq 0$  is the edge cost. This encourages neighboring nodes to have the same value (since we are trying to minimize energy). Since we are free to add any constant we like to the overall energy without affecting the MAP state estimate, let us rescale the local energy terms such that either  $E_u(1) = 0$  or  $E_u(0) = 0$ .

Now let us construct a graph which has the same set of nodes as the MRF, plus two distinguished nodes: the source s and the sinkt. If  $E_u(1) = 0$ , we add the edge  $x_u \to t$  with cost  $E_u(0)$ . (This ensures that if u is not in partition  $\mathcal{X}_t$ , meaning u is assigned to state 0, we will pay a cost of  $E_u(0)$  in the cut.) Similarly, If  $E_u(0) = 0$ , we add the edge  $x_u \to s$  with cost  $E_u(1)$ . Finally, for every pair of variables that are connected in the MRF, we add edges  $x_u \to x_v$ and  $x_v \to x_u$ , both with cost  $\lambda_{u,v} \geq 0$ . Figure 22.14 illustrates this construction for an MRF with 4 nodes, and with the following non-zero energy values:

$$
E_1(0) = 7, E_2(1) = 2, E_3(1) = 1, E_4(1) = 6 \tag{22.152}
$$

$$
\lambda_{1,2} = 6, \lambda_{2,3} = 6, \lambda_{3,4} = 2, \lambda_{1,4} = 1 \tag{22.153}
$$

Having constructed the graph, we compute a minimal  $s - t$  cut. This is a partition of the nodes into two sets,  $\mathcal{X}_s$ , which are nodes connected to s, and  $\mathcal{X}_t$ , which are nodes connected to t. We

<sup>8.</sup> There are a variety of ways to implement this algorithm, see e.g., (Sedgewick and Wayne 2011). The best take  $O(EV \log V)$  or  $O(V^3)$  time, where E is the number of edges and V is the number of nodes.

Image /page/35/Figure/1 description: This is a directed graph with nodes labeled s, t, z1, z2, z3, and z4. There are edges with weights connecting these nodes. The edges are: s to z4 with weight 6, s to z3 with weight 1 (dashed), s to z2 with weight 2 (dashed), z4 to z1 with weight 1 (dashed), z1 to t with weight 7, z1 to z2 with weight 6, z2 to z1 with weight 6, z2 to z3 with weight 6, and z3 to z4 with weight 2 (dashed). Nodes z1, z2, and t are enclosed in a rounded shape.

**Figure 22.14** Illustration of graphcuts applied to an MRF with 4 nodes. Dashed lines are ones which contribute to the cost of the cut (for bidirected edges, we only count one of the costs). Here the min cut has cost 6. Source: Figure 13.5 from (Koller and Friedman 2009). Used with kind permission of Daphne Koller.

pick the partition which minimizes the sum of the cost of the edges between nodes on different sides of the partition:

$$
cost(\mathcal{X}_s, \mathcal{X}_t) = \sum_{x_u \in \mathcal{X}_s, x_v \in \mathcal{X}_t} cost(x_u, s_v)
$$
\n(22.154)

In Figure 22.14, we see that the min-cut has cost 6.

Minimizing the cost in this graph is equivalent to minimizing the energy in the MRF. Hence nodes that are assigned to s have an optimal state of 0, and the nodes that are assigned to  $t$ have an optimal state of 1. In Figure 22.14, we see that the optimal MAP estimate is  $(1, 1, 1, 0)$ .

## ********** Graphcuts for binary MRFs with submodular potentials**

We now discuss how to extend the graphcuts construction to binary MRFs with more general kinds of potential functions. In particular, suppose each pairwise energy satisfies the following condition:

$$
E_{uv}(1,1) + E_{uv}(0,0) \le E_{uv}(1,0) + E_{uv}(0,1)
$$
\n(22.155)

In other words, the sum of the diagonal energies is less than the sum of the off-diagonal energies. In this case, we say the energies are **submodular** (Kolmogorov and Zabin 2004).9 An example of a submodular energy is an Ising model where  $\lambda_{uv} > 0$ . This is also known as an **attractive MRF** or **associative MRF**, since the model "wants" neighboring states to be the same.

<sup>9.</sup> Submodularity is the discrete analog of convexity. Intuitively, it corresponds to the "law of diminishing returns", that is, the extra value of adding one more element to a set is reduced if the set is already large. More formally, we say that  $f: 2^S \to R$  is submodular if for any  $A \subset B \subset S$  and  $x \in S$ , we have  $f(A \cup \{x\}) - f(A) \ge f(B \cup \{x\}) - f(B)$ . If  $-f$  is submodular, then f is **supermodular**.

To apply graphcuts to a binary MRF with submodular potentials, we construct the pairwise edge weights as follows:

$$
E'_{u,v}(0,1) = E_{u,v}(1,0) + E_{u,v}(0,1) - E_{u,v}(0,0) - E_{u,v}(1,1)
$$
\n(22.156)

This is guaranteed to be non-negative by virtue of the submodularity assumption. In addition, we construct new local edge weights as follows: first we initialize  $E'(u) = E(u)$ , and then for each edge pair  $(u, v)$ , we update these values as follows:

$$
E'_u(1) = E'_u(1) + (E_{u,v}(1,0) - E_{u,v}(0,0))
$$
\n(22.157)

$$
E'_{v}(1) = E'_{u}(1) + (E_{u,v}(1,1) - E_{u,v}(1,0))
$$
\n(22.158)

We now construct a graph in a similar way to before. Specifically, if  $E'_u(1) > E'_u(0)$ , we add the edge  $u \to s$  with cost  $E'_u(1) - E'_u(0)$ , otherwise we add the edge  $u \to t$  with cost  $E_u'(0) - E_u(1)$ . Finally for every MRF edge for which  $E_{u,v}'(0,1) > 0$ , we add a graphcuts edge  $x_u - x_v$  with cost  $E'_{u,v}(0, 1)$ . (We don't need to add the edge in both directions.)

One can show (Exercise 22.1) that the min cut in this graph is the same as the minimum energy configuration. Thus we can use max flow/min cut to find the globally optimal MAP estimate (Greig et al. 1989).

#### ********** Graphcuts for nonbinary metric MRFs**

We now discuss how to use graphcuts for approximate MAP estimation in MRFs where each node can have multiple states (Boykov et al. 2001). However, we require that the pairwise energies form a metric. We call such a model a **metric MRF**. For example, suppose the states have a natural ordering, as commonly arises if they are a discretization of an underlying continuous space. In this case, we can define a metric of the form  $E(x_s, x_t) = \min(\delta, ||x_s - x_t||)$  or a semi-metric of the form  $E(x_s, x_t) = \min(\delta, (x_s - x_t)^2)$ , for some constant  $\delta > 0$ . This energy encourages neighbors to have similar labels, but never "punishes" them by more than  $\delta$ . This  $\delta$ term prevents over-smoothing, which we illustrate in Figure 19.20.

One version of graphcuts is the **alpha expansion**. At each step, it picks one of the available labels or states and calls it  $\alpha$ ; then it solves a binary subproblem where each variable can choose to remain in its current state, or to become state  $\alpha$  (see Figure 22.15(d) for an illustration). More precisely, we define a new MRF on binary nodes, and we define the energies of this new model, relative to the current assignment **x**, as follows:

$$
E'_u(0) = E_u(x_u), E'_u(1) = E_u(\alpha), E'_{u,v}(0,0) = E_{u,v}(x_u, x_v)
$$
\n(22.159)

$$
E'_{u,v}(0,1) = E_{u,v}(x_u,\alpha), E'_{u,v}(1,0) = E_{u,v}(\alpha, x_v), E'_{u,v}(1,1) = E_{u,v}(\alpha,\alpha)
$$
\n(22.160)

To optimize  $E'$  using graph cuts (and thus figure out the optimal alpha expansion move), we require that the energies be submodular. Plugging in the definition we get the following constraint:

$$
E_{u,v}(x_u, x_v) + E_{u,v}(\alpha, \alpha) \le E_{u,v}(x_u, \alpha) + E_{u,v}(\alpha, x_v)
$$
\n(22.161)

For any distance function,  $E_{u,v}(\alpha, \alpha) = 0$ , and the remaining inequality follows from the triangle inequality. Thus we can apply the alpha expansion move to any metric MRF.

Image /page/37/Picture/1 description: This image displays four sequential diagrams, labeled (a), (b), (c), and (d), illustrating a process involving three distinct regions labeled alpha (red), beta (green), and gamma (cyan). Diagram (a) shows an initial state where the alpha region is surrounded by beta and gamma regions, with irregular boundaries. Diagram (b) highlights a specific area within the beta region adjacent to alpha with a white circle, suggesting a point of interest or change. Diagrams (c) and (d) show the progression of the alpha region, which appears to expand and engulf parts of the beta and gamma regions, smoothing its boundaries over time. The overall visual suggests a simulation or transformation process where the red region grows and changes shape.

(a) initial labeling (b) standard move (c)  $\alpha$ - $\beta$ -swap (d)  $\alpha$ -expansion

**Figure 22.15** (a) An image with 3 labels. (b) A standard local move (e.g., by iterative conditional modes) just flips the label of one pixel. (c) An  $\alpha - \beta$  swap allows all nodes that are currently labeled as  $\alpha$  to be relabeled as  $\beta$  if this decreases the energy. (d) An  $\alpha$  expansion allows all nodes that are not currently labeled as  $\alpha$  to be relabeled as  $\alpha$  if this decreases the energy. Source: Figure 2 of (Boykov et al. 2001). Used with kind permission of Ramin Zabih.

At each step of alpha expansion, we find the optimal move from amongst an exponentially large set; thus we reach a **strong local optimum**, of much lower energy than the local optima found by standard greedy label flipping methods such as iterative conditional modes. In fact, one can show that, once the algorithm has converged, the energy of the resulting solution is at most  $2c$  times the optimal energy, where

$$
c = \max_{(u,v)\in\mathcal{E}} \frac{\max_{\alpha\neq\beta} E_{uv}(\alpha,\beta)}{\min_{\alpha\neq\beta} E_{uv}(\alpha,\beta)}
$$
(22.162)

See Exercise 22.3 for the proof. In the case of the Potts model,  $c = 1$ , so we have a 2approximation.

Another version of graphcuts is the **alpha-beta swap**. At each step, two labels are chosen, call them  $\alpha$  and  $\beta$ . All the nodes currently labeled  $\alpha$  can change to  $\beta$  (and vice versa) if this reduces the energy (see Figure 22.15(c) for an illustration). The resulting binary subproblem can be solved exactly, even if the energies are only semi-metric (that is, the triangle inequality need not hold; see Exercise 22.2). Although the  $\alpha - \beta$  swap version can be applied to a broader class of models than the  $\alpha$ -expansion version, it is theoretically not as powerful. Indeed, in various low-level vision problems, (Szeliski et al. 2008) show empirically that the expansion version is usually better than the swap version (see Section 22.6.4).

## **22.6.4 Experimental comparison of graphcuts and BP**

In Section 19.6.2.7, we described lattice-structured CRFs for various low-level vision problems. (Szeliski et al. 2008) performed an extensive comparison of different approximate optimization techniques for this class of problems. Some of the results, for the problem of stereo depth estimation, are shown in Figure 22.16. We see that the graphcut and tree-reweighted maxproduct BP (TRW) give the best results, with regular max-product BP being much worse. In terms of speed, graphcuts is the fastest, with TRW a close second. Other algorithms, such as ICM, simulated annealing or a standard domain-specific heuristic known as normalize correlation, are

Image /page/38/Picture/1 description: The image displays three grayscale images in the top row, likely representing stereo input images and a corresponding depth map. Below these images are two plots showing energy minimization over running time. The left plot has a y-axis labeled 'Energy' ranging from 1.3 to 2.0 (in units of 10^6) and an x-axis labeled 'Running Time (s)' ranging from 10^0 to 10^3. It shows three curves: 'Max-Product BP' (solid line), 'a-Expansion' (dashed line), and 'TRW' (dotted line), with 'a-b Swap' also indicated by a thick solid line. The right plot has a y-axis labeled 'Energy' ranging from 3.6 to 4.2 (in units of 10^5) and an x-axis labeled 'Running Time (s)' ranging from 10^0 to 10^2. It also shows three curves: 'Max-Product BP' (solid line), 'a-Expansion' (dashed line), and 'TRW' (dotted line), with 'a-b Swap' also indicated by a thick solid line.

**Figure 22.16** Energy minimization on a CRF for stereo depth estimation. Top row: two input images along with the ground truth depth values. Bottom row: energy vs time for 4 different optimization algorithms. Bottom left: results are for the Teddy image (shown in top row). Bottom right: results are for the Tsukuba image (shown in Figure 22.17(a)). Source: Figure 13.B.1 of (Koller and Friedman 2009). Used with kind permission of Daphne Koller.

even worse, as shown qualitatively in Figure 22.17.

Since TRW is optimizing the dual of the relaxed LP problem, we can use its value at convergence to evaluate the optimal energy. It turns out that for many of the images in the stereo benchmark dataset, the ground truth has higher energy (lower probability) than the globally optimal estimate (Meltzer et al. 2005). This indicates that we are optimizing the wrong model. This is not surprising, since the pairwise CRF ignores known long-range constraints. Unfortunately, if we add these constraints to the model, the graph either becomes too dense (making BP slow), and/or the potentials become non-submodular (making graphcuts inapplicable).

One way around this is to generate a diverse set of local modes, using repeated applications of graph cuts, as described in (Yadollahpour et al. 2011). We can then apply a more sophisticated model, which uses global features, to rerank the solutions.

Image /page/39/Picture/1 description: This image displays a comparison of different stereo matching algorithms. The top row shows the original left image (a) with dimensions 384x288 and 15 labels, and the ground truth disparity map (b). The bottom two rows present the results from three different algorithms: Swap algorithm (c), Expansion algorithm (d), Normalized correlation (e), and Simulated annealing (f). Each subfigure shows a disparity map, where different shades of gray represent different disparity values.

**Figure 22.17** An example of stereo depth estimation using an MRF. (a) Left image, of size  $384 \times 288$ pixels, from the University of Tsukuba. (The corresponding right image is similar, but not shown.) (b) Ground truth depth map, quantized to 15 levels. (c-f): MAP estimates using different methods: (c)  $\alpha - \beta$ swap, (d)  $\alpha$  expansion, (e) normalized cross correlation, (f) simulated annealing. Source: Figure 10 of (Boykov et al. 2001). Used with kind permission of Ramin Zabih.

## **22.6.5 Dual decomposition**

We are interested in computing

$$
p^* = \max_{\mathbf{x} \in \mathcal{X}^m} \sum_{i \in V} \theta_i(x_i) + \sum_{f \in F} \theta_f(\mathbf{x}_f)
$$
\n(22.163)

where  $F$  represents a set of factors. We will assume that we can tractably optimize each local factor, but the combination of all of these factors makes the problem intractable. One way to proceed is to optimize each term independently, but then to introduce constraints that force all the local estimates of the variables' values to agree with each other. We explain this in more detail below, following the presentation of (Sontag et al. 2011).

Image /page/40/Figure/1 description: The image displays two diagrams, labeled (a) and (b), illustrating graph structures. Diagram (a) shows a square graph with nodes labeled x1, x2, x3, and x4. The edges are annotated with functions: θf(x1, x2) between x1 and x2, θh(x2, x4) between x2 and x4, θk(x3, x4) between x3 and x4, and θg(x1, x3) between x1 and x3. These nodes and edges are enclosed within a dashed oval. Diagram (b) presents a more complex graph structure, also enclosed in a dashed oval. It features nodes labeled with superscripts: x1^f, x2^f, x1^g, x3^g, x2^h, x4^h, x3^k, and x4^k. There are connections between x1^f and x2^f, x1^g and x3^g, and x3^k and x4^k, each annotated with functions θf(x1^f, x2^f), θg(x1^g, x3^g), and θk(x3^k, x4^k) respectively. Additionally, there are connections with double lines indicating equality: x1^g = x1, x2^h = x2, x3^g = x3, and x4^h = x4. There is also a connection between x2^h and x4^h labeled θh(x2^h, x4^h).

**Figure 22.18** (a) A pairwise MRF with 4 different edge factors. (b) We have 4 separate variables, plus a copy of each variable for each factor it participates in. Source: Figure 1.2-1.3 of (Sontag et al. 2011). Used with kind permission of David Sontag.

## ********** Basic idea**

Let us duplicate the variables  $x_i$ , once for each factor, and then force them to be equal. Specifically, let  $x_f^f = \{x_i^f\}_{i \in f}$  be the set of variables used by factor f. This construction is illustrated in Figure 22.18. We can reformulate the objective as follows:

$$
p^* = \max_{\mathbf{x}, \mathbf{x}^f} \sum_{i \in V} \theta_i(x_i) + \sum_{f \in F} \theta_f(\mathbf{x}_f^f) \quad \text{s.t.} \quad x_i^f = x_i \quad \forall f, i \in f \tag{22.164}
$$

Let us now introduce Lagrange multipliers, or dual variables,  $\delta_{fi}(k)$ , to enforce these constraints. The Lagrangian becomes

$$
L(\boldsymbol{\delta}, \mathbf{x}, \mathbf{x}^f) = \sum_{i \in V} \theta_i(x_i) + \sum_{f \in F} \theta_f(\mathbf{x}_f^f)
$$
\n(22.165)

$$
+\sum_{f\in F}\sum_{i\in f}\sum_{\hat{x}_i}\delta_{fi}(\hat{x}_i)\left(\mathbb{I}(x_i=\hat{x}_i)-\mathbb{I}(x_i^f=\hat{x}_i)\right) \tag{22.166}
$$

This is equivalent to our original problem in the following sense: for any value of  $\delta$ , we have

$$
p^* = \max_{\mathbf{x}, \mathbf{x}^f} L(\boldsymbol{\delta}, \mathbf{x}, \mathbf{x}^f) \qquad \text{s.t.} \quad x_i^f = x_i \quad \forall f, i \in f \tag{22.167}
$$

since if the constraints hold, the last term is zero. We can get an upper bound by dropping the consistency constraints, and just optimizing the following upper bound:

$$
L(\delta) \triangleq \max_{\mathbf{x}, \mathbf{x}^f} L(\delta, \mathbf{x}, \mathbf{x}^f) (22.168)
$$

$$
= \sum_{i} \max_{x_i} \left( \theta_i(x_i) + \sum_{f:i \in f} \delta_{fi}(x_i) \right) + \sum_{f} \max_{\mathbf{x}_f} \left( \theta_f(\mathbf{x}_f) - \sum_{i \in f} \delta_{fi}(x_i) \right) (22.169)
$$

See Figure 22.19 for an illustration.

Image /page/41/Figure/1 description: The image displays a diagram illustrating relationships between variables, likely in a mathematical or computational context. It features several nodes, represented by circles containing variables like x1, x2, x3, and x4. These nodes are connected by lines, forming small graphs. Each graph is enclosed within a dashed blue oval and is associated with a mathematical expression. For instance, one oval contains nodes x1 and x2 connected by a line, with the expression \(\\theta\_f(x1, x2) - \delta\_{f1}(x1) - \delta\_{f2}(x2)\\) outside. Another section shows four separate ovals, each containing a single variable node (x1, x2, x3, x4) and associated mathematical terms involving \(\delta\) functions and addition or subtraction. For example, one oval has x1 with terms \(\delta\_{f1}(x1)\\) and \(\delta\_{g1}(x1)\\) added together. The overall structure suggests a decomposition or representation of a larger function or system into smaller, interconnected components.

**Figure 22.19** Illustration of dual decomposition. Source: Figure 1.2 of (Sontag et al. 2011). Used with kind permission of David Sontag.

This objective is tractable to optimize, since each  $x_f$  term is decoupled. Furthermore, we see that  $L(\delta) \geq p^*$ , since by relaxing the consistency constraints, we are optimizing over a larger space. Furthermore, we have the property that

$$
\min_{\delta} L(\delta) = p^* \tag{22.170}
$$

so the upper bound is tight at the optimal value of  $\delta$ , which enforces the original constraints.

Minimizing this upper bound is known as **dual decomposition** or **Lagrangian relaxation** (Komodakis et al. 2011; Sontag et al. 2011; Rush and Collins 2012). Furthemore, it can be shown that  $L(\delta)$  is the dual to the same LP relaxation we saw before. We will discuss several possible optimization algorithms below.

The main advantage of dual decomposition from a practical point of view is that it allows one to mix and match different kinds of optimization algorithms in a convenient way. For example, we can combine a grid structured graph with local submodular factors to perform image segmentation, together with a tree structured model to perform pose estimation (see Exercise 22.4). Analogous methods can be used in natural language processing, where we often have a mix of local and global constraints (see e.g., (Koo et al. 2010; Rush and Collins 2012)).

## **22.6.5.2 Theoretical guarantees**

What can we say about the quality of the solutions obtained in this way? To understand this, let us first introduce some more notation:

$$
\overline{\theta}_i^{\delta}(x_i) \triangleq \theta_i(x_i) + \sum_{f:i \in f} \delta_{fi}(x_i) \tag{22.171}
$$

$$
\overline{\theta}_f^{\delta}(\mathbf{x}_f) \triangleq \theta_f(\mathbf{x}_f) - \sum_{i \in f} \delta_{fi}(x_i)
$$
\n(22.172)

This represents a reparameterization of the original problem, in the sense that

$$
\sum_{i} \theta_i(x_i) + \sum_{f} \theta_f(\mathbf{x}_f) = \sum_{i} \overline{\theta}_i^{\delta}(x_i) + \sum_{f} \overline{\theta}_f^{\delta}(\mathbf{x}_f)
$$
\n(22.173)

and hence

$$
L(\boldsymbol{\delta}) = \sum_{i} \max_{x_i} \overline{\theta}_i^{\boldsymbol{\delta}}(x_i) + \sum_{f} \max_{\mathbf{x}_f} \overline{\theta}_f^{\boldsymbol{\delta}}(\mathbf{x}_f)
$$
(22.174)

Now suppose there is a set of dual variables  $\delta^*$  and an assignment  $x^*$  such that the maximizing assignments to the singleton terms agrees with the assignments to the factor terms, i.e., so that  $x_i^* \in \text{argmax}_{x_i} \overline{\theta_i}^{\delta^*}(x_i)$  and  $\mathbf{x}_f^* \in \text{argmax}_{\mathbf{x}_f} \overline{\theta_f}^{\delta^*}(\mathbf{x}_f)$ . In this case, we have

$$
L(\boldsymbol{\delta}^*) = \sum_i \overline{\theta}_i^{\boldsymbol{\delta}^*}(x_i^*) + \sum_f \overline{\theta}_f^{\boldsymbol{\delta}^*}(\mathbf{x}_f^*) = \sum_i \theta_i(x_i^*) + \sum_f \theta_f(\mathbf{x}_f^*)
$$
(22.175)

Now since

$$
\sum_{i} \theta_i(x_i^*) + \sum_{f} \theta_f(\mathbf{x}_f^*) \le p^* \le L(\boldsymbol{\delta}^*)
$$
\n(22.176)

we conclude that  $L(\delta^*) = p^*$ , so  $\mathbf{x}^*$  is the MAP assignment.

So if we can find a solution where all the subproblems agree, we can be assured that it is the global optimum. This happens surprisingly often in practical problems.

## ********** Subgradient descent**

 $L(\delta)$  is a convex and continuous objective, but it is non-differentiable at points  $\delta$  where  $\overline{\theta}^\delta_i(x_i)$ or  $\overline{\theta}^{\delta}_f(\mathbf{x}_f)$  have multiple optima. One approach is to use subgradient descent. This updates all the elements of  $\delta$  at the same time, as follows:

$$
\delta_{fi}^{t+1}(x_i) = \delta_{fi}^t(x_i) - \alpha_t g_{fi}^t(x_i)
$$
\n(22.177)

where  $g^t$  the subgradient of  $L(\delta)$  at  $\delta^t$ . If the step sizes  $\alpha_t$  are set appropriately (see Section *******), this method is guaranteed to converge to a global optimum of the dual. (See (Komodakis et al. 2011) for details.)

One can show that the gradient is given by the following sparse vector. First let  $x_i^s \in$  $\mathop{\arg\!\max}_{x_i} \overline{\theta}_i^{\boldsymbol{\delta}^t}(x_i)$  and  $\mathbf{x}_f^f \in \mathop{\arg\!\max}_{\mathbf{x}_f} \overline{\theta}_f^{\boldsymbol{\delta}^t}(\mathbf{x}_f)$ . Next let  $g_{fi}(x_i)=0$  for all elements. Finally, if  $x_i^f \neq x_i^s$  (so factor f disagrees with the local term on how to set variable *i*), we set  $g_{fi}(x_i^s)$  = +1 and  $g_{fi}(x_i^f) = -1$ . This has the effect of decreasing  $\overline{\theta}_i^{\delta^t}(x_i^s)$  and increasing  $\overline{\theta}_i^{\delta^t}(x_i^f)$ , bringing them closer to agreement. Similarly, the subgradient update will decrease the value of  $\overline{\theta}_f^{\delta^t}(x_i^f, \mathbf{x}_{f\setminus i})$  and increasing the value of  $\overline{\theta}_f^{\delta^t}(x_i^s, \mathbf{x}_{f\setminus i}).$ 

To compute the gradient, we need to be able to solve subproblems of the following form:

$$
\underset{\mathbf{x}_{f}}{\operatorname{argmax}} \overline{\theta}_{f}^{\delta^{t}}(\mathbf{x}_{f}) = \underset{\mathbf{x}_{f}}{\operatorname{argmax}} \left[ \theta_{f}(\mathbf{x}_{f}) - \sum_{i \in f} \delta_{fi}^{t}(x_{i}) \right]
$$
(22.178)

(In (Komodakis et al. 2011), these subproblems are called slaves, whereas  $L(\delta)$  is called the master.) Obviously if the scope of factor  $f$  is small, this is simple. For example, if each factor is pairwise, and each variable has K states, the cost is just  $K^2$ . However, there are some kinds of global factors that also support exact and efficient maximization, including the following:

- Graphical models with low tree width.
- Factors that correspond to bipartite graph matchings (see e.g., (Duchi et al. 2007)). This is useful for data association problems, where we must match up a sensor reading with an unknown source. We can find the maximal matching using the so-called Hungarian algorithm in  $O(|f|^3)$  time (see e.g., (Padadimitriou and Steiglitz 1982)).
- Supermodular functions. We discuss this case in more detail in Section ********.
- Cardinality constraints. For example, we might have a factor over a large set of binary variables that enforces that a certain number of bits are turned on; this can be useful in problems such as image segmentation. In particular, suppose  $\theta_f(\mathbf{x}_f) = 0$  if  $\sum_{i \in f} x_i = L$ and  $\theta_f(\mathbf{x}_f) = -\infty$  otherwise. We can find the maximizing assignment in  $\overline{O(|f| \log |f|)}$ time as follows: first define  $e_i = \delta_{fi}(1) - \delta_{fi}(0)$ ; now sort the  $e_i$ ; finally set  $x_i = 1$  for the first L values, and  $x_i = 0$  for the rest (Tarlow et al. 2010).
- Factors which are constant for all but a small set S of distinguished values of  $x_f$ . Then we can optimize over the factor in  $O(|S|)$  time (Rother et al. 2009).

## **22.6.5.4 Coordinate descent**

An alternative to updating the entire  $\delta$  vector at once (albeit sparsely) is to update it using block coordinate descent. By choosing the size of the blocks, we can trade off convergence speed with ease of the local optimization problem.

One approach, which optimizes  $\delta_{fi}(x_i)$  for all  $i \in f$  and all  $x_i$  at the same time (for a fixed factor f), is known as **max product linear programming** (Globerson and Jaakkola 2008). Algorithmically, this is similar to belief propagation on a factor graph. In particular, we define  $\delta_{f\to i}$  as messages sent from factor f to variable i, and we define  $\delta_{i\to f}$  as messages sent from variable  $i$  to factor f. These messages can be computed as follows (see (Globerson and Jaakkola 2008) for the derivation):<sup>10</sup>

$$
\delta_{i \to f}(x_i) = \theta_i(x_i) + \sum_{g \neq f} \delta_{g \to i}(x_i)
$$
\n(22.179)

$$
\delta_{f \to i}(x_i) = -\delta_{i \to f}(x_i) + \frac{1}{|f|} \max_{\mathbf{x}_{f \setminus i}} \left[ \theta_f(\mathbf{x}_f) + \sum_{j \in f} \delta_{j \to f}(x_j) \right]
$$
(22.180)

We then set the dual variables  $\delta_{fi}(x_i)$  to be the messages  $\delta_{f\rightarrow i}(x_i)$ .

For example, consider a 2  $\times$  2 grid MRF, with the following pairwise factors:  $\theta_f(x_1, x_2)$ ,  $\theta_{\alpha}(x_1, x_3)$ ,  $\theta_{h}(x_2, x_4)$ , and  $\theta_{k}(x_3, x_4)$ . The outgoing message from factor f to variable 2 is a

<sup>10.</sup> Note that we denote their  $\delta_i^{-f}(x_i)$  by  $\delta_{i\to f}(x_i)$ .

function of all messages coming into  $f$ , and  $f'$ s local factor:

$$
\delta_{f \to 2}(x_2) = -\delta_{2 \to f}(x_2) + \frac{1}{2} \max_{x_1} \left[ \theta_f(x_1, x_2) + \delta_{1 \to f}(x_1) + \delta_{2 \to f}(x_2) \right]
$$
(22.181)

Similarly, the outgoing message from variable 2 to factor  $f$  is a function of all the messages sent into variable 2 from other connected factors (in this example, just factor  $h$ ) and the local potential:

$$
\delta_{2 \to f}(x_2) = \theta_2(\omega_2) + \delta_{h2}(x_2) \tag{22.182}
$$

The key computational bottleneck is computing the max marginals of each factor, where we max out all the variables from  $x_f$  except for  $x_i$ , i.e., we need to be able to compute the following max marginals efficiently:

$$
\max_{\mathbf{x}_{f\setminus i}} h(\mathbf{x}_{f\setminus i}, x_i), \quad h(\mathbf{x}_{f\setminus i}, x_i) \triangleq \theta_f(\mathbf{x}_f) + \sum_{j \in f} \delta_{jf}(x_j)
$$
\n(22.183)

The difference from Equation 22.178 is that we are maxing over all but one of the variables. We can solve this efficiently for low treewidth graphical models using message passing; we can also solve this efficiently for factors corresponding to bipartite matchings (Duchi et al. 2007) or to cardinality constraints (Tarlow et al. 2010). However, there are cases where maximizing over all the variables in a factor's scope is computationally easier than maximizing over all-but-one (see (Sontag et al. 2011, Sec 1.5.4) for an example); in such cases, we may prefer to use a subgradient method.

Coordinate descent is a simple algorithm that is often much faster at minimizing the dual than gradient descent, especially in the early iterations. It also reduces the objective monotonically, and does not need any step size parameters. Unfortunately, it is not guaranteed to converge to the global optimum, since  $L(\delta)$  is convex but not strictly convex (which implies there may be more than one globally optimizing value). One way to ensure convergence is to replace the max function in the definition of  $L(\delta)$  with the soft-max function, which makes the objective strictly convex (see e.g., (Hazan and Shashua 2010) for details).

## **22.6.5.5 Recovering the MAP assignment**

So far, we have been focussing on finding the optimal value of  $\delta^*$ . But what we really want is the optimal value of  $x^*$ . In general, computing  $x^*$  from  $\delta^*$  is NP-hard, even if the LP relaxation is tight and the MAP assignment is unique (Sontag et al. 2011, Theorem 1.4). (The troublesome cases arise when there are fractional assignments with the same optimal value as the MAP estimate.)

However, suppose that each  $\overline{\theta}_{i}^{\delta^{*}}$  has a unique maximum,  $x_{i}^{*}$ ; in this case, we say that  $\delta^{*}$  is **locally decodable** to **x**∗. One can show than in this case, the LP relaxation is unique and its solution is indeed **x**∗. If many, but not all, of the nodes are uniquely decodable, we can "clamp" the uniquely decodable ones to their MAP value, and then use exact inference algorithms to figure out the optimal assignment to the remaining variables. Using this method, (Meltzer et al. 2005) was able to optimally solve various stereo vision CRF estimation problems, and (Yanover et al. 2007) was able to optimally solve various protein side-chain structure predicition problems.

Another approach is to use the upper bound provided by the dual in a branch and bound search procedure (Geoffrion 1974).

## **Exercises**

**Exercise 22.1** Graphcuts for MAP estimation in binary submodular MRFs

(Source: Ex. 13.14 of (Koller and Friedman 2009).). Show that using the graph construction described in Section ********, the cost of the cut is equal to the energy of the corresponding assignment, up to an irrelevant constant. (Warning: this exercise involves a lot of algebraic book-keeping.)

**Exercise 22.2** Graphcuts for alpha-beta swap

(Source: Ex. 13.15 of (Koller and Friedman 2009).). Show how the optimal alpha-beta swap can be found by running min-cut on an appropriately constructed graph. More precisely,

- a. Define a set of binary variables  $t_1, \ldots, t_n$  such that  $t_i = 0$  means  $x'_i = \alpha$ ,  $t_i = 1$  if  $x'_i = \beta$ , and  $x'_i = x_i$  is unchanged  $f(x_i \neq \alpha)$  and  $x_i \neq \beta$ .  $x'_i = x_i$  is unchanged  $f \ x_i \neq \alpha$  and  $x_i \neq \beta$ .
- b. Define an energy function over the new variables such that  $E'(\mathbf{t}) = E(\mathbf{x}) + \text{const.}$
- c. Show that  $E'$  is submodular if  $E$  is a semimetric.

**Exercise 22.3** Constant factor optimality for alpha-expansion

(Source: Daphne Koller.). Let X be a pairwise metric Markov random field over a graph  $G = (V, E)$ . Suppose that the variables are nonbinary and that the node potentials are nonnegative. Let  $A$  denote the set of labels for each  $X \in \mathcal{X}$ . Though it is not possible to (tractably) find the globally optimal assignment  $x^*$  in general, the  $\alpha$ -expansion algorithm provides a method for finding assignments  $\hat{x}$  that are locally optimal with respect to a large set of transformations, *i.e.*, the possible  $\alpha$ -expansion moves.

Despite the fact that  $\alpha$ -expansion only produces a locally optimal MAP assignment, it is possible to prove that the energy of this assignment is within a known factor of the energy of the globally optimal solution  $x^*$ . In fact, this is a special case of a more general principle that applies to a wide variety of algorithms, including max-product belief propagation and more general move-making algorithms: If one can prove that the solutions obtained by the algorithm are 'strong local minima', *i.e.*, local minima with respect to a large set of potential moves, then it is possible to derive bounds on the (global) suboptimality of these solutions, and the quality of the bounds will depend on the nature of the moves considered. (There is a precise definition of 'large set of moves'.)

Consider the following approach to proving the suboptimality bound for  $\alpha$ -expansion.

- a. Let  $\hat{x}$  be a local minimum with respect to expansion moves. For each  $\alpha \in A$ , let  $V^{\alpha} = \{s \in V \mid x_s^* = a_s \}$ Let x be a local minimum with respect to expansion moves. For each  $\alpha \in A$ , let  $v = \{s \in v \mid x_s = \alpha\}$ , *i.e.*, the set of nodes labelled  $\alpha$  in the global minimum. Let x' be an assignment that is equal to  $x^*$  on  $V^{\alpha}$  and equal to  $\hat{x}$  elsewhere; this is an  $\alpha$ -expansion of  $\hat{x}$ . Verify that  $E(x^*) \leq E(\hat{x}) \leq E(x')$ .
- b. Building on the previous part, show that  $E(\hat{x}) \leq 2cE(x^*)$ , where  $c = \max_{(s,t) \in E} \left( \frac{\max_{\alpha \neq \beta} \varepsilon_{st}(\alpha,\beta)}{\min_{\alpha \neq \beta} \varepsilon_{st}(\alpha,\beta)} \right)$  $\min_{\alpha \neq \beta} \varepsilon_{st}(\alpha, \beta)$  $\setminus$

and E denotes the energy of an assignment.

*Hint*. Think about where  $x'$  agrees with  $\hat{x}$  and where it agrees with  $x^*$ .

#### **Exercise 22.4** Dual decomposition for pose segmentation

(Source: Daphne Koller.). Two important problems in computer vision are that of parsing articulated objects (*e.g.*, the human body), called *pose estimation*, and segmenting the foreground and the background, called *segmentation*. Intuitively, these two problems are linked, in that solving either one would be easier if the solution to the other were available. We consider solving these problems simultaneously using a joint model over human poses and foreground/background labels and then using dual decomposition for MAP inference in this model.

We construct a two-level model, where the high level handles pose estimation and the low level handles pixel-level background segmentation. Let  $G = (\mathcal{V}, \mathcal{E})$  be an undirected grid over the pixels. Each node  $i \in \mathcal{V}$  represents a pixel. Suppose we have one binary variable  $x_i$  for each pixel, where  $x_i = 1$  means that pixel i is in the foreground. Denote the full set of these variables by  $\mathbf{x} = (x_i)$ .

In addition, suppose we have an undirected tree structure  $T = (\mathcal{V}', \mathcal{E}')$  on the parts. For each body part, we have a discrete set of candidate poses that the part can be in, where each pose is characterized part, we have a discrete set of candidate poses that the part can be in, where each pose is characterized by parameters specifying its position and orientation. (These candidates are generated by a procedure external to the algorithm described here.) Define  $y_{jk}$  to be a binary variable indicating whether body part  $j \in \mathcal{V}'$  is in configuration k. Then the full set of part variables is given by  $\mathbf{y} = (y_{jk})$ , with  $j \in \mathcal{V}'$ <br>and  $k = 1, \ldots, K$ , where J is the total number of body parts and K is the number of candidate poses and  $k = 1, \ldots, K$ , where J is the total number of body parts and K is the number of candidate poses for each part. Note that in order to describe a valid configuration, **y** must satisfy the constraint that  $\sum_{k=1}^{K} y_{jk} = 1$  for each j.

Suppose we have the following energy function on pixels:

$$
E_1(\mathbf{x}) = \sum_{i \in \mathcal{V}} 1[x_i = 1] \cdot \theta_i + \sum_{(i,j) \in \mathcal{E}} 1[x_i \neq x_j] \cdot \theta_{ij}.
$$

Assume that the  $\theta_{ij}$  arises from a metric (*e.g.*, based on differences in pixel intensities), so this can be viewed as the energy for a pairwise metric MRF with respect to G.

We then have the following energy function for parts:

$$
E_2(\mathbf{y}) = \sum_{p \in \mathcal{V}'} \theta_p(y_p) + \sum_{(p,q) \in \mathcal{E}'} \theta_{pq}(y_p, y_q).
$$

Since each part candidate  $y_{jk}$  is assumed to come with a position and orientation, we can compute a binary mask in the image plane. The mask assigns a value to each pixel, denoted by  $\{w^i_{jk}\}_{i\in\mathcal{V}}$ , where  $w_{jk}^i = 1$  if pixel *i* lies on the skeleton and decreases as we move away. We can use this to define an energy function relating the parts and the pixels: energy function relating the parts and the pixels:

$$
E_3(\mathbf{x}, \mathbf{y}) = \sum_{i \in \mathcal{V}} \sum_{j \in \mathcal{V}'} \sum_{k=1}^K 1[x_i = 0, y_{jk} = 1] \cdot w_{jk}^i.
$$

In other words, this energy term only penalizes the case where a part candidate is active but the pixel underneath is labeled as background.

Formulate the minimization of  $E_1 + E_2 + E_3$  as an integer program and show how you can use dual decomposition to solve the dual of this integer program. Your solution should describe the decomposition into slaves, the method for solving each one, and the update rules for the overall algorithm. Briefly justify your design choices, particularly your choice of inference algorithms for the slaves.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
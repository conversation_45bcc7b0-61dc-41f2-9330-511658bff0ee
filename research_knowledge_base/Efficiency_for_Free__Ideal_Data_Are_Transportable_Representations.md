# Efficiency for Free: Ideal Data Are Transportable Representations

Peng <PERSON><sup>1,2</sup> <PERSON><sup>1</sup> <PERSON><sup>2,</sup>\* <sup>1</sup>Zhejiang University  $2West$ <NAME_EMAIL>, yi\<EMAIL>, <EMAIL>

## Abstract

Data, the seminal opportunity and challenge in modern machine learning, currently constrains the scalability of representation learning and impedes the pace of model evolution. In this work, we investigate the efficiency properties of data from both optimization and generalization perspectives. Our theoretical and empirical analysis reveals an unexpected finding: for a given task, utilizing a publicly available, taskand architecture-agnostic model (referred to as the 'prior model' in this paper) can effectively produce efficient data. Building on this insight, we propose the Representation Learning Accelerator (RELA), which promotes the formation and utilization of efficient data, thereby accelerating representation learning. Utilizing a ResNet-18 pre-trained on CIFAR-10 as a prior model to inform ResNet-50 training on ImageNet-1K reduces computational costs by 50% while maintaining the same accuracy as the model trained with the original BYOL, which requires 100% cost. Our code is available at: <https://github.com/LINs-lab/ReLA>.

<span id="page-0-1"></span>

## 1 Introduction

<span id="page-0-0"></span>Image /page/0/Figure/5 description: The image displays a two-part diagram illustrating a machine learning optimization process. Part 1, labeled '1', shows a workflow starting with a globe icon labeled 'Download', leading to a network diagram labeled 'Prior (ψ)' with nodes of different colors, then to a cylinder icon labeled 'Generate' containing various geometric shapes. An arrow labeled 'Feed' points from an 'Original' cylinder icon to a robot icon labeled 'Alg.', which then transforms into an 'Accelerated' robot icon labeled 'RELA+Alg.' via a clock icon. Part 2, labeled '2', depicts a 'Representation Space' with contour lines representing loss. It shows an initial point 'Init. φ', a star symbol labeled 'φ⋆' representing the optimal solution, and three paths: 'Original Alg.' in blue, 'Slow Learning' in dotted gray, and 'RELA+Alg.' in red and purple. A gradient bar on the right indicates 'Loss' from dark to light. The caption below reads 'Figure 1: Framework and Intuition of ReLA. (1) Framework, the data augmentation strategy. (2) Intuition, the optimization trajectory in the representation space.'

Figure 1: Framework and Intuition of RELA: (1) *Framework*: RELA serves as both a data optimizer and an auxiliary accelerator. Initially, it operates as a data optimizer by leveraging an dataset and a pre-trained model (e.g., one sourced from online repositories) to generate an efficient dataset. Subsequently, RELA functions as an auxiliary accelerator, enhancing existing (self-)supervised learning algorithms through the effective utilization of the efficient dataset, thereby promoting efficient representation learning. (2) *Intuition*: The central concept of RELA is to create an efficient-data-driven shortcut pathway within the learning process, enabling the initial model  $\phi$  to rapidly converge towards a 'proximal representation  $\psi$ ' of the target model  $\phi^*$  during the early stages of training. This approach significantly accelerates the overall learning process.

The available of massive datasets [\[20,](#page-11-0) [49\]](#page-12-0) and recent advances in parallel data processing [\[28,](#page-11-1) [42\]](#page-12-1) have facilitated the rapid evolution of large deep models, such as GPT-4 [\[1\]](#page-10-0) and LVM [\[2\]](#page-10-1). These

<sup>∗</sup>Corresponding author.

models excel in numerous learning tasks, attributable to their impressive representation capabilities. However, the emergence of vast amounts of data within the modern deep learning paradigm raises two fundamental challenges: (i) *the demand for human annotations of huge datasets consumes significant social resources [\[45,](#page-12-2) [20,](#page-11-0) [43\]](#page-12-3);* (ii) *training large models with increasing data and model capacity suffers from intensive computational burden [\[6,](#page-10-2) [16,](#page-10-3) [55\]](#page-13-0).*

The community has made considerable efforts to enhance learning efficiency. Self-supervised learning methods [\[12,](#page-10-4) [69,](#page-13-1) [30,](#page-11-2) [10,](#page-10-5) [26,](#page-11-3) [13,](#page-10-6) [9,](#page-10-7) [3\]](#page-10-8), with their superior representation learning devoid of human annotations via the self-learning paradigm, attempt to tackle the challenge (i). Concurrently, research has been conducted to mitigate data efficiency issues in challenge (ii): dataset distillation approaches [\[65,](#page-13-2) [56,](#page-13-3) [11,](#page-10-9) [72,](#page-14-0) [53,](#page-13-4) [54\]](#page-13-5) have successfully synthesized a small distilled dataset, on which models trained on this compact dataset can akin to one trained on the full dataset.

However, challenges (i) and (ii) persist and yet are far from being solved [\[43,](#page-12-3) [45,](#page-12-2) [6,](#page-10-2) [16,](#page-10-3) [55\]](#page-13-0), particularly the intervention of these two learning paradigms. In this paper, we identify two issues: (a) inefficiency in the self-supervised learning procedure compared to conventional supervised learning arises due to sub-optimal self-generating targets  $[30, 62]$  $[30, 62]$  $[30, 62]$ ; (b) although training on the distilled dataset is efficient and effective, the distillation process of optimization-based approaches [\[11,](#page-10-9) [72,](#page-14-0) [37\]](#page-12-4) is computationally demanding [\[18,](#page-10-10) [56\]](#page-13-3), often surpassing the computational load of training on the full dataset. This limitation restricts its potential to accelerate representation learning. To tackle these challenges, we propose a novel open problem in the domain of representation learning:

<span id="page-1-0"></span>Problem 1 (Accelerating Representation Learning through Free Models) . *According to the No Free Lunch theorem [\[66\]](#page-13-7), it is evident that accelerating the learning process without incorporating prior knowledge is inherently challenging. Fortunately, numerous publicly available pre-trained models can be accessed online, offering a form of free prior knowledge. Despite this, effectively utilizing these models poses several implicit challenges, as these models may not be directly relevant to our target learning task, or they may not be sufficiently well-trained. This leads to a question:*

How can we leverage task- and architecture-agnostic publicly available models to accelerate representation learning for a specific task?

To address [Problem 1](#page-1-0) , we propose RELA to utilize a freely available model downloaded from the internet to generate efficient data for training. This approach aims to accelerate training during the initial stages by effectively leveraging these generated data, thereby establishing a rapid pathway for representation learning (see [Figure 1](#page-0-0)). Specifically, we list our five key contributions below as the first step toward bridging representation learning with data-efficient learning:

(a) *Revealing beneficial/detrimental data properties for efficient/inefficient (self-)supervised learning (see [Section 3.2](#page-3-0) ).* We present a comprehensive analysis of linear models, demonstrating that data properties significantly influence the learning process by impacting the optimization of model training. Our findings reveal that modifications to the data can markedly enhance or impair this optimization. Additionally, we indicate that optimal training necessitates specific data properties—perfect bijective mappings between the samples and targets within a dataset.

(b) *Identifying the inefficiency problems of (self-)supervised learning from a data-centric perspective (see [Section 3.3](#page-4-0) ).* Specifically, we identify several factors contributing to the inefficiencies in (self- )supervised learning over real-world data. For instance, prevalent data augmentation techniques in modern deep learning can introduce a 'noisy mapping' issue, which may exacerbate the negative effects associated with inefficient data.

(c) *Generalization bound for models trained on optimized efficient data (see [Section 3.4](#page-5-0) ).* Although the efficiency properties of data do not inherently ensure the generalization of the trained model, i.e., efficient data alone cannot guarantee generalization ability, we present a generalization bound to analyze models trained on such data.

(d) *A novel method* RELA *to generate and exploit efficient data (see [Section 4](#page-6-0) ).* Leveraging our theoretical insights regarding the bounds of generalization and convergence rate, we introduce RELA, a novel optimization-free method tailored to efficiently generate and effectively exploit efficient data for accelerating representation learning.

(e) *An application of our* RELA*: accelerating (self-)supervised learning (see [Section 5](#page-7-0) and [Appendix I](#page-30-0) ).* Extensive experiments across four widely-used datasets, seven neural network architectures, eight self-supervised learning algorithms demonstrate the effectiveness and efficiency of RELA. Training models with RELA significantly outperforms training on the original dataset with the same budget, and even exceeds the performance of training on higher budget.

<span id="page-2-0"></span>

## 2 Related Work

This section integrates two distinct deep learning areas: (a) techniques to condense datasets while preserving efficacy; (b) self-supervised learning methods that enable training models on unlabeled data.

<span id="page-2-1"></span>

## 2.1 Dataset Distillation: Efficient yet Effective Learning Using Fewer Data

The objective of dataset distillation is to create a significantly smaller dataset that retains competitive performance relative to the original dataset.

Refining proxy metrics between original and distilled datasets. Traditional approaches involve replicating the behaviors of the original dataset within the distilled one. These methods aim to minimize discrepancies between surrogate neural network models trained on both synthetic and original datasets. Key metrics for this process include matching gradients [\[72,](#page-14-0) [32,](#page-11-4) [70,](#page-13-8) [44\]](#page-12-5), features [\[63\]](#page-13-9), distributions [\[71,](#page-13-10) [73\]](#page-14-1), and training trajectories [\[11,](#page-10-9) [17,](#page-10-11) [22,](#page-11-5) [18,](#page-10-10) [68,](#page-13-11) [24\]](#page-11-6). However, these methods suffer from substantial computational overhead due to the incessant calculation of discrepancies between the distilled and original datasets. The optimization of the distilled dataset involves minimizing these discrepancies, necessitating multiple iterations until convergence. As a result, scaling to large datasets, such as ImageNet [\[20\]](#page-11-0), becomes challenging.

Extracting key information from original into distilled datasets. A promising strategy involves identifying metrics that capture essential dataset information. These methods efficiently scale to large datasets like ImageNet-1K using robust backbones without necessitating multiple comparisons between original and distilled datasets. For instance,  $SRe^{2}L$  [\[67\]](#page-13-12) condenses the entire dataset into a model, such as pre-trained neural networks like ResNet-18 [\[25\]](#page-11-7), and then extracts the knowledge from these models into images and targets, forming a distilled dataset. Recently, RDED [\[56\]](#page-13-3) posits that images accurately recognized by strong observers, such as humans and pre-trained models, are more critical for learning.

**Summary.** We make the following observations regarding scalable dataset distillation methods utilizing various metrics: (a) a few of these metrics have proven effective for data distillation at the scale of ImageNet. (b) all these metrics require human-labeled data; (c) there is currently no established theory elucidating the conditions under which data distillation is feasible; (d) despite their success, the theory behind training neural networks with reduced data is underexplored.

<span id="page-2-2"></span>

### 2.2 Self-supervised Learning: Representation Learning Using Unlabeled Data

The primary objective of self-supervised learning is to extract robust representations without relying on human-labeled data. These representations should be competitive with those derived from supervised learning and deliver superior performance across multiple tasks.

Contrasting self-generated positive and negative Samples. Contrastive learning-based methods implicitly assign a one-hot label to each sample and its augmented versions to facilitate discrimination. Since InfoNCE [\[47\]](#page-12-6), various works [\[26,](#page-11-3) [12,](#page-10-4) [14,](#page-10-12) [8,](#page-10-13) [31,](#page-11-8) [15,](#page-10-14) [74,](#page-14-2) [40,](#page-12-7) [9,](#page-10-7) [27\]](#page-11-9) have advanced contrastive learning. MoCo [\[26,](#page-11-3) [14,](#page-10-12) [15\]](#page-10-14) uses a momentum encoder for consistent negatives, effective for both CNNs and Vision Transformers. SimCLR [\[12\]](#page-10-4) employs strong augmentations and a nonlinear projection head. Other methods integrate instance classification [\[8\]](#page-10-13), data augmentation [\[31,](#page-11-8) [74\]](#page-14-2), clustering [\[40,](#page-12-7) [9\]](#page-10-7), and adversarial training [\[27\]](#page-11-9). These enhance alignment and uniformity of representations on the hypersphere [\[64\]](#page-13-13).

Asymmetric model-generating representations as targets. Asymmetric network methods achieve self-supervised learning with only positive pairs [\[30,](#page-11-2) [50,](#page-12-8) [13\]](#page-10-6), avoiding representational collapse through asymmetric architectures. BYOL [\[30\]](#page-11-2) uses a predictor network and a momentum encoder. Richemond et al. [\[50\]](#page-12-8) show BYOL performs well without batch statistics. SimSiam [\[13\]](#page-10-6) halts the gradient to the target branch, mimicking the momentum encoder's effect. DINO [\[10\]](#page-10-5) employs a self-distillation loss. UniGrad [\[59\]](#page-13-14) integrates asymmetric networks with contrastive learning methods within a theoretically unified framework.

<span id="page-3-6"></span>

## 3 Revealing Critical Properties of Efficient Learning over Data

We begin by presenting formal definitions of supervised learning over a (efficient) dataset.

<span id="page-3-3"></span>**Definition 1 (Supervised learning over data)** . For a dataset  $D = (D_X, D_Y) = \{(\mathbf{x}_i, \mathbf{y}_i)\}_{i=1}^{|D|}$ , *drawn from the data distribution*  $(X, Y)$  *in space*  $(X, Y)$ *, the goal of a model learning algorithm* is to identify an optimal model  $\phi^{\star}$  that minimizes the expected error defined by:

<span id="page-3-1"></span>
$$
\mathbb{E}_{(\mathbf{x}, \mathbf{y}) \sim (X, Y)} \left[ \ell(\boldsymbol{\phi}^{\star}(\mathbf{x}), \mathbf{y}) \right] \le \epsilon, \tag{1}
$$

*where*  $\ell$  *indicates the loss function and*  $\epsilon$  *denotes a predetermined deviation. This is typically* achieved through a parameterized model φ<sub>θ</sub>, where θ denotes the model parameter within the *parameter space* Θ*. The optimal parameter* θ <sup>D</sup> *is determined by training the model to minimize the empirical loss over the dataset:*

<span id="page-3-2"></span>
$$
\boldsymbol{\theta}^D := \arg\min_{\boldsymbol{\theta} \in \Theta} \left\{ \mathcal{L}(\boldsymbol{\phi}_{\boldsymbol{\theta}}; D; \ell) \right\} := \arg\min_{\boldsymbol{\theta} \in \Theta} \left\{ \sum_{i=1}^{|D|} \ell(\boldsymbol{\phi}_{\boldsymbol{\theta}}(\mathbf{x}_i), \mathbf{y}_i) \right\}.
$$
 (2)

*The training process leverages an optimization algorithm such as stochastic gradient descent* [\[51,](#page-12-9) *[33\]](#page-11-10).*

<span id="page-3-5"></span>Definition 2 (Data-efficient Learning) . *Data-efficient learning seeks to derive an opti*mized/efficient dataset, denoted as  $S = (S_X, S_Y) = \{(\mathbf{x}_j, \mathbf{y}_j)\}_{j=1}^{|S|}$ , from the original dataset D*. The objective is to enable models* ϕ<sup>θ</sup> <sup>S</sup> *trained on* S *to achieve the desired generalization performance, as defined in* [\(1\)](#page-3-1)*, with fewer training steps and a reduced computational budget compared to training on the original dataset* D*.*

<span id="page-3-7"></span>

### 3.1 Unifying (Self-)Supervised Learning from a Data-Centric Perspective

To ease the understanding and our methodology design in [Section 3.4](#page-5-0) , we unify both conventional supervised learning and self-supervised learning as learning to map samples in  $D<sub>X</sub>$  to targets in  $D<sub>Y</sub>$ : this view forms 'supervised learning' from a data-centric perspective. Specifically, these two learning paradigms involve generating targets  $D_Y = \{y \mid y = \psi(x) \text{ s.t. } x \sim D_X\}$  and minimizing the empirical loss [\(2\)](#page-3-2). The only difference lies in the target generation models (or simply labelers)  $\psi$ :

- (a) *Conventional supervised learning*, referred to as human-supervised learning, generates targets via human annotation. Note that the targets are stored and used statically throughout the training.
- (b) *Self-supervised learning* (also see Footnote [2\)](#page-8-0), e.g., BYOL [\[30\]](#page-11-2) utilizes an Exponential Moving Average (EMA) version of the learning model  $\phi_{\theta}$  to generate targets  $y = EMA[\phi_{\theta}](x)$ . Note that the targets are dynamically changing during training as the model  $\phi_{\theta}$  keeps evolving.

This unified perspective allows us to jointly examine and address the inefficient training issue of (self-)supervised learning from a data-centric perspective, in which in [Section 3.2](#page-3-0) we first study the impact of samples  $D_X$  and targets  $D_Y$  on the model training process and then investigate whether and how a distilled dataset  $S = (S_X, S_Y)$  can facilitate this process.

<span id="page-3-0"></span>

### 3.2 Empirical and Theoretical Investigation of Data-Centric Efficient Learning

To elucidate the ideal data properties of training on a dataset  $D$ , we examine the simple task over a bimodal Gaussian mixture distribution as a case study. We begin by defining the problem.

<span id="page-3-4"></span>Definition 3 (Bimodal Gaussian mixture distribution) . *Given two Gaussian distributions*  $\mathcal{N}_0(\mu_1, \Sigma^2 \mathbf{I})$  and  $\mathcal{N}_1(\mu_2, \Sigma^2 \mathbf{I})$ , where  $\mu_1$  and  $\mu_2$  are the means and  $\Sigma^2$  is the variance (here we *set*  $\mu_1 = 1$ ,  $\mu_2 = 2$  *and*  $\Sigma = 0.5$ *). We define a bimodal mixture data*  $G = (G_X, G_Y)$  *as:* 

$$
G := \{(\mathbf{x}, y) \mid \mathbf{x} = (1 - y) \cdot \mathbf{x}_0 + y \cdot \mathbf{x}_1\} \text{ s.t. } y \sim \text{Bernoulli}(p = 0.5), \mathbf{x}_0 \sim \mathcal{N}_0, \mathbf{x}_1 \sim \mathcal{N}_1. \text{ (3)}
$$

*Moreover, we define a corresponding binary classification neural network model as:*

$$
f_{\theta}(\mathbf{x}) := \sigma \left( \theta^{[1]} \cdot \text{ReLU} \left( \theta^{[2]} \mathbf{x} + \theta^{[3]} \right) + \theta^{[4]} \right), \tag{4}
$$

*where*  $\sigma(z) = \frac{1}{1+e^{-z}}$  *is the sigmoid activation function;* ReLU( $z$ ) = max(0,  $z$ ) *is the activation function for the hidden layer, which provides non-linearity to the model;* θ [2] *and* θ [3] *are the* weights and biases of the hidden layer;  $\bm{\theta}^{[1]}$  and  $\bm{\theta}^{[4]}$  are the weights and biases of the output layer.

Modern representation learning fundamentally relies on optimization (see [Definition 1](#page-3-3)). We show that modifications to the data can influence the convergence rate of the optimization process, thereby impacting the overall representation learning procedure. Furthermore, we try to uncover several key properties of data efficiency through our theoretical analysis of the case study. In the following, we denote the modified distribution by  $G' = (G'_X, G'_Y)$  and examine the altered samples  $G'_X$  and corresponding targets  $G'_{Y}$  independently.

<span id="page-4-3"></span>Investigating the properties of modified samples. The modification process here only rescales the variance of the original sample distribution  $G_X$  defined in [Definition 3](#page-3-4) with new  $\Sigma$  (rather than the default 0.5), while let  $G'_Y := G_Y$ ; see explanations in [Appendix E](#page-24-0). Therefore, we examine the distilled samples  $G'_X$  by setting the variable  $\Sigma$  within the interval  $(0, 1)$ .

Results in [Figure 2](#page-5-1) demonstrate that the distilled samples  $G'_{X}$  with smaller variance  $\Sigma$  achieve faster convergence and better performance compared to that of  $G$ . To elucidate the underlying mechanism, we provide a rigorous theoretical analysis in [Appendix B](#page-17-0), culminating in [Theorem 1](#page-4-1).

<span id="page-4-1"></span>Theorem 1 (Convergence rate of learning on efficient samples) . *For the classification task stated in [Definition 3](#page-3-4), the convergence rate for the model f<sub>0</sub> trained t after steps over distilled data* G′ *is:*

$$
\mathbb{E}_{\theta_t} \left[ \mathcal{L}(f_{\theta_t}; G'; \ell) - \mathcal{L}(f_{\theta^{\star}}; G'; \ell) \right] \le \tilde{\mathcal{O}}(\Sigma^2) \,, \tag{5}
$$

*where*  $\ell$  *denotes the* MSE *loss, i.e.,*  $\ell(\hat{y}, y) := ||\hat{y} - y||^2$ , and  $f_{\theta^*}$  indicates the optimal model, *O* signifies the asymptotic complexity. Modified samples characterized by a smaller value of  $\Sigma$ facilitate faster convergence.

Investigating the properties of modified targets. On top of the property understanding for modified samples, we further investigate the potential of modified targets via  $G'$ . In detail, for modified samples, we consider the most challenging (c.f. [Figure 2b](#page-5-1)) yet the common case, namely  $G'_X$  with  $\Sigma = 1$  (see explanations in [Appendix M](#page-34-0)). For the corresponding modified targets  $G'_Y$ , similar to the prior data-efficient methods [\[56,](#page-13-3) [67\]](#page-13-12), for any sample x drawn from  $G'_{X}$ , we refine its label by assigning  $\hat{y} = \rho \cdot f_{\theta^*}(\mathbf{x}) + (1 - \rho) \cdot y$ . Here,  $\rho$  denotes the relabeling intensity coefficient, and  $f_{\theta^*}$ represents a strong pre-trained model (simply, we utilize the model trained on the data in [Figure 2c](#page-5-1) ).

<span id="page-4-2"></span>Theorem 2 (Convergence rate of learning on re-labeled data) . *For the classification task as in [Definition 3](#page-3-4)*, we have the convergence rate for the model  $f_{\theta}$  *trained after* t *steps over modified data* G′ *:*

$$
\mathbb{E}_{\theta_t} \left[ \mathcal{L}(f_{\theta_t}; G'; \ell) - \mathcal{L}(f_{\theta^{\star}}; G'; \ell) \right] \le \tilde{\mathcal{O}}(1 - \rho).
$$
 (6)

*Note that* ρ *controls the upper bound of the convergence rate, indicating that* using modified targets with a higher value of  $\rho$  enables faster convergence.

Results in [Figure 2](#page-5-1) illustrate that the modified targets  $G'_Y$  with higher values of  $\rho$  lead to faster training convergence and better performance. See theoretical analysis in [Theorem 2](#page-4-2) and [Appendix B](#page-17-0) .

<span id="page-4-0"></span>

### 3.3 Extended Understanding of Data-Centric Efficient Learning

The empirical and theoretical investigations regarding the properties of modified samples  $G'_{X}$  and targets  $G'_Y$  in [Section 3.2](#page-4-3) are limited to a simplified case (as described in [Definition 3](#page-3-4)) and may not extend to all practical scenarios, such as training a ResNet [\[25\]](#page-11-7) on the ImageNet dataset [\[20\]](#page-11-0).

Interestingly, we observe that the advantageous modifications of both samples  $G'_{X}$  and targets  $G'_{Y}$ converge towards a unified principle: minimizing or preventing any sample x from being labeled with multiple or inaccurate targets y. This principle emphasizes the importance of providing accurate and informative targets y for each sample x, as analyzed in [Remark 1](#page-5-2) , and suggests extending this insight to any complex dataset like S.

<span id="page-5-1"></span>Image /page/5/Figure/0 description: The image displays three plots. The leftmost plot is a contour plot showing validation loss with contour lines and a color bar ranging from -2.7 to -0.9. It illustrates the paths of three different optimization runs, starting from an initial parameter (yellow circle) and converging towards a global minimum (black star). The paths are colored red (0.1), blue (0.5), and green (1.0), corresponding to different sigma values. The middle plot is a line graph showing validation loss over training steps, with multiple lines representing different sigma values from 0.1 to 1.0, colored from purple to yellow. The rightmost plot is a scatter plot showing two clusters of points, one colored blue and the other red, with a color bar ranging from 0.0 to 1.0, likely representing some feature or classification of the data points.

### (a) Loss landscape for $\Sigma$

(b) Evaluate  $G'_X$  with different  $\Sigma$ 

(c) Optimal  $G'_X$  ( $\Sigma = 0.1$ )

Figure 2: Investigating modified samples with varied  $\Sigma$  values. Following [\[39\]](#page-12-10), Figure [2a](#page-5-1) visualizes the validation loss landscape within a two-dimensional parameter space, along with three training trajectories corresponding to different  $\Sigma$  settings. Figure [2b](#page-5-1) illustrates the performance of models trained using samples with varied Σ. The optimal case in our task, utilizing samples with  $\Sigma = 0.1$  (which achieves the lowest validation loss in Figure [2b\)](#page-5-1), is visualized in Figure [2c,](#page-5-1) where the color bar represents the values of targets y.

<span id="page-5-3"></span>Image /page/5/Figure/5 description: The image displays three plots. The leftmost plot is a contour plot showing parameter trajectories for different values of rho (0.1, 0.5, 1.0) starting from an initial parameter (yellow circle) and converging to a global minimum (black star). The background of this plot is a heatmap representing the loss function, with darker blues indicating lower loss and yellow indicating higher loss. The middle plot is a line graph showing validation loss over training steps for various rho values ranging from 0.1 to 1.0. The validation loss generally decreases with training steps, and higher rho values appear to lead to lower final validation loss. The rightmost plot is a scatter plot where each point represents a data point, colored according to a color bar that ranges from blue (0.0) to red (1.0). The x and y axes of this plot range from approximately -0.5 to 3.5. The distribution of points suggests a separation based on the color-coded value, with blue points clustered in the lower-left and red points in the upper-right.

(a) Loss landscape for  $\rho$ 

(b) Evaluate  $G_Y'$  with different  $\rho$ 

(c) Optimal  $G'_Y$  ( $\rho = 1.0$ )

Figure 3: Investigating modified targets with varied  $\rho$  values. We present a visualization of the validation loss landscape in Figure [3a,](#page-5-3) including three training trajectories that correspond to different  $\rho$  settings. Figure [3b](#page-5-3) illustrates the performance of models trained using targets with varying  $\rho$  values. The optimal scenario for our task, which uses targets with  $\rho = 1.0$ , is depicted in Figure [3c.](#page-5-3)

<span id="page-5-2"></span>Remark 1 (Ideal data properties avoid implicitly introduced gradient noise from data) . *Intuitively, the semantic information within each sample* x *should be unique and not identical to another sample. Consequently, the exact target* y*, which represents the semantic information of* x*, should also be unique and informative. This implies the necessity of establishing bijective (or one-to-one) mappings between samples and their respective targets.*

*In contrast, when a sample* x *(or several similar samples) is labeled with multiple different targets* y*, it may implicitly introduce noise into the gradient* ∇ℓ(x, y)*, thereby hindering the optimization.*

However, real-world datasets often deviate from the ideal properties described above, as discussed in [Remark 2](#page-5-4) below and further analyzed in [Appendix M](#page-34-0) .

<span id="page-5-4"></span>Remark 2 (Imperfect mappings and inaccurate targets in real-world datasets) . *In practice, we observe that 'noisy mappings' between input samples and targets are prevalent in real-world datasets. As illustrated in [Figure 6](#page-35-0) , several common phenomena contribute to this issue:*

- *Similar or identical input samples may be assigned different targets due to using data augmentations, which is common in both self-supervised and human-supervised learning settings.*
- *Inaccurate targets may be generated, particularly in self-supervised learning scenarios.*
- *In human-supervised learning, all samples within a class are mapped to a one-hot target.*

*These imperfect mappings and inaccurate targets pose challenges to achieving optimal training efficiency and effectiveness for real-world datasets.*

<span id="page-5-0"></span>

### 3.4 Generalization-bounded Efficient Data Synthesis

Given insights from [Remark 1](#page-5-2), an effective approach to generate efficient data  $S$  from original data D involves employing a high-quality labeler  $\psi$  to relabel each sample x within D. This process

results in the formation of an optimized dataset S. However, the generalization ability of models trained on these optimized datasets S is not inherently assured. For a given labeler  $\psi$ , we derive the generalization bound, which quantifies the representation distance between models  $\phi_{\theta}$  and  $\phi^*$ , trained on the optimized dataset S. This is presented in [Theorem 3](#page-6-1) (see [Appendix D](#page-20-0) for the proof).

Definition 4 (Representation distance*[a](#page-6-2)* ) . *We introduce our proposed metric as*

$$
\mathcal{D}_{\text{Rep}}(\phi_S \to \phi_T; D) := \inf_{\mathbf{W} \in \mathbb{R}^{m \times n}, \mathbf{b} \in \mathbb{R}^m} \left\{ \mathbb{E}_{\mathbf{x} \sim D} \left[ \ell(\mathbf{W} \phi_S(\mathbf{x}) + \mathbf{b}, \phi_T(\mathbf{x})) \right] \right\},\tag{7}
$$

*which quantifies the distance between a source model*  $\phi_S$  *and a target model*  $\phi_T$  *with respect to a dataset D, and the loss function is defined as*  $\ell(\hat{y}, y) := \mathbf{1}(\hat{y} \neq y)$ *.* 

<span id="page-6-2"></span><sup>a</sup>Intuitively, a smaller  $D_{\text{Rep}}(\phi_S \to \phi_T; D)$  indicates that the model  $\phi_S$  can be transformed into  $\phi_T$  over data D via a linear model with relative ease. This also implies that  $\phi_S$  can achieve the same linear evaluation performance as  $\phi_T$  on data D.

<span id="page-6-1"></span>**Theorem 3 (Generalization bound with labeler**  $\psi$ **).** Assumimg the model  $\phi_{\theta} : \mathcal{X} \to \mathcal{Y}$  belongs *to a hypothesis space*  $\Phi$ *. Then, for any*  $\delta \in (0, 1)$ *, with probability at least*  $1 - \delta$ *, we have* 

<span id="page-6-5"></span> $D_{\text{Rep}}(\phi_{\theta} \to \phi^{\star}; X) \leq B_{\text{Sample}} + B_{\text{Target}} + B_{\text{Model}}$ , (8)

 $\textit{where } \text{B}_{\text{Target}} = \text{D}_{\text{Rep}}(\boldsymbol{\psi} \rightarrow \boldsymbol{\phi}^{\star}; D_X)$  ,  $\text{B}_{\text{Model}} = \text{D}_{\text{Rep}}(\boldsymbol{\phi}_{\boldsymbol{\theta}} \rightarrow \boldsymbol{\psi}; S_X) + 2\mathfrak{R}_{D_X}(\Phi) + \tilde{\mathcal{O}}(|D_X|^{-1})$ *, and*  $\mathbf{B}_{\text{Sample}} = \mathbf{D}_{\text{TV}}(S_X, D_X)$  *.*  $\mathbf{D}_{\text{TV}}$  *represents the total variation divergence* [\[4\]](#page-10-15)*, and*  $\Re_{D_X}$  *is the empirical Rademacher complexity.*

Drawing insights from [Theorem 1](#page-4-1), [2](#page-4-2) and [3,](#page-6-1) we define the properties of ideal data below:

<span id="page-6-4"></span>Definition 5 (Properties of ideal efficient data, including samples  $S_X$  and targets  $S_Y$ ). *To meet the objectives of [Definition 2](#page-3-5), an ideal efficient data requires:*  $\mathcal{D}$  *Targets* ( $S_Y$ *):* 

- generating targets  $\psi(\mathbf{x})$  *from the labeler*  $\psi$  *that are accurate (i.e., align with human-annotating*  $a$  *targets<sup>a</sup>* and optimal model  $\phi^*$ ), aiming to minimize  $D_{\text{Rep}}(\psi \to \phi^*; D_X)$  ;
- *the generated target*  $\psi(\mathbf{x})$  *should be informative to corresponding sample*  $\mathbf{x}$  *(i.e., forming perfect bijective mappings with the original samples* x*), according to [Remark 5](#page-6-4) ;*

 $\circled{2}$  *Samples* (S<sub>X</sub>): low distribution disparity represented by  $D_{TV}(S_X, D_X)$  and high sample *diversity denoted as*  $|S_X|$ *, aiming at minimizing*  $D_{TV}(S_X, D_X)$ .

<span id="page-6-3"></span>*<sup>a</sup>*Alignment with human-annotated targets can occur via direct prediction or linear transportability.

To meet the requirement  $\circledcirc$  specified in [Remark 5](#page-6-4), we utilize *weak* data augmentation on  $D_X$  to generate the set  $S_X$ , aimed at enhancing the diversity  $|S_X|$  while ensuring the term  $D_{TV}(S_X, D_X)$ remains minimal. However, satisfying requirement  $\mathcal D$  is non-trivial, as capturing a labeler  $\psi$  that matches  $\phi^*$  is intractable (i.e., achieving  $\overrightarrow{D}_{\text{Rep}}(\psi \to \phi^*; D_X) = 0$  is challenging).

Fortunately, our experiments in real-world scenarios, as discussed in [Section A](#page-16-0) , demonstrate that employing a prior model as the labeler  $\psi$  generally approximates  $\phi^*$  within the *representation space*. Therefore, we posit that introducing a prior model  $\psi$  to generate an efficient dataset S can typically accelerate the early stages of learning. Subsequently, the original dataset  $D$  should be employed.

In [\(8\)](#page-6-5), the last term,  $D_{\text{Rep}}(\phi_{\theta} \to \psi; S_X) + 2\Re_{D_X}(\Phi) + \tilde{\mathcal{O}}(|D_X|^{-1})$ , includes  $2\Re_{D_X}(\Phi)$  and  $\tilde{\mathcal{O}}(|D_X|^{-1})$ , which depend on the neural architecture and the size of  $D_X$ , respectively, and can be considered constants. Thus, optimizing the model involves minimizing  $D_{\text{Rep}}(\phi_{\theta} \to \psi; S_X)$  through training  $\phi_{\theta}$ . A detailed technical solution is provided in [Section 4](#page-6-0).

<span id="page-6-0"></span>

## 4 Methodology

Building upon the theoretical insights from [Section 3.2](#page-3-0), we propose our RELA (see [Figure 1](#page-0-0)): (a) RELA-D ( $\bullet$ ) is used to generate efficient data (c.f. [Section 4.1](#page-7-1)); (b) RELA-F ( $\bullet$ ) guides the models to train over the efficient data from RELA-D  $(\mathcal{L})$  (c.f. [Section 4.2](#page-7-2)).

<span id="page-7-1"></span>

### 4.1 RELA-D $\left( \mathcal{L} \right)$ : Synthesis of Efficient Dataset

Motivated by two property requirements in [Definition 5](#page-6-4), here we introduce our optimization-free synthesis process of both samples and targets in our RELA-D (see technical details in [Appendix F](#page-26-0)).

Generating transportable representations as the targets. We argue that *well-trained models (called* prior models*) on diverse real-world datasets using various neural network architectures and algorithms converge towards the same linear representation space. In other words, the generated pseudo representations*  $R<sub>Y</sub>$  *for samples*  $D<sub>X</sub>$  *using these prior models are linearly transportable to each other and to the human-annotating targets.* The empirical verifications refer to [Appendix A](#page-16-0) . We further justify in [Appendix F.1](#page-26-1) that the requirement  $\mathcal D$  in [Definition 5](#page-6-4) can be achieved by employing a prior model as the ideal labeler  $\psi : \mathbb{R}^d \to \mathbb{R}^m$ , i.e., generating  $R_Y = \{ \psi(\mathbf{x}) \mid \mathbf{x} \sim D_X \}$  as the targets. The generation process of targets is conducted only once (refer to [Appendix H](#page-30-1) for details), and the generated targets  $R_Y$  are stored and combined with the samples  $D_X$  to form the data  $D = (D_X, R_Y)$ .

Efficient and distribution-aligned sample generation. To satisfy requirement  $\circled{2}$  in [Definition 5](#page-6-4) efficiently, we employ basic data augmentations into data  $D<sub>X</sub>$  such as RandomResizeCrop with a minimum scale of 0.5 (as opposed to the default of 0.08) and RandomHorizontalFlip with  $p = 0.5$ .

<span id="page-7-2"></span>

### 4.2 RELA-F $(\bigtriangledown)$ : Assist Learning with Generated Efficient Dataset

In this section, we showcase the significance of understanding ideal data properties and generated efficient dataset in assisting self-supervised learning, given this self-supervised paradigm on unlabeled data suffers from significant inefficiency issues compared to human-supervised learning [\[62\]](#page-13-6).

Here we propose a plug-and-play method that can be seamlessly integrated into any existing selfsupervised learning algorithm, significantly enhancing its training efficiency by introducing an additional loss term. Formally, the loss function is defined as follows:

<span id="page-7-3"></span>
$$
\lambda \cdot \mathcal{L}_{\text{RELA}} + (1 - \lambda) \cdot \mathcal{L}_{\text{SSL}} \text{, where } \mathcal{L}_{\text{RELA}} := \mathbb{E}_{\mathbf{x}, \mathbf{y} \sim (D_X, R_Y)} \left[ \ell(\mathbf{W} \phi_{\theta}(\mathbf{x}) - \mathbf{b}, \mathbf{y}) \right], \quad (9)
$$

where  $\ell(\mathbf{z}, \mathbf{y}) := 1 - \mathbf{z} \cdot \mathbf{y}/(\|\mathbf{z}\| \|\mathbf{y}\|)$  be the loss function,  $\mathcal{L}_{\text{SSL}}$  denotes the loss specified by any self-supervised learning method, respectively. Furthermore, the data  $D = (D_X, R_Y)$  are collected using the strategy outlined in [Section 4.1](#page-7-1), with updates occurring at each  $k$ -th epoch.

The dynamic coefficient  $\lambda \in \{0, 1\}$  divides the training process into two distinct stages. Initially,  $\lambda$  is set to 1 to emphasize  $\mathcal{L}_{\text{RELA}}$ , assuming its crucial role in the early learning phase. As the model  $\phi_{\theta}$ improves and self-generated targets become more reliable in  $\mathcal{L}_{\text{SSL}}$ , an adaptive attenuation algorithm adjusts  $\lambda$  to 0 (note that the initial  $\lambda$  is tuning-free for all cases and see [Appendix J](#page-31-0) for details). As a result, only a single loss term in [\(9\)](#page-7-3) is calculated, ensuring no extra computational cost with RELA.

To enhance the recognition of RELA-aided algorithms, we re-denote those that are used in their names. For example, the BYOL algorithm [\[30\]](#page-11-2), when enhanced with RELA, is re-denoted as BYOL  $(\bigtriangledown)$ . Furthermore, as the prior models downloaded from the internet are not consistently robust, the aforementioned dynamic setting of  $\lambda$  also prevents the model  $\phi_{\theta}$  from overfitting to potentially weak generated targets. The efficacy of our proposed RELA is empirically validated in [Section 5](#page-7-0) .

<span id="page-7-0"></span>

## 5 Experiments

This section describes the experimental setup and procedures undertaken to test our hypotheses and evaluate the effectiveness of our proposed methodologies.

**Experimental setting.** We list the settings below (see more details in [Appendix K](#page-31-1)).

• *Datasets:* For low-resolution data  $(32 \times 32)$ , we evaluate our method on two datasets, i.e., CIFAR-10 [\[35\]](#page-11-11) and CIFAR-100 [\[34\]](#page-11-12). For high-resolution data, we conduct experiments on two large-scale datasets including Tiny-ImageNet ( $64 \times 64$ ) [\[36\]](#page-12-11) and full ImageNet-1K ( $224 \times 224$ ) [\[20\]](#page-11-0), to assess the scalability and effectiveness of our method on more complex and varied datasets.

• *Neural network architectures:* Similar to prior works/benchmarks of dataset distillation [\[56\]](#page-13-3) and self-supervised learning [\[57,](#page-13-15) [19\]](#page-11-13), we use several backbone architectures to evaluate the generalizability

<span id="page-8-1"></span>Table 1: Benchmark our RELA with various prior models against BYOL. We compare evaluation results of the models trained using • BYOL with 10%, 20% and 50% training budget/steps; • BYOL ( $\blacklozenge$ ) with different prior models; • BYOL with full budget, denoted as BYOL<sup>\*</sup> in this table. Regarding the prior models used for our RELA, we respectively utilize six models with increasing representation capabilities, including • randomly initialized network (Rand.); • four BYOL<sup>\*</sup>-trained models (CF10-T, CF100-T, TIN-T, IN1K-T) corresponding to four datasets (listed below); • CLIP-RN50. The evaluations are performed across four datasets, i.e., CIFAR-10 (CF-10), CIFAR-100 (CF-100), Tiny-ImageNet (T-IN), and ImageNet-1K (IN-1K). We underline the results that outperform the full training, and bold the results that achieve the highest performance using a specific ratio of budget. All the networks used for training are ResNet-18, except the ResNet-50 used for IN-1K.

| Dataset | %  | BYOL (⚡) w/       |            |            |            |            |                   |                   | $BYOL*$    |
|---------|----|-------------------|------------|------------|------------|------------|-------------------|-------------------|------------|
|         |    | <b>BYOL</b>       | Rand.      | $CF10-T$   | $CF100-T$  | TIN-T      | $IN1K-T$          | CLIP-RN50         |            |
| CF-10   | 10 | <b>58.3 ± 0.1</b> | 71.4 ± 0.0 | 81.1 ± 0.1 | 78.2 ± 0.1 | 79.6 ± 0.1 | 81.6 ± 0.0        | <b>82.0 ± 0.1</b> | 82.7 ± 0.2 |
| CF-10   | 20 | <b>70.1 ± 0.2</b> | 77.1 ± 0.2 | 83.6 ± 0.1 | 81.4 ± 0.0 | 83.2 ± 0.1 | <b>84.4 ± 0.1</b> | 83.9 ± 0.1        |            |
| CF-10   | 50 | <b>77.9 ± 0.0</b> | 82.7 ± 0.1 | 86.5 ± 0.1 | 86.2 ± 0.0 | 86.2 ± 0.1 | <b>87.3 ± 0.2</b> | 86.7 ± 0.0        |            |
| CF-100  | 10 | 26.9 ± 0.2        | 41.8 ± 0.2 | 51.4 ± 0.1 | 51.4 ± 0.1 | 53.5 ± 0.1 | <b>56.4 ± 0.2</b> | 55.4 ± 0.1        |            |
| CF-100  | 20 | 34.8 ± 0.3        | 48.1 ± 0.1 | 55.7 ± 0.1 | 55.7 ± 0.1 | 56.7 ± 0.0 | <b>59.5 ± 0.1</b> | 57.9 ± 0.0        |            |
| CF-100  | 50 | 41.4 ± 0.3        | 54.6 ± 0.2 | 59.7 ± 0.1 | 59.8 ± 0.1 | 60.0 ± 0.1 | <b>61.6 ± 0.1</b> | 61.0 ± 0.0        | 52.5 ± 0.3 |
| T-IN    | 10 | 25.1 ± 0.3        | 34.5 ± 0.3 | 39.0 ± 0.1 | 38.4 ± 0.0 | 41.2 ± 0.1 | <b>41.6 ± 0.1</b> | 39.6 ± 0.4        |            |
| T-IN    | 20 | 30.7 ± 0.1        | 38.2 ± 0.0 | 41.9 ± 0.0 | 42.3 ± 0.0 | 43.2 ± 0.1 | <b>44.1 ± 0.1</b> | 42.6 ± 0.1        |            |
| T-IN    | 50 | 37.7 ± 0.2        | 43.9 ± 0.1 | 45.6 ± 0.1 | 45.9 ± 0.1 | 45.8 ± 0.1 | <b>46.4 ± 0.1</b> | 46.3 ± 0.1        | 43.6 ± 0.3 |
| IN-1K   | 10 | 44.5 ± 0.1        | 51.7 ± 0.1 | 53.7 ± 0.1 | 53.3 ± 0.1 | 53.6 ± 0.1 | <b>54.9 ± 0.1</b> | 56.2 ± 0.1        |            |
| IN-1K   | 20 | 55.3 ± 0.0        | 56.9 ± 0.0 | 57.6 ± 0.1 | 57.6 ± 0.1 | 57.8 ± 0.1 | <b>58.0 ± 0.0</b> | 59.5 ± 0.1        |            |
| IN-1K   | 50 | 60.8 ± 0.2        | 61.1 ± 0.1 | 62.1 ± 0.1 | 61.8 ± 0.1 | 61.7 ± 0.0 | <b>61.9 ± 0.0</b> | 62.9 ± 0.1        | 61.9 ± 0.1 |

of our method, including ResNet-{18, 50, 101} [\[25\]](#page-11-7), EfficientNet-B0 [\[58\]](#page-13-16), MobileNet-V2 [\[52\]](#page-12-12), ViT [\[21\]](#page-11-14), and a series of CLIP-based models [\[49\]](#page-12-0). These architectures represent a range of model complexities and capacities, enabling a comprehensive assessment of our approach.

• *Baselines:* Referring to a prior widely-used benchmark [\[57,](#page-13-15) [19\]](#page-11-13), we consider several state-of-the-art methods as baselines for a broader practical impact, including: SimCLR [\[12\]](#page-10-4), Barlow Twins [\[69\]](#page-13-1), BYOL [\[30\]](#page-11-2), DINO [\[10\]](#page-10-5), MoCo [\[26\]](#page-11-3), SimSiam [\[13\]](#page-10-6), SwAV [\[9\]](#page-10-7), and Vicreg [\[3\]](#page-10-8).

• *Evaluation:* Following previous benchmarks and research [\[57,](#page-13-15) [19,](#page-11-13) [12,](#page-10-4) [3\]](#page-10-8), we evaluate all the trained models using offline linear probing strategy to reflect the representation ability of the trained models, and ensure a fair and comprehensive comparison with baseline approaches.

• *Implementation details:* We implement our method by extending a popular self-supervised learning open-source benchmark [\[57\]](#page-13-15) and use their configurations therein. This includes using AdamW as the optimizer, with a mini-batch size of 128 (except for ImageNet-1K, where we use a mini-batch size of 512). We implement our method through PyTorch [\[48\]](#page-12-13), and all experiments are conducted on NVIDIA RTX 4090 GPUs. See more detailed configurations and hyper-parameters in [Appendix K](#page-31-1) .

<span id="page-8-2"></span>

### 5.1 Primary Experimental Results and Analysis

Recall that our RELA-D ( $\&$ ), as illustrated in [Figure 1](#page-0-0) and [Section 4.1](#page-7-1), requires an unlabeled dataset and *any pre-trained model freely available online* to generate the efficient dataset. To justify the superior performance and generality of our RELA across various unlabeled datasets using prior models with different representation abilities, our comparisons in this subsection start with  $\rm BVOL$   $[30]^2$  $[30]^2$  $[30]^2$  and then extend to other self-supervised learning methods.

[Table 1](#page-8-1) demonstrates the efficacy and efficiency of our RELA in facilitating the learning of robust representations. Overall, *BYOL ( ) consistently outperforms the original BYOL* when trained with a reduced budget. In certain cases, such as on CIFAR-100, BYOL ( $\oint$ ) employing only 10% of the budget can surpass the performance of BYOL-trained models using the entire budget Specifically:

- (a) A stronger prior model (e.g., CLIP) enhances the performance of RELA more effectively than a weaker model (e.g., Rand.);
- (b) Our RELA is not sensitive to the prior knowledge. For instance, using CF10-T as the prior model can achieve competitive performance compared to that trained on extensive datasets (e.g., CLIP);

<span id="page-8-0"></span><sup>&</sup>lt;sup>2</sup>Note that (1) BYOL is competitive across various datasets [\[30,](#page-11-2) [3,](#page-10-8) [57,](#page-13-15) [12\]](#page-10-4), and (2) various self-supervised learning methods can be unified in the same framework [\[59\]](#page-13-14) (see our detailed analysis in [Appendix G](#page-27-0) ).

<span id="page-9-0"></span>Table 2: Evaluating our RELA on cross-architecture settings. Our RELA-D  $\mathcal{S}$  distills datasets with prior RN18 (Rand.) and CLIP-{RN101, RN50×4, ViT B/32, ViT B/16, ViT L/14}, then versus transfer to ResNet-18; MobileNet-V2; EfficientNet-B0; ViT T/16. We train models using  $10\%$  budget through (original) BYOL ( $\blacklozenge$ ).

| Dataset | Arch.           | Original       | (⚡) w/ RN18    | RN101          | RN50x4         | ViT B/32       | ViT B/16       | ViT L/14       |
|---------|-----------------|----------------|----------------|----------------|----------------|----------------|----------------|----------------|
| CF-10   | ResNet-18       | $58.3 \pm 0.1$ | $71.4 \pm 0.0$ | $81.9 \pm 0.1$ | $82.1 \pm 0.3$ | $83.2 \pm 0.2$ | $83.1 \pm 0.1$ | $82.4 \pm 0.1$ |
|         | MobileNet-V2    | $47.7 \pm 0.1$ | $69.4 \pm 0.0$ | $82.2 \pm 0.1$ | $80.8 \pm 0.0$ | $81.6 \pm 0.1$ | $82.9 \pm 0.2$ | $81.2 \pm 0.2$ |
|         | EfficientNet-B0 | $23.9 \pm 0.2$ | $68.8 \pm 0.6$ | $83.2 \pm 0.2$ | $83.9 \pm 0.1$ | $87.4 \pm 0.1$ | $86.4 \pm 0.1$ | $83.1 \pm 0.1$ |
|         | ViT T/16        | $43.4 \pm 0.1$ | $57.1 \pm 0.1$ | $65.9 \pm 0.0$ | $66.4 \pm 0.1$ | $69.9 \pm 0.3$ | $68.8 \pm 0.1$ | $63.7 \pm 0.1$ |
| T-IN    | ResNet-18       | $25.1 \pm 0.3$ | $34.5 \pm 0.3$ | $38.3 \pm 0.1$ | $39.1 \pm 0.4$ | $35.8 \pm 0.1$ | $32.4 \pm 0.1$ | $28.4 \pm 0.2$ |
|         | MobileNet-V2    | $8.8 \pm 0.1$  | $28.3 \pm 0.3$ | $39.9 \pm 0.1$ | $36.8 \pm 0.2$ | $36.0 \pm 0.0$ | $37.9 \pm 0.3$ | $20.6 \pm 0.5$ |
|         | EfficientNet-B0 | $4.1 \pm 0.0$  | $33.2 \pm 0.3$ | $43.5 \pm 0.2$ | $41.7 \pm 0.2$ | $44.0 \pm 0.1$ | $44.2 \pm 0.0$ | $37.9 \pm 0.1$ |
|         | ViT T/16        | $12.5 \pm 0.1$ | $24.6 \pm 0.0$ | $26.1 \pm 0.1$ | $27.6 \pm 0.1$ | $26.9 \pm 0.2$ | $24.5 \pm 0.1$ | $21.6 \pm 0.0$ |

<span id="page-9-1"></span>Table 3: Evaluating our RELA across different self-supervised learning methods. We extend our analysis beyond BYOL by training and evaluating models using seven additional self-supervised learning methods, along with their RELA-augmented counterparts  $(\bigtriangledown)$ , utilizing randomly initialized ResNet-18 (Rand.) and CLIP-RN50 as prior models for the RELA-D  $\circ$ , All methods are trained using 10% budget.

|                | --------------------------------------                                                                                                                                                                                              |                                                                                                                                                                            |                                                                                                          |      |      |              |  |        |
|----------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------|------|------|--------------|--|--------|
| <b>Dataset</b> | Method                                                                                                                                                                                                                              | $\mid$ SimCLR                                                                                                                                                              | Barlow                                                                                                   | DINO | MoCo | SimSiam SwAV |  | Vicreg |
| $CF-10$        | $(\frac{1}{2})$ w/ Rand. $70.9 \pm 0.0$ 68.8 $\pm$ 0.2 70.6 $\pm$ 0.1 70.9 $\pm$ 0.1 66.7 $\pm$ 0.1 69.5 $\pm$ 0.2 71.3 $\pm$ 0.1<br>CLIP-RN50   76.4 ± 0.1  76.5 ± 0.2  82.4 ± 0.1  79.8 ± 0.1  79.3 ± 0.1  77.3 ± 0.0  80.1 ± 0.1 | Original $\begin{array}{ l} \n70.7 \pm 0.2 \quad 63.7 \pm 0.3 \quad 66.2 \pm 0.2 \quad 67.4 \pm 0.4 \quad 45.8 \pm 0.4 \quad 66.2 \pm 0.3 \quad 71.3 \pm 0.2\n\end{array}$ |                                                                                                          |      |      |              |  |        |
| T-IN           | Original<br>$(4)$ w/ Rand. $30.7 \pm 0.2$ $31.9 \pm 0.1$ $29.4 \pm 0.2$ $33.4 \pm 0.1$ $25.4 \pm 0.1$ $29.1 \pm 0.2$ $34.1 \pm 0.1$<br>CLIP-RN50                                                                                    | $130.4 \pm 0.1$ 28.9 $\pm$ 0.4 26.7 $\pm$ 0.3 27.1 $\pm$ 0.2 17.8 $\pm$ 0.3 20.2 $\pm$ 0.1 34.0 $\pm$ 0.1                                                                  | $33.0 \pm 0.3$ $33.5 \pm 0.2$ $35.1 \pm 0.0$ $37.1 \pm 0.1$ $32.6 \pm 0.1$ $32.6 \pm 0.1$ $39.1 \pm 0.2$ |      |      |              |  |        |

(c) A randomly initialized model can effectively aid in accelerating learning through our RELA. This can be considered an effective scenario of "weak-to-strong supervision" [\[7\]](#page-10-16) using pseudo targets.

Cross-architecture generalization. RELA-D ( $\clubsuit$ ) generates efficient datasets using a specific neural architecture. To evaluate the generalization ability of these datasets, it is essential to test their performance on various architectures not used in the distillation process. [Table 2](#page-9-0) presents the performance of our RELA in conjunction with various prior models and trained model architectures, demonstrating its robust generalization ability. Specifically:

- (a) The integration of RELA always enhances the performance of original BYOL;
- (b) Our RELA method exhibits minimal sensitivity to the architecture of the prior model, as evidenced by the comparable performance of  $BYOL$  ( $\bigtriangledown$ ) using both ViT-based and ResNet-based models.

Combining RELA across various self-supervised learning methods. To demonstrate the effectiveness and versatility of RELA in enhancing various self-supervised learning methods, we conduct experiments with widely-used techniques. [Table 3](#page-9-1) presents the results, highlighting the robust generalization capability of RELA. Our findings consistently show that RELA improves the performance of these methods while maintaining the same budget ratio, emphasizing its potential on learning using unlabeled data. Additionally, we provide the results when combining RELA with human-supervised learning in [Appendix I](#page-30-0) .

<span id="page-9-2"></span>

## 6 Conclusion and Limitation

In this paper, to address the [Problem 1](#page-1-0) , we investigate the optimal properties of data, including samples and targets, to identify the properties that improve generalization and optimization in deep learning models. Our theoretical insights indicate that targets which are informative and linearly transportable to strong representations (e.g., human annotations) enable trained models to exhibit robust representation abilities. Furthermore, we empirically find that well-trained models (called prior models) across various tasks and architectures serve as effective labelers for generating such targets. Consequently, we propose the Representation Learning Accelerator (RELA), which leverages any freely available prior model to generate high-quality targets for samples. Additionally, RELA can enhance existing (self-)supervised learning approaches by utilizing these generated data to accelerate training. However, our theoretical analysis is restricted to the simplified scenario described in [Definition 3](#page-3-4) , which has limited applicability in real-world contexts.

## References

- <span id="page-10-0"></span>[1] Josh Achiam, Steven Adler, Sandhini Agarwal, Lama Ahmad, Ilge Akkaya, Florencia Leoni Aleman, Diogo Almeida, Janko Altenschmidt, Sam Altman, Shyamal Anadkat, et al. Gpt-4 technical report. *arXiv preprint arXiv:2303.08774*, 2023.
- <span id="page-10-1"></span>[2] Yutong Bai, Xinyang Geng, Karttikeya Mangalam, Amir Bar, Alan Yuille, Trevor Darrell, Jitendra Malik, and Alexei A Efros. Sequential modeling enables scalable learning for large vision models. *arXiv preprint arXiv:2312.00785*, 2023.
- <span id="page-10-8"></span>[3] Adrien Bardes, Jean Ponce, and Yann LeCun. Vicreg: Variance-invariance-covariance regularization for self-supervised learning. *arXiv preprint arXiv:2105.04906*, 2021.
- <span id="page-10-15"></span>[4] Andrew R Barron, Lhszl Gyorfi, and Edward C van der Meulen. Distribution estimation consistent in total variation and in two types of information divergence. *IEEE transactions on Information Theory*, 38(5):1437–1454, 1992.
- <span id="page-10-17"></span>[5] Shai Ben-David, John Blitzer, Koby Crammer, Alex Kulesza, Fernando Pereira, and Jennifer Vaughan. A theory of learning from different domains. *Machine Learning*, 79:151–175, 2010. URL <http://www.springerlink.com/content/q6qk230685577n52/>.
- <span id="page-10-2"></span>[6] Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. *Advances in neural information processing systems*, 33:1877–1901, 2020.
- <span id="page-10-16"></span>[7] Collin Burns, Pavel Izmailov, Jan Hendrik Kirchner, Bowen Baker, Leo Gao, Leopold Aschenbrenner, Yining Chen, Adrien Ecoffet, Manas Joglekar, Jan Leike, et al. Weak-to-strong generalization: Eliciting strong capabilities with weak supervision. *arXiv preprint arXiv:2312.09390*, 2023.
- <span id="page-10-13"></span>[8] Yue Cao, Zhenda Xie, Bin Liu, Yutong Lin, Zheng Zhang, and Han Hu. Parametric instance classification for unsupervised visual feature learning. In *NeurIPS*, 2020.
- <span id="page-10-7"></span>[9] Mathilde Caron, Ishan Misra, Julien Mairal, Priya Goyal, Piotr Bojanowski, and Armand Joulin. Unsupervised learning of visual features by contrasting cluster assignments. In *NeurIPS*, 2020.
- <span id="page-10-5"></span>[10] Mathilde Caron, Hugo Touvron, Ishan Misra, Hervé Jégou, Julien Mairal, Piotr Bojanowski, and Armand Joulin. Emerging properties in self-supervised vision transformers. In *ICCV*, 2021.
- <span id="page-10-9"></span>[11] George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 4750–4759, 2022.
- <span id="page-10-4"></span>[12] Ting Chen, Simon Kornblith, Mohammad Norouzi, and Geoffrey Hinton. A simple framework for contrastive learning of visual representations. In *ICML*, 2020.
- <span id="page-10-6"></span>[13] Xinlei Chen and Kaiming He. Exploring simple siamese representation learning. In *CVPR*, 2021.
- <span id="page-10-12"></span>[14] Xinlei Chen, Haoqi Fan, Ross Girshick, and Kaiming He. Improved baselines with momentum contrastive learning. *arXiv preprint arXiv:2003.04297*, 2020.
- <span id="page-10-14"></span>[15] Xinlei Chen, Saining Xie, and Kaiming He. An empirical study of training self-supervised vision transformers. *arXiv preprint arXiv:2104.02057*, 2021.
- <span id="page-10-3"></span>[16] Yu Cheng, Duo Wang, Pan Zhou, and Tao Zhang. A survey of model compression and acceleration for deep neural networks. *arXiv preprint arXiv:1710.09282*, 2017.
- <span id="page-10-11"></span>[17] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. *Advances in Neural Information Processing Systems*, 35:810–822, 2022.
- <span id="page-10-10"></span>[18] Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *International Conference on Machine Learning*, pages 6565–6590. PMLR, 2023.

- <span id="page-11-13"></span>[19] Victor Guilherme Turrisi Da Costa, Enrico Fini, Moin Nabi, Nicu Sebe, and Elisa Ricci. sololearn: A library of self-supervised methods for visual representation learning. *Journal of Machine Learning Research*, 23(56):1–6, 2022.
- <span id="page-11-0"></span>[20] Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A largescale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pages 248–255. Ieee, 2009.
- <span id="page-11-14"></span>[21] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020.
- <span id="page-11-5"></span>[22] Jiawei Du, Yidi Jiang, Vincent YF Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the accumulated trajectory error to improve dataset distillation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3749–3758, 2023.
- <span id="page-11-16"></span>[23] Noah Golowich, Alexander Rakhlin, and Ohad Shamir. Size-independent sample complexity of neural networks, 2019.
- <span id="page-11-6"></span>[24] Ziyao Guo, Kai Wang, George Cazenavette, Hui Li, Kaipeng Zhang, and Yang You. Towards lossless dataset distillation via difficulty-aligned trajectory matching. *arXiv preprint arXiv:2310.05773*, 2023.
- <span id="page-11-7"></span>[25] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016.
- <span id="page-11-3"></span>[26] Kaiming He, Haoqi Fan, Yuxin Wu, Saining Xie, and Ross Girshick. Momentum contrast for unsupervised visual representation learning. In *CVPR*, 2020.
- <span id="page-11-9"></span>[27] Qianjiang Hu, Xiao Wang, Wei Hu, and Guo-Jun Qi. Adco: Adversarial contrast for efficient learning of unsupervised representations from self-trained negative adversaries. In *CVPR*, 2021.
- <span id="page-11-1"></span>[28] Yanping Huang, Youlong Cheng, Ankur Bapna, Orhan Firat, Dehao Chen, Mia Chen, HyoukJoong Lee, Jiquan Ngiam, Quoc V Le, Yonghui Wu, et al. Gpipe: Efficient training of giant neural networks using pipeline parallelism. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-11-15"></span>[29] Arthur Jacot, Franck Gabriel, and Clément Hongler. Neural tangent kernel: Convergence and generalization in neural networks. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-11-2"></span>[30] Grill Jean-Bastien, Strub Florian, Altché Florent, Tallec Corentin, Pierre Richemond H., Buchatskaya Elena, Doersch Carl, Bernardo Pires Avila, Zhaohan Guo Daniel, Mohammad Azar Gheshlaghi, Piot Bilal, Kavukcuoglu Koray, Munos Rémi, and Valko Michal. Bootstrap your own latent - a new approach to self-supervised learning. In *NeurIPS*, 2020.
- <span id="page-11-8"></span>[31] Yannis Kalantidis, Mert Bulent Sariyildiz, Noe Pion, Philippe Weinzaepfel, and Diane Larlus. Hard negative mixing for contrastive learning. In *NeurIPS*, 2020.
- <span id="page-11-4"></span>[32] Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pages 11102–11118. PMLR, 2022.
- <span id="page-11-10"></span>[33] Diederik P Kingma and Jimmy Ba. Adam: A method for stochastic optimization. *arXiv preprint arXiv:1412.6980*, 2014.
- <span id="page-11-12"></span>[34] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-11-11"></span>[35] Alex Krizhevsky, Vinod Nair, and Geoffrey Hinton. Cifar-10 and cifar-100 datasets. *URl: https://www. cs. toronto. edu/kriz/cifar. html*, 6(1):1, 2009.

- <span id="page-12-11"></span>[36] Ya Le and Xuan Yang. Tiny imagenet visual recognition challenge. *CS 231N*, 7(7):3, 2015.
- <span id="page-12-4"></span>[37] Dong Bok Lee, Seanie Lee, Joonho Ko, Kenji Kawaguchi, Juho Lee, and Sung Ju Hwang. Self-supervised dataset distillation for transfer learning. In *Proceedings of the International Conference on Learning Representations (ICLR)*, 2024.
- <span id="page-12-16"></span>[38] Jaehoon Lee, Lechao Xiao, Samuel S Schoenholz, Yasaman Bahri, Roman Novak, Jascha Sohl-Dickstein, and Jeffrey Pennington. Wide neural networks of any depth evolve as linear models under gradient descent \*. *Journal of Statistical Mechanics: Theory and Experiment*, 2020(12):124002, December 2020. ISSN 1742-5468. doi: 10.1088/1742-5468/abc62b. URL <http://dx.doi.org/10.1088/1742-5468/abc62b>.
- <span id="page-12-10"></span>[39] Hao Li, Zheng Xu, Gavin Taylor, Christoph Studer, and Tom Goldstein. Visualizing the loss landscape of neural nets. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-12-7"></span>[40] Junnan Li, Pan Zhou, Caiming Xiong, and Steven CH Hoi. Prototypical contrastive learning of unsupervised representations. In *ICLR*, 2021.
- <span id="page-12-14"></span>[41] Yuanzhi Li and Yingyu Liang. Learning overparameterized neural networks via stochastic gradient descent on structured data. *Advances in neural information processing systems*, 31, 2018.
- <span id="page-12-1"></span>[42] Tao Lin, Sebastian U Stich, Kumar Kshitij Patel, and Martin Jaggi. Don't use large mini-batches, use local sgd. *arXiv preprint arXiv:1808.07217*, 2018.
- <span id="page-12-3"></span>[43] Xiao Liu, Yanan Zheng, Zhengxiao Du, Ming Ding, Yujie Qian, Zhilin Yang, and Jie Tang. Gpt understands, too. *AI Open*, 2023.
- <span id="page-12-5"></span>[44] Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *arXiv preprint arXiv:2302.14416*, 2023.
- <span id="page-12-2"></span>[45] Sachin Mehta, Maxwell Horton, Fartash Faghri, Mohammad Hossein Sekhavat, Mahyar Najibi, Mehrdad Farajtabar, Oncel Tuzel, and Mohammad Rastegari. Catlip: Clip-level visual recognition accuracy with 2.7 x faster pre-training on web-scale image-text data. *arXiv preprint arXiv:2404.15653*, 2024.
- <span id="page-12-15"></span>[46] Mehryar Mohri, Afshin Rostamizadeh, and Ameet Talwalkar. *Foundations of Machine Learning*. 2018. URL [https://mitpress.ublish.com/ebook/](https://mitpress.ublish.com/ebook/foundations-of-machine-learning--2-preview/7093/Cover) [foundations-of-machine-learning--2-preview/7093/Cover](https://mitpress.ublish.com/ebook/foundations-of-machine-learning--2-preview/7093/Cover). Second Edition.
- <span id="page-12-6"></span>[47] Aaron van den Oord, Yazhe Li, and Oriol Vinyals. Representation learning with contrastive predictive coding. *arXiv preprint arXiv:1807.03748*, 2018.
- <span id="page-12-13"></span>[48] Adam Paszke, Sam Gross, Francisco Massa, Adam Lerer, James Bradbury, Gregory Chanan, Trevor Killeen, Zeming Lin, Natalia Gimelshein, Luca Antiga, et al. Pytorch: An imperative style, high-performance deep learning library. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-12-0"></span>[49] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, et al. Learning transferable visual models from natural language supervision. In *International conference on machine learning*, pages 8748–8763. PMLR, 2021.
- <span id="page-12-8"></span>[50] Pierre H Richemond, Jean-Bastien Grill, Florent Altché, Corentin Tallec, Florian Strub, Andrew Brock, Samuel Smith, Soham De, Razvan Pascanu, Bilal Piot, et al. Byol works even without batch statistics. *arXiv preprint arXiv:2010.10241*, 2020.
- <span id="page-12-9"></span>[51] Herbert E. Robbins. A stochastic approximation method. *Annals of Mathematical Statistics*, 22: 400–407, 1951. URL <https://api.semanticscholar.org/CorpusID:16945044>.
- <span id="page-12-12"></span>[52] Mark Sandler, Andrew Howard, Menglong Zhu, Andrey Zhmoginov, and Liang-Chieh Chen. Mobilenetv2: Inverted residuals and linear bottlenecks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 4510–4520, 2018.

- <span id="page-13-4"></span>[53] Shitong Shao, Zeyuan Yin, Muxin Zhou, Xindong Zhang, and Zhiqiang Shen. Generalized large-scale data condensation via various backbone and statistical matching. *arXiv preprint arXiv:2311.17950*, 2023.
- <span id="page-13-5"></span>[54] Shitong Shao, Zikai Zhou, Huanran Chen, and Zhiqiang Shen. Elucidating the design space of dataset condensation. *arXiv preprint arXiv:2404.13733*, 2024.
- <span id="page-13-0"></span>[55] Emma Strubell, Ananya Ganesh, and Andrew McCallum. Energy and policy considerations for deep learning in nlp. *arXiv preprint arXiv:1906.02243*, 2019.
- <span id="page-13-3"></span>[56] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: An efficient dataset distillation paradigm. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2024.
- <span id="page-13-15"></span>[57] Igor Susmelj, Matthias Heller, Philipp Wirth, Jeremy Prescott, and Malte Ebner et al. Lightly. *GitHub. Note: https://github.com/lightly-ai/lightly*, 2020.
- <span id="page-13-16"></span>[58] Mingxing Tan and Quoc Le. Efficientnet: Rethinking model scaling for convolutional neural networks. In *International conference on machine learning*, pages 6105–6114. PMLR, 2019.
- <span id="page-13-14"></span>[59] Chenxin Tao, Honghui Wang, Xizhou Zhu, Jiahua Dong, Shiji Song, Gao Huang, and Jifeng Dai. Exploring the equivalence of siamese self-supervised learning via a unified gradient framework. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 14431–14440, 2022.
- <span id="page-13-17"></span>[60] Yuandong Tian, Xinlei Chen, and Surya Ganguli. Understanding self-supervised learning dynamics without contrastive pairs. In *ICML*, 2021.
- <span id="page-13-18"></span>[61] Laurens Van der Maaten and Geoffrey Hinton. Visualizing data using t-sne. *Journal of machine learning research*, 9(11), 2008.
- <span id="page-13-6"></span>[62] Guangrun Wang, Keze Wang, Guangcong Wang, Philip HS Torr, and Liang Lin. Solving inefficiency of self-supervised representation learning. In *Proceedings of the IEEE/CVF International Conference on Computer Vision*, pages 9505–9515, 2021.
- <span id="page-13-9"></span>[63] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12196–12205, 2022.
- <span id="page-13-13"></span>[64] Tongzhou Wang and Phillip Isola. Understanding contrastive representation learning through alignment and uniformity on the hypersphere. In *ICML*, 2020.
- <span id="page-13-2"></span>[65] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-13-7"></span>[66] David H Wolpert and William G Macready. No free lunch theorems for optimization. *IEEE transactions on evolutionary computation*, 1(1):67–82, 1997.
- <span id="page-13-12"></span>[67] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze, recover and relabel: Dataset condensation at imagenet scale from a new perspective. *arXiv preprint arXiv:2306.13092*, 2023.
- <span id="page-13-11"></span>[68] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023.
- <span id="page-13-1"></span>[69] Jure Zbontar, Li Jing, Ishan Misra, Yann LeCun, and Stéphane Deny. Barlow twins: Selfsupervised learning via redundancy reduction. In *ICML*, 2021.
- <span id="page-13-8"></span>[70] Lei Zhang, Jie Zhang, Bowen Lei, Subhabrata Mukherjee, Xiang Pan, Bo Zhao, Caiwen Ding, Yao Li, and Dongkuan Xu. Accelerating dataset distillation via model augmentation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 11950–11959, 2023.
- <span id="page-13-10"></span>[71] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pages 6514–6523, 2023.

- <span id="page-14-0"></span>[72] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.
- <span id="page-14-1"></span>[73] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 7856–7865, 2023.
- <span id="page-14-2"></span>[74] Rui Zhu, Bingchen Zhao, Jingen Liu, Zhenglong Sun, and Chang Wen Chen. Improving contrastive learning by visualizing feature transformation. In *ICCV*, 2021.

## Contents

| 1 | Introduction                                                                                                                                                                                                                                                                                                                                                          | 1                                                        |  |  |
|---|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------|--|--|
| 2 | <b>Related Work</b><br>Dataset Distillation: Efficient yet Effective Learning Using Fewer Data<br>2.1<br>Self-supervised Learning: Representation Learning Using Unlabeled Data<br>2.2                                                                                                                                                                                | $\mathbf{3}$<br>$\overline{3}$<br>3                      |  |  |
| 3 | <b>Revealing Critical Properties of Efficient Learning over Data</b><br>Unifying (Self-)Supervised Learning from a Data-Centric Perspective<br>3.1<br>Empirical and Theoretical Investigation of Data-Centric Efficient Learning<br>3.2<br>Extended Understanding of Data-Centric Efficient Learning<br>3.3<br>Generalization-bounded Efficient Data Synthesis<br>3.4 | $\overline{\mathbf{4}}$<br>4<br>$\overline{4}$<br>5<br>6 |  |  |
| 4 | Methodology<br>RELA-D ( $\bullet$ ): Synthesis of Efficient Dataset<br>4.1<br>RELA-F ( $\bigtriangledown$ ): Assist Learning with Generated Efficient Dataset<br>4.2                                                                                                                                                                                                  | 7<br>$\,8\,$<br>8                                        |  |  |
| 5 | <b>Experiments</b><br>Primary Experimental Results and Analysis<br>5.1                                                                                                                                                                                                                                                                                                | 8<br>9                                                   |  |  |
| 6 | <b>Conclusion and Limitation</b>                                                                                                                                                                                                                                                                                                                                      | 10                                                       |  |  |
|   | <b>A</b> Ablation Study                                                                                                                                                                                                                                                                                                                                               | 17                                                       |  |  |
| B | <b>Proof of Theorem 1</b><br>Setup<br>B.1<br>B.2 Algorithm<br>B.3 Bounds with Variance<br><b>B.4</b><br>Nonlinear case<br>From the Perspective of Feature Learning<br>B.5                                                                                                                                                                                             | 18<br>18<br>18<br>18<br>19<br>19                         |  |  |
|   | C Proof of Theorem 2<br>C.1 Setting<br>C.2 Bounds with $\rho$<br>C.3 Nonlinear case                                                                                                                                                                                                                                                                                   | 20<br>20<br>20<br>20                                     |  |  |
|   | D Proof of Theorem 3<br>D.1 Relation between data distribution and Rademacher complexity                                                                                                                                                                                                                                                                              | 21<br>25                                                 |  |  |
| Е | <b>Explanation of Rescaling Samples</b>                                                                                                                                                                                                                                                                                                                               | 25                                                       |  |  |
| F | <b>Detailed Methodology of RELA-D</b><br>F.1 Proof for Ideal Properties of Prior Models                                                                                                                                                                                                                                                                               | 27<br>27                                                 |  |  |
|   | G Analysis of Different Self-Supervised Learning Methods<br>A Unified Framework for SSL<br>G.1<br>Contrastive Learning Methods<br>G.2<br>G.3 Asymmetric Network Methods<br>G.4 Feature Decorrelation Methods                                                                                                                                                          | 28<br>28<br>28<br>29<br>30                               |  |  |
|   | <b>H</b> Budget of RELA for Data Synthesis                                                                                                                                                                                                                                                                                                                            | 31                                                       |  |  |
| 1 | <b>RELA</b> in Labeled Dataset Distillation and Human-Supervised Learning<br>Experimental Setup<br>I.1<br>I.2<br>Main Results                                                                                                                                                                                                                                         | 31<br>31<br>31                                           |  |  |
| J | <b>RELA Algorithm</b>                                                                                                                                                                                                                                                                                                                                                 | 32                                                       |  |  |
|   | <b>K</b> Experimental Details<br>32                                                                                                                                                                                                                                                                                                                                   |                                                          |  |  |

| K.1 Detailed Setup for Experiments in Section 3.2 |     |
|---------------------------------------------------|-----|
| K.2 Detailed Setup for Experiments in Section 5   |     |
| L Batch PCA Reduction                             | 33  |
| L.1 Principal Component Analysis                  | 33  |
| M Analyze the Practical Data Augmentations        | 35  |
| M.1 Assumptions and Definitions                   | 35  |
| M.2 Augmented Distributions                       | 36  |
| M.3 Increased Variance Leads to Increased Overlap | -36 |
| M.4 Conclusion                                    | 37  |
| M.5 Empirical Analysis for Real-world Datasets    |     |

<span id="page-16-0"></span>

## A Ablation Study

We conduct ablation studies to understand the impact of each component of RELA on performance.

<span id="page-16-1"></span>Image /page/16/Figure/3 description: The image contains three plots. The first plot is a heatmap showing correlations between different models. The values range from 0.04 to 1.00. The second plot is a line graph showing Top-1 Accuracy (%) versus the Value of constant lambda. It displays four lines representing 'Static (Rand.)', 'Dynamic (Rand.)', 'Static (TIN-T)', and 'Dynamic (TIN-T)'. The third plot is a bar chart comparing 'Computational Cost (%)' for different datasets ('CF-10', 'CF-100', 'T-IN', 'IN-1K') across three 'Prior Models': 'Rand.', 'CF10-T', and 'CLIP'.

(a) Representation similarity (b) Dynamic vs. static strategies (c) Computational cost analysis

Figure 4: Ablation study on BYOL  $($   $\bigwedge$   $)$  components and parameters. (a) We analyze the representation similarity between various source models (indicated on the x-axis) and target models (indicated on the y-axis). (b) We compare the static RELA weight setting strategy with our adaptive strategy. Dotted lines ('- -') represent our adaptive strategy, while solid lines  $(-')$  denote the static  $\lambda$  setting strategy. Specifically, in the static weight setting (e.g., 0.4), the first 40% of the training leverages RELA, with the remaining 60% employing the original algorithm. (c) We present the computational cost, quantified as training time/steps, of our RELA across various prior models.

Empirical representation similarity. Our foundational assumption of our RELA is that the representations of well-trained models, developed using various neural network architectures and algorithms on diverse real-world datasets, exhibit linear transportability to one another. To test this hypothesis, we assess representation similarity, a metric that quantifies the linear transferability between pre-trained models. The results, depicted in  $Figure 4a$ , demonstrate that representations from robust models (e.g., CLIP) can be effectively transferred to less robust models (e.g., CF10-T). This finding aligns with our results in [Table 1](#page-8-1), showing that leveraging powerful models (e.g., CLIP and IN1K-T) consistently enhances learning in models trained on datasets with limited knowledge, such as CF-10.

Combining RELA and BYOL with static  $\lambda$  setting strategies. The coefficient  $\lambda$ , as introduced in [Section 4.2](#page-7-2) , is pivotal in controlling the weight of the RELA phase during training. To assess the robustness of our adaptive strategy, which dynamically adjusts  $\lambda$ , we compare it to a static  $\lambda$  setting strategy. The results in [Figure 4b](#page-16-1) indicate that larger (smaller) RELA weights are advantageous when using a strong (weak) prior model. Nonetheless, static settings lack generalizability across various scenarios, whereas our adaptive strategy demonstrates superior generalization capabilities.

Analysis of the computational cost when using RELA with different prior models. To validate that our RELA, in conjunction with various prior models, can effectively reduce computational costs in self-supervised learning, we conducted experiments comparing the computational expense required for  $BYOL$  ( $\blacklozenge$ ) to achieve equivalent performance to the original BYOL trained with a full budget. The results, illustrated in [Figure 4c](#page-16-1), consistently demonstrate that RELA assists BYOL in lowering training costs while maintaining equivalent performance levels. Furthermore, it is evident that employing robust prior models consistently leads to greater reductions in training budgets.

<span id="page-17-0"></span>

## B Proof of Theorem [1](#page-4-2)

In this section, we prove a slightly modified version of Theore[m1,](#page-4-1) extending the distribution to Generalized Gaussian Mixture(GGM) and making some assumptions for technical reasons. Yet this proof could still reflect the essential of the theorem.

<span id="page-17-1"></span>

### B.1 Setup

**Notation** N( $\mu, \alpha, \beta$ ) denotes the generalized Gaussian distribution with pdf  $\frac{\beta}{2\alpha \Gamma(1/\beta)} e^{-(|x-\mu|/\alpha)^{\beta}}$ , B for Bernoulli distribution.

We focus on the 1-dim situation. Assume that  $\mu_1 < \mu_2$ . Define the original data distribution( $\mathcal{N}_0$  =  $N(\mu_1, \alpha_0, \beta_0)$  and  $\mathcal{N}_1 = N(\mu_2, \alpha_0, \beta_0)$ 

$$
G := \{(x, y) \mid y \sim 2 \cdot B(1, \frac{1}{2}) - 1, x \sim \frac{1 - y}{2} \cdot \mathcal{N}_0 + \frac{1 + y}{2} \cdot \mathcal{N}_1\}
$$

and the modified one  $(\mathcal{N}'_0 = N(\mu_1, \alpha, \beta)$  and  $\mathcal{N}'_1 = N(\mu_2, \alpha, \beta)$ :

$$
G' := \{ (x,y) \mid y \sim 2 \cdot B(1,\frac{1}{2}) - 1, x \sim \frac{1-y}{2} \cdot \mathcal{N}'_0 + \frac{1+y}{2} \cdot \mathcal{N}'_1 \}.
$$

Our task is predicting y given x. Note that  $y \in {\pm 1}$ , which is a bit different from the definition in Section [3.2.](#page-3-0) In 1-dim situation, we just need one parameter for this classification task, so define  $f_{\theta}(x) := \text{sign}(x + \theta)$  to fit the distribution. We could compute the generalization loss on original distribution:

$$
\mathcal{L}(f_{\theta}) = \left(\int_{-\theta}^{+\infty} dF_{-} + \int_{-\infty}^{-\theta} dF_{+}\right)/2 = \left(1 - \int_{-\frac{\theta + \mu_{2}}{\alpha_{0}}}^{-\frac{\theta + \mu_{1}}{\alpha_{0}}} dF\right)/2
$$

Obviously  $\theta^* = -\frac{\mu_1 + \mu_2}{2}$ , we have:

$$
$\mathcal{L}(f_{\theta}) - \mathcal{L}(f_{\theta^*}) = \left(\int_{-\frac{\mu_2 - \mu_1}{2\alpha_0}}^{\frac{\mu_2 - \mu_1}{2\alpha_0}} dF - \int_{-\frac{\theta + \mu_2}{\alpha_0}}^{\frac{\theta + \mu_1}{\alpha_0}} dF\right)/2$
$$
  
$$
$\leq C_1 \cdot (\theta - \theta^*)^2 \quad (or \ C'_1 \ |\theta - \theta^*|)$
$$

where  $C_1$ ,  $C'_1$  are constants,  $F_0$   $F_1$   $F$  denote the CDF of  $\mathcal{N}_0$   $\mathcal{N}_1$  and  $N(0, 1, \beta_0)$  respectively. The inequality above is due to the fact that function  $h(x) = (\int_{-1}^{1} dF - \int_{x-1}^{x+1} dF)/x^2$  has limits at 0 and so is bounded.

<span id="page-17-2"></span>

### B.2 Algorithm

For a dataset  $\{(x_i, y_i)\}_{i=1}^n$ , set the loss function  $L(\theta) = \frac{1}{n} \sum_{i=1}^n \ell [y_i(x_i + \theta)], \ell(v) = \frac{1}{2}(1-v)^2$ . We apply the stocastic gradient descent algorithm and assume the online setting  $(n = 1)$ : at step t draw one sample  $(x_t, y_t)$  from G' then use the gradient  $\nabla L(\theta_t)$  to update  $\theta$  ( $\eta \in (0, 1), t \in \mathbb{N}$ ):

$$
\theta_{t+1} = \theta_t - \eta \nabla L(\theta_t),
$$
  
$$
\nabla L(\theta_t) = \theta + (x_t - y_t).
$$

It can be observed that randomness of  $x$  leads to noies on gradient.

<span id="page-17-3"></span>

### B.3 Bounds with Variance

We prove the proposition that lower variance of GG can make convergence faster, i.e.  $\mathbb{E}[\mathcal{L}(f_{\theta_t}) - \mathcal{L}(f_{\theta^*})]$  is bounded by an increasing function of variance (*t* fixed).

*Proof.* From above, we could get

$$
\theta_t = (1 - \eta)^t \theta_0 - \eta \left[ (x_{t-1} - y_{t-1}) + (1 - \eta)(x_{t-2} - y_{t-2}) + \dots + (1 - \eta)^{t-1}(x_0 - y_0) \right]
$$

and so:  
\n
$$
\mathbb{E}\left[\mathcal{L}(f_{\theta_t}) - \mathcal{L}(f_{\theta^*})\right] \leq C_1 \mathbb{E}\left[(\theta_t - \theta^*)^2\right]
$$
\n
$$
= C_1 \mathbb{E}\left\{\left[(1 - \eta)^t(\theta_0 - \theta^*) - \eta \sum_{j=1}^t (1 - \eta)^{j-1}(x_{t-j} - y_{t-j} + \theta^*)\right]^2\right\}
$$
\n
$$
= C_1 \mathbb{E}\left[(1 - \eta)^{2t}(\theta_0 - \theta^*)^2 + \eta^2 \sum_{j=1}^t (1 - \eta)^{2(j-1)}(x_{t-j} - y_{t-j} + \theta^*)^2\right]
$$
\n
$$
= C_1 \left((1 - \eta)^{2t}(\theta_0 - \theta^*)^2 + \frac{\eta}{(2 - \eta)}(1 - (1 - \eta)^{2t})\left[\frac{\alpha^2 \Gamma(3/\beta)}{\Gamma(1/\beta)} + \left(1 - \frac{\mu_2 - \mu_1}{2}\right)^2\right]\right)
$$

The last two equalities is due to the fact that for  $(x, y) \sim G'$ 

$$
\mathbb{E}[x - y + \theta^*] = 0,
$$
  
$$
\mathbb{E}[(x - y + \theta^*)^2] = \frac{\alpha^2 \Gamma(3/\beta)}{\Gamma(1/\beta)} + \left(1 - \frac{\mu_2 - \mu_1}{2}\right)^2.
$$

<span id="page-18-0"></span>

### B.4 Nonlinear case

In this subsection, we conduct some qualitative analysis on the nonlinear case. The setting is the same as that in Section [3.2.](#page-3-0) We point out the differences compared with the linear case above:  $\mathbf{x} \in \mathbb{R}^d, y \in \{0, 1\}$  and

$$
f_{\theta}(\mathbf{x}) := \sigma\left(\theta^{[1]}\cdot \text{ReLU}\left(\theta^{[2]}\mathbf{x} + \theta^{[3]}\right) + \theta^{[4]}\right)
$$

where  $\sigma(z) = \frac{1}{1+e^{-z}}$  is the sigmoid function; ReLU(z) = max(0, z) is the activation function for the hidden layer, which provides non-linearity to the model;  $\theta^{[2]}$  and  $\theta^{[3]}$  are the weights and biases of the hidden layer;  $\theta^{[1]}$  and  $\theta^{[4]}$  are the weights and biases of the output layer.

To make things explicit, we still assume the online setting and set the loss function  $L(\theta) = \frac{1}{2}(f_{\theta}(\mathbf{x}) -$ 2 y)<sup>2</sup>. Assume after some iterations,  $\theta^{[2]} \cdot \mu_1 + \theta^{[3]} < 0$  and  $\theta^{[2]} \cdot \mu_2 + \theta^{[3]} > 0$  (coordinate-wise). In this situation, we could see that if **x** is close to its mean( $\mu_1$  or  $\mu_2$ ), the sign of ReLU  $(\theta^{[2]}\mathbf{x}+\theta^{[3]})$ will be the same as y. So  $f_{\theta}$  will become an optimal classifier if  $\theta^{[1]} \to +\infty$  and  $\theta^{[4]} \to -\infty$ . We focus on  $\theta^{[1]}$ , using SGD:

$$
\boldsymbol{\theta}_{t+1}^{[1]} = \boldsymbol{\theta}_{t}^{[1]} - \eta \frac{\partial L}{\partial \boldsymbol{\theta}^{[1]}},
$$
$$
\frac{\partial L}{\partial \boldsymbol{\theta}^{[1]}} = (f_{\boldsymbol{\theta}}(\mathbf{x}) - y)\sigma(1-\sigma)\text{ReLU}\left(\boldsymbol{\theta}^{[2]}\mathbf{x} + \boldsymbol{\theta}^{[3]}\right)
$$

Note that we drop the variable value in  $\sigma(\cdot)$  to make the expression more compact.

Then we can analyze the phenomenon qualitatively: larger  $\Sigma$  will make convergence slower. The reason is that the larger  $\Sigma$  is, when x is drawn from  $\mathcal{N}_1$   $(y=1)$ ,  $\bm{\theta}^{[2]}$ x +  $\bm{\theta}^{[3]}< 0$  is more likely to happen(i.e. straying far away from the mean), causing  $\theta^{[1]}$  to stop updating; what's worse, when x is drawn from  $\mathcal{N}_0$   $(y=0)$ , with larger probability  $\bm{\theta}^{[2]}{\bf x}+\bm{\theta}^{[3]}>0$  which will make  $\bm{\theta}^{[1]}$  to go in the opposite direction. In summary, it is  $\Sigma$  that makes the gradient noisy thus impacts the convergence rate.

<span id="page-18-1"></span>

### B.5 From the Perspective of Feature Learning

In essence, the theoretical results in [\[41\]](#page-12-14) could also be interpreted as a proof of the theorem. [\[41\]](#page-12-14) study the learning of a two-layer ReLU neural network for k-class classification via stochastic gradient descent (SGD), assuming that each class corresponds to l patterns(distributions), with every two of the  $k \times l$  distributions of the input data are separated by a distance  $\delta$ . Below is the main theorem in [\[41\]](#page-12-14):

**Proposition 1** . Suppose some assumptions are satisfied, then for every  $\epsilon > 0$ , there is  $M = poly(k, l, 1/\delta, 1/\epsilon)$  *such that for every*  $m \geq M$ , after doing a minibatch SGD with *batch size*  $B = poly(k, l, 1/\delta, 1/\epsilon, \log m)$  *and learning rate*  $\eta = \frac{1}{m \cdot poly(k, l, 1/\delta, 1/\epsilon, \log m)}$  *for*  $T = poly(k, l, 1/\delta, 1/\epsilon, \log m)$  *iterations, with high probability, the generalization error(the probability that the model misclassifies) of the learned model is at most*  $\epsilon$ *.* 

Theoretical results above show that a larger  $\delta$  helps network to learn more efficiently. (In our case,  $\delta$  can be roughly viewed as the Mahalanobis distance between the two Gaussians, which is inverse proportion to the variance.) Also, Appendix D.2 of [\[41\]](#page-12-14) demonstrates an example very similar to ours in which the input data is drawn from Gaussian distributions with different means, indicating increasing variance of the Gaussian causes the test accuracy to decrease and takes longer time to get a good solution.

<span id="page-19-0"></span>

### C Proof of Theorem [2](#page-4-2)

<span id="page-19-1"></span>

#### C.1 Setting

Use the same setting in Section [B\(](#page-17-0)linear case) except that

$$
G' := \{(x,y') \mid y \sim 2 \cdot B(1,\frac{1}{2}) - 1, x = \frac{1-y}{2} \cdot \mathcal{N}_0 + \frac{1+y}{2} \cdot \mathcal{N}_1, y' = \rho \cdot f_{\theta^*}(x) + (1-\rho)y\}.
$$

In other words, we modify the distribution of  $y$  instead of  $x$  this time.

<span id="page-19-2"></span>

#### C.2 Bounds with $\rho$

We're going to prove that higher  $\rho$  can make convergence faster, i.e.  $\mathbb{E}[\mathcal{L}(f_{\theta_t}) - \mathcal{L}(f_{\theta^*})]$  is bounded by an decreasing function of  $\rho$  (t fixed).

*Proof.* The crucial part  $x - y' + \theta^* = \rho(x - f_{\theta^*}(x) + \theta^*) + (1 - \rho)(x - y + \theta^*)$ , and in fact  $\mathbb{E}|x - y + \theta^*| - \mathbb{E}|x - f_{\theta^*}(x) + \theta^*| := \epsilon_0 > 0.$ 

Similarly, we can get bounds with  $\rho$  (see  $C'_1$  in Section [B\)](#page-17-0):

$$
\mathbb{E}[\mathcal{L}(f_{\theta_t}) - \mathcal{L}(f_{\theta^*})] \le C'_1 \mathbb{E}[(1-\eta)^t | \theta_0 - \theta^*| + (1 - (1 - \eta)^t)|x - y' + \theta^*|]
$$

$$
\le C_2 (1 - \eta)^t + C'_1 (1 - (1 - \eta)^t) [\rho \cdot \mathbb{E}|x - f_{\theta^*}(x) + \theta^*| + (1 - \rho) \cdot \mathbb{E}|x - y + \theta^*|]
$$

$$
\le C_2 (1 - \eta)^t + (1 - (1 - \eta)^t)(C_3 - C_4\rho)
$$

where  $C_2 = C'_1 |\theta_0 - \theta^*|$ ,  $C_3 = C'_1 \mathbb{E} |x - y + \theta^*|$ ,  $C_4 = C'_1 \epsilon_0 > 0$ .

<span id="page-19-3"></span>

#### C.3 Nonlinear case

To see the impact of modifying y more clearly, we directly set  $\rho = 1$  and conduct a similar analysis as in Section [B.4.](#page-18-0) Let's still focus on  $\theta^{[1]}$  and use the same assumptions in Section [B.4,](#page-18-0) then we have:

∂L

 $\Box$ 

$$
\bm{\theta}_{t+1}^{[1]} = \bm{\theta}_{t}^{[1]} - \eta \frac{\partial L}{\partial \bm{\theta}^{[1]}},
$$
$$
\frac{\partial L}{\partial \bm{\theta}^{[1]}} = (f_{\bm{\theta}}(\mathbf{x}) - f_{\bm{\theta}^*}(\mathbf{x}))\sigma(1-\sigma)\text{ReLU}\left(\bm{\theta}^{[2]}\mathbf{x} + \bm{\theta}^{[3]}\right)
$$

Note y is replaced by  $f_{\theta^*}(\mathbf{x})$ . For instance x is drawn from  $\mathcal{N}_0$  ( $y = 0$ ) but strays far away from the  $\mu_1$ , causing  $\theta^{[2]} \mathbf{x} + \theta^{[3]} > 0$ . In this situation  $f_{\theta^*}$  is likely to regard x as a sample from  $\mathcal{N}_1$  (i.e.  $f_{\theta^*}(\mathbf{x})$  close to 1) thus making  $\theta^{[1]}$  to go in the right direction instead of the opposite. This explains why larger  $\rho$  can make convergence faster.

<span id="page-20-0"></span>

### D Proof of Theorem [3](#page-6-1)

We follow some proof steps in [\[5\]](#page-10-17). Let's begin by introducing some notations used in this section. Notation and Setup X is the input space,  $\overline{\mathcal{D}}_S$  and  $\mathcal{D}_T$  are two distributions over X. Let  $\mathcal{Y} = \{0, 1\}$ . H denotes a hypothesis class from X to y. To simplify notations,  $\forall h, f \in \mathcal{H}$  let  $\epsilon_S(h, f) =$  $E_{\mathbf{x} \sim \mathcal{D}_S}[\mathbf{1}(h(\mathbf{x}) \neq f(\mathbf{x}))]$ , and  $\hat{\epsilon}_S(h, f)$  be empirical error  $(\epsilon_T(h, f), \hat{\epsilon}_T(h, f))$  similar).

Then we introduce some concepts and lemmas, most of which are from [\[5\]](#page-10-17).

Definition 6 (H-divergence) . *The* H*-divergence between two distributions* D *and* D′ *is defined as:*

$$
d_{\mathcal{H}}(\mathcal{D}, \mathcal{D}') = 2 \sup_{h \in \mathcal{H}} |\mathrm{Pr}_{\mathcal{D}}[I(h)] - \mathrm{Pr}_{\mathcal{D}'}[I(h)]|
$$

*where*  $I(h) = {\mathbf{x} : h(\mathbf{x}) = 1}.$ 

Definition 7 (Total Variation Distance) . *For two distributions* D *and* D′ *, the total variation distance of them is defined as:*

$$
D_{TV}(\mathcal{D}, \mathcal{D}') = \sup_{A \subseteq \mathcal{F}} |Pr_{\mathcal{D}}(A) - Pr_{\mathcal{D}'}(A)|
$$

*where* F *denotes the collection of all events in the probability space.*

Lemma 1 . *For two distributions* D *and* D′ *, by definition it's easy to see that:*

$$
\frac{1}{2}d_{\mathcal{H}}(\mathcal{D}, \mathcal{D}') \leq D_{\text{TV}}(\mathcal{D}, \mathcal{D}')
$$

Definition 8 (Symmetric Difference Hypothesis Space) .

$$
\mathcal{H}\Delta\mathcal{H} := \{g: \mathcal{X} \to \{0,1\} | g(\mathbf{x}) = h(\mathbf{x}) \oplus h'(\mathbf{x}) \quad \forall h, h' \in \mathcal{H}\}
$$

⊕ *denotes the XOR operation.*

Lemma 2 .

$$
\forall h, h' \in \mathcal{H}, \left| \epsilon_S \left( h, h' \right) - \epsilon_T \left( h, h' \right) \right| \leq \frac{1}{2} d_{\mathcal{H} \Delta \mathcal{H}} \left( \mathcal{D}_S, \mathcal{D}_T \right)
$$

*Proof.* only need note that  $h(\mathbf{x}) \oplus h'(\mathbf{x}) = |h(\mathbf{x}) - h'(\mathbf{x})|$ , so

$$
\sup_{g\in \mathcal{H}\Delta\mathcal{H}}\left|\mathrm{Pr}_{\mathcal{D}_S}[I(g)] - \mathrm{Pr}_{\mathcal{D}_T}[I(g)]\right| = \sup_{h,h'\in \mathcal{H}}\left|\epsilon_S\left(h,h'\right) - \epsilon_T\left(h,h'\right)\right|
$$

this is done by definition.

With above notations we can derive a general proposition related with Theorem [3.](#page-6-1)

**Proposition 2** . Assumimg H is a hypothesis class from X to Y and  $\phi$ ,  $\phi' \in \mathcal{H}$ , we have:

$$
\mathbb{E}_{\mathbf{x} \sim \mathcal{D}_T} \left[ \ell(\boldsymbol{\phi}(\mathbf{x}), \boldsymbol{\phi}'(\mathbf{x})) \right] \leq \mathbb{E}_{\mathbf{x} \sim \mathcal{D}_S} \left[ \ell(\boldsymbol{\phi}(\mathbf{x}), \boldsymbol{\phi}'(\mathbf{x})) \right] + \mathbf{D}_{TV}(\mathcal{D}_S, \mathcal{D}_T)
$$
  
where loss function is  $\ell(\hat{y}, y) := \mathbf{1}(\hat{y} \neq y)$ .

*Proof.* using the lemmas above,

$$
\epsilon_T(\phi, \phi') \le \epsilon_S(\phi, \phi') + |\epsilon_T(\phi, \phi') - \epsilon_S(\phi, \phi')|
$$

$$
\le \epsilon_S(\phi, \phi') + \frac{1}{2} d_{\mathcal{H}\Delta\mathcal{H}}(\mathcal{D}_S, \mathcal{D}_T)
$$

$$
\le \epsilon_S(\phi, \phi') + D_{\text{TV}}(\mathcal{D}_S, \mathcal{D}_T)
$$

 $\Box$ 

 $\Box$ 

To obtain some useful inequalities of generalization bound, we need to introduce the Rademacher complexity.

**Definition 9 (Rademacher complexity of a function class)**. *Given a sample of points*  $S =$  $\{z_1, z_2, \ldots, z_m\} \subset Z$ , and considering a function class F of real-valued functions over Z, the *empirical Rademacher complexity of* F *given* S *is defined as:*

$$
\mathfrak{R}_S(\mathcal{F}) = \frac{1}{m} \mathbb{E}_{\sigma} \left[ \sup_{f \in \mathcal{F}} \sum_{i=1}^m \sigma_i f(z_i) \right]
$$

*where*  $σ<sub>i</sub>$  *are independent and identically distributed Rademacher random variables. In other words, for*  $i = 1, 2, \ldots, m$ *, the probability that*  $\sigma_i = +1$  *is equal to the probability that*  $\sigma_i = -1$ *, and both are* <sup>1</sup> *. Further, let* P *be a probability distribution over* Z*. The Rademacher complexity* 2 *of the function class* F *with respect to* P *for sample size* m *is:*

$$
\mathfrak{R}_{P,m}(\mathcal{F}):=\mathbb{E}_{S\sim P^m}\left[\mathfrak{R}_S(\mathcal{F})\right]
$$

Lemma 3 (Generalization bound with Rademacher complexity) . *Let* F *be a family of loss functions*  $\mathcal{F} = \{(x, y) \mapsto \ell((x, y), h) : h \in \mathcal{H}\}\$  *with*  $\ell((x, y), h) \in [0, 1]$  *for all*  $\ell, (x, y)$  *and* h. *Then, with probability*  $1 - \delta$ *, the generalization gap is* 

$$
L(h) - \hat{L}(h) \leq 2\Re_U(\mathcal{F}) + 3\sqrt{\frac{\log(2/\delta)}{2n}},
$$

*for all*  $h \in H$  *and samples* U *of size n*.

The proof of this classical result could be found in most machine learning textbooks, like [\[46\]](#page-12-15). Since  $\ell(\cdot)$  is 1-Lipschitz, we can derive that  $\Re_{U_S}(\ell \circ \mathcal{H}) \leq \Re_{U_S}(\mathcal{H})$ .

Theorem [3](#page-6-1) involves the representation distance, below we prove the triangle inequality for representation distance.

**Lemma 4 (Triangle inequality for Representation distance)** . *for any functions*  $\phi_S$ ,  $\phi_T$ ,  $\phi_U$ *and distribution* D*, we have:*

 $D_{\text{Rep}}(\phi_S \to \phi_T; D) \leq D_{\text{Rep}}(\phi_S \to \phi_U; D) + D_{\text{Rep}}(\phi_U \to \phi_T; D)$ 

*Proof.* Let's denote:

$$
d_1 = D_{\text{Rep}}(\phi_S \to \phi_U; D)
$$
  
$$
d_2 = D_{\text{Rep}}(\phi_U \to \phi_T; D)
$$

By definition:

$$
\begin{aligned} d_1 &= \inf_{W_1\in\mathbb{R}^{m\times n}, b_1\in\mathbb{R}^m} \mathbb{E}_{x\sim D} \ell(W_1\phi_S(x)+b_1,\phi_U(x)) \\ d_2 &= \inf_{W_2\in\mathbb{R}^{m\times n}, b_2\in\mathbb{R}^m} \mathbb{E}_{x\sim D} \ell(W_2\phi_U(x)+b_2,\phi_T(x)) \end{aligned}
$$

We need to show:

$$
D_{\text{Rep}}(\phi_S \to \phi_T; D) \le d_1 + d_2
$$

Consider the composition of the transformations:

$$
\phi_S(x) \xrightarrow{W_1, b_1} \phi_U(x) \xrightarrow{W_2, b_2} \phi_T(x)
$$

The combined transformation can be written as:

$$
W_2(W_1 \phi_S(x) + b_1) + b_2 = W_2 W_1 \phi_S(x) + W_2 b_1 + b_2
$$

Using the properties of the loss function  $\ell$ , we can write:

$$
\ell(W_2W_1\phi_S(x) + W_2b_1 + b_2, \phi_T(x)) \leq \ell(W_1\phi_S(x) + b_1, \phi_U(x)) + \ell(W_2\phi_U(x) + b_2, \phi_T(x))
$$

This inequality holds for the reason that it can only break when the two items at right-hand side are both 0, in which the left side is also 0 due to rule of composition. Thus the inequality holds for all  $x$ and  $W_1, b_1, W_2, b_2$ .

Taking expectations over  $x \sim D$ :

$$
\mathbb{E}_{x \sim D} \ell(W_2 W_1 \phi_S(x) + W_2 b_1 + b_2, \phi_T(x)) \leq \mathbb{E}_{x \sim D} \ell(W_1 \phi_S(x) + b_1, \phi_U(x)) + \mathbb{E}_{x \sim D} \ell(W_2 \phi_U(x) + b_2, \phi_T(x))
$$

Taking the infimum over  $W_1$ ,  $b_1$  and  $W_2$ ,  $b_2$ :

$$
\inf_{W_1, b_1, W_2, b_2} \mathbb{E}_{x \sim D} \ell(W_2 W_1 \phi_S(x) + W_2 b_1 + b_2, \phi_T(x)) \le d_1 + d_2
$$

Since the left-hand side is an upper bound for  $D_{\text{Rep}}(\phi_S \to \phi_T; D)$ , we have:

$$
D_{\text{Rep}}(\phi_S \to \phi_T; D) \le d_1 + d_2
$$

Therefore, the triangle inequality holds for the metric  $D_{\text{Rep}}$ .

$$
\Box
$$

Now we are ready to prove Theorem [3.](#page-6-1)

*Proof.* For arbitrary  $W$ ,  $b$ , with probability  $1 - \delta$ :

$$
\mathrm{D}_{\mathrm{Rep}}(\phi_{\theta}\to \phi^{\star};X)\leq \epsilon_{X}(W\phi_{\theta}+b,\phi^{\star})\leq \epsilon_{D_{X}}(W\phi_{\theta}+b,\phi^{\star})+2\mathfrak{R}_{D_{X}}(\Phi)+3\sqrt{\frac{\log(2/\delta)}{2\left|D_{X}\right|}}
$$

Taking the infimum over  $W, b$ :

$$
\mathrm{D}_{\mathrm{Rep}}(\phi_{\theta} \to \phi^{\star};X) \leq \mathrm{D}_{\mathrm{Rep}}(\phi_{\theta} \to \phi^{\star};D_X) + 2\mathfrak{R}_{D_X}(\Phi) + 3\sqrt{\frac{\log(2/\delta)}{2\left|D_X\right|}}
$$

Using the triangle inequality for representation distance:

$$
\mathsf{D}_{\mathsf{Rep}}(\phi_{\theta}\to \phi^{\star};D_X)\leq \mathsf{D}_{\mathsf{Rep}}(\phi_{\theta}\to \psi;D_X)+\mathsf{D}_{\mathsf{Rep}}(\psi\to \phi^{\star};D_X)
$$

For arbitrary  $W$ ,  $b$ , using the proposition above,

$$
D_{\text{Rep}}(\phi_{\theta} \to \psi; D_X) \le \epsilon_{D_X}(W\phi_{\theta} + b, \psi) \le \epsilon_{S_X}(W\phi_{\theta} + b, \psi) + D_{\text{TV}}(S_X, D_X)
$$

Taking the infimum over  $W, b$ :

$$
D_{\text{Rep}}(\phi_{\theta} \to \psi; D_X) \leq D_{\text{Rep}}(\phi_{\theta} \to \psi; S_X) + D_{\text{TV}}(S_X, D_X)
$$

Combining the above results, we have:

$$
D_{\text{Rep}}(\phi_{\theta} \to \phi^{\star}; X) \leq D_{\text{Rep}}(\psi \to \phi^{\star}; D_X) + D_{\text{Rep}}(\phi_{\theta} \to \psi; S_X) + 2\Re_{D_X}(\Phi) + 3\sqrt{\frac{\log(2/\delta)}{2|D_X|}}
$$

$$
+ D_{\text{TV}}(S_X, D_X)
$$

Impact of initialization In this part we leverage the Neural Tangent Kernel (NTK) framework [\[29\]](#page-11-15) to deduce that a neural network initialized closer to a target function  $f^*$  converges faster during training. Under the NTK regime, neural networks exhibit linearized training dynamics, allowing us to predict how the network's output evolves during training.

In the context of supervised learning, consider a neural network with parameters  $\theta$  and output function  $f(x; \theta)$ , where x is the input. The goal is to approximate a target function  $f^*(x)$  by minimizing a loss function, typically the mean squared error (MSE):

$$
L(\theta) = \frac{1}{2} \sum_{i=1}^{n} (f(x_i; \theta) - y_i)^2
$$

where  $\{(x_i, y_i)\}_{i=1}^n$  is the training data and  $y_i = f^*(x_i)$ .

Under gradient descent with learning rate  $\eta$ , the parameter updates are:

$$
\theta_{t+1} = \theta_t - \eta \nabla_{\theta} L(\theta_t)
$$

In the NTK regime, where the network width tends to infinity, the network's output evolves linearly with respect to the parameters around initialization  $\theta_0$  [\[38\]](#page-12-16). We can approximate:

$$
f(x; \theta) \approx f(x; \theta_0) + \nabla_{\theta} f(x; \theta_0)^{\top} (\theta - \theta_0)
$$

This linearization allows us to express the evolution of the network's output as:

$$
f_t(x) = f_0(x) - \eta \sum_{s=0}^{t-1} \sum_{i=1}^n K(x, x_i)(f_s(x_i) - y_i)
$$

where  $f_t(x) = f(x; \theta_t)$  is the network output at time t and  $K(x, x') = \nabla_{\theta} f(x; \theta_0)^\top \nabla_{\theta} f(x'; \theta_0)$  is the Neural Tangent Kernel.

In continuous time (gradient flow), the training dynamics can be described by a differential equation:

$$
\frac{df_t(x)}{dt} = -\sum_{i=1}^{n} K(x, x_i)(f_t(x_i) - y_i)
$$

Vectorizing over all training inputs, we have:

$$
\frac{d\mathbf{f}_t}{dt} = -\mathbf{K}(\mathbf{f}_t - \mathbf{y})
$$

where  $\mathbf{f}_t = [f_t(x_1), f_t(x_2), \dots, f_t(x_n)]^\top$ ,  $\mathbf{y} = [y_1, y_2, \dots, y_n]^\top$  and K is the NTK matrix with entries  $K_{ij} = K(x_i, x_j)$ . This differential equation has the solution:

$$
\mathbf{f}_t - \mathbf{y} = e^{-\mathbf{K}t}(\mathbf{f}_0 - \mathbf{y})
$$

This equation shows that the error  $f_t - y$  decays exponentially over time, with the rate governed by the NTK matrix K. Since K is symmetric, we can decompose K into its eigenvalues  $\{\lambda_i\}$  and corresponding eigenvectors  $\{v_i\}$ :

$$
\mathbf{K} = \sum_j \lambda_j \mathbf{v}_j \mathbf{v}_j^\top
$$

the solution becomes:

$$
\mathbf{f}_t - \mathbf{y} = \sum_j e^{-\lambda_j t} c_j \mathbf{v}_j
$$

where  $c_j = \mathbf{v}_j^\top (\mathbf{f}_0 - \mathbf{y})$ . The rate of convergence for each component of the error is proportional to the corresponding eigenvalue  $\lambda_j$  of the NTK. Larger eigenvalues lead to faster decay. Initial error matters: the initial error  $f_0 - y$  scales the amplitude of the exponential terms. A smaller initial error means the network starts closer to the target function, reducing the time required for the error to decay to a specific threshold.

Suppose we want the error to be less than  $\epsilon$ :

$$
\|\mathbf{f}_t - \mathbf{y}\| \leq \epsilon
$$

Using the solution:

$$
\|\mathbf{f}_t - \mathbf{y}\| \leq \|e^{-\lambda_{\min}t}\|\|\mathbf{f}_0 - \mathbf{y}\|
$$

where  $\lambda_{\min}$  is the smallest (positive) eigenvalue of **K**. Solving for time t:

$$
t \ge \frac{1}{\lambda_{\min}} \ln \left( \frac{\|\mathbf{f}_0 - \mathbf{y}\|}{\epsilon} \right)
$$

By leveraging the NTK framework, we've shown that a better initialization(meaning the neural network's initial output function is closer to the target function  $f^*$ ) leads to faster convergence during training. This is because the training dynamics under NTK are linear, and the exponential error decay is directly influenced by the magnitude of the initial error. Analysis above implies that if we can train a network that is closer to the target function, it will converge faster to the target function.

<span id="page-24-1"></span>

#### D.1 Relation between data distribution and Rademacher complexity

Now let's look at the Rademacher complexity appeared above more carefully. Let 1-Lipschitz positive homogeneous activation  $\sigma_i$  be given, and

$$
\mathcal{H} := \left\{ \mathbf{x} \mapsto \sigma_L \left( W_L \sigma_{L-1} \left( \cdots \sigma_1 \left( W_1 \mathbf{x} \right) \cdots \right) \right) : \left\| W_i \right\|_{\mathrm{F}} \leq B, \mathbf{x} \in \mathbb{R}^d \right\}.
$$

Then using Theorem 1 in [\[23\]](#page-11-16), for samples S of size  $m$  we have bound for the empirical Rademacher complexity:

$$
\Re_S(\mathcal{H}) \leq \frac{1+\sqrt{2L\ln 2}}{m} B^L \|X\|_{\mathrm{F}}
$$

where  $X \in \mathbb{R}^{d \times m}$  is the input data matrix, and  $\|\cdot\|_{\text{F}}$  denotes the Frobenius norm.

If we further assume that the data are drawn from the distribution  $G$  (stated in Section [3.2\)](#page-3-0) with covariance  $\Sigma$  (for simplicity let  $\mu_1 = -\mu$ ,  $\mu_2 = \mu$ ), then we can bound the Rademacher complexity of  $H$  with respect to  $G$ :

$$
\mathfrak{R}_{G,m}(\mathcal{H}) = \mathbb{E}_{S \sim G^m} [\mathfrak{R}_S(\mathcal{H})]
$$

$$
\leq \frac{1 + \sqrt{2L \ln 2}}{m} B^L \mathbb{E}_{S \sim G^m} ||X||_{\mathrm{F}}
$$

$$
= \frac{\sqrt{2} + 2\sqrt{L \ln 2}}{m} B^L \cdot \Sigma \Gamma\left(\frac{1 + dm}{2}\right) M\left(-\frac{1}{2}, \frac{dm}{2}, -\frac{dm\mu^2}{2\Sigma^2}\right) / \Gamma\left(\frac{dm}{2}\right)
$$

The right part of the last inequality is an increasing function with respect to  $\Sigma$ , and  $\Gamma(\cdot)$  denotes the gamma function,  $M(\cdot, \cdot, \cdot)$  is the Kummer's confluent hypergeometric function, given by:

$$
M(a, b, z) = \sum_{n=0}^{\infty} \frac{a^{(n)} z^n}{b^{(n)} n!} = {}_1F_1(a; b; z),
$$

where:

$$
a^{(0)} = 1,
$$
  
\n $a^{(n)} = a(a+1)(a+2)\cdots(a+n-1),$ 

is the rising factorial.

**Remark.** Usually, the Rademacher complexity  $\Re_S(\ell \circ \mathcal{F})$  could be bounded by of  $\Re_S(\mathcal{F})$ . For example, if  $\ell$  is L-lipschitz, then  $\Re_S(\ell \circ \mathcal{F}) \leq L \cdot \Re_S(\mathcal{F})$ . That's why we directly compute the Rademacher complexity of  $H$  instead of  $\ell \circ H$ .

<span id="page-24-0"></span>

### E Explanation of Rescaling Samples

Dataset distillation seeks to create a condensed dataset that allows models to achieve performance comparable to those trained on the full dataset, but with fewer training steps. In this section, we will demonstrate that rescaling the variance of Gaussian distributions, as defined in Definition [3,](#page-3-4) does not affect the optimal performance of models trained on these rescaled data.

*Proof.* Consider two Gaussian distributions  $\mathcal{N}_0(\mu_1, \sigma^2 \mathbf{I})$  and  $\mathcal{N}_1(\mu_2, \sigma^2 \mathbf{I})$  with means  $\mu_1 = 1$  and  $\mu_2 = 2$ , and variance  $\sigma^2$ . We define the bimodal mixture distribution  $G = (G_X, G_Y)$  such that  $(\mathbf{x}, y)$ is sampled according to:

$$
\mathbf{x} = (1 - y) \cdot \mathbf{x}_0 + y \cdot \mathbf{x}_1, \quad \text{with} \quad y \sim \text{Bernoulli}(0.5), \quad \mathbf{x}_0 \sim \mathcal{N}_0, \quad \mathbf{x}_1 \sim \mathcal{N}_1.
$$

The decision rule for optimal classification is determined by the likelihood ratio test. For a given sample x, the log-likelihood ratio  $\Lambda(x)$  is given by:

$$
\Lambda(\mathbf{x}) = \log \frac{P(\mathbf{x} \mid y = 1)}{P(\mathbf{x} \mid y = 0)}.
$$

Since  $x_0$  and  $x_1$  are drawn from Gaussian distributions, their probability density functions are:

$$
P(\mathbf{x} \mid y = 0) = \frac{1}{(2\pi\sigma^2)^{d/2}} \exp\left(-\frac{1}{2\sigma^2} ||\mathbf{x} - \mu_1||^2\right),
$$
  
$$
P(\mathbf{x} \mid y = 1) = \frac{1}{(2\pi\sigma^2)^{d/2}} \exp\left(-\frac{1}{2\sigma^2} ||\mathbf{x} - \mu_2||^2\right).
$$

Substituting these into the log-likelihood ratio, we have:

$$
\Lambda(\mathbf{x}) = \log \frac{\frac{1}{(2\pi\sigma^2)^{d/2}} \exp \left(-\frac{1}{2\sigma^2} ||\mathbf{x} - \mu_2||^2\right)}{\frac{1}{(2\pi\sigma^2)^{d/2}} \exp \left(-\frac{1}{2\sigma^2} ||\mathbf{x} - \mu_1||^2\right)}.
$$

Simplifying, we obtain:

$$
\Lambda(\mathbf{x}) = -\frac{1}{2\sigma^2} ||\mathbf{x} - \mu_2||^2 + \frac{1}{2\sigma^2} ||\mathbf{x} - \mu_1||^2.
$$

Further simplification gives:

$$
\Lambda(\mathbf{x}) = \frac{1}{2\sigma^2} (||\mathbf{x} - \mu_1||^2 - ||\mathbf{x} - \mu_2||^2).
$$

Since the optimal decision threshold for balanced classes (i.e.,  $P(y = 1) = P(y = 0) = 0.5$ ) is  $\Lambda(\mathbf{x}) = 0$ , we set:

$$
\|\mathbf{x} - \mu_1\|^2 - \|\mathbf{x} - \mu_2\|^2 = 0.
$$

Expanding and rearranging, we derive:

$$
\|\mathbf{x}\|^2 - 2\mu_1 \mathbf{x} + \mu_1^2 - \|\mathbf{x}\|^2 + 2\mu_2 \mathbf{x} - \mu_2^2 = 0.
$$

This simplifies to:

$$
2(\mu_2 - \mu_1)\mathbf{x} + (\mu_1^2 - \mu_2^2) = 0,
$$
  
$$
2(\mu_2 - \mu_1)\mathbf{x} = \mu_2^2 - \mu_1^2,
$$
  
$$
\mathbf{x} = \frac{\mu_2^2 - \mu_1^2}{2(\mu_2 - \mu_1)}.
$$

Solving yields:

$$
\mathbf{x} = \frac{\mu_2 + \mu_1}{2}.
$$

Therefore, the optimal decision boundary is  $x = 1.5$ , which is independent of the variance  $\sigma^2$ . This completes the proof.  $\Box$ 

Regarding efficiency, utilizing scaled data to train similar models with fewer training steps is proven in Section [B.](#page-17-0) Additionally, rescaling each Gaussian distribution preserves their means, which aligns with the objectives of conventional distribution matching-based dataset distillation methods. These methods aim to distill data while maintaining the distributional properties, specifically their means.

<span id="page-26-0"></span>

### F Detailed Methodology of RELA-D

Recall that  $\phi_{\theta} : \mathbb{R}^d \to \mathbb{R}^n$ . We then introduce a transport matrix  $\mathbf{W} \in \mathbb{R}^{m \times n}$  and define the combined model as  $\mathbf{W}\phi_{\theta}(\cdot) \in \mathbb{R}^{m}$ . During the training phase, the parameters W and  $\theta$  are jointly optimized. However, as the dimension  $m$  increases, the computational complexity of the optimization grows rapidly. To address this issue, we propose reducing the dimensionality of the target matrix  $R_{\rm Y}$ from m to n, where  $n < m$ :

<span id="page-26-2"></span>
$$
R'_{\mathbf{Y}} = V_n^{\top} \left( \frac{R_{\mathbf{Y}} - \mu}{\sigma} \right) \text{ s.t. } \frac{1}{|R_{\mathbf{Y}}| - 1} \left( \left( R_{\mathbf{Y}} - \mu \right)^{\top} \left( R_{\mathbf{Y}} - \mu \right) \right) = V \Lambda V^{\top}, \ V_n = V[:, : n], \tag{10}
$$

where  $\mu$  and  $\sigma$  denote the mean and standard deviation, respectively, of each column in the  $R_{\rm Y}$ . Practically, we use batch PCA to perform the computation shown in [\(10\)](#page-26-2), as illustrated in [L.](#page-32-1)

<span id="page-26-1"></span>

#### F.1 Proof for Ideal Properties of Prior Models

We aim to demonstrate that modern deep learning methods can effectively train models to serve as robust prior models by extracting sufficient information from samples as representations.

Therefore, we poist the existence of a prior model  $\xi$  capable of losslessly extracting the information of samples  $D_{\mathcal{X}}$  when trained using the InfoNCE loss [\[47\]](#page-12-6), a method prevalently employed in contemporary deep learning algorithms [\[12\]](#page-10-4).

*Proof.* To demonstrate that an encoder  $\xi$  trained with the InfoNCE loss preserves all information from the input data  $D_{\mathcal{X}}$ , we proceed as follows.

#### 1. Definitions and Setup

Let X denote the input data space with data distribution  $p_{data}(x)$ . Let  $p_{pos}(x, x^+)$  denote the distribution of positive pairs, typically generated via data augmentation. The encoder  $\xi : \mathcal{X} \to \mathbb{R}^d$ maps inputs to d-dimensional representations. The similarity function  $q : \mathbb{R}^d \times \mathbb{R}^d \to \mathbb{R}$  (e.g., dot product) measures similarity between representations. Given a positive pair  $(x, x^+)$ , let  $\{x_i^{\dagger}\}_{i=1}^N$  be N negative samples drawn i.i.d. from  $p_{data}(x)$ . The InfoNCE loss is defined as:

$$
\mathcal{L}_{\text{InfoNCE}}(\xi, q) = -\mathbb{E}_{(x,x^+) \sim p_{\text{pos}}} \left[ \log \frac{e^{q(\xi(x), \xi(x^+))/\tau}}{\sum_{i=1}^N e^{q(\xi(x), \xi(x^-_i))/\tau}} \right]
$$

where  $\tau > 0$  is a temperature parameter.

#### 2. InfoNCE as a Mutual Information Lower Bound

The InfoNCE objective serves as a lower bound to the mutual information between representations of positive pairs:

$$
I(\xi(X); \xi(X^+)) \ge \mathcal{J}(\xi, q)
$$

where

$$
\mathcal{J}(\xi, q) = \mathbb{E}_{(x,x^+) \sim p_{\text{pos}}} \left[ q(\xi(x), \xi(x^+)) - \tau \cdot \log \left( \sum_{i=1}^N e^{q(\xi(x), \xi(x^-_i))/\tau} \right) \right]
$$

As the number of negative samples  $N \to \infty$ , this bound becomes tight, approaching the true mutual information  $I(\xi(X); \xi(X^+))$ . 3. Optimal Encoder and Similarity Function Let  $(\xi^*, q^*)$  denote the optimal encoder and similarity function that maximize  $\mathcal{J}(\xi, q)$ . Under the assumption of infinite negative samples and an expressive similarity function, the optimal similarity satisfies:

$$
q^*(\xi^*(x), \xi^*(x^+)) = \log \frac{p_{\text{pos}}(x, x^+)}{p_{\text{data}}(x)p_{\text{data}}(x^+)} + C
$$

where C is a constant independent of  $(x, x<sup>+</sup>)$ . This aligns  $q^*$  with the pointwise mutual information (PMI) between x and  $x^+$ . 4. Injectivity through Mutual Information Maximization Maximizing  $I(\xi(X); \xi(X^+))$  encourages the encoder  $\xi$  to capture as much information about X as possible. To ensure injectivity:

• Sufficient Dimensionality: The representation dimension  $d$  must be at least as large as the intrinsic dimensionality of  $X$ .

<span id="page-27-3"></span>

| Table 4: Notations used in this section.            |                                                                                                                                                                                                              |
|-----------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Notation                                            | Meaning                                                                                                                                                                                                      |
| u1, u2<br>v                                         | current concerned samples<br>unspecified samples                                                                                                                                                             |
| u1o, vo<br>u2t, vt<br>u2s, vs<br>u2d, vd<br>u2m, vm | samples from online branch<br>samples from unspecified target branch<br>samples from weight-sharing target branch<br>samples from stop-gradient target branch<br>samples from momentum-encoder target branch |
| V<br>Vbatch<br>Vbank<br>V∞                          | unspecified sample set<br>sample set of current batch<br>sample set of memory bank<br>sample set of all previous samples                                                                                     |

• Expressive Architecture: The encoder  $\xi$  should be sufficiently expressive, potentially utilizing architectural constraints (e.g., invertible networks) to promote injectivity.

Under these conditions, maximizing mutual information implies that  $\xi^*$  approximates an injective mapping on the support of  $p_{data}(x)$ , i.e.,  $\xi^*(x) = \xi^*(x') \Rightarrow x = x'$  almost surely. **5. Existence of the Inverse Mapping** Given that  $\xi^*$  is injective, there exists a deterministic inverse mapping  $g : \mathbb{R}^d \to \mathcal{X}$ such that:

$$
g(\xi^*(x)) = x \quad \text{for all } x \in \mathcal{X}
$$

This mapping g can be constructed as the inverse of  $\xi^*$  on its image:

$$
g(z) = \xi^{*-1}(z) \quad \text{where } z \in \xi^*(\mathcal{X})
$$

 $\Box$ 

<span id="page-27-0"></span>

### G Analysis of Different Self-Supervised Learning Methods

We refer to the primary theoretical results for existing self-supervised learning methods as presented in [\[59\]](#page-13-14), where all methods are unified under a simple and cohesive framework, detailed below. Specifically, UniGrad [\[59\]](#page-13-14) demonstrates that most self-supervised learning methods can be unified by analyzing their gradients.

<span id="page-27-1"></span>

#### G.1 A Unified Framework for SSL

A typical self-supervised learning framework employs a siamese network with two branches: an online branch and a target branch. The target branch serves as the training target for the online branch.

Given an input image x, two augmented views  $x_1$  and  $x_2$  are generated, serving as inputs for the two branches. The encoder  $f(\cdot)$  extracts representations  $u_i \triangleq f(x_i)$  for  $i = 1, 2$  from these views.

Table [4](#page-27-3) details the notations used.  $u_1$  and  $u_2$  denote the current training samples, while v denotes unspecified samples.  $u_1^o$  and  $v^o$  are representations from the online branch. Three types of target branches are widely used: 1) weight-sharing with the online branch ( $u_2^s$  and  $v^s$ ); 2) weight-sharing but detached from gradient back-propagation ( $u_2^d$  and  $v^d$ ); 3) a momentum encoder updated from the online branch ( $u_2^m$  and  $v^m$ ). If unspecified,  $u_2^t$  and  $v^t$  are used. A symmetric loss is applied to the two augmented views, as described in [\[13\]](#page-10-6).

 $V$  represents the sample set in the current training step. Methods vary in constructing this set:  $V_{\rm batch}$ includes all samples from the current batch,  $V_{\text{bank}}$  uses a memory bank storing previous samples, and  $V_{\infty}$  includes all previous samples, potentially larger than a memory bank.

<span id="page-27-2"></span>

#### G.2 Contrastive Learning Methods

Contrastive learning relies on negative samples to prevent representational collapse and enhance performance. Positive samples are derived from different views of the same image, while negative samples come from other images. The goal is to attract positive pairs and repel negative pairs, typically using the InfoNCE loss [\[47\]](#page-12-6):

$$
L = \mathop{\mathbb{E}}_{u_1, u_2} \left[ -\log \frac{\exp\left(\cos(u_1^o, u_2^t)/\tau\right)}{\sum_{v^t \in \mathcal{V}} \exp\left(\cos(u_1^o, v^t)/\tau\right)} \right],\tag{11}
$$

where  $\cos(\cdot)$  denotes cosine similarity, and  $\tau$  is the temperature hyper-parameter. This formulation can be adapted for various methods, discussed below.

MoCo [\[26,](#page-11-3) [14\]](#page-10-12). MoCo uses a momentum encoder for the target branch and a memory bank for storing previous representations. Negative samples are drawn from this memory bank. The gradient for sample  $u_1^o$  is:

$$
\frac{\partial L}{\partial u_1^o} = \frac{1}{\tau N} \bigg( -u_2^m + \sum_{v^m \in \mathcal{V}_{\text{bank}}} s_v v^m \bigg),\tag{12}
$$

where  $s_v = \frac{\exp(\cos(u_1^o, v^m)/\tau)}{\sum_{y^m \in \mathcal{V}_{\text{bank}}}\exp(\cos(u_1^o, y^m)/\tau)}$  and N is the number of samples in the batch.

SimCLR [\[12\]](#page-10-4). SimCLR shares weights between the target and online branches and does not stop back-propagation. It uses all representations from other images in the batch as negative samples. The gradient is:

$$
\frac{\partial L}{\partial u_1^o} = \frac{1}{\tau N} \left( -u_2^s + \sum_{v^s \in \mathcal{V}_{\text{batch}} \setminus u_1^o} s_v v^s \right) + \frac{1}{\tau N} \left( -u_2^s + \sum_{v^s \in \mathcal{V}_{\text{batch}} \setminus u_1^o} t_v v^s \right), \quad (13)
$$

where  $t_v = \frac{\exp{(\cos(v^s, u_1^o)/\tau)}}{\sum_{v^s \in \mathcal{V}_{\text{max}} \setminus v^s} \exp{(\cos(v^s, u_1^o)/\tau)}}$  $\frac{\exp(\cos(v', u_1)/\tau)}{y^s \in V_{\text{batch}}\setminus v^s \exp(\cos(v^s, y^s)/\tau)}$ . If the gradient through the target branch is stopped, the second term vanishes.

Unified Gradient. The gradient for these methods can be unified as:

$$
\frac{\partial L}{\partial u_1^o} = \frac{1}{\tau N} \bigg( -u_2^t + \sum_{v^t \in \mathcal{V}} s_v v^t \bigg),\tag{14}
$$

comprising a weighted sum of positive and negative samples. The term  $-u_2^t$  pulls positive samples together, while  $\sum_{v^t \in V} s_v v^t$  pushes negative samples apart. The main difference between methods lies in the target branch used and the construction of the contrastive sample set  $V$ .

<span id="page-28-0"></span>

#### G.3 Asymmetric Network Methods

Asymmetric network methods learn representations by maximizing the similarity of positive pairs without using negative samples. These methods require symmetry-breaking network designs to avoid representational collapse. A predictor  $h(\cdot)$  is appended after the online branch, and the gradient to the target branch is stopped. The objective function is:

<span id="page-28-1"></span>
$$
L = \mathop{\mathbb{E}}_{u_1, u_2} \left[ -\cos(h(u_1^o), u_2^t) \right].
$$
 (15)

**Relation to BYOL [\[30\]](#page-11-2).** BYOL uses a momentum encoder for the target branch, i.e.,  $u_2^t = u_2^m$  in Eq. $(15)$ .

**Relation to Simsiam [\[13\]](#page-10-6).** Simsiam shows that a momentum encoder is unnecessary and only applies the stop-gradient operation to the target branch, i.e.,  $u_2^t = u_2^d$  in Eq.[\(15\)](#page-28-1).

Unified Gradient. Despite the performance of asymmetric network methods, the avoidance of collapse solutions is not well understood. DirectPred [\[60\]](#page-13-17) explores this by studying training dynamics and proposes an analytical solution for the predictor  $h(\cdot)$ .

DirectPred formulates the predictor as  $h(v) = W_h v$ , with  $W_h$  calculated based on the correlation matrix  $\mathbb{E}_{v}(vv^T)$ . The correlation matrix, F, is computed as the moving average for each batch:

 $F \triangleq \sum_{v^o \in V_{\infty}} \rho_v v^o v^o$ , where  $\rho_v$  is the moving average weight. Decomposing F into eigenvalues  $\Lambda_F$  and eigenvectors U,  $W_h$  is:

$$
W_h = U\Lambda_h U^T, \ \Lambda_h = \Lambda_F^{1/2} + \epsilon \lambda_{max} I,\tag{16}
$$

where  $\lambda_{max}$  is the max eigenvalue of F and  $\epsilon$  is a hyper-parameter to boost small eigenvalues.

DirectPred also derives the gradient:

$$
\frac{\partial L}{\partial u_1^o} = \frac{1}{||W_h u_1^o||_2 N} \bigg( -W_h^T u_2^t + \lambda \sum_{v^o \in \mathcal{V}_{\infty}} (\rho_v u_1^{oT} v^o) v^o \bigg),\tag{17}
$$

where  $-W_h^T u_2^t$  and  $\sum_{v \in V_\infty} (\rho_v u_1^{oT} v^o)v^o$  act as positive and negative gradients respectively, and  $\lambda = \frac{u_1^{\sigma T} W_h^T u_2^t}{u_1^{\sigma T} (F + \epsilon^2 I) u_1^{\sigma}}$  is a balance factor.

Though negative samples are absent in the loss function, they emerge from the predictor network's optimization. The eigenspace of the predictor  $W_h$  aligns with the feature correlation matrix  $F$ , encoding its information. During back-propagation, this encoded information functions as a negative gradient, influencing the optimization direction.

<span id="page-29-0"></span>

#### G.4 Feature Decorrelation Methods

Feature decorrelation methods have recently emerged as a novel approach in self-supervised learning. These methods aim to reduce redundancy among different feature dimensions to prevent collapse. Various loss functions have been proposed for this purpose. We examine their relations below.

Relation to Barlow Twins [\[69\]](#page-13-1). Barlow Twins employs the following loss function:

<span id="page-29-1"></span>
$$
L = \sum_{i=1}^{C} (W_{ii} - 1)^2 + \lambda \sum_{i=1}^{C} \sum_{j \neq i} W_{ij}^2,
$$
 (18)

where  $W = \frac{1}{N} \sum_{v_1^o, v_2^s \in V_{\text{batch}}} v_1^o v_2^{sT}$  is a cross-correlation matrix, C denotes the number of feature dimensions, and  $\lambda$  is a balancing hyper-parameter. The diagonal elements of W are encouraged to be close to 1, while the off-diagonal elements are forced towards 0.

Despite appearing different, Eq. [\(18\)](#page-29-1) operates similarly to previous methods from a gradient perspective, calculated as:

$$
\frac{\partial L}{\partial u_1^o} = \frac{2}{N} \left( -Au_2^s + \lambda \sum_{v_1^o, v_2^s \in \mathcal{V}_{\text{batch}}} \frac{u_2^{sT} v_2^s}{N} v_1^o \right),\tag{19}
$$

where  $A = I - (1 - \lambda)W_{\text{diag}}$  and  $(W_{\text{diag}})_{ij} = \delta_{ij}W_{ij}$  is the diagonal matrix of W. Barlow Twins applies batch normalization instead of  $\ell_2$  normalization to the representation v.

Relation to VICReg [\[3\]](#page-10-8). VICReg modifies Barlow Twins with the following loss function:

$$
L = \frac{1}{N} \sum_{v_1^o, v_2^s \in \mathcal{V}_{\text{batch}}} ||v_1^o - v_2^s||_2^2 + \frac{\lambda_1}{C} \sum_{i=1}^C \sum_{j \neq i}^C W_{ij}^{\prime 2} + \frac{\lambda_2}{C} \sum_{i=1}^C \max(0, \gamma - \text{std}(v_1^o)_i)
$$
(20)

where  $W' = \frac{1}{N-1} \sum_{v_1^o \in V_{\text{batch}}} (v_1^o - \bar{v}_1^o)(v_1^o - \bar{v}_1^o)^T$  is the covariance matrix of the same view, std $(v)_i$ denotes the standard deviation of the *i*-th channel of v,  $\gamma$  is a constant target value, and  $\lambda_1$ ,  $\lambda_2$  are balancing weights.

The gradient is derived as follows:

$$
\frac{\partial L}{\partial u_1^o} = \frac{2}{N} \left( -u_2^s + \lambda \sum_{v_1^o \in \mathcal{V}_{\text{batch}}} \frac{\tilde{u}_1^{oT} \tilde{v}_1^o}{N} \tilde{v}_1^o \right) + \frac{2\lambda}{N} \left( \frac{1}{\lambda} u_1^o - B \tilde{u}_1^o \right), \quad (21)
$$

where  $\tilde{v} = v - \bar{v}$  is the de-centered sample,  $\lambda = \frac{2\lambda_1 N^2}{c(N-1)^2}$ , and  $B = \frac{N}{c\lambda(N-1)}(2\lambda_1 W_{\text{diag}}' +$  $\frac{\lambda_2}{2}$  diag( $1(\gamma - \text{std}(v_1^o) > 0) \oslash \text{std}(v_1^o)$ )). Here, diag(x) is a matrix with the vector x on its diagonal,  $\mathbf{1}(\cdot)$  is the indicator function, and ⊘ denotes element-wise division.

VICReg does not normalize  $v$ ; instead, it uses de-centering and a standard deviation term in the loss function.

Unified Gradient. Given the equivalence of  $v^s$  and  $v^o$ , the gradient for feature decorrelation methods can be unified as:

$$
\frac{\partial L}{\partial u_1^o} = \frac{2}{N} \left( -u_2^t + \lambda \sum_{v^o \in \mathcal{V}_{\text{batch}}} \frac{u^{oT} v^o}{N} v_1^o \right),\tag{22}
$$

where  $-u_2^t$  is the positive gradient, and  $\sum_{v^o \in \mathcal{V}_{\text{batch}}} \left( \frac{u^{oT}v^o}{N} \right)$  $\left(\frac{x_v}{N}\right)v_1^o$  is the negative gradient.  $\lambda$  is a balancing factor. The difference between methods lies in the subscript for the negative coefficient. Feature decorrelation methods function similarly to other self-supervised methods, with the positive and negative gradients derived from the diagonal and off-diagonal elements of the correlation matrix.

<span id="page-30-1"></span>

### H Budget of RELA for Data Synthesis

While training on the distilled dataset is both efficient and effective, the distillation process in optimization-based approaches  $[11, 72, 65]$  $[11, 72, 65]$  $[11, 72, 65]$  $[11, 72, 65]$  $[11, 72, 65]$  is computationally intensive  $[18, 56]$  $[18, 56]$  $[18, 56]$ , often exceeding the computational load of training on the full dataset.

In contrast, the synthetic data generated by our RELA framework requires a budget less than that of training for a single epoch (c.f. Section [4.1\)](#page-7-1). This is because the synthesis budget of our RELA is equivalent to performing inference over the entire dataset  $D<sub>X</sub>$ . Consequently, this computational expense is negligible given that training epochs typically number around 100.

<span id="page-30-0"></span>

### I RELA in Labeled Dataset Distillation and Human-Supervised Learning

We apply our RELA in labeled dataset distillation and human-supervised learning.

<span id="page-30-2"></span>

#### I.1 Experimental Setup

Datasets and Neural Network Architectures. We conduct experiments on datasets of varying scales and resolutions.

- Small-scale: We evaluate on CIFAR-10  $(32 \times 32)$  [\[35\]](#page-11-11) and CIFAR-100  $(32 \times 32)$  [\[34\]](#page-11-12).
- Large-scale: We utilize Tiny-ImageNet  $(64 \times 64)$  [\[36\]](#page-12-11) and ImageNet-1K  $(224 \times 224)$  [\[20\]](#page-11-0).

Consistent with previous works on dataset distillation  $[67, 73, 24]$  $[67, 73, 24]$  $[67, 73, 24]$  $[67, 73, 24]$  $[67, 73, 24]$ , we use ConvNet  $[24]$  and ResNet-{18,50} [\[25\]](#page-11-7) as our backbone networks across all datasets. Specifically, Conv-3 is employed for CIFAR-10/100, and Conv-4 for Tiny-ImageNet and ImageNet-1K.

Baselines. We compare our method with several SOTA distillation methods capable of scaling to large high-resolution datasets, including G-VBSM [\[53\]](#page-13-4),  $SRe<sup>2</sup>L$  [\[67\]](#page-13-12), and RDED [\[56\]](#page-13-3). To the best of our knowledge, SRe<sup>2</sup>L, G-VBSM, and RDED are the only published works that efficiently scale to datasets of any size, making them our closest baselines. All distilled datasets synthesized from these baselines undergo the same post-training process. Results are reported in Table [5.](#page-31-3) For our RELA, the prior models used for distillation are identical to the pre-trained models employed in the baseline methods.

<span id="page-30-3"></span>

#### I.2 Main Results

Table [5](#page-31-3) demonstrates the superiority of our RELA, despite incorporating a zero-cost sample synthesis process.

|         |                                                                                                                                                                                                           |       | ConvNet     |                                                  |                                                                                                                                    |                                     | ResNet-18 |                                                                                                                                                                                                                                         |                                               |                                     | ResNet-50                                                                                      |                                                |
|---------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------|-------------|--------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------|-----------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------|-------------------------------------|------------------------------------------------------------------------------------------------|------------------------------------------------|
|         | Dataset IPC G-VBSM                                                                                                                                                                                        | SRe2L | <b>RDED</b> | RELA (Ours) G-VBSM                               |                                                                                                                                    | SRe2L                               | RDED      | RELA (Ours) G-VBSM                                                                                                                                                                                                                      |                                               | SRe2L                               | <b>RDED</b>                                                                                    | RELA (Ours)                                    |
| $CF-10$ | $1 \ \ 21.2 \pm 0.2 \ \ 22.2 \pm 1.1 \ \ 28.9 \pm 0.4$<br>$10\,$ 38.6 $\pm$ 0.8 39.6 $\pm$ 0.4 56.0 $\pm$ 0.1<br>50 $\left.62.7 \pm 0.5\right.57.7 \pm 0.4\right.71.1 \pm 0.2$                            |       |             | $40.3 + 0.4$<br>$67.0 + 0.4$<br>$77.3 \pm 0.1$   | $64.5 \pm 0.6$ 62.8 $\pm$ 1.2 76.4 $\pm$ 0.4                                                                                       |                                     |           | $17.0 \pm 1.0$ 19.9 $\pm$ 0.9 27.8 $\pm$ 0.4 35.0 $\pm$ 0.2 17.2 $\pm$ 0.9 20.2 $\pm$ 0.5 24.8 $\pm$ 0.5<br>$136.3 \pm 0.7$ 39.4 $\pm$ 0.9 47.3 $\pm$ 0.5 74.7 $\pm$ 0.1 33.8 $\pm$ 1.1 37.2 $\pm$ 0.6 45.1 $\pm$ 0.7<br>$89.2 \pm 0.1$ |                                               |                                     | $61.5 \pm 0.6$ 61.6 $\pm$ 0.2 74.1 $\pm$ 0.6                                                   | $31.7 + 0.3$<br>$70.9 + 1.3$<br>$88.5 + 0.3$   |
|         | $1 \left[ 13.4 \pm 0.3 \right] 12.9 \pm 0.1 \left[ 21.8 \pm 0.4 \right]$<br>CF-100 10 38.7 $\pm$ 0.8 34.2 $\pm$ 0.3 47.0 $\pm$ 0.3<br>50 $\vert 53.8 \pm 0.4 \vert 52.2 \pm 0.3 \vert 55.3 \pm 0.2 \vert$ |       |             | $32.5 + 0.3$<br>$51.3 \pm 0.1$<br>$56.3 \pm 0.3$ | $13.4 \pm 0.5$ 11.5 $\pm$ 0.4 4.6 $\pm$ 0.1<br>$47.0 + 0.4$ 42.7 + 0.5 53.4 + 0.3<br>$160.0 \pm 0.1$ 57.4 $\pm$ 0.2 64.0 $\pm$ 0.0 |                                     |           | $31.7 + 1.2$ 12.6 + 0.6 10.1 + 0.1 4.5 + 0.2<br>64.8 + 0.0 $\vert 47.5 + 0.5 \vert 44.2 + 0.5 \vert 54.0 + 0.3 \vert$<br>68.8 $\pm$ 0.2 $\left. \right $ 62.2 $\pm$ 0.3 60.6 $\pm$ 0.2 65.8 $\pm$ 0.3                                   |                                               |                                     |                                                                                                | $27.8 + 1.5$<br>$66.2 + 0.0$<br>$69.6 \pm 0.2$ |
| T-IN    | $18.6 \pm 0.2$ 11.8 $\pm$ 0.4 17.0 $\pm$ 0.4<br>$10\,$ $33.9 \pm 0.2$ $34.5 \pm 0.5$ $41.2 \pm 0.5$<br>50 $ 46.6 \pm 0.2 \, 46.3 \pm 0.2 \, 47.1 \pm 0.2$                                                 |       |             | $27.0 + 0.3$<br>$42.9 \pm 0.1$<br>$47.4 \pm 0.2$ | $37.7 \pm 0.3$ 43.6 $\pm$ 0.5 48.4 $\pm$ 0.3<br>$152.2 \pm 0.053.4 \pm 0.357.4 \pm 0.2$                                            |                                     |           | $9.0 + 0.1$ 13.5 + 0.2 15.4 + 0.6 24.2 + 1.4   8.2 + 0.2 12.8 + 0.2 14.8 + 0.5<br>$55.8 + 0.1$<br>59.9 ± 0.0 $155.9 \pm 0.3$ 57.1 $\pm$ 0.1 59.2 $\pm$ 0.2                                                                              | $141.3 \pm 0.1$ 47.0 $\pm$ 0.1 50.2 $\pm$ 0.1 |                                     |                                                                                                | $23.2 + 1.1$<br>$58.1 + 0.4$<br>$61.5 \pm 0.0$ |
| $IN-1k$ | $13.4 + 0.1$ $2.5 + 0.0$ $5.2 + 0.1$<br>$10 22.6 + 0.7 12.7 + 0.3 20.4 + 0.3$<br>$50$  37.8 + 0.4 36.0 + 0.3 38.8 + 0.6                                                                                   |       |             | $10.8 + 0.0$<br>$32.2 \pm 0.1$<br>$52.9 + 0.4$   | $35.7 + 0.2$ 31.1 + 0.1 41.1 + 0.2<br>$51.6 \pm 0.2$ 49.5 $\pm$ 0.2 55.3 $\pm$ 0.2                                                 | $2.1 + 0.1$ $2.8 + 0.2$ $5.9 + 0.1$ |           | $5.9 \pm 0.2$<br>$48.5 + 0.3$<br>$61.3 + 0.1$                                                                                                                                                                                           |                                               | $1.6 + 0.0$ $3.0 + 0.4$ $6.8 + 0.1$ | $142.6 \pm 0.4$ 38.3 $\pm$ 0.5 46.2 $\pm$ 0.3<br>$160.3 \pm 0.1$ 58.4 $\pm$ 0.1 62.5 $\pm$ 0.1 | $5.9 + 0.2$<br>$48.5 + 0.3$<br>$61.3 + 0.1$    |

<span id="page-31-3"></span>Table 5: Comparison with SOTA Baseline Dataset Distillation Methods. In the table, bold indicates the best result. IPC refers to the number of Images Per Class for distilled datasets.

<span id="page-31-0"></span>

### J RELA Algorithm

Algorithm 1 Adaptive Loss Weighting Algorithm for RELA and Self-Supervised Learning

**Require:** Number of training steps T, Initial  $\ell_s = 2.0$ , Initial  $\ell_f = 1.0$ , Initial  $\lambda = 1$ 1: for each training step  $t = 1$  to  $T$  do<br>2: if  $\lambda = 1$  then if  $\lambda = 1$  then 3:  $\ell_c \leftarrow$  Value of  $\mathcal{L}_{\text{RELA}}$  {Retrieve the current loss value from the optimization process}<br>4:  $\ell_f \leftarrow 0.999 \times \ell_f + 0.001 \times \ell_c$  {Calculate the short-term loss} 4:  $\ell_f \leftarrow 0.999 \times \ell_f + 0.001 \times \ell_c$  {Calculate the short-term loss}<br>5:  $\ell_s \leftarrow 0.99 \times \ell_s + 0.01 \times \ell_f$  {Calculate the long-term loss} 5:  $\ell_s \leftarrow 0.99 \times \ell_s + 0.01 \times \ell_f$  {Calculate the long-term loss}<br>6: **end if** 6: end if 7: **if**  $\exp(-\max{\ell_s - \ell_f}, 0) \ge 0.995$  then<br>8:  $\lambda \leftarrow 0$  {RELA learning is converged an  $\lambda \leftarrow 0$  {RELA learning is converged and over} 9: end if 10:  $\mathcal{L} \leftarrow \lambda \cdot \mathcal{L}_{\text{RELA}} + (1 - \lambda) \cdot \mathcal{L}_{\text{SSL}}$  {Control the RELA and the SSL states using  $\lambda$ } 11: end for

In essence, this algorithm is designed to detect the convergence of RELA. Upon convergence, it transitions to the original algorithm for self-supervised learning. This implicitly segments the learning procedure into two phases: the fast stage (RELA) and the slow stage (SSL). Moreover, Figure [5](#page-31-4) depicts the dynamics of the training process.

<span id="page-31-4"></span>Image /page/31/Figure/6 description: The image contains two plots side-by-side, both showing Top-1 Accuracy (%) on the y-axis against Training Steps on the x-axis. The left plot shows 'ReLA Training' in teal, 'ReLA Validation' in blue, 'Orig. Training' in purple, and 'Orig. Validation' in red. The right plot displays the same lines with the same colors. Both plots are divided into two colored regions: a pink 'Fast Stage' on the left and a light blue 'Slow Stage' on the right. The left plot shows 'ReLA Training' reaching approximately 80% accuracy, while 'ReLA Validation' reaches about 75%. 'Orig. Training' reaches around 50%, and 'Orig. Validation' reaches about 45%. The right plot shows 'ReLA Training' reaching approximately 65%, 'ReLA Validation' around 60%, 'Orig. Training' around 45%, and 'Orig. Validation' around 40%.

Image /page/31/Figure/7 description: Figure 5 compares the training dynamics of the RELA algorithm with the original BYOL algorithm. It presents two subfigures: (a) shows RELA with CF10-T as the prior model, and (b) shows RELA with Rand. as the prior model.

<span id="page-31-1"></span>

### K Experimental Details

<span id="page-31-2"></span>

#### K.1 Detailed Setup for Experiments in Section [3.2](#page-3-0)

We show the detailed setup for the experiments in Section [3.2](#page-3-0) in Tables [6](#page-32-3) and [7.](#page-32-4)

<span id="page-32-3"></span>

| Layer         | Type                                             | Input Units | Output Units |
|---------------|--------------------------------------------------|-------------|--------------|
| Input Layer   | -                                                | 2           | -            |
| Hidden Layer  | Fully Connected (Linear)<br>Activation (ReLU)    | 2           | 50           |
| Output Layer  | Fully Connected (Linear)<br>Activation (Sigmoid) | 50          | 1            |
| Loss Function | Mean Squared Error (MSE)                         | -           | -            |

Table 6: Architecture of the simple neural network model.

<span id="page-32-4"></span>Table 7: Optimization parameters for the simple neural network model.

| <b>Parameter</b>      | <b>Value</b>                      |
|-----------------------|-----------------------------------|
| <b>Training Steps</b> | 1000                              |
| Batch Size            | 1                                 |
| Optimizer             | Stochastic Gradient Descent (SGD) |
| Learning Rate         | 0.002                             |
| Momentum              | 0.98                              |

<span id="page-32-0"></span>

#### K.2 Detailed Setup for Experiments in Section [5](#page-7-0)

<span id="page-32-5"></span>Training details for self-supervised learning methods. We show the details of training in Table [8.](#page-32-5)

Table 8: Training parameters and optimizer settings for self-supervised learning methods.

| <b>Parameter</b> | Value |
|------------------|-------|
| Epochs           | 100   |
| Optimizer        | AdamW |
| Learning Rate    | 0.001 |
| Weight Decay     | 0.01  |

Linear evaluation details. We show the details of training the linear model in Table [9.](#page-33-0)

<span id="page-32-1"></span>

### L Batch PCA Reduction

To begin, we provide a full and rigorous mathematical framework for Principal Component Analysis (PCA).

<span id="page-32-2"></span>

#### L.1 Principal Component Analysis

Given a data matrix  $Y \in \mathbb{R}^{n \times d}$  and the desired number of components k, where  $k \leq d$ , PCA aims to extract a reduced data matrix  $Y_{reduced} \in \mathbb{R}^{n \times k}$  that retains the maximum variance from the original dataset.

First, the data needs to be centered by subtracting the mean of each feature. This can be mathematically represented as:

$$
\mathbf{Y}_{\text{centered}} = \mathbf{Y} - \frac{1}{n} \mathbf{1}_n \mathbf{1}_n^T \mathbf{Y}
$$

where  $1_n$  is a column vector of ones with length n. Next, the covariance matrix of the centered data is computed:

$$
\mathbf{C} = \frac{1}{n-1}\mathbf{Y}_{\text{centered}}^T\mathbf{Y}_{\text{centered}}
$$

<span id="page-33-0"></span>

| <b>Dataset</b> | <b>Batch Size</b> | <b>Linear Model</b>                   | <b>Loss Function</b> |
|----------------|-------------------|---------------------------------------|----------------------|
| CIFAR-10       | 128               | nn.Linear( <i>feature_dim</i> , 10)   | CrossEntropyLoss()   |
| CIFAR-100      | 128               | nn.Linear( <i>feature_dim</i> , 100)  | CrossEntropyLoss()   |
| Tiny-ImageNet  | 128               | nn.Linear( <i>feature_dim</i> , 200)  | CrossEntropyLoss()   |
| ImageNet-1K    | 1024              | nn.Linear( <i>feature_dim</i> , 1000) | CrossEntropyLoss()   |
| Optimizer      |                   | Adam (learning rate: 3e-4)            |                      |

Table 9: Linear evaluation parameters for various datasets.

To identify the principal components, eigendecomposition is performed on the covariance matrix:

$$
\mathbf{C} = \mathbf{V} \mathbf{\Lambda} \mathbf{V}^T
$$

where V is the matrix of eigenvectors and  $\Lambda$  is a diagonal matrix of eigenvalues. The eigenvectors are then sorted in descending order based on their corresponding eigenvalues. The top  $k$  eigenvectors are selected to form the projection matrix:

$$
\mathbf{W} = [\mathbf{v}_1, \mathbf{v}_2, \dots, \mathbf{v}_k]
$$

where  $v_i$  represents the  $i$ -th eigenvector. Finally, the centered data is projected onto the new subspace:

 $\mathbf{Y}_{\text{reduced}} = \mathbf{Y}_{\text{centerd}} \mathbf{W}$ 

The resulting reduced data matrix  $Y_{reduced}$  contains the principal components of the original data. Each principal component is a linear combination of the original features, designed to capture as much variance as possible in the reduced space.

Algorithm 2 Batch PCA Reduction

**Require:** Data matrix  $Y \in \mathbb{R}^{n \times d}$ , Number of components k **Ensure:** Reduced data matrix  $Y_{reduced} \in \mathbb{R}^{n \times k}$ 1:  $n \leftarrow$  number of rows in Y 2:  $m \leftarrow$  BATCHSIZE {Pre-set Batch size} 3:  $\mu \leftarrow \frac{1}{n} \sum_{i=1}^{n} Y_{i}$ . {Compute the mean of Y} 4:  $\mathbf{Y}_{\text{center}} \leftarrow \mathbf{Y} - \mu$  {Center the data} 5:  $\Sigma \leftarrow 0_{d \times d}$  {Initialize the covariance matrix} 6: for  $i = 0$  to n step m do 7:  $j \leftarrow \min(i+m, n)$ 8:  $\mathbf{B} \leftarrow \mathbf{Y}_{\text{center}}[i : j, \cdot]$ 9:  $\Sigma \leftarrow \Sigma + B^{\top}B$  {Update the covariance matrix} 10: end for 11:  $\Sigma \leftarrow \frac{\Sigma}{n-1}$  {Normalize the covariance matrix} 12:  $V \leftarrow$  eigenvectors of  $\Sigma$  corresponding to the largest k eigenvalues 13:  $Y_{reduced} \leftarrow 0_{n \times k}$  {Initialize the reduced data matrix} 14: for  $i = 0$  to n step m do 15:  $j \leftarrow \min(i+m, n)$ 16:  $\mathbf{B} \leftarrow \mathbf{Y}_{\text{centered}}[i : j, \cdot]$ <br>17:  $\mathbf{Y}_{\text{reduced}}[i : j, \cdot] \leftarrow \mathbf{B}$  $\mathbf{Y}_{reduced}[i : j, \cdot] \leftarrow \mathbf{BV}$ 18: end for 19: return Y<sub>reduced</sub>

Then we prove that the batch PCA we utilized here is exactly same to the standard PCA.

*Proof.* To prove that the Batch PCA algorithm achieves the same result as the standard PCA, we need to show that the covariance matrix and the reduced data matrix computed by the Batch PCA are equivalent to those computed by the standard PCA.

First, let's show that the covariance matrix  $\Sigma$  computed in the Batch PCA is equivalent to the covariance matrix C in the standard PCA.

In the standard PCA, the covariance matrix is computed as:

$$
\mathbf{C} = \frac{1}{n-1} \mathbf{Y}_{\text{centered}}^T \mathbf{Y}_{\text{centered}}
$$

In the Batch PCA, the covariance matrix is computed as:

$$
\Sigma = \frac{1}{n-1} \sum_{i=0}^{n-1} \mathbf{B}_i^T \mathbf{B}_i
$$
  
= 
$$
\frac{1}{n-1} (\mathbf{B}_0^T \mathbf{B}_0 + \mathbf{B}_1^T \mathbf{B}_1 + \dots + \mathbf{B}_{b-1}^T \mathbf{B}_{b-1})
$$
  
= 
$$
\frac{1}{n-1} \mathbf{Y}_{\text{centered}}^T \mathbf{Y}_{\text{centered}}
$$

where  $\mathbf{B}_i$  is the *i*-th batch of the centered data matrix  $\mathbf{Y}_{\text{centerd}}$ , and *b* is the number of batches.

Therefore, the covariance matrix computed by the Batch PCA is equivalent to the covariance matrix computed by the standard PCA.

Next, let's show that the reduced data matrix  $\mathbf{Y}_{reduced}$  computed in the Batch PCA is equivalent to the one computed in the standard PCA.

In the standard PCA, the reduced data matrix is computed as:

$$
\mathbf{Y}_{reduced} = \mathbf{Y}_{centered}\mathbf{W}
$$

where  $W$  is the matrix of the top  $k$  eigenvectors of the covariance matrix.

In the Batch PCA, the reduced data matrix is computed as:

$$
\mathbf{Y}_{reduced} = \begin{bmatrix} \mathbf{B}_0 \mathbf{V} \\ \mathbf{B}_1 \mathbf{V} \\ \vdots \\ \mathbf{B}_{b-1} \mathbf{V} \end{bmatrix}
$$
$$
= \begin{bmatrix} \mathbf{Y}_{centered}[0:m,:] \\ \mathbf{Y}_{centered}[m:2m,:] \\ \vdots \\ \mathbf{Y}_{centered}[(b-1)m:n,:] \end{bmatrix} \mathbf{V}
$$
$$
= \mathbf{Y}_{centered} \mathbf{V}
$$

where V is the matrix of the top k eigenvectors of the covariance matrix  $\Sigma$ , and m is the batch size.

Since we have shown that the covariance matrices C and  $\Sigma$  are equivalent, their eigenvectors W and V are also equivalent. Therefore, the reduced data matrix computed by the Batch PCA is equivalent to the one computed by the standard PCA.

In conclusion, we have proven that the Batch PCA algorithm achieves the same result as the standard PCA by showing that the covariance matrix and the reduced data matrix computed by the Batch PCA are equivalent to those computed by the standard PCA.  $\Box$ 

<span id="page-34-0"></span>

### M Analyze the Practical Data Augmentations

Here we presents a rigorous mathematical proof demonstrating that higher data augmentation intensity leads to increased overlap between samples from different classes. By modeling the sample distributions and augmentation process, we show that the variance of the augmented distributions increases with augmentation strength, resulting in wider and more overlapping distributions. We provide an exact calculation of the intersection point and approximate the overlap area using the cumulative distribution function of the standard normal distribution. Our theoretical analysis confirms the positive correlation between data augmentation intensity and inter-class overlap rate.

<span id="page-34-1"></span>

#### M.1 Assumptions and Definitions

<span id="page-35-0"></span>Image /page/35/Figure/0 description: This is a diagram illustrating a machine learning process. On the left, there are two images: a dog in the grass and a cat in the grass. Each image has a zoomed-in view of a portion of the animal. These are labeled as 'Data-Augmented Views'. An arrow points from these views to a 'Machine Learner' icon. The machine learner is presented with the question, 'Similar Views. Different Targets. What should I learn?'. From the machine learner, there are two paths. Path 1, labeled 'Human-Labeled', shows an arrow pointing to a dog image and a bar chart. Path 2, labeled 'Self-Generated', shows an arrow pointing to a dog image and a bar chart. Below the machine learner, there is a human icon and another machine learner icon, connected by 'Or'. The human icon has an arrow pointing to a bar chart and then to the 'Human-Labeled' path. The machine learner icon has an arrow pointing to a bar chart and then to the 'Self-Generated' path. There are also arrows originating from the human and machine learner icons pointing to a cat image and a bar chart, labeled 'Targeting 'Cat'' and 'Using Other Views'.

Figure 6: A scenario of similar views targeting different targets. In some cases, we may randomly crop two similar views from the original images as augmentations and input them into the machine learning model. This can lead to confusion in the model due to the differing targets assigned by humans or generated autonomously.

**Assumption 1** . *There are two classes*  $C_1$  *and*  $C_2$ *, with samples drawn from probability distributions*  $P_1$  *and*  $P_2$ *, respectively. The data augmentation intensity is denoted by*  $\alpha$ *.* 

**Definition 10 (Data Distribution and Augmentation)** . *The samples are satisfied*  $X_1 \sim P_1$ ,  $X_2 \sim P_2$ . The augmented samples are represented as  $X'_1 = X_1 + \epsilon$  and  $X'_2 = X_2 + \epsilon$ , where  $\epsilon$  *denotes the augmentation perturbation following the distribution*  $Q_{\alpha}$ *. The variance of*  $Q_{\alpha}$  *is proportional to* α*.*

<span id="page-35-1"></span>

#### M.2 Augmented Distributions

Let  $P_{1,\alpha}$  and  $P_{2,\alpha}$  denote the augmented sample distributions:

$$
P_{1,\alpha}(x') = (P_1 * Q_{\alpha})(x')
$$
\n(23)

$$
P_{2,\alpha}(x') = (P_2 * Q_{\alpha})(x')
$$
 (24)

where  $*$  represents the convolution operation.

**Variance of Augmented Distributions** Let  $\sigma_{\alpha}^2$  be the variance of  $Q_{\alpha}$ , with  $\sigma_{\alpha}^2 = k\alpha$ , where k is a constant. The variances of the augmented distributions are:

$$
\sigma_{P_{1,\alpha}}^2 = \sigma_{P_1}^2 + \sigma_{\alpha}^2 = \sigma_{P_1}^2 + k\alpha
$$
\n(25)

$$
\sigma_{P_{2,\alpha}}^2 = \sigma_{P_2}^2 + \sigma_{\alpha}^2 = \sigma_{P_2}^2 + k\alpha \tag{26}
$$

**Overlap Rate Definition** Let R denote the overlap region. The overlap rate  $O(\alpha)$  is defined as:

$$
O(\alpha) = \int_{R} P_{1,\alpha}(x') dx' + \int_{R} P_{2,\alpha}(x') dx'
$$
 (27)

<span id="page-35-2"></span>

#### M.3 Increased Variance Leads to Increased Overlap

As  $\alpha$  increases,  $\sigma_{\alpha}^2$  increases, resulting in larger variances of  $P_{1,\alpha}$  and  $P_{2,\alpha}$ . This makes the distributions wider and more dispersed, increasing the overlap region.

**Specific Derivation for One-Dimensional Gaussian Distributions** Assume  $P_1$  and  $P_2$  are two Gaussian distributions:

$$
P_1(x) = \mathcal{N}(\mu_1, \sigma_{P_1}^2)
$$
\n<sup>(28)</sup>

$$
P_2(x) = \mathcal{N}(\mu_2, \sigma_{P_2}^2)
$$
\n<sup>(29)</sup>

The augmented distributions are:

$$
P_{1,\alpha}(x') = \mathcal{N}(\mu_1, \sigma_{P_1}^2 + k\alpha) \tag{30}
$$

$$
P_{2,\alpha}(x') = \mathcal{N}(\mu_2, \sigma_{P_2}^2 + k\alpha) \tag{31}
$$

Exact Calculation of Intersection Point Equating the two Gaussian probability density functions and solving for  $x$ :

$$
\frac{1}{\sqrt{2\pi(\sigma_{P_1}^2 + k\alpha)}} \exp\left(-\frac{(x-\mu_1)^2}{2(\sigma_{P_1}^2 + k\alpha)}\right) = \frac{1}{\sqrt{2\pi(\sigma_{P_2}^2 + k\alpha)}} \exp\left(-\frac{(x-\mu_2)^2}{2(\sigma_{P_2}^2 + k\alpha)}\right) \tag{32}
$$

Simplifying the equation yields an analytical solution for the intersection point. To simplify the analysis, we assume  $\sigma_{P_1}^2 = \sigma_{P_2}^2$ , in which case the intersection point is  $(\mu_1 + \mu_2)/2$ .

Area of Overlap Region Let  $\Delta \mu = |\mu_1 - \mu_2|$ . The overlap region can be represented using the cumulative distribution function (CDF) of the standard normal distribution, denoted as  $\Phi(z)$ :

$$
\Phi(z) = \frac{1}{2} \left[ 1 + \text{erf}\left(\frac{z}{\sqrt{2}}\right) \right]
$$
\n(33)

The variance of the augmented distributions is  $\sigma_{\alpha}^2 = \sigma_{P_1}^2 + k\alpha$ . Therefore, the approximate area of the overlap region is:

$$
O(\alpha) \approx 2\Phi \left(\frac{\Delta \mu}{\sqrt{2(\sigma_{P_1}^2 + k\alpha)}}\right) - 1\tag{34}
$$

<span id="page-36-0"></span>

#### M.4 Conclusion

**Theorem 4** . As the data augmentation intensity  $\alpha$  increases, the overlap rate  $O(\alpha)$  between *samples from different classes increases.*

*Proof.* Increasing  $\alpha$  leads to an increase in the variance of the sample distributions, making them wider and more likely to overlap.

Specifically, increasing  $\alpha$  increases  $\sigma_{P_1}^2 + k\alpha$  in the denominator, thereby decreasing the argument of  $\Phi(\cdot)$ . Since  $\Phi(z)$  increases as z decreases for  $z > 0$ ,  $O(\alpha)$  increases as  $\alpha$  increases.  $\Box$ 

In summary, we have rigorously proven the positive correlation between data augmentation intensity and inter-class overlap rate from a statistical and probabilistic perspective.

<span id="page-36-1"></span>

#### M.5 Empirical Analysis for Real-world Datasets

Beyond the theoretical analysis for a simple case, we also provide empirical analysis for four realworld datasets under different intensities of data augmentation. We utilize TSNE [\[61\]](#page-13-18) to visualize the (augmented) samples in Figure [7.](#page-37-0) All the results demonstrate that:

- 1. as minscale increases from 0.02 to 0.5, the data points gradually change from being tightly clustered to more dispersed. This indicates that higher data augmentation intensity expands the range of sample distribution;
- 2. when minscale=0.02, the data points of the two classes exhibit significant overlap, and the boundary becomes blurred. In contrast, larger minscale values such as 0.2 and 0.5 allow for better separation between classes;

<span id="page-37-0"></span>Image /page/37/Figure/0 description: This figure displays six scatter plots, arranged in a 2x3 grid, illustrating the effect of the 'Minscale' parameter on data visualization. Each plot shows two distinct clusters of points, one colored purple and the other yellow. The plots are labeled (a) through (f). Plots (a) through (e) show the results with 'Minscale' set to 0.02, 0.04, 0.08, 0.2, and 0.5, respectively. Plot (f) is labeled 'Without using Augmentation'. The x-axis ranges from approximately -60 to 60 or -80 to 60, and the y-axis ranges from approximately -40 to 40 or -20 to 20. The plots demonstrate how changing the 'Minscale' value influences the separation and density of the clusters.

Figure 7: Visualization of samples with varying levels of data augmentation intensity. We visualize the samples of 2 classes from CIFAR-10 in 2-dimensional space, respectively setting minscale in RandomResizeCrop as {0.02, 0.04, 0.08, 0.2, 0.5} and without data augmentation.

3. when data augmentation is not used (the last figure), there is a clear gap between the two classes, and the data points within each class are very compact. This suggests that the feature distribution of the original samples is relatively concentrated.
Conclusions and open problems

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.

In these notes I have tried to present a consistent picture of the theory of optimal transport, with a dynamical, probabilistic and geometric point of view, insisting on the notions of displacement interpolation, probabilistic representation, and curvature effects.

The qualitative description of optimal transport, developed in Part I, now seems to be more or less under control. Even the smoothness of the transport map in curved geometries starts to be better understood, thanks in particular to the recent works of <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON><PERSON> which were described in Chapter 12. Among issues which seem to be of interest I shall mention:

• find relevant examples of cost functions with nonnegative, or positive c-curvature (Definition 12.27), and theorems guaranteeing that the optimal transport does not approach singularities of the cost function — so that the smoothness of the transport map can be established;

• get a precise description of the singularities of the optimal transport map when the latter is not smooth;

• further analyze the displacement interpolation on singular spaces, maybe via nonsmooth generalizations of <PERSON><PERSON>'s estimates (as in Open Problem 8.21).

For the applications of optimal transport to Riemannian geometry, a consistent picture is also emerging, as I have tried to show in Part II. The main regularity problems seem to be under control here, but there remain several challenging "structural" problems:

• How can one best understand the relation between plain displacement convexity and distorted displacement convexity, as described in Chapter 17? Is there an Eulerian counterpart of the latter concept? See Open Problems 17.38 and 17.39 for more precise formulations.

• Optimal transport seems to work well to establish sharp geometric inequalities when the "natural dimension of the inequality" coincides with the dimension bound; on the other hand, so far it has failed to establish for instance sharp logarithmic Sobolev or Talagrand inequalities (infinite-dimensional) under a  $CD(K, N)$  condition for  $N < \infty$  (Open Problems 21.6 and 22.44). The sharp  $L^2$ -Sobolev inequality (21.9) has also escaped investigations based on optimal transport (Open Problems 21.11). Can one find a more precise strategy to attack such problems by a displacement convexity approach? A seemingly closely related question is whether one can mimick (maybe by changes of unknowns in the transport problem?) the changes of variables in the  $\Gamma_2$  formalism, which are often at the basis of the derivation of such sharp inequalities, as in the recent papers of Jérôme Demange. To add to the confusion, the mysterious structure condition (25.10) has popped out in these works; it is natural to ask whether this condition has any interpretation in terms of optimal transport.

• Are there interesting examples of displacement convex functionals apart from the ones that have already been explored during the past ten years — basically all of the form  $\int_M U(\rho) d\nu + \int_{M^k} V d\mu^{\otimes k}$ ? It is frustrating that so few examples of displacement convex functionals are known, in contrast with the enormous amount of plainly convex functionals that one can construct. Open Problem 15.11 might be related to this question.

• Is there a transport-based proof of the famous  $Lévy–Gromov$ isoperimetric inequalities (Open Problem 21.16), that would not involve so much "hard analysis" as the currently known arguments? Besides its intrinsic interest, such a proof could hopefully be adapted to nonsmooth spaces such as the weak  $CD(K, N)$  spaces studied in Part III.

• Caffarelli's log concave perturbation theorem (alluded to in Chapter 2) is another riddle in the picture. The Gaussian space can be seen as the infinite-dimensional version of the sphere, which is the Riemannian "reference space" with positive constant (sectional) curvature; and the space  $\mathbb{R}^n$  equipped with a log concave measure is a space of nonnegative Ricci curvature. So Caffarelli's theorem can be restated as follows: If the Euclidean space  $(\mathbb{R}^n, d_2)$  is equipped with a probability measure  $\nu$  that makes it a CD(K,  $\infty$ ) space, then  $\nu$  can be realized as a 1-Lipschitz push-forward of the reference Gaussian measure with curvature  $K$ . This implies almost obviously that isoperimetric inequalities in  $(\mathbb{R}^n, d_2, \nu)$  are not worse than isoperimetric inequalities in the Gaussian space; so there is a strong analogy between Caffarelli's theorem on the one hand, and the Lévy–Gromov isoperimetric inequality on the other hand. It is natural to ask whether there is a common framework for both results; this does not seem obvious at all, and I have not been able to formulate even a decent guess of what could be a geometric generalization of Caffarelli's theorem.

• Another important remark is that the geometric theory has been almost exclusively developed in the case of the optimal transport with quadratic cost function; the exponent  $p = 2$  here is natural in the context of Riemannian geometry, but working with other exponents (or with radically different Lagrangian cost functions) might lead to new geometric territories. An illustration is provided by the recent work of Shin-ichi Ohta in Finsler geometry. A related question is Open Problem 15.12.

In Part III of these notes, I discussed the emerging theory of weak Ricci curvature lower bounds in metric-measure spaces, based on displacement convexity inequalities. The theory has grown very fast and is starting to be rather well-developed; however, some challenging issues remain to be solved before one can consider it as mature. Here are three missing pieces of the puzzle:

• A globalization theorem that would play the role of the Toponogov–Perelman theorem for Alexandrov spaces with a lower bound on the curvature. This result should state that a weak local  $CD(K, N)$  space is automatically a weak  $CD(K, N)$  space. Theorem 30.37 shows that this is true at least if  $K = 0, N < \infty$  and X is nonbranching; if Conjecture 30.34 turns out to be true, the same result will be available for all values of K.

• The compatibility with the theory of Alexandrov spaces (with lower curvature bounds). Alexandrov spaces have proven their flexibility and have gained a lot of popularity among geometers. Since Alexandrov bounds are weak sectional curvature bounds, they should in principle be able to control weak Ricci curvature bounds. The natural question here can be stated as follows: Let  $(\mathcal{X}, d)$  be a finite-dimensional Alexandrov space with dimension n and curvature bounded below by  $\kappa$ , and let  $\mathcal{H}^n$  be the *n*-dimensional Hausdorff measure on  $\mathcal{X}$ ; is  $(\mathcal{X}, d, \mathcal{H}^n)$ a weak  $CD((n-1)\kappa,n)$  space?

• A thorough discussion of the branching problem: Find examples of weak  $CD(K, N)$  spaces that are branching; that are singular but nonbranching; identify simple regularity conditions that prevent branching; etc. It is also of interest to enquire whether the nonbranching assumption can be dispensed with in Theorems 30.26 and 30.37 (recall Remarks 30.27 and 30.39).

More generally, we would like to have more information about the structure of weak  $CD(K, N)$  spaces, at least when N is finite. It is known from the work of Jeff Cheeger and others that metric-measures spaces in which the measure is (locally) doubling and satisfies a (local) Poincaré inequality have at least some little bit of regularity: There is a tangent space defined almost everywhere, varying in a measurable way.

## 926 Conclusions and open problems

In the context of Alexandrov spaces with curvature bounded below, some rather strong structure theorems have been established by Grigori Perelman and others; it is natural to ask whether similar results hold true for weak  $CD(K, N)$  spaces.

Another relevant problem is to check the compatibility of the  $CD(K, N)$  condition with the operations of quotient by Lie group actions, and lifting to the universal covering. As explained in the bibliographical notes of Chapter 30, only partial results are known in these directions.

Besides these issues, it seems important to find further examples of weak  $CD(K, N)$  spaces, apart from the ones presented in Chapter 29, mostly constructed as limits or quotients of manifolds. It was realized in a recent Oberwolfach meeting, as a consequence of discussions between Dario Cordero-Erausquin, Karl-Theodor Sturm and myself, that the Euclidean space  $\mathbb{R}^n$ , equipped with any norm  $\|\cdot\|$ , is a weak  $CD(0, n)$ space:

**Theorem.** Let  $\|\cdot\|$  be a norm on  $\mathbb{R}^n$  (considered as a distance on  $\mathbb{R}^n \times \mathbb{R}^n$ ), and let  $\lambda_n$  be the n-dimensional Lebesgue measure. Then the metric-measure space  $(\mathbb{R}^n, \|\cdot\|, \lambda_n)$  is a weak  $CD(0, n)$  space in the sense of Definition 29.8.

I did not include this theorem in the body of these notes, because it appeals to some results that have not yet been adapted to a genuinely geometric context, and which I preferred not to discuss. I shall sketch the proof at the end of this text, but first I would like to explain why this result is at the same time motivating, and a bit shocking:

(a) As pointed out to me by John Lott, if  $\|\cdot\|$  is not Euclidean, then the metric-measure space  $(\mathbb{R}^n, \|\cdot\|, \lambda_n)$  cannot be realized as a limit of smooth Riemannian manifolds with a uniform  $CD(0, N)$  bound, because it fails to satisfy the splitting principle. (If a nonnegatively curved space admits a line, i.e. a geodesic parametrized by R, then the space can be "factorized" by this geodesic.) Results by Jeff Cheeger, Toby Colding and Detlef Gromoll say that the splitting principle holds for  $CD(0, N)$  manifolds and their measured Gromov–Hausdorff limits.

(b) If  $\|\cdot\|$  is not the Euclidean norm, the resulting metric space is very singular in certain respects: It is in general not an Alexandrov space, and it can be extremely branching. For instance, if  $\|\cdot\|$  is the  $\ell_{\infty}$  norm, then any two distinct points are joined by an uncountable

infinity of geodesics. Since  $(\mathbb{R}^n, \|\cdot\|_{\ell_\infty}, \lambda_n)$  is the (pointed) limit of the nonbranching spaces  $(\mathbb{R}^n, \|\cdot\|_{\ell_p}, \lambda_n)$  as  $p \to \infty$ , we also realize that weak  $CD(K, N)$  bounds do not prevent the appearance of branching in measured Gromov-Hausdorff limits, at least if  $K \leq 0$ .

On the other hand, the study of optimal Sobolev inequalities in  $\mathbb{R}^n$ which I performed together with Bruno Nazaret and Dario Cordero-Erausquin shows that optimal Sobolev inequalities basically do not depend on the choice of the norm on  $\mathbb{R}^n$ . In a Riemannian context, Sobolev inequalities strongly depend on Ricci curvature bounds; so, our result suggests that it is not absurd to decide that  $\mathbb{R}^n$  is a weak  $CD(0, n)$ space independently of the norm. Shin-ichi Ohta has developed this point of view by studying curvature-dimension conditions in certain classes of Finsler spaces.

One can also ask whether there are additional regularity conditions that might be added to the definition of weak  $CD(K, N)$  space, in order to enforce nonbranching, or the splitting principle, or both, and in particular rule out non-Euclidean norms.

As a side consequence of point (a) above, we realize that smooth  $CD(K, N)$  manifolds are not dense in the spaces  $\mathcal{CDD}(K, N, D, m, M)$ introduced in Theorem 29.32.

The interpretation of dissipative equations as gradient flows with respect to optimal transport, and the theory reviewed in Chapters 23 to 25, also lead to fascinating issues that are relevant in smooth or nonsmooth geometry as well as in partial differential equations. For instance,

(a) Can one define a reasonably well-behaved heat flow on weak  $CD(K, N)$  spaces by taking the gradient flow for Boltzmann's H functional? The theory of gradient flows in abstract metric spaces has been pushed very far, in particular by Luigi Ambrosio, Giuseppe Savaré and collaborators; so it might not be so difficult to define an object that would play the role of a heat semigroup. But this will be of limited value unless one can prove relevant theorems about this object.

Shin-ichi Ohta, and independently Giuseppe Savaré, recently made progress in this direction by constructing gradient flows in the Wasserstein space over a finite-dimensional Alexandrov space of curvature bounded below, or over more general spaces satisfying very weak regularity assumptions expressed in terms of distances and angles. In the particular case when the energy functional is the Boltzmann entropy, this provides a natural notion of heat equation and heat semigroup. Savaré uses a very elegant argument, based on properties of Wasserstein distances and entropy, to prove the linearity of this semigroup, and other properties as well (positivity, contraction in  $W_p$  for  $1 \leq p \leq 2$ , contraction in  $L^p$ , some regularizing effect).

This problem might be related to the possibility of defining a Laplace operator on a singular space, an issue which has been addressed in particular by Jeff Cheeger and Toby Colding, for limits of Riemannian manifolds. However, their construction is strongly based on regularity properties enjoyed by such limits, and breaks down, e.g., for  $\mathbb{R}^n$ equipped with a non-Euclidean norm  $\|\cdot\|$ . In fact, as noted by Karl-Theodor Sturm, the gradient flow of the H functional in  $P_2((\mathbb{R}^N, \|\cdot\|))$ yields a nonlinear evolution. The fact that this equation has the same fundamental solution (in the sense of a solution evolving from a Dirac mass) as the Euclidean one is one argument to believe that this is a natural notion of heat equation on the non-Euclidean  $\mathbb{R}^N$  — and may reinforce us in the conviction that this space deserves its status of weak  $CD(0,N)$  space.

(b) Can one extend the theory of dissipative equations to other equations, which are of Hamiltonian, or, even more interestingly, of dissipative Hamiltonian nature? As explained in the bibliographical notes of Chapter 23, there has been some recent work in that direction by Luigi Ambrosio, Wilfrid Gangbo and others, however the situation is still far from clear.

A loosely related issue is the study of the semi-geostrophic system, which in the simplest situations can formally be written as a Hamiltonian flow, where the Hamiltonian function is the square Wasserstein distance with respect to some uniform reference measure. I think that the rigorous qualitative understanding of the semi-geostrophic system is one of the most exciting problems that I am aware of in theoretical fluid mechanics; and discussions with Mike Cullen convinced me that it is very relevant in applications to meteorology. Although the theory of the semi-geostrophic system is still full of fundamental open problems, enough has already been written on it to make the substance of a complete monograph.

On a much more theoretical level, the geometric understanding of the Wasserstein space  $P_2(\mathcal{X})$ , where X is a Riemannian manifold or just a geodesic space, has been the object of several recent studies, and still retains many mysteries. For instance, there is a neat statement according to which  $P_2(\mathcal{X})$  is nonnegatively curved, in the sense of Alexandrov, if and only if  $X$  itself is nonnegatively curved. But there is no similar statement for nonzero lower bounds on the curvature! In fact, if  $x$  is a point of negative curvature, then the curvature of  $P_2(\mathcal{X})$  seems to be unbounded in both directions ( $+\infty$  and  $-\infty$ ) in the neighborhood of  $\delta_x$ . Also it is not clear what exactly is "the right" structure on, say,  $P_2(\mathbb{R}^n)$ ; recent works on the subject have suggested differing answers. Another relevant open problem is whether there is a natural "volume" measure on  $P_2(M)$ . Karl-Theodor Sturm and Max-Kostja von Renesse have recently managed to construct a natural one-parameter family of "Gibbs" probability measures on  $P_2(S^1)$ . A multi-dimensional generalization would be of great interest.

In their book on gradient flows, Luigi Ambrosio, Nicola Gigli and Giuseppe Savaré make an intriguing observation: One may define "generalized geodesics" in  $P_2(\mathbb{R}^n)$  by considering the law of  $(1-t) X_0 + t X_1$ , where  $(X_0, Z)$  and  $(X_1, Z)$  are optimal couplings. These generalized geodesics have intriguing properties: For instance, they still satisfy the characteristic displacement interpolation inequalities; and they provide curves of "nonpositive curvature", that can be exploited for various purposes, such as error estimates for approximate gradient flow schemes. It is natural to further investigate the properties of these objects, which are reminiscent of the c-segments considered in Chapter 12.

The list above provides but a sample among the many problems that remain open in the theory of optimal transport.

As I already mentioned in the preface, another crucial issue which I did not address at all is the numerical analysis of optimal transport. This topic also has a long and complex history, with some famous schemes such as the old simplex algorithm, described for instance in Alexander Schrijver's monograph Combinatorial Optimization: Polyhedra and Efficiency; or the more recent auction algorithm developed by Dimitri Bertsekas. Numerical schemes based on Monge–Ampère equations have been suggested, but hardly implemented yet. Recent works by Uriel Frisch and collaborators in cosmology provide an example where one would like to efficiently solve the optimal transport problem with *huge* sets of data. To add to the variety of methods, continuous schemes based on partial differential equations have been making their way lately. All in all, this subject certainly deserves a systematic study on its own, with experiments, comparisons of algorithms, benchmark problems and so forth. By the way, the optimum matching problem is one of the topics that Donald Knuth has planned to address in his long-awaited Volume 4 of The Art of Computer Programming.

Needless to say, the theory might also decide to explore new horizons which I am unable to foresee.

Sketch of proof of the Theorem. First consider the case when  $N = || \cdot ||$ is a uniformly convex, smooth norm, in the sense that

$$
\lambda I_n \le \nabla^2 N^2 \le \Lambda I_n
$$

for some positive constants  $\lambda$  and  $\Lambda$ . Then the cost function  $c(x,y)$  =  $N(x-y)^2$  is both strictly convex and  $C^{1,1}$ , i.e. uniformly semiconcave. This makes it possible to apply Theorem 10.28 (recall Example 10.35) and deduce the following theorem about the structure of optimal maps: If  $\mu_0$  and  $\mu_1$  are compactly supported and absolutely continuous, then there is a unique optimal transport, and it takes the form

$$
T(x) = x - \nabla (N^2)^*(-\nabla \psi(x)), \qquad \psi \text{ a } c\text{-convex function.}
$$

Since the norm is uniformly convex, geodesic lines are just straight lines; so the displacement interpolation takes the form  $(T_t)_{\#}(\rho_0 \lambda_n)$ , where

$$
T_t(x) = x - t \nabla (N^2)^*(-\nabla \psi(x)) \qquad 0 \le t \le 1.
$$

Let  $\theta(x) = \nabla (N^2)^*(-\nabla \psi(x))$ . By [814, Remark 2.56], the Jacobian matrix  $\nabla \theta$ , although not symmetric, is pointwise diagonalizable, with eigenvalues bounded above by 1 (this remark goes back at least to a 1996 preprint by Otto [666, Proposition A.4]; a more general statement is in [30, Theorem 6.2.7]). It follows easily that  $t \to \det(I_n - t\nabla\theta)^{1/n}$ is a concave function of  $t$  [814, Lemma 5.21], and one can reproduce the proof of displacement convexity for  $U_{\lambda_n}$ , as soon as  $U \in \mathcal{DC}_n$  [814, Theorem  $5.15$  (i).

This shows that  $(\mathbb{R}^n, N, \lambda_n)$  satisfies the CD $(0, n)$  displacement convexity inequalities when  $N$  is a smooth uniformly convex norm. Now if N is arbitrary, it can be approximated by a sequence  $(N_k)_{k\in\mathbb{N}}$  of smooth uniformly convex norms, in such a way that  $(\mathbb{R}^n, N, \lambda_n, 0)$  is the pointed measured Gromov–Hausdorff limit of  $(\mathbb{R}^n, N_k, \lambda_n, 0)$  as  $k \to \infty$ . Then the general conclusion follows by stability of the weak  $CD(0, n)$  criterion (Theorem 29.24). □

**Remark.** In the above argument the spaces  $(\mathbb{R}^n, N_k, \lambda_n)$  satisfy the property that the displacement interpolation between any two absolutely continuous, compactly supported probability measures is unique, while the limit space  $(\mathbb{R}^n, N, \lambda_n)$  does not necessarily satisfy this property. For instance, if  $N = || \cdot ||_{\ell_{\infty}}$ , there is an enormous number of displacement interpolations between two given probability measures; and most of them do not satisfy the displacement convexity inequalities that are used to define  $CD(0, n)$  bounds. This shows that if in Definition 29.8 one requires the inequality (29.11) to hold true for any Wasserstein geodesic, rather than for some Wasserstein geodesic, then the resulting  $CD(K, N)$  property is not stable under measured Gromov–Hausdorff convergence.
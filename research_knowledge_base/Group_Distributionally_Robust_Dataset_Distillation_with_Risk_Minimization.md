# GROUP DISTRIBUTIONALLY ROBUST DATASET DISTIL-LATION WITH RISK MINIMIZATION

Saeed Vahidian<sup>1</sup>∗ <PERSON><PERSON><sup>2</sup>∗ <PERSON><PERSON><PERSON><sup>3</sup>∗ <PERSON><PERSON><PERSON><PERSON><sup>4</sup> <PERSON><sup>2</sup> <PERSON><PERSON><sup>1</sup>

<sup>1</sup> Duke University <sup>2</sup> Zhejiang University <sup>3</sup> The Ohio State University

<sup>4</sup> Czech Technical University

# ABSTRACT

Dataset distillation (DD) has emerged as a widely adopted technique for crafting a synthetic dataset that captures the essential information of a training dataset, facilitating the training of accurate neural models. Its applications span various domains, including transfer learning, federated learning, and neural architecture search. The most popular methods for constructing the synthetic data rely on matching the convergence properties of training the model with the synthetic dataset and the training dataset. However, using the empirical loss as the criterion must be thought of as auxiliary in the same sense that the training set is an approximate substitute for the population distribution, and the latter is the data of interest. Yet despite its popularity, an aspect that remains unexplored is the relationship of DD to its generalization, particularly across uncommon subgroups. That is, how can we ensure that a model trained on the synthetic dataset performs well when faced with samples from regions with low population density? Here, the representativeness and coverage of the dataset become salient over the guaranteed training error at inference. Drawing inspiration from distributionally robust optimization, we introduce an algorithm that combines clustering with the minimization of a risk measure on the loss to conduct DD. We provide a theoretical rationale for our approach and demonstrate its effective generalization and robustness across subgroups through numerical experiments.

# 1 INTRODUCTION

Dataset distillation (DD) is a burgeoning area of interest, involving the creation of a synthetic dataset significantly smaller than the real training set yet demonstrating comparable performance on a model [\(Wang et al., 2018;](#page-12-0) [Sachdeva & McAuley, 2023\)](#page-12-1). This practice has gained prominence in various computation-sensitive applications, providing a valuable means to efficiently construct accurate models [\(Gu et al., 2023b;](#page-10-0) [Medvedev & D'yakonov, 2021;](#page-11-0) [Xiong et al., 2023\)](#page-12-2). The standard optimization objectives that are used to steer the construction of the synthetic data typically aim to foster either distributional similarity to the training set [\(Zhao & Bilen, 2023a;](#page-13-0) [Zhao et al., 2023\)](#page-13-1) or similar stochastic gradient descent (SGD) training dynamics as the original dataset [\(Zhao et al., 2021;](#page-13-2) [Cazenavette et al., 2022\)](#page-10-1). Notably, recent literature suggests that the latter category has proven more successful [\(Kim et al., 2022;](#page-11-1) [Cazenavette et al., 2023\)](#page-10-2). Intuitively, this success can be attributed to the rationale that, with considerably fewer samples, prioritizing the most relevant information for training and model building becomes more judicious.

In this paper, we aim to address two important practical concerns in DD training. First, it is essential to note that the synthetic dataset might be applied across a wide range of potential circumstances with distinctions from the training phase. Consequently, models trained on the synthetic dataset must exhibit low out-of-distribution error and strong generalization performance. This means the synthetic dataset should be designed to have certain higher-order probabilistic properties, particularly in relation to the model and loss functions. In addition, shifts in circumstances over time (some latent exogenous variables, formally) mean that the population distribution on which the model is trained

<sup>∗</sup>Equal contribution.

<span id="page-1-0"></span>Image /page/1/Figure/1 description: The image displays a diagram illustrating a function f(x, y; heta) where x and y are sampled from X|c2. The diagram shows a large, irregularly shaped region labeled 'X'. Inside this region, there are several sub-regions or partitions labeled with conditional notation: X|c1, X|c2, X|c3, X|c4, X|c5, and X|c6. These sub-regions are demarcated by curved lines, some of which are solid and some are dashed or dotted in various colors (yellow, blue, red, cyan). An arrow originates from a point within the region X and curves upwards and to the right, pointing towards the text "f(x, y; heta), x, y ~ X|c2", indicating a transformation or relationship between the elements.

Figure 1: A visual representation of the robust inference task involves the partial partitioning of the population distribution, that is  $X$  across subgroups  $\{c_i\}$ . A classifier is considered robust when it demonstrates high performance across the subgroups. As a practical hypothetical example of online learning, at a particular time, a steady stream of samples from  $\mathcal{X}|c_3$ may appear to the classifier. Note that in this case the region of sample space defined by this subgroup is geometrically small, and we can consider that it has a low overall prior density. If this subgroup's behavior is particularly anomalous, a model and any associated distilled dataset trained only on minimizing empirical risk may perform poorly on this subgroup.

may resemble a clustered sample, *i.e.*, a subset of the population. Therefore, it is imperative that the synthetic dataset ensures good coverage across the support of the sample space.

To this end, we propose to enhance the generalization performance and group coverage properties of the distilled dataset using concepts and methods in the field of Distributionally Robust Optimization (DRO) [\(Lin et al., 2022;](#page-11-2) [Zeng & Lam, 2022;](#page-13-3) [Vilouras et al., 2023\)](#page-12-3). Within DRO, one solves a bilevel optimization problem where the objective is to minimize the loss over a data distribution, subject to the constraint that this distribution is a specific probabilistic distance away from the population distribution:

$$
\min_{\theta \in \mathbb{R}^d} \max_{\mathcal{Q} \in \mathbb{Q}} F(\theta, \mathcal{Q}), \mathbb{Q} = \{ \mathcal{Q} : \mathcal{D}(\mathcal{Q} || \mathcal{P}) \le \Delta \}
$$
\n(1)

where F is the loss function,  $\theta$  is the parameter,  $\mathbb Q$  is the distributional uncertainty set,  $\mathcal D$  is an f-divergence and  $P$  is the population distribution.

We now present the two desiderata of DD to which we aim to apply the concepts of DRO. First, our goal is to construct a synthetic dataset that is a quality representative of the underlying population distribution. Thus, the consideration of generalization is paramount. [Xu & Mannor](#page-13-4) [\(2012\)](#page-13-4) present arguments indicating the theoretical equivalence of testing to training error as robustness to perturbations of the data distribution, and the generalization accuracy. Second, a conventional maximum likelihood classifier inherently assigns higher weights to sample ranges with larger prior distributions than to those with lower overall probability density. This inherent bias poses a fundamental risk of underperformance in small population subgroups. Furthermore, in online inference, it is common to observe alterations in the overall prior distribution, particularly in terms of subgroups. DRO has been conjectured and observed to mitigate this issue, both generically [\(Duchi & Namkoong, 2021\)](#page-10-3) and with explicit quantification of subgroups in the distributional uncertainty set [\(Oren et al., 2019\)](#page-11-3), where subgroups of topics are considered for training language models.

Moreover, this permits flexibility as far as easily modularized methods to specific concerns. Indeed, domain expertise in regards to the properties of the population distribution has been shown to assist in generalization– [\(Hu et al., 2018\)](#page-10-4) indicates that a more precisely defined distributional uncertainty set, focusing on specific collections of probability distributions from subsamples of the population rather than encompassing all possibilities in a ball, yields improved generalization performance.

In a formal sense, we define that each subsample involved in an inference task is drawn from a union of closed, convex, connected subsets of the population distribution. An intuitive representation of the envisioned scenario is illustrated in Figure [1.](#page-1-0)

Inspired by these concepts, this paper introduces a Double-DRO-based DD procedure designed specifically to address these two concerns. Fundamentally we employ DRO at two levels to promote different advantages that we shall witness in the experimental results. 1) We leverage the loss across clusters of the latent variables to ensure group robustness. 2) We approximately solve a DRO problem internally in order to promote better within-group generalization, using a risk measure as a proxy for a DRO solution.

Our paper continues below as follows. Next, we present our algorithm, we analyze the optimization problem defining the notion of generalization of interest, and argue how our method effectively targets this criterion in Section [2.](#page-2-0) We give the mathematical analysis in Section [3.](#page-3-0) In Section [4](#page-6-0) we present numerical results validating the superior overall and subgroup generalization performance on standard machine learning benchmarks. Finally, we conclude this work in Section [5.](#page-9-0) Related works are reviewed in the Appendix in Section [E.](#page-19-0)

<span id="page-2-0"></span>

# 2 ALGORITHM

In this Section we describe our main procedure, in which we incorporate two techniques informed by DRO in order to improve both generic generalization performance and group distributional coverage.

Let  $S = \{x_S, y_S\}$  be the synthetic distilled dataset, where  $([x_S]_i, [y_S]_i)$  with  $i = 1, ..., |\mathcal{S}|$  is an individual input and label pair. Let  $\mathcal{T} = \{x_T, y_T\}$  be the real training set where  $([x_T]_i, [y_T]_j)$ ,  $j = 1, ..., |\mathcal{T}|$  is an individual input and label pair with  $|\mathcal{T}| \gg |\mathcal{S}|$ . The target of dataset distillation is to optimize the synthetic dataset S so that a network with parameter  $\theta$  can achieve similar performance compared with that attained on T. For simplicity, we define the input and label pair as  $z = (x, y)$ . And  $F(\theta; z)$  is the full loss function to optimize parameter  $\theta$  on data z, which is typically cross entropy loss in classification tasks.

Matching-based dataset distillation methods usually involve imitating certain training characteristics of the real training set, such as training gradients, feature distribution, training trajectory, *etc* [\(Zhao](#page-13-2) [et al., 2021;](#page-13-2) [Zhao & Bilen, 2023a;](#page-13-0) [Cazenavette et al., 2022\)](#page-10-1). Take gradient matching as an example. At each iteration, the training gradient is extracted for the synthetic data and the real data, based on the same network. The gradient difference is set as the objective of optimizing synthetic data. Considering that diverse supervision gradients from different training stages should be provided to enhance the consistency throughout a model training process, the network is simultaneously updated with the synthetic data optimization. Previous methods utilize the same loss function  $F(\theta; z)$  to update the network. However, in this work, we propose to incorporate DRO at this stage for more robust network optimization as well as distillation supervision.

Specifically, we wish to solve a DRO locally in order to promote solutions that are particularly suitable for generalization. However, as the dimensionality and desired training size in our settings of interest present intractability for DRO, instead of solving the full DRO we use a proxy in the form of a Conditional Value at Risk (CVaR), which we define below. In the theoretical analysis, we describe the known correspondence between CVaR and DRO.

In our algorithm, each iteration begins with subsampling the training dataset  $\mathcal{T} \subset \mathcal{T}$  and then clustering them based on their nearest synthetic data point. That is for all  $i \in [S]$  the set  $c_i$ contains the elements of  $\bar{\mathcal{T}}$  that are closer to  $z_i$  than to any other point in S. The following criterion optimization problem is solved for  $\theta$ , obtaining the set of parameters that solves for minimax loss across the subgroups defined by the clusters  $\{c_i\}$ 

<span id="page-2-1"></span>
$$
\min_{\theta} \left[ \frac{1}{|\mathcal{S}|} \sum_{i} \text{CVaR}[F(\theta; \mathbf{c}_i)] + \max_{i} \text{CVaR}[F(\theta; \mathbf{c}_i)] \right].
$$
 (2)

By considering the clusters  $c_i$  separately, especially including the  $\max_i$  term in the objective, we

robustify the performance with respect to the different clusters. The synthetic dataset is meant to represent the training set, and with clustering, we ensure the entire support of the training distribution is represented by some sample  $z_i \in S$ . This yields our effort to ensure the group distributional robustness. We intend that both the worst case and the average performance across the sub-groups are minimized.

In addition, to promote generalization broadly speaking, note that rather than the expectation, we use a risk measure, as far as statistically agglomerating the loss over the training data. We set  $\alpha$  to be some tail probability. Risk measures enable one to minimize one-sided tail behavior. The operator denoting the Conditional Value at Risk, CVaR, is defined, with respect to the empirical distribution of data points within  $c_i$ :

<span id="page-2-2"></span>
$$
\text{CVaR}[F(\theta; \mathbf{c}_i)] := -\frac{1}{\alpha} \left\{ \frac{1}{|\mathbf{c}_i|} \sum_{z_t \in \mathbf{c}_i} F(\theta; z_t) \mathbf{1}[F(\theta; z_t) \le f_\alpha] + f_\alpha (\alpha - \frac{1}{|\mathbf{c}_i|} \sum_{z_t \in \mathbf{c}_i} \mathbf{1}[F(\theta; z_t) \le f_\alpha]) \right\}
$$
(3)

<span id="page-3-1"></span>

## Algorithm 1 Robust Dataset Distillation

**Input:** Real training set  $\mathcal{T}$ , synthetic set  $\mathcal{S}$ , network with parameter  $\theta$ , distilling objective  $L(\mathcal{S})$ . Execute: While not converged: Subsample the training set  $\overline{\mathcal{T}} \subset \mathcal{T}$ . Cluster  $\widetilde{\mathcal{T}}$  by the distillation set, i.e. define, for all  $t \in ||\overline{\mathcal{T}}||$ :  $\mathcal{C}(z_t) = \arg \min_i ||z_t - [\mathcal{S}]_i||^2$  and  $\mathbf{c}_i = \{z_t \in \overline{\mathcal{T}} : \mathcal{C}(z_t) = i\}.$ Solve the optimization problem in equation [2](#page-2-1) to obtain  $\theta^*$ . Optimize synthetic set  $\hat{S}$  with  $L(S)$  based on the optimized parameter  $\theta^*$ .

with the quantity  $f_\alpha$  defined as, with  $|c_i|$  denoting the size of the training set in cluster  $c_i$ ,

$$
f_{\alpha} := \min \left\{ f \in \mathbb{R} : \frac{1}{|\mathbf{c}_i|} \sum_{z_t \in \mathbf{c}_i} \mathbf{1}[F(\theta; z_t) \le f_{\alpha}] \ge \alpha \right\}
$$
(4)

The choice of a CVaR weighing of the loss informed by DRO-associated theoretical work aims to improve test error accuracy rather than merely minimizing training error. Additionally, by clustering and weighting the balanced loss, Group DRO is applied across the connected components of the sample space distribution, which defines the population.

After updating the network with Group DRO, the synthetic dataset  $S$  will be optimized based on matching metrics, denoted by  $L(S)$ , where existing matching-based DD methods can be broadly plugged in. This makes the algorithm modular to any choice of DD procedure. The algorithm is shown in Alg. [1.](#page-3-1) As  $L(S)$  involves a risk function of the loss  $F(\theta; z)$ , averaged across a set of partitions of  $S$ , the supervision helps enhance the distributional robustness of the distilling process. Thereby, the distilled dataset can obtain superior generalization performance across different domain shifts, as well as better group coverage for more practical usefulness. We proceed to iterate between an update to the parameter based on solving equation [2](#page-2-1) and an update to the synthetic dataset  $S$ with our tailored DD procedure until the procedure reaches a fixed point, wherein the two do not significantly change.

<span id="page-3-0"></span>

## 3 ANALYSIS

In this Section we describe the formal optimization problem being solved as well as its convergence and statistical properties. The focus will be on justifying why we expect the procedures defined in the Algorithm to improve (group) generalization, through foundational results in DRO theory.

Let  $Q$  be a *partition* of the population distribution  $D$ . As such, we can describe the problem of group coverage at the point of inference as yielding comparable performance across elements of Q. To this end, we denote the support of the population distribution as  $D = \text{supp}(\mathcal{D})$  and we assume that it is compact. Let us define,

$$
\mathbb{Q} = \{ \mathcal{Q}_i \}, \text{ with } Q_i = \text{supp}(\mathcal{Q}_i), \bigcup_{i=1}^q Q_i = D, \underline{s} \leq \lambda(Q_i) \leq \overline{s}
$$
 (5)

for some  $\bar{s} > \bar{s} > 0$  and every  $Q_i$  is itself a union of compact, convex, and connected sets. Here  $\lambda$  is integration with respect to the Lebesgue measure. Recall that we denote the synthetic dataset by  $S$ with  $|S| = S$ .

Note the assumption that  $\mathcal D$  is finite. In addition, we assume a certain regularity of the loss function. Formally, we state the following:

<span id="page-3-2"></span>**Assumption 3.1.** The population distribution D has bounded support, i.e.,  $\lambda(D) < \infty$ . The loss  $F$  is Lipschitz continuously differentiable with respect to the first argument (the parameters) and continuous with respect to the second argument (the input features and labels). Finally, the partitions are not probabilistically small, i.e., there exists  $p_{q_0}$  such that  $\mathbb{P}_{\mathcal{D}}[A \in \mathcal{Q}_i] \geq p_{q_0}$  for all i.

Additionally, let's explore the asymptotic learning regime when examining the problem, where the entire population set could be sampled in the limit.

<span id="page-4-2"></span>**Assumption 3.2.** For all  $\mathcal{Q} \in \mathbb{Q}$ , consider the asymptotic online learning limit,

$$
\limsup_{t \to \infty} (\text{supp}(\mathcal{Q}) \cap \overline{\mathcal{T}}_t) = \text{supp}(\mathcal{Q}) \tag{6}
$$

where  $\bar{\mathcal{T}}_t$  is the training set sampled at iteration t.

Informally, group coverage corresponds to enforcing adequate performance for prediction using the trained model regardless of what portion of the population set is taken. Essentially, at a particular future instant, the learner could be expected to classify or predict a quantity for some small subpopulation cohort that may appear as a subsample at the time of online inference.

Let us formally articulate the optimization problem of interest, as outlined in the Introduction, as follows

<span id="page-4-0"></span>
$$
\min_{\mathcal{S}} \max_{\mathcal{Q} \in \mathbb{Q}} \mathbb{E}[F(\theta^*, \mathcal{Q})] \quad \text{s.t. } \theta^* \in \arg\min_{\theta} F(\theta, \mathcal{S}). \tag{7}
$$

Let us briefly consider the standard circumstance by which Algorithm 1 converges. By considering each iteration as two players' best response to a cooperative Nash game, we can ensure convergence asymptotically of iteratively solving the two optimization problems, as given in [Nash](#page-11-4) [\(1953\)](#page-11-4).

**Theorem 3.3.** *Under the circumstance by which the Morse-Saard condition holds (Souček & Souček, 1972*) and so the optimal set  $\{S^*(\theta)\}, \{\theta^*(\mathcal{S})\}$  is compact (possibly finite) for all  $\theta$ , S, then Algo*rithm [2](#page-15-0) converges to a fixed point of equation [7.](#page-4-0)*

A remark on the time complexity of the Algorithm. Using CVaR instead of a sample average only takes a constant multiple of operations on the subsample. Clustering itself can be done polynomial in the number of samples. Since the properties of the model and loss function, that is, nonconvex and nonsmooth, are fundamentally unchanged, there is no change to the iteration complexity.

Now we will discuss generalization and DRO. Recall that there are two layers of generalization: on the one hand, we are solving a problem robust with respect to the choice of  $\mathcal{Q} \in \mathbb{Q}$ , and on the other hand, we are considering the population error rather than an empirical loss.

We posit that the computation of a gradient estimate employs a conventional Stochastic Approximation procedure to address *some* bilevel optimization problem for S. Thus we do not address the convergence guarantees (that is, asymptotic stationarity and convergence rate) of the training procedure itself but study the properties of the associated optimization problems and their solutions.

Accordingly, our emphasis is on scrutinizing the properties associated with the criterion,

$$
\min_{\theta} \max_{\mathcal{Q} \in \mathbb{Q}} \mathbb{E}_Q[F(\theta, \mathcal{Q})]. \tag{8}
$$

that is ultimately used to steer the synthetic dataset, as the DD task is finding a dataset on whose associated  $\theta$ -minimizer also minimizes this quantity. In the analysis, we shall argue about the validity of these criteria as far ensuring the generalization as well as group robustness properties of the solution of equation [7.](#page-4-0)

To begin with, we rewrite equation [7](#page-4-0) as:

<span id="page-4-1"></span>
$$
\min_{\mathcal{S}} \max_{\mathcal{Q} \in \mathcal{Q}} \max_{\mathcal{Q}' \in \bar{\mathcal{Q}}} \mathbb{E}[F(\theta^*, \mathcal{Q}')] \quad \text{s.t.} \quad \theta^* \in \arg \min_{\theta} F(\theta, \mathcal{S})
$$
\n
$$
\bar{\mathcal{Q}} = \{ \mathcal{Q}' : I(\mathcal{Q}', \mathcal{S} \cup \mathcal{Q}_N) \le r \}
$$
\n(9)

where  $r > 0$  is some bound and we replace the inner problem to be evaluated on the entire training dataset with a data-driven DRO. In the appendix we present several results in the literature that indicate how the DRO to an empirical risk minimization problem exhibits generalization guarantees and hence is a valid auxiliary criterion for minimizing equation [7.](#page-4-0)

### 3.1 LARGE DEVIATIONS AND SOLVING THE BILEVEL DRO

To justify the clustering and risk measure minimization algorithm as an appropriate procedure for solving equation [9,](#page-4-1) we apply some theoretical analysis on the relationship between DRO and Large Deviations Principles (LDP). LDPs (*e.g.*, [Deuschel & Stroock](#page-10-5) [\(2001\)](#page-10-5)) define an exponential asymptotic decay of the measure of the tails of an empirical distribution with respect to a sample size.

The particular, Large Deviations Principle (LDP) capturing out-of-sample disappointment, which is of interest to us, is defined as follows:

<span id="page-5-0"></span>
$$
\limsup_{N \to \infty} \frac{1}{N} \log \mathbb{P}_{\mathcal{Q}}^{\infty} \left( F(\theta^*(\mathcal{S}), \mathbb{P}_{\mathcal{Q}}) > F(\theta^*(\mathcal{S}), \mathcal{S} \cup \mathcal{Q}_N) \right) \leq -r, \, \forall \mathcal{Q} \in \mathbb{Q} \tag{10}
$$

for some  $r > 0$ . This states that for all partitions Q of the population space partition  $\mathbb{Q}$ , there is an exponential asymptotic decay of the probability of one-sided sample error (i.e., samples of size  $N$ from the population  $\mathbb{P}_{\mathcal{O}}$ ) relative to the computed loss on the synthetic and training data points  $\mathcal{Q}_N$ , as the number of training data points grows asymptotically.

Now, we present the following Proposition, whose proof is in the appendix,

<span id="page-5-1"></span>Proposition 3.4. *Algorithm equation [2](#page-15-0) satisfies, under Assumptions [3.1](#page-3-2) and [3.2,](#page-4-2) the LDP equation [10.](#page-5-0)*

Next, we relate Algorithm equation [2](#page-15-0) together with [3.1](#page-3-2) to the DRO problem equation [9.](#page-4-1)

### 3.2 LARGE DEVIATIONS AND DISTRIBUTIONALLY ROBUST OPTIMIZATION

Applying Assumption [3.2,](#page-4-2) the LDP, and the continuity of  $F$  with respect to the data, Assumption [3.1](#page-3-2) (see also the proof of Lemma 1 as well as Example 3 in [Duchi & Namkoong](#page-10-3)  $(2021)$ ) we can bound the quantity:

$$
\mathbb{P}(F(\theta^*, q) > F(\theta^*, \mathcal{S} \cup \mathcal{Q}_N)
$$
\n(11)

for  $q \in \mathcal{Q}'$ , with  $\mathcal{D}(\mathcal{Q}_N, \mathcal{Q}') \leq r$  with a sufficient step-size.

This implies that one can bound the objective value of the DRO problem of interest equation [9:](#page-4-1)

$$
\min_{\mathcal{S}} \max_{\mathcal{Q} \in \bar{\mathcal{Q}}} \max_{\mathcal{Q}' \in \bar{\mathcal{Q}}} \mathbb{E}[F(\theta^*, \mathcal{Q}')] \quad \text{s.t.} \quad \theta^* \in \arg \min_{\theta} F(\theta, \mathcal{S})
$$
\n
$$
\bar{\mathcal{Q}} = \{ \mathcal{Q}' : I(\mathcal{Q}', \mathcal{S} \cup \mathcal{Q}_N) \le r \}
$$
\n(12)

i.e., there is some small  $C > 0$  such that,

$$
|O(\theta^*, S^*) - \hat{O}(\hat{\theta}, \hat{S})| \le C \tag{13}
$$

where O and  $\ddot{O}$  refer to the optimal value of the data driven DRO equation [9](#page-4-1) and the approximation given by Algorithm [1](#page-3-1) and [2.](#page-15-0)

### 3.3 DISTRIBUTIONALLY ROBUST OPTIMIZATION AND SUBGROUP COVERAGE

Let us return to equation [7:](#page-4-0)

$$
\min_{\mathcal{S}} \max_{\mathcal{Q} \in \mathbb{Q}} \mathbb{E}[F(\theta^*, \mathcal{Q})] \quad \text{s.t. } \theta^* \in \arg\min_{\theta} F(\theta, \mathcal{S})
$$

We have established that our Algorithm approximately solves a DRO which approximately bounds the population loss. Now consider the outer DRO itself. The same theory regarding DRO and LDPs can now be applied, but now to yield a stronger result, since we are evaluating the full (test) loss. Indeed the data being sampled are simply  $\mathcal{Q}_{\omega} \in \mathbb{Q}$ , that is, some i.i.d. selection  $\omega$  over the finite set of partitions. Since the entire subpopulation is taken, each estimator is constructed with the full population error, and w.p. 1 the entire set  $\mathbb Q$  is sampled for finite N.

We can directly apply Theorem 7 in the work of [Van Parys et al.](#page-12-5) [\(2021\)](#page-12-5) to deduce that the result satisfies:

$$
\limsup_{N \to \infty} \frac{1}{N} \log \mathbb{P}_{\omega}^{\infty} \left( F(\theta^*, Q_{\omega}) > F(\theta^*, \mathcal{S}) \right) \le -r.
$$
\n(14)

Thus by the Kolmogorov 0-1 principle we have that,

$$
F(\theta^*, \mathcal{Q}) \le F(\theta^*, \mathcal{S}),
$$

for all  $Q$ . This guarantees the quality of S in ensuring group robust guarantees on the loss.

<span id="page-6-1"></span>

| Table 1: Top-1 test accuracy on robustness testing sets. "Gain" denotes the performance gain of                 |
|-----------------------------------------------------------------------------------------------------------------|
| applying our proposed method. Except for absolute values, the relative fluctuation compared with the            |
| standard case is also reported as subscripts. The experiments are conducted under the IPC setting of            |
| 10. $\mathcal{R}$ indicates the proposed robust dataset distillation method applied. The better results between |
| baseline and the proposed method are marked in <b>bold</b> .                                                    |

| Dataset     | Setting     | Random           | IDC.                              | $IDC^{\mathcal{R}}$                       | Gain                 | GLaD                              | $GLaD^{\mathcal{R}}$             | Gain                 |
|-------------|-------------|------------------|-----------------------------------|-------------------------------------------|----------------------|-----------------------------------|----------------------------------|----------------------|
| CIFAR-10    | Standard    | $37.2_{+0.8}$    | $67.5_{\pm 0.5}$                  | 68.6 $\pm$ 0.2                            | 1.1                  | $46.7_{\pm 0.6}$                  | $\mathbf{50.2}_{\pm0.5}$         | 3.5                  |
|             | Cluster-min | $31.4_{+0.9}$    | $63.3_{\pm 0.7}$ $\downarrow$ 4.2 | 65.0 + 0.6 + 3.6                          | $1.7_{\text{10.6}}$  | $40.2_{\pm 1.2}$ $\downarrow 6.5$ | 46.7 $\pm$ 0.9 $\pm$ 3.5         | 6.5 $\uparrow$ 3.0   |
|             | Noise       | $35.4 + 1.2$     | $57.2_{\pm 1.1 \downarrow 10.3}$  | $59.4_{\pm 1.0 \ \downarrow 9.2}$         | $2.2_{\Upsilon1.1}$  | 44.1 $\pm$ 0.6 $\downarrow$ 2.5   | 49.5 $\pm$ 0.5 $\pm$ 0.7         | 5.4 $\uparrow$ 1.8   |
|             | Blur        | $29.4_{+0.6}$    | $48.3 \pm 0.9 \downarrow 19.2$    | $\mathbf{50.5}_{\pm 0.7 \downarrow 18.1}$ | $2.2_{\uparrow 1.1}$ | $36.9 + 0.6$ 19.8                 | 39.0 $\pm$ 0.6 $\downarrow$ 11.2 | 2.1 $\downarrow$ 1.4 |
|             | Invert      | $9.5_{+1.0}$     | $25.6_{\pm 0.5}$ 41.9             | $26.5_{\pm 0.6 \downarrow 42.1}$          | $0.9_{\pm 0.2}$      | $10.6_{\pm 1.1 \downarrow 36.1}$  | $13.0_{\pm 1.2 \downarrow 37.2}$ | 2.4 $\downarrow$ 1.1 |
| ImageNet-10 | Standard    | $46.9_{\pm 0.7}$ | $72.8_{\pm0.6}$                   | $\textbf{74.6}_{\pm0.9}$                  | 1.8                  | $50.9_{\pm0.9}$                   | $55.2_{\pm 1.1}$                 | 4.3                  |
|             | Cluster-min | $31.2_{+1.0}$    | $61.4_{\pm 1.0 \downarrow 11.4}$  | $65.7_{\pm 0.5}$ $_{\pm 8.9}$             | $4.3_{\text{ }72.5}$ | $34.9_{\pm 0.9 \downarrow 16.0}$  | 47.1 $\pm$ 1.2 $\downarrow$ 8.1  | $12.2_{7.9}$         |
|             | Noise       | 42.6 $\pm$ 0.8   | $65.8_{\pm 0.8}$ $\downarrow$ 7.0 | $68.8_{\pm0.9}$ $\downarrow5.8$           | $3.0_{\uparrow 1.2}$ | $48.6_{\pm 0.7}$ $\downarrow$ 2.3 | 53.9 $\pm$ 0.8 $\pm$ 1.3         | 5.3 $\uparrow$ 1.0   |
|             | Blur        | $45.5 \pm 1.1$   | $71.9 \pm 0.9$ $\downarrow 0.9$   | $74.1_{\pm 1.1 \ \pm 0.5}$                | $2.2_{\text{0.4}}$   | 47.9 ± 0.9 ± 3.0                  | 54.6 $\pm$ 0.9 $\downarrow$ 1.6  | 6.7 $\uparrow$ 1.4   |
|             | Invert      | $21.0_{+0.6}$    | $27.8_{\pm 0.7}$ 45.0             | $30.3_{\pm 0.8 \downarrow 44.3}$          | $2.5_{\text{0.7}}$   | $17.0_{\pm 1.0 \downarrow 33.9}$  | $21.6_{\pm0.7\downarrow33.6}$    | 4.6 $_{10.3}$        |

<span id="page-6-2"></span>Table 2: Top-1 test accuracy on standard testing sets. † indicates the result is reported based on our runs.  $\mathcal{R}$  indicates the proposed robust dataset distillation method applied on the baseline. The better results between baseline and the proposed method are marked in bold.

| Dataset     | <b>IPC</b> | Random           | <b>DSA</b>       | DM               | <b>KIP</b>               | <b>IDC</b>                    | $IDC^{\mathcal{R}}$ | GLaD                          | $GLaD^R$         |
|-------------|------------|------------------|------------------|------------------|--------------------------|-------------------------------|---------------------|-------------------------------|------------------|
|             |            | 14.6             | 27.5             | 24.2             | 57.3                     | $68.5_{\pm 0.9}$              | $68.9_{+0.4}$       | $32.5 + 0.5$ <sup>T</sup>     | $35.7_{+0.3}$    |
| <b>SVHN</b> | 10         | 35.1             | 79.2             | 72.0             | 75.0                     | $87.5 + 0.3$                  | $88.1_{\pm 0.3}$    | $68.2_{\pm 0.4}$ <sup>T</sup> | $72.5_{+0.4}$    |
|             | 50         | 70.9             | 84.4             | 84.3             | 80.5                     | $90.1_{\pm 0.1}$              | $90.8_{\pm 0.4}$    | $71.8_{\pm 0.6}$ <sup>T</sup> | $76.6_{+0.3}$    |
|             |            | 14.4             | 28.7             | 26.0             | 49.9                     | $50.6_{\pm 0.4}$ <sup>T</sup> | $51.3_{\pm 0.3}$    | $28.0_{+0.8}$ <sup>T</sup>    | $29.2_{+0.8}$    |
| CIFAR-10    | 10         | 37.2             | 52.1             | 53.8             | 62.7                     | $67.5_{\pm 0.5}$              | $68.6_{+0.2}$       | $46.7_{\pm 0.6}$ <sup>T</sup> | $50.2_{+0.5}$    |
|             | 50         | 56.5             | 60.6             | 65.6             | 68.6                     | 74.5 $\pm$ 0.1                | $75.3_{+0.5}$       | $59.9_{+0.9}$ <sup>T</sup>    | $62.5_{\pm 0.7}$ |
| ImageNet-10 |            | $23.2^{\dagger}$ | $30.6^{\dagger}$ | $30.2^{\dagger}$ | ۰                        | $54.4 + 1.1$ <sup>T</sup>     | $58.2_{\pm 1.2}$    | $33.5 + 0.9$ <sup>T</sup>     | $36.4_{\pm 0.8}$ |
|             | 10         | 46.9             | 52.7             | 52.3             | $\overline{\phantom{a}}$ | $72.8_{\pm 0.6}$              | 74.6+0.9            | $50.9_{\pm 1.0}$ <sup>T</sup> | $55.2_{+1.1}$    |

<span id="page-6-0"></span>

# 4 NUMERICAL RESULTS

In this section we present numerical results to validate the efficacy of the proposed robust dataset distillation method. Implementation details are listed in Sec. [H.](#page-21-0)

Results on Robustness Settings We first show the notable advantage offered by our proposed method that is the robustness against various domain shifts. This property is assessed through multiple protocols. Firstly, as suggested before, we present validation results on different partitions of the testing set. A clustering process is conducted to divide the original testing set into multiple sub-sets. We test the performance on each of them and report the worst accuracy among the sub-sets to demonstrate the robustness of distilled data, denoted as "Cluster-min" in Tab. [1.](#page-6-1) In the sub-scripts, the performance drop compared with the standard case is reported. Several key observations emerge from the experiment results. (1) Compared with random images, the Cluster-min accuracy of baseline DD methods exhibits improvement alongside the standard performance. It suggests that by condensing the knowledge from original data into informative distilled samples, DD methods contribute to enhanced data robustness. (2) Compared with CIFAR-10, the performance gap between the standard case and the worst sub-cluster on ImageNet-10 is more pronounced. This discrepancy can be attributed to a higher incidence of ID-unrelated interruptions in ImageNet-10, resulting in larger domain-shifts between sub-clusters and the original distribution. This finding aligns with the observation in Tab. [2.](#page-6-2) (3) With our proposed robust method applied, not only is the cluster-min performance improved, but the performance drop from the standard case is also significantly mitigated compared with baselines. It suggests exceptional overall generalization and robustness conferred by our method.

Furthermore, we provide testing results in Tab. [1](#page-6-1) on truncated testing sets, simulating scenarios where the testing set exhibits more substantial domain shifts compared with the training data. We employ three data truncation means, including the addition of Gaussian noise, application of blur effects, and

| Metric               | MetaShift                           |                                            | ImageNetBG                          |                                            |
|----------------------|-------------------------------------|--------------------------------------------|-------------------------------------|--------------------------------------------|
|                      | GLaD                                | GLaDR                                      | GLaD                                | GLaDR                                      |
| Average Accuracy     | 58.6 $\pm 2.3$                      | <b>62.2</b> $\pm 1.2$                      | 41.7 $\pm 1.5$                      | <b>45.5</b> $\pm 1.1$                      |
| Worst-group Accuracy | 51.3 $\pm 1.8$ ( $\downarrow 7.3$ ) | <b>57.0</b> $\pm 1.0$ ( $\downarrow 5.2$ ) | 32.2 $\pm 1.6$ ( $\downarrow 9.5$ ) | <b>38.6</b> $\pm 1.0$ ( $\downarrow 6.9$ ) |

<span id="page-7-0"></span>Table 3: Top-1 test accuracy on subpopulation shift benchmarks MetaShift and ImageNetBG. All the results are reported based on the average of 5 runs.  $\mathcal{R}$  indicates the proposed robust dataset distillation method applied. The better results between baseline and the proposed method are marked in **bold**.

<span id="page-7-1"></span>Table 4: (a) Cross-architecture generalization performance comparison. The experiment is conducted on ImageNet-10 under 10 IPC settings. The distilling architecture for IDC is ResNet-10, while for GLaD is ConvNet-5.  $\mathcal{R}$  indicates the proposed robust dataset distillation method applied. (b) Ablation study on CVaR loss. The experiment is conducted on CIFAR-10 and ImageNet-A under 10 IPC. The baseline model is GLaD. "CE" denotes cross entropy loss, and "CL-min" refers to the worst sub-cluster accuracy. "aCVaR" and "mCVaR" refer to average and maximum CVaR loss, respectively. The best results are marked in bold.

|                                     |                                                                                                                             |       | (a)                                                                                                                                            |     |                                                                       |  |                  |                |     | (b)                   |                                                                                                                                                                                                                                                           |                         |
|-------------------------------------|-----------------------------------------------------------------------------------------------------------------------------|-------|------------------------------------------------------------------------------------------------------------------------------------------------|-----|-----------------------------------------------------------------------|--|------------------|----------------|-----|-----------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------|
| Method                              | Conv                                                                                                                        | Res10 | Architecture<br>Res18                                                                                                                          | ViT | VGG11                                                                 |  | Loss.            | CE aCVaR mCVaR | Acc | CIFAR-10<br>$CL$ -min | Acc                                                                                                                                                                                                                                                       | ImageNet-A<br>$CL$ -min |
| IDC.<br>$IDC^{\mathcal{R}}$<br>GLaD | GLaD <sup>72</sup> 51.9 <sub>+0.7</sub> 55.2 <sub>+0.6</sub> 53.6 <sub>+0.5</sub> 39.2 <sub>+0.9</sub> 46.3 <sub>+0.8</sub> |       | $72.6_{+0.6}$ $74.6_{+0.7}$ $72.7_{+0.8}$ $56.4_{+1.1}$ $65.6_{+0.5}$<br>$48.2_{+0.7}$ $50.9_{+0.5}$ $51.2_{+0.4}$ $36.8_{+0.9}$ $44.2_{+1.0}$ |     | $71.9_{+0.8}$ $72.8_{+0.5}$ $70.8_{+1.0}$ $55.2_{+1.2}$ $64.5_{+0.6}$ |  | $\sim$<br>$\sim$ | ۰.<br>۰        |     |                       | $46.7 + 0.6$ $40.2 + 0.8$ $53.9 + 0.6$ $40.5 + 0.7$<br>$49.1_{+0.7}$ $45.3_{+1.0}$ $56.4_{+0.5}$ $43.9_{+0.8}$<br>$47.9_{+0.8}$ $44.2_{+0.9}$ $56.1_{+0.8}$ $43.7_{+0.9}$<br>$50.2_{+0.6}$ 46.7 <sub>+0.8</sub> 57.5 <sub>+0.7</sub> 45.8 <sub>+0.5</sub> |                         |

inversion of image colors. The conclusions drawn from truncated testing sets align with those from partitioned testing sets. While dataset distillation generally contributes to improved data robustness, the introduction of CVaR loss further amplifies the trend. In this analysis with a higher resolution to capture more details, we observe that truncation on ImageNet has a smaller impact compared with CIFAR-10. Generally, the improvement by our proposed method on truncated performance is also larger than that on standard testing sets, especially on ImageNet-10. The increased advantage on robustness settings further validates that the proposed method not only elevates overall accuracy but significantly fortifies the robustness of distilled data.

Subpopulation Shift Experiments In addition to the previous standard classification tasks, we also extend the method to subpopulation shift benchmarks MetaShift and ImageNetBG [\(Yang et al.,](#page-13-5) [2023;](#page-13-5) [Liang & Zou, 2022;](#page-11-5) [Xiao et al., 2021\)](#page-12-6). The two benchmarks consider the spurious correlation and attribute generalization problems, respectively. We use GlaD as the baseline method to distill 50 images for each class. Subsequently, we conduct the standard evaluation as in the other datasets, with the results reported in Table [3.](#page-7-0) The proposed robust dataset distillation algorithm not only improves the average accuracy, but also yields a smaller worst-group accuracy margin compared with the baseline method. The results further suggest that RDD can enhance the robustness of distilled data for datasets with subpopulation shift issues.

Results on Standard Benchmark We then evaluate our proposed method on standard testing sets, including SVHN [\(Sermanet et al., 2012\)](#page-12-7), CIFAR-10 [\(Krizhevsky et al., 2009\)](#page-11-6), ImageNet-10 [\(Deng et al., 2009\)](#page-10-6), and Tiny-ImageNet [\(Deng et al., 2009\)](#page-10-6) in Table [2.](#page-6-2) The ImageNet-10 split follows the configuration outlined in IDC [\(Kim et al., 2022\)](#page-11-1). We use IDC [\(Kim et al., 2022\)](#page-11-1) and GLaD [\(Cazenavette et al., 2023\)](#page-10-2) as baselines in this section, representing distilling at the pixel level and the latent level, respectively. Additionally, the validation results are also compared with DSA, DM, and KIP [\(Zhao & Bilen, 2021;](#page-13-6) [2023b;](#page-13-7) [Nguyen et al., 2021\)](#page-11-7).

The incorporation of CVaR loss consistently enhances performance across all scenarios. Notably, under the IPC of 10, facilitated by the supervision from segregated sub-clusters, our proposed method demonstrates the most substantial performance improvement over baselines. In the case of 1 IPC, where only a single synthetic sample is available for sub-cluster construction, the optimization comes back to a DRO problem. And minimizing CVaR still captures tail risk and helps improve the validation performance, despite the absence of guidance from multiple sub-clusters. Our proposed

Image /page/8/Figure/1 description: The image contains a table and a line graph. The table, titled "Setting Method", shows performance metrics for different methods (IDM, IDM^R, GLaD, GLaD^R) across various settings (Acc, Cluster-min, Noise, Blur, Invert). The values in the table are presented as mean ± standard deviation. For example, under the "Acc" setting, IDM has a value of 67.5±0.2, IDM^R has 68.1±0.1, GLaD has 24.9±0.5, and GLaD^R has 26.8±0.6. The line graph, labeled "Figure 2: Analysis on CVaR ratio α.", plots accuracy (%) on the y-axis against CVaR Ratio α on the x-axis. Two lines are shown: a blue line with star markers representing "Standard" and a red line with star markers representing "Cluster-min". The graph indicates that the "Standard" method generally achieves higher accuracy than the "Cluster-min" method across different CVaR ratios. Dashed lines indicate baseline values for "GLaD Standard" and "GLaD Cluster-min".

 $50$ 

<span id="page-8-0"></span>Table 5: Top-1 robustness evaluation on IDM (CIFAR-10) and GLaD (TinyImageNet).

method is especially effective on ImageNet-10, characterized by finer class divisions and more substantial intra-class variation compared with CIFAR-10. On ImageNet-10 we achieve an average top-1 accuracy gain of 3.1%, further elevating the state-of-the-art baseline in DD methods. More results are presented in the Appendix Section [D.](#page-16-0)

Cross-architecture Generalization In addition to enhanced robustness against domain-shifted data, the incorporation of CVaR loss also yields better cross-architecture generalization capabilities. We assess multiple network architectures including ConvNet-5, ResNet, ViT [\(Dosovitskiy et al.,](#page-10-7) [2020\)](#page-10-7) and VGG11 [\(Simonyan & Zisserman, 2014\)](#page-12-8), and compare the performance with and without our proposed robust method in Table [4a.](#page-7-1) Despite the different distilling architectures employed in IDC and GLaD, both methods achieve their highest accuracy on ResNet-10. Notably, the proposed robust distillation method consistently enhances performance across all architectures, showcasing remarkable cross-architecture generalization capabilities.

Ablation Study We conduct an ablation study on the incorporation of CVaR loss in Table [4b.](#page-7-1) In addition to CIFAR-10, we also report results on the ImageNet-A sub-set according to the setting in the work of [Cazenavette et al.](#page-10-2) [\(2023\)](#page-10-2). Both accuracy on the standard testing set and the worst sub-set accuracy "Cluster-min" are presented. Our focus is primarily on two aspects of utilizing the CVaR loss, *i.e.* the maximum value and average value of CVaR losses across all sub-clusters. The CVaR loss operates as a complement to the standard cross-entropy optimization. Hence the baseline case, involving only cross entropy loss, mirrors the performance of GLaD. Compared with the maximum value, the average CVaR loss proves more effective in enhancing the validation performance when applied independently. While both average and maximum CVaR loss yield considerable improvement over the baseline, their combined application further fortifies the robustness of the distilled data, which is selected as our eventual implementation. Besides, cross entropy loss is still employed in the implementation for a stable optimization.

**Experiments on Distribution Matching** In addition to methods constrained by gradient matching, the proposed robust DD can also be plugged into DD methods with other matching metrics. We evaluate the efficacy with IDM [\(Zhao et al., 2023\)](#page-13-1) as the baseline, where distribution similarity is used as the matching metric. Similar to the integration in gradient matching, the CVaR loss is adopted during model training phases. As shown in the first two columns in Table [5,](#page-8-0) our proposed robust optimization achieves improvement across all metrics. The results demonstrate the possibility of applying RDD to broader dataset distillation methods for robustness enhancement.

Scalability We also scale up the proposed robust optimization to TinyImageNet, which contains 100 classes, and hence is more challenging compared with other benchmarks. The experiments are conducted on GLaD, and the results are shown in the last two columns of Table [5.](#page-8-0) When the class number to be optimized is increased, the proposed RDD method provides consistent improvement on the robustness of the distilled data by enhancing model training phases.

**Parameter Analysis** An analysis is conducted to evaluate the influence of different CVaR ratio  $\alpha$  choices of equation [3](#page-2-2) in Figure [2.](#page-8-0) We vary the  $\alpha$  value from 0.2 to 1.0 to explore different ratios of data for calculating CVaR loss. Both the validation performance on the standard testing set and the worst sub-set accuracy (Cluster-min) reach their peak at  $\alpha$ =0.8. Including all the samples  $(\alpha=1.0)$  introduces certain interruptions for the optimization due to large loss values and results

in a slight performance drop. On the other hand, considering only a small portion of samples for CVaR loss loses essential information, leading to performance degradation. Notably, even the worst performance obtained at  $\alpha$ =0.2 is significantly higher than the GLaD baseline, particularly in terms of the Cluster-min metric. This observation strongly supports the effectiveness of our proposed robust dataset distillation method.

<span id="page-9-1"></span>Image /page/9/Figure/2 description: Two scatter plots are shown side-by-side, both titled with a method name. The left plot is titled "GLaD" and the right plot is titled "Ours". Both plots display a distribution of blue dots, representing data points, and several orange dots, which appear to be highlighted or special points. The axes for both plots range from approximately -30 to 30 on both the x and y axes. The orange dots in the "GLaD" plot are located around coordinates (0, 15), (-10, 0), and (-15, -5). The orange dots in the "Ours" plot are more centrally clustered and appear around coordinates (0, 15), (-5, 5), (-5, -5), (5, -5), (0, -10), and (5, 5).

Figure 3: T-SNE distribution visualization of original samples (blue dots) and synthesized samples (orange dots) on ImageNet bonnet class.

Image /page/9/Figure/4 description: The image displays a comparison between two methods, "GLaD" and "Ours", each presented with four sub-images. Both methods show distorted or abstract representations of what appears to be a doll or puppet. The "GLaD" section features a doll-like figure with exaggerated facial features in the top-left image, and abstract, fragmented visuals in the other three. The "Ours" section also presents a similar doll-like figure with distorted features in the top-left image, and similarly abstract and fragmented visuals in the remaining three images. The overall aesthetic suggests a visual effects or generative art comparison.

Figure 4: Synthesized sample visualization comparison between GLaD and our proposed method. The samples are initialized identically.

**Visualization** To explicitly illustrate the impact of CVaR loss on the distillation results, we visualize the sample distribution comparison in Figure [3.](#page-9-1) In the feature space, samples optimized by GLaD tend to form small clusters, while the introduction of robust optimization leads to a more evenly distributed distilled dataset. Note that there is no constraint on the feature distribution applied during the distilling process. The proposed robust optimization involves loss calculation on different data partitions, contributing to better coverage over the original data distribution. The more even distribution observed further affirms the effectiveness of our proposed robust distillation method.

Additionally, we compare the synthesized samples of the same ImageNet bonnet class in the pixel space between the baseline GLaD and our proposed method in Figure [4.](#page-9-1) The images are initialized with the same original samples for better comparison. Remarkably, the additional CVaR loss introduces more irregular shapes into the image during optimization. These irregular shapes weaken specific features present in each image while introducing common features of the corresponding class, leading to a more even sample distribution in the latent space.

<span id="page-9-0"></span>

# 5 CONCLUSION

This paper explores the intricate relationship between DD and its generalization, with a particular focus on performance across uncommon subgroups, especially in regions with low population density. To address this, we introduce an algorithm inspired by distributionally robust optimization, employing clustering and risk minimization to enhance DD. Our theoretical framework, supported by empirical evidence, demonstrates the effectiveness, generalization, and robustness of our approach across diverse subgroups. By prioritizing representativeness and coverage over training error guarantees, the method offers a promising avenue for enhancing the models trained on synthetic datasets in real-world scenarios, paving the way for enhanced applications of DD in a variety of settings.

Reproducibility Statement We have provided detailed instructions to help reproduce the experimental results of this work. In Section. [G](#page-20-0) we provide the statistics of the datasets used for evaluating the proposed method. In Section. [H](#page-21-0) we provide implementation details on the baseline methods and the hyper-parameter settings of our method. The evaluation metric design of the domain-shift setting is also included. Additionally, we have attached the adopted source code in the supplementary material, which will further help understand the proposed method.

# REFERENCES

<span id="page-9-2"></span>Haoyue Bai, Yifei Ming, Julian Katz-Samuels, and Yixuan Li. Hypo: Hyperspherical out-ofdistribution generalization. In *The Twelfth International Conference on Learning Representations*, pp. 1–27, 2024.

- <span id="page-10-8"></span>Albert S Berahas, Liyuan Cao, Krzysztof Choromanski, and Katya Scheinberg. A theoretical and empirical comparison of gradient approximations in derivative-free optimization. *Foundations of Computational Mathematics*, 22(2):507–560, 2022.
- <span id="page-10-9"></span>David B Brown. Large deviations bounds for estimating conditional value-at-risk. *Operations Research Letters*, 35(6):722–730, 2007.
- <span id="page-10-1"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, pp. 4750–4759, 2022.
- <span id="page-10-2"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *CVPR*, pp. 3739–3748, 2023.
- <span id="page-10-11"></span>Xuxi Chen, Yu Yang, Zhangyang Wang, and Baharan Mirzasoleiman. Data distillation can be like vodka: Distilling more times for better quality. In *The Twelfth International Conference on Learning Representations*, 2024. URL <https://openreview.net/forum?id=1NHgmKqOzZ>.
- <span id="page-10-6"></span>Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pp. 248–255. Ieee, 2009.
- <span id="page-10-5"></span>Jean-Dominique Deuschel and Daniel W Stroock. *Large deviations*, volume 342. American Mathematical Soc., 2001.
- <span id="page-10-10"></span>Josip Djolonga, Jessica Yung, Michael Tschannen, Rob Romijnders, Lucas Beyer, Alexander Kolesnikov, Joan Puigcerver, Matthias Minderer, Alexander D'Amour, Dan Moldovan, et al. On robustness and transferability of convolutional neural networks. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 16458–16468, 2021.
- <span id="page-10-7"></span>Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. In *International Conference on Learning Representations*, 2020.
- <span id="page-10-13"></span>Jiawei Du, Yidi Jiang, Vincent Y.F. Tan, Joey Tianyi Zhou, and Haizhou Li. Minimizing the Accumulated Trajectory Error to Improve Dataset Distillation. In *CVPR*, pp. 3749–3758, 2023.
- <span id="page-10-3"></span>John C Duchi and Hongseok Namkoong. Learning models with uniform performance via distributionally robust optimization. *The Annals of Statistics*, 49(3):1378–1406, 2021.
- <span id="page-10-15"></span>Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 4367–4375, 2018.
- <span id="page-10-14"></span>Jianyang Gu, Saeed Vahidian, Vyacheslav Kungurtsev, Haonan Wang, Wei Jiang, Yang You, and Yiran Chen. Efficient dataset distillation via minimax diffusion. *arXiv preprint arXiv:2311.15529*, 2023a.
- <span id="page-10-0"></span>Jianyang Gu, Kai Wang, Wei Jiang, and Yang You. Summarizing stream data for memory-restricted online continual learning. *arXiv preprint arXiv:2305.16645*, 2023b.
- <span id="page-10-16"></span>Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, pp. 770–778, 2016.
- <span id="page-10-4"></span>Weihua Hu, Gang Niu, Issei Sato, and Masashi Sugiyama. Does distributionally robust supervised learning give robust classifiers? In *International Conference on Machine Learning*, pp. 2029–2037. PMLR, 2018.
- <span id="page-10-12"></span>Zeyi Huang, Haohan Wang, Eric P Xing, and Dong Huang. Self-challenging improves cross-domain generalization. In *Computer vision–ECCV 2020: 16th European conference, Glasgow, UK, August 23–28, 2020, proceedings, part II 16*, pp. 124–140. Springer, 2020.

- <span id="page-11-12"></span>Yuqi Jia, Saeed Vahidian, Jingwei Sun, Jianyi Zhang, Vyacheslav Kungurtsev, Neil Zhenqiang Gong, and Yiran Chen. Unlocking the potential of federated learning: The symphony of dataset distillation via deep generative latents. *CoRR*, abs/2312.01537, 2023. doi: 10.48550/ARXIV.2312.01537. URL <https://doi.org/10.48550/arXiv.2312.01537>.
- <span id="page-11-16"></span>Yang Jiao, Kai Yang, and Dongjin Song. Federated distributionally robust optimization with nonconvex objectives: Algorithm and analysis. *arXiv preprint arXiv:2307.14364*, 2023.
- <span id="page-11-13"></span>Mohsen Joneidi, Saeed Vahidian, Ashkan Esmaeili, Weijia Wang, Nazanin Rahnavard, Bill Lin, and Mubarak Shah. Select to better learn: Fast and accurate deep learning using data selection from nonlinear manifolds. *Computer Vision and Pattern Recognition, CVPR 2020. IEEE Conference on*, 2020.
- <span id="page-11-1"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *Proceedings of the International Conference on Machine Learning (ICML)*, pp. 11102–11118, 2022.
- <span id="page-11-6"></span>Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-11-10"></span>Haoliang Li, Sinno Jialin Pan, Shiqi Wang, and Alex C Kot. Domain generalization with adversarial feature learning. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 5400–5409, 2018.
- <span id="page-11-5"></span>Weixin Liang and James Zou. Metashift: A dataset of datasets for evaluating contextual distribution shifts and training conflicts. In *International Conference on Learning Representations*, pp. 1–19, 2022.
- <span id="page-11-2"></span>Fengming Lin, Xiaolei Fang, and Zheming Gao. Distributionally robust optimization: A review on theory and applications. *Numerical Algebra, Control and Optimization*, 12(1):159–212, 2022.
- <span id="page-11-11"></span>Ping Liu, Xin Yu, and Joey Tianyi Zhou. Meta Knowledge Condensation for Federated Learning. In *ICLR*, 2022a.
- <span id="page-11-15"></span>Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. *NeurIPS*, 35:1100–1113, 2022b.
- <span id="page-11-9"></span>Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. In *ICCV*, 2023.
- <span id="page-11-14"></span>Noel Loo, Ramin Hasani, Mathias Lechner, and Daniela Rus. Dataset distillation with convexified implicit gradients. *arXiv preprint arXiv:2302.06755*, 2023.
- <span id="page-11-0"></span>Dmitry Medvedev and Alexander D'yakonov. Learning to generate synthetic training data using gradient matching and implicit differentiation. In *Proceedings of the International Conference on Analysis of Images, Social Networks and Texts (AIST)*, pp. 138–150, 2021.
- <span id="page-11-8"></span>Zakaria Mhammedi, Benjamin Guedj, and Robert C Williamson. Pac-bayesian bound for the conditional value at risk. *Advances in Neural Information Processing Systems*, 33:17919–17930, 2020.
- <span id="page-11-4"></span>John Nash. Two-person cooperative games. *Econometrica: Journal of the Econometric Society*, pp. 128–140, 1953.
- <span id="page-11-7"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. In *Proceedings of the Advances in Neural Information Processing Systems (NeurIPS)*, pp. 5186–5198, 2021.
- <span id="page-11-3"></span>Yonatan Oren, Shiori Sagawa, Tatsunori B Hashimoto, and Percy Liang. Distributionally robust language modeling. In *Proceedings of the 2019 Conference on Empirical Methods in Natural Language Processing and the 9th International Joint Conference on Natural Language Processing (EMNLP-IJCNLP)*, pp. 4227–4237, 2019.

- <span id="page-12-1"></span>Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *Transactions on Machine Learning Research*, 2023.
- <span id="page-12-18"></span>Shiori Sagawa, Pang Wei Koh, Tatsunori B Hashimoto, and Percy Liang. Distributionally robust neural networks. In *International Conference on Learning Representations*, 2019.
- <span id="page-12-12"></span>Ahmad Sajedi, Samir Khaki, Ehsan Amjadian, Lucy Z Liu, Yuri A Lawryshyn, and Konstantinos N Plataniotis. DataDAM: Efficient Dataset Distillation with Attention Matching. In *ICCV*, 2023.
- <span id="page-12-7"></span>Pierre Sermanet, Soumith Chintala, and Yann LeCun. Convolutional neural networks applied to house numbers digit classification. In *Proceedings of the 21st international conference on pattern recognition (ICPR2012)*, pp. 3288–3291. IEEE, 2012.
- <span id="page-12-8"></span>Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014.
- <span id="page-12-4"></span>Jiří Souček and Vladimír Souček. Morse-sard theorem for real-analytic functions. *Commentationes Mathematicae Universitatis Carolinae*, 13(1):45–51, 1972.
- <span id="page-12-10"></span>Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative Teaching Networks: Accelerating Neural Architecture Search by Learning to Generate Synthetic Training Data. In *ICML*, pp. 9206–9216, 2020.
- <span id="page-12-11"></span>Saeed Vahidian, Baharan Mirzasoleiman, and Alexander Cloninger. Coresets for estimating means and mean square error with limited greedy samples. In *Proceedings of the Thirty-Sixth Conference on Uncertainty in Artificial Intelligence, UAI 2020, virtual online, August 3-6, 2020*, volume 124, pp. 350–359, 2020.
- <span id="page-12-5"></span>Bart PG Van Parys, Peyman Mohajerin Esfahani, and Daniel Kuhn. From data to decisions: Distributionally robust optimization is optimal. *Management Science*, 67(6):3387–3402, 2021.
- <span id="page-12-3"></span>Konstantinos Vilouras, Xiao Liu, Pedro Sanchez, Alison Q O'Neil, and Sotirios A Tsaftaris. Group distributionally robust knowledge distillation. In *International Workshop on Machine Learning in Medical Imaging*, pp. 234–242. Springer, 2023.
- <span id="page-12-9"></span>Catherine Wah, Steve Branson, Peter Welinder, Pietro Perona, and Serge Belongie. The caltech-ucsd birds-200-2011 dataset. 2011.
- <span id="page-12-15"></span>Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023a.
- <span id="page-12-17"></span>Serena Wang, Harikrishna Narasimhan, Yichen Zhou, Sara Hooker, Michal Lukasik, and Aditya Krishna Menon. Robust distillation for worst-class performance: on the interplay between teacher and student objectives. In *Uncertainty in Artificial Intelligence*, pp. 2237–2247. PMLR, 2023b.
- <span id="page-12-0"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-12-14"></span>Xing Wei, Anjia Cao, Funing Yang, and Zhiheng Ma. Sparse parameterization for epitomic dataset distillation. In *NeurIPS*, volume 36, 2024.
- <span id="page-12-13"></span>Xindi Wu, Zhiwei Deng, and Olga Russakovsky. Multimodal dataset distillation for image-text retrieval. *arXiv preprint arXiv:2308.07545*, 2023.
- <span id="page-12-16"></span>Yihan Wu, Xinda Li, Florian Kerschbaum, Heng Huang, and Hongyang Zhang. Towards robust dataset learning. *arXiv preprint arXiv:2211.10752*, 2022.
- <span id="page-12-6"></span>Kai Yuanqing Xiao, Logan Engstrom, Andrew Ilyas, and Aleksander Madry. Noise or signal: The role of image backgrounds in object recognition. In *International Conference on Learning Representations*, 2021. URL <https://openreview.net/forum?id=gl3D-xY7wLq>.
- <span id="page-12-2"></span>Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. FedDM: Iterative Distribution Matching for Communication-Efficient Federated Learning. In *CVPR*, pp. 16323– 16332, 2023.

- <span id="page-13-4"></span>Huan Xu and Shie Mannor. Robustness and generalization. *Machine learning*, 86:391–423, 2012.
- <span id="page-13-9"></span>Eric Xue, Yijiang Li, Haoyang Liu, Yifan Shen, and Haohan Wang. Towards adversarially robust dataset distillation by curvature regularization. *arXiv preprint arXiv:2403.10045*, 2024.
- <span id="page-13-5"></span>Yuzhe Yang, Haoran Zhang, Dina Katabi, and Marzyeh Ghassemi. Change is hard: A closer look at subpopulation shift. In *International Conference on Machine Learning*, pp. 39584–39622. PMLR, 2023.
- <span id="page-13-10"></span>Netzer Yuval. Reading digits in natural images with unsupervised feature learning. In *Proceedings of the NIPS Workshop on Deep Learning and Unsupervised Feature Learning*, 2011.
- <span id="page-13-3"></span>Yibo Zeng and Henry Lam. Generalization bounds with minimal dependency on hypothesis class via distributionally robust optimization. *Advances in Neural Information Processing Systems*, 35: 27576–27590, 2022.
- <span id="page-13-6"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *Proceedings of the International Conference on Machine Learning (ICML)*, pp. 12674–12685, 2021.
- <span id="page-13-0"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, pp. 6514– 6523, 2023a.
- <span id="page-13-7"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision (WACV)*, pp. 6514–6523, 2023b.
- <span id="page-13-2"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021.
- <span id="page-13-1"></span>Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved Distribution Matching for Dataset Condensation. In *CVPR*, pp. 7856–7865, 2023.
- <span id="page-13-8"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *NeurIPS*, 35:9813–9827, 2022.

# APPENDIX

The appendix is organized as follows: Sec. [A](#page-14-0) provides more detailed theoretical analysis. Sec. [C](#page-15-1) presents the proof for proposition [3.4.](#page-5-1) Sec. [G](#page-20-0) and Sec. [H](#page-21-0) contain the dataset details and implementation details, respectively. Sec. [D](#page-16-0) offers more experimental results of the proposed robust dataset distillation method. Section [I](#page-23-0) discusses the broader impact; and finally, Section [F](#page-20-1) presents more sample visualization of the robust dataset distillation method.

<span id="page-14-0"></span>

## A THEORETICAL DETAILS

## A.1 DRO AND GENERALIZATION

Here we present several results from the literature that indicate that a DRO on an empirical risk minimization problem exhibits generalization guarantees, i.e., approximately minimizes the population (or expected test dataset) loss. Consider equation [9,](#page-4-1) repeated here

$$
\min_{S} \max_{\mathcal{Q} \in \mathbb{Q}} \max_{\mathcal{Q}' \in \bar{\mathcal{Q}}} \quad \mathbb{E}[F(\theta^*, \mathcal{Q}')] \n\text{s.t.} \quad \theta^* \in \arg \min_{\theta} F(\theta, S) \n\bar{\mathcal{Q}} = \{ \mathcal{Q}' : I(\mathcal{Q}', \mathcal{S} \cup \mathcal{Q}_N) \le r \}.
$$

- 1. Theorem 3 in the work of [Xu & Mannor](#page-13-4) [\(2012\)](#page-13-4) provides a bound for the test error at the DRO solution.
- 2. Theorem 3.1 in the work of [Zeng & Lam](#page-13-3) [\(2022\)](#page-13-3) presents a probability that  $|\mathbb{E}[F(\theta_{DRO}^*, \mathcal{Q})] - \mathbb{E}[F(\theta_{opt}^*, \mathcal{Q})]| \leq \epsilon$  as a function of  $\epsilon$ , where we denote the DRO and the exact minimum, respectively.

## A.2 LARGE DEVIATIONS AND DATA DRIVEN DRO

The analysis of the theoretical convergence and robustness properties of our method will rely significantly on the theoretical foundations of data driven DRO established in the work of [Van Parys](#page-12-5) [et al.](#page-12-5)  $(2021)$ . To this end, we review a few pertinent definitions. A *predictor* is a function  $c$ :  $\mathbb{R}^d \times \Xi \to \mathbb{R}$  that defines a model as applied to a data distribution. A *data driven predictor* uses an empirical distribution of samples  $\mathbb{P}_T$  in the prediction.

The *sample average predictor* is given as

$$
c(\theta, \hat{\mathbb{P}}_T) = \frac{1}{T} \sum_{t=1}^T F(\theta, \xi_t)
$$

Let  $\hat{\theta} = \arg \min \hat{c}(\theta, \mathbb{P})$  be the data driven predictor.

An ordering  $\leq$  is introduced to rank the set of predictors, with

$$
(\hat{c}_1, \hat{\theta}_1) \preceq (\hat{c}_2, \hat{\theta}_2) \text{ if and only if } \hat{c}_1(\hat{\theta}_1(\mathbb{P}'), \mathbb{P}') \leq \hat{c}_2(\hat{\theta}_2(\mathbb{P}'), \mathbb{P}'), \forall \theta, \mathbb{P}' \in \mathcal{P}
$$

The *Distributionally Robust Predictor* is one defined as,

$$
\hat{c}_r(\theta, \mathbb{P};) = \sup_{\mathbb{P} \in \mathcal{P}} \left\{ c(\theta, \mathbb{P}) : \mathcal{D}_I(\mathbb{P}', \mathbb{P}) \le r \right\}
$$

where  $\mathcal{D}_I(A, B)$  is the mutual information of random variables A and B.

They define the optimization problem

<span id="page-14-1"></span>
$$
\min_{\begin{array}{l}\n(\hat{c}, \hat{\theta}) \\
\text{s.t.} \quad \lim \sum_{T \to \infty} \frac{1}{T} \log \mathbb{P}^{\infty} \left( c(\hat{\theta}(\hat{\mathbb{P}}_T, \mathbb{P}) > \hat{c}(\hat{\theta}, \hat{\mathbb{P}}_T) \right) \leq -r, \forall \mathbb{P} \in \mathcal{P}\n\end{array} \tag{15}
$$

where the minimum is the lower bound with respect to the ordering  $\preceq$  of all feasible (LDP satisfying) predictors.

[Van Parys et al.](#page-12-5) [\(2021\)](#page-12-5) prove that the distributionally robust predictor solves equation [15](#page-14-1) in Theorem 4, for discrete distributions, and Theorem 6, for continuous ones.

We are constructing a synthetic dataset, while simultaneously considering training subsamples to form the empirical measure at each iteration, suggesting that the data-driven framework can fit the problem of interest. Formally, we consider solving optimization problems defined on a set of measures as decision variables,

$$
\min_{S} \max_{\mathcal{Q} \in \mathbb{Q}} \quad \mathbb{E}[F(\theta^*, \mathcal{Q})] \\ \text{s.t.} \quad \theta^* \in \arg \min_{\theta} F(\theta, S)
$$

by solving the data driven DRO approximation for the test error,

$$
\min_{S} \max_{\mathcal{Q} \in \mathbb{Q}} \max_{\mathcal{Q}' \in \bar{\mathcal{Q}}} \quad \mathbb{E}[F(\theta^*, \mathcal{Q}')] \n\text{s.t.} \quad \theta^* \in \arg \min_{\theta} F(\theta, S) \n\bar{\mathcal{Q}} = \{ \mathcal{Q}' : I(\mathcal{Q}', S \cup \mathcal{Q}_N) \le r \}
$$
\n(16)

Note that the form is the nested DRO problem described earlier. At the upper layer, there is an uncertainty set regarding the choice of  $\mathcal{Q} \in \mathbb{Q}$ , targeting group robustness. There is the data-driven DRO in the lower level, which is an algorithmic approximation of the population risk minimization with established accuracy guarantees.

### B GENERALIZE TO BROADER DATASET DISTILLATION SCENARIOS

<span id="page-15-0"></span>Algorithm 2 Zero-order Dataset Distillation

**Input:** Initial synthetic set S, distilling objective  $L(S)$ Execute: For epoch  $E = 1, ...$ Compute with  $v_l \sim \mathcal{N}(0, I)$ :  $g(S) \approx \nabla L(S) = \frac{1}{M}$  $\sum_{ }^{M}$  $l=1$  $L(S + \sigma v_l) - L(S)$ σ  $(17)$ Set stepsize s (diminishing stepsize rule, or stepwise decay). For instance:  $s = 0.1/\sqrt{1+E}$  (18)

Set  $S - sg \rightarrow S$ 

Existing dataset distillation methods adopt differentiable matching metrics to optimize the synthetic data  $S$ . However, there can be circumstances where the gradient of the objective cannot be directly acquired. In this case, dataset distillation can still be performed by zero order approximation of the gradient. Starting with some initial  $S$  and proceed as in Algorithm [2,](#page-15-0) a standard gradient descent approach can be applied, using a zero order approximation of the gradient of  $L$  concerning the dataset S, that is  $q(S) \approx \nabla L(S)$ , computed only using evaluations of  $L(S)$ .

We can use any method from [Berahas et al.](#page-10-8) [\(2022\)](#page-10-8) to obtain a gradient estimate of  $g \approx \nabla_S L(S)$ , here the featured procedure is Gaussian smoothing. The proposed robust dataset distillation method is still able to be applied towards  $L(S)$  for more robust optimization.

<span id="page-15-1"></span>

# C PROOF OF PROPOSITION [3.4](#page-5-1)

Consider the quantity:

<span id="page-15-2"></span>
$$
\frac{1}{N}\log \mathbb{P}_{\mathcal{Q}}^{\infty} \left( F(\theta^*(\mathcal{S}), \mathbb{P}_{\mathcal{Q}}) > F(\theta^*(\mathcal{S}), \mathcal{S} \cup \mathcal{Q}_N) \right)
$$
\n(19)

Now recall the expression for CVaR:

<span id="page-16-1"></span>Table 6: Top-1 test accuracy on CUB-200 transferred from ImageNet subsets. All the results are reported based on the average of 5 runs.  $\mathcal{R}$  indicates the proposed robust dataset distillation method applied. The better results between baseline and the proposed method are marked in bold.

| <b>Transfer Learning</b> | <b>GLaD</b> | <b>GLaDR</b>      |
|--------------------------|-------------|-------------------|
| ImageNet-50 → CUB-200    | 49.2 ± 0.9  | <b>54.6</b> ± 0.6 |

<span id="page-16-2"></span>Table 7: Top-1 test accuracy on more challenging test on the syn data trained model. All the results are reported based on the average of 5 runs.  $\overline{R}$  indicates the proposed robust dataset distillation method applied. The better results between baseline and the proposed method are marked in bold.

| Group                                | <b>GL</b> aD  | $GLAD^{\mathcal{R}}$ |
|--------------------------------------|---------------|----------------------|
| ImageNet-10 $\rightarrow$ ImageNet-A | $56.0_{+1.1}$ | $59.6 + 0.9$         |
| ImageNet-10 $\rightarrow$ ImageNet-B | $43.0_{+0.9}$ | $47.2_{+1.0}$        |

$$
\text{CVaR}[F(\theta; c_i)] = -\frac{1}{\alpha} \left\{ \frac{1}{|c_i|} \sum_{z_t \in c_i} F(\theta; z_t) \mathbb{1}[F(\theta; z_t) \le f_\alpha] + f_\alpha (\alpha - \frac{1}{|c_i|} \sum_{z_t \in c_i} \mathbb{1}[F(\theta; z_t) \le f_\alpha] \right\}
$$

and

$$
f_{\alpha} = \min\{f \in \mathbb{R} : \frac{1}{|c_i|} \sum_{z_t \in c_i} 1[F(\theta; z_t) \le f_{\alpha}] \ge \alpha\}
$$

Step 1: CVaR itself satisfies a LDP [\(Brown, 2007;](#page-10-9) [Mhammedi et al., 2020\)](#page-11-8). With these results, we can relate the empirical to the population CVaR.

**Step 2:** Notice that as long as  $\alpha < \frac{1}{2}$ , then minimizing the CVaR will also minimize equation [19.](#page-15-2)

Step 3: There is at least one estimator satisfying the LDP, namely, the DRO. Thus minimizing the empirical CVaR, by the feasibility of the LDP, must result in an estimator that satisfies the LDP.

<span id="page-16-0"></span>

## D MORE EXPERIMENTS AND ANALYSIS

## D.1 MORE CHALLENGING TESTING SCENARIOS

In Section [4,](#page-6-0) we demonstrate that the proposed robust dataset distillation method is able to enhance the robustness of the generated data on shifted or truncated testing sets of the same classes. In this section, we evaluate RDD on more challenging testing cases.

Downstream Fine-tuning The robustness of a neural network can be tested in transfer learning scenarios [\(Djolonga et al., 2021\)](#page-10-10). Accordingly, we conduct the experiment of transferring the model trained on distilled data to downstream tasks. More concretely, the model is first pre-trained on distilled data from the 50-class subset of ImageNet [\(Cazenavette et al., 2023\)](#page-10-2). Then it is fine-tuned on CUB-200 [Wah et al.](#page-12-9) [\(2011\)](#page-12-9). We use the top-1 accuracy on CUB-200 to evaluate the transferability of the distilled data, and the results are shown in Table [6.](#page-16-1) The results suggest that the data distilled with RDD applied significantly enhances the transferability of the trained model.

**One-shot Direct Transfer** A more challenging case would be directly applying the model trained on the distilled data on completely different classes. As in the original model, a classifier is involved to predict the classification probability, directly applying the classifier to different classes would be infeasible. Thus, we adopt a one-shot retrieval-style evaluation approach instead. In this approach, the model is initially trained using the distilled data of 10 classes and subsequently tested on another set of 10 classes. During testing, the corresponding images are passed through the backbone to obtain embedded features, with which a similarity matrix is computed. We use each sample as a query, and check whether the most similar sample among the remaining samples belongs to the same class and report the top-1 accuracy.

<span id="page-17-0"></span>

| Table 8: Top-1 test accuracy on standard and robustness testing sets. All the results are reported based            |
|---------------------------------------------------------------------------------------------------------------------|
| on the average of 5 runs. The experiment is conducted with DREAM and PDD on the CIFAR-10                            |
| IPC-50 setting. $\mathcal{R}$ indicates the proposed robust dataset distillation method applied. The better results |
| between baseline and the proposed method are marked in <b>bold</b> .                                                |

| Method | Acc              | Cluster-min      | Noise            | Blur             | Invert           |
|--------|------------------|------------------|------------------|------------------|------------------|
| PDD    | $67.9_{\pm 0.2}$ | $63.9_{\pm 0.4}$ | $58.2_{\pm 0.7}$ | $48.9_{\pm 1.1}$ | $25.7_{\pm 0.5}$ |
| PDDR   | $68.7_{\pm 0.6}$ | $65.1_{\pm 0.3}$ | $59.4_{\pm 0.9}$ | $50.6_{\pm 0.9}$ | $26.7_{\pm 0.7}$ |
| DREAM  | $69.4_{\pm 0.4}$ | $64.7_{\pm 0.6}$ | $58.8_{\pm 1.2}$ | $50.1_{\pm 0.8}$ | $25.7_{\pm 0.6}$ |
| DREAMR | $69.7_{\pm 0.5}$ | $65.5_{\pm 0.4}$ | $59.8_{\pm 0.9}$ | $50.7_{\pm 0.7}$ | $26.9_{\pm 0.8}$ |

<span id="page-17-1"></span>Table 9: Top-1 test accuracy on different cluster numbers. All the results are reported based on the average of 5 runs. The experiment is conducted with GLaD on CIFAR-10 under the IPC of 50.  $\mathcal{R}$ indicates the proposed robust dataset distillation method applied. The best results are marked in bold.

| Cluster | Acc                           | Cluster-min                       | Noise                             | Blur                         | Invert                        |
|---------|-------------------------------|-----------------------------------|-----------------------------------|------------------------------|-------------------------------|
| 5<br>10 | $61.6 + 0.8$<br>$62.5_{+0.7}$ | $55.1_{\pm 0.9}$<br>$56.6_{+1.0}$ | $54.6_{\pm 0.7}$<br>$55.7_{+0.7}$ | $40.3_{\pm 0.5}$<br>41.1+0.6 | $17.5 + 0.9$<br>$18.0_{+0.9}$ |
| 20      | $60.9 + 0.6$                  | $54.3_{+1.2}$                     | $52.8 + 0.4$                      | $39.8 + 0.5$                 | $17.3_{+0.7}$                 |
| 30      | $59.9_{+0.7}$                 | $53.2_{+0.9}$                     | $51.5_{\pm 0.6}$                  | $38.9_{+0.5}$                | $17.1_{+1.0}$                 |

The results are presented in Table [7.](#page-16-2) For these two groups, the model is firstly trained on distilled ImageNet-10 and then tested on ImageNet-A and ImageNet-B, respectively. The subset split of ImageNet-A and ImageNet-B follows the setting in GLaD [\(Cazenavette et al., 2023\)](#page-10-2). With the robust optimization applied, the distilled data also shows better robustness on unseen classes, demonstrating the efficacy of the proposed method in enhancing the generalizability of the distilled data.

## D.2 APPLICATION ON MORE DD METHODS

In addition to IDC [\(Kim et al., 2022\)](#page-11-1) and GLaD [\(Cazenavette et al., 2023\)](#page-10-2), we further conduct experiments on CIFAR-10 with DREAM [\(Liu et al., 2023\)](#page-11-9) and PDD [\(Chen et al., 2024\)](#page-10-11) as baselines in Table [8.](#page-17-0) Similar to the implementation for IDC, we use the extra CVaR criterion during the model updating. By the application of robust optimization, all the reported metrics have been improved, especially on the domain-shifted settings. This result demonstrates the generality of the proposed framework across DD methods.

## D.3 EFFICIENCY EVALUATION

As the proposed robust dataset distillation method involves extra CVaR loss calculation during the model updating, the efficiency issue might be a concern. Accordingly, we record the required time to complete the extra robust optimization on IDC. The baseline requires 70s to finish a loop, while the CVaR loss calculation takes up extra 30s. The robust optimization takes up less than 50% of the original calculation time. The extra time is not overwhelming, but brings significant improvements on the robustness of the distilled data.

## D.4 CHOICE OF THE NUMBER OF CLUSTERS

The CVaR loss calculation involves separating the training set into several sub-sets. Different settings of sub-set division can have influence on the CVaR optimization effects. In this work, the choice of the number of clusters is primarily based on common settings observed in DD benchmarks. Typically, our method is evaluated across different Images-per-class (IPC) settings, such as 1, 10, and 50. For an IPC below 10, we simply use the IPC as the number of clusters, and treat the synthetic samples as the cluster center. However, when dealing with a larger IPC, too many clusters might potentially result in insufficient samples in each cluster for CVaR loss calculation. To address this concern, we conduct a parameter analysis on the number of clusters specifically under an IPC of 50, where the number of clusters varies from 5 to 30. Synthetic samples of the same number are randomly selected to serve

<span id="page-18-0"></span>

| Table 10: Top-1 test accuracy on different mini-batch sizes. All the results are reported based on the          |
|-----------------------------------------------------------------------------------------------------------------|
| average of 5 runs. The experiment is conducted with GLaD on CIFAR-10 under the IPC of 10. $\mathcal{R}$         |
| indicates the proposed robust dataset distillation method applied. The best results are marked in <b>bold</b> . |

| Mini-batch Size | Acc                                | Cluster-min                        |
|-----------------|------------------------------------|------------------------------------|
| 64              | $46.9_{\pm 0.7}$                   | $40.3_{\pm 1.1}$                   |
| 128             | $48.9_{\pm 0.8}$                   | $45.1_{\pm 0.8}$                   |
| 256             | <b><math>50.2_{\pm 0.5}</math></b> | <b><math>46.7_{\pm 0.9}</math></b> |
| 512             | $49.3_{\pm 0.5}$                   | $45.7_{\pm 1.2}$                   |

<span id="page-18-1"></span>Table 11: Ablation study on the initialization of synthetic samples. All the results are reported based on the average of 5 runs.  $\mathcal{R}$  indicates the proposed robust dataset distillation method applied. "Cluster" means that the synthetic samples are initialized with clustering centers.

| Dataset     | IPC | GLaD             | GLaDR            | GLaDR+Cluster    |
|-------------|-----|------------------|------------------|------------------|
| CIFAR-10    | 1   | $28.0_{\pm 0.8}$ | $29.2_{\pm 0.8}$ | $29.4_{\pm 0.9}$ |
|             | 10  | $46.7_{\pm 0.6}$ | $50.2_{\pm 0.5}$ | $50.3_{\pm 0.6}$ |
| ImageNet-10 | 1   | $33.5_{\pm 0.9}$ | $36.4_{\pm 0.8}$ | $36.5_{\pm 0.8}$ |
|             | 10  | $50.9_{\pm 1.0}$ | $55.2}$          | $55.0_{\pm 1.2}$ |

as cluster centers, and sub-sets are accordingly separated based on these centers. The experiment is conducted with GLaD on CIFAR-10, and the results are shown in Table [9.](#page-17-1)

When the number of clusters increases over IPC, as the total training sample number is fixed, the sample number belonging to each sub-set would decrease. And the insufficient samples from each cluster cause sub-optimal CVaR optimization, leading to a performance drop. By empirical observation, we fix the number of clusters to 10 for large-IPC settings.

## D.5 CHOICE OF MINI-BATCH SIZE

As the group DRO does require abundant samples for precise calculation, we set the sample number in a mini-batch as 256 in our experiments. We further conduct an analysis on the parameter setting, and the results are listed in Table [10](#page-18-0) (on CIFAR-10). When there are limited samples in a mini-batch, the performance will be largely influenced. Applying a mini-batch size around 256 brings a mild influence on the validation performance. The slight performance drop when the mini-batch size is set as 512 might be due to less diversity between different mini-batches.

## D.6 CHOICE OF INITIALIZATION

As the clusters are separated based on the synthetic samples, different initialization of synthetic samples can influence the effects of CVaR loss calculation. In our experiments, we follow the baseline setting, which is random initialization with real samples for both IDC [\(Kim et al., 2022\)](#page-11-1) and GLaD [\(Cazenavette et al., 2023\)](#page-10-2). We accordingly conduct an ablation study to evaluate the robustness of RDD on the initialization. In addition to random sampling, we also test initializing the synthetic samples with clustering centers, which leads to a more even distribution. The results are shown in Table [11,](#page-18-1) where clustering initialization yields similar results to random initialization. Although random initialization cannot guarantee an even distribution at the beginning, during the subsequent optimization process, the algorithm is still robust enough to handle different initializations and provide stable performance improvement over the baseline.

## D.7 EVALUATION ON DOMAIN GENERALIZATION BASELINES

The proposed robust dataset distillation method mainly focuses on enhancing the robustness of distilled datasets. The method can also be combined with other domain generalization methods to further enhance the robustness of model training. We have included the three generalization methods MMD [\(Li et al., 2018\)](#page-11-10), RSC [\(Huang et al., 2020\)](#page-10-12), and HYPO [\(Bai et al., 2024\)](#page-9-2) in Table [12](#page-19-1) to serve as training pipelines. The results suggest that on the one hand, RDD consistently provides performance

<span id="page-19-1"></span>

| Table 12: Evaluation of the proposed robust dataset distillation on other domain generalization             |
|-------------------------------------------------------------------------------------------------------------|
| methods with GLaD. All the results are reported based on the average of 5 runs. $\mathcal{R}$ indicates the |
| proposed robust dataset distillation method applied. The better results between baseline and the            |
| proposed method are marked in <b>bold</b> .                                                                 |

| Method          | CIFAR-10   |            | ImageNet-10 |            |
|-----------------|------------|------------|-------------|------------|
|                 | GL aD      | $GLaDR$    | GL aD       | $GLaDR$    |
| <b>Baseline</b> | $46.7±0.6$ | $50.2±0.5$ | $50.9±1.0$  | $55.2±1.1$ |
| <b>MMD</b>      | $47.1±0.6$ | $51.3±0.5$ | $51.8±1.3$  | $56.5±1.2$ |
| <b>RSC</b>      | $47.9±0.5$ | $52.5±0.5$ | $52.4±1.1$  | $56.8±1.0$ |
| <b>HYPO</b>     | $49.0±0.5$ | $53.2±0.6$ | $53.6±1.2$  | $58.1±1.1$ |

<span id="page-19-2"></span>Table 13: Validation accuracy on more ImageNet sub-sets in comparison with GLaD. The data is distilled with ConvNet-5 and evaluated with ResNet-10 under the IPC of 10.  $\mathcal{R}$  indicates the proposed robust dataset distillation method applied. The better results between baseline and the proposed method are marked in bold.

| Dataset    | Acc              |                  | Cluster-min      |                  |
|------------|------------------|------------------|------------------|------------------|
|            | <b>GLaD</b>      | <b>GLaDR</b>     | <b>GLaD</b>      | <b>GLaDR</b>     |
| ImageNet-A | $53.9_{\pm 0.7}$ | $57.5_{\pm 1.2}$ | $40.5_{\pm 0.9}$ | $45.8_{\pm 0.8}$ |
| ImageNet-B | $50.3_{\pm 0.9}$ | $53.8_{\pm 0.8}$ | $42.9_{\pm 1.1}$ | $47.0_{\pm 1.0}$ |
| ImageNet-C | $49.2_{\pm 0.8}$ | $51.3_{\pm 0.6}$ | $28.2_{\pm 0.7}$ | $32.8_{\pm 0.6}$ |
| ImageNet-D | $39.1_{\pm 0.6}$ | $40.9_{\pm 0.7}$ | $27.0_{\pm 0.8}$ | $31.3_{\pm 0.9}$ |
| ImageNet-E | $38.9_{\pm 0.8}$ | $41.1_{\pm 0.9}$ | $25.8_{\pm 0.6}$ | $30.0_{\pm 0.7}$ |

improvement on different training pipelines. On the other hand, the combination of RDD and other domain generalization methods can further improve the results over the baseline.

### D.8 RESULTS ON MORE IMAGENET SUB-SETS

GLaD [\(Cazenavette et al., 2023\)](#page-10-2) designs experiments on multiple ImageNet sub-sets. We further provide results on these sub-sets in comparison with GLaD in Table [13.](#page-19-2) The sub-set division remains consistent with that in the work of [Cazenavette et al.](#page-10-2) [\(2023\)](#page-10-2). Our proposed robust DD method consistently outperforms the baseline, particularly in terms of the worst accuracy across clustered testing sub-sets. This observation underscores the stability and versatility of the proposed method.

<span id="page-19-0"></span>

# E RELATED WORK

Dataset Distillation Dataset distillation (DD) seeks to distill the richness of extensive datasets into compact sets of synthetic images that closely mimic training performance [\(Sachdeva & McAuley,](#page-12-1) [2023\)](#page-12-1). These condensed images prove invaluable for various tasks, including continual learning [\(Gu](#page-10-0) [et al., 2023b\)](#page-10-0), federated learning [\(Liu et al., 2022a;](#page-11-11) [Jia et al., 2023\)](#page-11-12), neural architecture search [\(Such](#page-12-10) [et al., 2020;](#page-12-10) [Medvedev & D'yakonov, 2021\)](#page-11-0), and semi-supervised learning [\(Vahidian et al., 2020;](#page-12-11) [Joneidi et al., 2020\)](#page-11-13). Existing DD methodologies can be broadly categorized into bi-level optimization and training metric matching approaches. Bi-level optimization integrates meta-learning into the surrogate image update process with the validation performance as a direct optimizatino target [\(Zhou](#page-13-8) [et al., 2022;](#page-13-8) [Loo et al., 2023\)](#page-11-14). Conversely, metric matching techniques refine synthetic images by aligning with training gradients [\(Kim et al., 2022;](#page-11-1) [Liu et al., 2023\)](#page-11-9), feature distribution [\(Sajedi et al.,](#page-12-12) [2023;](#page-12-12) [Zhao et al., 2023\)](#page-13-1), or training trajectories [\(Wu et al., 2023;](#page-12-13) [Du et al., 2023\)](#page-10-13) compared to the original images. Data parametrization [\(Kim et al., 2022;](#page-11-1) [Liu et al., 2022b;](#page-11-15) [Wei et al., 2024\)](#page-12-14) and generative prior [\(Cazenavette et al., 2023;](#page-10-2) [Gu et al., 2023a;](#page-10-14) [Wang et al., 2023a\)](#page-12-15) are also considered for more efficient DD method construction.

Robustness in Dataset Distillation To the best of our knowledge, this is the first work on subgroup accuracy specifically, or even DRO generally, for Dataset Distillation. We consider a few classes of related works. In the context of DD, adversarial robustness is another popular notion of robustness,

<span id="page-20-3"></span>

| <b>Training Dataset</b> | Class | Images per class | Resolution |
|-------------------------|-------|------------------|------------|
| <b>SVHN</b>             | 10    | ~ 6000           | 32x32      |
| CIFAR10                 | 10    | 5000             | 32x32      |
| ImageNet-10             | 10    | ~ 1200           | 128x128    |
| ImageNet subsets        | 10    | ~ 1200           | 128x128    |

| Table 14: The configurations of different datasets |  |  |  |  |  |
|----------------------------------------------------|--|--|--|--|--|
|----------------------------------------------------|--|--|--|--|--|

which is simply minimization with respect to the worst possible sample in the support of some perturbation on the data sample, rather than the distribution. Adversarial Robustness is more conservative than DRO, however, in some security-sensitive circumstances, this is a more appropriate framework [\(Wu et al., 2022;](#page-12-16) [Xue et al., 2024\)](#page-13-9). In the field of knowledge distillation, wherein a more parsimonious model, rather than dataset, is of interest, group DRO has been considered in the work of [Vilouras et al.](#page-12-3) [\(2023\)](#page-12-3) and [Wang et al.](#page-12-17) [\(2023b\)](#page-12-17).

The concept of group DRO is considered and an implementation thereof is demonstrated in the work of [Sagawa et al.](#page-12-18) [\(2019\)](#page-12-18), complemented with a regularization strategy that appears to assist in performance for small population subgroups. The coverage of disparate data distributions is also a concern in Non-IID federated learning. [Jiao et al.](#page-11-16) [\(2023\)](#page-11-16) present convergence theory for DRO in a federated setting.

<span id="page-20-1"></span>

# F VISUALIZATION OF SYNTHESIZED SAMPLES

We provide the visualization of synthesized samples in different datasets in Figure [5](#page-20-2) to Figure [8.](#page-22-0) Each row represents a class.

<span id="page-20-2"></span>Image /page/20/Picture/7 description: The image displays two grids of images. The left grid contains a variety of objects, including airplanes, cars, birds, cats, dogs, deer, frogs, and trucks. The right grid consists of images of handwritten digits from 0 to 9, arranged in rows and columns.

Figure 5: Synthesized samples of CIFAR-10 (left) and SVHN (right).

<span id="page-20-0"></span>

# G DATASET STATISTICS

We evaluate our method on the following datasets:

• SVHN [\(Yuval, 2011\)](#page-13-10) is a dataset for digits recognition cropped from pictures of house number plates that is widely used for validating image recognition models. It includes 600,000 32×32 RGB images of printed digits ranging from 0 to 9. SVHN comprises three subsets: a training set, a testing set, and an extra set of 530,000 less challenging images that can aid in the training process. SVHN dataset is released with a CC0:Public Domain license.

Image /page/21/Picture/1 description: The image displays two grids of smaller images, each grid containing 49 images arranged in a 7x7 format. The left grid features a variety of subjects including a child's face, dogs, flowers, snakes, and people in various settings. The right grid also shows a diverse range of subjects, with a prominent presence of dogs, fields of yellow flowers, birds, hamsters, Venetian canals with gondolas, and ducks in water. Both grids appear to be a collection of generated or sampled images, possibly from a machine learning model, with some images exhibiting a slightly blurred or abstract quality.

Figure 6: Synthesized samples of ImageNet-10 (left) and ImageNet-A (right).

Image /page/21/Picture/3 description: The image displays two grids of photographs, each containing 42 images arranged in 7 rows and 6 columns. The left grid features images of flamingos, computer screens displaying web pages, colorful parrots, wild dogs, close-ups of flowers and plants, buses and trams in city streets, hedgehogs, fluffy dogs, car dashboards, and shorebirds. The right grid showcases images of urban landscapes with graffiti, explosions or fireworks, ships and boats on water, close-ups of mechanical parts and wheels, various birds perched on branches, tropical fish, lions, colorful birds, butterflies, and yellow birds. The overall presentation suggests a comparative or illustrative display of diverse subjects.

Figure 7: Synthesized samples of ImageNet-B (left) and ImageNet-C (right).

- CIFAR-10 [\(Krizhevsky et al., 2009\)](#page-11-6) is a subset of the Tiny Images dataset and consists of 60000 32x32 color images. The images are labeled with one of 10 mutually exclusive classes: airplane, automobile, bird, cat, deer, dog, frog, horse, ship, and truck. There are 6000 images per class with 5000 training and 1000 testing images per class. CIFAR-10 dataset is released with an MIT license.
- ImageNet-10 and ImageNet subsets is the subset of ImageNet-1K [\(Deng et al., 2009\)](#page-10-6) containing 10 classes, where each class has approximately 1, 200 images with a resolution of  $128 \times 128$ . The individual configurations of these datasets are shown in Table [14.](#page-20-3) No license is specified for ImageNet.

<span id="page-21-0"></span>

# H IMPLEMENTATION DETAILS

The proposed method can be applied to various popular dataset distillation frameworks. In this paper, we perform experiments on pixel-level distillation method IDC [\(Kim et al., 2022\)](#page-11-1) and latent-level method GLaD [\(Cazenavette et al., 2023\)](#page-10-2) to substantiate the consistent efficacy of our method. The experiments are conducted on popular dataset distillation benchmarks, namely SVHN, CIFAR-10,

<span id="page-22-0"></span>Image /page/22/Picture/1 description: The image displays two grids of smaller images, each grid containing 49 images arranged in a 7x7 layout. The left grid features a variety of animals, including ostriches, dogs, birds, horses, cows, butterflies, and owls. The right grid also showcases animals such as penguins, swans, and toucans, alongside images of people engaged in activities like pottery and cooking, as well as various machines like printers and electronic equipment. The overall presentation is a collage of diverse subjects, with a mix of natural and man-made elements.

Figure 8: Synthesized samples of ImageNet-D (left) and ImageNet-E (right).

and ImageNet [\(Yuval, 2011;](#page-13-10) [Krizhevsky et al., 2009;](#page-11-6) [Deng et al., 2009\)](#page-10-6). The images of SVHN and CIFAR-10 are resized to  $32\times32$ , while those from ImageNet-10 are resized to  $128\times128$ , representing diverse resolution scenarios. The split of ImageNet-10 subset follows [Kim et al.](#page-11-1) [\(2022\)](#page-11-1)

The CVaR loss and the Cluster-min metric calculation both involve clustering. For the CVaR loss, Euclidean distance is adopted to evaluate the sample relationships. The real samples are assigned to the synthetic sample with the smallest distance. Due to the CVaR loss calculation involving an ample number of samples, the mini-batch size during model updating is increased to 256. In cases where the IPC setting is less than 10, the cluster number in Eq. [2](#page-2-1) is set equal to IPC. For larger IPCs, the cluster number is fixed at 10, with 10 random synthesized samples chosen as the clustering centers. The ratio  $\alpha$  in CVaR loss is set to 0.8. For the Cluster-min metric calculation, we first apply the standard KMeans algorithm to partition the original test set into 10 subsets. Specifically, as outlined in Algorithm 1, the subsampling and clustering processes are performed within each class. For the cluster-min metric calculation, we execute the clustering in the RGB space. This method ensures that clusters are formed based on common features shared by these samples rather than random selection, resulting in clusters that include samples from different classes. We have also verified that each cluster contains samples from all classes.

For the Cluster-min metric calculation, a standard KMeans algorithm is conducted to separate the original testing set into 10 sub-sets. To clarify, According to Algorithm 1, the subsampling and clustering processes are based on each class. As for the cluster-min metric calculation, we conduct the clustering in the RGB space. This approach ensures that clusters are formed based on common features rather than random selection and include samples from different classes. We have examined the cluster division to make sure that each cluster contains samples from all classes.

All the experiments are conducted on a single 24G RTX 4090 GPU.

For a fair comparison, the experiment settings are generally kept the same as in the original papers. Detailed explanations are listed below for both baselines.

## H.1 IDC DETAILS

A multi-formation operation is proposed in IDC to increase the information contained in each sample. The multi-formation factor is set as 2 on CIFAR-10 and 3 on ImageNet, which is kept the same as the original implementation.

On SVHN and CIFAR-10 datasets, a 3-layer ConvNet [\(Gidaris & Komodakis, 2018\)](#page-10-15) is employed for distillation, while on ImageNet, ResNet-10 [\(He et al., 2016\)](#page-10-16) is utilized. After distillation, we conduct

validation procedures on ConvNet-3 for  $32 \times 32$  datasets and ResNet-10 for ImageNet, ensuring a fair and consistent basis for comparison.

## H.2 GLAD DETAILS

There are multiple different matching metrics presented in GLaD. Here we adopt gradient matching (DC in GLaD paper) in our experiments for two primary reasons. Firstly, gradient matching is deemed to be more practical when compared with alternative metrics. Secondly, gradient matching incorporates model updating, providing a convenient avenue for embedding the proposed distributionally robust optimization method.

On SVHN and CIFAR-10 datasets, similar to IDC, a 3-layer ConvNet [\(Gidaris & Komodakis, 2018\)](#page-10-15) is employed for distillation. A 5-layer ConvNet is adopted for ImageNet, which is kept the same as in the original paper. For better comparison, we adopt the validation protocol in IDC as it yields better performance. The results of baseline GLaD are also re-produced with the same protocol.

## H.3 DISTILLING ITERATION

Initially, the iteration randomly initializes a network according to the architecture setting for each baseline. Then the synthetic images are updated to match the gradients from the network. The network supplying the gradient for the images is updated following the image update. During the network updates, the robust objective proposed in this paper is employed for training. The network training is restricted to an early stage, using only 4,000 images for IDC and 1,000 images for GLaD. 100 steps of synthetic image update together with the network training forms an iteration for IDC, and 2 steps for GLaD. For CIFAR-10 and ImageNet subsets, we adopt 2,000 and 500 iterations to complete the distilling process, respectively.

<span id="page-23-0"></span>

# I BROADER IMPACTS

The primary objective of dataset distillation is to alleviate the storage and computational resource demands associated with training deep neural networks. This need becomes particularly pronounced in the era of foundational models. Dataset distillation endeavors to expedite environmental sustainability efforts. Our proposed method, viewed from this perspective, markedly diminishes the resources needed for the distillation process. We aspire to draw attention to practical dataset distillation methods within the computer vision community, thereby fostering the sustainable development of society. Further, this work does not involve direct ethical concerns. Our experiments utilize publicly available datasets, namely ImageNet, SVHN, and CIFAR-10. In forthcoming research, we are committed to addressing issues related to generation bias and diversity when constructing small surrogate datasets.
variable **z** associated with each instance of **x**. As in the case of the Gaussian mixture,  $\mathbf{z} = (z_1, \dots, z_K)^T$  is a binary K-dimensional variable having a single component equal to 1, with all other components equal to 0. We can then write the conditional distribution of **x**, given the latent variable, as

$$
p(\mathbf{x}|\mathbf{z}, \boldsymbol{\mu}) = \prod_{k=1}^{K} p(\mathbf{x}|\boldsymbol{\mu}_k)^{z_k}
$$
(9.52)

while the prior distribution for the latent variables is the same as for the mixture of Gaussians model, so that

$$
p(\mathbf{z}|\boldsymbol{\pi}) = \prod_{k=1}^{K} \pi_k^{z_k}.
$$
\n(9.53)

If we form the product of  $p(\mathbf{x}|\mathbf{z}, \boldsymbol{\mu})$  and  $p(\mathbf{z}|\boldsymbol{\pi})$  and then marginalize over **z**, then we *Exercise 9.14* recover (9.47).

> In order to derive the EM algorithm, we first write down the complete-data log likelihood function, which is given by

$$
\ln p(\mathbf{X}, \mathbf{Z} | \boldsymbol{\mu}, \boldsymbol{\pi}) = \sum_{n=1}^{N} \sum_{k=1}^{K} z_{nk} \left\{ \ln \pi_k + \sum_{i=1}^{D} \left[ x_{ni} \ln \mu_{ki} + (1 - x_{ni}) \ln(1 - \mu_{ki}) \right] \right\}
$$
(9.54)

where  $X = \{x_n\}$  and  $Z = \{z_n\}$ . Next we take the expectation of the complete-data log likelihood with respect to the posterior distribution of the latent variables to give

$$
\mathbb{E}_{\mathbf{Z}}[\ln p(\mathbf{X}, \mathbf{Z} | \boldsymbol{\mu}, \boldsymbol{\pi})] = \sum_{n=1}^{N} \sum_{k=1}^{K} \gamma(z_{nk}) \left\{ \ln \pi_k + \sum_{i=1}^{D} \left[ x_{ni} \ln \mu_{ki} + (1 - x_{ni}) \ln(1 - \mu_{ki}) \right] \right\} \quad (9.55)
$$

where  $\gamma(z_{nk}) = \mathbb{E}[z_{nk}]$  is the posterior probability, or responsibility, of component  $k$  given data point  $\mathbf{x}_n$ . In the E step, these responsibilities are evaluated using Bayes' theorem, which takes the form

$$
\gamma(z_{nk}) = \mathbb{E}[z_{nk}] = \frac{\sum_{z_{nk}} z_{nk} \left[ \pi_k p(\mathbf{x}_n | \boldsymbol{\mu}_k) \right]^{z_{nk}}}{\sum_{z_{nj}} \left[ \pi_j p(\mathbf{x}_n | \boldsymbol{\mu}_j) \right]^{z_{nj}}}
$$
$$
= \frac{\pi_k p(\mathbf{x}_n | \boldsymbol{\mu}_k)}{\sum_{j=1}^K \pi_j p(\mathbf{x}_n | \boldsymbol{\mu}_j)}.
$$
(9.56)

# **9.3. An Alternative View of EM 447**

If we consider the sum over n in  $(9.55)$ , we see that the responsibilities enter only through two terms, which can be written as

$$
N_k = \sum_{n=1}^{N} \gamma(z_{nk}) \tag{9.57}
$$

$$
\overline{\mathbf{x}}_k = \frac{1}{N_k} \sum_{n=1}^N \gamma(z_{nk}) \mathbf{x}_n
$$
\n(9.58)

where  $N_k$  is the effective number of data points associated with component k. In the M step, we maximize the expected complete-data log likelihood with respect to the parameters  $\mu_k$  and  $\pi$ . If we set the derivative of (9.55) with respect to  $\mu_k$  equal to *Exercise* 9.15 zero and rearrange the terms, we obtain

$$
\mu_k = \overline{\mathbf{x}}_k. \tag{9.59}
$$

We see that this sets the mean of component  $k$  equal to a weighted mean of the data, with weighting coefficients given by the responsibilities that component  $k$  takes for data points. For the maximization with respect to  $\pi_k$ , we need to introduce a Lagrange multiplier to enforce the constraint  $\sum_{k} \pi_k = 1$ . Following analogous *Exercise* 9.16 steps to those used for the mixture of Gaussians, we then obtain

$$
\pi_k = \frac{N_k}{N} \tag{9.60}
$$

which represents the intuitively reasonable result that the mixing coefficient for component  $k$  is given by the effective fraction of points in the data set explained by that component.

Note that in contrast to the mixture of Gaussians, there are no singularities in which the likelihood function goes to infinity. This can be seen by noting that the *Exercise 9.17* likelihood function is bounded above because  $0 \leq p(\mathbf{x}_n|\boldsymbol{\mu}_k) \leq 1$ . There exist singularities at which the likelihood function goes to zero, but these will not be found by EM provided it is not initialized to a pathological starting point, because the EM algorithm always increases the value of the likelihood function, until a local *Section 9.4* maximum is found. We illustrate the Bernoulli mixture model in Figure 9.10 by using it to model handwritten digits. Here the digit images have been turned into binary vectors by setting all elements whose values exceed 0.5 to 1 and setting the remaining elements to 0. We now fit a data set of  $N = 600$  such digits, comprising the digits '2', '3', and '4', with a mixture of  $K = 3$  Bernoulli distributions by running 10 iterations of the EM algorithm. The mixing coefficients were initialized to  $\pi_k = 1/K$ , and the parameters  $\mu_{ki}$  were set to random values chosen uniformly in the range (0.25, 0.75) and then normalized to satisfy the constraint that  $\sum_j \mu_{kj} = 1$ . We see that a mixture of 3 Bernoulli distributions is able to find the three clusters in the data set corresponding to the different digits.

> The conjugate prior for the parameters of a Bernoulli distribution is given by the beta distribution, and we have seen that a beta prior is equivalent to introducing

*Exercise 9.15*

*Exercise 9.17*

*Section 9.4*

Image /page/2/Figure/1 description: The image displays a grid of handwritten digits. The top row contains five distinct digits: 2, 4, 4, 3, and 4. The bottom row shows blurred versions of the digits 2, 4, 3, and 3, suggesting a comparison or a different representation of the same digits.

**Figure 9.10** Illustration of the Bernoulli mixture model in which the top row shows examples from the digits data set after converting the pixel values from grey scale to binary using a threshold of 0.5. On the bottom row the first three images show the parameters  $\mu_{ki}$  for each of the three components in the mixture model. As a comparison, we also fit the same data set using a single multivariate Bernoulli distribution, again using maximum likelihood. This amounts to simply averaging the counts in each pixel and is shown by the right-most image on the bottom row.

- *Section 2.1.1* additional effective observations of **x**. We can similarly introduce priors into the Bernoulli mixture model, and use EM to maximize the posterior probability distri-*Exercise 9.18* butions.
- It is straightforward to extend the analysis of Bernoulli mixtures to the case of *Exercise* 9.19 multinomial binary variables having  $M > 2$  states by making use of the discrete distribution (2.26). Again, we can introduce Dirichlet priors over the model parameters if desired.

### **9.3.4 EM for Bayesian linear regression**

As a third example of the application of EM, we return to the evidence approximation for Bayesian linear regression. In Section 3.5.2, we obtained the reestimation equations for the hyperparameters  $\alpha$  and  $\beta$  by evaluation of the evidence and then setting the derivatives of the resulting expression to zero. We now turn to an alternative approach for finding  $\alpha$  and  $\beta$  based on the EM algorithm. Recall that our goal is to maximize the evidence function  $p(\mathbf{t}|\alpha, \beta)$  given by (3.77) with respect to  $\alpha$  and  $\beta$ . Because the parameter vector **w** is marginalized out, we can regard it as a latent variable, and hence we can optimize this marginal likelihood function using EM. In the E step, we compute the posterior distribution of **w** given the current setting of the parameters  $\alpha$  and  $\beta$  and then use this to find the expected complete-data log likelihood. In the M step, we maximize this quantity with respect to  $\alpha$  and  $\beta$ . We have already derived the posterior distribution of **w** because this is given by (3.49). The complete-data log likelihood function is then given by

$$
\ln p(\mathbf{t}, \mathbf{w} | \alpha, \beta) = \ln p(\mathbf{t} | \mathbf{w}, \beta) + \ln p(\mathbf{w} | \alpha)
$$
\n(9.61)

where the likelihood  $p(\mathbf{t}|\mathbf{w}, \beta)$  and the prior  $p(\mathbf{w}|\alpha)$  are given by (3.10) and (3.52), respectively, and  $y(x, w)$  is given by (3.3). Taking the expectation with respect to the posterior distribution of **w** then gives

$$
\mathbb{E}[\ln p(\mathbf{t}, \mathbf{w} | \alpha, \beta)] = \frac{M}{2} \ln \left(\frac{\alpha}{2\pi}\right) - \frac{\alpha}{2} \mathbb{E}[\mathbf{w}^{\mathrm{T}} \mathbf{w}] + \frac{N}{2} \ln \left(\frac{\beta}{2\pi}\right)
$$
$$
-\frac{\beta}{2} \sum_{n=1}^{N} \mathbb{E}[(t_n - \mathbf{w}^{\mathrm{T}} \boldsymbol{\phi}_n)^2]. \tag{9.62}
$$

Setting the derivatives with respect to  $\alpha$  to zero, we obtain the M step re-estimation *Exercise 9.20* equation

$$
\alpha = \frac{M}{\mathbb{E}\left[\mathbf{w}^{\mathrm{T}}\mathbf{w}\right]} = \frac{M}{\mathbf{m}_{N}^{\mathrm{T}}\mathbf{m}_{N} + \text{Tr}(\mathbf{S}_{N})}.
$$
\n(9.63)

*Exercise* 9.21 An analogous result holds for  $\beta$ .

Note that this re-estimation equation takes a slightly different form from the corresponding result (3.92) derived by direct evaluation of the evidence function. However, they each involve computation and inversion (or eigen decomposition) of an  $M \times M$  matrix and hence will have comparable computational cost per iteration.

These two approaches to determining  $\alpha$  should of course converge to the same result (assuming they find the same local maximum of the evidence function). This can be verified by first noting that the quantity  $\gamma$  is defined by

$$
\gamma = M - \alpha \sum_{i=1}^{M} \frac{1}{\lambda_i + \alpha} = M - \alpha \text{Tr}(\mathbf{S}_N). \tag{9.64}
$$

At a stationary point of the evidence function, the re-estimation equation (3.92) will be self-consistently satisfied, and hence we can substitute for  $\gamma$  to give

$$
\alpha \mathbf{m}_N^{\mathrm{T}} \mathbf{m}_N = \gamma = M - \alpha \text{Tr}(\mathbf{S}_N)
$$
\n(9.65)

and solving for  $\alpha$  we obtain (9.63), which is precisely the EM re-estimation equation.

As a final example, we consider a closely related model, namely the relevance vector machine for regression discussed in Section 7.2.1. There we used direct maximization of the marginal likelihood to derive re-estimation equations for the hyperparameters  $\alpha$  and  $\beta$ . Here we consider an alternative approach in which we view the weight vector **w** as a latent variable and apply the EM algorithm. The E step involves finding the posterior distribution over the weights, and this is given by (7.81). In the M step we maximize the expected complete-data log likelihood, which is defined by

$$
\mathbb{E}_{\mathbf{w}}\left[\ln p(\mathbf{t}|\mathbf{X},\mathbf{w},\beta)p(\mathbf{w}|\alpha)\right]
$$
 (9.66)

where the expectation is taken with respect to the posterior distribution computed using the 'old' parameter values. To compute the new parameter values we maximize *Exercise* 9.22 with respect to  $\alpha$  and  $\beta$  to give

*Exercise* 9.22

$$
\alpha_i^{\text{new}} = \frac{1}{m_i^2 + \Sigma_{ii}} \tag{9.67}
$$

$$
(\beta^{\text{new}})^{-1} = \frac{\Vert \mathbf{t} - \Phi \mathbf{m}_N \Vert^2 + \beta^{-1} \sum_i \gamma_i}{N}
$$
(9.68)

*Exercise 9.23* maxmization.

These re-estimation equations are formally equivalent to those obtained by direct

# **9.4. The EM Algorithm in General**

The *expectation maximization* algorithm, or EM algorithm, is a general technique for finding maximum likelihood solutions for probabilistic models having latent variables (Dempster *et al.*, 1977; McLachlan and Krishnan, 1997). Here we give a very general treatment of the EM algorithm and in the process provide a proof that the EM algorithm derived heuristically in Sections 9.2 and 9.3 for Gaussian mixtures does indeed maximize the likelihood function (Csiszar and Tusnady, 1984; Hathaway, 1986; Neal and Hinton, 1999). Our discussion will also form the basis for the *Section 10.1* derivation of the variational inference framework.

## *Section 10.1*

Consider a probabilistic model in which we collectively denote all of the observed variables by **X** and all of the hidden variables by **Z**. The joint distribution  $p(X, Z | \theta)$  is governed by a set of parameters denoted  $\theta$ . Our goal is to maximize the likelihood function that is given by

$$
p(\mathbf{X}|\boldsymbol{\theta}) = \sum_{\mathbf{Z}} p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta}).
$$
 (9.69)

Here we are assuming **Z** is discrete, although the discussion is identical if **Z** comprises continuous variables or a combination of discrete and continuous variables, with summation replaced by integration as appropriate.

We shall suppose that direct optimization of  $p(X|\theta)$  is difficult, but that optimization of the complete-data likelihood function  $p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta})$  is significantly easier. Next we introduce a distribution  $q(\mathbf{Z})$  defined over the latent variables, and we observe that, for any choice of  $q(\mathbf{Z})$ , the following decomposition holds

$$
\ln p(\mathbf{X}|\boldsymbol{\theta}) = \mathcal{L}(q, \boldsymbol{\theta}) + \mathrm{KL}(q||p)
$$
\n(9.70)

where we have defined

$$
\mathcal{L}(q, \theta) = \sum_{\mathbf{Z}} q(\mathbf{Z}) \ln \left\{ \frac{p(\mathbf{X}, \mathbf{Z} | \theta)}{q(\mathbf{Z})} \right\}
$$
(9.71)

$$
KL(q||p) = -\sum_{\mathbf{Z}} q(\mathbf{Z}) \ln \left\{ \frac{p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta})}{q(\mathbf{Z})} \right\}.
$$
 (9.72)

Note that  $\mathcal{L}(q, \theta)$  is a functional (see Appendix D for a discussion of functionals) of the distribution  $q(\mathbf{Z})$ , and a function of the parameters  $\boldsymbol{\theta}$ . It is worth studying

# **9.4. The EM Algorithm in General 451**

**Figure 9.11** Illustration of the decomposition given by (9.70), which holds for any choice of distribution  $q(\mathbf{Z})$ . Because the Kullback-Leibler divergence satisfies  $KL(q||p) \geqslant 0$ , we see that the quantity  $\mathcal{L}(q, \theta)$  is a lower bound on the log likelihood function  $\ln p(\mathbf{X}|\boldsymbol{\theta})$ .

Image /page/5/Figure/2 description: The image displays a diagram illustrating the relationship between KL divergence, a loss function, and the log-likelihood. A red horizontal line is at the top, representing a baseline. Below it, a blue horizontal line is positioned, indicating a lower value. An arrow points from the red line down to the blue line, labeled KL(q||p). Another arrow points from the blue line down to a gray horizontal line at the bottom, labeled L(q, θ). A third arrow originates from the red line and points directly down to the gray line at the bottom, labeled ln p(X|θ). This visual representation suggests that the log-likelihood is the sum of the KL divergence and the loss function.

carefully the forms of the expressions (9.71) and (9.72), and in particular noting that they differ in sign and also that  $\mathcal{L}(q, \theta)$  contains the joint distribution of **X** and **Z** while  $KL(q||p)$  contains the conditional distribution of **Z** given **X**. To verify the *Exercise* 9.24 decomposition (9.70), we first make use of the product rule of probability to give

$$
\ln p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta}) = \ln p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}) + \ln p(\mathbf{X}|\boldsymbol{\theta})
$$
\n(9.73)

which we then substitute into the expression for  $\mathcal{L}(q, \theta)$ . This gives rise to two terms, one of which cancels  $KL(q||p)$  while the other gives the required log likelihood ln  $p(X|\theta)$  after noting that  $q(Z)$  is a normalized distribution that sums to 1.

From (9.72), we see that  $KL(q||p)$  is the Kullback-Leibler divergence between  $q(\mathbf{Z})$  and the posterior distribution  $p(\mathbf{Z}|\mathbf{X}, \theta)$ . Recall that the Kullback-Leibler di-*Section 1.6.1* vergence satisfies  $KL(q||p) \ge 0$ , with equality if, and only if,  $q(\mathbf{Z}) = p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta})$ . It therefore follows from (9.70) that  $\Gamma(q, \boldsymbol{\theta}) \le \ln p(\mathbf{X}|\boldsymbol{\theta})$  in other words that  $\Gamma(q, \boldsymbol{\theta})$ therefore follows from (9.70) that  $\mathcal{L}(q, \theta) \leq \ln p(\mathbf{X}|\theta)$ , in other words that  $\mathcal{L}(q, \theta)$ is a lower bound on  $\ln p(\mathbf{X}|\boldsymbol{\theta})$ . The decomposition (9.70) is illustrated in Figure 9.11.

> The EM algorithm is a two-stage iterative optimization technique for finding maximum likelihood solutions. We can use the decomposition (9.70) to define the EM algorithm and to demonstrate that it does indeed maximize the log likelihood. Suppose that the current value of the parameter vector is  $\theta$ <sup>old</sup>. In the E step, the lower bound  $\mathcal{L}(q, \theta^{\text{old}})$  is maximized with respect to  $q(\mathbf{Z})$  while holding  $\theta^{\text{old}}$  fixed. The solution to this maximization problem is easily seen by noting that the value of  $\ln p(\mathbf{X}|\boldsymbol{\theta}^{\text{old}})$  does not depend on  $q(\mathbf{Z})$  and so the largest value of  $\mathcal{L}(q, \boldsymbol{\theta}^{\text{old}})$  will occur when the Kullback-Leibler divergence vanishes, in other words when  $q(\mathbf{Z})$  is equal to the posterior distribution  $p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}^{\text{old}})$ . In this case, the lower bound will equal the log likelihood, as illustrated in Figure 9.12.

> In the subsequent M step, the distribution  $q(\mathbf{Z})$  is held fixed and the lower bound  $\mathcal{L}(q, \theta)$  is maximized with respect to  $\theta$  to give some new value  $\theta^{\text{new}}$ . This will cause the lower bound  $\mathcal L$  to increase (unless it is already at a maximum), which will necessarily cause the corresponding log likelihood function to increase. Because the distribution  $q$  is determined using the old parameter values rather than the new values and is held fixed during the M step, it will not equal the new posterior distribution  $p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}^{\text{new}})$ , and hence there will be a nonzero KL divergence. The increase in the log likelihood function is therefore greater than the increase in the lower bound, as

*Exercise 9.24*

**Figure 9.12** Illustration of the E step of  $KL(q||p) = 0$ <br>the EM elgorithm The s the EM algorithm. The  $q$ distribution is set equal to the posterior distribution for the current parameter values  $\theta$ <sup>old</sup>, causing the lower bound to move up to the same value as the log likelihood function, with the KL

Image /page/6/Figure/2 description: This image illustrates the E step of the EM algorithm. It shows that the q distribution is set equal to the posterior distribution for the current parameter values \(\\theta^{\\text{old}}\\), causing the lower bound to move up to the same value as the log-likelihood function, with the KL divergence vanishing. The diagram shows two horizontal lines representing \( \text{KL}(q||p) = 0 \) and \( \ln p(\mathbf{X}| \theta^{\\text{old}}) \). Below these, a dashed blue line represents \( \mathcal{L}(q, \theta^{\\text{old}}) \). Vertical arrows indicate the relationships between these values, with a blue arrow showing the KL divergence becoming zero and a black arrow showing the lower bound equaling the log-likelihood.

shown in Figure 9.13. If we substitute  $q(\mathbf{Z}) = p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}^{\text{old}})$  into (9.71), we see that, after the E step, the lower bound takes the form

$$
\mathcal{L}(q, \theta) = \sum_{\mathbf{Z}} p(\mathbf{Z} | \mathbf{X}, \theta^{\text{old}}) \ln p(\mathbf{X}, \mathbf{Z} | \theta) - \sum_{\mathbf{Z}} p(\mathbf{Z} | \mathbf{X}, \theta^{\text{old}}) \ln p(\mathbf{Z} | \mathbf{X}, \theta^{\text{old}}) \\ = Q(\theta, \theta^{\text{old}}) + \text{const} \quad (9.74)
$$

where the constant is simply the negative entropy of the  $q$  distribution and is therefore independent of  $\theta$ . Thus in the M step, the quantity that is being maximized is the expectation of the complete-data log likelihood, as we saw earlier in the case of mixtures of Gaussians. Note that the variable  $\theta$  over which we are optimizing appears only inside the logarithm. If the joint distribution  $p(\mathbf{Z}, \mathbf{X}|\boldsymbol{\theta})$  comprises a member of the exponential family, or a product of such members, then we see that the logarithm will cancel the exponential and lead to an M step that will be typically much simpler than the maximization of the corresponding incomplete-data log likelihood function  $p(\mathbf{X}|\boldsymbol{\theta}).$ 

The operation of the EM algorithm can also be viewed in the space of parameters, as illustrated schematically in Figure 9.14. Here the red curve depicts the (in-

**Figure 9.13** Illustration of the M step of the EM algorithm. The distribution  $q(\mathbf{Z})$   $KL(q||p)$ is held fixed and the lower bound  $\mathcal{L}(q, \theta)$  is maximized with respect to the parameter vector *θ* to give a revised value *θ*new. Because the KL divergence is nonnegative, this causes the log likelihood  $\ln p(\mathbf{X}|\boldsymbol{\theta})$ to increase by at least as much as the lower bound does.

Image /page/6/Figure/8 description: This is a diagram illustrating the relationship between KL divergence and likelihood. The top horizontal line is red, and the second horizontal line is blue. A vertical arrow labeled KL(q||p) points from the red line down to the blue line. A dashed blue line is below the blue line, and a dashed red line is below the red line. A vertical arrow labeled L(q, θnew) points from the blue line down to a horizontal gray line at the bottom. A vertical arrow labeled ln p(X|θnew) points from the red line down to the gray line. A short red arrow points from the dashed red line up to the red line, and a short blue arrow points from the dashed blue line up to the blue line.

# **9.4. The EM Algorithm in General 453**

**Figure 9.14** The EM algorithm involves alternately computing a lower bound on the log likelihood for the current parameter values and then maximizing this bound to obtain the new parameter values. See the text for a full discussion.

Image /page/7/Figure/2 description: A graph shows three curves plotted against theta. The x-axis is labeled with theta-old and theta-new. The top curve, labeled "ln p(X|theta)", is red and has a peak around theta-new. The middle curve is green and also peaks around theta-new. The bottom curve, labeled "L(q, theta)", is blue and peaks between theta-old and theta-new. Two dotted vertical lines indicate the positions of theta-old and theta-new on the x-axis.

complete data) log likelihood function whose value we wish to maximize. We start with some initial parameter value  $\theta$ <sup>old</sup>, and in the first E step we evaluate the posterior distribution over latent variables, which gives rise to a lower bound  $\mathcal{L}(\theta, \theta^{\text{(old)}})$ whose value equals the log likelihood at  $\theta^{\text{(old)}}$ , as shown by the blue curve. Note that the bound makes a tangential contact with the log likelihood at  $\theta^{\text{(old)}}$ , so that both *Exercise 9.25* curves have the same gradient. This bound is a convex function having a unique maximum (for mixture components from the exponential family). In the M step, the bound is maximized giving the value  $\theta^{\text{(new)}}$ , which gives a larger value of log likelihood than  $\theta^{\text{(old)}}$ . The subsequent E step then constructs a bound that is tangential at *θ*(new) as shown by the green curve.

> For the particular case of an independent, identically distributed data set, **X** will comprise N data points  $\{x_n\}$  while **Z** will comprise N corresponding latent variables  $\{\mathbf{z}_n\}$ , where  $n = 1, \ldots, N$ . From the independence assumption, we have  $p(\mathbf{X}, \mathbf{Z}) = \prod_{n} p(\mathbf{x}_n, \mathbf{z}_n)$  and, by marginalizing over the  $\{\mathbf{z}_n\}$  we have  $p(\mathbf{X}) = \prod_{n} p(\mathbf{x})$ . Using the sum and product rules, we see that the posterior probability  $\prod_n p(\mathbf{x}_n)$ . Using the sum and product rules, we see that the posterior probability<br> $\prod_n p(\mathbf{x}_n)$ . Using the sum and product rules, we see that the posterior probability<br>that is evaluated in the E step takes the form that is evaluated in the E step takes the form

$$
p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}) = \frac{p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta})}{\sum_{\mathbf{Z}} p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta})} = \frac{\prod_{n=1}^{N} p(\mathbf{x}_n, \mathbf{z}_n | \boldsymbol{\theta})}{\sum_{\mathbf{Z}} \prod_{n=1}^{N} p(\mathbf{x}_n, \mathbf{z}_n | \boldsymbol{\theta})} = \prod_{n=1}^{N} p(\mathbf{z}_n | \mathbf{x}_n, \boldsymbol{\theta}) \quad (9.75)
$$

and so the posterior distribution also factorizes with respect to  $n$ . In the case of the Gaussian mixture model this simply says that the responsibility that each of the mixture components takes for a particular data point  $\mathbf{x}_n$  depends only on the value of  $x_n$  and on the parameters  $\theta$  of the mixture components, not on the values of the other data points.

We have seen that both the E and the M steps of the EM algorithm are increasing the value of a well-defined bound on the log likelihood function and that the

complete EM cycle will change the model parameters in such a way as to cause the log likelihood to increase (unless it is already at a maximum, in which case the parameters remain unchanged).

We can also use the EM algorithm to maximize the posterior distribution  $p(\theta|\mathbf{X})$ for models in which we have introduced a prior  $p(\theta)$  over the parameters. To see this, we note that as a function of  $\theta$ , we have  $p(\theta|\mathbf{X}) = p(\theta, \mathbf{X})/p(\mathbf{X})$  and so

$$
\ln p(\boldsymbol{\theta}|\mathbf{X}) = \ln p(\boldsymbol{\theta}, \mathbf{X}) - \ln p(\mathbf{X}).
$$
\n(9.76)

Making use of the decomposition (9.70), we have

$$
\ln p(\boldsymbol{\theta}|\mathbf{X}) = \mathcal{L}(q, \boldsymbol{\theta}) + \mathrm{KL}(q||p) + \ln p(\boldsymbol{\theta}) - \ln p(\mathbf{X})
$$
  
\n
$$
\geqslant \mathcal{L}(q, \boldsymbol{\theta}) + \ln p(\boldsymbol{\theta}) - \ln p(\mathbf{X}). \tag{9.77}
$$

where  $\ln p(\mathbf{X})$  is a constant. We can again optimize the right-hand side alternately with respect to q and  $\theta$ . The optimization with respect to q gives rise to the same Estep equations as for the standard EM algorithm, because q only appears in  $\mathcal{L}(q, \theta)$ . The M-step equations are modified through the introduction of the prior term  $\ln p(\theta)$ , which typically requires only a small modification to the standard maximum likelihood M-step equations.

The EM algorithm breaks down the potentially difficult problem of maximizing the likelihood function into two stages, the E step and the M step, each of which will often prove simpler to implement. Nevertheless, for complex models it may be the case that either the E step or the M step, or indeed both, remain intractable. This leads to two possible extensions of the EM algorithm, as follows.

The *generalized EM*, or *GEM*, algorithm addresses the problem of an intractable M step. Instead of aiming to maximize  $\mathcal{L}(q, \theta)$  with respect to  $\theta$ , it seeks instead to change the parameters in such a way as to increase its value. Again, because  $\mathcal{L}(q, \theta)$  is a lower bound on the log likelihood function, each complete EM cycle of the GEM algorithm is guaranteed to increase the value of the log likelihood (unless the parameters already correspond to a local maximum). One way to exploit the GEM approach would be to use one of the nonlinear optimization strategies, such as the conjugate gradients algorithm, during the M step. Another form of GEM algorithm, known as the *expectation conditional maximization*, or ECM, algorithm, involves making several constrained optimizations within each M step (Meng and Rubin, 1993). For instance, the parameters might be partitioned into groups, and the M step is broken down into multiple steps each of which involves optimizing one of the subset with the remainder held fixed.

We can similarly generalize the E step of the EM algorithm by performing a partial, rather than complete, optimization of  $\mathcal{L}(q, \theta)$  with respect to  $q(\mathbf{Z})$  (Neal and Hinton, 1999). As we have seen, for any given value of  $\theta$  there is a unique maximum of  $\mathcal{L}(q, \theta)$  with respect to  $q(\mathbf{Z})$  that corresponds to the posterior distribution  $q_{\theta}(\mathbf{Z}) =$  $p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta})$  and that for this choice of  $q(\mathbf{Z})$  the bound  $\mathcal{L}(q, \boldsymbol{\theta})$  is equal to the log likelihood function  $\ln p(\mathbf{X}|\boldsymbol{\theta})$ . It follows that any algorithm that converges to the global maximum of  $\mathcal{L}(q, \theta)$  will find a value of  $\theta$  that is also a global maximum of the log likelihood  $\ln p(\mathbf{X}|\boldsymbol{\theta})$ . Provided  $p(\mathbf{X}, \mathbf{Z}|\boldsymbol{\theta})$  is a continuous function of  $\boldsymbol{\theta}$  then, by continuity, any local maximum of  $\mathcal{L}(q, \theta)$  will also be a local maximum of  $\ln p(\mathbf{X}|\boldsymbol{\theta}).$ 

Consider the case of N independent data points  $x_1, \ldots, x_N$  with corresponding latent variables  $z_1, \ldots, z_N$ . The joint distribution  $p(X, Z | \theta)$  factorizes over the data points, and this structure can be exploited in an incremental form of EM in which at each EM cycle only one data point is processed at a time. In the E step, instead of recomputing the responsibilities for all of the data points, we just re-evaluate the responsibilities for one data point. It might appear that the subsequent M step would require computation involving the responsibilities for all of the data points. However, if the mixture components are members of the exponential family, then the responsibilities enter only through simple sufficient statistics, and these can be updated efficiently. Consider, for instance, the case of a Gaussian mixture, and suppose we perform an update for data point  $m$  in which the corresponding old and new values of the responsibilities are denoted  $\gamma^{old}(z_{mk})$  and  $\gamma^{new}(z_{mk})$ . In the M step, the required sufficient statistics can be updated incrementally. For instance, for the *Exercise* 9.26 means the sufficient statistics are defined by (9.17) and (9.18) from which we obtain

*Exercise 9.26*

$$
\mu_k^{\text{new}} = \mu_k^{\text{old}} + \left(\frac{\gamma^{\text{new}}(z_{mk}) - \gamma^{\text{old}}(z_{mk})}{N_k^{\text{new}}}\right) \left(\mathbf{x}_m - \mu_k^{\text{old}}\right) \tag{9.78}
$$

together with

$$
N_k^{\text{new}} = N_k^{\text{old}} + \gamma^{\text{new}}(z_{mk}) - \gamma^{\text{old}}(z_{mk}). \tag{9.79}
$$

The corresponding results for the covariances and the mixing coefficients are analogous.

Thus both the E step and the M step take fixed time that is independent of the total number of data points. Because the parameters are revised after each data point, rather than waiting until after the whole data set is processed, this incremental version can converge faster than the batch version. Each E or M step in this incremental algorithm is increasing the value of  $\mathcal{L}(q, \theta)$  and, as we have shown above, if the algorithm converges to a local (or global) maximum of  $\mathcal{L}(q, \theta)$ , this will correspond to a local (or global) maximum of the log likelihood function  $\ln p(\mathbf{X}|\boldsymbol{\theta})$ .

# **Exercises**

- **9.1** ( $\star$ ) **www** Consider the K-means algorithm discussed in Section 9.1. Show that as a consequence of there being a finite number of possible assignments for the set of discrete indicator variables  $r_{nk}$ , and that for each such assignment there is a unique optimum for the  $\{\mu_k\}$ , the K-means algorithm must converge after a finite number of iterations.
- **9.2** ( $\star$ ) Apply the Robbins-Monro sequential estimation procedure described in Section 2.3.5 to the problem of finding the roots of the regression function given by the derivatives of J in (9.1) with respect to  $\mu_k$ . Show that this leads to a stochastic K-means algorithm in which, for each data point  $\mathbf{x}_n$ , the nearest prototype  $\mu_k$  is updated using (9.5).

- **9.3** (\*) **WWW** Consider a Gaussian mixture model in which the marginal distribution  $p(\mathbf{z})$  for the latent variable is given by (9.10), and the conditional distribution  $p(\mathbf{x}|\mathbf{z})$ for the observed variable is given by (9.11). Show that the marginal distribution  $p(x)$ , obtained by summing  $p(z)p(x|z)$  over all possible values of z, is a Gaussian mixture of the form (9.7).
- **9.4**  $(\star)$  Suppose we wish to use the EM algorithm to maximize the posterior distribution over parameters  $p(\theta|\mathbf{X})$  for a model containing latent variables, where **X** is the observed data set. Show that the E step remains the same as in the maximum likelihood case, whereas in the M step the quantity to be maximized is given by  $\mathcal{Q}(\theta, \theta^{\text{old}}) + \ln p(\theta)$  where  $\mathcal{Q}(\theta, \theta^{\text{old}})$  is defined by (9.30).
- **9.5**  $(\star)$  Consider the directed graph for a Gaussian mixture model shown in Figure 9.6. By making use of the d-separation criterion discussed in Section 8.2, show that the posterior distribution of the latent variables factorizes with respect to the different data points so that

$$
p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\mu}, \boldsymbol{\Sigma}, \boldsymbol{\pi}) = \prod_{n=1}^{N} p(\mathbf{z}_n | \mathbf{x}_n, \boldsymbol{\mu}, \boldsymbol{\Sigma}, \boldsymbol{\pi}).
$$
 (9.80)

- **9.6**  $(\star \star)$  Consider a special case of a Gaussian mixture model in which the covariance matrices  $\Sigma_k$  of the components are all constrained to have a common value **Σ**. Derive the EM equations for maximizing the likelihood function under such a model.
- **9.7** ( $\star$ ) **www** Verify that maximization of the complete-data log likelihood (9.36) for a Gaussian mixture model leads to the result that the means and covariances of each component are fitted independently to the corresponding group of data points, and the mixing coefficients are given by the fractions of points in each group.
- **9.8** ( $\star$ ) **www** Show that if we maximize (9.40) with respect to  $\mu_k$  while keeping the responsibilities  $\gamma(z_{nk})$  fixed, we obtain the closed form solution given by (9.17).
- **9.9** ( $\star$ ) Show that if we maximize (9.40) with respect to  $\Sigma_k$  and  $\pi_k$  while keeping the responsibilities  $\gamma(z_{nk})$  fixed, we obtain the closed form solutions given by (9.19) and (9.22).
- **9.10**  $(\star \star)$  Consider a density model given by a mixture distribution

$$
p(\mathbf{x}) = \sum_{k=1}^{K} \pi_k p(\mathbf{x}|k)
$$
\n(9.81)

and suppose that we partition the vector **x** into two parts so that  $\mathbf{x} = (\mathbf{x}_a, \mathbf{x}_b)$ . Show that the conditional density  $p(\mathbf{x}_b|\mathbf{x}_a)$  is itself a mixture distribution and find expressions for the mixing coefficients and for the component densities.

- **9.11**  $(\star)$  In Section 9.3.2, we obtained a relationship between K means and EM for Gaussian mixtures by considering a mixture model in which all components have covariance  $\epsilon I$ . Show that in the limit  $\epsilon \to 0$ , maximizing the expected completedata log likelihood for this model, given by (9.40), is equivalent to minimizing the distortion measure  $J$  for the  $K$ -means algorithm given by (9.1).
- **9.12** ( $\star$ ) **www** Consider a mixture distribution of the form

$$
p(\mathbf{x}) = \sum_{k=1}^{K} \pi_k p(\mathbf{x}|k)
$$
\n(9.82)

where the elements of **x** could be discrete or continuous or a combination of these. Denote the mean and covariance of  $p(\mathbf{x}|k)$  by  $\mu_k$  and  $\Sigma_k$ , respectively. Show that the mean and covariance of the mixture distribution are given by (9.49) and (9.50).

**9.13**  $(\star \star)$  Using the re-estimation equations for the EM algorithm, show that a mixture of Bernoulli distributions, with its parameters set to values corresponding to a maximum of the likelihood function, has the property that

$$
\mathbb{E}[\mathbf{x}] = \frac{1}{N} \sum_{n=1}^{N} \mathbf{x}_n \equiv \overline{\mathbf{x}}.\tag{9.83}
$$

Hence show that if the parameters of this model are initialized such that all components have the same mean  $\mu_k = \hat{\mu}$  for  $k = 1, ..., K$ , then the EM algorithm will converge after one iteration, for any choice of the initial mixing coefficients, and that this solution has the property  $\mu_k = \bar{x}$ . Note that this represents a degenerate case of the mixture model in which all of the components are identical, and in practice we try to avoid such solutions by using an appropriate initialization.

- **9.14**  $(\star)$  Consider the joint distribution of latent and observed variables for the Bernoulli distribution obtained by forming the product of  $p(\mathbf{x}|\mathbf{z}, \boldsymbol{\mu})$  given by (9.52) and  $p(\mathbf{z}|\boldsymbol{\pi})$ given by (9.53). Show that if we marginalize this joint distribution with respect to **z**, then we obtain (9.47).
- **9.15**  $(\star)$  **www** Show that if we maximize the expected complete-data log likelihood function (9.55) for a mixture of Bernoulli distributions with respect to  $\mu_k$ , we obtain the M step equation (9.59).
- **9.16**  $(\star)$  Show that if we maximize the expected complete-data log likelihood function (9.55) for a mixture of Bernoulli distributions with respect to the mixing coefficients  $\pi_k$ , using a Lagrange multiplier to enforce the summation constraint, we obtain the M step equation (9.60).
- **9.17** ( $\star$ ) **www** Show that as a consequence of the constraint  $0 \leq p(\mathbf{x}_n|\boldsymbol{\mu}_k) \leq 1$  for the discrete variable  $x_n$ , the incomplete-data log likelihood function for a mixture of Bernoulli distributions is bounded above, and hence that there are no singularities for which the likelihood goes to infinity.

- **9.18**  $(\star \star)$  Consider a Bernoulli mixture model as discussed in Section 9.3.3, together with a prior distribution  $p(\mu_k|a_k, b_k)$  over each of the parameter vectors  $\mu_k$  given by the beta distribution (2.13), and a Dirichlet prior  $p(\pi|\alpha)$  given by (2.38). Derive the EM algorithm for maximizing the posterior probability  $p(\mu, \pi | X)$ .
- **9.19**  $(\star \star)$  Consider a D-dimensional variable **x** each of whose components i is itself a multinomial variable of degree M so that **x** is a binary vector with components  $x_{ij}$ where  $i = 1, \ldots, D$  and  $j = 1, \ldots, M$ , subject to the constraint that  $\sum_{j} x_{ij} = 1$  for all i. Suppose that the distribution of these variables is described by a mixture of the discrete multinomial distributions considered in Section 2.2 so that

$$
p(\mathbf{x}) = \sum_{k=1}^{K} \pi_k p(\mathbf{x} | \boldsymbol{\mu}_k)
$$
\n(9.84)

where

$$
p(\mathbf{x}|\boldsymbol{\mu}_k) = \prod_{i=1}^{D} \prod_{j=1}^{M} \mu_{kij}^{x_{ij}}.
$$
 (9.85)

The parameters  $\mu_{kij}$  represent the probabilities  $p(x_{ij} = 1 | \mu_k)$  and must satisfy  $0 \le \mu_{kij} \le 1$  together with the constraint  $\sum_j \mu_{kij} = 1$  for all values of k and i. Given an observed data set  $\{x_n\}$ , where  $n = 1, \ldots, N$ , derive the E and M step equations of the EM algorithm for optimizing the mixing coefficients  $\pi_k$  and the component parameters  $\mu_{kij}$  of this distribution by maximum likelihood.

- **9.20** ( $\star$ ) **WWW** Show that maximization of the expected complete-data log likelihood function (9.62) for the Bayesian linear regression model leads to the M step reestimation result (9.63) for  $\alpha$ .
- **9.21**  $(\star \star)$  Using the evidence framework of Section 3.5, derive the M-step re-estimation equations for the parameter  $\beta$  in the Bayesian linear regression model, analogous to the result (9.63) for  $\alpha$ .
- **9.22**  $(\star \star)$  By maximization of the expected complete-data log likelihood defined by (9.66), derive the M step equations (9.67) and (9.68) for re-estimating the hyperparameters of the relevance vector machine for regression.
- **9.23**  $(\star \star)$  **www** In Section 7.2.1 we used direct maximization of the marginal likelihood to derive the re-estimation equations (7.87) and (7.88) for finding values of the hyperparameters  $\alpha$  and  $\beta$  for the regression RVM. Similarly, in Section 9.3.4 we used the EM algorithm to maximize the same marginal likelihood, giving the re-estimation equations (9.67) and (9.68). Show that these two sets of re-estimation equations are formally equivalent.
- **9.24** ( $\star$ ) Verify the relation (9.70) in which  $\mathcal{L}(q, \theta)$  and KL(q||p) are defined by (9.71) and (9.72), respectively.

- **9.25** ( $\star$ ) **www** Show that the lower bound  $\mathcal{L}(q, \theta)$  given by (9.71), with  $q(\mathbf{Z}) = p(\mathbf{Z}|\mathbf{X}, \mathbf{q}^{(\text{old})})$  has the same gradient with respect to  $\theta$  as the log likelihood function  $p(\mathbf{Z}|\mathbf{X}, \boldsymbol{\theta}^{(\text{old})})$ , has the same gradient with respect to  $\boldsymbol{\theta}$  as the log likelihood function ln  $p(\mathbf{X}|\boldsymbol{\theta})$  at the point  $\boldsymbol{\theta} = \boldsymbol{\theta}^{\text{(old)}}$ .
- **9.26** ( $\star$ ) **www** Consider the incremental form of the EM algorithm for a mixture of Gaussians, in which the responsibilities are recomputed only for a specific data point  $\mathbf{x}_m$ . Starting from the M-step formulae (9.17) and (9.18), derive the results (9.78) and (9.79) for updating the component means.
- **9.27**  $(\star \star)$  Derive M-step formulae for updating the covariance matrices and mixing coefficients in a Gaussian mixture model when the responsibilities are updated incrementally, analogous to the result (9.78) for updating the means.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
# CAN PRE-TRAINED MODELS ASSIST IN DATASET DIS-TILLATION?

<PERSON><sup>1</sup>; <PERSON><PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><PERSON><sup>3</sup>, <PERSON><PERSON><PERSON><sup>2</sup>, <PERSON><PERSON><sup>4</sup>, <PERSON><PERSON><PERSON> $^1$ , <PERSON> $^1$ † <PERSON> $^4$ ‡ <PERSON> $^4$ †

<sup>1</sup>Zhejiang University of Technology <sup>2</sup>University of Electronic Science and Technology of China <sup>3</sup>Zhejiang University <sup>4</sup>National University of Singapore

# ABSTRACT

Dataset Distillation (DD) is a prominent technique that encapsulates knowledge from a large-scale original dataset into a small synthetic dataset for efficient training. Meanwhile, Pre-trained Models (PTMs) function as knowledge repositories, containing extensive information from the original dataset. This naturally raises a question: Can PTMs effectively transfer knowledge to synthetic datasets, guiding DD accurately? To this end, we conduct preliminary experiments, confirming the contribution of PTMs to DD. Afterwards, we systematically study different options in PTMs, including initialization parameters, model architecture, training epoch and domain knowledge, revealing that: 1) Increasing model diversity enhances the performance of synthetic datasets; 2) Sub-optimal models can also assist in DD and outperform well-trained ones in certain cases; 3) Domain-specific PTMs are not mandatory for DD, but a reasonable domain match is crucial. Finally, by selecting optimal options, we significantly improve the cross-architecture generalization over baseline DD methods. We hope our work will facilitate researchers to develop better DD techniques. Our code is available at <https://github.com/yaolu-zjut/DDInterpreter>.

## 1 INTRODUCTION

Dataset Distillation (DD) condenses a large-scale, real-world dataset into a small, synthetic one such that models trained on the latter yield comparable performance [\(Wang et al.,](#page-11-0) [2018;](#page-11-0) [Yu et al.,](#page-11-1) [2023\)](#page-11-1). Thanks to its extremely high compression ratio and strong performance, DD has become a mainstream approach for dataset compression [\(Lei & Tao,](#page-10-0) [2023\)](#page-10-0). Numerous follow-ups have been carried out in this research line, such as designing better optimization algorithms [\(Nguyen et al.,](#page-10-1) [2021;](#page-10-1) [Cazenavette et al.,](#page-9-0) [2022;](#page-9-0) [Wang et al.,](#page-11-2) [2022;](#page-11-2) [Kim et al.,](#page-10-2) [2022;](#page-10-2) [Jin et al.,](#page-10-3) [2022;](#page-10-3) [Dong et al.,](#page-9-1) [2022\)](#page-9-1) as well as extending the application of DD [\(Zhao & Bilen,](#page-12-0) [2021;](#page-12-0) [Xiong et al.,](#page-11-3) [2022;](#page-11-3) [Song et al.,](#page-11-4) [2022;](#page-11-4) [Zhou et al.,](#page-12-1) [2022;](#page-12-1) [Zhao & Bilen,](#page-12-2) [2023\)](#page-12-2).

In current DD methods [\(Zhao et al.,](#page-12-3) [2021;](#page-12-3) [Zhao & Bilen,](#page-12-0) [2021;](#page-12-0) [Wang et al.,](#page-11-2) [2022;](#page-11-2) [Cazenavette et al.,](#page-9-2) [2023\)](#page-9-2), two key steps are involved: training models and calculating the matching loss between the original dataset and synthetic dataset using these models. These two steps are performed alternately in an iterative loop to optimize the synthetic dataset [\(Yu et al.,](#page-11-1)  $2023$ ). Essentially, the step of training models in DD can be viewed as extracting knowledge from the original dataset. Given that Pre-trained Models (In this paper, "Pre-trained Models" refers to models trained on the original dataset.) (PTMs) are ready-made knowledge repositories that contain extensive information from the original dataset, a natural question arises: *Can pre-trained models assist in dataset distillation?*

With this question in mind, we conduct a series of experiments. Firstly, we introduce a plug-and-play loss term, *i.e.*, **CLoM** (short for **Classification Loss of pre-trained Model**). By leveraging PTMs as supervision signals, CLoM serves as stable guidance on the optimization direction of synthetic datasets. Preliminary experiments demonstrate that PTMs indeed contribute to DD. Subsequently, we

<sup>∗</sup> <EMAIL>

<sup>†</sup>Corresponding author (<EMAIL>, <EMAIL>).

<sup>‡</sup> Project lead.

systematically study for the first time the effects of different options in PTMs on DD when utilizing PTMs as supervision signals. Specifically, we consider initialization parameters<sup>[1](#page-1-0)</sup>, model architecture, training epoch and domain knowledge. In experiments involving domain knowledge, we introduce a novel loss term named CCLoM (Contrastive Classification Loss of pre-trained Model) to address the issue of label mismatch in PTMs across different domains. We summarize several key findings here:

- *Model diversification.* The increased diversity of PTMs, including various initialization parameters and diverse model architectures, leads to additional performance improvements in DD to some extent.
- *Training epoch.* It's not mandatory to employ well-trained models as supervision signals. Surprisingly, using sub-optimal models instead can sometimes achieve better results.
- *Domain knowledge.* PTMs used as supervision signals do not have to be trained on the original dataset; they can also come from other datasets. Among PTMs in various domains, domain-related PTMs are preferred as accurate supervision signals.

We hope that these findings will facilitate the DD community to concentrate on PTMs and inspire the development of future algorithms. Finally, by selecting optimal options, we significantly improve the cross-architecture generalization over baseline methods. Specifically, the gains on Dataset Condensation [\(Zhao et al.,](#page-12-3) [2021\)](#page-12-3) (DC), Differentiable Siamese Augmentation [\(Zhao & Bilen,](#page-12-0) [2021\)](#page-12-0) (DSA), and Distribution Matching [\(Zhao & Bilen,](#page-12-2) [2023\)](#page-12-2) (DM) are  $+4.5\%/+10.7\%/+5.2\%$ , respectively.

## 2 RELATED WORK

Dataset Distillation. With the unlimited growth of data, DD, as a data reduction technique, has attracted widespread attention. Previous studies have demonstrated the effectiveness and feasibility [\(Nguyen et al.,](#page-10-1) [2021;](#page-10-1) [Zhao et al.,](#page-12-3) [2021;](#page-12-3) [Zhao & Bilen,](#page-12-0) [2021;](#page-12-0) [Cazenavette et al.,](#page-9-0) [2022;](#page-9-0) [Wang et al.,](#page-11-2) [2022;](#page-11-2) [Kim et al.,](#page-10-2) [2022;](#page-10-2) [Zhou et al.,](#page-12-4) [2023\)](#page-12-4) of this technique and apply it to various fields like continual learning [\(Zhao et al.,](#page-12-3) [2021;](#page-12-3) [Zhou et al.,](#page-12-1) [2022;](#page-12-1) [Zhao & Bilen,](#page-12-2) [2023\)](#page-12-2), federated learning [\(Xiong et al.,](#page-11-3) [2022;](#page-11-3) [Song et al.,](#page-11-4) [2022;](#page-11-4) [Liu et al.,](#page-10-4) [2022;](#page-10-4) [Hu et al.,](#page-10-5) [2022\)](#page-10-5), neural architecture search [\(Such et al.,](#page-11-5) [2020;](#page-11-5) [Zhao et al.,](#page-12-3) [2021;](#page-12-3) [Zhao & Bilen,](#page-12-0) [2021;](#page-12-0) [2023\)](#page-12-2) and recommender system [\(Sachdeva et al.,](#page-11-6) [2022\)](#page-11-6), etc.

Pre-trained Models. PTMs have shown promising achievements in natural language processing, computer vision, and cross-modal fields [\(Radford et al.,](#page-11-7) [2018;](#page-11-7) [2019;](#page-11-8) [Kenton & Toutanova,](#page-10-6) [2019;](#page-10-6) [Brown et al.,](#page-9-3) [2020;](#page-9-3) [Radford et al.,](#page-11-9) [2021;](#page-11-9) [OpenAI,](#page-10-7) [2023\)](#page-10-7). They provide a practical solution for mitigating the data hunger and poor generalization ability in deep learning [\(Belkin et al.,](#page-9-4) [2019;](#page-9-4) [Xu et al.,](#page-11-10) [2020\)](#page-11-10). For example, a series of models [\(Simonyan & Zisserman,](#page-11-11) [2014;](#page-11-11) [He et al.,](#page-10-8) [2016\)](#page-10-8) are trained on the large-scale visual recognition dataset such as ImageNet [\(Deng et al.,](#page-9-5) [2009\)](#page-9-5). The rich knowledge in the dataset is injected into pre-trained models through model training and implicitly encoded in model parameters. Through fine-tuning, this knowledge can be transferred to numerous downstream tasks, spanning image classification [\(He et al.,](#page-10-8) [2016;](#page-10-8) [Zhang et al.,](#page-12-5) [2021\)](#page-12-5), object detection [\(Sermanet et al.,](#page-11-12) [2013;](#page-11-12) [Obaid et al.,](#page-10-9) [2022\)](#page-10-9), image segmentation [\(Iglovikov & Shvets,](#page-10-10) [2018;](#page-10-10) [Kalapos & Gyires-Tóth,](#page-10-11) [2022\)](#page-10-11), image generation [\(Bellagente et al.,](#page-9-6) [2023;](#page-9-6) [Zhang et al.,](#page-12-6) [2023b\)](#page-12-6), 3D vision [\(Zhang et al.,](#page-12-7) [2023a\)](#page-12-7), etc.

Unfortunately, while PTMs have successfully benefited various downstream tasks by taking advantage of the knowledge extracted from the original dataset, DD has not effectively harnessed this knowledge. In this paper, we explore the role of PTMs in DD, paving the way for future research.

## 3 BACKGROUND AND EXPERIMENTAL SETUP

#### 3.1 BACKGROUND

DD aims to synthesize small-scale surrogate datasets containing similar information to the original large-scale datasets [\(Wang et al.,](#page-11-0) [2018;](#page-11-0) [Zhao et al.,](#page-12-8) [2020;](#page-12-8) [Wang et al.,](#page-11-2) [2022\)](#page-11-2). Let  $\mathcal{T} = \{(x_i, y_i)\}_{i=1}^{|\mathcal{T}|}$  $i=1$ be the original dataset consisted of  $|T|$  pairs of images and corresponding labels. The synthetic

<span id="page-1-0"></span><sup>&</sup>lt;sup>1</sup>In this paper, "different initialization parameters" refers to the model's parameters initialized with various random seeds.

surrogate dataset is denoted as  $S = \{(s_i, y_i)\}_{i=1}^{|S|}$ , where  $|S| \ll |\mathcal{T}|$ . Then the DD task can be formulated as follows:

<span id="page-2-2"></span>
$$
S^* = \underset{S}{\arg\max} \phi(S, \mathcal{T}), \tag{1}
$$

where  $\phi$  is a task-specific loss that varies among different DD methods. Below we introduce three representative methods with each using a different technique.

DC was proposed to match the training gradients of the synthetic data and original data [\(Zhao et al.,](#page-12-3) [2021\)](#page-12-3). Given a model with parameters  $\hat{\theta}$ , the optimization process can be expressed as:

$$
\min_{\mathcal{S}} \sigma \left( \nabla_{\theta} \mathcal{L}(\theta; \mathcal{S}), \nabla_{\theta} \mathcal{L}(\theta; \mathcal{T}) \right),\tag{2}
$$

where  $\mathcal{L}(\cdot;\cdot)$  and  $\sigma(\cdot;\cdot)$  denote the training loss and a sum of cosine distances between the two gradients of weights associated with each output node at each layer, respectively. Building upon DC, DSA applies data augmentation techniques to further enhance performance [\(Zhao & Bilen,](#page-12-0) [2021\)](#page-12-0). It can be formulated as follows:

$$
\min_{\mathcal{S}} \sigma \left( \nabla_{\theta} \mathcal{L}(\mathcal{A}(\mathcal{S}, \omega^{\mathcal{S}}), \theta), \nabla_{\theta} \mathcal{L}(\mathcal{A}(\mathcal{T}, \omega^{\mathcal{T}}), \theta) \right),\tag{3}
$$

where  $A$  is a family of image transformations such as cropping, color jittering and flipping that are parameterized with  $\omega^S$  and  $\omega^T$ . Different from DC and DSA, DM aligns the feature distributions of the original dataset and synthetic dataset in sampled embedding spaces [\(Zhao & Bilen,](#page-12-2) [2023\)](#page-12-2), which can be formulated as:

$$
\min_{\mathcal{S}} \sigma \left( \frac{1}{|\mathcal{S}|} \sum_{i=0}^{|\mathcal{S}|} f(\theta; s_i), \frac{1}{|\mathcal{T}|} \sum_{i=0}^{|\mathcal{T}|} f(\theta; x_i) \right),\tag{4}
$$

where  $f(\cdot; \cdot)$  is the feature extraction function and  $\sigma(\cdot; \cdot)$  represents maximum mean discrepancy [\(Gret](#page-9-7)[ton et al.,](#page-9-7) [2012\)](#page-9-7).

#### 3.2 EXPERIMENTAL SETUP

Datasets: We conduct primary experiments on two publicly-available datasets: CIFAR-10 [\(Krizhevsky et al.,](#page-10-12) [2009\)](#page-10-12) and CIFAR-100 [\(Krizhevsky et al.,](#page-10-12) [2009\)](#page-10-12), which are most commonly used in DD. As for experiments related to domain knowledge, we use ImageNet-32 [\(Chrabaszcz](#page-9-8) [et al.,](#page-9-8) [2017\)](#page-9-8), PathMNIST [\(Yang et al.,](#page-11-13) [2023\)](#page-11-13), ImageNette<sup>[2](#page-2-0)</sup> and ImageFruit<sup>[3](#page-2-1)</sup>. ImageNet-32 is a dataset composed of small images downsampled from the original ImageNet [\(Deng et al.,](#page-9-5) [2009\)](#page-9-5). PathMNIST is a biomedical dataset about colon pathology. ImageNette and ImageFruit are subsets of the 10 classes in ImageNet, respectively.

Models: Unless otherwise specified, staying consistent with precedent DD methods [\(Zhao et al.,](#page-12-3) [2021;](#page-12-3) [Cazenavette et al.,](#page-9-0) [2022;](#page-9-0) [Cui et al.,](#page-9-9) [2022\)](#page-9-9), we use a standard ConvNet architecture with three convolutional layers (ConvNet-3) to train and evaluate synthetic datasets. As for experiments on ImageNette and ImageFruit, we increase the number of convolutional layers to 6 (ConvNet-6), in line with [Cazenavette et al.](#page-9-2) [\(2023\)](#page-9-2).

Training configurations: During the evaluation of synthetic datasets, we uniformly apply DSA to preprocess all synthetic images and train the augmented images with a batch size of 256 for 1,000 epochs. We employ the SGD optimizer with an initial learning rate of 0.01, a momentum of 0.9 and a weight decay of  $5 \times 10^{-4}$ . The learning rate is reduced by a factor of 0.1 after every 500 epochs. As for training to obtain PTMs, we train the model (without DSA) for 150 epochs, with a weight decay of  $5 \times 10^{-3}$  and reduce the learning rate by 0.1 after every 50 epochs. To mitigate the impact of randomness, we repeat each experiment 5 times and report the mean and standard deviation. All experiments are conducted on a server equipped with 2 NVIDIA Tesla A100 GPUs.

#### <span id="page-2-3"></span>4 CAN PRE-TRAINED MODELS ASSIST IN DATASET DISTILLATION?

PTMs have already learned valid feature representations and rich semantic information from the dataset on which they are trained [\(Tang et al.,](#page-11-14) [2020;](#page-11-14) [Gou et al.,](#page-9-10) [2021\)](#page-9-10). As a result, they inherently

<span id="page-2-0"></span> $^2$ ImageNette is downloaded from <https://github.com/fastai/imagenette>.

<span id="page-2-1"></span><sup>&</sup>lt;sup>3</sup> ImageFruit is downloaded using the label index provided in [Cazenavette et al.](#page-9-2) [\(2023\)](#page-9-2).

<span id="page-3-0"></span>Table 1: Performance comparison to different DD methods. C10 and C100 denote CIFAR10 and CIFAR100, respectively.  $\mathcal{N}_m$  represents the number of PTMs with different initialization parameters (different random seeds).  $\mathcal{N}_a$  denotes the number of different model architectures. Experiments with  $\mathcal{N}_m = 1$  and  $\mathcal{N}_a = 1$  verify whether PTMs are beneficial for DD; Experiments with  $\mathcal{N}_m = 10$  and  $\mathcal{N}_a = 1$  investigate the influence of initialization parameters on synthetic datasets; Experiments with  $\mathcal{N}_m = 1$  and  $\mathcal{N}_a = 4$  investigate the influence of model architecture on synthetic datasets.

|         |            |      |                 |                 | <b>IPC</b>                  |                   |                  |            |      |                 | <b>IPC</b>      |                             |                           |
|---------|------------|------|-----------------|-----------------|-----------------------------|-------------------|------------------|------------|------|-----------------|-----------------|-----------------------------|---------------------------|
| Dataset | Method     | CLoM | $\mathcal{N}_m$ | $\mathcal{N}_a$ | 10                          | 50                | Dataset          | Method     | CLoM | $\mathcal{N}_m$ | $\mathcal{N}_a$ | 10                          | 50                        |
|         |            |      |                 |                 | $51.4_{\pm 0.3}$            | $57.5_{\pm 0.2}$  | C <sub>100</sub> | DC         | Х    |                 |                 | $28.7_{\pm 0.3}$            | $31.4_{\pm 0.4}$          |
|         |            |      |                 |                 | $51.5_{\pm 0.3}$            | 57.8 $_{\pm 0.3}$ |                  |            |      |                 |                 | $29.4_{\pm0.4}$             | $33.7_{\pm 0.4}$          |
|         | DC         |      | 10              |                 | $52.0_{\pm0.3}$             | $60.2_{\pm 0.3}$  |                  |            |      | 10              |                 | $31.8_{\pm 0.3}$            | 39.5 $\pm$ 0.4            |
|         |            |      |                 | 4               | $52.2_{\pm 0.1}$            | 59.7 $_{\pm0.6}$  |                  |            |      |                 | 4               | $30.2_{+0.2}$               | $36.0_{\pm 0.6}$          |
|         | <b>DSA</b> |      |                 |                 | $53.3_{\pm 0.2}$            | $60.7_{\pm 0.5}$  |                  | <b>DSA</b> | Х    |                 |                 | $32.8{\scriptstyle \pm0.2}$ | $42.8 + 0.4$              |
|         |            |      |                 |                 | $53.4_{\pm 0.3}$            | 61.9 $_{\pm0.3}$  |                  |            |      |                 | 1               | $33.8_{\pm 0.2}$            | $43.0_{\pm 0.4}$          |
| C10     |            |      | 10              |                 | $54.4_{\pm0.4}$             | $65.1_{\pm 0.5}$  |                  |            |      | 10              |                 | $35.5_{\pm 0.2}$            | $43.2_{\pm 0.3}$          |
|         |            |      |                 | 4               | $53.5_{\pm 0.4}$            | 63.0 $\pm$ 0.2    |                  |            |      |                 | 4               | $34.1_{\pm 0.2}$            | $43.2_{\pm 0.2}$          |
|         |            |      |                 | ٠               | 49.6 $\rm _{\pm 0.3}$       | $62.5_{\pm 0.7}$  |                  |            | Х    |                 |                 | $30.0_{\pm0.2}$             | $43.2_{\pm 0.4}$          |
|         |            |      |                 |                 | $50.0_{\pm 0.4}$            | $64.0_{\pm 0.4}$  |                  |            |      |                 |                 | $30.3_{+0.2}$               | 44.6 $\pm$ <sub>0.3</sub> |
|         | DM         |      | 10              |                 | $51.2{\scriptstyle \pm0.4}$ | $66.2_{\pm 0.2}$  |                  | DM         |      | 10              |                 | $31.7{\scriptstyle \pm0.2}$ | $45.6_{\pm 0.3}$          |
|         |            |      |                 | 4               | $50.6_{\pm 0.3}$            | $65.3_{+0.2}$     |                  |            |      |                 | 4               | $30.7_{\pm 0.2}$            | $44.8_{\pm 0.4}$          |

possess a certain level of knowledge about the original dataset. Therefore, utilizing PTMs as supervision signals may provide stable guidance for the optimization direction of synthetic datasets. In order to verify our conjecture, we conduct a preliminary experiment.

To leverage the knowledge embedded in PTMs for enhancing DD, we propose a plug-and-play loss term, called Classification Loss of pre-trained Model (CLoM). CLoM can be portable to any existing DD baseline and serves as a stable guidance for the generation of synthetic datasets. The whole optimizing target can be updated from Eq.  $(1)$  to:

<span id="page-3-1"></span>
$$
S^* = \underset{S}{\arg\min} \mathcal{L}(S, \mathcal{T}) + \alpha \mathcal{L}_{CLoM},\tag{5}
$$

where  $\mathcal{L}_{CLoM} = \frac{1}{|\mathcal{S}|} \sum_{i=1}^{|\mathcal{S}|} \ell(f(\theta^*; s_i), y_i)$ ,  $\ell$  is the cross-entropy loss,  $\theta^*$  denotes the pre-trained model and  $\alpha$  represents a tunable hyperparameter. In Section [6,](#page-7-0) we report the impact of different  $\alpha$ on performance.

We implement CLoM on multiple state-of-the-art training pipelines of DD, including DC, DM, and DSA, to synthesize 10, and 50 images per class (IPC) respectively. For the sake of consistency, we opt for a pre-trained model ( $\mathcal{N}_m = 1$ ,  $\mathcal{N}_a = 1$ ) that has the same architecture (ConvNet-3) as the one used in DD. As shown in Table [1.](#page-3-0) We can observe that CLoM consistently improves the performance across all the baselines, which demonstrates that PTMs can indeed assist in DD.

## 5 EMPRICAL STUDY

In the previous section, we have demonstrated that pre-trained models can assist in dataset distillation. Herein, we systematically study different options in PTMs, including initialization parameters, model architecture, training epoch and domain knowledge, and analyze the influence of each of these factors on synthetic datasets individually.

#### <span id="page-3-2"></span>5.1 MODEL DIVERSIFICATION

First of all, we focus on model diversification, which includes two types: one is the diversification of initialization parameters, while the other is the diversification of model architecture. Herein, we use  $\mathcal{N}_m$  to denote the number of PTMs (these PTMs are initialized with different random seeds and are thoroughly trained on the original dataset) and  $\mathcal{N}_a$  to denote the number of different model architectures. To facilitate comparison, we set the experiments in Section [4](#page-2-3) ( $\mathcal{N}_m = 1, \mathcal{N}_a = 1$ ) as the control group and alter only one control variable at a time.

**Initialization parameters.** In Section [4,](#page-2-3) we utilize a single pre-trained model ( $\mathcal{N}_m = 1, \mathcal{N}_a = 1$ ) with the same architecture as the one used in baselines to conduct experiments. In order to achieve

<span id="page-4-0"></span>Image /page/4/Figure/0 description: This image contains three bar charts side-by-side, each titled "DC", "DSA", and "DM" respectively. The y-axis for all charts is labeled "Acc" and ranges from 0 to 100. The x-axis for all charts is labeled "Class". Each chart displays four sets of bars representing different neural network architectures: ConvNet (blue), VGG-11 (orange), ResNet-18 (green), and AlexNet (red). Within each chart, the bars represent performance on different classes, with the height of each bar indicating the accuracy (Acc) for that specific class and architecture. The bars generally decrease in height from left to right within each chart, indicating a decline in accuracy across the classes for most architectures. The "DC" chart shows accuracies generally above 60%, with some bars reaching 100%. The "DSA" chart also shows high accuracies initially, with a more pronounced decrease across classes. The "DM" chart exhibits a similar trend of decreasing accuracy, with the AlexNet bars showing a significant drop in the later classes.

Figure 1: Classification accuracy of different architectures on various categories of synthetic datasets.

<span id="page-4-1"></span>Image /page/4/Figure/2 description: The image contains two line graphs side-by-side, both plotting accuracy (Acc) against epochs. The left graph shows accuracy values ranging from 48 to 58, with epochs on the x-axis from 0 to 140. It displays three lines: DC (dashed blue), DSA (dashed yellow), and DM (dashed green), along with their corresponding "+CLoM" versions represented by solid lines with markers: DC+CLoM (blue with stars), DSA+CLoM (yellow with triangles), and DM+CLoM (green with plus signs). The right graph shows accuracy values ranging from 55 to 70, with epochs on the x-axis from 0 to 140. It uses the same line styles and markers as the left graph to represent DC, DSA, DM, and their "+CLoM" counterparts.

Figure 2: Performance of different baseline methods of DD. The models used in CLoM are derived from different stages of training. The dotted line represents the baseline.

diversification of initialization parameters, we train 10 models initialized with different random seeds from scratch ( $\mathcal{N}_m = 10$ ,  $\mathcal{N}_a = 1$ ). In each iteration, according to Eq. [\(5\)](#page-3-1), the synthetic dataset is updated using one model at a time. As depicted in Table [1,](#page-3-0) when compared to a single model architecture and a single initialization parameter, introducing diverse initialization parameters leads to further performance improvements on synthetic datasets. These improvements can be attributed to model ensemble [\(Sagi & Rokach,](#page-11-15) [2018;](#page-11-15) [Dong et al.,](#page-9-11) [2020;](#page-9-11) [Zhou et al.,](#page-12-9) [2021\)](#page-12-9) (two heads are better than one). Besides, we conduct an ablation study on  $\mathcal{N}_m$  in Section [6.](#page-7-0)

Model architecture. Different from diverse initialization parameters, here, we use various model architectures. Before starting experiments, we evaluate the classification accuracy of different architectures on various categories of synthetic datasets, with the results in Fig. [1.](#page-4-0) We observe that different architecture excels in specific categories, allowing them to complement each other effectively. This finding highlights the rationality of utilizing diverse model architectures. Subsequently, we expand PTMs from a single architecture (ConvNet-3) to 4 architectures ( $\mathcal{N}_m = 1, \mathcal{N}_a = 4$ ), including ConvNet-3 [\(Gidaris & Komodakis,](#page-9-12) [2018\)](#page-9-12), AlexNet [\(Krizhevsky et al.,](#page-10-13) [2017\)](#page-10-13), VGG-11 [\(Simonyan &](#page-11-11) [Zisserman,](#page-11-11) [2014\)](#page-11-11) and ResNet-18 [\(He et al.,](#page-10-8) [2016\)](#page-10-8). These model architectures are commonly used in mainstream DD methods [\(Cui et al.,](#page-9-9) [2022\)](#page-9-9). At each iteration, we randomly select one pre-trained model from any of the 4 different architectures to calculate CLoM and update the synthetic dataset according to Eq. [\(5\)](#page-3-1). We report the results in Table [1.](#page-3-0) Compared with a single model architecture and a single initialization parameter, diverse model architectures also enhance the performance of synthetic datasets to a certain extent. The ablation study on  $\mathcal{N}_a$  is in Section [6.](#page-7-0)

#### <span id="page-4-2"></span>5.2 TRAINING EPOCH

In previous studies, we have demonstrated that PTMs can assist in DD, and increasing the diversity of PTMs can further improve the performance of synthetic datasets. Herein, we want to ask *whether a (well-trained) PTM is necessary* and *if it's possible to use a sub-optimal model as a supervision signal instead*.

To answer the aforementioned questions, we conduct experiments using a single model architecture and a single initialization parameter ( $\mathcal{N}_m = 1$ ,  $\mathcal{N}_a = 1$ ). Different from using the well-trained PTM

in Section [4,](#page-2-3) we utilize a sub-optimal model (ConvNet-3) instead. Specifically, we employ models at different training stages to calculate CLoM, encompassing epochs 1, 4, 6, 10, 20, 35, 70, 120, and 150. Subsequently, we apply these models to the baselines of DC, DSA and DM on CIFAR-10 with IPC=10 as well as IPC=50, and the results are presented in Fig. [2.](#page-4-1) Even with fewer training epochs, implementing CLoM on such sub-optimal models still leads to significant improvements, and in some cases, it even outperforms well-trained PTMs. For example, on CIFAR-10 with IPC=50, DSA achieves an average accuracy of 68.1 at epoch 4, which is significantly higher than 61.9 achieved at epoch 150. This experimental result highlights that it is not necessary to utilize well-trained PTMs as supervision signals. Sub-optimal models can be employed instead, which not only enhances the performance of synthetic datasets but also takes less training cost.

#### <span id="page-5-3"></span>5.3 DOMAIN KNOWLEDGE

In Section [5.2,](#page-4-2) we find that sub-optimal models can also assist in DD. Therefore, in this subsection, we further lower the threshold for PTMs. We wonder whether domain-specific PTMs are necessary as supervision signals. In other words, *are these PTMs, when utilized as supervision signals, limited to training on the original dataset, or can they also be trained on other datasets?* To this end, we utilize PTMs trained on other datasets to conduct experiments. However, a challenge arises when attempting to switch to PTMs trained on other datasets. Since the final classification layer of each PTM is tailored to the specific classes on which it was trained and there is no inherent correspondence between the original and new label sets, directly feeding the synthetic dataset into PTMs (trained on other datasets) for calculating CLoM is not feasible.

To tackle this issue of label mismatch, we introduce a novel loss term named CCLoM, which stands for Contrastive Classification Loss of pre-trained Model. Inspired by contrastive learning [\(Khosla](#page-10-14) [et al.,](#page-10-14) [2020;](#page-10-14) [Jaiswal et al.,](#page-10-15) [2020\)](#page-10-15), this loss aims to minimize intra-class distances while maximizing inter-class distances using only feature representations. Specifically, in each training iteration, we sample a batch of examples  $(x_B, y_B)$  from the target dataset<sup>[4](#page-5-0)</sup> T and feed them into the PTM (trained on the source dataset) to obtain real features  $F(x)$ . Here, F is the feature extractor. Likewise, we take all examples  $(x_S, y_S)$  from the synthetic dataset S and feed them into the PTM to get synthetic features  $F(x<sub>S</sub>)$ . We then utilize Eq. [\(6\)](#page-5-1) to construct a distance matrix.

<span id="page-5-1"></span>
$$
\mathcal{D}_{con} = 1 - \frac{F(x_{\mathcal{B}})^T F(x_{\mathcal{S}})}{\|F(x_{\mathcal{B}})\|_2 \|F(x_{\mathcal{S}})\|_2}
$$
(6)

Due to the presence of labels, traditional contrastive losses are incapable of handling the case. In this case, we consider supervised contrastive learning [\(Khosla et al.,](#page-10-14) [2020\)](#page-10-14). Specifically, it contrasts all samples from the same class as positives against the negatives from the remainder of the batch. Let  $O(y_B)$  and  $O(y_S)$  be one-hot label encoding of the real batch and synthetic images, respectively. The label correspondence matrix is defined as  $\tilde{M} = O(y_B)^T O(y_S)$ , and CCLoM is computed as follows:

$$
\mathcal{L}_{CCLoM} = \sum_{i=1}^{N} \frac{\sum \mathcal{D}_{con} \odot M}{\sum \mathcal{D}_{con}},
$$
\n(7)

where  $N$  denotes the total batch of the target dataset. Finally, the whole optimizing target can be updated to Eq.  $(8)$  and Algorithm [1](#page-13-0) in appendix provides the details of the calculation of CCLoM.

<span id="page-5-2"></span>
$$
S^* = \underset{S}{\arg\min} \mathcal{L}(S, \mathcal{T}) + \alpha \mathcal{L}_{CCLoM}.
$$
 (8)

We conduct evaluations on two image classification benchmarks: CIFAR-10 and PathMNIST. To control variables, we use a single model architecture and a single initialization parameter ( $\mathcal{N}_m = 1$ ,  $\mathcal{N}_a = 1$ ). The only difference from Section [4](#page-2-3) is that the PTM is trained on ImageNet-32 or CIFAR-100. Table [2](#page-6-0) reports the results, from which we can draw two key conclusion:

1) As supervision signals, PTMs do not need to be trained on the original dataset, they can also be trained on other datasets. This is evidenced by the consistent improvement of synthetic datasets with the assistance of CCLoM.

<span id="page-5-0"></span><sup>4</sup> For ease of understanding, we refer to the dataset that needs to be distilled as the target dataset, and the dataset where PTMs are located as the source dataset in Section [5.3.](#page-5-3)

|                        |                                              | <b>IPC</b>                                                                                 |                                                |          |                       |                                              | <b>IPC</b>                                   |                                               |          |
|------------------------|----------------------------------------------|--------------------------------------------------------------------------------------------|------------------------------------------------|----------|-----------------------|----------------------------------------------|----------------------------------------------|-----------------------------------------------|----------|
| Dataset                | Method                                       | 10                                                                                         | 50                                             | Avg Gain | Dataset               | Method                                       | 10                                           | 50                                            | Avg Gain |
| $C100 \rightarrow C10$ | DC<br>DC+CCL <sub>o</sub> M<br>Gain          | $51.4_{\pm 0.3}$<br>$52.5_{+0.4}$<br>$+1.1$                                                | $57.5_{+0.2}$<br>$59.5_{+0.2}$<br>$+2.0$       |          |                       | DC<br>DC+CCL <sub>o</sub> M<br>Gain          | $56.1_{\pm 1.2}$<br>$56.4_{+0.7}$<br>$+0.3$  | $69.3_{\pm 0.6}$<br>$70.3_{+1.2}$<br>$+1.0$   |          |
|                        | <b>DSA</b><br>DSA+CCL <sub>o</sub> M<br>Gain | $53.3_{\pm 0.2}$<br>$60.7_{+0.5}$<br>$62.9_{\pm 0.5}$<br>$54.3_{+0.3}$<br>$+1.0$<br>$+2.2$ |                                                | $+1.35$  | $C100 \rightarrow PM$ | <b>DSA</b><br>DSA+CCL <sub>o</sub> M<br>Gain | $62.0_{+0.3}$<br>$62.1_{+1.3}$<br>$+0.1$     | $78.7_{+0.6}$<br>77.9 $_{\pm 0.8}$<br>$-0.8$  | $+0.15$  |
|                        | DM.<br>DM+CCL <sub>o</sub> M<br>Gain         | 49.6 $\pm$ <sub>0.3</sub><br>$50.0_{\pm 0.3}$<br>$+0.4$                                    | $62.5_{\pm 0.7}$<br>$63.9_{\pm 0.5}$<br>$+1.4$ |          |                       | DM<br>DM+CCLoM<br>Gain                       | 67.9 $_{\pm 0.9}$<br>$67.9_{\pm 1.1}$<br>0.0 | $78.9_{+0.1}$<br>$79.2_{\pm 0.8}$<br>$+0.3$   |          |
| $I32 \rightarrow C10$  | DC<br>DC+CCL <sub>o</sub> M<br>Gain          | $51.4_{+0.3}$<br>$52.2_{\pm 0.4}$<br>$+0.8$                                                | $57.5_{\pm 0.2}$<br>$58.1_{\pm 0.3}$<br>$+0.6$ |          | $I32 \rightarrow P M$ | DC<br>DC+CCL <sub>o</sub> M<br>Gain          | $56.1_{\pm 1.2}$<br>$56.5_{+0.8}$<br>$+0.4$  | $69.3_{\pm 0.6}$<br>$70.0_{+0.6}$<br>$+0.7$   |          |
|                        | <b>DSA</b><br>DSA+CCL <sub>o</sub> M<br>Gain | $53.3_{\pm 0.2}$<br>$53.6_{+0.2}$<br>$+0.3$                                                | $60.7_{\pm 0.5}$<br>$61.9_{\pm 0.1}$<br>$+1.2$ | $+0.83$  |                       | <b>DSA</b><br>DSA+CCL <sub>o</sub> M<br>Gain | $62.0_{+0.3}$<br>$63.0_{\pm 0.8}$<br>$+1.0$  | $78.7_{\pm0.6}$<br>$78.7_{\pm 1.3}$<br>$+0.0$ | $+0.40$  |
|                        | DM<br>DM+CCL <sub>o</sub> M<br>Gain          | 49.6 $\pm$ <sub>0.3</sub><br>$50.6_{\pm 0.2}$<br>$+1.0$                                    | $62.5_{\pm 0.7}$<br>$63.6_{\pm 0.4}$<br>$+1.1$ |          |                       | DM<br>DM+CCL <sub>o</sub> M<br>Gain          | 67.9 $\pm$ 0.9<br>$68.7_{\pm 0.6}$<br>$+0.8$ | $78.9_{+0.1}$<br>$78.4_{\pm 0.6}$<br>$-0.5$   |          |

<span id="page-6-0"></span>Table 2: Performance comparison to different DD methods. C10, C100, I32 and PM represents CIFAR-10, CIFAR-100, ImageNet-32 and PathMNIST, respectively. In this context,  $C100 \rightarrow C10$ denotes using a PTM trained on CIFAR-100 as a supervision signal to guide the generation of synthetic datasets.

<span id="page-6-2"></span>Table 3: Performance comparison to DM. Since the CLIP model is trained on a dataset of 400 million (image, text) pairs collected from the Internet, we call the dataset Internet.

|                                   |                                     |                                                                                                               | IPC                                             |                                   |                                     |                                            | IPC                                         |                                             |          |  |
|-----------------------------------|-------------------------------------|---------------------------------------------------------------------------------------------------------------|-------------------------------------------------|-----------------------------------|-------------------------------------|--------------------------------------------|---------------------------------------------|---------------------------------------------|----------|--|
| Dataset                           | Method                              |                                                                                                               | 10                                              | Avg Gain                          | Dataset                             | Method                                     |                                             | 10                                          | Avg Gain |  |
| $ImageNet \rightarrow ImageNette$ | DΜ<br>DM+CCL <sub>o</sub> M<br>Gain | $28.3 + 1.5$<br>$48.5 + 0.7$<br>$30.0_{+0.9}$<br>$49.5{\scriptstyle~ \pm 0.4}$<br>$+1.35$<br>$+1.7$<br>$+1.0$ |                                                 | $ImageNet \rightarrow ImageFruit$ | DΜ<br>DM+CCL <sub>o</sub> M<br>Gain | $21.3 + 1.0$<br>$22.1_{\pm 0.3}$<br>$+0.8$ | $28.7_{+1.2}$<br>$29.2_{\pm 1.0}$<br>$+0.5$ | $+0.65$                                     |          |  |
| Internet $\rightarrow$ ImageNette | DΜ<br>DM+CCLoM<br>Gain              | $28.3_{+1.5}$<br>$31.2_{+0.7}$<br>$+1.9$                                                                      | $48.5_{\pm 0.7}$<br>49.0 $_{\pm 0.3}$<br>$+0.5$ | $+1.20$                           | Internet $\rightarrow$ ImageFruit   | DΜ<br>DM+CCL <sub>o</sub> M<br>Gain        | $21.3_{+1.0}$<br>$22.3_{\pm 1.0}$<br>$+1.0$ | $28.7_{+1.2}$<br>$30.3_{\pm 1.9}$<br>$+1.6$ | $+1.30$  |  |

2) When domain-specific PTMs are not available, as supervision signals, domain-related datasets are preferred. It is worth noting that PathMNIST is a biomedical dataset and ImageNet-32 is a downsampled version of the original ImageNet. As supervision signals, PTMs trained on CIFAR-100 lead to significant performance gains (the average gain is 1.35) when CIFAR-10 is used as the target dataset for DD. Conversely, when the target dataset is switched to PathMNIST, the performance improvement is minimal (the average gain is 0.15). We believe this is attributed to the significant domain gap [\(Nam et al.,](#page-10-16) [2021\)](#page-10-16) between CIFAR-100 and PathMNIST. The same phenomenon occurs when ImageNet-32 is utilized as the source dataset.

To further demonstrate that domain-specific PTMs are not necessary for supervision signals to provide precise guidance to DD, we conduct experiments on ImageNette and ImageFruit ( $224 \times 224$ resolution scale). We utilize a pre-trained ResNet-18 [\(He et al.,](#page-10-8) [2016\)](#page-10-8) on ImageNet [\(Deng et al.,](#page-9-5)  $2009$ ) and a pre-trained CLIP [\(Radford et al.,](#page-11-9) [2021\)](#page-11-9) model as supervision signals<sup>[5](#page-6-1)</sup>. It's worth noting that these models can be obtained directly without the need for training. Then we conduct evaluations on DM with a batchsize of 512. The reason for using only DM is that DC and DSA require saving the computational graph to calculate the matching loss, which prevents them from conducting experiments on high-resolution datasets. Table [3](#page-6-2) reports the results. The conclusion that domain-specific PTMs are not necessary for DD still holds true in the case of high resolution. More importantly, based on the success of the CLIP model in guiding ImageNette and ImageFruit synthesis, we are inspired to use existing foundational models, such as GPT-4 [\(OpenAI,](#page-10-7) [2023\)](#page-10-7), to assist in DD.

<span id="page-6-1"></span> ${}^{5}$ ResNet-18 is downloaded from torchvision. As for the CLIP model, we use ResNet-50 as the base architecture for the image encoder.

| Method | CLOM | $N_m$                                                                          | $N_a$ | Training Epoch | Model            |                  |                  |                  | Avg Gain |
|--------|------|--------------------------------------------------------------------------------|-------|----------------|------------------|------------------|------------------|------------------|----------|
|        |      |                                                                                |       |                | ConvNet-3        | VGG-11           | ResNet-18        | AlexNet          |          |
| DC     | $✗$  | -                                                                              | -     | -              | $57.5_{\pm 0.2}$ | $50.4_{\pm 0.9}$ | $46.7_{\pm 0.4}$ | $37.6_{\pm 0.6}$ | -        |
|        | $✓$  | 10                                                                             | 1     | WT             | $60.2_{\pm 0.3}$ | $52.9_{\pm 0.7}$ | $47.4_{\pm 0.3}$ | $37.7_{\pm 0.4}$ | $+1.5$   |
|        | $✓$  | 10                                                                             | 4     | WT             | $61.3_{\pm 0.2}$ | $55.2_{\pm 0.7}$ | $48.6_{\pm 1.1}$ | $37.9_{\pm 0.4}$ | $+2.7$   |
|        | $✓$  | 10                                                                             | 4     | SO             | $63.0_{\pm 0.2}$ | $59.2_{\pm 0.6}$ | $50.3_{\pm 1.2}$ | $37.8_{\pm 1.0}$ | $+4.5$   |
| DSA    | $✗$  | -                                                                              | -     | -              | $60.7_{\pm 0.5}$ | $50.9_{\pm 0.7}$ | $50.3_{\pm 1.1}$ | $46.0_{\pm 0.3}$ | -        |
|        | $✓$  | 1WT $65.1_{\pm 0.5}$ $52.8_{\pm 0.9}$ $51.6_{\pm 0.8}$ $45.5_{\pm 0.5}$ $+1.8$ |       |                |                  |                  |                  |                  |          |
|        | $✓$  | 10                                                                             | 4     | WT             | $66.4_{\pm 0.3}$ | $55.9_{\pm 0.9}$ | $52.7_{\pm 0.8}$ | $46.1_{\pm 0.9}$ | $+3.3$   |
|        | $✓$  | 10                                                                             | 4     | SO             | $69.2_{\pm 0.2}$ | $61.7_{\pm 0.7}$ | $60.7_{\pm 1.0}$ | $58.9_{\pm 0.7}$ | $+10.7$  |
| DM     | $✗$  | -                                                                              | -     | -              | $62.5_{\pm 0.4}$ | $55.9_{\pm 0.5}$ | $54.6_{\pm 0.5}$ | $50.6_{\pm 0.7}$ | -        |
|        | $✓$  | 10                                                                             | 1     | WT             | $66.2_{\pm 0.2}$ | $59.6_{\pm 0.4}$ | $56.9_{\pm 0.6}$ | $52.4_{\pm 0.6}$ | $+2.9$   |
|        | $✓$  | 10                                                                             | 4     | WT             | $66.4_{\pm 0.3}$ | $60.4_{\pm 0.4}$ | $58.1_{\pm 0.8}$ | $57.1_{\pm 0.9}$ | $+4.6$   |
|        | $✓$  | 10                                                                             | 4     | SO             | $67.5_{\pm 0.1}$ | $62.1_{\pm 0.6}$ | $58.3_{\pm 1.0}$ | $56.3_{\pm 1.1}$ | $+5.2$   |

<span id="page-7-1"></span>Table 4: Cross-architecture performance comparison to different baseline methods of DD. The distillation architecture is ConvNet-3. The experiments are conducted on CIFAR-10 with IPC-50. WT and SO represent well-trained and sub-optimal, respectively. Bold entries are best results.

#### 5.4 VALIDATION AND EXTENSIONS

In the previous subsections, we analyze a variety of options in PTMs, including initialization parameters, model architecture, training epoch and domain knowledge, one by one. Herein, we choose various combinations of these options to perform extensive experiments. Since existing DD methods generally suffer from architecture overfitting [\(Zhao & Bilen,](#page-12-0) [2021;](#page-12-0) [Cazenavette et al.,](#page-9-0) [2022;](#page-9-0) [Zhao & Bilen,](#page-12-2) [2023;](#page-12-2) [Lei & Tao,](#page-10-0) [2023\)](#page-10-0). That is, the optimization of synthetic dataset is closely tied to the specific model architecture, and there is a performance drop when the synthetic dataset is applied to unknown architectures. Therefore, we employ CLoM with various combinations of options to mitigate architecture overfitting in DD. Specifically, we utilize ConvNet-3 with the default hyperparameters from the previous work [\(Cui et al.,](#page-9-9)  $2022$ ) to generate synthetic datasets. Subsequently, we evaluate the performance of synthetic datasets on ConvNet-3, VGG-11, ResNet-18 and AlexNet. With the goal of conducting a fair comparison, we utilize the same training configurations for evaluation. All experiments are conducted on CIFAR-10 with IPC-50.

Table [4](#page-7-1) exhibits the experimental results. In general, different combinations of options mitigate architecture overfitting to varying degrees. Among these combinations, sub-optimal models (According to Fig. [2,](#page-4-1) we select the models at the epoch with the best performance as sub-optimal models.) with diverse initialization parameters and various model architectures ( $\mathcal{N}_m = 10$ ,  $\mathcal{N}_a = 4$ ) perform best. Specifically, among these model architectures, DSA achieves an average performance improvement of 10.7% on synthetic datasets with the assistance of CLoM. In comparison, the improvements on DC and DM are relatively small but still substantial, at 4.5% and 5.2%, respectively. Besides, we visualize the feature distribution of these synthetic images and compare them to the feature distribution learned by DC, DSA and DM. Following the settings of [Zhao & Bilen](#page-12-2) [\(2023\)](#page-12-2), we utilize a model trained on the whole training set to extract features and visualize the features with t-SNE [\(Van der Maaten](#page-11-16) [& Hinton,](#page-11-16)  $2008$ ). As depicted in Fig. [A1,](#page-13-1) compared to the baseline, our synthetic images more accurately reflect the distribution of the original training set.

For additional supplementary experiments, please refer to Appendix [A.](#page-13-2)

## <span id="page-7-0"></span>6 ABLATION STUDY

In Section [5.1,](#page-3-2) we find that diverse initialization parameters and diverse model architectures can enhance the performance of synthetic datasets. Here, we conduct ablation studies on  $\mathcal{N}_m$  and  $\mathcal{N}_a$ . Additionally, we also investigate the influence of  $\alpha$  on the performance of synthetic datasets. Unless otherwise specified, all ablation studies are conducted on CIFAR-10 with IPC=50.

**Ablation on**  $\alpha$ . We conduct experiments on the basis of Section [4](#page-2-3) with varying  $\alpha$ . The outcome is presented in the left part of Fig. [3.](#page-8-0) We observe that different values of  $\alpha$  have a slight impact on the

<span id="page-8-0"></span>Image /page/8/Figure/0 description: The image contains two line graphs side-by-side. Both graphs have 'Acc' on the y-axis, ranging from 55 to 70. The left graph's x-axis is labeled 'a' and is on a logarithmic scale, with points at 100, 1000, and 10000. The right graph's x-axis is labeled 'Nm' and is on a linear scale, with points at 2, 4, 6, 8, and 10. Both graphs display three sets of data, each represented by a different line style and color, and a legend indicates that these correspond to 'DC' (dashed blue line), 'DSA' (dashed orange line), and 'DM' (dashed green line). Additionally, there are three corresponding lines with markers: 'DC+CLoM' (blue line with stars), 'DSA+CLoM' (orange line with triangles), and 'DM+CLoM' (green line with plus signs). In the left graph, 'DM+CLoM' is consistently the highest, around 64. 'DSA+CLoM' is around 61, and 'DC+CLoM' is around 58. In the right graph, 'DM+CLoM' starts around 64 and increases to about 66.5. 'DSA+CLoM' starts around 61.5 and increases to about 64. 'DC+CLoM' starts around 57.5 and fluctuates between 58 and 59.5.

Figure 3: Ablation study on  $\alpha$  and  $\mathcal{N}_m$ . The dotted line represents the baseline.

<span id="page-8-1"></span>Table 5: Explorations of the diversity of model architectures. This experiment is conducted based on DC. Bold entries are best results.

| ConvNet-3 | VGG-11 | ResNet-18 | AlexNet | ConvNet-4 | Performance           |
|-----------|--------|-----------|---------|-----------|-----------------------|
| ✓         |        |           |         |           | 57.8 $\pm$ 0.3        |
|           | ✓      |           |         |           | 57.7 $\pm$ 0.3        |
|           |        | ✓         |         |           | 57.3 $\pm$ 0.3        |
|           |        |           | ✓       |           | 57.3 $\pm$ 0.6        |
|           |        |           |         | ✓         | 58.2 $\pm$ 0.2        |
| ✓         | ✓      |           |         |           | 58.7 $\pm$ 0.5        |
| ✓         | ✓      | ✓         |         |           | 59.1 $\pm$ 0.6        |
| ✓         | ✓      | ✓         | ✓       |           | 59.7 $\pm$ 0.6        |
| ✓         | ✓      | ✓         |         | ✓         | <b>60.0</b> $\pm$ 0.2 |

performance of the synthetic dataset. This impact is primarily attributed to the trade-off between two losses in Eq.  $(5)$ .

**Ablation on**  $\mathcal{N}_m$ . Following the experimental settings of Section [4,](#page-2-3) we use different  $\mathcal{N}_m$  to perform experiments. As shown in the right part of Fig. [3,](#page-8-0) with the number of  $\mathcal{N}_m$  increasing, the overall performance of the synthetic dataset exhibits an upward trend. The best results are achieved when  $\mathcal{N}_m = 10$ . Hence, in the previous experiments, we set  $\mathcal{N}_m = 10$  by default.

**Ablation on**  $\mathcal{N}_a$ . Similarly, based on the experiment settings of Section [4,](#page-2-3) we only change  $\mathcal{N}_a$ . Our experiments cover a range of scenarios, from a single model architecture to various combinations of model architectures. To make the experiment more convincing, we further add the ConvNet-4. The results are exhibited in the Table [5.](#page-8-1) We observe that diverse model architectures consistently lead to improved performance of the synthetic dataset. The best results are achieved with all five model architectures included.

### 7 CONCLUSION

In this paper, we explore the question: "Can pre-trained models assist in dataset distillation?" To this end, we introduce two plug-and-play loss terms, *i.e.*, CLoM and CCLoM, portable to any existing DD baseline. By leveraging PTMs as supervision signals, they serve as stable guidance on the optimization direction of synthetic datasets. Then we systematically study the effects of different options in PTMs on DD, including initialization parameters, model architecture, training epoch and domain knowledge, and summarize several key findings. Based on these findings, we achieve significant improvements in cross-architecture generalization compared to the baseline method. Finally, we aspire that our research will facilitate the DD community to concentrate on PTMs and inspire the development of innovative algorithms in the future.

Limitations. While PTMs as supervision signals provide stable guidance for optimizing synthetic datasets, there is a potential limitation that should be acknowledged. For example, the training cost of PTMs cannot be overlooked. This can be mitigated by using sub-optimal models (see Section [5.2\)](#page-4-2). Besides, thanks to the CCLoM, domain-agnostic PTMs are able to serve as supervision signals (see Section [5.3\)](#page-5-3), which eliminates the need to train PTMs.

Future Works. In this paper, we demonstrate that domain-agnostic PTMs can also assist in DD (see Section [5.3\)](#page-5-3). Therefore, a promising direction is harnessing existing foundational models, such as CLIP [\(Radford et al.,](#page-11-9) [2021\)](#page-11-9) and GPT-4 [\(OpenAI,](#page-10-7) [2023\)](#page-10-7), to steer and enhance DD. Thanks to the generalization capabilities exhibited by these foundational models, the application of DD in more intricate scenarios, such as semantic segmentation [\(Long et al.,](#page-10-17) [2015\)](#page-10-17), objective detection [\(Girshick](#page-9-13) [et al.,](#page-9-13) [2014\)](#page-9-13) and multi-modal [\(Radford et al.,](#page-11-9) [2021\)](#page-11-9), becomes feasible.

#### **REFERENCES**

- <span id="page-9-4"></span>Mikhail Belkin, Daniel Hsu, Siyuan Ma, and Soumik Mandal. Reconciling modern machine-learning practice and the classical bias–variance trade-off. *Proceedings of the National Academy of Sciences*, 116(32):15849–15854, 2019.
- <span id="page-9-6"></span>Marco Bellagente, Manuel Brack, Hannah Teufel, Felix Friedrich, Björn Deiseroth, Constantin Eichenberg, Andrew Dai, Robert Baldock, Souradeep Nanda, Koen Oostermeijer, et al. Multifusion: Fusing pre-trained models for multi-lingual, multi-modal image generation. *arXiv preprint arXiv:2305.15296*, 2023.
- <span id="page-9-3"></span>Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. *NeurIPS*, 33:1877–1901, 2020.
- <span id="page-9-0"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, pp. 4750–4759, 2022.
- <span id="page-9-2"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Generalizing dataset distillation via deep generative prior. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 3739–3748, 2023.
- <span id="page-9-8"></span>Patryk Chrabaszcz, Ilya Loshchilov, and Frank Hutter. A downsampled variant of imagenet as an alternative to the cifar datasets. *arXiv preprint arXiv:1707.08819*, 2017.
- <span id="page-9-9"></span>Justin Cui, Ruochen Wang, Si Si, and Cho-Jui Hsieh. Dc-bench: Dataset condensation benchmark. *arXiv preprint arXiv:2207.09639*, 2022.
- <span id="page-9-5"></span>Jia Deng, Wei Dong, Richard Socher, Li-Jia Li, Kai Li, and Li Fei-Fei. Imagenet: A large-scale hierarchical image database. In *2009 IEEE conference on computer vision and pattern recognition*, pp. 248–255. Ieee, 2009.
- <span id="page-9-1"></span>Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning*, pp. 5378–5396. PMLR, 2022.
- <span id="page-9-11"></span>Xibin Dong, Zhiwen Yu, Wenming Cao, Yifan Shi, and Qianli Ma. A survey on ensemble learning. *Frontiers of Computer Science*, 14:241–258, 2020.
- <span id="page-9-12"></span>Spyros Gidaris and Nikos Komodakis. Dynamic few-shot visual learning without forgetting. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 4367–4375, 2018.
- <span id="page-9-13"></span>Ross Girshick, Jeff Donahue, Trevor Darrell, and Jitendra Malik. Rich feature hierarchies for accurate object detection and semantic segmentation. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 580–587, 2014.
- <span id="page-9-10"></span>Jianping Gou, Baosheng Yu, Stephen J Maybank, and Dacheng Tao. Knowledge distillation: A survey. *International Journal of Computer Vision*, 129:1789–1819, 2021.
- <span id="page-9-7"></span>Arthur Gretton, Karsten M Borgwardt, Malte J Rasch, Bernhard Schölkopf, and Alexander Smola. A kernel two-sample test. *The Journal of Machine Learning Research*, 13(1):723–773, 2012.

- <span id="page-10-8"></span>Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 770–778, 2016.
- <span id="page-10-5"></span>Shengyuan Hu, Jack Goetz, Kshitiz Malik, Hongyuan Zhan, Zhe Liu, and Yue Liu. Fedsynth: Gradient compression via synthetic data in federated learning. *arXiv preprint arXiv:2204.01273*, 2022.
- <span id="page-10-10"></span>Vladimir Iglovikov and Alexey Shvets. Ternausnet: U-net with vgg11 encoder pre-trained on imagenet for image segmentation. *arXiv preprint arXiv:1801.05746*, 2018.
- <span id="page-10-15"></span>Ashish Jaiswal, Ashwin Ramesh Babu, Mohammad Zaki Zadeh, Debapriya Banerjee, and Fillia Makedon. A survey on contrastive self-supervised learning. *Technologies*, 9(1):2, 2020.
- <span id="page-10-3"></span>Wei Jin, Xianfeng Tang, Haoming Jiang, Zheng Li, Danqing Zhang, Jiliang Tang, and Bing Yin. Condensing graphs via one-step gradient matching. In *Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, pp. 720–730, 2022.
- <span id="page-10-11"></span>András Kalapos and Bálint Gyires-Tóth. Self-supervised pretraining for 2d medical image segmentation. In *European Conference on Computer Vision*, pp. 472–484. Springer, 2022.
- <span id="page-10-6"></span>Jacob Devlin Ming-Wei Chang Kenton and Lee Kristina Toutanova. Bert: Pre-training of deep bidirectional transformers for language understanding. In *Proceedings of naacL-HLT*, volume 1, pp. 2, 2019.
- <span id="page-10-14"></span>Prannay Khosla, Piotr Teterwak, Chen Wang, Aaron Sarna, Yonglong Tian, Phillip Isola, Aaron Maschinot, Ce Liu, and Dilip Krishnan. Supervised contrastive learning. *Advances in neural information processing systems*, 33:18661–18673, 2020.
- <span id="page-10-2"></span>Jang-Hyun Kim, Jinuk Kim, Seong Joon Oh, Sangdoo Yun, Hwanjun Song, Joonhyun Jeong, Jung-Woo Ha, and Hyun Oh Song. Dataset condensation via efficient synthetic-data parameterization. In *International Conference on Machine Learning*, pp. 11102–11118. PMLR, 2022.

<span id="page-10-12"></span>Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.

- <span id="page-10-13"></span>Alex Krizhevsky, Ilya Sutskever, and Geoffrey E Hinton. Imagenet classification with deep convolutional neural networks. *Communications of the ACM*, 60(6):84–90, 2017.
- <span id="page-10-0"></span>Shiye Lei and Dacheng Tao. A comprehensive survey to dataset distillation. *arXiv preprint arXiv:2301.05603*, 2023.
- <span id="page-10-4"></span>Ping Liu, Xin Yu, and Joey Tianyi Zhou. Meta knowledge condensation for federated learning. *arXiv preprint arXiv:2209.14851*, 2022.
- <span id="page-10-18"></span>Yanqing Liu, Jianyang Gu, Kai Wang, Zheng Zhu, Wei Jiang, and Yang You. Dream: Efficient dataset distillation by representative matching. *arXiv preprint arXiv:2302.14416*, 2023.
- <span id="page-10-17"></span>Jonathan Long, Evan Shelhamer, and Trevor Darrell. Fully convolutional networks for semantic segmentation. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 3431–3440, 2015.
- <span id="page-10-16"></span>Hyeonseob Nam, HyunJae Lee, Jongchan Park, Wonjun Yoon, and Donggeun Yoo. Reducing domain gap by reducing style bias. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 8690–8699, 2021.
- <span id="page-10-1"></span>Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021.
- <span id="page-10-9"></span>Omar Ibrahim Obaid, Mazin Abed Mohammed, Akbal Omran Salman, Salama A Mostafa, and Ahmed A Elngar. Comparing the performance of pre-trained deep learning models in object detection and recognition. *Journal of Information Technology Management*, 14(4):40–56, 2022.

<span id="page-10-7"></span>OpenAI. Gpt-4 technical report, 2023.

- <span id="page-11-7"></span>Alec Radford, Karthik Narasimhan, Tim Salimans, Ilya Sutskever, et al. Improving language understanding by generative pre-training. 2018.
- <span id="page-11-8"></span>Alec Radford, Jeffrey Wu, Rewon Child, David Luan, Dario Amodei, Ilya Sutskever, et al. Language models are unsupervised multitask learners. *OpenAI blog*, 1(8):9, 2019.
- <span id="page-11-9"></span>Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, et al. Learning transferable visual models from natural language supervision. In *ICML*, pp. 8748–8763, 2021.
- <span id="page-11-6"></span>Noveen Sachdeva, Mehak Preet Dhaliwal, Carole-Jean Wu, and Julian McAuley. Infinite recommendation networks: A data-centric approach. *arXiv preprint arXiv:2206.02626*, 2022.
- <span id="page-11-15"></span>Omer Sagi and Lior Rokach. Ensemble learning: A survey. *Wiley Interdisciplinary Reviews: Data Mining and Knowledge Discovery*, 8(4):e1249, 2018.
- <span id="page-11-12"></span>Pierre Sermanet, David Eigen, Xiang Zhang, Michaël Mathieu, Rob Fergus, and Yann LeCun. Overfeat: Integrated recognition, localization and detection using convolutional networks. *arXiv preprint arXiv:1312.6229*, 2013.
- <span id="page-11-11"></span>Karen Simonyan and Andrew Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014.
- <span id="page-11-4"></span>Rui Song, Dai Liu, Dave Zhenyu Chen, Andreas Festag, Carsten Trinitis, Martin Schulz, and Alois Knoll. Federated learning via decentralized dataset distillation in resource-constrained edge environments. *arXiv preprint arXiv:2208.11311*, 2022.
- <span id="page-11-5"></span>Felipe Petroski Such, Aditya Rawal, Joel Lehman, Kenneth Stanley, and Jeffrey Clune. Generative teaching networks: Accelerating neural architecture search by learning to generate synthetic training data. In *International Conference on Machine Learning*, pp. 9206–9216. PMLR, 2020.
- <span id="page-11-14"></span>Jiaxi Tang, Rakesh Shivanna, Zhe Zhao, Dong Lin, Anima Singh, Ed H Chi, and Sagar Jain. Understanding and improving knowledge distillation. *arXiv preprint arXiv:2002.03532*, 2020.
- <span id="page-11-16"></span>Laurens Van der Maaten and Geoffrey Hinton. Visualizing data using t-sne. *Journal of machine learning research*, 9(11), 2008.
- <span id="page-11-2"></span>Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Yang, Shuo Wang, Guan Huang, Hakan Bilen, Xinchao Wang, and Yang You. Cafe: Learning to condense dataset by aligning features. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 12196–12205, 2022.
- <span id="page-11-17"></span>Kai Wang, Jianyang Gu, Daquan Zhou, Zheng Zhu, Wei Jiang, and Yang You. Dim: Distilling dataset into generative model. *arXiv preprint arXiv:2303.04707*, 2023.
- <span id="page-11-0"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-11-3"></span>Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. Feddm: Iterative distribution matching for communication-efficient federated learning. *arXiv preprint arXiv:2207.09653*, 2022.
- <span id="page-11-10"></span>Keyulu Xu, Mozhi Zhang, Jingling Li, Simon S Du, Ken-ichi Kawarabayashi, and Stefanie Jegelka. How neural networks extrapolate: From feedforward to graph neural networks. *arXiv preprint arXiv:2009.11848*, 2020.
- <span id="page-11-13"></span>Jiancheng Yang, Rui Shi, Donglai Wei, Zequan Liu, Lin Zhao, Bilian Ke, Hanspeter Pfister, and Bingbing Ni. Medmnist v2-a large-scale lightweight benchmark for 2d and 3d biomedical image classification. *Scientific Data*, 10(1):41, 2023.
- <span id="page-11-1"></span>Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023.

- <span id="page-12-7"></span>Renrui Zhang, Liuhui Wang, Yu Qiao, Peng Gao, and Hongsheng Li. Learning 3d representations from 2d pre-trained models via image-to-point masked autoencoders. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 21769–21780, 2023a.
- <span id="page-12-5"></span>Yifan Zhang, Bryan Hooi, Dapeng Hu, Jian Liang, and Jiashi Feng. Unleashing the power of contrastive self-supervised visual models via contrast-regularized fine-tuning. In *Advances in Neural Information Processing Systems*, 2021.
- <span id="page-12-6"></span>Yifan Zhang, Daquan Zhou, Bryan Hooi, Kai Wang, and Jiashi Feng. Expanding small-scale datasets with guided imagination. In *Advances in neural information processing systems*, 2023b.
- <span id="page-12-0"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *ICML*, pp. 12674–12685. PMLR, 2021.
- <span id="page-12-2"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, pp. 6514– 6523, 2023.
- <span id="page-12-8"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *arXiv preprint arXiv:2006.05929*, 2020.
- <span id="page-12-3"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 1(2):3, 2021.
- <span id="page-12-4"></span>Daquan Zhou, Kai Wang, Jianyang Gu, Xiangyu Peng, Dongze Lian, Yifan Zhang, Yang You, and Jiashi Feng. Dataset quantization. In *International Conference on Computer Vision*, pp. 17205–17216, 2023.
- <span id="page-12-9"></span>Kaiyang Zhou, Yongxin Yang, Yu Qiao, and Tao Xiang. Domain adaptive ensemble learning. *IEEE Transactions on Image Processing*, 30:8008–8018, 2021.
- <span id="page-12-1"></span>Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *arXiv preprint arXiv:2206.00719*, 2022.

#### <span id="page-13-0"></span>Algorithm 1 PyTorch-like pseudo-code for CCLoM.

```
# num_classes: number of classes
# batch_size: batch size of real images
def calculating_CCLoM(real_img, real_label, syn_img, syn_label):
     # One-Hot Encoding to real label
real_label_onehot = torch.zeros((batch_size, num_classes))
    real_label_onehot[torch.arange(batch_size), real_label] = 1
     # One-Hot Encoding to synthetic label
syn_label_onehot = torch.zeros((ipc * num_classes, num_classes))
    syn\_label\_onehot[torch.arange(ipc * num\_classes), syn\_label] = 1# True and false sample matrix
    labels = syn_label_onehot @ real_label_onehot.T
     # extract feature representations
    real_feat = model(real_img)
    syn_feat = model(syn_img)
     real_feature_norm = real_feat / real_feat.norm(dim=-1, keepdim=True)
syn_feature_norm = syn_feat / syn_feat.norm(dim=-1, keepdim=True)
    cos_dist = 1 - syn_feature_norm @ real_feature_norm.T
    cclom = torch.sum(labels * cos_dist) / torch.sum(cos_dist)
```

return cclom

<span id="page-13-1"></span>Image /page/13/Figure/3 description: The image displays a 2x3 grid of scatter plots, each visualizing the distribution of synthetic images learned by DC, DSA, and DM. Each plot has x and y axes ranging from -150 to 150. The plots show three distinct clusters of data points, colored blue, red, and green, representing Category 0, Category 1, and Category 2, respectively. Within each cluster, original data points are marked with small dots, while synthetic data points are marked with triangles. The legend in each plot clarifies these representations: Category 0 (Original), Category 1 (Original), Category 2 (Original), Category 0 (Synthetic), Category 1 (Synthetic), and Category 2 (Synthetic). The first row of plots appears to show the distributions from DC, the second row from DSA, and the third row from DM, though this is not explicitly stated in the provided text.

Figure A1: Distributions of synthetic images learned by DC, DSA and DM. The first row represents the distribution without CLoM, while the second row represents the distribution with CLoM using the best combination of options. From left to right are the distributions of DC, DSA, and DM. Best viewed in color.

## <span id="page-13-2"></span>A APPENDIX

We apply the proposed CLoM to state-of-the-art methods, including IDC [\(Kim et al.,](#page-10-2) [2022\)](#page-10-2), DiM [\(Wang et al.,](#page-11-17) [2023\)](#page-11-17), DREAM [\(Liu et al.,](#page-10-18) [2023\)](#page-10-18) and TM [\(Cazenavette et al.,](#page-9-0) [2022\)](#page-9-0), and compare the performance in Table [A1.](#page-14-0) For fair comparison, the experiments are conducted under the default parameters provided in original papers. Herein, we utilize well-trained models with diverse initialization parameters and single model architectures ( $\mathcal{N}_m = 10$ ,  $\mathcal{N}_a = 1$ ) to conduct experiments. The stable performance improvements demonstrate that even with state-of-the-art DD methods, PTMs can still improve the performance of synthetic datasets.

| Method     | IPC | CLoM     |                  | Method       | IPC | CLoM     |                  |
|------------|-----|----------|------------------|--------------|-----|----------|------------------|
|            |     | ✗        | ✓                |              |     | ✗        | ✓                |
| <b>IDC</b> | 10  | 67.5±0.5 | <b>69.4</b> ±0.3 | <b>DREAM</b> | 10  | 69.4±0.4 | <b>70.0</b> ±0.4 |
|            | 50  | 74.5±0.1 | <b>76.3</b> ±0.2 |              | 50  | 74.8±0.1 | <b>76.1</b> ±0.1 |
| <b>DiM</b> | 10  | 66.2±0.5 | <b>67.3</b> ±0.5 | <b>TM</b>    | 10  | 65.3±0.7 | <b>66.3</b> ±0.4 |
|            | 50  | 72.6±0.4 | <b>72.7</b> ±0.3 |              | 50  | 71.6±0.2 | <b>73.1</b> ±0.2 |

<span id="page-14-0"></span>Table A1: The performance comparison to state-of-the-art methods on CIFAR-10. Underline denotes results from the original papers. Bold entries are best results.
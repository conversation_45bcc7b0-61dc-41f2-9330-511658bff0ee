**Remark 8.12.** Since  $c^{0,T}$  is not assumed to be nonnegative, the optimal transport problem (8.26) is not trivial.

**Remark 8.13.** If  $L$  does not depend on  $t$ , then one can apply the previous result for any  $T = 2^{-\ell}$ , and then use a compactness argument to construct a constant curve  $(\mu_t)_{t \in \mathbb{R}}$  satisfying Properties (i)–(vi) above. In particular  $\mu_0$  is a *stationary measure* for the Lagrangian system.

Before giving its proof, let me explain briefly why Theorem 8.11 is interesting from the point of view of the dynamics. A trajectory of the dynamical system defined by the Lagrangian L is a curve  $\gamma$ which is locally action-minimizing; that is, one can cover the timeinterval by small subintervals on which the curve is action-minimizing. It is a classical problem in mechanics to construct and study periodic trajectories having certain given properties. Theorem 8.11 does not construct a periodic trajectory, but at least it constructs a random trajectory  $\gamma$  (or equivalently a probability measure  $\Pi$  on the set of trajectories) which is periodic on average: The law  $\mu_t$  of  $\gamma_t$  satisfies  $\mu_{t+T} = \mu_t$ . This can also be thought of as a probability measure  $\Pi$  on the set of all possible trajectories of the system.

Of course this in itself is not too striking, since there may be a great deal of invariant measures for a dynamical system, and some of them are often easy to construct. The important point in the conclusion of Theorem 8.11 is that the curve  $\gamma$  is not "too random", in the sense that the random variable  $(\gamma(0), \dot{\gamma}(0))$  takes values in a Lipschitz graph. (If  $(\gamma(0), \dot{\gamma}(0))$ ) were a deterministic element in TM, this would mean that  $\Pi$  just sees a single periodic curve. Here we may have an infinite collection of curves, but still it is not "too large".)

Another remarkable property of the curves  $\gamma$  is the fact that the minimization property holds along *any* time-interval in  $\mathbb{R}$ , not necessarily small.

Example 8.14. Let M be a compact Riemannian manifold, and let  $L(x, v, t) = |v|^2/2 - V(x)$ , where V has a unique maximum  $x_0$ . Then Mather's procedure selects the probability measure  $\delta_{x_0}$ , and the stationary curve  $\gamma \equiv x_0$  (which is an unstable equilibrium).

It is natural to try to construct more "interesting" measures and curves by Mather's procedure. One way to do so is to change the Lagrangian, for instance replace  $L(x, v, t)$  by  $L_{\omega} := L(x, v, t) + \omega(x) \cdot v$ , where  $\omega$  is a vector field on M. Indeed,

- If  $\omega$  is closed (as a differential form), that is if  $\nabla \omega$  is a symmetric operator, then  $L_{\omega}$  and L have the same Euler–Lagrange equations, so the associated dynamical system is the same;
- If  $\omega$  is exact, that is if  $\omega = \nabla f$  for some function  $f : M \to \mathbb{R}$ , then  $L_{\omega}$  and L have the same minimizing curves.

As a consequence, one may explore various parts of the dynamics by letting  $\omega$  vary over the finite-dimensional group obtained by taking the quotient of the closed forms by the exact forms. In particular, one can make sure that the expected mean "rotation number"  $\mathbb{E}\left(\frac{1}{7}\right)$  $\frac{1}{T} \int_0^T \dot{\gamma} dt$ takes nontrivial values as  $\omega$  varies.

Proof of Theorem 8.11. I shall repeatedly use Proposition 7.16 and Theorem 7.21. First,  $C^{0,T}(\mu,\mu)$  is a lower semicontinuous function of  $\mu$ , bounded below by  $T(\inf L) > -\infty$ , so the minimization problem (8.26) does admit a solution.

Define  $\mu_0 = \mu_T = \overline{\mu}$ , then define  $\mu_t$  by displacement interpolation for  $0 < t < T$ , and extend the result by periodicity.

Let  $k \in \mathbb{N}$  be given and let  $\tilde{\mu}$  be a minimizer for the variational problem

$$
\inf_{\mu \in P(M)} C^{0,kT}(\mu, \mu).
$$

We shall see later that actually  $\overline{\mu}$  is a solution of this problem. For the moment, let  $(\widetilde{\mu}_t)_{t\in\mathbb{R}}$  be obtained first by taking a displacement interpolation between  $\tilde{\mu}_0 = \tilde{\mu}$  and  $\tilde{\mu}_{kT} = \tilde{\mu}$ ; and then by extending the result by kT-periodicity.

On the one hand,

$$
C^{0,kT}(\tilde{\mu}, \tilde{\mu}) \le C^{0,kT}(\mu_0, \mu_{kT}) \le \sum_{j=0}^{k-1} C^{jT, (j+1)T}(\mu_{jT}, \mu_{(j+1)T})
$$
  
$$
= k C^{0,1}(\overline{\mu}, \overline{\mu}).
$$
 (8.27)

On the other hand, by definition of  $\overline{\mu}$ ,

$$
C^{0,T}(\overline{\mu},\overline{\mu}) \le C^{0,T}\left(\frac{1}{k}\sum_{j=0}^{k-1} \widetilde{\mu}_{jT}, \frac{1}{k}\sum_{j=0}^{k-1} \widetilde{\mu}_{jT}\right)
$$
(8.28)

$$
= C^{0,T} \left( \frac{1}{k} \sum_{j=0}^{k-1} \widetilde{\mu}_{jT}, \frac{1}{k} \sum_{j=0}^{k-1} \widetilde{\mu}_{(j+1)T} \right). \tag{8.29}
$$

Since  $C^{0,T}(\mu,\nu)$  is a convex function of  $(\mu,\nu)$  (Theorem 4.8),

$$
C^{0,T}\left(\frac{1}{k}\sum_{j=0}^{k-1}\widetilde{\mu}_{jT},\frac{1}{k}\sum_{j=0}^{k-1}\widetilde{\mu}_{(j+1)T}\right) \leq \frac{1}{k}\sum_{j=0}^{k-1}C^{jT,(j+1)T}(\widetilde{\mu}_{jT},\widetilde{\mu}_{(j+1)T})
$$
$$
= \frac{1}{k}C^{0,kT}(\widetilde{\mu}_{0},\widetilde{\mu}_{kT}),\tag{8.30}
$$

where the last equality is a consequence of Property (ii) in Theorem 7.21. Inequalities (8.29) and (8.30) together imply

$$
C^{0,1}(\overline{\mu},\overline{\mu}) \leq \frac{1}{k} C^{0,kT}(\widetilde{\mu}_0,\widetilde{\mu}_{kT}) = \frac{1}{k} C^{0,kT}(\widetilde{\mu},\widetilde{\mu}).
$$

Since the reverse inequality holds true by (8.27), in fact all the inequalities in (8.27), (8.29) and (8.30) have to be equalities. In particular,

$$
C^{0,kT}(\mu_0, \mu_{kT}) = \sum_{j=0}^{k-1} C^{jT, (j+1)T}(\mu_{jT}, \mu_{(j+1)T}).
$$
 (8.31)

Let us now check that the identity

$$
C^{t_1,t_2}(\mu_{t_1}, \mu_{t_2}) + C^{t_2,t_3}(\mu_{t_2}, \mu_{t_3}) = C^{t_1,t_3}(\mu_{t_1}, \mu_{t_3})
$$
\n(8.32)

holds true for any three intermediate times  $t_1 < t_2 < t_3$ . By periodicity, it suffices to do this for  $t_1 \geq 0$ . If  $0 \leq t_1 < t_2 < t_3 \leq T$ , then  $(8.32)$ is true by the property of displacement interpolation (Theorem 7.21 again). If  $jT \le t_1 < t_2 < t_3 \le (j+1)T$ , this is also true because of the T-periodicity. In the remaining cases, we may choose  $k$  large enough that  $t_3 \leq kT$ . Then

$$
C^{0,kT}(\mu_0, \mu_{kT}) \le C^{0,t_1}(\mu_0, \mu_{t_1}) + C^{t_1,t_3}(\mu_{t_1}, \mu_{t_3}) + C^{t_3,kT}(\mu_{t_3}, \mu_{kT})
$$

$$
\le C^{0,t_1}(\mu_0, \mu_{t_1}) + C^{t_1,t_2}(\mu_{t_1}, \mu_{t_2}) + C^{t_2,t_3}(\mu_{t_2}, \mu_{t_3}) + C^{t_3,kT}(\mu_{t_3}, \mu_{kT})
$$

$$
\le \sum_{j} C^{s_j,s_{j+1}}(\mu_{s_j}, \mu_{s_{j+1}})
$$

$$
(8.33)
$$

where the times  $s_j$  are obtained by ordering of  $\{0, T, 2T, \ldots, kT\}$  $\{t_1,t_2,t_3\}$ . On each time-interval  $[\ell T,(\ell+1)T]$  we know that  $(\mu_t)$  is a displacement interpolation, so we can apply Theorem 7.21(ii), and as a result bound the right-hand side of (8.33) by

$$
\sum_{\ell} C^{\ell T, (\ell+1)T} \left( \mu_{\ell T}, \mu_{(\ell+1)T} \right). \tag{8.34}
$$

(Consider for instance the particular case when  $0 < t_1 < t_2 < T <$  $t_3 < 2T$ ; then one can write  $C^{0,t_1} + C^{t_1,t_2} + C^{t_2,T} = C^{0,T}$ , and also  $C^{T,t_3} + C^{t_3,2T} = C^{T,2T}$ . So  $C^{0,t_1} + C^{t_1,t_2} + C^{t_2,T} + C^{T,t_3} + C^{t_3,2T} =$  $C^{0,T} + C^{T,2T}.$ 

But (8.34) is just  $C^{0,kT}(\mu_0, \mu_{kT})$ , as shown in (8.31). So there is in fact equality in all these inequalities, and (8.32) follows. Then by Theorem 7.21,  $(\mu_t)$  defines a displacement interpolation between any two of its intermediate values. This proves (i). At this stage we have also proven (iii) in the case when  $t_0 = 0$ .

Now for any  $t \in \mathbb{R}$ , one has, by (8.32) and the T-periodicity,

$$
C^{0,T}(\mu_0, \mu_T) = C^{0,t}(\mu_0, \mu_t) + C^{t,T}(\mu_t, \mu_T)
$$

$$
= C^{t,T}(\mu_t, \mu_T) + C^{T,t+T}(\mu_T, \mu_{t+T})
$$

$$
= C^{t,t+T}(\mu_t, \mu_{t+T})
$$

$$
= C^{t,t+T}(\mu_t, \mu_t)
$$

which proves (ii).

Next, let  $t_0$  be given, and repeat the same whole procedure with the initial time 0 replaced by  $t_0$ : That is, introduce a minimizer  $\tilde{\mu}$  for  $C^{t_0,t_0+T}(\mu,\mu)$ , etc. This gives a curve  $(\tilde{\mu}_t)_{t\in\mathbb{R}}$  with the property that  $C^{t,t+T}(\tilde{\mu}_t, \tilde{\mu}_t) = C^{0,T}(\tilde{\mu}_0, \tilde{\mu}_0)$ . It follows that

$$
C^{t_0,t_0+T}(\mu_{t_0}, \mu_{t_0}) = C^{0,T}(\overline{\mu}, \overline{\mu}) \leq C^{0,T}(\widetilde{\mu}_0, \widetilde{\mu}_0)
$$
  
=  $C^{t_0,t_0+T}(\widetilde{\mu}_{t_0}, \widetilde{\mu}_{t_0}) = C^{t_0,t_0+T}(\widetilde{\mu}, \widetilde{\mu}) \leq C^{t_0,t_0+T}(\mu_{t_0}, \mu_{t_0}).$ 

So there is equality everywhere, and  $\mu_{t_0}$  is indeed a minimizer for  $C^{t_0,t_0+T}(\mu,\mu)$ . This proves the remaining part of (iii).

Next, let  $(\gamma_t)_{0 \leq t \leq T}$  be a random minimizing curve on  $[0, T]$  with law  $(\gamma_t) = \mu_t$ , as in Theorem 7.21. For each k, define  $(\gamma_t^k)_{kT \le t \le (k+1)T}$  as a copy of  $(\gamma_t)_{0 \le t \le T}$ . Since  $\mu_t$  is T-periodic, law  $(\gamma_{kT}^k) = \text{law } (\gamma_{(k+1)T}^k) =$  $\mu_0$ , for all k. So we can glue together these random curves, just as in the proof of Theorem 7.30, and get random curves  $(\gamma_t)_{t \in \mathbb{R}}$  such that law  $(\gamma_t) = \mu_t$  for all  $t \in \mathbb{R}$ , and each curve  $(\gamma_t)_{kT \le t \le (k+1)T}$  is actionminimizing. Property (iv) is then satisfied by construction.

Property (v) can be established by a principle which was already used in the proof of Theorem 7.21. Let us check for instance that  $\gamma$  is minimizing on  $[0, 2T]$ . For this one has to show that (almost surely)

$$
c^{t_1,t_2}(\gamma_{t_1}, \gamma_{t_2}) + c^{t_2,t_3}(\gamma_{t_2}, \gamma_{t_3}) = c^{t_1,t_3}(\gamma_{t_1}, \gamma_{t_3}), \tag{8.35}
$$

for any choice of intermediate times  $t_1 < t_2 < t_3$  in [0,2T]. Assume, without real loss of generality, that  $0 < t_1 < t_2 < T < t_3 < 2T$ . Then

$$
C^{t_1,t_3}(\mu_{t_1}, \mu_{t_3}) \leq \mathbb{E} \, c^{t_1,t_3}(\gamma_{t_1}, \gamma_{t_3})
$$
  

$$
\leq \mathbb{E} \left[ c^{t_1,t_2}(\gamma_{t_1}, \gamma_{t_2}) + c^{t_2,t_3}(\gamma_{t_2}, \gamma_{t_3}) \right]
$$
  

$$
\leq \mathbb{E} \, c^{t_1,t_2}(\gamma_{t_1}, \gamma_{t_2}) + \mathbb{E} \, c^{t_2,T}(\gamma_{t_2}, \gamma_T) + \mathbb{E} \, c^{T,t_3}(\gamma_T, \gamma_{t_3})
$$
  

$$
= C^{t_1,t_2}(\mu_{t_1}, \mu_{t_2}) + C^{t_2,T}(\mu_{t_2}, \mu_T) + C^{T,t_3}(\mu_T, \mu_{t_3})
$$
  

$$
= C^{t_1,t_3}(\mu_{t_1}, \mu_{t_3}),
$$

where the property of optimality of the path  $(\mu_t)_{t\in\mathbb{R}}$  was used in the last step. So all these inequalities are equalities, and in particular

$$
\mathbb{E}\left[c^{t_1,t_3}(\gamma_{t_1},\gamma_{t_3})-c^{t_1,t_2}(\gamma_{t_1},\gamma_{t_2})-c^{t_2,t_3}(\gamma_{t_2},\gamma_{t_3})\right]=0.
$$

Since the integrand is nonpositive, it has to vanish almost surely. So  $(8.35)$  is satisfied almost surely, for given  $t_1, t_2, t_3$ . Then the same inequality holds true almost surely for all choices of rational times  $t_1, t_2, t_3$ ; and by continuity of  $\gamma$  it holds true almost surely at all times. This concludes the proof of (v).

From general principles of Lagrangian mechanics, there is a uniform bound on the speeds of all the curves  $(\gamma_t)_{-T \leq t \leq T}$  (this is because  $\gamma_{-T}$ and  $\gamma_T$  lie in a compact set). So for any given  $\varepsilon > 0$  we can find  $\delta$ such that  $0 \le t \le \delta$  implies  $d(\gamma_0, \gamma_t) \le \varepsilon$ . Then if  $\varepsilon$  is small enough the map  $(\gamma_0, \gamma_t) \rightarrow (\gamma_0, \dot{\gamma}_0)$  is Lipschitz. (This is another well-known fact in Lagrangian mechanics; it can be seen as a consequence of Remark 8.10.) But from Theorem 8.5, applied with the intermediate time  $t_0 = 0$ on the time-interval  $[-T, T]$ , we know that  $\gamma_0 \mapsto \gamma_t$  is well-defined (almost surely) and Lipschitz continuous. It follows that  $\gamma_0 \rightarrow \dot{\gamma}_0$  is also Lipschitz continuous. This concludes the proof of Theorem 8.11. ⊓⊔

The story does not end here. First, there is a powerful *dual point* of view to Mather's theory, based on solutions to the dual Kantorovich problem; this is a maximization problem defined by

$$
\sup \int (\phi - \psi) d\mu, \tag{8.36}
$$

where the supremum is over all probability measures  $\mu$  on  $M$ , and all pairs of Lipschitz functions  $(\psi, \phi)$  such that

$$
\forall (x, y) \in M \times M, \qquad \phi(y) - \psi(x) \le c^{0,T}(x, y).
$$

Next, Theorem 8.11 suggests that some objects related to optimal transport might be interesting to describe a Lagrangian system. This is indeed the case, and the notions defined below are useful and wellknown in the theory of dynamical systems:

Definition 8.15 (Useful transport quantities describing a Lagrangian system). For each displacement interpolation  $(\mu_t)_{t>0}$  as in Theorem 8.11, define

(i) the Mather critical value as the opposite of the mean optimal transport cost:

$$
-M = \overline{c} := \frac{1}{T} C^{0,T}(\mu, \mu) = \frac{1}{kT} C^{0,kT}(\mu, \mu); \tag{8.37}
$$

(ii) the Mather set as the closure of the union of the supports of all measures  $V_{\#}\mu_0$ , where  $(\mu_t)_{t>0}$  is a displacement interpolation as in Theorem 8.11 and V is the Lipschitz map  $\gamma_0 \rightarrow (\gamma_0, \dot{\gamma}_0);$ 

(iii) the Aubry set as the set of all  $(\gamma_0, \dot{\gamma}_0)$  such that there is a solution  $(\phi, \psi)$  of the dual problem (8.36) satisfying  $H_+^{0,T} \psi(\gamma_1) - \psi(\gamma_0) =$  $c^{0,T}(\gamma_0,\gamma_1).$ 

Up to the change of variables  $(\gamma_0, \dot{\gamma}_0) \rightarrow (\gamma_0, \gamma_1)$ , the Mather and Aubry sets are just the same as  $\Gamma_{\rm min}$  and  $\Gamma_{\rm max}$  appearing in the bibliographical notes of Chapter 5.

Example 8.16. Take a one-dimensional pendulum. For small values of the total energy, the pendulum is confined in a periodic motion, making just small oscillations, going back and forth around its equilibrium position and describing an arc of circle in physical space (see Figure 8.5). For large values, it also has a periodic motion but now it goes always in the same direction, and describes a complete circle ("revolution") in physical space. But if the system is given just the right amount of energy, it will describe a trajectory that is intermediate between these two regimes, and consists in going from the vertical upward position (at time  $-\infty$ ) to the vertical upward position again (at time  $+\infty$ ) after exploring all intermediate angles. There are two such trajectories (one clockwise, and one counterclockwise), which can be called revolutions of infinite period; and they are globally action-minimizing. When  $\xi = 0$ , the solution of the Mather problem is just the Dirac mass on the unstable equilibrium  $x_0$ , and the Mather and Aubry sets  $\Gamma$  are reduced to  $\{(x_0, x_0)\}\.$  When  $\xi$  varies in  $\mathbb R$ , this remains the same until  $\xi$  reaches

a certain critical value; above that value, the Mather measures are supported by revolutions. At the critical value, the Mather and Aubry sets differ: the Aubry set (viewed in the variables  $(x, v)$ ) is the union of the two revolutions of infinite period.

Image /page/6/Figure/2 description: The image displays two diagrams illustrating pendulum motion. The left diagram shows a simple pendulum with a solid black circle representing the bob at its lowest point, and a hollow white circle representing the bob at its extreme left position. Dashed lines indicate the path of the pendulum's swing, and double-headed arrows show the direction of motion. The right diagram depicts a pendulum swinging in a circular path, indicated by a dashed circle. A solid black circle represents the bob at one point in its circular trajectory, with a solid line representing the pendulum rod. An arrow on the dashed circle shows the direction of rotation.

Fig. 8.5. In the left figure, the pendulum oscillates with little energy between two extreme positions; its trajectory is an arc of circle which is described clockwise, then counterclockwise, then clockwise again, etc. On the right figure, the pendulum has much more energy and draws complete circles again and again, either clockwise or counterclockwise.

The dual point of view in Mather's theory, and the notion of the Aubry set, are intimately related to the so-called weak KAM theory, in which stationary solutions of Hamilton–Jacobi equations play a central role. The next theorem partly explains the link between the two theories.

Theorem 8.17 (Mather critical value and stationary Hamilton– **Jacobi equation).** With the same notation as in Theorem 8.11, assume that the Lagrangian L does not depend on t, and let  $\psi$  be a Lipschitz function on M, such that  $H_+^{0,t} \psi = \psi + ct$  for all times  $t \geq 0$ ; that is,  $\psi$  is left invariant by the forward Hamilton–Jacobi semigroup, except for the addition of a constant which varies linearly in time. Then, necessarily  $c = \overline{c}$ , and the pair  $(\psi, H_+^{0,T} \psi) = (\psi, \psi + \overline{c}T)$  is optimal in the dual Kantorovich problem with cost function  $c^{0,T}$ , and initial and final measures equal to  $\overline{\mu}$ .

**Remark 8.18.** The equation  $H_+^{0,1}\psi = \psi + ct$  is a way to reformulate the stationary Hamilton–Jacobi equation  $H(x, \nabla \psi(x)) + c = 0$ . Yet another reformulation would be obtained by changing the forward Hamilton–Jacobi semigroup for the backward one. Theorem 8.17 does

not guarantee the existence of such stationary solutions, it just states that *if* such solutions exist, then the value of the constant  $c$  is uniquely determined and can be related to a Monge–Kantorovich problem. In weak KAM theory, one then establishes the existence of these solutions by independent means; see the references suggested in the bibliographical notes for much more information.

**Remark 8.19.** The constant  $-\overline{c}$  (which coincides with Mather's critical value) is often called the effective Hamiltonian of the system.

*Proof of Theorem 8.17.* To fix the ideas, let us impose  $T = 1$ . Let  $\psi$  be such that  $H_+^{0,1}\psi = \psi + c$ , and let  $\mu$  be any probability measure on M; then

$$
\int (H_+^{0,1}\psi) d\mu - \int \psi d\mu = \int c d\mu = c.
$$

By the easy part of the Kantorovich duality,  $C^{0,1}(\mu,\mu) \geq c$ . By taking the infimum over all  $\mu \in P(M)$ , we conclude that  $\bar{c} \geq c$ .

To prove the reverse inequality, it suffices to construct a particular probability measure  $\mu$  such that  $C^{0,1}(\mu,\mu) \leq c$ . The idea is to look for  $\mu$ as a limit of probability measures distributed uniformly over some wellchosen long minimizing trajectories. Before starting this construction, we first remark that since  $M$  is compact, there is a uniform bound  $C$  on  $L(\gamma(t), \dot{\gamma}(t))$ , for all action-minimizing curves  $\gamma : [0, 1] \to M$ ; and since L is time-independent, this statement trivially extends to all actionminimizing curves defined on time-intervals  $[t_0,t_1]$  with  $|t_0-t_1|\geq 1$ . Also  $\psi$  is uniformly bounded on M.

Now let x be an arbitrary point in M; for any  $T > 0$  we have, by definition of the forward Hamilton–Jacobi semigroup,

$$
(H_+^{-T,0}\psi)(x) = \inf \left\{ \psi(\gamma(-T)) + \int_{-T}^0 L(\gamma(s), \dot{\gamma}(s)) ds; \quad \gamma(0) = x \right\},\,
$$

where the infimum is over all action-minimizing curves  $\gamma : [-T, 0] \to M$ ending at  $x$ . (The advantage in working with negative times is that one can fix one of the endpoints; in the present context where  $M$  is compact this is nonessential, but it would become important if  $M$  were noncompact.) By compactness, there is a minimizing curve  $\gamma = \gamma^{(T)}$ ; then, by the definition of  $\gamma^{(T)}$  and the stationarity of  $\psi$ ,

Introduction to Mather's theory 203

$$
\frac{1}{T} \int_{-T}^{0} L(\gamma^{(T)}(s), \dot{\gamma}^{(T)}(s)) ds = \frac{1}{T} \Big[ (H_{+}^{-T,0}\psi)(x) - \psi(\gamma^{(T)}(-T)) \Big]
$$

$$
= \frac{1}{T} \Big( \psi(x) + cT - \psi(\gamma^{(T)}(-T)) \Big)
$$

$$
= c + O\left(\frac{1}{T}\right).
$$

In the sequel, I shall write just  $\gamma$  for  $\gamma^{(T+1)}$ . Of course the estimate above remains unchanged upon replacement of  $T$  by  $T + 1$ , so

$$
\frac{1}{T} \int_{-(T+1)}^{0} L(\gamma(s), \dot{\gamma}(s)) ds = c + O\left(\frac{1}{T}\right).
$$

Then define

$$
\mu_T := \frac{1}{T} \int_{-(T+1)}^{-1} \delta_{\gamma(s)} ds; \qquad \nu_T := \frac{1}{T} \int_{-T}^0 \delta_{\gamma(s)} ds;
$$

and  $\theta : \gamma(s) \longmapsto \gamma(s+1)$ . It is clear that  $\theta_{\#}\mu_T = \nu_T$ ; moreover,

$$
c^{0,1}(\gamma(s),\theta(\gamma(s))) = c^{0,1}(\gamma(s),\gamma(s+1)) = \int_s^{s+1} L(\gamma(u),\dot{\gamma}(u)) du.
$$

Thus by Theorem 4.8,

$$
C^{0,1}(\mu_T, \nu_T) \leq \frac{1}{T} \int_{-(T+1)}^{-1} c^{0,1}(\gamma(s), \theta(\gamma(s))) ds
$$
  
= 
$$
\frac{1}{T} \int_{-(T+1)}^{-1} \left( \int_s^{s+1} L(\gamma(u), \dot{\gamma}(u)) du \right) ds
$$
  
= 
$$
\frac{1}{T} \int_{-(T+1)}^0 L(\gamma(u), \dot{\gamma}(u)) a(u) du,
$$
 (8.38)

where  $a:[-(T+1),0]\to [0,1]$  is defined by

$$
a(u) = 
\int 1_{s \le u \le s+1} ds = \begin{cases} 1 & \text{if } -T \le u \le -1; \\ -u & \text{if } -1 \le u \le 0; \\ u + T + 1 & \text{if } -(T + 1) \le u \le -T. \end{cases}
$$

Replacing  $a$  by 1 in the integrand of  $(8.38)$  involves modifying the integral on a set of measure at most 2; so

$$
C^{0,1}(\mu_T, \nu_T) \le \frac{1}{T} \int_{-(T+1)}^0 L(\gamma(u), \dot{\gamma}(u)) du + O\left(\frac{1}{T}\right) = c + O\left(\frac{1}{T}\right). \tag{8.39}
$$

Since  $P(M)$  is compact, the family  $(\mu_T)_{T \in \mathbb{N}}$  converges, up to extraction of a subsequence, to some probability measure  $\mu$ . Then (up to extraction of the same subsequence)  $\nu_T$  also converges to  $\mu$ , since

$$
\left\|\mu_T - \nu_T\right\|_{TV} = \frac{1}{T} \left\| \int_{-(T+1)}^{-T} \delta_{\gamma(s)} \, ds + \int_{-1}^0 \delta_{\gamma(s)} \, ds \right\|_{TV} \le \frac{2}{T}.
$$

Then from (8.39) and the lower semicontinuity of the optimal transport cost,

$$
C^{0,1}(\mu, \mu) \le \liminf_{T \to \infty} C^{0,1}(\mu_T, \nu_T) \le c.
$$

This concludes the proof. □

The next exercise may be an occasion to manipulate the concepts introduced in this section.

Exercise 8.20. With the same assumptions as in Theorem 8.11, assume that L is symmetric in v; that is,  $L(x, -v, t) = L(x, v, t)$ . Show that  $c^{0,T}(x,y) = c^{0,T}(y,x)$ . Take an optimal measure  $\overline{\mu}$  for the minimization problem (8.26), and let  $\pi$  be an associated optimal transference plan. By gluing together  $\pi$  and  $\check{\pi}$  (obtained by exchanging the variables x and y), construct an optimal transference plan for the problem  $(8.26)$  with T replaced by 2T, such that each point x stays in place. Deduce that the curves  $\gamma$  are 2T-periodic. Show that  $c^{0,2T}(x,x) = C^{0,2T}(\overline{\mu}, \overline{\mu})$ , and deduce that  $c^{0,T}(x,y)$  is  $\pi$ -almost surely constant. Construct  $\psi$  such that  $H_+^{0,2T} \psi = \psi + 2\overline{c}T$ ,  $\overline{\mu}$ -almost surely. Next assume that  $L$  does not depend on  $t$ , and use a compactness argument to construct a  $\psi$  and a stationary measure  $\mu$ , such that  $H_+^{\bar{0},t}\psi = \psi + \bar{c}t$ , for all  $t \geq 0$ ,  $\bar{\mu}$ -almost surely. Note that this is far from proving the existence of a stationary solution of the Hamilton– Jacobi equation, as appearing in Theorem 8.17, for two reasons: First the symmetry of  $L$  is a huge simplification; secondly the equation  $H^{0,t}_+\psi = \psi + \overline{c} t$  should hold everywhere in M, not just  $\overline{\mu}$ -almost surely.

# Possible extensions of Mather's estimates

As noticed in Example 8.4, it would be desirable to have a sharper version of Theorem 8.1 which would contain as a special case the correct exponents for the Lagrangian function  $L(x, v, t) = |v|^{1+\alpha}, 0 < \alpha < 1.$ 

But even for a "uniformly convex" Lagrangian there are several extensions of Theorem 8.1 which would be of interest, such as (a) getting rid of the compactness assumption, or at least control the dependence of constants at infinity; and (b) getting rid of the smoothness assumptions. I shall discuss both problems in the most typical case  $L(x, v, t) = |v|^2$ , i.e.  $c(x, y) = d(x, y)^2$ .

Intuitively, Mather's estimates are related to the local behavior of geodesics (they should not diverge too fast), and to the convexity properties of the square distance function  $d^2(x_0, \cdot)$ . Both features are well captured by lower bounds on the sectional curvature of the manifold. There is by chance a generalized notion of sectional curvature bounds, due to Alexandrov, which makes sense in a general metric space, without any smoothness; metric spaces which satisfy these bounds are called Alexandrov spaces. (This notion will be explained in more detail in Chapter 26.) In such spaces, one could hope to solve problems (a) and (b) at the same time. Although the proofs in the present chapter strongly rely on smoothness, I would be ready to believe in the following statement (which might be not so difficult to prove):

**Open Problem 8.21.** Let  $(X, d)$  be an Alexandrov space with curvature bounded below by  $K \in \mathbb{R}$ , and let  $x_1, x_2, y_1, y_2$  be four points in X such that

$$
d(x_1, y_1)^2 + d(x_2, y_2)^2 \le d(x_1, y_2)^2 + d(x_2, y_1)^2.
$$

Further, let  $\gamma_1$  and  $\gamma_2$  be two constant-speed geodesics respectively joining  $x_1$  to  $y_1$  and  $x_2$  to  $y_2$ . Then, for any  $t_0 \in (0,1)$ , there is a constant  $C_{t_0}$ , depending only on  $K$ ,  $t_0$ , and on an upper bound on all the distances involved, such that

$$
\sup_{0 \le t \le 1} d(\gamma_1(t), \gamma_2(t)) \le C_{t_0} d(\gamma_1(t_0), \gamma_2(t_0)).
$$

To conclude this discussion, I shall mention a much rougher "shortening lemma", which has the advantage of holding true in general metric spaces, even without curvature bounds. In such a situation, in general there may be branching geodesics, so a bound on the distance at

one intermediate time is clearly not enough to control the distance between the positions along the whole geodesic curves. One cannot hope either to control the distance between the velocities of these curves, since the velocities might not be well-defined. On the other hand, we may take advantage of the property of preservation of speed along the minimizing curves, since this remains true even in a nonsmooth context. The next theorem exploits this to show that if geodesics in a displacement interpolation pass near each other at some intermediate time, then their *lengths* have to be approximately equal.

Theorem 8.22 (A rough nonsmooth shortening lemma). Let  $(\mathcal{X}, d)$  be a metric space, and let  $\gamma_1$ ,  $\gamma_2$  be two constant-speed, minimizing geodesics such that

$$
d(\gamma_1(0), \gamma_1(1))^2 + d(\gamma_2(0), \gamma_2(1))^2 \leq d(\gamma_1(0), \gamma_2(1))^2 + d(\gamma_2(0), \gamma_1(1))^2.
$$

Let  $L_1$  and  $L_2$  stand for the respective lengths of  $\gamma_1$  and  $\gamma_2$ , and let D be a bound on the diameter of  $(\gamma_1 \cup \gamma_2)([0,1])$ . Then

$$
|L_1 - L_2| \le \frac{C\sqrt{D}}{\sqrt{t_0(1 - t_0)}} \sqrt{d(\gamma_1(t_0), \gamma_2(t_0))},
$$

for some numeric constant C.

*Proof of Theorem 8.22.* Write  $d_{12} = d(x_1, y_2), d_{21} = d(x_2, y_1), X_1 =$  $\gamma_1(t_0)$ ,  $X_2 = \gamma_2(t_0)$ . From the minimizing assumption, the triangle inequality and explicit calculations,

$$
0 \le d_{12}^2 + d_{21}^2 - L_1^2 - L_2^2
$$
  

$$
\le \left( d(x_1, X_1) + d(X_1, X_2) + d(X_2, y_2) \right)^2
$$
  

$$
+ \left( d(x_2, X_2) + d(X_2, X_1) + d(X_1, y_1) \right)^2
$$
  

$$
= \left( t_0 L_1 + d(X_1, X_2) + (1 - t_0) L_2 \right)^2
$$
  

$$
+ \left( t_0 L_2 + d(X_1, X_2) + (1 - t_0) L_1 \right)^2 - L_1^2 - L_2^2
$$
  

$$
= 2 d(X_1, X_2) \left( L_1 + L_2 + d(X_1, X_2) \right) - 2 t_0 (1 - t_0) (L_1 - L_2)^2.
$$

As a consequence,

$$
|L_1 - L_2| \le \sqrt{\frac{L_1 + L_2 + d(X_1, X_2)}{t_0(1 - t_0)}} \sqrt{d(X_1, X_2)},
$$

and the proof is complete. □

# Appendix: Lipschitz estimates for power cost functions

The goal of this Appendix is to prove the following shortening lemma for the cost function  $c(x, y) = |x - y|^{1+\alpha}$  in Euclidean space.

Theorem 8.23 (Shortening lemma for power cost functions). Let  $\alpha \in (0,1)$ , and let  $x_1, y_1, x_2, y_2$  be four points in  $\mathbb{R}^n$ , such that

$$
|x_1 - y_1|^{1+\alpha} + |x_2 - y_2|^{1+\alpha} \le |x_1 - y_2|^{1+\alpha} + |x_2 - y_1|^{1+\alpha}.
$$
 (8.40)

Further, let

$$
\gamma_1(t) = (1-t)x_1 + ty_1, \qquad \gamma_2(t) = (1-t)x_2 + ty_2.
$$

Then, for any  $t_0 \in (0,1)$  there is a constant  $K = K(\alpha, t_0) > 0$  such that

$$
|\gamma_1(t_0) - \gamma_2(t_0)| \ge K \sup_{0 \le t \le 1} |\gamma_1(t) - \gamma_2(t)|.
$$

Remark 8.24. The proof below is not constructive, so I won't have any quantitative information on the best constant  $K(\alpha,t)$ . It is natural to think that for each fixed t, the constant  $K(\alpha,t)$  (which only depends on  $\alpha$ ) will go to 0 as  $\alpha \downarrow 0$ . When  $\alpha = 0$ , the conclusion of the theorem is false: Just think of the case when  $x_1, y_1, x_2, y_2$  are aligned. But this is the only case in which the conclusion fails, so it might be that a modified statement still holds true.

Proof of Theorem 8.23. First note that it suffices to work in the affine space generated by  $x_1, y_1, x_2, y_2$ , which is of dimension at most 3; hence all the constants will be independent of the dimension  $n$ . For notational simplicity, I shall assume that  $t_0 = 1/2$ , which has no important influence on the computations. Let  $X_1 := \gamma_1(1/2), X_2 := \gamma_2(1/2)$ . It is sufficient to show that

$$
|x_1 - x_2| + |y_1 - y_2| \le C |X_1 - X_2|
$$

for some constant C, independent of  $x_1, x_2, y_1, y_2$ .

Step 1: Reduction to a compact problem by invariance. Exchanging the roles of  $x$  and  $y$  if necessary, we might assume that  $|x_2 - y_2| \le |x_1 - y_1|$ , and then by translation invariance that  $x_1 = 0$ , by homogeneity that  $|x_1 - y_1| = 1$  (treat separately the trivial case  $x_1 = y_1$ , and by rotation invariance that  $y_1 = e$  is a fixed unit vector.

Let  $R := |x_2|$ , then  $|y_2 - x_2| \le 1$  implies  $|x_2 - X_2| \le 1/2$ , so  $|X_2| \ge R-1/2$ , and since  $|X_1| \le 1/2$ , it follows that  $|X_1 - X_2| \ge R-1$ . On the other hand,  $|x_1-x_2| = R$  and  $|y_1-y_2| \leq R+1$ . So the conclusion is obvious if  $R \geq 2$ . Otherwise,  $|x_2|$  and  $|y_2|$  lie in the ball  $B_3(0)$ .

Step 2: Reduction to a perturbation problem by com**pactness.** For any positive integer k, let  $(x_2^{(k)})$  $\binom{k}{2}, \binom{k}{2}$  $\binom{(\kappa)}{2}$  be such that  $(|x_1-x_2|+|y_1-y_2|)/|X_1-X_2|$  is minimized by  $(x_1, y_1, x_2^{(k)})$  $_{2}^{(k)},y_{2}^{(k)}$  $\binom{\binom{\kappa}{2}}{2}$  under the constraint  $|X_1 - X_2| \geq k^{-1}$ .

By compactness, such a configuration does exist, and the value  $I_k$ of the infimum goes down with  $k$ , and converges to

$$
I := \inf \left( \frac{|x_1 - x_2| + |y_1 - y_2|}{|X_1 - X_2|} \right), \tag{8.41}
$$

where the infimum is taken over all configurations such that  $X_1 \neq X_2$ . The strict convexity of  $x \rightarrow |x|^{1+\alpha}$  together with inequality (8.40) prevent  $X_1 = X_2$ , unless  $(x_1, y_1) = (x_2, y_2)$ , in which case there is nothing to prove. So it is sufficient to show that  $I > 0$ .

Since the sequence  $(x_2^{(k)})$  $\binom{k}{2}, \binom{k}{2}$  $\binom{k}{2}$  takes values in a compact set, there is a subsequence thereof (still denoted  $(x_2^{(k)})$  $\binom{k}{2}, \binom{k}{2}$  $\binom{k}{2}$ ) which converges to some  $(x_2^{(\infty)}, y_2^{(\infty)})$ . By continuity, condition (8.40) holds true with  $(x_2, y_2) = (x_2^{(\infty)}, y_2^{(\infty)})$ . If (with obvious notation)  $|X_1 - X_2^{(\infty)}| > 0$ , then the configuration  $(x_1, y_1, x_2^{(\infty)}, y_2^{(\infty)})$  achieves the minimum I in (8.41), and that minimum is positive. So the only case there remains to treat is when  $X_2^{(\infty)} = X_1$ . Then, by strict convexity, condition (8.40) imposes  $x_2^{(\infty)} = x_1, y_2^{(\infty)} = y_1$ . Equivalently,  $x_2^{(k)}$  $\binom{k}{2}$  converges to  $x_1$ , and  $y_2^{(k)}$  $\int_{2}^{\infty}$  to  $y_1$ . All this shows that it suffices to treat the case when  $x_2$  is very close to  $x_1$  and  $y_2$  is very close to  $y_1$ .

Step 3: Expansions. Now let

$$
x_2 = x_1 + \delta x, \qquad y_2 = y_1 + \delta y,\tag{8.42}
$$

where  $\delta x$  and  $\delta y$  are vectors of small norm (recall that  $x_1 - y_1$  has unit norm). Of course

Appendix: Lipschitz estimates for power cost functions 209

$$
X_1 - X_2 = \frac{\delta x + \delta y}{2}
$$
,  $x_2 - x_1 = \delta x$ ,  $y_2 - y_1 = \delta y$ ;

so to conclude the proof it is sufficient to show that

$$
\left|\frac{\delta x + \delta y}{2}\right| \ge K(|\delta x| + |\delta y|),\tag{8.43}
$$

as soon as  $|\delta x|$  and  $|\delta y|$  are small enough, and (8.40) is satisfied.

By using the formulas  $|a+b|^2 = |a|^2 + 2\langle a,b\rangle + |b|^2$  and

$$
(1+\varepsilon)^{\frac{1+\alpha}{2}} = 1 + \frac{(1+\alpha)}{2} \varepsilon - \frac{(1+\alpha)(1-\alpha)}{8} \varepsilon^2 + O(\varepsilon^3),
$$

one easily deduces from (8.40) that

$$
|\delta x - \delta y|^2 - |\delta x|^2 - |\delta y|^2 \le (1 - \alpha) \Big[ \langle \delta x - \delta y, e \rangle^2 - \langle \delta x, e \rangle^2 - \langle \delta y, e \rangle^2 \Big] + O\Big( |\delta x|^3 + |\delta y|^3 \Big).
$$

This can be rewritten

$$
\langle \delta x, \delta y \rangle - (1 - \alpha) \langle \delta x, e \rangle \langle \delta y, e \rangle \ge O(|\delta x|^3 + |\delta y|^3).
$$

Consider the new scalar product

$$
\langle v, w \rangle := \langle v, w \rangle - (1 - \alpha) \langle v, e \rangle \langle w, e \rangle
$$

(which is indeed a scalar product because  $\alpha > 0$ ), and denote the associated norm by  $||v||$ . Then the above conclusion can be summarized into

$$
\langle \langle \delta x, \delta y \rangle \rangle \ge O\big( \|\delta x\|^3 + \|\delta y\|^3 \big). \tag{8.44}
$$

It follows that

$$
\left\| \frac{\delta x + \delta y}{2} \right\|^2 = \frac{1}{4} \left( \|\delta x\|^2 + \|\delta y\|^2 + 2 \langle \langle \delta x, \delta y \rangle \rangle \right)
$$
  
$$
\geq \frac{1}{4} (\|\delta x\|^2 + \|\delta y\|^2) + O(\|\delta x\|^3 + \|\delta y\|^3).
$$

So inequality (8.43) is indeed satisfied if  $|\delta x| + |\delta y|$  is small enough. □

**Exercise 8.25.** Extend this result to the cost function  $d(x, y)^{1+\alpha}$  on a Riemannian manifold, when  $\gamma$  and  $\tilde{\gamma}$  stay within a compact set.

Hints: This tricky exercise is only for a reader who feels very comfortable. One can use a reasoning similar to that in Step 2 of the above proof, introducing a sequence  $(\gamma^{(k)}, \tilde{\gamma}^{(k)})$  which is asymptotically the "worst possible", and converges, up to extraction of a subsequence, to  $(\gamma^{(\infty)}, \widetilde{\gamma}^{(\infty)})$ . There are three cases: (i)  $\gamma^{(\infty)}$  and  $\widetilde{\gamma}^{(\infty)}$  are distinct geodesic curves which cross; this is ruled out by Theorem 8.1. (ii)  $\gamma^{(k)}$ and  $\widetilde{\gamma}^{(k)}$  converge to a point; then everything becomes local and one can use the result in  $\mathbb{R}^n$ , Theorem 8.23. (iii)  $\gamma^{(k)}$  and  $\widetilde{\gamma}^{(k)}$  converge to a nontrivial geodesic  $\gamma^{(\infty)}$ ; then these curves can be approximated by infinitesimal perturbations of  $\gamma^{(\infty)}$ , which are described by differential equations (Jacobi equations).

Remark 8.26. Of course it would be much better to avoid the compactness arguments and derive the bounds directly, but I don't see how to proceed.

# Bibliographical notes

Monge's observation about the impossibility of crossing appears in his seminal 1781 memoir [636]. The argument is likely to apply whenever the cost function satisfies a triangle inequality, which is always the case in what Bernard and Buffoni have called the Monge–Mañé problem [104]. I don't know of a quantitative version of it.

A very simple argument, due to Brenier, shows how to construct, without any calculations, configurations of points that lead to linecrossing for a quadratic cost [814, Chapter 10, Problem 1].

There are several possible computations to obtain inequalities of the style of (8.3). The use of the identity (8.2) was inspired by a result by Figalli, which is described below.

It is an old observation in Riemannian geometry that two minimizing curves cannot intersect twice and remain minimizing; the way to prove this is the shortcut method already known to Monge. This simple principle has important geometrical consequences, see for instance the works by Morse [637, Theorem 3] and Hedlund [467, p. 722]. (These references, as well as a large part of the historical remarks below, were pointed out to me by Mather.)

At the end of the seventies, Aubry discovered a noncrossing lemma which is similar in spirit, although in a different setting. Together with Le Daeron, he demonstrated the power of this principle in studying the so-called Frenkel–Kantorova model from solid-state physics; see the "Fundamental Lemma" in [52]. In particular, the method of Aubry and Le Daeron provided an alternative proof of results by Mather [598] about the existence of quasiperiodic orbits for certain dynamical systems.<sup>1</sup> The relations between the methods of proof of Aubry and Mather are discussed in [599, 604] and constitute the core of what is usually called the Aubry–Mather theory. Bangert [66, Lemma 3.1] gave a general version of the Aubry–Le Daeron lemma, and illustrated its use in various aspects of the theory of twist diffeomorphisms. (Bangert's paper can be consulted as an entry point for this subject.) He also made the connection with the earlier works of Morse and Hedlund in geometry. There is a related independent study by Bialy and Polterovich [117].

Then Moser [641] showed that the theory of twist diffeomorphisms (at least in certain particular cases) could be embedded in the theory of strictly convex Lagrangian systems, and Denzler [297] adapted the noncrossing arguments of Aubry, Le Daeron and Bangert to this new setting (see for instance Theorem 2.3 there), which in some sense goes back to older geometric works.

Around 1990, Mather came up with two contributions which for our purposes are crucial. The first consists in introducing minimizing measures rather than minimizing curves [600]; the second consists in a quantitative version of the noncrossing argument, for a general class of strictly convex Lagrangian functions [601, p. 186]. This estimate, which in these notes I called Mather's shortening lemma, was the key technical ingredient in the proof of his fundamental "Lipschitz graph theorem" [601, Theorem 2].

Although the statement in [601] is not really the same as the one which appears in this chapter, the proof really is similar. The idea to use this approach in optimal transport theory came to me when Bernard mentioned Mather's lemma in a seminar where he was presenting his results with Buffoni about the optimal transport problem for rather general Lagrangian functions [105].

<sup>1</sup> According to Mather, the chronology is blurred, because Aubry knew similar results somewhat earlier, at least for certain classes of systems, but had never published them; in particular, the discoveries of Aubry and Mather were independent. Further, see the review paper [51].

In the meantime, an appropriate version of the noncrossing lemma had already been rediscovered (but not in a quantitative version) by researchers in optimal transport. Indeed, the noncrossing property of optimal trajectories, and the resulting estimates about absolute continuity of the displacement interpolant, were some of the key technical tools used by McCann [614] to establish convexity properties of certain functionals along displacement interpolation in  $\mathbb{R}^n$  for a quadratic cost; these statements were generalized by Cordero-Erausquin, McCann and Schmuckenschläger [246] for Riemannian manifolds, and for rather general convex cost functions in  $\mathbb{R}^n$  by Cordero-Erausquin [243].

Results similar to Theorems 8.5 and 8.7 are also proven by Bernard and Buffoni [105] via the study of Hamilton–Jacobi equations, in the style of weak KAM theory. This is a bit less elementary but powerful as well. The basic idea is to exploit the fact that solutions of Hamilton– Jacobi equations are automatically semiconcave for positive times; I learnt from Otto the usefulness of this regularization property in the context of optimal transport (see [814, p. 181]). Fathi and Figalli [348] generalized this strategy to noncompact situations. Bernard [102] also used the same idea to recover an important result about the existence of  $C<sup>1</sup>$  subsolutions of certain Hamilton–Jacobi equations.

Figalli and Juillet [366] obtained a result similar to Theorem 8.7 when the cost is the squared distance on a degenerate Riemannian structure such as the Heisenberg group or an Alexandrov space with curvature bounded below. Their approach is completely different since it uses the uniqueness of Wasserstein geodesics and the so-called measure contraction property (which is traditionally associated with Ricci curvature bounds but nevertheless holds in the Heisenberg group [496]). Figalli and Juillet note that concentration phenomena arise in the Heisenberg group which are not seen in Riemannian manifolds; and that the Monge–Mather shortening lemma does not hold in this setting.

Theorem 8.11 is a variant of Mather's Lipschitz graph theorem, appearing (up to minor modifications) in Bernard and Buffoni [105, Theorem C]. The core of the proof is also taken from that work.

The acronym "KAM" stands for Kolmogorov, Arnold and Moser; the "classical KAM theory" deals with the stability (with high probability) of perturbed integrable Hamiltonian systems. An account of this theory can be found in, e.g., Thirring [780, Section 3.6]. With respect to weak KAM theory, some important differences are that: (a) classical KAM theory only applies to slight perturbations of integrable systems; (b) it only deals with very smooth objects; (c) it controls the behavior of a large portion of the phase space (the whole of it, asymptotically when the size of the perturbation goes to 0).

The weak KAM theory is much more recent than the classical one; it was developed by several authors, in particular Fathi [344, 345]. A theorem of the existence of a stationary solution of the Hamilton– Jacobi equation can be found in [347, Theorem 4.4.6]. Precursors are Mather  $[602, 603]$  and Mañé  $[592, 593]$ . The reader can also consult the book by Fathi [347], and (with a complementary point of view) the one by Contreras and Iturriaga [238]. Also available are some technical notes by Ishii [488], and the review works [604, 752].

The proof of Theorem 8.17, as I wrote it, is a minor variation of an argument shown to me by Fathi. Related considerations appear in a recent work by Bernard and Buffoni [106], who analyze the weak KAM theory in light of the abstract Kantorovich duality. One may also consult [278].

From its very beginning, the weak KAM theory has been associated with the theory of viscosity solutions of Hamilton–Jacobi equations. An early work on the subject (anterior to Mather's papers) is an unpublished preprint by P.-L. Lions, Papanicolaou and Varadhan [564]. Recently, the weak KAM theory has been related to the large-time behavior of Hamilton–Jacobi equations [69, 107, 346, 349, 383, 384, 385, 487, 645, 707. Aubry sets are also related with the  $C^1$  regularity of Hamilton–Jacobi equations, which has important applications in the theory of dynamical systems [102, 103, 350]. See also Evans and Gomes [332, 333, 334, 423] and the references therein for an alternative point of view.

In this chapter I presented Mather's problem in terms of trajectories and transport cost. There is an alternative presentation in terms of invariant measures, following an idea by Mañé. In Mañé's version of the problem, the unknown is a probability measure  $\mu(dx\,dv)$  on the tangent bundle TM; it is stationary in the sense that  $\nabla_x \cdot (v \mu) = 0$ (this is a stationary kinetic transport equation), and it should minimize the action  $\int L(x,v) \mu(dx dv)$ . Then one can show that  $\mu$  is actually invariant under the Lagrangian flow defined by L. As Gomes pointed out to me, this approach has the drawback that the invariance of  $\mu$  is not built in from the definition; but it has several nice advantages:

- It makes the graph property trivial if  $L$  is strictly convex: Indeed, one can always collapse the measure  $\mu$ , at each  $x \in M$ , onto the barycenter  $\xi(x) = \int v \mu(dv|x)$ ; this operation preserves the invariance of the measure, and decreases the cost unless  $\mu$  was already supported on a graph. (Note: This does not give the Lipschitz regularity of the graph!)
- This is a linear programming problem, so it admits a dual problem which is  $\inf_{\varphi} \sup_x H(\nabla_x \varphi, x)$ ; the value of this infimum is but another way to characterize the effective Hamiltonian  $\overline{H}$ , see e.g. [238, 239].
- This is a good starting point for some generalizations, see for instance [422].

I shall conclude with some more technical remarks.

The use of a restriction property to prove the absolute continuity of the displacement interpolant without any compactness assumption was inspired by a discussion with Sturm on a related subject. It was also Sturm who asked me whether Mather's estimates could be generalized to Alexandrov spaces with curvature bounded below.

The theorem according to which a Lipschitz map  $T$  dilates the  $n$ dimensional Hausdorff measure by a factor at most  $\|T\|_{\operatorname{Lip}}^n$  is an almost immediate consequence of the definitions of Hausdorff measure, see e.g. [174, Proposition 1.7.8].

Alexandrov spaces are discussed at length in the very pedagogical monograph by Burago, Burago and Ivanov [174]. Several characterizations of Alexandrov spaces are given there, and their equivalence is established. For instance, an Alexandrov space has curvature bounded below by K if the square distance function  $d(z, \cdot)^2$  is "no more convex" than the square distance function in the model space having constant sectional curvature K. Also geodesics in an Alexandrov space cannot diverge faster than geodesics in the model space, in some sense. These properties explain why such spaces may be a natural generalized setting for optimal transport. Upper bounds on the sectional curvature, on the other hand, do not seem to be of any help.

Figalli recently solved the Open Problem 8.21 in the special case  $K = 0$  (nonnegative curvature), with a very simple and sharp argument: He showed that if  $\gamma_1$  and  $\gamma_2$  are any two minimizing, constantspeed geodesics in an Alexandrov space  $(\mathcal{X}, d)$  with nonnegative curvature, and  $\gamma_1(0) = x_1, \gamma_2(0) = x_2, \gamma_1(1) = y_1, \gamma_2(1) = y_2$ , then

$$
d(\gamma_1(t), \gamma_2(t)) \ge (1-t)^2 d(x_1, x_2)^2 + t^2 d(y_1, y_2)^2
$$
  
+  $t(1-t) [d(x_1, y_2)^2 + d(x_2, y_1)^2 - d(x_1, y_1)^2 - d(x_2, y_2)^2].$  (8.45)

(So in this case there is no need for an upper bound on the distances between  $x_1, x_2, y_1, y_2$ .) The general case where K might be negative seems to be quite more tricky. As a consequence of (8.45), Theorem 8.7 holds when the cost is the squared distance on an Alexandrov space with nonnegative curvature; but this can also be proven by the method of Figalli and Juillet [366].

Theorem 8.22 takes inspiration from the no-crossing proof in [246, Lemma 5.3. I don't know whether the Hölder- $1/2$  regularity is optimal, and I don't know either whether it is possible/useful to obtain similar estimates for more general cost functions.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
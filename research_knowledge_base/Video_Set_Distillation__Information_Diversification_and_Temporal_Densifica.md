# <span id="page-0-1"></span>Video Set Distillation: Information Diversification and Temporal Densification

Yin<PERSON><PERSON> $^{1,2,3}$ , <PERSON><PERSON> $^{1,2,4}$ , <PERSON><PERSON><PERSON> $^3$ , <PERSON><PERSON>-<PERSON> Ong $^{1,2,4}$ , <PERSON> $^{1,2}$ 

<sup>1</sup>CFAR, Agency for Science, Technology and Research  $(A*STAR)$ , Singapore <sup>2</sup>IHPC, Agency for Science, Technology and Research (A\*STAR), Singapore

<sup>3</sup>School of EEE, Nanyang Technological University, Singapore

<sup>4</sup>CCDS, Nanyang Technological University, Singapore

#### Abstract

*The rapid development of AI models has led to a growing emphasis on enhancing their capabilities for complex input data such as videos. While large-scale video datasets have been introduced to support this growth, the unique challenges of reducing redundancies in video sets have not been explored. Compared to image datasets or individual videos, video sets have a two-layer nested structure, where the outer layer is the collection of individual videos, and the inner layer contains the correlations among framelevel data points to provide temporal information. Video sets have two dimensions of redundancies: within-sample and inter-sample redundancies. Existing methods like key frame selection, dataset pruning or dataset distillation are not addressing the unique challenge of video sets since they aimed at reducing redundancies in only one of the dimensions. In this work, we are the first to study Video Set Distillation, which synthesizes optimized video data by jointly addressing within-sample and inter-sample redundancies. Our Information Diversification and Temporal Densification (IDTD) method jointly reduces redundancies across both dimensions. This is achieved through a Feature Pool and Feature Selectors mechanism to preserve inter-sample diversity, alongside a Temporal Fusor that maintains temporal information density within synthesized videos. Our method achieves state-of-the-art results in Video Dataset Distillation, paving the way for more effective redundancy reduction and efficient AI model training on video datasets.*

## 1. Introduction

With the rapid advancement of AI models, there has been an increasing focus on enhancing AI capabilities for complex input data such as videos. This also encourages the emergence of large scale video datasets proposed to accelerate the development of AI in this domain [\[1,](#page-7-0) [7,](#page-8-0) [10,](#page-8-1) [14\]](#page-8-2). However, compared to other data modality such as text and image, video data consumes significantly more storage and

<span id="page-0-0"></span>Image /page/0/Figure/9 description: This figure illustrates the concept of Video Set Distillation. It shows a process starting with a synthetic video of someone playing the violin. This video is then broken down into different temporal segments: 1 second, 5 seconds, and 1 minute. These segments are further processed to highlight inter-sample redundancy and within-sample redundancy. The process involves joint optimization, leading to a representation of the same class of video, 'Playing Violin'. The figure also indicates temporal redundancy in the processed video segments.

Figure 1. The grand challenge of Video Set Distillation comes from its nature as a two-layer nested set. Each individual video is a set of image-level data points, and video set is a set of individual videos. It is critical to jointly optimize over both intersample redundancy and the within-sample redundancy. Furthermore, given the large variety of temporal length in a video set, the within-sample redundancies are largely different from each other, increasing the complexity of redundancy reduction.

also requires more computational power to analyze. How to effectively reduce the size of this ever-growing pool of video datasets with a focus on training and evaluation of multi-modal machine intelligence have become an critical and practical problem.

It is apparent that video data contains a high level of redundancy in terms of information density. We identify two dimensions of redundancy that could be examined to reduce the size of a video dataset: within-sample redundancy(similar frames in a same video); b) inter-sample redundancy(similar videos in a same dataset). There are many

<span id="page-1-0"></span>approaches that could be applied to these two redundancy dimensions; such as Key Frame Selection[\[4\]](#page-8-3) or downsampling for within-sample redundancy and Dataset Pruning [\[21\]](#page-8-4) for inter-sample redundancy. However, these naive methods are basically pruning the information by keeping some information intact and throwing the other away without considering how AI model will perform on the pruned data. Alternatively in this paper, we study a dataset synthesis problem named Video Set Distillation which aims to generate video datasets that are much smaller than the original by jointly optimizing within-sample, inter-sample redundancy and AI model performance. Specifically, Video Set Distillation learns to project similar raw video frames into a reduced number of synthesized frames within one video sample; and at the same time learn to combine multiple similar video clips into one synthesized sample via generation process.

It is worth noting that Video Set Distillation is different from the traditional research domain of video compression which primarily aims to reduce the size of individual video sample without altering their visual quality and temporal consistency in ways perceptible to humans. In contrast, Video Set Distillation serves a machine-centric purpose, aiming to generate a much smaller synthetic dataset optimized for efficient model training and evaluation, with minimal concern for human interpretability of the synthetic data.

Due to the nature of the input video data, Video Set Distillation needs to tackle two dimensions of redundancy: within-sample redundancy and inter-sample redundancy. Specifically, within-sample redundancy refers to the existence of similar frames in a same video clip and similar pixels in a same certain frame while inter-sample redundancy refers to the existence of highly similar video clips in one video dataset, as shown in Fig[.1.](#page-0-0) Although there are existing studies on Dataset Distillation (DD)[\[18\]](#page-8-5) which generates synthetic data via optimization processes, they only focus on image which is a simple form of data and hence cannot be applied directly for Video Set Distillation. Different from images, videos contain action related semantics that are embedded in temporal sequences; and the information density in each video clip sample is different. For example in Fig[.1,](#page-0-0) the video samples for one action can have lengths ranging from 1 second to 1 minute, but they all share the same class of "playing violin". One challenge in Video Set Distillation is how to reduce the temporal redundancy for video samples that have different information density automatically and adaptively. Another challenge is how to effectively reduce the inter-sample redundancy within a same class and temporal redundancy within a same video clip simultaneously.

Existing work such as VDSD[\[19\]](#page-8-6) tried to tackle individual video data by simply condensing video clip into a single static image, ignoring the rich temporal information. To address this issue, we propose Information Diversification and Temporal Densification (IDTD), which aims to jointly reduce both redundancies in an end-to-end manner. Firstly for reducing inter-sample redundancy, we enhance information diversification by leveraging a Feature Pool shared by multiple Feature Selectors, where the information diversity is achieved by Feature Selectors driven by a diversity loss function, while the common knowledge within the class is preserved by the Feature Pool. To efficiently utilize the limited Instance Per Class (IPC) budget for each class, it is of crucial importance to more comprehensively represent the information distribution from the original dataset. Secondly, to reduce within-sample redundancy; the second part of our mechanism is a Temporal Fusor seamlessly integrating the diverse information into the synthetic video. It takes the diverse feature generated by the first part as input and generates the synthetic videos. Such process enables the feature diversity to exist along the temporal dimension, and ensures the temporal information density since it forces the synthetic video to maintain diverse feature in a limited temporal size.

Our contribution can be summarized as:

- We are the first to identify and tackle the two dimension of redundancy in Video Set Distillation by designing specific module for each type of redundancy.
- We propose IDTD to jointly optimize over within-sample and inter-sample redundancies on video sets in an endto-end manner. We also propose an information diversification module to reduce the inter-sample redundancy while at the same time maximize the feature diversity of the generated synthetic video sample.
- Comprehensive experiments are conducted on multiple datasets, the results demonstrate the superiority of our proposed approach which achieves SOTA in Video Set Distillation on all datasets.

### 2. Related Works

#### 2.1. Dataset Distillation

Existing Dataset Distillation (DD)[\[18\]](#page-8-5) approaches have primarily focused on image-level dataset distillations. They aims at reducing inter-sample redundancy by synthesizing a compact dataset of a limited Instance Per Class (IPC). Approaches such as [\[2,](#page-8-7) [24,](#page-8-8) [26\]](#page-8-9) adopt a bi-level optimization perspective and align the training dynamics between the synthetic dataset and the real dataset. Alternatively, approaches like [\[17,](#page-8-10) [23,](#page-8-11) [25\]](#page-8-12) aligns the features extracted between the synthetic and the real dataset. However, these approaches fails to explicitly consider within-sample redundancy of the dataset, which is crucial for video datasets due their temporal redundancy. Consequently, these approaches' performance remains suboptimal in redundancy

<span id="page-2-1"></span><span id="page-2-0"></span>Image /page/2/Figure/0 description: The image displays a flowchart illustrating the IDTD approach, which combines Information Diversification and Temporal Densification. The Information Diversification section shows a Feature Pool feeding into multiple Feature Selectors (1 to K), each producing a Diverse Segment. These segments are then processed by a Matching Loss and a Diversity Loss, with the Matching Loss connecting to a 'Real Video Set within Class'. The Temporal Densification section takes Diverse Segments and processes them through a Temporal Fusor, resulting in Temporally Diverse & Dense Video. This output is then subjected to Stochastic Temporal Augmentation, which also connects to a 'Real Video Set within Class' via a Matching Loss. The diagram includes a legend indicating 'Data Flow' with solid arrows, 'Diversity Loss' with dashed red arrows, and 'Matching Loss' with dashed green arrows.

Figure 2. As shown on the left, Our IDTD approach jointly conduct the Information Diversification and the Temporal Densification in an end-to-end manner. On the top right, the Information Diversification part is shown in detail. The Feature Pool is a learnable variable and the Feature Selectors are learnable modules. On the bottom right, the Temporal Diversificaiton part is shown in detail. The Temporal Fusor is a learnable module. The Diversity loss enforces  $K$  diverse segments to represent information from the original dataset, and the Temporal Fusor is optimized to integrate the diverse feature into a synthetic video instance driven by a dataset distillation matching loss. K is a hyperparameter determining number of Feature Selectors for each synthetic instance. Our approach effectively balance the redundancy reduction between inter-sample dimension and within-sample dimension, while it keeps the temporal diverse information.

reduction for video sets.

Several DD approaches were developed to handle larger scale image datasets. At a larger scale, the redundancy of the dataset increases significantly as scale grows, which is observed similar as in video datasets. TESLA[\[3\]](#page-8-13) addresses the memory consumption issue of large scale datasets, and utilized soft labels to encode more information into the synthetic dataset. However, such memory efficiency has a questionable generalizability to large scale video datasets. Redundancy in video sets increases at a much faster rate as the growth of sample size due to the additional of temporal dimension.  $SRe^{2}L[22]$  $SRe^{2}L[22]$  and RDED[\[15\]](#page-8-15) reduce computational cost by leveraging the knowledge from well-trained teacher model instead of training the student model in the bi-level optimization process. Nonetheless, these approaches are limited to image-level datasets and failed to addressed the issue of temporal redundancy reduction, leaving the key challenge of redundancy reduction in video sets unresolved.

Recent works have sought to extend DD approaches beyond image-level datasets. Text dataset distillation has been explored by [\[11\]](#page-8-16), and [\[20\]](#page-8-17) explored multi-modality dataset distillation with both image and text modalities. VDSD[\[19\]](#page-8-6) tried to adapt DD techniques on video datasets. However, the approach treats the contribution of temporal information as trivial. They distilled a static image from the real dataset and compensated it with dynamic information using a trained interpolator. As a result, VDSD has a limited improvement on video datasets compared to existing imagelevel DD approaches.

#### 2.2. Video Recognition and Redundancy

Video Recognition was firstly explored in the deep learning context by [\[9\]](#page-8-18). Convolutional Networks such as C3D[\[16\]](#page-8-19)

and I3D[\[1\]](#page-7-0) paved ways for video feature extraction foundation models with deep learning. Since the early days of video recognition studies, redundancies in videos have been a critical factor influencing task effectiveness. For example, [\[5\]](#page-8-20) splits the feature extraction process into appearance and motion pathways, leveraging residual networks[\[8\]](#page-8-21) to model the interaction between these pathways. Such approach jointly reduces video redundancy in an implicit way by addressing the interaction between two specific path-ways. SlowFast[\[6\]](#page-8-22) explicitly considers the temporal redundancy of video modality, employing two different temporal granularity of temporal information extraction. Such redundancy reduction endeavor underscore the importance of redundancy reduction in video data without compromising valuable information.

This principle of redundancy reduction at the model level is equally applicable when aiming to reduce redundancy in video sets themselves. However, existing research has not adequately addressed the complexity of video redundancies. A comprehensive approach should jointly consider both the inter-sample and within-sample redundancies, ensuring effective redundancy reduction in video sets.

### 3. Methodology

#### 3.1. IDTD

Most of existing works did not explicitly address the influence of temporal redundancy and information diversity to a video set distillation process. To tackle this issue, we propose a novel approach that leveraged a joint-optimization framework as outlined in the following sections as illustrated by Fig[.2.](#page-2-0)

The problem of Video Set Distillation on a real video

dataset  $T$  can be formulated as follows: Given N classes and  $M$  Instance Per Class (IPC), the objective is to generate a synthetic video set  $V_{syn} = \{\{v_{m,n}\}_{m=1}^M\}_{n=1}^N$ , where  $v_{m,n} \in \mathbb{R}^{H \times W \times C \times T}$ .

The effectiveness of existing DD approaches is significantly compromised when applied to video datasets due to the interplay between within-sample redundancy and intersample redundancy. To address this limitation, we propose an approach that jointly promotes higher information diversity while reducing temporal redundancy.

As illustrated in Fig[.2,](#page-2-0) our IDTD approach comprises two primary components: the diversification module and the fusion module. The diversification module is designed to generate segments with diverse features, optimized jointly through a diversity loss and a dataset distillation loss. Subsequently, the Temporal Fusor takes these diverse segments as input and synthesizes the final video.

Compared to existing approaches, our IDTD approach explicitly encourage diversity, which enhances the effectiveness and information density of the synthetic dataset.

<span id="page-3-2"></span>

# Algorithm $\overline{1\,IDTD}(\mathcal{T})\rightarrow V_{syn}$

- 1: **Input:** Training Set  $T$
- 2: **Output:** Synthetic Video Set  $V_{syn}$
- 3: Initialize variables Feature Pool P, Feature Selector S, Temporal Fusor  $F$ ; Hyperparameter  $K$ ; Number of Classes  $N$ ,  $IPC = M$

```
4:
```

```
5: Training
```

```
6: for it in Iterations do
```

```
7: for n \text{ in } N do
8: for m in M do
```

| 9: |  | for $k$ in $K$ do |  |
|----|--|-------------------|--|
|    |  |                   |  |

```
10: Produce Diverse Segments:
11: d_{m,n,k} = s_{m,n,k}(p_{m,n})
```

```
12: end for
```

```
13: v_{m,n} = f_{m,n}(\{d_{m,n,k}\}_{k=1}^K)<br>
14: v'_{m,n} = \tau(v_{m,n})
```

```
15: end for
```

```
16: Calculate loss by equation 1.
```

```
17: Update P, S, F
```

```
18: end for
```

```
19: end for
```

```
20:
```

 $14:$ 

```
21: Synthesization
```

```
22: for n in N do
```

```
23: for m in M do
```

```
24: v_{m,n} = f_{m,n}(\{s_{m,n,k}(p_{m,n})\}_{k=1}^K)
```

```
25: end for
26: end for
```

```
27:
```

```
28: return V_{syn}
```

<span id="page-3-1"></span>

### 3.2. Information Diversification

The information diversification process consists of a Feature Pool and a set of Feature Selectors. Each Feature Selector takes the Feature Pool as input and generates a video segment. The generated video segments are explicitly enforced to have distinct features. At the same time, because all Feature Selectors share the same Feature Pool as input, this structure ensures that the Feature Pool gathers meaningful information, regardless of how diverse the segments are.

The Features Pool can be expressed as  $P = {\{p_{m,n}\}_{m=1}^M}_{n=1}^N$  and Feature Selectors  $S = \{\{\{s_{m,n,k}\}_{k=1}^{K}\}_{m=1}^{M}\}_{n=1}^{N}$ , where K is a hyperparameter that determines the number of Feature Selectors and, consequently, the number of feature Segments per instance. The set of feature segments is collectively referred to as the Diverse Segments  $D = \{\{\{d_{m,n,k}\}_{k=1}^K\}_{m=1}^M\}_{n=1}^N$ .

The process of producing the Diverse Segments can be expressed as:

$$
d_{m,n,k} = s_{m,n,k}(p_{m,n})
$$

To ensure diversity among the information contained in the Diverse Segments, a diversity loss is applied. It is defined as the L2 distance between each pair of distinct Diverse Segments:

<span id="page-3-0"></span>
$$
\mathcal{L}_{div} = \sum_{k=1}^{K-2} \sum_{q=k+1}^{K} ||(\phi(d_{m,n,k}) - \phi(d_{m,n,q})||_2^2 \quad (1)
$$

where  $\phi$  is the feature extraction process of the student model's feature extraction process.

Simultaneously, the Diverse Segments are also optimized by dataset distillation matching loss between the Diverse Segments  $D$  and the real dataset  $T$ , expressed as  $\mathcal{L}_M(D, \mathcal{T}).$ 

#### 3.3. Temporal Densification

After D is computed, the Temporal Fusor integrates the segments in the temporal dimension, defined as  $F =$  ${ {\{f_{m,n}\}}_{m=1}^M }$   $\}_{n=1}^N$ . The input of the Temporal Fusor is the Diverse Segments, and the output is the synthetic video of target size. The Temporal Densification process can be expressed as:

$$
v_{m,n} = f_{m,n}(\{d_{m,n,k}\}_{k=1}^K)
$$

However, once  $v_{m,n}$  is generated, the temporal sequence and absolute positions of information within the synthetic video become fixed. This rigidity significantly limits the utility of information diversity achieved through [3.2.](#page-3-1) To address this limitation, we introduce Stochastic Temporal <span id="page-4-0"></span>Augmentation, analogous to data augmentation in imagelevel trainings. This augmentation randomly samples a temporal fraction of  $v_{m,n}$  and reshape it to the targeted temporal size of the synthetic video. The process can be described as:

$$
v'_{m,n} = \tau(v_{m,n}, \mu)
$$
$$
\mu = [s, e], \quad s < e \quad and \quad s, e \in [0, 1]
$$

where  $\tau(\cdot)$  is the temporal augmentation process, and  $\mu$ is a random temporal interval. The synthetic dataset after applying the Stochastic Temporal Augmentation is defined as  $V'_{syn} = \{\{v'_{m,n}\}_{m=1}^M\}_{n=1}^N$ 

### 3.4. Training Objectives

Our approach seamlessly integrate the objectives of information diversification and temporal densification with existing dataset distillation losses. Specifically, we apply a DD matching loss to both the Diverse Segments and the final synthetic video, and meanwhile a diversification loss is applied among the Diverse Segments. These losses are optimized jointly to achieve an effective balance between diversity and representativeness. Given a dataset distillation matching objective function between synthetic and real videos  $\mathcal{L}_M(\cdot, \cdot)$ , the overall training objective is defined as:

$$
\mathcal{L} = \mathcal{L}_M((D, \mathcal{T}) + \alpha_1 * \mathcal{L}_{div} + \alpha_2 * \mathcal{L}_M((V'_{syn}, \mathcal{T})
$$

where  $\alpha_1$  and  $\alpha_2$  are hyperparameters determining the contribution of each loss.

This training objective encourages Diverse Segments to diverge from each other (via  $\mathcal{L}_{div}$ ), while simultaneously aligning the synthetic features to the real data (via  $\mathcal{L}_M$ ). By doing so, the within-sample redundancy and inter-class redundancy are jointly reduced, enabling the synthetic video set to capture more meaningful and representative information.

To elaborate on the training dynamics, the Diverse Segments  $\{d_{m,n,k}\}_{k=1}^K$  share a common Feature Pool and are collectively optimized using the dataset distillation matching loss. This combination of losses forces the Feature Pool to encapsulate the essential knowledge within each class, and the Feature Selectors learns to generate diverse information within the same class.

### 3.5. Training Pipeline

As shown in Alg[.1,](#page-3-2) the training process and synthesization process of our approach share the same data flow, with the only difference of Stochastic Temporal Augmentation applied and calculating the overall loss with equation [1.](#page-3-0)

Our training pipeline updates the Feature Pool, the  $K$ Feature Selectors and the Temporal Fusor on a class-byclass basis each optimization iteration. Such updating mechanism reduces the memory consumption compared to updating all classes by one backward propagation within an iteration, making it computationally efficient.

## 4. Experiment

### 4.1. Datasets

We evaluated on commonly used Video recognition datasets such as UCF101[\[14\]](#page-8-2) HMDB51[\[10\]](#page-8-1), Something-SomethingV2 (SSv2)[\[7\]](#page-8-0) and Kenetics400 (K400)[\[1\]](#page-7-0). For a fair comparison, we evaluated our approach's performance of MiniUCF following VDSD[\[19\]](#page-8-6), and we divide the datasets into light-weight track (UCF101 & HMDB51) and heavy-weight track (SSv2 & K400).

UCF101 includes 101 action classes focusing on human action and interaction with objects. It has a total of 13320 videos, with on average 131 videos per class and average time duration from 2-14 seconds per class. HMDB51 is also a small scale dataset with 6849 videos in total and 51 classes. SSv2 is large scale dataset with 220,847 videos in total with 174 classes. K400 is the most challenging dataset among the datasets, consisting of 400 classes in total. Despite different numbers of classes among these datasets, the duration range of the videos in the datasets are relatively similar, which are mostly within 10 seconds.

#### 4.2. Implementation Details

To implement our approach, the Feature Pool is a randomly initialized learnable tensor and the Feature Selectors are randomly initialized light-weighted linear layers. In the Temporal Fusor, concatenated Diverse Segments are input into a light-weighted convolution layer for convolution along the time dimension. For a fair comparison, we used the same student model as VDSD[\[19\]](#page-8-6) which is ConvNet3D.

We set the learning rate to be 0.01. For the loss weight hyperparameters, we set  $\alpha_1 = 0.05$  and  $\alpha_2 = 10^{-4}$  for all datasets.  $\alpha_2$  is two levels of magnitude smaller than  $\alpha_1$  to prevent gradient explosion at the down stream of the computational graph. We set  $K = 8$  for all experiments for a reasonable trade-off between training efficiency and performance. For the data loading settings, we followed the same video spatial and temporal size as VDSD[\[19\]](#page-8-6) for a fair comparison.

#### 4.3. Evaluation Metrics

We applied the conventional evaluation metrics of Dataset Distillation: given a certain Instance Per Class (IPC) of the synthetic data, we measure the effectiveness of the synthetic dataset based on the evaluation accuracy of the student model on the original test set. When training the student model with the synthetic data, we applied temporal augmentation as we proposed, and train the model for a number of epochs until the loss converges .

#### 4.4. Comparison with Existing Approaches

We compared our approaches with existing image level DD approaches as well as approaches targeting the specific

<span id="page-5-1"></span>

| <b>Dataset</b><br>IPC | <b>MiniUCF</b>       |                   | <b>HMDB51</b>      |                   |                    |
|-----------------------|----------------------|-------------------|--------------------|-------------------|--------------------|
|                       | 1                    | 5                 | 1                  | 5                 |                    |
| <b>Full Dataset</b>   |                      | 57.22 ± 0.14      | 28.58 ± 0.69       |                   |                    |
| Coreset Selection     | Random [19]          | 9.9 ± 0.8         | 22.9 ± 1.1         | 4.6 ± 0.5         | 6.6 ± 0.7          |
|                       | Herding [12]         | 12.7 ± 1.6        | 25.8 ± 0.3         | 3.8 ± 0.2         | 8.5 ± 0.4          |
|                       | K-Center[13]         | 11.5 ± 0.7        | 23.0 ± 1.3         | 3.1 ± 0.1         | 5.2 ± 0.3          |
| Dataset Distillation  | DM[23]               | 15.3 ± 1.1        | 25.7 ± 0.2         | 6.1 ± 0.2         | 8.0 ± 0.2          |
|                       | MTT[2]               | 19.0 ± 0.1        | 28.4 ± 0.7         | 6.6 ± 0.5         | 8.4 ± 0.6          |
|                       | FRePo[26]            | 20.3 ± 0.5        | 30.2 ± 1.7         | 7.2 ± 0.8         | 9.6 ± 0.7          |
|                       | DM[23] + VDSD[19]    | 17.5 ± 0.1        | 27.2 ± 0.4         | 6.0 ± 0.9         | 8.2 ± 0.4          |
|                       | MTT[2] + VDSD[19]    | <b>23.3 ± 0.6</b> | 28.3 ± 0.0         | 6.5 ± 0.4         | 8.9 ± 0.1          |
|                       | FRePo[26] + VDSD[19] | 22.0 ± 1.0        | 31.2 ± 0.7         | 8.6 ± 0.1         | 10.3 ± 0.6         |
|                       | <b>IDTD</b> (Ours)   | 22.52 ± 0.1       | <b>33.29 ± 0.5</b> | <b>9.52 ± 0.3</b> | <b>16.15 ± 0.9</b> |

Table 1. Comparison with existing approaches on small scale datasets. We compared with the current main stream image level dataset distillation approaches as well existing video distillation approaches. Our approach obtained a better performance compared to those not considering joint optimization of both redundancies.

| <b>Dataset</b>                                                       | K400                                                     | SSv2                     |
|----------------------------------------------------------------------|----------------------------------------------------------|--------------------------|
| <b>IPC</b>                                                           | 5                                                        |                          |
| <b>Full Dataset</b>                                                  | $34.6 \pm 0.5$                                           | $29.0 \pm 0.6$           |
| Random                                                               | $3.0 \pm 0.1$ 5.6 $\pm 0.0$                              | $3.3 \pm 0.13.9 \pm 0.1$ |
| DM[23]                                                               | $6.3 \pm 0.0$ $9.1 \pm 0.9$                              | $3.6 \pm 0.04.1 \pm 0.0$ |
| MTT[2]                                                               | $3.8\pm0.2$ 9.1 $\pm$ 0.3 3.9 $\pm$ 0.1 6.3 $\pm$ 0.3    |                          |
| $DM[23] + VDSD[19][6.3 \pm 0.2 7.0 \pm 0.1  4.0 \pm 0.1 3.8 \pm 0.1$ |                                                          |                          |
| $MTT[2] + VDSD[19]$ 6.3±0.1 11.5±0.5 5.5±0.1 8.3±0.1                 |                                                          |                          |
| <b>IDTD</b> (Ours)                                                   | $6.1 \pm 0.1$ 12.1 $\pm$ 0.2 3.9 $\pm$ 0.1 9.5 $\pm$ 0.3 |                          |

Table 2. Comparison with existing approaches on large scale datasets. Our approach shows advantage at higher IPC. This could because of an increase of information diversity as IPC increases, and lower IPC is more difficult to be diverse enough to produce representative features.

challenge of videos.

It is worth noticing that the experiment result of our approach is achieved based on the baseline of Distribution Matching (DM) [\[23\]](#page-8-11). Other training dynamics matching approaches like MTT[\[2\]](#page-8-7) or FrePo[\[26\]](#page-8-9) take much more computational resources than DM based approach. Therefore, although our performance achieved significant improvements compared to all of other approaches, it would be more fair for our approach to be compared with Distribution Matching based approaches.

For small scale datasets of MiniUCF and HMDB51, it is observed that our approach have larger advantages at higher IPCs. This is explained by our training objectives of Information Diversification. As the IPC increases, there is a higher potential of information diversity that could be utilized and distilled into the synthetic dataset. Furthermore, we noticed that all previous approaches performed poorly on HMDB51 compared to that on MiniUCF. This can be

explained that for a more difficult datasets like HMDB51, there are more hard samples or diverse samples, and those samples could be better utilized with an Information Diversification scheme. Although these hard cases lowers the performance upper limit on the full dataset, this becomes an advantage for our approach since we are able to leverage the diversities.

For large scale datasets including K400 and SSv2, we observed that the performance at low  $IPC = 1$  is only comparable with existing approaches, while at higher  $IPC = 5$ , the performance starts to show advantage. This could because that as the synthetic dataset scale increases, information diversity becomes more accessible. We can also observe that almost all approaches at a low  $IPC = 1$  have difficulties surpassing the image-level approaches baselines. This shows that video set distillation at a larger scale remains challenging ans should be further explored in the future works.

<span id="page-5-0"></span>

|      | Feature Pool & Selectors | Temporal Fusor | MiniUCF   |
|------|--------------------------|----------------|-----------|
| A    |                          |                | 14.99±0.2 |
| B    |                          | ✓              | 18.40±0.3 |
| C    | ✓                        |                | 18.57±0.8 |
| IDTD | ✓                        | ✓              | 22.52±0.1 |

Table 3. Ablation on modules. We can interpret the ablation as the following. A: Compress and Stitch; B: Temporal Densification only; C: Information Diversification only.

## 4.5. Ablation Study

In the ablation study, we aim to explore the contribution of different parts of our approach to the final performance. We mainly evaluate it from three aspects: a) contributions of

<span id="page-6-3"></span><span id="page-6-0"></span>

|      | Selector<br>$\mathcal{L}_M$ | Selector<br>$\mathcal{L}_{div}$ | Fusor<br>$\mathcal{L}_M$ | MiniUCF         |
|------|-----------------------------|---------------------------------|--------------------------|-----------------|
| A    |                             |                                 | ✓                        | $13.82 \pm 0.3$ |
| B    |                             | ✓                               | ✓                        | $15.81 \pm 0.6$ |
| C    | ✓                           |                                 | ✓                        | $20.04 \pm 0.4$ |
| IDTD | ✓                           | ✓                               | ✓                        | $22.52 \pm 0.1$ |

Table 4. Ablation on training objectives. In this table, Selector  $\mathcal{L}_M$  is the dataset distillation matching loss applied to the Feature Selectors and Feature Pool, Selector  $\mathcal{L}_{div}$  is the diversity loss and Fusor  $\mathcal{L}_M$  is the matching loss applied to the Temporal Fusor.

different modules and their mutual influence; b) contributions of different losses; c) impact of different temporal size of the synthetic videos; d) impact of different level of redundancies from within-sample and inter-sample. Via these ablations, we aim to fully verify the effectiveness of the approach we proposed and how the within-sample and intersample redundancies interacts.

<span id="page-6-1"></span>Image /page/6/Figure/3 description: This is a line graph titled "Accuracy on MiniUCF". The x-axis represents the "Number of Frames of synthetic videos" and ranges from 1 to 16. The y-axis represents accuracy and ranges from 8 to 24. There are two lines on the graph: one orange line labeled "IDTD (Ours)" and one blue line labeled "SOTA [37]". At 1 frame, IDTD has an accuracy of approximately 10.5, and SOTA has an accuracy of approximately 16.5. At 2 frames, IDTD has an accuracy of approximately 12, and SOTA has an accuracy of approximately 18. At 4 frames, IDTD has an accuracy of approximately 15.5, and SOTA has an accuracy of approximately 16.8. At 8 frames, IDTD has an accuracy of approximately 19.5, and SOTA has an accuracy of approximately 18.2. At 16 frames, IDTD has an accuracy of approximately 22.5, and SOTA has an accuracy of approximately 20.5.

Figure 3. We compared the trend of performance as number of frames of synthetic videos changes. Ours shows a clear growth as the number of frames increases, while VDSD [\[19\]](#page-8-6) have no significant increase.

Modules: To verify the effectiveness of joint optimization of information diversification and temporal densification, We conducted ablation experiment on different modules as shown in Tab[.3,](#page-5-0) where Feature Selector aims to achieve an information diversifiction purpose, and the Temporal Fusor aims to achieve temporal densification purpose.

For Tab[.3-](#page-5-0)A, we randomly select  $K$  real videos and naively do a temporal compression by temporal sampling and stitch them into a synthetic video. For Tab[.3-](#page-5-0)B, we conduct the experiment without Feature Pool or Feature Selectors, and we initialize the Diverse Segments with  $K$  real videos and optimize the Diverse Segments and the Temporal Fusor. For Tab[.3-](#page-5-0)C, we optimzed only the Feature Pool and the Selectors and stitch the output of them (i.e. the Di-

<span id="page-6-2"></span>Image /page/6/Figure/7 description: This is a scatter plot showing the relationship between Within-sample Redundancy on the x-axis and Inter-sample Redundancy on the y-axis. There are several data points represented by circles, colored either red or blue. Each data point also has a percentage value associated with it, indicating some form of change or difference. The red circles are generally larger and have positive percentage values, while the blue circles are smaller and have both positive and negative percentage values. For example, there is a red circle at approximately (0.85, 0.72) with a value of +15.00%, and a blue circle at approximately (0.6, 0.65) with a value of -6.25%. Another prominent red circle is located around (0.88, 0.82) with a value of +67.44%.

Figure 4. Redundancy Analysis. As shown in the diagram, our approach obtained higher performance gain where both inter-sample redundancy and within-sample redundancy are higher, indicating the effectiveness of joint optimization rather than naively discarding all temporal information followed by interpolation.

verse Segments) into the synthetic videos.

From Tab[.3,](#page-5-0) we can tell that the joint optimzition of both the information diversification and the temporal densification,is a necessary condition to produce effective synthetic videos.

Losses: We further conduct our ablation study over the training objectives of our approach. The Feature Selector matching loss  $\mathcal{L}_M$  enforces the Feature Pool and the Feature Selector to learn useful information and generate the feature segments similar with the feature of the real, while the Feature Selector diversity loss  $\mathcal{L}_M$  pushed the feature of the Diverse Segments generated away from each other to diversely represent the real dataset.

We noticed that, even though our approach utilized an end-to-end training manner, naively applying the dataset distillation matching loss to the final output performs poorly as shown by Tab[.4-](#page-6-0)A. Afterthe diversity loss is applied as shown by Tab[.4-](#page-6-0)B, it increases the performance but is still far below the full performance since the Feature Selector and Feature Pool do not get enough supervision signal from simply the matching loss applied to the Temporal Fusor. By applying the dataset distillation matching loss to both the Feature Selector and the Temporal Fusor as shown by Tab[.4-](#page-6-0)C, the performance is increases since the Feature Selector could implicitly obtain diverse features driven by a matching loss and also by sharing the same Feature Pool.

<span id="page-7-2"></span><span id="page-7-1"></span>Image /page/7/Figure/0 description: The image displays two sets of generated images, labeled (a) and (b). Each set contains two rows of images, with the top row labeled "Naïve DM" and the bottom row labeled "IDTD". In set (a), the "Naïve DM" row shows a sequence of noisy, abstract images. The "IDTD" row in set (a) displays a sequence of more discernible images, appearing to be scenes with figures, some with a pinkish or greenish hue. In set (b), the "Naïve DM" row again shows noisy, abstract images, this time with a more circular or floral pattern. The "IDTD" row in set (b) presents a sequence of images that appear to be portraits or close-ups of figures, with a darker, more muted color palette.

Figure 5. Qualitative Results compared between image-level approach (Distribution Matching) and our IDTD approach jointly optimizaing within-sample and inter-sample class redundancy. Our approach preserves much more temporal information diversity. Class (a) is CleanAndJerk and Class(b) is playing violin.

We can see that the performance comes to its highest after all three training losses are applied as shown by the last row by Tab[.4.](#page-6-0)

Temporal Size: As shown in Fig[.3,](#page-6-1) we conducted ablation on the frame number of one synthetic videos and compared to VDSD[\[19\]](#page-8-6). The frame number of the real video is set as 16, and the synthetic frame number is changed. Our result shows a clear trend of performance increasing as the number of frames increases, while VDSD[\[19\]](#page-8-6) shows no obvious increase as they claimed. This is because our approach is able to preserve temporal information effectively and therefore is able to increase the performance as the number of the frames increases.

Inter Class Analysis: As shown in Fig[.4](#page-6-2) a evaluation of performance improvement of our approach compared to Tab[.3-](#page-5-0)A is conducted. Each circle in the scatter plot is the performance improvement of a class, with the x-axis of within-sample redundancy and y-axis of intersample redundancy. The radial  $r$  of the circle is positively correlated to the performance improvement. Namely,  $r \propto (acc_{IDTD} - acc_{Tab.3-A})$  $r \propto (acc_{IDTD} - acc_{Tab.3-A})$  $r \propto (acc_{IDTD} - acc_{Tab.3-A})$ . The red color of the circle represents a positive performance gain, while the blue color represents a negative gain.

Retrieving feature with temporal information from a middle layer of the student model, we get temporal feature of the video  $\mathcal{F}_t \in \mathcal{R}^{B \times t \times d}$ , the temporal redundancy is defined as

$$
R_t = \tanh\left(\frac{1}{\frac{1}{B}\sum_{b=1}^B var(\mathcal{F}_t(b))}\right)
$$

where  $var(.)$  is the variance along the first dimension of any variable. Retrieving a video's feature encoded by the second last layer of the student model  $\mathcal{F}_{IC} \in \mathcal{R}^{B \times \tilde{d}}$ , the inter-sample redundancy defined as

$$
R_{IC} = \tanh(\frac{1}{\frac{1}{B}var(\mathcal{F}_{IC})})
$$

Therefore in this way, we calculate redundancy in both dimensions by measuring the reciprocal of variance and normalizing the value to  $[0, 1]$ .

As shown in Fig[.4,](#page-6-2) as the within-sample redundancy and inter-sample redundancy increases, the performance gain in the classes significantly increases. We can also notice that, the performance gain achieved becomes the largest when the inter-sample redundancy and the within-sample redundancy is both high.

## 4.6. Qualitative Results

We compare the qualitative results with an image-level dataset distillation approach (Distribution Matching[\[23\]](#page-8-11)). As shown in Fig[.5,](#page-7-1) the results of Distribution Matching approach and our IDTD approach are shown. Our synthetic videos are significantly more diverse along the temporal axis and contains more temporal dynamic information than the image level dataset distillation or than VDSD[\[19\]](#page-8-6) which generated the synthetic video by interpolating a distilled static image.

## 5. Conclusion

In conclusion, in this work we are the first to study the problem of Video Set Distillation, and we proposed a approach to jointly reduce both the inter-sample redundancy and within-sample redundancy in an end-to-end manner. Our approach leveraged a diversity loss to produce more diverse and representative features for synthetic videos. A Temporal Fusor is applied to densify temporal information while preserving the diversity. Our approach achieved the SOTA on most of the dataset evaluations with a more realistic and temporally diverse synthetic videos.

#### **References**

<span id="page-7-0"></span>[1] Joao Carreira and Andrew Zisserman. Ouo vadis and action recognition? a new model and the kinetics dataset. In *CVPR*,

2017. [1,](#page-0-1) [3,](#page-2-1) [5](#page-4-0)

- <span id="page-8-7"></span>[2] George Cazenavette, Tongzhou Wang, Alexei A Efros Antonio Torralba, , and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *CVPR*, 2022. [2,](#page-1-0) [6](#page-5-1)
- <span id="page-8-13"></span>[3] Justin Cui, Ruochen Wang, Si Si, , and Cho-Jui Hsieh. Scaling up dataset distillation to imagenet-1k with constant memory. In *IMCL*, 2023. [3](#page-2-1)
- <span id="page-8-3"></span>[4] F. Dirfaux. Key frame selection to represent a video. In *Proceedings 2000 International Conference on Image Processing*, 2000. [2](#page-1-0)
- <span id="page-8-20"></span>[5] Christoph Feichtenhofer, Axel Pinz, , and Richard P Wildes. Spatiotemporal residual networks for video action recognition. In *NeurIPS*, 2016. [3](#page-2-1)
- <span id="page-8-22"></span>[6] Christoph Feichtenhofer, Haoqi Fan, Jitendra Malik, and Kaiming He. Slowfast networks for video recognition. In *ICCV*, 2019. [3](#page-2-1)
- <span id="page-8-0"></span>[7] Raghav Goyal, Samira Ebrahimi Kahou, Vincent Michal ski, Joanna Materzy´ nska, Susanne Westphal, Heuna Kim, Valentin Haenel, Ingo Fruend, Peter Yianilos, Moritz Mueller-Freitag, Florian Hoppe, Christian Thurau, Ingo Bax, and Roland Memisevic. The "something something" video database for learning and evaluating visual common sense. In *ICCV*, 2017. [1,](#page-0-1) [5](#page-4-0)
- <span id="page-8-21"></span>[8] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *CVPR*, 2016. [3](#page-2-1)
- <span id="page-8-18"></span>[9] Andrej Karpathy, George Toderici, Sanketh Shetty, Thomas Leung, Rahul Sukthankar, and Li Fei-Fei. Large-scale video classification with convolutional neural networks. In *CVPR*, 2014. [3](#page-2-1)
- <span id="page-8-1"></span>[10] H. Kuehne, H. Jhuang, E. Garrote, T. Poggio, , and T. Serre. Hmdb: A large video database for human motion recogni tion. In *ICCV*, 2011. [1,](#page-0-1) [5](#page-4-0)
- <span id="page-8-16"></span>[11] Yongqi Li and Wenjie Li. Data distillation for text classification. In *arXiv preprint arXiv:2104.08448*, 2021. [3](#page-2-1)
- <span id="page-8-23"></span>[12] MaxWelling. Herding dynamical weights to learn. In *ICML*, 2009. [6](#page-5-1)
- <span id="page-8-24"></span>[13] Ozan Sener and Silvio Savarese. Active learning for con volutional neural networks: A core-set approach. In *ICLR*, 2018. [6](#page-5-1)
- <span id="page-8-2"></span>[14] Khurram Soomro, Amir Roshan Zamir, , and Mubarak Shah. Ucf101: A dataset of 101 human actions classes from videos in the wild. In *CoRR*, 2012. [1,](#page-0-1) [5](#page-4-0)
- <span id="page-8-15"></span>[15] Peng Sun, Bei Shi, Daiwei Yu, and Tao Lin. On the diversity and realism of distilled dataset: Anefficient dataset distillation paradigm. In *CVPR*, 2024. [3](#page-2-1)
- <span id="page-8-19"></span>[16] Du Tran, Lubomir Bourdev, Rob Fergus, Lorenzo Torresani, and Manohar Paluri. Learning spatiotemporal features with 3d convolutional networks. In *ICCV*, 2015. [3](#page-2-1)
- <span id="page-8-10"></span>[17] Kai Wang, Bo Zhao, Xiangyu Peng, Zheng Zhu, Shuo Wang Shuo Yang, Guan Huang, Hakan Bilen, Xinchao Wang, , and Yang You. Cafe: Learning to condense dataset by aligning features. In *CVPR*, 2022. [2](#page-1-0)
- <span id="page-8-5"></span>[18] Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, , and Alexei A Efros. Dataset distillation. In *arXiv preprint arXiv:1811.10959*, 2018. [2](#page-1-0)

- <span id="page-8-6"></span>[19] Ziyu Wang, Yue Xu, Cewu Lu, and Yong-Lu Li. Dancing with still images: Video distillation via static-dynamic disentanglement. In *CVPR*, 2024. [2,](#page-1-0) [3,](#page-2-1) [5,](#page-4-0) [6,](#page-5-1) [7,](#page-6-3) [8](#page-7-2)
- <span id="page-8-17"></span>[20] Xindi Wu, Zhiwei Deng, , and Olga Russakovsky. Multimodal dataset distillation for image-text retrieval. In *arXiv preprint arXiv:2308.07545*, 2023. [3](#page-2-1)
- <span id="page-8-4"></span>[21] Shuo Yang, ZekeXie HanyuPeng, MinXu, MingmingSun, and PingLi. Dataset pruning: Reducing training data by examining generalization influence. In *Proceedings 2000 International Conference on Image Processing*, 2000. [2](#page-1-0)
- <span id="page-8-14"></span>[22] Zeyuan Yin, Eric Xing, and Zhiqiang Shen. Squeeze and recover and relabel: Dataset condensation at imagenet scale from a new perspective. In *NeurIPS*, 2023. [3](#page-2-1)
- <span id="page-8-11"></span>[23] Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *WACV*, 2023. [2,](#page-1-0) [6,](#page-5-1) [8](#page-7-2)
- <span id="page-8-8"></span>[24] Bo Zhao, Konda Reddy Mopuri, , and Hakan Bilen. Dataset condensation with gradient matching. In *ICLR*, 2021. [2](#page-1-0)
- <span id="page-8-12"></span>[25] Ganlong Zhao, Guanbin Li, Yipeng Qin, and Yizhou Yu. Improved distribution matching for dataset condensation. In *CVPR*, 2023. [2](#page-1-0)
- <span id="page-8-9"></span>[26] Yongchao Zhou, Ehsan Nezhadarya, , and Jimmy Ba. Dataset distillation using neural feature regression. In *NeurIPS*, 2022. [2,](#page-1-0) [6](#page-5-1)
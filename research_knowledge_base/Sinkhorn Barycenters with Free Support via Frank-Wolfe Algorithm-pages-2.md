# **Supplementary Material**

Below we give an overview of the structure of the supplementary material and highlight the main novel results of this work.

**Appendix [A:](#page-0-0) abstract Frank<PERSON><PERSON> algorithm in dual Banach spaces.** This section contains full details on <PERSON><PERSON><PERSON> algorithm. The novelty stands in the relaxation of the differentiability assumptions.

**Appendix [B:](#page-6-0) DAD problems and convergence of Sinkhorn-Knopp algorithm.** This section is a brief review of basic concepts from the nonlinear Perrom-Frobeius theory, DAD problems, and applications to the study of Sinkorn algorithm.

**Appendix [C:](#page-16-0) Lipschitz continuitity of the gradient of the Sinkhorn divergence with respect to Total Variation.** This section contains one of the main contributions of our work, Theorem C.4, from which we derive Theorem 4 in the main text.

**Appendix [D:](#page-22-0) Frank-Wolfe algorithm for Sinkhorn barycenters.** This section contains the complete analysis of FW algorithm for Sinkhorn barycenters, which takes into account the error in the computation of Sinkhorn potentials and the error in their minimization. The main result is the convergence of the <PERSON><PERSON><PERSON> scheme for finitely supported distributions in Theorem [D.2.](#page-23-0)

**Appendix [E:](#page-24-0) Sample complexity of Sinkhorn potential and convergence of Algorithm 2 in case of continuous measures.** This section contains the discussion and the proofs of two of main results of the work Theorem 6, Theorem 7.

**Appendix [F:](#page-29-0) additional experiments.** This section contains additional experiment on barycenters of mixture of Gaussian, barycenter of a mesh in 3D (dinosau) and additional figures on the experiment on Sinkhorn propagation described in Section 6.

<span id="page-0-0"></span>

## **A The Frank-Wolfe algorithm in dual Banach spaces**

In this section we detail the convergence analysis of the Frank-Wolfe algorithm in abstract dual Banach spaces and under mild directional differentiablility assumptions so to cover the setting of Sinkhorn barycenters described in Section 3 of the paper.

<span id="page-0-1"></span>Let W be a real Banach space and let be  $\mathcal{W}^*$  its topological dual. Let  $\mathcal{D} \subset \mathcal{W}^*$  be a nonempty, closed, convex, and bounded set and let  $G: \mathcal{D} \to \mathbb{R}$  be a convex function. We address the following optimization problem

$$
\min_{w \in \mathcal{D}} \mathsf{G}(w),\tag{A.1}
$$

assuming that the set of solutions is nonemtpy.

We recall the concept of the tangent cone of feasible directions.

<span id="page-1-3"></span>**Definition A.1.** *Let*  $w \in \mathcal{D}$ . Then the cone of feasible directions of  $\mathcal{D}$  at  $w$  *is*  $\mathcal{F}_{\mathcal{D}}(w) = \mathbb{R}_+(\mathcal{D} - w)$ *and the* tangent cone of D at *w is*

$$
\mathcal{T}_{\mathcal{D}}(w) = \overline{\mathcal{F}_{\mathcal{D}}(w)} = \{v \in \mathcal{W}^* \mid (\exists (t_k)_{k \in \mathbb{N}} \in \mathbb{R}^{\mathbb{N}}_{++}) (t_k \to 0) (\exists (w_k)_{k \in \mathbb{N}} \in \mathcal{D}^{\mathbb{N}}) \ t_k^{-1}(w_k - w) \to v \}.
$$

**Remark A.1.**  $\mathcal{F}_{\mathcal{D}}(w)$  is the cone generated by  $\mathcal{D} - w$ , and it is a convex cone. Indeed, if  $t > 0$  and  $v \in \mathcal{F}_{\mathcal{D}}(w)$ , then  $tv \in \mathcal{F}_{\mathcal{D}}(w)$ . Moreover, if  $v_1, v_2 \in \mathcal{F}_{\mathcal{D}}(w)$ , then there exists  $t_1, t_2 > 0$  and  $w_1, w_2 \in \mathcal{D}$ such that  $v_i = t_i(w_i - w)$ ,  $i = 1, 2$ . Thus,

$$
v_1 + v_2 = (t_1 + t_2) \left( \frac{t_1}{t_1 + t_2} w_1 + \frac{t_2}{t_1 + t_2} w_2 - w \right) \in \mathbb{R}_+(\mathcal{D} - w).
$$

So,  $\mathcal{T}_{\mathcal{D}}(w)$  is a closed convex cone too.

<span id="page-1-2"></span>**Definition A.2.** Let  $w \in \mathcal{D}$  and  $v \in \mathcal{F}_{\mathcal{D}}(w)$ . Then, the directional derivative of G at w in the direction *v is*

$$
\mathsf{G}'(w;v)=\lim_{t\to 0^+}\frac{\mathsf{G}(w+tv)-\mathsf{G}(w)}{t}\in [-\infty,+\infty[.
$$

**Remark A.2.** The above definition is well-posed. Indeed, since v is a feasible direction of  $\mathcal{D}$  at x, there exists  $t_1 > 0$  and  $w_1 \in \mathcal{D}$  such that  $v = t_1(w_1 - w)$ ; hence

$$
(\forall t \in [0, 1/t_1]) \quad x + tv = x + tt_1(w_1 - w) = (1 - tt_1)w + tt_1w_1 \in \mathcal{D}.
$$

Moreover, since G is convex, the function  $t \in [0, 1/t_1] \mapsto (G(w + tv) - G(w))/t$  is increasing, hence

$$
\lim_{t \to 0^+} \frac{\mathsf{G}(w + tv) - \mathsf{G}(w)}{t} = \inf_{t \in [0, 1/t_1]} \frac{\mathsf{G}(w + tv) - \mathsf{G}(w)}{t}.
$$
 (A.2)

It is easy to prove that the function

<span id="page-1-0"></span>
$$
v\in \mathcal{F}_{\mathcal{D}}(w)\mapsto \mathsf{G}'(w;v)\in [-\infty,+\infty[
$$

is positively homogeneous and sublinear (hence convex), that is,

- (i)  $(\forall v \in \mathcal{F}_{\mathcal{D}}(w))(\forall t \in \mathbb{R}_+)$   $\mathsf{G}'(w; tv) = t\mathsf{G}'(w; v);$
- (ii)  $(\forall v_1, v_2 \in \mathcal{F}_{\mathcal{D}}(w))$   $\mathsf{G}'(w; v_1 + v_2) \leq \mathsf{G}'(w; v_1) + \mathsf{G}'(w; v_2)$ .

We make the following assumptions about G:

- H1  $(\forall w \in \mathcal{D})$  the function  $v \mapsto G'(w; v)$  is finite, that is,  $G'(w; v) \in \mathbb{R}$ .
- <span id="page-1-1"></span>H2 The *curvature of* G is finite, that is,

$$
C_{\mathsf{G}} = \sup_{\substack{w,z \in \mathcal{D} \\ \gamma \in [0,1]}} \frac{2}{\gamma^2} \big( \mathsf{G}(w + \gamma(z - w)) - \mathsf{G}(w) - \gamma \mathsf{G}'(w, z - w) \big) < +\infty. \tag{A.3}
$$

#### **Algorithm A.1** Frank-Wolfe in Dual Banach Spaces

Let  $(\gamma_k)_{k\in\mathbb{N}} \in \mathbb{R}^{\mathbb{N}}_{++}$  be such that  $\gamma_0 = 1$  and, for every  $k \in \mathbb{N}$ ,  $1/\gamma_k \leq 1/\gamma_{k+1} \leq 1/2 + 1/\gamma_k$  (i.e.,  $\gamma_k = 2/(k+2)$ ). Let  $w_0 \in \mathcal{D}$  and  $(\Delta_k)_{k \in \mathbb{N}} \in \mathbb{R}^{\mathbb{N}}_+$  be such that  $(\Delta_k/\gamma_k)_{k \in \mathbb{N}}$  is nondecreasing. Then

> for  $k = 0, 1, \ldots$  $\left| \text{ find } z_{k+1} \in \mathcal{D} \text{ is such that } G'(w_k; z_{k+1} - w_k) \leq \inf_{z \in \mathcal{D}} G'(w_k; z - w_k) + \frac{1}{2}\Delta_k$  $w_{k+1} = w_k + \gamma_k(z_{k+1} - w_k)$

**Remark A.3.** For every  $w, z \in \mathcal{D}$ , we have

<span id="page-2-0"></span>
$$
\mathsf{G}(z) - \mathsf{G}(w) \ge \mathsf{G}'(w, z - w). \tag{A.4}
$$

This follows from  $(A.2)$  with  $w_1 = z$  and  $t = 1$   $(t_1 = 1)$ .

The (inexact) Frank-Wolfe algorithm is detailed in Algorithm A.1.

#### **Remark A.4.**

- (i) Algorithm A.1 does not require the sub-problem  $\min_{z \in \mathcal{D}} G'(w_k, z w_k)$  to have solutions. Indeed it only requires computing a  $\Delta_k$ -minimizer of  $\mathsf{G}'(w_k; \cdot - w_k)$  on  $\mathcal{D}$ , which always exists.
- (ii) Since  $\mathcal{D}$  is weakly-\* compact (by Banach-Alaoglu theorem), if  $G'(w_k, -w_k)$  is weakly-\* continuous on  $\mathcal{D}$ , then the sub-problem  $\min_{z \in \mathcal{D}} G'(w_k, z - w_k)$  admits solutions. Note that this occurs when the directional derivative  $G'(w; \cdot)$  is linear and can be represented in W. This case is addressed in the subsequent Proposition [A.7.](#page-3-0)

**Theorem A.5.** *Let*  $(w_k)_{k \in \mathbb{N}}$  *be defined according to Algorithm A.1. Then, for every integer*  $k \geq 1$ *,* 

<span id="page-2-1"></span>
$$
\mathsf{G}(w_k) - \min \mathsf{G} \le C_{\mathsf{G}} \gamma_k + \Delta_k. \tag{A.5}
$$

*Proof.* Let  $w_* \in \mathcal{D}$  be a solution of problem  $(A.1)$ . It follows from [H2](#page-1-1) and the definition of  $w_{k+1}$  in Algorithm A.1, that

$$
G(w_{k+1}) \le G(w_k) + \gamma_k G'(w_k; z_{k+1} - w_k) + \frac{\gamma_k^2}{2} C_G.
$$

Moreover, it follows from the definition of  $z_{k+1}$  in Algorithm A.1 and [\(A.4\)](#page-2-0) that

$$
G'(w_k; z_{k+1} - w_k) 
\le 
\inf_{z \in D} G'(w_k; z - w_k) + \frac{1}{2} 
\Delta_k
$$

$$
\le G'(w_k; w_* - w_k) + \frac{1}{2} 
\Delta_k
$$

$$
\le - (G(w_k) - G(w_*)) + \frac{1}{2} 
\Delta_k.
$$

<span id="page-2-2"></span>Then,

$$
G(w_{k+1}) - G(w_*) \le (1 - \gamma_k)(G(w_k) - G(w_*)) + \frac{\gamma_k^2}{2}(C_G + \frac{\Delta_k}{\gamma_k}).
$$
\n(A.6)

Now, similarly to [28, Theorem 2], we can prove  $(A.5)$  by induction. Since  $\gamma_0 = 1$ ,  $1/\gamma_1 \leq 1/2 + 1/\gamma_0$ , and  $\Delta_0/\gamma_0 \leq \Delta_1/\gamma_1$ , it follows from [\(A.6\)](#page-2-2) that

$$
\mathsf{G}(w_1) - \mathsf{G}(w_*) \le \frac{1}{2} \Big( C_\mathsf{G} + \frac{\Delta_0}{\gamma_0} \Big) \le \gamma_1 \Big( C_\mathsf{G} + \frac{\Delta_1}{\gamma_1} \Big),\tag{A.7}
$$

hence  $(A.5)$  is true for  $k = 1$ . Set, for the sake of brevity,  $C_k = C_G + \Delta_k/\gamma_k$  and suppose that  $(A.5)$ holds for  $k \in \mathbb{N}$ ,  $k \geq 1$ . Then, it follows from  $(A.6)$  and the properties of  $(\gamma_k)_{k \in \mathbb{N}}$  that

$$
G(w_{k+1}) - G(w_*) 
less (1 - \gamma_k)\gamma_k C_k + \frac{\gamma_k^2}{2}C_k
$$
  
=  $C_k \gamma_k \left(1 - \frac{\gamma_k}{2}\right)$   
$$
\leq C_k \gamma_k \left(1 - \frac{\gamma_{k+1}}{2}\right)
$$
  
$$
\leq C_k \frac{1}{1/\gamma_{k+1} - 1/2} \left(1 - \frac{\gamma_{k+1}}{2}\right)
$$
  
=  $C_k \gamma_{k+1}$   
$$
\leq C_{k+1} \gamma_{k+1}.
$$

**Corollary A.6.** *Under the assumptions of Theorem A.5, suppose in addition that*  $\Delta_k = \Delta \gamma_k^{\zeta}$ *k , for some*  $\zeta \in [0, 1]$  *and*  $\Delta \geq 0$ *. Then we have* 

$$
\mathsf{G}(w_k) - \min \mathsf{G} \le C_{\mathsf{G}} \gamma_k + \Delta \gamma_k^{\zeta}.\tag{A.8}
$$

*Proof.* It follows from Theorem A.5 by noting that the sequence  $\Delta_k/\gamma_k = 1/\gamma_k^{1-\zeta}$  is nondecreasing.

<span id="page-3-0"></span>**Proposition A.7.** Suppose that there exists a mapping  $\nabla G: \mathcal{D} \to \mathcal{W}$  such that<sup>[2](#page-3-1)</sup>,

$$
(\forall w \in \mathcal{D})(\forall z \in \mathcal{D}) \quad \langle \nabla G(w), z - w \rangle = G'(w; z - w). \tag{A.9}
$$

*Then the following holds.*

<span id="page-3-2"></span>(i) Let  $k \in \mathbb{N}$  and suppose that there exists  $u_k \in \mathcal{W}$  such that  $||u_k - \nabla \mathsf{G}(w_k)|| \leq \Delta_{1,k}/4$  and that *zk*+1 ∈ D *satisfies*

<span id="page-3-4"></span><span id="page-3-3"></span>
$$
\langle u_k, z_{k+1} \rangle \le \min_{z \in \mathcal{D}} \langle u_k, z \rangle + \frac{\Delta_{2,k}}{2},
$$

*for some*  $\Delta_{1,k}, \Delta_{2,k} > 0$ *. Then* 

$$
G'(w_k; z_{k+1} - w_k) \le \min_{z \in \mathcal{D}} G'(w_k; z - w_k) + \frac{1}{2} (\Delta_{1,k} \text{diam}(\mathcal{D}) + \Delta_{2,k}).
$$
 (A.10)

<span id="page-3-5"></span>(ii) *Suppose that*  $\nabla G: \mathcal{D} \to \mathcal{W}$  *is L-Lipschitz continuous for some*  $L > 0$ *. Then, for every*  $w, z \in \mathcal{D}$  $and \gamma \in [0,1],$ 

$$
\mathsf{G}(w + \gamma(z - w)) - \mathsf{G}(w) - \gamma \langle z - w, \nabla \mathsf{G}(w) \rangle \leq \frac{L}{2} \gamma^2 ||z - w||^2
$$

*and hence*  $C_G \leq L \text{diam}(\mathcal{D})^2$ .

<span id="page-3-1"></span><sup>&</sup>lt;sup>2</sup>This mapping does not need to be unique.

*Proof.* [\(i\):](#page-3-2) We have

$$
\langle \nabla G(w_k), z_{k+1} - w_k \rangle = \langle u_k, z_{k+1} - w_k \rangle + \langle \nabla G(w_k) - u_k, z_{k+1} - w_k \rangle
$$
  
$$
\leq \min_{z \in \mathcal{D}} \langle u_k, z - w_k \rangle + \frac{\Delta_{2,k}}{2} + \frac{\Delta_{1,k}}{4} \operatorname{diam}(\mathcal{D}). \tag{A.11}
$$

Moreover,

$$
(\forall z \in \mathcal{D}) \quad \langle u_k, z - w_k \rangle = \langle \nabla G(w_k), z - w_k \rangle + \langle u_k - \nabla G(w_k), z - w_k \rangle
$$
$$
\leq \langle \nabla G(w_k), z - w_k \rangle + \frac{\Delta_{1,k}}{4} \text{diam}(\mathcal{D}),
$$

<span id="page-4-1"></span>hence

$$
\min_{z \in \mathcal{D}} \langle u_k, z - w_k \rangle \le \min_{z \in \mathcal{D}} \langle \nabla \mathsf{G}(w_k), z - w_k \rangle + \frac{\Delta_{1,k}}{2} \operatorname{diam}(\mathcal{D}). \tag{A.12}
$$

Thus,  $(A.10)$  follows from  $(A.11)$ ,  $(A.12)$ , and  $(A.9)$ .

[\(ii\):](#page-3-5) Let  $w, z \in \mathcal{D}$ , and define  $\psi : [0, 1] \to \mathcal{W}^*$  such that,  $\forall \gamma \in [0, 1], \psi(\gamma) = \mathsf{G}(w + \gamma(z - w))$ . Then, it is easy to see that for every  $\gamma \in (0,1], \psi$  is differentiable at  $\gamma$  and  $\psi'(\gamma) = G'(w + \gamma(z-w); z-w) =$  $\langle \nabla G(w + \gamma(z - w)), z - w \rangle$ . Moreover,  $\psi$  is continuous on [0, 1]. Therefore, the fundamental theorem of calculus yields

<span id="page-4-0"></span>
$$
\psi(\gamma) - \psi(0) = \int_0^\gamma \psi(t) dt
$$

and hence

$$
\begin{aligned} G(w + \gamma(z - w)) - G(w) - \langle \nabla G(w), z - w \rangle &= \int_0^\gamma \langle \nabla G(w + t(z - w)) - \nabla G(w), z - w \rangle \, dt \\ &\le \int_0^\gamma \|\nabla G(w + t(z - w)) - \nabla G(w)\| \, \|z - w\| \, dt \\ &\le \int_0^\gamma Lt \, \|z - w\|^2 \, dt \\ &= L \frac{\gamma^2}{2} \, \|z - w\|^2 \, . \end{aligned}
$$

The following result is an extension of a classical result on the directional differentiability of a max function [7, Theorem 4.13] which relaxes the inf-compactness condition and allows the parameter space to be a convex set, instead of the entire Banach space. This result provide a prototype of functions (of which the entropic regularization of the Wasserstein distance is an instance) which are directionally differentiable only along the feasible directions of their domain and satisfies the hypotheses of Proposition [A.7.](#page-3-0)

<span id="page-4-2"></span>**Proposition A.8.** *Let Z and* W *be real Banach spaces and let* W<sup>∗</sup> *be the topological dual of* W*. Let*  $D \subset \mathcal{W}^*$  be a nonempty closed convex set, and let  $g: Z \times \mathcal{W}^* \to \mathbb{R}$  be such that

- 1) *for every*  $z \in Z$ ,  $g(z, \cdot): \mathcal{W}^* \to \mathbb{R}$  *is Gâteaux differentiable with derivative in* W, and the partial *derivative with respect to the second variable*  $D_2q: Z \times W^* \to W$  *is continuous.*
- 2) *for every*  $w \in \mathcal{D}$ ,  $S(w) := \operatorname{argmax}_Z g(\cdot, w) \neq \emptyset$ .

3) *there exists a continuous mapping*  $\varphi: \mathcal{D} \to Z$  *such that, for every*  $w \in \mathcal{D}$ ,  $\varphi(w) \in S(w)$ *. Let*  $G: \mathcal{D} \to \mathbb{R}$  *be defined as* 

<span id="page-5-5"></span><span id="page-5-3"></span>
$$
\mathsf{G}(w) = \max_{z \in Z} g(z, w). \tag{A.13}
$$

*Then,* G *is continuous, directionally differentiable, and, for every*  $w \in \mathcal{D}$  and  $v \in \mathcal{F}_{\mathcal{D}}(w)$ 

$$
\mathsf{G}'(w;v) = \max_{z \in S(w)} \langle D_2 g(z,w), v \rangle = \langle D_2 g(\varphi(w), w), v \rangle. \tag{A.14}
$$

*Proof.* The function G is well defined, since by assumption 2), for every  $w \in \mathcal{D}$ ,  $\argmax_{Z} g(\cdot, w) \neq \emptyset$ . Let  $w, u \in \mathcal{D}$  with  $w \neq u$ . Then, since  $\varphi(w) \in S(w)$ , we have  $\mathsf{G}(w) = g(\varphi(w), w)$  and hence

$$
\frac{G(u) - G(w) - \langle D_2 g(\varphi(w), w), u - w \rangle}{\|u - w\|} \ge \frac{g(\varphi(w), u) - g(\varphi(w), w) - \langle D_2 g(\varphi(w), w), u - w \rangle}{\|u - w\|} \to 0, \quad (A.15)
$$

since  $g(\varphi(w), \cdot)$  is Fréchet differentiable<sup>[3](#page-5-0)</sup> at *w* with gradient  $D_2g(\varphi(w), w)$ . Now,  $\varphi(u) \in S(u)$ , and hence  $G(u) = g(\varphi(u), u)$ . Moreover,  $g(\varphi(u), w) \le G(w)$ . Therefore,

$$
\frac{G(u) - G(w) - \langle D_2 g(\varphi(w), w), u - w \rangle}{\|u - w\|} \le \frac{g(\varphi(u), u) - g(\varphi(u), w) - \langle D_2 g(\varphi(w), w), u - w \rangle}{\|u - w\|}. \quad (A.16)
$$

Let  $\varepsilon > 0$ . Since  $D_2g$  is continuous, there exists  $\delta > 0$  such that, for every  $z' \in Z$  and  $w' \in \mathcal{W}^*$ 

$$
||z' - \varphi(w)|| \le \delta \text{ and } ||w' - w|| \le \delta \implies ||D_2 g(z', w') - D_2 g(\varphi(w), w)|| \le \varepsilon. \tag{A.17}
$$

Moreover, since  $\varphi: \mathcal{D} \to Z$  is continuous, there exists  $\eta > 0$  such that,

<span id="page-5-4"></span>
$$
||u - w|| \le \eta \implies ||\varphi(u) - \varphi(w)|| \le \delta. \tag{A.18}
$$

Let  $z' \in Z$  and suppose that  $||z' - \varphi(w)|| \le \delta$  and  $||u - w|| \le \delta$ . Define  $\psi : [0, 1] \to \mathbb{R}$  such that, for every  $s \in [0,1], \psi(s) = g(z', w + s(u - w))$ . Then,  $\psi$  is continuously differentiable on [0,1] and  $\psi'(s) = \langle D_2 g(z', w + s(u - w)), u - w \rangle$ . Therefore,

<span id="page-5-2"></span><span id="page-5-1"></span>
$$
\psi(1) - \psi(0) = \int_0^1 \psi'(s) ds \tag{A.19}
$$

and hence, it follows from  $(A.17)$  that

$$
|g(z', u) - g(z', w) - \langle D_2 g(\varphi(w), w), u - w \rangle|
$$
  
= 
$$
\left| \int_0^1 \langle D_2 g(z', w + s(u - w)) - D_2 g(\varphi(w), w), u - w \rangle ds \right|
$$
  
$$
\leq \int_0^1 \| D_2 g(z', w + s(u - w)) - D_2 g(\varphi(w), w) \| \|u - w\| ds
$$
  
$$
\leq \varepsilon \|u - w\|.
$$

<span id="page-5-0"></span><sup>&</sup>lt;sup>3</sup>continuously Gâteaux differentiable function are Fréchet differentiable [7, pp.34-35].

Therefore, we derive from [\(A.18\)](#page-5-2), that for every  $u \in \mathcal{D}$  such that  $||u - w|| \le \min\{\eta, \delta\}$ , we have

$$
\left|\frac{g(\varphi(u),u)-g(\varphi(u),w)-\langle D_2g(\varphi(w),w),u-w\rangle}{\|u-w\|}\right|\leq\varepsilon.
$$

This shows that

<span id="page-6-1"></span>
$$
\lim_{\substack{u \in \mathcal{D} \\ u \to w}} \frac{g(\varphi(u), u) - g(\varphi(u), w) - \langle D_2 g(\varphi(w), w), u - w \rangle}{\|u - w\|} = 0.
$$
\n(A.20)

Then, we derive from  $(A.15)$ ,  $(A.16)$ , and  $(A.20)$  that

<span id="page-6-2"></span>
$$
\lim_{\substack{u \in \mathcal{D} \\ u \to w}} \frac{\mathsf{G}(u) - \mathsf{G}(w) - \langle D_2 g(\varphi(w), w), u - w \rangle}{\|u - w\|} = 0.
$$
\n(A.21)

This implies that  $\lim_{u \in \mathcal{D}, u \to w} \mathsf{G}(u) = \mathsf{G}(w)$ . Moreover, if  $v \in \mathcal{F}_{\mathcal{D}}(w)$ , there exists  $\lambda > 0$  and  $u \in \mathcal{D}$ such that  $v = \lambda(u - w)$  and, for every  $t \in [0, 1/\lambda]$ ,

$$
\frac{\mathsf{G}(w+tv) - \mathsf{G}(w)}{t} - \langle D_2 g(\varphi(w), w), v \rangle
$$
  

$$
= \|\lambda(u-w)\| \frac{\mathsf{G}(w+t\lambda(u-w)) - \mathsf{G}(w) - \langle D_2 g(\varphi(w), w), t\lambda(u-w)\rangle}{\|t\lambda(u-w)\|} \quad \text{(A.22)}
$$

and the right hand side goes to zero as  $t \to 0^+$ , because of  $(A.21)$ . Therefore, for every  $z \in S(w)$ , since  $G(w) = g(z, w)$  and  $G(w + tv) \ge g(z, w + tv)$ , we have

$$
\langle D_2 g(\varphi(w), w), v \rangle = \lim_{t \to 0^+} \frac{\mathsf{G}(w + tv) - \mathsf{G}(w)}{t} \ge \lim_{t \to 0^+} \frac{g(z, w + tv) - g(z, w)}{t} = \langle D_2 g(z, w), v \rangle
$$
\n(A.14) follows.

and [\(A.14\)](#page-5-5) follows.

<span id="page-6-0"></span>

## **B DAD problems and convergence of Sinkhorn-Knopp algorithm**

In this section we review the basic concepts of the nonlinear Perron-Frobenius theory [32] which provides tools for dealing with DAD problems and ultimately to study the key properties of the Sinkhorn potentials. This analysis will allow us to provide in Appendix  $C$  an upper bound estimate for the Lipschitz constant of the gradient of  $B_{\varepsilon}$ , which is needed in the Frank-Wolfe algorithm.

#### **B.1 Hilbert's metric and the Birkhoff-Hopf theorem**

In the rest of the appendix we will assume  $\mathcal{X} \subset \mathbb{R}^d$  to be a compact set. We denote by  $\mathcal{C}(\mathcal{X})$  the space of continuous functions on X endowed with the sup norm, namely  $||f||_{\infty} = \sup_{x \in \mathcal{X}} |f(x)|$ . Let  $\mathcal{C}_+(\mathcal{X})$  be the cone of non-negative continuous functions, that is,  $f \in \mathcal{C}(\mathcal{X})$  such that  $f(x) \geq 0$  for every  $x \in \mathcal{X}$ . Also, we denote by  $\mathcal{C}_{++}(\mathcal{X})$  the set of continuous and (strictly) positive functions on  $\mathcal{X}$ , which turns out to be the interior of  $C_+(\mathcal{X})$ .

<span id="page-6-3"></span>Let  $c: \mathcal{X} \times \mathcal{X} \to \mathbb{R}_+$  be a positive, symmetric, and continuous function and define  $k: \mathcal{X} \times \mathcal{X} \to \mathbb{R}_{++}$ as

$$
(\forall x, y \in \mathcal{X}) \qquad \mathsf{k}(x, y) = e^{-\frac{\mathsf{c}(x, y)}{\varepsilon}}.\tag{B.1}
$$

Set  $D = \sup_{x,y \in \mathcal{X}} c(x,y)$ . Then, we have  $\mathsf{k}(x,y) \in [e^{-D/\varepsilon},1]$  for all  $x,y \in \mathcal{X}$ . Let  $\alpha \in \mathcal{M}_1^+(\mathcal{X})$ . The operator  $L_{\alpha}: \mathcal{C}(\mathcal{X}) \to \mathcal{C}(\mathcal{X})$  is defined as

$$
(\forall f \in \mathcal{C}(\mathcal{X})) \qquad \mathsf{L}_{\alpha} f \colon x \mapsto \int \mathsf{k}(x, z) f(z) \; d\alpha(z). \tag{B.2}
$$

Note that  $L_{\alpha}$  is linear and continuous. In particular, since  $k(x, y) \in [0, 1]$  for all  $x, y \in \mathcal{X}$ , we have

<span id="page-7-0"></span>
$$
(\forall f \in C_{+}(\mathcal{X})) \qquad \mathsf{L}_{\alpha} f \ge 0 \tag{B.3}
$$

<span id="page-7-1"></span>and

$$
(\forall f \in \mathcal{C}(\mathcal{X})) \qquad \|\mathsf{L}_{\alpha}f\|_{\infty} \le \|f\|_{\infty}.
$$
 (B.4)

**Hilbert's Metric.** The cone  $\mathcal{C}_+(\mathcal{X})$  induces a partial ordering  $\leq$  on  $\mathcal{C}(\mathcal{X})$ , such that

$$
(\forall f, f' \in \mathcal{C}(\mathcal{X})) \qquad f \le f' \Leftrightarrow f' - f \in \mathcal{C}_+(\mathcal{X}). \tag{B.5}
$$

According to [32], we say that a function  $f' \in C_+(\mathcal{X})$  *dominates*  $f \in C(\mathcal{X})$  if there exist  $t, s \in \mathbb{R}$ such that

$$
tf' \le f \le sf'.\tag{B.6}
$$

This notion induces an equivalence relation on  $\mathcal{C}_+(\mathcal{X})$ , denoted  $f \sim f'$ , meaning that f dominates  $f'$ and *f'* dominates *f*. The corresponding equivalence classes are called *parts* of  $C_+(\mathcal{X})$ . Let  $f, f' \in C_+(\mathcal{X})$ be such that  $f \sim f'$ . We define

$$
M(f/f') = \inf\{s \in \mathbb{R} \mid f \le sf'\} \quad \text{and} \quad m(f/f') = \sup\{t \in \mathbb{R} \mid tf' \le f\}. \tag{B.7}
$$

Note that  $m(f/f') \leq M(f/f')$ . Moreover, for every  $f, f' \in C_+(\mathcal{X})$  such that  $f \sim f'$ , we have that  $\text{supp}(f) = \text{supp}(f')$  and if  $f' \neq 0$  (hence  $f \neq 0$ ), then

$$
M(f/f') = \max_{x \in \text{supp}(f')} \frac{f(x)}{f'(x)} > 0 \quad \text{and} \quad m(f/f') = \min_{x \in \text{supp}(f')} \frac{f(x)}{f'(x)} > 0.
$$
 (B.8)

The *Hilbert's metric* is defined as

$$
d_H(f, f') = \log \frac{M(f/f')}{m(f/f')},\tag{B.9}
$$

for all  $f \sim f'$  with  $f \neq 0$  and  $f' \neq 0$ ,  $d_H(0,0) = 0$  and  $d_H(f, f') = +\infty$  otherwise. Direct calculation shows that [33, Proposition 2.1.1]

(i) 
$$
d_H(f, f') \ge 0
$$
 and  $d_H(f, f') = d_H(f', f)$ , for every  $f, f' \in C_+(\mathcal{X})$ ;

(ii) 
$$
d_H(f, f'') \leq d_H(f, f') + d_H(f', f'')
$$
, for every  $f, f', f'' \in C_+(\mathcal{X})$  with  $f \sim f'$  and  $f' \sim f''$ ;

(iii) 
$$
d_H(sf, tf') = d_H(f, f')
$$
, for every  $f, f' \in C_+(\mathcal{X})$  and  $s, t > 0$ .

Note that  $d_H$  is not a metric on the parts of  $\mathcal{C}_+(\mathcal{X})$ . However the set  $\mathcal{C}_{++}(\mathcal{X}) \cap \partial B_1(0) = \{f \in$  $\mathcal{C}_{++}(\mathcal{X}) \mid \|f\|_{\infty} = 1$  equipped with  $d_H$  is a complete metric space [37]. Also,  $d_H$  induces a metric on the rays of the parts of  $C_{+}(\mathcal{X})$  [33, Lemma 2.1].

<span id="page-7-2"></span>We now focus on  $\mathcal{C}_{++}(\mathcal{X})$ . A direct consequence of Hilbert's metric properties is the following.

**Lemma B.1** (Hilbert's Metric on  $\mathcal{C}_{++}(\mathcal{X})$ ). The interior of  $\mathcal{C}_{+}(\mathcal{X})$  corresponds to the set of (strictly) positive functions  $C_{++}(\mathcal{X})$  and is a part of  $C_{+}(\mathcal{X})$  with respect to the equivalence relation induced by *dominance. For every*  $f, f' \in C_{++}(\mathcal{X})$ *,* 

$$
M(f/f') = \max_{x \in \mathcal{X}} \frac{f(x)}{f'(x)} \qquad m(f/f') = \min_{x \in \mathcal{X}} \frac{f(x)}{f'(x)},
$$
(B.10)

 $\dim M(f/f') \geq m(f/f') > 0$ . Therefore

<span id="page-8-6"></span>
$$
d_H(f, f') = \log \max_{x, y \in \mathcal{X}} \frac{f(x) \ f'(y)}{f(y) \ f'(x)}.
$$
 (B.11)

*Proof.* Since X is compact it is straightfoward to see that  $C_{++}(\mathcal{X})$  is the interior of  $C_{+}(\mathcal{X})$ . By applying [32, Lemma 1.2.2] we have that  $\mathcal{C}_{++}(\mathcal{X})$  is a part of  $\mathcal{C}_{+}(\mathcal{X})$ . The characterization of  $M(f/f')$ and  $m(f/f')$  follow by direct calculation from the definition using the fact that  $\inf_{\mathcal{X}} h = \min_{\mathcal{X}} h > 0$ for any  $h \in C_{++}(\mathcal{X})$  since X is compact. Finally, the characterization of Hilbert's metric on  $C_{++}(\mathcal{X})$ is obtained by recalling that  $(\min_{x \in \mathcal{X}} h(x))^{-1} = \max_{x \in \mathcal{X}} h(x)^{-1}$  for every  $h \in \mathcal{C}_{++}(\mathcal{X})$ .  $\Box$ 

<span id="page-8-5"></span>**Lemma B.2** (Ordering properties of  $L_{\alpha}$ ). Let  $\alpha \in M_1^+(\mathcal{X})$ . Then the following holds:

<span id="page-8-0"></span>(i) the operator  $L_{\alpha}$  is order-preserving *(with respect to the cone*  $\mathcal{C}_+(\mathcal{X})$ *), that is,* 

$$
(\forall f, f' \in \mathcal{C}(\mathcal{X})) \qquad f \le f' \ \Rightarrow \ \mathsf{L}_{\alpha} f \le \mathsf{L}_{\alpha} f'; \tag{B.12}
$$

<span id="page-8-1"></span>(ii)  $L_{\alpha}$  *maps parts of*  $C_{+}(\mathcal{X})$  *to parts of*  $C_{+}(\mathcal{X})$ *, that is,* 

<span id="page-8-3"></span>
$$
(\forall f, f' \in \mathcal{C}(\mathcal{X})) \qquad f \sim f' \Rightarrow \mathsf{L}_{\alpha} f \sim \mathsf{L}_{\alpha} f'; \tag{B.13}
$$

<span id="page-8-2"></span>(iii)  $L_{\alpha}(\mathcal{C}_{+}(\mathcal{X})) \subset \mathcal{C}_{++}(\mathcal{X}) \cup \{0\}$  *and*  $L_{\alpha}(\mathcal{C}_{++}(\mathcal{X})) \subset \mathcal{C}_{++}(\mathcal{X})$ *.* 

*Proof.* [\(i\):](#page-8-0) Let  $f, f' \in C(\mathcal{X})$  with  $f \leq f'$ . Then  $f' - f \in C_+(\mathcal{X})$  and by linearity of  $L_\alpha$  combined with [\(B.3\)](#page-7-0), we have  $\mathsf{L}_{\alpha}f' - \mathsf{L}_{\alpha}f = \mathsf{L}_{\alpha}(f - f') \geq 0$ .

[\(ii\):](#page-8-1) Let *f*,  $f' \in C_+(\mathcal{X})$  with  $f \sim f'$ . Then there exist  $t, s \in \mathbb{R}$  and  $s', t' \in \mathbb{R}$  such that  $tf' \leq f \leq sf'$ and  $t'f \leq f' \leq s'f$ . Since  $L_{\alpha}$  is linear and order-preserving, we have  $L_{\alpha}f \sim L_{\alpha}f'$ .

[\(iii\):](#page-8-2) Let  $f \in C_+(\mathcal{X})$ . By [\(B.3\)](#page-7-0) and [\(B.4\)](#page-7-1), for any  $x \in \mathcal{X}$ 

$$
0 \le (\mathsf{L}_{\alpha} f)(x) \le \|\mathsf{L}_{\alpha} f\|_{\infty} \le \int f(x) \, d\alpha(x) = \|f\|_{L^{1}(\mathcal{X},\alpha)}.
$$
 (B.14)

<span id="page-8-4"></span>Moreover,

$$
\mathsf{L}_{\alpha}f(x) = \int k(y, x)f(y) \, d\alpha(y) \ge e^{-\mathsf{D}/\varepsilon} \, \|f\|_{L^{1}(\mathcal{X}, \alpha)}.
$$
 (B.15)

Therefore, if  $||f||_{L^1(\mathcal{X},\alpha)} = 0$  then by [\(B.14\)](#page-8-3) L<sub>α</sub>*f* = 0 while, if  $||f||_{L^1(\mathcal{X},\alpha)} > 0$  then by [\(B.15\)](#page-8-4)  $L_{\alpha} f \in C_{++}(\mathcal{X})$ . We conclude that the operator  $L_{\alpha}$  maps  $C_{+}(\mathcal{X})$  in  $C_{++}(\mathcal{X}) \cup \{0\}$ . Moreover,  $L_{\alpha}(\mathcal{C}_{++}(\mathcal{X})) \subset \mathcal{C}_{++}(\mathcal{X})$ , since for every  $f \in \mathcal{C}_{++}(\mathcal{X})$  we have  $||f||_{L^1(\mathcal{X},\alpha)} \ge \min_{\mathcal{X}} f > 0$ .  $\Box$ 

Following [32, Section A.4] we now introduce a quantity which plays a central role in our analysis.

**Definition B.1** (Projective Diameter of  $L_{\alpha}$ ). Let  $\alpha \in M_1^+(\mathcal{X})$ . The projective diameter of  $L_{\alpha}$  *is* 

$$
\Delta(\mathsf{L}_{\alpha}) = \sup \{ d_H(\mathsf{L}_{\alpha}f, \mathsf{L}_{\alpha}f') \mid f, f' \in \mathcal{C}_+(\mathcal{X}), \ \mathsf{L}_{\alpha}f \sim \mathsf{L}_{\alpha}f' \}. \tag{B.16}
$$

The following result shows that it is possible to find a finite upper bound on  $\Delta(L_{\alpha})$  that is independent on *α*.

<span id="page-9-0"></span>**Proposition B.3** (Upper bound on the Projective Diameter of  $L_{\alpha}$ ). Let  $\alpha \in M_1^+(\mathcal{X})$ . Then

$$
\Delta(\mathsf{L}_{\alpha}) \le 2\mathsf{D}/\varepsilon. \tag{B.17}
$$

*Proof.* Let  $f, f' \in C_+(\mathcal{X})$ . Recall that  $L_\alpha$  maps  $C_+(\mathcal{X})$  into  $C_{++}(\mathcal{X}) \cup \{0\}$  (see Lemma [B.2](#page-8-5) [\(iii\)\)](#page-8-2) and that  $\{0\}$  and  $\mathcal{C}_{++}(\mathcal{X})$  are two parts of  $\mathcal{C}_+(\mathcal{X})$  with respect to the relation ~ (see [32, Lemma 1.2.2]). Now, if  $L_\alpha f = L_\alpha f' = 0$ , then we have  $d_H(L_\alpha f, L_\alpha f') = d_H(0,0) = 0$ . Therefore it is sufficient to study the case that  $L_{\alpha} f, L_{\alpha} f' \in C_{++}(\mathcal{X})$ . Following the characterization of Hilbert's metric on  $C_{++}(\mathcal{X})$ given in Lemma [B.1,](#page-7-2) we have

$$
d_H(\mathsf{L}_\alpha f, \mathsf{L}_\alpha f') = \log \max_{x,y \in \mathcal{X}} \frac{(\mathsf{L}_\alpha f)(x) (\mathsf{L}_\alpha f')(y)}{(\mathsf{L}_\alpha f)(y) (\mathsf{L}_\alpha f')(x)}
$$
  

$$
= \log \max_{x,y \in \mathcal{X}} \frac{\int k(x,z)f(z) d\alpha(z) \int k(y,w)f'(w) d\alpha(w)}{\int k(y,z)f(z) d\alpha(z) \int k(x,w)f'(w) d\alpha(w)}
$$
  

$$
= \log \max_{x,y \in \mathcal{X}} \frac{\int k(x,z)k(y,w) f(z)f'(w) d\alpha(z)d\alpha(w)}{\int k(y,z)k(x,w) f(z)f'(w) d\alpha(z)d\alpha(w)}
$$
  

$$
= \log \max_{x,y \in \mathcal{X}} \int \frac{k(x,z)k(y,w)}{k(y,z)k(x,w)} f(z)f'(w) d\alpha(z)d\alpha(w)
$$
  

$$
\leq \log \max_{x,y,z,w \in \mathcal{X}} \frac{k(x,z)k(y,w)}{k(y,z)k(x,w)}.
$$

Since, for every  $x, y \in \mathcal{X}$ ,  $c(x, y) \in [0, D]$ , we have  $k(x, y) \in [e^{-D/\varepsilon}, 1]$  and hence

$$
d_H(\mathsf{L}_{\alpha}f, \mathsf{L}_{\alpha}f') \le 2\mathsf{D}/\varepsilon. \qquad \qquad \Box
$$

A consequence of Proposition [B.3](#page-9-0) is a special case of Birkhoff-Hopf theorem. **Theorem B.4** (Birkhoff-Hopf Theorem). Let  $\lambda = e^{D/\varepsilon} - 1$  $e^{D/\varepsilon}$ <sub>c</sub>D/ $\varepsilon$ <sub>1</sub> *and*  $\alpha \in \mathcal{M}_1^+(\mathcal{X})$ *. Then, for every f*,  $f' \in C_+(X)$  *such that*  $f \sim f'$ *, we have* 

<span id="page-9-1"></span>
$$
d_H(\mathsf{L}_{\alpha}f, \mathsf{L}_{\alpha}f') \le \lambda \, d_H(f, f'). \tag{B.18}
$$

*Proof.* The statement is a direct application of the Birkhoff-Hopf theory [32, Sections A.4 and A.7] The *Birkhoff contraction ratio* of  $L_{\alpha}$  is defined as

$$
\kappa(\mathsf{L}_{\alpha}) = \inf \{ \hat{\lambda} \in \mathbb{R}_+ \mid d_H(\mathsf{L}_{\alpha}f, \mathsf{L}_{\alpha}f') \leq \hat{\lambda} d_H(f, f') \quad \forall f, f' \in \mathcal{C}_+(\mathcal{X}), \quad f \sim f' \}.
$$

Then it follows from Birkhoff-Hopf theorem [32, Theorem A.4.1] that

$$
\kappa(\mathsf{L}_{\alpha}) = \tanh\left(\frac{1}{4}\Delta(\mathsf{L}_{\alpha})\right). \tag{B.19}
$$

Recalling the upper bound on the projective diameter f  $\mathsf{L}_{\alpha}$  given in Proposition [B.3,](#page-9-0) we have

$$
\kappa(\mathsf{L}_{\alpha}) \leq \tanh\left(\frac{\mathsf{D}}{2\varepsilon}\right) = \frac{e^{\mathsf{D}/\varepsilon} - 1}{e^{\mathsf{D}/\varepsilon} + 1} = \lambda,
$$

and [\(B.18\)](#page-9-1) follows.

 $\Box$ 

#### **B.2 DAD problems**

**The map**  $A_{\alpha}$ . Let  $\alpha \in M^1_+(\mathcal{X})$ . We define the map  $A_{\alpha}: \mathcal{C}_{++}(\mathcal{X}) \to \mathcal{C}_{++}(\mathcal{X})$ , such that

$$
(\forall f \in C_{++}(\mathcal{X})) \qquad \mathsf{A}_{\alpha}(f) = \mathsf{R} \circ \mathsf{L}_{\alpha}(f) = 1/(\mathsf{L}_{\alpha}f), \tag{B.20}
$$

where R:  $C_{++}(\mathcal{X}) \rightarrow C_{++}(\mathcal{X})$  is defined by  $R(f) = 1/f$  with

<span id="page-10-3"></span>
$$
(1/f): x \mapsto \frac{1}{f(x)}.\tag{B.21}
$$

Note that  $A_\alpha$  is well defined since, by Lemma [B.2](#page-8-5) [\(iii\),](#page-8-2)  $L_\alpha(\mathcal{C}_{++}(\mathcal{X})) \subset \mathcal{C}_{++}(\mathcal{X})$  and, for every  $f \in \mathcal{C}_{++}(\mathcal{X})$ ,  $\min_{\mathcal{X}} f > 0$ , being  $\mathcal X$  compact. Moreover, it follows from [\(B.11\)](#page-8-6) in Lemma [B.1,](#page-7-2) that, for any two  $f, f' \in C_{++}(\mathcal{X})$ 

<span id="page-10-5"></span>
$$
d_H(1/f, 1/f') = \log \max_{x, y \in \mathcal{X}} \frac{f(y)f'(x)}{f(x)f'(y)} = d_H(f, f').
$$
 (B.22)

We highlight here the connection between  $\mathsf{T}_{\alpha}$  introduced in the main text in (3) and  $\mathsf{A}_{\alpha}$ , namely for any  $\alpha \in \mathcal{M}_1^+(\mathcal{X})$  and  $u \in \mathcal{C}(\mathcal{X})$ 

<span id="page-10-0"></span>
$$
T_{\alpha}(u) = \varepsilon \log(A_{\alpha}(e^{u/\varepsilon})).
$$
\n(B.23)

**Dual** OT<sub>ε</sub> **Problem.** focus on the dual problem (2) of the optimal transport problem with entropic regularization. Let  $\alpha, \beta \in \mathcal{M}_1^+(\mathcal{X})$  and  $\varepsilon > 0$ , we consider

$$
\max_{u,v \in \mathcal{C}(\mathcal{X})} \int u(x) \, d\alpha + \int v(y) \, d\beta(y) - \varepsilon \int e^{\frac{u(x) + v(y) - c(x,y)}{\varepsilon}} \, d\alpha(x) d\beta(y). \tag{B.24}
$$

The optimality conditions for problem [\(B.24\)](#page-10-0) are

<span id="page-10-4"></span>
$$
\begin{cases}\ne^{-\frac{u(x)}{\varepsilon}} = \int_{\mathcal{X}} e^{\frac{v(y) - c(x, y)}{\varepsilon}} d\beta(y) & (\forall x \in \text{supp}(\alpha)) \\
e^{-\frac{v(y)}{\varepsilon}} = \int_{\mathcal{X}} e^{\frac{u(x) - c(x, y)}{\varepsilon}} d\alpha(x) & (\forall y \in \text{supp}(\beta)),\n\end{cases} \tag{B.25}
$$

which are equivalent to

<span id="page-10-2"></span><span id="page-10-1"></span>
$$
\begin{cases}g(y)^{-1} = \int_{\mathcal{X}} e^{\frac{-c(x,y)}{\varepsilon}} f(x) \, d\alpha(x) & (\forall y \in \text{supp}(\beta)) \\ f(x)^{-1} = \int_{\mathcal{X}} e^{\frac{-c(x,y)}{\varepsilon}} g(y) \, d\beta(y) & (\forall x \in \text{supp}(\alpha))\end{cases} \tag{B.26}
$$

where  $f = e^{u/\varepsilon} \in C_{++}(\mathcal{X})$  and  $g = e^{v/\varepsilon} \in C_{++}(\mathcal{X})$ . In the rest of the section we will consider the following *DAD problem* [32, 38]

$$
(\forall y \in \mathcal{X}) \quad \int_{\mathcal{X}} f(x) \mathsf{k}(x, y) g(y) \, d\alpha(x) = 1 \quad \text{and} \quad (\forall x \in \mathcal{X}) \quad \int_{\mathcal{X}} f(x) \mathsf{k}(x, y) g(y) \, d\beta(y) = 1. \tag{B.27}
$$

It is clear that a solution of  $(B.27)$  is also a solution of  $(B.26)$ . However, the vice versa is in general not true, even though there is a canonical way to build solutions of [\(B.27\)](#page-10-1) starting from solutions of [\(B.26\)](#page-10-2): indeed if  $(f, g)$  is a solution of (B.26), then the functions  $\bar{f}, \bar{g}: \mathcal{X} \to \mathbb{R}$  defined through  $\bar{f}(x)^{-1} = \int_{\mathcal{X}} k(x, y)g(y) d\beta(y)$  and  $\bar{g}(y)^{-1} = \int_{\mathcal{X}} k(x, y)g(y) d\beta(y)$  provide a solution of [\(B.27\)](#page-10-1). So, the dual  $\text{OT}_{\varepsilon}$  problem [\(B.24\)](#page-10-0) admits a solution if and only if the corresponding DAD problem [\(B.27\)](#page-10-1) admits a solution. Recalling the definition of  $A_\alpha$  in [\(B.20\)](#page-10-3), problem [\(B.27\)](#page-10-1) can be more compactly written as

<span id="page-11-1"></span>
$$
f = A_{\beta}(g)
$$
 and  $g = A_{\alpha}(f)$ , (B.28)

<span id="page-11-2"></span>or equivalently, by setting  $A_{\beta\alpha} = A_{\beta} \circ A_{\alpha}$  and  $A_{\alpha\beta} = A_{\alpha} \circ A_{\beta}$ ,

$$
f = A_{\beta\alpha}(f)
$$
 and  $g = A_{\alpha\beta}(g)$ . (B.29)

This shows that the solutions of the DAD problem [\(B.27\)](#page-10-1) are the fixed points of  $A_{\alpha\beta}$  and  $A_{\beta\alpha}$ respectively. Note that the operators  $A_{\beta\alpha}$  and  $A_{\alpha\beta}$  are positively homogeneous, that is, for every  $t \in \mathbb{R}_{++}$  and  $f \in C_{++}(\mathcal{X})$ ,  $A_{\beta\alpha}(tf) = tA_{\beta\alpha}(f)$  and  $A_{\alpha\beta}(tf) = tA_{\alpha\beta}(f)$ . Thus, if *f* is a fixed point of  $A_{\beta\alpha}$ , then *tf* is also a fixed point of  $A_{\beta\alpha}$ , for every  $t > 0$ . If  $(f, g)$  is a solution of the DAD problem  $(B.27)$ , then the pair  $(u, v)$ , with  $u = \varepsilon \log f$  and  $v = \varepsilon \log g$  is a solution of  $(B.24)$ . We refer to these solutions as *Sinkhorn potentials* of the pair  $(\alpha, \beta)$ . Finally, note that, it follows from [\(B.25\)](#page-10-4) that solutions of [\(B.24\)](#page-10-0) are determined  $(\alpha, \beta)$ -a.e. on X and up to a translation of the form  $(u + t, v - t)$ , for some  $t \in \mathbb{R}$ .

The following result is essentially the specialization of [32, Thm. 7.1.4] to the case of the map  $A_{\beta\alpha}$ . We report the proof here for convenience and completeness.

**Theorem B.5** (Hilbert's metric contraction for  $A_{\beta\alpha}$ ). The map  $A_{\beta\alpha}$  :  $C_{++}(\mathcal{X}) \rightarrow C_{++}(\mathcal{X})$  has a unique *fixed point up to positive scalar multiples. Moreover, let*  $\lambda = \frac{e^{D/\varepsilon} - 1}{e^{D/\varepsilon} - 1}$  $e^{\frac{\rho}{c}\int_{\epsilon}^{-1}(\epsilon)}$  *e n for every*  $f, f' \in C_{++}(\mathcal{X})$ *,* 

<span id="page-11-0"></span>
$$
d_H(\mathsf{A}_{\beta\alpha}(f), \mathsf{A}_{\beta\alpha}(f')) \le \lambda^2 \ d_H(f, f'). \tag{B.30}
$$

*Proof.* By combining [\(B.22\)](#page-10-5) with Theorem B.4 we obtain that, for any  $f, f' \in C_{++}(\mathcal{X})$ 

$$
d_H(\mathsf{A}_{\alpha}(f), \mathsf{A}_{\alpha}(f')) = d_H(1/(\mathsf{L}_{\alpha}f), 1/(\mathsf{L}_{\alpha}f')) = d_H(\mathsf{L}_{\alpha}f, \mathsf{L}_{\alpha}f') \leq \lambda d_H(f, f'). \tag{B.31}
$$

Since the same holds for  $A_\beta$  then [\(B.30\)](#page-11-0) is satisfied. Now, let  $C = C_{++}(\mathcal{X}) \cap \partial B_1(0)$ . Let  $\overline{A}_{\beta \alpha}$ :  $C \to C$ be the map such that

$$
(\forall f \in C) \qquad \overline{\mathsf{A}}_{\beta\alpha}(f) = \frac{\mathsf{A}_{\beta\alpha}(f)}{\|\mathsf{A}_{\beta\alpha}(f)\|_{\infty}}.\tag{B.32}
$$

Then, since  $d_H(sf, tf') = d_H(f, f')$  for any  $s, t > 0$  and  $f, f' \in C$ , we have

$$
d_H(\overline{\mathsf{A}}_{\beta\alpha}(f), \overline{\mathsf{A}}_{\beta\alpha}(f')) = d_H(\mathsf{A}_{\beta\alpha}(f), \mathsf{A}_{\beta\alpha}(f')) \leq \lambda^2 \ d_H(f, f'). \tag{B.33}
$$

Since  $(C, d_H)$  is a complete metric space [37, Theorem 1.2] and  $\overline{A}_{\beta\alpha}$  is a contraction, we can apply Banach's contraction theorem and conclude that there exists a unique fixed point of  $A_{\beta\alpha}$ , namely a function  $f \in C$  such that

$$
\bar{f} = \overline{A}_{\beta\alpha}(\bar{f}) = \frac{A_{\beta\alpha}(\bar{f})}{\left\|A_{\beta\alpha}(\bar{f})\right\|_{\infty}}.
$$
\n(B.34)

Hence  $\bar{f}$  is an eigenvector for  $A_{\beta\alpha}$  with eigenvalue  $t = \|A_{\beta\alpha}(\bar{f})\|_{\infty} > 0$ . Now, we note that

$$
(\forall f, g \in \mathcal{C}_{++}(\mathcal{X})) \quad \langle g \mathsf{L}_{\alpha} f, \beta \rangle = \langle f \mathsf{L}_{\beta} g, \alpha \rangle = \int_{\mathcal{X} \times \mathcal{X}} f(x) k(x, y) g(y) d(\alpha \otimes \beta)(x, y). \tag{B.35}
$$

Set  $\bar{g} = A_{\alpha}(\bar{f})$ , so that  $A_{\beta}(\bar{g}) = t\bar{f}$ . Then, recalling the definitions of  $A_{\alpha}$  and  $A_{\beta}$ , we have  $\bar{g}L_{\alpha}\bar{f} \equiv 1$ and  $t^{-1} \equiv \bar{f}L_{\beta}\bar{g}$ . Hence  $t^{-1} = \langle \bar{f}L_{\beta}\bar{g}, \alpha \rangle = \langle \bar{g}L_{\alpha}\bar{f}, \beta \rangle = 1$ . Therefore  $\bar{f}$  is a fixed point of  $A_{\beta\alpha}$ . Finally, if  $\bar{f}' \in C_{++}(\mathcal{X})$  is a fixed point of  $A_{\beta\alpha}$ , then, since  $A_{\beta\alpha}$  is positively homogeneous, we have

$$
\overline{A}_{\beta\alpha}(\overline{f}'/\|\overline{f}'\|_{\infty}) = \frac{A_{\beta\alpha}(\overline{f}'/\|\overline{f}'\|_{\infty})}{\|A_{\beta\alpha}(f'/\|\overline{f}'\|_{\infty})\|_{\infty}} = \frac{A_{\beta\alpha}(\overline{f}')}{\|A_{\beta\alpha}(\overline{f}')\|_{\infty}} = \frac{\overline{f}'}{\|\overline{f}'\|_{\infty}},
$$
(B.36)

that is,  $\bar{f}' / ||\bar{f}'||_{\infty}$  is a fixed point of  $\bar{A}_{\beta\alpha}$ . Thus,  $\bar{f}' / ||\bar{f}'||_{\infty} = \bar{f}$  and hence  $\bar{f}'$  is a multiple of  $\bar{f}$ .  $\Box$ 

**Corollary B.6** (Existence and uniqueness of Sinkhorn potentials). Let  $\alpha, \beta \in M^1_+(\mathcal{X})$ . Then, the *DAD problem* [\(B.27\)](#page-10-1) *admits a solution* (*f, g*) *and every other solution is of type* (*tf, t*−<sup>1</sup> *g*)*, for some*  $t > 0$ . Moreover, there exists a pair  $(u, v) \in C(X)^2$  of Sinkhorn potentials and every other pair of *Sinkhorn potentials is of type*  $(u + s, v - s)$ *, for some*  $s \in \mathbb{R}$ *. In particular, for every*  $x_o \in \mathcal{X}$ *, there exist a unique pair*  $(u, v)$  *of Sinkhorn potentials such that*  $u(x_0) = 0$ .

*Proof.* It follows from Theorem B.5 and the discussion after  $(B.29)$ .

**Bounding**  $(f, g)$  **point-wise.** We conclude this section by providing additional properties of the solutions  $(f, g)$  of the DAD problem  $(B.28)$ . In particular, we show that there exists one such solution for which it is possible to provide a point-wise upper and lower bound independent on  $\alpha$  and  $\beta$ .

**Remark B.7.** *Let*  $f \in C_{++}(\mathcal{X})$  *and set*  $g = A_{\alpha}(f)$ *. Then, recalling* [\(B.20\)](#page-10-3) *and* [\(B.4\)](#page-7-1)*, we have that, for every*  $x \in \mathcal{X}$ ,

$$
1 = g(x)(\mathsf{L}_{\alpha} f)(x) \le g(x) \| \mathsf{L}_{\alpha} f \|_{\infty} \le g(x) \| f \|_{\infty}
$$

*and*

<span id="page-12-3"></span>
$$
1 = g(x)(L_{\alpha} f)(x) \ge g(x)(\min_{\mathcal{X}} f) \int k(x, z) \ d\alpha(z) \ge g(x)(\min_{\mathcal{X}} f) e^{-D/\varepsilon}
$$

*Therefore,*

<span id="page-12-5"></span>
$$
\min_{\mathcal{X}} g \ge \frac{1}{\|f\|_{\infty}} \quad \text{and} \quad \|g\|_{\infty} \le \frac{e^{D/\varepsilon}}{\min_{\mathcal{X}} f}.\tag{B.37}
$$

<span id="page-12-4"></span>**Lemma B.8.** *(Auxiliary Cone) Consider the set*

$$
K = \{ f \in \mathcal{C}_+(\mathcal{X}) \mid f(x) \le f(y) \ e^{\mathsf{D}/\varepsilon} \quad \forall x, y \in \mathcal{X} \}. \tag{B.38}
$$

Let  $\alpha \in M^1_+(\mathcal{X})$ *. Then the following holds.* 

- <span id="page-12-0"></span>(i) *K is a closed convex cone and*  $K \subset \mathcal{C}_{++}(\mathcal{X}) \cup \{0\};$
- <span id="page-12-1"></span>(ii)  $L_{\alpha}(\mathcal{C}_+(\mathcal{X})) \subset K$ ;
- <span id="page-12-2"></span>(iii)  $R(K)$  ⊂ *K*;

*.*

- <span id="page-13-0"></span>(iv) Ran( $A_{\alpha}$ ) ⊂ *K*;
- <span id="page-13-1"></span>(v) If  $f \in K$  and  $g = A_\alpha f$ , then  $g \in K$  and  $1 \leq (\min_{\mathcal{X}} g) ||f||_{\infty} \leq ||g||_{\infty} ||f||_{\infty} \leq e^{2\mathsf{D}/\varepsilon}$ .
- <span id="page-13-3"></span>(vi) *If*  $f \in K$  *is such that*  $f(x_o) = 1$  *for some*  $x_o \in \mathcal{X}$ *, then*  $\|\varepsilon \log f\|_{\infty} \leq D$ *.*

*Proof.* [\(i\):](#page-12-0) We see that for any  $f \in K$ ,

<span id="page-13-2"></span>
$$
\max_{\mathcal{X}} f \le (\min_{\mathcal{X}} f) \ e^{\mathsf{D}/\varepsilon},\tag{B.39}
$$

so, if  $f(x) = 0$  for some  $x \in \mathcal{X}$ , then  $f(x) = 0$  on all X. Hence  $K \subseteq \mathcal{C}_{++}(\mathcal{X}) \cup \{0\}$ . It is straightforward to verify that *K* is a convex cone. Moreover *K* is also closed. Indeed if  $(f_n)_{n\in\mathbb{N}}$  is a sequence in *K* which converges uniformly to  $f \in \mathcal{C}(\mathcal{X})$ , then, for every  $x, y \in \mathcal{X}$  and every  $n \in \mathbb{N}$ ,  $f_n(x) \leq f_n(y)e^{D/\varepsilon}$ and hence, letting  $n \to +\infty$ , we have  $f(x) \leq f(y)e^{D/\varepsilon}$ .

[\(ii\):](#page-12-1) For every  $f \in C_+(\mathcal{X})$  and  $x, y \in \mathcal{X}$ , we have

$$
(L_{\alpha}f)(x) = \int k(x, z) f(z) d\alpha(z)
$$
  
= 
$$
\int \frac{k(x, z)}{k(y, z)} k(y, z) f(z) d\alpha(z)
$$
  
$$
\leq e^{D/\varepsilon} \int k(y, z) f(z) d\alpha(z)
$$
  
= 
$$
e^{D/\varepsilon} (L_{\alpha}f)(y).
$$

[\(iii\):](#page-12-2) For every  $f \in K$ ,

$$
(\forall x, y \in \mathcal{X}) \qquad f(x) \le f(y) \ e^{\mathsf{D}/\varepsilon} \iff \frac{1}{f(y)} \le \frac{1}{f(x)} \ e^{\mathsf{D}/\varepsilon}.
$$

- [\(iv\)](#page-13-0) It follows from [\(ii\)](#page-12-1) and [\(iii\)](#page-12-2) and the definitions of  $A_\alpha$ .
- <span id="page-13-4"></span> $(v)$ : It follows from [\(iv\),](#page-13-0) [\(B.37\)](#page-12-3), and [\(B.39\)](#page-13-2).

[\(vi\):](#page-13-3) Let  $f \in K$  be such that  $f(x_o) = 1$ . Then  $\min_{\mathcal{X}} f \leq 1 \leq \max_{\mathcal{X}} f$ . Thus, it follows from [\(B.39\)](#page-13-2) that

$$
\max_{\mathcal{X}} f \le e^{\mathsf{D}/\varepsilon} \quad \text{and} \quad \min_{\mathcal{X}} f \ge e^{-\mathsf{D}/\varepsilon} \tag{B.40}
$$

and hence, for every  $x \in \mathcal{X}$ ,  $-D \leq \varepsilon \log f(x) \leq D$ .

As a direct consequence of Lemma [B.8](#page-12-4) we can establish a uniform point-wise upper and lower bound for the value of DAD solutions.

<span id="page-13-5"></span>**Corollary B.9.** Let  $\alpha, \beta \in M_1^+(\mathcal{X})$ . Let  $x_o \in \mathcal{X}$  and let  $(f, g)$  be the solution of [\(B.28\)](#page-11-2) such that  $f(x_o) = 1$ *. Then*  $||f||_{\infty} \le e^{D/\varepsilon}$  *and*  $||g||_{\infty} \le e^{2D/\varepsilon}$ *. Moreover, the corrisponding pair*  $(u, v)$  *of Sinkhorn potentials satifies*  $||u||_{\infty} \le D$  *and*  $||v||_{\infty} \le 2D$ .

*Proof.* Since *f* and *g* are fixed points of  $A_{\beta\alpha}$  and  $A_{\alpha\beta}$  respectively, it follows from Lemma [B.8](#page-12-4) [\(iv\)](#page-13-0) that  $f, g \in K$ . Then, Lemma [B.8](#page-12-4) [\(vi\)](#page-13-3) yields  $||f||_{\infty} \leq e^{D/\varepsilon}$ , whereas by the second of [\(B.37\)](#page-12-3) and [\(B.40\)](#page-13-4) we derive that  $||g||_{\infty} \leq e^{2\mathsf{D}/\varepsilon}$ .  $\Box$ 

 $\Box$ 

#### **B.3 Sinkhorn-Knopp algorithm in infinite dimension**

In the context of optimal transport, Sinkhorn-Knopp algorithm is often presented and studied in finite dimension [13, 39]. The algorithm originates from so called *matrix scaling problems*, also called *DAD problems*, which consists in finding, for a given matrix *A* with nonnegative entries, two diagonal matrices  $D_1$ ,  $D_2$  such that  $D_1AD_2$  is doubly stochastic [42]. In our setting it is crucial to analyze the algorithm in infinite dimension.

Theorem B.5 shows that  $A_{\beta\alpha}$  is a contraction with respect to the Hilbert's metric. This suggests a direct approach to find the solutions of the DAD problem by adopting a fixed-point strategy, which turns out to applying the operators  $A_{\alpha}$  and  $A_{\beta}$  alternatively, starting from some  $f^{(0)} \in C_{++}(\mathcal{X})$ . This is exactly the approach to the Sinkhorn algorithm pioneered by [34, 22] and further developed in an infinite dimensional setting in [38]. In this section we review the algorithm and give the convergence properties for the special kernel  $k$  in  $(B.1)$ . In particular we provide rate of convergence in the sup norm  $\left\| \cdot \right\|_{\infty}$ .

**Algorithm B.1** Sinkhorn-Knopp algorithm (infinite dimensional case) Let  $\alpha, \beta \in \mathcal{M}^1_+(\mathcal{X})$ . Let  $f^{(0)} \in \mathcal{C}_{++}(\mathcal{X})$  and define,

> <span id="page-14-0"></span>for  $\ell = 0, 1,$ .  $g^{(\ell+1)} = A_{\alpha}(f^{(\ell)})$  $f^{(\ell+1)} = A_\beta(g^{(\ell+1)})$

<span id="page-14-2"></span>**Theorem B.10** (Convergence of Sinkhorn-Knopp algorithm). Let  $(f^{(\ell)})_{\ell \in \mathbb{N}}$  be defined according *to Algorithm B.1.* Let  $x_o \in \mathcal{X}$  and let  $(f, g)$  be the solution of the DAD problem [\(B.26\)](#page-10-2) such that  $f(x_o) = 1$ . Then, defining, for every  $\ell \in \mathbb{N}$ ,  $\tilde{f}^{(\ell)} = f^{(\ell)}/f^{(\ell)}(x_o)$  and  $\tilde{g}^{(\ell+1)} = g^{(\ell+1)}f^{(\ell)}(x_o)$ , we have

$$
\begin{cases} \|\log \tilde{f}^{(\ell)} - \log f\|_{\infty} \leq \lambda^{2\ell} \left(\frac{D}{\varepsilon} + \log \frac{\|f^{(0)}\|_{\infty}}{\min_{x} f^{(0)}}\right) \\ \|\log \tilde{g}^{(\ell+1)} - \log g\|_{\infty} \leq e^{3D/\varepsilon} \|\log \tilde{f}^{(\ell)} - \log f\|_{\infty}. \end{cases} \tag{B.41}
$$

*Moreover, let the potentials*  $(u, v) = (\varepsilon \log f, \varepsilon \log g)$  *and, for every*  $\ell \in \mathbb{N}$ ,  $(\tilde{u}^{(\ell)}, \tilde{v}^{(\ell)}) =$  $(\varepsilon \log \tilde{f}^{(\ell)}, \varepsilon \log \tilde{g}^{(\ell)})$ *. Then we have* 

<span id="page-14-1"></span>
$$
\|\tilde{u}^{(\ell)} - u\|_{\infty} \le \lambda^{2\ell} \bigg(\frac{\mathsf{D} + \max_{\mathcal{X}} u^{(0)} - \min_{\mathcal{X}} u^{(0)}}{\varepsilon}\bigg). \tag{B.42}
$$

*Proof.* Let A be the set in Lemma [C.1.](#page-7-2) Clearly, for every  $\ell \in \mathbb{N}$ , we have  $f^{(\ell+1)} = A_{\beta\alpha}(f^{(\ell)})$  and  $\tilde{f}, \tilde{f}^{\ell} \in \mathcal{A}$ . Thus, it follows from Theorem B.5 and [\(C.2\)](#page-16-1) in Lemma [C.1](#page-7-2) that, for every  $\ell \in \mathbb{N}$ ,

$$
\|\log \tilde{f}^{(\ell)} - \log f\|_{\infty} \le d_H(\tilde{f}^{\ell}, f) = d_H(\mathsf{A}^{(\ell)}_{\beta\alpha}(f^{(0)}), f) \le \lambda^{2\ell} d_H(f^{(0)}, f).
$$

Moreover, recalling  $(B.11)$ , we have

$$
d_H(f^{(0)}, f) = d_H(1/f^{(0)}, \mathsf{L}_{\beta}g) = \log \max_{x, y \in \mathcal{X}} \frac{f^{(0)}(y)\mathsf{L}_{\beta}g(y)}{f^{(0)}(x)\mathsf{L}_{\beta}g(x)} \le \log \left[ e^{\mathsf{D}/\varepsilon} \max_{x, y \in \mathcal{X}} \frac{f^{(0)}(y)}{f^{(0)}(x)} \right]
$$

where we used the fact that  $L_\beta(C_{++}(\mathcal{X})) \subset K$  and the definition [\(B.38\)](#page-12-5). Thus, the first inequality in [\(B.41\)](#page-14-0) follows. The second inequality in [\(B.41\)](#page-14-0) and [\(B.42\)](#page-14-1) follow directly from Lemma [C.3](#page-17-0) and the fact that  $u^{(0)} = \varepsilon \log f^{(0)}$ .  $\Box$ 

| <b>Algorithm B.2</b> Sinkhorn-Knopp algorithm (finite dimensional case)                                                                                                                                                                                     |
|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Let $M  otin \{ \mathbb R_{++}^{n_1 \times n_2} \}$ , $a  otin \{ \mathbb R_{+}^{n_1} \}$ , with $a^{\top} 1_{n_1} = 1$ , and $b  otin \{ \mathbb R_{+}^{n_2} \}$ , with $b^{\top} 1_{n_2} = 1$ . Let $f^{(0)}  otin \{ \mathbb R_{++}^{n_1} \}$ and define |
| for $\ell = 0, 1, \ldots$<br>$\left[ \begin{array}{c} g^{(\ell+1)} = \dfrac{b}{M^{\top} f^{(\ell)}} \[1ex] f^{(\ell+1)} = \dfrac{a}{M g^{(\ell+1)}}. \end{array} \right.$                                                                                   |

<span id="page-15-1"></span>**Proposition B.11.** *Suppose that α and β are probability measures with finite support. Then Algorithm B.1 can be reduced to the finite dimensional Algorithm B.2. More specifically, sup*pose that  $\alpha = \sum_{i=1}^{n_1} a_i \delta_{x_i}$ , and  $\beta = \sum_{i=1}^{n_2} b_i \delta_{y_i}$ , where  $a = (a_i)_{1 \leq i \leq n_1} \in \mathbb{R}_+^{n_1}$ ,  $\sum_{i=1}^{n_1} a_i = 1$  and  $\mathbf{b} = (b_i)_{1 \leq i \leq n_2} \in \mathbb{R}^{n_2}_+, \ \sum_{i=1}^n b_i = 1.$  Let  $\mathsf{K} \in \mathbb{R}^{n_1 \times n_2}$  be such that  $\mathsf{K}_{i_1, i_2} = \mathsf{k}(x_{i_1}, y_{i_2})$  and let  $M = \text{diag}(\overline{a})\overline{\text{K}}\text{diag}(b) \in \mathbb{R}^{n_1 \times n_2}$ . Let  $(f^{(\ell)})_{\ell \in \mathbb{N}}$  and  $(f^{(\ell)})_{\ell \in \mathbb{N}}$  be defined according to Algorithm B.2 and *Algorithm B.1 respectively, with*  $f^{(0)} = (f^{(0)}(x_i))_{1 \leq i \leq n_1}$ *. Then, for every*  $\ell \in \mathbb{N}$ *,* 

$$
(\forall x \in \mathcal{X})(\forall y \in \mathcal{X}) g^{(\ell+1)}(y)^{-1} = \sum_{i_1=1}^{n_1} k(x_{i_1}, y) a_{i_1} f_{i_1}^{(\ell)} \text{ and } f^{(\ell+1)}(x)^{-1} = \sum_{i_2=1}^{n_2} k(x, y_{i_2}) b_{i_2} g_{i_2}^{(\ell+1)}.
$$

Moreover, setting  $u^{(\ell)} = \varepsilon \log f^{(\ell)}$ ,  $v^{(\ell)} = \varepsilon \log g^{(\ell)}$ ,  $u^{(\ell)} = \varepsilon \log f^{(\ell)}$ , and  $v^{(\ell)} = \varepsilon \log g^{(\ell)}$ , we have

<span id="page-15-0"></span>
$$
(\forall y \in \mathcal{X}) v^{(\ell+1)}(y) = -\varepsilon \log \sum_{i_1=1}^{n_1} \exp(u_{i_1}^{(\ell)} - c(x_{i_1}, y)) a_{i_1}
$$

$$
(\forall x \in \mathcal{X}) u^{(\ell+1)}(x) = -\varepsilon \log \sum_{i_2=1}^{n_2} \exp(v_{i_2}^{(\ell+1)} - c(x, y_{i_2})) b_{i_2} \quad \text{(B.43)}
$$

*Proof.* Since  $\alpha$  and  $\beta$  have finite support, we derive from the definitions of  $f^{(\ell+1)}$  and  $g^{(\ell+1)}$  in Algorithm **B.1** and that of  $A_{\alpha}$  and  $A_{\beta}$  that

The following equations define the system:

$$
(\forall x \in \mathcal{X}) \quad g^{(\ell+1)}(y)^{-1} = (\text{L}_{\alpha}f^{(\ell)})(y) = \sum_{i_1=1}^{n_1} a_{i_1} \text{k}(x_{i_1}, y) f^{(\ell)}(x_{i_1})
$$

$$
(\forall y \in \mathcal{X}) \quad f^{(\ell+1)}(x)^{-1} = (\text{L}_{\beta}g^{(\ell+1)})(x) = \sum_{i_2=1}^{n_2} \text{k}(x, y_{i_2}) b_{i_2} g^{(\ell+1)}(y_{i_2}).
$$

Now, multiplying the above equations by  $b_{i_2}$  and  $a_{i_1}$  respectively, and recalling that  $M_{i_1,i_2}$  =  $a_{i_1}$ **k**( $x_{i_1}, y_{i_2}$ ) $b_{i_2}$ , we have

$$
\begin{bmatrix} b_1 g^{(\ell+1)}(y_1)^{-1} \\ \vdots \\ b_{n_2} g^{(\ell+1)}(y_{n_2})^{-1} \end{bmatrix} = M^{\top} \begin{bmatrix} f^{(\ell)}(x_1) \\ \vdots \\ f^{(\ell)}(x_{n_1}) \end{bmatrix}, \quad \begin{bmatrix} a_1 f^{(\ell+1)}(x_1)^{-1} \\ \vdots \\ a_{n_1} f^{(\ell+1)}(x_{n_1})^{-1} \end{bmatrix} = M \begin{bmatrix} g^{(\ell+1)}(y_1) \\ \vdots \\ g^{(\ell+1)}(y_{n_2}) \end{bmatrix},
$$

and hence

$$
\begin{bmatrix} g^{(\ell+1)}(y_1) \\ \vdots \\ g^{(\ell+1)}(y_{n_2}) \end{bmatrix} = \frac{b}{M^{\top}} \begin{bmatrix} f^{(\ell)}(x_1) \\ \vdots \\ f^{(\ell)}(x_{n_1}) \end{bmatrix}, \quad \begin{bmatrix} f^{(\ell+1)}(x_1) \\ \vdots \\ f^{(\ell+1)}(x_{n_1}) \end{bmatrix} = \frac{a}{M} \begin{bmatrix} g^{(\ell+1)}(y_1) \\ \vdots \\ g^{(\ell+1)}(y_{n_2}) \end{bmatrix}.
$$

Therefore, since  $f^{(0)} = (f^{(0)}(x_i))_{1 \leq i \leq n_1}$ , recalling Algorithm B.2, it follows by induction that, for every  $\ell \in \mathbb{N}$ ,  $f^{(\ell)} = (f^{(\ell)}(x_i))_{1 \leq i \leq n_1}$  and  $g^{(\ell)} = (g^{(\ell)}(x_i))_{1 \leq i \leq n_1}$ . Thus, the first part of the statement follows. The second part follows directly from the definitions of  $u^{(\ell)}$ ,  $v^{(\ell)}$ ,  $u^{(\ell)}$ , and  $v^{(\ell)}$ .  $\Box$ 

#### **Remark B.12.**

(i) Algorithm Algorithm B.2 is the classical (discrete) Sinkhorn algorithm which was recently studied in several papers [13]. It follows from Theorem [B.10](#page-14-2) that considering the solution  $(f, g)$ of the DAD problem such that  $f(x_1) = 1$  and defining  $\tilde{f}^{(\ell)} = f^{(\ell)}/f_0^{(\ell)}$  $S_0^{(\ell)}$  and  $\tilde{g}^{(\ell)} = g^{(\ell)} f_0^{(\ell)}$  $\mathfrak{g}_0^{(\ell)}$ , and  $f_i = f(x_i)$  and  $g_i = g(y_i)$ , we have

$$
\|\log \tilde{\mathsf{f}}^{(\ell)} - \log \mathsf{f} \|_\infty \leq \lambda^{2\ell} \bigg( \frac{\mathsf{D}}{\varepsilon} + \log \frac{\max_i \mathsf{f}_i^{(0)}}{\min_i \mathsf{f}_i^{(0)}} \bigg)
$$

<span id="page-16-2"></span>*.*

- (ii) The procedure SinkhornKnopp discussed in the paper and called in Algorithm 2, actually output the vector  $v = \varepsilon \log g^{(\ell)}$  for sufficiently large  $\ell$ .
- (iii) Referring to Section 4 in the paper, we recognize that the expressions on the right hand side of [\(B.43\)](#page-15-0) are precisely  $\mathsf{T}_{\alpha}(u^{(\ell)})(x)$  and  $\mathsf{T}_{\beta}(v^{(\ell+1)})(x)$  respectively.

<span id="page-16-0"></span>

## **C Lipschitz continuity of the gradient of Sinkhorn divergence with respect to the Total Variation**

In this section we show that the gradient of the Sinkhorn divergence is Lipschitz continuous with respect to the Total Variation on  $\mathcal{M}_1^+(\mathcal{X})$ .

We start by characterizing the relation between Hilbert's metric between functions of the form  $f = e^{u/\varepsilon}$  and the  $\left\| \cdot \right\|_{\infty}$  norm between functions of the form  $u = \varepsilon \log f$ .

**Lemma C.1.** *Let*  $f, f' \in C_{++}(X)$  *and set*  $u = \varepsilon \log f$  *and*  $u' = \varepsilon \log f'$ . *Then* 

$$
d_H(f, f') \le 2 ||\log f - \log f'||_{\infty}
$$
 or, equivalently  $d_H(e^{u/\varepsilon}, e^{u'/\varepsilon}) \le \frac{2}{\varepsilon} ||u - u'||_{\infty}$ . (C.1)

*Moreover, let*  $x_o \in \mathcal{X}$ *, consider the sets*  $\mathcal{A} = \{h \in \mathcal{C}_{++}(\mathcal{X}) \mid h(x_o) = 1\}$  and  $\mathcal{B} = \{w \in \mathcal{C}(\mathcal{X}) \mid w(x_o) = 1\}$ 0}*. Suppose that*  $f, f' \in \mathcal{A}$  *(or equivalently that*  $u, u' \in \mathcal{B}$ *). Then* 

<span id="page-16-1"></span>
$$
\frac{1}{2}d_H(f, f') \le ||\log f - \log f'||_{\infty} \le d_H(f, f'). \tag{C.2}
$$

<span id="page-16-3"></span>*and*

$$
\frac{\varepsilon}{2}d_H(e^{u/\varepsilon}, e^{u'/\varepsilon}) \le ||u - u'||_{\infty} \le \varepsilon \ d_H(e^{u/\varepsilon}, e^{u'/\varepsilon}). \tag{C.3}
$$

*Proof.* We have

$$
d_H(f, f') = 
\log \max_{x,y\in \mathcal{X}} \frac{f(x)f'(y)}{f(y)f'(x)}
$$

$$
= \log \max_{x\in \mathcal{X}} \frac{f(x)}{f'(x)} + \log \max_{y\in \mathcal{X}} \frac{f'(y)}{f(y)}
$$

$$
= \max_{x\in \mathcal{X}} \log \frac{f(x)}{f'(x)} + \max_{y\in \mathcal{X}} \log \frac{f'(y)}{f(y)}
$$

$$
\leq 2 \max_{x\in \mathcal{X}} \left| \log \frac{f(x)}{f'(x)} \right|
$$

$$
= 2 \left\| \log(f/f') \right\|_{\infty}
$$

$$
= 2 \left\| \log f - \log f' \right\|_{\infty}
$$

and [\(C.1\)](#page-16-2) follows. Suppose that  $f, f' \in \mathcal{A}$ . Then

$$
\|\log f - \log f'\|_{\infty} = \max \left\{ \log \max_{x \in \mathcal{X}} \frac{f(x)}{f'(x)}, \log \max_{x \in \mathcal{X}} \frac{f'(x)}{f(x)} \right\}
$$
  
$$
= \max \left\{ \log \max_{x \in \mathcal{X}} \frac{f(x)f'(x)}{f(\bar{x})f'(x)}, \log \max_{x \in \mathcal{X}} \frac{f(\bar{x})f'(x)}{f(x)f'(\bar{x})} \right\}
$$
  
$$
\leq \max \left\{ \log \max_{x,y \in \mathcal{X}} \frac{f(x)f'(y)}{f(y)f'(x)}, \log \max_{x,y \in \mathcal{X}} \frac{f(y)f'(x)}{f(x)f'(y)} \right\}
$$
  
$$
= d_H(f, f'),
$$

since  $f(x_o)/f'(o x) = f'(x_o)/f(x_o) = 1$ . Therefore, [\(C.2\)](#page-16-1) follows.

**Lemma C.2.** For every  $x, y \in \mathbb{R}_{++}$  we have

$$
|\log x - \log y| \le \max \left\{ x^{-1}, y^{-1} \right\} |x - y|.
$$
 (C.4)

The following result allows to extend the previous observations on a pair  $f, f'$  to the corresponding  $g = A_{\alpha} f$  and  $g' = A_{\alpha} f'$ .

<span id="page-17-0"></span>**Lemma C.3.** *Let*  $x_o \in \mathcal{X}$  *and*  $K \subset \mathcal{C}_+(\mathcal{X})$  *the cone from Lemma [B.8.](#page-12-4) Let*  $f, f' \in K$  *be such that*  $f(x_o) = f'(x_o) = 1$ , and set  $g = A_\alpha f$  and  $g' = A_\alpha f'$ . Then,

$$
\left\| \log g - \log g' \right\|_{\infty} \le e^{3D/\varepsilon} \left\| \log f - \log f' \right\|_{\infty}.
$$
 (C.5)

*Proof.* It follows from [\(B.20\)](#page-10-3) and Lemma [C.2](#page-8-5) that

$$
\left|\log g - \log g'\right| = \left|\log \frac{g}{g'}\right| = \left|\log \frac{\mathsf{L}_{\alpha}f'}{\mathsf{L}_{\alpha}f}\right| \le \max\left\{g',g\right\}\left|\mathsf{L}_{\alpha}f - \mathsf{L}_{\alpha}f'\right|.
$$

Therefore, since  $1 \leq ||f||_{\infty}$ ,  $||f'||_{\infty}$ , and recalling Lemma [B.8](#page-12-4) [\(v\)](#page-13-1) and [\(B.4\)](#page-7-1), we have

$$
\|\log g - \log g'\|_{\infty} \le \max\{\|g\|_{\infty}, \|g'\|_{\infty}\}\|L_{\alpha}f - L_{\alpha}f'\|_{\infty}
$$
  
$$
\le \max\{\|f\|_{\infty}\|g\|_{\infty}, \|f'\|_{\infty}\|g'\|_{\infty}\}\|L_{\alpha}f - L_{\alpha}f'\|_{\infty}
$$
  
$$
\le e^{2D/\varepsilon}\|f - f'\|_{\infty}
$$
  
$$
= e^{2D/\varepsilon}\|e^{\log f} - e^{\log f'}\|_{\infty}.
$$

Now, since  $f, f' \leq e^{D/\varepsilon}$ , we have  $\log f, \log f' \leq D/\varepsilon$ . Thus, the statement follows by noting that the exponential function is Lipschitz continuous on  $]-\infty, D/\varepsilon]$  with constant  $e^{D/\varepsilon}$ .  $\Box$ 

We are ready to prove the main result of the section.

**Theorem C.4** (Lipschitz continuity of the Sinkhorn potentials with respect to the total variation)**.** Let  $\alpha, \beta, \alpha', \beta' \in \mathcal{M}_1^+(\mathcal{X})$  and let  $x_o \in \mathcal{X}$ . Let  $(u, v), (u', v') \in \mathcal{C}(\mathcal{X})^2$  be the two pairs of Sinkhorn *potentials corresponding to the solution of the regularized OT problem in* [\(B.24\)](#page-10-0) *for*  $(\alpha, \beta)$  *and*  $(\alpha', \beta')$ *respectively such that*  $u(x_o) = u'(x_o) = 0$ . Then

$$
||u - u'||_{\infty} \le 2\varepsilon e^{3D/\varepsilon} ||(\alpha - \alpha', \beta - \beta')||_{TV}.
$$
 (C.6)

*Hence, the map which, for each pair of probability distributions*  $(\alpha, \beta) \in M_1^+(\mathcal{X})^2$  *associates the component u of the corresponding Sinkhorn potentials is* 2*εe*3D*/ε-Lipschitz continuous* with respect to the total variation*.*

*Proof.* The functions  $f = e^{u/\varepsilon}$  and  $f' = e^{u'/\varepsilon}$  are fixed points of the maps  $A_{\beta\alpha}$  and  $A_{\beta'\alpha'}$  respectively. Then, it follows from Theorem B.5 that

$$
d_H(f, f') = d_H(A_{\beta\alpha}(f), A_{\beta'\alpha'}(f'))
$$
  

$$
\leq d_H(A_{\beta\alpha}(f), A_{\beta'\alpha'}(f)) + d_H(A_{\beta'\alpha'}(f), A_{\beta'\alpha'}(f'))
$$
  

$$
\leq d_H(A_{\beta\alpha}(f), A_{\beta'\alpha'}(f)) + \lambda^2 d_H(f, f'),
$$

<span id="page-18-0"></span>hence,

<span id="page-18-1"></span>
$$
d_H(f, f') \le \frac{1}{1 - \lambda^2} d_H(\mathsf{A}_{\beta\alpha}(f), \mathsf{A}_{\beta'\alpha'}(f)).
$$
\n(C.7)

Moreover, using  $(C.1)$ , we have

$$
d_H(\mathsf{A}_{\beta\alpha}(f), \mathsf{A}_{\beta'\alpha'}(f)) \le d_H(\mathsf{A}_{\beta\alpha}(f), \mathsf{A}_{\beta'\alpha}(f)) + d_H(\mathsf{A}_{\beta'\alpha}(f), \mathsf{A}_{\beta'\alpha'}(f))
$$
  

$$
\le d_H(\mathsf{A}_{\beta}(g), \mathsf{A}_{\beta'}(g)) + \lambda d_H(\mathsf{A}_{\alpha}(f), \mathsf{A}_{\alpha'}(f))
$$
  

$$
\le 2 \left\| \log \frac{\mathsf{A}_{\beta}(g)}{\mathsf{A}_{\beta'}(g)} \right\|_{\infty} + 2\lambda \left\| \log \frac{\mathsf{A}_{\alpha}(f)}{\mathsf{A}_{\alpha'}(f)} \right\|_{\infty}. (C.8)
$$

Now, note that by Lemma [C.2](#page-8-5)

$$
\left| \log \left| \frac{A_{\beta}(g)}{A_{\beta'}(g)} \right| = \left| \log \left| \frac{L_{\beta'}g}{L_{\beta}g} \right| \le \max\{1/L_{\beta}g, 1/L_{\beta'}g\} |(L_{\beta'} - L_{\beta})g| \tag{C.9}
$$

and that, for every  $x \in \mathcal{X}$ ,

<span id="page-18-3"></span><span id="page-18-2"></span>
$$
[(\mathsf{L}_{\beta'} - \mathsf{L}_{\beta})g](x) = \int \mathsf{k}(x, z)g(z) \, d(\beta - \beta')(z)
$$
  
=  $\langle \mathsf{k}(x, \cdot)g, \beta - \beta' \rangle \le ||g||_{\infty} ||\beta - \beta'||_{TV},$  (C.10)

and, similarly,  $[(\mathsf{L}_{\beta} - \mathsf{L}_{\beta'})g](x) \leq ||g||_{\infty} ||\beta - \beta'||_{TV}$ . Therefore, since  $1/(\mathsf{L}_{\beta}g) = \mathsf{A}_{\beta}(g) = f$  and  $\mathsf{L}_{\beta'} g \geq e^{-\mathsf{D}/\varepsilon}$  min *g*, it follows from Lemma [B.8](#page-12-4) [\(v\)](#page-13-1) and [\(B.39\)](#page-13-2) (applied to *g*) that

$$
\left\| \log \left. \frac{A_{\beta}(g)}{A_{\beta'}(g)} \right\|_{\infty} \le \max \left\{ \|f\|_{\infty}, \frac{e^{D/\varepsilon}}{\min g} \right\} \|g\|_{\infty} \|\beta - \beta'\|_{TV} \le e^{2D/\varepsilon} \|\beta - \beta'\|_{TV} . \tag{C.11}
$$

Analogously, we have

<span id="page-19-0"></span>
$$
\left\| \log \left. \frac{\mathsf{A}_{\alpha}(f)}{\mathsf{A}_{\alpha'}(f)} \right\|_{\infty} \le e^{2\mathsf{D}/\varepsilon} \left\| \alpha - \alpha' \right\|_{TV} . \tag{C.12}
$$

Putting  $(C.7)$ ,  $(C.8)$ ,  $(C.11)$ , and  $(C.12)$  together, we have

$$
d_H(f, f') \le \frac{2e^{2\mathsf{D}/\varepsilon}}{1 - \lambda^2} \left(\lambda \left\|\alpha - \alpha'\right\|_{TV} + \left\|\beta - \beta'\right\|_{TV}\right). \tag{C.13}
$$

Now, note that since  $e^{\mathsf{D}/\varepsilon} \geq 1$ 

$$
\frac{1}{1 - \lambda^2} = \frac{(e^{\mathsf{D}/\varepsilon} + 1)^2}{4e^{\mathsf{D}/\varepsilon}} \le e^{\mathsf{D}/\varepsilon}.\tag{C.14}
$$

Finally, recalling  $(C.3)$ , we have

$$
||u - u'||_{\infty} \le 2\varepsilon e^{3D/\varepsilon} ||(\alpha - \alpha', \beta - \beta')||_{TV},
$$
\n(C.15)

where  $\|(\alpha - \alpha', \beta - \beta')\|_{TV} = \|\alpha - \alpha'\|_{TV} + \|\beta - \beta'\|_{TV}$  is the total variation norm on  $\mathcal{M}(\mathcal{X})^2$ .  $\Box$ 

<span id="page-19-4"></span>**Corollary C.5.** *Under the assumption of Theorem C.4, we have*

$$
\|u - u'\|_{\infty} + \|v - v'\|_{\infty} \le 2\varepsilon e^{3D/\varepsilon} (1 + \varepsilon e^{3D/\varepsilon}) \left\| (\alpha - \alpha', \beta - \beta') \right\|_{TV}.
$$
 (C.16)

*Proof.* It follows from Theorem C.4 and Lemma [C.3.](#page-17-0)

We finally address the issue of the differentiability of the Sinkhorn divergence. We first recall a few facts about the directional differentiability of OT*<sup>ε</sup>* briefly recalled in Section 2 of the main text. For a more in-depth analysis on this topic we refer the reader to [21] (in particular Proposition 2).

<span id="page-19-2"></span>**Fact C.6.** Let  $x_o \in \mathcal{X}$ ,  $\alpha, \beta \in \mathcal{M}_1^+(\mathcal{X})$  and  $(u, v) \in \mathcal{C}(\mathcal{X})^2$  be the pair of corresponding Sinkhorn *potentials with*  $u(x_o) = 0$ . The function  $\overline{OT}_{\varepsilon}$  is directionally differentiable and the directional derivative  $of \text{ OT}_{\varepsilon}$  *in*  $(\alpha, \beta)$  *along a feasible direction*  $(\mu, \nu) \in \mathcal{F}_{\mathcal{M}_1^+(\mathcal{X})^2}((\alpha, \beta))$  (see Definition [A.2\)](#page-1-2) is

$$
\mathrm{OT}'_{\varepsilon}(\alpha,\beta;\mu,\nu) = \int u(x) \ d\mu(x) + \int v(y) \ d\nu(y) = \langle (u,v), (\mu,\nu) \rangle. \tag{C.17}
$$

Let  $\nabla \overline{\mathrm{OT}}_{\varepsilon}$ :  $\mathcal{M}_1^+(\mathcal{X})^2 \to \mathcal{C}(\mathcal{X})^2$  be the operator that maps every pair of probability distributions  $(\alpha, \beta) \in \mathcal{M}_1^+(\mathcal{X})^2$  to the corresponding pair of Sinkhorn potentials  $(u, v) \in \mathcal{C}(\mathcal{X})^2$  with  $u(x_o) = 0$ . *Then* [\(C.17\)](#page-19-1) *can be written as*

$$
\mathrm{OT}_{\varepsilon}'(\alpha, \beta; \mu, \nu) = \langle \nabla \mathrm{OT}_{\varepsilon}(\alpha, \beta), (\mu, \nu) \rangle. \tag{C.18}
$$

<span id="page-19-3"></span>**Remark C.7.** In Fact [C.6,](#page-19-2) the requirement  $u(x<sub>o</sub>) = 0$  is only a convention to remove ambiguities. Indeed, for every  $t \in \mathbb{R}$ , replacing the Sinkhorn potential  $(u + t, u - t)$  in Definition [A.1](#page-1-3) does not affect  $(C.17)$ .

<span id="page-19-1"></span> $\Box$ 

Fact C.8. Let  $\beta \in M^1_+(\mathcal{X})$  and let  $\nabla_1 \text{OT}_{\varepsilon}$  be the first component of the gradient operator defined *in Fact [C.6.](#page-19-2)* Then the Sinkhorn divergence function  $S_{\varepsilon}(\cdot,\beta)$ :  $\mathcal{M}_+^1(\mathcal{X}) \to \mathbb{R}$  in (7) is directionally  $differential$  *and, for every*  $\alpha \in M^1_+(\mathcal{X})$  *and every*  $\mu \in \mathcal{F}_{M^1_+(\mathcal{X})}(\alpha)$ *,* 

$$
[S_{\varepsilon}(\cdot,\beta)]'(\alpha;\mu)=\langle \nabla_1\mathrm{OT}_{\varepsilon}(\alpha,\beta)-\nabla_1\mathrm{OT}_{\varepsilon}(\alpha,\alpha),\mu\rangle.
$$

 $So, one can define \nabla S_{\varepsilon}(\cdot,\beta) \colon \mathcal{M}_1^+(\mathcal{X}) \to \mathcal{C}(\mathcal{X})$  such that, for every  $\alpha \in \mathcal{M}_+^1(\mathcal{X}), \nabla [S_{\varepsilon}(\cdot,\beta)](\alpha) =$  $\nabla_1 \overline{\mathrm{OT}}_{\varepsilon}(\alpha, \beta) - \nabla_1 \overline{\mathrm{OT}}_{\varepsilon}(\alpha, \alpha)$  *and we have* 

$$
[S_{\varepsilon}(\cdot,\beta)]'(\alpha;\mu) = \langle \nabla S_{\varepsilon}(\cdot,\beta),\mu \rangle. \tag{C.19}
$$

*Finally, if* k *in* [\(B.1\)](#page-6-3) *is a positive definite kernel, then the Sinkhorn divergence*  $S_{\varepsilon}(\cdot, \beta)$  *is convex.* 

We are now ready to prove Theorem 4 in the paper. We recall also the statement for reader's convenience.

**Theorem 4.** *The gradient* ∇OT*<sup>ε</sup> defined in Proposition 1 is Lipschitz continuous. In particular, the first component*  $\nabla_1 \text{OT}_{\varepsilon}$  *is*  $2\varepsilon e^{3\text{D}/\varepsilon}$ -*Lipschitz continuous, i.e., for every*  $\alpha, \alpha', \beta, \beta' \in \mathcal{M}_1^+(\mathcal{X})$ *,* 

$$
\|u - u'\|_{\infty} = \|\nabla_1 \text{OT}_{\varepsilon}(\alpha, \beta) - \nabla_1 \text{OT}_{\varepsilon}(\alpha', \beta')\|_{\infty} \le 2\varepsilon e^{3\mathsf{D}/\varepsilon} \left( \|\alpha - \alpha'\|_{TV} + \|\beta - \beta'\|_{TV} \right),\tag{11}
$$

where  $D = \sup_{x,y \in \mathcal{X}} c(x,y)$ ,  $u = \mathsf{T}_{\beta\alpha}(u)$ ,  $u' = \mathsf{T}_{\beta',\alpha'}(u')$ , and  $u(x_o) = u'(x_o) = 0$ . Moreover, it follows *from* (8) *that*  $\nabla S_{\varepsilon}(\cdot, \beta)$  *is* 6*εe*<sup>3D/*ε*</sup>*-Lipschitz continuous. The same holds for*  $\nabla B_{\varepsilon}$ *.* 

*Proof.* The first part is just a consequence of Theorem C.4 and Fact [C.6.](#page-19-2) The second part, follows from the first part and Fact [C.8.](#page-19-3)  $\Box$ 

**Remark C.9.** It follows from the optimality conditions [\(B.25\)](#page-10-4) that, for every  $x \in \text{supp}(\alpha)$  and  $y \in \text{supp}(\beta)$ ,

$$
1 = \int_{\mathcal{X}} e^{\frac{u(x) + v(y) - c(x, y)}{\varepsilon}} d\beta(y) \quad \text{and} \quad 1 = \int_{\mathcal{X}} e^{\frac{u(x) + v(y) - c(x, y)}{\varepsilon}} d\alpha(x),
$$

hence,

$$
\int_{\mathcal{X}} e^{\frac{u \oplus v - \epsilon}{\varepsilon}} d\alpha \otimes \beta = 1.
$$
\n(C.20)

Then, recalling the definition of  $\overline{\text{OT}}_{\varepsilon}$  in (2) and that of its gradient, given above, we have

$$
\mathrm{OT}_{\varepsilon}(\alpha, \beta) = \langle \nabla \mathrm{OT}_{\varepsilon}(\alpha, \beta), (\alpha, \beta) \rangle - \varepsilon. \tag{C.21}
$$

Since,  $\nabla \overline{\mathrm{OT}}_{\varepsilon}$  is bounded and Lipschitz continuous, it follows that  $\overline{\mathrm{OT}}_{\varepsilon}$  is Lipschitz continuous with respect to the total variation.

We end the section by providing an independent proof of Fact  $C.6$ , which is based on Proposition [A.8](#page-4-2) and Corollary [C.5.](#page-19-4)

**Proposition C.10.** The function  $\text{OT}_{\varepsilon} \colon \mathcal{M}^1_+(\mathcal{X})^2 \to \mathbb{R}$ , defined in (2), is continuous with respect to *the total variation, directionally differentiable, and, for every*  $(\alpha, \beta) \in M^1_+(\mathcal{X})^2$  *and every feasible*  $direction (\mu, \nu) \in \mathcal{F}_{\mathcal{M}_{+}^{1}(\mathcal{X})^{2}}(\alpha, \beta),$  we have

$$
OT'_{\varepsilon}(\alpha, \beta; \mu, \nu) = \langle (u, v), (\mu, \nu) \rangle, \qquad (C.22)
$$

*where*  $(u, v) \in C(X)^2$  *is any solution of problem* (2)*.* 

*Proof.* Let  $g: \mathcal{C}(\mathcal{X})^2 \times \mathcal{M}(\mathcal{X})^2 \to \mathbb{R}$  be such that,

$$
g((u, v), (\alpha, \beta)) = \langle u, \alpha \rangle + \langle v, \beta \rangle - \varepsilon \langle \exp((u \oplus v - \mathsf{c})/\varepsilon), \alpha \otimes \beta \rangle. \tag{C.23}
$$

Then, for every  $(\alpha, \beta) \in \mathcal{M}^1_+(\mathcal{X})^2$ ,

<span id="page-21-1"></span><span id="page-21-0"></span>
$$
\mathrm{OT}_{\varepsilon}(\alpha, \beta) = \max_{(u,v) \in \mathcal{C}(\mathcal{X})^2} g((u,v), (\alpha, \beta)).
$$
\n(C.24)

Thus,  $\overline{or}_{\varepsilon}$  is of the type considered in Proposition [A.8.](#page-4-2) Let  $(u, v) \in C(X)$ . Then the function  $g((u, v), \cdot)$  admits directional derivatives and, for every  $(\alpha, \beta), (\mu, \nu) \in \mathcal{M}(\mathcal{X})^2$ , we have

$$
[g((u,v),\cdot)]'((\alpha,\beta);(\mu,\nu))
$$

$$
= \left\langle u - \varepsilon e^{\frac{u}{\varepsilon}} \int_{\mathcal{X}} e^{\frac{v - c(x, y)}{\varepsilon}} d\beta(y), \mu \right\rangle + \left\langle v - \varepsilon e^{\frac{v}{\varepsilon}} \int_{\mathcal{X}} e^{\frac{u - c(x, y)}{\varepsilon}} d\alpha(x), \nu \right\rangle. \tag{C.25}
$$

Indeed, for every  $t > 0$ ,

$$
\frac{1}{t} [g((u, v), (\alpha, \beta) + t(\mu, \nu)) - g((u, v), (\alpha, \beta))]
$$

$$
= \frac{1}{t} [\langle u, \alpha + t\mu \rangle + \langle v, \beta + t\nu \rangle - \varepsilon \langle \exp((u \oplus v - c)/\varepsilon), (\alpha + t\mu) \otimes (\beta + t\nu) \rangle - \langle u, \alpha \rangle - \langle v, \beta \rangle + \varepsilon \langle \exp((u \oplus v - c)/\varepsilon), \alpha \otimes \beta \rangle]
$$

$$
= \langle u, \mu \rangle + \langle v, \nu \rangle - \varepsilon \langle \exp((u \oplus v - c)/\varepsilon), \alpha \otimes \nu \rangle - \varepsilon \langle \exp((u \oplus v - c)/\varepsilon), \mu \otimes \beta \rangle - t\varepsilon \langle \exp((u \oplus v - c)/\varepsilon), \mu \otimes \nu \rangle,
$$

hence

$$
[g((u, v), \cdot)]'((\alpha, \beta); (\mu, \nu))
$$
  
=  $\langle u, \mu \rangle + \langle v, \nu \rangle - \varepsilon \langle \exp((u \oplus v - c)/\varepsilon), \alpha \otimes \nu \rangle - \varepsilon \langle \exp((u \oplus v - c)/\varepsilon), \mu \otimes \beta \rangle$ 

and [\(C.25\)](#page-21-0) follows. Thus, the function *g* is Gâteaux differentiable with respect to the second variable, with derivative

$$
D_2 g((u, v), (\alpha, \beta)) = \left(u - \varepsilon e^{\frac{u}{\varepsilon}} \int_{\mathcal{X}} e^{\frac{v - c(\cdot, y)}{\varepsilon}} d\beta(y), v - \varepsilon e^{\frac{v}{\varepsilon}} \int_{\mathcal{X}} e^{\frac{u - c(x, \cdot)}{\varepsilon}} d\alpha(x)\right)
$$
  
=  $(u, v) - \varepsilon (e^{\frac{u}{\varepsilon}} \mathsf{L}_{\beta} e^{\frac{v}{\varepsilon}}, e^{\frac{v}{\varepsilon}} \mathsf{L}_{\alpha} e^{\frac{u}{\varepsilon}}) \in C(\mathcal{X})^2$ ,

which is jointly continuous, since the maps  $(u, \alpha) \mapsto L_{\alpha}e^{u/\varepsilon}$  and  $(v, \beta) \mapsto L_{\beta}e^{v/\varepsilon}$  are continuous. Moreover, it follows from Corollary [C.5](#page-19-4) that there exists a continuous selection of Sinkhorn potentials. Therefore, it follows from Proposition [A.8](#page-4-2) that  $\mathrm{OT}_\varepsilon$  is directionally differentiable and

$$
OT'_{\varepsilon}((\alpha,\beta);(\mu,\nu)) = \max_{(u,v) \text{ solution of (C.24)}} \langle D_2g((u,v),(\alpha,\beta)),(\mu,\nu) \rangle. \tag{C.26}
$$

However, if  $(u, v)$  is a solution of  $(C.24)$ , it follows from the optimality conditions  $(B.25)$  that

$$
e^{\frac{u}{\varepsilon}} \int_{\mathcal{X}} e^{\frac{v - c(\cdot, y)}{\varepsilon}} d\beta(y) = 1 \quad \text{and} \quad e^{\frac{v}{\varepsilon}} \int_{\mathcal{X}} e^{\frac{u - c(x, \cdot)}{\varepsilon}} d\alpha(x) = 1, \tag{C.27}
$$

hence

$$
\langle D_2g((u,v),(\alpha,\beta)),(\mu,\nu)\rangle = \langle (u-\varepsilon,v-\varepsilon),(\mu,\nu)\rangle = \langle (u,v),(\mu,\nu)\rangle, \tag{C.28}
$$

where we used the fact that, since  $(\mu, \nu) = t(\mu_1 - \mu_2, \nu_1 - \nu_2)$  for some  $t > 0$  and  $\mu_1, \mu_2, \nu_1, \nu_2 \in \mathcal{M}_+^1(\mathcal{X}),$ we have  $\langle 1, \mu \rangle = t \langle 1, \mu_1 - \mu_2 \rangle = 0$  and  $\langle 1, \nu \rangle = t \langle 1, \nu_1 - \nu_2 \rangle = 0.$ 

<span id="page-22-0"></span>

## **D The Frank-Wolfe algorithm for Sinkhorn barycenters**

In this section we finally analyze the Frank-Wolfe algorithm for the Sinkhorn barycenters and give convergence results. The following result is a direct consequence of Theorem [B.10](#page-14-2) and Fact [C.6.](#page-19-2)

<span id="page-22-1"></span>**Theorem D.1.** *Let*  $(\tilde{u}^{(\ell)})_{\ell \in \mathbb{N}}$  *be generated according to Algorithm B.1. Then,* 

$$
(\forall \ell \in \mathbb{N}) \quad \|\tilde{u}^{(\ell)} - \nabla_1 \mathrm{OT}_{\varepsilon}(\alpha, \beta)\|_{\infty} \le \lambda^{2\ell} \bigg(\frac{\mathsf{D} + \max_{\mathcal{X}} u^{(0)} - \min_{\mathcal{X}} u^{(0)}}{\varepsilon}\bigg),\tag{D.1}
$$

*where*  $u^{(\ell)} = \varepsilon \log f^{(\ell)}$  *and*  $\tilde{u}^{(\ell)} = u^{(\ell)} - u^{(\ell)}(x_o)$ .

<span id="page-22-4"></span>Therefore, in view of Fact [C.8,](#page-19-3) Theorem [D.1,](#page-22-1) and Proposition [A.7,](#page-3-0) we can address the problem of the Sinkhorn barycenter (9) via the Frank-Wolfe Algorithm A.1. Note that, according to Proposi-tion [A.7](#page-3-0)[\(ii\),](#page-3-5) since the diameter of  $\mathcal{M}_1^+(\mathcal{X})$  with respect to  $\left\|\cdot\right\|_{TV}$  is 2, have that the curvature of  $B_\varepsilon$  is upper bounded by

<span id="page-22-2"></span>
$$
C_{\mathsf{B}_{\varepsilon}} \le 24\varepsilon e^{3\mathsf{D}/\varepsilon}.\tag{D.2}
$$

Let  $k \in \mathbb{N}$  and  $\alpha_k$  be the current iteration. For every  $j \in \{1, \ldots, m\}$ , we can compute  $\nabla_1 O T_{\varepsilon}(\alpha_k, \beta_j)$ and  $\nabla_1 \text{OT}_{\varepsilon}(\alpha_k, \alpha_k)$  by the Sinkhorn-Knopp algorithm. Thus, by [\(D.1\)](#page-22-2), we find  $\ell \in \mathbb{N}$  large enough so that  $\|\tilde{u}_j^{(\ell)} - \nabla_1 \text{OT}_{\varepsilon}(\alpha_k, \beta_q)\|_{\infty} \leq \Delta_{1,k}/8$  and  $\|\tilde{p}^{(\ell)} - \nabla_1 \text{OT}_{\varepsilon}(\alpha_k, \alpha_k)\|_{\infty} \leq \Delta_{1,k}/8$  and we set

$$
\tilde{u}^{(\ell)} := \sum_{j=1}^{m} \omega_j \tilde{u}_j^{(\ell)} - \tilde{p}^{(\ell)}.
$$
\n(D.3)

Then,

<span id="page-22-3"></span>
$$
\|\tilde{u}^{(\ell)} - \nabla \mathsf{B}_{\varepsilon}(\alpha_k)\|_{\infty} \le \frac{\Delta_{1,k}}{4}.\tag{D.4}
$$

Now, Frank-Wolf Algorithm A.1 (in the version considered in Proposition [A.7](#page-3-0)[\(i\)\)](#page-3-2) requires finding

$$
\eta_{k+1} \in \underset{\eta \in \mathcal{M}_1^+(\mathcal{X})}{\text{argmin}} \left\langle \tilde{u}^{(\ell)}, \eta - \alpha_k \right\rangle \tag{D.5}
$$

and make the update

$$
\alpha_{k+1} = (1 - \gamma_k)\alpha_k + \gamma_k \eta_{k+1}.
$$
\n(D.6)

Since the solution of  $(D.5)$  is a Dirac measure (see Section 4 in the paper), the algorithm reduces to

$$
\begin{cases} \text{find } x_{k+1} \in \mathcal{X} \text{ such that } \tilde{u}^{(\ell)}(x_{k+1}) \le \min_{x \in \mathcal{X}} \tilde{u}^{(\ell)}(x) + \frac{\Delta_{2,k}}{2} \\ \alpha_{k+1} = (1 - \gamma_k)\alpha_k + \gamma_k \delta_{x_{k+1}}. \end{cases} \tag{D.7}
$$

So, if we initialize the algorithm with  $\alpha_0 = \delta_{x_0}$ , then any  $\alpha_k$  will be a discrete probability measure with support contained in  $\{x_0, \ldots, x_k\}$ . This implies that if all the  $\beta_j$ 's are probability measures with finite support, the computation of  $\nabla_1 O T_{\varepsilon}(\alpha_k, \beta_i)$  by the Sinkhorn algorithm can be reduced to a fully discrete algorithm, as showed in Proposition [B.11.](#page-15-1) More precisely, assume that

$$
(\forall j = 1, ..., m) \quad \beta_j = \sum_{i_2=0}^n b_{j, i_2} \delta_{y_{j, i_2}}.
$$
 (D.8)

and that at iteration *k* we have

$$
\alpha_k = \sum_{i_1=0}^k a_{k,i_1} \delta_{x_{i_1}}.
$$
\n(D.9)

Set

\mathbf{a}\_{k} = \begin{bmatrix} a\_{k,0} \\ \vdots \\ a\_{k,k} \end{bmatrix} \in \mathbb{R}^{k+1}, \quad \mathsf{M}\_{0} = \begin{bmatrix} a\_{k,0}\mathsf{k}(x\_{0},x\_{0})a\_{k,0} & \cdots & a\_{k,0}\mathsf{k}(x\_{0},x\_{k})a\_{k,k} \\ \vdots & \ddots & \vdots \\ a\_{k,k}\mathsf{k}(x\_{k},x\_{0})a\_{k,0} & \cdots & a\_{k,k}\mathsf{k}(x\_{k},x\_{k})a\_{k,k} \end{bmatrix} \in \mathbb{R}^{(k+1)\times (k+1)} \tag{D.10}

and, for every  $j = 1, \ldots, m$ ,

$$
\mathbf{b}_{j} = \begin{bmatrix} b_{j,0} \\ \vdots \\ b_{j,n} \end{bmatrix} \in \mathbb{R}^{n+1}, \quad \mathbf{M}_{j} = \begin{bmatrix} a_{k,0} \mathsf{k}(x_{0}, y_{j,0}) b_{j,0} & \dots & a_{k,0} \mathsf{k}(x_{0}, y_{j,n}) b_{j,n} \\ \vdots & \ddots & \vdots \\ a_{k,k} \mathsf{k}(x_{k}, y_{j,0}) b_{j,0} & \dots & a_{k,n} \mathsf{k}(x_{k}, y_{j,n}) b_{j,n} \end{bmatrix} \in \mathbb{R}^{(k+1)\times(n+1)}. \quad \text{(D.11)}
$$

Then, run Algorithm B.2, with input  $a_k$ ,  $a_k$ , and  $M_0$  to get  $(e^{(\ell)}, h^{(\ell)})$ , and, for every  $j = 1, \ldots, m$ , with input  $a_k$ ,  $b_j$ , and  $M_j$  to get  $(f_j^{(\ell)})$  $g_j^{(\ell)}, \mathsf{g}_j^{(\ell)}$  $j^{(\ell)}$ ). So, we have,

(\forall \ell \in \mathbb{N}) \quad \begin{cases} \\ h^{(\ell+1)} = \frac{a\_k}{M\_0^{\top} e^{(\ell)}}, \\ e^{(\ell+1)} = \frac{a\_k}{M\_0 h^{(\ell+1)}} \\ (\forall j = 1, \dots, m) \quad \mathbf{g}\_j^{(\ell+1)} = \frac{b\_j}{M\_j^{\top} f\_j^{(\ell)}}, \\ f\_j^{(\ell+1)} = \frac{a\_k}{M\_j \mathbf{g}\_j^{(\ell+1)}}. \\ \end{cases} \tag{D.12}

Then, according to Proposition [B.11,](#page-15-1) for every  $\ell \in \mathbb{N}$ , we have

<span id="page-23-1"></span>
$$
(\forall x \in \mathcal{X}) \begin{cases} e^{(\ell)}(x)^{-1} = \sum_{i_2=0}^k k(x, x_{i_2}) h_{i_2}^{(\ell-1)} a_{k, i_2}, \ p^{(\ell)}(x) = \varepsilon \log e^{(\ell)}(x) = -\varepsilon \log \sum_{i_2=0}^k k(x, x_{i_2}) h_{i_2}^{(\ell-1)} a_{k, i_2} \ \tilde{p}^{(\ell)}(x) = p^{(\ell)}(x) - p^{(\ell)}(x_o). \end{cases} \hfill (D.13)
$$

and, for every  $j = 1, \ldots, m$ ,

<span id="page-23-2"></span>(\$\forall x \in \mathcal{X}\$)

$$
\begin{cases}
 f_{j}^{(\ell)}(x)^{-1} = \sum_{i_2=0}^{n} k(x, y_{i_2}) g_{j, i_2}^{(\ell-1)} b_{j, i_2}, \\
 u_{j}^{(\ell)}(x) = \varepsilon \log f_{j}^{(\ell)}(x) = -\varepsilon \log \sum_{i_2=0}^{n} k(x, y_{i_2}) g_{j, i_2}^{(\ell-1)} b_{j, i_2} \\
 \tilde{u}_{j}^{(\ell)}(x) = u_{j}^{(\ell)}(x) - u_{j}^{(\ell)}(x_o).
\end{cases}
$$
(D.14)

Since the  $\tilde{u}^{(\ell)}_i$  $y_j^{(\ell)}$ 's and  $u_j^{(\ell)}$  $j^{(\ell)}$ 's, and  $\tilde{p}^{(\ell)}$  and  $p^{(\ell)}$ , differ for a constant only, the final algorithm can be written as in Algorithm  $\overline{D.1}$ . We stress that this algorithm is even more general than Algorithm 2 since, in the computation of the Sinkhorn potentials and in their minimization, errors have been taken into account.

<span id="page-23-0"></span>We now give a final converge theorem, of which Theorem 5 in the paper is a special case.

#### **Algorithm D.1** Frank-Wolfe algorithm for Sinkhorn barycenter

Let  $\alpha_0 = \delta_{x_0}$  for some  $x_0 \in \mathcal{X}$ . Let  $(\Delta_k)_{k \in \mathbb{N}} \in \mathbb{R}^{\mathbb{N}}_+$  be such that  $\Delta_k/\gamma_k$  is nondecreasing. Define

for  $k = 0, 1, ...$  run Algorithm B.2 with input  $a_k$ ,  $a_k$ ,  $M_0$  till  $\lambda^{2\ell}D/\varepsilon \leq \frac{\Delta_{1,k}}{8} \to h \in \mathbb{R}^{k+1}$ compute *p* via [\(D.13\)](#page-23-1) with h for  $j = 1, \ldots m$  $\begin{array}{|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c|c$ run Algorithm B.2 with input  $a_k$ ,  $b_j$ ,  $M_j$  till  $\lambda^{2\ell}D/\varepsilon \leq \frac{\Delta_{1,k}}{8} \to g_j \in \mathbb{R}^{n+1}$ compute  $u_j$  via [\(D.14\)](#page-23-2) with  $g_j$ set  $u = \sum_{j=1}^{m} \omega_j u_j - p$ find  $x_{k+1} \in \mathcal{X}$  such that  $u(x_{k+1}) \le \min_{x \in \mathcal{X}} u(x) + \frac{\Delta_{2,k}}{2}$  $\alpha_{k+1} = (1 - \gamma_k)\alpha_k + \gamma_k \delta_{x_{k+1}}.$ 

**Theorem D.2.** *Suppose that*  $\beta_1, \ldots, \beta_m \in \mathcal{M}^1_+(\mathcal{X})$  *are probability measures with finite support, each of cardinality*  $n \in \mathbb{N}$ *. Let*  $(\alpha_k)_{k \in \mathbb{N}}$  *be generated by Algorithm D.1. Then, for every*  $k \in \mathbb{N}$ *,* 

$$
\mathsf{B}_{\varepsilon}(\alpha_k) - \min_{\alpha \in \mathcal{M}_+^1(\mathcal{X})} \mathsf{B}_{\varepsilon}(\alpha) \le \gamma_k 24\varepsilon e^{3\mathsf{D}/\varepsilon} + 2\Delta_{1,k} + \Delta_{2,k} \tag{D.15}
$$

*Proof.* It follows from Theorem A.5, Proposition [A.7,](#page-3-0) and [\(D.2\)](#page-22-4), recalling that  $\text{diam}(\mathcal{M}^1_+(\mathcal{X})) = 2$ .

<span id="page-24-0"></span>

## **E Sample complexity of Sinkhorn potential**

In the following we will denote by  $\mathcal{C}^s(\mathcal{X})$  the space of *s*-differentiable functions with continuous derivatives and by  $W^{s,p}(\mathcal{X})$  the Sobolev space of functions  $f: \mathcal{X} \to \mathbb{R}$  with *p*-summable weak derivatives up to order *s* [1]. We denote by  $\left\| \cdot \right\|_{s,p}$  the corresponding norm.

The following result shows that under suitable smoothness assumptions on the cost function c, the Sinkhorn potentials are uniformly bounded as functions in a suitable Sobolev space of corresponding smoothness. This fact will play a key role in approximating the Sinkhorn potentials of general distributions in practice.

**Theorem E.1** (Proposition 2 in [23]). Let X be a closed bounded domain with Lipschitz boundary in  $\mathbb{R}^d$  ([1, Definition 4.9]) and let  $c \in C^{s+1}(\mathcal{X} \times \mathcal{X})$ . Then for every  $(\alpha, \beta) \in \mathcal{M}_1^+(\mathcal{X})^2$ , the associated *Sinkhorn potentials*  $(u, v) \in C(X)^2$  *are functions in*  $W^{s, \infty}(\mathcal{X})$ *. Moreover, let*  $x_o \in \mathcal{X}$ *. Then there exists a constant*  $r > 0$ , depending only on  $\varepsilon$ , s and X, such that for every  $(\alpha, \beta) \in M_1^+(\mathcal{X})^2$  the *associated Sinkhorn potentials*  $(u, v) \in C(X)^2$  *with*  $u(x_o) = 0$  *satisfies*  $||u||_{s, \infty}, ||v||_{s, \infty} \le r$ .

In the original statement of [23, Proposition 2] the above result is formulated for  $c \in C^{\infty}(\mathcal{X})$  for simplicity. However, as clarified by the authors, it holds also for the more general case  $c \in C^{s+1}(\mathcal{X})$ .

**Lemma E.2.** Let  $\mathcal{X} \subset \mathbb{R}^d$  be a closed bounded domain with Lipschitz boundary and let  $u, u' \in$  $W^{s,\infty}(\mathcal{X})$ *. Then the following holds* 

- <span id="page-25-0"></span> $(ii)$   $\|uu'\|_{s,\infty} \leq m_1 \|u\|_{s,\infty} \|u'\|_{s,\infty},$
- <span id="page-25-1"></span>(ii)  $||e^u||_{s,\infty} \le ||e^u||_{\infty} (1 + m_2 ||u||_{s,\infty}),$

*where*  $m_1 = m_1(s, d)$  *and*  $m_2 = m_2(s, d) > 0$  *depend only on the dimension d and the order of*  $differentiality$  *s but not on u and u'*.

*Proof.* [\(i\)](#page-25-0) follows directly from Leibniz formula. To see [\(ii\),](#page-25-1) let  $\mathbf{i} = (i_1, \ldots, i_d) \in \mathbb{N}^d$  be a multi-index with  $|\mathbf{i}| = \sum_{\ell=1}^d i_\ell \leq s$  and note that by chain rule the derivatives of  $e^u$ 

$$
D^{\mathbf{i}} e^u = e^u P_{\mathbf{i}} \left( (D^{\mathbf{j}} u)_{\mathbf{j} \le \mathbf{i}} \right),
$$

where  $P_i$  is a polynomial of degree  $|i|$  and  $j \leq i$  is the ordering associated to the cone of non-negative vectors in  $\mathbb{R}^d$ . Note that  $P_0 = 1$ , while for  $|\mathbf{i}| > 0$ , the associated polyomial  $P_{\mathbf{i}}$  has a root in zero (i.e. it does not have constant term). Hence

$$
||e^u||_{s,\infty} \le ||e^u||_{\infty} \left(1+|P|\left((\left\|D^{\mathbf{i}}u\right\|_{\infty})_{|\mathbf{i}|\leq s}\right)\right),
$$

where we have denoted by  $P = \sum_{0 \leq |\mathbf{i}| \leq s} P_{\mathbf{i}}$  and by  $|P|$  the polynomial with coefficients corresponding to the absolute value of the coefficients of *P*. Therefore, since  $||D^i u||_{\infty} \le ||u||_{s,\infty}$  for any  $|i| \le s$ , by taking

$$
\mathsf{m}_2 = |P| \Big( (1)_{|\mathbf{i}| \le s} \Big),
$$

namely the sum of all the coefficients of  $|P|$ , we obtain the desired result. Indeed note that the coefficients of *P* do not depend on *u* but only on the smoothness *s* and dimension *d*.  $\Box$ 

**Lemma E.3.** Let  $X \subset \mathbb{R}^d$  be a closed bounded domain with Lipschitz boundary and let  $x_o \in X$ . Let  $c \in C^{s+1}(\mathcal{X} \times \mathcal{X})$ , for some  $s \in \mathbb{N}$ . Then for any  $\alpha, \beta \in \mathcal{M}_1^+(\mathcal{X})$  and corresponding pair of Sinkhorn potentials  $(u, v) \in C(\mathcal{X})^2$  with  $u(x_o) = 0$ , the functions  $\mathsf{k}(x, \cdot)e^{u/\varepsilon}$  and  $\mathsf{k}(x, \cdot)e^{v/\varepsilon}$  belong to  $W^{s,2}(\mathcal{X})$ *for every*  $x \in \mathcal{X}$ *. Moreover, they admit an extension to*  $\mathcal{H} = W^{s,2}(\mathbb{R}^d)$  *and there exists a constant*  $\bar{\mathbf{r}}$ *independent on*  $\alpha$  *and*  $\beta$ *, such that for every*  $x \in \mathcal{X}$ 

$$
\left\| \mathbf{k}(x,\cdot)e^{u/\varepsilon} \right\|_{\mathcal{H}}, \, \left\| \mathbf{k}(x,\cdot)e^{v/\varepsilon} \right\|_{\mathcal{H}} \le \bar{\mathsf{r}} \tag{E.1}
$$

*(with some abuse of notation, we have identified*  $\mathsf{k}(x, \cdot)e^{u/\varepsilon}$  *and*  $\mathsf{k}(x, \cdot)e^{v/\varepsilon}$  *with their extensions to*  $\mathbb{R}^d$  ).

*Proof.* In the following we denote by  $\|\cdot\|_{s,2} = \|\cdot\|_{s,2,\mathcal{X}}$  the norm of  $W^{s,2}(\mathcal{X})$  and by  $\|\cdot\|_{\mathcal{H}} = \|\cdot\|_{s,2,\mathbb{R}^d}$ the norm of  $\mathcal{H} = W^{s,2}(\mathbb{R})$ . Let  $x \in \mathcal{X}$ . Then, since  $u - c(x, \cdot) \in W^{s,\infty}(\mathcal{X})$  and  $||u||_{s,\infty} \leq r$ , it follows from Lemma [E.2](#page-8-5) that

$$
\left\| \mathbf{k}(x,\cdot)e^{u/\varepsilon} \right\|_{s,\infty} &= \left\| e^{(u-\mathbf{c}(x,\cdot))/\varepsilon} \right\|_{s,\infty} \\ &\leq \left\| e^{(u-\mathbf{c}(x,\cdot))/\varepsilon} \right\|_{\infty} (1+\mathbf{m}_2 \left\| u - \mathbf{c}(x,\cdot) \right\|_{s,\infty}) \\ &= \left\| \mathbf{k}(x,\cdot)e^{u/\varepsilon} \right\|_{\infty} (1+\mathbf{m}_2 \left\| u - \mathbf{c}(x,\cdot) \right\|_{s,\infty}) \\ &\leq \left\| e^{u/\varepsilon} \right\|_{\infty} (1+\mathbf{m}_2(\mathsf{r}+\left\| \mathbf{c} \right\|_{s,\infty})) \\ &\leq e^{D/\varepsilon} (1+\mathbf{m}_2(\mathsf{r}+\left\| \mathbf{c} \right\|_{s,\infty})), \end{mtable}
$$

where we used the fact that  $D^{\mathbf{i}}[\mathbf{c}(x, \cdot)] = (D^{\mathbf{i}}\mathbf{c})(x, \cdot)$ . This implies

$$
\left\| \mathbf{k}(x, \cdot) e^{u/\varepsilon} \right\|_{s,2} \le |\mathcal{X}|^{1/2} e^{D/\varepsilon} (1 + \mathsf{m}_2(\mathsf{r} + \|\mathsf{c}\|_{s,\infty}))
$$

where  $|\mathcal{X}|$  is the Lebesgue measure of  $\mathcal{X}$ . Now, we can proceed analogously to [23, Proposition 2], and use Stein's Extension Theorem [1, Theorem 5.24],[52, Chapter 6], to guarantee the existence of a *total extension operator* [1, Definition 5.17]. In particular, there exists a constant  $m_3 = m_3(s, 2, \mathcal{X})$ such that for any  $\varphi \in W^{s,2}(\mathcal{X})$  there exists  $\tilde{\varphi} \in W^{s,2}(\mathbb{R}^d)$  such that

$$
\|\tilde{\varphi}\|_{\mathcal{H}} = \|\tilde{\varphi}\|_{s,2,\mathbb{R}^d} \leq m_3 \|\varphi\|_{s,2,\mathcal{X}} = m_3 \|\varphi\|_{s,2}. \tag{E.2}
$$

Therefore, we conclude

$$
\left\| \mathbf{k}(x,\cdot)e^{u/\varepsilon} \right\|_{\mathcal{H}} \le \mathsf{m}_3 |\mathcal{X}|^{1/2} e^{\mathsf{D}/\varepsilon} (1 + \mathsf{m}_2(\mathsf{r} + \left\| \mathsf{c} \right\|_{s,\infty})) =: \bar{\mathsf{r}}.\tag{E.3}
$$

The same argument applies to  $\mathsf{k}(x, \cdot)e^{v/\varepsilon}$  with the only exception that now, in virtue of Corollary [B.9,](#page-13-5) we have  $||e^{v/\varepsilon}||_{\infty} \leq e^{2D/\varepsilon}$ . Note that  $\bar{r}$  is a constant depending only on X, c, *s* and *d* but it is independent on the probability distributions  $\alpha$  and  $\beta$ .  $\Box$ 

**Sobolev spaces and reproducing kernel Hilbert spaces.** Recall that for *s > d/*2 the space  $\mathcal{H} = W^{s,2}(\mathbb{R}^d)$ , is a reproducing kernel Hilbert space (RKHS) [54, Chapter 10]. In this setting we denote by  $h: \mathcal{X} \times \mathcal{X} \to \mathbb{R}$  the associated reproducing kernel, which is continuous and bounded and satisfies the reproducing property

$$
(\forall x \in \mathcal{X})(\forall f \in \mathcal{H}) \qquad \langle f, \mathsf{h}(x, \cdot) \rangle_{\mathcal{H}} = f(x). \tag{E.4}
$$

We can also assume that h is *normalized*, namely,  $\|\mathsf{h}(x, \cdot)\|_{\mathcal{H}} = 1$  for all  $x \in \mathcal{X}$  [54, Chapter 10].

**Kernel mean embeddings.** For every  $\beta \in M_1^+(\mathcal{X})$ , we denote by  $h_\beta \in \mathcal{H}$  the *Kernel Mean Embedding* of  $\beta$  in H [44, 36], that is, the vector

$$
h_{\beta} = \int h(x, \cdot) \ d\beta(x). \tag{E.5}
$$

In other words, the kernel mean embedding of a distribution *β* corresponds to the expectation of h(x, ·) with respect to  $\beta$ . By the linearity of the inner product and the integral, for every  $f \in \mathcal{H}$ , the inner product

$$
\langle f, \mathsf{h}_{\beta} \rangle_{\mathcal{H}} = \int \langle f, \mathsf{h}(x, \cdot) \rangle \, d\beta(x) = \int f(x) \, d\beta(x), \tag{E.6}
$$

corresponds to the expectation of *f*(*x*) with respect to *β*. The *Maximum Mean Discrepancy (MMD)* [47, 48, 36] between two probability distributions  $\beta, \beta' \in \mathcal{M}_1^+(\mathcal{X})$  is defined as

$$
\text{MMD}(\beta, \beta') = \left\| \mathbf{h}_{\beta} - \mathbf{h}_{\beta'} \right\|_{\mathcal{H}}.
$$
\n(E.7)

In the case of the Sobolev space  $\mathcal{H} = W^{s,2}(\mathbb{R}^d)$ , the MMD metrizes the weak-\* topology of  $\mathcal{M}_1^+(\mathcal{X})$ [49, 48].

<span id="page-26-0"></span>A well-established approach to approximate a distribution  $\beta \in \mathcal{M}_1^+(\mathcal{X})$  is to independently sample a set of points  $x_1, \ldots, x_n \in \mathcal{X}$  from  $\beta$  and consider the empirical distribution  $\beta_n = \frac{1}{n}$  $\frac{1}{n} \sum_{i=1}^{n} \delta_{x_i}$ . The following result shows that  $\beta_n$  converges to  $\beta$  in MMD with high probability. The original version of this result can be found in [47], we report an independent proof for completeness.

**Lemma E.4.** Let  $\beta \in \mathcal{M}_1^+(\mathcal{X})$ . Let  $x_1, \ldots, x_n \in \mathcal{X}$  be indepedently sampled according to  $\beta$  and *denote by*  $\beta_n = \frac{1}{n}$  $\frac{1}{n} \sum_{i=1}^{n} \delta_{x_i}$ *. Then, for any*  $\tau \in (0,1]$ *, we have* 

$$
\text{MMD}(\beta_n, \beta) \le \frac{4 \log \frac{3}{\tau}}{\sqrt{n}} \tag{E.8}
$$

*with probability at least*  $1 - \tau$ *.* 

*Proof.* The proof follows by applying Pinelis' inequality [55, 40, 43] for random vectors in Hilbert spaces. More precisely, for  $i = 1, ..., n$ , denote by  $\zeta_i = h(x_i, \cdot) \in \mathcal{H}$  and recall that  $\|\zeta_i\| = \|h(x, \cdot)\| = 1$ for all  $x \in \mathcal{X}$ . We can therefore apply [43, Lemma 2] with constants  $\widetilde{M} = 1$  and  $\sigma^2 = \sup_i \mathbb{E} \|\zeta_i\|^2 \le 1$ , which guarantees that, for every  $\tau \in (0, 1]$ 

<span id="page-27-0"></span>
$$
\left\| \frac{1}{n} \sum_{i=1}^{n} \left[ \zeta_i - \mathbb{E} \zeta_i \right] \right\|_{\mathcal{H}} \le \frac{2 \log \frac{2}{\tau}}{n} + \sqrt{\frac{2 \log \frac{2}{\tau}}{n}} \le \frac{4 \log \frac{3}{\tau}}{\sqrt{n}},\tag{E.9}
$$

holds with probability at least  $1 - \tau$ . Here, for the second inequality we have used the fact that  $\log \frac{2}{\tau} \leq \log \frac{3}{\tau}$  and  $\log \frac{3}{\tau} \geq 1$  for every  $\tau \in (0, 1]$ . The desired result follows by observing that

$$
\mathsf{h}_{\beta} = \int \mathsf{h}(x, \cdot) \ d\beta(x) = \mathbb{E} \ \zeta_i \tag{E.10}
$$

for all  $i = 1, \ldots, n$ , and

$$
h_{\beta} = \frac{1}{m} \sum_{i=1}^{m} h(x_i, \cdot) = \frac{1}{m} \sum_{i=1}^{m} \zeta_i.
$$
 (E.11)

 $\Box$ 

Therefore,

$$
\text{MMD}(\beta_k, \beta) = \left\| \mathbf{h}_{\beta_k} - \mathbf{h}_{\beta} \right\|_{\mathcal{H}} = \left\| \frac{1}{n} \sum_{i=1}^n \left[ \zeta_i - \mathbb{E} \; \zeta_i \right] \right\|_{\mathcal{H}},\tag{E.12}
$$

which combined with  $(E.9)$  leads to the desired result.

<span id="page-27-2"></span>**Proposition E.5** (Lipschitz continuity of the Sinkhorn Potentials with respect to the MMD)**.** *Let*  $\mathcal{X} \subset \mathbb{R}^d$  be a compact Lipschitz domain and  $c \in C^{s+1}(\mathcal{X} \times \mathcal{X})$ , with  $s > d/2$ . Let  $\alpha, \beta, \alpha', \beta' \in \mathcal{M}_1^+(\mathcal{X})$ . Let  $x_o \in \mathcal{X}$  and let  $(u, v), (u', v') \in \mathcal{C}(\mathcal{X})^2$  be the two Sinkhorn potentials corresponding to the solution *of the regularized OT problem in* [\(B.24\)](#page-10-0) *for*  $(\alpha, \beta)$  *and*  $(\alpha', \beta')$  *respectively such that*  $u(x_o) = u'(x_o) = 0$ . *Then*

$$
\|u - u'\|_{\infty} \le 2\varepsilon \bar{r}e^{3D/\varepsilon} \left( \text{MMD}(\alpha, \alpha') + \text{MMD}(\beta, \beta') \right),\tag{E.13}
$$

<span id="page-27-1"></span>*with*  $\bar{r}$  *from Lemma [E.3.](#page-17-0)* In other words, the operator  $\nabla_1 O T_\varepsilon$ :  $\mathcal{M}_1^+(\mathcal{X})^2 \to \mathcal{C}(\mathcal{X})$ , defined in Fact [C.6,](#page-19-2) *is* 2*ε*¯r*e* <sup>3</sup>D*/ε-Lipschitz continuous with respect to the* MMD*.*

*Proof.* Let  $f = e^{u/\varepsilon}$  and  $g = e^{v/\varepsilon}$ . By relying on Lemma [E.3](#page-17-0) we can now refine the analysis in Theorem C.4. More precisely, we observe that in  $(C.10)$  we have

$$
[(L_{\beta'} - L_{\beta})g](x) = \int k(x, z)g(z) d(\beta - \beta')(z)
$$
  
= 
$$
\int \langle k(x, \cdot)g, h(z, \cdot) \rangle_{\mathcal{H}} d(\beta - \beta')(z)
$$
  
= 
$$
\langle k(x, \cdot)g, h_{\beta} - h_{\beta'} \rangle_{\mathcal{H}}
$$
  
\$\leq ||k(x, \cdot)g||\_{\mathcal{H}} ||h\_{\beta} - h\_{\beta'}||\_{\mathcal{H}}\$  
\$\leq \bar{r}\$ MMD}(\beta, \beta'),

where in the first equality, with some abuse of notation, we have implicitly considered the extension of  $\mathsf{k}(x, \cdot)$  to  $\mathcal{H} = W^{s,2}(\mathbb{R}^d)$  as discussed in Lemma [E.3.](#page-17-0) The rest of the analysis in Theorem C.4 remains invaried, eventually leading to [\(E.13\)](#page-27-1).  $\Box$ 

It is now clear that Theorem 6 in the paper is just a consequence of Lemma [E.4](#page-26-0) and Proposition [E.5.](#page-27-2) We give the statement of the theorem for reader's convenience.

**Theorem 6** (Sample Complexity of Sinkhorn Potentials). Suppose that  $c \in C^{s+1}(\mathcal{X} \times \mathcal{X})$  with  $s > d/2$ . *Then, there exists a constant*  $\bar{\mathbf{r}} = \bar{\mathbf{r}}(\mathcal{X}, \mathbf{c}, d)$  *such that for any*  $\alpha, \beta \in \mathcal{M}_1^+(\mathcal{X})$  *and any empirical measure*  $\hat{\beta}$  *of a set of n points independently sampled from*  $\beta$ *, we have, for every*  $\tau \in (0,1]$ 

$$
||u - u_n||_{\infty} = ||\nabla_1 \text{OT}_{\varepsilon}(\alpha, \beta) - \nabla_1 \text{OT}_{\varepsilon}(\alpha, \hat{\beta})||_{\infty} \le \frac{8\varepsilon \bar{r}e^{3\text{D}/\varepsilon} \log \frac{3}{\tau}}{\sqrt{n}} \tag{17}
$$

with probability at least  $1 - \tau$ , where  $u = \mathsf{T}_{\beta\alpha}(u)$ ,  $u_n = \mathsf{T}_{\hat{\beta}\alpha}(u_n)$  and  $u(x_o) = u_n(x_o) = 0$ .

We finally provide the proof of Theorem 7 in the paper.

**Theorem 7.** Suppose that  $c \in C^{s+1}(\mathcal{X} \times \mathcal{X})$  with  $s > d/2$ . Let  $n \in \mathbb{N}$  and  $\hat{\beta}_1, \ldots, \hat{\beta}_m$  be empirical *distributions with n support points, each independently sampled from*  $\beta_1, \ldots, \beta_m$ *. Let*  $\alpha_k$  *be the k*-th iterate of Algorithm 2 applied to  $\hat{\beta}_1, \ldots, \hat{\beta}_m$ . Then for any  $\tau \in (0,1]$ , the following holds with *probability larger than*  $1 - \tau$ 

$$
\mathsf{B}_{\varepsilon}(\alpha_{k}) - \min_{\alpha \in \mathcal{M}_{1}^{+}(\mathcal{X})} \mathsf{B}_{\varepsilon}(\alpha) \le \frac{64\bar{\mathsf{r}}\varepsilon e^{3\mathsf{D}/\varepsilon} \log \frac{3}{\tau}}{\min(k, \sqrt{n})}.
$$
\n(18)

*Proof.* Let  $\widehat{\mathsf{B}_{\varepsilon}}(\alpha) = \sum_{j=1}^{m} \omega_j \mathsf{S}_{\varepsilon}(\alpha, \widehat{\beta}_j)$ . Then, it follows from the definition of  $\mathsf{B}_{\varepsilon}$  and Theorem 6 that, for every  $k \in \mathbb{N}$ , and with probability larger than  $1 - \tau$ , we have

$$
\|\nabla \widehat{\mathsf{B}}_{\varepsilon}(\alpha_{k}) - \nabla \mathsf{B}_{\varepsilon}(\alpha_{k})\|_{\infty} \leq \sum_{j=1}^{m} \omega_{j} \|\nabla[\mathsf{S}_{\varepsilon}(\cdot, \hat{\beta}_{j})](\alpha_{k}) - \mathsf{S}_{\varepsilon}(\cdot, \beta_{j})](\alpha_{k})\|_{\infty}
$$

$$
= \sum_{j=1}^{m} \omega_{j} \|\nabla_{1} \mathrm{OT}_{\varepsilon}(\alpha_{k}, \hat{\beta}_{j}) - \nabla_{1} \mathrm{OT}_{\varepsilon}(\alpha_{k}, \beta_{j})\|_{\infty}
$$

$$
\leq \frac{8\varepsilon \bar{r}e^{3D/\varepsilon} \log \frac{3}{\tau}}{\sqrt{n}}
$$

$$
= \frac{\Delta_{1}}{4},
$$

where

$$
\Delta_1 := \frac{32\varepsilon \bar{\mathsf{r}} e^{3\mathsf{D}/\varepsilon} \log \frac{3}{\tau}}{\sqrt{n}}.
$$

Now, let  $\gamma_k = 2/(k+2)$ . Since Algorithm 2 is applied to  $\hat{\beta}_1, \ldots, \hat{\beta}_m$ , we have

$$
\delta_{x_{k+1}} \in \underset{\mathcal{M}_+^1(\mathcal{X})}{\text{argmin}} \langle \nabla \widehat{\mathsf{B}_{\varepsilon}}(\alpha_k), \cdot \rangle \quad \text{and} \quad \alpha_{k+1} = (1 - \gamma_k) \alpha_k + \gamma_k \delta_{x_{k+1}}.
$$

Therefore, it follows from Theorem A.5, Proposition [A.7,](#page-3-0) and Theorem 4 that, with probability larger than  $1 - \tau$ , we have

$$
\mathsf{B}_{\varepsilon}(\alpha_k) - \min_{\mathcal{M}^1_+(\mathcal{X})} \mathsf{B}_{\varepsilon} \leq 6 \varepsilon \bar{\mathsf{r}} e^{3\mathsf{D}/\varepsilon} \text{diam}(\mathcal{M}^1_+(\mathcal{X}))^2 \gamma_k + \Delta_1 \text{diam}(\mathcal{M}^1_+(\mathcal{X})).
$$

The statement follows by noting that  $\text{diam}(\mathcal{M}_+^1(\mathcal{X})) = 2$ .

 $\Box$ 

<span id="page-29-0"></span>

## **F Additional experiments**

**Sampling of continuous measures: mixture of Gaussians.** We perform the barycenter of 5 mixtures of two Gaussians  $\mu_j$ , centered at  $(j/2, 1/2)$  and  $(j/2, 3/2)$  for  $j - 0, \ldots, 4$  respectively. Samples are provided in Figure [6.](#page-29-1) We use different relative weights pairs in the mixture of Gaussians, namely  $(1/10, 9/10)$ ,  $(1/4, 3/4)$ ,  $(1/2, 1/2)$ . At each iteration, a sample of  $n = 500$  points is drawn from  $\mu_j$ ,  $j = 0 \ldots, 4$ . Results are reported in Figure [7.](#page-29-2)

<span id="page-29-1"></span>Image /page/29/Picture/2 description: The image displays six clusters of red dots arranged in two rows of three clusters each. The top row clusters are larger and more spread out, while the bottom row clusters are smaller and more compact. The clusters are positioned with some space between them, creating a visual separation.

Figure 6: Samples of input measures

<span id="page-29-2"></span>Image /page/29/Picture/4 description: The image displays six clusters of blue dots, arranged in two rows of three. Each cluster is dense in the center and tapers off towards the edges, with some individual dots scattered around the periphery. The clusters are roughly circular and appear to be randomly distributed across the white background.

Figure 7: Barycenters of Mixture of Gaussians

**Propagation.** We extend the description on the experiment about propagation in Section 6. Edges  $\mathcal E$  are selected as follows: we created a matrix *D* such that  $D_{ij}$  contains the distance between station at vertex *i* and station at vertex *j*, computed using the geographical coordinates of the stations. Each node *v* in  $V$ , is connected to those nodes  $u \in V$  such that  $D_{vu} \leq 3$ . If the number of nodes *u* that meet this condition is *less* than 5, we connect *v* with its 5 nearest nodes. If the number of nodes *u* that meet this condition is *more* than 10, we connect *v* with its 10 nearest nodes. Each edge *euv* is weighted with  $\omega_{uv} := D_{uv}$ . Since intuitively we may expect that nearer nodes should have more

<span id="page-29-3"></span>Image /page/29/Figure/7 description: Two 3D scatter plots are shown side-by-side. The plot on the left displays a dinosaur shape rendered in red points against a white grid background. The plot on the right displays a similar dinosaur shape rendered in blue points against a white grid background.

Figure 8: 3D dinosaur mesh (left), barycenter of 3D meshes (right)

influence in the construction of the histograms of unknown nodes, in the propagation functional we weight  $S_{\varepsilon}(\rho_v, \rho_u)$  with use  $\exp(-\omega_{uv}/\sigma)$  or  $1/\omega_{vu}$  suitably normalized.

**Large scale discrete measures: meshes.** We perform the barycenter of two discrete measures with support in  $\mathbb{R}^3$ . Meshes of the dinosaur are taken from [45] and rescaled by a 0.5 factor. The internal problem in Frank-Wolfe algorithm is solved using L-BFGS-B SciPy optimizer. Formula of the Jacobian is passed to the method. The barycenter is displayed in Figure [8](#page-29-3) together with an example of the input.
# Vision-Language Models for Vision Tasks: A <PERSON>

<PERSON><PERSON>† , <PERSON><PERSON><PERSON>† , <PERSON><PERSON> and <PERSON><PERSON><PERSON><sup>∗</sup>

**Abstract**—Most visual recognition studies rely heavily on crowd-labelled data in deep neural networks (DNNs) training, and they usually train a DNN for each single visual recognition task, leading to a laborious and time-consuming visual recognition paradigm. To address the two challenges, Vision-Language Models (VLMs) have been intensively investigated recently, which learns rich vision-language correlation from web-scale image-text pairs that are almost infinitely available on the Internet and enables zero-shot predictions on various visual recognition tasks with a single VLM. This paper provides a systematic review of visual language models for various visual recognition tasks, including: (1) the background that introduces the development of visual recognition paradigms; (2) the foundations of VLM that summarize the widely-adopted network architectures, pre-training objectives, and downstream tasks; (3) the widely-adopted datasets in VLM pre-training and evaluations; (4) the review and categorization of existing VLM pre-training methods, VLM transfer learning methods, and VLM knowledge distillation methods; (5) the benchmarking, analysis and discussion of the reviewed methods; (6) several research challenges and potential research directions that could be pursued in the future VLM studies for visual recognition. A project associated with this survey has been created at [https://github.com/jingyi0000/VLM](https://github.com/jingyi0000/VLM_survey) survey.

**Index Terms**—Visual recognition, vision-language model, pre-training, transfer learning, knowledge distillation, image classification, object detection, semantic segmentation, deep neural network, deep learning, big model, big data

✦

# **1 INTRODUCTION**

Visual recognition (e.g., image classification, object detection and semantic segmentation) is a long-standing challenge in computer vision research, and it is also the cornerstone of a myriad of computer vision applications in autonomous driving [\[1\]](#page-17-0), remote sensing [\[2\]](#page-17-1), robotics [\[3\]](#page-17-2), etc. With the advent of deep learning [\[4\]](#page-17-3), [\[5\]](#page-17-4), [\[6\]](#page-17-5), visual recognition research has achieved great success by leveraging end-to-end trainable deep neural networks (DNNs). However, the shift from *Traditional Machine Learning* [\[7\]](#page-17-6), [\[8\]](#page-17-7), [\[9\]](#page-17-8) toward deep learning comes with two new grand challenges, namely, the slow convergence of DNN training under the classical setup of *Deep Learning from Scratch* [\[4\]](#page-17-3), [\[5\]](#page-17-4), [\[6\]](#page-17-5) and the laborious collection of large-scale, taskspecific, and crowd-labelled data [\[10\]](#page-17-9) in DNN training.

Recently, a new learning paradigm *Pre-training, Finetuning and Prediction* has demonstrated great effectiveness in a wide range of visual recognition tasks [\[11\]](#page-17-10), [\[12\]](#page-17-11), [\[13\]](#page-17-12). Under this new paradigm, a DNN model is first pre-trained with certain off-the-shelf large-scale training data, being annotated or unannotated, and the pre-trained model is then fine-tuned with task-specific annotated training data as illustrated in Figs. [2](#page-1-0) (a) and (b). With comprehensive knowledge learned in the pre-trained models, this learning paradigm can accelerate network convergence and train well-performing models for various downstream tasks.

Nevertheless, the *Pre-training, Fine-tuning and Prediction* paradigm still requires an additional stage of task-specific fine-tuning with labelled training data from each downstream task. Inspired by the advances in natural language processing [\[14\]](#page-17-13), [\[15\]](#page-17-14), [\[16\]](#page-17-15), a new deep learning paradigm

<span id="page-0-0"></span>Image /page/0/Figure/11 description: This is a bar chart showing the number of published papers on Google Scholar from 2021 to 2023. The y-axis represents the number of published papers, ranging from 0 to 3000. The x-axis represents the years 2021, 2022, and 2023. In 2021, there were approximately 200 published papers, with notable papers including CLIP, ALIGN, DeCLIP, and ViLD. In 2022, the number of published papers increased to around 700, with papers like UniCL, SLIP, LiT, and GroupViT mentioned. By 2023, the number of published papers surged to over 2200, with prominent papers such as HiCLIP, OneR, RA-CLIP, and ALIP. An orange curve indicates an upward trend in the number of publications over these years.

Fig. 1: Number of publications on visual recognition VLMs (from Google Scholar). The publications grow exponentially since the pioneer study CLIP [\[10\]](#page-17-9) in 2021.

named *Vision-Language Model Pre-training and Zero-shot Prediction* has attracted increasing attention recently [\[10\]](#page-17-9), [\[17\]](#page-17-16), [\[18\]](#page-17-17). In this paradigm, a vision-language model (VLM) is pre-trained with large-scale image-text pairs that are almost infinitely available on the internet, and the pretrained VLM can be directly applied to downstream visual recognition tasks without fine-tuning as illustrated in Fig. [2](#page-1-0) (c). The VLM pre-training is usually guided by certain vision-language objectives  $[10]$ ,  $[18]$ ,  $[19]$  that enable to learn image-text correspondences from the large-scale image-text pairs  $[20]$ ,  $[21]$ ,  $e.g., CLIP$   $[10]$  employs an imagetext contrastive objective and learns by pulling the paired images and texts close and pushing others faraway in the embedding space. In this way, the pre-trained VLMs capture rich vision-language correspondence knowledge and can perform zero-shot predictions by matching the embeddings of any given images and texts. This new learning paradigm enables effective usage of web data and allows zero-shot

<sup>•</sup> *All authors are with the School of Computer Science and Engineering, Nanyang Technological University, Singapore.*

<sup>•</sup> † *denotes equal contribution;* ∗ *denotes corresponding author.*

<span id="page-1-0"></span>Image /page/1/Figure/1 description: The image displays three distinct methodologies for training deep neural networks: (a) Supervised Pre-training, Fine-tuning and Prediction; (b) Unsupervised Pre-training, Fine-tuning and Prediction; and (c) Vision-Language Model Pre-training and Zero-shot Prediction. Method (a) involves supervised pre-training on large-scale crowd-labeled images for image classification, followed by task-specific fine-tuning on crowd-labeled images from downstream tasks, and finally prediction on unlabeled images. Method (b) mirrors (a) but uses large-scale unlabelled images for unsupervised pre-training with pretext tasks like image inpainting and reconstruction, then task-specific fine-tuning, and prediction. Method (c) details Vision-Language Model Pre-training using web-scale image-text pairs for objectives like image-text contrastive learning and masked cross-modal modeling. It also illustrates Zero-shot Prediction without fine-tuning, where text prompts and unlabeled images are fed into separate text and image deep neural networks, respectively, followed by similarity calculation to predict a class, such as 'Cat'.

Fig. 2: Three DNN training paradigms in visual recognition. Compared with the paradigms in **(a)** and **(b)** that requires fine-tuning for each specific task with task-specific labelled data, the new learning paradigm with VLMs in **(c)** enables effective usage of web data and zero-shot predictions without task-specific fine-tuning.

predictions without task-specific fine-tuning, which is simple to implement yet performs incredibly well, e.g., the pretrained CLIP has achieved superior zero-shot performance on 36 visual recognition tasks ranging from classic image classification  $[22]$ ,  $[23]$ ,  $[24]$ ,  $[25]$ ,  $[26]$  to human action and optical character recognition [\[10\]](#page-17-9), [\[27\]](#page-17-26), [\[28\]](#page-17-27), [\[29\]](#page-17-28), [\[30\]](#page-17-29).

Following the great success of *Vision-Language Model Pretraining and Zero-shot Prediction*, two lines of research have been intensively investigated beyond various VLM pretraining studies. The first line explores VLMs with transfer learning [\[31\]](#page-17-30), [\[32\]](#page-17-31), [\[33\]](#page-17-32), [\[34\]](#page-17-33). It is evidenced by several transfer approaches,  $e.g.,$  prompt tuning  $[31], [32],$  $[31], [32],$  $[31], [32],$  $[31], [32],$  visual adaptation  $[33]$ ,  $[34]$ , etc., all sharing the same target for effective adaptation of pre-trained VLMs towards various downstream tasks. The second line explores VLMs with knowledge distillation  $[35]$ ,  $[36]$ ,  $[37]$ , e.g., several studies [\[35\]](#page-17-34), [\[36\]](#page-17-35), [\[37\]](#page-17-36) explore how to distill knowledge from VLMs to downstream tasks, aiming for better performance in object detection, semantic segmentation, etc.

Despite the intensive interest in harvesting the vast knowledge from VLMs as evidenced by a great number of recent papers as shown in Fig. [1,](#page-0-0) the research community is short of a comprehensive survey that can help sort out existing VLM-based visual recognition studies, the facing challenges, as well as future research directions. We aim to fill up this gap by performing a systematic survey of VLM studies in various visual recognition tasks including image

classification, object detection, semantic segmentation, etc. We conduct the survey from different perspectives including background, foundations, datasets, technical approaches, benchmarking, and future research directions. We believe that this survey will provide a clear big picture on what we have achieved, and we could further achieve along this emerging yet very prospective research direction.

In summary, the main contributions of this work are threefold. *First*, it presents a systematic review of VLMs for visual recognition tasks including image classification, object detection and semantic segmentation. To the best of our knowledge, this is the *first* survey of VLMs for visual recognition, which provides a big picture of this promising research filed with comprehensive summary and categorization of existing studies. *Second*, it studies the upto-date progress of VLMs for visual recognition, including a comprehensive benchmarking and discussion of existing work over multiple public datasets. *Third*, it shares several research challenges and potential research directions that could be pursued in VLMs for visual recognition.

The rest of this survey is organized as follows. Section [2](#page-2-0) introduces the paradigm development of visual recognition and several related surveys. Section [3](#page-3-0) describes the foundations of VLMs, including widely used deep network architectures, pre-training objectives, pre-training frameworks and downstream tasks in VLM evaluations. Section [4](#page-6-0) introduces the commonly used datasets in VLM pre-training

and evaluations. Section [5](#page-6-1) reviews and categorizes VLM pre-training methods. Sections [6](#page-10-0) and [7](#page-12-0) provide a systematic review of transfer learning and knowledge distillation approaches for VLMs , respectively. Section [8](#page-13-0) benchmarks the reviewed methods on multiple widely-adopted datasets. Finally, we share several promising VLM research directions in Section [9.](#page-16-0)

# <span id="page-2-0"></span>**2 BACKGROUND**

This section first presents the development of the training paradigm of visual recognition and how it evolves towards the paradigm *Vision-Language Model Pre-training and Zeroshot Prediction*. Then, we introduce the development of the vision-language models (VLMs) for visual recognition. We also discuss several related surveys to highlight the scope and contributions of this survey.

## **2.1 Training Paradigms for Visual Recognition**

The development of visual recognition paradigms can be broadly divided into five stages, including (1) *Traditional Machine Learning and Prediction*, (2) *Deep Learning from Scratch and Prediction*, (3) *Supervised Pre-training, Fine-tuning and Prediction*, (4) *Unsupervised Pre-training, Fine-tuning and Prediction* and (5) *Vision-language Model Pre-training and Zeroshot Prediction*. In what following, we introduce, compare and analyze the five training paradigms in detail.

#### *2.1.1 Traditional Machine Learning and Prediction*

Before the deep learning era [\[4\]](#page-17-3), visual recognition studies rely heavily on *feature engineering* with hand-crafted features [\[9\]](#page-17-8), [\[38\]](#page-17-37) and lightweight learning models [\[7\]](#page-17-6), [\[8\]](#page-17-7), [\[39\]](#page-17-38) that classify the hand-crafted features into pre-defined semantic categories. However, this paradigm requires domain experts for crafting effective features for specific visual recognition tasks, which does not cope with complex tasks well and also has poor scalability.

### *2.1.2 Deep Learning from Scratch and Prediction*

With the advent of deep learning  $[4]$ ,  $[5]$ ,  $[6]$ , visual recognition research has achieved great success by leveraging end-to-end trainable DNNs that circumvent the complicated *feature engineering* and allow focusing on the *architecture engineering* of neural networks for learning effective features. For example, ResNet [\[6\]](#page-17-5) enables very deep networks by a skip design and allows learning from massive crowdlabelled data with unprecedented performance on the challenging ImageNet benchmark [\[40\]](#page-17-39). However, the turn from traditional machine learning toward deep learning raises two new grand challenges: the slow convergence of DNN training under the classical setup of *Deep Learning from Scratch* and the laborious collection of large-scale, taskspecific, and crowd-labelled data [\[10\]](#page-17-9) in DNN training.

### 2.1.3 Supervised Pre-training, Fine-tuning and Prediction

With the discovery that features learned from labelled largescale datasets can be transferred to downstream tasks [\[11\]](#page-17-10), the paradigm *Deep Learning from Scratch and Prediction* has been gradually replaced by a new paradigm of *Supervised Pre-training, Fine-tuning and Prediction*. This new learning

<span id="page-2-1"></span>Image /page/2/Figure/12 description: The image is a diagram illustrating the relationship between different pre-training objectives, frameworks, and downstream tasks in the field of vision-language models. The diagram is organized into three horizontal rows, each representing a different category: 'Pre-training Objective', 'Pre-training Framework', and 'Downstream Task'. The 'CLIP, ALIGN' label is positioned to the left, acting as a starting point for the 'Pre-training Framework' row. The 'Pre-training Objective' row, highlighted in yellow, shows a progression from 'Single objective' to 'Multiple hybrid objectives', with specific models like UniCL, FILIP, NLIP, and FIBER positioned along this spectrum. The 'Pre-training Framework' row, colored in blue, shows a progression from 'Multiple separate networks' to 'A Unified network', featuring models such as FLAVA, COCA, CLIPPO, and OneR. The 'Downstream Task' row, colored in green, lists models like SLIP, GroupViT, GLIP, and DetCLIP, implying their application in various downstream tasks. Arrows indicate the progression and relationships between these categories.

*Simple Task -------------------------- Complex Task* 

Fig. 3: Illustration of development of VLMs for visual recognition.

paradigm, as illustrated in Fig. [2](#page-1-0) (a), pre-trains DNNs on large-scale labelled data (e.g., ImageNet) with a supervised loss and then fine-tunes the pre-trained DNN with taskspecific training data [\[11\]](#page-17-10). As the pre-trained DNNs have learned certain visual knowledge, it can accelerate network convergence and help train well-performing models with limited task-specific training data.

*2.1.4 Unsupervised Pre-training, Fine-tuning & Prediction* Though *Supervised Pre-training, Fine-tuning and Prediction* achieves state-of-the-art performance on many visual recognition tasks, it requires large-scale labelled data in pretraining. To mitigate this constraint,  $[12]$ ,  $[13]$  adopt a new learning paradigm *Unsupervised Pre-training, Fine-tuning and Prediction* that explores self-supervised learning to learn useful and transferable representations from unlabelled data, as illustrated in Fig. [2](#page-1-0) (b). To this end, various selfsupervised training objectives [\[12\]](#page-17-11), [\[41\]](#page-17-40) have been proposed including masked image modelling that models cross-patch relations [\[41\]](#page-17-40), contrastive learning that learns discriminative features by contrasting training samples [\[12\]](#page-17-11), etc. The selfsupervised pre-trained models are then fine-tuned on downstream tasks with labelled task-specific training data. Since this paradigm does not require labelled data in pre-training, it can exploit more training data for learning useful and transferable features, leading to even better performance as compared with the supervised pre-training [\[12\]](#page-17-11), [\[13\]](#page-17-12).

# *2.1.5 VLM Pre-training and Zero-shot Prediction*

Though *Pre-training and Fine-tuning* with either supervised or unsupervised pre-training improves the network convergence, it still requires a fine-tuning stage with labelled task data as shown in Figs. [2](#page-1-0) (a) and (b). Motivated by great success in natural language processing  $[14]$ ,  $[15]$ ,  $[16]$ , a new deep learning paradigm named *Vision-Language Model Pre-training and Zero-shot Prediction* has been proposed for visual recognition, as shown in Fig. [2](#page-1-0) (c). With large-scale image-text pairs that are almost infinitely available on the internet, a VLM is pre-trained by certain vision-language objectives  $[10]$ ,  $[18]$ ,  $[19]$  which captures rich vision-language knowledge and can perform zero-shot predictions (without fine-tuning) on downstream visual recognition tasks by matching the embeddings of any given images and texts.

Compared with *Pre-training and Fine-tuning*, this new paradigm enables effective use of large-scale web data

Image /page/3/Figure/1 description: This is a flowchart illustrating different approaches to Vision-Language Models for Visual Recognition. The main categories are Vision-Language Model Pre-training (Section 5), Vision-Language Model Transfer Learning (Section 6), and Vision-Language Model Knowledge Distillation (Section 7). Under Pre-training, there are three sub-categories: Contrastive Objectives (Section 5.1) with three methods (Image contrastive learning, Image-Text Contrastive Learning, Image-Text-Label Contrastive Learning), Generative Objectives (Section 5.2) with four methods (Masked Image Modelling, Masked Language Modelling, Masked Cross-modal Modelling, Image-to-Text Generation), and Alignment Objectives (Section 5.3) with two methods (Image-Text Matching, Region-Word Matching). Under Transfer Learning, there are three sub-categories: Transfer via Prompt Tuning (Section 6.3.1) with three methods (Transfer with Text Prompt Tuning, Transfer with Visual Prompt Tuning, Transfer with Text and Visual Prompt Tuning), Transfer via Feature Adapter (Section 6.3.2), and Other Transfer Methods (Section 6.3.3). Under Knowledge Distillation, there are two sub-categories: Knowledge Distillation for Object Detection (Section 7.2.1) and Knowledge Distillation for Semantic Segmentation (Section 7.2.2).

Fig. 4: Typology of vision-language models for visual recognition.

and zero-shot predictions without task-specific fine-tuning. Most existing research attempts to improve VLMs from 3 perspectives: 1) collecting large-scale informative imagetext data, 2) designing high-capacity models for effective learning from big data, 3) designing new pre-training objectives for learning effective VLMs. In this paper, we provide a systematic survey of this new vision-language learning paradigm aiming to provide a clear big picture on exiting VLM studies, the facing challenges and future directions for this challenging but promising research filed.

#### **2.2 Development of VLMs for Visual Recognition**

Visual recognition related VLM studies have made great progresses since the development of CLIP [\[10\]](#page-17-9). We present VLMs for visual recognition from three aspects as illustrated in Fig. [3:](#page-2-1) (1) *Pre-training objectives: from "a single objective" to "multiple hybrid objectives".* Early VLMs [\[10\]](#page-17-9), [\[17\]](#page-17-16) generally adopt a single pre-training objective, whereas recent VLMs [\[18\]](#page-17-17), [\[42\]](#page-17-41) introduce multiple objectives  $(e.g.,$ contrastive, alignment and generative objectives) for exploring their synergy for more robust VLMs and better performance in downstream tasks; (2) *Pre-training frameworks: from "multiple separate networks" to "a unified network".* Early VLMs [\[10\]](#page-17-9), [\[17\]](#page-17-16) employ two-tower pre-training frameworks, whereas recent VLMs [\[43\]](#page-17-42), [\[44\]](#page-17-43) attempt one-tower pretraining framework that encodes images and texts with a unified network with less GPU memory usage yet more efficient communications across data modalities; 3) *Downstream tasks: from simple to complex tasks.* Early VLMs [\[10\]](#page-17-9), [\[17\]](#page-17-16) focus on image-level visual recognition tasks, whereas recent VLMs [\[45\]](#page-17-44), [\[46\]](#page-17-45) are more general-purpose which can also work for dense prediction tasks that are complex and require localization related knowledge.

### **2.3 Relevant Surveys**

To the best of our knowledge, this is the *first* survey that reviews VLMs for various visual recognition tasks. Several

relevant surveys have been conducted which review VLMs for vision-language tasks instead such as visual question answering  $[47]$ , natural language for visual reasoning  $[48]$ , and phrase grounding [\[49\]](#page-17-48). For instance, Li *et al.* [\[50\]](#page-17-49) shared advances on vision-language tasks, including VLM pre-training for various task-specific methods. Du *et al.* [\[51\]](#page-17-50) and Chen *et al.* [\[52\]](#page-17-51) reviewed VLM pre-training for visionlanguage tasks [\[47\]](#page-17-46), [\[48\]](#page-17-47), [\[49\]](#page-17-48). Xu *et al.* [\[53\]](#page-17-52) and Wang *et al.* [\[54\]](#page-17-53) shared recent progress of multi-modal learning on multi-modal tasks. Differently, we review VLMs for visual recognition tasks from three major aspects: 1) Recent progress of VLM pre-training for visual recognition tasks; 2) Two typical transfer approaches from VLMs to visual recognition tasks; 3) Benchmarking of VLM pre-training methods on visual recognition tasks.

# <span id="page-3-0"></span>**3 VLM FOUNDATIONS**

VLM pre-training  $[10]$ ,  $[17]$  aims to pre-train a VLM to learn image-text correlation, targeting effective zero-shot predictions on visual recognition tasks [\[6\]](#page-17-5), [\[55\]](#page-17-54), [\[56\]](#page-17-55). Given image-text pairs  $[20]$ ,  $[21]$ , it first employs a text encoder and an image encoder to extract image and text features  $[6]$ ,  $[14]$ , [\[57\]](#page-17-56), [\[58\]](#page-17-57) and then learns the vision-language correlation with certain pre-training objectives [\[10\]](#page-17-9), [\[17\]](#page-17-16). Hence, VLMs can be evaluated on unseen data in a zero-shot manner  $[10]$ , [\[17\]](#page-17-16) by matching the embeddings of any given images and texts. This section introduces the foundations of VLM pre-training, including common network architectures for extracting image and text features, pre-training objectives for modelling vision-language correlation, frameworks for VLM pre-training and downstream tasks for VLM evaluations.

#### **3.1 Network Architectures**

VLM pre-training works with a deep neural network that extracts image and text features from  $N$  image-text pairs

within a pre-training dataset  $\mathcal{D} = \{x_n^I, x_n^T\}_{n=1}^N$ , where  $x_n^I$ and  $x_n^T$  denote an image sample and its paired text sample. The deep neural network has an image encoder  $f_{\theta}$  and a text encoder  $f_{\phi}$ , which encode the image and text (from an image-text pair  $\{x_n^I, x_n^T\}$  into an image embedding  $z_n^I = f_\theta(x_n^I)$  and a text embedding  $z_n^T = f_\phi(x_n^T)$ , respectively. This section presents the architecture of widelyadopted deep neural networks in VLM pre-training.

#### *3.1.1 Architectures for Learning Image Features*

Two types of network architectures have been widely adopted to learn image features, namely, CNN-based architectures and Transformer-based architectures.

**CNN-based Architectures.** Different ConvNets (e.g., VGG [\[5\]](#page-17-4), ResNet [\[6\]](#page-17-5) and EfficientNet [\[59\]](#page-18-0)) have been designed for learning image features. Being one of the most popular ConvNet in VLM pre-training, ResNet [\[6\]](#page-17-5) adopts skip connections between convolution blocks which mitigates gradient vanishing and explosion and enables very deep neural networks. For better feature extraction and vision-language modelling, several studies [\[10\]](#page-17-9) modify the original network architecture [\[6\]](#page-17-5), [\[59\]](#page-18-0). Take ResNet as an example. They introduce the ResNet-D [\[60\]](#page-18-1), employ the antialiased rect-2 blur pooling in [\[61\]](#page-18-2), and replace the global average pooling with an attention pooling in the transformer multi-head attention [\[58\]](#page-17-57).

**Transformer-based Architectures.** Transformers have recently been extensively explored in visual recognition tasks, such as image classification [\[57\]](#page-17-56), object detection [\[62\]](#page-18-3) and semantic segmentation  $[63]$ . As a standard Transformer architecture for image feature learning, ViT [\[57\]](#page-17-56) employs a stack of Transformer blocks each of which consists of a multi-head self-attention layer and a feed-forward network. The input image is first split into fixed-size patches and then fed to the Transformer encoder after linear projection and position embedding. [\[10\]](#page-17-9), [\[18\]](#page-17-17), [\[64\]](#page-18-5) modify ViT by adding a normalization layer before the transformer encoder.

## *3.1.2 Architectures for Learning Language Features*

Transformer & its variants  $[14]$ ,  $[16]$ ,  $[58]$  have been widely adopted for learning text features. The standard Transformer [\[58\]](#page-17-57) has an encoder-decoder structure, where the encoder has 6 blocks each of which has a multi-head selfattention layer and a multi-layer perceptron (MLP). The decoder also has 6 blocks each of which has a multi-head attention layer, a masked multi-head layer and a MLP. Most VLM studies such as CLIP [\[10\]](#page-17-9) adopt the standard Transformer  $[58]$  with minor modifications as in GPT<sub>2</sub> [\[16\]](#page-17-15), and train from scratch without initialization with  $GPT<sub>2</sub>$  weights.

# **3.2 VLM Pre-training Objectives**

As the core of VLM, various vision-language pre-training objectives [\[10\]](#page-17-9), [\[12\]](#page-17-11), [\[14\]](#page-17-13), [\[19\]](#page-17-18), [\[42\]](#page-17-41), [\[65\]](#page-18-6), [\[66\]](#page-18-7), [\[67\]](#page-18-8) have been designed for learning rich vision-language correlation. They fall broadly into three categories: contrastive objectives, generative objectives and alignment objectives.

<span id="page-4-5"></span>

### 3.2.1 Contrastive Objectives

Contrastive objectives train VLMs to learn discriminative representations by pulling paired samples close and pushing others faraway in the feature space  $[10]$ ,  $[12]$ ,  $[65]$ .

**Image Contrastive Learning** aims to learn discriminative image features  $[12]$ ,  $[13]$  by forcing a query image to be close with its positive keys  $(i.e.,$  its data augmentations) and faraway from its negative keys  $(i.e.,$  other images) in the embedding space. Given a batch of  $B$  images, contrastivelearning objectives (e.g., InfoNCE  $[68]$  and its variants  $[12]$ , [\[13\]](#page-17-12)) are usually formulated as follows:

<span id="page-4-4"></span>
$$
\mathcal{L}_I^{\text{InfoNCE}} = -\frac{1}{B} \sum_{i=1}^{B} \log \frac{\exp(z_i^I \cdot z_+^I / \tau)}{\sum_{j=1, j \neq i}^{B+1} \exp(z_i^I \cdot z_j^I / \tau)}, \quad (1)
$$

where  $z_i^I$  is the query embedding,  $\{z_j^I\}_{j=1,j\neq i}^{B+1}$  are key embeddings, where  $z_+^I$  stands for  $z_i^I$ 's positive key and the rest are  $z_i^I$ 's negative keys.  $\tau$  is a temperature hyper-parameter that controls the density of the learned representation.

**Image-Text Contrastive Learning** aims to learn discriminative image-text representations by pulling the embeddings of paired images and texts close while pushing others [\[10\]](#page-17-9), [\[17\]](#page-17-16) away. It is usually achieved by minimizing a sym-metrical image-text infoNCE loss [\[10\]](#page-17-9), *i.e.*,  $\mathcal{L}^{IT}_{\text{infoNCE}}$  =  $\mathcal{L}_{I\rightarrow T}+\mathcal{L}_{T\rightarrow I}$ , where  $\mathcal{L}_{I\rightarrow T}$  contrasts the query image with the text keys while  $\mathcal{L}_{T\rightarrow I}$  contrasts the query text with image keys. Given a batch of B image-text pairs,  $\mathcal{L}_{I\rightarrow T}$  and  $\mathcal{L}_{T\rightarrow I}$  are defined as follows:

<span id="page-4-0"></span>
$$
\mathcal{L}_{I \to T} = -\frac{1}{B} \sum_{i=1}^{B} \log \frac{\exp(z_i^I \cdot z_i^T / \tau)}{\sum_{j=1}^{B} \exp(z_i^I \cdot z_j^T / \tau)},\tag{2}
$$

<span id="page-4-1"></span>
$$
\mathcal{L}_{T \to I} = -\frac{1}{B} \sum_{i=1}^{B} \log \frac{\exp(z_i^T \cdot z_i^I / \tau)}{\sum_{j=1}^{B} \exp(z_i^T \cdot z_j^I / \tau)},\tag{3}
$$

where  $z^I$  and  $z^T$  stand for the image embeddings and text embeddings, respectively.

**Image-Text-Label Contrastive Learning.** Image-text-label contrastive learning [\[65\]](#page-18-6) introduces Supervised Contrastive Learning [\[69\]](#page-18-10) into image-text contrastive learning, which is defined by reformulating Eqs. [2](#page-4-0) and [3](#page-4-1) as follows:

<span id="page-4-2"></span>
$$
\mathcal{L}_{I \to T}^{ITL} = -\sum_{i=1}^{B} \frac{1}{|\mathcal{P}(i)|} \sum_{k \in \mathcal{P}(i)} \log \frac{\exp(z_i^I \cdot z_k^T / \tau)}{\sum_{j=1}^{B} \exp(z_i^I \cdot z_j^T / \tau)}, \tag{4}
$$

<span id="page-4-3"></span>
$$
\mathcal{L}_{T \to I}^{ITL} = -\sum_{i=1}^{B} \frac{1}{|\mathcal{P}(i)|} \sum_{k \in \mathcal{P}(i)} \log \frac{\exp(z_i^T \cdot z_k^I / \tau)}{\sum_{j=1}^{B} \exp(z_i^T \cdot z_j^I / \tau)}, \quad (5)
$$

where  $k \in \mathcal{P}(i) = \{k | k \in B, y_k = y_i\}$  [\[65\]](#page-18-6) and y is the category label of  $(z^I, z^T)$ . With Eqs. [4](#page-4-2) and [5,](#page-4-3) the image-textlabel infoNCE loss is defined as:  $\mathcal{L}_{\text{infoNCE}}^{ITL} = \mathcal{L}_{I \rightarrow T}^{ITL} + \mathcal{L}_{T \rightarrow I}^{ITL}$ .

# *3.2.2 Generative Objectives*

Generative objectives learn semantic features by training networks to generate image/text data via image generation  $[12]$ ,  $[70]$ , language generation  $[14]$ ,  $[19]$ , or cross-modal generation [\[42\]](#page-17-41).

**Masked Image Modelling** learns cross-patch correlation by masking and reconstructing images [\[41\]](#page-17-40), [\[70\]](#page-18-11). It masks a set of patches of an input image randomly and trains the encoder to reconstruct the masked patches conditioned on unmasked patches. Given a batch of  $B$  images, the loss function can be formulated as:

<span id="page-4-6"></span>
$$
\mathcal{L}_{MIM} = -\frac{1}{B} \sum_{i=1}^{B} \log f_{\theta}(\overline{x}_{i}^{I} \mid \hat{x}_{i}^{I}), \qquad (6)
$$

where  $\bar{x}_i^I$  and  $\hat{x}_i^I$  denote the masked patches and the unmasked patches in  $x_i^I$ , respectively.

**Masked Language Modelling** is a widely adopted pretraining objective in NLP [\[14\]](#page-17-13). It randomly masks a certain percentage (*e.g.*, 15% in BERT [\[14\]](#page-17-13)) of the input text tokens, and reconstruct them with unmasked tokens:

<span id="page-5-1"></span>
$$
\mathcal{L}_{MLM} = -\frac{1}{B} \sum_{i=1}^{B} \log f_{\phi}(\overline{x}_{i}^{T} | \hat{x}_{i}^{T}), \qquad (7)
$$

where  $\overline{x}_i^T$  and  $\hat{x}_i^T$  denote the masked and unmasked tokens in  $x_i^T$ , respectively.  $B$  denotes the batch size.

**Masked Cross-Modal Modelling** integrates masked image modelling and masked language modelling [\[42\]](#page-17-41). Given an image-text pair, it randomly masks a subset of image patches and a subset of text tokens and then learns to reconstruct them conditioned on unmasked image patches and unmasked text tokens as follows:

<span id="page-5-2"></span>
$$
\mathcal{L}_{MCM} = -\frac{1}{B} \sum_{i=1}^{B} [\log f_{\theta}(\overline{x}_i^I | \hat{x}_i^I, \hat{x}_i^T) + \log f_{\phi}(\overline{x}_i^T | \hat{x}_i^I, \hat{x}_i^T)],
$$
\n(8)

where  $\overline{x}_i^I / \hat{x}_i^I$  denotes the masked/unmasked patches in  $x_i^I$ ,  $\overline{x}_i^T/\hat{x}_i^T$  denotes the masked/unmasked text tokens in  $x_i^T$ .

**Image-to-Text Generation** aims to predict text  $x^T$  autoregressively based on the image paired with  $x^T$  [\[19\]](#page-17-18):

<span id="page-5-3"></span>
$$
\mathcal{L}_{ITG} = -\sum_{l=1}^{L} \log f_{\theta}(x^T \mid x_{\leq l}^T, z^I), \tag{9}
$$

where  $L$  denotes the number of tokens to be predicted for  $x^T$  and  $z^I$  is the embedding of the image paired with  $x^T$ .

# *3.2.3 Alignment Objectives*

Alignment objectives align the image-text pair via global image-text matching [\[71\]](#page-18-12), [\[72\]](#page-18-13) or local region-word matching  $[45]$ ,  $[67]$  on embedding space.

**Image-Text Matching** models global correlation between images and texts [\[71\]](#page-18-12), [\[72\]](#page-18-13), which can be formulated with a score function  $\mathcal{S}(\cdot)$  that measures the alignment probability between the image and text and a binary classification loss:

<span id="page-5-4"></span>
$$
\mathcal{L}_{IT} = p \log \mathcal{S}(z^I, z^T) + (1 - p) \log(1 - \mathcal{S}(z^I, z^T)), \quad (10)
$$

where  $p$  is 1 if the image and text are paired and 0 otherwise. **Region-Word Matching** aims to model local cross-modal correlation (i.e., between "image regions" and "words") in image-text pairs  $[45]$ ,  $[67]$  for dense visual recognition tasks such as object detection. It can be formulated as:

$$
\mathcal{L}_{RW} = p \log \mathcal{S}^r(r^I, w^T) + (1 - p) \log(1 - \mathcal{S}^r(r^I, w^T)), \tag{11}
$$

where  $(r^I, w^T)$  denotes a region-word pair and  $p = 1$  if the region and word are paired otherwise  $p = 0$ .  $S<sup>r</sup>(.)$  denotes a local score function that measures the similarity between "image regions" and "words".

#### **3.3 VLM Pre-training Frameworks**

This section presents widely adopted frameworks in VLM pre-training, including two-tower, two-leg and one-tower pre-training frameworks.

Specifically, two-tower framework has been widely adopted in VLM pre-training [\[10\]](#page-17-9), [\[17\]](#page-17-16), where input images

<span id="page-5-0"></span>Image /page/5/Figure/21 description: The image displays three different architectures for Vision-Language Models (VLMs): (a) Two-Tower VLM, (b) Two-Leg VLM, and (c) One-Tower VLM. In the Two-Tower VLM, separate Image Deep Neural Networks and Text Deep Neural Networks process input images and texts, respectively, to generate image and text embeddings, which are then used for pre-training objectives. The Two-Leg VLM also uses separate Image and Text Deep Neural Networks for embeddings, but these embeddings are fed into Multi-modal Fusion Layers before being used for pre-training objectives. The One-Tower VLM utilizes a Unified Deep Neural Network to process both input images and texts, generating combined image and text embeddings for pre-training objectives.

Fig. 5: Illustration of typical VLM pre-training frameworks.

and texts are encoded with two separate encoders respectively, as illustrated in Fig. [5](#page-5-0) (a). Slightly differently, two-leg framework [\[19\]](#page-17-18), [\[42\]](#page-17-41) introduces additional multi-modal fusion layers which enable feature interaction between image and text modalities, as illustrated in Fig.  $5$  (b). As a comparison, one-tower VLMs [\[43\]](#page-17-42), [\[44\]](#page-17-43) attempt to unify vision and language learning in a single encoder as illustrated in Fig. [5](#page-5-0) (c), aiming to facilitate efficient communications across data modalities.

# <span id="page-5-5"></span>**3.4 Evaluation Setups and Downstream Tasks**

This section presents widely adopted setups and downstream tasks in VLM evaluation. The setups include *zeroshot prediction* and *linear probing*, and the downstream tasks include image classification, object detection, semantic segmentation, image-text retrieval, and action recognition.

#### *3.4.1 Zero-shot Prediction*

As the most common way of evaluating VLMs' generalization capability  $[10]$ ,  $[17]$ ,  $[18]$ ,  $[64]$ ,  $[84]$ , zero-shot prediction directly applies pre-trained VLMs to downstream tasks without any task-specific fine-tuning [\[10\]](#page-17-9).

**Image Classification** [\[5\]](#page-17-4), [\[6\]](#page-17-5) aims to classify images into predefined categories. VLMs achieve zero-shot image classification by comparing the embeddings of images and texts, where "prompt engineering" is often employed to generate task-related prompts like "a photo of a  $[$  label]."  $[$ 10].

**Semantic Segmentation** [\[56\]](#page-17-55) aims to assign a category label to each pixel in images. Pre-trained VLMs achieve zeroshot prediction for segmentation tasks by comparing the embeddings of the given image pixels and texts.

**Object Detection** [\[11\]](#page-17-10), [\[55\]](#page-17-54) aims to localize and classify objects in images, which is important for various vision applications. With the object locating ability learned from auxiliary datasets  $[85]$ ,  $[86]$ , pre-trained VLMs achieve zeroshot prediction for object detection tasks by comparing the embeddings of the given object proposals and texts.

**Image-Text Retrieval** [\[87\]](#page-18-17) aims to retrieve the demanded samples from one modality given the cues from another modality, which consists of two tasks, i.e., text-to-image retrieval that retrieves images based on texts and imageto-text retrieval that retrieves texts based on images.

### *3.4.2 Linear Probing*

Linear probing has been widely adopted in VLM evaluations [\[10\]](#page-17-9). It freezes the pre-trained VLM and trains a linear classifier to classify the VLM-encoded embeddings to assess the VLM representations. Image classification [\[5\]](#page-17-4), [\[6\]](#page-17-5) and

<span id="page-6-2"></span>TABLE 1: Summary of the widely used image-text datasets for VLM pre-training. [link] directs to dataset websites.

| <b>Dataset</b>                                                   | Year | Num. of Image-Text Pairs | Language           | Public |
|------------------------------------------------------------------|------|--------------------------|--------------------|--------|
| SBU Caption [73] [link]                                          | 2011 | 1M                       | English            | $✓$    |
| COCO Caption [74] [link]                                         | 2016 | 1.5M                     | English            | $✓$    |
| Yahoo Flickr Creative Commons 100 Million (YFCC100M) [75] [link] | 2016 | 100M                     | English            | $✓$    |
| Visual Genome (VG) [76] [link]                                   | 2017 | 5.4 M                    | English            | $✓$    |
| Conceptual Captions (CC3M) [77] [link]                           | 2018 | 3.3M                     | English            | $✓$    |
| Localized Narratives (LN) [78] [link]                            | 2020 | 0.87M                    | English            | $✓$    |
| Conceptual 12M (CC12M) [79] [link]                               | 2021 | 12M                      | English            | $✓$    |
| Wikipedia-based Image Text (WIT) [80] [link]                     | 2021 | 37.6M                    | 108 Languages      | $✓$    |
| Red Caps (RC) [81] [link]                                        | 2021 | 12M                      | English            | $✓$    |
| LAION400M [21] [link]                                            | 2021 | 400M                     | English            | $✓$    |
| LAION5B [20] [link]                                              | 2022 | 5B                       | Over 100 Languages | $✓$    |
| WuKong [82] [link]                                               | 2022 | 100M                     | Chinese            | $✓$    |
| CLIP [10]                                                        | 2021 | 400M                     | English            | $✗$    |
| ALIGN [17]                                                       | 2021 | 1.8B                     | English            | $✗$    |
| FILIP [18]                                                       | 2021 | 300M                     | English            | $✗$    |
| WebLI [83]                                                       | 2022 | 12B                      | 109 Languages      | $✗$    |

action recognition [\[28\]](#page-17-27), [\[29\]](#page-17-28) have been widely adopted in such evaluations, where video clips are often sub-sampled for efficient recognition in action recognition tasks [\[10\]](#page-17-9).

# <span id="page-6-0"></span>**4 DATASETS**

This section summarizes the commonly used datasets for VLM pre-training and evaluations, as detailed in Tables [1-](#page-6-2)[2.](#page-7-0)

#### **4.1 Datasets for Pre-training VLMs**

For VLM pre-training, multiple large-scale image-text datasets [\[10\]](#page-17-9), [\[17\]](#page-17-16), [\[20\]](#page-17-19), [\[21\]](#page-17-20) were collected from the internet. Compared with traditional crowd-labelled datasets [\[40\]](#page-17-39), [\[90\]](#page-18-29),  $[110]$ , the image-text datasets  $[10]$ ,  $[21]$  are much larger and cheaper to collect. For example, recent imagetext datasets are generally at billion scale [\[20\]](#page-17-19), [\[21\]](#page-17-20), [\[83\]](#page-18-28). Beyond image-text datasets, several studies [\[19\]](#page-17-18), [\[43\]](#page-17-42), [\[45\]](#page-17-44), [\[67\]](#page-18-8) utilize auxiliary datasets to provide additional information for better vision-language modelling, e.g., GLIP [\[67\]](#page-18-8) leverages Object365 [\[85\]](#page-18-15) for extracting region-level features. The details of image-text datasets and auxiliary datasets for VLM pre-training are provided in Appendix B.

# **4.2 Datasets for VLM Evaluation**

Many datasets have been adopted in VLM evaluations as shown in Table [2,](#page-7-0) including 27 for image classification, 4 for object detection, 4 for semantic segmentation, 2 for imagetext retrieval, and 3 for action recognition (dataset details provided in Appendix C). For example, the 27 image classification datasets cover a wide range of visual recognition tasks from fine-grained tasks like Oxford-IIIT PETS [\[26\]](#page-17-25) for pet identification and Stanford Cars [\[25\]](#page-17-24) for car recognition, to general tasks like ImageNet [\[40\]](#page-17-39).

# <span id="page-6-1"></span>**5 VISION-LANGUAGE MODEL PRE-TRAINING**

VLM pre-training has been explored with three typical objectives: contrastive objectives, generative objectives and alignment objectives. This section reviews them with multiple VLM pre-training studies as listed in Table [3.](#page-8-0)

### 5.1.1 VLM Pre-Training with Contrastive Objectives

Contrastive learning has been widely explored in VLM pretraining, which designs contrastive objectives for learning discriminative image-text features [\[10\]](#page-17-9), [\[64\]](#page-18-5), [\[113\]](#page-18-31).

#### *5.1.1 Image Contrastive Learning*

This pre-training objective aims to learn discriminative features in image modality, which often serves as an auxiliary objective for fully exploiting the image data potential. For example, SLIP [\[64\]](#page-18-5) employs a standard infoNCE loss defined in Eq. [1](#page-4-4) for learning discriminative image features.

# *5.1.2 Image-Text Contrastive Learning*

Image-text contrast aims to learn vision-language correlation by contrasting image-text pairs, i.e., pulling the embeddings of paired images and texts close while pushing others faraway [\[10\]](#page-17-9). For example, CLIP [\[10\]](#page-17-9) employs a symmetrical image-text infoNCE loss in Eq. [2](#page-4-0) which measures the imagetext similarity by a dot-product between image and text embeddings in Fig. [6.](#page-6-3) The pre-trained VLM hence learns image-text correlation which allows zero-shot predictions in downstream visual recognition tasks.

<span id="page-6-3"></span>Image /page/6/Figure/18 description: This diagram illustrates a multimodal learning approach. On the top, input texts, such as 'A running puppy ...', are processed by a Text Encoder, resulting in a series of text embeddings denoted as z1^T, z2^T, ..., zB^T. On the bottom, input images are fed into an Image Encoder, producing image embeddings z1^I, z2^I, ..., zB^I. These image embeddings are then used to compute a similarity matrix where each element represents the dot product of an image embedding with a text embedding (e.g., z1^I \* z1^T, z1^I \* z2^T, etc.). The matrix is B x B in size, with some cells highlighted in gray.

Fig. 6: Illustration of the image-text contrastive learning in CLIP [\[10\]](#page-17-9). Figure is reproduced from [\[10\]](#page-17-9).

Inspired by the great success of CLIP, many studies improve the symmetrical image-text infoNCE loss from different perspectives. For example, ALIGN [\[17\]](#page-17-16) scales up the VLM pre-training with large-scale  $(i.e., 1.8)$  billions) but noisy image-text pairs with noise-robust contrastive learning. Several studies [\[112\]](#page-18-32), [\[113\]](#page-18-31), [\[114\]](#page-18-33) instead explore dataefficient VLM pre-training with much less image-text pairs.

## JOURNAL OF L<sup>o</sup>TEX CLASS FILES,MARCH 2023 8 A 2009 1999 1999 1999 1999 1999 1999 1999

<span id="page-7-0"></span>TABLE 2: Summary of the widely-used visual recognition datasets for VLM evaluation. [link] directs to dataset websites.

| Task                       | Dataset                                     | Year | Classes | Training  | Testing  | Evaluation Metric |
|----------------------------|---------------------------------------------|------|---------|-----------|----------|-------------------|
| Image Classification       | MNIST [88] [link]                           | 1998 | 10      | 60,000    | 10,000   | Accuracy          |
|                            | Caltech-101 [89] [link]                     | 2007 | 102     | 3,060     | 6,085    | Mean Per Class    |
|                            | PASCAL VOC 2007 Classification [90] [link]  | 2008 | 20      | 5,011     | 4,952    | 11-point mAP      |
|                            | Oxford 102 Folwers [91] [link]              | 2008 | 102     | 2,040     | 6,149    | Mean Per Class    |
|                            | CIFAR-10 [23] [link]                        | 2009 | 10      | 50,000    | 10,000   | Accuracy          |
|                            | CIFAR-100 [23] [link]                       | 2009 | 100     | 50,000    | 10,000   | Accuracy          |
|                            | ImageNet-1k [40] [link]                     | 2009 | 1000    | 1,281,167 | 50,000   | Accuracy          |
|                            | SUN397 [24] [link]                          | 2010 | 397     | 19,850    | 19,850   | Accuracy          |
|                            | SVHN [92] [link]                            | 2011 | 10      | 73,257    | 26,032   | Accuracy          |
|                            | STL-10 [93] [link]                          | 2011 | 10      | 1,000     | 8,000    | Accuracy          |
|                            | GTSRB [94] [link]                           | 2011 | 43      | 26,640    | 12,630   | Accuracy          |
|                            | KITTI Distance [1] [link]                   | 2012 | 4       | 6,770     | 711      | Accuracy          |
|                            | IIIT5k [95] [link]                          | 2012 | 36      | 2,000     | 3,000    | Mean Per Class    |
|                            | Oxford-IIIT PETS [26] [link]                | 2012 | 37      | 3,680     | 3,669    | Accuracy          |
|                            | Stanford Cars [25] [link]                   | 2013 | 196     | 8,144     | 8,041    | Mean Per Class    |
|                            | FGVC Aircraft [96] [link]                   | 2013 | 100     | 6,667     | 3,333    | Accuracy          |
|                            | Facial Emotion Recognition 2013 [97] [link] | 2013 | 8       | 32,140    | 3,574    | Accuracy          |
|                            | Rendered SST2 [98] [link]                   | 2013 | 2       | 7,792     | 1,821    | Accuracy          |
|                            | Describable Textures (DTD) [99] [link]      | 2014 | 47      | 3,760     | 1,880    | Accuracy          |
|                            | Food-101 [22] [link]                        | 2014 | 102     | 75,750    | 25,250   | Accuracy          |
|                            | Birdsnap [100] [link]                       | 2014 | 500     | 42,283    | 2,149    | Accuracy          |
|                            | RESISC45 [101] [link]                       | 2017 | 45      | 3,150     | 25,200   | Accuracy          |
| CLEVR Counts [102] [link]  | 2017                                        | 8    | 2,000   | 500       | Accuracy |                   |
| PatchCamelyon [103] [link] | 2018                                        | 2    | 294,912 | 32,768    | Accuracy |                   |
| EuroSAT [104] [link]       | 2019                                        | 10   | 10,000  | 5,000     | Accuracy |                   |
| Hateful Memes [27] [link]  | 2020                                        | 2    | 8,500   | 500       | ROC AUC  |                   |
| Country211 [10] [link]     | 2021                                        | 211  | 43,200  | 21,100    | Accuracy |                   |
| Image-Text Retrieval       | Flickr30k [105] [link]                      | 2014 | -       | 31,783    | -        | Recall            |
|                            | COCO Caption [74] [link]                    | 2015 | -       | 82,783    | 5,000    | Recall            |
| Action Recognition         | UCF101 [29] [link]                          | 2012 | 101     | 9,537     | 1,794    | Accuracy          |
|                            | Kinetics700 [30] [link]                     | 2019 | 700     | 494,801   | 31,669   | Mean(top1, top5)  |
|                            | RareAct [28] [link]                         | 2020 | 122     | 7,607     | -        | mAP, mSAP         |
| Object Detection           | COCO 2014 Detection [106] [link]            | 2014 | 80      | 83,000    | 41,000   | box mAP           |
|                            | COCO 2017 Detection [106] [link]            | 2017 | 80      | 118,000   | 5,000    | box mAP           |
|                            | LVIS [107] [link]                           | 2019 | 1203    | 118,000   | 5,000    | box mAP           |
|                            | ODinW [108] [link]                          | 2022 | 314     | 132413    | 20070    | box mAP           |
| Semantic Segmentation      | PASCAL VOC 2012 Segmentation [90] [link]    | 2012 | 20      | 1464      | 1449     | mIoU              |
|                            | PASCAL Content [109] [link]                 | 2014 | 459     | 4998      | 5105     | mIoU              |
|                            | Cityscapes [110] [link]                     | 2016 | 19      | 2975      | 500      | mIoU              |
|                            | ADE20k [111] [link]                         | 2017 | 150     | 25574     | 2000     | mIoU              |

For example, DeCLIP [\[113\]](#page-18-31) introduces nearest-neighbor supervision to utilize the information from similar pairs, enabling effective pre-training on limited data. OTTER [\[112\]](#page-18-32) employs optimal transport to pseudo-pair images and texts reducing the required training data greatly. ZeroVL [\[114\]](#page-18-33) exploits limited data resource via debiased data sampling and data augmentation with coin flipping mixup.

Another line of follow-up studies [\[18\]](#page-17-17), [\[116\]](#page-18-56), [\[129\]](#page-19-0) aim for comprehensive vision-language correlation modelling by performing image-text contrastive learning across various semantic levels. For example, FILIP [\[18\]](#page-17-17) introduces region-word alignment into contrastive learning, enabling to learn fine-grained vision-language corresponding knowledge. PyramidCLIP [\[116\]](#page-18-56) constructs multiple semantic levels and performs both cross-level and peer-level contrastive learning for effective VLM pre-training.

Besides, several recent studies further improve by augmenting image-text pairs [\[125\]](#page-19-1), [\[126\]](#page-19-2), [\[127\]](#page-19-3), [\[128\]](#page-19-4). For example, LA-CLIP [\[126\]](#page-19-2) and ALIP [\[127\]](#page-19-3) employ large language models to augment synthetic captions for given images while RA-CLIP [\[125\]](#page-19-1) retrieves relevant image-text pairs for image-text pair augmentation. To facilitate efficient communications across data modalities, [\[44\]](#page-17-43) and [\[43\]](#page-17-42) attempt to unify vision and language learning in a single encoder.

#### *5.1.3 Image-Text-Label Contrastive Learning*

This type of pre-training introduces image classification labels [\[65\]](#page-18-6) into the image-text contrast as defined in Eq. [4,](#page-4-2) which encodes image, text and classification labels into a shared space as shown in Fig. [7.](#page-8-1) It exploits both supervised pre-training with image labels and unsupervised VLM pretraining with image-text pairs. As reported in UniCL [\[65\]](#page-18-6), such pre-training allows learning both discriminative and task-specific (i.e., image classification) features simultaneously. The ensuing work in [\[115\]](#page-18-57) scales UniCL with around 900M image-text pairs, leading to outstanding performance in various downstream recognition tasks.

#### *5.1.4 Discussion*

Contrastive objectives enforce positive pairs to have similar embeddings in contrast to negative pairs. They encourage VLMs to learn discriminative vision and language features [\[10\]](#page-17-9), [\[17\]](#page-17-16), where more discriminative features generally lead to more confident and accurate zero-shot predictions. However, the contrastive objective has two limitations: (1) Joint optimizing positive and negative pairs is complicated and challenging  $[10]$ ,  $[17]$ ;  $(2)$  it involves a heuristic temperature hyper-parameter for controlling the feature discriminability as described in Sec. [3.2.1.](#page-4-5)

#### JOURNAL OF LATEX CLASS FILES, MARCH 2023 AND A SERVICE STOLEN ASSESSED FOR A SERVICE STOLEN ASSESSED.

<span id="page-8-0"></span>TABLE 3: Summary of vision-language model pre-training methods. Con: Contrastive Objective; Gen: Generative Objective; Align: Alignment Objective.  $\dagger$ ,  $\dagger$  and  $\S$  denote two-tower, two-leg and one-tower pre-training frameworks, respectively.  $*$ denotes non-public datasets. [code] directs to code websites.

| Method                    | Dataset                                                 | Objective       | Contribution                                                                                                                                |
|---------------------------|---------------------------------------------------------|-----------------|---------------------------------------------------------------------------------------------------------------------------------------------|
| CLIP† [10] [code]         | $CLIP*$                                                 | Con             | Propose image-text contrastive learning for VLM pre-training.                                                                               |
| ALIGN† [17]               | $ALIGN*$                                                | Con             | Leverage large-scale noisy data to scale-up VLM pre-training data.                                                                          |
| OTTER† [112] [code]       | CC3M, YFCC15M, WIT                                      | Con             | Employ optimal transport for data efficient VLM pre-training.                                                                               |
| DeCLIP† [113] [code]      | CC3M, CC12M, YFCC100M, WIT*                             | Con, Gen        | Employ image/text self-supervision for data efficient VLM pre-training.                                                                     |
| ZeroVL† [114] [code]      | SBU, VG, CC3M, CC12M                                    | Con             | Introduce data augmentation for data-efficient VLM pre-training.                                                                            |
| FILIP†[18] [code]         | FILIP*, CC3M, CC12M, YFCC100M                           | Con, Align      | Leverage region-word similarity for fine-grained VLM pre-training.                                                                          |
| UniCL† [65] [code]        | CC3M, CC12M, YFCC100M                                   | Con             | Propose image-text-label contrastive learning for VLM pre-training.                                                                         |
| Florence† [115]           | FLD-900M*                                               | Con             | Scale up pre-training data and include depth and temporal information.                                                                      |
| SLIP† [64] [code]         | YFCC100M                                                | Con             | Introduce image self-supervision learning into VLM pre-training.                                                                            |
| PyramidCLIP†[116]         | SBU, CC3M, CC12M, YFCC100M,<br>LAION400M                | Con             | Perform peer-level/cross-level contrastive learning within/across mul-<br>tiple semantic levels.                                            |
| ChineseCLIP† [117] [code] | LAION5B, WuKong, VG, COCO                               | Con             | Collect large-scale Chinese image-text data and Introduce Chinese VLM.                                                                      |
| LiT† [118] [project]      | CC12M, YFCC100M, WIT*                                   | Con             | Propose contrastive tuning with the locked image encoder.                                                                                   |
| AltCLIP† [119] [code]     | WuDao, LAION2B, LAION5B                                 | Con             | Leverage the multilingual text encoder to achieve multilingual VLM.                                                                         |
| FLAVA‡ [42] [code]        | COCO, SBU, LN, CC3M, VG, WIT,<br>CC12M, RC, YFCC100M    | Gen,Con,Align   | Propose a universal and foundational VLM that tackles the single-modal<br>(i.e., image or text) and the multi-model cases at the same time. |
| KELIP† [120] [code]       | CUB200, WIT, YFCC15M, CC3M,<br>CC12M, LAION400M, K-WIT* | Con, Gen        | Collect large-scale Korean image-text pair data and develop bilingual<br>VLMs with Korean and English.                                      |
| COCA‡ [19] [code]         | $ALIGN*$                                                | Con, Gen        | Combine contrastive learning and image captioning for pre-training.                                                                         |
| nCLIP† [121]              | COCO, VG, SBU, CC3M, CC12M,<br>YFCC14M                  | Con, Align      | Propose a non-contrastive pre-training objective (i.e., a cross-entropy<br>loss for global image-text matching) for VLM pre-training.       |
| K-lite† [122] [code]      | CC3M, CC12M, YFCC100M                                   | Con             | Leverage auxiliary datasets for training transferable VLMs.                                                                                 |
| NLIP‡ [123]               | YFCC100M, COCO                                          | Con, Gen        | Train noise-robust VLM via noise harmonization and completion.                                                                              |
| UniCLIP† [84]             | CC3M, CC12M, YMCC100M                                   | Con             | Propose unified image-text and image-image contrastive learning.                                                                            |
| PaLI‡ [83] [project]      | WebLI*                                                  | Gen             | Scale up the data, model and language in VLM pre-taring.                                                                                    |
| HiCLIP† [124] [code]      | YFCC100M, CC3M, CC12M                                   | Con             | Propose to incorporate hierarchy-aware attention into VLM pre-training.                                                                     |
| CLIPPO§ [43] [code]       | WebLI*                                                  | Con             | Learn image and text data with a single network for VLM pre-training.                                                                       |
| OneR§ [44]                | CC3M, SBU, VG, COCO                                     | Con, Gen        | Unify image and text learning in a single tower transformer.                                                                                |
| RA-CLIP† [125]            | YFCC100M                                                | Con             | Propose retrieval-augmented image-text contrastive learning.                                                                                |
| LA-CLIP† [126] [code]     | CC3M, CC12M, RC, LAION400M                              | Con             | Propose LLMs-augmented image-text contrastive learning.                                                                                     |
| ALIP† [127] [code]        | YFCC100M                                                | Con             | Introduce synthetic caption supervision into VLM pre-training.                                                                              |
| GrowCLIP‡[128]            | CC12M                                                   | Con             | Propose online-learning image-text contrastive learning.                                                                                    |
| GroupVit† [129] [code]    | CC12M, YMCC100M                                         | Con             | Propose hierarchical visual concepts grouping for VLM pre-training.                                                                         |
| SegClip†[46] [code]       | CC3M, COCO                                              | Con, Gen        | Propose a plug-in semantic group module for VLM pre-training.                                                                               |
| CLIPpy† [130] [code]      | CC12M                                                   | Con             | Propose spatial representation aggregation for VLM pre-training.                                                                            |
| RegionClip† [131] [code]  | CC3M, COCO                                              | Con, Align      | Learn region-level visual representations for VLM pre-training.                                                                             |
| GLIP‡ [67] [code]         | CC3M, CC12M, SBU                                        | Align           | Unify detection and phrase grounding for grounded VLM pre-training.                                                                         |
| FIBER‡ [71] [code]        | COCO, CC3M, SBU, VG<br>YMCC100M                         | Con, Gen, Align | Propose deep multi-modal fusion for coarse-to-fine VLM pre-training.                                                                        |
| DetCLIP†[45]              |                                                         | Align           | Present a paralleled visual-concept VLM pre-training method.                                                                                |

<span id="page-8-1"></span>Image /page/8/Figure/3 description: The image displays a diagram illustrating different types of data pairs used in machine learning. It is organized into rows for 'Label', 'Image', and 'Text'. The top section shows 'Image-label pairs' with two examples: a dog labeled '[dog]' and a cat labeled '[cat]'. The second section, enclosed in a dashed green border, shows 'Image-text-label pairs'. This includes an image of a running puppy with the text 'A running puppy.' and the label '[dog]', and an image of a cat wearing glasses with the text 'A cat wearing glasses.' and the label '[cat]'. The third section, enclosed in a dashed blue border, shows 'Image-text pairs'. This includes an image of a sleeping cat with the text 'A cat is sleeping.' and the label '[cat]', and an image of a dog playing with a ball with the text 'A dog is playing with a ball.' and the label '[dog]'.

Fig. 7: Illustration of the image-text-label space proposed in UniCL [\[65\]](#page-18-6). Figure is reproduced from [65].

# **5.2 VLM Pre-training with Generative Objectives**

Generative VLM pre-training learns semantic knowledge by learning to generate images or texts via masked image modelling, masked language modelling, masked cross-modal modelling and image-to-text generation.

#### *5.2.1 Masked Image Modelling*

This pre-training objective guides to learn image context information by masking and reconstructing images as defined in Eq. [6.](#page-4-6) In Masked Image Modelling (e.g., MAE [\[41\]](#page-17-40) and BeiT [\[70\]](#page-18-11)), certain patches in an image are masked and the encoder is trained to reconstruct them conditioned on unmasked patches as shown in Fig. [8.](#page-8-2) For example, FLAVA [\[42\]](#page-17-41) adopts rectangular block masking as in BeiT [\[70\]](#page-18-11), while KELIP [\[120\]](#page-18-61) and SegCLIP [\[46\]](#page-17-45) follow MAE to mask out a large portion of patches  $(i.e., 75\%)$  in training.

<span id="page-8-2"></span>Image /page/8/Figure/10 description: This is a diagram illustrating an image processing pipeline. The pipeline starts with an 'Input Image' which is divided into a grid of smaller squares, some of which are grayed out. These squares are then fed into an 'Image Encoder'. The encoder processes the input and outputs a series of orange and gray squares, represented as columns. These processed features are then passed to an 'Image Decoder'. The decoder reconstructs the image, outputting a series of green squares, also represented as columns. Finally, these green squares are assembled to form the 'Output Image', which appears to be a complete and reconstructed version of the input image, showing a building against a sky background.

Fig. 8: Illustration of masked image modelling [\[66\]](#page-18-7). Figure is reproduced from [\[66\]](#page-18-7).

## *5.2.2 Masked Language Modelling*

Masked language modelling, a widely-adopted pre-training objective in NLP as defined in Eq. [7,](#page-5-1) also demonstrates its effectiveness in text feature learning in VLM pre-training. It works by masking a fraction of tokens in each input text and training networks to predict the masked tokens as illustrated in Fig. [9.](#page-9-0) Following  $[14]$ , FLAVA  $[42]$  masks out 15% text tokens and reconstructs them from the rest tokens for modelling cross-word correlation. FIBER [\[71\]](#page-18-12) adopts masked language modelling [\[14\]](#page-17-13) as one of the VLM pretraining objectives to extract better language features.

<span id="page-9-0"></span>Image /page/9/Figure/1 description: This diagram illustrates the process of a Transformer Encoder. The input text 'the cat is very cute !' is processed through token and position embeddings. Each word is represented by a combination of its token embedding (e.g., e\_the, e\_cat) and its position embedding (e.g., e0, e1). One word, 'cute', is masked (e\_[mask]). These combined embeddings are fed into the Transformer Encoder. The output of the encoder is a sequence of predictions, which in this case are the original words: 'the', 'cat', 'is', 'very', 'cute', and '!'. The word 'cute' is highlighted in orange in both the input and prediction stages, indicating it was the masked word.

Fig. 9: Illustration of masked language modelling [\[14\]](#page-17-13).

### 5.2.3 Masked Cross-Modal Modelling

Masked cross-modal modelling masks and reconstructs both image patches and text tokens jointly as defined in Eq. [8,](#page-5-2) which inherits the benefits of both masked image modelling and masked language modelling. It works by masking a certain percentage of image patches and text tokens and training VLMs to reconstruct them based on the embeddings of unmasked image patches and text tokens. For example, FLAVA [\[42\]](#page-17-41) masks ∼40% image patches as in [\[70\]](#page-18-11) and 15% text tokens as in [\[14\]](#page-17-13), and then employs a MLP to predict masked patches and tokens, capturing rich vision-language correspondence information.

#### *5.2.4 Image-to-Text Generation*

Image-to-text generation aims to generate descriptive texts for a given image for capturing fine-grained vision-language correlation by training VLMs to predict tokenized texts. It first encodes an input image into intermediate embeddings and then decodes them into descriptive texts as defined in Eq. [9.](#page-5-3) For instance, COCA [\[19\]](#page-17-18), NLIP [\[123\]](#page-18-64) and PaLI [\[83\]](#page-18-28) train VLMs with the standard encoder-decoder architecture and image captioning objectives as shown in Fig. [10.](#page-9-1)

<span id="page-9-1"></span>Image /page/9/Figure/7 description: This is a diagram illustrating a model for image captioning. The input image of a dog playing with a ball is processed by an Image Encoder, which outputs a sequence of feature vectors. These vectors are then fed into a Multi-modal Text Decoder. The input text, "A dog is playing with a ball", is processed by a Uni-modal Text Encoder. The diagram shows a cross-attention mechanism connecting the output of the Image Encoder to the Multi-modal Text Decoder. The Multi-modal Text Decoder generates the caption "a dog playing with a ball", and a "Captioning Loss" is calculated based on this output.

Fig. 10: A simplified illustration of image-to-caption generation in COCA [\[19\]](#page-17-18). Figure is reproduced based on [\[19\]](#page-17-18).

# *5.2.5 Discussion*

Generative objectives work by cross-modal generation or masked image/language/cross-modal modelling, encouraging VLMs to learn rich vision, language and visionlanguage contexts for better zero-shot predictions. Hence, generative objectives are generally adopted as additional objectives above other VLM pre-training objectives for learning rich context information [\[19\]](#page-17-18), [\[42\]](#page-17-41), [\[113\]](#page-18-31).

### 5.3.1 VLM Pre-training with Alignment Objectives

Alignment objectives enforce VLMs to align paired images and texts by learning to predict whether the given text describes the given image correctly. It can be broadly categorized into global image-text matching and local region-word matching for VLM pre-training.

### *5.3.1 Image-Text Matching*

Image-text matching models global image-text correlation by directly aligning paired images and texts as defined in Eq. [10.](#page-5-4) For example, given a batch of image-text pairs, FLAVA [\[42\]](#page-17-41) matches the given image with its paired text via a classifier and a binary classification loss. FIBER [\[71\]](#page-18-12) follows [\[72\]](#page-18-13) to mine hard negatives with pair-wise similarities for better alignment between images and texts.

<span id="page-9-2"></span>Image /page/9/Figure/15 description: This is a diagram illustrating a deep fusion model for visual grounding. The model takes prompted text, such as "A person is eating hot dogs and holding a cup," and an image as input. A text encoder processes the text, and an image encoder processes the image. The outputs of these encoders are then combined through a deep fusion module. The model generates word embeddings (w1 to wL) and region embeddings (r1 to rK). A word-region alignment score matrix is computed, showing the similarity between each word embedding and each region embedding. The diagram indicates that localization loss and alignment loss are used to train the model.

Fig. 11: Illustration of GLIP [\[67\]](#page-18-8) that uses word-region alignment for detection. Figure is reproduced from [\[67\]](#page-18-8).

#### *5.3.2 Region-Word Matching*

Region-word matching objective models local fine-grained vision-language correlation by aligning paired image regions and word tokens, greatly benefiting zero-shot dense predictions in object detection and semantic segmentation. For example, GLIP [\[67\]](#page-18-8), FIBER [\[71\]](#page-18-12) and DetCLIP [\[45\]](#page-17-44) replace object classification logits by region-word alignment scores,  $i.e.,$  the dot-product similarity between regional visual features and token-wise features as illustrated in Fig. [11.](#page-9-2)

#### *5.3.3 Discussion*

Alignment objectives learn to predict weather the given image and text data are matched or not, which are simple and easy-to-optimize and can be easily extended to model fine-grained vision-language correlation by matching image and text data locally. On the other hand, they often learn little correlation information within vision or language modality. Therefore, alignment objectives are often adopted as auxiliary losses to other VLM pre-training objectives for enhancing modelling the correlation across vision and language modalities [\[42\]](#page-17-41), [\[121\]](#page-18-62).

#### **5.4 Summary and Discussion**

In summary, VLM pre-training models the vision-language correlation with different cross-modal objectives such as image-text contrastive learning, masked cross-modal modelling, image-to-text generation and image-text/regionword matching. Various single-modal objectives have also been explored for fully exploiting the data potential of its own modality, such as masked image modelling for

<span id="page-10-2"></span>TABLE 4: Summary of VLM transfer learning methods. TPT: text-prompt tuning; VPT: visual-prompt tuning; FA: feature adapter; CA: cross-attention; FT: fine-tuning; AM: architecture modification; LLM: large-language model. [code] directs to code websites.

| Method                  | Category                | Setup         | Contribution                                                                                 |
|-------------------------|-------------------------|---------------|----------------------------------------------------------------------------------------------|
| CoOp [31] [code]        | <b>TPT</b>              | Few-shot Sup. | Introduce context optimization with learnable text prompts for VLM transfer learning.        |
| CoCoOp [32] [code]      | <b>TPT</b>              | Few-shot Sup. | Propose conditional text prompting to mitigate overfitting in VLM transfer learning.         |
| SubPT [132] [code]      | <b>TPT</b>              | Few-shot Sup. | Propose subspace text prompt tuning to mitigate overfitting in VLM transfer learning.        |
| LASP [133] [code]       | <b>TPT</b>              | Few-shot Sup. | Propose to regularize the learnable text prompts with the hand-engineered prompts.           |
| ProDA [134]             | <b>TPT</b>              | Few-shot Sup. | Propose prompt distribution learning that captures the distribution of diverse text prompts. |
| VPT [135]               | <b>TPT</b>              | Few-shot Sup. | Propose to model the text prompt learning with instance-specific distribution.               |
| ProGrad [136] [code]    | <b>TPT</b>              | Few-shot Sup. | Present a prompt-aligned gradient technique for preventing knowledge forgetting.             |
| CPL [137] [code]        | <b>TPT</b>              | Few-shot Sup. | Employ counterfactual generation and contrastive learning for text prompt tuning.            |
| PLOT [138] [code]       | <b>TPT</b>              | Few-shot Sup. | Introduce optimal transport to learn multiple comprehensive text prompts.                    |
| DualCoOp [139] [code]   | <b>TPT</b>              | Few-shot Sup. | Introduce positive and negative text prompt learning for multi-label classification.         |
| TaI-DPT [140] [code]    | <b>TPT</b>              | Few-shot Sup. | Introduce a double-grained prompt tuning technique for multi-label classification            |
| SoftCPT [141] [code]    | <b>TPT</b>              | Few-shot Sup. | Propose to fine-tune VLMs on multiple downstream tasks simultaneously.                       |
| DenseClip [142] [code]  | <b>TPT</b>              | Supervised    | Propose a language-guided fine-tuning technique for dense visual recognition tasks.          |
| UPL [143] [code]        | <b>TPT</b>              | Unsupervised  | Propose unsupervised prompt learning with self-training for VLM transfer learning.           |
| TPT [144] [code]        | <b>TPT</b>              | Unsupervised  | Propose test-time prompt tuning that learns adaptive prompts on the fly.                     |
| KgCoOp [145] [code]     | <b>TPT</b>              | Few-shot Sup. | Introduce knowledge-guided prompt tuning to improve the generalization ability.              |
| ProTeCt [146]           | <b>TPT</b>              | Few-shot Sup. | Propose a prompt tuning technique to improve consistency of model predictions.               |
| VP [147] [code]         | <b>VPT</b>              | Supervised    | Investigate the efficacy of visual prompt tuning for VLM transfer learning.                  |
| RePrompt [148]          | <b>VPT</b>              | Few-shot Sup. | Introduce retrieval mechanisms to leverage knowledge from downstream tasks.                  |
| UPT [149] [code]        | <b>TPT</b> , <b>VPT</b> | Few-shot Sup. | Propose a unified prompt tuning that jointly optimizes text and image prompts.               |
| MVLPT [150][code]       | <b>TPT</b> , <b>VPT</b> | Few-shot Sup. | Incorporate multi-task knowledge into text and image prompt tuning.                          |
| MaPLE [151][code]       | <b>TPT</b> , <b>VPT</b> | Few-shot Sup. | Propose multi-modal prompt tuning with a mutual promotion strategy.                          |
| CAVPT [152][code]       | <b>TPT</b> , <b>VPT</b> | Few-shot Sup. | Introduce class-aware visual prompt for concentrating more on visual concepts.               |
| Clip-Adapter [33][code] | <b>FA</b>               | Few-shot Sup. | Introduce an adapter with residual feature blending for efficient VLM transfer learning.     |
| Tip-Adapter [34][code]  | <b>FA</b>               | Few-shot Sup. | Propose to build a training-free adapter with the embeddings of few labelled images.         |
| SVL-Adpter [153][code]  | <b>FA</b>               | Few-shot Sup. | Introduce a self-supervised adapter by performing self-supervised learning on images.        |
| SuS-X [154][code]       | <b>FA</b>               | Unsupervised  | Propose a training-free name-only transfer learning paradigm with curated support sets.      |
| CLIPPR [155][code]      | <b>FA</b>               | Unsupervised  | Leverage the label distribution priors for adapting pre-trained VLMs.                        |
| SgVA-CLIP [156]         | <b>TPT</b> , <b>FA</b>  | Few-shot Sup. | Propose a semantic-guided visual adapter to generate discriminative adapted features.        |
| VT-Clip [157]           | <b>CA</b>               | Few-shot Sup. | Introduce visual-guided attention that semantically aligns text and image features.          |
| CALIP [158] [code]      | <b>CA</b>               | Unsupervised  | Propose parameter-free attention for the communication between visual and textual features.  |
| TaskRes [159] [code]    | <b>CA</b>               | Few-shot Sup. | Propose a technique for better learning old VLM knowledge and new task knowledge.            |
| CuPL [160]              | <b>LLM</b>              | Unsupervised  | Employ large language models to generate customized prompts for VLMs.                        |
| VCD [161]               | <b>LLM</b>              | Unsupervised  | Employ large language models to generate captions for VLMs.                                  |
| Wise-FT [162][code]     | <b>FT</b>               | Supervised    | Propose ensemble-based fine-tuning by combining the fine-tuned and original VLMs.            |
| MaskClip [163][code]    | <b>AM</b>               | Unsupervised  | Propose to extract dense features by modifying the image encoder architecture.               |
| MUST [164][code]        | <b>Self-training</b>    | Unsupervised  | Propose masked unsupervised self-training for unsupervised VLM transfer learning.            |

image modality and masked language modelling for text modality. At the other end, recent VLM pre-training focuses on learning global vision-language correlation with benefits in image-level recognition tasks such as image classification. Meanwhile, several studies [\[45\]](#page-17-44), [\[46\]](#page-17-45), [\[67\]](#page-18-8), [\[71\]](#page-18-12), [\[129\]](#page-19-0), [\[130\]](#page-19-6), [\[131\]](#page-19-7) model local fine-grained vision-language correlation via region-word matching, aiming for better dense predictions in object detection and semantic segmentation.

# <span id="page-10-0"></span>**6 VLM TRANSFER LEARNING**

Beyond *zero-shot prediction* that directly applies pre-trained VLMs on downstream tasks without fine-tuning, transfer learning has been studied recently which adapts VLMs to fit downstream tasks via prompt tuning [\[31\]](#page-17-30), [\[132\]](#page-19-8), feature adapter  $[33]$ ,  $[34]$ , etc. This section presents the motivation of transfer learning for pre-trained VLMs, the common transfer-learning setup, and three transfer learning approaches including prompt tuning methods, feature adapter methods and other methods.

## <span id="page-10-1"></span>**6.1 Motivation of Transfer learning**

Although pre-trained VLMs have demonstrated strong generalization capability, they often face two types of gaps while applied to various downstream tasks: 1) the gaps in image and text distributions, e.g., an downstream dataset may have task-specific image styles and text formats; 2) the gaps in training objectives, e.g., VLMs are generally trained with task-agnostic objectives and learn general concepts while downstream tasks often involve task-specific objectives such as coarse or fine-grained classification, region or pixel-level recognition, etc.

## **6.2 Common Setup of Transfer Learning**

Three transfer setups have been explored for mitigating the domain gaps described in Sec. [6.1,](#page-10-1) including supervised transfer, few-shot supervised transfer and unsupervised transfer. Supervised transfer employs all labelled downstream data for fine-tuning the pre-trained VLMs, while few-shot supervised transfer is more annotation efficient which just uses a small amount of labelled downstream samples. Differently, unsupervised transfer uses unlabelled downstream data for fine-tuning VLMs. It is thus more challenging but more promising and efficient for VLM transfer.

## **6.3 Common Transfer Learning Methods**

As shown in Table [4,](#page-10-2) we broadly group existing VLM transfer methods into three categories including prompt tuning approaches, feature adapter approaches, and others.

### 6.3.1 Transfer via Prompt Tuning

Inspired by the "prompt learning" in NLP [\[165\]](#page-19-41), many VLM prompt learning methods have been proposed for adapting VLMs to fit downstream tasks by finding optimal prompts without fine-tuning the entire VLM. Most existing studies follow three approaches by text prompt tuning, visual prompt tuning, and text-visual prompt tuning.

<span id="page-11-0"></span>Image /page/11/Figure/1 description: This figure illustrates two methods for image classification. Figure (a) shows a standard approach where an image is fed into an Image Encoder, and text labels like 'dog', 'cat', and 'bird' are processed by a Text Encoder. Both encoders contribute to a Classification Loss. Figure (b) introduces a visual prompting method. An input image is combined with a 'Learnable Visual Prompt' to create a 'Visual Prompted Image', which is then processed by the Image Encoder. Simultaneously, text labels are used to construct a 'Text Prompt' like "This is a photo of [label].", which is processed by the Text Encoder. Both encoders again contribute to a Classification Loss.

Fig. 12: Illustration of text prompt learning [\[31\]](#page-17-30) in (a) and visual prompt learning [\[147\]](#page-19-23) in (b).

**Transfer with Text Prompt Tuning.** Different from prompt engineering [\[165\]](#page-19-41) that manually designs text prompts for each task, text prompt tuning explores more effective and efficient learnable text prompts with several labelled downstream samples for each class. For example, CoOp [\[31\]](#page-17-30) explores context optimization to learn context words for a single class name with learnable word vectors. It expands a category word [label] into a sentence '[V]<sub>1</sub>, [V]<sub>2</sub>, ..., [V]<sub>m</sub> [label]', where [V] denotes the learnable word vectors that are optimized by minimizing the classification loss with the downstream samples as shown in Fig. [12](#page-11-0) (a). To mitigate the overfitting due to limited downstream samples in prompt learning, CoCoOp [\[32\]](#page-17-31) explores conditional context optimization that generates a specific prompt for each image. SubPT [\[132\]](#page-19-8) designs subsapce prompt tuning to improve the generalization of learned prompts. LASP [\[133\]](#page-19-9) regularizes learnable prompts with hand-engineered prompts. VPT [\[135\]](#page-19-11) models text prompts with instance-specific distribution with better generalization on downstream tasks. KgCoOp [\[145\]](#page-19-21) enhances the generalization of unseen class by mitigating the forgetting of textual knowledge.

In addition, SoftCPT [\[141\]](#page-19-17) fine-tunes VLMs on multiple few-shot tasks simultaneously for benefiting from multitask learning. PLOT [\[138\]](#page-19-14) employs optimal transport to learn multiple prompts to describe the diverse characteristics of a category. DualCoOp  $[139]$  and TaI-DP  $[140]$  transfer VLMs to multi-label classification tasks, where Dual-CoOp adopts both positive and negative prompts for multilabel classification while TaI-DP introduces double-grained prompt tuning for capturing both coarse-grained and finegrained embeddings. DenseCLIP [\[142\]](#page-19-18) explores languageguided fine-tuning that employs visual features to tune text prompts for dense prediction [\[55\]](#page-17-54), [\[56\]](#page-17-55). ProTeCt [\[146\]](#page-19-22) improves the consistency of model predictions for hierarchical classification task.

Beyond supervised and few-shot supervised prompt learning, recent studies explore unsupervised prompt tuning for better annotation efficiency and scalability. For instance, UPL [\[143\]](#page-19-19) optimizes learnable prompts with selftraining on selected pseudo-labeled samples. TPT [\[144\]](#page-19-20) explores test-time prompt tuning to learn adaptive prompts from a single downstream sample.

**Transfer with Visual Prompt Tuning.** Unlike text prompt tuning, visual prompt tuning [\[148\]](#page-19-24), [\[166\]](#page-19-42) transfers VLMs by modulating the input of image encoder as shown in Fig. [12](#page-11-0) (b). For example, VP [\[147\]](#page-19-23) adopts learnable image perturbations v to modify the input image  $x^I$  by  $x^I + v$ , aiming to adjust  $v$  to minimize a recognition loss. RePrompt [\[148\]](#page-19-24) integrates retrieval mechanisms into visual prompt tuning, allowing leveraging the knowledge from downstream tasks. Visual prompt tuning enables pixel-level adaptation to downstream tasks, benefiting them greatly especially for dense prediction tasks.

**Transfer with Text-Visual Prompt Tuning** aims to modulate the text and image inputs simultaneously, benefiting from joint prompt optimization on multiple modalities. For ex-ample, UPT [\[149\]](#page-19-25) unifies prompt tuning to jointly optimize text and image prompts, demonstrating the complementary nature of the two prompt tuning tasks. MVLPT [\[150\]](#page-19-26) explores multi-task vision-language prompt tuning to incorporate cross-task knowledge into text and image prompt tuning. MAPLE [\[151\]](#page-19-27) conducts multi-modal prompt tuning by aligning visual prompts with their corresponding language prompts, enabling a mutual promotion between text prompts and image prompts. CAVPT [\[152\]](#page-19-28) introduces a cross attention between class-aware visual prompts and text prompts, encouraging the visual prompts to concentrate more on visual concepts.

<span id="page-11-1"></span>Image /page/11/Figure/10 description: This diagram illustrates a zero-shot image classification model. The model takes an image and a text prompt as input. The image is processed by an Image Encoder to extract visual features. The text prompt, which is a template like "This is a photo of [label].", is used with different labels (e.g., dog, cat, bird) and processed by a Text Encoder. The visual features are then passed through a Feature Adapter to produce adapted features. Finally, the adapted features are combined with the output of the Text Encoder, and this combined representation is used to calculate the Classification Loss.

Fig. 13: Illustration of feature adapter [\[33\]](#page-17-32).

**Discussion.** Prompt tuning enables parameter-efficient VLM transfer by modifying input texts/images with a few learnable text/image prompts. It is simple and easyto-implement, and requires little extra network layers or complex network modifications. Therefore, prompt tuning allows adapting VLMs in a black-box manner, which has clear advantages in transferring VLMs that involve concerns in intellectual property. However, it still suffers from several limitations such as the low flexibility by following the manifold of the original VLMs in prompting [\[31\]](#page-17-30).

### 6.3.2 Transfer via Feature Adaptation

Feature adaptation fine-tunes VLMs to adapt image or text features with an additional light-weight feature adapter [\[167\]](#page-19-43). For example, Clip-Adapter [\[33\]](#page-17-32) inserts several trainable linear layers after CLIP's language and image encoders and optimizes them while keeping CLIP architecture and parameters frozen as illustrated in Fig. [13.](#page-11-1) Tip-Adapter [\[34\]](#page-17-33) presents a training-free adapter that directly employs the embeddings of few-shot labelled images as the adapter weights. SVL-Adapter [\[153\]](#page-19-29) designs a selfsupervised adapter which employs an additional encoder for self-supervised learning on input images. In summary, feature adapter adapts image and text features to fit VLMs to downstream data, which provides a promising alternative to prompt tuning for VLMs transfer.

**Discussion.** Feature adaptation adapts VLMs by modifying image and text features with an additional light-weight feature adapter. It is flexible and effective as its architecture and

the insertion manner allow tailoring flexibly for different downstream tasks. Therefore, feature adaptation has clear advantages in adapting VLMs to work on very different and complex downstream tasks [\[168\]](#page-19-44), [\[169\]](#page-19-45), [\[170\]](#page-19-46), [\[171\]](#page-19-47). On the other hand, it requires modifying network architecture and thus can not handle VLMs that have concerns in intellectual property.

# *6.3.3 Other Transfer Methods*

Several studies transfer VLMs by direct fine-tuning [\[162\]](#page-19-38), architecture modification [\[163\]](#page-19-39), and cross attention [\[157\]](#page-19-33), [\[158\]](#page-19-34). Specifically, Wise-FT [\[162\]](#page-19-38) combines the weights of a fine-tuned VLM and the original VLM for learning new information from downstream tasks. MaskCLIP [\[163\]](#page-19-39) extracts dense image features by modifying the architecture of the CLIP image encoder. VT-CLIP [\[157\]](#page-19-33) introduces visualguided attention to semantically correlate text features with downstream images, leading to a better transfer performance. CALIP [\[158\]](#page-19-34) introduces parameter-free attention for effective interaction and communication between visual and text features, leading to text-aware image features and visual-guided text features. TaskRes [\[159\]](#page-19-35) directly tunes text-based classifier to exploit the old knowledge in the pre-trained VLM. CuPL [\[160\]](#page-19-36) and VCD [\[161\]](#page-19-37) employ large language models,  $e.g., GPT_3$  [\[172\]](#page-19-48), to augment text prompts for learning rich discriminative text information.

# **6.4 Summary and Discussion**

In summary, prompt tuning and feature adapter are two major approaches for VLM transfer which work by modifying the input text/image and adapting image/text features, respectively. In addition, both approaches introduce very limited parameters while freezing the original VLMs, leading to efficient transfer. Further, while most studies follow few-shot supervised transfer [\[31\]](#page-17-30), [\[32\]](#page-17-31), [\[132\]](#page-19-8), [\[134\]](#page-19-10), recent studies show that unsupervised VLM transfer can achieve competitive performance on various tasks [\[143\]](#page-19-19), [\[144\]](#page-19-20), [\[160\]](#page-19-36), inspiring more research on unsupervised VLM transfer.

# <span id="page-12-0"></span>**7 VLM KNOWLEDGE DISTILLATION**

As VLMs capture generalizable knowledge that covers a wide range of visual and text concepts, several studies explore how to distil the general and robust VLM knowledge while tackling complex dense prediction tasks such as object detection and semantic segmentation. This section presents the motivation of distilling knowledge from VLMs as well as two groups of knowledge distillation studies on the tasks of semantic segmentation and object detection.

# **7.1 Motivation of Distilling Knowledge from VLMs**

Different from VLM transfer that generally keeps the original VLM architecture intact in transfer [\[31\]](#page-17-30), [\[132\]](#page-19-8), [\[136\]](#page-19-12), VLM knowledge distillation distils general and robust VLM knowledge to task-specific models without the restriction of VLM architecture, benefiting task-specific designs while tackling various dense prediction tasks [\[36\]](#page-17-35), [\[173\]](#page-19-49), [\[174\]](#page-19-50). For example, knowledge distillation allows transferring the general VLM knowledge to tackle detection tasks while taking the advantages of state-of-the-art detection architectures such as Faster R-CNN [\[55\]](#page-17-54) and DETR [\[62\]](#page-18-3).

# **7.2 Common Knowledge Distillation Methods**

As VLMs are generally pre-trained with architectures and objectives designed for image-level representation, most VLM knowledge distillation methods focus on transferring image-level knowledge to region- or pixel-level tasks such as object detection and semantic segmentation. Table [5](#page-13-1) shows a list of VLM knowledge distillation methods.

### 7.2.1 Knowledge Distillation for Object Detection

Open-vocabulary object detection [\[193\]](#page-20-0) aims to detect objects described by arbitrary texts, i.e., objects of any categories beyond the base classes. As VLMs like CLIP are trained with billion-scale image-text pairs that cover very broad vocabulary, many studies explore to distill VLM knowledge to enlarge the detector vocabulary. For example, ViLD [\[36\]](#page-17-35) distills VLM knowledge to a two-stage detector whose embedding space is enforced to be consistent with that of CLIP image encoder. Following ViLD, Hi-erKD [\[186\]](#page-19-51) explores hierarchical global-local knowledge dis-tillation, and RKD [\[187\]](#page-19-52) explores region-based knowledge distillation for better aligning region-level and image-level embeddings. ZSD-YOLO [\[198\]](#page-20-1) introduces self-labelling data augmentation for exploiting CLIP for better object detection. OADP [\[201\]](#page-20-2) preserves proposal features while transferring contextual knowledge. BARON [\[200\]](#page-20-3) uses neighborhood sampling to distill a bag of regions instead of individual regions. RO-ViT [\[199\]](#page-20-4) distills regional information from VLMs for open-vocabulary detection.

Another line of research explores VLM distillation via prompt learning [\[165\]](#page-19-41). For example, DetPro [\[37\]](#page-17-36) introduces a detection prompt technique for learning continuous prompt representations for open-vocabulary object detection. PromptDet [\[188\]](#page-19-53) introduces regional prompt learning for aligning word embeddings with regional image embeddings. Additionally, several studies [\[180\]](#page-19-54), [\[181\]](#page-19-55), [\[189\]](#page-19-56), [\[194\]](#page-20-5), [\[197\]](#page-20-6) explore VLM-predicted pseudo labels to improve ob-ject detectors. For example, PB-OVD [\[189\]](#page-19-56) trains object detectors with VLM-predicted pseudo bounding boxes while XPM [\[194\]](#page-20-5) introduces a robust cross-modal pseudo-labeling strategy that employs VLM-generated pseudo masks for open-vocabulary instance segmentation.  $P^3$ OVD [\[197\]](#page-20-6) exploits prompt-driven self-training that refines the VLMgenerated pseudo labels with fine-grained prompt tuning.

### 7.2.2 Knowledge Distillation for Semantic Segmentation

**Knowledge distillation for open-vocabulary semantic segmentation** leverages VLMs to enlarge the vocabulary of segmentation models, aim to segment pixels described by arbitrary texts (i.e., any categories of pixels beyond base classes). For example, [\[35\]](#page-17-34), [\[180\]](#page-19-54), [\[181\]](#page-19-55) achieve openvocabulary semantic segmentation by first class-agnostic segmentation by grouping pixels into multiple segments and then segment recognition with CLIP. CLIPSeg [\[175\]](#page-19-57) introduces a lightweight transformer decoder to extend CLIP for semantic segmentation. LSeg [\[176\]](#page-19-58) maximizes the correlation between CLIP text embeddings and pixel-wise image embedding encoded by segmentation models. Zeg-CLIP [\[174\]](#page-19-50) employs CLIP to generate semantic masks and introduces a relationship descriptor to mitigate overfitting on base classes. MaskCLIP+ [\[163\]](#page-19-39) and SSIW [\[177\]](#page-19-59) distill

#### TABLE 5: Summary of VLM knowledge distillation methods. [code] directs to code websites.

<span id="page-13-1"></span>

| Task                     | Method                                                                                                                                                                                                                                                                                                                                                                                                        | Contribution                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
|--------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Semantic<br>Segmentation | CLIPSeg [175] [code]<br>ZegFormer [35] [code]<br>LSeg [176] [code]<br>SSIW [177]<br>MaskClip+ [163] [code]<br>ZegClip [174] [code]<br>Fusioner [178] [code]<br>OVSeg [179] [code]<br>ZSSeg [180] [code]<br>OpenSeg [181] [code]<br>ReCo [182] [code]<br>CLIMS [183] [code]<br>CLIP-ES [184] [code]<br>FreeSeg [185] [code]                                                                                    | Extend CLIP by introducing a lightweight transformer-based decoder.<br>Group the pixels into segments and preforms zero-shot classification task on the segments.<br>Propose language-driven semantic segmentation by matching pixel and text embeddings.<br>Introduce a test-time augmentation technique to refine the pseudo labels generated by CLIP.<br>Perform self-training with the pseudo labels generated by MaskClip (modified from CLIP).<br>Propose deep prompt tuning, non-mutually exclusive loss and relationship descriptor.<br>Introduce cross-modality fusion that aligns the visual representation with language concept.<br>Adapt CLIP with the region-word pairs generated by the modified MaskFormer.<br>Propose to first generate mask proposals and then classifies the generated mask proposals.<br>Propose to align each word in the caption with the generated segmentation masks.<br>Propose language-guided co-segmentation with the CLIP-retrieved images.<br>Use CLIP to generate high-quality class activation maps w/o involving irrelevant background.<br>Employ CLIP to refine the class activation map for weakly-supervised segmentation.<br>Propose a unified, universal and open-Vocabulary image segmentation network.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              |
| Object<br>Detection      | ViLD [36] [code]<br>DetPro [37] [code]<br>HierKD [186] [code]<br>RKD [187] [code]<br>PromptDet [188] [code]<br>PB-OVD [189] [code]<br>CondHead [190]<br>VLDet [191] [code]<br>F-VLM [192]<br>OV-DETR [173] [code]<br>Detic [193] [code]<br>XPM [194] [code]<br>OWL-ViT [195] [code]<br>VL-PLM [196] [code]<br>P3OVD [197]<br>ZSD-YOLO [198] [code]<br>RO-ViT [199]<br>BARON [200] [code]<br>OADP [201] [code] | Propose to distill knowledge from a pre-trained VLM into a two-stage object detector.<br>Propose to learn continuous prompt representations for open-vocabulary object detection.<br>Propose hierarchical knowledge distillation for global-level and instance-level distillation.<br>Propose region-based knowledge distillation for aligning region- and image-level embeddings.<br>Introduce regional prompting for aligning text embeddings with regional image embeddings.<br>Propose to train object detectors with the pseudo bounding-box labels generated by VLMs.<br>Propose semantic-visual alignment for better box regression and mask segmentation.<br>Achieve open-vocabulary object detection by the bipartite matching between regions and words.<br>Propose to simply build a detection head upon the pre-trained VLM for object localization.<br>Achieve open-vocabulary detection transformer with a binary matching strategy.<br>Enlarge detection vocabulary using image-level supervision and pre-trained CLIP text encoder.<br>Design cross-modal pseudo-labeling to let VLMs generate caption-driven pseudo masks.<br>Propose ViT-based open-vocabulary detector by adding object classification/localization head.<br>Leverage VLMs for assigning category labels to the generated pseudo bounding boxes.<br>Propose prompt-driven self-training that refines the pseudo labels generated by VLMs.<br>Leverage CLIP for object detection with a self-labeling based data augmentation techiqniue.<br>Bridge the gap of VLM pre-training and downstream open-vocabulary detection.<br>Propose neighborhood sampling strategy to align the embedding of bag of regions.<br>Propose object-aware distillation network to preserve and transfer contextual knowledge. |

knowledge with VLM-predicted pixel-level pseudo labels. FreeSeg [\[185\]](#page-19-65) generates mask proposals firstly and then performs zero-shot classification for them.

**Knowledge distillation for weakly-supervised semantic segmentation** aims to leverage both VLMs and weak supervision (e.g., image-level labels) for semantic segmentation. For example, CLIP-ES [\[184\]](#page-19-64) employs CLIP to refine the class activation map by deigning a softmax function and a class-aware attention-based affinity module for mitigating the category confusion issue. CLIMS [\[183\]](#page-19-63) employs CLIP knowledge to generate high-quality class activation maps for better weakly-supervised semantic segmentation.

# **7.3 Summary and Discussion**

In summary, most VLM studies explore knowledge distillation over two dense visual recognition tasks, namely, object detection and semantic segmenting, where those for the former aim to better align image-level and objectlevel representations while those for the latter focus on tackling the mismatch between image-level and pixel-level representations. They can also be categorized based on their methodology, including feature-space distillation that enforces embedding consistency between VLM's encoder and the detection (or segmentation) encoder and pseudolabelling distillation that employs VLM-generated pseudo labels to regularize detection or segmentation models. Moreover, compared with VLM transfer, VLM knowledge distillation has clearly better flexibility of allowing different downstream networks regardless of the original VLMs.

<span id="page-13-2"></span>Image /page/13/Figure/7 description: The image contains two line graphs. The left graph shows the Top-1 Accuracy on ImageNet (%) on the y-axis and Pre-training Data Size (M) on the x-axis. The data points are for ResNet-50 and are plotted at 15M, 30M, 56M, and 88M. The corresponding accuracies are approximately 42%, 50%, 60%, and 63%. The right graph shows the Top-1 Accuracy on ImageNet (%) on the y-axis and Pre-training Model on the x-axis. The data points are for 400M Pre-training Data and are plotted for RN50, RN101, RN50x4, RN50x16, and RN50x64. The corresponding accuracies are approximately 60%, 63%, 67%, 70%, and 74%.

Fig. 14: Performance versus data size and model size. It shows that scaling up either the pre-training data [\[113\]](#page-18-31) or the pre-training model [\[10\]](#page-17-9) benefits VLM consistently.

# <span id="page-13-0"></span>**8 PERFORMANCE COMPARISON**

In this section, we compare, analyze and discuss the VLM pre-training, VLM transfer learning, and VLM knowledge distillation methods as reviewed in Sections [5](#page-6-1)[-7.](#page-12-0)

#### **8.1 Performance of VLM Pre-training**

As discussed in Sec. [3.4,](#page-5-5) *zero-shot prediction* as one widelyadopted evaluation setup assesses VLM generalization over unseen tasks without task-specific fine-tuning. This subsection presents the performance of *zero-shot prediction* over different visual recognition tasks including image classification, object detection, and semantic segmentation.

Table [6](#page-14-0) shows evaluations on 11 widely adopted image classification tasks. Note it shows the best VLM performance as VLM pre-training often have different implementations. Three conclusions can be drawn from Table [6](#page-14-0) as well as Fig. [14:](#page-13-2) 1) VLM performance is usually up to the size of

# <span id="page-14-0"></span>TABLE 6: Performance of VLM pre-training methods over zero-shot prediction setup on image classification tasks.

| Methods            | Image encoder   | Text encoder  | Data Size | ImageNet-1k [40] | CIFAR-10 [23] | CIFAR-100 [23] | Food101 [22] | sun397 [24] | Cars [25] | Aircraft [96] | DTD [99] | Pets [26] | caltech101 [89] | flowers102 [91] |
|--------------------|-----------------|---------------|-----------|------------------|---------------|----------------|--------------|-------------|-----------|---------------|----------|-----------|-----------------|-----------------|
| <b>CLIP</b> [10]   | $ViT-L/14$      | Transformer   | 400M      | 76.2             | 95.7          | 77.5           | 93.8         | 68.4        | 78.8      | 37.2          | 55.7     | 93.5      | 92.8            | 78.3            |
| ALIGN [17]         | EfficientNet    | <b>BERT</b>   | 1.8B      | 76.4             | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| <b>OTTER</b> [112] | FBNetV3-C       | DeCLUTR-Sci   | 3M        | -                | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| DeCLIP [113]       | <b>REGNET-Y</b> | <b>BERT</b>   | 88M       | 73.7             | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| ZeroVL [114]       | $ViT-B/16$      | <b>BERT</b>   | 100M      | -                | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| <b>FILIP</b> [18]  | $ViT-L/14$      | Transformer   | 340M      | 77.1             | 95.7          | 75.3           | 92.2         | 73.1        | 70.8      | 60.2          | 60.7     | 92.0      | 93.0            | 90.1            |
| UniCL [65]         | Swin-tiny       | Transformer   | 16.3M     | 71.3             | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| Florence [115]     | CoSwin          | <b>RoBERT</b> | 900M      | 83.7             | 94.6          | 77.6           | 95.1         | 77.0        | 93.2      | 55.5          | 66.4     | 95.9      | 94.7            | 86.2            |
| SLIP [64]          | ViT-L           | Transformer   | 15M       | 47.9             | 87.5          | 54.2           | 69.2         | 56.0        | 9.0       | 9.5           | 29.9     | 41.6      | 80.9            | 60.2            |
| PyramidCLIP [116]  | ResNet50        | T5            | 143M      | 47.8             | 81.5          | 53.7           | 67.8         | 65.8        | 65.0      | 12.6          | 47.2     | 83.7      | 81.7            | 65.8            |
| Chinese CLIP [117] | $ViT-L/14$      | CNRoberta     | 200M      | -                | 96.0          | 79.7           | -            | -           | -         | 26.2          | 51.2     | -         | -               | -               |
| LiT [118]          | $ViT-g/14$      |               | 4B        | 85.2             | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| AltCLIP [119]      | $ViT-L/14$      | Transformer   | 2M        | 74.5             | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| FLAVA [42]         | $ViT-B/16$      | $ViT-B/16$    | 70M       | -                | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| KELIP [120]        | $ViT-B/32$      | Transformer   | 1.1B      | 62.6             | 91.5          | 68.6           | 79.5         | -           | 75.4      | -             | 51.2     | -         | -               | -               |
| COCA [19]          | $ViT-G/14$      |               | 4.8B      | 86.3             | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| nCLIP [121]        | ViTB/16         | Transformer   | 35M       | 48.8             | 83.4          | 54.5           | 65.8         | 59.9        | 18.0      | 5.8           | 57.1     | 33.2      | 73.9            | 50.0            |
| K-lite [122]       | CoSwin          | RoBERT5       | 813M      | 85.8             | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| NLIP [123]         | $ViT-B/16$      | BART          | 26M       | 47.4             | 81.9          | 47.5           | 59.2         | 58.7        | 7.8       | 7.5           | 32.9     | 39.2      | 79.5            | 54.0            |
| UniCLIP [84]       | $ViT-B/32$      | Transformer   | 30M       | 54.2             | 87.8          | 56.5           | 64.6         | 61.1        | 19.5      | 4.7           | 36.6     | 69.2      | 84.0            | 8.0             |
| PaLI [83]          | ViT-e           | mT5           | 12B       | 85.4             | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| CLIPPO [43]        | $ViT-L/16$      | $ViT-L/16$    | 12B       | 70.5             | -             | -              | -            | -           | -         | -             | -        | -         | -               | -               |
| OneR [44]          | $ViT-L/16$      | $ViT-L/16$    | 4M        | 27.3             | -             | 31.4           | -            | -           | -         | -             | -        | -         | -               | -               |
| RA-CLIP [125]      | $ViT-B/32$      | BERT          | 15M       | 53.5             | 89.4          | 62.3           | 43.8         | 46.5        | -         | -             | 25.6     | -         | 76.9            | -               |
| LA-CLIP [126]      | $ViT-B/32$      | Transformer   | 400M      | 64.4             | 92.4          | 73.0           | 79.7         | 64.9        | 81.9      | 20.8          | 55.4     | 87.2      | 91.8            | 70.3            |
| ALIP [127]         | $ViT-B/32$      | Transformer   | 15M       | 40.3             | 83.8          | 51.9           | 45.4         | 47.8        | 3.4       | 2.7           | 23.2     | 30.7      | 74.1            | 54.8            |
| GrowCLIP [128]     | $ViT-B/16$      | Transformer   | 12M       | 36.1             | 60.7          | 28.3           | 42.5         | 45.5        | -         | -             | 17.3     | -         | 71.9            | 23.3            |

<span id="page-14-1"></span>TABLE 7: Performance of VLM pre-training methods over zero-shot prediction setup on segmentation tasks.

| Method         | Image encoder | Text encoder | Data size | VOC [90] | PASCAL C. [109] | COCO [106] |
|----------------|---------------|--------------|-----------|----------|-----------------|------------|
| GroupVit [129] | ViT           | Transformer  | 26M       | 52.3     | 22.4            | -          |
| SegClip [46]   | ViT           | Transformer  | 3.4M      | 52.6     | 24.7            | 26.5       |

<span id="page-14-2"></span>TABLE 8: Performance of VLM pre-training methods over zero-shot prediction setup on detection tasks.

| Method            | Image encoder | Text encoder | Data size | COCO [106] | LVIS [107] | LVIS Mini. [107] |
|-------------------|---------------|--------------|-----------|------------|------------|------------------|
| RegionClip [131]  | ResNet50x4    | Transformer  | 118k      | 29.6       | 11.3       | -                |
| <b>GLIP</b> [67]  | Swin-L        | <b>BERT</b>  | 27.43M    | 49.8       | 26.9       | 34.3             |
| <b>FIBER</b> [71] | Swin-B        | RoBERTa      | 4M        | 49.3       | -          | 32.2             |
| DetCLIP[45]       | Swin-L        | <b>BERT</b>  | 2.43M     | -          | 35.9       | -                |

training data. As shown in the first graph in Fig. [14,](#page-13-2) scaling up the pre-training data leads to consistent improvements; 2) VLM performance is usually up to the model size. As shown in the second graph, with the same pre-training data, scaling up model sizes improves the VLM performance consistently; 3) With large-scale image-text training data, VLMs can achieve superior zero-shot performance on various downstream tasks. As Table [6](#page-14-0) shows, COCA [\[19\]](#page-17-18) achieves state-of-the-art performance on ImageNet, and FILIP [\[18\]](#page-17-17) performs well consistently across 11 tasks.

The superior generalization of VLMs is largely attributed to three factors: 1) Big data - as image-text pairs are almost infinitely available on the Internet, VLMs are usually trained with millions or billions of image and text samples that cover very broad visual and language concepts, leading to strong generalization capability; 2) Big model - compared with traditional visual recognition models, VLMs generally adopt much larger models  $(e.g., ViT-G$  in COCA  $[19]$  with 2B parameters) that provide great capacity for effective learning from big data; 3) Task-agnostic learning - the supervision in VLM pre-training is usually general and taskagnostic. Compared with task-specific labels in traditional visual recognition, the texts in image-text pairs provide task-agnostic, diverse and informative language supervision which help train generalizable models that works well across various downstream tasks.

Note several studies [\[45\]](#page-17-44), [\[46\]](#page-17-45), [\[67\]](#page-18-8), [\[71\]](#page-18-12), [\[129\]](#page-19-0), [\[131\]](#page-19-7) investigate VLM pre-training for object detection and semantic segmentation with local VLM pre-training objectives such as region-word matching  $[67]$ . Tables [7](#page-14-1) and [8](#page-14-2) summarize *zero-shot prediction* performance on object detection and semantic segmentation tasks. We can observe that VLMs enable effective zero-shot prediction on both dense prediction tasks. Note the results in Tables [7](#page-14-1) and [8](#page-14-2) may not be aligned with the conclusions in previous paragraphs, largely because this field of research is under-explored with very limited VLMs on dense visual tasks.

**Limitations of VLMs.** As discussed above, although VLMs benefit clearly while data/model size scales up, they still suffer from several limitations: (1) When data/model size keeps increasing, the performance saturates and further scaling up won't improve performance [\[113\]](#page-18-31), [\[202\]](#page-20-10); (2) Adopting large-scale data in VLM pre-training necessitates extensive computation resources, e.g., 256 V100 GPUs, 288 training hours in CLIP ViT-L [\[10\]](#page-17-9); (3) Adopting large models introduces excessive computation and memory overheads in both training and inference.

# **8.2 Performance of VLM Transfer Learning**

This section summarizes the performance of VLM transfer under the setups of supervised transfer, few-shot supervised transfer and unsupervised transfer. Table [9](#page-15-0) shows the results on 11 widely adopted image classification datasets  $(e.g.,)$ 

TABLE 9: Performance of VLM transfer Learning methods on image classification tasks.

<span id="page-15-0"></span>

| Methods<br>Baseline [143]                                                                                                                                                                                                                                                                                                                                                                                                                               | Image encoder<br>ResNet-50                                                                                                                                                                                                                                                                                                                       | Setup<br>$w$ /o Transfer                                                                                                                                                                                                                                                                                                                                                                                                       | Average<br>59.2                                                                                                                                                                                                                                                                                                                                                                       | [40]<br>ImageNet-1k<br>60.3                                                                                                                                                                                        | 89<br>caltech101<br>86.1                                                                                                                                                                                                                                                                 | 26<br>Pets <sup>1</sup><br>85.8                                                                                                                                                  | 25<br>Cars<br>55.6                                                                                                                                                                                   | [91]<br>Flowers102<br>66.1                                                                                                                                                                                                                              | 22 <br>Food101<br>77.3                                                                                                                                                                     | Aircraft <sup>[96]</sup><br>16.9                                                                                                                                                                                      | 24<br>SUN397<br>60.2                                                                                                                                                                                                                | <b>PED</b> [99]<br>41.6                                                                                                                                                                                                                                                                       | [104]<br>EuroSAT<br>38.2                                                                                                                                                                                                                                                                     | 29<br>UCF101<br>62.7                                                                                                                                                                                   |
|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| Baseline [10]                                                                                                                                                                                                                                                                                                                                                                                                                                           | $ViT-B/16$                                                                                                                                                                                                                                                                                                                                       | $w$ /o Transfer                                                                                                                                                                                                                                                                                                                                                                                                                | 71.7                                                                                                                                                                                                                                                                                                                                                                                  | 70.2                                                                                                                                                                                                               | 95.4                                                                                                                                                                                                                                                                                     | 94.1                                                                                                                                                                             | 68.6                                                                                                                                                                                                 | 74.8                                                                                                                                                                                                                                                    | 90.6                                                                                                                                                                                       | 31.1                                                                                                                                                                                                                  | 72.2                                                                                                                                                                                                                                | 56.4                                                                                                                                                                                                                                                                                          | 60.6                                                                                                                                                                                                                                                                                         | 73.5                                                                                                                                                                                                   |
| Baseline [10]                                                                                                                                                                                                                                                                                                                                                                                                                                           | $ViT-L/14$                                                                                                                                                                                                                                                                                                                                       | w/o Transfer                                                                                                                                                                                                                                                                                                                                                                                                                   | 73.7                                                                                                                                                                                                                                                                                                                                                                                  | 76.2                                                                                                                                                                                                               | 92.8                                                                                                                                                                                                                                                                                     | 93.5                                                                                                                                                                             | 78.8                                                                                                                                                                                                 | 78.3                                                                                                                                                                                                                                                    | 93.8                                                                                                                                                                                       | 37.2                                                                                                                                                                                                                  | 68.4                                                                                                                                                                                                                                | 55.7                                                                                                                                                                                                                                                                                          | 59.6                                                                                                                                                                                                                                                                                         | 76.9                                                                                                                                                                                                   |
| CoOp[31]<br>CoCoOp[32]<br>SubPT [132]<br><b>LASP</b> [133]<br>ProDA $[134]$<br><b>VPT [135]</b><br>ProGrad [136]<br>CPL [137]<br><b>PLOT</b> [138]<br>CuPL [160]<br><b>UPL</b> [143]<br><b>TPT</b> [144]<br>VP [147]<br><b>UPT [149]</b><br><b>MaPLE</b> [151]<br><b>CAVPT</b> [152]<br>Tip-Adapter [34]<br>$SuS-X$ [154]<br>$SgVA-CLIP$ [156]<br>$VT-Clip$ [157]<br>$CALIP$ [158]<br>Wise-FT $[162]$<br>KgCoOp[145]<br>ProTeCt [146]<br>RePrompt [148] | $ViT-B/16$<br>$ViT-B/16$<br>ResNet50<br>$ViT-B/16$<br>ResNet50<br>$ViT-B/16$<br>ResNet-50<br>$ViT-B/16$<br>ResNet-50<br>$ViT-L/14$<br>ResNet-50<br>$ViT-B/16$<br>$ViT-B/32$<br>$ViT-B/16$<br>$ViT-B/16$<br>$ViT-B/16$<br>$ViT-B/16$<br>ResNet-50<br>$ViT-B/16$<br>ResNet-50<br>ResNet-50<br>$ViT-L/14$<br>$ViT-B/16$<br>$ViT-B/16$<br>$ViT-B/16$ | Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup.<br>Unsupervised<br>Unsupervised<br>Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup.<br>Unsupervised<br>Few-shot Sup.<br>Few-shot Sup.<br>Unsupervised<br>Supervised<br>Few-shot Sup.<br>Few-shot Sup.<br>Few-shot Sup. | 71.6<br>75.8<br>66.4<br>76.1<br>$\tilde{\phantom{a}}$<br>77.4<br>67.9<br>$\overline{\phantom{a}}$<br>73.9<br>$\overline{\phantom{a}}$<br>68.4<br>64.8<br>$\overline{\phantom{a}}$<br>76.2<br>78.6<br>83.2<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$<br>59.4<br>$\overline{\phantom{a}}$<br>74.4<br>69.9<br>83.2 | 71.9<br>73.1<br>63.4<br>73.0<br>65.3<br>73.4<br>62.1<br>76.0<br>63.0<br>76.6<br>61.1<br>69.0<br>$\sim$<br>73.2<br>73.5<br>72.5<br>70.8<br>61.8<br>73.3<br>60.6<br>87.1<br>70.1<br>$\overline{\phantom{a}}$<br>74.6 | 93.7<br>95.8<br>91.7<br>95.8<br>91.3<br>96.4<br>91.5<br>96.3<br>92.2<br>93.4<br>91.4<br>94.2<br>$\overline{\phantom{a}}$<br>96.1<br>96.0<br>96.1<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$<br>87.7<br>94.6<br>96.5 | 94.5<br>96.4<br>91.8<br>95.7<br>90.0<br>96.8<br>93.4<br>97.7<br>87.2<br>93.8<br>89.5<br>87.8<br>85.0<br>96.3<br>96.6<br>93.5<br>٠<br>٠<br>٠<br>93.1<br>58.6<br>93.2<br>٠<br>93.7 | 68.1<br>72.0<br>60.7<br>72.2<br>75.5<br>73.1<br>62.7<br>77.2<br>72.8<br>77.6<br>71.0<br>66.9<br>$\overline{a}$<br>71.8<br>73.5<br>88.2<br>٠<br>٠<br>77.4<br>71.9<br>$\overline{\phantom{a}}$<br>85.0 | 74.1<br>81.7<br>73.8<br>81.6<br>95.5<br>81.1<br>78.7<br>81.7<br>94.8<br>$\overline{\phantom{a}}$<br>76.6<br>69.0<br>70.3<br>81.0<br>82.6<br>97.6<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$<br>٠<br>66.4<br>90.6<br>$\overline{a}$<br>97.1 | 85.2<br>91.0<br>81.0<br>90.5<br>82.4<br>91.6<br>81.0<br>93.2<br>77.1<br>93.3<br>77.9<br>84.7<br>78.9<br>91.3<br>91.4<br>85.0<br>٠<br>٠<br>56.3<br>86.5<br>$\overline{\phantom{a}}$<br>87.4 | 28.7<br>27.7<br>20.3<br>31.6<br>36.6<br>34.7<br>21.9<br>$\overline{\phantom{a}}$<br>34.5<br>36.1<br>21.7<br>24.8<br>$\sim$<br>34.5<br>36.5<br>57.9<br>٠<br>٠<br>$\overline{\phantom{a}}$<br>17.7<br>32.4<br>٠<br>50.3 | 72.5<br>78.3<br>70.2<br>77.8<br>$\overline{\phantom{a}}$<br>78.5<br>70.3<br>80.6<br>70.0<br>61.7<br>66.4<br>65.5<br>60.6<br>78.7<br>79.7<br>74.3<br>٠<br>٠<br>76.4<br>$\overline{\phantom{a}}$<br>86.2<br>٠<br>71.7<br>74.5<br>77.5 | 54.2<br>64.8<br>54.7<br>62.8<br>70.1<br>67.3<br>57.8<br>$\overline{\phantom{a}}$<br>65.6<br>$\overline{\phantom{a}}$<br>55.1<br>47.8<br>57.1<br>65.6<br>68.2<br>72.6<br>$\overline{\phantom{a}}$<br>$\overline{\phantom{a}}$<br>٠<br>65.7<br>42.4<br>58.3<br>$\overline{\phantom{a}}$<br>73.7 | 68.7<br>71.2<br>54.5<br>74.6<br>84.3<br>77.7<br>59.0<br>$\overline{\phantom{a}}$<br>82.2<br>$\tilde{\phantom{a}}$<br>71.0<br>42.4<br>96.4<br>72.0<br>82.4<br>92.1<br>$\tilde{\phantom{a}}$<br>45.6<br>$\overline{\phantom{a}}$<br>38.9<br>$\overline{\phantom{a}}$<br>71.0<br>$\sim$<br>92.9 | 67.5<br>77.6<br>68.1<br>76.8<br>$\sim$<br>79.0<br>68.5<br>77.3<br>$\overline{\phantom{a}}$<br>70.2<br>60.8<br>66.1<br>77.2<br>80.8<br>85.3<br>50.6<br>$\overline{\phantom{0}}$<br>61.7<br>78.4<br>86.4 |
| TaskRes <sup>[159]</sup>                                                                                                                                                                                                                                                                                                                                                                                                                                | ResNet-50                                                                                                                                                                                                                                                                                                                                        | Few-shot Sup.                                                                                                                                                                                                                                                                                                                                                                                                                  | 75.7                                                                                                                                                                                                                                                                                                                                                                                  | 65.7                                                                                                                                                                                                               | 93.4                                                                                                                                                                                                                                                                                     | 87.8                                                                                                                                                                             | 76.8                                                                                                                                                                                                 | 96.0                                                                                                                                                                                                                                                    | 77.6                                                                                                                                                                                       | 36.3                                                                                                                                                                                                                  | 70.6                                                                                                                                                                                                                                | 67.1                                                                                                                                                                                                                                                                                          | 84.0                                                                                                                                                                                                                                                                                         | 77.9                                                                                                                                                                                                   |
| <b>VCD</b> [161]                                                                                                                                                                                                                                                                                                                                                                                                                                        | $ViT-B/16$                                                                                                                                                                                                                                                                                                                                       | Unsupervised                                                                                                                                                                                                                                                                                                                                                                                                                   | $\overline{\phantom{a}}$                                                                                                                                                                                                                                                                                                                                                              | 68.0                                                                                                                                                                                                               | $\overline{\phantom{a}}$                                                                                                                                                                                                                                                                 | 86.9                                                                                                                                                                             | $\overline{\phantom{a}}$                                                                                                                                                                             | $\overline{\phantom{a}}$                                                                                                                                                                                                                                | 88.5                                                                                                                                                                                       | $\overline{\phantom{0}}$                                                                                                                                                                                              | ٠                                                                                                                                                                                                                                   | 45.5                                                                                                                                                                                                                                                                                          | 48.6                                                                                                                                                                                                                                                                                         | $\overline{\phantom{a}}$                                                                                                                                                                               |

EuroSAT [\[104\]](#page-18-49), UCF101 [\[29\]](#page-17-28)) with different backbones such as CNN backbone ResNet-50 and Transformer backbones ViT-B and ViT-L. Note Table [9](#page-15-0) summarizes the performance of 16-shot setup for all *few-shot supervised* methods.

Three conclusions can be drawn from Table [9.](#page-15-0) First, VLM transfer setups helps in downstream tasks consistently. For example, supervised Wise-FT, few-shot supervised CoOp and unsupervised TPT improve accuracy by 10.9%,1.7% and 0.8%, respectively, on ImageNet. As pre-trained VLMs generally suffer from domain gaps with task-specific data, VLM transfer can mitigate the domain gaps by learning from task-specific data, being labelled or unlabelled.

Second, the performance of few-shot supervised transfer lag far behind that of supervised transfer (e.g., 87.1% in WiseFT  $[162]$  and 76.6% in CuPL  $[160]$ ), largely because VLMs may overfit to few-shot labelled samples with degraded generalization. Third, unsupervised transfer can perform comparably with few-shot supervised transfer (e.g., unsupervised UPL [\[143\]](#page-19-19) outperforms 2-shot supervised CoOp [\[31\]](#page-17-30) by 0.4%, unsupervised TPT [\[144\]](#page-19-20) is compara-ble with 16-shot CoOp [\[31\]](#page-17-30)), largely because unsupervised transfer can access massive unlabelled downstream data with much lower overfitting risks. Nevertheless, unsupervised transfer also faces several challenges such as noisy pseudo labels. We expect more studies on this promising but changeling research direction.

#### **8.3 Performance of VLM Knowledge Distillation**

This section presents how VLM knowledge distillation helps in the tasks of object detection and semantic segmentation. Tables [10](#page-15-1) and [11](#page-16-1) show the knowledge distillation

<span id="page-15-1"></span>TABLE 10: Performance of VLM knowledge distillation on object detection. CLIP Transformer is CLIP text encoder.

| Method               | Vision-Language         |             | <b>COCO</b> [106] |      |        | LVIS [107]      |                          |      |
|----------------------|-------------------------|-------------|-------------------|------|--------|-----------------|--------------------------|------|
|                      | Model                   | $AP_{base}$ | $AP_{novel}$      | AP   | $AP_r$ | AP <sub>c</sub> | $AP_f$                   | AP   |
| Baseline [36]        |                         | 28.3        | 26.3              | 27.8 | 19.5   | 19.7            | 17.0                     | 18.6 |
| <b>ViLD</b> [36]     | CLIP ViT-B/32           | 59.5        | 27.6              | 51.3 | 16.7   | 26.5            | 34.2                     | 27.8 |
| DetPro [37]          | CLIP ViT-B/32           |             |                   | 34.9 | 20.8   | 27.8            | 32.4                     | 28.4 |
| HierKD [186]         | CLIP ViT-B/32           | 53.5        | 27.3              |      |        |                 |                          |      |
| <b>RKD</b> [187]     | CLIP ViT-B/32           | 56.6        | 36.9              | 51.0 | 21.1   | 25.0            | 29.1                     | 25.9 |
| PromptDet [188]      | <b>CLIP</b> Transformer |             | 26.6              | 50.6 | 21.4   | 23.3            | 29.3                     | 25.3 |
| <b>PB-OVD</b> [189]  | CLIP Transformer        | 46.1        | 30.8              | 42.1 |        |                 |                          |      |
| CondHead [190]       | CLIP ViT-B/32           | 60.8        | 29.8              | 49.0 | 18.8   | 28.3            | 33.7                     | 28.8 |
| <b>VLDet</b> [191]   | <b>CLIP</b> Transformer | 50.6        | 32.0              | 45.8 | 26.3   | 39.4            | 41.9                     | 38.1 |
| <b>F-VLM [192]</b>   | CLIP ResNet-50          |             | 28.0              | 39.6 | 32.8   | ٠               |                          | 34.9 |
| <b>OV-DETR</b> [173] | CLIP ViT-B/32           | 52.7        | 29.4              | 61.0 | 17.4   | 25.0            | 32.5                     | 26.6 |
| <b>Detic [193]</b>   | <b>CLIP</b> Transformer | 45.0        | 27.8              | 47.1 | 17.8   | 26.3            | 31.6                     | 26.8 |
| <b>OWL-ViT [195]</b> | CLIP ViT-B/32           |             |                   | 28.1 | 18.9   | ۰               | ۰                        | 22.1 |
| <b>VL-PLM</b> [196]  | CLIP ViT-B/32           | 60.2        | 34.4              | 53.5 |        |                 | ۰                        | 22.2 |
| $P3 OVD$ [197]       | CLIP ResNet-50          | 51.9        | 31.5              | 46.6 |        |                 | $\overline{\phantom{0}}$ | 10.6 |
| <b>RO-ViT</b> [199]  | CLIP ViT-L/16           |             | 33.0              | 47.7 | 32.1   |                 |                          | 34.0 |
| <b>BARON</b> [200]   | CLIP ResNet-50          | 54.9        | 42.7              | 51.7 | 23.2   | 29.3            | 32.5                     | 29.5 |
| <b>OADP</b> [201]    | CLIP ViT-B/32           | 53.3        | 30.0              | 47.2 | 21.9   | 28.4            | 32.0                     | 28.7 |

performance on the widely used detection datasets  $(e.g.,)$ COCO [\[106\]](#page-18-51) and LVIS [\[107\]](#page-18-52)) and segmentation datasets  $(e.g.,$  PASCAL VOC  $[90]$  and ADE20k  $[111]$ ), respectively. We can observe that VLM knowledge distillation brings clear performance improvement on detection and segmentation tasks consistently, largely because it introduces general and robust VLM knowledge while benefiting from taskspecific designs in detection and segmentation models.

#### **8.4 Summary**

Several conclusions can be drawn from Tables [6](#page-14-0)[-11.](#page-16-1) Regarding *performance*, VLM pre-training achieves remarkable zeroshot prediction on a wide range of image classification tasks due to its well-designed pre-training objectives. Nevertheless, the development of VLM pre-training for dense visual recognition tasks (on region or pixel-level detection and

<span id="page-16-1"></span>TABLE 11: Performance of VLM knowledge distillation on semantic segmentation tasks.

| Method          | Vision-Language<br>Model | A-847<br>[111] | PC-459<br>[109] | A-150<br>[111] | PC-59<br>[109] | PAS-20<br>[90] | C-19<br>[110] |
|-----------------|--------------------------|----------------|-----------------|----------------|----------------|----------------|---------------|
| Baseline [203]  | -                        | -              | -               | -              | 24.3           | 18.3           | -             |
| LSeq [35]       | CLIP ResNet-101          | -              | -               | -              | -              | 47.4           | -             |
| ZegFormer [176] | CLIP ResNet-50           | -              | -               | 16.4           | -              | 80.7           | -             |
| OVSeg [179]     | CLIP Swin-B              | 9.0            | 12.4            | 29.6           | 55.7           | 94.5           | -             |
| ZSSeg [180]     | CLIP ResNet-101          | 7.0            | -               | 20.5           | 47.7           | -              | 34.5          |
| OpenSeg [181]   | CLIP Eff-B7              | 6.3            | 9.0             | 21.1           | 42.1           | -              | -             |
| ReCo [182]      | CLIP ResNet-101          | -              | -               | -              | -              | -              | 24.2          |
| FreeSeg [185]   | CLIP ViT-B/16            | -              | -               | 39.8           | -              | 86.9           | -             |

segmentation) lag far behind. In addition, VLM transfer has made remarkable progress across multiple image classification datasets and vision backbones. However, supervised or few-shot supervised transfer still requires labelled images, whereas the more promising but challenging unsupervised VLM transfer has been largely neglected.

Regarding *benchmark*, most VLM transfer studies adopt the same pre-trained VLM as the baseline model and perform evaluations on the same downstream tasks, which facilitates benchmarking greatly. They also release their codes and do not require intensive computation resources, easing reproduction and benchmarking greatly. Differently, VLM pre-training has been studied with different data  $(e.g., CLIP [10], LAION400M [21] and CC12M [79])$  $(e.g., CLIP [10], LAION400M [21] and CC12M [79])$  $(e.g., CLIP [10], LAION400M [21] and CC12M [79])$  $(e.g., CLIP [10], LAION400M [21] and CC12M [79])$  $(e.g., CLIP [10], LAION400M [21] and CC12M [79])$  $(e.g., CLIP [10], LAION400M [21] and CC12M [79])$  $(e.g., CLIP [10], LAION400M [21] and CC12M [79])$  and networks (e.g., ResNet  $[6]$ , ViT  $[57]$ , Transformer  $[58]$  and BERT [\[14\]](#page-17-13)), making fair benchmarking a very challenging task. Several VLM pre-training studies also use non-public training data  $[10]$ ,  $[18]$ ,  $[83]$  or require intensive compu-tation resources (e.g., 256 V100 GPUs in [\[10\]](#page-17-9)). For VLM knowledge distillation, many studies adopt different taskspecific backbones (e.g., ViLD adopts Faster R-CNN, OV-DETR uses DETR) which complicates benchmarking greatly. Hence, VLM pre-training and VLM knowledge distillation are short of certain norms in term of training data, networks and downstream tasks.

# <span id="page-16-0"></span>**9 FUTURE DIRECTIONS**

VLM enables effective usage of web data, zero-shot prediction without any task-specific fine-tuning, and openvocabulary visual recognition of images of arbitrary categories. It has been achieving great success with incredible visual recognition performance. In this section, we humbly share several research challenges and potential research directions that could be pursued in the future VLM study on various visual recognition tasks.

For **VLM pre-training**, there are four challenges and potential research directions as listed.

*(1) Fine-grained vision-language correlation modelling.* With local vision-language correspondence knowledge [\[45\]](#page-17-44), [\[67\]](#page-18-8), VLMs can better recognize patches and pixels beyond images, greatly benefiting dense prediction tasks such as object detection and semantic segmentation that play an important role in various visual recognition tasks. Given the very limited VLM studies along this direction [\[45\]](#page-17-44), [\[46\]](#page-17-45), [\[67\]](#page-18-8), [\[71\]](#page-18-12), [\[129\]](#page-19-0), [\[131\]](#page-19-7), we expect more research in fine-grained VLM pre-training for zero-shot dense prediction tasks.

*(2) Unification of vision and language learning.* The advent of Transformer  $[57]$ ,  $[58]$  makes it possible to unify image and text learning within a single Transformer by tokenizing images and texts in the same manner. Instead of

employing two separate networks as in existing VLMs [\[10\]](#page-17-9), [\[17\]](#page-17-16), unifying vision and language learning enables efficient communications across data modalities which can benefit both training effectiveness and training efficiency. This issue has attracted some attention  $[43]$ ,  $[44]$  but more efforts are needed towards more sustainable VLMs.

*(3) Pre-training VLMs with multiple languages.* Most existing VLMs are trained with a single language  $(i.e., En$ glish) [\[10\]](#page-17-9), [\[17\]](#page-17-16), which could introduce bias in term of cultures and regions [\[77\]](#page-18-22), [\[79\]](#page-18-24) and hinder VLM applications in other language areas. Pre-training VLMs with texts of multiple languages [\[119\]](#page-18-60), [\[120\]](#page-18-61) allows learning different cultural visual characteristics for the same meaning of words but different languages [\[20\]](#page-17-19), enabling VLMs to work efficiently and effectively across different language scenarios. We expect more research on multilingual VLMs.

*(4) Data-efficient VLMs.* Most existing work trains VLMs with large-scale training data and intensive computations, making its sustainability a big concern. Training effective VLMs with limited image-text data can mitigate this issue greatly. For example, instead of merely learning from each image-text pair, more useful information could be learned with the supervision among image-text pairs  $[112]$ ,  $[113]$ .

*(5) Pre-training VLMs with LLMs.* Recent studies [\[126\]](#page-19-2), [\[127\]](#page-19-3) retrieve rich language knowledge from LLMs to enhance VLM pre-training. Specifically, they employ LLMs to augment the texts in the raw image-text pairs, which provides richer language knowledge and helps better learn vision-language correlation. We expect more exploration of LLMs in VLM pre-training in the future research.

For **VLM Transfer Learning**, there are three challenges and potential research directions as listed.

*(1) Unsupervised VLM transfer.* Most existing VLM transfer studies work with a supervised or few-shot supervised setup that requires labelled data, and the latter tends to overfit to the few-shot samples. Unsupervised VLM transfer allows exploring massive unlabelled data with much lower risk of overfitting. More studies on unsupervised VLM transfer are expected in the ensuing VLM studies.

*(2) VLM transfer with visual prompt/adapter.* Most existing studies on VLM transfer focus on text prompt learning [\[31\]](#page-17-30). Visual prompt learning or visual adapter, which is complementary to text prompting and can enable pixel-level adaptation in various dense prediction tasks, is largely neglected. More VLM transfer studies in visual domain are expected.

*(3) Test-time VLM transfer.* Most existing studies conduct transfer by fine-tuning VLMs on each downstream task (i.e., prompt learning), leading to repetitive efforts while facing many downstream tasks. Test-time VLM transfer allows adapting prompts on the fly during inference, circumventing the repetitive training in existing VLM transfer. We can foresee more studies on test-time VLM transfer.

*(4) VLM transfer with LLMs.* Different from prompt engineering and prompt learning, several attempts [\[160\]](#page-19-36), [\[161\]](#page-19-37) exploit LLMs [\[172\]](#page-19-48) to generate text prompts that better describe downstream tasks. This approach is automatic and requires little labelled data. We expect more exploration of LLMs in VLM transfer in the future research.

**VLM knowledge distillation** could be further explored from two aspects. The first is knowledge distillation from multiple VLMs that could harvest their synergistic effect by coordinating knowledge distillation from multiple VLMs. The second is knowledge distillation for other visual recognition tasks such as instance segmentation, panoptic segmentation, person re-identification etc.

# **10 CONCLUSION**

Vision-language models for visual recognition enables effective usage of web data and allows zero-shot predictions without task-specific fine-tuning, which is simple to implement yet has achieved great success on a wide range of recognition tasks. This survey extensively reviews vision-language models for visual recognition from several perspectives, including background, foundations, datasets, technical approaches, benchmarking, and future research directions. The comparative summary of the VLM datasets, approaches, and performance in tabular forms provides a clear big picture of the recent development in VLM pretraining which will greatly benefit the future research along this emerging but very promising research direction.

# **REFERENCES**

- <span id="page-17-0"></span>[1] A. Geiger et al. Are we ready for autonomous driving? the kitti vision benchmark suite. In *CVPR*, pp. 3354–3361. IEEE, 2012.
- <span id="page-17-1"></span>[2] G. Cheng et al. Remote sensing image scene classification meets deep learning: Challenges, methods, benchmarks, and opportunities. *JSTARS*, 13:3735–3756, 2020.
- <span id="page-17-2"></span>[3] H. A. Pierson and M. S. Gashler. Deep learning in robotics: a review of recent research. *Advanced Robotics*, 31(16):821–835, 2017.
- <span id="page-17-3"></span>[4] A. Krizhevsky et al. Imagenet classification with deep convolutional neural networks. *Communications of the ACM*, 60(6):84–90, 2017.
- <span id="page-17-4"></span>[5] K. Simonyan and A. Zisserman. Very deep convolutional networks for large-scale image recognition. *arXiv preprint arXiv:1409.1556*, 2014.
- <span id="page-17-5"></span>[6] K. He et al. Deep residual learning for image recognition. In *CVPR*, pp. 770–778, 2016.
- <span id="page-17-6"></span>[7] L. E. Peterson. K-nearest neighbor. *Scholarpedia*, 4(2):1883, 2009.
- <span id="page-17-7"></span>[8] C. Cortes and V. Vapnik. Support-vector networks. *Machine learning*, 20:273–297, 1995.
- <span id="page-17-8"></span>[9] A. Mathur and G. M. Foody. Multiclass and binary svm classification: Implications for training and classification users. *IEEE Geoscience and remote sensing letters*, 5(2):241–245, 2008.
- <span id="page-17-9"></span>[10] A. Radford et al. Learning transferable visual models from natural language supervision. In *ICML*, pp. 8748–8763. PMLR, 2021.
- <span id="page-17-10"></span>[11] R. Girshick. Fast r-cnn. In *ICCV*, pp. 1440–1448, 2015.
- <span id="page-17-11"></span>[12] K. He et al. Momentum contrast for unsupervised visual representation learning. In *CVPR*, pp. 9729–9738, 2020.
- <span id="page-17-12"></span>[13] T. Chen et al. A simple framework for contrastive learning of visual representations. In *ICML*, pp. 1597–1607. PMLR, 2020.
- <span id="page-17-13"></span>[14] J. Devlin et al. Bert: Pre-training of deep bidirectional transformers for language understanding. *arXiv preprint arXiv:1810.04805*, 2018.
- <span id="page-17-14"></span>[15] A. Radford et al. Improving language understanding by generative pre-training. 2018.
- <span id="page-17-15"></span>[16] A. Radford et al. Language models are unsupervised multitask learners. *OpenAI blog*, 1(8):9, 2019.
- <span id="page-17-16"></span>[17] C. Jia et al. Scaling up visual and vision-language representation learning with noisy text supervision. In *ICML*, pp. 4904–4916. PMLR, 2021.
- <span id="page-17-17"></span>[18] L. Yao et al. Filip: Fine-grained interactive language-image pretraining. In *ICLR*, 2021.
- <span id="page-17-18"></span>[19] J. Yu et al. Coca: Contrastive captioners are image-text foundation models. *arXiv preprint arXiv:2205.01917*, 2022.
- <span id="page-17-19"></span>[20] C. Schuhmann et al. Laion-5b: An open large-scale dataset for training next generation image-text models. *arXiv preprint arXiv:2210.08402*, 2022.
- <span id="page-17-20"></span>[21] C. Schuhmann et al. Laion-400m: Open dataset of clip-filtered 400 million image-text pairs. *arXiv preprint arXiv:2111.02114*, 2021.

- <span id="page-17-21"></span>[22] L. Bossard et al. Food-101–mining discriminative components with random forests. In *ECCV*, pp. 446–461. Springer, 2014.
- <span id="page-17-22"></span>[23] A. Krizhevsky et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-17-23"></span>[24] J. Xiao et al. Sun database: Large-scale scene recognition from abbey to zoo. In *2010 IEEE computer society conference on computer vision and pattern recognition*, pp. 3485–3492. IEEE, 2010.
- <span id="page-17-24"></span>[25] J. Krause et al. Collecting a large-scale dataset of fine-grained cars. 2013.
- <span id="page-17-25"></span>[26] O. M. Parkhi et al. Cats and dogs. In *CVPR*, pp. 3498–3505. IEEE, 2012.
- <span id="page-17-26"></span>[27] D. Kiela et al. The hateful memes challenge: Detecting hate speech in multimodal memes. *NeurIPS*, 33:2611–2624, 2020.
- <span id="page-17-27"></span>[28] A. Miech et al. Rareact: A video dataset of unusual interactions. *arXiv preprint arXiv:2008.01018*, 2020.
- <span id="page-17-28"></span>[29] K. Soomro et al. Ucf101: A dataset of 101 human actions classes from videos in the wild. *arXiv preprint arXiv:1212.0402*, 2012.
- <span id="page-17-29"></span>[30] J. Carreira et al. A short note on the kinetics-700 human action dataset. *arXiv preprint arXiv:1907.06987*, 2019.
- <span id="page-17-30"></span>[31] K. Zhou et al. Learning to prompt for vision-language models. *IJCV*, 130(9):2337–2348, 2022.
- <span id="page-17-31"></span>[32] K. Zhou et al. Conditional prompt learning for vision-language models. In *CVPR*, pp. 16816–16825, 2022.
- <span id="page-17-32"></span>[33] P. Gao et al. Clip-adapter: Better vision-language models with feature adapters. *arXiv preprint arXiv:2110.04544*, 2021.
- <span id="page-17-33"></span>[34] R. Zhang et al. Tip-adapter: Training-free clip-adapter for better vision-language modeling. *arXiv preprint arXiv:2111.03930*, 2021.
- <span id="page-17-34"></span>[35] J. Ding et al. Decoupling zero-shot semantic segmentation. In *CVPR*, pp. 11583–11592, 2022.
- <span id="page-17-35"></span>[36] X. Gu et al. Open-vocabulary object detection via vision and language knowledge distillation. In *ICLR*, 2021.
- <span id="page-17-36"></span>[37] Y. Du et al. Learning to prompt for open-vocabulary object detection with vision-language model. In *CVPR*, pp. 14084– 14093, 2022.
- <span id="page-17-37"></span>[38] D. G. Lowe. Distinctive image features from scale-invariant keypoints. *IJCV*, 60:91–110, 2004.
- <span id="page-17-38"></span>[39] L. Breiman. Random forests. *Machine learning*, 45:5–32, 2001.
- <span id="page-17-39"></span>[40] J. Deng et al. Imagenet: A large-scale hierarchical image database. In *CVPR*, pp. 248–255. Ieee, 2009.
- <span id="page-17-40"></span>[41] K. He et al. Masked autoencoders are scalable vision learners. In *CVPR*, pp. 16000–16009, 2022.
- <span id="page-17-41"></span>[42] A. Singh et al. Flava: A foundational language and vision alignment model. In *CVPR*, pp. 15638–15650, 2022.
- <span id="page-17-42"></span>[43] M. Tschannen et al. Image-and-language understanding from pixels only. *arXiv preprint arXiv:2212.08045*, 2022.
- <span id="page-17-43"></span>[44] J. Jang et al. Unifying vision-language representation space with single-tower transformer. In *AAAI*, volume 37, pp. 980–988, 2023.
- <span id="page-17-44"></span>[45] L. Yao et al. Detclip: Dictionary-enriched visual-concept paralleled pre-training for open-world detection. In *NeurIPS*.
- <span id="page-17-45"></span>[46] H. Luo et al. Segclip: Patch aggregation with learnable centers for open-vocabulary semantic segmentation. *arXiv preprint arXiv:2211.14813*, 2022.
- <span id="page-17-46"></span>[47] S. Antol et al. Vqa: Visual question answering. In *ICCV*, pp. 2425–2433, 2015.
- <span id="page-17-47"></span>[48] A. Suhr et al. A corpus for reasoning about natural language grounded in photographs. *arXiv preprint arXiv:1811.00491*, 2018.
- <span id="page-17-48"></span>[49] A. Karpathy et al. Deep fragment embeddings for bidirectional image sentence mapping. *NeurIPS*, 27, 2014.
- <span id="page-17-49"></span>[50] F. Li et al. Vision-language intelligence: Tasks, representation learning, and large models. *arXiv preprint arXiv:2203.01922*, 2022.
- <span id="page-17-50"></span>[51] Y. Du et al. A survey of vision-language pre-trained models. *arXiv preprint arXiv:2202.10936*, 2022.
- <span id="page-17-51"></span>[52] F.-L. Chen et al. Vlp: A survey on vision-language pre-training. *Machine Intelligence Research*, 20(1):38–56, 2023.
- <span id="page-17-52"></span>[53] P. Xu et al. Multimodal learning with transformers: A survey. *arXiv preprint arXiv:2206.06488*, 2022.
- <span id="page-17-53"></span>[54] X. Wang et al. Large-scale multi-modal pre-trained models: A comprehensive survey. *arXiv preprint arXiv:2302.10035*, 2023.
- <span id="page-17-54"></span>[55] S. Ren et al. Faster r-cnn: Towards real-time object detection with region proposal networks. *NeurIPS*, 28, 2015.
- <span id="page-17-55"></span>[56] L.-C. Chen et al. Deeplab: Semantic image segmentation with deep convolutional nets, atrous convolution, and fully connected crfs. *TPAMI*, 40(4):834–848, 2017.
- <span id="page-17-56"></span>[57] A. Dosovitskiy et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020.
- <span id="page-17-57"></span>[58] A. Vaswani et al. Attention is all you need. *NeurIPS*, 30, 2017.

- <span id="page-18-0"></span>[59] M. Tan and Q. Le. Efficientnet: Rethinking model scaling for convolutional neural networks. In *ICML*, pp. 6105–6114. PMLR, 2019.
- <span id="page-18-1"></span>[60] T. He et al. Bag of tricks for image classification with convolutional neural networks. In *CVPR*, pp. 558–567, 2019.
- <span id="page-18-2"></span>[61] R. Zhang. Making convolutional networks shift-invariant again. In *ICML*, pp. 7324–7334. PMLR, 2019.
- <span id="page-18-3"></span>[62] N. Carion et al. End-to-end object detection with transformers. In *ECCV*, pp. 213–229. Springer, 2020.
- <span id="page-18-4"></span>[63] E. Xie et al. Segformer: Simple and efficient design for semantic segmentation with transformers. *NeurIPS*, 34:12077–12090, 2021.
- <span id="page-18-5"></span>[64] N. Mu et al. Slip: Self-supervision meets language-image pretraining. In *ECCV*, pp. 529–544. Springer, 2022.
- <span id="page-18-6"></span>[65] J. Yang et al. Unified contrastive learning in image-text-label space. In *CVPR*, pp. 19163–19173, 2022.
- <span id="page-18-7"></span>[66] K. He et al. Masked autoencoders are scalable vision learners. *arXiv preprint arXiv:2111.06377*, 2021.
- <span id="page-18-8"></span>[67] L. H. Li et al. Grounded language-image pre-training. In *CVPR*, pp. 10965–10975, 2022.
- <span id="page-18-9"></span>[68] A. v. d. Oord et al. Representation learning with contrastive predictive coding. *arXiv preprint arXiv:1807.03748*, 2018.
- <span id="page-18-10"></span>[69] P. Khosla et al. Supervised contrastive learning. 33:18661–18673, 2020.
- <span id="page-18-11"></span>[70] H. Bao et al. Beit: Bert pre-training of image transformers. *arXiv preprint arXiv:2106.08254*, 2021.
- <span id="page-18-12"></span>[71] Z.-Y. Dou et al. Coarse-to-fine vision-language pre-training with fusion in the backbone. In *NeurIPS*.
- <span id="page-18-13"></span>[72] H. Bao et al. Vlmo: Unified vision-language pre-training with mixture-of-modality-experts. *arXiv preprint arXiv:2111.02358*, 2021.
- <span id="page-18-18"></span>[73] V. Ordonez et al. Im2text: Describing images using 1 million captioned photographs. *NeurIPS*, 24, 2011.
- <span id="page-18-19"></span>[74] X. Chen et al. Microsoft coco captions: Data collection and evaluation server. *arXiv preprint arXiv:1504.00325*, 2015.
- <span id="page-18-20"></span>[75] B. Thomee et al. Yfcc100m: The new data in multimedia research. *Communications of the ACM*, 59(2):64–73, 2016.
- <span id="page-18-21"></span>[76] R. Krishna et al. Visual genome: Connecting language and vision using crowdsourced dense image annotations. *IJCV*, 123(1):32– 73, 2017.
- <span id="page-18-22"></span>[77] P. Sharma et al. Conceptual captions: A cleaned, hypernymed, image alt-text dataset for automatic image captioning. In *ACL*, pp. 2556–2565, 2018.
- <span id="page-18-23"></span>[78] J. Pont-Tuset et al. Connecting vision and language with localized narratives. In *ECCV*, pp. 647–664. Springer, 2020.
- <span id="page-18-24"></span>[79] S. Changpinyo et al. Conceptual 12m: Pushing web-scale imagetext pre-training to recognize long-tail visual concepts. In *CVPR*, pp. 3558–3568, 2021.
- <span id="page-18-25"></span>[80] K. Srinivasan et al. Wit: Wikipedia-based image text dataset for multimodal multilingual machine learning. In *ACM SIGIR*, pp. 2443–2449, 2021.
- <span id="page-18-26"></span>[81] K. Desai et al. Redcaps: Web-curated image-text data created by the people, for the people. *arXiv preprint arXiv:2111.11431*, 2021.
- <span id="page-18-27"></span>[82] J. Gu et al. Wukong: 100 million large-scale chinese cross-modal pre-training dataset and a foundation framework. *arXiv preprint arXiv:2202.06767*, 2022.
- <span id="page-18-28"></span>[83] X. Chen et al. Pali: A jointly-scaled multilingual language-image model. *arXiv preprint arXiv:2209.06794*, 2022.
- <span id="page-18-14"></span>[84] J. Lee et al. Uniclip: Unified framework for contrastive languageimage pre-training. In *NeurIPS*.
- <span id="page-18-15"></span>[85] S. Shao et al. Objects365: A large-scale, high-quality dataset for object detection. In *ICCV*, pp. 8430–8439, 2019.
- <span id="page-18-16"></span>[86] A. Kamath et al. Mdetr-modulated detection for end-to-end multi-modal understanding. In *ICCV*, pp. 1780–1790, 2021.
- <span id="page-18-17"></span>[87] M. Cao et al. Image-text retrieval: A survey on recent research and development. *arXiv preprint arXiv:2203.14713*, 2022.
- <span id="page-18-34"></span>[88] Y. LeCun et al. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998.
- <span id="page-18-35"></span>L. Fei-Fei et al. Learning generative visual models from few training examples: An incremental bayesian approach tested on 101 object categories. In *CVPR workshop*, pp. 178–178. IEEE, 2004.
- <span id="page-18-29"></span>[90] M. Everingham et al. The pascal visual object classes (voc) challenge. *IJCV*, 88(2):303–338, 2010.
- <span id="page-18-36"></span>[91] M.-E. Nilsback and A. Zisserman. Automated flower classification over a large number of classes. In *ICVGIP*, pp. 722–729. IEEE, 2008.
- <span id="page-18-37"></span>[92] Y. Netzer et al. Reading digits in natural images with unsupervised feature learning. 2011.

- <span id="page-18-38"></span>[93] A. Coates et al. An analysis of single-layer networks in unsupervised feature learning. In *AISTATS*, pp. 215–223. JMLR Workshop and Conference Proceedings, 2011.
- <span id="page-18-39"></span>[94] J. Stallkamp et al. The german traffic sign recognition benchmark: a multi-class classification competition. In *IJCNN*, pp. 1453–1460. IEEE, 2011.
- <span id="page-18-40"></span>[95] A. Mishra et al. Scene text recognition using higher order language priors. In *BMVC-British machine vision conference*. BMVA, 2012.
- <span id="page-18-41"></span>[96] S. Maji et al. Fine-grained visual classification of aircraft. *arXiv preprint arXiv:1306.5151*, 2013.
- <span id="page-18-42"></span>[97] I. J. Goodfellow et al. Challenges in representation learning: A report on three machine learning contests. In *ICONIP*, pp. 117– 124. Springer, 2013.
- <span id="page-18-43"></span>[98] R. Socher et al. Recursive deep models for semantic compositionality over a sentiment treebank. In *EMNLP*, pp. 1631–1642, 2013.
- <span id="page-18-44"></span>[99] M. Cimpoi et al. Describing textures in the wild. In *CVPR*, pp. 3606–3613, 2014.
- <span id="page-18-45"></span>[100] T. Berg et al. Birdsnap: Large-scale fine-grained visual categorization of birds. In *CVPR*, pp. 2011–2018, 2014.
- <span id="page-18-46"></span>[101] G. Cheng et al. Remote sensing image scene classification: Benchmark and state of the art. *Proceedings of the IEEE*, 105(10):1865– 1883, 2017.
- <span id="page-18-47"></span>[102] J. Johnson et al. Clevr: A diagnostic dataset for compositional language and elementary visual reasoning. In *CVPR*, pp. 2901– 2910, 2017.
- <span id="page-18-48"></span>[103] B. S. Veeling et al. Rotation equivariant cnns for digital pathology. In *International Conference on Medical image computing and computer-assisted intervention*, pp. 210–218. Springer, 2018.
- <span id="page-18-49"></span>[104] P. Helber et al. Eurosat: A novel dataset and deep learning benchmark for land use and land cover classification. *JSTARS*, 12(7):2217–2226, 2019.
- <span id="page-18-50"></span>[105] P. Young et al. From image descriptions to visual denotations: New similarity metrics for semantic inference over event descriptions. *TACL*, 2:67–78, 2014.
- <span id="page-18-51"></span>[106] T.-Y. Lin et al. Microsoft coco: Common objects in context. In *ECCV*, pp. 740–755. Springer, 2014.
- <span id="page-18-52"></span>[107] A. Gupta et al. Lvis: A dataset for large vocabulary instance segmentation. In *CVPR*, pp. 5356–5364, 2019.
- <span id="page-18-53"></span>[108] C. Li et al. Elevater: A benchmark and toolkit for evaluating language-augmented visual models. *arXiv preprint arXiv:2204.08790*, 2022.
- <span id="page-18-54"></span>[109] R. Mottaghi et al. The role of context for object detection and semantic segmentation in the wild. In *CVPR*, pp. 891–898, 2014.
- <span id="page-18-30"></span>[110] M. Cordts et al. The cityscapes dataset for semantic urban scene understanding. In *CVPR*, pp. 3213–3223, 2016.
- <span id="page-18-55"></span>[111] B. Zhou et al. Scene parsing through ade20k dataset. In *CVPR*, pp. 633–641, 2017.
- <span id="page-18-32"></span>[112] B. Wu et al. Data efficient language-supervised zero-shot recognition with optimal transport distillation. In *ICLR*, 2021.
- <span id="page-18-31"></span>[113] Y. Li et al. Supervision exists everywhere: A data efficient contrastive language-image pre-training paradigm. In *ICLR*, 2021.
- <span id="page-18-33"></span>[114] Q. Cui et al. Contrastive vision-language pre-training with limited resources. In *ECCV*, pp. 236–253. Springer, 2022.
- <span id="page-18-57"></span>[115] L. Yuan et al. Florence: A new foundation model for computer vision. *arXiv preprint arXiv:2111.11432*, 2021.
- <span id="page-18-56"></span>[116] Y. Gao et al. Pyramidclip: Hierarchical feature alignment for vision-language model pretraining. *arXiv preprint arXiv:2204.14095*, 2022.
- <span id="page-18-58"></span>[117] A. Yang et al. Chinese clip: Contrastive vision-language pretraining in chinese. *arXiv preprint arXiv:2211.01335*, 2022.
- <span id="page-18-59"></span>[118] X. Zhai et al. Lit: Zero-shot transfer with locked-image text tuning. In *CVPR*, pp. 18123–18133, 2022.
- <span id="page-18-60"></span>[119] Z. Chen et al. Altclip: Altering the language encoder in clip for extended language capabilities. *arXiv preprint arXiv:2211.06679*, 2022.
- <span id="page-18-61"></span>[120] B. Ko and G. Gu. Large-scale bilingual language-image contrastive learning. *arXiv preprint arXiv:2203.14463*, 2022.
- <span id="page-18-62"></span>[121] J. Zhou et al. Non-contrastive learning meets language-image pre-training. *arXiv preprint arXiv:2210.09304*, 2022.
- <span id="page-18-63"></span>[122] S. Shen et al. K-lite: Learning transferable visual models with external knowledge. *arXiv preprint arXiv:2204.09222*, 2022.
- <span id="page-18-64"></span>[123] R. Huang et al. Nlip: Noise-robust language-image pre-training. *arXiv preprint arXiv:2212.07086*, 2022.

- <span id="page-19-5"></span>[124] S. Geng et al. Hiclip: Contrastive language-image pretraining with hierarchy-aware attention. *arXiv preprint arXiv:2303.02995*, 2023.<br>[125] C.-W. Xie et al.
- <span id="page-19-1"></span>Ra-clip: Retrieval augmented contrastive language-image pre-training. In *CVPR*, pp. 19265–19274, 2023.
- <span id="page-19-2"></span>[126] L. Fan et al. Improving clip training with language rewrites. *arXiv preprint arXiv:2305.20088*, 2023.
- <span id="page-19-3"></span>[127] K. Yang et al. Alip: Adaptive language-image pre-training with synthetic caption. In *ICCV*, pp. 2922–2931, 2023.
- <span id="page-19-4"></span>[128] X. Deng et al. Growclip: Data-aware automatic model growing for large-scale contrastive language-image pre-training. In *ICCV*, pp. 22178–22189, 2023.
- <span id="page-19-0"></span>[129] J. Xu et al. Groupvit: Semantic segmentation emerges from text supervision. In *CVPR*, pp. 18134–18144, 2022.
- <span id="page-19-6"></span>[130] K. Ranasinghe et al. Perceptual grouping in vision-language models. *arXiv preprint arXiv:2210.09996*, 2022.
- <span id="page-19-7"></span>[131] Y. Zhong et al. Regionclip: Region-based language-image pretraining. In *CVPR*, pp. 16793–16803, 2022.
- <span id="page-19-8"></span>[132] C. Ma et al. Understanding and mitigating overfitting in prompt tuning for vision-language models. *arXiv preprint arXiv:2211.02219*, 2022.
- <span id="page-19-9"></span>[133] A. Bulat and G. Tzimiropoulos. Language-aware soft prompting for vision & language foundation models. *arXiv preprint arXiv:2210.01115*, 2022.
- <span id="page-19-10"></span>[134] Y. Lu et al. Prompt distribution learning. In *CVPR*, pp. 5206–5215, 2022.
- <span id="page-19-11"></span>[135] M. M. Derakhshani et al. Variational prompt tuning improves generalization of vision-language models. *arXiv preprint arXiv:2210.02390*, 2022.
- <span id="page-19-12"></span>[136] B. Zhu et al. Prompt-aligned gradient for prompt tuning. *arXiv preprint arXiv:2205.14865*, 2022.
- <span id="page-19-13"></span>[137] X. He et al. Cpl: Counterfactual prompt learning for vision and language models. *arXiv preprint arXiv:2210.10362*, 2022.
- <span id="page-19-14"></span>[138] G. Chen et al. Prompt learning with optimal transport for visionlanguage models. *arXiv preprint arXiv:2210.01253*, 2022.
- <span id="page-19-15"></span>[139] X. Sun et al. Dualcoop: Fast adaptation to multi-label recognition with limited annotations. In *NeurIPS*.
- <span id="page-19-16"></span>[140] Z. Guo et al. Texts as images in prompt tuning for multi-label image recognition. *arXiv preprint arXiv:2211.12739*, 2022.
- <span id="page-19-17"></span>[141] K. Ding et al. Prompt tuning with soft context sharing for visionlanguage models. *arXiv preprint arXiv:2208.13474*, 2022.
- <span id="page-19-18"></span>[142] Y. Rao et al. Denseclip: Language-guided dense prediction with context-aware prompting. In *CVPR*, pp. 18082–18091, 2022.
- <span id="page-19-19"></span>[143] T. Huang et al. Unsupervised prompt learning for visionlanguage models. *arXiv preprint arXiv:2204.03649*, 2022.
- <span id="page-19-20"></span>[144] M. Shu et al. Test-time prompt tuning for zero-shot generalization in vision-language models. In *NeurIPS*.
- <span id="page-19-21"></span>[145] H. Yao et al. Visual-language prompt tuning with knowledgeguided context optimization. In *CVPR*, pp. 6757–6767, 2023.
- <span id="page-19-22"></span>[146] T.-Y. Wu et al. Protect: Prompt tuning for hierarchical consistency. *arXiv preprint arXiv:2306.02240*, 2023.
- <span id="page-19-23"></span>[147] H. Bahng et al. Exploring visual prompts for adapting large-scale models. *arXiv preprint arXiv:2203.17274*, 1(3):4, 2022.
- <span id="page-19-24"></span>[148] J. Rong et al. Retrieval-enhanced visual prompt learning for fewshot classification. *arXiv preprint arXiv:2306.02243*, 2023.
- <span id="page-19-25"></span>[149] Y. Zang et al. Unified vision and language prompt learning. *arXiv preprint arXiv:2210.07225*, 2022.
- <span id="page-19-26"></span>[150] S. Shen et al. Multitask vision-language prompt tuning. *arXiv preprint arXiv:2211.11720*, 2022.
- <span id="page-19-27"></span>[151] M. U. Khattak et al. Maple: Multi-modal prompt learning. *arXiv preprint arXiv:2210.03117*, 2022.
- <span id="page-19-28"></span>[152] Y. Xing et al. Class-aware visual prompt tuning for visionlanguage pre-trained model. *arXiv preprint arXiv:2208.08340*, 2022.
- <span id="page-19-29"></span>[153] O. Pantazis et al. Svl-adapter: Self-supervised adapter for visionlanguage pretrained models. *arXiv preprint arXiv:2210.03794*, 2022
- <span id="page-19-30"></span>[154] V. Udandarao et al. Sus-x: Training-free name-only transfer of vision-language models. *arXiv preprint arXiv:2211.16198*, 2022.
- <span id="page-19-31"></span>[155] J. Kahana et al. Improving zero-shot models with label distribution priors. *arXiv preprint arXiv:2212.00784*, 2022.
- <span id="page-19-32"></span>[156] F. Peng et al. Sgva-clip: Semantic-guided visual adapting of vision-language models for few-shot image classification. *arXiv preprint arXiv:2211.16191*, 2022.
- <span id="page-19-33"></span>[157] R. Zhang et al. Vt-clip: Enhancing vision-language models with visual-guided texts. *arXiv preprint arXiv:2112.02399*, 2021.

- <span id="page-19-34"></span>[158] Z. Guo et al. Calip: Zero-shot enhancement of clip with parameter-free attention. *arXiv preprint arXiv:2209.14169*, 2022.
- <span id="page-19-35"></span>[159] T. Yu et al. Task residual for tuning vision-language models. In *CVPR*, pp. 10899–10909, 2023.
- <span id="page-19-36"></span>[160] S. Pratt et al. What does a platypus look like? generating customized prompts for zero-shot image classification. *arXiv preprint arXiv:2209.03320*, 2022.
- <span id="page-19-37"></span>[161] S. Menon and C. Vondrick. Visual classification via description from large language models. *arXiv preprint arXiv:2210.07183*, 2022.
- <span id="page-19-38"></span>[162] M. Wortsman et al. Robust fine-tuning of zero-shot models. In *CVPR*, pp. 7959–7971, 2022.
- <span id="page-19-39"></span>[163] C. Zhou et al. Extract free dense labels from clip. In *ECCV*, pp. 696–712. Springer, 2022.
- <span id="page-19-40"></span>[164] J. Li et al. Masked unsupervised self-training for zero-shot image classification. *arXiv preprint arXiv:2206.02967*, 2022.
- <span id="page-19-41"></span>[165] P. Liu et al. Pre-train, prompt, and predict: A systematic survey of prompting methods in natural language processing. *ACM Computing Surveys*, 55(9):1–35, 2023.
- <span id="page-19-42"></span>[166] M. Jia et al. Visual prompt tuning. *arXiv preprint arXiv:2203.12119*, 2022.
- <span id="page-19-43"></span>[167] N. Houlsby et al. Parameter-efficient transfer learning for nlp. In *ICML*, pp. 2790–2799. PMLR, 2019.
- <span id="page-19-44"></span>[168] R. Zhang et al. Pointclip: Point cloud understanding by clip. In *CVPR*, pp. 8552–8562, 2022.
- <span id="page-19-45"></span>[169] T. Huang et al. Clip2point: Transfer clip to point cloud classification with image-depth pre-training. In *ICCV*, pp. 22157–22167, 2023.
- <span id="page-19-46"></span>[170] M. Xu et al. Side adapter network for open-vocabulary semantic segmentation. In *CVPR*, pp. 2945–2954, 2023.
- <span id="page-19-47"></span>[171] G. Chen et al. Tem-adapter: Adapting image-text pretraining for video question answer. In *ICCV*, pp. 13945–13955, 2023.
- <span id="page-19-48"></span>[172] T. Brown et al. Language models are few-shot learners. *NeurIPS*, 33:1877–1901, 2020.
- <span id="page-19-49"></span>[173] Y. Zang et al. Open-vocabulary detr with conditional matching. *arXiv preprint arXiv:2203.11876*, 2022.
- <span id="page-19-50"></span>[174] Z. Zhou et al. Zegclip: Towards adapting clip for zero-shot semantic segmentation. *arXiv preprint arXiv:2212.03588*, 2022.
- <span id="page-19-57"></span>[175] T. Lüddecke and A. Ecker. Image segmentation using text and image prompts. In *CVPR*, pp. 7086–7096, 2022.
- <span id="page-19-58"></span>[176] B. Li et al. Language-driven semantic segmentation. In *ICLR*, 2021.
- <span id="page-19-59"></span>[177] N. Zabari and Y. Hoshen. Semantic segmentation in-the-wild without seeing any segmentation examples. *arXiv preprint arXiv:2112.03185*, 2021.
- <span id="page-19-60"></span>[178] C. Ma et al. Open-vocabulary semantic segmentation with frozen vision-language models. *arXiv preprint arXiv:2210.15138*, 2022.
- <span id="page-19-61"></span>[179] F. Liang et al. Open-vocabulary semantic segmentation with mask-adapted clip. *arXiv preprint arXiv:2210.04150*, 2022.
- <span id="page-19-54"></span>[180] M. Xu et al. A simple baseline for open-vocabulary semantic segmentation with pre-trained vision-language model. In *ECCV*, pp. 736–753. Springer, 2022.
- <span id="page-19-55"></span>[181] G. Ghiasi et al. Scaling open-vocabulary image segmentation with image-level labels. In *ECCV*, pp. 540–557. Springer, 2022.
- <span id="page-19-62"></span>[182] G. Shin et al. Reco: Retrieve and co-segment for zero-shot transfer. In *NeurIPS*.
- <span id="page-19-63"></span>[183] J. Xie et al. Clims: Cross language image matching for weakly supervised semantic segmentation. In *CVPR*, pp. 4483–4492, 2022.
- <span id="page-19-64"></span>[184] Y. Lin et al. Clip is also an efficient segmenter: A text-driven approach for weakly supervised semantic segmentation. *arXiv preprint arXiv:2212.09506*, 2022.
- <span id="page-19-65"></span>[185] J. Qin et al. Freeseg: Unified, universal and open-vocabulary image segmentation. In *CVPR*, pp. 19446–19455, 2023.
- <span id="page-19-51"></span>[186] Z. Ma et al. Open-vocabulary one-stage detection with hierarchical visual-language knowledge distillation. In *CVPR*, pp. 14074– 14083, 2022.
- <span id="page-19-52"></span>[187] H. A. Rasheed et al. Bridging the gap between object and imagelevel representations for open-vocabulary detection. In *NeurIPS*.
- <span id="page-19-53"></span>[188] C. Feng et al. Promptdet: Towards open-vocabulary detection using uncurated images. In *ECCV*, pp. 701–717. Springer, 2022.
- <span id="page-19-56"></span>[189] M. Gao et al. Open vocabulary object detection with pseudo bounding-box labels. In *ECCV*, pp. 266–282. Springer, 2022.
- <span id="page-19-66"></span>[190] T. Wang and N. Li. Learning to detect and segment for open vocabulary object detection. *arXiv preprint arXiv:2212.12130*, 2022.
- <span id="page-19-67"></span>[191] C. Lin et al. Learning object-language alignments for openvocabulary object detection. *arXiv preprint arXiv:2211.14843*, 2022.

- <span id="page-20-7"></span>[192] W. Kuo et al. F-vlm: Open-vocabulary object detection upon frozen vision and language models. *arXiv preprint* upon frozen vision and language models. *arXiv:2209.15639*, 2022.
- <span id="page-20-0"></span>[193] X. Zhou et al. Detecting twenty-thousand classes using imagelevel supervision. In *ECCV*, pp. 350–368. Springer, 2022.
- <span id="page-20-5"></span>[194] D. Huynh et al. Open-vocabulary instance segmentation via robust cross-modal pseudo-labeling. In *CVPR*, pp. 7020–7031, 2022.
- <span id="page-20-8"></span>[195] M. Minderer et al. Simple open-vocabulary object detection with vision transformers. *arXiv preprint arXiv:2205.06230*, 2022.
- <span id="page-20-9"></span>[196] S. Zhao et al. Exploiting unlabeled data with vision and language models for object detection. In *ECCV*, pp. 159–175. Springer, 2022.
- <span id="page-20-6"></span>[197] Y. Long et al. P3ovd: Fine-grained visual-text prompt-driven self-training for open-vocabulary object detection. *arXiv preprint arXiv:2211.00849, 2022.*<br>[198] J. Xie and S. Zheng.
- <span id="page-20-1"></span>Zsd-yolo: Zero-shot yolo detection<br>lowledgedistillation. arXiv preprint using vision-language knowledgedistillation. *arXiv:2109.12066*, 2021.
- <span id="page-20-4"></span>[199] D. Kim et al. Region-aware pretraining for open-vocabulary object detection with vision transformers. In *CVPR*, pp. 11144– 11154, 2023.
- <span id="page-20-3"></span>[200] S. Wu et al. Aligning bag of regions for open-vocabulary object detection. In *CVPR*, pp. 15254–15264, 2023.
- <span id="page-20-2"></span>[201] L. Wang et al. Object-aware distillation pyramid for openvocabulary object detection. In *CVPR*, pp. 11186–11196, 2023.
- <span id="page-20-10"></span>[202] M. Cherti et al. Reproducible scaling laws for contrastive language-image learning. In *CVPR*, pp. 2818–2829, 2023.
- <span id="page-20-11"></span>[203] Y. Xian et al. Semantic projection network for zero-and few-label semantic segmentation. In *CVPR*, pp. 8256–8265, 2019.
- <span id="page-20-12"></span>[204] X. Zhai et al. Scaling vision transformers. In *CVPR*, pp. 12104– 12113, 2022.
- <span id="page-20-13"></span>[205] C. Raffel et al. Exploring the limits of transfer learning with a unified text-to-text transformer. *J. Mach. Learn. Res.*, 21(140):1–67, 2020.

# **APPENDIX**

## A. Statistics on Visual Recognition VLM Publications

As shown in Figure 1 (in the main manuscript), we count the number of visual recognition VLM publications on Google Scholar over the recent two years. Specifically, we consider all the papers that have cited the pioneer VLM study  $(i.e.,$ CLIP) as potential publications and identify a publication as the visual recognition VLM study if it contains any one of keywords image classification, object detection and semantic segmentation. For the year 2023, we project the total publications based on the number of publications from 1 Jan 2023 to 30 November 2023.

## **B. DATASETS FOR PRE-TRAINING VLM**

For VLM pre-training, multiple large-scale image-text datasets [\[10\]](#page-17-9), [\[17\]](#page-17-16), [\[20\]](#page-17-19), [\[21\]](#page-17-20) were collected from the internet. Compared with traditional crowd-labelled datasets [\[40\]](#page-17-39), [\[90\]](#page-18-29),  $[110]$ , the image-text datasets  $[10]$ ,  $[21]$  are much larger and cheaper to collect. For example, recent image-text datasets are generally at billion scale [\[20\]](#page-17-19), [\[21\]](#page-17-20), [\[83\]](#page-18-28). Beyond image-text datasets, several studies [\[19\]](#page-17-18), [\[43\]](#page-17-42), [\[45\]](#page-17-44), [\[67\]](#page-18-8) utilize auxiliary datasets to provide additional information for better vision-language modelling, e.g., GLIP [\[67\]](#page-18-8) leverages Object365 [\[85\]](#page-18-15) for extracting region-level features.

### B.1. Image-Text Datasets

- **SBU** [\[73\]](#page-18-18) contains 1M images collected from Flicker website, paired with visually relevant captions.
- **COCO Caption [\[74\]](#page-18-19)** contains over 330k images from MS COCO [\[106\]](#page-18-51). It has two versions: COCO Caption

c5 with 5 reference captions for 330k images and COCO Caption c40 that provides 40 reference captions for a randomly sampled subset of 5,000 images.

- **YFCC100M [\[75\]](#page-18-20)** is a multimedia dataset containing 99.2M images and 0.8M videos with texts.
- **VG [\[76\]](#page-18-21)** provides a multi-perspective understanding of images, e.g., object-level information, scene graphs and visual question answer pairs. VG contains 108,000 images, each with 50 descriptions.
- **CC3M [\[77\]](#page-18-22)** is an image captioning dataset which consists of about 3.3M image-text pairs from the web.
- **CC12M [\[79\]](#page-18-24)** is introduced specifically for VLM pretraining. By relaxing the data collection pipeline used in CC3M [\[77\]](#page-18-22), CC12M collects less precise but much larger size of data, i.e., 12M image-text pairs.
- **LR [\[78\]](#page-18-23)** is an image captioning dataset with local multi-modal annotations, where every word is localized in the image with a mouse trace segment. It contains 848,749 images with 873,107 captions.
- **WIT [\[80\]](#page-18-25)** is a large multi-modal multilingual dataset collected from Wikipedia, which consists of 37.6M image-text pairs across 108 languages.
- **Red Caps [\[81\]](#page-18-26)** is a image-text dataset collected from social media Reddit, which contains 12M image-text pairs covering various objects and scenes.
- **LAION400M [\[21\]](#page-17-20):** LAION400M consists of 400M image-text pairs filtered by CLIP [\[10\]](#page-17-9), which also provides the data embeddings and kNN indices.
- **LAION5B [\[20\]](#page-17-19)** contains over 5.8B image-text pairs, which consists of three parts: 2.32B English imagetext pairs, 2.26B multilingual image-text pairs and 1.27B pairs without specific language.
- **WuKong [\[82\]](#page-18-27)** is a large-scale Chinese multi-modal dataset, which contains 100M Chinese image-text pairs collected from the web.
- **CLIP [\[10\]](#page-17-9)** is a large-scale web image-text dataset, which contains 400M image-text pairs collected form a variety of publicly available sources on the internet.
- **ALIGN [\[17\]](#page-17-16)** is an image-text dataset, which contains 1.8B noisy image-text pairs covering board concepts.
- **FILIP [\[18\]](#page-17-17)** is a large-scale image-text dataset with 300M image-text pairs collected from the internet.
- **WebLI [\[83\]](#page-18-28)** is a multilingual image-text dataset collected from the web, which comprises 10B images with 12B corresponding texts across 109 languages.

#### **B.2. Auxiliary Datasets**

- **JFT3B [\[204\]](#page-20-12)** contains nearly 3B images annotated with a noisy class hierarchy of around 30k labels.
- **C4 [\[205\]](#page-20-13)** is a collection of about 750GB English text sourced from the public Common Crawl web scrape.
- **Object365 [\[85\]](#page-18-15)** is a object detection dataset with 365 categories, 638K images, and ∼10M bounding boxes.
- **Gold-G [\[86\]](#page-18-16)** is an object-phrase dataset for object detection, which includes 0.8M human-annotated visual grounding data curated by [\[86\]](#page-18-16).

# **C. DATASETS FOR EVALUATION**

Many visual recognition datasets have been adopted for VLM evaluations as shown in Table 2 (in the main

JOURNAL OF LATEX CLASS FILES, MARCH 2023 22

manuscript) including 27 image classification datasets, 4 object detection datasets, 4 semantic segmentation datasets, 2 image-text retrieval datasets and 3 action recognition datasets. Below please find the detail of each dataset.

### C.1. Datasets for Image Classification

- **Food-101 [\[22\]](#page-17-21)** is a real-world food dataset for finegrained recognition. The dataset consists of 101,000 images, covering 101 classes. Specifically, every class contains 250 cleaned test samples and 750 purposely uncleaned training samples.
- **CIFAR-10 [\[23\]](#page-17-22)** contains a set of small images, which is commonly used for the image classification tasks. This dataset includes 60000 images with size 32 by 32, annotated with ten categories. This dataset has been divided into 5000 training samples and 1000 testing samples per class.
- **CIFAR-100 [\[23\]](#page-17-22)** is almost the same as CIFAR10, except CIFAR-100 instead contains 60000 samples with 100 categories that have been grouped into twenty super-categories.
- **Birdsnap [\[100\]](#page-18-45)** is a fine-grained classification dataset collected from Flicker. There are 49,829 images belonging to 500 bird species, including 47,386 training images and 2433 testing images. In this dataset, every image has been labelled with a bounding box, the coordinates of 17 parts, and auxiliary attribute annotations like male, female, immature, etc.
- **SUN397 [\[24\]](#page-17-23)** is proposed for scene recognition and contains 39700 images covering 397 well-sampled categories. The scene classification performance by human is provided as the reference for the comparisons with computational methods.
- **Stanford Cars [\[25\]](#page-17-24)** is designed for fine-grained recognition, containing 16185 images covering 196 categories. This dataset has been divided into 8,144 training samples and 8,041 testing samples.
- **FGVC Aircraft [\[96\]](#page-18-41)** includes 10K samples spanning 100 aircraft model variants. Every samples is labeled with a tight bounding box and a hierarchical category annotation. This dataset has been equally separated into training, validation, and test subsets, where every subset contains 33 or 34 images per variant.
- **PASCAL VOC 2007 Classification [\[90\]](#page-18-29)** is the widelyused dataset for various visual recognition tasks like detection, segmentation, and classification. There are 9963 samples covering 20 classes, including 5011 training images and 4952 testing images. Every sample in PASCAL VOC 2007 contains pixel-wise labels, object-level labels with object box and category labels.
- **Describable Textures [\[99\]](#page-18-44) (DTD)** is a collection of textural images for image recognition. This dataset includes 5640 samples with forty seven categories, which has been equally separated into training, validation, and test subsets, where each subset contains 40 images per class. For each image, the main category and a list of the joint attributes are provided.

- **Oxford-IIIT PETS [\[26\]](#page-17-25)** includes 7,349 cat and dog images with thirty seven different breeds, in which twenty five are dog breeds and twelve are cat breeds. These samples are separated into the training subset with around 1850 samples, the validation subset with about 1850 samples and testing subset with approximate 3700 samples. Every sample has been annotated with a breed annotation, a pixel-wise annotation that marks the body, and a rectangle box for locating the head.
- **Caltech-101 [\[89\]](#page-18-35)** consists of 9145 images belonging to 101 classes, Every category includes 40-80 images. For each image, the dataset provides an annotation to segment the foreground object.
- **Oxford 102 Folwers [\[91\]](#page-18-36)** is proposed for fine-grained image classification. This dataset contains 8189 flowers images that belong to 102 species. Each category contains 40-200 samples, including the flower captured under various sizes and illumination environments. Besides, this dataset also contains pixel-wise annotations.
- **Facial Emotion Recognition 2013 [\[97\]](#page-18-42)** is collected by requesting images associated with 184 key emotional terms from Google. The dataset contains 35,887 grayscale images with a resolution of 48x48 pixels and with 7 types of emotions.
- **STL-10 [\[93\]](#page-18-38)** is a type of classification benchmark for researching on unsupervised and self-taught training. It includes 10 categories and an unsupervised training subset with 100K samples, a supervised training subset with 5K samples, and a testing subset with 8K samples.
- **EuroSAT [\[104\]](#page-18-49)** is a set of satellite images used to benchmark the land use and land cover recognition tasks. It covers thirteen frequency bands with ten categories of 27K annotated and geo-referenced samples. Two datasets are provided including the RGB image dataset and the multi-spectral image dataset.
- **RESISC45 [\[101\]](#page-18-46)** has been proposed to benchmark Remote Sensing Image Scene Classification (RESISC). This dataset includes 31,500 sample with the image size of 256 by 256 and forty five scene categories, every category containing 700 samples. Besides, RE-SISC45 covers a wide range of spatial resolutions from 20cm to over 30m per pixel.
- **GTSRB [\[94\]](#page-18-39)** is a dataset for traffic signs classification, containing 50,000 images taken from various street scenes in Germany. It is classified into 43 categories, including a training subset with 39,209 samples and a testing subset with 12,630 samples.
- **Country211 [\[10\]](#page-17-9)** is an image classification dataset for geolocation evaluation, which is a subset of the YFCC100M dataset. For each country, there are one hundred and fifty train samples, fifty validation samples, and one hundred test samples.
- **PatchCamelyon [\[103\]](#page-18-48)** includes 327,680 RGB images with the size of 96 by 96 from Camelyon16, with a training subset with 75% samples, a validation subset with 12.5% samples, and a testing subset with 12.5% samples. Every sample has been labelled with a binary annotation showing if it contains the metastatic

tissue.

- **Hateful Memes [\[27\]](#page-17-26)** has been proposed for hateful meme classification  $(i.e.,$  image with text) created by Facebook AI. It includes over 10k memes annotated with either the hateful label or the non-hateful label.
- **Rendered SST2 [\[10\]](#page-17-9)** has been proposed for benchmarking optical character recognition. It includes 2 categories (the categoryies of positives and negatives). This dataset has been separated into 3 subsets: a training subset with 6920 samples, a validation subset with 872 samples, and a test subset with 1821 samples.
- **ImageNet-1k [\[40\]](#page-17-39)** includes about 1.2M samples that are uniformly distributed across the one thousand categories. The category annotation of ImageNet-1k follows WordNet hierarchy and every sample is annotated with one category label. Bisides, ImageNet-1k is one of the most popular image classification benchmarks.
- **CLEVR Counts [\[102\]](#page-18-47)** is a subset of CLEVR dataset, which is designed for visual question answering to evaluate the ability to perform visual reasoning. The counting tasks include 2000 training samples and 500 test samples.
- **SVHN [\[92\]](#page-18-37)** is a dataset for recognizing digits and numbers in real-world images which are collected from Google Street View images. It consists of about 600,000 images and all digits are cropped from the images and resized to a fixed resolution of 32x32 pixels.
- **IIIT5k [\[95\]](#page-18-40)** contains 5,000 cropped word images collected from Google image search by using search keyword such as signboards, house name plates, and movie posters, etc. The dataset is split into two parts, i.e., 2,000 word images for training and 3,000 word images for validation, respectively.
- **Rendered SST2 [\[98\]](#page-18-43)** is sentiment classification dataset which consists of two sentiment categories, i.e., negative and positive. The sentences in the dataset are extracted from Stanford Sentiment Treebank dataset.

### C.2. Datasets for Action Recognition

- **UCF101 [\[29\]](#page-17-28)** has been proposed for benchmarking human action recognition with videos. It includes about 13k video clips of 101 actions, which are collected from YouTube. The video clips in the dataset have a resolution of 320x240 pixels and a frame rate of 25 FPS.
- **Kinetics700 [\[30\]](#page-17-29)** is a video dataset for recognizing human action. It consists of about 65,000 video clips with 700 human actions, where each action category has more than 700 video clips lasting around 10 seconds.
- **RareAct** [\[28\]](#page-17-27) is a video dataset designed for identifying rare actions such as "Unplug Oven" and "fry phone". This dataset aims to evaluate action recognition models on unlikely combinations of common action verbs and object nouns. It contains 122 human

actions, where the verbs and object nouns in actions are rarely co-occurring together in HowTo100M.

### C.3. Datasets for Semantic Segmentation

- **ADE20k [\[111\]](#page-18-55)** is a semantic segmentation dataset which consists of 150 classes. It consists of a training subset with 25,574 samples and a validation subset with 2,000 samples.
- **PASCAL VOC 2012 Segmentation [\[90\]](#page-18-29)** contains 20 categories including vehicles, household and animals. This dataset includes a training subset with 1,464 samples and a testing subset with 1,449 samples, all of which are with pixel-wise annotations.
- **PASCAL Content [\[109\]](#page-18-54):** PASCAL Content is an extension of PASCAL VOC 2010 detection dataset [\[90\]](#page-18-29), which contains more than 400 categories with pixelwise annotations. It has 4,998 training images and 1,449 validation images.
- **Cityscapes [\[110\]](#page-18-30):** Cityscapes is a dataset for the visual recognition on street scenes. This dataset includes a training subset with 2,975 samples and a testing subset with 500 samples, all of which are with pixel-wise annotations of 19 categories.

### C.4. Datasets for Object Detection

- **MS COCO [\[106\]](#page-18-51):** MS COCO Dataset is a dataset for object detection. It consists of two versions: MS COCO 2014 contains 83,000 training images and 41,000 validation images with bounding box annotations of 80 categories and MS COCO 2017 contains 118,000 training images and 5,000 validation images with bounding box annotations of 80 categories.
- **ODinW [\[108\]](#page-18-53):** ODinW is a benchmark to evaluate the task-level transfer ability of pre-trained vision models, which consist of 35 free public Object Detection datasets in various domains. The dataset contains 132k training images and 20K testing images belonging to 314 concepts. Also, many of the 35 tasks have very limited (less than 100) training images, which makes it extremely difficult for standard detectors without any pre-training.
- **LVIS [\[107\]](#page-18-52):** LVIS is a large vocabulary dataset for long-tailed instance detection/ segmentation. The dataset contains 1203 categories with federated human annotations on 100K images.

### C.5. Datasets for Image and Text Retrieval

- **Flickr30k [\[105\]](#page-18-50):** Flickr30K is a dataset for automatic image description and grounded language understanding. It contains 31,000 images collected from Flickr, where each image is provided with 5 captions.
- **COCO Caption [\[74\]](#page-18-19):** COCO Caption contains over 330k images from MS COCO [\[106\]](#page-18-51). It has two versions: COCO Caption c5 with 5 reference captions for 330k images and COCO Caption c40 that provides 40 reference captions for a randomly sampled subset of 5,000 images.

#### JOURNAL OF LATEX CLASS FILES, MARCH 2023 24

**Jingyi Zhang** received her B.Sc. degree in electronic information science and technology from the University of Electronic Science and Technology of China (UESTC) and M.Sc. degree in signal processing from the Nanyang Technological University (NTU). She is currently a Research Associate and Ph.D. student with School of Computer Science and Engineering, NTU. Her research interests include computer vision, object detection.

**Jiaxing Huang** received his B.Eng. and M.Sc. in EEE from the University of Glasgow, UK, and the Nanyang Technological University (NTU), Singapore, respectively. He is currently a Research Associate and Ph.D. student with School of Computer Science and Engineering, NTU, Singapore. His research interests include computer vision and machine learning.

**Sheng Jin** is currently a Research Fellow at Nanyang Technology University (NTU), Singapore. Before that, he received his B.Sc. degree in Applied Mathematics from Harbin Institute of Technology and the Ph.D. degree in Computer Science and Technology om Harbin Institute of Technology. His research interests include computer vision and machine learning.

**Shijian Lu** is an Associate Professor with the School of Computer Science and Engineering at the Nanyang Technological University, Singapore. He received his PhD in electrical and computer engineering from the National University of Singapore. His major research interests include image and video analytics, visual intelligence, and machine learning.
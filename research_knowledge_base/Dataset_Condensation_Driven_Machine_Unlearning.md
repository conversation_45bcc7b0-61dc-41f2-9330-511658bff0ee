# Dataset Condensation Driven Machine Unlearning

Junaid <PERSON><PERSON><PERSON>ail: <EMAIL>

*Abstract*—The current trend in data regulation requirements and privacy-preserving machine learning has emphasized the importance of machine unlearning. The naive approach to unlearning training data by retraining over the complement of the forget samples is susceptible to computational challenges. These challenges have been effectively addressed through a collection of techniques falling under the umbrella of machine unlearning. However, there still exists a lack of sufficiency in handling persistent computational challenges in harmony with the utility and privacy of unlearned model. We attribute this to the lack of work on improving the computational complexity of approximate unlearning from the perspective of the training dataset. In this paper, we aim to fill this gap by introducing dataset condensation as an essential component of machine unlearning in the context of image classification. To achieve this goal, we propose new dataset condensation techniques and an innovative unlearning scheme that strikes a balance between machine unlearning privacy, utility, and efficiency. Furthermore, we present a novel and effective approach to instrumenting machine unlearning and propose its application in defending against membership inference and model inversion attacks. Additionally, we explore a new application of our approach, which involves removing data from 'condensed model', which can be employed to quickly train any arbitrary model without being influenced by unlearning samples. The corresponding code is available at [URL.](https://github.com/algebraicdianuj/DC_U)

*Index Terms*—Machine Unlearning, Dataset Condensation, Neural Networks, Image Classification.

## I. INTRODUCTION

The significance of the machine unlearning has already been established and well described in relationship to international data regulations like the 'the right to be forgotten' [\[1\]](#page-6-0) clause in General Data Protection Regulation (GDPR) [\[2\]](#page-6-1). Besides the main task of removing the user data from model unlearning, machine unlearning has found applications in other areas of privacy preserving machine learning like mitigating bias [\[3\]](#page-6-2), mitigating backdoor attacks [\[4\]](#page-6-3) etc. On the other hand, while this topic is still in its infancy, machine unlearning as privacy solution as been studied to be vulnerable to other kind of privacy attacks [\[5\]](#page-6-4), [\[6\]](#page-6-5). In any case, any unlearning algorithm is supposed to design to compete with the effects of naive unlearning approach of retraining on the remaining dataset (not including the samples to be forgotten) from scratch, but with additional caveat that the designed unlearning algorithm should be much more efficient. This target has been extensively studied to be achieved under classifications of 'exact machine unlearning' and 'approximate machine unlearning', where the associated technique exactly or approximately mimic the effects of naive unlearning, respectively. Its sister approach, namely 'catastrophic forgetting' [\[7\]](#page-6-6), which involves fine-tuning a pre-trained model over subset of the training dataset, and the model starts under performing upon the compliment of that subset. However, catastrophic forgetting has been treated more a challenges in machine learning, especially in incremental learning, than a commodity. However, the techniques under 'approximate machine unlearning' have been gaining much more popularity since they are computationally much more efficient than their exact unlearning counterparts and have been shown to be successful in approaching the metrics of naive unlearning.

Despite the popularity of approximate machine unlearning algorithms, they suffer from high margin between efficiency (the amount of time for unlearning algorithm completion), privacy (protection against adversary to infer the forgotten data from unlearned model) and utility (preservation of performance of unlearned model on retain dataset). One important illustration of this challenge is the work done in [\[8\]](#page-6-7), where a close form expression for difference between original and unlearning parameters is derived, assuming the distance between them is sufficiently small, but leads to a computationally expensive solution involving Hessians, which may not be applicable at all for large models. If we take step back, and focus on the potential of other domains of machine unlearning within approximate machine unlearning, then there have been several techniques studied to be beneficial for utility and efficacy perspective of unlearning, like distillation [\[3\]](#page-6-2), [\[9\]](#page-7-0), model pruning [\[10\]](#page-7-1) etc. Until now, this line of work has been centered around the model perspective. In other words, the unlearning algorithm have predominantly focused on modifying either the model's loss function or its parameters. In this paper, we take a digression and focus on dataset, as well as, model centeric machine unlearning scheme, which aims to fill the gap in unlearning literature to find a good median between privacy, utility and efficiency of approximate unlearning algorithm. More specifically, we design new dataset condensation techniques to reduce the training fodder for unlearning, and new unlearning scheme, which we term 'modular unlearning' to further accelerate unlearning via catastrophic forgetting. To simply describe the modularized training, we essentially split the model into three parts and train them seperately, the consequence of which is that middle part requires minimum epochs to achieve catastrophic forgetting. We also metricize this unlearning in two new ways, namely via 'unlearning' and 'overfitting' metric. Lastly, we envision our algorithm towards two new and important applications.

We summarize our major contributions as follows:

- We propose two new dataset condensation techniques as means to reduce the size of compliment of forget samples (retain dataset) for the training part of unlearning.
- We propose modularized unlearning, focused toward image classification task, which splits the pre-trained model into three parts and seperately trains them using the

Image /page/1/Figure/1 description: The image displays two distinct frameworks related to machine learning dataset management. The left side, labeled "Retain Dataset Reduction Framework," illustrates an offline phase where a training dataset is processed through unsupervised feature clustering-based grouping and dataset condensation. This results in K-groups and K-images, which are then collected via a protocol to form a reduced retain dataset, while some samples are marked as "Forget Samples." The right side, titled "Modular Unlearning Framework," depicts a more complex process involving a "Main Model" that takes input and produces output through modularization. This framework utilizes a testing dataset and remembrance samples, along with a training dataset and a reduced retain dataset. It features an offline phase with L-iterations and an online phase with S-iterations. Within these phases, different colored squares (yellow, green, blue) represent stages of training, with arrows indicating the flow and reset points. Some squares are marked with an asterisk, indicating "Trainable Only," and red curved arrows signify "Train 1 iteration." The framework details conditional iterations based on parameters like \u03b11, \u03b12, \u03b13, j, S, and \u03c4.

Fig. 1. Main abstraction of Proposed Scheme

reduced retain dataset.

- We propose two new metrics to measure unlearning, namely 'unlearning' and 'overfitting' metric.
- We propose two new applications of proposed unlearning. First one provides the defense of membership inference attack as a competitor of differentially private (DP)-Adam based training [\[11\]](#page-7-2), [\[12\]](#page-7-3). Second one allows removal of information from forget samples from an autoencoder, which when augmented with any new model can lead to fast learning.
- We conduct extensive experiments and show that our unlearning methodology finds a good balance between unlearning privacy, utility and efficiency, as compared to state of the art approximate unlearning approaches.

## II. RELATED WORKS

The problem of machine unlearning, i.e. to find a fast alternative to naive retraining, is non-trivial. For example, an obvious approach of gradient ascent over forget samples can quickly fail [\[13\]](#page-7-4). The current machine unlearning algorithms can be broadly divided into 'exact' and 'approximate' machine unlearning algorithms.

Exact machine unlearning attempts to emulate retraining, but in an optimized manner. However, the collection of techniques are still pertinent to computational and scalability challenges. One important work in this regard is partitioning of datasets into multiple subsets, which are themselves partioned as well. This is followed by the individual training of independent models on each discrete subset, and the outputs are ensembled [\[14\]](#page-7-5). The first machine unlearning work [\[15\]](#page-7-6) also falls with in this abstraction, where by converting the machine learning system to summation form, the unlearning request updates few of the summation terms.

Approximate unlearning algorithm achieve either a certified or a heuristically justifiable approach to achieve the effects of naive retraining with significant efficiency advantage. One of the main strategies in this regard is to be parameter focussed, and to either subtract the parameter updates due to batch per epoch gradient updates from forget samples in the prior training scheme [\[13\]](#page-7-4), [\[16\]](#page-7-7), [\[17\]](#page-7-8) or performing single step updates via gradients [\[18\]](#page-7-9) or Hessians [\[19\]](#page-7-10), [\[20\]](#page-7-11). Another important and rather ubiquitous strategy is to focus on training (fine-tuning) on forget dataset to achieve good unlearning privacy evaluations, and train on retain dataset to achieve competitive unlearning utility metrics [\[3\]](#page-6-2), [\[9\]](#page-7-0), [\[21\]](#page-7-12). A recent trend has been to find intersection of other branches of deep learning like adversarial attacks [\[22\]](#page-7-13), model sparsity/ pruning [\[10\]](#page-7-1) and model distillation [\[3\]](#page-6-2), [\[9\]](#page-7-0), with unlearning, which are shown to be promising as in improving the perepoch unlearning capacity as compared to naive retraining, and thus in this way, also improving upon unlearning efficiency. Model privacy is mostly metricized via membership inference attack [\[3\]](#page-6-2), [\[10\]](#page-7-1), [\[23\]](#page-7-14), which is the simplest attack in privacypreserving machine literature that aims to infer the probability whether a particular sample was using in training of model or not. Another way that has been shown to depict the unlearning privacy is via model inversion attack [\[24\]](#page-7-15), [\[25\]](#page-7-16), where one attempts to reconstruct the training data using the trained model.

The research question of whether large dataset can be reduced into smaller samples (so as to say condensing dataset), which then trained on arbitrary model would lead to similar accuracy as that of the original dataset, has been of great interest in recent years. Under the umberella of dataset condensation, the techniques for condensing dataset solely rely on convex optimization of the random images, such that either gradient of model trained on them and on the original dataset [\[26\]](#page-7-17), or the distance between distribution of pretrained model's features on them and the original dataset [\[27\]](#page-7-18), or the distance between parameter states over training trajectories when trained on them and on original dataset [\[28\]](#page-7-19), is minimized. Whilst there have been several improvements upon these strategies [\[29\]](#page-7-20)– [\[31\]](#page-7-21), a persistent major hurdle in their quick rapid adoption for downstream deep learning applications, including unlearning, is the associated computational bottleneck.

## III. PRELIMINARIES AND NOTATION

We define the original dataset as  $\mathcal{D} = \{T_i, l_i\}_{i=1}^{N_D}$ , where  $T_i$  is the  $i^{th}$  training image and  $l_i$  is the  $i^{th}$  label.  $\overline{\mathcal{D}}$  can be partitioned into the forget dataset  $\mathcal{F} = \{T_{f_i}, l_{f_i}\}_{i=1}^{N_F}$  and retain

Algorithm 1: Unsupervised feature clustering (Kmeans) based grouping

**Input:** Training images  $T = \bigcup_{i=1}^{c} \bigcup_{j=1}^{n} T_{ij}$ , and a pre-trained network M **Output:** Image clusters  $\bigcup_{i=1}^{c} \bigcup_{j=1}^{K} \Gamma_{ij}$ Image Clusters  $C = \{\};$ foreach *class* i do  $F_i = \mathcal{M}_{\text{features}}(\bigcup_{j=1}^{n} T_{ij}) = \bigcup_{j=1}^{n} \mathcal{M}_{\text{features}}(T_{ij});$ <br>Perform K-means clustering on  $F_i$  resulting in clusters  $\bigcup_{j=1}^{K} \Gamma_{ij}$  of the training images;  $C = C \cup \bigcup_{j=1}^K \Gamma_{ij};$ return *C*

# Algorithm 2: Dataset Condensation via Fast Distribution Matching

**Input:** Image Clusters  $C = \bigcup_{i=1}^{c} \bigcup_{j=1}^{K} \Gamma_{ij}$ , epochs E **Output:** Condensed Images  $C = \bigcup_{i=1}^{c} \bigcup_{j=1}^{K} \phi_{ij}$  $C = \{\};$ foreach *cluster* Γ<sub>ij</sub> do Initialize weighted average function W with parameters  $\omega \in \mathbb{R}^{\lvert \Gamma_{ij} \rvert}$ ;  $epoch = 1$ : while  $epoch \leq E$  do  $\mathcal{L} =$  $\|\text{mean}(\mathcal{M}_{\text{features}}(\Gamma_{ij})) - \text{mean}(\mathcal{M}_{\text{features}}(W(\Gamma_{ij})))\|_2;$ Optimize  $\omega$  by backpropagating the  $\mathcal{L}$ ;  $\phi_{ij} = W(\Gamma_{ij});$  $C = C \cup \phi_{ij};$  $epoch = epoch + 1;$ return C

dataset  $\mathcal{R} = \{T_{r_i}, l_{r_i}\}_{i=1}^{N_R}$ . Here,  $\mathcal{F} \cup \mathcal{R} = \mathcal{D}$ , and  $\mathcal{F} \cap \mathcal{R} = \emptyset$ . For the sake of representation, we represent the images in  $D$ as  $T = \bigcup_{i=1}^{c} \bigcup_{j=1}^{n} T_{ij}$ , where c is the total number of classes in D, n is the number of images per class such that  $N_D = nc$ and  $T_{ij}$  represents the image in  $i^{th}$  class and  $j^{th}$  index. The forget set  $\mathcal F$  represents the part of the training dataset  $\mathcal D$  to be forgotten by a model, trained on  $D$  using loss function  $\mathcal{L}_{CE}$ . Let's call this trained model as  $\mathcal{M}_{\theta}$  with trained parameters  $\theta$ . The goal of the dataset reduction framework is to reduce  $\mathcal{R} \rightarrow \mathcal{R}_{\text{red}}$ , such that  $|\mathcal{R}_{\text{red}}| < |\mathcal{R}|$ . Within the dataset reduction framework, the images of each class are grouped into K clusters, such that  $K < n$ . Finally, after the unlearning

#### IV. METHODOLOGY

procedure, the model  $\mathcal M$  gains parameters  $\theta^*$ .

In our methodology, we propose two frameworks. The first framework provides the minimum amount of training data for unlearning, in form of reduced retain dataset. The second framework performs the unlearning by using reduced retain dataset. Both of these frameworks have an offline and online phase, where the offline phase happens prior to actual unlearning phase, in a reasonably fast manner. The online phase happens during each unlearning cycle.

### A. Retain Dataset Reduction Framework

This framework comprises of an offline and online phase. The offline phase condeses the whole training dataset  $D$  into condensed form. During online phase, which happens during each unlearning cycle, the collection protocol takes in the condensed training dataset, and forget dataset  $\mathcal F$  to filter out a reduced dataset  $\mathcal{R}_{\text{red}}$ .

Algorithm 3: Image Condensation via Model Inversion

**Input:** Image Clusters  $C = \bigcup_{i=1}^{c} \bigcup_{j=1}^{K} \Gamma_{ij}$  with individual cluster labels  $l_{ij}$  for cluster images  $l_{ij}$  with original labels  $l_i$ , epochs  $\overline{E}$ , regularization parameter  $\lambda$  and pre-trained network M **Output:** Condensed Images  $C = \bigcup_{i=1}^{c} \bigcup_{j=1}^{K} \phi_{ij}$ Create InverterNet  $\Lambda: l_{ij} \to \Gamma_{ij}$  with parameters  $\theta_{\Lambda}$ ; Make  $M$  parameters  $\theta_M$  untrainable; Compose  $\Lambda$  and  $\mathcal M$  as  $\mathcal M\Lambda$  :  $l_{ij} \to l_i$ ;  $epoch = 1$ : while  $epoch \leq E$  do  $\mathcal{L} = \mathcal{L}_{CE}(\mathcal{M}\Lambda(l_{ij}), l_i) + \lambda \mathcal{L}_{MSE}(\Lambda(l_{ij}), \Gamma_{ij});$ Optimize  $\theta_{\Lambda}$  by backpropagating the  $\mathcal{L}$ ;  $epoch = epoch + 1;$  $C = \{\};$ foreach *cluster label*  $l_{ij}$  do  $\phi_{ij} = \Lambda(l_{ij});$  $\mathcal{C} = \mathcal{C} \cup \phi_{ij};$ return C

1) Offline Phase: For each  $i^{th}$  class in the dataset  $D$ , the images  $\bigcup_{j=1}^n T_{ij}$  is grouped into  $\bigcup_{j=1}^K \Gamma_{ij}$  using algorithm-1, where  $\Gamma_{ij}$  represents a  $j^{th}$  cluster of images for the  $i^{th}$ class, and  $|\overline{\Gamma}_{ij}| = \frac{n}{K}$  (assuming that the clustering algorithm leads to clusters of equal sizes). For each cluster  $\Gamma_{ij}$ , we assign cluster label  $l_{ij} = j l_i$ , such that each image in  $\Gamma_{ij}$  has label  $l_{ij}$ . Over all clusters, i.e.  $\bigcup_{i=1}^{c} \bigcup_{j=1}^{K} \Gamma_{ij}$ , we condense each cluster into a single image, either via our proposed fast distribution matching and model inversion.

Algorithm 4: Collection Protocol **Input:** Image clusters  $C = \bigcup_{i=1}^{c} \bigcup_{j=1}^{K} \Gamma_{ij}$ , condensed images  $\mathcal{C} = \bigcup_{i=1}^{c} \bigcup_{j=1}^{K} \phi_{ij}$ , forget dataset  $\mathcal{F} = \{T_{f_i}, l_{f_i}\}_{i=1}^{N_F}$ Output: Reduced retain dataset R<sub>red</sub>  $\mathcal{R}_{\text{red}} = \{\};$ **foreach** *forget image*  $T_{f_i}$  **do** foreach *cluster*  $\Gamma_{ij}$  do if  $T_{f_{\underline{i}}} \notin \Gamma_{ij}$  then  $\begin{aligned} \boxed{\phantom{0}}\mathcal{R}_{\rm red} = \mathcal{R} \cup (\phi_{ij}, l_i); \end{aligned}$ else  $\mathcal{R}_{\text{red}} = \mathcal{R} \cup (\Gamma_{ij} \setminus T_{f_i}, l_i);$ return R

*2) Dataset Condensation via Fast Distribution Matching:* Contrary to original distribution matching approach based condensation [\[27\]](#page-7-18) where images were optimized, we focus on optimizing the weighted average of images in cluster  $\Gamma_{ij}$ , leading to drastically low trainable parameter count  $\frac{n}{K}$  contrary to parameter count as  $n$  times the product of dimensions of training images, as in [\[27\]](#page-7-18). The images within each  $\Gamma_{ij}$  is condensed into a single image through a trainable weighted average, and the weights are optimized by matching the mean of distribution of features associated with condensed image, and the original image, assuming that features follow a gaussian distribution. This process is summarized in algorithm-2.

*3) Dataset Condensation via Model Inversion:* In model inversion attack [\[24\]](#page-7-15), we essentially find the inverse mapping for a given model, which can map output to input. Inspired by this, we create a new deep learning model, called 'InverterNet' Λ, for the given pretrained model  $M$ , such that the composition of these models maps  $l_{ij}$  to  $l_i$ , i.e.,  $\mathcal{M}\Lambda : l_{ij} \to l_i$ . It should be noted that if  $K = 1$ , then  $M\Lambda$  is an identity mapping, and  $\Lambda$  would form an inverse mapping of  $\mathcal{M}$ . Afterwards, the composition  $M\Lambda$  is trained on the cluster labels, original labels, and original images, such that the standard crossentropy loss is regularized with the reconstruction error of the InverterNet from the original images. After training, the condensed images with respect to each label  $l_i$  is collected by getting outputs of  $\Lambda$  over  $l_{ij}$ . This procedure is described in algorithm-3.

### B. Online Phase

In the online phase, the unlearning requests are aggregated to form forget dataset. Through the collection protocol acting on forget dataset, image clusters and the condensed dataset, we retrieve the reduced retain dataset for unlearning scheme of size  $N_r$ , whose size is much smaller than the original retain dataset, in time complexity equivalent to that of retrieving original retain dataset.

In collection protocol, if the forget dataset images are not found in image cluster  $\Gamma_{ij}$ , then the corresponding condensed image with label is collected. Otherwise, we collect the residual retain dataset images with in the cluster, other than the forget dataset images. If we assume that the number of images in all  $cK$  clusters is same, then under assumption that forget samples are randomly distributed through out the dataset, and thus through clusters, one can develop as asymptotic bound of the compression ratio of reduced retain dataset, defined as  $\eta_T = \frac{N_T}{N_R}$ , through application of the collection protocol as.

$$
\eta_T = (1 - \frac{1}{cK})^{N_D - cK} \frac{cK}{N_R} + \mathcal{O}\left((\frac{N_D}{N_R} - 1)(1 - \frac{1}{cK})(\frac{N_D}{cK} - 1)\right)
$$
\n(1)

Under the same assumptions, we can determine that for  $\eta_T$  < 1, then following inequality to hold.

$$
N_R > N_D - cK \log(cK) + 1 \tag{2}
$$

These assumptions are only valid if the unsupervised clustering algorithm partitions data into  $K$  clusters of equal sizes. For the adoption of this this work, we only restricted to k-means clustering which partitions data into K-clusters of possibly unequal sizes, based on distance in feature domain. On the other hand, there exists techniques like startified Kmeans clustering functions to partition into clusters of equal sizes.

### C. Modular Training

In neural networks, especially in convolution ones [\[32\]](#page-7-22), the output of layers progressively becomes translation-invariant from shallow to deep layers. This implies that shallower layers have relatively more information of input [\[33\]](#page-7-23), [\[34\]](#page-7-24), thus more vulnerable to model inversion attack. On the other hand, the deeper layers [\[34\]](#page-7-24), [\[35\]](#page-7-25) have more information of output, and thus more vulnerable to membership inference attack. In light of this observation, we attempt to partition a neural network into three parts, by grouping the layers from shallow to deeper part into compartments, namely beginning, intermediate and final respectively. These compartments are trained separately in a systematic manner, which we call 'modular training', to achieve certain privacy and efficiency goals. This partitioning is depicted in figure-1. We devise an offline and online phase to modular training to achieve unlearning.

*1) Offline Phase:* In the offline phase, we first sample M images per label from the testing set and call them remembrance samples, where  $M$  is small, e.g. 1-10. Then in the each of  $R$  iterations, we follow three steps. In the first step, we reset the parameters of **final** to original weights as that of pretrained model  $M$ . In second step, the final is only kept trainable, and trained over remembrance samples. In third step, only intermediate is kept trainable and trained on the original dataset, where the **beginning**, being dense in information of training images, acts a feature extractor to intermediate.

The remembrance training over final has two main objectives. First we reduce the vulnerability to membership inference attack to deeper layers [\[35\]](#page-7-25), as previously mentioned. Secondly, we induce the application of the resultant neural network towards a situation that, if the parameters of final or even its architecture is arbitrarly changed, then training only the final on remembrance samples (which are quite few) can regain back the accuracy.

*2) Online Phase:* In online phase, which actively performs unlearning, we assign  $S$  iterations, in which we perform training of beginning and final in two steps. In the first step, we train the **beginning** only for 1 epoch on reduced retain dataset, while in second step, we train final on remembrance samples for  $S_1$  iterations. We introduce a condition to perform second step, if the current iteration of  $S$  iterations is less than S- $\tau$ , where  $0 < \tau < S$  is a hyperparameter, which is designed to not degrade the accuracy of retain dataset, in later stage of  $S$  iterations. By training the **beginning**, we are generally reducing the FLOPs associated with training, since the gradient of loss of neural network is highly sensitive to gradient of shallow layers. After  $S$  iterations, we perform 1 step training of intermediate over reduced retain dataset. Because in the offline phase, the intermediate was trained with beginning acting as the feature extractor, the training knowledge was attempted to concentrate in intermediate. By re-modifying the feature extractor, i.e. beginning, the knowledge of intermediate is rendered obsolete, and thus with even 1 iteration, there is immediate catastrophic forgetting. This effect has been seen in transfer learning [\[36\]](#page-7-26) frequently, but never attributed to catastrophic forgetting. To empirically verify this, we show that gradient of intermediate of modularized unleaning model after S iteration is much spread out as compared to normal fine-tuning model's intermediate.

#### D. Instrumentation of Unlearning

For an unlearned model, if its distance in parameters space from original model  $M$  is not large, then 'roughly' the gradient of loss of unlearning model over retain dataset is orthogonal to that of forget dataset , i.e. the associated dot product is zero. We utilize this proposition in metricizing our unlearning scheme's performance as unlearning metric, by computing cosine similarity between the corresponding gradients, and subtracting it from 1, i.e  $1 - \frac{\nabla_{\theta} \mathcal{L}(\mathcal{D}_R) \cdot \nabla_{\theta} \mathcal{L}(\mathcal{D}_F)}{\|\nabla_{\theta} \mathcal{L}(\mathcal{D}_R) \| \|\nabla_{\theta} \mathcal{L}(\mathcal{D}_F)}$  $\|\nabla_{\theta}\mathcal{L}(\mathcal{D}_R)\|_2 \|\nabla_{\theta}\mathcal{L}(\mathcal{D}_F)\|_2$ We compute it as percentage for sake of interpretation.

Overfitting can be seen as the divergence of loss of model over unseen data, while loss of the model on training data is very small. We develope a heuristic metric, which we call overfitting metric, to analyze the overfitting in model from training data perspective as  $|(\mathcal{L}(D, \theta)) - \text{mean}(|\nabla_{\theta} \mathcal{L}(D, \theta)|)| \in \mathbb{R}$ , where D is some input-output pair in  $\mathcal{D}$ , and  $\nabla_{\theta}$  is the gradient with respect to parameters  $\theta$ . Smaller value of this metric would imply higher degree to which model is overfitted on data D.

We also propose a white-box model inversion attack, to visualize the information of training dataset  $D$  from unlearned model, by simply employing algorithm-3, and the  $K$  condensed images per class depict the reconstructions of training images per class from unlearned model.

### E. Applications of Unlearning

We propose two new applications for our proposed unlearning schemes.

*1) Defense Against Membership Inference Attack:* Membership inference attack has been largely linked to overfitting of model on training dataset [\[23\]](#page-7-14), [\[37\]](#page-7-27). Inspired by this, we connect unlearning as a tool to improve membership inference defense, by unlearning to some extent over training data subset, that is more overfitted than remaining. To this extent, we compute overfitting metric over the whole dataset, and perform Otsu binarization over the values of overfitting metric to find the subset of  $D$  that are relatively more overfitted. Then we perform unlearning over few epochs on the detected overfitting samples to achieve defense against membership inference attack.

*2) Unlearning in Dataset Condensation:* One of the characteristics of modular unlearning is in the flexibility of changing the architecture of parameters of final, and retraining on few remembrance samples can allow quick regain of accuracy. In light of this, we propose a route of new dataset condensation strategy that results in a 'condensed model', rather than images, as condensed representation. More precisely, in our modular training's offline part, we form an autoencoder topology on the beginning and intermediate, i.e. the input and output dimensions of beginning-intermediate composition is same. The output of the this structure over the remembrance samples can be used a substitute for condensed images. Hence, the condensed model can be combined with any new deep learning architecture (which would serve the role of final), and it can be quickly trained over the remembrance samples to gain accuracy over the original dataset. The reason for this route of dataset condensation is that this strategy of dataset condensation allows unlearning, which is not feasible for image-driven dataset condensation, while at the same time being very fast and accurate, but at the cost of increasing the parameter count of any new deep learning model it is applied to. Simply by performing the offline and online phase

Image /page/4/Figure/7 description: This image contains three line graphs plotted against epochs. The first graph, on the left, is titled "Unlearning Metric" and shows the performance of "Catastrophic", "Modular", and "Retraining" methods. The "Catastrophic" line (blue) starts at approximately 88.4 and fluctuates, ending at 88.3. The "Modular" line (green) starts at 93.5 and stays relatively stable, ending at 93.3. The "Retraining" line (red) starts at 96.2 and decreases slightly, ending at 95.7. The middle graph is titled "Overfitting Metric" and also plots the three methods against epochs. The "Catastrophic" line (blue) starts at 1.27 and peaks at 1.42 at epoch 2, then drops to 1.27 at epoch 3, and ends at 1.5. The "Modular" line (green) starts at 1.4, peaks at 1.65 at epoch 2, drops to 1.48 at epoch 3, and ends at 1.5. The "Retraining" line (red) starts at 1.83 and drops to 1.46 at epoch 3, then ends at 1.49. The third graph, on the right, is titled "MIA Score" and plots the same three methods. The "Catastrophic" line (blue) starts at 60.5 and fluctuates between 60.5 and 61.5. The "Modular" line (green) starts at 57.8, drops to 56.7 at epoch 2, and then increases to 58.4 at epoch 4, ending at 57.6. The "Retraining" line (red) starts at 50.3 and stays relatively stable between 50 and 51, ending at 50.9.

Fig. 2. Evolution of UM, OM and MIA for first first epochs of modular unlearning and catastrophic forgetting over VGG16 on CIFAR10

of our unlearning scheme with the assumptions of beginning, intermediate and final, and at the end replacement of final with a new deep learning model leads to augmented model which when trained on remembrance samples rapidly leads to model that is approximately equivalent to same model if it was trained only on retain dataset.

## V. PERFORMANCE EVALUATION

### A. Experimental Settings

.

*1) Datasets:* We conduct experiments over CIFAR-10 and SVHN, as in [\[14\]](#page-7-5) to evaluate unlearning performance as well as developing applications in image classification task.

*2) Unlearning Baselines:* We implement following approximate unlearning baselines for the unlearning performance comparison. First one is **Retraining**  $(R)$ , where we train the randomly initialized model on the retain dataset, as the naive unlearning method. In Catastrophic Forgetting (CF), we train the pre-trained model on retain dataset. Inspired by [\[3\]](#page-6-2), we implement **Distillation** (D) based unlearning only focus on the distilling the given model on retain dataset, without the increasing the KL-divergence over forget dataset, which then leads to increase to MIA score, which then is compensated by 'Rewinding' procedure. This is sufficient as we are exploring applications discussed in [\[3\]](#page-6-2), where negative KL-divergences are needed. Another distillation based unlearning methodology we implement isBad Teacher based Distillation (BD) [\[9\]](#page-7-0), where we utilize competent (pretrained model) and incompetent (randomly initialized model) teachers to minimize the weighted KL-divergence between student and the two teachers, over the forget dataset, and randomly sampled retain dataset. In Sparisity Regularized Unlearning (S) [\[10\]](#page-7-1) we basically perform catastrophic forgetting with regularization loss by  $\|\theta\|_1$ , where  $\theta$  is the vector containing parameters of the neural network [\[10\]](#page-7-1). We also adopt Pruning and then unlearning (P+U) [\[10\]](#page-7-1), we perform model prunning via synaptic flow [\[38\]](#page-7-28) on the pretrained model, and then perform unlearning. For the unlearning part, we simply train the resultant prunned model on retain set.

*3) Implementations:* We implemented the baselines and all experimentation in Python 3.10.12, within the framework of Pytorch library v2.1.0, in Windows Subsystem for Linux (WSL2) and GPU hardware as NVIDIA GeForce RTX 3090. For all experiments involving training, we use Adam optimizer with fixed learning rate. The architecture wise hyperparameters of the unlearning models are shown in table-1.

| M           | RA          | <b>FA</b> | MIA         | UT          | RA     | FA    | <b>MIA</b>       | UT    | RA     | FA            | <b>MIA</b>       | UT     | RA    | FA         | <b>MIA</b>    | UT     | <b>RBE</b> |
|-------------|-------------|-----------|-------------|-------------|--------|-------|------------------|-------|--------|---------------|------------------|--------|-------|------------|---------------|--------|------------|
|             | CIFAR10+MLP |           |             | CIFAR10+CNN |        |       | CIFAR10+ResNet18 |       |        | CIFAR10+VGG16 |                  |        |       |            |               |        |            |
| R           | 93.56       | 50.40     | 49.54       | 16.51       | 96.12  | 75.31 | 51.04            | 43.07 | 98.87  | 82.50         | 50.21            | 241.31 | 98.34 | 77.54      | 50.14         | 138.29 | 0.63       |
| CF          | 96.85       | 77.90     | 63.01       | 16.56       | 98.45  | 90.24 | 56.80            | 41.91 | 99.20  | 84.96         | 50.96            | 240.63 | 98.80 | 88.12      | 54.15         | 138.38 | 0.88       |
| D           | 95.99       | 55.00     | 51.42       | 20.61       | 98.91  | 79.51 | 50.66            | 50.00 | 100.00 | 86.98         | 51.79            | 310.37 | 89.48 | 73.00      | 49.46         | 174.88 | 0.87       |
| <b>BD</b>   | 76.64       | 35.76     | 53.61       | 8.37        | 93.59  | 63.00 | 73.43            | 21.16 | 70.35  | 15.02         | 76.08            | 154.34 | 86.67 | 25.04      | 77.76         | 87.16  | 1.31       |
| s           | 91.98       | 61.66     | 55.59       | 18.52       | 99.16  | 85.57 | 54.53            | 43.11 | 98.99  | 84.90         | 49.81            | 243.34 | 98.84 | 84.04      | 51.75         | 147.79 | 0.78       |
| $P+U$       | 75.61       | 52.86     | 51.71       | 18.01       | 100.00 | 83.77 | 54.01            | 46.61 | 89.03  | 76.88         | 50.46            | 241.91 | 99.54 | 91.14      | 55.94         | 146.41 | 0.97       |
| MU          | 89.49       | 60.70     | 55.37       | 14.92       | 91.72  | 85.93 | 56.85            | 31.31 | 94.84  | 86.02         | 53.20            | 169.90 | 90.41 | 81.82      | 54.44         | 90.95  | 0.80       |
|             | SVHN+MLP    |           |             | SVHN+CNN    |        |       | SVHN+ResNet18    |       |        | SVHN+VGG16    |                  |        |       |            |               |        |            |
| $\mathbf R$ | 93.52       | 81.20     | 51.67       | 14.82       | 99.87  | 91.88 | 50.48            | 24.88 | 99.46  | 92.20         | 49.83            | 215.69 | 99.56 | 90.73      | 50.10         | 124.85 | 0.61       |
| <b>CF</b>   | 96.65       | 87.68     | 55.57       | 15.12       | 99.81  | 96.66 | 53.13            | 25.11 | 99.74  | 93.75         | 50.71            | 216.07 | 99.73 | 96.73      | 52.60         | 124.66 | 0.69       |
| D           | 95.03       | 83.15     | 51.12       | 18.62       | 99.77  | 92.62 | 50.36            | 37.63 | 99.99  | 95.37         | 51.37            | 279.18 | 99.68 | 91.26      | 50.22         | 157.71 | 0.83       |
| BD          | 83.59       | 69.68     | 48.85       | 7.47        | 95.91  | 60.75 | 87.63            | 18.80 | 91.80  | 17.53         | 90.15            | 139.01 | 97.94 | 27.64      | 91.90         | 78.85  | 0.82       |
| s           | 93.45       | 83.88     | 50.87       | 16.59       | 99.44  | 93.04 | 51.10            | 28.63 | 99.90  | 93.73         | 49.97            | 218.90 | 99.75 | 93.35      | 50.92         | 131.46 | 0.66       |
| $P+U$       | 92.07       | 85.11     | 51.61       | 16.34       | 100.00 | 97.80 | 53.78            | 27.70 | 69.66  | 65.13         | 51.33            | 219.35 | 19.78 | 18.68      | 49.88         | 134.11 | 0.88       |
| MU          | 93.32       | 84.51     | 52.87       | 14.08       | 97.30  | 94.86 | 52.86            | 23.81 | 97.72  | 95.00         | 51.74            | 148.69 | 94.13 | 88.97      | 50.44         | 74.19  | 0.48       |
|             |             |           |             |             |        |       |                  |       |        |               |                  |        |       |            |               |        |            |
| M           | RA          | FA        | <b>MIA</b>  | UT          | RA     | FA    | <b>MIA</b>       | UT    | RA     | <b>FA</b>     | <b>MIA</b>       | UT     | RA    | FA         | <b>MIA</b>    | UT     | <b>RBE</b> |
|             |             |           | CIFAR10+MLP |             |        |       | CIFAR10+CNN      |       |        |               | CIFAR10+ResNet18 |        |       |            | CIFAR10+VGG16 |        |            |
| R           | 86.62       | 0.00      | 91.34       | 17.29       | 92.76  | 0.00  | 94.00            | 17.38 | 95.34  | 0.00          | 94.40            | 80.46  | 90.64 | 0.00       | 93.57         | 46.06  | 0.62       |
| CF          | 96.86       | 0.02      | 84.22       | 10.19       | 99.39  | 0.98  | 83.96            | 18.32 | 97.61  | 0.00          | 93.10            | 80.43  | 65.88 | 0.00       | 92.26         | 46.11  | 0.69       |
| D           | 79.81       | 0.06      | 79.74       | 7.58        | 94.06  | 0.02  | 84.32            | 22.06 | 99.99  | 9.12          | 84.39            | 103.71 | 90.35 | 0.00       | 92.19         | 58.45  | 0.83       |
| BD          | 87.29       | 25.08     | 55.52       | 2.77        | 98.91  | 1.24  | 88.16            | 9.92  | 80.89  | 0.12          | 89.96            | 51.63  | 97.48 | 24.62      | 88.29         | 29.24  | 0.82       |
| s           | 95.28       | 0.02      | 83.21       | 10.06       | 99.34  | 3.24  | 79.46            | 18.89 | 96.04  | 0.00          | 92.56            | 81.25  | 97.23 | 0.00       | 90.22         | 48.69  | 0.66       |
| $P+U$       | 71.71       | 0.00      | 92.33       | 9.90        | 94.95  | 0.00  | 92.98            | 18.75 | 79.68  | 0.00          | 94.67            | 83.10  | 99.68 | 0.00       | 88.22         | 50.81  | 0.87       |
| MU          | 88.42       | 36.76     | 49.19       | 4.07        | 91.51  | 64.78 | 47.31            | 4.77  | 92.23  | 82.66         | 52.52            | 8.79   | 94.74 | 91.96      | 57.05         | 5.29   | 0.47       |
|             |             |           | SVHN+MLP    |             |        |       | SVHN+CNN         |       |        |               | SVHN+ResNet18    |        |       | SVHN+VGG16 |               |        |            |
| R           | 87.17       | 0.00      | 90.26       | 5.17        | 99.31  | 0.00  | 92.72            | 8.03  | 97.88  | 0.00          | 93.31            | 72.00  | 97.77 | 0.00       | 92.95         | 41.56  | 1.32       |
| CF          | 96.59       | 0.02      | 86.2        | 5.15        | 99.72  | 7.51  | 79.88            | 7.98  | 98.02  | 0.00          | 93.92            | 72.08  | 99.76 | 0.00       | 85.34         | 41.43  | 1.15       |
| D           | 88.91       | 12.46     | 73.05       | 6.31        | 98.91  | 1.55  | 85.25            | 10.16 | 99.99  | 53.00         | 73.16            | 93.17  | 98.36 | 0.00       | 91.21         | 52.66  | 1.24       |
| <b>BD</b>   | 91.72       | 14.02     | 78.23       | 2.63        | 95.91  | 1.42  | 91.02            | 4.97  | 94.51  | 7.40          | 91.75            | 46.49  | 99.69 | 3.24       | 91.28         | 26.37  | 1.13       |
| s           | 94.20       | 0.08      | 86.55       | 5.95        | 99.63  | 27.53 | 72.40            | 9.16  | 98.75  | 0.00          | 94.01            | 72.97  | 99.80 | 0.00       | 88.27         | 43.63  | 1.17       |
| $P+U$       | 88.85       | 0.02      | 89.38       | 6.06        | 100.00 | 38.28 | 69.54            | 9.07  | 95.08  | 0.00          | 94.05            | 74.15  | 47.58 | 0.00       | 78.02         | 45.26  |            |

TABLE I

BENCHMARK OF REFERENCE UNLEARNING ALGORITHMS AND OURS IN CASE OF RANDOM IMAGE FORGETTING (FIRST TABLE) THE 10 PERCENT OF TOTAL TRAINING DATASET AND CLASS FORGETTING OF IMAGES (SECOND TABLE)

Image /page/5/Figure/3 description: The image displays a comparison of model performance and image reconstructions. Part (a) is a radar chart titled "Performance Comparison" which compares three models: "Pre-trained" (blue line), "DP-Trained" (orange line), and "MU-Regularized" (green line). The chart has axes labeled "TrA", "M-PPV", "M-A", and "TeA", with radial lines indicating percentages from 25% to 100%. Part (b) shows three sets of image reconstructions, labeled "Original Model MI Reconstruction", "DP-Model MI Reconstruction", and "MU-Model MI Reconstruction". Each set contains 30 small images arranged in a 5x6 grid, categorized by object type at the top: "cat", "deer", "frog", "bird", "ship", and "plane". The reconstructions vary in clarity and detail across the different models and object types.

Fig. 3. Proposed model inversion attack based reconstruction of images per class of CIFAR-10 dataset from original model, model training with differentiallyprivate Adam based optimization and proposed unlearning based regularization of model

*4) Metrics:* In order to elucidate on utility from the unlearning procedure, we utilize metrics like accuracy of unlearning model over retain dataset as (RA), accuracy over forget dataset as FA, total time (in seconds) for unlearning algorithm completion and membership inference attack accuracy as MIA by building a logistic classifier over losses of the unlearning model over forget and test dataset.

In order to rank the unlearning performance on previous all four metrics for a single dataset, by associating weights to RA, FA, MIA, and UT scores as 1, 0.5, 1 and 1 respectively. Here we first calculated absolute difference of the previous metric evaluations from the best metric found for each metric. Then performing min-max normalization of the resultant score over all score evaluations on the same dataset, then apply weighted averaging the RA, FA, MIA and UT scores. Afterwards, averaging the results for each model to getting a relative notion of performance measure, called relative best error, which should be small as possible.

In order the compute the membership inference attack accuracy of model over whole dataset  $D$ , we employ shadow model based strategy [\[23\]](#page-7-14), where the shadow models are multi-layer perceptrons (MLP), and the final classifier is a

Image /page/6/Figure/0 description: A bar chart displays the values of three metrics: modular unlearning, remembrance retraining, and retraining from scratch. The x-axis is labeled "Metrics" and shows the categories MIA, RA, FA, TA, and UT. The y-axis is labeled "Values" and ranges from 0 to 350. For MIA, modular unlearning is approximately 58, remembrance retraining is approximately 57, and retraining from scratch is approximately 50. For RA, modular unlearning is approximately 98, remembrance retraining is approximately 97, and retraining from scratch is approximately 100. For FA, modular unlearning is approximately 70, remembrance retraining is approximately 67, and retraining from scratch is approximately 85. For TA, modular unlearning is approximately 58, remembrance retraining is approximately 55, and retraining from scratch is approximately 80. For UT, modular unlearning is approximately 60, and retraining from scratch is approximately 330. A dashed line indicates a value of 0.51 between the modular unlearning bar and the retraining from scratch bar for the UT metric.

Fig. 4. Benchmarking of Unlearning in Condensation setting, where the goal is to unlearn the data from condensed knowledge which can be quickly used to train another model

logistic regressor. In addition, we also compute training and test dataset accuracy TrA and TeA for specific cases.

#### B. Balance Between Major Unlearning Metrics

We present the comparison of metrics RA, FA, MIA and UT over reference unlearning algorithm, and ours in table-1, where we attempt to forget either 10 percent of training dataset in the case of random forgetting or attempting to forget entire class in case of classwise forgetting. It can be observed that while our proposed unlearning methodology does not surpasses in all metrics over other approaches, it finds a good median between all metrics, while other approaches fail to do so. In order to quickly metricize this, we tabulated the RBE values over dataset case, and it can be discovered that our model ranks first in 3 out of 4 case, while ranking third in case of random forgetting in CIFAR-10 dataset. It should be noted that BD achieves lesser unlearning time than ours, especially in random forgetting case, is because it utilizes 30% randomly sampled retain dataset, while our approach systematically reduces dataset based on value of  $K$ . For reference, here  $K = 450$ , and average percentage of reduced retain dataset size is 80% in the case of random forgetting.

## C. Relationship between Unlearning Metric and Membership Inference Attack

We computed the unlearning metric, overfitting metric and **MIA** for first few epochs of unlearning, in the case of  $\bf{R}$ ,  $\bf{CF}$ and MU. The reason for chosing first few epochs is to prevent the unlearned parameters not deviate largely from original parameters. The results are shown in figure 2, where it can be noted that proposed MU consistently performs much better than CF, on which it is based on, approaches the effects of R. It can be noted that unlearning metric is significantly correlated with **MIA**, while the correlation between overfitting metric and MIA are less visible at this early point in training.

### D. Competitor to Differential Privacy

We attempt to exercise our strategy of defending against membership inference attack, and comparing it with differential privacy based solution. We performed our proposed unlearning based regularization over VGG16 trained on CI-FAR10, in comparison with training the VGG16 using DP-Adam [\[11\]](#page-7-2), [\[12\]](#page-7-3). The results are shown in figure 3. It is abundantly clear that while proposed unlearning based regularization has similar privacy effects as DP-Adam, it has significantly higher utility in close proximity with that of original model.

### E. Unlearning in Dataset Condensation

In order to show the effectiveness of our proposed unlearning in condensation, we created a convolution-deconvolution based autoencoder architecture to be utilized as beginning and intermediate in our strategy, while a MLP is assigned as final. We attempt to forget 10 percent of random images from CIFAR-10 dataset. After completion of offline and online phase of proposed unlearning, we subustitute the final with VGG16, which we intended to train on condensed dataset (not containing forget dataset). The results are vivid in figure 4, where the resultant model is approaches the privacy and utility of R, at significant advantage over the retraining VGG16 on retain dataset in terms of training time.

## VI. CONCLUSION

In this paper, we proposed a new unlearning scheme through interplay between catastrophic forgetting and dataset condensation. It has shown to be best balanced approximate unlearning scheme in terms of privacy, utility and efficiency through extensive experiments and results. We showed its application in protecting the privacy of deep learning model, as well as unlearning in dataset condensation. We envision our work as stepping stone for further investigation into relationship between unlearning and dataset condensation.

### ACKNOWLEDGMENTS

This work was done in Korea National University of Transportation, Chungju, South Korea. The work was partially funded by the IITP project, titled "Development of a highperformance visual discovery platform for understanding and predicting real-time large-scale shape data".

### REFERENCES

- <span id="page-6-0"></span>[1] J. Rosen, "The right to be forgotten," *Stan. L. Rev. Online*, vol. 64, p. 88, 2011.
- <span id="page-6-1"></span>[2] C. J. Hoofnagle, B. Van Der Sloot, and F. Z. Borgesius, "The european union general data protection regulation: what it is and what it means," *Information & Communications Technology Law*, vol. 28, no. 1, pp. 65– 98, 2019.
- <span id="page-6-2"></span>[3] M. Kurmanji, P. Triantafillou, and E. Triantafillou, "Towards unbounded machine unlearning," *arXiv preprint arXiv:2302.09880*, 2023.
- <span id="page-6-3"></span>[4] Y. Liu, M. Fan, C. Chen, X. Liu, Z. Ma, L. Wang, and J. Ma, "Backdoor defense with machine unlearning," in *IEEE INFOCOM 2022-IEEE Conference on Computer Communications*, pp. 280–289, IEEE, 2022.
- <span id="page-6-4"></span>[5] N. G. Marchant, B. I. Rubinstein, and S. Alfeld, "Hard to forget: Poisoning attacks on certified machine unlearning," in *Proceedings of the AAAI Conference on Artificial Intelligence*, vol. 36, pp. 7691–7700, 2022.
- <span id="page-6-5"></span>[6] J. Z. Di, J. Douglas, J. Acharya, G. Kamath, and A. Sekhari, "Hidden poison: Machine unlearning enables camouflaged poisoning attacks," in *NeurIPS ML Safety Workshop*, 2022.
- <span id="page-6-6"></span>[7] T. Feng, M. Wang, and H. Yuan, "Overcoming catastrophic forgetting in incremental object detection via elastic response distillation," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 9427–9436, 2022.
- <span id="page-6-7"></span>[8] P. W. Koh and P. Liang, "Understanding black-box predictions via influence functions," in *International conference on machine learning*, pp. 1885–1894, PMLR, 2017.

- <span id="page-7-0"></span>[9] V. S. Chundawat, A. K. Tarun, M. Mandal, and M. Kankanhalli, "Can bad teaching induce forgetting? unlearning in deep networks using an incompetent teacher," in *Proceedings of the AAAI Conference on Artificial Intelligence*, vol. 37, pp. 7210–7217, 2023.
- <span id="page-7-1"></span>[10] J. Jia, J. Liu, P. Ram, Y. Yao, G. Liu, Y. Liu, P. Sharma, and S. Liu, "Model sparsification can simplify machine unlearning," *arXiv preprint arXiv:2304.04934*, 2023.
- <span id="page-7-2"></span>[11] Z. Bu, Y.-X. Wang, S. Zha, and G. Karypis, "Differentially private optimization on large model at small cost," *arXiv preprint arXiv:2210.00038*, 2022.
- <span id="page-7-3"></span>[12] Z. Bu, Y.-X. Wang, S. Zha, and G. Karypis, "Differentially private bias-term only fine-tuning of foundation models," *arXiv preprint arXiv:2210.00036*, 2022.
- <span id="page-7-4"></span>[13] A. Thudi, G. Deza, V. Chandrasekaran, and N. Papernot, "Unrolling sgd: Understanding factors influencing machine unlearning," in *2022 IEEE 7th European Symposium on Security and Privacy (EuroS&P)*, pp. 303– 319, IEEE, 2022.
- <span id="page-7-5"></span>[14] L. Bourtoule, V. Chandrasekaran, C. A. Choquette-Choo, H. Jia, A. Travers, B. Zhang, D. Lie, and N. Papernot, "Machine unlearning," in *2021 IEEE Symposium on Security and Privacy (SP)*, pp. 141–159, IEEE, 2021.
- <span id="page-7-6"></span>[15] Y. Cao and J. Yang, "Towards making systems forget with machine unlearning," in *2015 IEEE symposium on security and privacy*, pp. 463– 480, IEEE, 2015.
- <span id="page-7-7"></span>[16] L. Graves, V. Nagisetty, and V. Ganesh, "Amnesiac machine learning," in *Proceedings of the AAAI Conference on Artificial Intelligence*, vol. 35, pp. 11516–11524, 2021.
- <span id="page-7-8"></span>[17] Y. Wu, E. Dobriban, and S. Davidson, "Deltagrad: Rapid retraining of machine learning models," in *International Conference on Machine Learning*, pp. 10355–10366, PMLR, 2020.
- <span id="page-7-9"></span>[18] A. Warnecke, L. Pirch, C. Wressnegger, and K. Rieck, "Machine unlearning of features and labels," *arXiv preprint arXiv:2108.11577*, 2021.
- <span id="page-7-10"></span>[19] A. Golatkar, A. Achille, and S. Soatto, "Eternal sunshine of the spotless net: Selective forgetting in deep networks," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 9304–9312, 2020.
- <span id="page-7-11"></span>[20] A. Mahadevan and M. Mathioudakis, "Certifiable machine unlearning for linear models," *arXiv preprint arXiv:2106.15093*, 2021.
- <span id="page-7-12"></span>[21] A. K. Tarun, V. S. Chundawat, M. Mandal, and M. Kankanhalli, "Fast yet effective machine unlearning," *IEEE Transactions on Neural Networks and Learning Systems*, 2023.
- <span id="page-7-13"></span>[22] M. Chen, W. Gao, G. Liu, K. Peng, and C. Wang, "Boundary unlearning: Rapid forgetting of deep networks via shifting the decision boundary, in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 7766–7775, 2023.
- <span id="page-7-14"></span>[23] R. Shokri, M. Stronati, C. Song, and V. Shmatikov, "Membership inference attacks against machine learning models," in *2017 IEEE symposium on security and privacy (SP)*, pp. 3–18, IEEE, 2017.
- <span id="page-7-15"></span>[24] M. Fredrikson, S. Jha, and T. Ristenpart, "Model inversion attacks that exploit confidence information and basic countermeasures," in *Proceedings of the 22nd ACM SIGSAC conference on computer and communications security*, pp. 1322–1333, 2015.
- <span id="page-7-16"></span>[25] V. S. Chundawat, A. K. Tarun, M. Mandal, and M. Kankanhalli, "Zeroshot machine unlearning," *IEEE Transactions on Information Forensics and Security*, 2023.
- <span id="page-7-17"></span>[26] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," *arXiv preprint arXiv:2006.05929*, 2020.
- <span id="page-7-18"></span>[27] B. Zhao and H. Bilen, "Dataset condensation with distribution matching," in *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pp. 6514–6523, 2023.
- <span id="page-7-19"></span>[28] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 4750–4759, 2022.
- <span id="page-7-20"></span>[29] B. Zhao and H. Bilen, "Dataset condensation with differentiable siamese augmentation," in *International Conference on Machine Learning*, pp. 12674–12685, PMLR, 2021.
- [30] G. Zhao, G. Li, Y. Qin, and Y. Yu, "Improved distribution matching for dataset condensation," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 7856–7865, 2023.
- <span id="page-7-21"></span>[31] J.-H. Kim, J. Kim, S. J. Oh, S. Yun, H. Song, J. Jeong, J.-W. Ha, and H. O. Song, "Dataset condensation via efficient synthetic-data parameterization," in *International Conference on Machine Learning*, pp. 11102–11118, PMLR, 2022.

- <span id="page-7-22"></span>[32] T. Wiatowski and H. Bölcskei, "A mathematical theory of deep convolutional neural networks for feature extraction," *IEEE Transactions on Information Theory*, vol. 64, no. 3, pp. 1845–1866, 2017.
- <span id="page-7-23"></span>[33] A. Dosovitskiy and T. Brox, "Inverting visual representations with convolutional networks," in *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 4829–4837, 2016.
- <span id="page-7-24"></span>[34] F. Mo, A. S. Shamsabadi, K. Katevas, S. Demetriou, I. Leontiadis, A. Cavallaro, and H. Haddadi, "Darknetz: towards model privacy at the edge using trusted execution environments," in *Proceedings of the 18th International Conference on Mobile Systems, Applications, and Services*, pp. 161–174, 2020.
- <span id="page-7-25"></span>[35] M. Nasr, R. Shokri, and A. Houmansadr, "Comprehensive privacy analysis of deep learning," in *Proceedings of the 2019 IEEE Symposium on Security and Privacy (SP)*, pp. 1–15, 2018.
- <span id="page-7-26"></span>[36] M. Raghu, C. Zhang, J. Kleinberg, and S. Bengio, "Transfusion: Understanding transfer learning for medical imaging," *Advances in neural information processing systems*, vol. 32, 2019.
- <span id="page-7-27"></span>[37] S. Yeom, I. Giacomelli, M. Fredrikson, and S. Jha, "Privacy risk in machine learning: Analyzing the connection to overfitting," in *2018 IEEE 31st computer security foundations symposium (CSF)*, pp. 268– 282, IEEE, 2018.
- <span id="page-7-28"></span>[38] H. Tanaka, D. Kunin, D. L. Yamins, and S. Ganguli, "Pruning neural networks without any data by iteratively conserving synaptic flow," *Advances in neural information processing systems*, vol. 33, pp. 6377– 6389, 2020.
- <span id="page-7-29"></span>[39] S. Liao, *Beyond perturbation: introduction to the homotopy analysis method*. CRC press, 2003.
- <span id="page-7-30"></span>[40] L. F. Richardson, *Advanced calculus: an introduction to linear analysis*. John Wiley & Sons, 2011.
- <span id="page-7-31"></span>[41] G. Strang, *Introduction to linear algebra*. SIAM, 2022.
- <span id="page-7-32"></span>[42] R. A. Horn and C. R. Johnson, *Matrix analysis*. Cambridge university press, 2012.
- <span id="page-7-33"></span>[43] B. Ghorbani, S. Krishnan, and Y. Xiao, "An investigation into neural net optimization via hessian eigenvalue density," in *International Conference on Machine Learning*, pp. 2232–2241, PMLR, 2019.
- <span id="page-7-34"></span>[44] C. Guo, T. Goldstein, A. Hannun, and L. Van Der Maaten, "Certified data removal from machine learning models," *arXiv preprint arXiv:1911.03030*, 2019.
- <span id="page-7-35"></span>[45] B. Hanin and D. Rolnick, "How to start training: The effect of initialization and architecture," *Advances in Neural Information Processing Systems*, vol. 31, 2018.
- <span id="page-7-36"></span>[46] H. Petzka, M. Kamp, L. Adilova, C. Sminchisescu, and M. Boley, "Relative flatness and generalization," *Advances in neural information processing systems*, vol. 34, pp. 18420–18432, 2021.
- <span id="page-7-37"></span>[47] G. Blom, L. Holst, and D. Sandell, *Problems and Snapshots from the World of Probability*. Springer Science & Business Media, 1993.
- <span id="page-7-38"></span>[48] N. Carlini, S. Chien, M. Nasr, S. Song, A. Terzis, and F. Tramer, "Membership inference attacks from first principles," in *2022 IEEE Symposium on Security and Privacy (SP)*, pp. 1897–1914, IEEE, 2022.
- <span id="page-7-39"></span>[49] K. Simonyan and A. Zisserman, "Very deep convolutional networks for large-scale image recognition," *arXiv preprint arXiv:1409.1556*, 2014.
- <span id="page-7-40"></span>[50] K. He, X. Zhang, S. Ren, and J. Sun, "Deep residual learning for image recognition," in *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 770–778, 2016.

## APPENDIX

We define the original dataset as  $\mathcal{D} = \{T_i, l_i\}_{i=1}^{N_D}$ , where  $T_i$  is the  $i^{th}$  training image and  $l_i$  is the  $i^{th}$  label.  $D$  can be partitioned into the forget dataset  $\mathcal{F} = \{T_{f_i}, l_{f_i}\}_{i=1}^{N_F}$  and retain dataset  $\mathcal{R} = \{T_{r_i}, l_{r_i}\}_{i=1}^{\bar{N}_R}$ . Here,  $\mathcal{F} \cup \mathcal{R} = \mathcal{D}$ , and  $\mathcal{F} \cap \mathcal{R} = \emptyset$ . For the sake of representation, we represent the images in  $D$ as  $T = \bigcup_{i=1}^{c} \bigcup_{j=1}^{n} T_{ij}$ , where c is the total number of classes in D, n is the number of images per class such that  $N_D = nc$ and  $T_{ij}$  represents the image in  $i^{th}$  class and  $j^{th}$  index.

The forget set  $F$  represents the part of the training dataset  $D$  to be forgotten by a model, trained on  $D$  using a twice differentiable loss function  $\mathcal{L}$ . Lets call this trained model as  $\mathcal{M}_{\theta}$  with trained parameters  $\theta$ , which is achieved by minimizing  $\mathcal{L}(\mathcal{D}, \theta) = \sum_{i=1}^{N_D} ((\mathcal{M}(T_i), l_i), \theta)$ . The goal of dataset reduction framework is reduce  $\mathcal{R} \rightarrow \mathcal{R}_{\text{red}}$ , such that  $N_r = |\mathcal{R}_{\text{red}}|$  and  $N_r < N_R$ . Within the dataset reduction framework, the images of each class are grouped into  $K$ clusters, such that  $K < n$ . Finally, after unlearning procedure, the model M gains parameters  $\theta^*$ .

### A. Relation between Original and Unlearned Parameters

Suppose there exists a parameter  $\zeta \in [0, 1]$ , and we define a modified loss function as.

$$
\mathcal{L}(\mathcal{D}, \theta(\zeta), \zeta) = \mathcal{L}(\mathcal{R}, \theta(\zeta)) + \zeta \mathcal{L}(\mathcal{F}, \theta(\zeta)) \tag{1}
$$

such that,

$$
\theta(\zeta) = \theta^* + \sum_{i=1}^{\infty} \zeta^i \theta_i \tag{2}
$$

As we deform  $\zeta$  from 0 to 1, then  $\mathcal{L}(\mathcal{D}, \theta, \zeta)$  changes from  $\mathcal{L}(\mathcal{R}, \theta(0))$  to  $\mathcal{L}(\mathcal{R}, \theta(1)) + \mathcal{L}(\mathcal{F}, \theta(1))$ , thus representing that  $\mathcal{L}(\mathcal{D}, \theta(\zeta), \zeta)$  deforms from loss over retain dataset to loss over whole training dataset. Suppose if  $\theta(\zeta)$  is local minima of  $\mathcal{L}(\mathcal{D}, \theta(\zeta), \zeta)$ , then.

$$
\nabla_{\theta} \mathcal{L}(\mathcal{R}, \theta(\zeta)) + \zeta \nabla_{\theta} \mathcal{L}(\mathcal{F}, \theta(\zeta)) = 0 \tag{3}
$$

In the lines of perturbation theory [\[39\]](#page-7-29), by choosing  $\zeta$  small enough, we make first order approximation of  $\theta \approx \theta^* + \zeta \theta_1$ , such that  $\zeta^2 \to 0$ . After inserting it into Equation (5).

$$
\nabla_{\theta} \mathcal{L}(\mathcal{R}, \theta^* + \zeta \theta_1) + \zeta \nabla_{\theta} \mathcal{L}(\mathcal{F}, \theta^* + \zeta \theta_1) = 0 \qquad (4)
$$

By performing Taylor approximation around  $\theta^*$ , we get.

$$
\nabla_{\theta} \mathcal{L}(\mathcal{R}, \theta^*) + \zeta \nabla_{\theta}^2 \mathcal{L}(\mathcal{R}, \theta^*) \theta_1 + \zeta \nabla_{\theta} \mathcal{L}(\mathcal{F}, \theta^*) + \zeta^2 \nabla_{\theta}^2 \mathcal{L}(\mathcal{F}, \theta^*) \theta_1 + o(\zeta \theta_1) = 0
$$
 (5)

Omitting  $o(\zeta\theta_1)$  term, we balance the coefficients of 1 and  $\zeta$ in Equation (5), since  $\zeta$  is arbitrary and independent parameter. Then  $\nabla_{\theta} \mathcal{L}(\mathcal{R}, \theta^*) = 0$ , implying that  $\theta^*$  is the minima of  $\mathcal{L}(\mathcal{R}, \theta^*)$ . For the case of  $\zeta$  coefficients,

$$
\theta_1 = -\nabla^2 \mathcal{L}(\mathcal{R}, \theta_0)^{-1} \nabla \mathcal{L}(\mathcal{F}, \theta_0)
$$
 (6)

Using Equations (2) and (6), we achieve first order approximation of  $\theta$  as,

$$
\left[\theta = \theta^* - \zeta \nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1} \nabla \mathcal{L}(\mathcal{F}, \theta^*) + o(\zeta)\right] \tag{7}
$$

### Remark:

• The Equation (7) resembles the derivation of change of parameters under small influence of a new training sample [\[8\]](#page-6-7). Through Cauchy-Schawarz inequality, it can be observed that  $\|\theta - \theta^*\|_2 \le$  $\zeta \|\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1}\|_F \|\nabla \mathcal{L}(\mathcal{F}, \theta^*)\|_2$ , where  $\|\|_F$  is the Forbenious norm. Since  $\|\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1}\|_F > 0$  as due to stationary condition of  $\mathcal{L}(\mathcal{R}, \theta^*)$  [\[40\]](#page-7-30) and strict positive definite condition [\[41\]](#page-7-31), therefore necessarily the performance of unlearned model (optimized from original pretrained model) deteriorates over forget dataset, because  $\|\nabla \mathcal{L}(\mathcal{F}, \theta^*)\|_2 > 0$  when  $\|\theta - \theta^*\|_2 > 0$  and  $\|\nabla \mathcal{L}(\mathcal{F}, \theta^*)\|_2 = 0$  if and only if  $\|\theta - \theta^*\|_2 = 0$ . Thus, Equation (7) is a mathematical statement about catastrophic forgetting.

• If instead of defining loss as summation of loss over individual samples, we averaged the individual losses, then we would have to redefine Equation (1) as  $\mathcal{L}(\mathcal{D}, \theta(\zeta), \zeta) = ((1 - \zeta) + \zeta \frac{N_R}{N_D}) \mathcal{L}(\mathcal{R}, \theta(\zeta)) +$  $\zeta \frac{N_F}{N_D} \mathcal{L}(\mathcal{F}, \theta(\zeta))$ . Then through same sequence of steps, we would arrive at modified version of Equation (7) as.

$$
\theta = \theta^* - \zeta \frac{N_F}{N_D} \nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1} \nabla \mathcal{L}(\mathcal{F}, \theta^*) + o(\zeta)
$$

We do not progress in this fashion, since first the positions of local minima does not change by scaling the loss function, and secondly the resultant analysis is simpler to deal with, needless to say it does not change the consequential results.

If we progressively substitute second-order, to  $n^{\text{th}}$  order approximation of  $\theta$  from Equation (2) into Equation (3), and apply similarly first order Taylor approximation around  $\theta_0$ , we can derive the expressions for  $\theta_2$  up to  $\theta_n$ , by equating the coefficients of  $\zeta^2$  to  $\zeta^n$  to zero. For example,  $\theta_2$  can be derived with this strategy as.

$$
\theta_2 = \nabla^2 \mathcal{L}(\mathcal{F}, \theta^*)^{-1} \nabla \mathcal{L}(\mathcal{F}, \theta^*) \nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1} \nabla \mathcal{L}(\mathcal{F}, \theta^*)
$$
\n(8)

So that substituting Equations (6) and (8) into Equation (2) would give a second order approximation of  $\theta$ .

$$
\theta = \theta^* - \zeta \nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1} \nabla \mathcal{L}(\mathcal{F}, \theta^*) + \zeta^2 \nabla^2 \mathcal{L}(\mathcal{F}, \theta^*)^{-1} \nabla \mathcal{L}(\mathcal{F}, \theta^*) \nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1} \n\nabla \mathcal{L}(\mathcal{F}, \theta^*) + o(\zeta^2)
$$

Remark: We highlight this equation, in light of [\[8\]](#page-6-7), where making  $\zeta \to 1$  besides  $\zeta \to 0$  for deriving influence function, i.e  $\frac{d}{d\zeta}(\theta - \theta^*)|_{\zeta=1}$  can lead to new analytical results with the underlying intuition of equally up-weighting the new data samples (samples in  $\mathcal F$  in current case), instead of infinitesimal up-weight of new-data samples. This course of study we leave for future work.

### B. Persistance of Loss of Unlearned Model on Retain Dataset

For input-output pair  $(T_i, l_i) \in \mathcal{R}$ , we expand the loss of pretrained model around unlearned parameters as a Taylor's expansion.

$$
\mathcal{L}((\mathcal{M}(T_i), l_i), \theta) = \mathcal{L}((\mathcal{M}(T_i), l_i), \theta^*) + \nabla \mathcal{L}((\mathcal{M}(T_i), l_i), \theta^*) \cdot \delta \theta + o(\|\delta \theta\|_2)
$$
\n(9)

where  $\delta\theta = \theta - \theta^*$ . We wish to the loss of pretrained and unlearned model over retain samples to be retain to conform to unlearning utility principle. Therefore we rewrite Equation (11), and omitting  $o(||\delta\theta||_2)$  terms.

$$
\mathcal{L}((\mathcal{M}(T_i), l_i), \theta) - \mathcal{L}((\mathcal{M}(T_i), l_i), \theta^*) = \nabla \mathcal{L}((\mathcal{M}(T_i), l_i), \theta^*) \cdot \delta \theta
$$
\n(10)

Utilizing Equation (7) with omission of  $o(\zeta)$  terms, Equation (11) changes to.

$$
\mathcal{L}((\mathcal{M}(T_i), l_i), \theta) - \mathcal{L}((\mathcal{M}(T_i), l_i), \theta^*) = \nabla \mathcal{L}((\mathcal{M}(T_i), l_i), \theta^*) \cdot (\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1} \nabla \mathcal{L}(\mathcal{F}, \theta^*)) \tag{11}
$$

By summing over all  $(T_i, l_i) \in \mathcal{R}$  and scaling both sides of equation with  $\frac{1}{N_R}$ , we write Equation (12) as follows.

$$
|\mathcal{L}(\mathcal{R}, \theta^*) - \mathcal{L}(\mathcal{R}, \theta)| =
$$
  
$$
\zeta |\nabla \mathcal{L}(\mathcal{R}, \theta^*) \cdot (\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1} \nabla \mathcal{L}(\mathcal{F}, \theta^*))|
$$
 (12)

*1) Nature of Inverse Hessian:* The Hessian in Equation (13) is positive definite [\[40\]](#page-7-30), and so is its inverse [\[42\]](#page-7-32). The  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1}$  has positive eigenvalues [\[41\]](#page-7-31) and can be represented as  $\{\frac{1}{\lambda_i}\}_{i=1}^n$ , where  $\lambda_i$  is the eigenvalue of  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)$ , which are also positive. Due to definite of positive definite matrices,  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1} \nabla \mathcal{L}(\mathcal{F}, \theta^*)$  should not change the direction of  $\nabla \mathcal{L}(\mathcal{F}, \theta^*)$  by more than  $\frac{\pi}{2}$ radians, due to positive dot product condition [\[41\]](#page-7-31).

It has been pointed out that during optimization, the eigenvalues of Hessian concentrate around zero with roughly equal distribution, with few outliers [\[43\]](#page-7-33). Taking this observation as starting point, we want to claim that the eigenvalues of  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1}$  even more concentrated (small vairance) and uniform. This motivation is based on eigendecomposition [\[41\]](#page-7-31) of positive definite matrix into  $UDU^{-1}$ , where U is a unitary matrix, whose columns are eigenvectors of positive definite matrix, forming an orthonormal basis, and  $D$  is a diagonal matrix with positive eigenvalues. In this interpretation, the  $U^{-1}$  and U move towards and back from positive definite matrix's orthonormal eigenbasis, like an encoder-decoder setup in deep learning. Within the eigenbasis (encoded domain), each component of resultant vector gets scaled with positive eigenvalues. We wish to concentrate and uniform the eigenvalues of  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)$ , so that it has dominating function of just scaling the components of  $\nabla \mathcal{L}(\mathcal{F}, \theta^*)$  in equal amount, i.e. it approximately acts as a scaler to a vector it acts upon. This effect's significance will be pointed out later.

Without loss of generality, we define variance of a finite positive sequence  $\{a_i\}_{i=1}^n$  as  $V\{a\} = \sum_{i=1}^n (M\{a\} - a_i)^2$ and mean as  $M\{a\} = \frac{1}{n} \sum_{j=1}^{n} a_j$ . Henceforth, we compute variance of eigenvalues of  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1}$  by starting with obvious first computation step and then applying arithematic mean-harmonic mean inequality on the first term in outer summation,

$$
V\{\frac{1}{\lambda}\} \le \sum_{i=1}^{n} \left(\frac{1}{\frac{1}{n}\sum_{j=1}^{n}\lambda_j} - \frac{1}{\lambda_i}\right)^2
$$
 (13)

$$
= \sum_{i=1}^{n} \left(\frac{\frac{1}{n}\sum_{j=1}^{n}\lambda_j - \lambda_i}{\frac{\lambda_i}{n}\sum_{j=1}^{n}\lambda_j}\right)^2
$$
 (14)

By apply Cauchy-Schwarz inequality in attempt to splitting sum over numerator and denomenator, and performing trivial computations, we arrive at.

$$
=\frac{V\{\lambda\}}{M\{\lambda\}}\sum_{i=1}^{n}\frac{1}{\lambda_i}
$$
 (15)

Next we reduce the above the harmonic like sum  $\sum_{i=1}^{n} \frac{1}{\lambda_i}$ , by applying Abel summation. We chose a sequence  $\{b_k\}_{k=1}^{n^{n}}$ <br>such that  $b_k = 1$ ,  $B(t) = \sum_{0 \le k \le t} b_k = t + 1$ , and  $\phi(t) = \frac{1}{\lambda_t}$ ,

such that  $\phi(t)$  is descending ordered sequence over  $\{\frac{1}{\lambda_i}\}_{i=1}^n$ , such that  $\phi(0) = \frac{1}{\lambda_{\min}}$  and  $\phi(n-1) = \frac{1}{\lambda_{\max}}$ . We then define the Abel summation formula as.

$$
\sum_{0 \le i \le n-1} b_i \phi(i) = B(n-1)\phi(n-1) - B(0)\phi(0) - \int_0^{n-1} B(z)\phi'(z)dz
$$
\n(16)

We model the descending nature of  $\phi(u)$  via  $\phi(u)$  =  $\frac{1}{\lambda_{\min}}e^{-ku}$ , where k can be derived by substituting  $\phi(n-1)$  =  $\frac{1}{\lambda_{\text{max}}}$  as  $k = \frac{1}{1-n} \log(\frac{\lambda_{\text{min}}}{\lambda_{\text{max}}})$ . We arrive from Equation (16) to following equation after substituting all assumptions.

$$
V\left\{\frac{1}{\lambda}\right\} = \mathcal{O}\left(\frac{V\{\lambda\}}{M\{\lambda\}}\left(\frac{n-1}{\lambda_{\max}} - \frac{1}{\lambda_{\min}}\right) + \frac{e^{k(1-n)}(kn+1) + k + 1}{k^2\lambda_{\min}}\right)\right)
$$
(17)

$$
V\left\{\frac{1}{\lambda}\right\} = \mathcal{O}\left(\frac{V\{\lambda\}}{M\{\lambda\}}\left(\frac{n-1}{\lambda_{\max}} - \frac{1}{\lambda_{\min}}\right.\right.\left.\left.\frac{1}{\lambda_{\min}}\left(\frac{1}{\left(\frac{1}{n-1}\log\left(\frac{\lambda_{\min}}{\lambda_{\max}}\right)\right)^2}\right.\right.\right.\left.\left.\frac{\lambda_{\min}}{\lambda_{\max}}\right)\left(1 + \frac{n}{n-1}\log\left(\frac{\lambda_{\min}}{\lambda_{\max}}\right)\right.\right.\right.\left.\left.\frac{1}{1 + \frac{1}{1-n}\log\left(\frac{\lambda_{\min}}{\lambda_{\max}}\right)}\right)\right)\right)
$$

The left hand side of equation (18) can be quickly approximated as  $\mathcal{O}\left(\frac{V\{\lambda\}}{M\{\lambda\}}\right)$  $\frac{V\{\lambda\}}{M\{\lambda\}}\left(\frac{(n-1)\lambda_{\min}-\lambda_{\max}}{\lambda_{\max}\lambda_{\min}}\right)$ . From this, we deduce that the variance of eignenvalues of  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1}$ is proportional to variance of eigenvalue distribution of  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)$ , scaled by reciprocal of its mean. If mean is greater than 1, than the distribution contracts, while the contrary is true otherwise. We take important observation from [\[43\]](#page-7-33) that outlier eigenvalues of Hessian are usually large, therefore we expect  $M\{\lambda\} > 1$ , even if eigenvalues are more concentrated around zeros. Thus we assert that the variance of eigenvalue distribution of  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1}$  is less than that of  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)$ 

Furthermore, the distribution becomes more uniform as the difference between maximum and minimum eigenvalue of  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1}$  is  $\frac{\lambda_{\text{max}} - \lambda_{\text{min}}}{\lambda_{\text{max}} \lambda_{\text{min}}},$  so essentially the previous uneven distribution dampens by  $\frac{1}{\lambda_{\max} \lambda_{\min}}$ .

From above conclusion we deduce that  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1}$  almost acts like a positive scaler, i.e. it almost preserves the direction of the vector it acts upon.

*2) Orthogonality Condition:* Based on discussion in section 2.2.1, since  $\nabla^2 \mathcal{L}(\mathcal{R}, \theta^*)^{-1}$  approximately preserves the direction of  $\nabla \mathcal{L}(\mathcal{F}, \theta^*)$ , and hence for left hand side of (13) to approach zero, then necessarily and approximately  $\nabla \mathcal{L}(\mathcal{R}, \theta^*) \cdot \nabla \mathcal{L}(\mathcal{F}, \theta^*) \rightarrow 0$ . Thus we find following unlearning orthogonality condition.

$$
\nabla \mathcal{L}(\mathcal{R}, \theta^*) \perp \nabla \mathcal{L}(\mathcal{F}, \theta^*)
$$
 (19)

Image /page/10/Figure/1 description: A bar chart displays the average gradient norm for different layers of a neural network. The x-axis labels include 'conv1', 'bn1', 'conv2', 'bn2', 'conv3', 'bn3', 'conv4', 'bn4', 'fc1', and 'fc2'. The y-axis represents the 'Average Gradient Norm', with values ranging from 0 to 60. The bar for 'conv1' is the tallest, reaching approximately 60. The other bars are significantly shorter, with 'conv2' and 'conv3' reaching around 10, 'fc2' reaching approximately 12, and the remaining bars ('bn1', 'bn2', 'bn3', 'bn4', 'fc1') reaching values between 1 and 5.

Fig. 5. Gradient of loss of CNN trained on CIFAR-10 over layers from shallow (left) to deep (right)

We would want to drop the Hessian since its computation is significantly dominated by gradient computation [\[44\]](#page-7-34). If in Equation (10), if we instead starting by expanding the loss of unlearning model around pretrained parameters, then the same sequence of steps leads to another orthogonality condition.

$$
\nabla \mathcal{L}(\mathcal{R}, \theta) \perp \nabla \mathcal{L}(\mathcal{F}, \theta^*)
$$
\n(20)

We derive the **unlearning metric** that conforms to condition defined in (20) as.

$$
1 - \frac{\nabla_{\theta} \mathcal{L}(\mathcal{R}) \cdot \nabla_{\theta} \mathcal{L}(\mathcal{F})}{\|\nabla_{\theta} \mathcal{L}(\mathcal{R})\|_{2} \|\nabla_{\theta} \mathcal{L}(\mathcal{F})\|_{2}} \tag{21}
$$

Suppose a model  $\mathcal{M}_{\theta}(x)$  with parameters  $\theta$  acting on an input  $x$  (with corresponding label  $y$ ), can be decomposed into composition of functions as  $\mathcal{M}_{\theta}(x) = f_{\theta_1}(g_{\theta_2}(x))$ , where f would represent the deeper layers of model with parameters  $\theta_1$ , g would represent shallow layers with parameters  $\theta_2$ , and  $\delta\theta_1 + \delta\theta_2 = \delta\theta$ . Essentially,  $\theta_1$  is not changed at indices under perturbations, where  $g$ 's parameters exist and vice versa. Consider following three cases. For sake of simplicity, we represent  $\mathcal{L}((\mathcal{M}(x), y), \theta)$  as  $\mathcal{L}(\theta)$ .

### C. Case-1: Perturbations in Shallow Layer's Parameters

Suppose if we make perturbation in parameters  $\theta_2$ , possibly in an attempt to train, then the gradient of the loss of the neural network can be written as.

$$
\nabla_{\theta} \mathcal{L}(\theta_1, \theta_2 + \delta \theta_2) = \frac{\partial \mathcal{L}}{\partial \mathcal{M}} \cdot \nabla_{\theta} f_{\theta_1}(g_{\theta_2 + \delta \theta_2}(x)) \tag{22}
$$

We make a Taylor approximation around  $\theta_2$ , and omitting  $o(||\delta \theta_2||_2)$  terms,

$$
\nabla_{\theta} \mathcal{L}(\theta_1, \theta_2 + \delta \theta_2) = \frac{\partial \mathcal{L}}{\partial \mathcal{M}} \cdot \{ \nabla_{\theta} f_{\theta_1}(g_{\theta_2}(x) + \nabla_{\theta} g(x) \delta \theta_2 \}
$$
\n(23)

In Equation (24), the  $\nabla_{\theta} g(x)$  represents the Jacobian of  $g(x, \theta)$  with respect to  $\theta$ , but for sake of convenience we use same notation as gradient. We make another Taylor approximation of Equation (24) around  $g_{\theta_2}(x)$  as.

$$
\nabla_{\theta} \mathcal{L}(\theta_1, \theta_2 + \delta \theta_2) = \frac{\partial \mathcal{L}}{\partial \mathcal{M}} \cdot \{ \nabla_{\theta} [f_{\theta_1}(g_{\theta_2}(x)) \\ + \nabla_{\theta} (f(g(x))) \cdot \nabla_{\theta} g(x) \cdot \delta \theta_2(x) \\ + o(\|\nabla_{\theta} g(x) \cdot \delta \theta_2\|_2) ] \} \tag{24}
$$

Image /page/10/Figure/15 description: This is a histogram showing the distribution of gradients for two different methods: Modularized and Normal. The x-axis represents the distribution, and the y-axis represents the gradient. The Modularized method is represented by blue bars and a blue curve, while the Normal method is represented by red bars and a red curve. The blue bars are concentrated in the higher distribution range (around 40-90), while the red bars are concentrated in the lower distribution range (around 0-40). The blue curve shows a relatively flat distribution peaking around a gradient of 8.5 between distribution values of 40 and 70, then decreasing. The red curve shows a peak gradient of around 8.5 at a distribution value of approximately 20, then decreasing sharply.

Fig. 6. Gradient distribution of normed gradient of intermediate in case of Modularized CNN (whose beginning and final are retrained in unlearning phase) and pretrained CNN over retain dataset

$$
\begin{align*}\nabla_{\theta} \mathcal{L}(\theta_1, \theta_2 + \delta \theta_2) &= \frac{\partial \mathcal{L}}{\partial \mathcal{M}} \cdot \left\{ \nabla_{\theta} (f_{\theta_1}(g_{\theta_2}(x))) \\ + \nabla_{\theta}^2 (f(g(x))) \cdot \nabla_{\theta} g(x) \cdot \delta \theta_2(x) \\ + \nabla_{\theta} (f(g(x))) \cdot \nabla_{\theta}^2 g(x) \cdot \delta \theta_2(x) \\ + o \left( \|\nabla_{\theta}^2 g(x) \cdot \delta \theta_2 \|_2 \right) \\ \right\} \\ \end{align*} (25)
$$

### Case-2: Perturbations in Deeper Layer's Parameters

Likewise to case-1, suppose if we make perturbation in only parameters of  $f$  part of the model as.

$$
\nabla_{\theta} \mathcal{L}(\theta_1 + \delta \theta_1, \theta_2) = \frac{\partial \mathcal{L}}{\partial \mathcal{M}} \cdot \nabla_{\theta} f_{\theta_1 + \delta \theta_1}(g_{\theta_2}(x)) \tag{26}
$$

Performing Taylor's expansion around  $\theta_1$  in Equation (27).

$$
\nabla_{\theta} \mathcal{L}(\theta_1 + \delta \theta_1, \theta_2) = \frac{\partial \mathcal{L}}{\partial \mathcal{M}} \cdot \{ \nabla_{\theta} f_{\theta_1}(g_{\theta_2}(x)) + \nabla_{\theta}^2 f_{\theta_1}(g_{\theta_2}(x)) \cdot \delta \theta_1 + o(\|\delta \theta_1\|_2) \}
$$
  $(27)$ 

### Case-3: Perturbations in Whole Model's Parameters

Combining case-1 and case-2, where we combine perturbations in  $\theta_1$  and  $\theta_2$ , as.

$$
\nabla_{\theta} \mathcal{L}(\theta_1 + \delta \theta_1, \theta_2 + \delta \theta_2) = \frac{\partial \mathcal{L}}{\partial \mathcal{M}} \cdot \nabla_{\theta} f_{\theta_1 + \delta \theta_1}(g_{\theta_2 + \delta \theta_2}(x))
$$
\n(28)

$$
\nabla_{\theta} \mathcal{L}(\theta_{1} + \delta \theta_{1}, \theta_{2} + \delta \theta_{2}) = \frac{\partial \mathcal{L}}{\partial \mathcal{M}} \cdot [ \nabla_{\theta} f_{\theta_{1}}(g_{\theta_{2}}(x)) \\ + \nabla_{\theta}^{2} f(g(x)) \cdot \nabla_{\theta} g(x) \cdot \delta \theta_{2} \\ + \nabla_{\theta} f(g(x)) \cdot \nabla_{\theta}^{2} g(x) \cdot \delta \theta_{2} \\ + \nabla_{\theta}^{2} f_{\theta_{1}}(g_{\theta_{2}}(x)) \cdot \delta \theta_{1} \\ + o(\|\nabla_{\theta}^{2} g(x) \cdot \delta \theta_{2}\|_{2} + \|\delta \theta_{1}\|_{2}) ] (29)
$$

|                 | R                                    | CF             | D                                                                       | BD                                      | S                                                           | P+U                               | MU                               |
|-----------------|--------------------------------------|----------------|-------------------------------------------------------------------------|-----------------------------------------|-------------------------------------------------------------|-----------------------------------|----------------------------------|
| <b>MLP</b>      | $	ext{bs}=256$                       | $	ext{bs}=256$ | $	ext{bs}=256$<br>$	ext{T}=4.0$<br>$	ext{hw}=1.0$<br>$	ext{sw}=10^{-1}$ | $	ext{bs}=256$<br>$	ext{ratio}_R = 0.3$ | $	ext{bs}=256$<br>$	ext{T}=4.0$<br>$	ext{epoch}_{L_1} = 15$ | $	ext{bs}=256$<br>$	ext{pr}=0.95$ | $	ext{bs}=256$<br>$	ext{bs}=256$ |
| <b>CNN</b>      | $	ext{lr}=10^{-3}$<br>$	ext{bs}=256$ | $	ext{bs}=256$ | $	ext{bs}=256$<br>$	ext{T}=4.0$<br>$	ext{hw}=1.0$<br>$	ext{sw}=10^{-1}$ | $	ext{bs}=256$<br>$	ext{ratio}_R = 0.3$ | $	ext{bs}=256$<br>$	ext{T}=4.0$<br>$	ext{epoch}_{L_1} = 15$ | $	ext{bs}=256$<br>$	ext{pr}=0.95$ | $	ext{bs}=256$<br>$	ext{bs}=256$ |
| <b>VGG16</b>    | $	ext{bs}=256$                       | $	ext{bs}=256$ | $	ext{bs}=256$<br>$	ext{T}=4.0$<br>$	ext{hw}=1.0$<br>$	ext{sw}=10^{-1}$ | $	ext{bs}=256$<br>$	ext{ratio}_R = 0.3$ | $	ext{bs}=256$<br>$	ext{T}=4.0$<br>$	ext{epoch}_{L_1} = 15$ | $	ext{bs}=256$<br>$	ext{pr}=0.95$ | $	ext{bs}=256$<br>$	ext{bs}=256$ |
| <b>ResNet18</b> | $	ext{bs}=256$                       | $	ext{bs}=256$ | $	ext{bs}=256$<br>$	ext{T}=4.0$<br>$	ext{hw}=1.0$<br>$	ext{sw}=10^{-1}$ | $	ext{bs}=256$<br>$	ext{ratio}_R = 0.3$ | $	ext{bs}=256$<br>$	ext{T}=4.0$<br>$	ext{epoch}_{L_1} = 15$ | $	ext{bs}=256$<br>$	ext{pr}=0.95$ | $	ext{bs}=256$<br>$	ext{bs}=256$ |
|                 |                                      |                |                                                                         | TABLE II                                |                                                             |                                   |                                  |

HYPERPARAMETERS OF BENCHMARK UNLEARNING METHODS OVER ARCHITECTURES

Image /page/11/Figure/2 description: The image displays two sets of line graphs, labeled (a) and (b), each containing four rows and three columns of graphs. Each individual graph plots a metric (RA, FA, MIA, UT) against a value 'K' on the x-axis, with different colored lines representing 'modular', 'catastrophic', and 'retraining' methods. The columns within each set represent different 'Forget Percentage' values: 1%, 10%, and 50%. Set (a) shows RA values generally increasing with K for 'modular' and 'catastrophic' methods, while 'retraining' remains low. FA values in (a) show a decrease for 'modular' and 'catastrophic' with increasing K, and 'retraining' stays low. MIA values in (a) are relatively stable across K for all methods, with 'modular' slightly higher. UT values in (a) show a decrease for 'modular' and 'catastrophic' with increasing K, and 'retraining' remains low. Set (b) shows similar trends to set (a) across the different forget percentages and metrics.

Fig. 7. (a) Evolution of metrics RA, FA, MIA and UT of modular unlearning over different sizes of clusters K, for the case of arbitrary forgetting, and condensation via fast distribution matching (b) Evolution of metrics RA, FA, MIA and UT of modular unlearning over different sizes of clusters K, for the case of class-wise forgetting, and condensation via fast distribution matching

With the abstractions  $f$  and  $g$ , we can make  $g$  sequentially cover from shallow to deep layers to perform an inference. We first define that  $g_i$  covers from the shallowest  $(0^{th})$  layer to the  $i^{th}$  layer of the model. Next we see that if we define domain D, which contains  $\theta_2$  and  $\delta\theta_2$ , and  $\mathbf{M}_i = \sup_{\theta_2 \in D} ||\nabla g_i(x, \theta_2)||_2$ , then due to [\[40\]](#page-7-30), we can write.

$$
||g_i(x, \theta_2 + \delta \theta_2) - g_i(x, \theta_2)||_2 \leq \mathbf{M}_i ||\delta \theta_2||_2 \tag{30}
$$

. Since neural networks are randomly initialized with small weights [\[45\]](#page-7-35), there  $\|\delta\theta_2\|_2$  is an invariant quantity, and thus we write Equation (31) as.

$$
||g_i(x, \theta_2 + \delta \theta_2) - g_i(x, \theta_2)||_2 = \mathcal{O}(\mathbf{M}_i)
$$
 (31)

Now we use a key observation from [\[46\]](#page-7-36) that represents that multiplicative perturbations in parameters as equivalent to multiplication perturbations in inputs (features). Hence we use this fact to rewrite Equation (26) as.

$$
||g_i(x + \delta x, \theta_2) - g_i(x, \theta_2)||_2 = \mathcal{O}(\mathbf{M}_i)
$$
 (32)

Next we take a general observation about neural networks that shallower layers of neural network learn low level features of the input, and thus sensitive to input [\[33\]](#page-7-23), [\[34\]](#page-7-24). Especially in convolution neural networks, the layer representations achieve more translation invariance as we move deeper [\[32\]](#page-7-22). Hence, with this observation we establish an equivalence using Equation (33) that as i moves from 0 to  $l$  layers, the left hand side progressively decreases, and simultaneously  $M_i$  progressively becomes smaller. We illustrate the effect in figure 3, where the norm of gradient of layers of trained CNN are shown, averaged over CIFAR-10 training dataset.

Thus we finally make inference from Equation (30) that  $\|\nabla_{\theta} \mathcal{L}(\mathcal{M}(x), y, \theta)\|_2$  is dominated by terms containing  $\|\nabla_\theta g(x)\|_2$  and  $\|\nabla_\theta^2 g(x)\|_2$  as  $\|\delta_1\|_2, \|\delta_2\|_2 \to 0$ , since  $\delta\theta_1$ 

Image /page/12/Figure/1 description: The image displays two sets of line graphs, labeled (a) and (b). Each set contains four rows and three columns of graphs, totaling twelve graphs per set. All graphs plot a metric on the y-axis against 'K' on the x-axis, with values of K ranging from 200 to 1000. The graphs in set (a) are categorized by 'Forget Percentage' of 1%, 10%, and 50%. Within each forget percentage column, the rows represent different metrics: RA, FA, MIA, and UT. The graphs in set (b) follow the same structure, with columns for 'Forget Percentage' of 1%, 10%, and 50%, and rows for RA, FA, MIA, and UT. Each graph shows three lines representing 'modular', 'catastrophic', and 'retraining' methods. For RA, forget percentage 1% shows modular and catastrophic around 85-95, with retraining around 75. Forget percentage 10% shows modular peaking at 95, catastrophic around 85, and retraining around 75. Forget percentage 50% shows modular around 75, catastrophic around 75, and retraining around 75. For FA, forget percentage 1% shows modular around 85, catastrophic around 80, and retraining around 55. Forget percentage 10% shows modular around 80, catastrophic around 75, and retraining around 55. Forget percentage 50% shows modular around 75, catastrophic around 70, and retraining around 55. For MIA, forget percentage 1% shows modular around 65, catastrophic around 80, and retraining around 50. Forget percentage 10% shows modular around 65, catastrophic around 80, and retraining around 50. Forget percentage 50% shows modular around 65, catastrophic around 80, and retraining around 50. For UT, forget percentage 1% shows modular around 2, catastrophic around 5.5, and retraining around 5.5. Forget percentage 10% shows modular around 2, catastrophic around 12.5, and retraining around 12.5. Forget percentage 50% shows modular around 2, catastrophic around 5.5, and retraining around 5.5. Set (b) shows similar trends but with different values. For RA, forget percentage 1% shows modular around 95, catastrophic around 75, and retraining around 75. Forget percentage 10% shows modular around 85, catastrophic around 75, and retraining around 75. Forget percentage 50% shows modular around 75, catastrophic around 75, and retraining around 75. For FA, forget percentage 1% shows modular around 45, catastrophic around 30, and retraining around 0. Forget percentage 10% shows modular around 35, catastrophic around 25, and retraining around 0. Forget percentage 50% shows modular around 30, catastrophic around 20, and retraining around 0. For MIA, forget percentage 1% shows modular around 85, catastrophic around 85, and retraining around 60. Forget percentage 10% shows modular around 85, catastrophic around 85, and retraining around 60. Forget percentage 50% shows modular around 85, catastrophic around 85, and retraining around 60. For UT, forget percentage 1% shows modular around 2, catastrophic around 5.5, and retraining around 5.5. Forget percentage 10% shows modular around 2, catastrophic around 5.5, and retraining around 5.5. Forget percentage 50% shows modular around 2, catastrophic around 5.5, and retraining around 5.5.

Fig. 8. (a) Evolution of metrics RA, FA, MIA and UT of modular unlearning over different sizes of clusters K, for the case of arbitrary forgetting, and condensation via model inversion (b) Evolution of metrics RA, FA, MIA and UT of modular unlearning over different sizes of clusters K, for the case of class-wise forgetting, and condensation via model inversion

and  $\delta\theta_2$  are arbitrary and independent. Based on our previous inference on trend of Jacobian over layers of model, we deduce that gradient based perturbations (which can be in the form of unlearning) over the whole model's parameters is dominated with the perturbations in shallower layers, while deeper layer parameters are less impacted. This effects serves as one of the main reasons for the principle of dividing the network into abstractions of beginning, intermediate and final, and training beginning during proposed unlearning phase. By changing the parameters of **beginning** towards new minima over retain dataset, leads to rapid forgetting in intermediate, which can be achieved with even 1 epoch. This stratagey is depicted in figure 2, where retraining the whole network leads to smaller gradients in intermediate part, as compared to our strategy (modularized).

Assuming that  $|\Gamma_{ij}| = |\Gamma_{ik}|$  for  $j \neq k$ , i.e. each cluster is of same size, and the forget samples are uniformly distributed through out the cluster. Through applications of collection protocol, total count of reduced retain dataset is.

$$
N_r = T_1 + T_2 \tag{33}
$$

where  $T_1$  is the count of cluster, not containing any forget samples, while  $T_2$  is the count of the retain samples from clusters, that do contain the forget samples. Assuming that the forget samples are uniform randomly distributed through out the dataset, then the expected count of retain images found in forget-infected clusters is given by.

$$
T_2 = \sum_{\phi_{ij}} \sum_{m=0}^{|\phi_{ij}|-1} {|\phi_{ij}|-1 \choose m} \left(\frac{1}{cK}\right)^m \left(1 - \frac{1}{cK}\right)^{|\phi_{ij}|-1-m} \left(\frac{N}{cK} - 1 - m\right) \tag{34}
$$

Here the inner sum represents the expect number of retain samples achieved from clusters, affected by the forget samples, while the outer sum accumulated the expected number per cluster over all clusters.  $\phi_{ij} \subseteq \Gamma_{ij}$  is the affected portion of the cluster by forget samples, and hence  $\sum_{\phi_{ij}} = \sum_{i,j} |\phi_{ij}| =$  $N_F$  and  $|\phi_{ij}| \leq \frac{N}{cK}$ . We can create an asymptotic bound for Equation (35) by substituting  $|\phi_{ij}|$  as  $\frac{N}{cK}$ , and then summing up the outer sum, we get.

T\_2 = 
$$
\mathcal{O}\left(\sum_{m=0}^{\frac{N}{S}-1} \binom{\frac{N}{cK}-1}{m}\right) \left(\frac{1}{cK}\right)^m \left(1 - \frac{1}{cK}\right)^{\frac{N}{cK}-1-j}
$$

$$
\left(\frac{N}{cK} - 1 - m\right) \sum_{\phi_{ij}}
$$

(35)

$$
T_2 = \mathcal{O}\left(\sum_{m=0}^{\frac{N}{cK}-1} \binom{\frac{N}{cK}-1}{m}\left(\frac{1}{cK}\right)^m \left(1 - \frac{1}{cK}\right)^{\frac{N}{cK}-1-j} \left(\frac{N}{cK} - 1 - m\right)|N_F| \right) \quad (36)
$$

On the other hand, we calculate the expectation of  $T_1$  as follows.

$$
T_1 = p_{miss}K\tag{37}
$$

We can calculate  $p_{\text{miss}}$  as follows. If the probability of 1 forget sample is in arbitrary one the cK clusters is  $\frac{|\Gamma_{ij}|}{N}$ , then probability that the 1 forget sample is not in one of the  $cK$ clusters is  $1-\frac{\Gamma_{ij}}{N}$ . We can extend this probability of  $N_F$  forget samples not in one of the cK clusters as  $(1 - \frac{\Gamma_{ij}}{N})^{N_F}$ . Since  $\Gamma_{ij} = \frac{N}{cK}$ , as the number of samples in each cluster is same by assumption, therefore  $(1 - \frac{1}{cK})^{N_F}$ , which is in fact  $p_{\text{miss}}$ . Henceforth,

$$
T_1 = (1 - \frac{1}{cK})^{N_F} K
$$
 (38)

Image /page/13/Figure/1 description: The image contains two bar charts. The top chart shows the running time in decibels (dBs) for four different methods: Distribution Matching, Gradient Matching, Improved Distribution Matching (Proposed), and Model Inversion (Proposed). The x-axis represents the number of images per class, with values of 1, 10, and 50. For 1 image per class, the running times are approximately 25, 19, 5, and 25 dBs, respectively. For 10 images per class, the running times are approximately 25, 31, 12, and 25 dBs. For 50 images per class, the running times are approximately 25, 21, 35, and 25 dBs. The bottom chart shows the test accuracy for the same four methods. For 1 image per class, the test accuracies are approximately 15, 20, 13, and 25. For 10 images per class, the test accuracies are approximately 23, 32, 25, and 30. For 50 images per class, the test accuracies are approximately 23, 34, 35, and 35.

Fig. 9. Benchmarking of dataset condensation of CIFAR10 dataset via Gradient Matching (GM), Distribution Matching (DM), and our proposed Improved Distribution Matching (IDM) and Model Inversion (MI) based condensation, while the dataset contains 5000 images per class

Combining Equations (36) and (39) into Equation (34), we get.

$$
N_r = \left(1 - \frac{1}{cK}\right)^{N_F} K + \mathcal{O}\left(\sum_{m=0}^{\frac{N}{S}-1} \left( \begin{array}{c} \frac{N}{cK} - 1 \\ m \end{array} \right) \left(\frac{1}{cK}\right)^m \left(1 - \frac{1}{cK}\right)^{\frac{N}{cK} - 1 - j} \left(\frac{N}{cK} - 1 - m\right)N_F \right) \right. (39)
$$

Expressing  $N_F = N_D - N_R$ , and representing  $\eta_R = \frac{N_r}{N_R}$ , we can express Equation (40) as.

\eta\_R = \left(1 - \frac{1}{cK}\right)^{N - \frac{K}{R}} + \mathcal{O}\left(\sum\_{m=0}^{\frac{N}{cK} - 1} \binom{\frac{N}{cK} - 1}{m} \left(\frac{1}{cK}\right)^m \left(1 - \frac{1}{cK}\right)^{\frac{N}{cK} - 1 - j} \left(\frac{N}{cK} - 1 - m\right)(N\_D - N\_R)\right) \tag{40}

Through application of binomial theorem, and simplification, we arrive at following expression.

$$
\eta_R = \left(1 - \frac{1}{cK}\right)^{N_D - cK} \frac{cK}{N_R} + \mathcal{O}\left(\left(\frac{N_D}{N_R} - 1\right)\left(1 - \frac{1}{cK}\right)\left(\frac{N_D}{cK} - 1\right)\right)
$$
(41)

Under the same assumptions, we realize the scenario where collection protocol leads to  $\eta_R = 1$ , equivalent to coupon collector's problem [\[47\]](#page-7-37). Under this problem, the expected number of forget samples to hit all the clusters is.

$$
N_F = cK \sum_{i=1}^{cK} \frac{1}{i} \tag{42}
$$

We use a well known sharp inequality  $\sum_{i=1}^{cK} \frac{1}{i} \ge \log(cK) +$  $\frac{1}{cK}$  to achieve a prophylactic equality in equation (43). Thus we arrive at.

$$
N_F = cK \log(cK) + 1 \tag{43}
$$

By substituting  $N_F = N_D - N_R$  and replacing the equality with an inequality for  $\eta_R$  to be less than 1 under expectation, then necessarily.

$$
N_R > N_D - cK \log(cK) + 1
$$
\n(44)

We take a key observation from [\[48\]](#page-7-38), where the distribution of losses of model over training dataset are more closer to zero mean, while the distribution of losses over test dataset are further away from zero mean. Through a strong connection between overfitting samples and membership inference attack, overfitted model (which achieve small over loss over training dataset) are more susceptible to inferring the presence or absence of random sample belonging to training dataset. more For a model trained on dataset  $D$ , it achieves a local minima of its associated loss with stationary condition  $\nabla_{\theta} \mathcal{L}(\mathcal{D}, \theta) = 0$ . Thus we capture the model achieving minima over dataset, as well as overfitting on it when  $\nabla_{\theta} \mathcal{L}(\mathcal{D}, \theta)$  and  $\mathcal{L}(\mathcal{D}, \theta)$  both approach zero. Consequently, we capture this essence through following overfitting metric with values in  $\mathbb{R}$ , such that smaller values would imply more overfitting per sample input-output pair  $(T_i, l_i) \in \mathcal{D}$ .

$$
|(\mathcal{L}((T_i, l_i), \theta)) - \text{mean}(|\nabla_{\theta} \mathcal{L}((T_i, l_i), \theta)|)| \qquad (45)
$$

Throughout our experiments, we utilized our proposed fast distribution matching based dataset condensation for progression of our modularized unlearning, if not specified. For the offline phase of modular unlearning framework, we set the  $L = 10$  iterations,  $L_1 = 20$  and  $L_2 = 20$  iterations with corresponding learning rate  $\alpha_1 = 10^{-4}$  and  $\alpha_2 = 10^{-5}$ respectively.

The MLP comprises of 3 linear layers with ReLU activation. The CNN comprises of 4 convolution layers with batch normalization and ReLU activation, then max-pooling operation, finally a 2-layered MLP with dropout operation. The VGG16 and ResNet18 architecture is according to [\[49\]](#page-7-39) and [\[50\]](#page-7-40) respectively.

The remaining hyperparameters used in our experimentation are summarized in table-1, where the hyperparameters of modular unlearning are described in online phase. For all the learning algorithms, the training iterations 'epochs $_{\text{main}}$ ' are set to 30 for experimentation over random sample forgetting, and 10 for case of single class-forgetting.  $\alpha$  generally means the learning rate associated with training associated with unlearning,  $T$  stands for the temperature associated with distillation based training, bs stands for size of batch during training. Other hyperparameters like ratio<sub>R</sub> stands for the ratio of randomly sample retain dataset utilized, pr stands for pruning ratio associated with model prunning algorithm, and epoch $L_1$  stands for the limit of of total unlearning epochs from end, till which  $\gamma$  associated with parameter  $L_1$  regularization is linearly decayed from its initial value via relation  $\gamma(1 - \frac{\text{current epoch}}{\text{epochs}_{\text{main}} - \text{epoch}_{L_1}})$ , otherwise  $\gamma$  is thresholded to zero [\[10\]](#page-7-1).

Image /page/14/Figure/0 description: The image displays three radar charts side-by-side, each representing different methods: Retraining, Catastrophic Forgetting, and Modular Unlearning. Each chart has five axes labeled MIA, RA, FA, TeA, and UT%, with a scale from 0% to 100%. Within each chart, five colored lines (blue, orange, green, red, and purple) represent different cycles (Cycle-1 to Cycle-5). The shaded area within each chart represents the performance across these metrics for each cycle. The charts show that for Retraining, all cycles perform similarly, with values around 50% for MIA, RA, and FA, and slightly lower for TeA and UT%. For Catastrophic Forgetting, the performance is generally lower across all metrics and cycles, with values mostly below 50%. Modular Unlearning shows a similar pattern to Retraining, with performance generally above 50% for MIA, RA, and FA, and slightly lower for TeA and UT%.

Fig. 10. Evolution of RA, FA, TrA and UT (represented as percentage here by taking ratio of unlearning time over all three methods) of unlearning via retraining, catastrophic forgetting and modular unlearning over 5 unlearning cycles with forgetting 10 percent of dataset per cycle

Image /page/14/Figure/2 description: This diagram illustrates a modular unlearning framework. The process begins with a training dataset and forget samples, which are processed by a Retain Dataset Reduction Framework to produce a reduced retain dataset. This dataset is then fed into a Modular Unlearning Framework consisting of an autoencoder with beginning and intermediate stages, and a final stage with a small classifier. The output of the modular framework is used to train a condensed model, which includes the beginning and intermediate autoencoder stages and a new large classifier. This condensed model is then trained on few epochs using remembrance samples, which are derived from a testing dataset, through a process called rapid unlearning. The final output is an unlearned augmented large classifier.

Fig. 11. Proposed methodology of utilization of proposed unlearning methodology in the scenario of unlearning in condensation

We consider two separate experimentations, where we we use both of our proposed dataset condensation schemes, i.e Fast Distribution Matching and Model Inversion based, for the offline phase of retain dataset reduction framework. With choice of dataset as CIFAR-10, we consider consider  $K = 50$ ,  $K = 500$  and  $K = 1000$ , while the CIFAR-10's training dataset has 5000 images per class. Furthermore, we also impose condition that if  $\eta_R > 0.7$ , then epochs<sub>main</sub> = 30. On the contrary, if  $0.4 < \eta_R \leq 0.7$ , then epochs<sub>main</sub> = 20, and lastly if  $\eta_R \leq 0.4$ , then epochs<sub>main</sub> = 10. This allows to preserve unlearning utility for our unlearning algorithm, when higher compression leads to reduction in accuracy during prolonged training.

The results are shown in figure 3 and figure 4, where fast distribution matching and model inversion based dataset condensation was utilized respectively. MU's unlearning time increases as K increases (because  $\eta_R$  increases due to equation (42), although not directly applicable as k-means leads to clusters of unequal sizes). Nevertheless, the performance of unlearning remains stable with variations in  $K$ , showing the potential of proposed unlearning to minimize retain dataset, and still have good unlearning performance.

We benchmark our dataset condensation with two main dataset condensation techniques, namely Gradient Matching [\[26\]](#page-7-17) and Distribution matching [\[27\]](#page-7-18). The results are shown in figure 5, where we attempt to condense 5000 images per class of CIFAR-10 into 1, 10 and 50 synthetic images. It is vivid from top and bottom bar graph that proposed approaches are either equivalent or better in performance, but with advantage of faster condensation time, especially for our fast distribution matching based approach.

We explore the case where unlearning happens over several unlearning cycles; for example, every month a new batch of data is deleted. We show the unlearning performance of the proposed unlearning with retraining, and catastrophic forgetting, over 5 unlearning cycles with 10% training deletion per cycle, in Figure 6. While modular unlearning maintains metrics like RA, FA, and TrA (Training accuracy), it shows utility capability in parallel to retraining and catastrophic forgetting, with the advantage of lesser unlearning time for at least the first 4 cycles. However, larger unlearning cycles lead to a smaller retained dataset size for the same K, and thus through Equation (42),  $\eta_R$  increases, leading to the same unlearning time as that of retraining or catastrophic forgetting.

Unlearning in dataset condensation is non-trivial, because if the whole training dataset is condensed into few images per class, then removing the information of few samples of training dataset can involve using the whole retain dataset in optimizing the condensed images, at least with techniques like gradient matching [\[26\]](#page-7-17) and distribution matching [\[27\]](#page-7-18). Not to mention that it is not guaranteed to unlearn, unlike the similar notion of catastrophic forgetting which can have some mathematical base for small distance between learned and unlearned parameters, because dataset condensation is so far based on heuristic ideas. To this end, we apply our proposed unlearning methodology in dataset condensation, by view the condensed dataset as a 'condensed model' with remembrance samples, which represent as unlearned condensed dataset. We create this correspondence by observing that this condensed model based proxy satisfies two importance properties of condensed dataset. First, it can be quickly used to train any new random model with considerably less amount of time, as compared to training on the original dataset. Secondly, the gained accuracy from this procedure is equivalent to that of the original dataset.

We exploit this correspondence into unlearning of forget samples from the condensed model (equivalent to removing of forget samples from original dataset), which can any new large image classification architecture. The strategy to achieve this illustrated in figure 7. Reduced retain dataset is constructed by using information of forget samples to be unlearning. The beginning and intermediate are assigned an auto-encoder architecture, and final is assigned some small classifier model. By application of modular unlearning over reduced retain dataset on this setup, we replace final with the target large architecture, and then train this modified setup with remembrance samples, leading to unlearned target architecture, augmented with the autoencoder.

The significance of this gets highlighted when we note that size of remembrance samples is very small, for example for CIFAR-10 case, we chose 10 images per class as remembrance samples, while original dataset comprised of 5000 images per class. Therefore, we achieve very fast training of large architectures, much significantly faster than the original training or even achievable through condensed dataset, with almost equivalent accuracy. This is characterized with learning without knowledge of forget samples, but at the cost of increase of parameter count in the target architecture due to addition of autoencoder. These results can witness from our experiments.
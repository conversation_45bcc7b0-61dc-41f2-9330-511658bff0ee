# Two Trades are not Baffled: Condensing Graph via Crafting Rational Gradient Matching

<PERSON><PERSON><PERSON><sup>1</sup>; <PERSON><PERSON><sup>1</sup>; <PERSON><PERSON><sup>1</sup>, <PERSON><sup>1</sup>; <PERSON><PERSON><sup>1</sup>, <PERSON><PERSON><PERSON><sup>3</sup>, <PERSON><PERSON><sup>3</sup>, <PERSON><sup>4</sup>, <PERSON><sup>2†</sup>, <PERSON><sup>1†</sup> <sup>1</sup>National University of Singapore  $2A*STAR$  Centre for Frontier AI Research (CFAR) <sup>3</sup>Shanghai AI Laboratory <sup>4</sup>University of Nevada, Reno

## Abstract

Training on large-scale graphs has achieved remarkable results in graph representation learning, but its cost and storage have raised growing concerns. As one of the most promising directions, graph condensation methods address these issues by employing gradient matching, aiming to condense the full graph into a more concise yet information-rich synthetic set. Though encouraging, these strategies primarily emphasize matching directions of the gradients, which leads to deviations in the training trajectories. Such deviations are further magnified by the differences between the condensation and evaluation phases, culminating in accumulated errors, which detrimentally affect the performance of the condensed graphs. In light of this, we propose a novel graph condensation method named CrafTing RationaL trajectory (CTRL), which offers an optimized starting point closer to the original dataset's feature distribution and a more refined strategy for gradient matching. Theoretically, CTRL can effectively neutralize the impact of accumulated errors on the performance of condensed graphs. We provide extensive experiments on various graph datasets and downstream tasks to support the effectiveness of CTRL.

<span id="page-0-0"></span>

# 1 Introduction

Graph neural networks (GNNs) have demonstrated superior performances in various graph analysis tasks, including node classification [\[52,](#page-11-0) [53\]](#page-11-1), link prediction [\[54,](#page-11-2) [55\]](#page-11-3), and graph generation [\[56,](#page-11-4) [57\]](#page-11-5). However, the training GNNs overheads on large-scale graphs comes at a high cost [\[59,](#page-11-6) [58\]](#page-11-7). One of the most straightforward ideas is to reduce the redundancy sub-counterpart like graph sparsification [\[62,](#page-12-0) [63\]](#page-12-1), which directly removes trivial edges or nodes, while graph coarsening [\[65,](#page-12-2) [67,](#page-12-3) [12\]](#page-9-0) shrink the graph size by merging similar nodes. Yet the rigid operation graph sparsification and coarsening conduct heavily rely on heuristic techniques [\[69\]](#page-12-4), which leads to the disregard for local details and limitation in compress ratio [\[68\]](#page-12-5).

The recently ever-developed graph condensation effectively opens a potential path for the upcoming large-scale graph training and storage, by generating a synthetic sub-counterpart to mimic the original graph [\[68,](#page-12-5) [71,](#page-12-6) [72\]](#page-12-7). As one of the well-established graph condensation frameworks, GCond takes the first step to adopt the condensation techniques in the computer vision realm [\[99\]](#page-13-0) and proposes a graph condensation concept based on gradient-matching. Concretely, GCond optimizes the synthetic graph by minimizing the gradient discrepancies of GNNs trained on the original graph and the synthetic graph. As a result, GCond is capable of condensing the graph under an extremely low condensation ratio without introducing significant performance degradation.

<sup>\*</sup>Equal contribution

<sup>†</sup>Corresponding authors

<sup>‡</sup> Project lead

<span id="page-1-0"></span>Image /page/1/Figure/0 description: This image contains three plots. Plot (a) shows the cosine distance over steps, with four lines representing CTRL (yellow), Cos (blue), Cos + Norm (teal), and Norm (red). The cosine distance for CTRL and Cos drops sharply from around 40 to near 0 within the first 0.25e6 steps and stays low. Cos + Norm drops to near 0 and stays low. Norm stays high around 25-30. Plot (b) shows the magnitude difference over steps, with the same four lines. CTRL and Cos + Norm remain close to 0. Cos shows large, periodic spikes, reaching up to 8. Norm stays around 1-2. Plot (c) shows the test accuracy (%) over epochs for the same four conditions. CTRL and Cos + Norm reach accuracies in the mid-90s. Cos reaches accuracies in the high-70s to low-80s. Norm shows significantly lower accuracy, fluctuating between 15% and 20%.

Figure 1: (a) and (b) illustrate the gradient difference during the optimization process of the condensed graphs using CTRL and the basic gradient matching methods with three gradient discrepancy metrics, step refers to the times of calculating gradient matching losses. (c) shows the performance of the condensed graphs during the optimization process for various methods.

Building on this milestone, a deluge of endeavors have adopted a similar paradigm for graph condensation [\[71,](#page-12-6) [128,](#page-15-0) [126\]](#page-14-0). However, gradient-matching primarily focuses on minimizing the cosine distance between gradients (termed Cos), leading to a discrepancy in magnitude between the gradients produced by the synthetic graph and those by the original graph. As shown in Fig[.1\(](#page-1-0)a)∼(c)<sup>[\\*](#page-1-1)</sup>, using either cosine distance or Euclidean distance as the sole metric for gradient matching leads to optimization collapse due to the inability to address gradient magnitude differences and the neglect of gradient angle differences. Based on the aforementioned methods, matching errors in this aspect have inadvertently been left unresolved (**Drawback**  $\bullet$ ). Worse still, this error can lead to a divergence in starting points between the condensation and evaluation phases, consequently further impacting the performance of the synthetic graph in the form of so-called accumulated errors [\[22\]](#page-10-0).

To address the above issue, a natural solution is to directly match the Euclidean distance between gradients (termed Norm) [\[72,](#page-12-7) [71\]](#page-12-6). However, as illustrated in Fig. [1\(](#page-1-0)a)∼(c), Norm performs poorly in graph data condensation tasks for node classification. One plausible reason is that, initially, the Euclidean distance between gradients is too small to guide the optimization of the synthetic graph effectively [\[23\]](#page-10-1). Additionally, the objective of minimizing the Euclidean distance overly emphasizes fitting the gradient magnitude while neglecting optimization of gradient orientations. To further reduce matching errors, we enhance existing gradient matching methods by combining cosine and Euclidean distances (termed Cos + Norm). As depicted in Fig. [1\(](#page-1-0)a)∼(c), this approach effectively reduces the final matching errors. However, this way introduces new challenges, tackle discrepancies in gradient orientations which require additional steps to address the optimization (Drawback ❷). This may be attributed to the fact that simple random sampling initialization may result in synthetic data initially exhibiting similar features, as shown in Fig. [1\(](#page-1-0)d), thereby introducing additional optimization challenges [\[90,](#page-13-1) [129\]](#page-15-1).

With this in mind, we introduce a novel graph condensation method called CrafTing RationaL gradient matching (CTRL). we consider both the direction and magnitude of gradients during gradient matching to minimize matching errors during the condensation phase, effectively reducing the impact of accumulated errors on the performance of synthetic graphs (Benefit  $\bullet$ ). Furthermore, we cluster each class of the original data into sub-clusters and sample from each sub-cluster as initialization values, as shown in Fig. [2,](#page-1-2) this approach can obtain initial synthetic data with even feature distribution, further assisting in reducing matching errors and accumulated errors (Benefit ❷).

<span id="page-1-2"></span>Image /page/1/Figure/5 description: This is a scatter plot showing three categories of data points: 'Samples', 'CTRL Sampling', and 'Random Sampling'. The 'Samples' are represented by small blue dots, clustered in the center and left side of the plot. The 'CTRL Sampling' points are marked by yellow stars, scattered across the plot, with a few concentrated near the center. The 'Random Sampling' points are indicated by teal triangles, with three visible points located towards the top and right edges of the plot. The plot appears to be a two-dimensional representation, possibly from a dimensionality reduction technique like PCA, illustrating the distribution and relationships between these different sampling methods.

Figure 2: Sampling example

Compared to previous methods, CTRL allows the systematic match-

ing of gradients and obtains initial synthetic data with even feature distribution (Fig. [2\)](#page-1-2), effectively reducing the accumulated errors caused by misalignment in gradient magnitude and gradient orientations. Moreover, by matching the gradient magnitude, CTRL can effectively capture the frequency distribution of signals, our studies reveal that the graph condensed by CTRL accurately mimics the frequency distribution of the original graph.

<span id="page-1-1"></span><sup>\*</sup>we provide detailed experimental settings in Appendix [C.3](#page-20-0)

We further assess the performance of CTRL through comprehensive experiments, to evaluate its generalization capabilities, we extend its application to node classification and graph classification tasks. Additionally, our synthetic graphs exhibit outstanding performance in various downstream tasks, including cross-architecture applications and neural architecture search.

With greater specificity, CTRL effectively reduces the Euclidean distance between gradients to just 3.7% of their original values while maintaining the cosine distance. Remarkably, we achieve lossless results on prominent datasets, including Citeseer, Cora, and Flickr for node classification, as well as the Ogbg-molbace and Ogbg-molbbbp datasets for graph classification. Notably, our approach yields new state-of-the-art results on the Ogbg-molbace and Ogbn-arxiv datasets, demonstrating improvements of 6.2% and 6.3%, respectively. Our main contributions are summarized as:

➠ Based on the analysis of existing graph condensation area, which dominated by gradient matching, we introduce CTRL, a simple and highly generalizable method for graph dataset condensation through finer-grained gradient matching.

• In CTRL, the optimization trajectory of our synthetic data closely approximates that of real data through a weighted combination of cosine distance and Euclidean distance and effectively captures the feature distribution of real data.

• We conduct experimental evaluations across 18 node classification tasks and 18 graph classification tasks. The results highlight that our method achieves state-of-the-art performances in 34 cases of experiments on 12 datasets with lossless performances on 5 datasets.

# 2 Effective Gradient Matching via CTRL

## 2.1 Graph Condensation via Gradient Matching

The objective of the graph condensation via gradient matching framework is to extract latent information from a large graph dataset  $\mathcal{T} = \{A, X, Y\}$  to synthesize a smaller dataset  $\mathcal{S} = \{A', X', Y'\},$ such that a model trained on S can achieve comparable results to one trained on T, where  $A \in \mathbb{R}^{N \times N}$ denotes the adjacency matrix,  $X \in \mathbb{R}^{N \times d}$  is the feature, and  $Y \in \mathbb{R}^{N \times 1}$  represents the labels, the first dimension of  $A^{\prime}$ ,  $X^{\prime}$ , and  $Y^{\prime}$  are  $N^{\prime}$ . To summarize, the process essentially revolves around matching the gradients generated during the training of GNNs on both datasets. To achieve this alignment, the following steps are undertaken: Fitstly, both on the large original dataset  $\mathcal T$  and the small synthetic dataset S, we train a GNN model parameterized with  $\theta$ , denotes as  $f_{\theta}$  and computes the parameter gradients at each layer for this model. We then use the gradients from the synthetic dataset to update the GNN model. The optimization goal is to minimize the distance  $D$  between the gradients at each layer, essentially, this aligns the training trajectories of the models over time, making them converge.

The above process can be written as follows:

min<sub>S</sub> E<sub>θ<sub>0</sub>~P<sub>θ<sub>0</sub></sub> ∑<sub>t=0</sub><sup>T-1</sup> [D(∇<sub>θ,t</sub><sup>S</sup>, ∇<sub>θ,t</sub><sup>T</sup>)], (1)</sub>

s.t. 

$$
∇θ,tS = rac{\partial 
\mathcal{L}(f_{\theta_t}(S), Y')}{\partial \theta}, ∇θ,tT = rac{\partial 
\mathcal{L}(f_{\theta_t}(T), Y)}{\partial \theta}.
$$

Where T is the number of steps of the whole training trajectory, and  $\theta_t$  denote the model parameters at time step t. The distance  $\mathcal D$  is further defined as the sum of the distance between two gradients at each layer. Given two gradient  $G^S \in \mathbb{R}^{d_1 \times d_2}$  and  $G^\mathcal{T} \in \mathbb{R}^{d_1 \times d_2}$  at a specific layer, we defined the distance  $dis(·, ·)$  as follows:

$$
\text{dis}(\mathbf{G}^{\mathcal{S}}, \mathbf{G}^{\mathcal{T}}) = \sum_{i=1}^{d_2} g(\mathbf{G}_i^{\mathcal{S}}, \mathbf{G}_i^{\mathcal{T}}).
$$
 (2)

Where  $G_i^S$ ,  $G_i^T$  are the *i*-th column vectors of the gradient matrices, and the metric g serves as an indicator for quantifying the difference between two gradient vectors.

<span id="page-3-1"></span>

### 2.2 Graph Condensation via Crafting Rational Trajectory

Refined matching criterion. Previous methods [\[71,](#page-12-6) [72\]](#page-12-7) often solely rely on either cosine or Euclidean distances between gradients to guide synthetic graph optimization. However, this approach typically neglects both gradient magnitudes and orientations.

To enhance gradient matching guidance, we combine cosine similarity and Euclidean distance into a linear combination. Initially, the optimization prioritizes aligning gradient directions due to the typically larger cosine distance between gradients in the initial phase. As the cosine distance diminishes, the focus of the Euclidean distance shifts primarily to measuring gradient magnitudes, allowing subsequent optimization processes to concentrate effectively on aligning the magnitudes of the gradients, ensuring they are well-aligned while avoiding interference with the alignment of gradient orientations. Additionally, We assign different weights to balance the significance of these two metrics through  $\beta$ . Formally, we determine the refined matching criterion  $g_{\text{ctrl}}$  as:

$$
g_{\text{ctrl}}(\mathbf{G}_i^{\mathcal{S}}, \mathbf{G}_i^{\mathcal{T}}) = \beta g_1 + (1 - \beta) g_2,
$$
  
s.t. 
$$
g_1 = ||\mathbf{G}_i^{\mathcal{S}} - \mathbf{G}_i^{\mathcal{T}}||, \ g_2 = 1 - \frac{\mathbf{G}_i^{\mathcal{S}} \cdot \mathbf{G}_i^{\mathcal{T}}}{||\mathbf{G}_i^{\mathcal{S}}|| ||\mathbf{G}_i^{\mathcal{T}}||}.
$$
 (3)

Initialization of the synthetic graph. Traditional graph condensation methods suffer from limitations in effectively capturing the feature distribution of real data. As shown in Fig. [1\(](#page-1-0)d), the random sampling initialization they employed brings uneven feature distribution, which leads to suboptimal initial optimization points in the condensation process, subsequently impairing the condensation process [\[90\]](#page-13-1). To tackle this problem, CTRL initially employs a clustering algorithm (termed  $Agg$ .), dividing each class of the original graph into M sub-clusters  $S_c = \{S_{c,0}, ..., S_{c,m}, ..., S_{c,M}\}\$ , where  $M$  is the number of nodes per class in the synthetic graph. Subsequently, a node feature is sampled from each cluster according to distribution  $P_{\phi}$  to serve as the initial values for the corresponding node feature in the synthetic graph  $x'_{c,m}$ . We formulate this initialization process as follows:

$$
x'_{c,m} \xleftarrow{\text{Init}} P_{\phi}(S_{c,m}), \text{ s.t. } S_c = Agg.(\mathbf{X}_c, M). \tag{4}
$$

After extensive experimental comparisons, we ultimately adopt the K-Means [\[88,](#page-13-2) [89\]](#page-13-3) and a uniform distribution as the methods for Agg. and  $P_{\phi}$  in our approach. This method ensures the initial synthetic graph's feature distribution is closer to the original graph's, while it only requires clustering data from a specific class at a time, which is exceptionally cost-effective in terms of computation.

Next, We provide the theoretical analysis to demonstrate the rationality of the proposed CTRL.

<span id="page-3-0"></span>Image /page/3/Figure/8 description: The image displays two phases of a process, labeled (i) Condensation Phase and (ii) Evaluation Phase. Both phases illustrate a sequence of states represented by black and red dots, with arrows indicating transitions. In the Condensation Phase, black dots labeled \$\hat{\theta}\_0\$, \$\hat{\theta}\_1\$, \$\hat{\theta}\_{t-1}\$, \$\hat{\theta}\_t\$, and \$\hat{\theta}\_{T-1}\$ are connected by dashed red arrows. Black dots labeled \$\hat{\theta}'\_1\$, \$\hat{\theta}'\_{t-1}\$, \$\hat{\theta}'\_t\$, and \$\hat{\theta}'\_{T-1}\$ are shown below the first sequence, connected by dashed black arrows originating from the corresponding red dots. Vertical dashed blue arrows labeled \$\delta\_{t-1}\$ and \$\delta\_t\$ indicate differences between states. The Evaluation Phase shows a similar structure with black dots labeled \$\hat{\theta}\_0\$, \$\hat{\theta}\_1\$, \$\hat{\theta}\_{t-1}\$, \$\hat{\theta}\_t\$, and \$\hat{\theta}\_{T-1}\$ connected by dashed red arrows. Below these, black dots labeled \$\theta^\*\_1\$, \$\theta^\*\_{t-1}\$, \$\theta^\*\_t\$, and \$\theta^\*\_{T-1}\$ are connected by dashed black arrows. Vertical dashed blue arrows labeled \$\epsilon\_{t-1}\$ and \$\delta\_t + I(\theta^\*\_{t-1}, \epsilon\_{t-1})\$ indicate differences and a more complex relationship between states.

Theoretical understanding. According to [\[22\]](#page-10-0) and [\[5\]](#page-9-1), the training trajectories of GNNs using original and synthetic graphs can be divided into T stages, donate as  $(\theta_0^*, \ldots, \theta_t^*, \ldots, \theta_{T-1}^*)$  and  $(\hat{\theta}_0, \dots, \hat{\theta}_t, \dots, \hat{\theta}_{T-1})$ , respectively, where  $\theta_0^*$ represents the initial parameters of all GNNs, i.e.  $\theta_0^* = \hat{\theta}_0$ . Then for any stage t, we define the following:

Definition 2.1 *Accumulated error*, which represents the parameter difference between models trained with synthetic and original data during the evaluation stage:

$$
\epsilon_t = \hat{\theta}_t - \theta_t^*.
$$
 (5)

Figure 3: Illustration of gradien matching.

This error originates from the challenge of completely eradicating gradient discrepancies during

the condensation phase. As a result, the starting points of GNNs' training exhibit differences between the condensation and evaluation phases in the same stage, which progressively accumulate during the evaluation phase, leading to an even greater divergence between GNNs trained on  $S$  and  $T$ , as illustrated in Fig. [3.](#page-3-0) To further analyze the origins of accumulated errors, we introduce the following definitions:

**Definition 2.2** *Matching Error* is the difference between the gradient trajectories generated by synthetic data and original data at this stage during condensation:

$$
\delta_{t+1} = \nabla_{\theta} L_S(f_{\hat{\theta}_t}) - \nabla_{\theta} L_T(f_{\hat{\theta}_t}).
$$
\n(6)

Definition 2.3 *Initialization Error* arises due to different initial parameters used during training.

$$
I(\theta_t^*, \epsilon_t) = \nabla_{\theta} L_T(f_{\theta_t^* + \epsilon_t}) - \nabla_{\theta} L_T(f_{\theta_t^*}).
$$
\n<sup>(7)</sup>

It's important to acknowledge that because the initial training points in the condensation and evaluation phases differ, the training outcomes will inevitably vary, even if the synthetic data manages to generate gradients that are identical to those of the original data at certain stages. Specifically, if there's a stage t at which  $\delta_t = 0$ , indicating no difference in gradients at that point, discrepancies in the final training results will still be present as long as there's an initial error in setting up the model. This underlines the significance of the initialization phase in the overall training process and its impact on the eventual performance of the model.

Theorem 2.4 During the evaluation process, the accumulated error at any stage is determined by the sum of matching and initialization errors.

*Proof of Theorem 2.4.*

$$
\epsilon_{t+1} = \hat{\theta}_{t+1} - \theta_{t+1}^{*}
$$
  
\n
$$
= (\hat{\theta}_{t} + \nabla_{\theta} L_{S}(f_{\hat{\theta}_{t}})) - (\theta_{t}^{*} + \nabla_{\theta} L_{T}(f_{\theta_{t}^{*}}))
$$
  
\n
$$
= (\hat{\theta}_{t} - \theta_{t}^{*}) + (\nabla_{\theta} L_{S}(f_{\hat{\theta}_{t}}) - \nabla_{\theta} L_{T}(f_{\hat{\theta}_{t}}))\nabla_{\theta} + (\nabla_{\theta} L_{T}(f_{\theta_{t}^{*}+\epsilon_{t}}) - \nabla_{\theta} L_{T}(f_{\theta_{t}^{*}}))
$$
  
\n
$$
= \epsilon_{t} + \delta_{t+1} + I(\theta_{t}^{*}, \epsilon_{t})
$$
  
\n
$$
= \sum_{i=0}^{t} \delta_{i+1} + \sum_{i=1}^{t} I(\theta_{i}^{*}, \epsilon_{i}) + \epsilon_{0}
$$
  
\n
$$
= \sum_{i=0}^{t} \delta_{i+1} + \sum_{i=1}^{t} I(\theta_{i}^{*}, \epsilon_{i}). \tag{8}
$$

Where  $\epsilon_0$  is the error introduced by network initialization, which can be eliminated over multiple iterations, thus the accumulated error can be represented as the sum of all matching errors in the condensation phase and all initialization errors in the evaluation phase.  $\Box$ 

Corollary 2.5 Early matching errors continuously affect subsequent initialization errors.

*Proof of corollary 2.5.*

<span id="page-4-0"></span>
$$
I(\theta_t^*, \epsilon_t) = \nabla_{\theta} L_T(f_{\theta_t^* + \epsilon_t}) - \nabla_{\theta} L_T(f_{\theta_t^*})
$$
  
= 
$$
\nabla_{\theta} L_T(f_{\theta_t^* + \sum_{i=0}^{t-1} \delta_{i+1} + \sum_{i=1}^{t-1} I(\theta_t^*, \epsilon_i)}) \nabla_{\theta} - \nabla_{\theta} L_T(f_{\theta_t^*}).
$$
 (9)

As illustrated by Eq. [9,](#page-4-0) the initialization error at any given stage is derived from both the matching error and the initialization errors of the preceding stages. This implies that the source of the initialization errors is still the matching error. If the matching error is not eliminated during the condensation process, this will not only result in the emergence of initialization errors but also lead to their gradual expansion as the evaluation progresses, ultimately causing a rapid increase in cumulative errors, which in turn adversely affects the performance of the synthetic graph.  $\Box$ 

**Proposition 2.6** Directly using  $\|\delta_i\|_2^2$  as the optimization objective is limited.

To minimize accumulated error, an intuitive solution is to optimize synthetic data using the Euclidean distance between gradients as the loss function during the gradient matching stage. This method has achieved good results in image and graph classification tasks [\[127,](#page-15-2) [71\]](#page-12-6). However, we found that in condensation tasks for node classification in graph datasets, using Euclidean distance as the loss function can lead to worse outcomes than at initialization (as shown in Fig. [1\(](#page-1-0)c) and Fig. [4\)](#page-5-0).

This occurs because when the discrepancy in gradient magnitudes is minimal, employing Euclidean distance as the loss function can make it susceptible to early convergence into local minima [\[23\]](#page-10-1). Consequently, this hampers the optimization of the synthetic dataset and results in suboptimal outcomes.

Corollary 2.7 The proposed strategy can optimize the accumulated error by reducing the matching error during the condensation phase.

$$
\epsilon'_{t+1} = \sum_{i=0}^{t} \delta'_{i+1} + \sum_{i=1}^{t} I'(\theta_i^*, \epsilon_i) < \epsilon_{t+1}.\tag{10}
$$

Where  $\epsilon'$ ,  $\delta'$ , I' are the reduced  $\epsilon$ , I,  $\delta$ , respectively. The above corollary implies that our approach can mitigate the initialization errors in the evaluation phase by significantly reducing the matching errors during the condensation phase, thereby effectively diminishing the impact of accumulated errors on the performance of the synthetic graph.

Model summary. In our approach, by introducing a synthetic graph initialization method that approximates the feature distribution of the original graph and a refined matching criterion, we generate a synthetic graph that more accurately matches the magnitude and direction of gradients produced by the original graph, thereby effectively reducing matching errors, as depicted in Fig. [4.](#page-5-0) This, consequently, leads to a significant enhancement in the performance of the synthetic graph by substantially mitigating the impact of accumulated errors. It is noteworthy that although the final L2 norm of the matching errors between the two methods does not significantly differ in absolute terms, the quality of the resulting synthetic graph is vastly different, as shown in Fig.  $1(c)$ . This outcome is consistent with our theoretical analysis.

<span id="page-5-0"></span>Image /page/5/Figure/6 description: This is a line graph showing the L2 norm over steps for two different conditions, CTRL and Norm. The x-axis represents the step number, ranging from 0.0 to 1.6e+6. The y-axis represents the L2 norm, ranging from 0 to 6. The CTRL line, shown in yellow, starts at a high value of approximately 6.5 and quickly decreases to a low value around 0.2, fluctuating slightly between 0.2 and 0.5 for the rest of the steps. The Norm line, shown in red, starts at approximately 1.5 and remains relatively stable around 1.5 throughout the entire range of steps, with minor fluctuations.

Figure 4: L2 norm of the matching error.

<span id="page-5-1"></span>Table 1: Performance comparison to baselines in the node classification tasks. We report test accuracy (%) on Citeseer, Cora, Ogbn-arxiv, Ogbn-xrt, Flickr and Reddit. Bold entries are best results, highlight mark the lossless results. The accuracy with the error bar reported in the main table is the maximum of top-1 test results 5 times repeated.

| Dataset    | Ratio                   | Random                                                            | Herding                                                           | K-Center                                                                  | Coarsening                                                        | DC-Graph                                                          | GCond                                                             | SFGC                                                                      | CTRL(ours)                                                                                | Whole Dataset       |
|------------|-------------------------|-------------------------------------------------------------------|-------------------------------------------------------------------|---------------------------------------------------------------------------|-------------------------------------------------------------------|-------------------------------------------------------------------|-------------------------------------------------------------------|---------------------------------------------------------------------------|-------------------------------------------------------------------------------------------|---------------------|
| Citeseer   | 0.90%<br>1.80%<br>3.60% | $54.4_{	extpm 4.4}$<br>$64.2_{	extpm 1.7}$<br>$69.1_{	extpm 0.1}$ | $57.1_{	extpm 1.5}$<br>$66.7_{	extpm 1.0}$<br>$69.0_{	extpm 0.1}$ | $52.4_{	extpm 2.8}$<br>$64.3_{	extpm 1.0}$<br>$69.1_{	extpm 0.1}$         | $52.2_{	extpm 0.4}$<br>$66.9_{	extpm 0.9}$<br>$65.3_{	extpm 0.5}$ | $66.8_{	extpm 1.5}$<br>$59.0_{	extpm 0.5}$<br>$66.3_{	extpm 1.5}$ | $70.5_{	extpm 1.2}$<br>$70.6_{	extpm 0.9}$<br>$69.8_{	extpm 1.4}$ | $71.4_{	extpm 0.5}$<br>$72.4_{	extpm 0.4}$<br>$70.6_{	extpm 0.7}$         | $73.3_{	extpm 0.4}$<br>$73.5_{	extpm 0.1}$<br>$73.4_{	extpm 0.2}$                         | $71.7_{	extpm 0.1}$ |
| Cora       | 1.30%<br>2.60%<br>5.20% | $63.6_{	extpm 3.7}$<br>$72.8_{	extpm 1.1}$<br>$76.8_{	extpm 0.1}$ | $67.0_{	extpm 1.3}$<br>$73.4_{	extpm 1.0}$<br>$76.8_{	extpm 0.1}$ | $64.0_{	extpm 2.3}$<br>$73.2_{	extpm 1.2}$<br>$76.7_{	extpm 0.1}$         | $31.2_{	extpm 0.2}$<br>$65.2_{	extpm 0.6}$<br>$70.6_{	extpm 0.1}$ | $67.3_{	extpm 1.9}$<br>$67.6_{	extpm 3.5}$<br>$79.8_{	extpm 1.3}$ | $79.8_{	extpm 1.3}$<br>$80.1_{	extpm 0.6}$<br>$79.3_{	extpm 0.3}$ | $80.1_{	extpm 0.4}$<br>$81.7_{	extpm 0.5}$<br>$81.6_{	extpm 0.8}$         | $	extbf{81.9}_{	extpm 0.1}$<br>$	extbf{81.8}_{	extpm 0.1}$<br>$	extbf{81.8}_{	extpm 0.1}$ | $81.2_{	extpm 0.2}$ |
| Ogbn-arxiv | 0.05%<br>0.25%<br>0.50% | $47.1_{	extpm 3.9}$<br>$57.3_{	extpm 1.1}$<br>$60.0_{	extpm 0.9}$ | $52.4_{	extpm 1.8}$<br>$58.6_{	extpm 1.2}$<br>$60.4_{	extpm 0.8}$ | $47.2_{	extpm 3.0}$<br>$56.8_{	extpm 0.8}$<br>$60.3_{	extpm 0.4}$         | $35.4_{	extpm 0.3}$<br>$43.5_{	extpm 0.2}$<br>$50.4_{	extpm 0.1}$ | $58.6_{	extpm 0.4}$<br>$59.9_{	extpm 0.3}$<br>$59.5_{	extpm 0.3}$ | $59.2_{	extpm 1.1}$<br>$63.2_{	extpm 0.3}$<br>$64.0_{	extpm 1.4}$ | $65.5_{	extpm 0.7}$<br>$66.1_{	extpm 0.4}$<br>$66.8_{	extpm 0.4}$         | $65.6_{	extpm 0.3}$<br>$66.5_{	extpm 0.3}$<br>$	extbf{67.6}_{	extpm 0.2}$                 | $71.4_{	extpm 0.1}$ |
| Ogbn-xrt   | 0.05%<br>0.25%<br>0.50% | $55.1_{	extpm 2.1}$<br>$64.2_{	extpm 1.4}$<br>$65.7_{	extpm 2.1}$ | $60.6_{	extpm 2.3}$<br>$64.4_{	extpm 1.7}$<br>$68.3_{	extpm 2.2}$ | $52.7_{	extpm 3.5}$<br>$60.8_{	extpm 2.2}$<br>$62.3_{	extpm 3.4}$         | $48.4_{	extpm 0.4}$<br>$51.3_{	extpm 0.3}$<br>$56.9_{	extpm 0.1}$ | $60.4_{	extpm 0.5}$<br>$61.5_{	extpm 0.2}$<br>$68.2_{	extpm 0.3}$ | $55.1_{	extpm 0.3}$<br>$68.1_{	extpm 0.4}$<br>$69.3_{	extpm 0.4}$ | $55.4_{	extpm 0.1}$<br>$67.7_{	extpm 0.1}$<br>$66.5_{	extpm 0.1}$         | $61.1_{	extpm 0.5}$<br>$	extbf{69.4}_{	extpm 0.4}$<br>$70.4_{	extpm 0.1}$                 | $73.4_{	extpm 0.1}$ |
| Flickr     | 0.10%<br>0.50%<br>1.0%  | $41.8_{	extpm 2.0}$<br>$44.0_{	extpm 0.4}$<br>$44.6_{	extpm 0.2}$ | $42.5_{	extpm 1.8}$<br>$43.9_{	extpm 0.9}$<br>$44.4_{	extpm 0.6}$ | $42.0_{	extpm 0.7}$<br>$43.2_{	extpm 0.1}$<br>$44.1_{	extpm 0.4}$         | $41.9_{	extpm 0.2}$<br>$44.5_{	extpm 0.1}$<br>$44.6_{	extpm 0.1}$ | $46.3_{	extpm 0.2}$<br>$45.9_{	extpm 0.1}$<br>$45.8_{	extpm 0.1}$ | $46.5_{	extpm 0.4}$<br>$47.1_{	extpm 0.1}$<br>$47.1_{	extpm 0.1}$ | $46.6_{	extpm 0.2}$<br>$47.0_{	extpm 0.1}$<br>$47.1_{	extpm 0.1}$         | $47.1_{	extpm 0.1}$<br>$47.4_{	extpm 0.1}$<br>$47.5_{	extpm 0.1}$                         | $47.2_{	extpm 0.1}$ |
| Reddit     | 0.01%<br>0.05%<br>0.50% | $46.1_{	extpm 4.4}$<br>$58.0_{	extpm 2.2}$<br>$66.3_{	extpm 1.9}$ | $53.1_{	extpm 2.5}$<br>$62.7_{	extpm 1.0}$<br>$71.0_{	extpm 1.6}$ | $	extbf{46.6}_{	extpm 2.3}$<br>$53.0_{	extpm 3.3}$<br>$58.5_{	extpm 2.1}$ | $40.9_{	extpm 0.5}$<br>$42.8_{	extpm 0.8}$<br>$47.4_{	extpm 0.9}$ | $88.2_{	extpm 0.2}$<br>$89.5_{	extpm 0.1}$<br>$90.5_{	extpm 1.2}$ | $88.0_{	extpm 1.8}$<br>$89.6_{	extpm 0.7}$<br>$90.1_{	extpm 0.5}$ | $	extbf{89.7}_{	extpm 0.2}$<br>$90.0_{	extpm 0.3}$<br>$90.3_{	extpm 0.3}$ | $89.2_{	extpm 0.2}$<br>$90.6_{	extpm 0.2}$<br>$91.9_{	extpm 0.4}$                         | $93.9_{	extpm 0.0}$ |

# 3 Experiments

## 3.1 Datasets & Implementation Details

Datasets. To better evaluate the performance of CTRL, we conducted experiments on six node classification datasets: Cora, Citeseer [\[95\]](#page-13-4), Ogbn-arxiv [\[113\]](#page-14-1), Ogbn-xrt [\[117\]](#page-14-2), Flickr [\[150\]](#page-16-0), and

<span id="page-6-0"></span>Image /page/6/Figure/0 description: The image displays three plots. Plot (a) shows the accuracies on Reddit over epochs, with two lines representing 'CTRL init.' (yellow) and 'Random init.' (blue), and a shaded pink region indicating improvement. The x-axis is labeled 'Epochs' and ranges from 150 to 600. The y-axis is labeled 'Acc. (%)' and ranges from 84 to 90. Plot (b) shows accuracies on Ogbn-arxiv over epochs, also with 'CTRL init.' (yellow) and 'Random init.' (blue) lines and a pink shaded improvement region. The x-axis is labeled 'Epochs' and ranges from 30 to 600. The y-axis is labeled 'Acc. (%)' and ranges from 54 to 66. Plot (c) presents a sensitivity analysis of beta, showing accuracies for 'Cora' (yellow), 'Ogbn-arxiv' (green), and 'Citeseer' (blue) against different values of beta. The x-axis is labeled 'β' and ranges from 0.05 to 0.9. The y-axis is labeled 'Acc. (%)' and ranges from 54 to 84.

<span id="page-6-2"></span><span id="page-6-1"></span>Figure 5: (a) and (b) show the improvement by employing the initialization method of CTRL. (c) indicates the sensitivity analysis experiments conducted on Cora, Citeseer, and Ogbn-arxiv, with condensation ratios of 1.3%, 0.9%, and 0.25%, respectively.

Reddit [\[118\]](#page-14-3), as well as six graph classification datasets, multiple molecular datasets from Open Graph Benchmark (OGB) [\[113\]](#page-14-1), TU Datasets (MUTAG and NCI1) [\[119\]](#page-14-4), and one superpixel dataset CIFAR10 [\[120\]](#page-14-5). For the dataset partitioning settings, we follow the settings of [\[68\]](#page-12-5) and [\[71\]](#page-12-6).

Implementation details. For node classification, without specific designation, in the condense stage, we adopt the 2-layer GCN with 128 hidden units as the backbone, and we adopt the settings on [\[68\]](#page-12-5) and [\[71\]](#page-12-6). For graph classification, we adopt a 3-layer GCN with 128 hidden units as the model for one-step gradient matching. More details can be found in Appendix [B.](#page-17-0)

Evaluation. We first train a GCN classifier on synthetic graphs generated by different baseline algorithms. Then, we evaluate the classifier's performance on the test sets of real graphs. In the context of node classification tasks, we condense the entire graph and the training set graphs for transductive datasets and inductive datasets, respectively. For graph classification tasks, we repeat the generation process of condensed graphs 5 times with different random seeds and train GCN on these graphs with 10 different random seeds. Finally, we report the average performance and variance across all kinds of experiments.

## 3.2 Comparison with Baselines

For node classification tasks, we compare our proposed CTRL with 7 existing popular compression baselines: graph coarsening methods [\[112\]](#page-14-6), Random, which randomly selected nodes to form the original graph, core set methods (Herding [\[110\]](#page-14-7) and K-Center [\[111\]](#page-14-8)), DC-Graph [\[68\]](#page-12-5), and the state-of-the-art graph condensation methods GCond [\[68\]](#page-12-5) and SFGC [\[72\]](#page-12-7). For graph classification tasks, we compare the proposed method with the above three coreset methods: Random, Herding, and K-Center, the above DC-Graph, and Doscond [\[71\]](#page-12-6). We showcase the performances in Table [1](#page-5-1) and Table [2,](#page-7-0) from which we can make the following observations:

Obs 1. In most cases of the node classification tasks, CTRL consistently surpasses the baseline methods, establishing a new benchmark. Compared with GCond, a conventional gradient matching approach, CTRL demonstrates superior performance across three traditional transductive datasets: Cora, Citeseer, and Ogbn-arxiv. Specifically, it delivers up to a 6.4% enhancement in performance, averaging an increase of 3.2%, and achieves lossless condensation on the first two datasets. Even compared to the state-of-the-art unstructured dataset condensation methods, our approach still achieves better results in the majority of tasks with less time and space consumption. Additionally, our method is currently the only one capable of achieving lossless condensation on the Flickr.

Obs 2. Across all datasets for graph classification, CTRL outperformed the current state-of-theart condensation method, DosCond, particularly on the ogbg-molbace dataset, where it achieved an average improvement of 5.9% and a peak of 6.2%. Furthermore, CTRL is the first method accomplishing lossless condensation on both the ogbg-molbace and ogbg-molbbbp datasets. This finding indicates that the issue arising from measuring gradient differences using a single metric is not confined to graph condensation tasks based on node classification alone. It further demonstrates the generalizability and necessity of our approach, suggesting that our method has the potential to be directly applied to other condensation methods.

Remark. To further validate the generality of our method, we also applied a more refined gradient matching strategy to image dataset condensation, resulting in an average improvement of about 0.2% and a maximum improvement of about 1.1%. Although there is a certain degree of improvement, it is not as remarkable as the improvements observed in graph dataset condensation. A possible reason for this could be that matching the magnitude of gradients helps the synthetic graph to fit the frequency domain distribution of the original graph. This point is elaborated in detail in Appendix [C.2](#page-19-0) and subsequent experiments.

<span id="page-7-0"></span>Table 2: The graph classification performance comparison to baselines. *CTRL achieves the highest results in all cases on graph classification and lossless results in 2 of 6 datasets.* We report the ROC-AUC for the first three datasets and accuracies (%) for others. Whole Dataset indicates the performance of the original dataset. Bold entries are best results, highlight mark the lossless results.

| Dataset      | Ratio                   | Random                                                           | Herding                                                          | K-Center                                                        | DCG                                                              | DosCond                                                          | CTRL(ours)                                                            | Whole Dataset      |
|--------------|-------------------------|------------------------------------------------------------------|------------------------------------------------------------------|-----------------------------------------------------------------|------------------------------------------------------------------|------------------------------------------------------------------|-----------------------------------------------------------------------|--------------------|
| ogbg-molbace | 0.20%<br>1.70%<br>8.30% | $0.580_{\pm 0.067}$<br>$0.598_{\pm0.073}$<br>$0.632_{\pm 0.047}$ | $0.548_{\pm 0.034}$<br>$0.639_{\pm0.039}$<br>$0.683_{\pm 0.022}$ | $0.548_{\pm 0.034}$<br>$0.591_{\pm0.056}$<br>$0.589_{\pm0.025}$ | $0.623_{\pm 0.046}$<br>$0.655_{\pm0.033}$<br>$0.652_{\pm 0.013}$ | $0.657_{\pm 0.034}$<br>$0.674_{\pm0.035}$<br>$0.688_{\pm0.012}$  | $0.716_{\pm0.025}$<br>$0.736_{\pm 0.014}$<br>$0.745_{\pm 0.009}$      | $0.724_{\pm0.005}$ |
| ogbg-molbbbp | 0.10%<br>1.20%<br>6.10% | $0.519_{\pm0.016}$<br>$0.586_{\pm0.040}$<br>$0.606_{\pm 0.020}$  | $0.546_{\pm0.019}$<br>$0.605_{\pm0.019}$<br>$0.617_{\pm 0.003}$  | $0.546_{\pm0.019}$<br>$0.530_{\pm0.039}$<br>$0.576_{\pm 0.019}$ | $0.559_{\pm0.044}$<br>$0.568_{\pm0.032}$<br>$0.579_{\pm0.032}$   | $0.581_{\pm 0.005}$<br>$0.605_{\pm0.008}$<br>$0.620_{\pm 0.007}$ | $0.592_{\pm0.011}$<br>$0.629_{\pm0.006}$<br>$0.650_{\pm 0.005}$       | $0.646_{\pm0.004}$ |
| ogbg-molhiv  | 0.01%<br>0.06%<br>0.30% | $0.719_{\pm 0.009}$<br>$0.720_{\pm0.011}$<br>$0.721_{\pm 0.014}$ | $0.721_{\pm0.002}$<br>$0.725_{\pm0.006}$<br>$0.725_{\pm 0.003}$  | $0.721_{\pm0.002}$<br>$0.713_{\pm0.009}$<br>$0.725_{\pm 0.006}$ | $0.718_{\pm0.013}$<br>$0.728_{\pm0.002}$<br>$0.726_{\pm 0.010}$  | $0.726_{\pm 0.003}$<br>$0.728_{\pm0.005}$<br>$0.731_{\pm0.004}$  | $0.732_{\pm 0.006}$<br>$0.734_{\pm0.008}$<br>$0.737_{\pm 0.006}$      | $0.757_{\pm0.008}$ |
| <b>MUTAG</b> | 1.30%<br>13.30%         | $67.47_{\pm 9.74}$<br>$77.89_{\pm 7.55}$                         | $70.84_{\pm 7.71}$<br>$80.42_{\pm 1.89}$                         | $70.84_{\pm 7.71}$<br>$81.00_{\pm 2.51}$                        | $75.00_{\pm 8.16}$<br>$82.66_{\pm 0.68}$                         | $82.21_{\pm1.61}$<br>$82.76_{\pm 2.31}$                          | $83.06_{\pm 3.15}$<br>$83.16_{\pm3.62}$                               | $88.63_{\pm 1.44}$ |
| NCI1         | 0.10%<br>0.60%<br>3.10% | $51.27_{\pm1.22}$<br>$54.33_{\pm 3.14}$<br>$58.51_{\pm 1.73}$    | $53.98_{\pm0.67}$<br>$57.11_{\pm0.56}$<br>$58.94_{\pm 0.83}$     | $53.98_{\pm0.67}$<br>$53.21_{\pm 1.44}$<br>$56.58_{\pm3.08}$    | $51.14_{\pm 1.08}$<br>$51.86_{\pm 0.81}$<br>$52.17_{\pm 1.90}$   | $56.58_{\pm 0.48}$<br>$58.02_{\pm 1.05}$<br>$60.07_{\pm1.58}$    | $	extbf{56.69}_{\pm0.69}$<br>$58.04_{\pm 1.28}$<br>$60.14_{\pm 1.73}$ | $71.70_{\pm0.20}$  |
| CIFAR10      | 0.06%<br>0.20%<br>1.10% | $15.61_{\pm 0.52}$<br>$23.07_{\pm 0.76}$<br>$30.56_{\pm 0.81}$   | $22.38_{\pm 0.49}$<br>$28.81_{\pm 0.35}$<br>$33.94_{\pm 0.37}$   | $22.37_{\pm0.50}$<br>$20.93_{\pm 0.62}$<br>$24.17_{\pm0.51}$    | $21.60_{\pm 0.42}$<br>$29.27_{\pm 0.77}$<br>$34.47_{\pm 0.52}$   | $24.70_{\pm 0.70}$<br>$30.70_{\pm0.23}$<br>$35.34_{\pm0.14}$     | $29.30_{\pm 0.27}$<br>$31.21_{\pm0.20}$<br>$35.53_{\pm 0.38}$         | $50.75_{\pm0.14}$  |

<span id="page-7-1"></span>Table 3: Comparison of the frequency domain similarity between synthetic graphs from CTRL and GCond with original graphs.

Table 4: Neural Architecture Search. Methods are compared in validation accuracy correlation and test accuracy obtained by searched architecture. Whole refers to the test accuracy obtained by the architecture searched using the whole dataset.

| Dataset  | Pearson Correlation / P-value |                   | Dataset  | Pearson Correlation / Performance(%) |           |           |                  | Whole Acc.(%) |
|----------|-------------------------------|-------------------|----------|--------------------------------------|-----------|-----------|------------------|---------------|
|          | GCond                         | CTRL              |          | Random                               | Herding   | GCond     | CTRL             |               |
| Cora     | 0.90/0.016                    | <b>0.96/0.003</b> | Cora     | 0.40/82.9                            | 0.21/82.9 | 0.76/83.1 | <b>0.86/83.2</b> | 82.6          |
| Citeseer | 0.92/0.009                    | <b>0.95/0.003</b> | Citeseer | 0.56/71.4                            | 0.29/71.3 | 0.79/71.3 | <b>0.93/72.0</b> | 71.6          |
| Pumbed   | 0.89/0.016                    | <b>0.93/0.014</b> | Pubmed   | 0.55/80.0                            | 0.21/79.9 | 0.81/79.7 | <b>0.83/80.1</b> | 80.5          |

## 3.3 Analysis of Components and Performance

Ablation study  $\&$  Sensitivity analysis. We further study the impact of our initialization method and the sensitivity of  $\beta$ , with specific experimental details provided in Appendix [B.3.](#page-18-0) Here we can make the following observations:

Obs 3. As shown in Fig. [5\(a\)](#page-6-0) and [5\(b\),](#page-6-1) the initialization strategy in CTRL not only improves the performances by approximately∼ 10% at the beginning stages but also leads to significant improvements throughout the condensation process. This suggests that CTRL's initialization method offers a superior starting point for synthetic graphs.

**Obs 4.** As indicated in Fig. [5\(c\),](#page-6-2) when the value of  $\beta$  varies, the change in test accuracy does not exceed 1.5%. These results indicate that our proposed matching criterion does not overly rely on the choice of the  $\beta$ .

### Quantitative analysis of frequency domain similarity.

To assess the fidelity with which CTRL-generated synthetic graphs replicate the frequency domain distribution of the original graph, we first calculate various metrics separately for both the synthetic and original graphs. The chosen metrics for evaluating the frequency domain distribution are detailed in Appendix [C.1.](#page-18-1) We then visually represent the correlation by computing the Pearson coefficient, which illustrates the general trend. Tab. [3](#page-7-1) exhibits that:

Obs 5. The comparison a greater similarity in the frequency distribution between the CTRL-generated synthetic graph and the original graph, which provides support for the previously mentioned capability of matching gradient magnitudes to approximate the frequency domain distribution.

Neural architecture search evaluation. Following the setting in [\[68\]](#page-12-5), we search 480 neural architectures on condensed graphs of the Cora, Citeseer, and Pubmed datasets. We documented the Pearson correlation between the validation accuracies achieved by architectures trained on condensed graphs versus those trained on original graphs and the average test accuracy of the searched architectures.

Obs 6. The results reported in Table [4](#page-7-1) demonstrate the consistent superiority of our method, evidenced by higher Pearson correlation coefficients [\[70\]](#page-12-8) and improved test performance, with enhancements of up to  $0.14$  and  $0.4\%$ , respectively. Notably, our method is capable of discovering architectures that outperform those searched through the entire Cora and Citeseer.

<span id="page-8-0"></span>Table 5: Performance across different GNN architectures. Avg. and Std. : the average performance and the standard deviation of the results of APPNP, Cheby, GCN, SAGE and SGC,  $\Delta(\%)$  denotes the improvements upon the DG-Graph. Bold entries are the best results.

| Datasets                      | Methods  | Architectures |      |       |       |      | Statistics |      |             |            |                 |
|-------------------------------|----------|---------------|------|-------|-------|------|------------|------|-------------|------------|-----------------|
|                               |          | MLP           | GAT  | APPNP | Cheby | GCN  | SAGE       | SGC  | Avg.        | Std.       | Δ(%)            |
| Cora<br>(Ratio = 1.80%)       | DC-Graph | 67.2          | -    | 67.1  | 67.7  | 67.9 | 66.2       | 72.8 | 68.3        | 2.6        | $\uparrow$ 10.1 |
|                               | GCond    | 73.1          | 66.2 | 78.5  | 76.0  | 80.1 | 78.2       | 79.3 | 78.4        | 4.9        | $\uparrow$ 11.7 |
|                               | CTRL     | 77.4          | 66.7 | 80.8  | 77.0  | 81.6 | 79.0       | 81.5 | <b>80.0</b> | <b>2.0</b> | $\uparrow$ 11.7 |
| Ogbn-arxiv<br>(Ratio = 0.25%) | DC-Graph | 59.9          | -    | 60.0  | 55.7  | 59.8 | 60.0       | 60.4 | 59.2        | 2.0        | -               |
|                               | GCond    | 62.2          | 60.0 | 63.4  | 54.9  | 63.2 | 62.6       | 63.7 | 61.6        | 3.7        | $\uparrow$ 2.4  |
|                               | CTRL     | 64.1          | 60.3 | 65.3  | 57.7  | 65.8 | 62.9       | 66.1 | <b>63.6</b> | 3.5        | $\uparrow$ 4.4  |
| Flickr<br>(Ratio = 0.50%)     | DC-Graph | 43.1          | -    | 45.7  | 43.8  | 45.9 | 45.8       | 45.6 | 45.4        | 0.9        | -               |
|                               | GCond    | 44.8          | 40.1 | 45.9  | 42.8  | 47.1 | 46.2       | 46.1 | 45.6        | 1.6        | $\uparrow$ 0.2  |
|                               | CTRL     | 42.6          | 41.0 | 46.2  | 43.1  | 47.2 | 46.5       | 46.7 | <b>45.9</b> | 1.6        | $\uparrow$ 0.5  |
| Reddit<br>(Ratio = 0.10%)     | DC-Graph | 50.3          | -    | 81.2  | 77.5  | 89.5 | 89.7       | 90.5 | 85.7        | 5.9        | -               |
|                               | GCond    | 42.5          | 60.2 | 87.8  | 75.5  | 89.4 | 89.1       | 89.6 | 86.3        | 6.1        | $\uparrow$ 0.6  |
|                               | CTRL     | 43.2          | 60.0 | 87.9  | 75.3  | 90.3 | 89.1       | 89.7 | <b>86.5</b> | 6.3        | $\uparrow$ 0.8  |

Cross-architecture generalization analysis. To further demonstrate the transferability of our method, we first utilize SGC to generate synthetic graphs, then evaluate them on a variety of architectures, including APPNP, GCN, SGC, GraphSAGE [\[118\]](#page-14-3), Cheby and GAT [\[138\]](#page-15-3), as well as a standard MLP. Furthermore, We conduct condensation with different structures on the Cora and Citeseer datasets and evaluate the cross-architecture generalization performance of the condensed graphs produced by different structures. Tab. [5](#page-8-0) and [6](#page-8-1) document the results of those experiments, from which we can draw the following conclusions:

Obs 7. As shown in Tab. [5,](#page-8-0) compared to GCond, our synthetic graphs generally exhibit better cross-architecture performance(up to  $2\%$ ), demonstrating that the finer-grained gradient matching does not lead the condensed graph to overfit to a single neural network architecture.

Remark. To make a fair comparison, we incorporated our refined matching criterion into SFGC and conducted a parallel comparison. The empirical results confirm that this improvement can further enhance the cross-architecture performance. Details are included in the Appendix [11.](#page-22-0)

Obs 8. The results in Tab. [6](#page-8-1) show that our approach achieves improved performance over GCond on the majority of model-dataset combinations, demonstrating that CTRL is versatile and achieves consistent gains across diverse architectures and datasets.

# 4 Conclusion

In this paper, we introduce a novel approach for graph condensation, which effectively reduces the impact of accumulated errors arising from the disparities between gradients produced by synthetic and original data through finer-grained gradient matching and a more rational initialization process.

<span id="page-8-1"></span>Table 6: Comparison of the cross-architecture generalization performance between GCond (on the left of  $\ell$ ) and CTRL (on the right of  $\ell$ ) on Cora(a) and Ogbn-arxiv(b). **Bold** entries are the best results. ↑/↓ : our method show increase or decrease performance.

| (a) Cora, Ratio = $2.60\%$ |  |  |
|----------------------------|--|--|
|----------------------------|--|--|

(b) Ogbn-arxiv, Ratio =  $0.05\%$ 

| C/T          | <b>APPNP</b> | Cheby | <b>GCN</b>                                                                                                                     | SAGE | SGC                                                                                             | C/T          | APPNP | Cheby | <b>GCN</b> | SAGE                                                                                                       | SGC |
|--------------|--------------|-------|--------------------------------------------------------------------------------------------------------------------------------|------|-------------------------------------------------------------------------------------------------|--------------|-------|-------|------------|------------------------------------------------------------------------------------------------------------|-----|
| <b>APPNP</b> |              |       | 72.1/76.1 60.8/75.1 73.5/76.7 72.3/72.7 73.1/78.0 1                                                                            |      |                                                                                                 | <b>APPNP</b> |       |       |            | $60.3/62.5\uparrow$ 51.8/57.3 $\uparrow$ 59.9/63.6 $\uparrow$ 59.0/61.3 $\uparrow$ 61.2/62.5 $\uparrow$    |     |
| Cheby        |              |       |                                                                                                                                |      | 75.3/78.5 <sup>+</sup> 71.8/75.8 <sup>+</sup> 76.8/74.1 76.4/75.2 75.5/75.6 <sup>+</sup>        | Cheby        |       |       |            | $57.4/59.2 \uparrow 53.5/55.2 \uparrow 57.4/59.0 \uparrow 57.1/55.2 \downarrow 58.2/57.7 \downarrow$       |     |
| <b>GCN</b>   |              |       | 69.8/72.5 <sup><math>\uparrow</math></sup> 53.2/62.4 $\uparrow$ 70.6/72.3 $\uparrow$ 60.2/60.6 $\uparrow$ 68.7/73.1 $\uparrow$ |      |                                                                                                 | GCN          |       |       |            | 59.3/61.4 51.8/55.8 60.3/61.1 60.2/60.3 59.2/61.9                                                          |     |
| SAGE         |              |       |                                                                                                                                |      | 77.1/77.2 69.3/75.9 77.0/79.3 76.1/75.7 77.7/79.1 1                                             | SAGE         |       |       |            | 57.6/60.7 <sup><math>\uparrow</math></sup> 53.9/53.31 58.1/62.9 $\uparrow$ 57.8/55.51 59.0/61.7 $\uparrow$ |     |
| SGC          |              |       |                                                                                                                                |      | 78.5/80.9 $\uparrow$ 76.0/77.5 $\uparrow$ 80.1/80.7 $\uparrow$ 78.2/74.8 $\downarrow$ 79.3/76.5 | SGC          |       |       |            | 57.6/60.61 53.9/55.91 58.1/61.81 57.8/58.91 59.0/61.61                                                     |     |

Extensive experimental results and analysis confirm the superiority of the proposed method in synthesizing high-quality small-scale graphs, including node classification, graph classification, crossarchitecture tasks, and neural architecture search tasks. Furthermore, considering the universality of the CTRL concept, it can be effectively integrated into various data condensation methods.

Limitations and future work. CTRL is limited to gradient matching framework, we will explore its applications in more matching approaches (metrics) in the future.

## References

- <span id="page-9-12"></span>[1] P. D. Hoff, A. E. Raftery, and M. S. Handcock, "Latent space approaches to social network analysis," *Journal of the american Statistical association*, vol. 97, no. 460, pp. 1090–1098, 2002.
- [2] Z. A. Sahili and M. Awad, "Spatio-temporal graph neural networks: A survey," 2023.
- <span id="page-9-15"></span>[3] M. Hashemi, S. Gong, J. Ni, W. Fan, B. A. Prakash, and W. Jin, "A comprehensive survey on graph reduction: Sparsification, coarsening, and condensation," *arXiv preprint*, 2024.
- <span id="page-9-2"></span>[4] S. Liu, K. Wang, X. Yang, J. Ye, and X. Wang, "Dataset distillation via factorization," 2022.
- <span id="page-9-1"></span>[5] Y. Zhang, T. Zhang, K. Wang, Z. Guo, Y. Liang, X. Bresson, W. Jin, and Y. You, "Navigating complexity: Toward lossless graph condensation via expanding window matching," in *Fortyfirst International Conference on Machine Learning*.
- <span id="page-9-5"></span>[6] Y. Lu, X. Chen, Y. Zhang, J. Gu, T. Zhang, Y. Zhang, X. Yang, Q. Xuan, K. Wang, and Y. You, "Can pre-trained models assist in dataset distillation?" *arXiv preprint [arXiv:2310.03295](http://arxiv.org/abs/2310.03295)*, 2023.
- <span id="page-9-4"></span>[7] K. Wang, J. Gu, D. Zhou, Z. Zhu, W. Jiang, and Y. You, "Dim: Distilling dataset into generative model," 2023.
- <span id="page-9-3"></span>[8] D. Zhou, K. Wang, J. Gu, X. Peng, D. Lian, Y. Zhang, Y. You, and J. Feng, "Dataset quantization," 2023.
- <span id="page-9-14"></span>[9] Z. Qin, K. Wang, Z. Zheng, J. Gu, X. Peng, Z. Xu, D. Zhou, L. Shang, B. Sun, X. Xie, and Y. You, "Infobatch: Lossless training speed up by unbiased dynamic data pruning," 2023.
- [10] Y. Li and W. Li, "Data distillation for text classification," 2021.
- <span id="page-9-13"></span>[11] K. Wang, Y. Liang, X. Li, G. Li, B. Ghanem, R. Zimmermann, H. Yi, Y. Zhang, Y. Wang *et al.*, "Brave the wind and the waves: Discovering robust and generalizable graph lottery tickets," *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2023.
- <span id="page-9-0"></span>[12] K. Wang, Y. Liang, P. Wang, X. Wang, P. Gu, J. Fang, and Y. Wang, "Searching lottery tickets in graph neural networks: A dual perspective," in *The Eleventh International Conference on Learning Representations*, 2022.
- <span id="page-9-11"></span>[13] A. Loukas, A. Simonetto, and G. Leus, "Distributed autoregressive moving average graph filters," *IEEE Signal Processing Letters*, vol. 22, no. 11, pp. 1931–1935, nov 2015. [Online]. Available:<https://doi.org/10.1109%2Flsp.2015.2448655>
- <span id="page-9-8"></span>[14] D. I. Shuman, S. K. Narang, P. Frossard, A. Ortega, and P. Vandergheynst, "The emerging field of signal processing on graphs: Extending high-dimensional data analysis to networks and other irregular domains," *IEEE Signal Processing Magazine*, vol. 30, no. 3, pp. 83–98, may 2013. [Online]. Available:<https://doi.org/10.1109%2Fmsp.2012.2235192>
- [15] G. Leus, A. G. Marques, J. M. Moura, A. Ortega, and D. I. Shuman, "Graph signal processing: History, development, impact, and outlook," *IEEE Signal Processing Magazine*, vol. 40, no. 4, pp. 49–60, jun 2023. [Online]. Available:<https://doi.org/10.1109%2Fmsp.2023.3262906>
- <span id="page-9-6"></span>[16] A. Ortega, P. Frossard, J. Kovačević, J. M. F. Moura, and P. Vandergheynst, "Graph signal" processing: Overview, challenges and applications," 2018.
- <span id="page-9-9"></span>[17] A. Sandryhaila and J. M. F. Moura, "Discrete signal processing on graphs: Frequency analysis," 2013.
- <span id="page-9-10"></span>[18] D. K. Hammond, P. Vandergheynst, and R. Gribonval, "Wavelets on graphs via spectral graph theory," 2009.
- <span id="page-9-7"></span>[19] B. Nica, *A Brief Introduction to Spectral Graph Theory*. EMS Press, may 2018. [Online]. Available:<https://doi.org/10.4171%2F188>

- <span id="page-10-2"></span>[20] I. Sucholutsky and M. Schonlau, "Soft-label dataset distillation and text dataset distillation," 2020.
- [21] H. Zhang, S. Lin, W. Liu, P. Zhou, J. Tang, X. Liang, and E. P. Xing, "Iterative graph self-distillation," 2023.
- <span id="page-10-0"></span>[22] J. Du, Y. Jiang, V. Y. Tan, J. T. Zhou, and H. Li, "Minimizing the accumulated trajectory error to improve dataset distillation," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2023, pp. 3749–3758.
- <span id="page-10-1"></span>[23] Z. Jiang, J. Gu, M. Liu, and D. Z. Pan, "Delving into effective gradient matching for dataset condensation," 2022.
- [24] X. Wu, Z. Deng, and O. Russakovsky, "Multimodal dataset distillation for image-text retrieval," 2023.
- [25] D. Ramírez, A. G. Marques, and S. Segarra, "Graph-signal reconstruction and blind deconvolution for structured inputs," *Signal Processing*, vol. 188, p. 108180, nov 2021. [Online]. Available:<https://doi.org/10.1016%2Fj.sigpro.2021.108180>
- [26] T. Dong, B. Zhao, and L. Lyu, "Privacy for free: How does dataset condensation help privacy?" in *International Conference on Machine Learning*. PMLR, 2022, pp. 5378–5396.
- [27] Y. Xiong, R. Wang, M. Cheng, F. Yu, and C.-J. Hsieh, "Feddm: Iterative distribution matching for communication-efficient federated learning," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2023, pp. 16 323–16 332.
- [28] X. Xu, L. Lyu, Y. Dong, Y. Lu, W. Wang, and H. Jin, "Splitgnn: Splitting gnn for node classification with heterogeneous attention," 2023.
- [29] P. Veličković, "Everything is connected: Graph neural networks," Current Opinion *in Structural Biology*, vol. 79, p. 102538, apr 2023. [Online]. Available: [https:](https://doi.org/10.1016%2Fj.sbi.2023.102538) [//doi.org/10.1016%2Fj.sbi.2023.102538](https://doi.org/10.1016%2Fj.sbi.2023.102538)
- [30] S. P. Borgatti and M. G. Everett, "Notions of position in social network analysis," *Sociological methodology*, pp. 1–35, 1992.
- <span id="page-10-3"></span>[31] B. Perozzi, R. Al-Rfou, and S. Skiena, "Deepwalk: Online learning of social representations," in *Proceedings of the 20th ACM SIGKDD international conference on Knowledge discovery and data mining*, 2014, pp. 701–710.
- [32] H. Ebel and S. Bornholdt, "Coevolutionary games on networks," *Physical Review E*, vol. 66, no. 5, p. 056118, 2002.
- <span id="page-10-4"></span>[33] D. Bear, C. Fan, D. Mrowca, Y. Li, S. Alter, A. Nayebi, J. Schwartz, L. F. Fei-Fei, J. Wu, J. Tenenbaum *et al.*, "Learning physical graph representations from visual scenes," *Advances in Neural Information Processing Systems*, vol. 33, pp. 6027–6039, 2020.
- <span id="page-10-5"></span>[34] A. Raffo, U. Fugacci, S. Biasotti, W. Rocchia, Y. Liu, E. Otu, R. Zwiggelaar, D. Hunter, E. I. Zacharaki, E. Psatha *et al.*, "Shrec 2021: Retrieval and classification of protein surfaces equipped with physical and chemical properties," *Computers & Graphics*, vol. 99, pp. 1–21, 2021.
- <span id="page-10-6"></span>[35] B. D. McKay, M. A. Yirik, and C. Steinbeck, "Surge: a fast open-source chemical graph generator," *Journal of Cheminformatics*, vol. 14, no. 1, p. 24, 2022.
- [36] É. Daller, S. Bougleux, L. Brun, and O. Lézoray, "Local patterns and supergraph for chemical graph classification with convolutional networks," in *Joint IAPR International Workshops on Statistical Techniques in Pattern Recognition (SPR) and Structural and Syntactic Pattern Recognition (SSPR)*. Springer, 2018, pp. 97–106.
- <span id="page-10-7"></span>[37] P. W. Battaglia, J. B. Hamrick, V. Bapst, A. Sanchez-Gonzalez, V. Zambaldi, M. Malinowski, A. Tacchetti, D. Raposo, A. Santoro, R. Faulkner *et al.*, "Relational inductive biases, deep learning, and graph networks," *arXiv preprint [arXiv:1806.01261](http://arxiv.org/abs/1806.01261)*, 2018.
- <span id="page-10-8"></span>[38] Z. Wu, S. Pan, F. Chen, G. Long, C. Zhang, and S. Y. Philip, "A comprehensive survey on graph neural networks," *IEEE transactions on neural networks and learning systems*, vol. 32, no. 1, pp. 4–24, 2020.
- <span id="page-10-9"></span>[39] J. Zhou, G. Cui, S. Hu, Z. Zhang, C. Yang, Z. Liu, L. Wang, C. Li, and M. Sun, "Graph neural networks: A review of methods and applications," *AI open*, vol. 1, pp. 57–81, 2020.

- [40] N. Wale, X. Ning, and G. Karypis, "Trends in chemical graph data mining," *Managing and mining graph data*, pp. 581–606, 2010.
- <span id="page-11-9"></span>[41] F. He, A. Hanai, H. Nagamochi, and T. Akutsu, "Enumerating naphthalene isomers of treelike chemical graphs," in *International Conference on Bioinformatics Models, Methods and Algorithms*, vol. 4. SCITEPRESS, 2016, pp. 258–265.
- <span id="page-11-11"></span>[42] A. Hogan, E. Blomqvist, M. Cochez, C. d'Amato, G. D. Melo, C. Gutierrez, S. Kirrane, J. E. L. Gayo, R. Navigli, S. Neumaier *et al.*, "Knowledge graphs," *ACM Computing Surveys (Csur)*, vol. 54, no. 4, pp. 1–37, 2021.
- <span id="page-11-12"></span>[43] S. Ji, S. Pan, E. Cambria, P. Marttinen, and S. Y. Philip, "A survey on knowledge graphs: Representation, acquisition, and applications," *IEEE transactions on neural networks and learning systems*, vol. 33, no. 2, pp. 494–514, 2021.
- [44] M. Nickel, K. Murphy, V. Tresp, and E. Gabrilovich, "A review of relational machine learning for knowledge graphs," *Proceedings of the IEEE*, vol. 104, no. 1, pp. 11–33, 2015.
- <span id="page-11-10"></span>[45] N. Noy, Y. Gao, A. Jain, A. Narayanan, A. Patterson, and J. Taylor, "Industry-scale knowledge graphs: Lessons and challenges: Five diverse technology companies show how it's done," *Queue*, vol. 17, no. 2, pp. 48–75, 2019.
- [46] L. D. Smith, L. A. Best, D. A. Stubbs, A. B. Archibald, and R. Roberson-Nay, "Constructing knowledge: The role of graphs and tables in hard and soft psychology." *American Psychologist*, vol. 57, no. 10, p. 749, 2002.
- [47] K. Xu, W. Hu, J. Leskovec, and S. Jegelka, "How powerful are graph neural networks?" *arXiv preprint [arXiv:1810.00826](http://arxiv.org/abs/1810.00826)*, 2018.
- [48] F. Scarselli, M. Gori, A. C. Tsoi, M. Hagenbuchner, and G. Monfardini, "The graph neural network model," *IEEE transactions on neural networks*, vol. 20, no. 1, pp. 61–80, 2008.
- <span id="page-11-8"></span>[49] J. You, R. Ying, and J. Leskovec, "Position-aware graph neural networks," in *International conference on machine learning*. PMLR, 2019, pp. 7134–7143.
- [50] J. Shlomi, P. Battaglia, and J.-R. Vlimant, "Graph neural networks in particle physics," *Machine Learning: Science and Technology*, vol. 2, no. 2, p. 021001, 2020.
- [51] L. Wu, P. Cui, J. Pei, L. Zhao, and X. Guo, "Graph neural networks: foundation, frontiers and applications," in *Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, 2022, pp. 4840–4841.
- <span id="page-11-0"></span>[52] B. Li and D. Pi, "Learning deep neural networks for node classification," *Expert Systems with Applications*, vol. 137, pp. 324–334, 2019.
- <span id="page-11-1"></span>[53] S. Xiao, S. Wang, Y. Dai, and W. Guo, "Graph neural networks in node classification: survey and evaluation," *Machine Vision and Applications*, vol. 33, pp. 1–19, 2022.
- <span id="page-11-2"></span>[54] J. Chen, X. Wang, and X. Xu, "Gc-lstm: Graph convolution embedded lstm for dynamic network link prediction," *Applied Intelligence*, pp. 1–16, 2022.
- <span id="page-11-3"></span>[55] A. Rossi, D. Firmani, P. Merialdo, and T. Teofili, "Explaining link prediction systems based on knowledge graph embeddings," in *Proceedings of the 2022 international conference on management of data*, 2022, pp. 2062–2075.
- <span id="page-11-4"></span>[56] D. Van Assche, T. Delva, G. Haesendonck, P. Heyvaert, B. De Meester, and A. Dimou, "Declarative rdf graph generation from heterogeneous (semi-) structured data: A systematic literature review," *Journal of Web Semantics*, p. 100753, 2022.
- <span id="page-11-5"></span>[57] C. Vignac, I. Krawczuk, A. Siraudin, B. Wang, V. Cevher, and P. Frossard, "Digress: Discrete denoising diffusion for graph generation," *arXiv preprint [arXiv:2209.14734](http://arxiv.org/abs/2209.14734)*, 2022.
- <span id="page-11-7"></span>[58] S. Polisetty, J. Liu, K. Falus, Y. R. Fung, S.-H. Lim, H. Guan, and M. Serafini, "Gsplit: Scaling graph neural network training on large graphs via split-parallelism," *arXiv preprint [arXiv:2303.13775](http://arxiv.org/abs/2303.13775)*, 2023.
- <span id="page-11-6"></span>[59] Y. Wang, K. Zhou, R. Miao, N. Liu, and X. Wang, "Adagcl: Adaptive subgraph contrastive learning to generalize large-scale graph training," in *Proceedings of the 31st ACM International Conference on Information & Knowledge Management*, 2022, pp. 2046–2055.
- <span id="page-11-13"></span>[60] H. Yu, L. Wang, B. Wang, M. Liu, T. Yang, and S. Ji, "Graphfm: Improving large-scale gnn training via feature momentum," in *International Conference on Machine Learning*. PMLR, 2022, pp. 25 684–25 701.

- [61] K. Duan, Z. Liu, P. Wang, W. Zheng, K. Zhou, T. Chen, X. Hu, and Z. Wang, "A comprehensive study on large-scale graph training: Benchmarking and rethinking," *Advances in Neural Information Processing Systems*, vol. 35, pp. 5376–5389, 2022.
- <span id="page-12-0"></span>[62] J. Li, T. Zhang, H. Tian, S. Jin, M. Fardad, and R. Zafarani, "Graph sparsification with graph convolutional networks," *International Journal of Data Science and Analytics*, pp. 1–14, 2022.
- <span id="page-12-1"></span>[63] S. Yu, F. Alesiani, W. Yin, R. Jenssen, and J. C. Principe, "Principle of relevant information for graph sparsification," in *Uncertainty in Artificial Intelligence*. PMLR, 2022, pp. 2331–2341.
- [64] Y. Chen, S. Khanna, and H. Li, "On weighted graph sparsification by linear sketching," in *2022 IEEE 63rd Annual Symposium on Foundations of Computer Science (FOCS)*. IEEE, 2022, pp. 474–485.
- <span id="page-12-2"></span>[65] J. Chen, Y. Saad, and Z. Zhang, "Graph coarsening: from scientific computing to machine learning," *SeMA Journal*, pp. 1–37, 2022.
- <span id="page-12-9"></span>[66] B. Kelley and S. Rajamanickam, "Parallel, portable algorithms for distance-2 maximal independent set and graph coarsening," in *2022 IEEE International Parallel and Distributed Processing Symposium (IPDPS)*. IEEE, 2022, pp. 280–290.
- <span id="page-12-3"></span>[67] F. Kammer and J. Meintrup, "Space-efficient graph coarsening with applications to succinct planar encodings," *arXiv preprint [arXiv:2205.06128](http://arxiv.org/abs/2205.06128)*, 2022.
- <span id="page-12-5"></span>[68] W. Jin, L. Zhao, S. Zhang, Y. Liu, J. Tang, and N. Shah, "Graph condensation for graph neural networks," 2022.
- <span id="page-12-4"></span>[69] C. Cai, D. Wang, and Y. Wang, "Graph coarsening with neural networks," *arXiv preprint [arXiv:2102.01350](http://arxiv.org/abs/2102.01350)*, 2021.
- <span id="page-12-8"></span>[70] G. Li, A. Zhang, Q. Zhang, D. Wu, and C. Zhan, "Pearson correlation coefficient-based performance enhancement of broad learning system for stock price prediction," *IEEE Transactions on Circuits and Systems II: Express Briefs*, vol. 69, no. 5, pp. 2413–2417, 2022.
- <span id="page-12-6"></span>[71] W. Jin, X. Tang, H. Jiang, Z. Li, D. Zhang, J. Tang, and B. Yin, "Condensing graphs via onestep gradient matching," in *Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, 2022, pp. 720–730.
- <span id="page-12-7"></span>[72] X. Zheng, M. Zhang, C. Chen, Q. V. H. Nguyen, X. Zhu, and S. Pan, "Structure-free graph condensation: From large-scale graphs to condensed graph-free data," *arXiv preprint [arXiv:2306.02664](http://arxiv.org/abs/2306.02664)*, 2023.
- [73] Z. Jiang, J. Gu, M. Liu, and D. Z. Pan, "Delving into effective gradient matching for dataset condensation," in *2023 IEEE International Conference on Omni-layer Intelligent Systems (COINS)*. IEEE, 2023, pp. 1–6.
- [74] S.-I. Amari, "Natural gradient works efficiently in learning," *Neural computation*, vol. 10, no. 2, pp. 251–276, 1998.
- [75] R. Pascanu and Y. Bengio, "Revisiting natural gradient for deep networks," *arXiv preprint [arXiv:1301.3584](http://arxiv.org/abs/1301.3584)*, 2013.
- [76] J. Martens, "New insights and perspectives on the natural gradient method," *The Journal of Machine Learning Research*, vol. 21, no. 1, pp. 5776–5851, 2020.
- [77] P. Masset, J. Zavatone-Veth, J. P. Connor, V. Murthy, and C. Pehlevan, "Natural gradient enables fast sampling in spiking neural networks," *Advances in Neural Information Processing Systems*, vol. 35, pp. 22 018–22 034, 2022.
- [78] D. E. Rumelhart, G. E. Hinton, and R. J. Williams, "Learning representations by backpropagating errors," *nature*, vol. 323, no. 6088, pp. 533–536, 1986.
- [79] S. Ruder, "An overview of gradient descent optimization algorithms," *arXiv preprint [arXiv:1609.04747](http://arxiv.org/abs/1609.04747)*, 2016.
- [80] E. H. Fukuda and L. G. Drummond, "On the convergence of the projected gradient method for vector optimization," *Optimization*, vol. 60, no. 8-9, pp. 1009–1021, 2011.
- [81] C. Xu and J. L. Prince, "Snakes, shapes, and gradient vector flow," *IEEE Transactions on image processing*, vol. 7, no. 3, pp. 359–369, 1998.
- [82] F. Cantrijn, M. de León, and E. Lacomba, "Gradient vector fields on cosymplectic manifolds," *Journal of Physics A: Mathematical and General*, vol. 25, no. 1, p. 175, 1992.

- [83] D. E. Rumelhart, G. E. Hinton, R. J. Williams *et al.*, "Learning internal representations by error propagation," 1985.
- [84] I. Sutskever, J. Martens, G. Dahl, and G. Hinton, "On the importance of initialization and momentum in deep learning," in *International conference on machine learning*. PMLR, 2013, pp. 1139–1147.
- [85] X. S. Huang, F. Perez, J. Ba, and M. Volkovs, "Improving transformer optimization through better initialization," in *International Conference on Machine Learning*. PMLR, 2020, pp. 4475–4483.
- [86] J. He, M. Lan, C.-L. Tan, S.-Y. Sung, and H.-B. Low, "Initialization of cluster refinement algorithms: A review and comparative study," in *2004 IEEE international joint conference on neural networks (IEEE Cat. No. 04CH37541)*, vol. 1. IEEE, 2004, pp. 297–302.
- [87] E. W. Forgy, "Cluster analysis of multivariate data: efficiency versus interpretability of classifications," *biometrics*, vol. 21, pp. 768–769, 1965.
- <span id="page-13-2"></span>[88] J. A. Hartigan and M. A. Wong, "Algorithm as 136: A k-means clustering algorithm," *Journal of the royal statistical society. series c (applied statistics)*, vol. 28, no. 1, pp. 100–108, 1979.
- <span id="page-13-3"></span>[89] D. Arthur and S. Vassilvitskii, "K-means++ the advantages of careful seeding," in *Proceedings of the eighteenth annual ACM-SIAM symposium on Discrete algorithms*, 2007, pp. 1027–1035.
- <span id="page-13-1"></span>[90] Y. Liu, J. Gu, K. Wang, Z. Zhu, W. Jiang, and Y. You, "Dream: Efficient dataset distillation by representative matching," *arXiv preprint [arXiv:2302.14416](http://arxiv.org/abs/2302.14416)*, 2023.
- [91] X. Zheng, M. Zhang, C. Chen, Q. Zhang, C. Zhou, and S. Pan, "Auto-heg: Automated graph neural network on heterophilic graphs," *arXiv preprint [arXiv:2302.12357](http://arxiv.org/abs/2302.12357)*, 2023.
- [92] A. Rosasco, A. Carta, A. Cossu, V. Lomonaco, and D. Bacciu, "Distilled replay: Overcoming forgetting through synthetic samples," in *International Workshop on Continual Semi-Supervised Learning*. Springer, 2021, pp. 104–117.
- [93] Y. Gao, H. Yang, P. Zhang, C. Zhou, and Y. Hu, "Graph neural architecture search," in *International joint conference on artificial intelligence*. International Joint Conference on Artificial Intelligence, 2021.
- [94] Y. Liu, R. Qiu, and Z. Huang, "Cat: Balanced continual graph learning with graph condensation," vol. abs/2309.09455, 2023.
- <span id="page-13-4"></span>[95] T. N. Kipf and M. Welling, "Semi-supervised classification with graph convolutional networks," *arXiv preprint [arXiv:1609.02907](http://arxiv.org/abs/1609.02907)*, 2016.
- <span id="page-13-5"></span>[96] T. Wang, J.-Y. Zhu, A. Torralba, and A. A. Efros, "Dataset distillation," *arXiv preprint [arXiv:1811.10959](http://arxiv.org/abs/1811.10959)*, 2018.
- <span id="page-13-6"></span>[97] O. Bohdal, Y. Yang, and T. Hospedales, "Flexible dataset distillation: Learn labels instead of images," *arXiv preprint [arXiv:2006.08572](http://arxiv.org/abs/2006.08572)*, 2020.
- <span id="page-13-7"></span>[98] T. Nguyen, Z. Chen, and J. Lee, "Dataset meta-learning from kernel ridge-regression," *arXiv preprint [arXiv:2011.00050](http://arxiv.org/abs/2011.00050)*, 2020.
- <span id="page-13-0"></span>[99] B. Zhao, K. R. Mopuri, and H. Bilen, "Dataset condensation with gradient matching," *arXiv preprint [arXiv:2006.05929](http://arxiv.org/abs/2006.05929)*, 2020.
- <span id="page-13-8"></span>[100] B. Zhao and H. Bilen, "Dataset condensation with differentiable siamese augmentation," in *International Conference on Machine Learning*. PMLR, 2021, pp. 12 674–12 685.
- <span id="page-13-9"></span>[101] Y. Chen, M. Welling, and A. Smola, "Super-samples from kernel herding," *arXiv preprint [arXiv:1203.3472](http://arxiv.org/abs/1203.3472)*, 2012.
- <span id="page-13-10"></span>[102] T. Campbell and T. Broderick, "Automated scalable bayesian inference via hilbert coresets," *The Journal of Machine Learning Research*, vol. 20, no. 1, pp. 551–588, 2019.
- <span id="page-13-11"></span>[103] C. Cheng, Y. Chen, J. Y. Lee, and Q. Sun, "Graph fourier transforms on directed product graphs," *arXiv preprint [arXiv:2209.01336](http://arxiv.org/abs/2209.01336)*, 2022.
- [104] H. Nt and T. Maehara, "Revisiting graph neural networks: All we have is low-pass filters," *arXiv preprint [arXiv:1905.09550](http://arxiv.org/abs/1905.09550)*, 2019.
- [105] Z. Chen, F. Chen, L. Zhang, T. Ji, K. Fu, L. Zhao, F. Chen, L. Wu, C. Aggarwal, and C.-T. Lu, "Bridging the gap between spatial and spectral domains: A survey on graph neural networks," *arXiv preprint [arXiv:2002.11867](http://arxiv.org/abs/2002.11867)*, 2020.

- <span id="page-14-13"></span>[106] L. Wu, H. Lin, Y. Huang, T. Fan, and S. Z. Li, "Extracting low-/high-frequency knowledge from graph neural networks and injecting it into mlps: An effective gnn-to-mlp distillation framework," *arXiv preprint [arXiv:2305.10758](http://arxiv.org/abs/2305.10758)*, 2023.
- <span id="page-14-12"></span>[107] D. Bo, X. Wang, C. Shi, and H. Shen, "Beyond low-frequency information in graph convolutional networks," in *Proceedings of the AAAI Conference on Artificial Intelligence*, vol. 35, 2021, pp. 3950–3957.
- [108] Y. Wang, Z. Hu, Y. Ye, and Y. Sun, "Demystifying graph neural network via graph filter assessment," 2019. [Online]. Available:<https://api.semanticscholar.org/CorpusID:213408668>
- [109] M. Liu, S. Li, X. Chen, and L. Song, "Graph condensation via receptive field distribution matching," *arXiv preprint [arXiv:2206.13697](http://arxiv.org/abs/2206.13697)*, 2022.
- <span id="page-14-7"></span>[110] M. Welling, "Herding dynamical weights to learn," in *Proceedings of the 26th Annual International Conference on Machine Learning*, 2009, pp. 1121–1128.
- <span id="page-14-8"></span>[111] O. Sener and S. Savarese, "Active learning for convolutional neural networks: A core-set approach," *arXiv preprint [arXiv:1708.00489](http://arxiv.org/abs/1708.00489)*, 2017.
- <span id="page-14-6"></span>[112] Z. Huang, S. Zhang, C. Xi, T. Liu, and M. Zhou, "Scaling up graph neural networks via graph coarsening," in *Proceedings of the 27th ACM SIGKDD conference on knowledge discovery & data mining*, 2021, pp. 675–684.
- <span id="page-14-1"></span>[113] W. Hu, M. Fey, M. Zitnik, Y. Dong, H. Ren, B. Liu, M. Catasta, and J. Leskovec, "Open graph benchmark: Datasets for machine learning on graphs," *Advances in neural information processing systems*, vol. 33, pp. 22 118–22 133, 2020.
- <span id="page-14-9"></span>[114] J. Tang, J. Li, Z. Gao, and J. Li, "Rethinking graph neural networks for anomaly detection," in *Proceedings of the 39th International Conference on Machine Learning*, ser. Proceedings of Machine Learning Research, K. Chaudhuri, S. Jegelka, L. Song, C. Szepesvari, G. Niu, and S. Sabato, Eds., vol. 162. PMLR, 17–23 Jul 2022, pp. 21 076–21 089. [Online]. Available: <https://proceedings.mlr.press/v162/tang22b.html>
- [115] T. Luo, Z. Mo, and S. J. Pan, "Fast graph generation via spectral diffusion," 2022.
- [116] K. Martinkus, A. Loukas, N. Perraudin, and R. Wattenhofer, "Spectre: Spectral conditioning helps to overcome the expressivity limits of one-shot graph generators," 2022.
- <span id="page-14-2"></span>[117] E. Chien, W.-C. Chang, C.-J. Hsieh, H.-F. Yu, J. Zhang, O. Milenkovic, and I. S. Dhillon, "Node feature extraction by self-supervised multi-scale neighborhood prediction," *arXiv preprint [arXiv:2111.00064](http://arxiv.org/abs/2111.00064)*, 2021.
- <span id="page-14-3"></span>[118] W. Hamilton, Z. Ying, and J. Leskovec, "Inductive representation learning on large graphs," *Advances in neural information processing systems*, vol. 30, 2017.
- <span id="page-14-4"></span>[119] C. Morris, N. M. Kriege, F. Bause, K. Kersting, P. Mutzel, and M. Neumann, "Tudataset: A collection of benchmark datasets for learning with graphs," *arXiv preprint [arXiv:2007.08663](http://arxiv.org/abs/2007.08663)*, 2020.
- <span id="page-14-5"></span>[120] V. P. Dwivedi, C. K. Joshi, A. T. Luu, T. Laurent, Y. Bengio, and X. Bresson, "Benchmarking graph neural networks," *arXiv preprint [arXiv:2003.00982](http://arxiv.org/abs/2003.00982)*, 2020.
- <span id="page-14-10"></span>[121] L. Stankovic, D. Mandic, M. Dakovic, M. Brajovic, B. Scalzo, and A. G. Constantinides, "Graph signal processing–part ii: Processing and analyzing signals on graphs," *arXiv preprint [arXiv:1909.10325](http://arxiv.org/abs/1909.10325)*, 2019.
- [122] Y. Bengio and Y. LeCun, "Scaling learning algorithms towards AI," in *Large Scale Kernel Machines*. MIT Press, 2007.
- [123] G. E. Hinton, S. Osindero, and Y. W. Teh, "A fast learning algorithm for deep belief nets," *Neural Computation*, vol. 18, pp. 1527–1554, 2006.
- [124] I. Goodfellow, Y. Bengio, A. Courville, and Y. Bengio, *Deep learning*. MIT Press, 2016, vol. 1.
- <span id="page-14-11"></span>[125] J. Gasteiger, A. Bojchevski, and S. Günnemann, "Predict then propagate: Graph neural networks meet personalized pagerank," *arXiv preprint [arXiv:1810.05997](http://arxiv.org/abs/1810.05997)*, 2018.
- <span id="page-14-0"></span>[126] X. Gao, T. Chen, Y. Zang, W. Zhang, Q. V. H. Nguyen, K. Zheng, and H. Yin, "Graph condensation for inductive node representation learning," 2023.

- <span id="page-15-2"></span>[127] G. Cazenavette, T. Wang, A. Torralba, A. A. Efros, and J.-Y. Zhu, "Dataset distillation by matching training trajectories," in *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, 2022, pp. 4750–4759.
- <span id="page-15-0"></span>[128] R. Mao, W. Fan, and Q. Li, "Gcare: Mitigating subgroup unfairness in graph condensation through adversarial regularization," *Applied Sciences*, vol. 13, no. 16, 2023. [Online]. Available:<https://www.mdpi.com/2076-3417/13/16/9166>
- <span id="page-15-1"></span>[129] J. Schuetzke, N. J. Szymanski, and M. Reischl, "A universal synthetic dataset for machine learning on spectroscopic data," 2022.
- <span id="page-15-8"></span>[130] G. Zhang, K. Wang, W. Huang, Y. Yue, Y. Wang, R. Zimmermann, A. Zhou, D. Cheng, J. Zeng, and Y. Liang, "Graph lottery ticket automated," in *The Twelfth International Conference on Learning Representations*, 2024.
- <span id="page-15-7"></span>[131] G. Zhang, Y. Yue, K. Wang, J. Fang, Y. Sui, K. Wang, Y. Liang, D. Cheng, S. Pan, and T. Chen, "Two heads are better than one: Boosting graph sparse training via semantic and topological awareness," *arXiv preprint [arXiv:2402.01242](http://arxiv.org/abs/2402.01242)*, 2024.
- <span id="page-15-9"></span>[132] G. Zhang, X. Sun, Y. Yue, K. Wang, T. Chen, and S. Pan, "Graph sparsification via mixture of graphs," *arXiv preprint [arXiv:2405.14260](http://arxiv.org/abs/2405.14260)*, 2024.
- <span id="page-15-10"></span>[133] F. Wu, A. Souza, T. Zhang, C. Fifty, T. Yu, and K. Weinberger, "Simplifying graph convolutional networks," in *International conference on machine learning*. PMLR, 2019, pp. 6861–6871.
- <span id="page-15-14"></span>[134] M. Defferrard, X. Bresson, and P. Vandergheynst, "Convolutional neural networks on graphs with fast localized spectral filtering," *Advances in neural information processing systems*, vol. 29, 2016.
- <span id="page-15-15"></span>[135] A. Sandryhaila and J. M. F. Moura, "Discrete signal processing on graphs," *IEEE Transactions on Signal Processing*, vol. 61, no. 7, pp. 1644–1656, apr 2013. [Online]. Available: <https://doi.org/10.1109%2Ftsp.2013.2238935>
- <span id="page-15-12"></span>[136] G. Stamatelatos and P. S. Efraimidis, "An exact, linear time barabási-albert algorithm," 2022.
- <span id="page-15-11"></span>[137] L. Erdős, A. Knowles, H.-T. Yau, and J. Yin, "Spectral statistics of erdős–rényi graphs i: Local semicircle law," *The Annals of Probability*, vol. 41, no. 3B, may 2013. [Online]. Available: <https://doi.org/10.1214%2F11-aop734>
- <span id="page-15-3"></span>[138] P. Veličković, G. Cucurull, A. Casanova, A. Romero, P. Lio, and Y. Bengio, "Graph attention networks," *arXiv preprint [arXiv:1710.10903](http://arxiv.org/abs/1710.10903)*, 2017.
- <span id="page-15-13"></span>[139] H. F. Song and X.-J. Wang, "Simple, distance-dependent formulation of the watts-strogatz model for directed and undirected small-world networks," *Physical Review E*, vol. 90, no. 6, dec 2014. [Online]. Available:<https://doi.org/10.1103%2Fphysreve.90.062801>
- [140] Y. Ma, Z. Guo, Z. Ren, E. Zhao, J. Tang, and D. Yin, "Streaming graph neural networks," 2018.
- [141] W. Jin, X. Tang, H. Jiang, Z. Li, D. Zhang, J. Tang, and B. Yin, "Condensing graphs via one-step gradient matching," in *Proceedings of the 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*. ACM, aug 2022. [Online]. Available: <https://doi.org/10.1145%2F3534678.3539429>
- [142] W. L. Hamilton, R. Ying, and J. Leskovec, "Inductive representation learning on large graphs," 2018.
- <span id="page-15-4"></span>[143] G. Wan, Y. Tian, W. Huang, N. V. Chawla, and M. Ye, "S3gcl: Spectral, swift, spatial graph contrastive learning," in *Forty-first International Conference on Machine Learning*, 2024.
- <span id="page-15-5"></span>[144] W. Huang, G. Wan, M. Ye, and B. Du, "Federated graph semantic and structural learning," in *Proceedings of the Thirty-Second International Joint Conference on Artificial Intelligence*, 2023, pp. 3830–3838.
- <span id="page-15-6"></span>[145] Z. Liu, G. Wan, B. A. Prakash, M. S. Lau, and W. Jin, "A review of graph neural networks in epidemic modeling," in *Proceedings of the 30th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, 2024, pp. 6577–6587.
- [146] M. F. Balın and Ümit V. Çatalyürek, "Layer-neighbor sampling defusing neighborhood explosion in gnns," 2023.

- <span id="page-16-2"></span>[147] G. Wan, Z. Liu, M. S. Lau, B. A. Prakash, and W. Jin, "Epidemiology-aware neural ode with continuous disease transmission graph," *arXiv preprint arXiv*, 2024.
- <span id="page-16-1"></span>[148] K. Wang, G. Zhang, X. Zhang, J. Fang, X. Wu, G. Li, S. Pan, W. Huang, and Y. Liang, "The heterophilic snowflake hypothesis: Training and empowering gnns for heterophilic graphs," in *Proceedings of the 30th ACM SIGKDD Conference on Knowledge Discovery and Data Mining*, 2024, pp. 3164–3175.
- [149] M. Chen, Z. Wei, B. Ding, Y. Li, Y. Yuan, X. Du, and J.-R. Wen, "Scalable graph neural networks via bidirectional propagation," in *Advances in Neural Information Processing Systems*, H. Larochelle, M. Ranzato, R. Hadsell, M. Balcan, and H. Lin, Eds., vol. 33. Curran Associates, Inc., 2020, pp. 14 556–14 566. [Online]. Available: [https://proceedings.neurips.cc/](https://proceedings.neurips.cc/paper_files/paper/2020/file/a7789ef88d599b8df86bbee632b2994d-Paper.pdf) [paper\\_files/paper/2020/file/a7789ef88d599b8df86bbee632b2994d-Paper.pdf](https://proceedings.neurips.cc/paper_files/paper/2020/file/a7789ef88d599b8df86bbee632b2994d-Paper.pdf)
- <span id="page-16-0"></span>[150] H. Zeng, H. Zhou, A. Srivastava, R. Kannan, and V. Prasanna, "Graphsaint: Graph sampling based inductive learning method," 2020.
- [151] G. Li, M. Müller, B. Ghanem, and V. Koltun, "Training graph neural networks with 1000 layers," 2022.
- <span id="page-16-3"></span>[152] M. Fey and J. E. Lenssen, "Fast graph representation learning with pytorch geometric," *arXiv preprint [arXiv:1903.02428](http://arxiv.org/abs/1903.02428)*, 2019.
- [153] P. Csikvári, "Note on the sum of the smallest and largest eigenvalues of a triangle-free graph," 2022.
- [154] H. Yu, "On the limit points of the smallest eigenvalues of regular graphs," 2011.
- [155] Z. Lin, J. Wang, and M. Cai, "The laplacian spectral ratio of connected graphs," 2023.
- <span id="page-16-5"></span>[156] G. Ao, R. Liu, and J. Yuan, "Spectral radius and spanning trees of graphs," 2022.
- <span id="page-16-4"></span>[157] J. Gallier, "Spectral theory of unsigned and signed graphs. applications to graph clustering: a survey," 2016.
- <span id="page-16-6"></span>[158] J. Gao and X. Hou, "The spectral radius of graphs without long cycles," 2017.
- <span id="page-16-7"></span>[159] D. Fan, H. Lin, and H. Lu, "Toughness, hamiltonicity and spectral radius in graphs," 2022.
- <span id="page-16-9"></span>[160] R. Sharma, A. Sharma, and R. Saini, "A note on variance bounds and location of eigenvalues," 2019.
- <span id="page-16-8"></span>[161] C. Min and Y. Chen, "On the variance of linear statistics of hermitian random matrices," *Acta Physica Polonica B*, vol. 47, no. 4, p. 1127, 2016. [Online]. Available: <https://doi.org/10.5506%2Faphyspolb.47.1127>

# A Related work

Dataset distillation or condensation. Dataset Distillation (DD) [\[96,](#page-13-5) [97,](#page-13-6) [98,](#page-13-7) [4,](#page-9-2) [8,](#page-9-3) [7,](#page-9-4) [6\]](#page-9-5) aims to condense large datasets into smaller versions for comparable model training, whereas Dataset Condensation (DC) [\[99,](#page-13-0) [100\]](#page-13-8) enhances this process, focusing on efficiency improvements through techniques like gradient matching. However, the previous approaches still have some open questions, such as how to design an effective matching strategy [\[20,](#page-10-2) [6\]](#page-9-5). In this work, we focus on designing a novel matching strategy for efficient graph condensation.

Graph signal processing. Our work is also related to graph signal processing, which studies the analysis and processing of signals defined on graphs [\[16,](#page-9-6) [19\]](#page-9-7). Graph signal processing extends classical signal processing concepts like frequency, filtering, and sampling to graph signals [\[14,](#page-9-8) [17\]](#page-9-9), which are functions that assign values to the nodes or edges of a graph, providing tools for feature extraction, denoising, compression, and learning on graph-structured data [\[18,](#page-9-10) [13,](#page-9-11) [143\]](#page-15-4).

Graph neural networks (GNNs). As the generalization of deep neural networks to graph data, Graph Neural Networks (GNNs) enhance the representation of individual nodes by utilizing information from their neighboring nodes[\[95,](#page-13-4) [49,](#page-11-8) [148,](#page-16-1) [144,](#page-15-5) [145\]](#page-15-6). Due to their powerful capability in handling graph-structured data, GNNs have achieved remarkable performance on various real-world tasks, such as social networks $[1, 31, 147]$  $[1, 31, 147]$  $[1, 31, 147]$  $[1, 31, 147]$  $[1, 31, 147]$ , physical  $[33, 34, 35]$  $[33, 34, 35]$  $[33, 34, 35]$  $[33, 34, 35]$  $[33, 34, 35]$ , and chemical interactions  $[41, 37, 38, 39]$  $[41, 37, 38, 39]$  $[41, 37, 38, 39]$  $[41, 37, 38, 39]$  $[41, 37, 38, 39]$  $[41, 37, 38, 39]$  $[41, 37, 38, 39]$ , and knowledge graphs[\[45,](#page-11-10) [42,](#page-11-11) [43\]](#page-11-12).

Graph coarsening & Graph sparsification. Graph coarsening minimizes the size of graph data while preserving its basic properties [\[65,](#page-12-2) [67,](#page-12-3) [66\]](#page-12-9) by grouping similar nodes into supernodes. Graph Sparsification reduces the number of edges to make the graph sparser [\[62,](#page-12-0) [60,](#page-11-13) [54,](#page-11-2) [11,](#page-9-13) [131,](#page-15-7) [130\]](#page-15-8), as there are many redundant relations in the graphs. Both two methods are based on the idea of coreset selection and data pruning [\[101,](#page-13-9) [102,](#page-13-10) [9,](#page-9-14) [132\]](#page-15-9), aiming to remove less important information from the original graph. However, these methods rely heavily on heuristic unsupervised techniques, resulting in poor generalization performance in downstream tasks. Moreover, they are unable to condense graphs with extremely low condensation ratios [\[5,](#page-9-1) [3\]](#page-9-15).

<span id="page-17-0"></span>

# B Datasets and implementation details

## B.1 Datasets

We evaluate CTRL on three transductive datasets: Cora, Citeseer [\[95\]](#page-13-4), Ogbn-arxiv [\[113\]](#page-14-1), Ogbnxrt [\[117\]](#page-14-2), and two inductive datasets: Flickr [\[150\]](#page-16-0), and Reddit [\[118\]](#page-14-3). We obtain all datasets from PyTorch Geometric[\[152\]](#page-16-3) with publicly available splits and consistently utilize these splits in all experiments. For graph classification, we use multiple molecular datasets from Open Graph Benchmark (OGB) [\[113\]](#page-14-1) and TU Datasets (MUTAG and NCI1) [\[119\]](#page-14-4) for graph-level property classification, and one superpixel dataset CIFAR10 [\[120\]](#page-14-5). In addition, we use Pubmed[\[95\]](#page-13-4) in our neural architecture search (NAS) experiments. Dataset statistics are shown in Table [7](#page-17-1) and [8.](#page-18-2)

| Dataset    | #Nodes  | #Edges     | #Classes | #Features | Training/Validation/Test |
|------------|---------|------------|----------|-----------|--------------------------|
| Cora       | 2,708   | 5,429      | 7        | 1,433     | 140/500/1000             |
| Citeseer   | 3,327   | 4,732      | 6        | 3,703     | 120/500/1000             |
| Ogbn-arxiv | 169,343 | 1,166,243  | 40       | 128       | 90,941/29,799/48,603     |
| Ogbn-xrt   | 169,343 | 1,166,243  | 40       | 768       | 90,941/29,799/48,603     |
| Flickr     | 89,250  | 899,756    | 7        | 500       | 44,625/22312/22313       |
| Reddit     | 232,965 | 57,307,946 | 210      | 602       | 15,3932/23,699/55,334    |

<span id="page-17-1"></span>Table 7: Dataset statistics(node classification). The first four are transductive datasets and the last two are inductive datasets.

### B.2 Implementation Details

During the implementation of CTRL, considering datasets with a large number of nodes, where the feature distribution plays a crucial role in the overall data, such as Ogbn-arxiv, Ogbn-xrt, and Reddit, we employ the specific initialization method depicted in Sec. [2.2](#page-3-1) on them. Additionally, due to the

<span id="page-18-2"></span>

| <b>Dataset</b> | Type       | #Classes | #Graphs | #Avg.Nodes | #Avg. Edges |
|----------------|------------|----------|---------|------------|-------------|
| CIFAR10        | Superpixel | 10       | 60,000  | 117.6      | 941.07      |
| ogbg-molhiv    | Molecule   | 2        | 41,127  | 25.5       | 54.9        |
| ogbg-molbace   | Molecule   | 2        | 1,513   | 34.1       | 36.9        |
| ogbg-molbbbp   | Molecule   | 2        | 2,039   | 24.1       | 26.0        |
| MUTAG          | Molecule   | 2        | 188     | 17.93      | 19.79       |
| NCI1           | Molecule   | 2        | 4,110   | 29.87      | 32.30       |

Table 8: Dataset statistics(graph classification).

substantial volume of data and numerous gradient matching iterations, we introduce a threshold  $tau$ during the matching process. We match gradients smaller than this threshold on both direction and magnitude, while gradients exceeding the threshold were matched solely based on their directions. Through extensive experimentation, we observe that this strategy reduced computational costs and led to further performance improvements. For other datasets, due to the smaller dataset size, we utilize both gradient magnitude and direction for matching throughout the entire matching process and we directly employ random sampling for initialization. For graph classification tasks, we employ gradient magnitude and direction matching throughout the graph condensation and adopt a strategy of random sampling for initialization. And the beta in Fig. [5\(a\)a](#page-6-0)nd [5\(b\)](#page-6-1) are 0.1 and 0.15, respectively.

<span id="page-18-0"></span>

## B.3 Implementation Details of Ablation Experiments

The values of  $\beta$  in the training of models in Fig[.5\(a\)](#page-6-0) and Fig[.5\(b\)](#page-6-1) are 0.1 and 0.15, respectively. In  $Fig.5(c)$ , we employ the specific initialization method(K-MEANS-based) in CTRL for Ogbn-arxiv, and random sampling for Cora and Citeseer, all the results are obtained by repeating experiments 5 times.

## B.4 Hyper-parameter Setting

For node classification, without specific mention, we adopt a 2-layer SGC [\[133\]](#page-15-10) with 256 hidden units as the GNN used for gradient matching. We employ a multi-layer perceptron (MLP) as the function  $g_{\Phi}$  models the relationship between  $\mathbf{A}'$  and  $\mathbf{X}'$ . Specifically, we adopt a 3-layer MLP with 128 hidden units for small graphs (Cora and Citeseer) and 256 hidden units for large graphs (Flickr, Reddit, and Ogbn-arxiv). We tune the training epoch for CTRL in a range of 400, 600, 1000. For the choices of condensation ratio  $r$ , we divide the discussion into two parts. The first part is about transductive datasets. For Cora and Citeseer, since their labeling rates are very small (5.2% and 3.6%, respectively), we choose r to be  $25\%, 50\%, 100\%$  of the labeling rate. Thus, we finally choose 1.3%, 2.6%, 5.2% for Cora and 0.9%, 1.8%, 3.6% for Citeseer. For Ogbn-arxiv and Ogbn-xrt, we choose r to be  $0.1\%$ ,  $0.5\%$ ,  $1\%$  of its labeling rate (53%), thus being  $0.05\%$ ,  $0.2\%$ ,  $0.5\%$ . The second part is about inductive datasets. As the nodes in the training graphs are all labeled in inductive datasets, we choose 0.1%, 0.5%, 0.1% for Flickr and 0.05%, 0.1%, 0.2% for Reddit. The beta that measures the weight of the direction and magnitude is in 0.1, 0.2, 0.5, 0.7, 0.9. For graph classification, we vary the number of learned synthetic graphs per class in the range of 1, 10, 50 (1, 10 for MUTAG, in the case where the condensation ratio has already reached 13.3% with a factor of 10, and we will refrain from further increasing the condensation ratio to ensure the effectiveness of data condensation.) and train a GCN on these graphs. The *beta* that measures the weight of the direction and magnitude is between 0.05 and 0.9 for node classification tasks, while between 0.1 and 0.9 for graph classification tasks.

# C More details about quantitative analysis

<span id="page-18-1"></span>

## C.1 Multiple Metrics

We select six complementary metrics to provide a comprehensive evaluation of the distribution of high and low-frequency signals between the synthetic and original graphs. The proportion of low-frequency nodes directly quantifies the relative abundance of high versus low-frequency signals. Meanwhile, the mean of the high-frequency signal region for each feature dimension can describe the

right-shift phenomenon in the whole spectrum [\[114\]](#page-14-9) and get a measure of the overall strength of the high-frequency content. Additionally, spectral peakedness and skewness directly characterize the shape of the frequency distribution [\[157,](#page-16-4) [156\]](#page-16-5). Spectral radius indicates the overall prevalence of high-frequency signals, with a larger radius corresponding to more high-frequency content [\[158,](#page-16-6) [159\]](#page-16-7). Finally, the variance of eigenvalues measures the spread of the frequency distribution [\[161,](#page-16-8) [160\]](#page-16-9).

Together, these metrics enable multi-faceted quantitative analysis of the frequency distribution from diverse perspectives, including intuitive reflection of frequency distribution, spectral characterization to assess distribution shapes, and statistical distribution properties like spread and central tendency. To provide a more intuitive summarized measure of the similarity between the synthetic and original graphs across the six metrics, we first normalize all these metrics by simply multiplying or dividing by powers of 10 in order to mitigate the impact of scale differences, then we compute the Pearson correlation coefficient which reflects the overall trend, complementing the individual metric comparisons.

<span id="page-19-0"></span>

### C.2 Details about Gradient Magnitudes and Graph Spectral Distribution.

**Measurement of graphs signals.** Graph spectral analysis provides insights into the frequency content of signals on graphs. This allows the graph signal to be decomposed into components of different frequencies based on the graph discrete Fourier transform (GDFT) [\[121,](#page-14-10) [103\]](#page-13-11), as shown in Eq. [\(11\)](#page-19-1) and Eq. [\(12\)](#page-19-2).

<span id="page-19-1"></span>
$$
L = I - D^{-\frac{1}{2}} A D^{-\frac{1}{2}} = U \Lambda U^{T},
$$
\n(11)

<span id="page-19-2"></span>
$$
\hat{\boldsymbol{x}} = \boldsymbol{U}^T \boldsymbol{x}, \quad \hat{\boldsymbol{x}}_H = \boldsymbol{U}_H^T \boldsymbol{x}, \quad \hat{\boldsymbol{x}}_L = \boldsymbol{U}_L^T \boldsymbol{x}, \tag{12}
$$

where *L* denotes a regularized graph Laplacian matrix, *U* is a matrix composed of the eigenvectors of *L*.  $\Lambda$  is a diagonal matrix with the eigenvalues of *L* on its diagonal, and  $x = (x_1, x_2, \cdots, x_N)^T \in \mathbb{R}^N$ and  $\hat{x} = (\hat{x}_1, \hat{x}_2, \dots, \hat{x}_N)^T = \mathbf{U}^T \mathbf{x} \in \mathbb{R}^N$  represent a signal on the graph and the GDFT of  $\mathbf{x}$ , respectively, while  $U_H$  and  $U_L$  correspond to matrices containing eigenvectors associated with eigenvalues that are above and below the specified cutoff frequency  $\tau$ . Furthermore,  $\hat{x}_H$  and  $\hat{x}_L$ represent signals attributed to high-frequency and low-frequency components respectively.

However, this process is often time-consuming due to the Laplacian matrix decomposition required. Following the previous work [\[114\]](#page-14-9), we introduce the definition of the high-frequency area, represented by Eq. [\(13\)](#page-19-3).

<span id="page-19-3"></span>
$$
S_{\text{high}} = \frac{\sum_{k=1}^{N} \lambda_k \hat{x}_k^2}{\sum_{k=1}^{N} \hat{x}_k^2} = \frac{\boldsymbol{x}^T \boldsymbol{L} \boldsymbol{x}}{\boldsymbol{x}^T \boldsymbol{x}},\tag{13}
$$

where  $\hat{x}_k^2/\sum_{i=1}^N \hat{x}_i^2$  denotes the spectral energy distribution at  $\lambda_k(1 \leq k \leq N)$ , as the spectral energy on low frequencies contributes less on  $S_{\text{high}}$ , this indicator increases when spectral energy shifts to larger eigenvalues. Note that  $S_{\text{high}}$  is defined under the premise of x being a one-dimensional vector. In subsequent computations, Shigh is computed for each feature dimension and averaged.

Employing the methods outlined in [\[114\]](#page-14-9) to create artificial graphs, we first initialize the composite graphs with three graph models, Erdos-Renyi [\[137\]](#page-15-11), Barabasi-Albert [\[136\]](#page-15-12), and Watts-Strogatz [\[139\]](#page-15-13). Subsequently, we conducted 3\*4 sets of experiments on four commonly used GNN models: SGC [\[133\]](#page-15-10), ChebNet [\[134\]](#page-15-14), GCN, and APPNP [\[125\]](#page-14-11), and repeat each experiment 1000 times by varying the standard deviation of the Gaussian distribution and the random seed. The results reveal a fairly strong correlation between the high-frequency area and the gradient magnitudes, as shown in Fig. [6,](#page-20-1) with the high-frequency area increasing gradually, the gradient size generated has an obvious upward trend. Furthermore, 8 of the 12 experiments showed a correlation greater than 0.95. We conducted several experiments in this study, utilizing the following implementation details:

We initialize a list of 1,000 random seeds ranging from 0 to 100,000,000. With a fixed random seed of 0, we created random synthetic graphs using the Erdos-Renyi [\[137\]](#page-15-11) (edge probability 0.2), Barabasi-Albert (2 edges per new node) [\[136\]](#page-15-12), and Watts-Strogatz [\[139\]](#page-15-13) (4 initial neighbors per node, rewiring probability 0.2) models. For each random seed, we followed the same procedure: We selected a bias term from [1, 2, 3, 4, 5] in sequence and then used these different bias terms to

<span id="page-20-1"></span>Image /page/20/Figure/0 description: The image displays two bar charts side-by-side, both illustrating the relationship between the mean of the high-frequency area and the average gradient magnitude. The left chart, labeled "(a) Train with SGC model", shows a Spearman correlation coefficient of 0.953. The x-axis represents the mean of the high-frequency area with values ranging from 0.494 to 0.964. The y-axis represents the average gradient magnitude, scaled by 10^5, with values from 0.5 to 2.5. A yellow line indicates a polynomial fit. The right chart, labeled "(b) Train with ChebNet model", shows a Spearman correlation coefficient of 0.965. Its x-axis also represents the mean of the high-frequency area, with values from 0.493 to 0.961. The y-axis represents the average gradient magnitude, scaled from 0 to 32, with values from 0 to 32. A yellow line also indicates a polynomial fit. Both charts show an increasing trend in average gradient magnitude as the mean of the high-frequency area increases.

Figure 6: (a) and (b) demonstrate the relationship between average gradient magnitude and graph spectral distribution for synthetic graphs conforming to the Erdos-Renyi model, training with SGC and ChebNet respectively. The curves shown are the result of fitting a fourth-order polynomial. Altering the frequency distribution causes a significant impact on the model gradient during training.

generate node features following a Gaussian distribution. With a fixed 200 nodes and 128 feature dimensions, we computed the mean of high-frequency area for each feature dimension based on the node feature matrix and adjacency matrix of the synthetic graph. We then trained a simple 2-layer SGC [\[133\]](#page-15-10), ChebNet [\[134\]](#page-15-14), GCN, and APPNP [\[125\]](#page-14-11) for 50 epochs, recording the average gradient magnitude. We calculated the Spearman correlation coefficient using the 1,000 rounds of results obtained. The results presented in Fig. [6](#page-20-1) were from two rounds under the Erdos-Renyi model, while Table [9](#page-20-2) shows the corresponding results under different graph models and GNNs.

<span id="page-20-2"></span>Table 9: Spearman correlation coefficients of high-frequency area and gradient amplitude under different graph models and GNN architectures. Strong correlations were shown under almost all combinations

|                 | SGC   | Cheby | APPNP | GCN   |
|-----------------|-------|-------|-------|-------|
| Erdos-Renyi     | 0.953 | 0.965 | 0.955 | 0.859 |
| Barabasi-Albert | 0.955 | 0.961 | 0.875 | 0.818 |
| Watts-Strogatz  | 0.946 | 0.955 | 0.939 | 0.679 |

<span id="page-20-0"></span>

#### C.3 More Details about Matching Trajectories.

The experiments of gradient difference analysis in the optimization process are conducted on the Reddit dataset with a condensation ratio of 0.50%, utilizing four distinct gradient matching methods. It is noteworthy that the hyperparameter  $\beta$  for both the CTRL and Cos + Norm methods were uniformly set to 0.3.

In the training process, the optimization loss for synthetic data is obtained by training the GNN model on subgraphs composed of nodes from each class. To enhance the evaluation of optimization effectiveness, we monitored the variations in gradients during the training process across nodes of distinct classes within both synthetic and original datasets. In the previous text, due to space limitations (Reddit has a total of 40 classes), we presented results for class 3 with a relatively high proportion of nodes. In this context, to make the experimental results more representative, as depicted in Fig. [7,](#page-21-0) we showcase representative results for classes with a lower proportion of nodes (class 32), classes with a moderate proportion of nodes (class 28), and another class with a higher proportion of nodes (class 28).

<span id="page-21-0"></span>Image /page/21/Figure/0 description: The image displays a 2x3 grid of plots. The top row contains three plots labeled (a), (b), and (c), all titled "Cosine distance" for classes 0, 28, and 32 respectively. The x-axis for all these plots is labeled "Step" and ranges from 0.0 to 1.6e+6. The y-axis is labeled "Cosine distance" and ranges from 0 to 21 in plot (a), 0 to 9 in plot (b), and 0 to 2.1 in plot (c). Each plot shows four lines representing different conditions: CTRL (yellow), Cos (blue), Cos + Norm (teal), and Norm (red). The bottom row contains three plots labeled (d), (e), and (f), all titled "Magnitude difference" for classes 0, 28, and 32 respectively. The x-axis for these plots is also labeled "Step" and ranges from 0.0 to 1.6e+6. The y-axis is labeled "Magnitude difference" and ranges from 0 to 6 in plot (d), 0 to 3 in plot (e), and 0 to 0.9 in plot (f). Similar to the top row, these plots also show four lines representing the same conditions: CTRL (yellow), Cos (blue), Cos + Norm (teal), and Norm (red).

Figure 7: (a)(b)(c)(d)(e) and (f)illustrate the gradient difference during the optimization process of the condensed graphs using CTRL and the basic gradient matching methods with three gradient discrepancy metrics, step refers to the times of calculating gradient matching losses. Notably, these results align closely with the conclusions drawn in Sec. [1.](#page-0-0)

# D More experiments

## D.1 Application of CTRL to other Condensation Methods

To further demonstrate the difference between distillation methods in Graph and CV yields, we tried to add CTRL to the CV distillation methods [\[23\]](#page-10-1), which can also achieve improvements, in some cases. However, it is not as obvious as in graph distillation. We also conducted experiments on the

Table 10: MD denotes the method proposed in [\[23\]](#page-10-1) using both cosine distance and Euclidean distance(equal weight), while MDA is the improved version of MD, all results in the table are averaged over three repetitions for accuracy and consistency.

| <b>Dataset</b>      | <b>IPC</b> | <b>MD</b>      | <b>MD+CTRL</b> | <b>MDA</b>     | <b>MDA+CTRL</b> |
|---------------------|------------|----------------|----------------|----------------|-----------------|
| <b>FashionMNIST</b> | 10         | $85.4 pm 0.3$ | $85.4 pm 0.3$ | $84.2 pm 0.3$ | $84.7 pm 0.1$  |
| <b>FashionMNIST</b> | 50         | $87.4 pm 0.2$ | $87.6 pm 0.2$ | $87.9 pm 0.2$ | $87.9 pm 0.2$  |
| <b>SVHN</b>         | 10         | $75.9 pm 0.7$ | $75.9 pm 0.4$ | $75.9 pm 0.7$ | $76.3 pm 0.4$  |
| <b>SVHN</b>         | 50         | $82.9 pm 0.2$ | $83.0 pm 0.1$ | $83.2 pm 0.3$ | $83.4 pm 0.2$  |
| <b>CIFAR-10</b>     | 1          | $30.0 pm 0.6$ | $31.0 pm 0.4$ | -              | -               |
| <b>CIFAR-10</b>     | 10         | $49.5 pm 0.5$ | $49.5 pm 0.3$ | $50.2 pm 0.6$ | $51.3 pm 0.1$  |

unstructured graph compression method SFGC, which similarly demonstrated notable improvements.

### D.2 Visualizations

To better understand the effectiveness of CTRL, we can visualize the condensed graphs in node classification tasks. It is worth noting that while utilizing the CTRL technique to generate synthetic graphs, it is possible to encounter outliers in contrast with GCOND, especially when working with extensive datasets. This phenomenon arises due to the difficulty in learning and filtering out high-frequency signals present in larger datasets, given the low-pass filtering nature of GNNs. The presence of outliers in the condensed graphs generated by the CTRL method implies that some of

<span id="page-22-0"></span>Table 11: Performance across different GNN architectures. **Avg.** and **Std.** : the average performance and the standard deviation of the results of all architectures,  $\Delta(\%)$  denotes the improvements upon the DC-Graph. We mark the best performance by bold.

| Datasets                      | Methods  | Architectures |      |       |       |      |      |      | Statistics |      |                  |
|-------------------------------|----------|---------------|------|-------|-------|------|------|------|------------|------|------------------|
|                               |          | MLP           | GAT  | APPNP | Cheby | GCN  | SAGE | SGC  | Avg.       | Std. | $\Delta(\%)$     |
| Citeseer<br>(Ratio = 1.80%)   | DC-Graph | 66.2          | -    | 66.4  | 64.9  | 66.2 | 65.9 | 69.6 | 66.5       | 1.5  |                  |
|                               | GCond    | 63.9          | 55.4 | 69.6  | 68.3  | 70.5 | 66.2 | 70.3 | 66.3       | 5.0  | $\downarrow$ 0.2 |
|                               | SFGC     | 71.3          | 72.1 | 70.5  | 71.8  | 71.6 | 71.7 | 71.8 | 71.5       | 0.5  | $\uparrow$ 5.0   |
|                               | CTRL     | 71.6          | 72.4 | 71.2  | 71.4  | 71.8 | 72.1 | 72.1 | 71.8       | 0.4  | $\uparrow$ 5.3   |
| Cora<br>(Ratio = 2.60%)       | DC-Graph | 67.2          | -    | 67.1  | 67.7  | 67.9 | 66.2 | 72.8 | 68.1       | 2.1  |                  |
|                               | GCond    | 73.1          | 66.2 | 78.5  | 76.0  | 80.1 | 78.2 | 79.3 | 75.9       | 4.5  | $\uparrow$ 7.8   |
|                               | SFGC     | 81.1          | 80.8 | 78.8  | 79.0  | 81.1 | 81.9 | 79.1 | 80.3       | 1.2  | $\uparrow$ 12.2  |
|                               | CTRL     | 81.1          | 81.0 | 78.7  | 79.2  | 81.2 | 82.0 | 80.1 | 80.5       | 1.1  | $\uparrow$ 12.4  |
| Ogbn-arxiv<br>(Ratio = 0.25%) | DC-Graph | 59.9          | -    | 60.0  | 55.7  | 59.8 | 60.0 | 60.4 | 59.3       | 1.6  |                  |
|                               | GCond    | 62.2          | 60.0 | 63.4  | 54.9  | 63.2 | 62.6 | 63.7 | 61.4       | 2.9  | $\uparrow$ 2.1   |
|                               | SFGC     | 65.1          | 65.7 | 63.9  | 60.7  | 65.1 | 64.8 | 64.8 | 64.3       | 1.6  | $\uparrow$ 5.0   |
|                               | CTRL     | 65.2          | 66.0 | 63.6  | 61.0  | 65.3 | 64.9 | 64.8 | 64.4       | 1.5  | $\uparrow$ 5.1   |
| Flickr<br>(Ratio = 0.50%)     | DC-Graph | 43.1          | -    | 45.7  | 43.8  | 45.9 | 45.8 | 45.6 | 45.0       | 1.1  |                  |
|                               | GCond    | 44.8          | 40.1 | 45.9  | 42.8  | 47.1 | 46.2 | 46.1 | 44.7       | 2.3  | $\downarrow$ 0.3 |
|                               | SFGC     | 47.1          | 45.3 | 40.7  | 45.4  | 47.1 | 47.0 | 42.5 | 45.0       | 2.3  |                  |
|                               | CTRL     | 47.1          | 45.6 | 40.9  | 45.3  | 47.2 | 47.1 | 43.2 | 45.2       | 2.2  | $\uparrow$ 0.2   |
| Reddit<br>(Ratio = 0.10%)     | DC-Graph | 50.3          | -    | 81.2  | 77.5  | 89.5 | 89.7 | 90.5 | 79.8       | 14.0 |                  |
|                               | GCond    | 42.5          | 60.2 | 87.8  | 75.5  | 89.4 | 89.1 | 89.6 | 76.3       | 17.1 | $\downarrow$ 3.5 |
|                               | SFGC     | 89.5          | 87.1 | 88.3  | 82.8  | 89.7 | 90.3 | 89.5 | 88.2       | 2.4  | $\uparrow$ 8.4   |
|                               | CTRL     | 89.7          | 87.6 | 88.5  | 83.0  | 90.0 | 90.5 | 89.8 | 88.4       | 2.4  | $\uparrow$ 8.6   |

the high-frequency signals in the original data are well-preserved in the synthesized graph [\[135\]](#page-15-15). Previous research has demonstrated the importance of both high and low-frequency signals in graph data for effective GNN training [\[107,](#page-14-12) [106\]](#page-14-13). This further substantiates the validity of this observation.

Image /page/22/Figure/3 description: The image displays five network graphs, labeled (a) through (e). Graph (a) is labeled "Cora, r = 2.5%". Graph (b) is labeled "Citeseer, r = 1.8%". Graph (c) is labeled "Flickr, r = 0.1%". Graph (d) is labeled "Ogbn-arxiv, r = 0.05%". Graph (e) is labeled "Reddit, r = 0.1%". Each graph consists of interconnected nodes, with varying densities and structures, and some nodes are colored in shades of blue, purple, red, and cyan.

Figure 8: When the size of the original datasets is relatively small(a)(b), the resulting composite dataset tends to exhibit fewer outliers. In contrast, when working with compressed results of large datasets $(c)(d)(e)$ , it is more likely to encounter a higher number of outliers

### D.3 Time complexity and Runtime

Time complexity. For simplicity, let the number of MLP layers in the adjacency matrix generator be  $L$ , and all the hidden units are  $d$ . In the forward process, we first calculate the  $A'$ , which has the complexity of  $O(N'^2d^2)$ . Second, the forward process of GNN on the original graph has a complexity of  $\tilde{O}(mLNd^2)$ , where m denotes the sampled size per node in training. Third, the complexity of

training on the condensed graph is  $O(LN'd)$ . Then, taking into account additional matching metrics calculations, the complexity of the gradient matching strategy is  $O(2|\theta| + |A'| + |X'|)$ .

The overall complexity of CTRL can be represented as  $O(N'^2d^2) + O(mLNd^2) + O(LN'd)$  +  $O(2|\theta| + |A'| + |X'|)$ . Note  $N' < N$ , we can drop the terms that only involve N' and constants (e.g., the number of L and m). The final complexity can be simplified as  $O(mLNd^2)$ , thus it can be seen that although the matching process is much finer, the complexity of CTRL still is linear to the number of nodes in the original graph.

Running time. We report the running time of the CTRL in two kinds of six groups of classification tasks. For node classification tasks, we vary the condensing ratio r in the range of  $\{0.9\%, 1.8\%, 3.6\%\}$ for Citeseer,  $\{1.3\%, 2.6\%, 5.2\%\}$  for Cora,  $\{0.1\%, 0.5\%, 1.0\%\}$  for Ogbn-arxiv, all experiments are conducted five times on one single V100-SXM2 GPU. For graph classification tasks, We vary the condensing ratio *r* in the range of  $\{0.2\%, 1.7\%, 8.3\%\}$  for Ogbg-molbace,  $\{0.1\%, 1.2\%, 6.1\%\}$  for Ogbg-molbbbp,  $\{0.1\%, 0.5\%, 1.0\% \}$  for Ogbg-molhiv, all experiments are repeated 5 times on one single A100-SXM4 GPU.

We also conduct experiments with GCond and Docscond using the same settings, respectively. As shown in Table [12](#page-23-0) and [13,](#page-23-0) our approach achieved a similar running time to GCond and Doscond, note that the comparison procedure here improves the cosine distance calculation of GCond, otherwise, our method would be faster on small datasets.

<span id="page-23-0"></span>

| Table 12: Runing time on Citeseer, Cora and |  |
|---------------------------------------------|--|
| Ogbn-arxiv for 50 epochs.                   |  |

Table 13: Runing time on Ogbg-molbace, molbbbp and molhiv for 100 epochs.

| Dataset    | r                             | GCond                                                     | <b>CTRL</b>                                                  | Dataset         | r                             | Doscond                                               | <b>CTRL</b>                                            |
|------------|-------------------------------|-----------------------------------------------------------|--------------------------------------------------------------|-----------------|-------------------------------|-------------------------------------------------------|--------------------------------------------------------|
| Citeseer   | $0.9\%$<br>$1.8\%$<br>$3.6\%$ | $68.7 + 2.4s$<br>$70.2 \pm 2.5s$<br>$78.1 \pm 2.3s$       | $71.3 \pm 2.5s$<br>$73.7 \pm 2.6s$<br>$88.6 \pm 2.1$ s       | Ogbg-molbace    | $0.2\%$<br>1.7%<br>8.3%       | $18.1 + 1.4s$<br>$23.1 \pm 1.5s$<br>$26.6 + 1.3s$     | $21.3 \pm 1.5s$<br>$27.4 \pm 1.6s$<br>$29.5 \pm 1.1$ s |
| Cora       | $1.3\%$<br>$2.6\%$<br>$5.2\%$ | $76.4 + 3.3s$<br>$77.7 + 2.2s$<br>$85.3 + 3.4s$           | $76.8 \pm 2.8$ s<br>$78.7 \pm 3.4s$<br>$89.2 + 2.5s$         | $Ogbg$ -molbbbp | $0.1\%$<br>$1.2\%$<br>$6.1\%$ | $17.2 \pm 1.3s$<br>$20.2 \pm 1.2$ s<br>$22.1 + 1.4s$  | $18.9 \pm 1.8$ s<br>$23.4 + 1.4s$<br>$24.9 \pm 1.5s$   |
| Ogbn-arxiv | $0.1\%$<br>$0.5\%$<br>$1.0\%$ | $939.3 + 4.9s$<br>$1008.4 \pm 3.1$ s<br>$1061.3 \pm 2.9s$ | $967.6 \pm 5.8$ s<br>$1033.4 \pm 4.2s$<br>$1087.6 \pm 1.8$ s | Ogbg-molhiv     | 0.1%<br>0.5%<br>$1.0\%$       | $39.6 \pm 1.9s$<br>$40.2 + 1.1$ s<br>$40.8 \pm 1.9$ s | $42.3 \pm 1.8s$<br>$43.1 \pm 2.2s$<br>$43.9 \pm 1.8$ s |
|            |                               |                                                           |                                                              |                 |                               |                                                       |                                                        |
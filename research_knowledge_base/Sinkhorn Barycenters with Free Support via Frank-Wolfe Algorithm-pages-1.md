# **Sinkhorn Barycenters with Free Support via <PERSON><PERSON><PERSON> Algorithm**

<PERSON><PERSON><PERSON><sup>1</sup> *<EMAIL>* <PERSON><PERSON><sup>2</sup> *<EMAIL>* <PERSON><PERSON><PERSON><PERSON><sup>1,2</sup> *<EMAIL>*

<PERSON><sup>1,3</sup> *<EMAIL>*

May 31, 2019

### **Abstract**

We present a novel algorithm to estimate the barycenter of arbitrary probability distributions with respect to the Sinkhorn divergence. Based on a Frank-Wolfe optimization strategy, our approach proceeds by populating the support of the barycenter incrementally, without requiring any pre-allocation. We consider discrete as well as continuous distributions, proving convergence rates of the proposed algorithm in both settings. Key elements of our analysis are a new result showing that the Sinkhorn divergence on compact domains has Lipschitz continuous gradient with respect to the Total Variation and a characterization of the sample complexity of Sinkhorn potentials. Experiments validate the effectiveness of our method in practice.

## **1 Introduction**

Aggregating and summarizing collections of probability measures is a key task in several machine learning scenarios. Depending on the metric adopted, the properties of the resulting average (or *barycenter*) of a family of probability measures vary significantly. By design, optimal transport metrics are better suited at capturing the geometry of the distribution than Euclidean distance or other *f*-divergence [\[14\]](#page-11-0). In particular, Wasserstein barycenters have been successfully used in settings such as texture mixing  $[41]$ , Bayesian inference  $[50]$ , imaging  $[26]$ , or model ensemble  $[18]$ .

The notion of barycenter in Wasserstein space was first introduced by [\[2\]](#page-10-0) and then investigated from the computational perspective for the original Wasserstein distance [\[51,](#page-13-2) [12\]](#page-11-2) as well as its entropic regularizations (e.g. Sinkhorn)  $[14, 6, 20]$  $[14, 6, 20]$  $[14, 6, 20]$  $[14, 6, 20]$  $[14, 6, 20]$ . Two main challenges in this regard are: *i*) how to efficiently identify the support of the candidate barycenter and *ii*) how to deal with continuous (or infinitely supported) probability measures. The first problem is typically addressed by either fixing the support of the barycenter a-priori [\[51,](#page-13-2) [20\]](#page-11-3) or by adopting an alternating minimization procedure to iteratively optimize the support point locations and their weights  $[14, 12]$  $[14, 12]$  $[14, 12]$ . While fixed-support methods enjoy better theoretical guarantees, free-support algorithms are more memory efficient and practicable in high dimensional settings. The problem of dealing with continuous distributions has been mainly

<sup>1</sup>Computer Science Department, University College London, WC1E 6BT London, United Kingdom

 $2$ Computational Statistics and Machine Learning - Istituto Italiano di Tecnologia, 16100 Genova, Italy

<sup>&</sup>lt;sup>3</sup>Electrial and Electronics Engineering Department, Imperial College London, SW7 2BT, United Kingdom.

approached by adopting stochastic optimization methods to minimize the barycenter functional [\[12,](#page-11-2) [51,](#page-13-2) [20\]](#page-11-3)

In this work we propose a novel method to compute the barycenter of a set of probability distributions with respect to the Sinkhorn divergence [\[25\]](#page-12-1) that does not require to fix the support beforehand. We address both the cases of discrete and continuous probability measures. In contrast to previous free-support methods, our algorithm does not perform an alternate minimization between support and weights. Instead, we adopt a Frank-Wolfe (FW) procedure to populate the support by incrementally adding new points and updating their weights at each iteration, similarly to kernel herding strategies [\[5\]](#page-10-2) and conditional gradient for sparse inverse problem [\[9,](#page-11-4) [8\]](#page-11-5). Upon completion of this paper, we found that an idea with similar flavor, concerning the application a Frank-Wolfe scheme in conjunction with Sinkhorn functionals has been very recently considered in distributional regression settings for the case of Sinkhorn negentropies [35]. However, the analysis in this paper focuses on the theoretical properties of the proposed algorithm, specifically for the case of an *inexact* Frank-Wolfe procedure, which becomes critical in the case of continuous measures. In particular, we prove the convergence and rates of the proposed optimization method for both finitely and infinitely supported distribution settings. A central result in our analysis is the characterization of regularity properties of Sinkhorn potentials (i.e. the dual solutions of the Sinkhorn divergence problem), which extends recent work in [\[21,](#page-11-6) [23\]](#page-12-2) and which we consider of independent interest. We empirically evaluate the performance of the proposed algorithm.

**Contributions.** The analysis of the proposed algorithm hinges on the following contributions: *i*) we show that the gradient of the Sinkhorn divergence is Lipschitz continuous on the space of probability measures with respect to the Total Variation. This grants us convergence of the barycenter algorithm in finite settings. *ii*) We characterize the sample complexity of Sinkhorn potentials of two empirical distributions sampled from arbitrary probability measures. This latter result allows us to *iii*) provide a concrete optimization scheme to approximately solve the barycenter problem for arbitrary probability measures with convergence guarantees. *iv*) A byproduct of our analysis is the generalization of the FW algorithm to settings where the objective functional is defined only on a set with empty interior, which is the case for Sinkhorn divergence barycenter problem.

The rest of the paper is organized as follows: Sec. [2](#page-1-0) reviews standard notions of optimal transport theory. Sec. [3](#page-3-0) introduces the barycenter functional, and proves the Lipschitz continuity of its gradient. Sec. [4](#page-4-0) describes the implementation of our algorithm and Sec. [5](#page-6-0) studies its convergence rates. Finally, Sec. [6](#page-8-0) evaluates the proposed methods empirically and Sec. [7](#page-10-3) provides concluding remarks.

<span id="page-1-0"></span>

### **2 Background**

The aim of this section is to recall definitions and properties of Optimal Transport theory with entropic regularization. Throughout the work, we consider a compact set  $\mathcal{X} \subset \mathbb{R}^d$  and a symmetric cost function c:  $\mathcal{X} \times \mathcal{X} \to \mathbb{R}$ . We set  $D := \sup_{x,y \in \mathcal{X}} c(x,y)$  and denote by  $\mathcal{M}_1^+(\mathcal{X})$  the space of probability measures on X (positive Radon measures with mass 1). For any  $\alpha, \beta \in \mathcal{M}_1^+(\mathcal{X})$ , the Optimal Transport problem with entropic regularization is defined as follow [\[39,](#page-13-3) [13,](#page-11-7) [24\]](#page-12-3)

<span id="page-1-1"></span>
$$
\mathrm{OT}_{\varepsilon}(\alpha,\beta) = \min_{\pi \in \Pi(\alpha,\beta)} \int_{\mathcal{X}^2} \mathsf{c}(x,y) \, d\pi(x,y) + \varepsilon \mathrm{KL}(\pi|\alpha \otimes \beta), \qquad \varepsilon \ge 0 \tag{1}
$$

where  $KL(\pi | \alpha \otimes \beta)$  is the *Kullback-Leibler divergence* between the candidate transport plan  $\pi$  and the product distribution  $\alpha \otimes \beta$ , and  $\Pi(\alpha, \beta) = {\pi \in M^1_+(\mathcal{X}^2): P_{1\#} \pi = \alpha, P_{2\#} \pi = \beta}$ , with

 $P_i: \mathcal{X} \times \mathcal{X} \to \mathcal{X}$  the projector onto the *i*-th component and # the push-forward operator. The case  $\varepsilon = 0$  corresponds to the classic Optimal Transport problem introduced by Kantorovich [\[29\]](#page-12-4). In particular, if  $c = ||\cdot||^p$  for  $p \in [1, \infty)$ , then OT<sub>0</sub> is the well-known *p*-Wasserstein distance [\[53\]](#page-14-0). Let  $\varepsilon > 0$ . Then, the dual problem of [\(1\)](#page-1-1), in the sense of Fenchel-Rockafellar, is [\[10,](#page-11-8) [21\]](#page-11-6)

$$
\mathrm{OT}_{\varepsilon}(\alpha,\beta) = \max_{u,v \in \mathcal{C}(\mathcal{X})} \int u(x) \, d\alpha(x) + \int v(y) \, d\beta(y) - \varepsilon \int e^{\frac{u(x) + v(y) - c(x,y)}{\varepsilon}} \, d\alpha(x) d\beta(y),\tag{2}
$$

where  $\mathcal{C}(\mathcal{X})$  denotes the space of real-valued continuous functions on X, endowed with  $\|\cdot\|_{\infty}$ . Let  $\mu \in \mathcal{M}_1^+(\mathcal{X})$ . We denote by  $\mathsf{T}_{\mu} \colon \mathcal{C}(\mathcal{X}) \to \mathcal{C}(\mathcal{X})$  the map such that, for any  $w \in \mathcal{C}(\mathcal{X})$ ,

<span id="page-2-5"></span><span id="page-2-1"></span><span id="page-2-0"></span>
$$
\mathsf{T}_{\mu}(w) \colon x \mapsto -\varepsilon \log \int e^{\frac{w(y) - c(x, y)}{\varepsilon}} \, d\mu(y). \tag{3}
$$

The first order optimality conditions for  $(2)$  are (see [\[21\]](#page-11-6) or Appendix B.2)

$$
u = \mathsf{T}_{\beta}(v) \quad \alpha
$$
- a.e. and  $v = \mathsf{T}_{\alpha}(u) \quad \beta$ - a.e. (4)

Pairs  $(u, v)$  satisfying [\(4\)](#page-2-1) exist [\[30\]](#page-12-5) and are referred to as *Sinkhorn potentials*. They are unique  $(\alpha, \beta)$ - a.e. up to additive constant, i.e.  $(u+t, v-t)$  is also a solution for any  $t \in \mathbb{R}$ . In line with [\[23,](#page-12-2) [21\]](#page-11-6) it will be useful in the following to assume  $(u, v)$  to be the Sinkhorn potentials such that: *i*)  $u(x<sub>o</sub>) = 0$ for an arbitrary anchor point  $x_o \in \mathcal{X}$  and *ii*) [\(4\)](#page-2-1) is satisfied pointwise on the entire domain  $\mathcal{X}$ . Then, *u* is a fixed point of the map  $\mathsf{T}_{\beta\alpha} = \mathsf{T}_{\beta} \circ \mathsf{T}_{\alpha}$  (analogously for *v*). This suggests a fixed point iteration approach to minimize [\(2\)](#page-2-0), yielding the well-known Sinkhorn-Knopp algorithm which has been shown to converge linearly in  $\mathcal{C}(\mathcal{X})$  [\[42,](#page-13-4) [30\]](#page-12-5). We recall a key result characterizing the differentiability of  $\mathrm{OT}_{\varepsilon}$  in terms of the Sinkhorn potentials that will be useful in the following.

<span id="page-2-3"></span>**Proposition 1** (Prop 2 in [\[21\]](#page-11-6)). Let  $\nabla \text{OT}_{\varepsilon}$ :  $\mathcal{M}_1^+(\mathcal{X})^2 \to \mathcal{C}(\mathcal{X})^2$  be such that,  $\forall \alpha, \beta \in \mathcal{M}_+^1(\mathcal{X})$ 

$$
\nabla \overline{\mathrm{OT}}_{\varepsilon}(\alpha, \beta) = (u, v), \quad \text{with} \quad u = \mathsf{T}_{\beta}(v), \quad v = \mathsf{T}_{\alpha}(u) \quad \text{on } \mathcal{X}, \quad u(x_o) = 0. \tag{5}
$$

*Then,*  $\forall \alpha, \alpha', \beta, \beta' \in \mathcal{M}_1^+(\mathcal{X})$ *, the directional derivative of*  $\overline{OT}_{\varepsilon}$  *along*  $(\mu, \nu) = (\alpha' - \alpha, \beta' - \beta)$  *is* 

<span id="page-2-2"></span>
$$
\mathrm{OT}_{\varepsilon}'(\alpha, \beta; \mu, \nu) = \langle \nabla \mathrm{OT}_{\varepsilon}(\alpha, \beta), (\mu, \nu) \rangle = \langle u, \mu \rangle + \langle v, \nu \rangle, \tag{6}
$$

*where*  $\langle w, \rho \rangle = \int w(x) d\rho(x)$  *denotes the canonical pairing between the spaces*  $\mathcal{C}(\mathcal{X})$  *and*  $\mathcal{M}(\mathcal{X})$ *.* 

Note that  $\nabla$ OT<sub> $\varepsilon$ </sub> is not a gradient in the standard sense. In particular note that the directional derivative in [\(6\)](#page-2-2) is not defined for any pair of signed measures, but only along *feasible directions*  $(\alpha' - \alpha, \beta' - \beta).$ 

**Sinkhorn Divergence.** The fast convergence of Sinkhorn-Knopp algorithm makes OT*<sup>ε</sup>* (with  $\varepsilon > 0$ ) preferable to OT<sub>0</sub> from a computational perspective [\[13\]](#page-11-7). However, when  $\varepsilon > 0$  the entropic regularization introduces a bias in the optimal transport problem, since in general  $\mathrm{OT}_{\varepsilon}(\mu,\mu) \neq 0$ . To compensate for this bias, [\[25\]](#page-12-1) introduced the Sinkhorn *divergence*

$$
\mathsf{S}_{\varepsilon} \colon \mathcal{M}_1^+(\mathcal{X}) \times \mathcal{M}_1^+(\mathcal{X}) \to \mathbb{R}, \qquad (\alpha, \beta) \mapsto \mathrm{OT}_{\varepsilon}(\alpha, \beta) - \frac{1}{2}\mathrm{OT}_{\varepsilon}(\alpha, \alpha) - \frac{1}{2}\mathrm{OT}_{\varepsilon}(\beta, \beta), \tag{7}
$$

<span id="page-2-4"></span>which was shown in [\[21\]](#page-11-6) to be nonnegative, bi-convex and to metrize the convergence in law under mild assumptions. We characterize the gradient of  $\mathsf{S}_{\varepsilon}(\cdot,\beta)$  for a fixed  $\beta \in \mathcal{M}_1^+(\mathcal{X})$ , which will be key to derive our optimization algorithm for computing Sinkhorn barycenters.

**Remark 2.** Let  $\nabla_1 \text{OT}_{\varepsilon} : \mathcal{M}_1^+(\mathcal{X})^2 \to \mathcal{C}(\mathcal{X})$  be the first component of  $\nabla \text{OT}_{\varepsilon}$  (informally, the *u* of *the Sinkhorn potentials). As in Prop.* [1,](#page-2-3) *for any*  $\beta \in M_1^+(\mathcal{X})$  *the gradient of*  $S_{\varepsilon}(\cdot,\beta)$  *is* 

$$
\nabla[S_{\varepsilon}(\cdot,\beta)]\colon \mathcal{M}_1^+(\mathcal{X}) \to \mathcal{C}(\mathcal{X}) \qquad \alpha \mapsto \nabla_1 \text{OT}_{\varepsilon}(\alpha,\beta) - \frac{1}{2} \nabla_1 \text{OT}_{\varepsilon}(\alpha,\alpha) = u - p,\tag{8}
$$

*with*  $u = \mathsf{T}_{\beta\alpha}(u)$  *and*  $p = \mathsf{T}_{\alpha}(p)$  *the Sinkhorn potentials of*  $\mathrm{OT}_{\varepsilon}(\alpha, \beta)$  *and*  $\mathrm{OT}_{\varepsilon}(\alpha, \alpha)$  *respectively.* 

<span id="page-3-0"></span>

### **3 Sinkhorn barycenters with Frank-Wolfe**

Given  $\beta_1, \ldots, \beta_m \in \mathcal{M}_1^+(\mathcal{X})$  and  $\omega_1, \ldots, \omega_m \geq 0$  a set of weights such that  $\sum_{j=1}^m \omega_j = 1$ , the main goal of this paper is to solve the following *Sinkhorn barycenter* problem

<span id="page-3-3"></span><span id="page-3-1"></span>
$$
\min_{\alpha \in \mathcal{M}_1^+(\mathcal{X})} \mathsf{B}_{\varepsilon}(\alpha), \quad \text{with} \quad \mathsf{B}_{\varepsilon}(\alpha) = \sum_{j=1}^m \omega_j \mathsf{S}_{\varepsilon}(\alpha, \beta_j). \tag{9}
$$

Although the objective functional  $B_{\varepsilon}$  is convex, its domain  $\mathcal{M}^1_+(\mathcal{X})$  has *empty* interior in the space of finite signed measure  $\mathcal{M}(\mathcal{X})$ . Hence standard notions of Fréchet or Gâteaux differentiability do not apply. This, in principle causes some difficulties in devising optimization methods. To circumvent this issue, in this work we adopt the Frank-Wolfe (FW) algorithm. Indeed, one key advantage of this method is that it is formulated in terms of directional derivatives along feasible directions (i.e., directions that locally remain inside the constraint set). Building upon [\[15,](#page-11-9) [16,](#page-11-10) [19\]](#page-11-11), which study the algorithm in Banach spaces, we show that the "weak" notion of directional differentiability of  $S_{\varepsilon}$  (and hence of B*ε*) in Remark [2](#page-2-4) is sufficient to carry out the convergence analysis. While full details are provided in Appendix A, below we give an overview of the main result.

**Frank-Wolfe in dual Banach spaces.** Let W be a real Banach space with topological dual W<sup>∗</sup> and let  $\mathcal{D} \subset \mathcal{W}^*$  be a nonempty, convex, closed and bounded set. For any  $w \in \mathcal{W}^*$  denote by  $\mathcal{F}_{\mathcal{D}}(w) = \mathbb{R}_+(\mathcal{D} - w)$  the set of feasible direction of  $\mathcal{D}$  at  $w$  (namely  $s = t(w'-w)$  with  $w' \in \mathcal{D}$  and  $t > 0$ ). Let G:  $\mathcal{D} \to \mathbb{R}$  be a convex function and assume that there exists a map  $\nabla$ G:  $\mathcal{D} \to \mathcal{W}$  (not necessarily unique) such that  $\langle \nabla \mathsf{G}(w), s \rangle = \mathsf{G}'(w; s)$  for every  $s \in \mathcal{F}_{\mathcal{D}}(w)$ . In Alg. [1](#page-4-1) we present a method to minimize G. The algorithm is structurally equivalent to the standard FW [\[19,](#page-11-11) [27\]](#page-12-6) and accounts for possible inaccuracies in solving the minimization in step (*i*). This will be key in Sec. [5](#page-6-0) when studying the barycenter problem for  $\beta_j$  with infinite support. The following result (see proof in Appendix A) shows that under the additional assumption that  $\nabla G$  is Lipschitz-continuous and with sufficiently fast decay of the errors, the above procedure converges in value to the minimum of G with rate  $O(1/k)$ . Here diam(D) denotes the diameter of D with respect to the dual norm.

<span id="page-3-2"></span>**Theorem 3.** *Under the assumptions above, suppose in addition that* ∇G *is L-Lipschitz continuous with*  $L > 0$ *. Let*  $(w_k)_{k \in \mathbb{N}}$  *be obtained according to Alg.* [1.](#page-4-1) Then, for every integer  $k \geq 1$ ,

$$
\mathsf{G}(w_k) - \min \mathsf{G} \le \frac{2}{k+2} L \operatorname{diam}(\mathcal{D})^2 + \Delta_k. \tag{10}
$$

**Frank-Wolfe Sinkhorn barycenters.** We show that the barycenter problem [\(9\)](#page-3-1) satisfies the setting and hypotheses of Thm. [3](#page-3-2) and can be thus approached via Alg. [1.](#page-4-1)

<span id="page-4-1"></span>

### **Algorithm 1** Frank-Wolfe in Dual Banach Spaces

**Input:** initial  $w_0 \in \mathcal{D}$ , precision  $(\Delta_k)_{k \in \mathbb{N}} \in \mathbb{R}^{\mathbb{N}}_{++}$ , such that  $\Delta_k(k+2)$  is nondecreasing.

**For**  $k = 0, 1, ...$ Take  $z_{k+1}$  such that  $G'(w_k, z_{k+1} - w_k) \le \min_{z \in \mathcal{D}} G'(w_k, z - w_k) + \frac{\Delta_k}{2}$  $w_{k+1} = w_k + \frac{2}{k+2}(z_{k+1} - w_k)$ 

*Optimization domain.* Let  $W = C(X)$ , with dual  $W^* = M(X)$ . The constraint set  $D = M_1^+(\mathcal{X})$  is convex, closed, and bounded.

*Objective functional.* The objective functional  $G = B_{\varepsilon} : \mathcal{M}_1^+(\mathcal{X}) \to \mathbb{R}$ , defined in [\(9\)](#page-3-1), is convex since it is a convex combination of  $S_{\varepsilon}(\cdot,\beta_j)$ , with  $j=1...m$ . The gradient  $\nabla B_{\varepsilon} \colon \mathcal{M}_1^+(\mathcal{X}) \to \mathcal{C}(\mathcal{X})$  is  $\nabla B_{\varepsilon} = \sum_{j=1}^{m} \omega_j \nabla S_{\varepsilon}(\cdot, \beta_j)$ , where  $\nabla \tilde{S}_{\varepsilon}(\cdot, \beta_j)$  is given in Remark [2.](#page-2-4)

*Lipschitz continuity of the gradient..* This is the most critical condition and is addressed in the following theorem.

<span id="page-4-2"></span>**Theorem 4.** *The gradient* ∇OT*<sup>ε</sup> defined in Prop. [1](#page-2-3) is Lipschitz continuous. In particular, the first component*  $\nabla_1 \overline{OT}_{\varepsilon}$  *is*  $2\varepsilon e^{3D/\varepsilon}$ -Lipschitz continuous, i.e., for every  $\alpha, \alpha', \beta, \beta' \in \mathcal{M}_1^+(\mathcal{X})$ ,

$$
\|u - u'\|_{\infty} = \|\nabla_1 \text{OT}_{\varepsilon}(\alpha, \beta) - \nabla_1 \text{OT}_{\varepsilon}(\alpha', \beta')\|_{\infty} \le 2\varepsilon e^{3\mathsf{D}/\varepsilon} \left( \|\alpha - \alpha'\|_{TV} + \|\beta - \beta'\|_{TV} \right),\tag{11}
$$

where  $D = \sup_{x,y \in \mathcal{X}} c(x,y)$ ,  $u = \mathsf{T}_{\beta\alpha}(u)$ ,  $u' = \mathsf{T}_{\beta',\alpha'}(u')$ , and  $u(x_o) = u'(x_o) = 0$ . Moreover, it follows *from* [\(8\)](#page-3-3) *that*  $\nabla$ **S**<sub> $\epsilon$ </sub>( $\cdot$ , $\beta$ ) *is* 6 $\epsilon e^{3D/\epsilon}$ *-Lipschitz continuous. The same holds for*  $\nabla$ **B** $_{\epsilon}$ *.* 

Thm. [4](#page-4-2) is one of the main contributions of this paper. It can be rephrased by saying that the operator that maps a pair of distributions to their Sinkhorn potentials is Lipschitz continuous. This result is significantly deeper than the one given in [\[20,](#page-11-3) Lemma 1], which establishes the Lipschitz continuity of the gradient in the *semidiscrete* case. The proof (given in Appendix D) relies on non-trivial tools from Perron-Frobenius theory for Hilbert's metric [\[32\]](#page-12-7), which is a well-established framework to study Sinkhorn potentials [\[39\]](#page-13-3). We believe this result is interesting not only for the application of FW to the Sinkhorn barycenter problem, but also for further understanding regularity properties of entropic optimal transport.

<span id="page-4-0"></span>

## **4 Algorithm: practical Sinkhorn barycenters**

According to Sec. [3,](#page-3-0) FW is a valid approach to tackle the barycenter problem [\(9\)](#page-3-1). Here we describe how to implement in practice the abstract procedure of Alg. [1](#page-4-1) to obtain a sequence of distributions  $(\alpha_k)_{k \in \mathbb{N}}$  minimizing  $B_{\varepsilon}$ . A main challenge in this sense resides in finding a minimizing feasible direction for  $B'_{\varepsilon}(\alpha_k; \mu - \alpha_k) = \langle \nabla B_{\varepsilon}(\alpha_k), \mu - \alpha_k \rangle$ . According to Remark [2,](#page-2-4) this amounts to solve

<span id="page-4-3"></span>
$$
\mu_{k+1} \in \underset{\mu \in \mathcal{M}_1^+(\mathcal{X})}{\text{argmin}} \sum_{j=1}^m \omega_j \langle u_{jk} - p_k, \mu \rangle \quad \text{where} \quad u_{jk} - p_k = \nabla \mathsf{S}_{\varepsilon}[(\cdot, \beta_j)](\alpha_k), \quad (12)
$$

with  $p_k = \nabla_1 O T_\varepsilon(\alpha_k, \alpha_k)$  not depending on *j*. In general [\(12\)](#page-4-3) would entail a minimization over the set of all probability distributions on  $\mathcal X$ . However, since the objective functional is linear in  $\mu$ 

and  $\mathcal{M}_1^+(\mathcal{X})$  is a weakly-\* compact convex set, we can apply Bauer maximum principle (see e.g., [\[3,](#page-10-4) Thm. 7.69]). Hence, solutions are achieved at the extreme points of the optimization domain, namely Dirac's deltas for the case of  $\mathcal{M}_1^+(\mathcal{X})$  [\[11,](#page-11-12) p. 108]. Now, denote by  $\delta_x \in \mathcal{M}_1^+(\mathcal{X})$  the Dirac's delta centered at  $x \in \mathcal{X}$ . We have  $\langle w, \delta_x \rangle = w(x)$  for every  $w \in \mathcal{C}(\mathcal{X})$ . Hence [\(12\)](#page-4-3) is equivalent to

$$
\mu_{k+1} = \delta_{x_{k+1}}
$$
 with  $x_{k+1} \in \operatorname*{argmin}_{x \in \mathcal{X}} \sum_{j=1}^{m} \omega_j (u_{jk}(x) - p_k(x)).$  (13)

Once the new support point  $x_{k+1}$  $x_{k+1}$  $x_{k+1}$  has been obtained, the update in Alg. 1 corresponds to

<span id="page-5-1"></span><span id="page-5-0"></span>
$$
\alpha_{k+1} = \alpha_k + \frac{2}{k+2}(\delta_{x_{k+1}} - \alpha_k) = \frac{k}{k+2}\alpha_k + \frac{2}{k+2}\delta_{x_{k+1}}.\tag{14}
$$

In particular, if FW is initialized with a distribition with finite support, say  $\alpha_0 = \delta_{x_0}$  for some  $x_0 \in \mathcal{X}$ , then also every further iterate  $\alpha_k$  will have at most  $k+1$  support points. According to [\(13\)](#page-5-0), the inner optimization for FW consists in minimizing the functional  $x \mapsto \sum_{j=1}^{m} \omega_j (u_{jk}(x) - p_k(x))$  over X. In practice, having access to such functional poses already a challenge, since it requires computing the Sinkhorn potentials  $u_{jk}$  and  $p_k$ , which are infinite dimensional objects. Below we discuss how to estimate these potentials when the  $\beta_j$  have finite support. We then address the general setting.

Computing  $\nabla_1 \text{OT}_{\varepsilon}$  for probability distributions with finite support. Let  $\alpha, \beta \in \mathcal{M}_1^+(\mathcal{X})$ , with  $\beta = \sum_{i=1}^n b_i \delta_{y_i}$  a probability measure with finite support, with  $\mathbf{b} = (b_i)_{i=1}^n$  nonnegative weights summing up to 1. It is useful to identify  $\beta$  with the pair  $(\mathbf{Y}, \mathbf{b})$ , where  $\mathbf{Y} \in \mathbb{R}^{\tilde{d} \times n}$  is the matrix with *i*-th column equal to *y*<sub>*i*</sub>. Let now  $(u, v) \in C(X)^2$  be the pair of Sinkhorn potentials associated to  $\alpha$ and  $\beta$  in Prop. [1,](#page-2-3) recall that  $u = \mathsf{T}_{\beta}(v)$ . Denote by  $\mathsf{v} \in \mathbb{R}^n$  the *evaluation vector* of the Sinkhorn potential *v*, with *i*-th entry  $v_i = v(y_i)$ . According to the definition of  $\mathsf{T}_\beta$  in [\(3\)](#page-2-5), for any  $x \in \mathcal{X}$ 

$$
[\nabla_1 \mathrm{OT}_{\varepsilon}(\alpha, \beta)](x) = u(x) = [\mathsf{T}_{\beta}(v)](x) = -\varepsilon \log \sum_{i=1}^n e^{(\mathsf{v}_i - \mathsf{c}(x, y_i))/\varepsilon} b_i,
$$
(15)

since the integral  $\mathsf{T}_{\beta}(v)$  reduces to a sum over the support of  $\beta$ . Hence, the gradient of OT<sub>*ε*</sub> (i.e. the potential *u*), *is uniquely characterized in terms of the finite dimensional vector* v *collecting the values of the potential v on the support of*  $\beta$ . We refer as SINKHORNGRADIENT to the routine which associates to each triplet  $(\mathbf{Y}, \mathbf{b}, \mathbf{v})$  the map  $x \mapsto -\varepsilon \log \sum_{i=1}^{n} e^{(\mathbf{v}_i - \mathbf{c}(x, y_i))/\varepsilon} b_i$ .

**Sinkhorn barycenters: finite case.** Alg. [2](#page-6-1) summarizes FW applied to the barycenter problem [\(9\)](#page-3-1) when the  $\beta_j$ 's have finite support. Starting from a Dirac's delta  $\alpha_0 = \delta_{x_0}$ , at each iteration  $k \in \mathbb{N}$  the algorithm proceeds by: *i*) finding the corresponding evaluation vectors  $v_j$ 's and **p** of the Sinkhorn potentials for  $\overline{\text{OT}}_{\varepsilon}(\alpha_k, \beta_j)$  and  $\overline{\text{OT}}_{\varepsilon}(\alpha_k, \alpha_k)$  respectively, via the routine SINKHORNKNOPP (see [\[13,](#page-11-7) [21\]](#page-11-6) or Alg. [B.2\)](#page-6-1). This is possible since both  $\beta_j$  and  $\alpha_k$  have finite support and therefore the problem of approximating the evaluation vectors  $v_j$  and  $p$  reduces to an optimization problem over finite vector spaces that can be efficiently solved [\[13\]](#page-11-7); *ii*) obtain the gradients  $u_j = \nabla_1 O T_{\varepsilon}(\alpha_k, \beta_j)$ and  $p = \nabla_1 \text{OT}_{\varepsilon}(\alpha_k, \alpha_k)$  via SINKHORNGRADIENT; *iii*) minimize  $\varphi: x \mapsto \sum_{j=1}^n \omega_j u_j(x) - p(x)$  over X to find a new point  $x_{k+1}$  (we comment on this meta-routine MINIMIZE below); *iv*) finally update the support and weights of  $\alpha_k$  according to [\(14\)](#page-5-1) to obtain the new iterate  $\alpha_{k+1}$ .

A key feature of Alg. [2](#page-6-1) is that the support of the candidate barycenter is updated *incrementally* by adding at most one point at each iteration, a procedure similar in flavor to the kernel herding strategy in  $[5, 31]$  $[5, 31]$  $[5, 31]$ . This contrasts with previous methods for barycenter estimation  $[14, 6, 51, 20]$  $[14, 6, 51, 20]$  $[14, 6, 51, 20]$  $[14, 6, 51, 20]$  $[14, 6, 51, 20]$  $[14, 6, 51, 20]$  $[14, 6, 51, 20]$ , <span id="page-6-1"></span>**Algorithm 2 SINKHORN BARYCENTER** 

**Input:**  $\beta_j = (\mathbf{Y}_j, \mathbf{b}_j)$  with  $\mathbf{Y}_j \in \mathbb{R}^{d \times n_j}$ ,  $\mathbf{b}_j \in \mathbb{R}^{n_j}$ ,  $\omega_j > 0$  for  $j = 1, \ldots, m$ ,  $x_0 \in \mathbb{R}^d$ ,  $\varepsilon > 0$ ,  $K \in \mathbb{N}$ . **Initialize:**  $\alpha_0 = (\mathbf{X}_0, \mathbf{a}_0)$  with  $\mathbf{X}_0 = x_0, \mathbf{a}_0 = 1$ . **For**  $k = 0, 1, \ldots, K - 1$  $p = \text{SINKHORNKNOPP}(\alpha_k, \alpha_k, \varepsilon)$  $p(\cdot) = \text{SINKHORNGRADIENT}(\mathbf{X}_k, \mathsf{a}_k, \mathsf{p})$ **For**  $j = 1, \ldots, m$  $v_j =$ SINKHORNKNOPP $(\alpha_k, \beta_j, \varepsilon)$  $u_j(\cdot) = \text{SinkHORNGRADIENT}(\mathbf{Y}_j, \mathbf{b}_j, \mathbf{v}_j)$ Let  $\varphi: x \mapsto \sum_{j=1}^m \omega_j u_j(x) - p(x)$  $x_{k+1} =$  MINIMIZE( $\varphi$ )  $\mathbf{X}_{k+1} = [\mathbf{X}_k, x_{k+1}]$  and  $\mathbf{a}_{k+1} = \frac{1}{k+2} [k \mathbf{a}_k, 2]$  $\alpha_{k+1} = (\mathbf{X}_{k+1}, \mathsf{a}_{k+1})$ **Return:** *α<sup>K</sup>*

which require the support set, or at least its cardinality, to be fixed beforehand. However, indentifying the new support point requires solving the nonconvex problem  $(13)$ , a task addressed by the metaroutine MINIMIZE. This problem is typically smooth (e.g., a linear combination of Gaussians when  $c(x, y) = ||x - y||^2$  and first or second order nonlinear optimization methods can be adopted to find stationary points. We note that all free-support methods in the literature for barycenter estimation are also affected by nonconvexity since they typically require solving a bi-convex problem (alternating minimization between support points and weights) which is not jointly convex [\[14,](#page-11-0) [12\]](#page-11-2). We conclude by observing that if we restrict to the setting of  $[51, 20]$  $[51, 20]$  $[51, 20]$  with fixed support set, then MINIMIZE can be solved exactly by evaluating the functional in [\(13\)](#page-5-0) on each candidate support point.

**Sinkhorn barycenters: general case.** When the  $\beta_j$ 's have infinite support, it is not possible to apply Sinkhorn-Knopp in practice. In line with [\[23,](#page-12-2) [51\]](#page-13-2), we can randomly sample empirical distributions  $\hat{\beta}_j = \frac{1}{n}$  $\frac{1}{n}\sum_{i=1}^{n} \delta_{x_{ij}}$  $\frac{1}{n}\sum_{i=1}^{n} \delta_{x_{ij}}$  $\frac{1}{n}\sum_{i=1}^{n} \delta_{x_{ij}}$  from each  $\beta_j$  and apply Sinkhorn-Knopp to  $(\alpha_k, \hat{\beta}_j)$  in Alg. 1 rather than to the ideal pair  $(\alpha_k, \beta_i)$ . This strategy is motivated by [\[21,](#page-11-6) Prop 13], where it was shown that Sinkhorn potentials vary continuously with the input measures. However, it opens two questions: *i*) whether this approach is theoretically justified (consistency) and *ii*) how many points should we sample from each  $\beta_j$  to ensure convergence (rates). We answer these questions in Thm. [7](#page-7-0) in the next section.

<span id="page-6-0"></span>

### **5 Convergence analysis**

We finally address the convergence of FW applied to both the finite and infinite settings discussed in Sec. [4.](#page-4-0) We begin by considering the finite setting.

<span id="page-6-2"></span>**Theorem 5.** *Suppose that*  $\beta_1, \ldots, \beta_m \in \mathcal{M}_1^+(\mathcal{X})$  *have finite support and let*  $\alpha_k$  *be the k-th iterate of Alg. [2](#page-6-1) applied to* [\(9\)](#page-3-1)*. Then,*

<span id="page-6-3"></span>
$$
\mathsf{B}_{\varepsilon}(\alpha_k) - \min_{\alpha \in \mathcal{M}_1^+(\mathcal{X})} \mathsf{B}_{\varepsilon}(\alpha) \le \frac{48 \, \varepsilon \, e^{3\mathsf{D}/\varepsilon}}{k+2}.\tag{16}
$$

The result follows by the convergence result of FW in Thm. [3](#page-3-2) applied with the Lipschitz constant computed in Thm. [4,](#page-4-2) and recalling that  $\text{diam}(\mathcal{M}_1^+(\mathcal{X})) = 2$  with respect to the Total Variation. We note that Thm. [5](#page-6-2) assumes SINKHORNKNOPP and MINIMIZE in Alg. [2](#page-6-1) to yield exact solutions. In Appendix  $\overline{D}$  we comment how approximation errors in this context affect the bound in [\(16\)](#page-6-3).

**General setting.** As mentioned in Sec. [4,](#page-4-0) when the  $\beta_j$ 's are not finitely supported we adopt a sampling approach. More precisely we propose to *replace* in Alg. [2](#page-6-1) the ideal Sinkhorn potentials of the pairs  $(\alpha, \beta_j)$  with those of  $(\alpha, \beta_j)$ , where each  $\beta_j$  is an empirical measure randomly sampled from  $\beta_j$ . In other words we are performing the FW algorithm with a (possibly rough) approximation of the correct gradient of  $B_{\varepsilon}$ . According to Thm. [3,](#page-3-2) FW allows errors in the gradient estimation (which are captured into the precision  $\Delta_k$  in the statement). To this end, the following result *quantifies* the approximation error between  $\nabla_1 O T_{\varepsilon}(\cdot, \beta)$  and  $\nabla_1 O T_{\varepsilon}(\cdot, \beta)$  in terms of the sample size of  $\hat{\beta}$ .

<span id="page-7-1"></span>**Theorem 6** (Sample Complexity of Sinkhorn Potentials). Suppose that  $c \in C^{s+1}(\mathcal{X} \times \mathcal{X})$  with  $s > d/2$ . *Then, there exists a constant*  $\bar{\mathbf{r}} = \bar{\mathbf{r}}(\mathcal{X}, \mathbf{c}, d)$  *such that for any*  $\alpha, \beta \in \mathcal{M}_1^+(\mathcal{X})$  *and any empirical measure*  $\hat{\beta}$  *of a set of n points independently sampled from*  $\beta$ *, we have, for every*  $\tau \in (0,1]$ 

<span id="page-7-2"></span>
$$
||u - u_n||_{\infty} = ||\nabla_1 \text{OT}_{\varepsilon}(\alpha, \beta) - \nabla_1 \text{OT}_{\varepsilon}(\alpha, \hat{\beta})||_{\infty} \le \frac{8\varepsilon \bar{r}e^{3\text{D}/\varepsilon} \log \frac{3}{\tau}}{\sqrt{n}} \tag{17}
$$

with probability at least  $1 - \tau$ , where  $u = \mathsf{T}_{\beta\alpha}(u)$ ,  $u_n = \mathsf{T}_{\hat{\beta}\alpha}(u_n)$  and  $u(x_o) = u_n(x_o) = 0$ .

Thm. [6](#page-7-1) is one of the main results of this work. We point out that it *cannot* be obtained by means of the Lipschitz continuity of  $\nabla_1 O T_\varepsilon$  in Thm. [4,](#page-4-2) since empirical measures do not converge in  $\lVert \cdot \rVert_{TV}$  to their target distribution [\[17\]](#page-11-13). Instead, the proof consists in considering the weaker *Maximum Mean Discrepancy (MMD)* metric associated to a universal kernel [\[47\]](#page-13-5), which metrizes the topology of the convergence in law of  $\mathcal{M}_1^+(\mathcal{X})$  [\[48\]](#page-13-6). Empirical measures converge in MMD metric to their target distribution [\[47\]](#page-13-5). Therefore, by proving the Lipschitz continuity of  $\nabla_1 O T_\varepsilon$  with *respect to* MMD in Prop. E.5 we are able to conclude that  $(17)$  holds. This latter result relies on higher regularity properties of Sinkhorn potentials, which have been recently shown [\[23,](#page-12-2) Thm.2] to be uniformly bounded in Sobolev spaces under the additional assumption  $c \in C^{s+1}(\mathcal{X} \times \mathcal{X})$ . For sufficiently large *s*, the Sobolev norm is in duality with the MMD [\[36\]](#page-12-9) and allows us to derive the required Lipschitz continuity. We conclude noting that while [\[23\]](#page-12-2) studied the sample complexity of the Sinkhorn *divergence*, Thm. [6](#page-7-1) is a sample complexity result for Sinkhorn *potentials*. In this sense, we observe that the constants appearing in the bound are tightly related to those in [\[23,](#page-12-2) Thm.3] and have similar behavior with respect to  $\varepsilon$ . We can now study the convergence of FW in continuous settings.

<span id="page-7-0"></span>**Theorem 7.** Suppose that  $c \in C^{s+1}(\mathcal{X} \times \mathcal{X})$  with  $s > d/2$ . Let  $n \in \mathbb{N}$  and  $\hat{\beta}_1, \ldots, \hat{\beta}_m$  be empirical *distributions with n support points, each independently sampled from*  $\beta_1, \ldots, \beta_m$ *. Let*  $\alpha_k$  *be the k-th iterate of Alg.* [2](#page-6-1) applied to  $\hat{\beta}_1, \ldots, \hat{\beta}_m$ . Then for any  $\tau \in (0,1]$ , the following holds with probability *larger than*  $1 - \tau$ 

$$
\mathsf{B}_{\varepsilon}(\alpha_{k}) - \min_{\alpha \in \mathcal{M}_{1}^{+}(\mathcal{X})} \mathsf{B}_{\varepsilon}(\alpha) \leq \frac{64 \bar{\mathsf{r}} \varepsilon e^{3\mathsf{D}/\varepsilon} \log \frac{3}{\tau}}{\min(k, \sqrt{n})}.
$$
\n(18)

The proof is shown in Appendix E. A consequence of Thm.  $7$  is that the accuracy of FW depends simultaneously on the number of iterations and the sample size used in the approximation of the gradients: by choosing  $n = k^2$  we recover the  $O(1/k)$  rate of the finite setting, while for  $n = k$  we have a rate of  $O(k^{-1/2})$ , which is reminiscent of typical sample complexity results, highlighting the statistical nature of the problem.

<span id="page-8-2"></span>Image /page/8/Figure/0 description: The image displays a grid of nine subplots, each containing a stylized representation of an ellipse or a pair of ellipses. The central subplot is highlighted with a red border and shows two concentric, slightly irregular circles with a gradient fill from light blue to dark blue. The other subplots feature various orientations and overlaps of blue elliptical outlines. To the right of the grid are two larger subplots, each depicting a scatter plot with a density contour overlay. The first larger subplot shows a roughly circular cluster of blue dots with a contour map indicating higher density towards the center. The second larger subplot displays an elongated, diagonal cluster of blue dots, also with a density contour map, suggesting a strong correlation between the two variables.

Figure 1: Ellipses Figure 2: Barycenters of Gaussians

**Remark 8** (Incremental Sampling)**.** *The above strategy requires sampling the empirical distributions for*  $\beta_1, \ldots, \beta_m$  *beforehand. A natural question is whether it is be possible to do this* incrementally,  $\hat{p}_j$  *sampling new points and updating*  $\hat{\beta}_j$  *accordingly, as the number of* FW *iterations increase. To this end, one can perform an intersection bound and see that this strategy is still consistent, but the bound in Thm. [7](#page-7-0) worsens the logarithmic term, which becomes*  $\log(3mk/\tau)$ *.* 

<span id="page-8-0"></span>

## **6 Experiments**

In this section we show the performance of our method in a range of experiments  $<sup>1</sup>$  $<sup>1</sup>$  $<sup>1</sup>$ .</sup>

**Discrete measures: barycenter of nested ellipses.** We compute the barycenter of 30 randomly generated nested ellipses on a  $50 \times 50$  grid similarly to [\[14\]](#page-11-0). We interpret each image as a probability distribution in 2D. The cost matrix is given by the squared Euclidean distances between pixels. Fig. [1](#page-8-2) reports 8 samples of the input ellipses and the barycenter obtained with Alg. [2.](#page-6-1) It shows qualitatively that our approach captures key geometric properties of the input measures.

**Continuous measures: barycenter of Gaussians.** We compute the barycenter of 5 Gaussian distributions  $\mathcal{N}(m_i, C_i)$   $i = 1, \ldots, 5$  in  $\mathbb{R}^2$ , with mean  $m_i \in \mathbb{R}^2$  and covariance  $C_i$  randomly generated. We apply Alg. [2](#page-6-1) to empirical measures obtained by sampling  $n = 500$  points from each  $\mathcal{N}(m_i, C_i)$ ,  $i = 1, \ldots, 5$ . Since the (Wasserstein) barycenter of Gaussian distributions can be estimated accurately (see [\[2\]](#page-10-0)), in Fig. [2](#page-8-2) we report both the output of our method (as a scatter plot) and the true Wasserstein barycenter (as level sets of its density). We observe that our estimator recovers both the mean and covariance of the target barycenter. See the supplementary material for additional experiments also in the case of mixtures of Gaussians.

**Image "compression" via distribution matching.** Similarly to [\[12\]](#page-11-2), we test Alg. [2](#page-6-1) in the special case of computing the "barycenter" of a single measure  $\beta \in M^1_+(\mathcal{X})$ . While the solution of this problem is the distribution  $\beta$  itself, we can interpret the intermediate iterates  $\alpha_k$  of Alg. [2](#page-6-1) as compressed version of the original measure. In this sense *k* would represent the level of compression since  $\alpha_k$ is supported on *at most k* points. Fig. [3](#page-9-0) (Right) reports iteration  $k = 5000$  of Alg. [2](#page-6-1) applied to the 140 × 140 image in Fig. [3](#page-9-0) (Left) interpreted as a probability measure  $\beta$  in 2D. We note that the number of points in the support is  $\sim$  3900: indeed, Alg. [2](#page-6-1) selects the most relevant support

<span id="page-8-1"></span><sup>1</sup> <https://github.com/GiulsLu/Sinkhorn-Barycenters>

<span id="page-9-0"></span>Image /page/9/Picture/0 description: The image displays three distinct sections. The leftmost section is a black and white close-up portrait of a cheetah's face, focusing on its eyes, nose, and ears. To the right of the cheetah portrait is a pixelated, black and white rendering of a cheetah's face, appearing as a low-resolution sketch. The rightmost section contains a grid of red numbers, arranged in three rows and three columns. The numbers are: 4, 2, 9, 7, 3, 1, 0, 5, 2.

Figure 3: (left) original image 140x140 pixels, sample (right) Figure 4: *k*-means

points points multiple times to accumulate the right amount of mass on each of them (darker color = higher weight). This shows that FW tends to greedily search for the most relevant support points, prioritizing those with higher weight

**k-means on MNIST digits.** We tested our algorithm on a *k*-means clustering experiment. We consider a subset of 500 random images from the MNIST dataset. Each image is suitably normalized to be interpreted as a probability distribution on the grid of  $28 \times 28$  pixels with values scaled between 0 and 1. We initialize 20 centroids according to the *k*-means++ strategy [\[4\]](#page-10-5). Fig. [4](#page-9-0) deipcts the 20 centroids obtained by performing *k*-means with Alg. [2.](#page-6-1) We see that the structure of the digits is successfully detected, recovering also minor details (e.g. note the difference between the 2 centroids).

**Real data: Sinkhorn propagation of weather data.** We consider the problem of Sinkhorn *propagation* similar to the one in [\[46\]](#page-13-7). The goal is to predict the distribution of missing measurements for weather stations in the state of Texas, US by "propagating" measurements from neighboring stations in the network. The problem can be formulated as minimizing the functional  $\sum_{(v,u)\in\mathcal{V}}\omega_{uv}\mathsf{S}_{\varepsilon}(\rho_v,\rho_u)$ over the set  $\{\rho_v \in \mathcal{M}_1^+(\mathbb{R}^2) | v \in V_0\}$  with:  $V_0 \subset V$  the subset of stations with missing measurements,  $G = (V, \mathcal{E})$  the whole graph of the stations network,  $\omega_{uv}$  a weight inversely proportional to the geographical distance between two vertices/stations  $u, v \in V$ . The variable  $\rho_v \in \mathcal{M}_1^+(\mathbb{R}^2)$  denotes the distribution of measurements at station *v* of daily *temperature* and *atmospheric pressure* over one year. This is a generalization of the barycenter problem  $(9)$  (see also [\[39\]](#page-13-3)). From the total  $|\mathcal{V}| = 115$ , we randomly select  $10\%, 20\%$  $10\%, 20\%$  $10\%, 20\%$  or  $30\%$  to be *available* stations, and use Alg. 2 to propagate their measurements to the remaining "missing" ones. We compare our approach (FW) with the Dirichlet (DR) baseline in [\[46\]](#page-13-7) in terms of the error  $d(C_T, \hat{C})$  between the covariance matrix  $C_T$  of the groundtruth distribution and that of the predicted one. Here  $d(A, B) = ||log(A^{-1/2}BA^{-1/2})||$  is the geodesic distance on the cone of positive definite matrices. The average prediction errors are: 2*.*07 (FW), 2*.*24 (DR) for 10%, 1*.*47 (FW), 1*.*89(DR) for 20% and 1*.*3 (FW), 1*.*6 (DR) for 30%. Fig. [5](#page-10-6) qualitatively reports the improvement  $\Delta = d(C_T, C_{DR}) - d(C_T, C_{FW})$  of our method on individual stations: a higher color intensity corresponds to a wider gap in our favor between prediction errors, from light green ( $\Delta \sim 0$ ) to red ( $\Delta \sim 2$ ). Our approach tends to propagate the distributions to missing locations with higher accuracy.

<span id="page-10-6"></span>Image /page/10/Figure/0 description: The image displays three maps of Texas, each with a different distribution of colored dots representing various locations. The maps are arranged side-by-side. Key cities like El Paso, Santa Fe, Albuquerque, N.M., Amarillo, Oklahoma City, Tulsa, Dallas, Shreveport, Austin, San Antonio, Houston, Corpus Christi, Laredo, Matamoros, and Monterrey are labeled on the maps. The dots are colored black, light blue, orange, and yellow. The first map shows a concentration of black and orange dots in the Dallas area, with scattered light blue and yellow dots across the state. The second map has a similar distribution but with more orange dots in the central and eastern parts of Texas. The third map also shows a pattern of black and orange dots clustered around Dallas, with a few scattered orange and yellow dots elsewhere.

Figure 5: From Left to Right: propagation of weather data with 10%*,* 20% and 30% stations with available measurements (see text).

<span id="page-10-3"></span>

## **7 Conclusion**

We proposed a Frank-Wolfe-based algorithm to find the Sinkhorn barycenter of probability distributions with either finitely or infinitely many support points. Our algorithm belongs to the family of barycenter methods with free support since it adaptively identifies support points rather than fixing them a-priori. In the finite settings, we were able to guarantee convergence of the proposed algorithm by proving the Lipschitz continuity of gradient of the barycenter functional in the Total Variation sense. Then, by studying the sample complexity of Sinkhorn potential estimation, we proved the convergence of our algorithm also in the infinite case. We empirically assessed our method on a number of synthetic and real datasets, showing that it exhibits good qualitative and quantitative performance. While in this work we have considered FW iterates that are a convex combination of Dirac's delta, models with higher regularity (e.g. mixture of Gaussians) might be more suited to approximate the barycenter of distributions with smooth density. Hence, future work will investigate how the perspective adopted in this work could be extended also to other barycenter estimators.

## **References**

- [1] R. A. Adams and J. J. F. Fournier. *Sobolev spaces*. Elsevier, 2003.
- <span id="page-10-0"></span>[2] M. Agueh and G. Carlier. Barycenters in the Wasserstein space. *SIAM J. Math. Analysis*, 43(2):904–924, 2011.
- <span id="page-10-4"></span>[3] K. Aliprantis, C. D. and Border. *Infinite Dimensional Analysis: a Hitchhiker's guide*. Springer Science & Business Media, 2006.
- <span id="page-10-5"></span>[4] David Arthur and Sergei Vassilvitskii. K-means++: The advantages of careful seeding. In *Proceedings of the Eighteenth Annual ACM-SIAM Symposium on Discrete Algorithms*, SODA '07, pages 1027–1035, Philadelphia, PA, USA, 2007. Society for Industrial and Applied Mathematics.
- <span id="page-10-2"></span>[5] Francis Bach, Simon Lacoste-Julien, and Guillaume Obozinski. On the equivalence between herding and conditional gradient algorithms. *arXiv preprint arXiv:1203.4523*, 2012.
- <span id="page-10-1"></span>[6] Jean-David Benamou, Guillaume Carlier, Marco Cuturi, Luca Nenna, and Gabriel Peyré. Iterative bregman projections for regularized transportation problems. *SIAM J. Scientific Computing*, 37(2), 2015.

- [7] J Frédéric Bonnans and Alexander Shapiro. *Perturbation analysis of optimization problems*. Springer Science & Business Media, 2013.
- <span id="page-11-5"></span>[8] Nicholas Boyd, Geoffrey Schiebinger, and Benjamin Recht. The alternating descent conditional gradient method for sparse inverse problems. *SIAM Journal on Optimization*, 27(2):616–639, 2017.
- <span id="page-11-4"></span>[9] Kristian Bredies and Hanna Katriina Pikkarainen. Inverse problems in spaces of measures. *ESAIM: Control, Optimisation and Calculus of Variations*, 19(1):190–218, 2013.
- <span id="page-11-8"></span>[10] Lenaic Chizat, Gabriel Peyré, Bernhard Schmitzer, and François-Xavier Vialard. Scaling algorithms for unbalanced optimal transport problems. *Mathematics of Computation*, 87(314):2563– 2609, 2018.
- <span id="page-11-12"></span>[11] G. Chouquet. *Lectures on Analysis, Vol. II*. W. A. Bejamin, Inc., Reading, MA, USA., 1969.
- <span id="page-11-2"></span>[12] S. Claici, E. Chien, and J. Solomon. Stochastic Wasserstein Barycenters. *ArXiv e-prints*, February 2018.
- <span id="page-11-7"></span>[13] Marco Cuturi. Sinkhorn distances: Lightspeed computation of optimal transport. In *Advances in Neural Information Processing Systems*, pages 2292–2300, 2013.
- <span id="page-11-0"></span>[14] Marco Cuturi and Arnaud Doucet. Fast computation of wasserstein barycenters. In Eric P. Xing and Tony Jebara, editors, *Proceedings of the 31st International Conference on Machine Learning*, volume 32 of *Proceedings of Machine Learning Research*, pages 685–693, Bejing, China, 22–24 Jun 2014. PMLR.
- <span id="page-11-9"></span>[15] V. F. Demyanov and A. M. Rubinov. The minimization of smooth convex functional on a convex set. *J. SIAM Control.*, 5(2):280–294, 1967.
- <span id="page-11-10"></span>[16] V. F. Demyanov and A. M. Rubinov. Minimization of functionals in normed spaces. *J. SIAM Control.*, 6(1):73–88, 1968.
- <span id="page-11-13"></span>[17] Luc Devroye, Laszlo Gyorfi, et al. No empirical probability measure can converge in the total variation sense for all distributions. *The Annals of Statistics*, 18(3):1496–1499, 1990.
- <span id="page-11-1"></span>[18] Pierre Dognin, Igor Melnyk, Youssef Mroueh, Jerret Ross, Cicero Dos Santos, and Tom Sercu. Wasserstein barycenter model ensembling. *arXiv preprint arXiv:1902.04999*, 2019.
- <span id="page-11-11"></span>[19] Joseph C Dunn and S Harshbarger. Conditional gradient algorithms with open loop step size rules. *Journal of Mathematical Analysis and Applications*, 62(2):432–444, 1978.
- <span id="page-11-3"></span>[20] Pavel Dvurechenskii, Darina Dvinskikh, Alexander Gasnikov, Cesar Uribe, and Angelia Nedich. Decentralize and randomize: Faster algorithm for wasserstein barycenters. In S. Bengio, H. Wallach, H. Larochelle, K. Grauman, N. Cesa-Bianchi, and R. Garnett, editors, *Advances in Neural Information Processing Systems 31*, pages 10760–10770. Curran Associates, Inc., 2018.
- <span id="page-11-6"></span>[21] Jean Feydy, Thibault Séjourné, François-Xavier Vialard, Shun-Ichi Amari, Alain Trouvé, and Gabriel Peyré. Interpolating between optimal transport and mmd using sinkhorn divergences. *International Conference on Artificial Intelligence and Statistics (AIStats)*, 2019.

- [22] J. Franklin and J. Lorenz. On the scaling of multidimensional matrices. *Linear Algebra and its Applications*, 114:717–735, 1989.
- <span id="page-12-2"></span>[23] Aude Genevay, Lénaic Chizat, Francis Bach, Marco Cuturi, and Gabriel Peyré. Sample complexity of sinkhorn divergences. *International Conference on Artificial Intelligence and Statistics (AIStats)*, 2018.
- <span id="page-12-3"></span>[24] Aude Genevay, Marco Cuturi, Gabriel Peyré, and Francis Bach. Stochastic optimization for large-scale optimal transport. In D. D. Lee, M. Sugiyama, U. V. Luxburg, I. Guyon, and R. Garnett, editors, *Advances in Neural Information Processing Systems 29*, pages 3440–3448. Curran Associates, Inc., 2016.
- <span id="page-12-1"></span>[25] Aude Genevay, Gabriel Peyré, and Marco Cuturi. Learning generative models with sinkhorn divergences. In *International Conference on Artificial Intelligence and Statistics*, pages 1608–1617, 2018.
- <span id="page-12-0"></span>[26] Alexandre Gramfort, Gabriel Peyré, and Marco Cuturi. Fast optimal transport averaging of neuroimaging data. In *International Conference on Information Processing in Medical Imaging*, pages 261–272. Springer, 2015.
- <span id="page-12-6"></span>[27] Martin Jaggi. Revisiting frank-wolfe: Projection-free sparse convex optimization. In *ICML (1)*, pages 427–435, 2013.
- [28] Martin Jaggi. Revisiting Frank-Wolfe: Projection-free sparse convex optimization. In *International Conference on Machine Learning*, pages 427–435, 2013.
- <span id="page-12-4"></span>[29] L Kantorovich. On the transfer of masses (in russian). *Doklady Akademii Nauk USSR*, 1942.
- <span id="page-12-5"></span>[30] Paul Knopp and Richard Sinkhorn. A note concerning simultaneous integral equations. *Canadian Journal of Mathematics*, 20:855–861, 1968.
- <span id="page-12-8"></span>[31] Simon Lacoste-Julien, Fredrik Lindsten, and Francis Bach. Sequential kernel herding: Frank-wolfe optimization for particle filtering. *arXiv preprint arXiv:1501.02056*, 2015.
- <span id="page-12-7"></span>[32] Bas Lemmens and Roger Nussbaum. *Nonlinear Perron-Frobenius Theory*, volume 189. Cambridge University Press, 2012.
- [33] Bas Lemmens and Roger Nussbaum. Birkhoff's version of Hilbert's metric and its applications in analysis. *arXiv preprint arXiv:1304.7921*, 2013.
- [34] M. V Menon. Reduction of a matrix with positive elements to a doubly stochastic matrix. *Proc. Amer. Math. Soc.*, 18:244–247, 1967.
- [35] Arthur Mensch, Mathieu Blondel, and Gabriel Peyré. Geometric losses for distributional learning. In *International Conference on Machine Learning*, pages 4516–4525, 2019.
- <span id="page-12-9"></span>[36] Krikamol Muandet, Kenji Fukumizu, Bharath Sriperumbudur, Bernhard Schölkopf, et al. Kernel mean embedding of distributions: A review and beyond. *Foundations and Trends* R *in Machine Learning*, 10(1-2):1–141, 2017.
- [37] Roger Nussbaum. Hilbert's projective metric and iterated nonlinear maps. *Mem. Amer. Math. Soc.*, 391:1–137, 1988.

- [38] Roger Nussbaum. Entropy minimization, Hilbert's projective metric and scaling integral kernels. *Journal of Functional Analysis*, 115:45–99, 1993.
- <span id="page-13-3"></span>[39] Gabriel Peyré and Marco Cuturi. Computational optimal transport. *Foundations and Trends* R *in Machine Learning*, 11(5-6):355–607, 2019.
- [40] Iosif Pinelis. Optimum bounds for the distributions of martingales in banach spaces. *The Annals of Probability*, pages 1679–1706, 1994.
- <span id="page-13-0"></span>[41] Julien Rabin, Gabriel Peyré, Julie Delon, and Marc Bernot. Wasserstein barycenter and its application to texture mixing. In *International Conference on Scale Space and Variational Methods in Computer Vision*, pages 435–446. Springer, 2011.
- <span id="page-13-4"></span>[42] Richard Sinkhorn and Paul Knopp. Concerning nonnegative matrices and doubly stochastic matrices. *Pacific J. Math.*, 21(2):343–348, 1967.
- [43] Steve Smale and Ding-Xuan Zhou. Learning theory estimates via integral operators and their approximations. *Constructive approximation*, 26(2):153–172, 2007.
- [44] Alex Smola, Arthur Gretton, Le Song, and Bernhard Schölkopf. A Hilbert space embedding for distributions. In *International Conference on Algorithmic Learning Theory*, pages 13–31. Springer, 2007.
- [45] Justin Solomon, Fernando De Goes, Gabriel Peyré, Marco Cuturi, Adrian Butscher, Andy Nguyen, Tao Du, and Leonidas Guibas. Convolutional wasserstein distances: Efficient optimal transportation on geometric domains. *ACM Transactions on Graphics (TOG)*, 34(4):66, 2015.
- <span id="page-13-7"></span>[46] Justin Solomon, Raif M. Rustamov, Leonidas Guibas, and Adrian Butscher. Wasserstein propagation for semi-supervised learning. In *Proceedings of the 31st International Conference on International Conference on Machine Learning - Volume 32*, ICML'14, pages I–306–I–314. JMLR.org, 2014.
- <span id="page-13-5"></span>[47] Le Song. Learning via Hilbert space embedding of distributions. 2008.
- <span id="page-13-6"></span>[48] Bharath K Sriperumbudur, Kenji Fukumizu, and Gert RG Lanckriet. Universality, characteristic kernels and RKHS embedding of measures. *Journal of Machine Learning Research*, 12(Jul):2389– 2410, 2011.
- [49] Bharath K Sriperumbudur, Arthur Gretton, Kenji Fukumizu, Bernhard Schölkopf, and Gert RG Lanckriet. Hilbert space embeddings and metrics on probability measures. *Journal of Machine Learning Research*, 11(Apr):1517–1561, 2010.
- <span id="page-13-1"></span>[50] Sanvesh Srivastava, Cheng Li, and David B Dunson. Scalable bayes via barycenter in wasserstein space. *The Journal of Machine Learning Research*, 19(1):312–346, 2018.
- <span id="page-13-2"></span>[51] Matthew Staib, Sebastian Claici, Justin M Solomon, and Stefanie Jegelka. Parallel streaming wasserstein barycenters. In *Advances in Neural Information Processing Systems*, pages 2647–2658, 2017.
- [52] Elias M Stein. *Singular integrals and differentiability properties of functions (PMS-30)*, volume 30. Princeton university press, 2016.

- <span id="page-14-0"></span>[53] C. Villani. *Optimal Transport: Old and New*. Grundlehren der mathematischen Wissenschaften. Springer Berlin Heidelberg, 2008.
- [54] Holger Wendland. *Scattered data approximation*, volume 17. Cambridge university press, 2004.
- [55] VV Yurinskii. Exponential inequalities for sums of random vectors. *Journal of multivariate analysis*, 6(4):473–499, 1976.
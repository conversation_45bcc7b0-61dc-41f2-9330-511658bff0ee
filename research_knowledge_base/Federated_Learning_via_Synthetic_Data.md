# Federated Learning via Synthetic Data

<PERSON>j <PERSON>

University of Michigan

September 29, 2020

### Abstract

Federated learning allows for the training of a model using data on multiple clients without the clients transmitting that raw data. However the standard method is to transmit model parameters (or updates), which for modern neural networks can be on the scale of millions of parameters, inflicting significant computational costs on the clients. We propose a method for federated learning where instead of transmitting a gradient update back to the server, we instead transmit a small amount of synthetic 'data'. We describe the procedure and show some experimental results suggesting this procedure has potential, providing more than an order of magnitude reduction in communication costs with minimal model degradation.

## 1 Introduction

Federated Learning (FL) helps protect user privacy by transmitting model updates instead of private user data. However these updates could potentially be much larger than the private data they are replacing, and depending on the number of users each user may need to transmit updates multiple times during the training of a single model. This puts an increased communication cost on the user, and reducing that burden is an important research direction in federated learning [\(<PERSON><PERSON><PERSON> et al.,](#page-11-0) [2019;](#page-11-0) [<PERSON> et al., 2020;](#page-11-1) [<PERSON> et al., 2020\)](#page-11-2). We propose a training process which reduces the upload communication costs incurred by the user. This method was motivated by [<PERSON> et al.](#page-11-3) [\(2018\)](#page-11-3), which showed that training on large datasets can be fairly well approximated by specifically built small synthetic datasets (in that training on the small synthetic datasets can produce networks which are almost as good as ones trained on large datasets, as long as that training data is available when producing the synthetic data). We will build on this method to present a procedure which can reduce the upload communication costs by one or two orders of magnitude, while still producing good server models.

We will start by combining these ideas with ideas from data poisoning attacks to introduce the procedure at a high level. We will then discuss a few technical changes which make this different from either of those techniques, and which improve the performance of the procedure, including an extension of the procedure to reduce download communication costs as well as upload costs. We conclude with experiments and discuss some possible next steps in developing the procedure.

## 2 Connection to Data Poisoning and Beyond

#### 2.1 Motivation from data poisoning

The inspiration for this method came from [Wang et al.](#page-11-3) [\(2018\)](#page-11-3), but at a high level this method is also very similar to data poisoning attacks. In data poisoning an adversary wants to generate synthetic data such that when a model is trained using it, the model 'does poorly' in some way. Let f be our model (usually a neural network) with parameters  $w$ , where the model evaluated with parameters are denoted by  $f(\cdot; w)$ . In [Muñoz-González et al.](#page-11-4) [\(2017\)](#page-11-4) they formulate this as a bi-level optimization problem:

$$
D_{po} \in \underset{D_{po}}{\text{arg max}} \quad \mathcal{L}(f(\mathcal{D}_{te}(X); w), \mathcal{D}_{te}(Y))
$$
  
s.t.  $w \in \underset{w}{\text{arg min}} L(f(\mathcal{D}_{tr}(X) \cup D_{po}(X); w), \mathcal{D}_{tr}(Y) \cup D_{po}(Y))$ 

where  $D_{po}$  is the synthetic 'poisoning' data,  $\mathcal{D}_{tr}$  is other training data and  $\mathcal{D}_{te}$  is test data, and the loss functions  $\mathcal{L}, L$  are specified by the application (in standard data poisoning attacks they are usually some variant of the loss the model is fitting). The model trains on the training and poisoning data, gets model parameters w, which it believes are good, but which are actually bad.

If synthetic data can be generated to hurt the training process, then it could also be made to help it by flipping the argmax to an argmin. And we could use no other training data other than our synthetic data, giving us:

$$
D \in \underset{D}{\text{arg min}} \quad \mathcal{L}(f(\mathcal{D}(X); w), \mathcal{D}(Y))
$$
  
s.t. 
$$
w \in \underset{w}{\text{arg min}} \, L(f(D(X); w), D(Y))
$$

where  $\mathcal D$  is our non-synthetic data and  $D$  is our synthetic data. In its most simple form  $L$  could be the quadratic approximation of the second order expansion of  $\mathcal L$  at the current parameter location  $w_0$ , meaning that the optimization problem on the second level simply becomes a step of gradient descent (where we will allow the learning rate to be optimized over as well).

$$
D, \eta \in \underset{D, \eta}{\text{arg min}} \quad \mathcal{L}(f(\mathcal{D}(X); w), \mathcal{D}(Y))
$$
s.t. 
$$
w = w_0 - g
$$
$$
g = \eta \nabla_w L(f(\mathcal{D}(X); w_0), \mathcal{D}(Y))
$$

In our federated optimization setting, during each training round the clients in the current cohort download the current model parameters  $w_0$ . The client then wants to find synthetic data  $D, \eta$  such that w has small loss on the client training data  $\mathcal{D}$ . Once the client has generated the synthetic data, the client uploads this synthetic data to the server, which then gathers all client data together and does a single step of gradient descent. In many cases the model parameters  $w$  may be much much larger in size than a single data point (or small number of data points), giving us a reduction (potentially quite significant) in upload transmission costs from client back to the server if we can upload a few synthetic data points instead of a gradient vector. Note this method is the same as in [Wang et al.](#page-11-3) [\(2018\)](#page-11-3), just applied in the federated setting. We provide additions to the method to improve the effectiveness of the procedure and specializing it for FL.

#### 2.2 Improvements

We discuss several changes, all of which let to empirical improvements in the performance of our system. Some have intuition, but ultimately we were led by experiments.

##### 2.2.1 Approximating standard federated learning

We can change the upper optimization problem so that instead of operating directly on the training data, the synthetic data tries to approximate the gradient update the standard federated learning would have transmitted back to the server. We do this by first running the usual local update procedure for FL. This produces a 'true update'  $\theta$ , which in standard FL we would transmit to the server directly. Now we adjust the upper optimization problem so that instead of fitting to the training data on the client, the synthetic data tries to induce an update g which is similar  $\theta$ . In the above our L is now more generally some function of D,  $w_0$  and  $q$ , so is  $\mathcal{L}(\mathcal{D}, w_0, q)$ .

We tried two ways of inducing a similar update. The first was directly penalizing the difference between  $\theta$  and g with a simple squared loss  $\mathcal{L} = ||\theta - g||^2$ , where  $\theta$  comes from updating our parameters from  $w_0$  using our real data  $\mathcal D$ . Here our loss function is working directly in parameter space. The other loss function  $\mathcal L$  we tested was one based on the following procedure: Using the true update  $\theta$ , calculate the probability vectors y' predicted by the updated network on each real data point. So  $y' = f(\mathcal{D}(X); w_0 - \theta)$ . Then the overall loss function looks at the KL-div between the probability vector predicted by the true updated network, and the probability vector predicted by the induced updated network. This conceptually is trying to fit the induced update by its similarity in function space of the true updated network and the induced updated network. This worked well, but in the end the squared error on parameter space ended up being better.

Remark. This may be due to the overparameterization of the network. There may be many different updates can create networks which are the same on a set of test points but differ across the function space. Even when the true data was augmented by randomly generated fake data (which corresponds to taking random samples of the functions) this was not as good as the squared loss (despite the appeal of fitting in function space).

###### 2.2.2 Multiple steps of gradient descent

There is no reason we need to limit ourselves to a single step of gradient descent. We can instead produce several batches of synthetic data  $D_m$  for  $m \in \{1, ..., M\}$  which produce intermediate updates  $g_m$ .

$$
\{D_m, \eta_m\}_{m=1}^M \in \underset{\{D_m, \eta_m\}_{m=1}^M}{\text{arg min}} \quad \mathcal{L}(\mathcal{D}, w_0, g)
$$
  
s.t.  $w = w_0 - g$   
 $g = \sum_{m=1}^{M} g_m$   
 $g_m = \eta_m \nabla L(f(D_m(X); w_{m-1}), D_m(Y))$ 

This was already done in [\(Wang et al., 2018\)](#page-11-3), where they found this to be useful. We found that it was not just helpful, but absolutely vital in approximating standard FL updates which had taken multiple steps of SGD. Although at first this seems like it would be computationally expensive as it would require computation of Hessians, a well known trick from [Pearlmutter](#page-11-5) [\(1994\)](#page-11-5) allows us to do this efficiently.

##### 2.2.3 Normalizing intermediate and final updates

We also moved to using normalized SGD (so we calculate the gradient, normalize it and then multiply by the learning rate). Conceptually this mean the synthetic data  $D_m$  only defines the direction of the update  $g_m$ , and the learning rate  $\eta_m$  completely defines the magnitude of the update. This enhanced the stability of our procedure.

We also normalized again after all  $M$  intermediate steps to produce our induced update  $g$ , and the 'learning rate' H which we multiply this by is equal to the norm of the true update  $\theta$ . The normalization of the overall update g ensures that we produce updates which are the same norm as the true update. This is valuable from an optimization perspective (conceptually this acts as a side constraint, feeding additional information to guide the optimization procedure).

##### 2.2.4 Trainable Y in synthetic data

Our experiments focused on the classification setting. In the original paper [\(Wang et al., 2018\)](#page-11-3) synthetic data was given a fixed class, so only the covariates were synthetic. This was done since having a label which is fractional did not really make any sense. However neural networks do not naturally work on categorical data space, they naturally work in the probability distribution space (the neural network will produce a probability of each class for a given data point and then we reinterpret that into a class label). However we noticed that fixing these labels was very limiting, so we allowed our synthetic data to have synthetic label distributions. So each synthetic data point has covariates X which are in the same space as our real data, and a label vector which are probabilities of each class instead of a single class label. Similar methods were shown in [Sucholutsky](#page-11-6) [and Schonlau](#page-11-6) [\(2019\)](#page-11-6) to be equally powerful. We project and normalize during training to keep all probabilities between 0 and 1 and summing to 1. We still use the standard cross entropy loss (or KL loss since they are the same here), but now our label vectors are no longer one hot vectors, instead are generally dense. This type of adjustment cannot generally be made in data poisoning attacks since the attacker needs to create data in the same space as the model. However here the client and server are working together, and so the server can adjust it's own training process to accommodate this super-labelled synthetic data. From here on we will consider the output of our neural network f to be the probability vector, as opposed to the class which is the argmax of that vector.

## 3 Synthetic data generating procedure

We present the subroutines for each server model update step. The most important part is the  $updateFromSynthetic$  function, which dictates how the synthetic data is decoded into an update. There are two functions *localU pdate* and *aggregate* which are undefined. These are the 'standard' methods from FL, so *localU pdate* is likely several passes of SGD over the client data, and *aggregate* could be federated averaging or something more advanced.

Algorithm 1: Server update

**Input:** Clients in cohort  $C$ , current server model parameters  $w_0$ Output: Server model update g for  $c \in [1 : |\mathcal{C}|]$  do Transmit current model parameters  $w_0$  to client  $\mathcal{C}[c]$ .; Client runs *clientU pdate* and transmits back synthetic data  $(D_c, H_c)$  $g_c = updateFromSynthetic(D_c, w_0, H_c);$ end  $g = aggregate({g_c})$ .; return g.; Remark: Here we have been a little sloppy with notation. The subscripts on  $g_c$  enumerate over the clients in the cohort, but the values transmitted back by each client are the final  $g$ 

in Algorithm [3.](#page-5-0)

#### Algorithm 2: clientUpdate

<span id="page-4-0"></span>**Input:** Transmitted model parameters  $w_0$ , client data  $\mathcal{D}$ , distillation learning rate  $\alpha$ **Output:** Synthetic data  $D = \{D_m, \eta_m\}_{m=1}^M$ , norm of the true local update H  $\theta = localUpdate(\mathcal{D}, w_0);$  $H = ||\theta||;$ Initialize  $D^0$ ; for  $t \in [1 : T]$  do Forward;  $g = updateFromSynthetic(D<sup>t-1</sup>, w<sub>0</sub>, H);$ Evaluate loss  $\mathcal{L} = \sum_{i} (\theta[i] - g[i])^2;$ Backwards.; for  $m \in [1:M]$  do  $D_m^t = D_m^{t-1} - \alpha \nabla_{D_m} \mathcal{L};$  $\eta_m^t = \eta_m^{t-1} - \alpha \nabla_{\eta_m} \mathcal{L}$ .; end end  $D = D^T$ .; return  $D, H$ 

In Figure [1](#page-5-1) the green leaf nodes comprising of  $(D_1, \eta_1...D_m, \eta_m)$  and the node H are what will be send back to the server, and the server runs  $updateFromSynthetic$  to produce g, which it will then treat as if that were the gradients uploaded by the client directly. It is vital that the updateFromSynthetic on the client and on the server are exactly the same! Figure [1](#page-5-1) provides the computational graph used to derive the backprop computations required. Although it may seem computationally prohibitive since we need the Hessian of our parameters, they are only needed in a vector-Jacobian product, and fortunately this can be efficiently calculated in  $\mathcal{O}(n)$  using a well known trick from [Pearlmutter](#page-11-5) [\(1994\)](#page-11-5).

An important structure we use here is that it is possible for  $D_i = D_j$ . The advantage of this is we only have to transmit that synthetic data once, but we generate multiple  $g_i$  from it. Conceptually it is similar to 'training on the synthetic data for multiple epoch'. We have found this to be very powerful. The changes to the computational graph above would just be to change the current  $D_m$ leaf nodes in the graph into intermediate nodes, and have a set of leaf data nodes which can point

#### Algorithm 3: updateFromSynthetic

<span id="page-5-0"></span>**Input:** Synthetic data D, initial model parameters  $w_0$ , true update norm H **Output:** Induced update g for  $m \in [1:M]$  do  $\tilde{g_m} = \nabla_w L(f(D_m(X); w_{m-1}), D_m(Y));$  $g_m=\eta_m\frac{\tilde{g_m}}{||\tilde{g_m}||};$ end  $\tilde{g} = \sum_{i=1}^{M}$  $m=1$  $g_m$ ;  $g=H\frac{\tilde{g}}{||\tilde{g}||};$ return g;

<span id="page-5-1"></span>Image /page/5/Figure/2 description: This is a flowchart illustrating a process involving multiple stages of training and updates. The process starts with initial parameters w0, learning rate η1, and dataset D1, which are used in a 'train' computation to produce g1. This g1, along with an 'update' operation, leads to w1. The process continues iteratively with intermediate parameters w1 through wm-1, learning rates ηm, and datasets Dm, each undergoing a 'train' computation to produce gm. Each gm, along with an 'update' operation, leads to the next parameter stage, culminating in wm. Additionally, a fixed node H influences an 'Overall update' computation, which also receives input from g1 and gm. The final output of the 'Overall update' is g. The legend indicates that green nodes represent 'Leaf nodes (data trained during distillation)', orange nodes represent 'Fixed nodes (non trainable data fixed during distillation)', and blue nodes represent 'Intermediate nodes (functions of leaf and fixed nodes)'. Rectangular shapes represent 'Data nodes', and shapes with a pointed end represent 'Compute nodes'.

Figure 1: Computational graph for  $updateFromSynthetic$ . Compute node formulas: train:  $g_m = \eta_m \frac{\tilde{g_m}}{||\tilde{g_m}||}$ where  $\tilde{g_m} = \nabla L(f(D_m(x); w_{m-1}), D_m(Y))$  where  $L(y, z) = D_{KL}(z||y)$ . update:  $w_{m+1} = w_m - g_m$ . Overall *update:*  $g = H \frac{\tilde{g}}{||\tilde{g}||}$  where  $\tilde{g} = \sum_{n=1}^{M}$  $\sum_{m=1} g_m$ ,  $H = ||\theta||$ .

to multiple of the current leaf nodes.

##### 3.1 Tracking the best approximation

Ideally we would produce synthetic data such that  $g = \theta$ , but in practice this will almost never happen. And our loss function  $\mathcal L$  is directly on the parameter space, which is not ideal since it does not take into account the impact small changes in parameters have on the output of the neural network. To try and take this into account, we adapt a technique from non-convex optimization. In non-convex optimization one common heuristic is to, separately from the sequence of solutions produced during your optimization procedure, keep track of the best solution, and only update that if you get a better solution (even if you let the optimization procedure move to a worse solution in the hope that it will eventually find an even better one). For example when training a NN you can use a hold out set, and keep track of the parameters which perform best on the hold out set, even if you let the parameters be updated to worse (on the hold out set) parameters. Usually people use the same objective function which they are optimizing (or same except evaluated on a different dataset). However here we will use two very different objective functions.

After every round, to test the quality of our induced update g, we calculate the cross entropy loss on our the client's training data set. We keep track of the synthetic data which induces the lowest cross entropy loss, using this loss to define our 'best' synthetic data. This is a pretty standard technique, but here we are using this to implicitly 'fit' to two complimentary (but not identical) objective functions: we want an induced update which is close to the true update, and which performs well on our training data. This is of course completely ad hoc, and almost certainly could be improved. However empirically this helped stabilize our optimization procedure.

## 4 Experiments

We test our synthetic data approach with experiments emulating federated learning. Our experiment setup is based on the MNIST experiments in [McMahan et al.](#page-11-7) [\(2016\)](#page-11-7), using the same MNIST CNN network structure (further experiments with CIFAR are in the works). We used the same data sharding scheme for the iid and non-iid data (100 clients, random sharding for the iid data, 2 classes per client with 300 points each for non-iid data). For the *localU pdate* we use SGD with 5 epochs, batch size of 10 and learning rate of 0.02, and for aggregate on the server we use federated averaging, with a cohort size of 10.

For the distillation parameters (parameters that affect the synthetic data) we used 5 batches of synthetic data, each containing 10 synthetic data points, and  $updateFromSynthetic$  will train on those batches for 5 epochs (giving us an  $M = 25$ ), with a distillation learning rate  $\alpha$  of 0.2. We also used Adam to train the synthetic data in Algorithm [2](#page-4-0) (as opposed to the GD stated).

This procedure requires transmission of 50 synthetic data points and the norm of  $\theta$ , requiring transmission of 39701 floats, or just 2.4%, or  $\frac{1}{40}$  the 1663370 floats required to transmit a gradient update for the MNIST CNN model. We train the synthetic data for 300 updates, requiring about 5.5 times the computation required for just running the *localU pdate* on it's own. Note that because we always run the same localU pdate as a first step, our synthetic data FL will always be more computationally expensive.

The selection of these distillation parameters involved a small amount of tuning (constrained by what we considered to be reasonable computational and communication costs), but could almost certainly be tuned more to improve the results. At the coarse level we tuned, both the iid and non-iid partitioning schemes used the same tuning parameters (where as one would expect them to have different optimal parameters at a more granular level of tuning).

### 4.1 Quality of procedure on IID and non-IID clients

In these experiments the seeds which dictate how the data is partitioned between users and randomness with the FL procedure (which client is in which cohort etc) are all the same, though they are different from the seeds used to select tuning parameters. This means the standard FL procedure is the same for all runs. And the seed used for creating the synthetic data (initialization etc) are different. The plots on the left show raw values, and the plots on the right show the difference between using synthetic data FL and standard full gradient transmission FL. The first two rows in Figure [2](#page-7-0) show results for the iid data partitioning, while the second two show the non-iid partitioning.

The synthetic data FL appears comparable to the full gradient transmission FL, while only requiring a small fraction of the upload transmission costs.

<span id="page-7-0"></span>Image /page/7/Figure/0 description: The image displays two sets of plots, each comparing "Synthetic data FL" and "Full FL" performance across 20 server update rounds. The top set of plots is titled "iid client partitioning", and the bottom set is titled "Non-iid client partitioning". Each set contains two plots: the left plot shows "Test accuracy" on the y-axis and "Number of server update rounds" on the x-axis, with values ranging from 0.95 to 1.00. The right plot shows the "Difference in test accuracy" on the y-axis, ranging from 0.00 to 0.10. Below these, the left plot shows "Test loss" on the y-axis, ranging from 0.000 to 0.175, and the right plot shows the "Difference in test loss" on the y-axis, ranging from 0.00 to 0.30. For the "iid client partitioning" plots, both "Synthetic data FL" and "Full FL" show a similar trend of increasing test accuracy and decreasing test loss, with the "Full FL" generally performing slightly better. The "Difference in test accuracy" and "Difference in test loss" plots for "iid client partitioning" show a rapid decrease in the initial rounds, stabilizing thereafter. For the "Non-iid client partitioning" plots, the "Test accuracy" shows more fluctuations, with "Full FL" generally outperforming "Synthetic data FL" in later rounds. The "Test loss" also shows more variability. The "Difference in test accuracy" and "Difference in test loss" plots for "Non-iid client partitioning" exhibit significant spikes and dips throughout the 20 rounds.

### Figure 2: Comparing synthetic data FL to full gradient transmission FL

iid client partitioning

### 4.2 Robustness to distillation learning rate

The biggest cost independent tuning parameter (in that changing this value does not change the computational or communication costs) is the distillation learning rate  $\alpha$ . Figure [3](#page-8-0) suggests that we are not super sensitive to the value of this learning rate, as most rates between 0.03 and 0.3 produced very similar values (the 0.995 is the learning rate decay, and had even less of an effect than the learning rate). Since hyperparameter tuning is challenging in FL this robustness is very valuable.

Image /page/8/Figure/0 description: Figure 3: Testing different learning rates

<span id="page-8-0"></span>Image /page/8/Figure/1 description: This image contains four plots arranged in a 2x2 grid. The x-axis for all plots represents the number of server update rounds, ranging from 2.5 to 20.0. The top-left plot shows 'Test accuracy' on the y-axis, ranging from 0.95 to 1.00. The top-right plot displays the 'Difference in test accuracy' on the y-axis, ranging from 0.0000 to 0.0200. The bottom-left plot illustrates 'Test loss' on the y-axis, ranging from 0.000 to 0.175. The bottom-right plot shows the 'Difference in test loss' on the y-axis, ranging from 0.00 to 0.10. Each plot contains multiple colored lines, with a legend at the bottom indicating that these lines represent different configurations: (0.03, 0.995), (0.06, 0.995), (0.09, 0.995), (0.15, 0.995), (0.2, 0.995), (0.3, 0.995), and 'Full FL'.

### 4.3 Trade off between communication, compute and approximation quality

Unsurprisingly as you increase the number of synthetic data points you use, or the amount of client compute you use to generate that synthetic data, you change your approximation quality and correspondingly how similar to full FL you perform. However there are several parameters you can change to increase the computation or the communication. For example if you are willing to double your communication costs, you can either double the size of each synthetic batch, or double the number of synthetic batches (or some mixture of the two). Note that both of these also increase your compute, so you may need to account for that. Similarly if you are willing to double your computation (without changing communication) you can double the amount of time you spend training your synthetic data, or you can double the number of epochs over the synthetic data you use in *updateFromSynthetic*. It appears that all of these suffer from diminishing returns, and so it is best to use some amount of all of these methods. There may also be a connection to the number of steps of SGD taken during *localU pdate* (one might expect it to be better to have more synthetic batches if there are more steps of SGD, see below), but we do not yet fully understand this.

#### 4.4 Adapt for server to client transmission

The above method is for client to server transmission, but does nothing for server to client. However we can also use this to transmit an approximation of the server model to the client. We need to know the initial state of the model on each client, and this can be achieved if we fix our model initialization protocol and then transmit the seed first used to initialize on the server to the client. Then we send synthetic data which induces an update that bring a model with that known initialization close to the current model on the server. The transmission and use of synthetic data is the same as our current setup (just sending synthetic data from server to client instead).

Although the principle is the same, there are differences which make this direction more challenging. The biggest difficulty is now we are replicating thousands (and possibly much more) of updates with just a few updates, as opposed to in client to server, where we are using a few updates to replicate tens of updates. In particular as the training process evolves, we need to replicate a growing number of updates, as opposed to the client to server direction where the number of updates we need to approximate is (roughly) constant. We believe this to be at least one of the main reasons why the approximation for the reverse direction is much more challenging computationally.

Experimentally we found using synthetic data to transmit the model in both directions to be much more challenging. The biggest issue we encountered were the occurrence of complete failures, where the server failed to approximate the model at all and the synthetic data ends up inducing a model which is no better than the initialized model. We were able to overcome this issue via a brute force approach, where we initialized the synthetic data using multiple seeds, separately trained each one and used the data which induced the model which was most similar to the current server model. Although this multiplicatively increases the server computational costs, in our setup the server is owned by the researchers and so server compute is much less valuable than client compute or communication costs. And since this training can be done in parallel, this can be done without dramatically increasing the wall time of our training procedure.

Initial experimental results suggest that this server to client transmission is viable, although would benefit from further refinement. We repeated used the same experimental setup as above, with the exception that we used synthetic data to transmit the required models in both directions. For the server to client transmission, we used 100 synthetic data points in 10 batches of 10 data points (requiring only  $\frac{1}{20}$ <sup>th</sup> the download cost compared to the full model parameters), trained for 600 updates, and tried 10 seeds for each server initialization.

Image /page/9/Figure/3 description: The image displays four plots arranged in a 2x2 grid, illustrating the performance of different federated learning (FL) configurations over server update rounds. The top-left plot shows 'Test accuracy' on the y-axis and 'Number of server update rounds' on the x-axis, ranging from 0.95 to 1.00. The top-right plot shows 'Difference in test accuracy' on the y-axis, ranging from 0.00 to 0.10, against 'Number of server update rounds' on the x-axis. The bottom-left plot displays 'Test loss' on the y-axis, ranging from 0.000 to 0.175, against 'Number of server update rounds' on the x-axis. The bottom-right plot shows 'Difference in test loss' on the y-axis, ranging from 0.00 to 0.30, against 'Number of server update rounds' on the x-axis. Three lines are plotted in each graph, representing 'Both direction synth FL' (blue), 'client to server synth FL' (orange), and 'Full FL' (green). In the 'Test accuracy' plot, the green line ('Full FL') shows the highest accuracy, reaching approximately 0.985 by round 20. The orange line ('client to server synth FL') is second, reaching about 0.98, and the blue line ('Both direction synth FL') is third, reaching around 0.97. In the 'Difference in test accuracy' plot, all lines quickly drop to near zero after the first few rounds, with the blue line showing a slightly higher difference initially. The 'Test loss' plot shows the green line ('Full FL') achieving the lowest loss, dropping below 0.075 by round 20. The orange line is next, ending around 0.11, and the blue line is the highest, around 0.12. The 'Difference in test loss' plot shows a sharp initial drop for all lines, with the orange line having the largest initial difference, followed by the blue line, and then the green line. All differences stabilize at low values after a few rounds.

Figure 4: Synthetic data transmission in both directions

The double synthetic data FL is able to train a good model, trading 1.5% loss in accuracy for over 90% reduction in download communication costs. However it appears that the model may be unable to further improve. Overcoming this shortfall is critical and the subject of continuous work.

## 5 Possible Future Work

These initial results seem very promising, but there are still many things we need to understand before we know if this will actually be useful in practice. Below we have several of the directions we think are most promising.

### 5.1 Async updates

This is probably the most speculative but also most exciting possible future direction. In [\(Wang](#page-11-3) [et al., 2018\)](#page-11-3) they show that the current system is extremely sensitive to  $w_0$  being the same on the client and on the server. However they also showed that if you train over a distribution of  $w_0$ 's on the client (so during each synthetic data update you draw from  $p(w_0)$  and use that for the update), and then the server draws from that same distribution, you can get back good performance. This hints that we might be able to use a similar procedure to generate synthetic data which is robust to producing good updates even if the model parameters have changed a little. You probably need to train on  $w_0$  drawn uniformly from some epsilon ball around the transmitted  $w_0$ , and who knows how much compute you will need, or whether you can do this effectively on a large enough ball (might need clipping of your server update, and even that might not be enough). So there is a lot of uncertainty here. But given that something similar was shown to work in [\(Wang et al., 2018\)](#page-11-3) it seems like it is worth trying.

### 5.2 Privacy concerns

Transmitting gradients has the benefit that they are much harder to interpret than sending data (though of course they are not impossible to learn from). There is the risk that the synthetic data we transmit may be much more revealing about the data on the client. In [Wang et al.](#page-11-3) [\(2018\)](#page-11-3) on the MNIST data set they produced synthetic data which looked very 'real', in that you could clearly recognize the synthetic data as showing the number of the label. Since our data no longer has single labels this might not be an issue, but we need to look into it possibly solve it. One possible solution would be to project our probabilities away from 0 to prevent any synthetic data point from representing a real label. We could also learn our synthetic data in a differentially private way, possibly adding noise before the approximation process.

### 5.3 Experiments with heterogeneous client resources

In FL we often have that different clients have very different quality hardware available. Clearly we can adapt our distillation parameters to accommodate that, using fewer synthetic data points for clients with less bandwidth and more compute on clients with faster phones. The question is whether this is a good idea, especially if there is correlation between resource usage and the type of data on the client. Does increasing resource usage on clients who can afford it lead to a strictly better model (where the improvement may be focused on areas where the faster clients have more data)? Or does this lead to detrimental experiences for the slower clients by biasing our models?

### 5.4 Remove needing *localUpdate*

We found that fitting to the true FL update worked better than creating synthetic data using the training data directly. However we may just have done it poorly. Removing the need for a localU pdate could be valuable when we are working in very low compute environments.

#### 5.5 Analysis from a compression view

Here we are really just using the 'training' procedure of the neural network as a decompressor, where the synthetic data is the compressed true local update  $\theta$ . Can arbitrary vectors be equally well compressed, or is there something about the connection between the decompressor and the nature of the vectors being compressed? Intuitively one might hypothesize that the manifold of possible updates is much smaller than all of  $\mathbb{R}^d$  (where d is the number of parameters in the model), and that they contain some sort of 'nested' property as you reduce the amount of data used to train. That could explain why we can well approximate lots of training with lots of data, using little training with little data.

## 6 Acknowledgements

We would like to thank Mikhail Yurochkin for his helpful advice and insights in both guiding the project and presenting this work.

### References

- <span id="page-11-0"></span>Kairouz, P., McMahan, H. B., Avent, B., Bellet, A., Bennis, M., Bhagoji, A. N., Bonawitz, K., Charles, Z., Cormode, G., Cummings, R., et al. (2019). Advances and open problems in federated learning. arXiv preprint arXiv:1912.04977.
- <span id="page-11-1"></span>Li, T., Sahu, A. K., Talwalkar, A., and Smith, V. (2020). Federated learning: Challenges, methods, and future directions. IEEE Signal Processing Magazine, 37(3):50–60.
- <span id="page-11-2"></span>Liu, X., Li, Y., Tang, J., and Yan, M. (2020). A double residual compression algorithm for efficient distributed learning. In International Conference on Artificial Intelligence and Statistics, pages 133–143.
- <span id="page-11-7"></span>McMahan, H. B., Moore, E., Ramage, D., Hampson, S., et al. (2016). Communication-efficient learning of deep networks from decentralized data. arXiv preprint arXiv:1602.05629.
- <span id="page-11-4"></span>Muñoz-González, L., Biggio, B., Demontis, A., Paudice, A., Wongrassamee, V., Lupu, E. C., and Roli, F. (2017). Towards poisoning of deep learning algorithms with back-gradient optimization. In Proceedings of the 10th ACM Workshop on Artificial Intelligence and Security, pages 27–38.
- <span id="page-11-5"></span>Pearlmutter, B. A. (1994). Fast exact multiplication by the hessian. Neural computation, 6(1):147– 160.
- <span id="page-11-6"></span>Sucholutsky, I. and Schonlau, M. (2019). Soft-label dataset distillation and text dataset distillation. arXiv preprint arXiv:1910.02551.
- <span id="page-11-3"></span>Wang, T., Zhu, J.-Y., Torralba, A., and Efros, A. A. (2018). Dataset distillation. *arXiv preprint* arXiv:1811.10959.
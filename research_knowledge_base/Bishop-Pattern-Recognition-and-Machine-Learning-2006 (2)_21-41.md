Image /page/0/Picture/0 description: The image is a title slide with the number 1 in red at the top, followed by the word "Introduction" in black text. The background is a blurred, abstract pattern of golden and silver wavy lines, resembling rippling water or metallic reflections.

The problem of searching for patterns in data is a fundamental one and has a long and successful history. For instance, the extensive astronomical observations of <PERSON><PERSON> in the 16th century allowed <PERSON> to discover the empirical laws of planetary motion, which in turn provided a springboard for the development of classical mechanics. Similarly, the discovery of regularities in atomic spectra played a key role in the development and verification of quantum physics in the early twentieth century. The field of pattern recognition is concerned with the automatic discovery of regularities in data through the use of computer algorithms and with the use of these regularities to take actions such as classifying the data into different categories.

Consider the example of recognizing handwritten digits, illustrated in Figure 1.1. Each digit corresponds to a  $28 \times 28$  pixel image and so can be represented by a vector **x** comprising <sup>784</sup> real numbers. The goal is to build a machine that will take such a vector **x** as input and that will produce the identity of the digit  $0, \ldots, 9$  as the output. This is a nontrivial problem due to the wide variability of handwriting. It could be

**Figure 1.1** Examples of hand-written digits taken from US zip codes.

Image /page/1/Picture/2 description: The image displays a grid of ten handwritten digits, arranged in two rows of five. The top row shows the digits 0, 1, 2, 3, and 4. The bottom row shows the digits 5, 6, 7, 8, and 9. Each digit is enclosed in a black square border and written in dark blue ink against a white background.

tackled using handcrafted rules or heuristics for distinguishing the digits based on the shapes of the strokes, but in practice such an approach leads to a proliferation of rules and of exceptions to the rules and so on, and invariably gives poor results.

Far better results can be obtained by adopting a machine learning approach in which a large set of N digits  $\{x_1, \ldots, x_N\}$  called a *training set* is used to tune the parameters of an adaptive model. The categories of the digits in the training set are known in advance, typically by inspecting them individually and hand-labelling them. We can express the category of a digit using *target vector* **t**, which represents the identity of the corresponding digit. Suitable techniques for representing categories in terms of vectors will be discussed later. Note that there is one such target vector **t** for each digit image **x**.

The result of running the machine learning algorithm can be expressed as a function  $\mathbf{y}(\mathbf{x})$  which takes a new digit image  $\mathbf{x}$  as input and that generates an output vector **y**, encoded in the same way as the target vectors. The precise form of the function **y**(**x**) is determined during the *training* phase, also known as the *learning* phase, on the basis of the training data. Once the model is trained it can then determine the identity of new digit images, which are said to comprise a *test set*. The ability to categorize correctly new examples that differ from those used for training is known as *generalization*. In practical applications, the variability of the input vectors will be such that the training data can comprise only a tiny fraction of all possible input vectors, and so generalization is a central goal in pattern recognition.

For most practical applications, the original input variables are typically *preprocessed* to transform them into some new space of variables where, it is hoped, the pattern recognition problem will be easier to solve. For instance, in the digit recognition problem, the images of the digits are typically translated and scaled so that each digit is contained within a box of a fixed size. This greatly reduces the variability within each digit class, because the location and scale of all the digits are now the same, which makes it much easier for a subsequent pattern recognition algorithm to distinguish between the different classes. This pre-processing stage is sometimes also called *feature extraction*. Note that new test data must be pre-processed using the same steps as the training data.

Pre-processing might also be performed in order to speed up computation. For example, if the goal is real-time face detection in a high-resolution video stream, the computer must handle huge numbers of pixels per second, and presenting these directly to a complex pattern recognition algorithm may be computationally infeasible. Instead, the aim is to find useful features that are fast to compute, and yet that also preserve useful discriminatory information enabling faces to be distinguished from non-faces. These features are then used as the inputs to the pattern recognition algorithm. For instance, the average value of the image intensity over a rectangular subregion can be evaluated extremely efficiently (Viola and Jones, 2004), and a set of such features can prove very effective in fast face detection. Because the number of such features is smaller than the number of pixels, this kind of pre-processing represents a form of dimensionality reduction. Care must be taken during pre-processing because often information is discarded, and if this information is important to the solution of the problem then the overall accuracy of the system can suffer.

Applications in which the training data comprises examples of the input vectors along with their corresponding target vectors are known as *supervised learning* problems. Cases such as the digit recognition example, in which the aim is to assign each input vector to one of a finite number of discrete categories, are called *classification* problems. If the desired output consists of one or more continuous variables, then the task is called *regression*. An example of a regression problem would be the prediction of the yield in a chemical manufacturing process in which the inputs consist of the concentrations of reactants, the temperature, and the pressure.

In other pattern recognition problems, the training data consists of a set of input vectors **x** without any corresponding target values. The goal in such *unsupervised learning* problems may be to discover groups of similar examples within the data, where it is called *clustering*, or to determine the distribution of data within the input space, known as *density estimation*, or to project the data from a high-dimensional space down to two or three dimensions for the purpose of *visualization*.

Finally, the technique of *reinforcement learning* (Sutton and Barto, 1998) is concerned with the problem of finding suitable actions to take in a given situation in order to maximize a reward. Here the learning algorithm is not given examples of optimal outputs, in contrast to supervised learning, but must instead discover them by a process of trial and error. Typically there is a sequence of states and actions in which the learning algorithm is interacting with its environment. In many cases, the current action not only affects the immediate reward but also has an impact on the reward at all subsequent time steps. For example, by using appropriate reinforcement learning techniques a neural network can learn to play the game of backgammon to a high standard (Tesauro, 1994). Here the network must learn to take a board position as input, along with the result of a dice throw, and produce a strong move as the output. This is done by having the network play against a copy of itself for perhaps a million games. A major challenge is that a game of backgammon can involve dozens of moves, and yet it is only at the end of the game that the reward, in the form of victory, is achieved. The reward must then be attributed appropriately to all of the moves that led to it, even though some moves will have been good ones and others less so. This is an example of a *credit assignment* problem. A general feature of reinforcement learning is the trade-off between *exploration*, in which the system tries out new kinds of actions to see how effective they are, and *exploitation*, in which the system makes use of actions that are known to yield a high reward. Too strong a focus on either exploration or exploitation will yield poor results. Reinforcement learning continues to be an active area of machine learning research. However, a

**Figure 1.2** Plot of a training data set of  $N =$ 10 points, shown as blue circles, each comprising an observation of the input variable  $x$  along with the corresponding target variable  $t$ . The green curve shows the function  $sin(2\pi x)$  used to generate the data. Our goal is to predict the value of  $t$  for some new value of  $x$ , without knowledge of the green curve.

Image /page/3/Figure/2 description: The image is a plot of a green curve with several blue circles representing data points. The x-axis is labeled 'x' and ranges from 0 to 1. The y-axis is labeled 't' and ranges from -1 to 1. The data points are scattered around the curve, which appears to be a sine wave or a similar oscillating function. Specifically, the data points are located at approximately (0.1, 0.4), (0.2, 0.8), (0.3, 1.0), (0.4, 0.9), (0.5, 0.2), (0.6, 0.1), (0.7, -0.5), (0.8, -0.7), (0.9, -0.5), and (1.0, 0.2).

detailed treatment lies beyond the scope of this book.

Although each of these tasks needs its own tools and techniques, many of the key ideas that underpin them are common to all such problems. One of the main goals of this chapter is to introduce, in a relatively informal way, several of the most important of these concepts and to illustrate them using simple examples. Later in the book we shall see these same ideas re-emerge in the context of more sophisticated models that are applicable to real-world pattern recognition applications. This chapter also provides a self-contained introduction to three important tools that will be used throughout the book, namely probability theory, decision theory, and information theory. Although these might sound like daunting topics, they are in fact straightforward, and a clear understanding of them is essential if machine learning techniques are to be used to best effect in practical applications.

# 1.1. Example: Polynomial Curve Fitting

We begin by introducing a simple regression problem, which we shall use as a running example throughout this chapter to motivate a number of key concepts. Suppose we observe a real-valued input variable  $x$  and we wish to use this observation to predict the value of a real-valued target variable  $t$ . For the present purposes, it is instructive to consider an artificial example using synthetically generated data because we then know the precise process that generated the data for comparison against any learned model. The data for this example is generated from the function  $sin(2\pi x)$ with random noise included in the target values, as described in detail in Appendix A.

Now suppose that we are given a training set comprising  $N$  observations of  $x$ , written  $\mathbf{x} \equiv (x_1, \ldots, x_N)^T$ , together with corresponding observations of the values of t, denoted  $\mathbf{t} \equiv (t_1, \ldots, t_N)^T$ . Figure 1.2 shows a plot of a training set comprising  $N = 10$  data points. The input data set **x** in Figure 1.2 was generated by choosing values of  $x_n$ , for  $n = 1, \ldots, N$ , spaced uniformly in range [0, 1], and the target data set **t** was obtained by first computing the corresponding values of the function

## 1.1. Example: Polynomial Curve Fitting

 $\sin(2\pi x)$  and then adding a small level of random noise having a Gaussian distribution (the Gaussian distribution is discussed in Section 1.2.4) to each such point in order to obtain the corresponding value  $t_n$ . By generating data in this way, we are capturing a property of many real data sets, namely that they possess an underlying regularity, which we wish to learn, but that individual observations are corrupted by random noise. This noise might arise from intrinsically stochastic (i.e. random) processes such as radioactive decay but more typically is due to there being sources of variability that are themselves unobserved.

Our goal is to exploit this training set in order to make predictions of the value t of the target variable for some new value  $\hat{x}$  of the input variable. As we shall see later, this involves implicitly trying to discover the underlying function  $sin(2\pi x)$ . This is intrinsically a difficult problem as we have to generalize from a finite data set. Furthermore the observed data are corrupted with noise, and so for a given  $\hat{x}$ there is uncertainty as to the appropriate value for  $\hat{t}$ . Probability theory, discussed in Section 1.2, provides a framework for expressing such uncertainty in a precise and quantitative manner, and decision theory, discussed in Section 1.5, allows us to exploit this probabilistic representation in order to make predictions that are optimal according to appropriate criteria.

For the moment, however, we shall proceed rather informally and consider a simple approach based on curve fitting. In particular, we shall fit the data using a polynomial function of the form

$$
y(x, \mathbf{w}) = w_0 + w_1 x + w_2 x^2 + \ldots + w_M x^M = \sum_{j=0}^{M} w_j x^j
$$
 (1.1)

where M is the *order* of the polynomial, and  $x^j$  denotes x raised to the power of j. The polynomial coefficients  $w_0, \ldots, w_M$  are collectively denoted by the vector **w**. Note that, although the polynomial function  $y(x, \mathbf{w})$  is a nonlinear function of x, it is a linear function of the coefficients **w**. Functions, such as the polynomial, which are linear in the unknown parameters have important properties and are called *linear models* and will be discussed extensively in Chapters 3 and 4.

The values of the coefficients will be determined by fitting the polynomial to the training data. This can be done by minimizing an *error function* that measures the misfit between the function  $y(x, w)$ , for any given value of w, and the training set data points. One simple choice of error function, which is widely used, is given by the sum of the squares of the errors between the predictions  $y(x_n, \mathbf{w})$  for each data point  $x_n$  and the corresponding target values  $t_n$ , so that we minimize

$$
E(\mathbf{w}) = \frac{1}{2} \sum_{n=1}^{N} \left\{ y(x_n, \mathbf{w}) - t_n \right\}^2
$$
 (1.2)

where the factor of  $1/2$  is included for later convenience. We shall discuss the motivation for this choice of error function later in this chapter. For the moment we simply note that it is a nonnegative quantity that would be zero if, and only if, the

**Figure 1.3** The error function (1.2) corresponds to (one half of) the sum of  $\,$   $_{t}$ the squares of the displacements (shown by the vertical green bars) of each data point from the function  $y(x, \mathbf{w})$ .

Image /page/5/Figure/2 description: The image displays a graph with the x-axis labeled 'x' and the y-axis labeled 't'. A red curve, representing the function y(x\_n, w), is plotted on the graph. Two vertical green lines connect points on the x-axis to points on the red curve. At the top of each green line, there is a blue circle labeled 't\_n'. At the bottom of each green line, there is a green circle on the red curve. The x-axis also has a tick mark labeled 'x\_n'.

function  $y(x, w)$  were to pass exactly through each training data point. The geometrical interpretation of the sum-of-squares error function is illustrated in Figure 1.3.

We can solve the curve fitting problem by choosing the value of **w** for which  $E(\mathbf{w})$  is as small as possible. Because the error function is a quadratic function of the coefficients **w**, its derivatives with respect to the coefficients will be linear in the elements of **w**, and so the minimization of the error function has a unique solution, *Exercise 1.1* denoted by  $w^*$ , which can be found in closed form. The resulting polynomial is given by the function  $y(x, \mathbf{w}^*)$ .

> There remains the problem of choosing the order  $M$  of the polynomial, and as we shall see this will turn out to be an example of an important concept called *model comparison* or *model selection*. In Figure 1.4, we show four examples of the results of fitting polynomials having orders  $M = 0, 1, 3$ , and 9 to the data set shown in Figure 1.2.

> We notice that the constant  $(M = 0)$  and first order  $(M = 1)$  polynomials give rather poor fits to the data and consequently rather poor representations of the function  $sin(2\pi x)$ . The third order ( $M = 3$ ) polynomial seems to give the best fit to the function  $sin(2\pi x)$  of the examples shown in Figure 1.4. When we go to a much higher order polynomial ( $M = 9$ ), we obtain an excellent fit to the training data. In fact, the polynomial passes exactly through each data point and  $E(\mathbf{w}^*)=0$ . However, the fitted curve oscillates wildly and gives a very poor representation of the function  $sin(2\pi x)$ . This latter behaviour is known as *over-fitting*.

> As we have noted earlier, the goal is to achieve good generalization by making accurate predictions for new data. We can obtain some quantitative insight into the dependence of the generalization performance on  $M$  by considering a separate test set comprising 100 data points generated using exactly the same procedure used to generate the training set points but with new choices for the random noise values included in the target values. For each choice of  $M$ , we can then evaluate the residual value of  $E(\mathbf{w}^*)$  given by (1.2) for the training data, and we can also evaluate  $E(\mathbf{w}^*)$ for the test data set. It is sometimes more convenient to use the root-mean-square

*Exercise 1.1*

Image /page/6/Figure/1 description: This figure displays four plots, each showing a set of blue data points and two fitted curves: a green curve representing the true underlying function and a red curve representing a polynomial fit of a certain order M. The top-left plot, labeled M = 0, shows a horizontal red line that poorly fits the data points, which follow a sinusoidal pattern. The top-right plot, labeled M = 1, shows a straight red line that also fails to capture the sinusoidal nature of the data. The bottom-left plot, labeled M = 3, shows a red curve that begins to follow the sinusoidal trend more closely, but still has some deviations. The bottom-right plot, labeled M = 9, shows a red curve that overfits the data, exhibiting wild oscillations between the data points, particularly at the edges of the plot. All plots share the same x and t axes, with x ranging from 0 to 1 and t ranging from -1 to 1.

**Figure 1.4** Plots of polynomials having various orders M, shown as red curves, fitted to the data set shown in Figure 1.2.

(RMS) error defined by

$$
E_{\rm RMS} = \sqrt{2E(\mathbf{w}^*)/N} \tag{1.3}
$$

in which the division by  $N$  allows us to compare different sizes of data sets on an equal footing, and the square root ensures that  $E_{RMS}$  is measured on the same scale (and in the same units) as the target variable  $t$ . Graphs of the training and test set RMS errors are shown, for various values of  $M$ , in Figure 1.5. The test set error is a measure of how well we are doing in predicting the values of t for new data observations of  $x$ . We note from Figure 1.5 that small values of  $M$  give relatively large values of the test set error, and this can be attributed to the fact that the corresponding polynomials are rather inflexible and are incapable of capturing the oscillations in the function  $sin(2\pi x)$ . Values of M in the range  $3 \le M \le 8$ give small values for the test set error, and these also give reasonable representations of the generating function  $sin(2\pi x)$ , as can be seen, for the case of  $M = 3$ , from Figure 1.4.

**Figure 1.5** Graphs of the root-mean-square error, defined by (1.3), evaluated on the training set and on an independent test set for various values of M.

Image /page/7/Figure/2 description: The image is a line graph showing the relationship between M on the x-axis and ERMS on the y-axis. There are two lines: a blue line labeled 'Training' and a red line labeled 'Test'. The training line starts at approximately 0.45 at M=0, decreases to about 0.35 at M=1, then to about 0.3 at M=2, and continues to decrease slightly to about 0.2 at M=3. It then stays relatively flat around 0.2 from M=3 to M=8, before dropping to 0 at M=9. The test line starts at approximately 0.58 at M=0, decreases to about 0.43 at M=1, then to about 0.42 at M=2. It then decreases to about 0.33 at M=3 and stays relatively flat around 0.33 from M=3 to M=8, before sharply increasing to approximately 0.95 at M=9.

For  $M = 9$ , the training set error goes to zero, as we might expect because this polynomial contains 10 degrees of freedom corresponding to the 10 coefficients  $w_0, \ldots, w_9$ , and so can be tuned exactly to the 10 data points in the training set. However, the test set error has become very large and, as we saw in Figure 1.4, the corresponding function  $y(x, \mathbf{w}^*)$  exhibits wild oscillations.

This may seem paradoxical because a polynomial of given order contains all lower order polynomials as special cases. The  $M = 9$  polynomial is therefore capable of generating results at least as good as the  $M = 3$  polynomial. Furthermore, we might suppose that the best predictor of new data would be the function  $sin(2\pi x)$ from which the data was generated (and we shall see later that this is indeed the case). We know that a power series expansion of the function  $sin(2\pi x)$  contains terms of all orders, so we might expect that results should improve monotonically as we increase M.

We can gain some insight into the problem by examining the values of the coefficients  $w^*$  obtained from polynomials of various order, as shown in Table 1.1. We see that, as  $M$  increases, the magnitude of the coefficients typically gets larger. In particular for the  $M = 9$  polynomial, the coefficients have become finely tuned to the data by developing large positive and negative values so that the correspond-

**Table 1.1** Table of the coefficients  $w^*$  for polynomials of various order. Observe how the typical magnitude of the coefficients increases dramatically as the order of the polynomial increases.

|         | $M=0$ | $M=1$   | $M=6$    | $M=9$         |
|---------|-------|---------|----------|---------------|
| $w_0^*$ | 0.19  | 0.82    | 0.31     | 0.35          |
| $w_1^*$ |       | $-1.27$ | 7.99     | 232.37        |
| $w_2^*$ |       |         | $-25.43$ | $-5321.83$    |
| $w_3^*$ |       |         | 17.37    | 48568.31      |
| $w_4^*$ |       |         |          | $-231639.30$  |
| $w_5^*$ |       |         |          | 640042.26     |
| $w_6^*$ |       |         |          | $-1061800.52$ |
| $w_7^*$ |       |         |          | 1042400.18    |
| $w_8^*$ |       |         |          | -557682.99    |
| $w_9^*$ |       |         |          | 125201.43     |

Image /page/8/Figure/1 description: The image displays two scatter plots with fitted curves, comparing the effect of sample size on model fitting. The left plot, labeled "N = 15", shows 15 data points scattered around a sinusoidal curve, with a red curve and a green curve fitted to these points. The red curve appears to be a higher-order polynomial fit that closely follows the data points, while the green curve is a smoother, lower-order fit. The right plot, labeled "N = 100", shows 100 data points scattered around a similar sinusoidal curve. Both plots have the x-axis labeled from 0 to 1 and the y-axis labeled from -1 to 1, with the y-axis also labeled as "t". The fitted curves in the right plot are much closer to each other and to the underlying sinusoidal trend, indicating a better fit with a larger sample size.

**Figure 1.6** Plots of the solutions obtained by minimizing the sum-of-squares error function using the  $M = 9$ polynomial for  $N = 15$  data points (left plot) and  $N = 100$  data points (right plot). We see that increasing the size of the data set reduces the over-fitting problem.

ing polynomial function matches each of the data points exactly, but between data points (particularly near the ends of the range) the function exhibits the large oscillations observed in Figure 1.4. Intuitively, what is happening is that the more flexible polynomials with larger values of  $M$  are becoming increasingly tuned to the random noise on the target values.

It is also interesting to examine the behaviour of a given model as the size of the data set is varied, as shown in Figure 1.6. We see that, for a given model complexity, the over-fitting problem become less severe as the size of the data set increases. Another way to say this is that the larger the data set, the more complex (in other words more flexible) the model that we can afford to fit to the data. One rough heuristic that is sometimes advocated is that the number of data points should be no less than some multiple (say 5 or 10) of the number of adaptive parameters in the model. However, as we shall see in Chapter 3, the number of parameters is not necessarily the most appropriate measure of model complexity.

Also, there is something rather unsatisfying about having to limit the number of parameters in a model according to the size of the available training set. It would seem more reasonable to choose the complexity of the model according to the complexity of the problem being solved. We shall see that the least squares approach to finding the model parameters represents a specific case of *maximum likelihood* (discussed in Section 1.2.5), and that the over-fitting problem can be understood as *Section 3.4* a general property of maximum likelihood. By adopting a *Bayesian* approach, the over-fitting problem can be avoided. We shall see that there is no difficulty from a Bayesian perspective in employing models for which the number of parameters greatly exceeds the number of data points. Indeed, in a Bayesian model the *effective* number of parameters adapts automatically to the size of the data set.

> For the moment, however, it is instructive to continue with the current approach and to consider how in practice we can apply it to data sets of limited size where we

*Section 3.4*

Image /page/9/Figure/1 description: The image displays two plots side-by-side, each showing a scatter plot of blue circles representing data points, and two fitted curves: one red and one green. The left plot is labeled "ln \lambda = -18" and shows a green curve that closely follows the data points, while the red curve is a smoother, less accurate fit. The right plot is labeled "ln \lambda = 0" and shows a green curve that is less flexible and does not fit the data as well as the red curve, which appears to be a better fit in this case. Both plots have 'x' on the horizontal axis and 't' on the vertical axis, with the vertical axis ranging from -1 to 1.

**Figure 1.7** Plots of  $M = 9$  polynomials fitted to the data set shown in Figure 1.2 using the regularized error function (1.4) for two values of the regularization parameter  $\lambda$  corresponding to  $\ln \lambda = -18$  and  $\ln \lambda = 0$ . The case of no regularizer, i.e.,  $\lambda = 0$ , corresponding to ln  $\lambda = -\infty$ , is shown at the bottom right of Figure 1.4.

may wish to use relatively complex and flexible models. One technique that is often used to control the over-fitting phenomenon in such cases is that of *regularization*, which involves adding a penalty term to the error function  $(1.2)$  in order to discourage the coefficients from reaching large values. The simplest such penalty term takes the form of a sum of squares of all of the coefficients, leading to a modified error function of the form

$$
\widetilde{E}(\mathbf{w}) = \frac{1}{2} \sum_{n=1}^{N} \left\{ y(x_n, \mathbf{w}) - t_n \right\}^2 + \frac{\lambda}{2} ||\mathbf{w}||^2
$$
\n(1.4)

where  $\|\mathbf{w}\|^2 \equiv \mathbf{w}^T \mathbf{w} = w_0^2 + w_1^2 + \ldots + w_M^2$ , and the coefficient  $\lambda$  governs the relative importance of the regularization term compared with the sum-of-squares error ative importance of the regularization term compared with the sum-of-squares error term. Note that often the coefficient  $w_0$  is omitted from the regularizer because its inclusion causes the results to depend on the choice of origin for the target variable (Hastie *et al.*, 2001), or it may be included but with its own regularization coefficient (we shall discuss this topic in more detail in Section 5.5.1). Again, the error function *Exercise 1.2* in (1.4) can be minimized exactly in closed form. Techniques such as this are known in the statistics literature as *shrinkage* methods because they reduce the value of the coefficients. The particular case of a quadratic regularizer is called *ridge regression* (Hoerl and Kennard, 1970). In the context of neural networks, this approach is known as *weight decay*.

> Figure 1.7 shows the results of fitting the polynomial of order  $M = 9$  to the same data set as before but now using the regularized error function given by (1.4). We see that, for a value of  $\ln \lambda = -18$ , the over-fitting has been suppressed and we now obtain a much closer representation of the underlying function  $sin(2\pi x)$ . If, however, we use too large a value for  $\lambda$  then we again obtain a poor fit, as shown in Figure 1.7 for  $\ln \lambda = 0$ . The corresponding coefficients from the fitted polynomials are given in Table 1.2, showing that regularization has the desired effect of reducing

### 1.1. Example: Polynomial Curve Fitting

**Table 1.2** Table of the coefficients  $w^*$  for  $M =$ 9 polynomials with various values for the regularization parameter  $\lambda$ . Note that  $\ln \lambda = -\infty$  corresponds to a model with no regularization, i.e., to the graph at the bottom right in Figure 1.4. We see that, as the value of  $\lambda$  increases, the typical magnitude of the coefficients gets smaller.

|               | $\ln \lambda = -\infty$ | $\ln \lambda = -18$ | $\ln \lambda = 0$ |
|---------------|-------------------------|---------------------|-------------------|
| $w_0^{\star}$ | 0.35                    | 0.35                | 0.13              |
| $w_1^*$       | 232.37                  | 4.74                | -0.05             |
| $w_2^{\star}$ | -5321.83                | -0.77               | -0.06             |
| $w_3^{\star}$ | 48568.31                | -31.97              | -0.05             |
| $w_4^{\star}$ | -231639.30              | -3.89               | -0.03             |
| $w_5^{\star}$ | 640042.26               | 55.28               | -0.02             |
| $w_6^{\star}$ | -1061800.52             | 41.32               | -0.01             |
| $w_7^{\star}$ | 1042400.18              | -45.95              | -0.00             |
| $w_8^{\star}$ | -557682.99              | -91.53              | 0.00              |
| $w_9^{\star}$ | 125201.43               | 72.68               | 0.01              |

the magnitude of the coefficients.

The impact of the regularization term on the generalization error can be seen by plotting the value of the RMS error (1.3) for both training and test sets against  $\ln \lambda$ , as shown in Figure 1.8. We see that in effect  $\lambda$  now controls the effective complexity of the model and hence determines the degree of over-fitting.

The issue of model complexity is an important one and will be discussed at length in Section 1.3. Here we simply note that, if we were trying to solve a practical application using this approach of minimizing an error function, we would have to find a way to determine a suitable value for the model complexity. The results above suggest a simple way of achieving this, namely by taking the available data and partitioning it into a training set, used to determine the coefficients **w**, and a separate *validation* set, also called a *hold-out* set, used to optimize the model complexity (either M or  $\lambda$ ). In many cases, however, this will prove to be too wasteful of *Section 1.3* valuable training data, and we have to seek more sophisticated approaches.

> So far our discussion of polynomial curve fitting has appealed largely to intuition. We now seek a more principled approach to solving problems in pattern recognition by turning to a discussion of probability theory. As well as providing the foundation for nearly all of the subsequent developments in this book, it will also

**Figure 1.8** Graph of the root-mean-square error (1.3) versus  $\ln \lambda$  for the  $M = 9$ polynomial.

Image /page/10/Figure/8 description: The image is a plot showing the relationship between ERMS and ln lambda. The x-axis is labeled "ln \u03bb" and ranges from -35 to -20. The y-axis is labeled "ERMS" and ranges from 0 to 1. There are two lines plotted: a blue line labeled "Training" and a red line labeled "Test". The training error starts at 0 and increases to about 0.25 at ln lambda = -30, then increases further to about 0.4 at ln lambda = -20. The test error starts at about 0.7 at ln lambda = -35, decreases to about 0.2 at ln lambda = -30, and then increases to about 0.5 at ln lambda = -20.

give us some important insights into the concepts we have introduced in the context of polynomial curve fitting and will allow us to extend these to more complex situations.

# 1.2. Probability Theory

A key concept in the field of pattern recognition is that of uncertainty. It arises both through noise on measurements, as well as through the finite size of data sets. Probability theory provides a consistent framework for the quantification and manipulation of uncertainty and forms one of the central foundations for pattern recognition. When combined with decision theory, discussed in Section 1.5, it allows us to make optimal predictions given all the information available to us, even though that information may be incomplete or ambiguous.

We will introduce the basic concepts of probability theory by considering a simple example. Imagine we have two boxes, one red and one blue, and in the red box we have 2 apples and 6 oranges, and in the blue box we have 3 apples and 1 orange. This is illustrated in Figure 1.9. Now suppose we randomly pick one of the boxes and from that box we randomly select an item of fruit, and having observed which sort of fruit it is we replace it in the box from which it came. We could imagine repeating this process many times. Let us suppose that in so doing we pick the red box 40% of the time and we pick the blue box 60% of the time, and that when we remove an item of fruit from a box we are equally likely to select any of the pieces of fruit in the box.

In this example, the identity of the box that will be chosen is a random variable, which we shall denote by  $B$ . This random variable can take one of two possible values, namely  $r$  (corresponding to the red box) or  $b$  (corresponding to the blue box). Similarly, the identity of the fruit is also a random variable and will be denoted by F. It can take either of the values  $\alpha$  (for apple) or  $\alpha$  (for orange).

To begin with, we shall define the probability of an event to be the fraction of times that event occurs out of the total number of trials, in the limit that the total number of trials goes to infinity. Thus the probability of selecting the red box is  $4/10$ 

**Figure 1.9** We use a simple example of two coloured boxes each containing fruit (apples shown in green and oranges shown in orange) to introduce the basic ideas of probability.

Image /page/11/Picture/8 description: Two containers are shown. The left container is red and contains two green circles on top and five orange circles below them. The right container is blue and contains one orange circle on top and three green circles below it.

**Figure 1.10** We can derive the sum and product rules of probability by considering two random variables, X, which takes the values  $\{x_i\}$  where  $i = 1, \ldots, M$ , and Y, which takes the values  $\{y_i\}$  where  $j = 1, \ldots, L$ . In this illustration we have  $M = 5$  and  $L = 3$ . If we consider a total number  $N$  of instances of these variables, then we denote the number of instances where  $X = x_i$  and  $Y = y_j$  by  $n_{ij}$ , which is the number of  $y_j$ points in the corresponding cell of the array. The number of points in column i, corresponding to  $X = x_i$ , is denoted by  $c_i$ , and the number of points in row j, corresponding to  $Y = y_i$ , is denoted by  $r_i$ .

Image /page/12/Figure/2 description: A grid of 12 squares, arranged in 3 rows and 4 columns, is outlined in red. Labels are placed around the grid. The label 'y\_j' is to the left of the second row. The label 'r\_j' is to the right of the second row, with a bracket encompassing the height of the second row. The label 'c\_i' is above the third column, with a bracket encompassing the width of the third column. The label 'x\_i' is below the grid, centered under the fourth column. The label 'n\_ij' is in the center of the square in the second row and third column.

and the probability of selecting the blue box is  $6/10$ . We write these probabilities as  $p(B = r) = 4/10$  and  $p(B = b) = 6/10$ . Note that, by definition, probabilities must lie in the interval  $[0, 1]$ . Also, if the events are mutually exclusive and if they include all possible outcomes (for instance, in this example the box must be either red or blue), then we see that the probabilities for those events must sum to one.

We can now ask questions such as: "what is the overall probability that the selection procedure will pick an apple?", or "given that we have chosen an orange, what is the probability that the box we chose was the blue one?". We can answer questions such as these, and indeed much more complex questions associated with problems in pattern recognition, once we have equipped ourselves with the two elementary rules of probability, known as the *sum rule* and the *product rule*. Having obtained these rules, we shall then return to our boxes of fruit example.

In order to derive the rules of probability, consider the slightly more general example shown in Figure 1.10 involving two random variables  $X$  and  $Y$  (which could for instance be the Box and Fruit variables considered above). We shall suppose that X can take any of the values  $x_i$  where  $i = 1, \ldots, M$ , and Y can take the values  $y_i$ where  $j = 1, \ldots, L$ . Consider a total of N trials in which we sample both of the variables X and Y, and let the number of such trials in which  $X = x_i$  and  $Y = y_j$ be  $n_{ij}$ . Also, let the number of trials in which X takes the value  $x_i$  (irrespective of the value that Y takes) be denoted by  $c_i$ , and similarly let the number of trials in which Y takes the value  $y_i$  be denoted by  $r_i$ .

The probability that X will take the value  $x_i$  and Y will take the value  $y_j$  is written  $p(X = x_i, Y = y_j)$  and is called the *joint* probability of  $X = x_i$  and  $Y = y_i$ . It is given by the number of points falling in the cell i,j as a fraction of the total number of points, and hence

$$
p(X = x_i, Y = y_j) = \frac{n_{ij}}{N}.
$$
\n(1.5)

Here we are implicitly considering the limit  $N \to \infty$ . Similarly, the probability that X takes the value  $x_i$  irrespective of the value of Y is written as  $p(X = x_i)$  and is given by the fraction of the total number of points that fall in column  $i$ , so that

$$
p(X = x_i) = \frac{c_i}{N}.\tag{1.6}
$$

Because the number of instances in column  $i$  in Figure 1.10 is just the sum of the number of instances in each cell of that column, we have  $c_i = \sum_j n_{ij}$  and therefore,

from  $(1.5)$  and  $(1.6)$ , we have

$$
p(X = x_i) = \sum_{j=1}^{L} p(X = x_i, Y = y_j)
$$
\n(1.7)

which is the *sum rule* of probability. Note that  $p(X = x_i)$  is sometimes called the *marginal* probability, because it is obtained by marginalizing, or summing out, the other variables (in this case  $Y$ ).

If we consider only those instances for which  $X = x_i$ , then the fraction of such instances for which  $Y = y_j$  is written  $p(Y = y_j | X = x_i)$  and is called the *conditional* probability of  $Y = y_i$  given  $X = x_i$ . It is obtained by finding the fraction of those points in column i that fall in cell  $i, j$  and hence is given by

$$
p(Y = y_j | X = x_i) = \frac{n_{ij}}{c_i}.
$$
\n(1.8)

From  $(1.5)$ ,  $(1.6)$ , and  $(1.8)$ , we can then derive the following relationship

$$
p(X = x_i, Y = y_j) = \frac{n_{ij}}{N} = \frac{n_{ij}}{c_i} \cdot \frac{c_i}{N}
$$
  
$$
= p(Y = y_j | X = x_i) p(X = x_i) (1.9)
$$

which is the *product rule* of probability.

So far we have been quite careful to make a distinction between a random variable, such as the box  $B$  in the fruit example, and the values that the random variable can take, for example r if the box were the red one. Thus the probability that  $B$  takes the value r is denoted  $p(B = r)$ . Although this helps to avoid ambiguity, it leads to a rather cumbersome notation, and in many cases there will be no need for such pedantry. Instead, we may simply write  $p(B)$  to denote a distribution over the random variable B, or  $p(r)$  to denote the distribution evaluated for the particular value r, provided that the interpretation is clear from the context.

With this more compact notation, we can write the two fundamental rules of probability theory in the following form.

#### The Rules of Probability

sum rule 
$$
p(X) = \sum_{Y} p(X, Y) \tag{1.10}
$$

**product rule** 
$$
p(X,Y) = p(Y|X)p(X).
$$
 (1.11)

Here  $p(X, Y)$  is a joint probability and is verbalized as "the probability of X and Y". Similarly, the quantity  $p(Y|X)$  is a conditional probability and is verbalized as "the probability of Y *given* X", whereas the quantity  $p(X)$  is a marginal probability

and is simply "the probability of  $X$ ". These two simple rules form the basis for all of the probabilistic machinery that we use throughout this book.

From the product rule, together with the symmetry property  $p(X, Y) = p(Y, X)$ , we immediately obtain the following relationship between conditional probabilities

$$
p(Y|X) = \frac{p(X|Y)p(Y)}{p(X)}
$$
\n(1.12)

which is called *Bayes' theorem* and which plays a central role in pattern recognition and machine learning. Using the sum rule, the denominator in Bayes' theorem can be expressed in terms of the quantities appearing in the numerator

$$
p(X) = \sum_{Y} p(X|Y)p(Y).
$$
 (1.13)

We can view the denominator in Bayes' theorem as being the normalization constant required to ensure that the sum of the conditional probability on the left-hand side of  $(1.12)$  over all values of Y equals one.

In Figure 1.11, we show a simple example involving a joint distribution over two variables to illustrate the concept of marginal and conditional distributions. Here a finite sample of  $N = 60$  data points has been drawn from the joint distribution and is shown in the top left. In the top right is a histogram of the fractions of data points having each of the two values of  $Y$ . From the definition of probability, these fractions would equal the corresponding probabilities  $p(Y)$  in the limit  $N \to \infty$ . We can view the histogram as a simple way to model a probability distribution given only a finite number of points drawn from that distribution. Modelling distributions from data lies at the heart of statistical pattern recognition and will be explored in great detail in this book. The remaining two plots in Figure 1.11 show the corresponding histogram estimates of  $p(X)$  and  $p(X|Y = 1)$ .

Let us now return to our example involving boxes of fruit. For the moment, we shall once again be explicit about distinguishing between the random variables and their instantiations. We have seen that the probabilities of selecting either the red or the blue boxes are given by

$$
p(B = r) = 4/10 \tag{1.14}
$$

$$
p(B = b) = 6/10 \tag{1.15}
$$

respectively. Note that these satisfy  $p(B = r) + p(B = b) = 1$ .

Now suppose that we pick a box at random, and it turns out to be the blue box. Then the probability of selecting an apple is just the fraction of apples in the blue box which is 3/4, and so  $p(F = a|B = b) = 3/4$ . In fact, we can write out all four conditional probabilities for the type of fruit, given the selected box

$$
p(F = a|B = r) = 1/4 \tag{1.16}
$$

$$
p(F = o|B = r) = 3/4 \tag{1.17}
$$

$$
p(F = a|B = b) = 3/4 \tag{1.18}
$$

$$
p(F = o|B = b) = 1/4.
$$
 (1.19)

Image /page/15/Figure/1 description: This figure displays four plots related to probability distributions. The top-left plot, labeled "p(X,Y)", shows a scatter plot of points divided into two horizontal regions labeled "Y=2" and "Y=1". Vertical red lines divide the plot into several bins along the X-axis. The top-right plot, labeled "p(Y)", is a bar chart with two horizontal bars representing the distribution of Y. The bottom-left plot, labeled "p(X)", is a bar chart showing the distribution of X. The bottom-right plot, labeled "p(X|Y=1)", is a bar chart illustrating the conditional distribution of X given Y=1.

**Figure 1.11** An illustration of a distribution over two variables, X, which takes 9 possible values, and Y, which takes two possible values. The top left figure shows a sample of 60 points drawn from a joint probability distribution over these variables. The remaining figures show histogram estimates of the marginal distributions  $p(X)$ and  $p(Y)$ , as well as the conditional distribution  $p(X|Y = 1)$  corresponding to the bottom row in the top left figure.

Again, note that these probabilities are normalized so that

$$
p(F = a|B = r) + p(F = o|B = r) = 1
$$
\n(1.20)

and similarly

$$
p(F = a|B = b) + p(F = o|B = b) = 1.
$$
\n(1.21)

We can now use the sum and product rules of probability to evaluate the overall probability of choosing an apple

$$
p(F = a) = p(F = a|B = r)p(B = r) + p(F = a|B = b)p(B = b)
$$
  
=  $\frac{1}{4} \times \frac{4}{10} + \frac{3}{4} \times \frac{6}{10} = \frac{11}{20}$  (1.22)

from which it follows, using the sum rule, that  $p(F = 0) = 1 - 11/20 = 9/20$ .

#### 1.2. Probability Theory

Suppose instead we are told that a piece of fruit has been selected and it is an orange, and we would like to know which box it came from. This requires that we evaluate the probability distribution over boxes conditioned on the identity of the fruit, whereas the probabilities in  $(1.16)$ – $(1.19)$  give the probability distribution over the fruit conditioned on the identity of the box. We can solve the problem of reversing the conditional probability by using Bayes' theorem to give

$$
p(B=r|F=o) = \frac{p(F=o|B=r)p(B=r)}{p(F=o)} = \frac{3}{4} \times \frac{4}{10} \times \frac{20}{9} = \frac{2}{3}.
$$
 (1.23)

From the sum rule, it then follows that  $p(B = b|F = o) = 1 - 2/3 = 1/3$ .

We can provide an important interpretation of Bayes' theorem as follows. If we had been asked which box had been chosen before being told the identity of the selected item of fruit, then the most complete information we have available is provided by the probability p(B). We call this the *prior probability* because it is the probability available *before* we observe the identity of the fruit. Once we are told that the fruit is an orange, we can then use Bayes' theorem to compute the probability  $p(B|F)$ , which we shall call the *posterior probability* because it is the probability obtained *after* we have observed F. Note that in this example, the prior probability of selecting the red box was  $4/10$ , so that we were more likely to select the blue box than the red one. However, once we have observed that the piece of selected fruit is an orange, we find that the posterior probability of the red box is now  $2/3$ , so that it is now more likely that the box we selected was in fact the red one. This result accords with our intuition, as the proportion of oranges is much higher in the red box than it is in the blue box, and so the observation that the fruit was an orange provides significant evidence favouring the red box. In fact, the evidence is sufficiently strong that it outweighs the prior and makes it more likely that the red box was chosen rather than the blue one.

Finally, we note that if the joint distribution of two variables factorizes into the product of the marginals, so that  $p(X, Y) = p(X)p(Y)$ , then X and Y are said to be *independent*. From the product rule, we see that  $p(Y|X) = p(Y)$ , and so the conditional distribution of Y given X is indeed independent of the value of X. For instance, in our boxes of fruit example, if each box contained the same fraction of apples and oranges, then  $p(F|B) = P(F)$ , so that the probability of selecting, say, an apple is independent of which box is chosen.

#### 1.2.1 Probability densities

As well as considering probabilities defined over discrete sets of events, we also wish to consider probabilities with respect to continuous variables. We shall limit ourselves to a relatively informal discussion. If the probability of a real-valued variable x falling in the interval  $(x, x + \delta x)$  is given by  $p(x)\delta x$  for  $\delta x \to 0$ , then  $p(x)$  is called the *probability density* over x. This is illustrated in Figure 1.12. The probability that x will lie in an interval  $(a, b)$  is then given by

$$
p(x \in (a, b)) = \int_{a}^{b} p(x) dx.
$$
 (1.24)

**Figure 1.12** The concept of probability for discrete variables can be extended to that of a probability density  $p(x)$  over a continuous variable  $x$  and is such that the probability of  $x$  lying in the interval  $(x, x + \delta x)$  is given by  $p(x)\delta x$ for  $\delta x \rightarrow 0$ . The probability density can be expressed as the derivative of a cumulative distribution function  $P(x)$ .

Image /page/17/Figure/2 description: The image displays a graph with the x-axis labeled 'x' and the y-axis unlabeled. Two curves are plotted: a red curve labeled 'p(x)' and a blue curve labeled 'P(x)'. The red curve, representing a probability density function, has a bimodal shape with two peaks. The blue curve, representing a cumulative distribution function, is monotonically increasing and appears to approach a plateau. A shaded green rectangle is shown on the graph, indicating a small interval 'δx' on the x-axis, positioned under the first peak of the red curve.

Because probabilities are nonnegative, and because the value of  $x$  must lie somewhere on the real axis, the probability density  $p(x)$  must satisfy the two conditions

<sup>∞</sup>

$$
p(x) \geqslant 0 \tag{1.25}
$$

$$
\int_{-\infty}^{\infty} p(x) dx = 1. \tag{1.26}
$$

Under a nonlinear change of variable, a probability density transforms differently from a simple function, due to the Jacobian factor. For instance, if we consider a change of variables  $x = g(y)$ , then a function  $f(x)$  becomes  $f(y) = f(g(y))$ . Now consider a probability density  $p_x(x)$  that corresponds to a density  $p_y(y)$  with respect to the new variable y, where the suffices denote the fact that  $p_x(x)$  and  $p_y(y)$ are different densities. Observations falling in the range  $(x, x + \delta x)$  will, for small values of  $\delta x$ , be transformed into the range  $(y, y + \delta y)$  where  $p_x(x)\delta x \simeq p_y(y)\delta y$ , and hence

$$
p_y(y) = p_x(x) \left| \frac{dx}{dy} \right|
$$

$$
= p_x(g(y)) |g'(y)| \quad (1.27)
$$

One consequence of this property is that the concept of the maximum of a probability *Exercise 1.4* density is dependent on the choice of variable.

The probability that x lies in the interval  $(-\infty, z)$  is given by the *cumulative distribution function* defined by

$$
P(z) = \int_{-\infty}^{z} p(x) dx
$$
 (1.28)

which satisfies  $P'(x) = p(x)$ , as shown in Figure 1.12.

If we have several continuous variables  $x_1, \ldots, x_D$ , denoted collectively by the vector **x**, then we can define a joint probability density  $p(\mathbf{x}) = p(x_1, \ldots, x_D)$  such

*Exercise 1.4*

that the probability of **x** falling in an infinitesimal volume  $\delta$ **x** containing the point **x** is given by  $p(x)\delta x$ . This multivariate probability density must satisfy

$$
p(\mathbf{x}) \geqslant 0 \tag{1.29}
$$

$$
\int p(\mathbf{x}) \, \mathrm{d}\mathbf{x} = 1 \tag{1.30}
$$

in which the integral is taken over the whole of **x** space. We can also consider joint probability distributions over a combination of discrete and continuous variables.

Note that if x is a discrete variable, then  $p(x)$  is sometimes called a *probability mass function* because it can be regarded as a set of 'probability masses' concentrated at the allowed values of  $x$ .

The sum and product rules of probability, as well as Bayes' theorem, apply equally to the case of probability densities, or to combinations of discrete and continuous variables. For instance, if  $x$  and  $y$  are two real variables, then the sum and product rules take the form

$$
p(x) = \int p(x, y) dy
$$
 (1.31)

$$
p(x, y) = p(y|x)p(x).
$$
 (1.32)

A formal justification of the sum and product rules for continuous variables (Feller, 1966) requires a branch of mathematics called *measure theory* and lies outside the scope of this book. Its validity can be seen informally, however, by dividing each real variable into intervals of width  $\Delta$  and considering the discrete probability distribution over these intervals. Taking the limit  $\Delta \rightarrow 0$  then turns sums into integrals and gives the desired result.

# 1.2.2 Expectations and covariances

One of the most important operations involving probabilities is that of finding weighted averages of functions. The average value of some function  $f(x)$  under a probability distribution  $p(x)$  is called the *expectation* of  $f(x)$  and will be denoted by  $\mathbb{E}[f]$ . For a discrete distribution, it is given by

$$
\mathbb{E}[f] = \sum_{x} p(x)f(x) \tag{1.33}
$$

so that the average is weighted by the relative probabilities of the different values of x. In the case of continuous variables, expectations are expressed in terms of an integration with respect to the corresponding probability density

$$
\mathbb{E}[f] = \int p(x)f(x) \, dx.
$$
 (1.34)

In either case, if we are given a finite number  $N$  of points drawn from the probability distribution or probability density, then the expectation can be approximated as a

finite sum over these points

$$
\mathbb{E}[f] \simeq \frac{1}{N} \sum_{n=1}^{N} f(x_n). \tag{1.35}
$$

We shall make extensive use of this result when we discuss sampling methods in Chapter 11. The approximation in (1.35) becomes exact in the limit  $N \to \infty$ .

Sometimes we will be considering expectations of functions of several variables, in which case we can use a subscript to indicate which variable is being averaged over, so that for instance

$$
\mathbb{E}_x[f(x,y)]\tag{1.36}
$$

denotes the average of the function  $f(x, y)$  with respect to the distribution of x. Note that  $\mathbb{E}_x[f(x, y)]$  will be a function of y.

We can also consider a *conditional expectation* with respect to a conditional distribution, so that

$$
\mathbb{E}_x[f|y] = \sum_x p(x|y)f(x) \tag{1.37}
$$

with an analogous definition for continuous variables.

The *variance* of  $f(x)$  is defined by

$$
\text{var}[f] = \mathbb{E}\left[\left(f(x) - \mathbb{E}[f(x)]\right)^2\right] \tag{1.38}
$$

and provides a measure of how much variability there is in  $f(x)$  around its mean value  $\mathbb{E}[f(x)]$ . Expanding out the square, we see that the variance can also be written *Exercise 1.5* in terms of the expectations of  $f(x)$  and  $f(x)^2$ 

$$
var[f] = \mathbb{E}[f(x)^{2}] - \mathbb{E}[f(x)]^{2}.
$$
 (1.39)

In particular, we can consider the variance of the variable  $x$  itself, which is given by

$$
\text{var}[x] = \mathbb{E}[x^2] - \mathbb{E}[x]^2. \tag{1.40}
$$

For two random variables x and y, the *covariance* is defined by

$$
\begin{array}{rcl}\n\text{cov}[x, y] & = & \mathbb{E}_{x, y} \left[ \{ x - \mathbb{E}[x] \} \{ y - \mathbb{E}[y] \} \right] \\
& = & \mathbb{E}_{x, y} [xy] - \mathbb{E}[x] \mathbb{E}[y]\n\end{array} \tag{1.41}
$$

*Exercise 1.6*

which expresses the extent to which x and y vary together. If x and y are indepen-*Exercise 1.6* dent, then their covariance vanishes.

In the case of two vectors of random variables **x** and **y**, the covariance is a matrix

cov
$$
[\mathbf{x}, \mathbf{y}]
$$
 =  $\mathbb{E}_{\mathbf{x}, \mathbf{y}} [\{\mathbf{x} - \mathbb{E}[\mathbf{x}]\} \{\mathbf{y}^{\mathrm{T}} - \mathbb{E}[\mathbf{y}^{\mathrm{T}}] \}]$   
 =  $\mathbb{E}_{\mathbf{x}, \mathbf{y}} [\mathbf{x} \mathbf{y}^{\mathrm{T}}] - \mathbb{E}[\mathbf{x}] \mathbb{E}[\mathbf{y}^{\mathrm{T}}].$  (1.42)

If we consider the covariance of the components of a vector **x** with each other, then we use a slightly simpler notation  $cov[\mathbf{x}] \equiv cov[\mathbf{x}, \mathbf{x}]$ .

# 1.2.3 Bayesian probabilities

So far in this chapter, we have viewed probabilities in terms of the frequencies of random, repeatable events. We shall refer to this as the *classical* or *frequentist* interpretation of probability. Now we turn to the more general *Bayesian* view, in which probabilities provide a quantification of uncertainty.

Consider an uncertain event, for example whether the moon was once in its own orbit around the sun, or whether the Arctic ice cap will have disappeared by the end of the century. These are not events that can be repeated numerous times in order to define a notion of probability as we did earlier in the context of boxes of fruit. Nevertheless, we will generally have some idea, for example, of how quickly we think the polar ice is melting. If we now obtain fresh evidence, for instance from a new Earth observation satellite gathering novel forms of diagnostic information, we may revise our opinion on the rate of ice loss. Our assessment of such matters will affect the actions we take, for instance the extent to which we endeavour to reduce the emission of greenhouse gasses. In such circumstances, we would like to be able to quantify our expression of uncertainty and make precise revisions of uncertainty in the light of new evidence, as well as subsequently to be able to take optimal actions or decisions as a consequence. This can all be achieved through the elegant, and very general, Bayesian interpretation of probability.

The use of probability to represent uncertainty, however, is not an ad-hoc choice, but is inevitable if we are to respect common sense while making rational coherent inferences. For instance, Cox (1946) showed that if numerical values are used to represent degrees of belief, then a simple set of axioms encoding common sense properties of such beliefs leads uniquely to a set of rules for manipulating degrees of belief that are equivalent to the sum and product rules of probability. This provided the first rigorous proof that probability theory could be regarded as an extension of Boolean logic to situations involving uncertainty (Jaynes, 2003). Numerous other authors have proposed different sets of properties or axioms that such measures of uncertainty should satisfy (Ramsey, 1931; Good, 1950; Savage, 1961; deFinetti, 1970; Lindley, 1982). In each case, the resulting numerical quantities behave precisely according to the rules of probability. It is therefore natural to refer to these quantities as (Bayesian) probabilities.

In the field of pattern recognition, too, it is helpful to have a more general no-

Image /page/20/Picture/6 description: A black and white portrait of a man in clerical attire. He has dark, wavy hair, a round face, and is wearing a dark robe with a white collar. The image is framed by a light purple border.

# Thomas Bayes 1701–1761

Thomas Bayes was born in Tunbridge Wells and was a clergyman as well as an amateur scientist and a mathematician. He studied logic and theology at Edinburgh University and was elected Fellow of the

Royal Society in 1742. During the  $18<sup>th</sup>$  century, issues regarding probability arose in connection with

gambling and with the new concept of insurance. One particularly important problem concerned so-called inverse probability. A solution was proposed by Thomas Bayes in his paper 'Essay towards solving a problem in the doctrine of chances', which was published in 1764, some three years after his death, in the Philosophical Transactions of the Royal Society. In fact, Bayes only formulated his theory for the case of a uniform prior, and it was Pierre-Simon Laplace who independently rediscovered the theory in general form and who demonstrated its broad applicability.
## Short-time behavior

A popular and useful topic in the study of diffusion processes consists in establishing regularization estimates in short time. Typically, a certain functional used to quantify the regularity of the solution (for instance, the supremum of the unknown or some Lebesgue or Sobolev norm) is shown to be bounded like  $O(t^{-\kappa})$  for some characteristic exponent  $\kappa$ , independent of the initial datum (or depending only on certain weak estimates on the initial datum), when  $t > 0$  is small enough. Here I shall present some slightly unconventional estimates of this type.

Theorem 24.16 (Short-time regularization for gradient flows). Let M be a Riemannian manifold satisfying a curvature-dimension bound  $CD(K, \infty)$ ,  $K \in \mathbb{R}$ ; let  $\nu = e^{-V}$ vol  $\in P_2(M)$ , with  $V \in C^4(M)$ , and let  $U \in \mathcal{DC}_{\infty}$  with  $U(1) = 0$ . Further, let  $(\mu_t)_{t>0}$  be a smooth solution of (24.1). Then:

(i) If  $K \geq 0$  then for any  $t \geq 0$ ,

$$
t^{2} I_{U,\nu}(\mu_{t}) + 2t U_{\nu}(\mu_{t}) + W_{2}(\mu_{t},\nu)^{2} \leq W_{2}(\mu_{0},\nu)^{2}.
$$

In particular,

$$
U_{\nu}(\mu_t) \le \frac{W_2(\mu_0, \nu)^2}{2t},\tag{24.18}
$$

$$
I_{U,\nu}(\mu_t) \le \frac{W_2(\mu_0,\nu)^2}{t^2}.
$$
\n(24.19)

(ii) If  $K \geq 0$  and  $t \geq s > 0$ , then

$$
W_2(\mu_s, \mu_t) \le \min\left(\sqrt{2 \, U_\nu(\mu_s)} \, \sqrt{|t-s|}, \, \sqrt{I_{U,\nu}(\mu_s)} \, |t-s|\right) \quad (24.20)
$$

$$
\leq W_2(\mu_0, \nu) \min\left(\frac{\sqrt{|t-s|}}{\sqrt{s}}, \frac{|t-s|}{s}\right). \tag{24.21}
$$

(iii) If  $K < 0$ , the previous conclusions become

$$
U_{\nu}(\mu_{t}) \leq \frac{e^{2Ct} W_{2}(\mu_{0}, \nu)^{2}}{2t}; \qquad I_{U, \nu}(\mu_{t}) \leq \frac{e^{2Ct} W_{2}(\mu_{0}, \nu)^{2}}{t^{2}};
$$
$$
W_{2}(\mu_{s}, \mu_{t}) \leq e^{Ct} \min \left( \sqrt{2 U_{\nu}(\mu_{s})}\sqrt{|t-s|}, \sqrt{I_{U, \nu}(\mu_{s})}|t-s| \right)
$$
$$
\leq e^{2Ct} W_{2}(\mu_{0}, \nu) \min \left( \frac{\sqrt{|t-s|}}{\sqrt{s}}, \frac{|t-s|}{s} \right),
$$

with  $C = -K$ .

Particular Case 24.17. When  $U(\rho) = \rho \log \rho$ , inequalities (24.18) and (24.19) become

$$
H_{\nu}(\mu_t) \le \frac{W_2(\mu_0, \nu)^2}{2t}, \qquad I_{\nu}(\mu_t) \le \frac{W_2(\mu_0, \nu)^2}{t^2}.
$$
 (24.22)

Under a CD( $K, \infty$ ) bound ( $K < 0$ ) there is an additional factor  $e^{-2Kt}$ .

Remark 24.18. Theorem 24.16 should be thought of as an a priori estimate. If life is not unfair, one can then remove the assumption of smoothness by a density argument, and transform  $(24.18)$ ,  $(24.19)$  into genuine regularization estimates. This is true at least for the Particular Case 24.17.

Remark 24.19. Inequalities (24.20) and (24.21) establish the following estimates: The curve  $(\mu_t)_{t>0}$ , viewed as a function of time t, is Hölder-1/2 close to  $t = 0$ , and Lipschitz away from  $t = 0$ , if  $U_{\nu}(\mu_0)$  is finite. If  $I_{U,\nu}(\mu_0)$  is finite, then the curve is Lipschitz all along.

**Remark 24.20.** Theorem 24.7 gave upper bounds on  $U_{\nu}(\mu_t) - U_{\nu}(\nu)$ like  $O(e^{-\kappa t})$ , with a constant depending on  $U_{\nu}(\mu_0)$ . But now we can combine Theorem 24.7 with Theorem 24.16 to get an exponential decay with a constant that does not depend on  $U_{\nu}(\mu_0)$ , but only on  $W_2(\mu_0,\nu)$ . By approximation, this will lead to results of convergence that do not need the finiteness of  $U_{\nu}(\mu_0)$ .

Remark 24.21. I would bet that the estimates in (24.22) are optimal in general (although they would deserve more thinking) as far as the dependence on  $\mu_0$  and t is concerned. On the other hand, if  $\mu_0$  is given, these bounds are terrible estimates for the short-time behavior of the Kullback and Fisher informations as functions of just  $t$ . Indeed, the correct scale for the Kullback information  $H_{\nu}(\mu_t)$  is  $O(\log(1/t))$ , and for the Fisher information it is  $O(1/t)$ , as can be checked easily in the particular case when  $M = \mathbb{R}^n$  and  $\nu$  is the Gaussian measure.

*Proof of Theorem 24.16.* First note that  $U(1) = 0$  implies  $U_{\nu}(\mu) \geq$  $U_{\nu}(\nu)=0.$ 

Let  $t > 0$  be given, and let  $\exp(\nabla \psi)$  be the optimal transport between  $\mu_t$  and  $\nu$ , where as usual  $\psi$  is  $d^2/2$ -convex. Since  $U_{\nu}(\nu) = 0$  and  $K \geq 0$ , Theorem 23.14 implies

$$
U_{\nu}(\mu_t) + \int \langle \widetilde{\nabla} \psi, \nabla p(\rho_t) \rangle \le 0.
$$
 (24.23)

On the other hand, by Theorem 23.9, for almost all  $t$ ,

$$
\frac{d^+}{dt}W_2(\mu_t,\nu)^2 \le 2\int \langle \widetilde{\nabla}\psi, \nabla p(\rho_t) \rangle \, d\nu. \tag{24.24}
$$

The combination of (24.23) and (24.24) implies

$$
\frac{d^+}{dt}W_2(\mu_t,\nu)^2 \le -2U_\nu(\mu_t). \tag{24.25}
$$

Now introduce

$$
\psi(t) := a(t) I_{U,\nu}(\mu_t) + b(t) U_{\nu}(\mu_t) + c(t) W_2(\mu_t, \nu)^2,
$$

where  $a(t)$ ,  $b(t)$  and  $c(t)$  will be determined later.

Because of the assumption of nonnegative curvature, the quantity  $I_{U,\nu}(\mu_t)$  is nonincreasing with time. (Set  $K = 0$  in  $(24.5)(b)$ .) Combining this with  $(24.25)$  and Theorem  $24.2(ii)$ , we get

$$
\frac{d^+\psi}{dt} \leq [a'(t) - b(t)] I_{U,\nu}(\mu_t) + [b'(t) - 2c(t)] U_{\nu}(\mu_t) + c'(t) W_2(\mu_t, \nu)^2.
$$

If we choose

$$
a(t) \equiv t^2, \qquad b(t) \equiv 2t, \qquad c(t) \equiv 1,
$$

then  $\psi$  has to be nonincreasing as a function of t, and this implies (i).

Let us now prove (ii). By Theorem 24.2(iv), for almost all  $t > s \geq 0$ ,

$$
\frac{d^+}{dt}W_2(\mu_s, \mu_t) \le \sqrt{I_{U,\nu}(\mu_t)} \le \sqrt{I_{U,\nu}(\mu_s)},
$$
  
$$
W_2(\mu_s, \mu_t) \le \sqrt{I_{U,\nu}(\mu_s)} |t - s|
$$
(24.26)

On the other hand, by Theorems 23.9 and 23.14 (more precisely (23.26) with  $K = 0$ ,  $\sigma$  replaced by  $\mu_t$  and  $\mu$  replaced by  $\mu_s$ ),

$$
\frac{d^+}{dt} W_2(\mu_s, \mu_t)^2 \le 2 [U_\nu(\mu_s) - U_\nu(\mu_t)] \le 2 U_\nu(\mu_s).
$$

So

so

$$
W_2(\mu_s, \mu_t)^2 \le 2 U_\nu(\mu_s) |t - s|.
$$
 (24.27)

Then (ii) results from the combination of (24.26) and (24.27), together with (i).

724 24 Gradient flows II: Qualitative properties

The proof of (iii) is pretty much the same, with the following modifications:

$$
\frac{dI_{U,\nu}(\mu_t)}{dt} \le (-2K) I_{U,\nu}(\mu_t);
$$
  
$$
\frac{d^+}{dt} W_2(\mu_t, \nu)^2 \le -2 U_{\nu}(\mu_t) + (-2K) W_2(\mu_t, \nu)^2;
$$
  
$$
\psi(t) := e^{2Kt} \Big( t^2 I_{U,\nu}(\mu_t) + 2t U_{\nu}(\mu_t) + W_2(\mu_t, \nu)^2 \Big).
$$

Details are left to the reader. (The estimates in (iii) can be somewhat refined.) □

Exercise 24.22. Assuming  $CD(0, \infty)$ , establish the estimate

$$
I_{U,\nu}(\mu_t) \leq \frac{U_{\nu}(\mu_0)}{t}.
$$

Remark 24.23. There are many known regularization results in short time, for certain of the gradient flows considered in this chapter. The two most famous examples are:

• the Li–Yau estimates, which give lower bounds on  $\Delta \log \rho_t$ , for a solution of the heat equation on a Riemanian manifold, under certain curvature-dimension conditions. For instance, if M satisfies  $CD(0,N)$ , then

$$
\Delta \log \rho_t \geq -\frac{N}{2t};
$$

• the Aronson–Bénilan estimates, which give lower bounds on  $\Delta \rho_t^{m-1}$  for solutions of the nonlinear diffusion equation  $\partial_t \rho = \Delta \rho^m$ in  $\mathbb{R}^n$ , where  $1 - 2/n < m < 1$ :

$$
\frac{m}{m-1} \Delta(\rho_t^{m-1}) \ge -\frac{n}{\lambda t}, \qquad \lambda = 2 - n(1-m).
$$

There is an obvious similarity between these two estimates, and both can be interpreted as a lower bound on the rate of divergence of the vector field which drives particles in the gradient flow interpretation of these partial differential equations. I think it would be very interesting to have a unified proof of these inequalities, under certain geometric conditions. For instance one could try to use the gradient flow interpretation of the heat and nonlinear diffusion equations, and maybe some localization by restriction.

## Bibliographical notes

In [669], Otto advocated the use of his formalism both for the purpose of finding new schemes of proof, and for giving a new understanding of certain results.

What I call the Fokker–Planck equation is

$$
\frac{\partial \mu}{\partial t} = \Delta \mu + \nabla \cdot (\mu_t \, \nabla V).
$$

This is in fact an equation on measures. It can be recast as an equation on functions (densities):

$$
\frac{\partial \rho}{\partial t} = \Delta \rho - \nabla V \cdot \nabla \rho.
$$

From the point of view of stochastic processes, the relation between these two formalisms is the following:  $\mu_t$  can be thought of as law  $(X_t)$ , where  $X_t$  is the stochastic process defined by  $dX_t = \sqrt{2} dB_t - \nabla V(X_t) dt$  $(B_t =$  standard Brownian motion on the manifold), while  $\rho_t(x)$  is defined by the equation  $\rho_t(x) = \mathbb{E}_{x} \rho_0(X_t)$  (the subscript x means that the process  $X_t$  starts at  $X_0 = x$ ). In the particular case when V is a quadratic potential in  $\mathbb{R}^n$ , the evolution equation for  $\rho_t$  is often called the Ornstein–Uhlenbeck equation.

The observation that the Fisher information  $I_{\nu}$  is the time-derivative of the entropy functional  $-H_{\nu}$  along the heat semigroup seems to first appear in a famous paper by Stam [758] at the end of the fifties, in the case  $M = \mathbb{R}$  (equipped with the Lebesgue measure). Stam gives credit to de Bruijn for that remark. The generalization appearing in Theorem 24.2(ii) has been discovered and rediscovered by many authors.

Theorem  $24.2(iii)$  goes back to Bakry and Emery [56] for the case  $U(r) = r \log r$ . After many successive generalizations, the statement as I wrote it was formally derived in [577, Appendix D]. To my knowledge, the argument given in the present chapter is the first rigorous one to be written down in detail (modulo the technical justifications of the integrations by parts), although it is a natural expansion of previous works.

Theorem 24.2(iv) was proven by Otto and myself [671] for  $\sigma = \mu_0$ . The case  $\sigma = \nu$  is also useful and was considered in [219].

Regularity theory for porous medium equations has been the object of many works, see in particular the synthesis works by Vázquez [804, 805, 806]. When one studies nonlinear diffusions by means of optimal transport theory, the regularity theory is the first thing to worry about. In a Riemannian context, Demange [291, 292, 290, 293] presents many approximation arguments based on regularization, truncation, etc. in great detail. Going into these issues would have led me to considerably expand the size of this chapter; but ignoring them completely would have led to incorrect proofs.

It has been known since the mid-seventies that logarithmic Sobolev inequalities yield rates of convergence to equilibrium for heat-like equations, and that these estimates are independent of the dimension. For certain problems of convergence to equilibrium involving entropy, logarithmic Sobolev inequalities are quite more convenient than spectral tools. This is especially true in infinite dimension, although logarithmic Sobolev inequalities are also very useful in finite dimension. For more information see the bibliographical notes of Chapter 21.

As recalled in Remark 24.11, convergence in the entropy sense implies convergence in total variation. In [220] various functional methods leading to convergence in total variation are examined and compared.

Around the mid-nineties, Toscani [784, 785] introduced the logarithmic Sobolev inequality in kinetic theory, where it was immediately recognized as a powerful tool (see e.g. [300]). The links between logarithmic Sobolev inequalities and Fokker–Planck equations were re-investigated by the kinetic theory community, see in particular [43] and the references therein. The emphasis was more on proving logarithmic Sobolev inequalities thanks to the study of the convergence to equilibrium for Fokker–Planck equations, than the reverse. So the key was the study of convergence to equilibrium in the Fisher information sense, as in Chapter 25; but the final goal really was convergence in the entropy sense. To my knowledge, it is only in a recent study of certain algorithms based on stochastic integration [549], that convergence in the Fisher information sense in itself has been found useful. (In this work some constructive criteria for exponential convergence in Fisher information are given; for instance this is true for the heat equation  $\partial_t \rho = \Delta \rho$ , under a  $CD(K,\infty)$  bound  $(K<0)$  and a logarithmic Sobolev inequality.)

Around 2000, it was discovered independently by Otto [669], Carrillo and Toscani [215] and Del Pino and Dolbeault [283] that the same "information-theoretical" tools could be used for nonlinear equations of the form

$$
\frac{\partial \rho}{\partial t} = \Delta \rho^m \tag{24.28}
$$

in  $\mathbb{R}^n$ . Such equations are called porous medium equations for  $m > 1$ , and fast diffusion equations for  $m < 1$ . For these models there is no convergence to equilibrium: the solution disperses at infinity. But there is a well-known scaling, due to Barenblatt, which transforms (24.28) into

$$
\frac{\partial \rho}{\partial t} = \Delta \rho^m + \nabla_x \cdot (\rho x). \tag{24.29}
$$

Then, up to rescaling space and time, it is equivalent to understand the convergence to equilibrium for (24.29), or to understand the asymptotic behavior for (24.28), that is, how fast it approaches a certain known self-similar profile.

The extra drift term in (24.29) acts like the confinement by a quadratic potential, and this in effect is equivalent to imposing a curvature condition  $CD(K,\infty)$   $(K > 0)$ . This explains why there is an approach based on generalized logarithmic Sobolev inequalities, quite similar to the proof of Theorem 24.7.

These problems can be attacked without any knowledge of optimal transport. In fact, among the authors quoted before, only Otto did use optimal transport, and this was not at the level of proofs, but only at the level of intuition. Later in [671], Otto and I gave a more direct proof of logarithmic Sobolev inequality based on the HWI inequality. The same strategy was applied again in my joint work with Carrillo and McCann [213], for more general equations involving also a (simple) nonlinear drift.

In [213] the basic equation takes the form

$$
\frac{\partial \rho}{\partial t} = \sigma \Delta \rho + \nabla \cdot (\rho \nabla V) + \nabla \cdot (\rho \nabla (\rho * \nabla W)), \tag{24.30}
$$

where  $\sigma \in \mathbb{R}_+$  and  $W = W(x - y)$  is some interaction potential on  $\mathbb{R}^n$ . These equations (a particular instance of McKean–Vlasov equations) appeared in the modeling of granular media [92, 93, 622], either with  $\sigma = 0$  or with  $\sigma > 0$ , in particular in dimension 1. See the review paper [820] for much more information. Similar equations also appear in the theory of self-interacting diffusion processes [83, 84, 85, 521, 535]. (Some of the ingredients of [213] are used again in [521], along with many subtle probabilistic arguments, in the study of the confining effect of self-interaction.)

The study of exponential convergence for (24.30) leads to interesting issues, some of them briefly reviewed in [815, 820]. There are criteria for exponential convergence in terms of the convexity of  $V$  and  $W$ . These problems can also be set on a Riemannian manifold M (replace  $W(x - y)$  by  $W(x, y)$ , and then Ricci curvature estimates come into play [761]. In the particular case of linear diffusion in  $\mathbb{R}^n$ , there are alternative approaches to these convergence results, more directly based on coupling arguments [221, 590, 591]. In the other particular case where (24.30) is set in dimension 1,  $\sigma = 0$  and  $W(z) = |z|^3/3$ , the solution converges to a Dirac mass, and there is a self-similar scaling allowing one to refine the study of the rate of convergence. A somewhat surprising (at least so it was for us) result of Caglioti and myself [195] states that the refinement obtained by this method is necessarily small; the argument is based on a proof of "slow convergence" for a rescaled equation, which uses the 1-Wasserstein distance  $W_1$ .

The strategy based on displacement convexity does not apply directly to  $(24.30)$  when the potential W is not convex. However, there is an interesting interplay between the diffusion and the effect of the interaction potential. Such an effect was studied in [213] for (24.30) when  $\sigma > 0$ ,  $V = 0$  and  $W = |z|^3$ : even though the interaction potential is degenerately convex, the spreading caused by the diffusion is sufficient to make it "effectively" uniformly convex. Even more striking, Calvez and Carrillo [198] established convergence to equilibrium (in Wasserstein distance) for  $(24.30)$  with  $V = 0$  in the nonconvex case  $W(z) = |z|^k / k$  for  $-1 < k < 1$  ( $k = 0$  corresponds to log  $|z|$ ). This also works if the linear diffusion  $\Delta \rho$  is replaced by  $\Delta \rho^m$  with  $m + k > 1$ .

Demange [290, 291, 292, 293] studied the fast diffusion equation  $\partial_t \rho = \Delta \rho^{1-1/N}$  on a Riemannian manifold, under a curvature-dimension condition  $CD(K, N)$ . He used the Sobolev inequality, in the form

$$
H_{N/2}(\mu) \le \frac{(N-2)(N-1)}{2K} \int \rho^{-1-\frac{2}{N}} |\nabla \rho|^2 d\nu
$$
  
$$
\le \frac{(N-2)(N-1)}{2K} (\sup \rho)^{-\frac{1}{N}} \int \rho^{1-\frac{1}{N}} |\nabla \rho|^2 d\nu
$$

to obtain a differential inequality such as

$$
\frac{dH_{N/2}(\mu_t)}{dt} \le -\left(\frac{N-2}{N-1}\right)(\sup \rho)^{-\frac{1}{N}} \frac{H_{N/2}(\mu_t)}{2K},
$$

and deduced an estimate of the form

$$
H_{N/2}(\mu_t) = O\big(e^{-(\lambda_N + \varepsilon)t}\big),
$$

where  $\lambda_N$  is the presumably optimal rate that one would obtain without the (sup  $\rho$ ) term, and  $\varepsilon > 0$  is arbitrarily small. His estimate is slightly stronger than the one which I derived in Theorem 24.7 and Remark 24.12, but the asymptotic rate is the same.

All the methods described before apply to the study of the time asymptotics of the porous medium equation  $\partial_t \rho = \Delta \rho^m$ , but only under the restriction  $m \geq 1 - 1/N$ . In that regime one can use time-rescaling and tools similar to the ones described in this chapter, to prove that the solutions become close to Barenblatt's self-similar solution.

When  $m < 1-1/N$ , displacement convexity and related tricks do not apply any more. This is why it was rather a sensation when Carrillo and Vázquez  $[217]$  applied the Aronson–Bénilan estimates to the problem of asymptotic behavior for fast diffusion equations with exponents  $m$ in  $\left(1-\frac{2}{N}\right)$  $\frac{2}{N}, 1-\frac{1}{N}$  $\frac{1}{N}$ , which is about the best that one can hope for, since Barenblatt profiles do not exist for  $m \leq 1 - 2/N$ .

Here we see the limits of Otto's formalism: such results as the dimensional refinement of the rate of convergence for diffusive equations  $(Remark 24.10)$ , or the Carrillo–Vázquez estimates, rely on inequalities of the form

$$
\int p(\rho) \Gamma_2(\nabla U'(\rho)) d\nu + \int p_2(\rho) (LU'(\rho))^2 d\nu \geq \dots
$$

in which ones takes advantage of the fact that the same function  $\rho$ appears in the terms  $p(\rho)$  and  $p_2(\rho)$  on the one hand, and in the terms  $\nabla U'(\rho)$  and  $LU'(\rho)$  on the other. The technical tool might be changes of variables for the  $\Gamma_2$  (as in [541]), or elementary integration by parts (as in [217]); but I don't see any interpretation of these tricks in terms of the Wasserstein space  $P_2(M)$ .

The story about the rates of equilibration for fast diffusion equations does not end here. At the same time as Carrillo and Vázquez obtained their main results, Denzler and McCann [298, 299] computed the spectral gap for the linearized fast diffusion equations in the same interval of exponents  $(1 - \frac{2}{N})$  $\frac{2}{N}$ , 1 –  $\frac{1}{N}$  $\frac{1}{N}$ ). This study showed that the rate of convergence obtained by Carrillo and Vázquez is off the value suggested by the linearized analysis by a factor 2 (except in the radially symmetric case where they obtain the optimal rate thanks to a comparison method). The connection between the nonlinear and the linearized dynamics is still unclear, although some partial results have been obtained by Mc-Cann and Slepčev [619]. More recently, S.J. Kim and McCann [517] have derived optimal rates of convergence for the "fastest" nonlinear diffusion equations, in the range  $1 - 2/N < m \leq 1 - 2/(N + 2)$ , by comparison methods involving Newtonian potentials. Another work by Cáceres and Toscani [183] also recovers some of the results of Denzler and McCann by means of completely different methods with their roots in kinetic theory. There is still ongoing research to push the rates of convergence and the range of admissible nonlinearities, in particular by Denzler, Koch, McCann and probably others.

In dimension 2, the limit case  $m = 0$  corresponds to a logarithmic diffusion; it is related to geometric problems, such as the evolution of conformal surfaces or the Ricci flow [806, Chapter 8].

More general nonlinear diffusion equations of the form  $\partial_t \rho = \Delta p(\rho)$ have been studied by Biler, Dolbeault and Esteban [119], and Carrillo, Di Francesco and Toscani [210, 211] in  $\mathbb{R}^n$ . In the latter work the rescaling procedure is recast in a more geometric and physical interpretation, in terms of temperature and projections; a sequel by Carrillo and Vázquez [218] shows that the intermediate asymptotics can be complicated for well-chosen nonlinearities. Nonlinear diffusion equations on manifolds were also studied by Demange [291] under a  $CD(K, N)$  curvature-dimension condition,  $K > 0$ .

Theorem 24.7(ii) is related to a long tradition of study of contraction rates in Wasserstein distance for diffusive equations [231, 232, 458, 662]. Sturm and Renesse [764] noted that such contraction rates characterize nonnegative Ricci curvature; Sturm [761] went on to give various characterizations of  $CD(K, N)$  bounds in terms of contraction rates for possibly nonlinear diffusion equations.

In the one-dimensional case  $(M = \mathbb{R})$  there are alternative methods to get contraction rates in  $W_2$  distance, and one can also treat larger classes of models (for instance viscous conservation laws), and even obtain decay in  $W_p$  for any p; see for instance [137, 212]. Recently, Brenier found a re-interpretation of these one-dimensional contraction properties in terms of monotone operators [167]. Also the asymptotic behavior of certain conservation laws has been analyzed in this way [208, 209] (with the help of the strong " $W_{\infty}$  distance"!).

Another model for which contraction in  $W_2$  distance has been established is the Boltzmann equation, in the particular case of a spatially homogeneous gas of Maxwellian molecules. This contraction property was discovered by Tanaka [644, 776, 777]; see [138] for recent work on the subject. Some striking uniqueness results have been obtained by Fournier and Mouhot [377, 379] with a related method (see also [378]).

To conclude this discussion about contraction estimates, I shall briefly discuss some links with Perelman's analysis of the backward version of Hamilton's Ricci flow. A first observation by McCann and Topping [620] is that the evolution by this flow forces the heat equation to be a contraction in Wasserstein distance, even if the Ricci curvature is not everywhere nonnegative. McCann and Topping also established a converse result characterizing the Ricci flow in terms of contractive semigroups. Related topics were independently studied by Carfora [200].

These investigations were pushed further by Topping [782] and Lott [576] with the help of the formalism of displacement interpolation for Lagrangian actions (recall Chapter 7). For instance, define

$$
\mathcal{L}_0^{t_0,t_1}(x,y) = \inf \left\{ \frac{1}{2} \int_{t_0}^{t_1} \left( ||\dot{\gamma}(t)|^2 + S(\gamma(t),t) \right) dt \right\},\,
$$

where the infimum is taken over all  $C^1$  paths  $\gamma : [t_0, t_1] \to M$  such that  $\gamma(t_0) = x$  and  $\gamma(t_1) = y$ , and  $S(x,t)$  is the scalar curvature of M (evolving under backward Ricci flow) at point x and time t. As in Chapter 7, this induces a Hamilton–Jacobi equation, and an action in the space of measures. Then it is shown in [576] that  $H(\mu_t) - \int \phi_t d\mu_t$  is convex in t along the associated displacement interpolation, where  $(\phi_t)$  is a solution of the Hamilton–Jacobi equation. Other theorems in [576, 782] deal with a variant of  $\mathcal{L}_0$  in which some time-rescalings have been performed. Not only do these results generalize the contraction property of [620], but they also imply Perelman's estimates of monotonicity of the so-called W-entropy and reduced volume functionals (which were an important tool in the proof of the Poincaré conjecture).

I shall now comment on short-time decay estimates. The short-time behavior of the entropy and Fisher information along the heat flow (Theorem 24.16) was studied by Otto and myself around 1999 as a technical ingredient to get certain a priori estimates in a problem of hydrodynamical limits. This work was not published, and I was quite surprised to discover that Bobkov, Gentil and Ledoux [127, Theorem 4.3] had found similar inequalities and applied them to get a new proof of the HWI inequality. Otto and I published our method [672] as a comment to [127]; this is the same as the proof of Theorem 24.16. It can be considered as an adaptation, in the context of the Wasserstein space, of some classical estimates about gradient flows in Hilbert spaces, that can be found in Brézis [171, Théorème 3.7]. The result of Bobkov, Gentil and Ledoux is actually more general than ours, because these authors seem to have sharp constants under  $CD(K,\infty)$  for all values of  $K \in \mathbb{R}$ ,

while it is not clear that our method is sharp for  $K \neq 0$ . For  $K = 0$ . both methods yield exactly the same result, which was a bit of a puzzle to me. It would be interesting to clarify all this.

In relation to Remark 24.21, I was asked the following question by Guionnet (and I am unable to answer): Given a solution  $(\mu_t)$  of the heat equation  $\partial_t \rho = L\rho$ , is it true that  $t I_{\nu}(\mu_t)$  converges to a finite limit as  $t \to 0$ ? If yes, then by De L'Hospital's rule, this is also the limit of  $H_{\nu}(\mu_t)/|\log t|$  as  $t \to 0$ . In the particular case when  $\mu_0 =$  $f \nu + \sum_{k=1}^{N} a_k \, \delta_{x_k}$ , with f smooth, it is not difficult to show that  $t I_{\nu}(\mu_t)$ converges to  $\sum a_k$ . This question is motivated by some problems in free probability theory.

Inequality (24.25) goes back to [672], under adequate regularity assumptions, for the main case of interest which is  $U(r) = r \log r$ .

Hölder- $1/2$  estimates in time are classical for gradient flows; in the context of the Wasserstein space, they appeared in several works, for instance [30].

In [214] and [30] there were some investigations about the possibility to directly use Otto's formalism to perform the proof of Theorem 24.2 and the other theorems in this chapter.

The Li–Yau heat kernel estimates go back to [552]; they were refined by Davies [272], then by Bakry and Qian [60]; the latter paper is closely related to certain issues that will be addressed in the next chapter. In any case, the Bochner formula and various forms of maximum principles are the main ingredients behind these estimates. Recently, Bakry and Ledoux [59] derived improved forms of the Li–Yau estimates, and made the connection with the theory of logarithmic Sobolev inequalities.

There are similarities between the Li–Yau parabolic Harnack inequality and the study of Perelman's  $\mathcal{L}\text{-}$ functional; in view of Topping's work [782], this seems to give one further reason to hope for a direct relation between optimal transport and Li–Yau inequalities.

The Aronson–Bénilan estimates were established in [45]. There is some overlap between the Aronson–Bénilan and Li–Yau bounds; see [580] for a common framework. (Early attempts were performed by Carrillo and myself.)

Recently Demange has obtained short-time regularization estimates like sup  $\rho_t = O(t^{-N})$  for the fast diffusion equation  $\partial_t \rho = \Delta \rho^{1-1/N}$  in positive curvature, which are optimal in a certain sense.

In this chapter as in the previous one, I have only been interested in gradient flows; but there are probably other questions about the qualitative behavior of Hamiltonian flows which make sense in relation to optimal transport. For instance, if one were able to construct "Gibbs measures" of the form (15.23) on the set  $P_2(M)$ , where M is a symplectic manifold, then they would be natural candidates to be relevant invariant measures for Hamiltonian flows in  $P_2(M)$ . Take for instance  $M = \mathbb{T}^2$ , and define the Hamiltonian as  $H(\mu) = \int G(x, y) \mu(dx) \mu(dy)$ , where  $G(x, y)$  is the fundamental solution of the Laplace operator on  $\mathbb{T}^2$ ; then the associated "Hamiltonian flow" should be the twodimensional Euler equation. For this equation the problem of constructing invariant measures was considered long ago [94, 693] without real success (see however [14]); it is natural to ask whether the optimal transport approach provides a path to attack this problem.

The following are the results of the experiment:

| <strong>Labels</strong> | <strong>Values</strong> |
|-------------------------|-------------------------|
| Experiment ID           | 12345                   |
| Date                    | 2023-10-27              |
| Result                  | Success                 |

Further analysis is required to understand the implications of these findings.
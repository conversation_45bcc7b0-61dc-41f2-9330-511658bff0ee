# First Appendix: Second differentiability of convex functions

In this Appendix I shall provide a proof of Theorem 14.1. As explained right after the statement of that theorem, it suffices to consider the particular case of a convex function  $\mathbb{R}^n \to \mathbb{R}$ . So here is the statement to be proven:

Theorem 14.25 (<PERSON><PERSON><PERSON>'s second differentiability theorem).

Let  $\varphi : \mathbb{R}^n \to \mathbb{R}$  be a convex function. Then, for Lebesgue-almost every  $x \in \mathbb{R}^n$ ,  $\varphi$  is differentiable at x and there exists a symmetric operator  $A: \mathbb{R}^n \to \mathbb{R}^n$ , characterized by any one of the following equivalent properties:

(i) 
$$
\nabla \varphi(x+v) = \nabla \varphi(x) + Av + o(|v|)
$$
 as  $v \to 0$ ;

(i') 
$$
\partial \varphi(x+v) = \nabla \varphi(x) + Av + o(|v|)
$$
 as  $v \to 0$ ;

(ii) 
$$
\varphi(x+v) = \varphi(x) + \nabla \varphi(x) \cdot v + \frac{\langle Av, v \rangle}{2} + o(|v|^2)
$$
 as  $v \to 0$ ;

(ii')  $\forall v \in \mathbb{R}^n$ , 
$$
\varphi(x+tv) = \varphi(x) + t \nabla \varphi(x) \cdot v + t^2 \frac{\langle Av, v \rangle}{2} + o(t^2)
$$

as  $t \to 0$ .

as  $t \to 0$ .

(In (i) the vector v is such that  $\varphi$  is differentiable at  $x + v$ ; in (ii) the notation  $o(|v|)$  means a set whose elements are all bounded in norm like  $o(|v|)$ .)

The operator A is denoted by  $\nabla^2 \varphi(x)$  and called the Hessian of  $\varphi$ at x. When no confusion is possible, the quadratic form defined by  $A$  is also called the Hessian of  $\varphi$  at x. Moreover, the function  $x \to \nabla^2 \psi(x)$ (resp.  $x \to \Delta \psi(x) = \text{tr}(\nabla^2 \psi(x))$ ) is the density of the absolutely continuous part of the distribution  $\nabla_{\mathcal{D}'}^2 \psi$  (resp. of the distribution  $\Delta_{\mathcal{D}'} \psi$ ).

Before starting the proof, let me recall an elementary lemma about convex functions.

**Lemma 14.26.** (i) Let  $\varphi : \mathbb{R}^n \to \mathbb{R}$  be a convex function, and let  $x_0$ ,  $x_1, \ldots, x_{n+1} \in \mathbb{R}^n$  such that  $B(x_0, 2r)$  is included in the convex hull of  $x_1, \ldots, x_{n+1}$ . Then,

$$
2\,\varphi(x_0) - \max_{1 \leq i \leq n+1} \varphi(x_i) \leq \inf_{B(x_0, 2r)} \varphi \leq \sup_{B(x_0, 2r)} \varphi \leq \max_{1 \leq i \leq n+1} \varphi(x_i);
$$
$$
\|\varphi\|_{\text{Lip}(B(x_0, r))} \leq \frac{2\left(\max_{1 \leq i \leq n+1} \varphi(x_i) - \varphi(x_0)\right)}{r}.
$$

(ii) If  $(\varphi_k)_{k\in\mathbb{N}}$  is a sequence of convex functions which converges pointwise to some function  $\Phi$ , then the convergence is locally uniform.

*Proof of Lemma 14.26.* If x lies in  $B(x_0, 2r)$  then of course, by convexity,  $\varphi(x) \leq \max(\varphi(x_1), \ldots, \varphi(x_{n+1}))$ . Next, if  $z \in B(x_0, 2r)$ , then  $\tilde{z} :=$  $2x_0 - z \in B(x_0, 2r)$  and  $\varphi(z) \geq 2\varphi(x_0) - \varphi(\tilde{z}) \geq 2\varphi(x_0) - \max \varphi(x_i)$ . Next, let  $x \in B(x_0, r)$  and let  $y \in \partial \varphi(x)$ ; let  $z = x + ry/|y| \in B(x_0, 2r)$ . From the subdifferential inequality,  $r|y| = \langle y, z - x \rangle \leq \varphi(z) - \varphi(x) \leq \varphi(z)$  $2(\max \varphi(x_i) - \varphi(x_0))$ . This proves (i).

Now let  $(\varphi_k)_{k \in \mathbb{N}}$  be a sequence of convex functions, let  $x_0 \in \mathbb{R}^n$  and let  $r > 0$ . Let  $x_1, \ldots, x_{n+1}$  be such that  $B(x_0, 2r)$  is included in the convex hull of  $x_1, \ldots, x_{n+1}$ . If  $\varphi_k(x_i)$  converges for all j, then by (i) there is a uniform bound on  $\|\varphi_k\|_{\text{Lip}}$  on  $B(x_0,r)$ . So if  $\varphi_k$  converges pointwise on  $B(x_0,r)$ , the convergence has to be uniform. This proves (ii).

Now we start the proof of Theorem 14.25. To begin with, we should check that the formulations (i), (i), (ii) and (ii) are equivalent; this will use the convexity of  $\varphi$ .

*Proof of the equivalence in Theorem 14.25.* It is obvious that  $(i') \Rightarrow (i)$ and (ii)  $\Rightarrow$  (ii'), so we just have to show that (i)  $\Rightarrow$  (ii) and (ii')  $\Rightarrow$  (i').

To prove (i)  $\Rightarrow$  (ii), the idea is to use the mean value theorem; since a priori  $\varphi$  is not smooth, we shall regularize it. Let  $\zeta$  be a radially symmetric nonnegative smooth function  $\mathbb{R}^n \to \mathbb{R}$ , with compact support in  $B_1(0)$ , such that  $\int \zeta = 1$ . For any  $\varepsilon > 0$ , let  $\zeta_{\varepsilon}(x) = \varepsilon^{-n} \zeta(x/\varepsilon)$ ; then let  $\varphi_{\varepsilon} := \varphi * \zeta_{\varepsilon}$ . The resulting function  $\varphi_{\varepsilon}$  is smooth and converges pointwise to  $\varphi$  as  $\varepsilon \to 0$ ; moreover, since  $\varphi$  is locally Lipschitz we have (by dominated convergence)  $\nabla \varphi_{\varepsilon} = (\nabla \varphi) * \zeta_{\varepsilon}$ .

Then we can write

$$
\varphi(x+v) - \varphi(x) = \lim_{\varepsilon \to 0} [\varphi_{\varepsilon}(x+v) - \varphi_{\varepsilon}(x)]
$$
$$
= \lim_{\varepsilon \to 0} \int_{0}^{1} \nabla \varphi_{\varepsilon}(x+tv) \cdot v \, dt. \tag{14.70}
$$

Let us assume that  $\varepsilon \leq |v|$ ; then, by (i), for all  $z \in B_{2\varepsilon}(x)$ ,

$$
\nabla \varphi(z) = \nabla \varphi(x) + A(z - x) + o(|v|).
$$

If  $y \in B_{\varepsilon}(x)$ , we can integrate this identity against  $\zeta_{\varepsilon}(y-z) dz$  (since  $\zeta_{\varepsilon}(y-z) = 0$  if  $|y-z| > \varepsilon$ ); taking into account  $\int (z-x) \zeta_{\varepsilon}(z-x) dz = 0$ , we obtain

$$
\nabla \varphi_{\varepsilon}(y) = \nabla \varphi_{\varepsilon}(x) + A(y - x) + o(|v|).
$$

In particular,  $\nabla \varphi_{\varepsilon}(x+tv) = \nabla \varphi_{\varepsilon}(x) + tAv + o(|v|)$ . By plugging this into the right-hand side of (14.70), we obtain Property (ii).

Now let us prove that (ii)  $\Rightarrow$  (i). Without loss of generality we may assume that  $x = 0$  and  $\nabla \varphi(x) = 0$ . So the assumption is  $\varphi(tw) =$  $\langle h^2 \langle Aw, w \rangle / 2 + o(t^2)$ , for any w. If (i') is false, then there are sequences  $x_k \to 0, |x_k| \neq 0$ , and  $y_k \in \partial \varphi(x_k)$  such that

$$
\frac{y_k - Ax_k}{|x_k|} \underset{k \to \infty}{\longrightarrow} 0. \tag{14.71}
$$

Extract an arbitrary sequence from  $(x_k, y_k)$  (still denoted  $(x_k, y_k)$ ) for simplicity) and define

$$
\varphi_k(w) := \frac{1}{|x_k|^2} \varphi(|x_k|w).
$$

Assumption (ii) implies that  $\varphi_k$  converges pointwise to  $\Phi$  defined by

$$
\Phi(w) = \frac{\langle Aw, w \rangle}{2}.
$$

The functions  $\varphi_k$  are convex, so the convergence is actually locally uniform by Lemma 14.26.

Since  $y_k \in \partial \varphi(x_k)$ ,

$$
\forall z \in \mathbb{R}^n, \qquad \varphi(z) \ge \varphi(x_k) + \langle y_k, z - x_k \rangle,
$$

or equivalently, with the notation  $w_k = x_k/|x_k|$ ,

$$
\forall w \in \mathbb{R}^n, \qquad \varphi_k(w) \ge \varphi_k(w_k) + \left\langle \frac{y_k}{|x_k|}, w - w_k \right\rangle. \tag{14.72}
$$

The choice  $w = w_k + y_k/|y_k|$  shows that  $|y_k|/|x_k| \leq \varphi_k(w) - \varphi_k(w_k)$ , so  $|y_k|/|x_k|$  is bounded. Up to extraction of a subsequence, we may assume that  $w_k = x_k/|x_k| \to \sigma \in S^{n-1}$  and  $y_k/|x_k| \to y$ . Then we can pass to the limit in (14.72) and recover

$$
\forall w \in \mathbb{R}^n, \qquad \Phi(w) \ge \Phi(\sigma) + \langle y, w - \sigma \rangle.
$$

It follows that  $y \in \partial \Phi(\sigma) = \{A\sigma\}$ . So  $y_k/|x_k| \to A\sigma$ , or equivalently  $(y_k - Ax_k)/|x_k| \to 0$ . What has been shown is that each subsequence of the original sequence  $(y_k-Ax_k)/|x_k|$  has a subsequence which converges to 0; thus the whole sequence converges to 0. This is in contradiction with  $(14.71)$ , so  $(i')$  has to be true. □

Now, before proving Theorem 14.25 in full generality, I shall consider two particular cases which are much simpler.

*Proof of Theorem 14.25 in dimension 1.* Let  $\varphi : \mathbb{R} \to \mathbb{R}$  be a convex function. Then its derivative  $\varphi'$  is nondecreasing, and therefore differentiable almost everywhere. ⊓⊔

Proof of Theorem 14.25 when  $\nabla\varphi$  is locally Lipschitz. Let  $\varphi : \mathbb{R}^n \to \mathbb{R}$ be a convex function, continuously differentiable and such that  $\nabla\varphi$ locally Lipschitz. By Rademacher's theorem, each function  $\partial_i\varphi$  is differentiable almost everywhere, where  $\partial_i$  stands for the partial derivative with respect to  $x_i$ . So the functions  $\partial_j(\partial_i\varphi)$  are defined almost everywhere. To conclude the proof, it suffices to show that  $\partial_i(\partial_i\varphi) = \partial_i(\partial_i\varphi)$ almost everywhere. To prove this, let  $\zeta$  be any  $C^2$  compactly supported function; then, by successive use of the dominated convergence theorem and the smoothness of  $\varphi * \zeta$ ,

$$
(\partial_i \partial_j \varphi) * \zeta = \partial_i (\partial_j \varphi * \zeta) = \partial_i \partial_j (\varphi * \zeta)
$$
  
= 
$$
\partial_j \partial_i (\varphi * \zeta) = \partial_j (\partial_i \varphi * \zeta) = (\partial_j \partial_i \varphi) * \zeta.
$$

It follows that  $(\partial_i \partial_j \varphi - \partial_j \partial_i \varphi) * \zeta = 0$ , and since  $\zeta$  is arbitrary this implies that  $\partial_i \partial_j \varphi - \partial_j \partial_i \varphi$  vanishes almost everywhere. This concludes the argument. the argument.

Proof of Theorem 14.25 in the general case. As in the proof of Theorem 10.8(ii), the strategy will be to reduce to the one-dimensional case. For any  $v \in \mathbb{R}^n$ ,  $t > 0$ , and x such that  $\varphi$  is differentiable at x, define

$$
Q_v(t,x) = \frac{\varphi(x+tv) - \varphi(x) - t \nabla \varphi(x) \cdot v}{t^2} \ge 0.
$$

The goal is to show that for Lebesgue–almost all  $x \in \mathbb{R}^n$ ,

$$
q_v(x) := \lim_{t \to 0} Q_v(t, x)
$$

exists for all  $v$ , and is a quadratic function of  $v$ .

Let Dom  $q(x)$  be the set of  $v \in \mathbb{R}^n$  such that  $q_v(x)$  exists. It is clear from the definition that:

(a)  $q_v(x)$  is nonnegative and homogeneous of degree 2 in v on Dom  $q(x)$ ;

(b)  $q_v(x)$  is a convex function of v on Dom  $q(x)$ : this is just because it is the limit of the family  $Q_v(t,x)$ , which is convex in v;

(c) If v is interior to Dom  $q(x)$  and  $q_w(x) \to \ell$  as  $w \to v, w \in$ Dom  $q(x)$ , then also  $v \in$  Dom  $q(x)$  and  $q_v(x) = \ell$ . Indeed, let  $\varepsilon > 0$ and let  $\delta$  be so small that  $|w - v| \leq \delta \Longrightarrow |q_w(x) - \ell| \leq \varepsilon$ ; then, we can find  $v_1, \ldots, v_{n+1}$  in Dom  $q(x) \cap B(v, \delta)$  so that v lies in the convex hull of  $v_1, \ldots, v_{n+1}$ , and then  $v_0 \in \text{Dom } q(x) \cap B(v, \delta)$ , so  $v \in B(v_0, \delta)$  and  $B(v_0, r)$  is included in the convex hull of  $v_1, \ldots, v_{n+1}$ . By Lemma 14.26,

$$
2 Q_{v_0}(t, x) - \max Q_{v_i}(t, x) \le Q_v(t, x) \le \max Q_{v_i}(t, x).
$$

So

$$
\ell - 3\varepsilon \le 2 q_{v_0}(x) - \max q_{v_i}(x) \le \liminf_{t \to 0} Q_v(t, x)
$$
  
\$\le\$ 
$$
\limsup_{t \to 0} Q_v(t, x) \le \max q_{v_i}(x) \le \ell + \varepsilon.
$$

It follows that  $\lim Q_{v}(t,x) = \ell$ , as desired.

Next, we can use the same reasoning as in the proof of Rademacher's theorem (Theorem 10.8(ii)): Let v be given,  $v \neq 0$ , let us show that  $q_v(x)$  exists for almost all x. By Fubini's theorem, it is sufficient to show that  $q_v(x)$  exists  $\lambda_1$ -almost everywhere on each line parallel to v. So let  $x_0 \in v^{\perp}$  be given, and let  $L_{x_0} = x_0 + \mathbb{R}v$  be the line passing through  $x_0$ , parallel to v; the existence of  $q_v(x_0 + t_0v)$  is equivalent to the second differentiability of the convex function  $\psi : t \to \varphi(x_0 + tv)$  at  $t = t_0$ , and from our study of the one-dimensional case we know that this happens for  $\lambda_1$ -almost all  $t_0 \in \mathbb{R}$ .

So for each v, the set  $A_v$  of  $x \in \mathbb{R}^n$  such that  $q_v(x)$  does not exist is of zero measure. Let  $(v_k)$  be a dense subset of  $\mathbb{R}^n$ , and let  $A = \cup A_{v_k}$ : A is of zero measure, and for each  $x \in \mathbb{R}^n \setminus A$ , Dom  $q(x)$  contains all the vectors  $v_k$ .

Again, let  $x \in \mathbb{R}^n \setminus A$ . By Property (b),  $q_v(x)$  is a convex function of  $v$ , so it is locally Lipschitz and can be extended uniquely into a continuous convex function  $r(v)$  on  $\mathbb{R}^n$ . By Property (c),  $r(v) = q_v(x)$ , which means that Dom  $q(x) = \mathbb{R}^n$ .

At this point we know that for almost any x the limit  $q_v(x)$  exists for all  $v$ , and it is a convex function of  $v$ , homogeneous of degree 2. What we do not know is whether  $q_v(x)$  is a *quadratic* function of v.

Let us try to solve this problem by a regularization argument. Let  $\zeta$  be a smooth nonnegative compactly supported function on  $\mathbb{R}^n$ , with  $\int \zeta = 1$ . Then  $\nabla \varphi * \zeta = \nabla(\varphi * \zeta)$ . Moreover, thanks to the nonnegativity of  $Q_v(x,t)$  and Fatou's lemma,

$$
(q_v * \zeta)(x) = \int \lim_{t \downarrow 0} Q_v(y, t) \zeta(x - y) dy
$$
  

$$
\leq \liminf_{t \downarrow 0} \int Q_v(y, t) \zeta(x - y) dy
$$
  

$$
= \liminf_{t \downarrow 0} \frac{1}{t^2} \Big[ (\varphi * \zeta)(x + tv) - (\varphi * \zeta)(x) - t \nabla (\varphi * \zeta)(x) \cdot v \Big]
$$
  

$$
= \frac{1}{2} \langle \nabla^2 (\varphi * \zeta)(x) \cdot v, v \rangle.
$$

It is obvious that the right-hand side is a quadratic form in  $v$ , but this is only an upper bound on  $q_v * \zeta(x)$ . In fact, in general  $q_v * \zeta$  does not coincide with  $(1/2)\langle \nabla^2(\varphi*\zeta)v,v\rangle$ . The difference is caused by the singular part of the measure  $\mu_v := (1/2)\langle \nabla^2 \varphi \cdot v, v \rangle$ , defined in the distribution sense by

$$
\int \zeta(x) \,\mu_v(dx) = \frac{1}{2} \int \langle \nabla^2 \zeta(x) \cdot v, v \rangle \,\varphi(x) \, dx.
$$

This obstacle is the main new difficulty in the proof of Alexandrov's theorem, as compared to the proof of Rademacher's theorem.

To avoid the singular part of the measure  $\mu_v$ , we shall appeal to Lebesgue's density theory, in the following precise form: Let  $\mu$  be a locally finite measure on  $\mathbb{R}^n$ , and let  $\rho \lambda_n + \mu_s$  be its Lebesgue decomposition into an absolutely continuous part and a singular part. Then, for Lebesgue–almost all  $x \in \mathbb{R}^n$ ,

$$
\frac{1}{\delta^n} \|\mu - \rho(x)\lambda_n\|_{\mathrm{TV}(B_\delta(x))} \xrightarrow[\delta \to 0]{} 0,
$$

where  $\|\cdot\|_{TV(B_\delta(x))}$  stands for the total variation on the ball  $B_\delta(x)$ . Such an  $x$  will be called a Lebesgue point of  $\mu$ .

So let  $\rho_v$  be the density of  $\mu_v$ . It is easy to check that  $\mu_v$  is locally finite, and we also showed that  $q_v$  is locally integrable. So, for  $\lambda_n$ -almost all  $x_0$  we have

$$
\begin{cases} \frac{1}{\delta^n} \int_{B_\delta(x_0)} |q_v(x) - q_v(x_0)| dx \xrightarrow[\delta \to 0]{} 0; \\ \frac{1}{\delta^n} ||\mu_v - \rho_v(x_0)\lambda_n||_{\text{TV}(B_\delta(x_0))} \xrightarrow[\delta \to 0]{} 0. \end{cases}
$$

The goal is to show that  $q_v(x_0) = \rho_v(x_0)$ . Then the proof will be complete, since  $\rho_v$  is a quadratic form in v (indeed,  $\rho_v(x_0)$ ) is obtained by averaging  $\mu_v(dx)$ , which itself is quadratic in v). Without loss of generality, we may assume that  $x_0 = 0$ .

To prove that  $q_v(0) = \rho_v(0)$ , it suffices to establish

$$
\lim_{\delta \to 0} \frac{1}{\delta^n} \int_{B_\delta(0)} |q_v(x) - \rho_v(0)| \, dx = 0,\tag{14.73}
$$

To estimate  $q_v(x)$ , we shall express it as a limit involving points in  $B_\delta(x)$ , and then use a Taylor formula; since  $\varphi$  is not a priori smooth, we shall regularize it on a scale  $\varepsilon \leq \delta$ . Let  $\zeta$  be as before, and let  $\zeta_{\varepsilon}(x) = \varepsilon^{-n} \zeta(x/\varepsilon);$  further, let  $\varphi_{\varepsilon} := \varphi * \zeta.$ 

We can restrict the integral in (14.73) to those x such that  $\nabla \varphi(x)$ exists and such that x is a Lebesgue point of  $\nabla \varphi$ ; indeed, such points form a set of full measure. For such an x,  $\varphi(x) = \lim_{\varepsilon \to 0} \varphi_{\varepsilon}(x)$ , and  $\nabla \varphi(x) = \lim_{\varepsilon \to 0} \nabla \varphi_{\varepsilon}(x)$ . So,

$$
\frac{1}{\delta^n} \int_{B_\delta(0)} |q_v(x) - \rho_v(0)| dx
$$
\n
$$
= \frac{1}{\delta^n} \int_{B_\delta(0)} \left| \lim_{t \to 0} \left[ \frac{\varphi(x + t\delta v) - \varphi(x) - \nabla \varphi(x) \cdot t\delta v}{t^2 \delta^2} \right] - \rho_0(v) \right| dx
$$
\n
$$
= \frac{1}{\delta^n} \int_{B_\delta(0)} \lim_{t \to 0} \lim_{\varepsilon \to 0} \left| \frac{\varphi_\varepsilon(x + t\delta v) - \varphi_\varepsilon(x) - \nabla \varphi_\varepsilon(x) \cdot t\delta v}{t^2 \delta^2} - \rho_0(v) \right| dx
$$
\n
$$
= \frac{1}{\delta^n} \int_{B_\delta(0)} \lim_{t \to 0} \lim_{\varepsilon \to 0} \left| \int_0^1 \left[ \langle \nabla^2 \varphi_\varepsilon(x + st\delta v) \cdot v, v \rangle - 2\rho_v(0) \right] (1 - s) ds \right| dx
$$
\n
$$
\leq \liminf_{t \to 0} \liminf_{\varepsilon \to 0} \frac{1}{\delta^n} \int_{B_\delta(0)} \left| \int_0^1 \left[ \langle \nabla^2 \varphi_\varepsilon(x + st\delta v) \cdot v, v \rangle - 2\rho_v(0) \right] \right. (1 - s) ds \right| dx
$$
\n
$$
\leq 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \varepsilon \to 0 \quad \text{as } \
$$

$$
\leq \liminf_{t \to 0} \liminf_{\varepsilon \to 0} \frac{1}{\delta^n} \int_0^1 \int_{B_\delta(st\delta v)} \left| \langle \nabla^2 \varphi_\varepsilon(y) \cdot v, v \rangle - \rho_v(0) \right| dy ds,
$$

where Fatou's lemma and Fubini's theorem were used successively. Since  $B(st\delta v,\delta) \subset B(0,(1+|v|)\delta)$ , independently of s and t, we can bound the above expression by

$$
\liminf_{\varepsilon \to 0} \frac{1}{\delta^n} \int_{B(0,(1+|v|)\delta)} |\langle \nabla^2 \varphi_{\varepsilon}(y) \cdot v, v \rangle - \rho_v(0)| dy
$$

$$
= \liminf_{\varepsilon \to 0} \frac{1}{\delta^n} \int_{B(0,(1+|v|)\delta)} |\int \zeta_{\varepsilon}(y-z) [\mu_v - \rho_v(0) \lambda_n](dz)| dy
$$

$$
\leq \liminf_{\varepsilon \to 0} \frac{1}{\delta^n} \int_{B(0,(1+|v|)\delta)} \int \zeta_{\varepsilon}(y-z) |\mu_v - \rho_v(0) \lambda_n|(dz) dy.
$$

When y varies in  $B(0,(1+|v|)\delta)$ , z stays in  $B(0,(1+|v|)\delta+\varepsilon)$ , which itself is included in  $B(0, C\delta)$  with  $C = 2 + |v|$ . So, after using Fubini's theorem and integrating out  $\zeta_{\varepsilon}(y-z) dy$ , we conclude that

$$
\frac{1}{\delta^n} \int_{B_\delta(0)} |q_v(x) - \rho_v(0)| dx \le ||\mu_v - \rho_v(0) \lambda_n||_{\text{TV}(B(0, C\delta))}.
$$

The conclusion is obtained by taking the limit  $\delta \to 0$ .

Once  $\nabla^2 \varphi$  has been identified as the density of the distributional Hessian of  $\varphi$ , it follows immediately that  $\Delta \varphi := \text{tr}(\nabla^2 \varphi)$  is the density of the distributional Laplacian of  $\varphi$ . (The trace of a matrix-valued nonnegative measure is singular if and only if the measure itself is singular.) □

Remark 14.27. The notion of a distributional Hessian on a Riemannian manifold is a bit subtle, which is why I did not state anything about it in Theorem 14.1. On the other hand, there is no difficulty in defining the distributional Laplacian.

# Second Appendix: Very elementary comparison arguments

There are well-developed theories of comparison estimates for secondorder linear differential equations; but the statement to be considered here can be proven by very elementary means.

Theorem 14.28 (One-dimensional comparison for second-order inequalities). Let  $\Lambda \in \mathbb{R}$ , and  $f \in C([0,1]) \cap C^2(0,1)$ ,  $f \geq 0$ . Then the following two statements are equivalent:

- (i)  $\ddot{f} + Af \leq 0$  in  $(0, 1)$ ;
- (ii) If  $\Lambda < \pi^2$  then for all  $t_0, t_1 \in [0, 1]$ ,

$$
f((1 - \lambda)t_0 + \lambda t_1) \geq \tau^{(1 - \lambda)}(|t_0 - t_1|) f(t_0) + \tau^{(\lambda)}(|t_0 - t_1|) f(t_1),
$$

where

$$
\tau^{(\lambda)}(\theta) = \begin{cases}\frac{\sin(\lambda \theta \sqrt{\Lambda})}{\sin(\theta \sqrt{\Lambda})} & \text{if } 0 < \Lambda < \pi^2 \\ \lambda & \text{if } \Lambda = 0 \\ \frac{\sinh(\lambda \theta \sqrt{-\Lambda})}{\sinh(\theta \sqrt{-\Lambda})} & \text{if } \Lambda < 0.\end{cases}
$$

If  $\Lambda = \pi^2$  then  $f(t) = c \sin(\pi t)$  for some  $c \geq 0$ ; finally if  $\Lambda > \pi^2$  then  $f=0.$ 

*Proof of Theorem 14.28.* The easy part is (ii)  $\Rightarrow$  (i). If  $\Lambda \geq \pi^2$  this is trivial. If  $\Lambda < \pi^2$ , take  $\lambda = 1/2$ , then a Taylor expansion shows that

$$
\tau^{(1/2)}(\theta) = \frac{1}{2} \Big( 1 + \frac{\theta \Lambda^2}{8} \Big) + o(\theta^3)
$$

and

$$
\frac{f(t_0) + f(t_1)}{2} = f\left(\frac{t_0 + t_1}{2}\right) + \frac{(t_0 - t_1)^2}{4} \ddot{f}\left(\frac{t_0 + t_1}{2}\right) + o(|t_0 - t_1|^2).
$$

So, if we fix  $t \in (0,1)$  and let  $t_0, t_1 \to t$  in such a way that  $t = (t_0+t_1)/2$ , we get

$$
\tau^{(1/2)}(|t_0 - t_1|) f(t_0) + \tau^{(1/2)}(|t_0 - t_1|) f(t_1) - f(t)
$$
  
= 
$$
\frac{(t_0 - t_1)^2}{8} (\ddot{f}(t) + Af(t) + o(1)).
$$

By assumption the left-hand side is nonnegative, so in the limit we recover  $\hat{f} + Af \leq 0$ .

Now consider the reverse implication (ii)  $\Rightarrow$  (i). By abuse of notation, let us write  $f(\lambda) = f((1 - \lambda)t_0 + \lambda t_1)$ , and denote by a prime the derivation with respect to  $\lambda$ ; so  $f'' + A\theta^2 f \leq 0$ ,  $\theta = |t_0 - t_1|$ . Let  $g(\lambda)$  be defined by the right-hand side of (ii); that is,  $\lambda \to g(\lambda)$  is the solution of  $g'' + A\theta^2 g = 0$  with  $g(0) = f(0), g(1) = f(1)$ . The goal is to show that  $f \geq g$  on [0, 1].

(a) Case  $\Lambda$  < 0. Let  $a > 0$  be any constant; then  $f_a := f + a$  still solves the same differential inequality as f, and  $f_a > 0$  (even if we did not assume  $f \geq 0$ , we could take a sufficiently large for this to be true). Let  $g_a$  be defined as the solution of  $g''_a + A\theta^2 g_a = 0$  with  $g_a(0) = f_a(0)$ ,  $g_a(1) = f_a(1)$ . As  $a \to 0$ ,  $f_a$  converges to f and  $g_a$  converges to g, so it is sufficient to show  $f_a \geq g_a$ . Therefore, without loss of generality we may assume that  $f,g$  are positive, so  $g/f$  is continuous.

If  $g/f$  attains its maximum at 0 or 1, then we are done. Otherwise, there is  $\lambda_0 \in (0,1)$  such that  $(g/f)''(\lambda_0) \leq 0$ ,  $(g/f)'(\lambda_0) = 0$ , and then the identity

$$
\left(\frac{g}{f}\right)'' = \frac{(g'' + Ag)}{f} - \frac{g}{f^2}(f'' + Af) - 2\frac{f'}{f}\left(\frac{g}{f}\right)' - 2A\frac{g}{f},
$$

evaluated at  $\lambda_0$ , yields  $0 > -2Ag/f$ , which is impossible.

(b) Case  $\Lambda = 0$ . This is the basic property of concave functions.

(c) Case  $0 < \Lambda < \pi^2$ . Let  $\theta = |t_0 - t_1| \leq 1$ . Since  $\theta \sqrt{\Lambda} < \pi$ , we can find a function w such that  $w'' + A\theta^2 w \le 0$  and  $w > 0$  on  $(0, 1)$ . (Just take a well-chosen sine or cosine function.) Then  $f_a := f + aw$ still satisfies the same differential inequality as  $f$ , and it is positive. Let  $g_a$  be defined by the equation  $g''_a + A\theta^2 g_a = 0$  with  $g_a(0) = f_a(0)$ ,  $g_a(1) = f_a(1)$ . As  $a \to 0$ ,  $f_a$  converges to f and  $g_a$  to g, so it is sufficient to show that  $f_a \geq g_a$ . Thus we may assume that f and g are positive, and  $f/g$  is continuous.

The rest of the reasoning is parallel to the case  $\Lambda < 0$ : If  $f/g$  attains its minimum at 0 or 1, then we are done. Otherwise, there is  $\lambda_0 \in (0,1)$ such that  $(f/g)''(\lambda_0) \geq 0$ ,  $(f/g)'(\lambda_0) = 0$ , and then the identity

$$
\left(\frac{f}{g}\right)'' = \frac{(f'' + Af)}{g} - \frac{f}{g^2}(g'' + Ag) - 2\frac{g'}{g}\left(\frac{f}{g}\right)' - 2A\frac{f}{g},
$$

evaluated at  $\lambda_0$ , yields  $0 < -2Af/g$ , which is impossible.

(d) Case  $\Lambda = \pi^2$ . Take  $t_0 = 0$ ,  $t_1 = 1$ . Then let  $g(\lambda) = \sin(\pi \lambda)$ , and let  $h := f/g$ . The differential equations  $f'' + Af \leq 0$  and  $g'' + Ag = 0$ combine to yield  $(h'g^2)' = h''g^2 + 2gh'g' \leq 0$ . So  $h'g^2$  is nonincreasing. If  $h'(\lambda_0) < 0$  for some  $t_0 \in (0,1)$ , then  $h'g^2(\lambda_0) < 0$  for all  $\lambda \ge \lambda_0$ , so  $h'(\lambda) \leq -C/g(\lambda)^2 \leq -C'/(1-\lambda)^2$  as  $\lambda \to 1$ , where  $C, C'$  are positive constants. It follows that  $h(\lambda)$  becomes negative for  $\lambda$  close to 1, which is impossible. If on the other hand  $h'(\lambda_0) > 0$ , then a similar reasoning shows that  $h(\lambda)$  becomes negative for  $\lambda$  close to 0. The conclusion is that h' is identically 0, so  $f/g$  is a constant.

(e) If  $\Lambda > \pi^2$ , then for all  $t_0, t_1 \in [0,1]$  with  $|t_0 - t_1| = \pi/\sqrt{\Lambda}$ , the function  $f(\lambda) = f(\lambda t_0 + (1 - \lambda t_1))$  is proportional to  $\sin(\pi \lambda)$ , by Case (d). By letting  $t_0$ ,  $t_1$  vary, it is easy to deduce that f is identically 0.  $□$ 

# Third Appendix: Jacobi fields forever

Let  $R : t \longrightarrow R(t)$  be a continuous map defined on [0, 1], valued in the space of  $n \times n$  symmetric matrices, and let  $\mathcal U$  be the space of functions  $u: t \longmapsto u(t) \in \mathbb{R}^n$  solving the second-order linear differential equation

$$
\ddot{u}(t) + R(t) u(t) = 0.
$$
\n(14.74)

By the theory of linear differential equations,  $\mathcal U$  is a  $(2n)$ -dimensional vector space and the map  $u \mapsto (u(0), \dot{u}(0))$  is a linear isomorphism  $\mathcal{U} \to \mathbb{R}^{2n}$ .

As explained in the beginning of this chapter, if  $\gamma : [0, 1] \to M$  is a geodesic in a Riemannian manifold  $M$ , and  $\xi$  is a Jacobi field along γ (that is, an infinitesimal variation of geodesic around γ), then the coordinates of  $\xi(t)$ , written in an orthonormal basis of  $T_{\gamma(t)}M$  evolving by parallel transport, satisfy an equation of the form (14.74).

It is often convenient to consider an array  $(u_1(t),\ldots,u_n(t))$  of solutions of (14.74); this can be thought of as a time-dependent matrix  $t \mapsto J(t)$  solving the differential (matrix) equation

$$
\ddot{J}(t) + R(t) J(t) = 0.
$$

Such a solution will be called a **Jacobi matrix**. If  $J$  is a Jacobi matrix and A is a constant matrix, then JA is still a Jacobi matrix.

Jacobi matrices enjoy remarkable properties, some of which are summarized in the next three statements. In the sequel, the time-interval [0, 1] and the symmetric matrix  $t \mapsto R(t)$  are fixed once for all, and the dot stands for time-derivation.

Proposition 14.29 (Jacobi matrices have symmetric logarithmic derivatives). Let J be a Jacobi matrix such that  $J(0)$  is invertible and  $J(0) J(0)^{-1}$  is symmetric. Let  $t_* \in [0,1]$  be largest possible such that  $J(t)$  is invertible for  $t < t_*$ . Then  $\dot{J}(t) J(t)^{-1}$  is symmetric for all  $t \in [0, t_*)$ .

Proposition 14.30 (Cosymmetrization of Jacobi matrices). Let  $J_0^1$  and  $J_1^0$  be Jacobi matrices defined by the initial conditions

$$
J_0^1(0) = I_n
$$
,  $J_0^1(0) = 0$ ,  $J_1^0(0) = 0$ ,  $J_1^0(0) = I_n$ ;

so that any Jacobi matrix can be written as

$$
J(t) = J_0^1(t) J(0) + J_1^0(t) \dot{J}(0).
$$
 (14.75)

Assume that  $J_1^0(t)$  is invertible for all  $t \in (0,1]$ . Then:

(a)  $S(t) := J_1^0(t)^{-1} J_0^1(t)$  is symmetric positive for all  $t \in (0,1]$ , and it is a decreasing function of t.

(b) There is a unique pair of Jacobi matrices  $(J^{1,0}, J^{0,1})$  such that

$$
J^{1,0}(0) = I_n, \quad J^{1,0}(1) = 0, \quad J^{0,1}(0) = 0, \quad J^{0,1}(1) = I_n;
$$

moreover  $\dot{J}^{1,0}(0)$  and  $\dot{J}^{0,1}(1)$  are symmetric.

(c) For  $t \in [0,1]$  let

$$
K(t) = t J_1^0(t)^{-1},
$$

extended by continuity at  $t = 0$  by  $K(0) = I_n$ . If J is a Jacobi matrix such that  $J(0)$  is invertible, and  $J(0)^* S(1) J(0)^{-1} = S(1)$  and  $\dot{J}(0) J(0)^{-1}$  are symmetric, then for any  $t \in [0,1]$  the matrices

$$
K(t) (J^{1,0}(t) J(0)) J(0)^{-1}
$$
 and  $K(t) (J^{0,1}(t) J(1)) J(0)^{-1}$ 

are symmetric. Moreover,  $\det K(t) > 0$  for all  $t \in [0, 1)$ .

Proposition 14.31 (Jacobi matrices with positive determinant). Let  $S(t)$  and  $K(t)$  be the matrices defined in Proposition 14.30. Let J be a Jacobi matrix such that  $J(0) = I_n$  and  $\dot{J}(0)$  is symmetric. Then the following properties are equivalent:

(i)  $\dot{J}(0) + S(1) > 0$ ; (ii)  $K(t) J^{0,1}(t) J(1) > 0$  for all  $t \in (0,1);$ (iii)  $K(t) J(t) > 0$  for all  $t \in [0,1]$ ; (iv) det  $J(t) > 0$  for all  $t \in [0, 1]$ .

The equivalence remains true if one replaces the strict inequalities in  $(i)$ – $(ii)$  by nonstrict inequalities, and the time-interval [0, 1] in (iii)–(iv)  $by [0, 1)$ .

Before proving these propositions, it is interesting to discuss their geometric interpretation:

• If  $\gamma(t) = \exp_x(t\nabla\psi(x))$  is a minimizing geodesic, then  $\dot{\gamma}(t) =$  $\nabla \psi(t, \gamma(t))$ , where  $\psi$  solves the Hamilton–Jacobi equation

$$
\begin{cases} \frac{\partial \psi(t,x)}{\partial t} + \frac{|\nabla \psi(t,x)|^2}{2} = 0 \\ \psi(0, \cdot) = \psi; \end{cases}
$$

at least before the first shock of the Hamilton–Jacobi equation. This corresponds to Proposition 14.29 with  $J(0) = I_n$ ,  $\dot{J}(0) = \nabla^2 \psi(x)$ ,  $\dot{J}(t) J(t)^{-1} = \nabla^2 \psi(t, \gamma(t))$ . (Here  $\nabla^2 \psi(x)$  and  $\nabla^2 \psi(t, \gamma(t))$  are identified with the matrix of their respective coordinates, as usual in a varying orthonormal basis).

- The Jacobi matrices  $J_0^1(t)$  and  $J_1^0(t)$  represent  $\partial_x F_t$  and  $\partial_v F_t$  respectively, where  $F_t(x, v) = (\exp_x(tv), (d/dt) \exp_x(tv))$  is the geodesic flow at time t. So the hypothesis of invertibility of  $J_1^0(t)$  in Proposition 14.30 corresponds to an assumption of nonfocalization along the geodesic  $\gamma$ .
- The formula

$$
\gamma(t) = \exp_x \left( -\nabla_x \frac{d(\,\cdot\,, \gamma(t))^2}{2} \right)
$$

yields, after differentiation,

$$
0 = J_0^1(t) - J_1^0(t) \frac{H(t)}{t},
$$

where (modulo identification)

$$
H(t) = \nabla_x^2 \frac{d(\cdot, \gamma(t))^2}{2}.
$$

(The extra  $t$  in the denominator comes from time-reparameterization.) So  $S(t) = J_1^0(t)^{-1} J_0^1(t) = H(t)/t$  should be symmetric.

The assumption

$$
J(0) = I_n, \qquad \dot{J}(0) + S(1) \ge 0
$$

is the Jacobi field version of the formulas

$$
\gamma(t) = \exp_x(t\nabla\psi(x)), \qquad \nabla^2\psi(x) + \nabla_x^2 \frac{d(\cdot, \exp_x \nabla\psi(x))^2}{2} \ge 0.
$$

The latter inequality holds true if  $\psi$  is  $d^2/2$ -convex, so Proposition 14.31 implies the last part of Theorem 11.3, according to which the Jacobian of the optimal transport map remains positive for  $0 < t < 1$ .

Now we can turn to the proofs of Propositions 14.29 to 14.31.

Proof of Proposition 14.29. The argument was already mentioned in the beginning of this chapter: the matrix  $U(t) = \dot{J}(t) J(t)^{-1}$  satisfies the Ricatti equation

$$
\dot{U}(t) + U(t)^2 + R(t) = 0
$$

on  $[0, t_*)$ ; and since R is symmetric the transpose  $U(t)^*$  of  $U(t)$  satisfies the same equation. By assumption  $U(0) = U(0)$ <sup>\*</sup>; so the Cauchy– Lipschitz uniqueness theorem implies  $U(t) = U(t)^*$  for all  $t$ . □

Proof of Proposition 14.30. First of all, the identity in (14.75) follows immediately from the observation that both sides solve the Jacobi equation with the same initial conditions.

Let now  $t \in (0,1], w \in \mathbb{R}^n$  and  $\widehat{w} = J_1^0(t)^{-1} J_0^1(t) w$ ; so that  $J_1^0(t)\,\hat{w} = J_0^1(t)\,w.$  The function  $s \mapsto u(s) = J_0^1(s)\,w - J_1^0(s)\,\hat{w}$  belongs to U and satisfies  $u(t) = 0$ . Moreover,  $w = u(0)$ ,  $\hat{w} = -\hat{u}(0)$ . So the matrix  $S(t) = J_1^0(t)^{-1} J_0^1(t)$  can be interpreted as the linear map  $u(0) \rightarrow -\dot{u}(0)$ , where  $s \rightarrow u(s)$  varies in the vector space  $\mathcal{U}_t$  of solutions of the Jacobi equation (14.74) satisfying  $u(t) = 0$ . To prove the symmetry of  $S(t)$  it suffices to check that for any two  $u, v$  in  $\mathcal{U}_t$ ,

$$
\langle u(0), \dot{v}(0) \rangle = \langle \dot{u}(0), v(0) \rangle,
$$

where  $\langle \, \cdot \, , \, \cdot \, \rangle$  stands for the usual scalar product on  $\mathbb{R}^n.$  But

$$
\frac{d}{ds}\Big(\langle u(s), \dot{v}(s)\rangle - \langle \dot{u}(s), v(s)\rangle \Big) = -\langle u(s), R(s) v(s)\rangle + \langle R(s) u(s), v(s)\rangle
$$
  
= 0 (14.76)

by symmetry of  $R$ . This implies the symmetry of  $S$  since

$$
\langle u(0), \dot{v}(0) \rangle - \langle \dot{u}(0), v(0) \rangle = \langle u(t), \dot{v}(t) \rangle - \langle \dot{u}(t), v(t) \rangle = 0.
$$

Remark 14.32. A knowledgeable reader may have noticed that this is a "symplectic" argument (related to the Hamiltonian nature of the geodesic flow): if  $\mathbb{R}^n \times \mathbb{R}^n$  is equipped with its natural symplectic form

$$
\omega\big((u,\dot{u}),(v,\dot{v})\big)=\langle\dot{u},v\rangle-\langle\dot{v},u\rangle,
$$

then the flow  $(u(s), \dot{u}(s)) \longmapsto (u(t), \dot{u}(t))$ , where  $u \in \mathcal{U}$ , preserves  $\omega$ . The subspaces  $\mathcal{U}_0 = \{u \in \mathcal{U}; u(0) = 0\}$  and  $\dot{\mathcal{U}}_0 = \{u \in \mathcal{U}; \dot{u}(0) = 0\}$  are Lagrangian: this means that their dimension is the half of the dimension

of U, and that  $\omega$  vanishes identically on each of them. Moreover  $\omega$  is nondegenerate on  $\mathcal{U}_0 \times \mathcal{U}_0$ , providing an identification of these spaces. Then  $\mathcal{U}_t$  is also Lagrangian, and if one writes it as a graph in  $\mathcal{U}_0 \times \dot{\mathcal{U}}_0$ , it is the graph of a symmetric operator.

Back to the proof of Proposition 14.29, let us show that  $S(t)$  is a decreasing function of t. To reformulate the above observations we write, for any  $w \in \mathbb{R}^n$ ,

$$
\langle S(t)w, w \rangle = -\Big\langle w, \ \frac{\partial u}{\partial s}(0, t) \Big\rangle,
$$

where  $u(s,t)$  is defined by

$$
\begin{cases}\frac{\partial^2 u(s,t)}{\partial s^2} + R(s) u(s,t) = 0 \\ u(t,t) = 0 \\ u(0,t) = w.\end{cases} (14.77)
$$

So

$$
\langle \dot{S}(t)w, w \rangle = -\langle w, \frac{\partial^2 u}{\partial s \partial t}(0, t) \rangle
$$
  
$$
= -\langle w, \frac{\partial}{\partial s}(\partial_t u)(0, t) \rangle = -\langle u(0), \frac{\partial v}{\partial s}(0) \rangle,
$$

where  $s \mapsto v(s) = (\partial_t u)(s,t)$  and  $s \mapsto u(s) = u(s,t)$  are solutions of the Jacobi equation. Moreover, by differentiating the conditions in (14.77) one obtains

$$
v(t) + \frac{\partial u}{\partial s}(t) = 0; \qquad v(0) = 0. \tag{14.78}
$$

By (14.76) again,

$$
-\langle u(0), \frac{\partial v}{\partial s}(0)\rangle = -\langle \frac{\partial u}{\partial s}(0), v(0)\rangle - \langle u(t), \frac{\partial v}{\partial s}(t)\rangle + \langle \frac{\partial u}{\partial s}(t), v(t)\rangle.
$$

The first two terms in the right-hand side vanish because  $v(0) = 0$  and  $u(t) = u(t, t) = 0$ . Combining this with the first identity in (14.78) one finds in the end

$$
\langle \dot{S}(t)w, w \rangle = -||v(t)||^2. \tag{14.79}
$$

We already know that  $v(0) = 0$ ; if in addition  $v(t) = 0$  then  $0 =$  $v(t) = J_1^0(t)\dot{v}(0)$ , so (by invertibility of  $J_1^0(t)$ )  $\dot{v}(0) = 0$ , and v vanishes identically; then by (14.78)  $(du/ds)$  vanishes at  $s = t$ , and since also  $u(t) = 0$  we know that u vanishes identically, which implies  $w = 0$ . In other words, the right-hand side of (14.79) is strictly negative unless  $w = 0$ ; this means that  $S(t)$  is a strictly decreasing function of t. Thus the proof of (a) is finished.

To prove (b) it is sufficient to exhibit the matrices  $J^{1,0}(t)$  and  $J^{0,1}(t)$ explicitly in terms of  $J_0^1$  and  $J_1^0$ :

$$
J^{1,0}(t) = J_0^1(t) - J_1^0(t) J_1^0(1)^{-1} J_0^1(1); \t J^{0,1}(t) = J_1^0(t) J_1^0(1)^{-1}.
$$
\n(14.80)\nMoreover, 
$$
J^{1,0}(0) = -J_1^0(1)^{-1} J_0^1(1)
$$
 and 
$$
J^{0,1}(t) = J_1^0(1) J_1^0(1)^{-1}
$$
 are

symmetric in view of (a) and Proposition 14.29. Also

$$
K(t) J^{1,0}(t) J(0) J(0)^{-1} = t J_1^0(t)^{-1} \left( J_0^1(t) - J_1^0(t) J_1^0(1)^{-1} J_0^1(1) \right)
$$
  
= 
$$
t \left( J_1^0(t)^{-1} J_0^1(t) - J_1^0(1)^{-1} J_0^1(1) \right)
$$

is positive symmetric since by (a) the matrix  $S(t) = J_1^0(t)^{-1} J_0^1(t)$  is a *strictly* decreasing function of t. In particular  $K(t)$  is invertible for  $t > 0$ ; but since  $K(0) = I_n$ , it follows by continuity that  $\det K(t)$ remains positive on  $[0, 1)$ .

Finally, if J satisfies the assumptions of (c), then  $S(1) J(0)^{-1}$  is symmetric (because  $S(1)^* = S(1)$ ). Then

$$
(K(t) J^{0,1}(t)) J(1) J(0)^{-1}
$$
  

$$
= (t J_1^0(t)^{-1} J_1^0(t) J_1^0(1)^{-1}) (J_0^1(1) + J_1^0(1) \dot{J}(0)) J(0)^{-1}
$$
  

$$
= t (J_1^0(1)^{-1} J_0^1(1) J(0)^{-1} + \dot{J}(0) J(0)^{-1})
$$

is also symmetric. ⊓⊔

Proof of Proposition 14.31. Assume (i); then, by the formulas in the end of the proof of Proposition 14.30, with  $J(0) = I_n$ ,

$$
K(t) J^{1,0}(t) = t [S(t) - S(1)]; \qquad K(t) J^{0,1}(t) J(1) = t [S(1) + J(0)].
$$

As we already noticed, the first matrix is positive for  $t \in (0,1)$ ; and the second is also positive, by assumption. In particular (ii) holds true.

The implication (ii)  $\Rightarrow$  (iii) is obvious since  $J(t) = J^{1,0}(t) +$  $J^{0,1}(t)$  J(1) is the sum of two positive matrices for  $t \in (0,1)$ . (At  $t = 0$ one sees directly  $K(0) J(0) = I_n$ .

If (iii) is true then  $(\det K(t))(\det J(t)) > 0$  for all  $t \in [0,1)$ , and we already know that det  $K(t) > 0$ ; so det  $J(t) > 0$ , which is (iv).

It remains to prove (iv)  $\Rightarrow$  (i). Recall that  $K(t) J(t) = t[S(t)+J(0)];$ since det  $K(t) > 0$ , the assumption (iv) is equivalent to the statement that the symmetric matrix  $A(t) = t S(t) + t J(0)$  has positive determinant for all  $t \in (0,1]$ . The identity  $t S(t) = K(t) J_0^1(t)$  shows that  $A(t)$  approaches  $I_n$  as  $t \to 0$ ; and since none of its eigenvalues vanishes,  $A(t)$  has to remain positive for all t. So  $S(t) + J(0)$  is positive for all  $t \in (0,1]$ ; but S is a decreasing function of t, so this is equivalent to  $S(1) + J(0) > 0$ , which is condition (i).

The last statement in Proposition 14.31 is obtained by similar arguments and its proof is omitted. □

### Bibliographical notes

Recommended textbooks about Riemannian geometry are the ones by do Carmo [306], Gallot, Hulin and Lafontaine [394] and Chavel [223]. All the necessary background about Hessians, Laplace–Beltrami operators, Jacobi fields and Jacobi equations can be found there.

Apart from these sources, a review of comparison methods based on Ricci curvature bounds can be found in [846].

Formula (14.1) does not seem to appear in standard textbooks of Riemannian geometry, but can be derived with the tools found therein, or by comparison with the sphere/hyperbolic space. On the sphere, the computation can be done directly, thanks to a classical formula of spherical trigonometry: If  $a, b, c$  are the lengths of the sides of a triangle drawn on the unit sphere  $S^2$ , and  $\gamma$  is the angle opposite to c, then  $\cos c = \cos a \cos b + \sin a \sin b \cos \gamma$ . A more standard computation usually found in textbooks is the asymptotic expansion of the perimeter of a circle centered at x with (geodesic) radius r, as  $r \to 0$ . Y. H. Kim and McCann [520, Lemma 4.5] recently generalized  $(14.1)$  to more general cost functions, and curves of possibly differing lengths.

The differential inequalities relating the Jacobian of the exponential map to the Ricci curvature can be found (with minor variants) in a number of sources, e.g. [223, Section 3.4]. They usually appear in conjunction with volume comparison principles such as the Heintze– Kärcher, Lévy–Gromov, Bishop–Gromov theorems, all of which express the idea that if the Ricci curvature is bounded below by  $K$ , and the dimension is less than  $N$ , then volumes along geodesic fields grow no faster than volumes in model spaces of constant sectional curvature having dimension  $N$  and Ricci curvature identically equal to  $K$ . These computations are usually performed in a smooth setting; their adaptation to the nonsmooth context of semiconvex functions has been achieved only recently, first by Cordero-Erausquin, McCann and Schmuckenschläger  $[246]$  (in a form that is somewhat different from the one presented here) and more recently by various sets of authors [247, 577, 761].

Bochner's formula appears, e.g., as [394, Proposition 4.15] (for a vector field  $\xi = \nabla \psi$ ) or as [680, Proposition 3.3 (3)] (for a vector field  $\xi$  such that  $\nabla \xi$  is symmetric, i.e. the 1-form  $p \to \xi \cdot p$  is closed). In both cases, it is derived from properties of the Riemannian curvature tensor. Another derivation of Bochner's formula for a gradient vector field is via the properties of the square distance function  $d(x_0, x)^2$ ; this is quite simple, and not far from the presentation that I have followed, since  $d(x_0, x)^2/2$  is the solution of the Hamilton–Jacobi equation at time 1, when the initial datum is 0 at  $x_0$  and  $+\infty$  everywhere else. But I thought that the explicit use of the Lagrangian/Eulerian duality would make Bochner's formula more intuitive to the readers, especially those who have some experience of fluid mechanics.

There are several other Bochner formulas in the literature; Chapter 7 of Petersen's book [680] is entirely devoted to that subject. In fact "Bochner formula" is a generic name for many identities involving commutators of second-order differential operators and curvature.

The examples (14.10) are by now standard; they have been discussed for instance by Bakry and Qian [61], in relation with spectral gap estimates. When the dimension  $N$  is an integer, these reference spaces are obtained by "projection" of the model spaces with constant sectional curvature.

The practical importance of separating out the direction of motion is implicit in Cordero-Erausquin, McCann and Schmuckenschläger [246], but it was Sturm who attracted my attention on this. To implement this idea in the present chapter, I essentially followed the discussion in [763, Section 1]. Also the integral bound (14.56) can be found in this reference.

Many analytic and geometric consequences of Ricci curvature bounds are discussed in Riemannian geometry textbooks such as the one by

Gallot, Hulin and Lafontaine [394], and also in hundreds of research papers.

Cordero-Erausquin, McCann and Schmuckenschläger [246, Section 2] express differential inequalities about the Jacobian determinant in terms of volume distortion coefficients; all the discussion about distortion coefficients is inspired from this reference, and most of the material in the Third Appendix is also adapted from that source. This Appendix was born from exchanges with Cordero-Erausquin, who also unwillingly provided its title. It is a pleasure to acknowledge the help of my geometer colleagues (Ghys, Sikorav, Welschinger) in getting the "symplectic argument" for the proof of Proposition 14.29.

Concerning Bakry's approach to curvature-dimension bounds, among many sources one can consult the survey papers [54] and [545].

The almost everywhere second differentiability of convex functions was proven by Alexandrov in 1942 [16]. The proof which I gave in the first Appendix has several points in common with the one that can be found in [331, pp. 241–245], but I have modified the argument to make it look as much as possible like the proof of Rademacher's theorem (Theorem 10.8(ii)). The resulting proof is a bit redundant in some respects, but hopefully it will look rather natural to the reader; also I think it is interesting to have a parallel presentation of the theorems by Rademacher and Alexandrov. Alberti and Ambrosio [11, Theorem 7.10] prove Alexandrov's theorem by a quite different technique, since they deduce it from Rademacher's theorem (in the form of the almost everywhere existence of the tangent plane to a Lipschitz graph) together with the area formula. Also they directly establish the differentiability of the gradient, and then deduce the existence of the Hessian; that is, they prove formulation (i) in Theorem 14.1 and then deduce (ii), while in the First Appendix I did it the other way round.

Lebesgue's density theorem can be found for instance in [331, p. 42].

The theorem according to which a nonincreasing function  $\mathbb{R} \to \mathbb{R}$ is differentiable almost everywhere is a well-know result, which can be deduced as a corollary of [318, Theorems 7.2.4 and 7.2.7].
# Photorealistic Text-to-Image Diffusion Models with Deep Language Understanding

<PERSON><PERSON><PERSON><sup>∗</sup>, <PERSON><sup>∗</sup>, <PERSON><PERSON><PERSON><PERSON><sup>†</sup>, <PERSON><PERSON><sup>†</sup>, <PERSON><sup>†</sup>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON> , <PERSON>† , <PERSON><sup>∗</sup>

{sahari<PERSON>,william<PERSON>,mnorouzi}@google.com {srbs,lala,jwhang,jonathan<PERSON>,davidfleet}@google.com

> Google Research, Brain Team Toronto, Ontario, Canada

## Abstract

We present Imagen, a text-to-image diffusion model with an unprecedented degree of photorealism and a deep level of language understanding. Imagen builds on the power of large transformer language models in understanding text and hinges on the strength of diffusion models in high-fidelity image generation. Our key discovery is that generic large language models (e.g. T5), pretrained on text-only corpora, are surprisingly effective at encoding text for image synthesis: increasing the size of the language model in Imagen boosts both sample fidelity and imagetext alignment much more than increasing the size of the image diffusion model. Imagen achieves a new state-of-the-art FID score of 7.27 on the COCO dataset, without ever training on COCO, and human raters find Imagen samples to be on par with the COCO data itself in image-text alignment. To assess text-to-image models in greater depth, we introduce DrawBench, a comprehensive and challenging benchmark for text-to-image models. With DrawBench, we compare Imagen with recent methods including VQ-GAN+CLIP, Latent Diffusion Models, GLIDE and DALL-E 2, and find that human raters prefer Imagen over other models in side-byside comparisons, both in terms of sample quality and image-text alignment. See [imagen.research.google](https://imagen.research.google/) for an overview of the results.

## 1 Introduction

Multimodal learning has come into prominence recently, with text-to-image synthesis [\[53,](#page-12-0) [12,](#page-10-0) [57\]](#page-13-0) and image-text contrastive learning [\[49,](#page-12-1) [31,](#page-11-0) [74\]](#page-14-0) at the forefront. These models have transformed the research community and captured widespread public attention with creative image generation [\[22,](#page-11-1) [54\]](#page-12-2) and editing applications [\[21,](#page-11-2) [41,](#page-12-3) [34\]](#page-11-3). To pursue this research direction further, we introduce Imagen, a text-to-image diffusion model that combines the power of transformer language models (LMs) [\[15,](#page-10-1) [52\]](#page-12-4) with high-fidelity diffusion models [\[28,](#page-11-4) [29,](#page-11-5) [16,](#page-10-2) [41\]](#page-12-3) to deliver an unprecedented degree of photorealism and a deep level of language understanding in text-to-image synthesis. In contrast to prior work that uses only image-text data for model training  $[e.g., 53, 41]$  $[e.g., 53, 41]$  $[e.g., 53, 41]$  $[e.g., 53, 41]$ , the key finding behind Imagen is that text embeddings from large LMs [\[52,](#page-12-4) [15\]](#page-10-1), pretrained on text-only corpora, are remarkably effective for text-to-image synthesis. See Fig. [1](#page-1-0) for select samples.

Imagen comprises a frozen T5-XXL [\[52\]](#page-12-4) encoder to map input text into a sequence of embeddings and a 64×64 image diffusion model, followed by two super-resolution diffusion models for generating

<sup>∗</sup>Equal contribution.

<sup>†</sup>Core contribution.

Image /page/1/Picture/0 description: The word "imagen" is spelled out in green leafy letters that appear to be growing out of an open book. The letters are three-dimensional and have a textured, organic appearance. Small red berries are visible at the base of some of the letters. Lush green leaves and stems sprout from the book, intertwining with the letters. The background is a plain, light gray, creating a clean and minimalist aesthetic. The overall image conveys a sense of growth, nature, and knowledge.

fairytale book.

<span id="page-1-0"></span>Image /page/1/Picture/1 description: A Shiba Inu dog wearing a straw hat, blue sunglasses, a life vest, and blue shorts rides a bicycle. The dog has a backpack on its back and is smiling with its tongue out. The background is blurred green foliage and a paved path.

Sprouts in the shape of text 'Imagen' coming out of a A photo of a Shiba Inu dog with a backpack riding a A high contrast portrait of a very happy fuzzy panda bike. It is wearing sunglasses and a beach hat.

Image /page/1/Picture/3 description: A panda wearing a chef's hat and apron is rolling out dough with a rolling pin. The panda is in a kitchen with a painting of flowers on the wall. The panda has its tongue sticking out slightly.

dressed as a chef in a high end kitchen making dough. There is a painting of flowers on the wall behind him.

Image /page/1/Picture/5 description: A teddy bear wearing a white swim cap and goggles swims in a swimming pool. The teddy bear is in the middle of a lane, with lane lines visible in the foreground and background. The water is splashing around the teddy bear as it swims.

Teddy bears swimming at the Olympics 400m Butterfly event.

Image /page/1/Picture/7 description: A corgi dog is peeking out from a house made of sushi rolls. The sushi rolls are arranged to form the walls and roof of the house, with salmon and avocado visible inside some of the rolls. The dog has its front paws resting on the bottom row of sushi rolls, and its mouth is open in a happy expression. The background is blurred and appears to be a light-colored surface.

Image /page/1/Picture/9 description: A close-up, eye-level shot shows a sloth holding a small, ornate wooden treasure chest. The sloth has long, shaggy gray fur, dark eye patches, and a gentle smile. Its large, dark eyes are looking directly at the viewer. The treasure chest is made of dark wood with metal accents and a golden clasp shaped like a stylized face. A soft, golden light emanates from behind the chest, illuminating the sloth's fur and the chest itself. The background is softly blurred, suggesting a natural, leafy environment.

A cute corgi lives in a house made out of sushi. A cute sloth holding a small treasure chest. A bright golden glow is coming from the chest.

Image /page/1/Picture/11 description: The image displays a triptych of surreal, digitally rendered scenes. The leftmost panel features a red rocket ship with a human brain as its pilot, soaring through a starry night sky with a large moon and shooting stars. The middle panel shows a personified dragon fruit, depicted as a muscular fighter with green arms and a black belt, standing in a snowy landscape against a bright blue, snowy sky. The rightmost panel presents a close-up of a strawberry carved into a mug shape, filled with white seeds, and resting on a reflective surface, possibly water, with its reflection visible below.

A brain riding a rocketship heading towards the moon. A dragon fruit wearing karate belt in the snow. A strawberry mug filled with white sesame seeds. The mug is floating in a dark chocolate sea.

Figure 1: Select  $1024 \times 1024$  Imagen samples for various text inputs. We only include photorealistic images in this figure and leave artistic content to the Appendix, since generating photorealistic images is more challenging from a technical point of view. Figs. A.1 to A.3 show more samples.

 $256 \times 256$  and  $1024 \times 1024$  images (see Fig. A.4). All diffusion models are conditioned on the text embedding sequence and use classifier-free guidance [\[27\]](#page-11-6). Imagen relies on new sampling techniques to allow usage of large guidance weights without sample quality degradation observed in prior work, resulting in images with higher fidelity and better image-text alignment than previously possible.

While conceptually simple and easy to train, Imagen yields surprisingly strong results. Imagen outperforms other methods on COCO [\[36\]](#page-11-7) with zero-shot FID-30K of 7.27, significantly outperforming prior work such as GLIDE  $[41]$  (at 12.4) and the concurrent work of DALL-E 2 [\[54\]](#page-12-2) (at 10.4). Our zero-shot FID score is also better than state-of-the-art models trained on COCO, e.g., Make-A-Scene [\[22\]](#page-11-1) (at 7.6). Additionally, human raters indicate that generated samples from Imagen are on-par in image-text alignment to the reference images on COCO captions.

We introduce DrawBench, a new structured suite of text prompts for text-to-image evaluation. Draw-Bench enables deeper insights through a multi-dimensional evaluation of text-to-image models, with text prompts designed to probe different semantic properties of models. These include compositionality, cardinality, spatial relations, the ability to handle complex text prompts or prompts with rare words, and they include creative prompts that push the limits of models' ability to generate highly implausible scenes well beyond the scope of the training data. With DrawBench, extensive human evaluation shows that Imagen outperforms other recent methods [\[57,](#page-13-0) [12,](#page-10-0) [54\]](#page-12-2) by a significant margin. We further demonstrate some of the clear advantages of the use of large pre-trained language models [\[52\]](#page-12-4) over multi-modal embeddings such as CLIP [\[49\]](#page-12-1) as a text encoder for Imagen.

Key contributions of the paper include:

- 1. We discover that large frozen language models trained only on text data are surprisingly very effective text encoders for text-to-image generation, and that scaling the size of frozen text encoder improves sample quality significantly more than scaling the size of image diffusion model.
- 2. We introduce *dynamic thresholding*, a new diffusion sampling technique to leverage high guidance weights and generating more photorealistic and detailed images than previously possible.
- 3. We highlight several important diffusion architecture design choices and propose *Efficient U-Net*, a new architecture variant which is simpler, converges faster and is more memory efficient.
- 4. We achieve a new state-of-the-art COCO FID of 7.27. Human raters find Imagen to be on-par with the reference images in terms of image-text alignment.
- 5. We introduce DrawBench, a new comprehensive and challenging evaluation benchmark for the text-to-image task. On DrawBench human evaluation, we find Imagen to outperform all other work, including the concurrent work of DALL-E 2 [\[54\]](#page-12-2).

## 2 Imagen

Imagen consists of a text encoder that maps text to a sequence of embeddings and a cascade of conditional diffusion models that map these embeddings to images of increasing resolutions (see Fig. A.4). In the following subsections, we describe each of these components in detail.

### 2.1 Pretrained text encoders

Text-to-image models need powerful semantic text encoders to capture the complexity and compositionality of arbitrary natural language text inputs. Text encoders trained on paired image-text data are standard in current text-to-image models; they can be trained from scratch [\[41,](#page-12-3) [53\]](#page-12-0) or pretrained on image-text data [\[54\]](#page-12-2) (e.g., CLIP [\[49\]](#page-12-1)). The image-text training objectives suggest that these text encoders may encode visually semantic and meaningful representations especially relevant for the text-to-image generation task. Large language models can be another models of choice to encode text for text-to-image generation. Recent progress in large language models (e.g., BERT [\[15\]](#page-10-1), GPT [\[47,](#page-12-5) [48,](#page-12-6) [7\]](#page-10-3), T5 [\[52\]](#page-12-4)) have led to leaps in textual understanding and generative capabilities. Language models are trained on text only corpus significantly larger than paired image-text data, thus being exposed to a very rich and wide distribution of text. These models are also generally much larger than text encoders in current image-text models [\[49,](#page-12-1) [31,](#page-11-0) [80\]](#page-14-1) (e.g. PaLM [\[11\]](#page-10-4) has 540B parameters, while CoCa [\[80\]](#page-14-1) has a  $\approx$  1B parameter text encoder).

It thus becomes natural to explore both families of text encoders for the text-to-image task. Imagen explores pretrained text encoders: BERT [\[15\]](#page-10-1), T5 [\[51\]](#page-12-7) and CLIP [\[46\]](#page-12-8). For simplicity, we freeze the weights of these text encoders. Freezing has several advantages such as offline computation of embeddings, resulting in negligible computation or memory footprint during training of the textto-image model. In our work, we find that there is a clear conviction that scaling the text encoder size improves the quality of text-to-image generation. We also find that while T5-XXL and CLIP

text encoders perform similarly on simple benchmarks such as MS-COCO, human evaluators prefer T5-XXL encoders over CLIP text encoders in both image-text alignment and image fidelity on DrawBench, a set of challenging and compositional prompts. We refer the reader to Section [4.4](#page-6-0) for summary of our findings, and Appendix D.1 for detailed ablations.

### 2.2 Diffusion models and classifier-free guidance

Here we give a brief introduction to diffusion models; a precise description is in Appendix A. Diffusion models [\[63,](#page-13-1) [28,](#page-11-4) [65\]](#page-13-2) are a class of generative models that convert Gaussian noise into samples from a learned data distribution via an iterative denoising process. These models can be conditional, for example on class labels, text, or low-resolution images [e.g. [16,](#page-10-2) [29,](#page-11-5) [59,](#page-13-3) [58,](#page-13-4) [75,](#page-14-2) [41,](#page-12-3) [54\]](#page-12-2). A diffusion model  $\hat{\mathbf{x}}_{\theta}$  is trained on a denoising objective of the form

$$
\mathbb{E}_{\mathbf{x},\mathbf{c},\boldsymbol{\epsilon},t}\big[w_t\|\hat{\mathbf{x}}_\theta(\alpha_t\mathbf{x}+\sigma_t\boldsymbol{\epsilon},\mathbf{c})-\mathbf{x}\|_2^2\big]\tag{1}
$$

where (x, c) are data-conditioning pairs,  $t \sim \mathcal{U}([0,1])$ ,  $\epsilon \sim \mathcal{N}(0, \mathbf{I})$ , and  $\alpha_t, \sigma_t, w_t$  are functions of t that influence sample quality. Intuitively,  $\hat{\mathbf{x}}_{\theta}$  is trained to denoise  $\mathbf{z}_t := \alpha_t \mathbf{x} + \sigma_t \boldsymbol{\epsilon}$  into x using a squared error loss, weighted to emphasize certain values of t. Sampling such as the ancestral sampler [\[28\]](#page-11-4) and DDIM [\[64\]](#page-13-5) start from pure noise  $z_1 \sim \mathcal{N}(0, I)$  and iteratively generate points  $z_{t_1}, \ldots, z_{t_T}$ , where  $1 = t_1 > \cdots > t_T = 0$ , that gradually decrease in noise content. These points are functions of the x-predictions  $\hat{\mathbf{x}}_0^t := \hat{\mathbf{x}}_{\theta}(\mathbf{z}_t, \mathbf{c})$ .

Classifier guidance [\[16\]](#page-10-2) is a technique to improve sample quality while reducing diversity in conditional diffusion models using gradients from a pretrained model  $p(c|z_t)$  during sampling. *Classifierfree guidance* [\[27\]](#page-11-6) is an alternative technique that avoids this pretrained model by instead jointly training a single diffusion model on conditional and unconditional objectives via randomly dropping c during training (e.g. with 10% probability). Sampling is performed using the adjusted x-prediction  $(\mathbf{z}_t - \sigma \tilde{\boldsymbol{\epsilon}}_{\theta})/\alpha_t$ , where

$$
\tilde{\boldsymbol{\epsilon}}_{\theta}(\mathbf{z}_t, \mathbf{c}) = w \boldsymbol{\epsilon}_{\theta}(\mathbf{z}_t, \mathbf{c}) + (1 - w) \boldsymbol{\epsilon}_{\theta}(\mathbf{z}_t).
$$
\n(2)

Here,  $\epsilon_{\theta}(\mathbf{z}_t, \mathbf{c})$  and  $\epsilon_{\theta}(\mathbf{z}_t)$  are conditional and unconditional  $\epsilon$ -predictions, given by  $\epsilon_{\theta} := (\mathbf{z}_t - \mathbf{z}_t)$  $\alpha_t \hat{\mathbf{x}}_{\theta}$ / $\sigma_t$ , and w is the *guidance weight*. Setting  $w = 1$  disables classifier-free guidance, while increasing  $w > 1$  strengthens the effect of guidance. Imagen depends critically on classifier-free guidance for effective text conditioning.

### 2.3 Large guidance weight samplers

We corroborate the results of recent text-guided diffusion work [\[16,](#page-10-2) [41,](#page-12-3) [54\]](#page-12-2) and find that increasing the classifier-free guidance weight improves image-text alignment, but damages image fidelity producing highly saturated and unnatural images [\[27\]](#page-11-6). We find that this is due to a train-test mismatch arising from high guidance weights. At each sampling step t, the x-prediction  $\hat{\mathbf{x}}_0^t$  must be within the same bounds as training data x, i.e. within  $[-1, 1]$ , but we find empirically that high guidance weights cause x-predictions to exceed these bounds. This is a train-test mismatch, and since the diffusion model is iteratively applied on its own output throughout sampling, the sampling process produces unnatural images and sometimes even diverges. To counter this problem, we investigate *static thresholding* and *dynamic thresholding*. See Appendix Fig. A.31 for reference implementation of the techniques and Appendix Fig. A.9 for visualizations of their effects.

Static thresholding: We refer to elementwise clipping the x-prediction to [−1, 1] as *static thresholding*. This method was in fact used but not emphasized in previous work [\[28\]](#page-11-4), and to our knowledge its importance has not been investigated in the context of guided sampling. We discover that static thresholding is essential to sampling with large guidance weights and prevents generation of blank images. Nonetheless, static thresholding still results in over-saturated and less detailed images as the guidance weight further increases.

Dynamic thresholding: We introduce a new *dynamic thresholding* method: at each sampling step we set s to a certain percentile absolute pixel value in  $\hat{x}_0^t$ , and if  $s > 1$ , then we threshold  $\hat{x}_0^t$ to the range  $[-s, s]$  and then divide by s. Dynamic thresholding pushes saturated pixels (those near -1 and 1) inwards, thereby actively preventing pixels from saturation at each step. We find that dynamic thresholding results in significantly better photorealism as well as better image-text alignment, especially when using very large guidance weights.

### 2.4 Robust cascaded diffusion models

Imagen utilizes a pipeline of a base  $64 \times 64$  model, and two text-conditional super-resolution diffusion models to upsample a  $64 \times 64$  generated image into a  $256 \times 256$  image, and then to  $1024 \times 1024$ image. Cascaded diffusion models with noise conditioning augmentation [\[29\]](#page-11-5) have been extremely effective in progressively generating high-fidelity images. Furthermore, making the super-resolution models aware of the amount of noise added, via noise level conditioning, significantly improves the sample quality and helps improving the robustness of the super-resolution models to handle artifacts generated by lower resolution models [\[29\]](#page-11-5). Imagen uses noise conditioning augmentation for both the super-resolution models. We find this to be a critical for generating high fidelity images.

Given a conditioning low-resolution image and augmentation level (a.k.a aug\_level) (e.g., strength of Gaussian noise or blur), we corrupt the low-resolution image with the augmentation (corresponding to aug\_level), and condition the diffusion model on aug\_level. During training, aug\_level is chosen randomly, while during inference, we sweep over its different values to find the best sample quality. In our case, we use Gaussian noise as a form of augmentation, and apply variance preserving Gaussian noise augmentation resembling the forward process used in diffusion models (Appendix A). The augmentation level is specified using aug\_level  $\in [0, 1]$ . See Fig. A.32 for reference pseudocode.

### 2.5 Neural network architecture

**Base model:** We adapt the U-Net architecture from [\[40\]](#page-12-9) for our base  $64 \times 64$  text-to-image diffusion model. The network is conditioned on text embeddings via a pooled embedding vector, added to the diffusion timestep embedding similar to the class embedding conditioning method used in [\[16,](#page-10-2) [29\]](#page-11-5). We further condition on the entire sequence of text embeddings by adding cross attention [\[57\]](#page-13-0) over the text embeddings at multiple resolutions. We study various methods of text conditioning in Appendix D.3.1. Furthermore, we found Layer Normalization [\[2\]](#page-9-0) for text embeddings in the attention and pooling layers to help considerably improve performance.

**Super-resolution models:** For  $64 \times 64 \rightarrow 256 \times 256$  super-resolution, we use the U-Net model adapted from [\[40,](#page-12-9) [58\]](#page-13-4). We make several modifications to this U-Net model for improving memory efficiency, inference time and convergence speed (our variant is 2-3x faster in steps/second over the U-Net used in [\[40,](#page-12-9) [58\]](#page-13-4)). We call this variant *Efficient U-Net* (See Appendix B.1 for more details and comparisons). Our  $256 \times 256 \rightarrow 1024 \times 1024$  super-resolution model trains on  $64 \times 64 \rightarrow 256 \times 256$ crops of the  $1024 \times 1024$  image. To facilitate this, we remove the self-attention layers, however we keep the text cross-attention layers which we found to be critical. During inference, the model receives the full 256  $\times$  256 low-resolution images as inputs, and returns upsampled 1024  $\times$  1024 images as outputs. Note that we use text cross attention for both our super-resolution models.

## 3 Evaluating Text-to-Image Models

The COCO [\[36\]](#page-11-7) validation set is the standard benchmark for evaluating text-to-image models for both the supervised [\[82,](#page-14-3) [22\]](#page-11-1) and the zero-shot setting [\[53,](#page-12-0) [41\]](#page-12-3). The key automated performance metrics used are FID [\[26\]](#page-11-8) to measure image fidelity, and CLIP score [\[25,](#page-11-9) [49\]](#page-12-1) to measure image-text alignment. Consistent with previous works, we report zero-shot FID-30K, for which 30K prompts are drawn randomly from the validation set, and the model samples generated on these prompts are compared with reference images from the full validation set. Since guidance weight is an important ingredient to control image quality and text alignment, we report most of our ablation results using trade-off (or *pareto*) curves between CLIP and FID scores across a range of guidance weights.

Both FID and CLIP scores have limitations, for example FID is not fully aligned with perceptual quality  $[42]$ , and CLIP is ineffective at counting  $[49]$ . Due to these limitations, we use human evaluation to assess image quality and caption similarity, with ground truth reference caption-image pairs as a baseline. We use two experimental paradigms:

- 1. To probe image quality, the rater is asked to select between the model generation and reference image using the question: "Which image is more photorealistic (looks more real)?". We report the percentage of times raters choose model generations over reference images (the *preference rate*).
- 2. To probe alignment, human raters are shown an image and a prompt and asked "Does the caption accurately describe the above image?". They must respond with "yes", "somewhat", or "no". These responses are scored as 100, 50, and 0, respectively. These ratings are obtained independently for model samples and reference images, and both are reported.

<span id="page-5-0"></span>Image /page/5/Figure/0 description: Figure 2 displays a grid of nine images, each accompanied by a descriptive caption. The top row features a brown bird and a blue bear, one cat and two dogs sitting on grass, and a sign that reads 'NeurIPS'. The middle row shows a small blue book on a large red book, a blue pizza with red and yellow toppings, and a wine glass balanced on the head of a black dog and a golden dog. The bottom row includes a pear cut into seven pieces arranged in a ring, a confused grizzly bear in a calculus class, and two small vessels on water, one blue and one grey. The figure is titled 'Non-cherry picked Imagen samples for different categories of prompts from DrawBench'.

For both cases we use 200 randomly chosen image-caption pairs from the COCO validation set. Subjects were shown batches of 50 images. We also used interleaved "control" trials, and only include rater data from those who correctly answered at least 80% of the control questions. This netted 73 and 51 ratings per image for image quality and image-text alignment evaluations, respectively.

**DrawBench**: While COCO is a valuable benchmark, it is increasingly clear that it has a limited spectrum of prompts that do not readily provide insight into differences between models (e.g., see Sec. [4.2\)](#page-6-1). Recent work by [\[10\]](#page-10-5) proposed a new evaluation set called PaintSkills to systematically evaluate visual reasoning skills and social biases beyond COCO. With similar motivation, we introduce *DrawBench*, a comprehensive and challenging set of prompts that support the evaluation and comparison of text-to-image models. DrawBench contains 11 categories of prompts, testing different capabilities of models such as the ability to faithfully render different colors, numbers of objects, spatial relations, text in the scene, and unusual interactions between objects. Categories also include complex prompts, including long, intricate textual descriptions, rare words, and also misspelled prompts. We also include sets of prompts collected from DALL-E [\[53\]](#page-12-0), Gary Marcus et al. [\[38\]](#page-11-10) and [Reddit.](https://reddit.com/r/dalle2/) Across these 11 categories, DrawBench comprises 200 prompts in total, striking a good balance between the desire for a large, comprehensive dataset, and small enough that human evaluation remains feasible. (Appendix C provides a more detailed description of DrawBench. Fig. [2](#page-5-0) shows example prompts from DrawBench with Imagen samples.)

We use DrawBench to directly compare different models. To this end, human raters are presented with two sets of images, one from Model A and one from Model B, each of which has 8 samples. Human raters are asked to compare Model A and Model B on sample fidelity and image-text alignment. They respond with one of three choices: Prefer Model A; Indifferent; or Prefer Model B.

# 4 Experiments

Section [4.1](#page-5-1) describes training details, Sections [4.2](#page-6-1) and [4.3](#page-6-2) analyze results on MS-COCO and DrawBench, and Section [4.4](#page-6-0) summarizes our ablation studies and key findings. For all experiments below, the images are fair random samples from Imagen with no post-processing or re-ranking.

<span id="page-5-1"></span>

### 4.1 Training details

Unless specified, we train a 2B parameter model for the  $64 \times 64$  text-to-image synthesis, and 600M and 400M parameter models for  $64 \times 64 \rightarrow 256 \times 256$  and  $256 \times 256 \rightarrow 1024 \times 1024$  for superresolution respectively. We use a batch size of 2048 and 2.5M training steps for all models. We use 256 TPU-v4 chips for our base  $64 \times 64$  model, and 128 TPU-v4 chips for both super-resolution <span id="page-6-3"></span>Table 1: MS-COCO  $256 \times 256$  FID-30K. We use a guidance weight of 1.35 for our  $64 \times 64$  model, and a guidance weight of 8.0 for our super-resolution model.

| Model                    | <b>FID-30K</b> | Zero-shot<br><b>FID-30K</b> |
|--------------------------|----------------|-----------------------------|
| AttnGAN [76]             | 35.49          |                             |
| DM-GAN [83]              | 32.64          |                             |
| DF-GAN [69]              | 21.42          |                             |
| DM-GAN + CL [78]         | 20.79          |                             |
| XMC-GAN [81]             | 9.33           |                             |
| LAFITE [82]              | 8.12           |                             |
| Make-A-Scene [22]        | 7.55           |                             |
| DALL-E [53]              |                | 17.89                       |
| LAFITE [82]              |                | 26.94                       |
| GLIDE [41]               |                | 12.24                       |
| DALL-E 2 [54]            |                | 10.39                       |
| <b>Imagen (Our Work)</b> |                | 7.27                        |

Table 2: COCO  $256 \times 256$  human evaluation comparing model outputs and original images. For the bottom part (no people), we filter out prompts containing one of man, men, woman, women, person, people, child, adult, adults, boy, boys, girl, girls, guy, lady, ladies, someone, toddler, (sport) player, workers, spectators.

| Model                        | <b>Photorealism</b> $\uparrow$ | <b>Alignment</b> $\uparrow$ |
|------------------------------|--------------------------------|-----------------------------|
| <i>Original</i><br>Original  | 50.0%                          | $91.9 \pm 0.42$             |
| Imagen                       | $39.5 \pm 0.75\%$              | $91.4 \pm 0.44$             |
| <i>No people</i><br>Original | 50.0%                          | $92.2 \pm 0.54$             |
| Imagen                       | $43.9 \pm 1.01\%$              | $92.1 \pm 0.55$             |

models. We do not find over-fitting to be an issue, and we believe further training might improve overall performance. We use Adafactor for our base  $64 \times 64$  model, because initial comparisons with Adam suggested similar performance with much smaller memory footprint for Adafactor. For superresolution models, we use Adam as we found Adafactor to hurt model quality in our initial ablations. For classifier-free guidance, we joint-train unconditionally via zeroing out the text embeddings with 10% probability for all three models. We train on a combination of internal datasets, with  $\approx$  460M image-text pairs, and the publicly available Laion dataset [\[61\]](#page-13-7), with  $\approx$  400M image-text pairs. There are limitations in our training data, and we refer the reader to Section [6](#page-8-0) for details. See Appendix F for more implementation details.

<span id="page-6-1"></span>

### 4.2 Results on COCO

We evaluate Imagen on the COCO validation set using FID score, similar to [\[53,](#page-12-0) [41\]](#page-12-3). Table [1](#page-6-3) displays the results. Imagen achieves state of the art *zero-shot* FID on COCO at 7.27, outperforming the concurrent work of DALL-E 2 [\[54\]](#page-12-2) and even models trained on COCO. Table [2](#page-6-3) reports the human evaluation to test image quality and alignment on the COCO validation set. We report results on the original COCO validation set, as well as a filtered version in which all reference data with people have been removed. For photorealism, Imagen achieves 39.2% preference rate indicating high image quality generation. On the set with no people, there is a boost in preference rate of Imagen to 43.6%, indicating Imagen's limited ability to generate photorealistic people. On caption similarity, Imagen's score is on-par with the original reference images, suggesting Imagen's ability to generate images that align well with COCO captions.

<span id="page-6-2"></span>

### 4.3 Results on DrawBench

Using DrawBench, we compare Imagen with DALL-E 2 (the public version) [\[54\]](#page-12-2), GLIDE [\[41\]](#page-12-3), [Latent Diffusion](https://github.com/CompVis/latent-diffusion) [\[57\]](#page-13-0), and [CLIP-guided VQ-GAN](https://github.com/EleutherAI/vqgan-clip) [\[12\]](#page-10-0). Fig. [3](#page-7-0) shows the human evaluation results for pairwise comparison of Imagen with each of the three models. We report the percentage of time raters prefer Model A, Model B, or are indifferent for both image fidelity and image-text alignment. We aggregate the scores across all the categories and raters. We find the human raters to exceedingly prefer Imagen over all others models in both image-text alignment and image fidelity. We refer the reader to Appendix E for a more detailed category wise comparison and qualitative comparison.

<span id="page-6-0"></span>

### 4.4 Analysis of Imagen

For a detailed analysis of Imagen see Appendix D. Key findings are discussed in Fig. [4](#page-7-1) and below.

Scaling text encoder size is extremely effective. We observe that scaling the size of the text encoder leads to consistent improvement in both image-text alignment and image fidelity. Imagen trained with our largest text encoder, T5-XXL (4.6B parameters), yields the best results (Fig. [4a\)](#page-7-1).

<span id="page-7-0"></span>Image /page/7/Figure/0 description: This is a bar chart that compares the performance of Imagen against four other models: DALL-E 2, GLIDE, VQGAN+CLIP, and Latent Diffusion. The chart is divided into four sections, each representing a comparison between Imagen and one of the other models. Within each section, there are two bars, one for 'Alignment' and one for 'Fidelity'. The blue bars represent Imagen, and the green bars represent the other models. The y-axis ranges from 0% to 100%. In the first section, Imagen's Alignment is approximately 60% and DALL-E 2's Alignment is approximately 35%. Imagen's Fidelity is approximately 70% and DALL-E 2's Fidelity is approximately 25%. In the second section, Imagen's Alignment is approximately 60% and GLIDE's Alignment is approximately 55%. Imagen's Fidelity is approximately 70% and GLIDE's Fidelity is approximately 40%. In the third section, Imagen's Alignment is approximately 75% and VQGAN+CLIP's Alignment is approximately 20%. Imagen's Fidelity is approximately 75% and VQGAN+CLIP's Fidelity is approximately 15%. In the fourth section, Imagen's Alignment is approximately 75% and Latent Diffusion's Alignment is approximately 20%. Imagen's Fidelity is approximately 75% and Latent Diffusion's Fidelity is approximately 35%.

Figure 3: Comparison between Imagen and DALL-E 2 [\[54\]](#page-12-2), GLIDE [\[41\]](#page-12-3), VQ-GAN+CLIP [\[12\]](#page-10-0) and Latent Diffusion [\[57\]](#page-13-0) on DrawBench: User preference rates (with 95% confidence intervals) for image-text alignment and image fidelity.

<span id="page-7-1"></span>Image /page/7/Figure/2 description: The image displays three scatter plots, each with the x-axis labeled "CLIP Score" and the y-axis labeled "FID-10K" or "FID@10K". Plot (a) "Impact of encoder size" shows four curves representing "T5-Small" (yellow), "T-Large" (green), "T5-XL" (red), and "T5-XXL" (blue). The x-axis ranges from 0.22 to 0.28, and the y-axis ranges from 10 to 25. Plot (b) "Impact of U-Net size" shows four curves representing "300M" (blue), "500M" (red), "1B" (green), and "2B" (yellow). The x-axis ranges from 0.24 to 0.29, and the y-axis ranges from 10 to 25. Plot (c) "Impact of thresholding" shows two curves representing "static thresholding" (blue) and "dynamic thresholding" (red). The x-axis ranges from 0.26 to 0.29, and the y-axis ranges from 10 to 25.

Figure 4: Summary of some of the critical findings of Imagen with pareto curves sweeping over different guidance values. See Appendix D for more details.

Scaling text encoder size is more important than U-Net size. While scaling the size of the diffusion model U-Net improves sample quality, we found scaling the text encoder size to be significantly more impactful than the U-Net size (Fig. [4b\)](#page-7-1).

Dynamic thresholding is critical. We show that dynamic thresholding results in samples with significantly better photorealism and alignment with text, over static or no thresholding, especially under the presence of large classifier-free guidance weights (Fig. [4c\)](#page-7-1).

Human raters prefer T5-XXL over CLIP on DrawBench. The models trained with T5-XXL and CLIP text encoders perform similarly on the COCO validation set in terms of CLIP and FID scores. However, we find that human raters prefer T5-XXL over CLIP on DrawBench across all 11 categories.

Noise conditioning augmentation is critical. We show that training the super-resolution models with noise conditioning augmentation leads to better CLIP and FID scores. We also show that noise conditioning augmentation enables stronger text conditioning for the super-resolution model, resulting in improved CLIP and FID scores at higher guidance weights. Adding noise to the low-res image during inference along with the use of large guidance weights allows the super-resolution models to generate diverse upsampled outputs while removing artifacts from the low-res image.

Text conditioning method is critical. We observe that conditioning over the sequence of text embeddings with cross attention significantly outperforms simple mean or attention based pooling in both sample fidelity as well as image-text alignment.

Efficient U-Net is critical. Our Efficient U-Net implementation uses less memory, converges faster, and has better sample quality with faster inference.

# 5 Related Work

Diffusion models have seen wide success in image generation [\[28,](#page-11-4) [40,](#page-12-9) [59,](#page-13-3) [16,](#page-10-2) [29,](#page-11-5) [58\]](#page-13-4), outperforming GANs in fidelity and diversity, without training instability and mode collapse issues [\[6,](#page-10-6) [16,](#page-10-2) [29\]](#page-11-5). Autoregressive models [\[37\]](#page-11-11), GANs [\[76,](#page-14-4) [81\]](#page-14-7), VQ-VAE Transformer-based methods [\[53,](#page-12-0) [22\]](#page-11-1), and diffusion models have seen remarkable progress in text-to-image [\[57,](#page-13-0) [41,](#page-12-3) [57\]](#page-13-0), including the concurrent DALL-E 2 [\[54\]](#page-12-2), which uses a diffusion prior on CLIP text latents and cascaded diffusion models

to generate high resolution  $1024 \times 1024$  images; we believe Imagen is much simpler, as Imagen does not need to learn a latent prior, yet achieves better results in both MS-COCO FID and human evaluation on DrawBench. GLIDE [\[41\]](#page-12-3) also uses cascaded diffusion models for text-to-image, but we use large pretrained frozen language models, which we found to be instrumental to both image fidelity and image-text alignment. XMC-GAN [\[81\]](#page-14-7) also uses BERT as a text encoder, but we scale to much larger text encoders and demonstrate the effectiveness thereof. The use of cascaded models is also popular throughout the literature [\[14,](#page-10-7) [39\]](#page-12-11) and has been used with success in diffusion models to generate high resolution images [\[16,](#page-10-2) [29\]](#page-11-5).

# <span id="page-8-0"></span>6 Conclusions, Limitations and Societal Impact

Imagen showcases the effectiveness of frozen large pretrained language models as text encoders for the text-to-image generation using diffusion models. Our observation that scaling the size of these language models have significantly more impact than scaling the U-Net size on overall performance encourages future research directions on exploring even bigger language models as text encoders. Furthermore, through Imagen we re-emphasize the importance of classifier-free guidance, and we introduce dynamic thresholding, which allows usage of much higher guidance weights than seen in previous works. With these novel components, Imagen produces  $1024 \times 1024$  samples with unprecedented photorealism and alignment with text.

Our primary aim with Imagen is to advance research on generative methods, using text-to-image synthesis as a test bed. While end-user applications of generative methods remain largely out of scope, we recognize the potential downstream applications of this research are varied and may impact society in complex ways. On the one hand, generative models have a great potential to complement, extend, and augment human creativity [\[30\]](#page-11-12). Text-to-image generation models, in particular, have the potential to extend image-editing capabilities and lead to the development of new tools for creative practitioners. On the other hand, generative methods can be leveraged for malicious purposes, including harassment and misinformation spread [\[20\]](#page-11-13), and raise many concerns regarding social and cultural exclusion and bias [\[67,](#page-13-8) [62,](#page-13-9) [68\]](#page-13-10). These considerations inform our decision to not to release code or a public demo. In future work we will explore a framework for responsible externalization that balances the value of external auditing with the risks of unrestricted open-access.

Another ethical challenge relates to the large scale data requirements of text-to-image models, which have have led researchers to rely heavily on large, mostly uncurated, web-scraped datasets. While this approach has enabled rapid algorithmic advances in recent years, datasets of this nature have been critiqued and contested along various ethical dimensions. For example, public and academic discourse regarding appropriate use of public data has raised concerns regarding data subject awareness and consent [\[24,](#page-11-14) [18,](#page-10-8) [60,](#page-13-11) [43\]](#page-12-12). Dataset audits have revealed these datasets tend to reflect social stereotypes, oppressive viewpoints, and derogatory, or otherwise harmful, associations to marginalized identity groups [\[44,](#page-12-13) [4\]](#page-9-1). Training text-to-image models on this data risks reproducing these associations and causing significant representational harm that would disproportionately impact individuals and communities already experiencing marginalization, discrimination and exclusion within society. As such, there are a multitude of data challenges that must be addressed before text-to-image models like Imagen can be safely integrated into user-facing applications. While we do not directly address these challenges in this work, an awareness of the limitations of our training data guide our decision not to release Imagen for public use. We strongly caution against the use text-to-image generation methods for any user-facing tools without close care and attention to the contents of the training dataset.

Imagen's training data was drawn from several pre-existing datasets of image and English alt-text pairs. A subset of this data was filtered to removed noise and undesirable content, such as pornographic imagery and toxic language. However, a recent audit of one of our data sources, LAION-400M [\[61\]](#page-13-7), uncovered a wide range of inappropriate content including pornographic imagery, racist slurs, and harmful social stereotypes [\[4\]](#page-9-1). This finding informs our assessment that Imagen is not suitable for public use at this time and also demonstrates the value of rigorous dataset audits and comprehensive dataset documentation (e.g.  $[23, 45]$  $[23, 45]$  $[23, 45]$ ) in informing consequent decisions about the model's appropriate and safe use. Imagen also relies on text encoders trained on uncurated web-scale data, and thus inherits the social biases and limitations of large language models [\[5,](#page-9-2) [3,](#page-9-3) [50\]](#page-12-15).

While we leave an in-depth empirical analysis of social and cultural biases encoded by Imagen to future work, our small scale internal assessments reveal several limitations that guide our decision not to release Imagen at this time. First, all generative models, including Imagen, Imagen, may run into danger of dropping modes of the data distribution, which may further compound the social

consequence of dataset bias. Second, Imagen exhibits serious limitations when generating images depicting people. Our human evaluations found Imagen obtains significantly higher preference rates when evaluated on images that do not portray people, indicating a degradation in image fidelity. Finally, our preliminary assessment also suggests Imagen encodes several social biases and stereotypes, including an overall bias towards generating images of people with lighter skin tones and a tendency for images portraying different professions to align with Western gender stereotypes. Even when we focus generations away from people, our preliminary analysis indicates Imagen encodes a range of social and cultural biases when generating images of activities, events, and objects.

While there has been extensive work auditing image-to-text and image labeling models for forms of social bias (e.g.  $[8, 9, 68]$  $[8, 9, 68]$  $[8, 9, 68]$  $[8, 9, 68]$  $[8, 9, 68]$ ), there has been comparatively less work on social bias evaluation methods for text-to-image models, with the recent exception of [\[10\]](#page-10-5). We believe this is a critical avenue for future research and we intend to explore benchmark evaluations for social and cultural bias in future work—for example, exploring whether it is possible to generalize the normalized pointwise mutual information metric [\[1\]](#page-9-4) to the measurement of biases in image generation models. There is also a great need to develop a conceptual vocabulary around potential harms of text-to-image models that could guide the development of evaluation metrics and inform responsible model release. We aim to address these challenges in future work.

# 7 Acknowledgements

We give thanks to Ben Poole for reviewing our manuscript, early discussions, and providing many helpful comments and suggestions throughout the project. Special thanks to Kathy Meier-Hellstern, Austin Tarango, and Sarah Laszlo for helping us incorporate important responsible AI practices around this project. We appreciate valuable feedback and support from Elizabeth Adkison, Zoubin Ghahramani, Jeff Dean, Yonghui Wu, and Eli Collins. We are grateful to Tom Small for designing the Imagen watermark. We thank Jason Baldridge, Han Zhang, and Kevin Murphy for initial discussions and feedback. We acknowledge hard work and support from Fred Alcober, Hibaq Ali, Marian Croak, Aaron Donsbach, Tulsee Doshi, Toju Duke, Douglas Eck, Jason Freidenfelds, Brian Gabriel, Molly FitzMorris, David Ha, Philip Parham, Laura Pearce, Evan Rapoport, Lauren Skelly, Johnny Soraker, Negar Rostamzadeh, Vijay Vasudevan, Tris Warkentin, Jeremy Weinstein, and Hugh Williams for giving us advice along the project and assisting us with the publication process. We thank Victor Gomes and Erica Moreira for their consistent and critical help with TPU resource allocation. We also give thanks to Shekoofeh Azizi, Harris Chan, Chris A. Lee, and Nick Ma for volunteering a considerable amount of their time for testing out DrawBench. We thank Aditya Ramesh, Prafulla Dhariwal, and Alex Nichol for allowing us to use DALL-E 2 samples and providing us with GLIDE samples. We are thankful to Matthew Johnson and Roy Frostig for starting the JAX project and to the whole JAX team for building such a fantastic system for high-performance machine learning research. Special thanks to Durk Kingma, Jascha Sohl-Dickstein, Lucas Theis and the Toronto Brain team for helpful discussions and spending time Imagening!

# References

- <span id="page-9-4"></span>[1] Osman Aka, Ken Burke, Alex Bauerle, Christina Greer, and Margaret Mitchell. Measuring Model Biases in the Absence of Ground Truth. In *Proceedings of the 2021 AAAI/ACM Conference on AI, Ethics, and Society*, 2021.
- <span id="page-9-0"></span>[2] Jimmy Lei Ba, Jamie Ryan Kiros, and Geoffrey E Hinton. Layer normalization. *arXiv preprint arXiv:1607.06450*, 2016.
- <span id="page-9-3"></span>[3] Emily M. Bender, Timnit Gebru, Angelina McMillan-Major, and Shmargaret Shmitchell. On the dangers of stochastic parrots: Can language models be too big? . In *Proceedings of FAccT 2021*, 2021.
- <span id="page-9-1"></span>[4] Abeba Birhane, Vinay Uday Prabhu, and Emmanuel Kahembwe. Multimodal datasets: misogyny, pornography, and malignant stereotypes. In *arXiv:2110.01963*, 2021.
- <span id="page-9-2"></span>[5] Shikha Bordia and Samuel R. Bowman. Identifying and Reducing Gender Bias in Word-Level Language Models. In *NAACL*, 2017.

- <span id="page-10-6"></span>[6] Andrew Brock, Jeff Donahue, and Karen Simonyan. Large scale gan training for high fidelity natural image synthesis. *arXiv preprint arXiv:1809.11096*, 2018.
- <span id="page-10-3"></span>[7] Tom B. Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, Sandhini Agarwal, Ariel Herbert-Voss, Gretchen Krueger, Tom Henighan, Rewon Child, Aditya Ramesh, Daniel M. Ziegler, Jeffrey Wu, Clemens Winter, Christopher Hesse, Mark Chen, Eric Sigler, Mateusz Litwin, Scott Gray, Benjamin Chess, Jack Clark, Christopher Berner, Sam McCandlish, Alec Radford, Ilya Sutskever, and Dario Amodei. Language Models are Few-Shot Learners. In *NeurIPS*, 2020.
- <span id="page-10-9"></span>[8] Joy Buolamwini and Timnit Gebru. Gender shades: Intersectional accuracy disparities in commercial gender classification. In *Conference on Fairness, Accountability and Transparency, FAT 2018, 23-24 February 2018, New York, NY, USA*, Proceedings of Machine Learning Research. PMLR, 2018.
- <span id="page-10-10"></span>[9] Kaylee Burns, Lisa Hendricks, Trevor Darrell, and Anna Rohrbach. Women also snowboard: Overcoming bias in captioning models. In *European Conference on Computer Vision (ECCV)*, 2018.
- <span id="page-10-5"></span>[10] Jaemin Cho, Abhay Zala, and Mohit Bansal. Dall-eval: Probing the reasoning skills and social biases of text-to-image generative transformers. *arxiv:2202.04053*, 2022.
- <span id="page-10-4"></span>[11] Aakanksha Chowdhery, Sharan Narang, Jacob Devlin, Maarten Bosma, Gaurav Mishra, Adam Roberts, Paul Barham, Hyung Won Chung, Charles Sutton, Sebastian Gehrmann, Parker Schuh, Kensen Shi, Sasha Tsvyashchenko, Joshua Maynez, Abhishek Rao, Parker Barnes, Yi Tay, Noam Shazeer, Vinodkumar Prabhakaran, Emily Reif, Nan Du, Ben Hutchinson, Reiner Pope, James Bradbury, Jacob Austin, Michael Isard, Guy Gur-Ari, Pengcheng Yin, Toju Duke, Anselm Levskaya, Sanjay Ghemawat, Sunipa Dev, Henryk Michalewski, Xavier Garcia, Vedant Misra, Kevin Robinson, Liam Fedus, Denny Zhou, Daphne Ippolito, David Luan, Hyeontaek Lim, Barret Zoph, Alexander Spiridonov, Ryan Sepassi, David Dohan, Shivani Agrawal, Mark Omernick, Andrew M. Dai, Thanumalayan Sankaranarayana Pillai, Marie Pellat, Aitor Lewkowycz, Erica Moreira, Rewon Child, Oleksandr Polozov, Katherine Lee, Zongwei Zhou, Xuezhi Wang, Brennan Saeta, Mark Diaz, Orhan Firat, Michele Catasta, Jason Wei, Kathy Meier-Hellstern, Douglas Eck, Jeff Dean, Slav Petrov, and Noah Fiedel. PaLM: Scaling Language Modeling with Pathways. In *arXiv:2001.08361*, 2022.
- <span id="page-10-0"></span>[12] Katherine Crowson, Stella Biderman, Daniel Kornis, Dashiell Stander, Eric Hallahan, Louis Castricato, and Edward Raff. Vqgan-clip: Open domain image generation and editing with natural language guidance. *arXiv preprint arXiv:2204.08583*, 2022.
- [13] Valentin De Bortoli, James Thornton, Jeremy Heng, and Arnaud Doucet. Diffusion schrödinger bridge with applications to score-based generative modeling. *Advances in Neural Information Processing Systems*, 34, 2021.
- <span id="page-10-7"></span>[14] Emily Denton, Soumith Chintala, Arthur Szlam, and Rob Fergus. Deep Generative Image Models using a Laplacian Pyramid of Adversarial Networks. In *NIPS*, 2015.
- <span id="page-10-1"></span>[15] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding. In *NAACL*, 2019.
- <span id="page-10-2"></span>[16] Prafulla Dhariwal and Alex Nichol. Diffusion models beat gans on image synthesis. In *NeurIPS*, 2022.
- [17] Ming Ding, Zhuoyi Yang, Wenyi Hong, Wendi Zheng, Chang Zhou, Da Yin, Junyang Lin, Xu Zou, Zhou Shao, Hongxia Yang, et al. Cogview: Mastering text-to-image generation via transformers. *Advances in Neural Information Processing Systems*, 34, 2021.
- <span id="page-10-8"></span>[18] Dulhanty, Chris. Issues in Computer Vision Data Collection: Bias, Consent, and Label Taxonomy. In *UWSpace*, 2020.
- [19] Patrick Esser, Robin Rombach, and Bjorn Ommer. Taming transformers for high-resolution image synthesis. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 12873–12883, 2021.

- <span id="page-11-13"></span>[20] Mary Anne Franks and Ari Ezra Waldman. Sex, lies and videotape: deep fakes and free speech delusions. *Maryland Law Review*, 78(4):892–898, 2019.
- <span id="page-11-2"></span>[21] Tsu-Jui Fu, Xin Eric Wang, and William Yang Wang. Language-Driven Image Style Transfer. *arXiv preprint arXiv:2106.00178*, 2021.
- <span id="page-11-1"></span>[22] Oran Gafni, Adam Polyak, Oron Ashual, Shelly Sheynin, Devi Parikh, and Yaniv Taigman. Make-a-scene: Scene-based text-to-image generation with human priors. *arXiv preprint arXiv:2203.13131*, 2022.
- <span id="page-11-15"></span>[23] Timnit Gebru, Jamie Morgenstern, Briana Vecchione, Jennifer Wortman Vaughan, Hanna Wallach, Hal Daumé III, and Kate Crawford. Datasheets for Datasets. *arXiv:1803.09010 [cs]*, March 2020.
- <span id="page-11-14"></span>[24] Adam Harvey and Jules LaPlace. MegaPixels: Origins and endpoints of biometric datasets "In the Wild". https://megapixels.cc, 2019.
- <span id="page-11-9"></span>[25] Jack Hessel, Ari Holtzman, Maxwell Forbes, Ronan Le Bras, and Yejin Choi. Clipscore: A reference-free evaluation metric for image captioning. *arXiv preprint arXiv:2104.08718*, 2021.
- <span id="page-11-8"></span>[26] Martin Heusel, Hubert Ramsauer, Thomas Unterthiner, Bernhard Nessler, and Sepp Hochreiter. Gans trained by a two time-scale update rule converge to a local nash equilibrium. *arXiv preprint arXiv:1706.08500*, 2017.
- <span id="page-11-6"></span>[27] Jonathan Ho and Tim Salimans. Classifier-free diffusion guidance. In *NeurIPS 2021 Workshop on Deep Generative Models and Downstream Applications*, 2021.
- <span id="page-11-4"></span>[28] Jonathan Ho, Ajay Jain, and Pieter Abbeel. Denoising Diffusion Probabilistic Models. *NeurIPS*, 2020.
- <span id="page-11-5"></span>[29] Jonathan Ho, Chitwan Saharia, William Chan, David J Fleet, Mohammad Norouzi, and Tim Salimans. Cascaded diffusion models for high fidelity image generation. *JMLR*, 2022.
- <span id="page-11-12"></span>[30] Rowan T. Hughes, Liming Zhu, and Tomasz Bednarz. Generative adversarial networks-enabled human-artificial intelligence collaborative applications for creative and design industries: A systematic review of current approaches and trends. *Frontiers in artificial intelligence*, 4, 2021.
- <span id="page-11-0"></span>[31] Chao Jia, Yinfei Yang, Ye Xia, Yi-Ting Chen, Zarana Parekh, Hieu Pham, Quoc Le, Yun-Hsuan Sung, Zhen Li, and Tom Duerig. Scaling up visual and vision-language representation learning with noisy text supervision. In *International Conference on Machine Learning*, pages 4904–4916. PMLR, 2021.
- [32] Zahra Kadkhodaie and Eero P Simoncelli. Solving linear inverse problems using the prior implicit in a denoiser. *arXiv preprint arXiv:2007.13640*, 2020.
- [33] Zahra Kadkhodaie and Eero P Simoncelli. Stochastic solutions for linear inverse problems using the prior implicit in a denoiser. *Advances in Neural Information Processing Systems*, 34, 2021.
- <span id="page-11-3"></span>[34] Gwanghyun Kim and Jong Chul Ye. Diffusionclip: Text-guided image manipulation using diffusion models. *arXiv preprint arXiv:2110.02711*, 2021.
- [35] Diederik P Kingma, Tim Salimans, Ben Poole, and Jonathan Ho. Variational diffusion models. *arXiv preprint arXiv:2107.00630*, 2021.
- <span id="page-11-7"></span>[36] Tsung-Yi Lin, Michael Maire, Serge Belongie, Lubomir Bourdev, Ross Girshick, James Hays, Pietro Perona, Deva Ramanan, Lawrence Zitnick, and Piotr Dollár. Microsoft COCO: Common Objects in Context. In *ECCV*, 2014.
- <span id="page-11-11"></span>[37] Elman Mansimov, Emilio Parisotto, Jimmy Lei Ba, and Ruslan Salakhutdinov. Generating Images from Captions with Attention. In *ICLR*, 2016.
- <span id="page-11-10"></span>[38] Gary Marcus, Ernest Davis, and Scott Aaronson. A very preliminary analysis of DALL-E 2. In *arXiv:2204.13807*, 2022.

- <span id="page-12-11"></span>[39] Jacob Menick and Nal Kalchbrenner. Generating High Fidelity Images with Subscale Pixel Networks and Multidimensional Upscaling. In *ICLR*, 2019.
- <span id="page-12-9"></span>[40] Alex Nichol and Prafulla Dhariwal. Improved denoising diffusion probabilistic models. *arXiv preprint arXiv:2102.09672*, 2021.
- <span id="page-12-3"></span>[41] Alex Nichol, Prafulla Dhariwal, Aditya Ramesh, Pranav Shyam, Bob McGrew Pamela Mishkin, Ilya Sutskever, and Mark Chen. GLIDE: Towards Photorealistic Image Generation and Editing with Text-Guided Diffusion Models. In *arXiv:2112.10741*, 2021.
- <span id="page-12-10"></span>[42] Gaurav Parmar, Richard Zhang, and Jun-Yan Zhu. On Aliased Resizing and Surprising Subtleties in GAN Evaluation. In *CVPR*, 2022.
- <span id="page-12-12"></span>[43] Amandalynne Paullada, Inioluwa Deborah Raji, Emily M. Bender, Emily Denton, and Alex Hanna. Data and its (dis)contents: A survey of dataset development and use in machine learning research. *Patterns*, 2(11):100336, 2021.
- <span id="page-12-13"></span>[44] Vinay Uday Prabhu and Abeba Birhane. Large image datasets: A pyrrhic win for computer vision? *arXiv:2006.16923*, 2020.
- <span id="page-12-14"></span>[45] Mahima Pushkarna, Andrew Zaldivar, and Oddur Kjartansson. Data cards: Purposeful and transparent dataset documentation for responsible ai. In *Proceedings of the 2021 ACM Conference on Fairness, Accountability, and Transparency*, 2022.
- <span id="page-12-8"></span>[46] Alec Radford, Rafal Jozefowicz, and Ilya Sutskever. Learning to Generate Reviews and Discovering Sentiment. In *arXiv:1704.01444*, 2017.
- <span id="page-12-5"></span>[47] Alec Radford, Karthik Narasimhan, Tim Salimans, and Ilya Sutskever. Improving Language Understanding by Generative Pre-Training. In *preprint*, 2018.
- <span id="page-12-6"></span>[48] Alec Radford, Jeffrey Wu, Rewon Child, David Luan, Dario Amodei, and Ilya Sutskever. Language Models are Unsupervised Multitask Learners. In *preprint*, 2019.
- <span id="page-12-1"></span>[49] Alec Radford, Jong Wook Kim, Chris Hallacy, Aditya Ramesh, Gabriel Goh, Sandhini Agarwal, Girish Sastry, Amanda Askell, Pamela Mishkin, Jack Clark, Gretchen Krueger, and Ilya Sutskever. Learning Transferable Visual Models From Natural Language Supervision. In *ICML*, 2021.
- <span id="page-12-15"></span>[50] Jack Rae, Sebastian Borgeaud, Trevor Cai, Katie Millican, Jordan Hoffmann, Francis Song, John Aslanides, Sarah Henderson, Roman Ring, Susannah Young, Eliza Rutherford, Tom Hennigan, Jacob Menick, Albin Cassirer, Richard Powell, George Driessche, Lisa Hendricks, Maribeth Rauh, Po-Sen Huang, and Geoffrey Irving. Scaling language models: Methods, analysis & insights from training gopher. *arXiv:2112.11446*, 2021.
- <span id="page-12-7"></span>[51] Colin Raffel, Minh-Thang Luong, Peter J. Liu, Ron J. Weiss, and Douglas Eck. Online and Linear-Time Attention by Enforcing Monotonic Alignments. In *ICML*, 2017.
- <span id="page-12-4"></span>[52] Colin Raffel, Noam Shazeer, Adam Roberts, Katherine Lee, Sharan Narang, Michael Matena, Yanqi Zhou, Wei Li, and Peter J. Liu. Exploring the Limits of Transfer Learning with a Unified Text-to-Text Transformer. *JMLR*, 21(140), 2020.
- <span id="page-12-0"></span>[53] Aditya Ramesh, Mikhail Pavlov, Gabriel Goh, Scott Gray, Chelsea Voss, Alec Radford, Mark Chen, and Ilya Sutskever. Zero-Shot Text-to-Image Generation. In *ICML*, 2021.
- <span id="page-12-2"></span>[54] Aditya Ramesh, Prafulla Dhariwal, Alex Nichol, Casey Chu, and Mark Chen. Hierarchical Text-Conditional Image Generation with CLIP Latents. In *arXiv*, 2022.
- [55] Ali Razavi, Aaron van den Oord, and Oriol Vinyals. Generating diverse high-fidelity images with vq-vae-2. *arXiv preprint arXiv:1906.00446*, 2019.
- [56] Scott Reed, Zeynep Akata, Xinchen Yan, Lajanugen Logeswaran, Bernt Schiele, and Honglak Lee. Generative adversarial text to image synthesis. In *International conference on machine learning*, pages 1060–1069. PMLR, 2016.

- <span id="page-13-0"></span>[57] Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Björn Ommer. High-Resolution Image Synthesis with Latent Diffusion Models. In *CVPR*, 2022.
- <span id="page-13-4"></span>[58] Chitwan Saharia, William Chan, Huiwen Chang, Chris A. Lee, Jonathan Ho, Tim Salimans, David J. Fleet, and Mohammad Norouzi. Palette: Image-to-Image Diffusion Models. In *arXiv:2111.05826*, 2021.
- <span id="page-13-3"></span>[59] Chitwan Saharia, Jonathan Ho, William Chan, Tim Salimans, David J Fleet, and Mohammad Norouzi. Image super-resolution via iterative refinement. *arXiv preprint arXiv:2104.07636*, 2021.
- <span id="page-13-11"></span>[60] Morgan Klaus Scheuerman, Emily L. Denton, and A. Hanna. Do datasets have politics? disciplinary values in computer vision dataset development. *Proceedings of the ACM on Human-Computer Interaction*, 5:1 – 37, 2021.
- <span id="page-13-7"></span>[61] Christoph Schuhmann, Richard Vencu, Romain Beaumont, Robert Kaczmarczyk, Clayton Mullis, Aarush Katta, Theo Coombes, Jenia Jitsev, and Aran Komatsuzaki. Laion-400m: Open dataset of clip-filtered 400 million image-text pairs. *arXiv preprint arXiv:2111.02114*, 2021.
- <span id="page-13-9"></span>[62] Lucas Sequeira, Bruno Moreschi, Amanda Jurno, and Vinicius Arruda dos Santos. Which faces can AI generate? Normativity, whiteness and lack of diversity in This Person Does Not Exist. In *CVPR Workshop Beyond Fairness: Towards a Just, Equitable, and Accountable Computer Vision*, 2021.
- <span id="page-13-1"></span>[63] Jascha Sohl-Dickstein, Eric Weiss, Niru Maheswaranathan, and Surya Ganguli. Deep unsupervised learning using nonequilibrium thermodynamics. In *International Conference on Machine Learning*, pages 2256–2265. PMLR, 2015.
- <span id="page-13-5"></span>[64] Jiaming Song, Chenlin Meng, and Stefano Ermon. Denoising diffusion implicit models. *arXiv preprint arXiv:2010.02502*, 2020.
- <span id="page-13-2"></span>[65] Yang Song and Stefano Ermon. Generative Modeling by Estimating Gradients of the Data Distribution. *NeurIPS*, 2019.
- [66] Yang Song, Jascha Sohl-Dickstein, Diederik P Kingma, Abhishek Kumar, Stefano Ermon, and Ben Poole. Score-based generative modeling through stochastic differential equations. In *ICLR*, 2021.
- <span id="page-13-8"></span>[67] Ramya Srinivasan and Kanji Uchino. Biases in generative art: A causal look from the lens of art history. In *Proceedings of the 2021 ACM Conference on Fairness, Accountability, and Transparency*, page 41–51, 2021.
- <span id="page-13-10"></span>[68] Ryan Steed and Aylin Caliskan. Image representations learned with unsupervised pre-training contain human-like biases. In *Proceedings of the 2021 ACM Conference on Fairness, Accountability, and Transparency*, FAccT '21, page 701–713. Association for Computing Machinery, 2021.
- <span id="page-13-6"></span>[69] Ming Tao, Hao Tang, Songsong Wu, Nicu Sebe, Xiao-Yuan Jing, Fei Wu, and Bingkun Bao. Df-gan: Deep fusion generative adversarial networks for text-to-image synthesis. *arXiv preprint arXiv:2008.05865*, 2020.
- [70] Belinda Tzen and Maxim Raginsky. Neural Stochastic Differential Equations: Deep Latent Gaussian Models in the Diffusion Limit. In *arXiv:1905.09883*, 2019.
- [71] Aaron Van Den Oord, Oriol Vinyals, et al. Neural discrete representation learning. *Advances in neural information processing systems*, 30, 2017.
- [72] Pascal Vincent. A connection between score matching and denoising autoencoders. *Neural Computation*, 23(7):1661–1674, 2011.
- [73] Ting-Chun Wang, Ming-Yu Liu, Jun-Yan Zhu, Andrew Tao, Jan Kautz, and Bryan Catanzaro. High-resolution image synthesis and semantic manipulation with conditional gans. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 8798–8807, 2018.

- <span id="page-14-0"></span>[74] Jason Weston, Samy Bengio, and Nicolas Usunier. Wsabie: Scaling up to large vocabulary image annotation. In *Twenty-Second International Joint Conference on Artificial Intelligence*, 2011.
- <span id="page-14-2"></span>[75] Jay Whang, Mauricio Delbracio, Hossein Talebi, Chitwan Saharia, Alexandros G Dimakis, and Peyman Milanfar. Deblurring via stochastic refinement. *arXiv preprint arXiv:2112.02475*, 2021.
- <span id="page-14-4"></span>[76] Tao Xu, Pengchuan Zhang, Qiuyuan Huang, Han Zhang, Zhe Gan, Xiaolei Huang, and Xiaodong He. AttnGAN: Fine-Grained Text to Image Generation with Attentional Generative Adversarial Networks. In *CVPR*, 2018.
- [77] Tao Xu, Pengchuan Zhang, Qiuyuan Huang, Han Zhang, Zhe Gan, Xiaolei Huang, and Xiaodong He. Attngan: Fine-grained text to image generation with attentional generative adversarial networks. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 1316–1324, 2018.
- <span id="page-14-6"></span>[78] Hui Ye, Xiulong Yang, Martin Takac, Rajshekhar Sunderraman, and Shihao Ji. Improving text-to-image synthesis using contrastive learning. *arXiv preprint arXiv:2107.02423*, 2021.
- [79] Jiahui Yu, Xin Li, Jing Yu Koh, Han Zhang, Ruoming Pang, James Qin, Alexander Ku, Yuanzhong Xu, Jason Baldridge, and Yonghui Wu. Vector-quantized image modeling with improved vqgan. *arXiv preprint arXiv:2110.04627*, 2021.
- <span id="page-14-1"></span>[80] Jiahui Yu, Zirui Wang, Vijay Vasudevan, Legg Yeung, Mojtaba Seyedhosseini, and Yonghui Wu. Coca: Contrastive captioners are image-text foundation models. *arXiv preprint arXiv:2205.01917*, 2022.
- <span id="page-14-7"></span>[81] Han Zhang, Jing Yu Koh, Jason Baldridge, Honglak Lee, and Yinfei Yang. Cross-Modal Contrastive Learning for Text-to-Image Generation. In *CVPR*, 2021.
- <span id="page-14-3"></span>[82] Yufan Zhou, Ruiyi Zhang, Changyou Chen, Chunyuan Li, Chris Tensmeyer, Tong Yu, Jiuxiang Gu, Jinhui Xu, and Tong Sun. Lafite: Towards language-free training for text-to-image generation. *arXiv preprint arXiv:2111.13792*, 2021.
- <span id="page-14-5"></span>[83] Minfeng Zhu, Pingbo Pan, Wei Chen, and Yi Yang. Dm-gan: Dynamic memory generative adversarial networks for text-to-image synthesis. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 5802–5810, 2019.
- [84] Yukun Zhu, Ryan Kiros, Rich Zemel, Ruslan Salakhutdinov, Raquel Urtasun, Antonio Torralba, and Sanja Fidler. Aligning books and movies: Towards story-like visual explanations by watching movies and reading books. In *ICCV*, 2015.
# Knowledge Hierarchy Guided Biological-Medical Dataset Distillation for Domain LLM Training

Xu<PERSON><PERSON><sup>1,2†</sup>, <PERSON><PERSON><PERSON><sup>1†</sup>, <PERSON><PERSON><sup>1†</sup>, <PERSON><PERSON><PERSON><sup>1,2,3,4</sup>, and  $\text{<PERSON><PERSON>}^{\mathbb{I}}(\boxtimes)$ 

<sup>1</sup> Computer Network Information Center, Chinese Academy of Sciences, Beijing, China {xxcai,crwang,qqlong,zyc,shaow}@cnic.cn

<sup>2</sup> University of Chinese Academy of Sciences, Beijing, China

<sup>3</sup> Hangzhou Institute for Advanced Study, University of Chinese Academy of Sciences

<sup>4</sup> University of Science and Technology of China, Hefei, China

Abstract. The rapid advancement of large language models (LLMs) in biological-medical applications has highlighted a gap between their potential and the limited scale and often low quality of available opensource annotated textual datasets. In addition, the inherent complexity of the biomedical knowledge hierarchy significantly hampers efforts to bridge this gap. Can LLMs themselves play a pivotal role in overcoming this limitation? Motivated by this question, we investigate this challenge in the present study. We propose a framework that automates the distillation of high-quality textual training data from the extensive scientific literature. Our approach self-evaluates and generates questions that are more closely aligned with the biomedical domain, guided by the biomedical knowledge hierarchy through medical subject headings (MeSH). This comprehensive framework establishes an automated workflow, thereby eliminating the need for manual intervention. Furthermore, we conducted comprehensive experiments to evaluate the impact of our framework-generated data on downstream language models of varying sizes. Our approach substantially improves question-answering tasks compared to pre-trained models from the life sciences domain and powerful close-source models represented by GPT-4. Notably, the generated AI-Ready dataset enabled the Llama3-70B base model to outperform GPT-4 using MedPrompt with multiple times the number of parameters. Detailed case studies and ablation experiments underscore the significance of each component within our framework<sup>[5](#page-0-0)</sup>.

## 1 Introduction

The rise of LLMs has revolutionized bioinformatics, driving the adoption of automated applications across areas  $[38,7,21,9,15,31]$  $[38,7,21,9,15,31]$  $[38,7,21,9,15,31]$  $[38,7,21,9,15,31]$  $[38,7,21,9,15,31]$  $[38,7,21,9,15,31]$  $[38,7,21,9,15,31]$  $[38,7,21,9,15,31]$ , with their effectiveness increasingly validated in real-world Question-Answer (QA) [\[23](#page-14-3)[,41](#page-15-0)[,14\]](#page-13-3). Nevertheless, the inherent complexity of biomedical tasks [\[49\]](#page-15-1) means that general LLMs often fail to give correct answer unless they are carefully adapted and fine-tuned [\[37\]](#page-14-4).

 $\frac{1}{\sqrt{2}}$  These authors contributed equally to this work.

<span id="page-0-0"></span><sup>5</sup> Our code is shared on Dropbox: [link.](https://www.dropbox.com/scl/fo/0127mpvenexo51n0rpp1m/AETMLlsul52jmDy2JhgEnoM?rlkey=1g6m3j4upiydanya9137fkpqi&st=6um1v8sg&dl=0)

<span id="page-1-0"></span>Image /page/1/Figure/1 description: The image illustrates two approaches to training language models, highlighting limitations of existing methods and proposing a new solution called KAILIN. The top section, labeled 'Existing Limitation and Challenge,' shows two pathways. The first pathway involves training on small, limited-size open-source datasets, leading to a base model, an LLM, and an application, which is associated with a thumbs-down icon. The second pathway involves training on low-quality, raw documents, also leading to a base model, an LLM, and an application, again associated with a thumbs-down icon. The bottom section, labeled 'KAILIN,' presents a different approach. It starts with a limited-size open-source train-set, which undergoes 'dataset distillation' to produce a large and high-quality 'ideal dataset.' This ideal dataset is then used to train a base model, an LLM, and finally results in a 'Better Application,' indicated by a thumbs-up icon.

Fig. 1: Analyzing limitation and challenge of existing pipeline.

Additionally, the limited availability of substantial biomedical text data impedes the fine-tuning of domain-specific LLMs. While biomedical research papers indeed serve as rich sources of quality and dependable corpora, they are characterized by complex terminologies and detailed conceptual frameworks that demand considerable human effort for understanding and processing. [\[42,](#page-15-2)[4\]](#page-12-0). Figure [1](#page-1-0) gives an overview of this data limitation challenge. Those observations lead to an essential question: How to automatically distill high-quality, large-scale datasets from extensive research papers, thus support LLM training?

To address the automated corpora distillation challenge, existing frameworks can be categorized primarily into three approaches: (1) Predefined rules-based approaches [\[19](#page-13-4)[,3\]](#page-12-1) undertake extensive data cleaning by filtering and standardizing large-scale bioinformatics datasets. While those approaches reduce noise and improve data quality, they incur significant operational costs and limit scalability due to the human labor. (2) Knowledge graph-based approaches [\[44,](#page-15-3)[52\]](#page-15-4) leverage biomedical text data to create comprehensive knowledge structures, but the reliance on curated databases results in inefficiencies and scalability challenges. (3) Synthesis approaches [\[18](#page-13-5)[,35,](#page-14-5)[5\]](#page-13-6) present a promising automated solution to generate question-answer pairs and process large volumes of documents by using LLMs. Nonetheless, these studies neglect the integration of cross-disciplinary collaboration [\[48,](#page-15-5)[22\]](#page-14-6), resulting in a lack of diversity and reliability.

Motivated by those limitations, we propose Knowledge hierArchy guIded biologicaL-medIcal dataset distillatioN (KAILIN), an automated framework that integrates knowledge hierarchy and utilizes multiple LLMs as experts for domain QA training corpora extraction. The core idea of KAILIN is to introduce a well-established knowledge hierarchy [\[47\]](#page-15-6) (i.e., Medical Subject Headings (MeSH) [\[25\]](#page-14-7)) to assess the alignment of the generated 'Question-Answer-Context' pair to the domain understanding. This framework begins with fine-tuning two LLMs to generate questions from annotated yet scarce open-source datasets. After that, the framework retrieves the context from 23 million collected research articles that are most related to the generated questions. The better question is determined by evaluating the retrieved contexts and selecting the one with the superior alignment score to the knowledge hierarchy. By that, the automated

pipeline of generating preference data is present. This dataset is designed to train a language model to craft improved questions from unannotated research articles that align more effectively with the existing knowledge structure. Using the improved question, we can retrieve the related context, generate answers, and finally form the AI-Ready dataset.

In summary, the key contributions of this work can be summarized as:

- Biomedical Dataset Distillation Workflow: We present a comprehensive, highly automated workflow for distilling biomedical corpora from large-scale research articles. This framework enables the creation of expansive, domainspecific training datasets without the need for manual annotation, significantly reducing the cost and time involved in dataset preparation.
- Framework and Methodology: We proposed the KAILIN framework which incorporates a MeSH-based knowledge hierarchy similarity evaluation method to integrate and evaluate the quality of the distilled biomedical corpora, obviating the need for human intervention in dataset curation..
- Empirical Validation and Insights: We conducted extensive experiments to validate the effectiveness of our framework and the resulting datasets. Through ablation studies and case analyses, we explored the impact of each technical component. Additionally, we investigated the scaling law of dataset distillation across various settings and model hyperparameter selections.

## 2 Methodology

In this section, we introduce the KAILIN framework, which aims to enhance the open-source dataset through the dataset distillation process and validate the effectiveness of downstream tasks.

Image /page/2/Figure/8 description: The image displays a flowchart illustrating the KAILIN framework. The framework is divided into six steps: Step I, Fine-tuning Question Generator; Step II, Infer and Retrieve; Step III, Preference Dataset Generation; Step IV, Direct Preference Optimization; Step V, Retrieve and Generate Answer; and Step VI, Downstream Task. The process begins with an Open-Source train-set and a Base Model, which are used to fine-tune LLM 1 and LLM 2. These LLMs then infer and retrieve questions and contexts, which are stored in a Vector Database. Step III involves Hierarchy Similarity Compare to generate a Preference Dataset. Step IV uses the Preference Dataset to optimize LLM 1, creating a Hierarchical Domain-Optimized Question Generator. Step V retrieves information and generates an Answer using a Refined LLM. Finally, Step VI utilizes an Ideal Dataset and a Base Model for a Downstream Application. The overall process is described as training, retrieving, and constructing a dataset, followed by inference.

Fig. 2: The overview of KAILIN framework.

Fine-Tuning Question Generator: To enhance the performance of generalpurpose base models in biomedical question generation, we employed biomedical open-source BioASQ dataset [\[40\]](#page-15-7) as training set  $\mathcal{T}$ , with the prompt as Figure [9.](#page-9-0) Using this training set  $\mathcal{T}$ , we trained two distinct question generators  $\theta^1$  and  $\theta^2$ , with LLaMA-2-7B and BioMistral as base models  $\theta$  respectively.

Retrieval Process: We collected 23 million abstracts from PubMed<sup>[6](#page-3-0)</sup>, which serves as the raw dataset  $R$  for dataset distillation and is also constructed as a vector database V for retrieval purposes. Given a document  $d_i \in \mathcal{R}$ , and two question generation models  $\theta^1$  and  $\theta^2$  that use  $d_i$  as input to infer questions  $q_i^1$  and  $q_i^2$ . We then used these questions  $q_i^1$  and  $q_i^2$  respectively as input to retrieve the top- $k$  most similar documents from the vector database  $V$ . These retrieved raw documents, serving as the contexts  $c_i^1$  and  $c_i^2$  associated with  $q_i^1$  and  $q_i^2$  respectively, will be used for the subsequent knowledge hierarchy similarity evaluation.

Knowledge Hierarchy Similarity Evaluation: Using the retrieved contexts  $c_i^1$  and  $c_i^2$  associated with the generated questions  $q_i^1$  and  $q_i^2$ , we conducted a knowledge hierarchy similarity evaluation between each context and the original document  $d_i$  that used for generating the questions. This allows us to select the question that better aligns with the knowledge hierarchy of the biomedical field. For similarity evaluation, we introduced Medical Subject Headings (MeSH), denoted as M. MeSH is a hierarchical classification system centered on an well-organized vocabulary that systematically classifies and organizes biomedical knowledge through structured subject terms. We present ≺, a partial order illustrating the Belong-to relationship, to explain how various terms are interrelated. The characteristics of  $\prec$  include being asymmetric, anti-reflexive, and transitive[\[45,](#page-15-8)[46\]](#page-15-9):

- The only one greatest category root is the root of the  $M$ ,
- $\forall m_i^x \in M_i, m_j^y \in M_j, m_i^x \prec m_j^y \rightarrow m_j^y \not\prec m_i^x,$
- $\bullet$   $\forall m_i^x \in M_i, m_i^x \nless m_i^x,$
- $\bullet$   $\forall m_i^x \in M_i, m_j^y \in M_j, m_k^z \in M_k, m_i^x \prec m_j^y \wedge m_j^y \prec m_k^z \rightarrow m_i^x \prec m_k^z$

Finally, we define the Hierarchical MeSH Structure  $M$  as a partial order set  $\mathcal{M} = (\mathbf{M}, \prec),$  where  $\mathbf{M} = \{M_i\}_{i=1}^n$  is a level-organized term set and n denote the total depth. We then analyzed the structured subject terms in the contexts  $c_i^1, c_i^2$  and the original document  $d_i$ , incorporating the information content of their hierarchical positions. For any structured subject term  $m$ , we first calculate its information content as:

$$
IC(m) = -\log(\frac{freq(\mathcal{M}(m))}{n_{terms}}),\tag{1}
$$

where  $\mathcal{M}(m)$  denotes the set of all descendants of MeSH term m, and  $n_{terms}$ represents the total number of MeSH terms in the corpus. The IC reflects the

<span id="page-3-0"></span><sup>6</sup> PubMed:<https://pubmed.ncbi.nlm.nih.gov/>

specificity of a term; rarer terms have higher IC values. For any two MeSH terms  $m^x$  and  $m^y$ , we identify their Lowest Common Ancestor (LCA) in the MeSH hierarchy as  $\Lambda(m^x, m^y)$ . Referring to Lin's approach [\[24\]](#page-14-8), we calculate the semantic similarity in a taxonomy based on information content as:

$$
S_{x,y} = \frac{2 \times IC(A(m^x, m^y))}{IC(m^x) + IC(m^y)},\tag{2}
$$

where the  $S_{x,y}$  represent similarity between  $m^x$  and  $m^y$ . We then calculate the final similarity of the knowledge hierarchy between context  $c_i^1$  and original document  $d_i$  by averaging over all pairwise comparisons between terms as:

$$
\bar{S}_i^1 = \frac{1}{|d_i||c_i^1|} \sum_{m^x \in d_i} \sum_{m^y \in c_i^1} S_{x,y},\tag{3}
$$

where  $\bar{S}^1_i$  denotes the knowledge hierarchy similarity between original document  $d_i$  and associated context  $c_i^1$  for question  $q_i^1$ . Similarly, we calculate the knowledge hierarchy similarity  $\bar{S}_i^2$  between the original document  $d_i$  and  $c_i^2$ .

Preference Dataset Construction. Using the knowledge hierarchy similarity evaluation metrics, we conducted a similarity comparison of the generated questions  $q_i^1$  and  $q_i^2$  on a large number of original documents  $d_i \in \mathcal{R}$ . By comparing the corresponding similarity  $\bar{S}_i^1$  and  $\bar{S}_i^2$ , we assessed the alignment of  $q_i^1$  and  $q_i^2$ with the biomedical knowledge hierarchy. Based on such comparison, we consider  $q_i^1$  or  $q_i^2$  with the higher similarity score to be better aligned and designate it as  $q_i^+$ , while the other is designated as  $q_i^-$  and constructed a preference dataset  $\mathcal{P} = \{d_i, q_i^+, q_i^-\}_{i=1}^N.$ 

Direct Preference Optimization. With the preference data pairs prepared offline as  $P$ , we employed direct preference optimization (DPO)[\[33\]](#page-14-9) for model alignment. We performed DPO to get optimized question generator  $\theta_3$ ,  $\theta_1 \xrightarrow{\mathcal{P}} \theta^3$ .

Ideal Dataset Construction Algorithm [1](#page-5-0) provides a detailed illustration of the whole pipeline. We employed the optimized question generation model  $\theta_3$  to utilize a original document  $d_j \in \mathcal{R}$  as input and generate optimized question  $q_j$ . Documents related to  $q_j$  were then retrieved as context  $c_j$ , and we further employed the LLaMA-3-70B and GPT-4o to generate ideal answers  $a_i$ . We constructed two different ideal datasets for distinct purposes. To enhance the model's foundational understanding in the biomedical field, we combined questions  $q_j$  and relevant contexts  $c_j$  to form an ideal dataset  $\mathcal{I}_1 = \{q_j, c_j\}_{j=1}^N$ for continued pre-training. For improving question-answering performance, we combined questions  $q_j$ , relevant contexts  $c_j$ , and corresponding answers  $a_j$  to form an ideal supervised fine-tuning dataset  $\mathcal{I}_2 = \{q_j, c_j, a_j\}_{j=1}^N$ .

Algorithm 1: KAILIN Framework

<span id="page-5-0"></span>

|    | <b>Input:</b> Training set $\mathcal{T}$ , Updatable base model $\theta(\theta^1, \theta^2)$ trained from |  |  |  |
|----|-----------------------------------------------------------------------------------------------------------|--|--|--|
|    | LLaMA-2-7B, BioMistral as base model $\theta$ respretively, $\theta^3$ trained                            |  |  |  |
|    | from $\theta^1$ ), Static refined model $\theta^S$ , Raw dataset for distillation $\mathcal{R}$ ,         |  |  |  |
|    | Vector databse $V$ , Knowledge Hierarchy $M$ , Prompts.                                                   |  |  |  |
|    | <b>Output:</b> Ideal datasets $\mathcal{I}_1, \mathcal{I}_2$ .                                            |  |  |  |
|    | 1 Update $\theta$ with training set $\mathcal T$ to get distinct $\theta^1$ and $\theta^2$ # Prompt 1     |  |  |  |
|    | <b>2</b> for each document $d_i$ sampled from $R$ do                                                      |  |  |  |
| 3  | Generate question $q_i^1, q_i^2$ from $\theta^1, \theta^2$ using $d_i$ # Prompt 1 (Figure 9)              |  |  |  |
|    | Retrieve context $c_i^1$ and $c_i^2$ from V for $q_i^1$ and $q_i^2$<br>$\overline{\mathbf{4}}$            |  |  |  |
|    | Evaluate $((q_i^1, c_i^1), d_i)$ and $((q_i^2, c_i^2), d_i)$ pairs for hierarchy similarity               |  |  |  |
|    | <b>6</b> Construct preference dataset $\mathcal{P} = \{d_i, q_i^+, q_i^-\}_{i=1}^N$ based on similarity   |  |  |  |
|    | comparison via $\mathcal M$                                                                               |  |  |  |
|    | 7 Update $\theta^1$ with P to produce optimized question-generator $\theta^3$                             |  |  |  |
|    | <b>s</b> for each document $d_i$ sampled from $\mathcal{R}$ do                                            |  |  |  |
| 9  | Generate question $q_j$ from $\theta^3$ using $d_j \neq$ Prompt 1 (Figure 9)                              |  |  |  |
| 10 | Retrieve relevant context $c_i$ from $V$                                                                  |  |  |  |
| 11 | Generate answer $a_i$ from $\theta^S$ using $(q_i, c_j)$ # Prompt 3 (Figure 3)                            |  |  |  |
|    |                                                                                                           |  |  |  |

 $1, 1, 0, 0, 0, 1$ 

 $\overline{0}$ 

<span id="page-5-1"></span>12 return Ideal datasets  $\mathcal{I}_1 = \{q_j, c_j\}_{j=1}^N$  and  $\mathcal{I}_2 = \{q_j, c_j, a_j\}_{j=1}^N$ 

<span id="page-5-2"></span>findings. During this process, I identified the following key materials: 「{Context}」 Reflecting on these insights, I formulated the following research question: 「{Question}」.

**Prompt:**<br>Your task is to evaluate a scientific question in the biomedical field based on the provided context: 「{Question}, {Related Context}」. After analyzing the information, you must select one option from 「Yes / No / Maybe」 Your response should only consist of the chosen option, with no additional explanation or reasoning. Fig. 3: The prompt for PubMedQA inference. **Prompt:** To address the challenges for biomedical field, I reviewed the paper titled: 「{Raw Title}: {Raw Content}」. Motivated by this study, I conducted a literature review to gather additional resources and contextualize its

Fig. 4: The prompt for continuous pre-training.

Training for Downstream Task When we proceeded with further training for biomedical question-answering applications, we utilized the  $\mathcal{I}_1$  dataset for continued pretraining to achieve better performance improvements along with the subsequent supervised fine-tuning. We describe the two training stages in detail:

- Continuous Pre-training. We utilize heuristic questions  $q_i$  generated by the KAILIN framework, along with the retrieved Top- $k$  documents  $c_j$ associated with them, as the corpus for continuous pre-training, using the prompt as Figure [4.](#page-5-2) With the pre-training corpus constructed in this manner, we continued to pre-train our base models with varying parameter sizes.
- Supervised Fine-tuning. To optimize the model's question-answering performance following continuous pre-training, we further performed fullparameter fine-tuning on the models with the PQA-A training set from PubMedQA, with the prompt as Figure [3.](#page-5-1)

## 3 Experiment

### 3.1 Experimental Setups

Base Models and Baselines. We utilized Llama-2-7B, Llama-2-13B [\[39\]](#page-15-10), Llama-3-8B, and Llama-3-70B [\[8\]](#page-13-7) as the base models in our primary experiment, while also incorporating BioMistral [\[19\]](#page-13-4) for building the preference dataset in training the question generator. We conducted a comprehensive evaluation of various open-source models, including LLaMA-2 [\[39\]](#page-15-10), LLaMA-3 [\[8\]](#page-13-7), Mistral [\[13\]](#page-13-8), and Gemma[\[36\]](#page-14-10), as well as proprietary models like GPT-4[\[1\]](#page-12-2), and PaLM [\[34\]](#page-14-11). In particular, we focused on models specifically trained for the biomedical domain, such as BioMistral [\[19\]](#page-13-4), PMC-LLaMA [\[44\]](#page-15-3), HEAL [\[51\]](#page-15-11), and MMedLM [\[32\]](#page-14-12), to demonstrate the effectiveness of our approach.

Evaluation Datasets. We validated the results of our main experiment using the PubMedQA benchmark [\[17\]](#page-13-9), a dataset specifically designed to assess the performance of question-answering systems in the biomedical domain. PubMedQA is tailored to address questions relevant to biomedical literature, making it highly suitable for assessing our framework's adaption in this field. Additionally, we categorized the benchmark based on Medical Subject Headings (MeSH) [\[25\]](#page-14-7) and publication dates, enabling us to evaluate our system's improved understanding and robustness across diverse biomedical topics and varying time spans.

### 3.2 Main Results and Analysis

<span id="page-6-0"></span>Image /page/6/Figure/6 description: The image displays two bar charts comparing the performance of various language models. The left chart, labeled (a) LLM performance on the 13B parameter set, shows models like MedAlpaca(7B) scoring 72.8, LLaMA-2(7B) scoring 73.4, LLaMA-3(8B) scoring 74.0, MEDITRON(7B) scoring 74.4, PMC-LLaMA(7B) scoring 74.6, Mistral(7B) scoring 75.4, Gemma(7B) scoring 76.2, LLaMA-2(13B) scoring 76.4, KAILIN + LLaMA-2(7B) scoring 76.6, BioMistral(7B) scoring 77.5, PMC-LLaMA(13B) scoring 77.9, HEAL (13B) scoring 78.4, KAILIN + LLaMA-2(13B) scoring 79.7, MMed-LLaMA-3(8B) scoring 80.1, and KAILIN + LLaMA-3(8B) scoring 81.1. The right chart, labeled (b) LLM performance on the 70B parameter set, includes Galactica(120B) with a score of 77.6, GPT-3.5(>70B) with 79.6, GPT-4(>70B) with 80.4, MEDITRON(70B) with 81.6, Med-PaLM-2(>70B) with 81.8, GPT-4 Med Prompt(>70B) with 82.0, and KAILIN + LLaMA-3(70B) with 82.5. The x-axis for both charts ranges from 72 to 82, with tick marks at every two units.

(a) LLMs of fewer than 13B parameters (b) LLMs of more than 70B parameters

Fig. 5: Evaluations (accuracy  $(\%)$ ) for PubMedQA. Models marked with  $*$  indicate that they are domain-specific models. The  $\#$  symbol denotes closed-source models.

We compared KAILIN against various models divided into two groups based on parameter size: those with fewer than 13B parameters and those with 70B parameters or more. When comparing domain-specific models with general-purpose

models within LLMs with fewer than 13B parameters, as shown in Figure [5,](#page-6-0) we observed that general-purpose models tend to struggle more to excel in domainspecific tasks. However, the KAILIN framework enables general-purpose models to outperform domain-specific models with higher training costs, even with minimal additional training. The underlying driver stems from the KAILIN framework's use of MeSH-based knowledge hierarchy similarity evaluation, which effectively addresses the comprehension challenges posed by the rich terminologies and complex conceptual structures inherent in biomedical texts. This phenomenon indicates that the KAILIN framework excels in training small general-purpose models to adapt more effectively to specific domains, which is particularly advantageous in the fast-paced evolution of large model iterations.

When comparing LLMs with more than 70B parameters, we observed that the KAILIN framework enables LLaMA-3-70B to outperform closed-source models with significantly larger parameter counts, such as GPT-4 with MedPrompt[\[30](#page-14-13)[,29\]](#page-14-14) and Med-PaLM-2[\[34\]](#page-14-11). While larger models typically demonstrate superior performance, the KAILIN framework leverages MeSH-based knowledge hierarchy similarity evaluation for preference alignment. This approach acts as a pivotal underlying driver, enabling the model to excel in specific tasks and surpass significantly larger counterparts. This phenomenon also highlights a potential future direction for training domain-specific models: KAILIN demonstrates the use of smaller datasets that retain a comprehensive understanding of the knowledge hierarchy to optimize performance on domain-specific tasks.

### 3.3 Robustness Checking

When dealing with real-world problems, LLMs often struggle with tasks that involve managing cross-temporal and cross-disciplinary contexts. Therefore, we also conducted experimental validation to assess whether the KAILIN framework, by integrating documents across different time spans and sub-disciplines, enhances the robustness of representative 8B and 70B models.

<span id="page-7-0"></span>Image /page/7/Figure/5 description: This image displays a series of bar charts comparing the performance of different language models across various time spans. The legend at the top indicates five models: LLaMA-3-8B-Instruct (pink), MMed-LLaMA-3-8B-EnIns (blue), LLaMA-3-8B + KAILIN (green), LLaMA-3-70B-Instruct (purple), and LLaMA-3-70B+KAILIN (orange). The bar charts are organized into two rows, with four charts in the top row and four in the bottom row, each labeled with a time span: 1989-2000, 2001-2004, 2005-2007, 2008-2009, 2010-2011, 2012-2013, 2014-2015, and 2016-2017. The y-axis for all charts ranges from 30 to 90. To the right of the bar charts, a table lists the time spans and their corresponding proportions (Prop). The proportions are as follows: 1989-2000 (9.6%), 2001-2004 (12.2%), 2005-2007 (11.9%), 2008-2009 (11.9%), 2010-2011 (9.6%), 2012-2013 (14.8%), 2014-2015 (15.0%), and 2016-2017 (9.2%).

Fig. 6: Evaluations (accuracy  $(\%)$ ) for different chronological subsets problems in PubMedQA PQA-L on on our models compared to other representative models.

Chronological Robustness. In the comparison between the representative 8B and 70B models, as shown in Figure [6,](#page-7-0) we observed that the KAILIN framework enables models to exhibit greater robustness compared to MMedLM[\[32\]](#page-14-12), which is also designed for the biomedical field and uses LLaMA-3-8B as its base model. The underlying driver is that the KAILIN framework, during dataset distillation, incorporates richer context, thereby better approximating the full distribution of the original dataset. This contributes to a significant performance boost for the models. In addition, we also observed that larger models tend to exhibit more chronological robustness compared to smaller models. The underlying driver is that the increased parameter count of larger models corresponds to a greater knowledge capacity and stronger adaptability, enabling them to capture essential features more effectively when trained on data spanning different time periods.

<span id="page-8-0"></span>Image /page/8/Figure/2 description: This image contains a bar chart comparing the performance of different language models across various age groups: Female, Male, Middle Aged, Aged, Adult, and Adolescent. The legend indicates five models: LLaMA-3-8B-Instruct (pink), MMed-LLaMA-3-8B-EnIns (blue), LLaMA-3-8B + KAILIN (green), LLaMA-3-70B-Instruct (purple), and LLaMA-3-70B + KAILIN (orange). Each age group has a separate bar chart showing the performance scores, with the y-axis ranging from 40 to 100. To the right of the bar charts, a table lists MeSH terms (age groups) and their corresponding sample sizes: Female (785), Male (703), Middle Aged (542), Aged (414), Adult (492), and Adolescent (204).

Fig. 7: Evaluations (accuracy  $(\%)$ ) for different MeSH term subsets problems in PubMedQA PQA-L on our models compared to other representative models.

Subdisciplinary Robustness. As the Figure [7](#page-8-0) shows, in the comparison within the representative 8B and 70B models, we observed that KAILIN framework demonstrates greater robustness across various subdisciplines. We attribute this advantage to KAILIN's MeSH-based knowledge hierarchy similarity evaluation. Our method comprehensively analyzes multiple documents within the overall knowledge hierarchy, enabling the dataset to cover a more complete range across subdisciplines. This phenomenon highlights that KAILIN framework more closely aligns with the distribution of extensive biomedical literature datasets and is better adapted to the knowledge hierarchy in the biomedical field. Furthermore, discrepancies in accuracy, such as variations across different demographic and age-based subsets, indicate potential issues either with the model itself or the training dataset.

### 3.4 Scaling Law of Dataset Distillation

We conducted experiments using datasets of varying scales for continuous pretraining, followed by supervised fine-tuning under consistent experimental settings,

<span id="page-9-1"></span>10 Xunxin Cai et al.

Image /page/9/Figure/1 description: This is a line graph showing the accuracy of two different models, LLaMA-3-8B and LLaMA-3-70B, at different scales (K). The x-axis represents the scale in thousands (K), ranging from 20 to 100. The y-axis represents the accuracy in percentage (%). The LLaMA-3-8B model, represented by an orange line with circular markers, shows an increasing accuracy from 74.4% at scale 20 to 81.1% at scale 100. The LLaMA-3-70B model, represented by a green line with square markers, shows a higher accuracy overall, starting at 78.1% at scale 20 and reaching 82.5% at scale 100. Specific data points shown are: LLaMA-3-8B at (20, 74.4), (40, 75.5), (60, 78.3), (80, 79.9), and (100, 81.1); LLaMA-3-70B at (20, 78.1), (40, 78.1), (60, 78.3), (80, 81.2), and (100, 82.5).

Fig. 8: Evaluation(accuracy(%)) of Llama-3-8B and Llama-3-70B on PubMedQA using datasets of varying scales.

to evaluate the impact of different data volumes on model performance. In this study, we selected the Llama-3-8B and Llama-3-70B models as base models, to verify the impact of varying dataset scales on models of different sizes.

As shown in Figure [8,](#page-9-1) we observed that the larger LLaMA-3-70B model demonstrates overall better performance compared to the smaller LLaMA-3-8B model. We attributed this advantage to its larger parameter scale, which provides superior feature capture and generalization capabilities. We also observed that on datasets of varying sizes, both models showed a trend of performance improvement as the dataset size increased. The underlying driver is that larger datasets distilled by KAILIN are better able to represent a broader of biomedical knowledge due to greater number of documents being comprehensively analyzed. This phenomenon indicates that the KAILIN framework indeed produces higher-quality datasets when distilling larger-scale datasets. Moreover, we found that while smaller models may not outperform larger models, they seem to benefit more from progressively larger distilled datasets. We attribute this phenomenon to the degree of alignment between task complexity and model scale. This phenomenon indicates that larger models, due to potential knowledge redundancy, may gain less from incremental datasets on a specific task.

### 3.5 Ablation Study and Case Study

<span id="page-9-0"></span>Image /page/9/Figure/6 description: The image displays a document analysis framework with a source paper ID: PMID198207. The left side shows a prompt asking to analyze title and context to generate a question, with placeholders for Title and Context. Below this, the actual Title is 'Dimerization of papain induced by mercuric chloride and a bifunctional organic mercurial.' and the Abstract is 'The bifunctional mercurial meso 1,4 bis(acetatomercuri) 2,3 diethoxybutane and mercuric chloride are capable of dimerizing papain, ...'. The right side of the image presents four different question generation scenarios: 'FULL', 'w/o MeSH', 'w/o Embedding', and 'w/o BOTH'. Each scenario includes a generated question and a comment evaluating its quality. The 'FULL' scenario asks 'Does thiol attachment of meso-dimerization reagents to papain result in a highly stable dimer?' with a 'Best' comment. The 'w/o MeSH' scenario asks 'Does papain lose its activity when reacted with mercuric chloride?' with a 'Good' comment. The 'w/o Embedding' scenario asks 'Is the proteolytic enzyme papain used in detergents?' with a 'Good' comment. The 'w/o BOTH' scenario asks 'What is the target of the drug Papain?' with a 'Bad' comment.

Fig. 9: A case study comparing the generated questions under different experimental settings in the ablation study.

In the ablation study, we employed two different inference settings from PubMedQA study: reasoning-required and question-only. In our previous experiments, we used the default and recommended reasoning-required setting, where the model is provided with the question and relevant context to make a decision. In the question-only setting, only the question is provided without any context, which tests the model's inherent knowledge more directly compared to the reasoning-required setting. We applied both inference settings to further assess the impact of different ablation configurations on the model's knowledge retention and performance. We conducted a comprehensive evaluation of the impact of different components of the KAILIN framework by combining quantitative analysis on PubMedQA with qualitative analysis from the case studies.

<span id="page-10-0"></span>Table 1: Evaluations (accuracy  $(\%)$ ) of the overall experimental results of the ablation study. The reasoning-required and question-only are both inference settings in PubMedQA study.

|                 | Reasoning-required | Question-only |
|-----------------|--------------------|---------------|
| w/o MeSH        | 69                 | 56.4          |
| w/o Embedding   | 71.8               | 55.6          |
| w/o <b>Both</b> | 64.8               | 43            |
| Full            | <b>72.4</b>        | <b>57.8</b>   |

We investigated the impact of our MeSH-based knowledge hierarchy similarity evaluation by instead utilizing a Term Frequency-Inverse Document Frequency (TF-IDF) approach for evaluating the document collection. We also investigated the influence of the embedding model during retrieval process by randomly sampling top-k documents and analyzing their replacement in the MeSH-based preference selection process.

As shown in Table [1,](#page-10-0) we found that the overall performance was worse under the ablation setting without MeSH than without Embedding model, highlighting the critical role of MeSH as part of the framework. Without MeSH serving as a structured marker for each document within the knowledge hierarchy, the model struggled to align with the overall knowledge hierarchy of the biomedical field through isolated documents. From the specific cases shown in Figure [9,](#page-9-0) it can be inferred from the questions posed that the model understands "activity" as related to papain being an enzyme. However, due to a lack of comprehensive knowledge, it fails to generate more integrative questions that reflect a broader perspective. Additionally, performance variations under the question-only inference setting further demonstrated that, in the ablation setting without the Embedding model, the model struggled to grasp question-specific knowledge. This limitation led to poorer performance when relevant contextual information was absent. As the specific cases in Figure [9,](#page-9-0) we found that the model, due to the presence of MeSH in the experimental setup, could grasp the meaning of papain as a "proteolytic enzyme" within the overall knowledge hierarchy. However, it was evident that the model lacked specific knowledge related to papain, as it failed to ask questions about enzyme activity or the dimerization of papain.

### 3.6 Hyperparameter Experiment

<span id="page-11-0"></span>Image /page/11/Figure/2 description: The line graph shows the accuracy (%) of a model based on the retrieval number (k). The x-axis represents the retrieval number, ranging from 2 to 8. The y-axis represents the accuracy in percentage, ranging from 69% to 73%. The data points are plotted as follows: at k=2, the accuracy is 69.4%; at k=4, the accuracy is 72.8%; at k=6, the accuracy is 72.7%; and at k=8, the accuracy is 72.4%. The line shows an increasing trend from k=2 to k=4, and then a slight decreasing trend from k=4 to k=8.

Fig. 10: Evaluation( $\arccos(\%)$ ) on PubMedQA using continuous pre-training datasets of retrieving different Top-k documents.

In the KAILIN framework, the number of retrieved documents in context, Top- $k$ , is a highly influential parameter. This parameter directly impacts the balance between the comprehensiveness, richness of context information, and the upper limit of contextual understanding of models. We conducted a set of hyperparameter experiments to study the effect of retrieving different numbers of  $k$  documents as context on model performance. As shown in Figure [10,](#page-11-0) the model exhibited the best performance when k was set to 4. This optimal  $k$  allows the model to gain a more comprehensive understanding of the overall information relevant to the question in the biomedical domain and and improving alignment, while reducing noise. We maintained this optimal setting for the remainder of the experiments.

## 4 Related Work

Dataset Distillation. Dataset distillation[\[50\]](#page-15-12) is an information extraction technique that uses generative models to distill core information from large-scale raw datasets, resulting in high-quality datasets. Existing dataset distillation approaches are primarily categorized based on their optimization objectives into three types: performance matching, parameter matching, and distribution matching. Performance matching methods [\[43,](#page-15-13)[55,](#page-15-14)[27](#page-14-15)[,28\]](#page-14-16) aim to optimize dataset distillation by minimizing the loss of a model trained on the distilled dataset when evaluated on the raw dataset. Parameter matching methods [\[54](#page-15-15)[,20](#page-14-17)[,16,](#page-13-10)[6\]](#page-13-11) focus on the consistency of trainable model parameters when trained on the distilled dataset compared to the raw dataset. Unlike these two training-based objectives, distribution matching methods[\[53\]](#page-15-16) analyze the distilled dataset itself, using the distributional consistency between the distilled and raw datasets as the target for optimization.

Synthesis Text Data in Biomedical field. Existing methods for generating and utilizing synthetic text [\[5\]](#page-13-6) data in biomedical text mining face several challenges. PubMedQA [\[17\]](#page-13-9) relies on converting titles into questions, often introducing noise due to simplistic or irrelevant content. Attempts to improve PubMedQA through LLM-based rewriting strategies, like those using GPT-3.5-turbo or GPT-4, fail to offer significant diversity in generated content[\[11\]](#page-13-12). MedSyn[\[18\]](#page-13-5), while leveraging a Medical Knowledge Graph and LLMs like GPT-4, lacks a comprehensive integration of hierarchical knowledge. Some other methods that rely on

12

experts to use LLMs with their own knowledge [\[12](#page-13-13)[,35\]](#page-14-5) place too much dependence on expert effort, lacking automation and efficiency.

LLMs in Biomedical Domain. Recent advancements in biomedical language models have focused on improving domain adaptation to better handle dense terminology and complex medical concepts. Approaches such as retrieval-augmented generation have enhanced models' ability to retrieve relevant information for domain-specific tasks[\[10](#page-13-14)[,26,](#page-14-18)[2\]](#page-12-3). PMC-LLaMA[\[44\]](#page-15-3) has been fine-tuned using millions of biomedical papers and medical textbooks, integrating medical knowledge graphs like UMLS for domain knowledge. Similarly, BioMistral[\[19\]](#page-13-4) has applied extensive data processing and adaptive pretraining on PubMed Central, while HEAL[\[51\]](#page-15-11) has combined public and proprietary datasets for continuous pretraining on a general-purpose model, further enhancing its biomedical capabilities.

## 5 Conclusion

In this paper, we proposed a novel automated dataset distillation framework, namely KAILIN, which integrates domain-specific knowledge hierarchy. We aim for KAILIN to serve as an automated approach that distlls datasets of high-quality from existing unlabeled datasets at lower costs while preserving the integrity of domain knowledge hierarchy. Our method was evaluated on the PubMedQA leaderboard, and we further assessed performance robustness across subsets divided by time span and disciplines. These evaluations revealed how LLMs perform differently across various time periods and subfields, demonstrating the superiority of our approach.

## 6 Ackownledgement

This work is partially supported by the Beijing Municipal Natural Science Foundation (No.4254089), the Postdoctoral Fellowship Program of CPSF (No.GZC20232736), the China Postdoctoral Science Foundation Funded Project (No.2023M743565), and National Natural Science Foundation of China (No.92470204).

## References

- <span id="page-12-2"></span>1. Achiam, J., Adler, S., Agarwal, S., Ahmad, L., Akkaya, I., Aleman, F.L., Almeida, D., Altenschmidt, J., Altman, S., Anadkat, S., et al.: Gpt-4 technical report. arXiv preprint arXiv:2303.08774 (2023)
- <span id="page-12-3"></span>2. Alkhalaf, M., Yu, P., Yin, M., Deng, C.: Applying generative ai with retrieval augmented generation to summarize and extract key clinical information from electronic health records. Journal of Biomedical Informatics p. 104662 (2024)
- <span id="page-12-1"></span>3. Angerer, P., Simon, L., Tritschler, S., Wolf, F.A., Fischer, D., Theis, F.J.: Single cells make big data: new challenges and opportunities in transcriptomics. Current opinion in systems biology 4, 85–91 (2017)
- <span id="page-12-0"></span>4. Barrit, S., El Hadwe, S., Carron, R., Madsen, J.R.: Rise of large language models in neurosurgery. Journal of Neurosurgery  $1(aop)$ ,  $1-2$  (2024)

- 14 Xunxin Cai et al.
- <span id="page-13-6"></span>5. Cai, X., Xiao, M., Ning, Z., Zhou, Y.: Resolving the imbalance issue in hierarchical disciplinary topic inference via llm-based data augmentation. In: 2023 IEEE International Conference on Data Mining (ICDM). pp. 956–961. IEEE (2023)
- <span id="page-13-11"></span>6. Cazenavette, G., Wang, T., Torralba, A., Efros, A.A., Zhu, J.Y.: Dataset distillation by matching training trajectories. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition. pp. 4750–4759 (2022)
- <span id="page-13-0"></span>7. Chen, Z., Hu, C., Wu, M., Long, Q., Wang, X., Zhou, Y., Xiao, M.: Genesum: Large language model-based gene summary extraction. In: 2024 IEEE International Conference on Bioinformatics and Biomedicine (BIBM). pp. 1438–1443. IEEE (2024)
- <span id="page-13-7"></span>8. Dubey, A., Jauhri, A., Pandey, A., Kadian, A., Al-Dahle, A., Letman, A., Mathur, A., Schelten, A., Yang, A., Fan, A., et al.: The llama 3 herd of models. arXiv preprint arXiv:2407.21783 (2024)
- <span id="page-13-1"></span>9. Fang, C., Qin, C., Zhang, Q., Yao, K., Zhang, J., Zhu, H., Zhuang, F., Xiong, H.: Recruitpro: A pretrained language model with skill-aware prompt learning for intelligent recruitment. In: Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining. pp. 3991–4002 (2023)
- <span id="page-13-14"></span>10. Gao, Y., Zong, L., Li, Y.: Enhancing biomedical question answering with parameterefficient fine-tuning and hierarchical retrieval augmented generation. CLEF Working Notes (2024)
- <span id="page-13-12"></span>11. Guo, Z., Wang, P., Wang, Y., Yu, S.: Improving small language models on pubmedqa via generative data augmentation. arXiv preprint arXiv:2305.07804 (2023)
- <span id="page-13-13"></span>12. Indran, I.R., Paramanathan, P., Gupta, N., Mustafa, N.: Twelve tips to leverage ai for efficient and effective medical question generation: A guide for educators using chat gpt. Medical Teacher pp. 1–6 (2023)
- <span id="page-13-8"></span>13. Jiang, A.Q., Sablayrolles, A., Mensch, A., Bamford, C., Chaplot, D.S., Casas, D.d.l., Bressand, F., Lengyel, G., Lample, G., Saulnier, L., et al.: Mistral 7b. arXiv preprint arXiv:2310.06825 (2023)
- <span id="page-13-3"></span>14. Jiang, F., Qin, C., Yao, K., Fang, C., Zhuang, F., Zhu, H., Xiong, H.: Enhancing question answering for enterprise knowledge bases using large language models. In: International Conference on Database Systems for Advanced Applications. pp. 273–290. Springer (2024)
- <span id="page-13-2"></span>15. Jiang, F., Qin, C., Zhang, J., Yao, K., Chen, X., Shen, D., Zhu, C., Zhu, H., Xiong, H.: Towards Efficient Resume Understanding: A Multi-Granularity Multi-Modal Pre-Training Approach . In: 2024 IEEE International Conference on Multimedia and Expo (ICME). pp. 1–6. IEEE Computer Society, Los Alamitos, CA, USA (Jul 2024). <https://doi.org/10.1109/ICME57554.2024.10687439>, [https://doi.](https://doi.ieeecomputersociety.org/10.1109/ICME57554.2024.10687439) [ieeecomputersociety.org/10.1109/ICME57554.2024.10687439](https://doi.ieeecomputersociety.org/10.1109/ICME57554.2024.10687439)
- <span id="page-13-10"></span>16. Jiang, Z., Gu, J., Liu, M., Pan, D.Z.: Delving into effective gradient matching for dataset condensation. In: 2023 IEEE International Conference on Omni-layer Intelligent Systems (COINS). pp. 1–6. IEEE (2023)
- <span id="page-13-9"></span>17. Jin, Q., Dhingra, B., Liu, Z., Cohen, W.W., Lu, X.: Pubmedqa: A dataset for biomedical research question answering. arXiv preprint arXiv:1909.06146 (2019)
- <span id="page-13-5"></span>18. Kumichev, G., Blinov, P., Kuzkina, Y., Goncharov, V., Zubkova, G., Zenovkin, N., Goncharov, A., Savchenko, A.: Medsyn: Llm-based synthetic medical text generation framework. In: Joint European Conference on Machine Learning and Knowledge Discovery in Databases. pp. 215–230. Springer (2024)
- <span id="page-13-4"></span>19. Labrak, Y., Bazoge, A., Morin, E., Gourraud, P.A., Rouvier, M., Dufour, R.: Biomistral: A collection of open-source pretrained large language models for medical domains. arXiv preprint arXiv:2402.10373 (2024)

- <span id="page-14-17"></span>20. Lee, S., Chun, S., Jung, S., Yun, S., Yoon, S.: Dataset condensation with contrastive signals. In: International Conference on Machine Learning. pp. 12352–12364. PMLR (2022)
- <span id="page-14-1"></span>21. Li, C., Long, Q., Zhou, Y., Xiao, M.: screader: Prompting large language models to interpret scrna-seq data. arXiv preprint arXiv:2412.18156 (2024)
- <span id="page-14-6"></span>22. Li, Z., Zhu, H., Lu, Z., Yin, M.: Synthetic data generation with large language models for text classification: Potential and limitations. arXiv preprint arXiv:2310.07849 (2023)
- <span id="page-14-3"></span>23. Liévin, V., Hother, C.E., Motzfeldt, A.G., Winther, O.: Can large language models reason about medical questions? Patterns 5(3) (2024)
- <span id="page-14-8"></span>24. Lin, D., et al.: An information-theoretic definition of similarity. In: Icml. vol. 98, pp. 296–304 (1998)
- <span id="page-14-7"></span>25. Lipscomb, C.E.: Medical subject headings (mesh). Bulletin of the Medical Library Association 88(3), 265 (2000)
- <span id="page-14-18"></span>26. Merker, J.H., Bondarenko, A., Hagen, M., Viehweger, A.: Mibi at bioasq 2024: retrieval-augmented generation for answering biomedical questions (2024)
- <span id="page-14-15"></span>27. Nguyen, T., Chen, Z., Lee, J.: Dataset meta-learning from kernel ridge-regression. arXiv preprint arXiv:2011.00050 (2020)
- <span id="page-14-16"></span>28. Nguyen, T., Novak, R., Xiao, L., Lee, J.: Dataset distillation with infinitely wide convolutional networks. Advances in Neural Information Processing Systems 34, 5186–5198 (2021)
- <span id="page-14-14"></span>29. Nori, H., King, N., McKinney, S.M., Carignan, D., Horvitz, E.: Capabilities of gpt-4 on medical challenge problems. arXiv preprint arXiv:2303.13375 (2023)
- <span id="page-14-13"></span>30. Nori, H., Lee, Y.T., Zhang, S., Carignan, D., Edgar, R., Fusi, N., King, N., Larson, J., Li, Y., Liu, W., et al.: Can generalist foundation models outcompete special-purpose tuning? case study in medicine. arXiv preprint arXiv:2311.16452 (2023)
- <span id="page-14-2"></span>31. Qin, C., Fang, C., Yao, K., Chen, X., Zhuang, F., Zhu, H.: Cotr: Efficient job task recognition for occupational information systems with class-incremental learning. ACM Trans. Manage. Inf. Syst. (Jan 2025). <https://doi.org/10.1145/3712306>, <https://doi.org/10.1145/3712306>, just Accepted
- <span id="page-14-12"></span>32. Qiu, P., Wu, C., Zhang, X., Lin, W., Wang, H., Zhang, Y., Wang, Y., Xie, W.: Towards building multilingual language model for medicine. arXiv preprint arXiv:2402.13963 (2024)
- <span id="page-14-9"></span>33. Rafailov, R., Sharma, A., Mitchell, E., Manning, C.D., Ermon, S., Finn, C.: Direct preference optimization: Your language model is secretly a reward model. Advances in Neural Information Processing Systems 36 (2024)
- <span id="page-14-11"></span>34. Singhal, K., Tu, T., Gottweis, J., Sayres, R., Wulczyn, E., Hou, L., Clark, K., Pfohl, S., Cole-Lewis, H., Neal, D., et al.: Towards expert-level medical question answering with large language models. arXiv preprint arXiv:2305.09617 (2023)
- <span id="page-14-5"></span>35. Tang, R., Han, X., Jiang, X., Hu, X.: Does synthetic data generation of llms help clinical text mining? arXiv preprint arXiv:2303.04360 (2023)
- <span id="page-14-10"></span>36. Team, G., Mesnard, T., Hardin, C., Dadashi, R., Bhupatiraju, S., Pathak, S., Sifre, L., Rivière, M., Kale, M.S., Love, J., et al.: Gemma: Open models based on gemini research and technology. arXiv preprint arXiv:2403.08295 (2024)
- <span id="page-14-4"></span>37. Thakkar, V., Silverman, G.M., Kc, A., Ingraham, N.E., Jones, E., King, S., Tignanelli, C.J.: Comparison of large language models versus traditional information extraction methods for real world evidence of patient symptomatology in acute and post-acute sequelae of sars-cov-2 (2024)
- <span id="page-14-0"></span>38. Thirunavukarasu, A.J., Ting, D.S.J., Elangovan, K., Gutierrez, L., Tan, T.F., Ting, D.S.W.: Large language models in medicine. Nature medicine 29(8), 1930–1940 (2023)

- 16 Xunxin Cai et al.
- <span id="page-15-10"></span>39. Touvron, H., Martin, L., Stone, K., Albert, P., Almahairi, A., Babaei, Y., Bashlykov, N., Batra, S., Bhargava, P., Bhosale, S., et al.: Llama 2: Open foundation and fine-tuned chat models. arXiv preprint arXiv:2307.09288 (2023)
- <span id="page-15-7"></span>40. Tsatsaronis, G., Schroeder, M., Paliouras, G., Almirantis, Y., Androutsopoulos, I., Gaussier, E., Gallinari, P., Artieres, T., Alvers, M.R., Zschunke, M., et al.: Bioasq: A challenge on large-scale biomedical semantic indexing and question answering. In: 2012 AAAI Fall Symposium Series (2012)
- <span id="page-15-0"></span>41. Wang, C., Long, Q., Xiao, M., Cai, X., Wu, C., Meng, Z., Wang, X., Zhou, Y.: Biorag: A rag-llm framework for biological question reasoning. arXiv preprint arXiv:2408.01107 (2024)
- <span id="page-15-2"></span>42. Wang, C., Li, M., He, J., Wang, Z., Darzi, E., Chen, Z., Ye, J., Li, T., Su, Y., Ke, J., et al.: A survey for large language models in biomedicine. arXiv preprint arXiv:2409.00133 (2024)
- <span id="page-15-13"></span>43. Wang, T., Zhu, J.Y., Torralba, A., Efros, A.A.: Dataset distillation. arXiv preprint arXiv:1811.10959 (2018)
- <span id="page-15-3"></span>44. Wu, C., Lin, W., Zhang, X., Zhang, Y., Xie, W., Wang, Y.: Pmc-llama: toward building open-source language models for medicine. Journal of the American Medical Informatics Association p. ocae045 (2024)
- <span id="page-15-8"></span>45. Wu, F., Zhang, J., Honavar, V.: Learning classifiers using hierarchically structured class taxonomies. In: International symposium on abstraction, reformulation, and approximation. pp. 313–320. Springer (2005)
- <span id="page-15-9"></span>46. Xiao, M., Qiao, Z., Fu, Y., Dong, H., Du, Y., Wang, P., Xiong, H., Zhou, Y.: Hierarchical interdisciplinary topic detection model for research proposal classification. IEEE Transactions on Knowledge and Data Engineering 35(9), 9685–9699 (2023). <https://doi.org/10.1109/TKDE.2023.3248608>
- <span id="page-15-6"></span>47. Xiao, M., Qiao, Z., Fu, Y., Du, Y., Wang, P., Zhou, Y.: Expert knowledge-guided length-variant hierarchical label generation for proposal classification. In: 2021 IEEE International Conference on Data Mining (ICDM). pp. 757–766. IEEE (2021)
- <span id="page-15-5"></span>48. Xiao, M., Wu, M., Qiao, Z., Fu, Y., Ning, Z., Du, Y., Zhou, Y.: Interdisciplinary fairness in imbalanced research proposal topic inference: A hierarchical transformerbased method with selective interpolation. ACM Transactions on Knowledge Discovery from Data
- <span id="page-15-1"></span>49. Xiao, M., Zhang, W., Huang, X., Zhu, H., Wu, M., Li, X., Zhou, Y.: Knowledgeguided biomarker identification for label-free single-cell rna-seq data: A reinforcement learning perspective. arXiv preprint arXiv:2501.04718 (2025)
- <span id="page-15-12"></span>50. Yu, R., Liu, S., Wang, X.: Dataset distillation: A comprehensive review. IEEE Transactions on Pattern Analysis and Machine Intelligence (2023)
- <span id="page-15-11"></span>51. Yuan, D., Rastogi, E., Naik, G., Chintagunta, J., Rajagopal, S.P., Zhao, F., Goyal, S., Ward, J.: A continued pretrained llm approach for automatic medical note generation. arXiv preprint arXiv:2403.09057 (2024)
- <span id="page-15-4"></span>52. Yuan, J., Jin, Z., Guo, H., Jin, H., Zhang, X., Smith, T., Luo, J.: Constructing biomedical domain-specific knowledge graph with minimum supervision. Knowledge and Information Systems 62, 317–336 (2020)
- <span id="page-15-16"></span>53. Zhao, B., Bilen, H.: Dataset condensation with distribution matching. In: Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision. pp. 6514–6523 (2023)
- <span id="page-15-15"></span>54. Zhao, B., Mopuri, K.R., Bilen, H.: Dataset condensation with gradient matching. arXiv preprint arXiv:2006.05929 (2020)
- <span id="page-15-14"></span>55. Zhou, Y., Nezhadarya, E., Ba, J.: Dataset distillation using neural feature regression. Advances in Neural Information Processing Systems 35, 9813–9827 (2022)
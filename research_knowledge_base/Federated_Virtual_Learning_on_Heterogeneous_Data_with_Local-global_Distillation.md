# **Federated Learning on Virtual Heterogeneous Data with Local-Global Dataset Distillation**

**<PERSON><PERSON><PERSON>** *<EMAIL>*

*University of British Columbia Vector Institute*

**<PERSON><PERSON><PERSON>**

*University of British Columbia*

*NVIDIA*

**Daguang Xu**
*NVIDIA*

**<PERSON><PERSON><PERSON>** *ruin<PERSON><PERSON>@alumni.ubc.ca*

*University of British Columbia Vector Institute*

# San Zhao

*NVIDIA*

*NVIDIA*

**Xiaoxiao Li** *<EMAIL> University of British Columbia Vector Institute*

**Reviewed on OpenReview:** <https://openreview.net/forum?id=QplBL2pV4Z>

## **Abstract**

While Federated Learning (FL) is gaining popularity for training machine learning models in a decentralized fashion, numerous challenges persist, such as asynchronization, computational expenses, data heterogeneity, and gradient and membership privacy attacks. Lately, dataset distillation has emerged as a promising solution for addressing the aforementioned challenges by generating a compact synthetic dataset that preserves a model's training efficacy. *However, we discover that using distilled local datasets can amplify the heterogeneity issue in FL.* To address this, we propose **Fed**erated Learning on Virtual Heterogeneous Data with **Local-G**lobal **D**ataset **D**istillation (FEDLGD), where we seamlessly integrate dataset distillation algorithms into FL pipeline and train FL using a smaller synthetic dataset (referred as *virtual data*). Specifically, to harmonize the domain shifts, we propose iterative distribution matching to inpaint global information to *local virtual data* and use federated gradient matching to distill *global virtual data* that serve as anchor points to rectify heterogeneous local training, without compromising data privacy. We experiment on both benchmark and real-world datasets that contain heterogeneous data from different sources, and further scale up to an FL scenario that contains a large number of clients with heterogeneous and classimbalanced data. Our method outperforms *state-of-the-art* heterogeneous FL algorithms under various settings. Our code is available at [https://github.com/ubc-tea/FedLGD.](https://github.com/ubc-tea/FedLGD)

## **1 Introduction**

Having a compatible training dataset is an essential *de facto* precondition in modern machine learning. However, in areas such as medical applications, collecting such a massive amount of data is not realistic since it may compromise privacy regulations such as GDPR (Voigt  $&$  Von dem Bussche, 2017). Thus, researchers seek to circumvent the privacy leakage by utilizing federated learning pipelines or training with synthetic data.

*<EMAIL>*

*<EMAIL>*

**Can Zhao** *<EMAIL>*

**Daguang Xu** *<EMAIL>*

<EMAIL>

Federated learning (FL) [\(McMahan et al., 2017\)](#page-15-0) has emerged as a pivotal paradigm for conducting machine learning on data from multiple sources in a distributed manner. Traditional FL involves a large number of clients collaborating to train a global model. By keeping data local and sharing only the local model updates, FL prevents the direct exposure of local datasets in collaborative training. However, despite these advantages, several research challenges remain in FL, including computational costs, asynchronization, data heterogeneity, and vulnerabilities to deep privacy attacks [\(Wen et al., 2023\)](#page-16-1).

Another approach to GDPR compliance that has gained increased interest is using synthetic data in machine learning model training to supplement or replace real data when the latter is not suitable for direct use [\(Nikolenko, 2021\)](#page-15-1). Among data synthesis methods, dataset distillation [\(Wang et al., 2018;](#page-16-2) [Cazenavette](#page-13-0) [et al., 2022;](#page-13-0) [Zhao et al., 2021;](#page-17-0) [Zhao & Bilen, 2021;](#page-17-1) [2023\)](#page-17-2) has emerged as an ideal data synthesis strategy, as it is explored to enhance the efficiency and privacy of machine learning. Dataset distillation creates a compact synthetic dataset while retaining similar model performance of that trained on the original dataset, allowing efficiently training a machine learning model [\(Zhao et al., 2021;](#page-17-0) [Zhao & Bilen, 2023\)](#page-17-2). The distilled data usually remains low fidelity to the raw data but yet contains highly condensed essential information that makes the appearance of the distilled data dissimilar to the real data [\(Dong et al., 2022\)](#page-14-0).

In this work, we introduce an effective training strategy that leverages both FL and virtual data generated via dataset distillation, referred as *federated virtual learning*, as the models are trained from virtual data (also referred as synthetic data) [\(Xiong et al., 2023;](#page-16-3) [Goetz & Tewari, 2020;](#page-14-1) [Hu et al., 2022\)](#page-14-2). In particular, we aim to find the best way to incorporate dataset distillation into FL under ordinary FL pipeline, where the only change is replacing real data with virtual data for local training. A simple approach is to generate synthetic data first and then use it for FL training; however, this could lead to suboptimal performance in data heterogeneous settings. We observe increased divergence in loss curves in early FL rounds if we simply replace real data with distilled virtual data synthesized by Distribution Matching (DM) [\(Zhao &](#page-17-2) [Bilen, 2023\)](#page-17-2). Thus, We perform a simple experiment: Measure the distances between a set of digits datasets (please refer to DIGITS in Sec. [5.2](#page-9-0) for details) before and after distillation with DM. Statistically, we find that the MMD scores increase after distillation, resulting in an averaged 37% increment. Empirically, we visualize the tSNE plots of two different datasets (USPS [\(Hull, 1994\)](#page-14-3) and SynthDigits [\(Ganin & Lempitsky,](#page-14-4) [2015\)](#page-14-4)) in Fig. [1,](#page-1-0) and distributions become diverse after distillation. This reveals that local virtual data from dataset distillation may worsen the data heterogeneity issue in FL. Note that the data heterogeneity referred here (also throughout the paper) is *domain shift*, which assumes variations in  $P(X|y)$  across clients, where *X* represents the input data and *y* the corresponding labels. The concept differs from label shift (*P*(*y*)), which considers the heterogeneity on the labels.

To alleviate the problem of data heterogeneity, we enforce consistency in local embedded features using consensual anchors that capture global distribution. Existing works usually rely on the anchors yield from pre-generated noise [\(Tang et al., 2022\)](#page-16-4) that cannot reflect training data distribution; or shared additional features from the client side [\(Zhou et al.,](#page-17-3) [2023;](#page-17-3) [Ye et al., 2023b\)](#page-16-5), exposing more data leakage. To overcome the limitations, we propose an effective solution to address the heterogeneity issues using global virtual anchor for regularization, supported by our theoretical analysis. Without compromising privacy in implementation, our global anchors are distilled from **pre-existing** shared gradient information in FL to facilitate sharing global distribution information.

Apart from facilitating heterogeneous FL, such federated virtual learning further reduces computational cost and offers better empirical privacy protection. Specifically, we empirically demonstrate the

<span id="page-1-0"></span>Image /page/1/Figure/6 description: The image displays two scatter plots side-by-side, labeled (a) Original datasets and (b) Distilled virtual datasets. Both plots show data points represented by circles and crosses, colored according to a legend that maps colors to labels 0.0 through 9.0. The legend also indicates that circles represent data from client0 and crosses represent data from client1. Both plots feature two dashed red ellipses highlighting clusters of data points. The plot on the right, (b) Distilled virtual datasets, appears to show a more distinct separation of data clusters compared to the plot on the left, (a) Original datasets.

Figure 1: Distilled local datasets using Distribution Matching (DM) [\(Zhao & Bilen, 2023\)](#page-17-2) can worsen heterogeneity in FL. tSNE plots of (a) original datasets and (b) distilled virtual datasets of USPS (client 0) and SynthDigits (client 1). The two distributions are marked in the dashed curves. We observe fewer overlapped  $\circ$  and  $\times$  in (b) compared with (a), indicating higher heterogeneity between two clients after distillation. Statistically, we find that the Maximum Mean Discrepancy (MMD) [\(Gretton](#page-14-5) [et al., 2012\)](#page-14-5) scores increase after distillation, resulting in an averaged 37% increment.

reconstructed image from Gradient Inversion Attack [\(Geiping et al., 2020;](#page-14-6) [Huang et al., 2021\)](#page-14-7) trained on distilled data obtain much lower quality. We also present that our virtual training can defend better against Membership Inference Attacks [\(Shokri et al., 2017\)](#page-15-2). Please refer to Sec. [5](#page-8-0) for more details.

To this end, we propose FedLGD, a federated virtual learning method with local and global distillation. Data distillation is gaining attention in centralized machine learning. Recognizing the need for efficiency in FL, we propose integrating two efficient dataset distillation methods into our FL pipeline. Specifically, we propose *iterative distribution matching* in local distillation by comparing the feature distribution of real and synthetic data using an evolving global feature extractor. The local distillation results in compact local virtual datasets with balanced class distributions, achieving efficiency and synchronization while avoiding class imbalance. In addition, unlike previously proposed federated virtual learning methods that rely solely on local distillation [\(Goetz & Tewari, 2020;](#page-14-1) [Xiong et al., 2023;](#page-16-3) [Hu et al., 2022\)](#page-14-2), we also propose a novel and efficient method, *federated gradient matching*, that seamlessly integrate dataset distillation into FL pipeline to synthesize global virtual data as anchors on the server side using the uploaded averaged gradients. The global virtual data then serves as anchors to alleviate domain shifts among clients.

We conclude our contributions as follows:

- This paper focuses on an important but under-explored FL setting in which local models are trained on small virtual datasets, which we refer to as *federated virtual learning*, and we are *the first* to reveal that using distilled local virtual data using Distribution Matching [\(Zhao & Bilen, 2023\)](#page-17-2) may *exacerbate* the heterogeneity problem in the federated virtual learning setting.
- We propose to address the heterogeneity problem by our novel distillation strategies, *iterative distribution matching* and *federated gradient matching*, that utilizes pre-existing shared information in FL, and theoretically show it can effectively lower the statistic margin.
- Through comprehensive experiments on benchmark and real-world datasets, we empirically show that FedLGD outperforms existing state-of-the-art FL algorithms.

# **2 Related Work**

## **2.1 Dataset Distillation**

Data distillation [\(Wang et al., 2018\)](#page-16-2) aims to improve data efficiency by distilling the most essential feature from a large-scale dataset (e.g., datasets comprising billions of data points) into a certain terse and high-fidelity dataset. For example, Gradient Matching [\(Zhao et al., 2021\)](#page-17-0) is proposed to make the deep neural network produce similar gradients for both the terse synthetic images and the large-scale original dataset. Besides, [\(Cazenavette et al., 2022\)](#page-13-0) proposes matching the model training trajectory between real and synthetic data to guide the update for distillation. Another popular way of conducting data distillation is through Distribution Matching [\(Zhao & Bilen, 2023\)](#page-17-2). This strategy instead, attempts to match the distribution of the smaller synthetic dataset with the original large-scale dataset in latent representation space. It significantly improves the distillation efficiency. There are following works that further improves the utility of the distilled data [\(Li et al.;](#page-15-3) [Zhang et al., 2024\)](#page-16-6). Moreover, recent studies have justified that data distillation can defend against popular privacy attacks such as Gradient Inversion Attacks and Membership Inference Attacks [\(Dong et al., 2022;](#page-14-0) [Carlini et al., 2022b\)](#page-13-1), which is critical in federated learning. In practice, dataset distillation is used in healthcare for medical data sharing for privacy protection [\(Li et al., 2022\)](#page-15-4). We refer the readers to [\(Sachdeva & McAuley, 2023\)](#page-15-5) for further data distillation strategies.

## **2.2 Heterogeneous Federated Learning**

FL performance downgrading on non-iid data is a critical challenge [\(Ye et al., 2023a\)](#page-16-7). A variety of FL algorithms have been proposed ranging from global aggregation to local optimization to handle this heterogeneous issue, as echoed by [\(Sun et al., 2024\)](#page-16-8), data heterogeneity plays a critical role for model generalization. *Global aggregation* improves the global model exchange process for better unitizing the updated client models to create a powerful server model. FedNova [\(Wang et al., 2020\)](#page-16-9) notices an imbalance among different local models caused by different levels of training stage (e.g., certain clients train more epochs than others) and tackles such imbalance by normalizing and scaling the local updates accordingly. FedRod [\(Chen & Chao,](#page-13-2) [2021\)](#page-13-2) seeks to bridge personalized and generic FL by training separate global and local projection layers. Similarly, FedGELA [Fan et al.](#page-14-8) [\(2024\)](#page-14-8) also aims to bridge personalized and generic FL, employing simplex equiangular tight frame (ETF) to address class-imbalance data. Meanwhile, FedAvgM [\(Hsu et al., 2019\)](#page-14-9) applies the momentum to server model aggregation to stabilize the optimization. Furthermore, there are strategies to refine the server model or client models using knowledge distillation such as FedDF [\(Lin et al.,](#page-15-6) [2020\)](#page-15-6), FedGen [\(Zhu et al., 2021\)](#page-17-4), FedFTG [\(Zhang et al., 2022\)](#page-17-5), FedICT [\(Wu et al., 2023\)](#page-16-10), FedGKT [\(He](#page-14-10) [et al., 2020\)](#page-14-10), FedDKC [\(Wu et al., 2022\)](#page-16-11), and FedHKD [\(Chen et al., 2023\)](#page-13-3). However, we consider knowledge distillation and data distillation two orthogonal directions to solve data heterogeneity issues. *Local training optimization* aims to explore the local objective to tackle the non-iid issue in FL system. FedProx [\(Li et al.,](#page-15-7) [2020\)](#page-15-7) straightly adds *L*<sup>2</sup> norm to regularize the client model and previous server model. Scaffold [\(Karim](#page-14-11)[ireddy et al., 2020\)](#page-14-11) adds the variance reduction term to mitigate the "clients-drift". MOON [\(Li et al., 2021\)](#page-15-8) brings mode-level contrastive learning to maximize the similarity between model representations to stable the local training. There is also another line of works [\(Ye et al., 2023b;](#page-16-5) [Tang et al., 2022\)](#page-16-4) proposed to use a global *anchor* to regularize local training. Global anchor can be either a set of virtual global data or global virtual representations in feature space. However, in [\(Tang et al., 2022\)](#page-16-4), the empirical global anchor selection may not be suitable for data from arbitrary distribution as they don't update the anchor according to the training datasets. More recently, [\(Chen et al., 2024\)](#page-13-4) propose to utilize communication compression to facilitate heterogeneous FL training. Other methods, such as those rely on feature sharing from clients [\(Zhou](#page-17-3) [et al., 2023;](#page-17-3) [Ye et al., 2023b\)](#page-16-5), are less practical, as they pose greater data privacy risks compared to classical FL settings.

<span id="page-3-0"></span>

### **2.3 Datasets Distillation for FL**

Dataset distillation for FL is an emerging topic that has attracted attention due to its benefit for efficient FL systems. It trains model on distilled synthetic datasets, thus we refer it as federated virtual learning. It can help with FL synchronization and improve training efficiency by condensing every client's data into a small set. To the best of our knowledge, there are few published works on distillation in FL. Concurrently with our work, some studies [\(Goetz & Tewari, 2020;](#page-14-1) [Xiong et al., 2023;](#page-16-3) [Hu et al., 2022;](#page-14-2) [Huang et al.,](#page-14-12) [2024\)](#page-14-12) distill datasets locally and share the virtual datasets with other clients/servers. Although privacy is protected against *currently* existing attack models, we consider directly sharing local virtual data not a reliable strategy. It is worth noting that some recent works propose to share locally generated surrogates, such as prototypes [\(Tan et al., 2022\)](#page-16-12), performance-sensitive features [\(Yang et al., 2024\)](#page-16-13), or logits [\(Huang et al.,](#page-14-12) [2024\)](#page-14-12) instead of the global model parameters. However, this work focuses on combining dataset distillation with pre-existing shared information in the classical FL setting to alleviate the data heterogeneity problem.

# **3 Method**

## **3.1 Setup for Federated Virtual Learning**

We start with describing the classical FL setting. Suppose there are *N* parties (clients) who own local datasets  $(D^{c_1}, \ldots, D^{c_N})$ , and the goal of a classical FL system, such as FedAvg [\(McMahan et al., 2017\)](#page-15-0), is to train a global model with parameters  $\theta$  on the distributed datasets  $(D \equiv \bigcup_{i \in [N]} D^{c_i})$ . The objective function is written as:  $\mathcal{L}(\theta) = \sum_{i=1}^{N} \frac{|D^{c_i}|}{|D|}$  $\frac{D^{i}I}{|D|}\mathcal{L}_i(\theta)$ , where  $\mathcal{L}_i(\theta)$  is the empirical loss of client *i*. In practice, different clients in FL may have variant amounts of training samples, leading to asynchronized updates. In this work, we focus on a new type of FL training method – federated virtual learning, that trains on virtual datasets for efficiency and synchronization (discussed in Sec[.2.3\)](#page-3-0). Federated virtual learning synthesizes local virtual data  $\tilde{D}^{c_i}$  for client i for  $i \in [N]$  and form  $\tilde{D} \equiv \bigcup_{i \in [N]} \tilde{D}^{c_i}$ . Typically,  $|\tilde{D}^{c_i}| \ll |\tilde{D}^{c_i}|$  and  $|\tilde{D}^{c_i}| = |\tilde{D}^{c_j}|$ . A basic setup for federated virtual learning is to replace  $D^{c_i}$  with  $\tilde{D}^{c_i}$  to train FL model on the virtual datasets.

Image /page/4/Figure/1 description: The image depicts a two-part flowchart illustrating federated learning processes. The top section, titled "Selected Rounds - Local-Global Data Distribution," shows two parallel processes. On the left, under "Client 1" to "Client N," data from clients is processed. One path involves iterative distribution matching using LMMD, leading to a new dataset. Another path involves model updating using LCE, resulting in a local model. These local models are then sent to a server. On the right, the server aggregates models and performs federated gradient matching using LDist, updating a global dataset and model. The middle section displays visual representations of data transformation, showing initial noisy images evolving into clearer, structured images. The bottom section, titled "Unselected Rounds - Local-Global Model Update," shows a simplified process. Clients receive a new global dataset and update their local models using LCE + lambda Lcon. These updated local models are then sent to the server for model aggregation, resulting in a new global model.

<span id="page-4-0"></span>**Selected Rounds - Local-Global Data Distillation**

Figure 2: Overview of the proposed method FedLGD. We split FL rounds into *selected* and *unselected* rounds. For the *selected* rounds, clients will refine the local virtual data and update local models, while the server uses aggregated gradients to update global virtual data and the global model. We term this procedure Local-Global Data Distillation. For the *unselected* rounds, we perform ordinary FL training with virtual data while adding regularization loss on local model updating. In the middle box, we also show the evolution of global and virtual data. Observe that although local virtual does not change visually, we found the local distillation steps are essential for improving model performance as shown in Fig. [3c](#page-11-0) and [3d.](#page-11-1)

# **3.2 Overall Pipeline**

The overall pipeline of our proposed method contains three phases, including *1) initialization, 2) local-global* distillation, and 3) local-global model update. We depict the essential design of FEDLGD in Fig. [2.](#page-4-0) We begin with the initialization of the clients' local virtual data  $\hat{D}^c$  by performing distribution matching (DM) [\(Zhao](#page-17-2) [& Bilen, 2023\)](#page-17-2). Meanwhile, the server will randomly initialize global virtual data  $\tilde{D}^g$  and network parameters *θ g* 0 . Then, we refine our local and global virtual data using our proposed *local-global* distillation strategies. Among the selected iterations, we update  $\theta$ ,  $\tilde{D}^g$ , and  $\tilde{D}^c$  in early training epochs, where the server and clients can update their virtual data to match global information. For the unselected iterations, we train *θ* using with additional regularization loss which penalizes the shift between local and global virtual data. The full algorithm is shown in Algorithm [1.](#page-5-0)

# **3.3 FL with Local-Global Dataset Distillation**

## **3.3.1 Local Data Distillation for Federated Virtual Learning**

First of all, we hope to distill virtual data conditional on class labels to achieve class-balanced virtual datasets. Second, we hope the virtual local data is best suited for the classification task. Last but not least, the process should be efficient due to the limited computational resource locally. To this end, we design Iterative Distribution Matching to fulfill our purpose.

**Iterative distribution matching.** The objective for this part is to gradually improve local distillation quality during FL. Given efficiency is critical for an FL system, we propose to adapt one of the most efficient yet effective data distillation method that leverage distribution matching (DM) in the representation space, DM [\(Zhao & Bilen, 2023\)](#page-17-2), in an iterative updating form to be integrated with FL. To this end, we split <span id="page-5-0"></span>**Algorithm 1** Federated Virtual Learning with Local-global Distillation

**Require:** *f θ* : Model, *ψ θ* : Feature extractor, *θ*: Model parameters, *D*˜: Virtual data, *D*: Original data, L: Losses, *G*: Gradients. 1: 2: **Distillation Functions:** 3:  $\tilde{D}^c \leftarrow \text{DM}(D^c, f^{\theta})$ ) *▷* Distribution Matching 4:  $\tilde{D}_{\text{t}}^{\text{c}} \leftarrow \text{IterativeDM}(\tilde{D}_{\text{t-1}}^{\text{c}}, f_{\text{t}}^{\theta})$ <br>5:  $\tilde{D}_{\text{t+1}}^{\text{g}} \leftarrow \text{FederatedGM}(\tilde{D}_{\text{t}}^{\text{g}}, G_{\text{t}}^{\text{g}})$ ) *▷* Iterative Distribution Matching ) *▷* Federated Gradient Matching 6: 7: **Initialization:** 8:  $\tilde{D}_0^c \leftarrow \text{DM}(D_{\text{rand}}^c, f_{\text{rad}}^{\theta})$ rand) *▷* Distilled local data for virtual FL training 9: 10: **FedLGD Pipeline:** 11: **for**  $t = 1, ..., T$  **do** 12: **Clients:** 13: **for** each selected Client **do** 14: **if**  $t \in \tau$  **then**  $\triangleright$  Local-global distillation 15:  $\tilde{D}_{t}^{c} \leftarrow \text{IterativeDM}(\tilde{D}_{t-1}^{c}, f_{t}^{\theta})$ 16:  $G_t^c \leftarrow \nabla_{\theta} \mathcal{L}_{CE}(\tilde{D}_t^c, f_t^{\theta})$ 17: **else** 18:  $\tilde{D}_{\text{t}}^{\text{c}} \leftarrow \tilde{D}_{\text{t-1}}^{\text{c}}$ <br>19:  $G_{\text{t}}^{\text{c}} \leftarrow \nabla_{\theta} \left[ \mathcal{L}_{\text{CE}}(\tilde{D}_{\text{t}}^{\text{c}}, \tilde{G}_{\text{t}}^{\text{c}}; f_{\text{t}}^{\theta}) + \lambda \mathcal{L}_{\text{CON}}(\tilde{D}_{\text{t}}^{\text{g}}, \tilde{D}_{\text{t}}^{\text{c}}; \psi_{\text{t}}^{\theta}) \right]$ 20: **end if** 21: Uploads  $G_t^c$  to Server 22: **end for** 23: **Server:** 24:  $G_t^g \leftarrow \text{Aggregate}(G_t^1, ..., G_t^c)$ 25: **if**  $t \in \tau$  **then**  $\triangleright$  Local-global distillation 26:  $\tilde{D}_{t+1}^g \leftarrow \text{FederatedGM}(\tilde{D}_t^g, G_t^g)$ 27: Send  $\tilde{D}_{t+1}^{\text{g}}$  to Clients 28: **end if** 29: *f*  $\theta_{t+1}^{\theta} \leftarrow \text{ModelUpdate}(G_t^{\text{g}}, f_t^{\theta})$ 30: Send  $f_{t+1}^{\theta}$  to Clients 31: **end for**

a model into two parts, feature extractor  $\psi$  and classification head h, and the whole classification model is defined as  $f^{\theta} = h \circ \psi$ . Given a feature extractor  $\psi : \mathbb{R}^{d} \to \mathbb{R}^{d'}$ , we want to generate  $\tilde{D}^{c}$  so that  $P_{\psi}(D^{c}) \approx P_{\psi}(D^{c})$  where  $P_{\psi}$  is the distribution in feature space. To distill local data during FL efficiently that best fits our task, we intend to use the up-to-date global feature extractor as our kernel function to distill virtual data with global information. Since we can't obtain ground truth distribution of local data, we utilize empirical maximum mean discrepancy (MMD) [\(Gretton et al., 2012\)](#page-14-5) as our loss function for local virtual distillation:

$$
\mathcal{L}_{\text{MMD}} = \sum_{k}^{K} ||\frac{1}{|D_{k}^{c}|} \sum_{i=1}^{|D_{k}^{c}|} \psi^{t}(x_{i}) - \frac{1}{|\tilde{D}_{k}^{c,t}|} \sum_{j=1}^{|\tilde{D}_{k}^{c,t}|} \psi^{t}(\tilde{x}_{j}^{t})||^{2}, \tag{1}
$$

where  $\psi^t$  and  $\tilde{D}^{c,t}$  are the server feature extractor and local virtual data from the latest global iteration *t*.  $x_i$  and  $\tilde{x}_j^t$  are the data sampled from  $D_k^c$  and  $\tilde{D}_k^{c,t}$ , respectively. *K* is the total number of classes, and we sum over MMD loss for each class  $k \in [K]$ . Thus, we obtain updated local virtual data for each FL round.

Although such an efficient distillation strategy is inspired by DM, we highlight the key difference that DM uses randomly initialized model to extract features, whereas we use trained global feature extractor, as the *iterative updating* on the clients' data using the up-to-date network parameters can generate better taskspecific local virtual data. Our intuition comes from the recent success of the empirical neural tangent kernel for data distribution learning and matching [\(Mohamadi & Sutherland, 2022;](#page-15-9) [Franceschi et al., 2022\)](#page-14-13). Especially, the feature extractor of the model trained with FEDLGD could obtain feature information from other clients, which further harmonizes the domain shift among clients. We apply DM to the baseline FL methods and demonstrate the effectiveness of our proposed iterative strategy in Sec. [5.](#page-8-0) During distilling global information, FedLGD only requires a few hundreds steps for, which is computationally efficient.

**Harmonizing local heterogeneity with global anchors.** Data collected in different sites may have different distributions due to different collecting protocols and populations, which degrades the performance of FL. Even more concerning, we find that the issue of data heterogeneity among clients is exacerbated when training with distilled local virtual data in FL (see Fig. [1\)](#page-1-0).To address this, we propose adding a regularization term in the feature space to the total loss function during local model updates, inspired by [\(Tang et al.,](#page-16-4) [2022\)](#page-16-4).

$$
\mathcal{L}_{\text{total}} = \mathcal{L}_{\text{CE}}(\tilde{D}^g, \tilde{D}^c; \theta) + \lambda \mathcal{L}_{\text{Con}}(\tilde{D}^g, \tilde{D}^c; \psi), \tag{2}
$$

<span id="page-6-1"></span><span id="page-6-0"></span>
$$
\mathcal{L}_{\text{CE}} = \frac{1}{|\tilde{D}|} \sum_{x, y \in \tilde{D}} -\sum_{k}^{K} y_k \log(\hat{y}_k), \hat{y} = f(x; \theta),\tag{3}
$$

<span id="page-6-3"></span>
$$
\mathcal{L}_{\text{Con}} = \sum_{j \in B} -\frac{1}{|B_{\backslash j}^{y_j}|} \sum_{x_p \in B_{\backslash j}^{y_j}} \log \frac{e^{(\psi_i(x_j) \cdot \psi_i(x_p)/\tau_{temp})}}{\sum_{x_a \in B_{\backslash j}} e^{(\psi_i(x_j) \cdot \psi_i(x_a)/\tau_{temp})}}.
$$
(4)

 $\mathcal{L}_{\text{CE}}$  is the cross-entropy measured on the virtual data  $\tilde{D} = \{\tilde{D}^c, \tilde{D}^g\}$  and K is the number of classes.  $\mathcal{L}_{\text{Con}}$ is the supervised contrastive loss [\(Khosla et al., 2020\)](#page-15-10) for decreasing the feature distances between data from the same class while increasing the feature distances for those from different classes.  $B_{\setminus j}$  represents a batch containing both  $\tilde{D}^c$  and  $\tilde{D}^g$  but without data *j*,  $B^{y_j}_{\backslash i}$  $\bigvee_{j}^{y_j}$  is a subset of  $B_{\setminus j}$  only with samples belonging to class  $y_j$ , and  $\tau_{temp}$  is a scalar temperature parameter. In such a way, global virtual data can be served for calibration and groups the features of same classes together. At this point, a critical problem arises: *What global virtual data shall we use?*

### **3.3.2 Global Data Distillation for Heterogeneity Harmonization**

We claim a 'good' global virtual data should be representative of the global data distributions. Therefore, we propose to leverage local clients' averaged gradients to distill global virtual data, and this process can be naturally incorporated into FL pipeline. We term this global data distillation method as *Federated Gradient Matching*.

**Federated Gradient Matching.** The concept of gradient-based dataset distillation is to minimize the distance between gradients from model parameters trained by original data and virtual data. It is usually considered as a learning-to-learn problem because the procedure consists of model updates and virtual data updates. Zhao *et al.* [\(Zhao et al., 2021\)](#page-17-0) studies gradient matching in the centralized setting via bi-level optimization that iteratively optimizes the virtual data and model parameters. However, their implementation is not appropriate in our context because there are two fundamental differences in our settings: 1) for model updating, the virtual dataset is on the server side and will not directly optimize the targeted task; 2) for virtual data update, the 'optimal' model comes from the optimized local model aggregation. We argue that these two steps can naturally be embedded in local model updating and global virtual data distillation from the aggregated local gradients. First, we utilize the distance loss L*Dist* [\(Zhao](#page-17-0) [et al., 2021\)](#page-17-0) for gradient matching:

<span id="page-6-2"></span>
$$
\mathcal{L}_{Dist} = Dist(\bigtriangledown_{\theta} \mathcal{L}_{CE}^{\tilde{D}^g}(\theta), \overline{\bigtriangledown_{\theta} \mathcal{L}}_{CE}^{\tilde{D}^c}(\theta)),\tag{5}
$$

where  $\tilde{D}^c$  and  $\tilde{D}^g$  denote local and global virtual data, and  $\overline{\nabla_{\theta} \mathcal{L}}_{CE}^{\tilde{D}^c}$  is the average client gradient. The  $Dist(S, T)$  is defined as

$$
Dist(S, T) = \sum_{l=1}^{L} \sum_{i=1}^{d_l} \left(1 - \frac{S_i^l \cdot T_i^l}{||S_i^l|| \, ||T_i^l||}\right) \tag{6}
$$

where L is the number of layers,  $S_i^l$  and  $T_i^l$  are flattened vectors of gradients corresponding to each output node *i* from layer *l*, and *d<sub>l</sub>* is the layer output dimension. Then, our proposed federated gradient matching optimize as follows:

$$
\min_{D^g} \mathcal{L}_{Dist}(\theta) \quad \text{subject to} \quad \theta = \frac{1}{N} \sum_{i}^{N} {\theta^{c_i}}^*,
$$

where  $\theta^{c_i^*} = \arg \min_{\theta} \mathcal{L}_i(\tilde{D}^c)$  is the optimal local model weights of client *i*. By doing federated gradient matching, we gradually distill global virtual data that captures local model information. It is worth noting that we do not need to perform this step for every FL communication round, instead, we find that only selecting a few rounds in the early stage of FL is sufficient to synthesize useful global virtual data, which shares similar insights as reported in [\(Feng et al., 2023\)](#page-14-14). We provide theoretical analysis to justify the effectiveness of our novel federated gradient matching in lowering the statistical margin in Appendix [4.](#page-7-0)

<span id="page-7-0"></span>

# **4 Theoretical Analysis**

In this section, we show theoretical insights on FEDLGD. Denote the distribution of global virtual data as  $P_g$  and the distribution of client local virtual data as  $P_c$ . In providing theoretical justification for the efficacy of FedLGD, we can adopt a similar analysis approach as demonstrated in Theorem 3.2 of VHL [\(Tang et al.,](#page-16-4) [2022\)](#page-16-4), where the relationship between generalization performance and domain misalignment for classification tasks is studied by considering *maximizing* the statistic margin (SM) [\(Koltchinskii & Panchenko, 2002\)](#page-15-11).

To assess the generalization performance of f with respect to the distribution  $\mathcal{P}(x, y)$ , we define the SM of FEDLGD as follows:

$$
\mathbb{E}_{f=\text{FEDLGD}(\mathcal{P}_g(x,y))} SM_m(f, \mathcal{P}(x,y)),\tag{7}
$$

where *m* is a distance metric, and  $f = \text{FEDLGD}(\mathcal{P}_q(x, y))$  means that model f is optimized using FEDLGD with minimizing Eq. [3.](#page-6-0) Similar to Theorem A.2 of [\(Tang et al., 2022\)](#page-16-4), we have the lower bound

<span id="page-7-1"></span>**Lemma 1 (Lower bound of FedLGD's statistic margin)** Let  $f = \phi \circ \rho$  be a neural network decompose *of a feature extractor*  $\phi$  *and a classifier*  $\rho$ *. The lower bound of FEDLGD's SM is* 

 $\mathbb{E}_{\rho \leftarrow \mathcal{P}_g} SM_m(\rho, \mathcal{P}) \geq \mathbb{E}_{\rho \leftarrow \mathcal{P}_g} SM_m(\rho, \tilde{D}) - \left| \mathbb{E}_{\rho \leftarrow \mathcal{P}_g} \left[ SM_m(\rho, \mathcal{P}_g) - SM_m(\rho, \tilde{D}) \right] \right| - \mathbb{E}_y d \left( \mathcal{P}_c(\phi \mid y), \mathcal{P}_g(\phi \mid y) \right).$ 

**Proof 1** *Following proof in Theorem A.2 of [\(Tang et al., 2022\)](#page-16-4), the statistical margin is decomposed as*

$$
\mathbb{E}_{\rho \leftarrow \mathcal{P}_g} SM_m(\rho, \mathcal{P})
$$

$$
\geq \mathbb{E}_{\rho \leftarrow \mathcal{P}_g} SM_m(\rho, \tilde{D}) - \left| \mathbb{E}_{\rho \leftarrow \mathcal{P}_g} \left[ SM_m(\rho, \mathcal{P}_g) - SM_m(\rho, \tilde{D}) \right] \right| - \left| \mathbb{E}_{\rho \leftarrow \mathcal{P}_g} \left[ SM_m(\rho, \mathcal{P}) - SM_m(\rho, \mathcal{P}_g) \right] \right|
$$

$$
\geq \mathbb{E}_{\rho \leftarrow \mathcal{P}_g} SM_m(\rho, \tilde{D}) - \left| \mathbb{E}_{\rho \leftarrow \mathcal{P}_g} \left[ SM_m(\rho, \mathcal{P}_g) - SM_m(\rho, \tilde{D}) \right] \right| - \mathbb{E}_y d \left( \mathcal{P}(\phi \mid y), \mathcal{P}_g(\phi \mid y) \right)
$$

Another component in our analysis is building the connection between our used gradient matching strategy and the distribution match term in the bound.

<span id="page-7-2"></span>**Lemma 2 (Proposition 2 of [\(Yu et al., 2023\)](#page-16-14))** *First-order distribution matching objective is approximately equal to gradient matching of each class for kernel ridge regression models following a random feature extractor.*

**Theorem 1** *Due to the complexity of data distillation steps, without loss of generality, we consider kernel ridge regression models with a random feature extractor. Minimizing total loss of FEDLGD (Eq. [2\)](#page-6-1)* for *harmonizing local heterogeneity with global anchors elicits a model with bounded statistic margin (i.e.,the upper bound of the SM bound in Theorem [1\)](#page-7-1).*

**Proof 2** *The first and second term can be bounded by maximizing SM of local virtual training data and global virtual data. The large SM of global virtual data distribution*  $\mathcal{P}_q(x, y)$  *is encouraged by minimizing*  $\overline{c}$ *cross-entropy*  $L_{CE}(\tilde{D}^g, y)$  *in our objective function Eq. [3.](#page-6-0)* 

| <b>DIGITS</b> |             |      | <b>MNIST</b> | <b>SVHN</b> |      | <b>USPS</b> |      |      | SynthDigits |      | MNIST-M |      | Average |
|---------------|-------------|------|--------------|-------------|------|-------------|------|------|-------------|------|---------|------|---------|
| <b>IPC</b>    |             | 10   | 50           | 10          | 50   | 10          | 50   | 10   | 50          | 10   | 50      | 10   | 50      |
| FedAvg        | R           | 73.0 | 92.5         | 20.5        | 48.9 | 83.0        | 89.7 | 13.6 | 28.0        | 37.8 | 72.3    | 45.6 | 66.3    |
|               | С           | 94.0 | 96.1         | 65.9        | 71.7 | 91.0        | 92.9 | 55.5 | 69.1        | 73.2 | 83.3    | 75.9 | 82.6    |
| FedProx       | $\mathbf R$ | 72.6 | 92.5         | 19.7        | 48.4 | 81.5        | 90.1 | 13.2 | 27.9        | 37.3 | 67.9    | 44.8 | 65.3    |
|               | С           | 93.9 | 96.1         | 66.0        | 71.5 | 90.9        | 92.9 | 55.4 | 69.0        | 73.7 | 83.3    | 76.0 | 82.5    |
| FedNova       | $\mathbf R$ | 75.5 | 92.3         | 17.3        | 50.6 | 80.3        | 90.1 | 11.4 | 30.5        | 38.3 | 67.9    | 44.6 | 66.3    |
|               | C           | 94.2 | 96.2         | 65.5        | 73.1 | 90.6        | 93.0 | 56.2 | 69.1        | 74.6 | 83.7    | 76.2 | 83.0    |
| Scaffold      | $\mathbf R$ | 75.8 | 93.4         | 16.4        | 53.8 | 79.3        | 91.3 | 11.2 | 34.2        | 38.3 | 70.8    | 44.2 | 68.7    |
|               | С           | 94.1 | 96.3         | 64.9        | 73.3 | 90.6        | 93.4 | 56.0 | 70.1        | 74.6 | 84.7    | 76.0 | 83.6    |
| <b>MOON</b>   | $\mathbf R$ | 15.5 | 80.4         | 15.9        | 14.2 | 25.0        | 82.4 | 10.0 | 11.5        | 11.0 | 35.4    | 15.5 | 44.8    |
|               | С           | 85.0 | 95.5         | 49.2        | 70.5 | 83.4        | 92.0 | 31.5 | 67.2        | 56.9 | 82.3    | 61.2 | 81.5    |
| FedProto      | $\mathbf R$ | 13.5 | 56.7         | 9.3         | 7.8  | 39.6        | 79.7 | 10.0 | 10.6        | 10.0 | 11.2    | 16.5 | 33.2    |
|               | C           | 91.9 | 96.8         | 52.7        | 73.9 | 93.3        | 96.6 | 27.2 | 52.8        | 69.0 | 84.3    | 668  | 80.9    |
| VHL           | $\mathbf R$ | 87.8 | 95.9         | 29.5        | 67.0 | 88.0        | 93.5 | 18.2 | 60.7        | 52.2 | 85.7    | 55.1 | 80.5    |
|               | С           | 95.0 | 96.9         | 68.6        | 75.2 | 92.2        | 94.4 | 60.7 | 72.3        | 76.1 | 83.7    | 78.5 | 84.5    |
| FEDLGD        | $\mathbf R$ | 92.9 | 96.7         | 46.9        | 73.3 | 89.1        | 93.9 | 27.9 | 72.9        | 70.8 | 85.2    | 65.5 | 84.4    |
|               | С           | 95.8 | 97.1         | 68.2        | 77.3 | 92.4        | 94.6 | 67.4 | 78.5        | 79.4 | 86.1    | 80.6 | 86.7    |

<span id="page-8-1"></span>Table 1: Test accuracy for DIGITS under different images per class (IPC) and model architectures. R and C stand for ResNet18 and ConvNet, respectively, and we set IPC to 10 and 50. 'Average' is the unweighted test accuracy average of all the clients. The best results are marked in **bold**.

*The third term represents the discrepancy of distributions of virtual and real data. We denote this term as*  $\mathcal{D}^{\mathcal{P}_c}_{\phi|y}(\mathcal{P}_g) = \mathbb{E}_y d\left(\mathcal{P}_c(\phi \mid y), \mathcal{P}_g(\phi \mid y)\right)$  and aim to show that  $\mathcal{D}^{\mathcal{P}_c}_{\phi|y}(\mathcal{P}_g)$  can achieve small upper bound under *proper assumptions.*

*Based on Lemma [2,](#page-7-2) the first-order distribution matching objective*  $\mathcal{D}^{\mathcal{P}_c}_{\phi|y}(\mathcal{P}_g)$  *is approximately equal to gradient matching of each class, as shown in objective* L*Dist (Eq. [5\)](#page-6-2). Namely, minimizing gradient matching objective*  $\mathcal{L}_{Dist}$  in FEDLGD implies minimizing  $\mathcal{D}^{\mathcal{P}_c}_{\phi|y}(\mathcal{P}_g)$  in the setting. Hence, using gradient matching generated *global virtual data elicits the model's SM a tight lower bound.*

**Remark 1** *The key distinction between* FedLGD *and VHL primarily lies in the final term, which is exactly a distribution matching objective. It is important to note that in VHL, the global virtual data is generated from an un-pretrained StyleGAN, originating from various Gaussian distributions, which we denote as* P*g. The VHL paper only provided a lower bound for*  $\mathcal{D}^{\mathcal{P}_c}_{\phi|y}(\mathcal{P}_g)$  *but did not show how it is upper bounded. However, for the purpose of maximizing SM to achieve strong generalization, we want to show SM has a tight lower bound. Therefore, upper bounded the last term is desired. In contrast, our approach employs the* gradient matching *strategy to synthesize the global virtual data. To prove our performance improvement, we can show that* FEDLGD could achieve a tight lower bound for SM.

<span id="page-8-0"></span>

# **5 Experiment**

To evaluate FEDLGD, we consider the FL setting in which clients obtain data from different domains with the same target task. Specifically, we compare with multiple baselines on **benchmark datasets** DIGITS, where each client has data from completely different open-sourced datasets. The experiment aims to show that FedLGD can effectively mitigate large domain shifts. Additionally, we evaluate the performance of FedLGD on another **large benchmark dataset**, CIFAR10C [\(Hendrycks & Dietterich, 2019\)](#page-14-15), which collects data with different corruptions yielding data distribution shift and contains a large number of clients, so that we can investigate varied client sampling in FL. The experiment aims to show FEDLGD's feasibility on large-scale FL environments. We also validate the performance under **real medical datasets**, RETINA.

<span id="page-9-1"></span>

| CIFAR10C     |     | FedAvg |              | FedProx                    |                      | FedNova |    | Scaffold                    |    | <b>MOON</b> |      | FedProto |      | <b>VHL</b>         |     | FEDLGD                                                                                                                                         |         |
|--------------|-----|--------|--------------|----------------------------|----------------------|---------|----|-----------------------------|----|-------------|------|----------|------|--------------------|-----|------------------------------------------------------------------------------------------------------------------------------------------------|---------|
| IPC          |     |        | $50^{\circ}$ | 10                         | -50                  | 10      | 50 | 10                          | 50 | 10          | 50   | 10       | -50  | <sup>10</sup>      | -50 | -10                                                                                                                                            | -50     |
|              | 0.2 | 27.0   |              |                            | $44.9$   27.0   44.9 |         |    | $26.7$   34.1   27.0   44.9 |    | 20.5   31.3 |      | 14.4     |      | 26.3   21.8   45.0 |     | 32.9                                                                                                                                           | $-46.8$ |
| Client ratio |     |        |              | $0.5$   29.8   51.4   29.8 | $\vert$ 51.4 $\vert$ | 29.6    |    | $45.9$ 30.6 51.6            |    | 23.8        | 43.2 | 16.4     | 36.1 |                    |     | $\mid$ 29.3   51.7   39.5   52.8                                                                                                               |         |
|              |     | 33.0   |              |                            |                      |         |    |                             |    |             |      |          |      |                    |     | $54.9$   $33.0$   $54.9$   $30.0$   $53.2$   $33.8$   $54.5$   $26.4$   $51.6$   $20.3$   $40.4$   $34.4$   $55.2$   <b>47.6</b>   <b>57.4</b> |         |

| Table 2: Averaged test accuracy for CIFAR10C with ConvNet. |  |  |  |  |  |
|------------------------------------------------------------|--|--|--|--|--|
|------------------------------------------------------------|--|--|--|--|--|

## **5.1 Training and Evaluation Setup**

**Model architecture.** We adapt ResNet18 [\(He et al., 2016\)](#page-14-16) and ConvNet [\(Zhao et al., 2021\)](#page-17-0) (detailed in Appendix [C.4\)](#page-22-0) in our study. To achieve the optimal performance, we apply the same architecture to perform both the local distillation task and the classification task, as suggested in [\(Zhao et al., 2021\)](#page-17-0).

**Comparison methods.** We compare the performance of downstream classification tasks using state-of-theart heterogeneous FL algorithms, FedAvg [\(McMahan et al., 2017\)](#page-15-0), FedProx [\(Li et al., 2020\)](#page-15-7), FedNova [\(Wang](#page-16-9) [et al., 2020\)](#page-16-9), Scaffold [\(Karimireddy et al., 2020\)](#page-14-11), MOON [\(Li et al., 2021\)](#page-15-8), FedProto [\(Tan et al., 2022\)](#page-16-12), and VHL [\(Tang et al., 2022\)](#page-16-4). We use local virtual data from our initialization stage for FL methods other than ours and perform classification on client's testing set and report the test accuracies.

**FL training setup.** We use the SGD optimizer to update local models. If not specified, our default setting for learning rate is  $10^{-2}$ , local model update epochs is 1, total update rounds is 100, the batch size for local training is 32, and the number of virtual data update iterations  $(|\tau|)$  is 10. The numbers of default virtual data distillation steps for clients and server are set to 100 and 500, respectively. Since we only have a few clients for DIGITS, we will select all the clients for each iteration, while the client selection criteria for CIFAR10C experiments will be specified in Sec. [5.3.](#page-10-0)

**Proper Initialization for Distillation.** For privacy concerns and model performance, we initialize local virtual data using local statistics for local data distillation. Specifically, each client calculates the statistics of its own data for each class, denoted as  $\mu_i^c, \sigma_i^c$ , and then initializes the distillation images per class,  $x \sim \mathcal{N}(\mu_i^c, \sigma_i^c)$ , where *c* and *i* represent each client and categorical label. For privacy consideration, we use random noise as initialization for global virtual data distillation. The comparison between different initialization strategies can be found in Appendix [B.](#page-18-0)

<span id="page-9-0"></span>

# **5.2 DIGITS Experiment**

**Datasets.** We use the following datasets for our benchmark experiments:  $\text{DIST} S = \{ \text{MNIST} \mid \text{LeCun} \}$ [et al., 1998\)](#page-15-12), SVHN [\(Netzer et al., 2011\)](#page-15-13), USPS [\(Hull, 1994\)](#page-14-3), SynthDigits [\(Ganin & Lempitsky, 2015\)](#page-14-4), MNIST-M [\(Ganin & Lempitsky, 2015\)](#page-14-4)}. Each dataset in DIGITS contains handwritten, real street and synthetic digit images of  $0, 1, \dots, 9$ . As a result, we have 5 clients in the experiments.

**Comparison under various conditions.** To validate the effectiveness of FEDLGD, we first compare it with the alternative FL methods varying on two important factors: Image-per-class (IPC) and different deep neural network architectures (arch). We use IPC  $\in \{10, 50\}$  and arch  $\in \{$  ResNet18(R), ConvNet(C)} to examine the performance of SOTA models and FEDLGD using distilled DIGITS. Note that we fix IPC  $=$ 10 for global virtual data and vary IPC for local virtual data. Tab. [1](#page-8-1) shows the test accuracies of DIGITS experiments. One can observe that for each  $FL$  algorithm, Conv $Net(C)$  always has the best performance under all IPCs. The observation is consistent with [\(Zhao & Bilen, 2023\)](#page-17-2) as more complex architectures may cause over-fitting to virtual data. It is also shown that using  $IPC = 50$  always outperforms  $IPC = 10$  as expected since more virtual data can captures more real data distribution and thus facilitates model training. Overall, FEDLGD outperforms other SOTA methods, where on average accuracy, FEDLGD increases the best test accuracy results among the baseline methods of  $2.1\%$  (IPC =10, arch = C),  $10.4\%$  (IPC =10, arch = R),  $2.2\%$  (IPC = 50, arch = C) and  $3.9\%$  (IPC = 50, arch = R). VHL is the closest strategy to FEDLGD and achieves the best performance among the baseline methods, indicating that the feature alignment solutions are promising for handling heterogeneity in federated virtual learning. However, the worse performance may result from the differences in synthesizing global virtual data. VHL uses untrained StyleGAN [\(Karras et al.,](#page-15-14) [2019\)](#page-15-14) to generate global virtual data without further updating. On the contrary, we gradually update our global virtual data during FL training.

<span id="page-10-0"></span>

## **5.3 CIFAR10C Experiment**

Datasets. We conduct large-scale FL experiments on CIFAR[1](#page-10-1)0C<sup>1</sup>, where, like previous studies [\(Li et al.,](#page-15-8) [2021\)](#page-15-8), we apply Dirichlet distribution with  $\alpha = 2$  to generate 3 partitions on each distorted Cifar10-C [\(Hendrycks & Dietterich, 2019\)](#page-14-15), resulting in 57 *domain and label heterogeneous* non-IID clients. In addition, we randomly sample a fraction of clients with ratio  $= 0.2, 0.5$ , and 1 for each FL round.

**Comparison under different client sampling ratios.** The objective of the experiment is to test FedLGD under popular FL questions: class imbalance, large number of clients, different client sample ratios, and domain and label heterogeneity. One benefit of federated virtual learning is that we can easily handle class imbalance by distilling the same number (IPC) of virtual data. We will vary IPC and fix the model architecture to ConvNet since it is validated to yield better performance in virtual training. One can observe from Tab. [2](#page-9-1) that FEDLGD consistently achieves the best performance under different IPC and client sampling ratios. We would like to point out that when IPC=10, the performance boosts are significant, which indicates that FEDLGD is well-suited for FL when there is a large group of clients with limited number of local virtual data.

## **5.4 RETINA Experiment**

*comatous* and *Normal*. Example images and distributions can be found in Appendix [C.3.](#page-22-1) **Comparison with baselines.** The results for RETINA experiments are shown in Table [3,](#page-10-2) where D, A, Ri, Re represent Drishti, Acrima, Rim, and Refuge datasets. We only set IPC=10 for this experiment as clients in RETINA contain much fewer data points. The learning rate is set to  $10^{-3}$ . FedLGD has the best performance compared to the other methods w.r.t the unweighted averaged accuracy (Avg) among clients. To be precise, FEDLGD increases unweighted averaged test accuracy for 3.1%(versus the best baseline) on ConvNet. The same accuracy for different methods is due to the limited number of testing samples. We conjecture the reason why VHL [\(Tang et al., 2022\)](#page-16-4) has lower performance improvement in RETINA experiments is that this dataset is in higher dimensional and clinical diagnosis evidence on fine-grained details, *e.g.*,

**Dataset.** For medical dataset, we use the retina image datasets, RETINA = {Drishti [\(Sivaswamy](#page-15-15) [et al., 2014\)](#page-15-15), Acrima [\(Diaz-Pinto et al., 2019\)](#page-14-17), Rim [\(Batista et al., 2020\)](#page-13-5), Refuge [\(Orlando](#page-15-16) [et al., 2020\)](#page-15-16)}, where each dataset contains retina images from different stations with image size 96 × 96, thus forming four clients in FL. We perform binary classification to identify *Glau-*

<span id="page-10-2"></span>Table 3: Test accuracy for RETINA experiments under different model architectures and IPC=10. We have 4 clients:  $Drishti(D), Acrima(A), Rim(Ri), and Refuge(Re), respectively.$ tively. We also show the average test accuracy (Avg). The same accuracy for different methods is due to the limited number of testing samples.

| RETINA        |                   | D    | A    | Ri   | Re   | Avg  |
|---------------|-------------------|------|------|------|------|------|
| FedAvg        | C                 | 69.4 | 84.0 | 88.0 | 86.5 | 82.0 |
| FedProx       | $\mathcal{C}$     | 68.4 | 84.0 | 88.0 | 86.5 | 81.7 |
| FedNova       | C                 | 68.4 | 84.0 | 88.0 | 86.5 | 81.7 |
| Scaffold      | $\mathcal{C}_{1}$ | 68.4 | 84.0 | 88.0 | 86.5 | 81.7 |
| <b>MOON</b>   | $\mathcal{C}_{1}$ | 57.9 | 72.0 | 76.0 | 85.0 | 72.7 |
| FedProto      | C                 | 73.6 | 86.0 | 54.0 | 77.5 | 72.8 |
| VHL           | $\mathcal{C}$     | 68.4 | 78.0 | 81.0 | 87.0 | 78.6 |
| <b>FEDLGD</b> | C                 | 78.9 | 86.0 | 88.0 | 87.5 | 85.1 |

cup-to-disc ratio and disc rim integrity [\(Schuster et al., 2020\)](#page-15-17). Therefore, it is difficult for untrained Style-GAN [\(Karras et al., 2019\)](#page-15-14) to serve as anchor for this kind of larger images.

## **5.5 Ablation studies for FedLGD**

The success of FEDLGD relies on the novel design of local-global data distillation, where the selection of regularization loss and the number of iterations for data distillation play a key role. Recall that among the total FL training epochs, we perform local-global distillation on the selected *τ iterations*, where the server and clients will perform data updating for some pre-defined *steps*. Thus, we will study the choice of regularization loss and its weighting (*λ*) in the total loss function, as well as the effect of *iterations* and *steps*. By default, we use ConvNet, global IPC=10, local IPC=50, |*τ* |=10, and (local, global) update *steps*=(100,

<span id="page-10-1"></span><sup>&</sup>lt;sup>1</sup>Cifar10-C is a collection of augmented Cifar10 that applies 19 different corruptions, resulting in  $6k \times 19 = 114k$  data points.

<span id="page-11-2"></span>Image /page/11/Figure/1 description: The image displays four plots related to machine learning model performance. Plot (a) shows accuracy versus regularization loss (λ) for DIGITS, CIFAR10C, Contrastive(ours), and MMD, with DIGITS accuracy around 84%, CIFAR10C around 58%, Contrastive(ours) decreasing from 58% to 18%, and MMD decreasing from 58% to 4%. Plot (b) shows the change in averaged accuracy (%) versus |τ| for DIGITS, CIFAR10C, and FLOPs, indicating a general increase in accuracy with |τ| and a corresponding increase in computation cost. Plots (c) and (d) are 3D surface plots showing accuracy for DIGITS and CIFAR10C respectively, varying with 'Local' and 'Global' steps. For DIGITS, accuracy ranges from 85% to 90%, and for CIFAR10C, accuracy ranges from 56% to 58%.

<span id="page-11-4"></span>Figure 3: (a) Comparison between different regularization losses and their weightings( $\lambda$ ). One can observe that  $\mathcal{L}_{\text{Con}}$  gives us better and more stable performance with different coefficient choices. (b) The solid curves describes the improved accuracy compared to  $|\tau|=0$ , and the dashed curve indicates the computation cost. The model performance improves with the increasing |*τ* |, which is a trade-off between computation cost and model performance. Vary data updating *steps* for (c) DIGITS and (d) CIFAR10C. FEDLGD yields consistent performance, and the accuracy improves with an increasing number of local and global steps.

<span id="page-11-3"></span><span id="page-11-1"></span><span id="page-11-0"></span>Image /page/11/Figure/3 description: This image displays three scatter plots comparing FedAvg and FedLGD methods. The plots visualize feature distributions, with different colors representing labels 0 through 9, and markers indicating data sources (circles for client0, crosses for client1). Plot (a) FedAvg shows a 'Feature shift' between two clusters of data points, highlighted by red dashed circles and arrows. Plot (b) FedLGD without regularization also indicates a 'Feature shift' with similar highlighting. Plot (c) FedLGD shows 'Aligned feature' with the clusters appearing more consolidated and overlapping, suggesting improved feature alignment.

Figure 4: tSNE plots on feature space for FedAvg, FEDLGD without regularization, and FEDLGD.

500). We also discuss computation cost and privacy, two important factors in FL. Further ablation studies can be found in Appendix [B.](#page-18-0)

**Effect of regularization loss.** FEDLGD uses supervised contrastive loss  $\mathcal{L}_{Con}$  as a regularization term to encourage local and global virtual data embedding into a similar feature space. To demonstrate its effectiveness, we perform ablation studies to replace  $\mathcal{L}_{Con}$  with an alternative distribution similarity measurement, MMD loss, with different  $\lambda$ 's ranging from 0 to 20. Fig. [3a](#page-11-2) shows the average test accuracy. Using  $\mathcal{L}_{\text{Con}}$ gives us better and more stable performance with different  $\lambda$  choices. We select  $\lambda=10$  and 1 for DIGITS and CIFAR10C, respectively. It is worth noting that when  $\lambda = 0$ , FEDLGD can still yield competitive accuracy, which indicates the utility of our local and global virtual data. To explain the effect of our proposed regularization loss on feature representations, we embed the latent features before fully-connected layers to a 2D space using tSNE [\(Van der Maaten & Hinton, 2008\)](#page-16-15) shown in Fig. [4.](#page-11-3) For the model trained with FedAvg (Fig. [4a](#page-11-3)), features from two clients ( $\times$  and  $\circ$ ) are closer to their own distribution regardless of the labels (colors). In Fig. [4b](#page-11-3), we perform virtual FL training but without the regularization term (Eq. [4\)](#page-6-3). Fig. [4c](#page-11-3) shows FEDLGD, and one can observe that data from different clients with the same label are grouped together.

**Analysis of distillation** *iterations*  $(|\tau|)$ . Fig. [3b](#page-11-4) shows the improved averaged test accuracy if we increase the number of distillation iterations with FEDLGD. The base accuracy for DIGITS and CIFAR10C are 85.8 and 55.2 when  $\tau = \emptyset$ . We fix local and global update *steps* to 100 and 500, and the selected iterations  $(\tau)$  are defined as arithmetic sequences with  $d = 5$  (i.e.,  $\tau = \{0, 5, ...\}$ ). One can observe that the model performance improves with the increasing  $|\tau|$ . This is because we obtain better virtual data with more local-global distillation iterations, which is a trade-off between computation cost and model performance.

**Robustness on virtual data update** *steps***.** In Fig. [3c](#page-11-0) and Fig. [3d,](#page-11-1) we vary (local, global) data updating steps. One can observe that FEDLGD yields stable performance (always outperforms baselines), and the accuracy slightly improves with an increasing number of local and global steps.

**Computation cost.** We have shown the increased computation cost caused by increasing the number of selected rounds  $|\tau|$  in Fig. [3b.](#page-11-4) Here, we discuss the overall accumulated computation cost for the 100 total FL training rounds, including both selected and unselected iterations in Fig. [5.](#page-12-0) The computation costs for FedLGD in DIGITS and CIFAR10C are identical since we use IPC=50 for training. For RETINA, since we apply IPC=10, FEDLGD has significant efficiency improvement. Overall, FEDLGD reduces the computation cost on the clients' side by training with virtual data compared to classical FedAvg that train on real datasets.

**Privacy.** We note that FEDLGD uses *pre-existing* information, *i.e.*, shared averaged gradients and global model, to distill virtual data, so there is no extra privacy leakage. Like the standard FL training, FedLGD may be vulnerable to deep privacy attacks, such as membership inference attacks (MIAs) [\(Shokri et al., 2017\)](#page-15-2) and gradient inversion attacks(GIAs) [\(Zhu et al., 2019;](#page-17-6) [Huang et al.,](#page-14-7) [2021\)](#page-14-7). We empirically show FEDLGD can potentially defend both attacks, which is also implied by [\(Xiong et al., 2023;](#page-16-3) [Dong et al., 2022\)](#page-14-0). Preserv-

<span id="page-12-0"></span>Image /page/12/Figure/3 description: This image contains three line graphs side-by-side, labeled (a) DIGITS, (b) CIFAR10C, and (c) RETINA. Each graph plots M FLOPs on the y-axis against FL rounds on the x-axis, ranging from 0 to 100. All three graphs show three lines representing FedAvg (red solid line), FedLGDc (green solid line), and FedLGDs (green dashed line). In graph (a) DIGITS, FedAvg increases linearly from 0 to 25 M FLOPs. FedLGDc increases to about 4 M FLOPs and stays flat. FedLGDs increases linearly to 15 M FLOPs and stays flat. In graph (b) CIFAR10C, FedAvg increases linearly from 0 to 50 M FLOPs. FedLGDc increases to about 4 M FLOPs and stays flat. FedLGDs increases linearly to about 15 M FLOPs and stays flat. In graph (c) RETINA, FedAvg increases linearly from 0 to 175 M FLOPs. FedLGDc increases to about 2 M FLOPs and stays flat. FedLGDs increases linearly to about 15 M FLOPs and stays flat.

Figure 5: FEDLGD reduces the Accumulated computation cost on the clients' side compared to FedAvg.

ing identity-level privacy can be further improved by employing differential privacy [\(Abadi et al., 2016\)](#page-13-6) in dataset distillation, such as applying DPSGD during local data distillation or applying DPSGD on the local gradients, but this goes beyond the main focus of our work.

MIAs [\(Shokri et al., 2017\)](#page-15-2) aims to identify if a given data point belongs to the model's training data. We compare the performance of MIA directly on models trained with original data (FedAvg) and with the synthetic dataset (FEDLGD). If the MIA performance on the original images is worse than the one on FedAvg, we claim that the synthetic data helps with privacy. Here, we implemented the Likelihood Ratio MIA [\(Carlini et al., 2022a\)](#page-13-7), where the gradients are collected for the server model on training and testing data individually. The likelihood of the point belonging to the training set is then obtained using the Gaussian kernel density estimation (Fig. [6\)](#page-12-1). If the ROC curve intersects with the diagonal dashed line (representing a random membership

<span id="page-12-1"></span>Image /page/12/Figure/7 description: The image displays two Receiver Operating Characteristic (ROC) curves side-by-side. The left plot, labeled "(a) MIA on Synth-Digits", shows the performance of "FedLGD" (blue line) and "FedAvg" (orange line) on Synth-Digits data. The right plot, labeled "(b) MIA on MNIST-M", shows the performance of "FedLGD" (blue line) and "FedAvg" (orange line) on MNIST-M data. Both plots have "False Positive Rate" on the x-axis and "True Positive Rate" on the y-axis, ranging from 0.0 to 1.0. In both plots, the orange line representing "FedAvg" is above the blue line representing "FedLGD" for most of the curve, indicating better performance for FedAvg in terms of true positive rate for a given false positive rate.

Figure 6: MIA results on models trained with FedAvg (using original dataset) and FEDLGD (using distilled virtual dataset). If the ROC curve is the same as the diagonal line, it means the membership cannot be inferred.

classifier), it signifies that the approach provides a stronger defense against membership inference compared to the method with a larger area under the ROC curve. FedLGD results in ROC curves that are more closely aligned with the diagonal line, suggesting that attacking membership becomes more challenging.

Using dataset distillation to synthesize virtual data can be shown to mitigate against gradient-based inversion attacks (GIAs) [\(Geiping et al., 2020;](#page-14-6) [Huang](#page-14-7) [et al., 2021\)](#page-14-7). Here, we use Cifar10 [\(Krizhevsky](#page-15-18) [et al., 2009\)](#page-15-18) as an example. We perform local training on a ConvNet from one client in CIFAR10Cand apply gradient inversion attack to reconstruct the raw images. Then, we evaluate the reconstruction quality using perceptual loss (LPIPS) [\(Zhang et al.,](#page-17-7) [2018\)](#page-17-7). As a result, the reconstructed distilled image is visually different from raw images, and it effec-

Image /page/12/Figure/11 description: The image displays two rows of reconstructed Cifar10 images. The top row, labeled (a), shows reconstructed raw Cifar10 images. The bottom row, labeled (b), shows reconstructed distilled Cifar10 images. Each row contains eight small, square images arranged horizontally. The images in the top row appear to be clearer and more recognizable, depicting objects like a cat, a blue bird, and a horse. The images in the bottom row are more blurry and abstract, with less distinct features.

(b) Reconstructed distilled Cifar10 images

Figure 7: GIA on raw and distilled Cifar10 images.

tively alleviates the attack from perceptual perspective, by reducing LPIPS from 0.253 to 0.177. Note that

in FedLGD, the shared global virtual data is synthesized by the *averaged* gradients, which further improves the privacy guarantee.

# **6 Conclusion**

In this paper, we introduce a new approach for FL, called FEDLGD. It utilizes virtual data on both client and server sides to train FL models. We are the first to reveal that FL on distilled local virtual data can increase heterogeneity. To tackle the heterogeneity issue, we seamlessly integrated dataset distillation algorithms into FL pipeline by proposing iterative distribution matching and federated gradient matching to iteratively update local and global virtual data. Then, we apply global virtual regularization to effectively harmonize domain shift. Our experiments on benchmark and real medical datasets show that FEDLGD outperforms current state-of-the-art methods in heterogeneous settings. Furthermore, FEDLGD can be combined with other model-synchronization-based FL approaches to further improve its performance. The potential limitation lies in the additional communication and computation cost in data distillation, but we show that the trade-off is acceptable and can be mitigated by decreasing distillation *iterations* and *steps*. Our future direction includes investigating privacy-preserving data generation and utilizing the synthesized global virtual data for federated continual learning or training personalized models. We believe that this work sheds light on how to effectively mitigate data heterogeneity from a dataset distillation perspective and will inspire future work to enhance FL performance, privacy, and efficiency.

## **Acknowledgement**

This work is supported in part by the Natural Sciences and Engineering Research Council of Canada (NSERC), CIFAR AI Chair Awards, CIFAR AI Catalyst Grant, NVIDIA Hardware Award, UBC Sockeye, and Compute Canada Research Platform.

## **References**

- <span id="page-13-6"></span>Martin Abadi, Andy Chu, Ian Goodfellow, H Brendan McMahan, Ilya Mironov, Kunal Talwar, and Li Zhang. Deep learning with differential privacy. In *Proceedings of the 2016 ACM SIGSAC conference on computer and communications security*, pp. 308–318, 2016.
- <span id="page-13-5"></span>Francisco José Fumero Batista, Tinguaro Diaz-Aleman, Jose Sigut, Silvia Alayon, Rafael Arnay, and Denisse Angel-Pereira. Rim-one dl: A unified retinal image database for assessing glaucoma using deep learning. *Image Analysis & Stereology*, 39(3):161–167, 2020.
- <span id="page-13-7"></span>Nicholas Carlini, Steve Chien, Milad Nasr, Shuang Song, Andreas Terzis, and Florian Tramer. Membership inference attacks from first principles. In *2022 IEEE Symposium on Security and Privacy (SP)*, pp. 1897–1914. IEEE, 2022a.
- <span id="page-13-1"></span>Nicholas Carlini, Vitaly Feldman, and Milad Nasr. No free lunch in" privacy for free: How does dataset condensation help privacy". *arXiv preprint arXiv:2209.14987*, 2022b.
- <span id="page-13-0"></span>George Cazenavette, Tongzhou Wang, Antonio Torralba, Alexei A Efros, and Jun-Yan Zhu. Dataset distillation by matching training trajectories. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 4750–4759, 2022.
- <span id="page-13-2"></span>Hong-You Chen and Wei-Lun Chao. On bridging generic and personalized federated learning for image classification. *arXiv preprint arXiv:2107.00778*, 2021.
- <span id="page-13-3"></span>Huancheng Chen, Haris Vikalo, et al. The best of both worlds: Accurate global and personalized models through federated learning with data-free hyper-knowledge distillation. *arXiv preprint arXiv:2301.08968*, 2023.
- <span id="page-13-4"></span>Sijin Chen, Zhize Li, and Yuejie Chi. Escaping saddle points in heterogeneous federated learning via distributed sgd with communication compression. In *International Conference on Artificial Intelligence and Statistics*, pp. 2701–2709. PMLR, 2024.

- <span id="page-14-17"></span>Andres Diaz-Pinto, Sandra Morales, Valery Naranjo, Thomas Köhler, Jose M Mossi, and Amparo Navea. Cnns for automatic glaucoma assessment using fundus images: an extensive validation. *Biomedical engineering online*, 18(1):1–19, 2019.
- <span id="page-14-0"></span>Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning*, pp. 5378–5396. PMLR, 2022.
- <span id="page-14-8"></span>Ziqing Fan, Jiangchao Yao, Bo Han, Ya Zhang, Yanfeng Wang, et al. Federated learning with bilateral curation for partially class-disjoint data. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-14-14"></span>Yunzhen Feng, Shanmukha Ramakrishna Vedantam, and Julia Kempe. Embarrassingly simple dataset distillation. In *The Twelfth International Conference on Learning Representations*, 2023.
- <span id="page-14-13"></span>Jean-Yves Franceschi, Emmanuel De Bézenac, Ibrahim Ayed, Mickaël Chen, Sylvain Lamprier, and Patrick Gallinari. A neural tangent kernel perspective of gans. In *International Conference on Machine Learning*, pp. 6660–6704. PMLR, 2022.
- <span id="page-14-4"></span>Yaroslav Ganin and Victor Lempitsky. Unsupervised domain adaptation by backpropagation. In *International conference on machine learning*, pp. 1180–1189. PMLR, 2015.
- <span id="page-14-6"></span>Jonas Geiping, Hartmut Bauermeister, Hannah Dröge, and Michael Moeller. Inverting gradients-how easy is it to break privacy in federated learning? *Advances in neural information processing systems*, 33: 16937–16947, 2020.
- <span id="page-14-1"></span>Jack Goetz and Ambuj Tewari. Federated learning via synthetic data. *arXiv preprint arXiv:2008.04489*, 2020.
- <span id="page-14-5"></span>Arthur Gretton, Karsten M Borgwardt, Malte J Rasch, Bernhard Schölkopf, and Alexander Smola. A kernel two-sample test. *The Journal of Machine Learning Research*, 13(1):723–773, 2012.
- <span id="page-14-10"></span>Chaoyang He, Murali Annavaram, and Salman Avestimehr. Group knowledge transfer: Federated learning of large cnns at the edge. *Advances in Neural Information Processing Systems*, 33:14068–14080, 2020.
- <span id="page-14-16"></span>Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 770–778, 2016.
- <span id="page-14-15"></span>Dan Hendrycks and Thomas Dietterich. Benchmarking neural network robustness to common corruptions and perturbations. *arXiv preprint arXiv:1903.12261*, 2019.
- <span id="page-14-9"></span>Tzu-Ming Harry Hsu, Hang Qi, and Matthew Brown. Measuring the effects of non-identical data distribution for federated visual classification. *arXiv preprint arXiv:1909.06335*, 2019.
- <span id="page-14-2"></span>Shengyuan Hu, Jack Goetz, Kshitiz Malik, Hongyuan Zhan, Zhe Liu, and Yue Liu. Fedsynth: Gradient compression via synthetic data in federated learning. *arXiv preprint arXiv:2204.01273*, 2022.
- <span id="page-14-12"></span>Chun-Yin Huang, Kartik Srinivas, Xin Zhang, and Xiaoxiao Li. Overcoming data and model heterogeneities in decentralized federated learning via synthetic anchors. In *Forty-first International Conference on Machine Learning*, 2024.
- <span id="page-14-7"></span>Yangsibo Huang, Samyak Gupta, Zhao Song, Kai Li, and Sanjeev Arora. Evaluating gradient inversion attacks and defenses in federated learning. *Advances in Neural Information Processing Systems*, 34:7232– 7241, 2021.
- <span id="page-14-3"></span>Jonathan J. Hull. A database for handwritten text recognition research. *IEEE Transactions on pattern analysis and machine intelligence*, 16(5):550–554, 1994.
- <span id="page-14-11"></span>Sai Praneeth Karimireddy, Satyen Kale, Mehryar Mohri, Sashank Reddi, Sebastian Stich, and Ananda Theertha Suresh. Scaffold: Stochastic controlled averaging for federated learning. In *International Conference on Machine Learning*, pp. 5132–5143. PMLR, 2020.

- <span id="page-15-14"></span>Tero Karras, Samuli Laine, and Timo Aila. A style-based generator architecture for generative adversarial networks. In *Proceedings of the IEEE/CVF conference on computer vision and pattern recognition*, pp. 4401–4410, 2019.
- <span id="page-15-10"></span>Prannay Khosla, Piotr Teterwak, Chen Wang, Aaron Sarna, Yonglong Tian, Phillip Isola, Aaron Maschinot, Ce Liu, and Dilip Krishnan. Supervised contrastive learning. *Advances in Neural Information Processing Systems*, 33:18661–18673, 2020.
- <span id="page-15-11"></span>Vladimir Koltchinskii and Dmitry Panchenko. Empirical margin distributions and bounding the generalization error of combined classifiers. *The Annals of Statistics*, 30(1):1–50, 2002.
- <span id="page-15-18"></span>Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images. 2009.
- <span id="page-15-12"></span>Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proceedings of the IEEE*, 86(11):2278–2324, 1998.
- <span id="page-15-4"></span>Guang Li, Ren Togo, Takahiro Ogawa, and Miki Haseyama. Dataset distillation for medical dataset sharing. *arXiv preprint arXiv:2209.14603*, 2022.
- <span id="page-15-3"></span>Hongcheng Li, Yucan Zhou, Xiaoyan Gu, Bo Li, and Weiping Wang. Diversified semantic distribution matching for dataset distillation. In *ACM Multimedia 2024*.
- <span id="page-15-8"></span>Qinbin Li, Bingsheng He, and Dawn Song. Model-contrastive federated learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 10713–10722, 2021.
- <span id="page-15-7"></span>Tian Li, Anit Kumar Sahu, Manzil Zaheer, Maziar Sanjabi, Ameet Talwalkar, and Virginia Smith. Federated optimization in heterogeneous networks. *Proceedings of Machine Learning and Systems*, 2:429–450, 2020.
- <span id="page-15-6"></span>Tao Lin, Lingjing Kong, Sebastian U Stich, and Martin Jaggi. Ensemble distillation for robust model fusion in federated learning. *Advances in Neural Information Processing Systems*, 33:2351–2363, 2020.
- <span id="page-15-0"></span>Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas. Communication-efficient learning of deep networks from decentralized data. In *Artificial intelligence and statistics*, pp. 1273–1282. PMLR, 2017.
- <span id="page-15-9"></span>Mohamad Amin Mohamadi and Danica J Sutherland. A fast, well-founded approximation to the empirical neural tangent kernel. *arXiv preprint arXiv:2206.12543*, 2022.
- <span id="page-15-13"></span>Yuval Netzer, Tao Wang, Adam Coates, Alessandro Bissacco, Baolin Wu, Andrew Y Ng, et al. Reading digits in natural images with unsupervised feature learning. In *NIPS workshop on deep learning and unsupervised feature learning*, volume 2011, pp. 4. Granada, 2011.
- <span id="page-15-1"></span>Sergey I Nikolenko. *Synthetic data for deep learning*, volume 174. Springer, 2021.
- <span id="page-15-16"></span>José Ignacio Orlando, Huazhu Fu, João Barbosa Breda, Karel van Keer, Deepti R Bathula, Andrés Diaz-Pinto, Ruogu Fang, Pheng-Ann Heng, Jeyoung Kim, JoonHo Lee, et al. Refuge challenge: A unified framework for evaluating automated methods for glaucoma assessment from fundus photographs. *Medical image analysis*, 59:101570, 2020.
- <span id="page-15-5"></span>Noveen Sachdeva and Julian McAuley. Data distillation: A survey. *arXiv preprint arXiv:2301.04272*, 2023.
- <span id="page-15-17"></span>Alexander K Schuster, Carl Erb, Esther M Hoffmann, Thomas Dietlein, and Norbert Pfeiffer. The diagnosis and treatment of glaucoma. *Deutsches Ärzteblatt International*, 117(13):225, 2020.
- <span id="page-15-2"></span>Reza Shokri, Marco Stronati, Congzheng Song, and Vitaly Shmatikov. Membership inference attacks against machine learning models. In *2017 IEEE symposium on security and privacy (SP)*, pp. 3–18. IEEE, 2017.
- <span id="page-15-15"></span>Jayanthi Sivaswamy, SR Krishnadas, Gopal Datt Joshi, Madhulika Jain, and A Ujjwaft Syed Tabish. Drishtigs: Retinal image dataset for optic nerve head (onh) segmentation. In *2014 IEEE 11th international symposium on biomedical imaging (ISBI)*, pp. 53–56. IEEE, 2014.

- <span id="page-16-8"></span>Zhenyu Sun, Xiaochun Niu, and Ermin Wei. Understanding generalization of federated learning via stability: Heterogeneity matters. In *International Conference on Artificial Intelligence and Statistics*, pp. 676–684. PMLR, 2024.
- <span id="page-16-12"></span>Yue Tan, Guodong Long, Lu Liu, Tianyi Zhou, Qinghua Lu, Jing Jiang, and Chengqi Zhang. Fedproto: Federated prototype learning across heterogeneous clients. In *Proceedings of the AAAI Conference on Artificial Intelligence*, volume 36, pp. 8432–8440, 2022.
- <span id="page-16-4"></span>Zhenheng Tang, Yonggang Zhang, Shaohuai Shi, Xin He, Bo Han, and Xiaowen Chu. Virtual homogeneity learning: Defending against data heterogeneity in federated learning. *arXiv preprint arXiv:2206.02465*, 2022.
- <span id="page-16-15"></span>Laurens Van der Maaten and Geoffrey Hinton. Visualizing data using t-sne. *Journal of machine learning research*, 9(11), 2008.
- <span id="page-16-0"></span>Paul Voigt and Axel Von dem Bussche. The eu general data protection regulation (gdpr). *A Practical Guide, 1st Ed., Cham: Springer International Publishing*, 10(3152676):10–5555, 2017.
- <span id="page-16-9"></span>Jianyu Wang, Qinghua Liu, Hao Liang, Gauri Joshi, and H Vincent Poor. Tackling the objective inconsistency problem in heterogeneous federated optimization. *Advances in neural information processing systems*, 33: 7611–7623, 2020.
- <span id="page-16-2"></span>Tongzhou Wang, Jun-Yan Zhu, Antonio Torralba, and Alexei A Efros. Dataset distillation. *arXiv preprint arXiv:1811.10959*, 2018.
- <span id="page-16-1"></span>Jie Wen, Zhixia Zhang, Yang Lan, Zhihua Cui, Jianghui Cai, and Wensheng Zhang. A survey on federated learning: challenges and applications. *International Journal of Machine Learning and Cybernetics*, 14(2): 513–535, 2023.
- <span id="page-16-11"></span>Zhiyuan Wu, Sheng Sun, Yuwei Wang, Min Liu, Quyang Pan, Junbo Zhang, Zeju Li, and Qingxiang Liu. Exploring the distributed knowledge congruence in proxy-data-free federated distillation. *arXiv preprint arXiv:2204.07028*, 2022.
- <span id="page-16-10"></span>Zhiyuan Wu, Sheng Sun, Yuwei Wang, Min Liu, Quyang Pan, Xuefeng Jiang, and Bo Gao. Fedict: Federated multi-task distillation for multi-access edge computing. *IEEE Transactions on Parallel and Distributed Systems*, 2023.
- <span id="page-16-3"></span>Yuanhao Xiong, Ruochen Wang, Minhao Cheng, Felix Yu, and Cho-Jui Hsieh. Feddm: Iterative distribution matching for communication-efficient federated learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 16323–16332, 2023.
- <span id="page-16-13"></span>Zhiqin Yang, Yonggang Zhang, Yu Zheng, Xinmei Tian, Hao Peng, Tongliang Liu, and Bo Han. Fedfed: Feature distillation against data heterogeneity in federated learning. *Advances in Neural Information Processing Systems*, 36, 2024.
- <span id="page-16-7"></span>Mang Ye, Xiuwen Fang, Bo Du, Pong C Yuen, and Dacheng Tao. Heterogeneous federated learning: Stateof-the-art and research challenges. *ACM Computing Surveys*, 56(3):1–44, 2023a.
- <span id="page-16-5"></span>Rui Ye, Zhenyang Ni, Chenxin Xu, Jianyu Wang, Siheng Chen, and Yonina C Eldar. Fedfm: Anchor-based feature matching for data heterogeneity in federated learning. *IEEE Transactions on Signal Processing*, 2023b.
- <span id="page-16-14"></span>Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. *arXiv preprint arXiv:2301.07014*, 2023.
- <span id="page-16-6"></span>Hansong Zhang, Shikun Li, Fanzhao Lin, Weiping Wang, Zhenxing Qian, and Shiming Ge. Dance: Dual-view distribution alignment for dataset condensation. *arXiv preprint arXiv:2406.01063*, 2024.

- <span id="page-17-5"></span>Lin Zhang, Li Shen, Liang Ding, Dacheng Tao, and Ling-Yu Duan. Fine-tuning global model via data-free knowledge distillation for non-iid federated learning. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pp. 10174–10183, 2022.
- <span id="page-17-7"></span>Richard Zhang, Phillip Isola, Alexei A Efros, Eli Shechtman, and Oliver Wang. The unreasonable effectiveness of deep features as a perceptual metric. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pp. 586–595, 2018.
- <span id="page-17-1"></span>Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, pp. 12674–12685. PMLR, 2021.
- <span id="page-17-2"></span>Bo Zhao and Hakan Bilen. Dataset condensation with distribution matching. In *Proceedings of the IEEE/CVF Winter Conference on Applications of Computer Vision*, pp. 6514–6523, 2023.
- <span id="page-17-0"></span>Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. *ICLR*, 1 (2):3, 2021.
- <span id="page-17-3"></span>Tailin Zhou, Jun Zhang, and Danny HK Tsang. Fedfa: Federated learning with feature anchors to align features and classifiers for heterogeneous data. *IEEE Transactions on Mobile Computing*, 2023.
- <span id="page-17-6"></span>Ligeng Zhu, Zhijian Liu, and Song Han. Deep leakage from gradients. *Advances in neural information processing systems*, 32, 2019.
- <span id="page-17-4"></span>Zhuangdi Zhu, Junyuan Hong, and Jiayu Zhou. Data-free knowledge distillation for heterogeneous federated learning. In *International conference on machine learning*, pp. 12878–12889. PMLR, 2021.

**Road Map of Appendix** Our appendix is organized into six sections. The notation table is in Appendix [A,](#page-18-1) which contains the mathematical notations for Algorithm [1,](#page-5-0) which outlines the pipeline of FEDLGD. Ap-pendix [B](#page-18-0) provides a list of ablation studies to analyze FEDLGD, including communication overhead, convergence rate under different random seeds, and hyper-parameter choices. Last but not least, Appendix [C](#page-21-0) lists the details of our experiments, including the data set information and model architectutres. Our code and model checkpoints are available in this [https://github.com/ubc-tea/FedLGD.](https://github.com/ubc-tea/FedLGD)

<span id="page-18-1"></span>

# **A Notation Table**

| Notations                      | Description                                        |
|--------------------------------|----------------------------------------------------|
| d                              | input dimension                                    |
| d'                             | feature dimension                                  |
| $f^	heta$                      | global model                                       |
| $	heta$                        | model parameters                                   |
| $\psi$                         | feature extractor                                  |
| h                              | projection head                                    |
| $D^g, D^c$                     | original global and local data                     |
| $\tilde{D}^g, \tilde{D}^c$     | global and local synthetic data                    |
| $\mathcal{L}_{\text{total}}$   | total loss function for virtual federated training |
| $\mathcal{L}_{\text{CE}}$      | cross-entropy loss                                 |
| $\mathcal{L}_{\text{Dist}}$    | Distance loss for gradient matching                |
| $\mathcal{L}_{\text{MMD}}$     | MMD loss for distribution matching                 |
| $\mathcal{L}_{{\mathrm{Con}}}$ | Contrastive loss for local training regularization |
| $ lambda$                      | coefficient for local training regularization term |
| T                              | total training iterations                          |
| $ 	au$                         | selected local global distillation iterations      |

Table 4: Important notations used in the paper.

<span id="page-18-0"></span>

## **B Additional Results and Ablation Studies for FedLGD**

<span id="page-18-2"></span>**B.1 Communication overhead.**

Image /page/18/Figure/7 description: The image contains two plots side-by-side. The left plot is labeled "(a) ConvNet" and the right plot is labeled "(b) ResNet18". Both plots have "FL rounds" on the x-axis and "M Bytes" on the y-axis. The left plot shows four lines: a dashed red line labeled "FedAvg28", a solid red line labeled "FedLGD28", a dashed green line labeled "FedAvg96", and a solid green line labeled "FedLGD96". The y-axis ranges from 0 to 80. The right plot also shows the same four lines with the same labels. The y-axis in the right plot ranges from 0 to 1250. In the left plot, the "FedLGD96" line increases sharply from 0 to approximately 85 as "FL rounds" goes from 0 to 100. The "FedLGD28" line increases from 0 to approximately 55. The "FedAvg96" and "FedAvg28" lines are close together and increase from 0 to approximately 25 and 20 respectively. In the right plot, all four lines are very close together and increase from 0 to approximately 1100-1200 as "FL rounds" goes from 0 to 100.

Figure 8: Accumulated communication overhead compared to classical FedAvg.

The accumulated communication overhead for image size  $28 \times 28$  and  $96 \times 96$  can be found in Fig. [8.](#page-18-2) We show the communication cost for both ConvNet and ResNet18. Note that the trade-off of our design reflects in the increased communication overhead, where the clients need to *download* the latest global virtual data in the selected rounds ( $\tau$ ). However, we argue that the  $|\tau|$  can be adjusted based on the communication budget. Additionally, as the model architecture becomes more complex, the added communication overhead turns out to be minor. For instance, the difference between the dashed and solid lines in Fig. [8\(](#page-18-2)b) is less significant than the difference observed in Fig. [8\(](#page-18-2)a).

<span id="page-19-0"></span>

### **B.2 Different random seeds**

Image /page/19/Figure/3 description: This image contains three plots labeled (a), (b), and (c). Each plot shows the 'Loss' on the y-axis against 'Epochs' on the x-axis, ranging from 0 to 100. All three plots display the performance of five different methods: fedavg, fedprox, scaffold, vhl, and fedlgd, indicated by different colored lines. Plot (a) shows losses ranging from 0.50 to 2.25. Plot (b) shows losses ranging from 1.2 to 2.2. Plot (c) shows losses ranging from 0.4 to 1.0. In all plots, the 'fedlgd' line generally shows the lowest loss, indicating better performance, while the 'vhl' line shows the highest loss, especially in plot (c).

Figure 9: Averaged testing loss for (a) DIGITS with  $IPC = 50$ , (b) CIFAR10C with  $IPC = 50$ , and (c) RETINA with  $IPC = 10$  experiments.

<span id="page-19-1"></span>Image /page/19/Figure/5 description: The image displays three line graphs, labeled (a), (b), and (c), each plotting accuracy (Acc) against epochs. All graphs share a common x-axis representing epochs from 0 to 100. Graph (a) shows five lines representing 'fedavg', 'fedprox', 'scaffold', 'vhl', and 'fedlgd', with accuracy values ranging from approximately 0.3 to 0.9. The lines for 'fedavg' and 'fedlgd' are the highest, reaching around 0.85, while 'fedprox' and 'scaffold' are slightly lower, and 'vhl' is the lowest among the top four. Graph (b) also shows the same five lines, but with accuracy values ranging from about 0.25 to 0.55. In this graph, 'fedavg' and 'fedlgd' are the highest, reaching around 0.55, followed closely by 'fedprox' and 'scaffold', with 'vhl' being the lowest. Graph (c) presents a different pattern, with accuracy values ranging from approximately 0.5 to 0.85. Here, 'fedavg', 'fedprox', 'scaffold', and 'fedlgd' show similar performance, reaching around 0.8 to 0.85, while the 'vhl' line shows a much lower and more erratic performance, starting around 0.45 and fluctuating significantly up to about 0.65.

Figure 10: Averaged testing accuracy for (a) DIGITS with IPC = 50, (b) CIFAR10C with IPC = 50, and (c) RETINA with  $IPC = 10$  experiments.

To show the consistent performance of FedLGD, we repeat the experiments for DIGITS, CIFAR10C, and RETINA with three random seeds, and report the validation loss and accuracy curves in Figure [9](#page-19-0) and [10](#page-19-1) (The standard deviations of the curves are plotted as shadows.). We use ConvNet for all the experiments. IPC is set to 50 for CIFAR10C and DIGITS; 10 for RETINA. We use the default hyperparameters for each dataset, and only report FedAvg, FedProx, Scaffold, VHL, which achieves the best performance among baseline as indicated in Table [1,](#page-8-1) [2,](#page-9-1) and [3](#page-10-2) for clear visualization. One can observe that FEDLGD has faster convergence rate and results in optimal performances compared to other baseline methods.

### **B.3 Different heterogeneity levels of label shift**

In the experiment presented in Sec [5.3,](#page-10-0) we study FEDLGD under both label and domain shifts, where labels are sampled from Dirichlet distribution. To ensure dataset distillation performance, we ensure that each class at least has 100 samples per client, thus setting the coefficient of Dirichlet distribution  $\alpha = 2$  to simulate the worst case of label heterogeneity that meets the quality dataset distillation requirement. Here, we show the performance with a less heterogeneity level  $(\alpha = 5)$  while keeping the other settings the same as those in Sec[.5.3.](#page-10-0) The results are shown in Table [5.](#page-20-0) As we expect, the performance drop when the heterogeneity level increases (*α* decreases). One can observe that when heterogeneity increases, FedLGD's performance drop <span id="page-20-0"></span>less except for VHL. We conjecture that VHL yields similar test accuracy for  $\alpha = 2$  and  $\alpha = 5$  is that it uses fixed global virtual data so that the effectiveness of regularization loss does not improve much even if the heterogeneity level is decreased. Nevertheless, FEDLGD consistently outperforms all the baseline methods.

| alpha       | 2    | 5    |
|-------------|------|------|
| FedAvg      | 54.9 | 55.4 |
| FedProx     | 54.9 | 55.4 |
| FedNova     | 53.2 | 55.4 |
| Scaffold    | 54.5 | 55.6 |
| <b>MOON</b> | 51.6 | 51.1 |
| VHL         | 55.2 | 55.4 |
| FedLGD      | 57.4 | 58.1 |

Table 5: Comparison of different  $\alpha$  for Drichilet distribution on CIFAR10C.

### **B.4 Analysis of batch size**

Batch size is another factor for training the FL model and our distilled data. We vary the batch size ∈ {8*,* 16*,* 32*,* 64} to train models for CIFAR10C with the fixed default learning rate. We show the effect of batch size in Table [6](#page-20-1) reported on average testing accuracy. One can observe that the performance is slightly better with moderately smaller batch size which might due to two reasons: 1) more frequent model update locally; and 2) larger model update provides larger gradients, and FedLGD can benefit from the large gradients to distill higher quality virtual data. Overall, the results are generally stable with different batch size choices.

<span id="page-20-1"></span>Table 6: Varying batch size in FedLGD on CIFAR10C. We report the unweighted accuracy. One can observe that the performance increases when the batch size decreases.

| Batch Size | 8    | 16   | 32   | 64   |
|------------|------|------|------|------|
| CIFAR10C   | 59.5 | 58.3 | 57.4 | 56.0 |

### **B.5 Analysis of Local Epoch**

Aggregating at different frequencies is known as an important factor that affects FL behavior. Here, we vary the local epoch  $\in \{1,2,5\}$  to train all baseline models on CIFAR10C. Figure [11](#page-21-1) shows the result of test accuracy under different epochs. One can observe that as the local epoch increases, the performance of FedLGD would drop a little bit. This is because doing gradient matching requires the model to be trained to an intermediate level, and if local epochs increase, the loss of CIFAR10C models will drop significantly. However, FEDLGD still consistently outperforms the baseline methods. As our future work, we will investigate the tuning of the learning rate in the early training stage to alleviate the effect.

### **B.6 Different Initialization for Virtual Images**

To validate our proposed initialization for virtual images has the best trade-off between privacy and efficacy, we compare our test accuracy with the models trained with synthetic images initialized by random noise and real images in Table [7.](#page-21-2) To show the effect of initialization under large domain shift, we run experiments on DIGITS dataset. One can observe that our method which utilizes the statistics  $(\mu_i, \sigma_i)$  of local clients as initialization outperforms random noise initialization. Although our performance is slightly worse than the initialization that uses real images from clients, we do not ask the clients to share real image-level information to the server which is more privacy-preserving.

<span id="page-21-1"></span>Image /page/21/Figure/1 description: A bar chart displays the accuracy (Acc) of different federated learning algorithms (FedAvg, FedProx, FedNova, Scaffold, MOON, FedVHL, FedLGD) across three epochs: 1, 2, and 5. At epoch 1, FedAvg, FedProx, FedNova, Scaffold, MOON, FedVHL, and FedLGD have accuracies of approximately 55, 55, 54, 54, 52, 54, and 57 respectively. At epoch 2, the accuracies are around 55, 55, 33, 35, 51, 55, and 57. At epoch 5, the accuracies are approximately 55, 55, 37, 12, 52, 55, and 57.

Figure 11: Comparison of model performances under different local epochs with CIFAR10C.

<span id="page-21-2"></span>Table 7: Comparison of different initialization for synthetic images DIGITS. Ours  $(\mathcal{N}(\mu_i, \sigma_i))$  is shown in the middle column.

| <b>DIGITS</b>      | $\mathcal{N}(0,1)$ | $\mathcal{N}(\mu_i, \sigma_i)$ | <b>Real Images</b> |
|--------------------|--------------------|--------------------------------|--------------------|
| <b>MNIST</b>       | 96.3               | 97.1                           | 97.7               |
| <b>SVHN</b>        | 75.9               | 77.3                           | 78.8               |
| <b>USPS</b>        | 933                | 94.6                           | 94.2               |
| <b>SynthDigits</b> | 72.0               | 78.5                           | 82.4               |
| <b>MNIST-M</b>     | 83.7               | 86.1                           | 89.5               |
| <b>Average</b>     | 84.2               | 86.7                           | 88.5               |

<span id="page-21-0"></span>

# **C Experimental details**

## **C.1 Visualization of the original images**

<span id="page-21-3"></span>Image /page/21/Picture/7 description: The image displays five grids of numbers, labeled (a) through (e). Grid (a) shows nine white digits on a black background: three 0s in the top row, three 1s in the middle row, and three 2s in the bottom row. Grid (b) shows nine colored digits on various backgrounds, including numbers 0, 1, 2, and 10, with some digits appearing blurry. Grid (c) shows nine blurry white digits on a black background, similar to grid (a) but with a softer focus. Grid (d) shows nine digits overlaid on colorful, abstract backgrounds, with digits 0, 1, 2, and an arrow shape. Grid (e) shows nine digits on natural, outdoor backgrounds, including numbers 0, 1, 2, and a flower-like shape.

Figure 12: Visualization of the original digits dataset. (a) visualized the MNIST client; (b) visualized the SVHN client; (c) visualized the USPS client; (d) visualized the SynthDigits client; (e) visualized the MNIST-M client.

The visualization of the original DIGITS, CIFAR10C, and RETINA images can be found in Figure [12,](#page-21-3) Figure [13,](#page-22-2) and Figure [14,](#page-22-3) respectively.

## **C.2 Visualization of our distilled global and local images**

The visualization of the virtual DIGITS, CIFAR10C, and RETINA images can be found in Figure [15,](#page-23-0) Figure [16,](#page-23-1) and Figure [17,](#page-24-0) respectively.

<span id="page-22-2"></span>Image /page/22/Picture/1 description: The image displays six grids of images, labeled (a) through (f). Each grid contains nine smaller images arranged in a 3x3 format. Grid (a) shows various vehicles, including cars and possibly a train or bus, and some animals like a dog and birds. Grid (b) also features cars and birds. Grid (c) contains images of airplanes, cars, and birds. Grid (d) presents a mix of animals, including birds and possibly a seal or sea lion, along with cars and a stealth aircraft. Grid (e) shows airplanes, cars, and birds, including what appears to be a bat and an owl. Grid (f) includes airplanes, cars, and birds, with one image showing an ostrich.

Figure 13: Visualization of the original CIFAR10C. Sampled images from the first six clients.

<span id="page-22-3"></span>Image /page/22/Figure/3 description: The image displays four sets of retinal fundus photographs, labeled (a), (b), (c), and (d) from left to right. Each set consists of four individual photographs arranged in a 2x2 grid. The photographs show the optic disc and surrounding retinal vasculature. In set (a), the top left and bottom left images show a healthy optic disc with visible blood vessels. The top right and bottom right images also show optic discs, with the top right appearing slightly blurred and the bottom right showing a more pronounced optic cup. Set (b) presents four images of optic discs, with varying degrees of clarity and color saturation. The top left and bottom right images appear to have a lighter optic disc, while the top right and bottom left images show more prominent vasculature. Set (c) contains four images of optic discs, all appearing to have a healthy appearance with clear vasculature. The optic discs are generally pinkish-orange in color. Set (d) also shows four images of optic discs, with the top left and bottom left images exhibiting a clear view of the optic disc and vessels. The top right and bottom right images show optic discs with a brighter appearance and visible blood vessels branching out.

Figure 14: Visualization of the original retina dataset. (a) visualized the Drishti client; (b) visualized the Acrima client; (c) visualized the Rim client; (d) visualized the Refuge client.

<span id="page-22-1"></span>

## **C.3 Visualization of the heterogeneity of the datasets**

The visualization of the original distribution in histogram for DIGITS, CIFAR10C, and RETINA images can be found in Figure [18,](#page-24-1) Figure [19,](#page-25-0) and Figure [20,](#page-25-1) respectively.

<span id="page-22-0"></span>

## **C.4 Model architecture**

The two model architectures (ResNet18 and ConvNet) are detailed in Table [8](#page-26-0) and Table [9,](#page-26-1) respectively.

<span id="page-23-0"></span>Image /page/23/Figure/1 description: The image displays six grids of images, labeled (a) through (f). Grid (a) shows three rows and three columns of handwritten digits: 0s in the first row, 1s in the second, and 2s in the third. Grid (b) shows three rows and three columns of blurry, abstract color images. Grid (c) is similar to grid (a), showing handwritten digits 0, 1, and 2 in a similar arrangement, but with a softer, more diffused appearance. Grid (d) contains three rows and three columns of colorful, abstract images with swirling patterns and varied hues. Grid (e) shows three rows and three columns of digits, primarily 0s, 1s, and 2s, rendered with a soft, watercolor-like effect and subtle color variations. Grid (f) presents three rows and three columns of highly abstract, colorful, and noisy images, with intricate patterns and a wide spectrum of colors.

Figure 15: Visualization of the global and local distilled images from the digits dataset. (a) visualized the MNIST client; (b) visualized the SVHN client; (c) visualized the USPS client; (d) visualized the SynthDigits client; (e) visualized the MNIST-M client; (f) visualized the server distilled data.

<span id="page-23-1"></span>Image /page/23/Picture/3 description: The image displays a grid of smaller images, organized into two rows. The top row contains three sets of nine images each, labeled (a), (b), and (c) from left to right. Each of these sets shows nine individual images arranged in a 3x3 grid. The bottom row contains four sets of nine images each, labeled (d), (e), (f), and (g) from left to right. The first three sets in the bottom row, (d), (e), and (f), also show nine individual images in a 3x3 grid. The final set, labeled (g), displays a single 3x3 grid of images that are much more colorful and abstract compared to the images in sets (a) through (f). The images in sets (a) through (f) appear to be generated images, possibly of objects like airplanes or cars, with varying degrees of clarity and detail. The overall presentation suggests a comparison or progression of generated image quality or style.

Figure 16: (a)-(f) visualizes the distailled images for the first six clients of CIFAR10C. (g) visualizes the global distilled images.

<span id="page-24-0"></span>Image /page/24/Picture/1 description: The image displays five sets of images, labeled (a) through (e). Set (a) contains four images of the optic nerve head, with a distinct optic disc and surrounding retinal vasculature. Set (b) also shows four similar images of the optic nerve head, exhibiting variations in clarity and color. Set (c) presents four more images of the optic nerve head, again with visible optic discs and blood vessels. Set (d) contains four images that appear to be of the optic nerve head, with some images showing a brighter optic disc. Set (e) displays four images that are significantly different, appearing as noisy, abstract patterns with a mix of red, green, and yellow colors, lacking the clear structure of the optic nerve heads in the other sets.

Figure 17: Visualization of the global and local distilled images from retina dataset. (a) visualized the Drishti client; (b) visualized the Acrima client; (c) visualized the Rim client; (d) visualized the Refuge client; (e) visualized the server distilled data.

<span id="page-24-1"></span>Image /page/24/Figure/3 description: The image displays five histograms, labeled (a) MNIST, (b) SVHN, (c) USPS, (d) SynthDigits, and (e) MNIST-M. Histogram (a) shows a single tall bar at the beginning of the x-axis, representing data for MNIST. Histogram (b) shows a bell-shaped distribution peaking around 100-150 on the x-axis, with multiple colored bars (blue, green, red) representing data for SVHN. Histogram (c) shows a sharp drop-off from a very tall bar at the beginning of the x-axis, representing data for USPS. Histogram (d) displays a distribution similar to (b) but with a slightly wider spread and a peak around 150 on the x-axis, also with multiple colored bars for SynthDigits. Histogram (e) shows a distribution with multiple peaks and a generally wider spread than (b) and (d), with a prominent peak around 125-150 on the x-axis, also with multiple colored bars for MNIST-M. The y-axis scales vary across the histograms, with (a) and (c) having scales up to 3.5e7 and 1.6e6 respectively, while (b), (d), and (e) have scales up to 400,000, 40,000, and 70,000 respectively. The x-axis for all histograms ranges from 0 to 250.

Figure 18: Histogram for the frequency of each RGB value in original DIGITS. The red bar represents the count for R; the green bar represents the frequency of each pixel for G; the blue bar represents the frequency of each pixel for B. One can observe the distributions are very different. Note that figure (a) and figure (c) are both greyscale images with most pixels lying in 0 and 255.

<span id="page-25-0"></span>Image /page/25/Figure/1 description: This image displays six histograms, labeled (a) through (f), arranged in a 2x3 grid. Each histogram represents the frequency of RGB values, with different colors (blue, green, and red) indicating the distribution of each color channel. The x-axis for all histograms ranges from 0.0 to 1.0, representing the normalized RGB values. The y-axis represents the frequency or count of pixels for each value. Histograms (a), (b), (c), and (f) show distributions that are generally spread across the 0.0 to 1.0 range, with varying peaks and concentrations. Histograms (d) and (e) show distributions that are more concentrated towards the lower end of the x-axis, with histogram (e) having a particularly wide spread of frequencies from 0.0 to approximately 0.6.

Figure 19: Histogram for the frequency of each RGB value in the first six clients of original CIFAR10C. The red bar represents the count for R; the green bar represents the frequency of each pixel for G; the blue bar represents the frequency of each pixel for B.

<span id="page-25-1"></span>Image /page/25/Figure/3 description: This image displays four histograms, labeled (a) Drishti, (b) Acrima, (c) RIM, and (d) REFUGE. Each histogram shows the distribution of pixel values for three color channels (likely red, green, and blue) across a range of 0 to 200 or 250 on the x-axis, with the y-axis representing the count of pixels. The Drishti histogram shows a dominant peak at low values for one channel, with smaller peaks for the other two channels at higher values. The Acrima histogram exhibits three distinct peaks across the range, with the first two peaks being more pronounced. The RIM histogram also shows three main peaks, with the first two being the highest. The REFUGE histogram displays a broad distribution across the range, with a very sharp, high peak at the maximum value of 250 for one of the channels.

Figure 20: Histogram for the frequency of each RGB value in original RETINA. The red bar represents the count for R; the green bar represents the frequency of each pixel for G; the blue bar represents the frequency of each pixel for B.

<span id="page-26-0"></span>Table 8: ResNet18 architecture. For the convolutional layer (Conv2D), we list parameters with a sequence of input and output dimensions, kernel size, stride, and padding. For the max pooling layer (MaxPool2D), we list kernel and stride. For a fully connected layer (FC), we list input and output dimensions. For the BatchNormalization layer (BN), we list the channel dimension.

| Layer | Details                                          |
|-------|--------------------------------------------------|
| 1     | Conv2D $(3, 64, 7, 2, 3)$ , BN $(64)$ , ReLU     |
| 2     | Conv2D $(64, 64, 3, 1, 1)$ , BN $(64)$ , ReLU    |
| 3     | Conv2D $(64, 64, 3, 1, 1)$ , BN $(64)$           |
| 4     | Conv2D $(64, 64, 3, 1, 1)$ , BN $(64)$ , ReLU    |
| 5     | Conv2D $(64, 64, 3, 1, 1)$ , BN $(64)$           |
| 6     | Conv2D $(64, 128, 3, 2, 1)$ , BN $(128)$ , ReLU  |
| 7     | Conv2D $(128, 128, 3, 1, 1)$ , BN $(64)$         |
| 8     | Conv2D $(64, 128, 1, 2, 0)$ , BN $(128)$         |
| 9     | Conv2D $(128, 128, 3, 1, 1)$ , BN $(128)$ , ReLU |
| 10    | Conv2D $(128, 128, 3, 1, 1)$ , BN $(64)$         |
| 11    | Conv2D $(128, 256, 3, 2, 1)$ , BN $(128)$ , ReLU |
| 12    | Conv2D $(256, 256, 3, 1, 1)$ , BN $(64)$         |
| 13    | Conv2D $(128, 256, 1, 2, 0)$ , BN $(128)$        |
| 14    | Conv2D $(256, 256, 3, 1, 1)$ , BN $(128)$ , ReLU |
| 15    | Conv2D $(256, 256, 3, 1, 1)$ , BN $(64)$         |
| 16    | Conv2D $(256, 512, 3, 2, 1)$ , BN $(512)$ , ReLU |
| 17    | Conv2D $(512, 512, 3, 1, 1)$ , BN $(512)$        |
| 18    | Conv2D $(256, 512, 1, 2, 0)$ , BN $(512)$        |
| 19    | Conv2D $(512, 512, 3, 1, 1)$ , BN $(512)$ , ReLU |
| 20    | Conv2D $(512, 512, 3, 1, 1)$ , BN $(512)$        |
| 21    | AvgPool2D                                        |
| 22    | FC $(512, num_class)$                            |

<span id="page-26-1"></span>Table 9: ConvNet architecture. For the convolutional layer (Conv2D), we list parameters with a sequence of input and output dimensions, kernel size, stride, and padding. For the max pooling layer (MaxPool2D), we list kernel and stride. For a fully connected layer (FC), we list the input and output dimensions. For the GroupNormalization layer (GN), we list the channel dimension.

| Layer | Details                                  |
|-------|------------------------------------------|
| 1     | Conv2D(3, 128, 3, 1, 1), GN(128), ReLU   |
| 2     | AvgPool2d(2,2,0)                         |
| 3     | Conv2D(128, 118, 3, 1, 1), GN(128), ReLU |
| 4     | AvgPool2d(2,2,0)                         |
| 5     | Conv2D(128, 128, 3, 1, 1), GN(128), ReLU |
| 6     | AvgPool2d(2,2,0)                         |
| 7     | FC(1152, num_class)                      |
# **13** *Sparse linear models*

# **13.1 Introduction**

We introduced the topic of feature selection in Section 3.5.4, where we discussed methods for finding input variables which had high mutual information with the output. The trouble with this approach is that it is based on a myopic strategy that only looks at one variable at a time. This can fail if there are interaction effects. For example, if  $y = xor(x_1, x_2)$ , then neither  $x_1$  nor  $x_2$  on its own can predict the response, but together they perfectly predict the response. For a real-world example of this, consider genetic association studies: sometimes two genes on their own may be harmless, but when present together they cause a recessive disease (<PERSON><PERSON> 2006).

In this chapter, we focus on selecting sets of variables at a time using a model-based approach. If the model is a generalized linear model, of the form  $p(y|\mathbf{x}) = p(y|f(\mathbf{w}^T\mathbf{x}))$  for some link function f, then we can perform feature selection by encouraging the weight vector **w** to be **sparse**, i.e., to have lots of zeros. This approach turns out to offer significant computational advantages, as we will see below.

Here are some applications where feature selection/ sparsity is useful:

- In many problems, we have many more dimensions  $D$  than training cases  $N$ . The corresponding design matrix is short and fat, rather than tall and skinny. This is called the **small** N, large D problem. This is becoming increasingly prevalent as we develop more high throughput measurement devices, For example, with gene microarrays, it is common to measure the expression levels of  $D \sim 10,000$  genes, but to only get  $N \sim 100$  such examples. (It is perhaps a sign of the times that even our data seems to be getting fatter...) We may want to find the smallest set of features that can accurately predict the response (e.g., growth rate of the cell) in order to prevent overfitting, to reduce the cost of building a diagnostic device, or to help with scientific insight into the problem.
- In Chapter 14, we will use basis functions centered on the training examples, so  $\phi(\mathbf{x}) =$  $[\kappa(\mathbf{x}, \mathbf{x}_1), \ldots, \kappa(\mathbf{x}, \mathbf{x}_N)]$ , where  $\kappa$  is a kernel function. The resulting design matrix has size  $N \times N$ . Feature selection in this context is equivalent to selecting a subset of the training examples, which can help reduce overfitting and computational cost. This is known as a sparse kernel machine.
- In signal processing, it is common to represent signals (images, speech, etc.) in terms of wavelet basis functions. To save time and space, it is useful to find a sparse representation

of the signals, in terms of a small number of such basis functions. This allows us to estimate signals from a small number of measurements, as well as to compress the signal. See Section 13.8.3 for more information.

Note that the topic of feature selection and sparsity is currently one of the most active areas of machine learning/ statistics. In this chapter, we only have space to give an overview of the main results.

# **13.2 Bayesian variable selection**

A natural way to pose the variable selection problem is as follows. Let  $\gamma_i = 1$  if feature j is "relevant", and let  $\gamma_i = 0$  otherwise. Our goal is to compute the posterior over models

$$
p(\gamma|\mathcal{D}) = \frac{e^{-f(\gamma)}}{\sum_{\gamma'} e^{-f(\gamma')}}\tag{13.1}
$$

where  $f(\gamma)$  is the cost function:

$$
f(\gamma) \triangleq -[\log p(\mathcal{D}|\gamma) + \log p(\gamma)] \tag{13.2}
$$

For example, suppose we generate  $N = 20$  samples from a  $D = 10$  dimensional linear regression model,  $y_i$  ∼  $\mathcal{N}(\mathbf{w}^T\mathbf{x}_i, \sigma^2)$ , in which  $K = 5$  elements of **w** are non-zero. In particular, we use  $\mathbf{w} = (0.00, -1.67, 0.13, 0.00, 0.00, 1.19, 0.00, -0.04, 0.33, 0.00)$  and  $\sigma^2 =$ 1. We enumerate all  $2^{10} = 1024$  models and compute  $p(\gamma|\mathcal{D})$  for each one (we give the equations for this below). We order the models in **Gray code** order, which ensures consecutive vectors differ by exactly 1 bit (the reasons for this are computational, and are discussed in Section 13.2.3).

The resulting set of bit patterns is shown in Figure 13.1(a). The cost of each model,  $f(\gamma)$ , is shown in Figure 13.1(b). We see that this objective function is extremely "bumpy". The results are easier to interpret if we compute the posterior distribution over models,  $p(\gamma|\mathcal{D})$ . This is shown in Figure 13.1(c). The top 8 models are listed below:

| model | prob  | members  |
|-------|-------|----------|
| 4     | 0.447 | 2,       |
| 61    | 0.241 | 2, 6,    |
| 452   | 0.103 | 2, 6, 9, |
| 60    | 0.091 | 2, 3, 6, |
| 29    | 0.041 | 2, 5,    |
| 68    | 0.021 | 2, 6, 7, |
| 36    | 0.015 | 2, 5, 6, |
| 5     | 0.010 | 2, 3,    |

The "true" model is  $\{2, 3, 6, 8, 9\}$ . However, the coefficients associated with features 3 and 8 are very small (relative to  $\sigma^2$ ). so these variables are harder to detect. Given enough data, the method will converge on the true model (assuming the data is generated from a linear model), but for finite data sets, there will usually be considerable posterior uncertainty.

Interpreting the posterior over a large number of models is quite difficult, so we will seek various summary statistics. A natural one is the posterior mode, or MAP estimate

$$
\hat{\gamma} = \operatorname{argmax} p(\gamma | \mathcal{D}) = \operatorname{argmin} f(\gamma) \tag{13.3}
$$

Image /page/2/Figure/1 description: This image contains four plots labeled (a), (b), (c), and (d). Plot (a) is a black and white image with horizontal lines of varying widths, resembling a fractal pattern or a binary tree structure, with labels 1 through 10 on the left vertical axis and numbers 100 through 1000 on the bottom horizontal axis. Plot (b) is a line graph titled "log p(model, data)" with the x-axis labeled from 0 to 1000 and the y-axis labeled from -220 to -40, showing a fluctuating blue line. Plot (c) is a scatter plot titled "p(model|data)" with the x-axis labeled from 0 to 1000 and the y-axis labeled from 0 to 0.1, displaying several blue data points connected by lines, with prominent spikes at approximately x=10 and x=500. Plot (d) is a bar chart titled "p(gamma(j)|data)" with the x-axis labeled from 1 to 10 and the y-axis labeled from 0 to 1, showing several blue bars of varying heights, with the tallest bar at x=2 reaching a height of 1, and another significant bar at x=6 reaching approximately 0.5.

**Figure 13.1** (a) All possible bit vectors of length 10 enumerated in Gray code order. (b) Score function for all possible models. (c) Posterior over all 1024 models. Vertical scale has been truncated at 0.1 for clarity. (d) Marginal inclusion probabilities. Figure generated by linregAllsubsetsGraycodeDemo.

However, the mode is often not representative of the full posterior mass (see Section 5.2.1.3). A better summary is the **median model** (Barbieri and Berger 2004; Carvahlo and Lawrence 2007), computed using

$$
\hat{\gamma} = \{ j : p(\gamma_j = 1 | \mathcal{D}) > 0.5 \}
$$
\n(13.4)

This requires computing the posterior marginal **inclusion probabilities**,  $p(\gamma_j = 1 | \mathcal{D})$ . These are shown in Figure 13.1(d). We see that the model is confident that variables 2 and 6 are included; if we lower the decision threshold to 0.1, we would add 3 and 9 as well. However, if we wanted to "capture" variable 8, we would incur two false positives (5 and 7). This tradeoff between false positives and false negatives is discussed in more detail in Section 5.7.2.1.

The above example illustrates the "gold standard" for variable selection: the problem was sufficiently small (only 10 variables) that we were able to compute the full posterior exactly. Of course, variable selection is most useful in the cases where the number of dimensions is large. Since there are  $2^D$  possible models (bit vectors), it will be impossible to compute the full posterior in general, and even finding summaries, such as the MAP estimate or marginal inclusion probabilities, will be intractable. We will therefore spend most of this chapter focussing on algorithmic speedups. But before we do that, we will explain how we computed  $p(\gamma|\mathcal{D})$  in the above example.

#### **13.2.1 The spike and slab model**

The posterior is given by

$$
p(\boldsymbol{\gamma}|\mathcal{D}) \propto p(\boldsymbol{\gamma})p(\mathcal{D}|\boldsymbol{\gamma})
$$
\n(13.5)

We first consider the prior, then the likelihood.

It is common to use the following prior on the bit vector:

$$
p(\gamma) = \prod_{j=1}^{D} \text{Ber}(\gamma_j|\pi_0) = \pi_0^{||\gamma||_0} (1 - \pi_0)^{D - ||\gamma||_0}
$$
\n(13.6)

where  $\pi_0$  is the probability a feature is relevant, and  $||\gamma||_0 = \sum_{j=1}^D \gamma_j$  is the  $\ell_0$  **pseudo-norm**, that is, the number of non-zero elements of the vector. For comparison with later models, it is useful to write the log prior as follows:

$$
\log p(\gamma|\pi_0) = ||\gamma||_0 \log \pi_0 + (D - ||\gamma||_0) \log(1 - \pi_0)
$$
\n(13.7)

$$
= \quad ||\gamma||_0 (\log \pi_0 - \log(1 - \pi_0)) + \text{const}
$$
\n(13.8)

$$
= -\lambda ||\gamma||_0 + \text{const}
$$
 (13.9)

where  $\lambda \triangleq \log \frac{1-\pi_0}{\pi_0}$  controls the sparsity of the model.

We can write the likelihood as follows:

$$
p(\mathcal{D}|\boldsymbol{\gamma}) = p(\mathbf{y}|\mathbf{X},\boldsymbol{\gamma}) = \int \int p(\mathbf{y}|\mathbf{X},\mathbf{w},\boldsymbol{\gamma}) p(\mathbf{w}|\boldsymbol{\gamma},\sigma^2) p(\sigma^2) d\mathbf{w} d\sigma^2
$$
\n(13.10)

For notational simplicity, we have assumed the response is centered, (i.e.,  $\bar{y} = 0$ ), so we can ignore any offset term  $\mu$ .

We now discuss the prior  $p(\mathbf{w}|\gamma, \sigma^2)$ . If  $\gamma_j = 0$ , feature j is irrelevant, so we expect  $w_j = 0$ . If  $\gamma_j = 1$ , we expect  $w_j$  to be non-zero. If we standardize the inputs, a reasonable prior is  $\mathcal{N}(0, \sigma^2 \sigma_w^2)$ , where  $\sigma_w^2$  controls how big we expect the coefficients associated with the relevant variables to be (which is scaled by the overall noise level  $\sigma^2$ ). We can summarize this prior as follows:

$$
p(w_j|\sigma^2, \gamma_j) = \begin{cases} \delta_0(w_j) & \text{if } \gamma_j = 0\\ \mathcal{N}(w_j|0, \sigma^2 \sigma_w^2) & \text{if } \gamma_j = 1 \end{cases}
$$
\n(13.11)

The first term is a "spike" at the origin. As  $\sigma_w^2 \to \infty$ , the distribution  $p(w_j | \gamma_j = 1)$  approaches a uniform distribution, which can be thought of as a "slab" of constant height. Hence this is called the **spike and slab** model (Mitchell and Beauchamp 1988).

We can drop the coefficients  $w_j$  for which  $w_j = 0$  from the model, since they are clamped to zero under the prior. Hence Equation 13.10 becomes the following (assuming a Gaussian likelihood):

$$
p(\mathcal{D}|\boldsymbol{\gamma}) = \int \int \mathcal{N}(\mathbf{y}|\mathbf{X}_{\boldsymbol{\gamma}}\mathbf{w}_{\boldsymbol{\gamma}}, \sigma^2 \mathbf{I}_N) \mathcal{N}(\mathbf{w}_{\boldsymbol{\gamma}}|\mathbf{0}_{D_{\boldsymbol{\gamma}}}, \sigma^2 \sigma_w^2 \mathbf{I}_{D_{\boldsymbol{\gamma}}}) p(\sigma^2) d\mathbf{w}_{\boldsymbol{\gamma}} d\sigma^2
$$
(13.12)

where  $D_{\gamma} = ||\gamma||_0$  is the number of non-zero elements in  $\gamma$ . In what follows, we will generalize this slightly by defining a prior of the form  $p(\mathbf{w}|\gamma, \sigma^2) = \mathcal{N}(\mathbf{w}_\gamma|\mathbf{0}_{D_\gamma}, \sigma^2\Sigma_\gamma)$  for any positive definite matrix **Σ**γ. 1

Given these priors, we can now compute the marginal likelihood. If the noise variance is known, we can write down the marginal likelihood (using Equation 13.151) as follows:

$$
p(\mathcal{D}|\gamma,\sigma^2) = \int \mathcal{N}(\mathbf{y}|\mathbf{X}_{\gamma}\mathbf{w}_{\gamma},\sigma^2\mathbf{I})\mathcal{N}(\mathbf{w}_{\gamma}|\mathbf{0},\sigma^2\mathbf{\Sigma}_{\gamma})d\mathbf{w}_{\gamma} = \mathcal{N}(\mathbf{y}|\mathbf{0},\mathbf{C}_{\gamma})
$$
(13.13)

$$
\mathbf{C}_{\gamma} \triangleq \sigma^2 \mathbf{X}_{\gamma} \mathbf{\Sigma}_{\gamma} \mathbf{X}_{\gamma}^T + \sigma^2 \mathbf{I}_N \tag{13.14}
$$

If the noise is unknown, we can put a prior on it and integrate it out. It is common to use  $p(\sigma^2) = \text{IG}(\sigma^2 | a_{\sigma}, b_{\sigma})$ . Some guidelines on setting a, b can be found in (Kohn et al. 2001). If we use  $a = b = 0$ , we recover the Jeffrey's prior,  $p(\sigma^2) \propto \sigma^{-2}$ . When we integrate out the noise, we get the following more complicated expression for the marginal likelihood (Brown et al. 1998):

$$
p(\mathcal{D}|\boldsymbol{\gamma}) = \int p(\mathbf{y}|\boldsymbol{\gamma}, \mathbf{w}_{\boldsymbol{\gamma}}, \sigma^2) p(\mathbf{w}_{\boldsymbol{\gamma}}|\boldsymbol{\gamma}, \sigma^2) p(\sigma^2) d\mathbf{w}_{\boldsymbol{\gamma}} d\sigma^2
$$
\n(13.15)

$$
\propto \left| \mathbf{X}_{\gamma}^T \mathbf{X}_{\gamma} + \mathbf{\Sigma}_{\gamma}^{-1} \right|^{-\frac{1}{2}} \left| \mathbf{\Sigma}_{\gamma} \right|^{-\frac{1}{2}} (2b_{\sigma} + S(\gamma))^{-(2a_{\sigma} + N - 1)/2}
$$
(13.16)

where  $S(\gamma)$  is the RSS:

$$
S(\gamma) \triangleq \mathbf{y}^T \mathbf{y} - \mathbf{y}^T \mathbf{X}_{\gamma} (\mathbf{X}_{\gamma}^T \mathbf{X}_{\gamma} + \mathbf{\Sigma}_{\gamma}^{-1})^{-1} \mathbf{X}_{\gamma}^T \mathbf{y}
$$
\n(13.17)

See also Exercise 13.4.

When the marginal likelihood cannot be computed in closed form (e.g., if we are using logistic regression or a nonlinear model), we can approximate it using BIC, which has the form

$$
\log p(\mathcal{D}|\boldsymbol{\gamma}) \approx \log p(\mathbf{y}|\mathbf{X}, \hat{\mathbf{w}}_{\gamma}, \hat{\sigma}^2) - \frac{||\boldsymbol{\gamma}||_0}{2} \log N \tag{13.18}
$$

where  $\hat{\mathbf{w}}_{\gamma}$  is the ML or MAP estimate based on  $\mathbf{X}_{\gamma}$ , and  $||\gamma||_0$  is the "degrees of freedom" of the model (Zou et al. 2007). Adding the log prior, the overall objective becomes

$$
\log p(\boldsymbol{\gamma}|\mathcal{D}) \approx \log p(\mathbf{y}|\mathbf{X}, \hat{\mathbf{w}}_{\gamma}, \hat{\sigma}^2) - \frac{||\boldsymbol{\gamma}||_0}{2} \log N - \lambda ||\boldsymbol{\gamma}||_0 + \text{const}
$$
\n(13.19)

We see that there are two complexity penalties: one arising from the BIC approximation to the marginal likelihood, and the other arising from the prior on  $p(\gamma)$ . Obviously these can be combined into one overall complexity parameter, which we will denote by  $\lambda$ .

#### **13.2.2** From the Bernoulli-Gaussian model to $\ell_0$ regularization

Another model that is sometimes used (e.g., (Kuo and Mallick 1998; Zhou et al. 2009; Soussen et al. 2010)) is the following:

$$
y_i|\mathbf{x}_i, \mathbf{w}, \gamma, \sigma^2 \sim \mathcal{N}(\sum_j \gamma_j w_j x_{ij}, \sigma^2)
$$
 (13.20)

$$
\gamma_j \sim \text{Ber}(\pi_0) \tag{13.21}
$$

$$
w_j \sim \mathcal{N}(0, \sigma_w^2) \tag{13.22}
$$

<sup>1.</sup> It is common to use a g-prior of the form  $\Sigma_{\gamma} = g(\mathbf{X}_{\gamma}^T \mathbf{X}_{\gamma})^{-1}$  for reasons explained in Section 7.6.3.1 (see also<br>Exercise 13.4) Various approaches have been proposed for setting a including cross validati Exercise 13.4). Various approaches have been proposed for setting  $q$ , including cross validation, empirical Bayes (Minka 2000b; George and Foster 2000), hierarchical Bayes (Liang et al. 2008), etc.

In the signal processing literature (e.g., (Soussen et al. 2010)), this is called the **Bernoulli-Gaussian** model, although we could also call it the **binary mask** model, since we can think of the  $\gamma_i$  variables as "masking out" the weights  $w_j$ .

Unlike the spike and slab model, we do not integrate out the "irrelevant" coefficients; they always exist. In addition, the binary mask model has the form  $\gamma_i \to \mathbf{y} \leftarrow w_i$ , whereas the spike and slab model has the form  $\gamma_i \to w_j \to y$ . In the binary mask model, only the product  $\gamma_i w_j$ can be identified from the likelihood.

One interesting aspect of this model is that it can be used to derive an objective function that is widely used in the (non-Bayesian) subset selection literature. First, note that the joint prior has the form

$$
p(\gamma, \mathbf{w}) \propto \mathcal{N}(\mathbf{w}|\mathbf{0}, \sigma_w^2 \mathbf{I}) \pi_0^{||\gamma||_0} (1 - \pi_0)^{D - ||\gamma||_0}
$$
\n(13.23)

Hence the scaled unnormalized negative log posterior has the form

$$
f(\gamma, \mathbf{w}) \triangleq -2\sigma^2 \log p(\gamma, \mathbf{w}, \mathbf{y}|\mathbf{X}) = ||\mathbf{y} - \mathbf{X}(\gamma \cdot * \mathbf{w})||^2 + \frac{\sigma^2}{\sigma_w^2} ||\mathbf{w}||^2 + \lambda ||\gamma||_0 + \text{const} \quad (13.24)
$$

where

$$
\lambda \triangleq 2\sigma^2 \log(\frac{1-\pi_0}{\pi_0})
$$
\n(13.25)

Let us split **w** into two subvectors,  $w_{\gamma}$  and  $w_{\gamma}$ , indexed by the zero and non-zero entries of *γ* respectively. Since  $\mathbf{X}(\gamma \cdot \mathbf{w}) = \mathbf{X}_{\gamma} \mathbf{w}_{\gamma}$ , we can just set  $\mathbf{w}_{-\gamma} = \mathbf{0}$ .

Now consider the case where  $\sigma_w^2 \to \infty$ , so we do not regularize the non-zero weights (so there is no complexity penalty coming from the marginal likelihood or its BIC approximation). In this case, the objective becomes

$$
f(\gamma, \mathbf{w}) = ||\mathbf{y} - \mathbf{X}_{\gamma} \mathbf{w}_{\gamma}||_2^2 + \lambda ||\gamma||_0
$$
\n(13.26)

This is similar to the BIC objective above.

Instead of keeping track of the bit vector  $\gamma$ , we can define the set of relevant variables to be the **support**, or set of non-zero entries, of **w**. Then we can rewrite the above equation as follows:

$$
f(\mathbf{w}) = ||\mathbf{y} - \mathbf{X}\mathbf{w}||_2^2 + \lambda ||\mathbf{w}||_0
$$
\n(13.27)

This is called  $\ell_0$  **regularization**. We have converted the discrete optimization problem (over  $\gamma \in \{0,1\}^D$ ) into a continuous one (over  $\mathbf{w} \in \mathbb{R}^D$ ); however, the  $\ell_0$  pseudo-norm makes the objective very non smooth, so this is still hard to optimize. We will discuss different solutions to this in the rest of this chapter.

## **13.2.3 Algorithms**

Since there are  $2^D$  models, we cannot explore the full posterior, or find the globally optimal model. Instead we will have to resort to heuristics of one form or another. All of the methods we will discuss involve searching through the space of models, and evaluating the cost  $f(\gamma)$  at

Image /page/6/Figure/1 description: The image contains two subfigures, labeled (a) and (b). Subfigure (a) displays a triangular arrangement of sets, starting with the empty set {} at the bottom, then sets with single elements {1}, {2}, {3}, {4} in the row above, followed by sets with two elements, then three elements, and finally the set with all four elements {1, 2, 3, 4} at the top. Subfigure (b) is a scatter plot titled "all subsets on prostate cancer". The x-axis is labeled "subset size" and ranges from 0 to 8. The y-axis is labeled "training set error" and ranges from 0.4 to 1.4. The plot shows blue dots representing individual data points, and a red line connecting points that represent the average training set error for each subset size. The red line shows a decreasing trend, starting at approximately 1.35 for subset size 0, dropping to 0.6 for subset size 1, then to around 0.55 for subset size 2, 0.5 for subset size 3, and continuing to decrease to about 0.45 for subset size 6 and beyond.

**Figure 13.2** (a) A lattice of subsets of  $\{1, 2, 3, 4\}$ . (b) Residual sum of squares versus subset size, on the prostate cancer data set. The lower envelope is the best RSS achievable for any set of a given size. Based on Figure 3.5 of (Hastie et al. 2001). Figure generated by prostateSubsets.

each point. This requires fitting the model (i.e., computing argmax  $p(\mathcal{D}|\mathbf{w})$ ), or evaluating its marginal likelihood (i.e., computing  $\int p(\mathcal{D}|\mathbf{w})p(\mathbf{w})d\mathbf{w}$ ) at each step. This is sometimes called the **wrapper method**, since we "wrap" our search for the best model (or set of good models) around a generic model-fitting procedure.

In order to make wrapper methods efficient, it is important that we can quickly evaluate the score function for some new model, *γ* , given the score of a previous model, *γ*. This can be done provided we can efficiently update the sufficient statistics needed to compute  $f(\gamma)$ . This is possible provided  $\gamma'$  only differs from  $\gamma$  in one bit (corresponding to adding or removing a single variable), and provided  $f(\gamma)$  only depends on the data via  $X_{\gamma}$ . In this case, we can use rank-one matrix updates/ downdates to efficiently compute  $X_{\gamma}^T X_{\gamma}$  from  $X_{\gamma}^T X_{\gamma}$ . These updates are usually applied to the QR decomposition of **X**. See e.g., (Miller 2002; Schniter et al. 2008) for details.

#### **13.2.3.1 Greedy search**

Suppose we want to find the MAP model. If we use the  $\ell_0$ -regularized objective in Equation 13.27, we can exploit properties of least squares to derive various efficient greedy forwards search methods, some of which we summarize below. For further details, see (Miller 2002; Soussen et al. 2010).

- **Single best replacement** The simplest method is to use greedy hill climbing, where at each step, we define the neighborhood of the current model to be all models than can be reached by flipping a single bit of  $\gamma$ , i.e., for each variable, if it is currently out of the model, we consider adding it, and if it is currently in the model, we consider removing it. In (Soussen et al. 2010), they call this the **single best replacement** (SBR). Since we are expecting a sparse solution, we can start with the empty set,  $\gamma = 0$ . We are essentially moving through the lattice of subsets, shown in Figure 13.2(a). We continue adding or removing until no improvement is possible.
- **Orthogonal least squares** If we set  $\lambda = 0$  in Equation 13.27, so there is no complexity penalty, there will be no reason to perform deletion steps. In this case, the SBR algorithm is equivalent to **orthogonal least squares** (Chen and Wigger 1995), which in turn is equivalent

to greedy **forwards selection**. In this algorithm, we start with the empty set and add the best feature at each step. The error will go down monotonically with  $||\gamma||_0$ , as shown in Figure 13.2(b). We can pick the next best feature  $j^*$  to add to the current set  $\gamma_t$  by solving

$$
j^* = \arg\min_{j \notin \gamma_t} \min_{\mathbf{w}} ||\mathbf{y} - (\mathbf{X}_{\gamma_t \cup j})\mathbf{w}||^2
$$
\n(13.28)

We then update the active set by setting  $\gamma^{(t+1)} = \gamma^{(t)} \cup \{j^*\}$ . To choose the next feature to add at step t, we need to solve  $D - D_t$  least squares problems at step t, where  $D_t = |\gamma_t|$  is the cardinality of the current active set. Having chosen the best feature to add, we need to solve an additional least squares problem to compute  $w_{t+1}$ ).

• **Orthogonal matching pursuits** Orthogonal least squares is somewhat expensive. A simplification is to "freeze" the current weights at their current value, and then to pick the next feature to add by solving

$$
j^* = \arg\min_{j \notin \gamma_t} \min_{\beta} ||\mathbf{y} - \mathbf{X} \mathbf{w}_t - \beta \mathbf{x}_{:,j}||^2
$$
\n(13.29)

This inner optimization is easy to solve: we simply set  $\beta = \mathbf{x}_{:,j}^T \mathbf{r}_t / ||\mathbf{x}_{:,j}||^2$ , where  $\mathbf{r}_t =$  $\mathbf{y} - \mathbf{X} \mathbf{w}_t$  is the current residual vector. If the columns are unit norm, we have

$$
j^* = \arg \max \mathbf{x}_{:,j}^T \mathbf{r}_t \tag{13.30}
$$

so we are just looking for the column that is most correlated with the current residual. We then update the active set, and compute the new least squares estimate  $w_{t+1}$  using  $X_{\gamma_{t+1}}$ . This method is called **orthogonal matching pursuits** or **OMP** (Mallat et al. 1994). This only requires one least squares calculation per iteration and so is faster than orthogonal least squares, but is not quite as accurate (Blumensath and Davies 2007).

- **Matching pursuits** An even more aggressive approximation is to just greedily add the feature that is most correlated with the current residual. This is called **matching pursuits** (Mallat and Zhang 1993). This is also equivalent to a method known as least squares boosting (Section 16.4.6).
- **Backwards selection Backwards selection** starts with all variables in the model (the socalled **saturated model**), and then deletes the worst one at each step. This is equivalent to performing a greedy search from the top of the lattice downwards. This can give better results than a bottom-up search, since the decision about whether to keep a variable or not is made in the context of all the other variables that might depende on it. However, this method is typically infeasible for large problems, since the saturated model will be too expensive to fit.
- **FoBa** The **forwards-backwards algorithm** of (Zhang 2008) is similar to the single best replacement algorithm presented above, except it uses an OMP-like approximation when choosing the next move to make. A similar "dual-pass" algorithm was described in (Moghaddam et al. 2008).
- **Bayesian Matching pursuit** The algorithm of (Schniter et al. 2008) is similiar to OMP except it uses a Bayesian marginal likelihood scoring criterion (under a spike and slab model) instead of a least squares objective. In addition, it uses a form of beam search to explore multiple paths through the lattice at once.

#### ********** Stochastic search**

If we want to approximate the posterior, rather than just computing a mode (e.g. because we want to compute marginal inclusion probabilities), one option is to use MCMC. The standard approach is to use Metropolis Hastings, where the proposal distribution just flips single bits. This enables us to efficiently compute  $p(\gamma'|D)$  given  $p(\gamma|D)$ . The probability of a state (bit configuration) is estimated by counting how many times the random walk visits this state. See (O'Hara and Sillanpaa 2009) for a review of such methods, and (Bottolo and Richardson 2010) for a very recent method based on evolutionary MCMC.

However, in a discrete state space, MCMC is needlessly inefficient, since we can compute the (unnormalized) probability of a state directly using  $p(\gamma, D) = \exp(-f(\gamma))$ ; thus *there is no need to ever revisit a state*. A much more efficient alternative is to use some kind of stochastic search algorithm, to generate a set S of high scoring models, and then to make the following approximation

$$
p(\gamma|\mathcal{D}) \approx \frac{e^{-f(\gamma)}}{\sum_{\gamma' \in \mathcal{S}} e^{-f(\gamma')}} \tag{13.31}
$$

See (Heaton and Scott 2009) for a review of recent methods of this kind.

#### ********** EM and variational inference \***

It is tempting to apply EM to the spike and slab model, which has the form  $\gamma_j \to w_j \to \mathbf{y}$ . We can compute  $p(\gamma_i = 1|w_j)$  in the E step, and optimize **w** in the M step. However, this will not work, because when we compute  $p(\gamma_j = 1|w_j)$ , we are comparing a delta-function,  $\delta_0(w_j)$ , with a Gaussian pdf,  $\mathcal{N}(w_j | 0, \sigma_w^2)$ . We can replace the delta function with a narrow Gaussian, and then the E step amounts to classifying  $w_i$  under the two possible Gaussian models. However, this is likely to suffer from severe local minima.

An alternative is to apply EM to the Bernoulli-Gaussian model, which has the form  $\gamma_i \to \mathbf{y} \leftarrow$  $w_i$ . In this case, the posterior  $p(\gamma | \mathcal{D}, \mathbf{w})$  is intractable to compute because all the bits become correlated due to explaining away. However, it is possible to derive a mean field approximation of the form  $\prod_{i} q(\gamma_i)q(w_j)$  (Huang et al. 2007; Rattray et al. 2009).

# **13.3** **1 regularization: basics**

When we have many variables, it is computationally difficult to find the posterior mode of  $p(\gamma|\mathcal{D})$ . And although greedy algorithms often work well (see e.g., (Zhang 2008) for a theoretical analysis), they can of course get stuck in local optima.

Part of the problem is due to the fact that the  $\gamma_i$  variables are discrete,  $\gamma_i \in \{0, 1\}$ . In the optimization community, it is common to relax hard constraints of this form by replacing discrete variables with continuous variables. We can do this by replacing the spike-and-slab style prior, that assigns finite probability mass to the event that  $w_j = 0$ , to continuous priors that "encourage"  $w_i = 0$  by putting a lot of probability density near the origin, such as a zero-mean Laplace distribution. This was first introduced in Section 7.4 in the context of robust linear regression. There we exploited the fact that the Laplace has heavy tails. Here we exploit the fact

Image /page/9/Figure/1 description: The image displays two plots side-by-side, each illustrating a concept in optimization or statistics. Both plots feature a set of concentric red ellipses, representing level sets of a function, with a black dot labeled "ŵ" at their center. To the left, a blue diamond shape is positioned in the lower-left quadrant, intersecting the ellipses. To the right, a blue circle is centered at the origin, also intersecting the ellipses. The plots use a standard Cartesian coordinate system with an x-axis and a y-axis.

**Figure 13.3** Illustration of  $\ell_1$  (left) vs  $\ell_2$  (right) regularization of a least squares problem. Based on Figure 3.12 of (Hastie et al. 2001).

that it has a spike near  $\mu = 0$ . More precisely, consider a prior of the form

$$
p(\mathbf{w}|\lambda) = \prod_{j=1}^{D} \text{Lap}(w_j|0, 1/\lambda) \propto \prod_{j=1}^{D} e^{-\lambda|w_j|}
$$
\n(13.32)

We will use a uniform prior on the offset term,  $p(w_0) \propto 1$ . Let us perform MAP estimation with this prior. The penalized negative log likelihood has the form

$$
f(\mathbf{w}) = -\log p(\mathcal{D}|\mathbf{w}) - \log p(\mathbf{w}|\lambda) = \text{NLL}(\mathbf{w}) + \lambda ||\mathbf{w}||_1
$$
\n(13.33)

where  $||\mathbf{w}||_1 = \sum_{j=1}^D |w_j|$  is the  $\ell_1$  norm of **w**. For suitably large  $\lambda$ , the estimate  $\hat{\mathbf{w}}$  will be sparse, for reasons we explain below. Indeed, this can be thought of as a convex approximation to the non-convex  $\ell_0$  objective

$$
\underset{\mathbf{w}}{\operatorname{argmin}} \text{NLL}(\mathbf{w}) + \lambda ||\mathbf{w}||_0 \tag{13.34}
$$

In the case of linear regression, the  $\ell_1$  objective becomes

$$
f(\mathbf{w}) = \sum_{i=1}^{N} -\frac{1}{2\sigma^2} (y_i - (w_0 + \mathbf{w}^T \mathbf{x}_i))^2 + \lambda ||\mathbf{w}||_1
$$
\n(13.35)

$$
= \text{RSS}(\mathbf{w}) + \lambda' ||\mathbf{w}||_1 \tag{13.36}
$$

where  $\lambda' = 2\lambda\sigma^2$ . This method is known as **basis pursuit denoising** or **BPDN** (Chen et al. 1998). The reason for this term will become clear later. In general, the technique of putting a zero-mean Laplace prior on the parameters and performing MAP estimation is called  $\ell_1$  **regularization**. It can be combined with any convex or non-convex NLL term. Many different algorithms have been devised for solving such problems, some of which we review in Section 13.4.

#### 13.3.1 Why does $\ell_1$ regularization yield sparse solutions?

We now explain why  $\ell_1$  regularization results in sparse solutions, whereas  $\ell_2$  regularization does not. We focus on the case of linear regression, although similar arguments hold for logistic regression and other GLMs.

The objective is the following non-smooth objective function:

$$
\min_{\mathbf{w}} \text{RSS}(\mathbf{w}) + \lambda ||\mathbf{w}||_1 \tag{13.37}
$$

We can rewrite this as a constrained but smooth objective (a quadratic function with linear constraints):

$$
\min_{\mathbf{w}} \text{RSS}(\mathbf{w}) \quad \text{s.t.} \quad ||\mathbf{w}||_1 \le B \tag{13.38}
$$

where B is an upper bound on the  $\ell_1$ -norm of the weights: a small (tight) bound B corresponds to a large penalty  $\lambda$ , and vice versa.<sup>2</sup> Equation 13.38 is known as **lasso**, which stands for "least" absolute shrinkage and selection operator" (Tibshirani 1996). We will see why it has this name later.

Similarly, we can write ridge regression

$$
\min_{\mathbf{w}} \text{RSS}(\mathbf{w}) + \lambda ||\mathbf{w}||_2^2 \tag{13.39}
$$

or as a bound constrained form:

$$
\min_{\mathbf{w}} \text{RSS}(\mathbf{w}) \qquad \text{s.t.} \quad ||\mathbf{w}||_2^2 \le B \tag{13.40}
$$

In Figure 13.3, we plot the contours of the RSS objective function, as well as the contours of the  $\ell_2$  and  $\ell_1$  constraint surfaces. From the theory of constrained optimization, we know that the optimal solution occurs at the point where the lowest level set of the objective function intersects the constraint surface (assuming the constraint is active). It should be geometrically clear that as we relax the constraint B, we "grow" the  $\ell_1$  "ball" until it meets the objective; the corners of the ball are more likely to intersect the ellipse than one of the sides, especially in high dimensions, because the corners "stick out" more. The corners correspond to sparse solutions, which lie on the coordinate axes. By contrast, when we grow the  $\ell_2$  ball, it can intersect the objective at any point; there are no "corners", so there is no preference for sparsity.

To see this another away, notice that, with ridge regression, the prior cost of a sparse solution, such as  $\mathbf{w} = (1, 0)$ , is the same as the cost of a dense solution, such as  $\mathbf{w} = (1/\sqrt{2}, 1/\sqrt{2})$ , as long as they have the same  $\ell_2$  norm:

$$
||(1,0)||_2 = ||(1/\sqrt{2}, 1/\sqrt{2}||_2 = 1
$$
\n(13.41)

However, for lasso, setting  $\mathbf{w} = (1,0)$  is cheaper than setting  $\mathbf{w} = (1/\sqrt{2}, 1/\sqrt{2})$ , since

$$
||(1,0)||_1 = 1 < ||(1/\sqrt{2}, 1/\sqrt{2})||_1 = \sqrt{2}
$$
\n(13.42)

The most rigorous way to see that  $\ell_1$  regularization results in sparse solutions is to examine conditions that hold at the optimum. We do this in Section 13.3.2.

#### **13.3.2 Optimality conditions for lasso**

The lasso objective has the form

$$
f(\boldsymbol{\theta}) = \text{RSS}(\boldsymbol{\theta}) + \lambda ||\mathbf{w}||_1
$$
\n(13.43)

<sup>2.</sup> Equation 13.38 is an example of a **quadratic program** or **QP**, since we have a quadratic objective subject to linear inequality constraints. Its Lagrangian is given by Equation 13.37.

Image /page/11/Figure/1 description: The image displays a graph illustrating the concept of a tangent line and secant lines. The x-axis is labeled with points X0 and X. The y-axis is not explicitly labeled but shows values increasing upwards. A blue curve represents a function f(x). Two red lines, labeled 'c' and 'c'', are shown as secant lines passing through a point on the blue curve at X0. The line 'c' also passes through a point on the blue curve at X. A vertical dashed line connects X0 on the x-axis to the point on the blue curve at X0. A vertical solid line connects X on the x-axis to the point on the blue curve at X. A horizontal dashed line connects the point on the blue curve at X to the line 'c'. An arrow indicates the difference f(x) - f(x0) between the y-values of the function at X and X0. Another arrow indicates the difference c(x - x0) between the y-value on the secant line 'c' at X and the y-value on the secant line 'c' at X0. The line 'c'' is steeper than the line 'c'.

**Figure 13.4** Illustration of some sub-derivatives of a function at point  $x_0$ . Based on a figure at http: //en.wikipedia.org/wiki/Subderivative. Figure generated by subgradientPlot.

Unfortunately, the  $||\mathbf{w}||_1$  term is not differentiable whenever  $w_j = 0$ . This is an example of a **non-smooth** optimization problem.

To handle non-smooth functions, we need to extend the notion of a derivative. We define a **subderivative** or **subgradient** of a (convex) function  $f : \mathcal{I} \to \mathbb{R}$  at a point  $\theta_0$  to be a scalar g such that

$$
f(\theta) - f(\theta_0) \ge g(\theta - \theta_0) \quad \forall \theta \in \mathcal{I}
$$
\n(13.44)

where  $\mathcal I$  is some interval containing  $\theta_0$ . See Figure 13.4 for an illustration.<sup>3</sup> We define the *set* of subderivatives as the interval  $[a, b]$  where a and b are the one-sided limits

$$
a = \lim_{\theta \to \theta_0^-} \frac{f(\theta) - f(\theta_0)}{\theta - \theta_0}, \quad b = \lim_{\theta \to \theta_0^+} \frac{f(\theta) - f(\theta_0)}{\theta - \theta_0}
$$
\n(13.46)

The set  $[a, b]$  of all subderivatives is called the **subdifferential** of the function f at  $\theta_0$  and is denoted  $\partial f(\theta)|_{\theta_0}$ . For example, in the case of the absolute value function  $f(\theta) = |\theta|$ , the subderivative is given by

$$
\partial f(\theta) = \begin{cases} \{-1\} & \text{if } \theta < 0 \\ [-1, 1] & \text{if } \theta = 0 \\ \{+1\} & \text{if } \theta > 0 \end{cases}
$$
 (13.47)

If the function is everywhere differentiable, then  $\partial f(\theta) = \{\frac{df(\theta)}{d\theta}\}\.$  By analogy to the standard calculus result, one can show that the point  $\hat{\theta}$  is a local minimum of f iff  $0 \in \partial f(\theta)|\hat{\theta}$ .

$$
f(\boldsymbol{\theta}) - f(\boldsymbol{\theta}_0) \ge (\boldsymbol{\theta} - \boldsymbol{\theta}_0)^T \mathbf{g}
$$
\n(13.45)

so **g** is a linear lower bound to the function at  $\theta_0$ .

<sup>3.</sup> In general, for a vector valued function, we say that **g** is a subgradient of f at  $\theta_0$  if for all vectors  $\theta$ ,

Image /page/12/Figure/1 description: The image displays two plots, labeled (a) and (b), each showing two lines, one black and one red, plotted against the variable c\_k on the x-axis. Both plots have a vertical y-axis and a horizontal x-axis. In plot (a), both the black and red lines are straight and have a positive slope, with the red line consistently below the black line. Both lines pass through the origin. In plot (b), the black line is a straight line with a positive slope passing through the origin. The red line also has a positive slope but is characterized by a horizontal segment at y=0 for negative values of c\_k, and then it increases with a slope similar to the black line for positive values of c\_k. The red line in plot (b) also appears to be below the black line for positive c\_k.

**Figure 13.5** Left: soft thresholding. The flat region is the interval  $[-\lambda, +\lambda]$ . Right: hard thresholding.

Let us apply these concepts to the lasso problem. Let us initially ignore the non-smooth penalty term. One can show (Exercise 13.1) that

$$
\frac{\partial}{\partial w_j} \text{RSS}(\mathbf{w}) = a_j w_j - c_j \tag{13.48}
$$

$$
a_j = 2 \sum_{i=1}^{n} x_{ij}^2
$$
 (13.49)

$$
c_j = 2\sum_{i=1}^{n} x_{ij} (y_i - \mathbf{w}_{-j}^T \mathbf{x}_{i,-j})
$$
\n(13.50)

where  $\mathbf{w}_{-j}$  is  $\mathbf{w}$  without component j, and similarly for  $\mathbf{x}_{i,-j}$ . We see that  $c_j$  is (proportional to) the correlation between the j'th feature  $\mathbf{x}_{:,j}$  and the residual due to the other features, **r**−j = **y** − **X**<sub>:,−j</sub>**w**−j. Hence the magnitude of  $c_j$  is an indication of how relevant feature j is for predicting **y** (relative to the other features and the current parameters).

Adding in the penalty term, we find that the subderivative is given by

$$
\partial_{w_j} f(\mathbf{w}) = (a_j w_j - c_j) + \lambda \partial_{w_j} ||\mathbf{w}||_1 \quad (13.51)
$$

$$
= \begin{cases}
\{a_j w_j - c_j - \lambda\} & \text{if } w_j < 0 \\ 
[-c_j - \lambda, -c_j + \lambda] & \text{if } w_j = 0 \\ 
\{a_j w_j - c_j + \lambda\} & \text{if } w_j > 0
\end{cases} \quad (13.52)
$$

We can write this in a more compact fashion as follows:

$$
\mathbf{X}^T (\mathbf{X} \mathbf{w} - \mathbf{y})_j \in \begin{cases} \{-\lambda\} & \text{if } w_j < 0 \ [-\lambda, \lambda] & \text{if } w_j = 0 \ \{\lambda\} & \text{if } w_j > 0 \end{cases}
$$
(13.53)

Depending on the value of  $c_j$ , the solution to  $\partial_{w_j} f(\mathbf{w})=0$  can occur at 3 different values of  $w_i$ , as follows:

- 1. If  $c_i < -\lambda$ , so the feature is strongly negatively correlated with the residual, then the subgradient is zero at  $\hat{w}_j = \frac{c_j + \lambda}{a_j} < 0$ .
- 2. If  $c_j \in [-\lambda, \lambda]$ , so the feature is only weakly correlated with the residual, then the subgradient is zero at  $\hat{w}_i = 0$ .
- 3. If  $c_j > \lambda$ , so the feature is strongly positively correlated with the residual, then the subgradient is zero at  $\hat{w}_j = \frac{c_j - \lambda}{a_j} > 0$ .

In summary, we have

$$
\hat{w}_j(c_j) = \begin{cases} (c_j + \lambda)/a_j & \text{if } c_j < -\lambda \\ 0 & \text{if } c_j \in [-\lambda, \lambda] \\ (c_j - \lambda)/a_j & \text{if } c_j > \lambda \end{cases}
$$
 (13.54)

We can write this as follows:

$$
\hat{w}_j = \operatorname{soft}(\frac{c_j}{a_j}; \frac{\lambda}{a_j}) \tag{13.55}
$$

where

$$
\operatorname{soft}(a;\delta) \quad \triangleq \quad \operatorname{sign}(a) \left( |a| - \delta \right)_+ \tag{13.56}
$$

and  $x_+ = \max(x, 0)$  is the positive part of x. This is called **soft thresholding**. This is illustrated in Figure 13.5(a), where we plot  $\hat{w}_i$  vs  $c_i$ . The dotted line is the line  $w_i = c_i/a_i$ corresponding to the least squares fit. The solid line, which represents the regularized estimate  $\hat{w}_j(c_j)$ , shifts the dotted line down (or up) by  $\lambda$ , except when  $-\lambda \leq c_j \leq \lambda$ , in which case it sets  $w_i = 0$ .

By contrast, in Figure 13.5(b), we illustrate **hard thresholding**. This sets values of  $w_j$  to 0 if  $-\lambda \leq c_j \leq \lambda$ , but it does not shrink the values of  $w_j$  outside of this interval. The slope of the soft thresholding line does not coincide with the diagonal, which means that even large coefficients are shrunk towards zero; consequently lasso is a biased estimator. This is undesirable, since if the likelihood indicates (via  $c_j$ ) that the coefficient  $w_j$  should be large, we do not want to shrink it. We will discuss this issue in more detail in Section 13.6.2.

Now we finally can understand why Tibshirani invented the term "lasso" in (Tibshirani 1996): it stands for "least absolute selection and shrinkage operator", since it selects a subset of the variables, and shrinks all the coefficients by penalizing the absolute values. If  $\lambda = 0$ , we get the OLS solution (of minimal  $\ell_1$  norm). If  $\lambda \geq \lambda_{max}$ , we get  $\hat{\mathbf{w}} = \mathbf{0}$ , where

$$
\lambda_{max} = ||\mathbf{X}^T \mathbf{y}||_{\infty} = \max_{j} |\mathbf{y}^T \mathbf{x}_{:,j}|
$$
\n(13.57)

This value is computed using the fact that **0** is optimal if  $(\mathbf{X}^T \mathbf{y})_j \in [-\lambda, \lambda]$  for all j. In general, the maximum penalty for an  $\ell_1$  regularized objective is

$$
\lambda_{max} = \max_{j} |\nabla_j NLL(\mathbf{0})| \tag{13.58}
$$

## **13.3.3 Comparison of least squares, lasso, ridge and subset selection**

We can gain further insight into  $\ell_1$  regularization by comparing it to least squares, and  $\ell_2$  and  $\ell_0$  regularized least squares. For simplicity, assume all the features of **X** are orthonormal, so  $X^T X = I$ . In this case, the RSS is given by

$$
RSS(\mathbf{w}) = ||\mathbf{y} - \mathbf{X}\mathbf{w}||^2 = \mathbf{y}^T \mathbf{y} + \mathbf{w}^T \mathbf{X}^T \mathbf{X} \mathbf{w} - 2\mathbf{w}^T \mathbf{X}^T \mathbf{y}
$$
(13.59)

$$
= \cosh \frac{1}{2} \sum_{k} w_k^2 - 2 \sum_{k} \sum_{i} w_k x_{ik} y_i \tag{13.60}
$$

so we see this factorizes into a sum of terms, one per dimension. Hence we can write down the MAP and ML estimates analytically, as follows:

• **MLE** The OLS solution is given by

$$
\hat{w}_k^{OLS} = \mathbf{x}_{:k}^T \mathbf{y} \tag{13.61}
$$

where  $\mathbf{x}_{:k}$  is the k'th column of **X**. This follows trivially from Equation 13.60. We see that  $\hat{w}_k^{OLS}$  is just the orthogonal projection of feature k onto the response vector (see Section 7.3.2).

• **Ridge** One can show that the ridge estimate is given by

$$
\hat{w}_k^{ridge} = \frac{\hat{w}_k^{OLS}}{1+\lambda} \tag{13.62}
$$

• Lasso From Equation 13.55, and using the fact that  $a_k = 2$  and  $\hat{w}_k^{OLS} = c_k/2$ , we have

$$
\hat{w}_k^{lasso} = \text{sign}(\hat{w}_k^{OLS}) \left( |\hat{w}_k^{OLS}| - \frac{\lambda}{2} \right)_+ \tag{13.63}
$$

This corresponds to soft thresholding, shown in Figure 13.5(a).

• **Subset selection** If we pick the best K features using subset selection, the parameter estimate is as follows

$$
\hat{w}_k^{SS} = \begin{cases}\n\hat{w}_k^{OLS} & \text{if } \text{rank}(|w_k^{OLS}|) \le K \\
0 & \text{otherwise}\n\end{cases}
$$
\n(13.64)

where rank refers to the location in the sorted list of weight magnitudes. This corresponds to hard thresholding, shown in Figure 13.5(b).

Figure 13.6(a) plots the MSE vs  $\lambda$  for lasso for a degree 14 polynomial, and Figure 13.6(b) plots the MSE vs polynomial order. We see that lasso gives similar results to the subset selection method.

As another example, consider a data set concerning prostate cancer. We have  $D = 8$  features and  $N = 67$  training cases; the goal is to predict the log prostate-specific antigen levels (see (Hastie et al. 2009, p4) for more biological details). Table 13.1 shows that lasso gives better prediction accuracy (at least on this particular data set) than least squares, ridge, and best subset regression. (In each case, the strength of the regularizer was chosen by cross validation.) Lasso also gives rise to a sparse solution. Of course, for other problems, ridge may give better predictive accuracy. In practice, a combination of lasso and ridge, known as the elastic net, often performs best, since it provides a good combination of sparsity and regularization (see Section 13.5.3).

Image /page/15/Figure/1 description: This image contains two plots, labeled (a) and (b). Plot (a) is titled "performance of MLE" and shows the mean squared error (mse) on the y-axis against lambda on the x-axis. The x-axis values range from 103.249 down to 0. The plot displays two lines: a dotted blue line with square markers representing "train" data, and a solid red line with cross markers representing "test" data. The mse for both train and test data starts high and decreases as lambda decreases, with the test data mse leveling off around 10, while the train data mse continues to decrease to near 0. Plot (b) also shows mse on the y-axis against degree on the x-axis, with values ranging from 0 to 16. Similar to plot (a), it has "train" (dotted blue line with squares) and "test" (solid red line with crosses) data. The mse for both decreases sharply from degree 0 to 2, then the test data mse increases significantly from degree 14 onwards, while the train data mse remains low and relatively flat.

**Figure 13.6** (a) MSE vs  $\lambda$  for lasso for a degree 14 polynomial. Note that  $\lambda$  decreases as we move to the right. Figure generated by linregPolyLassoDemo. (b) MSE versus polynomial degree. Note that the model order increases as we move to the right. See Figure 1.18 for a plot of some of these polynomial regression models. Figure generated by linregPolyVsDegree.

| Term       | LS     | Best Subset | Ridge  | Lasso  |
|------------|--------|-------------|--------|--------|
| Intercept  | 2.452  | 2.481       | 2.479  | 2.480  |
| lcavol     | 0.716  | 0.651       | 0.656  | 0.653  |
| lweight    | 0.293  | 0.380       | 0.300  | 0.297  |
| age        | -0.143 | -0.000      | -0.129 | -0.119 |
| lbph       | 0.212  | -0.000      | 0.208  | 0.200  |
| svi        | 0.310  | -0.000      | 0.301  | 0.289  |
| lcp        | -0.289 | -0.000      | -0.260 | -0.236 |
| gleason    | -0.021 | -0.000      | -0.019 | 0.000  |
| pgg45      | 0.277  | 0.178       | 0.256  | 0.226  |
| Test Error | 0.586  | 0.572       | 0.580  | 0.564  |

Table 13.1 Results of different methods on the prostate cancer data, which has 8 features and 67 training cases. Methods are: LS = least squares, Subset = best subset regression, Ridge, Lasso. Rows represent the coefficients; we see that subset regression and lasso give sparse solutions. Bottom row is the mean squared error on the test set (30 cases). Based on Table 3.3. of (Hastie et al. 2009). Figure generated by prostateComparison.

## **13.3.4 Regularization path**

As we increase  $\lambda$ , the solution vector  $\hat{\mathbf{w}}(\lambda)$  will tend to get sparser, although not necessarily monotonically. We can plot the values  $\hat{w}_i(\lambda)$  vs  $\lambda$  for each feature j; this is known as the **regularization path**.

This is illustrated for ridge regression in Figure 13.7(a), where we plot  $\hat{w}_i(\lambda)$  as the regularizer  $\lambda$  decreases. We see that when  $\lambda = \infty$ , all the coefficients are zero. But for any finite value of  $\lambda$ , all coefficients are non-zero; furthermore, they increase in magnitude as  $\lambda$  is decreased.

In Figure 13.7(b), we plot the analogous result for lasso. As we move to the right, the upper bound on the  $\ell_1$  penalty, B, increases. When  $B = 0$ , all the coefficients are zero. As we increase

Image /page/16/Figure/1 description: The image contains two plots, labeled (a) and (b). Both plots show the relationship between a variable on the x-axis and a variable on the y-axis, with multiple lines representing different features. The y-axis ranges from -0.2 to 0.6 in plot (a) and -0.2 to 0.7 in plot (b). The x-axis ranges from 0 to 30 in plot (a) and 0 to 25 in plot (b). The legend in both plots indicates eight features: lcavol, lweight, age, lbph, svi, lcp, gleason, and pgg45. Plot (a) shows that most features start at 0 and increase as the x-axis value increases, with lcavol showing the steepest increase. The feature 'age' shows a decrease from around 0.05 to -0.15. Plot (b) shows a similar trend for most features, with lcavol reaching a value of approximately 0.65. The features lweight, lbph, and svi also show significant increases, while lcp and gleason show smaller increases. The feature 'age' decreases from 0 to approximately -0.15. A vertical red line is present in both plots, at x=23 in plot (a) and x=6 in plot (b).

**Figure 13.7** (a) Profiles of ridge coefficients for the prostate cancer example vs bound on  $\ell_2$  norm of **w**, so small t (large  $\lambda$ ) is on the left. The vertical line is the value chosen by 5-fold CV using the 1SE rule. Based on Figure 3.8 of (Hastie et al. 2009). Figure generated by ridgePathProstate. (b) Profiles of lasso coefficients for the prostate cancer example vs bound on  $\ell_1$  norm of **w**, so small t (large  $\lambda$ ) is on the left. Based on Figure 3.10 of (Hastie et al. 2009). Figure generated by lassoPathProstate.

Image /page/16/Figure/3 description: The image contains two plots, labeled (a) and (b). Both plots show the relationship between a variable on the x-axis and coefficients on the y-axis, with different colored lines representing different variables. Plot (a) has 'τ' on the x-axis, ranging from 0 to 2, and the y-axis ranges from -0.2 to 0.7. Plot (b) has 'lars step' on the x-axis, ranging from 1 to 9, and the y-axis also ranges from -0.2 to 0.7. A legend in the top left of each plot indicates that the lines represent 'lcavol' (blue), 'lweight' (green), 'age' (red), 'lbph' (teal), 'svi' (purple), 'lcp' (yellow), 'gleason' (gray), and 'pgg45' (dark blue). In plot (a), 'lcavol' starts at 0 and increases linearly to approximately 0.7 at τ=2. 'svi' starts at 0 and increases to approximately 0.22 at τ=1.5. 'lweight' starts at 0 and increases to approximately 0.15 at τ=1.5. 'lbph' starts at 0 and increases to approximately 0.1 at τ=1.5. 'age' starts at 0 and decreases to approximately -0.15 at τ=1.5. 'lcp' and 'gleason' remain at 0 for most of the range, with 'lcp' showing a slight increase towards the end. 'pgg45' is the same as 'lcavol' in this plot. In plot (b), the trends are similar but plotted against 'lars step'. 'lcavol' increases from 0 to approximately 0.7 at lars step 9. 'svi' increases from 0 to approximately 0.25 at lars step 9. 'lweight' increases from 0 to approximately 0.18 at lars step 9. 'lbph' increases from 0 to approximately 0.12 at lars step 9. 'age' decreases from 0 to approximately -0.15 at lars step 8. 'lcp' and 'gleason' remain close to 0, with 'lcp' showing a slight increase and 'gleason' a slight decrease towards the end. 'pgg45' is the same as 'lcavol' in this plot.

**Figure 13.8** Illustration of piecewise linearity of regularization path for lasso on the prostate cancer example. (a) We plot  $\hat{w}_i(B)$  vs B for the critical values of B. (b) We plot vs steps of the LARS algorithm. Figure generated by lassoPathProstate.

B, the coefficients gradually "turn on". But for any value between 0 and  $B_{max} = ||\hat{\mathbf{w}}_{OLS}||_1$ , the solution is sparse. $4$ 

Remarkably, it can be shown that the solution path is a piecewise linear function of  $B$  (Efron et al. 2004). That is, there are a set of critical values of  $B$  where the active set of non-zero coefficients changes. For values of  $B$  between these critical values, each non-zero coefficient increases or decreases in a linear fashion. This is illustrated in Figure 13.8(a). Furthermore, one can solve for these critical values analytically. This is the basis of the **LARS** algorithm (Efron et al. 2004), which stands for "least angle regression and shrinkage" (see Section 13.4.2 for details). Remarkably, LARS can compute the entire regularization path for roughly the same

<sup>4.</sup> It is common to plot the solution versus the **shrinkage factor**, defined as  $s(B) = B/B_{max}$ , rather than against B. This merely affects the scale of the horizontal axis, not the shape of the curves.

Image /page/17/Figure/1 description: This figure displays four plots comparing different signal reconstruction methods. The top plot, labeled "Original (D = 4096, number of nonzeros = 160)", shows a sparse signal with values of 1 and -1. The second plot, "L1 reconstruction (K0 = 1024, lambda = 0.0516, MSE = 0.0027)", shows a reconstruction of the original signal using L1 regularization. The third plot, "Debiased (MSE = 3.26e-005)", presents a debiased version of the signal. The bottom plot, "Minimum norm solution (MSE = 0.0292)", shows the result of a minimum norm solution. All plots have the x-axis ranging from 0 to 4000, and the y-axis ranges from -1 to 1 for the first three plots, and -0.5 to 0.5 for the last plot.

Figure 13.9 Example of recovering a sparse signal using lasso. See text for details. Based on Figure 1 of (Figueiredo et al. 2007). Figure generated by sparseSensingDemo, written by Mario Figueiredo.

computational cost as a single least squares fit (namely  $O(\min(ND^2, DN^2))$ .

In Figure 13.8(b), we plot the coefficients computed at each critical value of B. Now the piecewise linearity is more evident. Below we display the actual coefficient values at each step along the regularization path (the last line is the least squares solution):

| Listing I3.1 Output of lassoPathProstate |        |         |        |        |         |         |        |
|------------------------------------------|--------|---------|--------|--------|---------|---------|--------|
| 0                                        | 0      | 0       | 0      | 0      | 0       | 0       | 0      |
| 0.4279                                   | 0      | 0       | 0      | 0      | 0       | 0       | 0      |
| 0.5015                                   | 0.0735 | 0       | 0      | 0      | 0       | 0       | 0      |
| 0.5610                                   | 0.1878 | 0       | 0      | 0.0930 | 0       | 0       | 0      |
| 0.5622                                   | 0.1890 | 0       | 0.0036 | 0.0963 | 0       | 0       | 0      |
| 0.5797                                   | 0.2456 | 0       | 0.1435 | 0.2003 | 0       | 0       | 0.0901 |
| 0.5864                                   | 0.2572 | -0.0321 | 0.1639 | 0.2082 | 0       | 0       | 0.1066 |
| 0.6994                                   | 0.2910 | -0.1337 | 0.2062 | 0.3003 | -0.2565 | 0       | 0.2452 |
| 0.7164                                   | 0.2926 | -0.1425 | 0.2120 | 0.3096 | -0.2890 | -0.0209 | 0.2773 |

By changing B from 0 to  $B_{max}$ , we can go from a solution in which all the weights are zero to a solution in which all weights are non-zero. Unfortunately, not all subset sizes are achievable using lasso. One can show that, if  $D > N$ , the optimal solution can have at most N variables in it, before reaching the complete set corresponding to the OLS solution of minimal  $\ell_1$  norm. In Section 13.5.3, we will see that by using an  $\ell_2$  regularizer as well as an  $\ell_1$  regularizer (a method known as the elastic net), we can achieve sparse solutions which contain more variables than training cases. This lets us explore model sizes between  $N$  and  $D$ .

#### **13.3.5 Model selection**

It is tempting to use  $\ell_1$  regularization to estimate the set of relevant variables. In some cases, we can recover the true sparsity pattern of **w**∗, the parameter vector that generated the data. A method that can recover the true model in the  $N \to \infty$  limit is called **model selection consistent**. The details on which methods enjoy this property, and when, are beyond the scope of this book; see e.g., (Buhlmann and van de Geer 2011) for details.

Instead of going into a theoretical discussion, we will just show a small example. We first generate a sparse signal  $w^*$  of size  $D = 4096$ , consisting of 160 randomly placed  $\pm 1$  spikes. Next we generate a random design matrix **X** of size  $N \times D$ , where  $N = 1024$ . Finally we generate a noisy observation  $y = \mathbf{Xw}^* + \epsilon$ , where  $\epsilon_i \sim \mathcal{N}(0, 0.01^2)$ . We then estimate w from **y** and **X**.

The original  $w^*$  is shown in the first row of Figure 13.9. The second row is the  $\ell_1$  estimate  $\hat{\mathbf{w}}_{L1}$  using  $\lambda = 0.1\lambda_{max}$ . We see that this has "spikes" in the right places, but they are too small. The third row is the least squares estimate of the coefficients which are estimated to be non-zero based on supp( $\hat{w}_{L1}$ ). This is called **debiasing**, and is necessary because lasso shrinks the relevant coefficients as well as the irrelevant ones. The last row is the least squares estimate for all the coefficients jointly, ignoring sparsity. We see that the (debiased) sparse estimate is an excellent estimate of the original signal. By contrast, least squares without the sparsity assumption performs very poorly.

Of course, to perform model selection, we have to pick  $\lambda$ . It is common to use cross validation. However, it is important to note that cross validation is picking a value of  $\lambda$  that results in good predictive accuracy. This is not usually the same value as the one that is likely to recover the "true" model. To see why, recall that  $\ell_1$  regularization performs selection *and* shrinkage, that is, the chosen coefficients are brought closer to 0. In order to prevent relevant coefficients from being shrunk in this way, cross validation will tend to pick a value of  $\lambda$  that is not too large. Of course, this will result in a less sparse model which contains irrelevant variables (false positives). Indeed, it was proved in (Meinshausen and Buhlmann 2006) that the prediction-optimal value of  $\lambda$  does not result in model selection consistency. In Section 13.6.2, we will discuss some adaptive mechanisms for automatically tuning  $\lambda$  on a per-dimension basis that does result in model selection consistency.

A downside of using  $\ell_1$  regularization to select variables is that it can give quite different results if the data is perturbed slightly. The Bayesian approach, which estimates posterior marginal inclusion probabilities,  $p(\gamma_i = 1 | \mathcal{D})$ , is much more robust. A frequentist solution to this is to use bootstrap resampling (see Section 6.2.1), and to rerun the estimator on different versions of the data. By computing how often each variable is selected across different trials, we can approximate the posterior inclusion probabilities. This method is known as **stability selection** (Meinshausen and BÃijhlmann 2010).

We can threshold the stability selection (bootstrap) inclusion probabilities at some level, say 90%, and thus derive a sparse estimator. This is known as **bootstrap lasso** or **bolasso** (Bach 2008). It will include a variable if it occurs in at least 90% of sets returned by lasso (for a fixed  $\lambda$ ). This process of intersecting the sets is a way of eliminating the false positives that vanilla lasso produces. The theoretical results in (Bach 2008) prove that bolasso is model selection consistent under a wider range of conditions than vanilla lasso.

As an illustration, we reproduced the experiments in (Bach 2008). In particular, we created

Image /page/19/Figure/1 description: The image displays three plots labeled (a), (b), and (c). Plot (a) is a heatmap titled 'lasso on sign inconsistent data'. The x-axis is labeled '-log(λ)' ranging from 0 to 15, and the y-axis is labeled 'variable index' ranging from 2 to 16. The heatmap shows varying shades of gray, with darker shades indicating lower values and lighter shades indicating higher values, as indicated by the color bar on the right. Plot (b) is also a heatmap titled 'bolasso on sign inconsistent data' with '128 bootstraps'. It has the same axes as plot (a), with '-log(λ)' on the x-axis and 'variable index' on the y-axis. The color bar on the right indicates values from 0 to 1. Plot (c) is a line graph titled 'lasso vs bolasso on sign inconsistent data' with 'nbootstraps = [0,2,4,8,16,32,64,128,256]'. The x-axis is labeled '-log(λ)' ranging from 0 to 15, and the y-axis is labeled 'P(correct support)' ranging from 0 to 1. The plot shows a black solid line labeled 'lasso' and multiple dashed red lines labeled 'bolasso', each representing a different number of bootstraps. The dashed red lines show a peak probability of correct support around -log(λ) = 4, while the black line shows a much lower probability.

**Figure 13.10** (a) Probability of selection of each variable (white = large probabilities, black = small probabilities) vs. regularization parameter for Lasso. As we move from left to right, we decrease the amount of regularization, and therefore select more variables. (b) Same as (a) but for bolasso. (c) Probability of correct sign estimation vs. regularization parameter. Bolasso (red, dashed) and Lasso (black, plain): The number of bootstrap replications is in  $\{2, 4, 8, 16, 32, 64, 128, 256\}$ . Based on Figures 1-3 of (Bach 2008). Figure generated by bolassoDemo.

256 datasets of size  $N = 1000$  with  $D = 16$  variables, of which 8 are relevant. See (Bach 2008) for more detail on the experimental setup. For dataset n, variable j, and sparsity level k, define  $S(j, k, n) = \mathbb{I}(\hat{w}_j(\lambda_k, \mathcal{D}_n) \neq 0)$ . Now define  $P(j, k)$  be the average of  $S(j, k, n)$  over the 256 datasets. In Figure 13.10(a-b), we plot P vs  $-\log(\lambda)$  for lasso and bolasso. We see that for bolasso, there is a large range of  $\lambda$  where the true variables are selected, but this is not the case for lasso. This is emphasized in Figure 13.10(c), where we plot the empirical probability that the correct set of variables is recovered, for lasso and for bolasso with an increasing number of bootstrap samples. Of course, using more samples takes longer. In practice, 32 bootstraps seems to be a good compromise between speed and accuracy.

With bolasso, there is the usual issue of picking  $\lambda$ . Obviously we could use cross validation, but plots such as Figure 13.10(b) suggest another heuristic: shuffle the rows to create a large black block, and then pick  $\lambda$  to be in the middle of this region. Of course, operationalizing this intuition may be tricky, and will require various ad-hoc thresholds (it is reminiscent of the "find the knee in the curve" heuristic discussed in Section 11.5.2 when discussing how to pick  $K$  for mixture models). A Bayesian approach provides a more principled method for selecting  $\lambda$ .

## **13.3.6 Bayesian inference for linear models with Laplace priors**

We have been focusing on MAP estimation in sparse linear models. It is also possible to perform Bayesian inference (see e.g., (Park and Casella 2008; Seeger 2008)). However, the posterior mean and median, as well as samples from the posterior, are not sparse; only the mode is sparse. This is another example of the phenomenon discussed in Section 5.2.1, where we said that the MAP estimate is often untypical of the bulk of the posterior.

Another argument in favor of using the posterior mean comes from Equation 5.108, which showed that that plugging in the posterior mean, rather than the posterior mode, is the optimal thing to do if we want to minimize squared prediction error. (Schniter et al. 2008) shows experimentally, and (Elad and Yavnch 2009) shows theoretically, that using the posterior mean with a spike-and-slab prior results in better prediction accuracy than using the posterior mode with a Laplace prior, albeit at slightly higher computational cost.

# **13.4** $\ell_1$ **regularization: algorithms**

In this section, we give a brief review of some algorithms that can be used to solve  $\ell_1$  regularized estimation problems. We focus on the lasso case, where we have a quadratic loss. However, most of the algorithms can be extended to more general settings, such as logistic regression (see (Yaun et al. 2010) for a comprehensive review of  $\ell_1$  regularized logistic regression). Note that this area of machine learning is advancing very rapidly, so the methods below may not be state of the art by the time you read this chapter. (See (Schmidt et al. 2009; Yaun et al. 2010; Yang et al. 2010) for some recent surveys.)

## **13.4.1 Coordinate descent**

Sometimes it is hard to optimize all the variables simultaneously, but it easy to optimize them one by one. In particular, we can solve for the  $j'$ th coefficient with all the others held fixed:

$$
w_j^* = \underset{z}{\operatorname{argmin}} f(\mathbf{w} + z\mathbf{e}_j) - f(\mathbf{w})
$$
\n(13.65)

where  $\mathbf{e}_i$  is the j'th unit vector. We can either cycle through the coordinates in a deterministic fashion, or we can sample them at random, or we can choose to update the coordinate for which the gradient is steepest.

The coordinate descent method is particularly appealing if each one-dimensional optimization problem can be solved analytically For example, the **shooting** algorithm (Fu 1998; Wu and Lange 2008) for lasso uses Equation 13.54 to compute the optimal value of  $w_i$  given all the other coefficients. See Algorithm 7 for the pseudo code (and LassoShooting for some Matlab code).

See (Yaun et al. 2010) for some extensions of this method to the logistic regression case. The resulting algorithm was the fastest method in their experimental comparison, which concerned document classification with large sparse feature vectors (representing bags of words). Other types of data (e.g., dense features and/or regression problems) might call for different algorithms.

**Algorithm 13.1:** Coordinate descent for lasso (aka shooting algorithm)

 Initialize **w** =  $(\mathbf{X}^T \mathbf{X} + \lambda \mathbf{I})^{-1} \mathbf{X}^T \mathbf{y}$ ; **<sup>2</sup> repeat for**  $j = 1, ..., D$  **do**   $\begin{bmatrix} a_j = 2 \sum_{i=1}^n x_{ij}^2; \end{bmatrix}$  $c_j = 2 \sum_{i=1}^{n} x_{ij} (y_i - \mathbf{w}^T \mathbf{x}_i + w_j x_{ij})$ ;  $w_j = \operatorname{soft}(\frac{c_j}{a_j}, \frac{\lambda}{a_j});$ **until** *converged*;

## **13.4.2 LARS and other homotopy methods**

The problem with coordinate descent is that it only updates one variable at a time, so can be slow to converge. **Active set** methods update many variables at a time. Unfortunately, they are

more complicated, because of the need to identify which variables are constrained to be zero, and which are free to be updated.

Active set methods typically only add or remove a few variables at a time, so they can take a long if they are started far from the solution. But they are ideally suited for generating a set of solutions for different values of  $\lambda$ , starting with the empty set, i.e., for generating regularization path. These algorithms exploit the fact that one can quickly compute  $\hat{\mathbf{w}}(\lambda_k)$  from  $\hat{\mathbf{w}}(\lambda_{k-1})$ if  $\lambda_k \approx \lambda_{k-1}$ ; this is known as **warm starting**. In fact, even if we only want the solution for a single value of  $\lambda$ , call it  $\lambda_*$ , it can sometimes be computationally more efficient to compute a set of solutions, from  $\lambda_{max}$  down to  $\lambda_*$ , using warm-starting; this is called a **continuation method** or **homotopy** method. This is often much faster than directly "cold-starting" at  $\lambda_*$ ; this is particularly true if  $\lambda_*$  is small.

Perhaps the most well-known example of a homotopy method in machine learning is the **LARS** algorithm, which stands for "least angle regression and shrinkage" (Efron et al. 2004) (a similar algorithm was independently invented in (Osborne et al. 2000b,a)). This can compute  $\hat{\mathbf{w}}(\lambda)$  for all possible values of  $\lambda$  in an efficient manner.

LARS works as follows. It starts with a large value of  $\lambda$ , such that only the variable that is most correlated with the response vector **y** is chosen. Then  $\lambda$  is decreased until a second variable is found which has the same correlation (in terms of magnitude) with the current residual as the first variable, where the residual at step k is defined as  $\mathbf{r}_k = \mathbf{y} - \mathbf{X}_{:,\mathbb{F}_k} \mathbf{w}_k$ , where  $F_k$  is the current **active set** (c.f., Equation 13.50). Remarkably, one can solve for this new value of  $\lambda$  analytically, by using a geometric argument (hence the term "least angle"). This allows the algorithm to quickly "jump" to the next point on the regularization path where the active set changes. This repeats until all the variables are added.

It is necessary to allow variables to be removed from the active set if we want the sequence of solutions to correspond to the regularization path of lasso. If we disallow variable removal, we get a slightly different algorithm called **LAR**, which tends to be faster. In particular, LAR costs the same as a single ordinary least squares fit, namely  $O(ND \min(N, D))$ , which is  $O(ND^2)$ if  $N > D$ , and  $O(N^2D)$  if  $D > N$ . LAR is very similar to greedy forward selection, and a method known as least squares boosting (see Section 16.4.6).

There have been many attempts to extend the LARS algorithm to compute the full regularization path for  $\ell_1$  regularized GLMs, such as logistic regression. In general, one cannot analytically solve for the critical values of  $\lambda$ . Instead, the standard approach is to start at  $\lambda_{\text{max}}$ , and then slowly decrease λ, tracking the solution as we go; this is called a **continuation method** or **homotopy** method. These methods exploit the fact that we can quickly compute  $\hat{\mathbf{w}}(\lambda_k)$  from  $\hat{\mathbf{w}}(\lambda_{k-1})$  if  $\lambda_k \approx \lambda_{k-1}$ ; this is known as **warm starting**. Even if we don't want the full path, this method is often much faster than directly "cold-starting" at the desired value of  $\lambda$  (this is particularly true if  $\lambda$  is small).

The method described in (Friedman et al. 2010) combines coordinate descent with this warmstarting strategy, and computes the full regularization path for any  $\ell_1$  regularized GLM. This has been implemented in the glmnet package, which is bundled with PMTK.

# **13.4.3 Proximal and gradient projection methods**

In this section, we consider some methods that are suitable for very large scale problems, where homotopy methods made be too slow. These methods will also be easy to extend to other kinds of regularizers, beyond  $\ell_1$ , as we will see later. Our presentation in this section is based on (Vandenberghe 2011; Yang et al. 2010).

Consider a convex objective of the form

$$
f(\boldsymbol{\theta}) = L(\boldsymbol{\theta}) + R(\boldsymbol{\theta}) \tag{13.66}
$$

where  $L(\theta)$  (representing the loss) is convex and differentiable, and  $R(\theta)$  (representing the regularizer) is convex but not necessarily differentiable. For example,  $L(\theta) = \text{RSS}(\theta)$  and  $R(\theta) = \lambda ||\theta||_1$  corresponds to the BPDN problem. As another example, the lasso problem can be formulated as follows:  $L(\theta) = \text{RSS}(\theta)$  and  $R(\theta) = I_C(\theta)$ , where  $C = {\theta : ||\theta||_1 \leq B}$ , and  $I_C(\theta)$  is the indicator function of a convex set C, defined as

$$
I_C(\boldsymbol{\theta}) \triangleq \begin{cases} 0 & \boldsymbol{\theta} \in C \\ +\infty & \text{otherwise} \end{cases}
$$
 (13.67)

In some cases, it is easy to optimize functions of the form in Equation 13.66. For example, suppose  $L(\theta) = \text{RSS}(\theta)$ , and the design matrix is simply  $X = I$ . Then the obective becomes  $f(\theta) = R(\theta) + \frac{1}{2} ||\theta - \mathbf{y}||_2^2$ . The minimizer of this is given by  $\text{prox}_R(\mathbf{y})$ , which is the **proximal operator** for the convex function R, defined by

$$
\text{prox}_R(\mathbf{y}) = \underset{\mathbf{z}}{\text{argmin}} \left( R(\mathbf{z}) + \frac{1}{2} ||\mathbf{z} - \mathbf{y}||_2^2 \right) \tag{13.68}
$$

Intuitively, we are returning a point that minimizes R but which is also close (proximal) to **y**. In general, we will use this operator inside an iterative optimizer, in which case we want to stay close to the previous iterate. In this case, we use

$$
\text{prox}_{R}(\boldsymbol{\theta}_k) = \underset{\mathbf{z}}{\text{argmin}} \left( R(\mathbf{z}) + \frac{1}{2} ||\mathbf{z} - \boldsymbol{\theta}_k||_2^2 \right)
$$
(13.69)

The key issues are: how do we efficiently compute the proximal operator for different regularizers  $R$ , and how do we extend this technique to more general loss functions  $L<sup>2</sup>$ . We discuss these issues below.

#### **13.4.3.1 Proximal operators**

If  $R(\theta) = \lambda ||\theta||_1$ , the proximal operator is given by componentwise soft-thresholding:

$$
\text{prox}_R(\boldsymbol{\theta}) = \text{soft}(\boldsymbol{\theta}, \lambda) \tag{13.70}
$$

as we showed in Section 13.3.2. If  $R(\theta) = \lambda ||\theta||_0$ , the proximal operator is given by componentwise hard-thresholding:

$$
\text{prox}_R(\boldsymbol{\theta}) = \text{hard}(\boldsymbol{\theta}, \sqrt{2\lambda})
$$
\n(13.7)

where  $\text{hard}(u, a) \triangleq u \mathbb{I}(|u| > a)$ .

If  $R(\theta) = I_C(\theta)$ , the proximal operator is given by the projection onto the set C:

$$
\text{prox}_{R}(\boldsymbol{\theta}) = \underset{\mathbf{z} \in C}{\text{argmin}} ||\mathbf{z} - \boldsymbol{\theta}||_{2}^{2} = \text{proj}_{C}(\boldsymbol{\theta})
$$
\n(13.72)

Image /page/23/Figure/1 description: This is a diagram illustrating a concept in optimization, likely related to constrained optimization or projected gradient descent. The image shows a set of contour lines representing the function f(theta). Inside these contours, there is a shaded blue polygon labeled "Feasible Set". A point labeled "theta\_k" is located within the feasible set. A vector originating from "theta\_k" and pointing outside the feasible set is shown, labeled "d\_k". Another point, "theta\_k - g\_k", is shown outside the feasible set, and a dotted line connects it to a point labeled "P(theta\_k - g\_k)" on the boundary of the feasible set. A green line connects "theta\_k" to "theta\_k - g\_k", and a yellow line connects "theta\_k" to "P(theta\_k - g\_k)". The diagram visually represents the projection of a point onto a feasible set in the context of optimizing a function.

**Figure 13.11** Illustration of projected gradient descent. The step along the negative gradient, to  $\theta_k - \mathbf{g}_k$ , takes us outside the feasible set. If we project that point onto the closest point in the set we get  $\theta_{k+1} = \text{proj}_{\Theta}(\theta_k - \mathbf{g}_k)$ . We can then derive the implicit update direction using  $\mathbf{d}_k = \theta_{k+1} - \theta_k$ . Used with kind permission of Mark Schmidt.

For some convex sets, it is easy to compute the projection operator. For example, to project onto the rectangular set defined by the box constraints  $C = \{ \theta : \ell_j \leq \theta_j \leq u_j \}$  we can use

$$
\text{proj}_C(\boldsymbol{\theta})_j = \begin{cases} \ell_j & \theta_j \le \ell_j \\ \theta_j & \ell_j \le \theta_j \le u_j \\ u_j & \theta_j \ge u_j \end{cases}
$$
(13.73)

To project onto the Euclidean ball  $C = {\theta : ||\theta||_2 \le 1}$  we can use

$$
\text{proj}_C(\boldsymbol{\theta}) = \begin{cases} \n\frac{\boldsymbol{\theta}}{\|\boldsymbol{\theta}\|_2} & \|\boldsymbol{\theta}\|_2 > 1 \\ \n\boldsymbol{\theta} & \|\boldsymbol{\theta}\|_2 \le 1 \n\end{cases} \tag{13.74}
$$

To project onto the 1-norm ball  $C = {\theta : ||\theta||_1 \le 1}$  we can use

$$
\text{proj}_C(\boldsymbol{\theta}) = \text{soft}(\boldsymbol{\theta}, \lambda) \tag{13.75}
$$

where  $\lambda = 0$  if  $||\boldsymbol{\theta}||_1 \leq 1$ , and otherwise  $\lambda$  is the solution to the equation

$$
\sum_{j=1}^{D} \max(|\theta_j| - \lambda, 0) = 1
$$
\n(13.76)

We can implement the whole procedure in  $O(D)$  time, as explained in (Duchi et al. 2008). We will see an application of these different projection methods in Section 13.5.1.2.

#### **13.4.3.2 Proximal gradient method**

We now discuss how to use the proximal operator inside of a gradient descent routine. The basic idea is to minimize a simple quadratic approximation to the loss function, centered on the

$$
\boldsymbol{\theta}_k:
$$

$$
\boldsymbol{\theta}_{k+1} = \underset{\mathbf{z}}{\operatorname{argmin}} R(\mathbf{z}) + L(\boldsymbol{\theta}_k) + \mathbf{g}_k^T(\mathbf{z} - \boldsymbol{\theta}_k) + \frac{1}{2t_k} ||\mathbf{z} - \boldsymbol{\theta}_k||_2^2
$$
(13.77)

where  $\mathbf{g}_k = \nabla L(\boldsymbol{\theta}_k)$  is the gradient of the loss,  $t_k$  is a constant discussed below, and the last term arises from a simple approximation to the Hessian of the loss of the form  $\nabla^2 L(\theta_k) \approx \frac{1}{t_k} \mathbf{I}$ .

Dropping terms that are independent of **z**, and multiplying by  $t_k$ , we can rewrite the above expression in terms of a proximal operator as follows:

$$
\boldsymbol{\theta}_{k+1} = \underset{\mathbf{z}}{\operatorname{argmin}} \left[ t_k R(\mathbf{z}) + \frac{1}{2} ||\mathbf{z} - \mathbf{u}_k||_2^2 \right] = \operatorname{prox}_{t_k R}(\mathbf{u}_k)
$$
(13.78)

$$
\mathbf{u}_k = \boldsymbol{\theta}_k - t_k \mathbf{g}_k \tag{13.79}
$$

$$
\mathbf{g}_k = \nabla L(\boldsymbol{\theta}_k) \tag{13.80}
$$

If  $R(\theta)=0$ , this is equivalent to gradient descent. If  $R(\theta)=I_{C}(\theta)$ , the method is equivalent to **projected gradient descent**, sketched in Figure 13.11. If  $R(\theta) = \lambda ||\theta||_1$ , the method is known as **iterative soft thresholding**.

There are several ways to pick  $t_k$ , or equivalently,  $\alpha_k = 1/t_k$ . Given that  $\alpha_k \mathbf{I}$  is an approximation to the Hessian  $\nabla^2 L$ , we require that

$$
\alpha_k(\boldsymbol{\theta}_k - \boldsymbol{\theta}_{k-1}) \approx \mathbf{g}_k - \mathbf{g}_{k-1}
$$
\n(13.81)

in the least squares sense. Hence

$$
\alpha_k = \underset{\alpha}{\text{argmin}} ||\alpha(\boldsymbol{\theta}_k - \boldsymbol{\theta}_{k-1}) - (\mathbf{g}_k - \mathbf{g}_{k-1})||_2^2 = \frac{(\boldsymbol{\theta}_k - \boldsymbol{\theta}_{k-1})^T (\mathbf{g}_k - \mathbf{g}_{k-1})}{(\boldsymbol{\theta}_k - \boldsymbol{\theta}_{k-1})^T (\boldsymbol{\theta}_k - \boldsymbol{\theta}_{k-1})}
$$
(13.82)

This is known as the **Barzilai-Borwein** (BB) or **spectral** stepsize (Barzilai and Borwein 1988; Fletcher 2005; Raydan 1997). This stepsize can be used with any gradient method, whether proximal or not. It does not lead to monotonic decrease of the objective, but it is much faster than standard line search techniques. (To ensure convergence, we require that the objective decrease "on average", where the average is computed over a sliding window of size  $M + 1$ .)

When we combine the BB stepsize with the iterative soft thresholding technique (for  $R(\theta)$ )  $\lambda ||\theta||_1$ , plus a continuation method that gradually reduces  $\lambda$ , we get a fast method for the BPDN problem known as the SpaRSA algorithm, which stands for "sparse reconstruction by separable approximation" (Wright et al. 2009). However, we will call it the iterative shrinkage and thresholding algorithm. See Algorithm 12 for some pseudocode, and SpaRSA for some Matlab code. See also Exercise 13.11 for a related approach based on projected gradient descent.

#### **13.4.3.3 Nesterov's method**

A faster version of proximal gradient descent can be obtained by epxanding the quadratic approximation around a point other than the most recent parameter value. In particular, consider performing updates of the form

$$
\boldsymbol{\theta}_{k+1} = \text{prox}_{t_k R}(\boldsymbol{\phi}_k - t_k \mathbf{g}_k) \tag{13.83}
$$

$$
\mathbf{g}_k = \nabla L(\phi_k) \tag{13.84}
$$

$$
\phi_k = \theta_k + \frac{k-1}{k+2}(\theta_k - \theta_{k-1}) \tag{13.85}
$$

**Algorithm 13.2:** Iterative Shrinkage-Thresholding Algorithm (ISTA)

 Input:  $\mathbf{X} \in \mathbb{R}^{N \times D}$ ,  $\mathbf{y} \in \mathbb{R}^{N}$ , parameters  $\lambda \geq 0$ ,  $M \geq 1$ ,  $0 < s < 1$ ; Initialize  $\theta_0 = 0$ ,  $\alpha = 1$ ,  $\mathbf{r} = \mathbf{y}$ ,  $\lambda_0 = \infty$ ; **<sup>3</sup> repeat**  $\lambda_t = \max(s||\mathbf{X}^T\mathbf{r}||_{\infty}, \lambda)$  // Adapt the regularizer ;<br>**repeat 5 repeat g** =  $\nabla L(\boldsymbol{\theta})$ ;  $\mathbf{u} = \boldsymbol{\theta} - \frac{1}{\alpha} \mathbf{g}$ ; **a**  $\theta = \operatorname{soft}(\mathbf{u}, \frac{\lambda_t}{\alpha});$  Update  $\alpha$  using BB stepsize in Equation 13.82 ; **until**  $f(\theta)$  *increased too much within the past* M *steps*; **r** = **y** − **X** $\theta$  // Update residual ; **until**  $\lambda_t = \lambda$ ;

Image /page/25/Figure/3 description: This is a graphical model diagram. At the top, a shaded circle labeled gamma (γ) points to an unshaded circle labeled tau sub j (τj). This is enclosed in a box labeled D. The tau sub j (τj) points to an unshaded circle labeled w sub j (wj), which is also enclosed in the box labeled D. The w sub j (wj) points to a shaded circle labeled y sub i (yi). Separately, an unshaded circle labeled sigma squared (σ²) points to the y sub i (yi). The y sub i (yi) is enclosed in a box labeled N. A shaded circle labeled x sub i (xi) points to the y sub i (yi) and is also enclosed in the box labeled N.

**Figure 13.12** Representing lasso using a Gaussian scale mixture prior.

This is known as **Nesterov's method** (Nesterov 2004; Tseng 2008). As before, there are a variety of ways of setting  $t_k$ ; typically one uses line search.

When this method is combined with the iterative soft thresholding technique (for  $R(\theta)$ )  $\lambda ||\theta||_1$ ), plus a continuation method that gradually reduces  $\lambda$ , we get a fast method for the BPDN problem known as the **fast iterative shrinkage thesholding algorithm** or **FISTA** (Beck and Teboulle 2009).

# **13.4.4 EM for lasso**

In this section, we show how to solve the lasso problem using lasso. At first sight, this might seem odd, since there are no hidden variables. The key insight is that we can represent the Laplace distribution as a **Gaussian scale mixture** (GSM) (Andrews and Mallows 1974; West 1987) as follows:

$$
Lap(w_j|0, 1/\gamma) = \frac{\gamma}{2} e^{-\gamma|w_j|} = \int \mathcal{N}(w_j|0, \tau_j^2) Ga(\tau_j^2|1, \frac{\gamma^2}{2}) d\tau_j^2
$$
 (13.86)

Thus the Laplace is a GSM where the mixing distibution on the variances is the exponential distribution,  $\text{Expon}(\tau_j^2 | \frac{\gamma^2}{2} = \text{Ga}(\tau_j^2 | 1, \frac{\gamma^2}{2})$ . Using this decomposition, we can represent the lasso model as shown in Figure 13.12. The corresponding joint distribution has the form

$$
p(\mathbf{y}, \mathbf{w}, \tau, \sigma^2 | \mathbf{X}) = \mathcal{N}(\mathbf{y} | \mathbf{X} \mathbf{w}, \sigma^2 \mathbf{I}_N) \mathcal{N}(\mathbf{w} | \mathbf{0}, \mathbf{D}_\tau)
$$

$$
IG(\sigma^2 | a_\sigma, b_\sigma) \left[ \prod_j G_a(\tau_j^2 | 1, \gamma^2 / 2) \right] (13.87)
$$

where  $\mathbf{D}_{\tau} = \text{diag}(\tau_i^2)$ , and where we have assumed for notational simplicity that **X** is standardized and that **y** is centered (so we can ignore the offset term  $\mu$ ). Expanding out, we get

$$
p(\mathbf{y}, \mathbf{w}, \boldsymbol{\tau}, \sigma^2 | \mathbf{X}) \propto (\sigma^2)^{-N/2} \exp\left(-\frac{1}{2\sigma^2} ||\mathbf{y} - \mathbf{X}\mathbf{w}||_2^2\right) |\mathbf{D}_{{\boldsymbol{\tau}}}|^{-\frac{1}{2}}
$$
  
$$
\exp\left(-\frac{1}{2}\mathbf{w}^T \mathbf{D}_{{\boldsymbol{\tau}}} \mathbf{w}\right) (\sigma^2)^{-(a_{\sigma}+1)}
$$
  
$$
\exp(-b_{\sigma}/\sigma^2) \prod_j \exp(-\frac{\gamma^2}{2}\tau_j^2)
$$
 (13.88)

Below we describe how to apply the EM algorithm to the model in Figure 13.12.<sup>5</sup> In brief, in the E step we infer  $\tau_i^2$  and  $\sigma^2$ , and in the M step we estimate **w**. The resulting estimate  $\hat{\mathbf{w}}$  is the same as the lasso estimator. This approach was first proposed in (Figueiredo 2003) (see also (Griffin and Brown 2007; Caron and Doucet 2008; Ding and Harrison 2010) for some extensions).

#### **13.4.4.1 Why EM?**

Before going into the details of EM, it is worthwhile asking why we are presenting this approach at all, given that there are a variety of other (often much faster) algorithms that directly solve the  $\ell_1$  MAP estimation problem (see linregFitL1Test for an empirical comparison). The reason is that the latent variable perspective brings several advantages, such as the following:

• It provides an easy way to derive an algorithm to find  $\ell_1$ -regularized parameter estimates for a variety of other models, such as robust linear regression (Exercise 11.12) or probit regression (Exercise 13.9).

<sup>5.</sup> To ensure the posterior is unimodal, one can follow (Park and Casella 2008) and slightly modify the model by making the prior variance for the weights depend on the observation noise:  $p(w_j | \tau_j^2, \sigma^2) = \mathcal{N}(w_j | 0, \sigma^2 \tau_j^2)$ . The EM algorithm is easy to modify.

- It suggests trying other priors on the variances besides  $Ga(\tau_j^2|1,\gamma^2/2)$ . We will consider various extensions below.
- It makes it clear how we can compute the full posterior,  $p(\mathbf{w}|\mathcal{D})$ , rather than just a MAP estimate. This technique is known as the **Bayesian lasso** (Park and Casella 2008; Hans 2009).

#### **13.4.4.2 The objective function**

From Equation 13.88, the complete data penalized log likelihood is as follows (dropping terms that do not depend on **w**)

$$
\ell_c(\mathbf{w}) = -\frac{1}{2\sigma^2}||\mathbf{y} - \mathbf{X}\mathbf{w}||_2^2 - \frac{1}{2}\mathbf{w}^T \mathbf{\Lambda} \mathbf{w} + \text{const}
$$
\n(13.89)

where  $\mathbf{\Lambda} = \text{diag}(\frac{1}{\tau_j^2})$  is the precision matrix for **w**.

#### **13.4.4.3 The E step**

The key is to compute  $\mathbb{E}\left[\frac{1}{\tau_j^2}|w_j\right]$ . We can derive this directly (see Exercise 13.8). Alternatively, we can derive the full posterior, which is given by the following (Park and Casella 2008):

$$
p(1/\tau_j^2 | \mathbf{w}, \mathcal{D}) = \text{InverseGaussian}\left(\sqrt{\frac{\gamma^2}{w_j^2}}, \gamma^2\right)
$$
\n(13.90)

(Note that the **inverse Gaussian** distribution is also known as the Wald distribution.) Hence

$$
\mathbb{E}\left[\frac{1}{\tau_j^2}|w_j\right] = \frac{\gamma}{|w_j|} \tag{13.91}
$$

Let  $\overline{\mathbf{\Lambda}}=\operatorname{diag}(\mathbb{E}\left[1/\tau_1^2\right],\ldots,\mathbb{E}\left[1/\tau_D^2\right])$  denote the result of this E step.

We also need to infer  $\sigma^2$ . It is easy to show that that the posterior is

$$
p(\sigma^2|\mathcal{D}, \mathbf{w}) = \text{IG}(a_{\sigma} + (N)/2, b_{\sigma} + \frac{1}{2}(\mathbf{y} - \mathbf{X}\hat{\mathbf{w}})^T(\mathbf{y} - \mathbf{X}\hat{\mathbf{w}})) = \text{IG}(a_N, b_N)
$$
(13.92)

Hence

$$
\mathbb{E}\left[1/\sigma^2\right] = \frac{a_N}{b_N} \triangleq \overline{\omega} \tag{13.93}
$$

## **13.4.4.4 The M step**

The M step consists of computing

$$
\hat{\mathbf{w}} = \underset{\mathbf{w}}{\operatorname{argmax}} -\frac{1}{2}\overline{\omega}||\mathbf{y} - \mathbf{X}\mathbf{w}||_2^2 - \frac{1}{2}\mathbf{w}^T \mathbf{\Lambda}\mathbf{w}
$$
\n(13.94)

This is just MAP estimation under a Gaussian prior:

$$
\hat{\mathbf{w}} = (\sigma^2 \overline{\mathbf{\Lambda}} + \mathbf{X}^T \mathbf{X})^{-1} \mathbf{X}^T \mathbf{y}
$$
\n(13.95)

However, since we expect many  $w_j = 0$ , we will have  $\tau_j^2 = 0$  for many j, making inverting  $\overline{\mathbf{\Lambda}}$ numerically unstable. Fortunately, we can use the SVD of **X**, given by  $X = UDV^T$ , as follows:

$$
\hat{\mathbf{w}} = \Psi \mathbf{V} (\mathbf{V}^T \Psi \mathbf{V} + \frac{1}{\omega} \mathbf{D}^{-2})^{-1} \mathbf{D}^{-1} \mathbf{U}^T \mathbf{y}
$$
\n(13.96)

where

$$
\Psi = \overline{\Lambda}^{-1} = \text{diag}\left(\frac{1}{\mathbb{E}\left[1/\tau_j^2\right]}\right) = \text{diag}\left(\frac{|w_j|}{\pi'(w_j)}\right) \tag{13.97}
$$

#### **13.4.4.5 Caveat**

Since the lasso objective is convex, this method should always find the global optimum. Unfortunately, this sometimes does not happen, for numerical reasons. In particular, suppose that in the true solution,  $w_j^* \neq 0$ . Further, suppose that we set  $\hat{w}_j = 0$  in an M step. In the following E step we infer that  $\tau_j^2 = 0$ , so then we set  $\hat{w}_j = 0$  again; thus we can never "undo" our mistake. Fortunately, in practice, this situation seems to be rare. See (Hunter and Li 2005) for further discussion.

## **13.5** $\ell_1$ **regularization: extensions**

In this section, we discuss various extensions of "vanilla"  $\ell_1$  regularization.

#### **13.5.1 Group Lasso**

In standard  $\ell_1$  regularization, we assume that there is a 1:1 correspondence between parameters and variables, so that if  $\hat{w}_i = 0$ , we interpret this to mean that variable j is excluded. But in more complex models, there may be many parameters associated with a given variable. In particular, we may have a vector of weights for each input,  $w_j$ . Here are some examples:

- **Multinomial logistic regression** Each feature is associated with C different weights, one per class.
- **Linear regression with categorical inputs** Each scalar input is one-hot encoded into a vector of length  $C$ .
- **Multi-task learning** In multi-task learning, we have multiple related prediction problems. For example, we might have C separate regression or binary classification problems. Thus each feature is associated with  $C$  different weights. We may want to use a feature for all of the tasks or none of the tasks, and thus select weights at the group level (Obozinski et al. 2007).

If we use an  $\ell_1$  regularizer of the form  $||\mathbf{w}|| = \sum_j \sum_c |w_{jc}|$ , we may end up with with some elements of  $w_j$ , being zero and some not. To prevent this kind of situation, we partition the parameter vector into  $G$  groups. We now minimize the following objective

$$
J(\mathbf{w}) = \text{NLL}(\mathbf{w}) + \sum_{g=1}^{G} \lambda_g ||\mathbf{w}_g||_2
$$
\n(13.98)

where

$$
||\mathbf{w}_g||_2 = \sqrt{\sum_{j \in g} w_j^2}
$$
\n(13.99)

is the 2-norm of the group weight vector. If the NLL is least squares, this method is called **group lasso** (Yuan and Lin 2006).

We often use a larger penalty for larger groups, by setting  $\lambda_q = \lambda \sqrt{d_q}$ , where  $d_q$  is the number of elements in group q. For example, if we have groups  $\{1, 2\}$  and  $\{3, 4, 5\}$ , the objective becomes

$$
J(\mathbf{w}) = \text{NLL}(\mathbf{w}) + \lambda \left[ \sqrt{2} \sqrt{(w_1^2 + w_2^2)} \right] + \sqrt{3} \sqrt{(w_3^2 + w_4^2 + w_5^2)} \right]
$$
(13.100)

Note that if we had used the square of the 2-norms, the model would become equivalent to ridge regression, since

$$
\sum_{g=1}^{G} ||\mathbf{w}_g||_2^2 = \sum_g \sum_{j \in g} w_j^2 = ||\mathbf{w}||_2^2
$$
\n(13.101)

By using the square root, we are penalizing the radius of a ball containing the group's weight vector: the only way for the radius to be small is if all elements are small. Thus the square root results in group sparsity.

A variant of this technique replaces the 2-norm with the infinity-norm (Turlach et al. 2005; Zhao et al. 2005):

$$
||\mathbf{w}_g||_{\infty} = \max_{j \in g} |w_j| \tag{13.102}
$$

It is clear that this will also result in group sparsity.

An illustration of the difference is shown in Figures 13.13 and 13.14. In both cases, we have a true signal **w** of size  $D = 2^{12} = 4096$ , divided into 64 groups each of size 64. We randomly choose 8 groups of **w** and assign them non-zero values. In the first example, the values are drawn from a  $\mathcal{N}(0, 1)$ . In the second example, the values are all set to 1. We then pick a random design matrix **X** of size  $N \times D$ , where  $N = 2^{10} = 1024$ . Finally, we generate  $\mathbf{y} = \mathbf{X}\mathbf{w} + \boldsymbol{\epsilon}$ , where  $\epsilon \sim \mathcal{N}(\mathbf{0}, 10^{-4} \mathbf{I}_N)$ . Given this data, we estimate the support of **w** using  $\ell_1$  or group  $\ell_1$ , and then estimate the non-zero values using least squares. We see that group lasso does a much better job than vanilla lasso, since it respects the known group structure.<sup>6</sup> We also see that the  $\ell_{\infty}$  norm has a tendency to make all the elements within a block to have similar magnitude. This is appropriate in the second example, but not the first. (The value of  $\lambda$  was the same in all examples, and was chosen by hand.)

#### ********** GSM interpretation of group lasso**

Group lasso is equivalent to MAP estimation using the following prior

$$
p(\mathbf{w}|\gamma, \sigma^2) \propto \exp\left(-\frac{\gamma}{\sigma} \sum_{g=1}^G ||\mathbf{w}_g||_2\right)
$$
 (13.103)

<sup>6.</sup> The slight non-zero "noise" in the  $\ell_{\infty}$  group lasso results is presumably due to numerical errors.

Image /page/30/Figure/1 description: This image displays four plots arranged in two columns, labeled (a) and (b). The top row, labeled (a), shows two plots. The first plot is titled "Original (D = 4096, number groups = 64, active groups = 8)" and displays a signal with spikes occurring at regular intervals. The second plot in row (a) is titled "Standard L1 (debiased 1, tau = 0.385, MSE = 0.06929)" and shows a similar pattern of spikes, but with some values closer to zero. The bottom row, labeled (b), also contains two plots. The first plot in row (b) is titled "Block-L2 (debiased 1, tau = 0.385, MSE = 0.000351)" and presents a signal that closely resembles the "Original" plot, with distinct groups of spikes. The second plot in row (b) is titled "Block-Linf (debiased 1, tau = 0.385, MSE = 0.053)" and shows a signal where the spikes are broader and less sharp, with values clustered around zero between the main spike groups. All plots have x-axis labels ranging from 0 to 4000 and y-axis labels ranging from -2 to 2.

**Figure 13.13** Illustration of group lasso where the original signal is piecewise Gaussian. Top left: original signal. Bottom left:: vanilla lasso estimate. Top right: group lasso estimate using a  $\ell_2$  norm on the blocks. Bottom right: group lasso estimate using an  $\ell_{\infty}$  norm on the blocks. Based on Figures 3-4 of (Wright et al. 2009). Figure generated by groupLassoDemo, based on code by Mario Figueiredo.

Now one can show (Exercise 13.10) that this prior can be written as a GSM, as follows:

$$
\mathbf{w}_g|\sigma^2, \tau_g^2 \sim \mathcal{N}(\mathbf{0}, \sigma^2 \tau_g^2 \mathbf{I}_{d_g})
$$
\n(13.104)

$$
\tau_g^2 |\gamma \sim \text{Ga}(\frac{d_g+1}{2}, \frac{\gamma}{2}) \tag{13.105}
$$

where  $d_g$  is the size of group g. So we see that there is one variance term per group, each of which comes from a Gamma prior, whose shape parameter depends on the group size, and whose rate parameter is controlled by  $\gamma$ . Figure 13.15 gives an example, where we have 2 groups, one of size 2 and one of size 3.

This picture also makes it clearer why there should be a grouping effect. Suppose  $w_{1,1}$  is small; then  $\tau_1^2$  will be estimated to be small, which will force  $w_{1,2}$  to be small. Converseley, suppose  $w_{1,1}$  is large; then  $\tau_1^2$  will be estimated to be large, which will allow  $w_{1,2}$  to be become large as well.

Image /page/31/Figure/1 description: This image displays four line graphs comparing different signal processing methods. The top graph, labeled "Original (D = 4096, number groups = 64, active groups = 8)", shows a clean signal with distinct peaks. Below it, the "Standard L1 (debiased 1, tau = 0.356, MSE = 0.1206)" graph shows a noisier reconstruction with some artifacts. The bottom two graphs, labeled "Block-L2 (debiased 1, tau = 0.356, MSE = 0.000342)" and "Block-Linf (debiased 1, tau = 0.356, MSE = 0.000425)", show significantly improved reconstructions compared to the Standard L1 method, with the Block-L2 and Block-Linf methods producing results very close to the original signal. All graphs have the x-axis ranging from 0 to 4000 and the y-axis ranging from 0 to 1. The graphs are presented in two sections, (a) and (b).

Figure 13.14 Same as Figure 13.13, except the original signal is piecewise constant.

Image /page/31/Figure/3 description: This is a graphical model diagram. At the top, a shaded circle labeled 'γ' has arrows pointing to two unshaded circles labeled 'τ1' and 'τ2'. 'τ1' has arrows pointing to unshaded circles labeled 'w11' and 'w12'. 'τ2' has arrows pointing to unshaded circles labeled 'w21', 'w22', and 'w23'. An unshaded circle labeled 'σ²' has an arrow pointing to a shaded circle labeled 'yi'. 'w11', 'w12', 'w21', 'w22', and 'w23' all have arrows pointing to the shaded circle 'yi'. The shaded circle 'yi' has an arrow pointing to a shaded circle labeled 'xi'. A box encloses 'yi' and 'xi', indicating a plate or repetition.

**Figure 13.15** Graphical model for group lasso with 2 groups, the first has size  $G_1 = 2$ , the second has size  $G_2 = 3$ .
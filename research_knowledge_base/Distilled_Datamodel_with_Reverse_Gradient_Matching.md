<span id="page-0-0"></span>

# Distilled Datamodel with Reverse Gradient Matching

<PERSON><PERSON> † National University of Singapore

National University of Singapore

<EMAIL>, {ruonan,songhua.liu}@u.nus.edu, <EMAIL>

# Abstract

*The proliferation of large-scale AI models trained on extensive datasets has revolutionized machine learning. With these models taking on increasingly central roles in various applications, the need to understand their behavior and enhance interpretability has become paramount. To investigate the impact of changes in training data on a pretrained model, a common approach is leave-one-out retraining. This entails systematically altering the training dataset by removing specific samples to observe resulting changes within the model. However, retraining the model for each altered dataset presents a significant computational challenge, given the need to perform this operation for every dataset variation. In this paper, we introduce an efficient framework for assessing data impact, comprising offline training and online evaluation stages. During the offline training phase, we approximate the influence of training data on the target model through a distilled synset, formulated as a reversed gradient matching problem. For online evaluation, we expedite the leave-one-out process using the synset, which is then utilized to compute the attribution matrix based on the evaluation objective. Experimental evaluations, including training data attribution and assessments of data quality, demonstrate that our proposed method achieves comparable model behavior evaluation while significantly speeding up the process compared to the direct retraining method.*

## 1. Introduction

In the contemporary landscape of machine learning and artificial intelligence, our substantial reliance on largescale training data has become increasingly pronounced. The notable successes of large AI models like GPT-3 [\[7\]](#page-8-0), BERT [\[10\]](#page-8-1), and DALL-E [\[33\]](#page-9-0) can predominantly be attributed to the availability of extensive datasets, enabling them to discern complex patterns and relationships. As AI models progressively embrace a data-driven paradigm, comprehending the notion of "training data attribution" within a machine learning framework emerges as pivotal. It is imperative to acknowledge that model errors, biases, and the overall capabilities of these systems are frequently intertwined with the characteristics of the training data, making the enhancement of training data quality a reliable avenue for bolstering model performance.

Despite the various techniques available for interpreting models' decision-making processes, the very most of them concentrate on assessing the significance of features [\[29,](#page-8-2) [35,](#page-9-1) [40\]](#page-9-2) and explaining the internal representations of models [\[3,](#page-8-3) [14,](#page-8-4) [20,](#page-8-5) [48\]](#page-9-3). When examining the attribution of training data, a persistent dilemma surfaces, one that revolves around the delicate balance between computational demands and effectiveness. On one hand, techniques like influence approximation  $[16, 21]$  $[16, 21]$  $[16, 21]$  prioritize computational efficiency, but they may exhibit unreliability, especially in non-convex environments. Concurrently, another line of research has achieved remarkable progress in approximating the impact of even minor alterations, such as the removal of a single data point or a small subset from the complete training set, on the trained model [\[32,](#page-9-4) [47\]](#page-9-5). These methods, however, are tailored specifically for scenarios involving minor changes in the training data, lacking the necessary flexibility for broader applications.

In this study, we prioritize flexibility and robustness by opting to retrain the model using a dataset that excludes specific data points. Subsequently, we compare the newly trained models with the original model. The attribution matrix is then computed based on the specific objectives of model evaluation. Specifically, to effectively and explicitly study the newly trained models, we introduce in this paper a novel *Distilled Datamodel* framework (DDM). The DDM framework is centered around the estimation of parameters for the newly trained models, rather than solely focusing on the evaluation of prediction performance at a specific test point. This approach grants the flexibility to analyze various aspects of model behavior and performance. As is shown in Fig. [1,](#page-2-0) DDM encompasses two distinct processes: offline training and online evaluation. During offline training, we distilled the influence of the training data

<sup>†</sup> Corresponding author.

<span id="page-1-0"></span>back to the input space to get a rather small synset, a process achieved through reversed gradient matching. We contend that this novel reversed gradient matching approach, when compared to the standard gradient matching [\[55\]](#page-9-6), is more effective in afterward mitigating the influence of specific training data on the target network. During online evaluation, we perturbed the synset by deleting, which, along with the target network, is leveraged to quickly train the new model. With all the newly trained networks, the attribution matrix can be easily obtained for different evaluation objectives. In a word, our contributions are:

- We explore a training data attribution framework that explicitly identifies a training sample's responsible for various behaviors exhibited by the target model. By quantifying the impact and contribution of individual samples, our framework provides insights into the relationship between the training data and the model.
- We introduce a novel influence-based dataset distillation scheme that matches the reversed gradient update, which results in a highly efficient unlearning of certain data points from the target network.
- Experimental results demonstrate that the proposed analysis method provides an accurate interpretation and achieves a significant speedup compared to its unlearning counterpart.

# 2. Related Work

## 2.1. Data-based Model Analysis

Model behavior analysis has emerged as a foundational aspect of machine learning and artificial intelligence research and development, often categorized into training data based and testing data based methods

Testing data based methods focus on elucidating the model's inference capabilities for for a certain input. Plenty researches [\[1,](#page-8-8) [2,](#page-8-9) [9,](#page-8-10) [13,](#page-8-11) [34,](#page-9-7) [41,](#page-9-8) [42,](#page-9-9) [44–](#page-9-10)[46,](#page-9-11) [52,](#page-9-12) [56\]](#page-9-13) contribute to this field of research.

In this study, our primary focus is on analyzing the model's behavior based on its training data, with one key approach being the utilization of influence approximation techniques as demonstrated by prior research [\[4,](#page-8-12) [16,](#page-8-6) [21,](#page-8-7) [37\]](#page-9-14). As pointed out by the authors, these approaches primarily focus on local changes that are *infinitesimally-small*, which are also extremely time consuming. Datamodels [\[19\]](#page-8-13) is proposed for analyzing the behavior of a model class in terms of the training data, which measures the correlation between true model outputs and attribution-derived predictions for those outputs. Following this work, ModelPred [\[53\]](#page-9-15) is proposed for predicting the trained model parameters directly instead of the trained model behaviors. Nevertheless, both these methods still entail the training of a considerable number of models, often in the thousands or tens of thousands, for effectiveness. In this work, we investigate a more efficient framework to facilitate this process.

### 2.2. Machine Unlearning

The concept of unlearning is firstly introduced by Bourtoule *et al.* [\[5\]](#page-8-14), which aims to eliminate the effect of data point(s) on the already trained model. Along this line, machine unlearning has attracted more attentions, of which the existing approaches can be roughly divided into *exact* [\[5,](#page-8-14) [6,](#page-8-15) [8,](#page-8-16) [15\]](#page-8-17) methods and *approximate* methods [\[6,](#page-8-15) [18,](#page-8-18) [30,](#page-9-16) [39,](#page-9-17) [49,](#page-9-18) [50\]](#page-9-19).

Exact methods decrease the time it takes to exactly/directly retrain the models. Bourtoule *et al.* [\[5\]](#page-8-14) propose an unlearning framework that when data needs to be unlearned, only one of the constituent models whose shards contains the point to be unlearned needs to be retrained. Cao *et al.* [\[8\]](#page-8-16) transform learning algorithms used by a system into a summation form and to forget a training data sample, they simply update a small number of summations. DaRE trees [\[6\]](#page-8-15) are proposed to enable the removal of training data with minimal retraining, which cache statistics at each node and training data at each leaf to update only the necessary subtrees as data is removed. Unlike the exact methods, the approximate ones try to find a way to approximate the retraining procedure. To minimize the retraining time, data removal-enabled forests [\[6\]](#page-8-15) are introduced as a variant of random forests, which delete data orders of magnitude faster than retraining from scratch while sacrificing little to no predictive power. Nguyen *et al.* [\[30\]](#page-9-16) study the problem of approximately unlearning a Bayesian model from a small subset of the training data to be erased.

The above unlearning methods focus more on balancing the accuracies and the efficiency. Here we focus more on the efficiency, which model the network behavior for analyzing the attributions of a target model.

### 2.3. Dataset Distillation

Dataset condensation/distillation [\[25](#page-8-19)[–28,](#page-8-20) [51,](#page-9-20) [54\]](#page-9-21) aims to condense a large training set into a small synthetic set to obtain the highest generalization performance with a model trained on such small set of synthetic images. Zhao et al. [\[54\]](#page-9-21) formulate the goal as a gradient matching problem between the gradients of deep neural network weights that are trained on the original and the synthetic data. Zhou et al. [\[57\]](#page-9-22) address these challenges of significant computation and memory costs by neural feature regression with pooling. Nguyen et al. [\[31\]](#page-9-23) apply a distributed kernel-based meta-learning framework to achieve state-of-the-art results for dataset distillation using infinitely wide convolutional neural networks. Sucholutsky et al. [\[43\]](#page-9-24) propose to simultaneously distill both images and their labels, thus assigning each synthetic sample a 'soft' label.

Different from the previous data condensation methods, we tend to use the fast convergence and the gradient matching properties for the analysis of the target network. Thus,

<span id="page-2-0"></span>Image /page/2/Figure/0 description: This diagram illustrates a machine learning model training and evaluation process. The left side shows the 'Normal Training' phase where a 'Trainset D' is used to train a 'TargetNet'. Simultaneously, a 'Distill' process creates a 'Synset' from k clusters (D1 to Dk), which is then stored. The 'Synset' is also used in 'Offline Training' with 'Reverse Gradient matching'. The right side details the 'Online Evaluation' phase. The 'Synset' (S1 to Sk) is subjected to 'Perturb' operations, followed by 'Leave-one-out Retraining'. An 'Evaluation Objective' leads to the computation of an 'Attribution Matrix {W1, W2, ..., Wk}'.

Figure 1. The framework of the proposed distilled datamodel. During the offline training, the synset is distilled during the normal training of target network. As for online evaluation we perturb the learned synset and fast learn the perturbed model set, which is computed to form the final attribution matrix.

we focus on how to model the data's impact on the network not just for improving the accuracies.

# 3. Proposed Method

In this paper, we propose the distilled datamodel framework to build the training data attribution to evaluate various model behaviors.

## 3.1. Problem Statement

Given a target model  $M$  trained on dataset  $D$ , we tend to construct direct relationship between them, which is denoted as the attribution matrix  $W$ . Each weight in  $W$  measures the responsible of the corresponding training points on certain behaviors of M.

The attribution matrix W learned by the proposed DDM framework works on various behaviors, which include but not limited to:

- Model functionality analysis. This involves evaluating the performance of the target network using the training data. This could include measuring key metrics such as accuracy, precision, recall, and F1 score, and comparing the results to established benchmarks or industry standards.
- Model diagnose. This involves examining the errors made by the target network when processing the training data. This could include identifying the types of errors made, such as misclassifications or false positives, and determining the root cause of the errors, such as data quality issues or model limitations.
- Influence function of certain test samples. This traces a model's prediction through the learning algorithm and back to its training data, thereby identifying training points most responsible for a given prediction.

In what follows, we take studying the model behavior on the influence of certain test samples as an example, showing how to learn the corresponding training data attribution with the proposed DDM framework. We would also include more details on studying other kinds of model behaviors in the supplementary.

Note that in Fig. [1,](#page-2-0) the proposed DDM framework introduces a two-step process:

- Offline Training (Sec. [3.2\)](#page-2-1): This step is learned only once and can be integrated into network training. Its objective is to distill and store data influence with improved approximation and reduced storage requirements.
- **Online Evaluation** (Sec. [3.3\)](#page-4-0): This phase involves evaluation to meet specific requirements for model behavior analysis, which is realized by perturbing the dataset. The primary goal is to compute the training data attribution matrix while minimizing time and computational costs.

<span id="page-2-1"></span>

## 3.2. Offline Training

During the offline training, we tend to obtain the synset  $S(|S| \ll |\mathcal{D}|)$  to distill the training data influence from the target network  $M$ , so as to produce the parameters of the network with perturbed dataset. To begin with, we cluster the original training data  $D$  into  $K$  groups as  $\{\mathcal{D}_1, \mathcal{D}_2, ..., \mathcal{D}_K\}$ , with the consideration that the existing of single data point won't be able to make much difference on the behaviors of the target network  $M$ . So it's more meaningful to build the cluster-level training data attribution under this circumstance.

Here the target network is initialized with parameters  $\theta_0$ and subsequently trained on the dataset  $D$  for  $\tau$  epochs, and the synset is for finetuning the trained target network for T epochs, resulting in updated parameters  $\theta_{\tau}$  and  $\theta_{T}$ . The <span id="page-3-4"></span>objective is formulated as:

<span id="page-3-0"></span>
$$
\mathcal{A}(\mathcal{D}): \theta_{\tau} = \arg \min_{\theta} \mathcal{L}(\theta, \mathcal{D}) = \arg \min_{\theta} \sum_{k} \mathcal{L}(\theta, \mathcal{D}_{k}),
$$

$$
\mathcal{U}(\mathcal{D}_{\kappa}) = \mathcal{A}(\bigcup_{k \neq \kappa} \mathcal{D}_{k}) : \theta_{\tau}^{\kappa} = \arg \min_{\theta} \sum_{k \neq \kappa} \mathcal{L}(\theta, \mathcal{D}_{k}), (1)
$$

$$
\mathcal{F}(\mathcal{S}_{\kappa}) : \tilde{\theta}_{T}^{\kappa} = \arg \min_{\theta} \mathcal{L}(\theta, \mathcal{S}_{\kappa}),
$$

$$
s.t. \quad \mathcal{S}_{\kappa} = \arg \min_{\mathcal{S}_{\kappa}, |\mathcal{S}_{\kappa}| \leq |\mathcal{D}_{\kappa}|} [\mathcal{D}ist(\theta_{\tau}^{\kappa}, \tilde{\theta}_{\tau}^{\kappa})],
$$

where  $\kappa = \{1, 2, ..., K\}$  and  $\mathcal{L}(\cdot, \cdot)$  is the loss for training the target network  $M$ .  $A$  stands for the learning process with  $\tau$  epochs, U stands for the unlearning process (equivalent to training without the unlearn set with  $\tau$  epochs),  $\mathcal F$ stands for the fine-tuning process starting with  $\theta_0 \leftarrow \theta_\tau$ with T epochs. The synset  $S = \{S_1, S_2, ..., S_K\}$  is extremely small in scale comparing with the original dataset  $D$ . To achieve this goal, our approach involves the minimization of the distribution distance, denoted as  $Dist($ ,), between the parameters of the synset fine-tuned model  $\tilde{\theta}_{\tau}^{\kappa}$ , and the directly unlearned parameters  $\theta_{\tau}^{\kappa}$ .

Assuming that the target network parameters are updated through stochastic gradient descent for  $t = 1, 2, ..., \tau$ epochs with a learning rate  $\eta_a$ , and the finetuning process of the target network spans  $t = 1, 2, ..., T$  epochs with a learning rate  $\eta_f$ , we can reformulate the problem based on Eq. [1](#page-3-0) as follows:

$$
\theta_{t+1} \leftarrow \theta_t - \eta_a \nabla \mathcal{L}(\theta_t, \mathcal{D}),
$$
  

$$
\theta_{t+1}^{\kappa} \leftarrow \theta_t^{\kappa} - \eta_a \sum_{k \neq \kappa} \nabla \mathcal{L}(\theta_t^{\kappa}, \mathcal{D}_k) \quad w.r.t. \quad \theta_0^{\kappa} = \theta_0, \tag{2}
$$
  

$$
\tilde{\theta}_{t+1}^{\kappa} \leftarrow \tilde{\theta}_t^{\kappa} - \eta_f \nabla \mathcal{L}(\tilde{\theta}_t^{\kappa}, \mathcal{S}_k) \quad w.r.t. \quad \tilde{\theta}_0^{\kappa} = \theta_\tau,
$$

where  $\nabla \mathcal{L}$  is the gradient computed on  $\theta$ . Based on it, we simplify the problem by setting  $\eta = \eta_a = \eta_f$  and  $\tau = T$ . In this way, we accumulate the gradients in the learning and finetuning process as:

<span id="page-3-1"></span>
$$
\theta_{\tau} = \theta_0 - \eta \sum_{t} \nabla \mathcal{L}(\theta_t, \mathcal{D}),
$$

$$
\theta_{\tau}^{\kappa} = \theta_0 - \eta \sum_{t} \sum_{k \neq \kappa} \nabla \mathcal{L}(\theta_t^{\kappa}, \mathcal{D}_k),
$$

$$
= \theta_0 - \eta \sum_{t} \left[ \nabla \mathcal{L}(\theta_t, \mathcal{D}) - \nabla \mathcal{L}(\theta_t^{\kappa}, \mathcal{D}_\kappa) \right], (3)
$$

$$
\tilde{\theta}_{\tau}^{\kappa} = \theta_{\tau} - \eta \sum_{t} \nabla \mathcal{L}(\tilde{\theta}_t^{\kappa}, \mathcal{S}_\kappa).
$$

<span id="page-3-2"></span>Note that our goal is to make  $\tilde{\theta}_{\tau}^{\kappa} \approx \theta_{\tau}^{\kappa}$ , then Eq. [3](#page-3-1) can be further simplified as:

$$
-\sum_{t} \nabla \mathcal{L}(\tilde{\theta}^{\kappa}_{t}, \mathcal{S}_{\kappa}) = \sum_{t} \nabla \mathcal{L}(\theta^{\kappa}_{t}, \mathcal{D}_{\kappa}), \tag{4}
$$

<span id="page-3-3"></span>Image /page/3/Figure/9 description: The image illustrates a process involving parameters denoted by theta. It begins with an initial parameter state \(\\theta\_0\). A dashed blue line labeled 'Learn' shows a trajectory from \(\\theta\_0\) to \(\\theta\_t\), and then to \(\\theta\_{t+s}\), and finally to \(\\theta\_\tau\). A dashed orange line labeled 'Finetune' shows a trajectory from \(\\theta\_t\) to \(\\tilde{\\theta}\_{t+s}\). An arrow indicates minimizing \(\\tilde{\\theta}\_{t+s}\) from \(\\theta\_t\).

Figure 2. The proposed reverse gradient matching process. The synset is optimized by the reverse gradients.

given that  $\tilde{\theta}_0^{\kappa} = \theta_{\tau}$  and  $\theta_0^{\kappa} = \theta_0$ , the sufficient solution to Eq.  $4$  is:

$$
\nabla \mathcal{L}(\tilde{\theta}_{\tau-t}^{\kappa}, \mathcal{S}_{\kappa}) = -\nabla \mathcal{L}(\theta_t^{\kappa}, \mathcal{D}_{\kappa}),
$$
  

$$
\Rightarrow \sum_{\kappa} \nabla \mathcal{L}(\tilde{\theta}_{\tau-t}^{\kappa}, \mathcal{S}_{\kappa}) = -\sum_{\kappa} \nabla \mathcal{L}(\theta_t^{\kappa}, \mathcal{D}_{\kappa}),
$$
  

$$
\Rightarrow \sum_{\kappa} \nabla \mathcal{L}(\theta_{\tau-t}^{\kappa}, \mathcal{S}_{\kappa}) = -\sum_{\kappa} \nabla \mathcal{L}(\theta_t^{\kappa}, \mathcal{D}_{\kappa})
$$
 $(5)$ 

where the synset in our proposed DDM is learnt for matching the reverse training trajectory while training the target network initialized from  $\theta_0$  to  $\theta_\tau$ . This reverse gradient matching process is depicted in Fig. [2.](#page-3-3) Thus, synset  $S$  here is for predicting the parameters of the target network M that unlearns the  $\kappa$ -th data cluster  $\mathcal{D}_{\kappa}$ , which is achieved by directly finetuning the target network with the synset  $S$ .

We constrain the scale of the synset to ensure efficient storage and fine-tuning process. Motivated by the idea of dataset condensation [\[55\]](#page-9-6) with gradient matching, we propose the reverse gradient matching to distill and store the gradient information to a couple of synthetic images  $S$  $(|S| \ll |\mathcal{D}|)$ . The synset S is optimized by:

<span id="page-3-5"></span>
$$
\underset{|\mathcal{S}|=K \times ipc}{\arg \min} \sum_{t} \sum_{\kappa} \mathcal{D}ist(\nabla \mathcal{L}(\theta_{\tau-t}, \mathcal{S}_{\kappa}), -\nabla \mathcal{L}(\theta_t, \mathcal{D}_{\kappa})), \tag{6}
$$

where for each data cluster  $\mathcal{D}_{\kappa}$ , we learn a corresponding  $\mathcal{S}_{\kappa}$ which contains *ipc* images. In experiments, we set  $\text{i}pc = 1$ and using the cosine distance for  $Dist(\cdot)$ .

Why do we choose reverse gradient matching over gradient matching? There are two main reasons:

- Enhanced matching performance. Since the number of unlearn set is smaller than the whole set and the optimization of data matches the initial stage of the learning trajectory, making the accumulated trajectory error much smaller using our proposed reverse gradient matching. Detailed evidence supporting this claim is provided in the supplementary materials.
- Improved Privacy Protection: While traditional data distillation using gradient matching offers a degree of privacy protection for the dataset [\[11\]](#page-8-21), the distilled images still retain distinguishable patterns of the main object, posing privacy risks. In contrast, images synthesized

using reverse gradient matching exhibit no explicit patterns, thus ensuring a higher level of privacy protection. Detailed comparisons in this regard are presented in the experimental results.

<span id="page-4-0"></span>

## 3.3. Online Evaluation

During the online evaluation stage, both the synset  $S$  and the target network  $\mathcal M$  are available, allowing for the evaluation of specific model behaviors.

The primary concept behind online evaluation is to employ leave-one-out cross-validation, which entails systematically perturbing the training dataset  $D$  by removing specific training samples. This process helps analyze the resulting impact on the model's performance. Here we take studying the influence function for example, which is a typical task for analyzing the model's data sensitivity, offering insights into its robustness and decision boundaries. To be concrete, given a test sample  $\{x_t, y_t\}$ , The corresponding prediction result as  $\tilde{y}_t$ , where the target model is trained on the original whole dataset  $D$ . The objective here is to directly build the relationship with the network prediction  $\tilde{y}_t$  and the training data  $\mathcal D$  (dataset $\rightarrow$ target network $\rightarrow$ prediction) by the attribution matrix W:

$$
\tilde{y}_{t(p_t \circ \mathcal{D})} = W \cdot p_t + b, \quad p_t \subseteq \{0, 1\}^K, \tag{7}
$$

where  $p_t$  stands for the perturbation operation over the dataset, and  $p_t(\kappa) = 0$  denotes the deletion of that data cluster  $\mathcal{D}_{\kappa}$  from the training set. And  $\tilde{y}_{t(P_t \circ \mathcal{D})}$  denotes the prediction by the target network, which is trained from scratch using the dataset  $p_t \circ \mathcal{D}$ .

Then, the attribution matrix  $W$  calculated from perturbing the training data can be calculated as:

<span id="page-4-1"></span>
$$
\underset{W}{\arg\min} \sum_{p_t \subseteq P_t} \beta_{p_t} \cdot \mathcal{D}ist(\tilde{y}_{t(p_t \circ \mathcal{D})}, W \cdot p_t), \qquad (8)
$$

where  $p_t$  is randomly sampled from the  $\{0,1\}^K$ ,  $\beta_{p_t}$  represents the weights corresponding to the number of 0s in  $p_t$ . The distance function  $Dist(\cdot)$  is set as the L2 norm distance for measuring influence function of the model. And the attribution matrix  $W \subseteq \mathbb{R}^{K \times |y_t|}$ , signifies the contribution of each training data cluster to the confidence scores of each label in the target network's prediction for the test sample  $x_t$ . Let  $P_t$  denote the perturbation set. To calculate the attribution matrix  $W$ , a minimum of  $K$  perturbations is required, such that  $|P_t| \geq K$ , applied to the training data.

The main difficulty in Eq. [8](#page-4-1) lies in obtaining  $|P_t|$  new trained models training with the perturbed dataset  $p_t \circ \mathcal{D}$ , so as to get the corresponding inference  $\tilde{y}_{t(p_t \circ \mathcal{D})}$ . Recall that during the offline training process, we already got the distilled synthetic data  $S_{\kappa}$  for each data cluster  $\mathcal{D}_{\kappa}$ , which could fast unlearn  $\mathcal{D}_{\kappa}$  from the target network. And the model parameters with the perturbed dataset could be finetuned with the synset as:

$$
\theta_{p_t} \leftarrow \mathcal{F}_{\kappa \in \{1, 2, \ldots, K\}, p_t(\kappa) = 0}(\mathcal{S}_{\kappa}). \tag{9}
$$

As a result, in the offline evaluation stage, we solve this difficulty by eliminating each cluster of training data from the target network, which is further accelerated by our proposed reverse gradient matching.

Accelerate with hierarchical distilled datamodel. To expedite the online evaluation process, we implement a hierarchical data distillation approach. This strategy encompasses the distillation of both the class-wise datamodel (with  $K = |y|$ ) and the cluster-wise datamodel (with  $K =$  $|y| \times c$ , where each label's data is partitioned into c clusters).

By following this approach, we can construct both the class-wise and the cluster-wise attribution matrices. This approach accelerates the analysis of model behavior, including tasks such as identifying the most influential training data. This is achieved by initially pinpointing the class-wise data points and subsequently calculating the training matrix within each class.

<span id="page-4-2"></span>

| <b>Algorithm 1</b> The Proposed DDM Framework |                                                                                                                                                         |
|-----------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------|
| <i>- Offline Training -</i>                   |                                                                                                                                                         |
| <b>Input:</b>                                 | $D$ : training set; $M$ : target model; ${\theta_0, \theta_1, ..., \theta_\tau}$ :<br>training trajectory of the target network; $s$ : trajectory step. |
| 1:                                            | Divide the training data $D$ into K clusters;                                                                                                           |
| 2:                                            | Randomly initialize K synthetic samples, formed as $S$ ;                                                                                                |
| 3:                                            | for each distillation step do                                                                                                                           |
| 4:                                            | Choose random start from target trajectory: $\theta_t$ ( $0 \le t < \tau$ );                                                                            |
| 5:                                            | Choose the end from target trajectory: $\theta_{t+s}$ ( $t + s < \tau$ );                                                                               |
| 6:                                            | for $\kappa = 1, 2, ..., K$ do                                                                                                                          |
| 7:                                            | Calculate the gradients on real data: $\nabla \mathcal{L}(\theta_t; \mathcal{D}_\kappa)$ ;                                                              |
| 8:                                            | Calculate the gradients of the synset $\nabla \mathcal{L}(\theta_{t+s}, \mathcal{S}_\kappa)$ ;                                                          |
| 9:                                            | $\min \mathcal{D}ist(\nabla \mathcal{L}(\theta_t;\mathcal{D}_\kappa), -\nabla \mathcal{L}(\theta_{t+s},\mathcal{S}_\kappa))$ to update                  |

9. 
$$
\lim_{k \to \infty} \frac{D(s\mathcal{L}(v\mathcal{L}(v_t, \nu_{\kappa})) - \mathbf{v} \mathcal{L}(v_{t+s}, \sigma_{\kappa}))}{\mathcal{S}_{\kappa}}
$$

10: end for

11: end for

**Output:** Cluster-wise synthetic data  $\{S_1, S_2, ..., S_K\}$ .

#### 3.3.1. Online Evaluation

**Input:**  $M$ : target model;  $S$ : synset;  $x_t$ : test sample.

- 1: Randomly sample perturbations  $p_t$  to form  $P_t$ ;
- 2: for each  $p_t$  in  $P_t$  do
- 3: Perform perturbation  $p_t$  on the synset S as  $p_t \circ S$ ;
- 4: Fine-tune M with  $p_t \circ S$ ;
- 5: Input  $x_t$  to the fine-tuned network and get  $\tilde{y}_t$ ;
- 6: end for
- 7: Calculate the attribution matrix  $W$  with Eq. [8.](#page-4-1)

```
Output: Attribution matrix W.
```

<span id="page-5-1"></span><span id="page-5-0"></span>Table 1. Ablation study on the influence analysis of certain test samples on MNIST, CIFAR10 and CIFAR100 datasets. We locate to the source data considering three distance functions. We report the value  $\times 100$  for  $Dist_1$  in the table, larger is better and tiny number in red donates the improvement or drop compared with 'Random Select'.

| <b>Method</b>    | <b>MNIST</b> |               |               | <b>CIFAR10</b> |               |               | <b>CIFAR100</b> |               |             |
|------------------|--------------|---------------|---------------|----------------|---------------|---------------|-----------------|---------------|-------------|
|                  | $Dist_1$     | $Dist_2$      | $Dist_3$      | $Dist_1$       | $Dist_2$      | $Dist_3$      | $Dist_1$        | $Dist_2$      | $Dist_3$    |
| Random Select    | 3.3          | 0.43          | 0.07          | 2.7            | 0.34          | 0.54          | 2.3             | 0.54          | 0.8         |
| Predict-based    | $5.5 + 2.2$  | -             | -             | $3.1 + 0.4$    | -             | -             | $3.9 + 1.6$     | -             | -           |
| Clustering-based | $6.2 + 2.9$  | -             | -             | $2.5 - 0.2$    | -             | -             | $3.6 + 1.3$     | -             | -           |
| DDM w/o cluster  | $9.1 + 5.8$  | $0.55 + 0.12$ | $0.11 + 0.04$ | $5.9 + 3.2$    | $0.57 + 0.23$ | $0.78 + 0.24$ | $3.5 + 1.2$     | $0.77 + 0.23$ | $1.2 + 0.4$ |
| DDM-match        | $10.8 + 7.5$ | $0.73 + 0.30$ | $0.13 + 0.06$ | $5.8 + 3.1$    | $0.76 + 0.42$ | $0.81 + 0.27$ | $4.8 + 2.5$     | $0.71 + 0.17$ | $1.6 + 0.8$ |
| DDM-full (ours)  | $10.8 + 7.5$ | $0.73 + 0.30$ | $0.13 + 0.06$ | $6.8 + 4.1$    | $0.81 + 0.41$ | $0.92 + 0.38$ | $5.3 + 3.0$     | $0.88 + 0.34$ | $1.6 + 0.8$ |

## 3.4. Algorithm and Discussions.

We depict the proposed algorithm including offline training and online evaluation in Alg. [1.](#page-4-2) During the offline training stage, we follow and modify the basic optimization framework of dataset distillation. And we give the algorithm for evaluating the influence function with  $x_t$  as input. In the online evaluation phase, adjustments are made to accommodate different evaluation objectives. Importantly, the offline training process occurs only once and remains fixed for subsequent evaluations.

# 4. Experiments

## 4.1. Experimental Settings

Datasets and networks. We conduct our experiments on several standard image classification datasets: digit recognition on MNIST dataset [\[24\]](#page-8-22), CIFAR-10 dataset, CIFAR-100 dataset [\[22\]](#page-8-23) and TinyImageNet [\[36\]](#page-9-25). Regarding the architectures of the target network, we evaluated various architectures, including AlexNetIN [\[23\]](#page-8-24), ResNet18, ViT, and ConvNet.

Training details and parameter settings. We implemented our experiments using the PyTorch framework. In the default setting, unless otherwise specified, we set the number of clusters per class to  $num_{\text{cluster}} = 10$ . This implies that there are a total of  $K = 100$  clusters for the MNIST and CIFAR-10 datasets, and  $K = 1000$  clusters for the CIFAR-100 dataset. For both class-wise and clusterwise condensation, we used a single synthetic image per cluster. These synthetic images were initialized by randomly sampling real images, and standard data augmentation techniques were applied during training. The learning rate for updating synthetic images was set to 10, while the learning rate for updating network parameters was set to 0.01. To perturb the training set  $D$ , we set  $|P_t| = K$ .

Evaluation metrics. To assess the attribution of training data to the behavior of the target network when influencing specific test samples, we investigate three influence objectives, each defined by a distinct distance metric. We randomly select 20 test samples ( $|X_t| = 20$ ) from the validation set of the training data and report the average distance metrics. And in order to compare the accuracy of such built relationship, we use the exact-unlearn network for evaluation. That is, after locating the data cluster  $\mathcal{D}_i$  with the target influence objective, we scratch train the unlearn network  $\mathcal{M}^u$  on  $\mathcal{D}/\mathcal{D}_i$ , and get predictions as  $y_t^u$ . We compute distance function  $Avg\_dist$  regarding different types of influence analysis:

<span id="page-5-2"></span>
$$
Avg\_dist = \mathbb{E}_{x_t \sim X_t} Dist_i
$$
  
where 
$$
Dist_1 = ||y_t^u - \tilde{y}_t||^2, Dist_2 = \ell_{ce}(y_t^u, y_t), \quad (10)
$$
$$
Dist_3 = 1/(1 + ||y_t^u - \tilde{y}_t||^2),
$$

where  $y_t$  is the groundtruth label for  $x_t$  and  $\tilde{y}_t$  is the output from the target network  $M$ . For all three distance metrics, namely  $Dist_1$ ,  $Dist_2$ , and  $Dist_3$ , larger values are indicative of more significant influence. Specifically,  $Dist_1$  is designed to trace back to the training data points that have the most influence on the current prediction,  $Dist_2$  focuses on identifying those with the most influence on whether the model makes correct predictions using the cross entropy loss  $\ell_{ce}$ , and  $Dist_3$  is utilized to pinpoint the training data points with the least influence on the current prediction.

For evaluation objectives other than the influence function of specific test samples, relevant metrics are provided within the corresponding experimental analysis part.

### 4.2. Experimental Results

DDM could be used for training data influence analysis. By telling us the training points "responsible" for a given prediction, influence functions reveal insights about how models rely on and extrapolate from the training data. For three different distance functions ( $Dist_1$ ,  $Dist_2$  and  $Dist<sub>3</sub>$ ), we calculate different weight matrix from Eq. [8](#page-4-1) by replacing the corresponding distance function, obtaining  $W^1, W^2, W^3$ . And then we locate the corresponding  $\mathcal{D}_i$ , where  $i = \arg \max_i W_i$ . The ablation study re-

<span id="page-6-3"></span><span id="page-6-1"></span>Table 2. Comparative experimental results with other works on MNIST, CIFAR10 and CIFAR100 datasets, regarding  $Dist_1$  influence.

| Method          | MNIST       | CIFAR-10   | CIFAR-100  |
|-----------------|-------------|------------|------------|
| Random Select   | 3.3         | 2.7        | 2.3        |
| Koh et al. [21] | 10.0        | 5.6        | 2.6        |
| FASTIF [16]     | 9.8         | 6.5        | 2.4        |
| Scaleup [38]    | 10.4        | 6.5        | 3.9        |
| <b>DDM</b>      | <b>10.8</b> | <b>6.8</b> | <b>5.3</b> |

<span id="page-6-2"></span>Table 3. Detecting useless training data for the target network.

| Percentage    | 0%   | 1%   | 10%  | 20%  | 50%  |
|---------------|------|------|------|------|------|
| Random Select | 95.7 | 95.8 | 92.8 | 74.6 | 65.9 |
| Koh et al.    | 95.7 | 95.9 | 93.7 | 81.5 | 74.3 |
| <b>FASTIF</b> | 95.7 | 95.5 | 94.1 | 79.6 | 74.0 |
| Scaleup       | 95.7 | 96.0 | 95.2 | 82.9 | 73.3 |
| DDM           | 95.7 | 96.2 | 95.9 | 85.4 | 79.3 |

garding the three types of influence functions is depicted in Table [1,](#page-5-0) where 'Random Select' denotes that we randomly choose  $D_i$  from K clusters; 'Predict-based' denotes we choose the datapoint  $\mathcal{D}_i$  with the highest prediction similarity. 'Cluster-Based' denotes locating  $\mathcal{D}_i$  by using the clustering strategy as we pre-process the dataset  $D$ , which denotes the highest visual similarity; 'DDM w/o cluster' clusters  $D$  in K clusters by sequence number not by kmeans, 'DDM-match' denotes the DDM framework that uses the gradient matching loss during the offline training stage. From the table, we observe that:

- Methods categorized as 'Predicted-based' and 'Clustering-based' can serve as alternatives for evaluating  $Dist_1$  type inferences. While they turn to be less accurate than our DDM. It further proves that visual similarity between two images does not fully capture the influence of one on the other in terms of model behavior [\[19\]](#page-8-13). In addition, they are limited in their ability to provide a comprehensive analysis of  $Dist_2$  and  $Dist_3$ . This highlights the potential of our proposed DDL model to evaluate a wider range of model behaviors.
- Comparing the results with 'DDM w/o cluster' and 'DDM-full', it becomes evident that the clustering strategy offers a more accurate method for pinpointing influential training data.
- Upon comparing the results with 'DDM-match' and 'DDM-full', it is apparent that optimizing the synset with gradient matching produces favorable outcomes on simpler datasets like MNIST. However, as the dataset complexity increases, this approach exhibits a decline in performance, falling behind the optimization with reverse gradient matching.

How do different target network architectures af-

<span id="page-6-0"></span>Image /page/6/Figure/9 description: The image displays two line graphs side-by-side, comparing the performance of three different architectures: AlexNet, ResNet, and ViT. The left graph is titled "Different Architectures on MNIST", and the right graph is titled "Different Architectures on CIFAR-10". Both graphs have a legend indicating the colors for each architecture. The x-axis for both graphs ranges from 1 to 10, representing some form of iteration or parameter. The y-axis represents performance, though the scale is not explicitly labeled. On the MNIST graph, ViT (blue) starts with the highest performance, decreases, then increases slightly towards the end. ResNet (green) starts high, decreases, and then increases. AlexNet (red) shows a general downward trend. On the CIFAR-10 graph, ResNet (green) consistently shows the highest performance with some fluctuations. ViT (blue) starts with moderate performance and shows a general downward trend with slight increases. AlexNet (red) shows the lowest performance, with a relatively flat trend.

Figure 3. Comparison of the training data attribution weights calculated form different network architectures. In the figure, we show the class-wise weights.

We calculate the training data attribution weights for each work into several different network architectures, includfect DDM? We have conducted our proposed DDM frameing AlexNetIN, ResNet18 AP [\[17\]](#page-8-25) and simple ViT [\[12\]](#page-8-26). class of training data for measuring the model behaviors on  $Dist_1$ . The test input is a batch of images with the groundtruth label of '2'. The experimental results are conducted on the MNIST dataset and the CIFAR-10 dataset, which are depicted in Fig. [3.](#page-6-0) From the figure, observations can be drawn that:

- All the networks with different architectures trace to the similar source training data (with the highest value with label  $'2$ ;
- For the simple classification task in MNIST, the training data attribution matrices look similar among all the architectures;
- For a more difficult task in CIFAR10, the training data attribution matrices look also similar in the trend, but vary in the absolute weight values among all the architectures.

Comparing DDM performance with other works. The comparative results with existing works are presented in Table [2.](#page-6-1) We compare the  $Dist_1$  influence with three other works. To evaluate, we identify the most influential data point and calculate the average distance metrics. It is evident that our method demonstrates greater accuracy in locating these influential data points.

DDM could be used as model diagnostic for lowquality training samples. In addition to analyzing the influence functions for specific test samples, the proposed DDM also offers a comprehensive model of the overall performance of the target network. We randomly sample 10% samples and calculate the The experimental results are depicted in Table [3,](#page-6-2) which is conducted on MNIST dataset. As depicted in the figure, the deletion of 10% of the training data actually improves the network's final performance. Therefore, our proposed DDM framework succeeds in model diagnostics by detecting and removing low-quality training samples.

DDM meets the privacy protection demand. To substantiate our previous assertion that the proposed reverse gradient matching enhances privacy protection, we conducted a comparison between the distilled samples generated using traditional gradient matching and our novel re-

<span id="page-7-0"></span>Image /page/7/Figure/0 description: The image displays a comparison of two methods, Gradient Matching and Reverse Gradient Matching, applied to two different datasets: MNIST and CIFAR-100. The left side of the image shows the MNIST dataset, with the first column displaying original handwritten digits from 0 to 9, and the second column showing results from Gradient Matching. The third column shows results from Reverse Gradient Matching. The right side of the image displays the CIFAR-100 dataset, with the first column showing original images, the second column showing results from Gradient Matching, and the third column showing results from Reverse Gradient Matching. The MNIST dataset results show recognizable digits for Gradient Matching, while Reverse Gradient Matching results are more abstract. The CIFAR-100 dataset results show colorful images for Gradient Matching, and more abstract, colorful patterns for Reverse Gradient Matching.

Figure 4. Visualization of condensed 10 image/class with ConvNet for MNIST (a) and CIFAR-100 (b). We compare the visualization results between gradient matching and reverse gradient matching. Each column represents a condensation of a cluster.

<span id="page-7-1"></span>Table 4. The new trained network's accuracies comparison. We compare the networks fine-tuning with the proposed DDM and gradient matching synthetic images.

| Dataset      | Method    | Acc. $(	heta_0)$ | Acc. $(	heta_{	au})$ |
|--------------|-----------|------------------|----------------------|
| MNIST        | Normal    | 12.5             | 95.7                 |
|              | DDM-Match | 12.6             | 85.0                 |
|              | DDM       | 0.6              | 95.7                 |
| CIFAR 10     | Normal    | 12.6             | 85.0                 |
|              | DDM-Match | 12.6             | 40.2                 |
|              | DDM       | 15.5             | 85.0                 |
| CIFAR100     | Normal    | 2.2              | 56.1                 |
|              | DDM-Match | 2.2              | 23.5                 |
|              | DDM       | 0.8              | 56.1                 |
| TinyImageNet | Normal    | 1.4              | 37.5                 |
|              | DDM-Match | 1.4              | 7.8                  |
|              | DDM       | 0.1              | 37.5                 |

verse gradient matching, as illustrated in Fig. [4.](#page-7-0) As evident from the visualization, in gradient matching data distillation, the synthetic images retain the characteristic features of the training set images, thus potentially revealing training data through these conspicuous patterns, particularly noticeable in the MNIST dataset. In contrast, in the visualization results of our reverse gradient matching, the distinctive features of the images are replaced by several indistinct patterns, akin to a form of obfuscation. This implies that, especially in scenarios with privacy concerns, our proposed DDM framework can be safely employed by directly releasing the synset, providing enhanced privacy protection for the original training data.

DDM could be used as a quick unlearn method. We also assert that the proposed reverse gradient matching improves matching performance, which is experimentally verified in Table [4.](#page-7-1) It's worth noting that traditional gradient matching begins by matching the initial state of the target network, resulting in the same 'Acc. $(\theta_0)$ ' as normal training. However, for more complex datasets (e.g., TinyImageNet), it struggles to match the final performance of the target network, 'Acc.  $(\theta_{\tau})$ '. In contrast, our proposed DDM commences from the final state of the target network and also effectively matches the initial performance of the target network. Thus, we contend that the proposed DDM significantly enhances matching performance.

# 5. Conclusion

In this paper, we introduce a novel framework known as DDM that facilitates a comprehensive analysis of training data's impact on a target machine learning model. The DDM framework comprises two key stages: the offline training stage and the online evaluation stage. During the offline training stage, we propose a novel technique, reverse gradient matching, to distill the influence of training data into a compact synset. In the online evaluation stage, we perturb the synset, enabling the rapid elimination of specific training clusters from the target network. This process culminates in the derivation of an attribution matrix tailored to the evaluation objectives. Overall, our DDM framework serves as a potent tool for unraveling the behavior of machine learning models, thus enhancing their performance and reliability.

Future research could extend the application of the DDM framework to diverse machine learning tasks and datasets. These applications could encompass fields as varied as natural language processing, reinforcement learning, computer vision, and beyond. The versatility of DDM offers opportunities to gain deeper insights into model behaviors, data quality, and training dynamics in these domains.

## Acknowledgement

This project is supported by the Ministry of Education Singapore, under its Academic Research Fund Tier 2 (Award Number: MOE-T2EP20122-0006), and the National Research Foundation, Singapore, under its AI Singapore Programme (AISG Award No: AISG2-RP-2021- 023).

# 6. References

- <span id="page-8-8"></span>[1] André Altmann, Laura Tolosi, Oliver Sander, and Thomas Lengauer. Permutation importance: a corrected feature importance measure. *Bioinformatics*, pages 1340–1347, 2010.  $\overline{2}$  $\overline{2}$  $\overline{2}$
- <span id="page-8-9"></span>[2] Daniel W. Apley and Jingyu Zhu. Visualizing the effects of predictor variables in black box supervised learning models. *Journal of the Royal Statistical Society: Series B (Statistical Methodology)*, 82, 2020. [2](#page-1-0)
- <span id="page-8-3"></span>[3] Daniel W Apley and Jingyu Zhu. Visualizing the effects of predictor variables in black box supervised learning models. *Journal of the Royal Statistical Society Series B: Statistical Methodology*, 82(4):1059–1086, 2020. [1](#page-0-0)
- <span id="page-8-12"></span>[4] Juhan Bae, Nathan Ng, Alston Lo, Marzyeh Ghassemi, and Roger B Grosse. If influence functions are the answer, then what is the question? *Advances in Neural Information Processing Systems*, 35:17953–17967, 2022. [2](#page-1-0)
- <span id="page-8-14"></span>[5] Lucas Bourtoule, Varun Chandrasekaran, Christopher A. Choquette-Choo, Hengrui Jia, Adelin Travers, Baiwu Zhang, David Lie, and Nicolas Papernot. Machine unlearning. *IEEE Symposium on Security and Privacy*, pages 141–159, 2021. [2](#page-1-0)
- <span id="page-8-15"></span>[6] Jonathan Brophy and Daniel Lowd. Machine unlearning for random forests. In *International Conference on Machine Learning*, 2021. [2](#page-1-0)
- <span id="page-8-0"></span>[7] Tom Brown, Benjamin Mann, Nick Ryder, Melanie Subbiah, Jared D Kaplan, Prafulla Dhariwal, Arvind Neelakantan, Pranav Shyam, Girish Sastry, Amanda Askell, et al. Language models are few-shot learners. *Advances in neural information processing systems*, 33:1877–1901, 2020. [1](#page-0-0)
- <span id="page-8-16"></span>[8] Yinzhi Cao and Junfeng Yang. Towards making systems forget with machine unlearning. *2015 IEEE Symposium on Security and Privacy*, pages 463–480, 2015. [2](#page-1-0)
- <span id="page-8-10"></span>[9] Rich Caruana, Yin Lou, Johannes Gehrke, Paul Koch, M. Sturm, and Noémie Elhadad. Intelligible models for healthcare: Predicting pneumonia risk and hospital 30-day readmission. *Proceedings of the 21th ACM SIGKDD International Conference on Knowledge Discovery and Data Mining*, 2015. [2](#page-1-0)
- <span id="page-8-1"></span>[10] Jacob Devlin, Ming-Wei Chang, Kenton Lee, and Kristina Toutanova. Bert: Pre-training of deep bidirectional transformers for language understanding. *arXiv preprint arXiv:1810.04805*, 2018. [1](#page-0-0)
- <span id="page-8-21"></span>[11] Tian Dong, Bo Zhao, and Lingjuan Lyu. Privacy for free: How does dataset condensation help privacy? In *International Conference on Machine Learning*, pages 5378–5396. PMLR, 2022. [4](#page-3-4)
- <span id="page-8-26"></span>[12] Alexey Dosovitskiy, Lucas Beyer, Alexander Kolesnikov, Dirk Weissenborn, Xiaohua Zhai, Thomas Unterthiner, Mostafa Dehghani, Matthias Minderer, Georg Heigold, Sylvain Gelly, et al. An image is worth 16x16 words: Transformers for image recognition at scale. *arXiv preprint arXiv:2010.11929*, 2020. [7](#page-6-3)
- <span id="page-8-11"></span>[13] Jerome H. Friedman. Greedy function approximation: A gradient boosting machine. *Annals of Statistics*, 29:1189–1232, 2001. [2](#page-1-0)

- <span id="page-8-4"></span>[14] Jerome H Friedman. Greedy function approximation: a gradient boosting machine. *Annals of statistics*, pages 1189– 1232, 2001. [1](#page-0-0)
- <span id="page-8-17"></span>[15] Antonio A. Ginart, Melody Y. Guan, Gregory Valiant, and James Y. Zou. Making ai forget you: Data deletion in machine learning. In *NeurIPS*, 2019. [2](#page-1-0)
- <span id="page-8-6"></span>[16] Han Guo, Nazneen Fatema Rajani, Peter Hase, Mohit Bansal, and Caiming Xiong. Fastif: Scalable influence functions for efficient model interpretation and debugging. *arXiv preprint arXiv:2012.15781*, 2020. [1,](#page-0-0) [2,](#page-1-0) [7](#page-6-3)
- <span id="page-8-25"></span>[17] Kaiming He, Xiangyu Zhang, Shaoqing Ren, and Jian Sun. Deep residual learning for image recognition. In *Proceedings of the IEEE conference on computer vision and pattern recognition*, pages 770–778, 2016. [7](#page-6-3)
- <span id="page-8-18"></span>[18] Yingzhe He, Guozhu Meng, Kai Chen, Jinwen He, and Xingbo Hu. Deepobliviate: A powerful charm for erasing data residual memory in deep neural networks. *ArXiv*, abs/2105.06209, 2021. [2](#page-1-0)
- <span id="page-8-13"></span>[19] Andrew Ilyas, Sung Min Park, Logan Engstrom, Guillaume Leclerc, and Aleksander Madry. Datamodels: Predicting predictions from training data. In *Proceedings of the 39th International Conference on Machine Learning*, 2022. [2,](#page-1-0) [7](#page-6-3)
- <span id="page-8-5"></span>[20] Been Kim, Martin Wattenberg, Justin Gilmer, Carrie Cai, James Wexler, Fernanda Viegas, et al. Interpretability beyond feature attribution: Quantitative testing with concept activation vectors (tcav). In *International conference on machine learning*, pages 2668–2677. PMLR, 2018. [1](#page-0-0)
- <span id="page-8-7"></span>[21] Pang Wei Koh and Percy Liang. Understanding black-box predictions via influence functions. In *International conference on machine learning*, pages 1885–1894. PMLR, 2017. [1,](#page-0-0) [2,](#page-1-0) [7](#page-6-3)
- <span id="page-8-23"></span>[22] Alex Krizhevsky. Learning multiple layers of features from tiny images. 2009. [6](#page-5-1)
- <span id="page-8-24"></span>[23] Alex Krizhevsky, Ilya Sutskever, and Geoffrey E. Hinton. Imagenet classification with deep convolutional neural networks. *Communications of the ACM*, 60:84 – 90, 2012. [6](#page-5-1)
- <span id="page-8-22"></span>[24] Yann LeCun, Léon Bottou, Yoshua Bengio, and Patrick Haffner. Gradient-based learning applied to document recognition. *Proc. IEEE*, 86:2278–2324, 1998. [6](#page-5-1)
- <span id="page-8-19"></span>[25] Saehyung Lee, Sanghyuk Chun, Sangwon Jung, Sangdoo Yun, and Sungroh Yoon. Dataset condensation with contrastive signals. In *International Conference on Machine Learning*, pages 12352–12364. PMLR, 2022. [2](#page-1-0)
- [26] Songhua Liu and Xinchao Wang. Mgdd: A meta generator for fast dataset distillation. In *Advances in Neural Information Processing Systems*, 2023.
- [27] Songhua Liu, Kai Wang, Xingyi Yang, Jingwen Ye, and Xinchao Wang. Dataset distillation via factorization. In *Advances in Neural Information Processing Systems*, 2022.
- <span id="page-8-20"></span>[28] Songhua Liu, Jingwen Ye, Runpeng Yu, and Xinchao Wang. Slimmable dataset condensation. In *Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition*, pages 3759–3768, 2023. [2](#page-1-0)
- <span id="page-8-2"></span>[29] Scott M Lundberg and Su-In Lee. A unified approach to interpreting model predictions. *Advances in neural information processing systems*, 30, 2017. [1](#page-0-0)

- <span id="page-9-16"></span>[30] Quoc Phong Nguyen, Bryan Kian Hsiang Low, and Patrick Jaillet. Variational bayesian unlearning. *Advances in Neural Information Processing Systems*, 33:16025–16036, 2020. [2](#page-1-0)
- <span id="page-9-23"></span>[31] Timothy Nguyen, Roman Novak, Lechao Xiao, and Jaehoon Lee. Dataset distillation with infinitely wide convolutional networks. *Advances in Neural Information Processing Systems*, 34:5186–5198, 2021. [2](#page-1-0)
- <span id="page-9-4"></span>[32] Garima Pruthi, Frederick Liu, Satyen Kale, and Mukund Sundararajan. Estimating training data influence by tracing gradient descent. *Advances in Neural Information Processing Systems*, 33:19920–19930, 2020. [1](#page-0-0)
- <span id="page-9-0"></span>[33] Aditya Ramesh, Mikhail Pavlov, Gabriel Goh, Scott Gray, Chelsea Voss, Alec Radford, Mark Chen, and Ilya Sutskever. Zero-shot text-to-image generation. In *International Conference on Machine Learning*, pages 8821–8831. PMLR, 2021. [1](#page-0-0)
- <span id="page-9-7"></span>[34] Marco Tulio Ribeiro, Sameer Singh, and Carlos Guestrin. "why should i trust you?": Explaining the predictions of any classifier. *Proceedings of the 22nd ACM SIGKDD International Conference on Knowledge Discovery and Data Mining*, 2016. [2](#page-1-0)
- <span id="page-9-1"></span>[35] Marco Tulio Ribeiro, Sameer Singh, and Carlos Guestrin. " why should i trust you?" explaining the predictions of any classifier. In *Proceedings of the 22nd ACM SIGKDD international conference on knowledge discovery and data mining*, pages 1135–1144, 2016. [1](#page-0-0)
- <span id="page-9-25"></span>[36] Olga Russakovsky, Jia Deng, Hao Su, Jonathan Krause, Sanjeev Satheesh, Sean Ma, Zhiheng Huang, Andrej Karpathy, Aditya Khosla, Michael S. Bernstein, Alexander C. Berg, and Li Fei-Fei. Imagenet large scale visual recognition challenge. *International Journal of Computer Vision*, 115:211– 252, 2014. [6](#page-5-1)
- <span id="page-9-14"></span>[37] Andrea Schioppa, Polina Zablotskaia, David Vilar, and Artem Sokolov. Scaling up influence functions. In *AAAI Conference on Artificial Intelligence*, 2021. [2](#page-1-0)
- <span id="page-9-26"></span>[38] Andrea Schioppa, Polina Zablotskaia, David Vilar, and Artem Sokolov. Scaling up influence functions. In *Proceedings of the AAAI Conference on Artificial Intelligence*, pages 8179–8186, 2022. [7](#page-6-3)
- <span id="page-9-17"></span>[39] Ayush Sekhari, Jayadev Acharya, Gautam Kamath, and Ananda Theertha Suresh. Remember what you want to forget: Algorithms for machine unlearning. *Advances in Neural Information Processing Systems*, 34, 2021. [2](#page-1-0)
- <span id="page-9-2"></span>[40] Ramprasaath R Selvaraju, Michael Cogswell, Abhishek Das, Ramakrishna Vedantam, Devi Parikh, and Dhruv Batra. Grad-cam: Visual explanations from deep networks via gradient-based localization. In *Proceedings of the IEEE international conference on computer vision*, pages 618–626, 2017. [1](#page-0-0)
- <span id="page-9-8"></span>[41] Ramprasaath R. Selvaraju, Abhishek Das, Ramakrishna Vedantam, Michael Cogswell, Devi Parikh, and Dhruv Batra. Grad-cam: Visual explanations from deep networks via gradient-based localization. *International Journal of Computer Vision*, 128:336–359, 2019. [2](#page-1-0)
- <span id="page-9-9"></span>[42] Mateusz Staniak and Przemyslaw Biecek. Explanations of model predictions with live and breakdown packages. *arXiv preprint arXiv:1804.01955*, 2018. [2](#page-1-0)

- <span id="page-9-24"></span>[43] Ilia Sucholutsky and Matthias Schonlau. Soft-label dataset distillation and text dataset distillation. In *2021 International Joint Conference on Neural Networks (IJCNN)*, pages 1–8. IEEE, 2021. [2](#page-1-0)
- <span id="page-9-10"></span>[44] Mukund Sundararajan, Ankur Taly, and Qiqi Yan. Axiomatic attribution for deep networks. In *International conference on machine learning*, pages 3319–3328. PMLR, 2017. [2](#page-1-0)
- [45] Berk Ustun and Cynthia Rudin. Supersparse linear integer models for optimized medical scoring systems. *Machine Learning*, 102:349–391, 2015.
- <span id="page-9-11"></span>[46] Dennis Wei, Sanjeeb Dash, Tian Gao, and Oktay Günlük. Generalized linear rule models. In *ICML*, 2019. [2](#page-1-0)
- <span id="page-9-5"></span>[47] Yinjun Wu, Edgar Dobriban, and Susan B. Davidson. Deltagrad: Rapid retraining of machine learning models. In *International Conference on Machine Learning*, 2020. [1](#page-0-0)
- <span id="page-9-3"></span>[48] Xingyi Yang and Xinchao Wang. Diffusion model as representation learner. In *IEEE/CVF International Conference on Computer Vision*, 2023. [1](#page-0-0)
- <span id="page-9-18"></span>[49] Jingwen Ye, Yifang Fu, Jie Song, Xingyi Yang, Songhua Liu, Xin Jin, Mingli Song, and Xinchao Wang. Learning with recoverable forgetting. In *European Conference on Computer Vision*, 2022. [2](#page-1-0)
- <span id="page-9-19"></span>[50] Jingwen Ye, Songhua Liu, and Xinchao Wang. Partial network cloning. In *2023 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)*, 2023. [2](#page-1-0)
- <span id="page-9-20"></span>[51] Ruonan Yu, Songhua Liu, and Xinchao Wang. Dataset distillation: A comprehensive review. In *IEEE Transactions on Pattern Analysis and Machine Intelligence*, 2024. [2](#page-1-0)
- <span id="page-9-12"></span>[52] Matthew D Zeiler and Rob Fergus. Visualizing and understanding convolutional networks. In *European conference on computer vision*, pages 818–833. Springer, 2014. [2](#page-1-0)
- <span id="page-9-15"></span>[53] Yingyan Zeng, Jiachen T Wang, Si Chen, Hoang Anh Just, Ran Jin, and Ruoxi Jia. Modelpred: A framework for predicting trained model from training data. In *2023 IEEE Conference on Secure and Trustworthy Machine Learning (SaTML)*, pages 432–449. IEEE, 2023. [2](#page-1-0)
- <span id="page-9-21"></span>[54] Bo Zhao and Hakan Bilen. Dataset condensation with differentiable siamese augmentation. In *International Conference on Machine Learning*, 2021. [2](#page-1-0)
- <span id="page-9-6"></span>[55] Bo Zhao, Konda Reddy Mopuri, and Hakan Bilen. Dataset condensation with gradient matching. In *Ninth International Conference on Learning Representations 2021*, 2021. [2,](#page-1-0) [4](#page-3-4)
- <span id="page-9-13"></span>[56] Bolei Zhou, Aditya Khosla, Agata Lapedriza, Aude Oliva, ` and Antonio Torralba. Learning deep features for discriminative localization. *IEEE Conference on Computer Vision and Pattern Recognition*, pages 2921–2929, 2016. [2](#page-1-0)
- <span id="page-9-22"></span>[57] Yongchao Zhou, Ehsan Nezhadarya, and Jimmy Ba. Dataset distillation using neural feature regression. *arXiv preprint arXiv:2206.00719*, 2022. [2](#page-1-0)

# Distilled Datamodel with Reverse Gradient Matching

# Supplementary Material

In this document, we present supplementary materials that couldn't be accommodated within the main manuscript due to page limitations. Specifically, we offer additional details on the proposed DDM framework, elucidating the computation of the hierarchical attribution matrix through our framework and the methodology employed for data clustering.

## 6. More Details of DDM Framework

### 6.1. Data Clustering of DDM

To enhance tracing performance, we concentrate on calculating the weighting matrix for each batch of data, rather than for each individual image. That is, the total cluster number K is set as  $L < K < |\mathcal{D}|$  (L is the total number of the class). This approach is taken as the impact of a single image becomes negligible when training with a large number of images.

In our proposed DDM framework, we employ data clustering to partition the training data into several distinct batches. Specifically, we utilize a pre-trained feature extractor to embed the training images into the same feature space. Subsequently, we apply K-means clustering to the features, denoting each cluster of data as  $\mathcal{D}_{c,k}$ , where  $l = 1, 2, ..., L$ and  $c = 1, 2, ..., C$ . This implies that we cluster the images within a class into  $C$  clusters. For each class of data, we set the number of clusters  $C$  to be 10.

<span id="page-10-1"></span>

### 6.2. Accelerating with Hierarchical DDM

As stated in the main paper, to enhance the efficiency of online evaluation, we employ hierarchical attribution calculation, which expedites the leave-one-out retraining process. Note that on the offline training stage, both the class-wise and cluster-wise synset are learned, where the class-wise one is denoted as  $\mathcal{S}_{class} = \{S_1, S_2, ..., S_L\}$  and the clusterwise one is denoted as  $\mathcal{S}_{cluster} = \{\mathcal{S}_{l,1}, \mathcal{S}_{l,2}, ..., \mathcal{S}_{l,C}\}_{l=1}^{L}$ and  $K = L \times C$ . With this hierarchical synset, we don't need to apply the retraining to the entire perturbation set  $|P_t|$ . Instead, it is calculated as:

$$
l \leftarrow \underset{l}{\arg \max W_l}, \quad 1 \le l \le L, \nc \leftarrow \underset{l}{\arg \max W_{l,c}}, \quad 1 \le c \le C
$$
\n(11)

<span id="page-10-2"></span>In the first equation, we require  $|P_t| = L$ , and in the second one, we need  $|P_t| = C$ . Therefore, with this hierarchical synset, we only need to retrain the networks  $C + L$  times, significantly reducing the original  $C \times L$  retraining to  $C + L$ .

In the standard setting for the CIFAR-10 dataset, where  $K = 10 \times 10$ , and for the CIFAR-100 dataset, where  $K =$   $100 \times 10$ , the hierarchical synset accelerates the process by 5 times in the CIFAR-10 dataset and 10 times in the CIFAR-100 dataset.

### 6.3. Reverse Gradient Matching for Matching Performance Enhancement

In the main paper, we claim that our proposed reverse gradient matching provides enhanced matching performance, which has been proved in the experiment ('DDM could be used as a quick unlearn method'). The reasons for it can be analyzed by the accumulated errors. Since we performance the leave-one-out retraining, which means that each time we unlearn one data cluster. Thus, for unlearning the data cluster  $\mathcal{D}_{\kappa}$ , the errors of our proposed reverse gradient matching can be denoted as:

<span id="page-10-0"></span>
$$
\epsilon_{\kappa} = \sum_{t} \left| \nabla \mathcal{L}(\theta_{\tau-t}, \mathcal{S}_{\kappa}) + \nabla \mathcal{L}(\theta_t, \mathcal{D}_{\kappa}) \right|, \qquad (12)
$$

where  $1 < \kappa < K$  and  $S_{\kappa}$  is corresponding synset that matches the data cluster  $\mathcal{D}_{\kappa}$  with reverse gradients. And denote  $\mathcal{X}_{\kappa}$  as the corresponding synset that matches the data cluster  $\mathcal{D}_{\kappa}$  with normal gradients, then the accumulated errors could be denotes as:

$$
\epsilon_{\kappa} = \sum_{t} \sum_{k \neq \kappa} |\nabla \mathcal{L}(\theta_t, \mathcal{X}_k) - \nabla \mathcal{L}(\theta_t, \mathcal{D}_\kappa)|, \qquad (13)
$$

which, compared with Eq. [12,](#page-10-0) introduces an additional summation operation, resulting in larger accumulated error values.

## 7. Experimental Setting

The experimental setting of the proposed DDM framework is depicted in Table [5,](#page-11-0) where the dataset information, network backbones (ConvNet, AlexNet, ResNet and Simple Vit) are listed. Two different distance functions  $Dist(\cdot)$  are utilized in Eq. [6](#page-3-5) of the main paper.

### 8. More Experiments

### 8.1. Class-wise DDM vs Cluster-wise DDM

In our proposed DDM framework, both the class-wise and the cluster-wise synthetic images are obtained for efficient tracing by hierarchical search (details in Sec. [6.2\)](#page-10-1). In Fig. [6,](#page-11-1) we depict the tracing results (got by Eq. [11\)](#page-10-2) with the most influential datapoints in terms of  $Dist_1$ , with  $||X_t|| = 1$  ('one image inference' with groundtruth label as '5') and  $||X_t|| = 256$  ('a batch of images inference' with

<span id="page-11-0"></span>

| <b>Dataset</b> | <b>Network</b>         | <b>Settings</b> |        |                        |                 |             |             |  |
|----------------|------------------------|-----------------|--------|------------------------|-----------------|-------------|-------------|--|
|                |                        | lr_net          | lr_img | <b>Training Epochs</b> | Step for Synset | Step Length | Dist        |  |
| <b>MNIST</b>   | ConvNet/AlexNet/ResNet | 0.001           | 0.1    | 30                     | 50              | 4           | Cosine Dist |  |
|                | Simple ViT             | 0.001           | 0.005  | 30                     | 50              | 4           | <b>MSE</b>  |  |
| CIFAR10        | ConvNet/AlexNet/ResNet | 0.01            | 0.1    | 30                     | 50              | 4           | Cosine Dist |  |
|                | Simple ViT             | 0.01            | 0.005  | 30                     | 50              | 4           | <b>MSE</b>  |  |
| CIFAR100       | ConvNet/AlexNet/ResNet | 0.01            | 0.1    | 30                     | 50              | 4           | Cosine Dist |  |
|                | Simple ViT             | 0.01            | 0.005  | 30                     | 50              | 4           | <b>MSE</b>  |  |
| TinyImgNet     | ConvNet/AlexNet/ResNet | 0.01            | 0.1    | 30                     | 50              | 4           | Cosine Dist |  |
|                | Simple ViT             | 0.01            | 0.005  | 30                     | 50              | 4           | <b>MSE</b>  |  |

Table 5. The detailed settings in the experimental implementation.

<span id="page-11-2"></span>Image /page/11/Figure/2 description: The image displays a grid of generated images, categorized by two labels: "GM" and "DDM". The "GM" section is further divided into two rows, each containing 10 images. The top row of the "GM" section features images of insects, flowers, and natural landscapes. The second row of the "GM" section shows images of food items, including what appear to be fruits and possibly coffee. The "DDM" section is also divided into three rows, each with 10 images. These images are more abstract and less clearly defined than those in the "GM" section, with a recurring pattern of colorful, somewhat blurry visuals that could be interpreted as abstract art or distorted natural scenes.

Figure 5. Visualization of condensed 10 image/class with ConvNet for TinyImageNet dataset. We compare the visualization results between gradient matching (GM) and reverse gradient matching (DDM). In each visualization, each column represents a condensation of a cluster.

<span id="page-11-1"></span>Image /page/11/Figure/4 description: The image displays two scatter plots side-by-side, each illustrating data points and trend lines. The top legend indicates four elements: 'Class-wise DDM' represented by pink squares connected by a line, 'Cluster-wise DDM' represented by blue circles, 'Class Tracing' represented by black crosses, and 'Cluster Tracing' represented by yellow crosses. The left plot is titled 'One Image Inference' and the right plot is titled 'A batch of Images Inference'. Both plots show a distribution of blue circles, with a pink line connecting several pink squares that generally follows the trend of the blue circles. Black crosses and yellow crosses are scattered within the plots, indicating specific data points or events. The x-axis for both plots ranges from 0 to 100, and the y-axis has grid lines but no explicit labels.

Figure 6. Locating the most influential data points, where the results are computed with distance function  $Dist_1$  on CIFAR10 dataset. We firstly locate the class with the class-wise synset and then to the cluster with the cluster-wise synset of that class.

<span id="page-11-3"></span>Image /page/11/Figure/6 description: The image is a line graph with a dashed blue line labeled "Exact Unlearn" and a solid red line labeled "DDM". The x-axis ranges from 0 to 100, and the y-axis ranges from -0.03 to 0.03. Both lines show fluctuating values across the x-axis. The red line generally stays above the x-axis, with peaks reaching around 0.025, while the blue line generally stays below the x-axis, with troughs reaching around -0.025.

Figure 7. Comparing the empirical error between the exact unlearn model and the DDM model. We scale and flip 'exact unlearn' for better visual comparison.

all groundtruth labels as '2'). As can be observed, both the class-wise tracing and cluster-wise tracing give consistent tracing results. In addition, DDM works on both the sin-

<span id="page-12-0"></span>Image /page/12/Figure/0 description: The image contains three line graphs. The top graph is titled "Influence on MNIST" and shows three lines representing "ini-1" (red), "ini-2" (green), and "ini-3" (blue) plotted against values from 1 to 10. The middle graph is titled "Influence on CIFAR-10" and also shows three lines representing "ini-1" (red), "ini-2" (green), and "ini-3" (blue) plotted against values from 1 to 10. The bottom graph is titled "Influence on CIFAR-100" and displays three dotted lines representing "ini-1" (red), "ini-2" (green), and "ini-3" (blue) plotted against values from 0 to 100.

Figure 8. Comparison of the training data attribution matrix with different initializations. The experiments are conducted on MNIST, CIFAR-10 and CIFAR-100 datasets, with the ConvNet as the base network.

gle image inference (gt label belongs to the 5-th label) and multi-image inference cases (all gt labels belong to the 2-th label). indicating the robustness of the DDM framework.

### 8.2. More Visualization Results

In Fig. [4](#page-7-0) of the main paper, we visualize the synset on MNIST dataset and CIFAR-100 dataset. Here, we provide additional visualization results on the TinyImageNet dataset, as depicted in Fig. [5.](#page-11-2)

The figure provides additional evidence supporting the privacy protection capabilities of our proposed DDM, as the synsets exhibit no recognizable objects. Moreover, the uniqueness of synthetic images learned from each data cluster highlights the importance of the clustering operation.

## 8.3. Comparing DDM for Machine Unlearning with Exact Unlearn

As discussed in the method section, we optimize the synthetic images using the proposed reverse gradient matching, which are then employed to fine-tune the target network to mitigate the impact of specific data clusters. It's important to note that we don't anticipate the DDM framework to perfectly mimic the exact unlearned model. Instead, our focus is on whether it can capture important characteristics of the model. In this experiment, we use empirical error for comparison, as depicted in Fig. [7.](#page-11-3) In the figure, we scale and flip 'exact unlearn', which, in an ideal situation, should be symmetric to 'DDM'. As observed, they are roughly symmetrical, indicating that the DDM models can serve as a surrogate for analyzing the target model and perform well in eliminating certain data points compared to the exact unlearn.

## 8.4. How Did Different Initializations Influence the Network?

Here, we investigate the impact of different initializations on the computed training data matrix. We perform this comparative experiment on the MNIST, CIFAR-10, and CIFAR-100 datasets trained using the ConvNet architecture. We consider three different initializations: 'Kaiming' (ini-1), 'Normal' (ini-2), and 'xavier' (ini-3).

The experimental results are illustrated in Fig. [8,](#page-12-0) revealing the following observations:

- The training data attribution is robust across different initialization methods, yielding similar attribution matrices. This observation holds true for the MNIST, CIFAR-10, and CIFAR-100 datasets.
- With an increase in the size of the training data, the attribution matrices learned from the three different initializations become more diverse. This divergence may arise from the selection of the basic ConvNet as the backbone, potentially leading to local optima.

### 9. DDM for Analysis of Other Model Behaviors

In the main paper, we detailed instructions on analyzing networks by identifying the most influential training data for certain test images. We emphasize that both the local and global behaviors of the network can be captured by the training data matrix when using specific distance functions defined by the certain evaluation objective.

Here, we present various distance functions for different evaluation objectives.

### 9.1. Inference Function of Certain Test Samples

The distance function is defined in Eq. [10](#page-5-2) in the main paper. This function aims to identify which part of the training data is responsible for the final prediction. We provide different

<span id="page-13-0"></span>Table 6. Detecting noisy training data for the target network. We add random noise with perturbation norm of 0.1 to each image.

| <b>Percentage</b> | 0%   | 10%  | 20%  | 30%  | 40%  | <b>Percentage</b>    | <b>0%</b> | <b>10%</b> | <b>20%</b> | <b>30%</b> | <b>40%</b> |
|-------------------|------|------|------|------|------|----------------------|-----------|------------|------------|------------|------------|
| Random Select     | 78.4 | 70.5 | 68.8 | 61.3 | 55.7 | <b>Random Select</b> | 94.1      | 93.5       | 88.7       | 87.3       | 85.8       |
| <b>DDM</b>        | 78.4 | 82.0 | 79.3 | 73.6 | 70.1 | DDM                  | 94.1      | 94.2       | 94.2       | 92.9       | 87.3       |

evaluation objectives, including those influencing the current predictions most/least and those contributing the most to making correct predictions.

And the corresponding experiments have been listed in the main paper in Table [1.](#page-5-0)

# 9.2. Model Diagnostic for Low-quality Training Samples

To identify essential part of data that contributes to the overall prediction ability. We have already displayed the corresponding experiments in Table [3](#page-6-2) by sorting the training cluster with its influence to the final network performance. To be specific, we randomly choose part of training data as the validation set  $V$ , then we could determine distance function for the model global performance evaluation as:

<span id="page-13-1"></span>
$$
\sum_{x_t \in \mathcal{V}} \mathcal{D}ist_2(x_t),\tag{14}
$$

where  $Dist_2$  is pre-defined in Eq. [10](#page-5-2) for identifying those with the most influence on whether the model makes correct predictions.

In addition to the previous experiment focused on removing locally low-quality data from the training set, we conducted an additional experiment involving the introduction of random noise to 20% of the training data. Subsequently, we removed the data based on the evaluation objectives mentioned earlier.

The experimental results are presented in Table [6.](#page-13-0) The table reveals that the addition of random noise has a detrimental effect on the performance of the target model. However, when selectively removing data based on our proposed DDM, the network's performance improves, demonstrating a more significant improvement compared to the 'Random Select' approach.

#### 9.3. Transferability Between Different Networks.

And we also find that our proposed DDM also provides to measure and improve the transferability between different networks. To be concrete, in the typical work like knowledge distillation, the student network can be trained by:

$$
\mathcal{L}_{\text{total}} = (1 - \alpha) \cdot \mathcal{L}_{ce}(\mathcal{N}(x), y^t) + \alpha \cdot \mathcal{KL}(\mathcal{N}(x), \mathcal{M}(x)),
$$
\n(15)

where  $N$  represents the student network to be trained,  $M$  is the target network,  $\mathcal{KL}$  denotes the KL-divergence, and  $\alpha$  is

<span id="page-13-2"></span>Table 7. DDM for network tranferability while distillation.

the balancing weight. It is important to note that not all networks may experience performance improvement through such distillation, as there could be conflicts that hinder the overall performance enhancement.

We use the KL-divergence as the distance function and calculate the whole evaluation distance in the similar way as in Eq. [14.](#page-13-1) The experiments are conducted on Table [7.](#page-13-2) In the table, we delete some percentages of data from the network training and distill it to train the student network. The experiments are conducted on CIFAR-10 dataset on ResNet-18, and the teacher network is optimized by knowledge undistillation. Thus, directly distill from the teacher would cause accuracy drop. And deleting some samples from the training data could attack such knowledge distillation and improve the network performance.

## 9.4. To be Explored.

In this paper, we distilled the training gradients to several synthetic images, which enables the fast impact elimination. Thus, it is possible to build the training data attributes with the distance function defined between networks, which shows great potential to explore other kinds of network behaviors.
#!/usr/bin/env python3
"""
Real Terminal-Based Research Implementation
Executes actual research with real API calls and terminal output
"""

import os
import sys
import time
import yaml
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from enhanced_research_orchestrator import EnhancedResearchOrchestrator

class RealResearchTerminal:
    """Real research execution with terminal output"""
    
    def __init__(self):
        self.orchestrator = None
        self.session_id = None
        
    def initialize_orchestrator(self):
        """Initialize the research orchestrator with real API keys"""
        print("🔧 Initializing Research Orchestrator...")
        
        try:
            self.orchestrator = EnhancedResearchOrchestrator(silent=False)
            print(f"✅ Orchestrator initialized with {len(self.orchestrator.tools)} tools")
            print(f"🤖 Available model providers: {list(self.orchestrator.model_interface.providers.keys())}")
            
            # Check which providers are actually available
            available_providers = self.orchestrator.model_interface.get_available_providers()
            print(f"🟢 Active providers: {available_providers}")
            
            if not available_providers:
                print("⚠️ No model providers are available. Please configure API keys.")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize orchestrator: {e}")
            return False
    
    def configure_api_keys_interactive(self):
        """Interactive API key configuration"""
        print("\n🔑 API Key Configuration")
        print("=" * 50)
        
        # Load current config
        with open('config.yaml', 'r') as f:
            config = yaml.safe_load(f)
        
        if 'unified_models' not in config:
            config['unified_models'] = {'providers': {}, 'default_provider': 'gemini', 'fallback_order': []}
        
        providers = {
            'gemini': {
                'name': 'Google Gemini',
                'models': ['gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-1.5-pro', 'gemini-1.5-flash'],
                'url': 'https://aistudio.google.com/app/apikey'
            },
            'openai': {
                'name': 'OpenAI',
                'models': ['o4-mini-2025-04-16'],
                'url': 'https://platform.openai.com/api-keys'
            },
            'anthropic': {
                'name': 'Anthropic Claude',
                'models': ['claude-4-sonnet', 'claude-4-opus', 'claude-3-5-sonnet-20241022', 'claude-3-5-haiku-20241022', 'claude-3-opus-20240229'],
                'url': 'https://console.anthropic.com/settings/keys'
            },
            'deepseek': {
                'name': 'DeepSeek',
                'models': ['deepseek-chat', 'deepseek-coder', 'deepseek-r1'],
                'url': 'https://platform.deepseek.com/api_keys'
            },
            'moonshot': {
                'name': 'Moonshot AI (Kimi)',
                'models': ['moonshot-v1-8k', 'moonshot-v1-32k', 'moonshot-v1-128k', 'moonshot-v1-auto'],
                'url': 'https://platform.moonshot.cn/console/api-keys'
            },
            'openrouter': {
                'name': 'OpenRouter',
                'models': ['x-ai/grok-4', 'moonshotai/kimi-k2:free', 'moonshotai/kimi-k2', 'openai/o4-mini-2025-04-16', 'anthropic/claude-4-sonnet', 'anthropic/claude-4-opus'],
                'url': 'https://openrouter.ai/keys'
            }
        }
        
        configured_any = False
        
        for provider_id, provider_info in providers.items():
            print(f"\n📡 {provider_info['name']}")
            print(f"   Get API key: {provider_info['url']}")
            
            api_key = input(f"   Enter {provider_info['name']} API key (or press Enter to skip): ").strip()
            
            if api_key:
                print(f"   Available models: {', '.join(provider_info['models'])}")
                model = input(f"   Select model [{provider_info['models'][0]}]: ").strip()
                if not model:
                    model = provider_info['models'][0]
                
                # Configure provider
                config['unified_models']['providers'][provider_id] = {
                    'api_key': api_key,
                    'model': model,
                    'rate_limit_delay': 5.0 if provider_id == 'gemini' else 1.0,
                    'timeout': 600
                }
                
                if provider_id == 'openai':
                    config['unified_models']['providers'][provider_id]['base_url'] = 'https://api.openai.com/v1'
                elif provider_id == 'anthropic':
                    config['unified_models']['providers'][provider_id]['base_url'] = 'https://api.anthropic.com/v1'
                elif provider_id == 'deepseek':
                    config['unified_models']['providers'][provider_id]['base_url'] = 'https://api.deepseek.com/v1'
                elif provider_id == 'moonshot':
                    config['unified_models']['providers'][provider_id]['base_url'] = 'https://api.moonshot.cn/v1'
                elif provider_id == 'openrouter':
                    config['unified_models']['providers'][provider_id]['base_url'] = 'https://openrouter.ai/api/v1'
                
                print(f"   ✅ {provider_info['name']} configured with {model}")
                configured_any = True
        
        if configured_any:
            # Set default provider and fallback order
            configured_providers = list(config['unified_models']['providers'].keys())
            config['unified_models']['default_provider'] = configured_providers[0]
            config['unified_models']['fallback_order'] = configured_providers
            
            # Save config
            with open('config.yaml', 'w') as f:
                yaml.dump(config, f, default_flow_style=False)
            
            print(f"\n✅ Configuration saved with {len(configured_providers)} providers")
            return True
        else:
            print("\n⚠️ No API keys configured. Using existing configuration.")
            return False
    
    def run_real_research(self, query: str, research_type: str = "literature_review", 
                         model_provider: str = None, num_agents: int = 3):
        """Run real research with terminal output"""
        
        print(f"\n🔬 STARTING REAL RESEARCH")
        print("=" * 60)
        print(f"📝 Query: {query}")
        print(f"🔍 Type: {research_type}")
        print(f"🤖 Model: {model_provider or 'Auto (with fallback)'}")
        print(f"👥 Agents: {num_agents}")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            # Execute real research
            result = self.orchestrator.orchestrate_research(
                research_query=query,
                research_type=research_type,
                model_provider=model_provider,
                context_aware=True,
                context_mode="standard",
                num_agents=num_agents
            )
            
            execution_time = time.time() - start_time
            
            print(f"\n🎯 RESEARCH COMPLETED")
            print("=" * 60)
            print(f"⏱️ Execution Time: {execution_time:.1f}s")
            print(f"📊 Status: {result.get('status', 'unknown')}")
            
            if result.get('status') == 'success':
                print(f"🤖 Model Used: {result.get('model_provider', 'Auto')}")
                print(f"👥 Agents: {result.get('num_agents', 'Unknown')}")
                print(f"🔄 Context Mode: {result.get('context_mode', 'standard')}")
                
                # Display phase results
                phases = result.get('phases', {})
                for phase_name, phase_results in phases.items():
                    print(f"\n📋 {phase_name.upper().replace('_', ' ')}:")
                    print("-" * 40)
                    
                    if isinstance(phase_results, list):
                        for i, agent_result in enumerate(phase_results, 1):
                            status = agent_result.get('status', 'unknown')
                            model_used = agent_result.get('model_used', 'unknown')
                            exec_time = agent_result.get('execution_time', 0)
                            
                            print(f"  Agent {i}: {status} ({model_used}, {exec_time:.1f}s)")
                            
                            if status == 'success':
                                response = agent_result.get('response', '')
                                # Show first 200 characters
                                preview = response[:200] + "..." if len(response) > 200 else response
                                print(f"    Response: {preview}")
                            else:
                                print(f"    Error: {agent_result.get('response', 'Unknown error')}")
                
                # Display synthesis
                synthesis = result.get('final_synthesis', '')
                if synthesis and synthesis != "No synthesis available":
                    print(f"\n🧠 FINAL SYNTHESIS:")
                    print("=" * 60)
                    print(synthesis)
                else:
                    print(f"\n⚠️ No synthesis generated")
                
                # Save results
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"research_results_{timestamp}.json"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                
                print(f"\n💾 Results saved to: {filename}")
                
            else:
                print(f"❌ Research failed: {result.get('error', 'Unknown error')}")
            
            return result
            
        except Exception as e:
            print(f"\n❌ Research execution failed: {e}")
            import traceback
            traceback.print_exc()
            return {"status": "error", "error": str(e)}
    
    def interactive_research_session(self):
        """Interactive research session"""
        print("🎓 RESEARCH HEAVY - Real Terminal Research")
        print("=" * 60)
        
        # Initialize orchestrator
        if not self.initialize_orchestrator():
            print("\n🔑 Let's configure API keys first...")
            if not self.configure_api_keys_interactive():
                print("❌ Cannot proceed without API keys. Exiting.")
                return
            
            # Reinitialize with new config
            if not self.initialize_orchestrator():
                print("❌ Still cannot initialize. Please check your API keys.")
                return
        
        while True:
            print(f"\n{'='*60}")
            print("🔬 RESEARCH OPTIONS")
            print("1. Run Literature Review")
            print("2. Run Gap Analysis") 
            print("3. Run Idea Generation")
            print("4. Run Implementation Research")
            print("5. Run Comprehensive Research")
            print("6. Configure API Keys")
            print("7. Exit")
            
            choice = input("\nSelect option (1-7): ").strip()
            
            if choice == '7':
                print("👋 Goodbye!")
                break
            elif choice == '6':
                self.configure_api_keys_interactive()
                self.initialize_orchestrator()  # Reinitialize
                continue
            elif choice in ['1', '2', '3', '4', '5']:
                research_types = {
                    '1': 'literature_review',
                    '2': 'gap_analysis', 
                    '3': 'idea_generation',
                    '4': 'implementation',
                    '5': 'comprehensive'
                }
                
                research_type = research_types[choice]
                
                # Get research parameters
                query = input("\n📝 Enter research query: ").strip()
                if not query:
                    print("❌ Query cannot be empty")
                    continue
                
                # Get model provider
                available_providers = self.orchestrator.model_interface.get_available_providers()
                print(f"\n🤖 Available providers: {available_providers}")
                model_provider = input("Select model provider (or press Enter for auto): ").strip()
                if model_provider and model_provider not in available_providers:
                    model_provider = None
                
                # Get number of agents
                try:
                    num_agents = int(input("👥 Number of agents [3]: ") or "3")
                    num_agents = max(1, min(num_agents, 20))  # Limit 1-20
                except ValueError:
                    num_agents = 3
                
                # Run research
                self.run_real_research(query, research_type, model_provider, num_agents)
            
            else:
                print("❌ Invalid option. Please select 1-7.")

def main():
    """Main entry point"""
    terminal = RealResearchTerminal()
    terminal.interactive_research_session()

if __name__ == '__main__':
    main()

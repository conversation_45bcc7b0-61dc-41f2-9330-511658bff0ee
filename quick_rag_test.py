#!/usr/bin/env python3
"""
Quick RAG Test
Tests the current state of the RAG system
"""

from tools.knowledge_base_tool import KnowledgeBaseTool

def quick_test():
    """Quick test of RAG system"""
    print("🧪 QUICK RAG SYSTEM TEST")
    print("=" * 40)
    
    try:
        # Initialize
        config = {}
        kb_tool = KnowledgeBaseTool(config)
        
        # Test status
        status = kb_tool.execute(action="status")
        print(f"Status: {status.get('status', 'unknown')}")
        print(f"Chunks: {status.get('total_chunks', 0):,}")
        print(f"Embedding dim: {status.get('embedding_dimension', 0)}")
        print(f"Vector store: {status.get('vector_store', 'unknown')}")
        
        # Quick search test
        if status.get('total_chunks', 0) > 0:
            search = kb_tool.execute(action="semantic_search", query="test", max_results=1)
            print(f"Search: {search.get('status', 'unknown')}")
            if search.get('status') == 'success':
                print(f"Results: {len(search.get('results', []))}")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == '__main__':
    quick_test()

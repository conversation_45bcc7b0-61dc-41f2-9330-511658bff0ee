import os
import json
import pickle
import hashlib
import time
import numpy as np
from datetime import datetime
from typing import Dict, Any, List, Optional
from .base_tool import BaseTool

class KnowledgeBaseTool(BaseTool):
    """Local knowledge base management for research data storage and retrieval"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        if 'research_apis' in config and 'knowledge_base' in config['research_apis']:
            self.kb_path = config['research_apis']['knowledge_base']['path']
            self.checkpoint_path = config['research_apis']['knowledge_base']['checkpoint_path']
        else:
            self.kb_path = 'research_knowledge_base'
            self.checkpoint_path = 'research_checkpoints'
        
        # Ensure directories exist
        os.makedirs(self.kb_path, exist_ok=True)
        os.makedirs(self.checkpoint_path, exist_ok=True)
        
        # Initialize index file
        self.index_file = os.path.join(self.kb_path, 'index.json')
        self.embeddings_file = os.path.join(self.kb_path, 'embeddings.pkl')
        self._initialize_index()

        # Simple embedding cache for semantic search
        self.embeddings_cache = self._load_embeddings()
    
    @property
    def name(self) -> str:
        return "knowledge_base"
    
    @property
    def description(self) -> str:
        return "Store, retrieve, and manage research knowledge base including papers, findings, and research data"
    
    @property
    def parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": ["store", "retrieve", "search", "semantic_search", "list", "delete", "checkpoint", "upload_document"],
                    "description": "Action to perform on knowledge base"
                },
                "data_type": {
                    "type": "string",
                    "enum": ["paper", "finding", "idea", "methodology", "dataset", "code", "note"],
                    "description": "Type of data to store/retrieve",
                    "default": "note"
                },
                "key": {
                    "type": "string",
                    "description": "Unique identifier for the data item"
                },
                "content": {
                    "type": "string",
                    "description": "Content to store (for store action)"
                },
                "metadata": {
                    "type": "object",
                    "description": "Additional metadata (authors, year, venue, etc.)"
                },
                "query": {
                    "type": "string",
                    "description": "Search query (for search action)"
                },
                "tags": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Tags for categorization"
                }
            },
            "required": ["action"]
        }
    
    def execute(self, action: str, data_type: str = "note", key: str = "", content: str = "",
                metadata: Dict = None, query: str = "", tags: List[str] = None, max_results: int = 10) -> Dict[str, Any]:
        """Execute knowledge base operation"""
        try:
            if action == "store":
                return self._store_data(data_type, key, content, metadata or {}, tags or [])
            elif action == "retrieve":
                return self._retrieve_data(key)
            elif action == "search":
                return self._search_data(query, data_type)
            elif action == "semantic_search":
                return self._semantic_search(query, max_results)
            elif action == "list":
                return self._list_data(data_type)
            elif action == "delete":
                return self._delete_data(key)
            elif action == "checkpoint":
                return self._create_checkpoint()
            elif action == "upload_document":
                return self._upload_document(content, data_type)
            else:
                return {
                    "error": f"Unknown action: {action}",
                    "status": "error"
                }
                
        except Exception as e:
            return {
                "error": f"Knowledge base operation failed: {str(e)}",
                "status": "error",
                "action": action
            }
    
    def _initialize_index(self):
        """Initialize or load the knowledge base index"""
        if not os.path.exists(self.index_file):
            index = {
                "created": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "items": {},
                "tags": {},
                "data_types": {}
            }
            with open(self.index_file, 'w') as f:
                json.dump(index, f, indent=2)
    
    def _load_index(self) -> Dict:
        """Load the knowledge base index"""
        with open(self.index_file, 'r') as f:
            return json.load(f)
    
    def _save_index(self, index: Dict):
        """Save the knowledge base index"""
        index["last_updated"] = datetime.now().isoformat()
        with open(self.index_file, 'w') as f:
            json.dump(index, f, indent=2)
    
    def _store_data(self, data_type: str, key: str, content: str, metadata: Dict, tags: List[str]) -> Dict[str, Any]:
        """Store data in knowledge base"""
        if not key:
            # Generate key from content hash if not provided
            key = hashlib.md5(content.encode()).hexdigest()[:16]
        
        # Load index
        index = self._load_index()
        
        # Create data item
        item = {
            "key": key,
            "data_type": data_type,
            "content": content,
            "metadata": metadata,
            "tags": tags,
            "created": datetime.now().isoformat(),
            "updated": datetime.now().isoformat()
        }
        
        # Store content in separate file
        content_file = os.path.join(self.kb_path, f"{key}.json")
        with open(content_file, 'w') as f:
            json.dump(item, f, indent=2)
        
        # Update index
        index["items"][key] = {
            "data_type": data_type,
            "tags": tags,
            "metadata": metadata,
            "file": content_file,
            "created": item["created"],
            "updated": item["updated"]
        }
        
        # Update tag index
        for tag in tags:
            if tag not in index["tags"]:
                index["tags"][tag] = []
            if key not in index["tags"][tag]:
                index["tags"][tag].append(key)
        
        # Update data type index
        if data_type not in index["data_types"]:
            index["data_types"][data_type] = []
        if key not in index["data_types"][data_type]:
            index["data_types"][data_type].append(key)
        
        # Save index
        self._save_index(index)
        
        return {
            "status": "success",
            "action": "store",
            "key": key,
            "data_type": data_type,
            "message": f"Stored {data_type} with key '{key}'"
        }
    
    def _retrieve_data(self, key: str) -> Dict[str, Any]:
        """Retrieve data from knowledge base"""
        if not key:
            return {
                "error": "Key is required for retrieval",
                "status": "error"
            }
        
        index = self._load_index()
        
        if key not in index["items"]:
            return {
                "error": f"Key '{key}' not found in knowledge base",
                "status": "error"
            }
        
        # Load content from file
        content_file = os.path.join(self.kb_path, f"{key}.json")
        if not os.path.exists(content_file):
            return {
                "error": f"Content file for key '{key}' not found",
                "status": "error"
            }
        
        with open(content_file, 'r') as f:
            item = json.load(f)
        
        return {
            "status": "success",
            "action": "retrieve",
            "key": key,
            "data": item
        }
    
    def _search_data(self, query: str, data_type: str = "") -> Dict[str, Any]:
        """Search data in knowledge base"""
        if not query:
            return {
                "error": "Query is required for search",
                "status": "error"
            }
        
        index = self._load_index()
        results = []
        
        # Search through all items
        for key, item_info in index["items"].items():
            # Filter by data type if specified
            if data_type and item_info["data_type"] != data_type:
                continue
            
            # Load full item for content search
            content_file = os.path.join(self.kb_path, f"{key}.json")
            if os.path.exists(content_file):
                with open(content_file, 'r') as f:
                    item = json.load(f)
                
                # Simple text search in content, tags, and metadata
                search_text = (
                    item.get("content", "").lower() + " " +
                    " ".join(item.get("tags", [])).lower() + " " +
                    str(item.get("metadata", {})).lower()
                )
                
                if query.lower() in search_text:
                    results.append({
                        "key": key,
                        "data_type": item["data_type"],
                        "tags": item["tags"],
                        "metadata": item["metadata"],
                        "created": item["created"],
                        "content_preview": item["content"][:200] + "..." if len(item["content"]) > 200 else item["content"]
                    })
        
        return {
            "status": "success",
            "action": "search",
            "query": query,
            "data_type": data_type,
            "results": results,
            "count": len(results)
        }
    
    def _list_data(self, data_type: str = "") -> Dict[str, Any]:
        """List data in knowledge base"""
        index = self._load_index()
        
        if data_type:
            # List specific data type
            items = index["data_types"].get(data_type, [])
            item_list = []
            for key in items:
                if key in index["items"]:
                    item_info = index["items"][key]
                    item_list.append({
                        "key": key,
                        "data_type": item_info["data_type"],
                        "tags": item_info["tags"],
                        "created": item_info["created"]
                    })
        else:
            # List all items
            item_list = []
            for key, item_info in index["items"].items():
                item_list.append({
                    "key": key,
                    "data_type": item_info["data_type"],
                    "tags": item_info["tags"],
                    "created": item_info["created"]
                })
        
        return {
            "status": "success",
            "action": "list",
            "data_type": data_type,
            "items": item_list,
            "count": len(item_list),
            "summary": {
                "total_items": len(index["items"]),
                "data_types": list(index["data_types"].keys()),
                "tags": list(index["tags"].keys())
            }
        }
    
    def _delete_data(self, key: str) -> Dict[str, Any]:
        """Delete data from knowledge base"""
        if not key:
            return {
                "error": "Key is required for deletion",
                "status": "error"
            }
        
        index = self._load_index()
        
        if key not in index["items"]:
            return {
                "error": f"Key '{key}' not found in knowledge base",
                "status": "error"
            }
        
        # Remove from index
        item_info = index["items"][key]
        del index["items"][key]
        
        # Remove from tag index
        for tag in item_info["tags"]:
            if tag in index["tags"] and key in index["tags"][tag]:
                index["tags"][tag].remove(key)
                if not index["tags"][tag]:  # Remove empty tag
                    del index["tags"][tag]
        
        # Remove from data type index
        data_type = item_info["data_type"]
        if data_type in index["data_types"] and key in index["data_types"][data_type]:
            index["data_types"][data_type].remove(key)
            if not index["data_types"][data_type]:  # Remove empty data type
                del index["data_types"][data_type]
        
        # Delete content file
        content_file = os.path.join(self.kb_path, f"{key}.json")
        if os.path.exists(content_file):
            os.remove(content_file)
        
        # Save index
        self._save_index(index)
        
        return {
            "status": "success",
            "action": "delete",
            "key": key,
            "message": f"Deleted item with key '{key}'"
        }
    
    def _create_checkpoint(self) -> Dict[str, Any]:
        """Create a checkpoint of the current knowledge base"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        checkpoint_name = f"checkpoint_{timestamp}"
        checkpoint_dir = os.path.join(self.checkpoint_path, checkpoint_name)
        
        # Create checkpoint directory
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Copy index and all data files
        import shutil
        
        # Copy index
        shutil.copy2(self.index_file, os.path.join(checkpoint_dir, 'index.json'))
        
        # Copy all data files
        index = self._load_index()
        for key in index["items"]:
            content_file = os.path.join(self.kb_path, f"{key}.json")
            if os.path.exists(content_file):
                shutil.copy2(content_file, checkpoint_dir)
        
        return {
            "status": "success",
            "action": "checkpoint",
            "checkpoint_name": checkpoint_name,
            "checkpoint_path": checkpoint_dir,
            "items_backed_up": len(index["items"]),
            "message": f"Created checkpoint '{checkpoint_name}' with {len(index['items'])} items"
        }

    def _load_embeddings(self) -> Dict[str, np.ndarray]:
        """Load embeddings cache"""
        try:
            if os.path.exists(self.embeddings_file):
                with open(self.embeddings_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            print(f"Warning: Could not load embeddings cache: {e}")
        return {}

    def _save_embeddings(self):
        """Save embeddings cache"""
        try:
            with open(self.embeddings_file, 'wb') as f:
                pickle.dump(self.embeddings_cache, f)
        except Exception as e:
            print(f"Warning: Could not save embeddings cache: {e}")

    def _simple_text_embedding(self, text: str) -> np.ndarray:
        """Simple text embedding using TF-IDF-like approach"""
        words = text.lower().split()
        vocab = set(words)
        vector = np.zeros(min(len(vocab), 100))

        for i, word in enumerate(list(vocab)[:100]):
            vector[i] = words.count(word) / len(words)

        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm

        return vector

    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            # Check for empty or mismatched vectors
            if vec1.size == 0 or vec2.size == 0:
                return 0.0

            # Ensure vectors have the same shape
            if vec1.shape != vec2.shape:
                print(f"Warning: Vector shape mismatch: {vec1.shape} vs {vec2.shape}")
                return 0.0

            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            return dot_product / (norm1 * norm2)
        except Exception as e:
            print(f"Cosine similarity error: {e}")
            return 0.0

    def _semantic_search(self, query: str, max_results: int = 10) -> Dict[str, Any]:
        """Perform semantic search on knowledge base"""
        try:
            results = self._semantic_search_entries(query, max_results)

            formatted_results = []
            for result in results:
                entry = result['entry']
                formatted_results.append({
                    "id": result['entry_id'],
                    "title": entry.get('title', 'Untitled'),
                    "content": entry.get('content', '')[:500] + "..." if len(entry.get('content', '')) > 500 else entry.get('content', ''),
                    "data_type": entry.get('data_type', 'unknown'),
                    "similarity_score": round(result['similarity'], 4),
                    "created": entry.get('created', ''),
                    "tags": entry.get('tags', [])
                })

            return {
                "status": "success",
                "action": "semantic_search",
                "query": query,
                "results": formatted_results,
                "total_results": len(formatted_results),
                "message": f"Found {len(formatted_results)} semantically similar entries"
            }

        except Exception as e:
            return {
                "status": "error",
                "action": "semantic_search",
                "error": str(e),
                "message": "Semantic search failed"
            }

    def _semantic_search_entries(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Perform semantic search on knowledge base entries"""
        try:
            index = self._load_index()

            if not index or not index.get("items"):
                return []

            query_embedding = self._simple_text_embedding(query)

            # Check if query embedding is valid
            if query_embedding.size == 0:
                print("Warning: Empty query embedding generated")
                return []

            similarities = []

            for entry_id, entry in index["items"].items():
                if entry_id not in self.embeddings_cache:
                    content = f"{entry.get('title', '')} {entry.get('content', '')} {entry.get('summary', '')}"
                    if not content.strip():
                        continue  # Skip empty content
                    self.embeddings_cache[entry_id] = self._simple_text_embedding(content)

                entry_embedding = self.embeddings_cache[entry_id]

                # Skip if entry embedding is invalid
                if entry_embedding.size == 0:
                    continue

                similarity = self._cosine_similarity(query_embedding, entry_embedding)

                similarities.append({
                    'entry_id': entry_id,
                    'entry': entry,
                    'similarity': similarity
                })

            similarities.sort(key=lambda x: x['similarity'], reverse=True)
            self._save_embeddings()

            return similarities[:limit]

        except Exception as e:
            print(f"Semantic search error: {e}")
            return []

    def _upload_document(self, file_path: str, data_type: str = "document") -> Dict[str, Any]:
        """Upload and process a document into the knowledge base"""
        try:
            if not os.path.exists(file_path):
                return {
                    "status": "error",
                    "message": f"File not found: {file_path}"
                }

            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            elif file_ext == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    content = json.dumps(data, indent=2)
            else:
                with open(file_path, 'rb') as f:
                    content = f"Binary file: {file_path} ({len(f.read())} bytes)"

            filename = os.path.basename(file_path)
            entry_data = {
                "title": filename,
                "content": content,
                "file_path": file_path,
                "file_type": file_ext,
                "summary": f"Uploaded document: {filename}"
            }

            result = self._store_data(data_type, filename, content, entry_data, [])

            return {
                "status": "success",
                "message": f"Document uploaded successfully: {filename}",
                "entry_id": result.get("key"),
                "content_length": len(content)
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to upload document: {str(e)}"
            }

import os
import json
import pickle
import hashlib
import time
import numpy as np
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from .base_tool import BaseTool

# Enhanced RAG dependencies with fallbacks
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

logger = logging.getLogger(__name__)

class KnowledgeBaseTool(BaseTool):
    """Enhanced RAG-based knowledge base with neural embeddings and semantic search"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # Knowledge base path
        if 'research_apis' in config and 'knowledge_base' in config['research_apis']:
            self.kb_path = config['research_apis']['knowledge_base']['path']
            self.checkpoint_path = config['research_apis']['knowledge_base']['checkpoint_path']
        else:
            self.kb_path = r'D:\Downloads\make-it-heavy-main_\research_knowledge_base'
            self.checkpoint_path = r'D:\Downloads\make-it-heavy-main_\research_checkpoints'

        # Ensure directories exist
        os.makedirs(self.kb_path, exist_ok=True)
        os.makedirs(self.checkpoint_path, exist_ok=True)

        # Enhanced RAG files
        self.index_file = os.path.join(self.kb_path, 'enhanced_index.json')
        self.embeddings_file = os.path.join(self.kb_path, 'enhanced_embeddings.pkl')
        self.faiss_index_file = os.path.join(self.kb_path, 'faiss_index.bin')
        self.metadata_file = os.path.join(self.kb_path, 'metadata.json')

        # Initialize enhanced embedding model
        self.embedding_model = self._initialize_embedding_model()
        self.embedding_dim = self._get_embedding_dimension()

        # Initialize vector store
        self.vector_store = self._initialize_vector_store()

        # Load existing data
        self._initialize_index()
        self.metadata = self._load_metadata()

        # Document processing parameters
        self.chunk_size = 512  # Optimal chunk size for embeddings
        self.chunk_overlap = 50  # Overlap between chunks

        # Legacy compatibility
        self.embeddings_cache = self._load_legacy_embeddings()

    def _load_metadata(self) -> List[Dict[str, Any]]:
        """Load metadata from file"""
        try:
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Could not load metadata: {e}")
        return []

    def _load_legacy_embeddings(self) -> Dict[str, np.ndarray]:
        """Load legacy embeddings cache for backward compatibility"""
        try:
            legacy_file = os.path.join(self.kb_path, 'embeddings.pkl')
            if os.path.exists(legacy_file):
                with open(legacy_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.warning(f"Could not load legacy embeddings: {e}")
        return {}
    
    def _initialize_embedding_model(self):
        """Initialize the best available embedding model"""
        # Priority: SentenceTransformers > Gemini > OpenAI > Simple TF-IDF

        # Try SentenceTransformers first (free, high quality, local)
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                model = SentenceTransformer('all-MiniLM-L6-v2')  # Fast and good quality
                logger.info("Using SentenceTransformers (all-MiniLM-L6-v2) - Free, high quality")
                return model
            except Exception as e:
                logger.warning(f"SentenceTransformers failed: {e}")

        # Try Gemini embeddings (free for user)
        if GEMINI_AVAILABLE:
            try:
                gemini_config = self.config.get('unified_models', {}).get('providers', {}).get('gemini', {})
                if gemini_config.get('api_key'):
                    genai.configure(api_key=gemini_config['api_key'])
                    logger.info("Using Gemini embeddings (free)")
                    return "gemini"
            except Exception as e:
                logger.warning(f"Gemini embeddings failed: {e}")

        # Try OpenAI embeddings (paid)
        if OPENAI_AVAILABLE and self.config.get('unified_models', {}).get('providers', {}).get('openai', {}).get('api_key'):
            try:
                openai.api_key = self.config['unified_models']['providers']['openai']['api_key']
                logger.info("Using OpenAI embeddings (text-embedding-3-small) - Paid API")
                return "openai"
            except Exception as e:
                logger.warning(f"OpenAI embeddings failed: {e}")

        # Fallback to enhanced TF-IDF
        logger.info("Using enhanced TF-IDF embeddings (fallback)")
        return "enhanced_tfidf"

    def _get_embedding_dimension(self):
        """Get embedding dimension based on model"""
        if isinstance(self.embedding_model, SentenceTransformer):
            return self.embedding_model.get_sentence_embedding_dimension()
        elif self.embedding_model == "gemini":
            return 768  # Gemini embedding dimension
        elif self.embedding_model == "openai":
            return 1536  # text-embedding-3-small dimension
        else:
            return 384  # Enhanced TF-IDF dimension

    def _initialize_vector_store(self):
        """Initialize FAISS vector store if available"""
        if FAISS_AVAILABLE:
            try:
                if os.path.exists(self.faiss_index_file):
                    # Load existing index
                    index = faiss.read_index(self.faiss_index_file)

                    # Check dimension compatibility
                    if index.d != self.embedding_dim:
                        logger.warning(f"FAISS index dimension ({index.d}) != embedding dimension ({self.embedding_dim})")
                        logger.info("Creating new FAISS index with correct dimension")
                        index = faiss.IndexFlatIP(self.embedding_dim)
                    else:
                        logger.info(f"Loaded existing FAISS index with {index.ntotal} vectors")
                else:
                    # Create new index
                    index = faiss.IndexFlatIP(self.embedding_dim)  # Inner product for cosine similarity
                    logger.info(f"Created new FAISS index with dimension {self.embedding_dim}")
                return index
            except Exception as e:
                logger.warning(f"FAISS initialization failed: {e}")

        logger.info("Using numpy-based vector store")
        return None

    @property
    def name(self) -> str:
        return "knowledge_base"

    @property
    def description(self) -> str:
        return "Enhanced RAG-based knowledge base with neural embeddings, semantic search, and document chunking"
    
    @property
    def parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": ["store", "retrieve", "search", "semantic_search", "rag_query", "process_files", "status", "list", "delete", "checkpoint", "upload_document"],
                    "description": "Action to perform on knowledge base"
                },
                "data_type": {
                    "type": "string",
                    "enum": ["paper", "finding", "idea", "methodology", "dataset", "code", "note"],
                    "description": "Type of data to store/retrieve",
                    "default": "note"
                },
                "key": {
                    "type": "string",
                    "description": "Unique identifier for the data item"
                },
                "content": {
                    "type": "string",
                    "description": "Content to store (for store action)"
                },
                "metadata": {
                    "type": "object",
                    "description": "Additional metadata (authors, year, venue, etc.)"
                },
                "query": {
                    "type": "string",
                    "description": "Search query (for search action)"
                },
                "tags": {
                    "type": "array",
                    "items": {"type": "string"},
                    "description": "Tags for categorization"
                }
            },
            "required": ["action"]
        }
    
    def execute(self, action: str, data_type: str = "note", key: str = "", content: str = "",
                metadata: Dict = None, query: str = "", tags: List[str] = None, max_results: int = 10) -> Dict[str, Any]:
        """Execute knowledge base operation"""
        try:
            if action == "store":
                return self._store_data(data_type, key, content, metadata or {}, tags or [])
            elif action == "retrieve":
                return self._retrieve_data(key)
            elif action == "search":
                return self._search_data(query, data_type)
            elif action == "semantic_search":
                return self._enhanced_semantic_search(query, max_results)
            elif action == "rag_query":
                return self._rag_query(query, max_results or 3)
            elif action == "process_files":
                return self._process_files_action()
            elif action == "status":
                return self._get_status()
            elif action == "list":
                return self._list_data(data_type)
            elif action == "delete":
                return self._delete_data(key)
            elif action == "checkpoint":
                return self._create_checkpoint()
            elif action == "upload_document":
                return self._upload_document(content, data_type)
            else:
                return {
                    "error": f"Unknown action: {action}",
                    "status": "error",
                    "available_actions": ["store", "retrieve", "search", "semantic_search", "rag_query", "process_files", "status", "list", "delete", "checkpoint", "upload_document"]
                }
                
        except Exception as e:
            return {
                "error": f"Knowledge base operation failed: {str(e)}",
                "status": "error",
                "action": action
            }
    
    def _initialize_index(self):
        """Initialize or load the knowledge base index"""
        if not os.path.exists(self.index_file):
            index = {
                "created": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "items": {},
                "tags": {},
                "data_types": {}
            }
            with open(self.index_file, 'w') as f:
                json.dump(index, f, indent=2)
    
    def _load_index(self) -> Dict:
        """Load the knowledge base index"""
        with open(self.index_file, 'r') as f:
            return json.load(f)
    
    def _save_index(self, index: Dict):
        """Save the knowledge base index"""
        index["last_updated"] = datetime.now().isoformat()
        with open(self.index_file, 'w') as f:
            json.dump(index, f, indent=2)
    
    def _store_data(self, data_type: str, key: str, content: str, metadata: Dict, tags: List[str]) -> Dict[str, Any]:
        """Store data in knowledge base"""
        if not key:
            # Generate key from content hash if not provided
            key = hashlib.md5(content.encode()).hexdigest()[:16]
        
        # Load index
        index = self._load_index()
        
        # Create data item
        item = {
            "key": key,
            "data_type": data_type,
            "content": content,
            "metadata": metadata,
            "tags": tags,
            "created": datetime.now().isoformat(),
            "updated": datetime.now().isoformat()
        }
        
        # Store content in separate file
        content_file = os.path.join(self.kb_path, f"{key}.json")
        with open(content_file, 'w') as f:
            json.dump(item, f, indent=2)
        
        # Update index
        index["items"][key] = {
            "data_type": data_type,
            "tags": tags,
            "metadata": metadata,
            "file": content_file,
            "created": item["created"],
            "updated": item["updated"]
        }
        
        # Update tag index
        for tag in tags:
            if tag not in index["tags"]:
                index["tags"][tag] = []
            if key not in index["tags"][tag]:
                index["tags"][tag].append(key)
        
        # Update data type index
        if data_type not in index["data_types"]:
            index["data_types"][data_type] = []
        if key not in index["data_types"][data_type]:
            index["data_types"][data_type].append(key)
        
        # Save index
        self._save_index(index)
        
        return {
            "status": "success",
            "action": "store",
            "key": key,
            "data_type": data_type,
            "message": f"Stored {data_type} with key '{key}'"
        }
    
    def _retrieve_data(self, key: str) -> Dict[str, Any]:
        """Retrieve data from knowledge base"""
        if not key:
            return {
                "error": "Key is required for retrieval",
                "status": "error"
            }
        
        index = self._load_index()
        
        if key not in index["items"]:
            return {
                "error": f"Key '{key}' not found in knowledge base",
                "status": "error"
            }
        
        # Load content from file
        content_file = os.path.join(self.kb_path, f"{key}.json")
        if not os.path.exists(content_file):
            return {
                "error": f"Content file for key '{key}' not found",
                "status": "error"
            }
        
        with open(content_file, 'r') as f:
            item = json.load(f)
        
        return {
            "status": "success",
            "action": "retrieve",
            "key": key,
            "data": item
        }
    
    def _search_data(self, query: str, data_type: str = "") -> Dict[str, Any]:
        """Search data in knowledge base"""
        if not query:
            return {
                "error": "Query is required for search",
                "status": "error"
            }
        
        index = self._load_index()
        results = []
        
        # Search through all items
        for key, item_info in index["items"].items():
            # Filter by data type if specified
            if data_type and item_info["data_type"] != data_type:
                continue
            
            # Load full item for content search
            content_file = os.path.join(self.kb_path, f"{key}.json")
            if os.path.exists(content_file):
                with open(content_file, 'r') as f:
                    item = json.load(f)
                
                # Simple text search in content, tags, and metadata
                search_text = (
                    item.get("content", "").lower() + " " +
                    " ".join(item.get("tags", [])).lower() + " " +
                    str(item.get("metadata", {})).lower()
                )
                
                if query.lower() in search_text:
                    results.append({
                        "key": key,
                        "data_type": item["data_type"],
                        "tags": item["tags"],
                        "metadata": item["metadata"],
                        "created": item["created"],
                        "content_preview": item["content"][:200] + "..." if len(item["content"]) > 200 else item["content"]
                    })
        
        return {
            "status": "success",
            "action": "search",
            "query": query,
            "data_type": data_type,
            "results": results,
            "count": len(results)
        }
    
    def _list_data(self, data_type: str = "") -> Dict[str, Any]:
        """List data in knowledge base"""
        index = self._load_index()
        
        if data_type:
            # List specific data type
            items = index["data_types"].get(data_type, [])
            item_list = []
            for key in items:
                if key in index["items"]:
                    item_info = index["items"][key]
                    item_list.append({
                        "key": key,
                        "data_type": item_info["data_type"],
                        "tags": item_info["tags"],
                        "created": item_info["created"]
                    })
        else:
            # List all items
            item_list = []
            for key, item_info in index["items"].items():
                item_list.append({
                    "key": key,
                    "data_type": item_info["data_type"],
                    "tags": item_info["tags"],
                    "created": item_info["created"]
                })
        
        return {
            "status": "success",
            "action": "list",
            "data_type": data_type,
            "items": item_list,
            "count": len(item_list),
            "summary": {
                "total_items": len(index["items"]),
                "data_types": list(index["data_types"].keys()),
                "tags": list(index["tags"].keys())
            }
        }
    
    def _delete_data(self, key: str) -> Dict[str, Any]:
        """Delete data from knowledge base"""
        if not key:
            return {
                "error": "Key is required for deletion",
                "status": "error"
            }
        
        index = self._load_index()
        
        if key not in index["items"]:
            return {
                "error": f"Key '{key}' not found in knowledge base",
                "status": "error"
            }
        
        # Remove from index
        item_info = index["items"][key]
        del index["items"][key]
        
        # Remove from tag index
        for tag in item_info["tags"]:
            if tag in index["tags"] and key in index["tags"][tag]:
                index["tags"][tag].remove(key)
                if not index["tags"][tag]:  # Remove empty tag
                    del index["tags"][tag]
        
        # Remove from data type index
        data_type = item_info["data_type"]
        if data_type in index["data_types"] and key in index["data_types"][data_type]:
            index["data_types"][data_type].remove(key)
            if not index["data_types"][data_type]:  # Remove empty data type
                del index["data_types"][data_type]
        
        # Delete content file
        content_file = os.path.join(self.kb_path, f"{key}.json")
        if os.path.exists(content_file):
            os.remove(content_file)
        
        # Save index
        self._save_index(index)
        
        return {
            "status": "success",
            "action": "delete",
            "key": key,
            "message": f"Deleted item with key '{key}'"
        }
    
    def _create_checkpoint(self) -> Dict[str, Any]:
        """Create a checkpoint of the current knowledge base"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        checkpoint_name = f"checkpoint_{timestamp}"
        checkpoint_dir = os.path.join(self.checkpoint_path, checkpoint_name)
        
        # Create checkpoint directory
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Copy index and all data files
        import shutil
        
        # Copy index
        shutil.copy2(self.index_file, os.path.join(checkpoint_dir, 'index.json'))
        
        # Copy all data files
        index = self._load_index()
        for key in index["items"]:
            content_file = os.path.join(self.kb_path, f"{key}.json")
            if os.path.exists(content_file):
                shutil.copy2(content_file, checkpoint_dir)
        
        return {
            "status": "success",
            "action": "checkpoint",
            "checkpoint_name": checkpoint_name,
            "checkpoint_path": checkpoint_dir,
            "items_backed_up": len(index["items"]),
            "message": f"Created checkpoint '{checkpoint_name}' with {len(index['items'])} items"
        }

    def _load_embeddings(self) -> Dict[str, np.ndarray]:
        """Load embeddings cache"""
        try:
            if os.path.exists(self.embeddings_file):
                with open(self.embeddings_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            print(f"Warning: Could not load embeddings cache: {e}")
        return {}

    def _save_embeddings(self):
        """Save embeddings cache"""
        try:
            with open(self.embeddings_file, 'wb') as f:
                pickle.dump(self.embeddings_cache, f)
        except Exception as e:
            print(f"Warning: Could not save embeddings cache: {e}")

    def _simple_text_embedding(self, text: str) -> np.ndarray:
        """Simple text embedding using TF-IDF-like approach"""
        words = text.lower().split()
        vocab = set(words)
        vector = np.zeros(min(len(vocab), 100))

        for i, word in enumerate(list(vocab)[:100]):
            vector[i] = words.count(word) / len(words)

        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm

        return vector

    def _cosine_similarity(self, vec1: np.ndarray, vec2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            # Check for empty or mismatched vectors
            if vec1.size == 0 or vec2.size == 0:
                return 0.0

            # Ensure vectors have the same shape
            if vec1.shape != vec2.shape:
                print(f"Warning: Vector shape mismatch: {vec1.shape} vs {vec2.shape}")
                return 0.0

            dot_product = np.dot(vec1, vec2)
            norm1 = np.linalg.norm(vec1)
            norm2 = np.linalg.norm(vec2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            return dot_product / (norm1 * norm2)
        except Exception as e:
            print(f"Cosine similarity error: {e}")
            return 0.0

    def _semantic_search(self, query: str, max_results: int = 10) -> Dict[str, Any]:
        """Perform semantic search on knowledge base"""
        try:
            results = self._semantic_search_entries(query, max_results)

            formatted_results = []
            for result in results:
                entry = result['entry']
                formatted_results.append({
                    "id": result['entry_id'],
                    "title": entry.get('title', 'Untitled'),
                    "content": entry.get('content', '')[:500] + "..." if len(entry.get('content', '')) > 500 else entry.get('content', ''),
                    "data_type": entry.get('data_type', 'unknown'),
                    "similarity_score": round(result['similarity'], 4),
                    "created": entry.get('created', ''),
                    "tags": entry.get('tags', [])
                })

            return {
                "status": "success",
                "action": "semantic_search",
                "query": query,
                "results": formatted_results,
                "total_results": len(formatted_results),
                "message": f"Found {len(formatted_results)} semantically similar entries"
            }

        except Exception as e:
            return {
                "status": "error",
                "action": "semantic_search",
                "error": str(e),
                "message": "Semantic search failed"
            }

    def _semantic_search_entries(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Perform semantic search on knowledge base entries"""
        try:
            index = self._load_index()

            if not index or not index.get("items"):
                return []

            query_embedding = self._simple_text_embedding(query)

            # Check if query embedding is valid
            if query_embedding.size == 0:
                print("Warning: Empty query embedding generated")
                return []

            similarities = []

            for entry_id, entry in index["items"].items():
                if entry_id not in self.embeddings_cache:
                    content = f"{entry.get('title', '')} {entry.get('content', '')} {entry.get('summary', '')}"
                    if not content.strip():
                        continue  # Skip empty content
                    self.embeddings_cache[entry_id] = self._simple_text_embedding(content)

                entry_embedding = self.embeddings_cache[entry_id]

                # Skip if entry embedding is invalid
                if entry_embedding.size == 0:
                    continue

                similarity = self._cosine_similarity(query_embedding, entry_embedding)

                similarities.append({
                    'entry_id': entry_id,
                    'entry': entry,
                    'similarity': similarity
                })

            similarities.sort(key=lambda x: x['similarity'], reverse=True)
            self._save_embeddings()

            return similarities[:limit]

        except Exception as e:
            print(f"Semantic search error: {e}")
            return []

    def _upload_document(self, file_path: str, data_type: str = "document") -> Dict[str, Any]:
        """Upload and process a document into the knowledge base"""
        try:
            if not os.path.exists(file_path):
                return {
                    "status": "error",
                    "message": f"File not found: {file_path}"
                }

            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext == '.txt':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            elif file_ext == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    content = json.dumps(data, indent=2)
            else:
                with open(file_path, 'rb') as f:
                    content = f"Binary file: {file_path} ({len(f.read())} bytes)"

            filename = os.path.basename(file_path)
            entry_data = {
                "title": filename,
                "content": content,
                "file_path": file_path,
                "file_type": file_ext,
                "summary": f"Uploaded document: {filename}"
            }

            result = self._store_data(data_type, filename, content, entry_data, [])

            return {
                "status": "success",
                "message": f"Document uploaded successfully: {filename}",
                "entry_id": result.get("key"),
                "content_length": len(content)
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to upload document: {str(e)}"
            }

    # Enhanced RAG Methods
    def _generate_embedding(self, text: str) -> np.ndarray:
        """Generate embedding for text using the best available model"""
        try:
            if isinstance(self.embedding_model, SentenceTransformer):
                embedding = self.embedding_model.encode(text)
                return np.array(embedding, dtype=np.float32)

            elif self.embedding_model == "gemini":
                # Use enhanced TF-IDF for now (Gemini doesn't have direct embedding API)
                return self._enhanced_text_embedding(text)

            elif self.embedding_model == "openai":
                response = openai.embeddings.create(
                    model="text-embedding-3-small",
                    input=text
                )
                return np.array(response.data[0].embedding, dtype=np.float32)

            else:
                # Enhanced TF-IDF fallback
                return self._enhanced_text_embedding(text)

        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            return self._enhanced_text_embedding(text)

    def _enhanced_text_embedding(self, text: str) -> np.ndarray:
        """Enhanced TF-IDF-like embedding with better features"""
        words = text.lower().split()
        vocab = set(words)
        vector = np.zeros(self.embedding_dim, dtype=np.float32)

        # TF-IDF with position weighting
        for i, word in enumerate(list(vocab)[:self.embedding_dim]):
            if i < self.embedding_dim:
                tf = words.count(word) / len(words)
                # Add position weighting (words at beginning are more important)
                position_weight = 1.0 + (1.0 / (words.index(word) + 1)) if word in words else 1.0
                vector[i] = tf * position_weight

        # Normalize
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm

        return vector

    def _chunk_text(self, text: str, filename: str = "") -> List[Dict[str, Any]]:
        """Split text into overlapping chunks for better retrieval"""
        words = text.split()
        chunks = []

        if len(words) <= self.chunk_size:
            # If text is small, use as single chunk
            chunks.append({
                'text': text,
                'filename': filename,
                'chunk_id': 0,
                'start_word': 0,
                'end_word': len(words)
            })
        else:
            # Split into overlapping chunks
            for i in range(0, len(words), self.chunk_size - self.chunk_overlap):
                chunk_words = words[i:i + self.chunk_size]
                chunk_text = ' '.join(chunk_words)

                chunks.append({
                    'text': chunk_text,
                    'filename': filename,
                    'chunk_id': i // (self.chunk_size - self.chunk_overlap),
                    'start_word': i,
                    'end_word': min(i + self.chunk_size, len(words))
                })

        return chunks

    def _enhanced_semantic_search(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """Enhanced semantic search using neural embeddings"""
        if not query.strip():
            return {
                "status": "error",
                "error": "Query cannot be empty"
            }

        try:
            # Check if we have processed files
            metadata = self._load_metadata()
            if not metadata:
                # Fall back to legacy search
                return self._semantic_search(query, max_results)

            # Generate query embedding
            query_embedding = self._generate_embedding(query)

            # Search using FAISS if available
            if self.vector_store is not None and FAISS_AVAILABLE:
                return self._faiss_search(query_embedding, query, max_results, 0.5)
            else:
                return self._numpy_search(query_embedding, query, max_results, 0.5)

        except Exception as e:
            logger.error(f"Enhanced semantic search failed: {e}")
            # Fall back to legacy search
            return self._semantic_search(query, max_results)

    def _faiss_search(self, query_embedding: np.ndarray, query: str, max_results: int, similarity_threshold: float) -> Dict[str, Any]:
        """Search using FAISS vector store"""
        try:
            # Normalize query embedding for cosine similarity
            query_embedding = query_embedding.reshape(1, -1).astype(np.float32)
            faiss.normalize_L2(query_embedding)

            # Search
            scores, indices = self.vector_store.search(query_embedding, max_results * 2)  # Get more to filter

            # Load metadata
            metadata = self._load_metadata()

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0 and idx < len(metadata) and score >= similarity_threshold:
                    meta = metadata[idx]
                    results.append({
                        "filename": meta['filename'],
                        "content": meta['text'][:500] + "..." if len(meta['text']) > 500 else meta['text'],
                        "chunk_id": meta['chunk_id'],
                        "similarity_score": float(score),
                        "start_word": meta['start_word'],
                        "end_word": meta['end_word']
                    })

            return {
                "status": "success",
                "action": "semantic_search",
                "query": query,
                "results": results[:max_results],
                "total_results": len(results),
                "search_method": "FAISS",
                "message": f"Found {len(results)} relevant chunks"
            }

        except Exception as e:
            logger.error(f"FAISS search failed: {e}")
            return self._numpy_search(query_embedding, query, max_results, similarity_threshold)

    def _numpy_search(self, query_embedding: np.ndarray, query: str, max_results: int, similarity_threshold: float) -> Dict[str, Any]:
        """Fallback search using numpy"""
        try:
            # Load embeddings
            if os.path.exists(self.embeddings_file):
                with open(self.embeddings_file, 'rb') as f:
                    embeddings = pickle.load(f)
            else:
                return {
                    "status": "error",
                    "error": "No embeddings found. Run 'process_files' first."
                }

            # Calculate similarities
            similarities = np.dot(embeddings, query_embedding) / (
                np.linalg.norm(embeddings, axis=1) * np.linalg.norm(query_embedding)
            )

            # Get top results
            top_indices = np.argsort(similarities)[::-1]

            # Load metadata
            metadata = self._load_metadata()

            results = []
            for idx in top_indices:
                if similarities[idx] >= similarity_threshold and len(results) < max_results:
                    if idx < len(metadata):
                        meta = metadata[idx]
                        results.append({
                            "filename": meta['filename'],
                            "content": meta['text'][:500] + "..." if len(meta['text']) > 500 else meta['text'],
                            "chunk_id": meta['chunk_id'],
                            "similarity_score": float(similarities[idx]),
                            "start_word": meta['start_word'],
                            "end_word": meta['end_word']
                        })

            return {
                "status": "success",
                "action": "semantic_search",
                "query": query,
                "results": results,
                "total_results": len(results),
                "search_method": "NumPy",
                "message": f"Found {len(results)} relevant chunks"
            }

        except Exception as e:
            logger.error(f"NumPy search failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    def _rag_query(self, query: str, max_results: int = 3) -> Dict[str, Any]:
        """Perform RAG query with context generation"""
        # First get relevant documents
        search_result = self._enhanced_semantic_search(query, max_results)

        if search_result['status'] != 'success':
            return search_result

        # Prepare context from retrieved documents
        context_parts = []
        for result in search_result['results']:
            context_parts.append(f"From {result['filename']}: {result['content']}")

        context = "\n\n".join(context_parts)

        return {
            "status": "success",
            "action": "rag_query",
            "query": query,
            "context": context,
            "retrieved_documents": search_result['results'],
            "context_length": len(context),
            "message": f"Retrieved {len(search_result['results'])} relevant documents for context"
        }

    def _process_files_action(self) -> Dict[str, Any]:
        """Process all files in knowledge base"""
        try:
            count = self._process_knowledge_base_files()
            return {
                "status": "success",
                "action": "process_files",
                "processed_chunks": count,
                "message": f"Processed knowledge base with {count} chunks"
            }
        except Exception as e:
            return {
                "status": "error",
                "action": "process_files",
                "error": str(e)
            }

    def _get_status(self) -> Dict[str, Any]:
        """Get knowledge base status"""
        try:
            metadata = self._load_metadata()

            # Count files and chunks
            files = set()
            for meta in metadata:
                files.add(meta['filename'])

            # Check vector store status
            vector_store_info = "None"
            if self.vector_store is not None and FAISS_AVAILABLE:
                vector_store_info = f"FAISS ({self.vector_store.ntotal} vectors)"
            elif os.path.exists(self.embeddings_file):
                vector_store_info = "NumPy arrays"

            return {
                "status": "success",
                "action": "status",
                "knowledge_base_path": self.kb_path,
                "total_files": len(files),
                "total_chunks": len(metadata),
                "embedding_model": str(type(self.embedding_model).__name__) if hasattr(self.embedding_model, '__class__') else str(self.embedding_model),
                "embedding_dimension": self.embedding_dim,
                "vector_store": vector_store_info,
                "chunk_size": self.chunk_size,
                "chunk_overlap": self.chunk_overlap
            }

        except Exception as e:
            return {
                "status": "error",
                "action": "status",
                "error": str(e)
            }

    def _process_knowledge_base_files(self):
        """Process all files in the knowledge base directory with chunking"""
        logger.info("Processing knowledge base files with enhanced RAG...")

        processed_count = 0
        embeddings = []
        metadata_list = []

        for filename in os.listdir(self.kb_path):
            if filename.endswith(('.md', '.txt', '.json')) and not filename.startswith('enhanced_'):
                file_path = os.path.join(self.kb_path, filename)

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Chunk the content
                    chunks = self._chunk_text(content, filename)

                    for chunk in chunks:
                        # Generate embedding
                        embedding = self._generate_embedding(chunk['text'])
                        embeddings.append(embedding)

                        # Store metadata
                        metadata_list.append({
                            'filename': filename,
                            'chunk_id': chunk['chunk_id'],
                            'text': chunk['text'],
                            'start_word': chunk['start_word'],
                            'end_word': chunk['end_word'],
                            'processed_at': datetime.now().isoformat()
                        })

                    processed_count += 1
                    if processed_count % 10 == 0:
                        print(f"Processed {processed_count} files...")

                except Exception as e:
                    logger.error(f"Error processing {filename}: {e}")

        # Store embeddings and metadata
        if embeddings:
            embeddings_array = np.vstack(embeddings)

            # Save to FAISS if available
            if self.vector_store is not None and FAISS_AVAILABLE:
                # Normalize for cosine similarity
                faiss.normalize_L2(embeddings_array)
                self.vector_store.add(embeddings_array)
                faiss.write_index(self.vector_store, self.faiss_index_file)
                logger.info(f"Added {len(embeddings)} embeddings to FAISS index")

            # Save metadata
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata_list, f, indent=2)

            # Save embeddings as backup
            with open(self.embeddings_file, 'wb') as f:
                pickle.dump(embeddings_array, f)

        logger.info(f"Processed {processed_count} files with {len(embeddings)} chunks")
        return len(embeddings)

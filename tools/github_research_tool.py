import requests
import json
import base64
from typing import Dict, Any, List
from .base_tool import BaseTool

class GitHubResearchTool(BaseTool):
    """GitHub repository analysis for implementation research"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # Support both old and new config formats
        if 'github' in config:
            github_config = config['github']
        elif 'research_apis' in config and 'github' in config['research_apis']:
            github_config = config['research_apis']['github']
        else:
            github_config = {}

        self.token = github_config.get('token', '')
        self.base_url = github_config.get('base_url', 'https://api.github.com')
        self.rate_limit_delay = github_config.get('rate_limit_delay', 1.0)
        self.timeout = github_config.get('timeout', 30)

        self.headers = {
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'Research-Heavy-Tool/1.0'
        }

        if self.token:
            self.headers['Authorization'] = f'token {self.token}'
    
    @property
    def name(self) -> str:
        return "github_research"
    
    @property
    def description(self) -> str:
        return "Search and analyze GitHub repositories for implementation research, code examples, and technical approaches"
    
    @property
    def parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "query": {
                    "type": "string",
                    "description": "Search query for repositories or code"
                },
                "search_type": {
                    "type": "string",
                    "enum": ["repositories", "code", "issues", "users"],
                    "description": "Type of GitHub search to perform",
                    "default": "repositories"
                },
                "language": {
                    "type": "string",
                    "description": "Programming language filter (e.g., 'python', 'javascript')",
                    "default": ""
                },
                "sort": {
                    "type": "string",
                    "enum": ["stars", "forks", "updated", "created"],
                    "description": "Sort criteria for repositories",
                    "default": "stars"
                },
                "max_results": {
                    "type": "integer",
                    "description": "Maximum number of results to return (1-100)",
                    "default": 20,
                    "minimum": 1,
                    "maximum": 100
                },
                "repo_owner": {
                    "type": "string",
                    "description": "Repository owner for specific repo analysis",
                    "default": ""
                },
                "repo_name": {
                    "type": "string",
                    "description": "Repository name for specific repo analysis",
                    "default": ""
                }
            },
            "required": ["query"]
        }
    
    def execute(self, query: str, search_type: str = "repositories", language: str = "",
                sort: str = "stars", max_results: int = 20, repo_owner: str = "",
                repo_name: str = "") -> Dict[str, Any]:
        """Execute GitHub search or repository analysis"""
        import time

        try:
            # Rate limiting
            time.sleep(self.rate_limit_delay)

            if not self.token:
                return {
                    "error": "GitHub token not configured. Please check github settings in config.yaml",
                    "status": "error"
                }
            
            # Handle specific repository analysis
            if repo_owner and repo_name:
                return self._analyze_repository(repo_owner, repo_name)
            
            # Handle different search types
            if search_type == "repositories":
                return self._search_repositories(query, language, sort, max_results)
            elif search_type == "code":
                return self._search_code(query, language, max_results)
            elif search_type == "issues":
                return self._search_issues(query, max_results)
            elif search_type == "users":
                return self._search_users(query, max_results)
            else:
                return self._search_repositories(query, language, sort, max_results)
                
        except Exception as e:
            return {
                "error": f"GitHub API error: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _search_repositories(self, query: str, language: str, sort: str, max_results: int) -> Dict[str, Any]:
        """Search GitHub repositories"""
        try:
            # Construct search query
            search_query = query
            if language:
                search_query += f" language:{language}"
            
            params = {
                'q': search_query,
                'sort': sort,
                'order': 'desc',
                'per_page': min(max_results, 100)
            }
            
            response = requests.get(f"{self.base_url}/search/repositories", 
                                  headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            repositories = []
            
            for repo in data.get('items', []):
                repo_info = {
                    'name': repo.get('name', ''),
                    'full_name': repo.get('full_name', ''),
                    'description': repo.get('description', ''),
                    'language': repo.get('language', ''),
                    'stars': repo.get('stargazers_count', 0),
                    'forks': repo.get('forks_count', 0),
                    'issues': repo.get('open_issues_count', 0),
                    'created_at': repo.get('created_at', ''),
                    'updated_at': repo.get('updated_at', ''),
                    'url': repo.get('html_url', ''),
                    'clone_url': repo.get('clone_url', ''),
                    'topics': repo.get('topics', []),
                    'license': repo.get('license', {}).get('name', '') if repo.get('license') else '',
                    'owner': repo.get('owner', {}).get('login', '')
                }
                repositories.append(repo_info)
            
            return {
                "status": "success",
                "search_type": "repositories",
                "query": query,
                "total_results": data.get('total_count', 0),
                "returned_results": len(repositories),
                "repositories": repositories,
                "summary": f"Found {data.get('total_count', 0)} repositories for '{query}'. Showing top {len(repositories)} results."
            }
            
        except Exception as e:
            return {
                "error": f"Repository search failed: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _search_code(self, query: str, language: str, max_results: int) -> Dict[str, Any]:
        """Search GitHub code"""
        try:
            search_query = query
            if language:
                search_query += f" language:{language}"
            
            params = {
                'q': search_query,
                'per_page': min(max_results, 100)
            }
            
            response = requests.get(f"{self.base_url}/search/code", 
                                  headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            code_results = []
            
            for item in data.get('items', []):
                code_info = {
                    'name': item.get('name', ''),
                    'path': item.get('path', ''),
                    'repository': item.get('repository', {}).get('full_name', ''),
                    'url': item.get('html_url', ''),
                    'language': item.get('repository', {}).get('language', ''),
                    'score': item.get('score', 0)
                }
                code_results.append(code_info)
            
            return {
                "status": "success",
                "search_type": "code",
                "query": query,
                "total_results": data.get('total_count', 0),
                "returned_results": len(code_results),
                "code_results": code_results,
                "summary": f"Found {data.get('total_count', 0)} code files for '{query}'. Showing top {len(code_results)} results."
            }
            
        except Exception as e:
            return {
                "error": f"Code search failed: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _search_issues(self, query: str, max_results: int) -> Dict[str, Any]:
        """Search GitHub issues"""
        try:
            params = {
                'q': query,
                'per_page': min(max_results, 100)
            }
            
            response = requests.get(f"{self.base_url}/search/issues", 
                                  headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            issues = []
            
            for issue in data.get('items', []):
                issue_info = {
                    'title': issue.get('title', ''),
                    'number': issue.get('number', 0),
                    'state': issue.get('state', ''),
                    'repository': issue.get('repository_url', '').split('/')[-2:],
                    'url': issue.get('html_url', ''),
                    'created_at': issue.get('created_at', ''),
                    'updated_at': issue.get('updated_at', ''),
                    'labels': [label.get('name', '') for label in issue.get('labels', [])],
                    'user': issue.get('user', {}).get('login', '')
                }
                issues.append(issue_info)
            
            return {
                "status": "success",
                "search_type": "issues",
                "query": query,
                "total_results": data.get('total_count', 0),
                "returned_results": len(issues),
                "issues": issues,
                "summary": f"Found {data.get('total_count', 0)} issues for '{query}'. Showing top {len(issues)} results."
            }
            
        except Exception as e:
            return {
                "error": f"Issues search failed: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _search_users(self, query: str, max_results: int) -> Dict[str, Any]:
        """Search GitHub users"""
        try:
            params = {
                'q': query,
                'per_page': min(max_results, 100)
            }
            
            response = requests.get(f"{self.base_url}/search/users", 
                                  headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            users = []
            
            for user in data.get('items', []):
                user_info = {
                    'login': user.get('login', ''),
                    'name': user.get('name', ''),
                    'bio': user.get('bio', ''),
                    'location': user.get('location', ''),
                    'company': user.get('company', ''),
                    'public_repos': user.get('public_repos', 0),
                    'followers': user.get('followers', 0),
                    'following': user.get('following', 0),
                    'url': user.get('html_url', ''),
                    'avatar_url': user.get('avatar_url', '')
                }
                users.append(user_info)
            
            return {
                "status": "success",
                "search_type": "users",
                "query": query,
                "total_results": data.get('total_count', 0),
                "returned_results": len(users),
                "users": users,
                "summary": f"Found {data.get('total_count', 0)} users for '{query}'. Showing top {len(users)} results."
            }
            
        except Exception as e:
            return {
                "error": f"Users search failed: {str(e)}",
                "status": "error",
                "query": query
            }
    
    def _analyze_repository(self, owner: str, repo: str) -> Dict[str, Any]:
        """Analyze a specific repository in detail"""
        try:
            # Get repository information
            repo_response = requests.get(f"{self.base_url}/repos/{owner}/{repo}", 
                                       headers=self.headers, timeout=30)
            repo_response.raise_for_status()
            repo_data = repo_response.json()
            
            # Get repository contents
            contents_response = requests.get(f"{self.base_url}/repos/{owner}/{repo}/contents", 
                                           headers=self.headers, timeout=30)
            contents_data = contents_response.json() if contents_response.status_code == 200 else []
            
            # Get recent commits
            commits_response = requests.get(f"{self.base_url}/repos/{owner}/{repo}/commits", 
                                          headers=self.headers, params={'per_page': 10}, timeout=30)
            commits_data = commits_response.json() if commits_response.status_code == 200 else []
            
            return {
                "status": "success",
                "repository": {
                    'name': repo_data.get('name', ''),
                    'full_name': repo_data.get('full_name', ''),
                    'description': repo_data.get('description', ''),
                    'language': repo_data.get('language', ''),
                    'stars': repo_data.get('stargazers_count', 0),
                    'forks': repo_data.get('forks_count', 0),
                    'size': repo_data.get('size', 0),
                    'created_at': repo_data.get('created_at', ''),
                    'updated_at': repo_data.get('updated_at', ''),
                    'topics': repo_data.get('topics', []),
                    'license': repo_data.get('license', {}).get('name', '') if repo_data.get('license') else '',
                    'default_branch': repo_data.get('default_branch', ''),
                    'url': repo_data.get('html_url', '')
                },
                "contents": [{'name': item.get('name', ''), 'type': item.get('type', ''), 'path': item.get('path', '')} 
                           for item in contents_data[:20]],  # Limit to first 20 items
                "recent_commits": [{'message': commit.get('commit', {}).get('message', ''), 
                                  'author': commit.get('commit', {}).get('author', {}).get('name', ''),
                                  'date': commit.get('commit', {}).get('author', {}).get('date', '')} 
                                 for commit in commits_data[:5]],  # Last 5 commits
                "summary": f"Analyzed repository {owner}/{repo} with {repo_data.get('stargazers_count', 0)} stars"
            }
            
        except Exception as e:
            return {
                "error": f"Repository analysis failed: {str(e)}",
                "status": "error",
                "repository": f"{owner}/{repo}"
            }

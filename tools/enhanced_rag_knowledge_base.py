import os
import json
import pickle
import hashlib
import time
import numpy as np
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from .base_tool import BaseTool

# Try to import advanced embedding models
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False

logger = logging.getLogger(__name__)

class EnhancedRAGKnowledgeBase(BaseTool):
    """Enhanced RAG-based knowledge base with proper vector embeddings and retrieval"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Knowledge base path
        if 'research_apis' in config and 'knowledge_base' in config['research_apis']:
            self.kb_path = config['research_apis']['knowledge_base']['path']
            self.checkpoint_path = config['research_apis']['knowledge_base']['checkpoint_path']
        else:
            self.kb_path = r'D:\Downloads\make-it-heavy-main_\research_knowledge_base'
            self.checkpoint_path = r'D:\Downloads\make-it-heavy-main_\research_checkpoints'
        
        # Ensure directories exist
        os.makedirs(self.kb_path, exist_ok=True)
        os.makedirs(self.checkpoint_path, exist_ok=True)
        
        # Initialize files
        self.index_file = os.path.join(self.kb_path, 'enhanced_index.json')
        self.embeddings_file = os.path.join(self.kb_path, 'enhanced_embeddings.pkl')
        self.faiss_index_file = os.path.join(self.kb_path, 'faiss_index.bin')
        self.metadata_file = os.path.join(self.kb_path, 'metadata.json')
        
        # Initialize embedding model
        self.embedding_model = self._initialize_embedding_model()
        self.embedding_dim = self._get_embedding_dimension()
        
        # Initialize vector store
        self.vector_store = self._initialize_vector_store()
        
        # Load existing data
        self._initialize_index()
        self.metadata = self._load_metadata()
        
        # Document processing
        self.chunk_size = 512  # Optimal chunk size for embeddings
        self.chunk_overlap = 50  # Overlap between chunks
        
    def _initialize_embedding_model(self):
        """Initialize the best available embedding model"""
        # Priority: OpenAI > SentenceTransformers > Simple TF-IDF
        
        # Try OpenAI embeddings first (best quality)
        if OPENAI_AVAILABLE and self.config.get('unified_models', {}).get('providers', {}).get('openai', {}).get('api_key'):
            try:
                openai.api_key = self.config['unified_models']['providers']['openai']['api_key']
                # Test the API
                test_response = openai.embeddings.create(
                    model="text-embedding-3-small",
                    input="test"
                )
                logger.info("Using OpenAI embeddings (text-embedding-3-small)")
                return "openai"
            except Exception as e:
                logger.warning(f"OpenAI embeddings failed: {e}")
        
        # Try SentenceTransformers (good quality, local)
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                model = SentenceTransformer('all-MiniLM-L6-v2')  # Fast and good quality
                logger.info("Using SentenceTransformers (all-MiniLM-L6-v2)")
                return model
            except Exception as e:
                logger.warning(f"SentenceTransformers failed: {e}")
        
        # Fallback to simple TF-IDF
        logger.info("Using simple TF-IDF embeddings (fallback)")
        return "simple"
    
    def _get_embedding_dimension(self):
        """Get embedding dimension based on model"""
        if self.embedding_model == "openai":
            return 1536  # text-embedding-3-small dimension
        elif isinstance(self.embedding_model, SentenceTransformer):
            return self.embedding_model.get_sentence_embedding_dimension()
        else:
            return 384  # Simple TF-IDF dimension
    
    def _initialize_vector_store(self):
        """Initialize FAISS vector store if available"""
        if FAISS_AVAILABLE:
            try:
                if os.path.exists(self.faiss_index_file):
                    # Load existing index
                    index = faiss.read_index(self.faiss_index_file)
                    logger.info(f"Loaded existing FAISS index with {index.ntotal} vectors")
                else:
                    # Create new index
                    index = faiss.IndexFlatIP(self.embedding_dim)  # Inner product for cosine similarity
                    logger.info(f"Created new FAISS index with dimension {self.embedding_dim}")
                return index
            except Exception as e:
                logger.warning(f"FAISS initialization failed: {e}")
        
        logger.info("Using simple numpy-based vector store")
        return None
    
    def _generate_embedding(self, text: str) -> np.ndarray:
        """Generate embedding for text using the best available model"""
        try:
            if self.embedding_model == "openai":
                response = openai.embeddings.create(
                    model="text-embedding-3-small",
                    input=text
                )
                return np.array(response.data[0].embedding, dtype=np.float32)
            
            elif isinstance(self.embedding_model, SentenceTransformer):
                embedding = self.embedding_model.encode(text)
                return np.array(embedding, dtype=np.float32)
            
            else:
                # Simple TF-IDF fallback
                return self._simple_text_embedding(text)
                
        except Exception as e:
            logger.error(f"Embedding generation failed: {e}")
            return self._simple_text_embedding(text)
    
    def _simple_text_embedding(self, text: str) -> np.ndarray:
        """Simple TF-IDF-like embedding as fallback"""
        words = text.lower().split()
        vocab = set(words)
        vector = np.zeros(self.embedding_dim, dtype=np.float32)
        
        for i, word in enumerate(list(vocab)[:self.embedding_dim]):
            if i < self.embedding_dim:
                vector[i] = words.count(word) / len(words)
        
        # Normalize
        norm = np.linalg.norm(vector)
        if norm > 0:
            vector = vector / norm
        
        return vector
    
    def _chunk_text(self, text: str, filename: str = "") -> List[Dict[str, Any]]:
        """Split text into overlapping chunks for better retrieval"""
        words = text.split()
        chunks = []
        
        for i in range(0, len(words), self.chunk_size - self.chunk_overlap):
            chunk_words = words[i:i + self.chunk_size]
            chunk_text = ' '.join(chunk_words)
            
            chunks.append({
                'text': chunk_text,
                'filename': filename,
                'chunk_id': i // (self.chunk_size - self.chunk_overlap),
                'start_word': i,
                'end_word': min(i + self.chunk_size, len(words))
            })
        
        return chunks
    
    def _process_knowledge_base_files(self):
        """Process all files in the knowledge base directory"""
        logger.info("Processing knowledge base files...")
        
        processed_count = 0
        embeddings = []
        metadata_list = []
        
        for filename in os.listdir(self.kb_path):
            if filename.endswith(('.md', '.txt', '.json')):
                file_path = os.path.join(self.kb_path, filename)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Chunk the content
                    chunks = self._chunk_text(content, filename)
                    
                    for chunk in chunks:
                        # Generate embedding
                        embedding = self._generate_embedding(chunk['text'])
                        embeddings.append(embedding)
                        
                        # Store metadata
                        metadata_list.append({
                            'filename': filename,
                            'chunk_id': chunk['chunk_id'],
                            'text': chunk['text'],
                            'start_word': chunk['start_word'],
                            'end_word': chunk['end_word'],
                            'processed_at': datetime.now().isoformat()
                        })
                    
                    processed_count += 1
                    if processed_count % 10 == 0:
                        logger.info(f"Processed {processed_count} files...")
                        
                except Exception as e:
                    logger.error(f"Error processing {filename}: {e}")
        
        # Store embeddings and metadata
        if embeddings:
            embeddings_array = np.vstack(embeddings)
            
            # Save to FAISS if available
            if self.vector_store is not None and FAISS_AVAILABLE:
                # Normalize for cosine similarity
                faiss.normalize_L2(embeddings_array)
                self.vector_store.add(embeddings_array)
                faiss.write_index(self.vector_store, self.faiss_index_file)
                logger.info(f"Added {len(embeddings)} embeddings to FAISS index")
            
            # Save metadata
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata_list, f, indent=2)
            
            # Save embeddings as backup
            with open(self.embeddings_file, 'wb') as f:
                pickle.dump(embeddings_array, f)
        
        logger.info(f"Processed {processed_count} files with {len(embeddings)} chunks")
        return len(embeddings)
    
    @property
    def name(self) -> str:
        return "enhanced_rag_knowledge_base"
    
    @property
    def description(self) -> str:
        return "Enhanced RAG-based knowledge base with advanced embeddings and vector search"
    
    @property
    def parameters(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "enum": ["search", "semantic_search", "rag_query", "process_files", "status"],
                    "description": "Action to perform"
                },
                "query": {
                    "type": "string",
                    "description": "Search query or question"
                },
                "max_results": {
                    "type": "integer",
                    "default": 5,
                    "description": "Maximum number of results to return"
                },
                "similarity_threshold": {
                    "type": "number",
                    "default": 0.7,
                    "description": "Minimum similarity score for results"
                }
            },
            "required": ["action"]
        }

    def execute(self, **kwargs) -> Dict[str, Any]:
        """Execute knowledge base operations"""
        action = kwargs.get('action', 'search')

        try:
            if action == 'search' or action == 'semantic_search':
                return self._semantic_search(
                    query=kwargs.get('query', ''),
                    max_results=kwargs.get('max_results', 5),
                    similarity_threshold=kwargs.get('similarity_threshold', 0.7)
                )

            elif action == 'rag_query':
                return self._rag_query(
                    query=kwargs.get('query', ''),
                    max_results=kwargs.get('max_results', 3)
                )

            elif action == 'process_files':
                return self._process_files_action()

            elif action == 'status':
                return self._get_status()

            else:
                return {
                    "status": "error",
                    "error": f"Unknown action: {action}",
                    "available_actions": ["search", "semantic_search", "rag_query", "process_files", "status"]
                }

        except Exception as e:
            logger.error(f"Error executing action {action}: {e}")
            return {
                "status": "error",
                "action": action,
                "error": str(e)
            }

    def _semantic_search(self, query: str, max_results: int = 5, similarity_threshold: float = 0.7) -> Dict[str, Any]:
        """Perform semantic search using vector embeddings"""
        if not query.strip():
            return {
                "status": "error",
                "error": "Query cannot be empty"
            }

        try:
            # Generate query embedding
            query_embedding = self._generate_embedding(query)

            # Search using FAISS if available
            if self.vector_store is not None and FAISS_AVAILABLE:
                return self._faiss_search(query_embedding, query, max_results, similarity_threshold)
            else:
                return self._numpy_search(query_embedding, query, max_results, similarity_threshold)

        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "message": "Semantic search failed"
            }

    def _faiss_search(self, query_embedding: np.ndarray, query: str, max_results: int, similarity_threshold: float) -> Dict[str, Any]:
        """Search using FAISS vector store"""
        try:
            # Normalize query embedding for cosine similarity
            query_embedding = query_embedding.reshape(1, -1).astype(np.float32)
            faiss.normalize_L2(query_embedding)

            # Search
            scores, indices = self.vector_store.search(query_embedding, max_results * 2)  # Get more to filter

            # Load metadata
            metadata = self._load_metadata()

            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx >= 0 and idx < len(metadata) and score >= similarity_threshold:
                    meta = metadata[idx]
                    results.append({
                        "filename": meta['filename'],
                        "content": meta['text'][:500] + "..." if len(meta['text']) > 500 else meta['text'],
                        "chunk_id": meta['chunk_id'],
                        "similarity_score": float(score),
                        "start_word": meta['start_word'],
                        "end_word": meta['end_word']
                    })

            return {
                "status": "success",
                "action": "semantic_search",
                "query": query,
                "results": results[:max_results],
                "total_results": len(results),
                "search_method": "FAISS",
                "message": f"Found {len(results)} relevant chunks"
            }

        except Exception as e:
            logger.error(f"FAISS search failed: {e}")
            return self._numpy_search(query_embedding, query, max_results, similarity_threshold)

    def _numpy_search(self, query_embedding: np.ndarray, query: str, max_results: int, similarity_threshold: float) -> Dict[str, Any]:
        """Fallback search using numpy"""
        try:
            # Load embeddings
            if os.path.exists(self.embeddings_file):
                with open(self.embeddings_file, 'rb') as f:
                    embeddings = pickle.load(f)
            else:
                return {
                    "status": "error",
                    "error": "No embeddings found. Run 'process_files' first."
                }

            # Calculate similarities
            similarities = np.dot(embeddings, query_embedding) / (
                np.linalg.norm(embeddings, axis=1) * np.linalg.norm(query_embedding)
            )

            # Get top results
            top_indices = np.argsort(similarities)[::-1]

            # Load metadata
            metadata = self._load_metadata()

            results = []
            for idx in top_indices:
                if similarities[idx] >= similarity_threshold and len(results) < max_results:
                    if idx < len(metadata):
                        meta = metadata[idx]
                        results.append({
                            "filename": meta['filename'],
                            "content": meta['text'][:500] + "..." if len(meta['text']) > 500 else meta['text'],
                            "chunk_id": meta['chunk_id'],
                            "similarity_score": float(similarities[idx]),
                            "start_word": meta['start_word'],
                            "end_word": meta['end_word']
                        })

            return {
                "status": "success",
                "action": "semantic_search",
                "query": query,
                "results": results,
                "total_results": len(results),
                "search_method": "NumPy",
                "message": f"Found {len(results)} relevant chunks"
            }

        except Exception as e:
            logger.error(f"NumPy search failed: {e}")
            return {
                "status": "error",
                "error": str(e)
            }

    def _rag_query(self, query: str, max_results: int = 3) -> Dict[str, Any]:
        """Perform RAG query with context generation"""
        # First get relevant documents
        search_result = self._semantic_search(query, max_results, 0.6)

        if search_result['status'] != 'success':
            return search_result

        # Prepare context from retrieved documents
        context_parts = []
        for result in search_result['results']:
            context_parts.append(f"From {result['filename']}: {result['content']}")

        context = "\n\n".join(context_parts)

        return {
            "status": "success",
            "action": "rag_query",
            "query": query,
            "context": context,
            "retrieved_documents": search_result['results'],
            "context_length": len(context),
            "message": f"Retrieved {len(search_result['results'])} relevant documents for context"
        }

    def _process_files_action(self) -> Dict[str, Any]:
        """Process all files in knowledge base"""
        try:
            count = self._process_knowledge_base_files()
            return {
                "status": "success",
                "action": "process_files",
                "processed_chunks": count,
                "message": f"Processed knowledge base with {count} chunks"
            }
        except Exception as e:
            return {
                "status": "error",
                "action": "process_files",
                "error": str(e)
            }

    def _get_status(self) -> Dict[str, Any]:
        """Get knowledge base status"""
        try:
            metadata = self._load_metadata()

            # Count files and chunks
            files = set()
            for meta in metadata:
                files.add(meta['filename'])

            # Check vector store status
            vector_store_info = "None"
            if self.vector_store is not None and FAISS_AVAILABLE:
                vector_store_info = f"FAISS ({self.vector_store.ntotal} vectors)"
            elif os.path.exists(self.embeddings_file):
                vector_store_info = "NumPy arrays"

            return {
                "status": "success",
                "action": "status",
                "knowledge_base_path": self.kb_path,
                "total_files": len(files),
                "total_chunks": len(metadata),
                "embedding_model": str(type(self.embedding_model).__name__) if hasattr(self.embedding_model, '__class__') else str(self.embedding_model),
                "embedding_dimension": self.embedding_dim,
                "vector_store": vector_store_info,
                "chunk_size": self.chunk_size,
                "chunk_overlap": self.chunk_overlap
            }

        except Exception as e:
            return {
                "status": "error",
                "action": "status",
                "error": str(e)
            }

    def _load_metadata(self) -> List[Dict[str, Any]]:
        """Load metadata from file"""
        try:
            if os.path.exists(self.metadata_file):
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.warning(f"Could not load metadata: {e}")
        return []

    def _initialize_index(self):
        """Initialize index file"""
        if not os.path.exists(self.index_file):
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump({"version": "2.0", "created": datetime.now().isoformat()}, f)

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Research Heavy - Multi-Agent Research Orchestration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-dark bg-dark">
        <div class="container-fluid">
            <span class="navbar-brand mb-0 h1">
                <i class="fas fa-brain"></i> Research Heavy
                <small class="text-muted">Multi-Agent Research Orchestration</small>
            </span>
            <div class="d-flex">
                <button class="btn btn-outline-light me-2" onclick="showConfigModal()">
                    <i class="fas fa-cog"></i> Configure APIs
                </button>
                <button class="btn btn-outline-light" onclick="showKnowledgeBaseModal()">
                    <i class="fas fa-database"></i> Knowledge Base
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <div class="row">
            <!-- Left Panel: Input and Configuration -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-edit"></i> Research Query</h5>
                    </div>
                    <div class="card-body">
                        <form id="researchForm">
                            <!-- Query Input -->
                            <div class="mb-3">
                                <label for="queryInput" class="form-label">Research Query</label>
                                <textarea class="form-control" id="queryInput" rows="6" 
                                    placeholder="Enter your research question or directive here..."></textarea>
                            </div>

                            <!-- Research Type -->
                            <div class="mb-3">
                                <label for="researchType" class="form-label">Research Type</label>
                                <select class="form-select" id="researchType">
                                    <option value="comprehensive">Comprehensive Research (4 phases)</option>
                                    <option value="literature_review">Literature Review</option>
                                    <option value="gap_analysis">Gap Analysis</option>
                                    <option value="idea_generation">Idea Generation</option>
                                    <option value="implementation">Implementation Research</option>
                                </select>
                            </div>

                            <!-- Model Provider -->
                            <div class="mb-3">
                                <label for="modelProvider" class="form-label">Model Provider</label>
                                <select class="form-select" id="modelProvider">
                                    <option value="">Auto (with fallback)</option>
                                    <option value="gemini">Google Gemini</option>
                                    <option value="openai">OpenAI</option>
                                    <option value="openrouter">OpenRouter</option>
                                    <option value="anthropic">Anthropic Claude</option>
                                    <option value="moonshot">Moonshot AI (Kimi)</option>
                                    <option value="deepseek">DeepSeek</option>
                                </select>
                            </div>

                            <!-- Number of Agents -->
                            <div class="mb-3">
                                <label for="numAgents" class="form-label">Number of Agents: <span id="agentCount">6</span></label>
                                <input type="range" class="form-range" id="numAgents" min="3" max="20" value="6"
                                    oninput="document.getElementById('agentCount').textContent = this.value">
                                <div class="form-text">
                                    <small>Quick: 3-4 | Standard: 6-8 | Deep: 12-20</small>
                                </div>
                            </div>

                            <!-- Advanced Options -->
                            <div class="mb-3">
                                <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#advancedOptions" aria-expanded="false">
                                    <i class="fas fa-cog"></i> Advanced Options
                                </button>
                                <div class="collapse mt-2" id="advancedOptions">
                                    <div class="card card-body">
                                        <!-- Context Window -->
                                        <div class="mb-3">
                                            <label for="maxTokens" class="form-label">Max Context Window: <span id="tokenCount">4096</span></label>
                                            <input type="range" class="form-range" id="maxTokens" min="1024" max="32768" value="4096" step="1024"
                                                oninput="document.getElementById('tokenCount').textContent = this.value">
                                            <div class="form-text">
                                                <small>1K-4K: Fast | 8K-16K: Standard | 32K+: Deep Analysis</small>
                                            </div>
                                        </div>

                                        <!-- Temperature -->
                                        <div class="mb-3">
                                            <label for="temperature" class="form-label">Creativity (Temperature): <span id="tempValue">0.7</span></label>
                                            <input type="range" class="form-range" id="temperature" min="0" max="1" value="0.7" step="0.1"
                                                oninput="document.getElementById('tempValue').textContent = this.value">
                                            <div class="form-text">
                                                <small>0.0: Focused | 0.7: Balanced | 1.0: Creative</small>
                                            </div>
                                        </div>

                                        <!-- API Call Intensity -->
                                        <div class="mb-3">
                                            <label for="apiIntensity" class="form-label">Research Intensity</label>
                                            <select class="form-select" id="apiIntensity">
                                                <option value="light">Light (Fewer API calls, faster)</option>
                                                <option value="standard" selected>Standard (Balanced)</option>
                                                <option value="intensive">Intensive (More API calls, thorough)</option>
                                            </select>
                                        </div>

                                        <!-- Tool Usage -->
                                        <div class="mb-3">
                                            <label class="form-label">Research Tools</label>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="useSemanticScholar" checked>
                                                <label class="form-check-label" for="useSemanticScholar">
                                                    Semantic Scholar (Academic Papers)
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="useKnowledgeBase" checked>
                                                <label class="form-check-label" for="useKnowledgeBase">
                                                    Knowledge Base (Local Search)
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="useGitHub" checked>
                                                <label class="form-check-label" for="useGitHub">
                                                    GitHub Research (Code & Implementations)
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="useWebSearch" checked>
                                                <label class="form-check-label" for="useWebSearch">
                                                    Web Search (General Information)
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Context Mode -->
                            <div class="mb-3">
                                <label class="form-label">Context Mode</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="contextMode" id="contextOff" value="off">
                                    <label class="form-check-label" for="contextOff">
                                        Independent Agents
                                        <small class="text-muted d-block">Agents work independently</small>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="contextMode" id="contextStandard" value="standard" checked>
                                    <label class="form-check-label" for="contextStandard">
                                        Standard Context-Aware
                                        <small class="text-muted d-block">Share initial prompt + knowledge base</small>
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="contextMode" id="contextEnhanced" value="enhanced">
                                    <label class="form-check-label" for="contextEnhanced">
                                        Enhanced Context-Aware
                                        <small class="text-muted d-block">Share initial prompt + previous phase results</small>
                                    </label>
                                </div>
                            </div>

                            <!-- Preset Configurations -->
                            <div class="mb-3">
                                <label class="form-label">Quick Presets</label>
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setPreset('quick')">
                                        Quick (3 agents)
                                    </button>
                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="setPreset('standard')">
                                        Standard (6 agents)
                                    </button>
                                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="setPreset('deep')">
                                        Deep (12 agents)
                                    </button>
                                </div>
                            </div>

                            <!-- Start Research Button -->
                            <button type="submit" class="btn btn-primary w-100" id="startResearchBtn">
                                <i class="fas fa-play"></i> Start Research
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Agent Status Panel -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-users"></i> Agent Status</h6>
                    </div>
                    <div class="card-body">
                        <div id="agentStatus">
                            <p class="text-muted">No active research session</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel: Results and Progress -->
            <div class="col-md-8">
                <!-- Progress Panel -->
                <div class="card mb-3" id="progressPanel" style="display: none;">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line"></i> Research Progress</h5>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 id="overallProgress" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="phaseProgress"></div>
                        <div id="realTimeUpdates" class="mt-3"></div>
                    </div>
                </div>

                <!-- Results Panel -->
                <div class="card" id="resultsPanel">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-file-alt"></i> Research Results</h5>
                        <div id="exportButtons" style="display: none;">
                            <button class="btn btn-outline-primary btn-sm me-2" onclick="exportResults('json')">
                                <i class="fas fa-download"></i> JSON
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="exportResults('txt')">
                                <i class="fas fa-download"></i> TXT
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="resultsContent">
                            <div class="text-center text-muted py-5">
                                <i class="fas fa-search fa-3x mb-3"></i>
                                <h4>Ready for Research</h4>
                                <p>Enter your research query and start the multi-agent orchestration</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- API Configuration Modal -->
    <div class="modal fade" id="configModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-cog"></i> API Configuration</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="configForm">
                        <!-- Gemini API -->
                        <div class="mb-4">
                            <h6><i class="fab fa-google"></i> Google Gemini</h6>
                            <div class="mb-2">
                                <label class="form-label">API Key</label>
                                <input type="password" class="form-control" id="geminiApiKey" 
                                    placeholder="Enter Gemini API key">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Model</label>
                                <select class="form-select" id="geminiModel">
                                    <option value="gemini-2.5-pro">Gemini 2.5 Pro</option>
                                    <option value="gemini-2.5-flash">Gemini 2.5 Flash</option>
                                    <option value="gemini-1.5-pro">Gemini 1.5 Pro</option>
                                    <option value="gemini-1.5-flash">Gemini 1.5 Flash</option>
                                </select>
                            </div>
                        </div>

                        <!-- OpenAI API -->
                        <div class="mb-4">
                            <h6><i class="fas fa-robot"></i> OpenAI</h6>
                            <div class="mb-2">
                                <label class="form-label">API Key</label>
                                <input type="password" class="form-control" id="openaiApiKey" 
                                    placeholder="Enter OpenAI API key">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Model</label>
                                <select class="form-select" id="openaiModel">
                                    <option value="gpt-4o-mini">GPT-4o Mini</option>
                                    <option value="gpt-4o">GPT-4o</option>
                                    <option value="o1-mini">o1 Mini</option>
                                    <option value="o1-preview">o1 Preview</option>
                                    <option value="o3-mini">o3 Mini</option>
                                    <option value="gpt-4-turbo">GPT-4 Turbo</option>
                                </select>
                            </div>
                        </div>

                        <!-- OpenRouter API -->
                        <div class="mb-4">
                            <h6><i class="fas fa-route"></i> OpenRouter</h6>
                            <div class="mb-2">
                                <label class="form-label">API Key</label>
                                <input type="password" class="form-control" id="openrouterApiKey" 
                                    placeholder="Enter OpenRouter API key">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Model</label>
                                <select class="form-select" id="openrouterModel">
                                    <option value="anthropic/claude-3.5-sonnet">Claude 3.5 Sonnet</option>
                                    <option value="anthropic/claude-3.5-haiku">Claude 3.5 Haiku</option>
                                    <option value="openai/gpt-4o">GPT-4o</option>
                                    <option value="openai/gpt-4o-mini">GPT-4o Mini</option>
                                    <option value="openai/o1-mini">o1 Mini</option>
                                    <option value="openai/o1-preview">o1 Preview</option>
                                    <option value="google/gemini-2.5-pro">Gemini 2.5 Pro</option>
                                    <option value="google/gemini-2.5-flash">Gemini 2.5 Flash</option>
                                    <option value="moonshot/moonshot-v1-128k">Kimi 2 (128K)</option>
                                    <option value="deepseek/deepseek-r1">DeepSeek R1</option>
                                    <option value="meta-llama/llama-3.1-405b-instruct">Llama 3.1 405B</option>
                                </select>
                            </div>
                        </div>

                        <!-- Anthropic API -->
                        <div class="mb-4">
                            <h6><i class="fas fa-brain"></i> Anthropic Claude</h6>
                            <div class="mb-2">
                                <label class="form-label">API Key</label>
                                <input type="password" class="form-control" id="anthropicApiKey" 
                                    placeholder="Enter Anthropic API key">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Model</label>
                                <select class="form-select" id="anthropicModel">
                                    <option value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet</option>
                                    <option value="claude-3-5-haiku-20241022">Claude 3.5 Haiku</option>
                                    <option value="claude-3-opus-20240229">Claude 3 Opus</option>
                                </select>
                            </div>
                        </div>

                        <!-- Moonshot AI -->
                        <div class="mb-4">
                            <h6><i class="fas fa-moon"></i> Moonshot AI (Kimi)</h6>
                            <div class="mb-2">
                                <label class="form-label">API Key</label>
                                <input type="password" class="form-control" id="moonshotApiKey"
                                    placeholder="Enter Moonshot API key">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Model</label>
                                <select class="form-select" id="moonshotModel">
                                    <option value="moonshot-v1-8k">Kimi v1 8K</option>
                                    <option value="moonshot-v1-32k">Kimi v1 32K</option>
                                    <option value="moonshot-v1-128k">Kimi v1 128K (Kimi 2)</option>
                                    <option value="moonshot-v1-auto">Kimi Auto</option>
                                </select>
                            </div>
                        </div>

                        <!-- DeepSeek API -->
                        <div class="mb-4">
                            <h6><i class="fas fa-search"></i> DeepSeek</h6>
                            <div class="mb-2">
                                <label class="form-label">API Key</label>
                                <input type="password" class="form-control" id="deepseekApiKey"
                                    placeholder="Enter DeepSeek API key">
                            </div>
                            <div class="mb-2">
                                <label class="form-label">Model</label>
                                <select class="form-select" id="deepseekModel">
                                    <option value="deepseek-chat">DeepSeek Chat</option>
                                    <option value="deepseek-coder">DeepSeek Coder</option>
                                    <option value="deepseek-r1">DeepSeek R1</option>
                                </select>
                            </div>
                        </div>

                        <!-- GitHub API -->
                        <div class="mb-4">
                            <h6><i class="fab fa-github"></i> GitHub Research</h6>
                            <div class="mb-2">
                                <label class="form-label">GitHub Token</label>
                                <input type="password" class="form-control" id="githubToken"
                                    placeholder="Enter GitHub personal access token">
                                <small class="form-text text-muted">
                                    Get token from: <a href="https://github.com/settings/tokens" target="_blank">GitHub Settings</a>
                                </small>
                            </div>
                        </div>

                        <!-- Default Provider -->
                        <div class="mb-3">
                            <label class="form-label">Default Provider</label>
                            <select class="form-select" id="defaultProvider">
                                <option value="gemini">Google Gemini</option>
                                <option value="openai">OpenAI</option>
                                <option value="openrouter">OpenRouter</option>
                                <option value="anthropic">Anthropic</option>
                                <option value="moonshot">Moonshot AI (Kimi)</option>
                                <option value="deepseek">DeepSeek</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveConfig()">Save Configuration</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Knowledge Base Modal -->
    <div class="modal fade" id="knowledgeBaseModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-database"></i> Knowledge Base Management</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Upload Documents</h6>
                            <div class="mb-3">
                                <input type="file" class="form-control" id="documentUpload" 
                                    accept=".txt,.pdf,.docx,.json,.csv" multiple>
                                <div class="form-text">Supported: TXT, PDF, DOCX, JSON, CSV</div>
                            </div>
                            <button class="btn btn-primary" onclick="uploadDocuments()">
                                <i class="fas fa-upload"></i> Upload
                            </button>
                            
                            <hr>
                            
                            <h6>Search Knowledge Base</h6>
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" id="kbSearchQuery" 
                                    placeholder="Search knowledge base...">
                                <button class="btn btn-outline-secondary" onclick="searchKnowledgeBase()">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Knowledge Base Entries</h6>
                            <div id="knowledgeBaseEntries" style="max-height: 400px; overflow-y: auto;">
                                <p class="text-muted">Loading...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>

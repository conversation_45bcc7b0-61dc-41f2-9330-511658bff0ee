#!/usr/bin/env python3
"""
Install Free Dependencies for Enhanced RAG System
Installs SentenceTransformers for free, local embeddings
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package}: {e}")
        return False

def main():
    """Install free dependencies for RAG system"""
    print("🔧 INSTALLING FREE DEPENDENCIES FOR ENHANCED RAG SYSTEM")
    print("=" * 70)
    
    # Free, local dependencies (no API costs)
    free_packages = [
        "sentence-transformers",  # Free local embeddings
        "numpy",                  # Vector operations
        "scikit-learn",          # Additional ML utilities
        "nltk",                  # Text processing
        "tqdm",                  # Progress bars
    ]
    
    # Optional: FAISS for faster vector search (free, local)
    optional_packages = [
        "faiss-cpu",  # Fast vector search (CPU version)
    ]
    
    print("📦 Installing core free packages...")
    success_count = 0
    
    for package in free_packages:
        print(f"\n📥 Installing {package}...")
        if install_package(package):
            success_count += 1
    
    print(f"\n📦 Installing optional packages...")
    for package in optional_packages:
        print(f"\n📥 Installing {package} (optional)...")
        install_package(package)  # Don't count failures for optional
    
    print(f"\n🎯 INSTALLATION SUMMARY")
    print("=" * 70)
    print(f"✅ Core packages installed: {success_count}/{len(free_packages)}")
    
    if success_count == len(free_packages):
        print("🎉 All core dependencies installed successfully!")
        print("\n📋 What you get:")
        print("   ✅ SentenceTransformers: Free, high-quality local embeddings")
        print("   ✅ No API costs: Everything runs locally")
        print("   ✅ Fast performance: Optimized vector operations")
        print("   ✅ FAISS support: Ultra-fast similarity search")
        
        print("\n🚀 Next steps:")
        print("   1. The enhanced RAG system will automatically use SentenceTransformers")
        print("   2. No OpenAI API costs for embeddings")
        print("   3. Gemini API can be used for generation only (free tier)")
        print("   4. Run the analysis script to test the free setup")
        
    else:
        print("⚠️ Some packages failed to install. Check the errors above.")
    
    return success_count == len(free_packages)

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
Configure Free RAG System
Sets up the system to use free Gemini API and local embeddings
"""

import yaml
import os
from tools.enhanced_rag_knowledge_base import EnhancedRAGKnowledgeBase

def update_config_for_free_setup(gemini_api_key: str):
    """Update config to prioritize free options"""
    print("🔧 CONFIGURING FREE RAG SYSTEM")
    print("=" * 60)
    
    # Load current config
    with open('config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    
    # Update Gemini configuration
    if 'unified_models' not in config:
        config['unified_models'] = {'providers': {}}
    
    config['unified_models']['providers']['gemini'] = {
        'api_key': gemini_api_key,
        'model': 'gemini-2.5-pro',
        'rate_limit_delay': 5,
        'timeout': 600
    }
    
    # Set Gemini as default for generation
    config['unified_models']['default_provider'] = 'gemini'
    config['unified_models']['fallback_order'] = ['gemini', 'openrouter', 'openai']
    
    # Save updated config
    with open('config.yaml', 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    print(f"✅ Updated config.yaml with Gemini API key")
    print(f"✅ Set Gemini as default provider (free)")
    
    return config

def test_free_rag_system(gemini_api_key: str):
    """Test the free RAG system setup"""
    print("\n🧪 TESTING FREE RAG SYSTEM")
    print("=" * 60)
    
    # Update config first
    config = update_config_for_free_setup(gemini_api_key)
    
    try:
        # Initialize enhanced RAG system
        enhanced_kb = EnhancedRAGKnowledgeBase(config)
        
        print(f"📊 System Status:")
        status_result = enhanced_kb.execute(action="status")
        
        if status_result.get('status') == 'success':
            print(f"   ✅ Status: Working")
            print(f"   🧠 Embedding model: {status_result.get('embedding_model', 'unknown')}")
            print(f"   📐 Embedding dimension: {status_result.get('embedding_dimension', 0)}")
            print(f"   🗄️ Vector store: {status_result.get('vector_store', 'unknown')}")
            print(f"   📁 Knowledge base: {status_result.get('total_files', 0)} files")
            
            # Test if we need to process files
            if status_result.get('total_chunks', 0) == 0:
                print(f"\n🔄 Processing knowledge base with free embeddings...")
                process_result = enhanced_kb.execute(action="process_files")
                
                if process_result.get('status') == 'success':
                    print(f"   ✅ Processed {process_result.get('processed_chunks', 0)} chunks")
                    print(f"   💰 Cost: FREE (local embeddings)")
                else:
                    print(f"   ❌ Processing failed: {process_result.get('error', 'unknown')}")
            
            # Test semantic search
            print(f"\n🔍 Testing semantic search...")
            search_result = enhanced_kb.execute(
                action="semantic_search",
                query="machine learning algorithms",
                max_results=3
            )
            
            if search_result.get('status') == 'success':
                results = search_result.get('results', [])
                print(f"   ✅ Search working: {len(results)} results found")
                print(f"   🔍 Search method: {search_result.get('search_method', 'unknown')}")
                print(f"   💰 Cost: FREE (local search)")
            else:
                print(f"   ❌ Search failed: {search_result.get('error', 'unknown')}")
            
            return True
            
        else:
            print(f"   ❌ System status failed: {status_result.get('error', 'unknown')}")
            return False
            
    except Exception as e:
        print(f"❌ Free RAG system test failed: {e}")
        return False

def show_cost_comparison():
    """Show cost comparison between setups"""
    print("\n💰 COST COMPARISON")
    print("=" * 60)
    
    print("🔴 OpenAI Setup (Current):")
    print("   - Embeddings: $0.00002 per 1K tokens")
    print("   - 500 files (~1M tokens): ~$20")
    print("   - Generation: $0.01-0.06 per 1K tokens")
    
    print("\n🟢 Free Setup (Recommended):")
    print("   - Embeddings: FREE (SentenceTransformers local)")
    print("   - 500 files: $0")
    print("   - Generation: FREE (Gemini free tier)")
    
    print("\n🎯 Savings: ~$20+ per knowledge base processing")
    print("✅ Same quality: SentenceTransformers are research-grade")

def main():
    """Main configuration function"""
    print("🎯 FREE RAG SYSTEM CONFIGURATION")
    print("=" * 70)
    
    # Show cost comparison
    show_cost_comparison()
    
    # Get Gemini API key
    print(f"\n🔑 Gemini API Key Setup:")
    print(f"   1. Go to: https://aistudio.google.com/app/apikey")
    print(f"   2. Create a free API key")
    print(f"   3. Enter it below")
    
    gemini_api_key = input(f"\n🔑 Enter your Gemini API key: ").strip()
    
    if not gemini_api_key:
        print("❌ No API key provided. Exiting.")
        return False
    
    # Test the free system
    success = test_free_rag_system(gemini_api_key)
    
    if success:
        print(f"\n🎉 FREE RAG SYSTEM CONFIGURED SUCCESSFULLY!")
        print("=" * 70)
        print("✅ Embeddings: FREE (SentenceTransformers local)")
        print("✅ Generation: FREE (Gemini API)")
        print("✅ Vector Search: FREE (local)")
        print("✅ Knowledge Base: 500 files ready")
        print("✅ No API costs for embeddings")
        
        print(f"\n🚀 Your system now uses:")
        print(f"   - Local embeddings (no API costs)")
        print(f"   - Gemini for generation (free tier)")
        print(f"   - High-quality semantic search")
        print(f"   - Full RAG pipeline")
        
    else:
        print(f"\n❌ Configuration failed. Check the errors above.")
    
    return success

if __name__ == '__main__':
    main()

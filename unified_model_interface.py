#!/usr/bin/env python3
"""
Unified Model Interface for Multi-Provider LLM Support
Supports OpenRouter, OpenAI, Anthropic, Google Gemini, and Moonshot AI
"""

import time
import json
import requests
import google.generativeai as genai
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

class BaseLLMProvider(ABC):
    """Abstract base class for LLM providers"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.last_request_time = 0
        self.rate_limit_delay = config.get('rate_limit_delay', 1.0)
        self.timeout = config.get('timeout', 600)
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    @abstractmethod
    def generate(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response from the model"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """Check if the provider is available"""
        pass

class OpenRouterProvider(BaseLLMProvider):
    """OpenRouter API provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config['api_key']
        self.base_url = config.get('base_url', 'https://openrouter.ai/api/v1')
        self.model = config.get('model', 'anthropic/claude-3.5-sonnet')
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://research-heavy.ai',
            'X-Title': 'Research Heavy'
        }
    
    def generate(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using OpenRouter"""
        try:
            self._rate_limit()
            
            payload = {
                'model': self.model,
                'messages': [{'role': 'user', 'content': prompt}],
                'max_tokens': kwargs.get('max_tokens', 4096),
                'temperature': kwargs.get('temperature', 0.7),
                'top_p': kwargs.get('top_p', 0.9)
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'status': 'success',
                    'content': data['choices'][0]['message']['content'],
                    'provider': 'openrouter',
                    'model': self.model,
                    'usage': data.get('usage', {})
                }
            else:
                return {
                    'status': 'error',
                    'error': f"OpenRouter API error: {response.status_code} - {response.text}",
                    'provider': 'openrouter'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'error': f"OpenRouter request failed: {str(e)}",
                'provider': 'openrouter'
            }
    
    def is_available(self) -> bool:
        """Check OpenRouter availability"""
        try:
            test_response = requests.get(f"{self.base_url}/models", headers=self.headers, timeout=10)
            return test_response.status_code == 200
        except:
            return False

class OpenAIProvider(BaseLLMProvider):
    """OpenAI API provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config['api_key']
        self.model = config.get('model', 'gpt-4o-mini')
        self.base_url = config.get('base_url', 'https://api.openai.com/v1')
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def generate(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using OpenAI"""
        try:
            self._rate_limit()
            
            # Use max_completion_tokens for newer models and max_tokens for older models
            max_tokens_param = 'max_completion_tokens' if self.model in ['o4-mini-2025-04-16', 'o4-mini', 'o3-mini', 'o1-mini', 'o1-preview'] else 'max_tokens'

            # Configure optimal parameters for o4-mini-2025-04-16
            if self.model == 'o4-mini-2025-04-16':
                # Optimal parameters for o4-mini research advisor
                payload = {
                    'model': self.model,
                    'messages': [
                        {
                            'role': 'system',
                            'content': 'You are a cutting‑edge AI research advisor. When asked, you generate multiple, creative, yet feasible research‑technique proposals.'
                        },
                        {
                            'role': 'user',
                            'content': prompt
                        }
                    ],
                    max_tokens_param: kwargs.get('max_tokens', 1200),
                    'temperature': 1.1,  # High creativity
                    'top_p': 0.9,  # Trim extreme low-prob tokens
                    'reasoning_effort': 'high',  # Deeper logical chains
                    'n': 1,  # Generate 1 response (can be increased for multiple ideas)
                    'stream': False,
                    'tool_choice': 'none',
                    'response_format': {'type': 'text'}
                }
            else:
                # Standard configuration for other models
                temperature = 1.0 if self.model in ['o4-mini', 'o3-mini'] else kwargs.get('temperature', 0.7)

                payload = {
                    'model': self.model,
                    'messages': [{'role': 'user', 'content': prompt}],
                    max_tokens_param: kwargs.get('max_tokens', 4096),
                    'temperature': temperature
                }

                # Only add top_p for models that support it (not o4-mini, o3-mini)
                if self.model not in ['o4-mini', 'o3-mini']:
                    payload['top_p'] = kwargs.get('top_p', 0.9)
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'status': 'success',
                    'content': data['choices'][0]['message']['content'],
                    'provider': 'openai',
                    'model': self.model,
                    'usage': data.get('usage', {})
                }
            else:
                return {
                    'status': 'error',
                    'error': f"OpenAI API error: {response.status_code} - {response.text}",
                    'provider': 'openai'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'error': f"OpenAI request failed: {str(e)}",
                'provider': 'openai'
            }
    
    def is_available(self) -> bool:
        """Check OpenAI availability"""
        try:
            test_response = requests.get(f"{self.base_url}/models", headers=self.headers, timeout=10)
            return test_response.status_code == 200
        except:
            return False

class GeminiProvider(BaseLLMProvider):
    """Google Gemini API provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config['api_key']
        self.model_name = config.get('model', 'gemini-2.5-pro')
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel(self.model_name)
    
    def generate(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using Gemini with enhanced error handling"""
        import time
        import random

        max_retries = 3
        base_delay = 2

        for attempt in range(max_retries):
            try:
                self._rate_limit()

                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=kwargs.get('max_tokens', 8192),
                        temperature=kwargs.get('temperature', 0.7),
                        top_p=kwargs.get('top_p', 0.8),
                        top_k=kwargs.get('top_k', 40)
                    )
                )

                # Extract text content safely
                try:
                    content = response.text
                except:
                    # Fallback for complex responses
                    content = ""
                    if response.candidates:
                        for part in response.candidates[0].content.parts:
                            if hasattr(part, 'text'):
                                content += part.text

                return {
                    'status': 'success',
                    'content': content,
                    'provider': 'gemini',
                    'model': self.model_name,
                    'usage': {'prompt_tokens': len(prompt.split()), 'completion_tokens': len(content.split())}
                }

            except Exception as e:
                error_str = str(e).lower()

                # Check for retryable errors
                if any(term in error_str for term in ['500', 'internal server error', 'rate limit', 'quota', 'timeout']):
                    if attempt < max_retries - 1:
                        # Exponential backoff with jitter
                        delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                        print(f"   ⏳ Gemini error (attempt {attempt + 1}/{max_retries}), retrying in {delay:.1f}s: {str(e)[:100]}")
                        time.sleep(delay)
                        continue

                # Non-retryable error or max retries reached
                return {
                    'status': 'error',
                    'error': f"Gemini request failed after {attempt + 1} attempts: {str(e)}",
                    'provider': 'gemini'
                }

        return {
            'status': 'error',
            'error': f"Gemini request failed after {max_retries} retries",
            'provider': 'gemini'
        }
    
    def is_available(self) -> bool:
        """Check Gemini availability"""
        try:
            # Simple check - if we have an API key and model, assume available
            return bool(self.api_key and len(self.api_key) > 10)
        except:
            return False

class MoonshotProvider(BaseLLMProvider):
    """Moonshot AI (Kimi) API provider"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config['api_key']
        self.model = config.get('model', 'moonshot-v1-8k')
        self.base_url = config.get('base_url', 'https://api.moonshot.cn/v1')
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def generate(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using Moonshot AI"""
        try:
            self._rate_limit()
            
            payload = {
                'model': self.model,
                'messages': [{'role': 'user', 'content': prompt}],
                'max_tokens': kwargs.get('max_tokens', 4096),
                'temperature': kwargs.get('temperature', 0.7),
                'top_p': kwargs.get('top_p', 0.9)
            }
            
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'status': 'success',
                    'content': data['choices'][0]['message']['content'],
                    'provider': 'moonshot',
                    'model': self.model,
                    'usage': data.get('usage', {})
                }
            else:
                return {
                    'status': 'error',
                    'error': f"Moonshot API error: {response.status_code} - {response.text}",
                    'provider': 'moonshot'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'error': f"Moonshot request failed: {str(e)}",
                'provider': 'moonshot'
            }
    
    def is_available(self) -> bool:
        """Check Moonshot availability"""
        try:
            test_response = requests.get(f"{self.base_url}/models", headers=self.headers, timeout=10)
            return test_response.status_code == 200
        except:
            return False

class DeepSeekProvider(BaseLLMProvider):
    """DeepSeek API provider"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config['api_key']
        self.model = config.get('model', 'deepseek-chat')
        self.base_url = config.get('base_url', 'https://api.deepseek.com/v1')
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

    def generate(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using DeepSeek"""
        try:
            self._rate_limit()

            payload = {
                'model': self.model,
                'messages': [{'role': 'user', 'content': prompt}],
                'max_tokens': kwargs.get('max_tokens', 4096),
                'temperature': kwargs.get('temperature', 0.7),
                'top_p': kwargs.get('top_p', 0.9)
            }

            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.timeout
            )

            if response.status_code == 200:
                data = response.json()
                return {
                    'status': 'success',
                    'content': data['choices'][0]['message']['content'],
                    'provider': 'deepseek',
                    'model': self.model,
                    'usage': data.get('usage', {})
                }
            else:
                return {
                    'status': 'error',
                    'error': f"DeepSeek API error: {response.status_code} - {response.text}",
                    'provider': 'deepseek'
                }

        except Exception as e:
            return {
                'status': 'error',
                'error': f"DeepSeek request failed: {str(e)}",
                'provider': 'deepseek'
            }

    def is_available(self) -> bool:
        """Check DeepSeek availability"""
        try:
            test_response = requests.get(f"{self.base_url}/models", headers=self.headers, timeout=10)
            return test_response.status_code == 200
        except:
            return False

class AnthropicProvider(BaseLLMProvider):
    """Anthropic Claude API provider"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.api_key = config['api_key']
        self.model = config.get('model', 'claude-3-5-sonnet-20241022')
        self.base_url = config.get('base_url', 'https://api.anthropic.com/v1')
        self.headers = {
            'x-api-key': self.api_key,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01'
        }
    
    def generate(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using Anthropic Claude"""
        try:
            self._rate_limit()
            
            payload = {
                'model': self.model,
                'max_tokens': kwargs.get('max_tokens', 4096),
                'temperature': kwargs.get('temperature', 0.7),
                'messages': [{'role': 'user', 'content': prompt}]
            }
            
            response = requests.post(
                f"{self.base_url}/messages",
                headers=self.headers,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    'status': 'success',
                    'content': data['content'][0]['text'],
                    'provider': 'anthropic',
                    'model': self.model,
                    'usage': data.get('usage', {})
                }
            else:
                return {
                    'status': 'error',
                    'error': f"Anthropic API error: {response.status_code} - {response.text}",
                    'provider': 'anthropic'
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'error': f"Anthropic request failed: {str(e)}",
                'provider': 'anthropic'
            }
    
    def is_available(self) -> bool:
        """Check Anthropic availability"""
        try:
            # Anthropic doesn't have a simple health check endpoint
            # We'll assume it's available if we have an API key
            return bool(self.api_key and len(self.api_key) > 10)
        except:
            return False

class UnifiedModelInterface:
    """Unified interface for all LLM providers with fallback support"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.providers = {}
        self.fallback_order = config.get('fallback_order', ['gemini', 'openrouter', 'openai', 'anthropic', 'moonshot'])
        self.current_provider = config.get('default_provider', 'gemini')
        
        # Initialize providers
        self._initialize_providers()
    
    def _initialize_providers(self):
        """Initialize all configured providers"""
        provider_classes = {
            'openrouter': OpenRouterProvider,
            'openai': OpenAIProvider,
            'gemini': GeminiProvider,
            'moonshot': MoonshotProvider,
            'anthropic': AnthropicProvider,
            'deepseek': DeepSeekProvider
        }
        
        for provider_name, provider_class in provider_classes.items():
            if provider_name in self.config.get('providers', {}):
                try:
                    self.providers[provider_name] = provider_class(self.config['providers'][provider_name])
                    print(f"✅ {provider_name.title()} provider initialized")
                except Exception as e:
                    print(f"❌ Failed to initialize {provider_name}: {str(e)}")
    
    def generate(self, prompt: str, provider: Optional[str] = None, max_retries: int = 3, **kwargs) -> Dict[str, Any]:
        """Generate response with automatic fallback and retry logic"""
        import time
        import random

        # Use specified provider or current default
        target_provider = provider or self.current_provider

        # Try the target provider first with retries
        if target_provider in self.providers:
            print(f"🤖 Using provider: {target_provider}")
            result = self._generate_with_retry(target_provider, prompt, max_retries, **kwargs)
            if result['status'] == 'success':
                return result
            print(f"⚠️ {target_provider} failed after {max_retries} retries: {result.get('error', 'Unknown error')}")

        # Try fallback providers with retries
        for fallback_provider in self.fallback_order:
            if fallback_provider != target_provider and fallback_provider in self.providers:
                print(f"🔄 Trying fallback provider: {fallback_provider}")
                result = self._generate_with_retry(fallback_provider, prompt, max_retries, **kwargs)
                if result['status'] == 'success':
                    return result
                print(f"⚠️ {fallback_provider} failed after {max_retries} retries: {result.get('error', 'Unknown error')}")

        # All providers failed
        return {
            'status': 'error',
            'error': f'All LLM providers failed after {max_retries} retries each',
            'provider': 'none',
            'attempted_providers': [target_provider] + [p for p in self.fallback_order if p != target_provider and p in self.providers]
        }

    def _generate_with_retry(self, provider_name: str, prompt: str, max_retries: int, **kwargs) -> Dict[str, Any]:
        """Generate with retry logic for rate limiting and temporary failures"""
        import time
        import random

        provider = self.providers[provider_name]

        for attempt in range(max_retries):
            try:
                result = provider.generate(prompt, **kwargs)

                if result['status'] == 'success':
                    return result

                # Check for rate limiting or quota errors
                error_msg = result.get('error', '').lower()
                if any(term in error_msg for term in ['rate limit', 'quota', 'too many requests', '429', '503']):
                    if attempt < max_retries - 1:
                        # Exponential backoff with jitter
                        delay = (2 ** attempt) + random.uniform(0, 1)
                        print(f"   ⏳ Rate limited, waiting {delay:.1f}s before retry {attempt + 2}/{max_retries}")
                        time.sleep(delay)
                        continue

                # For other errors, return immediately (no point retrying)
                return result

            except Exception as e:
                if attempt < max_retries - 1:
                    delay = (2 ** attempt) + random.uniform(0, 1)
                    print(f"   ⏳ Provider error, waiting {delay:.1f}s before retry {attempt + 2}/{max_retries}")
                    time.sleep(delay)
                    continue
                else:
                    return {
                        'status': 'error',
                        'error': f'Provider {provider_name} exception: {str(e)}',
                        'provider': provider_name
                    }

        return {
            'status': 'error',
            'error': f'Provider {provider_name} failed after {max_retries} retries',
            'provider': provider_name
        }
    
    def get_available_providers(self) -> List[str]:
        """Get list of available providers"""
        available = []
        for name, provider in self.providers.items():
            if provider.is_available():
                available.append(name)
        return available
    
    def set_current_provider(self, provider: str):
        """Set the current default provider"""
        if provider in self.providers:
            self.current_provider = provider
            print(f"✅ Current provider set to: {provider}")
        else:
            print(f"❌ Provider {provider} not available")
    
    def get_provider_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all providers"""
        status = {}
        for name, provider in self.providers.items():
            # Get model name safely for JSON serialization
            model_name = getattr(provider, 'model_name', None) or getattr(provider, 'model', 'unknown')
            if hasattr(model_name, '__class__') and 'GenerativeModel' in str(model_name.__class__):
                model_name = getattr(provider, 'model_name', 'gemini-model')

            status[name] = {
                'available': provider.is_available(),
                'model': str(model_name),
                'rate_limit_delay': provider.rate_limit_delay,
                'timeout': provider.timeout
            }
        return status

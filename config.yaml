agent:
  max_iterations: 10
github:
  base_url: https://api.github.com
  rate_limit_delay: 1
  timeout: 30
  token: ****************************************
openrouter:
  api_key: sk-or-v1-b8c9d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7
  base_url: https://openrouter.ai/api/v1
  model: moonshotai/kimi-k2
orchestrator:
  aggregation_strategy: consensus
  parallel_agents: 4
  task_timeout: 600
research_apis:
  dblp:
    author_url: https://dblp.org/search/author/api
    base_url: https://dblp.org/search/publ/api
    venue_url: https://dblp.org/search/venue/api
  gemini:
    api_key: AIzaSyCbiclmPhwSaIYNWugD09VSguiN5uBZhpI
    base_url: https://generativelanguage.googleapis.com/v1beta
    model: gemini-2.5-pro
  github:
    base_url: https://api.github.com
    token: ****************************************
  knowledge_base:
    checkpoint_path: D:/Downloads/make-it-heavy-main/research_checkpoints
    path: D:/Downloads/make-it-heavy-main/research_knowledge_base
  semantic_scholar:
    api_key: zsrJUOvwp45vyecRd658z3tPN1RCDURR84xuAEdn
    base_url: https://api.semanticscholar.org/graph/v1
    status: disabled_due_to_rate_limits
research_orchestrator:
  agent_types:
    implementation: Technical implementation research and code analysis
    literature_review: Comprehensive academic literature analysis and synthesis
    methodology: Research methodology design and validation
    novelty_assessment: Research gap identification and novelty evaluation
    synthesis: Cross-domain research synthesis and idea generation
    validation: Research validation and experimental design
  aggregation_strategy: research_synthesis
  parallel_agents: 6
  question_generation_prompt: 'You are an orchestrator that needs to create {num_agents}
    different questions to thoroughly analyze this topic from multiple angles.


    Original user query: {user_input}


    Generate exactly {num_agents} different, specific questions that will help gather
    comprehensive information about this topic.

    Each question should approach the topic from a different angle (research, analysis,
    verification, alternatives, etc.).


    Return your response as a JSON array of strings, like this:

    ["question 1", "question 2", "question 3", "question 4"]


    Only return the JSON array, nothing else.

    '
  research_phases:
  - literature_review
  - gap_analysis
  - methodology_design
  - implementation_planning
  - novelty_assessment
  - validation_strategy
  synthesis_prompt: "You have {num_responses} different AI agents that analyzed the\
    \ same query from different perspectives. \nYour job is to synthesize their responses\
    \ into ONE comprehensive final answer.\n\nHere are all the agent responses:\n\n\
    {agent_responses}\n\nIMPORTANT: Just synthesize these into ONE final comprehensive\
    \ answer that combines the best information from all agents. \nDo NOT call mark_task_complete\
    \ or any other tools. Do NOT mention that you are synthesizing multiple responses.\
    \ \nSimply provide the final synthesized answer directly as your response.\n"
  task_timeout: 600
search:
  max_results: 5
  user_agent: Mozilla/5.0 (compatible; OpenRouter Agent)
system_prompt: "You are a helpful research assistant. When users ask questions that\
  \ require \ncurrent information or web search, use the search tool and all other\
  \ tools available to find relevant \ninformation and provide comprehensive answers\
  \ based on the results.\n\nIMPORTANT: When you have fully satisfied the user's request\
  \ and provided a complete answer, \nyou MUST call the mark_task_complete tool with\
  \ a summary of what was accomplished and \na final message for the user. This signals\
  \ that the task is finished.\n"
unified_models:
  default_provider: gemini
  fallback_order:
  - gemini
  - openai
  - openrouter
  - anthropic
  - moonshot
  - deepseek
  providers:
    anthropic:
      api_key: YOUR_ANTHROPIC_API_KEY
      base_url: https://api.anthropic.com/v1
      model: claude-4-sonnet
      rate_limit_delay: 1.0
      timeout: 600
    deepseek:
      api_key: YOUR_DEEPSEEK_API_KEY
      base_url: https://api.deepseek.com/v1
      model: deepseek-chat
      rate_limit_delay: 1.0
      timeout: 600
    gemini:
      api_key: AIzaSyD0dPqnPNx4-yFyEMWw8RHaUvo1hUPzh7U
      model: gemini-2.5-pro
      rate_limit_delay: 5
      timeout: 300
    moonshot:
      api_key: YOUR_MOONSHOT_API_KEY
      base_url: https://api.moonshot.cn/v1
      model: moonshot-v1-8k
      rate_limit_delay: 1.0
      timeout: 600
    openai:
      api_key: 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
      model: o4-mini-2025-04-16
      rate_limit_delay: 1
      timeout: 600
      base_url: https://api.openai.com/v1
    openrouter:
      api_key: 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
      base_url: https://openrouter.ai/api/v1
      model: moonshotai/kimi-k2:free
      rate_limit_delay: 1.0
      timeout: 600

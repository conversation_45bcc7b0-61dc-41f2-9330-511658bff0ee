# OpenRouter API settings
openrouter:
  api_key: "sk-or-v1-b8c9d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7"
  base_url: "https://openrouter.ai/api/v1"

  # IMPORTANT: When selecting a model, ensure it has a high context window (200k+ tokens recommended)
  # The orchestrator can generate large amounts of results from multiple agents that need to be
  # processed together during synthesis. Low context window models may fail or truncate results.
  model: "moonshotai/kimi-k2"

# Configuration for large-scale research orchestration
research_apis:
  gemini:
    api_key: 'AIzaSyCbiclmPhwSaIYNWugD09VSguiN5uBZhpI'
    model: 'gemini-2.5-pro'
    base_url: 'https://generativelanguage.googleapis.com/v1beta'

  dblp:
    base_url: 'https://dblp.org/search/publ/api'
    author_url: 'https://dblp.org/search/author/api'
    venue_url: 'https://dblp.org/search/venue/api'
    # No API key required for DBLP

  github:
    token: '****************************************'
    base_url: 'https://api.github.com'

  semantic_scholar:
    api_key: 'zsrJUOvwp45vyecRd658z3tPN1RCDURR84xuAEdn'  # BACKUP: Saved for later use
    base_url: 'https://api.semanticscholar.org/graph/v1'
    status: 'disabled_due_to_rate_limits'

  knowledge_base:
    path: 'D:/Downloads/make-it-heavy-main/research_knowledge_base'
    checkpoint_path: 'D:/Downloads/make-it-heavy-main/research_checkpoints'

# System prompt for the agent
system_prompt: |
  You are a helpful research assistant. When users ask questions that require 
  current information or web search, use the search tool and all other tools available to find relevant 
  information and provide comprehensive answers based on the results.
  
  IMPORTANT: When you have fully satisfied the user's request and provided a complete answer, 
  you MUST call the mark_task_complete tool with a summary of what was accomplished and 
  a final message for the user. This signals that the task is finished.

# Agent settings
agent:
  max_iterations: 10

# Orchestrator settings
orchestrator:
  parallel_agents: 4  # Number of agents to run in parallel
  task_timeout: 300   # Timeout in seconds per agent
  aggregation_strategy: "consensus"  # How to combine results

# Research-specific orchestrator settings
research_orchestrator:
  parallel_agents: 6  # More agents for comprehensive research
  task_timeout: 600   # Longer timeout for research tasks
  aggregation_strategy: "research_synthesis"

  # Research workflow phases
  research_phases:
    - "literature_review"
    - "gap_analysis"
    - "methodology_design"
    - "implementation_planning"
    - "novelty_assessment"
    - "validation_strategy"

  # Specialized research agent types
  agent_types:
    literature_review: "Comprehensive academic literature analysis and synthesis"
    implementation: "Technical implementation research and code analysis"
    novelty_assessment: "Research gap identification and novelty evaluation"
    methodology: "Research methodology design and validation"
    synthesis: "Cross-domain research synthesis and idea generation"
    validation: "Research validation and experimental design"
  
  # Question generation prompt for orchestrator
  question_generation_prompt: |
    You are an orchestrator that needs to create {num_agents} different questions to thoroughly analyze this topic from multiple angles.
    
    Original user query: {user_input}
    
    Generate exactly {num_agents} different, specific questions that will help gather comprehensive information about this topic.
    Each question should approach the topic from a different angle (research, analysis, verification, alternatives, etc.).
    
    Return your response as a JSON array of strings, like this:
    ["question 1", "question 2", "question 3", "question 4"]
    
    Only return the JSON array, nothing else.

  # Synthesis prompt for combining all agent responses
  synthesis_prompt: |
    You have {num_responses} different AI agents that analyzed the same query from different perspectives. 
    Your job is to synthesize their responses into ONE comprehensive final answer.
    
    Here are all the agent responses:
    
    {agent_responses}
    
    IMPORTANT: Just synthesize these into ONE final comprehensive answer that combines the best information from all agents. 
    Do NOT call mark_task_complete or any other tools. Do NOT mention that you are synthesizing multiple responses. 
    Simply provide the final synthesized answer directly as your response.

# Search tool settings
search:
  max_results: 5
  user_agent: "Mozilla/5.0 (compatible; OpenRouter Agent)"

# Unified Model Interface Configuration
unified_models:
  default_provider: "gemini"
  fallback_order: ["gemini", "openrouter", "openai", "anthropic", "moonshot"]
  providers:
    gemini:
      api_key: "AIzaSyCbiclmPhwSaIYNWugD09VSguiN5uBZhpI"
      model: "gemini-2.5-pro"
      base_url: "https://generativelanguage.googleapis.com/v1beta"
      rate_limit_delay: 5.0
      timeout: 300
    openrouter:
      api_key: "sk-or-v1-b8c9d4e5f6a7b8c9d0e1f2a3b4c5d6e7f8a9b0c1d2e3f4a5b6c7d8e9f0a1b2c3d4e5f6a7"
      model: "anthropic/claude-3.5-sonnet"
      base_url: "https://openrouter.ai/api/v1"
      rate_limit_delay: 1.0
      timeout: 300
    openai:
      api_key: "YOUR_OPENAI_API_KEY"
      model: "gpt-4o-mini"
      base_url: "https://api.openai.com/v1"
      rate_limit_delay: 1.0
      timeout: 300
    anthropic:
      api_key: "YOUR_ANTHROPIC_API_KEY"
      model: "claude-3-5-sonnet-20241022"
      base_url: "https://api.anthropic.com/v1"
      rate_limit_delay: 1.0
      timeout: 300
    moonshot:
      api_key: "YOUR_MOONSHOT_API_KEY"
      model: "moonshot-v1-8k"
      base_url: "https://api.moonshot.cn/v1"
      rate_limit_delay: 1.0
      timeout: 300